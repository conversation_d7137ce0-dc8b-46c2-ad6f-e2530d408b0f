import { describe, it, expect, beforeAll, afterAll, vi, inject } from "vitest";
import { loader } from "../api.team";
import { Config } from "../../.server/config";
import type { TeamSchema } from "../../schemas/team";
import { AuthCentralClient } from "../../.server/grpc/auth-central";
import { resetTokenExchangeClient } from "../../.server/grpc/token-exchange";
import { CustomerUiRole } from "~services/auth/central/server/auth_entities_pb";

vi.mock("../../.server/grpc/tenant-watcher", () => ({
  TenantWatcherCachingClient: {
    getInstance: vi.fn().mockReturnValue({
      tenantFor: vi.fn().mockResolvedValue({
        cloud: "test-cloud",
        id: "test456",
        name: "self-serve-team",
        shardNamespace: "d0",
      }),
    }),
  },
}));

vi.mock("../../.server/auth", async () => {
  const originalModule = vi.importActual("../../.server/auth");
  return {
    ...(await originalModule),
    authenticator: {
      isAuthenticated: vi.fn().mockResolvedValue(true),
    },
    getSession: vi.fn().mockResolvedValue({
      get: vi.fn().mockReturnValue(null),
    }),
    withAuthApi: (handler: any) => {
      return (args: any) => handler({ ...args, user: args.user });
    },
  };
});

const mockUser = {
  userId: "test-user-id",
  sessionId: "test-session-id",
  tenantId: "test456",
  tenantName: "self-serve-team",
  shardNamespace: "d0",
  email: "<EMAIL>",
  roles: ["ADMIN"],
  createdAt: Date.now(),
};

describe("api.team loader", () => {
  beforeAll(async () => {
    const testServerConfig = inject("testServerConfig");

    Config.AUTH_CENTRAL_ENDPOINT = testServerConfig.auth_central_grpc_server;
    AuthCentralClient.resetInstance();

    Config.TOKEN_EXCHANGE_ENDPOINT = testServerConfig.token_exchange_server;
    resetTokenExchangeClient();

    const authCentralClient = AuthCentralClient.getInstance();

    try {
      const response = await authCentralClient.addUserToTenant(
        mockUser,
        mockUser.email,
      );
      if (!response.user) {
        throw new Error("No user returned from addUserToTenant");
      }
      mockUser.userId = response.user.id;

      await authCentralClient.updateUserOnTenant(mockUser, mockUser.userId, [
        CustomerUiRole.ADMIN,
      ]);
    } catch (err) {
      console.warn(`Setup failed: ${err}`);
    }
  });

  afterAll(async () => {
    try {
      const authCentralClient = AuthCentralClient.getInstance();
      await authCentralClient.removeUserFromTenant(
        mockUser,
        mockUser.userId,
        mockUser.tenantId,
      );
      console.info(`Cleanup successful: Removed user ${mockUser.email}`);
    } catch (err) {
      console.warn(`Cleanup failed: ${err}`);
    }
  });

  it.skip("should fetch real team data from Auth Central", async () => {
    try {
      const mockRequest = new Request("http://localhost/api/team", {
        headers: {
          cookie: "some-cookie-value",
        },
      });

      const response = await loader({
        request: mockRequest,
        user: mockUser,
      } as any);

      expect(response).not.toBeNull();

      if (response && response instanceof Response) {
        const responseData = await response.json();

        expect(responseData.status).toBe("active");

        // Get the team data from the nested structure
        const data = responseData.team as TeamSchema;

        expect(data.id).toBe(mockUser.tenantId);
        expect(typeof data.seats).toBe("number");
        expect(data.seats).toBe(data.users.length);

        expect(Array.isArray(data.users)).toBe(true);
        const testUser = data.users.find(
          (user) => user.email === mockUser.email,
        );
        expect(testUser).toBeDefined();

        if (testUser) {
          expect(testUser.role).toBe("ADMIN");
          expect(testUser.id).toBe(mockUser.userId);
        }

        expect(data.invitations).toHaveLength(1);
      }
    } catch (error) {
      console.error("Test failed with error:", error);
      throw error;
    }
  });
});

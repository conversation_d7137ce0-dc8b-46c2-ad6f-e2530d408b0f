import { describe, it, expect, vi, beforeEach } from "vitest";
import { faker } from "@faker-js/faker";

import { action } from "../api.team.user.$userId";

import {
  BillingMethod,
  CustomerUiRole,
  User,
} from "~services/auth/central/server/auth_entities_pb";
import { ListTenantUsersResponse } from "~services/auth/central/server/auth_pb";

function createFakeUser(overrides: Partial<User> = {}) {
  return new User({
    id: faker.string.uuid(),
    email: faker.internet.email(),
    createdAt: {
      seconds: BigInt(Math.floor(Date.now() / 1000)),
      nanos: 0,
    },
    nonce: BigInt(0),
    tenants: [],
    stripeCustomerId: faker.string.alphanumeric(16),
    orbCustomerId: faker.string.alphanumeric(16),
    ...overrides,
  });
}

function createFakeListTenantUsersResponse(
  userOverrides: Partial<User>[] = [],
) {
  const users = userOverrides.map((overrides) => createFakeUser(overrides));
  return new ListTenantUsersResponse({
    users: users,
  });
}

// Mock the withAuthApi function to directly call the action function with a mock user
vi.mock("../../.server/auth", async () => {
  const TenantTier = (
    await import("~services/tenant_watcher/tenant_watcher_pb")
  ).TenantTier;
  return {
    withAuthApi: vi.fn((fn) => {
      return (args: unknown) => fn(args);
    }),
    getTenantTier: vi.fn().mockResolvedValue(TenantTier.PROFESSIONAL),
  };
});

// Create a shared mock instance that will be returned by getInstance
const mockAuthCentralInstance = {
  getUserOnTenant: vi.fn(),
  listTenantUsers: vi.fn(),
  removeUserFromTenant: vi.fn(),
  getUser: vi.fn().mockResolvedValue({
    user: {
      id: faker.string.uuid(),
      email: faker.internet.email(),
      createdAt: {
        seconds: BigInt(Math.floor(Date.now() / 1000)),
        nanos: 0,
      },
      nonce: BigInt(0),
      tenants: [],
      stripeCustomerId: faker.string.alphanumeric(16),
      orbCustomerId: faker.string.alphanumeric(16),
      billingMethod: BillingMethod.ORB,
    },
  }),
};

// Mock the Auth Central client
vi.mock("../../.server/grpc/auth-central", () => {
  return {
    AuthCentralClient: {
      getInstance: vi.fn(() => mockAuthCentralInstance),
    },
  };
});

vi.mock("../../utils/team.server", () => {
  return {
    teamsAllowed: vi.fn().mockResolvedValue(true),
  };
});

describe("api.team.user.$userId action", () => {
  const tenantId = faker.string.uuid();
  const adminUserId = faker.string.uuid();
  const regularUserId = faker.string.uuid();
  const lastAdminUserId = faker.string.uuid();
  const nonExistentUserId = faker.string.uuid();

  const mockUser = {
    userId: adminUserId,
    sessionId: faker.string.uuid(),
    tenantId: tenantId,
    tenantName: faker.company.name(),
    shardNamespace: faker.system.directoryPath(),
    email: faker.internet.email(),
    roles: ["ADMIN"],
    createdAt: faker.date.recent().getTime(),
  };

  const mockRequest = new Request("http://localhost/api/team/user/123", {
    method: "DELETE",
  });

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock current user as admin
    mockAuthCentralInstance.getUserOnTenant.mockImplementation(
      async (_, userId, _tenantId) => {
        if (userId === adminUserId || userId === lastAdminUserId) {
          return {
            customerUiRoles: [CustomerUiRole.ADMIN],
          };
        }
        return {
          customerUiRoles: [CustomerUiRole.UNKNOWN_CUSTOMER_UI_ROLE],
        };
      },
    );

    // Mock tenant users list
    mockAuthCentralInstance.listTenantUsers.mockResolvedValue(
      createFakeListTenantUsersResponse([
        { id: adminUserId },
        { id: regularUserId },
        { id: lastAdminUserId },
      ]),
    );

    // Mock successful removal
    mockAuthCentralInstance.removeUserFromTenant.mockResolvedValue({});
  });

  it("should return 405 for non-DELETE requests", async () => {
    const nonDeleteRequest = new Request("http://localhost/api/team/user/123", {
      method: "POST",
    });

    const response = await action({
      request: nonDeleteRequest,
      params: { userId: regularUserId },
      user: mockUser,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(405);
    }
  });

  it("should successfully remove a regular user", async () => {
    const response = await action({
      request: mockRequest,
      params: { userId: regularUserId },
      user: mockUser,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(200);
      expect(mockAuthCentralInstance.removeUserFromTenant).toHaveBeenCalledWith(
        mockUser,
        regularUserId,
        tenantId,
      );
    }
  });

  it("should return 404 if user not found in tenant", async () => {
    const response = await action({
      request: mockRequest,
      params: { userId: nonExistentUserId },
      user: mockUser,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(404);
      expect(
        mockAuthCentralInstance.removeUserFromTenant,
      ).not.toHaveBeenCalled();
    }
  });

  it("should successfully remove self if current user is not an admin", async () => {
    // Mock current user as non-admin
    mockAuthCentralInstance.getUserOnTenant.mockImplementationOnce(
      async () => ({
        customerUiRoles: [CustomerUiRole.UNKNOWN_CUSTOMER_UI_ROLE],
      }),
    );

    const response = await action({
      request: mockRequest,
      params: { userId: regularUserId },
      user: { ...mockUser, userId: regularUserId },
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(200);
      expect(mockAuthCentralInstance.removeUserFromTenant).toHaveBeenCalledWith(
        { ...mockUser, userId: regularUserId },
        regularUserId,
        tenantId,
      );
    }
  });

  it("should return 400 if trying to remove the last admin", async () => {
    // Setup scenario with only one admin
    mockAuthCentralInstance.listTenantUsers.mockResolvedValueOnce(
      createFakeListTenantUsersResponse([
        { id: lastAdminUserId },
        { id: regularUserId },
      ]),
    );

    const response = await action({
      request: mockRequest,
      params: { userId: lastAdminUserId },
      user: { ...mockUser, userId: lastAdminUserId },
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(400);
      expect(
        mockAuthCentralInstance.removeUserFromTenant,
      ).not.toHaveBeenCalled();
    }
  });

  it("should return 400 if userId is missing", async () => {
    const response = await action({
      request: mockRequest,
      params: {},
      user: mockUser,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(400);
      expect(
        mockAuthCentralInstance.removeUserFromTenant,
      ).not.toHaveBeenCalled();
    }
  });

  it("should return 400 if user has no tenant", async () => {
    const response = await action({
      request: mockRequest,
      params: { userId: regularUserId },
      user: { ...mockUser, tenantId: undefined },
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(400);
      expect(
        mockAuthCentralInstance.removeUserFromTenant,
      ).not.toHaveBeenCalled();
    }
  });

  it("should handle errors when checking admin status", async () => {
    mockAuthCentralInstance.getUserOnTenant.mockRejectedValueOnce(
      new Error("Failed to get user on tenant"),
    );

    const response = await action({
      request: mockRequest,
      params: { userId: regularUserId },
      user: mockUser,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(500);
      expect(
        mockAuthCentralInstance.removeUserFromTenant,
      ).not.toHaveBeenCalled();
    }
  });

  it("should handle errors when listing tenant users", async () => {
    mockAuthCentralInstance.listTenantUsers.mockRejectedValueOnce(
      new Error("Failed to list tenant users"),
    );

    const response = await action({
      request: mockRequest,
      params: { userId: regularUserId },
      user: mockUser,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(500);
      expect(
        mockAuthCentralInstance.removeUserFromTenant,
      ).not.toHaveBeenCalled();
    }
  });

  it("should handle errors when removing user from tenant", async () => {
    mockAuthCentralInstance.removeUserFromTenant.mockRejectedValueOnce(
      new Error("Failed to remove user from tenant"),
    );

    const response = await action({
      request: mockRequest,
      params: { userId: regularUserId },
      user: mockUser,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(500);
    }
  });
});

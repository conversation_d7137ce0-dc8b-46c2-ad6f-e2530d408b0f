import {
  describe,
  it,
  expect,
  vi,
  beforeEach,
  type MockedFunction,
} from "vitest";
import { faker } from "@faker-js/faker";
import { action } from "../api.team.invite.$invitationId";
import { CustomerUiRole } from "~services/auth/central/server/auth_entities_pb";
import { ensureTeamForUser } from "../../utils/team.server";

// Mock the ensureTeamForUser function
const ensureTeamForUserMock = ensureTeamForUser as MockedFunction<
  typeof ensureTeamForUser
>;

vi.mock("../../utils/team.server", () => {
  return {
    ensureTeamForUser: vi.fn(),
    teamsAllowed: vi.fn().mockResolvedValue(true),
  };
});

// Mock the withAuthApi function to directly call the action function with a mock user
vi.mock("../../.server/auth", async () => {
  const TenantTier = (
    await import("~services/tenant_watcher/tenant_watcher_pb")
  ).TenantTier;
  return {
    withAuthApi: vi.fn((fn) => {
      return (args: unknown) => fn(args);
    }),
    getTenantTier: vi.fn().mockResolvedValue(TenantTier.PROFESSIONAL),
  };
});

// Create a shared mock instance that will be returned by getInstance
const mockAuthCentralInstance = {
  getUserOnTenant: vi.fn(),
  deleteInvitation: vi.fn(),
};

// Create a mock tenant watcher instance
const mockTenantWatcherInstance = {
  tenantFor: vi.fn(),
};

// Mock the Auth Central client
vi.mock("../../.server/grpc/auth-central", () => {
  return {
    AuthCentralClient: {
      getInstance: vi.fn(() => mockAuthCentralInstance),
      getUser: vi.fn().mockResolvedValue({
        user: {
          id: faker.string.uuid(),
          email: faker.internet.email(),
          createdAt: {
            seconds: BigInt(Math.floor(Date.now() / 1000)),
            nanos: 0,
          },
          nonce: BigInt(0),
          tenants: [],
          stripeCustomerId: faker.string.alphanumeric(16),
          orbCustomerId: faker.string.alphanumeric(16),
        },
      }),
    },
  };
});

// Mock the Tenant Watcher client
vi.mock("../../.server/grpc/tenant-watcher", () => {
  return {
    TenantWatcherCachingClient: {
      getInstance: vi.fn(() => mockTenantWatcherInstance),
    },
  };
});

// Mock the logger
vi.mock("@augment-internal/logging", () => {
  return {
    logger: {
      error: vi.fn(),
      warn: vi.fn(),
      info: vi.fn(),
    },
  };
});

describe("api.team.invite.$invitationId action", () => {
  const tenantId = faker.string.uuid();
  const adminUserId = faker.string.uuid();
  const nonAdminUserId = faker.string.uuid();
  const validInvitationId = faker.string.uuid();

  const mockAdminUser = {
    userId: adminUserId,
    sessionId: faker.string.uuid(),
    tenantId: tenantId,
    tenantName: faker.company.name(),
    shardNamespace: faker.system.directoryPath(),
    email: faker.internet.email(),
    roles: ["ADMIN"],
    createdAt: faker.date.recent().getTime(),
  };

  const mockNonAdminUser = {
    ...mockAdminUser,
    userId: nonAdminUserId,
    roles: [],
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock ensureTeamForUser to return the tenant ID by default
    vi.mocked(ensureTeamForUserMock).mockResolvedValue(tenantId);

    // Mock tenant watcher to return a self-serve team
    mockTenantWatcherInstance.tenantFor.mockResolvedValue({
      id: tenantId,
      config: {
        configs: {
          is_self_serve_team: "true",
        },
      },
    });

    // Mock getUserOnTenant to return admin role for admin user and non-admin role for others
    mockAuthCentralInstance.getUserOnTenant.mockImplementation(
      async (_, userId) => {
        if (userId === adminUserId) {
          return {
            customerUiRoles: [CustomerUiRole.ADMIN],
          };
        }
        return {
          customerUiRoles: [CustomerUiRole.UNKNOWN_CUSTOMER_UI_ROLE],
        };
      },
    );

    // Mock successful invitation deletion by default
    mockAuthCentralInstance.deleteInvitation.mockResolvedValue({});
  });

  it("should return 405 for non-DELETE requests", async () => {
    const mockRequest = new Request("http://localhost/api/team/invite/123", {
      method: "GET",
    });

    const response = await action({
      request: mockRequest,
      params: { invitationId: validInvitationId },
      user: mockAdminUser,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(405);
      const data = await response.json();
      expect(data).toHaveProperty("message", "Method not allowed");
    }
  });

  it("should return 400 if invitationId is missing", async () => {
    const mockRequest = new Request("http://localhost/api/team/invite/", {
      method: "DELETE",
    });

    const response = await action({
      request: mockRequest,
      params: {}, // Missing invitationId
      user: mockAdminUser,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data).toHaveProperty("message", "Invitation ID is required");
    }
  });

  it("should return 400 if invitationId format is invalid", async () => {
    const mockRequest = new Request(
      "http://localhost/api/team/invite/invalid",
      {
        method: "DELETE",
      },
    );

    const response = await action({
      request: mockRequest,
      params: { invitationId: "invalid-id" },
      user: mockAdminUser,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data).toHaveProperty("message", "Invalid invitation ID format");
    }
  });

  it("should successfully delete an invitation if non-admin user attempts to delete invitation", async () => {
    const mockRequest = new Request("http://localhost/api/team/invite/123", {
      method: "DELETE",
    });

    const response = await action({
      request: mockRequest,
      params: { invitationId: validInvitationId },
      user: mockNonAdminUser,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(200);
      expect(mockAuthCentralInstance.deleteInvitation).toHaveBeenCalledWith(
        mockNonAdminUser,
        validInvitationId,
        tenantId,
      );
    }
  });

  it("should return 500 if deleting invitation fails", async () => {
    mockAuthCentralInstance.deleteInvitation.mockRejectedValueOnce(
      new Error("Failed to delete invitation"),
    );

    const mockRequest = new Request("http://localhost/api/team/invite/123", {
      method: "DELETE",
    });

    const response = await action({
      request: mockRequest,
      params: { invitationId: validInvitationId },
      user: mockAdminUser,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data).toHaveProperty("message", "Internal Server Error");
    }
  });

  it("should successfully delete an invitation", async () => {
    const mockRequest = new Request("http://localhost/api/team/invite/123", {
      method: "DELETE",
    });

    const response = await action({
      request: mockRequest,
      params: { invitationId: validInvitationId },
      user: mockAdminUser,
    } as any);

    expect(response).not.toBeNull();
    if (response && response instanceof Response) {
      expect(response.status).toBe(200);
      expect(mockAuthCentralInstance.deleteInvitation).toHaveBeenCalledWith(
        mockAdminUser,
        validInvitationId,
        tenantId,
      );
    }
  });
});

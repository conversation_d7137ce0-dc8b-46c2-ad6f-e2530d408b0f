import { withAuth<PERSON><PERSON> } from "../.server/auth";
import {
  cacheableJson,
  getAuthenticatedApiData,
  getDateFilters,
} from "../utils/api";
import { cachingClient } from "../.server/grpc/request-insights-analytics";

export const loader = withAuthApi(
  async ({ request, user }) => {
    const { tenantId } = user;
    const dateFilters = getDateFilters(request);

    const { chatStats } = await getAuthenticatedApiData(
      user,
      {
        tenantId,
        dateFilters,
      },
      cachingClient.getChatStats.bind(cachingClient),
    );

    return cacheableJson(chatStats);
  },
  {
    adminOnly: true,
    enterpriseOnly: true,
  },
);

import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { withAuth } from "../.server/auth";
import { GetChatConversationRequest } from "~services/share/share_pb";
import { cachingClient } from "../.server/grpc/share";
import { getAuthenticatedApiData } from "../utils/api";
import { cachingClient as RIAClient } from "../.server/grpc/request-insights-analytics";
import {
  GetUserLastRequestTimestampRequest,
  GetUserLastRequestTimestampRequest_RequestType,
  type GetUserLastRequestTimestampResponse,
} from "~services/request_insight/analytics/request_insight_analytics_pb";
import ShareLayout from "../components/share/ShareLayout";

export const loader = withAuth(
  async ({ request, user }) => {
    const { tenantId } = user;
    const { userId } = user;
    const url = new URL(request.url);
    const pathSegments = url.pathname.split("/");
    const uuid = pathSegments[pathSegments.length - 1];
    if (!uuid) {
      throw json(
        { message: "uuid is required" },
        {
          status: 400,
          statusText: "Bad Request",
        },
      );
    }

    const chatRequest = new GetChatConversationRequest({
      uuid: uuid,
    });

    const msg = await getAuthenticatedApiData(
      user,
      chatRequest,
      cachingClient.getChatConversation.bind(cachingClient),
    );

    const earliestTimestamp = await getAuthenticatedApiData(
      user,
      { tenantId },
      RIAClient.getEarliestRequestTimestamp.bind(RIAClient),
    );

    const userLastRequestTimestampRequest =
      new GetUserLastRequestTimestampRequest({
        userId: userId,
        tenantId: tenantId,
        requestTypes: [GetUserLastRequestTimestampRequest_RequestType.CHAT],
        minSearchTimestamp: earliestTimestamp.earliestRequestTimestamp, //Since you are getting a shared chat request you are assured earliest timestamp is not null
      });

    const lastRequestTimestampResponse: GetUserLastRequestTimestampResponse =
      await getAuthenticatedApiData(
        user,
        userLastRequestTimestampRequest,
        RIAClient.getUserLastRequestTimestamp.bind(RIAClient),
      );
    const lastRequestDate =
      lastRequestTimestampResponse.lastRequestTimestamp?.toDate();

    return json({
      ...msg,
      reader_email: user.email,
      author_email: msg.user,
      lastRequestDate: lastRequestDate,
    });
  },
  {
    adminOnly: false,
    entry: "/share",
  },
);

type LoaderData = Omit<
  ReturnType<typeof useLoaderData<typeof loader>>,
  "date"
> & {
  date: string; // Protobuf Timestamp has its own toJSON method that makes it into a string, which JsonifyObject does not understand.
};

export default function Layout() {
  const data = useLoaderData<LoaderData>();

  return (
    <ShareLayout
      title={data.title}
      date={data.date}
      chat={data.chat}
      reader_email={data.reader_email}
      author_email={data.author_email}
      lastRequestDate={data.lastRequestDate}
    />
  );
}

import type { MetaFunction } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { isAdmin, withAuth, isEnterpriseTier } from "../.server/auth";
import { isUserInLegacySelfServeTeam } from "../utils/team.server";

export const meta: MetaFunction = () => {
  return [
    { title: "Augment" },
    {
      name: "description",
      content: "Augment yourself with the best AI pair programmer.",
    },
  ];
};

export const loader = withAuth(async ({ user }) => {
  if (await isEnterpriseTier(user)) {
    if (await isAdmin(user)) {
      return redirect("/dashboard/overview");
    } else {
      return redirect("/enterprise-member");
    }
  }
  if (await isUserInLegacySelfServeTeam(user)) {
    return redirect("/legacy-team");
  }
  return redirect("/account");
});

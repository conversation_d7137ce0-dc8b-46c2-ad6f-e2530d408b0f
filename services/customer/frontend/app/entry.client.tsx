/*
 * DO NOT DELETE THIS FILE
 * Even though Remix documentation says this file is optional,
 * deleting it causes a build error when running the dev server.
 *
 * This file was generated by <PERSON> using `npx remix reveal`.
 *
 * https://remix.run/docs/en/main/file-conventions/entry.client
 */

import { RemixBrowser } from "@remix-run/react";
import { startTransition, StrictMode } from "react";
import { hydrateRoot } from "react-dom/client";

startTransition(() => {
  hydrateRoot(
    document,
    <StrictMode>
      <RemixBrowser />
    </StrictMode>,
  );
});

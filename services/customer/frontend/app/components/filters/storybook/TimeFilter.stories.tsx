import type { <PERSON>a, StoryObj } from "@storybook/react";
import TimeFilter from "../TimeFilter";

const meta: Meta = {
  title: "UI/TimeFilter",
  component: TimeFilter,
  tags: ["autodocs"],
  parameters: {
    layout: "fullscreen",
  },
  argTypes: {
    activeFilter: {
      description: "The time filter to apply to the data",
    },
    active: {
      description: "The active time filter",
      control: "select",
      options: ["all", "7", "30", "60"],
    },
    setActiveFilter: {
      description: "The function to set the active filter",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    active: "all",
  },
};

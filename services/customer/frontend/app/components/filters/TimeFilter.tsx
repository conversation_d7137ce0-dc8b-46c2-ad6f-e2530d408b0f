import { useMemo } from "react";
import { sub } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import { ClientOnly } from "remix-utils/client-only";
import { Flex, SegmentedControl, Skeleton } from "@radix-ui/themes";

import { isBetween } from "../../utils/data";
import { useAppStore } from "../../utils/app-store";
import {
  DEFAULT_TIMEZONE,
  convertDateToYearMonthDay,
  TimeFrame,
} from "../../utils/date";

// Create a safe version of useAppStore that won't throw if the store isn't initialized
function useSafeAppStoreWithDefault() {
  try {
    return useAppStore((state) => state.earliestData);
  } catch (e) {
    const date = new Date();
    date.setDate(date.getDate() - 60);
    return convertDateToYearMonthDay(date);
  }
}

const defaultFilterOptions = [
  { label: "Last 7 Days", value: TimeFrame.Last7Days },
  { label: "Last 30 Days", value: TimeFrame.Last30Days },
  { label: "Last 60 Days", value: TimeFrame.Last60Days },
  { label: "Last 90 Days", value: TimeFrame.Last90Days },
  { label: "All Time", value: TimeFrame.AllTime },
];

export interface FilterOption {
  label: string;
  value: string;
}

export interface FilterProps {
  activeFilter: string;
  setActiveFilter: (value: string) => void;
  customFilterOptions?: FilterOption[];
}

export default function TimeFilter({
  activeFilter,
  setActiveFilter,
  customFilterOptions,
}: FilterProps) {
  const nowZoned = toZonedTime(new Date(), DEFAULT_TIMEZONE);
  const today = convertDateToYearMonthDay(nowZoned);

  // Use our safe version of useAppStore that won't throw
  const earliestData = useSafeAppStoreWithDefault();

  const filteredFilterOptions = useMemo(() => {
    const optionsToFilter = customFilterOptions || defaultFilterOptions;
    return optionsToFilter.filter((option) => {
      if (option.value === TimeFrame.AllTime) {
        return true;
      }
      const daysToSubtract = parseInt(option.value, 10);
      const subtractedDate = convertDateToYearMonthDay(
        sub(nowZoned, { days: daysToSubtract }),
      );
      return isBetween(earliestData, subtractedDate, today);
    });
  }, [earliestData, today, customFilterOptions]);

  return (
    <ClientOnly fallback={<Skeleton height="32px" width="464px" />}>
      {() => (
        <Flex align="start" direction="column" gap="4">
          <SegmentedControl.Root
            defaultValue={activeFilter}
            variant="surface"
            aria-label="Time selection"
            onValueChange={setActiveFilter}
          >
            {filteredFilterOptions.map(({ value, label }) => (
              <SegmentedControl.Item
                key={value}
                value={value}
                aria-label={label}
              >
                {label}
              </SegmentedControl.Item>
            ))}
          </SegmentedControl.Root>
        </Flex>
      )}
    </ClientOnly>
  );
}

import React from "react";
import { Flex, Skeleton } from "@radix-ui/themes";
import { Group } from "@visx/group";
import { BarRounded } from "@visx/shape";
import { scaleBand, scaleLinear } from "@visx/scale";
import { useParentSize } from "@visx/responsive";
import { Text } from "@visx/text";

import { formatYAxisValue } from "./formatting";

export interface BarChartProps {
  data: { label: string; value: number }[] | null;
  color?: string;
}

export default function BarChart({ data, color }: BarChartProps) {
  // Define margins
  const margin = { top: 0, bottom: 0, left: 70, right: 36 };
  const { parentRef, width: parentWidth, height } = useParentSize();

  const sortedData = data?.sort((a, b) => b.value - a.value) || [];
  const maxLabelLength = Math.max(...sortedData.map((d) => d.label.length)) + 5;

  const width = Math.max(parentWidth - 16, 0);
  const xMax = width - margin.left - margin.right - maxLabelLength;
  const yMax = height - margin.top - margin.bottom;

  // Scales
  const xScale = scaleLinear({
    domain: [0, Math.max(...sortedData.map((d) => d.value))],
    nice: true,
    range: [0, xMax],
  });

  const yScale = scaleBand({
    domain: sortedData.map((d) => d.label),
    range: [0, yMax],
    padding: 0.4,
  });

  return (
    <Flex
      ref={parentRef}
      flexGrow="1"
      flexShrink="1"
      flexBasis="0%"
      height="100%"
      width="100%"
    >
      {data === undefined || width === 0 || height === 0 ? (
        <Skeleton height="100%" width="100%" />
      ) : (
        <svg width={width} height="100%">
          <Group top={margin.top} left={margin.left}>
            {sortedData.map((d, i) => {
              const barWidth = xScale(d.value);
              const barHeight = yScale.bandwidth();
              const barX = maxLabelLength;
              const barY = yScale(d.label) ?? 0;
              return (
                <React.Fragment key={`bar-${i}`}>
                  {/* Bar */}
                  <BarRounded
                    x={barX}
                    y={barY}
                    width={barWidth}
                    height={barHeight}
                    radius={3}
                    right={true}
                    left={true}
                    fill={`var(--${color}-7)`}
                    opacity={0.7}
                  />
                  {/* Label Text */}
                  <Text
                    x={-5 + maxLabelLength}
                    y={barY + barHeight / 2}
                    dy=".35em"
                    fontSize={14}
                    textAnchor="end"
                    fill="var(--gray-11)"
                  >
                    {d.label}
                  </Text>
                  {/* Value Text */}
                  <Text
                    x={barX + barWidth + 6}
                    y={barY + barHeight / 2}
                    dy=".35em"
                    fontSize={14}
                    textAnchor="start"
                    fill="var(--gray-11)"
                  >
                    {formatYAxisValue(d.value)}
                  </Text>
                </React.Fragment>
              );
            })}
          </Group>
        </svg>
      )}
    </Flex>
  );
}

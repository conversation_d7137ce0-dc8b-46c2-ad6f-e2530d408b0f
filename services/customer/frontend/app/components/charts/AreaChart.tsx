import { useId, useMemo } from "react";
import { format, parse } from "date-fns";
import { Flex, Skeleton } from "@radix-ui/themes";
import {
  XYChart,
  Axis,
  Grid,
  AnimatedAreaSeries,
  AnimatedLineSeries,
} from "@visx/xychart";
import { LinearGradient } from "@visx/gradient";
import { curveLinear } from "@visx/curve";
import { useParentSize } from "@visx/responsive";
import { formatYAxisValue } from "./formatting";
import type { ChartDataPointSchema } from "app/schemas/chart";

export type ChartProps = {
  data: ChartDataPointSchema[] | null;
  showTrendLine?: boolean;
  showAxis?: boolean;
  color?: string;
};

export default function XYChartComponent({
  data,
  color,
  showTrendLine = false,
  showAxis = true,
}: ChartProps) {
  const id = useId();
  const { parentRef, width, height } = useParentSize();

  const accessors = {
    xAccessor: (d: ChartDataPointSchema) => {
      if (d) {
        return parse(d.date, "yyyy-M-d", new Date());
      }
    },
    yAccessor: (d: ChartDataPointSchema) => d.value,
  };

  const calculateTrendLine = (
    data: ChartDataPointSchema[],
    n: number,
  ): ChartDataPointSchema[] => {
    return data.map((_, index, arr) => {
      const start = Math.max(0, index - n + 1);
      const end = index + 1;
      const subset = arr.slice(start, end);
      const sum = subset.reduce((acc, curr) => acc + curr.value, 0);
      return { date: arr[index].date, value: sum / subset.length };
    });
  };

  const trendLineData = useMemo(
    () => calculateTrendLine(data ?? [], 7),
    [data],
  );

  return (
    <Flex
      ref={parentRef}
      flexGrow="1"
      flexShrink="1"
      flexBasis="0%"
      height="100%"
      width="100%"
    >
      {data === null || width === 0 || height === 0 ? (
        <Skeleton
          height="calc(100% - 10px)"
          width="100%"
          style={{ marginTop: "10px" }}
        />
      ) : (
        <XYChart
          width={width}
          height={height}
          xScale={{ type: "time" }}
          yScale={{ type: "linear" }}
          margin={
            showAxis
              ? { top: 20, bottom: 30, left: 50, right: 20 }
              : { top: 0, bottom: 0, left: 0, right: 0 }
          }
        >
          {showAxis && <Grid stroke="var(--grey-1)" />}
          {showAxis && (
            <Axis
              orientation="bottom"
              numTicks={5}
              tickFormat={(d) => format(d, "MMM d")}
            />
          )}
          {showAxis && (
            <Axis
              numTicks={5}
              orientation="left"
              tickFormat={formatYAxisValue}
            />
          )}
          <LinearGradient
            id={`area-gradient-${id}`}
            from={`var(--${color}-9)`}
            to={`var(--${color}-3)`}
            toOpacity={0.5}
          />
          <AnimatedAreaSeries
            dataKey="XYKey"
            data={data as ChartDataPointSchema[]}
            renderLine={false}
            {...accessors}
            fill={
              showAxis ? `url(#${`area-gradient-${id}`})` : `var(--${color}-a7)`
            }
            stroke={showAxis ? `var(--${color}-3)` : `var(--${color}-9)`}
          />
          {showTrendLine && (
            <AnimatedLineSeries
              dataKey="TrendLine"
              data={trendLineData}
              {...accessors}
              stroke={"var(--gray-12)"}
              strokeWidth={1.5}
              curve={curveLinear}
            />
          )}
        </XYChart>
      )}
    </Flex>
  );
}

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import <PERSON><PERSON><PERSON>, { type BarChartProps } from "../BarChart";
import { generateKeyData } from "../../../storybook-data";

const meta = {
  title: "Charts/Bar",
  component: <PERSON><PERSON><PERSON>,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    data: {
      description: "Data to display in the chart",
      control: {
        type: "object",
      },
    },
  },
  args: {
    data: await generateKeyData({
      labels: ["Refactor", "Explain", "Test", "Optimize", "Fix"],
      timeFrame: "30",
    }),
  },
} satisfies Meta<typeof BarChart>;

export default meta;
type Story = StoryObj<typeof meta>;

const Template = ({ ...args }: BarChartProps) => {
  return (
    <div style={{ width: "500px", height: "300px" }}>
      <BarChart {...args} />
    </div>
  );
};

export const Default: Story = {
  args: {
    data: await generateKeyData({
      labels: ["Refactor", "Explain", "Test", "Optimize", "Fix"],
      timeFrame: "30",
    }),
    color: "bronze",
  },
  render: (args: any) => <Template {...args} />,
};

import type { <PERSON>a, StoryObj } from "@storybook/react";
import AreaChart, { type ChartProps } from "../AreaChart";
import { generateChartData } from "../../../storybook-data";

const meta = {
  title: "Charts/Area",
  component: AreaChart,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    data: {
      description: "Data to display in the chart",
      control: {
        type: "object",
      },
    },
    showTrendLine: {
      description: "Show the trend line",
      control: {
        type: "boolean",
      },
    },
    showAxis: {
      description: "Show the axis",
      control: {
        type: "boolean",
      },
    },
  },
} satisfies Meta<typeof AreaChart>;

export default meta;
type Story = StoryObj<typeof meta>;

const Template = ({ ...args }: ChartProps) => {
  return (
    <div style={{ display: "flex", width: "500px", height: "300px" }}>
      <AreaChart {...args} />
    </div>
  );
};

export const Default: Story = {
  args: {
    data:
      (
        await generateChartData({
          timeFrame: "60",
          interval: "days",
        })
      ).data || [],
    color: "indigo",
    showTrendLine: true,
  },
  render: (args: any) => <Template {...args} />,
};

export const SummaryCards: Story = {
  args: {
    data:
      (
        await generateChartData({
          timeFrame: "60",
          interval: "days",
        })
      ).data || [],
    showTrendLine: false,
    showAxis: false,
    color: "indigo",
  },
  render: (args: any) => <Template {...args} />,
};

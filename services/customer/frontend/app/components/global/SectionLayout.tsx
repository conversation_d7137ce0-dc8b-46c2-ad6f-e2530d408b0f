import { Container, Flex } from "@radix-ui/themes";

import SectionHeader from "./SectionHeader";
import TimeFilter, { type FilterOption } from "../filters/TimeFilter";

export default function SectionLayout({
  children,
  title,
  description,
  activeFilter,
  setActiveFilter,
  customFilterOptions,
}: {
  children: React.ReactNode;
  title: string;
  description?: string;
  activeFilter: string;
  setActiveFilter: (value: string) => void;
  customFilterOptions?: FilterOption[];
}) {
  return (
    <Container p={{ initial: "4", md: "8" }}>
      <Flex direction="column" gap="4">
        <SectionHeader title={title} description={description} />
        <TimeFilter
          activeFilter={activeFilter}
          setActiveFilter={setActiveFilter}
          customFilterOptions={customFilterOptions}
        />
        {children}
      </Flex>
    </Container>
  );
}

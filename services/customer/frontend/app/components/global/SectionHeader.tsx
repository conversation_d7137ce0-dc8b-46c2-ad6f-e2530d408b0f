import { Flex, Text } from "@radix-ui/themes";

export default function SectionHeader({
  title,
  description,
}: {
  title: string;
  description?: string;
}) {
  return (
    <Flex direction="column">
      <Text size="6" weight="medium">
        {title}
      </Text>
      {description && (
        <Text size="3" weight="regular">
          {description}
        </Text>
      )}
    </Flex>
  );
}

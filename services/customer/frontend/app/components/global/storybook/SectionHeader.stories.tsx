import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import SectionHeader from "../SectionHeader";

const meta: Meta = {
  title: "UI/SectionHeader",
  component: SectionHeader,
  tags: ["autodocs"],
  parameters: {
    layout: "fullscreen",
  },
  argTypes: {
    title: {
      description: "The title of the section",
      control: { type: "text" },
    },
    description: {
      description: "The description of the section",
      control: { type: "text" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    title: "Section Title",
    description: "Section Description",
  },
};

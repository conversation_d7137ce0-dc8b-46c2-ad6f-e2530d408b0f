import { useRef, useEffect, useState } from "react";
import { Text, Badge, Flex, Box, IconButton } from "@radix-ui/themes";
import { Link2Icon } from "@radix-ui/react-icons";
import Markdown from "../chat/markdown/Markdown";
import type { ChatExchange } from "./ShareLayout";
import { useTheme } from "next-themes";

// Keep track of loaded themes to prevent duplicate loading
const loadedThemes = new Set<string>();
let currentTheme: string | null = null;

const loadThemeStyles = async (theme: string) => {
  const themeId = `hljs-theme-${theme}`;

  // Remove previous theme if it exists
  if (currentTheme && currentTheme !== theme) {
    const prevThemeId = `hljs-theme-${currentTheme}`;
    const prevTheme = document.getElementById(prevThemeId);
    if (prevTheme) {
      prevTheme.remove();
      loadedThemes.delete(currentTheme);
    }
  }
  currentTheme = theme;
  if (!loadedThemes.has(theme)) {
    const styleSheet = document.createElement("style");
    styleSheet.id = themeId;
    if (theme === "light") {
      styleSheet.textContent = (
        await import("highlight.js/styles/vs.css?raw")
      ).default;
    } else {
      styleSheet.textContent = (
        await import("highlight.js/styles/vs2015.css?raw")
      ).default;
    }

    document.head.appendChild(styleSheet);
    loadedThemes.add(theme);
  }
};

type ChatContainerProps = {
  title: string;
  date: string;
  chat: ChatExchange[];
  author_email: string;
};

export default function ChatContainer({
  title,
  date,
  chat,
  author_email,
}: ChatContainerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const { resolvedTheme } = useTheme();
  const [copiedId, setCopiedId] = useState<string | null>(null);

  useEffect(() => {
    loadThemeStyles(resolvedTheme || "dark");
  }, [resolvedTheme]);

  const copyRequestId = async (requestId: string) => {
    await navigator.clipboard.writeText(requestId);
    setCopiedId(requestId);
    setTimeout(() => setCopiedId(null), 1500);
  };

  return (
    <Box
      ref={containerRef}
      style={{
        width: "100%",
        maxWidth: "800px",
        padding: "0 8px",
        alignSelf: "center",
      }}
    >
      <Flex direction="column" gap="4">
        <Flex direction="column" gap="2">
          <Text size="5" weight="bold">
            {title}
          </Text>
          <Text size="1" weight="bold" color="gray">
            {new Date(date).toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            })}
          </Text>
        </Flex>
        <Flex gap="5" direction="column">
          {chat.map((exchange: ChatExchange) => (
            <Flex direction="column" gap="2" key={`${exchange.requestId}`}>
              <Flex justify="end" direction="column" align="end" gap="1">
                <Flex align="center" gap="2">
                  <Badge
                    size="2"
                    variant="soft"
                    color="gray"
                    highContrast={false}
                  >
                    {author_email.split("@")?.[0] || author_email}
                  </Badge>
                  <IconButton
                    size="1"
                    variant="ghost"
                    onClick={() => copyRequestId(exchange.requestId)}
                    title={
                      copiedId === exchange.requestId
                        ? "Copied!"
                        : "Copy request ID"
                    }
                    className="request-id-button"
                  >
                    <Link2Icon />
                  </IconButton>
                </Flex>
                <Text size="2" color="gray" style={{ paddingLeft: "64px" }}>
                  <p style={{ whiteSpace: "pre-wrap" }}>{exchange.message}</p>
                </Text>
              </Flex>
              <Flex
                justify="start"
                direction="column"
                align="start"
                gap="1"
                style={{ paddingTop: "8px" }}
              >
                <Badge
                  size="2"
                  variant="soft"
                  color="green"
                  highContrast={false}
                >
                  Augment
                </Badge>
                <Markdown content={exchange.response} />
              </Flex>
            </Flex>
          ))}
        </Flex>
      </Flex>
    </Box>
  );
}

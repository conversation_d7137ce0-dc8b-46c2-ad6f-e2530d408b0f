import { useMemo } from "react";
import { Container, Flex, Theme } from "@radix-ui/themes";
import { ThemeProvider } from "next-themes";
import ShareHeader from "./ShareHeader";
import ChatContainer from "./ChatContainer";
import GetStartedCallout from "./GetStartedCallout";
import { toDate } from "date-fns";

declare module "next-themes" {
  interface ThemeProviderProps {
    children: React.ReactNode;
  }
}

export type ChatExchange = {
  requestId: string;
  message: string;
  response: string;
};

type ShareLayoutProps = {
  title: string;
  date: string;
  chat: ChatExchange[];
  reader_email: string;
  author_email: string;
  lastRequestDate?: string;
};

export default function ShareLayout({
  title,
  date,
  chat,
  reader_email,
  author_email,
  lastRequestDate,
}: ShareLayoutProps) {
  const shouldShowCallout = useMemo((): boolean => {
    // If the last request date is more than a month ago, show the callout
    if (!lastRequestDate) return true;
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    return toDate(lastRequestDate) < oneMonthAgo;
  }, [lastRequestDate]);

  return (
    <ThemeProvider attribute="class">
      <Theme>
        <Container
          size="4"
          className={
            shouldShowCallout
              ? "container-no-padding"
              : "container-with-padding"
          }
        >
          <Flex direction="column" gap="4">
            <ShareHeader reader_email={reader_email} />
            <ChatContainer
              title={title}
              date={date}
              chat={chat}
              author_email={author_email}
            />
            {shouldShowCallout && <GetStartedCallout />}
          </Flex>
        </Container>
      </Theme>
    </ThemeProvider>
  );
}

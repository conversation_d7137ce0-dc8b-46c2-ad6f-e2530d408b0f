import { Link } from "@remix-run/react";
import { IconButton, Button, Callout, Flex } from "@radix-ui/themes";
import { ExternalLinkIcon, Cross2Icon } from "@radix-ui/react-icons";
import { useState } from "react";

export default function GetStartedCallout() {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  return (
    <Callout.Root
      style={{
        margin: "0 auto",
        height: "100%",
        width: "100%",
        maxWidth: "800px",
        alignSelf: "center",
        zIndex: 1000,
        display: "block",
      }}
      color="blue"
      highContrast={false}
    >
      <Flex
        width="100%"
        direction="row"
        align="center"
        justify="between"
        gap="2"
      >
        <Flex
          direction="row"
          align="center"
          justify="between"
          gap="2"
          style={{ flex: 1 }}
        >
          <Callout.Text>Chat with Augment yourself.</Callout.Text>
          <Button asChild size="2">
            <Link
              to="https://docs.augmentcode.com/introduction#get-started-in-minutes"
              target="_blank"
              rel="noopener noreferrer"
              style={{ display: "flex", alignItems: "center", gap: "6px" }}
            >
              Get Started
              <ExternalLinkIcon />
            </Link>
          </Button>
        </Flex>
        <IconButton
          size="1"
          variant="ghost"
          color="blue"
          highContrast={false}
          onClick={() => setIsVisible(false)}
          style={{ padding: "4px" }}
        >
          <Cross2Icon />
        </IconButton>
      </Flex>
    </Callout.Root>
  );
}

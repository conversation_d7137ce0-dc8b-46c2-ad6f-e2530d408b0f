import { Form } from "@remix-run/react";
import { Flex, Text, Box, Button } from "@radix-ui/themes";
import { useTheme } from "next-themes";
import "./share.css";

type ShareHeaderProps = {
  reader_email: string;
};

export default function ShareHeader({ reader_email }: ShareHeaderProps) {
  const { resolvedTheme } = useTheme();

  return (
    <Flex direction="row" justify="between" align="center" mb="4">
      <Flex align="start" gap="4">
        <img
          src={
            resolvedTheme === "dark" ? "/augment-dark.svg" : "/augment-logo.svg"
          }
          alt="Augment Logo"
          style={{ width: "230px", height: "39.66px" }}
        />
      </Flex>
      <Flex direction="column" align="end">
        <Text size="2" color="gray">
          {reader_email}
        </Text>
        <Box>
          <Form action="/logout">
            <Button
              size="1"
              color="gray"
              variant="outline"
              type="submit"
              className="logout-button"
            >
              Logout
            </Button>
          </Form>
        </Box>
      </Flex>
    </Flex>
  );
}

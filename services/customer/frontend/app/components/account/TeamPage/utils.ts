import type {
  EmailTagListProps,
  EmailValidationResult,
} from "app/components/ui/TagList";
import type { TeamSchema } from "app/schemas/team";
import { unique } from "app/utils/collection";
import { trim } from "app/utils/string";
import type { ExtractType } from "@augment-internal/ts-utils/type";

export function totalCurrentMembers(
  teamMembers: TeamSchema["users"],
  invitations: TeamSchema["invitations"],
) {
  return Math.max(teamMembers.length + invitations.length, 1);
}

export function seatsAvailable(
  totalCurrentMembers: number,
  totalSeats: number,
) {
  return Math.max(0, totalSeats - totalCurrentMembers);
}

export function newMembers(
  emailTags: EmailTagListProps["tags"],
  inputChange: TeamMemberValidationResult,
) {
  const emails = emailTags.map((tag) => tag.text);
  if (inputChange.isValid) {
    emails.push(inputChange.text);
  }
  return unique(emails.map(trim).filter(Boolean));
}

/**
 * Calculate how many additional seats are needed
 * We need to consider both active members and pending invitations
 * when calculating if we need more seats
 */
export function additionalSeatsNeeded(
  newTotalMembers: number,
  totalSeats: number,
) {
  return Math.max(0, newTotalMembers - totalSeats);
}

export type TeamMemberValidationResult =
  EmailValidationResult<"duplicate-member">;

export type DuplicateMemberValidationResult = ExtractType<
  TeamMemberValidationResult,
  "reason",
  "duplicate-member"
>;

export const validEmpty = { isValid: true, text: "" } as const;

export function validateIsNotAlreadyMember(existingMemberEmails: Set<string>) {
  return (email: string): TeamMemberValidationResult => {
    if (existingMemberEmails.has(email)) {
      return {
        isValid: false,
        reason: "duplicate-member",
        text: email,
      } satisfies DuplicateMemberValidationResult;
    }
    return validEmpty;
  };
}

export function isDuplicateMember(
  validationResult: TeamMemberValidationResult,
): validationResult is DuplicateMemberValidationResult {
  return (
    validationResult.isValid === false &&
    validationResult.reason === "duplicate-member"
  );
}

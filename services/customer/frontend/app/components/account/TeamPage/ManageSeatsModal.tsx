import { Button } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  subscriptionQueryOptions,
  teamQueryOptions,
  updateTeamSeats,
  userQueryOptions,
} from "app/client-cache";
import Modal from "app/components/ui/Modal";
import { isTeamActive } from "app/schemas/team";
import { useEffect, useState } from "react";
import { NumberControl } from "../../ui/NumberControl/NumberControl";
import { Badge } from "app/components/ui/Badge";
import { typography } from "app/utils/style";
import { Card } from "app/components/ui/Card/Card";
import * as utils from "./utils";
import { formatMoney } from "app/utils/string";
import { CommunityPlanWarning } from "./CommunityPlanWarning";

export function ManageSeatsModal() {
  const updateSeatsMutation = useMutation(updateTeamSeats);
  const { data: teamData } = useQuery(teamQueryOptions);
  const { data: userData } = useQuery(userQueryOptions);
  const { data: subscriptionData } = useQuery(subscriptionQueryOptions);
  const userIsAdmin = !!userData?.isAdmin;
  const [seats, setSeats] = useState<number>(NaN);
  useEffect(() => () => setSeats(NaN), []);
  if (!userData || !userIsAdmin || !isTeamActive(teamData) || !subscriptionData)
    return null;
  const planName = subscriptionData.planName ?? "plan";
  const { seats: totalSeats, users: teamMembers, invitations } = teamData.team;
  if (isNaN(seats)) {
    setSeats(totalSeats);
    return null;
  }

  function handleUpdateSeats() {
    if (seats === totalSeats) return;
    updateSeatsMutation.mutate(seats);
  }

  const totalMembers = utils.totalCurrentMembers(teamMembers, invitations);
  const maxSeats = subscriptionData.maxNumSeats;
  return (
    <Modal
      title="Manage Seats"
      onOpenChange={() => {
        setSeats(NaN);
      }}
      description={`Adjust the number of seats on your ${planName}`}
      trigger={(open) => (
        <Button onClick={open} aria-label="Manage Seats" variant="ghost">
          Manage Seats
        </Button>
      )}
      footer={(close) => (
        <>
          <Button variant="soft" onClick={close}>
            Cancel
          </Button>
          <Button onClick={close(handleUpdateSeats)}>Save changes</Button>
        </>
      )}
    >
      <div className="plan-info">
        <div className="plan-info-header">
          <div className="plan-name">{planName}</div>
          <div className="plan-price">
            Seats: ${subscriptionData.pricePerSeat} per user per month
          </div>
        </div>
        <Badge color="green">Current plan</Badge>
      </div>
      <Card className="seat-card">
        <div className="card-header">
          <div className="card-title">Seats</div>
          <div className="card-description">
            {totalMembers} currently in use
          </div>
        </div>
        <SeatControl
          currentSeats={seats}
          onChange={setSeats}
          minSeats={totalMembers}
          maxSeats={maxSeats}
        />
      </Card>
      <div className="proration-message">
        {
          "Seats you add are charged immediately (prorated); seats you remove stay active and won't lower charges until the next billing cycle."
        }
      </div>
      <div className="billing-info">
        <div className="billing-info-row">
          <span>Current monthly billing</span>
          <span>
            {formatMoney(
              totalSeats * parseFloat(subscriptionData.pricePerSeat),
            )}
          </span>
        </div>
        <div className="billing-info-row">
          <span>New monthly billing</span>
          <span>
            {formatMoney(seats * parseFloat(subscriptionData.pricePerSeat))}
          </span>
        </div>
      </div>
      <CommunityPlanWarning />
      <style scoped>{`
        :scope {
          display: flex;
          flex-direction: column;
          gap: var(--ds-spacing-4);
        }

        .plan-info {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        }

        .plan-info-header {
          display: flex;
          flex-direction: column;
          align-items: start;
          gap: var(--ds-spacing-1);

          .plan-name {
            ${typography.text3.bold}
          }

          .plan-price {
            ${typography.text3.regular}
            color: var(--ds-color-text-subtle);
          }
        }

        .plan-info-header {
          ${typography.text3.regular}
        }

        .seat-card {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        }

        .card-header {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: start;
        }

        .card-title {
          ${typography.text2.bold}
        }

        .card-description {
          ${typography.text3.regular}
          color: var(--ds-color-text-subtle);
        }

        .proration-message {
          ${typography.text2.regular}
          color: var(--ds-color-text-subtle);
        }

        .billing-info {
          display: flex;
          flex-direction: column;
          gap: var(--ds-spacing-1);
          ${typography.text3.regular}
          color: var(--ds-color-text-subtle);

          .billing-info-row {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: flex-end;
          }
        }
      `}</style>
    </Modal>
  );
}

type SeatControlProps = {
  currentSeats: number;
  onChange: (value: number) => void;
  minSeats: number;
  maxSeats: number;
};

function SeatControl({
  currentSeats,
  onChange,
  minSeats,
  maxSeats,
}: SeatControlProps) {
  const [seats, setSeats] = useState(currentSeats);
  function handleSeatsChange(value: number) {
    if (isNaN(value)) return;
    if (value < minSeats) return;
    setSeats(value);
    onChange(value);
  }
  return (
    <NumberControl
      value={seats}
      min={minSeats}
      max={maxSeats}
      onChange={handleSeatsChange}
    />
  );
}

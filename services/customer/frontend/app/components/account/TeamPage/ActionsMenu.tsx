import type { TeamData } from "./TeamTable";
import { DotsHorizontalIcon } from "@radix-ui/react-icons";
import { createCancelInvitationModal } from "./DeleteInvitationModal";
import { createRemoveMemberModal } from "./RemoveMemberFromTeamModal";
import { cn } from "app/utils/style";
import { Dropdown } from "app/components/ui/Dropdown/Dropdown";
import { Button } from "@radix-ui/themes";
import { useMemo } from "react";

type ActionsMenuProps = {
  member: TeamData;
  isAdmin: boolean;
  /** Whether the member is the current user */
  isSelf: boolean;
  className?: string;
};

/** @returns true if the actions menu should be shown for the given member */
export function shouldShowActionsMenu(
  member: TeamData,
  isAdmin: boolean,
  isSelf: boolean,
) {
  const memberIsPending = member.status === "Pending";
  // admins can remove anyone except themselves
  // non-admins can only remove pending members
  // (technically non admins can remove themselves but the UI for that is through "leave team")
  const canRemoveMember = (isAdmin || memberIsPending) && !isSelf;
  return canRemoveMember;
}

export function ActionsMenu(props: ActionsMenuProps) {
  const { member, isAdmin, isSelf, className } = props;
  const memberIsPending = member.status === "Pending";
  const memberIsAdmin = !memberIsPending && member.role === "ADMIN";
  const canRemoveMember = (isAdmin || !memberIsAdmin) && !isSelf;
  const {
    RemoveMemberTrigger,
    RemoveMemberModal, //
  } = useMemo(createRemoveMemberModal, []);
  const {
    CancelInvitationTrigger,
    CancelInvitationModal, //
  } = useMemo(createCancelInvitationModal, []);

  const dropdownItems = [];

  if (canRemoveMember && memberIsPending) {
    dropdownItems.push({
      type: "item" as const,
      value: "cancel-invitation",
      label: <CancelInvitationTrigger />,
      className: "cancel-invitation",
    });
  }

  if (canRemoveMember && !memberIsPending) {
    dropdownItems.push({
      type: "item" as const,
      value: "remove-member",
      label: <RemoveMemberTrigger />,
      className: "remove-member",
    });
  }

  if (dropdownItems.length === 0) {
    return null;
  }

  return (
    <div className={cn(className, "actions-menu")}>
      <Dropdown items={dropdownItems}>
        <Button variant="ghost" className="trigger-btn">
          <DotsHorizontalIcon />
        </Button>
      </Dropdown>
      <style scoped>{`
        :scope.actions-menu {
          color: var(--ds-color-text-default);

          .trigger-btn {
            aspect-ratio: 1;
            padding: 0;
            height: var(--ds-spacing-6);
          }
        }
      `}</style>
      <RemoveMemberModal member={member} />
      <CancelInvitationModal member={member} />
      <style>{`
        :scope {
          .cancel-invitation,
          .remove-member {
            color: var(--ds-color-error-9);
            padding: unset;
            height: fit-content;

            &[data-highlighted] {
              background-color: unset;
            }
            > * {
              margin: unset;
            }
          }
          .no-actions {
            color: var(--ds-color-text-subtle);
          }
        }
      `}</style>
    </div>
  );
}

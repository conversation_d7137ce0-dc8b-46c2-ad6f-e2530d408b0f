import { PersonIcon } from "@radix-ui/react-icons";
import { Button } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  addTeamMembers,
  subscriptionQueryOptions,
  teamQueryOptions,
  updateTeamSeats,
  userQueryOptions,
} from "app/client-cache";
import { Callout } from "app/components/ui/Callout";
import Modal from "app/components/ui/Modal";
import {
  EmailTagList,
  type EmailTagListProps,
  createTag,
} from "app/components/ui/TagList";
import { Tooltip } from "app/components/ui/Tooltip";
import { toast } from "app/components/ui/Toast";
import { isTeamActive } from "app/schemas/team";
import { p, pc } from "app/utils/plural";
import { typography } from "app/utils/style";
import { useState } from "react";
import * as utils from "./utils";
import { collect } from "app/utils/collection";
import { maybeType } from "app/utils/guards";
import { CommunityPlanWarning } from "./CommunityPlanWarning";

export function AddTeamMemberModal() {
  const [inputChange, setInputChange] =
    useState<utils.TeamMemberValidationResult>(utils.validEmpty);
  const [emailTags, setEmailTags] = useState<EmailTagListProps["tags"]>([]);
  const { data: teamData } = useQuery(teamQueryOptions);
  const { data: subscriptionData } = useQuery(subscriptionQueryOptions);
  const updateSeatsMutation = useMutation(updateTeamSeats);
  const userIsAdmin = !!useQuery(userQueryOptions).data?.isAdmin;
  const [validationErrors, setValidationErrors] = useState<
    utils.TeamMemberValidationResult[] | null
  >(null);
  const addMemberMutation = useMutation(addTeamMembers);
  if (!isTeamActive(teamData) || !subscriptionData) return null;
  const {
    users: teamMembers,
    seats: totalSeats,
    invitations = [],
  } = teamData.team;

  const totalCurrentMembers = utils.totalCurrentMembers(
    teamMembers,
    invitations,
  );
  const seatsAvailable = utils.seatsAvailable(totalCurrentMembers, totalSeats);
  const newMembers = utils.newMembers(emailTags, inputChange);
  const newTotalMembers = totalCurrentMembers + newMembers.length;
  const { maxNumSeats } = subscriptionData;
  const additionalSeatsNeeded = utils.additionalSeatsNeeded(
    newTotalMembers,
    totalSeats,
  );

  async function handleAddMembers() {
    if (inputChange.isValid && inputChange.text.length) {
      setInputChange(utils.validEmpty);
      emailTags.push(createTag(inputChange.text));
      setEmailTags([...emailTags]);
    }
    if (newTotalMembers > maxNumSeats) return false;

    try {
      // If we need more seats, update seats first
      if (additionalSeatsNeeded > 0) {
        await updateSeatsMutation.mutateAsync(newTotalMembers, {
          onSuccess: () => {
            toast.success({
              title: "Seats updated",
              description: `Successfully added ${pc(additionalSeatsNeeded, "new seat", "new seats")}.`,
            });
          },
        });
      }

      // Then add the members
      await addMemberMutation.mutateAsync(
        emailTags.map((tag) => tag.text),
        {
          onSuccess: () => {
            // Clear the form state before the modal closes
            toast.success({
              title: "Team members added",
              description: (
                <>
                  Successfully added{" "}
                  {newMembers.length === 1 ? (
                    <b>{newMembers[0]}</b>
                  ) : (
                    <>{newMembers.length} new team members</>
                  )}
                  .
                </>
              ),
            });
            setEmailTags([]);
            setInputChange(utils.validEmpty);
            newMembers.length = 0;
            clearValidationErrors();
          },
        },
      );

      // If we get here, the mutation was successful
      return true;
    } catch (error) {
      // Error handling is already done by the mutations
      toast.error({
        title: "Failed to add members",
        description: "Please try again.",
      });
      return false;
    }
  }
  const clearValidationErrors = () => setValidationErrors(null);
  const handleEmailTagsChange = (tags: EmailTagListProps["tags"]) => {
    setEmailTags(tags);
    clearValidationErrors();
  };
  const existingMemberEmails = new Set([
    ...teamMembers.map((member) => member.email),
    ...invitations.map((invitation) => invitation.email),
  ]);

  return (
    <Modal
      onOpenChange={(isOpen) => {
        if (isOpen) return;
        // Check if there are unsaved changes
        const isDirty = newMembers.length > 0;
        if (isDirty) {
          const shouldClose = confirm(
            "You have unsaved changes. Are you sure you want to close this dialog?",
          );
          if (!shouldClose) {
            return false;
          }
        }

        setEmailTags([]);
        setInputChange(utils.validEmpty);
        clearValidationErrors();
      }}
      trigger={(open) => (
        <Tooltip
          content="No seats available. Contact your admin to add more seats."
          enabled={!userIsAdmin && seatsAvailable === 0}
        >
          <Button
            onClick={open}
            disabled={!userIsAdmin && seatsAvailable === 0}
            aria-label="Add Members"
          >
            <PersonIcon aria-hidden="true" /> Add Members
          </Button>
        </Tooltip>
      )}
      title="Add Members"
      footer={(close) => (
        <>
          <Button variant="soft" onClick={close}>
            Cancel
          </Button>
          <Button
            onClick={close(handleAddMembers)}
            disabled={
              newMembers.length === 0 ||
              newTotalMembers > maxNumSeats ||
              addMemberMutation.isPending ||
              updateSeatsMutation.isPending ||
              validationErrors !== null
            }
          >
            {addMemberMutation.isPending || updateSeatsMutation.isPending
              ? "Processing..."
              : `Add ${newMembers.length > 0 ? newMembers.length : ""} ${p(newMembers.length, "Member", "Members")}`}
          </Button>
        </>
      )}
    >
      <div className="email-input-container">
        <EmailTagList
          inputRef={(el) => el?.focus()}
          aria-label="emails-input"
          tags={emailTags}
          onTagsChange={handleEmailTagsChange}
          inputValue={inputChange.text}
          onInputChange={(_, validationResult) => {
            setInputChange(validationResult);
            clearValidationErrors();
          }}
          emailValidator={utils.validateIsNotAlreadyMember(
            existingMemberEmails,
          )}
          onValidationError={setValidationErrors}
          placeholder="Type email addresses and press Enter..."
        />
        <EmailValidatorErrors
          validationErrors={collect(
            validationErrors,
            maybeType(inputChange, utils.isDuplicateMember),
          )}
        />
        <CommunityPlanWarning />
        <Callout enabled={newTotalMembers > maxNumSeats}>
          You can&apos;t add more members than your current plan allows.{" "}
          {userIsAdmin
            ? "Please upgrade your plan to add more members."
            : "Contact your admin to upgrade your plan."}
        </Callout>
        <Callout
          type="info"
          className="seat-warning"
          enabled={additionalSeatsNeeded > 0}
        >
          Adding {pc(newMembers.length, "member", "members")} will require{" "}
          {pc(additionalSeatsNeeded, "additional seat", "additional seats")}.
          Your account will be updated automatically.
        </Callout>
        <Callout
          type="error"
          enabled={addMemberMutation.isError || updateSeatsMutation.isError}
        >
          {addMemberMutation.error?.message ||
            updateSeatsMutation.error?.message ||
            "An error occurred"}
        </Callout>
        <style scoped>{`
          :scope.email-input-container {
            display: flex;
            flex-direction: column;
            gap: var(--ds-spacing-4);

            .emails-input {
              width: 100%;
              padding: var(--ds-spacing-2) var(--ds-spacing-3);
              border-radius: var(--ds-radius-2);
              border: 1px solid var(--ds-color-neutral-6);
              ${typography.text2.regular}
              color: var(--ds-color-text-default);
              margin-top: var(--ds-spacing-1);
            }
          }
        `}</style>
      </div>
    </Modal>
  );
}

type EmailValidatorErrorsProps = {
  validationErrors: utils.TeamMemberValidationResult[] | null;
};

function EmailValidatorErrors({ validationErrors }: EmailValidatorErrorsProps) {
  if (!validationErrors) return null;
  return [
    validationErrors.some(
      (error) => !error.isValid && error.reason === "duplicate-member",
    ) && (
      <Callout type="error" key="duplicate-member">
        Some email addresses are already members of this team.
      </Callout>
    ),
    validationErrors.some(
      (error) => !error.isValid && error.reason === "invalid-email",
    ) && (
      <Callout type="error" key="invalid-email">
        Some email addresses are invalid.
      </Callout>
    ),
  ];
}

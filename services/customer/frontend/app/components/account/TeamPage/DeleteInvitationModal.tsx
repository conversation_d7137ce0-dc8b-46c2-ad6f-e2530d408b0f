import { PersonIcon } from "@radix-ui/react-icons";
import { Button } from "@radix-ui/themes";
import { useMutation } from "@tanstack/react-query";
import { deleteTeamInvitation } from "app/client-cache";
import Modal from "app/components/ui/Modal";
import { toast } from "app/components/ui/Toast";
import type { TeamMemberSchema } from "app/schemas/team";
import { typography } from "app/utils/style";
import { type PrimitiveAtom, atom, useAtom, useSetAtom } from "jotai";

type CancelInvitationTriggerProps = {
  enabledAtom: PrimitiveAtom<boolean>;
};

export function createCancelInvitationModal() {
  const cancelInviteModalAtom = atom(false);
  return {
    CancelInvitationTrigger: ({
      ...props
    }: Omit<CancelInvitationTriggerProps, "enabledAtom">) => (
      <CancelInvitationTrigger {...props} enabledAtom={cancelInviteModalAtom} />
    ),
    CancelInvitationModal: (
      props: Omit<CancelInvitationModalProps, "enabledAtom">,
    ) => (
      <CancelInvitationModal {...props} enabledAtom={cancelInviteModalAtom} />
    ),
  };
}

/**
 * This is the Remote trigger for the modal.
 * CancelInvitationTrigger is in the ActionsMenu dropdown.
 * Since the dropdown closes when CancelInvitationTrigger is clicked,
 * we cannot have the modal and trigger in the same component.
 * To fix this, we have the trigger as a separate component from the modal.
 */
function CancelInvitationTrigger(props: CancelInvitationTriggerProps) {
  const setIsOpen = useSetAtom(props.enabledAtom);
  return (
    <Button onClick={() => setIsOpen(true)} variant="ghost" color="red">
      Cancel Invitation
    </Button>
  );
}

export type CancelInvitationModalProps = {
  member: Pick<TeamMemberSchema, "email" | "id">;
  enabledAtom: PrimitiveAtom<boolean>;
};

function CancelInvitationModal({
  member,
  enabledAtom,
}: CancelInvitationModalProps) {
  const [isOpen, setIsOpen] = useAtom(enabledAtom);
  const deleteInvitationMutation = useMutation(deleteTeamInvitation);
  function handleCancelInvitation() {
    deleteInvitationMutation.mutate(member.id, {
      onSuccess: () => {
        toast.success({
          title: "Invitation canceled",
          description: `Successfully canceled invitation for ${member.email}.`,
        });
      },
      onError: (error) => {
        toast.error({
          title: "Failed to cancel invitation",
          description: error.message || "Please try again.",
        });
      },
    });
  }

  return (
    <Modal
      isOpen={isOpen}
      onOpenChange={setIsOpen}
      className="cancel-invitation-modal"
      maxWidth={"375px"}
      footer={(close) => (
        <>
          <Button variant="soft" onClick={close}>
            Cancel
          </Button>
          <Button
            onClick={close(handleCancelInvitation)}
            disabled={deleteInvitationMutation.isPending}
            variant="solid"
            color="red"
          >
            Confirm
          </Button>
        </>
      )}
    >
      <div className="delete-invite-modal-body">
        <PersonIcon className="person-icon" />
        <div className="title">Cancel Invitation</div>
        <div className="message-are-you-sure">
          Are you sure you want to cancel the invitation for{" "}
          <span className="email" title={member.email}>
            {member.email}
          </span>
          ?
        </div>
      </div>
      <style scoped>{`
        :scope .delete-invite-modal-body {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: var(--ds-spacing-2);
          ${typography.text3.regular}
          color: var(--ds-color-text-subtle);

          .person-icon {
            width: var(--ds-spacing-8);
            height: var(--ds-spacing-8);
            padding: var(--ds-spacing-3);
            background-color: var(--ds-color-error-3);
            border-radius: 50%;
            ${typography.text5.bold}
            color: var(--ds-color-error-9);
            margin-bottom: var(--ds-spacing-2);
          }

          .title {
            ${typography.text4.bold}
          }
          
          .message-are-you-sure {
            display: block;
          }

          .email {
            ${typography.text2.bold}
            color: var(--ds-color-text-subtle);
            max-width: 200px;
            overflow: hidden;
            padding-bottom: 0.25rem;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
            line-height: 14px;
            vertical-align: middle;
          }
        }
      `}</style>
    </Modal>
  );
}

import type { <PERSON>a, StoryObj } from "@storybook/react";
import {
  createCancelInvitationModal,
  type CancelInvitationModalProps,
} from "./DeleteInvitationModal";

const meta = {
  title: "Components/Account/TeamPage/CancelInvitationModal",
  component: ({
    ...props
  }: Omit<CancelInvitationModalProps, "enabledAtom">) => {
    const { CancelInvitationTrigger, CancelInvitationModal } =
      createCancelInvitationModal();
    return (
      <>
        <CancelInvitationTrigger />
        <CancelInvitationModal {...props} />
      </>
    );
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CancelInvitationModalProps>;

export default meta;
type Story = StoryObj<CancelInvitationModalProps>;

export const Default: Story = {
  args: {
    member: {
      id: "invitation-1",
      email: "<EMAIL>",
    },
  },
};

export const WithLongEmail: Story = {
  args: {
    member: {
      id: "invitation-2",
      email:
        "<EMAIL>",
    },
  },
};

import { Heading, Spinner } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { isTeamPending } from "app/schemas/team";
import { typography } from "app/utils/style";
import { Callout } from "../../ui/Callout";
import { pc } from "app/utils/plural";
import { teamQueryOptions, userQueryOptions } from "app/client-cache";
import { AddTeamMemberModal } from "./AddTeamMemberModal";
import { ManageSeatsModal } from "./ManageSeatsModal";
import { TeamTable } from "./TeamTable";
import { DangerZone } from "./DangerZone";
import { Enabled } from "app/components/ui/Enabled";
import * as utils from "app/components/account/TeamPage/utils";
import { ProgressPage } from "app/components/ui/ProgressPage";

export function TeamPage() {
  const {
    data: teamData,
    isError: teamIsError,
    isPending: teamIsPending,
    error: teamError,
  } = useQuery(teamQueryOptions);
  const userIsAdmin = !!useQuery(userQueryOptions).data?.isAdmin;

  if (teamIsPending) {
    return (
      <ProgressPage
        title="Loading Your Account"
        description="Fetching your account data and settings."
        message="Augment is building the site that you're about to use from scratch!"
        progressColor="ds-color-accent"
        icon={<Spinner />}
        initialProgress={0}
        maxProgress={90}
        updateInterval={200}
        progressIncrement={30}
      />
    );
  }

  if (teamIsError) throw teamError;

  if (isTeamPending(teamData)) {
    return (
      <Callout type="info">
        <h2 className="text-lg font-semibold">Team Pending</h2>
        <p>Your team is still being created. Please try again later.</p>
      </Callout>
    );
  }

  const { users: teamMembers, seats: totalSeats, invitations } = teamData.team;
  const totalCurrentMembers = utils.totalCurrentMembers(
    teamMembers,
    invitations,
  );
  const seatsAvailable = utils.seatsAvailable(totalCurrentMembers, totalSeats);
  let availableStr = pc(seatsAvailable, "seat", "seats");
  if (seatsAvailable === 0) {
    availableStr = "No seats";
  }

  return (
    <div className="team-page">
      <div className="team-header">
        <div>
          <Heading size="8" weight="bold" as="h1">
            Team Members
          </Heading>
          <div className="team-stats">
            <span className="team-stat" aria-live="polite">
              {pc(totalSeats, "seat", "seats")} purchased. {availableStr}{" "}
              available.
            </span>
          </div>
        </div>
        <div className="team-actions">
          <ManageSeatsModal />
          <AddTeamMemberModal />
        </div>
      </div>
      <TeamTable />
      <Enabled enabled={!userIsAdmin} asChild>
        <DangerZone />
      </Enabled>

      <style scoped>{`
        :scope.team-page {
          padding-bottom: var(--ds-spacing-10);
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: var(--ds-spacing-5);

          .team-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--ds-spacing-4) 0 var(--ds-spacing-4) 0;

            .team-stats {
              display: flex;
              gap: var(--ds-spacing-2);
              margin-top: var(--ds-spacing-2);
              ${typography.text3.regular}
              color: var(--ds-color-text-subtle);
            }

            .team-actions {
              display: flex;
              gap: var(--ds-spacing-4);
              align-items: center;
            }
          }

          .error-message {
            color: var(--ds-color-error-9);
            ${typography.text2.regular}
            margin-top: var(--ds-spacing-2);
          }
        }
      `}</style>
    </div>
  );
}

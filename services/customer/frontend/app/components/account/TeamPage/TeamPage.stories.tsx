import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { TeamPage } from "./TeamPage";
import { addDays, formatISO } from "date-fns";
import { TeamSchema, type TeamMemberSchema } from "app/schemas/team";
import { delay } from "@augment-internal/ts-utils/timer";
import mocks from "app/mocks";
import { generateMock } from "@anatine/zod-mock";
import { HttpResponse } from "msw";

const mockedTeam = mocks.team.GET.Response[200].active;
mockedTeam.team.seats = 10;
mockedTeam.team.users = generateMock(TeamSchema).users;
mockedTeam.team.invitations = generateMock(TeamSchema).invitations;

const meta = {
  title: "Pages/Account/TeamPage",
  component: TeamPage,
  parameters: {
    layout: "fullscreen",
    msw: {
      overrides: (http) => [
        http.get("/api/team", async () => {
          await delay(1000);
          return HttpResponse.json(mockedTeam);
        }),
      ],
      delay: {
        user: 1000,
      },
    },
  },
} satisfies Meta<typeof TeamPage>;

export default meta;
type Story = StoryObj<typeof TeamPage>;

// Generate mock team members
const generateMockTeamMembers = (
  count: number,
  adminIndex = 0,
): TeamMemberSchema[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: `user-${index + 1}`,
    email: `user${index + 1}@example.com`,
    name: `User ${index + 1}`,
    role: index === adminIndex ? "ADMIN" : "MEMBER",
    joinedAt: formatISO(addDays(new Date(), -index * 30)),
  }));
};

// Generate mock invitations
const generateMockInvitations = (count: number) => {
  return Array.from({ length: count }, (_, index) => ({
    id: `invitation-${index + 1}`,
    email: `pending${index + 1}@example.com`,
    invitedAt: formatISO(addDays(new Date(), -index * 2)),
  }));
};

export const AdminView: Story = {
  args: {
    isAdmin: false,
    users: generateMockTeamMembers(5),
    invitations: generateMockInvitations(3),
    seatsAvailable: 7,
  },
};

export const NoSeatsAvailable: Story = {
  args: {
    isAdmin: true,
    users: generateMockTeamMembers(10),
    seatsAvailable: 0,
  },
};

export const EmptyTeam: Story = {
  args: {
    isAdmin: true,
    users: [],
    seatsAvailable: 10,
  },
};

export const LargeTeam: Story = {
  args: {
    isAdmin: true,
    users: generateMockTeamMembers(20, 5),
    seatsAvailable: 7,
  },
};

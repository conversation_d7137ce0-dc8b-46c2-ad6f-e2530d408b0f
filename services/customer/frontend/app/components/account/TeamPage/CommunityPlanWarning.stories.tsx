import type { <PERSON>a, StoryObj } from "@storybook/react";
import { CommunityPlanWarning } from "./CommunityPlanWarning";
import mocks from "app/mocks";
import { delay } from "@augment-internal/ts-utils/timer";
import { HttpResponse } from "msw";

const meta = {
  title: "Components/Account/TeamPage/CommunityPlanWarning",
  component: CommunityPlanWarning,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof CommunityPlanWarning>;

export default meta;
type Story = StoryObj<typeof CommunityPlanWarning>;

export const Visible: Story = {
  parameters: {
    msw: {
      overrides: (http) => [
        http.get("/api/team", async () => {
          await delay(1000);
          return HttpResponse.json(mocks.team.GET.Response[200].none);
        }),
      ],
    },
  },
};

export const Hidden: Story = {
  parameters: {},
};

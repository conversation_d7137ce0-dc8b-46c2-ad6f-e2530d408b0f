import type { <PERSON>a, StoryObj } from "@storybook/react";
import { ManageSeatsModal } from "./ManageSeatsModal";
import { delay } from "@augment-internal/ts-utils/timer";
import mocks from "app/mocks";
import { HttpResponse } from "msw";

const mockedTeam = mocks.team.GET.Response[200].active;
mockedTeam.team.seats = 10;

const mockedUser = mocks.user.GET.Response[200];

const mockSubscription = mocks.subscription.GET.Response[200];

const meta = {
  title: "Components/Account/TeamPage/ManageSeatsModal",
  component: ManageSeatsModal,
  parameters: {
    layout: "fullscreen",
    msw: {
      overrides: (http) => [
        http.get("/api/team", async () => {
          await delay(1000);
          return HttpResponse.json(mockedTeam);
        }),
        http.get("/api/user", async () => {
          await delay(1000);
          return HttpResponse.json(mockedUser);
        }),
        http.get("/api/subscription", async () => {
          await delay(1000);
          return HttpResponse.json(mockSubscription);
        }),
      ],
    },
  },
} satisfies Meta<typeof ManageSeatsModal>;

export default meta;
type Story = StoryObj<typeof ManageSeatsModal>;

export const Default: Story = {
  args: {},
};

// Create a story with a mock team (isMock=true) to show the community plan warning
export const WithCommunityPlanWarning: Story = {
  parameters: {
    msw: {
      overrides: (http) => [
        http.get("/api/team", async () => {
          await delay(1000);
          return HttpResponse.json(mocks.team.GET.Response[200].none);
        }),
        http.get("/api/user", async () => {
          await delay(1000);
          return HttpResponse.json(mockedUser);
        }),
        http.get("/api/subscription", async () => {
          await delay(1000);
          return HttpResponse.json(mockSubscription);
        }),
      ],
    },
  },
};

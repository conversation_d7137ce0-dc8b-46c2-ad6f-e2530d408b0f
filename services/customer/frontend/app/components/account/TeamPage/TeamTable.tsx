import { ClockIcon } from "@radix-ui/react-icons";
import { Table } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { teamQueryOptions, userQueryOptions } from "app/client-cache";
import { isTeamActive, type TeamSchema } from "app/schemas/team";
import { isNullable } from "app/utils/guards";
import { assign } from "app/utils/object";
import { alphabeticalSort } from "app/utils/string";
import type { IntersectedUnion } from "@augment-internal/ts-utils/type";
import { format } from "date-fns";
import { ActionsMenu, shouldShowActionsMenu } from "./ActionsMenu";
import { IconBadge, Badge } from "app/components/ui/Badge";
import { SearchInput } from "app/components/ui/Input";
import { useState } from "react";
import { Dropdown } from "app/components/ui/Select";

type Status = "All statuses" | "Active" | "Pending";

export type TeamData = IntersectedUnion<
  TeamSchema["users"][number] | TeamSchema["invitations"][number]
> & {
  status: "Active" | "Pending";
};

export function TeamTable() {
  const { data: userData } = useQuery(userQueryOptions);
  const { data: teamData } = useQuery(teamQueryOptions);
  const userIsAdmin = !!userData?.isAdmin;
  const [searchTerm, setSearchTerm] = useState("");
  const [filter, setFilter] = useState<Status>("All statuses");

  if (!isTeamActive(teamData) || isNullable(userData)) return null;

  const { users: teamMembers, invitations = [] } = teamData.team;
  // Create a sorted combined list of members and invitations
  const sortedTeamData = [
    ...teamMembers.map(assign({ status: "Active" } as const)),
    ...invitations.map(assign({ status: "Pending" } as const)),
  ]
    .filter((member) => {
      return (
        member.email.includes(searchTerm) &&
        (filter === "All statuses" || filter === member.status)
      );
    })
    .sort((a, b) => alphabeticalSort(a.email, b.email)) satisfies TeamData[];

  const shouldShowActionsColumn = sortedTeamData.some((member) =>
    shouldShowActionsMenu(member, userIsAdmin, member.email === userData.email),
  );

  return (
    <div>
      <div className="filter-row">
        <SearchInput
          value={searchTerm}
          onChange={setSearchTerm}
          placeholder="Search members..."
          className="search-input"
        />
        <Dropdown
          // icon={FunnelIcon}
          className="status-filter"
          value={filter}
          onChange={setFilter}
          items={
            [
              { value: "All statuses", label: "All statuses" },
              { value: "Active", label: "Active" },
              { value: "Pending", label: "Pending" },
            ] as const
          }
        />
      </div>
      <Table.Root variant="surface" className="team-members">
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeaderCell className="email-column">
              Email
            </Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell className="status-column">
              Status
            </Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell className="date-column">
              Date Added
            </Table.ColumnHeaderCell>
            {shouldShowActionsColumn && (
              <Table.ColumnHeaderCell className="action-column">
                Actions
              </Table.ColumnHeaderCell>
            )}
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {sortedTeamData.map((member) => {
            const memberIsPending = member.status === "Pending";
            const memberIsAdmin = !memberIsPending && member.role === "ADMIN";
            return (
              <Table.Row key={member.id}>
                <Table.Cell className="email-column">
                  {member.email}{" "}
                  <Badge
                    color="amber"
                    className="admin-badge"
                    enabled={memberIsAdmin}
                  >
                    Admin
                  </Badge>
                </Table.Cell>
                <Table.Cell className="status-column">
                  {memberIsPending ? (
                    <IconBadge
                      color="orange"
                      icon={<ClockIcon />}
                      className="pending-badge"
                    >
                      Pending
                    </IconBadge>
                  ) : (
                    <Badge color="green">Active</Badge>
                  )}
                </Table.Cell>
                <Table.Cell className="date-column">
                  {format(
                    new Date(
                      memberIsPending ? member.invitedAt : member.joinedAt,
                    ),
                    "MMM dd, yyyy",
                  )}
                </Table.Cell>
                {shouldShowActionsColumn && (
                  <Table.Cell className="action-column">
                    <ActionsMenu
                      member={member}
                      isAdmin={userIsAdmin}
                      isSelf={member.email === userData.email}
                      className="action-menu"
                    />
                  </Table.Cell>
                )}
              </Table.Row>
            );
          })}
          {sortedTeamData.length === 0 && (
            <Table.Row>
              <Table.Cell colSpan={4} className="no-members">
                No members found
              </Table.Cell>
            </Table.Row>
          )}
        </Table.Body>
      </Table.Root>
      <style scoped>{`
        :scope {
          display: flex;
          flex-direction: column;
          gap: var(--ds-spacing-5);
        }

        .filter-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: var(--ds-spacing-5);

          .search-input {
            width: 100%;
          }
        }

        .team-members {
          tr {
            line-height: var(--ds-spacing-7);
          }

          .email-column {
            width: auto;
            min-width: 200px;
            white-space: nowrap;
          }
          .admin-badge {
            margin-left: var(--ds-spacing-2);
          }

          .status-column {
            width: 100px;
            white-space: nowrap;
          }

          .date-column {
            width: 150px;
            white-space: nowrap;
          }

          .action-column {
            width: 60px;
          }

          .no-members {
            text-align: center;
            color: var(--ds-color-text-subtle);
          }

          .action-menu {
            padding-left: var(--ds-spacing-3);
            display: flex;
            align-items: center;
            justify-content: flex-start;
            height: 100%;
            width: 100%;
          }
        }
      `}</style>
    </div>
  );
}

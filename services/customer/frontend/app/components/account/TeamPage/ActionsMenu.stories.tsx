import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ActionsMenu } from "./ActionsMenu";

const meta = {
  title: "Components/Account/TeamPage/ActionsMenu",
  component: ActionsMenu,
  parameters: {
    layout: "centered",
  },
  argTypes: {
    isAdmin: {
      control: "boolean",
      description: "Whether the current user is an admin",
    },
  },
} satisfies Meta<typeof ActionsMenu>;

export default meta;
type Story = StoryObj<typeof ActionsMenu>;

export const ForTeamMember: Story = {
  args: {
    member: {
      id: "user-1",
      email: "<EMAIL>",
      name: "Team Member",
      role: "MEMBER",
      status: "Active",
    },
    isAdmin: true,
    isSelf: false,
  },
};

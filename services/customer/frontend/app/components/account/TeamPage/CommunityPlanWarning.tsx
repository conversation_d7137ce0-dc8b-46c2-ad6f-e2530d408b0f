import { useQuery } from "@tanstack/react-query";
import { Callout } from "app/components/ui/Callout";
import { teamQueryOptions } from "app/client-cache";

/**
 * A warning callout that displays when a team has isMock=true,
 * informing users that once they add seats and create a team,
 * they will be unable to switch to the community plan.
 */
export function CommunityPlanWarning() {
  const { data: teamData } = useQuery(teamQueryOptions);

  // Only show the warning if the team is a mock team (isMock=true)
  const isMockTeam =
    teamData && "isMock" in teamData && teamData.isMock === true;

  return (
    <Callout type="warning" enabled={isMockTeam} size="small">
      Once you add seats or invite team members, you will be unable to switch to
      the Community Plan.
    </Callout>
  );
}

import type { Meta, StoryObj } from "@storybook/react";
import {
  createRemoveMemberModal,
  type RemoveMemberModalProps,
} from "./RemoveMemberFromTeamModal";

const meta = {
  title: "Components/Account/TeamPage/RemoveMemberModal",
  component: ({ ...props }: Omit<RemoveMemberModalProps, "enabledAtom">) => {
    const { RemoveMemberTrigger, RemoveMemberModal } =
      createRemoveMemberModal();
    return (
      <>
        <RemoveMemberTrigger />
        <RemoveMemberModal {...props} />
      </>
    );
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<RemoveMemberModalProps>;

export default meta;
type Story = StoryObj<RemoveMemberModalProps>;

export const Default: Story = {
  args: {
    member: {
      id: "user-1",
      email: "<EMAIL>",
    },
  },
};

export const WithLongEmail: Story = {
  args: {
    member: {
      id: "user-2",
      email:
        "<EMAIL>",
    },
  },
};

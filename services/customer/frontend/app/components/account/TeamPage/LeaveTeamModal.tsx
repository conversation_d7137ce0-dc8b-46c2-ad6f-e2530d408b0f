import { <PERSON><PERSON>, <PERSON> } from "@radix-ui/themes";
import { useNavigate } from "@remix-run/react";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  removeTeamMember,
  teamQueryOptions,
  userQueryOptions,
} from "app/client-cache";
import Modal from "app/components/ui/Modal";
import { isTeamActive } from "app/schemas/team";
import { isNullable } from "app/utils/guards";
import { typography } from "app/utils/style";

export function LeaveTeamModal() {
  const { data: userData } = useQuery(userQueryOptions);
  const { data: teamData } = useQuery(teamQueryOptions);
  const removeMemberMutation = useMutation(removeTeamMember);
  const navigate = useNavigate();

  if (isNullable(userData) || !isTeamActive(teamData)) return null;
  const { email } = userData;
  const { users: teamMembers } = teamData.team;
  const memberId = teamMembers.find((member) => member.email === email)?.id;
  if (isNullable(memberId)) return null;

  function handleLeaveTeam() {
    removeMemberMutation.mutate(memberId!, {
      onSuccess: () => {
        navigate("/logout");
      },
    });
  }

  return (
    <Modal
      title="Are you sure you want to leave this team?"
      description="This will remove your access to the team plan and user requests."
      trigger={(open) => (
        <Button
          onClick={open}
          className="leave-team-button"
          variant="outline"
          color="red"
        >
          Leave team
        </Button>
      )}
      footer={(close) => (
        <>
          <Button onClick={close} variant="soft">
            Cancel
          </Button>
          <Button onClick={close(handleLeaveTeam)} variant="solid" color="red">
            Leave team
          </Button>
        </>
      )}
    >
      <Card className="leave-team-card">
        <div className="card-title">What happens when you leave the team</div>
        <div className="card-description">
          <ul>
            <li>You will lose access to any available team user requests</li>
            <li>You can be invited back to the team</li>
            <li>You may still use Augment with the same email address</li>
            <li>You will be logged out of Augment</li>
            <li>You will need to select a new plan when you sign in</li>
          </ul>
        </div>
      </Card>
      <style scoped>{`
      :scope {
        display: flex;
        align-items: center;
        gap: var(--ds-spacing-2);
        color: var(--ds-color-text-subtle);

        .leave-team-card {
          width: 100%;
          margin-top: var(--ds-spacing-4);
          display: flex;
          flex-direction: column;
          gap: var(--ds-spacing-1);
          background-color: var(--ds-color-neutral-6);

          ul {
            list-style: disc;
            padding-left: var(--ds-spacing-5);
          }
        }

        .card-title {
          ${typography.text3.bold}
        }

        .card-description {
          ${typography.text3.regular}
          color: var(--ds-color-text-subtle);
        }
      }
    `}</style>
    </Modal>
  );
}

import { Flex, Text, Card } from "@radix-ui/themes";
import { CalendarIcon } from "@radix-ui/react-icons";

interface SubscriptionInfoProps {
  subscription: {
    isTrialing: boolean;
    trialEnd?: number;
    currentPeriodEnd: number;
    cancelAtPeriodEnd: boolean;
    isCancelled?: boolean;
  };
  planColor?: string;
}

export default function SubscriptionInfo({
  subscription,
  planColor = "var(--blue-9)",
}: SubscriptionInfoProps) {
  const formatDate = (timestamp: number) =>
    new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });

  return (
    <Card
      variant="surface"
      style={{
        borderColor: `color-mix(in srgb, ${planColor} 30%, var(--gray-5))`,
        background: `linear-gradient(135deg, color-mix(in srgb, ${planColor} 5%, white) 0%, white 100%)`,
        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.04)",
        width: "100%",
      }}
    >
      <Flex direction="column" gap="3">
        <Flex align="center" gap="2">
          <CalendarIcon style={{ color: planColor }} />
          <Text
            size="2"
            weight="medium"
            style={{
              background: `linear-gradient(90deg, ${planColor}, color-mix(in srgb, ${planColor} 70%, #000))`,
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
            }}
          >
            Subscription Details
          </Text>
        </Flex>

        {subscription.isTrialing && subscription.trialEnd && (
          <Flex
            direction={{ initial: "column", sm: "row" }}
            justify="between"
            gap="1"
          >
            <Text size="2" color="gray">
              Trial ends on
            </Text>
            <Text size="2" weight="medium" style={{ color: planColor }}>
              {formatDate(subscription.trialEnd)}
            </Text>
          </Flex>
        )}

        <Flex
          direction={{ initial: "column", sm: "row" }}
          justify="between"
          gap="1"
        >
          <Text size="2" color="gray">
            {subscription.isTrialing
              ? "First billing date"
              : "Next billing date"}
          </Text>
          <Text size="2" weight="medium" style={{ color: planColor }}>
            {formatDate(subscription.currentPeriodEnd)}
          </Text>
        </Flex>

        {subscription.cancelAtPeriodEnd && (
          <Text size="2" color="red" weight="medium">
            {subscription.isTrialing
              ? "Trial will be cancelled at end of period"
              : "Cancels at end of billing period"}
          </Text>
        )}
      </Flex>
    </Card>
  );
}

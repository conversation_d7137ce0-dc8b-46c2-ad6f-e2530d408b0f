import type { Meta } from "@storybook/react";
import { CancelSubscriptionModal } from "./CancelSubscriptionModal";
import { useState } from "react";
import mocks from "app/mocks";
import { delay } from "@augment-internal/ts-utils/timer";
import { HttpResponse } from "msw";

const meta = {
  title: "Components/Account/Billing/CancelSubscriptionModal",
  component: CancelSubscriptionModal,
  parameters: {
    layout: "centered",
    msw: {
      overrides: (http) => [
        http.get("/api/plans", async () => {
          await delay(1000);
          const response = mocks.plans.GET.Response[200];
          response[0].augmentPlanType = "community";
          return HttpResponse.json(response);
        }),
      ],
    },
  },
} satisfies Meta<typeof CancelSubscriptionModal>;

export default meta;

export const Default = () => {
  const [isOpen, setIsOpen] = useState(true);
  return <CancelSubscriptionModal isOpen={isOpen} onOpenChange={setIsOpen} />;
};

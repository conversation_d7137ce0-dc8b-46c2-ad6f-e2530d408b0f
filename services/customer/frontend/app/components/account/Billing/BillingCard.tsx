import { Button, Flex, Link, Text, Card, Skeleton } from "@radix-ui/themes";
import { IdCardIcon } from "@radix-ui/react-icons";
import { format, toDate } from "date-fns";
import { useQuery } from "@tanstack/react-query";
import { subscriptionQueryOptions, userQueryOptions } from "app/client-cache";
import { paymentQueryOptions } from "app/client-cache/queries/payment";
import { CalendarIcon } from "./CalendarIcon";
import { Callout } from "app/components/ui/Callout";

export type BillingCardProps = {
  handleSetupPayment: () => void;
  handleClearPaymentMethod: () => void;
  setIsPlanPickerOpen: (isOpen: boolean) => void;
  isSettingUpPayment: boolean;
};

export function BillingCard({
  handleSetupPayment,
  handleClearPaymentMethod,
  setIsPlanPickerOpen,
  isSettingUpPayment,
}: BillingCardProps) {
  const userIsAdmin = !!useQuery(userQueryOptions).data?.isAdmin;
  const subscriptionQuery = useQuery(subscriptionQueryOptions);
  const paymentQuery = useQuery(paymentQueryOptions);

  const subscriptionData = subscriptionQuery.data ?? {
    // mock data helps with skeletons
    portalUrl: null,
    billingPeriodEnd: null,
    augmentPlanType: "trial",
    billingCycleBillingAmount: 0,
  };

  const hasPaymentMethod = paymentQuery.data?.hasPaymentMethod ?? false;

  return (
    <Card size="3">
      <style scoped>
        {`
          :scope {
            flex: 1;
            display: flex;
            flex-direction: column;

            .c-billing-card__flex-wrapper {
              height: 100%;
            }

            .c-billing-card__button-container {
              width: 100%;
            }

            .c-billing-card__button {
              flex-grow: 1;
            }

            .subtle-text {
              color: var(--ds-color-neutral-11);
              font-size: var(--ds-font-size-2);
            }

            .c-billing-card__payment-error {
              margin-top: var(--ds-spacing-2);
            }
          }
        `}
      </style>
      <Flex direction="column" gap="3" className="c-billing-card__flex-wrapper">
        <Flex justify="between" align="center">
          <Text weight="medium">Billing</Text>
          {subscriptionData.portalUrl && (
            <Link size="2" href={subscriptionData.portalUrl} target="_blank">
              Payment history
            </Link>
          )}
        </Flex>

        <Flex align="start" gap="2">
          <CalendarIcon />
          <Flex direction="column" gap="1">
            <Text weight="medium" size="5">
              <Skeleton loading={subscriptionQuery.isLoading}>
                {subscriptionData.billingPeriodEnd &&
                  format(
                    new Date(subscriptionData.billingPeriodEnd),
                    "MMMM d, yyyy",
                  )}
              </Skeleton>
            </Text>
            <Text className="subtle-text">
              <Skeleton loading={subscriptionQuery.isLoading}>
                {subscriptionData.augmentPlanType === "trial"
                  ? "Trial End Date"
                  : "Next Billing Date"}
              </Skeleton>
            </Text>
          </Flex>
        </Flex>

        {paymentQuery.isError ? (
          <Text className="subtle-text">
            Error loading payment method information.
          </Text>
        ) : (
          <>
            <div className="subtle-text">
              <Skeleton loading={paymentQuery.isLoading}>
                <>
                  {hasPaymentMethod
                    ? `Your card will be charged $${subscriptionData.billingCycleBillingAmount}`
                    : "No payment method on file"}
                  {paymentQuery.data?.failedPayment && (
                    <Callout
                      type="error"
                      size="small"
                      className="c-billing-card__payment-error"
                    >
                      <div>
                        Your last payment of $
                        {paymentQuery.data.failedPayment.amount} on{" "}
                        {format(
                          toDate(paymentQuery.data.failedPayment.date),
                          "P",
                        )}{" "}
                        failed.
                      </div>
                      <div>Please update your payment method.</div>
                    </Callout>
                  )}
                </>
              </Skeleton>
            </div>
          </>
        )}

        <Flex align="end" flexGrow="1">
          <Skeleton loading={paymentQuery.isLoading || paymentQuery.isError}>
            {hasPaymentMethod ? (
              <Flex
                direction="column"
                gap="3"
                wrap="wrap"
                className="c-billing-card__button-container"
              >
                {userIsAdmin && (
                  <Flex gap="2">
                    <Button
                      variant="soft"
                      onClick={handleSetupPayment}
                      className="c-billing-card__button"
                    >
                      Update Payment Method
                    </Button>
                    <Button
                      className="c-billing-card__button"
                      variant="soft"
                      color="red"
                      onClick={handleClearPaymentMethod}
                    >
                      Remove
                    </Button>
                  </Flex>
                )}
              </Flex>
            ) : (
              <Flex
                direction="column"
                gap="3"
                className="c-billing-card__button-container"
              >
                {subscriptionData.augmentPlanType === "trial" ? (
                  userIsAdmin && (
                    <Button
                      color="blue"
                      onClick={() => setIsPlanPickerOpen(true)}
                    >
                      Upgrade Now
                    </Button>
                  )
                ) : (
                  <>
                    <Text className="subtle-text">
                      Configure a payment method to enable automatic billing for
                      your subscription.
                    </Text>
                    <Button
                      color="blue"
                      onClick={handleSetupPayment}
                      disabled={isSettingUpPayment}
                    >
                      <IdCardIcon />
                      {isSettingUpPayment
                        ? "Setting up..."
                        : "Add Payment Method"}
                    </Button>
                  </>
                )}
              </Flex>
            )}
          </Skeleton>
        </Flex>
      </Flex>
    </Card>
  );
}

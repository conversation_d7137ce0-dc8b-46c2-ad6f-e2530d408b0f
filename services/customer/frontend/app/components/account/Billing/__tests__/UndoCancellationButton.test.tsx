import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import { UndoCancellationButton } from "../UndoCancellationButton";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { toast } from "app/components/ui/Toast";
import { unscheduleCancellation } from "app/client-cache";
import { describe, it, expect, vi, beforeEach } from "vitest";

// Mock the dependencies
vi.mock("app/client-cache", () => ({
  unscheduleCancellation: {
    mutationFn: vi.fn(),
  },
}));

vi.mock("app/components/ui/Toast", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("UndoCancellationButton", () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    vi.clearAllMocks();
  });

  it("renders the button correctly", () => {
    render(
      <QueryClientProvider client={queryClient}>
        <UndoCancellationButton />
      </QueryClientProvider>,
    );

    expect(
      screen.getByRole("button", { name: /undo cancellation/i }),
    ).toBeInTheDocument();
  });

  it("shows loading state when processing", async () => {
    // Mock the mutation to be in pending state
    vi.mocked(unscheduleCancellation.mutationFn as any).mockImplementationOnce(
      () => {
        return new Promise((resolve) => {
          // This promise won't resolve during the test
          setTimeout(resolve, 10000);
        });
      },
    );

    render(
      <QueryClientProvider client={queryClient}>
        <UndoCancellationButton />
      </QueryClientProvider>,
    );

    // Click the button to start the mutation
    fireEvent.click(screen.getByRole("button", { name: /undo cancellation/i }));

    // Check that the button shows the loading state
    await waitFor(() => {
      expect(
        screen.getByRole("button", { name: /processing/i }),
      ).toBeInTheDocument();
    });
  });

  it("calls the mutation and shows success toast on successful undo", async () => {
    // Mock successful mutation
    (unscheduleCancellation.mutationFn as jest.Mock).mockResolvedValueOnce({});

    render(
      <QueryClientProvider client={queryClient}>
        <UndoCancellationButton />
      </QueryClientProvider>,
    );

    // Click the button
    fireEvent.click(screen.getByRole("button", { name: /undo cancellation/i }));

    // Verify the mutation was called
    await waitFor(() => {
      expect(unscheduleCancellation.mutationFn).toHaveBeenCalled();
    });

    // Verify success toast was shown
    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith({
        title: "Cancellation undone",
        description: "Your subscription will continue as normal.",
      });
    });
  });

  it("shows error toast on failed undo", async () => {
    // Mock failed mutation
    (unscheduleCancellation.mutationFn as jest.Mock).mockRejectedValueOnce(
      new Error("Failed to unschedule cancellation"),
    );

    render(
      <QueryClientProvider client={queryClient}>
        <UndoCancellationButton />
      </QueryClientProvider>,
    );

    // Click the button
    fireEvent.click(screen.getByRole("button", { name: /undo cancellation/i }));

    // Verify the mutation was called
    await waitFor(() => {
      expect(unscheduleCancellation.mutationFn).toHaveBeenCalled();
    });

    // Verify error toast was shown
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith({
        title: "Failed to undo cancellation",
        description: "Please try again later.",
      });
    });
  });
});

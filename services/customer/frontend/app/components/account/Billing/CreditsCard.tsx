import {
  Button,
  Flex,
  Link,
  Text,
  Card,
  Skeleton,
  Progress,
} from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { creditsQueryOptions } from "app/client-cache/queries/credits";
import { USAGE_UNITS } from "app/data/constants";
import { titleCase } from "app/utils/string";
import { CreditsIcon } from "./CreditsIcon";
import { subscriptionQueryOptions, userQueryOptions } from "app/client-cache";
import { PurchaseCreditsModal } from "./PurchaseCreditsModal";
import { paymentQueryOptions } from "app/client-cache/queries/payment";

export type CreditsCardProps = {
  setIsPlanPickerOpen: (isOpen: boolean) => void;
};

export function CreditsCard({ setIsPlanPickerOpen }: CreditsCardProps) {
  const creditsQuery = useQuery(creditsQueryOptions);
  const userIsAdmin = !!useQuery(userQueryOptions).data?.isAdmin;
  const subscriptionQuery = useQuery(subscriptionQueryOptions);
  const paymentQuery = useQuery(paymentQueryOptions);

  const subscriptionData = subscriptionQuery.data ?? {
    // mock data helps with skeletons
    portalUrl: null,
    creditsRenewingEachBillingCycle: 0,
    addUsageAvailable: false,
  };

  const creditsData = creditsQuery.data ?? {
    // mock data helps with skeletons
    usageUnitsAvailable: 0,
    usageUnitsPending: 0,
    usageUnitsUsedThisBillingCycle: 1,
  };

  const hasPaymentMethod = paymentQuery.data?.hasPaymentMethod ?? false;

  return (
    <Card style={{ flex: 1 }} size="3">
      <style scoped>
        {`
          :scope {
            flex: 1;

            .c-credits-card__flex-wrapper {
              height: 100%;
            }

            .c-credits-card__upgrade-button {
              width: 100%;
            }
          }
        `}
      </style>
      <Flex direction="column" gap="3" className="c-credits-card__flex-wrapper">
        <Flex justify="between" align="center">
          <Text weight="medium">{titleCase(USAGE_UNITS)}</Text>
          {subscriptionData.portalUrl && (
            <Link size="2" href={subscriptionData.portalUrl} target="_blank">
              View usage
            </Link>
          )}
        </Flex>
        {creditsQuery.isError ? (
          <Flex align="start" gap="2">
            <Flex direction="column" gap="1">
              <Text className="subtle-text">
                Error loading {USAGE_UNITS} data
              </Text>
            </Flex>
          </Flex>
        ) : (
          <>
            <Flex align="start" gap="2">
              <CreditsIcon />
              <Flex direction="column" gap="1">
                <Text weight="medium" size="5">
                  <Skeleton loading={creditsQuery.isLoading}>
                    {creditsData.usageUnitsAvailable.toFixed(2)} available
                  </Skeleton>
                  {creditsData.usageUnitsPending > 0 && (
                    <Text className="pending-credits">
                      {creditsData.usageUnitsPending.toFixed(2)} pending
                    </Text>
                  )}
                </Text>
                {subscriptionData.creditsRenewingEachBillingCycle > 0 && (
                  <Text className="subtle-text">
                    {subscriptionData.creditsRenewingEachBillingCycle} renew
                    monthly
                  </Text>
                )}
              </Flex>
            </Flex>

            {/* Progress bar */}
            <div>
              <Skeleton loading={creditsQuery.isLoading} width="100%">
                <Progress
                  value={creditsData.usageUnitsUsedThisBillingCycle}
                  max={
                    creditsData.usageUnitsUsedThisBillingCycle +
                    creditsData.usageUnitsAvailable
                  }
                />
              </Skeleton>
            </div>

            <Text className="subtle-text">
              <Skeleton loading={creditsQuery.isLoading}>
                Used {creditsData.usageUnitsUsedThisBillingCycle} of{" "}
                {creditsData.usageUnitsUsedThisBillingCycle +
                  creditsData.usageUnitsAvailable}{" "}
                this month
              </Skeleton>
            </Text>
          </>
        )}
        <Flex flexGrow="1" align="end">
          {userIsAdmin && (
            <Skeleton
              loading={subscriptionQuery.isLoading || paymentQuery.isLoading}
            >
              {subscriptionData.addUsageAvailable ? (
                hasPaymentMethod ? (
                  <PurchaseCreditsModal />
                ) : (
                  <Text className="subtle-text">
                    Add a payment method to purchase additional {USAGE_UNITS}.
                  </Text>
                )
              ) : (
                <Button
                  variant="soft"
                  size="2"
                  onClick={() => setIsPlanPickerOpen(true)}
                  className="c-credits-card__upgrade-button"
                >
                  Upgrade plan to add {USAGE_UNITS}
                </Button>
              )}
            </Skeleton>
          )}
        </Flex>
      </Flex>
    </Card>
  );
}

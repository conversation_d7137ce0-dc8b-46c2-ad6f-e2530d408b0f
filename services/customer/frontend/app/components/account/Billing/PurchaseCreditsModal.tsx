import { Button } from "@radix-ui/themes";
import { Card } from "app/components/ui/Card/Card";
import Modal from "app/components/ui/Modal";
import { Separator } from "app/components/ui/Separator";
import { typography } from "app/utils/style";
import { useState } from "react";
import { PlusIcon } from "@radix-ui/react-icons";
import { useMutation, useQuery } from "@tanstack/react-query";
import { purchaseCredits } from "app/client-cache/mutations/purchase-credits";
import { toast } from "app/components/ui/Toast";
import { subscriptionQueryOptions } from "app/client-cache";
import { USAGE_UNITS } from "app/data/constants";
import { formatMoney } from "app/utils/string";

const creditOptions = [100, 250, 500, 1000] as const;
type CreditOptions = (typeof creditOptions)[number];

export interface PurchaseCreditsModalProps {
  /** Optional flag to indicate if the modal is open (for storybook only) */
  isOpen?: boolean;
  /** Optional callback when the open state changes (for storybook only) */
  onOpenChange?: (isOpen: boolean) => void;
}

export function PurchaseCreditsModal({
  isOpen,
  onOpenChange,
}: PurchaseCreditsModalProps) {
  const [credits, setCredits] = useState<CreditOptions>(100);
  const purchaseCreditsMutation = useMutation(purchaseCredits);
  const subscriptionQuery = useQuery(subscriptionQueryOptions); // most likely already loaded

  if (subscriptionQuery.isError || subscriptionQuery.isPending) {
    return null; // TODO maybe handle this more gracefully, but this is probably good enough.
  }

  const handlePurchaseCredits = () => {
    purchaseCreditsMutation.mutate(credits, {
      onSuccess: () => {
        toast.success({
          title: "Purchase Pending",
          description: `Payment pending for ${credits} ${USAGE_UNITS}`,
        });
      },
      onError: (error) => {
        toast.error({
          title: "Purchase Failed",
          description: `Failed to purchase ${USAGE_UNITS}: ${error.message || "Please try again later."}`,
        });
      },
    });
  };

  const usageUnitCostDisplay = formatMoney(
    parseFloat(subscriptionQuery.data?.additionalUsageUnitCost ?? "0.00"),
  );

  return (
    <Modal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title={`Purchase additional ${USAGE_UNITS}`}
      description={`Additional ${USAGE_UNITS} expire after 12 months. It may take a few minutes for your new ${USAGE_UNITS} to become available.`}
      maxWidth="450px"
      trigger={(open) => (
        <Button
          onClick={open}
          variant="soft"
          color="green"
          style={{ flexBasis: "100%" }}
        >
          <PlusIcon />
          {purchaseCreditsMutation.isPending
            ? "Purchasing..."
            : `Purchase additional ${USAGE_UNITS}`}
        </Button>
      )}
      footer={(close) => (
        <>
          <Button variant="soft" onClick={close}>
            Cancel
          </Button>
          <Button
            onClick={() => {
              handlePurchaseCredits();
              close();
            }}
            disabled={purchaseCreditsMutation.isPending}
            variant="solid"
            color="green"
          >
            {purchaseCreditsMutation.isPending
              ? "Purchasing..."
              : `Purchase ${USAGE_UNITS}`}
          </Button>
        </>
      )}
    >
      <div>
        <div className="select-quantity-header">
          <div className="title">Select quantity</div>
          {usageUnitCostDisplay} per {USAGE_UNITS.replace(/s$/, "")}
        </div>
        <Card className="credit-options-selector">
          {creditOptions.map((creditAmount) => (
            <Button
              key={creditAmount}
              onClick={() => setCredits(creditAmount)}
              variant={credits === creditAmount ? "solid" : "outline"}
            >
              {creditAmount.toLocaleString()} {USAGE_UNITS}
            </Button>
          ))}
        </Card>
        <div className="summary">
          <div>
            <span>Selected {USAGE_UNITS}</span>
            <span>{credits}</span>
          </div>
          <div>
            <span>Price per {USAGE_UNITS.replace(/s$/, "")}</span>
            <span>{usageUnitCostDisplay}</span>
          </div>
          <Separator />
          <div className="bottom-line">
            <span>Total cost</span>
            <span>
              {formatMoney(
                credits *
                  parseFloat(subscriptionQuery.data?.additionalUsageUnitCost),
              )}
            </span>
          </div>
        </div>
        <style scoped>
          {`
            :scope {
              display: flex;
              flex-direction: column;
              gap: var(--ds-spacing-3);

              .select-quantity-header {
                display: flex;
                flex-direction: column;
                gap: var(--ds-spacing-1);
                ${typography.text2.regular}
                color: var(--ds-color-text-subtle);

                .title {
                  ${typography.text3.bold}
                }
              }

              .credit-options-selector {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: var(--ds-spacing-4);
                box-sizing: border-box;
                padding: var(--ds-spacing-4);
                margin: var(--ds-spacing-2) 0;

                > button {
                  padding: var(--ds-spacing-3);
                  height: fit-content;
                }
              }

              .summary {
                display: flex;
                flex-direction: column;
                gap: var(--ds-spacing-1);
                ${typography.text2.regular}
                color: var(--ds-color-text-subtle);

                > div {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  flex-direction: row;
                }

                .bottom-line {
                  ${typography.text2.bold}
                }
              }
            }
          `}
        </style>
      </div>
    </Modal>
  );
}

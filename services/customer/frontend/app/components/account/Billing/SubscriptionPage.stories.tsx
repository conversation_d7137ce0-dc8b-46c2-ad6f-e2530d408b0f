/**
 * SubscriptionPage Stories
 *
 * This file demonstrates a pattern for merging MSW overrides between meta defaults and individual stories.
 *
 * Pattern:
 * 1. Define base MSW overrides in `baseOverrides` function that will be used across all stories
 * 2. Use `mergeOverrides` helper to combine base overrides with story-specific overrides
 * 3. Individual stories can add their own overrides which will take precedence over base ones
 *    (MSW uses the first matching handler, so story overrides are placed first in the array)
 *
 * Usage in individual stories:
 * ```
 * parameters: {
 *   msw: {
 *     overrides: mergeOverrides((http) => [
 *       // Story-specific overrides here
 *       http.get("/api/endpoint", () => HttpResponse.json(customData)),
 *     ])
 *   }
 * }
 * ```
 */

import type { Meta, StoryObj } from "@storybook/react";
import {
  SubscriptionPage,
  type SubscriptionPageProps,
} from "./SubscriptionPage";
import { addDays, format } from "date-fns";
import { useEffect } from "react";
import { samplePlans } from "./storyData";
import mocks from "app/mocks";
import { delay } from "@augment-internal/ts-utils/timer";
import type { GetUserOrbCreditsInfoResponse } from "app/schemas/credits";
import { HttpResponse } from "msw";
import type { OrbCustomerInfoSchema } from "app/schemas/orb";
import { PlanOptionSchema } from "app/schemas/plan";
import type { HttpHandler } from "msw";
import type { UserApiGETResponseSchema } from "app/schemas/user";

// Base mock data
const baseMockCreditsData: GetUserOrbCreditsInfoResponse = {
  usageUnitsAvailable: 750,
  usageUnitsPending: 0,
  usageUnitsUsedThisBillingCycle: 250,
};

// Base mock subscription data
const baseMockSubscriptionData: OrbCustomerInfoSchema = {
  portalUrl: "https://billing.example.com/portal",
  planId: "orb_developer_plan",
  augmentPlanType: "paid" as const,
  planName: "Developer",
  billingPeriodEnd: format(addDays(new Date(), 30), "yyyy-MM-dd'T'HH:mm:ss'Z'"),
  trialPeriodEnd: null,
  creditsRenewingEachBillingCycle: 1000,
  creditsIncludedThisBillingCycle: 1000,
  billingCycleBillingAmount: "49.00",
  monthlyTotalCost: "49.00",
  pricePerSeat: "49.00",
  maxNumSeats: 10,
  numberOfSeatsThisBillingCycle: 1,
  numberOfSeatsNextBillingCycle: 1,
  subscriptionEndDate: null,
  planIsExpired: false,
  addUsageAvailable: true,
  teamsAllowed: true,
  additionalUsageUnitCost: "1.00",
  scheduledTargetPlanId: null,
};

// Base MSW overrides that will be used in all stories
const baseOverrides = (http: any): HttpHandler[] => [
  http.get("/api/team", async () => {
    await delay(1000);
    return HttpResponse.json(mocks.team.GET.Response[200].none);
  }),
  http.get("/api/subscription", async () => {
    await delay(1000);
    return HttpResponse.json(baseMockSubscriptionData);
  }),
  http.get("/api/plans", async () => {
    await delay(1000);
    const response = Object.assign(
      mocks.plans.GET.Response[200],
      PlanOptionSchema.array().parse(samplePlans),
    );
    return HttpResponse.json(response);
  }),
  http.get("/api/user", async () => {
    await delay(1000);
    return HttpResponse.json({
      ...mocks.user.GET.Response[200],
      isAdmin: true,
      isSelfServeTeamMember: false,
    } as UserApiGETResponseSchema);
  }),
  http.get("/api/payment", async () => {
    await delay(1000);
    return HttpResponse.json({
      hasPaymentMethod: true,
    });
  }),
];

// Helper function to merge base overrides with story-specific overrides
// Story overrides come first so they take precedence over base overrides
const mergeOverrides = (storyOverrides: (http: any) => HttpHandler[]) => {
  return (http: any) => [...storyOverrides(http), ...baseOverrides(http)];
};

// Wrapper component to automatically open the purchase credits modal
function PurchaseCreditsModalWrapper(props: SubscriptionPageProps) {
  useEffect(() => {
    // Find and click the purchase credits button after render
    setTimeout(() => {
      // Look for the button with the PlusIcon or text containing "Purchase additional credits"
      const buttons = Array.from(document.querySelectorAll("button"));
      const purchaseButton = buttons.find(
        (button) =>
          button.textContent?.includes("Purchase additional credits") ||
          button.querySelector(".rt-PlusIcon"),
      );

      if (purchaseButton) {
        (purchaseButton as HTMLButtonElement).click();
      }
    }, 500);
  }, []);

  return <SubscriptionPage {...props} />;
}

// Wrapper component to automatically open the upgrade plan modal (for trial users)
function UpgradePlanModalWrapper(props: SubscriptionPageProps) {
  useEffect(() => {
    // Find and click the upgrade plan button after render
    setTimeout(() => {
      // Look for the button with text containing "Upgrade plan"
      const buttons = Array.from(document.querySelectorAll("button"));
      const upgradeButton = buttons.find((button) =>
        button.textContent?.includes("Upgrade plan"),
      );

      if (upgradeButton) {
        (upgradeButton as HTMLButtonElement).click();
      }
    }, 500);
  }, []);

  return <SubscriptionPage {...props} />;
}

// Wrapper component to automatically open the change plan modal
function ChangePlanModalWrapper(props: SubscriptionPageProps) {
  useEffect(() => {
    // Find and click the change plan button after render
    setTimeout(() => {
      // Look for the button with text containing "Change plan"
      const buttons = Array.from(document.querySelectorAll("button"));
      const changePlanButton = buttons.find((button) =>
        button.textContent?.includes("Change plan"),
      );

      if (changePlanButton) {
        (changePlanButton as HTMLButtonElement).click();
      }
    }, 500);
  }, []);

  return <SubscriptionPage {...props} />;
}

// Wrapper component to automatically open the cancel subscription modal
function CancelSubscriptionModalWrapper(props: SubscriptionPageProps) {
  useEffect(() => {
    // Find and click the cancel subscription button after render
    setTimeout(() => {
      // Look for the button with text containing "Cancel subscription"
      const buttons = Array.from(document.querySelectorAll("button"));
      const cancelButton = buttons.find((button) =>
        button.textContent?.includes("Cancel subscription"),
      );

      if (cancelButton) {
        (cancelButton as HTMLButtonElement).click();
      }
    }, 500);
  }, []);

  return <SubscriptionPage {...props} />;
}

const meta = {
  title: "Pages/Account/SubscriptionPage",
  component: SubscriptionPage,
  parameters: {
    layout: "fullscreen",
    msw: {
      overrides: baseOverrides,
    },
  },
} satisfies Meta<typeof SubscriptionPage>;

export default meta;

type Story = StoryObj<typeof SubscriptionPage>;

const trialData = {
  ...baseMockSubscriptionData,
  trialPeriodEnd: format(addDays(new Date(), 14), "yyyy-MM-dd'T'HH:mm:ss'Z'"),
  creditsRenewingEachBillingCycle: 0,
  creditsIncludedThisBillingCycle: 100,
  billingCycleBillingAmount: "0.00",
  monthlyTotalCost: "0.00",
  pricePerSeat: "0.00",
  numberOfSeatsThisBillingCycle: 1,
  numberOfSeatsNextBillingCycle: 1,
  subscriptionEndDate: format(
    addDays(new Date(), 14),
    "yyyy-MM-dd'T'HH:mm:ss'Z'",
  ),
  planIsExpired: false,
  planName: "Trial Plan",
  planId: "orb_trial_plan",
  augmentPlanType: "trial" as const,
  addUsageAvailable: false,
  teamsAllowed: false,
};

// Mock handlers for all the required props
const mockHandlers = {
  handleSetupPayment: () => console.log("Setup payment clicked"),
  handleClearPaymentMethod: () => console.log("Clear payment method clicked"),
};

// Base args that all stories will use
const baseArgs = {
  isSettingUpPayment: false,
  plans: samplePlans,
  subscriptionData: baseMockSubscriptionData,
  trialEndsInDays: null,
  planFacts: [
    "Context Engine",
    "MCP & Native Tools",
    "Unlimited completions",
    "Unlimited Next Edit",
    "Email and community support",
  ],
  suspensions: [],
  scheduledPlanId: null,
  ...mockHandlers,
};

export const Default: Story = {
  args: {
    ...baseArgs,
  },
};

export const DefaultNoCommunityAvailable: Story = {
  args: {
    ...baseArgs,
  },
  parameters: {
    msw: {
      overrides: mergeOverrides((http) => [
        http.get("/api/plans", async () => {
          await delay(500);
          const response = Object.assign(
            mocks.plans.GET.Response[200],
            PlanOptionSchema.array().parse(
              samplePlans.filter((plan) => plan.id !== "orb_community_plan"),
            ),
          );
          return HttpResponse.json(response);
        }),
      ]),
    },
  },
};

export const TrialPeriod: Story = {
  args: {
    ...baseArgs,
    subscriptionData: trialData,
    trialEndsInDays: "14 days",
  },
  parameters: {
    msw: {
      overrides: mergeOverrides((http) => [
        http.get("/api/subscription", async () => {
          await delay(500);
          return HttpResponse.json(trialData);
        }),
        http.get("/api/payment", async () => {
          await delay(500);
          return HttpResponse.json({
            hasPaymentMethod: false,
          });
        }),
      ]),
    },
  },
};

export const TrialPeriodWithPaymentMethod: Story = {
  args: {
    ...baseArgs,
    subscriptionData: {
      ...trialData,
    },
    trialEndsInDays: "14 days",
  },
  parameters: {
    msw: {
      overrides: mergeOverrides((http) => [
        http.get("/api/subscription", async () => {
          await delay(500);
          return HttpResponse.json({
            ...trialData,
          });
        }),
      ]),
    },
  },
};

export const NoPaymentMethod: Story = {
  args: {
    ...baseArgs,
  },
  parameters: {
    msw: {
      overrides: mergeOverrides((http) => [
        http.get("/api/subscription", async () => {
          await delay(500);
          return HttpResponse.json({
            ...baseMockSubscriptionData,
            hasPaymentMethod: false,
          });
        }),
      ]),
    },
  },
};

export const PendingCredits: Story = {
  args: {
    ...baseArgs,
  },
  parameters: {
    msw: {
      overrides: mergeOverrides((http) => [
        http.get("/api/credits", async () => {
          await delay(500);
          return HttpResponse.json({
            usageUnitsAvailable: 100,
            usageUnitsUsedThisBillingCycle: 50,
            usageUnitsPending: 250,
          });
        }),
      ]),
    },
  },
};

export const LowCredits: Story = {
  args: {
    ...baseArgs,
  },
  parameters: {
    msw: {
      overrides: mergeOverrides((http) => [
        http.get("/api/credits", async () => {
          await delay(500);
          return HttpResponse.json({
            usageUnitsAvailable: 20,
            usageUnitsUsedThisBillingCycle: 80,
            usageUnitsRenewingEachBillingCycle: 100,
            usageUnitsIncludedThisBillingCycle: 150,
            usageUnitsPending: 0,
          });
        }),
      ]),
    },
  },
};

export const CancelledSubscription: Story = {
  args: {
    ...baseArgs,
    subscriptionData: {
      ...baseMockSubscriptionData,
      subscriptionEndDate: format(
        addDays(new Date(), 15),
        "yyyy-MM-dd'T'HH:mm:ss'Z'",
      ),
      planIsExpired: true,
    },
  },
  parameters: {
    msw: {
      overrides: mergeOverrides((http) => [
        http.get("/api/subscription", async () => {
          const data: OrbCustomerInfoSchema = {
            ...baseMockSubscriptionData,
            subscriptionEndDate: format(
              addDays(new Date(), 15),
              "yyyy-MM-dd'T'HH:mm:ss'Z'",
            ),
          };
          await delay(500);
          return HttpResponse.json(data);
        }),
      ]),
    },
  },
};

export const LoadingStates: Story = {
  args: {
    ...baseArgs,
    isSettingUpPayment: true,
  },
  parameters: {
    msw: {
      overrides: mergeOverrides((http) => [
        http.get("/api/credits", async () => {
          await delay(12000);
          return HttpResponse.json(baseMockCreditsData);
        }),
        http.get("/api/payment", async () => {
          await delay(10000);
          return HttpResponse.json(mocks.payment.GET.Response[200]);
        }),
        http.get("/api/subscription", async () => {
          await delay(7000);
          return HttpResponse.json(baseMockSubscriptionData);
        }),
        http.get("/api/user", async () => {
          await delay(5000);
          return HttpResponse.json({
            ...mocks.user.GET.Response[200],
            isAdmin: true,
            isSelfServeTeamMember: false,
          });
        }),
      ]),
    },
  },
};

// Modal stories
export const PurchaseCreditsModal: Story = {
  args: {
    ...baseArgs,
    subscriptionData: {
      ...baseMockSubscriptionData,
      trialPeriodEnd: format(
        addDays(new Date(), 14),
        "yyyy-MM-dd'T'HH:mm:ss'Z'",
      ),
    },
    trialEndsInDays: "14 days",
  },
  render: (args) => <PurchaseCreditsModalWrapper {...args} />,
};

export const UpgradePlanModal: Story = {
  args: {
    ...baseArgs,
  },
  render: (args) => <UpgradePlanModalWrapper {...args} />,
};

export const ChangePlanModal: Story = {
  args: {
    ...baseArgs,
  },

  render: (args) => <ChangePlanModalWrapper {...args} />,
};

export const CancelSubscriptionModal: Story = {
  args: {
    ...baseArgs,
    subscriptionData: {
      ...baseMockSubscriptionData,
      trialPeriodEnd: null,
      subscriptionEndDate: null,
      planIsExpired: false,
    },
  },

  render: (args) => <CancelSubscriptionModalWrapper {...args} />,
};

export const ErrorOnCredits: Story = {
  args: {
    ...baseArgs,
  },
  parameters: {
    msw: {
      overrides: mergeOverrides((http) => [
        http.get("/api/credits", async () => {
          await delay(500);
          return HttpResponse.json(
            {
              error: "Failed to get credits",
            },
            { status: 500 },
          );
        }),
      ]),
    },
  },
};

export const NonAdminTeamMember: Story = {
  args: {
    ...baseArgs,
  },
  parameters: {
    msw: {
      overrides: mergeOverrides((http) => [
        http.get("/api/user", async () => {
          await delay(500);
          return HttpResponse.json({
            ...mocks.user.GET.Response[200],
            isAdmin: false,
            isSelfServeTeamMember: true,
          });
        }),
      ]),
    },
  },
};

export const FailedPayment: Story = {
  args: {
    ...baseArgs,
  },
  parameters: {
    msw: {
      overrides: mergeOverrides((http) => [
        http.get("/api/payment", async () => {
          await delay(500);
          return HttpResponse.json({
            hasPaymentMethod: true,
            failedPayment: {
              amount: "10.00",
              date: "2021-01-01T00:00:00.000Z",
            },
          });
        }),
      ]),
    },
  },
};

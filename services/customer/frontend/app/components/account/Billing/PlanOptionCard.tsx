import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Badge, <PERSON>, Link } from "@radix-ui/themes";
import {
  CheckIcon,
  LightningBoltIcon,
  InfoCircledIcon,
  PersonIcon,
  PlusIcon,
} from "@radix-ui/react-icons";
import type { PlanOptionSchema } from "app/schemas/plan";
import { cn } from "app/utils/style";
import { USAGE_UNITS } from "app/data/constants";
import { getFeatureFlag } from "app/feature-flags/feature-flags.client";

type PlanOptionCardProps = {
  plan: PlanOptionSchema;
  isSelected?: boolean;
  isCurrent: boolean;
  isScheduled?: boolean;
  showButton?: boolean;
  allowCardClick?: boolean;
  onClick: () => void;
  className?: string;
};

// Helper function to determine button text based on plan state
function getButtonText(
  isEnterprisePlan: boolean,
  isCurrent: boolean,
  isScheduled?: boolean,
  isSelected?: boolean,
): string {
  if (isEnterprisePlan) {
    return "Contact Sales";
  } else if (isCurrent) {
    return "Current Plan";
  } else if (isScheduled) {
    return "Scheduled";
  } else if (isSelected === true) {
    return "Selected";
  } else if (isSelected === false) {
    return "Select";
  } else {
    return "Switch Plan";
  }
}

export function PlanOptionCard(props: PlanOptionCardProps) {
  const {
    plan,
    isSelected,
    isCurrent,
    isScheduled = false,
    showButton = true,
    allowCardClick = true,
    onClick,
    className,
  } = props;
  // Get the plan color configuration
  const planConfig = {
    color: plan.colorScheme.radixColor,
  };

  // Check if this is the enterprise plan or current plan
  const isEnterprisePlan = plan.augmentPlanType === "enterprise";
  // Don't allow clicking on current plan, scheduled plan, or enterprise plan
  const shouldAllowCardClick =
    allowCardClick && !isEnterprisePlan && !isCurrent && !isScheduled;
  return (
    <Card
      size="2"
      tabIndex={shouldAllowCardClick ? 0 : -1}
      onClick={shouldAllowCardClick ? onClick : undefined}
      className={cn(
        className,
        "plan-option-card",
        isSelected && "selected",
        isEnterprisePlan && "enterprise",
        isCurrent && "current",
        isScheduled && "scheduled",
        shouldAllowCardClick && "clickable",
      )}
    >
      <style scoped>
        {`
         :scope.plan-option-card {
            position: relative;
            overflow: hidden;
            border: 2px solid var(--gray-5);
            border-radius: 10px;
            background: white;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            cursor: default;
            transition: all 0.2s ease;
            padding: 16px;
            opacity: 1;
            height: fit-content;
            background-color: white;
            position: relative;
          }

          :scope.plan-option-card.selected {
            border: 2px solid ${plan.color};
          }

          :scope.plan-option-card.enterprise {
            opacity: 1;
          }

          :scope.plan-option-card.current,
          :scope.plan-option-card.scheduled,
          :scope.plan-option-card.enterprise {
            background-color: var(--gray-3);
          }

          :scope.plan-option-card.clickable {
            cursor: pointer;
          }

          :scope.plan-option-card.clickable:hover {
            background-color: var(--ds-color-accent-4) !important;
          }

          :scope.plan-option-card.clickable:active {
            background-color: var(--ds-color-accent-5) !important;
          }

          .plan-name {
            background: linear-gradient(90deg, ${plan.color}, color-mix(in srgb, ${plan.color} 70%, #000));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: -0.02em;
          }

          .current-badge {
            display: flex;
            align-items: center;
            gap: 4px;
            border-radius: 4px;
            padding: 0 8px;
          }

          .plan-price {
            color: ${plan.color};
          }

          .info-icon {
            width: 16px;
            height: 16px;
          }

          .team-icon-container {
            position: relative;
            width: 16px;
            height: 16px;
          }

          .team-plus-icon {
            position: absolute;
            width: 10px;
            height: 10px;
            right: -4px;
            bottom: -2px;
            background: white;
            border-radius: 50%;
          }

          .team-text {
            color: var(--teal-9);
          }

          .plan-description {
            flex: 1;
          }

          .plan-button {
            min-width: 120px;
            width: fit-content;
            height: 36px;
            font-weight: bold;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25);
            border-radius: 6px;
            padding: 0 12px;
            background: linear-gradient(135deg, ${plan.colorScheme.gradientStart} 0%, ${plan.colorScheme.gradientEnd} 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 1;
          }

          .plan-button.enterprise {
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25);
            opacity: 1;
            text-decoration: none;
          }

          .plan-button.current {
            opacity: 0.6;
          }

          .button-icon {
            margin-right: 4px;
          }

          .button-text {
            font-size: 14px;
          }

          .ai-training-text {
            font-style: italic;
            opacity: 0.85;
          }
        `}
      </style>
      <Flex direction="column" gap="2">
        {/* Header section with name and price */}
        <Flex justify="between" align="start">
          <Flex direction="row" align="center" gap="2">
            <Text size="5" weight="bold" className="plan-name">
              {plan.name}
            </Text>
            {isCurrent && (
              <Badge
                size="1"
                variant="solid"
                color={planConfig.color as any}
                className="current-badge"
              >
                <CheckIcon />
                Current
              </Badge>
            )}
            {isScheduled && (
              <Badge
                size="1"
                variant="solid"
                color={planConfig.color as any}
                className="current-badge"
              >
                <CheckIcon />
                Scheduled
              </Badge>
            )}
          </Flex>
          <Text size="6" weight="bold" className="plan-price">
            {plan.priceLabel}
          </Text>
        </Flex>

        {/* Features section */}
        <Flex gap="4" wrap="wrap" align="center">
          <Flex align="center" gap="2">
            <Text size="2" weight="medium">
              {plan.agentRequests.toLocaleString()} {USAGE_UNITS}/mo
            </Text>
          </Flex>

          {plan.hasTraining && (
            <Flex align="center" gap="2">
              <InfoCircledIcon color="var(--blue-9)" className="info-icon" />
              <Text size="2" weight="regular" className="ai-training-text">
                AI Training
              </Text>
            </Flex>
          )}

          {plan.hasTeams &&
            (getFeatureFlag("team_management") ||
              plan.augmentPlanType === "enterprise") && (
              <Flex align="center" gap="2">
                <Flex className="team-icon-container">
                  <PersonIcon color="var(--teal-9)" className="info-icon" />
                  <PlusIcon color="var(--teal-9)" className="team-plus-icon" />
                </Flex>
                <Text size="2" weight="medium" className="team-text">
                  Teams
                </Text>
              </Flex>
            )}
        </Flex>

        {/* Description and Select button */}
        <Flex justify="between" align="center" gap="3">
          <Text color="gray" size="2" className="plan-description">
            {plan.description}
          </Text>

          {/* Select button or Selected label */}
          {isEnterprisePlan ? (
            // Always show button for Enterprise plans
            <Button
              size="2"
              variant="solid"
              color={planConfig.color as any}
              className="plan-button enterprise"
              asChild
            >
              <Link href="https://www.augmentcode.com/contact" target="_blank">
                <Text weight="bold" className="button-text">
                  Contact Sales
                </Text>
              </Link>
            </Button>
          ) : showButton ? (
            <Button
              size="2"
              variant="solid"
              color={planConfig.color as any}
              onClick={(e) => {
                e.stopPropagation();
                // Use the same logic as the card's onClick
                if (!isCurrent && !isScheduled) {
                  // Call the onClick handler which will update state and notify parent
                  onClick();
                }
              }}
              // Disable the button if it's the current plan or scheduled plan
              disabled={isCurrent || isScheduled}
              className={cn("plan-button", (isCurrent || isScheduled) ? "current" : "")}
            >
              <>
                <LightningBoltIcon
                  width="16"
                  height="16"
                  className="button-icon"
                />
                <Text weight="bold" className="button-text">
                  {getButtonText(isEnterprisePlan, isCurrent, isScheduled, isSelected)}
                </Text>
              </>
            </Button>
          ) : null}
        </Flex>
      </Flex>
    </Card>
  );
}

import { Flex, Text } from "@radix-ui/themes";
import { CheckIcon } from "@radix-ui/react-icons";
import MaterialIcon from "../chat/material-icon/MaterialIcon";
import type { PlanFeatureSchema } from "app/schemas/plan";

interface PlanFeatureProps {
  feature: PlanFeatureSchema;
}

export default function PlanFeature({ feature }: PlanFeatureProps) {
  const featureText = typeof feature === "string" ? feature : feature.text;

  // Default info icon styling
  const defaultInfoIconStyle = {
    color: "var(--blue-9)",
    weight: 300,
    size: "16px",
    textStyle: {
      fontStyle: "italic" as const,
      opacity: 0.85,
    },
  };

  // Different icon and color based on feature type
  const iconName = typeof feature === "string" ? "check" : feature.icon;
  const iconColor =
    typeof feature === "string"
      ? "#4CAF50" // Green for standard features
      : feature.icon === "info" && defaultInfoIconStyle
        ? defaultInfoIconStyle.color // Use color from plans
        : "var(--ds-color-neutral-9)"; // Neutral color for other object features

  // Make check marks more bold by increasing size and weight
  const iconSize =
    typeof feature === "string"
      ? "20px"
      : feature.icon === "info" && defaultInfoIconStyle
        ? defaultInfoIconStyle.size
        : "16px";

  const iconWeight =
    typeof feature === "string"
      ? 700
      : typeof feature === "object" &&
          feature.icon === "info" &&
          defaultInfoIconStyle
        ? defaultInfoIconStyle.weight
        : 400;

  const className = typeof feature === "string" ? "plan-feature-check" : "";

  const shouldAddSpacing = typeof feature === "object" && feature.spacing;

  return (
    <Flex
      key={featureText}
      gap="2"
      align="center"
      style={shouldAddSpacing ? { marginTop: "8px" } : undefined}
    >
      {typeof feature === "string" ? (
        <CheckIcon style={{ color: "#4CAF50" }} />
      ) : (
        <MaterialIcon
          iconName={iconName}
          size={iconSize}
          color={iconColor}
          weight={iconWeight}
          className={className}
        />
      )}
      <Text
        size="2"
        color="gray"
        style={{
          fontStyle:
            typeof feature === "object" &&
            feature.icon === "info" &&
            defaultInfoIconStyle
              ? defaultInfoIconStyle.textStyle.fontStyle
              : "normal",
          opacity:
            typeof feature === "object" &&
            feature.icon === "info" &&
            defaultInfoIconStyle
              ? defaultInfoIconStyle.textStyle.opacity
              : 1,
        }}
      >
        {featureText}
      </Text>
    </Flex>
  );
}

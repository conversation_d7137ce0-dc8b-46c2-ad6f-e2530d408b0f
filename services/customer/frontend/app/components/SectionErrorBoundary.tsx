import { useRouteError } from "@remix-run/react";
import { ExclamationTriangleIcon } from "@radix-ui/react-icons";
import { Button, Card, Flex, Text } from "@radix-ui/themes";

export default function SectionErrorBoundary({ title }: { title: string }) {
  const error = useRouteError();

  let errorMessage = "An unexpected error occurred while loading this section.";
  if (error instanceof Error) {
    errorMessage = error.message;
  } else if (typeof error === "string") {
    errorMessage = error;
  }

  return (
    <Card mt="4" style={{ width: "100%" }}>
      <Flex direction="column" align="center" gap="3">
        <ExclamationTriangleIcon color="red" width={24} height={24} />
        <Text size="5" weight="bold">
          Error in {title} Section
        </Text>
        <Text size="2" color="gray">
          {errorMessage}
        </Text>
        <Button
          onClick={() => window.location.reload()}
          size="2"
          variant="soft"
        >
          Retry
        </Button>
      </Flex>
    </Card>
  );
}

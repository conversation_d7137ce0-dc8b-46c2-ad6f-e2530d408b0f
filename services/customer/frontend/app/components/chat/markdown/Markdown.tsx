import type { ReactElement } from "react";
import type React from "react";
import { useEffect, useRef } from "react";
import Marked from "marked-react";
import hljs from "highlight.js";
import "highlight.js/styles/vs.css";
import hljs_svelte from "highlightjs-svelte";
import { Text } from "@radix-ui/themes";
import "./markdown.css";
import Codeblock from "./codeblock/Codeblock";
import MermaidBlock from "./mermaidblock/MermaidBlock";

// Register Svelte language
hljs_svelte(hljs);

interface MarkdownProps {
  content: string;
}

const Markdown: React.FC<MarkdownProps> = ({ content }) => {
  useEffect(() => {
    hljs.highlightAll();
  }, [content]);

  const indexRef = useRef(0);

  const renderer = {
    code(code: string, infostring: string) {
      const [language, ...metaparts] = infostring.split(" ");
      const meta = Object.fromEntries(
        metaparts.map((part) => {
          const [key, ...rest] = part.split("=");
          const value = rest.join("=");
          return [key, value];
        }),
      );
      const key = `code-${indexRef.current++}`;
      switch (language) {
        case "mermaid": {
          return <MermaidBlock code={code} meta={meta} key={key} />;
        }
        default: {
          return (
            <Codeblock code={code} language={language} meta={meta} key={key} />
          );
        }
      }
    },
    codespan(code: string) {
      return (
        <span key={`codespan-${indexRef.current++}`}>
          <code className="markdown-codespan">
            <span className="markdown-codespan-linkable">{code}</span>
          </code>
        </span>
      );
    },
    paragraph(text: string) {
      return (
        <Text size="2">
          <p
            key={`paragraph-${indexRef.current++}`}
            className="markdown-paragraph"
          >
            {text}
          </p>
        </Text>
      );
    },
    list(body: ReactElement[], ordered: boolean, start: number | undefined) {
      const Tag = ordered ? "ol" : "ul";
      return (
        <Tag
          key={`list-${indexRef.current++}`}
          className="markdown-list"
          start={ordered ? start : undefined}
        >
          {body}
        </Tag>
      );
    },
    listItem(text: string[]) {
      return (
        <Text size="2">
          <li
            key={`listitem-${indexRef.current++}`}
            className="markdown-list-item"
          >
            {text}
          </li>
        </Text>
      );
    },
  };

  indexRef.current = 0;

  return (
    <div className="markdown-content">
      <Marked renderer={renderer}>{content}</Marked>
    </div>
  );
};

export default Markdown;

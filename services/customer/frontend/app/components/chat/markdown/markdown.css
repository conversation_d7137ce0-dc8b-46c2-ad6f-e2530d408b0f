.markdown-content {
  width: 100%;
}

.markdown-content pre {
  padding: 0.2em;
  border-radius: 5px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  padding-bottom: 4px;
}

.markdown-content code {
  font-family: "Courier New", Courier, monospace;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.hljs {
  white-space: pre-wrap;
  word-wrap: break-word;
}

:root {
  --markdown-link-color: #002bb7;
}

@media (prefers-color-scheme: dark) {
  :root {
    --markdown-link-color: #9eb1ff;
  }
}

.markdown-content .file-path {
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
  color: var(--markdown-link-color);
}

.markdown-codespan-linkable {
  cursor: pointer;
  text-decoration: none;
  user-select: all;
  transition: text-decoration 0.2s ease;
}

.markdown-codespan-linkable:hover {
  text-decoration: underline;
  text-decoration-color: var(--markdown-link-color);
}

.markdown-codespan {
  border-radius: 2px;
  padding: 0.15em 0.3em;
  color: #0a84ff;
  /* Fixes line wrapping */
  word-break: break-all;
  display: inline;
}

.augment-markdown-paragraph {
  white-space: pre-line;
  /* Overwrite default `<p/>` style to be more compact */
  margin-block-start: var(--p-2);
  margin-block-end: var(--p-2);
}

.markdown-content > * + * {
  margin-top: var(--p-2);
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 2em;
  margin: var(--p-2) 0;
}

/* Unordered list styles for different levels */
.markdown-content ul {
  list-style-type: disc;
}

.markdown-content ul ul {
  list-style-type: circle;
}

.markdown-content ul ul ul {
  list-style-type: square;
}

/* Ordered list styles for different levels */
.markdown-content ol {
  list-style-type: decimal;
}

.markdown-content ol ol {
  list-style-type: lower-alpha;
}

.markdown-content ol ol ol {
  list-style-type: lower-roman;
}

/* List item spacing */
.markdown-content li {
  margin: 0.25em 0;
}

/* Nested list indentation */
.markdown-content li > ul,
.markdown-content li > ol {
  margin: 0.25em 0;
}

import hljs from "highlight.js";
import type React from "react";
import { useEffect, useRef } from "react";
import type { Property } from "csstype";
import { useTheme } from "next-themes";
import MaterialIcon from "../../material-icon/MaterialIcon";
import "./codeblock.css";

export interface CodeblockButton {
  text: string;
  title: string;
  onClick: () => void;
  icon: string;
}

export interface CodeblockProps {
  code: string;
  language: string;
  meta: Record<string, string>;
  buttons?: CodeblockButton[];
  /** Specifiy the height of the codeblock in pixels. */
  height?: Property.Height<string | number>;
  /**
   * The content to render inside the codeblock wrapper. If no children are
   * provided, the code will be rendered with hljs highlighting.
   */
  children?: React.ReactNode;
}

const Codeblock: React.FC<CodeblockProps> = ({
  code,
  language,
  meta,
  buttons,
  height,
  children,
}) => {
  const { resolvedTheme } = useTheme();
  const codeRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const updateTheme = async () => {
      if (!children && codeRef.current) {
        hljs.highlightElement(codeRef.current);
      }
    };
    updateTheme();
  }, [resolvedTheme, code, language, children]);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const validLanguage = hljs.getLanguage(language) ? language : "plaintext";
  const codeElement = (
    <code ref={codeRef} className={`hljs language-${validLanguage}`}>
      {code}
    </code>
  );

  const copyButton = {
    text: "Copy",
    title: "Copy to clipboard",
    onClick: () => copyToClipboard(code),
    icon: "file_copy",
  };
  const allButtons = [...(buttons ?? []), copyButton];

  return (
    <div className="code-block-wrapper" data-theme={resolvedTheme}>
      <pre>
        {meta.path ? (
          <div className="code-block-path">
            <span className="code-block-path-text">{meta.path}</span>
            {allButtons.map((button) => (
              <button
                className="code-block-copy-button"
                onClick={button.onClick}
                title={button.title}
                key={button.text}
              >
                <MaterialIcon iconName={button.icon} /> {button.text}
              </button>
            ))}
          </div>
        ) : null}
        <div
          className="code-block-content"
          style={{ height: height ?? "auto" }}
        >
          {children ?? codeElement}
        </div>
      </pre>
    </div>
  );
};

export default Codeblock;

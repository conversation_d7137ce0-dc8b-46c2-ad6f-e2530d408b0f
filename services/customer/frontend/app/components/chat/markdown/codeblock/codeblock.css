@import "../variables.css";

.code-block-wrapper {
  margin-bottom: 1em;
  position: relative;
  border-radius: 5px;
  overflow: hidden;
  border: 1px solid var(--code-block-border-color);
}

.code-block-path {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: monospace;
  font-size: 0.9em;
  padding: 0.5em;
  word-break: break-all;
  white-space: pre-wrap;
  border-bottom: 1px solid var(--code-block-border-color);
  color: inherit;
}

.code-block-wrapper > pre {
  padding: 0;
}

.code-block-wrapper[data-theme="dark"] {
  border: 1px solid var(--code-block-dark-border-color);
}

.code-block-wrapper[data-theme="dark"] .code-block-path {
  background-color: var(--dark-background-color);
  border-bottom-color: var(--code-block-dark-border-color);
}

.code-block-path-text {
  flex-grow: 1;
  max-width: 80%;
}
.code-block-copy-button {
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 0.2em 0.5em;
  font-size: 0.9em;
  color: inherit;
  transition: opacity 0.2s ease;
}

.code-block-copy-button:hover {
  opacity: 0.7;
}

.code-block-copy-button .material-icons {
  font-size: 1em;
  vertical-align: middle;
}

.code-block-wrapper pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.code-block-content {
  overflow: hidden;
}

.code-block-content > code {
  height: 100%;
}

@import "../variables.css";

.mermaid-content {
  width: 100%;
  height: var(--mermaid-diagram-height);
  display: flex;
  flex-direction: row;
  gap: var(--mermaid-content-gap);
}

.mermaid-hide {
  opacity: 0;
  visibility: hidden;
}

.mermaid-content svg {
  height: var(--mermaid-diagram-height);
}

.mermaid-diagram {
  flex: 1;
}

.code-block-wrapper[data-theme="dark"] .mermaid-diagram {
  background-color: var(--dark-background-color);
}

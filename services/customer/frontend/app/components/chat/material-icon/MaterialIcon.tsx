import React from "react";
import "./material-icon.css";

interface MaterialIconProps {
  iconName: string;
  className?: string;
  color?: string;
  title?: string;
  size?: string | number;
  weight?: number;
  fill?: 0 | 1;
}

const MaterialIcon: React.FC<MaterialIconProps> = ({
  iconName,
  className = "",
  color = "var(--augment-icon-clor)",
  title,
  size = "1rem",
  weight = 400,
}) => {
  const style: React.CSSProperties = {
    fontSize: size,
    color,
    verticalAlign: "middle",
    display: "inline-flex",
    fontWeight: weight,
  };

  return (
    <span
      className={`material-symbols-outlined ${className}`}
      style={style}
      title={title}
      aria-label={title || iconName}
      role="img"
    >
      {iconName}
    </span>
  );
};

export default React.memo(MaterialIcon);

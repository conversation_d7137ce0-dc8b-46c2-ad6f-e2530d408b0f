import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import {
  QueryClient,
  QueryClientProvider,
  useQuery,
} from "@tanstack/react-query";
import { teamQueryOptions } from "../../client-cache/queries/team";
import { isTeamActive } from "../../schemas/team";

function TeamInfo() {
  const { data: teamStatus, isLoading, error } = useQuery(teamQueryOptions);
  if (isLoading) return <div>Loading team information...</div>;
  if (error) return <div>Error loading team: {error.message}</div>;
  if (!teamStatus) return <div>No team information available</div>;
  if (!isTeamActive(teamStatus)) return <div>Team is not active</div>;
  return <pre>{JSON.stringify(teamStatus.team, null, 2)}</pre>;
}

const meta = {
  title: "Mocks/TeamInfo",
  component: TeamInfo,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof TeamInfo>;

export default meta;
type Story = StoryObj<typeof meta>;

// Create a custom query client for each story
function createTestQueryClient() {
  const client = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        refetchOnWindowFocus: false,
        staleTime: Infinity,
      },
    },
  });

  return client;
}

export const Default: Story = {
  decorators: [
    (Story: () => JSX.Element) => {
      const testClient = createTestQueryClient();
      return (
        <QueryClientProvider client={testClient}>
          <div
            style={{
              maxWidth: "500px",
              padding: "20px",
              border: "1px solid #eee",
            }}
          >
            <Story />
          </div>
        </QueryClientProvider>
      );
    },
  ],
};

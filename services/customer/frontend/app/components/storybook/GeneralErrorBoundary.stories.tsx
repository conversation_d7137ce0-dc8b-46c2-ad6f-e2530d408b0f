import type { <PERSON>Obj, Meta } from "@storybook/react";
import { GeneralErrorBoundary } from "../GeneralErrorBoundary";
import { createRemixStub as createRemixStub } from "@remix-run/testing";
import { Form } from "@remix-run/react";

export default {
  title: "ErrorBoundary",
  component: () => <div>Sample Child Component</div>,
  parameters: {
    hasLocalStub: true,
  },
} satisfies Meta<typeof GeneralErrorBoundary>;

export const LoaderError = {
  decorators: [
    (Story) => {
      const RemixStub = createRemixStub([
        {
          path: "/",
          Component: Story,
          ErrorBoundary: GeneralErrorBoundary,
          loader: () => {
            // throw an error with a status code
            throw new Response("Error Message", {
              status: 500,
            });
          },
        },
      ]);

      return <RemixStub />;
    },
  ],
} satisfies StoryObj<typeof GeneralErrorBoundary>;

export const ActionError = {
  decorators: [
    (Story) => {
      // This is how you make remix-specific routes, loaders and actions etc.
      // This works for all remix utilities e.g. useLoaderData and even fetchers!
      // To see full usage check out the @remix-run/testing tests - the docs are not updated yet for this package.
      // https://github.com/remix-run/remix/blob/main/packages/remix-testing/__tests__/stub-test.tsx
      const RemixStub = createRemixStub([
        {
          path: "/",
          Component: Story,
          ErrorBoundary: GeneralErrorBoundary,
          action: () => {
            throw new Response("Unauthorised", {
              status: 401,
            });
          },
        },
      ]);

      return <RemixStub />;
    },
  ],
  render: () => (
    <Form action="/" method="POST">
      <button type="submit">Submit</button>
    </Form>
  ),
} satisfies StoryObj<typeof GeneralErrorBoundary>;

import { CaretDownIcon } from "@radix-ui/react-icons";
import { cn, typography } from "app/utils/style";
import { type ReactNode, useLayoutEffect, useRef, useMemo } from "react";
import { Dropdown, type DropdownProps } from "../Dropdown";
import { Enabled } from "../Enabled";

type SelectProps<T extends string> = {
  icon?: ReactNode;
} & Omit<DropdownProps<T>, "children">;

function useScaleByChars(chars: number) {
  const ref = useRef<HTMLDivElement>(null);
  useLayoutEffect(() => {
    if (ref.current) {
      ref.current.style.minWidth = `${chars}ch`;
    }
  }, [chars]);
  return ref;
}

export function Select<T extends string>(props: SelectProps<T>) {
  const { icon, value, onChange, items, className } = props;
  const longestValue = useMemo(
    () => Math.max(...items.map((item) => item.value.length)),
    [items],
  );
  const ref = useScaleByChars(longestValue);
  return (
    <div className={cn("select", className)} ref={ref}>
      <Dropdown
        value={value}
        onChange={onChange}
        items={items}
        className="select-dropdown"
      >
        <div className="select-trigger">
          <Enabled enabled={Boolean(icon)}>
            <span className="select-trigger-icon">{icon as ReactNode}</span>
          </Enabled>
          <span className="select-title">{value}</span>
          <CaretDownIcon className="select-icon" />
        </div>
      </Dropdown>
      <style scoped>{`
        :scope.select {
          position: relative;
          border-radius: var(--ds-radius-3);
          border: 1px solid var(--ds-color-neutral-6);
          display: inline-flex;
        }
        .select-dropdown {
          width: 100%;
        }

        .dropdown-trigger {
          margin: 0;
          padding: var(--ds-spacing-2);
          width: 100%;
          box-sizing: border-box;
          justify-content: start;
        }

        .select-trigger {
          display: flex;
          align-items: center;
          gap: var(--ds-spacing-1);
          cursor: pointer;
          ${typography.text2.regular}

          .select-title {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .select-trigger-icon {
          display: flex;
          align-items: center;
        }
        .select-icon {
          color: var(--ds-color-neutral-8);
        }
        .select-content {
          background-color: var(--ds-panel-solid);
          border-radius: var(--ds-radius-2);
          border: 1px solid var(--ds-color-neutral-6);
          box-shadow: var(--ds-shadow-2);
          min-width: 180px;
        }
      `}</style>
    </div>
  );
}

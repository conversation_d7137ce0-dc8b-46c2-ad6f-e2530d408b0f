import type { Meta } from "@storybook/react";
import { Select } from "./Select";
import { useState } from "react";
import { GearIcon } from "@radix-ui/react-icons";

const meta = {
  title: "UI/Select",
  component: Select,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    icon: {
      description: "Icon displayed before the current value",
    },
    className: {
      description: "Additional CSS class names",
    },
  },
} satisfies Meta<typeof Select>;

export default meta;

// Controlled select example
export function Default() {
  const [value, setValue] = useState("option1");

  return (
    <div style={{ width: "250px" }}>
      <Select
        value={value}
        onChange={setValue}
        items={[
          { value: "option1", label: "Option 1" },
          { value: "option2", label: "Option 2" },
          { value: "option3", label: "Option 3" },
        ]}
      />
    </div>
  );
}

export function WithIcon() {
  const [value, setValue] = useState("settings");

  return (
    <div style={{ width: "250px" }}>
      <Select
        icon={<GearIcon />}
        value={value}
        onChange={setValue}
        items={[
          { value: "settings", label: "Settings" },
          { value: "profile", label: "Profile" },
          { value: "notifications", label: "Notifications" },
        ]}
      />
    </div>
  );
}

export function WithDivider() {
  const [value, setValue] = useState("option1");

  return (
    <div style={{ width: "250px" }}>
      <Select
        value={value}
        onChange={setValue}
        items={[
          { value: "option1", label: "Option 1" },
          { value: "option2", label: "Option 2" },
          { type: "divider", value: "divider1", label: "" },
          { value: "option3", label: "Option 3" },
          { value: "option4", label: "Option 4" },
        ]}
      />
    </div>
  );
}

export function WithDisabledItems() {
  const [value, setValue] = useState("option1");

  return (
    <div style={{ width: "250px" }}>
      <Select
        value={value}
        onChange={setValue}
        items={[
          { value: "option1", label: "Option 1" },
          { value: "option2", label: "Option 2", disabled: true },
          { value: "option3", label: "Option 3" },
          { value: "option4", label: "Option 4", disabled: true },
        ]}
      />
    </div>
  );
}

import { type ReactNode, type ReactElement, isValidElement } from "react";

/**
 * A component that conditionally renders its children based on the enabled prop.
 *
 * @prop enabled - Whether the children should be rendered
 * @prop asChild - Renders grandchildren instead of children when enabled is false.
 * @prop children - The children to conditionally render.
 */
export function Enabled(props: {
  enabled: boolean;
  asChild: true;
  children: ReactElement;
}): ReactNode | null;

export function Enabled(props: {
  enabled: boolean;
  asChild?: false;
  children: ReactNode;
}): ReactNode | null;

export function Enabled({
  enabled,
  asChild,
  children,
}: {
  enabled: boolean;
  children: ReactNode;
  asChild?: boolean;
}) {
  if (!enabled) {
    if (asChild) {
      if (!isValidElement(children)) {
        console.error(
          "Enabled: asChild requires a single React element as children",
        );
        return null;
      }
      return children.props.children ?? null;
    }
    return null;
  }

  return <>{children}</>;
}

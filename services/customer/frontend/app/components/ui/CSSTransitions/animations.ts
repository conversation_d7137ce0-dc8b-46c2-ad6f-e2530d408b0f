import { dedent as d } from "ts-dedent";
import { DEFAULT_BASE_CLASS } from "./CSSTransition";
import { makeAnimations } from "./utils";

export function fadeAnimation(
  baseClassName: string = DEFAULT_BASE_CLASS,
  from = 0,
  to = 1,
) {
  return d`
    .${baseClassName}-enter {
      opacity: ${from};
    }
    
    .${baseClassName}-enter-active {
      opacity: ${to};
    }
    
    .${baseClassName}-exit {
      opacity: ${to};
    }
    
    .${baseClassName}-exit-active {
      opacity: ${from};
    }
  `;
}

export const [
  scaleAnimation,
  translateYAnimation,
  // Add more animations here
] = makeAnimations([
  (value: number) => `scale(${value})`,
  (value: string | number) => `translateY(${value})`,
  // Add more animation templates here
] as const);

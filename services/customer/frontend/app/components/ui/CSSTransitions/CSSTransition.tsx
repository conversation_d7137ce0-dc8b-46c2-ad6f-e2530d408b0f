import { isNumber } from "app/utils/guards";
import type { OmitAny } from "@augment-internal/ts-utils/type";
import type { ComponentProps } from "react";
import { CSSTransition as CSSTransitionRTG } from "react-transition-group";
import { makeTransformVariables } from "./utils";

export const DEFAULT_BASE_CLASS = "transition";

type CSSTransitionRTGProps = OmitAny<ComponentProps<typeof CSSTransitionRTG>>;

type TimeoutsObj = Required<
  Exclude<CSSTransitionRTGProps["timeout"], number | undefined>
>;

export type ClassNamesObj = Required<
  Exclude<CSSTransitionRTGProps["classNames"], string | undefined>
>;

type NoSpaces<S extends string> = S extends `${infer _Before} ${infer _After}`
  ? never
  : S;

export type CSSTransitionProps<BaseClassName extends string = string> =
  CSSTransitionRTGProps & {
    baseClassName?: NoSpaces<BaseClassName>;
  };

function getTimeouts(timeout: CSSTransitionProps["timeout"]): TimeoutsObj {
  if (isNumber(timeout)) {
    return {
      enter: timeout,
      exit: timeout,
      appear: timeout,
    };
  }
  return {
    enter: timeout?.enter ?? 0,
    exit: timeout?.exit ?? 0,
    appear: timeout?.appear ?? timeout?.enter ?? 0,
  };
}

function getClassNames(
  baseClassName: CSSTransitionProps["baseClassName"],
): ClassNamesObj {
  return {
    appear: `${baseClassName}-appear`,
    appearActive: `${baseClassName}-appear-active`,
    appearDone: `${baseClassName}-appear-done`,
    enter: `${baseClassName}-enter`,
    enterActive: `${baseClassName}-enter-active`,
    enterDone: `${baseClassName}-enter-done`,
    exit: `${baseClassName}-exit`,
    exitActive: `${baseClassName}-exit-active`,
    exitDone: `${baseClassName}-exit-done`,
  };
}

/**
 * A wrapper around react-transition-group's CSSTransition that provides
 * scoped styling and CSS variables for customizing transitions.
 */
export function CSSTransition<BaseClassName extends string>(
  props: CSSTransitionProps<BaseClassName>,
) {
  const {
    children,
    baseClassName = DEFAULT_BASE_CLASS,
    timeout,
    ...rest
  } = props;

  const timeouts = getTimeouts(timeout);
  const classNamesObj = getClassNames(baseClassName);

  return (
    <>
      <CSSTransitionRTG
        {...rest}
        timeout={timeouts}
        classNames={classNamesObj}
        unmountOnExit={props.unmountOnExit ?? true}
      >
        {children}
      </CSSTransitionRTG>
      <style scoped>{`
        :scope {
          --transition-property-enter: "all";
          --transition-property-exit: "all";
          --transition-property-appear: "all";
          --transition-timing-function-enter: "ease";
          --transition-timing-function-exit: "ease";
          --transition-timing-function-appear: "ease";
          --transition-enter-duration: ${timeouts.enter}ms;
          --transition-exit-duration: ${timeouts.exit}ms;
          --transition-appear-duration: ${timeouts.appear}ms;

          ${makeTransformVariables(classNamesObj)}

          .${classNamesObj.enterActive} {
            transition-property: var(--transition-property-enter);
            transition-timing-function: var(--transition-timing-function-enter);
            transition-duration: var(--transition-enter-duration);
          }

          .${classNamesObj.exitActive} {
            transition-property: var(--transition-property-exit);
            transition-timing-function: var(--transition-timing-function-exit);
            transition-duration: var(--transition-exit-duration);
          }

          .${classNamesObj.appearActive} {
            transition-property: var(--transition-property-appear);
            transition-timing-function: var(
              --transition-timing-function-appear
            );
            transition-duration: var(--transition-appear-duration);
          }
        }
      `}</style>
    </>
  );
}

import { dedent as d } from "ts-dedent";
import { kababToCamel } from "app/utils/string";
import type { ClassNamesObj } from "./CSSTransition";

const supportedTransformStates = [
  "enter", // from
  "enter-active", // to
  "exit", // to
  "exit-active", // from
] as const;

let transformCount = 0;

type Animations<T extends readonly ((value: string) => string)[]> = {
  [k in keyof T]: (
    from: Parameters<T[k]>[0],
    to: Parameters<T[k]>[0],
  ) => string;
};

export function makeAnimations<
  const T extends readonly ((value: any) => string)[],
>(templates: T) {
  transformCount = templates.length;
  return templates.map((template, index) => {
    const id = index + 1;
    return (from: unknown, to: unknown) => {
      const values: unknown[] = [from, to, to, from];
      return supportedTransformStates
        .map((action, i) => [action, template(values[i])])
        .map(([action, value]) => `--transform-${action}-${id}: ${value};`)
        .join("\n");
    };
  }) as Animations<T>;
}

export function makeTransformVariables(baseClassName: ClassNamesObj) {
  let result = d`
    /* CSS Variables for transforms */
    --transform-noop: matrix(1, 0, 0, 1, 0, 0);\n\n`;
  const actionEntries = supportedTransformStates.map((action) => [
    action,
    baseClassName[kababToCamel(action)],
  ]);
  if (transformCount !== 0) {
    for (const [action, className] of actionEntries) {
      result += `--transform-${action}: `;
      for (let i = 1; i <= transformCount; i++)
        result += `\n  var(--transform-${action}-${i}, var(--transform-noop))`;
      result += ";\n\n";
      result += `.${className} {
        transform: var(--transform-${action});
      }\n\n`;
    }
  }
  return result;
}

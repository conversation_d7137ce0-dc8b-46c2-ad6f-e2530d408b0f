import type { Meta } from "@storybook/react";
import { CSSTransition } from "../CSSTransitions/CSSTransition";
import { But<PERSON> } from "@radix-ui/themes";
import { useState } from "react";
import {
  fadeAnimation,
  scaleAnimation,
  translateYAnimation,
} from "./animations";

const meta = {
  title: "UI/CSSTransitions",
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta;

export default meta;

export function BasicTransition() {
  const [show, setShow] = useState(false);

  return (
    <div className="container">
      <Button onClick={() => setShow(!show)}>
        {show ? "Hide Element" : "Show Element"}
      </Button>

      <CSSTransition in={show} timeout={700} baseClassName="box">
        <div className="box">
          <h3>Animated Content</h3>
          <p>This content animates on mount and unmount</p>
        </div>
      </CSSTransition>

      <style scoped>{`
          :scope.container {
            display: flex;
            flex-direction: column;
            align-items: center;
            
            .box {
              background-color: var(--accent-9);
              color: white;
              padding: 20px;
              border-radius: 8px;
              width: 300px;
            }
            
            ${translateYAnimation("40px", "0px")}
            ${fadeAnimation("box")}
            ${scaleAnimation(0.7, 1)}
          }
        `}</style>
    </div>
  );
}

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import * as Icons from "./index";
import { TextField } from "@radix-ui/themes";
import { useState, useEffect, useRef } from "react";
import { alphabeticalSort } from "app/utils/string";
import { Separator } from "../Separator";
import { cn, typography } from "app/utils/style";
import { toast } from "../Toast";

const meta = {
  title: "ui/Icons",
  component: IconsShowcase,
  parameters: {
    layout: "fullscreen",
  },
} satisfies Meta<typeof IconsShowcase>;

export default meta;
type Story = StoryObj<typeof meta>;

export const AllIcons: Story = {};

function IconsShowcase() {
  const [search, setSearch] = useState("");
  const [copiedIcon, setCopiedIcon] = useState<string | null>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Focus search input when component mounts
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // Add clear search function
  const clearSearch = () => {
    setSearch("");
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  // Get all icons from RadixIcons and sort alphabetically
  const icons = Object.entries(Icons)
    .filter(([name]) => name.endsWith("Icon"))
    .sort(([nameA], [nameB]) => alphabeticalSort(nameA, nameB));

  // Copy icon name to clipboard and show toast
  const copyToClipboard = (iconName: string) => {
    navigator.clipboard.writeText(iconName);
    setCopiedIcon(iconName);
    toast({
      title: "Copied!",
      description: `${iconName} copied to clipboard`,
      duration: 2000,
    });
    setTimeout(() => setCopiedIcon(null), 2000);
  };

  // Filter icons based on search term
  const filteredIcons = search
    ? icons.filter(([name]) =>
        name.toLowerCase().includes(search.toLowerCase()),
      )
    : icons;

  return (
    <div className="icons-showcase">
      <div className="search-container">
        {/* Search bar with clear button */}
        <TextField.Root
          placeholder="Search icons..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="search-field"
          ref={searchInputRef}
        >
          <TextField.Slot side="left">
            <Icons.MagnifyingGlassIcon height="16" width="16" />
          </TextField.Slot>
          {search && (
            <TextField.Slot side="right">
              <button
                onClick={clearSearch}
                className="clear-button"
                title="Clear search"
              >
                <Icons.Cross2Icon height="16" width="16" />
              </button>
            </TextField.Slot>
          )}
        </TextField.Root>

        <p className="count-text">
          {search
            ? `Showing ${filteredIcons.length} of ${icons.length} icons`
            : `Showing ${icons.length} icons`}
        </p>
      </div>

      <Separator size="4" className="separator" />

      <div className="icons-grid">
        {filteredIcons.map(([name, Icon]) => {
          // Ensure Icon is a valid component
          const IconComponent = Icon as React.ComponentType<
            React.SVGProps<SVGSVGElement>
          >;

          return (
            <div key={name} className="icon-wrapper">
              <button
                onClick={() => copyToClipboard(name)}
                className={cn("icon-button", copiedIcon === name && "copied")}
                title={`Click to copy: ${name}`}
              >
                <IconComponent className="icon" />
                <span className="icon-name">{name.replace("Icon", "")}</span>
              </button>
            </div>
          );
        })}
      </div>

      <style scoped>{`
        :scope.icons-showcase {
          padding: var(--ds-spacing-9);
        }

        .heading {
          ${typography.text2.bold}
          margin-bottom: var(--ds-spacing-4);
        }

        .search-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: var(--ds-spacing-3);
          margin-bottom: var(--ds-spacing-4);
        }

        .search-field {
          width: 300px;
          height: fit-content;
          border-radius: var(--ds-radius-4);
          padding: var(--ds-spacing-3) var(--ds-spacing-3);
          cursor: text;
          ${typography.text3.regular}
        }

        .count-text {
          ${typography.text1.regular}
          color: var(--ds-color-text-subtle);
        }

        .separator {
          margin-bottom: var(--ds-spacing-4);
        }

        .icons-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
          gap: var(--ds-spacing-4);
        }

        .icon-button {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 100%;
          aspect-ratio: 1;
          padding: var(--ds-spacing-2);
          border: 1px solid var(--ds-color-border-subtle);
          border-radius: var(--ds-radius-3);
          background: var(--ds-color-surface-1);
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .icon-button:hover {
          background-color: var(--ds-color-surface-3);
        }

        .icon-button:hover .icon {
          color: var(--ds-color-accent-9);
        }

        .icon-button.copied {
          background-color: var(--ds-color-success-3);
          border-color: var(--ds-color-success-6);
        }

        .icon {
          height: 24px;
          width: 24px;
          margin-bottom: var(--ds-spacing-2);
          color: var(--ds-color-neutral-11);
        }

        .icon-name {
          ${typography.text1.regular}
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 100%;
        }

        .clear-button {
          background: none;
          border: none;
          cursor: pointer;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--ds-color-text-subtle);
        }

        .clear-button:hover {
          color: var(--ds-color-primary);
        }
      `}</style>
    </div>
  );
}

import { useState, useEffect } from "react";
import { Box, Container, Flex, Heading, Text } from "@radix-ui/themes";
import { Progress } from "../Progress";
import { cn } from "app/utils/style";

export type ProgressPageVariant = "default" | "minimal";
export type ProgressPageSize = "sm" | "md" | "lg";

export interface ProgressPageProps {
  /**
   * The initial progress value (0-100)
   * @default 0
   */
  initialProgress?: number;

  /**
   * The maximum progress value to animate to (0-100)
   * @default 90
   */
  maxProgress?: number;

  /**
   * The interval in milliseconds between progress updates
   * @default 1000
   */
  updateInterval?: number;

  /**
   * The amount to increment progress by each interval
   * @default 30
   */
  progressIncrement?: number;

  /**
   * The title to display
   * @default "Processing Your Request"
   */
  title?: string;

  /**
   * The description to display
   * @default "Your request is being processed. This may take a few moments."
   */
  description?: string;

  /**
   * The message to display below the progress bar
   * @default "Please wait while we complete this operation."
   */
  message?: string;

  /**
   * The color of the progress bar
   * @default "ds-color-accent"
   */
  progressColor?: string;

  /**
   * The height of the progress bar
   * @default "12px"
   */
  progressHeight?: string;

  /**
   * Optional icon to display above the title
   */
  icon?: React.ReactNode;

  /**
   * Visual variant of the progress page component
   * @default "default"
   */
  variant?: ProgressPageVariant;

  /**
   * Size of the progress page component
   * @default "md"
   */
  size?: ProgressPageSize;

  /**
   * Whether to show a pulsing animation on the progress bar
   * @default true
   */
  pulseAnimation?: boolean;

  /**
   * Maximum width of the container
   * @default "800px"
   */
  maxWidth?: string;

  /**
   * Custom CSS class name
   */
  className?: string;
}

/**
 * A versatile progress page component that displays a progress bar with customizable
 * title, description, and styling options. Can be used for any operation that requires
 * showing progress to the user in a full-page context.
 */
export function ProgressPage(props: ProgressPageProps) {
  const {
    initialProgress = 0,
    maxProgress = 90,
    updateInterval = 400,
    progressIncrement = 30,
    title = "Processing Your Request",
    description = "Your request is being processed. This may take a few moments.",
    message = "Please wait while we complete this operation.",
    progressColor = "ds-color-accent",
    progressHeight = "12px",
    icon,
    variant = "default",
    size = "md",
    pulseAnimation = true,
    maxWidth = "800px",
    className,
  } = props;

  // State for progress animation
  const [progressValue, setProgressValue] = useState(initialProgress);
  const [isAnimating, setIsAnimating] = useState(false);

  // Animate progress from initial to maxProgress over time
  useEffect(() => {
    if (progressValue < maxProgress) {
      const interval = setInterval(() => {
        setProgressValue((prev) => {
          // Calculate the next value based on the increment
          const nextValue = prev + progressIncrement;
          // Cap at maxProgress
          return nextValue > maxProgress ? maxProgress : nextValue;
        });
      }, updateInterval);

      return () => clearInterval(interval);
    }
  }, [progressValue, maxProgress, updateInterval, progressIncrement]);

  // Add pulse animation effect
  useEffect(() => {
    if (pulseAnimation) {
      const pulseInterval = setInterval(() => {
        setIsAnimating(true);
        setTimeout(() => setIsAnimating(false), 1000);
      }, 2000);

      return () => clearInterval(pulseInterval);
    }
  }, [pulseAnimation]);

  // Determine size-based styles
  const sizeStyles = {
    sm: {
      padding: "40px 0",
      headingSize: "5" as const,
      textSize: "2" as const,
      messageSize: "1" as const,
      progressMaxWidth: "300px",
    },
    md: {
      padding: "60px 0",
      headingSize: "6" as const,
      textSize: "3" as const,
      messageSize: "2" as const,
      progressMaxWidth: "400px",
    },
    lg: {
      padding: "80px 0",
      headingSize: "7" as const,
      textSize: "4" as const,
      messageSize: "2" as const,
      progressMaxWidth: "500px",
    },
  };

  const currentSize = sizeStyles[size];

  return (
    <Container
      size="3"
      className={cn(
        "progress-page-container",
        `progress-page-variant-${variant}`,
        className,
      )}
      style={{ maxWidth, margin: "0 auto", padding: currentSize.padding }}
    >
      <Box className="progress-page-content">
        {icon && <Box className="progress-page-icon">{icon}</Box>}

        <Heading size={currentSize.headingSize} className="progress-page-title">
          {title}
        </Heading>

        <Text size={currentSize.textSize} className="progress-page-description">
          {description}
        </Text>

        <Flex
          direction="column"
          align="center"
          gap="3"
          className="progress-page-progress-container"
        >
          <Box
            className={cn(
              "progress-page-progress-wrapper",
              isAnimating && "progress-page-progress-pulse",
            )}
            style={{ maxWidth: currentSize.progressMaxWidth }}
          >
            <Progress
              value={progressValue}
              max={100}
              color={progressColor}
              height={progressHeight}
              className="progress-page-progress-bar"
            />
          </Box>

          {message && (
            <Text
              size={currentSize.messageSize}
              className="progress-page-message"
            >
              {message}
            </Text>
          )}
        </Flex>
      </Box>
      <style scoped>{`
        :scope .progress-page-container {
          width: 100%;
        }
        
        :scope .progress-page-content {
          text-align: center;
          padding: 40px 20px;
          border-radius: 12px;
          transition: all 0.3s ease;
        }

        :scope .progress-page-variant-minimal .progress-page-content {
          padding: 20px;
        }
        
        :scope .progress-page-icon {
          margin-bottom: 24px;
          display: flex;
          justify-content: center;
          font-size: 32px;
          color: var(--${progressColor}-9);
        }
        
        :scope .progress-page-title {
          margin-bottom: 16px;
          font-weight: 600;
        }
        
        :scope .progress-page-description {
          margin-bottom: 32px;
          color: var(--gray-11);
          max-width: 600px;
          margin-left: auto;
          margin-right: auto;
        }
        
        :scope .progress-page-progress-container {
          width: 100%;
        }
        
        :scope .progress-page-progress-wrapper {
          width: 100%;
          margin-bottom: 16px;
          position: relative;
          transition: all 0.2s ease;
        }
        
        :scope .progress-page-progress-pulse {
          opacity: 0.85;
          transform: scale(1.01);
        }
        
        :scope .progress-page-progress-bar {
          border-radius: 6px;
          overflow: hidden;
        }
        
        :scope .progress-page-message {
          color: var(--gray-11);
          font-style: italic;
        }
        
        @media (max-width: 640px) {
          :scope .progress-page-content {
            padding: 30px 16px;
          }
          
          :scope .progress-page-description {
            margin-bottom: 24px;
          }
        }
      `}</style>
    </Container>
  );
}

import { InfoCircledIcon } from "@radix-ui/react-icons";
import { Flex, IconButton, Popover, Text } from "@radix-ui/themes";

export default function MetricMetadata({
  title,
  definition,
}: {
  title: string;
  definition?: string;
}) {
  return (
    <Flex gap="2" align="center">
      <Text size="2" weight="regular">
        {title}
      </Text>
      {definition && (
        <Popover.Root>
          <Popover.Trigger>
            <IconButton variant="ghost">
              <InfoCircledIcon color="var(--blue-a11)" />
            </IconButton>
          </Popover.Trigger>
          <Popover.Content width="360px">
            <Flex gap="3">{definition}</Flex>
          </Popover.Content>
        </Popover.Root>
      )}
    </Flex>
  );
}

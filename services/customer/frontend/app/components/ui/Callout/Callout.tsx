import { useMemo, type ReactElement, type ReactNode } from "react";
import {
  Cross2Icon,
  InfoCircledIcon,
  ExclamationTriangleIcon,
  CheckCircledIcon,
} from "@radix-ui/react-icons";
import type { ClassValue } from "clsx";
import { cn, typography } from "app/utils/style";
import type { Nullish } from "@augment-internal/ts-utils/type";

type CalloutType = "default" | "info" | "warning" | "error" | "success";

type BaseCalloutProps = {
  // default: gray, info: blue, warning: gold, error: red, success: green
  type?: CalloutType;
  // not provided | undefined = the default icon based on the type
  // null = no icon
  // ReactElement = a custom icon provided
  icon?: ReactElement | null;
  // the body content
  children: ReactNode;
  className?: ClassValue;
  enabled?: boolean;
  size?: "small" | "normal";
};

type NonDismissableCalloutProps = BaseCalloutProps & {
  // not dismissable
  onDismiss?: never;
  isDismissed?: never;
};

type DismissableCalloutProps = BaseCalloutProps & {
  // shows X that calls onDismiss onclick
  onDismiss: () => void;
  isDismissed: boolean;
};

export type CalloutProps = NonDismissableCalloutProps | DismissableCalloutProps;

export function Callout({
  type = "default",
  icon,
  children,
  className,
  onDismiss,
  isDismissed,
  enabled = true,
  size = "normal",
}: CalloutProps) {
  const defaultIcon = useMemo(() => getDefaultIcon(icon, type), [icon, type]);
  if (!enabled) return null;
  if (isDismissed) return null;
  const font =
    size === "small" ? typography.text2.regular : typography.text3.regular;
  const padding =
    size === "small" ? "var(--ds-spacing-2)" : "var(--ds-spacing-4)";
  const paddingSide =
    size === "small" ? "var(--ds-spacing-3)" : "var(--ds-spacing-4)";

  return (
    <div
      className={cn("callout", `callout-${type}`, className)}
      role={type === "error" ? "alert" : "status"}
      aria-live={type === "error" ? "assertive" : "polite"}
    >
      {defaultIcon ? <div className="callout-icon">{defaultIcon}</div> : null}
      <div className="callout-content">{children}</div>
      {onDismiss && (
        <button
          type="button"
          className="callout-dismiss"
          onClick={onDismiss}
          aria-label="Dismiss"
        >
          <Cross2Icon />
        </button>
      )}
      <style scoped>{`
        :scope {
          --callout-default-border: var(--ds-color-neutral-6);
          --callout-default-bg: var(--ds-color-neutral-2);
          --callout-default-bg-hover: var(--ds-color-neutral-3);
          --callout-default-text: var(--ds-color-neutral-11);

          --callout-info-border: var(--ds-color-info-6);
          --callout-info-bg: var(--ds-color-info-2);
          --callout-info-bg-hover: var(--ds-color-info-3);
          --callout-info-text: var(--ds-text-info);

          --callout-warning-border: var(--ds-color-warning-6);
          --callout-warning-bg: var(--ds-color-warning-2);
          --callout-warning-bg-hover: var(--ds-color-warning-3);
          --callout-warning-text: var(--ds-text-warning);

          --callout-error-border: var(--ds-color-error-6);
          --callout-error-bg: var(--ds-color-error-2);
          --callout-error-bg-hover: var(--ds-color-error-3);
          --callout-error-text: var(--ds-text-error);

          --callout-success-border: var(--ds-color-success-6);
          --callout-success-bg: var(--ds-color-success-2);
          --callout-success-bg-hover: var(--ds-color-success-3);
          --callout-success-text: var(--ds-text-success);
        }

        :scope.callout {
          display: flex;
          align-items: flex-start;
          width: 100%;
          border-radius: var(--ds-radius-3);
          padding: ${padding} ${paddingSide};
          border: 1px solid;
          gap: var(--ds-spacing-2);
          align-items: center;
          ${font}
          transition: background-color 0.7s ease-out;
        }

        :scope.callout-default {
          border-color: var(--callout-default-border);
          background-color: var(--callout-default-bg);
          color: var(--callout-default-text);
        }

        :scope.callout-default:hover {
          background-color: var(--callout-default-bg-hover);
        }

        :scope.callout-info {
          border-color: var(--callout-info-border);
          background-color: var(--callout-info-bg);
          color: var(--callout-info-text);
        }

        :scope.callout-info:hover {
          background-color: var(--callout-info-bg-hover);
        }

        :scope.callout-warning {
          border-color: var(--callout-warning-border);
          background-color: var(--callout-warning-bg);
          color: var(--callout-warning-text);
        }

        :scope.callout-warning:hover {
          background-color: var(--callout-warning-bg-hover);
        }

        :scope.callout-error {
          border-color: var(--callout-error-border);
          background-color: var(--callout-error-bg);
          color: var(--callout-error-text);
        }

        :scope.callout-error:hover {
          background-color: var(--callout-error-bg-hover);
        }

        :scope.callout-success {
          border-color: var(--callout-success-border);
          background-color: var(--callout-success-bg);
          color: var(--callout-success-text);
        }

        :scope.callout-success:hover {
          background-color: var(--callout-success-bg-hover);
        }

        :scope.callout-icon {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          margin-top: 2px;
        }

        :scope.callout-content {
          flex: 1;
        }

        :scope.callout-dismiss {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          background: transparent;
          border: none;
          cursor: pointer;
          padding: 0;
          color: currentColor;
          opacity: 0.7;
          transition: opacity 0.2s;
        }

        :scope.callout-dismiss:hover {
          opacity: 1;
        }
      `}</style>
    </div>
  );
}

// Default icons based on type
function getDefaultIcon(icon: ReactElement | Nullish, type: CalloutType) {
  if (icon === null) return null;
  if (icon !== undefined) return icon;

  switch (type) {
    case "info":
      return <InfoCircledIcon />;
    case "warning":
      return <ExclamationTriangleIcon />;
    case "error":
      return <ExclamationTriangleIcon />;
    case "success":
      return <CheckCircledIcon />;
    default:
      return null;
  }
}

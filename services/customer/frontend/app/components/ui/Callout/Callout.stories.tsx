import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { type ComponentProps, useState } from "react";
import { Callout } from "./Callout";
import { MixerHorizontalIcon } from "@radix-ui/react-icons";
import { Button } from "@radix-ui/themes";

const meta: Meta<typeof Callout> = {
  title: "UI/Callout",
  component: Callout,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    type: {
      control: "select",
      options: ["default", "info", "warning", "error", "success"],
      description: "The type of callout which determines its color scheme",
    },
    icon: {
      control: "boolean",
      description: "Whether to show the default icon or not",
    },
    children: {
      control: "text",
      description: "The content of the callout",
    },
    onDismiss: {
      action: "dismissed",
      description: "Function called when the dismiss button is clicked",
    },
  },
  args: {
    type: "default",
    children: "This is a default callout with some information.",
    onDismiss: undefined,
  },
};

export default meta;
type Story = StoryObj<typeof Callout>;

export const AllVariants: Story = {
  render: (args) => (
    <div>
      <Callout {...args} type="default">
        This is a default callout with some information.
      </Callout>
      <Callout {...args} type="info">
        This is an informational callout with some details.
      </Callout>
      <Callout {...args} type="warning">
        Warning! This action might have consequences.
      </Callout>
      <Callout {...args} type="error">
        An error occurred while processing your request.
      </Callout>
      <Callout {...args} type="success">
        Your changes have been successfully saved.
      </Callout>
      <DismissableCallout {...args} />
      <style scoped>{`
        :scope {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }
      `}</style>
    </div>
  ),
};

export const AllVariantsSmall: Story = {
  render: (args) => (
    <div>
      <Callout {...args} type="default" size="small">
        This is a default callout with some information.
      </Callout>
      <Callout {...args} type="info" size="small">
        This is an informational callout with some details.
      </Callout>
      <Callout {...args} type="warning" size="small">
        Warning! This action might have consequences.
      </Callout>
      <Callout {...args} type="error" size="small">
        An error occurred while processing your request.
      </Callout>
      <Callout {...args} type="success" size="small">
        Your changes have been successfully saved.
      </Callout>
      <DismissableCallout {...args} size="small" />
      <style scoped>{`
        :scope {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }
      `}</style>
    </div>
  ),
};

export const Default: Story = {
  args: {
    type: "default",
    children: "This is a default callout with some information.",
  },
};

export const Info: Story = {
  args: {
    type: "info",
    children: "This is an informational callout with some details.",
  },
};

export const Warning: Story = {
  args: {
    type: "warning",
    children: "Warning! This action might have consequences.",
  },
};

export const Error: Story = {
  args: {
    type: "error",
    children: "An error occurred while processing your request.",
  },
};

export const Success: Story = {
  args: {
    type: "success",
    children: "Your changes have been successfully saved.",
  },
};

export const WithCustomIcon: Story = {
  args: {
    type: "info",
    icon: <MixerHorizontalIcon />,
    children: "This callout has a custom icon.",
  },
};

export const WithNoIcon: Story = {
  args: {
    type: "info",
    icon: null,
    children: "This callout has no icon.",
  },
};

const DismissableCallout = (args: ComponentProps<typeof Callout>) => {
  const [isDismissed, setIsDismissed] = useState(false);
  if (isDismissed) {
    return <Button onClick={() => setIsDismissed(false)}>Reopen</Button>;
  }
  return (
    <Callout
      {...args}
      isDismissed={isDismissed}
      onDismiss={() => setIsDismissed(true)}
    >
      This callout can be dismissed. Click the X to close it.
    </Callout>
  );
};

export const Dismissable: Story = {
  render: DismissableCallout,
  args: {
    type: "info",
  },
};

export const MultilineContent: Story = {
  args: {
    type: "warning",
    children: (
      <>
        <p>
          <strong>Multiple lines of content</strong>
        </p>
        <p>
          This callout contains multiple paragraphs to demonstrate how it
          handles longer content.
        </p>
        <p>The height will adjust automatically based on the content.</p>
      </>
    ),
  },
};

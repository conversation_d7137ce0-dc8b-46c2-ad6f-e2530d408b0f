// eslint-disable-next-line no-restricted-imports
import { Separator as RadixSeparator } from "@radix-ui/themes";
import type { SeparatorProps as RadixSeparatorProps } from "@radix-ui/themes";

export type SeparatorProps = RadixSeparatorProps;

/**
 * A separator is a thin line that visually separates content in a container.
 *
 * Use separators to help users scan and differentiate content in a container.
 *
 * @see https://www.radix-ui.com/themes/docs/components/separator
 */
export function Separator({
  size = "4",
  orientation = "horizontal",
  color = "gray",
  decorative = false,
  ...props
}: SeparatorProps) {
  return (
    <RadixSeparator
      className="separator"
      orientation={orientation}
      size={size}
      color={color}
      decorative={decorative}
      {...props}
    />
  );
}

export default Separator;

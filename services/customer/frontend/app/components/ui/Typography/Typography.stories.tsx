import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { typography } from "app/utils/style";

const fontWeights = ["light", "regular", "medium", "bold"] as const;
type Level = keyof typeof typography;

function generateTypographyCSS() {
  return (Object.keys(typography) as Level[])
    .flatMap((level) =>
      fontWeights.map(
        (weight) => `.${level}-${weight} { ${typography[level][weight]} }`,
      ),
    )
    .join("\n");
}

/* All layout / spacing / flex styles in one chunk */
const layoutCSS = /* css */ `
.wrapper         { display: grid; gap: 1.5rem; }
.section-heading { margin-bottom: 0.75rem; }
.row             { display: flex; gap: 2rem; flex-wrap: wrap; }
`;

function TypographyShowcase() {
  return (
    <article className="wrapper">
      {(Object.keys(typography) as Level[]).map((level) => (
        <section key={level}>
          <h3 className="section-heading">{level}</h3>

          <div className="row">
            {fontWeights.map((weight) => (
              <p key={`${level}-${weight}`} className={`${level}-${weight}`}>
                {level} – {weight}
              </p>
            ))}
          </div>
        </section>
      ))}

      <style scoped>{layoutCSS + generateTypographyCSS()}</style>
    </article>
  );
}

export default {
  title: "Foundations/Typography (scoped)",
  component: TypographyShowcase,
} satisfies Meta<typeof TypographyShowcase>;

export const Showcase: StoryObj<typeof TypographyShowcase> = {
  render: () => <TypographyShowcase />,
};

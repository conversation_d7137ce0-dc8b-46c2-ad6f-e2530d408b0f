import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Tooltip } from "./Tooltip";
import { Button } from "@radix-ui/themes";

const meta = {
  title: "UI/Tooltip/Tooltip",
  component: Tooltip,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    content: {
      description: "The content to display in the tooltip",
      control: "text",
    },
    side: {
      description: "The side of the tooltip relative to the trigger",
      control: { type: "select" },
      options: ["top", "right", "bottom", "left"],
    },
    align: {
      description: "The alignment of the tooltip relative to the trigger",
      control: { type: "select" },
      options: ["start", "center", "end"],
    },
    enabled: {
      description: "Whether the tooltip is enabled",
      control: "boolean",
    },
  },
  args: {
    content: "This is a tooltip",
    side: "top",
    align: "center",
    enabled: true,
  },
} satisfies Meta<typeof Tooltip>;

export default meta;

type Story = StoryObj<typeof meta>;
type StoryArgs = Story["args"];

export function Default(args: StoryArgs = meta.args) {
  const { content, ...rest } = args;
  return (
    <Tooltip content={content} {...rest}>
      <Button>Hover me</Button>
    </Tooltip>
  );
}

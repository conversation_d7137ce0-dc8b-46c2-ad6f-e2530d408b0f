import { Tooltip as RadixTooltip } from "@radix-ui/themes";
import { Enabled } from "../Enabled";

type TooltipProps = React.ComponentProps<typeof RadixTooltip> & {
  enabled?: boolean;
};

export function Tooltip(props: TooltipProps) {
  const { content, enabled = true, ...rest } = props;
  return (
    <Enabled enabled={enabled && Boolean(content)} asChild>
      <RadixTooltip content={content} {...rest} />
    </Enabled>
  );
}

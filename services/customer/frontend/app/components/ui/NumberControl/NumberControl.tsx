import type React from "react";
import { useState } from "react";
import { TextField, IconButton } from "@radix-ui/themes";
import { PlusIcon, MinusIcon } from "@radix-ui/react-icons";

type NumberControlProps = {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
};

export function NumberControl(props: NumberControlProps) {
  const { value, onChange, min = -Infinity, max = Infinity } = props;
  const [inputValue, setInputValue] = useState(value);

  function handleInputChange(e: React.ChangeEvent<HTMLInputElement>) {
    const value = parseInt(e.target.value, 10);
    if (isNaN(value)) return;
    setInputValue(value);
  }

  function handleChange(newValue: number) {
    if (newValue > max) newValue = max;
    if (newValue < min) newValue = min;
    setInputValue(newValue);
    onChange(newValue);
  }

  const inputWidth = Math.max(3, String(value).length + 1) + "ch";
  return (
    <div className="update-value-container">
      <div
        className="number-input-container"
        onBlur={() => handleChange(inputValue)}
      >
        <IconButton
          size="1"
          variant="ghost"
          onMouseUp={() => handleChange(inputValue - 1)}
          disabled={inputValue <= min}
          className="control-button"
          aria-label="Decrease value"
        >
          <MinusIcon />
        </IconButton>
        <TextField.Root
          type="number"
          value={inputValue}
          onChange={handleInputChange}
          min={min}
          max={max}
          aria-label="Number of value"
          data-testid="value-input"
          className="number-input"
        />
        <IconButton
          size="1"
          variant="ghost"
          onMouseUp={() => handleChange(inputValue + 1)}
          disabled={inputValue >= max}
          className="control-button"
          aria-label="Increase value"
        >
          <PlusIcon strokeWidth={20} />
        </IconButton>
      </div>

      <style scoped>{`
        :scope.update-value-container {
          display: flex;
          flex-direction: row;
          align-items: center;
          gap: var(--ds-spacing-1);
        }
        .number-input-container {
          position: relative;
          display: flex;
          width: fit-content;
          align-items: center;
          border: 1px solid var(--gray-a7);
          border-radius: var(--radius-2, 4px);
          overflow: hidden;
          outline: none;
        }
        .number-input-container:focus-within,
        .number-input-container:focus {
          outline: var(--ds-color-accent-9);
          outline-width: 1px;
          outline-offset: 0px;
          outline-style: auto;
        }
        .number-input {
          width: ${inputWidth};
          box-shadow: unset;
          outline: none;
          text-align: center;
          --text-field-padding: 0;
        }
        .number-input :global(input) {
          border: none;
          text-align: center;
        }
        .control-button {
          height: 100%;
          width: var(--ds-spacing-5);
          margin: 0;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: transparent;
          border-radius: 0;
          border: none;
          box-shadow: unset;
          color: var(--gray-a11);
        }
        .control-button[data-disabled] {
          color: var(--gray-a7);
        }
        .control-button:hover:not([data-disabled]) {
          color: var(--ds-color-accent-9);
        }
        .control-button:active:not([data-disabled]) {
          color: var(--ds-color-accent-11);
        }
      `}</style>
    </div>
  );
}

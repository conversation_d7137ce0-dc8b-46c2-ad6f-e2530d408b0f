import type { <PERSON>a, StoryObj } from "@storybook/react";
import { NumberControl } from "./NumberControl";
import { useState } from "react";

const meta = {
  title: "UI/NumberControl",
  component: NumberControl,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    value: {
      description: "The current value",
    },
    onChange: {
      description: "The function to call when the value changes",
      action: "changed",
    },
    min: {
      description: "The minimum value",
      control: { type: "number" },
    },
    max: {
      description: "The maximum value",
      control: { type: "number" },
    },
  },
  // Default Values
  args: {
    min: 0,
    max: 5,
  },
} satisfies Meta<typeof NumberControl>;

export default meta;

type Story = StoryObj<typeof meta>;
type StoryArgs = Story["args"];

// Stories
export function Default({ min, max }: StoryArgs) {
  const [value, setValue] = useState(1);
  return (
    <NumberControl value={value} onChange={setValue} min={min} max={max} />
  );
}

import { render, fireEvent, act } from "@testing-library/react";
import { createToaster, ANIMATION_OUT_DURATION } from "./Toast";
import { vi, describe, test, expect, beforeEach, afterEach } from "vitest";

describe("Toast component", () => {
  const originalAddEventListener = window.addEventListener;
  const originalDispatchEvent = window.dispatchEvent;
  let toaster: ReturnType<typeof createToaster>;

  beforeEach(() => {
    toaster = createToaster();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.restoreAllMocks();
    vi.useRealTimers();
    window.addEventListener = originalAddEventListener;
    window.dispatchEvent = originalDispatchEvent;
  });

  test("toast function collects events while mounted", async () => {
    const { container } = render(<toaster.Toasts />);

    act(() => {
      toaster.toast({ title: "Test Title", description: "Test Description" });
    });
    expect(container.querySelector(".ToastViewport")).toBeInTheDocument();
    expect(container.querySelector(".ToastRoot")).toBeInTheDocument();
    const toastItems = container.querySelectorAll(".ToastRoot");
    expect(toastItems.length).toBe(1);
    expect(toastItems[0]).toHaveTextContent("Test Title");
  });

  test("unmountedQueue stores toasts when not mounted", async () => {
    // Call toast before mounting component
    act(() => {
      toaster.toast({ title: "Test Title", description: "Test Description" });
    });

    expect(toaster.unmountedQueue.length).toBe(1);
    expect(toaster.unmountedQueue[0].title).toBe("Test Title");

    const { container, unmount } = render(<toaster.Toasts />);

    expect(container.querySelector(".ToastViewport")).toBeInTheDocument();
    expect(container.querySelector(".ToastRoot")).toBeInTheDocument();
    expect(toaster.unmountedQueue.length).toBe(0);

    const toastItems = container.querySelectorAll(".ToastRoot");
    expect(toastItems.length).toBe(1);
    expect(toastItems[0]).toHaveTextContent("Test Title");

    unmount();

    act(() => {
      toaster.toast({
        title: "Test Title 2",
        description: "Test Description 2",
      });
    });

    // Queue should still have the previous toast
    expect(toaster.unmountedQueue.length).toBe(1);
    expect(toaster.unmountedQueue[0].title).toBe("Test Title 2");
  });

  test("toastElementsMap tracks toast elements", () => {
    const { container } = render(<toaster.Toasts />);
    expect(container.querySelector(".ToastViewport")).toBeInTheDocument();

    // Add a toast
    act(() => {
      toaster.toast.success({
        title: "Success",
        description: "Operation successful",
      });
    });

    // Check if element was added to map
    expect(toaster.toastElementsMap.size).toBe(1);
  });

  test("toast removal cleans up toastElementsMap and calls sortToasts", async () => {
    const { container } = render(<toaster.Toasts />);

    act(() => {
      toaster.toast.success({
        title: "Success",
        description: "Operation successful",
      });
    });

    // Find the close button and click it
    const closeButton = container.querySelector(".ToastClose")!;
    fireEvent.click(closeButton);

    act(() => {
      vi.advanceTimersByTime(ANIMATION_OUT_DURATION + 10);
    });

    // Map should be empty after animation completes
    expect(toaster.toastElementsMap.size).toBe(0);
  });
});

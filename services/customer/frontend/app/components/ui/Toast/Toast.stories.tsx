import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { toast, Toasts } from "./Toast";
import { Button } from "@radix-ui/themes";
import "../../../styles/toasts.css";

const meta = {
  title: "UI/Toast",
  component: Toasts,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div>
        <Toasts>
          <Story />
        </Toasts>
        <style scoped>{`
          :scope {
            width: 100%;
            height: 50vh;
            position: relative;
          }
        `}</style>
      </div>
    ),
  ],
} satisfies Meta<typeof Toasts>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic toast example
export const Default: Story = {
  render: () => {
    return (
      <div>
        <Button
          onClick={() => {
            toast({
              status: "default",
              title: "Default Toast",
              description: "This is a default toast notification",
            });
          }}
        >
          Add Default Toast
        </Button>
        <Button
          onClick={() => {
            toast.default({
              title: "Default Toast",
              description: "This is a default toast notification",
            });
          }}
          variant="outline"
        >
          Add Default Toast
        </Button>
        <Button
          onClick={() => {
            toast.success({
              title: "Success!",
              description: "Your action was completed successfully",
            });
          }}
          color="green"
        >
          Add Success Toast
        </Button>
        <Button
          onClick={() => {
            toast.error({
              title: "Error",
              description: "Something went wrong. Please try again.",
            });
          }}
          color="red"
        >
          Add Error Toast
        </Button>
        <style scoped>
          {`
            :scope {
              display: flex;
              gap: 1rem;
              flex-wrap: wrap;
            }
          `}
        </style>
      </div>
    );
  },
};

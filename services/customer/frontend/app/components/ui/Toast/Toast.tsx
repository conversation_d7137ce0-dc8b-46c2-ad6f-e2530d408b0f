import {
  useCallback,
  useLayoutEffect,
  useRef,
  useState,
  useEffect,
  type ReactNode,
} from "react";
import * as ToastPrimitive from "@radix-ui/react-toast";
import { Cross2Icon } from "@radix-ui/react-icons";
import { addEventListener } from "../../../utils/extra";

export const ANIMATION_OUT_DURATION = 350;

type ToastPayload = {
  title: ReactNode;
  description: ReactNode;
  duration?: number;
  type?: "foreground" | "background";
};

type Toast = {
  title: ReactNode;
  description: ReactNode;
  duration?: number;
  type?: "foreground" | "background";
  status: "default" | "success" | "error";
  open: boolean;
};

const CheckmarkIcon = () => <div aria-hidden className="checkmark" />;
const ErrorIcon = () => <div aria-hidden className="error" />;

let namespaceId = 0;

/**
 * Creates a new toaster instance.
 * @example
 * const { toast, Toasts } = createToaster();
 * toast({ title: "Hello", description: "World" });
 * // Render the Toasts component somewhere in your app
 * <Toasts />
 *
 * @returns
 */
export function createToaster() {
  /** Unique event name for this toaster instance */
  const TOAST_EVENT = `__MY_CUSTOM_TOAST_EVENT__${namespaceId++}`;

  /** Track whether our <Toast> component is mounted */
  let isMounted = false;

  /** If <Toast> isn't mounted yet, store calls here */
  const unmountedQueue: Omit<Toast, "open">[] = [];

  const toastElementsMap = new Map<string, HTMLElement>();

  function sortToasts() {
    const toastElements = Array.from(toastElementsMap).reverse();
    const heights: number[] = [];

    toastElements.forEach(([, toastEl], index) => {
      if (!toastEl) return;
      const height = toastEl.clientHeight;
      heights.push(height);

      // The front toast is index === 0
      const frontToastHeight = heights[0];
      toastEl.setAttribute("data-front", String(index === 0));
      toastEl.setAttribute("data-hidden", String(index > 2));
      toastEl.style.setProperty("--index", String(index));
      toastEl.style.setProperty("--height", `${height}px`);
      toastEl.style.setProperty("--front-height", `${frontToastHeight}px`);

      const hoverOffsetY = heights
        .slice(0, index)
        .reduce((res, next) => (res += next), 0);
      toastEl.style.setProperty("--hover-offset-y", `-${hoverOffsetY}px`);
    });
  }

  // Helper to dispatch or queue toast events
  function dispatchToast(payload: Omit<Toast, "open">) {
    if (!isMounted) {
      // If the Toast component isn’t mounted, queue it
      unmountedQueue.push(payload);
      return;
    }
    // Otherwise, dispatch a CustomEvent
    window.dispatchEvent(new CustomEvent(TOAST_EVENT, { detail: payload }));
  }

  function toast(
    payloadWithStatus: ToastPayload & { status?: Toast["status"] },
  ) {
    dispatchToast({ status: "default", ...payloadWithStatus });
  }

  toast.default = (payload: ToastPayload) =>
    dispatchToast({ ...payload, status: "default" });
  toast.success = (payload: ToastPayload) =>
    dispatchToast({ ...payload, status: "success" });
  toast.error = (payload: ToastPayload) =>
    dispatchToast({ ...payload, status: "error" });

  // The React component that holds & renders Radix toasts. This refactors the original
  // approach to rely on our custom event system plus a queue if unmounted.
  function Toasts(props: ToastPrimitive.ToastProviderProps) {
    const [toasts, setToasts] = useState<Map<string, Toast>>(new Map());
    const viewportRef = useRef<HTMLOListElement>(null);

    // Helper to remove a toast
    const removeToast = useCallback(
      (key: string) => {
        setToasts((prev) => {
          const newMap = new Map(prev);
          newMap.delete(key);
          return newMap;
        });
      },
      [setToasts],
    );

    // On mount, mark as mounted, flush any queued calls,
    // and add an event listener for toasts.
    useEffect(() => {
      isMounted = true;

      // Helper to add a toast
      function addToast(payload: Omit<Toast, "open">) {
        setToasts((prev) => {
          const newMap = new Map(prev);
          newMap.set(String(Date.now()), { ...payload, open: true });
          return newMap;
        });
      }

      // Flush whatever was queued while we weren't mounted
      unmountedQueue.forEach(addToast);
      unmountedQueue.length = 0;

      // Listen for new toasts
      const removeToastEventListener = addEventListener(
        globalThis,
        TOAST_EVENT,
        function handleToastEvent(e: Event) {
          const { detail } = e as CustomEvent<Omit<Toast, "open">>;
          if (detail) {
            addToast(detail);
          }
        },
      );

      return () => {
        removeToastEventListener();
        isMounted = false;
      };
    }, []);

    // Pointer & focus handling from the original snippet
    useEffect(() => {
      const viewport = viewportRef.current;
      if (!viewport) return;

      function handleFocus() {
        toastElementsMap.forEach((toastEl) => {
          toastEl.setAttribute("data-hovering", "true");
        });
      }

      function handleBlur(event: FocusEvent) {
        // If the mouse/focus leaves the container, remove "hovering"
        if (
          (viewport && !viewport.contains(event.target as Node)) ||
          viewport === event.target
        ) {
          toastElementsMap.forEach((toastEl) => {
            toastEl.setAttribute("data-hovering", "false");
          });
        }
      }

      viewport.addEventListener("pointermove", handleFocus);
      viewport.addEventListener("pointerleave", handleBlur);
      viewport.addEventListener("focusin", handleFocus);
      viewport.addEventListener("focusout", handleBlur);

      return () => {
        viewport.removeEventListener("pointermove", handleFocus);
        viewport.removeEventListener("pointerleave", handleBlur);
        viewport.removeEventListener("focusin", handleFocus);
        viewport.removeEventListener("focusout", handleBlur);
      };
    }, []);

    return (
      <ToastPrimitive.Provider {...props}>
        {props.children}
        {Array.from(toasts).map(([key, toastObj]) => (
          <ToastItem
            key={key}
            id={key}
            toast={toastObj}
            onOpenChange={(open) => {
              if (!open) {
                // Remove from the element map & re-sort
                toastElementsMap.delete(key);
                sortToasts();

                // Delay final removal to allow exit animation
                setTimeout(() => removeToast(key), ANIMATION_OUT_DURATION);
              }
            }}
          />
        ))}
        <ToastPrimitive.Viewport ref={viewportRef} className="ToastViewport" />
      </ToastPrimitive.Provider>
    );
  }

  interface ToastItemProps extends Omit<ToastPrimitive.ToastProps, "type"> {
    onOpenChange: (open: boolean) => void;
    toast: Toast;
    id: string;
  }

  function ToastItem(props: ToastItemProps) {
    const { onOpenChange, toast, id, ...toastProps } = props;
    const elementRef = useRef<HTMLLIElement>(null);
    const { duration, type, status, title, description } = toast;

    useLayoutEffect(() => {
      if (elementRef.current) {
        toastElementsMap.set(id, elementRef.current);
        sortToasts();
      }
    }, [id]);

    return (
      <ToastPrimitive.Root
        {...toastProps}
        ref={elementRef}
        type={type}
        duration={duration}
        className="ToastRoot"
        onOpenChange={onOpenChange}
      >
        <div className="ToastInner" data-status={status}>
          <ToastStatusIcon status={status} />
          <ToastPrimitive.Title className="ToastTitle">
            <p>{title}</p>
          </ToastPrimitive.Title>
          <ToastPrimitive.Description className="ToastDescription">
            {description}
          </ToastPrimitive.Description>
          <ToastPrimitive.Close aria-label="Close" className="ToastClose">
            <Cross2Icon />
          </ToastPrimitive.Close>
        </div>
      </ToastPrimitive.Root>
    );
  }

  function ToastStatusIcon({ status }: { status: Toast["status"] }) {
    if (status === "default") {
      return null;
    }
    return (
      <div style={{ gridArea: "icon", alignSelf: "start" }}>
        {status === "success" && <CheckmarkIcon />}
        {status === "error" && <ErrorIcon />}
      </div>
    );
  }

  return {
    toast,
    Toasts,
    TOAST_EVENT,
    unmountedQueue,
    toastElementsMap,
    sortToasts,
  };
}

export let { toast, Toasts } = createToaster();

/** For use in storybook only. Allows us to create a new toaster instance for each story. */
export function setToaster(toaster: ReturnType<typeof createToaster>) {
  ({ toast, Toasts } = toaster);
}

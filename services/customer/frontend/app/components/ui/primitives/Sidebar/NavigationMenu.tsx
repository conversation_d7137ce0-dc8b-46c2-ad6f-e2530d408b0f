import * as Base from "@radix-ui/react-navigation-menu";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "app/utils/style";
import {
  type ComponentPropsWithoutRef,
  type ElementRef,
  forwardRef,
} from "react";

const rootVariants = cva("relative inline-flex flex-col", {
  variants: {
    orientation: {
      horizontal: "flex-row",
      vertical: "flex-col",
    },
  },
  defaultVariants: {
    orientation: "horizontal",
  },
});

type RootProps = ComponentPropsWithoutRef<typeof Base.Root> &
  VariantProps<typeof rootVariants>;

export const Root = forwardRef<ElementRef<typeof Base.Root>, RootProps>(
  (props, ref) => {
    const { className = "", orientation, ...rest } = props;
    return (
      <Base.Root
        ref={ref}
        {...rest}
        className={cn(rootVariants({ orientation }), className)}
      />
    );
  },
);
Root.displayName = "NavigationMenuRoot";

const subVariants = cva({
  variants: {
    orientation: {
      horizontal: "flex-row",
      vertical: "flex-col",
    },
  },
  defaultVariants: {
    orientation: "horizontal",
  },
});

type SubProps = ComponentPropsWithoutRef<typeof Base.Sub>;

export const Sub = forwardRef<ElementRef<typeof Base.Sub>, SubProps>(
  (props, ref) => {
    const { className = "", ...rest } = props;
    return (
      <Base.Sub ref={ref} {...rest} className={cn(subVariants(), className)} />
    );
  },
);
Sub.displayName = "NavigationMenuSub";

const listVariants = cva("flex list-none p-0 m-0", {
  variants: {},
  defaultVariants: {},
});

type listProps = ComponentPropsWithoutRef<typeof Base.List> &
  VariantProps<typeof listVariants>;

export const List = forwardRef<ElementRef<typeof Base.List>, listProps>(
  (props, ref) => {
    const { className = "", ...rest } = props;
    return (
      <Base.List
        ref={ref}
        {...rest}
        className={cn(listVariants(), className)}
      />
    );
  },
);
List.displayName = "NavigationMenuList";

const itemVariants = cva("relative");

type ItemProps = ComponentPropsWithoutRef<typeof Base.Item>;

export const Item = forwardRef<ElementRef<typeof Base.Item>, ItemProps>(
  (props, ref) => {
    const { className = "", ...rest } = props;
    return (
      <Base.Item
        ref={ref}
        {...rest}
        className={cn(itemVariants(), className)}
      />
    );
  },
);
Item.displayName = "NavigationMenuItem";

const triggerVariants = cva(
  "inline-flex items-center justify-center px-3 py-2 rounded-md outline-none focus:ring focus:ring-blue-500",
);

type TriggerProps = ComponentPropsWithoutRef<typeof Base.Trigger>;

export const Trigger = forwardRef<
  ElementRef<typeof Base.Trigger>,
  TriggerProps
>((props, ref) => {
  const { className = "", ...rest } = props;
  return (
    <Base.Trigger
      ref={ref}
      {...rest}
      className={cn(triggerVariants(), className)}
    />
  );
});
Trigger.displayName = "NavigationMenuTrigger";

const contentVariants = cva("absolute top-full left-0 mt-2");

type ContentProps = ComponentPropsWithoutRef<typeof Base.Content>;

export const Content = forwardRef<
  ElementRef<typeof Base.Content>,
  ContentProps
>((props, ref) => {
  const { className = "", ...rest } = props;
  return (
    <Base.Content
      ref={ref}
      {...rest}
      className={cn(contentVariants(), className)}
    />
  );
});
Content.displayName = "NavigationMenuContent";

const linkVariants = cva("text-sm outline-none focus:underline cursor-pointer");

type LinkProps = ComponentPropsWithoutRef<typeof Base.Link>;

export const Link = forwardRef<ElementRef<typeof Base.Link>, LinkProps>(
  (props, ref) => {
    const { className = "", ...rest } = props;
    return (
      <Base.Link
        ref={ref}
        {...rest}
        className={cn(linkVariants(), className)}
      />
    );
  },
);
Link.displayName = "NavigationMenuLink";

const indicatorVariants = cva("absolute bg-gray-200");

type IndicatorProps = ComponentPropsWithoutRef<typeof Base.Indicator>;

export const Indicator = forwardRef<
  ElementRef<typeof Base.Indicator>,
  IndicatorProps
>((props, ref) => {
  const { className = "", ...rest } = props;
  return (
    <Base.Indicator
      ref={ref}
      {...rest}
      className={cn(indicatorVariants(), className)}
    />
  );
});
Indicator.displayName = "NavigationMenuIndicator";

const viewportVariants = cva("relative transition-all duration-200");

type ViewportProps = ComponentPropsWithoutRef<typeof Base.Viewport>;

export const Viewport = forwardRef<
  ElementRef<typeof Base.Viewport>,
  ViewportProps
>((props, ref) => {
  const { className = "", ...rest } = props;
  return (
    <Base.Viewport
      ref={ref}
      {...rest}
      className={cn(viewportVariants(), className)}
    />
  );
});
Viewport.displayName = "NavigationMenuViewport";

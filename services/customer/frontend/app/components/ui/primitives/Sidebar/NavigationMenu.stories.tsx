import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import * as NavigationMenu from "./NavigationMenu";

const meta = {
  title: "Primitive/NavigationMenu",
} satisfies Meta<typeof NavigationMenu>;

type Story = StoryObj<typeof meta>;

export default meta;

// The simplest story, showing a horizontal menu
export const HorizontalMenu: Story = {
  render: () => (
    <NavigationMenu.Root orientation="horizontal">
      <NavigationMenu.List>
        <NavigationMenu.Item>
          <NavigationMenu.Trigger>Item One</NavigationMenu.Trigger>
          <NavigationMenu.Content>
            <div className="p-4 bg-gray-100">
              <NavigationMenu.Link href="#">Sub Link A</NavigationMenu.Link>
              <NavigationMenu.Link href="#">Sub Link B</NavigationMenu.Link>
            </div>
          </NavigationMenu.Content>
        </NavigationMenu.Item>
        <NavigationMenu.Item>
          <NavigationMenu.Trigger>Item Two</NavigationMenu.Trigger>
          <NavigationMenu.Content>
            <div className="p-4 bg-gray-100">
              <NavigationMenu.Link href="#">Sub Link C</NavigationMenu.Link>
            </div>
          </NavigationMenu.Content>
        </NavigationMenu.Item>
        <NavigationMenu.Indicator />
      </NavigationMenu.List>
      <NavigationMenu.Viewport className="bg-blue-50" />
    </NavigationMenu.Root>
  ),
};

// Another variation: vertical orientation
export const VerticalMenu: Story = {
  render: () => (
    <NavigationMenu.Root orientation="vertical">
      <NavigationMenu.List>
        <NavigationMenu.Item>
          <NavigationMenu.Trigger>Item One</NavigationMenu.Trigger>
          <NavigationMenu.Content>
            <div className="p-4 bg-gray-100">
              <NavigationMenu.Link href="#">
                Vertical Link A
              </NavigationMenu.Link>
              <NavigationMenu.Link href="#">
                Vertical Link B
              </NavigationMenu.Link>
            </div>
          </NavigationMenu.Content>
        </NavigationMenu.Item>
        <NavigationMenu.Item>
          <NavigationMenu.Trigger>Item Two</NavigationMenu.Trigger>
          <NavigationMenu.Content>
            <div className="p-4 bg-gray-100">
              <NavigationMenu.Link href="#">
                Vertical Link C
              </NavigationMenu.Link>
            </div>
          </NavigationMenu.Content>
        </NavigationMenu.Item>
        <NavigationMenu.Indicator />
      </NavigationMenu.List>
      <NavigationMenu.Viewport className="bg-blue-50" />
    </NavigationMenu.Root>
  ),
};

import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { useState, useEffect } from "react";
import { Box, Flex, Text, Button } from "@radix-ui/themes";
import { Progress } from "./Progress";

const meta = {
  title: "UI/Progress",
  component: Progress,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    value: {
      control: { type: "range", min: 0, max: 100, step: 1 },
      description: "Current progress value (0-100)",
    },
    max: {
      control: { type: "number", min: 1, max: 1000, step: 1 },
      description: "Maximum progress value",
    },
    color: {
      control: "select",
      options: [
        "ds-color-accent-4",
        "ds-color-success-4",
        "ds-color-error-4",
        "ds-color-warning-4",
        "ds-color-neutral-4",
      ],
      description: "Color of the progress indicator",
    },
    height: {
      control: "text",
      description: "Height of the progress bar",
    },
    className: {
      control: "text",
      description: "Additional CSS class names",
    },
  },
} satisfies Meta<typeof Progress>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic progress bar with default settings
export const Default: Story = {
  args: {
    value: 50,
    max: 100,
    color: "ds-color-accent-3",
    height: "8px",
  },
};

// Progress bar with different heights
export const Heights: Story = {
  render: () => (
    <Flex direction="column" gap="4" style={{ width: "300px" }}>
      <Box>
        <Text size="2" style={{ marginBottom: "4px" }}>
          Thin (4px)
        </Text>
        <Progress value={60} max={100} height="4px" />
      </Box>
      <Box>
        <Text size="2" style={{ marginBottom: "4px" }}>
          Default (8px)
        </Text>
        <Progress value={60} max={100} height="8px" />
      </Box>
      <Box>
        <Text size="2" style={{ marginBottom: "4px" }}>
          Medium (12px)
        </Text>
        <Progress value={60} max={100} height="12px" />
      </Box>
      <Box>
        <Text size="2" style={{ marginBottom: "4px" }}>
          Thick (16px)
        </Text>
        <Progress value={60} max={100} height="16px" />
      </Box>
    </Flex>
  ),
};

// Progress bar with different colors
export const Colors: Story = {
  render: () => (
    <Flex direction="column" gap="4" style={{ width: "300px" }}>
      <Box>
        <Text size="2" style={{ marginBottom: "4px" }}>
          Accent
        </Text>
        <Progress value={70} max={100} color="ds-color-accent" />
      </Box>
      <Box>
        <Text size="2" style={{ marginBottom: "4px" }}>
          Success
        </Text>
        <Progress value={70} max={100} color="ds-color-success" />
      </Box>
      <Box>
        <Text size="2" style={{ marginBottom: "4px" }}>
          Error
        </Text>
        <Progress value={70} max={100} color="ds-color-error" />
      </Box>
      <Box>
        <Text size="2" style={{ marginBottom: "4px" }}>
          Warning
        </Text>
        <Progress value={70} max={100} color="ds-color-warning" />
      </Box>
      <Box>
        <Text size="2" style={{ marginBottom: "4px" }}>
          Neutral
        </Text>
        <Progress value={70} max={100} color="ds-color-neutral" />
      </Box>
    </Flex>
  ),
};

// Progress bar with different values
export const Values: Story = {
  render: () => (
    <Flex direction="column" gap="4" style={{ width: "300px" }}>
      <Box>
        <Text size="2" style={{ marginBottom: "4px" }}>
          Empty (0%)
        </Text>
        <Progress value={0} max={100} />
      </Box>
      <Box>
        <Text size="2" style={{ marginBottom: "4px" }}>
          Quarter (25%)
        </Text>
        <Progress value={25} max={100} />
      </Box>
      <Box>
        <Text size="2" style={{ marginBottom: "4px" }}>
          Half (50%)
        </Text>
        <Progress value={50} max={100} />
      </Box>
      <Box>
        <Text size="2" style={{ marginBottom: "4px" }}>
          Three quarters (75%)
        </Text>
        <Progress value={75} max={100} />
      </Box>
      <Box>
        <Text size="2" style={{ marginBottom: "4px" }}>
          Full (100%)
        </Text>
        <Progress value={100} max={100} />
      </Box>
    </Flex>
  ),
};

// Interactive progress bar with animation
export const Animated: Story = {
  render: () => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [progress, setProgress] = useState(0);
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [isRunning, setIsRunning] = useState(false);

    // eslint-disable-next-line react-hooks/rules-of-hooks
    useEffect(() => {
      let interval: NodeJS.Timeout;

      if (isRunning) {
        interval = setInterval(() => {
          setProgress((prev) => {
            if (prev >= 100) {
              setIsRunning(false);
              return 100;
            }
            return prev + 5;
          });
        }, 300);
      }

      return () => {
        if (interval) clearInterval(interval);
      };
    }, [isRunning]);

    const startProgress = () => {
      setProgress(0);
      setIsRunning(true);
    };

    const resetProgress = () => {
      setProgress(0);
      setIsRunning(false);
    };

    return (
      <Flex direction="column" gap="4" style={{ width: "300px" }}>
        <Box>
          <Text size="2" style={{ marginBottom: "4px" }}>
            Progress: {progress}%
          </Text>
          <Progress
            value={progress}
            max={100}
            color="ds-color-accent"
            height="12px"
          />
        </Box>
        <Flex gap="2">
          <Button onClick={startProgress} disabled={isRunning}>
            {progress === 0 ? "Start" : "Restart"}
          </Button>
          <Button onClick={resetProgress} disabled={progress === 0}>
            Reset
          </Button>
        </Flex>
      </Flex>
    );
  },
};

// Progress bar with indeterminate animation
export const Indeterminate: Story = {
  render: () => {
    return (
      <Flex direction="column" gap="4" style={{ width: "300px" }}>
        <Box>
          <Text size="2" style={{ marginBottom: "4px" }}>
            Indeterminate progress
          </Text>
          <div className="indeterminate-progress-container">
            <Progress value={30} max={100} height="8px" />
          </div>
        </Box>
        <style>{`
          .indeterminate-progress-container .progress-indicator {
            width: 30% !important;
            animation: indeterminate-progress 1.5s infinite ease-in-out;
            transform-origin: 0% 50%;
          }
          
          @keyframes indeterminate-progress {
            0% {
              transform: translateX(-100%);
            }
            100% {
              transform: translateX(400%);
            }
          }
        `}</style>
      </Flex>
    );
  },
};

// Progress bar with label
export const WithLabel: Story = {
  render: () => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [value, setValue] = useState(42);

    return (
      <Flex direction="column" gap="4" style={{ width: "300px" }}>
        <Box>
          <Flex justify="between" style={{ marginBottom: "4px" }}>
            <Text size="2">Downloading...</Text>
            <Text size="2" weight="medium">
              {value}%
            </Text>
          </Flex>
          <Progress
            value={value}
            max={100}
            height="8px"
            color="ds-color-accent"
          />
        </Box>
        <Flex gap="2">
          <Button onClick={() => setValue(Math.max(0, value - 10))}>
            -10%
          </Button>
          <Button onClick={() => setValue(Math.min(100, value + 10))}>
            +10%
          </Button>
        </Flex>
      </Flex>
    );
  },
};

import { Box } from "@radix-ui/themes";
import { cn } from "app/utils/style";
import { forwardRef } from "react";

export interface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * The value of the progress indicator.
   * @default 0
   */
  value?: number;

  /**
   * The maximum value.
   * @default 100
   */
  max?: number;

  /**
   * The color of the progress indicator.
   * @default "ds-color-neutral"
   */
  color?: string;

  /**
   * The height of the progress bar.
   * @default "8px"
   */
  height?: string;
}

const Progress = forwardRef<HTMLDivElement, ProgressProps>(
  (
    {
      className,
      value = 0,
      max = 100,
      color = "ds-color-neutral",
      height = "8px",
      ...props
    },
    ref,
  ) => {
    // Calculate the percentage
    const percentage = Math.min(Math.max(0, (value / max) * 100), 100);

    return (
      <Box
        ref={ref}
        role="progressbar"
        aria-valuemin={0}
        aria-valuemax={max}
        aria-valuenow={value}
        className={cn("progress-container", className)}
        style={{
          position: "relative",
          height,
          backgroundColor: "var(--ds-color-neutral-4)",
          borderRadius: "4px",
          overflow: "hidden",
        }}
        {...props}
      >
        <Box
          className="progress-indicator"
          style={{
            position: "absolute",
            left: 0,
            top: 0,
            height: "100%",
            width: `${percentage}%`,
            backgroundColor: `var(--${color}-9)`,
            borderRadius: "4px",
            transition: "width 0.2s ease-in-out",
          }}
        />
      </Box>
    );
  },
);

Progress.displayName = "Progress";

export { Progress };

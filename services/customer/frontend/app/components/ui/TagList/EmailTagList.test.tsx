import { render, screen, fireEvent } from "@testing-library/react";
import { EmailTagList } from "./EmailTagList";
import { vi, describe, test, expect, beforeEach } from "vitest";

// TODO(d<PERSON><PERSON><PERSON>) fix these tests
describe.skip("EmailTagList component", () => {
  const mockOnTagsChange = vi.fn();
  const initialTags = [
    { id: "1", text: "<EMAIL>" },
    { id: "2", text: "<EMAIL>" },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock randomUUID for consistent test results
    vi.spyOn(crypto, "randomUUID").mockReturnValue(
      "00000000-0000-0000-0000-000000000000",
    );
  });

  test("renders email tags correctly", () => {
    render(<EmailTagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
  });

  test("validates and adds valid email", () => {
    render(<EmailTagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const input = screen.getByTestId("tag-input");
    input.textContent = "<EMAIL>";
    fireEvent.input(input);
    fireEvent.keyDown(input, { key: "Enter" });

    expect(mockOnTagsChange).toHaveBeenCalledWith([
      ...initialTags,
      { id: "00000000-0000-0000-0000-000000000000", text: "<EMAIL>" },
    ]);
  });

  test("rejects invalid email", () => {
    render(<EmailTagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const input = screen.getByTestId("tag-input");
    input.textContent = "not-an-email";
    fireEvent.input(input);
    fireEvent.keyDown(input, { key: "Enter" });

    expect(mockOnTagsChange).not.toHaveBeenCalled();
  });

  test("accepts custom email validator", () => {
    const customValidator = vi.fn().mockImplementation((email) => {
      return email.endsWith("@company.com");
    });

    render(
      <EmailTagList
        tags={initialTags}
        onTagsChange={mockOnTagsChange}
        emailValidator={customValidator}
      />,
    );

    // Should reject non-company email
    const input = screen.getByTestId("tag-input");
    input.textContent = "<EMAIL>";
    fireEvent.input(input);
    fireEvent.keyDown(input, { key: "Enter" });
    expect(mockOnTagsChange).not.toHaveBeenCalled();
    expect(customValidator).toHaveBeenCalledWith("<EMAIL>");

    // Should accept company email
    input.textContent = "<EMAIL>";
    fireEvent.input(input);
    fireEvent.keyDown(input, { key: "Enter" });
    expect(mockOnTagsChange).toHaveBeenCalled();
  });

  test("handles pasted emails with delimiters", () => {
    render(<EmailTagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const input = screen.getByTestId("tag-input");
    const pasteEvent = new Event("paste", { bubbles: true });
    Object.defineProperty(pasteEvent, "clipboardData", {
      value: {
        getData: () => "<EMAIL>, <EMAIL>; <EMAIL>",
      },
    });

    input.dispatchEvent(pasteEvent);
    expect(mockOnTagsChange).toHaveBeenCalledTimes(3);
  });
});

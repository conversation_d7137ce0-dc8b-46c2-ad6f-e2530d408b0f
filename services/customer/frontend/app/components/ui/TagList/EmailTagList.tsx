import { useState } from "react";
import { TagList, type ValidationResult, type TagList<PERSON><PERSON> } from "./TagList";
import isEmail from "email-validator";
import { composeFns } from "app/utils/function";

export type EmailValidationResult<R extends string = never> = ValidationResult<
  "invalid-email" | R
>;

export type EmailTagListProps<R extends string = never> = Omit<
  TagListProps<R | "invalid-email">,
  "validator" | "onValidationError"
> & {
  /**
   * Optional custom email validator function
   * If not provided, uses email-validator package
   */
  emailValidator?: (email: string) => EmailValidationResult<R>;
  /**
   * Optional callback for when email validation fails
   * Receives an array of failed validation results
   */
  onValidationError?: (failedValidation: EmailValidationResult<R>[]) => void;
};

export function EmailTagList<R extends string = never>({
  emailValidator,
  delimiters = [" ", ",", ";", "\n"],
  onInputChange,
  onTagsChange,
  onValidationError,
  ...tagListProps
}: EmailTagListProps<R>) {
  const [validationFailures, setValidationFailures] = useState<
    EmailValidationResult<R>[] | null
  >(null);
  const clearValidationFailures = () => setValidationFailures(null);
  function validateEmailTag(email: string): EmailValidationResult<R> {
    if (!isEmail.validate(email)) {
      return {
        isValid: false,
        reason: "invalid-email",
        text: email,
      } as EmailValidationResult<R>;
    }
    const customValidationResult = emailValidator?.(email);
    if (customValidationResult && !customValidationResult.isValid) {
      return customValidationResult;
    }
    return { isValid: true, text: email } as EmailValidationResult<R>;
  }

  return (
    <div>
      <TagList<R | "invalid-email">
        {...tagListProps}
        onTagsChange={composeFns(clearValidationFailures, onTagsChange)}
        onInputChange={composeFns(clearValidationFailures, onInputChange)}
        validator={validateEmailTag}
        placeholder={
          tagListProps.placeholder || "Type email and press Enter..."
        }
        delimiters={delimiters}
        onValidationError={composeFns(setValidationFailures, onValidationError)}
      />
      <style scoped>{`
        :scope {
          --input-text-color: var(
            ${
              validationFailures
                ? "--ds-color-error-11"
                : "--ds-color-neutral-11"
            }
          );
          display: flex;
          flex-direction: column;
          gap: var(--ds-spacing-2);
        }
      `}</style>
    </div>
  );
}

export default EmailTagList;

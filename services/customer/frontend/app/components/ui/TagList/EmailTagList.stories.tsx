import type { <PERSON>a, StoryObj } from "@storybook/react";
import { EmailTagList } from "./EmailTagList";
import { useState } from "react";
import { Flex } from "@radix-ui/themes";

const meta = {
  title: "UI/TagList/EmailTagList",
  component: EmailTagList,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <Flex style={{ width: "500px" }}>
        <Story />
      </Flex>
    ),
  ],
} satisfies Meta<typeof EmailTagList>;

export default meta;
type Story = StoryObj<typeof meta>;

// Interactive example with state
const InteractiveEmailTagList = () => {
  const [tags, setTags] = useState([
    { id: "1", text: "<EMAIL>" },
    { id: "2", text: "<EMAIL>" },
  ]);
  return (
    <EmailTagList
      tags={tags}
      onTagsChange={setTags}
      placeholder="Add email addresses..."
    />
  );
};

export const Interactive = {
  render: () => <InteractiveEmailTagList />,
};

export const Default: Story = {
  args: {
    tags: [
      { id: "1", text: "<EMAIL>" },
      { id: "2", text: "<EMAIL>" },
    ],
    onTagsChange: (newTags: { id: string; text: string }[]) => {
      console.info("Tags changed:", newTags);
    },
  },
};

export const Empty: Story = {
  args: {
    tags: [],
    onTagsChange: () => {},
    placeholder: "Enter email addresses...",
  },
};

export const Disabled: Story = {
  args: {
    tags: [
      { id: "1", text: "<EMAIL>" },
      { id: "2", text: "<EMAIL>" },
    ],
    onTagsChange: () => {},
    disabled: true,
  },
};

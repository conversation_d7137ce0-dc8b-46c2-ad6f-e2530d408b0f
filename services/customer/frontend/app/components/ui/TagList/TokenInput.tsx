import {
  type ClipboardEvent,
  type FormEvent,
  forwardRef,
  type KeyboardEvent,
  useLayoutEffect,
  useRef,
} from "react";
import { createDelimiterUtils, type Delimiters } from "./utils";
import { combineRefs } from "app/utils/react";
import { cn, typography } from "app/utils/style";
import { addEventListener } from "app/utils/extra";
import type { Nullish } from "@augment-internal/ts-utils/type";

export type TokenInputProps = {
  className?: string;
  "data-testid"?: string;
  delimiters: Delimiters | string | string[];
  onAddTokens?: (tokens: string[]) => [accepted: string[], rejected: string[]];
  onValueChange?: (value: string) => void;
  onKeyDown?: (e: KeyboardEvent<HTMLDivElement>) => void;
  placeholder?: string;
  value?: string;
};

export const TokenInput = forwardRef<HTMLDivElement, TokenInputProps>(
  function ContentEditable(
    props,
    ref: React.ForwardedRef<HTMLDivElement> | Nullish,
  ) {
    const {
      className,
      "data-testid": dataTestId = "token-input",
      delimiters,
      onAddTokens,
      onValueChange,
      onKeyDown,
      placeholder,
      value,
    } = props;

    // Sync input value with external changes
    useLayoutEffect(() => {
      if (value !== undefined) {
        if (inputRef.current && value !== inputRef.current.textContent) {
          inputRef.current.textContent = value;
          // move cursor to end
          const selection = window.getSelection();
          if (selection) {
            const range = document.createRange();
            range.selectNodeContents(inputRef.current);
            range.collapse(false);
            selection.removeAllRanges();
            selection.addRange(range);
          }
        }
      }
    }, [value]);

    const inputRef = combineRefs(useRef<HTMLDivElement>(null), ref);

    // Remove <br> tag that gets added when the placeholder is shown
    useLayoutEffect(() => {
      if (inputRef.current) {
        return addEventListener(
          inputRef.current,
          "input",
          () => {
            if (inputRef.current?.innerHTML === "<br>") {
              inputRef.current.innerHTML = "";
            }
          },
          true,
        );
      }
    }, [inputRef.current]);

    const delimiterUtils = createDelimiterUtils(delimiters);

    function handleKeyDown(e: KeyboardEvent<HTMLDivElement>) {
      if (e.key === "Enter") {
        e.preventDefault();
        onAddTokens?.(getCurrentTokens(inputRef, delimiterUtils));
        return;
      }
      onKeyDown?.(e);
    }

    function handlePaste(e: ClipboardEvent<HTMLDivElement>) {
      e.preventDefault();

      const pastedText = e.clipboardData.getData("text");
      const selection = window.getSelection();
      if (!selection?.rangeCount) return;

      const range = selection.getRangeAt(0);
      range.deleteContents();

      const textNode = document.createTextNode(pastedText);
      range.insertNode(textNode);
      onValueChange?.(getCurrentText(inputRef));
      if (!delimiterUtils.test(pastedText)) return;
      if (!onAddTokens) return;
      const tagNames = getCurrentTokens(inputRef, delimiterUtils);
      const [accepted] = onAddTokens(tagNames);
      if (accepted.length) {
        // Move the caret to the end of the new text content
        const inputEl = inputRef.current!;
        const newRange = document.createRange();
        newRange.selectNodeContents(inputEl);
        newRange.collapse(false);
        selection.removeAllRanges();
        selection.addRange(newRange);
      }
    }

    function handleInputChange(e: FormEvent<HTMLDivElement>) {
      const newValue = e.currentTarget.textContent || "";
      onValueChange?.(newValue);

      // Check if the last character is a delimiter
      if (newValue.length > 0) {
        const lastChar = newValue.charAt(newValue.length - 1);
        // Replace &nbsp; with space
        const lastCharCode = lastChar.charCodeAt(0) === 160 ? " " : lastChar;
        if (delimiterUtils.has(lastCharCode)) {
          // Remove the delimiter and add the tag
          const tagText = newValue.slice(0, -1);
          if (tagText.length) {
            onAddTokens?.(getCurrentTokens(inputRef, delimiterUtils));
          }
        }
      } else {
        onAddTokens?.([]);
      }
    }

    return (
      <div>
        <div
          ref={inputRef}
          contentEditable
          role="textbox"
          tabIndex={0}
          aria-multiline="true"
          onInput={handleInputChange}
          onKeyDown={handleKeyDown}
          onPaste={handlePaste}
          className={cn("input", className)}
          data-placeholder={placeholder}
          data-testid={dataTestId}
        />
        <style scoped>{`
          :scope {
            flex-grow: 1;
          }

          .input {
            min-width: var(--ds-spacing-14);
            display: block;
            padding: 4px 0;
            outline: none;
            ${typography.text3.regular}
            flex: 1;
            max-height: var(--ds-spacing-4);
            min-height: fit-content;
            white-space: nowrap;
            display: flex;
            color: var(--input-text-color, --ds-color-neutral-11);
          }

          .input::before {
            content: none;
            color: var(--ds-color-neutral-a11);
            opacity: 0.5;
            pointer-events: none;
          }

          .input:empty::before {
            content: attr(data-placeholder);
          }
        `}</style>
      </div>
    );
  },
);

function getCurrentText(inputRef: React.RefObject<HTMLDivElement>) {
  return inputRef.current?.textContent ?? "";
}

function getCurrentTokens(
  inputRef: React.RefObject<HTMLDivElement>,
  delimiterUtils: Delimiters,
) {
  return getCurrentText(inputRef).split(delimiterUtils.regex).filter(Boolean);
}

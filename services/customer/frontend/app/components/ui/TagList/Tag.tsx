import { forwardRef, type ReactNode, useState } from "react";
import { IconBadge, type IconBadgeProps } from "../Badge/IconBadge";
import { Cross1Icon } from "@radix-ui/react-icons";
import { cn } from "app/utils/style";
import { Button } from "@radix-ui/themes";
import { Enabled } from "../Enabled";

export type TagProps = {
  children: ReactNode;
  onDismiss?: () => void;
  isDismissed?: boolean;
} & IconBadgeProps;

export const Tag = forwardRef<HTMLDivElement, TagProps>(
  function Tag(props, ref) {
    const { children, onDismiss, isDismissed, className, ...rest } = props;
    const [isRemoveHovered, setIsRemoveHovered] = useState(false);

    if (isDismissed) return null;

    return (
      <IconBadge
        {...rest}
        className={cn("tag", className, { "remove-hovered": isRemoveHovered })}
        ref={ref}
      >
        {children}
        <Enabled enabled={Boolean(onDismiss)}>
          <Button
            asChild
            size="1"
            variant="ghost"
            color="gray"
            onClick={onDismiss}
            className="remove-icon"
            onMouseEnter={() => setIsRemoveHovered(true)}
            onMouseLeave={() => setIsRemoveHovered(false)}
          >
            <Cross1Icon />
          </Button>
        </Enabled>
        <style scoped>{`
        :scope.tag {
          --tag-bg-color: inherit;
          --tag-bg-color-hover: var(--ds-color-primary-2);
          --tag-bg-color-active: var(--ds-color-primary-3);
          --tag-selected-bg-color: var(--ds-color-accent-3);
          --tag-remove-hover-bg-color: var(--ds-color-error-3);

          transition: background-color 0.2s ease;
          padding: var(--ds-spacing-2) var(--ds-spacing-3);
          gap: var(--ds-spacing-2);
          border-radius: var(--ds-radius-2);

          .remove-icon {
            display: flex;
            align-items: center;
            padding: var(--ds-spacing-0_5);
            border-radius: 50%;
            width: var(--ds-spacing-3);
            height: var(--ds-spacing-3);
            margin: 0;
            justify-content: center;
            transition: background-color 0.2s ease;
            cursor: pointer;
          }

          .remove-icon:hover {
            background-color: var(--ds-color-error-5);
          }
          
          &:hover {
            background-color: var(--tag-bg-color-hover);
          }
          &:active {
            background-color: var(--tag-bg-color-active);
          }
          &.remove-hovered {
            background-color: var(--tag-remove-hover-bg-color);
          }
          &.selected {
            outline: 2px solid var(--ds-color-accent-8);
            background-color: var(--tag-selected-bg-color);
          }
          &.selected:hover {
            background-color: var(--tag-selected-bg-color);
            opacity: 0.9;
          }
          &.selected:active {
            background-color: var(--ds-color-accent-5);
          }
        }
      `}</style>
      </IconBadge>
    );
  },
);

export default Tag;

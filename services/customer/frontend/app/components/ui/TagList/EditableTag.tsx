import { cn } from "app/utils/style";
import Tag, { type TagProps } from "./Tag";
import {
  type KeyboardEvent,
  type ChangeEvent,
  useEffect,
  useState,
  useRef,
  forwardRef,
} from "react";
import { composeFns } from "app/utils/function";
import { combineRefs } from "app/utils/react";

export type EditableTagProps = {
  /** Controlled text value */
  value: string;
  /** Called when editing finishes and the value changed */
  onChange: (value: string) => void;
  /** Called on every keystroke in controlled mode */
  onValueChange?: (value: string) => void;
  /** External control of editing mode */
  isEditing?: boolean;
  /**
   * Called when editing starts or stops
   * return `false` to block the edit mode change
   */
  onEditChange?: (isEditing: boolean, value: string) => void | boolean;
  className?: string;
  isSelected?: boolean;
} & Omit<TagProps, "children" | "onChange">;

export const EditableTag = forwardRef<HTMLDivElement, EditableTagProps>(
  function EditableTag(props, ref) {
    const internalRef = useRef<HTMLDivElement>(null);
    const [internalIsEditing, setInternalIsEditing] = useState(false);
    const {
      value: externalValue,
      onChange,
      onValueChange,
      isEditing = internalIsEditing,
      onEditChange,
      isSelected,
      ...rest
    } = props;

    const [internalValue, setInternalValue] = useState(externalValue);
    const isControlled = typeof onValueChange === "function";
    const value = isControlled ? externalValue : internalValue;
    const previousValue = useRef<string>("");

    // Keep internal value in sync with external value
    useEffect(() => {
      if (isControlled) {
        setInternalValue(externalValue);
      }
    }, [externalValue, isControlled]);

    // Enter (true) and Exit (false) editing mode
    function setEditing(next: boolean) {
      if (next === isEditing) return;
      if (next) {
        // record the value before editing starts so we can revert later
        previousValue.current = value;
      }
      const shouldEditChange = onEditChange?.(next, value);
      if (shouldEditChange === false) return;
      setInternalIsEditing(next);
    }

    // Start editing on input click
    function handleClick() {
      if (!isEditing) {
        setEditing(true);
      }
    }

    // Notify external when a new value has been committed
    // return `false` to block exiting edit mode
    function handleChange(next: string) {
      setInternalValue(next);
      if (next !== previousValue.current) {
        onChange?.(next);
      }
    }

    function finishEditing() {
      handleChange(value);
      if (isEditing) {
        setEditing(false);
        // Focus the tag element after exiting edit mode
        setTimeout(() => {
          internalRef.current?.focus();
        }, 0);
      }
    }

    function cancelEditing() {
      // revert edits
      handleChange(previousValue.current);
      if (isEditing) {
        setEditing(false);
        // Focus the tag element after exiting edit mode
        setTimeout(() => {
          internalRef.current?.focus();
        }, 0);
      }
    }

    function handleBlur() {
      finishEditing();
    }

    function handleInputChange(e: ChangeEvent<HTMLInputElement>) {
      const next = e.target.value;
      onValueChange?.(next);
      setInternalValue(next);
    }

    function handleKeyDown(e: KeyboardEvent<HTMLInputElement>) {
      if (e.code === "Enter") {
        e.preventDefault();
        isEditing ? finishEditing() : setEditing(true);
        return;
      }
      if (e.code === "Escape") {
        e.preventDefault();
        isEditing ? cancelEditing() : setEditing(false);
        return;
      }
    }

    return (
      <Tag
        {...rest}
        ref={combineRefs(internalRef, ref)}
        className={cn(rest.className, "editable-tag", { selected: isSelected })}
        onKeyDown={composeFns(rest.onKeyDown, handleKeyDown)}
        tabIndex={rest.tabIndex ?? 0}
      >
        {isEditing ? (
          <input
            type="text"
            value={isControlled ? value : internalValue}
            onChange={handleInputChange}
            onBlur={handleBlur}
            // eslint-disable-next-line jsx-a11y/no-autofocus
            autoFocus
            className="tag-input"
          />
        ) : (
          <>
            {/* eslint-disable-next-line jsx-a11y/click-events-have-key-events,jsx-a11y/interactive-supports-focus */}
            <div role="button" onClick={handleClick} className="tag-text">
              {value}
            </div>
          </>
        )}
        <style scoped>{`
        :scope.editable-tag {
          padding: var(--ds-spacing-2) var(--ds-spacing-3);
          gap: var(--ds-spacing-2);
          border-radius: var(--ds-radius-2);
          cursor: pointer;

          .tag-input {
            background: var(--ds-color-surface-1);
            outline: none;
            flex: 1;
            white-space: nowrap;
            color: var(--input-text-color, --ds-color-neutral-11);
          }

          .tag-text {
            user-select: none;
            cursor: text;
          }
        }
      `}</style>
      </Tag>
    );
  },
);
export default EditableTag;

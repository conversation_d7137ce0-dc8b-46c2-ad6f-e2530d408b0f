import { isFunction } from "app/utils/function";
import { toSplit } from "app/utils/string";

export type Delimiters = {
  prime: string;
  set: Set<string>;
  regex: RegExp;
  has: (char: string) => boolean;
  test: (text: string) => boolean;
};

export function isDelimiters(
  delimiterStringOrArray: string | string[] | Delimiters,
): delimiterStringOrArray is Delimiters {
  return (
    typeof delimiterStringOrArray === "object" &&
    "prime" in delimiterStringOrArray &&
    "set" in delimiterStringOrArray &&
    "regex" in delimiterStringOrArray &&
    "has" in delimiterStringOrArray &&
    "test" in delimiterStringOrArray
  );
}

export function createDelimiterUtils(
  delimiterStringOrArray: string | string[] | Delimiters,
): Delimiters {
  if (isDelimiters(delimiterStringOrArray)) {
    return delimiterStringOrArray;
  }
  const delimiters = toSplit("")(delimiterStringOrArray);
  return {
    prime: delimiters[0],
    set: new Set(delimiters),
    get regex() {
      return new RegExp(`[${[...delimiters].join("")}]`);
    },
    has: (char: string) => delimiters.includes(char),
    test: (text: string) => delimiters.some((d) => text.includes(d)),
  };
}

export type Tag = {
  id: string;
  text: string;
};

export type ValidationResult =
  | { isValid: true; text: string }
  | {
      isValid: false;
      reason: "empty" | "duplicate" | "invalid" | string;
      text: string;
    };

export function createTag(text: string): Tag {
  return { id: crypto.randomUUID(), text };
}

export function createTagValidator<VR extends ValidationResult>(
  currentTags: Set<string>,
  validator?: (text: string) => VR,
) {
  return <VR extends ValidationResult>(
    text: string,
    tags: Set<string> = currentTags,
  ): VR => {
    const trimmedText = text.trim();

    // Check if empty
    if (!trimmedText) {
      return { isValid: false, reason: "empty", text: trimmedText } as VR;
    }

    // Check if already exists
    if (tags.has(trimmedText)) {
      return { isValid: false, reason: "duplicate", text: trimmedText } as VR;
    }

    // Check custom validator
    const validationResult = isFunction(validator)
      ? validator(trimmedText)
      : null;
    if (validationResult && !validationResult.isValid) {
      return validationResult as unknown as VR;
    }

    return { isValid: true, text: trimmedText } as VR;
  };
}

export function toTagSet(tags: Tag[]): Set<string> {
  return new Set(tags.map((t) => t.text));
}

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Tag } from "./Tag";
import badgeMeta from "../Badge/Badge.stories";
import { useState } from "react";
import { Button } from "@radix-ui/themes";
import { cn } from "app/utils/style";

const badgeColors = badgeMeta.argTypes.color.options;
const badgeSizes = badgeMeta.argTypes.size.options;
const badgeVariants = badgeMeta.argTypes.variant.options;

const meta = {
  title: "UI/TagList/Tag",
  component: Tag,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    color: {
      description: "The visual style of the badge",
      control: { type: "select" },
      options: badgeColors,
    },
    size: {
      description: "The size of the badge",
      control: { type: "select" },
      options: badgeSizes,
    },
    variant: {
      description: "The variant of the badge",
      control: { type: "select" },
      options: badgeVariants,
    },
    tooltip: {
      description: "Optional tooltip text",
      control: { type: "text" },
    },
    onDismiss: {
      description: "Callback when the badge is dismissed",
      action: "dismissed",
    },
  },
  // Default Values
  args: {
    children: "Dismissable Badge",
    color: "default",
    size: "medium",
    tooltip: "Click the X to dismiss",
  },
} satisfies Meta<typeof Tag>;

export default meta;

type Story = StoryObj<typeof meta>;
type StoryArgs = Story["args"];

const badges = [
  { id: 1, text: "Badge 1" },
  { id: 2, text: "Badge 2" },
  { id: 3, text: "Badge 3" },
];

// Interactive story with state
export function Default(props: StoryArgs) {
  const [hidden, setHidden] = useState<Set<number>>(new Set());

  const handleDismiss = (id: number) => setHidden((v) => new Set([...v, id]));

  const resetBadges = () => setHidden(new Set());

  return (
    <div className="container">
      <div className="badges">
        {hidden.size === badges.length ? (
          <Button onClick={resetBadges}>Reset Badges</Button>
        ) : (
          badges.map((badge) => (
            <Tag
              {...props}
              key={badge.id}
              onDismiss={() => handleDismiss(badge.id)}
              className={cn({ hidden: hidden.has(badge.id) })}
            >
              {badge.text}
            </Tag>
          ))
        )}
      </div>
      <style scoped>{`
        :scope.container {
          display: flex;
          flex-direction: column;
          gap: var(--ds-spacing-3);
          align-items: center;
          justify-content: center;
          height: 100%;
        }

        .badges {
          display: flex;
          flex-wrap: wrap;
          gap: var(--ds-spacing-2);

          .hidden {
            visibility: hidden;
          }
        }
      `}</style>
    </div>
  );
}

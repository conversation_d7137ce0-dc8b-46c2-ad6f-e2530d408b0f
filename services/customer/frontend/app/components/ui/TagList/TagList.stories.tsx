import type { Meta } from "@storybook/react";
import { TagList } from "./TagList";
import { useState } from "react";
import { Flex } from "@radix-ui/themes";

const meta = {
  title: "UI/TagList/TagList",
  component: TagList,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <Flex style={{ width: "500px" }}>
        <Story />
      </Flex>
    ),
  ],
} satisfies Meta<typeof TagList>;

export default meta;

// Interactive example with state
const InteractiveTagList = () => {
  const [tags, setTags] = useState([
    { id: "1", text: "React" },
    { id: "2", text: "TypeScript" },
    { id: "3", text: "Radix UI" },
  ]);

  return (
    <TagList
      tags={tags}
      onTagsChange={setTags}
      placeholder="Add technologies..."
    />
  );
};

export const Default = {
  render: () => <InteractiveTagList />,
};

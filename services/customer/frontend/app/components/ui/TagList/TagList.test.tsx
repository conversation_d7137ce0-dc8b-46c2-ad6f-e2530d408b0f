import { render, screen, fireEvent } from "@testing-library/react";
import { TagList } from "./TagList";
import { vi, describe, test, expect, beforeEach } from "vitest";

// TODO(d<PERSON><PERSON><PERSON>) fix these tests
describe.skip("TagList component", () => {
  const mockOnTagsChange = vi.fn();
  const initialTags = [
    { id: "1", text: "React" },
    { id: "2", text: "TypeScript" },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock randomUUID for consistent test results
    vi.spyOn(crypto, "randomUUID").mockReturnValue(
      "00000000-0000-0000-0000-000000000000",
    );
  });

  test("renders tags correctly", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    expect(screen.getByText("React")).toBeInTheDocument();
    expect(screen.getByText("TypeScript")).toBeInTheDocument();
    expect(screen.getByTestId("tag-input")).toBeInTheDocument();
  });

  test("adds a new tag when pressing Enter", async () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const input = screen.getByTestId("tag-input");
    input.textContent = "JavaScript";
    fireEvent.input(input);
    fireEvent.keyDown(input, { key: "Enter" });

    expect(mockOnTagsChange).toHaveBeenCalledWith([
      ...initialTags,
      { id: "00000000-0000-0000-0000-000000000000", text: "JavaScript" },
    ]);
  });

  test("doesn't add empty tags", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const input = screen.getByTestId("tag-input");
    input.textContent = "   ";
    fireEvent.input(input);
    fireEvent.keyDown(input, { key: "Enter" });

    expect(mockOnTagsChange).not.toHaveBeenCalled();
  });

  test("doesn't add duplicate tags", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const input = screen.getByTestId("tag-input");
    input.textContent = "React";
    fireEvent.input(input);
    fireEvent.keyDown(input, { key: "Enter" });

    expect(mockOnTagsChange).not.toHaveBeenCalled();
  });

  test("removes a tag when clicking the remove icon", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const removeButton = screen.getByTestId("remove-tag-1");
    fireEvent.click(removeButton);

    expect(mockOnTagsChange).toHaveBeenCalledWith([initialTags[1]]);
  });

  test("removes the last tag when pressing Backspace with empty input", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const input = screen.getByTestId("tag-input");
    fireEvent.keyDown(input, { key: "Backspace" });

    expect(mockOnTagsChange).toHaveBeenCalledWith([initialTags[0]]);
  });

  test("selects a tag when clicking on it", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const tag = screen.getByTestId("tag-1");
    fireEvent.click(tag);

    expect(tag).toHaveClass("selected");
  });

  test("deselects a tag when clicking on it again", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const tag = screen.getByTestId("tag-1");
    fireEvent.click(tag);
    fireEvent.click(tag);

    expect(tag).not.toHaveClass("selected");
  });

  test("enters edit mode when clicking on a selected tag", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const tag = screen.getByTestId("tag-1");
    fireEvent.click(tag);
    fireEvent.click(tag);

    const editInput = screen.getByTestId("edit-tag-1");
    expect(editInput).toBeInTheDocument();
    expect(editInput).toHaveValue("React");
  });

  test("saves tag edit when pressing Enter", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const tag = screen.getByTestId("tag-1");
    fireEvent.click(tag);
    fireEvent.click(tag);

    const editInput = screen.getByTestId("edit-tag-1");
    fireEvent.change(editInput, { target: { value: "React Updated" } });
    fireEvent.keyDown(editInput, { key: "Enter" });

    expect(mockOnTagsChange).toHaveBeenCalledWith([
      { id: "1", text: "React Updated" },
      initialTags[1],
    ]);
  });

  test("cancels tag edit when pressing Escape", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const tag = screen.getByTestId("tag-1");
    fireEvent.click(tag);
    fireEvent.click(tag);

    const editInput = screen.getByTestId("edit-tag-1");
    fireEvent.change(editInput, { target: { value: "React Updated" } });
    fireEvent.keyDown(editInput, { key: "Escape" });

    expect(mockOnTagsChange).not.toHaveBeenCalled();
    expect(screen.getByText("React")).toBeInTheDocument();
  });

  test("multi-selects tags with Ctrl/Cmd key", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const tag1 = screen.getByTestId("tag-1");
    const tag2 = screen.getByTestId("tag-2");

    // Use ctrlKey for Windows/Linux (default in tests)
    fireEvent.click(tag1, { ctrlKey: true });
    fireEvent.click(tag2, { ctrlKey: true });

    expect(tag1).toHaveClass("selected");
    expect(tag2).toHaveClass("selected");
  });

  test("removes multiple selected tags when pressing Delete", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const tag1 = screen.getByTestId("tag-1");
    const tag2 = screen.getByTestId("tag-2");

    // Select both tags
    fireEvent.click(tag1, { ctrlKey: true });
    fireEvent.click(tag2, { ctrlKey: true });

    // Press Delete
    const container = screen.getByTestId("tag-list-container");
    fireEvent.keyDown(container, { key: "Delete" });

    expect(mockOnTagsChange).toHaveBeenCalledWith([]);
  });

  test("deselects tags when clicking on container", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const tag = screen.getByTestId("tag-1");
    fireEvent.click(tag);

    expect(tag).toHaveClass("selected");

    const container = screen.getByTestId("tag-list-container");
    fireEvent.click(container);

    expect(tag).not.toHaveClass("selected");
  });

  test("deselects tags when clicking on tagList", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const tag = screen.getByTestId("tag-1");
    fireEvent.click(tag);

    expect(tag).toHaveClass("selected");

    const tagList = screen.getByTestId("tag-list");
    fireEvent.click(tagList);

    expect(tag).not.toHaveClass("selected");
  });

  test("handles pasted text as a new tag", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const input = screen.getByTestId("tag-input");
    const pasteEvent = new Event("paste", { bubbles: true });
    Object.defineProperty(pasteEvent, "clipboardData", {
      value: {
        getData: () => "JavaScript",
      },
    });

    input.dispatchEvent(pasteEvent);

    expect(mockOnTagsChange).toHaveBeenCalledWith([
      ...initialTags,
      { id: "00000000-0000-0000-0000-000000000000", text: "JavaScript" },
    ]);
  });

  test("handles pasted text with delimiters as multiple tags", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    const input = screen.getByTestId("tag-input");
    const pasteEvent = new Event("paste", { bubbles: true });
    Object.defineProperty(pasteEvent, "clipboardData", {
      value: {
        getData: () => "JavaScript,Node.js",
      },
    });

    input.dispatchEvent(pasteEvent);

    expect(mockOnTagsChange).toHaveBeenCalledTimes(2);
  });

  test("doesn't render input when disabled", () => {
    render(
      <TagList
        tags={initialTags}
        onTagsChange={mockOnTagsChange}
        disabled={true}
      />,
    );

    expect(screen.queryByTestId("tag-input")).not.toBeInTheDocument();
  });

  test("doesn't show remove buttons when disabled", () => {
    render(
      <TagList
        tags={initialTags}
        onTagsChange={mockOnTagsChange}
        disabled={true}
      />,
    );

    expect(screen.queryByTestId("remove-tag-1")).not.toBeInTheDocument();
    expect(screen.queryByTestId("remove-tag-2")).not.toBeInTheDocument();
  });

  test("navigates between tags with arrow keys", () => {
    render(<TagList tags={initialTags} onTagsChange={mockOnTagsChange} />);

    // Select the last tag with left arrow from input
    const input = screen.getByTestId("tag-input");
    fireEvent.keyDown(input, { key: "ArrowLeft" });

    const tag2 = screen.getByTestId("tag-2");
    expect(tag2).toHaveClass("selected");

    // Navigate to the first tag with left arrow
    const container = screen.getByTestId("tag-list-container");
    fireEvent.keyDown(container, { key: "ArrowLeft" });

    const tag1 = screen.getByTestId("tag-1");
    expect(tag1).toHaveClass("selected");
    expect(tag2).not.toHaveClass("selected");

    // Navigate back to the second tag with right arrow
    fireEvent.keyDown(container, { key: "ArrowRight" });
    expect(tag1).not.toHaveClass("selected");
    expect(tag2).toHaveClass("selected");

    // Navigate to input with right arrow from last tag
    fireEvent.keyDown(container, { key: "ArrowRight" });
    expect(tag2).not.toHaveClass("selected");
    // Input should be focused (hard to test directly)
  });
});

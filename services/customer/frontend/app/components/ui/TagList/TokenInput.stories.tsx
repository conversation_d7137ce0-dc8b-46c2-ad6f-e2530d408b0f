import type { <PERSON>a, StoryObj } from "@storybook/react";
import { TokenInput } from "./TokenInput";
import { useState } from "react";
import { Badge } from "../Badge";

const meta = {
  title: "UI/TagList/TokenInput",
  component: TokenInput,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    delimiters: {
      control: "text",
    },
    placeholder: {
      control: "text",
    },
  },
  args: {
    delimiters: " ,",
    placeholder: "Type and press Enter to add tags...",
  },
} satisfies Meta<typeof TokenInput>;

export default meta;
type Story = StoryObj<typeof meta>;
type StoryArgs = Story["args"];

// Interactive example with state
function InteractiveTagInput(args: StoryArgs) {
  const [value, setValue] = useState("");
  const [tokens, setTokens] = useState<string[]>([]);

  const handleValueChange = (text: string) => {
    setValue(text);
  };

  const handleAddTokens = (newTokens: string[]): [string[], string[]] => {
    setTokens(newTokens);
    return [tokens, []];
  };

  return (
    <div className="container">
      <TokenInput
        {...args}
        delimiters={args?.delimiters ?? " , "}
        className="storied-token-input"
        onValueChange={handleValueChange}
        onAddTokens={handleAddTokens}
      />

      <pre className="info">
        <div>
          Current value: <span>{value}</span>
        </div>
        <div>
          Added tokens:{" "}
          <span className="tokens">
            {tokens.map((t, i) => (
              <Badge key={t + i} className="token">
                {t}
              </Badge>
            ))}
          </span>
        </div>
      </pre>

      <style scoped>{`
        :scope.container {
          display: flex;
          flex-direction: column;
          gap: var(--ds-spacing-3);
          width: 400px;
        }
        :scope .storied-token-input {
          width: 100%;
          border: 1px solid var(--ds-color-border-default);
          border-radius: var(--ds-radius-3);
          padding: var(--ds-spacing-3);
        }
        :scope .info {
          font-size: var(--ds-font-size-sm);
          color: var(--ds-color-text-muted);
          display: flex;
          flex-direction: column;
          gap: var(--ds-spacing-1);
        }
        :scope .tokens {
          display: inline-flex;
          flex-wrap: wrap;
          gap: var(--ds-spacing-1);
        }
        :scope .token {
          display: inline-block;
        }
      `}</style>
    </div>
  );
}

export const Interactive = {
  render: InteractiveTagInput,
};

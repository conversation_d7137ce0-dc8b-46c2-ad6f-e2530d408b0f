import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { EditableTag } from "./EditableTag";
import { useState } from "react";

const meta = {
  title: "UI/TagList/EditableTag",
  component: EditableTag,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof EditableTag>;

export default meta;
type Story = StoryObj<typeof meta>;
type StoryArgs = Story["args"];

export function Default(props: StoryArgs) {
  const [value, setValue] = useState("React");
  const [validationError, setHasValidationError] = useState<boolean>(false);

  function hasUppercase(text: string) {
    return text !== text.toLowerCase();
  }

  function handleOnChange(next: string) {
    setValue(next);
  }

  function handleEditChange(isEditing: boolean, value: string) {
    if (isEditing) return;
    if (!hasUppercase(value)) {
      setHasValidationError(true);
      // block exiting edit mode
      return false;
    }
    setHasValidationError(false);
  }

  return (
    <div className="container">
      <EditableTag
        {...props}
        className="storied-editable-tag"
        data-testid="storied-editable-tag"
        value={value}
        onChange={handleOnChange}
        onEditChange={handleEditChange}
        onDismiss={() => console.info("dismissed")}
      />
      {validationError && (
        <div className="validation-error">
          Validation failed: You must have at least one uppercase letter
        </div>
      )}
      <style scoped>{`
        :scope.container {
          display: flex;
          flex-direction: column;
          gap: 8px;
          align-items: center;
          justify-content: center;
          height: 100%;
        }

        :scope .validation-error {
          color: red;
          margin-top: 8px;
        }
      `}</style>
    </div>
  );
}

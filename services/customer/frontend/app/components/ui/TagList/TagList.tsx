import {
  useState,
  useRef,
  useEffect,
  type KeyboardEvent,
  type MouseEvent,
  type Ref,
} from "react";
import { addEventListener } from "app/utils/extra";
import { toSet } from "app/utils/collection";
import { EditableTag } from "./EditableTag";
import {
  createDelimiterUtils,
  createTag,
  createTagValidator,
  toTagSet,
} from "./utils";
import { TokenInput } from "./TokenInput";
import { combineRefs } from "app/utils/react";
import { isNonNullable } from "app/utils/guards";
import type { Prettify } from "@augment-internal/ts-utils/type";

// Detect platform
const isMac =
  typeof navigator !== "undefined" && /mac/i.test(navigator.platform);

export type Tag = {
  id: string;
  text: string;
};

type CoreReasons = "empty" | "duplicate" | "invalid";

export type ValidationResult<R extends string = never> =
  | { isValid: true; text: string }
  | Prettify<
      {
        [K in R | CoreReasons]: {
          isValid: false;
          reason: K;
          text: string;
        };
      }[R | CoreReasons]
    >;

export type TagListProps<R extends string = never> = {
  tags: Tag[];
  onTagsChange: (tags: Tag[]) => void;
  inputValue?: string | undefined;
  onInputChange?: (text: string, validationResult: ValidationResult<R>) => void;
  inputRef?: Ref<HTMLDivElement>;
  placeholder?: string;
  disabled?: boolean;
  delimiters?: string | string[];
  validator?: (text: string) => ValidationResult<R>;
  onValidationError?: (failedValidation: ValidationResult<R>[]) => void;
};

export function TagList<R extends string = never>(props: TagListProps<R>) {
  const {
    tags,
    onTagsChange,
    inputValue: externalInputValue,
    onInputChange,
    inputRef: externalInputRef,
    placeholder = "Type and press Enter to add tags...",
    disabled = false,
    delimiters: delimiterStringOrArray = [" ", ",", ";", "\n"],
    validator,
    onValidationError,
    ...rest
  } = props;

  const [selectedTagIds, setSelectedTagIds] = useState<Set<string>>(new Set());
  const [editingTagId, setEditingTagId] = useState<string | null>(null);
  const [editingTagText, setEditingTagText] = useState("");
  const inputRef = useRef<HTMLDivElement>(null);
  const tagRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const containerRef = useRef<HTMLDivElement>(null);
  const delimiterUtils = createDelimiterUtils(delimiterStringOrArray);
  const [internalInputValue, setInputValue] = useState(externalInputValue);
  const isInputControlled =
    isNonNullable(externalInputValue) && isNonNullable(onInputChange);
  const inputValue = isInputControlled
    ? externalInputValue
    : internalInputValue;

  function isTagSelected(id: string) {
    return selectedTagIds.has(id);
  }

  function getFirstSelectedTag() {
    return selectedTagIds.size > 0 ? Array.from(selectedTagIds)[0] : null;
  }

  const validateTag = createTagValidator<ValidationResult<R>>(
    toTagSet(tags),
    validator,
  );

  function handleInputChange(text: string) {
    onInputChange?.(text, validateTag(text));
    setInputValue(text);
  }

  function addTags(
    tagNames: string[],
  ): [accepted: string[], rejected: string[]] {
    const accepted: string[] = [];
    const rejected: string[] = [];
    const failedValidation: ValidationResult<R>[] = [];
    const newTags: Set<string> = toTagSet(tags);

    for (const text of tagNames) {
      const trimmedText = text.trim();
      const validationResult = validateTag<ValidationResult<R>>(
        trimmedText,
        newTags,
      );
      if (!validationResult.isValid) {
        if (!["duplicate", "empty"].includes(validationResult.reason)) {
          failedValidation.push(validationResult);
        }
        rejected.push(text);
      } else {
        accepted.push(trimmedText);
        newTags.add(trimmedText);
      }
    }

    if (accepted.length > 0) {
      onTagsChange(Array.from(newTags, createTag));
      handleInputChange("");
      inputRef.current!.textContent = rejected.join(delimiterUtils.prime);
    }
    if (failedValidation.length > 0) {
      onValidationError?.(failedValidation);
    }

    return [accepted, rejected];
  }

  function removeSelectedTags(selectedTagIds: Set<string> | string) {
    const idSet = toSet(selectedTagIds);
    if (idSet.size === 0) return;
    const newTags = tags.filter((tag) => !idSet.has(tag.id));
    onTagsChange(newTags);
    // After deletion, select the tag to the left
    // if the tag deleted was the first, select the tag to the right
    // if no other tags, select the input
    if (idSet.size === 1 && newTags.length > 0) {
      const idx = tags.findIndex((t) => t.id === idSet.values().next().value);
      // select the tag to the right
      setSelectedTagIds(new Set([newTags.at(idx - newTags.length || -1)!.id]));
      tagRefs.current[newTags[idx]?.id]?.focus();
    } else if (newTags.length > 0) {
      setSelectedTagIds(new Set([newTags.at(-1)!.id]));
      tagRefs.current[newTags.at(-1)!.id]?.focus();
    } else {
      setSelectedTagIds(new Set());
      inputRef.current?.focus();
    }
  }

  function handleTagClick(e: MouseEvent, tagId: string) {
    e.stopPropagation();
    const isModifierKeyPressed = isMac ? e.metaKey : e.ctrlKey;

    if (isModifierKeyPressed) {
      // Multi-select
      const next = new Set(selectedTagIds);
      next.has(tagId) ? next.delete(tagId) : next.add(tagId);
      setSelectedTagIds(next);
    } else {
      // Single select
      if (selectedTagIds.size === 1 && selectedTagIds.has(tagId)) {
        // When user clicks already-selected tag, enter edit mode
        const tag = tags.find((t) => t.id === tagId);
        if (tag) {
          setEditingTagId(tagId);
          setEditingTagText(tag.text);
          // ensure tag gets keyboard focus
          tagRefs.current[tagId]?.focus();
        }
      } else {
        setSelectedTagIds(new Set([tagId]));
        tagRefs.current[tagId]?.focus();
        inputRef.current?.blur();
      }
    }
  }

  function cancelTagEdit() {
    setEditingTagId(null);
    setEditingTagText("");
  }

  function exitEditTag(text = editingTagText, tags = props.tags) {
    if (editingTagId === null) return false;
    const tag = tags.find((t) => t.id === editingTagId);
    if (!tag) return false;
    const result = validateTag<ValidationResult<R>>(
      text,
      toTagSet(tags.filter((t) => t.id !== editingTagId)),
    );
    if (!result.isValid) {
      if (!["duplicate", "empty"].includes(result.reason)) {
        onValidationError?.([result]);
      }
      cancelTagEdit();
    } else {
      onTagsChange(
        tags.map((t) => (t.id === editingTagId ? { ...t, text } : t)),
      );
    }
    setEditingTagId(null);
    setEditingTagText("");
    return result.isValid;
  }

  function handleKeyDown(e: KeyboardEvent<HTMLDivElement>) {
    // If tags are selected, handle backspace/delete
    if (
      selectedTagIds.size > 0 &&
      (e.key === "Backspace" || e.key === "Delete")
    ) {
      e.preventDefault();
      removeSelectedTags(selectedTagIds);
      return;
    }

    if (e.key === "Backspace") {
      // Get current selection
      const sel = window.getSelection();
      if (sel && sel.rangeCount > 0) {
        const range = sel.getRangeAt(0);
        // Only select the last tag if cursor is at the beginning of input
        // and no text is selected
        if (
          range.collapsed &&
          range.startOffset === 0 &&
          tags.length > 0 &&
          selectedTagIds.size === 0
        ) {
          e.preventDefault();
          setSelectedTagIds(new Set([tags[tags.length - 1].id]));
          tagRefs.current[tags[tags.length - 1].id]?.focus();
        }
      }
      return;
    }

    if (e.key === "Escape") {
      setSelectedTagIds(new Set());
      return;
    }
  }

  function handleContainerKeyDown(e: KeyboardEvent<HTMLDivElement>) {
    // Skip container key handling if the input is focused
    if (e.key === "Tab") {
      const selectedId = getFirstSelectedTag();
      const idx = tags.findIndex((t) => t.id === selectedId);

      // If we're editing a tag, finish editing first
      if (editingTagId)
        exitEditTag(
          editingTagText,
          tags.filter((t) => t.id !== editingTagId),
        );

      if (e.shiftKey) {
        // if the inputRef is focused, focus the last tag
        if (inputRef.current === document.activeElement) {
          setSelectedTagIds(new Set([tags[tags.length - 1].id]));
          tagRefs.current[tags[tags.length - 1].id]?.focus();
          e.preventDefault();
          return;
        }
        // Shift+Tab: move selection to previous tag or to input if at first tag
        if (idx > 0) {
          setSelectedTagIds(new Set([tags[idx - 1].id]));
          tagRefs.current[tags[idx - 1].id]?.focus();
          e.preventDefault();
          return;
        }
        if (idx === 0) {
          setSelectedTagIds(new Set());
          return;
        }
      } else {
        // Tab: move selection to next tag or to input if at last tag
        if (inputRef.current === document.activeElement) {
          setSelectedTagIds(new Set());
          return;
        }
        if (idx < tags.length - 1) {
          setSelectedTagIds(new Set([tags[idx + 1].id]));
          tagRefs.current[tags[idx + 1].id]?.focus();
          e.preventDefault();
          return;
        }
        if (idx === tags.length - 1) {
          // At last tag, move focus back to input
          setSelectedTagIds(new Set());
          inputRef.current?.focus();
          e.preventDefault();
          return;
        }
      }
      return;
    }

    if (e.key === "ArrowLeft") {
      const selection = window.getSelection();
      if (e.target === inputRef.current && selection?.anchorOffset !== 0)
        return;
      e.preventDefault();
      const selectedId = getFirstSelectedTag();
      if (selectedId) {
        const idx = tags.findIndex((t) => t.id === selectedId);
        if (idx > 0) {
          setSelectedTagIds(new Set([tags[idx - 1].id]));
          tagRefs.current[tags[idx - 1].id]?.focus();
        }
        return;
      }
      if (tags.length > 0) {
        setSelectedTagIds(new Set([tags[tags.length - 1].id]));
        tagRefs.current[tags[tags.length - 1].id]?.focus();
      }
      return;
    }

    if (e.key === "ArrowRight") {
      if (e.target === inputRef.current) return;
      e.preventDefault();
      const selectedId = getFirstSelectedTag();
      if (selectedId) {
        const idx = tags.findIndex((t) => t.id === selectedId);
        if (idx < tags.length - 1) {
          setSelectedTagIds(new Set([tags[idx + 1].id]));
          tagRefs.current[tags[idx + 1].id]?.focus();
        } else {
          // Move focus back to input
          setSelectedTagIds(new Set());
          inputRef.current?.focus();
        }
      }
      return;
    }

    if (e.key === "Enter") {
      e.preventDefault();
      const selectedId = getFirstSelectedTag();
      if (selectedId && editingTagId === null) {
        setEditingTagId(selectedId);
        setEditingTagText(tags.find((t) => t.id === selectedId)!.text);
        tagRefs.current[selectedId]?.focus();
      }
      return;
    }

    if (e.key === "Backspace" || e.key === "Delete") {
      if (document.activeElement === inputRef.current || editingTagId !== null)
        return;
      e.preventDefault();
      removeSelectedTags(selectedTagIds);
      return;
    }

    if (e.key === "Escape") {
      if (editingTagId) return;
      e.preventDefault();
      setSelectedTagIds(new Set());
      inputRef.current?.focus();
    }
  }

  useEffect(() => {
    if (selectedTagIds.size > 0) {
      containerRef.current?.focus();
    }
  }, [selectedTagIds]);

  // Click outside to deselect
  useEffect(() => {
    return addEventListener(document, "mousedown", (e) => {
      if (
        containerRef.current &&
        e.target instanceof Node &&
        !containerRef.current.contains(e.target)
      ) {
        setSelectedTagIds(new Set());
        setEditingTagId(null);
      }
    });
  }, []);

  return (
    <div
      className="container"
      data-testid="tag-list-container"
      ref={containerRef}
      role="listbox"
      aria-label="Tag list"
      aria-multiselectable="true"
      tabIndex={0}
      onClick={(e) => {
        // Clear selection when background clicked
        if (
          e.target === containerRef.current ||
          e.target === inputRef.current ||
          (e.target as HTMLElement).classList.contains("tag-list")
        ) {
          setSelectedTagIds(new Set());
          setEditingTagId(null);
          inputRef.current?.focus();
        }
      }}
      onKeyDown={handleContainerKeyDown}
      {...rest}
    >
      <div className="tag-list" data-testid="tag-list">
        {tags.map((tag) => {
          const isSelected = isTagSelected(tag.id);
          const isEditing = editingTagId === tag.id;

          return (
            <EditableTag
              key={tag.id}
              ref={(el) => (tagRefs.current[tag.id] = el)}
              tabIndex={0}
              isSelected={isSelected}
              isEditing={isEditing}
              value={isEditing ? editingTagText : tag.text}
              onValueChange={(txt) => setEditingTagText(txt)}
              onChange={exitEditTag}
              onEditChange={(nextEditing) => {
                if (disabled) return false;
                if (!nextEditing) cancelTagEdit();
                return true;
              }}
              onDismiss={
                disabled ? undefined : () => removeSelectedTags(tag.id)
              }
              onClick={(e) => !disabled && handleTagClick(e, tag.id)}
              data-testid={`tag-${tag.id}`}
            />
          );
        })}

        {!disabled && (
          <TokenInput
            ref={combineRefs(inputRef, externalInputRef)}
            value={inputValue}
            className="tag-input"
            onAddTokens={addTags}
            onValueChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            delimiters={delimiterUtils}
          />
        )}
      </div>

      <style scoped>{`
        :scope {
          --tag-bg-color-hover: var(--ds-color-primary-2);
          --tag-bg-color-active: var(--ds-color-primary-3);
          --tag-selected-bg-color: var(--ds-color-accent-3);
          --tag-bg-color: inherit;

          width: 100%;
          height: 100%;
          overflow-y: scroll;
          display: flex;
          flex-direction: column;
          gap: var(--ds-spacing-2);
          outline: none;
          position: relative;
          padding: var(--ds-spacing-2);
          border: 1px solid var(--ds-color-neutral-6);
          border-radius: var(--ds-radius-3);
          cursor: text;
        }

        .tag-list {
          display: flex;
          flex-wrap: wrap;
          gap: var(--ds-spacing-2);
          align-items: center;
          width: 100%;
          flex: 1;
        }
      `}</style>
    </div>
  );
}

export default TagList;

import type { Meta, StoryObj } from "@storybook/react";
import { Card } from "./Card";

const meta = {
  title: "UI/Card",
  component: Card,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    className: {
      description: "Additional CSS class names",
      control: { type: "text" },
    },
  },
} satisfies Meta<typeof Card>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Default: Story = {
  render: () => (
    <Card>
      <h1>Card Title</h1>
      <p>Card Content</p>
      <p>Card Footer</p>

      <style scoped>{`
      :scope {
        width: 350px;
      }
    `}</style>
    </Card>
  ),
};

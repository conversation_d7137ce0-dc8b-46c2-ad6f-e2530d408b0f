import * as React from "react";
import { cn } from "app/utils/style";

export const Card = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div">
>(function Card({ className, children, ...props }, ref) {
  return (
    <div
      data-slot="card"
      ref={ref}
      className={cn(className, "card")}
      {...props}
    >
      {children}
      <style scoped>
        {`
        :scope {
          border-radius: var(--ds-radius-4);
          border: 1px solid var(--ds-color-border-default);
          padding: var(--ds-spacing-4);
        }
      `}
      </style>
    </div>
  );
});

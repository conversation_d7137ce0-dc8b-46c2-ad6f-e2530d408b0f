import type { Meta } from "@storybook/react";
import { Dropdown } from "./Dropdown";
import { useState } from "react";
import { DotsHorizontalIcon } from "@radix-ui/react-icons";

const meta = {
  title: "UI/Dropdown",
  component: Dropdown,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    children: {
      description: "Content to display in the dropdown trigger",
    },
  },
} satisfies Meta<typeof Dropdown>;

export default meta;

export function Default() {
  const [value, setValue] = useState("option1");
  return (
    <div style={{ width: "250px" }}>
      <Dropdown
        value={value}
        onChange={setValue}
        items={[
          { value: "option1", label: "Option 1" },
          { value: "option2", label: "Option 2" },
          { value: "option3", label: "Option 3" },
        ]}
      >
        {value}
      </Dropdown>
    </div>
  );
}

export function WithIcon() {
  const [value, setValue] = useState("option1");
  return (
    <div style={{ width: "250px" }}>
      <Dropdown
        value={value}
        onChange={setValue}
        items={[
          { value: "option1", label: "Option 1" },
          { value: "option2", label: "Option 2" },
          { value: "option3", label: "Option 3" },
        ]}
      >
        <DotsHorizontalIcon />
      </Dropdown>
      <style scoped>{`
          :scope {
            .dropdown-trigger {
              aspect-ratio: 1;
              padding: 0;
              height: var(--ds-spacing-6);
            }
          }
        `}</style>
    </div>
  );
}

import { Button, DropdownMenu } from "@radix-ui/themes";
import { cn, typography } from "app/utils/style";
import type { ReactNode } from "react";

type DropdownItem<T extends string> = {
  type?: "divider" | "item";
  value: T;
  label: ReactNode;
  selected?: boolean;
  disabled?: boolean;
};

export type DropdownProps<T extends string> = {
  value?: T;
  isOpen?: boolean;
  onOpenChange?: (isOpen: boolean) => void;
  onChange?: (value: T) => void;
  items: DropdownItem<T>[];
  className?: string;
  children: ReactNode;
};

export function Dropdown<T extends string>(props: DropdownProps<T>) {
  const { value, onChange, isOpen, onOpenChange, items, className, children } =
    props;
  return (
    <div className={cn("dropdown", className)}>
      <DropdownMenu.Root open={isOpen} onOpenChange={onOpenChange}>
        <DropdownMenu.Trigger>
          <Button variant="ghost" className="dropdown-trigger">
            {children}
          </Button>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content
          align="start"
          side="bottom"
          className="dropdown-content"
        >
          {items.map((item, index) => {
            if (item.type === "divider") {
              return <DropdownMenu.Separator key={`divider-${index}`} />;
            }
            return (
              <DropdownItem
                key={item.value}
                {...item}
                onSelect={() => onChange?.(item.value)}
                selected={value === item.value}
              />
            );
          })}
          <style scoped>{`
            .dropdown-item {
              padding: var(--ds-spacing-2) var(--ds-spacing-3);
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: var(--ds-spacing-2);
              color: var(--ds-color-text-default);
            }

            .dropdown-item--selected {
              background-color: var(--ds-color-success-3);
            }

            .dropdown-item:hover {
              background-color: var(--ds-color-accent-3);
            }

            .dropdown-item[data-disabled] {
              color: var(--ds-color-neutral-8);
              cursor: not-allowed;
            }

            .dropdown-item[data-highlighted] {
              background-color: var(--ds-color-accent-3);
            }
          `}</style>
        </DropdownMenu.Content>
      </DropdownMenu.Root>
      <style scoped>{`
        :scope.dropdown {
          position: relative;
          height: fit-content;
        }

        .dropdown-trigger {
          display: flex;
          align-items: center;
          ${typography.text2.regular}
        }

        .dropdown-content {
          background-color: var(--ds-panel-solid);
          border-radius: var(--ds-radius-2);
          border: 1px solid var(--ds-color-neutral-6);
          box-shadow: var(--ds-shadow-2);
          min-width: 180px;
        }
      `}</style>
    </div>
  );
}

type DropdownItemProps<T extends string> = DropdownItem<T> & {
  className?: string;
  onSelect?: () => void;
};

function DropdownItem<T extends string>(props: DropdownItemProps<T>) {
  const { value, label, selected, className, ...restProps } = props;
  return (
    <DropdownMenu.Item
      key={value}
      className={cn("dropdown-item", className, {
        "dropdown-item--selected": selected,
      })}
      {...restProps}
    >
      {label}
    </DropdownMenu.Item>
  );
}

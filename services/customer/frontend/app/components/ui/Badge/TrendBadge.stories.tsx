import type { Meta, StoryObj } from "@storybook/react";
import { TrendBadge } from "../Badge";

const meta = {
  title: "UI/Badge/TrendBadge",
  component: TrendBadge,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    value: {
      description: "The value of the trend",
      control: { type: "number" },
    },
  },
  // Default Values
  args: {
    value: 5,
  },
} satisfies Meta<typeof TrendBadge>;

export default meta;

type Story = StoryObj<typeof meta>;

// Stories
export const Default: Story = {
  args: {
    value: 25,
  },
};

export const Negative: Story = {
  args: {
    value: -15,
  },
};

export const Zero: Story = {
  args: {
    value: 0,
  },
};

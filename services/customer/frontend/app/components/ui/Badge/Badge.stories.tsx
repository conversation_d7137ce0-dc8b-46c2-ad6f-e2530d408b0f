import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Badge, sizeMap, colorMap } from "./Badge";
import { sortBy } from "app/utils/collection";
import { Separator } from "../Separator";
import { capitalize, alphabeticalSort } from "app/utils/string";
import { isNonNullable } from "app/utils/guards";

const badgeColors = Object.keys(colorMap) as (keyof typeof colorMap)[];
const badgeSizes = Object.keys(sizeMap) as (keyof typeof sizeMap)[];
const badgeVariants = [
  "surface" as const,
  "solid" as const,
  "soft" as const,
  "outline" as const,
];

const meta = {
  title: "UI/Badge/Badge",
  component: Badge,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    color: {
      description: "The visual style of the badge",
      control: { type: "select" },
      options: badgeColors,
    },
    size: {
      description: "The size of the badge",
      control: { type: "select" },
      options: badgeSizes,
    },
    variant: {
      description: "The variant of the badge",
      control: { type: "select" },
      options: badgeVariants,
    },
    tooltip: {
      description: "Optional tooltip text",
      control: { type: "text" },
    },
  },
  // Default Values
  args: {
    children: "Badge Text",
    color: "default",
    size: "medium",
    tooltip: "This is a tooltip",
  },
} satisfies Meta<typeof Badge>;

export default meta;

type Story = StoryObj<typeof meta>;

// Basic story
export const Default: Story = {
  args: {
    children: "Default Badge",
  },
};

// Grid of all variants
export const AllVariants: Story = {
  render: () => {
    return (
      <div>
        {sortBy(alphabeticalSort)(badgeVariants)
          .filter(isNonNullable)
          .map((variant) => (
            <div key={variant}>
              <h3>{capitalize(variant)}</h3>
              <Separator />
              <div className="badge-grid">
                {badgeSizes.map((size) =>
                  badgeColors.map((color) => (
                    <div key={`${size}-${color}`} className="badge-cell">
                      <Badge
                        key={`${size}-${color}`}
                        size={size}
                        color={color}
                        variant={variant}
                        tooltip={`This is a ${size} ${color} ${variant} badge`}
                      >
                        {String(size)} {color}
                      </Badge>
                    </div>
                  )),
                )}
              </div>
            </div>
          ))}
        <style scoped>
          {`
            :scope {
              display: flex;
              flex-direction: column;
              gap: var(--ds-spacing-8);
            }
            .badge-grid {
              margin-top: var(--ds-spacing-4);
              display: grid;
              grid-template-columns: repeat(${badgeColors.length}, 1fr);
              grid-template-rows: repeat(${badgeSizes.length}, 1fr);
              gap: var(--ds-spacing-5);
              place-items: center;
            }
          `}
        </style>
      </div>
    );
  },
};

import { forwardRef, type ReactNode } from "react";
import {
  Badge as RadixBadge,
  type BadgeProps as RadixBadgeProps,
} from "@radix-ui/themes";
import { Tooltip } from "../Tooltip";
import { cn } from "app/utils/style";

export type BadgeSize = keyof typeof sizeMap | RadixBadgeProps["size"];
export type BadgeColor = keyof typeof colorMap | RadixBadgeProps["color"];
export type BadgeVariant = RadixBadgeProps["variant"];

export type BadgeProps = {
  children: ReactNode;
  color?: BadgeColor;
  variant?: BadgeVariant;
  size?: BadgeSize;
  tooltip?: string;
  enabled?: boolean;
  className?: string;
} & Omit<RadixBadgeProps, "color" | "size">;

// Map our custom sizes to Radix sizes
export const sizeMap = {
  small: "1",
  medium: "2",
  large: "3",
} as const satisfies Record<string, RadixBadgeProps["size"]>;

// Map our custom variants to Radix colors
export const colorMap = {
  default: "indigo",
  success: "green",
  warning: "amber",
  error: "red",
  info: "blue",
  neutral: "gray",
  premium: "violet",
} as const satisfies Record<string, RadixBadgeProps["color"]>;

export const Badge = forwardRef<HTMLDivElement, BadgeProps>(
  function Badge(props, ref) {
    const {
      children,
      color = "default",
      variant = "surface",
      size = "medium",
      tooltip,
      enabled = true,
      className,
      ...rest
    } = props;
    const radixSize = sizeMap[size as keyof typeof sizeMap] ?? size;
    const radixColor = colorMap[color as keyof typeof colorMap] ?? color;
    if (!enabled) return null;
    return (
      <Tooltip content={tooltip}>
        <RadixBadge
          ref={ref}
          size={radixSize}
          radius="full"
          variant={variant}
          color={radixColor}
          {...rest}
          className={cn("badge", className)}
        >
          {children}
          <style scoped>
            {`
          :scope.badge {
            width: fit-content;
          }
        `}
          </style>
        </RadixBadge>
      </Tooltip>
    );
  },
);

export default Badge;

import { forwardRef, type ReactElement, type ReactNode } from "react";
import { Badge, type BadgeProps } from "./Badge";
import { cn } from "app/utils/style";

export type IconBadgeProps = {
  icon?: ReactElement;
  children: ReactNode;
} & Omit<BadgeProps, "children">;

export const IconBadge = forwardRef<HTMLDivElement, IconBadgeProps>(
  function IconBadge(props, ref) {
    const {
      icon,
      children,
      color = "default",
      variant = "surface",
      size = "medium",
      ...rest
    } = props;
    return (
      <Badge
        {...rest}
        color={color}
        variant={variant}
        size={size}
        className={cn("icon-badge", rest.className)}
        ref={ref}
      >
        {icon && <span className="icon-container">{icon}</span>}
        {children}
        <style scoped>{`
        .icon-badge {
          display: inline-flex;
          align-items: center;
          gap: var(--ds-spacing-1);
        }
      `}</style>
      </Badge>
    );
  },
);

export default IconBadge;

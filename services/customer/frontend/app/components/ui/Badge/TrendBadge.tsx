import {
  ArrowDownIcon,
  ArrowRightIcon,
  ArrowUpIcon,
} from "@radix-ui/react-icons";
import { IconBadge } from "./IconBadge";

/**
 * A component that displays a trend value with an indicator arrow.
 */
export function TrendBadge({ value = 0 }: { value: number }) {
  const icon =
    value > 0 ? (
      <ArrowUpIcon />
    ) : value < 0 ? (
      <ArrowDownIcon />
    ) : (
      <ArrowRightIcon />
    );

  const color = value > 0 ? "green" : value < 0 ? "yellow" : "gray";

  return (
    <IconBadge icon={icon} color={color} size="1">
      {`${value}%`}
    </IconBadge>
  );
}

export default TrendBadge;

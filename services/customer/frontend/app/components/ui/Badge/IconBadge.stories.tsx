import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { IconBadge, type IconBadgeProps } from "./IconBadge";
import * as RadixIcons from "@radix-ui/react-icons";
import badgeMeta from "./Badge.stories";
import { createElement, type ReactElement } from "react";

const badgeColors = badgeMeta.argTypes.color.options;
const badgeSizes = badgeMeta.argTypes.size.options;
const badgeVariants = badgeMeta.argTypes.variant.options;

const allIcons = Object.keys(RadixIcons)
  .filter((name) => name.endsWith("Icon"))
  .sort();

const meta = {
  title: "UI/Badge/IconBadge",
  component: IconBadge,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    color: {
      description: "The visual style of the badge",
      control: { type: "select" },
      options: badgeColors,
    },
    size: {
      description: "The size of the badge",
      control: { type: "select" },
      options: badgeSizes,
    },
    variant: {
      description: "The variant of the badge",
      control: { type: "select" },
      options: badgeVariants,
    },
    tooltip: {
      description: "Optional tooltip text",
      control: { type: "text" },
    },
    icon: {
      description: "Icon to display in the badge",
      options: allIcons,
      mapping: Object.fromEntries(
        allIcons.map((name) => [
          name,
          createElement(RadixIcons[name as keyof typeof RadixIcons]),
        ]),
      ),
      control: {
        type: "select",
      },
    },
  },
  // Default Values
  args: {
    children: "Icon Badge",
    color: "default",
    size: "medium",
    tooltip: "This is an icon badge",
    icon: "InfoCircledIcon",
  },
} satisfies Meta<
  (
    props: Omit<IconBadgeProps, "icon"> & { icon: keyof typeof RadixIcons },
  ) => ReactElement
>;

export default meta;

type Story = StoryObj<typeof meta>;

// Basic story
export const Default: Story = {
  args: {
    children: "Default Icon Badge",
    icon: <RadixIcons.InfoCircledIcon />,
  },
};

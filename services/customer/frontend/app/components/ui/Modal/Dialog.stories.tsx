import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import * as Dialog from "./Dialog.sub";
import { Theme } from "@radix-ui/themes";
import { useEffect } from "react";

// Add a wrapper component to control the dialog state
const DialogExample = () => {
  // Force the dialog to be open in Storybook
  useEffect(() => {
    setTimeout(() => {
      const dialogTrigger = document.querySelector(
        '[data-slot="dialog-trigger"]',
      );
      if (dialogTrigger) {
        (dialogTrigger as HTMLButtonElement).click();
      }
    }, 500);
  }, []);

  return (
    <Theme>
      <Dialog.Root>
        <Dialog.Trigger>
          <button className="px-4 py-2 bg-blue-500 rounded">Open Dialog</button>
        </Dialog.Trigger>
        <Dialog.Portal>
          <Dialog.Overlay />
          <Dialog.Content>
            <Dialog.Header>
              <Dialog.Title>Dialog Title</Dialog.Title>
              <Dialog.Description>
                This is a description of the dialog content and purpose.
              </Dialog.Description>
            </Dialog.Header>
            <div className="py-4">
              <p>This is the main content of the dialog.</p>
              <p className="mt-2">
                You can add any components or content here.
              </p>
            </div>
            <Dialog.Footer>
              <button className="px-4 py-2 bg-gray-200 rounded mr-2">
                Cancel
              </button>
              <button className="px-4 py-2 bg-blue-500 rounded">Confirm</button>
            </Dialog.Footer>
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>
    </Theme>
  );
};

const meta = {
  title: "ui/Modal/Dialog",
  component: DialogExample,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof DialogExample>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {};

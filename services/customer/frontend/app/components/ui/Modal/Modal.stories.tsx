import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { But<PERSON>, TextField, Text, Flex } from "@radix-ui/themes";
import { useState } from "react";
import { Modal } from "./Modal";

const meta = {
  title: "UI/Modal",
  component: Modal,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    title: {
      control: "text",
      description: "The title of the modal",
    },
    description: {
      control: "text",
      description: "The description of the modal (optional)",
    },
    children: {
      control: "text",
      description: "The content of the modal",
    },
    footer: {
      control: "object",
      description: "Optional footer content",
    },
    trigger: {
      control: "object",
      description: "The trigger that opens the modal",
    },
    isDirty: {
      control: "boolean",
      description: "Whether the modal has unsaved changes",
    },
  },
} satisfies Meta<typeof Modal>;

export default meta;
type Story = StoryObj<typeof Modal>;

// Basic modal example
export const Default: Story = {
  args: {
    title: "Example Modal",
    description: "This is a description of the modal",
    children: <Text>This is the content of the modal</Text>,
    trigger: (open: () => void) => <Button onClick={open}>Open Modal</Button>,
    footer: (close: () => void) => <Button onClick={close}>Close</Button>,
  },
};

// Custom component for the form modal with dirty state handling
function FormModalWithDirtyState() {
  const [isOpen, setIsOpen] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");

  const handleSubmit = () => {
    // Submit form logic would go here
    setIsDirty(false);
    setIsOpen(false);
    // Reset form
    setName("");
    setEmail("");
    setMessage("");
  };

  const handleInputChange = () => {
    setIsDirty(true);
  };

  return (
    <Modal
      title="Form with Unsaved Changes"
      description="This form will prompt for confirmation if closed with unsaved changes"
      isOpen={isOpen}
      onOpenChange={setIsOpen}
      isDirty={isDirty}
      trigger={(open) => <Button onClick={open}>Open Modal With Form</Button>}
      footer={(close) => (
        <>
          <Button variant="soft" color="gray" onClick={close}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>Submit</Button>
        </>
      )}
    >
      <div className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium mb-1">
            Name
          </label>
          <TextField.Root
            id="name"
            placeholder="Enter your name"
            value={name}
            onChange={(e) => {
              setName(e.target.value);
              handleInputChange();
            }}
          />
        </div>
        <div>
          <label htmlFor="email" className="block text-sm font-medium mb-1">
            Email
          </label>
          <TextField.Root
            id="email"
            placeholder="Enter your email"
            value={email}
            onChange={(e) => {
              setEmail(e.target.value);
              handleInputChange();
            }}
          />
        </div>
        <div>
          <label htmlFor="message" className="block text-sm font-medium mb-1">
            Message
          </label>
          <TextField.Root
            id="message"
            placeholder="Enter your message"
            value={message}
            onChange={(e) => {
              setMessage(e.target.value);
              handleInputChange();
            }}
          />
        </div>
      </div>
    </Modal>
  );
}

export const WithForm: Story = {
  render: () => <FormModalWithDirtyState />,
};

// Modal with React node as title and description
export const ReactNodeContent: Story = {
  args: {
    title: "Custom Title",
    description: (
      <Flex direction="column" gap="2">
        <Text>This description uses React components</Text>
        <Text color="gray">You can use any React component here</Text>
      </Flex>
    ),
    children: (
      <Text>
        This modal demonstrates using React nodes for title and description
      </Text>
    ),
    trigger: (open: () => void) => <Button onClick={open}>Open Modal</Button>,
  },
};

// Custom component for the confirmation dialog with working cancel button
const ConfirmationDialogModal = () => {
  return (
    <Modal
      title="Delete Confirmation"
      description="Are you sure you want to delete this item? This action cannot be undone."
      trigger={(open) => (
        <Button onClick={open} color="red">
          Delete Item
        </Button>
      )}
      footer={(close) => (
        <>
          <Button variant="soft" color="gray" onClick={close}>
            Cancel
          </Button>
          <Button color="red">Delete</Button>
        </>
      )}
    >
      <Text>This is a confirmation dialog example.</Text>
    </Modal>
  );
};

// Modal with confirmation dialog
export const ConfirmationDialog: Story = {
  render: () => <ConfirmationDialogModal />,
};

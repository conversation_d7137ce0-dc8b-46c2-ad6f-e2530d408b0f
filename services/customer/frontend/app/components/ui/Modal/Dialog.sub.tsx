import { type ReactNode, type ComponentProps, forwardRef } from "react";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { cn } from "app/utils/style";
import Icons from "../Icons";

type DialogRootProps = ComponentProps<typeof DialogPrimitive.Root>;
type DialogTriggerProps = ComponentProps<typeof DialogPrimitive.Trigger>;
type DialogPortalProps = ComponentProps<typeof DialogPrimitive.Portal>;
type DialogCloseProps = ComponentProps<typeof DialogPrimitive.Close>;
type DialogOverlayProps = ComponentProps<typeof DialogPrimitive.Overlay>;
type DialogContentProps = ComponentProps<typeof DialogPrimitive.Content> & {
  children: ReactNode;
};
type DialogHeaderProps = ComponentProps<"div">;
type DialogFooterProps = ComponentProps<"div">;
type DialogTitleProps = ComponentProps<typeof DialogPrimitive.Title>;
type DialogDescriptionProps = ComponentProps<
  typeof DialogPrimitive.Description
>;

function DialogRoot({ ...props }: DialogRootProps) {
  return <DialogPrimitive.Root data-slot="dialog" {...props} />;
}

const DialogTrigger = forwardRef<HTMLButtonElement, DialogTriggerProps>(
  function DialogTrigger({ ...props }, ref) {
    return (
      <DialogPrimitive.Trigger
        data-slot="dialog-trigger"
        ref={ref}
        {...props}
      />
    );
  },
);

function DialogPortal({ ...props }: DialogPortalProps) {
  return <DialogPrimitive.Portal data-slot="dialog-portal" {...props} />;
}

const DialogClose = forwardRef<HTMLButtonElement, DialogCloseProps>(
  function DialogClose({ ...props }, ref) {
    return (
      <DialogPrimitive.Close data-slot="dialog-close" ref={ref} {...props} />
    );
  },
);

const DialogOverlay = forwardRef<HTMLDivElement, DialogOverlayProps>(
  function DialogOverlay({ className, ...props }, ref) {
    return (
      <DialogPrimitive.Overlay
        data-slot="dialog-overlay"
        className={cn(
          "fixed inset-0 z-50 bg-black/50",
          "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);

const DialogContent = forwardRef<HTMLDivElement, DialogContentProps>(
  function DialogContent({ className, children, ...props }, ref) {
    return (
      <DialogPrimitive.Content
        data-slot="dialog-content"
        className={cn(
          "fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",
          "bg-[var(--ds-color-surface-default)]",
          "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
          "dialog-content",
          className,
        )}
        ref={ref}
        {...props}
      >
        {children}
        <DialogPrimitive.Close className="absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:outline-hidden focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none">
          <Icons.Cross2Icon width="16" height="16" />
          <span className="sr-only">Close</span>
        </DialogPrimitive.Close>
      </DialogPrimitive.Content>
    );
  },
);

function DialogHeader({ className, ...props }: DialogHeaderProps) {
  return (
    <div
      data-slot="dialog-header"
      className={cn(
        "flex flex-col gap-2 text-center sm:text-left",
        "dialog-header",
        className,
      )}
      {...props}
    />
  );
}

function DialogFooter({ className, ...props }: DialogFooterProps) {
  return (
    <div
      data-slot="dialog-footer"
      className={cn(
        "flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",
        "dialog-footer",
        className,
      )}
      {...props}
    />
  );
}

const DialogTitle = forwardRef<HTMLHeadingElement, DialogTitleProps>(
  function DialogTitle({ className, ...props }, ref) {
    return (
      <DialogPrimitive.Title
        data-slot="dialog-title"
        className={cn(
          "text-lg leading-none font-semibold",
          "dialog-title",
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);

const DialogDescription = forwardRef<
  HTMLParagraphElement,
  DialogDescriptionProps
>(function DialogDescription({ className, ...props }, ref) {
  return (
    <DialogPrimitive.Description
      data-slot="dialog-description"
      className={cn(
        "text-[var(--ds-color-text-muted)] text-sm",
        "dialog-description",
        className,
      )}
      ref={ref}
      {...props}
    />
  );
});

export {
  DialogRoot as Root,
  DialogClose as Close,
  DialogContent as Content,
  DialogDescription as Description,
  DialogFooter as Footer,
  DialogHeader as Header,
  DialogOverlay as Overlay,
  DialogPortal as Portal,
  DialogTitle as Title,
  DialogTrigger as Trigger,
};

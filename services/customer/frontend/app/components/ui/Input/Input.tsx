import { TextField } from "@radix-ui/themes";
import { cn, typography } from "app/utils/style";
import { type ComponentProps, forwardRef, type ReactNode } from "react";

type InputProps = {
  value: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  children?: ReactNode;
} & Omit<ComponentProps<typeof TextField.Root>, "onChange" | "value">;

export const Input = forwardRef<HTMLInputElement, InputProps>(function Input(
  {
    type = "text",
    value,
    onChange,
    placeholder,
    disabled,
    className,
    children,
    ...rest
  },
  ref,
) {
  return (
    <div className={cn(className, "input")}>
      <TextField.Root
        type={type}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        ref={ref}
        {...rest}
      >
        {children}
      </TextField.Root>
      <style scoped>{`
          :scope.input {
            cursor: text;
            width: 100%;
            height: fit-content;
            box-shadow: unset;
            border-radius: var(--ds-radius-4);
            ${typography.text2.regular}
            color: var(--ds-color-text-default);

            > div {
              padding: var(--ds-spacing-0_5);
              height: fit-content;
            }
            input {
              padding: var(--ds-spacing-1_5);
            }
          }
        `}</style>
    </div>
  );
});

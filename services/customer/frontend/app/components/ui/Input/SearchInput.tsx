import { type ComponentProps, forwardRef } from "react";
import { Input } from "./Input";
import { cn } from "app/utils/style";
import { Cross2Icon, MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { Button, TextField } from "@radix-ui/themes";

type SearchInputProps = Omit<
  ComponentProps<typeof Input>,
  "onChange" | "type" | "value"
> & {
  value: string;
  onChange?: (value: string) => void;
};

export const SearchInput = forwardRef<HTMLInputElement, SearchInputProps>(
  function SearchInput(props, ref) {
    const { className, value, onChange, ...rest } = props;
    const hasValue = value && value.trim() !== "";
    return (
      <Input
        ref={ref}
        type="search"
        className={cn(className, "search-input")}
        value={value}
        onChange={(value) => {
          onChange?.(value);
        }}
        {...rest}
      >
        <TextField.Slot>
          <MagnifyingGlassIcon height={16} width={16} />
        </TextField.Slot>
        {hasValue && (
          <TextField.Slot>
            <Button
              type="button"
              onClick={() => onChange?.("")}
              className="clear-button"
              variant="ghost"
            >
              <Cross2Icon height={16} width={16} />
            </Button>
          </TextField.Slot>
        )}
      </Input>
    );
  },
);

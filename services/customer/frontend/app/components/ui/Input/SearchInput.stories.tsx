import type { Meta } from "@storybook/react";
import { SearchInput } from "./SearchInput";
import { useState, useMemo } from "react";

const meta = {
  title: "UI/SearchInput",
  component: SearchInput,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    placeholder: {
      control: "text",
      description: "Placeholder text for the search input",
    },
    disabled: {
      control: "boolean",
      description: "Whether the input is disabled",
    },
    className: {
      control: "text",
      description: "Additional CSS class names",
    },
  },
} satisfies Meta<typeof SearchInput>;

export default meta;

// Sample data for vacation destinations
const vacationDestinations = [
  { id: 1, name: "Paris", country: "France", region: "Europe", type: "City" },
  { id: 2, name: "Bali", country: "Indonesia", region: "Asia", type: "Island" },
  { id: 3, name: "Tokyo", country: "Japan", region: "Asia", type: "City" },
  {
    id: 4,
    name: "New York",
    country: "USA",
    region: "North America",
    type: "City",
  },
  {
    id: 5,
    name: "Santorini",
    country: "Greece",
    region: "Europe",
    type: "Island",
  },
  {
    id: 6,
    name: "<PERSON><PERSON>cchu",
    country: "Peru",
    region: "South America",
    type: "Historical",
  },
  {
    id: 7,
    name: "Maldives",
    country: "Maldives",
    region: "Asia",
    type: "Island",
  },
  {
    id: 8,
    name: "Barcelona",
    country: "Spain",
    region: "Europe",
    type: "City",
  },
  {
    id: 9,
    name: "Cape Town",
    country: "South Africa",
    region: "Africa",
    type: "City",
  },
  {
    id: 10,
    name: "Sydney",
    country: "Australia",
    region: "Oceania",
    type: "City",
  },
  {
    id: 11,
    name: "Kyoto",
    country: "Japan",
    region: "Asia",
    type: "Historical",
  },
  {
    id: 12,
    name: "Cancun",
    country: "Mexico",
    region: "North America",
    type: "Beach",
  },
  { id: 13, name: "Venice", country: "Italy", region: "Europe", type: "City" },
  {
    id: 14,
    name: "Serengeti",
    country: "Tanzania",
    region: "Africa",
    type: "Safari",
  },
  {
    id: 15,
    name: "Rio de Janeiro",
    country: "Brazil",
    region: "South America",
    type: "City",
  },
];

// Basic search input
export function Default() {
  const [value, setValue] = useState("");

  return (
    <div className="container">
      <SearchInput value={value} onChange={setValue} placeholder="Search..." />
      <div className="value-display">
        Search value: <code>{value}</code>
      </div>
      <style scoped>{`
        :scope .container {
          width: 300px;
        }
        :scope .value-display {
          margin-top: var(--ds-spacing-3);
        }
      `}</style>
    </div>
  );
}
Default.args = {};

// Levenshtein distance calculation
function levenshteinDistance(a: string, b: string): number {
  const matrix: number[][] = [];

  // Initialize matrix
  for (let i = 0; i <= b.length; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= a.length; j++) {
    matrix[0][j] = j;
  }

  // Fill matrix
  for (let i = 1; i <= b.length; i++) {
    for (let j = 1; j <= a.length; j++) {
      const cost = a[j - 1] === b[i - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1, // deletion
        matrix[i][j - 1] + 1, // insertion
        matrix[i - 1][j - 1] + cost, // substitution
      );
    }
  }

  return matrix[b.length][a.length];
}

// Find best match positions for highlighting
function findMatchPositions(text: string, query: string): number[] {
  if (!query || query.length === 0) return [];

  const positions: number[] = [];
  const lowerText = text.toLowerCase();
  const lowerQuery = query.toLowerCase();

  // Simple case: direct substring match
  const directIndex = lowerText.indexOf(lowerQuery);
  if (directIndex !== -1) {
    for (let i = 0; i < query.length; i++) {
      positions.push(directIndex + i);
    }
    return positions;
  }

  // For fuzzy matching, we'll use a dynamic programming approach
  // to find the best alignment of the query within the text
  const m = text.length;
  const n = query.length;

  if (n > m) return []; // Query longer than text, no good match

  // Try each possible starting position in the text
  let bestScore = Infinity;
  let bestPositions: number[] = [];

  for (let i = 0; i <= m - n; i++) {
    const substring = text.substring(i, i + n);
    const score = levenshteinDistance(substring.toLowerCase(), lowerQuery);

    if (score < bestScore) {
      bestScore = score;
      bestPositions = [];
      for (let j = 0; j < n; j++) {
        bestPositions.push(i + j);
      }
    }
  }

  // Only return positions if the score is reasonable
  // (adjust threshold as needed)
  return bestScore <= Math.min(3, Math.floor(query.length / 2))
    ? bestPositions
    : [];
}

function HighlightText({
  text,
  matchPositions,
}: {
  text: string;
  matchPositions: number[];
}) {
  if (!matchPositions.length) return <>{text}</>;

  const result: JSX.Element[] = [];
  let lastIndex = 0;

  // Create a set for O(1) lookups
  const positionSet = new Set(matchPositions);

  for (let i = 0; i < text.length; i++) {
    if (positionSet.has(i)) {
      if (i > lastIndex) {
        result.push(
          <span key={`text-${i}`}>{text.substring(lastIndex, i)}</span>,
        );
      }
      result.push(<b key={`bold-${i}`}>{text[i]}</b>);
      lastIndex = i + 1;
    }
  }

  if (lastIndex < text.length) {
    result.push(<span key={`text-end`}>{text.substring(lastIndex)}</span>);
  }

  return <>{result}</>;
}

// With fuzzy search and highlighted matches
export function FuzzySearchWithHighlighting() {
  const [searchTerm, setSearchTerm] = useState("");

  // Filter and sort destinations by Levenshtein distance
  const filteredDestinations = useMemo(() => {
    if (!searchTerm.trim()) return vacationDestinations;

    const lowerSearchTerm = searchTerm.toLowerCase();

    return vacationDestinations
      .map((destination) => {
        // Calculate distances for each searchable field
        const nameDistance = levenshteinDistance(
          destination.name.toLowerCase(),
          lowerSearchTerm,
        );
        const countryDistance = levenshteinDistance(
          destination.country.toLowerCase(),
          lowerSearchTerm,
        );
        const regionDistance = levenshteinDistance(
          destination.region.toLowerCase(),
          lowerSearchTerm,
        );
        const typeDistance = levenshteinDistance(
          destination.type.toLowerCase(),
          lowerSearchTerm,
        );

        // Use the minimum distance as the score
        const score = Math.min(
          nameDistance,
          countryDistance,
          regionDistance,
          typeDistance,
        );

        return {
          ...destination,
          score,
        };
      })
      .filter((item) => {
        // Only include items with a reasonable match score
        // Adjust threshold based on search term length
        const threshold = Math.min(
          4,
          Math.max(2, Math.floor(searchTerm.length / 2)),
        );
        return item.score <= threshold;
      })
      .sort((a, b) => a.score - b.score); // Sort by score (lower is better)
  }, [searchTerm]);

  return (
    <div className="container">
      <SearchInput
        value={searchTerm}
        onChange={setSearchTerm}
        placeholder="Fuzzy search vacation destinations..."
      />

      <div className="results-container">
        <div className="results-header">
          <span className="results-count">
            {filteredDestinations.length} destinations
          </span>
          {searchTerm && (
            <button className="clear-button" onClick={() => setSearchTerm("")}>
              Clear
            </button>
          )}
        </div>

        {filteredDestinations.length > 0 ? (
          <ul className="results-list">
            {filteredDestinations.map((destination) => {
              // Find match positions for highlighting
              const nameMatches = findMatchPositions(
                destination.name,
                searchTerm,
              );
              const countryMatches = findMatchPositions(
                destination.country,
                searchTerm,
              );
              const regionMatches = findMatchPositions(
                destination.region,
                searchTerm,
              );
              const typeMatches = findMatchPositions(
                destination.type,
                searchTerm,
              );

              return (
                <li key={destination.id} className="result-item">
                  <div className="destination-info">
                    <span className="destination-name">
                      <HighlightText
                        text={destination.name}
                        matchPositions={nameMatches}
                      />
                    </span>
                    <span className="destination-country">
                      <HighlightText
                        text={destination.country}
                        matchPositions={countryMatches}
                      />
                    </span>
                  </div>
                  <div className="destination-tags">
                    <span className="destination-region">
                      <HighlightText
                        text={destination.region}
                        matchPositions={regionMatches}
                      />
                    </span>
                    <span className="destination-type">
                      <HighlightText
                        text={destination.type}
                        matchPositions={typeMatches}
                      />
                    </span>
                  </div>
                </li>
              );
            })}
          </ul>
        ) : (
          <div className="no-results">No destinations found</div>
        )}
      </div>

      <style scoped>{`
        :scope .container {
          width: 400px;
        }
        :scope .results-container {
          margin-top: var(--ds-spacing-3);
          border: 1px solid var(--ds-color-border);
          border-radius: var(--ds-radius-2);
          overflow: hidden;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        :scope .results-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--ds-spacing-2) var(--ds-spacing-3);
          background-color: var(--ds-color-background-subtle);
          border-bottom: 1px solid var(--ds-color-border);
        }
        :scope .results-count {
          font-size: 0.875rem;
          color: var(--ds-color-text-muted);
        }
        :scope .clear-button {
          font-size: 0.75rem;
          padding: var(--ds-spacing-1) var(--ds-spacing-2);
          background-color: transparent;
          border: 1px solid var(--ds-color-border);
          border-radius: var(--ds-radius-1);
          cursor: pointer;
        }
        :scope .clear-button:hover {
          background-color: var(--ds-color-background-muted);
        }
        :scope .results-list {
          max-height: 350px;
          overflow-y: auto;
          margin: 0;
          padding: 0;
          list-style: none;
        }
        :scope .result-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--ds-spacing-3);
          border-bottom: 1px solid var(--ds-color-border-subtle);
          transition: background-color 0.2s ease;
        }
        :scope .result-item:last-child {
          border-bottom: none;
        }
        :scope .result-item:hover {
          background-color: var(--ds-color-background-subtle);
        }
        :scope .destination-info {
          display: flex;
          flex-direction: column;
        }
        :scope .destination-name {
          font-weight: 500;
          font-size: 1rem;
          margin-bottom: var(--ds-spacing-1);
        }
        :scope .destination-country {
          font-size: 0.875rem;
          color: var(--ds-color-text-muted);
        }
        :scope .destination-tags {
          display: flex;
          gap: var(--ds-spacing-2);
        }
        :scope .destination-region {
          font-size: 0.75rem;
          color: var(--ds-color-text-muted);
          background-color: var(--ds-color-background-muted);
          padding: var(--ds-spacing-1) var(--ds-spacing-2);
          border-radius: var(--ds-radius-1);
        }
        :scope .destination-type {
          font-size: 0.75rem;
          color: var(--ds-color-text-accent);
          background-color: var(--ds-color-accent-2);
          padding: var(--ds-spacing-1) var(--ds-spacing-2);
          border-radius: var(--ds-radius-1);
        }
        :scope .no-results {
          padding: var(--ds-spacing-3);
          font-size: 0.875rem;
          color: var(--ds-color-text-muted);
        }
      `}</style>
    </div>
  );
}

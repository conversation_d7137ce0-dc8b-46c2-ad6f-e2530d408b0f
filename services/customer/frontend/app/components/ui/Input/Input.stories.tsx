import type { <PERSON>a, <PERSON>Obj } from "@storybook/react";
import { Input } from "./Input";
import { useState } from "react";
import { BellIcon } from "@radix-ui/react-icons";
import { TextField } from "@radix-ui/themes";

const meta = {
  title: "UI/Input",
  component: Input,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    type: {
      control: { type: "select" },
      options: ["text", "number", "email", "password", "url"],
      description: "The type of input",
    },
    placeholder: {
      control: "text",
      defaultValue: "Type something...",
      description: "Placeholder text for the input",
    },
    disabled: {
      control: "boolean",
      description: "Whether the input is disabled",
    },
    className: {
      description: "Additional CSS class names",
    },
  },
} satisfies Meta<typeof Input>;

export default meta;
type Story = StoryObj<typeof meta>;
type StoryArgs = Omit<Story["args"], "value" | "onChange">;

// Controlled input example
export function Default(props: StoryArgs) {
  const [value, setValue] = useState("");
  return (
    <div>
      <Input {...props} value={value} onChange={setValue} className="input" />
      <style scoped>{`
        :scope {
          width: 100%;
          .input {
            font-size: 0.8rem;
            color: var(--ds-color-text-muted);
          }
        } 
      `}</style>
    </div>
  );
}

// Different input types
export function InputTypes() {
  const [emailValue, setEmailValue] = useState("");
  const [passwordValue, setPasswordValue] = useState("");
  const [numberValue, setNumberValue] = useState("");
  const [urlValue, setUrlValue] = useState("");
  const [textValue, setTextValue] = useState("");

  return (
    <div className="input-types">
      <Input
        value={textValue}
        onChange={setTextValue}
        placeholder="Text input (default)"
      />
      <Input
        type="email"
        value={emailValue}
        onChange={setEmailValue}
        placeholder="Email input"
      />
      <Input
        type="password"
        value={passwordValue}
        onChange={setPasswordValue}
        placeholder="Password input"
      />
      <Input
        type="number"
        value={numberValue}
        onChange={setNumberValue}
        placeholder="Number input"
      />
      <Input
        type="url"
        value={urlValue}
        onChange={setUrlValue}
        placeholder="URL input"
      />
      <style scoped>{`
        .input-types {
          display: flex;
          flex-direction: column;
          gap: 10px;
          width: 100%;
        }
      `}</style>
    </div>
  );
}

// With icon
export function WithIcon() {
  const [searchValue, setSearchValue] = useState("");

  return (
    <div style={{ width: "300px" }}>
      <Input
        value={searchValue}
        onChange={setSearchValue}
        placeholder="Search..."
      >
        <TextField.Slot side="left">
          <BellIcon height={16} width={16} />
        </TextField.Slot>
      </Input>
    </div>
  );
}

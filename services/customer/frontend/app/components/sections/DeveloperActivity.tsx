import { useMemo } from "react";
import { Box, Flex, Grid } from "@radix-ui/themes";
import { t } from "../../utils/helpers";
import { useAppStore } from "../../utils/app-store";
import { between } from "../../utils/data";
import { useDateFilter } from "../../utils/date";
import { getDateValueList } from "../../utils/value-calculations";
import SectionLayout from "../global/SectionLayout";
import ChartCard from "../cards/ChartCard";
import AreaChart from "../charts/AreaChart";
import type { DateAndCount } from "../../../../../request_insight/analytics/request_insight_analytics_pb";
import { useQuery } from "@tanstack/react-query";
import { dateRangeQuery } from "app/client-cache/queries/dateRange";

export default function DeveloperActivity({
  title,
  accentColor,
}: {
  title: string;
  accentColor: string;
}) {
  const earliestData = useAppStore((state) => state.earliestData);

  const [
    developerActivityFilter,
    setDeveloperActivityFilter,
    startDate,
    endDate,
  ] = useDateFilter(earliestData, "Adoption", "developerActivityFilter");

  const filter = useMemo(() => {
    return between(startDate, endDate);
  }, [startDate, endDate]);

  const { data: activeUsersData } = useQuery(
    dateRangeQuery<DateAndCount[]>("active-users", startDate, endDate),
  );

  const activeUsersValues = useMemo(() => {
    if (!activeUsersData) {
      return null;
    }

    return getDateValueList(activeUsersData, "count", filter);
  }, [activeUsersData, filter]);

  return (
    <SectionLayout
      title={title}
      activeFilter={developerActivityFilter}
      setActiveFilter={setDeveloperActivityFilter}
    >
      <Grid
        columns={{ initial: "1", md: "2" }}
        gap="4"
        width="auto"
        height="auto"
      >
        <Box gridRow="span 2" height="400px">
          <ChartCard
            title={t("dailyActiveUsers").title}
            granularity={t("daily").title}
            rollingAvg={t("sevenDayAverage").title}
          >
            <AreaChart
              color={accentColor}
              showTrendLine={true}
              showAxis={true}
              data={activeUsersValues}
            />
          </ChartCard>
        </Box>

        <Flex direction="column" gap="4"></Flex>
      </Grid>
    </SectionLayout>
  );
}

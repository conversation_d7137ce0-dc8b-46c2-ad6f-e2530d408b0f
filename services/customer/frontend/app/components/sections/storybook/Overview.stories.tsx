import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Theme } from "@radix-ui/themes";
import { createRemixStub } from "@remix-run/testing";
import { GeneralErrorBoundary } from "../../GeneralErrorBoundary";
import Overview from "../Overview";
import {
  generateDateAndCounts,
  generateChatStats,
  generateEditStats,
  generateKeywordsStats,
} from "../../../storybook-data";

const meta: Meta = {
  title: "Sections/Overview",
  component: Overview,
  decorators: [
    (Story) => {
      // This is how you make remix-specific routes, loaders and actions etc.
      // This works for all remix utilities e.g. useLoaderData and even fetchers!
      // To see full usage check out the @remix-run/testing tests - the docs are not updated yet for this package.
      // https://github.com/remix-run/remix/blob/main/packages/remix-testing/__tests__/stub-test.tsx
      const RemixStub = createRemixStub([
        {
          path: "/",
          Component: Story,
          ErrorBoundary: GeneralErrorBoundary,
          action: () => {
            throw new Response("Unauthorised", {
              status: 401,
            });
          },
          loader: () => {
            return {
              overviewFilter: "7",
              devDaysQuery: new Promise((resolve) =>
                setTimeout(
                  () =>
                    resolve({
                      devDays: generateDateAndCounts(7),
                    }),
                  1000,
                ),
              ),
              activeUsersQuery: Promise.resolve({
                activeUsers: generateDateAndCounts(7),
              }),
              chatStatsQuery: Promise.resolve({
                chatStats: generateChatStats(7),
              }),
              editStatsQuery: Promise.resolve({
                editStats: generateEditStats(7),
              }),
              topInstructionsQuery: new Promise((resolve) =>
                setTimeout(
                  () =>
                    resolve({
                      keywordsStats: generateKeywordsStats(10),
                    }),
                  1000,
                ),
              ),
            };
          },
        },
      ]);

      return <RemixStub />;
    },
  ],
  parameters: {
    layout: "fullscreen",
    hasLocalStub: true,
  },
  tags: ["autodocs"],
  argTypes: {
    title: {
      description: "The title of the layout",
      control: "text",
    },
    description: {
      description: "The description of the layout",
      control: "text",
    },
    accentColor: {
      description:
        "The radix color to use as an accent --  https://www.radix-ui.com/colors",
      control: "color",
    },
    defaultActiveFilter: {
      description: "The default active filter",
      control: "inline-radio",
      options: ["7", "30", "60", "all"],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  parameters: {
    loaderData: {
      overviewFilter: "7",
    },
  },
  args: {
    title: "Overview",
    description: "Augment's impact at your organization",
    accentColor: "indigo",
  },
  // @ts-expect-error -- StoryObj type is not compatible with Story type, args are spread operated
  render: (args) => <Overview {...args} />,
};

export const DarkMode: Story = {
  args: {
    title: "Overview",
    description: "Augment's impact at your organization",
    accentColor: "indigo",
  },

  render: (args: any) => (
    <Theme appearance="dark">
      <Overview {...args} />
    </Theme>
  ),
};

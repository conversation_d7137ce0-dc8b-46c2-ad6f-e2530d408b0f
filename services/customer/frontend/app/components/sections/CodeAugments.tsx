import { useMemo } from "react";
import { Box, Card, Grid } from "@radix-ui/themes";
import { t } from "../../utils/helpers";
import { useAppStore } from "../../utils/app-store";
import { between } from "../../utils/data";
import { useDateFilter } from "../../utils/date";
import { combineSums, sumDateValueLists } from "../../utils/value-calculations";
import SectionLayout from "../global/SectionLayout";
import ChartCard from "../cards/ChartCard";
import AreaChart from "../charts/AreaChart";
import Metric from "../cards/Metric";
import type {
  GetCompletionStatsResponse_CompletionStats,
  GetEditStatsResponse_EditStats,
} from "../../../../../request_insight/analytics/request_insight_analytics_pb";
import { useQuery } from "@tanstack/react-query";
import { dateRangeQuery } from "app/client-cache/queries/dateRange";

export default function CodeAugments({
  title,
  color,
}: {
  title: string;
  color: string;
}) {
  const earliestData = useAppStore((state) => state.earliestData);

  const [codeAugmentsFilter, setCodeAugmentsFilter, startDate, endDate] =
    useDateFilter(earliestData, "CodeChanges", "codeAugmentsFilter");

  const filter = useMemo(() => {
    return between(startDate, endDate);
  }, [startDate, endDate]);

  const { data: completionStatsData } = useQuery(
    dateRangeQuery<GetCompletionStatsResponse_CompletionStats[]>(
      "completion-stats",
      startDate,
      endDate,
    ),
  );

  const { data: editStatsData } = useQuery(
    dateRangeQuery<GetEditStatsResponse_EditStats[]>(
      "edit-stats",
      startDate,
      endDate,
    ),
  );

  const dailyCompletionsAndInstructionsValues = useMemo(() => {
    if (
      !completionStatsData ||
      !editStatsData ||
      completionStatsData.length !== editStatsData.length
    ) {
      return null;
    }

    return sumDateValueLists(
      completionStatsData,
      "requestCount",
      editStatsData,
      "requestCount",
      filter,
    );
  }, [completionStatsData, editStatsData, filter]);

  const codeLinesValue = useMemo(() => {
    if (!editStatsData || !completionStatsData) {
      return null;
    }

    return combineSums(
      editStatsData,
      "acceptedLineCount",
      completionStatsData,
      "acceptedLineCount",
      filter,
    ).toLocaleString();
  }, [editStatsData, completionStatsData, filter]);

  const codeLinesValues = useMemo(() => {
    if (!editStatsData || !completionStatsData) {
      return null;
    }

    return sumDateValueLists(
      editStatsData,
      "acceptedLineCount",
      completionStatsData,
      "acceptedLineCount",
      filter,
    );
  }, [editStatsData, completionStatsData, filter]);

  return (
    <SectionLayout
      title={title}
      activeFilter={codeAugmentsFilter}
      setActiveFilter={setCodeAugmentsFilter}
    >
      <Grid columns={{ initial: "1", md: "2" }} gap="4">
        <Box height="400px" gridRow="span 2">
          <ChartCard
            title={t("completionsAndInstructions").title}
            granularity={t("daily").title}
            rollingAvg={t("sevenDayAverage").title}
          >
            <AreaChart
              color={color}
              showAxis={true}
              showTrendLine={true}
              data={dailyCompletionsAndInstructionsValues}
            />
          </ChartCard>
        </Box>

        <Box height="400px" gridRow="span 2">
          <Card variant="classic" style={{ height: "100%", width: "100%" }}>
            <Grid columns="1" height="100%" gap="4">
              <Metric title={t("codeLines").title} value={codeLinesValue} />
              <AreaChart
                data={codeLinesValues}
                showTrendLine={false}
                showAxis={false}
                color={color}
              />
            </Grid>
          </Card>
        </Box>
      </Grid>
    </SectionLayout>
  );
}

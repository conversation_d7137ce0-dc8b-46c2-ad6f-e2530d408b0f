import { useMemo } from "react";
import { Box, Grid } from "@radix-ui/themes";
import { t } from "../../utils/helpers";
import { useAppStore } from "../../utils/app-store";
import { between } from "../../utils/data";
import { useDateFilter } from "../../utils/date";
import { getDateValueList, getTopList } from "../../utils/value-calculations";
import SectionLayout from "../global/SectionLayout";
import ChartCard from "../cards/ChartCard";
import AreaChart from "../charts/AreaChart";
import BarChart from "../charts/BarChart";
import type {
  GetCategoriesStatsResponse_CategoryStats,
  GetEditStatsResponse_EditStats,
} from "../../../../../request_insight/analytics/request_insight_analytics_pb";
import { useQuery } from "@tanstack/react-query";
import {
  dateRangeQuery,
  dateRangeRequestTypeQueryOptions,
} from "app/client-cache/queries/dateRange";

export default function Instructions({
  title,
  color,
}: {
  title: string;
  color: string;
}) {
  const earliestData = useAppStore((state) => state.earliestData);

  const [instructionsFilter, setInstructionsFilter, startDate, endDate] =
    useDateFilter(earliestData, "CodeChanges", "instructionsFilter");

  const filter = useMemo(() => {
    return between(startDate, endDate);
  }, [startDate, endDate]);

  const { data: editStatsData } = useQuery(
    dateRangeQuery<GetEditStatsResponse_EditStats[]>(
      "edit-stats",
      startDate,
      endDate,
    ),
  );

  const { data: instructionCategoriesData } = useQuery(
    dateRangeRequestTypeQueryOptions<
      GetCategoriesStatsResponse_CategoryStats[]
    >("categories-stats", startDate, endDate, "EDIT"),
  );

  const dailyInstructionsValues = useMemo(() => {
    if (!editStatsData) {
      return null;
    }

    return getDateValueList(editStatsData, "requestCount", filter);
  }, [editStatsData, filter]);

  const topInstructionCategories = useMemo(() => {
    if (!instructionCategoriesData) {
      return null;
    }

    return getTopList(instructionCategoriesData, "category", "count", 15);
  }, [instructionCategoriesData]);

  return (
    <SectionLayout
      title={title}
      activeFilter={instructionsFilter}
      setActiveFilter={setInstructionsFilter}
    >
      <Grid
        columns={{ initial: "1", md: "2" }}
        gap="4"
        width="auto"
        height="auto"
      >
        <Box gridRow="span 2" height="400px">
          <ChartCard
            title={t("dailyCodeInstructions").title}
            granularity={t("daily").title}
            rollingAvg={t("sevenDayAverage").title}
          >
            <AreaChart
              color={color}
              showTrendLine={true}
              showAxis={true}
              data={dailyInstructionsValues}
            />
          </ChartCard>
        </Box>

        <Box gridRow="span 2" height="400px">
          <ChartCard title="Top instruction types">
            <BarChart color={color} data={topInstructionCategories} />
          </ChartCard>
        </Box>
      </Grid>
    </SectionLayout>
  );
}

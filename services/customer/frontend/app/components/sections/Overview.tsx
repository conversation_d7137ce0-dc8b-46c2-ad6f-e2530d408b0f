import { useMemo } from "react";
import { Box, Flex, Grid } from "@radix-ui/themes";
import { t } from "../../utils/helpers";
import { useAppStore } from "../../utils/app-store";
import { between } from "../../utils/data";
import { useDateFilter } from "../../utils/date";
import { getDateValueList, getTopList } from "../../utils/value-calculations";
import SectionLayout from "../global/SectionLayout";
import ChartCard from "../cards/ChartCard";
import AreaChart from "../charts/AreaChart";
import BarChart from "../charts/BarChart";
import type {
  DateAndCount,
  GetCategoriesStatsResponse_CategoryStats,
} from "../../../../../request_insight/analytics/request_insight_analytics_pb";
import {
  dateRangeQuery,
  dateRangeRequestTypeQueryOptions,
} from "app/client-cache/queries/dateRange";
import { useQuery } from "@tanstack/react-query";

export default function Overview({
  title,
  description,
}: {
  title: string;
  description: string;
}) {
  const earliestData = useAppStore((state) => state.earliestData);

  const [overviewFilter, setOverviewFilter, startDate, endDate] = useDateFilter(
    earliestData,
    "Overview",
    "overviewFilter",
  );

  const filter = useMemo(() => {
    return between(startDate, endDate);
  }, [startDate, endDate]);

  const { data: chatCategoriesData } = useQuery(
    dateRangeRequestTypeQueryOptions<
      GetCategoriesStatsResponse_CategoryStats[]
    >("categories-stats", startDate, endDate, "CHAT"),
  );

  const { data: instructionCategoriesData } = useQuery(
    dateRangeRequestTypeQueryOptions<
      GetCategoriesStatsResponse_CategoryStats[]
    >("categories-stats", startDate, endDate, "EDIT"),
  );

  const { data: activeUsersData } = useQuery(
    dateRangeQuery<DateAndCount[]>("active-users", startDate, endDate),
  );

  const topChatCategories = useMemo(() => {
    if (!chatCategoriesData) {
      return null;
    }

    return getTopList(chatCategoriesData, "category", "count");
  }, [chatCategoriesData]);

  const topInstructionCategories = useMemo(() => {
    if (!instructionCategoriesData) {
      return null;
    }

    return getTopList(instructionCategoriesData, "category", "count");
  }, [instructionCategoriesData]);

  const activeUsersValues = useMemo(() => {
    if (!activeUsersData) {
      return null;
    }

    return getDateValueList(activeUsersData, "count", filter);
  }, [activeUsersData, filter]);

  return (
    <SectionLayout
      title={title}
      description={description}
      activeFilter={overviewFilter}
      setActiveFilter={setOverviewFilter}
    >
      <Grid
        columns={{ initial: "1", md: "2" }}
        gap="4"
        width="auto"
        height="auto"
      >
        <Flex direction="column" gap="4" gridRow="span 2" height="400px">
          <ChartCard title={"Top chat message types"}>
            <BarChart color="amber" data={topChatCategories} />
          </ChartCard>
          <ChartCard title={"Top Instructions"}>
            <BarChart color="bronze" data={topInstructionCategories} />
          </ChartCard>
        </Flex>
        <Box gridRow="span 2" height="400px">
          <ChartCard
            title={t("averageDailyActiveUsers").title}
            granularity={t("daily").title}
            rollingAvg={t("sevenDayAverage").title}
          >
            <AreaChart
              color="indigo"
              showTrendLine={true}
              showAxis={true}
              data={activeUsersValues}
            />
          </ChartCard>
        </Box>
      </Grid>
    </SectionLayout>
  );
}

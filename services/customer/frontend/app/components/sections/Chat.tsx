import { useMemo } from "react";
import { Box, Grid } from "@radix-ui/themes";
import { t } from "../../utils/helpers";
import { useAppStore } from "../../utils/app-store";
import { between } from "../../utils/data";
import { useDateFilter } from "../../utils/date";
import { getDateValueList, getTopList } from "../../utils/value-calculations";
import SectionLayout from "../global/SectionLayout";
import BarChart from "../charts/BarChart";
import ChartCard from "../cards/ChartCard";
import AreaChart from "../charts/AreaChart";
import type {
  GetChatStatsResponse_ChatStats,
  GetCategoriesStatsResponse_CategoryStats,
} from "../../../../../request_insight/analytics/request_insight_analytics_pb";
import { useQuery } from "@tanstack/react-query";
import {
  dateRangeQuery,
  dateRangeRequestTypeQueryOptions,
} from "app/client-cache/queries/dateRange";

export default function Chat({
  title,
  accentColor,
}: {
  title: string;
  accentColor: string;
}) {
  const earliestData = useAppStore((state) => state.earliestData);

  const [chatFilter, setChatFilter, startDate, endDate] = useDateFilter(
    earliestData,
    "Adoption",
    "chatFilter",
  );

  const filter = useMemo(() => {
    return between(startDate, endDate);
  }, [startDate, endDate]);

  const { data: chatStatsData } = useQuery(
    dateRangeQuery<GetChatStatsResponse_ChatStats[]>(
      "chat-stats",
      startDate,
      endDate,
    ),
  );

  const { data: chatCategoriesData } = useQuery(
    dateRangeRequestTypeQueryOptions<
      GetCategoriesStatsResponse_CategoryStats[]
    >("categories-stats", startDate, endDate, "CHAT"),
  );

  const dailyChatValues = useMemo(() => {
    if (!chatStatsData) {
      return null;
    }

    return getDateValueList(chatStatsData, "requestCount", filter);
  }, [chatStatsData, filter]);

  const topChatMessageTypes = useMemo(() => {
    if (!chatCategoriesData) {
      return null;
    }

    return getTopList(chatCategoriesData, "category", "count", 15);
  }, [chatCategoriesData]);

  return (
    <SectionLayout
      title={title}
      activeFilter={chatFilter}
      setActiveFilter={setChatFilter}
    >
      <Grid
        columns={{ initial: "1", md: "2" }}
        gap="4"
        width="auto"
        height="auto"
      >
        <Box gridRow="span 2" height="400px">
          <ChartCard
            title={t("dailyChatMessages").title}
            granularity={t("daily").title}
            rollingAvg={t("sevenDayAverage").title}
          >
            <AreaChart
              color={accentColor}
              showTrendLine={true}
              showAxis={true}
              data={dailyChatValues}
            />
          </ChartCard>
        </Box>
        <Box gridRow="span 2">
          <ChartCard title={t("topChatMessageTypes").title}>
            <BarChart color={accentColor} data={topChatMessageTypes} />
          </ChartCard>
        </Box>
      </Grid>
    </SectionLayout>
  );
}

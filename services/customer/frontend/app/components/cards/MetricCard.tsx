import { Card } from "@radix-ui/themes";

import Metric from "./Metric";

interface MetricCardProps {
  title: string;
  definition?: string;
  value: string | null;
  trend?: number;
  py?: string;
}

export default function MetricCard({
  title,
  definition,
  value,
  trend,
  py = "4",
}: MetricCardProps) {
  return (
    <Card variant="classic">
      <Metric
        title={title}
        definition={definition}
        value={value}
        trend={trend}
        py={py}
      />
    </Card>
  );
}

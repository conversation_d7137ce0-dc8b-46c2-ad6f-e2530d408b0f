import type { <PERSON>a, StoryObj } from "@storybook/react";
import MetricCard from "../MetricCard";

const meta = {
  title: "Cards/Metric",
  component: MetricCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    title: {
      description: "Title of the card",
      control: { type: "text" },
    },
    definition: {
      description: "Definition of the card",
      control: { type: "text" },
    },
    trend: {
      description: "Trend of the card",
      control: { type: "number" },
    },
  },
  // Default Values
  args: {
    title: "Title",
    definition: "Definition",
    trend: 22,
  },
} satisfies Meta<typeof MetricCard>;

export default meta;

type Story = StoryObj<typeof meta>;

// Stories
export const Default: Story = {
  args: {
    value: "297",
  },
};

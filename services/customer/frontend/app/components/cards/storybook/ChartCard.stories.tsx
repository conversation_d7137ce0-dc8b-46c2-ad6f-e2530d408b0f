import type { Meta, StoryFn } from "@storybook/react";
import ChartCard, { type ChartCardProps } from "../ChartCard";
import Bar<PERSON>hart from "../../../components/charts/BarChart";
import AreaChart from "../../../components/charts/AreaChart";
import { generateChartData, generateKeyData } from "../../../storybook-data";

const meta = {
  title: "Cards/Chart",
  component: ChartCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    children: {
      description: "The children of the card",
      control: false,
    },
    title: {
      description: "The title of the card",
      control: { type: "text" },
    },
    trendValue: {
      description: "The value of the trend",
      control: { type: "number" },
    },
  },
  args: {
    title: "Top Instructions",
  },
} satisfies Meta<typeof ChartCard>;

export default meta;

// type Story = StoryObj<typeof meta>
const Template: StoryFn<ChartCardProps> = (args) => (
  <div style={{ width: "500px", height: "300px" }}>
    <ChartCard {...args} />
  </div>
);

// Stories
export const Area = Template.bind({});
Area.args = {
  title: "Developer Days",
  trendValue: 10,
  children: (
    <AreaChart
      color={"indigo"}
      showTrendLine={true}
      showAxis={true}
      data={
        (
          await generateChartData({
            timeFrame: "60",
            interval: "days",
          })
        ).data || []
      }
    />
  ),
};

export const Bar = Template.bind({});
Bar.args = {
  title: "Top Instructions",
  children: (
    <BarChart
      color={"bronze"}
      data={await generateKeyData({
        labels: ["Refactor", "Explain", "Test", "Optimize", "Fix"],
        timeFrame: "30",
      })}
    />
  ),
};

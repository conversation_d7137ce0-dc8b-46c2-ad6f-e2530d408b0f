import { Badge, Card, Flex, Skeleton } from "@radix-ui/themes";
import { ShadowInnerIcon } from "@radix-ui/react-icons";
import CardHeader from "./CardHeader";
import { TrendBadge } from "../ui/Badge";

export interface ChartCardProps {
  children: React.ReactNode;
  title: string;
  granularity?: string;
  rollingAvg?: string;
  trendValue?: number | null;
  style?: React.CSSProperties;
}

export default function ChartCard({
  children,
  title,
  granularity,
  rollingAvg,
  trendValue,
  style,
}: ChartCardProps) {
  return (
    <Card variant="classic" style={{ height: "100%", width: "100%", ...style }}>
      <Flex gap="0" direction="column" px="2" justify="center" height="100%">
        <CardHeader title={title}>
          {trendValue && (
            <Skeleton loading={!trendValue} height="100%">
              <TrendBadge value={trendValue} />
            </Skeleton>
          )}
          {granularity && (
            <Badge size="1" color="blue">
              <ShadowInnerIcon />
              {granularity}
            </Badge>
          )}
          {rollingAvg && (
            <Badge size="1" color="gray">
              <ShadowInnerIcon />
              {rollingAvg}
            </Badge>
          )}
        </CardHeader>
        {children}
      </Flex>
    </Card>
  );
}

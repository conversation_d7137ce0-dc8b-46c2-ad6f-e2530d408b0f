import { Flex, Grid, Skeleton, Text } from "@radix-ui/themes";
import { TrendBadge } from "../ui/Badge";
import MetricMetadata from "../ui/MetricMetadata";

interface MetricProps {
  title: string;
  definition?: string;
  value: string | null;
  trend?: number;
  py?: string;
}

export default function Metric({
  title,
  definition,
  value,
  trend,
  py = "4",
}: MetricProps) {
  return (
    <Grid columns="1" height="100%" gap="2">
      <Flex
        py={py}
        gap="3"
        direction="column"
        justify="center"
        className="md:col-span-2"
      >
        <Flex
          align={{ initial: "start", md: "end" }}
          direction={{ initial: "column-reverse", md: "row" }}
          gap="2"
        >
          {value === null ? (
            <Skeleton width="100%" height="40px" />
          ) : (
            <Text size="8" weight="bold">
              {value}
            </Text>
          )}
          {trend && <TrendBadge value={trend} />}
        </Flex>
        <MetricMetadata title={title} definition={definition} />
      </Flex>
    </Grid>
  );
}

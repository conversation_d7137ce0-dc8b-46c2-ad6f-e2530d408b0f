import { <PERSON>, <PERSON>ton, Code, Flex, Text } from "@radix-ui/themes";

// Component for displaying and managing the JWT keypair
export const KeypairSetup = ({
  publicKey,
  onGenerateKeyPair,
  isGenerating = false,
}: {
  publicKey?: string;
  onGenerateKeyPair: () => void;
  isGenerating?: boolean;
}) => {
  return (
    <Box>
      <Text size="2" weight="bold" mb="4">
        Glean-Augment Public Key
      </Text>
      <Flex direction="column" gap="3" mt="3">
        <Text size="2">
          {publicKey
            ? "Current public key for JWT authentication:"
            : "No keypair exists for JWT authentication."}
        </Text>

        {publicKey && (
          <Box
            style={{
              maxWidth: "100%",
              overflow: "auto",
              padding: "8px",
              backgroundColor: "var(--gray-2)",
              borderRadius: "4px",
              marginBottom: "8px",
            }}
          >
            <Code
              size="2"
              style={{
                whiteSpace: "pre-wrap",
                wordBreak: "break-all",
              }}
            >
              {publicKey}
            </Code>
          </Box>
        )}

        <Box mt="2">
          <Flex direction="column" gap="2">
            <Button
              onClick={onGenerateKeyPair}
              disabled={isGenerating}
              color="orange"
            >
              {isGenerating
                ? "Generating..."
                : publicKey
                  ? "Regenerate Keypair"
                  : "Generate Keypair"}
            </Button>
            {publicKey ? (
              <Text size="1" color="gray">
                Warning: Regenerating the keypair will invalidate any existing
                integrations using the current keypair.
              </Text>
            ) : null}
          </Flex>
        </Box>
      </Flex>
    </Box>
  );
};

export default KeypairSetup;

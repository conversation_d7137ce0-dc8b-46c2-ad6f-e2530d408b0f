import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { KeypairSetup } from "./KeypairSetup";

const meta: Meta<typeof KeypairSetup> = {
  title: "Glean/KeypairSetup",
  component: KeypairSetup,
  parameters: {
    layout: "centered",
  },
  decorators: [
    (Story) => (
      <div style={{ width: "400px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const NoKeypair: Story = {
  args: {
    publicKey: undefined,
    onGenerateKeyPair: () => console.info("Generate keypair clicked"),
    isGenerating: false,
  },
};

export const WithKeypair: Story = {
  args: {
    publicKey: `-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEcJriKLwDmKiweF7j6UCUwTJNWVXz
KXouRDYCkAVYpZ1uYyOPIjw0cdpeENmFYeJMxRfSsSmLf3VF1JzBjND0aQ==
-----E<PERSON> PUBLIC KEY-----`,
    onGenerateKeyPair: () => console.info("Generate keypair clicked"),
    isGenerating: false,
  },
};

export const Generating: Story = {
  args: {
    publicKey: undefined,
    onGenerateKeyPair: () => console.info("Generate keypair clicked"),
    isGenerating: true,
  },
};

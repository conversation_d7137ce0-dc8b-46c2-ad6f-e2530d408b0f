import { useEffect, useRef, useState } from "react";
import { toast } from "../ui/Toast";

import { Box, Button, Flex } from "@radix-ui/themes";
import { useActionData, useLoaderData, useSubmit } from "@remix-run/react";

import type { LoaderData } from "../../routes/_layout.dashboard.glean";

import SectionHeader from "../global/SectionHeader";
import { toFormData } from "app/utils/url";
import { EditView } from "./GleanForm";
import { InstructionsPanel } from "./GleanInstructions";
import { StatusView } from "./GleanStatus";
import { KeypairSetup } from "./KeypairSetup";

export interface GleanConfig {
  gleanDomain: string;
}

export default function GleanSetupSection() {
  const { settings } = useLoaderData<LoaderData>();
  const actionData = useActionData<{
    error?: string;
    publicKey?: string;
    success?: boolean;
  }>();
  const submit = useSubmit();
  const isInitialLoad = useRef(true);
  const savedGleanDomain = useRef<string | null>(null);

  // Initialize with consistent default values for server and client
  const [showInstructions, setShowInstructions] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [gleanDomain, setGleanDomain] = useState("");
  const [isGeneratingKeypair, setIsGeneratingKeypair] = useState(false);
  const [publicKey, setPublicKey] = useState<string | undefined>(undefined);

  const [config, setConfig] = useState<GleanConfig>({
    gleanDomain: "",
  });

  // State to track form validity
  const [formValidity, setFormValidity] = useState<boolean | string>(false);

  // Function to validate the form
  const validateDomain = (domain: string): boolean | string => {
    // Verify the gleanDomain is properly formatted as a URL
    // with an HTTPS scheme and a full domain, with no path or query parameters

    if (domain === "") {
      // Empty domain for deletion is allowed.
      return true;
    }

    try {
      const url = new URL(domain.trim());

      // Check for HTTPS scheme
      if (url.protocol !== "https:") {
        return "URL must use HTTPS protocol";
      }

      // Check for no path (or just '/')
      if (!(url.pathname === "/" || url.pathname === "")) {
        return "URL should not contain a path";
      }

      // Check for no query parameters
      if (url.search !== "") {
        return "URL should not contain query parameters";
      }

      // Check for no hash
      if (url.hash !== "") {
        return "URL should not contain a hash fragment";
      }

      return true;
    } catch (error) {
      // If URL parsing fails, the input is not a valid URL
      return "Please enter a valid URL format";
    }
  };

  // Update state once loader data is available.
  useEffect(() => {
    if (settings) {
      // Only update if settings is available
      const domain = settings.gleanDomain || "";
      setGleanDomain(domain);
      setPublicKey(settings.publicKey);

      // Create updated config without referencing the current config state
      const updatedConfig = {
        gleanDomain: domain,
        publicKey: settings.publicKey,
      };
      setConfig(updatedConfig);

      // Validate the form with the initial config
      setFormValidity(validateDomain(updatedConfig.gleanDomain));
    }
  }, [settings]);

  const handleConfigChange = (field: keyof GleanConfig, value: string) => {
    // Update config state
    const updatedConfig = { ...config, [field]: value };
    setConfig(updatedConfig);

    // Validate form with the updated config
    const validity = validateDomain(updatedConfig.gleanDomain);
    setFormValidity(validity);
  };

  const handleSave = async () => {
    savedGleanDomain.current = config.gleanDomain; // Store the glean domain being saved

    const formData = toFormData({
      action: "updateSettings",
      settings: JSON.stringify({
        gleanDomain: config.gleanDomain,
      }),
    });

    submit(formData, { method: "post" });
  };

  const handleGenerateKeyPair = () => {
    setIsGeneratingKeypair(true);

    const formData = toFormData({
      action: "generateKeyPair",
    });

    submit(formData, { method: "post" });
  };

  useEffect(() => {
    if (actionData?.error) {
      toast.error({
        title: "Error Saving Glean Configuration.",
        description: actionData.error,
      });
      setIsGeneratingKeypair(false);
    }

    if (actionData?.success && actionData?.publicKey) {
      setPublicKey(actionData.publicKey);
      setIsGeneratingKeypair(false);
      toast.success({
        title: "Keypair Generated",
        description:
          "Your JWT authentication keypair has been successfully generated.",
      });
    }
  }, [actionData?.error, actionData?.success, actionData?.publicKey]);

  useEffect(() => {
    if (isInitialLoad.current) {
      isInitialLoad.current = false;
      return;
    }

    if (actionData && !actionData.error) {
      setIsEditing(false);
      setConfig(() => ({
        clientId: "",
        clientSecret: "",
        gleanDomain: savedGleanDomain.current!,
        subdomain: "",
      }));
      setGleanDomain(savedGleanDomain.current!);
      toast.success({
        title: "Configuration Saved",
        description: "Your Glean configuration has been successfully saved.",
      });
    }
  }, [actionData]);

  return (
    <Flex direction="column" gap="6">
      <SectionHeader
        title="Glean Setup"
        description="Connect your Glean instance to enable enhanced search capabilities. This feature is currently invite-only - please contact support for access."
      />

      <Flex gap="6">
        <Flex
          direction="column"
          gap="4"
          style={{ width: "400px", flexShrink: 0 }}
        >
          <Box>
            <Button
              size="2"
              variant="soft"
              style={{ width: "100%" }}
              onClick={() => setShowInstructions(!showInstructions)}
              mt="4"
            >
              {showInstructions
                ? "Hide Setup Instructions"
                : "View Setup Instructions"}
            </Button>
          </Box>
          {!isEditing ? (
            <>
              <StatusView
                gleanDomain={gleanDomain!}
                onEdit={() => setIsEditing(true)}
              />
            </>
          ) : (
            <EditView
              config={config}
              onConfigChange={handleConfigChange}
              onSave={handleSave}
              onCancel={() => setIsEditing(false)}
              isFormValid={formValidity}
            />
          )}

          {/* Always show the keypair setup section */}
          <Box mt="6">
            <KeypairSetup
              publicKey={publicKey}
              onGenerateKeyPair={handleGenerateKeyPair}
              isGenerating={isGeneratingKeypair}
            />
          </Box>
        </Flex>

        {showInstructions && (
          <InstructionsPanel onClose={() => setShowInstructions(false)} />
        )}
      </Flex>
    </Flex>
  );
}

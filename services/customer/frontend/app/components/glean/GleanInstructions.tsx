import { Box, Button, Flex, Text } from "@radix-ui/themes";
import { instructions } from "./GleanInstructionsData";

function Instructions() {
  return (
    <Flex direction="column" gap="4">
      {instructions.map((step, index) => (
        <Box key={index}>
          <Text size="2">
            {index + 1}. {step.content}
          </Text>
        </Box>
      ))}
    </Flex>
  );
}

export const InstructionsPanel = ({ onClose }: { onClose: () => void }) => (
  <Box
    style={{
      flex: 1,
      maxWidth: "800px",
      maxHeight: "calc(100vh - 200px)",
      padding: "24px",
      borderRadius: "6px",
      backgroundColor: "var(--gray-2)",
      overflow: "auto",
      position: "sticky",
      top: "24px",
    }}
  >
    <Flex direction="column" gap="3">
      <Text size="5" weight="bold">
        Instructions to Allow Augment to Access Glean
      </Text>
    </Flex>

    <Box my="4">
      <Instructions />
    </Box>

    <Flex gap="3" justify="end">
      <Button variant="soft" color="gray" onClick={onClose}>
        Close
      </Button>
    </Flex>
  </Box>
);

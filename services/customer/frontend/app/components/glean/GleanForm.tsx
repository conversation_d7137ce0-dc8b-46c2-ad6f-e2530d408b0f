import { <PERSON>, <PERSON>ton, <PERSON>lex, <PERSON>, <PERSON>, TextField } from "@radix-ui/themes";
import type { GleanConfig } from "./GleanSetup";

export type EditViewProps = {
  config: GleanConfig;
  onConfigChange: (field: keyof GleanConfig, value: string) => void;
  onSave: () => void;
  onCancel: () => void;
  isFormValid: boolean | string;
};

export function EditView({
  config,
  onConfigChange,
  onSave,
  onCancel,
  isFormValid,
}: EditViewProps) {
  return (
    <Box>
      <Box mt="4">
        <Flex direction="column" gap="4">
          <Box>
            <Text size="2" weight="bold" mb="2">
              Enter your Glean Server Instance URL
            </Text>
            <TextField.Root
              placeholder="https://example-be.glean.com"
              value={config.gleanDomain}
              onChange={(e) => onConfigChange("gleanDomain", e.target.value)}
            />
            <Flex align="center" gap="1" mt="1">
              <Text size="1" color="gray">
                Found in Glean&apos;s Admin Console
              </Text>
              <Link
                size="1"
                href="https://app.glean.com/admin/about-glean"
                target="_blank"
              >
                here
              </Link>
            </Flex>
          </Box>

          <Flex direction="column" gap="2" mt="4">
            {typeof isFormValid === "string" && (
              <Text size="1" color="red">
                {isFormValid}
              </Text>
            )}
            <Flex gap="3">
              <Button onClick={onSave} disabled={isFormValid !== true}>
                Save Changes
              </Button>
              <Button variant="soft" color="gray" onClick={onCancel}>
                Cancel
              </Button>
            </Flex>
          </Flex>
        </Flex>
      </Box>
    </Box>
  );
}

import { <PERSON>, Button, Flex, Text } from "@radix-ui/themes";

export const StatusView = ({
  gleanDomain,
  onEdit,
}: {
  gleanDomain: string;
  onEdit: () => void;
}) => (
  <Box>
    <Text size="2" weight="bold" mb="4">
      Current Configuration
    </Text>
    <Flex direction="column" gap="3" mt="3">
      <Flex justify="between" align="center">
        <Text size="2" weight="bold">
          Glean Domain
        </Text>
        <Text size="2" weight="medium">
          {gleanDomain || "No Glean Domain Configured"}
        </Text>
      </Flex>
      <Box mt="4">
        <Button onClick={onEdit}>Update Configuration</Button>
      </Box>
    </Flex>
  </Box>
);

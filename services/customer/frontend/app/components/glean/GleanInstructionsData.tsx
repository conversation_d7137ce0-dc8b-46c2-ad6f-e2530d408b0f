import { Link } from "@radix-ui/themes";
import type React from "react";

interface InstructionStep {
  content: React.ReactNode;
}

// Steps for setting up the JWT authentication with Glean
const getJwtAuthSetupSteps = (): InstructionStep[] => [
  {
    content: (
      <>
        Navigate to{" "}
        <Link
          href="https://app.glean.com/admin/platform/tokenManagement?tab=client"
          target="_blank"
          rel="noopener noreferrer"
        >
          Glean&apos;s API Token Access page.
        </Link>
      </>
    ),
  },
  {
    content: (
      <>
        Under &quot;Partner Authentication via Key Pair&quot;, click the{" "}
        <strong>Manage Settings</strong> button.
      </>
    ),
  },
  {
    content: (
      <>
        Paste the public key you generated in the previous step into the
        provided field.
      </>
    ),
  },
];

export const instructions = [
  {
    content: (
      <>
        Enter and save your Glean domain in the configuration form. This should
        be the URL of your Glean backend server (e.g.,{" "}
        <code>https://example-be.glean.com</code>). You can find this in{" "}
        <Link
          href="https://app.glean.com/admin/about-glean"
          target="_blank"
          rel="noopener noreferrer"
        >
          Glean&apos;s Admin Console under &quot;About Glean&quot; &gt;
          &quot;Server instance&quot;
        </Link>
        .
      </>
    ),
  },
  {
    content: (
      <>
        Click the &quot;Generate Keypair&quot; button to create a new JWT
        authentication keypair. This will generate a public/private key pair
        that will be used to authenticate with Glean.
      </>
    ),
  },
  ...getJwtAuthSetupSteps(),
];

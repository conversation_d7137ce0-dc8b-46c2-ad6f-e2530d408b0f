import type { Meta, StoryObj } from "@storybook/react";
import GleanSetupSection from "./GleanSetup";

/**
 * @generated from enum glean.Provider
 */
export declare enum Provider {
  /**
   * @generated from enum value: UNSPECIFIED_PROVIDER = 0;
   */
  UNSPECIFIED_PROVIDER = 0,

  /**
   * @generated from enum value: GSUITE = 1;
   */
  GSUITE = 1,

  /**
   * @generated from enum value: AZURE = 2;
   */
  AZURE = 2,

  /**
   * @generated from enum value: OKTA = 3;
   */
  OKTA = 3,

  /**
   * @generated from enum value: ONELOGIN = 4;
   */
  ONELOGIN = 4,
}

const meta: Meta = {
  title: "Glean/GleanSetup",
  component: GleanSetupSection,
  parameters: {
    layout: "fullscreen",
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const FullyConfigured: Story = {
  args: {},
  parameters: {
    loaderData: {
      settings: {
        gleanDomain: "https://example-be.glean.com",
        publicKey: `-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEcJriKLwDmKiweF7j6UCUwTJNWVXz
KXouRDYCkAVYpZ1uYyOPIjw0cdpeENmFYeJMxRfSsSmLf3VF1JzBjND0aQ==
-----END PUBLIC KEY-----`,
      },
    },
  },
};

import { z } from "zod";

export const featureFlagsSchema = z
  .object({
    auth_central_user_tier_change: z.boolean().optional(),
    team_management: z.boolean().optional(),
    publish_user_session_events: z.boolean().optional(),
    team_management_canary_domains: z.string().optional(),
    // Add other feature flags here
  })
  .strict();

export type FeatureFlags = z.infer<typeof featureFlagsSchema>;

import { logger } from "@augment-internal/logging";
import * as LaunchDarkly from "@launchdarkly/node-server-sdk";
import { Config } from "../.server/config";
import type { FeatureFlags } from "./schema";
import type { SessionUser } from "../.server/auth";
import { featureFlagsSchema } from "./schema";

let ldClient: LaunchDarkly.LDClient | null = null;
let ldlPromise: ReturnType<
  LaunchDarkly.LDClient["waitForInitialization"]
> | null = null;

/**
 * Initialize the LaunchDarkly client
 * Should be called during server startup
 */
export async function _initializeFeatureFlags() {
  try {
    if (ldClient) {
      return ldlPromise;
    }
    const { FEATURE_FLAGS_SDK_KEY, DYNAMIC_FEATURE_FLAGS_ENDPOINT } = Config;
    if (!FEATURE_FLAGS_SDK_KEY) {
      logger.error(
        "LaunchDarkly SDK key not provided, feature flags will use defaults",
      );
    }
    const timeout = 10; // timeout in seconds

    // Configure LaunchDarkly client with custom endpoint if available
    const config: LaunchDarkly.LDOptions = { timeout };
    if (DYNAMIC_FEATURE_FLAGS_ENDPOINT) {
      logger.info(
        `Using custom feature flag endpoint: ${DYNAMIC_FEATURE_FLAGS_ENDPOINT}`,
      );
      config.baseUri = DYNAMIC_FEATURE_FLAGS_ENDPOINT;
      config.streamUri = DYNAMIC_FEATURE_FLAGS_ENDPOINT;
      config.eventsUri = DYNAMIC_FEATURE_FLAGS_ENDPOINT;
    }

    ldClient = LaunchDarkly.init(FEATURE_FLAGS_SDK_KEY, config);
    await (ldlPromise = ldClient.waitForInitialization({ timeout }));
  } catch (error) {
    logger.error("Failed to initialize LaunchDarkly client:", error);
  }
}

/**
 * Validates the feature flags against the zod schema and
 * filters out all flags that have a false value
 */
function parseAndFilterFeatureFlags(flags: object): FeatureFlags {
  const customerUIFlags = Object.fromEntries(
    Object.entries(flags).filter(([key]) => key in featureFlagsSchema.shape),
  ) as FeatureFlags;
  const result = featureFlagsSchema.safeParse(customerUIFlags);
  if (!result.success) {
    throw new Error(
      `Invalid feature flags: ${JSON.stringify(result.error.issues, null, 2)}`,
    );
  }
  const activeCustomerUIFlags = Object.fromEntries(
    Object.entries(result.data).filter(([, value]) => value !== false),
  ) as FeatureFlags;
  logger.info(
    "Active feature flags:",
    JSON.stringify(activeCustomerUIFlags, null, 2),
  );
  return activeCustomerUIFlags;
}

/**
 * Get feature flags for a specific tenant
 */
export async function loadFeatureFlags({
  tenantId,
  shardNamespace,
}: Pick<SessionUser, "tenantId" | "shardNamespace">): Promise<FeatureFlags> {
  try {
    await _initializeFeatureFlags();
    if (!ldClient) return {};

    const context = {
      kind: "namespace",
      key: shardNamespace,
      tenant_name: tenantId,
      pod_namespace: Config.NAMESPACE || "",
      pod_name: Config.POD_NAME || "",
      cloud: Config.CURRENT_CLOUD || "",
      env: Config.POD_ENV || "",
    };

    const allFlags = await ldClient.allFlagsState(context);
    const flags = allFlags.toJSON() || {};
    return parseAndFilterFeatureFlags(flags);
  } catch (error) {
    logger.error("Failed to fetch feature flags:", error);
    return {};
  }
}

/**
 * Shutdown the LaunchDarkly client
 * Should be called during server shutdown
 */
export async function _shutdownFeatureFlags() {
  if (ldClient) {
    await ldClient.flush();
    await ldClient.close();
    ldClient = null;
  }
}

async function handleShutdown() {
  try {
    await _shutdownFeatureFlags();
  } catch (error) {
    logger.error("Error during shutdown:", error);
  }
}

if (typeof process !== "undefined" && process.env.NODE_ENV === "production") {
  process.on("SIGTERM", handleShutdown);
  process.on("SIGINT", handleShutdown);
  process.on("exit", handleShutdown);
}

export async function getFeatureFlag<T extends keyof FeatureFlags>(
  flagName: T,
  {
    tenantId,
    shardNamespace,
  }: Pick<SessionUser, "tenantId" | "shardNamespace">,
): Promise<FeatureFlags[T]> {
  try {
    const flags = (await loadFeatureFlags({
      tenantId,
      shardNamespace,
    })) as FeatureFlags;
    return flags[flagName]!;
  } catch (error) {
    logger.error(`Failed to get feature flag ${String(flagName)}:`, error);
    return undefined;
  }
}

import {
  describe,
  it,
  expect,
  beforeEach,
  afterEach,
  vi,
  type Mock,
} from "vitest";
import * as z from "zod";
import * as LaunchDarkly from "@launchdarkly/node-server-sdk";
import {
  _initializeFeatureFlags,
  loadFeatureFlags,
  _shutdownFeatureFlags,
} from "./feature-flags.server";
import { existsSync } from "fs";
import { logger } from "@augment-internal/logging";

// Mock dependencies
vi.mock("@launchdarkly/node-server-sdk", () => {
  const mockClient = {
    waitForInitialization: vi.fn().mockResolvedValue(undefined),
    allFlagsState: vi.fn(),
    flush: vi.fn().mockResolvedValue(undefined),
    close: vi.fn().mockResolvedValue(undefined),
  };
  return {
    init: vi.fn().mockReturnValue(mockClient),
  };
});

vi.mock("fs", () => ({
  existsSync: vi.fn(),
}));

vi.mock("fs/promises", () => ({
  readFile: vi.fn(),
}));

vi.mock("@augment-internal/logging", () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
  },
}));

vi.mock("./schema", () => ({
  featureFlagsSchema: z.object({
    flag1: z.boolean(),
    flag2: z.boolean(),
  }),
}));

vi.mock("../.server/config", () => ({
  Config: {
    FEATURE_FLAGS_SDK_KEY: "test-sdk-key",
    FEATURE_FLAGS_SDK_KEY_PATH: "/path/to/sdk-key",
    NAMESPACE: "test-namespace",
    POD_NAME: "test-pod",
    CURRENT_CLOUD: "test-cloud",
    POD_ENV: "test-env",
  },
}));

describe("Feature Flags Server", () => {
  let mockLdClient: any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockLdClient = {
      waitForInitialization: vi.fn().mockResolvedValue(undefined),
      allFlagsState: vi.fn(),
      flush: vi.fn().mockResolvedValue(undefined),
      close: vi.fn().mockResolvedValue(undefined),
    };
    (LaunchDarkly.init as Mock).mockReturnValue(mockLdClient);
  });

  afterEach(async () => {
    await _shutdownFeatureFlags();
  });

  describe("initializeFeatureFlags", () => {
    it("should initialize with SDK key from config", async () => {
      (existsSync as Mock).mockReturnValue(false);
      await _initializeFeatureFlags();

      expect(LaunchDarkly.init).toHaveBeenCalledWith(
        "test-sdk-key",
        expect.any(Object),
      );
      expect(mockLdClient.waitForInitialization).toHaveBeenCalled();
    });

    it("should log error if initialization fails", async () => {
      (existsSync as Mock).mockReturnValue(false);
      (LaunchDarkly.init as Mock).mockImplementation(() => {
        throw new Error("Init error");
      });

      await _initializeFeatureFlags();

      expect(logger.error).toHaveBeenCalledWith(
        "Failed to initialize LaunchDarkly client:",
        expect.any(Error),
      );
    });
  });

  describe("getFeatureFlags", () => {
    it("should return feature flags for a tenant", async () => {
      const mockFlagsState = {
        toJSON: vi.fn().mockReturnValue({ flag1: true, flag2: false }),
      };
      mockLdClient.allFlagsState.mockResolvedValue(mockFlagsState);

      const flags = await loadFeatureFlags({
        tenantId: "tenant-123",
        shardNamespace: "shard-1",
      });

      expect(mockLdClient.allFlagsState).toHaveBeenCalledWith({
        kind: "namespace",
        key: "shard-1",
        tenant_name: "tenant-123",
        pod_namespace: "test-namespace",
        pod_name: "test-pod",
        cloud: "test-cloud",
        env: "test-env",
      });
      expect(flags).toEqual({ flag1: true });
    });

    it("should return empty object if client is not initialized", async () => {
      const flags = await loadFeatureFlags({
        tenantId: "tenant-123",
        shardNamespace: "shard-1",
      });

      expect(flags).toEqual({});
    });

    it("should handle errors and return empty object", async () => {
      mockLdClient.allFlagsState.mockRejectedValue(new Error("Flag error"));

      const flags = await loadFeatureFlags({
        tenantId: "tenant-123",
        shardNamespace: "shard-1",
      });

      expect(logger.error).toHaveBeenCalledWith(
        "Failed to fetch feature flags:",
        expect.any(Error),
      );
      expect(flags).toEqual({});
    });
  });

  describe("shutdownFeatureFlags", () => {
    it("should flush and close the client", async () => {
      await _initializeFeatureFlags();
      await _shutdownFeatureFlags();

      expect(mockLdClient.flush).toHaveBeenCalled();
      expect(mockLdClient.close).toHaveBeenCalled();
    });

    it("should do nothing if client is not initialized", async () => {
      mockLdClient = null;

      await _shutdownFeatureFlags();

      // No error should be thrown
    });
  });
});

import { describe, it, expect, beforeEach, vi } from "vitest";
import { getFeatureFlag as _getFeatureFlag } from "./feature-flags.client";

const testFeatureFlags = {
  testFlag: true,
} as const;

globalThis.FEATURE_FLAGS = testFeatureFlags as any;

type TestFeatureFlags = typeof testFeatureFlags;

type GetFeatureFlag = <T extends keyof TestFeatureFlags>(
  flagName: T,
) => TestFeatureFlags[T];

const getFeatureFlag = _getFeatureFlag as GetFeatureFlag;

describe("Feature Flags Client", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("getFeatureFlag", () => {
    it("should return the value of a feature flag", () => {
      expect(getFeatureFlag("testFlag")).toBe(true);
    });

    it("should return undefined if flag does not exist", () => {
      expect(getFeatureFlag("nonExistentFlag" as any)).toBeUndefined();
    });
  });
});

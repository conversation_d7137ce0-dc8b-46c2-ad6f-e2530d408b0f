import { generateMock } from "@anatine/zod-mock";
import { TeamStatusActiveSchema, TeamStatusNoneSchema } from "app/schemas/team";
import { UserApiGETResponseSchema } from "app/schemas/user";
import { PlansApiGETResponseSchema } from "app/schemas/plan";
import { addDays, format } from "date-fns";
import { OrbCustomerInfoSchema } from "app/schemas/orb";
import { deepAssign } from "app/utils/object";
import { GetUserOrbCreditsInfoResponseSchema } from "app/schemas/credits";

/** mocks for GET '/api/team' */
const team = {
  GET: {
    Response: {
      200: {
        get active() {
          return generateMock(TeamStatusActiveSchema);
        },
        get none() {
          return generateMock(TeamStatusNoneSchema);
        },
      },
    },
  },
};

/** mocks for GET '/api/user' */
const user = {
  GET: {
    Response: {
      get 200() {
        return generateMock(UserApiGETResponseSchema);
      },
    },
  },
};

/** mocks for GET '/api/plans' */
const plans = {
  GET: {
    Response: {
      get 200() {
        return generateMock(PlansApiGETResponseSchema);
      },
    },
  },
};

/** mocks for GET '/api/credits' */
const credits = {
  GET: {
    Response: {
      get 200() {
        return deepAssign({
          usageUnitsAvailable: 750,
          usageUnitsPending: 0,
          usageUnitsUsedThisBillingCycle: 250,
        })(generateMock(GetUserOrbCreditsInfoResponseSchema));
      },
    },
  },
};

const subscriptionMockOverrides = OrbCustomerInfoSchema.parse({
  portalUrl: "https://billing.example.com/portal",
  planId: "orb_developer_plan",
  augmentPlanType: "paid" as const,
  planName: "Developer Plan",
  billingPeriodEnd: format(addDays(new Date(), 30), "yyyy-MM-dd'T'HH:mm:ss'Z'"),
  trialPeriodEnd: null,
  creditsRenewingEachBillingCycle: 1000,
  creditsIncludedThisBillingCycle: 1000,
  billingCycleBillingAmount: "49.00",
  monthlyTotalCost: "49.00",
  pricePerSeat: "49.00",
  numberOfSeatsThisBillingCycle: 1,
  numberOfSeatsNextBillingCycle: 1,
  subscriptionEndDate: null,
  planIsExpired: false,
  addUsageAvailable: true,
  teamsAllowed: true,
  additionalUsageUnitCost: "1.00",
  maxNumSeats: 10,
});

/** mocks for GET/POST '/api/subscription' */
const subscription = {
  GET: {
    Response: {
      get 200() {
        return deepAssign(subscriptionMockOverrides)(
          generateMock(OrbCustomerInfoSchema),
        );
      },
    },
  },
  POST: {
    Response: {
      get 200() {
        return {};
      },
    },
  },
};

/** mocks for DELETE '/api/team/invitation/:id' */
const teamInvitation = {
  DELETE: {
    Response: {
      get 200() {
        return { success: true };
      },
    },
  },
};

const payment = {
  GET: {
    Response: {
      get 200() {
        return { hasPaymentMethod: true };
      },
    },
  },
};

const mocks = {
  team,
  user,
  plans,
  credits,
  subscription,
  teamInvitation,
  payment,
};

export default mocks;

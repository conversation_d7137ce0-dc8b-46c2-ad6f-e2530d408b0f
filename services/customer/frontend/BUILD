load("@aspect_rules_js//js:defs.bzl", "js_binary", "js_image_layer", "js_library", "js_run_binary", "js_run_devserver")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config", "ts_project")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("@npm//services/customer/frontend:@remix-run/dev/package_json.bzl", remix_bin = "bin")
load("@npm//services/customer/frontend:tsx/package_json.bzl", tsx_bin = "bin")
load("@npm//services/customer/frontend:vitest/package_json.bzl", vitest_bin = "bin")
load("//tools/bzl:image.bzl", "image")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

npm_link_all_packages()

ASSET_PATTERNS = [
    "public/*",
]

SRC_PATTERNS = [
    "app/**/*.ts",
    "app/**/*.tsx",
    "app/**/*.css",
    "test/**/*.d.ts",
    ":node_modules/@augment-internal/logging",
    ":node_modules/@augment-internal/systemenv",
    ":node_modules/@augment-internal/ts-utils",
]

BUILD_MODULES = [
    ":node_modules/@radix-ui/colors",
    ":node_modules/@radix-ui/react-dialog",
    ":node_modules/@radix-ui/react-icons",
    ":node_modules/@radix-ui/react-navigation-menu",
    ":node_modules/@radix-ui/react-select",
    ":node_modules/@radix-ui/react-slot",
    ":node_modules/@radix-ui/react-toast",
    ":node_modules/@radix-ui/react-visually-hidden",
    ":node_modules/@radix-ui/themes",
    ":node_modules/next-themes",
    ":node_modules/@remix-run/react",
    ":node_modules/date-fns",
    ":node_modules/date-fns-tz",
    ":node_modules/email-validator",
    ":node_modules/react-transition-group",
    ":node_modules/node-cache",
    ":node_modules/isbot",
    ":node_modules/react",
    ":node_modules/csstype",
    ":node_modules/ts-dedent",
    ":node_modules/react-dom",
    ":node_modules/@remix-run/router",
    ":node_modules/remix-utils",
    ":node_modules/@anatine/zod-mock",
    ":node_modules/msw",
    ":node_modules/msw-storybook-addon",
    ":node_modules/ts-pattern",
    ":node_modules/zustand",
    ":node_modules/jotai",
    ":node_modules/jotai-effect",
    ":node_modules/jotai-tanstack-query",
    ":node_modules/use-memo-one",
    ":node_modules/immer",
    ":node_modules/@visx/xychart",
    ":node_modules/@visx/gradient",
    ":node_modules/@visx/curve",
    ":node_modules/@visx/responsive",
    ":node_modules/@visx/group",
    ":node_modules/@visx/shape",
    ":node_modules/@visx/scale",
    ":node_modules/@visx/text",
    ":node_modules/@tanstack/react-query",
    ":node_modules/@testing-library/react",
    ":node_modules/@tailwindcss/vite",
    ":node_modules/tw-animate-css",
    ":node_modules/tailwindcss",
    ":node_modules/tailwind-merge",
    ":node_modules/clsx",
    ":node_modules/class-variance-authority",
    ":node_modules/radix-ui",
    ":node_modules/@remix-run/testing",
    ":node_modules/remix-auth",
    ":node_modules/remix-auth-oauth2",
    ":node_modules/@connectrpc/connect",
    ":node_modules/@connectrpc/connect-node",
    ":node_modules/@google-cloud/pubsub",
    ":node_modules/google-auth-library",
    ":node_modules/highlight.js",
    ":node_modules/highlightjs-svelte",
    ":node_modules/marked-react",
    ":node_modules/mermaid",
    ":node_modules/stripe",
    ":node_modules/@storybook/react",
    ":node_modules/zod",
    "//services/auth/central/server:auth_entities_ts_proto",
    "//services/auth/central/server:auth_ts_proto",
    "//services/token_exchange:token_exchange_ts_proto",
    "//services/request_insight/analytics:request_insight_analytics_ts_proto",
    "//services/request_insight:request_insight_ts_proto",
    "//services/integrations/slack_bot/processor:processor_ts_proto",
    "//services/integrations/glean:glean_ts_proto",
    "//services/integrations/github/processor:processor_ts_proto",
    "//services/integrations/notion:notion_ts_proto",
    "//services/integrations/linear:linear_ts_proto",
    "//services/integrations/atlassian:atlassian_ts_proto",
    "//services/integrations/supabase:supabase_ts_proto",
    "//services/tenant_watcher:tenant_watcher_ts_proto",
    "//services/share:share_ts_proto",
    "//services/settings:settings_ts_proto",
]

DEV_MODULES = [
    ":vite_config_ts",
    ":node_modules/@bazel/runfiles",
    ":node_modules/@remix-run/dev",
    ":node_modules/vite",
    ":node_modules/vite-tsconfig-paths",
]

COMMON = [
    ":npmrc",
    ":package_json",
    ":tsconfig",
]

SERVER_PATTERNS = [
    "server.ts",
    "app/**/*.tsx",
    "app/**/*.ts",
    "types/**/*.d.ts",
    ":node_modules/vite/types/**/*.d.ts",
]

SERVER_RUNTIME = [
    ":node_modules/compression",
    ":node_modules/dotenv",
    ":node_modules/express",
    ":node_modules/prom-client",
    ":node_modules/@augment-internal/logging",
    ":node_modules/@augment-internal/systemenv",
    ":node_modules/@augment-internal/ts-utils",
    ":node_modules/@launchdarkly/node-server-sdk",
    ":node_modules/@isaacs/express-prometheus-middleware",
    ":node_modules/@remix-run/node",
    ":node_modules/@remix-run/express",
    ":node_modules/@remix-run/server-runtime",
    ":node_modules/jsonwebtoken",
    ":node_modules/source-map-support",
]

TEST_MODULES = [
    "vitest.setup.ts",
    "vitest.workspace.ts",
    "vitest.global-setup.ts",
    ":node_modules/vitest",
    ":node_modules/@testing-library/jest-dom",
    ":node_modules/@faker-js/faker",
]

SERVER = SERVER_RUNTIME + [
    ":node_modules/@types/compression",
    ":node_modules/@types/express",
    ":node_modules/@types/jsonwebtoken",
    ":node_modules/@types/react",
    ":node_modules/@types/react-dom",
    ":node_modules/@types/react-transition-group",
    ":node_modules/@types/source-map-support",
    ":node_modules/storybook",
]

remix_bin.remix_binary(
    name = "remix",
    chdir = package_name(),
    data = SERVER + COMMON + DEV_MODULES,
)

tsx_bin.tsx_binary(
    name = "tsx",
    chdir = package_name(),
    data = [":node_modules/tsx"],
)

js_run_devserver(
    name = "dev",
    args = [
        "dev.ts",
    ],
    data = BUILD_MODULES + DEV_MODULES + COMMON + SERVER + glob(SERVER_PATTERNS + SRC_PATTERNS + ASSET_PATTERNS) + [
        "dev.ts",
        "forwards",
        "tsx",
        ":node_modules/@kubernetes/client-node",
        "//:node_modules",
        "//third_party/proto:googleapis_status_js_proto",
    ],
    env = {
        "NODE_ENV": "development",
        "AU_BAZEL_RUN_MODE": "1",
    },
    tool = ":tsx",
)

# js_library flattens a srcs + their dependencies into a single directory, so that
# it can be consumed by remix's js_run_binary. if the srcs are used directly, their
# deps are not flattened and the build fails.
js_library(
    name = "frontend_src",
    srcs = glob(ASSET_PATTERNS + SRC_PATTERNS),
    deps = BUILD_MODULES + ["//third_party/proto:googleapis_status_js_proto"],
)

js_run_binary(
    name = "frontend",
    srcs = [":frontend_src"],
    args = ["vite:build"],
    out_dirs = ["dist"],
    tool = ":remix",
)

ts_config(
    name = "ts-server-config",
    src = "tsconfig-server.json",
    deps = [
        "tsconfig.json",
    ],
)

ts_project(
    name = "ts-server",
    srcs = glob(
        SERVER_PATTERNS,
        exclude = [
            "**/*.test.ts",
            "**/*.test.tsx",
            "**/*.stories.ts",
            "**/*.stories.tsx",
            "app/entry.client.tsx",
        ],
    ),
    allow_js = False,
    out_dir = "ts_dist",
    resolve_json_module = True,
    source_map = True,
    tsconfig = ":ts-server-config",
    deps = BUILD_MODULES + SERVER,
)

tsx_bin.tsx_binary(
    name = "start",
    args = ["server.ts"],
    chdir = package_name(),
    data = SERVER + BUILD_MODULES + COMMON + [
        "example-auth-secrets.json",
        ":frontend",
    ] + glob(SERVER_PATTERNS),
    env = {
        "PORT": "5000",
    },
)

js_library(
    name = "server-lib",
    srcs = [
        ":frontend",
        ":ts-server",
    ],
    deps = SERVER_RUNTIME + COMMON,
)

js_binary(
    name = "server",
    data = SERVER_RUNTIME + COMMON + [
        ":frontend",
        ":ts-server",
    ],
    entry_point = "ts_dist/server.js",
)

js_image_layer(
    name = "layers",
    binary = ":server",
)

# we can write a js_oci_image if we do this more often
image(
    name = "image",
    base = "//tools/docker:ubuntu_base_image",
    cmd = [
        "/services/customer/frontend/server",
    ],
    env = {
        "NODE_ENV": "production",
        "BAZEL_BINDIR": "/services/customer/frontend/server.runfiles/_main",
    },
    tars = [":layers"],
    # both added indirectly via esbuild
    trivy_allow_list = [
    ],
    workdir = "/",
)

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
)

js_library(
    name = "npmrc",
    srcs = [".npmrc"],
)

js_library(
    name = "package_json",
    srcs = ["package.json"],
)

js_library(
    name = "vite_config_ts",
    srcs = ["vite.config.ts"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/deploy/configs:orb",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

kubecfg(
    name = "kubecfg_monitoring",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_monitoring",
    ],
)

js_library(
    name = "vitest_src",
    srcs = glob(SERVER_PATTERNS),
    deps = BUILD_MODULES + ["//third_party/proto:googleapis_status_js_proto"],
)

vitest_bin.vitest_test(
    name = "unit_tests",
    args = [
        "run",
        "--project",
        "unit",
    ],
    chdir = package_name(),
    data = TEST_MODULES + DEV_MODULES + COMMON + SERVER + [":vitest_src"] + [
        "//:node_modules",
    ],
)

vitest_bin.vitest_test(
    name = "integration_tests",
    # Set a longer timeout for integration tests
    timeout = "long",
    args = [
        "run",
        "--project",
        "integration",
    ],
    chdir = package_name(),
    data = TEST_MODULES + DEV_MODULES + COMMON + SERVER + [":vitest_src"] + [
        "//:node_modules",
        "//services/auth/test:launch",
    ],
    no_copy_to_bin = [
        "//services/auth/test:launch",
    ],
)

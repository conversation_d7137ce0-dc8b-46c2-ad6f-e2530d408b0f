import { vitePlugin as remix } from "@remix-run/dev";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import { installGlobals } from "@remix-run/node";
import tailwindcss from "@tailwindcss/vite";
import type { Plugin } from "vite";

if (process.env.NODE_ENV === "test") {
  installGlobals();
}

export default defineConfig({
  plugins: [
    tailwindcss() as Plugin[],
    tsconfigPaths({ projects: ["./tsconfig.json"] }),
    process.env.NODE_ENV === "test"
      ? null
      : remix({
          buildDirectory: "dist",
          future: {
            v3_fetcherPersist: true,
            v3_relativeSplatPath: true,
            v3_throwAbortReason: true,
          },
          serverModuleFormat: "esm",
        }),
  ],
  server: process.env.HMR_PORT
    ? {
        hmr: {
          port: Number(process.env.HMR_PORT),
        },
      }
    : undefined,
  build: {
    rollupOptions: {
      // needed to avoid bundling these dependencies because they
      // are already provided by Remix
      // https://github.com/remix-run/remix/discussions/9536
      external: ["isbot"],
    },
    sourcemap: true,
  },
});

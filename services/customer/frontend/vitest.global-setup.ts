import type { GlobalSetupContext } from "vitest/node";
import * as runfiles from "@bazel/runfiles";
import { spawn, type ChildProcessWithoutNullStreams } from "child_process";

interface TestServerConfig {
  unlikely_string: string;
  auth_central_grpc_server: string;
  auth_central_http_server: string;
  token_exchange_server: string;
}

let authProcess: ChildProcessWithoutNullStreams | null = null;

async function startAuthTestServer(): Promise<TestServerConfig> {
  return new Promise<TestServerConfig>((resolve, reject) => {
    const r = new runfiles.Runfiles();

    // Spawn the process
    authProcess = spawn(r.resolve("_main/services/auth/test/launch"), {
      stdio: ["pipe", "pipe", "pipe"],
    });

    // Collect stdout to parse the server info
    let stdoutData = "";
    authProcess.stdout.on("data", (data) => {
      const chunk = data.toString();
      stdoutData += chunk;

      console.log(`Auth server stdout: ${chunk}`);

      // Look for the JSON output containing server info
      if (chunk.includes("unlikely_string")) {
        try {
          // Extract the JSON object from the output
          const jsonMatch = stdoutData.match(
            /\{[^}]*"unlikely_string"[^}]*\}/g,
          );
          if (jsonMatch) {
            const testServerConfig = JSON.parse(
              jsonMatch[0],
            ) as TestServerConfig;
            if (!testServerConfig) {
              console.error("Failed to parse auth server info");
              reject(new Error("Failed to parse auth server info"));
              return;
            }
            if (
              testServerConfig["unlikely_string"] !== "KPgjijlupAYN_wII26qbXg"
            ) {
              console.error("Auth server info doesn't match what we expect");
              reject(
                new Error("Auth server info doesn't match what we expect"),
              );
              return;
            }

            console.log("Test server config:", testServerConfig);

            // Store the config in a global variable for tests to access if needed
            global.__TEST_SERVER_CONFIG__ = testServerConfig;

            setTimeout(() => {
              console.log("Waiting for config to be applied...");
              resolve(testServerConfig);
            }, 1000);
          }
        } catch (error) {
          console.error("Failed to parse auth server info:", error);
          reject(error);
        }
      }
    });

    // Handle errors
    authProcess.stderr.on("data", (data) => {
      console.error(`Auth server stderr: ${data}`);
    });

    authProcess.on("error", (error) => {
      console.error(`Failed to start auth server: ${error.message}`);
      reject(error);
    });

    authProcess.on("exit", (code) => {
      if (code !== 0 && !global.__TEST_SERVER_CONFIG__) {
        console.error(`Auth server exited with code ${code}`);
        reject(new Error(`Auth server exited with code ${code}`));
      }
    });
  });
}

export async function setup({ provide }: GlobalSetupContext) {
  console.log("Starting auth test server before tests...");
  try {
    const config = await startAuthTestServer();
    // Make the config available to tests via inject()
    provide("testServerConfig", config);
    return config;
  } catch (error) {
    console.error("Failed to start auth test server:", error);
    process.exit(1);
  }
}

export async function teardown() {
  if (authProcess) {
    console.log("Stopping auth test server...");
    // Send Enter key to stop the process (as the script waits for Enter)
    authProcess.stdin.write("\n");

    // Wait for the process to exit gracefully
    return new Promise<void>((resolve) => {
      const timeout = setTimeout(() => {
        console.log("Auth server didn't exit gracefully, killing it...");
        authProcess?.kill("SIGKILL");
        authProcess = null;
        resolve();
      }, 5000); // 5 second timeout

      authProcess!.on("exit", () => {
        clearTimeout(timeout);
        console.log("Auth server exited gracefully");
        authProcess = null;
        resolve();
      });
    });
  }
}

// Declare global for TypeScript
declare global {
  // eslint-disable-next-line no-var
  var __TEST_SERVER_CONFIG__: TestServerConfig;
}

// Add type definition for the provided context
declare module "vitest" {
  export interface ProvidedContext {
    testServerConfig: TestServerConfig;
  }
}

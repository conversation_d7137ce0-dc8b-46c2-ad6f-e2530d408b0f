{"name": "customer-app", "private": true, "sideEffects": false, "type": "module", "scripts": {"build-server": "$npm_execpath tsc -p tsconfig-server.json", "build": "remix vite:build", "dev": "tsx ./dev.ts", "start": "tsx server.ts", "storybook": "CHOKIDAR_USEPOLLING=1 storybook dev -p 6006 --no-open", "build-storybook": "storybook build", "fix": "pnpm run \"/^fix:.*/\"", "fix:lint": "git ls-files -- . | xargs pre-commit run eslint --hook-stage=manual --files || true", "fix:format": "git ls-files -- . | xargs pre-commit run prettier --hook-stage=manual --files || true", "test": "pnpm run \"/^test:.*/\"", "test:format": "git ls-files -- . | xargs pre-commit run prettier --files", "test:lint": "git ls-files -- . | xargs pre-commit run eslint --files", "test:spec": "vitest run --project unit", "test:types": "tsc"}, "dependencies": {"@augment-internal/logging": "workspace:*", "@augment-internal/systemenv": "workspace:*", "@augment-internal/ts-utils": "workspace:*", "@bufbuild/protobuf": "1.10.0", "@connectrpc/connect": "1.4.0", "@connectrpc/connect-node": "1.4.0", "@google-cloud/pubsub": "^4.8.0", "@isaacs/express-prometheus-middleware": "^1.2.1", "@launchdarkly/node-server-sdk": "^9.7.5", "@radix-ui/colors": "^3.0.0", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-visually-hidden": "^1.1.3", "@radix-ui/themes": "^3.1.1", "@remix-run/express": "^2.16.7", "@remix-run/node": "^2.16.7", "@remix-run/react": "^2.16.7", "@remix-run/router": "^1.23.0", "@remix-run/server-runtime": "^2.16.7", "@tanstack/react-query": "^5.51.24", "@types/stripe": "^8.0.417", "@visx/curve": "^3.3.0", "@visx/gradient": "^3.3.0", "@visx/group": "^3.3.0", "@visx/responsive": "^3.10.2", "@visx/scale": "^3.5.0", "@visx/shape": "^3.5.0", "@visx/text": "^3.3.0", "@visx/xychart": "^3.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.7.4", "csstype": "^3.1.3", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "dotenv": "^16.4.5", "email-validator": "^2.0.4", "express": "^4.21.2", "google-auth-library": "^9.15.1", "highlight.js": "^11.9.0", "highlightjs-svelte": "^1.0.6", "immer": "^10.1.1", "isbot": "^5.1.13", "jotai": "^2.12.2", "jotai-effect": "^2.0.2", "jotai-tanstack-query": "^0.9.0", "jsonwebtoken": "^9.0.2", "marked-react": "^3.0.0", "mermaid": "^11.4.1", "next-themes": "^0.4.4", "node-cache": "^5.1.2", "prom-client": "^15.1.3", "radix-ui": "^1.1.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-transition-group": "^4.4.5", "remix-auth": "^3.7.0", "remix-auth-oauth2": "^2.1.0", "remix-utils": "^7.6.0", "source-map-support": "^0.5.21", "stripe": "^17.6.0", "tailwind-merge": "^3.2.0", "ts-dedent": "^2.2.0", "ts-pattern": "^5.6.2", "tsx": "^4.17.0", "tw-animate-css": "^1.2.5", "use-memo-one": "^1.1.3", "vite-tsconfig-paths": "^5.0.1", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@anatine/zod-mock": "^3.14.0", "@bazel/runfiles": "6", "@chromatic-com/storybook": "^3.2.2", "@faker-js/faker": "^9.6.0", "@kubernetes/client-node": "^1.1.2", "@remix-run/dev": "^2.16.7", "@remix-run/testing": "^2.16.7", "@storybook/addon-essentials": "^8.4.5", "@storybook/addon-interactions": "^8.4.5", "@storybook/addon-links": "^8.4.5", "@storybook/blocks": "^8.4.5", "@storybook/react": "^8.4.5", "@storybook/react-vite": "^8.4.5", "@storybook/test": "^8.4.5", "@tailwindcss/vite": "^4.0.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^15.0.6", "@types/compression": "^1.7.5", "@types/express": "4.16", "@types/jsonwebtoken": "^9.0.6", "@types/node": "20.14.11", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-transition-group": "^4.4.12", "@types/source-map-support": "^0.5.10", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "esbuild": "^0.23.0", "eslint": "^8.57.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-react": "^7.34.4", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-storybook": "^0.8.0", "matchers": "link:@testing-library/jest-dom/matchers", "msw": "^2.7.5", "msw-storybook-addon": "^2.0.4", "shadcn": "^2.4.1", "storybook": "^8.6.10", "tailwindcss": "^4.0.16", "typescript": "^5.5.3", "vite": "5.4.19", "vitest": "^2.0.4"}, "engines": {"node": ">=20.0.0", "npm": "please-use-pnpm", "yarn": "please-use-pnpm", "pnpm": "9"}, "msw": {"workerDirectory": ["public", ".storybook/public"]}}
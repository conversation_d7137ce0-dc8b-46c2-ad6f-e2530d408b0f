/**
 * This is intended to be a basic starting point for linting in your app.
 * It relies on recommended configs out of the box for simplicity, but you can
 * and should modify this configuration to best suit your team's needs.
 */
const path = require("path");

/** @type {import('eslint').Linter.Config} */
module.exports = {
  root: true,
  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module",
    ecmaFeatures: {
      jsx: true,
    },
  },
  env: {
    browser: true,
    commonjs: true,
    es6: true,
  },
  ignorePatterns: ["!**/.server", "!**/.client"],

  // Base config
  extends: ["eslint:recommended", "plugin:storybook/recommended"],

  overrides: [
    // React
    {
      files: ["**/*.{js,jsx,ts,tsx}"],
      plugins: ["react", "jsx-a11y"],
      extends: [
        "plugin:react/recommended",
        "plugin:react/jsx-runtime",
        "plugin:react-hooks/recommended",
        "plugin:jsx-a11y/recommended",
      ],
      settings: {
        react: {
          version: "detect",
        },
        formComponents: ["Form"],
        linkComponents: [
          { name: "<PERSON>", linkAttribute: "to" },
          { name: "NavLink", linkAttribute: "to" },
        ],
        "import/resolver": {
          typescript: {},
        },
      },
    },

    // Typescript
    {
      files: ["**/*.{ts,tsx}"],
      plugins: ["@typescript-eslint", "import"],
      parser: "@typescript-eslint/parser",
      parserOptions: {
        ecmaVersion: "ES2022",
        sourceType: "module",
        ecmaFeatures: {
          jsx: true,
        },
        project: path.join(__dirname, "tsconfig.json"),
      },
      settings: {
        "import/internal-regex": "^~/",
        "import/resolver": {
          node: {
            extensions: [".ts", ".tsx"],
          },
          typescript: {
            alwaysTryTypes: true,
          },
        },
      },
      extends: [
        "plugin:@typescript-eslint/recommended",
        "plugin:import/recommended",
        "plugin:import/typescript",
      ],
      rules: {
        "@typescript-eslint/consistent-type-imports": [
          "error",
          {
            prefer: "type-imports",
            fixStyle: "inline-type-imports",
          },
        ],
        "@typescript-eslint/no-import-type-side-effects": "error",
        "@typescript-eslint/no-unused-vars": [
          "error",
          { argsIgnorePattern: "^_", varsIgnorePattern: "^_" },
        ],
        "@typescript-eslint/no-explicit-any": "off",
        "import/default": "off",
        "import/named": "error",
        "import/no-duplicates": "error",
        "import/no-unresolved": "off",
        "import/namespace": "off", // Rule does not support const type parameters
        "no-constant-condition": "off",
        "no-restricted-imports": [
          "error",
          {
            paths: [
              {
                name: "@tanstack/react-query",
                importNames: ["queryOptions"],
                message:
                  'Import queryOptions from "app/client-cache/queryOptions" instead.',
              },
            ],
          },
        ],
        "no-restricted-syntax": [
          "error",
          {
            selector: "CallExpression[callee.name='log']",
            message:
              "Please use logger from @augment-internal/logging instead of the log utility function.",
          },
        ],
        "react/no-unknown-property": ["error", { ignore: ["scoped"] }],
        "react/prop-types": "off",
        "no-empty-pattern": "off",
        "@typescript-eslint/no-misused-promises": [
          "error",
          {
            checksVoidReturn: false,
          },
        ],
      },
    },
    {
      // server
      files: ["**/app/.server/**/*.{ts,tsx}"],
      parserOptions: {
        project: path.join(__dirname, "tsconfig-server.json"),
      },
    },
    {
      files: ["**/app/**/*.{js,jsx,ts,tsx}"],
      excludedFiles: [
        "**/*.stories.{js,jsx,ts,tsx}",
        "**/*.test.{js,jsx,ts,tsx}",
      ],
      rules: {
        "no-console": ["error", { allow: ["warn", "error", "info"] }],
      },
    },

    // Node
    {
      files: [".eslintrc.cjs"],
      env: {
        node: true,
      },
    },
  ],
};

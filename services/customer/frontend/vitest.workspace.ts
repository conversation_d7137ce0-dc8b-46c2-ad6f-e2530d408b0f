import { defineWorkspace } from "vitest/config";
import tsconfigPaths from "vite-tsconfig-paths";
import { installGlobals } from "@remix-run/node";

if (process.env.NODE_ENV === "test") {
  installGlobals();
}

const sharedPlugins = [
  tsconfigPaths({
    projects: ["./tsconfig.json"],
  }),
];

const sharedEnv = {
  NODE_ENV: "test" as const,
};

export default defineWorkspace([
  {
    test: {
      name: "unit",
      root: "./",
      include: ["./app/**/*.test.{ts,tsx}"],
      exclude: ["./app/**/*.integration.test.{ts,tsx}"],
      environmentMatchGlobs: [
        // Server tests
        ["./app/**/*.server.test.ts", "node"],
        ["./app/.server/**/*.test.ts", "node"],
        // Client tests (default to jsdom)
        ["**/*.test.{ts,tsx}", "jsdom"],
      ],
      env: sharedEnv,
      setupFiles: ["./vitest.setup.ts"],
    },
    plugins: sharedPlugins,
  },
  {
    test: {
      name: "integration",
      root: "./",
      include: ["./app/**/*.integration.test.{ts,tsx}"],
      environment: "node",
      env: {
        ...sharedEnv,
        INTEGRATION_TEST: "true",
      },
      globalSetup: "./vitest.global-setup.ts",
      // Integration tests might need longer timeouts
      testTimeout: 30000,
    },
    plugins: sharedPlugins,
  },
]);

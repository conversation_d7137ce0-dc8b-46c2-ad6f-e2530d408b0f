{
  "include": ["**/*.ts", "**/*.tsx", "**/*.css"],
  "compilerOptions": {
    "lib": ["DOM", "DOM.Iterable", "ES2022"],
    "types": ["@remix-run/node", "vite/client", "@testing-library/jest-dom"],
    "isolatedModules": true,
    "esModuleInterop": true,
    "jsx": "react-jsx",
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    "target": "ES2022",
    "strict": true,
    "allowJs": false,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    // Vite takes care of building everything, not tsc.
    "noEmit": true,
    "paths": {
      "~services/*": ["../../*", "../../../bazel-bin/services/*"]
    }
  }
}

// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details

{
  deployment: [
    {
      name: 'customer-ui',
      kubecfg: {
        target: '//services/customer/frontend:kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'central',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['jacqueline', 'liam', 'moogi'],
          slack_channel: '#customer-ui',
        },
      },
    },

    {
      name: 'customer-ui-monitoring',
      kubecfg: {
        target: '//services/customer/frontend:kubecfg_monitoring',
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
    },
  ],
}

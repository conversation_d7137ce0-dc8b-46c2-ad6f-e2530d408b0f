import type { Preview, StoryContext, StoryFn } from "@storybook/react";
import { Theme } from "@radix-ui/themes";
import { initialize as initializeMSW, mswDecorator } from "msw-storybook-addon";
import { createRemixStub } from "@remix-run/testing";
import { getThemeFromBackground } from "./dark-mode-utils";
import "@radix-ui/themes/styles.css";
import "../app/styles/main.css";
import "../app/styles/tailwind.css";
import { applyPolyfill } from "../app/polyfill/style-scoped/style-scoped.polyfill";
import { withStoryProviders } from "./withStoryProviders";
import type { FeatureFlags } from "../app/feature-flags/schema";
import { GeneralErrorBoundary } from "../app/components/GeneralErrorBoundary";
import "../app/feature-flags/feature-flags.d";
import { http, HttpRequestHandler, HttpResponse } from "msw";
import mocks from "../app/mocks";
import { delay } from "@augment-internal/ts-utils/timer";

applyPolyfill();
initializeMSW({
  onUnhandledRequest: "warn",
  serviceWorker: { url: "/mockServiceWorker.js" },
});

const apiDefaults = [
  {
    key: "plans",
    url: "/api/plans",
    method: "get",
    response: mocks.plans.GET.Response[200],
  },
  {
    key: "subscription",
    url: "/api/subscription",
    method: "get",
    response: mocks.subscription.GET.Response[200],
  },
  {
    key: "subscription",
    url: "/api/subscription",
    method: "post",
    response: mocks.subscription.POST.Response[200],
  },
  {
    key: "team",
    url: "/api/team",
    method: "get",
    response: mocks.team.GET.Response[200].active,
  },
  {
    key: "user",
    url: "/api/user",
    method: "get",
    response: mocks.user.GET.Response[200],
  },
  {
    key: "teamInvitation",
    url: "/api/team/invitation/:id",
    method: "delete",
    response: mocks.teamInvitation.DELETE.Response[200],
  },
  {
    key: "credits",
    url: "/api/credits",
    method: "get",
    response: mocks.credits.GET.Response[200],
  },
  {
    key: "payment",
    url: "/api/payment",
    method: "get",
    response: mocks.payment.GET.Response[200],
  },
] as const;

function* createDefaultHandlers(context: StoryContext) {
  const overrideCache = {};
  const { msw } = context.parameters;
  if (typeof msw?.overrides === "function") {
    const httpInject = Object.fromEntries(
      Object.getOwnPropertyNames(http).map((method) => [
        method,
        (path: string, resolver: HttpRequestHandler) => {
          overrideCache[path] ??= {};
          overrideCache[path][method] = true;
          const handler = http[method](path, resolver);
          handler.isDefault = false;
          return handler;
        },
      ]),
    ) as any as typeof http;
    yield* msw.overrides(httpInject);
  }
  for (const apiDefault of apiDefaults) {
    const { key, url, method, response } = apiDefault;
    if (overrideCache[url]?.[method.toLowerCase()]) continue;
    const handler = http[method.toLowerCase()](url, async () => {
      const duration =
        msw?.delay?.[key]?.[method] ?? msw?.delay?.[key] ?? msw?.delay ?? 500;
      await delay(duration);
      return HttpResponse.json(response);
    });
    handler.isDefault = true;
    yield handler;
  }
}

function withMSWHandlers(Story: StoryFn, context: StoryContext) {
  (context.parameters.msw ??= {}).handlers = [
    ...createDefaultHandlers(context),
  ];
  return mswDecorator(Story, context);
}

// Set default feature flags for all stories
function withFeatureFlags(Story: StoryFn, context: StoryContext) {
  // Default feature flags that will be available in all stories
  const defaultFlags: FeatureFlags = {
    auth_central_user_tier_change: false,
    team_management: false,
  };

  // Override with story-specific flags if provided
  window.FEATURE_FLAGS = {
    ...defaultFlags,
    ...(context.parameters.featureFlags || {}),
  };

  return <Story {...context} />;
}

function withRemixStub(Story: StoryFn, context: StoryContext) {
  const path = context.parameters.path ?? "/";
  const RemixStub = createRemixStub(
    [
      {
        path,
        Component: () => <Story {...context} />,
        ErrorBoundary: context.parameters.ErrorBoundary ?? GeneralErrorBoundary,
        loader: () => context.parameters.loaderData ?? {},
      },
      {
        path: "*",
        Component: () => <Story {...context} />,
        ErrorBoundary: context.parameters.ErrorBoundary ?? GeneralErrorBoundary,
        loader: () => context.parameters.loaderData ?? {},
      },
    ],
    context.parameters.remixStubOptions,
  );
  return <RemixStub initialEntries={[context.parameters.path ?? "/"]} />;
}

// Theme provider
function withTheme(Story, context) {
  // Use dark theme if specified in parameters
  return (
    <Theme
      appearance={getThemeFromBackground(context)}
      data-is-root-theme={false}
    >
      <Story {...context} />
    </Theme>
  );
}

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
    backgrounds: {
      default: "light",
      values: [
        { name: "light", value: "#ffffff" },
        { name: "dark", value: "#333333" },
      ],
    },
    options: {
      storySort: {
        method: "alphabetical",
        order: ["Layouts"],
      },
    },
  },
  // Apply decorators globally to all stories
  // Order matters: innermost (Theme) to outermost (Router)
  decorators: [
    withRemixStub,
    withFeatureFlags,
    withMSWHandlers,
    withTheme,
    withStoryProviders,
  ],
};

export default preview;

import React from "react";
import { createStore, Provider as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "jotai";
import type { StoryContext, StoryFn } from "@storybook/react";
import {
  QueryCache,
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query";
import { createToaster, setToaster } from "../app/components/ui/Toast/Toast";

type StoryClients = {
  toaster: ReturnType<typeof createToaster>;
  store: ReturnType<typeof createStore>;
  queryClient: QueryClient;
};

// map of storyId to providers
const providerMap = new Map<string, StoryClients>();

export function withStoryProviders(Story: StoryFn, context: StoryContext) {
  // Create a new queryClient for each story
  const storyId = context.id ?? context.componentId;

  if (!providerMap.has(storyId)) {
    const toaster = createToaster();

    const store = createStore();
    const queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
          refetchOnWindowFocus: false,
        },
      },
      queryCache: new QueryCache({
        onError: (err) => {
          console.error("Query error in Storybook:", err);
        },
      }),
    });

    providerMap.set(storyId, { store, queryClient, toaster });
  }

  const { queryClient, toaster, store } = providerMap.get(storyId)!;
  const { Toasts } = toaster;

  setToaster(toaster);

  return (
    <QueryClientProvider client={queryClient}>
      <JotaiProvider store={store}>
        <Toasts>
          <Story {...context} />
        </Toasts>
      </JotaiProvider>
    </QueryClientProvider>
  );
}

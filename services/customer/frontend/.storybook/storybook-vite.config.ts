import { defineConfig, loadEnv } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import tailwindcss from "@tailwindcss/vite";
import type { Plugin } from "vite";

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");
  process.env = { ...process.env, ...env };

  return {
    plugins: [
      tsconfigPaths({ projects: ["./tsconfig.json"] }),
      tailwindcss() as Plugin[],
    ],
    build: {
      sourcemap: true,
    },
  };
});

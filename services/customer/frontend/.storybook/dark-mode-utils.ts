import type { StoryContext as BaseStoryContext } from "@storybook/react";

type StoryContext = {
  globals: {
    backgrounds: {
      value:
        | "transparent"
        | NonNullable<
            StoryContext["parameters"]["backgrounds"]
          >["values"][number]["value"];
    } | null;
  };
  parameters: {
    backgrounds: {
      default: NonNullable<
        StoryContext["parameters"]["backgrounds"]
      >["values"][number]["name"];
      values: [
        { name: "light"; value: "#ffffff" },
        { name: "dark"; value: "#333333" },
      ];
    } | null;
  };
} & BaseStoryContext;

export function getThemeFromBackground(
  context: StoryContext,
): "light" | "dark" {
  const backgroundValue = context.globals.backgrounds?.value;
  const backgrounds = context.parameters.backgrounds?.values;
  const theme = backgrounds?.find((v) => v.value === backgroundValue)?.name;
  const defaultTheme = context.parameters.backgrounds?.default ?? "light";
  return theme ?? defaultTheme;
}

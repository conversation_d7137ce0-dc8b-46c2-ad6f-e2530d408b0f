import { mergeConfig } from "vite";
import type { StorybookConfig } from "@storybook/react-vite";

const config: StorybookConfig = {
  stories: ["../app/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  addons: [
    "@storybook/addon-links",
    "@storybook/addon-essentials",
    "@storybook/addon-interactions",
    "msw-storybook-addon",
  ],
  framework: {
    name: "@storybook/react-vite",
    options: {
      builder: {
        viteConfigPath: ".storybook/storybook-vite.config.ts",
      },
    },
  },
  typescript: {
    // https://github.com/storybookjs/storybook/issues/26652
    reactDocgen: "react-docgen-typescript",
  },
  async viteFinal(config) {
    return mergeConfig(config, {
      plugins: [],
      resolve: {
        alias: {
          // prettier-ignore
          "@remix-run/react/dist/components": "@remix-run/react/dist/esm/components",
        },
      },
    });
  },
  core: {
    disableTelemetry: true,
  },
  staticDirs: [{ from: "../.storybook/public", to: "/" }],
};

export default config;

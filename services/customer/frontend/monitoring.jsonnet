local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

function(cloud)
  // Alert on 500s observed by the ingress, to capture situations where customer-ui is down and
  // therefore not reporting errors itself. This has a high threshold because we generally want to
  // leave error reporting to the endpoint-level metrics.
  local ingressErrorSpec = {
    displayName: 'Customer UI ingress errors',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (target_proxy_name)(increase(loadbalancing_googleapis_com:https_backend_request_count{monitored_resource="https_lb_rule",target_proxy_name=~".*customer-ui.*"}[5m])) > 50 and
        sum by (target_proxy_name)(increase(loadbalancing_googleapis_com:https_backend_request_count{monitored_resource="https_lb_rule",target_proxy_name=~".*customer-ui.*",response_code_class="500"}[5m])) /
          sum by (target_proxy_name)(increase(loadbalancing_googleapis_com:https_backend_request_count{monitored_resource="https_lb_rule",target_proxy_name=~".*customer-ui.*"}[5m])) > 0.5
      |||,
    },
  };

  // TOOD(sophie): revisit these thresholds once things have settled
  // We set the 4xx error rate to be higher because we have a lot of 401s from users who are not logged in, and this alert is too noisy to be useful
  // We set the 5xx error rate to be lower because 5xx errors are more concerning

  local errorSpec4XX = {
    displayName: 'Customer UI 4XX Errors',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (cluster)(increase(au_http_requests_total{status=~"^4XX$", app=~"customer-ui"}[10m])) /
          (sum by (cluster)(increase(au_http_requests_total{app=~"customer-ui"}[10m]))) > 0.15
      |||,
    },
  };

  local errorSpec5XX = {
    displayName: 'Customer UI 5XX Errors',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (cluster)(increase(au_http_requests_total{status=~"^5XX$", app=~"customer-ui"}[10m])) /
          (sum by (cluster)(increase(au_http_requests_total{app=~"customer-ui"}[10m]))) > 0.03
      |||,
    },
  };

  local prodTeamCreationErrorSpec = {
    displayName: 'Customer UI Team Creation Error Rate',
    conditionPrometheusQueryLanguage: {
      duration: '60s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum(increase(au_customer_ui_new_team_creation_count_total{namespace="central", status!="OK"}[10m])) /
          sum(increase(au_customer_ui_new_team_creation_count_total{namespace="central"}[10m])) > 0.1
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud=cloud,
      condition=ingressErrorSpec,
      name='customer-ui-ingress-errors',
      description="customer-ui's ingress error rate over 50%% for more than 5 minutes for ingress %s." % monitoringLib.label('target_proxy_name'),
      team='insights',
    ),
    monitoringLib.alertPolicy(
      cloud,
      errorSpec4XX,
      'customer-ui-4xx-errors',
      'Customer UI requests return 4XX errors',
      team='insights',
    ),
    monitoringLib.alertPolicy(
      cloud,
      errorSpec5XX,
      'customer-ui-5xx-errors',
      'Customer UI requests return 5XX errors',
      team='insights',
    ),
    monitoringLib.alertPolicy(
      cloud=cloud,
      condition=prodTeamCreationErrorSpec,
      name='customer-ui-team-creation-errors',
      description="Customer UI's team creation error rate is over 10%%",
      team='insights',
    ),
  ]

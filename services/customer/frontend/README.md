# Customer App

#### An administration web app for Augment customers.

📖 See the [Remix docs](https://remix.run/docs) and the [Remix Vite docs](https://remix.run/docs/en/main/guides/vite) for details on supported features.

## Development

Set up your dev deployment:

```bash
$ bazel run //services/deploy:dev_deploy -- --services default customer_ui --operation Apply
```

Run the app in dev mode:

```bash
$ npx ibazel run //services/customer/frontend:dev
```

## HMR (Hot Module Reloading)

\_Port-forwarding is required for the Remix app to run in development mode. This is handled by the `dev.ts` script and requires that KUBE_NAMESPACE= be set and kubectl is installed. This
script reads from the deployed customer_ui pod to determine what ports need to be forwarded. It also reads from the forwards file to determine any additional ports that need to be forwarded. You can
forward additional ports by adding them to "forwards" in the root directory. This format is the environmental variable=service:port. If the port is not specified, it will use the port from the service.
If the service does not exist in the namespace, it will warn you and skip it.

Running bazel is sometimes necessary to generate all the included dependencies. While running with bazel is safer, it is also slower, and HMR may not work correctly.

To run the dev server directly (without <PERSON><PERSON>) run:

```sh
$ pnpm dev
```

To run the dev server directly (with Bazel) run:

```sh
$ npx ibazel run //services/customer/frontend:dev
```

## Storybook

Storybook is a tool for UI component development. It allows you to develop components in isolation, and visualize them within different states. We do not run this through Bazel, but rather through `pnpm`.

```sh
$ pnpm storybook
```

## Deployment

To deploy locally through Bazel run:

```sh
$ bazel run //services/customer/frontend:start
```

To deploy to your dev deployment, run:

```bash
bazel run //services/customer/frontend:kubecfg
```

To access the customer app in your dev deployment, follow these instructions:

1. ask auth leaders (@moogi, @costa) to grant permissions to your dev deployment.
2. visit the support site in your dev deployment `support.dev-{username}.t.us-central1.dev.augmentcode.com` and add yourself the `Admin` role (via `Update User`) under the `Users` tab.
3. visit `app.dev-{username}.us-central1.dev.augmentcode.com` (clear cookies if needed).

## Testing

To run the tests run

```
bazel run //services/customer/frontend:vitest
```

## Data loading

To load data into your dev deployment, follow these instructions:

- Add ``.override('useSharedDevRequestInsightBigquery', false)` to my config.flags in my dev deployment jsonnet in deploy/tenants/namespace_configs/<USERNAME>.
- Delete existing data set

```sh
$ bazel run //services/request_insight/analytics_dataset:kubecfg --delete
```

- Setup new env

```sh
$ bazel run //services/deploy:dev_deploy  -- --services +auth +auth_query +oauth2_proxy +token_exchange +request_insight_analytics --apply
```

- See <USERNAME>\_request_insight_analytics_dataset in the list under system-services-dev at
  `https://console.cloud.google.com/bigquery?project=system-services-dev&ws=!1m0`
- Get a Tenant Id from the support site `https://support.dev-<USERNAME>.t.us-central1.dev.augmentcode.com/tenants` and copy a tenant Id.
  Then run:

```sh
$ ./experimental/jacqueline/populate_dev_dataset.sh dev_<USERNAME>_request_insight_analytics_dataset "2024-07-01" "2024-08-20" <TENANT_ID>

```

## IDE Setup

To debug in VSCode you can use the following launch config:

```js
{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "CustomerUI Dev",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/services/customer/frontend/dev.ts",
      "cwd": "${workspaceFolder}/services/customer/frontend",
      //use tsx not node.
      "runtimeExecutable": "${workspaceFolder}/services/customer/frontend/node_modules/.bin/tsx",
      "outFiles": ["${workspaceFolder}/services/customer/frontend/**/*.js"],
      //this makes logging work.
      "outputCapture": "std",
      "env": {
        "NODE_ENV": "development",
        //set the logging level
        "DEBUG": "debug"
      }
    }
  ]
}
```

## Local Development (Experimental)

1 - Install [kubectl macos](https://kubernetes.io/docs/tasks/tools/install-kubectl-macos/#install-kubectl-on-macos)
2 - Install gcloud
3 - Install auth-plugin

```sh
$ gcloud components install gke-gcloud-auth-plugin
```

4 - Login to gcloud

```sh
$ gcloud auth login
```

5 - Set kubectl context

```sh
$ gcloud container clusters get-credentials system-services-dev --region us-central1 --project system-services-dev
```

6 - Run dev server

```sh
$ pnpm dev
```

## Metrics

Prometheus metrics are exposed to [grafana](https://grafana.us-central1.prod.augmentcode.com/d/bdtcp54ay6vpcb/customer-ui?orgId=1).

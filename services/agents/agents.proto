syntax = "proto3";

package agents;

import "base/blob_names/blob_names.proto";
import "google/rpc/status.proto";
import "services/chat_host/chat.proto";

// All agent APIs are experimental and subject to change.
// Goal is to support an external prototype that makes model calls / agentic tool calls /
// retrieval calls through services.
service Agents {
  rpc LLMGenerate(LLMGenerateRequest) returns (LLMGenerateResponse) {}
  // Agentic tool implementations
  rpc CodebaseRetrieval(CodebaseRetrievalRequest) returns (CodebaseRetrievalResponse) {}
  rpc EditFile(EditFileRequest) returns (EditFileResponse) {}

  rpc ListRemoteTools(ListRemoteToolsRequest) returns (ListRemoteToolsResponse) {}
  rpc RunRemoteTool(RunRemoteToolRequest) returns (RunRemoteToolResponse) {}
  rpc CheckToolSafety(CheckToolSafetyRequest) returns (CheckToolSafetyResponse) {}

  // Revoke the external access for a tool.
  // If there is an oauth grant, it will be revoked to remove access to the external integration.
  // Return code:
  // OK: Operation was successful and access is revoked
  // UNIMPLEMENTED: Revoke is not implemented for this tool yet
  // NOT_FOUND: Tool with specified ID not found
  // ALREADY_EXISTS: Access already revoked; so it is successful but no revoke actually happened.
  // INTERNAL: Revocation failed due to errors on Augment side.  For example a bug or a needed service is unavailable
  // UNKNOWN: Unknown error occurred
  rpc RevokeToolAccess(RevokeToolAccessRequest) returns (RevokeToolAccessResponse) {}

  // Test the connection to an external tool.
  // Return code:
  // OK: Connection is OK and tool is ready to use
  // UNIMPLEMENTED: Connection test not implemented for this tool
  // NOT_FOUND: Tool with specified ID not found
  // UNAVAILABLE: Service is unavailable
  // INTERNAL: Connection error occurred
  // PERMISSION_DENIED: Authentication failure. Likely we did not have proper auth
  // UNKNOWN: Unknown error occurred
  rpc TestToolConnection(TestToolConnectionRequest) returns (TestToolConnectionResponse) {}
}

// Utilizes chat types, but with an interface more workable for
// today's Agent prototype.
// More direct generation bypassing prompt formatting and retrieval,
// and giving control over system prompt from the client (for now),
// temperature, and max tokens
message LLMGenerateRequest {
  // currently unused; expect to later support multiple models from one host
  string model_name = 1;
  // User message, represented as a list of structured content blocks
  repeated chat.ChatRequestNode user_message = 2 [debug_redact = true];
  // History of previous messages alternating request/response
  repeated chat.Exchange dialog = 3 [debug_redact = true];
  int32 max_tokens = 4;
  string system_prompt = 5 [debug_redact = true];
  float temperature = 6; // default: 0.0f
  repeated chat.ToolDefinition tool_definitions = 7 [debug_redact = true];
  optional chat.ToolChoice tool_choice = 8;
}

message LLMGenerateResponse {
  repeated chat.ChatResultNode response_nodes = 1 [debug_redact = true];
}

message CodebaseRetrievalRequest {
  string information_request = 1 [debug_redact = true];
  repeated chat.Exchange dialog = 2 [debug_redact = true];
  repeated base.blob_names.Blobs blobs = 3;
  // Maximum length of formatted_retrieval output
  int32 max_output_length = 4;
  // Flag to disable codebase retrieval
  bool disable_codebase_retrieval = 5;
  // Flag to enable commit retrieval
  bool enable_commit_retrieval = 6;
}

message CodebaseRetrievalResponse {
  // Note: Formatting the chunks in the backend avoids surfacing chunks in the frontend, but
  // results in a single string response which may be difficult to reduce the size of if
  // dialog begins to grow. Perhaps it is reasonable to discard all results of a retrieval
  // tool call from dialog if we discard any.
  string formatted_retrieval = 1 [debug_redact = true];
}

message EditFileRequest {
  string file_path = 1 [debug_redact = true];
  string edit_summary = 2 [debug_redact = true];
  string detailed_edit_description = 3 [debug_redact = true];
  string file_contents = 4 [debug_redact = true];
}

message EditFileResponse {
  string modified_file_contents = 1 [debug_redact = true];
  bool is_error = 2;
}

// Should match with public_api.RemoteToolId
// TODO: deduplicate and have them share the same proto?
enum RemoteToolId {
  UNKNOWN = 0;

  // Google search
  WEB_SEARCH = 1;

  // Jira tools
  // To be deprecated in favor of general JIRA tool
  JIRA_SEARCH = 2;
  JIRA_ISSUE = 3;
  JIRA_PROJECT = 4;

  // Notion tools
  // To be deprecated in favor of general NOTION tool
  NOTION_SEARCH = 5;
  NOTION_PAGE = 6;

  // Linear tools

  // Linear search issues tool is deprecated. Use the Linear API tool instead as it
  // supports a superset of the functionality.
  LINEAR_SEARCH_ISSUES = 7 [deprecated = true];

  // GitHub tools
  GITHUB_API = 8;

  // Confluence tools
  // To be deprecated in favor of general CONFLUENCE tool
  CONFLUENCE_SEARCH = 9;
  CONFLUENCE_CONTENT = 10;
  CONFLUENCE_SPACE = 11;

  LINEAR = 12;

  // Currently not used
  JIRA = 13;
  CONFLUENCE = 14;

  NOTION = 15;

  // Supabase tool
  SUPABASE = 16;

  GLEAN = 17;
}

// Status codes for remote tool execution responses
enum RemoteToolResponseStatus {
  // Unknown status
  TOOL_EXECUTION_UNKNOWN_STATUS = 0;

  // Tool executed successfully
  TOOL_EXECUTION_SUCCESS = 1;

  // Tool not found
  TOOL_NOT_FOUND = 2;

  // Invalid input that violates the tool's input schema
  INVALID_TOOL_INPUT = 3;

  // Tool execution failed
  TOOL_EXECUTION_ERROR = 4;

  // Tool is not available due to config
  TOOL_NOT_AVAILABLE = 5;

  // Auth failed
  TOOL_AUTHENTICATION_ERROR = 6;
}

// Defines the safety level of a tool and whether it requires user approval to run
enum ToolSafety {
  // Tool always needs user approval to run.
  TOOL_UNSAFE = 0;

  // Tool does not need user approval to run.
  TOOL_SAFE = 1;

  // For some inputs, the tool needs user approval and for some it does not.
  TOOL_CHECK = 2;
}

// Should match with public_api.ToolAvailabilityStatus
enum ToolAvailabilityStatus {
  UNKNOWN_STATUS = 0;

  // used for tools that do not need any configuration to be available OR have been configured for the tenant through admin settings
  AVAILABLE = 1;

  // used for tools that require user configuration in the frontend client to
  USER_CONFIG_REQUIRED = 2;
}

message ListRemoteToolsRequest {
  repeated RemoteToolId tool_ids = 1;
}

message ListRemoteToolsResponseMessage {
  chat.ToolDefinition tool_definition = 1;
  RemoteToolId remote_tool_id = 2;
  ToolAvailabilityStatus availability_status = 3;
  ToolSafety tool_safety = 4;
  // OAuth URL that a user can be redirected to for authentication
  string oauth_url = 5;
}

message ListRemoteToolsResponse {
  repeated ListRemoteToolsResponseMessage tools = 1;
}

message AtlassianToolExtraInput {
  string server_url = 1 [debug_redact = true];
  string personal_api_token = 2 [debug_redact = true];
  string username = 3 [debug_redact = true];
}

message NotionToolExtraInput {
  string api_token = 1 [debug_redact = true];
}

message LinearToolExtraInput {
  string api_token = 1 [debug_redact = true];
}

message GitHubToolExtraInput {
  string api_token = 1 [debug_redact = true];
}

// Note that you may want to update request_insight.RIRemoteToolCallRequest, the
// sanitized RI counterpart of this message, when updating this.
message RunRemoteToolRequest {
  string tool_name = 1 [deprecated = true];
  string tool_input_json = 2 [debug_redact = true];
  RemoteToolId tool_id = 3;

  // Optional: extra input for specific tools
  oneof extra_tool_input {
    AtlassianToolExtraInput atlassian_tool_extra_input = 4;
    NotionToolExtraInput notion_tool_extra_input = 5;
    LinearToolExtraInput linear_tool_extra_input = 6;
    GitHubToolExtraInput github_tool_extra_input = 7;
  }
}

message RunRemoteToolResponse {
  // The main output string that will be shown to the model.
  string tool_output = 1 [debug_redact = true];
  // A description of what the tool did, for displaying in the UI or logging.
  string tool_result_message = 2 [debug_redact = true];
  // Boolean to indicate if the tool experienced an error
  bool is_error = 3 [deprecated = true];
  // Status code of the response
  RemoteToolResponseStatus status = 4;
}

message CheckToolSafetyRequest {
  RemoteToolId tool_id = 1;
  string tool_input_json = 2;
}

message CheckToolSafetyResponse {
  bool is_safe = 1;
}

message RevokeToolAccessRequest {
  RemoteToolId tool_id = 1;
}

message RevokeToolAccessResponse {
  // Status of the tool access revocation operation
  google.rpc.Status status = 1;
}

message TestToolConnectionRequest {
  RemoteToolId tool_id = 1;
}

message TestToolConnectionResponse {
  // Status of the tool connection test
  google.rpc.Status status = 1;
}

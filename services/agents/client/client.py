"""A Python client library for the Agents service."""

import logging
from typing import Optional

import grpc

from base.python.grpc import client_options
from services.agents import agents_pb2_grpc


def setup_stub(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> agents_pb2_grpc.AgentsStub:
    """Setup the client stub for the Agents service.

    Args:
        endpoint: The endpoint of the Agents service.
        credentials: The credentials to use for the channel (optional)
        options: Additional gRPC channel options (optional)

    Returns:
        The client stub for the Agents service.
    """
    logging.info("Creating grpc client to %s with options %s", endpoint, options)
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = agents_pb2_grpc.AgentsStub(channel)
    return stub

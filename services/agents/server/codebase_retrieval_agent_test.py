from pathlib import Path
from unittest.mock import Mock

import pytest

from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestText,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    Exchange,
)
from services.agents.agents_pb2 import (
    CodebaseRetrievalRequest,
    CodebaseRetrievalResponse,
)
from services.agents.server import codebase_retrieval_agent
from services.agents.server.codebase_retrieval_agent import (
    CodebaseMultiRetrievalAgent,
    CodebaseQueryRewritingRetrievalAgent,
    create_directory_structure,
    format_dialog_as_string,
    format_directory_subtree,
)
from services.chat_host import chat_pb2
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval.retriever import RetrievalChunk, Retriever
from services.request_insight.publisher import request_insight_publisher


def test_format_dialog_as_string():
    """Test formatting dialog messages with various types of messages."""
    # Create a mix of different message types
    dialog = [
        # Exchange 1: Simple text exchange
        Exchange(
            request_message="What is the purpose of foo?",
            response_text="Foo is a utility class.",
            request_id="1",
        ),
        # Exchange 2: Structured exchange with tool use
        Exchange(
            request_message=[
                ChatRequestNode(
                    id=1,
                    type=ChatRequestNodeType.TEXT,
                    text_node=ChatRequestText(
                        content="Show me the implementation of foo"
                    ),
                    tool_result_node=None,
                )
            ],
            response_text=[
                ChatResultNode(
                    id=2,
                    type=ChatResultNodeType.RAW_RESPONSE,
                    content="Sure, let me find it for you.",
                    tool_use=None,
                ),
                ChatResultNode(
                    id=3,
                    type=ChatResultNodeType.TOOL_USE,
                    content="",
                    tool_use=ChatResultToolUse(
                        name="ask_for_codebase_snippets",
                        input={
                            "code_section_requests": [
                                {"description": "foo implementation"}
                            ]
                        },
                        tool_use_id="tool1",
                    ),
                ),
            ],
            request_id="2",
        ),
        # Exchange 3: Exchange with tool result
        Exchange(
            request_message=[
                ChatRequestNode(
                    id=1,
                    type=ChatRequestNodeType.TOOL_RESULT,
                    text_node=None,
                    tool_result_node=ChatRequestToolResult(
                        tool_use_id="tool1",
                        content="class Foo:\n    pass",
                        is_error=False,
                        request_id="tool_result_request_id",
                    ),
                )
            ],
            response_text=[
                ChatResultNode(
                    id=1,
                    type=ChatResultNodeType.RAW_RESPONSE,
                    content="This is a simple implementation of Foo.",
                    tool_use=None,
                )
            ],
            request_id="3",
        ),
    ]

    # Test with different char budgets
    # Full budget should include all messages
    result_full = format_dialog_as_string(dialog, char_budget=1000)
    expected_order = [
        "User message:\nWhat is the purpose of foo?",
        "Assistant message:\nFoo is a utility class",
        "User message:\nShow me the implementation of foo",
        "Assistant message:\nSure, let me find it for you",
        "Tool call:ask_for_codebase_snippets:",
        "Tool result:\nclass Foo:\n    pass",
        "Assistant message:\nThis is a simple implementation of Foo",
    ]
    for i, expected in enumerate(expected_order):
        assert (
            expected in result_full
        ), f"Expected message {i + 1} not found or out of order"
        if i > 0:
            assert result_full.index(expected) > result_full.index(
                expected_order[i - 1]
            ), f"Message {i + 1} is not in the correct order"

    # Limited budget should only include most recent messages
    result_limited = format_dialog_as_string(dialog, char_budget=50)
    assert "What is the purpose of foo?" not in result_limited
    assert len(result_limited) <= 50

    # Zero budget should return empty string
    result_zero = format_dialog_as_string(dialog, char_budget=0)
    assert result_zero == ""


# Tests for CodebaseMultiRetrievalAgent
@pytest.fixture
def mock_codebase_multi_retrieval_agent():
    retriever = Mock()
    ri_publisher = Mock()
    return CodebaseMultiRetrievalAgent(
        retriever=retriever,
        ri_publisher=ri_publisher,
    )


# Tests for CodebaseQueryRewritingRetrievalAgent
@pytest.fixture
def mock_codebase_query_rewriting_retrieval_agent():
    retriever = Mock()
    ri_publisher = Mock()
    return CodebaseQueryRewritingRetrievalAgent(
        retriever=retriever,
        ri_publisher=ri_publisher,
    )


class TestCodebaseMultiRetrievalAgent:
    def test_run(self, mock_codebase_multi_retrieval_agent):
        # Mock the necessary methods
        mock_codebase_multi_retrieval_agent.get_retriever_queries = Mock(
            # Return both queries and exchange
            return_value=([{"description": "test query"}], Mock())
        )

        # Mock the _retrieve function instead of the retrieve method
        # We need to patch the module function
        original_retrieve = codebase_retrieval_agent._retrieve
        codebase_retrieval_agent._retrieve = Mock(
            return_value=[
                RetrievalChunk(
                    text="test chunk",
                    path="test/path",
                    char_start=0,
                    char_end=10,
                    blob_name="test_blob",
                    chunk_index=0,
                )
            ]
        )

        mock_codebase_multi_retrieval_agent.get_final_round_retriever_queries = Mock(
            return_value=(
                [{"description": "final query"}],
                Mock(),
            )  # Also return exchange here
        )
        mock_codebase_multi_retrieval_agent.format_retrieval_for_output = Mock(
            return_value="formatted output"
        )
        # Mock the _record_exchanges function to avoid protobuf issues
        original_record_exchanges = codebase_retrieval_agent._record_exchanges
        codebase_retrieval_agent._record_exchanges = Mock()

        # Create a mock request
        request = CodebaseRetrievalRequest(information_request="test request")
        request_context = RequestContext(
            request_id="test_id",
            request_session_id="test_session",
            request_source="test_source",
        )
        auth_info = AuthInfo(
            tenant_id="test_tenant",
            tenant_name="test_tenant_name",
            shard_namespace="test_namespace",
            cloud="test_cloud",
        )

        try:
            # Run the method
            response = mock_codebase_multi_retrieval_agent.run(
                request, Mock(), request_context, auth_info
            )

            # Assert the response
            assert isinstance(response, CodebaseRetrievalResponse)
            assert response.formatted_retrieval == "formatted output"
        finally:
            # Restore the original functions
            codebase_retrieval_agent._retrieve = original_retrieve
            codebase_retrieval_agent._record_exchanges = original_record_exchanges

    def test_get_retriever_queries(self, mock_codebase_multi_retrieval_agent):
        # Create a properly structured mock tool use response
        tool_use = Mock()
        tool_use.tool_use_id = "test_id"
        tool_use.tool_name = "test_tool"
        tool_use.input = {"code_section_requests": [{"description": "test query"}]}

        message = Mock()
        message.tool_use = tool_use

        # Configure prompt_cache_usage mock with integer attributes
        prompt_cache_usage = Mock()
        prompt_cache_usage.input_tokens = 0
        prompt_cache_usage.cache_read_input_tokens = 0
        prompt_cache_usage.cache_creation_input_tokens = 0
        message.prompt_cache_usage = prompt_cache_usage

        # Mock generate_response_stream to return both message and prompt_cache_usage
        llm_client = Mock()
        llm_client.generate_response_stream.return_value = iter([message])

        # Create request context
        request_context = RequestContext(
            request_id="test_id",
            request_session_id="test_session",
            request_source="test_source",
        )

        # Mock _generate_tool_use to avoid the _record_prompt_cache_usage issue
        original_generate_tool_use = codebase_retrieval_agent._generate_tool_use
        codebase_retrieval_agent._generate_tool_use = Mock(return_value=tool_use)

        try:
            # Call the method
            queries, exchange = (
                mock_codebase_multi_retrieval_agent.get_retriever_queries(
                    llm_client,
                    "test request",
                    request_context=request_context,
                    dialog_messages=[],  # Add empty dialog messages
                    auth_info=AuthInfo(
                        tenant_id="test_tenant",
                        tenant_name="test_tenant_name",
                        shard_namespace="test_namespace",
                        cloud="test_cloud",
                    ),
                )
            )
        finally:
            # Restore the original function
            codebase_retrieval_agent._generate_tool_use = original_generate_tool_use

        # Assert the queries result
        assert queries == [{"description": "test query"}]

        # Assert the exchange
        assert exchange.request_nodes[0].type == ChatRequestNodeType.TEXT
        assert (
            exchange.request_nodes[0].text_node.content
            == "Intermediate retrieval query generation."
        )
        assert exchange.response_nodes[0].type == ChatResultNodeType.TOOL_USE
        assert exchange.response_nodes[0].tool_use.tool_use_id == "test_id"
        assert exchange.response_nodes[0].tool_use.tool_name == "test_tool"
        assert (
            exchange.response_nodes[0].tool_use.input_json
            == '{"code_section_requests": [{"description": "test query"}]}'
        )
        assert exchange.request_id == "test_id"

    def test_get_final_round_retriever_queries(
        self, mock_codebase_multi_retrieval_agent
    ):
        # Create a properly structured mock tool use response
        tool_use = Mock()
        tool_use.tool_use_id = "test_id"
        tool_use.tool_name = "test_tool"
        tool_use.input = {"code_section_requests": [{"description": "final query"}]}

        message = Mock()
        message.tool_use = tool_use

        # Configure prompt_cache_usage mock with integer attributes
        prompt_cache_usage = Mock()
        prompt_cache_usage.input_tokens = 0
        prompt_cache_usage.cache_read_input_tokens = 0
        prompt_cache_usage.cache_creation_input_tokens = 0
        message.prompt_cache_usage = prompt_cache_usage

        # Mock generate_response_stream to return both message and prompt_cache_usage
        llm_client = Mock()
        llm_client.generate_response_stream.return_value = [message]

        # Create request context
        request_context = RequestContext(
            request_id="test_id",
            request_session_id="test_session",
            request_source="test_source",
        )

        # Mock _generate_tool_use to avoid the _record_prompt_cache_usage issue
        original_generate_tool_use = codebase_retrieval_agent._generate_tool_use
        codebase_retrieval_agent._generate_tool_use = Mock(return_value=tool_use)

        try:
            # Call the method
            queries, exchange = (
                mock_codebase_multi_retrieval_agent.get_final_round_retriever_queries(
                    llm_client,
                    "test request",
                    [
                        (
                            {"description": "test query"},
                            [
                                RetrievalChunk(
                                    text="test chunk",
                                    path="test/path",
                                    char_start=0,
                                    char_end=10,
                                    blob_name="test_blob",
                                    chunk_index=0,
                                )
                            ],
                        )
                    ],
                    request_context=request_context,
                    auth_info=AuthInfo(
                        tenant_id="test_tenant",
                        tenant_name="test_tenant_name",
                        shard_namespace="test_namespace",
                        cloud="test_cloud",
                    ),
                )
            )
        finally:
            # Restore the original function
            codebase_retrieval_agent._generate_tool_use = original_generate_tool_use

        # Assert the queries result
        assert queries == [{"description": "final query"}]

        # Assert the exchange
        assert exchange.request_nodes[0].type == ChatRequestNodeType.TEXT
        assert (
            exchange.request_nodes[0].text_node.content
            == "Final round retrieval query generation."
        )
        assert exchange.response_nodes[0].type == ChatResultNodeType.TOOL_USE
        assert exchange.response_nodes[0].tool_use.tool_use_id == "test_id"
        assert exchange.response_nodes[0].tool_use.tool_name == "test_tool"
        assert (
            exchange.response_nodes[0].tool_use.input_json
            == '{"code_section_requests": [{"description": "final query"}]}'
        )
        assert exchange.request_id == "test_id"

    def test_get_retriever_queries_invalid_tool_call(
        self, mock_codebase_multi_retrieval_agent
    ):
        """Test that get_retriever_queries and get_final_round_retriever_queries
        return empty list if tool call is invalid
        """

        missing_call = Mock()
        missing_call.text = "train of thought"
        missing_call.tool_use = None

        invalid_call = Mock()
        invalid_call.tool_use = Mock()
        invalid_call.tool_use.tool_use_id = "test_id"
        invalid_call.tool_use.tool_name = "test_tool"
        invalid_call.tool_use.input = {
            "code_section_requests": "unstructured test query"
        }

        request_context = RequestContext(
            request_id="test_id",
            request_session_id="test_session",
            request_source="test_source",
        )

        # Mock _generate_tool_use to avoid the _record_prompt_cache_usage issue
        original_generate_tool_use = codebase_retrieval_agent._generate_tool_use

        for message in [missing_call, invalid_call]:
            prompt_cache_usage = Mock()
            prompt_cache_usage.input_tokens = 0
            prompt_cache_usage.cache_read_input_tokens = 0
            prompt_cache_usage.cache_creation_input_tokens = 0
            message.prompt_cache_usage = prompt_cache_usage

            llm_client = Mock()
            llm_client.generate_response_stream.return_value = [message]

            # Set the mock to return the current message's tool_use
            codebase_retrieval_agent._generate_tool_use = Mock(
                return_value=message.tool_use
            )

            queries, exchange = (
                mock_codebase_multi_retrieval_agent.get_retriever_queries(
                    llm_client,
                    "test request",
                    request_context=request_context,
                    dialog_messages=[],
                    auth_info=AuthInfo(
                        tenant_id="test_tenant",
                        tenant_name="test_tenant_name",
                        shard_namespace="test_namespace",
                        cloud="test_cloud",
                    ),
                )
            )
            assert len(queries) == 0
            if message is invalid_call:
                assert (
                    exchange.response_nodes[0].tool_use.input_json
                    == '{"code_section_requests": "unstructured test query"}'
                )

            queries, exchange = (
                mock_codebase_multi_retrieval_agent.get_final_round_retriever_queries(
                    llm_client,
                    "test request",
                    [
                        (
                            {"description": "test query"},
                            [
                                RetrievalChunk(
                                    text="test chunk",
                                    path="test/path",
                                    char_start=0,
                                    char_end=10,
                                    blob_name="test_blob",
                                    chunk_index=0,
                                )
                            ],
                        )
                    ],
                    request_context=request_context,
                    dialog_messages=[],
                    auth_info=AuthInfo(
                        tenant_id="test_tenant",
                        tenant_name="test_tenant_name",
                        shard_namespace="test_namespace",
                        cloud="test_cloud",
                    ),
                )
            )
            assert len(queries) == 0
            # Assert that the invalid tool call is returned to be logged
            if message is invalid_call:
                assert (
                    exchange.response_nodes[0].tool_use.input_json
                    == '{"code_section_requests": "unstructured test query"}'
                )

        # Restore the original function
        codebase_retrieval_agent._generate_tool_use = original_generate_tool_use

    def test_retrieval_result_no_queries(self, mock_codebase_multi_retrieval_agent):
        """Test that retrieval returns a failure message if no queries are generated"""
        agent = mock_codebase_multi_retrieval_agent
        agent.get_retriever_queries = Mock()
        agent.get_retriever_queries.return_value = [], Mock()
        agent.get_final_round_retriever_queries = Mock()
        agent.get_final_round_retriever_queries.return_value = [], Mock()

        # Mock the _record_exchanges function to avoid protobuf issues
        original_record_exchanges = codebase_retrieval_agent._record_exchanges
        codebase_retrieval_agent._record_exchanges = Mock()

        # Actually won't be used, as we are already mocking the functions which call it
        llm_client = Mock()
        request = CodebaseRetrievalRequest(information_request="test request")
        request_context = RequestContext(
            request_id="test_id",
            request_session_id="test_session",
            request_source="test_source",
        )
        auth_info = AuthInfo(
            tenant_id="test_tenant",
            tenant_name="test_tenant_name",
            shard_namespace="test_namespace",
            cloud="test_cloud",
        )

        try:
            response = agent.run(request, llm_client, request_context, auth_info)
            assert agent.get_retriever_queries.called
            assert not agent.get_final_round_retriever_queries.called
            assert codebase_retrieval_agent._record_exchanges.call_count == 1
            assert response.formatted_retrieval == "Retrieval failed. Please try again."

            agent.get_retriever_queries.return_value = (
                [{"description": "test query"}],
                Mock(),
            )
            agent.retriever.retrieve.return_value = Mock(
                get_retrieved_chunks=Mock(
                    return_value=[
                        RetrievalChunk(
                            text="test chunk",
                            path="test/path",
                            char_start=0,
                            char_end=10,
                            blob_name="test_blob",
                            chunk_index=0,
                        )
                    ]
                )
            )

            response = agent.run(request, llm_client, request_context, auth_info)
            assert agent.get_retriever_queries.called
            assert agent.get_final_round_retriever_queries.called
            assert codebase_retrieval_agent._record_exchanges.call_count == 2
            assert response.formatted_retrieval == "Retrieval failed. Please try again."
            assert llm_client.generate_response_stream.call_count == 0
        finally:
            # Restore the original function
            codebase_retrieval_agent._record_exchanges = original_record_exchanges

    def test_format_retrieval_for_intermediate(self):
        # Prepare test data
        chunks = [
            RetrievalChunk(
                text="test chunk",
                path="test/path",
                blob_name="test_blob",
                char_start=0,
                char_end=10,
                chunk_index=0,
            )
        ]

        # Call the function directly
        result, _ = codebase_retrieval_agent._format_retrieval(chunks, 1000)

        # Assert the result
        assert "The following code sections were retrieved:" in result
        assert "Path: test/path" in result
        assert "test chunk" in result

    def test_format_retrieval_for_output(self, mock_codebase_multi_retrieval_agent):
        # Prepare test data
        query_result_list = [
            (
                {"description": "test query", "critical": True},
                [
                    RetrievalChunk(
                        text="test chunk",
                        path="test/path",
                        blob_name="test_blob",
                        char_start=0,
                        char_end=10,
                        chunk_index=0,
                    )
                ],
            )
        ]

        # Call the method
        result = mock_codebase_multi_retrieval_agent.format_retrieval_for_output(
            query_result_list, 1000
        )

        # Assert the result
        assert "The following code sections were retrieved:" in result
        assert "Path: test/path" in result
        assert "test chunk" in result

    def test_retrieve(self):
        # Create a mock retriever
        retriever = Mock()
        retriever.retrieve.return_value = Mock(
            get_retrieved_chunks=Mock(
                return_value=[
                    RetrievalChunk(
                        text="test chunk",
                        path="test/path",
                        char_start=0,
                        char_end=10,
                        blob_name="test_blob",
                        chunk_index=0,
                    )
                ]
            )
        )

        # Call the _retrieve function directly
        result = codebase_retrieval_agent._retrieve(
            retriever=retriever,
            request_description="test description",
            request_path="test/path",
            request_contains_string="test string",
            request_context=RequestContext(
                request_id="test_id",
                request_session_id="test_session",
                request_source="test_source",
            ),
            auth_info=AuthInfo(
                tenant_id="test_tenant",
                tenant_name="test_tenant_name",
                shard_namespace="test_namespace",
                cloud="test_cloud",
            ),
            blobs=[],
            max_chunks=1024,
        )

        # Create the expected chunk
        expected_chunk = RetrievalChunk(
            text="test chunk",
            path="test/path",
            char_start=0,
            char_end=10,
            blob_name="test_blob",
            chunk_index=0,
        )

        # Assert the result
        assert len(result) == 1
        assert result[0] == expected_chunk


class TestCreateDirectoryStructure:
    def test_empty_chunks(self):
        """Test creating directory structure with empty chunks list."""
        chunks = []
        result = create_directory_structure(chunks)

        # Should have an empty root directory
        assert "" in result
        assert len(result[""]["files"]) == 0
        assert len(result[""]["dirs"]) == 0
        assert len(result[""]["dir_name_set"]) == 0

    def test_single_file_root(self):
        """Test with a single file in the root directory."""
        chunks = [
            RetrievalChunk(
                text="content",
                path="file.py",
                char_start=0,
                char_end=7,
                blob_name="test_blob",
                chunk_index=0,
            )
        ]
        result = create_directory_structure(chunks)

        # Should have one file in the root directory
        assert "" in result
        assert len(result[""]["files"]) == 1
        assert str(result[""]["files"][0]) == "file.py"
        assert len(result[""]["dirs"]) == 0

    def test_multiple_files_root(self):
        """Test with multiple files in the root directory."""
        chunks = [
            RetrievalChunk(
                text="content1",
                path="file1.py",
                char_start=0,
                char_end=8,
                blob_name="test_blob1",
                chunk_index=0,
            ),
            RetrievalChunk(
                text="content2",
                path="file2.py",
                char_start=0,
                char_end=8,
                blob_name="test_blob2",
                chunk_index=0,
            ),
            RetrievalChunk(
                text="content3",
                path="file3.py",
                char_start=0,
                char_end=8,
                blob_name="test_blob3",
                chunk_index=0,
            ),
        ]
        result = create_directory_structure(chunks)

        # Should have three files in the root directory, sorted alphabetically
        assert "" in result
        assert len(result[""]["files"]) == 3
        assert str(result[""]["files"][0]) == "file1.py"
        assert str(result[""]["files"][1]) == "file2.py"
        assert str(result[""]["files"][2]) == "file3.py"
        assert len(result[""]["dirs"]) == 0

    def test_files_in_subdirectory(self):
        """Test with files in a subdirectory."""
        chunks = [
            RetrievalChunk(
                text="content1",
                path="dir/file1.py",
                char_start=0,
                char_end=8,
                blob_name="test_blob1",
                chunk_index=0,
            ),
            RetrievalChunk(
                text="content2",
                path="dir/file2.py",
                char_start=0,
                char_end=8,
                blob_name="test_blob2",
                chunk_index=0,
            ),
        ]
        result = create_directory_structure(chunks)

        # Should have a directory in the root and files in the subdirectory
        assert "" in result
        assert "dir" in result
        assert len(result[""]["files"]) == 0
        assert len(result[""]["dirs"]) == 1
        assert str(result[""]["dirs"][0]) == "dir"
        assert "dir" in result[""]["dir_name_set"]

        # Check the subdirectory
        assert len(result["dir"]["files"]) == 2
        assert str(result["dir"]["files"][0]) == "dir/file1.py"
        assert str(result["dir"]["files"][1]) == "dir/file2.py"

    def test_nested_directories(self):
        """Test with nested directory structure."""
        chunks = [
            RetrievalChunk(
                text="content1",
                path="dir1/dir2/file.py",
                char_start=0,
                char_end=8,
                blob_name="test_blob",
                chunk_index=0,
            ),
        ]
        result = create_directory_structure(chunks)

        # Check the directory structure
        assert "" in result
        assert "dir1" in result
        assert "dir1/dir2" in result

        # Check the root directory
        assert len(result[""]["files"]) == 0
        assert len(result[""]["dirs"]) == 1
        assert str(result[""]["dirs"][0]) == "dir1"

        # Check the first level directory
        assert len(result["dir1"]["files"]) == 0
        assert len(result["dir1"]["dirs"]) == 1
        assert str(result["dir1"]["dirs"][0]) == "dir1/dir2"

        # Check the second level directory
        assert len(result["dir1/dir2"]["files"]) == 1
        assert str(result["dir1/dir2"]["files"][0]) == "dir1/dir2/file.py"

    def test_duplicate_paths(self):
        """Test with duplicate file paths."""
        chunks = [
            RetrievalChunk(
                text="content1",
                path="dir/file.py",
                char_start=0,
                char_end=8,
                blob_name="test_blob1",
                chunk_index=0,
            ),
            RetrievalChunk(
                text="content2",
                path="dir/file.py",
                char_start=0,
                char_end=8,
                blob_name="test_blob2",
                chunk_index=0,
            ),  # Duplicate
        ]
        result = create_directory_structure(chunks)

        # Should only have one file in the directory (duplicates removed)
        assert "dir" in result
        assert len(result["dir"]["files"]) == 1
        assert str(result["dir"]["files"][0]) == "dir/file.py"

    def test_while_loop_conditions_with_normal_path(self):
        """Test the while loop with a normal path that has a name."""
        chunks = [
            RetrievalChunk(
                text="content",
                path="dir1/dir2/dir3/file.py",
                char_start=0,
                char_end=7,
                blob_name="test_blob",
                chunk_index=0,
            )
        ]
        result = create_directory_structure(chunks)

        # Verify all directories were created
        assert "" in result
        assert "dir1" in result
        assert "dir1/dir2" in result
        assert "dir1/dir2/dir3" in result

        # Verify the file is in the deepest directory
        assert len(result["dir1/dir2/dir3"]["files"]) == 1
        assert str(result["dir1/dir2/dir3"]["files"][0]) == "dir1/dir2/dir3/file.py"

        # Verify directory structure is correct
        assert len(result[""]["dirs"]) == 1
        assert str(result[""]["dirs"][0]) == "dir1"
        assert len(result["dir1"]["dirs"]) == 1
        assert str(result["dir1"]["dirs"][0]) == "dir1/dir2"
        assert len(result["dir1/dir2"]["dirs"]) == 1
        assert str(result["dir1/dir2"]["dirs"][0]) == "dir1/dir2/dir3"

    def test_while_loop_conditions_with_dot_path(self):
        """Test the while loop with a path that is Path('.')."""
        # Create a chunk with a path that will become Path('.') after parent operations
        chunks = [
            RetrievalChunk(
                text="content",
                path="file.py",  # This path's parent is Path('.')
                char_start=0,
                char_end=7,
                blob_name="test_blob",
                chunk_index=0,
            )
        ]
        result = create_directory_structure(chunks)

        # Verify the file is in the root directory
        assert "" in result
        assert len(result[""]["files"]) == 1
        assert str(result[""]["files"][0]) == "file.py"

        # Verify no directories were created
        assert len(result[""]["dirs"]) == 0

    def test_while_loop_conditions_with_empty_path(self):
        """Test the while loop with a path that is Path('')."""
        # Create a chunk with a path that will become Path('') after parent operations
        chunks = [
            RetrievalChunk(
                text="content",
                path="",  # Empty path
                char_start=0,
                char_end=7,
                blob_name="test_blob",
                chunk_index=0,
            )
        ]
        result = create_directory_structure(chunks)

        # Verify the root directory exists but has no files (since path is invalid)
        assert "" in result
        assert len(result[""]["files"]) == 0
        assert len(result[""]["dirs"]) == 0

    def test_while_loop_termination(self):
        """Test that the while loop terminates properly for all path types."""
        # Create chunks with various path types to test all termination conditions
        chunks = [
            RetrievalChunk(
                text="content1",
                path="dir1/file1.py",  # Normal path
                char_start=0,
                char_end=8,
                blob_name="test_blob1",
                chunk_index=0,
            ),
            RetrievalChunk(
                text="content2",
                path="file2.py",  # Path with parent = '.'
                char_start=0,
                char_end=8,
                blob_name="test_blob2",
                chunk_index=0,
            ),
            RetrievalChunk(
                text="content3",
                path="",  # Empty path
                char_start=0,
                char_end=8,
                blob_name="test_blob3",
                chunk_index=0,
            ),
        ]
        result = create_directory_structure(chunks)

        # Verify the structure is correct
        assert "" in result
        assert "dir1" in result

        # Check root directory
        assert len(result[""]["files"]) == 1  # Only file2.py should be here
        assert str(result[""]["files"][0]) == "file2.py"
        assert len(result[""]["dirs"]) == 1
        assert str(result[""]["dirs"][0]) == "dir1"

        # Check dir1 directory
        assert len(result["dir1"]["files"]) == 1
        assert str(result["dir1"]["files"][0]) == "dir1/file1.py"


class TestCodebaseQueryRewritingRetrievalAgent:
    def test_run(self, mock_codebase_query_rewriting_retrieval_agent):
        # Mock the necessary methods
        mock_codebase_query_rewriting_retrieval_agent.get_retriever_queries = Mock(
            # Return queries, exchange, and used_initial_retrievals
            return_value=([{"description": "test query"}], Mock(), [])
        )

        # Mock the _retrieve function instead of the retrieve method
        original_retrieve = codebase_retrieval_agent._retrieve
        codebase_retrieval_agent._retrieve = Mock(
            return_value=[
                RetrievalChunk(
                    text="test chunk",
                    path="test/path",
                    char_start=0,
                    char_end=10,
                    blob_name="test_blob",
                    chunk_index=0,
                )
            ]
        )

        # Mock the _record_exchanges function to avoid protobuf issues
        original_record_exchanges = codebase_retrieval_agent._record_exchanges
        codebase_retrieval_agent._record_exchanges = Mock()

        # Mock the _format_retrieval function
        original_format_retrieval = codebase_retrieval_agent._format_retrieval
        codebase_retrieval_agent._format_retrieval = Mock(
            return_value=("formatted output", [])
        )

        # Create a mock request
        request = CodebaseRetrievalRequest(information_request="test request")
        request_context = RequestContext(
            request_id="test_id",
            request_session_id="test_session",
            request_source="test_source",
        )
        auth_info = AuthInfo(
            tenant_id="test_tenant",
            tenant_name="test_tenant_name",
            shard_namespace="test_namespace",
            cloud="test_cloud",
        )

        try:
            # Run the method
            response = mock_codebase_query_rewriting_retrieval_agent.run(
                request, Mock(), request_context, auth_info
            )

            # Assert the response
            assert isinstance(response, CodebaseRetrievalResponse)
            assert response.formatted_retrieval == "formatted output"

            # Verify the correct methods were called
            assert mock_codebase_query_rewriting_retrieval_agent.get_retriever_queries.called
            assert (
                codebase_retrieval_agent._retrieve.call_count >= 2
            )  # Initial + query retrieval
            assert codebase_retrieval_agent._record_exchanges.called
            assert codebase_retrieval_agent._format_retrieval.called
        finally:
            # Restore the original functions
            codebase_retrieval_agent._retrieve = original_retrieve
            codebase_retrieval_agent._record_exchanges = original_record_exchanges
            codebase_retrieval_agent._format_retrieval = original_format_retrieval

    def test_get_retriever_queries(self, mock_codebase_query_rewriting_retrieval_agent):
        # Create a properly structured mock tool use response
        tool_use = Mock()
        tool_use.tool_use_id = "test_id"
        tool_use.tool_name = "test_tool"
        tool_use.input = {"code_section_requests": [{"description": "test query"}]}

        message = Mock()
        message.tool_use = tool_use

        # Configure prompt_cache_usage mock with integer attributes
        prompt_cache_usage = Mock()
        prompt_cache_usage.input_tokens = 0
        prompt_cache_usage.cache_read_input_tokens = 0
        prompt_cache_usage.cache_creation_input_tokens = 0
        message.prompt_cache_usage = prompt_cache_usage

        # Mock generate_response_stream to return both message and prompt_cache_usage
        llm_client = Mock()
        llm_client.generate_response_stream.return_value = iter([message])

        # Create request context
        request_context = RequestContext(
            request_id="test_id",
            request_session_id="test_session",
            request_source="test_source",
        )

        # Mock _generate_tool_use to avoid the _record_prompt_cache_usage issue
        original_generate_tool_use = codebase_retrieval_agent._generate_tool_use
        codebase_retrieval_agent._generate_tool_use = Mock(return_value=tool_use)

        # Mock _format_retrieval to return formatted text and chunks
        original_format_retrieval = codebase_retrieval_agent._format_retrieval
        codebase_retrieval_agent._format_retrieval = Mock(
            return_value=(
                "formatted retrieval",
                [
                    RetrievalChunk(
                        text="test chunk",
                        path="test/path",
                        char_start=0,
                        char_end=10,
                        blob_name="test_blob",
                        chunk_index=0,
                    )
                ],
            )
        )

        # Mock format_directory_subtree
        original_format_directory_subtree = (
            codebase_retrieval_agent.format_directory_subtree
        )
        codebase_retrieval_agent.format_directory_subtree = Mock(
            return_value=("formatted directory", {"truncated": False})
        )

        # Create initial retrievals
        initial_retrievals = [
            RetrievalChunk(
                text="initial chunk",
                path="initial/path",
                char_start=0,
                char_end=13,
                blob_name="initial_blob",
                chunk_index=0,
            )
        ]

        try:
            # Call the method
            queries, exchange, used_chunks = (
                mock_codebase_query_rewriting_retrieval_agent.get_retriever_queries(
                    llm_client,
                    "test request",
                    request_context=request_context,
                    auth_info=AuthInfo(
                        tenant_id="test_tenant",
                        tenant_name="test_tenant_name",
                        shard_namespace="test_namespace",
                        cloud="test_cloud",
                    ),
                    dialog_messages=[],
                    initial_retrievals=initial_retrievals,
                    max_initial_retrieval_chars=1000,
                )
            )

            # Assert the queries result
            assert queries == [{"description": "test query"}]

            # Assert the exchange
            assert exchange.request_nodes[0].type == ChatRequestNodeType.TEXT
            assert (
                exchange.request_nodes[0].text_node.content
                == "Intermediate retrieval query generation."
            )
            assert exchange.response_nodes[0].type == ChatResultNodeType.TOOL_USE
            assert exchange.response_nodes[0].tool_use.tool_use_id == "test_id"
            assert exchange.response_nodes[0].tool_use.tool_name == "test_tool"
            assert (
                exchange.response_nodes[0].tool_use.input_json
                == '{"code_section_requests": [{"description": "test query"}]}'
            )
            assert exchange.request_id == "test_id"

            # Assert the used chunks
            assert len(used_chunks) == 1
            assert used_chunks[0].text == "test chunk"
            assert used_chunks[0].path == "test/path"
        finally:
            # Restore the original functions
            codebase_retrieval_agent._generate_tool_use = original_generate_tool_use
            codebase_retrieval_agent._format_retrieval = original_format_retrieval
            codebase_retrieval_agent.format_directory_subtree = (
                original_format_directory_subtree
            )

    def test_get_retriever_queries_invalid_tool_call(
        self, mock_codebase_query_rewriting_retrieval_agent
    ):
        """Test that get_retriever_queries returns empty list if tool call is invalid"""

        missing_call = Mock()
        missing_call.text = "train of thought"
        missing_call.tool_use = None

        invalid_call = Mock()
        invalid_call.tool_use = Mock()
        invalid_call.tool_use.tool_use_id = "test_id"
        invalid_call.tool_use.tool_name = "test_tool"
        invalid_call.tool_use.input = {
            "code_section_requests": "unstructured test query"
        }

        request_context = RequestContext(
            request_id="test_id",
            request_session_id="test_session",
            request_source="test_source",
        )

        # Mock _generate_tool_use to avoid the _record_prompt_cache_usage issue
        original_generate_tool_use = codebase_retrieval_agent._generate_tool_use

        # Mock _format_retrieval to return formatted text and chunks
        original_format_retrieval = codebase_retrieval_agent._format_retrieval
        codebase_retrieval_agent._format_retrieval = Mock(
            return_value=(
                "formatted retrieval",
                [
                    RetrievalChunk(
                        text="test chunk",
                        path="test/path",
                        char_start=0,
                        char_end=10,
                        blob_name="test_blob",
                        chunk_index=0,
                    )
                ],
            )
        )

        # Mock format_directory_subtree
        original_format_directory_subtree = (
            codebase_retrieval_agent.format_directory_subtree
        )
        codebase_retrieval_agent.format_directory_subtree = Mock(
            return_value=("formatted directory", {"truncated": False})
        )

        # Create initial retrievals
        initial_retrievals = [
            RetrievalChunk(
                text="initial chunk",
                path="initial/path",
                char_start=0,
                char_end=13,
                blob_name="initial_blob",
                chunk_index=0,
            )
        ]

        for message in [missing_call, invalid_call]:
            prompt_cache_usage = Mock()
            prompt_cache_usage.input_tokens = 0
            prompt_cache_usage.cache_read_input_tokens = 0
            prompt_cache_usage.cache_creation_input_tokens = 0
            message.prompt_cache_usage = prompt_cache_usage

            llm_client = Mock()
            llm_client.generate_response_stream.return_value = [message]

            # Set the mock to return the current message's tool_use
            codebase_retrieval_agent._generate_tool_use = Mock(
                return_value=message.tool_use
            )

            try:
                queries, exchange, used_chunks = (
                    mock_codebase_query_rewriting_retrieval_agent.get_retriever_queries(
                        llm_client,
                        "test request",
                        request_context=request_context,
                        auth_info=AuthInfo(
                            tenant_id="test_tenant",
                            tenant_name="test_tenant_name",
                            shard_namespace="test_namespace",
                            cloud="test_cloud",
                        ),
                        dialog_messages=[],
                        initial_retrievals=initial_retrievals,
                        max_initial_retrieval_chars=1000,
                    )
                )

                # Assert the queries result
                assert len(queries) == 0

                # Assert the exchange
                if message is invalid_call:
                    assert (
                        exchange.response_nodes[0].tool_use.input_json
                        == '{"code_section_requests": "unstructured test query"}'
                    )
            finally:
                pass

        # Restore the original functions
        codebase_retrieval_agent._generate_tool_use = original_generate_tool_use
        codebase_retrieval_agent._format_retrieval = original_format_retrieval
        codebase_retrieval_agent.format_directory_subtree = (
            original_format_directory_subtree
        )

    def test_retrieval_result_no_queries(
        self, mock_codebase_query_rewriting_retrieval_agent
    ):
        """Test that retrieval returns a failure message if no queries are generated"""
        agent = mock_codebase_query_rewriting_retrieval_agent
        agent.get_retriever_queries = Mock()
        agent.get_retriever_queries.return_value = [], Mock(), []

        # Mock the _retrieve function
        original_retrieve = codebase_retrieval_agent._retrieve
        codebase_retrieval_agent._retrieve = Mock(
            return_value=[
                RetrievalChunk(
                    text="test chunk",
                    path="test/path",
                    char_start=0,
                    char_end=10,
                    blob_name="test_blob",
                    chunk_index=0,
                )
            ]
        )

        # Mock the _record_exchanges function to avoid protobuf issues
        original_record_exchanges = codebase_retrieval_agent._record_exchanges
        codebase_retrieval_agent._record_exchanges = Mock()

        # Actually won't be used, as we are already mocking the functions which call it
        llm_client = Mock()
        request = CodebaseRetrievalRequest(information_request="test request")
        request_context = RequestContext(
            request_id="test_id",
            request_session_id="test_session",
            request_source="test_source",
        )
        auth_info = AuthInfo(
            tenant_id="test_tenant",
            tenant_name="test_tenant_name",
            shard_namespace="test_namespace",
            cloud="test_cloud",
        )

        try:
            response = agent.run(request, llm_client, request_context, auth_info)
            assert agent.get_retriever_queries.called
            assert codebase_retrieval_agent._record_exchanges.called
            assert response.formatted_retrieval == "Retrieval failed. Please try again."
        finally:
            # Restore the original functions
            codebase_retrieval_agent._retrieve = original_retrieve
            codebase_retrieval_agent._record_exchanges = original_record_exchanges

    def test_run_with_max_output_length(
        self, mock_codebase_query_rewriting_retrieval_agent
    ):
        """Test that the agent respects the max_output_length parameter"""
        # Mock the necessary methods
        mock_codebase_query_rewriting_retrieval_agent.get_retriever_queries = Mock(
            # Return queries, exchange, and used_initial_retrievals
            return_value=([{"description": "test query"}], Mock(), [])
        )

        # Mock the _retrieve function
        original_retrieve = codebase_retrieval_agent._retrieve
        codebase_retrieval_agent._retrieve = Mock(
            return_value=[
                RetrievalChunk(
                    text="test chunk",
                    path="test/path",
                    char_start=0,
                    char_end=10,
                    blob_name="test_blob",
                    chunk_index=0,
                )
            ]
        )

        # Mock the _record_exchanges function
        original_record_exchanges = codebase_retrieval_agent._record_exchanges
        codebase_retrieval_agent._record_exchanges = Mock()

        # Mock the _format_retrieval function
        original_format_retrieval = codebase_retrieval_agent._format_retrieval
        codebase_retrieval_agent._format_retrieval = Mock(
            return_value=("formatted output", [])
        )

        # Create a mock request with max_output_length set
        custom_max_length = 5000
        request = CodebaseRetrievalRequest(
            information_request="test request", max_output_length=custom_max_length
        )
        request_context = RequestContext(
            request_id="test_id",
            request_session_id="test_session",
            request_source="test_source",
        )
        auth_info = AuthInfo(
            tenant_id="test_tenant",
            tenant_name="test_tenant_name",
            shard_namespace="test_namespace",
            cloud="test_cloud",
        )

        try:
            # Run the method
            mock_codebase_query_rewriting_retrieval_agent.run(
                request, Mock(), request_context, auth_info
            )

            # Verify that _format_retrieval was called with the correct max_chars parameter
            args, _ = codebase_retrieval_agent._format_retrieval.call_args
            assert args[1] == custom_max_length
        finally:
            # Restore the original functions
            codebase_retrieval_agent._retrieve = original_retrieve
            codebase_retrieval_agent._record_exchanges = original_record_exchanges
            codebase_retrieval_agent._format_retrieval = original_format_retrieval

    def test_run_with_dialog_messages(
        self, mock_codebase_query_rewriting_retrieval_agent
    ):
        """Test that the agent correctly processes dialog messages"""
        # Mock the necessary methods
        mock_codebase_query_rewriting_retrieval_agent.get_retriever_queries = Mock(
            # Return queries, exchange, and used_initial_retrievals
            return_value=([{"description": "test query"}], Mock(), [])
        )

        # Mock the _retrieve function
        original_retrieve = codebase_retrieval_agent._retrieve
        codebase_retrieval_agent._retrieve = Mock(
            return_value=[
                RetrievalChunk(
                    text="test chunk",
                    path="test/path",
                    char_start=0,
                    char_end=10,
                    blob_name="test_blob",
                    chunk_index=0,
                )
            ]
        )

        # Mock the _record_exchanges function
        original_record_exchanges = codebase_retrieval_agent._record_exchanges
        codebase_retrieval_agent._record_exchanges = Mock()

        # Mock the _format_retrieval function
        original_format_retrieval = codebase_retrieval_agent._format_retrieval
        codebase_retrieval_agent._format_retrieval = Mock(
            return_value=("formatted output", [])
        )

        # Create a mock request with dialog messages
        dialog_exchange = chat_pb2.Exchange(
            request_nodes=[
                chat_pb2.ChatRequestNode(
                    type=chat_pb2.ChatRequestNodeType.TEXT,
                    text_node=chat_pb2.ChatRequestText(content="previous user message"),
                )
            ],
            response_nodes=[
                chat_pb2.ChatResultNode(
                    type=chat_pb2.ChatResultNodeType.RAW_RESPONSE,
                    content="previous assistant response",
                )
            ],
            request_id="previous_request_id",
        )

        request = CodebaseRetrievalRequest(
            information_request="test request", dialog=[dialog_exchange]
        )
        request_context = RequestContext(
            request_id="test_id",
            request_session_id="test_session",
            request_source="test_source",
        )
        auth_info = AuthInfo(
            tenant_id="test_tenant",
            tenant_name="test_tenant_name",
            shard_namespace="test_namespace",
            cloud="test_cloud",
        )

        try:
            # Run the method
            response = mock_codebase_query_rewriting_retrieval_agent.run(
                request, Mock(), request_context, auth_info
            )

            # Verify that get_retriever_queries was called with dialog_messages
            _, kwargs = (
                mock_codebase_query_rewriting_retrieval_agent.get_retriever_queries.call_args
            )
            assert len(kwargs.get("dialog_messages", [])) > 0
            assert isinstance(kwargs.get("dialog_messages", [])[0], Exchange)

            # Verify the response
            assert isinstance(response, CodebaseRetrievalResponse)
            assert response.formatted_retrieval == "formatted output"
        finally:
            # Restore the original functions
            codebase_retrieval_agent._retrieve = original_retrieve
            codebase_retrieval_agent._record_exchanges = original_record_exchanges
            codebase_retrieval_agent._format_retrieval = original_format_retrieval

    def test_extract_and_interleave_chunks(self):
        """Test the _extract_and_interleave_chunks function"""
        # Create test data
        query1_chunks = [
            RetrievalChunk(
                text="chunk1 from query1",
                path="path1",
                char_start=0,
                char_end=17,
                blob_name="blob1",
                chunk_index=0,
            )
        ]

        query2_chunks = [
            RetrievalChunk(
                text="chunk1 from query2",
                path="path2",
                char_start=0,
                char_end=17,
                blob_name="blob2",
                chunk_index=0,
            ),
            RetrievalChunk(
                text="chunk2 from query2",
                path="path2",
                char_start=18,
                char_end=35,
                blob_name="blob2",
                chunk_index=1,
            ),
        ]

        query_result_list = [
            ({"description": "query1"}, query1_chunks),
            ({"description": "query2"}, query2_chunks),
        ]

        # Call the function
        result = codebase_retrieval_agent._extract_and_interleave_chunks(
            query_result_list
        )

        # Verify the result
        assert len(result) == 3
        assert result[0].text == "chunk1 from query1"
        assert result[1].text == "chunk1 from query2"
        assert result[2].text == "chunk2 from query2"


class TestSimpleRetrievalAgent:
    @pytest.fixture
    def mock_retriever(self):
        """Mock retriever for testing."""
        return Mock(spec=Retriever)

    @pytest.fixture
    def mock_ri_publisher(self):
        """Mock request insight publisher for testing."""
        return Mock(spec=request_insight_publisher.RequestInsightPublisher)

    @pytest.fixture
    def simple_retrieval_agent(self, mock_retriever, mock_ri_publisher):
        """Create a SimpleRetrievalAgent instance for testing."""
        return codebase_retrieval_agent.SimpleRetrievalAgent(
            retriever=mock_retriever,
            ri_publisher=mock_ri_publisher,
        )

    def test_constructor(self, mock_retriever, mock_ri_publisher):
        """Test that the constructor properly initializes the agent."""
        agent = codebase_retrieval_agent.SimpleRetrievalAgent(
            retriever=mock_retriever,
            ri_publisher=mock_ri_publisher,
        )

        assert agent.retriever == mock_retriever
        assert agent.ri_publisher == mock_ri_publisher
        assert agent.max_chunks == 1024

    def test_run(self, simple_retrieval_agent):
        """Test the run method with proper mocking."""
        # Mock the necessary functions
        original_retrieve = codebase_retrieval_agent._retrieve
        original_record_retrieval = codebase_retrieval_agent._record_retrieval
        original_format_retrieval = codebase_retrieval_agent._format_retrieval

        # Create mock chunks
        mock_chunks = [
            RetrievalChunk(
                text="test chunk content",
                path="test/path.py",
                char_start=0,
                char_end=18,
                blob_name="test_blob",
                chunk_index=0,
            )
        ]

        codebase_retrieval_agent._retrieve = Mock(return_value=mock_chunks)
        codebase_retrieval_agent._record_retrieval = Mock()
        codebase_retrieval_agent._format_retrieval = Mock(
            return_value=("formatted output", mock_chunks)
        )

        # Create test request
        request = CodebaseRetrievalRequest(
            information_request="test request",
            blobs=[],
        )
        request_context = RequestContext(
            request_id="test_id",
            request_session_id="test_session",
            request_source="test_source",
        )
        auth_info = AuthInfo(
            tenant_id="test_tenant",
            tenant_name="test_tenant_name",
            shard_namespace="test_namespace",
            cloud="test_cloud",
        )

        try:
            # Run the method
            response = simple_retrieval_agent.run(
                request, None, request_context, auth_info
            )

            # Verify the response
            assert isinstance(response, CodebaseRetrievalResponse)
            assert response.formatted_retrieval == "formatted output"

            # Verify that _retrieve was called with correct parameters
            codebase_retrieval_agent._retrieve.assert_called_once()
            args, kwargs = codebase_retrieval_agent._retrieve.call_args
            assert kwargs["retriever"] == simple_retrieval_agent.retriever
            assert kwargs["request_description"] == "test request"
            assert kwargs["request_path"] == ""
            assert kwargs["request_contains_string"] == ""
            assert kwargs["request_context"] == request_context
            assert kwargs["auth_info"] == auth_info
            assert kwargs["blobs"] == []
            assert kwargs["max_chunks"] == 1024

            # Verify that _record_retrieval was called
            codebase_retrieval_agent._record_retrieval.assert_called_once_with(
                ri_publisher=simple_retrieval_agent.ri_publisher,
                output_chunks=mock_chunks,
                request_context=request_context,
                auth_info=auth_info,
            )

            # Verify that _format_retrieval was called
            codebase_retrieval_agent._format_retrieval.assert_called_once()

        finally:
            # Restore the original functions
            codebase_retrieval_agent._retrieve = original_retrieve
            codebase_retrieval_agent._record_retrieval = original_record_retrieval
            codebase_retrieval_agent._format_retrieval = original_format_retrieval


class TestFormatDirectorySubtree:
    def test_directory_not_found(self):
        """Test formatting a directory that doesn't exist in the structure."""
        directory_structure = {"": {"files": [], "dirs": [], "dir_name_set": set()}}
        result, metadata = format_directory_subtree(
            directory_structure, "nonexistent", 1000, 1000
        )

        assert result == "Directory nonexistent not found."
        assert metadata["truncated"] is False

    def test_empty_directory(self):
        """Test formatting an empty directory."""
        directory_structure = {"": {"files": [], "dirs": [], "dir_name_set": set()}}
        result, metadata = format_directory_subtree(directory_structure, "", 1000, 1000)

        assert result == ""
        assert metadata["truncated"] is False

    def test_files_in_root(self):
        """Test formatting files in the root directory."""
        directory_structure = {
            "": {
                "files": [Path("file1.py"), Path("file2.py")],
                "dirs": [],
                "dir_name_set": set(),
            }
        }
        result, metadata = format_directory_subtree(directory_structure, "", 1000, 1000)

        assert result == "file1.py\nfile2.py\n"
        assert metadata["truncated"] is False

    def test_subdirectories(self):
        """Test formatting a directory with subdirectories."""
        directory_structure = {
            "": {
                "files": [],
                "dirs": [Path("dir1"), Path("dir2")],
                "dir_name_set": {"dir1", "dir2"},
            },
            "dir1": {
                "files": [Path("dir1/file1.py")],
                "dirs": [],
                "dir_name_set": set(),
            },
            "dir2": {
                "files": [Path("dir2/file2.py")],
                "dirs": [],
                "dir_name_set": set(),
            },
        }
        result, metadata = format_directory_subtree(directory_structure, "", 1000, 1000)

        assert result == "dir1\n  file1.py\ndir2\n  file2.py\n"
        assert metadata["truncated"] is False

    def test_nested_directories(self):
        """Test formatting nested directories."""
        directory_structure = {
            "": {
                "files": [],
                "dirs": [Path("dir1")],
                "dir_name_set": {"dir1"},
            },
            "dir1": {
                "files": [],
                "dirs": [Path("dir1/dir2")],
                "dir_name_set": {"dir2"},
            },
            "dir1/dir2": {
                "files": [Path("dir1/dir2/file.py")],
                "dirs": [],
                "dir_name_set": set(),
            },
        }
        result, metadata = format_directory_subtree(directory_structure, "", 1000, 1000)

        assert result == "dir1\n  dir2\n    file.py\n"
        assert metadata["truncated"] is False

    def test_directory_char_limit(self):
        """Test formatting with directory character limit."""
        # Create a deep directory structure
        directory_structure = {
            "": {
                "files": [],
                "dirs": [Path("dir1")],
                "dir_name_set": {"dir1"},
            },
            "dir1": {
                "files": [],
                "dirs": [Path("dir1/dir2")],
                "dir_name_set": {"dir2"},
            },
            "dir1/dir2": {
                "files": [],
                "dirs": [Path("dir1/dir2/dir3")],
                "dir_name_set": {"dir3"},
            },
            "dir1/dir2/dir3": {
                "files": [Path("dir1/dir2/dir3/file.py")],
                "dirs": [],
                "dir_name_set": set(),
            },
        }

        # Set a very low directory character limit
        result, metadata = format_directory_subtree(directory_structure, "", 10, 1000)

        # Should be truncated
        assert metadata["truncated"] is True
        assert result == "dir1\n  dir2\n    ...\n"

    def test_file_char_limit(self):
        """Test formatting with file character limit."""
        # Create a structure with many files
        directory_structure = {
            "": {
                "files": [Path(f"file{i}.py") for i in range(10)],
                "dirs": [],
                "dir_name_set": set(),
            },
        }

        # Set a very low file character limit
        _, metadata = format_directory_subtree(directory_structure, "", 1000, 1)

        # The implementation returns an empty string when the limit is too low,
        # but still marks it as truncated
        assert metadata["truncated"] is True

    def test_depth_limit(self):
        """Test formatting with depth limit."""
        # Create a deep directory structure
        directory_structure = {
            "": {
                "files": [],
                "dirs": [Path("dir1")],
                "dir_name_set": {"dir1"},
            },
            "dir1": {
                "files": [],
                "dirs": [Path("dir1/dir2")],
                "dir_name_set": {"dir2"},
            },
            "dir1/dir2": {
                "files": [],
                "dirs": [Path("dir1/dir2/dir3")],
                "dir_name_set": {"dir3"},
            },
            "dir1/dir2/dir3": {
                "files": [Path("dir1/dir2/dir3/file.py")],
                "dirs": [],
                "dir_name_set": set(),
            },
        }

        # Set a depth limit of 1
        result, metadata = format_directory_subtree(
            directory_structure, "", 1000, 1000, max_depth=1
        )

        # Should show dir1 but not go deeper
        assert result == "dir1\n  ...\n"
        assert metadata["truncated"] is False  # Not truncated, just limited by depth

    def test_non_root_starting_directory(self):
        """Test formatting starting from a non-root directory."""
        directory_structure = {
            "": {
                "files": [],
                "dirs": [Path("dir1")],
                "dir_name_set": {"dir1"},
            },
            "dir1": {
                "files": [Path("dir1/file1.py")],
                "dirs": [Path("dir1/dir2")],
                "dir_name_set": {"dir2"},
            },
            "dir1/dir2": {
                "files": [Path("dir1/dir2/file2.py")],
                "dirs": [],
                "dir_name_set": set(),
            },
        }

        # Start from dir1 instead of root
        result, metadata = format_directory_subtree(
            directory_structure, "dir1", 1000, 1000
        )

        # Should only show contents of dir1
        assert result == "dir2\n  file2.py\nfile1.py\n"
        assert metadata["truncated"] is False

    def test_mixed_files_and_directories(self):
        """Test formatting with a mix of files and directories at the same level."""
        directory_structure = {
            "": {
                "files": [Path("root_file1.py"), Path("root_file2.py")],
                "dirs": [Path("dir1"), Path("dir2")],
                "dir_name_set": {"dir1", "dir2"},
            },
            "dir1": {
                "files": [Path("dir1/file1.py")],
                "dirs": [],
                "dir_name_set": set(),
            },
            "dir2": {
                "files": [Path("dir2/file2.py")],
                "dirs": [],
                "dir_name_set": set(),
            },
        }

        result, metadata = format_directory_subtree(directory_structure, "", 1000, 1000)

        # Directories should be listed first, then files
        assert (
            result
            == "dir1\n  file1.py\ndir2\n  file2.py\nroot_file1.py\nroot_file2.py\n"
        )
        assert metadata["truncated"] is False

    def test_many_files_truncation(self):
        """Test formatting with many files to test truncation behavior."""
        # Create a structure with 50 files
        directory_structure = {
            "": {
                "files": [Path(f"file{i}.py") for i in range(50)],
                "dirs": [],
                "dir_name_set": set(),
            },
        }

        # Set a very low character limit to ensure truncation
        result, metadata = format_directory_subtree(directory_structure, "", 1000, 10)

        # With a very low limit, the implementation returns an empty string
        # but still marks it as truncated
        assert metadata["truncated"] is True
        assert result == ""  # Empty string with very low limit

    def test_special_characters_in_names(self):
        """Test formatting with special characters in file/directory names."""
        directory_structure = {
            "": {
                "files": [
                    Path("file-with-dashes.py"),
                    Path("file_with_underscores.py"),
                ],
                "dirs": [Path("dir.with.dots"), Path("dir with spaces")],
                "dir_name_set": {"dir.with.dots", "dir with spaces"},
            },
            "dir.with.dots": {
                "files": [Path("dir.with.dots/file.py")],
                "dirs": [],
                "dir_name_set": set(),
            },
            "dir with spaces": {
                "files": [Path("dir with spaces/file.py")],
                "dirs": [],
                "dir_name_set": set(),
            },
        }

        result, metadata = format_directory_subtree(directory_structure, "", 1000, 1000)

        # Should correctly format names with special characters
        assert "dir.with.dots" in result
        assert "dir with spaces" in result
        assert "file-with-dashes.py" in result
        assert "file_with_underscores.py" in result
        assert metadata["truncated"] is False

    def test_very_deep_structure_with_limited_depth(self):
        """Test formatting a very deep structure with limited max_depth."""
        # Create a structure with 10 levels of nesting
        directory_structure = {
            "": {
                "files": [],
                "dirs": [Path("level1")],
                "dir_name_set": {"level1"},
            }
        }

        current_path = "level1"
        for i in range(2, 11):  # Create 10 levels
            parent_path = current_path
            current_path = f"{parent_path}/level{i}"

            directory_structure[parent_path] = {
                "files": [],
                "dirs": [Path(current_path)],
                "dir_name_set": {f"level{i}"},
            }

        # Add a file at the deepest level
        directory_structure[current_path] = {
            "files": [Path(f"{current_path}/deep_file.py")],
            "dirs": [],
            "dir_name_set": set(),
        }

        # Set a max_depth of 3
        result, metadata = format_directory_subtree(
            directory_structure, "", 1000, 1000, max_depth=3
        )

        # The implementation actually shows only level1 with ellipsis when max_depth=3
        # This is because the depth is counted from 0, and the ellipsis is added at depth-1
        assert result == "level1\n  ...\n"
        assert metadata["truncated"] is False  # Not truncated, just limited by depth

local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local anthropicLib = import 'services/chat_host/server/anthropic-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local requestInsightPublisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';

function(
  env,
  namespace,
  cloud,
  namespace_config,
)
  local appName = 'agents';

  local chatanolConfig = (import 'services/deploy/chatanol-qwen-v1-1_fp8_config.jsonnet') + {
    modelConfig+: {
      max_retrieval_results: 1024,
    },
  };
  local retrievalConfigs = [
    modelDeployment.denseRetrievalConfig(chatanolConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
  ];
  // TODO: prompt formatters, or alternatively, just call a chat-svc and use one of those models
  local chatConfigs = {
    'claude-3-5-sonnet-v2': {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-5-sonnet-v2@20241022',
      temperature: 0.0,
      max_output_tokens: 8000,
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      gcp_project_id: cloudInfo[cloud].projectId,
      // anthropic_api_key_path: added later, once access method is established
    },
    'claude-3-7-sonnet': {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-7-sonnet@20250219',
      temperature: 0.0,
      max_output_tokens: 40000,
      gcp_region: 'us-east5',
      gcp_project_id: cloudInfo[cloud].projectId,
      // anthropic_api_key_path: added later, once access method is established
    },
    'claude-3-7-sonnet-asia': {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-7-sonnet@20250219',
      temperature: 0.0,
      max_output_tokens: 8000,
      gcp_region: 'asia-southeast1',
      gcp_project_id: cloudInfo[cloud].projectId,
    },
    'claude-3-7-sonnet-lb': {
      client_type: 'anthropic_load_balanced',
      model_name: 'claude-3-7-sonnet@20250219',  // Same model name as the original
      temperature: 0.0,
      max_output_tokens: 40000,
      gcp_region: 'us-east5',  // This will be used as a hint, but load balancing will be applied
      gcp_project_id: cloudInfo[cloud].projectId,
      // anthropic_api_key_path: added later, once access method is established
    },
    'claude-4-0-sonnet-direct': {
      client_type: 'anthropic_direct',
      model_name: 'claude-sonnet-4-20250514',
      temperature: 0.0,
      max_output_tokens: 40000,
      // anthropic_api_key_path: added later, once access method is established
    },
  };

  // mutual TLS is enabled if the namespace config has the forceMtls flag set
  // MTLS ensures that the client and server certificates are valid
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);
  local requestInsightPublisher = requestInsightPublisherLib(cloud, env, namespace, appName=appName);

  // client certificate so that the pod can authenticiate to grpc servers (incl. itself for health checks)
  // in the same namespace
  local clientCert = certLib.createClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );

  // client certificate so that the pod can authenticiate to grpc servers running in the central namespace (e.g. embedder)
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='central-client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );

  // server certificate for MTLS
  local serverCert = certLib.createServerCert(name='%s-server-certificate' % appName,
                                              namespace=namespace,
                                              appName=appName,
                                              dnsNames=grpcLib.grpcServiceNames(appName),
                                              volumeName='certs');

  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true
  );
  local serviceAccountObjects = serviceAccount.objects + [
    requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName),
  ];

  // For anthropic_direct, deploy a sealed secret with the API key
  // For anthropic_vertexai, grant service account permission to Vertex AI
  local anthropicDirectAccess = anthropicLib(appName=appName, namespace=namespace, cloud=cloud);
  local anthropicVertexAIAccess = {
    objects: gcpLib.grantAccess(
      env=env,
      namespace=namespace,
      appName=appName,
      name='aiplatform-%s-grant' % appName,
      resourceRef={
        kind: 'Project',
        external: 'projects/%s' % cloudInfo[cloud].projectId,
      },
      bindings=[
        {
          role: 'roles/aiplatform.user',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ],
      abandon=true,
    ),
  };

  // Use a single secret for all Google Search API keys
  // The secret contains a JSON object with multiple API keys that will be round-robin distributed
  local googleSearchKeyInfo = {
    purpose: 'google-custom-search-api-keys',
    version: {
      DEV: 'latest',
      STAGING: '1',
      PROD: '1',
    }[env],
  };

  local googleSearch = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose=googleSearchKeyInfo.purpose,
    version=googleSearchKeyInfo.version,
    serviceAccount=serviceAccount
  );
  // From https://developers.google.com/custom-search/v1/overview,
  // https://programmablesearchengine.google.com/controlpanel/all
  local googleSearchCx = {
    DEV: '62e52447fef1e4b9d',
    STAGING: '100493042e94d4e92',
    PROD: 'a6cebf36830b24bd4',
  }[env];

  local config = {
    grpc_port: 50051,
    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
    dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    auth_config: {
      token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    },
    chat_model_configs: std.mapWithKey(
      function(k, v) v + {
        anthropic_api_key_path: anthropicDirectAccess.api_key_path,
      }, chatConfigs
    ),
    default_chat_model_name: 'claude-3-7-sonnet',
    context_retrieval: { retrieval_configs: retrievalConfigs },
    content_manager_endpoint: 'content-manager-svc:50051',
    model_finder_endpoint: 'model-finder-svc:50051',
    atlassian_endpoint: 'atlassian-svc:50051',
    github_processor_endpoint: 'github-processor-svc:50051',
    linear_endpoint: 'linear-svc:50051',
    notion_endpoint: 'notion-svc:50051',
    supabase_endpoint: 'supabase-svc:50051',
    glean_endpoint: 'glean-svc:50051',
    agent_tools_config: {
      web_search_config: {
        api_key_path: googleSearch.filePath,
        cx: googleSearchCx,
      },
      github_config: {
        // use the default values for now
        github_api_base: 'https://api.github.com',
      },
    },
  } + if !mtls then {} else {
    server_mtls: serverCert.config,
    client_mtls: clientCert.config,
    central_client_mtls: centralClientCert.config,
  };
  assert config.chat_model_configs[config.default_chat_model_name] != null;

  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local services = grpcLib.grpcService(appName=appName, namespace=namespace);

  local retrievalObjects = std.map(function(r) r.getRetrievalObjects(appName, namespace), retrievalConfigs);
  local container = {
    name: appName,
    target: {
      name: '//services/agents/server:image',
      dst: 'agents-py',
    },
    ports: [
      {
        containerPort: 50051,
        name: 'grpc-svc',
      },
    ],
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
    volumeMounts: lib.flatten([
      configMap.volumeMountDef,
      dynamicFeatureFlags.volumeMountDef,
      requestInsightPublisher.volumeMountDef,
      centralClientCert.volumeMountDef,
      serverCert.volumeMountDef,
      clientCert.volumeMountDef,
      anthropicDirectAccess.volumeMountDef,
      [r.volumeMountDef for r in retrievalObjects if r.volumeMountDef != null],
      [r.innerRetrieverInfo.volumeMountDef for r in retrievalObjects if std.get(r, 'innerRetrieverInfo', null) != null],
      if googleSearch != null then googleSearch.volumeMountDef else [],
    ]),
    readinessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    livenessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    resources: {
      limits: {
        cpu: 1,
        memory: '2Gi',
      },
    },
  };
  local pod = {
    serviceAccountName: serviceAccount.name,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    containers: [
      container,
    ],
    volumes: lib.flatten([
      configMap.podVolumeDef,
      dynamicFeatureFlags.podVolumeDef,
      requestInsightPublisher.podVolumeDef,
      centralClientCert.podVolumeDef,
      serverCert.podVolumeDef,
      clientCert.podVolumeDef,
      anthropicDirectAccess.podVolumeDef,
      [r.podVolumeDef for r in retrievalObjects if r.podVolumeDef != null],
      [r.innerRetrieverInfo.podVolumeDef for r in retrievalObjects if std.get(r, 'innerRetrieverInfo', null) != null],
      if googleSearch != null then googleSearch.podVolumeDef else [],
    ]),
  };

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      // increase the progress deadline to allow more time for IAM service account setup
      progressDeadlineSeconds: if env == 'DEV' then 1800 else 300,
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };

  lib.flatten([
    configMap.objects,
    serviceAccountObjects,
    anthropicDirectAccess.objects,
    anthropicVertexAIAccess.objects,
    serverCert.objects,
    centralClientCert.objects,
    clientCert.objects,
    dynamicFeatureFlags.k8s_objects,
    deployment,
    services,
    [r.objects for r in retrievalObjects],
    [r.innerRetrieverInfo.objects for r in retrievalObjects if std.get(r, 'innerRetrieverInfo', null) != null],
    if googleSearch != null then googleSearch.objects else [],
  ])

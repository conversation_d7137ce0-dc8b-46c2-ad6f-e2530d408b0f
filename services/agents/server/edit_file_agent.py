import difflib
import random
import threading
import time
from dataclasses import dataclass
from typing import Any

import grpc
import jsonschema
import opentelemetry.trace
import structlog

import base.feature_flags
from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestText,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    Exchange,
    RequestMessage,
)
from base.python.grpc import client_options

# TODO: add general third part client errors instead of relying on an anthropic wrapper-specific error...
from base.third_party_clients.anthropic_direct_client import (
    UnavailableRpcError as AnthropicUnavailableRpcError,
)
from base.third_party_clients.third_party_model_client import (
    PromptCacheUsage,
    ThirdPartyModelClient,
    ToolChoice,
    ToolChoiceType,
)
from services.agents import agents_pb2
from services.agents.agents_pb2 import EditFileRequest, EditFileResponse
from services.agents.tool import Tool
from services.api_proxy import model_finder_pb2, model_finder_pb2_grpc
from services.chat_host import chat_proto_util
from services.deploy.model_instance import model_instance_pb2
from services.edit_host import edit_pb2
from services.edit_host.client import EditClient
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.request_insight import request_insight_pb2
from services.request_insight.publisher import request_insight_publisher

logger = structlog.get_logger()
tracer = opentelemetry.trace.get_tracer(__name__)

_SMART_PASTE_MODEL = base.feature_flags.StringFlag("smart_paste_model", "")


class EditClientProvider:
    def __init__(
        self,
        model_finder: model_finder_pb2_grpc.ModelFinderStub,
        channel_creds: grpc.ChannelCredentials | None,
    ):
        self.model_client = model_finder
        self.channel_creds = channel_creds
        self.lock = threading.Lock()
        self.clients: dict[str, FileEditClient] = {}

    def get_flag_based_client(
        self, auth_info: AuthInfo, request_context: RequestContext
    ) -> "FileEditClient":
        context = base.feature_flags.get_global_context().bind_attribute(
            "tenant_name", auth_info.tenant_name
        )
        model_name = _SMART_PASTE_MODEL.get(context)
        if not model_name:
            logger.warn(
                "Smart paste model flag is not set, will look up any edit model"
            )
        return self.get_client_for_model(model_name, request_context)

    def get_client_for_model(
        self, model_name: str, request_context: RequestContext
    ) -> "FileEditClient":
        endpoint = None
        if not model_name:
            model_name, endpoint = self.find_model_endpoint("", request_context)

        logger.info("edit model: %s", model_name)
        with self.lock:
            entry = self.clients.get(model_name)
            if entry is not None:
                return entry

        # If we knew the model_name at function entry, we now need to look up the endpoint
        if not endpoint:
            _, endpoint = self.find_model_endpoint(model_name, request_context)
        edit_host_client = self.construct_client(model_name, endpoint)
        entry = FileEditClient(edit_host_client, model_name)
        with self.lock:
            self.clients[model_name] = entry
        return entry

    def find_model_endpoint(
        self, model_name: str, request_context: RequestContext
    ) -> tuple[str, str]:
        candidates = []
        with tracer.start_as_current_span("model_finder"):
            resp = self.model_client.GetGenerationModels(
                model_finder_pb2.GetModelsRequest(),
                metadata=request_context.to_metadata(),
            )
        for model in resp.models:
            if model_name and model.name == model_name:
                if model.model_type != model_instance_pb2.ModelType.EDIT:
                    raise ValueError(f"Model {model_name} is not an edit model")
                return model.name, model.edit.edit_endpoint
            elif (
                not model_name and model.model_type == model_instance_pb2.ModelType.EDIT
            ):
                candidates.append(
                    (model.edit.model_priority, model.name, model.edit.edit_endpoint)
                )
        if model_name:
            raise ValueError(f"Model {model_name} not found")
        if not candidates:
            raise Exception("No edit models found")
        candidates.sort(reverse=True)
        _, name, endpoint = candidates[0]
        return name, endpoint

    def construct_client(self, model_name: str, endpoint: str) -> EditClient:
        with tracer.start_as_current_span("edit_client_setup"):
            options = client_options.get_grpc_client_options(
                client_options.GrpcClientOptions(
                    load_balancing="headless" in endpoint,
                )
            )

            return EditClient(
                endpoint,
                self.channel_creds,
                options,
            )


class EditFileAgent:
    def __init__(
        self,
        client_provider: EditClientProvider,
        ri_publisher: request_insight_publisher.RequestInsightPublisher,
    ):
        """Initialize the edit file agent.

        Args:
            edit_client: Client for making edits to files
            edit_model: Model to use for edits
            llm_client: Client for interacting with the LLM
        """
        self.edit_client_provider = client_provider
        self.ri_publisher = ri_publisher
        self.max_turns = 3

    def _get_llm_response(
        self,
        llm_client: ThirdPartyModelClient,
        history: list[Exchange],
        instruction: RequestMessage,
        tool_params: list,
        request_context: RequestContext,
        auth_info: AuthInfo,
        tool_choice: ToolChoice | None = None,
    ) -> list[ChatResultNode]:
        """Get a response from the LLM.

        Args:
            instruction: Instruction for the LLM
            tool_params: Parameters for the tools
            tool_choice: Choice of tool to use

        Returns:
            List of ChatResultNode objects representing the response
        """
        # TODO(markus): move retry logic for anthropic into a central place; make it configurable via third_party_model_client interface.
        TRIES = 3
        for attempt in range(1, TRIES + 1):
            try:
                with tracer.start_as_current_span("llm_generation"):
                    return self._try_get_llm_response(
                        llm_client=llm_client,
                        history=history,
                        instruction=instruction,
                        tool_params=tool_params,
                        tool_choice=tool_choice,
                        request_context=request_context,
                        auth_info=auth_info,
                    )
            except AnthropicUnavailableRpcError as e:
                # The other reasons do not really justify retrying
                retry = "overloaded" in e.message.lower() or "Rate limit" in e.message
                logger.warn(
                    "Anthropic unavailable error in agent (attempt %d/%d): %s",
                    attempt,
                    TRIES,
                    e.message,
                )
                if retry and attempt < TRIES:
                    time.sleep(5 * random.uniform(0.8, 1.2))
                    continue
                raise
        assert False, "Unreachable"

    def _try_get_llm_response(
        self,
        llm_client: ThirdPartyModelClient,
        history: list[Exchange],
        instruction: RequestMessage,
        tool_params: list,
        request_context: RequestContext,
        auth_info: AuthInfo,
        tool_choice: ToolChoice | None = None,
    ) -> list[ChatResultNode]:
        response_nodes: list[ChatResultNode] = []
        response_stream = llm_client.generate_response_stream(
            model_caller="agent-edit-file",
            chat_history=history,
            cur_message=instruction,
            tool_definitions=tool_params,
            tool_choice=tool_choice,
            use_caching=True,
        )

        # Streaming call generates many short sequences of text; for our purposes,
        # we greatly prefer these be concatenated into larger nodes.
        text_buffer = []

        def add_text_node():
            if not text_buffer:
                return
            response_nodes.append(
                ChatResultNode(
                    type=ChatResultNodeType.RAW_RESPONSE,
                    content="".join(text_buffer),
                    tool_use=None,
                    id=len(response_nodes),
                )
            )
            text_buffer.clear()

        for response in response_stream:
            if response.tool_use is not None:
                add_text_node()
                response_nodes.append(
                    ChatResultNode(
                        type=ChatResultNodeType.TOOL_USE,
                        content="",
                        tool_use=ChatResultToolUse(
                            name=response.tool_use.tool_name,
                            input=response.tool_use.input,
                            tool_use_id=response.tool_use.tool_use_id,
                        ),
                        id=len(response_nodes),
                    )
                )
            elif response.text:
                text_buffer.append(response.text)
            if response.prompt_cache_usage:
                self.record_prompt_cache_usage(
                    response.prompt_cache_usage, request_context, auth_info
                )

        add_text_node()
        return response_nodes

    def run(
        self,
        edit_request: EditFileRequest,
        llm_client: ThirdPartyModelClient,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> EditFileResponse:
        """Run the file edit operation.

        Args:
            edit_request: The edit request containing file path and edit details
                message EditFileRequest {
                    string file_path = 1;
                    string edit_summary = 2;
                    string detailed_edit_description = 3;
                    string file_contents = 4;
                }
            request_context: Context for the current request
            auth_info: Authentication information

        Returns:
            EditFileResponse indicating success or failure
        """
        edit_tool = EditFileToolV2(
            self.edit_client_provider.get_flag_based_client(auth_info, request_context)
        )

        complete_tool = CompleteTool()

        tools = [edit_tool, complete_tool]
        tool_params = [tool.get_tool_definition() for tool in tools]

        path = edit_request.file_path
        target_file_content = edit_request.file_contents

        edit_instruction = f"""\
Edit the file {path} according to the following instructions:

{edit_request.edit_summary}

{edit_request.detailed_edit_description}

Call the supplied tool to perform the edit. Make sure you specify both a description
and a precise listing of the new code. The new code should include every piece of
code that needs to be changed, even if it spans multiple locations in the file.
You can use placeholder comments to separate the different sections.

Here is the full contents of the file. The remainder of this message contains the contents.

{target_file_content}
"""

        review_instruction_template = """\
Please review the changes made. Make sure that:
- The instructions were followed correctly
- No important related changes were missed
- No changes were made that weren't necessary from the instructions

Here is the diff showing the changes made:

{diff}

If the changes are satisfactory, call the complete tool to end the task.
If not, decide which edits are needed now, and call the edit tool again."""

        exchanges: list[Exchange] = []

        response_nodes: list[ChatResultNode] = self._get_llm_response(
            llm_client=llm_client,
            history=exchanges,
            instruction=edit_instruction,
            tool_params=tool_params,
            request_context=request_context,
            auth_info=auth_info,
            tool_choice=ToolChoice(type=ToolChoiceType.TOOL, name=edit_tool.name),
        )

        exchanges.append(
            Exchange(request_message=edit_instruction, response_text=response_nodes)
        )

        edit_response = None

        # A "turn" is a round of (Generate, Edit, Review)
        # We must check the stop condition in the middle of the loop to
        # avoid unnecessary edit or LLM generation calls.
        remaining_turns = self.max_turns
        while True:
            assert isinstance(exchanges[-1].response_text, list)
            pending_tool_calls: list[ChatResultNode] = list(
                filter(
                    lambda x: x.type == ChatResultNodeType.TOOL_USE,
                    exchanges[-1].response_text,
                )
            )

            tool_results: list[ChatRequestToolResult] = []
            for tool_call in pending_tool_calls:
                assert tool_call.tool_use is not None
                if tool_call.tool_use.name == edit_tool.name:
                    if remaining_turns <= 0:
                        # In this condition we're happy to run the complete tool,
                        # but not the edit tool
                        break

                    try:
                        edit_response = edit_tool.run(
                            tool_call.tool_use.input,
                            EditContext(path, target_file_content),
                            request_context,
                        )
                        tool_results.append(
                            ChatRequestToolResult(
                                tool_use_id=tool_call.tool_use.tool_use_id,
                                content=f"File {path} edited",
                                is_error=False,
                            )
                        )
                    except Exception:
                        # Do not log the exception, as it may include sensitive customer
                        # information
                        self.record_dialog(exchanges, request_context, auth_info)
                        return EditFileResponse(
                            modified_file_contents="Failed to edit file", is_error=True
                        )
                elif tool_call.tool_use.name == complete_tool.name:
                    complete_tool.run(tool_call.tool_use.input, None, request_context)

            assert isinstance(edit_response, str)
            if complete_tool.should_stop or remaining_turns <= 0:
                break
            remaining_turns -= 1

            # Review stage
            # Generate diff between the current file contents and the edit response
            diff = "\n".join(
                difflib.unified_diff(
                    target_file_content.splitlines(),
                    edit_response.splitlines(),
                    fromfile=str(path),
                    tofile=str(path),
                    lineterm="",
                )
            )

            # Since we changed the file's contents, update the target_file_content for the next iteration
            target_file_content = edit_response

            review_instruction = review_instruction_template.format(diff=diff)
            user_message = [
                ChatRequestNode(
                    id=0,
                    type=ChatRequestNodeType.TOOL_RESULT,
                    text_node=None,
                    tool_result_node=result,
                )
                for result in tool_results
            ] + [
                ChatRequestNode(
                    id=0,
                    type=ChatRequestNodeType.TEXT,
                    text_node=ChatRequestText(content=review_instruction),
                    tool_result_node=None,
                )
            ]

            response_nodes: list[ChatResultNode] = self._get_llm_response(
                llm_client=llm_client,
                history=exchanges,
                instruction=user_message,
                tool_params=tool_params,
                request_context=request_context,
                auth_info=auth_info,
            )

            exchanges.append(
                Exchange(
                    request_message=user_message,
                    response_text=response_nodes,
                )
            )

        self.record_dialog(exchanges, request_context, auth_info)
        if complete_tool.should_stop:
            return EditFileResponse(
                modified_file_contents=edit_response, is_error=False
            )
        else:
            # If we reach this point, we have exceeded the maximum number of turns and the task is not complete
            return EditFileResponse(
                modified_file_contents=edit_response or target_file_content,
                is_error=True,
            )

    def record_dialog(
        self,
        exchanges: list[Exchange],
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        if not exchanges:
            return
        conv = list(map(chat_proto_util.exchange_to_proto, exchanges))
        event = request_insight_publisher.new_event()
        event.sub_agent_dialog.MergeFrom(
            request_insight_pb2.SubAgentDialog(dialog=conv)
        )
        self.ri_publisher.publish_request_insight(
            self.ri_publisher.update_request_info_request(
                request_context.request_id, [event], auth_info
            )
        )

    def record_prompt_cache_usage(
        self,
        prompt_cache_usage: PromptCacheUsage,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        event = request_insight_publisher.new_event()
        event.prompt_cache_usage.MergeFrom(
            request_insight_pb2.PromptCacheUsage(
                input_tokens=prompt_cache_usage.input_tokens,
                cache_read_input_tokens=prompt_cache_usage.cache_read_input_tokens,
                cache_creation_input_tokens=prompt_cache_usage.cache_creation_input_tokens,
            )
        )
        self.ri_publisher.publish_request_insight(
            self.ri_publisher.update_request_info_request(
                request_context.request_id, [event], auth_info
            )
        )


class CompleteTool(Tool):
    name = "complete"
    """The model should call this tool when it is done with the task."""

    description = "Call this tool when you are done with the task, and supply your answer or summary."
    input_schema = {
        "type": "object",
        "properties": {
            "answer": {
                "type": "string",
                "description": "The answer to the question, or final summary of actions taken to accomplish the task.",
            },
        },
        "required": ["answer"],
    }

    def __init__(self):
        super().__init__()
        self.answer: str = ""

    @property
    def should_stop(self):
        return self.answer != ""

    def reset(self):
        self.answer = ""

    def run(
        self,
        tool_input: dict[str, Any],
        extra_tool_input: Any,
        request_context: RequestContext,
    ) -> str:
        self.answer = tool_input["answer"]
        return "Task completed"

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        return agents_pb2.ToolAvailabilityStatus.AVAILABLE


class FileEditClient:
    """A client for the Forger smart paste model."""

    def __init__(
        self,
        edit_host_client: EditClient,
        model_name: str,
    ):
        self.edit_host_client = edit_host_client
        self.model_name = model_name

    def edit_file(
        self,
        target_file_path: str,
        target_file_content: str,
        edit_plan: str,
        code_block: str,
        request_context: RequestContext,
    ) -> str:
        """
        Edits a target file according to the edit plan and generated code block.

        Args:
            target_file_path: The path to the file to edit.
            target_file_content: The content of the file to edit.
            edit_plan: A language description of the edit to make.
            code_block: The new code. May contain placeholders like '... existing methods here ...'.

        Returns:
            edited_content
        """
        max_retries = 3
        retry_delays = [1, 5, 20]  # in seconds

        for retry_attempt in range(max_retries):
            try:
                return self.edit_file_unsafe(
                    target_file_path=target_file_path,
                    target_file_content=target_file_content,
                    edit_plan=edit_plan,
                    code_block=code_block,
                    request_context=request_context,
                )
            except Exception as e:
                logger.error(
                    "Edit request failed attempt %d/%d (%s)",
                    retry_attempt + 1,
                    max_retries,
                    type(e).__name__,
                )
                if retry_attempt == max_retries - 1:
                    raise e
                time.sleep(retry_delays[retry_attempt])

        assert False, "Never should get here."

    def edit_file_unsafe(
        self,
        target_file_path: str,
        target_file_content: str,
        edit_plan: str,
        code_block: str,
        request_context: RequestContext,
    ) -> str:
        # The Forger model expects 4 fields:
        #   chat_history.request_message : the instruction / edit description
        #   code_block : the generated code (with the placeholders etc.)
        #   target_file_path : the path of the file to edit
        #   target_file_content : the full content of that file
        #
        # It optionally accepts selected_text, prefix, and suffix.
        # It ignores additional chat history.

        chat_history = [
            edit_pb2.Exchange(
                request_message=edit_plan,
                response_text="",
            )
        ]

        req = edit_pb2.InstructionRequest(
            model_name=self.model_name,
            code_block=code_block,
            chat_history=chat_history,
            target_file_path=target_file_path,
            target_file_content=target_file_content,
        )
        instruction_response = self.edit_host_client.instruction(req, request_context)
        replacement_start_line, replacement_end_line = None, None
        response = []
        for replacement in instruction_response.replace_text:
            if replacement.start_line is not None:
                # API returns 1-based line numbers
                replacement_start_line = replacement.start_line - 1
            if replacement.end_line is not None:
                # API returns 1-based line numbers
                replacement_end_line = replacement.end_line - 1
            if replacement.text is not None:
                response.append(replacement.text)

        target_file_lines = target_file_content.splitlines(True)
        target_file_lines[replacement_start_line:replacement_end_line] = response
        return "".join(target_file_lines)


@dataclass
class EditContext:
    """This agent doesn't have access to user's workspace, so can only work on
    a single file. This class represents the path and up-to-date contents of that
    file when invoking the tool."""

    file_path: str
    file_contents: str


class EditFileToolV2(Tool):
    name = "edit_file_v2"
    """A tool that edits a file.

    Uses Forger, our smart paste model.
    """

    description = "Edit the file. Accepts a description of where/what to edit, and the specific code changes to make."
    input_schema = {
        "type": "object",
        "properties": {
            "edit_plan": {
                "type": "string",
                "description": "Description of where in the file to make changes and what needs to be modified.",
            },
            "suggested_edit": {
                "type": "string",
                "description": """\
The suggested changes to the file including any new code that needs to be inserted as well as parts of the surrounding old code to maximize readability. Be concise - include only the necessary context. Placeholders are allowed with comments, for example:
# ... existing methods here ...
// ... (rest of the method omitted) ...

If you remove parts of the old code, highlight that with additional comments:
# ... method foobar() has been removed ...

This suggested change must be easy to read for a human.""",
            },
        },
        "required": ["edit_plan", "suggested_edit"],
    }

    def __init__(
        self,
        file_edit_client: FileEditClient,
    ):
        super().__init__()
        self.file_edit_client = file_edit_client

    def run(
        self,
        tool_input: dict[str, Any],
        extra_tool_input: EditContext,
        request_context: RequestContext,
    ) -> str:
        try:
            jsonschema.validate(tool_input, self.input_schema)
        except jsonschema.ValidationError:
            logger.error(
                f"EditFileAgent: tool use {self.name} failed input schema validation"
            )
            raise

        return self.file_edit_client.edit_file(
            target_file_path=extra_tool_input.file_path,
            target_file_content=extra_tool_input.file_contents,
            edit_plan=tool_input["edit_plan"],
            code_block=tool_input["suggested_edit"],
            request_context=request_context,
        )

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        return agents_pb2.ToolAvailabilityStatus.AVAILABLE

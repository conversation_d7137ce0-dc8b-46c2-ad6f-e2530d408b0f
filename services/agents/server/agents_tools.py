"""Tools used by the agent."""

from dataclasses import dataclass

import structlog
from dataclasses_json import dataclass_json
import grpc
from pydantic import SecretStr

from base.third_party_clients.third_party_model_client import ThirdPartyModelClient
from services.agents import agents_pb2
from services.integrations.linear.client.client import LinearClient
from services.integrations.notion.client.client import NotionClient
from services.integrations.atlassian.client.client import AtlassianClient
from services.integrations.glean.client.client import GleanClient
from services.integrations.github.processor.client.client import GithubProcessorClient
from services.integrations.supabase.client.client import Supabase<PERSON>lient
from services.integrations.supabase.agent_tools.supabase_tool import SupabaseTool
from services.agents.tool import BaseExtraToolInput, EmptyExtraToolInput, Tool
from services.integrations.glean.agent_tools.glean_tools import GleanApiTool
from services.integrations.atlassian.agent_tools.jira_tools import (
    <PERSON>ra<PERSON><PERSON>ueTool,
    <PERSON>raProjectTool,
    <PERSON>raSearchTool,
)
from services.integrations.atlassian.agent_tools.atlassian_tools import (
    <PERSON>ra<PERSON><PERSON>,
    ConfluenceTool,
)
from services.integrations.google_search.agent_tools.web_search import WebSearchConfig
from services.integrations.github.agent_tools.github_api_tool import (
    GitHubAPITool,
    GitHubConfig,
)
from services.integrations.google_search.agent_tools.web_search_tool import (
    WebSearchTool,
)
from services.integrations.linear.agent_tools.linear_tools import (
    LinearExtraToolInput,
    LinearTool,
    LinearSearchIssuesTool,
)
from services.integrations.atlassian.config import (
    AtlassianExtraToolInput,
)
from services.integrations.notion.agent_tools.notion_tools import (
    NotionExtraToolInput,
    NotionPageTool,
    NotionSearchTool,
    NotionTool,
)
from services.integrations.github.agent_tools.config import GitHubExtraToolInput
from services.integrations.atlassian.agent_tools.confluence_tools import (
    ConfluenceSearchTool,
    ConfluenceContentTool,
    ConfluenceSpaceTool,
)

log = structlog.get_logger()


class AgentToolRpcError(grpc.RpcError):
    """Exception thrown during agent tool processing."""

    def __init__(self, status_code: grpc.StatusCode, msg: str = ""):
        self.status_code = status_code
        self.msg = msg

    def code(self) -> grpc.StatusCode:
        return self.status_code

    def details(self) -> str:
        return self.msg

    def __str__(self) -> str:
        return (
            f"{self.__class__.__name__}(status_code={self.status_code}, msg={self.msg})"
        )


@dataclass_json
@dataclass
class AgentToolsConfig:
    """Configuration for tools used by this agent."""

    # the configuration for the web search tool
    web_search_config: WebSearchConfig | None = None
    github_config: GitHubConfig | None = None


def create_tools(
    config: AgentToolsConfig,
    *,
    llm_client: ThirdPartyModelClient | None = None,
    linear_client: LinearClient,
    notion_client: NotionClient,
    atlassian_client: AtlassianClient,
    github_processor_client: GithubProcessorClient,
    glean_client: GleanClient,
    supabase_client: SupabaseClient,
) -> list[Tool]:
    """Create the tools used by this agent.

    Args:
        config: The configuration for the agent tools.
        llm_client: LLM client to be used by tools as needed.
        linear_client: Client for connecting to the Linear service.
        notion_client: Client for connecting to the Notion service.
        atlassian_client: Client for connecting to the Atlassian service.
        github_processor_client: Client for connecting to the GitHub service.
        supabase_client: Client for connecting to the Supabase service.
    """
    tools = []
    if config.web_search_config is not None:
        tools.append(WebSearchTool(config.web_search_config))

    tools.extend(
        [
            # New Atlassian Tools
            JiraTool(atlassian_client),
            ConfluenceTool(atlassian_client),
            # New Notion Tools
            NotionTool(notion_client),
            # Supabase tools
            SupabaseTool(supabase_client),
            # Old Atlassian Tools - keeping for backwards compatibility
            JiraSearchTool(atlassian_client),
            JiraIssueTool(atlassian_client),
            JiraProjectTool(atlassian_client),
            ConfluenceSearchTool(atlassian_client),
            ConfluenceContentTool(atlassian_client),
            ConfluenceSpaceTool(atlassian_client),
            # Old Notion Tools -- keeping for backwards compatability
            NotionPageTool(notion_client),
            NotionSearchTool(notion_client),
            # Glean tools
            GleanApiTool(glean_client),
        ]
    )

    if llm_client is not None:
        # New Tool
        tools.append(LinearTool(llm_client, linear_client))
        # Old tool - keeping for backwards compatibility
        tools.append(LinearSearchIssuesTool(llm_client, linear_client))
    else:
        log.warning("LLM client not provided. Linear tools will not be available.")

    if config.github_config is not None:
        tools.append(GitHubAPITool(config.github_config, github_processor_client))

    log.info(f"Created {len(tools)} tools: {tools}")
    return tools


def create_tool_extra_input(
    extra_tool_input: agents_pb2.RunRemoteToolRequest,
) -> BaseExtraToolInput:
    """Create the extra input for a tool from the proto message."""
    match extra_tool_input.WhichOneof("extra_tool_input"):
        case "atlassian_tool_extra_input":
            return AtlassianExtraToolInput.from_fields(
                server_url=extra_tool_input.atlassian_tool_extra_input.server_url,
                personal_api_token=extra_tool_input.atlassian_tool_extra_input.personal_api_token,
                username=extra_tool_input.atlassian_tool_extra_input.username,
            )
        case "linear_tool_extra_input":
            return LinearExtraToolInput(
                api_token=SecretStr(extra_tool_input.linear_tool_extra_input.api_token),
            )
        case "notion_tool_extra_input":
            return NotionExtraToolInput.from_fields(
                api_token=extra_tool_input.notion_tool_extra_input.api_token,
            )
        case "github_tool_extra_input":
            return GitHubExtraToolInput.from_fields(
                api_token=extra_tool_input.github_tool_extra_input.api_token,
            )
        case "supabase_tool_extra_input":
            # Supabase tool doesn't use extra input
            return EmptyExtraToolInput()
        case None:
            return EmptyExtraToolInput()
        case other:
            # This should never happen. It means that the proto message has been updated
            # but this function has not been updated to handle the new type. However,
            # we still want to raise a more graceful error.
            log.error(f"Unhandled extra tool input type: {other}")
            raise AgentToolRpcError(
                status_code=grpc.StatusCode.INVALID_ARGUMENT,
                msg=f"Unhandled extra tool input type: {other}",
            )


# Agents service

Service providing generation and backend tool implementations for agents

# Current goals

- Priority is enabling interactive agent <PERSON><PERSON><PERSON> prototype to be delivered
  externally
- Top-level agent runs externally, making requests to this service as needed.
  Tools provided by client, and tool uses returned to client. Tool implementations
  may call back into this service.
- Support single generation model + retriever model
- Assume existence of particular retriever and edit service in the namespace

# Potential future goals

- Move more of the agent's "conversation" logic into the service, with the client
  only concerned with displaying dialog, user input, and client-local tool execution
  - As a result, less open LLMGenerate API; e.g. system prompt not provided by client
- IDE integration may not rely on this service's LLMGenerate api, only its tool
  implementations, if we use Chat for generation
- Server-only tools: added to model calls by the backend. Any tool use is persisted
  in services, with a handle returned to the client. When handle is passed in
  later requests, the tool use can be recovered, and tool use results added back
  into the dialog as needed.
- Support for multiple generation models / retriever models
- Automatic detection / selection of edit service(s) in the namespace
- Addition of location retrieval

load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library", "py_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

py_library(
    name = "tool",
    srcs = [
        "tool.py",
    ],
    visibility = [
        "//services/agents/server:__subpackages__",
        "//services/integrations:__subpackages__",
    ],
    deps = [
        ":agents_py_proto",
        "//base/third_party_clients:third_party_model_client",
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/request_context:request_context_py",
    ],
)

proto_library(
    name = "agents_proto",
    srcs = ["agents.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_proto",
        "//services/chat_host:chat_proto",
        "@googleapis//google/rpc:status_proto",
    ],
)

py_grpc_library(
    name = "agents_py_proto",
    protos = [":agents_proto"],
    visibility = [
        "//base/datasets:__subpackages__",
        "//services:__subpackages__",
    ],
    deps = [
        "//services/chat_host:chat_proto_py_proto",
        "//third_party/proto:googleapis_status_py_proto",
    ],
)

go_grpc_library(
    name = "agents_go_proto",
    importpath = "github.com/augmentcode/augment/services/agents/proto",
    proto = ":agents_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "//services/chat_host:chat_host_go_proto",
        "//services/lib/proto:chat_go_proto",
        "@org_golang_google_genproto_googleapis_rpc//status:go_default_library",
    ],
)

ts_proto_library(
    name = "agents_ts_proto",
    data = [
        "//base/blob_names:blob_names_ts_proto",
        "//services/chat_host:chat_host_ts_proto",
    ],
    node_modules = "//:node_modules",
    proto = ":agents_proto",
    visibility = ["//services:__subpackages__"],
)

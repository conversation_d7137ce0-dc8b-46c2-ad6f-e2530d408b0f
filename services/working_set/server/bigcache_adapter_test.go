package main

import (
	"testing"
	"time"

	blob_names "github.com/augmentcode/augment/base/blob_names"
	"github.com/augmentcode/augment/base/go/blobset_cache"
	"github.com/stretchr/testify/assert"
)

// Test basic cache setup and get/set operations
func TestBigCacheAdapter(t *testing.T) {
	cache, err := NewBigCacheAdapter( /*ttl*/ 10*time.Minute /*maxSizeInMB*/, 1 /*cleanWindow*/, 1*time.Second, nil)
	assert.NoError(t, err)
	assert.NotNil(t, cache)

	// Initial allocation should be > 0
	assert.Greater(t, cache.MemorySizeInBytes(), 0)

	tenantID := "tenant1"
	setID := "set1"
	key := blobset_cache.BlobSetKey{TenantID: tenantID, SetID: setID}

	// Create proper blob names with 32-byte SHA-256 hashes
	blobNames := []blob_names.BlobName{
		blob_names.GetBlobName("path/to/file1.txt", []byte("content of file 1")),
		blob_names.GetBlobName("path/to/file2.txt", []byte("content of file 2")),
		blob_names.GetBlobName("path/to/file3.txt", []byte("content of file 3")),
	}

	err = cache.SetBlobSet(key, blobNames)
	assert.NoError(t, err)

	retrievedBlobNames, err := cache.GetBlobSet(key)
	assert.NoError(t, err)
	assert.Equal(t, len(blobNames), len(retrievedBlobNames))

	for i, blobName := range blobNames {
		assert.Equal(t, string(blobName), string(retrievedBlobNames[i]))
	}

	assert.Equal(t, 1, cache.NumEntries())

	err = cache.DeleteBlobSet(key)
	assert.NoError(t, err)
	_, err = cache.GetBlobSet(key)
	assert.Error(t, err, "Expected error after deleting the blob set")
	assert.ErrorIs(t, err, blobset_cache.ErrEntryNotFound, "Expected ErrEntryNotFound error")
	assert.Equal(t, 0, cache.NumEntries(), "Cache should be empty after deletion")

	// Test deleting a non-existent key (should not cause errors)
	nonExistentKey := blobset_cache.BlobSetKey{TenantID: "nonexistent", SetID: "nonexistent"}
	err = cache.DeleteBlobSet(nonExistentKey)
	assert.NoError(t, err, "DeleteBlobSet should not return an error for non-existent keys")
	_, err = cache.GetBlobSet(nonExistentKey)
	assert.Error(t, err)
	assert.ErrorIs(t, err, blobset_cache.ErrEntryNotFound, "Expected ErrEntryNotFound error")
}

// Test serialization and deserialization of BlobSetKey
func TestSerializeBlobSetKey(t *testing.T) {
	key := blobset_cache.BlobSetKey{TenantID: "tenant1", SetID: "set1"}
	serialized := serializeBlobSetKey(key)

	assert.Equal(t, "tenant1::set1", serialized)

	deserialized, err := deserializeBlobSetKey(serialized)
	assert.NoError(t, err)
	assert.Equal(t, key.TenantID, deserialized.TenantID)
	assert.Equal(t, key.SetID, deserialized.SetID)

	_, err = deserializeBlobSetKey("invalid-format")
	assert.Error(t, err)
}

// Test serialization and deserialization of BlobNames
func TestSerializeDeserializeBlobNames(t *testing.T) {
	blobName1 := blob_names.GetBlobName("path/to/file1.txt", []byte("content of file 1"))
	blobName2 := blob_names.GetBlobName("path/to/file2.txt", []byte("content of file 2"))

	blobNames := []blob_names.BlobName{blobName1, blobName2}

	serialized, err := serializeBlobNames(blobNames)
	assert.NoError(t, err)
	assert.NotNil(t, serialized)

	// Each blob name is a 32-byte SHA-256 hash
	assert.Equal(t, len(blobNames)*32, len(serialized))

	deserialized, err := deserializeBlobNames(serialized)
	assert.NoError(t, err)
	assert.Equal(t, len(blobNames), len(deserialized))

	for i, blobName := range blobNames {
		assert.Equal(t, string(blobName), string(deserialized[i]))
	}

	_, err = deserializeBlobNames([]byte{})
	assert.Error(t, err)

	_, err = deserializeBlobNames([]byte{0x01, 0x02, 0x03})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not a multiple of 32")
}

// Test the eviction callback setup and invocation
func TestEvictionCallback(t *testing.T) {
	var evictedKey blobset_cache.BlobSetKey
	var evictedBlobNames []blob_names.BlobName
	evictionCalled := false

	onEvict := func(key blobset_cache.BlobSetKey, blobNames []blob_names.BlobName) {
		evictedKey = key
		evictedBlobNames = blobNames
		evictionCalled = true
	}

	cache, err := NewBigCacheAdapter( /*ttl*/ 10*time.Millisecond /*maxSizeInMB*/, 1 /*cleanWindow*/, 1*time.Second, onEvict)
	assert.NoError(t, err)
	assert.NotNil(t, cache)

	tenantID := "tenant1"
	setID := "set1"
	key := blobset_cache.BlobSetKey{TenantID: tenantID, SetID: setID}

	// Create proper blob names with 32-byte SHA-256 hashes
	blobNames := []blob_names.BlobName{
		blob_names.GetBlobName("path/to/file1.txt", []byte("content of file 1")),
		blob_names.GetBlobName("path/to/file2.txt", []byte("content of file 2")),
	}

	err = cache.SetBlobSet(key, blobNames)
	assert.NoError(t, err)

	retrievedBlobNames, err := cache.GetBlobSet(key)
	assert.NoError(t, err)
	assert.Equal(t, len(blobNames), len(retrievedBlobNames))

	// Wait for the TTL to expire and the cleanup to run
	time.Sleep(2000 * time.Millisecond)

	// Try to get the data again, it should be evicted
	_, err = cache.GetBlobSet(key)
	assert.Error(t, err)

	// Verify the eviction callback was called with the correct data
	assert.True(t, evictionCalled, "Eviction callback should have been called")
	assert.Equal(t, tenantID, evictedKey.TenantID)
	assert.Equal(t, setID, evictedKey.SetID)
	assert.Equal(t, len(blobNames), len(evictedBlobNames))
	for i, blobName := range blobNames {
		assert.Equal(t, string(blobName), string(evictedBlobNames[i]))
	}
}

// Test cache stats
func TestCacheStats(t *testing.T) {
	cache, err := NewBigCacheAdapter( /*ttl*/ 10*time.Minute /*maxSizeInMB*/, 1 /*cleanWindow*/, 1*time.Second, nil)
	assert.NoError(t, err)
	assert.NotNil(t, cache)

	key := blobset_cache.BlobSetKey{TenantID: "tenant1", SetID: "set1"}
	blobNames := []blob_names.BlobName{
		blob_names.GetBlobName("path/to/file1.txt", []byte("content of file 1")),
	}

	// Initial stats should be zero
	stats := cache.Stats()
	assert.Equal(t, int64(0), stats.Hits)
	assert.Equal(t, int64(0), stats.Misses)

	// Set a value
	err = cache.SetBlobSet(key, blobNames)
	assert.NoError(t, err)

	// Get the value (should increment hits)
	_, err = cache.GetBlobSet(key)
	assert.NoError(t, err)

	// Get a non-existent value (should increment misses)
	nonExistentKey := blobset_cache.BlobSetKey{TenantID: "nonexistent", SetID: "nonexistent"}
	_, err = cache.GetBlobSet(nonExistentKey)
	assert.Error(t, err)

	// Check stats
	stats = cache.Stats()
	assert.Equal(t, int64(1), stats.Hits)
	assert.Equal(t, int64(1), stats.Misses)
}

// Test closing the cache
func TestCacheClose(t *testing.T) {
	cache, err := NewBigCacheAdapter( /*ttl*/ 10*time.Minute /*maxSizeInMB*/, 1 /*cleanWindow*/, 1*time.Second, nil)
	assert.NoError(t, err)
	assert.NotNil(t, cache)

	// This should not panic
	cache.Close()
}

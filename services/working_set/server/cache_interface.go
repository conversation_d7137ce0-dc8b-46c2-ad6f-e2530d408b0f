package main

import (
	blob_names "github.com/augmentcode/augment/base/blob_names"
	"github.com/augmentcode/augment/base/go/blobset_cache"
)

// CacheInterface defines the interface for a cache that stores sets of blob names
// This allows us to have multiple implementations (e.g. bigcache-backed or blobset_cache)
// that can be selected via feature flag
type CacheInterface interface {
	// GetBlobSet retrieves blob names from the cache by key
	GetBlobSet(key blobset_cache.BlobSetKey) ([]blob_names.BlobName, error)

	// SetBlobSet stores blob names in the cache
	SetBlobSet(key blobset_cache.BlobSetKey, blobNames []blob_names.BlobName) error

	// DeleteBlobSet removes a blob set from the cache by its key
	DeleteBlobSet(key blobset_cache.BlobSetKey) error

	// NumEntries returns the number of blob sets in the cache
	NumEntries() int

	// MemorySizeInBytes returns the amount of memory currently being used by the cache in bytes
	MemorySizeInBytes() int

	// Stats returns cache statistics
	Stats() blobset_cache.CacheStats

	// Close stops any background goroutines
	Close()
}

package main

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/allegro/bigcache/v3"
	blob_names "github.com/augmentcode/augment/base/blob_names"
	"github.com/augmentcode/augment/base/go/blobset_cache"
)

// BigCacheAdapter adapts bigcache to the CacheInterface
type BigCacheAdapter struct {
	cache *bigcache.BigCache
}

// NewBigCacheAdapter creates a new adapter for bigcache
func NewBigCacheAdapter(ttl time.Duration, maxSizeInMB int, cleanupInterval time.Duration, onEvict blobset_cache.EvictionCallback) (CacheInterface, error) {
	config := bigcache.Config{
		// We use a single shard since cache operations are behind locks in WorkingSetStore.
		Shards: 1,
		// TTL of an entry before it's evicted due to inactivity.
		LifeWindow: ttl,
		// How often the cleanup routine runs.
		CleanWindow: cleanupInterval,
		// Number of entries to preallocate for each shard - since we expect potentially large
		// checkpoints/indexes we will only preallocate 10 entries per cache/shard and allow for
		// growth via reallocation.
		MaxEntriesInWindow: 10,
		// This is only used for determining the initial memory allocation size - the ballpark
		// calculation here is 500,000 blobs per checkpoint/index and 32 bytes per blob = 16MB
		MaxEntrySize: 16 * 1024 * 1024,
		// Turn on memory allocation logs
		Verbose: true,
		// Sets the maximum size of the whole cache (all shards) in MB. If this value is reached
		// oldest (LRU) entries will be evicted to make room for new entries.
		HardMaxCacheSize: maxSizeInMB,
		// We don't need the OnRemove callback because we're using the OnRemoveWithReason callback
		// below.
		OnRemove: nil,
		// We will set the OnRemoveWithReason callback below if onEvict is provided.
		OnRemoveWithReason: nil,
	}

	if onEvict != nil {
		config.OnRemoveWithReason = func(keyStr string, entry []byte, reason bigcache.RemoveReason) {
			key, err := deserializeBlobSetKey(keyStr)
			if err != nil {
				return
			}

			blobNames, err := deserializeBlobNames(entry)
			if err != nil {
				return
			}

			onEvict(key, blobNames)
		}
		config = config.OnRemoveFilterSet(bigcache.Expired, bigcache.NoSpace, bigcache.Deleted)
	}

	cache, err := bigcache.New(context.Background(), config)
	if err != nil {
		return nil, fmt.Errorf("failed to create bigcache: %w", err)
	}

	return &BigCacheAdapter{
		cache: cache,
	}, nil
}

// serializeBlobSetKey converts a BlobSetKey to a string for use as a key in bigcache
func serializeBlobSetKey(key blobset_cache.BlobSetKey) string {
	return key.TenantID + "::" + key.SetID
}

// deserializeBlobSetKey converts a string back to a BlobSetKey
func deserializeBlobSetKey(keyStr string) (blobset_cache.BlobSetKey, error) {
	parts := strings.Split(keyStr, "::")
	if len(parts) != 2 {
		return blobset_cache.BlobSetKey{}, fmt.Errorf("invalid blob set key format: %s", keyStr)
	}
	return blobset_cache.BlobSetKey{
		TenantID: parts[0],
		SetID:    parts[1],
	}, nil
}

// serializeBlobNames converts a slice of BlobNames to a byte array for storage in bigcache
func serializeBlobNames(blobNames []blob_names.BlobName) ([]byte, error) {
	const sha256Size = 32
	buf := make([]byte, 0, len(blobNames)*sha256Size)

	for _, blobName := range blobNames {
		blobBytes, err := blob_names.DecodeHexBlobName(blobName)
		if err != nil {
			return nil, fmt.Errorf("failed to decode blob name: %w", err)
		}

		buf = append(buf, blobBytes...)
	}

	return buf, nil
}

// deserializeBlobNames converts a byte array back to a slice of BlobNames
func deserializeBlobNames(data []byte) ([]blob_names.BlobName, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("empty data")
	}

	const sha256Size = 32
	if len(data)%sha256Size != 0 {
		return nil, fmt.Errorf("invalid data length: %d is not a multiple of %d", len(data), sha256Size)
	}

	count := len(data) / sha256Size
	blobNames := make([]blob_names.BlobName, count)

	for i := 0; i < count; i++ {
		start := i * sha256Size
		end := start + sha256Size
		blobBytes := data[start:end]

		blobNames[i] = blob_names.NewBlobNameFromBytes(blobBytes)
	}

	return blobNames, nil
}

// GetBlobSet implements CacheInterface
func (a *BigCacheAdapter) GetBlobSet(key blobset_cache.BlobSetKey) ([]blob_names.BlobName, error) {
	keyStr := serializeBlobSetKey(key)

	entry, err := a.cache.Get(keyStr)
	if err != nil {
		if errors.Is(err, bigcache.ErrEntryNotFound) {
			return nil, blobset_cache.ErrEntryNotFound
		}
		return nil, err
	}

	blobNames, err := deserializeBlobNames(entry)
	if err != nil {
		return nil, err
	}

	return blobNames, nil
}

// SetBlobSet implements CacheInterface
func (a *BigCacheAdapter) SetBlobSet(key blobset_cache.BlobSetKey, blobNames []blob_names.BlobName) error {
	keyStr := serializeBlobSetKey(key)

	data, err := serializeBlobNames(blobNames)
	if err != nil {
		return err
	}

	err = a.cache.Set(keyStr, data)
	if err != nil {
		return err
	}

	return nil
}

// DeleteBlobSet implements CacheInterface
func (a *BigCacheAdapter) DeleteBlobSet(key blobset_cache.BlobSetKey) error {
	keyStr := serializeBlobSetKey(key)
	err := a.cache.Delete(keyStr)
	if err != nil {
		// Don't return error for non-existent keys
		if !errors.Is(err, bigcache.ErrEntryNotFound) {
			return err
		}
	}
	return nil
}

// NumEntries implements BlobSetCacheInterface
func (a *BigCacheAdapter) NumEntries() int {
	return a.cache.Len()
}

// MemorySizeInBytes implements BlobSetCacheInterface
func (a *BigCacheAdapter) MemorySizeInBytes() int {
	return a.cache.Capacity()
}

// Stats implements BlobSetCacheInterface
func (a *BigCacheAdapter) Stats() blobset_cache.CacheStats {
	bigCacheStats := a.cache.Stats()
	return blobset_cache.CacheStats{
		Hits:   bigCacheStats.Hits,
		Misses: bigCacheStats.Misses,
	}
}

// Close implements BlobSetCacheInterface
func (a *BigCacheAdapter) Close() {
	a.cache.Close()
}

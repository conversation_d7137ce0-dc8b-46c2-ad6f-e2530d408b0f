package main

import (
	"time"

	blob_names "github.com/augmentcode/augment/base/blob_names"
	"github.com/augmentcode/augment/base/go/blobset_cache"
)

// BlobSetCacheAdapter adapts the blobset_cache.BlobSetCache to the CacheInterface
type BlobSetCacheAdapter struct {
	cache *blobset_cache.BlobSetCache
}

// NewBlobSetCacheAdapter creates a new adapter for blobset_cache.BlobSetCache
func NewBlobSetCacheAdapter(ttl time.Duration, maxSizeInMB int, cleanupInterval time.Duration, onEvict blobset_cache.EvictionCallback) (CacheInterface, error) {
	cache, err := blobset_cache.NewBlobSetCache(ttl, maxSizeInMB, cleanupInterval, onEvict)
	if err != nil {
		return nil, err
	}
	return &BlobSetCacheAdapter{cache: cache}, nil
}

// GetBlobSet implements CacheInterface
func (a *BlobSetCacheAdapter) GetBlobSet(key blobset_cache.BlobSetKey) ([]blob_names.BlobName, error) {
	return a.cache.GetBlobSet(key)
}

// SetBlobSet implements CacheInterface
func (a *BlobSetCacheAdapter) SetBlobSet(key blobset_cache.BlobSetKey, blobNames []blob_names.BlobName) error {
	return a.cache.SetBlobSet(key, blobNames)
}

// DeleteBlobSet implements CacheInterface
func (a *BlobSetCacheAdapter) DeleteBlobSet(key blobset_cache.BlobSetKey) error {
	return a.cache.DeleteBlobSet(key)
}

// NumEntries implements CacheInterface
func (a *BlobSetCacheAdapter) NumEntries() int {
	return a.cache.NumEntries()
}

// MemorySizeInBytes implements CacheInterface
func (a *BlobSetCacheAdapter) MemorySizeInBytes() int {
	return a.cache.MemorySizeInBytes()
}

// Stats implements CacheInterface
func (a *BlobSetCacheAdapter) Stats() blobset_cache.CacheStats {
	return a.cache.Stats()
}

// Close implements CacheInterface
func (a *BlobSetCacheAdapter) Close() {
	a.cache.Close()
}

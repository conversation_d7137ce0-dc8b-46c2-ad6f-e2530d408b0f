load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

go_library(
    name = "server_lib",
    srcs = [
        "bigcache_adapter.go",
        "bigtable_helper.go",
        "blobset_cache_adapter.go",
        "cache_interface.go",
        "lsh_index.go",
        "main.go",
        "minhash.go",
        "server.go",
        "store.go",
    ],
    importpath = "github.com/augmentcode/augment/services/working_set/server/go",
    visibility = ["//visibility:private"],
    deps = [
        "//base/blob_names:blob_names_go",
        "//base/blob_names:blob_names_go_proto",
        "//base/feature_flags:feature_flags_go",
        "//base/go/blobset_cache",
        "//base/go/durationutil",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//base/tracing/go:tracing_go",
        "//services/bigtable_proxy:bigtable_proxy_go_proto",
        "//services/bigtable_proxy/client:client_go",
        "//services/checkpoint_indexer:checkpoint_indexer_go_proto",
        "//services/content_manager/client:client_go",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/metrics:grpc_metrics_go",
        "//services/lib/grpc/recovery:grpc_recovery_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/request_context:request_context_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "//services/working_set:working_set_go_proto",
        "@com_github_alitto_pond_v2//:pond",
        "@com_github_allegro_bigcache_v3//:bigcache",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_github_stretchr_testify//mock",
        "@com_google_cloud_go_bigtable//:bigtable",
        "@com_google_cloud_go_bigtable//apiv2/bigtablepb",
        "@com_google_cloud_go_pubsub//:pubsub",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//proto",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
)

# create a container image
go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/lib/pubsub:pubsub-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":monitoring_kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

go_test(
    name = "lsh_index_test",
    srcs = ["lsh_index_test.go"],
    embed = [":server_lib"],
)

go_test(
    name = "minhash_test",
    srcs = ["minhash_test.go"],
    embed = [":server_lib"],
)

go_test(
    name = "bigtable_helper_test",
    srcs = [
        "bigtable_helper_test.go",
        "test_utils.go",
    ],
    embed = [":server_lib"],
    deps = [
        "//base/blob_names:blob_names_go",
        "//base/blob_names:blob_names_go_proto",
        "//base/go/secretstring:secretstring_go",
        "//base/test_utils/pubsub:pubsub_emulator_go",
        "//services/bigtable_proxy:bigtable_proxy_go_proto",
        "//services/bigtable_proxy/client:fake_client_go",
        "//services/content_manager:content_manager_go_proto",
        "//services/content_manager/client:client_go",
        "//services/lib/request_context:request_context_go",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@com_github_stretchr_testify//require",
        "@com_google_cloud_go_pubsub//:pubsub",
        "@org_golang_google_api//option",
    ],
)

go_test(
    name = "store_test",
    srcs = [
        "mock_feature_flag_handle.go",
        "store_test.go",
        "test_utils.go",
    ],
    embed = [":server_lib"],
    deps = [
        "//base/blob_names:blob_names_go",
        "//base/blob_names:blob_names_go_proto",
        "//base/feature_flags:feature_flags_go",
        "//base/go/secretstring:secretstring_go",
        "//base/test_utils/pubsub:pubsub_emulator_go",
        "//services/bigtable_proxy:bigtable_proxy_go_proto",
        "//services/bigtable_proxy/client:fake_client_go",
        "//services/checkpoint_indexer:checkpoint_indexer_go_proto",
        "//services/content_manager:content_manager_go_proto",
        "//services/content_manager/client:client_go",
        "//services/lib/request_context:request_context_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@com_github_stretchr_testify//require",
        "@com_google_cloud_go_pubsub//:pubsub",
        "@org_golang_google_api//option",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//proto",
    ],
)

go_test(
    name = "bigcache_adapter_test",
    srcs = ["bigcache_adapter_test.go"],
    embed = [":server_lib"],
    deps = [
        "//base/blob_names:blob_names_go",
        "//base/go/blobset_cache",
        "@com_github_allegro_bigcache_v3//:bigcache",
        "@com_github_stretchr_testify//assert",
    ],
)

kubecfg(
    name = "monitoring_kubecfg",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

name: Storybook
on:
  push:
    paths:
      - clients/common/webviews/src/**
      - clients/common/webviews/mocks/**
      - clients/common/webviews/scripts/**
      - .github/workflows/storybook.yaml
    branches:
      - main
  pull_request:
    paths:
      - clients/common/webviews/src/**
      - clients/common/webviews/mocks/**
      - clients/common/webviews/scripts/**
      - .github/workflows/storybook.yaml

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  upload:
    runs-on:
      - self-hosted
      - gcp-us1
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      - uses: ./.github/actions/changed-files
        id: changes
        with:
          files: |
            clients/common/webviews/src/**
            clients/common/webviews/mocks/**
            clients/common/webviews/scripts/**
            .github/workflows/storybook.yaml
      - name: List all changed files
        if: steps.changes.outputs.all_changed_files_count != 0
        run: |
          echo "Changed files:"
          cat "${{ steps.changes.outputs.all_changed_files_path }}"
      - name: Noop
        if: steps.changes.outputs.all_changed_files_count == 0
        run: echo "No changes to storybook, skipping upload"
      - uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a
        if: steps.changes.outputs.all_changed_files_count != 0
        with:
          node-version: ">= 20.15.1"
      - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda
        if: steps.changes.outputs.all_changed_files_count != 0
        with:
          version: 9
      - name: Get short SHA
        if: steps.changes.outputs.all_changed_files_count != 0
        id: slug
        run: echo "sha=$(git rev-parse --short=5 HEAD)" >> $GITHUB_OUTPUT
      - run: pnpm install
        if: steps.changes.outputs.all_changed_files_count != 0
      - run: pnpm run upload-storybook --replace gcp-us1-public-html storybook/${{ github.ref_name }}/${{ steps.slug.outputs.sha }}
        if: steps.changes.outputs.all_changed_files_count != 0
        working-directory: clients/common/webviews
        env:
          CI_OBJECT_STORE_CONFIG: ${{ secrets.CI_OBJECT_STORE_CONFIG }}
      - run: pnpm run upload-storybook --replace gcp-us1-public-html storybook/${{ github.ref_name }}/latest
        if: github.ref_name == 'main' && steps.changes.outputs.all_changed_files_count != 0
        working-directory: clients/common/webviews
        env:
          CI_OBJECT_STORE_CONFIG: ${{ secrets.CI_OBJECT_STORE_CONFIG }}
      - name: update pr
        if: github.event_name == 'pull_request' && steps.changes.outputs.all_changed_files_count != 0
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🎨 Storybook deployed to: https://webserver.gcp-us1.r.augmentcode.com/storybook/${{ github.ref_name }}/${{ steps.slug.outputs.sha }}/index.html`
            })

# Because the CI system runs the commit hook, we can
# restrict hooks to run locally by setting them to run
# in the "push" stage
default_language_version:
    python: python3.11
    node: 22.5.1 # NOTE(mattm): drop pin after https://github.com/pre-commit/pre-commit/issues/3261
repos:
    - repo: https://github.com/pre-commit/pre-commit-hooks
      rev: v4.4.0
      hooks:
            - id: check-added-large-files
              exclude: "MODULE.bazel.lock"
            - id: check-case-conflict
            - id: check-json
              exclude: ^third_party/|tsconfig.json|.eslintrc.json|\.vscode/
            - id: check-symlinks
              exclude: ^third_party/
            - id: check-yaml
              args: [--allow-multiple-documents]
              exclude: ^third_party/|pnpm-lock.yaml|experimental/marcmac/gcp/helm/
            - id: destroyed-symlinks
            - id: detect-aws-credentials
              args: [
                --allow-missing-credentials,
                ]
              exclude: ^third_party/
            - id: detect-private-key
              exclude: ^third_party/|^.*/test_data/[a-z0-9A-Z_-]+.(jwks|jwt|pem)$|services/request_insight/support_database/exporter/exporter_test.go
            - id: end-of-file-fixer
              exclude: |
                  (?x)^(
                    .*_pb\.d\.ts|
                    .*_pb\.js|
                    .*_connect.d.ts|
                    third_party/.*|
                    clients/intellij/src/test/testData/.*|
                    clients/.*\.d.ts|
                    research/data/synthetic_code_edit/seeds/.*/.*\.txt|
                    research/eval/edit/data/.*/.*\.txt|
                    services/integrations/github/processor/.*\.d.ts|
                    services/token_exchange/client/test_data/.*\.jwt|
                    services/lib/grpc/auth/test_data/.*\.jwt|
                    services/request_insight/analytics/.*\.d.ts|
                    services/request_insight/.*\.d.ts|
                    services/share/.*\.d.ts|
                    services/tenant_watcher/.*\.d.ts|
                    services/token_exchange/.*\.d.ts|
                    services/auth/central/server/.*\.d.ts|
                  )$
            - id: trailing-whitespace
              exclude: ^third_party/|^research/data/synthetic_code_edit/seeds/.*/.*\.txt$|^research/eval/edit/data/.*/.*\.txt$|^.*/__snapshots__/.*|^base/prompt_format_next_edit/description_prompt_formatter_test.py$|^clients/sidecar/libs/src/tools/sidecar-tools/str-replace-editor-tool/__tests__/test_data/

    - repo: https://github.com/astral-sh/ruff-pre-commit
      # Ruff version.
      rev: v0.6.1
      hooks:
        # Run the linter.
        - id: ruff
          args: [ --fix ]
        # Run the formatter.
        - id: ruff-format
        # manual stages to auto-correct
        - id: ruff
          args: [ --fix ]
          stages: [manual]
        - id: ruff-format
          stages: [manual]

    - repo: local
      hooks:
        - id: check-notebook-output
          name: check-notebook-output
          language: python
          files: .*.ipynb
          description: verify the output of any jupyter notebooks is empty
          entry: research/gpt-neox/tools/check_notebook_output.py

        - id: clear-notebook-output
          name: clear-notebook-output
          language: python
          files: .*.ipynb
          description: verify the output of any jupyter notebooks is empty
          entry: research/gpt-neox/tools/check_notebook_output.py --clear
          stages: [manual]

    - repo: local
      hooks:
        - id: check-experimental-imports
          name: check-experimental-imports
          language: python
          files: .*.py
          description: check for invalid imports
          entry: research/precommit/check_experimental_imports.py
          exclude: ^experimental/

    - repo: https://github.com/Yelp/detect-secrets
      rev: v1.5.0
      hooks:
        - id: detect-secrets
          args: ['--baseline', '.secrets.baseline', 'audit']
          exclude: package.lock.json|pnpm-lock.yaml|^.*/test_data/[a-z0-9A-Z_-]+.(jwks|jwt|pem)$|^research/data/synthetic_code_edit/seeds/.*/.*\.txt$|^research/eval/edit/data/.*/.*\.txt$|research/infra/cfg/secrets/eng_secrets/.*json$

    - repo: local
      hooks:
      - id: pyright
        name: pyright
        entry: pyright
        language: system
        types: [python]
        args: [--stats, -p, pyrightconfig.ci.json]
        # Keep the files/exclude in sync with pyrightconfig.json
        files: '^research/'
        exclude: '^research/(data|gpt-neox|eval/dataset_generation_lib|eval/edit/experimental_edit_systems.py|eval/edit/visualize_result.py|eval/generation/execution.py|eval/hydra|eval/vulcan/testdata|retrieval/legacy_retrieval_implementations/bm25|fastbackward|static_analysis/tests/testdata|fim/tests/testdata)/'

    - repo: https://github.com/pre-commit/mirrors-eslint
      rev: v8.56.0
      hooks:
      - id: eslint
        stages: [commit, push]
        args: [--quiet]
        types: [file]
        types_or: [javascript, ts, tsx, svelte]
        exclude: '.*/testdata/.*'
        additional_dependencies:
        - 'eslint@8.57.0'
        - '@typescript-eslint/eslint-plugin'
        - 'eslint-config-react-app'
        - '@typescript-eslint/parser'
        - 'eslint-plugin-jest'
        - 'eslint-plugin-mocha'
        - 'eslint-plugin-svelte'
        - 'svelte-eslint-parser'
        - 'svelte'
        - '@sveltejs/vite-plugin-svelte'
        - 'eslint-plugin-unused-imports'
        - 'eslint-plugin-import'
        - 'eslint-import-resolver-alias'
        - 'eslint-import-resolver-typescript'
        - '@vitest/eslint-plugin'
      - id: eslint
        stages: [manual]
        args: [--quiet, --fix]
        types: [file]
        types_or: [javascript, ts, tsx, svelte]
        exclude: '.*/testdata/.*'
        additional_dependencies:
        - 'eslint@8.57.0'
        - '@typescript-eslint/eslint-plugin'
        - 'eslint-config-react-app'
        - '@typescript-eslint/parser'
        - 'eslint-plugin-jest'
        - 'eslint-plugin-mocha'
        - 'eslint-plugin-svelte'
        - 'svelte-eslint-parser'
        - 'svelte'
        - '@sveltejs/vite-plugin-svelte'
        - 'eslint-plugin-unused-imports'
        - 'eslint-plugin-import'
        - 'eslint-import-resolver-alias'
        - 'eslint-import-resolver-typescript'
        - '@vitest/eslint-plugin'

    - repo: https://github.com/pre-commit/mirrors-prettier
      rev: v3.1.0
      hooks:
      - id: prettier
        entry: prettier  --write --list-different --ignore-unknown
        types_or: [css, javascript, ts, tsx, svelte]
        stages: [commit, push]
        exclude: '.*/testdata/.*'
        additional_dependencies:
        -   'prettier@3.3.3'
        -   'prettier-plugin-svelte'
      - id: prettier
        entry: prettier --write --check --ignore-unknown
        types_or: [css, javascript, ts, tsx, svelte]
        stages: [manual]
        exclude: '.*/testdata/.*'
        additional_dependencies:
        -   'prettier@3.3.3'
        -   'prettier-plugin-svelte'
    - repo: https://github.com/macisamuele/language-formatters-pre-commit-hooks
      rev: v2.13.0
      hooks:
        - id: pretty-format-kotlin
          args: [--autofix]
          stages: [commit, push]
        - id: pretty-format-kotlin
          stages: [manual]

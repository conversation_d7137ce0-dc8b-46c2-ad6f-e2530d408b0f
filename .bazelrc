# See https://bazel.build/run/bazelrc for the syntax of this file

# Disable WARNING and DEBUG messages from bazel (not from test/program output).
#
# If you are debugging bazel build issues, it may help to comment this out.
#
# When using print() in starlark, print() outputs as DEBUG and get filtered out.
common --ui_event_filters=-WARNING,-DEBUG

build --verbose_failures
# deal (a bit better) with remote cache eviction problems
common --experimental_remote_cache_eviction_retries=4
# Larger stdout
build --experimental_ui_max_stdouterr_bytes=4194304
build --force_pic
build --experimental_cc_implementation_deps
# Use CUDA
build --@rules_cuda//cuda:archs=compute_90a:compute_90a,sm_90a;compute_80:compute_80,sm_80,sm_86 --@rules_cuda//cuda:enable
build --@rules_cuda//cuda:copts="--use_fast_math"
build --@rules_cuda//cuda:copts="-fno-omit-frame-pointer"
build --@rules_cuda//cuda:copts="-std=c++17"
build --@rules_cuda//cuda:copts="-forward-unknown-to-host-compiler"
build --@rules_cuda//cuda:runtime=@remote_cuda_toolchain//:cuda_runtime_static
# download java
build --java_runtime_version=remotejdk_11
# linkmode=normal causes executable panics immediately on startup. linkmode=pie seems more stable.
build --@io_bazel_rules_go//go/config:linkmode=pie

# only typecheck specified ts files
common --@aspect_rules_ts//ts:skipLibCheck=always
common --@aspect_rules_ts//ts:default_to_tsc_transpiler

common --extra_toolchains=@llvm_toolchain//:cc-toolchain-x86_64-linux

# Speed up all builds by not checking if external repository files have been modified.
# Docs: https://github.com/bazelbuild/bazel/blob/1af61b21df99edc2fc66939cdf14449c2661f873/src/main/java/com/google/devtools/build/lib/bazel/repository/RepositoryOptions.java#L244
common --noexperimental_check_external_repository_files


# Don't report when the root module's lower bound for a dependency happens to be less than the resolved version.
# This is expected and should NOT prompt an engineer to update our lower bound to match.
# WARNING: For repository 'aspect_bazel_lib', the root module requires module version aspect_bazel_lib@1.30.2,
# but got aspect_bazel_lib@1.31.2 in the resolved dependency graph.
# see https://docs.aspect.build/guides/bazelrc/#options-introduced-in-bazel-70
common --check_direct_dependencies=off

# forward kubernetes envs to tests
common --test_env=KUBERNETES_SERVICE_PORT
common --test_env=KUBERNETES_PORT
common --test_env=KUBERNETES_PORT_443_TCP_ADDR
common --test_env=KUBERNETES_PORT_443_TCP_PORT
common --test_env=KUBERNETES_PORT_443_TCP_PROTO
common --test_env=KUBERNETES_PORT_443_TCP
common --test_env=KUBERNETES_SERVICE_PORT_HTTPS
common --test_env=KUBERNETES_SERVICE_HOST

# forward the namespace
common --test_env=BUILD_USER_NAMESPACE

common --test_env=NAMESPACE_MANAGER_ENDPOINT
common --test_env=TEST_RUNNER_JOB_ID

# Only forward visible devices
common --test_env=CUDA_VISIBLE_DEVICES

# ensure TRITON knows where to look for libcuda.so
common --test_env=TRITON_CUDA_DIRS

build --cxxopt=-std=c++17
build --cxxopt=-Wall
build --copt=-fPIC

# sandbox for exclusive tests
test --incompatible_exclusive_test_sandboxed

# matches developer expectations
test --build_tests_only

build --reuse_sandbox_directories
build --experimental_guard_against_concurrent_changes

# reduce the number of symlinks in the runfiles tree by avoiding the 'external' directories
build --nolegacy_external_runfiles

# leave a bit of room for other operations
build --local_resources=cpu=HOST_CPUS*.85
build --local_resources=memory=HOST_RAM*.75

# upload in the background
build --experimental_remote_cache_async
# speedup cache hit checking
build --experimental_remote_merkle_tree_cache
# speed remote build after bazel (local) server restart
build --experimental_action_cache_store_output_metadata

build --workspace_status_command="./tools/bzl/workspace-status"
# hide env variables unless explicitly enabled
build --incompatible_strict_action_env
build --remote_timeout=600s

common --@aspect_rules_py//py:interpreter_version=3.11.0

build --incompatible_enable_cc_toolchain_resolution

build --attempt_to_print_relative_paths
test --test_output=errors
test --test_summary=terse

# see https://bazelbuild.github.io/rules_rust/rust_clippy.html
build --aspects=@rules_rust//rust:defs.bzl%rust_clippy_aspect
build --output_groups=+clippy_checks
build --@rules_rust//:extra_rustc_flag=--cfg=tokio_unstable
build --@rules_rust//:extra_rustc_flag=--cfg=tokio_taskdump

# user bazelrc file
# see https://bazel.build/configure/best-practices
try-import %workspace%/user.bazelrc

common --enable_bzlmod

# Add config for remote download of outputs.
# Used by //clients/ to download outputs with ibazel
build:local_output --remote_download_outputs=all

////////////////////////////////////////////////////////////////////////////////
//
// K8s Core API
//

local k8s_base = import 'k8s-base.jsonnet';
local enums = import 'k8s-enums.jsonnet';

k8s_base + {
  //////////////////////////////////////////////////////////////////////////////
  //
  // Top Level Objects
  //

  Namespace:: $.ClusterObject + {
    apiVersion: 'v1',
    kind: 'Namespace',
  },

  ConfigMap:: $.Object + {
    apiVersion: 'v1',
    kind: 'ConfigMap',
  },

  PV:: $.PersistentVolume,
  PVC:: $.PersistentVolumeClaim,

  PersistentVolume:: $.ClusterObject + {
    local pvc = self,
    apiVersion: 'v1',
    kind: 'PersistentVolume',
    spec+: $.BaseSpec,
  },

  PersistentVolumeClaim:: $.Object + {
    local pvc = self,
    apiVersion: 'v1',
    kind: 'PersistentVolumeClaim',
    storageCapacity:: error 'required',

    spec+: $.BaseSpec + {
      // storageClassName: (typical field)
      // accessModes: (typical field)
      resources+: {
        requests+: {
          storage: pvc.storageCapacity,
        },
      },
    },
  },

  Pod:: $.Object + {
    apiVersion: 'v1',
    kind: 'Pod',
    spec: $.PodSpec,
  },

  Secret:: $.Object + {
    i_know_what_im_doing:: error 'Consider using a SealedSecret',

    local sec = self,
    apiVersion: 'v1',
    kind: if sec.i_know_what_im_doing == true then 'Secret' else null,
    type: enums.SECRET_TYPE.OPAQUE,
    immutable: null,
    // data: (typical field)
    // stringData: (typical field)
  },

  ServiceAccount:: $.Object + {
    apiVersion: 'v1',
    kind: 'ServiceAccount',
  },

  Service:: $.Object + {
    local svc = self,
    apiVersion: 'v1',
    kind: 'Service',
    externalDNS:: null,
    metadata+: {
      annotations+: std.prune({
        'external-dns.alpha.kubernetes.io/hostname': svc.externalDNS,
      }),
    },
    spec+: $.BaseSpec + {
      type: enums.SERVICE_TYPE.CLUSTER_IP,
      ports: [],
    },
  },

  HeadlessService:: $.Service + {
    spec+: {
      type: enums.SERVICE_TYPE.CLUSTER_IP,
      clusterIP: 'None',
    },
  },

  LoadBalancerService:: $.Service + {
    spec+: {
      type: enums.SERVICE_TYPE.LOAD_BALANCER,
      externalTrafficPolicy: 'Local',
    },
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Lower Level Types and Specs
  //

  PodTemplateSpec:: $.BaseTemplateSpec + {
    spec+: $.PodSpec,
  },

  PodSpec:: $.BaseSpec + {
    containers+: [],
    volumes+: self.volmount_volumes,
    enableServiceLinks: false,  // override k8s default

    volmounts+:: [],
    _volmounts:: $.VolMounts(self.volmounts),
    volmount_volumes:: self._volmounts.volumes,
    volmount_mounts:: self._volmounts.mounts,
  },

  Container:: $.BaseSpec + {
    name: error 'required',
    image: error 'required',
  },

  VolMounts(items):: {
    // VolMounts is a helper for the common case where Volumes and Mounts are 1:1.
    // Each item is an object, with a `name`, a `volume` and a `mount`. The returned object
    // has two main properties:
    //  - volumes: A list of K8s Volume specs, appropriate for the PodSpec.volumes field
    //  - mounts: A list of K8s VolumeMount specs, appropariate for the Container.mounts field
    //
    // Most users don't need to call this directly. Instead, populate the PodSpec.volmounts property.
    // The PodSpec.volumes property will be automatically populated, but Container.mounts will need
    // to be manually set to the PodSpec's volmount_mounts property.
    volumes:: [
      i.volume + { name: i.name }
      for i in items
      if i != null
    ],
    mounts:: [
      i.mount + { name: i.name }
      for i in items
      if i != null
    ] + [
      em + { name: i.name }
      for i in items
      if i != null && std.objectHas(i, 'extraMounts')
      for em in i.extraMounts
    ],
  },

}

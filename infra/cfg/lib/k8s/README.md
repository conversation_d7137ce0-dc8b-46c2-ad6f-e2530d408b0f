## Generic K8s Jsonnet Library

This library is used for building K8s objects in jsonnet. It aims to be as
generic and unopinionated as possible. At the least, it removes much of the
boilerplate from defining K8s objects. Additional features are noted below.

### Usage

Most users will not use this library directly. Instead see
[`providers/`](providers/) which wraps this library in an additional layer with
extensions and settings specific to GKE, CoreWeave, etc.

Whether using this library directly, or one of the [`providers/`](providers/)
wrappers, there are basically two styles to choose from. The *environment* style
is preferred, but the *builder* style is perfectly acceptable. See
[`examples/`](examples/) for details.

### Features

*Some* of the features on top of an extremely vanilla library are:

 - Base/Metadata:
   - `BaseLabels`, `BaseObject`, `ClusterObject`, and `Object` can be extended to automagically apply config to all objects built from the library.

   - `BaseLabels` apply not just to top-level object metadata, but to TemplateSpecs. For example, the Pod metadata for pods owned by Deployments, StatefulSets, Jobs, CronJobs, etc

   - `name` and `namespace` are upleveled to the top level of objects so they're easier to set and get. Additional fields fields are also upleveld (inconsistently, as mattm saw fit...).

 - Core:
   - `PVC` is an alias for `PersistentVolumeClaim`.

   - `Service` adds support for setting the `external-dns` label.

   - `HeadlessService` and `LoadBalancerServer` as wrappers around `Service`.

   - `PodSpec.enableServiceLinks` defaults to false, overriding the k8s default. It's up for debate whether this feature is more annoying than useless or more useless than annoying.

   - A "`VolMounts`" helper is added for defining volumes and mounts (one or more) together.

 - Apps:
   - Selector labels for `Deployment`, `StatefulSet`, and `DaemonSet` are added automatically. (I'm not sure why this is the user's responsibility in the first place.)

 - RBAC:
   - Constants for sets of verbs (`LIST_VERBS`, `READ_VERBS`, `WRITE_VERBS`, etc)

   - Helpers properties for creating role bindings for service account objects and usernames.

   - Support for `RoleBinding` over `ClusterRole`s.

local k8s_base = import 'k8s-base.jsonnet';

k8s_base + {
  StorageClass:: $.ClusterObject + {
    local sc = self,

    apiVersion: 'storage.k8s.io/v1',
    kind: 'StorageClass',
    default:: null,
    metadata+: if sc.default != null then {
      annotations+: {
        'storageclass.kubernetes.io/is-default-class': std.toString(sc.default),
      },
    } else {},

    RECLAIM_POLICY:: {
      DELETE: 'Delete',
    },

    VOLUME_BINDING_MODE:: {
      IMMEDIATE: 'Immediate',
      WAIT_FOR_FIRST_CONSUMER: 'WaitForFirstConsumer',
    },
  },

  VolumeSnapshotClass:: $.ClusterObject + {
    apiVersion: 'snapshot.storage.k8s.io/v1',
    kind: 'VolumeSnapshotClass',

    driver: error 'VolumeSnapshotClass.driver is required',
    // parameters
    // deletionPolicy

    DELETION_POLICY:: {
      DELETE: 'Delete',
      RETAIN: 'Retain',
    },
  },

  VolumeSnapshotContent:: $.ClusterObject + {
    apiVersion: 'snapshot.storage.k8s.io/v1',
    kind: 'VolumeSnapshotContent',
    spec+: $.BaseSpec + {
      // deletionPolicy
      // driver
      // source.snapshotHandle
      // volumeSnapshotRef.{kind, name, namespace}
    },
  },

  VolumeSnapshot:: $.Object + {
    apiVersion: 'snapshot.storage.k8s.io/v1',
    kind: 'VolumeSnapshot',
    spec+: $.BaseSpec + {
      // volumeSnapshotClassName
      source+: {
        // || persistentVolumeClaimName
        // || volumeSnapshotContentName
      },
    },
  },

}

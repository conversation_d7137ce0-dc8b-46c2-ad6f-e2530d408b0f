local k8s_rbac = import 'k8s-rbac.jsonnet';

{
  IMAGE_PP:: {
    ALWAYS: 'Always',
    NEVER: 'Never',
    IF_NOT_PRESENT: 'IfNotPresent',
  },

  ACCESS_MODE:: {
    READ_WRITE_ONCE: 'ReadWriteOnce',
    READ_WRITE_MANY: 'ReadWriteMany',
  },

  SECRET_TYPE:: {
    OPAQUE: 'Opaque',
    SERVICE_ACCOUNT: 'kubernetes.io/service-account-token',
    DOCKER: 'kubernetes.io/dockerconfigjson',
    BASIC_AUTH: 'kubernetes.io/basic-auth',
  },

  SERVICE_TYPE:: {
    CLUSTER_IP: 'ClusterIP',
    EXTERNAL_NAME: 'ExternalName',
    NODE_PORT: 'NodePort',
    LOAD_BALANCER: 'LoadBalancer',
  },

  CRONJOB_POLICY:: {
    ALLOW: 'Allow',  // default
    FORBID: 'Forbid',
    REPLACE: 'Replace',
  },

  RESTART_POLICY:: {
    ALWAYS: 'Always',
    ON_FAILURE: 'OnFailure',
    NEVER: 'Never',
  },

  LIST_VERBS:: k8s_rbac.LIST_VERBS,
  READ_VERBS:: k8s_rbac.READ_VERBS,
  WRITE_VERBS_NO_COLLECTION:: k8s_rbac.WRITE_VERBS_NO_COLLECTION,
  WRITE_VERBS:: k8s_rbac.WRITE_VERBS,

  PREEMPTION_POLICY:: {
    NEVER: 'Never',
    LOWER: 'PreemptLowerPriority',
  },
}

local k8s_base = import 'k8s-base.jsonnet';

k8s_base + {
  ValidatingWebhookConfiguration:: $.ClusterObject + {
    apiVersion: 'admissionregistration.k8s.io/v1',
    kind: 'ValidatingWebhookConfiguration',
    // webooks: []self.ValidatingWebhook,
  },

  MutatingWebhookConfiguration:: $.ClusterObject + {
    apiVersion: 'admissionregistration.k8s.io/v1',
    kind: 'MutatingWebhookConfiguration',
    // webooks: []self.MutatingWebhook,
  },

  _WebHookCommon:: {
    name: error 'Webhook.name required.',
    admissionReviewVersions: ['v1'],

    SIDE_EFFECTS:: {
      NONE: 'None',
      SOME: 'Some',
      NONE_ON_DRYRUN: 'NoneOnDryRun',
    },

    FAILURE_POLICY:: {
      IGNORE: 'Ignore',
      FAIL: 'Fail',
    },

    MATCH_POLICY:: {
      EXACT: 'Exact',
      EQUIVALENT: 'Equivalent',
    },

    Rule:: {
      OPERATION_TYPE:: {
        ALL: '*',
        CREATE: 'CREATE',
        UPDATE: 'UPDATE',
        DELETE: 'DELETE',
        CONNECT: 'CONNECT',
      },

      SCOPE_TYPE:: {
        CLUSTER: 'Cluster',
        NAMESPACED: 'Namespaced',
        ALL: '*',
      },
    },
  },

  ValidatingWebhook:: $._WebHookCommon + {},

  MutatingWebhook:: $._WebHookCommon + {
    REINVOCATION_POLICY:: {
      NEVER: 'Never',
      IF_NEEDED: 'IfNeeded',
    },
  },
}

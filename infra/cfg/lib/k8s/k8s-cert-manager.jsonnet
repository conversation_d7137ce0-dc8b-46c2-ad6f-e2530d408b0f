local k8s_base = import 'k8s-base.jsonnet';

k8s_base + {
  local apiVersion = 'cert-manager.io/v1',

  ClusterIssuer:: $.ClusterObject + {
    apiVersion: apiVersion,
    kind: 'ClusterIssuer',
  },

  Issuer:: $.Object + {
    apiVersion: apiVersion,
    kind: 'Issuer',
  },

  Certificate:: $.Object + {
    local o = self,
    apiVersion: apiVersion,
    kind: 'Certificate',
    spec+: $.BaseSpec + {
      issuerRef+: {
        ISSUER:: 'Issuer',
        CLUSTER_ISSUER:: 'ClusterIssuer',
      },
      secretName: o.metadata.name,
      secretTemplate: {
        labels+: $.BaseLabels,
      },
    },
  },
}

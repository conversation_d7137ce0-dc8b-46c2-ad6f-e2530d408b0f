/* K8s Jsonnet Library Example: As an environment
 *
 * This example shows how to use the k8s library as an envrinment. This is the preferred style,
 * but the `as-builder` style is sometimes better suited. Both examples produce identical output.
 *
 * This example builds a ConfigMap+Deployment+Service(s).
 *
 */
local k8s = import '../k8s.jsonnet';

function(name='my-app') k8s + {

  // Add default labels to all objects and templates.
  BaseLabels+:: {
    'aug.service': name,
    'aug.style': 'as-environment',
  },

  // Set a default object name.
  BaseObject+:: {
    name:: 'my-app',
  },

  svc: $.Service + {
    spec+: {
      selector+: {
        'aug.service': name,
      },
    },
  },

  svc_headless: $.HeadlessService + {  // use optional HeadlessService wrapper.
    name+:: '-headless',  // extend default name
    spec+: {
      selector+: {
        'aug.service': name,
      },
    },
  },

  cm: $.ConfigMap + {
    data: {
      key0: 'val0',
      key1: 'val1',
    },
  },

  // Create a deployment. 'k8s.deployment' is automagically added to the selector and pod labels.
  deploy: $.Deployment + {
    spec+: {
      template+: {
        spec+: {
          local pod = self,
          containers+: [
            $.Container + {
              name: name,
              image: 'alpine:latest',
              command: ['sleep', 'infinity'],
              volumeMounts: pod.volmount_mounts,
            },
          ],
          // use "volmount" to define volume and its mounts in one place.
          volmounts+: [
            {
              local vm = self,
              name: $.cm.name,
              volume: {
                configMap: {
                  name: vm.name,
                },
              },
              mount: {
                mountPath: '/run/configs/' + vm.name,
              },
            },
          ],
        },
      },
    },
  },

}

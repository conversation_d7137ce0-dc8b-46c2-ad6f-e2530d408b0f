load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_to_json")

package(default_visibility = ["//visibility:private"])

jsonnet_to_json(
    name = "as-builder_json",
    src = "as-builder.jsonnet",
    outs = ["as-builder.json"],
    deps = ["//infra/cfg/lib/k8s"],
)

jsonnet_to_json(
    name = "as-environment_json",
    src = "as-environment.jsonnet",
    outs = ["as-environment.json"],
    deps = ["//infra/cfg/lib/k8s"],
)

sh_binary(
    name = "as-builder",
    srcs = [":example_sh"],
    args = ["$(location :as-builder_json)"],
    data = [":as-builder_json"],
)

sh_binary(
    name = "as-environment",
    srcs = [":example_sh"],
    args = ["$(location :as-environment_json)"],
    data = [":as-environment_json"],
)

gen<PERSON>le(
    name = "example_sh",
    outs = ["example.sh"],
    cmd = """printf 'cat $$1\n' > '$@'""",
)

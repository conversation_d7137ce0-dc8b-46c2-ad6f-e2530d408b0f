/* K8s Jsonnet Library Example: As a builder
 *
 * This example shows how to use the k8s library as a builder. This is sometimes necessary but in general the
 * `as-environment` style is preferred. Both examples produce identical output.
 *
 * This example builds a ConfigMap+Deployment+Service(s).
 *
 */
local _k8s = import '../k8s.jsonnet';

function(name='my-app') {
  local k8s = _k8s + {

    // Add default labels to all objects and templates.
    BaseLabels+:: {
      'aug.service': name,
      'aug.style': 'as-builder',
    },

    // Set a default object name.
    BaseObject+:: {
      name:: 'my-app',
    },
  },

  svc: k8s.Service + {
    spec+: {
      selector+: {
        'aug.service': name,
      },
    },
  },

  svc_headless: k8s.HeadlessService + {  // use optional HeadlessService wrapper.
    name+:: '-headless',  // extend default name
    spec+: {
      selector+: {
        'aug.service': name,
      },
    },
  },

  cm: k8s.ConfigMap + {
    data: {
      key0: 'val0',
      key1: 'val1',
    },
  },

  // Create a deployment. 'k8s.deployment' is automagically added to the selector and pod labels.
  deploy: k8s.Deployment + {
    spec+: {
      template+: {
        spec+: {
          local pod = self,
          containers+: [
            k8s.Container + {
              name: name,
              image: 'alpine:latest',
              command: ['sleep', 'infinity'],
              volumeMounts: pod.volmount_mounts,
            },
          ],
          // use "volmount" to define volume and its mounts in one place.
          volmounts+: [
            {
              local vm = self,
              name: $.cm.name,
              volume: {
                configMap: {
                  name: vm.name,
                },
              },
              mount: {
                mountPath: '/run/configs/' + vm.name,
              },
            },
          ],
        },
      },
    },
  },

}

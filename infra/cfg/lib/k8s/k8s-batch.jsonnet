local k8s_base = import 'k8s-base.jsonnet';
local enums = import 'k8s-enums.jsonnet';

k8s_base + {
  Job:: $.Object + {
    apiVersion: 'batch/v1',
    kind: 'Job',
    spec+: $.JobSpec,
  },

  CronJob:: $.Object + {
    local t = self,
    schedule:: error 'required',
    timezone:: null,
    concurrencyPolicy:: enums.CRONJOB_POLICY.ALLOW,
    podspec:: error 'required',

    apiVersion: 'batch/v1',
    kind: 'CronJob',
    spec+: $.BaseSpec + {
      schedule: t.schedule,
      timeZone: t.timezone,
      concurrencyPolicy: t.concurrencyPolicy,
      jobTemplate+: $.JobTemplateSpec + {
        spec+: {
          template+: {
            spec+: t.podspec,
          },
        },
      },
    },
  },

  JobSpec:: $.BaseSpec + {
    template+: $.PodTemplateSpec,
  },

  JobTemplateSpec:: $.BaseTemplateSpec + {
    spec+: $.JobSpec,
  },

}

local k8s_base = import 'k8s-base.jsonnet';

k8s_base + {
  Deployment:: $.Object + {
    local t = self,
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    spec+: $.BaseSpec + {
      selector+: $.LabelSelector + {
        matchLabels+: {
          'k8s.deployment': t.metadata.name,
        },
      },
      replicas: 1,
      template+: $.PodTemplateSpec + {
        metadata+: {
          labels+: {
            'k8s.deployment': t.metadata.name,
          },
        },
      },
    },
  },

  DaemonSet:: $.Object + {
    local t = self,
    apiVersion: 'apps/v1',
    kind: 'DaemonSet',
    spec+: $.BaseSpec + {
      selector+: $.LabelSelector + {
        matchLabels+: {
          'k8s.daemonset': t.metadata.name,
        },
      },
      template+: $.PodTemplateSpec + {
        metadata+: {
          labels+: {
            'k8s.daemonset': t.metadata.name,
          },
        },
      },
    },
  },

  StatefulSet:: $.Object + {
    local t = self,
    apiVersion: 'apps/v1',
    kind: 'StatefulSet',
    spec+: $.BaseSpec + {
      selector+: $.LabelSelector + {
        matchLabels+: {
          'k8s.statefulset': t.metadata.name,
        },
      },
      template+: $.PodTemplateSpec + {
        metadata+: {
          labels+: {
            'k8s.statefulset': t.metadata.name,
          },
        },
      },
      POD_MANAGEMENT_POLICY:: {
        ORDERED_READY: 'OrderedReady',
        PARALLEL: 'Parallel',
      },
      persistentVolumeClaimRetentionPolicy: {
        PVC_RETENTION_POLICY:: {
          RETAIN: 'Retain',
          DELETE: 'Delete',
        },
      },
    },
  },
}

local k8s_base = import 'k8s-base.jsonnet';

k8s_base + {
  local apiVersion = 'apiextensions.k8s.io/v1',

  CustomResourceDefinition:: $.ClusterObject + {
    local crd = self,
    apiVersion: apiVersion,
    kind: 'CustomResourceDefinition',
    metadata+: {
      name: crd.spec.names.plural + '.' + crd.spec.group,
    },
    spec+: $.BaseSpec + {
      group: error 'crd.spec.group is required',
      names+: {
        plural: error 'crd.spec.names.plural is required',
      },

      SCOPE_NAMESPACED:: 'Namespaced',
      SCOPE_CLUSTER:: 'Cluster',
      scope: error 'crd.spec.scope must be namespaced or cluster.',

      _version+:: {
        name: error 'crd._version.name is required (v1bete1, v1, etc)',
        served: true,
        storage: true,
        // additionalPrinterColumns: [],
        // selectableFields: [{jsonPath: ""}], (max 8)
        schema+: {
          openAPIV3Schema+: {},
        },
      },
    },
  },

  // CRD is a wrapper around a full CustomResourceDefinition which makes it easier to define
  // a typical CRD with a `spec` and `status` subresource.
  CRD:: $.CustomResourceDefinition + {
    local crd = self,
    spec+: {
      spec_prop:: {
        type: 'object',
      },
      global_validations:: [],
      selectable_fields:: [],
      versions: [self._version + {
        name: 'v1',
        subresources: {
          status: {},
        },
        selectableFields: [
          {
            jsonPath: p,
          }
          for p in crd.spec.selectable_fields
        ],
        schema+: {
          openAPIV3Schema+: {
            type: 'object',
            required: ['spec'],
            properties+: {
              spec+: crd.spec.spec_prop,
            },
            'x-kubernetes-validations'+: crd.spec.global_validations,
          },
        },
      }],
    },
  },

}

local k8s = import '../k8s.jsonnet';

k8s + {
  PVC+:: {
    local s = self,
    storageClass:: 'shared-vast',
    accessMode:: $.ACCESS_MODE.READ_WRITE_ONCE,
    spec+: {
      storageClassName: s.storageClass,
      accessModes: [s.accessMode],
    },
  },

  LoadBalancerService+:: {
    metadata+: {
      annotations+: {
        'service.beta.kubernetes.io/coreweave-load-balancer-ip-families': 'ipv4',
        'service.beta.kubernetes.io/coreweave-load-balancer-type': 'public',
      },
    },
  },

  Ingress+:: {
    local t = self,
    ingress_hostname:: error 'Ingress.ingress_hostname required',
    ingress_service:: error 'Ingress.ingress_service required',
    ingress_port:: error 'Ingress.ingress_port required',
    cert_issuer:: 'letsencrypt-prod',
    metadata+: {
      annotations+: {
        'cert-manager.io/cluster-issuer': t.cert_issuer,
      },
    },
    spec+: {
      ingressClassName: 'nginx',
      tls: [
        {
          hosts: [t.ingress_hostname],
          secretName: t.metadata.name + '-tls',
        },
      ],
      rules: [
        {
          host: t.ingress_hostname,
          http: {
            paths: [
              {
                path: '/',
                pathType: 'Prefix',
                backend: {
                  service: {
                    name: t.ingress_service,
                    port: {
                      number: t.ingress_port,
                    },
                  },
                },
              },
            ],
          },
        },
      ],
    },
  },

  CW_OBJECT_STORAGE_ACCESS:: {
    // https://docs.coreweave.com/storage/object-storage#identity-and-access-management-iam-and-access-levels
    READ: 'read',
    WRITE: 'write',
    RW: 'readwrite',
    FULL: 'full',
  },

  CWObjectStorageUser:: $.Object + {
    local o = self,
    apiVersion: 'objectstorage.coreweave.com/v1alpha1',
    kind: 'User',
    access:: $.CW_OBJECT_STORAGE_ACCESS.READ,  // default
    spec+: $.BaseSpec + std.prune({
      owner: o.metadata.namespace,
      access: o.access,
      secretName: null,  // defaults to <namespace>-<name>-obj-store-creds
    }),
  },
}

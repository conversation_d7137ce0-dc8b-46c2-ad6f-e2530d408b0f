local gcp_cnrm = import '../gcp-cnrm.jsonnet';
local k8s = import '../k8s.jsonnet';

k8s + {
  PVC+:: {
    local t = self,
    storageClassName:: 'premium-rwx-vpc0',
    accessMode:: k8s.ACCESS_MODE.READ_WRITE_MANY,
    spec+: {
      storageClassName: t.storageClassName,
      accessModes: [t.accessMode],
    },
  },

  Network:: $.ClusterObject + {
    apiVersion: 'networking.gke.io/v1',
    kind: 'Network',
    TYPE:: {
      L2: 'L2',
      L3: 'L3',
      DEVICE: 'Device',
    },

    local o = self,
    type:: null,
    params:: null,

    spec+: $.BaseSpec + std.prune({
      type: o.type,
      parametersRef: if o.params != null then {
        group: o.params.apiGroup,
        kind: o.params.kind,
        name: o.params.metadata.name,
        namespace: std.get(o.params.metadata, 'namespace', null),
      },
    }),
  },

  GKENetworkParamSet:: $.ClusterObject + {
    apiVersion: 'networking.gke.io/v1',
    kind: 'GKENetworkParamSet',

    DEVICE_MODE:: {
      DPDK_VFIO: 'DPDK-VFIO',
      NET_DEVICE: 'NetDevice',
      RDMA: 'RDMA',
    },

    local o = self,
    vpc:: null,
    vpcSubnet:: null,
    deviceMode:: null,

    spec+: $.BaseSpec + std.prune({
      vpc: o.vpc,
      vpcSubnet: o.vpcSubnet,
      deviceMode: o.deviceMode,
    }),
  },

} + (
  gcp_cnrm
)

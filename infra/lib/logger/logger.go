package logger

import (
	"io"
	"log"
	"os"
	"strings"
	"time"
)

type Logger struct {
	now func() time.Time
	log *log.Logger
	tag string
}

func New(w io.Writer) Logger {
	if w == nil {
		w = os.<PERSON>derr
	}
	return Logger{
		now: time.Now,
		log: log.New(w, "", 0),
	}
}

func WithTag(l Logger, tag string) Logger {
	l.tag = tag
	return l
}

func (l Logger) Log(lvl string, fmt string, a ...any) {
	t := l.now().Format("2006-01-02 15:04:05 MST")
	if d := len("NOTICE") - len(lvl); d > 0 { // NOTICE is the longest level, below
		lvl += strings.Repeat(" ", d)
	}
	pfx := t + " " + lvl
	if l.tag != "" {
		pfx += " [" + l.tag + "]"
	}
	l.log.Printf(pfx+" "+fmt+"\n", a...)
}

func (l Logger) LogDebug(fmt string, a ...any) {
	l.Log("DEBUG", fmt, a...)
}

func (l Logger) LogInfo(fmt string, a ...any) {
	l.Log("INFO", fmt, a...)
}

func (l Logger) LogNotice(fmt string, a ...any) {
	l.Log("NOTICE", fmt, a...)
}

func (l Logger) LogWarn(fmt string, a ...any) {
	l.Log("WARN", fmt, a...)
}

func (l Logger) LogErr(fmt string, a ...any) {
	l.Log("ERR", fmt, a...)
}

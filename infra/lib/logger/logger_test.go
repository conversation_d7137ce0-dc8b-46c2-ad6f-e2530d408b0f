package logger

import (
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
)

func TestLogger(t *testing.T) {
	bld := &strings.Builder{}
	logger := New(bld).AtZero()
	logger2 := WithTag(logger, "tag0")

	logger.LogDebug("test %d", 1)
	logger2.LogInfo("test %d", 2)
	logger.LogNotice("test %d", 3)
	logger2.LogWarn("test %d", 4)
	logger.LogErr("test %d", 5)

	got := strings.Split(bld.String(), "\n")
	want := []string{
		"0001-01-01 00:00:00 UTC DEBUG  test 1",
		"0001-01-01 00:00:00 UTC INFO   [tag0] test 2",
		"0001-01-01 00:00:00 UTC NOTICE test 3",
		"0001-01-01 00:00:00 UTC WARN   [tag0] test 4",
		"0001-01-01 00:00:00 UTC ERR    test 5",
		"",
	}

	if diff := cmp.Diff(want, got); diff != "" {
		t.Errorf("diff -want +got:\n%s", diff)
	}
}

load("//tools/bzl:go.bzl", "go_library", "go_test")

package(default_visibility = ["//infra:internal"])

go_library(
    name = "logger",
    srcs = [
        "logger.go",
        "testutil.go",
    ],
    importpath = "github.com/augmentcode/augment/infra/lib/logger",
)

go_test(
    name = "logger_test",
    size = "small",
    srcs = ["logger_test.go"],
    embed = [":logger"],
    deps = ["@com_github_google_go_cmp//cmp"],
)

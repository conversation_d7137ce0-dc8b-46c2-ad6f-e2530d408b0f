package utils

import (
	"context"
)

// CtxMutex is a channel-based mutex implementation that supports locking with
// context.
type CtxMutex interface {
	Lock(context.Context) error
	UnLock()
	Locked() bool
}

// NewCtxMutex builds a new mutex.
func NewCtxMutex() CtxMutex {
	return &ctxmu{
		ch: make(chan struct{}, 1),
	}
}

type ctxmu struct {
	ch chan struct{}
}

func (mu *ctxmu) Lock(ctx context.Context) error {
	select {
	case <-ctx.Done():
		return context.Cause(ctx)
	case mu.ch <- struct{}{}:
		return nil
	}
}

func (mu *ctxmu) UnLock() {
	<-mu.ch
}

func (mu *ctxmu) Locked() bool {
	return len(mu.ch) > 0
}

package utils

import (
	"bytes"
	"strings"
)

func FilterSlice[T any](lst []T, f func(T) bool) []T {
	if f == nil {
		return lst
	}
	ret := []T{}
	for _, i := range lst {
		if f(i) {
			ret = append(ret, i)
		}
	}
	return ret
}

// IsOneLine detects if `in` is a single line, which is true when there aren't any `\n` characters except an optional trailing on.
func IsOneLine[T []byte | string](in T) bool {
	if len(in) == 0 {
		return true
	}
	in2 := []byte(in[:len(in)-1]) // Ignore any trailing newline, and handle the string|[]byte generics
	return !bytes.ContainsRune(in2, '\n')
}

// TrimOneLine trims spaces from `in` IFF `in` is considered `IsOneLine()`.
func TrimOneLine[T []byte | string](in T) T {
	if IsOneLine(in) {
		switch v := any(in).(type) {
		case string:
			in = T(strings.TrimSpace(v))
		case []byte:
			in = T(bytes.TrimSpace(v))
		}
	}
	return in
}

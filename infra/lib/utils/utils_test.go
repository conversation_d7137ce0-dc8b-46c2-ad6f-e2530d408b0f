package utils

import (
	"testing"

	"github.com/google/go-cmp/cmp"
)

func TestOneLine(t *testing.T) {
	tests := map[string]struct {
		in   string
		wb   bool
		want string
	}{
		"empty/nil": {
			in:   "",
			wb:   true,
			want: "",
		},
		"no-change": {
			in:   "abc",
			wb:   true,
			want: "abc",
		},
		"trailing-newline": {
			in:   "abc\n",
			wb:   true,
			want: "abc",
		},
		"strip-trailing-newline": {
			in:   " abc \n",
			wb:   true,
			want: "abc",
		},
		"multiline": {
			in:   " abc \n def ",
			wb:   false,
			want: " abc \n def ",
		},
		"multiline-trailing-newline": {
			in:   " abc \n def \n",
			wb:   false,
			want: " abc \n def \n",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			// Strings
			if got, want := IsOneLine(tc.in), tc.wb; got != want {
				t.Errorf("IsOneLine[string]: got %v, want %v.", got, want)
			}
			if diff := cmp.Diff(tc.want, TrimOneLine(tc.in)); diff != "" {
				t.Errorf("TrimOneLine[string]: -want +got\n%s", diff)
			}

			// Bytes
			bin, bwant := []byte(tc.in), []byte(tc.want)
			if len(bin) == 0 {
				bin = nil
			}
			if len(bwant) == 0 {
				bwant = nil
			}
			if got, want := IsOneLine(bin), tc.wb; got != want {
				t.Errorf("IsOneLine[bytes]: got %v, want %v.", got, want)
			}
			if diff := cmp.Diff(bwant, TrimOneLine(bin)); diff != "" {
				t.Errorf("TrimOneLine[bytes]: -want +got\n%s", diff)
			}
		})
	}
}

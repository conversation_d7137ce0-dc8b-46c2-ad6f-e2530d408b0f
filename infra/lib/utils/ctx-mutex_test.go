package utils

import (
	"context"
	"errors"
	"fmt"
	"testing"
)

func TestCtxMutex(t *testing.T) {
	ctx := context.Background()
	mu := NewCtxMutex()

	if got, want := mu.Locked(), false; got != want {
		t.<PERSON><PERSON><PERSON>("Locked() [init]: got %v, want %v.", got, want)
	}

	if err := mu.Lock(ctx); err != nil {
		t.<PERSON><PERSON><PERSON>("Lock() [init] error: %v.", err)
	}

	if got, want := mu.Locked(), true; got != want {
		t.<PERSON><PERSON><PERSON>("Locked() [post-lock]: got %v, want %v.", got, want)
	}

	mu.UnLock()

	if got, want := mu.Locked(), false; got != want {
		t.<PERSON>rrorf("Locked() [post-unlock]: got %v, want %v.", got, want)
	}

	if err := mu.Lock(ctx); err != nil {
		t.<PERSON><PERSON>("Lock() [again] error: %v.", err)
	}

	wantErr := fmt.Errorf("test error")

	ctx2, cancel := context.WithCancelCause(ctx)
	cancel(wantErr)
	if gotErr := mu.Lock(ctx2); !errors.Is(gotErr, wantErr) {
		t.Errorf("Lock() [contention] error got: %v, want %v.", gotErr, wantErr)
	}
}

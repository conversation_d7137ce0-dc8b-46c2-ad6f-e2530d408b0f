load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(default_visibility = ["//infra:internal"])

go_library(
    name = "utils",
    srcs = [
        "ctx-mutex.go",
        "utils.go",
    ],
    importpath = "github.com/augmentcode/augment/infra/lib/utils",
)

go_test(
    name = "utils_test",
    srcs = [
        "ctx-mutex_test.go",
        "utils_test.go",
    ],
    embed = [":utils"],
    deps = ["@com_github_google_go_cmp//cmp"],
)

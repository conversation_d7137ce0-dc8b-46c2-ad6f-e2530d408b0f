package github

import (
	"context"
	"crypto/rsa"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/go-github/v69/github"

	"github.com/augmentcode/augment/infra/lib/utils"
)

// TokenGenerator does two things: 1) formats JWT tokens using a GitHub App ID
// and RSA Private Key; 2) exchanges said JWT tokens for App Installation
// Authorization Tokens using a GitHub App Installation ID.
//
// The New* function accepts inputs as any of: a) proper native types; b)
// strings, which are then parsed to the native types; or c) filepaths leading
// with '/' or './' which are read and parsed to the native types.
//
// A thread-safe cache is used for the App Installation Auth Tokens.
//
// JWTs are generated locally (cheap) and so can have very tight expirations.
// App Installation Authorization Tokens are resources generated within the
// GitHub API and come with a longer expiration (GH docs say always 1 hour).
// Therefor, Auth Tokens are cached and reused until their expiration.
//
// Backend IO is swappable for testing (ReadFile(), Now(), ...).
type TokenGenerator struct {
	ghAppID     string
	ghInstallID int64
	privkey     *rsa.PrivateKey

	jwtIatPadding  time.Duration
	jwtExpLifetime time.Duration

	github *github.Client

	tokCache     string
	tokExpiresAt time.Time
	tokLk        utils.CtxMutex

	// Swappable for testing.
	tReadFile        func(string) ([]byte, error)
	tNow             func() time.Time
	tCreateInstToken func(context.Context, int64, *github.InstallationTokenOptions) (*github.InstallationToken, *github.Response, error)
}

// TokenGeneratorOpt can be used with NewTokenGenerator() and/or to an existing
// TokenGenerator.
type TokenGeneratorOpt func(*TokenGenerator)

// OptIatPadding is a small duration removed when setting the IssuedAt ('iat')
// JWT claim, to help with clock skew among systems. The default is 1 minute.
func OptIatPadding(d time.Duration) TokenGeneratorOpt {
	return func(tg *TokenGenerator) {
		tg.jwtIatPadding = d
	}
}

// OptExpLifetime is added to "now" for the ExpiresAt ('exp') JWT claim. The
// default is 2 minutes. JWTs are generated locally
func OptExpLifetime(d time.Duration) TokenGeneratorOpt {
	return func(tg *TokenGenerator) {
		tg.jwtExpLifetime = d
	}
}

// OptReadFile swaps out the default os.ReadFile() implementation when handling
// any of the input args as a filename.
func OptReadFile(f func(string) ([]byte, error)) TokenGeneratorOpt {
	return func(tg *TokenGenerator) {
		tg.tReadFile = f
	}
}

// OptNow swaps out the default time.Now() implementation for a) setting JWT
// claim timestamps and b) checking auth token expiration.
func OptNow(f func() time.Time) TokenGeneratorOpt {
	return func(tg *TokenGenerator) {
		tg.tNow = f
	}
}

func OptCreateInstToken(f func(context.Context, int64, *github.InstallationTokenOptions) (*github.InstallationToken, *github.Response, error)) TokenGeneratorOpt {
	return func(tg *TokenGenerator) {
		tg.tCreateInstToken = f
	}
}

// NewTokenGenerator accepts inputs as any of: a) proper native types; b)
// strings, which are then parsed to the native types; or c) filepaths leading
// with '/' or './' which are read and parsed to the native types.
func NewTokenGenerator[T uint64 | string, U int64 | string, V []byte | string](ghAppID T, ghInstallID U, privkeyPEM V, opts ...TokenGeneratorOpt) (*TokenGenerator, error) {
	/// Initialize, with input and defaults.

	tg := &TokenGenerator{
		jwtIatPadding:  time.Minute,
		jwtExpLifetime: 2 * time.Minute,
		tokLk:          utils.NewCtxMutex(),
		tReadFile:      os.ReadFile,
		tNow:           time.Now,
	}
	tg.github = github.NewClient(&http.Client{Transport: tg})
	tg.tCreateInstToken = tg.github.Apps.CreateInstallationToken

	/// Apply Opts

	for _, o := range opts {
		o(tg)
	}

	/// App ID

	switch v := any(ghAppID).(type) {
	case uint64:
		tg.ghAppID = strconv.FormatUint(v, 10)
	case string:
		if strings.HasPrefix(v, "/") || strings.HasPrefix(v, "./") {
			if buf, err := tg.tReadFile(v); err != nil {
				return nil, fmt.Errorf("ghAppID: %w", err)
			} else {
				v = string(buf)
			}
		}
		if v == "" {
			return nil, fmt.Errorf("ghAppID is empty")
		}
		tg.ghAppID = v
	}

	/// Installation ID

	switch v := any(ghInstallID).(type) {
	case int64:
		tg.ghInstallID = v
	case string:
		if strings.HasPrefix(v, "/") || strings.HasPrefix(v, "./") {
			if buf, err := tg.tReadFile(v); err != nil {
				return nil, fmt.Errorf("ghInstallID: %w", err)
			} else {
				v = string(buf)
			}
		}
		var err error
		if tg.ghInstallID, err = strconv.ParseInt(v, 10, 64); err != nil {
			return nil, fmt.Errorf("ghInstallID %s: %w", v, err)
		}
	}

	/// App Private Key

	privkeyBuf := []byte{}
	switch v := any(privkeyPEM).(type) {
	case []byte:
		privkeyBuf = v
	case string:
		if strings.HasPrefix(v, "/") || strings.HasPrefix(v, "./") {
			if buf, err := tg.tReadFile(v); err != nil {
				return nil, fmt.Errorf("privkeyPEM: %w", err)
			} else {
				privkeyBuf = buf // pragma: allowlist secret
			}
		} else {
			privkeyBuf = []byte(v)
		}
	}
	if key, err := jwt.ParseRSAPrivateKeyFromPEM(privkeyBuf); err != nil {
		return nil, err
	} else {
		tg.privkey = key // pragma: allowlist secret
	}

	return tg, nil
}

// FormatJWT uses the generator's saved App ID and RSA Private Key to format a JWT.
func (tg TokenGenerator) FormatJWT() (string, error) {
	now := tg.tNow()
	tok := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.RegisteredClaims{
		Issuer:    tg.ghAppID,
		IssuedAt:  jwt.NewNumericDate(now.Add(-tg.jwtIatPadding)),
		ExpiresAt: jwt.NewNumericDate(now.Add(tg.jwtExpLifetime)),
	})
	return tok.SignedString(tg.privkey)
}

// GetAccessTokenNoCache exchanges the JWT from `FormatJWT()` for a GitHub App
// Installation Access Token. Most callers should use `GetAccessToken()` to
// reuse unexpired tokens.
func (tg TokenGenerator) GetAccessTokenNoCache(ctx context.Context) (string, time.Time, error) {
	tok, _, err := tg.tCreateInstToken(ctx, tg.ghInstallID, nil)
	if err != nil {
		return "", time.Time{}, err
	}

	var tokstr string
	var exp time.Time

	if tokstr = tok.GetToken(); tokstr == "" {
		return "", time.Time{}, fmt.Errorf("empty token received: %+v", tok)
	}

	tsw := tok.GetExpiresAt()
	if ts := tsw.GetTime(); ts == nil || (*ts).IsZero() {
		return "", time.Time{}, fmt.Errorf("token with no expiration received: %+v", tok)
	} else {
		exp = *ts
	}

	return tokstr, exp, nil
}

// GetAccessTokenNoCache exchanges the JWT from `FormatJWT()` for a GitHub App
// Installation Access Token. The generator caches and reuses unexpired tokens.
func (tg *TokenGenerator) GetAccessToken(ctx context.Context) (string, time.Time, error) {
	if err := tg.tokLk.Lock(ctx); err != nil {
		return "", time.Time{}, err
	}
	defer tg.tokLk.UnLock()

	if !tg.tNow().Before(tg.tokExpiresAt) {
		tok, exp, err := tg.GetAccessTokenNoCache(ctx)
		if err != nil {
			return "", time.Time{}, err
		}
		tg.tokCache = tok
		tg.tokExpiresAt = exp
	}

	return tg.tokCache, tg.tokExpiresAt, nil
}

// GetAccessTokenForRepoNoCache exchanges the JWT from `FormatJWT()` for a GitHub App Installation Access Token by looking
// up the Installation ID from a repo.
func (tg TokenGenerator) GetAccessTokenForRepoNoCache(ctx context.Context, org, repo string) (string, time.Time, error) {
	inst, _, err := tg.github.Apps.FindRepositoryInstallation(ctx, org, repo)
	if err != nil {
		return "", time.Time{}, err
	}

	tok, _, err := tg.tCreateInstToken(ctx, inst.GetID(), nil)
	if err != nil {
		return "", time.Time{}, err
	}

	var tokstr string
	var exp time.Time

	if tokstr = tok.GetToken(); tokstr == "" {
		return "", time.Time{}, fmt.Errorf("empty token received: %+v", tok)
	}

	tsw := tok.GetExpiresAt()
	if ts := tsw.GetTime(); ts == nil || (*ts).IsZero() {
		return "", time.Time{}, fmt.Errorf("token with no expiration received: %+v", tok)
	} else {
		exp = *ts
	}

	return tokstr, exp, nil
}

// RoundTrip implements the http.RoundTripper interface. The TokenGenerator can
// be used as an http.Client Transport middleware to add the 'Authorization:
// Bearer {FormatJWT()}' header. (This is used internally for `GetAccessToken()`
// auth.).
func (tg TokenGenerator) RoundTrip(r *http.Request) (*http.Response, error) {
	jwt, err := tg.FormatJWT()
	if err != nil {
		return nil, err
	}
	r.Header.Add("Authorization", "Bearer "+jwt)
	return http.DefaultTransport.RoundTrip(r)
}

// InstallationRoundTripper returns an `http.RoundTripper` which injects a (cached)
// App Installation Token into the 'Authorization' header.
func (tg *TokenGenerator) InstallationRoundTripper() http.RoundTripper {
	return &instRoundTripper{tg: tg}
}

type instRoundTripper struct {
	tg *TokenGenerator
}

func (irt *instRoundTripper) RoundTrip(r *http.Request) (*http.Response, error) {
	tok, _, err := irt.tg.GetAccessToken(r.Context())
	if err != nil {
		return nil, err
	}
	r.Header.Add("Authorization", "Bearer "+tok)
	return http.DefaultTransport.RoundTrip(r)
}

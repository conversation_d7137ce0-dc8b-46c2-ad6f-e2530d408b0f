package github

import (
	"net/http"
	"os"
	"strings"

	"github.com/google/go-github/v69/github"

	"github.com/augmentcode/augment/infra/lib/logger"
)

type Client struct {
	logger.Logger
	gh    *github.Client
	owner string
	repo  string
}

func NewPlainClient() Client {
	return Client{
		Logger: logger.New(nil),
		gh:     github.NewClient(nil),
	}
}

// NewClient builds a new `Client` with an auth token. If `tok` begins with
// "name://", it is taken as the name of a file in $CONFIG/github/<name>.tok
// containing the token.
func NewClient(owner, repo, tok string) Client {
	if name, found := strings.CutPrefix(tok, "name://"); found {
		if cfgdir, err := os.UserConfigDir(); err == nil {
			fname := cfgdir + "/github/" + name + ".tok"
			if buf, err := os.ReadFile(fname); err == nil {
				tok = strings.TrimSpace(string(buf))
			}
		}
	}
	return Client{
		Logger: logger.New(nil),
		gh:     github.NewClient(nil).WithAuthToken(tok),
		owner:  owner,
		repo:   repo,
	}
}

// NewAppInstClient builds a client which dynamically refreshes an app installation token as needed.
func NewAppInstClient[T uint64 | string, U int64 | string, V []byte | string](owner, repo string, appID T, instID U, privkeyPEM V) (*Client, error) {
	tg, err := NewTokenGenerator(appID, instID, privkeyPEM)
	if err != nil {
		return nil, err
	}
	return &Client{
		Logger: logger.New(nil),
		gh:     github.NewClient(&http.Client{Transport: tg.InstallationRoundTripper()}),
		owner:  owner,
		repo:   repo,
	}, nil
}

package github

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/google/go-github/v69/github"
)

func (c Client) UserSSHKeys(ctx context.Context, user string) ([]*github.Key, *github.Response, error) {
	keys, resp, err := c.gh.Users.ListKeys(ctx, user, nil)
	if err != nil {
		return nil, resp, err
	}
	if resp.StatusCode != http.StatusOK {
		return nil, resp, fmt.Errorf("ListKeys(%s): %d/%s", user, resp.StatusCode, resp.Status)
	}
	return keys, resp, nil
}

// UserSSHAuthorizedKeys wraps `UserSSHKeys()` and returns a multi-line `authorized_keys`-style string.
func (c Client) UserSSHAuthorizedKeys(ctx context.Context, user string) (string, *github.Response, error) {
	keys, resp, err := c.User<PERSON>ey<PERSON>(ctx, user)
	if err != nil {
		return "", resp, err
	}
	lines := []string{}
	for _, key := range keys {
		if key.Key == nil {
			continue
		}
		lines = append(lines, *key.Key)
	}
	return strings.Join(lines, "\n"), resp, nil
}

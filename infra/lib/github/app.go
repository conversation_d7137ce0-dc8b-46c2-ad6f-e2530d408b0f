package github

import (
	"context"

	"github.com/google/go-github/v69/github"
)

func (c Client) AppOrgInstallation(ctx context.Context, org string) (*github.Installation, *github.Response, error) {
	return c.gh.Apps.FindOrganizationInstallation(ctx, org)
}

func (c Client) AppRepoInstallation(ctx context.Context, org, repo string) (*github.Installation, *github.Response, error) {
	return c.gh.Apps.FindRepositoryInstallation(ctx, org, repo)
}

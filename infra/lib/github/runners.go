package github

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"sort"
	"strings"

	"github.com/google/go-github/v69/github"

	"github.com/augmentcode/augment/infra/lib/utils"
)

type RunnerFilter func(*github.Runner) bool

func StatusFilter(status string) RunnerFilter {
	return func(r *github.Runner) bool {
		return strings.ToLower(r.GetStatus()) == strings.ToLower(status)
	}
}

func NamePrefixFilter(pfx string) RunnerFilter {
	return func(r *github.Runner) bool {
		return strings.HasPrefix(r.GetName(), pfx)
	}
}

func RunnerStatusLine(r *github.Runner) string {
	busy := "idle"
	if r.GetBusy() {
		busy = "busy"
	}
	return fmt.Sprintf("[%d] %s %s %s", r.Get<PERSON>(), r.<PERSON>(), r.GetStatus(), busy)
}

func (c Client) Runners(ctx context.Context, fs ...RunnerFilter) ([]*github.Runner, *github.Response, error) {
	runners, resp, err := c.gh.Actions.ListRunners(ctx, c.owner, c.repo, &github.ListRunnersOptions{
		ListOptions: github.ListOptions{
			PerPage: 512,
		},
	})
	if err != nil {
		return nil, resp, err
	}
	if resp.StatusCode != http.StatusOK {
		return nil, resp, fmt.Errorf("ListRunners(): %d/%s", resp.StatusCode, resp.Status)
	}
	for _, f := range fs {
		runners.Runners = utils.FilterSlice(runners.Runners, f)
	}
	sort.Slice(runners.Runners, func(i, j int) bool {
		return runners.Runners[i].GetName() < runners.Runners[j].GetName()
	})
	return runners.Runners, resp, nil
}

func (c Client) RunnerRemove(ctx context.Context, id int64) (*github.Response, error) {
	resp, err := c.gh.Actions.RemoveRunner(ctx, c.owner, c.repo, id)
	if err != nil {
		return resp, err
	}
	if resp.StatusCode != http.StatusNoContent {
		return resp, fmt.Errorf("RunnerRemove: %d/%s", resp.StatusCode, resp.Status)
	}
	return resp, nil
}

func (c Client) RunnersPurgeOffline(ctx context.Context, dryRun bool, fs ...RunnerFilter) error {
	fs = append(fs, StatusFilter("offline"))
	runners, _, err := c.Runners(ctx, fs...)
	if err != nil {
		return err
	}

	if l := len(runners); l == 0 {
		c.LogInfo("Found %d Runners.", l)
		return nil
	} else {
		c.LogInfo("Removing %d Runners:", l)
	}

	if dryRun {
		for _, r := range runners {
			c.LogInfo("[DryRun] Not Deleting Runner: %s.", RunnerStatusLine(r))
		}
		return nil
	}

	errs := []error{}
	for _, r := range runners {
		c.LogInfo("Deleting Runner: %s...", RunnerStatusLine(r))
		if _, err := c.RunnerRemove(ctx, r.GetID()); err != nil {
			c.LogErr("Error Deleting Runner %d/%s: %v.", r.GetID(), r.GetName(), err)
			errs = append(errs, err)
		} else {
			c.LogInfo("Deleted Runner %d/%s.", r.GetID(), r.GetName())
		}
	}

	return errors.Join(errs...)
}

load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(default_visibility = ["//infra:internal"])

go_library(
    name = "github",
    srcs = [
        "app.go",
        "download.go",
        "github.go",
        "runners.go",
        "token.go",
        "users.go",
    ],
    importpath = "github.com/augmentcode/augment/infra/lib/github",
    deps = [
        "//infra/lib/logger",
        "//infra/lib/utils",
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@com_github_google_go_github_v69//github",
    ],
)

go_test(
    name = "github_test",
    srcs = ["token_test.go"],
    embed = [":github"],
    deps = [
        "@com_github_google_go_github_v69//github",
        "@com_github_jarcoal_httpmock//:httpmock",
    ],
)

package github

import (
	"context"
	"encoding/base64"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/google/go-github/v69/github"
	"github.com/jarcoal/httpmock"
)

// pragma: allowlist nextline secret
const aPrivateKey = `-----BEGIN RSA PRIVATE K3Y-----
MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDFMIz1kYMRK2Ej
EePbb1q/r0hUboAXyMAYFevPwJUwDbmUxtLrmyDNVUBqYxcObl6H5U166FBBoefo
JIrGpLhxMyKvYCi8oPkJWrrPXw9uHuG5L0Ktx+4cbT08NO3gEAPWjPbsfGKEHbLD
9u+xtwQogkEC6oNbyfFD1iGEP6ek2HSJVqq8Z/jLBrYGR0U/0IPhh7cLEEW4pS1c
6eavVXUosmKc4rcBuqlSrgXpldaOPH6EQTF/OQfU/uhVWW61G1Rkj2qOIVu23juf
OHNfyCzP6de021pkr5bJx2omJxYPiHMGw9/YJJdmduEpYXMgZ+PBoP1qd8fxYGm/
/LSbXmvtAgMBAAECggEAQuu+sup+yjloHnBIXaTVbf9NM0ZLF4dMVgA+hsOrfeLy
jADL/SSpBbZ8hX56iS3AzCrwWBl2f0e0vg6EwHSPme9KQq2GNboFunx9sLnosS0u
2tukq/iRoTkbko69+hBFVS+C73ApOQAhNqtb3nqnoR3PdW9kRCbtR+HofwrhWKp6
pMWpLLlEDSKqqCn8Uz5HSKgdLlA+nzTq8VbFv/iOLFI54Hpt0zAsqWFZe5fwSmnw
l0vmhpIg0qN3oUuPhwtx0ZrcIH03CohdHD6StoRMJ9i9jy7C5ZvdrfvHq3DEiUZR
dXAGunB1zv34mY/I/zk+FTxlDoBJCnD31u/MKofIywKBgQDiRF1W0Nwiun1GLb03
ceCIfstMBQA6dthUvKLQ/LrJzIbTXzm+s37eG18WEN27pc427DuJgm4Eb1go1I27
3KpkcXrKk99oJEyMDxCgGCc/zpi9JPt/ItoACWmMNO1Q8ks/510JJ0Tndipk0Gcf
kH9Dc0+7kSGxTee5a6eDAvzWPwKBgQDfGgVT3qB+Vh4/G9BMty5R5U0Q+FxhDxqW
wFYas6RvGFNV+2BH3ImjRRjjL6quFRqwKNvEfgr/qLSTAhRqSP3+fesGaaXJ+m5I
/A9SK8HKaP6VcxcHYGVqKdJncyypC+sMzwKbhinoIBdEo4MjuH2BNMPzfujgC0Bb
BK6RXEyq0wKBgQCTP95mk0HPsAG63CVZodh3u9xdupttcM5URvbGftKoZVwQztXr
jzCvJqz41T0X002iXJZaZPFoHXYIDFu4uGCDQX6hux4WzhiqxVsSdOWGFtM7tIef
M3UftQBlaERQ+32RNuZLAK1M3I+2UBHsbsMDiFWJQWfl1UdKWg8HqbgVTQKBgQCf
b6YNCNVjU8Hr0cGrgWFNz3OixAqopjWXND6UeHrzlcvAb5Of52BJWNDvUppEK/rV
l21u37CWggcB5AOY7S7u1yfFfmynihuUduWwEGs+bog5X3U+pUn0c4zUtz9cU5MX
pTy0zk1WgNCAEeZe+bJfSskaU0CzE/da1F64rOBxVwKBgQCjGUCZhsmmjLJvpSYh
pN/m35C3ROkVEJ47v1Dwm7TCmNQpwCMg5mbNRkpyj0SGhTM9UPtiW3wIG8DA2as3
RT7Nl7r0lhGov1+S7URoYSvuMscrnvx/u08MqL+lQHe9ggq1anK/tHqD/Pqk037H
IDkv5ei9fr35W1uyritklth19Q==
-----END RSA PRIVATE K3Y-----`

func TestNewTokenGeneratorNative(t *testing.T) {
	tests := map[string]struct {
		inAppID    uint64
		inInstID   int64
		inKey      []byte
		inOpts     []TokenGeneratorOpt
		wantErr    string
		wantAppID  string
		wantInstID int64
		wantIat    time.Duration
		wantExp    time.Duration
	}{
		"success": {
			inAppID:    7,
			inInstID:   42,
			inKey:      []byte(aPrivateKey),
			inOpts:     nil,
			wantErr:    "",
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
		},
		"empty-key": {
			inAppID:    7,
			inInstID:   42,
			inKey:      []byte{},
			inOpts:     nil,
			wantErr:    "invalid key",
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
		},
		"invalid-key": {
			inAppID:    7,
			inInstID:   42,
			inKey:      []byte("__bad_key__"),
			inOpts:     nil,
			wantErr:    "invalid key",
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
		},
		"opts": {
			inAppID:    7,
			inInstID:   42,
			inKey:      []byte(aPrivateKey),
			wantErr:    "",
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Second,
			wantExp:    time.Hour,
			inOpts: []TokenGeneratorOpt{
				OptIatPadding(time.Second),
				OptExpLifetime(time.Hour),
				OptReadFile(nil),
				OptNow(nil),
				OptCreateInstToken(nil),
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			tg, gotErr := NewTokenGenerator(tc.inAppID, tc.inInstID, tc.inKey, tc.inOpts...)
			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got err %v, want no err.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want err containing '%v'.", gotErr, tc.wantErr)
			}
			if tg == nil {
				return
			}

			if got, want := tg.ghAppID, tc.wantAppID; got != want {
				t.Errorf("app_id: got %v, want %v.", got, want)
			}
			if got, want := tg.ghInstallID, tc.wantInstID; got != want {
				t.Errorf("installation_id: got %v, want %v.", got, want)
			}
			if got, want := tg.jwtIatPadding, tc.wantIat; got != want {
				t.Errorf("iat padding: got %v, want %v.", got, want)
			}
			if got, want := tg.jwtExpLifetime, tc.wantExp; got != want {
				t.Errorf("exp lifetime: got %v, want %v.", got, want)
			}
		})
	}
}

func TestNewTokenGeneratorStrings(t *testing.T) {
	tests := map[string]struct {
		inAppID    string
		inInstID   string
		inKey      string
		inOpts     []TokenGeneratorOpt
		wantErr    string
		wantAppID  string
		wantInstID int64
		wantIat    time.Duration
		wantExp    time.Duration
	}{
		"success-literals": {
			inAppID:    "7",
			inInstID:   "42",
			inKey:      aPrivateKey,
			inOpts:     nil,
			wantErr:    "",
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
		},
		"appid-as-clientid": {
			inAppID:    "XyZ123AbC",
			inInstID:   "42",
			inKey:      aPrivateKey,
			inOpts:     nil,
			wantErr:    "",
			wantAppID:  "XyZ123AbC",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
		},
		"invalid-instid-literal": {
			inAppID:    "7",
			inInstID:   "forty-two",
			inKey:      aPrivateKey,
			inOpts:     nil,
			wantErr:    `parsing "forty-two": invalid syntax`,
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
		},
		"invalid-privkey-literal": {
			inAppID:    "7",
			inInstID:   "42",
			inKey:      "__bad_key__",
			inOpts:     nil,
			wantErr:    "invalid key",
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
		},
		"empty-appid-literal": {
			inAppID:    "",
			inInstID:   "42",
			inKey:      aPrivateKey,
			inOpts:     nil,
			wantErr:    "ghAppID",
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
		},
		"empty-instid-literal": {
			inAppID:    "7",
			inInstID:   "",
			inKey:      aPrivateKey,
			inOpts:     nil,
			wantErr:    "ghInstallID",
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
		},
		"empty-key-literal": {
			inAppID:    "7",
			inInstID:   "42",
			inKey:      "",
			inOpts:     nil,
			wantErr:    "invalid key",
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
		},
		"success-fromfile-rel": {
			inAppID:    "./t_appid",
			inInstID:   "./t_instid",
			inKey:      "./t_privkey",
			wantErr:    "",
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
			inOpts: []TokenGeneratorOpt{
				OptReadFile(func(fname string) ([]byte, error) {
					switch fname {
					case "./t_appid":
						return []byte("7"), nil
					case "./t_instid":
						return []byte("42"), nil
					case "./t_privkey":
						return []byte(aPrivateKey), nil
					default:
						return nil, nil
					}
				}),
			},
		},
		"success-fromfile-abs": {
			inAppID:    "/t_appid",
			inInstID:   "/t_instid",
			inKey:      "/t_privkey",
			wantErr:    "",
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
			inOpts: []TokenGeneratorOpt{
				OptReadFile(func(fname string) ([]byte, error) {
					switch fname {
					case "/t_appid":
						return []byte("7"), nil
					case "/t_instid":
						return []byte("42"), nil
					case "/t_privkey":
						return []byte(aPrivateKey), nil
					default:
						return nil, nil
					}
				}),
			},
		},
		"success-fromfile-appid-as-clientid": {
			inAppID:    "/t_appid",
			inInstID:   "/t_instid",
			inKey:      "/t_privkey",
			wantErr:    "",
			wantAppID:  "aBc123xYz",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
			inOpts: []TokenGeneratorOpt{
				OptReadFile(func(fname string) ([]byte, error) {
					switch fname {
					case "/t_appid":
						return []byte("aBc123xYz"), nil
					case "/t_instid":
						return []byte("42"), nil
					case "/t_privkey":
						return []byte(aPrivateKey), nil
					default:
						return nil, nil
					}
				}),
			},
		},
		"success-fromfile-invalid-instid": {
			inAppID:    "/t_appid",
			inInstID:   "/t_instid",
			inKey:      "/t_privkey",
			wantErr:    `parsing "forty-two": invalid syntax`,
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
			inOpts: []TokenGeneratorOpt{
				OptReadFile(func(fname string) ([]byte, error) {
					switch fname {
					case "/t_appid":
						return []byte("7"), nil
					case "/t_instid":
						return []byte("forty-two"), nil
					case "/t_privkey":
						return []byte(aPrivateKey), nil
					default:
						return nil, nil
					}
				}),
			},
		},
		"success-fromfile-invalid-privkey": {
			inAppID:    "/t_appid",
			inInstID:   "/t_instid",
			inKey:      "/t_privkey",
			wantErr:    "invalid key",
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
			inOpts: []TokenGeneratorOpt{
				OptReadFile(func(fname string) ([]byte, error) {
					switch fname {
					case "/t_appid":
						return []byte("7"), nil
					case "/t_instid":
						return []byte("42"), nil
					case "/t_privkey":
						return []byte("__bad_key__"), nil
					default:
						return nil, nil
					}
				}),
			},
		},
		"success-fromfile-fileio-err-appid": {
			inAppID:    "/t_appid",
			inInstID:   "/t_instid",
			inKey:      "/t_privkey",
			wantErr:    "appid io error",
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
			inOpts: []TokenGeneratorOpt{
				OptReadFile(func(fname string) ([]byte, error) {
					switch fname {
					case "/t_appid":
						return []byte("-7"), fmt.Errorf("appid io error")
					case "/t_instid":
						return []byte("42"), nil
					case "/t_privkey":
						return []byte(aPrivateKey), nil
					default:
						return nil, nil
					}
				}),
			},
		},
		"success-fromfile-fileio-err-instid": {
			inAppID:    "/t_appid",
			inInstID:   "/t_instid",
			inKey:      "/t_privkey",
			wantErr:    "instid io error",
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
			inOpts: []TokenGeneratorOpt{
				OptReadFile(func(fname string) ([]byte, error) {
					switch fname {
					case "/t_appid":
						return []byte("7"), nil
					case "/t_instid":
						return []byte("forty-two"), fmt.Errorf("instid io error")
					case "/t_privkey":
						return []byte(aPrivateKey), nil
					default:
						return nil, nil
					}
				}),
			},
		},
		"success-fromfile-fileio-err-privkey": {
			inAppID:    "/t_appid",
			inInstID:   "/t_instid",
			inKey:      "/t_privkey",
			wantErr:    "privkey io error",
			wantAppID:  "7",
			wantInstID: 42,
			wantIat:    time.Minute,
			wantExp:    2 * time.Minute,
			inOpts: []TokenGeneratorOpt{
				OptReadFile(func(fname string) ([]byte, error) {
					switch fname {
					case "/t_appid":
						return []byte("7"), nil
					case "/t_instid":
						return []byte("42"), nil
					case "/t_privkey":
						return []byte("__bad_key__"), fmt.Errorf("privkey io error")
					default:
						return nil, nil
					}
				}),
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			tg, gotErr := NewTokenGenerator(tc.inAppID, tc.inInstID, tc.inKey, tc.inOpts...)
			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got err %v, want no err.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want err containing '%v'.", gotErr, tc.wantErr)
			}
			if tg == nil {
				return
			}

			if got, want := tg.ghAppID, tc.wantAppID; got != want {
				t.Errorf("app_id: got %v, want %v.", got, want)
			}
			if got, want := tg.ghInstallID, tc.wantInstID; got != want {
				t.Errorf("installation_id: got %v, want %v.", got, want)
			}
			if got, want := tg.jwtIatPadding, tc.wantIat; got != want {
				t.Errorf("iat padding: got %v, want %v.", got, want)
			}
			if got, want := tg.jwtExpLifetime, tc.wantExp; got != want {
				t.Errorf("exp lifetime: got %v, want %v.", got, want)
			}
		})
	}
}

func TestFormatJWT(t *testing.T) {
	tests := map[string]struct {
		inAppID     uint64
		inInstID    int64
		inKey       []byte
		inOpts      []TokenGeneratorOpt
		wantErr     string
		wantHeader  string
		wantPayload string
		wantSig     string
	}{
		"success": {
			inAppID:     7,
			inInstID:    42,
			inKey:       []byte(aPrivateKey),
			wantErr:     "",
			wantHeader:  `{"alg":"RS256","typ":"JWT"}`,
			wantPayload: `{"iss":"7","exp":3720,"iat":3540}`,
			wantSig:     "hRyT9CzlqKb6fU5ElsSbvUzjfmA2NbTGVW4ypU_qz7UVeFSfWFRMlO6SqfMDpBwoRqiFe0VmDeo-c3xlrOLzaW_gsm5dF0hD4gAF1rfJzN0N7M_oFC6SSBeLt1CnOL5hKMxjAS5C-GJngoR0xKMbeLRm3O4THDYgJvv9DapvxojWgtVViIEywkfN2oEjKJuI1xCJQDz6a-fDOJQBnU3uhyvj4y4HlaUFGJZ10oatvaaLUN4USxaGpmBOv2hdZeg-bZIHO_kLbjY6Ukx8I3XfKhgM8BJvhAOByzGtLBtauSc-jwSZWoYUGUSIuR1QRiqm33BgRczCp_f18AxjcPlOkg",
			inOpts: []TokenGeneratorOpt{
				OptNow(func() time.Time {
					return time.Unix(3600, 0)
				}),
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			tg, err := NewTokenGenerator(tc.inAppID, tc.inInstID, tc.inKey, tc.inOpts...)
			if err != nil {
				t.Fatalf("failed to build TokenGenerator for test: %v.", err)
			}

			gotJWT, gotErr := tg.FormatJWT()
			gotHeader, gotNext, _ := strings.Cut(gotJWT, ".")
			gotPayload, gotSig, _ := strings.Cut(gotNext, ".")

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got err %v, want no err.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want err containing '%v'.", gotErr, tc.wantErr)
			}

			// NOTE(mattm): Comparing raw json strings is lazy and
			// may need to be unmarshaled if the test becomes flaky
			// due to ordering.

			if gotb, err := base64.StdEncoding.DecodeString(gotHeader); err != nil {
				t.Errorf("jwt header b64 error: %v.", err)
			} else if got, want := string(gotb), tc.wantHeader; got != want {
				t.Errorf("jwt header: got %v, want %v.", got, want)
			}
			if gotb, err := base64.StdEncoding.DecodeString(gotPayload); err != nil {
				t.Errorf("jwt payload b64 error: %v.", err)
			} else if got, want := string(gotb), tc.wantPayload; got != want {
				t.Errorf("jwt payload: got %v, want %v.", got, want)
			}
			if got, want := gotSig, tc.wantSig; got != want {
				t.Errorf("jwt signature: got %v, want %v.", got, want)
			}
		})
	}
}

// TestGestAccessTokenNoCache tests the access_token API using httpmock.
func TestGetAccessTokenNoCache(t *testing.T) {
	tests := map[string]struct {
		inAppID   uint64
		inInstID  int64
		inKey     []byte
		inOpts    []TokenGeneratorOpt
		mWantJWT  string
		mTok      string
		mTime     string
		mRespCode int
		wantErr   string
		wantTok   string
		wantExp   time.Time
	}{
		"success": {
			inAppID:   7,
			inInstID:  42,
			inKey:     []byte(aPrivateKey),
			mWantJWT:  "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", // pragma: allowlist secret
			mTok:      "tok0",
			mTime:     "1970-01-01T02:00:00Z",
			mRespCode: http.StatusCreated,
			wantErr:   "",
			wantTok:   "tok0",
			wantExp:   time.Unix(7200, 0),
			inOpts: []TokenGeneratorOpt{
				OptNow(func() time.Time {
					return time.Unix(3600, 0)
				}),
			},
		},
		"rpc-error": {
			inAppID:   7,
			inInstID:  42,
			inKey:     []byte(aPrivateKey),
			mWantJWT:  "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", // pragma: allowlist secret
			mTok:      "tok0",
			mTime:     "1970-01-01T02:00:00Z",
			mRespCode: http.StatusInternalServerError,
			wantErr:   "500",
			wantTok:   "",
			wantExp:   time.Time{},
			inOpts: []TokenGeneratorOpt{
				OptNow(func() time.Time {
					return time.Unix(3600, 0)
				}),
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			httpmock.Activate()
			t.Cleanup(httpmock.DeactivateAndReset)

			httpmock.RegisterResponder("POST", fmt.Sprintf("/app/installations/%d/access_tokens", tc.inInstID),
				func(r *http.Request) (*http.Response, error) {
					if got, want := r.Header.Get("Authorization"), "Bearer "+tc.mWantJWT; got != want {
						t.Errorf("authorization: got %v, want %v.", got, want)
					}
					body := fmt.Sprintf(`{
						"token": "%s",
						"expires_at": "%s"
					}`, tc.mTok, tc.mTime)
					return httpmock.NewStringResponder(tc.mRespCode, body)(r)
				},
			)

			tg, err := NewTokenGenerator(tc.inAppID, tc.inInstID, tc.inKey, tc.inOpts...)
			if err != nil {
				t.Fatalf("failed to build TokenGenerator for test: %v.", err)
			}

			ctx := context.Background()
			gotTok, gotExp, gotErr := tg.GetAccessTokenNoCache(ctx)

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got err %v, want no err.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want err containing '%v'.", gotErr, tc.wantErr)
			}

			if got, want := gotTok, tc.wantTok; got != want {
				t.Errorf("token: got %v, want %v.", got, want)
			}
			if got, want := gotExp, tc.wantExp; !got.Equal(want) {
				t.Errorf("expiration: got %v, want %v.", got, want)
			}
		})
	}
}

// TestGestAccessTokenNoCacheLibrary tests the access_token go library because
// it does some additonal things with pointers.
func TestGetAccessTokenNoCacheLibrary(t *testing.T) {
	stringP := func(s string) *string {
		x := s
		return &x
	}

	tests := map[string]struct {
		inAppID  uint64
		inInstID int64
		inKey    []byte
		inOpts   []TokenGeneratorOpt
		mWantID  int64
		mTok     *string
		mTime    *github.Timestamp
		mErr     error
		wantErr  string
		wantTok  string
		wantExp  time.Time
	}{
		"rpc-error": {
			inAppID:  7,
			inInstID: 42,
			inKey:    []byte(aPrivateKey),
			mWantID:  42,
			mTok:     nil,
			mTime:    nil,
			mErr:     fmt.Errorf("__rpc_error__"),
			wantErr:  "__rpc_error__",
			wantTok:  "",
			wantExp:  time.Time{},
			inOpts: []TokenGeneratorOpt{
				OptNow(func() time.Time {
					return time.Unix(3600, 0)
				}),
			},
		},
		"empty-token-error": {
			inAppID:  7,
			inInstID: 42,
			inKey:    []byte(aPrivateKey),
			mWantID:  42,
			mTok:     stringP(""),
			mTime:    nil,
			mErr:     nil,
			wantErr:  "empty token received",
			wantTok:  "",
			wantExp:  time.Time{},
			inOpts: []TokenGeneratorOpt{
				OptNow(func() time.Time {
					return time.Unix(3600, 0)
				}),
			},
		},
		"empty-token-nil-error": {
			inAppID:  7,
			inInstID: 42,
			inKey:    []byte(aPrivateKey),
			mWantID:  42,
			mTok:     nil,
			mTime:    nil,
			mErr:     nil,
			wantErr:  "empty token received",
			wantTok:  "",
			wantExp:  time.Time{},
			inOpts: []TokenGeneratorOpt{
				OptNow(func() time.Time {
					return time.Unix(3600, 0)
				}),
			},
		},
		"no-exp-error": {
			inAppID:  7,
			inInstID: 42,
			inKey:    []byte(aPrivateKey),
			mWantID:  42,
			mTok:     stringP("tok0"),
			mTime:    nil,
			mErr:     nil,
			wantErr:  "token with no expiration",
			wantTok:  "",
			wantExp:  time.Time{},
			inOpts: []TokenGeneratorOpt{
				OptNow(func() time.Time {
					return time.Unix(3600, 0)
				}),
			},
		},
		"success": {
			inAppID:  7,
			inInstID: 42,
			inKey:    []byte(aPrivateKey),
			mWantID:  42,
			mTok:     stringP("tok0"),
			mTime:    &github.Timestamp{Time: time.Unix(7200, 0)},
			mErr:     nil,
			wantErr:  "",
			wantTok:  "tok0",
			wantExp:  time.Unix(7200, 0),
			inOpts: []TokenGeneratorOpt{
				OptNow(func() time.Time {
					return time.Unix(3600, 0)
				}),
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			tg, err := NewTokenGenerator(tc.inAppID, tc.inInstID, tc.inKey, tc.inOpts...)
			if err != nil {
				t.Fatalf("failed to build TokenGenerator for test: %v.", err)
			}
			OptCreateInstToken(func(_ context.Context, gotID int64, _ *github.InstallationTokenOptions) (*github.InstallationToken, *github.Response, error) {
				if got, want := gotID, tc.mWantID; got != want {
					t.Errorf("create installation token called with id: got %v, want %v.", got, want)
				}
				return &github.InstallationToken{
					Token:     tc.mTok,
					ExpiresAt: tc.mTime,
				}, nil, tc.mErr
			})(tg)

			ctx := context.Background()
			gotTok, gotExp, gotErr := tg.GetAccessTokenNoCache(ctx)

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got err %v, want no err.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want err containing '%v'.", gotErr, tc.wantErr)
			}

			if got, want := gotTok, tc.wantTok; got != want {
				t.Errorf("token: got %v, want %v.", got, want)
			}
			if got, want := gotExp, tc.wantExp; !got.Equal(want) {
				t.Errorf("expiration: got %v, want %v.", got, want)
			}
		})
	}
}

func TestGetAccessToken(t *testing.T) {
	stringP := func(s string) *string {
		x := s
		return &x
	}

	tests := map[string]struct {
		inAppID    uint64
		inInstID   int64
		inKey      []byte
		inOpts     []TokenGeneratorOpt
		mWantID    int64
		mTok       *string
		mTime      *github.Timestamp
		mErr       error
		wantErr    string
		wantTok    string
		wantExp    time.Time
		cCache     string
		cExpiresAt time.Time
	}{
		"rpc-error": {
			inAppID:  7,
			inInstID: 42,
			inKey:    []byte(aPrivateKey),
			mWantID:  42,
			mTok:     nil,
			mTime:    nil,
			mErr:     fmt.Errorf("__rpc_error__"),
			wantErr:  "__rpc_error__",
			wantTok:  "",
			wantExp:  time.Time{},
			inOpts: []TokenGeneratorOpt{
				OptNow(func() time.Time {
					return time.Unix(3600, 0)
				}),
			},
		},
		"success-cache-miss": {
			inAppID:  7,
			inInstID: 42,
			inKey:    []byte(aPrivateKey),
			mWantID:  42,
			mTok:     stringP("tok0"),
			mTime:    &github.Timestamp{Time: time.Unix(7200, 0)},
			mErr:     nil,
			wantErr:  "",
			wantTok:  "tok0",
			wantExp:  time.Unix(7200, 0),
			inOpts: []TokenGeneratorOpt{
				OptNow(func() time.Time {
					return time.Unix(3600, 0)
				}),
			},
		},
		"success-cache-hit": {
			inAppID:  7,
			inInstID: 42,
			inKey:    []byte(aPrivateKey),
			mWantID:  0,
			mTok:     nil,
			mTime:    nil,
			mErr:     nil,
			wantErr:  "",
			wantTok:  "tok1",
			wantExp:  time.Unix(7200, 0),
			inOpts: []TokenGeneratorOpt{
				OptNow(func() time.Time {
					return time.Unix(3600, 0)
				}),
			},
			cCache:     "tok1",
			cExpiresAt: time.Unix(7200, 0),
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			tg, err := NewTokenGenerator(tc.inAppID, tc.inInstID, tc.inKey, tc.inOpts...)
			if err != nil {
				t.Fatalf("failed to build TokenGenerator for test: %v.", err)
			}
			createCalled := false
			OptCreateInstToken(func(_ context.Context, gotID int64, _ *github.InstallationTokenOptions) (*github.InstallationToken, *github.Response, error) {
				createCalled = true
				if got, want := gotID, tc.mWantID; got != want {
					t.Errorf("create installation token called with id: got %v, want %v.", got, want)
				}
				return &github.InstallationToken{
					Token:     tc.mTok,
					ExpiresAt: tc.mTime,
				}, nil, tc.mErr
			})(tg)

			tg.tokCache = tc.cCache
			tg.tokExpiresAt = tc.cExpiresAt

			ctx := context.Background()
			gotTok, gotExp, gotErr := tg.GetAccessToken(ctx)

			if got, want := createCalled, tc.mWantID > 0; got != want {
				t.Errorf("create called: got %v, want %v.", got, want)
			}

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got err %v, want no err.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want err containing '%v'.", gotErr, tc.wantErr)
			}

			if got, want := gotTok, tc.wantTok; got != want {
				t.Errorf("token: got %v, want %v.", got, want)
			}
			if got, want := gotExp, tc.wantExp; !got.Equal(want) {
				t.Errorf("expiration: got %v, want %v.", got, want)
			}
		})
	}
}

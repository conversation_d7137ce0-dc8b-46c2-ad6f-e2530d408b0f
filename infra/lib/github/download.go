package github

import (
	"context"
	"fmt"
	"io"
	"net/http"
)

func (c Client) Download(ctx context.Context, ref string, rf func(io.Reader) error) error {
	u := fmt.Sprintf("repos/%s/%s/tarball/%s", c.owner, c.repo, ref)
	req, err := c.gh.NewRequest(http.MethodGet, u, http.NoBody)
	if err != nil {
		return err
	}
	resp, err := c.gh.BareDo(ctx, req)
	if err != nil {
		return err
	}
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("%d/%s", resp.StatusCode, resp.Status)
	}
	defer resp.Body.Close()
	return rf(resp.Body)
}

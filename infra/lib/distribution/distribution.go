package distribution

import (
	"strings"
)

// ParseImage parses [REGISTRY"/"]REPO[":"TAG]. See documentation here:
// https://docs.docker.com/reference/cli/docker/image/tag/. The REGISTRY
// is a hostname, all other "/" are paths in the REPO.
//
// NOTE(mattm): Update this based on the spec here:
// https://github.com/opencontainers/distribution-spec/blob/main/spec.md#pulling-manifests.
func ParseImage(s string) (registry, repo, tag string) {
	registry, repoAndTag, _ := strings.Cut(s, "/")
	if repoAndTag == "" {
		repoAndTag = registry
		registry = ""
	}
	repo, tag, _ = strings.Cut(repoAndTag, ":")
	return
}

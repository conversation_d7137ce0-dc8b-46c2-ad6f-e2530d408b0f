package distribution

import (
	"testing"
)

func TestParseImage(t *testing.T) {
	tests := map[string]struct {
		in       string
		wantReg  string
		wantRepo string
		wantTag  string
	}{
		"empty": {
			in:       "",
			wantReg:  "",
			wantRepo: "",
			wantTag:  "",
		},
		"simple-name": {
			in:       "image0",
			wantReg:  "",
			wantRepo: "image0",
			wantTag:  "",
		},
		"name-and-tag": {
			in:       "image0:tag0",
			wantReg:  "",
			wantRepo: "image0",
			wantTag:  "tag0",
		},
		"name-and-multi-tag": {
			in:       "image0:tag0:tag1:tag2",
			wantReg:  "",
			wantRepo: "image0",
			wantTag:  "tag0:tag1:tag2",
		},
		"reg-and-name": {
			in:       "reg0/image0",
			wantReg:  "reg0",
			wantRepo: "image0",
			wantTag:  "",
		},
		"reg-port-and-name": {
			in:       "reg0:1000/image0",
			wantReg:  "reg0:1000",
			wantRepo: "image0",
			wantTag:  "",
		},
		"reg-and-multi-name": {
			in:       "reg0/image0/image1/image2",
			wantReg:  "reg0",
			wantRepo: "image0/image1/image2",
			wantTag:  "",
		},
		"reg-port-and-name-and-multi-tag": {
			in:       "reg0:1000/image0/image1/image2:tag0:tag1:tag2",
			wantReg:  "reg0:1000",
			wantRepo: "image0/image1/image2",
			wantTag:  "tag0:tag1:tag2",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			gotReg, gotRepo, gotTag := ParseImage(tc.in)
			if got, want := gotReg, tc.wantReg; got != want {
				t.Errorf("registry: got %s, want %s.", got, want)
			}
			if got, want := gotRepo, tc.wantRepo; got != want {
				t.Errorf("repo: got %s, want %s.", got, want)
			}
			if got, want := gotTag, tc.wantTag; got != want {
				t.Errorf("tag: got %s, want %s.", got, want)
			}
		})
	}
}

load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(default_visibility = ["//infra:internal"])

go_library(
    name = "docker",
    srcs = [
        "config.go",
        "creds.go",
    ],
    importpath = "github.com/augmentcode/augment/infra/lib/docker",
)

go_test(
    name = "docker_test",
    srcs = [
        "config_test.go",
        "creds_test.go",
    ],
    embed = [":docker"],
    deps = ["@com_github_google_go_cmp//cmp"],
)

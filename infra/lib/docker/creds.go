package docker

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"os"
	"os/exec"
	"strings"
)

// ParseAuth parses a simple [<username>:]<password> string commonly used in Docker and HTTP
// auths. (Note: See `ParseAuthB64()`).
func ParseAuth(auth string) (string, string) {
	username, password, found := strings.Cut(auth, ":")
	if !found {
		username, password = "", username
	}
	return username, password
}

// ParseAuthB64 parses a base64-encoded [<username>:]<password> string commonly
// used in Docker configs. See also `ParseAuth()`.
func ParseAuthB64(auth string) (string, string, error) {
	if auth == "" {
		return "", "", nil
	}
	decoded, err := base64.StdEncoding.DecodeString(auth)
	if err != nil {
		return "", "", err
	}
	username, password := ParseAuth(string(decoded))
	return username, password, nil
}

// AuthFromHelper returns the username,password credentials for a server from a
// "docker-credential-*" helper.
func AuthFromHelper(ctx context.Context, helper, server string) (string, string, error) {
	return credHelper{}.get(ctx, helper, server)
}

type credHelper struct {
	tOutput func(*exec.Cmd) ([]byte, error)
}

func (h credHelper) output(cmd *exec.Cmd) ([]byte, error) {
	f := func(cmd *exec.Cmd) ([]byte, error) { return cmd.Output() }
	if g := h.tOutput; g != nil {
		f = g
	}
	return f(cmd)
}

func (h credHelper) get(ctx context.Context, helper, server string) (string, string, error) {
	if pfx := "docker-credential-"; !strings.HasPrefix(helper, pfx) {
		helper = pfx + helper
	}

	cmd := exec.CommandContext(ctx, helper, "get")
	cmd.Stderr = os.Stderr

	if stdin, err := cmd.StdinPipe(); err != nil {
		return "", "", err
	} else if _, err := stdin.Write([]byte(server)); err != nil {
		return "", "", err
	} else if err := stdin.Close(); err != nil {
		return "", "", err
	}

	resp := struct {
		Username string `json:"Username"`
		Secret   string `json:"Secret"`
	}{}
	if buf, err := h.output(cmd); err != nil {
		return "", "", err
	} else if err := json.Unmarshal(buf, &resp); err != nil {
		return "", "", err
	}

	return resp.Username, resp.Secret, nil
}

// AuthFromSpec gets a username:password for a server a flexible source.
//
// - "": Lookup from default docker config (~/.docker/config.json).
// - [.]/path/to/file: (must start with `./` or `/`) Lookup from a docker file at an alternative path.
// - {...}: Lookup from literal config JSON string (must have leading `{`).
// - helper://<helper>: Invoke a cred helper directly.
// - [<user>:]<pass>: Return the <user> and <pass> directly.
//
// NOTE(mattm): One more option secret://<secret> is support on the command line for reading from a K8s
// secret.
func AuthFromSpec(ctx context.Context, spec, srv string) (string, string, error) {
	return specReader{}.AuthFromSpec(ctx, spec, srv)
}

type specReader struct {
	configReader configReader
	credHelper   credHelper
}

func (r specReader) AuthFromSpec(ctx context.Context, spec, srv string) (string, string, error) {
	// The first few options build a Config and return from there.

	var cfg *Config
	var err error

	if spec == "" {
		cfg, err = r.configReader.FromFile(DefaultConfigPath)
	} else if strings.HasPrefix(spec, "./") || strings.HasPrefix(spec, "/") {
		cfg, err = r.configReader.FromFile(spec)
	} else if strings.HasPrefix(spec, "{") {
		cfg, err = r.configReader.FromString(spec)
	}

	if err != nil {
		return "", "", err
	} else if cfg != nil {
		cfg.ch = r.credHelper
		return cfg.Auth(ctx, srv)
	}

	// The remaining options do not work off of a Config.

	if helper, found := strings.CutPrefix(spec, "helper://"); found {
		return r.credHelper.get(ctx, helper, srv)
	}

	user, pass := ParseAuth(spec)
	return user, pass, nil
}

package docker

import (
	"context"
	"fmt"
	"os/exec"
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
)

func TestParseAuth(t *testing.T) {
	tests := map[string]struct {
		inAuth string
		wUser  string
		wPass  string
	}{
		"empty": {
			inAuth: "",
			wUser:  "",
			wPass:  "",
		},
		"empty-colon": {
			inAuth: ":",
			wUser:  "",
			wPass:  "",
		},
		"left": {
			inAuth: "user0:",
			wUser:  "user0",
			wPass:  "",
		},
		"right": {
			inAuth: ":pass0",
			wUser:  "",
			wPass:  "pass0",
		},
		"no-colon": {
			inAuth: "pass0",
			wUser:  "",
			wPass:  "pass0",
		},
		"normal": {
			inAuth: "user0:pass0",
			wUser:  "user0",
			wPass:  "pass0",
		},
		"extra-colon": {
			inAuth: "user0:pass0:pass1",
			wUser:  "user0",
			wPass:  "pass0:pass1",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			gUser, gPass := ParseAuth(tc.inAuth)
			if got, want := gUser, tc.wUser; got != want {
				t.Errorf("username: got %v, want %v.", got, want)
			}
			if got, want := gPass, tc.wPass; got != want {
				t.Errorf("password: got %v, want %v.", got, want)
			}
		})
	}
}

func TestParseAuthB64(t *testing.T) {
	tests := map[string]struct {
		inAuth string
		wUser  string
		wPass  string
		wErr   string
	}{
		"empty": {
			inAuth: "",
			wUser:  "",
			wPass:  "",
			wErr:   "",
		},
		"b64-error": {
			inAuth: "^^^",
			wUser:  "",
			wPass:  "",
			wErr:   "illegal base64 data",
		},
		"normal": {
			inAuth: "****************",
			wUser:  "user0",
			wPass:  "pass0",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			gUser, gPass, gErr := ParseAuthB64(tc.inAuth)
			if tc.wErr == "" && gErr != nil {
				t.Errorf("got error %v, want no error.", gErr)
			}
			if tc.wErr != "" && (gErr == nil || !strings.Contains(gErr.Error(), tc.wErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gErr, tc.wErr)
			}
			if got, want := gUser, tc.wUser; got != want {
				t.Errorf("username: got %v, want %v.", got, want)
			}
			if got, want := gPass, tc.wPass; got != want {
				t.Errorf("password: got %v, want %v.", got, want)
			}
		})
	}
}

func TestAuthFromHelper(t *testing.T) {
	tests := map[string]struct {
		inHelper string
		inServer string
		wUser    string
		wPass    string
		wErr     string

		mSkip   bool
		wArgs   []string
		wStdin  string
		mStdout string
		mErr    error
	}{
		"no-mock": {
			inHelper: "_undefined_helper_",
			inServer: "server0",
			wUser:    "",
			wPass:    "",
			wErr:     "executable file not found",
			mSkip:    true,
		},
		"cmd-error": {
			inHelper: "helper0",
			inServer: "server0",
			wUser:    "",
			wPass:    "",
			wErr:     "cmd-error0",
			wArgs:    []string{"docker-credential-helper0", "get"},
			wStdin:   "server0",
			mStdout:  "_ERROR_UNUSED_",
			mErr:     fmt.Errorf("cmd-error0"),
		},
		"cmd-bad-output": {
			inHelper: "helper0",
			inServer: "server0",
			wUser:    "",
			wPass:    "",
			wErr:     "invalid character '_'",
			wArgs:    []string{"docker-credential-helper0", "get"},
			wStdin:   "server0",
			mStdout:  "__not_json__",
			mErr:     nil,
		},
		"success": {
			inHelper: "helper0",
			inServer: "server0",
			wUser:    "user0",
			wPass:    "pass0",
			wErr:     "",
			wArgs:    []string{"docker-credential-helper0", "get"},
			wStdin:   "server0",
			mStdout:  `{"Username": "user0", "Secret": "pass0"}`,
			mErr:     nil,
		},
		"success-has-prefix": {
			inHelper: "docker-credential-helper0",
			inServer: "server0",
			wUser:    "user0",
			wPass:    "pass0",
			wErr:     "",
			wArgs:    []string{"docker-credential-helper0", "get"},
			wStdin:   "server0",
			mStdout:  `{"Username": "user0", "Secret": "pass0"}`,
			mErr:     nil,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()

			var gUser, gPass string
			var gErr error

			if tc.mSkip {
				gUser, gPass, gErr = AuthFromHelper(ctx, tc.inHelper, tc.inServer)
			} else {
				mock := func(cmd *exec.Cmd) ([]byte, error) {
					if diff := cmp.Diff(tc.wArgs, cmd.Args); diff != "" {
						t.Errorf("helper args: -want +got:\n%s", diff)
					}
					stdin := make([]byte, len(tc.wStdin))
					cmd.Stdin.Read(stdin)
					if got, want := string(stdin), tc.wStdin; got != want {
						t.Errorf("helper stdin: got %v, want %v.", got, want)
					}
					return []byte(tc.mStdout), tc.mErr
				}
				h := credHelper{tOutput: mock}
				gUser, gPass, gErr = h.get(ctx, tc.inHelper, tc.inServer)
			}

			if tc.wErr == "" && gErr != nil {
				t.Errorf("got error %v, want no error.", gErr)
			}
			if tc.wErr != "" && (gErr == nil || !strings.Contains(gErr.Error(), tc.wErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gErr, tc.wErr)
			}
			if got, want := gUser, tc.wUser; got != want {
				t.Errorf("username: got %v, want %v.", got, want)
			}
			if got, want := gPass, tc.wPass; got != want {
				t.Errorf("password: got %v, want %v.", got, want)
			}
		})
	}
}

func TestAuthFromSpec(t *testing.T) {
	tests := map[string]struct {
		inSpec string
		inSrv  string

		fUserHome string
		wReadFile string
		fReadFile string
		wHelper   string
		fOutput   string

		wUser string
		wPass string
		wErr  string
	}{
		"empty-spec": {
			inSpec:    "",
			inSrv:     "srv0",
			fUserHome: "home0",
			wReadFile: "home0/.docker/config.json",
			fReadFile: `{"auths": {"srv0": {"auth": "****************"}}}`,
			fOutput:   "__unused__",
			wUser:     "user0",
			wPass:     "pass0",
			wErr:      "",
		},
		"rel-file": {
			inSpec:    "./foo/~/file/path",
			inSrv:     "srv0",
			fUserHome: "home0",
			wReadFile: "./foo/home0/file/path",
			fReadFile: `{"auths": {"srv0": {"auth": "****************"}}}`,
			fOutput:   "__unused__",
			wUser:     "user0",
			wPass:     "pass0",
			wErr:      "",
		},
		"abs-file": {
			inSpec:    "/file/path",
			inSrv:     "srv0",
			fUserHome: "__unused__",
			wReadFile: "/file/path",
			fReadFile: `{"auths": {"srv0": {"auth": "****************"}}}`,
			fOutput:   "__unused__",
			wUser:     "user0",
			wPass:     "pass0",
			wErr:      "",
		},
		"literal-config": {
			inSpec:    `{"auths": {"srv0": {"auth": "****************"}}}`,
			inSrv:     "srv0",
			fUserHome: "__unused__",
			wReadFile: "__unused__",
			fReadFile: "__unused__",
			fOutput:   "__unused__",
			wUser:     "user0",
			wPass:     "pass0",
			wErr:      "",
		},
		"config-err": {
			inSpec:    `{__invalid__}`,
			inSrv:     "srv0",
			fUserHome: "__unused__",
			wReadFile: "__unused__",
			fReadFile: "__unused__",
			fOutput:   "__unused__",
			wUser:     "",
			wPass:     "",
			wErr:      "invalid character '_'",
		},
		"helper": {
			inSpec:    `helper://h0`,
			inSrv:     "srv0",
			fUserHome: "__unused__",
			wReadFile: "__unused__",
			fReadFile: "__unused__",
			wHelper:   "docker-credential-h0",
			fOutput:   `{"Username": "user0", "Secret": "pass0"}`,
			wUser:     "user0",
			wPass:     "pass0",
			wErr:      "",
		},
		"literal-userpass": {
			inSpec:    "user0:pass0",
			inSrv:     "__unused__",
			fUserHome: "__unused__",
			wReadFile: "__unused__",
			fReadFile: "__unused__",
			fOutput:   "__unused__",
			wUser:     "user0",
			wPass:     "pass0",
			wErr:      "",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()

			r := specReader{
				configReader: configReader{
					tUserHomeDir: func() (string, error) {
						return tc.fUserHome, nil
					},
					tReadFile: func(fname string) ([]byte, error) {
						if got, want := fname, tc.wReadFile; got != want {
							t.Errorf("filename: got %v, want %v.", got, want)
						}
						return []byte(tc.fReadFile), nil
					},
				},
				credHelper: credHelper{
					tOutput: func(cmd *exec.Cmd) ([]byte, error) {
						if got, want := cmd.Args[0], tc.wHelper; got != want {
							t.Errorf("helper: got %v, want %v.", got, want)
						}
						return []byte(tc.fOutput), nil
					},
				},
			}

			gUser, gPass, gErr := r.AuthFromSpec(ctx, tc.inSpec, tc.inSrv)

			if tc.wErr == "" && gErr != nil {
				t.Errorf("got error %v, want no error.", gErr)
			}
			if tc.wErr != "" && (gErr == nil || !strings.Contains(gErr.Error(), tc.wErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gErr, tc.wErr)
			}
			if got, want := gUser, tc.wUser; got != want {
				t.Errorf("username: got %v, want %v.", got, want)
			}
			if got, want := gPass, tc.wPass; got != want {
				t.Errorf("password: got %v, want %v.", got, want)
			}
		})
	}

	// And one more for full coverage
	t.Run("from-default", func(t *testing.T) {
		gUser, gPass, gErr := AuthFromSpec(context.Background(), "user0:pass0", "srv0")
		if gErr != nil {
			t.Errorf("error: %v.", gErr)
		}
		if got, want := gUser, "user0"; got != want {
			t.Errorf("username: got %v, want %v.", got, want)
		}
		if got, want := gPass, "pass0"; got != want {
			t.Errorf("password: got %v, want %v.", got, want)
		}
	})
}

package docker

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"sort"
	"strings"
)

const (
	DefaultConfigPath = "~/.docker/config.json"
)

// Config is the format found in the default Docker config file ~/.docker/config.json, as
// well as in K8s `kubernetes.io/dockerconfigjson` Secrets.
type Config struct {
	Auths map[string]struct {
		Auth string `json:"auth"`
	} `json:"auths"`
	<PERSON>red<PERSON><PERSON><PERSON> map[string]string `json:"credHelpers"`

	ch credHelper // for testing
}

// ConfigFromString parses a new `Config` from a string.
func ConfigFromString(str string) (*Config, error) {
	return configReader{}.FromString(str)
}

// ConfigFromFile reads `fname` and passes it to `ConfigFromString()`. Tilde expansion is
// performed on any full path component matching `~` and replaced with the user's homedir.
func ConfigFromFile(str string) (*Config, error) {
	return configReader{}.FromFile(str)
}

// ConfigFromDefault is a convenience for `ConfigFromFile(DefaultConfigPath)`.
func ConfigFromDefault() (*Config, error) {
	return ConfigFromFile(DefaultConfigPath)
}

// configReader provides the real implementation of the above builders, with
// syscalls et al swappable for testing.
type configReader struct {
	tUserHomeDir func() (string, error)
	tReadFile    func(string) ([]byte, error)
}

func (r configReader) userHomeDir() (string, error) {
	f := os.UserHomeDir
	if g := r.tUserHomeDir; g != nil {
		f = g
	}
	return f()
}

func (r configReader) readFile(fname string) ([]byte, error) {
	f := os.ReadFile
	if g := r.tReadFile; g != nil {
		f = g
	}
	return f(fname)
}

func (r configReader) FromString(str string) (*Config, error) {
	cfg := &Config{}
	if err := json.Unmarshal([]byte(str), cfg); err != nil {
		return nil, err
	}
	return cfg, nil
}

func (r configReader) FromFile(fname string) (*Config, error) {
	if fname == "" {
		fname = DefaultConfigPath
	}

	parts := strings.Split(fname, "/")
	homedir := ""
	for i, part := range parts {
		if part == "~" {
			if homedir == "" {
				var err error
				if homedir, err = r.userHomeDir(); err != nil {
					return nil, err
				}
			}
			parts[i] = homedir
		}
	}
	fname = strings.Join(parts, "/")

	if buf, err := r.readFile(fname); err != nil {
		return nil, err
	} else {
		return ConfigFromString(string(buf))
	}
}

// Servers returns the list of servers referenced by auths and cred helpers.
func (cfg Config) Servers() []string {
	set := map[string]bool{}
	for k := range cfg.Auths {
		set[k] = true
	}
	for k := range cfg.CredHelpers {
		set[k] = true
	}
	ret := []string{}
	for k := range set {
		ret = append(ret, k)
	}
	sort.Strings(ret)
	return ret
}

// Auth returns the username and password for a given server. If the server has a credhelper
// defined, it will be exected as a subprocess.
func (cfg Config) Auth(ctx context.Context, server string) (string, string, error) {
	if auth, ok := cfg.Auths[server]; ok {
		return ParseAuthB64(auth.Auth)
	}
	if helper := cfg.CredHelpers[server]; helper != "" {
		return cfg.ch.get(ctx, helper, server)
	}
	return "", "", fmt.Errorf("%s: no creds found in config", server)
}

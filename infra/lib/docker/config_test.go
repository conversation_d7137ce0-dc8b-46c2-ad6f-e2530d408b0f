package docker

import (
	"context"
	"fmt"
	"os/exec"
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
)

func TestConfigFromString(t *testing.T) {
	tests := map[string]struct {
		in       string
		wErr     string
		wServers []string
	}{
		"bad-json": {
			in:   "__bad_json__",
			wErr: "invalid character",
		},
		"success": {
			in: `{
				"auths": {
					"a": {},
					"b": {}
				},
				"credHelpers": {
					"b": "",
					"c": ""
				}
			}`,
			wServers: []string{"a", "b", "c"},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			cfg, gErr := ConfigFromString(tc.in)
			if tc.wErr == "" && gErr != nil {
				t.Errorf("got error %v, want no error.", gErr)
			}
			if tc.wErr != "" && (gErr == nil || !strings.Contains(gErr.Error(), tc.wErr)) {
				t.<PERSON>("got error %v, want error containing '%v'.", gErr, tc.wErr)
			}
			if cfg == nil {
				if len(tc.wServers) != 0 {
					t.Errorf("servers: got nil cfg, wanted %v.", tc.wServers)
				}
			} else {
				if diff := cmp.Diff(tc.wServers, cfg.Servers()); diff != "" {
					t.Errorf("servers: -want +got:\n%s", diff)
				}
			}
		})
	}
}

func TestConfigFromFile(t *testing.T) {
	tests := map[string]struct {
		inFname string
		wErr    string
		wNil    bool
		fHD     string
		fHDErr  error
		wRFFile string
		fRF     string
		fRFErr  error
	}{
		"no-fname": {
			inFname: "",
			wErr:    "",
			wNil:    false,
			fHD:     "_home_",
			fHDErr:  nil,
			wRFFile: "_home_/.docker/config.json",
			fRF:     "{}",
			fRFErr:  nil,
		},
		"success": {
			inFname: "/file/path",
			wErr:    "",
			wNil:    false,
			fHD:     "",
			fHDErr:  fmt.Errorf("__unused_err__"),
			wRFFile: "/file/path",
			fRF:     "{}",
			fRFErr:  nil,
		},
		"homedir-error": {
			inFname: "/file/~/path",
			wErr:    "homedir-err0",
			wNil:    true,
			fHD:     "_home0_",
			fHDErr:  fmt.Errorf("some homedir-err0"),
			wRFFile: "_unused_file_",
			fRF:     "_unused_json_",
			fRFErr:  fmt.Errorf("_unused_readfile_err0_"),
		},
		"readfile-error": {
			inFname: "/file/path",
			wErr:    "readfile-err0",
			wNil:    true,
			fHD:     "",
			fHDErr:  fmt.Errorf("__unused_err__"),
			wRFFile: "/file/path",
			fRF:     "_unused_json_",
			fRFErr:  fmt.Errorf("readfile-err0"),
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			r := configReader{
				tUserHomeDir: func() (string, error) {
					return tc.fHD, tc.fHDErr
				},
				tReadFile: func(fname string) ([]byte, error) {
					if got, want := fname, tc.wRFFile; got != want {
						t.Errorf("readfile: got %v, want %v.", got, want)
					}
					return []byte(tc.fRF), tc.fRFErr
				},
			}

			cfg, gErr := r.FromFile(tc.inFname)

			if tc.wErr == "" && gErr != nil {
				t.Errorf("got error %v, want no error.", gErr)
			}
			if tc.wErr != "" && (gErr == nil || !strings.Contains(gErr.Error(), tc.wErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gErr, tc.wErr)
			}
			if got, want := cfg == nil, tc.wNil; got != want {
				t.Errorf("nil cfg: got %v, want %v.", got, want)
			}
		})
	}
}

func TestConfigAuth(t *testing.T) {
	cfgstr := `{
		"auths": {
			"srv0": {
				"auth": "dXNlcjA6cGFzczA=",
				"foo0": "bar0"
			},
			"srv1": {
				"auth": "dXNlcjE6cGFzczE=",
				"foo0": "bar1"
			}
		},
		"credHelpers": {
			"srv1": "helper0",
			"srv2": "helper1"
		}
	}`

	tests := map[string]struct {
		in     string
		inSrv  string
		wUser  string
		wPass  string
		wErr   string
		fCHOut string
		fCHErr error
	}{
		"from-auths": {
			in:     cfgstr,
			inSrv:  "srv0",
			wUser:  "user0",
			wPass:  "pass0",
			wErr:   "",
			fCHOut: "_unused_",
			fCHErr: fmt.Errorf("_unused_err_"),
		},
		"from-auths-priority": {
			in:     cfgstr,
			inSrv:  "srv1",
			wUser:  "user1",
			wPass:  "pass1",
			wErr:   "",
			fCHOut: "_unused_",
			fCHErr: fmt.Errorf("_unused_err_"),
		},
		"from-credhelper-err": {
			in:     cfgstr,
			inSrv:  "srv2",
			wUser:  "",
			wPass:  "",
			wErr:   "_credhelper_err_",
			fCHOut: "_unused_",
			fCHErr: fmt.Errorf("_credhelper_err_"),
		},
		"from-credhelper": {
			in:     cfgstr,
			inSrv:  "srv2",
			wUser:  "user2",
			wPass:  "pass2",
			wErr:   "",
			fCHOut: `{"Username": "user2", "Secret": "pass2"}`,
			fCHErr: nil,
		},
		"miss-err": {
			in:     cfgstr,
			inSrv:  "srv3",
			wUser:  "",
			wPass:  "",
			wErr:   "srv3: no creds found",
			fCHOut: "_unused_",
			fCHErr: fmt.Errorf("_unused_err_"),
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			cfg, err := ConfigFromString(tc.in)
			if err != nil {
				t.Fatalf("Error building Config: %v.", err)
			}
			cfg.ch = credHelper{
				tOutput: func(cmd *exec.Cmd) ([]byte, error) {
					stdin := make([]byte, len(tc.inSrv))
					cmd.Stdin.Read(stdin)
					if got, want := string(stdin), tc.inSrv; got != want {
						t.Errorf("helper stdin: got %v, want %v.", got, want)
					}
					return []byte(tc.fCHOut), tc.fCHErr
				},
			}

			ctx := context.Background()
			gUser, gPass, gErr := cfg.Auth(ctx, tc.inSrv)

			if tc.wErr == "" && gErr != nil {
				t.Errorf("got error %v, want no error.", gErr)
			}
			if tc.wErr != "" && (gErr == nil || !strings.Contains(gErr.Error(), tc.wErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gErr, tc.wErr)
			}

			if got, want := gUser, tc.wUser; got != want {
				t.Errorf("username: got %v, want %v.", got, want)
			}
			if got, want := gPass, tc.wPass; got != want {
				t.Errorf("password: got %v, want %v.", got, want)
			}
		})
	}

	// Shameless for line coverage, there at least shouldn't be a panic.
	_, _ = ConfigFromDefault()
}

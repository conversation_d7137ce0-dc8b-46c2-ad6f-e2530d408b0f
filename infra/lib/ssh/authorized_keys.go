package ssh

import (
	"errors"
	"fmt"
	"strings"

	"golang.org/x/crypto/ssh"
)

// MaybeMapPrivateToPublic is usefull in situations where we expect an SSH public key (in the one-line, authorized_key format) but may
// have been given an SSH private key instead. For example `id_ecdsa` instead of `id_ecdsa.pub`. Try to parse `orig` as a single, PEM-encoded,
// multi-line, ASCII private key and if successful, derive and return the correspoding PublicKey (single line).
//
// If there is an error parsing `orig` as a PrivateKey, it is returned unmodified and the error is ignored. If `orig` is parsed as a PrivateKey,
// but a PublicKey cannot be derived or marshaled to the `authorized_key` format, an error **is** returned.
func MaybeMapPrivateToPublic(orig string) (string, error) {
	priv, err := ParseRawPrivateKey([]byte(orig))
	if err != nil {
		return orig, nil
	}
	pub, err := PublicASCII(priv, "")
	return string(pub), err
}

// ValidatedAuthorizedKeys is a wrapper around `ValidateAuthorizedKeys()` and `MaybeMapPrivateToPublic()`. If the result
// has been updated due to private->public mapping, the boolean return value will be `true`.
func ValidatedAuthorizedKeys(keys string) (string, bool, error) {
	keys2, err := MaybeMapPrivateToPublic(keys)
	if err != nil {
		return "", false, err
	}
	if _, err := ValidateAuthorizedKeys(keys2); err != nil {
		return "", false, err
	}
	return keys2, keys2 != keys, nil
}

// ValidateAuthorizedKeys validates a multi-line `keys` string, such as the contents of an
// `authorized_keys` file. Blank lines and comments are ignored. All other lines must parse
// as `ssh.ParseAuthorizedKey()`.
func ValidateAuthorizedKeys(keys string) (int, error) {
	count, errs := 0, []error{}
	for i, line := range strings.Split(keys, "\n") {
		line = strings.TrimSpace(line)
		if len(line) == 0 || strings.HasPrefix(line, "#") {
			continue
		}
		if err := ValidateAuthorizedKey(line); err != nil {
			errs = append(errs, fmt.Errorf("line %d: %v :: >%s", i+1, err, line))
		} else {
			count++
		}
	}
	return count, errors.Join(errs...)
}

// ValidateAuthorizedKey validates a single `line` as from an `authorized_keys` file. The line
// must parse as `ssh.ParseAuthorizedKey()`.
func ValidateAuthorizedKey(line string) error {
	_, _, _, _, err := ssh.ParseAuthorizedKey([]byte(line))
	return err
}

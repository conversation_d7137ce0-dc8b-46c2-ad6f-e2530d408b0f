package ssh

import (
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
)

const (
	sshTypeRSA   = "ssh-rsa"
	sshPubRSA    = "AAAAB3NzaC1yc2EAAAADAQABAAABgQDIGuJoesE1QkccH5EkBydksRElEPG2zLVF10P2HwugtyBHwv/Br8TdHZznp438+4NtmO1Pf9TSf8DWYp8/gRS8ODqFoniiosUi+zHGqj6/WWqY8/eE/qwWtXg3WNmyWtbK68jkHaQ/xDPnawSJpRF96TgDnKKjXwDkczCapXfzUF3S5yhQ9Mg7r9wsttJzoemzaaPYcjqz8wHvSDPHFNNlZ4Igz4TPUuxIzo0YsbDnggCzVOAc/dhCs7W/ptLVmoU4qzhWJd/u9BB9AFG7Am1NgXyRuzQjs2MsI2MbXoeCtE60P3fsE17wkY77x5hQM4hbUdHyCJPwg/kTuP2CpqerAOc9l0BHLBOmC6WGtRWvZ6rpJ7eliwU5ApqTQ23hu+jcmIU/PCeLRAiecGWshfUSUVBhNN3UcB3KIR+4r8ps4cEiZfKYP4IM2W2oFntXrIRSJTZxsiuRMBDkAJUA6y42PiD4RDxmVQ8OUrURAQ86pRzoqqKguRNLC5hduXK/mPk="
	sshType25519 = "ssh-ed25519"
	sshPub25519  = "AAAAC3NzaC1lZDI1NTE5AAAAIASEsyJoTMgSLcaKobkm3BhA2WMi/rgshe/YrZ+S3+zh"
	// pragma: allowlist nextline secret
	sshPriv25519 = `****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
)

func TestMaybeMapPrivateToPublic(t *testing.T) {
	pub := sshType25519 + " " + sshPub25519 + "\n"
	priv := sshPriv25519
	tests := map[string]struct {
		orig    string
		want    string
		wantErr string
	}{
		"invalid": {
			orig: "__invalid__",
			want: "__invalid__",
		},
		"already-public": {
			orig: pub,
			want: pub,
		},
		"already-multiple": {
			orig: pub + pub,
			want: pub + pub,
		},
		"priv": {
			orig: priv,
			want: pub,
		},
		"priv-multiple": {
			orig: priv + priv,
			want: pub,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			got, gotErr := MaybeMapPrivateToPublic(tc.orig)

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want error containing '%s'.", gotErr, tc.wantErr)
			}

			if diff := cmp.Diff(tc.want, got); diff != "" {
				t.Errorf("-want +got:\n%s", diff)
			}
		})
	}
}

func TestValidatedAuthorizedKeys(t *testing.T) {
	pub := sshType25519 + " " + sshPub25519 + "\n"
	priv := sshPriv25519
	tests := map[string]struct {
		orig    string
		want    string
		wantB   bool
		wantErr string
	}{
		"invalid": {
			orig:    "__invalid__",
			want:    "",
			wantB:   false,
			wantErr: "__invalid__",
		},
		"already-public": {
			orig:    pub,
			want:    pub,
			wantB:   false,
			wantErr: "",
		},
		"priv": {
			orig:    priv,
			want:    pub,
			wantB:   true,
			wantErr: "",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			got, gotB, gotErr := ValidatedAuthorizedKeys(tc.orig)

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want error containing '%s'.", gotErr, tc.wantErr)
			}

			if diff := cmp.Diff(tc.want, got); diff != "" {
				t.Errorf("-want +got:\n%s", diff)
			}

			if got, want := gotB, tc.wantB; got != want {
				t.Errorf("mapped: got %v, want %v.", got, want)
			}
		})
	}
}

func TestValidateAuthorizedKeys(t *testing.T) {
	tests := map[string]struct {
		inLines   []string
		wantCount int
		wantErr   string
	}{
		"empty": {
			wantCount: 0,
			wantErr:   "",
			inLines:   nil,
		},
		"only-comments": {
			wantCount: 0,
			wantErr:   "",
			inLines: []string{
				"# comment",
				"  # another comment",
				"",
				"# a third comment",
			},
		},
		"mixed-comments-and-keys": {
			wantCount: 4,
			wantErr:   "",
			inLines: []string{
				"# comment",
				sshType25519 + " " + sshPub25519,
				"pty,no-X11-forwarding" + " " + sshType25519 + " " + sshPub25519 + " " + "comment0 comment1 comment2",
				"  # another comment",
				"",
				sshType25519 + " " + sshPub25519 + " " + "comment0 comment1 comment2",
				"# a third comment",
				sshTypeRSA + " " + sshPubRSA,
			},
		},
		"bad-key": {
			wantCount: 3,
			wantErr:   "line 6: ssh: no key found",
			inLines: []string{
				"# comment",
				sshType25519 + " " + sshPub25519,
				"pty,no-X11-forwarding" + " " + sshType25519 + " " + sshPub25519 + " " + "comment0 comment1 comment2",
				"  # another comment",
				"",
				sshType25519 + " " + sshPub25519 + "x" + " " + "comment0 comment1 comment2",
				"# a third comment",
				sshTypeRSA + " " + sshPubRSA,
			},
		},
		"2-bad-keys": {
			wantCount: 2,
			wantErr:   "no key found",
			inLines: []string{
				"# comment",
				sshType25519 + " " + sshPub25519,
				"extra! pty,no-X11-forwarding" + " " + sshType25519 + " " + sshPub25519 + " " + "comment0 comment1 comment2",
				"  # another comment",
				"",
				sshType25519 + " " + sshPub25519 + "x" + " " + "comment0 comment1 comment2",
				"# a third comment",
				sshTypeRSA + " " + sshPubRSA,
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			gotCount, gotErr := ValidateAuthorizedKeys(strings.Join(tc.inLines, "\n"))
			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want error containing '%s'.", gotErr, tc.wantErr)
			}
			if got, want := gotCount, tc.wantCount; got != want {
				t.Errorf("count: got %v, want %v.", got, want)
			}
		})
	}
}

func TestValidateAuthorizedKey(t *testing.T) {
	tests := map[string]struct {
		inLine  string
		wantErr string
	}{
		"empty": {
			inLine:  "",
			wantErr: "no key found",
		},
		"comment": {
			inLine:  "# comment",
			wantErr: "no key found",
		},
		"good-ed25519": {
			inLine: sshType25519 + " " + sshPub25519,
		},
		"good-rsa": {
			inLine: sshTypeRSA + " " + sshPubRSA,
		},
		"good-with-comment": {
			inLine: sshType25519 + " " + sshPub25519 + " " + "comment0 comment1 comment2",
		},
		"leading-space-ok": {
			inLine: " \t \t " + sshType25519 + " " + sshPub25519,
		},
		"bad-type-only": {
			inLine:  sshType25519,
			wantErr: "no key found",
		},
		"bad-key-only": {
			inLine:  sshPub25519,
			wantErr: "no key found",
		},
		"bad-extra-chars": {
			inLine:  sshType25519 + " " + sshPub25519 + "x" + " " + "comment0",
			wantErr: "no key found",
		},
		"good-with-options": {
			inLine: "pty,no-X11-forwarding" + " " + sshType25519 + " " + sshPub25519 + " " + "comment0 comment1 comment2",
		},
		"invalid-options-okay": {
			inLine: "foo,bar" + " " + sshType25519 + " " + sshPub25519 + " " + "comment0 comment1 comment2",
		},
		"bad-too-many-options": {
			inLine:  "foo bar" + " " + sshType25519 + " " + sshPub25519 + " " + "comment0 comment1 comment2",
			wantErr: "no key found",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			gotErr := ValidateAuthorizedKey(tc.inLine)
			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want error containing '%s'.", gotErr, tc.wantErr)
			}
		})
	}
}

package ssh

import (
	"errors"
	"testing"
)

// TestE2ELens does some very basic testing. Even with a deterministic random
// generator (e.g., all Zeros, the generated keys are not deterministic.
func TestE2ELens(t *testing.T) {
	keygen := KeyGen{}
	set, err := keygen.GenHostKeyFiles("comment0")
	if err != nil {
		t.Fatalf("GenHostKeyFiles(): %v.", err)
	}
	set.TestMockedOut(t, "comment0")
}

func TestE2EErr(t *testing.T) {
	keygen := KeyGen{
		rand: ErrReader{},
	}
	_, err := keygen.GenHostKeyFiles("comment0")
	if !errors.Is(err, ErrReading) {
		t.<PERSON>("Expected ErrReading, got: %v.", err)
	}
}

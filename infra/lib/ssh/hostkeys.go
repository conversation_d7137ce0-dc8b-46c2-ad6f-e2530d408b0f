package ssh

import (
	"crypto/ecdsa"
	"crypto/ed25519"
	"crypto/rsa"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
)

// GenHostKeys generates the same set of hostkeys as `ssh-keygen -A`.
func (kg KeyGen) GenHostKeys() (*HostKeySet, error) {
	var err error
	keyset := &HostKeySet{}

	if keyset.RSA, err = kg.GenRSA(); err != nil {
		return nil, err
	}
	if keyset.ECDSA, err = kg.GenECDSA(); err != nil {
		return nil, err
	}
	if keyset.ED25519, err = kg.GenED25519(); err != nil {
		return nil, err
	}

	return keyset, nil
}

// GenHostKeyFiles extends `GenHostKeys()` to a map of file names to
// ascii-encoded public and private keys.
func (kg KeyGen) GenHostKeyFiles(comment string) (HostKeyFileSet, error) {
	set, err := kg.GenHostKeys()
	if err != nil {
		return nil, err
	}
	return set.Files(comment)
}

// HostKeySet is a set of hostkeys per ssh-keygen(1) -A.
type HostKeySet struct {
	RSA     *rsa.PrivateKey
	ECDSA   *ecdsa.PrivateKey
	ED25519 ed25519.PrivateKey
}

// Files generates filenames -> ascii for the keys in the set.
func (s HostKeySet) Files(comment string) (HostKeyFileSet, error) {
	files := HostKeyFileSet{}

	add := func(typ string, k PrivateKey) error {
		if priv, err := PrivateASCII(k, comment); err != nil {
			return err
		} else {
			files["ssh_host_"+typ+"_key"] = priv
		}
		if pub, err := PublicASCII(k, comment); err != nil {
			return err
		} else {
			files["ssh_host_"+typ+"_key.pub"] = pub
		}
		return nil
	}

	if err := add("rsa", s.RSA); err != nil {
		return nil, err
	}
	if err := add("ecdsa", s.ECDSA); err != nil {
		return nil, err
	}
	if err := add("ed25519", s.ED25519); err != nil {
		return nil, err
	}

	return files, nil
}

func IsPublicFile(fname string) bool {
	return strings.HasSuffix(fname, ".pub")
}

// HostKeyFileSet is a mapping of base filenames to ascii contents.
type HostKeyFileSet map[string][]byte

// PrintAll is useful for debugging.
func (f HostKeyFileSet) PrintAll() {
	fnames := []string{}
	for fname := range f {
		fnames = append(fnames, fname)
	}
	sort.Strings(fnames)

	for _, fname := range fnames {
		fmt.Printf("### %s:\n%s\n", fname, string(f[fname]))
	}
}

// Info returns the set as list of `HostKeyFileInfo`.
func (f HostKeyFileSet) Info() []HostKeyFileInfo {
	ret := []HostKeyFileInfo{}
	for fname, buf := range f {
		pub := IsPublicFile(fname)
		mode := func() os.FileMode {
			if pub {
				return 0o644
			} else {
				return 0o600
			}
		}()
		ret = append(ret, HostKeyFileInfo{
			Name:   fname,
			Public: pub,
			Mode:   mode,
			Buf:    string(buf),
		})
	}
	sort.Slice(ret, func(i, j int) bool {
		return ret[i].Name < ret[j].Name
	})
	return ret
}

// HostKeyFileInfo encodes typical filesystem metadata for public and private keys.
type HostKeyFileInfo struct {
	Name   string
	Public bool
	Mode   os.FileMode
	Buf    string
}

// WriteFS writes the FileSet out to the filesystem. Dirname is created with the `mkdir -p`
// equivalent if it doesn't exist. If any of the target files exist, not are written and an
// error is returned; unless `force` is given.
func (f HostKeyFileSet) WriteFS(dirname string, force bool) error {
	if !force {
		errs := []error{}
		for fname := range f {
			fullname := filepath.Join(dirname, fname)
			if _, err := os.Stat(fullname); err == nil {
				errs = append(errs, fmt.Errorf("%s: file exists", fullname))
			} else if !errors.Is(err, os.ErrNotExist) {
				errs = append(errs, err)
			}
		}
		if err := errors.Join(errs...); err != nil {
			return err
		}
	}
	if err := os.MkdirAll(dirname, 0o750); err != nil {
		return err
	}
	for _, info := range f.Info() {
		fullname := filepath.Join(dirname, info.Name)
		file, err := os.OpenFile(fullname, os.O_CREATE|os.O_TRUNC|os.O_WRONLY, info.Mode)
		if err != nil {
			return err
		}
		defer file.Close()
		if _, err := file.Write([]byte(info.Buf)); err != nil {
			return err
		}
		if err := file.Close(); err != nil {
			return err
		}
	}
	return nil
}

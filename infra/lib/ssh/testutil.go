// TODO(mattm): Figure out how to make this testing-only again.

package ssh

import (
	"fmt"
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
	"golang.org/x/crypto/ssh"
)

type ErrReader struct{}

var ErrReading = fmt.Errorf("ErrReading")

func (r ErrReader) Read(p []byte) (int, error) {
	return 0, ErrReading
}

func (f HostKeyFileSet) MockOutForTest() {
	for k, v := range f {
		if strings.HasSuffix(k, ".pub") {
			pub, comment, _, _, err := ssh.ParseAuthorizedKey(v)
			if err != nil {
				f[k] = []byte(fmt.Sprintf("ERROR: %v", err))
			} else {
				f[k] = []byte(fmt.Sprintf("%s %s", pub.Type(), comment))
			}
		} else {
			priv, err := ssh.ParseRawPrivateKey(v)
			if err != nil {
				f[k] = []byte(fmt.Sprintf("ERROR: %v", err))
			} else {
				f[k] = []byte(fmt.Sprintf("%T", priv))
			}
		}
	}
}

func (f HostKeyFileSet) TestMockedOut(t *testing.T, comment string) {
	t.Helper()
	f.MockOutForTest()
	got := f.Info()
	want := []HostKeyFileInfo{
		{
			Name:   "ssh_host_ecdsa_key",
			Public: false,
			Mode:   0o600,
			Buf:    "*ecdsa.PrivateKey",
		},
		{
			Name:   "ssh_host_ecdsa_key.pub",
			Public: true,
			Mode:   0o644,
			Buf:    "ecdsa-sha2-nistp256 " + comment,
		},
		{
			Name:   "ssh_host_ed25519_key",
			Public: false,
			Mode:   0o600,
			Buf:    "*ed25519.PrivateKey",
		},
		{
			Name:   "ssh_host_ed25519_key.pub",
			Public: true,
			Mode:   0o644,
			Buf:    "ssh-ed25519 " + comment,
		},
		{
			Name:   "ssh_host_rsa_key",
			Public: false,
			Mode:   0o600,
			Buf:    "*rsa.PrivateKey",
		},
		{
			Name:   "ssh_host_rsa_key.pub",
			Public: true,
			Mode:   0o644,
			Buf:    "ssh-rsa " + comment,
		},
	}
	if diff := cmp.Diff(want, got); diff != "" {
		t.Errorf("ssh.TestMockedOut(): -want +got:\n%s", diff)
	}
}

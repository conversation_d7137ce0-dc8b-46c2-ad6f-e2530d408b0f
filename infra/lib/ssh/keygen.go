package ssh

import (
	"crypto/ecdsa"
	"crypto/ed25519"
	"crypto/elliptic"
	"crypto/rand"
	"crypto/rsa"
	"io"
)

var (
	DefaultRSABits    = 3072
	DefaultECDSACurve = elliptic.P256
)

// KeyGen is a key generator.
type KeyGen struct {
	rand io.Reader
}

// <PERSON> returns the random io.Reader of the generator. By default, `rand.Reader`.
func (kg KeyGen) Rand() io.Reader {
	r := kg.rand
	if r == nil {
		r = rand.Reader
	}
	return r
}

// GenRSA generates an RSA key, default 3072 bits per ssh-keygen(1).
func (kg KeyGen) GenRSA() (*rsa.PrivateKey, error) {
	return rsa.GenerateKey(kg.Rand(), DefaultRSABits)
}

// GenECDSA generates an ECDSA key, default curve P256 per ssh-keygen(1).
func (kg KeyGen) GenECDSA() (*ecdsa.PrivateKey, error) {
	return ecdsa.GenerateKey(DefaultECDSACurve(), kg.Rand())
}

// GenED25519 generates an ED25519 key.
func (kg KeyGen) GenED25519() (ed25519.<PERSON><PERSON><PERSON>, error) {
	_, priv, err := ed25519.<PERSON>rate<PERSON>ey(kg.<PERSON>())
	return priv, err
}

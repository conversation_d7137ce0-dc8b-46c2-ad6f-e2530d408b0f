package ssh

import (
	"crypto"
	"encoding/pem"
	"fmt"

	"golang.org/x/crypto/ssh"
)

// PrivateKey is the real PrivateKey interface that the `crypto` package
// says it can't use for backwards compatibility:
// https://pkg.go.dev/crypto#PrivateKey
type PrivateKey interface {
	Public() crypto.PublicKey
	Equal(crypto.PrivateKey) bool
}

// ParseRawPrivateKey is a thin wrapper around `ssh.ParseRawPrivateKey()` which also converts to the proper `PrivateKey` type.
func ParseRawPrivateKey(pemBytes []byte) (PrivateKey, error) {
	raw, err := ssh.ParseRawPrivateKey([]byte(pemBytes))
	if err != nil {
		return nil, err
	}
	priv, ok := raw.(PrivateKey)
	if !ok {
		return nil, fmt.Errorf("ssh.ParseRawPrivateKey() returned %T, which does not implement the crypto.Private interface", raw)
	}
	return priv, nil
}

// PrivateASCII converts a PrivateKey to its SSH file format.
func PrivateASCII(k PrivateKey, comment string) ([]byte, error) {
	block, err := ssh.MarshalPrivateKey(k, comment)
	if err != nil {
		return nil, err
	}
	return pem.EncodeToMemory(block), nil
}

// PublicASCII converts a PrivateKey's PublicKey to its SSH file format.
func PublicASCII(k PrivateKey, comment string) ([]byte, error) {
	pub, err := ssh.NewPublicKey(k.Public())
	if err != nil {
		return nil, err
	}
	buf := ssh.MarshalAuthorizedKey(pub)
	if comment != "" && len(buf) > 1 && buf[len(buf)-1] == '\n' {
		buf = append(buf[:len(buf)-1], []byte(" "+comment+"\n")...)
	}
	return buf, nil
}

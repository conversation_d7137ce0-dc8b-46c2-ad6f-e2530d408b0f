package k8s

import (
	"context"
	"errors"
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/dynamic"
)

////////////////////////////////////////////////////////////////////////////////
//
// IAM ServiceAccount
//

type GCP_IAM_ServiceAccount struct {
	Object
	Raw *GCP_IAM_ServiceAccountObject `json:",inline"`
}

type GCP_IAM_ServiceAccountObject struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec struct {
		DisplayName string `json:"displayName,omitempty"`
		Description string `json:"description,omitempty"`
		Disabled    bool   `json:"disabled,omitempty"`
		ResourceID  string `json:"resourceID,omitempty"`
	} `json:"spec"`
}

func (o *GCP_IAM_ServiceAccount) Unstructured() (*unstructured.Unstructured, error) {
	u, err := o.Object.Unstructured()
	if err != nil {
		return nil, err
	}
	unstructured.RemoveNestedField(u.Object, "metadata", "creationTimestamp")
	if o.Raw.Spec == (GCP_IAM_ServiceAccountObject{}).Spec {
		unstructured.RemoveNestedField(u.Object, "spec")
	}
	return u, nil
}

func (o *GCP_IAM_ServiceAccount) Email(project string) string {
	return fmt.Sprintf("%s@%s.iam.gserviceaccount.com", o.Name(), project)
}

func (o *GCP_IAM_ServiceAccount) IAMRef(project string) string {
	return fmt.Sprintf("projects/%s/serviceAccounts/%s", project, o.Email(project))
}

func NewGCP_IAM_ServiceAccount(raw *GCP_IAM_ServiceAccountObject) *GCP_IAM_ServiceAccount {
	if raw == nil {
		raw = &GCP_IAM_ServiceAccountObject{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "IAMServiceAccount"
	}
	if raw.TypeMeta.APIVersion == "" {
		raw.TypeMeta.APIVersion = "iam.cnrm.cloud.google.com/v1beta1"
	}
	return &GCP_IAM_ServiceAccount{
		Object: NewObject(raw),
		Raw:    raw,
	}
}

func NewGCP_IAM_ServiceAccountFromUnstructured(u *unstructured.Unstructured) (*GCP_IAM_ServiceAccount, error) {
	o := NewGCP_IAM_ServiceAccount(nil)
	if err := o.FromUnstructured(u); err != nil {
		return nil, err
	}
	return o, nil
}

func (o *GCP_IAM_ServiceAccountObject) DeepCopy() *GCP_IAM_ServiceAccountObject {
	if o == nil {
		return nil
	}
	cpy := *o
	cpy.ObjectMeta = *o.ObjectMeta.DeepCopy()
	o.ObjectMeta.DeepCopyInto(&cpy.ObjectMeta)
	return &cpy
}

func (o *GCP_IAM_ServiceAccountObject) DeepCopyObject() runtime.Object {
	return o.DeepCopy()
}

func (c Client) gcp_iam_serviceaccounts() dynamic.ResourceInterface {
	return c.Dynamic("iam.cnrm.cloud.google.com", "v1beta1", "iamserviceaccounts")
}

func (c Client) ListGCP_IAM_ServiceAccounts(ctx context.Context, opts ...ListOpt) ([]*GCP_IAM_ServiceAccount, error) {
	us, err := c.gcp_iam_serviceaccounts().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*GCP_IAM_ServiceAccount{}
	errs := []error{}
	for _, u := range us.Items {
		if o, err := NewGCP_IAM_ServiceAccountFromUnstructured(&u); err != nil {
			errs = append(errs, err)
		} else {
			ret = append(ret, o)
		}
	}
	if err := errors.Join(errs...); err != nil {
		return nil, err
	}
	return ret, nil
}

func (c Client) GetGCP_IAM_ServiceAccount(ctx context.Context, name string, opts ...GetOpt) (*GCP_IAM_ServiceAccount, error) {
	u, err := c.gcp_iam_serviceaccounts().Get(ctx, name, GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewGCP_IAM_ServiceAccountFromUnstructured(u)
}

func (c Client) CreateGCP_IAM_ServiceAccount(ctx context.Context, o *GCP_IAM_ServiceAccount) (*GCP_IAM_ServiceAccount, error) {
	u, err := o.Unstructured()
	if err != nil {
		return nil, err
	}
	u2, err := c.gcp_iam_serviceaccounts().Create(ctx, u, CreateOptions())
	if err != nil {
		return nil, err
	}
	return NewGCP_IAM_ServiceAccountFromUnstructured(u2)
}

func (c Client) UpdateGCP_IAM_ServiceAccount(ctx context.Context, o *GCP_IAM_ServiceAccount) (*GCP_IAM_ServiceAccount, error) {
	u, err := o.Unstructured()
	if err != nil {
		return nil, err
	}
	u2, err := c.gcp_iam_serviceaccounts().Update(ctx, u, UpdateOptions())
	if err != nil {
		return nil, err
	}
	return NewGCP_IAM_ServiceAccountFromUnstructured(u2)
}

func (c Client) ReplaceGCP_IAM_ServiceAccount(ctx context.Context, o *GCP_IAM_ServiceAccount) (*GCP_IAM_ServiceAccount, error) {
	if o.HasUID() {
		return c.UpdateGCP_IAM_ServiceAccount(ctx, o)
	} else {
		return c.CreateGCP_IAM_ServiceAccount(ctx, o)
	}
}

func (c Client) DeleteGCP_IAM_ServiceAccount(ctx context.Context, name string) error {
	c.LogInfo("Deleting GCP IAMServiceAccount %s...", name)
	return c.gcp_iam_serviceaccounts().Delete(ctx, name, DeleteOptions())
}

func (c Client) DeleteIfGCP_IAM_ServiceAccount(ctx context.Context, name string) error {
	if _, err := c.GetGCP_IAM_ServiceAccount(ctx, name); err == nil {
		return NotFoundOK(c.DeleteGCP_IAM_ServiceAccount(ctx, name))
	} else if IsNotFound(err) {
		c.LogInfo("Not Deleting GC IAMServiceAccount %s: already NotFound.", name)
		return nil
	} else {
		return err
	}
}

func (c Client) ApplyGCP_IAM_ServiceAccount(ctx context.Context, o *GCP_IAM_ServiceAccount, opts ...ApplyOpt) (*GCP_IAM_ServiceAccount, error) {
	u, err := o.Unstructured()
	if err != nil {
		return nil, err
	}
	opts = append([]ApplyOpt{ApplyFieldManager(c.defaultFieldManager)}, opts...)
	u2, err := c.gcp_iam_serviceaccounts().Apply(ctx, u.GetName(), u, ApplyOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewGCP_IAM_ServiceAccountFromUnstructured(u2)
}

////////////////////////////////////////////////////////////////////////////////
//
// IAM PolicyMember
//

type GCP_IAM_PolicyMember struct {
	Object
	Raw *GCP_IAM_PolicyMemberObject `json:",inline"`
}

func (o *GCP_IAM_PolicyMember) SetK8sNamespaceMember(projectNumber int, project string, namespace string) {
	o.Raw.Spec.Member = fmt.Sprintf("principalSet://iam.googleapis.com/projects/%d/locations/global/workloadIdentityPools/%s.svc.id.goog/namespace/%s", projectNumber, project, namespace)
}

func (o *GCP_IAM_PolicyMember) SetK8sServiceAccountMember(project string, namespace string, serviceAccount string) {
	o.Raw.Spec.Member = fmt.Sprintf("serviceAccount:%s.svc.id.goog[%s/%s]", project, namespace, serviceAccount)
}

type GCP_IAM_PolicyMemberObject struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec struct {
		Member      string `json:"member,omitempty"`
		ResourceRef struct {
			Kind       string `json:"kind,omitempty"`
			APIVersion string `json:"apiVersion,omitempty"`
			External   string `json:"external,omitempty"`
		} `json:"resourceRef,omitempty"`
		Role string `json:"role,omitempty"`
	} `json:"spec"`
}

func (o *GCP_IAM_PolicyMember) Unstructured() (*unstructured.Unstructured, error) {
	u, err := o.Object.Unstructured()
	if err != nil {
		return nil, err
	}
	unstructured.RemoveNestedField(u.Object, "metadata", "creationTimestamp")
	return u, nil
}

func NewGCP_IAM_PolicyMember(raw *GCP_IAM_PolicyMemberObject) *GCP_IAM_PolicyMember {
	if raw == nil {
		raw = &GCP_IAM_PolicyMemberObject{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "IAMPolicyMember"
	}
	if raw.TypeMeta.APIVersion == "" {
		raw.TypeMeta.APIVersion = "iam.cnrm.cloud.google.com/v1beta1"
	}
	return &GCP_IAM_PolicyMember{
		Object: NewObject(raw),
		Raw:    raw,
	}
}

func NewGCP_IAM_PolicyMemberFromUnstructured(u *unstructured.Unstructured) (*GCP_IAM_PolicyMember, error) {
	o := NewGCP_IAM_PolicyMember(nil)
	if err := o.FromUnstructured(u); err != nil {
		return nil, err
	}
	return o, nil
}

func (o *GCP_IAM_PolicyMemberObject) DeepCopy() *GCP_IAM_PolicyMemberObject {
	if o == nil {
		return nil
	}
	cpy := *o
	cpy.ObjectMeta = *o.ObjectMeta.DeepCopy()
	o.ObjectMeta.DeepCopyInto(&cpy.ObjectMeta)
	return &cpy
}

func (o *GCP_IAM_PolicyMemberObject) DeepCopyObject() runtime.Object {
	return o.DeepCopy()
}

func (c Client) gcp_iam_policymembers() dynamic.ResourceInterface {
	return c.Dynamic("iam.cnrm.cloud.google.com", "v1beta1", "iampolicymembers")
}

func (c Client) ListGCP_IAM_PolicyMembers(ctx context.Context, opts ...ListOpt) ([]*GCP_IAM_PolicyMember, error) {
	us, err := c.gcp_iam_policymembers().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*GCP_IAM_PolicyMember{}
	errs := []error{}
	for _, u := range us.Items {
		if o, err := NewGCP_IAM_PolicyMemberFromUnstructured(&u); err != nil {
			errs = append(errs, err)
		} else {
			ret = append(ret, o)
		}
	}
	if err := errors.Join(errs...); err != nil {
		return nil, err
	}
	return ret, nil
}

func (c Client) GetGCP_IAM_PolicyMember(ctx context.Context, name string, opts ...GetOpt) (*GCP_IAM_PolicyMember, error) {
	u, err := c.gcp_iam_policymembers().Get(ctx, name, GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewGCP_IAM_PolicyMemberFromUnstructured(u)
}

func (c Client) CreateGCP_IAM_PolicyMember(ctx context.Context, o *GCP_IAM_PolicyMember) (*GCP_IAM_PolicyMember, error) {
	u, err := o.Unstructured()
	if err != nil {
		return nil, err
	}
	u2, err := c.gcp_iam_policymembers().Create(ctx, u, CreateOptions())
	if err != nil {
		return nil, err
	}
	return NewGCP_IAM_PolicyMemberFromUnstructured(u2)
}

func (c Client) UpdateGCP_IAM_PolicyMember(ctx context.Context, o *GCP_IAM_PolicyMember) (*GCP_IAM_PolicyMember, error) {
	u, err := o.Unstructured()
	if err != nil {
		return nil, err
	}
	u2, err := c.gcp_iam_policymembers().Update(ctx, u, UpdateOptions())
	if err != nil {
		return nil, err
	}
	return NewGCP_IAM_PolicyMemberFromUnstructured(u2)
}

func (c Client) ReplaceGCP_IAM_PolicyMember(ctx context.Context, o *GCP_IAM_PolicyMember) (*GCP_IAM_PolicyMember, error) {
	if o.HasUID() {
		return c.UpdateGCP_IAM_PolicyMember(ctx, o)
	} else {
		return c.CreateGCP_IAM_PolicyMember(ctx, o)
	}
}

func (c Client) DeleteGCP_IAM_PolicyMember(ctx context.Context, name string) error {
	c.LogInfo("Deleting GCP IAMPolicyMember %s...", name)
	return c.gcp_iam_policymembers().Delete(ctx, name, DeleteOptions())
}

func (c Client) DeleteIfGCP_IAM_PolicyMember(ctx context.Context, name string) error {
	if _, err := c.GetGCP_IAM_PolicyMember(ctx, name); err == nil {
		return NotFoundOK(c.DeleteGCP_IAM_PolicyMember(ctx, name))
	} else if IsNotFound(err) {
		c.LogInfo("Not Deleting GC IAMPolicyMember %s: already NotFound.", name)
		return nil
	} else {
		return err
	}
}

func (c Client) ApplyGCP_IAM_PolicyMember(ctx context.Context, o *GCP_IAM_PolicyMember, opts ...ApplyOpt) (*GCP_IAM_PolicyMember, error) {
	u, err := o.Unstructured()
	if err != nil {
		return nil, err
	}
	opts = append([]ApplyOpt{ApplyFieldManager(c.defaultFieldManager)}, opts...)
	u2, err := c.gcp_iam_policymembers().Apply(ctx, u.GetName(), u, ApplyOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewGCP_IAM_PolicyMemberFromUnstructured(u2)
}

////////////////////////////////////////////////////////////////////////////////
//
// CloudIdentity Membership
//

type GCP_CloudIdentity_Membership struct {
	Object
	Raw *GCP_CloudIdentity_MembershipObject `json:",inline"`
}

type GCP_CloudIdentity_MembershipObject struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec struct {
		GroupRef struct {
			External string `json:"external,omitempty"`
		} `json:"groupRef,omitempty"`

		MemberKey struct {
			ID string `json:"id,omitempty"`
		} `json:"memberKey,omitempty"`

		PreferredMemberKey struct {
			ID string `json:"id,omitempty"`
		} `json:"preferredMemberKey,omitempty"`

		ResourceID string `json:"resourceID,omitempty"`

		Roles []struct {
			Name string `json:"name,omitempty"`
		} `json:"roles"`
	} `json:"spec"`
}

func (o *GCP_CloudIdentity_Membership) Unstructured() (*unstructured.Unstructured, error) {
	u, err := o.Object.Unstructured()
	if err != nil {
		return nil, err
	}
	unstructured.RemoveNestedField(u.Object, "metadata", "creationTimestamp")
	if o.Raw.Spec.MemberKey.ID == "" {
		unstructured.RemoveNestedField(u.Object, "spec", "memberKey")
	}
	if o.Raw.Spec.PreferredMemberKey.ID == "" {
		unstructured.RemoveNestedField(u.Object, "spec", "preferredMemberKey")
	}
	return u, nil
}

func (o *GCP_CloudIdentity_Membership) SetMemberRole() {
	o.Raw.Spec.Roles = []struct {
		Name string `json:"name,omitempty"`
	}{{
		Name: "MEMBER",
	}}
}

func NewGCP_CloudIdentity_Membership(raw *GCP_CloudIdentity_MembershipObject) *GCP_CloudIdentity_Membership {
	if raw == nil {
		raw = &GCP_CloudIdentity_MembershipObject{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "CloudIdentityMembership"
	}
	if raw.TypeMeta.APIVersion == "" {
		raw.TypeMeta.APIVersion = "cloudidentity.cnrm.cloud.google.com/v1beta1"
	}
	return &GCP_CloudIdentity_Membership{
		Object: NewObject(raw),
		Raw:    raw,
	}
}

func NewGCP_CloudIdentity_MembershipFromUnstructured(u *unstructured.Unstructured) (*GCP_CloudIdentity_Membership, error) {
	o := NewGCP_CloudIdentity_Membership(nil)
	if err := o.FromUnstructured(u); err != nil {
		return nil, err
	}
	return o, nil
}

func (o *GCP_CloudIdentity_MembershipObject) DeepCopy() *GCP_CloudIdentity_MembershipObject {
	if o == nil {
		return nil
	}
	cpy := *o
	cpy.ObjectMeta = *o.ObjectMeta.DeepCopy()
	o.ObjectMeta.DeepCopyInto(&cpy.ObjectMeta)
	return &cpy
}

func (o *GCP_CloudIdentity_MembershipObject) DeepCopyObject() runtime.Object {
	return o.DeepCopy()
}

func (c Client) gcp_cloudidentity_memberships() dynamic.ResourceInterface {
	return c.Dynamic("cloudidentity.cnrm.cloud.google.com", "v1beta1", "cloudidentitymemberships")
}

func (c Client) ListGCP_CloudIdentity_Memberships(ctx context.Context, opts ...ListOpt) ([]*GCP_CloudIdentity_Membership, error) {
	us, err := c.gcp_cloudidentity_memberships().List(ctx, ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	ret := []*GCP_CloudIdentity_Membership{}
	errs := []error{}
	for _, u := range us.Items {
		if o, err := NewGCP_CloudIdentity_MembershipFromUnstructured(&u); err != nil {
			errs = append(errs, err)
		} else {
			ret = append(ret, o)
		}
	}
	if err := errors.Join(errs...); err != nil {
		return nil, err
	}
	return ret, nil
}

func (c Client) GetGCP_CloudIdentity_Membership(ctx context.Context, name string, opts ...GetOpt) (*GCP_CloudIdentity_Membership, error) {
	u, err := c.gcp_cloudidentity_memberships().Get(ctx, name, GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewGCP_CloudIdentity_MembershipFromUnstructured(u)
}

func (c Client) CreateGCP_CloudIdentity_Membership(ctx context.Context, o *GCP_CloudIdentity_Membership) (*GCP_CloudIdentity_Membership, error) {
	u, err := o.Unstructured()
	if err != nil {
		return nil, err
	}
	u2, err := c.gcp_cloudidentity_memberships().Create(ctx, u, CreateOptions())
	if err != nil {
		return nil, err
	}
	return NewGCP_CloudIdentity_MembershipFromUnstructured(u2)
}

func (c Client) UpdateGCP_CloudIdentity_Membership(ctx context.Context, o *GCP_CloudIdentity_Membership) (*GCP_CloudIdentity_Membership, error) {
	u, err := o.Unstructured()
	if err != nil {
		return nil, err
	}
	u2, err := c.gcp_cloudidentity_memberships().Update(ctx, u, UpdateOptions())
	if err != nil {
		return nil, err
	}
	return NewGCP_CloudIdentity_MembershipFromUnstructured(u2)
}

func (c Client) ReplaceGCP_CloudIdentity_Membership(ctx context.Context, o *GCP_CloudIdentity_Membership) (*GCP_CloudIdentity_Membership, error) {
	if o.HasUID() {
		return c.UpdateGCP_CloudIdentity_Membership(ctx, o)
	} else {
		return c.CreateGCP_CloudIdentity_Membership(ctx, o)
	}
}

func (c Client) DeleteGCP_CloudIdentity_Membership(ctx context.Context, name string) error {
	c.LogInfo("Deleting GCP CloudIdentityMembership %s...", name)
	return c.gcp_cloudidentity_memberships().Delete(ctx, name, DeleteOptions())
}

func (c Client) DeleteIfGCP_CloudIdentity_Membership(ctx context.Context, name string) error {
	if _, err := c.GetGCP_CloudIdentity_Membership(ctx, name); err == nil {
		return NotFoundOK(c.DeleteGCP_CloudIdentity_Membership(ctx, name))
	} else if IsNotFound(err) {
		c.LogInfo("Not Deleting GC CloudIdentityMembership %s: already NotFound.", name)
		return nil
	} else {
		return err
	}
}

func (c Client) ApplyGCP_CloudIdentity_Membership(ctx context.Context, o *GCP_CloudIdentity_Membership, opts ...ApplyOpt) (*GCP_CloudIdentity_Membership, error) {
	u, err := o.Unstructured()
	if err != nil {
		return nil, err
	}
	opts = append([]ApplyOpt{ApplyFieldManager(c.defaultFieldManager)}, opts...)
	u2, err := c.gcp_cloudidentity_memberships().Apply(ctx, u.GetName(), u, ApplyOptions(opts...))
	if err != nil {
		return nil, err
	}
	return NewGCP_CloudIdentity_MembershipFromUnstructured(u2)
}

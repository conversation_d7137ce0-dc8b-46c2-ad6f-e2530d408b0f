package k8s

import (
	"encoding/json"

	rbacv1 "k8s.io/api/rbac/v1"
	rbacv1a "k8s.io/client-go/applyconfigurations/rbac/v1"
)

type RoleBinding struct {
	Object
	raw *rbacv1.RoleBinding
}

type RoleBindingApplyConfig struct {
	*rbacv1a.RoleBindingApplyConfiguration
}

func NewRoleBinding(raw *rbacv1.RoleBinding) *RoleBinding {
	if raw == nil {
		raw = &rbacv1.RoleBinding{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "RoleBinding"
	}
	return &RoleBinding{
		Object: NewObject(raw),
		raw:    raw,
	}
}

func BuildRoleBindingForSA(name, namespace, sa, role string, labels map[string]string) *RoleBinding {
	rb := NewRoleBinding(nil)
	rb.Raw().ObjectMeta.Name = name
	rb.Raw().ObjectMeta.Namespace = namespace
	rb.Raw().ObjectMeta.Labels = labels

	rb.Raw().RoleRef = rbacv1.RoleRef{
		APIGroup: rbacv1.GroupName,
		Kind:     "Role",
		Name:     role,
	}

	rb.Raw().Subjects = []rbacv1.Subject{
		{
			Kind:      rbacv1.ServiceAccountKind,
			Name:      sa,
			Namespace: namespace,
		},
	}

	return rb
}

func (o RoleBinding) Raw() *rbacv1.RoleBinding {
	return o.raw
}

func (o RoleBinding) ApplyConfig(fieldManager string) (*RoleBindingApplyConfig, error) {
	raw, err := rbacv1a.ExtractRoleBinding(o.Raw(), fieldManager)
	return &RoleBindingApplyConfig{RoleBindingApplyConfiguration: raw}, err
}

func NewRoleBindingApplyConfig(name, namespace string) *RoleBindingApplyConfig {
	return &RoleBindingApplyConfig{
		RoleBindingApplyConfiguration: rbacv1a.RoleBinding(name, namespace),
	}
}

func (cfg *RoleBindingApplyConfig) FromJSON(buf string) error {
	if buf == "" {
		return nil
	}
	return json.Unmarshal([]byte(buf), cfg.Raw())
}

func (cfg *RoleBindingApplyConfig) Raw() *rbacv1a.RoleBindingApplyConfiguration {
	if cfg == nil {
		return nil
	}
	return cfg.RoleBindingApplyConfiguration
}

func (cfg *RoleBindingApplyConfig) Name() string {
	if raw := cfg.Raw(); raw == nil {
		return ""
	} else if name := raw.Name; name == nil {
		return ""
	} else {
		return *name
	}
}

func BuildRoleBindingApplyConfigForSA(name, namespace, sa, sans, kind, role string, labels map[string]string) *RoleBindingApplyConfig {
	rb := NewRoleBindingApplyConfig(name, namespace)
	rb.WithLabels(labels)
	rb.WithRoleRef(
		rbacv1a.RoleRef().
			WithAPIGroup(rbacv1.GroupName).
			WithKind(kind).
			WithName(role),
	)
	rb.WithSubjects(
		rbacv1a.Subject().
			WithKind(rbacv1.ServiceAccountKind).
			WithName(sa).
			WithNamespace(sans),
	)
	return rb
}

package k8s

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/user"
	"sort"
	"strconv"
	"strings"

	"gopkg.in/yaml.v3"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/clientcmd/api"
)

// InClusterNamespace returns the name of the namespace from K8s in-cluster config. If readFile
// is nil, it defaults to os.ReadFile, but is swappable for testing.
func InClusterNamespace(readFile func(string) ([]byte, error)) (string, error) {
	if readFile == nil {
		readFile = os.ReadFile
	}
	if nsbuf, err := readFile("/var/run/secrets/kubernetes.io/serviceaccount/namespace"); err != nil {
		return "", err
	} else {
		return strings.TrimSpace(string(nsbuf)), nil
	}
}

// Config is a wrapper around the k8s client-go config types.
type Config struct {
	cfg     clientcmd.ClientConfig
	raw     api.Config
	rest    *rest.Config
	ns      string
	context string
}

// LoadConfig first tries kubeconfig, and then in-cluster.
func LoadConfig(context string) (*Config, error) {
	errs := []error{}
	if cfg, err := LoadKubeConfig(context); err != nil {
		errs = append(errs, err)
	} else {
		return cfg, nil
	}

	if cfg, err := LoadInClusterConfig(); err != nil {
		errs = append(errs, err)
	} else {
		return cfg, nil
	}

	return nil, errors.Join(errs...)
}

func LoadKubeConfig(context string, paths ...string) (*Config, error) {
	rules := clientcmd.NewDefaultClientConfigLoadingRules()
	if len(paths) > 0 {
		rules.Precedence = paths
		rules.WarnIfAllMissing = true
	}
	config := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(
		rules,
		&clientcmd.ConfigOverrides{
			CurrentContext: context,
		},
	)
	raw, err := config.RawConfig()
	if err != nil {
		return nil, err
	}
	rest, err := config.ClientConfig()
	if err != nil {
		return nil, err
	}
	rest.QPS = -1
	rest.Wrap(func(rt http.RoundTripper) http.RoundTripper {
		return &openApiV2WokaroundMiddleware{up: rt}
	})
	ns, _, err := config.Namespace()
	if err != nil {
		return nil, err
	}
	if context == "" {
		context = raw.CurrentContext
	}

	return &Config{
		cfg:     config,
		raw:     raw,
		rest:    rest,
		ns:      ns,
		context: context,
	}, nil
}

func LoadInClusterConfig() (*Config, error) {
	rest, err := rest.InClusterConfig()
	if err != nil {
		return nil, err
	}
	rest.QPS = -1
	rest.Wrap(func(rt http.RoundTripper) http.RoundTripper {
		return &openApiV2WokaroundMiddleware{up: rt}
	})

	ns, err := InClusterNamespace(nil)
	if err != nil {
		return nil, err
	}

	return &Config{
		rest: rest,
		ns:   string(ns),
	}, nil
}

func (cfg Config) Raw() api.Config {
	return cfg.raw
}

func (cfg Config) REST() *rest.Config {
	return cfg.rest
}

func (cfg Config) Namespace() string {
	return cfg.ns
}

func (cfg Config) Context() string {
	return cfg.context
}

func (cfg Config) Contexts() []string {
	ret := []string{}
	for c := range cfg.raw.Contexts {
		ret = append(ret, c)
	}
	sort.Strings(ret)
	return ret
}

// Username returns the username for the given context.
func (cfg Config) Username(context string) string {
	ctx, ok := cfg.Raw().Contexts[context]
	if !ok {
		return ""
	}
	auth, ok := cfg.Raw().AuthInfos[ctx.AuthInfo]
	if !ok {
		return ""
	}
	if u := auth.Username; u != "" {
		return u
	}
	return ctx.AuthInfo
}

func (cfg Config) Cluster() (string, *api.Cluster, error) {
	contextName := cfg.Context()

	context := cfg.Raw().Contexts[contextName]
	if context == nil {
		return "", nil, fmt.Errorf("context '%s' not found in config", contextName)
	}

	cluster := cfg.Raw().Clusters[context.Cluster]
	if cluster == nil {
		return context.Cluster, nil, fmt.Errorf("cluster '%s' not found in config", context.Cluster)
	}

	return context.Cluster, cluster, nil
}

// WithToken returns a new config using the given token. The "API Config" is ignored; this
// works by copying and updating the underlying rest.Config.
//
// This is meant to be used with the `AsServiceAccount()` feature of the `Client`.
func (cfg Config) WithToken(tok string) (*Config, error) {
	if cfg.REST() == nil {
		return nil, fmt.Errorf("No REST configuration to update.")
	}
	r := rest.AnonymousClientConfig(cfg.REST())
	r.BearerToken = tok
	// rest.AnonymousClientConfig does almost exactly what we want, except it doesn't retain wrapped
	// middleware which we do want to keep.
	r.Wrap(func(rt http.RoundTripper) http.RoundTripper {
		return &openApiV2WokaroundMiddleware{up: rt}
	})
	return &Config{
		rest:    r,
		ns:      cfg.ns,
		context: cfg.context,
	}, nil
}

////////////////////////////////////////////////////////////////////////////////
//
// Custom KubeConfig structs to ensure nice formatting.
//

type KubeConfig struct {
	Kind           string                   `yaml:"kind"`
	ApiVersion     string                   `yaml:"apiVersion"`
	CurrentContext string                   `yaml:"current-context"`
	Users          []KubeConfigNamedUser    `yaml:"users"`
	Clusters       []KubeConfigNamedCluster `yaml:"clusters"`
	Contexts       []KubeConfigNamedContext `yaml:"contexts"`

	buf string
}
type KubeConfigNamedUser struct {
	Name string         `yaml:"name"`
	User KubeConfigUser `yaml:"user"`
}
type KubeConfigUser struct {
	Token string `yaml:"token"`
}
type KubeConfigNamedCluster struct {
	Name    string            `yaml:"name"`
	Cluster KubeConfigCluster `yaml:"cluster"`
}
type KubeConfigCluster struct {
	Server string `yaml:"server"`
	CAData string `yaml:"certificate-authority-data"`
}
type KubeConfigNamedContext struct {
	Name    string            `yaml:"name"`
	Context KubeConfigContext `yaml:"context"`
}
type KubeConfigContext struct {
	Cluster   string `yaml:"cluster"`
	Namespace string `yaml:"namespace"`
	User      string `yaml:"user"`
}

func (kc KubeConfig) YAML() (string, error) {
	buf, err := yaml.Marshal(kc)
	if err != nil {
		return "", err
	}
	buf = append(buf, '\n')
	return string(buf), nil
}

// WithBuf calls and stores `YAML()` so that the `String()` method can be used to retrieve it without errors.
func (kc *KubeConfig) WithBuf() (*KubeConfig, error) {
	if str, err := kc.YAML(); err != nil {
		return nil, err
	} else {
		kc.buf = str
		return kc, nil
	}
}

// String returns the YAML representation of the config that was pre-generated. Call `WithBuf()` to update, or `YAML()` directly.
func (kc KubeConfig) String() string {
	return kc.buf
}

// Bytes returns the YAML representation of the config that was pre-generated. Call `WithBuf()` to update, or `YAML()` directly.
func (kc KubeConfig) Bytes() []byte {
	return []byte(kc.String())
}

func (kc KubeConfig) CurrentUser() string {
	for _, ctx := range kc.Contexts {
		if ctx.Name == kc.CurrentContext {
			return ctx.Context.User
		}
	}
	return ""
}

func (kc *KubeConfig) SetTokenForUser(user, token string) error {
	if user == "" {
		user = kc.CurrentUser()
	}
	if user == "" {
		return fmt.Errorf("no user specified")
	}
	for i, u := range kc.Users {
		if u.Name == user {
			kc.Users[i].User.Token = token
			return nil
		}
	}
	return fmt.Errorf("user %s not found in kubeconfig", user)
}

func (kc KubeConfig) WriteXdgSnippet(username, snipname string) error {
	if snipname == "" && len(kc.Contexts) > 0 {
		snipname = kc.Contexts[0].Context.User
	}
	if !strings.HasSuffix(snipname, ".conf") {
		snipname += ".conf"
	}
	u, err := user.Lookup(username)
	if err != nil {
		return err
	}
	// Make XDG_RUNTIME_DIR too since we might be on a system that hasn't set that up.
	rname := "/run/user"
	xname := fmt.Sprintf("%s/%s", rname, u.Uid)
	dname := fmt.Sprintf("%s/kubeconfig.d", xname)
	fname := fmt.Sprintf("%s/%s", dname, snipname)

	if err := os.MkdirAll(rname, 0o755); err != nil {
		return err
	} else if err := os.MkdirAll(dname, 0o700); err != nil {
		return err
	} else if uid, err := strconv.Atoi(u.Uid); err != nil {
		return err
	} else if gid, err := strconv.Atoi(u.Gid); err != nil {
		return err
	} else if err := os.Chown(xname, uid, gid); err != nil {
		return err
	} else if err := os.Chown(dname, uid, gid); err != nil {
		return err
	} else if err := os.WriteFile(fname, kc.Bytes(), 0o600); err != nil {
		return err
	} else if err := os.Chown(fname, uid, gid); err != nil {
		return err
	}
	return nil
}

func (kc *KubeConfig) SetNamespace(ctxname, namespace string) {
	for i, ctx := range kc.Contexts {
		if ctx.Name == ctxname {
			fmt.Printf("MATTM: Updating: %+v\n", kc.Contexts[i])
			kc.Contexts[i].Context.Namespace = namespace
			fmt.Printf("MATTM: Updated: %+v\n", kc.Contexts[i])
		}
	}
}

func KubeConfigFromYAML(buf string) (*KubeConfig, error) {
	kc := &KubeConfig{}
	if err := yaml.Unmarshal([]byte(buf), kc); err != nil {
		return nil, err
	}
	return kc, nil
}

func (c Client) KubeConfigFromServiceAccount(ctx context.Context, name, ctxname, ns string) (*KubeConfig, error) {
	/// Get ServiceAccount.

	sa, err := c.GetServiceAccount(ctx, name)
	if err != nil {
		return nil, err
	}

	/// Get Secret.

	sec, err := sa.TokenSecret(ctx, &c)
	if err != nil {
		return nil, err
	}

	/// From Secret get ca.crt, token, and namespace.

	cacrt, err := sec.ServiceAccountRootCA()
	if err != nil {
		return nil, err
	}
	token, err := sec.ServiceAccountToken()
	if err != nil {
		return nil, err
	}
	if ns == "" {
		var err error
		if ns, err = sec.ServiceAccountNamespace(); err != nil {
			return nil, err
		}
	}

	/// From Environment, get K8s address

	host := os.Getenv("KUBERNETES_SERVICE_HOST")
	port := os.Getenv("KUBERNETES_SERVICE_PORT")
	if port == "" {
		port = "443"
	}

	/// Build Config

	if ctxname == "" {
		ctxname = sa.Name()
	}
	cfg := &KubeConfig{
		Kind:           "Config",
		ApiVersion:     "v1",
		CurrentContext: ctxname,
		Users: []KubeConfigNamedUser{{
			Name: sa.Name(),
			User: KubeConfigUser{
				Token: token,
			},
		}},
		Clusters: []KubeConfigNamedCluster{{
			Name: sa.Name() + "-incluster",
			Cluster: KubeConfigCluster{
				Server: fmt.Sprintf("https://%s:%s", host, port),
				CAData: base64.StdEncoding.EncodeToString(cacrt),
			},
		}},
		Contexts: []KubeConfigNamedContext{{
			Name: ctxname,
			Context: KubeConfigContext{
				Cluster:   sa.Name() + "-incluster",
				User:      sa.Name(),
				Namespace: ns,
			},
		}},
	}

	return cfg.WithBuf()
}

func (c Client) KubeConfigFromTokenRequest(ctx context.Context, name, ctxname, ns string, exp int64) (*KubeConfig, error) {
	/// Get TokenRequest.

	tok, err := c.CreateToken(ctx, name, exp)
	if err != nil {
		return nil, err
	}

	/// Get CA from kube-root-ca.crt

	cm, err := c.GetConfigMap(ctx, "kube-root-ca.crt")
	if err != nil {
		return nil, err
	}
	cacrt := cm.Key("ca.crt")

	/// Get Namespace

	if ns == "" {
		ns = c.Namespace()
	}

	/// From Environment, get K8s address

	host := os.Getenv("KUBERNETES_SERVICE_HOST")
	port := os.Getenv("KUBERNETES_SERVICE_PORT")
	if port == "" {
		port = "443"
	}

	/// Build Config

	if ctxname == "" {
		ctxname = c.Config().Context()
	}
	cfg := &KubeConfig{
		Kind:           "Config",
		ApiVersion:     "v1",
		CurrentContext: ctxname,
		Users: []KubeConfigNamedUser{{
			Name: name,
			User: KubeConfigUser{
				Token: tok.Token(),
			},
		}},
		Clusters: []KubeConfigNamedCluster{{
			Name: name + "-incluster",
			Cluster: KubeConfigCluster{
				Server: fmt.Sprintf("https://%s:%s", host, port),
				CAData: base64.StdEncoding.EncodeToString([]byte(cacrt)),
			},
		}},
		Contexts: []KubeConfigNamedContext{{
			Name: ctxname,
			Context: KubeConfigContext{
				Cluster:   name + "-incluster",
				User:      name,
				Namespace: ns,
			},
		}},
	}

	return cfg.WithBuf()
}

func (c Client) KubeConfigFromServiceAccountExternal(ctx context.Context, name, ctxname, ns string) (*KubeConfig, error) {
	/// Get ServiceAccount.

	sa, err := c.GetServiceAccount(ctx, name)
	if err != nil {
		return nil, err
	}

	/// Get Secret.

	sec, err := sa.TokenSecret(ctx, &c)
	if err != nil {
		return nil, err
	}

	/// From Secret get ~ca.crt~, token, and namespace.

	token, err := sec.ServiceAccountToken()
	if err != nil {
		return nil, err
	}
	if ns == "" {
		var err error
		if ns, err = sec.ServiceAccountNamespace(); err != nil {
			return nil, err
		}
	}

	/// From current KubeConfig, get ca.crt and server.

	cfgClusterName, cfgCluster, err := c.Config().Cluster()
	if err != nil {
		return nil, err
	}

	/// Build Config

	if ctxname == "" {
		ctxname = sa.Name()
	}
	cfg := &KubeConfig{
		Kind:           "Config",
		ApiVersion:     "v1",
		CurrentContext: ctxname,
		Users: []KubeConfigNamedUser{{
			Name: sa.Name(),
			User: KubeConfigUser{
				Token: token,
			},
		}},
		Clusters: []KubeConfigNamedCluster{{
			Name: cfgClusterName,
			Cluster: KubeConfigCluster{
				Server: cfgCluster.Server,
				CAData: base64.StdEncoding.EncodeToString(cfgCluster.CertificateAuthorityData),
			},
		}},
		Contexts: []KubeConfigNamedContext{{
			Name: ctxname,
			Context: KubeConfigContext{
				Cluster:   cfgClusterName,
				User:      sa.Name(),
				Namespace: ns,
			},
		}},
	}

	return cfg.WithBuf()
}

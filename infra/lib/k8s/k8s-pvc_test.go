package k8s

import (
	"strings"
	"testing"

	"k8s.io/apimachinery/pkg/api/resource"

	corev1 "k8s.io/api/core/v1"
)

func TestPVCAccessMode(t *testing.T) {
	tests := map[string]struct {
		modeS string
		modeT corev1.PersistentVolumeAccessMode
		want  corev1.PersistentVolumeAccessMode
	}{
		/// ReadWriteOnce

		"ReadWriteOnce-string": {
			modeS: "ReadWriteOnce",
			want:  corev1.ReadWriteOnce,
		},
		"ReadWriteOnce-typed": {
			modeT: corev1.PersistentVolumeAccessMode("ReadWriteOnce"),
			want:  corev1.ReadWriteOnce,
		},
		"ReadWriteOnce-typed-case": {
			modeT: corev1.PersistentVolumeAccessMode("READWRITEONCE"),
			want:  corev1.ReadWriteOnce,
		},
		"ReadWriteOnce-alias": {
			modeS: "rwo",
			want:  corev1.ReadWriteOnce,
		},
		"ReadWriteOnce-alias-typed": {
			modeT: corev1.PersistentVolumeAccessMode("rwo"),
			want:  corev1.ReadWriteOnce,
		},

		/// ReadWriteMany

		"ReadWriteMany-string": {
			modeS: "ReadWriteMany",
			want:  corev1.ReadWriteMany,
		},
		"ReadWriteMany-typed": {
			modeT: corev1.PersistentVolumeAccessMode("ReadWriteMany"),
			want:  corev1.ReadWriteMany,
		},
		"ReadWriteMany-typed-case": {
			modeT: corev1.PersistentVolumeAccessMode("READWRITEMANY"),
			want:  corev1.ReadWriteMany,
		},
		"ReadWriteMany-alias": {
			modeS: "rwx",
			want:  corev1.ReadWriteMany,
		},
		"ReadWriteMany-alias-typed": {
			modeT: corev1.PersistentVolumeAccessMode("rwx"),
			want:  corev1.ReadWriteMany,
		},

		/// ReadWriteOncePod

		"ReadWriteOncePod-string": {
			modeS: "ReadWriteOncePod",
			want:  corev1.ReadWriteOncePod,
		},
		"ReadWriteOncePod-typed": {
			modeT: corev1.PersistentVolumeAccessMode("ReadWriteOncePod"),
			want:  corev1.ReadWriteOncePod,
		},
		"ReadWriteOncePod-typed-case": {
			modeT: corev1.PersistentVolumeAccessMode("READWRITEONCEPOD"),
			want:  corev1.ReadWriteOncePod,
		},
		"ReadWriteOncePod-alias": {
			modeS: "rwop",
			want:  corev1.ReadWriteOncePod,
		},
		"ReadWriteOncePod-alias-typed": {
			modeT: corev1.PersistentVolumeAccessMode("rwop"),
			want:  corev1.ReadWriteOncePod,
		},

		/// Unknown

		"unknown": {
			modeS: "unknown",
			want:  corev1.PersistentVolumeAccessMode("unknown"),
		},
		"unknown-case": {
			modeS: "Unknown",
			want:  corev1.PersistentVolumeAccessMode("Unknown"),
		},
		"unknown-case-typed": {
			modeT: corev1.PersistentVolumeAccessMode("Unknown"),
			want:  corev1.PersistentVolumeAccessMode("Unknown"),
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			if tc.modeS != "" {
				if got, want := PVCAccessMode(tc.modeS), tc.want; got != want {
					t.Errorf("string: got %v, want %v.", got, want)
				}
			} else {
				if got, want := PVCAccessMode(tc.modeT), tc.want; got != want {
					t.Errorf("string: got %v, want %v.", got, want)
				}
			}
		})
	}
}

func TestGrowSize(t *testing.T) {
	tests := map[string]struct {
		orig    string
		arg     string
		want    string
		wantErr string
	}{
		"replace": {
			orig: "10Gi",
			arg:  "15Gi",
			want: "15Gi",
		},
		"replace-match-units": {
			orig: "10Gi",
			arg:  "15",
			want: "15Gi",
		},
		"replace-new-units": {
			orig: "10Gi",
			arg:  "15Ti",
			want: "15Ti",
		},
		"replace-float": {
			orig: "10Gi",
			arg:  "10.5Gi",
			want: "10752Mi",
		},
		"add": {
			orig: "10Gi",
			arg:  "+15Gi",
			want: "25Gi",
		},
		"add-match-units": {
			orig: "10Gi",
			arg:  "+15",
			want: "25Gi",
		},
		"add-new-units": {
			orig: "10Gi",
			arg:  "+15Ti",
			want: "15370Gi",
		},
		"add-float": {
			orig: "10Gi",
			arg:  "+10.5Gi",
			want: "20992Mi",
		},
		"err": {
			orig:    "10Gi",
			arg:     "_bad_",
			want:    "0",
			wantErr: "quantities must match the regular expression",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			orig, err := resource.ParseQuantity(tc.orig)
			if err != nil {
				t.Fatalf("Unable to parse orig %v: %v.", orig, err)
			}

			got, gotErr := GrowSize(orig, tc.arg)

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got err %v, want no err.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want err containing '%v'.", gotErr, tc.wantErr)
			}

			if got, want := got.String(), tc.want; got != want {
				t.Errorf("got %v, want %v.", got, want)
			}
		})
	}
}

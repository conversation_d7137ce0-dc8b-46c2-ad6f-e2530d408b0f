package k8s

import (
	"encoding/json"

	yaml "github.com/goccy/go-yaml"
	corev1 "k8s.io/api/core/v1"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"
)

type Namespace struct {
	Object
	raw *corev1.Namespace
}

type NamespaceApplyConfig struct {
	*corev1a.NamespaceApplyConfiguration
}

func NewNamespace(raw *corev1.Namespace) *Namespace {
	if raw == nil {
		raw = &corev1.Namespace{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "Namespace"
	}
	return &Namespace{
		Object: NewObject(raw),
		raw:    raw,
	}
}

func (o Namespace) Raw() *corev1.Namespace {
	return o.raw
}

func (o Namespace) ApplyConfig(fieldManager string) (*NamespaceApplyConfig, error) {
	raw, err := corev1a.ExtractNamespace(o.Raw(), fieldManager)
	return &NamespaceApplyConfig{NamespaceApplyConfiguration: raw}, err
}

func NewNamespaceApplyConfig(name string) *NamespaceApplyConfig {
	return &NamespaceApplyConfig{
		NamespaceApplyConfiguration: corev1a.Namespace(name),
	}
}

func (cfg *NamespaceApplyConfig) FromJSON(buf string) error {
	if buf == "" {
		return nil
	}
	return json.Unmarshal([]byte(buf), cfg.Raw())
}

func (cfg *NamespaceApplyConfig) Raw() *corev1a.NamespaceApplyConfiguration {
	if cfg == nil {
		return nil
	}
	return cfg.NamespaceApplyConfiguration
}

func (cfg *NamespaceApplyConfig) Name() string {
	if raw := cfg.Raw(); raw == nil {
		return ""
	} else if name := raw.Name; name == nil {
		return ""
	} else {
		return *name
	}
}

func (cfg *NamespaceApplyConfig) YAML() (string, error) {
	buf, err := yaml.MarshalWithOptions(cfg.Raw(), yaml.UseJSONMarshaler(), yaml.CustomMarshaler(func(b []byte) ([]byte, error) {
		// Avoid printing lists of individual bytes
		return b, nil
	}))
	return string(buf), err
}

func (cfg *NamespaceApplyConfig) JSON() (string, error) {
	buf, err := json.MarshalIndent(cfg.Raw(), "", "  ")
	return string(buf), err
}

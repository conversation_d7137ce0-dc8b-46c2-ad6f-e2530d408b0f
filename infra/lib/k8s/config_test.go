package k8s

import (
	"fmt"
	"strings"
	"testing"
)

func TestInClusterNamespace(t *testing.T) {
	tests := map[string]struct {
		fBuf    string
		fErr    error
		want    string
		wantErr string
	}{
		"err": {
			fBuf:    "ns0",
			fErr:    fmt.<PERSON>rrorf("err0"),
			want:    "",
			wantErr: "err0",
		},
		"ok": {
			fBuf:    "\t\tns0\n",
			fErr:    nil,
			want:    "ns0",
			wantErr: "",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			got, gotErr := InClusterNamespace(func(_ string) ([]byte, error) {
				return []byte(tc.fBuf), tc.fErr
			})
			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got err %v, want no err.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want err containing '%v'.", gotErr, tc.wantErr)
			}
			if want := tc.want; got != want {
				t.Errorf("got %v, want %v.", got, want)
			}
		})
	}
}

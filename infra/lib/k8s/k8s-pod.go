package k8s

import (
	"context"
	"fmt"
	"iter"
	"regexp"
	"sort"
	"strings"

	corev1 "k8s.io/api/core/v1"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"
	rh "k8s.io/component-helpers/resource"
)

type Pod struct {
	Object
	raw *corev1.Pod
}

type PodApplyConfig struct {
	*corev1a.PodApplyConfiguration
}

func NewPod(raw *corev1.Pod) *Pod {
	if raw == nil {
		raw = &corev1.Pod{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "Pod"
	}
	return &Pod{
		Object: NewObject(raw),
		raw:    raw,
	}
}

func (o Pod) Raw() *corev1.Pod {
	return o.raw
}

func (o Pod) StatusLine() string {
	return fmt.Sprintf("pod/%s: %s", o.Name(), o.Status().String())
}

func (o Pod) Status() PodStatus {
	return PodStatus{o.Raw().Status}
}

func (o Pod) Logs(ctx context.Context, c *Client, opts ...PodLogOpt) iter.Seq2[string, error] {
	return c.PodLogs(ctx, o.Name(), opts...)
}

func (o Pod) CanonicalGPUType() string {
	return CanonicalGpu(o.GPUType())
}

func (o Pod) GPUType() string {
	aff := o.Raw().Spec.Affinity
	if aff == nil {
		return ""
	}
	na := aff.NodeAffinity
	if na == nil {
		return ""
	}
	r := na.RequiredDuringSchedulingIgnoredDuringExecution
	if r == nil {
		return ""
	}
	nst := r.NodeSelectorTerms
	if nst == nil {
		return ""
	}
	for _, term := range nst {
		for _, expr := range term.MatchExpressions {
			if expr.Key == "gpu.nvidia.com/class" || expr.Key == "cloud.google.com/gke-accelerator" {
				return expr.Values[0]
			}
		}
	}
	return ""
}

func (o Pod) GPUCount() int {
	count := 0
	for _, c := range o.Raw().Spec.Containers {
		lim := c.Resources.Limits
		gpu, ok := lim["nvidia.com/gpu"]
		if !ok {
			// fall back to requeests
			req := c.Resources.Requests
			gpu, ok = req["nvidia.com/gpu"]
			if !ok {
				continue
			}
		}
		count += int(gpu.Value())
	}
	return count
}

func (o Pod) NodeName() string {
	return o.Raw().Spec.NodeName
}

// Requests returns the pod's total requested resources, which should be what is used for
// scheduling (both how much a pod is requesting, and how much is available on a node by summing pods).
// This includes the sum of container requests, sidecar containers, and pod-level requests (alpha).
func (o Pod) Requests() corev1.ResourceList {
	return rh.PodRequests(o.Raw(), rh.PodResourcesOptions{
		ExcludeOverhead:       false,
		SkipPodLevelResources: false,
	})
}

func (o Pod) AugmentUser() string {
	// Get the user
	labels := o.Labels()
	if user := labels["aug.user"]; user != "" {
		return user
	}
	if user := labels["augmentDeterminedUser"]; user != "" {
		return user
	}
	if app := labels["spark-app-name"]; app != "" {
		return strings.Split(app, "-")[0]
	}

	if user := o.Annotation("determined.ai/user"); user != "" {
		return user
	}

	// Now we're really just guessing.
	if parts := strings.Split(o.Name(), "-"); len(parts) > 1 {
		return parts[0]
	}
	re := regexp.MustCompile(`\d`)
	if parts := re.Split(o.Name(), -1); len(parts) > 1 {
		return parts[0]
	}
	return ""
}

type PodStatus struct {
	corev1.PodStatus
}

func (s PodStatus) String() string {
	return fmt.Sprintf("%s", s.Phase)
}

func (s PodStatus) Conditions() []PodCondition {
	cs := []PodCondition{}
	for _, c := range s.PodStatus.Conditions {
		cs = append(cs, PodCondition{c})
	}
	sort.Slice(cs, func(i, j int) bool {
		ti := &cs[i].LastTransitionTime
		tj := &cs[j].LastTransitionTime
		return ti.Before(tj)
	})
	return cs
}

func (s PodStatus) LatestCondition() PodCondition {
	latest := PodCondition{}
	for _, c := range s.Conditions() {
		if (&latest.LastTransitionTime).Before(&c.LastTransitionTime) {
			latest = c
		}
	}
	return latest
}

func (s PodStatus) Condition(t corev1.PodConditionType) PodCondition {
	for _, c := range s.PodStatus.Conditions {
		if c.Type == t {
			return PodCondition{c}
		}
	}
	return PodCondition{}
}

type PodCondition struct {
	corev1.PodCondition
}

func (c PodCondition) String() string {
	t := c.LastTransitionTime.Format("2006-01-02 15:04 MST")
	s := fmt.Sprintf("%s %s=%s", t, c.Type, c.Status)
	if c.Reason != "" || c.Message != "" {
		s += ":"
	}
	if c.Reason != "" {
		s += fmt.Sprintf(" [%s]", c.Reason)
	}
	if c.Message != "" {
		s += " " + c.Message
	}
	return s
}

func (o Pod) ApplyConfig(fieldManager string) (*PodApplyConfig, error) {
	raw, err := corev1a.ExtractPod(o.Raw(), fieldManager)
	return &PodApplyConfig{PodApplyConfiguration: raw}, err
}

func NewPodApplyConfig(name, namespace string) *PodApplyConfig {
	return &PodApplyConfig{
		PodApplyConfiguration: corev1a.Pod(name, namespace),
	}
}

func (cfg *PodApplyConfig) Raw() *corev1a.PodApplyConfiguration {
	if cfg == nil {
		return nil
	}
	return cfg.PodApplyConfiguration
}

func (cfg *PodApplyConfig) Name() string {
	if raw := cfg.Raw(); raw == nil {
		return ""
	} else if name := raw.Name; name == nil {
		return ""
	} else {
		return *name
	}
}

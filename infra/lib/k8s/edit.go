package k8s

import (
	"context"
	"fmt"
	"os"
	"os/exec"
)

const (
	DefaultEditor = "vim"
)

func editor() string {
	if e := os.Getenv("EDITOR"); e != "" {
		return e
	}
	return DefaultEditor
}

func (c Client) edit(
	ctx context.Context,
	o *Object,
	msg string,
	reread func(string) error,
	update func(context.Context) error,
) error {
	// Use XDG_RUNTIME_DIR for temp files, since we may be working with secrets we don't want
	// to make a mistake in shared /tmp. XDG_RUNTIME_DIR should always be in memory as well.
	xdgRunDir := os.Getenv("XDG_RUNTIME_DIR")
	if xdgRunDir == "" {
		return fmt.Errorf("XDG_RUNTIME_DIR is required for edit")
	}

	file, err := os.CreateTemp(xdgRunDir, fmt.Sprintf("edit-%s-%s-*.yaml", o.<PERSON>(), o.Name()))
	if err != nil {
		return err
	}
	defer func() {
		if err := file.Close(); err != nil {
			c.LogErr("error closing temporary secret edit file '%s': %v.", file.Name(), err)
		}
		if err := os.Remove(file.Name()); err != nil {
			c.LogErr("error removing temporary secret edit file '%s': %v.", file.Name(), err)
		}
	}()

	if yaml, err := o.YAML(); err != nil {
		return err
	} else if err := file.Truncate(0); err != nil {
		return err
	} else if _, err := file.WriteAt([]byte(msg+"\n\n"+yaml), 0); err != nil {
		return err
	} else if err := file.Sync(); err != nil {
		return err
	}

	for {
		cmd := exec.CommandContext(ctx, editor(), file.Name())
		cmd.Stdin, cmd.Stdout, cmd.Stderr = os.Stdin, os.Stdout, os.Stderr
		if err := cmd.Run(); err != nil {
			return err
		}

		buf, err := os.ReadFile(file.Name())
		if err != nil {
			return err
		}

		if err := reread(string(buf)); err != nil {
			c.LogWarn("Error re-reading %s: %v.", o.ShortName(), err)
			continue
		}

		if err := update(ctx); err != nil {
			c.LogWarn("Error updating %s: %v.", o.ShortName(), err)
			continue
		}

		return nil
	}
}

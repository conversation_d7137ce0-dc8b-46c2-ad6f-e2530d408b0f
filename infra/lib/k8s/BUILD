load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(default_visibility = ["//infra:internal"])

go_library(
    name = "k8s",
    srcs = [
        "api-options.go",
        "config.go",
        "config_openapiv2_workaround.go",
        "edit.go",
        "json.go",
        "k8s.go",
        "k8s-admission-review.go",
        "k8s-config-connector.go",
        "k8s-configmap.go",
        "k8s-deployment.go",
        "k8s-event.go",
        "k8s-ingress.go",
        "k8s-namespace.go",
        "k8s-node.go",
        "k8s-object.go",
        "k8s-pod.go",
        "k8s-pvc.go",
        "k8s-replica-set.go",
        "k8s-role-binding.go",
        "k8s-sealed-secret.go",
        "k8s-secret.go",
        "k8s-self-subject-review.go",
        "k8s-service.go",
        "k8s-service-account.go",
        "k8s-stateful-set.go",
        "k8s-token-request.go",
        "k8s-volume-snapshot.go",
        "kubeseal.go",
        "quantity.go",
        "sync.go",
    ],
    importpath = "github.com/augmentcode/augment/infra/lib/k8s",
    deps = [
        "//infra/lib/docker",
        "//infra/lib/logger",
        "@com_github_bitnami_labs_sealed_secrets//pkg/apis/sealedsecrets/v1alpha1",
        "@com_github_bitnami_labs_sealed_secrets//pkg/kubeseal",
        "@com_github_goccy_go_yaml//:go-yaml",
        "@com_github_golang_protobuf//proto",
        "@com_github_google_gnostic_models//openapiv2",
        "@com_github_kubernetes_csi_external_snapshotter_client_v8//apis/volumesnapshot/v1:volumesnapshot",
        "@com_github_kubernetes_csi_external_snapshotter_client_v8//clientset/versioned/typed/volumesnapshot/v1:volumesnapshot",
        "@com_github_pmezard_go_difflib//difflib",
        "@in_gopkg_yaml_v3//:yaml_v3",
        "@io_k8s_api//admission/v1:admission",
        "@io_k8s_api//apps/v1:apps",
        "@io_k8s_api//authentication/v1:authentication",
        "@io_k8s_api//core/v1:core",
        "@io_k8s_api//networking/v1:networking",
        "@io_k8s_api//rbac/v1:rbac",
        "@io_k8s_apimachinery//pkg/api/errors",
        "@io_k8s_apimachinery//pkg/api/meta",
        "@io_k8s_apimachinery//pkg/api/resource",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_apimachinery//pkg/apis/meta/v1/unstructured",
        "@io_k8s_apimachinery//pkg/fields",
        "@io_k8s_apimachinery//pkg/runtime",
        "@io_k8s_apimachinery//pkg/runtime/schema",
        "@io_k8s_apimachinery//pkg/types",
        "@io_k8s_apimachinery//pkg/util/intstr",
        "@io_k8s_apimachinery//pkg/watch",
        "@io_k8s_client_go//applyconfigurations/apps/v1:apps",
        "@io_k8s_client_go//applyconfigurations/core/v1:core",
        "@io_k8s_client_go//applyconfigurations/meta/v1:meta",
        "@io_k8s_client_go//applyconfigurations/rbac/v1:rbac",
        "@io_k8s_client_go//dynamic",
        "@io_k8s_client_go//kubernetes",
        "@io_k8s_client_go//kubernetes/scheme",
        "@io_k8s_client_go//kubernetes/typed/apps/v1:apps",
        "@io_k8s_client_go//kubernetes/typed/authentication/v1:authentication",
        "@io_k8s_client_go//kubernetes/typed/core/v1:core",
        "@io_k8s_client_go//kubernetes/typed/networking/v1:networking",
        "@io_k8s_client_go//kubernetes/typed/rbac/v1:rbac",
        "@io_k8s_client_go//rest",
        "@io_k8s_client_go//tools/clientcmd",
        "@io_k8s_client_go//tools/clientcmd/api",
        "@io_k8s_client_go//tools/remotecommand",
        "@io_k8s_component_helpers//resource",
        "@io_k8s_component_helpers//scheduling/corev1",
        "@io_k8s_component_helpers//scheduling/corev1/nodeaffinity",
        "@io_k8s_sigs_json//:json",
        "@org_golang_x_sync//errgroup",
    ],
)

go_test(
    name = "k8s_test",
    srcs = [
        "config_test.go",
        "k8s-pvc_test.go",
        "quantity_test.go",
    ],
    embed = [":k8s"],
    deps = [
        "@io_k8s_api//core/v1:core",
        "@io_k8s_apimachinery//pkg/api/resource",
    ],
)

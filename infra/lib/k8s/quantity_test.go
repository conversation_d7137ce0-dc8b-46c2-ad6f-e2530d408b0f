package k8s

import (
	"testing"

	"k8s.io/apimachinery/pkg/api/resource"
)

func quantT(t *testing.T, s string) resource.Quantity {
	t.Helper()
	if s == "" {
		return resource.Quantity{}
	}
	q, err := resource.ParseQuantity(s)
	if err != nil {
		t.Fatalf("resource.ParseQuantity(%s): %v.", s, err)
	}
	return q
}

func TestHumanize(t *testing.T) {
	tests := map[string]struct {
		q    resource.Quantity
		want string
	}{
		"empty": {
			q:    quantT(t, ""),
			want: "0",
		},
		"decimal": {
			q:    quantT(t, "1G"),
			want: "1G",
		},
		"binary": {
			q:    quantT(t, "1Gi"),
			want: "1Gi",
		},
		"decimal-multiple": {
			q:    quantT(t, "1000G"),
			want: "1T",
		},
		"binary-multiple": {
			q:    quantT(t, "1024Gi"),
			want: "1Ti",
		},
		"decimal-fraction": {
			q:    quantT(t, "1500G"),
			want: "1.5T",
		},
		"binary-fraction": {
			q:    quantT(t, "1536Gi"),
			want: "1.5Ti",
		},
		"decimal-negative": {
			q:    quantT(t, "-1500G"),
			want: "-1.5T",
		},
		"binary-negative": {
			q:    quantT(t, "-1536Gi"),
			want: "-1.5Ti",
		},
		"millis": {
			q:    quantT(t, "0.1119"),
			want: "112m",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			if got, want := Humanize(tc.q), tc.want; got != want {
				t.Errorf("Humanize(): got %v (%v), want %v.", got, tc.q.AsApproximateFloat64(), want)
			}
		})
	}
}

package k8s

import (
	"fmt"
	"strings"

	authv1 "k8s.io/api/authentication/v1"
)

type SelfSubjectReview struct {
	Object
	raw *authv1.SelfSubjectReview
}

func NewSelfSubjectReview(raw *authv1.SelfSubjectReview) *SelfSubjectReview {
	if raw == nil {
		raw = &authv1.SelfSubjectReview{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "SelfSubjectReview"
	}
	return &SelfSubjectReview{
		Object: NewObject(raw),
		raw:    raw,
	}
}

func (o SelfSubjectReview) Raw() *authv1.SelfSubjectReview {
	return o.raw
}

func (o SelfSubjectReview) UserInfo() authv1.UserInfo {
	return o.Raw().Status.UserInfo
}

func (o SelfSubjectReview) Username() string {
	return o.UserInfo().Username
}

func (o SelfSubjectReview) UID() string {
	return o.UserInfo().UID
}

func (o SelfSubjectReview) Groups() []string {
	return o.UserInfo().Groups
}

func (o SelfSubjectReview) UserInfoString() string {
	return fmt.Sprintf("[%s] %s; groups: %v", o.UID(), o.Username(), strings.Join(o.Groups(), ", "))
}

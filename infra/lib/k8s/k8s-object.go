package k8s

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	yaml "github.com/goccy/go-yaml"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	kjson "sigs.k8s.io/json"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type ObjectInterface interface {
	metav1.Object
	runtime.Object
}

type Object struct {
	i ObjectInterface
}

func NewObject(i ObjectInterface) Object {
	return Object{i: i}
}

func (o Object) YAML() (string, error) {
	o.i.SetManagedFields(nil)
	buf, err := yaml.MarshalWithOptions(o.i, yaml.UseJSONMarshaler(), yaml.CustomMarshaler(func(b []byte) ([]byte, error) {
		// Avoid printing lists of individual bytes
		return b, nil
	}))
	return string(buf), err
}

func (o Object) FromYAML(buf string) error {
	if buf == "" {
		return nil
	}
	if o.i == nil {
		return fmt.Errorf("cannot unmarshal into nil object, try creating with New*(nil) first")
	}
	return yaml.UnmarshalWithOptions([]byte(buf), o.i, yaml.UseJSONUnmarshaler())
}

func (o Object) FromYAMLStrict(buf string) error {
	if buf == "" {
		return nil
	}
	if o.i == nil {
		return fmt.Errorf("cannot unmarshal into nil object, try creating with New*(nil) first")
	}
	return yaml.UnmarshalWithOptions([]byte(buf), o.i, yaml.UseJSONUnmarshaler(), yaml.Strict())
}

func (o Object) JSON() (string, error) {
	buf, err := json.MarshalIndent(o.i, "", "  ")
	return string(buf), err
}

func (o Object) FromJSON(buf string) error {
	if buf == "" {
		return nil
	}
	if o.i == nil {
		return fmt.Errorf("cannot unmarshal into nil object, try creating with New*(nil) first")
	}
	return kjson.UnmarshalCaseSensitivePreserveInts([]byte(buf), o.i)
}

func (o Object) Unstructured() (*unstructured.Unstructured, error) {
	conv := runtime.DefaultUnstructuredConverter
	if m, err := conv.ToUnstructured(o.i); err != nil {
		return nil, err
	} else {
		u := &unstructured.Unstructured{}
		u.SetUnstructuredContent(m)
		return u, nil
	}
}

func (o Object) FromUnstructured(u runtime.Unstructured) error {
	conv := runtime.DefaultUnstructuredConverter
	return conv.FromUnstructured(u.UnstructuredContent(), o.i)
}

func (o Object) DeepCopy() Object {
	return Object{
		i: o.i.DeepCopyObject().(ObjectInterface),
	}
}

// ReplaceFrom is useful when generating o2 from scratch, but with the intent to
// update o1. It copies: UID, CreationTimestamp, Owners, Namespace, and ResourceVersion.
// UID alone might be the only /necessary/ field, but the others seem safe (generic) and
// eliminate common diff noise. Existing finalizers are preserved and new ones are appended.
func (o2 *Object) ReplaceFrom(o1 Object) {
	o2.SetNamespace(o1.Namespace())
	o2.SetUID(o1.UID())
	o2.CopyOwnerFrom(o1)
	o2.SetCreationTimestamp(o1.CreationTimestamp())
	o2.SetResourceVersion(o1.ResourceVersion())

	existingFinalizers := map[string]bool{}
	for _, f := range o1.Finalizers() {
		existingFinalizers[f] = true
	}

	newf := o1.Finalizers() // Start with the old list.
	for _, f := range o2.Finalizers() {
		if !existingFinalizers[f] {
			newf = append(newf, f) // Append only net-new finalizers.
		}
	}
	o2.SetFinalizers(newf)
}

////////////////////////////////////////////////////////////////////////////////
//
// TypeMeta
//

func (o Object) gvk() schema.GroupVersionKind {
	gvk := o.i.GetObjectKind().GroupVersionKind()
	gv := map[string]string{
		"Namespace":             "v1",
		"ConfigMap":             "v1",
		"Event":                 "v1",
		"Pod":                   "v1",
		"PersistentVolumeClaim": "v1",
		"Secret":                "v1",
		"SealedSecret":          "bitnami.com/v1alpha1",
		"Service":               "v1",
		"ServiceAccount":        "v1",
		"Deployment":            "apps/v1",
		"ReplicaSet":            "apps/v1",
		"Ingress":               "networking.k8s.io/v1",
		"RoleBinding":           "rbac.authorization.k8s.io/v1",
		"Role":                  "rbac.authorization.k8s.io/v1",
		"ClusterRoleBinding":    "rbac.authorization.k8s.io/v1",
		"ClusterRole":           "rbac.authorization.k8s.io/v1",
		"VolumeSnapshot":        "snapshot.storage.k8s.io/v1",
	}
	defg, defv, _ := strings.Cut(gv[gvk.Kind], "/")
	if defv == "" {
		defg, defv = "", defg
	}
	if gvk.Group == "" {
		gvk.Group = defg
	}
	if gvk.Version == "" {
		gvk.Version = defv
	}
	return gvk
}

func (o Object) Group() string {
	return o.gvk().Group
}

func (o Object) Version() string {
	return o.gvk().Version
}

func (o Object) Kind() string {
	return o.gvk().Kind
}

// NOTE(matt): There's a better way to do this.
func (o Object) ShortKind() string {
	dict := map[string]string{
		"Namespace":             "ns",
		"Node":                  "no",
		"ConfigMap":             "cm",
		"Event":                 "ev",
		"Pod":                   "pod",
		"PersistentVolumeClaim": "pvc",
		"Secret":                "secret",
		"SealedSecret":          "sealedsecret",
		"Service":               "svc",
		"ServiceAccount":        "sa",
		"Deployment":            "deploy",
		"ReplicaSet":            "rs",
		"Ingress":               "ing",
		"RoleBinding":           "rolebinding",
		"Role":                  "role",
		"ClusterRoleBinding":    "clusterrolebinding",
		"ClusterRole":           "clusterrole",
		"VolumeSnapshot":        "vs",
	}
	if k := dict[o.Kind()]; k != "" {
		return k
	} else {
		return "??"
	}
}

// GVR returns the GroupVersionResource for a given object. It's based on a local list of well-known resources for simplicity. If needed,
// the discovery client can be used to perform the mapping against the API server.
func (o Object) GVR() schema.GroupVersionResource {
	mapping := map[string]string{
		"ConfigMap":             "configmaps",
		"Pod":                   "pods",
		"PersistentVolumeClaim": "persistentvolumeclaims",
		"Secret":                "secrets",
		"SealedSecret":          "sealedsecrets",
		"Service":               "services",
		"ServiceAccount":        "serviceaccounts",
		"Deployment":            "deployments",
		"ReplicaSet":            "replicasets",
		"Ingress":               "ingresses",
		"RoleBinding":           "rolebindings",
		"Role":                  "roles",
	}
	gvk := o.gvk()
	gvr := schema.GroupVersionResource{
		Group:    gvk.Group,
		Version:  gvk.Version,
		Resource: mapping[gvk.Kind],
	}
	if gvr.Resource == "" {
		gvr.Resource = "__unmapped_resource_" + gvk.Kind + "__"
	}
	return gvr
}

////////////////////////////////////////////////////////////////////////////////
//
// ObjectMeta
//

func (o Object) UIDt() types.UID {
	return o.i.GetUID()
}

func (o Object) UIDtOrTODO() types.UID {
	if u := o.UIDt(); u != "" {
		return u
	}
	return types.UID(fmt.Sprintf("TODO:%s/%s", strings.ToLower(o.Kind()), o.Name()))
}

func (o Object) UID() string {
	return string(o.i.GetUID())
}

func (o Object) HasUID() bool {
	return o.UID() != ""
}

func (o *Object) SetUID(s string) {
	o.i.SetUID(types.UID(s))
}

func (o Object) Name() string {
	return o.i.GetName()
}

func (o *Object) SetName(name string) {
	o.i.SetName(name)
}

func (o Object) ShortName() string {
	return o.ShortKind() + "/" + o.Name()
}

func (o Object) Namespace() string {
	return o.i.GetNamespace()
}

func (o *Object) SetNamespace(s string) {
	o.i.SetNamespace(s)
}

func (o Object) Labels() map[string]string {
	return o.i.GetLabels()
}

func (o Object) Label(key string) string {
	return o.Labels()[key]
}

func (o Object) SetLabel(key, value string) {
	if o.Labels() == nil {
		o.i.SetLabels(map[string]string{})
	}
	o.Labels()[key] = value
}

func (o Object) Annotations() map[string]string {
	return o.i.GetAnnotations()
}

func (o Object) Annotation(key string) string {
	return o.Annotations()[key]
}

func (o Object) SetAnnotation(key, value string) {
	if o.Annotations() == nil {
		o.i.SetAnnotations(map[string]string{})
	}
	o.Annotations()[key] = value
}

func (o Object) Revision() int {
	if str := o.Annotations()["deployment.kubernetes.io/revision"]; str == "" {
		return 0
	} else if i, err := strconv.Atoi(str); err != nil {
		// TODO(mattm): log error
		return 0
	} else {
		return i
	}
}

func (o Object) Owners() []metav1.OwnerReference {
	return o.i.GetOwnerReferences()
}

func (o Object) OwnerMatch(other Object) bool {
	for _, owner := range o.Owners() {
		if owner.Kind == other.Kind() && owner.Name == other.Name() {
			return true
		}
	}
	return false
}

// ClearOwners clears any existing OwnerReferences.
func (o *Object) ClearOwners() {
	o.i.SetOwnerReferences(nil)
}

// SetOwner clears any existing OwnerReferences and sets `other` as the owner.
func (o *Object) SetOwner(owner Object) {
	o.i.SetOwnerReferences([]metav1.OwnerReference{{
		APIVersion:         owner.Version(),
		Kind:               owner.Kind(),
		Name:               owner.Name(),
		UID:                owner.UIDtOrTODO(),
		BlockOwnerDeletion: func() *bool { b := true; return &b }(),
	}})
}

// CopyOwnerReferencesFrom clears any existing OwnerReferences and deep-copies from `other`.
func (o *Object) CopyOwnerFrom(other Object) {
	refs := []metav1.OwnerReference{}
	for _, ref := range other.Owners() {
		refs = append(refs, ref)
	}
	o.i.SetOwnerReferences(refs)
}

func (o Object) CreationTimestamp() time.Time {
	return o.i.GetCreationTimestamp().Time
}

func (o *Object) SetCreationTimestamp(t time.Time) {
	o.i.SetCreationTimestamp(metav1.NewTime(t))
}

func (o Object) ResourceVersion() string {
	return o.i.GetResourceVersion()
}

func (o *Object) SetResourceVersion(s string) {
	o.i.SetResourceVersion(s)
}

func (o Object) Finalizers() []string {
	return o.i.GetFinalizers()
}

func (o *Object) SetFinalizers(finalizers []string) {
	o.i.SetFinalizers(finalizers)
}

func (o *Object) AppendFinalizers(finalizers ...string) {
	o.i.SetFinalizers(append(o.Finalizers(), finalizers...))
}

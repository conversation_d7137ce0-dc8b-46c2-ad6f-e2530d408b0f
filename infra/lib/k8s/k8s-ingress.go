package k8s

import (
	netv1 "k8s.io/api/networking/v1"
)

type Ingress struct {
	Object
	raw *netv1.Ingress
}

func NewIngress(raw *netv1.Ingress) *Ingress {
	if raw == nil {
		raw = &netv1.Ingress{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "Ingress"
	}
	return &Ingress{
		Object: NewObject(raw),
		raw:    raw,
	}
}

func (o Ingress) Raw() *netv1.Ingress {
	return o.raw
}

func (o Ingress) ClassName() string {
	if n := o.Raw().Spec.IngressClassName; n != nil {
		return *n
	} else {
		return ""
	}
}

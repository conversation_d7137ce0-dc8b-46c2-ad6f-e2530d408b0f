package k8s

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"

	yaml "github.com/goccy/go-yaml"

	appsv1 "k8s.io/api/apps/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime/schema"
	appsv1a "k8s.io/client-go/applyconfigurations/apps/v1"
	metav1a "k8s.io/client-go/applyconfigurations/meta/v1"
)

type StatefulSet struct {
	Object
	raw *appsv1.StatefulSet
}

func NewStatefulSet(raw *appsv1.StatefulSet) *StatefulSet {
	if raw == nil {
		raw = &appsv1.StatefulSet{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "StatefulSet"
	}
	return &StatefulSet{
		Object: NewObject(raw),
		raw:    raw,
	}
}

type StatefulSetApplyConfig struct {
	*appsv1a.StatefulSetApplyConfiguration
}

func (o StatefulSet) Raw() *appsv1.StatefulSet {
	return o.raw
}

func (o StatefulSet) Replicas() int32 {
	if i := o.Raw().Spec.Replicas; i != nil {
		return *i
	}
	return 1
}

func (o StatefulSet) GetPods(ctx context.Context, c *Client) ([]*Pod, error) {
	return c.ListPods(ctx, ListLabelSelector(o.Raw().Spec.Selector))
}

func (o StatefulSet) GetPod(ctx context.Context, c *Client) (*Pod, error) {
	pods, err := o.GetPods(ctx, c)
	if err != nil {
		return nil, err
	}
	if len(pods) == 0 {
		err := apierrors.NewNotFound(schema.GroupResource{Resource: "pod"}, o.ShortName())
		return nil, err
	}
	if l := len(pods); l != 1 {
		return nil, fmt.Errorf("Found %d pods, expected 1", l)
	}
	return pods[0], nil
}

func (o StatefulSet) StatusLine() string {
	return fmt.Sprintf("statefulset/%s: %s", o.Name(), o.Status().String())
}

func (o StatefulSet) Status() StatefulSetStatus {
	return StatefulSetStatus{o.Raw().Status}
}

type StatefulSetStatus struct {
	appsv1.StatefulSetStatus
}

func (s StatefulSetStatus) String() string {
	return fmt.Sprintf("%d/%d/%d Ready/Updated/Total Replicas", s.ReadyReplicas, s.UpdatedReplicas, s.Replicas)
}

func (s StatefulSetStatus) Conditions() []StatefulSetCondition {
	cs := []StatefulSetCondition{}
	for _, c := range s.StatefulSetStatus.Conditions {
		cs = append(cs, StatefulSetCondition{c})
	}
	sort.Slice(cs, func(i, j int) bool {
		ti := &cs[i].LastTransitionTime
		tj := &cs[j].LastTransitionTime
		return ti.Before(tj)
	})
	return cs
}

func (s StatefulSetStatus) LatestCondition() StatefulSetCondition {
	latest := StatefulSetCondition{}
	for _, c := range s.Conditions() {
		if (&latest.LastTransitionTime).Before(&c.LastTransitionTime) {
			latest = c
		}
	}
	return latest
}

type StatefulSetCondition struct {
	appsv1.StatefulSetCondition
}

func (c StatefulSetCondition) String() string {
	t := c.LastTransitionTime.Format("2006-01-02 15:04 MST")
	s := fmt.Sprintf("%s %s=%s", t, c.Type, c.Status)
	if c.Reason != "" || c.Message != "" {
		s += ":"
	}
	if c.Reason != "" {
		s += fmt.Sprintf(" [%s]", c.Reason)
	}
	if c.Message != "" {
		s += " " + c.Message
	}
	return s
}

func (o StatefulSet) ApplyConfig(fieldManager string) (*StatefulSetApplyConfig, error) {
	raw, err := appsv1a.ExtractStatefulSet(o.Raw(), fieldManager)
	return &StatefulSetApplyConfig{StatefulSetApplyConfiguration: raw}, err
}

func NewStatefulSetApplyConfig(name, namespace string) *StatefulSetApplyConfig {
	return &StatefulSetApplyConfig{
		StatefulSetApplyConfiguration: appsv1a.StatefulSet(name, namespace),
	}
}

func (cfg *StatefulSetApplyConfig) FromJSON(buf string) error {
	if buf == "" {
		return nil
	}
	return json.Unmarshal([]byte(buf), cfg.Raw())
}

func (cfg *StatefulSetApplyConfig) Raw() *appsv1a.StatefulSetApplyConfiguration {
	if cfg == nil {
		return nil
	}
	return cfg.StatefulSetApplyConfiguration
}

func (cfg *StatefulSetApplyConfig) Name() string {
	if raw := cfg.Raw(); raw == nil {
		return ""
	} else if name := raw.Name; name == nil {
		return ""
	} else {
		return *name
	}
}

func (cfg *StatefulSetApplyConfig) YAML() (string, error) {
	buf, err := yaml.MarshalWithOptions(cfg.Raw(), yaml.UseJSONMarshaler(), yaml.CustomMarshaler(func(b []byte) ([]byte, error) {
		// Avoid printing lists of individual bytes
		return b, nil
	}))
	return string(buf), err
}

func (cfg *StatefulSetApplyConfig) JSON() (string, error) {
	buf, err := json.MarshalIndent(cfg.Raw(), "", "  ")
	return string(buf), err
}

func (cfg *StatefulSetApplyConfig) WithOwner(o *Object) *StatefulSetApplyConfig {
	owner := metav1a.OwnerReference().
		WithAPIVersion(o.Version()).
		WithKind(o.Kind()).
		WithName(o.Name()).
		WithUID(o.UIDtOrTODO()).
		WithBlockOwnerDeletion(true)
	cfg.Raw().OwnerReferences = nil
	cfg.WithOwnerReferences(owner)
	return cfg
}

package k8s

import (
	"fmt"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
)

////////////////////////////////////////////////////////////////////////////////
//
// Event (single event)
//

type Event struct {
	Object
	raw *corev1.Event
}

func NewEvent(raw *corev1.Event) *Event {
	if raw == nil {
		raw = &corev1.Event{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "Event"
	}
	return &Event{
		Object: NewObject(raw),
		raw:    raw,
	}
}

func NewCreationEvent(o Object) *Event {
	raw := &corev1.Event{
		InvolvedObject: corev1.ObjectReference{
			Kind:       o.Kind(),
			Namespace:  o.Namespace(),
			Name:       o.Name(),
			APIVersion: o.Version(),
			UID:        o.UIDt(),
		},
		Reason:    "Creation",
		Message:   fmt.Sprintf("%s created.", o.ShortName()),
		EventTime: metav1.NewMicroTime(o.CreationTimestamp()),
	}
	return NewEvent(raw)
}

func (e *Event) Raw() *corev1.Event {
	return e.raw
}

// InvolvedObject is a simple getter.
func (e *Event) InvolvedObject() corev1.ObjectReference {
	return e.Raw().InvolvedObject
}

// InvolvedObjectMasked returns a masked `InvolvedObject()` with UID and other fields specific
// to a single instance of an object masked. For example, to match the same pod name reused
// multiple times (e.g., in a StatefulSet).
func (e *Event) InvolvedObjectMasked() corev1.ObjectReference {
	orig := e.InvolvedObject()
	return corev1.ObjectReference{
		Kind:       orig.Kind,
		Namespace:  orig.Namespace,
		Name:       orig.Name,
		APIVersion: orig.APIVersion,
	}
}

func (e *Event) InvolvedUID() types.UID {
	return e.InvolvedObject().UID
}

// Timestamp is a single timestamp that takes the first that exists of EventTime, FirstTimestamp, LastTimestamp, Metadata.CreationTimestamp.
func (e *Event) Timestamp() time.Time {
	if t := e.Raw().EventTime; !t.IsZero() {
		return t.Time
	}
	if t := e.Raw().LastTimestamp; !t.IsZero() {
		return t.Time
	}
	if t := e.Raw().FirstTimestamp; !t.IsZero() {
		return t.Time
	}
	return e.CreationTimestamp()
}

////////////////////////////////////////////////////////////////////////////////
//
// EventList
//

type EventList []*Event

// MostRecent prunes events for all but the most recent object UID for each unique Namespace+Kind+Name+APIVersion. It
// assumes that the original list is well-ordered.
func (lst EventList) MostRecent() EventList {
	// Pass 1: Track most recent UID.
	mostRecent := map[corev1.ObjectReference]types.UID{}
	for _, ev := range lst {
		mostRecent[ev.InvolvedObjectMasked()] = ev.InvolvedUID()
	}
	// Pass 2: Filter.
	ret := EventList{}
	for _, ev := range lst {
		if mostRecent[ev.InvolvedObjectMasked()] == ev.InvolvedUID() {
			ret = append(ret, ev)
		}
	}
	return ret
}

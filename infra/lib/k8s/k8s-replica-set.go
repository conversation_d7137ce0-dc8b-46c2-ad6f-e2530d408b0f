package k8s

import (
	"context"

	appsv1 "k8s.io/api/apps/v1"
)

type ReplicaSet struct {
	Object
	raw *appsv1.ReplicaSet
}

func NewReplicaSet(raw *appsv1.ReplicaSet) *ReplicaSet {
	if raw == nil {
		raw = &appsv1.ReplicaSet{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "ReplicaSet"
	}
	return &ReplicaSet{
		Object: NewObject(raw),
		raw:    raw,
	}
}

func (o ReplicaSet) Raw() *appsv1.ReplicaSet {
	return o.raw
}

func (o ReplicaSet) GetPods(ctx context.Context, c *Client) ([]*Pod, error) {
	pods, err := c.ListPods(ctx, ListLabelSelector(o.Raw().Spec.Selector))
	if err != nil {
		return nil, err
	}
	ret := []*Pod{}
	for _, pod := range pods {
		if pod.OwnerMatch(o.Object) {
			ret = append(ret, pod)
		}
	}
	return ret, nil
}

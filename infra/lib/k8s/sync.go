package k8s

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pmezard/go-difflib/difflib"
	"golang.org/x/sync/errgroup"
)

type syncEndpoint struct {
	k8s  *Client
	name string
}

func (e syncEndpoint) Context() string {
	return e.k8s.Config().Context()
}

type syncSet struct {
	typ   string
	name  string
	dests []syncEndpoint
}

func (c Client) newSyncSet(name string, kcfg string, specs ...string) (syncSet, error) {
	/// Parse and validate SOURCE name.
	typ, name, _ := strings.Cut(name, "/")
	if name == "" {
		name = typ
		typ = "cm"
	}
	if typ != "cm" {
		return syncSet{}, fmt.Errorf("%s/%s: Only ConfigMap is currently supported", typ, name)
	}

	/// Init Set.
	set := syncSet{
		typ:  typ,
		name: name,
	}

	/// Parse and validate each DEST spec.
	for _, spec := range specs {
		dcontext, dname, _ := strings.Cut(spec, ":")
		if dname == "" {
			dname = name
		}

		/// Parse and validate DEST name.
		dtyp, dname, _ := strings.Cut(dname, "/")
		if dname == "" {
			dname = dtyp
			dtyp = "cm"
		}
		if dtyp != typ {
			return syncSet{}, fmt.Errorf("%s/%s: Type must match source %s/%s", dtyp, dname, typ, name)
		}

		/// Create a DEST K8s client.
		var dk8s *Client
		if kcfg == "" {
			var err error
			if dk8s, err = New(dcontext); err != nil {
				return syncSet{}, err
			}
		} else {
			path := fmt.Sprintf("%s/%s.yaml", kcfg, dcontext)
			var err error
			if dk8s, err = NewFromKubeconfig(dcontext, path); err != nil {
				return syncSet{}, err
			}
		}

		/// Add DEST.
		set.dests = append(set.dests, syncEndpoint{
			k8s:  dk8s,
			name: dname,
		})
	}

	c.LogInfo("Sync Source: %s/%s: Configured.", set.typ, set.name)
	for _, d := range set.dests {
		c.LogInfo("Sync Dest [%s]: %s/%s: Configured.", d.Context(), set.typ, d.name)
	}

	return set, nil
}

// Sync syncs a source object to one or more destination cluster:objects.
// Currently, only ConfigMaps are supported and Secrets may be the only other
// type that makes sense.
//
// Only the ConfigMap data is synced. A new object is created if it doesn't already exist.
//
// Each destination spec is CONTEXT[:NAME]. The CONTEXT is expected to be found in the local
// client's Kubeconfig. If kcfg is not empty, each dest Kubeconfig is read from <kcfg>/<dest>.yaml.
// A NAME is optional, and defaults to the source name.
func (c Client) Sync(ctx context.Context, name string, kcfg string, dryRun bool, specs ...string) error {
	set, err := c.newSyncSet(name, kcfg, specs...)
	if err != nil {
		return err
	}

	cm1, err := c.GetConfigMap(ctx, set.name)
	if err != nil {
		return err
	}

	grp, gctx := errgroup.WithContext(ctx)
	for _, dest := range set.dests {
		grp.Go(func() error {
			if err := c.SyncConfigMap(gctx, dest.k8s, cm1, nil, set.name, dest.name, dryRun); err != nil {
				c.LogErr("Sync Dest [%s]: %s/%s: Error: %v.", dest.Context(), set.typ, dest.name, err)
			} else {
				c.LogInfo("Sync Dest [%s]: %s/%s: Synced.", dest.Context(), set.typ, dest.name)
			}
			return nil
		})
	}
	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// SyncRun continuously syncs the source to all dests. There are three drivers:
//  1. A watch is placed on the source which triggers syncs to all dests.
//  2. A watch is placed on each dest, which triggers a single sync from the source.
//  3. A timer (for kicks) triggers a sync from the source to all dests.
//
// See also `Sync()`.
func (c Client) SyncRun(ctx context.Context, name string, kcfg string, dryRun bool, specs ...string) error {
	/// Parse and Connect
	set, err := c.newSyncSet(name, kcfg, specs...)
	if err != nil {
		return err
	}

	/// Single-stream event channels for (1), (2), and (3) above.
	type syncEv struct {
		cm1 *ConfigMap
		cm2 *ConfigMap
	}
	chs := []chan syncEv{}

	//. Non-blocking send, we only need to do one sync at a time.
	notify := func(ch chan<- syncEv, cm1, cm2 *ConfigMap) {
		select {
		case ch <- syncEv{cm1: cm1, cm2: cm2}:
		default:
		}
	}
	notifyAll := func(cm1 *ConfigMap) {
		for _, ch := range chs {
			notify(ch, cm1, nil)
		}
	}

	for _, dest := range set.dests {
		ch := make(chan syncEv, 1)
		chs = append(chs, ch)

		/// Dest Event Loop, for receiving (1), (2), (3).
		go func() {
			for {
				select {
				case <-ctx.Done():
					return
				case ev := <-ch:
					if err := c.SyncConfigMap(ctx, dest.k8s, ev.cm1, ev.cm2, set.name, dest.name, dryRun); err != nil {
						c.LogErr("Sync Dest [%s]: %s/%s: Error: %v.", dest.Context(), set.typ, dest.name, err)
					} else {
						c.LogInfo("Sync Dest [%s]: %s/%s: Synced.", dest.Context(), set.typ, dest.name)
					}
				}
			}
		}()

		/// (3): Dest Watcher.
		go func() {
			if err := dest.k8s.WatchConfigMaps(ctx, func(ev string, cm2 *ConfigMap) {
				c.LogInfo("Sync Dest [%s]: %s/%s: Event received, syncing from source...", dest.Context(), set.typ, cm2.Name())
				notify(ch, nil, cm2)
			}, ListByName(dest.name)); err != nil {
				c.LogErr("Sync Dest [%s]: %s/%s: Destination Watcher Failed: %v.", dest.Context(), set.typ, dest.name, err)
			}
		}()
	}

	/// (2): Timer Trigger (fallback, probably isn't even needed).
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				c.LogInfo("Sync Timer: %s/%s: Tick received, syncing to destinations...", set.typ, set.name)
				if cm1, err := c.GetConfigMap(ctx, set.name); err != nil {
					c.LogInfo("Sync Timer: Error getting %s/%s: %v.", set.typ, set.name, err)
				} else {
					notifyAll(cm1)
				}
			}
		}
	}()

	/// (1): Source Watcher.
	cm1, err := c.GetConfigMap(ctx, set.name)
	if err != nil {
		return err
	}
	return cm1.Watch(ctx, &c, func(ev string, cm1 *ConfigMap) {
		c.LogInfo("Sync Source: %s/%s: Event received, syncing to destinations...", set.typ, cm1.Name())
		notifyAll(cm1)
	})
}

// SyncConfigMap syncs the data from source (`cm1`/`name1`) to dest (`cm2`/`name2`). If either of `cm1` or `cm2` are nil,
// they are read from their respective client.
func (c Client) SyncConfigMap(ctx context.Context, dst *Client, cm1, cm2 *ConfigMap, name1, name2 string, dryRun bool) error {
	/// Get the source object, if needed.

	if cm1 == nil {
		var err error
		if cm1, err = c.GetConfigMap(ctx, name1); err != nil {
			return err
		}
	}

	/// Get the existing destination object, if needed. If NotFound, do a simple Create().

	if cm2 == nil {
		var err error
		if cm2, err = dst.GetConfigMap(ctx, name2); IsNotFound(err) {
			c.LogInfo("Sync Dest [%s]: %s/%s: NotFound, creating...", dst.Config().Context(), "cm", name2)
			if dryRun {
				return nil
			} else {
				cm2 := NewConfigMap(nil)
				cm2.Raw().ObjectMeta.Name = name2
				cm2.Raw().Data = cm1.Raw().Data
				_, err := dst.CreateConfigMap(ctx, cm2)
				return err
			}
		} else if err != nil {
			return err
		}
	}

	/// Diff the SOURCE and DEST.

	lines := func(cm *ConfigMap) []string {
		ret := []string{}
		for _, key := range cm.Keys() {
			ret = append(ret, key+":\n")
			for _, line := range strings.Split(cm.Key(key), "\n") {
				ret = append(ret, "\t"+line+"\n")
			}
		}
		return ret
	}

	diff, err := difflib.GetUnifiedDiffString(difflib.UnifiedDiff{
		ToFile:   fmt.Sprintf("%s/%s", c.Config().Context(), cm1.ShortName()),
		B:        lines(cm1),
		FromFile: fmt.Sprintf("%s/%s", dst.Config().Context(), cm2.ShortName()),
		A:        lines(cm2),
		Context:  3,
	})
	if err != nil {
		return err
	}

	/// No Diff.

	if diff == "" {
		c.LogInfo("Sync Dest [%s]: %s: No Diff.", dst.Config().Context(), cm2.ShortName())
		return nil
	}

	/// Diff

	c.LogInfo("Sync Dest [%s]: %s: Diff, updating...:\n%s", dst.Config().Context(), cm2.ShortName(), diff)
	if !dryRun {
		cm2.Raw().Data = cm1.Raw().Data
		_, err := dst.UpdateConfigMap(ctx, cm2)
		return err
	} else {
		return nil
	}
}

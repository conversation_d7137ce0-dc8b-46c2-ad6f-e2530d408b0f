package k8s

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"

	yaml "github.com/goccy/go-yaml"

	"k8s.io/apimachinery/pkg/types"

	corev1 "k8s.io/api/core/v1"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"
	metav1a "k8s.io/client-go/applyconfigurations/meta/v1"

	"github.com/augmentcode/augment/infra/lib/docker"
)

var ErrKeyNotFound = fmt.Errorf("key not found")

const (
	SecretTypeSSHHostKeys = corev1.SecretType("augmentcode.com/ssh-hostkeys")
)

type Secret struct {
	Object
	raw *corev1.Secret
}

type SecretApplyConfig struct {
	*corev1a.SecretApplyConfiguration
}

func NewSecret(raw *corev1.Secret) *Secret {
	if raw == nil {
		raw = &corev1.Secret{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "Secret"
	}
	return &Secret{
		Object: NewObject(raw),
		raw:    raw,
	}
}

func (o Secret) Raw() *corev1.Secret {
	return o.raw
}

func (o Secret) Type() corev1.SecretType {
	return o.Raw().Type
}

func (o Secret) StringData() map[string]string {
	ret := map[string]string{}
	for k, v := range o.Raw().Data {
		ret[k] = string(v)
	}
	return ret
}

func (o Secret) Keys() []string {
	ret := []string{}
	for key := range o.Raw().Data {
		ret = append(ret, key)
	}
	sort.Strings(ret)
	return ret
}

// KeyBytes returns the value as raw bytes.
func (o Secret) KeyBytes(k string) []byte {
	return o.Raw().Data[k]
}

// Key wraps KeyBytes and converts to string.
func (o Secret) Key(k string) string {
	return string(o.KeyBytes(k))
}

// KeyBytesErr is like KeyBytes except a `ErrKeyNotFound` is returned if the value is empty.
func (o Secret) KeyBytesErr(k string) ([]byte, error) {
	if b := o.KeyBytes(k); len(b) == 0 {
		return nil, fmt.Errorf("secret %s: key '%s': %w", o.Name(), k, ErrKeyNotFound)
	} else {
		return b, nil
	}
}

// KeyErr is like Key except a `ErrKeyNotFound` is returned if the value is empty.
func (o Secret) KeyErr(k string) (string, error) {
	if s := o.Key(k); len(s) == 0 {
		return "", fmt.Errorf("secret %s: key '%s': %w", o.Name(), k, ErrKeyNotFound)
	} else {
		return s, nil
	}
}

// KeyBoolErr returns the Key parsed as a `bool`. Parse errors and `ErrKeyNotFound` may be returned.
func (o Secret) KeyBoolErr(k string) (bool, error) {
	if s, err := o.KeyErr(k); err != nil {
		return false, err
	} else if b, err := strconv.ParseBool(s); err != nil {
		return false, fmt.Errorf("secret %s: key '%s': %w", o.Name(), k, err)
	} else {
		return b, nil
	}
}

// KeyInt64Err returns the Key parsed as an `int64`. Parse errors and `ErrKeyNotFound` may be returned.
func (o Secret) KeyInt64Err(k string) (int64, error) {
	if s, err := o.KeyErr(k); err != nil {
		return 0, err
	} else if i, err := strconv.ParseInt(s, 10, 64); err != nil {
		return 0, fmt.Errorf("secret %s: key '%s': %w", o.Name(), k, err)
	} else {
		return i, nil
	}
}

// KeyUint64Err returns the Key parsed as an `uint64`. Parse errors and `ErrKeyNotFound` may be returned.
func (o Secret) KeyUint64Err(k string) (uint64, error) {
	if s, err := o.KeyErr(k); err != nil {
		return 0, err
	} else if i, err := strconv.ParseUint(s, 10, 64); err != nil {
		return 0, fmt.Errorf("secret %s: key '%s': %w", o.Name(), k, err)
	} else {
		return i, nil
	}
}

func (o Secret) UserPass() (string, string) {
	return o.Key(corev1.BasicAuthUsernameKey), o.Key(corev1.BasicAuthPasswordKey)
}

func (o Secret) ServiceAccountName() string {
	return o.Annotations()[corev1.ServiceAccountNameKey]
}

func (o Secret) ServiceAccountToken() (string, error) {
	return o.KeyErr(corev1.ServiceAccountTokenKey)
}

func (o Secret) ServiceAccountNamespace() (string, error) {
	return o.KeyErr(corev1.ServiceAccountNamespaceKey)
}

func (o Secret) ServiceAccountRootCA() ([]byte, error) {
	return o.KeyBytesErr(corev1.ServiceAccountRootCAKey)
}

func (o Secret) DockerConfig() (*docker.Config, error) {
	if got, want := o.Type(), corev1.SecretTypeDockerConfigJson; got != want {
		return nil, fmt.Errorf("Secret.Type %v: expected to be %v for DockerConfig", got, want)
	}
	if str, err := o.KeyErr(corev1.DockerConfigJsonKey); err != nil {
		return nil, err
	} else {
		return docker.ConfigFromString(str)
	}
}

func (o Secret) DockerAuth(ctx context.Context, server string) (string, string, error) {
	if typ := o.Type(); typ == corev1.SecretTypeDockerConfigJson {
		if cfg, err := o.DockerConfig(); err != nil {
			return "", "", err
		} else {
			return cfg.Auth(ctx, server)
		}
	}
	user, pass := o.UserPass()
	return user, pass, nil
}

func (o Secret) SetData(ctx context.Context, c *Client, data map[string]string) (*Secret, error) {
	d := struct {
		Data map[string]any `json:"data"`
	}{
		Data: map[string]any{},
	}
	for k, v := range data {
		if v == "" {
			d.Data[k] = nil
		} else {
			d.Data[k] = []byte(v)
		}
	}
	return c.PatchSecret(ctx, o.Name(), types.StrategicMergePatchType, d)
}

func (o Secret) SetKey(ctx context.Context, c *Client, key, val string) (*Secret, error) {
	data := map[string]string{
		key: val,
	}
	return o.SetData(ctx, c, data)
}

func (o Secret) Edit(ctx context.Context, c *Client) (*Secret, error) {
	// Create a stripped down copy for editing. We'll only support editing
	// the data (as stringData), but put some other info in the editor for
	// clarity.
	tmp := NewSecret(nil)
	tmp.Raw().Name = o.Name()
	tmp.Raw().Namespace = o.Namespace()
	tmp.Raw().Type = o.Type()
	tmp.Raw().StringData = o.StringData()

	msg := strings.Join([]string{
		"# Edit the secrets stringData below. ONLY edits to `stringData` are",
		"# handled, other edits such as to metadata are ignored.",
		"# To abort this edit, exit your EDITOR with an error, for example `:cq` in vim.",
	}, "\n")

	reread := func(buf string) error {
		tmp.Raw().Data = map[string][]byte{}
		tmp.Raw().StringData = map[string]string{}
		if err := tmp.FromYAMLStrict(string(buf)); err != nil {
			return err
		}
		o.Raw().Data = map[string][]byte{}
		for k, v := range tmp.Raw().StringData {
			fmt.Println(k)
			o.Raw().Data[k] = []byte(v)
		}
		return nil
	}

	var o2 *Secret
	update := func(ctx context.Context) error {
		var err error
		o2, err = c.UpdateSecret(ctx, &o)
		return err
	}

	if err := c.edit(ctx, &tmp.Object, msg, reread, update); err != nil {
		return nil, err
	}

	return o2, nil
}

func NewSecretSSHHostKeys(sec *Secret, name string, keys map[string][]byte) *Secret {
	if sec == nil {
		sec = NewSecret(nil)
	}
	sec.Raw().ObjectMeta.Name = name
	sec.Raw().Type = SecretTypeSSHHostKeys
	sec.Raw().Data = keys
	return sec
}

func CreateSecretSSHHostKeys(ctx context.Context, c *Client, name string, force bool, keys map[string][]byte) (*Secret, error) {
	sec, err := c.GetSecret(ctx, name)
	err = NotFoundOK(err)
	if err != nil {
		return nil, err
	}
	if sec != nil && !force {
		return nil, fmt.Errorf("%s: already exists", sec.ShortName())
	}
	sec = NewSecretSSHHostKeys(sec, name, keys)
	return c.ReplaceSecret(ctx, sec)
}

func (o Secret) ApplyConfig(fieldManager string) (*SecretApplyConfig, error) {
	raw, err := corev1a.ExtractSecret(o.Raw(), fieldManager)
	return &SecretApplyConfig{SecretApplyConfiguration: raw}, err
}

func NewSecretApplyConfig(name, namespace string) *SecretApplyConfig {
	return &SecretApplyConfig{
		SecretApplyConfiguration: corev1a.Secret(name, namespace),
	}
}

func (cfg *SecretApplyConfig) FromJSON(buf string) error {
	if buf == "" {
		return nil
	}
	return json.Unmarshal([]byte(buf), cfg.Raw())
}

func (cfg *SecretApplyConfig) Raw() *corev1a.SecretApplyConfiguration {
	if cfg == nil {
		return nil
	}
	return cfg.SecretApplyConfiguration
}

func (cfg *SecretApplyConfig) Name() string {
	if raw := cfg.Raw(); raw == nil {
		return ""
	} else if name := raw.Name; name == nil {
		return ""
	} else {
		return *name
	}
}

func (cfg *SecretApplyConfig) Data() map[string][]byte {
	if raw := cfg.Raw(); raw == nil {
		return nil
	} else {
		return raw.Data
	}
}

func (cfg *SecretApplyConfig) YAML() (string, error) {
	buf, err := yaml.MarshalWithOptions(cfg.Raw(), yaml.UseJSONMarshaler(), yaml.CustomMarshaler(func(b []byte) ([]byte, error) {
		// Avoid printing lists of individual bytes
		return b, nil
	}))
	return string(buf), err
}

func (cfg *SecretApplyConfig) JSON() (string, error) {
	buf, err := json.MarshalIndent(cfg.Raw(), "", "  ")
	return string(buf), err
}

func (cfg *SecretApplyConfig) WithOwner(o *Object) *SecretApplyConfig {
	owner := metav1a.OwnerReference().
		WithAPIVersion(o.Version()).
		WithKind(o.Kind()).
		WithName(o.Name()).
		WithUID(o.UIDtOrTODO()).
		WithBlockOwnerDeletion(true)
	cfg.Raw().OwnerReferences = nil
	cfg.WithOwnerReferences(owner)
	return cfg
}

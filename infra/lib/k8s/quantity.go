package k8s

import (
	"fmt"

	"k8s.io/apimachinery/pkg/api/resource"
)

func Humanize(q resource.Quantity) string {
	// Handle negative values
	isNegative := false
	val := q.AsApproximateFloat64()
	if val < 0 {
		isNegative = true
		val *= -1
	}

	if q.Format == resource.BinarySI {
		unit, units := 0, []string{"", "Ki", "Mi", "Gi", "Ti", "Pi", "Ei", "Zi"}
		for val >= 1024 && unit < len(units)-1 {
			unit++
			val /= 1024
		}
		if isNegative {
			val *= -1
		}
		return fmt.Sprintf("%.2g%s", val, units[unit])
	}

	if q.Format == resource.DecimalSI {
		unit, units := 0, []string{"", "k", "M", "G", "T", "P", "E", "Z"}
		for val >= 1000 && unit < len(units)-1 {
			unit++
			val /= 1000
		}
		// Special case for millis
		if unit == 0 && val < 1.0 {
			milliVal := q.MilliValue()
			return fmt.Sprintf("%dm", milliVal)
		}
		if isNegative {
			val *= -1
		}
		return fmt.Sprintf("%.2g%s", val, units[unit])
	}

	// Default fallback
	return q.String()
}

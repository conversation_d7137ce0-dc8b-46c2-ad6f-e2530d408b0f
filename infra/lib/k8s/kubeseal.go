package k8s

import (
	"bytes"
	"context"
	"crypto/rsa"
	"strings"

	"github.com/bitnami-labs/sealed-secrets/pkg/kubeseal"
	"k8s.io/client-go/kubernetes/scheme"

	ssv1a "github.com/bitnami-labs/sealed-secrets/pkg/apis/sealedsecrets/v1alpha1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// SealingKey gets the Sealed Secrets (RSA) Public Key used for sealing raw data and/or secrets. The default
// controller info (kube-system/sealed-secrets-controller) is hardcoded for simplicity. See `SealingKeyOnce()`
// for a cached wrapper around this method.
func (c *Client) SealingKey(ctx context.Context) (*rsa.PublicKey, error) {
	cfg := c.Config().cfg
	r, err := kubeseal.OpenCert(ctx, cfg, metav1.NamespaceSystem, "sealed-secrets-controller", "")
	if err != nil {
		return nil, err
	}
	defer func() {
		if err := r.Close(); err != nil {
			c.<PERSON>n("error closing kubseal cert reader: %v", err)
		}
	}()
	return kubeseal.ParseKey(r)
}

// SealingKeyOnce wraps `SealingKey()` and caches the response to avoid multiple RPCs when sealing multiple secrets.
func (c Client) SealingKeyOnce(ctx context.Context) (*rsa.PublicKey, error) {
	c.sealLk.Lock()
	defer c.sealLk.Unlock()
	if c.sealKey == nil {
		if key, err := c.SealingKey(ctx); err != nil {
			return nil, err
		} else {
			c.sealKey = key
		}
	}
	return c.sealKey, nil
}

// Seal seals `data` using the key from `SealingKeyOnce()`. The SealingScope is inferred from `ns` and `name`,
// which may be empty.
func (c Client) Seal(ctx context.Context, ns, name string, data []byte) ([]byte, error) {
	pubkey, err := c.SealingKeyOnce(ctx)
	if err != nil {
		return nil, err
	}

	scope := func() ssv1a.SealingScope {
		switch {
		case ns == "":
			return ssv1a.ClusterWideScope
		case name == "":
			return ssv1a.NamespaceWideScope
		default:
			return ssv1a.StrictScope
		}
	}()

	w := bytes.Buffer{}
	if err := kubeseal.EncryptSecretItem(&w, name, ns, data, scope, pubkey); err != nil {
		return nil, err
	}

	return w.Bytes(), nil
}

// SealSecret seals a K8s Secret resource `sec` into a K8s SealedSecret resource.
// NOTE(mattm): The scope is currently hardcoded to Strict, but support for Namespace and Cluster is easy.
func (c Client) SealSecret(ctx context.Context, sec Secret) (*SealedSecret, error) {
	pubkey, err := c.SealingKeyOnce(ctx)
	if err != nil {
		return nil, err
	}

	j, err := sec.JSON()
	if err != nil {
		return nil, err
	}
	in := strings.NewReader(j)
	out := strings.Builder{}

	cfg := c.Config().cfg
	if err := kubeseal.Seal(cfg, "json", in, &out, scheme.Codecs, pubkey, ssv1a.StrictScope, true, "", ""); err != nil {
		return nil, err
	}

	ssec := NewSealedSecret(nil)
	if err := ssec.FromJSON(out.String()); err != nil {
		return nil, err
	}

	return ssec, nil
}

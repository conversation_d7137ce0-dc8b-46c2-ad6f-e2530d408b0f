package k8s

import (
	"context"
	"encoding/json"
	"sort"

	yaml "github.com/goccy/go-yaml"

	"k8s.io/apimachinery/pkg/types"

	corev1 "k8s.io/api/core/v1"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"
	metav1a "k8s.io/client-go/applyconfigurations/meta/v1"
)

type ConfigMap struct {
	Object
	raw *corev1.ConfigMap
}

type ConfigMapApplyConfig struct {
	*corev1a.ConfigMapApplyConfiguration
}

func NewConfigMap(raw *corev1.ConfigMap) *ConfigMap {
	if raw == nil {
		raw = &corev1.ConfigMap{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "ConfigMap"
	}
	return &ConfigMap{
		Object: NewObject(raw),
		raw:    raw,
	}
}

func (o ConfigMap) Raw() *corev1.ConfigMap {
	return o.raw
}

func (o ConfigMap) Data() map[string]string {
	return o.Raw().Data
}

func (o ConfigMap) Keys() []string {
	ret := []string{}
	for key := range o.Data() {
		ret = append(ret, key)
	}
	sort.Strings(ret)
	return ret
}

func (o ConfigMap) Has(k string) bool {
	_, has := o.Data()[k]
	return has
}

func (o ConfigMap) Key(k string) string {
	return o.Data()[k]
}

func (o ConfigMap) SetData(ctx context.Context, c *Client, data map[string]string) (*ConfigMap, error) {
	d := struct {
		Data map[string]any `json:"data"`
	}{
		Data: map[string]any{},
	}
	for k, v := range data {
		if v == "" {
			d.Data[k] = nil
		} else {
			d.Data[k] = v
		}
	}
	return c.PatchConfigMap(ctx, o.Name(), types.StrategicMergePatchType, d)
}

func (o ConfigMap) SetKey(ctx context.Context, c *Client, key, val string) (*ConfigMap, error) {
	data := map[string]string{
		key: val,
	}
	return o.SetData(ctx, c, data)
}

func (o ConfigMap) Watch(ctx context.Context, c *Client, f func(string, *ConfigMap), opts ...ListOpt) error {
	os := []ListOpt{ListByName(o.Name())}
	os = append(os, opts...)
	return c.WatchConfigMaps(ctx, f, os...)
}

func (o ConfigMap) ApplyConfig(fieldManager string) (*ConfigMapApplyConfig, error) {
	raw, err := corev1a.ExtractConfigMap(o.Raw(), fieldManager)
	return &ConfigMapApplyConfig{ConfigMapApplyConfiguration: raw}, err
}

func NewConfigMapApplyConfig(name, namespace string) *ConfigMapApplyConfig {
	return &ConfigMapApplyConfig{
		ConfigMapApplyConfiguration: corev1a.ConfigMap(name, namespace),
	}
}

func (cfg *ConfigMapApplyConfig) FromJSON(buf string) error {
	if buf == "" {
		return nil
	}
	return json.Unmarshal([]byte(buf), cfg.Raw())
}

func (cfg *ConfigMapApplyConfig) Raw() *corev1a.ConfigMapApplyConfiguration {
	if cfg == nil {
		return nil
	}
	return cfg.ConfigMapApplyConfiguration
}

func (cfg *ConfigMapApplyConfig) Name() string {
	if raw := cfg.Raw(); raw == nil {
		return ""
	} else if name := raw.Name; name == nil {
		return ""
	} else {
		return *name
	}
}

func (cfg *ConfigMapApplyConfig) Data() map[string]string {
	if raw := cfg.Raw(); raw == nil {
		return nil
	} else {
		return raw.Data
	}
}

func (cfg *ConfigMapApplyConfig) Keys() []string {
	ret := []string{}
	for key := range cfg.Data() {
		ret = append(ret, key)
	}
	sort.Strings(ret)
	return ret
}

func (cfg *ConfigMapApplyConfig) YAML() (string, error) {
	buf, err := yaml.MarshalWithOptions(cfg.Raw(), yaml.UseJSONMarshaler(), yaml.CustomMarshaler(func(b []byte) ([]byte, error) {
		// Avoid printing lists of individual bytes
		return b, nil
	}))
	return string(buf), err
}

func (cfg *ConfigMapApplyConfig) JSON() (string, error) {
	buf, err := json.MarshalIndent(cfg.Raw(), "", "  ")
	return string(buf), err
}

func (cfg *ConfigMapApplyConfig) WithOwner(o *Object) *ConfigMapApplyConfig {
	owner := metav1a.OwnerReference().
		WithAPIVersion(o.Version()).
		WithKind(o.Kind()).
		WithName(o.Name()).
		WithUID(o.UIDtOrTODO()).
		WithBlockOwnerDeletion(true)
	cfg.Raw().OwnerReferences = nil
	cfg.WithOwnerReferences(owner)
	return cfg
}

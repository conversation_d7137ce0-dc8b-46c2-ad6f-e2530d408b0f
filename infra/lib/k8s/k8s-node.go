package k8s

import (
	"fmt"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	scheduling "k8s.io/component-helpers/scheduling/corev1"
	"k8s.io/component-helpers/scheduling/corev1/nodeaffinity"
)

type Node struct {
	Object
	raw *corev1.Node

	// Set by Node.CountRequested().
	requested corev1.ResourceList
}

func CanonicalGpu(raw string) string {
	canonicalGpu := map[string]string{
		"nvidia-h100-80gb":      "H100",
		"nvidia-h100-mega-80gb": "H100",
		"nvidia-a100-80gb":      "A100",
		"H100_NVLINK_80GB":      "H100",
		"A100_NVLINK_80GB":      "A100",
	}

	if c, ok := canonicalGpu[raw]; ok {
		return c
	}
	return raw
}

func NewNode(raw *corev1.Node) *Node {
	if raw == nil {
		raw = &corev1.Node{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "Node"
	}
	return &Node{
		Object: NewObject(raw),
		raw:    raw,
	}
}

func (o Node) Raw() *corev1.Node {
	return o.raw
}

func (o Node) Name() string {
	return o.Raw().Name
}

func (o Node) Status() corev1.NodeStatus {
	return o.Raw().Status
}

func (o Node) GPUType() string {
	if l := o.Labels()["cloud.google.com/gke-accelerator"]; l != "" {
		return l
	}
	return o.Labels()["gpu.nvidia.com/class"]
}

func (o Node) CanonicalGPUType() string {
	return CanonicalGpu(o.GPUType())
}

func (o Node) GPUCapacity() int {
	q, ok := o.Capacity("nvidia.com/gpu")["nvidia.com/gpu"]
	if !ok {
		return 0
	}
	return int(q.Value())
}

// Turns out this is useless, it doesn't reflect GPUs assigned to pods
func (o Node) GPUAllocatable() int {
	q, ok := o.Allocatable("nvidia.com/gpu")["nvidia.com/gpu"]
	if !ok {
		return 0
	}
	return int(q.Value())
}

func (o Node) StatusLine() string {
	c := o.GPUCapacity()
	return fmt.Sprintf("%s %s %dx%s", o.Name(), CanonicalGpu(o.GPUType()), c, CanonicalGpu(o.GPUType()))
}

func (o Node) String() string {
	return fmt.Sprintf("%s: %s", o.Name(), o.StatusLine())
}

func (o Node) Allocatable(k ...corev1.ResourceName) corev1.ResourceList {
	if len(k) == 0 {
		return o.Status().Allocatable
	}
	ret := map[corev1.ResourceName]resource.Quantity{}
	for _, k := range k {
		ret[k] = o.Status().Allocatable[k]
	}
	return ret
}

func (o Node) Capacity(k ...corev1.ResourceName) corev1.ResourceList {
	if len(k) == 0 {
		return o.Status().Capacity
	}
	ret := map[corev1.ResourceName]resource.Quantity{}
	for _, k := range k {
		ret[k] = o.Status().Capacity[k]
	}
	return ret
}

////////////////////////////////////////////////////////////////////////////////
//
// Utils for checking if a pod can fit on a node.
//

// Tolerates returns true if a pod's tolerations cover all the node's taints.
func (n Node) Tolerates(p *Pod) bool {
	taints := n.Raw().Spec.Taints
	tols := p.Raw().Spec.Tolerations
	for _, taint := range taints {
		if !scheduling.TolerationsTolerateTaint(tols, &taint) {
			return false
		}
	}
	return true
}

// Selects returns true if a pod's label requirements are met by the node. Selector labels
// and required node affinity are both handled.
func (n Node) Selects(p *Pod) (bool, error) {
	rna := nodeaffinity.GetRequiredNodeAffinity(p.Raw())
	return rna.Match(n.Raw())
}

// Fits returns true if a pod's resource requests fit within a node's available resources. Also
// returned are: the resource list if the pod was scheduled, and the resource list of negative
// misses (respecitvely).
// **Be sure to call `CountRequested()` before calling this.**
func (n Node) Fits(p *Pod) (bool, corev1.ResourceList, corev1.ResourceList) {
	fits := true
	after := n.Available().DeepCopy()
	fails := corev1.ResourceList{}
	for t, q := range p.Requests() {
		q0 := after[t]
		if q0.Cmp(q) < 0 {
			fits = false
		}
		q0.Sub(q)
		after[t] = q0
		if q0.Cmp(resource.Quantity{}) < 0 {
			fails[t] = q0
		}
	}
	return fits, after, fails
}

// Requested returns the sum of resources requested by pods on the node.
// **Be sure to call `CountRequested()` before calling this.**
func (n Node) Requested() corev1.ResourceList {
	return n.requested
}

// Available returns the remaining resources on the node. It is
// `Capacity() - Requested()`.
// **Be sure to call `CountRequested()` before calling this.**
func (n Node) Available() corev1.ResourceList {
	avail := n.Capacity().DeepCopy()
	for t, q := range n.Requested() {
		q0 := avail[t]
		q0.Sub(q)
		avail[t] = q0
	}
	return avail
}

// CountRequested tallies the resource requests of pods on the node.
func (n *Node) CountRequested(pods ...*Pod) {
	if n.requested == nil {
		n.requested = corev1.ResourceList{}
	}
	podOnNode := func(pod *Pod) bool {
		// TODO(mattm): Fix this to check status, etc.
		if pod.NodeName() != n.Name() {
			return false
		}
		return true
	}
	for _, pod := range pods {
		if !podOnNode(pod) {
			continue
		}
		for t, q := range pod.Requests() {
			q0 := n.requested[t]
			q0.Add(q)
			n.requested[t] = q0
		}
	}
}

// CountResourceUsage tallies the resource usage of each node.
func CountResourceUsage(nodes []*Node, pods []*Pod) {
	nidx := map[string]*Node{}
	for _, n := range nodes {
		nidx[n.Name()] = n
	}
	for _, p := range pods {
		if n := nidx[p.NodeName()]; n != nil {
			n.CountRequested(p)
		}
	}
}

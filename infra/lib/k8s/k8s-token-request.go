package k8s

import (
	"encoding/base64"
	"fmt"
	"strings"
	"time"

	authv1 "k8s.io/api/authentication/v1"
)

type TokenRequest struct {
	Object
	raw *authv1.TokenRequest
}

func NewTokenRequest(raw *authv1.TokenRequest) *TokenRequest {
	if raw == nil {
		raw = &authv1.TokenRequest{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "TokenRequest"
	}
	return &TokenRequest{
		Object: NewObject(raw),
		raw:    raw,
	}
}

func NewTokenRequestExp(exp int64) *TokenRequest {
	tr := NewTokenRequest(nil)
	tr.Raw().Spec.ExpirationSeconds = &exp
	return tr
}

func (o TokenRequest) Raw() *authv1.TokenRequest {
	return o.raw
}

func (o TokenRequest) Token() string {
	return o.Raw().Status.Token
}

func (o TokenRequest) ExpirationTime() time.Time {
	return o.Raw().Status.ExpirationTimestamp.Time
}

func (o TokenRequest) ExpiresIn(now time.Time) time.Duration {
	return o.ExpirationTime().Sub(now)
}

func (o TokenRequest) JWTHeaderAndPayloadUnverified() (string, string, error) {
	parts := strings.Split(o.Token(), ".")
	if l := len(parts); l != 3 {
		return "", "", fmt.Errorf("raw jwt has %d parts, expecting 3", l)
	}
	hdr64, payload64 := parts[0], parts[1]
	hdr, err := base64.RawStdEncoding.DecodeString(hdr64)
	if err != nil {
		return "", "", err
	}
	payload, err := base64.RawStdEncoding.DecodeString(payload64)
	if err != nil {
		return "", "", err
	}
	return string(hdr), string(payload), nil
}

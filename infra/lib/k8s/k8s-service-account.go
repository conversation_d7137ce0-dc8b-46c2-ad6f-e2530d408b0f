package k8s

import (
	"context"
	"encoding/json"
	"strings"

	yaml "github.com/goccy/go-yaml"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime/schema"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"
	metav1a "k8s.io/client-go/applyconfigurations/meta/v1"
)

type ServiceAccount struct {
	Object
	raw *corev1.ServiceAccount
}

type ServiceAccountApplyConfig struct {
	*corev1a.ServiceAccountApplyConfiguration
}

func NewServiceAccount(raw *corev1.ServiceAccount) *ServiceAccount {
	if raw == nil {
		raw = &corev1.ServiceAccount{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "ServiceAccount"
	}
	return &ServiceAccount{
		Object: NewObject(raw),
		raw:    raw,
	}
}

func BuildSimpleServiceAccount(name, namespace string, labels map[string]string) *ServiceAccount {
	sa := NewServiceAccount(nil)
	sa.Raw().ObjectMeta.Name = name
	sa.Raw().ObjectMeta.Namespace = namespace
	sa.Raw().ObjectMeta.Labels = labels

	// NOTE(mattm): Other interesting fields:
	// sa.Raw().ObjectMeta.Annotations = map[string]string{}
	// sa.Raw().Secrets = []ObjectReference
	// sa.Raw().ImagePullSecrets = []LocalObjectReference
	// sa.Raw().AutomountSeriveAccountToken = bool

	return sa
}

func (o ServiceAccount) Raw() *corev1.ServiceAccount {
	return o.raw
}

func (o ServiceAccount) TokenSecret(ctx context.Context, c *Client) (*Secret, error) {
	haystack, err := c.ListSecrets(ctx, ListFieldSelector("type="+string(corev1.SecretTypeServiceAccountToken)))
	if err != nil {
		return nil, err
	}

	matches := map[string]*Secret{}
	names := []string{}
	for _, needle := range haystack {
		if needle.ServiceAccountName() == o.Name() {
			matches[needle.Name()] = needle
			names = append(names, needle.Name())
		}
	}

	if len(matches) > 1 {
		c.LogWarn("%s: multiple token secrets found: %s", o.ShortName(), strings.Join(names, ","))
	}
	for _, match := range matches {
		return match, nil
	}
	return nil, apierrors.NewNotFound(schema.GroupResource{Resource: "secret"}, o.Name()+"-???")
}

func (o ServiceAccount) ApplyConfig(fieldManager string) (*ServiceAccountApplyConfig, error) {
	raw, err := corev1a.ExtractServiceAccount(o.Raw(), fieldManager)
	return &ServiceAccountApplyConfig{ServiceAccountApplyConfiguration: raw}, err
}

func NewServiceAccountApplyConfig(name, namespace string) *ServiceAccountApplyConfig {
	return &ServiceAccountApplyConfig{
		ServiceAccountApplyConfiguration: corev1a.ServiceAccount(name, namespace),
	}
}

func (cfg *ServiceAccountApplyConfig) FromJSON(buf string) error {
	if buf == "" {
		return nil
	}
	return json.Unmarshal([]byte(buf), cfg.Raw())
}

func (cfg *ServiceAccountApplyConfig) Raw() *corev1a.ServiceAccountApplyConfiguration {
	if cfg == nil {
		return nil
	}
	return cfg.ServiceAccountApplyConfiguration
}

func (cfg *ServiceAccountApplyConfig) Name() string {
	if raw := cfg.Raw(); raw == nil {
		return ""
	} else if name := raw.Name; name == nil {
		return ""
	} else {
		return *name
	}
}

func (cfg *ServiceAccountApplyConfig) YAML() (string, error) {
	buf, err := yaml.MarshalWithOptions(cfg.Raw(), yaml.UseJSONMarshaler(), yaml.CustomMarshaler(func(b []byte) ([]byte, error) {
		// Avoid printing lists of individual bytes
		return b, nil
	}))
	return string(buf), err
}

func (cfg *ServiceAccountApplyConfig) JSON() (string, error) {
	buf, err := json.MarshalIndent(cfg.Raw(), "", "  ")
	return string(buf), err
}

func (cfg *ServiceAccountApplyConfig) WithOwner(o *Object) *ServiceAccountApplyConfig {
	owner := metav1a.OwnerReference().
		WithAPIVersion(o.Version()).
		WithKind(o.Kind()).
		WithName(o.Name()).
		WithUID(o.UIDtOrTODO()).
		WithBlockOwnerDeletion(true)
	cfg.Raw().OwnerReferences = nil
	cfg.WithOwnerReferences(owner)
	return cfg
}

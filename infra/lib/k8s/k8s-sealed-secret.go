package k8s

import (
	ssv1a "github.com/bitnami-labs/sealed-secrets/pkg/apis/sealedsecrets/v1alpha1"
)

type SealedSecret struct {
	Object
	raw *ssv1a.SealedSecret
}

func NewSealedSecret(raw *ssv1a.SealedSecret) *SealedSecret {
	if raw == nil {
		raw = &ssv1a.SealedSecret{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "SealedSecret"
	}
	return &SealedSecret{
		Object: NewObject(raw),
		raw:    raw,
	}
}

func (o SealedSecret) Raw() *ssv1a.SealedSecret {
	return o.raw
}

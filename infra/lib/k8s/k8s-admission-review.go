package k8s

import (
	"encoding/json"

	admv1 "k8s.io/api/admission/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	kjson "sigs.k8s.io/json"
)

type AdmissionReview struct {
	raw *admv1.AdmissionReview
}

func NewAdmissionReview(raw *admv1.AdmissionReview) *AdmissionReview {
	if raw == nil {
		raw = &admv1.AdmissionReview{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "AdmissionReview"
	}
	return &AdmissionReview{
		raw: raw,
	}
}

func (o AdmissionReview) Raw() *admv1.AdmissionReview {
	return o.raw
}

func (o AdmissionReview) JSON() (string, error) {
	buf, err := json.MarshalIndent(o.Raw(), "", "  ")
	return string(buf), err
}

func (o AdmissionReview) FromJSON(buf string) error {
	if buf == "" {
		return nil
	}
	if o.raw == nil {
		o.raw = &admv1.AdmissionReview{}
	}
	return kjson.UnmarshalCaseSensitivePreserveInts([]byte(buf), o.raw)
}

func (o AdmissionReview) Request() *admv1.AdmissionRequest {
	return o.Raw().Request
}

func (o AdmissionReview) Response() *admv1.AdmissionResponse {
	return o.Raw().Response
}

func (o *AdmissionReview) InitResponse() *admv1.AdmissionResponse {
	if o.raw.Response == nil {
		o.raw.Response = &admv1.AdmissionResponse{}
	}
	return o.Response()
}

func (o *AdmissionReview) GVK() metav1.GroupVersionKind {
	if req := o.Request(); req != nil {
		return req.Kind
	}
	return metav1.GroupVersionKind{}
}

func (o *AdmissionReview) IsGVK(g, v, k string) bool {
	return o.GVK() == metav1.GroupVersionKind{
		Group:   g,
		Version: v,
		Kind:    k,
	}
}

func (o *AdmissionReview) TryPod() (*Pod, error) {
	if req := o.Request(); req != nil {
		if o.IsGVK("", "v1", "Pod") {
			pod := NewPod(nil)
			if err := pod.FromJSON(string(req.Object.Raw)); err != nil {
				return nil, err
			}
			return pod, nil
		}
	}
	return nil, nil
}

func (o *AdmissionReview) AddPatch(p any) error {
	if buf, err := json.MarshalIndent(p, "", "  "); err != nil {
		return err
	} else {
		jsonpatch := admv1.PatchTypeJSONPatch

		resp := o.InitResponse()
		resp.PatchType = &jsonpatch
		resp.Patch = buf
		return nil
	}
}

func (o *AdmissionReview) SetStatus(code int32, reason metav1.StatusReason, err error) {
	resp := o.InitResponse()
	resp.Result = &metav1.Status{
		Status: metav1.StatusFailure,
		Code:   code,
		Reason: reason,
	}
	if err != nil {
		resp.Result.Message = err.Error()
	}
}

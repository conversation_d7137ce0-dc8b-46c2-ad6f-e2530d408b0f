package k8s

import (
	"encoding/json"

	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
)

func FromJSON(buf []byte) ([]Object, error) {
	var raw any
	if err := json.Unmarshal(buf, &raw); err != nil {
		return nil, err
	}

	objs := []Object{}
	for queue := []any{raw}; len(queue) > 0; {
		pop := queue[0]
		queue = queue[1:]

		switch cur := pop.(type) {
		case []any:
			queue = append(queue, cur...)
			continue
		case map[string]any:
			if cur["kind"] == nil {
				for _, child := range cur {
					queue = append(queue, child)
				}
			} else {
				obj, err := objectFromRaw(cur)
				if err != nil {
					return nil, err
				}
				objs = append(objs, obj)

			}
		default:
			// ignore
		}
	}

	return objs, nil
}

func objectFromRaw(raw map[string]any) (Object, error) {
	u := &unstructured.Unstructured{Object: raw}
	return NewObject(u), nil
}

package k8s

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"

	yaml "github.com/goccy/go-yaml"

	appsv1 "k8s.io/api/apps/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime/schema"
	appsv1a "k8s.io/client-go/applyconfigurations/apps/v1"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"
	metav1a "k8s.io/client-go/applyconfigurations/meta/v1"
)

type Deployment struct {
	Object
	raw *appsv1.Deployment
}

func NewDeployment(raw *appsv1.Deployment) *Deployment {
	if raw == nil {
		raw = &appsv1.Deployment{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "Deployment"
	}
	return &Deployment{
		Object: NewObject(raw),
		raw:    raw,
	}
}

type DeploymentApplyConfig struct {
	*appsv1a.DeploymentApplyConfiguration
}

func (o Deployment) Raw() *appsv1.Deployment {
	return o.raw
}

func (o Deployment) Replicas() int32 {
	if i := o.Raw().Spec.Replicas; i != nil {
		return *i
	}
	return 1
}

func (o Deployment) GetReplicaSet(ctx context.Context, c *Client) (*ReplicaSet, error) {
	replicasets, err := c.ListReplicaSets(ctx, ListLabelSelector(o.Raw().Spec.Selector))
	if err != nil {
		return nil, err
	}
	var ret *ReplicaSet
	for _, replicaset := range replicasets {
		if replicaset.OwnerMatch(o.Object) {
			if ret == nil || replicaset.Revision() > ret.Revision() {
				ret = replicaset
			}
		}
	}
	if ret == nil {
		err := apierrors.NewNotFound(schema.GroupResource{Resource: "replicaset"}, o.ShortName())
		return nil, err
	}
	return ret, nil
}

func (o Deployment) GetPods(ctx context.Context, c *Client) ([]*Pod, error) {
	replicaset, err := o.GetReplicaSet(ctx, c)
	if err != nil {
		return nil, err
	}
	return replicaset.GetPods(ctx, c)
}

func (o Deployment) GetPod(ctx context.Context, c *Client) (*Pod, error) {
	pods, err := o.GetPods(ctx, c)
	if err != nil {
		return nil, err
	}
	if len(pods) == 0 {
		err := apierrors.NewNotFound(schema.GroupResource{Resource: "pod"}, o.ShortName())
		return nil, err
	}
	if l := len(pods); l != 1 {
		return nil, fmt.Errorf("Found %d pods, expected 1", l)
	}
	return pods[0], nil
}

func (o Deployment) StatusLine() string {
	return fmt.Sprintf("deploy/%s: %s", o.Name(), o.Status().String())
}

func (o Deployment) Status() DeploymentStatus {
	return DeploymentStatus{o.Raw().Status}
}

type DeploymentStatus struct {
	appsv1.DeploymentStatus
}

func (s DeploymentStatus) String() string {
	return fmt.Sprintf("%d/%d/%d Ready/Updated/Total Replicas", s.ReadyReplicas, s.UpdatedReplicas, s.Replicas)
}

func (s DeploymentStatus) Conditions() []DeploymentCondition {
	cs := []DeploymentCondition{}
	for _, c := range s.DeploymentStatus.Conditions {
		cs = append(cs, DeploymentCondition{c})
	}
	sort.Slice(cs, func(i, j int) bool {
		ti := &cs[i].LastTransitionTime
		tj := &cs[j].LastTransitionTime
		return ti.Before(tj)
	})
	return cs
}

func (s DeploymentStatus) LatestCondition() DeploymentCondition {
	latest := DeploymentCondition{}
	for _, c := range s.Conditions() {
		if (&latest.LastTransitionTime).Before(&c.LastTransitionTime) {
			latest = c
		}
	}
	return latest
}

type DeploymentCondition struct {
	appsv1.DeploymentCondition
}

func (c DeploymentCondition) String() string {
	t := c.LastTransitionTime.Format("2006-01-02 15:04 MST")
	s := fmt.Sprintf("%s %s=%s", t, c.Type, c.Status)
	if c.Reason != "" || c.Message != "" {
		s += ":"
	}
	if c.Reason != "" {
		s += fmt.Sprintf(" [%s]", c.Reason)
	}
	if c.Message != "" {
		s += " " + c.Message
	}
	return s
}

func (o Deployment) ApplyConfig(fieldManager string) (*DeploymentApplyConfig, error) {
	raw, err := appsv1a.ExtractDeployment(o.Raw(), fieldManager)
	return &DeploymentApplyConfig{DeploymentApplyConfiguration: raw}, err
}

func NewDeploymentApplyConfig(name, namespace string) *DeploymentApplyConfig {
	return &DeploymentApplyConfig{
		DeploymentApplyConfiguration: appsv1a.Deployment(name, namespace),
	}
}

func (cfg *DeploymentApplyConfig) FromJSON(buf string) error {
	if buf == "" {
		return nil
	}
	return json.Unmarshal([]byte(buf), cfg.Raw())
}

func (cfg *DeploymentApplyConfig) Raw() *appsv1a.DeploymentApplyConfiguration {
	if cfg == nil {
		return nil
	}
	return cfg.DeploymentApplyConfiguration
}

func (cfg *DeploymentApplyConfig) Name() string {
	if raw := cfg.Raw(); raw == nil {
		return ""
	} else if name := raw.Name; name == nil {
		return ""
	} else {
		return *name
	}
}

func (cfg *DeploymentApplyConfig) YAML() (string, error) {
	buf, err := yaml.MarshalWithOptions(cfg.Raw(), yaml.UseJSONMarshaler(), yaml.CustomMarshaler(func(b []byte) ([]byte, error) {
		// Avoid printing lists of individual bytes
		return b, nil
	}))
	return string(buf), err
}

func (cfg *DeploymentApplyConfig) JSON() (string, error) {
	buf, err := json.MarshalIndent(cfg.Raw(), "", "  ")
	return string(buf), err
}

func (cfg *DeploymentApplyConfig) WithOwner(o *Object) *DeploymentApplyConfig {
	owner := metav1a.OwnerReference().
		WithAPIVersion(o.Version()).
		WithKind(o.Kind()).
		WithName(o.Name()).
		WithUID(o.UIDtOrTODO()).
		WithBlockOwnerDeletion(true)
	cfg.Raw().OwnerReferences = nil
	cfg.WithOwnerReferences(owner)
	return cfg
}

func (cfg *DeploymentApplyConfig) Volumes() []corev1a.VolumeApplyConfiguration {
	if raw := cfg.Raw(); raw == nil {
		return nil
	} else if spec := raw.Spec; spec == nil {
		return nil
	} else if tmpl := spec.Template; tmpl == nil {
		return nil
	} else if pod := tmpl.Spec; spec == nil {
		return nil
	} else {
		return pod.Volumes
	}
}

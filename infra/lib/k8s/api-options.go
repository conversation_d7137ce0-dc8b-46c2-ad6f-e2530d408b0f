package k8s

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type ListOpt func(*metav1.ListOptions)

func ListOptions(opts ...ListOpt) metav1.ListOptions {
	listopts := metav1.ListOptions{}
	for _, o := range opts {
		o(&listopts)
	}
	return listopts
}

func ListMatchLabels(labels map[string]string) ListOpt {
	ls := &metav1.LabelSelector{
		MatchLabels: labels,
	}
	return ListLabelSelector(ls)
}

func ListLabelSelector(ls *metav1.LabelSelector) ListOpt {
	str := metav1.FormatLabelSelector(ls)
	return func(listopts *metav1.ListOptions) {
		listopts.LabelSelector = str
	}
}

func ListFieldSelector(fs string) ListOpt {
	return func(listopts *metav1.ListOptions) {
		listopts.FieldSelector = fs
	}
}

func ListByName(name string) ListOpt {
	return ListFieldSelector("metadata.name=" + name)
}

func ListWatch(b bool) ListOpt {
	return func(listopts *metav1.ListOptions) {
		listopts.Watch = b
	}
}

func ListAllowWatchBookmarks(b bool) ListOpt {
	return func(listopts *metav1.ListOptions) {
		listopts.AllowWatchBookmarks = b
	}
}

// ListResourceVersion sets the ResourceVersion and ResourseVersionMatch list options.
// https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions.
// Since it is invalid to set ResourceVersionMatch without a ResourceVersion, if
// `ver` is unset, then `match` will be ignored.
func ListResourceVersion(ver string, match metav1.ResourceVersionMatch) ListOpt {
	if ver == "" {
		match = ""
	}
	return func(listopts *metav1.ListOptions) {
		listopts.ResourceVersion = ver
		listopts.ResourceVersionMatch = match
	}
}

func ListSendInitialEvents(b bool) ListOpt {
	bp := new(bool)
	*bp = b
	return func(listopts *metav1.ListOptions) {
		listopts.SendInitialEvents = bp
	}
}

type GetOpt func(*metav1.GetOptions)

func GetOptions(opts ...GetOpt) metav1.GetOptions {
	getopts := metav1.GetOptions{}
	for _, o := range opts {
		o(&getopts)
	}
	return getopts
}

func PatchOptions() metav1.PatchOptions {
	return metav1.PatchOptions{}
}

type ApplyOpt func(*metav1.ApplyOptions)

func ApplyOptions(opts ...ApplyOpt) metav1.ApplyOptions {
	applyopts := metav1.ApplyOptions{}
	for _, o := range opts {
		o(&applyopts)
	}
	return applyopts
}

func ApplyDryRun(b bool) ApplyOpt {
	return func(o *metav1.ApplyOptions) {
		if b {
			o.DryRun = []string{metav1.DryRunAll}
		} else {
			o.DryRun = nil
		}
	}
}

func ApplyForce(b bool) ApplyOpt {
	return func(o *metav1.ApplyOptions) {
		o.Force = b
	}
}

func ApplyFieldManager(f string) ApplyOpt {
	return func(o *metav1.ApplyOptions) {
		o.FieldManager = f
	}
}

func CreateOptions() metav1.CreateOptions {
	return metav1.CreateOptions{}
}

func UpdateOptions() metav1.UpdateOptions {
	return metav1.UpdateOptions{}
}

func DeleteOptions() metav1.DeleteOptions {
	return metav1.DeleteOptions{}
}

type PodLogOpt func(*corev1.PodLogOptions)

func PodLogOptions(opts ...PodLogOpt) *corev1.PodLogOptions {
	logopts := &corev1.PodLogOptions{}
	for _, o := range opts {
		o(logopts)
	}
	return logopts
}

func PodLogContainer(c string) PodLogOpt {
	return func(o *corev1.PodLogOptions) {
		o.Container = c
	}
}

func PodLogFollow(b bool) PodLogOpt {
	return func(o *corev1.PodLogOptions) {
		o.Follow = b
	}
}

func PodLogTimestamps(b bool) PodLogOpt {
	return func(o *corev1.PodLogOptions) {
		o.Timestamps = b
	}
}

func PodLogTail(i int64) PodLogOpt {
	return func(o *corev1.PodLogOptions) {
		if i == 0 {
			o.TailLines = nil
		} else {
			o.TailLines = &i
		}
	}
}

package k8s

import (
	"context"
	"encoding/json"
	"strconv"
	"strings"

	yaml "github.com/goccy/go-yaml"

	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/types"

	corev1 "k8s.io/api/core/v1"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"
	metav1a "k8s.io/client-go/applyconfigurations/meta/v1"
)

// PVCAccessMode parses `mode`. It supports full names and aliases:
// - ReadWriteOnce, rwo
// - ReadOnlyMany, rox
// - ReadWriteMany, rwx
// - ReadWriteOncePod, rwop
//
// If `mode` doesn't match one of the well-known modes, it is still returned as
// a `corev1.PersistentVolumeAccessMode`.
func PVCAccessMode[T string | corev1.PersistentVolumeAccessMode](mode T) corev1.PersistentVolumeAccessMode {
	switch strings.ToLower(string(mode)) {
	case strings.ToLower(string(corev1.ReadWriteOnce)), "rwo":
		return corev1.ReadWriteOnce
	case strings.ToLower(string(corev1.ReadOnlyMany)), "rox":
		return corev1.ReadOnlyMany
	case strings.ToLower(string(corev1.ReadWriteMany)), "rwx":
		return corev1.ReadWriteMany
	case strings.ToLower(string(corev1.ReadWriteOncePod)), "rwop":
		return corev1.ReadWriteOncePod
	default:
		return corev1.PersistentVolumeAccessMode(mode)
	}
}

type PVC struct {
	Object
	raw *corev1.PersistentVolumeClaim
}

type PVCApplyConfig struct {
	*corev1a.PersistentVolumeClaimApplyConfiguration
}

func NewPVC(raw *corev1.PersistentVolumeClaim) *PVC {
	if raw == nil {
		raw = &corev1.PersistentVolumeClaim{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "PersistentVolumeClaim"
	}
	return &PVC{
		Object: NewObject(raw),
		raw:    raw,
	}
}

func (o PVC) Raw() *corev1.PersistentVolumeClaim {
	return o.raw
}

func (o PVC) Phase() corev1.PersistentVolumeClaimPhase {
	return o.Raw().Status.Phase
}

func (o PVC) Class() string {
	if c := o.Raw().Spec.StorageClassName; c != nil {
		return *c
	}
	return ""
}

func (o PVC) AccessModes() []corev1.PersistentVolumeAccessMode {
	return o.Raw().Spec.AccessModes
}

func (o PVC) AccessModesString() string {
	modes := []string{}
	for _, m := range o.AccessModes() {
		modes = append(modes, string(m))
	}
	return strings.Join(modes, ",")
}

func (o PVC) Capacity() string {
	if q, ok := o.Raw().Spec.Resources.Requests[corev1.ResourceStorage]; ok {
		return q.String()
	}
	return ""
}

func (o PVC) StatusCapacity() string {
	if q, ok := o.Raw().Status.Capacity[corev1.ResourceStorage]; ok {
		return q.String()
	}
	return ""
}

func (o PVC) PatchSize(ctx context.Context, c *Client, q resource.Quantity) (*PVC, error) {
	data := struct {
		Spec struct {
			Resources struct {
				Requests corev1.ResourceList `json:"requests"`
			} `json:"resources"`
		} `json:"spec"`
	}{}
	data.Spec.Resources.Requests = corev1.ResourceList{corev1.ResourceStorage: q}
	return c.PatchPVC(ctx, o.Name(), types.StrategicMergePatchType, data)
}

// Grow takes either an absolute capacity or a relative size if starting with a
// `+` prefix. The new capacity is then patched into the PVC. If the given capacity is
// unitless, the existing units are matched.
func (o PVC) Grow(ctx context.Context, c *Client, arg string, dryrun bool) (*PVC, error) {
	cur := o.Raw().Spec.Resources.Requests[corev1.ResourceStorage]
	q, err := GrowSize(cur, arg)
	if err != nil {
		return nil, err
	}
	c.LogInfo("%s: capacity %s ==> %s.", o.ShortName(), cur.String(), q.String())

	if dryrun {
		return nil, nil
	} else {
		return o.PatchSize(ctx, c, q)
	}
}

func GrowSize(cur resource.Quantity, arg string) (resource.Quantity, error) {
	// Strip any leading `+` and remember it in `add`.
	arg, add := strings.CutPrefix(arg, "+")

	// If we can parse a float without error, then `arg` was given without units. Copy them
	// from the current quantity.
	if _, err := strconv.ParseFloat(arg, 64); err == nil {
		buf := make([]byte, 128) // eww?
		_, sfx := cur.CanonicalizeBytes(buf)
		arg += string(sfx)
	}

	// Parse the provided quanitity, without `+`, and with units defaulted to the existing.
	q, err := resource.ParseQuantity(arg)
	if err != nil {
		return resource.Quantity{}, err
	}

	// When adding, we should add the new capacity to the original -- not the
	// other way around -- to preserve the original units.
	if add {
		tmp := q
		q = cur
		q.Add(tmp)
	}

	return q, nil
}

func (o PVC) ApplyConfig(fieldManager string) (*PVCApplyConfig, error) {
	raw, err := corev1a.ExtractPersistentVolumeClaim(o.Raw(), fieldManager)
	return &PVCApplyConfig{PersistentVolumeClaimApplyConfiguration: raw}, err
}

func NewPVCApplyConfig(name, namespace string) *PVCApplyConfig {
	return &PVCApplyConfig{
		PersistentVolumeClaimApplyConfiguration: corev1a.PersistentVolumeClaim(name, namespace),
	}
}

func (cfg *PVCApplyConfig) FromJSON(buf string) error {
	if buf == "" {
		return nil
	}
	return json.Unmarshal([]byte(buf), cfg.Raw())
}

func (cfg *PVCApplyConfig) Raw() *corev1a.PersistentVolumeClaimApplyConfiguration {
	if cfg == nil {
		return nil
	}
	return cfg.PersistentVolumeClaimApplyConfiguration
}

func (cfg *PVCApplyConfig) Name() string {
	if raw := cfg.Raw(); raw == nil {
		return ""
	} else if name := raw.Name; name == nil {
		return ""
	} else {
		return *name
	}
}

func (cfg *PVCApplyConfig) YAML() (string, error) {
	buf, err := yaml.MarshalWithOptions(cfg.Raw(), yaml.UseJSONMarshaler(), yaml.CustomMarshaler(func(b []byte) ([]byte, error) {
		// Avoid printing lists of individual bytes
		return b, nil
	}))
	return string(buf), err
}

func (cfg *PVCApplyConfig) JSON() (string, error) {
	buf, err := json.MarshalIndent(cfg.Raw(), "", "  ")
	return string(buf), err
}

func (cfg *PVCApplyConfig) WithOwner(o *Object) *PVCApplyConfig {
	owner := metav1a.OwnerReference().
		WithAPIVersion(o.Version()).
		WithKind(o.Kind()).
		WithName(o.Name()).
		WithUID(o.UIDtOrTODO()).
		WithBlockOwnerDeletion(true)
	cfg.Raw().OwnerReferences = nil
	cfg.WithOwnerReferences(owner)
	return cfg
}

package k8s

import (
	"fmt"
	"time"

	snapshotv1 "github.com/kubernetes-csi/external-snapshotter/client/v8/apis/volumesnapshot/v1"
	"k8s.io/apimachinery/pkg/api/resource"
)

type VolumeSnapshot struct {
	Object
	raw *snapshotv1.VolumeSnapshot
}

func NewVolumeSnapshot(raw *snapshotv1.VolumeSnapshot) *VolumeSnapshot {
	if raw == nil {
		raw = &snapshotv1.VolumeSnapshot{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "VolumeSnapshot"
	}
	return &VolumeSnapshot{
		Object: NewObject(raw),
		raw:    raw,
	}
}

func (o VolumeSnapshot) Raw() *snapshotv1.VolumeSnapshot {
	return o.raw
}

func (o VolumeSnapshot) PVC() string {
	if n := o.Raw().Spec.Source.PersistentVolumeClaimName; n != nil {
		return *n
	}
	return ""
}

func (o VolumeSnapshot) ReadyToUse() (bool, error) {
	var b bool
	var err error
	if bp := o.Raw().Status.ReadyToUse; bp != nil {
		b = *bp
	}
	if vserr := o.Raw().Status.Error; vserr != nil {
		ts, msg := time.Time{}, ""
		if v := vserr.Time; v != nil {
			ts = v.Time
		}
		if v := vserr.Message; v != nil {
			msg = *v
		}
		err = fmt.Errorf("%v: %s", ts, msg)
	}
	return b, err
}

func (o VolumeSnapshot) RestoreSize() *resource.Quantity {
	return o.Raw().Status.RestoreSize
}

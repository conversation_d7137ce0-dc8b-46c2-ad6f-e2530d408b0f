package k8s

import (
	"encoding/json"

	yaml "github.com/goccy/go-yaml"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"
	metav1a "k8s.io/client-go/applyconfigurations/meta/v1"
)

type Service struct {
	Object
	raw *corev1.Service
}

type ServiceApplyConfig struct {
	*corev1a.ServiceApplyConfiguration
}

func NewService(raw *corev1.Service) *Service {
	if raw == nil {
		raw = &corev1.Service{}
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = "Service"
	}
	return &Service{
		Object: NewObject(raw),
		raw:    raw,
	}
}

func (o Service) Raw() *corev1.Service {
	return o.raw
}

func (o Service) Type() corev1.ServiceType {
	return o.Raw().Spec.Type
}

func (o Service) ClusterIP() string {
	return o.Raw().Spec.ClusterIP
}

func (o Service) PublicIP() string {
	for _, ingress := range o.Raw().Status.LoadBalancer.Ingress {
		if ip := ingress.IP; ip != "" {
			return ip
		}
	}
	return ""
}

// ExternalDNSHostname returns a hostname, if available, from https://github.com/kubernetes-sigs/external-dns.
// Currently, it uses the documented `external-dns.alpha.kubernetes.io/hostname` annotation.
func (o Service) ExternalDNSHostname() string {
	return o.Annotation("external-dns.alpha.kubernetes.io/hostname")
}

// ExternalDNSHostnameSet sets the hostname. See `ExternalDNSHostname` for further details.
func (o Service) ExternalDNSHostnameSet(hn string) {
	o.SetAnnotation("external-dns.alpha.kubernetes.io/hostname", hn)
}

// AppendPort builds a ServicePort and adds it to the Service. If target is 0, it defaults to port. For
// other ServicePort options such as a string target port, AppProtocol, etc, build and append directly.
func (o *Service) AppendPort(name string, port, target int32, proto corev1.Protocol) {
	if target == 0 {
		target = port
	}
	o.Raw().Spec.Ports = append(o.Raw().Spec.Ports, corev1.ServicePort{
		Name:       name,
		Port:       port,
		TargetPort: intstr.FromInt32(target),
		Protocol:   proto,
	})
}

func (o Service) ApplyConfig(fieldManager string) (*ServiceApplyConfig, error) {
	raw, err := corev1a.ExtractService(o.Raw(), fieldManager)
	return &ServiceApplyConfig{ServiceApplyConfiguration: raw}, err
}

func NewServiceApplyConfig(name, namespace string) *ServiceApplyConfig {
	return &ServiceApplyConfig{
		ServiceApplyConfiguration: corev1a.Service(name, namespace),
	}
}

func (cfg *ServiceApplyConfig) FromJSON(buf string) error {
	if buf == "" {
		return nil
	}
	return json.Unmarshal([]byte(buf), cfg.Raw())
}

func (cfg *ServiceApplyConfig) Raw() *corev1a.ServiceApplyConfiguration {
	if cfg == nil {
		return nil
	}
	return cfg.ServiceApplyConfiguration
}

func (cfg *ServiceApplyConfig) Name() string {
	if raw := cfg.Raw(); raw == nil {
		return ""
	} else if name := raw.Name; name == nil {
		return ""
	} else {
		return *name
	}
}

func (cfg *ServiceApplyConfig) YAML() (string, error) {
	buf, err := yaml.MarshalWithOptions(cfg.Raw(), yaml.UseJSONMarshaler(), yaml.CustomMarshaler(func(b []byte) ([]byte, error) {
		// Avoid printing lists of individual bytes
		return b, nil
	}))
	return string(buf), err
}

func (cfg *ServiceApplyConfig) JSON() (string, error) {
	buf, err := json.MarshalIndent(cfg.Raw(), "", "  ")
	return string(buf), err
}

func (cfg *ServiceApplyConfig) WithOwner(o *Object) *ServiceApplyConfig {
	owner := metav1a.OwnerReference().
		WithAPIVersion(o.Version()).
		WithKind(o.Kind()).
		WithName(o.Name()).
		WithUID(o.UIDtOrTODO()).
		WithBlockOwnerDeletion(true)
	cfg.Raw().OwnerReferences = nil
	cfg.WithOwnerReferences(owner)
	return cfg
}

func (cfg *ServiceApplyConfig) WithPort(name string, proto corev1.Protocol, port int32, targetPort int32) *ServiceApplyConfig {
	cfg.Raw().Spec.WithPorts(corev1a.ServicePort().
		WithName(name).
		WithProtocol(proto).
		WithPort(port).
		WithTargetPort(intstr.FromInt32(targetPort)))
	return cfg
}

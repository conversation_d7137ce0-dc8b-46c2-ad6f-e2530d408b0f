from dataclasses import dataclass
from typing import Literal

from base.ranges.range_types import LineRange


DiagnosticSeverity = Literal["ERROR", "WARNING", "INFORMATION", "HINT"]


@dataclass(frozen=True)
class DiagnosticFileLocation:
    """A location in a file."""

    path: str
    """The path of the file."""

    line_start: int
    """The line start of the edit (inclusive). Based on last known state of the file."""

    line_end: int
    """The line end of the edit (exclusive). Based on last known state of the file."""

    @property
    def lrange(self) -> LineRange:
        """The range of the diagnostic."""
        return LineRange(self.line_start, self.line_end)


@dataclass(frozen=True)
class Diagnostic:
    """A diagnostic message."""

    location: DiagnosticFileLocation
    """The range of the diagnostic."""

    message: str
    """The diagnostic message."""

    severity: DiagnosticSeverity
    """The severity of the diagnostic."""

from pathlib import Path
from random import Random

import pytest

from research.core.changes import Modified
from research.next_edits.edit_gen_sampler import EditGenProblem, EditGenSampler
from research.next_edits.wip_repo_sampler import WipSamplingStrategy
from research.utils.repo_change_utils import (
    CommitMeta,
    FileTuple,
    RepoChange,
    pyr_map,
    pyr_vector,
)


@pytest.mark.parametrize("wip_sampling_strategy", ["uniform", "keep_most"])
def test_edit_gen_sampler_runs(wip_sampling_strategy: WipSamplingStrategy):
    """Test that EditGenSampler runs on a small test repo."""
    before_code = """\
aaaaa
bbbbb
ccccc
ddddd
"""
    before_file = FileTuple(Path("file1.py"), before_code)

    after_code = """\
aaxaa
bbbbb
ccccc
new line
ddddd
eeeee
"""
    after_file = FileTuple(Path("file1.py"), after_code)

    repo_change = RepoChange(
        before_files=pyr_map({Path("file1.py"): before_code}),
        after_files=pyr_map({Path("file1.py"): after_code}),
        changed_files=pyr_vector([Modified(before_file, after_file)]),
    )
    commit_info = CommitMeta("hash", message="a fake commit used for testing.")

    sampler = EditGenSampler(wip_sampling_strategy=wip_sampling_strategy)
    rng = Random(42)
    problems = list[EditGenProblem]()
    for _ in range(500):
        problems.extend(sampler.sample_problems(rng, repo_change, commit_info, None))

    # We should be able to sometimes sample a problem from file1's change.
    assert len(problems) > 0
    for problem in problems:
        assert problem.current_path == Path("file1.py")

"""Sampling synthetic data for edit generation."""

from __future__ import annotations

from base.caching.lru_cache import lru_cache
import logging
import math
import re
from dataclasses import dataclass, field
from pathlib import Path
from random import Random
from typing import Any, Iterable, Sequence, assert_never

from pyrsistent import PMap as PyrMap, pmap as pyr_map

from base.diff_utils.changes import Changed
from base.languages.language_guesser import guess_language
from base.languages.languages import LanguageId
from base.prompt_format_next_edit.gen_prompt_formatter import (
    EditGenOutput,
    equal_modulo_spaces,
)
from base.ranges.line_map import LineMap
from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>, LineRange
from research.core.artifacts import collect_artifacts, post_artifact
from research.core.changes import Modified, Unchanged
from research.core.str_diff import NoopSpan, precise_line_diff
from research.next_edits.next_edits_dataset import PRMeta, OrderedRepoChange
from research.next_edits.smart_chunking import SmartChunker
from research.next_edits.wip_repo_sampler import (
    OrderedWipRepoSampler,
    SimulatedWip<PERSON><PERSON>,
    WipRepoSampler,
    WipSamplingStrategy,
    find_import_change_lines,
    lerp_round,
    simulate_wip_code,
)
from research.utils.repo_change_utils import CommitMeta, FileTuple, RepoChange

__reexported__ = [EditGenOutput]


@dataclass
class EditGenProblem:
    """An edit generation problem."""

    current_path: Path
    """The file path of the target file."""

    prev_path: Path | None
    """The previous path of the target file, before the repo_change. None means the file is newly added."""

    current_code: str
    """Current contents of the target file.

    Note that an EditGen problem assumes that the user/retriever has selected a line
    range in an already existing file, and this should be the corresponding contents
    at the time of the selection.
    """

    final_code: str
    """The contents of the entire target file in the PR's finished state.

    `output.replacement` is always a substring of `final_code`.
    """

    edit_region: CharRange
    """The character range of the selected edit region in `current_code`."""

    instruction: str
    """An optional editing instruction."""

    repo_change: RepoChange
    """Records how the repo has changed since the last commit/PR.

    repo_change.after_files contains the update-to-date file contents at the time
    of the edit. The diffs constructed from `repo_change` can be shown to
    the model as additional inputs.
    """

    output: EditGenOutput
    """The expected output."""

    commit_meta: CommitMeta
    """The commit from which this problem is sampled.

    Note that because we simulate work-in-progress repo state, contents of this
    problem may not match the original commit content exactly.
    """

    pr_meta: PRMeta | None = None
    """The PR metadata of the commit.

    This is None if the commit is not from a PR.
    """

    changed_final_files: PyrMap[Path, str] | None = None
    """The final state of the repo at the end of the commit/PR.

    This only records the subset of the files that are modified by the original
    commit/PR. This is None for old data sampled before sampler version 30.
    """

    debug_info: dict[str, Any] = field(default_factory=dict)
    """Extra info recording how the problem was sampled."""

    @property
    def prefix(self) -> str:
        """Get the prefix of the selected text in the current path."""
        return self.current_code[: self.edit_region.start]

    @property
    def suffix(self) -> str:
        """Get the suffix of the selected text in the current path."""
        return self.current_code[self.edit_region.stop :]

    def is_whitespace_change(self) -> bool:
        """Whether the change is whitespace-only."""
        return (self.selected_code != self.output.replacement) and equal_modulo_spaces(
            self.selected_code, self.output.replacement
        )

    @property
    def selected_code(self) -> str:
        return self.current_code[self.edit_region.to_slice()]

    def current_to_future(self) -> RepoChange:
        """Get the repo change from the current state to the final state.

        Note that this currently only record file modifications.
        """
        if self.changed_final_files is None:
            raise ValueError("final_files is not available.")
        current_files = self.repo_change.after_files
        changed_files = list[Changed[FileTuple]]()
        for path, final_code in self.changed_final_files.items():
            if (
                current_code := current_files.get(path)
            ) is not None and current_code != final_code:
                changed_files.append(
                    Modified(FileTuple(path, current_code), FileTuple(path, final_code))
                )
        return RepoChange.from_before_and_changes(current_files, changed_files)


@dataclass
class EditGenSampler:
    """Sampling synthetic data for edit generation."""

    max_file_chars: int = 100_000
    """Will not sample from files larger than this size limit."""

    max_line_width: int = 300
    """Will not sample from files with lines longer than this limit."""

    random_edit_region_rate: float = 0.7
    """The probability of sampling a random edit region instead of sampling from a
    region that requires additional changes."""

    random_target_file_rate: float = 0.2
    """The probability of sampling a random target file instead of sampling from a
    file changed by the commit/PR."""

    max_diff_tokens: int = 2000
    """Changes with more than this many tokens will (effectively) be downsampled."""

    max_edit_region_lines: int = 120
    """The max size (in lines) of a sampled edit region.

    Note that this limit is ignored when sampling based on the rechunker.
    """

    min_edit_region_lines: int = 5
    """The min size (in lines) of a sampled edit region.

    Note that it's possible to sample edit regions smaller than this limit if the file
    itself is too small.
    """

    rechunker_rate: float = 0.75
    """The probability of rechunking the target file before sampling an edit region."""

    max_chunk_size: int = 3000
    """The max chunk size used by the smart chunker."""

    wip_sampling_strategy: WipSamplingStrategy = "keep_most"
    """The strategy that decides how to partically drop changes."""

    repo_change_wip_ratio: float = 0.6
    """How incomplete the sampled WIP repo state should be.

    0 means always sampling from the final state, whereas 1 means uniformly sampling
    from the begin to the final state.
    """

    def sample_problems_with_order(
        self,
        rng: Random,
        repo_change_with_order: OrderedRepoChange,
        prev_changes: Sequence[RepoChange],
    ) -> Iterable[EditGenProblem]:
        """Sample edit generation problems from a given repo change with hunk order."""
        prev_change = prev_changes[-1] if prev_changes else None
        wip_states = OrderedWipRepoSampler().simulate_wip_repo_states_with_order(
            repo_change_with_order, prev_change, rng
        )
        wip_states = list(wip_states)
        # only use the near-final repo state to ensure most changes are in context
        N = len(wip_states)
        first_state_group_id = round(N * 0.75)
        wip_states = wip_states[first_state_group_id:]

        # the set of (selected_code, replacement) pairs sampled so far
        changes_sampled = set[tuple[str, str]]()

        for i, wip_repo_state in enumerate(wip_states):
            past_to_wip = wip_repo_state.past_to_wip
            wip_to_future = wip_repo_state.wip_to_future
            debug_info = wip_repo_state.debug_info
            debug_info["group_id"] = first_state_group_id + i
            candidate_files: list[Modified[FileTuple] | Unchanged[FileTuple]]
            candidate_files = [
                change
                for change in wip_to_future.changed_files
                if isinstance(change, Modified) and self._is_candidate_file(change)
            ]
            if not candidate_files:
                continue
            # Then we also randomly add some unchanged files to target_files. These
            # files will always produce negative examples and improve the diversity of
            # the negative examples.
            n_to_add = 0
            for _ in candidate_files:
                if rng.random() < self.random_target_file_rate:
                    n_to_add += 1
            unchanged_files = list(wip_to_future.unchanged_files())
            rng.shuffle(unchanged_files)
            for file in unchanged_files[:n_to_add]:
                candidate_files.append(Unchanged(file))
            rng.shuffle(candidate_files)
            for file_change in candidate_files:
                with collect_artifacts() as collector:
                    prob = self._sample_problem_with_order(
                        rng,
                        file_change,
                        past_to_wip,
                        commit_meta=repo_change_with_order.commit_meta,
                        pr_meta=repo_change_with_order.pr_meta,
                    )
                    # use the group description as the instruction
                    # prob.instruction = wip_repo_state.description
                    debug_info["_sample_problem_with_order"] = (
                        collector.get_flattened_artifacts()
                    )
                prob.debug_info = debug_info
                prob_key = (prob.selected_code, prob.output.replacement)
                if prob_key not in changes_sampled:
                    changes_sampled.add(prob_key)
                    yield prob

    def sample_problems(
        self,
        rng: Random,
        repo_change: RepoChange,
        commit_info: CommitMeta,
        pr_meta: PRMeta | None,
    ) -> Iterable[EditGenProblem]:
        """Sample edit generation problems from a given commit/PR.

        Suppose the commit contains `N` changed files. We first use `WipRepoSampler`
        to sample `N` WIP repo states (one after each changed file). For each WIP
        repo state, we follow the step below to sample an edit generation problem:

        1. Compare the WIP repo state with the final commit/PR state, identify which
        files require additional changes, then randomly pick one of them as the target
        file.
        2. Sample an edit region in the target file. The `random_edit_region_rate`
        controls the frequency in which we sample a random edit region.
        3. Compute the text that should replace the selected region, then construct
        the edit generation problem accordingly.
        """

        candidate_files = {
            change
            for change in repo_change.changed_files
            if isinstance(change, Modified) and self._is_candidate_file(change)
        }
        if not candidate_files:
            return

        # sample a set of WIP repo changes to be used as prediction context
        wip_repo_changes = WipRepoSampler().simulate_wip_repo_states(repo_change, rng)
        wip_repo_changes = list(wip_repo_changes)
        if not wip_repo_changes:
            logging.warning(f"No changes sampled for commit: {commit_info.summary()}")
            return

        target_files: list[Modified[FileTuple] | Unchanged[FileTuple]]

        # sample a set of changed target files, bias toward files with more changes.
        BASE_WEIGHT = 10
        candidate_weights = [
            BASE_WEIGHT
            + math.sqrt(
                estimate_file_diff_tokens(change.before.code, change.after.code)
            )
            for change in candidate_files
        ]
        target_files = rng.choices(
            list(candidate_files), candidate_weights, k=len(candidate_files)
        )

        # Then we also randomly add some unchanged files to target_files. These
        # files will always produce negative examples and improve the diversity of
        # the negative examples.
        n_to_add = 0
        for _ in candidate_files:
            if rng.random() < self.random_target_file_rate:
                n_to_add += 1
        unchanged_files = list(repo_change.unchanged_files())
        rng.shuffle(unchanged_files)
        for file in unchanged_files[:n_to_add]:
            target_files.append(Unchanged(file))
        rng.shuffle(target_files)

        changed_final_files = pyr_map(
            {
                change.after.path: change.after.code
                for change in repo_change.changed_files
                if isinstance(change, Modified)
            }
        )

        # the set of (selected_code, replacement) pairs sampled so far
        changes_sampled = set[tuple[str, str]]()

        # pair target files with WIP repo states to sample EditGenProblems.
        if self.wip_sampling_strategy == "keep_most":
            N = len(wip_repo_changes) - 1
            for file_change in target_files:
                # only use a near-final repo state to ensure most changes are in context
                with collect_artifacts() as collector:
                    n_dropped = round(N * self.repo_change_wip_ratio * rng.random())
                    repo_change = wip_repo_changes[N - n_dropped]
                    info = {
                        "sample_problems": {
                            "total wip_repo_changes": len(wip_repo_changes),
                            "wip index": N - n_dropped,
                        }
                    }
                    post_artifact(info)
                    prob = self._sample_problem(
                        rng,
                        file_change,
                        repo_change,
                        commit_info,
                        pr_meta,
                        changed_final_files=changed_final_files,
                    )
                    prob.debug_info = collector.get_flattened_artifacts()
                prob_key = (prob.selected_code, prob.output.replacement)
                if prob_key not in changes_sampled:
                    changes_sampled.add(prob_key)
                    yield prob
        elif self.wip_sampling_strategy == "uniform":
            for file_change, repo_change in zip(target_files, wip_repo_changes):
                with collect_artifacts() as collector:
                    prob = self._sample_problem(
                        rng,
                        file_change,
                        repo_change,
                        commit_info,
                        pr_meta,
                        changed_final_files=changed_final_files,
                    )
                    prob.debug_info = collector.get_flattened_artifacts()
                prob_key = (prob.selected_code, prob.output.replacement)
                if prob_key not in changes_sampled:
                    changes_sampled.add(prob_key)
                    yield prob
        else:
            assert_never(self.wip_sampling_strategy)

    def sample_problems_with_explicit_wip(
        self,
        rng: Random,
        past_to_wip: RepoChange,
        wip_to_future: RepoChange,
        commit_info: CommitMeta,
        pr_meta: PRMeta | None,
        custom_instruction: str | None = None,
    ) -> Iterable[EditGenProblem]:
        """Sample edit generation problems from a given past-to-WIP and WIP-to-future
        repo changes.
        """
        candidate_files = {
            change
            for change in wip_to_future.changed_files
            if isinstance(change, Modified)
        }
        if not candidate_files:
            return

        target_files: list[Modified[FileTuple] | Unchanged[FileTuple]]

        # sample a set of changed target files, bias toward files with more changes.
        BASE_WEIGHT = 10
        candidate_weights = [
            BASE_WEIGHT
            + math.sqrt(
                estimate_file_diff_tokens(change.before.code, change.after.code)
            )
            for change in candidate_files
        ]
        target_files = rng.choices(
            list(candidate_files), candidate_weights, k=len(candidate_files)
        )

        # Then we also randomly add some unchanged files to target_files. These
        # files will always produce negative examples and improve the diversity of
        # the negative examples.
        n_to_add = 0
        for _ in candidate_files:
            if rng.random() < self.random_target_file_rate:
                n_to_add += 1
        unchanged_files = list(wip_to_future.unchanged_files())
        rng.shuffle(unchanged_files)
        for file in unchanged_files[:n_to_add]:
            target_files.append(Unchanged(file))
        rng.shuffle(target_files)

        # the set of (selected_code, replacement) pairs sampled so far
        changes_sampled = set[tuple[str, str]]()

        for file_change in target_files:
            prob = self._sample_problem_with_order(
                rng,
                file_change,
                past_to_wip,
                commit_meta=commit_info,
                pr_meta=pr_meta,
                custom_instruction=custom_instruction,
            )
            prob_key = (prob.selected_code, prob.output.replacement)
            if prob_key not in changes_sampled:
                changes_sampled.add(prob_key)
                yield prob

    # NOTE: this function doesn't take in an order directly but is instead a helper
    # function being called by sample_problems_with_order. sample_problems_with_order
    # uses the order to simulate (past_to_wip, wip_to_future) pairs and this function
    # only takes in a changed file from wip_to_future.
    def _sample_problem_with_order(
        self,
        rng: Random,
        target_file_change: Modified[FileTuple] | Unchanged[FileTuple],
        past_to_wip: RepoChange,
        commit_meta: CommitMeta,
        pr_meta: PRMeta | None,
        custom_instruction: str | None = None,
    ):
        """Sample an edit generation problem from the given file change.

        Note: if `target_file_change` is `Unchanged`, the result is guaranteed to be a
        negative example.

        Args:
            rng: The random number generator.
            target_file_change: The file change to sample from.
            past_to_wip: The past-to-WIP repo change.
            commit_info: The commit info to be used as the instruction.
            pr_meta: The PR metadata associated with the commit, if any.
        """
        before_path, before_code = target_file_change.before
        final_path, final_code = target_file_change.after
        # no need to simulate a WIP state assuming `target_file_change` already
        # contains the WIP simulation.
        lang = guess_language(final_path)
        wip_code = SimulatedWipCode(before_code, None, None).code

        # step 1: sample an edit region in the target file.
        edit_region_wip, edit_region_after = self._sample_edit_region(
            rng, before_code, wip_code, final_code, lang
        )

        # step 2: construct the problem accordingly.
        selected_code = wip_code[edit_region_wip.to_slice()]
        replacement = final_code[edit_region_after.to_slice()]
        output = EditGenOutput(
            replacement=replacement,
            changed=replacement != selected_code,
        )
        estimated_diff_tokens = estimate_file_diff_tokens(selected_code, replacement)
        info = {
            "estimated_diff_tokens": estimated_diff_tokens,
        }
        post_artifact(info)

        instruction = commit_meta.message
        if pr_meta is not None:
            instruction = pr_meta.title + "\n" + pr_meta.body
        if custom_instruction is not None:
            instruction = custom_instruction

        return EditGenProblem(
            current_path=final_path,
            prev_path=before_path,
            current_code=wip_code,
            final_code=final_code,
            edit_region=edit_region_wip,
            instruction=instruction,
            repo_change=past_to_wip,
            output=output,
            commit_meta=commit_meta,
            pr_meta=pr_meta,
        )

    def _is_candidate_file(self, file_change: Modified[FileTuple]) -> bool:
        before_code = file_change.before.code
        after_code = file_change.after.code
        return (
            len(before_code) <= self.max_file_chars
            and len(after_code) <= self.max_file_chars
            and all(
                len(line) <= self.max_line_width
                for line in before_code.splitlines(keepends=True)
            )
            and all(
                len(line) <= self.max_line_width
                for line in after_code.splitlines(keepends=True)
            )
        )

    def _sample_problem(
        self,
        rng: Random,
        target_file_change: Modified[FileTuple] | Unchanged[FileTuple],
        past_to_wip: RepoChange,
        commit_meta: CommitMeta,
        pr_meta: PRMeta | None,
        changed_final_files: PyrMap[Path, str],
    ) -> EditGenProblem:
        """Sample an edit generation problem from the given file change.

        Note: if `target_file_change` is `Unchanged`, the result is guaranteed to be a
        negative example.

        Args:
            rng: The random number generator.
            target_file_change: The file change to sample from.
            past_to_wip: The past-to-WIP repo change.
            commit_info: The commit info to be used as the instruction.
            pr_meta: The PR metadata associated with the commit, if any.
        """
        before_path, before_code = target_file_change.before
        final_path, final_code = target_file_change.after
        # step 1: sample a WIP state for the target file.
        # We use "uniform" strategy here since the target file that the user is working
        # on can be in either an early or late editing stage.
        lang = guess_language(final_path)
        wip_code = simulate_wip_code(
            rng, before_code, final_code, lang, wip_strategy="uniform"
        ).code

        # step 2: sample an edit region in the target file.
        edit_region_wip, edit_region_after = self._sample_edit_region(
            rng, before_code, wip_code, final_code, lang
        )

        # step 3: construct the problem accordingly.
        selected_code = wip_code[edit_region_wip.to_slice()]
        replacement = final_code[edit_region_after.to_slice()]
        output = EditGenOutput(
            replacement=replacement,
            changed=replacement != selected_code,
        )
        change_path = Modified(before_path, final_path)
        # override any existing changes to the target file with the newly sampled one
        if change_path in past_to_wip.changed_paths():
            past_to_wip = past_to_wip.drop_change(change_path)
        past_to_wip = past_to_wip.add_changed_file(
            Modified(
                FileTuple(before_path, before_code),
                FileTuple(final_path, wip_code),
            )
        )
        estimated_diff_tokens = estimate_file_diff_tokens(selected_code, replacement)
        info = {
            "_sample_problem": {
                "estimated_diff_tokens": estimated_diff_tokens,
            }
        }
        post_artifact(info)

        instruction = commit_meta.message
        if pr_meta is not None:
            instruction = pr_meta.title + "\n" + pr_meta.body

        return EditGenProblem(
            current_path=final_path,
            prev_path=before_path,
            current_code=wip_code,
            final_code=final_code,
            edit_region=edit_region_wip,
            instruction=instruction,
            repo_change=past_to_wip,
            output=output,
            commit_meta=commit_meta,
            pr_meta=pr_meta,
            changed_final_files=changed_final_files,
        )

    def _sample_edit_region(
        self,
        rng: Random,
        before_code: str,
        wip_code: str,
        new_code: str,
        lang: LanguageId | None,
    ) -> tuple[CharRange, CharRange]:
        """Sample an edit region in the WIP code.

        Based on `self.random_edit_region_rate`, we either sample an entirely random
        edit region, or otherwise sample an edit region that requires additional
        changes.

        We consider two types of edit regions:
        1. A free-form edit region that can start and end anywhere in the file. This
        simulates a user selection or a selection by the background mode.
        2. A chunk-based edit region that aligns with the chunk boundaries given by
        the SmartChunker.

        When the edit region partially overlaps with a modification span given by the
        diff, we conservatively teach the model to predict the entire span.after even
        if the edit region does not fully cover the span.before.

        Returns (edit_region_before, edit_region_after).
        """
        diff = precise_line_diff(wip_code, new_code)
        lmap_wip = LineMap(wip_code)
        candidates: list[LineRange]

        # first create candidates for the edit region
        if rng.random() < self.rechunker_rate:
            # use the smart chunker to get candidates for the edit region
            chunker = SmartChunker(max_chunk_chars=self.max_chunk_size, max_headers=0)
            chunks = chunker.split_chunks(wip_code, lang=lang)
            candidates = [chunk.lrange() for chunk in chunks]
            used_rechunker = True
        else:
            # randomly create candidates for the edit region
            candidates = []
            line_offset = 0
            while (size_left := lmap_wip.size_lines() - line_offset) > 0:
                region_size = lerp_round(
                    self.min_edit_region_lines,
                    self.max_edit_region_lines,
                    rng.random() ** 2,
                )
                region_size = min(region_size, size_left)
                candidate = LineRange(line_offset, line_offset + region_size)
                candidates.append(candidate)
                line_offset += region_size
            used_rechunker = False

        debug_info = {
            "no_change_in_input": wip_code == new_code,
            "used_rechunker": used_rechunker,
            "num_candidates": len(candidates),
        }
        if not candidates:
            candidates = [LineRange(0, lmap_wip.size_lines())]

        if rng.random() < self.random_edit_region_rate:
            candidate = rng.choice(candidates)
            crange_wip = lmap_wip.lrange_to_crange(candidate)
            crange_after = diff.before_range_to_after(crange_wip)
            randomly_picked = True
        else:
            # evaluate each candidate and then perform weighted sampling
            import_lines = set(find_import_change_lines(diff, lang)) if lang else set()
            candidate_weights: list[float] = []
            candidate_cranges: list[tuple[CharRange, CharRange]] = []
            candidate_imports: list[int] = []
            for candidate in candidates:
                crange_wip = lmap_wip.lrange_to_crange(candidate)
                crange_after = diff.before_range_to_after(crange_wip)
                edited_code_wip = wip_code[crange_wip.to_slice()]
                edited_code_after = new_code[crange_after.to_slice()]
                editing_size = estimate_file_diff_tokens(
                    edited_code_wip, edited_code_after
                )
                num_imports = 0
                if edited_code_wip.strip() == edited_code_after.strip():
                    # do not sample no-op edits
                    weight = 0.0
                else:
                    # we want to put more weight on larger edits
                    weight = math.sqrt(1 + min(self.max_diff_tokens, editing_size))
                    for line in candidate:
                        if line in import_lines:
                            num_imports += 1
                    if num_imports > 0:
                        # we try to avoid editing imports in general by always
                        # downweighting such targets
                        weight /= 10
                        if before_code.strip() == wip_code.strip():
                            # However, if we haven't made any changes so far, then we
                            # make it even more unlikely to pick imports as the target
                            weight /= 10
                candidate_weights.append(weight)
                candidate_cranges.append((crange_wip, crange_after))
                candidate_imports.append(num_imports)

            if sum(candidate_weights) == 0:
                range_i = rng.choice(range(len(candidates)))
                randomly_picked = True
            else:
                range_i = rng.choices(range(len(candidates)), candidate_weights)[0]
                randomly_picked = False

            debug_info["num_imports"] = candidate_imports[range_i]
            crange_wip, crange_after = candidate_cranges[range_i]

        debug_info["randomly_picked"] = randomly_picked
        post_artifact({"_sample_edit_region": debug_info})
        return crange_wip, crange_after


@lru_cache(maxsize=512)
def estimate_file_diff_tokens(before: str, after: str):
    """Estimate the nubmer of tokens needed to represent the diff."""
    diff = precise_line_diff(before, after)
    count = 0
    for span in diff.spans:
        if isinstance(span, NoopSpan):
            continue
        count += _estimate_span_diff_tokens(span.before, span.after)

    return count


def _estimate_span_diff_tokens(before: str, after: str):
    """Estimate the nubmer of tokens needed to represent the diff span."""
    before_lines = before.splitlines(keepends=True)
    after_lines = after.splitlines(keepends=True)
    # for each line, we need roughly 3 tokens to represent the line markers
    line_tokens = 3 * (len(before_lines) + len(after_lines))
    # for every 3 added non-whitepace chars, we need roughly 1 token
    char_tokens = len(re.sub(r"\s+", "", after)) // 3
    return line_tokens + char_tokens

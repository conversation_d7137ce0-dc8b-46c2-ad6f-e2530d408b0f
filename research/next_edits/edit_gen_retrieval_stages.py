"""Data pipeline stages for the retriever for next edit generation.

This is mostly a perplexity distillation pipeline.
"""

import dataclasses
import functools
import logging
from dataclasses import dataclass, field
from pathlib import Path
from random import Random
from typing import Any, Iterable, TypedDict

from dataclasses_json import DataClassJsonMixin

from base.diff_utils.diff_utils import File
from base.languages.language_guesser import guess_language
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.prompt_format_next_edit.gen_prompt_formatter import (
    EditGenFormatterConfig,
    EditGenPromptFormatter,
    EditGenPromptInput,
)
from base.prompt_format_next_edit.retrieval_prompt_formatter import (
    EditGenRetrievalPromptInput,
    EditGenRetrievalQueryFormatterConfig,
    EditGenRetrievalQueryPromptFormatter,
)
from base.prompt_format_retrieve.ethanol_embedding_prompt_formatter import (
    Ethanol6DocumentFormatter,
)
from base.prompt_format_retrieve.prompt_formatter import DocumentRetrieverPromptInput
from base.tokenizers import create_tokenizer_by_name
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from base.tokenizers.tokenizer import RetrievalSpecialTokens
from research.core.types import Chunk, Scored
from research.models.language_model import LanguageModel, log_likelihood_continuation
from research.next_edits.edit_gen_formatters import equal_modulo_spaces
from research.next_edits.edit_gen_sampler import EditGenOutput
from research.next_edits.edit_gen_stages import (
    RagEditGenProblem,
    fuzz_edit_gen_prompt_input,
)
from research.utils.token_array_utils import TokenArray, to_token_array

logger = logging.getLogger(__name__)


class RetrieverConfigDict(TypedDict):
    """The config dict expected by the `create_retriever` function"""

    scorer: dict
    chunker: dict
    query_formatter: dict
    document_formatter: dict


@dataclass
class ScoreChunksConfig(DataClassJsonMixin):
    """Configuration for reordering chunks."""

    formatter_config: EditGenFormatterConfig
    """The config parameters for the EditGenPromptFormatter."""

    checkpoint_path: str
    """Path the the edit model checkpoint."""

    downsample_retrieval_rate: float = 0.0
    """The rate at which to downsample the retrieval chunks."""

    drop_instruction_rate: float = 0.0
    """The rate at which to drop the instruction."""

    downsample_no_change_rate: float = 0.0
    """The rate at which to downsample problems where nothing has been changed."""

    max_scored_chunks: int = 128
    """The max number of chunks to score."""

    max_target_tokens: int = 128
    """The max number of tokens to score in the target.

    A too large value here will slow the pipeline down.
    """

    VERSION = "1.0"
    """The version of the config.

    Should be updated after each incompatible change to the reordering stage.
    """


ScoredChunks = list[Scored[Chunk]]
"""Represents a list of scored chunks."""


@dataclass
class EditGenRetrievalProblem(DataClassJsonMixin):
    """A problem with a list of retrieved chunks."""

    problem: RagEditGenProblem
    """The original problem."""

    perplexity_scored_chunks: ScoredChunks
    """The chunks in `input` with perplexity scores."""

    perplexity_baseline: float
    """The score of the empty chunk, used as a baseline for the scored chunks."""

    diff_context_lines_when_scoring: int = -1
    """The number of diff context lines used when scoring the chunks.

    -1 means this value is not set.
    """

    debug_info: dict[str, Any] = field(default_factory=dict)
    """Extra info recording how the problem was created."""


def score_chunks(
    config: ScoreChunksConfig,
    problems: Iterable[RagEditGenProblem],
    seed: int,
) -> Iterable[EditGenRetrievalProblem]:
    """Rescore the chunks in each problem.

    Args:
        config: The config.
        problems: The problems to score.
        seed: The random seed for this batch. This isn't currently used, but may be
            relevant in the future.

    This function is CPU-bound.
    """
    rng = Random(seed)
    # 1. Load generation model from config.
    model = create_model(config.checkpoint_path)
    prompt_formatter = EditGenPromptFormatter(
        tokenizer=StarCoder2Tokenizer(),
        config=config.formatter_config,
    )

    for problem in problems:
        prompt_input: EditGenPromptInput = problem.input
        output = problem.output
        debug_info = {}

        if equal_modulo_spaces(prompt_input.selected_code, output.replacement):
            # treat whitespace-only changes as negative examples
            output = EditGenOutput(prompt_input.selected_code, changed=False)

        # Downsample problems where nothing has been changed -- we won't get useful
        # signal from scoring just the "no change" token.
        if not output.changed and rng.random() < config.downsample_no_change_rate:
            continue

        # NOTE(arun): we switched from `Chunk` to `PromptChunk` in our pipelines, but
        # some older data still uses `Chunk` and we need to convert.
        prompt_input = dataclasses.replace(
            prompt_input,
            retrieval_chunks=[
                chunk.to_prompt_chunk() if isinstance(chunk, Chunk) else chunk
                for chunk in prompt_input.retrieval_chunks
            ],
        )

        # treat whitespace-only changes as negative examples
        prompt_input = fuzz_edit_gen_prompt_input(
            prompt_input,
            rng,
            config.downsample_retrieval_rate,
            config.drop_instruction_rate,
            debug_info=debug_info,
        )

        if config.formatter_config.diff_context_lines > 5:
            # randomly vary diff context lines to increase data diversity
            diff_context_lines = rng.randint(
                5, config.formatter_config.diff_context_lines
            )
            prompt_formatter.config.diff_context_lines = diff_context_lines
        else:
            diff_context_lines = config.formatter_config.diff_context_lines
        debug_info["diff_context_lines"] = diff_context_lines

        input_tokens = prompt_formatter.format_input_prompt(
            dataclasses.replace(prompt_input, retrieval_chunks=[])
        ).tokens
        lang = guess_language(prompt_input.current_file.path)
        target_tokens = prompt_formatter.format_output_prompt(
            prompt_input.selected_code,
            output,
            lang=lang,
        )
        target_tokens = target_tokens[: config.max_target_tokens]

        baseline_score = log_likelihood_continuation(
            model,
            input_tokens,
            target_tokens,
        )

        scored_chunks = []
        for chunk in prompt_input.retrieval_chunks[: config.max_scored_chunks]:
            input_tokens = prompt_formatter.format_input_prompt(
                dataclasses.replace(prompt_input, retrieval_chunks=[chunk])
            ).tokens
            score = log_likelihood_continuation(
                model,
                input_tokens,
                target_tokens,
            )
            scored_chunks.append(Scored(chunk, score))

        # Reorder the chunks by score
        scored_chunks.sort(key=lambda x: x.score, reverse=True)

        yield EditGenRetrievalProblem(
            problem=dataclasses.replace(problem, input=prompt_input),
            perplexity_scored_chunks=scored_chunks,
            perplexity_baseline=baseline_score,
            diff_context_lines_when_scoring=diff_context_lines,
            debug_info=debug_info,
        )


@functools.cache
def create_model(
    checkpoint_path: Path | str,
    checkpoint_sha256: str = "5b49ba95d6d1350cc46dbc6ef90a5d06aa7f8f9d9afb4c84d6bac2e97a66307e",
) -> LanguageModel:
    from research.models.language_models.fastforward import (
        create_from_starcoder2_checkpoint,
    )

    return create_from_starcoder2_checkpoint(checkpoint_path, checkpoint_sha256)


@dataclass
class FormatAsTokensConfig:
    """Configuration for formatting edit localization problems as token sequences."""

    query_formatter_config: EditGenRetrievalQueryFormatterConfig
    """The config dict for the query formatter."""

    document_formatter_config: dict
    """The config dict for the document formatter.

    TODO(arun): Unfortunately, there are no good options for creating query formatters.
    For now, this configuration dict corresponds to the relevant options in
    Ethanol6DocumentFormatter:
        - max_content_length (int)
        - add_path (bool)
    """

    tokenizer_name: str = "starcoder"
    """The name of the base tokenizer to use."""

    VERSION = "1.0"
    """The version of the prompt config.

    Should be updated after each incompatible change to the formatting stage.

    Changelog:
        - 1.0: First version of the config.
    """


def format_as_tokens(
    config: FormatAsTokensConfig,
    problems: Iterable[EditGenRetrievalProblem],
    seed: int,
) -> Iterable[TokenArray]:
    """Format edit localization retrieval problems into token sequences.

    This stage also optionally performs:
        - dropping the instruction (if there is at least one recent change)
        - turning whitespace-only changes into negative examples

    This function is CPU-bound.
    """
    del seed

    tokenizer = create_tokenizer_by_name(config.tokenizer_name)
    assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)

    query_formatter = EditGenRetrievalQueryPromptFormatter(
        tokenizer,
        config=config.query_formatter_config,
    )
    document_formatter = Ethanol6DocumentFormatter(
        apportionment_config=TokenApportionmentConfig(
            max_content_len=config.document_formatter_config.get(
                "max_content_length", 1024
            ),
            input_fraction=0,
            prefix_fraction=0,
            max_path_tokens=0,
        ),
        tokenizer=tokenizer,
        add_path=config.document_formatter_config.get("add_path", True),
    )

    for problem in problems:
        # At this point in the pipeline, we've already fuzzed the input, so we'll use
        # the original input as is.
        prompt_input = EditGenRetrievalPromptInput(
            current_file=problem.problem.input.current_file,
            edit_region=problem.problem.input.edit_region,
            instruction=problem.problem.input.instruction,
            recent_changes=problem.problem.input.recent_changes,
        )
        if problem.diff_context_lines_when_scoring == -1:
            # NOTE(arun): for backwards compatibility, we used to represent the number
            # of diff context lines we used sneakily in debug info.
            problem.diff_context_lines_when_scoring = problem.debug_info.get(
                "diff_context_lines", 5
            )
        query_formatter.config.diff_context_lines = (
            problem.diff_context_lines_when_scoring
        )

        try:
            tokens = query_formatter.format_prompt(prompt_input).tokens()
        except Exception:
            logger.exception("Failed: couldn't format query.")
            continue
        if not tokens:
            logger.warning("Failed: empty query.")
            continue
        if not problem.perplexity_scored_chunks:
            logger.warning("Failed: empty scored chunks.")
            continue

        # Save chunks with scores
        for scored_chunk in sorted(
            problem.perplexity_scored_chunks,
            key=lambda x: x.score,
            reverse=True,
        ):
            tokens += document_formatter.format_prompt(
                DocumentRetrieverPromptInput(
                    text=scored_chunk.item.text,
                    path=str(scored_chunk.item.path),
                ),
            ).tokens()
            # Add the score. Due to limitations in `IndexedDataset`, we tokenize the
            # score as a string.
            tokens += [
                *tokenizer.tokenize_safe(str(scored_chunk.score)),
                tokenizer.special_tokens.end_of_key,
            ]

        yield to_token_array(tokens, tokenizer.vocab_size)

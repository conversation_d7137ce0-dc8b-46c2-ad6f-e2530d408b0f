"""Utilities to convert the ground truth data extracted from Hindsight into a format
suitable for eval and training."""

from collections import Counter
from collections.abc import Iterable
from random import Random
from typing import Sequence

from base.blob_names.python.blob_names import FilePath
from base.datasets.hindsight_next_edit import NextEditIntermediateType
from base.datasets.hindsight_next_edit_intermediate_dataset import (
    GCSCaches,
    GroundTruthEdit,
)
from base.diff_utils.edit_events import SquashableEdits, grouped_events_to_file_changes
from base.diff_utils.str_diff import DeletedSpan, NoopSpan, precise_line_diff
from base.logging.secret_logging import Secret<PERSON>ogger, IgnoreAllLogger
from base.next_edit_filter.rule_based_filters import (
    is_exact_undo_recent_changes,
    is_suggestion_touching_recent_changes,
)
from base.prompt_format_next_edit.gen_prompt_formatter import (
    EditGenOutput,
    equal_modulo_spaces,
)
from base.ranges.line_map import LineMap, LineRange
from base.retrieval.chunking.line_based_chunking import LineChunkContents
from base.retrieval.chunking.smart_chunking import <PERSON>Chunker
from research.next_edits.edit_gen_stages import is_deleting_imports
from research.next_edits.next_edits_dataset import (
    HindsightEditGenProblem,
    HindsightOrigin,
)
from research.utils.sampling_utils import downsample_to


def convert_to_edit_classification_problem(
    rng: Random,
    chunker: SmartChunker,
    inter_data: NextEditIntermediateType,
    ground_truth: GroundTruthEdit,
    gcp_caches: GCSCaches,
) -> Iterable[HindsightEditGenProblem]:
    """Convert into positive and negative examples that are smart chunk aligned."""
    blob_cache = gcp_caches.blob_cache
    request = inter_data.request
    files_for_events = inter_data.files_for_events
    path_to_content = {
        str(pc.path): pc.content
        for pc in blob_cache.get(request.blob_names)
        if pc is not None
    }
    # override this mapping with the more up-to-date files_for_events
    path_to_content |= {file.path: file.contents for file in files_for_events.values()}
    squashable_edits = SquashableEdits(
        edit_events=request.edit_events,
        path_to_current_content=path_to_content,
    )
    gt_before_crange = ground_truth.before_crange
    gt_file_text = files_for_events[ground_truth.path].contents
    gt_file_new_text = (
        gt_file_text[: gt_before_crange.start]
        + ground_truth.after_text
        + gt_file_text[gt_before_crange.stop :]
    )
    gt_diff = precise_line_diff(gt_file_text, gt_file_new_text)
    positive_chunks = list[tuple[FilePath, LineChunkContents]]()
    negative_chunks = list[tuple[FilePath, LineChunkContents]]()
    for chunk in chunker.split_chunks(gt_file_text, lang=None):
        if chunk.crange().touches(gt_before_crange):
            positive_chunks.append((ground_truth.path, chunk))
        else:
            negative_chunks.append((request.path, chunk))
    if request.path != ground_truth.path:
        # also use the active file's chunks as negatives
        active_file_text = path_to_content[request.path]
        for chunk in chunker.split_chunks(active_file_text, lang=None):
            negative_chunks.append((request.path, chunk))
    # pick at most one positive chunk and one negative chunk
    picked_chunks = list[tuple[FilePath, LineChunkContents]]()
    if positive_chunks:
        picked_chunks.append(rng.choice(positive_chunks))
    if negative_chunks:
        picked_chunks.append(rng.choice(negative_chunks))

    for path, chunk in picked_chunks:
        edit_region = chunk.crange()
        if path == ground_truth.path:
            edit_after_region = gt_diff.before_range_to_after(edit_region)
            selected_text = gt_file_text[edit_region.to_slice()]
            replacement_text = gt_file_new_text[edit_after_region.to_slice()]
            edit_gen_output = EditGenOutput(
                replacement=replacement_text, changed=replacement_text != selected_text
            )
        else:
            file_text = path_to_content[path]
            edit_gen_output = EditGenOutput(
                replacement=file_text[edit_region.to_slice()],
                changed=False,
            )

        yield HindsightEditGenProblem(
            current_path=path,
            current_files=path_to_content,
            squashable_edits=squashable_edits,
            edit_region=edit_region,
            ground_truth=edit_gen_output,
            origin=HindsightOrigin(
                request_id=request.request_id,
                session_id=inter_data.session_id,
                tenant=gcp_caches.tenant.name,
                request_time=request.timestamp,
            ),
        )


def convert_to_positive_edit_problem(
    inter_data: NextEditIntermediateType,
    ground_truth: GroundTruthEdit,
    gcp_caches: GCSCaches,
    edit_region_margin_lines: int = 3,
) -> HindsightEditGenProblem:
    """Convert into positive examples where the edit region just covers the ground truth."""
    blob_cache = gcp_caches.blob_cache
    request = inter_data.request
    files_for_events = inter_data.files_for_events
    path_to_content = {
        str(pc.path): pc.content
        for pc in blob_cache.get(request.blob_names)
        if pc is not None
    }
    # override this mapping with the more up-to-date files_for_events
    path_to_content |= {file.path: file.contents for file in files_for_events.values()}
    squashable_edits = SquashableEdits(
        edit_events=request.edit_events,
        path_to_current_content=path_to_content,
    )
    gt_before_crange = ground_truth.before_crange
    gt_file_text = files_for_events[ground_truth.path].contents
    gt_file_new_text = (
        gt_file_text[: gt_before_crange.start]
        + ground_truth.after_text
        + gt_file_text[gt_before_crange.stop :]
    )
    gt_diff = precise_line_diff(gt_file_text, gt_file_new_text)

    gt_lmap = LineMap(gt_file_text)
    gt_before_lrange = ground_truth.before_lrange
    # expand the ground truth region by 3 lines on each side
    edit_lrange = LineRange(
        max(0, gt_before_lrange.start - edit_region_margin_lines),
        min(gt_lmap.size_lines(), gt_before_lrange.stop + edit_region_margin_lines),
    )
    edit_region = gt_lmap.lrange_to_crange(edit_lrange)
    edit_after_region = gt_diff.before_range_to_after(edit_region)
    selected_text = gt_file_text[edit_region.to_slice()]
    replacement_text = gt_file_new_text[edit_after_region.to_slice()]
    edit_gen_output = EditGenOutput(
        replacement=replacement_text, changed=replacement_text != selected_text
    )

    return HindsightEditGenProblem(
        current_path=ground_truth.path,
        current_files=path_to_content,
        squashable_edits=squashable_edits,
        edit_region=edit_region,
        ground_truth=edit_gen_output,
        origin=HindsightOrigin(
            request_id=request.request_id,
            session_id=inter_data.session_id,
            tenant=gcp_caches.tenant.name,
            request_time=request.timestamp,
        ),
    )


def is_large_edit_problem(
    problem: HindsightEditGenProblem, max_changed_lines: int
) -> bool:
    if not problem.ground_truth.changed:
        return False
    current_file = problem.current_files[problem.current_path]
    start, stop = problem.edit_region.to_tuple()
    new_file = (
        current_file[:start] + problem.ground_truth.replacement + current_file[stop:]
    )
    diff = precise_line_diff(current_file, new_file)
    n_changed_lines = 0.0
    for span in diff.spans:
        if isinstance(span, NoopSpan):
            continue
        if isinstance(span, DeletedSpan):
            n_changed_lines += len(span.deleted.splitlines()) / 2
        else:
            n_changed_lines += len(span.after.splitlines())
    return n_changed_lines >= max_changed_lines


def n_changed_size_lines(problem: HindsightEditGenProblem) -> int:
    if not problem.ground_truth.changed:
        return 0
    old_size = sum(1 for line in problem.selected_code.splitlines() if line.strip())
    new_size = sum(
        1 for line in problem.ground_truth.replacement.splitlines() if line.strip()
    )
    return new_size - old_size


def deduplicate_problems(
    problems: Iterable[HindsightEditGenProblem],
) -> list[HindsightEditGenProblem]:
    """Deduplicate problems by diff."""
    prob_signatures = set[tuple]()
    results = list[HindsightEditGenProblem]()
    duplicated_prob_paths = Counter[FilePath]()
    for prob in problems:
        # we turn the last edit event into position-insensitive signature
        events_sig = tuple(
            tuple((e.before_text, e.after_text) for e in event.edits)
            for event in prob.squashable_edits.edit_events[-1:]
        )
        # we use the following signature to deduplicate problems
        sig = (
            prob.current_path,
            prob.selected_code,
            prob.ground_truth.replacement,
            events_sig,
        )
        if sig in prob_signatures:
            duplicated_prob_paths[prob.current_path] += 1
            continue
        prob_signatures.add(sig)
        results.append(prob)

    print("Top 5 duplicated problem paths:", duplicated_prob_paths.most_common(5))
    return results


def remove_whitespace_examples(
    problems: Iterable[HindsightEditGenProblem],
) -> Iterable[HindsightEditGenProblem]:
    """Remove examples where the change is whitespace-only."""
    for prob in problems:
        if equal_modulo_spaces(
            prob.selected_code,
            prob.ground_truth.replacement,
        ):
            continue
        yield prob


def remove_import_deletion_examples(
    problems: Iterable[HindsightEditGenProblem],
) -> Iterable[HindsightEditGenProblem]:
    """Remove examples where the change deletes imports."""
    for prob in problems:
        new_code = prob.prefix + prob.ground_truth.replacement + prob.suffix
        if is_deleting_imports(
            prob.current_path,
            prob.current_code,
            new_code,
        ):
            continue
        yield prob


def is_touching_the_last_edits(
    problem: HindsightEditGenProblem,
    n_last_edits: int,
    safe_logger: SecretLogger | None = None,
) -> bool:
    """Check if the label change touches the range of the very last edit.

    As a special case (for efficiency), this returns True if there are no recent
    edits.
    """
    if not problem.ground_truth.changed:
        return False
    if safe_logger is None:
        safe_logger = IgnoreAllLogger()
    squashable_edits = problem.squashable_edits
    current_path = problem.current_path
    if not squashable_edits.edit_events:
        return False
    file_changes = squashable_edits.convert_edit_events_to_modified_files(
        safe_logger=safe_logger,
        group_sizes=[n_last_edits],
    )
    if not file_changes:
        # this drops problems with an empty diff history
        return True
    # we only check edits in the current file
    file_changes = [
        change for change in file_changes if change.get_later().path == current_path
    ]
    if not file_changes:
        return False  # no recent edits to check
    file_change = file_changes[-1]
    new_code = problem.prefix + problem.output.replacement + problem.suffix
    return is_suggestion_touching_recent_changes(
        current_text=file_change.after.contents,
        previous_text=file_change.before.contents,
        suggested_text=new_code,
    )


def is_undo_recent_change_example(
    problem: HindsightEditGenProblem,
    undo_filter_events: int = 5,
    safe_logger: SecretLogger | None = None,
) -> bool:
    """Check if the problem is an example that undoes a recent change."""
    if not problem.ground_truth.changed:
        return False
    if safe_logger is None:
        safe_logger = IgnoreAllLogger()
    squashable_edits = problem.squashable_edits
    current_path = problem.current_path
    same_file_events = [
        event for event in squashable_edits.edit_events if event.path == current_path
    ]
    for k in range(1, 1 + min(undo_filter_events, len(same_file_events))):
        last_k_edits = same_file_events[-k:]
        file_changes = grouped_events_to_file_changes(
            [last_k_edits], squashable_edits.path_to_current_content, safe_logger
        ).changes
        if is_exact_undo_recent_changes(
            recent_changes=file_changes,
            selection_range=problem.edit_region,
            existing_code=problem.selected_code,
            suggested_code=problem.ground_truth.replacement,
            current_path=current_path,
        ):
            return True
    return False


def rebalance_positive_negative_examples(
    rng: Random,
    problems: Sequence[HindsightEditGenProblem],
    remove_whitespace_only: bool = True,
):
    """Downsample to have the desired positive example ratio."""
    positive_examples = list[HindsightEditGenProblem]()
    negative_examples = list[HindsightEditGenProblem]()

    for prob in problems:
        if prob.selected_code != prob.ground_truth.replacement:
            if equal_modulo_spaces(
                prob.selected_code,
                prob.ground_truth.replacement,
            ):
                # whitespace-only change
                if not remove_whitespace_only:
                    negative_examples.append(prob)
            else:
                # non-whitespace change
                positive_examples.append(prob)
        else:
            # no change at all
            negative_examples.append(prob)

    # Calculate target numbers to achieve 50/50 ratio
    target_count = min(len(positive_examples), len(negative_examples))

    # Downsample the larger group
    if len(positive_examples) > target_count:
        print(
            f"Throwing away {len(positive_examples) - target_count} positive examples."
        )
        positive_examples = downsample_to(rng, positive_examples, target_count)
    elif len(negative_examples) > target_count:
        print(
            f"Throwing away {len(negative_examples) - target_count} negative examples."
        )
        negative_examples = downsample_to(rng, negative_examples, target_count)

    # Combine and shuffle
    balanced_problems = positive_examples + negative_examples

    return balanced_problems


def measure_last_edit_location_acc_on_problems(
    problems: Sequence[HindsightEditGenProblem],
):
    pos_contain_last_edit = list[bool]()
    neg_not_contain_last_edit = list[bool]()
    for prob in problems:
        if not prob.squashable_edits.edit_events:
            continue
        last_edit = prob.squashable_edits.edit_events[-1]
        last_edit_touches_edit_region = (
            last_edit.path == prob.current_path
            and prob.edit_region.touches(last_edit.edits[0].after_crange())
        )
        if prob.ground_truth.changed:
            pos_contain_last_edit.append(last_edit_touches_edit_region)
        else:
            neg_not_contain_last_edit.append(not last_edit_touches_edit_region)
    return {
        "pos_contain_last_edit": counted_percentage(pos_contain_last_edit),
        "neg_not_contain_last_edit": counted_percentage(neg_not_contain_last_edit),
    }


def measure_last_edit_location_acc_on_ground_truths(
    chunker: SmartChunker,
    examples: Sequence[tuple[NextEditIntermediateType, GroundTruthEdit]],
):
    edit_location_correct = list[bool]()
    gt_in_current_file = list[bool]()
    gt_in_last_edited_file = list[bool]()
    for datum, ground_truth in examples:
        if not datum.request.edit_events:
            continue
        gt_in_current_file.append(datum.request.path == ground_truth.path)
        gt_file = datum.files_for_events[ground_truth.path]
        file_chunks = chunker.split_chunks(gt_file.contents, None)
        last_edit = datum.request.edit_events[-1]
        gt_in_last_edited_file.append(last_edit.path == ground_truth.path)
        if last_edit.path != ground_truth.path:
            edit_location_correct.append(False)
            continue
        touched_chunks = [
            chunk
            for chunk in file_chunks
            if chunk.crange().touches(last_edit.edits[0].after_crange())
        ]
        if len(touched_chunks) != 1:
            edit_location_correct.append(False)
            continue
        last_touched_chunk = touched_chunks[-1]
        edit_location_correct.append(
            last_touched_chunk.crange().touches(ground_truth.before_crange)
        )
    return {
        "gt_in_current_file": counted_percentage(gt_in_current_file),
        "edit_location_correct": counted_percentage(edit_location_correct),
        "gt_in_last_edited_file": counted_percentage(gt_in_last_edited_file),
    }


def counted_percentage(values: Sequence[bool], digits: int = 1) -> str:
    """Return the average of a list of values, with the number of values in parentheses."""
    import numpy as np

    if not values:
        return "NaN (0)"
    return f"{np.mean(values):.{digits}%} ({len(values)})"

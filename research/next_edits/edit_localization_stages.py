"""Data pipeline stages for next edit localization.

We use a 3-stage pipeline to generate the training data:

1. Sampling stage: this stage walks through a list of commits/PRs from a repo and
samples edit localization problems from each commit/PR. This stage is CPU and IO-bound.
2. Retrieval stage: this stage performs dense retrieval on the sampled problems to find
negative chunks. This stage is GPU-bound.
3. Formatting stage: this stage formats the result from the previous stage into
prompt tokens expected by the model. This stage is CPU-bound.
    3a. Merge with instructions: this stage lets you join with instructions that were
    generated by a model in a separate stage.
"""

import dataclasses
import functools
import itertools
import json
import logging
import math
import sys
from collections.abc import Sequence
from dataclasses import dataclass, field
from functools import cached_property
from itertools import islice
from pathlib import Path
from random import Random
from typing import Any, Iterable, Literal, TypedDict, TypeVar

from dataclasses_json import DataClassJsonMixin
from pyrsistent import pvector as pyr_vector

from base.diff_utils.diff_utils import File
from base.languages.language_guesser import guess_language
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.prompt_format_next_edit.common import NextEditPromptInput
from base.prompt_format_next_edit.gen_prompt_formatter import (
    EditGenFormatterConfig,
    EditGenPromptFormatter,
    EditGenPromptInput,
)
from base.prompt_format_next_edit.location_prompt_formatter import (
    LocalizationNextEditPromptInput,
    NextEditLocationQueryFormatter,
)
from base.prompt_format_retrieve.ethanol_embedding_prompt_formatter import (
    Ethanol6DocumentFormatter,
)
from base.prompt_format_retrieve.prompt_formatter import DocumentRetrieverPromptInput
from base.ranges.range_types import CharRange
from base.static_analysis.usage_analysis import path_distance
from base.tokenizers import create_tokenizer_by_name
from base.tokenizers.tokenizer import NextEditGenSpecialTokens, RetrievalSpecialTokens
from research.core.changes import get_after
from research.core.types import Chunk, Document
from research.models.language_model import LanguageModel, log_likelihood_continuation
from research.next_edits.common_stages import (
    create_retriever,
    get_chunk_cache,
    load_instructions,
    repo_timer,
)
from research.next_edits.next_edits_dataset import CommitMeta, PRMeta
from research.next_edits.wip_repo_sampler import WipRepoSampler, WipSamplingStrategy
from research.retrieval import chunking_functions
from research.retrieval.scorers.recency_scorer import (
    get_diff_line_ranges,
    label_overlapping_chunks,
)
from research.retrieval.types import DocumentIndex
from research.utils import repo_change_utils
from research.utils.repo_change_utils import (
    FileTuple,
    RepoChange,
    patchset_from_repo_change,
)
from research.utils.token_array_utils import TokenArray, to_token_array

logger = logging.getLogger(__name__)


class RetrieverConfigDict(TypedDict):
    """The config dict expected by the `create_retriever` function"""

    scorer: dict
    chunker: dict
    query_formatter: dict
    document_formatter: dict


@dataclass
class RepoChangeToProblemConfig(DataClassJsonMixin):
    """Configuration for WIP repo sampling."""

    max_problems_per_repo: int = 1_000
    """Max number of problems to sample from each repo."""

    max_problems_per_commit: int = 10
    """Max number of problems to sample from each commit / PR."""

    max_repo_size_files: int = 2_000
    """Repos with more than this many after_files will be filtered out.

    This helps limit the time taken for the retrieval stage.
    """

    wip_sampling_strategy: WipSamplingStrategy = "keep_most"
    """The strategy that decides how to partially drop changes."""

    timeout_per_repo_s: float | None = 5 * 60
    """Timeout (in seconds) for processing each repo.

    Partial results will be returned if timeout is hit.
    """

    remove_unsupported_languages: bool = False
    """If set, filter out files that are not in the selected languages."""

    empty_change_chance: float = 0.0
    """Probability of sampling no change in the past_to_wip diff."""

    # TODO(arun): option to filter or score future changes.

    VERSION = "1.4"
    """The version of the sampling config.

    Should be updated after each incompatible change to the sampling stage.

    Changelog:
        - 1.0: First version -- corresponds to version 1.6 of the edit gen sampler.
        - 1.1: Catch diff parsing and path exceptions.
        - 1.2: Updated languages list.
        - 1.3: Fix a line ending bug in the PR dataset.
        - 1.4: Non-backwards-compatible change to make past_to_future_repo_change computed instead of wip_to_future_repo_change. Also adds custom
        instructions field.
    """


@dataclass
class EditLocalizationProblem:
    """Describes an edit localization problem.

    Given the full_repo_change and wip_repo_change, one can calculate the past repo
    state (full_repo_change.before_files), the wip state (wip_repo_change.after_files),
    and the future state (full_repo_change.after_files). The past_to_wip diff gives us
    the input to the localization problem, while wip_to_future diff gives us the target
    chunks.

    NOTE(arun): This data structure is pickled as intermediate stage output. If you are
    changing or renaming any fields, please re-run the pipeline.
    """

    pr_meta: PRMeta
    """Metadata about the PR this problem is sampled from."""

    commit_meta: CommitMeta
    """Metadata about the commit this problem is sampled from (future state, not WIP state)."""

    wip_to_future_repo_change: RepoChange
    """The full repo change that this problem is sampled from."""

    past_to_wip_repo_change: RepoChange
    """The repo change that is the current WIP state."""

    instructions: str = ""
    """Custom instructions for the problem. This will overwrite using the commit message as
    the instruction.
    """

    command_output: str | None = None
    """The output from the command that generated the change."""

    @cached_property
    def past_to_future_repo_change(self) -> RepoChange:
        """The repo change that is the future state."""
        return self.past_to_wip_repo_change.squash(self.wip_to_future_repo_change)


def create_wip_to_future_repo_change(
    past_to_future_repo_change: RepoChange,
    past_to_wip_repo_change: RepoChange,
) -> RepoChange:
    wip_to_future_diff = repo_change_utils.compute_repo_diff(
        past_to_wip_repo_change.after_repo(),
        past_to_future_repo_change.after_repo(),
    )
    return repo_change_utils.repo_change_from_patchset(
        past_to_wip_repo_change.after_repo(), wip_to_future_diff
    )


def repo_change_to_problems(
    repo_change: RepoChange,
    pr_meta: PRMeta,
    commit_meta: CommitMeta,
    config: RepoChangeToProblemConfig,
    seed: int,
) -> Iterable[EditLocalizationProblem]:
    """Sample edit localization problems from a given repository change.

    This function is CPU-bound and IO-bound.

    Args:
        repo_change: The repo change to sample problems from.
        pr_meta: Metadata about the PR this problem is sampled from.
        commit_meta: Metadata about the commit this problem is sampled from.
        config: The sampling config.
        seed: The random seed.
        deadline_s: If set the deadline in seconds to generate problems. We track how
            long we've been waiting for any given repo to cap the time taken.
    """
    if config.remove_unsupported_languages:
        repo_change = repo_change.filter_by_path(
            lambda path: guess_language(str(path)) is None
        )

    if (
        len(repo_change.before_files) > config.max_repo_size_files
        or len(repo_change.after_files) > config.max_repo_size_files
    ):
        return

    rng = Random(seed)
    sampler = WipRepoSampler()

    with repo_timer(commit_meta.repo_name) as timer:
        # NOTE(arun): We guard the RNG call to reproduce existing datasets when
        # config.empty_change_chance is 0. This is simply a nice-to-have property, so
        # drop it if it makes things easier down the line.
        if config.empty_change_chance > 0 and rng.random() < config.empty_change_chance:
            past_to_future_repo_change = repo_change
            past_to_wip_repo_change = RepoChange(
                repo_change.before_files, repo_change.before_files, pyr_vector([])
            )
            wip_to_future_repo_change = create_wip_to_future_repo_change(
                past_to_future_repo_change,
                past_to_wip_repo_change,
            )
            yield EditLocalizationProblem(
                pr_meta=pr_meta,
                commit_meta=commit_meta,
                wip_to_future_repo_change=wip_to_future_repo_change,
                past_to_wip_repo_change=past_to_wip_repo_change,
            )

        for i, wip_repo_change in enumerate(
            islice(
                sampler.simulate_wip_repo_states(repo_change, rng),
                config.max_problems_per_commit,
            )
        ):
            if len(wip_repo_change.after_files) > config.max_repo_size_files:
                continue
            if wip_repo_change.after_files == repo_change.after_files:
                # If there's no difference between the WIP and future states, there's
                # nothing to predict, so skip this sample.
                continue

            past_to_future_repo_change = repo_change
            past_to_wip_repo_change = wip_repo_change
            wip_to_future_repo_change = create_wip_to_future_repo_change(
                past_to_future_repo_change,
                past_to_wip_repo_change,
            )
            yield EditLocalizationProblem(
                pr_meta=pr_meta,
                commit_meta=commit_meta,
                wip_to_future_repo_change=wip_to_future_repo_change,
                past_to_wip_repo_change=past_to_wip_repo_change,
            )

            if (
                config.timeout_per_repo_s
                and timer.elapsed_s > config.timeout_per_repo_s
            ):
                logger.info(
                    "Ran out of time processing %s. Generated %d problems.",
                    commit_meta,
                    i + 1,
                )
                return


NegativeRetrievalStrategy = Literal["uniform", "retrieved"]
"""How to sample negative chunks.

- uniform: Sample negatives uniformly at random.
- retrieved: Sample negatives using a retrieval model.
"""


@dataclass
class CreateRetrievalProblemsConfig(DataClassJsonMixin):
    """Configuration for negative chunk retrieval."""

    retriever_config: RetrieverConfigDict
    """The config dict expected by the `create_retriever` function."""

    num_retrieved_chunks: int = 128
    """The number of retrieved chunks to return for each strategy."""

    timeout_per_repo_s: float = 10 * 60
    """Timeout (in seconds) for processing each repo.

    Partial results will be returned if timeout is hit.
    """

    chunker_configs: Sequence[dict] = field(default_factory=list)
    """If set, randomly sample one of these options to create the chunker."""

    retrieval_strategy: NegativeRetrievalStrategy = "uniform"
    """How to sample negative chunks."""

    ignore_whitespace_changes: bool = True
    """Whether to treat whitespace-only changes as negative examples."""

    instructions_path: Path | str | None = (
        "/mnt/efs/spark-data/user/guy/data-pipeline/pr_instructions_v2"
    )
    """The path to the instructions file."""

    max_chunks_per_document: int = 100
    """The max number of chunks to consider per document.

    Truncate any documents with larger than these number of chunks.
    """

    max_chunks_per_problem: int = 50_000
    """The max number of chunks to sample per document.

    Skip problems with more than this number of chunks.
    """

    max_gold_chunks_per_problem: int = 1000
    """The max number of chunks to sample per document.
    Skip problems with more than this number of gold chunks.
    """

    ignore_problems_with_insufficient_chunks: bool = False
    """Whether to ignore problems where there are not enough positive and negative chunks to reach num_retrieved_chunks."""

    VERSION = "1.2"
    """The version of the sampling config.

    Should be updated after each incompatible change to the retrieval stage.

    Changelog:
        - 1.0: First version -- uniform random sampling, ignore whitespace changes.
        - 1.1: Catch exceptions while parsing diffs.
        - 1.2: Added limits on chunk sizes at a per document and per repo level.
    """


@dataclass
class EditLocalizationRetrievalProblem:
    """Describes an edit localization sample for retrieval training.

    In addition to the problem, this sample also contains positive chunks and negative
    chunks in the current repo state.
    """

    positive_chunks: list[Chunk]
    """The positive chunks in the WIP repo state."""

    negative_chunks: dict[NegativeRetrievalStrategy, list[Chunk]]
    """The negative chunks in the WIP repo state."""

    positive_scores: list[float] | None = None
    """The positive scores in the WIP repo state.

    If set, scores correspond 1:1 with `positive_chunks`. If not, all positive chunks
    are given a score of 0.
    """

    negative_scores: dict[NegativeRetrievalStrategy, list[float]] | None = None
    """The negative scores in the WIP repo state.

    If set, scores correspond 1:1 with `negative_chunks`. If not, all negative chunks
    are given a score of -1.
    """


def create_retrieval_problems(
    problems: Iterable[EditLocalizationProblem],
    config: CreateRetrievalProblemsConfig,
    seed: int,
) -> Iterable[tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]]:
    """Create a retrieval problem by creating positive and negative chunks.

    Note:
        - This function is GPU-bound when using the "retrieved" strategy, and CPU-bound
            when using the "uniform" strategy.
        - Adjacent problems should be arranged to have similar retrieval contexts
            to enable efficient retrieval index update.
    """
    rng = Random(seed)

    retriever: DocumentIndex[NextEditPromptInput] | None
    instructions = None
    retriever = None
    if config.retrieval_strategy == "retrieved" and config.instructions_path:
        retriever = create_retriever(json.dumps(config.retriever_config))
        if config.instructions_path:
            instructions = load_instructions(config.instructions_path)

    for problem in problems:
        if config.chunker_configs and len(config.chunker_configs) > 1:
            chunker_config = rng.choice(config.chunker_configs)
        elif config.chunker_configs:
            chunker_config = config.chunker_configs[0]
        else:
            chunker_config = config.retriever_config["chunker"]
        chunk_cache = get_chunk_cache(
            tuple(chunker_config.items()), config.max_chunks_per_document
        )

        # Given two overlapping chunks, we take anything away from half of the overlap
        # to make sure it is safely in one or the other chunk. Here's a cartoon diagram
        # where the `....` represent a line.
        #
        #  ----- chunk 1 starts
        #  .....
        #  .....
        #  .....
        #  .....
        #  -*-*- chunk 2 starts
        #  .....
        #  ..... ^^^^^ labelled in chunk 1.
        #  ..... vvvvv labelled in chunk 2.
        #  .....
        #  ----- chunk 1 ends
        #  .....
        if isinstance(chunk_cache.chunker, chunking_functions.LineLevelChunker):
            boundary_lines = chunk_cache.chunker.overlap_lines // 2
        else:
            boundary_lines = 0

        with repo_timer(problem.commit_meta.repo_name) as timer:
            if timer.elapsed_s > config.timeout_per_repo_s:
                logger.info("Ran out of time processing %s.", problem.commit_meta)
                continue
            wip_files = problem.past_to_wip_repo_change.after_files
            logger.info(
                "Processing %s (%d files).", problem.commit_meta, len(wip_files)
            )

            wip_docs = [
                Document.new(contents, path) for path, contents in wip_files.items()
            ]
            wip_chunks = [
                chunk for doc in wip_docs for chunk in chunk_cache.get_chunks(doc)
            ]

            if len(wip_chunks) > config.max_chunks_per_problem:
                logger.info("Skipping %s: too many chunks.", problem.commit_meta)
                continue

            try:
                wip_to_future_diff = repo_change_utils.patchset_from_repo_change(
                    problem.wip_to_future_repo_change,
                    num_context_lines=0,
                    ignore_whitespace=config.ignore_whitespace_changes,
                )
            except Exception:
                logger.warning("Failed %s: couldn't parse diff.", problem.pr_meta)
                continue

            # Get positive chunks.
            diff_line_ranges = get_diff_line_ranges(
                wip_to_future_diff, source_range=True
            )
            chunk_labels = label_overlapping_chunks(
                wip_chunks,
                diff_line_ranges,
                ignore_boundary_lines=boundary_lines,
            )

            positive_chunks = [
                chunk for chunk, label in zip(wip_chunks, chunk_labels) if label
            ]
            negative_chunks = [
                chunk for chunk, label in zip(wip_chunks, chunk_labels) if not label
            ]
            if not positive_chunks or not negative_chunks:
                logger.info(
                    "Skipping %s: no positive or no negative chunks.",
                    problem.commit_meta,
                )
                continue
            if len(positive_chunks) > config.max_gold_chunks_per_problem:
                logger.error(
                    f"Skipping {problem.commit_meta}: too many positive chunks. Found {len(positive_chunks)} positive chunks but limit is {config.max_gold_chunks_per_problem}."
                )
                continue
            if (
                config.ignore_problems_with_insufficient_chunks
                and len(positive_chunks) + len(negative_chunks)
                < config.num_retrieved_chunks
            ):
                logger.error(
                    f"Skipping {problem.commit_meta}: not enough chunks. Found {len(positive_chunks)} positive chunks and {len(negative_chunks)} negative chunks but we expect {config.num_retrieved_chunks} chunks"
                    f" since config.ignore_problems_with_insufficient_chunks is True."
                )
                continue

            rng.shuffle(negative_chunks)
            random_negative_chunks = negative_chunks[: config.num_retrieved_chunks]
            if config.retrieval_strategy == "retrieved":
                assert retriever is not None
                assert instructions is not None

                retriever.chunker = chunk_cache.chunker
                retriever.add_docs(wip_docs)
                if problem.instructions:
                    instruction = problem.instructions
                else:
                    instruction = rng.choice(
                        # Add the empty instruction as one of the choices.
                        instructions.get(problem.pr_meta, [problem.pr_meta.title])
                        + [""]
                    )
                file_changes = tuple(
                    change.map(FileTuple.to_file)
                    for change in problem.past_to_wip_repo_change.changed_files
                )
                retrieved_negative_chunks, _ = retriever.query(
                    LocalizationNextEditPromptInput(
                        # TODO(arun): Fill with actual prompt information.
                        current_file=File("", ""),
                        edit_region=CharRange(0, 0),
                        instruction=instruction,
                        recent_changes=file_changes,
                    ),
                    doc_ids=[doc.id for doc in wip_docs],
                    top_k=config.num_retrieved_chunks,
                )
                # Filter out positive chunks
                positive_chunk_ids = {chunk.id for chunk in positive_chunks}
                retrieved_negative_chunks = [
                    chunk
                    for chunk in retrieved_negative_chunks
                    if chunk.id not in positive_chunk_ids
                ]

            else:
                retrieved_negative_chunks = []

            yield (
                problem,
                EditLocalizationRetrievalProblem(
                    positive_chunks=positive_chunks,
                    negative_chunks={
                        "uniform": random_negative_chunks,
                        "retrieved": retrieved_negative_chunks,
                    },
                ),
            )


ScoringStrategy = Literal["path_distance", "edit_model_score"]
"""The strategy to use to score positive chunks.

Options:
    - path_distance:
      For positive chunks, score based on their path distance to chunks in past_to_wip.
      For negative chunks, score is 0.
    - edit_model_score: Score based on the edit model score's `has_change` probability.
"""


@dataclass
class EditModelScorerConfig(DataClassJsonMixin):
    """Configuration for the edit model scorer."""

    formatter_config: EditGenFormatterConfig
    """The config parameters for the EditGenPromptFormatter."""

    checkpoint_path: str
    """Path to the edit model checkpoint."""

    tokenizer_name: str = "starcoder2"
    """The name of the base tokenizer to use."""

    VERSION = "1.0"
    """The version of the config.

    Should be updated after each incompatible change to the reordering stage.
    """


@dataclass
class ScoreChunksConfig(DataClassJsonMixin):
    """Configuration for reordering chunks."""

    scoring_strategies: Sequence[ScoringStrategy] = ("path_distance",)
    """Which strategies to use to score positive / negative chunks.

    It is assumed that each strategy produces a score between 0 and 1; the final score
    considered will be the average of the normalized scores from each strategy.
    """

    edit_model_scorer_config: EditModelScorerConfig | None = None
    """If set, the config parameters for the edit model scorer."""

    max_positive_level_sets: int = 5
    """The maximum number of levels to use for the path distance score.

    Too many levels means that adjacent levels will be too close and the score will be
    too noisy.
    """

    drop_negative_chunks: bool = False
    """The rate at which to drop negative chunks."""

    VERSION = "1.1"
    """The version of the config.

    Should be updated after each incompatible change to the reordering stage.

    Changelog:
        - 1.0: First version of the config.
        - 1.1: Add edit_model_score strategy.
    """


def score_chunks(
    config: ScoreChunksConfig,
    problems: Iterable[
        tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]
    ],
    seed: int,
) -> Iterable[tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]]:
    """Rescore the chunks in each problem.

    Args:
        config: The config.
        problems: The problems to score.
        seed: The random seed for this batch. This isn't currently used, but may be
            relevant in the future.

    This function is GPU-bound if an edit model is used and CPU-bound otherwise.
    """
    del seed

    if "edit_model_score" in config.scoring_strategies:
        assert (
            config.edit_model_scorer_config
        ), "edit_model_score requires an edit model to be configured"
        prompt_formatter = EditGenPromptFormatter(
            create_tokenizer_by_name(config.edit_model_scorer_config.tokenizer_name),
            config=config.edit_model_scorer_config.formatter_config,
        )
        model = create_model(
            config.edit_model_scorer_config.checkpoint_path,
        )
    else:
        prompt_formatter = None
        model = None

    for problem, retrieval_problem in problems:
        positive_scores_by_strategy: dict[ScoringStrategy, list[float]] = {}
        negative_scores_by_strategy: dict[
            NegativeRetrievalStrategy, dict[ScoringStrategy, list[float]]
        ] = {
            rstrategy: {}
            for rstrategy in (retrieval_problem.negative_scores or {}).keys()
        }

        # NOTE(arun): We end up truncating the number of negative chunks we consider
        # in the next stage anyways, and scoring all of them takes for ever. So...
        # as a hack, we halve the negative chunks in each strategy here.
        if config.drop_negative_chunks:
            for retrieval_strategy, chunks in retrieval_problem.negative_chunks.items():
                retrieval_problem.negative_chunks[retrieval_strategy] = chunks[
                    : len(chunks) // 2
                ]

        if "path_distance" in config.scoring_strategies:
            # Order changed paths to keep the most recently changed file at the beginning.
            wip_changed_paths = []
            for changed_path in reversed(problem.past_to_wip_repo_change.changed_files):
                if (after_path := get_after(changed_path)) is not None and (
                    after_path not in wip_changed_paths
                ) is not None:
                    wip_changed_paths.append(after_path.path)

            # Reorder positive chunks based on path distance to chunks in past_to_wip.
            positive_scores_by_strategy["path_distance"] = (
                score_chunks_by_path_distance(
                    retrieval_problem.positive_chunks,
                    wip_changed_paths,
                    min_score=0.5,
                    max_score=1,
                )
            )

            # For now, negative scores are set uniformly to 0.
            for retrieval_strategy, chunks in retrieval_problem.negative_chunks.items():
                negative_scores_by_strategy.setdefault(retrieval_strategy, {})[
                    "path_distance"
                ] = [0 for _ in chunks]

        if "edit_model_score" in config.scoring_strategies:
            assert prompt_formatter is not None and model is not None
            # Score positive chunks based on the edit model score.
            positive_scores_by_strategy["edit_model_score"] = [
                score_chunk_by_edit_model(chunk, problem, prompt_formatter, model)
                for chunk in retrieval_problem.positive_chunks
            ]
            for retrieval_strategy, chunks in retrieval_problem.negative_chunks.items():
                negative_scores_by_strategy.setdefault(retrieval_strategy, {})[
                    "edit_model_score"
                ] = [
                    score_chunk_by_edit_model(chunk, problem, prompt_formatter, model)
                    for chunk in chunks
                ]

        # Aggregate across strategies.
        def aggregate_scores(scores: dict[ScoringStrategy, list[float]]) -> list[float]:
            # Model training expects log scores
            return [
                math.log(sum(scores_) / len(scores_))
                for scores_ in zip(*scores.values(), strict=True)
            ]

        retrieval_problem.positive_scores = aggregate_scores(
            positive_scores_by_strategy
        )
        retrieval_problem.negative_scores = {
            retrieval_strategy: aggregate_scores(chunks_by_strategy)
            for retrieval_strategy, chunks_by_strategy in negative_scores_by_strategy.items()
        }

        yield (
            problem,
            retrieval_problem,
        )


def score_chunks_by_path_distance(
    chunks: Sequence[Chunk],
    target_paths: Sequence[Path | str],
    min_score: float = -1.0,
    max_score: float = 0.0,
    max_level_sets: int = 5,
) -> list[float]:
    """Rescore chunks based on path distance to target paths.

    This function orders chunks based on the minimum path distance to one of
    `target_paths`. If two chunks have the same minimum path distance, we prefer the
    chunk that is closer to target paths that appear earlier in `target_paths`.

    It then assigns scores to chunks based on their level-sets in the ordering.
    For example, given chunks in a/foo.py, a/bar.py, b/baz.py, b/qux.py, and the
    target paths [b/baz.py, a/foo.py], the level sets are:
        - b/baz.py, a/foo.py, b/qux.py, a/bar.py
    with scores [0.00, -0.33, -0.66, -1.00] respectively.

    Args:
        chunks: The chunks to reorder.
        target_paths: The paths to reorder chunks by.

    Returns:
        The reordered chunks.
    """
    if not chunks:
        return []

    target_paths = [Path(path) for path in target_paths]
    scores: list[tuple[int, ...]] = [
        min(
            [
                (path_distance(Path(chunk.path), target_path), idx)
                for idx, target_path in enumerate(target_paths)
            ],
            default=(0, 0),
        )
        if chunk.path
        else (sys.maxsize, sys.maxsize)
        for chunk in chunks
    ]
    level_sets = sorted(set(scores))

    # z is the normalization factor.
    z = min(max_level_sets, len(level_sets)) - 1
    if z == 0:
        return [max_score] * len(chunks)

    level_set_scores = {
        level_set: max_score - min(i, z) / z * (max_score - min_score)
        for i, level_set in enumerate(level_sets)
    }
    return [level_set_scores[score] for score in scores]


def score_chunk_by_edit_model(
    chunk: Chunk,
    problem: EditLocalizationProblem,
    prompt_formatter: EditGenPromptFormatter,
    model: LanguageModel,
) -> float:
    """Score a chunk based on the edit model score's `has_change` probability."""
    # 1. Format the prompt using the diff in the problem.
    recent_changes = tuple(
        change.map(FileTuple.to_file)
        for change in problem.past_to_wip_repo_change.changed_files
    )
    if chunk.path and (
        file_contents := problem.wip_to_future_repo_change.before_files.get(
            Path(chunk.path)
        )
    ):
        current_file = File(chunk.path, file_contents)
        edit_region = chunk.range
    else:
        # Fall back to the chunk text if we don't have the file contents.
        current_file = File(chunk.path or "", chunk.text)
        edit_region = CharRange(0, len(chunk.text))

    # NOTE(arun): In this pipeline, we don't have the instructions, so only use the
    # pr title as an instruction if we have no recent changes; otherwise don't use
    # any instruction.
    instruction = problem.pr_meta.title if not recent_changes else ""

    prompt_input = EditGenPromptInput(
        current_file=current_file,
        edit_region=edit_region,
        instruction=instruction,
        recent_changes=recent_changes,
        retrieval_chunks=[],
    )
    prompt_tokens = prompt_formatter.format_input_prompt(prompt_input).tokens
    assert isinstance(
        prompt_formatter.tokenizer.special_tokens, NextEditGenSpecialTokens
    )
    # Make this a probability.
    score = math.exp(
        log_likelihood_continuation(
            model,
            prompt_tokens,
            [prompt_formatter.tokenizer.special_tokens.has_change],
        )
    )
    return score


@dataclass
class FormatAsTokensConfig:
    """Configuration for formatting edit localization problems as token sequences."""

    query_formatter_config: dict
    """The config dict for the query formatter.

    TODO(arun): Unfortunately, there are no good options for creating query formatters.
    For now, this configuration dict corresponds to the options in
    NextEditLocationQueryFormatter (minus `tokenizer`).
    """

    document_formatter_config: dict
    """The config dict for the document formatter.

    TODO(arun): Unfortunately, there are no good options for creating query formatters.
    For now, this configuration dict corresponds to the relevant options in
    Ethanol6DocumentFormatter:
        - max_content_length (int)
        - add_path (bool)
    """

    downsample_commits_hunk_threshold: int = -1
    """If > 0, downsample large commits with greater than this number of hunks."""

    drop_instruction_rate: float = 0.5
    """The fraction of samples in which we drop the instruction."""

    instructions_path: Path | str | None = (
        "/mnt/efs/spark-data/user/guy/data-pipeline/pr_instructions_v2"
    )
    """The path to the instructions file.

    If None, we don't include instructions.
    """

    tokenizer_name: str = "starcoder"
    """The name of the base tokenizer to use."""

    negative_retrieval_strategies: Sequence[NegativeRetrievalStrategy] = ()
    """Which types of negative chunks to include in the output.

    If empty, use all the available negative sampling strategies.
    """

    min_diff_context_lines: int = 5
    """The minimum number of hunk context lines to show in the query diff section."""

    max_diff_context_lines: int = 5
    """The number of hunk context lines to show in the query diff section.

    If greater than `min_diff_context_lines`, we randomly vary the number of context
    lines to increase data diversity.
    """

    VERSION = "1.1"
    """The version of the prompt config.

    Should be updated after each incompatible change to the formatting stage.

    Changelog:
        - 1.0: First version of the config.
        - 1.1: Catch exceptions while parsing diffs.
    """


def format_as_tokens(
    config: FormatAsTokensConfig,
    problems: Iterable[
        tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]
    ],
    seed: int,
) -> Iterable[
    tuple[TokenArray, EditLocalizationProblem, EditLocalizationRetrievalProblem]
]:  # type: ignore
    """Format edit localization retrieval problems into token sequences.

    This stage also optionally performs:
        - dropping the instruction (if there is at least one recent change)
        - turning whitespace-only changes into negative examples

    This function is CPU-bound.
    """
    tokenizer = create_tokenizer_by_name(config.tokenizer_name)
    assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)

    instructions = (
        load_instructions(config.instructions_path)
        if config.instructions_path
        else dict[PRMeta, list[str]]()
    )

    query_cfg_dct: dict[str, Any] = dict(
        # We initially set this to be the minimum number of context lines, but sample
        # from the range [min, max] below.
        diff_context_lines=config.min_diff_context_lines,
        **config.query_formatter_config,
    )
    if "name" in query_cfg_dct:
        del query_cfg_dct["name"]
    if "tokenizer" in query_cfg_dct:
        del query_cfg_dct["tokenizer"]

    query_formatter = NextEditLocationQueryFormatter(
        tokenizer,
        config=NextEditLocationQueryFormatter.Config(**query_cfg_dct),
    )
    document_formatter = Ethanol6DocumentFormatter(
        apportionment_config=TokenApportionmentConfig(
            max_content_len=config.document_formatter_config.get(
                "max_content_length", 1000
            ),
            input_fraction=0,
            prefix_fraction=0,
            max_path_tokens=0,
        ),
        tokenizer=tokenizer,
        add_path=config.document_formatter_config.get("add_path", True),
    )

    rng = Random(seed)
    for problem, retrieval_problem in problems:
        # Get the total number of changes to filter large changes.
        try:
            past_to_future_diff = patchset_from_repo_change(
                problem.past_to_future_repo_change,
                num_context_lines=3,
                ignore_whitespace=True,
            )
            n_hunks = sum(
                len(pfile)
                for pfile in past_to_future_diff
                # Don't count added files because we can't predict their location.
                if not pfile.is_added_file
            )
        except Exception:
            logger.warning("Failed %s: couldn't parse diff.", problem.pr_meta)
            continue

        if (
            config.downsample_commits_hunk_threshold > 0
            and n_hunks > config.downsample_commits_hunk_threshold
            and rng.random() < 0.5
        ):
            logger.info(
                "Skipping %s: too many hunks %d > %d.",
                problem.pr_meta,
                n_hunks,
                config.downsample_commits_hunk_threshold,
            )
            continue

        # randomly vary diff context lines to increase data diversity
        if config.max_diff_context_lines > config.min_diff_context_lines:
            query_formatter.config.diff_context_lines = rng.randint(
                config.min_diff_context_lines, config.max_diff_context_lines
            )

        # Try to use one of the given instructions if possible; otherwise fall back to
        # using the PR title.
        if problem.instructions:
            instruction = problem.instructions
        else:
            instruction = rng.choice(
                instructions.get(problem.pr_meta, [problem.pr_meta.title]) or [""]
            )

        file_changes = tuple(
            change.map(FileTuple.to_file)
            for change in problem.past_to_wip_repo_change.changed_files
        )
        prompt_input = LocalizationNextEditPromptInput(
            # We don't actually use the current file, but we need to fill this in.
            current_file=File("", ""),
            edit_region=CharRange(0, 0),
            instruction=instruction,
            recent_changes=file_changes,
        )

        if (
            # only drop the instruction if there are recent changes
            len(prompt_input.recent_changes) > 0
            and rng.random() < config.drop_instruction_rate
        ):
            prompt_input = dataclasses.replace(prompt_input, instruction="")

        try:
            tokens = query_formatter.format_prompt(prompt_input).tokens()
        except Exception:
            logger.warning("Failed %s: couldn't format query.", problem.pr_meta)
            continue
        if not tokens:
            logger.warning("Failed %s: empty query.", problem.pr_meta)
            continue

        # Previously, we didn't have scores, and used 0.0 as the default value.
        if not retrieval_problem.positive_scores:
            retrieval_problem.positive_scores = [0.0] * len(
                retrieval_problem.positive_chunks
            )

        # Order positive chunks by score
        for chunk, score in sorted(
            zip(retrieval_problem.positive_chunks, retrieval_problem.positive_scores),
            key=lambda x: x[1],
            reverse=True,
        ):
            tokens += document_formatter.format_prompt(
                DocumentRetrieverPromptInput(
                    text=chunk.text,
                    path=str(chunk.parent_doc.path),
                ),
            ).tokens()
            # Add the score. Due to limitations in `IndexedDataset`, we tokenize the
            # score as a string.
            tokens += [
                *tokenizer.tokenize_safe(str(score)),
                tokenizer.special_tokens.end_of_key,
            ]

        # Combine the negative chunks
        for strategy in config.negative_retrieval_strategies:
            if strategy not in retrieval_problem.negative_chunks:
                logger.warning(
                    "Couldn't find the strategy %s in the input. Found %s.",
                    strategy,
                    retrieval_problem.negative_chunks.keys(),
                )
        negative_retrieval_strategies: Iterable[NegativeRetrievalStrategy] = [
            strategy
            for strategy in config.negative_retrieval_strategies
            if strategy in retrieval_problem.negative_chunks
        ] or retrieval_problem.negative_chunks.keys()

        # Default to -1 for all negative chunks.
        if not retrieval_problem.negative_scores:
            retrieval_problem.negative_scores = {
                strategy: [-1] * len(chunks)
                for strategy, chunks in retrieval_problem.negative_chunks.items()
            }

        negative_chunks = [
            chunk_score
            for chunk_score in interleave_sequences(
                [
                    zip(
                        retrieval_problem.negative_chunks[strategy],
                        retrieval_problem.negative_scores[strategy],
                    )
                    for strategy in negative_retrieval_strategies
                ]
            )
            if chunk_score is not None
        ]

        for chunk, score in negative_chunks:
            tokens += document_formatter.format_prompt(
                DocumentRetrieverPromptInput(
                    text=chunk.text,
                    path=str(chunk.parent_doc.path),
                )
            ).tokens()
            # Add the score
            tokens += [
                *tokenizer.tokenize_safe(str(score)),
                tokenizer.special_tokens.end_of_key,
            ]

        yield to_token_array(tokens, tokenizer.vocab_size), problem, retrieval_problem


T = TypeVar("T")


def interleave_sequences(sequences: Sequence[Iterable[T]]) -> Iterable[T | None]:
    return itertools.chain.from_iterable(itertools.zip_longest(*sequences))


@functools.cache
def create_model(
    checkpoint_path: Path | str,
) -> LanguageModel:
    from research.models.language_models.fastforward import (
        create_from_starcoder2_checkpoint,
    )

    return create_from_starcoder2_checkpoint(checkpoint_path)

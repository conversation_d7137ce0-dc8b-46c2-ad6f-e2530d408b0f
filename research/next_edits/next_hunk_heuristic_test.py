from research.next_edits.next_hunk_heuristic import _find_active_hunks, _HunkRanges
from base.ranges.range_types import In<PERSON><PERSON><PERSON><PERSON>


def test_find_active_hunks():
    all_hunks = {
        first_hunk := _HunkRanges(
            "hindsight_next_edit_dataset.py",
            <PERSON><PERSON><PERSON><PERSON><PERSON>(1, 3),
            In<PERSON><PERSON><PERSON><PERSON>(1, 6),
        ),
        second_hunk := _HunkRanges(
            "hindsight_next_edit_dataset.py",
            <PERSON><PERSON><PERSON><PERSON><PERSON>(10, 20),
            <PERSON>t<PERSON><PERSON><PERSON>(30, 40),
        ),
    }

    edit_ranges = [
        # touches second hunk
        [("hindsight_next_edit_dataset.py", Int<PERSON><PERSON><PERSON>(12, 15))],
        # touches first hunk
        [("hindsight_next_edit_dataset.py", Int<PERSON><PERSON><PERSON>(0, 4))],
    ]

    assert _find_active_hunks(all_hunks, edit_ranges) == [second_hunk, first_hunk]

    # touches second hunk, so will reset the active hunks
    edit_ranges.append([("hindsight_next_edit_dataset.py", <PERSON><PERSON><PERSON><PERSON><PERSON>(9, 11))])
    assert _find_active_hunks(all_hunks, edit_ranges) == [second_hunk]

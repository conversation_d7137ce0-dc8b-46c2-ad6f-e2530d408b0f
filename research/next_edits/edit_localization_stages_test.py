"""Tests for stages in research.next_edits.edit_localization_stages."""

import pytest

from research.core.types import Chunk, Document
from research.next_edits.edit_localization_stages import score_chunks_by_path_distance


@pytest.mark.parametrize(
    "target_paths, expected_scores",
    # Scores are for the chunks:
    # ["a/foo.py", "a/bar.py", "b/baz.py", "b/qux.py", "pathless"]
    [
        pytest.param(
            [],
            # Level sets are: [0, 0, 0, 0, 1]
            [0.0, 0.0, 0.0, 0.0, -1.0],
            id="empty_targets",
        ),
        pytest.param(
            ["a/bar.py"],
            # Level sets are: [1, 0, 2, 2, 3]
            [-1 / 3, 0, -2 / 3, -2 / 3, -3 / 3],
            id="one_target",
        ),
        pytest.param(
            ["b/baz.py", "a/foo.py"],
            # Level sets are: [3, 1, 0, 2, 4]
            [-1 / 4, -3 / 4, 0, -2 / 4, -4 / 4],
            id="two_targets",
        ),
    ],
)
def test_score_chunks_by_path_distance(
    target_paths: list[str], expected_scores: list[float]
):
    chunks = [
        Chunk(
            id=path_name,
            text=path_name,
            parent_doc=Document(path_name, path_name, path_name),
            # Arbitrary values for testing.
            char_offset=0,
            length=100,
            line_offset=0,
            length_in_lines=30,
        )
        for path_name in [
            "a/foo.py",
            "a/bar.py",
            "b/baz.py",
            "b/qux.py",
        ]
    ] + [
        # bad chunk without a path.
        Chunk(
            id="pathless",
            text="pathless",
            parent_doc=Document("pathless", "pathless", ""),
            # Arbitrary values for testing.
            char_offset=0,
            length=100,
            line_offset=0,
            length_in_lines=30,
        )
    ]
    actual_scores = score_chunks_by_path_distance(chunks, target_paths)
    assert actual_scores == expected_scores

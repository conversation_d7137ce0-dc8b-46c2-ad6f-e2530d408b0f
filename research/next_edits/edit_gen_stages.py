"""Data pipeline stages for next edit generation.

We use a 3-stage pipeline to generate the training data:

1. Sampling stage: this stage walks through a list of commits/PRs from a repo and
samples edit generation problems from each commit/PR. This stage is CPU and IO-bound.
2. Retrieval stage: this stage performs dense retrieval on the sampled problems.
This stage is GPU-bound.
3. Formatting stage: this stage formats the result from the previous stage into
prompt tokens expected by the model. This stage is CPU-bound.
"""

import copy
import dataclasses
import logging
import time
from collections.abc import Sequence
from dataclasses import dataclass, field
from pathlib import Path
from random import Random
from typing import Any, Iterable, Literal, TypedDict, TypeVar, assert_never, cast

from pyrsistent import pvector as pyr_vector

from base.diff_utils.changes import Modified
from base.diff_utils.diff_formatter import DiffHunk, format_file_changes_with_ranges
from base.diff_utils.diff_utils import File
from base.diff_utils.str_diff import (
    precise_line_diff,
)
from base.languages.language_guesser import guess_language
from base.logging.secret_logging import Ignore<PERSON>ll<PERSON>ogger
from base.next_edit_filter.rule_based_filters import is_deleting_imports
from base.prompt_format.common import PromptChunk
from base.prompt_format_next_edit.gen_prompt_formatter import (
    EditGenFormatterConfig,
    containment_fraction,
    tokenize_diff_hunks,
)
from base.prompt_format_next_edit.retrieval_prompt_formatter import (
    EditGenRetrievalPromptInput,
    EditGenRetrievalQueryFormatterConfig,
)
from base.prompt_format_retrieve import RetrieverPromptFormatter
from base.prompt_format_retrieve.prompt_formatter import InstructRetrieverPromptInput
from base.ranges.range_types import CharRange
from base.static_analysis.common import assert_eq
from base.tokenizers import create_tokenizer_by_name
from research.core.types import Document, DocumentId, NTuple
from research.next_edits.common_stages import (
    load_instructions,
)
from research.next_edits.edit_gen_formatters import (
    EditGenPromptFormatter,
    EditGenPromptInput,
    PromptSectionName,
    default_prompt_section_order,
)
from research.next_edits.edit_gen_sampler import (
    EditGenOutput,
    EditGenProblem,
    EditGenSampler,
    estimate_file_diff_tokens,
)
from research.next_edits.next_edits_dataset import (
    HindsightEditGenProblem,
    HindsightOrigin,
    OrderedRepoChange,
    PRMeta,
    RepoChangeWithMeta,
)
from research.next_edits.wip_repo_sampler import (
    WipRepoSampler,
    lerp_round,
    simulate_recency_diffs,
)
from research.retrieval.types import DocumentIndex
from research.utils.repo_change_utils import (
    CommitMeta,
    FileTuple,
    GitOperationError,
    RepoChange,
    squash_file_changes,
)
from research.utils.token_array_utils import TokenArray, to_token_array


@dataclass
class SamplingConfig:
    """Configuration for the Next Edit sampling stage.

    See the doc string under `VERSION` for the change log.
    """

    sampler: EditGenSampler = field(default_factory=EditGenSampler)
    """The sampler used to sample edit gen problems."""

    max_problems_per_repo: int = 1000
    """Max number of edit gen problems to sample from each repo."""

    max_problems_per_commit: int = 10
    """Max number of edit gen problems to sample from each commit."""

    max_files_per_repo: int = 5000
    """Repo change with more than this many after_files will be filtered out.

    This may be needed to speed up the retrieval stage.
    """

    max_deleted_lines: int = 5
    """Edits that delete more than this many lines will be downsampled."""

    turn_into_negative_sample_rate: float = 0.0
    """The probability of turning a positive sample into a negative sample by
    applying the changes in the output. This simulates the case where the user has
    just accepted a model suggestion."""

    timeout_per_repo: float = 600
    """Timeout (in seconds) for processing each repo.

    Partial results will be returned if timeout is hit.
    """

    max_squashes: int = 2
    """Max number of previous repo changes to be squashed with the repo change.

    When nonzero, the sampling stage will sometimes "squash" previous repo changes
    with the current repo change to simulate a more noisy diff context.
    When set to zero, no squashing will be performed.
    """

    simulate_recency_diffs: bool = True
    """Whether to simulate recency diffs when sampling edit gen problems."""

    small_commits_tokens: int = 1000
    """Commits with a size below this limit will get downsampled."""

    VERSION = "30"
    """The version of the sampling config.

    Should be updated after each incompatible change to the sampling stage.

    Change log:
        - v1.1: Reduce `random_edit_region_rate` to 0.3.`
        - v1.2: Fix a minor bug in the sampler that reduces number of candidates.
        - v1.3:
            - Fix an edit region sampling bug that only selects tiny target changes.
            - Improve `uniform` sampling strategy.
            - Add `keep_most` WIP sampling strategy.
            - Add `max_problems_per_commit` and remove `max_commits_per_repo`.
            - Add `random_target_file_rate` to improve negative examples sampling.
        - v1.4:
            - Increase the number of dropped changes when using `keep_most`.
        - v1.5:
            - Split large additions into smaller ones.
            - Default to using `keep_most` strategy.
        - v1.6:
            - Drop more changes when using `keep_most`.
            - Increase default `max_edit_region_lines` to 80.
            - Bias toward larger changes when sampling edit regions.
        - v1.7:
            - Record full commit messages instead of just the first line.
        - v1.8:
            - Various changes to sample larger changes.
            - Support smart chunker-based edit regions.
            - Increase default `max_files_per_repo` to 3000.
        - v1.9:
            - Introduce a new 'sandwich' case in simulate_wip_span.
        - v1.10:
            - Bias import changes to be later than other changes.
            - Reduce the chance of picking import changes as the editing region.
            - Merge character_diff_rate and hunk_diff_rate into `wip_file_rate`.
        - v1.11:
            - More diverse WIP span sampling by randomly splitting both the before
              and after diff span.
            - This fixes the regression caused by some v1.9 changes.
            - v1.11.1: Attach PRMeta to sampled problems when using the PR dataset.
        - v1.12:
            - Fix a line ending bug in the PR dataset.
        - v1.13:
            - Use more precise line diff in the sampler and output formatter.
            - v1.13.1: Further improve mod span diff alignment.
        - v14:
            - Update how we are storing commit and PR metadata.
        - v15:
            - Implement commit squashing to sample more noisy diff contexts.
        - v16:
            - Sampling less problems from small commits.
            - Default max_files_per_repo to 5000.
            - Rebalance to 60% of positive examples (applied in the last stage).
            - v16.1: Less agressive filtering; refactor config interface.
            - v16.2:
                - Deduplicate similar changes in the sampled problems.
                - Remove .ipynb files from the dataset.
        - v17:
            - Switch to using precise_char_diff in the WIP sampler.
            - v17.1: Fix two bugs in squashing repo changes.
            - v17.2:
                - Ignore commit changes with no file extension.
                - More robust handling of `git show` errors.
            - v17.3: Shuffle recent changes in the 1st stage instead of the 3rd.
            - v17.4: Remove `.min.js` files from training data.
        - v18:
            - Default to use `simulate_recency_diffs`.
        - v19:
            - Partially subtract recency diff from vcs diff.
        - v20:
            - Downsample whitespace-only changes.
        - v21:
            - Stop rejecting oversized changes when sampling editing regions.
        - v22:
            - Upsample edit regions around the simulated next change.
        - v23:
            - More detailed simulation of recency diffs.
            - Randomly cut off some VCS diffs.
        - v24:
            - Improve the recency diff simulation introduced by v23.
            - v24.1: use near-final states when sampling from ordered repo changes.
            - v24.2: reduce additional_target_groups to 1.
        - v25:
            - downsample edits that delete more than 5 lines.
            - use keep_most strategy to simulate current file.
            - increase rechunker_rate to 0.75.
            - filter out files with lines longer than 300 chars.
            - only reorder last recency diff when the problem has a change.
        - v26:
            - Turn off diff duplication in simulate_recency_diffs.
            - Simplify simulate_wip_span.
            - Introduce `turn_into_negative_sample_rate`.
        - v27:
            - Sometimes split wip spans by lines.
            - Switch back to use `uniform` strategy to simulate current file.
        - v28: Aim to make the model less lazy.
            - Remove the logic that upsamples edit regions around the simulated next
              change.
            - Use `group_into_hunks` in simulate_wip_code to simulate less broken code.
            - Default `turn_into_negative_sample_rate` to 0.
        - v29:
            - Skip examples that deletes imports.
            - Default `repo_change_wip_ratio` to 0.6.
            - Default `small_commits_tokens` to 1000.
        - v30:
            - Record the final state of the repo at the end of the commit/PR.
            - Reduce ROW_SIZE_LIMIT_MB to 100.
    """


class RetrieverConfigDict(TypedDict):
    """The config dict expected by the `create_retriever` function"""

    scorer: dict
    chunker: dict
    query_formatter: dict
    document_formatter: dict


@dataclass
class RetrievalConfig:
    retriever_config: RetrieverConfigDict
    """The config dict expected by the `create_retriever` function."""

    num_retrieved_chunks: int
    """The number of retrieved chunks to return."""

    interleave_gold_prompt_chunks_rate: float = 0.5
    """Randomly interleave normal retrieval results with ones using the replacement."""

    skip_dense_retrieval: bool = False
    """Whether to skip dense retrieval. Mainly used for debugging."""

    synthetic_instructions_path: Path | None = None
    """If set, will sometimes replace PR descriptions with synthetic instructions."""

    timeout_per_repo: float = 1200
    """Timeout (in seconds) for processing each repo.

    Partial results will be returned if timeout is hit.
    """

    query_formatter_config: EditGenRetrievalQueryFormatterConfig = field(
        default_factory=EditGenRetrievalQueryFormatterConfig
    )
    """The config dict for the query formatter."""

    filter_retrieval_overlap_ratio: float = 0.9
    """Threshold of overlap ratio between retrieved chunks and diff hunks.

    If the overlap ratio is greater than this threshold, the chunk will be filtered out.
    """

    edit_group_sizes: Sequence[int] = (1, 5)
    """The maximum number of events in each group.

    See the description in `group_edit_events` for more details.
    """

    VERSION = "5"
    """The version of the sampling config.

    Should be updated after each incompatible change to the retrieval stage.

    Change log:
        - v1.1:
            - Support training with synthetic instructions.
            - Return RagEditGenProblem instead of input-output pairs.
        - v1.2:
            - Use `EditRetrieverPromptFormatter` for retrieval prompt.
        - v3/v1.3:
            - Use `InstructRetrieverPromptInput` for retrieval prompt.
        - v4/1.4:
            - Use `EditGenRetrievalPromptInput` for retrieval prompt.
        - v5:
            - Add `edit_group_sizes` to support grouping edit events.
    """


@dataclass
class PromptConfig:
    tokenizer_name: Literal[
        "starcoder",
        "starcoder2",
        "llama3_base",
        "llama3_instruct",
        "deepseek_coder_base",
        "deepseek_coder_instruct",
        "qwen25coder",
    ]
    """The registered name of the tokenizer to use."""

    formatter_config: EditGenFormatterConfig
    """The config parameters for the EditGenPromptFormatter."""

    downsample_retrieval_rate: float = 0.2
    """The fraction of samples in which we downsample the number of retrieved chunks."""

    drop_instruction_rate: float = 0.5
    """The fraction of samples in which we drop the instruction."""

    max_output_tokens: int = 1000
    """The max number of output tokens to keep in each training example."""

    VERSION = "21"
    """The version of the prompt config.

    Should be updated after each incompatible change to the formatting stage.

    ### Change logs
    - v1.1: Fixed a chunk filtering bug.
    - v1.2:
        - Added `diff_filter` to filter out diffs from certain files.
        - Order diffs by path distance to current file.
        - Added support for diff-based output.
    - v1.3:
        - Only drop instruction if there are recent changes.
        - Use diff-based output by default.
    - v1.4:
        - Turn whitespace-only changes into negative examples.
    - v1.5:
        - Randomly vary diff context lines to increase data diversity.
    - v1.6:
        - Sample different prompt section orders during training.
        - Change the default prompt section order.
    - v1.7:
        - Switch to a new difflib-based format.
        - Randomly shuffle the order of the file changes during training.
        - Truncate the diff section from left.
        - Update the default section budgets.
    - v1.8:
        - Support training the model also on instruction tokens.
        - Add loss mask to the output tokens using negative token values.
        - Switch to using the default prompt section order during training.
    - v1.9:
        - Mask the instruction when there's no diff in the prompt.
        - Default use_diff_output to True.
    - v1.10:
        - Increase the default max_output_tokens to 1000.
        - v10.1: Return FormattedEditGenProblem instead of TokenArray.
    - v1.11:
        - Use smart header in the diff by default.
    - v1.12:
        - Add Ruby and Swift smart header support.
        - More precise default smart header support.
    - v13/v1.13:
        - Default to use more compact model diff.
    - v14/v1.14:
        - Filter out duplicated file paths from the diff.
    - v15:
        - Remove extra newlines in the smart header.
    - v16:
        - Remove the logic that turns whitespace-only changes into negative examples.
    - v17:
        - Add pause tokens into model output (diff_chunk_size_chars=800).
    - v18:
        - Fix a bug in pause token logic.
    - v19:
        - Add vertical bar for single-line modifications.
    - v20:
        - Use a fixed budget for prefix and suffix.
    - v21:
        - Switch to the improved token masking logic.
    """

    def max_sequence_length(self) -> int:
        return self.formatter_config.max_prompt_tokens + self.max_output_tokens


def repo_changes_to_problems(
    config: SamplingConfig,
    commits: Iterable[RepoChangeWithMeta],
    seed: int,
    start_time: float,
) -> Iterable[EditGenProblem]:
    """Sample edit gen problems from a stream of repo changes.

    This function assumes `commits` are all from the same repo when applying the
    corresponding repo-level limits defined in the config.

    Args:
        config: The sampling config.
        commits: A stream of consecutive commits from the same repo.
        seed: The random seed.
        start_time: The start time used to check for timeout. Partial results will be\
            returned current_time - start_time > config.timeout_per_repo.
    """
    rng = Random(seed)
    timeout = config.timeout_per_repo
    problem_count = 0
    prev_commits = list[RepoChange]()
    for i, commit in enumerate(commits):
        repo_change = commit.repo_change
        if len(repo_change.after_files) > config.max_files_per_repo:
            logging.warning(
                f"Skipping repo {commit.commit_meta.repo_name} with {len(repo_change.after_files)} files."
            )
            break
        for problem in _sample_wip_problems_from_commit(rng, config, commit):
            problem = _post_process_problem(rng, config, prev_commits, problem)
            yield problem
            problem_count += 1
            if problem_count >= config.max_problems_per_repo:
                return
            if timeout and time.time() - start_time > timeout:
                logging.warning(f"Repo timeout hit. ({i} commits processed)")
                return
        if config.max_squashes > 0 and repo_change.changed_files:
            prev_commits.append(repo_change)
            # keep only the last `max_squashes` commits to save memory
            prev_commits = prev_commits[-config.max_squashes :]
        if timeout and time.time() - start_time > timeout:
            logging.warning(f"Repo timeout hit. ({i} commits processed)")
            return


def _sample_wip_problems_from_commit(
    rng: Random, config: SamplingConfig, commit: RepoChangeWithMeta
) -> list[EditGenProblem]:
    """Sample a list of edit gen problems from a given repo change.

    This function performs a additional downsampling based on the parameters in the
    config.
    """
    commit_size = _estimate_commit_modified_diff_tokens(commit.repo_change)
    # We probabilistically skip sampling from small commits
    if rng.random() ** 2 > (commit_size / config.small_commits_tokens):
        return []
    problems = list[EditGenProblem]()
    for problem in config.sampler.sample_problems(
        rng, commit.repo_change, commit.commit_meta, commit.pr_meta
    ):
        # Downsample whitespace-only changes
        if problem.is_whitespace_change() and rng.random() < 0.8:
            continue
        # Skip examples that deletes imports
        before_code = problem.current_code
        after_code = problem.prefix + problem.output.replacement + problem.suffix
        if is_deleting_imports(problem.current_path, before_code, after_code):
            continue
        deleted_lines = count_nonempty_lines(
            problem.selected_code
        ) - count_nonempty_lines(problem.output.replacement)
        # Downsample edits that delete too many lines
        if deleted_lines > config.max_deleted_lines and rng.random() < 0.9:
            continue
        problem.debug_info["deleted_lines"] = deleted_lines
        problems.append(problem)

    # Make sure we do not sample too many problems from a single commit
    if len(problems) > config.max_problems_per_commit:
        rng.shuffle(problems)
        problems = problems[: config.max_problems_per_commit]
    return problems


def _post_process_problem(
    rng: Random,
    config: SamplingConfig,
    prev_commits: Sequence[RepoChange],
    problem: EditGenProblem,
) -> EditGenProblem:
    """Perform post-process transformations on the problem.

    Note that the original problem will be mutated by this function.
    """
    changes_shuffled = False
    vcs_change = problem.repo_change

    # Optionally shuffle the order of the file changes in the current commit
    if rng.random() < 0.5:
        changes_shuffled = True
        shuffled_changes = list(problem.repo_change.changed_files)
        rng.shuffle(shuffled_changes)
        vcs_change = RepoChange(
            before_files=problem.repo_change.before_files,
            after_files=problem.repo_change.after_files,
            changed_files=pyr_vector(shuffled_changes),
        )
    problem.debug_info["shuffle_recent_changes"] = changes_shuffled

    # Simulate recency diff (aka. granular edits).
    recency_change = RepoChange.no_change(vcs_change.after_files)
    if config.simulate_recency_diffs and rng.random() < 0.8:
        target_path = problem.current_path if problem.output.changed else None
        vcs_change, recency_change = simulate_recency_diffs(
            vcs_change, target_path, rng, max_recency_files=2
        )
    problem.debug_info["num_recency_diffs"] = len(recency_change.changed_files)

    # Optionally squash the current commit with previous commits to have a more
    # noisy diff context
    if config.max_squashes > 0 and rng.random() < 0.5:
        n_squashes = min(len(prev_commits), rng.randint(1, config.max_squashes))
        prev_changes = prev_commits[len(prev_commits) - n_squashes :]
        vcs_change = squash_diff_context(
            rng, vcs_change, prev_changes, problem.commit_meta
        )
        problem.debug_info["n_squashes"] = n_squashes

    # Optionally cut off some earlier parts of the diff context to simulate a
    # truncated diff context (e.g., the beginning of a stream of granular edit events
    # may not align with PR boundaries.)
    if rng.random() < 0.5:
        cut_points = list(WipRepoSampler().simulate_wip_repo_states(vcs_change, rng))
        if cut_points:
            # throw away at most half of the changes
            cut_at = lerp_round(0, len(cut_points), rng.random() * 0.5)
            vcs_change = cut_points[cut_at]
            problem.debug_info["dropped_vcs_changes"] = cut_at

        all_changed_files = vcs_change.changed_files + recency_change.changed_files
        problem.repo_change = RepoChange(
            before_files=vcs_change.before_files,
            after_files=recency_change.after_files,
            changed_files=all_changed_files,
        )
    # Optionally turn the sample into a negative example by applying the changes in
    # the output to the current code.
    if rng.random() < config.turn_into_negative_sample_rate:
        problem = _turn_into_negative_sample(problem, rng)
    return problem


def _turn_into_negative_sample(problem: EditGenProblem, rng: Random) -> EditGenProblem:
    """Turn a positive sample into negative by applying the changes in the output."""
    output = problem.output
    if not output.changed:
        return problem
    new_code = problem.prefix + output.replacement + problem.suffix
    size_change = len(new_code) - len(problem.current_code)
    new_edit_region = CharRange(
        problem.edit_region.start,
        problem.edit_region.stop + size_change,
    )
    # Compute compute the new output using problem.final_code.
    diff_to_future = precise_line_diff(new_code, problem.final_code)
    edit_region_in_future = diff_to_future.before_range_to_after(new_edit_region)
    new_replacement = problem.final_code[edit_region_in_future.to_slice()]
    new_output = EditGenOutput(
        new_replacement, changed=new_replacement != output.replacement
    )
    # Add the new change to the diff context.
    new_repo_change = problem.repo_change.add_changed_file(
        Modified(
            FileTuple(problem.current_path, problem.current_code),
            FileTuple(problem.current_path, new_code),
        )
    )
    new_debug_info = copy.copy(problem.debug_info)
    new_debug_info["turn_into_negative_sample"] = True
    if rng.random() < 0.75:
        # try to squash the new change with the last change in the context
        squashed = squash_file_changes(new_repo_change.changed_files[-2:])
        new_file_changes = new_repo_change.changed_files[:-2] + squashed
        new_repo_change = RepoChange(
            before_files=new_repo_change.before_files,
            after_files=new_repo_change.after_files,
            changed_files=new_file_changes,
        )
        new_debug_info["squash_last_two_changes"] = True
    new_problem = copy.copy(problem)
    new_problem.current_code = new_code
    new_problem.edit_region = new_edit_region
    new_problem.output = new_output
    new_problem.repo_change = new_repo_change
    new_problem.debug_info = new_debug_info
    return new_problem


def ordered_repo_changes_to_problems(
    config: SamplingConfig,
    ordered_repo_changes: Iterable[OrderedRepoChange],
    seed: int,
    start_time: float,
) -> Iterable[EditGenProblem]:
    """Sample edit gen problems from a stream of repo changes.

    This function assumes `commits` are all from the same repo when applying the
    corresponding repo-level limits defined in the config.

    Args:
        config: The sampling config.
        ordered_repo_changes: A stream of consecutive commits from the same repo.
        seed: The random seed.
        start_time: The start time used to check for timeout. Partial results will be\
            returned if timeout is hit.
    """
    rng = Random(seed)
    timeout = config.timeout_per_repo
    problem_count = 0
    prev_commits = list[RepoChange]()
    for i, ordered_repo_change in enumerate(ordered_repo_changes):
        repo_change = ordered_repo_change.repo_change
        commit = ordered_repo_change.commit_meta
        if len(repo_change.after_files) > config.max_files_per_repo:
            logging.warning(
                f"Skipping repo {commit.repo_name} with {len(repo_change.after_files)} files."
            )
            break
        problems = list[EditGenProblem]()
        for problem in config.sampler.sample_problems_with_order(
            rng, ordered_repo_change, prev_commits
        ):
            if problem.is_whitespace_change() and rng.random() < 0.8:
                # downsample whitespace-only changes
                continue
            deleted_lines = count_nonempty_lines(
                problem.selected_code
            ) - count_nonempty_lines(problem.output.replacement)
            if deleted_lines > config.max_deleted_lines and rng.random() < 0.9:
                # downsample edits that delete too many lines
                continue
            problem.debug_info["deleted_lines"] = deleted_lines
            problems.append(problem)

        if len(problems) > config.max_problems_per_commit:
            # randomly downsample the problems
            rng.shuffle(problems)
            problems = problems[: config.max_problems_per_commit]

        for problem in problems:
            yield problem
            problem_count += 1
            if problem_count >= config.max_problems_per_repo:
                return
        if config.max_squashes > 0 and repo_change.changed_files:
            prev_commits.append(repo_change)
            # keep only the last `max_squashes` commits to save memory
            prev_commits = prev_commits[-config.max_squashes :]
        if timeout and time.time() - start_time > timeout:
            logging.warning(f"Repo timeout hit. ({i} commits processed)")
            return


def squash_diff_context(
    rng: Random,
    repo_change: RepoChange,
    prev_changes: Sequence[RepoChange],
    commit_meta: CommitMeta,
) -> RepoChange:
    """Add previous repo changes into `repo_change`'s diff context."""
    if not prev_changes:
        return repo_change
    for prev_change in reversed(prev_changes):
        prev_file_changes = list(prev_change.changed_files)
        rng.shuffle(prev_file_changes)
        prev_change = RepoChange(
            before_files=prev_change.before_files,
            after_files=prev_change.after_files,
            changed_files=pyr_vector(prev_file_changes),
        )
        try:
            repo_change = prev_change.squash(repo_change)
        except GitOperationError as e:
            logging.warning(
                f"Failed to squash previous changes for problem: {str(commit_meta)}."
                f"Git error: {e.args[0]}"
            )
            return repo_change
    return repo_change


@dataclass
class RagEditGenProblem:
    """Retrieval-augmented edit gen problem."""

    input: EditGenPromptInput
    output: EditGenOutput
    commit_meta: CommitMeta | None
    pr_meta: PRMeta | None
    debug_info: dict[str, Any]
    hindsight_origin: HindsightOrigin | None = None


def perform_dense_retrieval(
    config: RetrievalConfig,
    problems: Iterable[EditGenProblem] | Iterable[HindsightEditGenProblem],
    seed: int,
) -> Iterable[RagEditGenProblem]:
    """Perform dense retrieval for each edit gen problem.

    Note:
        - This function is GPU-bound.
        - Adjacent problems should be arranged to have similar retrieval contexts
            to enable efficient retrieval index update.
    """
    all_problems = list(problems)
    if not all_problems:
        # skip the cost of building the retriever if there are no problems
        return

    rng = Random(seed)

    index: DocumentIndex | None
    if config.skip_dense_retrieval:
        index = None
    else:
        index = _get_retriever_cached(config)

    indexed_docs: set[DocumentId] = set()
    synth_instructions = {}
    if config.synthetic_instructions_path:
        synth_instructions = load_instructions(config.synthetic_instructions_path)

    for problem in all_problems:
        # make copies to be safely mutated later
        new_info = {}
        if isinstance(problem, EditGenProblem):
            problem = copy.copy(problem)
            debug_info = copy.copy(problem.debug_info)
            candidate_instructions = None
            if problem.pr_meta is not None:
                candidate_instructions = synth_instructions.get(problem.pr_meta)
            if candidate_instructions and rng.random() < 0.5:
                # randomly replace the instruction with a synthetic one
                problem.instruction = rng.choice(candidate_instructions)
                new_info["use_synthetic_instruction"] = True
            after_files = problem.repo_change.after_files
            all_docs = [Document.new(code, path) for path, code in after_files.items()]
            recent_changes = [
                change.map(lambda x: x.to_file())
                for change in problem.repo_change.changed_files
            ]
        else:
            debug_info = {}
            all_docs = [
                Document.new(code, path) for path, code in problem.current_files.items()
            ]
            recent_changes = (
                problem.squashable_edits.convert_edit_events_to_modified_files(
                    safe_logger=IgnoreAllLogger(),
                    group_sizes=config.edit_group_sizes,
                )
            )

        if index:
            _update_index(index, indexed_docs, all_docs)
            doc_ids = {doc.id for doc in all_docs}

            query_formatter: RetrieverPromptFormatter = index.scorer.query_formatter  # type: ignore

            if query_formatter.input_type == EditGenRetrievalPromptInput:
                query = EditGenRetrievalPromptInput(
                    current_file=File(str(problem.current_path), problem.current_code),
                    edit_region=problem.edit_region,
                    instruction=problem.instruction,
                    recent_changes=recent_changes,
                )
            elif query_formatter.input_type == InstructRetrieverPromptInput:
                query = InstructRetrieverPromptInput(
                    prefix=problem.prefix,
                    suffix=problem.suffix,
                    path=str(problem.current_path),
                    selected_code=problem.selected_code,
                    instruction=problem.instruction,
                )
            else:
                raise ValueError(f"Unknown {query_formatter.input_type=}")

            all_diff_hunks = format_file_changes_with_ranges(
                recent_changes,
                diff_context_lines=config.query_formatter_config.diff_context_lines,
            )

            # Filter down to a random subset of these hunks.
            diff_budget = config.query_formatter_config.section_budgets["diff_tks"]
            diff_token_budget = rng.randint(diff_budget // 2, diff_budget)
            t_hunks = tokenize_diff_hunks(
                all_diff_hunks,
                diff_token_budget,
                query_formatter.tokenizer,
            )

            chunks, _ = index.query(
                query,  # type: ignore
                doc_ids=doc_ids,
                # Get a extra chunks to account for filtering.
                top_k=int(1.5 * config.num_retrieved_chunks),
            )
            # Filter out chunks that overlap with the diff hunks early so we can use
            # them in later scoring pipelines.
            chunks = filter_overlapping_chunks(
                [chunk.to_prompt_chunk() for chunk in chunks],
                problem.current_path,
                problem.edit_region,
                [h.hunk for h in t_hunks],
                config.filter_retrieval_overlap_ratio,
            )

            if (
                problem.output.changed
                and rng.random() < config.interleave_gold_prompt_chunks_rate
            ):
                if isinstance(query, EditGenRetrievalPromptInput):
                    new_code = (
                        problem.prefix + problem.output.replacement + problem.suffix
                    )
                    new_file = File(str(problem.current_path), new_code)
                    new_region = CharRange(
                        problem.edit_region.start,
                        problem.edit_region.start + len(problem.output.replacement),
                    )
                    query = dataclasses.replace(
                        query,  # type: ignore
                        current_file=new_file,
                        edit_region=new_region,
                    )
                elif isinstance(query, InstructRetrieverPromptInput):
                    query.prefix = problem.prefix
                    query.suffix = problem.suffix
                    query.selected_code = problem.output.replacement
                else:
                    assert_never(query)
                gold_chunks, _ = index.query(
                    query,
                    doc_ids=doc_ids,
                    top_k=config.num_retrieved_chunks,
                )
                gold_chunks = filter_overlapping_chunks(
                    [chunk.to_prompt_chunk() for chunk in gold_chunks],
                    problem.current_path,
                    problem.edit_region,
                    [h.hunk for h in t_hunks],
                    config.filter_retrieval_overlap_ratio,
                )
                chunks = list(interleave_unique(chunks, gold_chunks))

        else:
            chunks = []

        prompt_input = EditGenPromptInput(
            current_file=File(str(problem.current_path), problem.current_code),
            edit_region=problem.edit_region,
            instruction=problem.instruction,
            recent_changes=recent_changes,
            # NOTE(arun): We store more retrieved chunks than we need to because it's
            # expensive to recompute them.
            retrieval_chunks=list(chunks),
        )
        if new_info:
            debug_info["perform_dense_retrieval"] = new_info
        if isinstance(problem, HindsightEditGenProblem):
            yield RagEditGenProblem(
                prompt_input,
                problem.output,
                commit_meta=None,
                pr_meta=None,
                debug_info=debug_info,
                hindsight_origin=problem.origin,
            )
        else:
            yield RagEditGenProblem(
                prompt_input,
                problem.output,
                problem.commit_meta,
                problem.pr_meta,
                debug_info=debug_info,
                hindsight_origin=None,
            )


_retriever_cached: tuple[DocumentIndex, RetrievalConfig] | None = None


def _get_retriever_cached(config: RetrievalConfig) -> DocumentIndex:
    """Get the cached retriever instance."""
    # NOTE(arun): Importing the types required here causes the GPU to be
    # opened and breaks loading.
    from research.eval.harness.factories import create_retriever

    global _retriever_cached

    if _retriever_cached is None or _retriever_cached[1] != config:
        index = create_retriever(cast(dict, config.retriever_config))
        index.load()
        _retriever_cached = (index, config)
    else:
        index, _ = _retriever_cached
    return index


@dataclass
class FormattedEditGenProblem:
    tokens: TokenArray
    """The formatted prompt tokens."""
    commit_meta: CommitMeta | None
    """The commit metadata."""
    pr_meta: PRMeta | None
    """The PR metadata."""
    debug_info: dict[str, Any]
    """Extra info recording how the problem was created."""
    hindsight_origin: HindsightOrigin | None = None
    """Nonempty if this problem was sampled from Hindsight data."""


def format_as_tokens(
    config: PromptConfig,
    problems: Iterable[RagEditGenProblem],
    seed: int,
) -> Iterable[FormattedEditGenProblem]:
    """Format edit gen problems into token sequences.

    This stage also optionally performs:
        - downsampling number of retrieved chunks
        - dropping the instruction (if there is at least one recent change)
        - turning whitespace-only changes into negative examples
        - shuffle the order of the file changes

    This function is CPU-bound.
    """
    tokenizer = create_tokenizer_by_name(config.tokenizer_name)
    # always add loss mask for the training data
    formatter_config = dataclasses.replace(config.formatter_config, add_loss_mask=True)
    formatter = EditGenPromptFormatter(tokenizer, config=formatter_config)
    rng = Random(seed)
    for problem in problems:
        prompt_input = problem.input
        output = problem.output
        debug_info = copy.copy(problem.debug_info)
        new_info = {}
        prompt_input = fuzz_edit_gen_prompt_input(
            prompt_input,
            rng,
            config.downsample_retrieval_rate,
            config.drop_instruction_rate,
            debug_info=new_info,
        )
        if formatter.config.diff_context_lines > 5:
            # randomly vary diff context lines to increase data diversity
            diff_context_lines = rng.randint(5, formatter.config.diff_context_lines)
            formatter.config.diff_context_lines = diff_context_lines
            new_info["diff_context_lines"] = diff_context_lines

        format_output = formatter.format_input_prompt(prompt_input)
        if not prompt_input.instruction.strip() and not format_output.diff_tokens:
            # skip problems with neither an instruction nor diff tokens
            continue
        input_prompt = format_output.tokens
        lang = guess_language(prompt_input.current_file.path)
        output_prompt = formatter.format_output_prompt(
            prompt_input.selected_code, output, lang=lang
        )
        output_prompt = output_prompt[: config.max_output_tokens]
        full_prompt = input_prompt + output_prompt
        tokens = to_token_array(full_prompt, tokenizer.vocab_size, nonnegative=False)
        if new_info:
            debug_info["format_as_tokens"] = new_info
        yield FormattedEditGenProblem(
            tokens,
            problem.commit_meta,
            problem.pr_meta,
            hindsight_origin=problem.hindsight_origin,
            debug_info=debug_info,
        )


def fuzz_edit_gen_prompt_input(
    prompt_input: EditGenPromptInput,
    rng: Random,
    downsample_retrieval_rate: float,
    drop_instruction_rate: float,
    debug_info: dict[str, Any] | None = None,
) -> EditGenPromptInput:
    """Fuzz an edit gen problem."""
    if debug_info is None:
        debug_info = {}

    if rng.random() < downsample_retrieval_rate:
        n_keep = rng.randint(0, 5)
        prompt_input = dataclasses.replace(
            prompt_input, retrieval_chunks=prompt_input.retrieval_chunks[:n_keep]
        )
        debug_info["downsample_retrieval_chunks"] = n_keep
    if prompt_input.recent_changes and rng.random() < drop_instruction_rate:
        # note that we only drop the instruction if there are recent changes
        prompt_input = dataclasses.replace(prompt_input, instruction="")
        debug_info["drop_instruction"] = True

    return prompt_input


def _update_index(
    index: DocumentIndex, indexed: set[DocumentId], new_docs: Iterable[Document]
) -> None:
    """Add any new document to the index."""
    to_add = [doc for doc in new_docs if doc.id not in indexed]
    index.add_docs(to_add)
    indexed.update(doc.id for doc in to_add)


def _random_section_order(rng: Random) -> NTuple[PromptSectionName]:
    """Sample a random prompt section order s.t. the last section is "selection"."""
    default_order = default_prompt_section_order
    # The last section should always be "selection"
    assert_eq(default_order[-1], PromptSectionName.SELECTION)
    shuffled_order: list[PromptSectionName] = list(default_order[:-1])
    rng.shuffle(shuffled_order)
    return tuple(shuffled_order + [PromptSectionName.SELECTION])


def _estimate_commit_modified_diff_tokens(repo_change: RepoChange) -> int:
    return sum(
        estimate_file_diff_tokens(change.before.code, change.after.code)
        for change in repo_change.changed_files
        if isinstance(change, Modified)
    )


T = TypeVar("T")


def interleave_unique(seq1: Sequence[T], seq2: Sequence[T]) -> Iterable[T]:
    """Interleave two sequences, ignoring duplicates.

    Given two lists of items, interleave them in alternating order, ignoring
    duplicates. For example,

    >>> chunks1 = ["A", "B", "C", "D"]
    >>> chunks2 = ["C", "E", "D", "F"]
    >>> list(interleave_unique(chunks1, chunks2))
    ["A", "C", "B", "E", "D", "F"]
    """
    seen = set()
    i, j = 0, 0
    while i < len(seq1) and j < len(seq2):
        if seq1[i] not in seen:
            yield seq1[i]
            seen.add(seq1[i])
        if seq2[j] not in seen:
            yield seq2[j]
            seen.add(seq2[j])
        i += 1
        j += 1
    while i < len(seq1):
        if seq1[i] not in seen:
            yield seq1[i]
            seen.add(seq1[i])
        i += 1
    while j < len(seq2):
        if seq2[j] not in seen:
            yield seq2[j]
            seen.add(seq2[j])
        j += 1


def filter_overlapping_chunks(
    chunks: Sequence[PromptChunk],
    current_path: Path | str,
    edit_region: CharRange,
    diff_hunks: Sequence[DiffHunk],
    filter_retrieval_overlap_ratio: float = 0.9,
) -> Sequence[PromptChunk]:
    return [
        chunk
        for chunk in chunks
        # Keep chunks that don't overlap with current edit region...
        if not (chunk.path == str(current_path) and chunk.crange.touches(edit_region))
        # ... and aren't significantly overlapped by a diff hunk.
        and not any(
            h.after_path == chunk.path
            and containment_fraction(outer=h.after_crange, inner=chunk.crange)
            >= filter_retrieval_overlap_ratio
            for h in diff_hunks
        )
    ]


def count_nonempty_lines(s: str) -> int:
    return sum(1 for line in s.splitlines() if line.strip())

"""Records next edit data based on your local git repo.

This script works by using the state of the git repository this script is run in. It
uses the state of the staged git repository as the "work in progress" (wip) state,
with the staged diff being the "past_to_wip" diff state, and the unstaged diff as the
"wip_to_future" diff state.

Usage:
$ python3 record_next_edits.py [-r repo path] <output_path>

Commands:

1. `a` or `add`: Add the currently staged changes to the current session as a new
    temporary commit.
3. `s` or `save`: Save the past_to_wip and wip_to_future diffs for each intermediate
    commit in the session using previous and next commits.
4. `git <cmd>`: lets you call git in the target repo. This let's you call e.g. git add,
    git amend, etc. as a convenience wrapper. You may want to do:
        - git add -u to stage all changes
        - git st or git diff to preview the changes.
5. `r` or `reset`: undoes all the checkpoint commits and resets the repo
    to its original state.
"""

import cmd
import shlex
import subprocess
import textwrap
import uuid
from collections.abc import Sequence
from contextlib import contextmanager
from pathlib import Path
from typing import TextIO, cast

import git
import pygments
import pygments.formatters
import pygments.lexers
import zstandard

from base.caching.lru_cache import lru_cache
from research.core import diff_utils
from research.next_edits.next_edits_dataset import CommitMeta, Datum


@lru_cache
def get_repo_name(repo: git.Repo) -> str:
    try:
        return (
            repo.git.config("--get", "remote.origin.url")
            .split("/")[-1]
            .replace(".git", "")
        )
    except git.GitCommandError:
        return Path(repo.git_dir).parent.name


@lru_cache
def create_document(blob: git.Blob) -> diff_utils.File | None:
    path = str(blob.path)
    try:
        text = blob.data_stream.read().decode()
        return diff_utils.File(path, text)
    except UnicodeDecodeError:
        return None


@contextmanager
def stage_changes(repo: git.Repo, commit_unstaged: bool = False):
    """Temporarily commit the staged changes to get a temporary commit sha.

    Yields:
        The sha of the temporary "WIP" commit.
    """
    # Check if there are any changes to stage in the first place.
    has_staged_changes = repo.is_dirty(index=True, working_tree=False)
    has_unstaged_changes = repo.is_dirty(index=False, working_tree=True)

    if has_staged_changes:
        # Step 1: commit the staged changes.
        repo.git.commit("-m", "WIP staged commit (record_next_edits.py)", "--no-verify")

    if commit_unstaged and has_unstaged_changes:
        # Step 1: commit the unstaged changes.
        repo.git.commit(
            "-a", "-m", "WIP unstaged commit (record_next_edits.py)", "--no-verify"
        )

    yield repo.head.commit.hexsha

    if commit_unstaged and has_unstaged_changes:
        # Undo the change by doing a "mixed" reset -- the changes are left unstaged.
        repo.git.reset("HEAD~1")

    if has_staged_changes:
        # Undo the change by doing a "soft" reset -- the changes are left staged.
        repo.git.reset("HEAD~1", "--soft")


def create_repo_snapshot(
    repo: git.Repo, commit_sha: str = "HEAD", exclude_prefixes: Sequence[str] = ()
) -> diff_utils.Repository:
    """Create a snapshot of the current repo.

    Args:
        repo: The git repo to create the snapshot from.

    Returns:
        diff_utils.Repository: The snapshot of the current repo.
    """
    return diff_utils.Repository(
        files=[
            doc
            for blob in repo.commit(commit_sha).tree.traverse()
            if blob.type == "blob"  # type: ignore
            and not any(blob.path.startswith(prefix) for prefix in exclude_prefixes)  # type: ignore
            and (doc := create_document(cast(git.Blob, blob))) is not None
        ]
    )


def create_datum(
    repo: git.Repo,
    instruction: str,
    past_repo: diff_utils.Repository,
    wip_repo: diff_utils.Repository,
    future_repo: diff_utils.Repository,
    num_context_lines: int = 5,
    group_id: str = "",
    group_sequence_id: int = 0,
) -> Datum:
    """Create a Datum object from the current git repo relative to session_sha.

    Args:
        repo: The git repo to create the Datum from.
        instruction: The instruction to use for the Datum.
        past_sha: The sha to use as the base for the Datum.
        wip_sha: The sha to use as the current WIP state for the Datum. If these are
            staged changes, call `stage_changes` to get the sha of the temporary commit.
        future_sha: The sha to use for the final, future state of the Datum. Defaults to
            the current HEAD + any unstaged changes.
        exclude_prefixes: A list of path prefixes to exclude.
        num_lines_context: The number of lines of context to use for the diff.
        group_id: The group id to use for the Datum.

    Returns:
        Datum: The Datum object.
    """
    past_to_wip_diff = diff_utils.compute_repo_diff(
        past_repo, wip_repo, num_context_lines=num_context_lines
    )
    wip_to_future_diff = diff_utils.compute_repo_diff(
        wip_repo, future_repo, num_context_lines=num_context_lines
    )

    # We'll always use the message of the past_sha.
    past_sha = repo.head.commit.hexsha
    message = repo.head.commit.message
    if isinstance(message, bytes):
        message = message.decode("utf-8")

    return Datum(
        id=f"{group_id}:{group_sequence_id}",
        instruction=instruction,
        past_to_wip_diff=str(past_to_wip_diff),
        wip_to_future_diff=str(wip_to_future_diff),
        wip_files=list(wip_repo.files),
        commit_meta=CommitMeta(
            sha=past_sha,
            message=str(message),
            repo_name=get_repo_name(repo),
        ),
        group_id=group_id,
        group_sequence_id=group_sequence_id,
    )


def print_diff(diff_text: str):
    print(
        pygments.highlight(
            diff_text,
            pygments.lexers.DiffLexer(),
            pygments.formatters.Terminal256Formatter(),
        )
    )


def preview_datum(datum: Datum):
    print("Previewing changes...")
    print(f"Instruction: {datum.instruction or '<none>'}")
    print(f"WIP state: {len(datum.wip_files)} docs")
    print(f"Commit state: {datum.commit_meta}")
    print("=== Past to WIP changes ===")
    print_diff(datum.past_to_wip_diff)
    print("=== WIP to future changes ===")
    print_diff(datum.wip_to_future_diff)


def confirm(prompt: str, default: bool = False) -> bool:
    if default:
        return input(f"{prompt} [Y/n]").lower() != "n"
    else:
        return input(f"{prompt} [y/N]").lower() == "y"


class NextEditRecorder(cmd.Cmd):
    def __init__(
        self,
        repo: git.Repo,
        output: TextIO,
        exclude_prefixes: list[str],
    ):
        super().__init__()
        self.repo = repo
        self.output = output
        self.exclude_prefixes = exclude_prefixes

        # Tracks repository states seen in the current session.
        self._session_repos = []

        self.prompt = "next_edits> "
        self.intro = textwrap.dedent(
            """\
                Welcome to the next edit recorder!
                Use: `add [-u]` to add the current staged changes as a new WIP state.
                  Use add multiple times to create a sequence of WIP states.
                  -u: will stage all unstaged changes.
                Use: `save` when you are done to save the whole sequence of WIP states
                  to the output file.
            """
        )

    def do_a(self, flags: str):
        return self.do_add(flags)

    def do_add(self, flags: str):
        commit_unstaged = False
        if flags == "-u":
            commit_unstaged = True
        elif flags:
            print(f"The only valid flag is `-u`. Got {flags=}.")
            return

        # Create the session if it doesn't exist already.
        if not self._session_repos:
            head_sha = self.repo.head.commit.hexsha
            self._session_repos.append(
                create_repo_snapshot(
                    self.repo, head_sha, exclude_prefixes=self.exclude_prefixes
                )
            )
            print(f"Starting new session at {head_sha}.")
        else:
            print(f"Continuing session with {len(self._session_repos) - 1} commits.")

        with stage_changes(self.repo, commit_unstaged=commit_unstaged) as wip_sha:
            wip_repo = create_repo_snapshot(
                self.repo, wip_sha, exclude_prefixes=self.exclude_prefixes
            )

        incremental_diff = str(
            diff_utils.compute_repo_diff(self._session_repos[-1], wip_repo)
        )

        if not incremental_diff:
            print("No changes since last checkpoint.")
        else:
            print_diff(incremental_diff)

        if not incremental_diff or not confirm("Add?", default=True):
            # If we haven't added any commits, pop the "past repo".
            if len(self._session_repos) == 0:
                self._session_repos.pop()
            return

        self._session_repos.append(wip_repo)
        print("Added.")

    def do_s(self, instruction: str = ""):
        return self.do_save(instruction)

    def do_save(self, instruction: str = ""):
        if not self._session_repos:
            print("No session to save.")
            return

        # For the final repo, we'll want to add everything.
        with stage_changes(self.repo, commit_unstaged=True) as final_sha:
            future_repo = create_repo_snapshot(
                self.repo, final_sha, exclude_prefixes=self.exclude_prefixes
            )

        incremental_diff = str(
            diff_utils.compute_repo_diff(self._session_repos[-1], future_repo)
        )

        if not incremental_diff:
            print("No changes since last checkpoint.")
            future_repo = self._session_repos[-1]
        else:
            print("Adding checkpoint with final incremental diff:")
            print_diff(incremental_diff)
            self._session_repos.append(future_repo)

        if not instruction:
            instruction = input("Instruction: ").strip()

        past_repo = self._session_repos[0]
        data = []
        should_save = True

        # Get a random but shared id for all the checkpoints.
        group_id = str(uuid.uuid4())
        for i, wip_repo in enumerate(self._session_repos[1:-1]):
            if i > 0 and not confirm("Continue?", default=True):
                should_save = False
                break

            print(f"Checkpoint #{i} / {len(self._session_repos) - 2}:")
            datum = create_datum(
                self.repo,
                instruction,
                past_repo=past_repo,
                wip_repo=wip_repo,
                future_repo=future_repo,
                # We group the changes by the first commit in the chain.
                group_id=group_id,
                group_sequence_id=i,
            )
            data.append(datum)
            preview_datum(datum)

        if not should_save or not confirm("Save?", default=True):
            return

        for datum in data:
            self.output.write(datum.to_json())
            self.output.write("\n")
        self.output.flush()
        print("Saved.")

        self.do_reset(None)

    def r(self, arg: None):
        return self.do_reset(arg)

    def do_reset(self, arg: None):
        # Reset the current head to the session head.
        if not self._session_repos:
            print("No session to reset.")
            return

        if not confirm("Reset?", default=True):
            return

        # This is equivalent to "git reset --hard HEAD", and will remove any modified
        # or staged added files. Untracked files are unharmed.
        self.repo.head.reset(self.repo.head.commit, index=True, working_tree=True)
        self._session_repos.clear()
        print("Reset.")

    def do_q(self, arg: None):
        return self.do_quit(arg)

    def do_quit(self, arg: None):
        print("Quitting...")
        self.do_reset(None)
        return True

    def do_git(self, arg: str):
        subprocess.run(
            ["git", "--git-dir", self.repo.git_dir] + shlex.split(arg),
            cwd=Path(self.repo.git_dir).parent,
        )


def main():
    import argparse

    parser = argparse.ArgumentParser(__doc__)
    parser.add_argument(
        "-r", "--git-root", type=Path, default=Path(), help="Path to the git root."
    )
    parser.add_argument(
        "-x",
        "--exclude-prefixes",
        type=str,
        nargs="*",
        default=[],
        help="Path prefixes to ignore.",
    )
    parser.add_argument("output_path", type=Path, help="Path to save output.")
    args = parser.parse_args()

    repo = git.Repo(args.git_root)

    print("Excluding files with the following prefixes:", args.exclude_prefixes)

    with zstandard.open(args.output_path, "at") as f:
        NextEditRecorder(repo, f, args.exclude_prefixes).cmdloop()


if __name__ == "__main__":
    main()

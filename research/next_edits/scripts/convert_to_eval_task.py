"""<PERSON><PERSON>t to convert validation data from parquet to JSON as expected by the eval task.

Usage:
$ python3 convert_to_eval_task.py <path to parquet file> <path to output json file>
"""

import hashlib
from collections.abc import Iterable
from concurrent import futures
from functools import partial
from pathlib import Path
from typing import Any, Optional, cast

import pandas
from tqdm import tqdm
from unidiff import PatchSet, UnidiffParseError

from research.core.diff_utils import parse_git_diff_output
from research.data.spark.pipelines.stages.prs import NextEditLocationSample
from research.next_edits.next_edits_dataset import CommitMeta, Da<PERSON>


def try_parse(diff: str) -> Optional[PatchSet]:
    try:
        return parse_git_diff_output(diff)
    except UnidiffParseError:
        # unidiff itself can fail, and this wrapper fixes some
        # but not all failures, so just skip those that still fail.
        return None


def convert_to_next_edit_datum(row: dict, max_files: int = 1000) -> Datum | None:
    """Convert data from `NextEditLocationSample` format to next edit dataset format."""
    row = cast(dict[str, Any], row)
    sample = NextEditLocationSample.schema().load(row)

    # Ignore PRs that are too large.
    if len(sample.wip_files) > max_files:
        return None
    if try_parse(sample.wip_to_future_diff) is None:
        # Ignore malformed diffs
        return None
    if not sample.wip_to_future_diff:
        # Ignore complete diffs (nothing to predict)
        return None
    if not sample.past_to_wip_diff:
        # Ignore empty diffs (no context to predict with)
        return None

    # Compute some sort of stable id that is unique to this sample.
    sample_id = hashlib.sha256(
        "".join(
            [
                sample.repo_name,
                str(sample.pr_number),
                sample.past_to_wip_diff,
                sample.wip_to_future_diff,
            ]
        ).encode("utf-8")
    ).hexdigest()

    return Datum(
        id=sample_id,
        instruction=sample.title,
        past_to_wip_diff=sample.past_to_wip_diff,
        wip_to_future_diff=sample.wip_to_future_diff,
        wip_files=sample.wip_files,
        commit_meta=CommitMeta(repo_name=sample.repo_name, sha="", message=""),
        group_id=f"{sample.repo_name}/{sample.pr_number}",
        group_sequence_id=0,
    )


def load_next_edit_data_from_parquet(
    parquet_path: Path, max_files: int = 1000
) -> Iterable[Datum | None]:
    rows = [
        row
        for path in tqdm(
            sorted(parquet_path.glob("*.parquet")), desc="converting parquet"
        )
        for row in pandas.read_parquet(path).to_dict(orient="records")
    ]

    with futures.ProcessPoolExecutor() as executor:
        yield from tqdm(
            executor.map(
                partial(convert_to_next_edit_datum, max_files=max_files), rows
            ),
            total=len(rows),
            desc="converting data",
        )


def main():
    import argparse

    parser = argparse.ArgumentParser(__doc__)
    parser.add_argument(
        "-i", "--input_path", type=Path, help="Path to input parquet file."
    )
    parser.add_argument(
        "-o", "--output_path", type=Path, help="Path to output json file."
    )
    parser.add_argument(
        "--filter_max_files",
        type=int,
        default=1000,
        help="Filter samples with more than these number of files.",
    )
    args = parser.parse_args()

    data = load_next_edit_data_from_parquet(
        args.input_path, max_files=args.filter_max_files
    )
    with open(args.output_path, "w") as f:
        for datum in data:
            if datum is None:
                continue
            f.write(datum.to_json())
            f.write("\n")


if __name__ == "__main__":
    main()

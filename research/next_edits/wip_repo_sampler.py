"""Utilities to sample WIP repo states from a PR or commit."""

from __future__ import annotations

from collections import defaultdict
import logging
from dataclasses import dataclass
from functools import cache
from pathlib import Path
from random import Random
from typing import Iterable, Literal, Mapping, Sequence, TypeVar, assert_never

from base.caching.lru_cache import lru_cache
from base.diff_utils.diff_utils import compute_file_diff, compute_file_diff_hunks
from base.diff_utils.edit_events import FilePath, SingleFileEdit
from base.diff_utils.str_diff import DeletedSpan
from base.languages.language_guesser import guess_language
from base.languages.languages import LanguageId
from base.ranges.line_map import LineMap, get_line_break
from base.ranges.range_types import Char<PERSON>ange
from base.static_analysis.common import assert_eq
from research.core.utils import join_list, assert_str_eq, shorten_str
from research.core.artifacts import collect_artifacts, post_artifact
from research.core.changes import Added, Changed, Deleted, Modified
from research.core.str_diff import (
    Added<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    precise_char_diff,
)
from research.next_edits.next_edits_dataset import OrderedRepo<PERSON>hange
from research.static_analysis.import_finder import find_all_import_lines
from research.utils.repo_change_utils import (
    FileTuple,
    PyrMap,
    PyrVector,
    RepoChange,
    pyr_vector,
    squash_file_changes,
)
from research.utils.sampling_utils import downsample_to

WipSamplingStrategy = Literal["uniform", "keep_most"]
"""The strategy that decides how to partially drop changes in a PR.

If there are `N` droppable changes and we want to drop `n` changes,
- "uniform" will sample `n` uniformly between 0 and `N`
- "keep_most" will sample `n` that is closer to 0 so we keep most of the changes.
"""


@dataclass
class WipRepoState:
    """A simulated work-in-progress repo state."""

    description: str
    """Description for the future changes."""
    past_to_wip: RepoChange
    """The past to WIP repo change."""
    wip_to_future: RepoChange
    """The WIP to future repo change."""
    debug_info: dict
    """Debug info."""


@dataclass
class OrderedWipRepoSampler:
    """A sampler to sample WIP repo states from a RepoChange with hunk order."""

    wip_file_rate: float = 0.3
    """The probability of simulating a WIP file state.

    The WIP sampler create a new WIP repo state for each group of changes. For
    each changed file in the group, we either keep all its changes or drop a subset
    of changes to form a WIP file state.
    """

    additional_target_groups: int = 1
    """How many additional change groups to incldue as the targets."""

    def simulate_wip_repo_states_with_order(
        self,
        repo_change_with_order: OrderedRepoChange,
        prev_change: RepoChange | None,
        rng: Random,
    ) -> Iterable[WipRepoState]:
        """Simulate a series of WIP repo states from the repo change and hunk order.

        This function generates a sequence of work-in-progress (WIP) repository states
        based on the provided ordered repository changes. It simulates the gradual
        application of changes, mimicking a developer's workflow.

        The process works as follows:
        1. Shuffles the changes within each edit group to introduce randomness.
        2. For each group of changes:
           - Simulates partial changes already made by the user. This is controlled
           by the `wip_file_rate` parameter.
           - Simulates more granular diffs to represent incremental changes.
           - Optionally squashes the current commit with previous commits.
        4. Yields a WipRepoState for each group, containing:
           - A description of future changes.
           - The changes from the past state to the current WIP state.
           - The remaining changes from the WIP state to the future state.
           - Debug information about the simulation process.

        Args:
            repo_change_with_order: The ordered repository changes.
            prev_change: The previous change, if any.
            rng: A random number generator for introducing randomness.

        Yields:
            WipRepoState: Simulated work-in-progress repository states.
        """
        assert self.additional_target_groups >= 0

        edit_groups = list[list[SingleFileEdit]]()
        instructions = list[str]()
        for g in repo_change_with_order.edit_groups:
            changes = list(g.changes)
            # TODO (jiayi, arun): decide if this is what we want.
            rng.shuffle(changes)
            edit_groups.append(changes)
            instructions.append(g.description)
        if len(edit_groups) < 2:
            # we require at least two edit groups to generate training samples
            return
        before_files = repo_change_with_order.repo_change.before_files
        groupped_changes = edits_to_file_changes_grouped(before_files, edit_groups)
        changes_so_far: PyrVector[Changed[FileTuple]] = pyr_vector()
        earlier_descriptions = list[str]()
        for i in range(len(groupped_changes)):
            # also include changes from later groups as additional targets
            all_targets = join_list(
                groupped_changes[i : i + 1 + self.additional_target_groups]
            )
            assert all_targets
            # simulate partial changes that are already made by the user
            partial_changes = list[Changed[FileTuple]]()
            remaining_changes = list[Changed[FileTuple]]()
            for change in all_targets:
                if rng.random() > self.wip_file_rate:
                    # do not include this into partial_changes
                    remaining_changes.append(change)
                    continue
                if not isinstance(change, Modified):
                    partial_changes.append(change)
                    continue
                # partially include this change
                partial_code = simulate_wip_code(
                    rng,
                    change.before.code,
                    change.after.code,
                    guess_language(change.after.path),
                    wip_strategy="uniform",
                ).code
                partial_file = FileTuple(change.after.path, partial_code)
                if partial_file != change.before:
                    partial_changes.append(Modified(change.before, partial_file))
                if partial_file != change.after:
                    remaining_changes.append(Modified(partial_file, change.after))
            past_to_wip_changes = changes_so_far + pyr_vector(partial_changes)
            with collect_artifacts() as collector:
                past_to_wip_changes = self.simulate_granular_diff(
                    past_to_wip_changes, rng
                )
                debug_info = collector.get_flattened_artifacts()
            past_to_wip = RepoChange.from_before_and_changes(
                before_files,
                past_to_wip_changes,
            )
            wip_to_future = RepoChange.from_before_and_changes(
                past_to_wip.after_files,
                remaining_changes,
            )

            num_squashes = 0
            if prev_change is not None and rng.random() < 0.5:
                # squash the current commit with previous commits
                past_to_wip = RepoChange(
                    prev_change.before_files,
                    past_to_wip.after_files,
                    prev_change.changed_files + past_to_wip.changed_files,
                )
                num_squashes += 1

            debug_info["num_squashes"] = num_squashes
            debug_info["earlier_descriptions"] = earlier_descriptions.copy()
            change_group, description = groupped_changes[i], instructions[i]
            yield WipRepoState(
                description=description,
                past_to_wip=past_to_wip,
                wip_to_future=wip_to_future,
                debug_info={"simulate_wip_repo_states_with_order": debug_info},
            )
            changes_so_far = changes_so_far + pyr_vector(change_group)
            earlier_descriptions.append(description)

    def simulate_granular_diff(
        self,
        changed_files: PyrVector[Changed[FileTuple]],
        rng: Random,
        debug_mode: bool = True,
    ) -> PyrVector[Changed[FileTuple]]:
        """Simulate granular diffs from the given sequence of detailed file changes.

        This function breaks down the given file changes into more granular diffs,
        simulating a more realistic development process. It works as follows:

        1. Splits the changes into two groups: earlier and later changes.
        2. Squashes the changes within each group.
        3. For the last change in the later group (if it's a Modified change):
            - Simulates an intermediate state between the before and after versions.
            - Splits this last change into two smaller changes if possible.
        4. Optionally generates debug information about the simulated diffs.
        """
        divider = round(lerp(0.6, 0.9, rng.random()) * len(changed_files))
        earlier_changes = squash_file_changes(changed_files[:divider])
        later_changes = squash_file_changes(changed_files[divider:])
        # further split the last change to simulate more granular diffs
        if later_changes and isinstance(last_change := later_changes[-1], Modified):
            before_file = last_change.before
            after_file = last_change.after
            wip_code = simulate_wip_code(
                rng,
                before_file.code,
                after_file.code,
                guess_language(after_file.path),
                "uniform",
            ).code
            later_changes = later_changes[:-1]
            if before_different := (wip_code != before_file.code):
                later_changes = later_changes.append(
                    Modified(
                        before_file,
                        FileTuple(after_file.path, wip_code),
                    )
                )
            if after_different := (wip_code != after_file.code):
                later_changes = later_changes.append(
                    Modified(
                        FileTuple(after_file.path, wip_code),
                        after_file,
                    )
                )
            if before_different and after_different:
                granular_path = after_file.path
                post_artifact(
                    {"simulated_granular_diffs": 1, "granular_path": granular_path}
                )
                if debug_mode:
                    original_diff = compute_file_diff(
                        before_file.to_file(), after_file.to_file()
                    )
                    sim_diff1 = compute_file_diff(
                        before_file.to_file(),
                        FileTuple(after_file.path, wip_code).to_file(),
                    )
                    sim_diff2 = compute_file_diff(
                        FileTuple(after_file.path, wip_code).to_file(),
                        after_file.to_file(),
                    )
                    post_artifact(
                        {
                            "original_diff": original_diff,
                            "sim_diff1": sim_diff1,
                            "sim_diff2": sim_diff2,
                        }
                    )
        return earlier_changes + later_changes


@dataclass
class WipRepoSampler:
    """A sampler to sample WIP repo states from a RepoChange."""

    wip_file_rate: float = 0.4
    """The probability of simulating a WIP file state.

    The WIP sampler traverses all changed files in a random order, and for each
    traversed file, either keep all its changes or drop a subset of changes to form
    a WIP file state. This parameter controls the frequency of performing each type
    of action.
    """

    def simulate_wip_repo_states(
        self, repo_change: RepoChange, rng: Random
    ) -> Iterable[RepoChange]:
        """Simulate a series of WIP repo states from the given repo change.

        All returned repo changes share the original repo_change's after state.

        This works by iterating through changed files one by one in a random order.
        For each file, either keep all its changes or drop a subset of changes to form
        a WIP file state, then yield a new WIP repo state using all the changes kept
        so far. Repeat this process for all changed files to yield a stream of
        WIP repo states.
        """
        all_changes = list(repo_change.changed_files)
        # shuffle the order of the changes, then apply them file by file
        rng.shuffle(all_changes)

        changes_so_far: PyrVector[Changed[FileTuple]] = pyr_vector()
        repo_state: PyrMap[Path, str] = repo_change.before_files
        for file_change in all_changes:
            if isinstance(file_change, (Added, Deleted)):
                # just apply these changes since we currently have no way to
                # meaningfully break down these changes
                changes_so_far = changes_so_far.append(file_change)
                if isinstance(file_change, Added):
                    path, code = file_change.after
                    repo_state = repo_state.set(path, code)
                else:
                    repo_state = repo_state.discard(file_change.before.path)
                wip_change = RepoChange(
                    repo_change.before_files, repo_state, changes_so_far
                )
                wip_change.check_changed_files()
                yield wip_change
                continue
            assert isinstance(file_change, Modified)
            before_path, before_code = file_change.before
            after_path, after_code = file_change.after
            if rng.random() < self.wip_file_rate:
                # simulate a near-finished WIP file state
                lang = guess_language(after_path)
                wip_code = simulate_wip_code(
                    rng,
                    before_code,
                    after_code,
                    lang,
                    wip_strategy="keep_most",
                ).code
            else:
                wip_code = after_code
            if wip_code != before_code or before_path != after_path:
                changes_so_far = changes_so_far.append(
                    Modified(
                        FileTuple(before_path, before_code),
                        FileTuple(after_path, wip_code),
                    )
                )
                repo_state = repo_state.discard(before_path)
                repo_state = repo_state.set(after_path, wip_code)
            wip_change = RepoChange(
                repo_change.before_files, repo_state, changes_so_far
            )
            wip_change.check_changed_files()
            yield wip_change


def edits_to_file_changes_grouped(
    before_files: Mapping[Path, str], edit_groups: Sequence[Sequence[SingleFileEdit]]
) -> list[list[Changed[FileTuple]]]:
    """Convert a sequence of (non-overlapping) edits to a list of file change groups.

    The edits will be applied to the before_files in order. Each edit in the input
    corresponds to a single file change in the output.
    """
    group_sizes = [len(group) for group in edit_groups]
    all_edits = [edit for group in edit_groups for edit in group]
    file_changes = edits_to_file_changes(before_files, all_edits)
    file_change_groups = list[list[Changed[FileTuple]]]()
    i = 0
    for group_size in group_sizes:
        file_change_groups.append(file_changes[i : i + group_size])
        i += group_size
    assert_eq(len(file_change_groups), len(edit_groups))
    return file_change_groups


def edits_to_file_changes(
    current_files: Mapping[Path, str], edits: Sequence[SingleFileEdit]
) -> list[Changed[FileTuple]]:
    """Convert a sequence of (non-overlapping) edits to a list of file changes.

    The edits will be applied to the current_files in order. Each edit in the input
    corresponds to a single file change in the output.
    """
    # stores all edits made to each file.
    applied_edits = defaultdict[FilePath, list[SingleFileEdit]](list)
    # first, create a list of file changes from the ordered edits
    after_files = dict(current_files)
    rename_map = dict[FilePath, FilePath]()
    file_changes = list[Changed[FileTuple]]()
    for edit in edits:
        if edit.before_path is None:
            # this is adding a new file
            assert edit.after_path is not None
            file_change = Added(FileTuple(Path(edit.after_path), edit.after_text))
        elif edit.after_path is None:
            # this is deleting a file
            assert edit.before_path is not None
            file_change = Deleted(FileTuple(Path(edit.before_path), edit.before_text))
        else:
            before_path = rename_map.get(edit.before_path, edit.before_path)
            prev_edits = [
                e
                for e in applied_edits[before_path]
                if e.before_stop <= edit.before_start
            ]
            offset = sum(len(e.after_text) - len(e.before_text) for e in prev_edits)
            before_text = after_files[Path(before_path)]
            start, stop = edit.before_start + offset, edit.before_stop + offset
            edit_before_text = before_text[start:stop]
            assert_str_eq(
                edit_before_text, edit.before_text, lambda: f"{edit.after_path=}"
            )
            after_text = before_text[:start] + edit.after_text + before_text[stop:]
            file_change = Modified(
                FileTuple(Path(before_path), before_text),
                FileTuple(Path(edit.after_path), after_text),
            )
            if before_path != edit.after_path:
                rename_map[before_path] = edit.after_path
                applied_edits[edit.after_path] = applied_edits[before_path]
            applied_edits[edit.after_path].append(edit)
            after_files[Path(edit.after_path)] = after_text
        file_changes.append(file_change)
    assert_eq(len(file_changes), len(edits))
    return file_changes


def file_change_to_edits(file_change: Changed[FileTuple]) -> list[SingleFileEdit]:
    match file_change:
        case Added(file):
            return [
                SingleFileEdit(
                    before_start=0,
                    before_text="",
                    after_start=0,
                    after_text=file.code,
                    before_path=None,
                    after_path=str(file.path),
                )
            ]
        case Deleted(file):
            return [
                SingleFileEdit(
                    before_start=0,
                    before_text=file.code,
                    after_start=0,
                    after_text="",
                    before_path=str(file.path),
                    after_path=None,
                )
            ]
        case Modified(before_file, after_file):
            hunks = compute_file_diff_hunks(
                before_file=before_file.to_file(),
                after_file=after_file.to_file(),
                use_smart_header=True,
                num_context_lines=3,
            )
            edits = list[SingleFileEdit]()
            for hunk in hunks:
                edits.append(
                    SingleFileEdit(
                        before_start=hunk.before_crange.start,
                        before_text=before_file.code[hunk.before_crange.to_slice()],
                        after_start=hunk.after_crange.start,
                        after_text=after_file.code[hunk.after_crange.to_slice()],
                        before_path=str(before_file.path),
                        after_path=str(after_file.path),
                    )
                )
            return edits
        case _:
            assert_never(file_change)


def get_repo_change_file_edits(
    repo_change: RepoChange,
) -> list[SingleFileEdit]:
    edits = list[SingleFileEdit]()
    for change in repo_change.changed_files:
        edits.extend(file_change_to_edits(change))
    return edits


def simulate_recency_diffs(
    repo_change: RepoChange,
    target_path: Path | None,
    rng: Random,
    max_recency_files: int = 3,
    max_recency_splits: int = 2,
) -> tuple[RepoChange, RepoChange]:
    """Randomly break repo_change into a big change followed by a small change.

    Args:
        repo_change: the original repo change.
        target_path: the path of the target file. Set this to None if the target file\
            is randomly picked.
        rng: random number generator.
        max_recency_files: the max number of recency files to simulate.
        max_recency_splits: the max number of recency splits of the last file.

    Returns:
        (earlier_change, recency_change) such that applying earlier_change and then
        recency_change will result in the original repo_change.
    """
    if max_recency_files < 1:
        raise ValueError(f"{max_recency_files=} must be >= 1")

    # intially, put all changes into vcs diff and leave recency diff empty
    early_changed_files = list(repo_change.changed_files)
    recency_changed_files = list[Modified[FileTuple]]()

    # sample a subset of changed files as the recency_changed_files
    candidate_ids = [
        i for i, c in enumerate(early_changed_files) if isinstance(c, Modified)
    ]
    n_recency_files = rng.randint(0, min(len(candidate_ids), max_recency_files))
    recency_ids = downsample_to(rng, candidate_ids, n_recency_files)

    # if the target file is one of the changed files, always put it as the last
    # recency change
    current_file_id = [
        i
        for i, c in enumerate(early_changed_files)
        if target_path and c.get_later().path == target_path
    ]
    if current_file_id:
        if current_file_id[0] in recency_ids:
            recency_ids.remove(current_file_id[0])
        recency_ids.append(current_file_id[0])

    recency_before_files = repo_change.after_files

    for i in recency_ids:
        # for each file picked, simulate a recency diff for it using the "keep_most"
        # WIP sampling strategy
        original_change = early_changed_files[i]
        assert isinstance(original_change, Modified)
        lang = guess_language(original_change.after.path)
        recency_before_code = simulate_wip_code(
            rng,
            original_change.before.code,
            original_change.after.code,
            lang,
            "keep_most",
        ).code
        if recency_before_code == original_change.after.code:
            # sampled an empty recency diff for this file
            continue
        recency_before_file = FileTuple(original_change.after.path, recency_before_code)
        recency_change = Modified(recency_before_file, original_change.after)
        recency_changed_files.append(recency_change)
        recency_before_files = recency_before_files.set(
            recency_before_file.path, recency_before_code
        )

    # filter out unchanged files
    early_changed_files = [
        c
        for c in early_changed_files
        if not (isinstance(c, Modified) and c.before == c.after)
    ]
    recency_changed_files = [
        c
        for c in recency_changed_files
        if not (isinstance(c, Modified) and c.before == c.after)
    ]

    # Optionally, recursively split the last file change into smaller changes
    if recency_changed_files and (recency_splits := rng.randint(0, max_recency_splits)):
        change = recency_changed_files.pop()
        path = change.after.path
        lang = guess_language(path)
        for _ in range(recency_splits):
            intermediate_state = simulate_wip_code(
                rng,
                change.before.code,
                change.after.code,
                lang,
                "keep_most",
            ).code
            if intermediate_state == change.before.code:
                # no split is performed, so skip
                continue
            recency_changed_files.append(
                Modified(
                    FileTuple(path, change.before.code),
                    FileTuple(path, intermediate_state),
                )
            )
            change = Modified(
                FileTuple(path, intermediate_state),
                FileTuple(path, change.after.code),
            )
        if change.before != change.after:
            recency_changed_files.append(change)

    early_change = RepoChange(
        repo_change.before_files,
        recency_before_files,
        pyr_vector(early_changed_files),
    )
    recency_change = RepoChange(
        recency_before_files,
        repo_change.after_files,
        pyr_vector(recency_changed_files),
    )
    return early_change, recency_change


def sample_change_order(
    rng: Random,
    diff: StrDiff,
    lang: LanguageId | None,
) -> list[int]:
    """Given a StrDiff, sample a random order among the changed spans.

    Returns the indexes of the diff spans in the order of the changes. Noop spans are
    not included in the result.
    """
    spans = diff.spans
    change_ids = [i for i, span in enumerate(spans) if not isinstance(span, NoopSpan)]
    if not change_ids:
        return []
    if len(change_ids) > 1000:
        logging.debug(
            f"Too many diff spans: {len(change_ids)}, falling back to random order."
        )
        rng.shuffle(change_ids)
        return change_ids

    # find out which changes are likely to be import changes
    if lang is not None:
        import_change_ids = find_import_change_spans(diff, lang)
    else:
        import_change_ids = set[int]()
    before_lmap = LineMap(diff.get_before())

    order = list[int]()
    remaining_ids = set(change_ids)
    # we initialize the location to an imaginary -1th location to bias toward picking
    # an early changes as the first change.
    current_id = -1
    while remaining_ids:
        elems_remain = list(remaining_ids)
        if current_id == -1:
            current_lrange = None
        else:
            current_lrange = before_lmap.crange_to_lrange(
                diff.span_ranges_in_before[current_id]
            )
        scores = list[float]()
        for next_i in elems_remain:
            score = _closeness(next_i - current_id)
            if next_i in import_change_ids:
                # make it much less likely to transfer to import changes unless
                # there aren't other choices
                if len(order) < 3:
                    # the first 3 changes should almost never be an import change
                    score /= 100
                else:
                    score /= 10
            span_lrange = before_lmap.crange_to_lrange(
                diff.span_ranges_in_before[next_i]
            )
            if current_lrange and span_lrange.overlaps(current_lrange):
                # make it much more likely to transfer to changes to the same line
                score *= 5
            scores.append(score)
        current_id = rng.choices(elems_remain, weights=scores)[0]
        remaining_ids.remove(current_id)
        order.append(current_id)
    return order


def find_import_change_lines(
    diff: StrDiff,
    lang: LanguageId,
) -> set[int]:
    """Return the set of line numbers that are likely to be import changes."""
    span_ids = find_import_change_spans(diff, lang)
    lmap = LineMap(diff.get_before())
    lines = set[int]()
    for span_id in span_ids:
        span_crange = diff.span_ranges_in_before[span_id]
        span_lrange = lmap.crange_to_lrange(span_crange)
        lines.update(span_lrange)
        lines.add(span_lrange.start)
    return set(lines)


def find_import_change_spans(
    diff: StrDiff,
    lang: LanguageId,
) -> set[int]:
    """Return changed span ids that are likely to contain import changes."""
    # find out which changes are likely to be import changes
    before_code = diff.get_before()
    after_code = diff.get_after()
    before_import_lines = set(find_all_import_lines(before_code, lang))
    after_import_lines = set(find_all_import_lines(after_code, lang))
    before_lmap = LineMap(before_code)
    after_lmap = LineMap(after_code)

    # maps each span id to whether it is an import change
    import_change_ids = set[int]()
    for i, span in enumerate(diff.spans):
        if isinstance(span, NoopSpan):
            continue
        before_crange = diff.span_ranges_in_before[i]
        before_lrange = before_lmap.crange_to_lrange(before_crange)
        if before_lrange.start in before_import_lines or any(
            line in before_import_lines for line in before_lrange
        ):
            import_change_ids.add(i)
            continue
        after_crange = diff.span_ranges_in_after[i]
        after_lrange = after_lmap.crange_to_lrange(after_crange)
        if after_lrange.start in after_import_lines or any(
            line in after_import_lines for line in after_lrange
        ):
            import_change_ids.add(i)
            continue
    return import_change_ids


@lru_cache(maxsize=256)
def build_character_diff(
    before: str,
    after: str,
) -> StrDiff:
    return precise_char_diff(before, after)


@dataclass
class SimulatedWipCode:
    code: str
    """The simulated WIP code."""

    next_change_range: CharRange | None
    """The character range of the simulated next change, relative to `code`."""

    next_change_span: ModSpan | None
    """The diff span of the simulated next change."""


def simulate_wip_code(
    rng: Random,
    prev_code: str,
    new_code: str,
    lang: LanguageId | None,
    wip_strategy: WipSamplingStrategy,
) -> SimulatedWipCode:
    """Simulate a WIP code between `prev_code` and `new_code`.

    Args:
        rng: random number generator.
        prev_code: the code before the change.
        new_code: the code after the change.
        lang: the language ID of the file. If not provided, certain heuristics such as\
            import delaying will not be used.
        wip_strategy: the strategy to decide how many changes to drop.
    """
    if prev_code == new_code:
        return SimulatedWipCode(new_code, None, None)

    diff = build_character_diff(prev_code, new_code)
    # Randomly choose how many context lines to include in diff hunks (None means don't group into hunks)
    hunk_context_lines = rng.choice([None, 0, 1, 2])
    if hunk_context_lines is not None:
        # If using hunks, group nearby changes together if they're within hunk_context_lines of each other
        diff = diff.group_into_hunks(hunk_context_lines)
    change_order = sample_change_order(rng, diff, lang)
    if not change_order:
        return SimulatedWipCode(new_code, None, None)

    # Randomly sample which changes to drop. Drop at least one change
    if wip_strategy == "uniform":
        n_drop = rng.randint(1, len(change_order))
    elif wip_strategy == "keep_most":
        # drop less stuffs than uniform
        drop_fraction = 0.4 * rng.random()
        n_drop = lerp_round(1, len(change_order), drop_fraction)
    else:
        assert_never(wip_strategy)
    kept_ids = change_order[:-n_drop]
    dropped_ids = set(change_order[-n_drop:])
    next_change_id = None
    if n_drop > 0:
        next_change_id = change_order[-n_drop]

    assert dropped_ids, f"{change_order=}, {diff=}"

    # TODO(jiayi) break big spans using smart chunker

    last_5_dropped = sorted(dropped_ids)[-5:]

    wip_file_parts = list[str]()
    next_change_range: CharRange | None = None
    next_change_span: ModSpan | None = None
    num_wip_spans = 0
    for span_id, op in enumerate(diff.spans):
        if span_id in dropped_ids:
            if span_id in last_5_dropped and rng.random() < 0.25:
                # for the last few spans, we may partially drop them
                wip_span = simulate_wip_span(rng, op)
                num_wip_spans += 1
            else:
                # otherwise, drop this span entirely
                wip_span = op.before
            if span_id == next_change_id and wip_span != op.after:
                # record this as the next change
                offset = sum(len(x) for x in wip_file_parts)
                next_change_range = CharRange(offset, offset + len(wip_span))
                next_change_span = ModSpan(wip_span, op.after)
            wip_file_parts.append(wip_span)
        else:
            wip_file_parts.append(op.after)

    info = {
        "simulate_wip_code": {
            "wip_strategy": wip_strategy,
            "hunk_context_lines": hunk_context_lines,
            "change_order": change_order,
            "n_drop": n_drop,
            "n_wip_spans": num_wip_spans,
            "kept_ids": kept_ids,
            "next_change_span": next_change_span,
        }
    }
    post_artifact(info)

    wip_code = "".join(wip_file_parts)
    return SimulatedWipCode(wip_code, next_change_range, next_change_span)


def simulate_wip_span(
    rng: Random,
    span: DiffSpan,
) -> str:
    """Simulate a WIP state between `span.before` and `span.after`.

    Note that here the DiffSpan is given by the diff algorithm and is the smallest unit
    that aligns some characters in the before version of the code to some characters
    in the after version. A full diff consists of a sequence of such DiffSpans.
    """
    if isinstance(span, (DeletedSpan, NoopSpan)):
        # for deletion and noop, we simply return the before version
        return span.before

    # do not split tiny changes
    if len(span.after.strip()) < 10 and len(span.before.strip()) < 10:
        return span.before

    debug_info = {}

    # Simulate a "split" case where we assume the user has replaced some portion
    # of the before span with some portion of the after span.
    # Sometimes we split by character, sometimes we split by line.
    if rng.random() < 0.5:
        before_divider = rng.randint(0, len(span.before))
    else:
        before_lines = span.before.splitlines(keepends=True)
        before_divider_lines = rng.randint(0, len(before_lines))
        before_divider = sum(len(line) for line in before_lines[:before_divider_lines])
    if rng.random() < 0.5:
        after_divider = rng.randint(0, len(span.after))
    else:
        after_lines = span.after.splitlines(keepends=True)
        after_divider_lines = rng.randint(0, len(after_lines))
        after_divider = sum(len(line) for line in after_lines[:after_divider_lines])
    if rng.random() < 0.5:
        before_kept = span.before[:before_divider]
    else:
        before_kept = span.before[before_divider:]
    debug_info["before_kept"] = shorten_str(before_kept)
    if rng.random() < 0.5:
        after_kept = span.after[:after_divider]
    else:
        after_kept = span.after[after_divider:]
    debug_info["after_kept"] = shorten_str(after_kept)
    if rng.random() < 0.5:
        wip_code = before_kept + after_kept
        debug_info["before_kept_first"] = True
    else:
        wip_code = after_kept + before_kept
        debug_info["before_kept_first"] = False

    if (
        (lb := get_line_break(span.after))
        and (get_line_break(wip_code) != lb)
        and rng.random() < 0.75
    ):
        # add line break to make it more realistic
        wip_code += lb
    if wip_code != span.before:
        debug_info["wip_code"] = shorten_str(wip_code)
        post_artifact({"simulate_wip_span": debug_info})

    return wip_code


def sample_geometric(rng: Random, max_n: int, continue_rate: float = 0.5) -> int:
    """Sample a number from a (capped) geometric distribution.

    Args:
        rng: random number generator.
        max_n: the max number to sample.
        continue_rate: the rate of continuing to sample.
    Returns:
        A number sampled from geometric distribution.
    """
    n = 0
    for _ in range(max_n):
        if rng.random() < continue_rate:
            n += 1
        else:
            break
    return n


def lerp(start: float, stop: float, t: float) -> float:
    """Linearly interpolate between start and stop at t."""
    return start + t * (stop - start)


def lerp_round(start: float, stop: float, t: float) -> int:
    """Linearly interpolate between start and stop at t, then round to nearest int."""
    return round(lerp(start, stop, t))


T = TypeVar("T")


def sample_near_sorted_order(rng: Random, xs: Sequence[T]) -> list[T]:
    """Randomly shuffle 0, 1, ..., n-1 such that they tend to be locally sorted.

    ## Example

    >>> rng = Random(42)
    >>> for _ in range(3):
    ...     print(sample_near_sorted_order(rng, range(10)))
    [3, 0, 1, 2, 7, 8, 9, 4, 5, 6]
    [0, 2, 1, 3, 6, 7, 8, 9, 5, 4]
    [5, 7, 6, 2, 9, 4, 0, 1, 8, 3]
    """

    if not xs:
        return []
    if len(xs) > 1000:
        logging.debug(
            "Too many elements to sample near sorted order, "
            "failling back to random shuffle."
        )
        path = list(xs)
        rng.shuffle(path)
        return path

    # sample a near sorted order using a random walk
    path = list[T]()
    remaining_ids = set(range(len(xs)))
    current_i = -1
    while remaining_ids:
        elems_remain = list(remaining_ids)
        scores = [_closeness(i - current_i) for i in elems_remain]
        current_i = rng.choices(elems_remain, weights=scores)[0]
        remaining_ids.remove(current_i)
        path.append(xs[current_i])
    return path


@cache
def _closeness(next_minus_current: int) -> float:
    """Measure the closeness of a location w.r.t. the current location."""
    # closer locations should be score higher
    score = 1 / abs(next_minus_current)
    if next_minus_current > 0:
        # forward direction should be scored higher
        score *= 2
    return score

"""Implements heuristic to identify and extract upcoming code edits from user events.

This module analyzes sequences of user editing events to determine the next meaningful
code change (hunk) that will be made.
"""

from dataclasses import dataclass, field
from datetime import timed<PERSON><PERSON>
from typing import AbstractSet, Iterable, Mapping, NamedTuple, Sequence

from base.caching.lru_cache import lru_cache
from base.datasets.hindsight_next_edit import NextEditIntermediateType
from base.datasets.hindsight_next_edit_intermediate_dataset import GroundTruthEdit
from base.datasets.user_event import TextEditEvent, merge_adjacent_edit_stream
from base.diff_utils.str_diff import NoopSpan, StrDiff, precise_line_diff
from base.ranges.line_map import LineMap
from base.ranges.range_types import Char<PERSON><PERSON><PERSON>, LineRange

FilePath = str
FileText = str


@dataclass
class HeuristicFailed(Exception):
    category: str
    details: list[str]


@dataclass
class HeuristicArgs:
    max_active_hunks: int = 3
    """The max number of active hunks in the edit sequence.

    When there are more than this many active hunks, the first hunk in the sequence
    will be considered as stable, and we extract the ground truth edit from it.

    Why 3 is a good value for this parameter: we need at least two active hunks to
    support a common editing pattern where the user jumps between two pieces of code.
    And sometimes, when they edit another piece of code, they also update the import
    section, resulting in 3 active hunks.
    """

    hunk_context_lines: int = 5
    """The number of context lines to include in each hunk."""


class _HunkRanges(NamedTuple):
    path: FilePath
    before_range: CharRange
    after_range: CharRange


@dataclass
class NextHunkHeuristic:
    """This heuristic works by tracking a set of active hunks based on user edits.

    An "active hunk" is a contiguous region of code changes that has been modified by
    the user and remains part of the current editing sequence. The sequence of active
    hunks represents the order in which different parts of the code were modified.

    Key concepts:
    - The first hunk in the sequence is special - if it's touched again, all other hunks
      are removed from the sequence (assuming the user has returned to their primary task)
    - A hunk becomes "stable" (ready to be extracted as ground truth) when the sequence
      grows beyond max_active_hunks, indicating the user has moved on to editing other
      parts of the code

    The algorithm works by:
    1. Walking through user edits one by one
    2. Computing a diff between current codebase state and request time snapshot
    3. Ordering diff hunks into an "active hunk sequence" based on edit event order
    4. When there are more than max_active_hunks active hunks, considering the first
       hunk stable and extracting it as ground truth
    5. If a new edit touches the first hunk, resetting the sequence to just that hunk
    """

    args: HeuristicArgs = field(default_factory=HeuristicArgs)

    def __post_init__(self):
        self._cached_line_diff = lru_cache(maxsize=512)(precise_line_diff)
        self._cached_line_map = lru_cache(maxsize=512)(LineMap)

    def get_gold_edits(
        self,
        datum: NextEditIntermediateType,
        analyzer: "HeuristicStateRecorder | None" = None,
    ) -> GroundTruthEdit:
        files_at_request_time: Mapping[FilePath, FileText] = {
            file.path: file.contents for file in datum.files_for_events.values()
        }
        modified_files: dict[FilePath, FileText] = {}
        line_diffs: dict[FilePath, StrDiff] = {}
        # Records future edits' ranges mapped to the request time, with context lines
        # extended. This is used to figure out the order among hunks.
        # Simultaneous edits are grouped together.
        edit_ranges_at_request_time: list[list[tuple[FilePath, CharRange]]] = []

        def record_edit_range(edit: TextEditEvent, hunk_context_lines: int) -> None:
            """Record the edit's range mapped to the request time, with context lines
            extended."""
            simultaneous_edits = list[tuple[FilePath, CharRange]]()
            for change in edit.content_changes:
                init_text = files_at_request_time[edit.file_path]
                if edit.file_path in modified_files:
                    current_text = modified_files.get(edit.file_path, init_text)
                    diff = self._cached_line_diff(init_text, current_text)
                    before_crange = diff.after_range_to_before(change.crange)
                else:
                    before_crange = change.crange
                lmap = self._cached_line_map(init_text)
                try:
                    before_lrange = lmap.crange_to_lrange(before_crange)
                except IndexError as e:
                    raise HeuristicFailed(
                        "Invalid before_crange in record_edit_range",
                        [f"IndexedError={e}, {before_crange=}, {edit=}"],
                    )
                extended_lrange = LineRange(
                    max(0, before_lrange.start - hunk_context_lines),
                    min(
                        lmap.size_lines(),
                        before_lrange.stop + hunk_context_lines,
                    ),
                )
                extended_crange = lmap.lrange_to_crange(extended_lrange)
                simultaneous_edits.append((edit.file_path, extended_crange))
            edit_ranges_at_request_time.append(simultaneous_edits)

        def extract_gold_edit(hunk: _HunkRanges) -> GroundTruthEdit:
            path, before_crange, after_crange = hunk
            before_file = files_at_request_time[path]
            after_file = modified_files[path]
            before_lrange = self._cached_line_map(before_file).crange_to_lrange(
                before_crange
            )
            after_lrange = self._cached_line_map(after_file).crange_to_lrange(
                after_crange
            )
            gold_edit = GroundTruthEdit(
                path=path,
                before_crange=before_crange,
                after_crange=after_crange,
                before_lrange=before_lrange,
                after_lrange=after_lrange,
                before_text=before_file[before_crange.to_slice()],
                after_text=after_file[after_crange.to_slice()],
            )
            return gold_edit

        def report_state(
            last_edit: TextEditEvent | None,
            edit_apply_error: ValueError | None,
            hunks_so_far: set[_HunkRanges],
            hunk_sequence: list[_HunkRanges],
        ):
            if analyzer is None:
                return

            # Make shallow copies of the fields because we will mutate them later.
            # We never mutate the values in these containers, so a shallow copy is safe.
            state = NextHunkHeuristic.InternalState(
                modified_files=modified_files.copy(),
                line_diffs=line_diffs.copy(),
                edit_ranges_at_request_time=edit_ranges_at_request_time.copy(),
                last_edit=last_edit,
                edit_apply_error=edit_apply_error,
                hunks_so_far=hunks_so_far.copy(),
                hunk_sequence=hunk_sequence.copy(),
            )
            analyzer.take_snapshot(state)

        if not datum.future_events:
            raise HeuristicFailed("No future events found", [])
        hunk_sequence = list[_HunkRanges]()
        future_events = merge_adjacent_edit_stream(datum.future_events)
        report_state(None, None, set(), hunk_sequence)
        for t, edit in enumerate(future_events):
            if not edit.after_changes_hash:
                # we cannot safely use the data without hash check the edits
                raise HeuristicFailed(
                    "after_changes_hash is missing", [f"{edit.user_id=}"]
                )
            try:
                init_text = files_at_request_time[edit.file_path]
                current_text = modified_files.get(edit.file_path, init_text)
                new_text, apply_error = edit.apply_to_text(current_text)
                if new_text == current_text:
                    # no-op edit, skipping
                    continue
                if (
                    t == 0
                    and apply_error
                    and abs(edit.time - datum.request.timestamp)
                    < timedelta(seconds=0.1)
                ):
                    # edit is too close to the request time, try skip it instead
                    continue
                file_range = CharRange(0, len(current_text))
                if all(file_range.contains(c.crange) for c in edit.content_changes):
                    record_edit_range(edit, self.args.hunk_context_lines)
                else:
                    apply_error = ValueError(
                        f"Edit range is not contained in the file. {file_range=}"
                    )
                modified_files[edit.file_path] = new_text
                line_diffs[edit.file_path] = self._cached_line_diff(init_text, new_text)
                hunks = set(
                    _get_modified_hunks(line_diffs, self.args.hunk_context_lines)
                )
                hunk_sequence = _find_active_hunks(hunks, edit_ranges_at_request_time)
                report_state(edit, apply_error, hunks, hunk_sequence)
                # we first report the state before checking for errors to aid debugging
                if apply_error is not None:
                    raise HeuristicFailed(
                        "Failed to apply edit",
                        [f"{apply_error=}, {edit=}"],
                    )
                if len(hunk_sequence) <= self.args.max_active_hunks:
                    # the first hunk is not stable yet, keep waiting for more edits
                    continue
                # the first hunk is now stable, let's extract the gold edits from it
                gt = extract_gold_edit(hunk_sequence[0])
                if analyzer is not None:
                    analyzer.add_ground_truth(gt, is_stable=True)
                return gt
            except HeuristicFailed as e:
                e.details.append(f"edit id={t}")
                raise e
        if analyzer and hunk_sequence:
            # save the first hunk as if it's the ground truth for debugging
            gt = extract_gold_edit(hunk_sequence[0])
            analyzer.add_ground_truth(gt, is_stable=False)
        raise HeuristicFailed(
            "No stable hunk found",
            [f"hunk_sequence_length={len(hunk_sequence)}"],
        )

    @dataclass(frozen=True)
    class InternalState:
        """A snapshot of the internal state of `self.get_gold_edits`."""

        modified_files: Mapping[FilePath, FileText]
        line_diffs: Mapping[FilePath, StrDiff]
        edit_ranges_at_request_time: Sequence[Sequence[tuple[FilePath, CharRange]]]
        last_edit: TextEditEvent | None
        edit_apply_error: ValueError | None
        hunks_so_far: AbstractSet[_HunkRanges]
        hunk_sequence: Sequence[_HunkRanges]


def _get_modified_hunks(
    line_diffs: Mapping[FilePath, StrDiff], hunk_context_lines: int
) -> Iterable[_HunkRanges]:
    """Return the modified hunks' before and after ranges."""
    for path, diff in line_diffs.items():
        diff = diff.group_into_hunks(hunk_context_lines)
        for before_range, after_range, span in zip(
            diff.span_ranges_in_before, diff.span_ranges_in_after, diff.spans
        ):
            if not isinstance(span, NoopSpan):
                yield _HunkRanges(path, before_range, after_range)


def _find_active_hunks(
    hunks: set[_HunkRanges],
    edit_ranges_at_request_time: Sequence[Sequence[tuple[FilePath, CharRange]]],
) -> list[_HunkRanges]:
    """Return the active hunks ordered by their last modification time.

    An active hunk is a code change that's part of the current editing sequence.
    The sequence is maintained according to these rules:
    1. When a hunk is first touched, it's added to the sequence
    2. If the first hunk in the sequence is touched again, all other hunks are removed
       (assuming the user has returned to their primary task)
    3. Edits that touch multiple hunks simultaneously are ignored as they likely
       represent automated changes rather than user edits

    Args:
        hunks: Set of all diff hunks found between current and request time
        edit_ranges_at_request_time: Sequence of edit ranges in chronological order,
            where simultaneous edits are grouped together

    Returns:
        List of hunks in order of modification, representing the active editing sequence
    """
    result = list[_HunkRanges]()
    if not hunks:
        return result
    first_modified: _HunkRanges | None = None
    result_set: set[_HunkRanges] = set()
    # Process edit groups chronologically
    for edit_group in edit_ranges_at_request_time:
        # Find all hunks touched by this edit group
        touched_hunks = {
            hunk
            for hunk in hunks
            if any(
                edit[0] == hunk.path and edit[1].touches(hunk.before_range)
                for edit in edit_group
            )
        }
        if len(touched_hunks) != 1:
            # Ignore edits touching multiple hunks as they're likely automated changes
            continue
        if first_modified and first_modified in touched_hunks:
            # User returned to editing the first hunk - reset sequence to just this hunk
            result = [first_modified]
            result_set = {first_modified}
            continue
        # Record newly touched hunks in sequence
        if first_modified is None:
            assert len(touched_hunks) == 1
            first_modified = next(iter(touched_hunks))
        new_hunks = touched_hunks - result_set
        result.extend(new_hunks)
        result_set.update(new_hunks)

    return result


@dataclass
class HeuristicStateRecorder:
    """Records of the internal state of the heuristic for debugging."""

    args: HeuristicArgs
    datum: NextEditIntermediateType

    def __post_init__(self):
        self.snapshots = list[NextHunkHeuristic.InternalState]()
        self.ground_truths: list[GroundTruthEdit] = []
        self.ground_truths_are_stable: list[bool] = []

    def take_snapshot(self, state: NextHunkHeuristic.InternalState):
        """Take a snapshot of the current state for analysis.

        This can be used as the callback for `GroundTruthHeuristic.get_gold_edits`.
        """
        self.snapshots.append(state)

    def add_ground_truth(self, ground_truth: GroundTruthEdit, is_stable: bool):
        """Add a ground truth edit to the analyzer."""
        self.ground_truths.append(ground_truth)
        self.ground_truths_are_stable.append(is_stable)


def fix_next_edit_datum_events(datum: NextEditIntermediateType) -> None:
    """Drop events that cannot be applied."""
    files_at_request_time = {
        file.path: file.contents for file in datum.files_for_events.values()
    }

    def should_keep_edit(edit: TextEditEvent) -> bool:
        """Whether to keep the edit based on its path."""
        if edit.file_path not in files_at_request_time:
            # likely edits to untracked files, skipping
            return False
        if edit.file_path.endswith(".ipynb"):
            # we cannot handle notebook edits correctly yet
            return False
        return True

    datum.future_events = list(filter(should_keep_edit, datum.future_events))


def dedup_next_edit_data(
    data: Iterable[NextEditIntermediateType], num_events_to_check: int = 5
) -> Iterable[NextEditIntermediateType]:
    """Deduplicate next edit data by checking the next few events."""
    last_events: Sequence[TextEditEvent] | None = None
    for datum in data:
        if last_events and last_events == datum.future_events[:num_events_to_check]:
            # consider this datum a duplicate
            continue
        last_events = datum.future_events[:num_events_to_check]
        yield datum

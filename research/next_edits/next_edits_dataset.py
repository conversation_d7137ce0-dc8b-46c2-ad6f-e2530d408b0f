"""Describes the schema for a next edits dataset.

Note that this schema is distinct from the next edit location prompt input. It is a
sufficient representation of the input and output to the task, while the prompt can be
more complex than the input and includes assumptions on e.g. how many lines of context
should be used.
"""

from collections.abc import Sequence
from dataclasses import dataclass, field
from datetime import datetime
from functools import cached_property
from pathlib import Path
from typing import NamedTuple
from base.blob_names.python.blob_names import FilePath
from base.ranges.range_types import CharRange

import unidiff
from dataclasses_json import DataClassJsonMixin

from base.diff_utils.edit_events import SingleFileEdit, SquashableEdits
from base.prompt_format_next_edit.gen_prompt_formatter import EditGenOutput
from research.core import diff_utils
from research.utils.repo_change_utils import (
    CommitMeta,
    RepoChange,
    get_commit_history,
    iterate_repo_history,
)

__reexported__ = [CommitMeta]


@dataclass(frozen=True)
class PRMeta:
    repo_name: str
    """The name of the repo."""

    pr_number: int
    """The PR number if it exists."""

    title: str
    """If non-empty, the title of the PR."""

    body: str = ""
    """If non-empty, the body of the PR."""

    def __str__(self):
        return f"{self.repo_name}/PR#{self.pr_number}"


class RepoChangeWithMeta(NamedTuple):
    """A repo change with additional metadata."""

    repo_change: RepoChange
    commit_meta: CommitMeta
    pr_meta: PRMeta | None


@dataclass
class GroupedEdits:
    description: str
    changes: Sequence[SingleFileEdit]


@dataclass(frozen=True)
class OrderedRepoChange:
    """A repo change with metadata and the order among the changes."""

    repo_change: RepoChange
    commit_meta: CommitMeta
    pr_meta: PRMeta | None
    edit_groups: Sequence[GroupedEdits]


@dataclass
class Datum(DataClassJsonMixin):
    """Represents a data point for the next edit generation and localization tasks.

    - When set, the commit_meta.sha defines the base / "past" state of the repository.
    - The user makes the changes in `past_diff` to bring the repository to the current
      "work in progress" (WIP) state.
    - Finally, the `future_diff` represents the changes needed to bring the repository
      to its desired "future" state.
    """

    id: str
    """A unique ID determining this datum, e.g. the WIP state's sha."""

    instruction: str
    """The instruction / PR title for this change. Can be empty."""

    past_to_wip_diff: str
    """The diff from the past state to the current WIP state."""

    wip_to_future_diff: str
    """The diff from the current WIP state to the desired future state."""

    wip_files: list[diff_utils.File] = field(default_factory=list)
    """The documents in the current WIP state of the repo."""

    commit_meta: CommitMeta | None = None
    """Metadata of the commit that the datum was created from."""

    group_id: str = ""
    """If non-empty, the group ID of the datum.

    Multiple examples share a group ID when created from the same (final) commit.
    This metadata is useful to group statistics, etc.
    """

    group_sequence_id: int = 0
    """If non-zero, the sequence ID of the datum in its group."""

    pr_meta: PRMeta | None = None
    """Metadata of the PR that the datum was created from."""

    @cached_property
    def wip_repo(self):
        """Gets the current WIP state of the repository."""
        return diff_utils.Repository(self.wip_files)

    @cached_property
    def past_repo(self):
        """Gets past state of the repository."""
        return diff_utils.apply_diff(
            self.wip_repo, unidiff.PatchSet(self.past_to_wip_diff), reverse=True
        )

    @cached_property
    def future_repo(self):
        """Gets the future state of the repository."""
        return diff_utils.apply_diff(
            self.wip_repo, unidiff.PatchSet(self.wip_to_future_diff)
        )


@dataclass
class GroupedDatum(DataClassJsonMixin):
    data: list[Datum]
    """The data in the group."""

    group_id: str
    """The group ID of the data."""

    def past_repo(self):
        """Gets past state of the repository."""
        return self.data[0].past_repo

    def future_repo(self):
        """Gets the future state of the repository."""
        return self.data[-1].future_repo


def repo_changes_from_commits(
    repo_dir: Path, max_commits: int, last_commit: str = "HEAD", max_workers: int = 8
) -> list[RepoChangeWithMeta]:
    """Read repo changes from a git repo's latest commits."""
    commit_history = get_commit_history(repo_dir, max_commits, commit_id=last_commit)
    repo_changes = iterate_repo_history(
        repo_dir, commit_history, max_workers=max_workers
    )
    return [
        RepoChangeWithMeta(repo_change, commit_meta, None)
        for repo_change, commit_meta in zip(repo_changes, commit_history[1:])
    ]


@dataclass(frozen=True)
class HindsightOrigin:
    """Where the Hindsight data came from."""

    request_id: str
    """The request ID of the Hindsight request."""

    session_id: str
    """The session ID of the Hindsight request."""

    tenant: str
    """The tenant name of the Hindsight request."""

    request_time: datetime
    """The front end time of the Hindsight request."""


@dataclass
class HindsightEditGenProblem:
    """An edit generation problem obtained from Hindsight data."""

    current_path: FilePath
    """The file path of the target file."""

    current_files: dict[FilePath, str]
    """Map paths to up-to-date file contents."""

    squashable_edits: SquashableEdits
    """The recent edits made by the user."""

    edit_region: CharRange
    """The character range of the selected edit region in `current_code`."""

    ground_truth: EditGenOutput
    """The expected output."""

    origin: HindsightOrigin
    """Where this problem was obtained from."""

    # Below are aliases to make it more compatible with EditGenProblem.

    @property
    def current_code(self) -> str:
        return self.current_files[self.current_path]

    @property
    def selected_code(self) -> str:
        return self.current_code[self.edit_region.to_slice()]

    @property
    def prefix(self) -> str:
        return self.current_code[: self.edit_region.start]

    @property
    def suffix(self) -> str:
        return self.current_code[self.edit_region.stop :]

    @property
    def instruction(self) -> str:
        return ""  # not available from Hindsight data

    @property
    def output(self) -> EditGenOutput:
        return self.ground_truth

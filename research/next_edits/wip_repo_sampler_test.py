from pathlib import Path
from random import Random
import subprocess
import pytest
from base.diff_utils.changes import Modified
from base.static_analysis.common import join_list
from base.test_utils.testing_utils import error_context
from research.core.constants import AUGMENT_ROOT
from research.core.str_diff import DiffAlgName, build_str_diff, print_diff
from research.next_edits.wip_repo_sampler import (
    edits_to_file_changes,
    edits_to_file_changes_grouped,
    find_import_change_lines,
    get_repo_change_file_edits,
    simulate_recency_diffs,
)
from research.utils.repo_change_utils import (
    RepoChange,
    get_commit_history,
    iterate_repo_history,
    squash_file_changes,
)
from research.utils.tests.test_repo_change_utils import is_source_file


EXAMPLE_BEFORE = """\
from dataclasses import dataclass, field
import tree_sitter as ts  # some comments
from base.ranges import ByteRange as BRange, LineRange as LRange

from base.static_analysis.common import (
    LanguageID,
    replace_str,
    show_str_diff,
)

foo(1 + x)

# from research.next_edits.wip_repo_sampler import sample_near_sorted_order
"""

EXAMPLE_AFTER = """\
from dataclasses import dataclass, field
from base.ranges import ByteRange as BRange, LineRange as LRange
import tree_sitter as ts  # some comments

from base.static_analysis.common import (
    LanguageID,
    show_str_diff,
)

foo(x * 2) # not an import change

from research.next_edits.wip_repo_sampler import sample_near_sorted_order
"""


@pytest.mark.parametrize("algorithm", ["linediff", "line_dmp", "precise_chardiff"])
def test_find_import_change_lines(algorithm: DiffAlgName):
    """Simple test cases for find_import_change_lines.

    Note that we have more thorough tests for `find_all_import_lines`, so
    we don't exhaustively test different import syntaxes here.
    """

    diff = build_str_diff(EXAMPLE_BEFORE, EXAMPLE_AFTER, algorithm=algorithm)
    print_diff(diff)
    import_lines = find_import_change_lines(diff, "Python")
    assert import_lines == {1, 2, 6, 12}


@pytest.fixture
def test_repo_fixture(tmp_path: Path):
    test_bundle_path = AUGMENT_ROOT / "research/utils/tests/testdata/test_repo.bundle"
    # git clone from the bundle
    subprocess.run(
        f"git clone -b master {test_bundle_path} test_repo",
        shell=True,
        check=True,
        cwd=tmp_path,
    )
    yield tmp_path / "test_repo"


def test_simulate_recency_diffs_on_repo(test_repo_fixture: Path):
    # If we squash the two changes from simulate_recency_diffs, we should get
    # the original repo_change back.

    history = get_commit_history(test_repo_fixture, commit_id="HEAD")
    repo_changes = list(
        iterate_repo_history(test_repo_fixture, history, is_source_file, silent=True)
    )

    rng = Random(42)

    for repo_change in repo_changes:
        for _ in range(100):
            change1, change2 = simulate_recency_diffs(
                repo_change, Path("not needed"), rng
            )
            squashed_file_changes = squash_file_changes(
                change1.changed_files + change2.changed_files
            )
            assert set(squashed_file_changes) == set(repo_change.changed_files)
            for cf in change2.changed_files:
                assert isinstance(cf, Modified)
                assert cf.before != cf.after
            squashed = change1.squash(change2)
            assert squashed.before_files == repo_change.before_files
            assert squashed.after_files == repo_change.after_files
            assert set(squashed.changed_files) == set(repo_change.changed_files)


def test_edits_to_file_changes(test_repo_fixture: Path):
    history = get_commit_history(test_repo_fixture, commit_id="HEAD")
    repo_changes = list(
        iterate_repo_history(test_repo_fixture, history, is_source_file, silent=True)
    )

    rng = Random(42)

    for repo_change in repo_changes:
        edits = get_repo_change_file_edits(repo_change)
        for _ in range(20):
            if not edits:
                continue
            rng.shuffle(edits)
            file_changes = edits_to_file_changes(repo_change.before_files, edits)
            reconstructed = RepoChange.from_before_and_changes(
                repo_change.before_files, file_changes
            ).after_files
            with error_context(f"{edits=}\n{file_changes=}"):
                assert dict(reconstructed) == dict(repo_change.after_files)

            split_point = rng.randint(1, len(edits))
            edit_groups = [edits[:split_point], edits[split_point:]]
            file_change_groups = edits_to_file_changes_grouped(
                repo_change.before_files, edit_groups
            )
            assert len(file_change_groups) == len(edit_groups)
            reconstructed2 = RepoChange.from_before_and_changes(
                repo_change.before_files, join_list(file_change_groups)
            ).after_files
            with error_context(f"{edit_groups=}\n{file_change_groups=}"):
                assert dict(reconstructed2) == dict(repo_change.after_files)

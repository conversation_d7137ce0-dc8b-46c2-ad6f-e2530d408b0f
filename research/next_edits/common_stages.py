"""Common data pipeline stages for the next edit project.

See `edit_gen_stages` and `edit_localization_stages` for model-specific stages.
"""

import json
import logging
import time
from collections.abc import <PERSON><PERSON><PERSON>, Mapping, Sequence
from dataclasses import dataclass
from pathlib import Path

import pandas as pd

from base.caching.lru_cache import lru_cache
from research.core.types import Chunk, Document, DocumentId
from research.next_edits.next_edits_dataset import PRMeta
from research.retrieval import chunking_functions
from research.retrieval.retrieval_database import RetrievalDatabase
from research.retrieval.types import Chunker, DocumentIndex

logger = logging.getLogger(__name__)


@dataclass
class Timer:
    start_time_s: float | None = None
    """The start time of the timer if started."""

    cumulative_time_s: float = 0.0
    """The cumulative elapsed time of the timer."""

    def start(self):
        """Start the timer if it hasn't already been started."""
        if self.start_time_s is None:
            self.start_time_s = time.time()

    def pause(self):
        """Pause the timer if it is running."""
        if self.start_time_s is not None:
            self.cumulative_time_s += time.time() - self.start_time_s
            self.start_time_s = None

    @property
    def elapsed_s(self):
        """Get the elapsed time of the timer."""
        if self.start_time_s is not None:
            return self.cumulative_time_s + time.time() - self.start_time_s
        else:
            return self.cumulative_time_s

    def __enter__(self):
        self.start()
        return self

    def __exit__(self, *args):
        self.pause()


@lru_cache()
def repo_timer(repo_name: str) -> Timer:
    """A per-process per-repo timer."""
    return Timer()


@lru_cache()
def load_instructions(instructions_path: Path | str) -> Mapping[PRMeta, list[str]]:  # noqa: F821
    """Load generated instructions."""
    ret = {}

    for shard in Path(instructions_path).glob("*.parquet"):
        # There are some empty shards that can't be loaded when filtering columns,
        # so we manually exclude them here.
        if shard.stat().st_size < 1024:
            continue
        for dct in pd.read_parquet(
            shard, columns=["repo_name", "number", "title", "body", "instructions"]
        ).to_dict(orient="records"):
            pr_meta = PRMeta(
                repo_name=dct["repo_name"],
                pr_number=dct["number"],
                title=dct["title"],
                body=dct["body"],
            )
            ret[pr_meta] = [
                i
                for instructions in dct["instructions"].values()
                if instructions is not None
                for i in instructions
            ]

    return ret


class ChunkCache:
    """Cache document chunking results."""

    def __init__(self, chunker: Chunker, max_chunks_per_doc: int = -1):
        self.chunker = chunker
        self._doc_to_chunks_cache: dict[DocumentId, list[Chunk]] = {}
        self.max_chunks_per_doc = max_chunks_per_doc

    def get_chunks(self, doc: Document) -> list[Chunk]:
        if doc.id not in self._doc_to_chunks_cache:
            chunks = self.chunker.split_into_chunks(doc) or []
            if self.max_chunks_per_doc > 0 and len(chunks) > self.max_chunks_per_doc:
                logger.info(
                    f"Truncating chunks from {doc.id}: {len(chunks)=} > {self.max_chunks_per_doc=}."
                )
                chunks = chunks[: self.max_chunks_per_doc]
            self._doc_to_chunks_cache[doc.id] = chunks
        return self._doc_to_chunks_cache[doc.id]


# Create one per process.
@lru_cache()
def get_chunk_cache(
    chunker_options: Sequence[tuple[str, Hashable]], max_chunks_per_doc: int = -1
) -> ChunkCache:
    chunker_config = dict(chunker_options)
    cls_name = chunker_config.pop("name")
    return ChunkCache(
        chunking_functions.get_chunker(cls_name, **chunker_config), max_chunks_per_doc
    )


@lru_cache()
def create_retriever(config_json: str) -> RetrievalDatabase:
    """Create a retriever from a config JSON."""
    from research.eval.harness import factories

    retriever = factories.create_retriever(json.loads(config_json))
    assert isinstance(retriever, RetrievalDatabase)

    return retriever

# Vanguard For Research

## License Filter Task

Please check [this](https://www.notion.so/License-Pipeline-Overview-176bba10175a804091d5c6b94522ab96) notion page for an overview of the pipeline.

To directly run the pipeline end-to-end for completion requests, use the following command:
```
python research/vanguard/license_filter/pipeline.py \
--event_type completion_host_request \
--env prod \
--date_from 2025-01-01 \
--date_to 2025-02-01 \
--commit (optional; drop this flag in testing or adhoc runs)
```

For chat requests, use `--event_type chat_host_request` instead.

You can also run the pipeline step by step as follows:

### Step 1: Get a fixed set of request IDs to work with

Retrieve and save a list of request IDs as the source of ground truth for downstream steps.

For example, we can get a set of completion request IDs issued by the plugin by running:
```
python research/vanguard/license_filter/get_request_ids_from_search_db.py \
--table_name session_event \
--event_type completion_request_id_issued \
--tenants i0-vanguard0,i0-vanguard1,i0-vanguard2,i0-vanguard3,i0-vanguard4,i0-vanguard5,i0-vanguard6,i0-vanguard7,i1-vanguard0,i1-vanguard1,i1-vanguard2,i1-vanguard3,i1-vanguard4,i1-vanguard5,i1-vanguard6,i1-vanguard7 \
--date_from 2025-01-01 \
--date_to 2025-02-01
```

After running the above script, the resulting request IDs will be stored in 2 places:
1. A BigQuery table `augment-research-gsc.vanguard.request_ids` (internal use only for now)
2. A parquet file under the directory `gs://gcp-us1-spark-data/shared/vanguard/license_filter/tmp/request_ids`
Note that the parquet file on GCS is the one used in the rest of the pipeline.

Alternatively, we can get a list of selected request IDs from other event types/tables or directly from researchers' data pipelines, then save them as parquet under the same GCS location.

### Step 2: Get license information for selected blobs

Retrieve blob IDs for the selected request IDs, then detect license information for each blob:
```
python research/vanguard/license_filter/get_blob_licenses.py \
--event_type completion_host_request \
--field_to_blobs blobs \
--tenants i0-vanguard0 \
--date_from 2025-01-01 \
--date_to 2025-02-01 \
--workers 64 \
--cores 2 \
--ram 32
```

To save computation time, we first filter out blobs by their file names and only run license detection on blobs whose file name matches a list of common license file names.

We leverage [go-license-detector](https://github.com/go-enry/go-license-detector) to find the license information for the remaining blobs. The potential license files will be individually downloaded from GCS and then processed by the `license-detector` CLI.

The results will be saved as parquet under the directory `gs://gcp-us1-spark-data/shared/vanguard/license_filter/tmp/blob_licenses`. The `license_info` column contains the confidence score of each detected license and is saved in serialized JSON format, e.g.
```
[
    {
        "project": "/tmp/tmpahlxs2zd",
        "matches": [
            { "license": "MIT", "confidence": 0.94578314, "file": "LICENSE" },
            { "license": "MIT-0", "confidence": 0.82208586, "file": "LICENSE" }
        ]
    }
]
```

### Step 3: Determine permissiveness for each request ID

Determine permissiveness for each request ID by checking if any of the blobs used in the request contains a non-permissive license:
```
python research/vanguard/license_filter/get_request_permits.py \
--confidence_threshold 0.7 \
--workers 8 \
--cores 4 \
--ram 32
```

We will first compute permissiveness for each blob. A given blob is considered containing a certain license if the confidence score of that license in the detection result exceeds the provided threshold. A blob is considered permissive if all contained licenses are permissive. The list of permissive licenses is stored in the BigQuery table `augment-research-gsc.core.permissive_licenses`.

We then compute permissiveness for each request ID by checking if any of the blobs used in the request is non-permissive. A request is considered permissive if all of its blobs are permissive. The results will be saved as parquet under the directory `gs://gcp-us1-spark-data/shared/vanguard/license_filter/tmp/request_permits`.

### Step 4: Validate and commit data to an organized output directory

Validate the results saved in the tmp directory and handle failures. Then commit `blob_licenses` data and `request_permits` data to a more permanent location for future consumption:
```
python research/vanguard/license_filter/commit_data.py \
--env prod \
--event_type completion_host_request \
--date_from 2025-01-01 \
--date_to 2025-02-01
```

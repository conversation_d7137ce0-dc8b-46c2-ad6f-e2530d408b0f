"""Vanguard License Pipeline

Example usage:
python research/vanguard/license_filter/pipeline.py \
--event_type completion_host_request \
--env prod \
--date_from 2025-01-01 \
--date_to 2025-02-01

Note that the from/to dates are inclusive/exclusive respectively.
"""

import argparse
import time
from contextlib import contextmanager
from dataclasses import dataclass

from base.datasets.tenants import DATASET_TENANTS
from research.vanguard.license_filter import (
    commit_data,
    get_blob_licenses,
    get_request_ids_from_search_db,
    get_request_permits,
    update_rids,
)
from research.vanguard.license_filter.utils import (
    SLACK_MSG_END,
    SLACK_MSG_START,
    QueryParams,
)

# Default confidence threshold used for license detection
DEFAULT_CONFIDENCE_THRESHOLD = 0.7


@dataclass
class PipelineConfig:
    """Configuration for running the license pipeline."""

    event_type: str
    """Type of request event to run the pipeline for."""

    rid_table_name: str
    """Name of the BigQuery table containing source request IDs."""

    rid_event_type: str
    """Type of event to query for collecting request IDs."""

    rid_request_type: str
    """Type of request to query for collecting request IDs."""

    field_to_blobs: str
    """Name of the field in the event that contains the blobs JSON object."""


COMPLETION_CONFIG = PipelineConfig(
    event_type="completion_host_request",
    rid_table_name="session_event",
    rid_event_type="completion_request_id_issued",
    rid_request_type="",
    field_to_blobs="blobs",
)

CHAT_CONFIG = PipelineConfig(
    event_type="chat_host_request",
    rid_table_name="request_metadata",
    rid_event_type="",
    rid_request_type="CHAT",
    field_to_blobs="request.blobs",
)

SUPPORTED_EVENT_TYPES = {
    COMPLETION_CONFIG.event_type: COMPLETION_CONFIG,
    CHAT_CONFIG.event_type: CHAT_CONFIG,
}


@contextmanager
def time_block(name, log_func=print):
    log_func(f"Starting {name}")
    start = time.time()
    yield
    duration_min = (time.time() - start) / 60
    log_func(f"{name} took {duration_min:.2f} minutes")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--event_type",
        type=str,
        required=True,
        help=f"Type of request event to run the pipeline for. Supported types: {list(SUPPORTED_EVENT_TYPES.keys())}",
    )
    parser.add_argument(
        "--env",
        type=str,
        required=True,
        help="Environment that the request data came from (prod or staging)",
    )
    parser.add_argument(
        "--date_from",
        type=str,
        required=True,
        help="Start date for the query (inclusive)",
    )
    parser.add_argument(
        "--date_to",
        type=str,
        required=True,
        help="End date for the query (exclusive)",
    )
    parser.add_argument(
        "--commit",
        action="store_true",
        default=False,
        required=False,
        help="Whether to commit the results to the final output directory",
    )
    parser.add_argument(
        "--confidence_threshold",
        default=DEFAULT_CONFIDENCE_THRESHOLD,
        type=float,
        required=False,
        help="Confidence threshold to consider if a blob contains a certain license",
    )
    parser.add_argument(
        "--workers",
        default=64,
        type=int,
        required=False,
        help="Number of Spark workers to request",
    )
    parser.add_argument(
        "--cores",
        default=2,
        type=int,
        required=False,
        help="Number of cores per worker",
    )
    parser.add_argument(
        "--ram",
        default=32,
        type=int,
        required=False,
        help="Amount of RAM per worker in GiB",
    )
    parser.add_argument(
        "--output_files",
        default=1,
        type=int,
        required=False,
        help="Number of output files to write in commit step",
    )
    args = parser.parse_args()

    # Validate and print arguments
    if args.event_type not in SUPPORTED_EVENT_TYPES:
        raise ValueError(f"Unsupported event type: {args.event_type}")
    pipeline_config = SUPPORTED_EVENT_TYPES[args.event_type]
    if args.env not in ["prod", "staging"]:
        raise ValueError(f"Invalid environment: {args.env}. Must be prod or staging.")
    tenants = [
        t_name
        for t_name, t in DATASET_TENANTS.items()
        if t.events_bucket_name.split("-")[1] == args.env  # us-staging or us-prod
    ]

    print(f"Running for event type: {args.event_type}")
    print(f"Pipeline config: {pipeline_config}")
    print(f"Running on {args.env} env, tenants: {tenants}")
    print(f"Running from {args.date_from} to {args.date_to}")
    print(f"Confidence threshold: {args.confidence_threshold}")
    print(f"Spark workers: {args.workers}, cores: {args.cores}, ram: {args.ram}g")
    print(f"Committing results: {args.commit}")
    print(f"Number of output files: {args.output_files}")
    print("Starting pipeline...")

    # Step 1: Get a fixed set of request IDs to work with
    with time_block("step_1"):
        get_request_ids_from_search_db.main(
            QueryParams(
                table_name=pipeline_config.rid_table_name,
                event_type=pipeline_config.rid_event_type,
                request_type=pipeline_config.rid_request_type,
                tenants=tenants,
                date_from=args.date_from,
                date_to=args.date_to,
            )
        )

    # Step 2: Get license information for selected blobs
    with time_block("step_2"):
        get_blob_licenses.main(
            QueryParams(
                table_name="",  # unused; events are fetched from blob_id_source=gcs
                event_type=pipeline_config.event_type,
                field_to_blobs=pipeline_config.field_to_blobs,
                tenants=tenants,
                date_from=args.date_from,
                date_to=args.date_to,
            ),
            args.workers,
            args.cores,
            args.ram,
            debug=False,
            blob_id_source="gcs",
        )

    # Step 3: Determine permissiveness for each request ID
    with time_block("step_3"):
        get_request_permits.main(
            args.confidence_threshold,
            args.workers,
            args.cores,
            args.ram,
        )

    print(SLACK_MSG_START)
    if args.commit:
        # Step 4: Validate and commit data to an organized output directory
        with time_block("step_4"):
            commit_data.main(
                env=args.env,
                event_type=args.event_type,
                date_from=args.date_from,
                date_to=args.date_to,
                output_files=args.output_files,
            )

        # Step 5: Update the permissive and non-permissive request IDs
        with time_block("step_5"):
            update_rids.main(args.event_type)
    else:
        print("Skipping commit step")

    print("Pipeline completed successfully!")
    print(SLACK_MSG_END)

"""<PERSON><PERSON><PERSON> to validate and commit the results of the license filter pipeline.

Example usage:
python research/vanguard/license_filter/commit_data.py \
--env prod \
--event_type completion_host_request \
--date_from 2025-01-01 \
--date_to 2025-02-01

This script will read data from the tmp directory and commit them
to the final output directory with predefined partitions.
"""

import argparse
import time

import pyspark.sql.functions as F
from pyspark.sql import SparkSession

from research.data.spark import k8s_session
from research.vanguard.license_filter.utils import (
    SPECIAL_TAGS,
    get_blob_ids_path,
    get_blob_licenses_path,
    get_request_ids_path,
    get_request_permits_path,
    get_top_license,
)

# Number of Spark workers to request for this step
# We use a fixed small size here as the cluster is only for calculating
# metrics and moving data
SPARK_WORKERS = 4


def show_metrics(spark: SparkSession):
    request_ids = spark.read.parquet(get_request_ids_path())
    rid_count = request_ids.count()
    print(f"Number of request IDs: {rid_count}")
    rid_unique_count = request_ids.distinct().count()
    print(f"Number of unique request IDs: {rid_unique_count}")
    rid_dup_pct = (rid_count - rid_unique_count) / rid_count * 100
    print(f"Percentage of duplicate request IDs: {rid_dup_pct:.2f}%")
    print()

    blob_ids = spark.read.parquet(get_blob_ids_path()).select(
        F.explode(F.col("blob_ids")).alias("blob_id")
    )
    blob_unique_count = blob_ids.distinct().count()
    print(f"Number of unique involved blob IDs: {blob_unique_count}")

    blob_licenses = spark.read.parquet(get_blob_licenses_path(is_tmp=True))
    blob_licenses_count = blob_licenses.count()
    print(f"Number of rows in blob licenses: {blob_licenses_count}")

    detected_licenses = blob_licenses.filter(~F.col("license_info").isin(SPECIAL_TAGS))
    detected_licenses_count = detected_licenses.count()
    print(f"Number of blobs with licenses detected: {detected_licenses_count}")
    detected_licenses_pct = detected_licenses_count / blob_licenses_count * 100
    print(f"Percentage of blobs with licenses detected: {detected_licenses_pct:.2f}%")
    print()

    @F.udf("string")
    def get_top_license_udf(license_info: str) -> str:
        return get_top_license(license_info)

    (
        detected_licenses.select(
            get_top_license_udf(F.col("license_info")).alias("top_license")
        )
        .filter(~F.col("top_license").isin(SPECIAL_TAGS))
        .groupBy("top_license")
        .agg(F.count("*").alias("count"))
        .sort(F.desc("count"))
        .show()
    )

    request_permits = spark.read.parquet(get_request_permits_path(is_tmp=True))
    request_permits_count = request_permits.count()
    print(f"Number of rows in request permits: {request_permits_count}")
    vs_input_rid_pct = request_permits_count / rid_unique_count * 100
    print(f"Percentage of request IDs in input: {vs_input_rid_pct:.2f}%")
    non_permissive_count = request_permits.filter(~F.col("is_permissive")).count()
    print(f"Number of non-permissive requests: {non_permissive_count}")
    non_permissive_pct = non_permissive_count / request_permits_count * 100
    print(f"Percentage of non-permissive: {non_permissive_pct:.2f}%")
    print()


def commit_data(spark: SparkSession, src_path: str, dst_path: str, output_files: int):
    print(f"Committing data from {src_path} to {dst_path}")
    (
        spark.read.parquet(src_path)
        .repartition(output_files)
        .write.mode("overwrite")
        .parquet(dst_path)
    )


def commit_blob_licenses(spark: SparkSession, env: str, output_files: int) -> str:
    input_path = get_blob_licenses_path(is_tmp=True)
    output_root = get_blob_licenses_path(is_tmp=False)
    timestamp = int(time.time())
    output_path = f"{output_root}/{env}/{timestamp}"
    commit_data(spark, input_path, output_path, output_files)
    return output_path


def commit_request_permits(
    spark: SparkSession,
    env: str,
    event_type: str,
    date_from: str,
    date_to: str,
    output_files: int,
) -> str:
    input_path = get_request_permits_path(is_tmp=True)
    output_root = get_request_permits_path(is_tmp=False)
    date_range = f"{date_from}_{date_to}"
    output_path = f"{output_root}/{event_type}/{env}/{date_range}"
    commit_data(spark, input_path, output_path, output_files)
    return output_path


def main(env: str, event_type: str, date_from: str, date_to: str, output_files: int):
    spark = k8s_session(max_workers=SPARK_WORKERS)
    show_metrics(spark)
    commit_blob_licenses(spark, env, output_files)
    commit_request_permits(spark, env, event_type, date_from, date_to, output_files)
    spark.stop()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--env",
        type=str,
        required=True,
        help="Environment that the request data came from (prod or staging)",
    )
    parser.add_argument(
        "--event_type",
        type=str,
        required=True,
        help="Type of request event for the produced license data (completion_host_request or chat_host_request)",
    )
    parser.add_argument(
        "--date_from",
        type=str,
        required=True,
        help="Start date for the produced license data (inclusive)",
    )
    parser.add_argument(
        "--date_to",
        type=str,
        required=True,
        help="End date for the produced license data (exclusive)",
    )
    parser.add_argument(
        "--output_files",
        type=int,
        default=1,
        required=False,
        help="Number of output files to write for each dataset",
    )
    args = parser.parse_args()
    main(args.env, args.event_type, args.date_from, args.date_to, args.output_files)

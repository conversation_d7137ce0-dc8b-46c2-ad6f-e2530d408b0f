"""<PERSON><PERSON>t to send notification messages to <PERSON>lack.

Example usage:
python research/vanguard/license_filter/slack_msg.py \
    --token "$slack_bot_token" \
    --channel "$slack_channel" \
    --message "Completed Vanguard License Pipeline for date range: [$last_month, $this_month)" \
    --log_file "$log_file"  # optional
"""

import argparse

import slack_sdk

from research.vanguard.license_filter.utils import SLACK_MSG_END, SLACK_MSG_START

# Maximum number of lines to send from the log file
MAX_LOG_LINES = 100


def main(token: str, channel: str, message: str, log_file: str | None = None):
    client = slack_sdk.WebClient(token=token)
    response = client.chat_postMessage(channel=channel, text=message)
    if log_file:
        with open(log_file, "r") as f:
            lines = f.readlines()
            start, end = 0, len(lines)
            for i in range(len(lines) - 1, -1, -1):
                if SLACK_MSG_END in lines[i]:
                    end = i
                if SLACK_MSG_START in lines[i]:
                    start = i + 1
                    break
            assert (
                start < end
            ), f"Failed to find slack message boundaries in {log_file}. start={start}, end={end}."
            lines = lines[start:end]
            if len(lines) > MAX_LOG_LINES:
                lines = lines[-MAX_LOG_LINES:]
        client.chat_postMessage(
            channel=channel,
            text="```\n" + "".join(lines) + "```",
            thread_ts=response["ts"],
        )


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--token",
        type=str,
        required=True,
        help="Slack bot token to use",
    )
    parser.add_argument(
        "--channel",
        type=str,
        required=True,
        help="Slack channel to send notifications to",
    )
    parser.add_argument(
        "--message",
        type=str,
        required=True,
        help="Message to send to Slack",
    )
    parser.add_argument(
        "--log_file",
        type=str,
        required=False,
        help="Path to the log file to send pipeline run report from",
    )
    args = parser.parse_args()
    main(args.token, args.channel, args.message, args.log_file)

"""<PERSON>ript to determine permissiveness for each request ID.

Example usage:
python research/vanguard/license_filter/get_request_permits.py \
--confidence_threshold 0.7 \
--workers 8 \
--cores 4 \
--ram 32

In this job, we first compute permissiveness for each blob, and then use
the results to determine permissiveness for each request ID.
"""

import argparse

import pyspark.sql.functions as F

from research.data.spark import k8s_session
from research.vanguard.license_filter.utils import (
    SPECIAL_TAGS,
    get_blob_ids_path,
    get_blob_licenses_path,
    get_permissive_licenses,
    get_request_ids_path,
    get_request_permits_path,
    get_top_license,
    is_permissive,
)

# Number of partitions (output files) for writing datasets
OUTPUT_PARTITIONS = 50


def main(confidence_threshold: float, workers: int, cores: int, ram: int):
    spark = k8s_session(
        max_workers=workers,
        conf={
            "spark.executor.cores": str(cores),
            "spark.executor.memory": f"{ram}g",
        },
    )

    permissive_licenses = get_permissive_licenses(spark)
    print(f"Number of permissive licenses: {len(permissive_licenses)}")

    @F.udf("string")
    def get_top_license_udf(license_info: str) -> str:
        return get_top_license(license_info)

    @F.udf("boolean")
    def is_permissive_udf(license_info: str) -> bool:
        return is_permissive(license_info, permissive_licenses, confidence_threshold)

    blob_permits = (
        spark.read.parquet(get_blob_licenses_path(is_tmp=True))
        .filter(~F.col("license_info").isin(SPECIAL_TAGS))
        .withColumns(
            {
                "top_license": get_top_license_udf(F.col("license_info")),
                "is_permissive": is_permissive_udf(F.col("license_info")),
            },
        )
    )

    non_permissive_blobs = (
        blob_permits.filter(~F.col("is_permissive")).select("blob_id").collect()
    )
    blob_denylist = [row["blob_id"] for row in non_permissive_blobs]
    print(f"Number of non-permissive blobs: {len(blob_denylist)}")

    request_permits = (
        spark.read.parquet(get_blob_ids_path())
        .select(
            F.col("request_id"),
            F.explode(F.col("blob_ids")).alias("blob_id"),
        )
        .withColumn(
            "is_permissive",
            ~F.col("blob_id").isin(blob_denylist),
        )
        .groupBy("request_id")
        .agg(F.min("is_permissive").alias("is_permissive"))
        .persist()
    )

    requests_with_empty_blob = (
        spark.read.parquet(get_request_ids_path())
        .select("request_id")
        .join(
            request_permits,
            on="request_id",
            how="left_anti",
        )
        .withColumn("is_permissive", F.lit(True))
    )

    (
        request_permits.union(requests_with_empty_blob)
        .repartition(OUTPUT_PARTITIONS)
        .write.mode("overwrite")
        .parquet(get_request_permits_path(is_tmp=True))
    )

    spark.stop()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--confidence_threshold",
        default=0.7,
        type=float,
        required=False,
        help="Confidence threshold to consider if a blob contains a certain license",
    )
    parser.add_argument(
        "--workers",
        default=8,
        type=int,
        required=False,
        help="Number of Spark workers to request",
    )
    parser.add_argument(
        "--cores",
        default=4,
        type=int,
        required=False,
        help="Number of cores per worker",
    )
    parser.add_argument(
        "--ram",
        default=32,
        type=int,
        required=False,
        help="Amount of RAM per worker in GiB",
    )
    args = parser.parse_args()
    main(args.confidence_threshold, args.workers, args.cores, args.ram)

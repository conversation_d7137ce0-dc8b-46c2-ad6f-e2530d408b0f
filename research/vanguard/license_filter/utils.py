"""Utilities for the license filter task."""

import json
from dataclasses import dataclass
from pathlib import Path

from google.cloud import storage
from google.protobuf.json_format import MessageToDict
from pyspark.sql import DataFrame, SparkSession

from services.request_insight import request_insight_pb2

# BigQuery project and dataset to use
RESEARCH_PROJECT_ID = "augment-research-gsc"
VANGUARD_DATASET_NAME = "vanguard"

# BigQuery table containing permissive licenses
PERMISSIVE_LICENSES_TABLE_ID = f"{RESEARCH_PROJECT_ID}.core.permissive_licenses"

# GCS bucket and root directories
# The EFS directory is auto-synced with the GCS bucket
GCS_BUCKET = "gcp-us1-spark-data"
ROOT_DIR = "shared/vanguard/license_filter"
GCS_ROOT_PATH = f"gs://{GCS_BUCKET}/{ROOT_DIR}"
EFS_ROOT_PATH = f"/mnt/efs/spark-data/{ROOT_DIR}"
# Subdirectory under root for temporary/intermediate data
TMP_SUBDIR = "tmp"
# Non-temporary folders
BLOB_LICENSES_DIR = "blob_licenses"
REQUEST_PERMITS_DIR = "request_permits"
PERMISSIVE_RIDS_DIR = "permissive_rids"
NON_PERMISSIVE_RIDS_DIR = "non_permissive_rids"

# Relative path to the license detector CLI to be used in the Spark executor container
CLI_RELATIVE_PATH = "research/vanguard/license_filter/license-detector"

# Keys in the `blobs` JSON object
KEY_CHECKPOINT = "baseline_checkpoint_id"
KEY_ADDED = "added"
KEY_DELETED = "deleted"

# List of file names to check for licenses
# All names here are uppercased and then matched case-insensitively
LICENSE_FILE_NAMES = [
    "LICENSE",
    "LICENSE.MD",
    "LICENSE.TXT",
    "LICENSE.RST",
    "COPYING",
    "COPYING.MD",
    "COPYING.TXT",
    "COPYING.RST",
    "COPYRIGHT",
    "COPYRIGHT.MD",
    "COPYRIGHT.TXT",
    "COPYRIGHT.RST",
    "NOTICE",
    "README",
    "README.MD",
    "README.MARKDOWN",
    "README.TXT",
    "README.RST",
    "README.ADOC",
    "README.ASCIIDOC",
]

# Tags for special cases (errors or other indicators) in the output datasets
# in `blob_ids` dataset
CHECKPOINT_NOT_FOUND_ERROR = "==CHECKPOINT_NOT_FOUND_ERROR=="
# in `blob_licenses` dataset
GET_PATH_ERROR = "==GET_PATH_ERROR=="
BLOB_NOT_FOUND_ERROR = "==BLOB_NOT_FOUND_ERROR=="
LICENSE_DETECTION_ERROR = "==LICENSE_DETECTION_ERROR=="
NOT_LICENSE = "==NOT_LICENSE=="
# A list of all special tags for easy filtering
SPECIAL_TAGS = [
    CHECKPOINT_NOT_FOUND_ERROR,
    GET_PATH_ERROR,
    BLOB_NOT_FOUND_ERROR,
    LICENSE_DETECTION_ERROR,
    NOT_LICENSE,
]

# Barriers for slack notification messages
SLACK_MSG_START = "==========SLACK_MSG_START=========="
SLACK_MSG_END = "==========SLACK_MSG_END=========="


@dataclass
class QueryParams:
    """Common parameters for querying BigQuery tables."""

    table_name: str
    """Name of the table to query."""

    tenants: list[str]
    """Names of the tenants to filter on."""

    date_from: str
    """Start date for the query (inclusive)."""

    date_to: str
    """End date for the query (exclusive)."""

    event_type: str = ""
    """Type of event to filter on."""

    request_type: str = ""
    """Type of request to filter on."""

    field_to_blobs: str = ""
    """Name of the field in the event that contains the blobs JSON object."""


def get_table_id(table_name: str):
    """Get the full table ID for a table in the vanguard dataset."""
    return f"{RESEARCH_PROJECT_ID}.{VANGUARD_DATASET_NAME}.{table_name}"


def get_filters_str(query_params: QueryParams) -> str:
    """Get the WHERE clause for filtering the query."""
    filters = []
    tenants_list_str = ", ".join([f'"{t}"' for t in query_params.tenants])
    filters.append(f"tenant IN UNNEST([{tenants_list_str}])")
    if query_params.request_type:
        filters.append(f"request_type = '{query_params.request_type}'")
    if query_params.event_type:
        filters.append(f"event_type = '{query_params.event_type}'")
    filters.append(f"time >= '{query_params.date_from}'")
    filters.append(f"time < '{query_params.date_to}'")
    return " AND ".join(filters)


def get_root_path(use_efs: bool = False):
    """Get the root path for the license filter task."""
    return EFS_ROOT_PATH if use_efs else GCS_ROOT_PATH


def get_request_ids_path(use_efs: bool = False):
    """Get the path to the directory containing the selected request IDs."""
    # Not a dataset to be consumed by users, so always write to tmp
    return f"{get_root_path(use_efs)}/{TMP_SUBDIR}/request_ids"


def get_blob_ids_path(use_efs: bool = False):
    """Get the path to the directory containing blob IDs for selected request IDs."""
    # Not a dataset to be consumed by users, so always write to tmp
    return f"{get_root_path(use_efs)}/{TMP_SUBDIR}/blob_ids"


def get_blob_licenses_path(is_tmp: bool = False, use_efs: bool = False):
    """Get the path to the directory containing license info for blobs."""
    root_path = get_root_path(use_efs)
    if is_tmp:
        return f"{root_path}/{TMP_SUBDIR}/{BLOB_LICENSES_DIR}"
    else:
        return f"{root_path}/{BLOB_LICENSES_DIR}"


def get_request_permits_path(is_tmp: bool = False, use_efs: bool = False):
    """Get the path to the directory containing request permits."""
    root_path = get_root_path(use_efs)
    if is_tmp:
        return f"{root_path}/{TMP_SUBDIR}/{REQUEST_PERMITS_DIR}"
    else:
        return f"{root_path}/{REQUEST_PERMITS_DIR}"


def get_permissive_rids_path(use_efs: bool = False, subdir: str = ""):
    """Get the path to the directory containing permissive request IDs."""
    return f"{get_root_path(use_efs)}/{PERMISSIVE_RIDS_DIR}/{subdir}"


def get_non_permissive_rids_path(use_efs: bool = False, subdir: str = ""):
    """Get the path to the directory containing non-permissive request IDs."""
    return f"{get_root_path(use_efs)}/{NON_PERMISSIVE_RIDS_DIR}/{subdir}"


def read_parquets(spark: SparkSession, gcs_path: str) -> DataFrame | None:
    """Read all parquet files under the given GCS path."""
    assert gcs_path.startswith("gs://")
    bucket_name, prefix = gcs_path[5:].split("/", 1)
    blobs = storage.Client().bucket(bucket_name).list_blobs(prefix=prefix)
    parquet_files = [
        f"gs://{bucket_name}/{blob.name}"
        for blob in blobs
        if blob.name.endswith(".parquet")
    ]
    print(f"Found {len(parquet_files)} parquet files under {gcs_path}")
    if not parquet_files:
        return None
    return spark.read.parquet(*parquet_files)


def get_storage_blob(
    project_id: str,
    bucket_name: str,
    blob_name: str,
    credentials,
) -> storage.Blob:
    """Get a storage blob object from a GCS bucket."""
    client = storage.Client(
        project=project_id,
        credentials=credentials,
    )
    bucket = client.bucket(bucket_name)
    return bucket.blob(blob_name)


def get_storage_blobs(
    project_id: str,
    bucket_name: str,
    prefix: str,
    credentials,
) -> list[storage.Blob]:
    """Get a list of storage blob objects with a given prefix from a GCS bucket."""
    client = storage.Client(
        project=project_id,
        credentials=credentials,
    )
    bucket = client.bucket(bucket_name)
    return list(bucket.list_blobs(prefix=prefix))


def get_request_event_field(
    project_id: str,
    bucket_name: str,
    tenant_id: str,
    request_id: str,
    event_type: str,
    field_name: str,
    credentials,
) -> list[str]:
    """Get value of a request event field in serialized JSON format."""
    prefix = f"{tenant_id}/request/{request_id}/{event_type}"
    storage_blobs = get_storage_blobs(
        project_id,
        bucket_name,
        prefix,
        credentials,
    )
    ret_val = []
    for storage_blob in storage_blobs:
        storage_blob_bytes = storage_blob.download_as_bytes()
        request_event = request_insight_pb2.RequestEvent()
        request_event.ParseFromString(storage_blob_bytes)
        request_event_dict = MessageToDict(
            request_event, preserving_proto_field_name=True
        )
        if event_type not in request_event_dict:
            raise ValueError(f"Event type {event_type} not found: {request_event_dict}")
        event_data = request_event_dict[event_type]
        if not event_data:
            print(f"Event data is empty in {prefix}")
            continue
        # Recursively traverse the event_data dictionary to find the specified field
        # The field_name can be nested, with levels separated by dots
        for field in field_name.split("."):
            if field not in event_data:
                print(f"Field {field} not found in {event_data}")
                continue
            event_data = event_data[field]
        if isinstance(event_data, list):
            # the provided field is a list of target objects
            ret_val.extend([json.dumps(item) for item in event_data])
        else:
            ret_val.append(json.dumps(event_data))
    return ret_val


def get_checkpoint_blob_ids(
    project_id: str,
    bucket_name: str,
    bucket_prefix: str,
    checkpoint_id: str,
    credentials,
) -> list[str]:
    """Get the list of blob IDs from a checkpoint."""
    storage_blob = get_storage_blob(
        project_id,
        bucket_name,
        f"{bucket_prefix}/{checkpoint_id}",
        credentials,
    )
    if not storage_blob.exists():
        print(f"Checkpoint {checkpoint_id} not found")
        return [CHECKPOINT_NOT_FOUND_ERROR]
    checkpoint_text = storage_blob.download_as_text()
    return json.loads(checkpoint_text)


def get_blob_file_name(blob: storage.Blob) -> str:
    """Get the file name of the blob from its metadata."""
    blob.reload()
    if blob.metadata is None or "path" not in blob.metadata:
        return GET_PATH_ERROR
    try:
        file_path = blob.metadata.get("path")
        file_name = Path(file_path).name
    except Exception:
        return GET_PATH_ERROR
    return file_name


def get_permissive_licenses(spark: SparkSession) -> list[str]:
    """Get the list of permissive licenses from the BigQuery table."""
    collected_licenses = (
        spark.read.format("bigquery")
        .option("table", PERMISSIVE_LICENSES_TABLE_ID)
        .load()
        .collect()
    )
    # NOTE: Lowercase all license names for case-insensitive matching
    return [str(row["name"]).lower() for row in collected_licenses] + ["unlicense"]


def load_license_matches(license_info: str) -> list[dict]:
    """Parse license info and return a list of license matches."""
    parsed = json.loads(license_info)
    if len(parsed) != 1:
        # We only pass one project at a time to license-detector
        raise ValueError("license_info should have only 1 element")
    head = parsed[0]
    if "matches" not in head:
        return []
    return head["matches"]


def get_top_license(license_info: str) -> str:
    """Get the top license from license info."""
    matches = load_license_matches(license_info)
    if not matches:
        return NOT_LICENSE
    top_match = max(matches, key=lambda x: float(x["confidence"]))
    return str(top_match["license"]).lower()


def is_permissive(
    license_info: str,
    permissive_licenses: list[str],
    confidence_threshold: float,
) -> bool:
    """Check if the license info is permissive.
    It's permissive if and only if:
    * all licenses with confidence above the threshold are permissive.
    """
    matches = load_license_matches(license_info)
    for match in matches:
        license = str(match["license"]).lower()
        confidence = float(match["confidence"])
        if confidence > confidence_threshold and license not in permissive_licenses:
            return False
    return True

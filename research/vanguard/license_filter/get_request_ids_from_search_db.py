"""<PERSON><PERSON><PERSON> to fetch request IDs from BigQuery tables in the search database.
Save the results to GCS for later use in the license pipeline.

Example usage:

python research/vanguard/license_filter/get_request_ids_from_search_db.py \
--table_name session_event \
--event_type completion_request_id_issued \
--tenants i0-vanguard0,i0-vanguard1,i0-vanguard2,i0-vanguard3,i0-vanguard4,i0-vanguard5,i0-vanguard6,i0-vanguard7,i1-vanguard0,i1-vanguard1,i1-vanguard2,i1-vanguard3,i1-vanguard4,i1-vanguard5,i1-vanguard6,i1-vanguard7 \
--date_from 2025-01-01 \
--date_to 2025-02-01

python research/vanguard/license_filter/get_request_ids_from_search_db.py \
--table_name request_metadata \
--request_type CHAT \
--tenants i0-vanguard0,i0-vanguard1,i0-vanguard2,i0-vanguard3,i0-vanguard4,i0-vanguard5,i0-vanguard6,i0-vanguard7,i1-vanguard0,i1-vanguard1,i1-vanguard2,i1-vanguard3,i1-vanguard4,i1-vanguard5,i1-vanguard6,i1-vanguard7 \
--date_from 2025-01-01 \
--date_to 2025-02-01
"""

import argparse
import json
import time
from pathlib import Path

import pyspark.sql.functions as F
from google.cloud import bigquery

from base.datasets.tenants import get_tenant
from research.data.spark import k8s_session
from research.vanguard.license_filter.utils import (
    RESEARCH_PROJECT_ID,
    QueryParams,
    get_filters_str,
    get_request_ids_path,
    get_table_id,
)

# BigQuery table to be created/replaced (contains request IDs from search database)
# It's an intermediate table and not expected to be used by other jobs
RID_TABLE_NAME = "request_ids"
RID_TABLE_ID = get_table_id(RID_TABLE_NAME)

# Number of Spark workers to request for this step
# We use a fixed small size here as the cluster is only for moving data
SPARK_WORKERS = 4
# Number of partitions (output files) for writing datasets
OUTPUT_PARTITIONS = 1


def fetch_request_ids_on_bq(query_params: QueryParams):
    """Fetch corresponding request IDs from the specified BigQuery table,
    then save them to a new BigQuery table under the research vanguard dataset.
    """
    input_tables = set(
        [
            f"{get_tenant(t).project_id}.{get_tenant(t).search_dataset_name}.{query_params.table_name}"
            for t in query_params.tenants
        ]
    )
    if len(input_tables) > 1:
        raise ValueError("Multiple tenants must share the same input table.")

    query = f"""
    CREATE OR REPLACE TABLE `{RID_TABLE_ID}` AS
    SELECT
        tenant_id,
        tenant,
        request_id
    FROM `{list(input_tables)[0]}`
    WHERE
        {get_filters_str(query_params)}
    """
    print(query)

    client = bigquery.Client(project=RESEARCH_PROJECT_ID)
    job = client.query(query)
    job.result()


def save_request_ids_to_gcs(query_params: QueryParams):
    """Save the request IDs from the output BigQuery table to parquet on GCS.

    Note: We use Spark here to write parquet to GCS for better integration
    with the rest of the pipeline (to avoid problems with format or schema).
    """
    spark = k8s_session(max_workers=SPARK_WORKERS)
    (
        spark.read.format("bigquery")
        .option("table", RID_TABLE_ID)
        .load()  # load the request IDs from the output table
        .select(
            F.col("tenant_id"),
            F.col("tenant"),
            F.col("request_id"),
        )
        .filter(
            F.col("tenant_id").isNotNull()
            & F.col("tenant").isNotNull()
            & F.col("request_id").isNotNull()
        )
        .distinct()  # clean up data
        .repartition(OUTPUT_PARTITIONS)
        .write.mode("overwrite")
        .parquet(get_request_ids_path())  # write to GCS
    )
    spark.stop()

    # Wait for the write operation to finish before saving the params
    # There can be race conditions if we save the params immediately
    time.sleep(5)

    # Log the script parameters for debugging purposes
    params_file_path = Path(get_request_ids_path(use_efs=True)) / "_params.json"
    print(f"Saving script params to {params_file_path}")

    max_retries = 3
    for attempt in range(max_retries):
        try:
            write_params_file(query_params, params_file_path)
            break
        except Exception:
            if attempt == max_retries - 1:
                raise
            print(f"Failed to write params file (Attempt {attempt + 1}/{max_retries})")
            time.sleep(5)


def write_params_file(query_params: QueryParams, params_file_path: Path):
    """Write the script parameters to a JSON file."""
    with open(params_file_path, "w") as params_file:
        json.dump(
            {
                "table_name": query_params.table_name,
                "request_type": query_params.request_type,
                "event_type": query_params.event_type,
                "tenants": query_params.tenants,
                "date_from": query_params.date_from,
                "date_to": query_params.date_to,
            },
            params_file,
        )


def main(query_params: QueryParams):
    fetch_request_ids_on_bq(query_params)
    save_request_ids_to_gcs(query_params)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--table_name",
        type=str,
        required=True,
        help="Name of the table to query for collecting request IDs",
    )
    parser.add_argument(
        "--request_type",
        default="",
        type=str,
        required=False,
        help="Type of request to query for collecting request IDs",
    )
    parser.add_argument(
        "--event_type",
        default="",
        type=str,
        required=False,
        help="Type of event to query for collecting request IDs",
    )
    parser.add_argument(
        "--tenants",
        default="i0-vanguard0,i0-vanguard1,i0-vanguard2,i0-vanguard3,i0-vanguard4,i0-vanguard5,i0-vanguard6,i0-vanguard7,i1-vanguard0,i1-vanguard1,i1-vanguard2,i1-vanguard3,i1-vanguard4,i1-vanguard5,i1-vanguard6,i1-vanguard7",
        type=str,
        required=False,
        help="Name of the tenants to run on (see options in base/datasets/tenants.py). Separate multiple tenants with comma.",
    )
    parser.add_argument(
        "--date_from",
        default="2000-01-01",
        type=str,
        required=False,
        help="Start date for the query (inclusive)",
    )
    parser.add_argument(
        "--date_to",
        default="2100-01-01",
        type=str,
        required=False,
        help="End date for the query (exclusive)",
    )
    args = parser.parse_args()
    query_params = QueryParams(
        table_name=args.table_name,
        request_type=args.request_type,
        event_type=args.event_type,
        tenants=args.tenants.split(","),
        date_from=args.date_from,
        date_to=args.date_to,
    )
    main(query_params)

"""Script to get license information for selected blobs.

Example usage:

python research/vanguard/license_filter/get_blob_licenses.py \
--event_type completion_host_request \
--field_to_blobs blobs \
--tenants i0-vanguard0,i0-vanguard1,i0-vanguard2,i0-vanguard3,i0-vanguard4,i0-vanguard5,i0-vanguard6,i0-vanguard7,i1-vanguard0,i1-vanguard1,i1-vanguard2,i1-vanguard3,i1-vanguard4,i1-vanguard5,i1-vanguard6,i1-vanguard7 \
--date_from 2025-01-01 \
--date_to 2025-02-01 \
--workers 64 \
--cores 2 \
--ram 32

python research/vanguard/license_filter/get_blob_licenses.py \
--event_type chat_host_request \
--field_to_blobs request.blobs \
--tenants i0-vanguard0,i0-vanguard1,i0-vanguard2,i0-vanguard3,i0-vanguard4,i0-vanguard5,i0-vanguard6,i0-vanguard7,i1-vanguard0,i1-vanguard1,i1-vanguard2,i1-vanguard3,i1-vanguard4,i1-vanguard5,i1-vanguard6,i1-vanguard7 \
--date_from 2025-01-01 \
--date_to 2025-02-01 \
--workers 64 \
--cores 2 \
--ram 32

Here we leverage [go-license-detector](https://github.com/go-enry/go-license-detector)
to find the license information for each potential license file.
"""

import argparse
import json
import subprocess
import tempfile
from pathlib import Path

import google.auth
import pyspark.sql.functions as F
from google.cloud import bigquery
from pyspark.sql.types import ArrayType, StringType

from base.datasets.tenants import DatasetTenant, get_tenant
from research.data.spark import k8s_session
from research.vanguard.license_filter.utils import (
    BLOB_NOT_FOUND_ERROR,
    CHECKPOINT_NOT_FOUND_ERROR,
    CLI_RELATIVE_PATH,
    GET_PATH_ERROR,
    KEY_ADDED,
    KEY_CHECKPOINT,
    KEY_DELETED,
    LICENSE_DETECTION_ERROR,
    LICENSE_FILE_NAMES,
    NOT_LICENSE,
    RESEARCH_PROJECT_ID,
    QueryParams,
    get_blob_file_name,
    get_blob_ids_path,
    get_blob_licenses_path,
    get_checkpoint_blob_ids,
    get_filters_str,
    get_request_event_field,
    get_request_ids_path,
    get_storage_blob,
    get_table_id,
    read_parquets,
)

# Number of partitions (output files) for writing datasets
OUTPUT_PARTITIONS = 50

# Number of rows to show in debug mode
DEBUG_SHOW_SIZE = 20

# Full path to the license detector CLI in Spark executor containers
CLI_PATH = f"/mnt/ephemeral/disk/deps/lib/python3.11/site-packages/{CLI_RELATIVE_PATH}"

# BigQuery table to be created/replaced (contains blobs used in selected requests)
# It's an intermediate table and not expected to be used by other jobs
BLOBS_TABLE_NAME = "blobs"
BLOBS_TABLE_ID = get_table_id(BLOBS_TABLE_NAME)


def fetch_blobs_on_bq(query_params: QueryParams):
    """Fetch the blobs JSON object from the specified BigQuery table,
    then save it to a new BigQuery table under the research vanguard dataset.
    """
    input_tables = set(
        [
            f"{get_tenant(t).project_id}.{get_tenant(t).dataset_name}.{query_params.table_name}"
            for t in query_params.tenants
        ]
    )
    if len(input_tables) > 1:
        raise ValueError("Multiple tenants must share the same input table.")

    query = f"""
    CREATE OR REPLACE TABLE `{BLOBS_TABLE_ID}` AS
    SELECT
        request_id,
        tenant,
        TO_JSON_STRING(JSON_QUERY(raw_json, "$.{query_params.field_to_blobs}")) AS blobs
    FROM `{list(input_tables)[0]}`
    WHERE
        {get_filters_str(query_params)}
    """
    print(query)

    client = bigquery.Client(project=RESEARCH_PROJECT_ID)
    job = client.query(query)
    job.result()


def run_spark(
    workers: int,
    cores: int,
    ram: int,
    debug: bool,
    credentials,
    tenants: dict[str, DatasetTenant],
    query_params: QueryParams,
    blob_id_source: str,
):
    # Launch a Spark cluster
    parallelism = workers * cores * 4
    spark = k8s_session(
        max_workers=workers,
        conf={
            "spark.executor.cores": str(cores),
            "spark.executor.memory": f"{ram}g",
            "spark.default.parallelism": f"{parallelism}",
            "spark.sql.shuffle.partitions": f"{parallelism}",
        },
    )

    # Step 1: Get the fixed set of request IDs to work with
    request_ids = (
        spark.read.parquet(get_request_ids_path())
        .select(
            F.col("tenant_id"),
            F.col("tenant"),
            F.col("request_id"),
        )
        .filter(
            F.col("tenant_id").isNotNull()
            & F.col("tenant").isNotNull()
            & F.col("request_id").isNotNull()
        )
        .distinct()
    )

    if debug:
        print("Input Request IDs:")
        request_ids.printSchema()
        request_ids.show(DEBUG_SHOW_SIZE)
        print(f"Count: {request_ids.count()}")

    # Step 2: Get the blobs (in serialized JSON format) from request events
    if blob_id_source == "bq":
        blobs = (
            spark.read.format("bigquery")
            .option("table", BLOBS_TABLE_ID)
            .load()
            .select(F.col("request_id"), F.col("tenant"), F.col("blobs"))
            .filter(
                F.col("request_id").isNotNull()
                & F.col("tenant").isNotNull()
                & F.col("blobs").isNotNull()
            )
            .dropDuplicates(subset=["request_id"])
            .join(
                request_ids,
                on=["request_id", "tenant"],
                how="inner",
            )
        )
    elif blob_id_source == "gcs":

        @F.udf(ArrayType(StringType()))
        def get_request_event_field_udf(
            tenant_id: str, tenant: str, request_id: str
        ) -> list[str]:
            return get_request_event_field(
                tenants[tenant].project_id,
                tenants[tenant].events_bucket_name,
                tenant_id,
                request_id,
                query_params.event_type,
                query_params.field_to_blobs,
                credentials,
            )

        blobs = request_ids.repartition(parallelism).withColumn(
            "blobs",
            F.explode(
                get_request_event_field_udf(
                    F.col("tenant_id"), F.col("tenant"), F.col("request_id")
                )
            ),
        )
    else:
        raise ValueError(f"Invalid blob ID source: {blob_id_source}")

    if debug:
        print("Blobs in Serialized JSON:")
        blobs.printSchema()
        blobs.show(DEBUG_SHOW_SIZE)

    # Step 3: Get the complete list of blob IDs from the blobs JSON object
    @F.udf(ArrayType(StringType()))
    def get_blob_ids_udf(tenant: str, blobs_json: str) -> list[str]:
        blob_ids = set()
        json_obj = json.loads(blobs_json)
        if KEY_CHECKPOINT in json_obj:
            blob_ids = set(
                get_checkpoint_blob_ids(
                    tenants[tenant].project_id,
                    tenants[tenant].checkpoint_bucket_name,
                    tenants[tenant].checkpoint_bucket_prefix,
                    json_obj[KEY_CHECKPOINT],
                    credentials,
                )
            )
        if KEY_ADDED in json_obj:
            blob_ids = blob_ids.union(json_obj[KEY_ADDED])
        if KEY_DELETED in json_obj:
            blob_ids = blob_ids.difference(json_obj[KEY_DELETED])
        return list(blob_ids)

    blob_ids = blobs.withColumn(
        "blob_ids",
        get_blob_ids_udf(F.col("tenant"), F.col("blobs")),
    )

    if debug:
        print("Blob IDs in Array:")
        blob_ids.printSchema()
        blob_ids.show(DEBUG_SHOW_SIZE)

    # Step 4: Save the dataset of blob IDs
    blob_ids_path = get_blob_ids_path()
    (
        blob_ids.repartition(OUTPUT_PARTITIONS)
        .write.mode("overwrite")
        .parquet(blob_ids_path)
    )

    # Step 5: Detect licenses for each blob
    blob_id = (
        spark.read.parquet(blob_ids_path)
        .select(
            F.col("tenant"),
            F.explode(F.col("blob_ids")).alias("blob_id"),
        )
        .distinct()
        .filter(F.col("blob_id") != CHECKPOINT_NOT_FOUND_ERROR)
        .persist()
    )

    if debug:
        print("Blob ID (one per row):")
        blob_id.printSchema()
        blob_id.show(DEBUG_SHOW_SIZE)
        print(f"Number of blobs to process: {blob_id.count()}")

    existing_df = read_parquets(spark, get_blob_licenses_path(is_tmp=False))
    if existing_df is None:
        print("No existing blob licenses data found")
        blobs_detected = spark.createDataFrame([], schema=blob_id.schema)
        blobs_to_detect = blob_id
    else:
        existing_blob_licenses = existing_df.dropDuplicates(
            ["tenant", "blob_id"]
        ).persist()
        blobs_detected = blob_id.join(
            existing_blob_licenses,
            on=["tenant", "blob_id"],
            how="inner",
        )
        blobs_to_detect = blob_id.join(
            existing_blob_licenses,
            on=["tenant", "blob_id"],
            how="left_anti",
        )

    if debug:
        print(f"Number of blobs already detected: {blobs_detected.count()}")
        print(f"Number of blobs to detect: {blobs_to_detect.count()}")

    @F.udf(StringType())
    def get_license_info_udf(tenant: str, blob_id: str) -> str:
        return get_license_info(
            tenants[tenant].project_id,
            tenants[tenant].blob_bucket_name,
            tenants[tenant].blob_bucket_prefix,
            blob_id,
            credentials,
        )

    blob_licenses = blobs_to_detect.repartition(parallelism).withColumn(
        "license_info",
        get_license_info_udf(F.col("tenant"), F.col("blob_id")),
    )

    if debug:
        print("Blob Licenses:")
        blob_licenses.printSchema()
        blob_licenses.show(DEBUG_SHOW_SIZE)

    if existing_df is None:
        # Update schema of the empty dataframe for the union operation
        blobs_detected = spark.createDataFrame([], schema=blob_licenses.schema)

    # Step 6: Save the dataset of blob licenses
    (
        blob_licenses.union(blobs_detected)
        .repartition(OUTPUT_PARTITIONS)
        .write.mode("overwrite")
        .parquet(get_blob_licenses_path(is_tmp=True))
    )

    spark.stop()


def get_license_info(
    project_id: str,
    bucket_name: str,
    bucket_prefix: str,
    blob_id: str,
    credentials,
) -> str:
    """Get the license information for a given blob."""
    storage_blob = get_storage_blob(
        project_id,
        bucket_name,
        f"{bucket_prefix}/{blob_id}",
        credentials,
    )
    if not storage_blob.exists():
        return BLOB_NOT_FOUND_ERROR

    file_name = get_blob_file_name(storage_blob)
    if file_name == GET_PATH_ERROR:
        return GET_PATH_ERROR

    ret_val = NOT_LICENSE
    if file_name.upper() not in LICENSE_FILE_NAMES:
        return ret_val

    blob_content = storage_blob.download_as_text()
    with tempfile.TemporaryDirectory() as tmpdir:
        with open(Path(tmpdir) / file_name, "w") as f:
            f.write(blob_content)
        try:
            license_info = subprocess.run(
                f"{CLI_PATH} -f json {tmpdir}",
                shell=True,
                check=True,
                capture_output=True,
                text=True,
            )
        except Exception as e:
            print(f"License detection failed for {blob_id}: {e}")
            return LICENSE_DETECTION_ERROR
        ret_val = license_info.stdout

    ret_val = ret_val.replace("\n", "").replace("\t", "")
    return ret_val


def main(
    query_params: QueryParams,
    workers: int,
    cores: int,
    ram: int,
    debug: bool,
    blob_id_source: str,
):
    # Get GCP credentials and tenant details
    credentials, project_id = google.auth.default()
    tenants = {t: get_tenant(t) for t in query_params.tenants}

    # Validate tenants
    tenant_project_ids = set([t.project_id for t in tenants.values()])
    if len(tenant_project_ids) > 1:
        raise ValueError("Multiple tenants must share the same project ID")
    if project_id not in tenant_project_ids:
        raise ValueError(
            f"Current project ID {project_id} does not match tenant project IDs: {tenant_project_ids}"
        )
    print(f"Using project ID {project_id} for all tenants")

    # Validate blob ID source
    if blob_id_source not in ["bq", "gcs"]:
        raise ValueError(f"Invalid blob ID source: {blob_id_source}")

    if blob_id_source == "bq":
        # This is the old way of getting blob IDs from the request_event BigQuery table
        # The current queried full-export dataset will be deprecated soon
        # We keep this code path as we may be able to fetch blob IDs from a new BigQuery table
        fetch_blobs_on_bq(query_params)

    # Leverage Spark to process the data in parallel
    run_spark(
        workers,
        cores,
        ram,
        debug,
        credentials,
        tenants,
        query_params,
        blob_id_source,
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--event_type",
        type=str,
        required=True,
        help="Type of event to query for collecting blob IDs.",
    )
    parser.add_argument(
        "--field_to_blobs",
        default="blobs",
        type=str,
        required=False,
        help="Name of the field in the event that contains the blobs JSON object. Nested fields are supported with dot notation.",
    )
    parser.add_argument(
        "--tenants",
        default="i0-vanguard0,i0-vanguard1,i0-vanguard2,i0-vanguard3,i0-vanguard4,i0-vanguard5,i0-vanguard6,i0-vanguard7,i1-vanguard0,i1-vanguard1,i1-vanguard2,i1-vanguard3,i1-vanguard4,i1-vanguard5,i1-vanguard6,i1-vanguard7",
        type=str,
        required=False,
        help="Name of the tenants to run on (see options in base/datasets/tenants.py). Separate multiple tenants with comma.",
    )
    parser.add_argument(
        "--date_from",
        default="2000-01-01",
        type=str,
        required=False,
        help="Start date for the query (inclusive)",
    )
    parser.add_argument(
        "--date_to",
        default="2100-01-01",
        type=str,
        required=False,
        help="End date for the query (exclusive)",
    )
    parser.add_argument(
        "--workers",
        default=8,
        type=int,
        required=False,
        help="Number of Spark workers to request",
    )
    parser.add_argument(
        "--cores",
        default=4,
        type=int,
        required=False,
        help="Number of cores per worker",
    )
    parser.add_argument(
        "--ram",
        default=32,
        type=int,
        required=False,
        help="Amount of RAM per worker in GiB",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        default=False,
        required=False,
        help="Whether to run in debug mode",
    )
    parser.add_argument(
        "--blob_id_source",
        default="gcs",
        type=str,
        required=False,
        help="Source of blob IDs. Can be 'bq' (BigQuery) or 'gcs' (Google Cloud Storage).",
    )
    args = parser.parse_args()
    query_params = QueryParams(
        table_name="request_event",
        event_type=args.event_type,
        field_to_blobs=args.field_to_blobs,
        tenants=args.tenants.split(","),
        date_from=args.date_from,
        date_to=args.date_to,
    )
    main(
        query_params,
        args.workers,
        args.cores,
        args.ram,
        args.debug,
        args.blob_id_source,
    )

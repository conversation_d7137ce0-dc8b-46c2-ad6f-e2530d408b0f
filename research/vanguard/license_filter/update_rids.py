"""<PERSON><PERSON><PERSON> to update the permissive and non-permissive request IDs using the latest results.

Example usage:
python research/vanguard/license_filter/update_rids.py \
--event_type completion_host_request
"""

import argparse

import pyspark.sql.functions as F

from research.data.spark import k8s_session
from research.vanguard.license_filter.utils import (
    get_non_permissive_rids_path,
    get_permissive_rids_path,
    get_request_permits_path,
    read_parquets,
)

SPARK_WORKERS = 8

SUPPORTED_EVENT_TYPES = [
    "completion_host_request",
    "chat_host_request",
]


def main(event_type: str):
    if event_type not in SUPPORTED_EVENT_TYPES:
        raise ValueError(f"Unsupported event type: {event_type}")
    print(f"Running for event type: {event_type}")

    spark = k8s_session(max_workers=SPARK_WORKERS)

    gcs_path = f"{get_request_permits_path(is_tmp=False, use_efs=False)}/{event_type}"
    print(f"Reading from {gcs_path}")
    request_permits = read_parquets(spark, gcs_path)
    if request_permits is None:
        raise ValueError(f"No data found at {gcs_path}")

    request_permits = request_permits.persist()
    print(f"Total Count: {request_permits.count()}")

    permissive_path = get_permissive_rids_path(use_efs=False, subdir=event_type)
    (
        request_permits.filter(F.col("is_permissive"))
        .select(F.col("request_id"))
        .distinct()
        .repartition(1)
        .write.mode("overwrite")
        .parquet(permissive_path)
    )
    permissive_count = spark.read.parquet(permissive_path).count()
    print(f"Permissive Count: {permissive_count}")

    non_permissive_path = get_non_permissive_rids_path(use_efs=False, subdir=event_type)
    (
        request_permits.filter(~F.col("is_permissive"))
        .select(F.col("request_id"))
        .distinct()
        .repartition(1)
        .write.mode("overwrite")
        .parquet(non_permissive_path)
    )
    non_permissive_count = spark.read.parquet(non_permissive_path).count()
    print(f"Non-Permissive Count: {non_permissive_count}")

    print(f"Sum: {permissive_count + non_permissive_count}")
    spark.stop()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--event_type",
        type=str,
        required=True,
        help=f"Type of request event to run the pipeline for. Supported types: {SUPPORTED_EVENT_TYPES}",
    )
    args = parser.parse_args()
    main(args.event_type)

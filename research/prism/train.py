"""Training a Prism Filter.

Usage:

python research/prism/train.py \
  --tokenizer_name=starcoder \
  --train_data=/mnt/efs/augment/data/prism/2024-06-15_2024-07-03_elden_20k/train.jsonl \
  --test_data=/mnt/efs/augment/data/prism/2024-06-15_2024-07-03_elden_20k/test.jsonl \
  --ckpt_path=services/completion_host/single_model_server/prism_models/prism_eldenv3_2.json \
  --no_filter_data

python research/prism/train.py \
  --tokenizer_name=starcoder \
  --test_data=/mnt/efs/augment/data/prism/2024-07-03_2024-07-11_elden/test.jsonl  \
  --ckpt_path=services/completion_host/single_model_server/prism_models/prism_eldenv3_filtered.json
"""

import pathlib
import json
import xgboost as xgb
import argparse
from sklearn.metrics import roc_auc_score

import base.tokenizers
from base.datasets.completion import CompletionDatum
from base.completion_filter.extract_completion_filter_features import (
    FeatureExtractorFactory,
    FeatureExtractorBase,
    CompletionFilterFeaturesBase,
)


def load_completion_datum(json_file: str) -> list[CompletionDatum]:
    """Loads a list of completion datum from a json file."""
    with open(json_file, "r", encoding="utf-8") as f:
        return [CompletionDatum.from_dict(json.loads(line)) for line in f]  # type: ignore


def fliter_data(data: list[CompletionDatum]):
    """Filter data."""
    # Only keeps data whose resolution.timestamp - response.timestamp is between 5 and 95 percentile.
    # 5th and 95th percentile.
    min_delta, max_delta = 0.1, 6.9
    print(
        f"Keep data whose resolution.timestamp - response.timestamp is between: {min_delta} and {max_delta}"
    )
    output = []
    for d in data:
        assert d.resolution is not None
        if (
            min_delta
            <= (d.resolution.timestamp - d.response.timestamp).total_seconds()
            <= max_delta
        ):
            output.append(d)
    return output


def to_dmatrix(
    data: list[CompletionFilterFeaturesBase], Y: list, features
) -> xgb.DMatrix:
    """Converts a list of typed dicts into an XGBoost DMatrix."""
    assert len(data) == len(Y)
    X = []
    for d in data:
        cache = []
        for key in features:
            cache.append(d[key])
        X.append(cache)

    return xgb.DMatrix(
        X,
        label=Y,
        feature_names=features,
    )


def create_features(
    c: CompletionDatum,
    tokenizer: base.tokenizers.Tokenizer,
    feature_extractor: FeatureExtractorBase,
) -> CompletionFilterFeaturesBase:
    """Creates features for a completion datum."""
    return feature_extractor.extract_completion_filter_features(
        token_ids=c.response.token_ids,
        log_probs=c.response.token_log_probs,
        tokenizer=tokenizer,
        prefix=c.request.prefix,
        path=c.request.path,
    )


def datum_to_dmatrix(
    data: list[CompletionDatum], tokenizer: base.tokenizers.Tokenizer, feature_extractor
) -> xgb.DMatrix:
    """Converts a list of dicts into an XGBoost DMatrix."""
    feature_names = list(create_features(data[0], tokenizer, feature_extractor).keys())
    extract_features = lambda c: create_features(c, tokenizer, feature_extractor)  # noqa: E731
    extract_labels = lambda c: 0 if c.resolution.accepted else 1  # noqa: E731
    return to_dmatrix(
        list(map(extract_features, data)),
        list(map(extract_labels, data)),
        feature_names,
    )


def main(args):
    print(f"Args: {args}")

    print("Loading data...")
    train_data = load_completion_datum(args.train_data) if args.train_data else []
    test_data = load_completion_datum(args.test_data) if args.test_data else []
    if not args.no_filter_data and train_data:
        train_data = fliter_data(train_data)

    tokenizer = base.tokenizers.create_tokenizer_by_name(args.tokenizer_name)
    feature_extractor = FeatureExtractorFactory.create(args.extract_features_version)

    dtrain = (
        datum_to_dmatrix(train_data, tokenizer, feature_extractor)
        if train_data
        else None
    )
    deval = (
        datum_to_dmatrix(test_data, tokenizer, feature_extractor) if test_data else None
    )

    print("Training data:", dtrain.num_row() if dtrain else None)
    print("Eval data:", deval.num_row() if deval else None)

    if args.ckpt_path:
        ckpt_path = pathlib.Path(args.ckpt_path)
        model = None
        if args.train_data:
            if ckpt_path.exists():
                raise ValueError(f"Checkpoint already exists: {args.ckpt_path}.")
            print(f"Training model and saving to checkpoint: {args.ckpt_path}")
        else:
            if not ckpt_path.exists():
                raise ValueError(f"Checkpoint does not exist: {args.ckpt_path}.")
            model = xgb.Booster(model_file=args.ckpt_path)
    else:
        model = None
        print("Training model without saving a checkpoint.")
    if input("Continue? [Y/n]").lower() == "n":
        return

    if args.train_data and dtrain is not None:
        print("Training model...")
        model = xgb.train(
            dict(
                max_depth=1,
                eta=1.0,
                objective="binary:logistic",
                random_state=0,
            ),
            dtrain,
            num_boost_round=96,
        )
        bst_predict = model.predict(dtrain)
        print("Training AUCROC", roc_auc_score(dtrain.get_label(), bst_predict))
        if args.ckpt_path:
            model.save_model(args.ckpt_path)

    if model is not None and args.test_data and deval is not None:
        labels = deval.get_label()
        bst_predict = model.predict(deval)
        print("Eval AUCROC", roc_auc_score(labels, bst_predict))

        print("== True Positive Rate vs False Positive Rate ==")
        total_accepted, total_rejected = (labels == 0).sum(), (labels == 1).sum()
        n_a, n_r, prev = 0, 0, 0
        for predict, label in sorted(zip(bst_predict, labels), reverse=True):
            n_a += label == 0
            n_r += label == 1
            tpr, fpr = n_r / total_rejected, n_a / total_accepted
            if tpr - prev > 0.02:
                print(
                    f"True Positive: {tpr:.3f}, False Positive: {fpr:.3f}, Threshold: {predict:.3f}"
                )
                prev = tpr


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--tokenizer_name",
        type=str,
        help="The tokenizer to use.",
        required=True,
    )
    parser.add_argument(
        "--train_data",
        type=str,
        default=None,
        help="The path to the training data. If not specified, the model will be loaded from the checkpoint.",
    )
    parser.add_argument(
        "--test_data",
        type=str,
        default=None,
        help="The path to the test data. If not specified, the model is not evaluated on test data.",
    )
    parser.add_argument(
        "--ckpt_path",
        type=str,
        default=None,
        help="The path to the checkpoint to use for training.",
    )
    parser.add_argument(
        "--no_filter_data",
        action="store_true",
        default=False,
        help="Whether to filter data.",
    )
    parser.add_argument(
        "--extract_features_version",
        type=str,
        default="feature_extractor_v1",
        help="The version of the feature extractor to use.",
    )
    args = parser.parse_args()

    main(args)

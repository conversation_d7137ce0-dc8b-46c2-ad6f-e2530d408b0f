"""Utilities for next edit Prism."""

from typing import Sequence, Mapping, Union

import xgboost as xgb

Number = Union[int, float]


def to_dmatrix(
    data: Sequence[Mapping[str, Number]], label: Sequence[int], features: Sequence[str]
) -> xgb.DMatrix:
    """Converts a list of typed dicts into an XGBoost DMatrix."""
    assert len(data) == len(label)
    data_ = []
    for d in data:
        data_.append([d[key] for key in features])

    return xgb.DMatrix(
        data_,
        label=label,
        feature_names=features,
    )

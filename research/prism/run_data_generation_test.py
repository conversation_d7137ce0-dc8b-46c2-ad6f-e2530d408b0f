"""
Unit tests for run_data_generation.py.

pytest research/prism/run_data_generation_test.py -rF
"""

from research.prism.run_data_generation import get_train_eval_data
from datetime import datetime
from unittest.mock import patch


class CompletionDatumMock:
    def __init__(self, request_id):
        self.request_id = request_id

    def __str__(self):
        return self.request_id


class CompletionDatasetMock:
    def __init__(self):
        self.data = [CompletionDatumMock(f"req {i}") for i in range(10)]

    def get_completions(self):
        return self.data

    def __str__(self):
        return str(self.data)


def test_get_train_eval_data():
    """Test get_train_eval_data."""
    num_accepted = 10
    num_rejected = 10
    completion_model_name = "elden"
    start_date = datetime.fromisoformat("2024-06-15")
    end_date = datetime.fromisoformat("2024-07-03")
    test_size = 0.1

    # mock transform_completion_datum
    with patch(
        "research.prism.run_data_generation.CompletionDataset.create"
    ) as mock_create:
        mock_create.return_value = CompletionDatasetMock()
        with patch(
            "research.prism.run_data_generation.transform_completion_datum"
        ) as mock_transform:
            mock_transform.side_effect = lambda x: x
            train_data, test_data = get_train_eval_data(
                num_accepted,
                num_rejected,
                completion_model_name,
                start_date,
                end_date,
                test_size,
            )
            assert len(train_data) == 18
            assert len(test_data) == 2


def test_get_train_eval_data_all_test():
    """Test get_train_eval_data with all test data."""
    num_accepted = 10
    num_rejected = 10
    completion_model_name = "elden"
    start_date = datetime.fromisoformat("2024-06-15")
    end_date = datetime.fromisoformat("2024-07-03")
    test_size = 1.0

    with patch(
        "research.prism.run_data_generation.CompletionDataset.create"
    ) as mock_create:
        mock_create.return_value = CompletionDatasetMock()
        with patch(
            "research.prism.run_data_generation.transform_completion_datum"
        ) as mock_transform:
            mock_transform.side_effect = lambda x: x
            train_data, test_data = get_train_eval_data(
                num_accepted,
                num_rejected,
                completion_model_name,
                start_date,
                end_date,
                test_size,
            )
            assert len(train_data) == 0
            assert len(test_data) == 20


def test_get_train_eval_data_all_train():
    """Test get_train_eval_data with all train data."""
    num_accepted = 10
    num_rejected = 10
    completion_model_name = "elden"
    start_date = datetime.fromisoformat("2024-06-15")
    end_date = datetime.fromisoformat("2024-07-03")
    test_size = 0.0

    with patch(
        "research.prism.run_data_generation.CompletionDataset.create"
    ) as mock_create:
        mock_create.return_value = CompletionDatasetMock()
        with patch(
            "research.prism.run_data_generation.transform_completion_datum"
        ) as mock_transform:
            mock_transform.side_effect = lambda x: x
            train_data, test_data = get_train_eval_data(
                num_accepted,
                num_rejected,
                completion_model_name,
                start_date,
                end_date,
                test_size,
            )
            assert len(train_data) == 20
            assert len(test_data) == 0

# Prism

A Prism filter is a model that classifies completions as good or bad and is intended to be used to filter out low-quality completions. The Prism filter model must be trained and evaluated separately for each completion model. This directory contains the data generation code and training code for the Prism filter.
V2 filter model data, training, and evaluation code is in the `xgboost_completion_filter/` directory. Please see the `xgboost_completion_filter/README.md` for more details.

## Data Generation

The Prism filter is trained on data collected from dogfood with the accept/reject labels from the `run_data_generation.py` script. This script retrieves data for the necessary completion model from BigQuery and saves it to a JSONL file. This script allows the user to specify the completion model, the start and end dates for the data, the number of accepted and rejected examples, and the test size. For example, the following command will retrieve 10,000 accepted and 10,000 rejected examples for the elden model from 2024-06-15 to 2024-07-03 and save them to a JSONL file:

python research/prism/run_data_generation.py \
--completion_model_name=eldenv3-15b \
--start_date=2024-06-15 \
--end_date=2024-07-03 \
--num_accepted=10000 \
--num_rejected=10000 \
--test_size=0.1

The dataset is saved to a directory with the following format:
<mount_location>/data/prism/<state_date>_<end_date>_<completion_model_name>_{<num_accepted>+<num_rejected>/1000}k>/


## Model Usage
To train and evaluate the Prism filter, run the following command:

python research/prism/train.py \
--tokenizer_name=starcoder \
--train_data=/path/to/train/data.jsonl \
--test_data=/path/to/test/data.jsonl \
--ckpt_path=/path/to/checkpoint.json

For example:

python research/prism/train.py \
  --tokenizer_name=starcoder
  --train_data=/mnt/efs/augment/data/prism/2024-06-15_2024-07-03_elden_20k/train.jsonl \
  --test_data=/mnt/efs/augment/data/prism/2024-06-15_2024-07-03_elden_20k/test.jsonl \
  --ckpt_path=services/completion_host/single_model_server/prism_models/prism_eldenv3_2.json

This will train the Prism filter on the specified training data and evaluate it on the specified test data. The trained model will be saved to the specified checkpoint path.

## Data Filtering

The Prism filter uses a data filtering mechanism to remove low-quality data from the training and test sets. This is done by filtering out data whose resolution timestamp - response timestamp is not between the 5th and 95th percentiles.

## Model Training

The Prism filter is trained using the XGBoost library. The model is trained on the training data and evaluated on the test data. The trained model is saved to the specified checkpoint path. As input features depend on the completion model, the Prism filter model must be trained and evaluated separately for each completion model. Features such as minimum/median/maximum token probability, leading average, moving average, and number of lines and token diversity are used as model input features. These features are extracted from:

token_ids: Token IDs from the response.
log_probs: Log probabilities of the response tokens.
tokenizer: The tokenizer used to process the input.

## Evaluation Metrics
The Prism filter is evaluated using the AUC-ROC metric. The true positive rate and false positive rate are also calculated and printed to the console.

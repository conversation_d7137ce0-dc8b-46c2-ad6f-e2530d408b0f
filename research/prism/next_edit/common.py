from datetime import datetime
from typing import Literal

from pydantic import BaseModel


class Suggestion(BaseModel):
    """Dataclass for a single suggestion."""

    request_id: str
    suggest_id: str
    # Features from generation response.
    prompt: str
    prompt_token_ids: list[int]
    log_probs: list[float]
    token_ids: list[int]
    # Features from suggestion response.
    existing_code: str
    suggested_code: str
    # Features from session events.
    event_name: list[str]
    event_time: list[datetime]
    # Derived features.
    user_action: (
        Literal["accepted", "weak_rejected", "strong_rejected", "no_hover_shown"] | None
    ) = None
    time_to_action: float | None = None
    line_distance: int | None = None

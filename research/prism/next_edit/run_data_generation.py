"""<PERSON><PERSON>t to generate synthetic data for next edit.

Usage:

python research/prism/next_edit/run_data_generation.py \
--start_date=2024-09-25 \
--end_date=2024-10-01 \
--test_size=0.1 \
--timeout=7200

"""

import collections
import json
import random
import time
from datetime import datetime
from pathlib import Path
from typing import Iterable, TypeVar

import more_itertools
import tqdm
from absl import app, flags
from google.cloud import bigquery

from base.datasets import tenants
from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.pipeline import Pipeline
from base.ranges import CharRange
from research.core.data_paths import canonicalize_path
from research.prism.next_edit.common import Suggestion

_START_DATE = flags.DEFINE_string(
    "start_date", None, "Start date of the data in YYYY-MM-DD format.", required=True
)
_END_DATE = flags.DEFINE_string(
    "end_date", None, "End date of the data in YYYY-MM-DD format.", required=True
)
_TEST_SIZE = flags.DEFINE_float(
    "test_size",
    0.0,
    "Fraction of data to use for test set.",
    lower_bound=0.0,
    upper_bound=1.0,
)
_MAX_NUM_EXAMPLE = flags.DEFINE_integer(
    "max_num_example",
    None,
    "Maximum number of examples to generate.",
    lower_bound=0,
)
_TIMEOUT = flags.DEFINE_integer(
    "timeout",
    7200,
    "Timeout in seconds for the data pipeline.",
    lower_bound=0,
)

_TENANT = tenants.DOGFOOD_SHARD
_MODE = "BACKGROUND"
OUTPUT_DIR = Path(canonicalize_path("data/prism/next_edit"))
_RANDOM_SEED = 42

# Each row contains data for a single suggestion.
# TODO: The query is slow. To be improved.
QUERY = f"""
WITH
  request AS (
  SELECT
    request_id,
    raw_json,
    time
  FROM
    `staging_request_insight_full_export_dataset.next_edit_host_request`
  WHERE
    tenant = @tenant
    AND DATE(time) >= @start_date
    AND DATE(time) < @end_date
    AND JSON_EXTRACT_SCALAR(raw_json, "$.request.mode") = "{_MODE}"),
  response AS(
  SELECT
    request_id,
    ARRAY_AGG(raw_json) AS raw_json,
  FROM (
    SELECT
      request_id,
      time,
      raw_json,
    FROM
      `staging_request_insight_full_export_dataset.next_edit_host_response`
    WHERE
      tenant = @tenant
      AND JSON_EXTRACT(raw_json, "$.suggestions") IS NOT NULL
      OR JSON_EXTRACT(raw_json, "$.generation") IS NOT NULL )
  GROUP BY
    request_id),
  session AS (SELECT
  request_id,
  sid,
  ARRAY_AGG(event_name) AS event_name,
  ARRAY_AGG(time) AS time,
FROM (
  WITH
    SESSION AS (
    SELECT
      JSON_EXTRACT_SCALAR(raw_json, "$.related_suggestion_id") AS sid,
      JSON_EXTRACT_SCALAR(raw_json, "$.related_request_id") AS request_id,
      JSON_EXTRACT_SCALAR(raw_json, "$.event_name") AS event_name,
      time,
    FROM
      `system-services-prod.staging_request_insight_full_export_dataset.next_edit_session_event`
    WHERE
      tenant="dogfood-shard"
      AND JSON_EXTRACT_SCALAR(raw_json, "$.related_suggestion_id") IS NOT NULL),
    METADATA AS (
    SELECT
      request_id,
      JSON_VALUE(raw_json, "$.user_id") AS user_id,
      JSON_VALUE(raw_json, "$.user_agent") AS user_agent,
      CAST(REGEXP_EXTRACT(JSON_VALUE(raw_json, "$.user_agent"), "Augment.vscode-augment/0.([0-9]+).0") AS INT64) AS version
    FROM
      `staging_request_insight_full_export_dataset.request_metadata`
    WHERE
      NOT STARTS_WITH(JSON_VALUE(raw_json, "$.user_agent"), 'AugmentHealthCheck')
      AND NOT STARTS_WITH(JSON_VALUE(raw_json, "$.user_agent"), 'augment_review_bot')
      AND NOT STARTS_WITH(JSON_VALUE(raw_json, "$.user_agent"), 'Augment-EvalHarness')
      AND tenant = @tenant
      -- Excludes eng who are actively working on the feature.
      AND JSON_VALUE(raw_json, "$.user_id") NOT IN ("joel",
        "arunchaganty")
      -- Client Version. This effects what events we are recording.
      AND CAST(REGEXP_EXTRACT(JSON_VALUE(raw_json, "$.user_agent"), "Augment.vscode-augment/0.([0-9]+).0") AS INT64) >= 229)
  SELECT
    SESSION.request_id AS request_id,
    SESSION.sid AS sid,
    SESSION.time AS time,
    SESSION.event_name AS event_name,
  FROM
    SESSION
  JOIN
    METADATA
  USING
    (request_id)
  ORDER BY
    SESSION.sid,
    SESSION.time)
GROUP BY
  request_id,
  sid)
SELECT
  request.request_id,
  session.sid AS suggest_id,
  request.raw_json AS request_json,
  response.raw_json AS response_json,
  session.event_name AS event_name,
  session.time AS event_time
FROM
  request
JOIN
  response
USING
  (request_id)
JOIN
  session
USING
  (request_id)
ORDER BY
  request.request_id
"""


def _find_suggestion_response(row) -> None | dict:
    """Find the suggestion response for a given suggestion."""
    suggestion_id = row["suggest_id"]
    for response in row["response_json"]:
        if "suggestions" in response:
            if (
                response["suggestions"][0]["result"]["suggested_edit"]["suggestion_id"]
                == suggestion_id
            ):
                return response


def _find_generation_response(row) -> None | dict:
    """Find the generation response for a given suggestion."""
    s = _find_suggestion_response(row)
    if not s or "suggestions" not in s or len(s["suggestions"]) == 0:
        return None
    generation_id = s["suggestions"][0]["generation_id"]
    for response in row["response_json"]:
        if "generation" in response:
            if response["generation"][0]["generation_id"] == generation_id:
                return response


def _get_old_file(request):
    return (
        request.get("prefix", "")
        + request.get("selected_text", "")
        + request.get("suffix", "")
    )


def _char_to_line_num(file, char: int):
    from base.ranges.line_map import LineMap

    lmap = LineMap(file)
    return lmap.crange_to_lrange(CharRange(char, char)).start


def _current_cursor_char(request):
    return int(
        len(request.get("prefix", "")) + len(request.get("selected_text", "")) / 2.0
    )


def _get_real_diffs(suggested_edit):
    real_diffs = []

    existing = suggested_edit.get("existing_code", "")
    suggested = suggested_edit.get("suggested_code", "")
    all_diffs = suggested_edit["diff_spans"]

    # print(all_diffs)
    for diff in all_diffs:
        old_span = existing[
            diff["original"].get("start", 0) : diff["original"].get("stop", 0)
        ]
        new_span = suggested[
            diff["updated"].get("start", 0) : diff["updated"].get("stop", 0)
        ]
        if old_span != new_span:
            # print(old_span, new_span)
            real_diffs.append((diff, old_span, new_span))

    return real_diffs


def _suggest_start_char(suggest):
    start = suggest["result"]["suggested_edit"].get("char_start", 0)
    real_diffs = _get_real_diffs(suggest["result"]["suggested_edit"])
    # return real_diffs
    return start + real_diffs[0][0]["original"].get("start", 0)


def _line_number_delta(row) -> int | None:
    r = row["request_json"]["request"]
    s = _find_suggestion_response(row)
    if not s or "suggestions" not in s or len(s["suggestions"]) == 0:
        return None
    content = _get_old_file(r)

    current_line = _char_to_line_num(
        content,
        _current_cursor_char(r),
    )
    suggest_line = _char_to_line_num(
        content,
        _suggest_start_char(s["suggestions"][0]),
    )
    return abs(current_line - suggest_line)


def _classify_suggest(row) -> Suggestion | None:
    request_id = row["request_id"]
    suggest_id = row["suggest_id"]
    # Only sort by event_time.
    event_time, event_name = zip(
        *sorted(zip(row["event_time"], row["event_name"]), key=lambda x: x[0])
    )
    suggest_dict = _find_suggestion_response(row)
    generation_dict = _find_generation_response(row)

    if (
        not event_name
        or event_name[0] != "nonempty-suggestion-added"
        or not suggest_dict
        or not generation_dict
    ):
        return
    prompt = generation_dict["generation"][0]["generation_prompt"]["text"]
    prompt_token_ids = generation_dict["generation"][0]["generation_prompt"][
        "token_ids"
    ]
    log_probs = generation_dict["generation"][0]["generation_output"]["log_probs"]
    token_ids = generation_dict["generation"][0]["generation_output"]["token_ids"]
    existing_code = suggest_dict["suggestions"][0]["result"]["suggested_edit"].get(
        "existing_code", ""
    )
    suggested_code = suggest_dict["suggestions"][0]["result"]["suggested_edit"].get(
        "suggested_code", ""
    )
    line_distance = _line_number_delta(row)
    timestamp_added = event_time[0]
    hover_shown = False

    for t, event in zip(event_time, event_name):
        if event == "hover-shown":
            hover_shown = True
        elif event in ["accept", "accept-and-next-triggered"]:
            return Suggestion(
                request_id=request_id,
                suggest_id=suggest_id,
                prompt=prompt,
                prompt_token_ids=prompt_token_ids,
                log_probs=log_probs,
                token_ids=token_ids,
                existing_code=existing_code,
                suggested_code=suggested_code,
                event_name=event_name,
                event_time=event_time,
                user_action="accepted",
                time_to_action=(t - timestamp_added).total_seconds(),
                line_distance=line_distance,
            )
        elif event == "reject":
            return Suggestion(
                request_id=request_id,
                suggest_id=suggest_id,
                prompt=prompt,
                prompt_token_ids=prompt_token_ids,
                log_probs=log_probs,
                token_ids=token_ids,
                existing_code=existing_code,
                suggested_code=suggested_code,
                event_name=event_name,
                event_time=event_time,
                user_action="strong_rejected",
                time_to_action=(t - timestamp_added).total_seconds(),
                line_distance=line_distance,
            )
        elif event in (
            "nonempty-suggestion-becomes-stale",
            "nonempty-suggestion-dropped",
            "nonempty-suggestion-invalidated",
        ):
            # The suggest is no longer shown.
            if not hover_shown:
                return Suggestion(
                    request_id=request_id,
                    suggest_id=suggest_id,
                    prompt=prompt,
                    prompt_token_ids=prompt_token_ids,
                    log_probs=log_probs,
                    token_ids=token_ids,
                    existing_code=existing_code,
                    suggested_code=suggested_code,
                    event_name=event_name,
                    event_time=event_time,
                    user_action="no_hover_shown",
                    time_to_action=(t - timestamp_added).total_seconds(),
                    line_distance=line_distance,
                )
            else:
                return Suggestion(
                    request_id=request_id,
                    suggest_id=suggest_id,
                    prompt=prompt,
                    prompt_token_ids=prompt_token_ids,
                    log_probs=log_probs,
                    token_ids=token_ids,
                    existing_code=existing_code,
                    suggested_code=suggested_code,
                    event_name=event_name,
                    event_time=event_time,
                    user_action="weak_rejected",
                    time_to_action=(t - timestamp_added).total_seconds(),
                    line_distance=line_distance,
                )


def _validate_date_format(date_string: str):
    datetime.strptime(date_string, "%Y-%m-%d")


T = TypeVar("T")


def _flat_iter(batches: Iterable[Iterable[T]]) -> Iterable[T]:
    """Flatten an iterable of iterables into a single iterable."""
    for batch in batches:
        for item in batch:
            yield item


def _train_test_split(data: Iterable[Suggestion], output_dir: Path, test_size: float):
    """Split data into train and test sets and write to files."""
    train_file = output_dir / f"{_MODE}_train.jsonl"
    test_file = output_dir / f"{_MODE}_test.jsonl"
    pbar = tqdm.tqdm(data, desc="Processing batches")
    random.seed(_RANDOM_SEED)
    train_counter = collections.Counter()
    test_counter = collections.Counter()
    start_time = time.time()

    # TODO(vzhao): The data pipeline fails on some corner cases.
    with train_file.open("w") as train_f, test_file.open("w") as test_f:
        for value in pbar:
            pbar.set_description(f"{train_counter + test_counter}")
            if random.random() < test_size:
                test_counter[value.user_action] += 1
                test_f.write(value.model_dump_json() + "\n")
            else:
                train_counter[value.user_action] += 1
                train_f.write(value.model_dump_json() + "\n")

            if (
                _MAX_NUM_EXAMPLE.value
                and train_counter.total() + test_counter.total()
                >= _MAX_NUM_EXAMPLE.value
            ):
                break
            if _TIMEOUT.value and time.time() - start_time > _TIMEOUT.value:
                break

    print(f"Train: {train_counter}")
    print(f"Test: {test_counter}")
    stats = {"train": dict(train_counter), "test": dict(test_counter)}
    stats_file = output_dir / f"{_MODE}_stats.json"
    with stats_file.open("w") as f:
        json.dump(stats, f, indent=2)


def main(argv):
    _validate_date_format(_START_DATE.value)
    _validate_date_format(_END_DATE.value)
    output_dir = OUTPUT_DIR / f"{_START_DATE.value}_{_END_DATE.value}"
    output_dir.mkdir(parents=True, exist_ok=False)

    query_params = [
        bigquery.ScalarQueryParameter("tenant", "STRING", _TENANT.name),
        bigquery.ScalarQueryParameter("start_date", "DATE", _START_DATE.value),
        bigquery.ScalarQueryParameter("end_date", "DATE", _END_DATE.value),
    ]
    bigquery_client = bigquery.Client(
        project=_TENANT.project_id, credentials=get_gcp_creds()[0]
    )
    rows = bigquery_client.query_and_wait(
        QUERY,
        job_config=bigquery.QueryJobConfig(query_parameters=query_params),
        page_size=128,
    )

    def _batch_process(batch) -> list[Suggestion]:
        output = []
        for row in batch:
            value = _classify_suggest(row)
            if value is not None:
                output.append(value)
        return output

    pipeline = (
        Pipeline.from_source(more_itertools.batched(rows, 4))
        .and_then(_batch_process)
        .run(max_queue_size=16)
    )

    _train_test_split(
        data=_flat_iter(pipeline), output_dir=output_dir, test_size=_TEST_SIZE.value
    )


if __name__ == "__main__":
    app.run(main)

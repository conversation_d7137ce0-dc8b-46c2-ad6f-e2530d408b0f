"""Training a Prism Filter for next edit.

Usage:

# Train and test the model without saving the model.
python research/prism/next_edit/train.py \
  --train_data=/mnt/efs/augment/data/prism/next_edit/2024-09-25_2024-10-01/BACKGROUND_train.jsonl \
  --test_data=/mnt/efs/augment/data/prism/next_edit/2024-09-25_2024-10-01/BACKGROUND_test.jsonl

# Train and save the model.
python research/prism/next_edit/train.py \
  --train_data=/mnt/efs/augment/data/prism/next_edit/2024-09-25_2024-10-01/BACKGROUND_train.jsonl \
  --test_data=/mnt/efs/augment/data/prism/next_edit/2024-09-25_2024-10-01/BACKGROUND_test.jsonl \
  --ckpt_path=services/next_edit_host/server/prism_models/prism_next_edit_v1.json

# Eval a checkpoint.
python research/prism/next_edit/train.py \
  --test_data=/mnt/efs/augment/data/prism/next_edit/2024-09-25_2024-10-01/BACKGROUND_test.jsonl \
  --ckpt_path=services/next_edit_host/server/prism_models/prism_next_edit_v1.json
"""

import collections
from pathlib import Path
from typing import Sequence

import xgboost as xgb
from absl import app, flags
from sklearn.metrics import roc_auc_score

from base.next_edit_filter.extract_next_edit_filter_features import (
    FeatureExtractorV1,
    NextEditFeatureExtractorProtocol,
)
from base.tokenizers import StarCoder2Tokenizer
from research.prism import utils
from research.prism.next_edit.common import Suggestion

_TRAIN_DATA = flags.DEFINE_multi_string(
    "train_data", None, "Path to the training data."
)
_TEST_DATA = flags.DEFINE_multi_string(
    "test_data", None, "Path to the test data.", required=True
)
_CKPT_PATH = flags.DEFINE_string(
    "ckpt_path", None, "Path to the checkpoint to use for training."
)

# Constants to find interesting no hover shown suggests.
_MAX_LINE_DISTANCE = 10
_MIN_TIME_TO_ACTION = 1.8
_MAX_TIME_TO_ACTION = 60


def _load_suggestion_jsonl(
    json_files: Sequence[str],
) -> tuple[list[Suggestion], list[int]]:
    """Loads suggestion data from jsonl files."""
    data: list[Suggestion] = []
    labels: list[int] = []
    for file in json_files:
        with open(file, "r") as f:
            for line in f:
                suggest = Suggestion.model_validate_json(line)
                if (label := _filter_and_get_label(suggest)) is not None:
                    data.append(suggest)
                    labels.append(label)
    assert len(data) == len(labels)
    return data, labels


def _filter_and_get_label(suggest: Suggestion) -> int | None:
    if suggest.user_action == "accepted":
        return 0
    if suggest.user_action in ["weak_rejected", "strong_rejected"]:
        return 1
    if (
        suggest.user_action == "no_hover_shown"
        and suggest.line_distance
        and suggest.line_distance <= _MAX_LINE_DISTANCE
        and suggest.time_to_action
        and _MIN_TIME_TO_ACTION <= suggest.time_to_action <= _MAX_TIME_TO_ACTION
    ):
        return 1


def _to_dmatrix(
    data: list[Suggestion],
    labels: list[int],
    feature_extractor: NextEditFeatureExtractorProtocol,
) -> xgb.DMatrix:
    """Converts a list of typed dicts into an XGBoost DMatrix."""
    assert len(data) == len(labels)
    data_ = [
        feature_extractor.extract_features(d.log_probs, d.token_ids, d.prompt_token_ids)
        for d in data
    ]
    return utils.to_dmatrix(data_, labels, feature_extractor.get_features_name())


def main(args):
    del args
    tokenizer = StarCoder2Tokenizer()
    feature_extractor = FeatureExtractorV1(tokenizer)
    print("Loading data...")
    dtrain = None
    if _TRAIN_DATA.value:
        train_data, train_labels = _load_suggestion_jsonl(_TRAIN_DATA.value)
        print(
            f"Train set size: {len(train_data)} ({len(train_labels) - sum(train_labels)} positives)"
        )
        dtrain = _to_dmatrix(train_data, train_labels, feature_extractor)

    dtest, test_data = None, None
    if _TEST_DATA.value:
        test_data, test_labels = _load_suggestion_jsonl(_TEST_DATA.value)
        print(
            f"Test set size: {len(test_data)} ({len(test_labels) - sum(test_labels)} positives)"
        )
        dtest = _to_dmatrix(test_data, test_labels, feature_extractor)

    model = None
    if dtrain is not None:
        model = xgb.train(
            dict(
                max_depth=1,
                eta=1.0,
                objective="binary:logistic",
                random_state=0,
            ),
            dtrain,
            num_boost_round=96,
        )
        bst_predict = model.predict(dtrain)
        print("Training AUCROC", roc_auc_score(dtrain.get_label(), bst_predict))
        if _CKPT_PATH.value:
            if Path(_CKPT_PATH.value).exists():
                raise ValueError(f"Checkpoint already exists: {_CKPT_PATH.value}.")
            model.save_model(_CKPT_PATH.value)

    if model is None and _CKPT_PATH.value:
        model = xgb.Booster(model_file=_CKPT_PATH.value)

    if model is not None and dtest is not None and test_data is not None:
        user_action_counter = collections.Counter([s.user_action for s in test_data])

        print(
            "Eval AUCROC",
            roc_auc_score(dtest.get_label(), preds := model.predict(dtest)),
        )
        print("== Feature Importance ==")
        importance = model.get_score(importance_type="gain")
        sorted_importance = sorted(importance.items(), key=lambda x: x[1], reverse=True)
        print(len(sorted_importance))
        for feature, score in sorted_importance:
            print(f"{feature}: {score:.4f}")

        total_hover_shown = (
            user_action_counter.total() - user_action_counter["no_hover_shown"]
        )
        filtered_counter = collections.Counter()
        prev = 0

        print(
            "Accept Rate \t Hover Show Rate \t Accept Recall \t Not Accept Recall \t Filter Rate \t Threshold"
        )
        for predict, suggest in sorted(
            zip(preds, test_data), key=lambda x: x[0], reverse=True
        ):
            filtered_counter[suggest.user_action] += 1
            filter_rate = filtered_counter.total() / user_action_counter.total()
            # Print every 2% increase in filter rate up to 90%.
            if filter_rate - prev > 0.02 and filter_rate < 0.9:
                accept_rate = (
                    user_action_counter["accepted"] - filtered_counter["accepted"]
                ) / (user_action_counter.total() - filtered_counter.total())
                hover_show_rate = (
                    total_hover_shown
                    - filtered_counter.total()
                    + filtered_counter["no_hover_shown"]
                ) / (user_action_counter.total() - filtered_counter.total())
                accept_recall = (
                    user_action_counter["accepted"] - filtered_counter["accepted"]
                ) / user_action_counter["accepted"]
                not_accept_recall = (
                    user_action_counter.total()
                    - user_action_counter["accepted"]
                    - filtered_counter.total()
                    + filtered_counter["accepted"]
                ) / (user_action_counter.total() - user_action_counter["accepted"])
                print(
                    f"{accept_rate:.3f} \t {hover_show_rate:.3f} \t {accept_recall:.3f} \t {not_accept_recall:.3f} \t {filter_rate:.3f} \t {predict:.3f}"
                )
                prev = filter_rate


if __name__ == "__main__":
    app.run(main)

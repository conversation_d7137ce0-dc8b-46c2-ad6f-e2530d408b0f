"""<PERSON><PERSON>t to generate synthetic data for code editing.

Usage:

python research/prism/run_data_generation.py \
--completion_model_name=eldenv3-15b \
--start_date=2024-06-15 \
--end_date=2024-07-03 \
--num_accepted=10000 \
--num_rejected=10000 \
--test_size=0.1

python research/prism/run_data_generation.py \
--completion_model_name=eldenv3-15b \
--start_date=2024-07-03 \
--end_date=2024-07-11 \
--num_accepted=10000 \
--num_rejected=10000 \
--test_size=1.0

"""

import argparse
from datetime import datetime
from pathlib import Path

import tqdm
from sklearn.model_selection import train_test_split

from base.datasets import completion, tenants
from base.datasets.completion_dataset import CompletionDataset
from research.core.data_paths import canonicalize_path

TENANT_NAME = "dogfood"
OUTPUT_DIR = Path(canonicalize_path("data/prism/"))
RANDOM_STATE = 42


def transform_completion_datum(
    row: completion.CompletionDatum,
) -> completion.CompletionDatum:
    """Transforms a completion datum to only include the necessary fields. Used to save space and time loading dataset."""
    return completion.CompletionDatum(
        request_id=row.request_id,
        user_id="",
        request=completion.CompletionRequest(
            prefix=row.request.prefix,
            suffix=row.request.suffix,
            path=row.request.path,
            timestamp=row.request.timestamp,
            # The following is not used.
            blob_names=[],
            output_len=-1,
            position=None,
        ),
        response=completion.CompletionResponse(
            text=row.response.text,
            model=row.response.model,
            timestamp=row.response.timestamp,
            token_ids=row.response.token_ids,
            token_log_probs=row.response.token_log_probs,
            prompt_token_ids=row.response.prompt_token_ids,
            # The following is not used.
            tokens=[],
            skipped_suffix="",
            suffix_replacement_text="",
            unknown_blob_names=[],
            retrieved_chunks=[],
            prompt_tokens=[],
        ),
        resolution=completion.CompletionResolution(
            accepted=row.resolution.accepted,  # type: ignore
            timestamp=row.resolution.timestamp,  # type: ignore
        ),
        feedback=None,
    )


def get_train_eval_data_helper(
    num_accepted: int,
    num_rejected: int,
    completion_model_name: str,
    start_date: datetime,
    end_date: datetime,
    test_size: float,
    accepted_completion: bool,
):
    """Split the data into training and test sets."""

    limit = num_accepted if accepted_completion else num_rejected
    completion_type = "accepted" if accepted_completion else "rejected"

    print(f"Querying for {limit} {completion_type} completions...")
    # Query completions. We avoid the SQL ORDER BY and instead sort after, because this
    # can make the SQL query go over memory limits.
    completion_filters = CompletionDataset.Filters(
        model_names=[completion_model_name],
        timestamp_begin=start_date,
        timestamp_end=end_date,
        accepted_completion=accepted_completion,
    )
    completions = CompletionDataset.create(
        tenant=tenants.get_tenant(TENANT_NAME),
        filters=completion_filters,
        limit=limit,
        order_by="request_id",
        page_size=8192,
    )
    completions = completions.get_completions()
    completions = [
        transform_completion_datum(completion) for completion in tqdm.tqdm(completions)
    ]
    print(f"Found {len(completions)} {completion_type} completions.")

    if test_size == 0.0:
        return completions, []
    elif test_size == 1.0:
        return [], completions
    else:
        return train_test_split(
            completions,
            test_size=test_size,
            random_state=RANDOM_STATE,
        )


def get_train_eval_data(
    num_accepted: int,
    num_rejected: int,
    completion_model_name: str,
    start_date: datetime,
    end_date: datetime,
    test_size: float,
):
    """Get training and evaluation data."""

    a_train, a_test = get_train_eval_data_helper(
        num_accepted,
        num_rejected,
        completion_model_name,
        start_date,
        end_date,
        test_size,
        accepted_completion=True,
    )
    r_train, r_test = get_train_eval_data_helper(
        num_accepted,
        num_rejected,
        completion_model_name,
        start_date,
        end_date,
        test_size,
        accepted_completion=False,
    )

    print(f"Training data size (accepted, rejected): ({len(a_train)}, {len(r_train)})")
    print(f"Test data size (accepted, rejected): ({len(a_test)}, {len(r_test)})")

    return a_train + r_train, a_test + r_test


def create_output_dir(
    start_date: datetime,
    end_date: datetime,
    completion_model_name: str,
    num_accepted: int,
    num_rejected: int,
):
    """Get the output directory."""

    # If the base output directory (<mount_location>/data/prism) does not exist, raise an error.
    if not OUTPUT_DIR.is_dir():
        raise ValueError(f"{OUTPUT_DIR} does not exist.")

    # Add the start and end dates, completion model name, and number of examples to the output directory.
    # If this output directory already exists, raise an error.
    output_dir = (
        OUTPUT_DIR
        / f"{start_date.date()}_{end_date.date()}_{completion_model_name}_{(num_accepted + num_rejected)//1000}k"
    )
    print(f"Output directory: {output_dir}")
    output_dir.mkdir(parents=True, exist_ok=False)

    return output_dir


def main(
    start_date: datetime,
    end_date: datetime,
    completion_model_name: str,
    num_accepted: int,
    num_rejected: int,
    test_size: float,
):
    """Main function."""
    print(
        f"{start_date=}, {end_date=}, {completion_model_name=}, {num_accepted=}, {num_rejected=}, {test_size=}"
    )

    output_dir = create_output_dir(
        start_date, end_date, completion_model_name, num_accepted, num_rejected
    )
    train_data, test_data = get_train_eval_data(
        num_accepted,
        num_rejected,
        completion_model_name,
        start_date,
        end_date,
        test_size,
    )

    with (output_dir / "train.jsonl").open("w") as f:
        for datum in train_data:
            f.write(datum.to_json())  # type: ignore
            f.write("\n")

    with (output_dir / "test.jsonl").open("w") as f:
        for datum in test_data:
            f.write(datum.to_json())  # type: ignore
            f.write("\n")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--completion_model_name",
        type=str,
        required=True,
        help="The name of the completion model to use for training (e.g. eldenv3-15b). Collect completions that were generated with this model.",
    )
    parser.add_argument(
        "--start_date",
        type=datetime.fromisoformat,
        required=True,
        help="The date to use for the start of the model (format: YYYY-MM-DD).",
    )
    parser.add_argument(
        "--end_date",
        type=datetime.fromisoformat,
        required=True,
        help="The date to use for the end of the model (format: YYYY-MM-DD).",
    )
    parser.add_argument(
        "--num_accepted",
        type=int,
        default=5000,
        help="The number of accepted examples in the dataset (training and test).",
    )
    parser.add_argument(
        "--num_rejected",
        type=int,
        default=5000,
        help="The number of rejected examples in the dataset (training and test).",
    )
    parser.add_argument(
        "--test_size",
        type=float,
        default=0.2,
        help="The fraction of the dataset to include in the test split.",
    )
    args = parser.parse_args()
    assert args.test_size >= 0.0 and args.test_size <= 1.0

    main(
        args.start_date,
        args.end_date,
        args.completion_model_name,
        args.num_accepted,
        args.num_rejected,
        args.test_size,
    )

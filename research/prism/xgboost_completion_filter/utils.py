from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass, field
from datetime import datetime
import json
from pathlib import Path

import xgboost as xgb
from tabulate import tabulate

from research.prism.xgboost_completion_filter.evaluation import EvaluationManager


@dataclass
class EvalResult:
    score: Optional[float]
    size: int

    def __post_init__(self):
        self.score = round(self.score, 4) if self.score is not None else None


@dataclass
class CategoryEvalResult:
    old_result: EvalResult
    new_result: EvalResult

    @property
    def improvement(self) -> Optional[float]:
        if self.old_result.score is not None and self.new_result.score is not None:
            return round(self.new_result.score - self.old_result.score, 4)
        return None


@dataclass
class EvalResultsTable:
    results: Dict[str, Dict[Tuple[datetime, datetime], CategoryEvalResult]] = field(
        default_factory=dict
    )

    def add_result(
        self,
        category: str,
        test_range: Tuple[datetime, datetime],
        old_result: EvalResult,
        new_result: EvalResult,
    ):
        if category not in self.results:
            self.results[category] = {}
        self.results[category][test_range] = CategoryEvalResult(old_result, new_result)

    def get_categories(self) -> List[str]:
        return sorted(self.results.keys())

    def get_test_ranges(self) -> List[Tuple[datetime, datetime]]:
        return sorted(
            set(range for ranges in self.results.values() for range in ranges.keys())
        )

    def get_result(
        self, category: str, test_range: Tuple[datetime, datetime]
    ) -> Optional[CategoryEvalResult]:
        return self.results.get(category, {}).get(test_range)

    def average_value_and_size(
        self, category: str, value_getter
    ) -> Tuple[Optional[float], Optional[float]]:
        values = []
        sizes = []
        for result in self.results.get(category, {}).values():
            if value_getter(result) is not None:
                values.append(value_getter(result))
                sizes.append(result.old_result.size)

        if values:
            avg_values = sum(values) / len(values)
            avg_size = sum(sizes) / len(sizes)
            return round(avg_values, 4), round(avg_size, 0)
        return None, None


class CompareModels:
    def __init__(
        self,
        prev_model_evals: List[EvaluationManager],
        new_model_evals: List[EvaluationManager],
    ):
        self.eval_results = EvalResultsTable()
        self._process_evals(prev_model_evals, new_model_evals)
        self.categories = self.eval_results.get_categories()
        self.test_ranges = self.eval_results.get_test_ranges()

        self.matrix_overall = self.create_matrix(
            "Overall AUC", self.create_model_auc_rows
        )
        self.matrix_category = self.create_matrix(
            "Filter Category AUC Improvement (Old->New Model)",
            self.create_category_rows,
        )

    def _process_evals(self, prev_model_evals, new_model_evals):
        for prev_eval, new_eval in zip(prev_model_evals, new_model_evals):
            assert prev_eval.test_range == new_eval.test_range
            assert prev_eval.eval_results.keys() == new_eval.eval_results.keys()

            test_range = prev_eval.test_range
            for category, (old_score, old_size) in prev_eval.eval_results.items():
                new_score, new_size = new_eval.eval_results[category]
                assert old_size == new_size

                self.eval_results.add_result(
                    category,
                    test_range,
                    EvalResult(old_score, old_size),
                    EvalResult(new_score, new_size),
                )

    def create_matrix(self, header_name: str, create_rows_fn):
        header = self.create_header(header_name)
        data = create_rows_fn()
        return tabulate(data, headers=header, tablefmt="grid")

    def create_header(self, header_name: str):
        header = [header_name]
        for start_date, end_date in self.test_ranges:
            header.append(f"{start_date.date()} - {end_date.date()}")
        header.append("Average")
        return header

    def create_model_auc_rows(self):
        return [
            self.create_row("New Model AUC", lambda result: result.new_result.score),
            self.create_row("Old Model AUC", lambda result: result.old_result.score),
            self.create_row("AUC Improvement", lambda result: result.improvement),
        ]

    def create_category_rows(self):
        return [
            self.create_row(category, lambda result: result.improvement)
            for category in self.categories
            if category != "all"
        ]

    def create_row(self, row_name, value_getter):
        row = [row_name]
        category = "all" if row_name.startswith(("New", "Old", "AUC")) else row_name

        for test_range in self.test_ranges:
            result = self.eval_results.get_result(category, test_range)
            if result:
                value = value_getter(result)
                size = result.old_result.size
                if value is not None:
                    row.append(f"{value:.4f} ({size})")
                else:
                    row.append("N/A")
            else:
                row.append("N/A")

        avg_improvement, avg_size = self.eval_results.average_value_and_size(
            category, value_getter
        )
        if avg_improvement is not None and avg_size is not None:
            row.append(f"{avg_improvement:.4f} ({avg_size:.0f})")
        else:
            row.append("N/A")

        return row


def save_model(model: xgb.Booster, ckpt_dir: Optional[Path]) -> Optional[Path]:
    model_file = None
    if ckpt_dir:
        model_file = ckpt_dir / "model.json"
        model.save_model(model_file)
        print(f"Saved model to {model_file}")
    return model_file


def create_tp_fp_threshold_tables(evals: List[EvaluationManager]):
    """Create tables for true positive, false positive, and threshold values."""
    tables = []
    for eval in evals:
        tpr, fpr, thresholds = eval.tpr, eval.fpr, eval.thresholds
        table_data = []
        prev = -1
        for curr_tpr, curr_fpr, curr_threshold in zip(tpr, fpr, thresholds):
            if (curr_tpr - prev > 0.01 and curr_fpr < 0.05 and curr_fpr >= 0.005) or (
                curr_tpr - prev > 0.05 and curr_fpr >= 0.005
            ):
                prev = curr_tpr
                table_data.append(
                    [f"{curr_tpr:.3f}", f"{curr_fpr:.3f}", f"{curr_threshold:.3f}"]
                )

        headers = ["True Positive", "False Positive", "Threshold"]
        table = tabulate(table_data, headers=headers, tablefmt="grid")
        tables.append(table)
    return tables


def create_model_info_file(
    ckpt_dir: Path,
    model_file: Optional[Path],
    compare_models: CompareModels,
    new_model_evals: List[EvaluationManager],
    base_dirs: List[Path],
    files: List[str],
    prev_ckpt_path: str,
):
    model: xgb.Booster = new_model_evals[0].model
    model_features = model.feature_names
    assert model_features

    model_info_file = ckpt_dir / "model_info.txt"
    with model_info_file.open("w", encoding="utf-8") as f:
        # General Information
        f.write("Model Information\n")
        f.write("=================\n\n")

        # Data Source Information
        f.write("Data Source Information\n")
        f.write("------------------------\n")
        f.write("Base directories:\n")
        for base_dir in base_dirs:
            f.write(f"  - {str(base_dir)}\n")
        f.write("Data files:\n")
        for file in files:
            f.write(f"  - {file}.jsonl.zst\n")
        f.write("\n")

        # Features
        f.write("Model Features\n")
        f.write("--------------\n")
        f.write(f"Features: {', '.join(model_features)}\n\n")

        # Train and Eval Data Information
        f.write("Data Information\n")
        f.write("-----------------\n")
        for i, new_eval in enumerate(new_model_evals):
            f.write(f"Eval Set {i+1}:\n")
            f.write(f"  Test Data: {len(new_eval.test_data)} samples\n")
            f.write(
                f"  Eval Date Range (Inclusive): {new_eval.test_range[0].date()} to {new_eval.test_range[1].date()}\n"
            )
        f.write("\n")

        # Matrix Table
        f.write("Evaluation Results\n")
        f.write("-------------------------\n")
        f.write(f"New model checkpoint: {model_file or 'N/A'}\n")
        f.write(f"Old model checkpoint: {prev_ckpt_path}\n\n")
        f.write('Format of each cell is "EVAL_AUC (EVAL_SIZE)"\n')
        f.write(compare_models.matrix_overall)
        f.write("\n\n")
        f.write(
            "Each category below represents a subset of the evaluation data where a specific feature is present.\n"
        )
        f.write(
            "This analysis helps identify the new model's performance on samples with particular characteristics.\n"
        )
        f.write(compare_models.matrix_category)
        f.write("\n\n")

        # TP/FP/Threshold Tables
        f.write("True Positive / False Positive / Threshold Tables\n")
        f.write("------------------------------------------------\n")
        new_eval_tables = create_tp_fp_threshold_tables(new_model_evals)
        for i, table in enumerate(new_eval_tables):
            f.write(f"Eval Set {i+1}:\n")
            f.write(table)
            f.write("\n\n")

        # Feature Importance
        f.write("Feature Importance\n")
        f.write("------------------\n")
        importance_scores = model.get_score(importance_type="gain")
        importance_table = sorted(
            importance_scores.items(), key=lambda x: x[1], reverse=True
        )
        headers = ["Feature", "Importance Score"]
        f.write(tabulate(importance_table, headers=headers, tablefmt="grid"))
        f.write("\n\n")

        # Hyperparameters
        f.write("Model Hyperparameters\n")
        f.write("---------------------\n")
        params = json.loads(model.save_config())
        for param, value in params.items():
            f.write(f"{param}: {value}\n")
        f.write("\n")

        # Model File Location
        f.write("Model File\n")
        f.write("----------\n")
        f.write(f"Model saved at: {model_file or 'N/A'}\n")
        f.write("\n")

        # Threshold Information
        f.write("Threshold Information\n")
        f.write("---------------------\n")
        f.write("Threshold Value: TBD\n")

    print(f"Saved model and model info to {ckpt_dir}")

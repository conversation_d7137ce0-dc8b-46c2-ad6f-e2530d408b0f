"""
<PERSON><PERSON>t to train and evaluate xgboost filter.

Usage:
python research/prism/xgboost_completion_filter/train_and_eval.py \
--data_dir=/mnt/efs/augment/data/prism/completion \
--eval \
--prev_ckpt_path=services/completion_host/single_model_server/prism_models/prism_eldenv3.json \
--model_name=prism_eldenv4
"""

import argparse
from pathlib import Path
from typing import Optional
import xgboost as xgb
from research.core.data_paths import canonicalize_path

from research.prism.xgboost_completion_filter.data_loader import (
    find_data_directories,
    load_data,
)
from research.prism.xgboost_completion_filter.feature_extraction import (
    XGBoostFeatureDataSet,
    FeatureExtractorFactory,
)
from research.prism.xgboost_completion_filter.evaluation import (
    MultiDateBasedSplit,
    eval_model,
)
from research.prism.xgboost_completion_filter.model import ModelTrainer
from research.prism.xgboost_completion_filter.utils import (
    create_model_info_file,
    create_tp_fp_threshold_tables,
    CompareModels,
    save_model,
)
from base.tokenizers import create_tokenizer_by_name

CKPT_DIR = Path(canonicalize_path("checkpoints/prism/completion"))


def parse_args():
    parser = argparse.ArgumentParser(
        description="Train and evaluate XGBoost model for completion filtering"
    )
    parser.add_argument(
        "--data_dir",
        "-d",
        type=str,
        default=canonicalize_path("data/prism/completion"),
        help="Base directory containing data",
    )
    parser.add_argument(
        "--tokenizer_name",
        "-t",
        type=str,
        default="starcoder2",
        help="Name of tokenizer to use",
    )
    parser.add_argument(
        "--eval",
        "-e",
        action="store_true",
        help="Whether to evaluate the trainedmodel",
    )
    parser.add_argument(
        "--prev_ckpt_path",
        "-p",
        type=str,
        default=None,
        help="Path to previous checkpoint to compare against",
    )
    parser.add_argument(
        "--model_name",
        "-m",
        type=str,
        default=None,
        help="Name of the model to be saved. If not set, the model and model info file will not be saved.",
    )
    return parser.parse_args()


def main(
    data_dir: str,
    tokenizer_name: str,
    eval: bool,
    prev_ckpt_path: Optional[str],
    model_name: Optional[str],
):
    data_dirs = find_data_directories(Path(data_dir))
    print(f"Found data directories: {[str(d) for d in data_dirs]}")

    json_data, file_paths = load_data(data_dirs)
    tokenizer = create_tokenizer_by_name(tokenizer_name)
    xgboost_dataset = XGBoostFeatureDataSet(data=json_data, tokenizer=tokenizer)

    # The dates to evaluate on.
    cv = MultiDateBasedSplit(
        X=xgboost_dataset.data_points,
        test_ranges=[
            ("2024-07-01", "2024-07-02"),
            ("2024-08-01", "2024-08-02"),
            ("2024-08-30", "2024-08-31"),
        ],
    )

    trainer = ModelTrainer(
        xgboost_dataset, cv.train_indices, "feature_extractor_v2", verbose=True
    )
    new_model = trainer.train_model()

    comparison = None
    new_model_evals = None
    if eval:
        print("\nEvaluating on new model with test data")
        new_model_evals = eval_model(
            new_model, xgboost_dataset, cv, trainer.version, verbose=False
        )
        tables = create_tp_fp_threshold_tables(new_model_evals)
        print()
        for i, table in enumerate(tables):
            print(f"New Model Eval Set {i+1}:")
            print(table)
            print("\n")

        if prev_ckpt_path:
            print("\nEvaluating on previous model with test data")
            prev_model = xgb.Booster(model_file=prev_ckpt_path)
            prev_model_features = (
                set(prev_model.feature_names) if prev_model.feature_names else set()
            )
            prev_model_version = None
            for (
                version,
                feature_extractor,
            ) in FeatureExtractorFactory.versions().items():
                if prev_model_features == set(feature_extractor.get_feature_names()):
                    prev_model_version = version
                    break
            if not prev_model_version:
                raise ValueError(
                    f"Cannot find feature extractor version for previous model. Previous model features {prev_model_features} do not match any extractor."
                )

            prev_model_evals = eval_model(
                prev_model, xgboost_dataset, cv, prev_model_version, verbose=False
            )
            tables = create_tp_fp_threshold_tables(prev_model_evals)
            print()
            for i, table in enumerate(tables):
                print(f"Prev Model Eval Set {i+1}:")
                print(table)
                print("\n")

            comparison = CompareModels(prev_model_evals, new_model_evals)

            print("\nComparison Results:")
            print(comparison.matrix_overall)
            print()
            print(comparison.matrix_category)

    if not model_name:
        print("Not saving model.")
        return new_model

    ckpt_dir = CKPT_DIR / model_name
    ckpt_dir.mkdir(parents=True, exist_ok=True)
    model_file = save_model(new_model, ckpt_dir)

    if new_model_evals and prev_ckpt_path and comparison:
        create_model_info_file(
            ckpt_dir=ckpt_dir,
            model_file=model_file,
            compare_models=comparison,
            new_model_evals=new_model_evals,
            base_dirs=data_dirs,
            files=file_paths,
            prev_ckpt_path=prev_ckpt_path,
        )
    return new_model


if __name__ == "__main__":
    args = parse_args()
    main(
        args.data_dir,
        args.tokenizer_name,
        args.eval,
        args.prev_ckpt_path,
        args.model_name,
    )

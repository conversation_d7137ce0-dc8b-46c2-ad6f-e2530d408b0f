from typing import List, Dict
import xgboost as xgb
from research.prism.xgboost_completion_filter.feature_extraction import (
    XGBoostFeatureDataSet,
)


def to_dmatrix(data: List[Dict], Y: List[int], features: List[str]) -> xgb.DMatrix:
    """Converts a list of dicts into an XGBoost DMatrix."""
    assert len(data) == len(Y)
    X = []
    for d in data:
        cache = []
        for key in features:
            cache.append(d[key])
        X.append(cache)

    return xgb.DMatrix(
        X,
        label=Y,
        feature_names=features,
    )


class ModelTrainer:
    def __init__(
        self,
        xgboost_dataset: XGBoostFeatureDataSet,
        train_indices: List[int],
        version: str,
        verbose=False,
    ):
        self.train_data = [xgboost_dataset.data_points[i] for i in train_indices]
        self.feature_names = xgboost_dataset.get_feature_names(version)
        self.version = version

        if verbose:
            category_counts = XGBoostFeatureDataSet.classification_counts(
                self.train_data
            )
            print(f"\nTrain data size: {len(self.train_data)}")
            print(f"Train data category counts: {category_counts}")

    def train_model(self) -> xgb.Booster:
        x_train = [dp.get_features(self.version) for dp in self.train_data]
        y_train = [0 if dp.accepted else 1 for dp in self.train_data]

        dtrain = to_dmatrix(x_train, y_train, self.feature_names)
        model = xgb.train(
            dict(
                max_depth=8,
                eta=0.005,
                objective="binary:logistic",
                random_state=0,
                alpha=2,
                gamma=0.1,
            ),
            dtrain,
            num_boost_round=1000,
            verbose_eval=False,
        )
        self.model: xgb.Booster = model
        return model

"""
<PERSON><PERSON><PERSON> to prepare data for xgboost filter.
Note: Processing data takes a long time (1hr/40k requests).

Usage:
python research/prism/xgboost_completion_filter/data_download.py \
--start-date=2024-07-01 \
--end-date=2024-07-31 \
--query-tenant=dogfood-shard \
--model-name=elden \
--stages=query process verify
"""

import argparse
import json
import logging
from collections.abc import Iterable
from datetime import datetime
from pathlib import Path

import pandas as pd
import zstandard as zstd
from google.cloud import bigquery

from base.datasets import tenants
from base.datasets.completion import (
    CompletionDatum,
    CompletionResponse,
    CompletionRequest,
)
from base.datasets.completion_dataset_gcs import (
    CompletionDataset,
    Filters,
)
from base.datasets.gcp_creds import get_gcp_creds
from research.core.data_paths import canonicalize_path
from research.core.utils_for_file import read_jsonl_zst, write_jsonl_zst

logging.basicConfig(level=logging.INFO)

AVAILABLE_TENANTS = ["dogfood", "dogfood-shard"]
BASE_DIR = Path(canonicalize_path("data/prism/completion/"))
DATA_FOLDER_DIR_NAME = "{start_date}_{end_date}_{model_name}"
METADATA_CSV_NAME = "metadata_{tenant}.csv"
FULL_DATA_NAME = "data_{tenant}.jsonl.zst"


def get_query(
    start_date: str, end_date: str, query_tenant: str, model_name: str
) -> str:
    """Generate BigQuery SQL query."""
    return f"""
    WITH
    request AS (
        SELECT
            request_id,
            tenant,
            time,
            JSON_VALUE(sanitized_json, "$.model") AS model
        FROM `system-services-prod.us_staging_request_insight_analytics_dataset.completion_host_request`
        WHERE time >= "{start_date}"
        AND time <= "{end_date}"
        AND tenant = "{query_tenant}"
    ),
    response AS (
        SELECT
            request_id,
            character_count,
        FROM `system-services-prod.us_staging_request_insight_analytics_dataset.completion_host_response`
        WHERE time >= "{start_date}"
        AND time <= "{end_date}"
        AND character_count >= 1
    ),
    metadata AS (
        SELECT
            request_id,
            user_id,
            user_agent
        FROM `system-services-prod.us_staging_request_insight_analytics_dataset.human_request_metadata`
        WHERE time >= "{start_date}"
        AND time <= "{end_date}"
    ),
    resolution AS (
        SELECT
            request_id,
            accepted
        FROM `system-services-prod.us_staging_request_insight_analytics_dataset.completion_resolution`
        WHERE time >= "{start_date}"
        AND time <= "{end_date}"
    )
    SELECT
        request.request_id,
        MAX(request.tenant) AS tenant,
        MAX(request.model) AS model,
        MAX(request.time) AS time,
        MAX(metadata.user_id) AS user_id,
        MAX(metadata.user_agent) AS user_agent,
        MAX(resolution.accepted) AS accepted
    FROM request
    JOIN response USING (request_id)
    JOIN metadata USING (request_id)
    JOIN resolution USING (request_id)
    WHERE request.model LIKE '%{model_name}%'
    GROUP BY request.request_id
    """


def query_and_save_request_ids(
    start_date: str,
    end_date: str,
    query_tenant: str,
    model_name: str,
    metadata_csv_path: Path,
    bigquery_client: bigquery.Client,
):
    """Query BigQuery and save request IDs to a CSV file."""
    query = get_query(start_date, end_date, query_tenant, model_name)
    rows = bigquery_client.query_and_wait(query)
    logging.info(f"Found {rows.total_rows or -1} rows for {start_date} to {end_date}")

    schema = [field.name for field in rows.schema]
    data = [row.values() for row in rows]

    metadata_df = pd.DataFrame(data, columns=pd.Index(schema))
    metadata_df = metadata_df.sort_values(by=["time"])
    metadata_df.to_csv(metadata_csv_path, index=False)
    assert len(metadata_df) == len(
        set(metadata_df["request_id"])
    ), "Duplicate request IDs found"
    logging.info(f"Sorted and saved {len(metadata_df)} rows to {metadata_csv_path}")


def transform_completion_datum(row: CompletionDatum) -> CompletionDatum:
    """Transform a completion datum to include only necessary fields."""
    return CompletionDatum(
        request_id=row.request_id,
        user_id=row.user_id,
        user_agent=row.user_agent,
        request=CompletionRequest(
            prefix=row.request.prefix,
            suffix=row.request.suffix,
            path=row.request.path,
            timestamp=row.request.timestamp,
            position=row.request.position,
            blob_names=[],
            output_len=-1,
        ),
        response=CompletionResponse(
            text=row.response.text,
            model=row.response.model,
            timestamp=row.response.timestamp,
            token_ids=row.response.token_ids,
            token_log_probs=row.response.token_log_probs,
            prompt_token_ids=row.response.prompt_token_ids,
            tokens=row.response.tokens,
            skipped_suffix="",
            suffix_replacement_text="",
            unknown_blob_names=[],
            retrieved_chunks=[],
            prompt_tokens=[],
        ),
        resolution=row.resolution,
        feedback=None,
    )


def get_completions_data(
    tenant: str, request_ids: list[str]
) -> Iterable[CompletionDatum]:
    """Retrieve completion data for given request IDs."""
    return CompletionDataset.create_data_from_gcs(
        tenant=tenants.get_tenant(tenant), filters=Filters(request_ids=request_ids)
    )


def process_and_write_data(
    completions: Iterable[CompletionDatum], full_data_path: Path
):
    """Process and write completion data to a compressed JSON file."""
    logging.info(f"Writing completions to {full_data_path}...")
    with zstd.open(full_data_path, "w", encoding="utf-8") as f:
        for com in completions:
            datum_json = transform_completion_datum(com).to_json()
            datum_dict = json.loads(datum_json)
            json.dump(datum_dict, f)
            f.write("\n")


def remove_duplicates(full_data_path: Path):
    """Clean up data by removing duplicates."""
    full_data = read_jsonl_zst(full_data_path)
    unique_ids = set()
    new_full_data = []
    for datum in full_data:
        if datum["request_id"] in unique_ids:
            continue
        unique_ids.add(datum["request_id"])
        new_full_data.append(datum)
    write_jsonl_zst(full_data_path, new_full_data)


def verify_data(metadata_csv_path: Path, full_data_path: Path, model_name: str):
    """Verify the processed data."""
    metadata = pd.read_csv(metadata_csv_path)
    metadata_unique_ids = set(metadata["request_id"])
    assert (
        len(metadata) == len(metadata_unique_ids)
    ), f"Duplicate request IDs found in metadata. {len(metadata)=}, {len(metadata_unique_ids)=}"

    logging.info(f"Reading {full_data_path}")
    full_data = read_jsonl_zst(full_data_path)
    logging.info(
        f"Found {len(full_data)} {model_name} completions in {full_data_path}."
    )

    # We are unlikely to satisfy this condition.
    if len(full_data) != len(metadata):
        logging.warning(
            f"Number of completions does not match. Found {len(metadata)} in metadata, got {len(full_data)} in full data. This is expected, should be very close to the number of metadata rows, and should be investigated."
        )

    unique_ids = set(r["request_id"] for r in full_data)
    assert (
        len(unique_ids) == len(full_data)
    ), f"Duplicate request IDs found in full data. {len(unique_ids)=}, {len(full_data)=}"


def main(args):
    start_date = datetime.strptime(args.start_date, "%Y-%m-%d").date()
    end_date = datetime.strptime(args.end_date, "%Y-%m-%d").date()

    model_name = args.model_name
    data_folder = DATA_FOLDER_DIR_NAME.format(
        start_date=start_date, end_date=end_date, model_name=model_name
    )
    output_dir = BASE_DIR / data_folder
    output_dir.mkdir(parents=True, exist_ok=True)
    logging.info(f"Base Output Directory: {output_dir}")

    query_tenant = args.query_tenant
    metadata_csv_path = output_dir / METADATA_CSV_NAME.format(tenant=query_tenant)
    full_data_path = output_dir / FULL_DATA_NAME.format(tenant=query_tenant)

    if "query" in args.stages:
        # Step 1: Get completion IDs from BigQuery and save to a file
        gcp_creds, _ = get_gcp_creds()
        project_id = tenants.get_tenant(query_tenant).project_id
        bigquery_client = bigquery.Client(project=project_id, credentials=gcp_creds)

        query_and_save_request_ids(
            start_date.isoformat(),
            end_date.isoformat(),
            query_tenant,
            model_name,
            metadata_csv_path,
            bigquery_client,
        )

    if "process" in args.stages:
        # Step 2: Retrieve completions and save to .jsonl.zst format
        assert metadata_csv_path.exists()
        if full_data_path.exists():
            if (
                not input(
                    "Continue processing? This will overwrite existing data. [y/N]"
                )
                == "y"
            ):
                return
        metadata_df = pd.read_csv(metadata_csv_path)
        tenant_data = metadata_df[metadata_df["tenant"] == query_tenant]
        assert len(metadata_df) == len(tenant_data)

        tenant_request_ids = tenant_data["request_id"].tolist()
        completions = get_completions_data(query_tenant, tenant_request_ids)
        process_and_write_data(completions, full_data_path)
        remove_duplicates(full_data_path)

    if "verify" in args.stages:
        # Step 3: Verify data
        assert metadata_csv_path.exists()
        assert full_data_path.exists()
        verify_data(metadata_csv_path, full_data_path, model_name)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Prepare data for xgboost filter.")
    parser.add_argument(
        "--start-date", type=str, required=True, help="Start date in YYYY-MM-DD format"
    )
    parser.add_argument(
        "--end-date", type=str, required=True, help="End date in YYYY-MM-DD format"
    )
    parser.add_argument(
        "--query-tenant",
        choices=AVAILABLE_TENANTS,
        required=True,
        help="Tenant to query and process",
    )
    parser.add_argument(
        "--model-name",
        type=str,
        required=True,
        help="Model name pattern to filter completions. Uses wildcard matching (e.g., 'elden' matches 'eldenv3-15b', 'qweldenv4', etc.)",
    )
    parser.add_argument(
        "--stages",
        nargs="+",
        choices=["query", "process", "verify"],
        default=["query", "process", "verify"],
        help="Stages to run: query (get and save request IDs), process (data processing), verify (data verification)",
    )
    args = parser.parse_args()
    main(args)

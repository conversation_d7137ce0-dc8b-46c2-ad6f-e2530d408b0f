from typing import List, Dict, Tuple, Optional
from datetime import datetime
import numpy as np
import pandas as pd
import xgboost as xgb
from sklearn.metrics import roc_auc_score, roc_curve

from research.prism.xgboost_completion_filter.feature_extraction import (
    XGBoostFeatureDataSet,
    DataPoint,
)
from research.prism.xgboost_completion_filter.model import to_dmatrix


class MultiDateBasedSplit:
    def __init__(
        self, X: List[DataPoint], test_ranges: List[Tuple[str, str]], verbose=False
    ):
        self.test_ranges: List[Tuple[datetime, datetime]] = [
            (datetime.fromisoformat(start), datetime.fromisoformat(end))
            for start, end in test_ranges
        ]
        self.verbose = verbose

        self._precompute_indices(X)

    def _precompute_indices(self, X: List[DataPoint]):
        dates = pd.Series([dp.timestamp for dp in X])

        no_train_mask = pd.Series(np.zeros(len(dates), dtype=bool))
        for start, end in self.test_ranges:
            no_train_mask |= (dates >= start) & (dates <= end)

        train_indices = list(np.where(~no_train_mask)[0])

        if self.verbose:
            print(f"# Train Examples: {len(train_indices)}")

        test_indices = []
        for start, end in self.test_ranges:
            test_mask = (dates >= start) & (dates <= end)
            test_indices.append(list(np.where(test_mask)[0]))
            assert len(set(train_indices) & set(test_indices[-1])) == 0
            if self.verbose:
                print(f"# Test Examples ({start} - {end}): {len(test_indices[-1])}")

        self.train_indices: List[int] = train_indices
        self.test_indices: List[List[int]] = test_indices

    def get_n_splits(self):
        return len(self.test_ranges)


class EvaluationManager:
    def __init__(
        self,
        model: xgb.Booster,
        xgboost_dataset: XGBoostFeatureDataSet,
        test_range: Tuple[datetime, datetime],
        test_indices: List[int],
        version: str,
        verbose=False,
    ):
        self.test_data = [xgboost_dataset.data_points[i] for i in test_indices]
        self.test_range = test_range
        self.model = model
        self.version = version
        self.verbose = verbose

        assert model.feature_names
        self.feature_names = list(model.feature_names)

        self.eval_results: Dict[str, Tuple[Optional[float], int]] = {}
        self.category_counts = XGBoostFeatureDataSet.classification_counts(
            self.test_data
        )
        print(f"Test data category counts: {self.category_counts}")

    def _eval_model_helper(self, data: List[DataPoint]):
        x_test = [dp.get_features(self.version) for dp in data]
        y_test = [0 if dp.accepted else 1 for dp in data]
        deval = to_dmatrix(x_test, y_test, self.feature_names)
        labels = deval.get_label()
        bst_predict = self.model.predict(deval)
        try:
            score = round(float(roc_auc_score(labels, bst_predict)), 5)
        except Exception:
            score = None
        return labels, bst_predict, score

    def eval_model(self):
        if not self.test_data:
            return

        labels, bst_predict, score = self._eval_model_helper(self.test_data)
        self.eval_results["all"] = (score, len(self.test_data))
        print(f"Evaluating all with {len(self.test_data)} data... Eval AUC {score}")
        self.fpr, self.tpr, self.thresholds = roc_curve(labels, bst_predict)

        for category in XGBoostFeatureDataSet.recognized_categories:
            test_data_in_category = (
                XGBoostFeatureDataSet.filter_by_classification_label(
                    self.test_data, category
                )
            )
            score = None
            if test_data_in_category:
                _, _, score = self._eval_model_helper(test_data_in_category)
            self.eval_results[category] = (score, len(test_data_in_category))
            if self.verbose:
                print(
                    f"Evaluating {category} with {len(test_data_in_category)} data... Eval AUC {score}"
                )


def eval_model(
    model: xgb.Booster,
    xgboost_dataset: XGBoostFeatureDataSet,
    cv: MultiDateBasedSplit,
    version: str,
    verbose=False,
) -> List[EvaluationManager]:
    evals = []
    for eval_idx in range(cv.get_n_splits()):
        test_range = cv.test_ranges[eval_idx]
        test_indices = cv.test_indices[eval_idx]
        evaluation_manager = EvaluationManager(
            model, xgboost_dataset, test_range, test_indices, version, verbose=verbose
        )
        evaluation_manager.eval_model()
        evals.append(evaluation_manager)
    return evals

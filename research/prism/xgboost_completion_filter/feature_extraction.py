from typing import List, Dict, Any, Optional
from datetime import datetime
from tqdm import tqdm
import pandas as pd

from base.completion_filter.extract_completion_filter_features import (
    FeatureExtractorFactory,
)
from base.tokenizers import Tokenizer


class DataPoint:
    def __init__(self, raw_data: Dict[str, Any], feature_extractor_versions: List[str]):
        self.raw_data = raw_data
        self.features: Dict[str, Dict[str, float]] = {
            version: {} for version in feature_extractor_versions
        }
        self.feature_names = {version: [] for version in feature_extractor_versions}
        self.classification_labels = set()
        self.timestamp = datetime.fromisoformat(raw_data["request"]["timestamp"])
        self.accepted = raw_data["resolution"]["accepted"]
        assert self.accepted is not None

    def set_features(self, version: str, features: Dict[str, float]):
        self.features[version] = features
        self.feature_names[version] = list(features.keys())

    def determine_classification_labels(self, categories: List[str]):
        for category in categories:
            if self.features["feature_extractor_v2"].get(category):
                self.classification_labels.add(category)

    def get_features(self, version: str) -> Dict[str, float]:
        return self.features[version]

    def has_classification_label(self, label: str) -> bool:
        return label in self.classification_labels


class XGBoostFeatureDataSet:
    recognized_categories = [
        "any_completion_line_matches_last_prefix_line",
        "first_long_non_print_line_matches_recent_prefix",
        "is_comment",
        "is_py",
        "is_ipynb",
        "is_ts",
        "is_tsx",
        "is_go",
        "is_rs",
        "is_proto",
        "is_yaml",
        "is_json",
        "is_java",
        "is_misc",
        "completion_geq_1_duplicate_line",
        "completion_geq_4_duplicate_line",
    ]

    def __init__(
        self,
        data: List[Dict[str, Any]],
        tokenizer: Tokenizer,
        versions: List[str] = ["feature_extractor_v1", "feature_extractor_v2"],
    ):
        self.feature_extractors = {
            version: FeatureExtractorFactory.create(version) for version in versions
        }
        self.feature_names = {
            version: self.feature_extractors[version].get_feature_names()
            for version in versions
        }
        self.data_points: List[DataPoint] = []

        for raw_data in tqdm(data, desc="Extracting features"):
            data_point = DataPoint(raw_data, versions)
            for version, extractor in self.feature_extractors.items():
                features = self.extract_features(raw_data, tokenizer, extractor)
                data_point.set_features(version, features)
            data_point.determine_classification_labels(self.recognized_categories)
            self.data_points.append(data_point)

    def extract_features(
        self, c: Dict[str, Any], tokenizer: Tokenizer, extractor
    ) -> Dict[str, float]:
        return extractor.extract_completion_filter_features(
            c["response"]["token_ids"],
            c["response"]["token_log_probs"],
            tokenizer,
            c["request"]["prefix"],
            c["request"]["path"],
        )

    def get_features(self, version: str) -> List[Dict[str, float]]:
        if version not in self.feature_extractors:
            raise ValueError(f"Unknown feature extractor version: {version}")
        return [dp.get_features(version) for dp in self.data_points]

    def get_feature_names(self, version: str) -> List[str]:
        if version not in self.feature_extractors:
            raise ValueError(f"Unknown feature extractor version: {version}")
        return self.feature_names[version]

    def get_dataframe(self, version: Optional[str] = None) -> pd.DataFrame:
        if version is None:
            return pd.DataFrame([dp.raw_data for dp in self.data_points])
        else:
            if version not in self.feature_extractors:
                raise ValueError(f"Unknown feature extractor version: {version}")
            df = pd.DataFrame([dp.raw_data for dp in self.data_points])
            features_df = pd.DataFrame(
                [dp.get_features(version) for dp in self.data_points]
            )
            return pd.concat([df, features_df], axis=1)

    @classmethod
    def filter_by_classification_label(
        cls, data: List[DataPoint], label: str
    ) -> List[DataPoint]:
        return [dp for dp in data if dp.has_classification_label(label)]

    @classmethod
    def classification_counts(cls, data: List[DataPoint]) -> Dict[str, int]:
        category_counts = {}
        for category in cls.recognized_categories:
            data_in_category = cls.filter_by_classification_label(data, category)
            category_counts[category] = len(data_in_category)
        return {
            k: v
            for k, v in sorted(
                category_counts.items(), key=lambda item: item[1], reverse=True
            )
        }

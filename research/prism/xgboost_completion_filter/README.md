# Prism

A Prism filter is a model that classifies completions as good or bad and is intended to be used to filter out low-quality completions. The Prism filter model must be trained and evaluated separately for each completion model. This directory contains the data generation code and training code for the Prism filter.

## Data Generation

The Prism filter is trained on data collected from dogfood and dogfood-shard with the accept/reject labels collected from the `data_download.py` script. This script retrieves data for the necessary completion model from BigQuery and saves it to a JSONL file. We collect roughly 200k examples from dogfood and dogfood-shard in July and August.

## Training / Evaluation

The Prism filter is trained and evaluated using the `train_and_eval.py` script. This script reads the JSONL files generated by the `data_download.py` script, trains the model, evaluates the model on the test sets, compares the results to the previous model, and saves the model.

The script will save a `model_info.txt` file that contains the evaluation results and the model file `model.json` that contains the trained model.
The `model.json` file is saved to the `/mnt/efs/augment/checkpoints/prism/<model_name>` directory as these models are much larger than previously trained models. The file `model_info.txt` file is also saved to the same directory.

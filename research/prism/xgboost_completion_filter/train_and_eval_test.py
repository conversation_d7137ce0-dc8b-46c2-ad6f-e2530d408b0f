"""Tests for train_and_eval.py to ensure the filter xgboost model weights are the same as the expected weights.

pytest research/prism/xgboost_completion_filter/train_and_eval_test.py
"""

from pathlib import Path
import xgboost as xgb

from research.prism.xgboost_completion_filter.train_and_eval import main
from research.core.data_paths import canonicalize_path


def test_train_and_eval():
    # Test directory containing test model and test data
    test_dir = canonicalize_path("data/prism/completion_test/")

    # Train the model
    model = main(
        data_dir=str(test_dir),
        tokenizer_name="starcoder2",
        eval=False,
        prev_ckpt_path=None,
        model_name=None,
    )

    # Load expected model
    test_model_path = Path(test_dir) / "model.json"
    expected_model = xgb.Booster(model_file=test_model_path)

    # Compare the two models for equivalence
    assert model.get_dump() == expected_model.get_dump()

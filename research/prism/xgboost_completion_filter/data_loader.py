import glob
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Tuple

from research.core.utils_for_file import read_jsonl_zst


def find_data_directories(data_dir: Path) -> List[Path]:
    if not data_dir.exists():
        raise ValueError(f"Data directory {data_dir} does not exist")

    data_dirs = [d for d in data_dir.iterdir() if d.is_dir()]
    if not data_dirs:
        raise ValueError(f"No valid data directories found in {data_dir}")

    return sorted(data_dirs)


def load_data(data_dirs: List[Path]) -> Tuple[List[Dict[str, Any]], List[str]]:
    model_json_data = []
    file_paths = []

    for data_dir in data_dirs:
        files = glob.glob(str(data_dir / "*.jsonl.zst"))
        print(f"\nFound {len(files)} files in {data_dir}")
        for file in files:
            print(f"\tReading {file}... ", end="")
            file_paths.append(file)
            json_data = read_jsonl_zst(Path(file))
            print(f"\tFound {len(json_data)} completions")
            model_json_data.extend(json_data)

    model_json_data = [d for d in model_json_data if d["resolution"] is not None]
    assert len(model_json_data) == len(
        set([d["request_id"] for d in model_json_data])
    ), "Duplicate request IDs found"

    for data in model_json_data:
        data["request"]["timestamp"] = datetime.fromtimestamp(
            data["request"]["timestamp"]
        ).strftime("%Y-%m-%d %H:%M:%S")
        data["response"]["timestamp"] = datetime.fromtimestamp(
            data["response"]["timestamp"]
        ).strftime("%Y-%m-%d %H:%M:%S")
        data["resolution"]["timestamp"] = datetime.fromtimestamp(
            data["resolution"]["timestamp"]
        ).strftime("%Y-%m-%d %H:%M:%S")

    model_json_data = sorted(model_json_data, key=lambda x: x["request"]["timestamp"])

    print(f"Found {len(model_json_data)} completions.")
    return model_json_data, file_paths

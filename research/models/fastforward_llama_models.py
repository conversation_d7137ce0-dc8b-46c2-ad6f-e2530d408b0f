"""A research model that uses the inference host.

The inference host allows for fast, python-based inference.
"""

import json
import logging
import pathlib
import sys
import time
from typing import Sequence

import torch
from megatron.tokenizer.tokenizer import (
    DeepSeekCoderBaseTokenizer,
    DeepSeekCoderInstructTokenizer,
)

from base.fastforward.fwd import ModelSpec
from base.fastforward.llama.model_specs import _MODEL_SPECS
from base.fastforward.parallel import ParallelConfig
from base.tokenizers.llama3_tokenizer import (
    Llama3BaseTokenizer,
    Llama3InstructTokenizer,
)
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer

from research.core.all_prompt_formatters import PromptFormatterEnder
from research.core.constants import AUGMENT_ROOT
from research.core.data_paths import canonicalize_path
from research.core.edit_prompt_formatters import DroidEditPromptFormatter
from research.core.llama_prompt_formatters import (
    DeepSeekC<PERSON>r<PERSON><PERSON><PERSON><PERSON>atter,
    DeepSeekCoder<PERSON><PERSON>ru<PERSON><PERSON><PERSON><PERSON>er,
    LLaMA3Instruct<PERSON>rompt<PERSON><PERSON>atter,
)
from research.models.fastforward_models import FastForwardModel, TokenBoostingStrategy
from research.models.meta_model import register_model

# This is so we can import from the inference host
sys.path.append(str(AUGMENT_ROOT))

# TODO(guy) tear this out once we have flash attention working in research.
# We don't have flash attention installed in research, so we need to mock it out.
# This makes "from third_party.flash_attention import flash_attn" work as a noop.
# Then we turn off flash attention after importing cached_attention.
# sys.path.append(f"{AUGMENT_ROOT}/research/mock")

from base.fastforward import cached_attention, fwd_utils  # noqa: E402
from base.fastforward.llama import fwd_llama, fwd_llama_fp8  # noqa: E402
from base.fastforward.llama import model_specs as llama_model_specs  # noqa: E402

logger = logging.getLogger(__file__)


class LLAMA_FastForwardModel(FastForwardModel):
    """A fast-forward model that uses the LLaMA architecture."""

    model_spec: llama_model_specs.LlamaModelSpec
    """Model spec must be set before the constructor is called.

    How to use this: subclasses that represent specific models should set
    this class attribute to the relevant model spec.
    """

    def __init__(
        self,
        checkpoint_path: pathlib.Path | None = None,
        checkpoint_sha256: str | None = None,
        sequence_length: int | None = None,
        token_boosters: Sequence[TokenBoostingStrategy] = (),
        use_fp8: bool = False,
        num_gpus: int | None = None,
    ):
        """Constructor.

        Args:
            model_spec: The model specification
            checkpoint_path: Optional, path to the specific checkpoint dir.
            checkpoint_sha256: Optional, the SHA-256 of the checkpoint.
            sequence_length: Optional, the sequence length to use.
            use_fp: Whether to use fp8 or bf16.
            num_gpus: Optional, the number of GPUs (==parallelism) to use. If not specified,
                will use all available GPUs.
        """
        super().__init__(
            model_path=checkpoint_path
            or pathlib.Path(
                "/tmp"
            ),  # TODO(guy) a hack because we don't always have a model path
            checkpoint_path=checkpoint_path,
            checkpoint_sha256=checkpoint_sha256,
            token_boosters=token_boosters,
        )

        if checkpoint_path is not None:
            self.model_spec.checkpoint_path = str(checkpoint_path.absolute())
        if checkpoint_sha256 is not None:
            self.model_spec.checkpoint_sha256 = checkpoint_sha256
        if sequence_length is not None:
            self.seq_length = sequence_length
            self.model_spec.max_position_embeddings = sequence_length
            self.model_spec.unscaled_max_position_embeddings = int(
                sequence_length / self.model_spec.rotary_scaling_factor
            )
        if self.model_spec.max_position_embeddings != self.seq_length:
            raise ValueError(
                f"Model seq_length ({self.model_spec.max_position_embeddings}) "
                f"does not match the expected seq_length ({self.seq_length})"
            )
        self.use_fp8 = use_fp8
        self.num_gpus = num_gpus

    def get_model_spec(self) -> ModelSpec:
        return self.model_spec

    def load(self):
        """Load the model."""
        if self.is_loaded:
            return

        if (self.checkpoint_path / "params.json").exists():
            params_dict = json.loads((self.checkpoint_path / "params.json").read_text())
            self.model_spec.vocab_size = params_dict["vocab_size"]
            self.model_spec.checkpoint_sha256 = params_dict["checkpoint_sha256"]

        num_gpus: int = self.num_gpus or torch.cuda.device_count()
        if num_gpus > torch.cuda.device_count():
            raise ValueError(
                f"Requested {num_gpus} GPUs, but only {torch.cuda.device_count()} are available."
            )
        logger.info("Loading model %s with %d GPUs...", self.name, num_gpus)
        start = time.time()
        round_sizes = (32, 128, 512, 2048)
        parallel_config = ParallelConfig.from_legacy_config(num_gpus, False)
        if self.use_fp8:
            self.step_fn = fwd_llama_fp8.generate_step_fn(
                ms=self.model_spec,
                parallel_config=parallel_config,
                auto_capture_graphs=True,
                batch_sizes=round_sizes,
            )
        else:
            self.step_fn = fwd_llama.generate_step_fn(
                ms=self.model_spec,
                num_processes=num_gpus,
                auto_capture_graphs=True,
                batch_sizes=round_sizes,
            )
        self.step_fn = fwd_utils.pad_and_step(self.step_fn, round_sizes=round_sizes)
        attn_cache_generator_fn = fwd_llama.LlamaAttentionFactory(
            ms=self.model_spec,
            parallel_config=parallel_config,
            pre_attention_kernel_fusion=True,
            attention_impl=cached_attention.AttentionImpl.MULTI_REQUEST_FLASH,
        )
        self.generation_attn_cache = attn_cache_generator_fn(self.seq_length)

        # Let's use the same cache to save some memory. We don't
        # need K/V caches across generations in research right now.
        self.forward_attn_cache = self.generation_attn_cache

        elapsed = time.time() - start
        logger.info("Loaded the model in %.1f seconds", elapsed)


##############################################################################
#                                                                            #
# LLaMA family models.
#                                                                            #
##############################################################################


@register_model("fastforward_llama3_70b_base")
class FastForwardLLAMA3_70B_Base(LLAMA_FastForwardModel):
    """Llama 3 70B Base."""

    seq_length: int = 8192

    model_spec: llama_model_specs.LlamaModelSpec = _MODEL_SPECS["llama3-70b"]

    def __init__(
        self, checkpoint_path: str | pathlib.Path, checkpoint_sha256: str | None = None
    ):
        super().__init__(
            checkpoint_path=pathlib.Path(checkpoint_path),
            checkpoint_sha256=checkpoint_sha256,
        )

    @classmethod
    def create_default_formatter(cls):
        # TODO(guy) The null formatter only has the tokenizer property, which is used
        # by the system. At some point we should decouple the prompt formatter
        # from the model.
        class NullPromptFormatter:
            @property
            def tokenizer(self):
                return Llama3BaseTokenizer()

        return NullPromptFormatter()


##############################################################################
#                                                                            #
# Inference host DeepSeek models                                             #
#                                                                            #
##############################################################################


def _get_deepseek_33b_model_spec(
    name: str, checkpoint_path: str
) -> llama_model_specs.LlamaModelSpec:
    return llama_model_specs.LlamaModelSpec(
        name=name,
        checkpoint_path=checkpoint_path,
        emb_dim=7168,
        num_layers=62,
        num_heads=8,
        num_queries_per_head=7,
        head_dim=128,
        attn_split_head_mode=cached_attention.SplitHeadModes.KV_HEADS,
        rotary_theta=100000.0,
        rotary_scaling_factor=4.0,
        rotary_pct=1.0,
        vocab_size=32256,
        mlp_dim_divisible_by=256,
        ffn_dim_multiplier=1.0,
        norm_eps=1e-05,
        max_position_embeddings=16384,
        unscaled_max_position_embeddings=4096,
    )


@register_model("fastforward_deepseek_coder_base")
class FastForwardDeepSeekCoderBaseModel(LLAMA_FastForwardModel):
    """A class for loading fastbackward-produced checkpoints of DeepSeek Base models."""

    @classmethod
    def create_default_formatter(cls) -> DeepSeekCoderBaseFormatter:
        return DeepSeekCoderBaseFormatter()

    def load_tokenizer(self) -> DeepSeekCoderBaseTokenizer:
        """Load tokenizer."""
        tokenizer = DeepSeekCoderBaseTokenizer()
        # Reset the tokenizer to be the same as the loaded model.
        self.prompt_formatter.rebind(tokenizer=tokenizer)  # type: ignore
        return tokenizer


@register_model("fastforward_deepseek_coder_base_33b")
class FastForwardDeepSeekCoderBase33B(FastForwardDeepSeekCoderBaseModel):
    def __init__(self, checkpoint_path: pathlib.Path | str, use_fp8: bool = False):
        canonical_path = canonicalize_path(checkpoint_path)
        self.model_spec = _get_deepseek_33b_model_spec(
            name="deepseek-coder-base-33b",
            checkpoint_path=str(canonical_path),
        )
        super().__init__(
            pathlib.Path(canonical_path),
            sequence_length=16384,
            use_fp8=use_fp8,
        )


@register_model("fastforward_deepseek_coder_instruct")
class FastForwardDeepSeekCoderInstructModel(LLAMA_FastForwardModel):
    """A class for loading fastbackward-produced checkpoints of DeepSeek Instruct models."""

    @classmethod
    def create_default_formatter(cls) -> DeepSeekCoderInstructFormatter:
        return DeepSeekCoderInstructFormatter()

    def load_tokenizer(self) -> DeepSeekCoderInstructTokenizer:
        """Load tokenizer."""
        tokenizer = DeepSeekCoderInstructTokenizer()
        # Reset the tokenizer to be the same as the loaded model.
        self.prompt_formatter.rebind(tokenizer=tokenizer)  # type: ignore
        return tokenizer


@register_model("fastforward_deepseek_coder_instruct_33b")
class FastForwardDeepSeekCoderInstruct33B(FastForwardDeepSeekCoderInstructModel):
    """The open-source DeepSeek-Coder-Instruct-33b model."""

    seq_length: int = 16384
    supports_fim: bool = False
    supports_retrieval: bool = False

    model_spec: llama_model_specs.LlamaModelSpec = _get_deepseek_33b_model_spec(
        name="deepseek-coder-33b",
        checkpoint_path=str(
            # v1 checkpoint; see base/fastforward/checkpoints/migrations/convert_to_v2.py
            canonicalize_path("checkpoints/deepseek/fastforward/coder-33b-instruct")
        ),
    )


@register_model("fastforward_deepseek_coder_instruct_33b_fp8")
class FastForwardDeepSeekCoderInstruct33BFP8(FastForwardDeepSeekCoderInstructModel):
    """The open-source DeepSeek-Coder-Instruct-33b model."""

    seq_length: int = 16384
    supports_fim: bool = False
    supports_retrieval: bool = False

    model_spec: llama_model_specs.LlamaModelSpec = _get_deepseek_33b_model_spec(
        name="deepseek-coder-33b-fp8",
        checkpoint_path=str(
            # v1 checkpoint; see base/fastforward/checkpoints/migrations/convert_to_v2.py
            canonicalize_path("checkpoints/deepseek/fastforward/coder-33b-instruct-fp8")
        ),
    )

    def __init__(
        self,
        checkpoint_path: pathlib.Path | None = None,
        sequence_length: int | None = None,
    ):
        super().__init__(
            checkpoint_path=checkpoint_path,
            sequence_length=sequence_length,
            use_fp8=True,
        )


@register_model("fastforward_deepseek_coder_instruct_1.3b")
class FastForwardDeepSeekCoderInstruct1B(FastForwardDeepSeekCoderInstructModel):
    """The open-source DeepSeek-Coder-Instruct-1.3b model."""

    seq_length: int = 16384
    supports_fim: bool = False
    supports_retrieval: bool = False

    model_spec: llama_model_specs.LlamaModelSpec = llama_model_specs.LlamaModelSpec(
        name="deepseek-coder-1.3b",
        checkpoint_path=str(
            canonicalize_path(
                "checkpoints/deepseek/fastforward/coder-1.3b-instruct-ckptv2/"
            )
        ),
        checkpoint_sha256="cb3cfb2da962a40a6619d8d657a2970445aee121d31f2c69923f46b7c4f7cd6c",
        emb_dim=2048,
        num_layers=24,
        num_heads=16,
        num_queries_per_head=1,
        head_dim=128,
        attn_split_head_mode=cached_attention.SplitHeadModes.KV_HEADS,
        rotary_theta=100000.0,
        rotary_scaling_factor=4.0,
        rotary_pct=1.0,
        vocab_size=32256,
        mlp_dim_divisible_by=128,
        ffn_dim_multiplier=1.0,
        norm_eps=1e-05,
        max_position_embeddings=16384,
        unscaled_max_position_embeddings=4096,
    )


@register_model("fastforward_deepseek_coder_instruct_7b_fp8")
class FastForwardDeepSeekCoderInstruct7BFP8(FastForwardDeepSeekCoderInstructModel):
    """The open-source DeepSeek-Coder-Instruct-6.7B quantized to FP8 model."""

    seq_length: int = 16384
    supports_fim: bool = False
    supports_retrieval: bool = False

    model_spec: llama_model_specs.LlamaModelSpec = llama_model_specs.LlamaModelSpec(
        name="deepseek-coder-6.7b",
        checkpoint_path=str(
            canonicalize_path(
                "checkpoints/deepseek/fastforward/coder-6.7b-instruct-fp8"
            )
        ),
        checkpoint_sha256="d59261a029c1034a2aff9866a2b2f7ca59a8a39a23a638ed38ec45a9dfb4d59e",
        emb_dim=4096,
        num_layers=32,
        num_heads=32,
        num_queries_per_head=1,
        head_dim=128,
        attn_split_head_mode=cached_attention.SplitHeadModes.KV_HEADS,
        rotary_theta=100000.0,
        rotary_scaling_factor=4.0,
        rotary_pct=1.0,
        vocab_size=32256,
        mlp_dim_divisible_by=256,
        ffn_dim_multiplier=1.0,
        norm_eps=1e-06,
        max_position_embeddings=16384,
        unscaled_max_position_embeddings=4096,
    )

    def __init__(
        self,
        checkpoint_path: pathlib.Path | None = None,
        sequence_length: int | None = None,
    ):
        super().__init__(
            checkpoint_path=checkpoint_path,
            sequence_length=sequence_length,
            use_fp8=True,
        )


@register_model("fastforward_llama3_instruct")
class FastForwardLlama3InstructModel(LLAMA_FastForwardModel):
    """A class for loading FastForward checkpoints of LLaMa 3 Instruct models."""

    @classmethod
    def create_default_formatter(cls) -> LLaMA3InstructPromptFormatter:
        return LLaMA3InstructPromptFormatter()

    def load_tokenizer(self) -> Llama3InstructTokenizer:
        """Load tokenizer."""
        tokenizer = Llama3InstructTokenizer()
        # Reset the tokenizer to be the same as the loaded model.
        self.prompt_formatter.rebind(tokenizer=tokenizer)  # type: ignore
        return tokenizer


@register_model("fastforward_llama3_instruct_70b_fp8")
class FastForwardLlama3Instruct70bModelFp8(FastForwardLlama3InstructModel):
    """LLaMa 3 Instruct 70B model at FP8 precision in FastForward."""

    seq_length: int = 8192
    supports_fim: bool = False
    supports_retrieval: bool = False

    model_spec: llama_model_specs.LlamaModelSpec = llama_model_specs.LlamaModelSpec(
        name="llama3-70b",
        checkpoint_path=str(
            canonicalize_path(
                "checkpoints/llama3/Meta-Llama-3-70B-Instruct-ff-fp8-8shard"
            )
        ),
        checkpoint_sha256="f30b9d0bcb4910affd2414651d41a0bef5065bcd12fc40c55265cb28a3a78aca",
        emb_dim=8_192,
        num_layers=80,
        num_heads=8,
        num_queries_per_head=8,
        head_dim=128,
        attn_split_head_mode=cached_attention.SplitHeadModes.KV_HEADS,
        rotary_theta=500_000.0,
        rotary_scaling_factor=1.0,
        rotary_pct=1.0,
        vocab_size=128_256,
        mlp_dim_divisible_by=4_096,
        ffn_dim_multiplier=1.3,
        norm_eps=1e-05,
        max_position_embeddings=8_192,
        unscaled_max_position_embeddings=8_192,
    )

    def __init__(
        self,
        checkpoint_path: pathlib.Path | None = None,
        sequence_length: int | None = None,
    ):
        super().__init__(
            checkpoint_path=checkpoint_path,
            sequence_length=sequence_length,
            use_fp8=True,
        )


@register_model("fastforward_llama3_instruct_70b_16k_fp8")
class FastForwardLlama3Instruct70b16kModelFp8(FastForwardLlama3InstructModel):
    """LLaMa 3 Instruct 70B model with 16K context at FP8 precision in FastForward."""

    seq_length: int = 16_384
    supports_fim: bool = False
    supports_retrieval: bool = False

    model_spec: llama_model_specs.LlamaModelSpec = llama_model_specs.LlamaModelSpec(
        name="llama3-70b",
        checkpoint_path=str(
            canonicalize_path(
                "checkpoints/llama3/Meta-Llama-3-70B-Instruct-ff-fp8-8shard"
            )
        ),
        checkpoint_sha256=(
            "f30b9d0bcb4910affd2414651d41a0bef5065bcd12fc40c55265cb28a3a78aca"
        ),
        emb_dim=8_192,
        num_layers=80,
        num_heads=8,
        num_queries_per_head=8,
        head_dim=128,
        attn_split_head_mode=cached_attention.SplitHeadModes.KV_HEADS,
        rotary_theta=500_000.0,
        rotary_scaling_factor=4.0,
        rotary_extension_method="yarn",
        beta_fast=32,
        beta_slow=1,
        rotary_pct=1.0,
        vocab_size=128_256,
        mlp_dim_divisible_by=4_096,
        ffn_dim_multiplier=1.3,
        norm_eps=1e-05,
        max_position_embeddings=16_384,
        # NOTE: LLaMa 3 70B's original max position embeddings is 8192. However,
        # experiments show that using 4096 (which is consistent with the theta scaling
        # factor of 4.0) leads to better performance.
        # Relatedly, the RotaryConfig class asserts that the max position embeddings
        # matches the product of the original max position embeddings, the theta scaling
        # factor, and the position interpolation factor.
        unscaled_max_position_embeddings=4_096,
    )

    def __init__(
        self,
        checkpoint_path: pathlib.Path | None = None,
        sequence_length: int | None = None,
    ):
        super().__init__(
            checkpoint_path=checkpoint_path,
            sequence_length=sequence_length,
            use_fp8=True,
        )


@register_model("fastforward_llama3_instruct_8b")
class FastForwardLlama3Instruct8BModel(FastForwardLlama3InstructModel):
    """LLaMa 3 Instruct 8B model in FastForward."""

    seq_length: int = 8192
    supports_fim: bool = False
    supports_retrieval: bool = False

    model_spec: llama_model_specs.LlamaModelSpec = llama_model_specs.LlamaModelSpec(
        name="llama3-8b",
        checkpoint_path=str(
            canonicalize_path("checkpoints/llama3/Meta-Llama-3-8B-Instruct-ff")
        ),
        checkpoint_sha256=(
            "c7e79b6173c1f7203fd387bd9b7c455f4a647e5d051693ac7d7134347ae899d3"
        ),
        emb_dim=4096,
        num_layers=32,
        num_heads=8,
        num_queries_per_head=4,
        head_dim=128,
        attn_split_head_mode=cached_attention.SplitHeadModes.KV_HEADS,
        rotary_theta=500_000.0,
        rotary_scaling_factor=1.0,
        rotary_pct=1.0,
        vocab_size=128_256,
        mlp_dim_divisible_by=1024,
        ffn_dim_multiplier=1.3,
        norm_eps=1e-05,
        max_position_embeddings=8_192,
        unscaled_max_position_embeddings=8_192,
    )

    def __init__(
        self,
        checkpoint_path: pathlib.Path | None = None,
        sequence_length: int | None = None,
    ):
        super().__init__(
            checkpoint_path=checkpoint_path,
            sequence_length=sequence_length,
            use_fp8=False,
        )


@register_model("fastforward_llama3_1_instruct_70b_16k_fp8")
class FastForwardLlama31Instruct70b16kModelFp8(FastForwardLlama3InstructModel):
    """LLaMa 3 Instruct 70B model with 16K context at FP8 precision in FastForward."""

    seq_length: int = 16_384
    supports_fim: bool = False
    supports_retrieval: bool = False

    model_spec: llama_model_specs.LlamaModelSpec = llama_model_specs.LlamaModelSpec(
        name="llama3.1-70b",
        checkpoint_path=str(
            canonicalize_path("checkpoints/llama3.1/ff/Meta-Llama-3.1-70B-Instruct-FP8")
        ),
        checkpoint_sha256=(
            "1a481e2b028478b26b2d3c2c530b915388ae94433b1f92bde51105f7857b2052"
        ),
        emb_dim=8_192,
        num_layers=80,
        num_heads=8,
        num_queries_per_head=8,
        head_dim=128,
        attn_split_head_mode=cached_attention.SplitHeadModes.KV_HEADS,
        rotary_theta=500_000.0,
        rotary_scaling_factor=8.0,
        rotary_extension_method="llama3_1",
        rotary_pct=1.0,
        vocab_size=128_256,
        mlp_dim_divisible_by=4_096,
        ffn_dim_multiplier=1.3,
        norm_eps=1e-05,
        max_position_embeddings=16_384,
    )

    def __init__(
        self,
        checkpoint_path: pathlib.Path | None = None,
        sequence_length: int | None = None,
    ):
        super().__init__(
            checkpoint_path=checkpoint_path,
            sequence_length=sequence_length,
            use_fp8=True,
        )


@register_model("fastforward_droid_1b16k")
class FastForwardDroid1B16K(FastForwardDeepSeekCoderInstruct1B):
    """In-house Droid model based on DeepSeek-Coder-Instruct-1.3B), where the default is a BF16 model."""

    def __init__(
        self,
        checkpoint_path: pathlib.Path = pathlib.Path(
            "checkpoints/code-edit/droid/droid-v1-16k-1.3B-BF16-ckptv2/"
        ),
        checkpoint_sha256="24e8ebaa2b35c89b9a1b18be19d4802f13fa07a8bfd7535518f42bb36ba59bc9",
        use_fp8: bool = False,
    ):
        super().__init__(
            pathlib.Path(canonicalize_path(checkpoint_path)),
            checkpoint_sha256=checkpoint_sha256,
            sequence_length=16384,
            use_fp8=use_fp8,
        )

    @classmethod
    def create_default_formatter(cls) -> DroidEditPromptFormatter:
        return DroidEditPromptFormatter(
            path_len=256,
            instruction_len=512,
            prefix_len=1536,
            selected_code_len=4096,
            suffix_len=1024,
            max_prompt_tokens=16384 - 4096,
        )


@register_model("fastforward_droid_1b16k_fp8")
class FastForwardDroid1B16KFP8(FastForwardDroid1B16K):
    """In-house Droid model based on DeepSeek-Coder-Instruct-1.3B)."""

    def __init__(
        self,
        checkpoint_path: pathlib.Path = pathlib.Path(
            "checkpoints/code-edit/droid/droid-v1-16k-1.3B-FP8_ckptv2"
        ),
        checkpoint_sha256="3651469dc9f7eabdb63e2c326a6e72382dd86c878486364f796400ff6eaabced",
    ):
        super().__init__(
            pathlib.Path(canonicalize_path(checkpoint_path)),
            checkpoint_sha256=checkpoint_sha256,
            use_fp8=True,
        )


@register_model("fastforward_droid_33b16k")
class FastForwardDroid33B16K(FastForwardDeepSeekCoderInstruct33B):
    """In-house Droid model based on DeepSeek-Coder-Instruct-33B), where the default is a BF16 model."""

    def __init__(
        self,
        checkpoint_path: pathlib.Path = pathlib.Path(
            # v1 checkpoint; see base/fastforward/checkpoints/migrations/convert_to_v2.py
            "checkpoints/code-edit/droid/droid-v1-16k-33B-BF16/"
        ),
        use_fp8: bool = False,
    ):
        super().__init__(
            pathlib.Path(canonicalize_path(checkpoint_path)),
            sequence_length=16384,
            use_fp8=use_fp8,
        )

    @classmethod
    def create_default_formatter(cls) -> DroidEditPromptFormatter:
        return DroidEditPromptFormatter(
            path_len=256,
            instruction_len=512,
            prefix_len=1536,
            selected_code_len=4096,
            suffix_len=1024,
            max_prompt_tokens=16384 - 4096,
        )


@register_model("fastforward_droid_33b16k_fp8")
class FastForwardDroid33B16KFP8(FastForwardDroid33B16K):
    """In-house FP8 Droid model based on DeepSeek-Coder-Instruct-33B)."""

    def __init__(
        self,
        checkpoint_path: pathlib.Path = pathlib.Path(
            # v1 checkpoint; see base/fastforward/checkpoints/migrations/convert_to_v2.py
            "checkpoints/code-edit/droid/droid-v1-16k-33B-FP8"
        ),
    ):
        super().__init__(pathlib.Path(canonicalize_path(checkpoint_path)), use_fp8=True)


# TODO(Xuanyi Dong): remove the following two models once the code edit model is ready in production.
@register_model("fastforward_droid")
class FastForwardDroid(FastForwardDeepSeekCoderInstruct33B):
    """In-house Droid model based on DeepSeek-Coder-Instruct-33b (Basic Droid without retrieval)."""

    seq_length: int = 8192
    supports_fim: bool = False
    supports_retrieval: bool = False

    model_spec: llama_model_specs.LlamaModelSpec = llama_model_specs.LlamaModelSpec(
        name="deepseek-33b",
        checkpoint_path=str(
            canonicalize_path(
                # v1 checkpoint; see base/fastforward/checkpoints/migrations/convert_to_v2.py
                "user/yuri/checkpoint_llama_iteration_252_no_dropout_jan_16_ffw"
            )
        ),
        emb_dim=7168,
        num_layers=62,
        num_heads=8,
        num_queries_per_head=7,
        head_dim=128,
        attn_split_head_mode=cached_attention.SplitHeadModes.KV_HEADS,
        rotary_theta=100000.0,
        rotary_scaling_factor=4.0,
        rotary_pct=1.0,
        vocab_size=32256,
        mlp_dim_divisible_by=256,
        ffn_dim_multiplier=1.0,
        norm_eps=1e-05,
        max_position_embeddings=8192,
        unscaled_max_position_embeddings=2048,
    )

    @classmethod
    def create_default_formatter(cls) -> DroidEditPromptFormatter:
        return DroidEditPromptFormatter()


##############################################################################
#                                                                            #
# Qwen-2.5-Coder models                                                      #
#                                                                            #
##############################################################################


@register_model("fastforward_qwen25coder")
class FastForwardQwen25CoderModel(LLAMA_FastForwardModel):
    """A class for loading fastforward checkpoints of Qwen-2.5-Coder models."""

    @classmethod
    def create_default_formatter(cls) -> PromptFormatterEnder:
        formatter = PromptFormatterEnder(
            max_prefix_tokens=1024,
            max_suffix_tokens=512,
            max_signature_tokens=1024,
        )
        formatter.tokenizer = Qwen25CoderTokenizer()
        return formatter

    def load_tokenizer(self) -> Qwen25CoderTokenizer:
        """Load tokenizer."""
        tokenizer = Qwen25CoderTokenizer()
        # Reset the tokenizer to be the same as the loaded model.
        self.prompt_formatter.rebind(tokenizer=tokenizer)  # type: ignore
        return tokenizer


@register_model("fastforward_qwen25coder_7b")
class FastForwardQwen25Coder_7B(FastForwardQwen25CoderModel):
    seq_length: int = 32768

    model_spec: llama_model_specs.LlamaModelSpec = (
        llama_model_specs.get_llama_model_spec(
            model_name="qwen2_5-7b",
        )
    )


@register_model("fastforward_qwen25coder_14b")
class FastForwardQwen25Coder_14B(FastForwardQwen25CoderModel):
    seq_length: int = 32768

    model_spec: llama_model_specs.LlamaModelSpec = (
        llama_model_specs.get_llama_model_spec(
            model_name="qwen2_5-14b",
        )
    )


@register_model("fastforward_qwen25coder_32b")
class FastForwardQwen25Coder_32B(FastForwardQwen25CoderModel):
    seq_length: int = 32768

    model_spec: llama_model_specs.LlamaModelSpec = (
        llama_model_specs.get_llama_model_spec(
            model_name="qwen2_5-32b",
        )
    )

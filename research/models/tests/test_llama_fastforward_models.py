"""Test inference-host DeepSeek models.

CUDA_VISIBLE_DEVICES=0 pytest research/models/tests/test_llama_fastforward_models.py
"""

import pytest
import torch

from research.core.model_input import ModelInput
from research.models.all_models import GenerationOptions


@pytest.mark.parametrize(
    "use_fp8",
    [
        pytest.param(False, id="fp16"),
        pytest.param(True, id="fp8", marks=pytest.mark.skip_in_ci),
    ],
)
def test_simple_case(use_fp8: bool):
    # Import inside the test case to avoid pytest --forked / CUDA problems
    from research.models.fastforward_llama_models import (  # pylint: disable=import-outside-toplevel
        FastForwardDroid1B16K,
        FastForwardDroid1B16KFP8,
    )

    if use_fp8:
        model = FastForwardDroid1B16KFP8()
    else:
        model = FastForwardDroid1B16K()
    model.load()

    result = model.generate(
        ModelInput(
            prefix="def hello_world() -> str:\n",
            suffix='if __name__ == "__main__":\n',
            extra={
                "instruction": "replace log by print",
                "selected_code": "  log('Hello World!')\n",
            },
        ),
        GenerationOptions(temperature=0.0, max_generated_tokens=32),
    )
    assert result == "  print('Hello World!')\n\n```\n"

    model.unload()


def test_forward_pass():
    # Import inside the test case to avoid pytest --forked / CUDA problems
    from research.models.fastforward_llama_models import (
        FastForwardDroid1B16K,  # pylint: disable=import-outside-toplevel
    )

    model = FastForwardDroid1B16K()
    model.load()

    inputs = [
        ModelInput(
            prefix="def hello_world() -> str:\n",
            suffix='if __name__ == "__main__":\n',
            target="  print('Hello World!')\n\n```\n",
            extra={
                "instruction": "replace log by print",
                "selected_code": "  log('Hello World!')\n",
            },
        ),
    ]

    outputs = model.forward_pass(inputs)

    # Test logits
    target_logits = [
        x.logits.gather(dim=-1, index=x.label_tokens.unsqueeze(-1))[
            x.target_mask
        ].squeeze(-1)
        for x in outputs
    ]
    expected_target_logits = [
        [63.75, 56.0, 66.5, 36.0, 64.0, 66.0, 40.5, 61.5, 35.25, 50.5, 78.0]
    ]
    expected_target_logits = [
        torch.tensor(x, dtype=torch.bfloat16, device="cuda")
        for x in expected_target_logits
    ]
    # TODO(markus): The logits change often after small changes to FFWD,
    # especially for low-precision models. We are using a coarse comparison
    # here, but the different logits are still separated by a large margin.
    torch.testing.assert_close(target_logits, expected_target_logits, rtol=1e-2, atol=1)

    # Test accuracy
    accuracy = [
        (x.logits[x.target_mask].argmax(dim=-1) == x.label_tokens[x.target_mask])
        .float()
        .mean()
        .item()
        for x in outputs
    ]
    assert accuracy == [1.0]

    # Test detokenize
    texts = [
        model.tokenizer.detokenize(output.label_tokens[output.target_mask].tolist())
        for output in outputs
    ]
    assert [model_input.target for model_input in inputs] == texts

    # Test token input/output is causal
    assert [x.input_tokens[1:].tolist() for x in outputs] == [
        x.label_tokens[:-1].tolist() for x in outputs
    ]
    model.unload()

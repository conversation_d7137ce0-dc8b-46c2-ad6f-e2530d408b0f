"""Test research models against each other for correctness."""

import logging
from typing import Any

import numpy as np
import pytest
import torch

from research.core.constants import AUGMENT_CHECKPOINTS_ROOT
from research.core.model_input import ModelInput
from research.retrieval.types import (
    Chunk,
    Document,
)
from tests.common import relocate_model

logger = logging.getLogger(__file__)
logger.setLevel(logging.INFO)

BASIC_INPUTS = [
    ModelInput(
        prefix="def hello_world():\n",
        path="hello_world.py",
        target='print("Hello World!")',
    ),
    ModelInput(
        prefix="def foo():\n",
        suffix='def bar():\n    return "bar"\n',
        path="foobar.py",
        target='    return "foo"\n\n\n',
    ),
    ModelInput(
        prefix="import foo\n\n\ndef bar(s):\n",
        suffix="def foobar(s):\n    return foo(bar(s))\n",
        retrieved_chunks=[
            Chunk(
                id="abc",
                text='def foo(s):\n    return "foo" + s\n',
                parent_doc=Document.new(
                    text='def foo(s):\n    return "foo" + s\n', path="foo.py"
                ),
                char_offset=0,
                length=35,
                line_offset=0,
                length_in_lines=2,
            ),
        ],
        path="foobar.py",
        target='    return "bar" + s\n\n\n',
    ),
]


# TODO(jeff): maybe actually collect some realistic data for this.
@pytest.fixture(name="model_input_test_data")
def fixture_model_input_test_data() -> dict[str, list[ModelInput]]:
    """Name to a list of model inputs for testing."""
    return {
        "basic_inputs": BASIC_INPUTS,
    }


PROMPT_CONFIGS = {
    "rogue": {
        "max_prefix_tokens": 1280,
        "max_suffix_tokens": 768,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 3816,
        "always_use_suffix_token": True,
        "only_truncate_true_prefix": True,
    },
}

MODEL_CONFIGS = {
    "starcoderbase_1b_hf": {
        "name": "starcoderbase_1b_hf",
    },
    "starcoderbase_1b": {
        "name": "starcoderbase_1b",
    },
    "starcoderbase_1b_ff": {
        "name": "starcoderbase_1b_fastforward",
    },
    "starcoderbase_7b": {
        "name": "starcoderbase_7b",
    },
    "starcoderbase_7b_ff": {
        "name": "starcoderbase_7b_fastforward",
    },
    "rogue_1b": {
        "name": "rogue",
        "checkpoint_path": str(
            AUGMENT_CHECKPOINTS_ROOT / "rogue/rogue1B_diffb1m_chunk30"
        ),
        "prompt": PROMPT_CONFIGS["rogue"],
    },
    "rogue_1b_ff": {
        "name": "rogue_fastforward",
        "model_path": str(AUGMENT_CHECKPOINTS_ROOT / "rogue/rogue1B_diffb1m_chunk30"),
        "prompt": PROMPT_CONFIGS["rogue"],
    },
}


def split_batch(items: list[Any], batch_size: int) -> list[list[Any]]:
    """Split items into batches of size `batch_size`. If 0, returns a single batch."""
    if batch_size == 0:
        return [items]
    return [items[i : i + batch_size] for i in range(0, len(items), batch_size)]


@pytest.mark.parametrize(
    "model_names,test_data_name,batch_size,tols",
    [
        pytest.param(
            # TODO(jeff): "starcoderbase_1b_hf" is entirely in bfloat16...
            ["starcoderbase_1b", "starcoderbase_1b_ff"],
            "basic_inputs",
            0,
            {"rtol": 0.01, "atol": 0.20, "rmse_tol": 0.04},
            id="starcoderbase_1b",
        ),
        pytest.param(
            ["rogue_1b", "rogue_1b_ff"],
            "basic_inputs",
            0,
            {"rtol": 0.01, "atol": 0.30, "rmse_tol": 0.08},
            id="rogue_1b",
        ),
        # An example 7b test, tolerances might not actually be accurate.
        pytest.param(
            ["starcoderbase_7b", "starcoderbase_7b_ff"],
            "basic_inputs",
            0,
            {"rtol": 0.01, "atol": 0.5, "rmse_tol": 0.25},
            marks=[pytest.mark.large_gpu, pytest.mark.slow],
            id="starcoderbase_7b",
        ),
    ],
)
def test_forward_pass(
    model_names: list[str],
    test_data_name: str,
    batch_size: int,
    tols: dict[str, float],
    model_input_test_data: dict[str, list[ModelInput]],
):
    """Test forward pass."""

    # Import inside the test case to avoid pytest --forked / CUDA problems
    # TODO(jeff): avoid cross-package import for create_model.
    import research.models.fastforward_models  # pylint: disable=unused-import # pylint: disable=import-outside-toplevel # noqa: F401
    from research.eval.harness.factories import (
        create_model,  # pylint: disable=import-outside-toplevel
    )

    model_inputs = model_input_test_data[test_data_name]
    batches = split_batch(model_inputs, batch_size)
    configs = [MODEL_CONFIGS[model_name] for model_name in model_names]

    # Iterate batch first in case the logit outputs are too large.
    for batch in batches:
        # Run each model and collect the outputs
        all_outputs = []
        for config in configs:
            model = create_model(config)
            relocate_model(model)
            model.load()
            outputs = model.forward_pass(batch)
            all_outputs.append(outputs)
            model.unload()

        # Iterate by input and test outputs against first model.
        for j in range(len(all_outputs[0])):
            for i in range(1, len(model_names)):
                logger.info("Comparing %s to %s", model_names[i], model_names[0])
                actual = all_outputs[i][j]
                expected = all_outputs[0][j]
                torch.testing.assert_close(actual.input_tokens, expected.input_tokens)
                torch.testing.assert_close(actual.label_tokens, expected.label_tokens)
                torch.testing.assert_close(actual.target_mask, expected.target_mask)
                # GPT-neox adds dummy tokens which end up being zeroed out.
                max_logits = min(actual.logits.size(-1), expected.logits.size(-1))

                # The rounding errors can become fairly large element-wise.
                torch.testing.assert_close(
                    actual.logits[:, :max_logits],
                    expected.logits[:, :max_logits],
                    check_dtype=False,
                    rtol=tols["rtol"],
                    atol=tols["atol"],
                )

                # sqrt_mse per token seems to be more reliable.
                sqrt_mse = actual.logits.float() - expected.logits.float()
                sqrt_mse = (sqrt_mse**2).mean(dim=-1) ** 0.5
                np.testing.assert_array_less(
                    sqrt_mse.numpy(force=True), tols["rmse_tol"]
                )

                # For some checkpoints (e.g. starcoderbase), HF has the vocab size at
                # 49153 whereas GPT-NeoX has rounded it up to 512000. In that case, the
                # last 2047 embeddings will be zero. So if comparing the two models
                # is desired, we need to compare the last entries against zero.
                torch.testing.assert_close(
                    actual.logits[:, max_logits:],
                    torch.zeros_like(actual.logits[:, max_logits:]),
                )
                torch.testing.assert_close(
                    expected.logits[:, max_logits:],
                    torch.zeros_like(expected.logits[:, max_logits:]),
                )


# TODO(jeff): test generate methods
def test_generate():
    """Test generate."""

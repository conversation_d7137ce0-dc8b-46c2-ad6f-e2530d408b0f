"""Provides a caching proxy around any embedding model."""

import hashlib
import shelve
from collections.abc import Sequence
from pathlib import Path

import numpy as np
import torch

from research.models.embedding_model import EmbeddingModel, Tokens

_VALIDATION_KEY = "__validation_key__"
"""Special key used to validate that the embedding model is what the cache expects."""


def _hash_tokens(tokens: Tokens) -> str:
    """Compute a stable hash for a list of tokens.

    We prefer this approach to using Python's `hash` function which is not guaranteed to
    be stable across runs or Python versions.

    Args:
        tokens: A list of tokens.

    Returns:
        A hex digest of the SHA256 hash of the tokens.
    """
    return hashlib.sha256(np.array(tokens, dtype=np.int32).tobytes()).hexdigest()


class CachedEmbeddingModel(EmbeddingModel):
    def __init__(self, embedding_model: EmbeddingModel, cache_path: Path | str):
        self.embedding_model = embedding_model

        Path(cache_path).mkdir(exist_ok=True, parents=True)
        self._cache = shelve.open(str(cache_path), writeback=True)

        # Validate that the embedder we have is the same as one used to create the
        # cache. We do this by storing a sentinel embedding value that will change with
        # different checkpoints. Note that this is also sensitive to changes in e.g.
        # quantization or GPUs.
        empty_embedding = (
            self.embedding_model.embed_batch(
                [[self.embedding_model.embedding_token_id]]
            )
            .float()
            .cpu()
            .numpy()
        )
        if _VALIDATION_KEY not in self._cache:
            self._cache[_VALIDATION_KEY] = empty_embedding
        elif not np.allclose(empty_embedding, self._cache[_VALIDATION_KEY]):
            # Close the cache to make sure we clean up before raising an error.
            self._cache.close()

            raise ValueError(
                "The embedding model generated a different embedding than the one used "
                f"to generate the cache at {cache_path=}. Either provide a different "
                "path or delete the existing cache."
            )

    @property
    def embedding_dim(self) -> int:
        """The embedding dimension."""
        return self.embedding_model.embedding_dim

    @property
    def embedding_token_id(self) -> int:
        """The token id used to extract embeddings."""
        return self.embedding_model.embedding_token_id

    def embed_batch(self, tokens_batch: Sequence[Tokens]) -> torch.Tensor:
        """Embeds a batch of tokens.

        Args:
            tokens_batch: A batch_size list of tokens to embed.

        Returns:
            A tensor of shape [batch_size, emb_dim] corresponding to the embeddings of
            the first token with `embedding_token_id`.
        """
        tokens_hashes = [_hash_tokens(tokens) for tokens in tokens_batch]
        is_present = [hsh in self._cache for hsh in tokens_hashes]
        if not all(is_present):
            # Compute a smaller batch to embed
            to_embed = [
                tokens
                for tokens, present in zip(tokens_batch, is_present)
                if not present
            ]
            embeddings = (
                self.embedding_model.embed_batch(to_embed).float().cpu().numpy()
            )
            embeddings_hashes = [
                hsh for hsh, present in zip(tokens_hashes, is_present) if not present
            ]
            self._cache.update(
                {hsh: emb for hsh, emb in zip(embeddings_hashes, embeddings)}
            )
            self._cache.sync()

        return torch.from_numpy(np.stack([self._cache[hsh] for hsh in tokens_hashes]))

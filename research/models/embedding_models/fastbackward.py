"""Implementation of embedding models in fastbackward."""

import math
from collections.abc import Sequence
from pathlib import Path

import torch
import tqdm
from tensordict import TensorDict

from research.fastbackward import retrieval_models
from research.fastbackward.model import Transformer
from research.fastbackward.retrieval_models import (
    DualEncoderModel,
    TransformerEmbedder,
)
from research.fastbackward.utils import batched
from research.models.embedding_model import EmbeddingModel


class FastBackwardEmbeddingModel(EmbeddingModel):
    def __init__(
        self,
        model: TransformerEmbedder,
        embedding_token_id: int,
        max_batch_size: int = 32,
        max_sequence_len: int = 8192,
    ):
        """Create a fastbackward embedding model.

        Args:
            model: The model to use for embedding.
            embedding_token_id: The token id to use for extracting embeddings.
            max_batch_size: The maximum batch size to use.
                NOTE(arun): There's a tradeoff between poor utilization with
                small batches and extra compute due to padding tokens with large
                batches. The default of 32 was chosen based on benchmarks for the
                next-edit project, and should be reasonable, but YMMV.
            max_sequence_len: The maximum sequence length supported by the underlying
                language model. We will assert that all sequences are shorter
                than this.
        """
        self.model = model
        self._embedding_token_id = embedding_token_id
        self.max_batch_size = max_batch_size
        self._max_sequence_len = max_sequence_len

        # Prepare the model for evaluation.
        self.model.eval()
        self.model.requires_grad_(False)
        # fastbackward models need to be on the GPU.
        self.model.cuda()

    @property
    def embedding_token_id(self) -> int:
        return self._embedding_token_id

    @property
    def embedding_dim(self) -> int:
        return self.model.config.output_projection_dim

    def embed_batch(self, tokens_batch: Sequence[list[int]]) -> torch.Tensor:
        for i, tokens in enumerate(tokens_batch):
            assert self.embedding_token_id in tokens, (
                f"Missing embedding token {self.embedding_token_id} in tokens #{i}: "
                f"{tokens[:5]}...{tokens[-5:] if len(tokens) > 10 else tokens[5:]}"
            )
            assert (
                len(tokens) <= self._max_sequence_len
            ), f"Sequence too long: {len(tokens)} > {self._max_sequence_len}."

        # Guess the device based on where the model is.
        my_device = self.model.output_projection.weight.device

        # Sort the input by sequence length to minimize wasted padding.
        sorted_ixs = torch.argsort(torch.tensor([len(toks) for toks in tokens_batch]))
        tokens_batch = [tokens_batch[ix] for ix in sorted_ixs.tolist()]

        # Prepare batch for embedding.
        emb_BD_microbatches = []

        # Split the batch into chunks of size `max_batch_size`.
        for tokens_batch_micro in tqdm.tqdm(
            batched(tokens_batch, self.max_batch_size),
            total=math.ceil(len(tokens_batch) / self.max_batch_size),
            desc="Embedding batches",
        ):
            # Pad sequences only for each micro-batch to avoid wasted padding.
            Bm = len(tokens_batch_micro)
            tokens_BmL = torch.nn.utils.rnn.pad_sequence(
                list(map(torch.tensor, tokens_batch_micro)),
                batch_first=True,
                padding_value=0,
            )

            X_micro = TensorDict(
                {
                    "tokens_BL": tokens_BmL,
                    "emb_tokens_B": torch.tensor(
                        [self.embedding_token_id] * Bm, dtype=torch.long
                    ),
                },
                batch_size=[Bm],
                device=my_device,
            )

            emb_BD_microbatches.append(self.model(X_micro))

        emb_BD = torch.cat(emb_BD_microbatches)

        # Undo the sorting.
        unsorted_ixs = torch.argsort(sorted_ixs).to(my_device)
        emb_BD = emb_BD[unsorted_ixs]
        return emb_BD


def create_from_dual_encoder_checkpoint(
    checkpoint_path: Path | str,
    max_batch_size: int = 8,
    dtype: torch.dtype | None = torch.bfloat16,
    model_key: str = "model",
) -> tuple[FastBackwardEmbeddingModel, FastBackwardEmbeddingModel]:
    """Create a fastbackward embedding model from a dual encoder checkpoint.

    Args:
        checkpoint_path: The path to the checkpoint.
        max_batch_size: The maximum batch size to use.
        dtype: The dtype to cast the model. If none, use the dtype loaded from the
            checkpoint.

    Returns:
        A tuple of query and document embedders.
    """
    components = retrieval_models.load_checkpoint(checkpoint_path)
    model = components.get_with_type(model_key, DualEncoderModel)
    assert isinstance(model.query_model, TransformerEmbedder)
    assert isinstance(model.doc_model, TransformerEmbedder)
    assert isinstance(model.query_model.lm, Transformer)
    assert isinstance(model.doc_model.lm, Transformer)

    query_model = FastBackwardEmbeddingModel(
        model.query_model,
        embedding_token_id=model.config.query_token_id,
        max_batch_size=max_batch_size,
        max_sequence_len=model.query_model.lm.params.max_seq_len,
    )
    doc_model = FastBackwardEmbeddingModel(
        model.doc_model,
        embedding_token_id=model.config.document_token_id,
        max_batch_size=max_batch_size,
        max_sequence_len=model.doc_model.lm.params.max_seq_len,
    )

    if dtype is not None:
        query_model.model.to(dtype=dtype)
        doc_model.model.to(dtype=dtype)

    return query_model, doc_model


def create_from_neox_starethanol_checkpoint(
    checkpoint_path: Path | str,
    max_batch_size: int = 32,
    dtype: torch.dtype | None = torch.float16,
) -> tuple[FastBackwardEmbeddingModel, FastBackwardEmbeddingModel]:
    """Create a fastbackward embedding model from a neox starethanol checkpoint.

    This function is mostly there to test fastbackward with neox checkpoints: most of
    the time, you'll want to use `create_from_dual_encoder_checkpoint` instead.

    Args:
        checkpoint_path: The path to the checkpoint.
        max_batch_size: The maximum batch size to use.
        dtype: The dtype to cast the model. If none, use the dtype loaded from the
            checkpoint.

    Returns:
        A tuple of query and document embedders.
    """
    from base.tokenizers import TiktokenStarCoderTokenizer
    from research.fastbackward.checkpointing.neox import load_starethanol_checkpoint

    tokenizer = TiktokenStarCoderTokenizer()

    checkpoint_path = Path(checkpoint_path)

    query_model = doc_model = load_starethanol_checkpoint(
        checkpoint_path, use_activation_checkpointing=False
    )
    assert isinstance(query_model.lm, Transformer)
    assert isinstance(doc_model.lm, Transformer)

    query_model = FastBackwardEmbeddingModel(
        query_model,
        embedding_token_id=tokenizer.special_tokens.end_of_query,
        max_batch_size=max_batch_size,
        max_sequence_len=query_model.lm.params.max_seq_len,
    )
    doc_model = FastBackwardEmbeddingModel(
        doc_model,
        embedding_token_id=tokenizer.special_tokens.end_of_key,
        max_batch_size=max_batch_size,
        max_sequence_len=doc_model.lm.params.max_seq_len,
    )

    if dtype is not None:
        query_model.model.to(dtype=dtype)
        doc_model.model.to(dtype=dtype)

    return query_model, doc_model

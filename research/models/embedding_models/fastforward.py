"""Implementation of embedding models in fastbackward."""

from collections.abc import Sequence
from pathlib import Path

import torch
from tqdm import tqdm

from base.fastforward import fwd_utils
from base.fastforward.fwd import AttentionFactory, ForwardStepFn, OutputTensorType
from base.fastforward.fwd_utils import get_model_spec_from_neox_checkpoint
from base.tokenizers import create_tokenizer_by_name
from base.tokenizers.tokenizer import RetrievalSpecialTokens
from research.models.embedding_model import EmbeddingModel

import logging

logger = logging.getLogger(__file__)


class FastForwardEmbeddingModel(EmbeddingModel):
    def __init__(
        self,
        step_fn: ForwardStepFn,
        attention_factory: AttentionFactory,
        embedding_token_id: int,
        embedding_dim: int,
        max_batch_size: int = 128,
    ):
        """Create a fastbackward embedding model.

        Args:
            step_fn: The FastForward step funciton to use to generate embeddings.
            embedding_token_id: The token id to use for extracting embeddings.
            embedding_dim: The dimensionality of the embeddings.
        """
        self.step_fn = step_fn
        self._embedding_token_id = embedding_token_id
        self._embedding_dim = embedding_dim
        self.attn = attention_factory(max_length=8192)
        self.max_batch_size = max_batch_size

    @property
    def embedding_token_id(self) -> int:
        return self._embedding_token_id

    @property
    def embedding_dim(self) -> int:
        return self._embedding_dim

    def embed_batch(self, tokens_batch: Sequence[list[int]]) -> torch.Tensor:
        full_embeddings = None
        for i in tqdm(
            range(0, len(tokens_batch), self.max_batch_size), desc="Embedding batches"
        ):
            logger.info("Embedding batch %d / %d", i, len(tokens_batch))
            iter_embeddings = torch.stack(
                [
                    fwd_utils.get_embeddings(
                        self.step_fn, self.attn, tokens, [self.embedding_token_id]
                    )
                    for tokens in tokens_batch[i : i + self.max_batch_size]
                ],
            )
            if full_embeddings is None:
                full_embeddings = iter_embeddings
            else:
                full_embeddings = torch.cat([full_embeddings, iter_embeddings], dim=0)
        assert full_embeddings is not None
        return full_embeddings


def create_from_starethanol_neox_checkpoint(
    checkpoint_path: Path | str,
    dtype: torch.dtype | None = torch.float16,
) -> tuple[FastForwardEmbeddingModel, FastForwardEmbeddingModel]:
    """Create a fastbackward embedding model from a dual encoder checkpoint.

    Args:
        checkpoint_path: The path to the checkpoint.
        max_batch_size: The maximum batch size to use.
        dtype: The dtype to cast the model. If none, use the dtype loaded from the
            checkpoint.

    Returns:
        A tuple of query and document embedders.
    """
    from base.fastforward.starcoder.fwd_starcoder import (
        StarCoder,
        StarcoderAttentionFactory,
        generate_step_fn,
    )

    ffwd_model_spec = get_model_spec_from_neox_checkpoint(checkpoint_path)
    ffwd_model = generate_step_fn(
        ffwd_model_spec,
        auto_capture_graphs=False,
        output_type=OutputTensorType.EMBEDDING,
    )
    assert isinstance(ffwd_model, StarCoder)
    assert isinstance(ffwd_model.final_projection, torch.nn.Linear)

    embedding_dim = int(ffwd_model.final_projection.weight.shape[0])

    ffwd_model.cuda()
    if dtype is not None:
        ffwd_model.to(dtype=dtype)

    # Prepare the model for evaluation.
    ffwd_model.eval()
    ffwd_model.requires_grad_(False)

    attn_factory = StarcoderAttentionFactory(ffwd_model_spec)
    tokenizer = create_tokenizer_by_name("starcoder")
    assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)

    query_model = FastForwardEmbeddingModel(
        ffwd_model,
        attn_factory,
        embedding_token_id=tokenizer.special_tokens.end_of_query,
        embedding_dim=embedding_dim,
    )
    doc_model = FastForwardEmbeddingModel(
        ffwd_model,
        attn_factory,
        embedding_token_id=tokenizer.special_tokens.end_of_key,
        embedding_dim=embedding_dim,
    )

    return query_model, doc_model


def create_from_llama_retriever_checkpoint(
    model_name: str,
    tokenizer_name: str,
    checkpoint_path: Path | str,
    dtype: torch.dtype | None = torch.bfloat16,
    sha256: str | None = None,
    use_fp8: bool = False,
) -> tuple[FastForwardEmbeddingModel, FastForwardEmbeddingModel]:
    """Create a fastbackward embedding model from a converted checkpoint trained
    with fastbackward.
    """
    from base.fastforward.llama import fwd_llama, fwd_llama_fp8
    from base.fastforward.llama.model_specs import get_llama_model_spec, LlamaModelSpec

    ffwd_model_spec = get_llama_model_spec(
        model_name=model_name,
        checkpoint_path=checkpoint_path,
        checkpoint_sha256=sha256,
    )
    assert isinstance(ffwd_model_spec, LlamaModelSpec)

    if use_fp8:
        ffwd_model = fwd_llama_fp8.generate_step_fn(
            ffwd_model_spec,
            auto_capture_graphs=False,
            output_type=OutputTensorType.EMBEDDING,
        )
        assert isinstance(ffwd_model, fwd_llama_fp8.Llama)
    else:
        ffwd_model = fwd_llama.generate_step_fn(
            ffwd_model_spec,
            auto_capture_graphs=False,
            output_type=OutputTensorType.EMBEDDING,
        )
        assert isinstance(ffwd_model, fwd_llama.Llama)
    assert isinstance(ffwd_model.output_projection, torch.nn.Linear)
    ffwd_model.cuda()
    ffwd_model.eval()
    if dtype is not None and not use_fp8:
        ffwd_model.to(dtype=dtype)
    if use_fp8:
        round_sizes = (32, 128, 512, 2048)
        ffwd_model = fwd_utils.pad_and_step(ffwd_model, round_sizes=round_sizes)
    attn_factory = fwd_llama.LlamaAttentionFactory(
        ffwd_model_spec, dtype=dtype or torch.bfloat16
    )
    tokenizer = create_tokenizer_by_name(tokenizer_name)

    # ffwd_model_spec.emb_dim is the hidden dim, not the output projection dim
    embedding_dim = ffwd_model_spec.output_projection_dim
    assert (
        embedding_dim is not None
    ), "Must have output projection dim defined for a retriever."
    assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
    query_model = FastForwardEmbeddingModel(
        ffwd_model,
        attention_factory=attn_factory,
        embedding_token_id=tokenizer.special_tokens.end_of_query,
        embedding_dim=embedding_dim,
    )
    doc_model = FastForwardEmbeddingModel(
        ffwd_model,
        attention_factory=attn_factory,
        embedding_token_id=tokenizer.special_tokens.end_of_key,
        embedding_dim=embedding_dim,
    )
    return query_model, doc_model

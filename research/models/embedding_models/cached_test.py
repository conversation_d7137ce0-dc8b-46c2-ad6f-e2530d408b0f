import tempfile
from collections.abc import Sequence
from pathlib import Path
from unittest.mock import MagicMock

import pytest
import torch

from research.models.embedding_model import EmbeddingModel, Tokens
from research.models.embedding_models.cached import CachedEmbeddingModel


@pytest.fixture()
def mock_embedding_model():
    def embed_batch(tokens_batch: Sequence[Tokens]):
        if tokens_batch == [[99]]:
            return torch.zeros(len(tokens_batch), 128)
        else:
            return torch.ones(len(tokens_batch), 128)

    base_model = MagicMock(
        EmbeddingModel,
        embedding_dim=128,
        embedding_token_id=99,
    )
    base_model.embed_batch.side_effect = embed_batch
    return base_model


def test_caching_works(mock_embedding_model: EmbeddingModel):
    assert isinstance(mock_embedding_model, EmbeddingModel)
    assert isinstance(mock_embedding_model, MagicMock)

    with tempfile.TemporaryDirectory() as tmp_dir:
        cached_model = CachedEmbeddingModel(
            mock_embedding_model, Path(tmp_dir) / "cache"
        )
        # When creating a model, we should call the base model once to validate the
        # cache.
        assert mock_embedding_model.embed_batch.call_count == 1
        mock_embedding_model.embed_batch.reset_mock()

        cached_model.embed_batch(
            [
                [1, 2, 3, 99],
                [4, 5, 99],
            ]
        )
        # We have never seen these tokens before, so we should call the base model.
        assert mock_embedding_model.embed_batch.call_count == 1
        mock_embedding_model.embed_batch.reset_mock()

        cached_model.embed_batch(
            [
                [1, 2, 3, 99],
                [6, 7, 8, 99],
                [4, 5, 99],
            ]
        )
        # We should call the base model only on new input.
        assert mock_embedding_model.embed_batch.call_count == 1
        assert mock_embedding_model.embed_batch.call_args.args == ([[6, 7, 8, 99]],)
        mock_embedding_model.embed_batch.reset_mock()

        # Delete the cached model and create a new one.
        del cached_model

        cached_model = CachedEmbeddingModel(
            mock_embedding_model, Path(tmp_dir) / "cache"
        )
        # Call the base model once to validate the cache.
        assert mock_embedding_model.embed_batch.call_count == 1
        mock_embedding_model.embed_batch.reset_mock()

        cached_model.embed_batch(
            [
                [1, 2, 3, 99],
                [4, 5, 99],
            ]
        )
        # Then don't call it again because we have the cache.
        assert mock_embedding_model.embed_batch.call_count == 0
        # Delete the cached model to make sure we clean up.
        del cached_model


def test_invalid_cache_fails():
    base_model: EmbeddingModel = MagicMock(
        EmbeddingModel,
        embedding_dim=128,
        embedding_token_id=99,
    )

    with tempfile.TemporaryDirectory() as tmp_dir:
        base_model.embed_batch.return_value = torch.zeros(1, 128)
        cached_model = CachedEmbeddingModel(base_model, Path(tmp_dir) / "cache")
        # When creating a model, we should call the base model once to validate the
        # cache.
        assert base_model.embed_batch.call_count == 1
        base_model.embed_batch.reset_mock()

        # Delete the cached model and create a new one.
        del cached_model

        # Change the base model behavior.
        base_model.embed_batch.return_value = torch.ones(1, 128)
        with pytest.raises(ValueError):
            CachedEmbeddingModel(base_model, Path(tmp_dir) / "cache")

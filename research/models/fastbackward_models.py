"""Implementations for the research FastBackward models."""

import json
import logging
import os
import pathlib
import time
import typing

import torch
import torch.nn.functional as F

from research.core.all_prompt_formatters import (
    get_prompt_formatter,
)
from research.core.constants import AUGMENT_CHECKPOINTS_ROOT
from research.core.model_input import ModelInput
from research.fastbackward.utils import sample_top_p
from research.models.meta_model import (
    FimGenMode,
    GenerationCanceledError,
    GenerationOptions,
    GenerativeLanguageModel,
    ModelForwardOutput,
    ModelPrepareForwardOutput,
    RawGenerateOutput,
    register_model,
)


@register_model("fastbackward")
class FastBackwardLLM(GenerativeLanguageModel):
    """The FastBackward Research Model."""

    def __init__(
        self,
        checkpoint_path: pathlib.Path | None,
        seq_length: int,
        model_parallel_size: int | None = None,
        # We should deprecate "fim_gen_mode" from the model interface, since it should be handled by system
        fim_gen_mode: FimGenMode = FimGenMode.raw,
        kv_cache_batch_size: int = 1,
    ):
        self.checkpoint_path = checkpoint_path
        if self.checkpoint_path is not None and not self.checkpoint_path.is_absolute():
            self.checkpoint_path = AUGMENT_CHECKPOINTS_ROOT / self.checkpoint_path
        assert self.checkpoint_path is not None
        self.seq_length = seq_length
        self.fim_gen_mode = fim_gen_mode
        if model_parallel_size is None:
            self.model_parallel_size = torch.cuda.device_count()
        else:
            self.model_parallel_size = model_parallel_size
        assert self.model_parallel_size <= torch.cuda.device_count()
        # Always keep max_batch_size as 1 for now.
        self.max_batch_size = 1
        self.random_seed = 1
        # model_runner and model_args will be set through the load function
        self.model_runner = None
        self.model_args = None
        self.kv_cache_batch_size = kv_cache_batch_size

    @classmethod
    def create_default_formatter(cls):
        return get_prompt_formatter("basic")

    def load(self):
        """Load the model."""
        # NOTE(fastbackward-import): importing model code from fastbackward includes code annotated
        # with `torch.compile`. For unknown reasons, this causes the "CUDA already initialized"
        # issue when importing at the top level in a pytest run (that executes imports during
        # test collection). So we push these imports into the function itself.
        # TODO: figure out why this happens here and not elsewhere.
        from research.fastbackward.inference import ParallelTransformerRunner
        from research.fastbackward.model import ModelArgs, fix_model_arg_params

        if self.checkpoint_path is None:
            raise ValueError("checkpoint_path must be set.")
        if self.model_runner:
            logging.info("The model is already loaded, skip loading.")
            return
        # Note: the ParallelModelRunner has no randomness, so this seed is for the main process in
        # top-k sampling (if enabled).
        torch.manual_seed(self.random_seed)
        start_time = time.time()
        checkpoint_path = pathlib.Path(self.checkpoint_path)
        param_path = checkpoint_path / "params.json"
        with param_path.open("r") as f:
            params = json.loads(f.read())
        if "max_seq_len" in params:
            # Ensure the model can support at least this seqlen
            assert params["max_seq_len"] >= self.seq_length
        else:
            params["max_seq_len"] = self.seq_length
        params["max_generation_batch_size"] = self.max_batch_size

        params = fix_model_arg_params(params)
        # NOTE(xuanyi): ideally, use_sequence_parallel should not be on `ModelArgs`, as it's execution properties.
        # In particular, it should be mutable. Regardless, sequence parallel makes no sense for
        # token-at-a-time generation, but a checkpoint might have `sequence_parallel=True` from
        # training. We manually disable it here.
        params["use_sequence_parallel"] = False

        params["kv_cache_batch_size"] = self.kv_cache_batch_size
        # NOTE(tongfei): Custom kv_cache_batch_size that overrides the default value of 1 in ModelArgs.

        model_args = ModelArgs.load_from_dict(params)
        logging.info(f"params: {params}")
        logging.info(f"After fix, the model args: {model_args}")
        model_runner = ParallelTransformerRunner(self.model_parallel_size)
        model_runner.load_model(model_args, checkpoint_path)
        logging.info(f"Loaded in {time.time() - start_time:.2f} seconds")
        self.model_runner = model_runner
        self.model_args = model_args

    def unload(self):
        """Unload the model."""
        if self.model_runner is not None:  # Makes pyright happy
            self.model_runner.unload_model()
        self.model_runner = None  # pylint: disable=attribute-defined-outside-init
        self.model_args = None  # pylint: disable=attribute-defined-outside-init
        torch.cuda.empty_cache()

    @property
    def is_loaded(self):
        return self.model_runner is not None

    @torch.inference_mode()
    def raw_generate_tokens(
        self,
        prompt_tokens: list[int],
        options: GenerationOptions,
        should_cancel: typing.Callable[[], bool] = lambda: False,
    ) -> RawGenerateOutput:
        """Produce the final prediction."""
        # See NOTE(fastbackward-import).
        from research.fastbackward.inference import ParallelTransformerRunner

        if (options.stop_criteria is not None) and (
            not options.stop_criteria.is_empty()
        ):
            raise NotImplementedError("Hugging Face models do not support stop yet.")
        bsz = 1
        if bsz != self.max_batch_size:
            raise NotImplementedError("Currently only support match size of 1.")
        if options.top_k:
            raise NotImplementedError("Currently do not support top_k sample.")
        assert isinstance(self.model_runner, ParallelTransformerRunner)
        # the total buffer size
        if options.max_generated_tokens is None:
            total_len = self.seq_length
        else:
            total_len = min(
                len(prompt_tokens) + options.max_generated_tokens, self.seq_length
            )

        device = f"cuda:{int(os.environ.get('LOCAL_RANK', 0))}"
        with torch.inference_mode():
            if options.stop_tokens is not None and len(options.stop_tokens) > 0:
                stop_tokens = torch.tensor(list(options.stop_tokens), device=device)
            else:
                stop_tokens = torch.tensor(
                    [self.tokenizer.special_tokens.eos], device=device
                )
            tokens = torch.full((bsz, total_len), -1, dtype=torch.long, device=device)
            tokens[0, : len(prompt_tokens)] = torch.tensor(
                prompt_tokens, dtype=torch.long, device=device
            )

            prev_pos = 0
            eod_reached = torch.tensor([False] * bsz, device=device)
            input_text_mask = tokens != -1
            token_logits: list[torch.Tensor] = []
            for cur_pos in range(len(prompt_tokens), total_len):
                if should_cancel():
                    raise GenerationCanceledError()
                logits = self.model_runner.generate(
                    tokens[:, prev_pos:cur_pos], prev_pos
                )

                if options.temperature > 0:
                    assert options.top_p is not None
                    probs = torch.softmax(logits[:, -1] / options.temperature, dim=-1)
                    next_token = sample_top_p(probs, options.top_p)
                else:
                    next_token = torch.argmax(logits[:, -1], dim=-1)
                token_logits.append(logits[0, -1])
                next_token = next_token.reshape(-1)
                # only replace token if prompt has already been generated
                next_token = torch.where(
                    input_text_mask[:, cur_pos], tokens[:, cur_pos], next_token
                )
                tokens[:, cur_pos] = next_token
                eod_reached |= (~input_text_mask[:, cur_pos]) & (
                    torch.isin(next_token, stop_tokens)
                )
                prev_pos = cur_pos
                if all(eod_reached):
                    break
            # NOTE: "generated_tokens" might be a confusing name, as it also contains prompt tokens
            [generated_tokens] = tokens[:, : prev_pos + 1].tolist()
            return RawGenerateOutput(
                tokens=generated_tokens[len(prompt_tokens) :], logits=token_logits
            )

    def raw_generate(self, prompt_tokens: list[int], options: GenerationOptions) -> str:
        """Generate a completion based on the tokenized prompt."""
        raw_generation_output = self.raw_generate_tokens(prompt_tokens, options)
        generated_tokens = self.postprocess_generation(
            prompt_tokens + raw_generation_output.tokens, len(prompt_tokens)
        )
        final_text = self.tokenizer.detokenize(generated_tokens)
        prompt_text = self.tokenizer.detokenize(prompt_tokens)
        generated_text = final_text[len(prompt_text) :]
        return generated_text

    def forward_pass_single_logits(self, input_tokens: torch.Tensor) -> torch.Tensor:
        """Runs a raw forward pass on a single tokenized input.

        Args:
            input_tokens: the tokenized input tensor.

        Returns:
            A tensor with the output logits for each input token.
        """
        assert self.model_runner is not None
        if len(input_tokens) > self.seq_length:
            raise ValueError(
                f"Model input too long: {len(input_tokens)} > {self.seq_length}"
            )

        with torch.inference_mode():
            # `model.generate` expects a (dummy) batch dimension
            logits = self.model_runner.generate(input_tokens.unsqueeze(0), start_pos=0)
        return logits.squeeze(0)  # remove dummy dimension

    def forward_pass_multiple_logits(self, input_tokens: torch.Tensor) -> torch.Tensor:
        """Runs a raw forward pass on a multiple tokenized input.

        Args:
            input_tokens: the tokenized input tensor, shape [batch, seqlen]

        Returns:
            A tensor with the output logits for each input token.
        """
        assert self.model_runner is not None
        if len(input_tokens) > self.seq_length:
            raise ValueError(
                f"Model input too long: {len(input_tokens)} > {self.seq_length}"
            )

        with torch.inference_mode():
            logits = self.model_runner.generate(input_tokens, start_pos=0)
        return logits

    def _forward_pass_single(self, model_input: ModelInput) -> ModelForwardOutput:
        """Runs a forward pass on a single ModelInput input.

        Wraps _forward_pass_single_logits by performing formatting and tokenization.
        """
        x = self.prepare_forward_pass(model_input)
        logits = self.forward_pass_single_logits(x.input_tokens)

        return ModelForwardOutput(
            logits=logits,
            input_tokens=x.input_tokens,
            label_tokens=x.label_tokens,
            target_mask=x.target_mask,
        )

    def _forward_pass_multiple(
        self, model_inputs: list[ModelInput]
    ) -> list[ModelForwardOutput]:
        """Runs a forward pass on a multiple ModelInput inputs.

        Wraps _forward_pass_single_logits by performing formatting and tokenization.
        """
        xs: list[ModelPrepareForwardOutput] = [
            self.prepare_forward_pass(model_input) for model_input in model_inputs
        ]
        input_tokens_max_length = max([len(x.input_tokens) for x in xs])
        batched_input_tokens = torch.stack(
            [
                torch.cat(
                    [
                        x.input_tokens,
                        torch.full(
                            [input_tokens_max_length - len(x.input_tokens)],
                            self.tokenizer.special_tokens.padding,
                            dtype=torch.long,
                            device=x.input_tokens.device,
                        ),
                    ],
                    dim=0,
                )
                for x in xs
            ],
            dim=0,
        )
        logits = self.forward_pass_multiple_logits(batched_input_tokens)
        return [
            ModelForwardOutput(
                logits=logits[i, : len(xs[i].input_tokens)],
                input_tokens=xs[i].input_tokens,
                label_tokens=xs[i].label_tokens,
                target_mask=xs[i].target_mask,
            )
            for i in range(len(xs))
        ]

    def forward_pass(self, inputs: list[ModelInput]) -> list[ModelForwardOutput]:
        """Run the model forward pass."""
        if not self.is_loaded:
            raise ValueError("The model hasn't been loaded yet.")

        outputs = self._forward_pass_multiple(inputs)
        return outputs

    def log_likelihood(self, text: str) -> typing.Tuple[float, torch.Tensor]:
        if self.model_runner is None or self.tokenizer is None:
            raise ValueError("Haven't load the model yet.")
        tokens = self.tokenizer.tokenize_safe(text)
        if len(tokens) == 0:
            raise ValueError("Input is empty, cannot compute log-likelihood")
        if len(tokens) > self.seq_length:
            raise ValueError(
                "Input token length exceeds sequence length in log_likelihood, "
                "results may be unpredictable: "
                f"{len(tokens)} > {self.seq_length}"
            )
        with torch.inference_mode():
            device_tokens = torch.tensor([tokens], dtype=torch.long, device="cuda")
            logits = self.model_runner.generate(device_tokens, 0)
            loss = torch.nn.functional.cross_entropy(
                logits[0], device_tokens[0], reduction="none"
            )
        return loss.item(), logits[0].cpu()

    # TODO: merge with `log_likelihood`
    def raw_log_likelihood(self, prompt_tokens: list[int]) -> torch.Tensor:
        """Compute per-token log-likelihood.

        Args:
            prompt_tokens: Tokenized prompts.

        Returns:
            The 1-D tensor of per-token log-likelihood.
        """
        if not self.is_loaded:
            raise ValueError("The model hasn't been loaded yet.")

        with torch.inference_mode():
            input_ids = torch.tensor([prompt_tokens]).cuda()
            logits = self.model_runner.generate(input_ids, 0)  # type: ignore
            shift_logits = logits[..., :-1, :].contiguous()
            shift_labels = input_ids[..., 1:].contiguous()

            loss = F.cross_entropy(
                shift_logits.view(-1, shift_logits.size(-1)),
                shift_labels.view(-1),
                reduction="none",
            )
            return -loss

"""Describes a protocol for all embedding models.

An embedding model is one that takes tokens in and produces embeddings out.
Embedding model implementations operate at the level of modeling frameworks (neox,
fastforward, fastbackward, etc.).

Prompt formatting is not part of the embedding model protocol, and it is up to the user
to ensure that the tokens provided to the embedding model are appropriate.
"""

import typing
from collections.abc import Sequence

import torch

Tokens = list[int]


@typing.runtime_checkable
class EmbeddingModel(typing.Protocol):
    """Protocol for embedding models: "tokens in, embeddings out"."""

    # NOTE(arun): keeping these properties to allow them to be derived values.
    @property
    def embedding_dim(self) -> int:
        """The embedding dimension."""
        raise NotImplementedError()

    @property
    def embedding_token_id(self) -> int:
        """The token id used to extract embeddings."""
        raise NotImplementedError()

    def embed_batch(self, tokens_batch: Sequence[Tokens]) -> torch.Tensor:
        """Embeds a batch of tokens.

        Args:
            tokens_batch: A batch_size list of tokens to embed.

        Returns:
            A tensor of shape [batch_size, emb_dim] corresponding to the embeddings of
            the first token with `embedding_token_id`.
        """
        raise NotImplementedError()

{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Examples of Running LLAMA2 Models"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Load the model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "from termcolor import colored\n", "from research.models import get_model, list_models, GenerationOptions\n", "\n", "all_model_names = list_models()\n", "print(f'We have {len(all_model_names)} models in total.')\n", "print(f\": {all_model_names}\")\n", "# logging.disable(logging.CRITICAL)  # Disable a lot of annoying logs\n", "\n", "checkpoints_root = \"/home/<USER>/checkpoints\"\n", "\n", "model = get_model(\"llama2-13b-pretrain-hf\")\n", "print(colored(str(model), color=\"blue\"), end=\"\\n\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.load()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Simple completion"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.model_input import ModelInput\n", "from textwrap import dedent\n", "from termcolor import colored\n", "\n", "prompt = r\"\"\"\n", "Try your best to coherently and concisely complete the following function\n", "The codes star by the 10 repetitive char of >.\n", "After you finish the completion, please output 10 repetitive char of < indicating finish.\n", "Here is an example:\n", ">>>>>>>>>>\n", "def hello_world():\n", "    print(\"Hello World!\")\n", "<<<<<<<<<<\n", "\n", "Ok, now let's start.\n", "\n", ">>>>>>>>>>\n", "from dataclasses import dataclass\n", "\n", "@dataclass\n", "class Turtle:\n", "    '''The turtle can move forward, backward, and turn 90 degrees.'''\n", "    def __init__(\n", "\"\"\"\n", "generated_text = model.generate(\n", "    ModelInput(prefix = prompt),\n", "    GenerationOptions(temperature=0.0, max_generated_tokens=128, top_k=0, top_p=0.0),\n", ")\n", "print(prompt, end=\"\")\n", "print(colored(generated_text, \"green\"))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Demonstration on Stop Criteria"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.model_input import ModelInput\n", "from research.models.starcoder_models import StarCoderBase1B\n", "from research.models import StopCriteria, GenerationOptions\n", "\n", "model = StarCoderBase1B()\n", "model.load()\n", "print(model)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generation without stop condition:\n", "\u001b[34m    with open('input.txt') as f:\n", "        contents = f.read()\n", "    print(contents)\u001b[0m\n", "\n", "Generation with stop condition:\n", "\u001b[32m    with open('input.txt') as f\u001b[0m\n"]}], "source": ["from termcolor import colored\n", "\n", "def compare_stop(prefix: str, suffix: str, stop_texts: list[str]=[\":\"], temperature=0):\n", "    stop_criteria = StopCriteria(stop_texts, check_stopping_condition_every=1)\n", "\n", "    result = model.generate(\n", "        ModelInput(prefix=prefix, suffix=suffix),\n", "        GenerationOptions(temperature=temperature, max_generated_tokens=512, stop_criteria=None),\n", "    )\n", "    print(\"Generation without stop condition:\")\n", "    print(colored(result, color=\"blue\"), end=\"\\n\\n\")\n", "\n", "    result = model.generate(\n", "        ModelInput(prefix=prefix, suffix=suffix),\n", "        GenerationOptions(temperature=temperature, max_generated_tokens=512, stop_criteria=stop_criteria),\n", "    )\n", "    print(\"Generation with stop condition:\")\n", "    print(colored(result, color=\"green\"))\n", "\n", "\n", "sample = \"\"\"\n", "'''Read a file and print its contents.'''\n", "\n", "def main():\n", "<FILL-HERE>\n", "\n", "if __name__ == '__main__':\n", "    main()\n", "\"\"\"\n", "\n", "if \"<FILL-HERE>\" in sample:\n", "    prefix, suffix = sample.split(\"<FILL-HERE>\")\n", "else:\n", "    prefix = sample\n", "    suffix = \"\"\n", "\n", "\n", "compare_stop(prefix, suffix)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generation without stop condition:\n", "\u001b[34m    '''Write a for loop to find the first 100 prime numbers.'''\n", "    for i in range(1, 101):\n", "        if is_prime(i):\n", "            print(i)\u001b[0m\n", "\n", "Generation with stop condition:\n", "\u001b[32m    '''Write a \u001b[0m\n"]}], "source": ["sample = \"\"\"\n", "'''Write a for loop to find the first 100 prime numbers.'''\n", "\n", "def main():\n", "<FILL-HERE>\n", "\n", "if __name__ == '__main__':\n", "    main()\n", "\"\"\"\n", "\n", "if \"<FILL-HERE>\" in sample:\n", "    prefix, suffix = sample.split(\"<FILL-HERE>\")\n", "else:\n", "    prefix = sample\n", "    suffix = \"\"\n", "\n", "\n", "compare_stop(prefix, suffix, stop_texts=['for'])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Code completion using the research models"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(os.environ[\"HOME\"])\n", "\n", "import collections\n", "import logging\n", "from termcolor import colored\n", "import time\n", "from textwrap import dedent\n", "\n", "from research.model_server.launch_model_server import get_docs_from_files\n", "from research.models import GenerationOptions\n", "import research.models.fastforward_models\n", "from research.models.all_models import get_model\n", "from research.models.meta_model import list_models\n", "\n", "from research.retrieval.types import Document, Chunk\n", "from research.retrieval.retrieval_database import RetrievalDatabase\n", "from research.retrieval.chunking_functions import (\n", "    ScopeAwareChunker,\n", "    LineLevelChunker,\n", ")\n", "from research.retrieval.scorers.good_enough_bm25_scorer import (\n", "    GoodEnoughBM25Scorer,\n", ")\n", "from research.core.model_input import ModelInput\n", "from research.models import StopCriteria\n", "\n", "logging.disable(logging.CRITICAL)  # Disable a lot of annoying logs\n", "\n", "def show_available_models():\n", "    \"\"\"Print available models in two columns.\"\"\"\n", "    print(\"Available models:\")\n", "    print(\"=================\")\n", "    available_models = list_models()\n", "    max_padding = max(len(model) for model in available_models[::2]) + 2\n", "    for i in range(0, len(available_models), 2):\n", "        if i < len(available_models) - 1:\n", "            model_name1 = available_models[i]\n", "            model_name2 = available_models[i+1]\n", "            fmt = \"{0:\" + str(max_padding) + \"}  {1}\"\n", "            print(fmt.format(model_name1, model_name2))\n", "\n", "show_available_models()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Load the model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "model = get_model(\"starcoderbase_1b\")\n", "# model = get_model(\"code_llama\", url=\"http://127.0.0.1:8080\", mode=\"chat\")\n", "model.load()\n", "elapsed = time.time() - start\n", "\n", "print(f\"Loaded model '{model.name}' in {elapsed:.1f} seconds\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Simple completion"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt = \"def print_hello():\"\n", "max_generated_tokens=32\n", "options = GenerationOptions(temperature=0.0, max_generated_tokens=max_generated_tokens)\n", "\n", "generated_text = model.generate(\n", "    ModelInput(prefix=prompt),\n", "    options,\n", ")\n", "\n", "print(colored(prompt, \"white\", \"on_blue\"), end=\"\")\n", "print(colored(generated_text, \"green\", \"on_grey\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Model playground"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import gradio\n", "\n", "def generate_completion(\n", "        prefix: str,\n", "        suffix: str,\n", "        temperature: float,\n", "        max_generated_tokens: int\n", "        ):\n", "    model_input = ModelInput(\n", "        prefix=prefix,\n", "        suffix=suffix\n", "    )\n", "    options = GenerationOptions(\n", "        temperature=float(temperature),\n", "        max_generated_tokens=int(max_generated_tokens)\n", "    )\n", "    generated = model.generate(model_input, options)\n", "    return generated\n", "\n", "model_playground = gradio.Interface(\n", "    fn=generate_completion,\n", "    inputs=[\n", "        gradio.Textbox(lines=8, placeholder=\"\"),\n", "        gradio.Textbox(lines=8, placeholder=\"\"),\n", "        gradio.Slider(minimum=0., maximum=1.4, value=0.),\n", "        gradio.Slider(minimum=1, maximum=512, value=32, step=1),\n", "        ],\n", "    outputs=\"text\",\n", "    title=f\"Model: {model.name}\",\n", "    examples=[[\"def hello():\", \"def main():\\n    hello()\", 0, 32]])\n", "    \n", "# Server options:\n", "#\n", "# server_name=\"0.0.0.0\" will expose this server to the world\n", "# auth specifies username / password\n", "# share=True creates a public (though obfuscated) link that can be shared with\n", "# anyone. This seems to be needed to get the UI to show up in VSCode.. :(\n", "\n", "ui_height = 950\n", "# model_playground.launch(server_name=\"0.0.0.0\", auth=(\"cellar\", \"door\"), height=ui_height)\n", "# model_playground.launch(server_name=\"0.0.0.0\", auth=(\"cellar\", \"door\"), height=ui_height, share=True)\n", "model_playground.launch(server_name=\"0.0.0.0\", height=ui_height, share=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Measure time per token"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt = \"print('The quick brown fox jumps over the lazy dog.')\\n\"\n", "max_generated_tokens=64\n", "\n", "for _ in range(3):\n", "    start = time.time()\n", "    generated_text = model.generate(\n", "        ModelInput(prefix=prompt),\n", "        GenerationOptions(temperature=0.0, max_generated_tokens=max_generated_tokens),\n", "    )\n", "    elapsed = time.time() - start\n", "    gen_tokens = model.tokenizer.tokenize(generated_text)\n", "    assert len(gen_tokens) == max_generated_tokens - 1\n", "    print(f\"Generation of {len(gen_tokens)} took {elapsed:.2f} seconds, or {elapsed / max_generated_tokens:.3f} seconds/token\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Completion with Stop Criteria"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt = \"def hello_world():\\n\"\n", "\n", "result = model.generate(\n", "    ModelInput(prefix=prompt),\n", "    GenerationOptions(\n", "        temperature=0.0,\n", "        max_generated_tokens=120,\n", "        stop_criteria=StopCriteria(stop_texts=[\"\\n\"], check_stopping_condition_every=4),\n", "    ),\n", ")\n", "\n", "print(\"Code Completion:\\n\\n\\n\")\n", "print(colored(prompt, color=\"blue\"), end=\"\")\n", "print(colored(result, color=\"green\"))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Generation without retrieval"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a prompt\n", "prompt = dedent(\n", "    \"\"\"\n", "    magic_number = 1231\n", "\n", "    def print_constant():\n", "        '''Print the magic constant defined above.'''\"\"\"\n", ")\n", "\n", "# Generate\n", "generated_text = model.generate(\n", "    ModelInput(prefix=prompt),\n", "    GenerationOptions(temperature=0.0, max_generated_tokens=32, top_k=0, top_p=0.0),\n", ")\n", "print(colored(prompt, \"blue\"), end=\"\")\n", "print(colored(generated_text, \"green\"))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Generation and retrieval using a supplied corpus"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Build a document index\n", "chunker = LineLevelChunker(max_lines_per_chunk=20)\n", "scorer = Good<PERSON><PERSON>ughBM25Scorer()\n", "doc_index = RetrievalDatabase(chunker, scorer)\n", "\n", "docs = [Document(id=\"1\", text=\"magic_number = 1231\")]\n", "doc_index.add_docs(docs)\n", "\n", "# Create a prompt\n", "prompt = dedent(\n", "    \"\"\"\n", "    def print_constant():\n", "        '''Print the magic number defined above.'''\"\"\"\n", ")\n", "\n", "\n", "# Retrieve\n", "chunks, scores = doc_index.query(ModelInput(prefix=prompt), top_k=3)\n", "\n", "# Generate\n", "input = ModelInput(prefix=prompt, retrieved_chunks=chunks)\n", "generated_text = model.generate(\n", "    input,\n", "    GenerationOptions(temperature=0.0, max_generated_tokens=32),\n", ")\n", "print(colored(prompt, \"blue\"), end=\"\")\n", "print(colored(generated_text, \"green\"))\n", "\n", "print(\"\\n\\n======= Final Prompt:\")\n", "print(colored(model.prompt_formatter.prepare_prompt_text(input)[0], \"green\"))\n", "\n", "print(\"\\n\\n====== Retrieved Chunks:\")\n", "for chunk in chunks:\n", "    print(chunk)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Generation and examples with retrieval from our repository"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "print(\"Creating the BM25 index...\")\n", "\n", "chunker = LineLevelChunker(max_lines_per_chunk=20)\n", "scorer = Good<PERSON><PERSON>ughBM25Scorer()\n", "repo_doc_index = RetrievalDatabase(chunker, scorer)\n", "\n", "# Your local augment repo\n", "repo_root = pathlib.Path(os.environ[\"HOME\"], \"src\", \"augment\")\n", "repo_doc_index.add_docs(get_docs_from_files(root=repo_root, include_extensions=[\".py\"]))\n", "\n", "prompt = \"def hello_world():\"\n", "\n", "print(\"\\n======= Generating without retrieval:\")\n", "# generated_text = model.generate(prefix=prompt, retrieve=False)\n", "generated_text = model.generate(\n", "    ModelInput(prefix=prompt, retrieved_chunks=[]),\n", "    GenerationOptions(temperature=0.0, max_generated_tokens=32),\n", ")\n", "print(colored(prompt, \"blue\"), end=\"\")\n", "print(colored(generated_text, \"green\"))\n", "\n", "print(\"\\n\\n======= Retrieving and generating:\")\n", "chunks, scores = repo_doc_index.query(ModelInput(prefix=prompt), top_k=3)\n", "generated_text = model.generate(\n", "    ModelInput(prefix=prompt, retrieved_chunks=chunks),\n", "    GenerationOptions(temperature=0.0, max_generated_tokens=32),\n", ")\n", "print(colored(prompt, \"blue\"), end=\"\")\n", "print(colored(generated_text, \"green\"))\n", "\n", "print(\"\\n\\n======= Prompt when we have retrieval:\")\n", "print(\n", "    colored(\n", "        model.prompt_formatter.prepare_prompt_text(\n", "            ModelInput(prefix=prompt, retrieved_chunks=chunks)\n", "        )[0],\n", "        \"green\",\n", "    )\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Compute cross-entropy loss and logits"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np\n", "\n", "input_text = \"The quick brown fox jumps over the lazy dog.\"\n", "loss, logits = model.log_likelihood(text=input_text)\n", "\n", "print(f\"Cross-entropy loss: {loss}\")\n", "print(f\"Logits shape: {logits.shape}\")\n", "print(f\"Logits:\\n {logits}\")\n", "\n", "# Make sure the logits and targets align\n", "inv_vocab = {v: k for k, v in model.tokenizer.vocab.items()}\n", "tokens = np.array(model.tokenizer.tokenize(input_text))\n", "pred_tokens = torch.argmax(torch.tensor(logits, dtype=torch.int64), dim=-1)\n", "\n", "print(\"Token alignment:\")\n", "print(tokens[1:])\n", "print(pred_tokens)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### <PERSON>\n", "\n", "An example of aligning the model using one-shot prompting. For best results,\n", "use StarCoder."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from textwrap import dedent\n", "from termcolor import colored\n", "\n", "sample = dedent(\n", "    \"\"\"\n", "'''A turtle library.\n", "\n", "The turtle can move forward, backward, and turn 90 degrees.\n", "'''\n", "\n", "@dataclass\n", "class Turtle:\n", "\"\"\"\n", ")\n", "\n", "result = model.generate(\n", "    ModelInput(prefix=sample),\n", "    GenerationOptions(temperature=0.0, max_generated_tokens=256),\n", ")\n", "\n", "print(colored(\"===== without prompt =====\", \"blue\"))\n", "print(colored(sample, \"blue\"), end=\"\")\n", "print(colored(result, \"green\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["few_shot_prompt = dedent(\n", "    \"\"\"\n", "'''A socket library.'''\n", "\n", "import socket\n", "\n", "@dataclass\n", "class SocketInfo:\n", "    addr: str\n", "    port: int\n", "\n", "class Socket:\n", "    '''A TCP socket.'''\n", "    def __init__(self):\n", "        self._sock = None\n", "\n", "    def connect(self, address, port):\n", "        self._sock = socket.connect(address, port)\n", "\n", "    def send(self, bytes):\n", "        self._sock.send(bytes)\n", "\"\"\"\n", ")\n", "\n", "sample = dedent(\n", "    \"\"\"\n", "'''A turtle library.\n", "\n", "The turtle can move forward, backward, and turn 90 degrees.\n", "'''\n", "\n", "@dataclass\n", "class Turtle:\n", "\"\"\"\n", ")\n", "\n", "prompt = few_shot_prompt + \"\\n\" + sample\n", "result = model.generate(\n", "    ModelInput(prefix=prompt),\n", "    GenerationOptions(temperature=0.0, max_generated_tokens=256),\n", ")\n", "result = result.split(\"\\n\\n\\n\")[0]\n", "print(colored(\"===== with prompt =====\", \"blue\"))\n", "print(colored(sample, \"blue\"), end=\"\")\n", "print(colored(result, \"green\"))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
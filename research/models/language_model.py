"""Describes a tokens-to-logits protocol for language models.

A language model is one that takes tokens in and produces logits out.
Language model implementations operate at the level of modeling frameworks (neox,
fastforward, fastbackward, etc.). For efficency reasons, the interface is specified as
a bidirectional stream, where the caller can send additional tokens to the model (
typically sampled from previously returned logits), and the model can send back logits.

Prompt formatting is not part of the language model protocol, and it is up to the user
to ensure that the tokens provided to the language model are appropriate.
"""

import abc
from collections.abc import Callable, Generator, Sequence
from dataclasses import dataclass

import torch

Tokens = list[int]
LogitsGenerator = Generator[torch.Tensor, Tokens | None, None]
"""A bidirectional generator that yields logits.

Callers can send subsequent tokens to the generator, and receive logits for those
tokens.

Here's a sketch of how to use this generator:

>>> tokens = [1, 2, 3]                          # Some tokens
>>> logits_gen = model.generate_logits(tokens)  # Get a generator
>>> logits = next(logits_gen)                   # `logits` is 3 x vocab_size.
>>> next_token = sample_token_fn(logits[-1])    # Sample a token from the last logits.
>>> next_logits = logits_gen.send([next_token]) # `next_logits` is 1 x vocab_size.
"""

StopCriteria = Callable[[Tokens], bool]
TokenSampler = Callable[[torch.Tensor], int]


def greedy_sample(logits: torch.Tensor) -> int:
    """Sample the maximum probability token from the logits.

    Args:
        logits: The logits to sample from of shape (vocab_size).

    Returns:
        The sampled token.
    """
    return int(torch.argmax(logits).cpu().item())


def temperature_sample(
    logits: torch.Tensor,
    temperature: float = 1.0,
) -> int:
    """Sample a token from the logits proportional to the temperature.

    Args:
        logits: The logits to sample from of shape (vocab_size).

    Returns:
        The sampled token.
    """
    if temperature == 0:
        return greedy_sample(logits)
    else:
        return int(
            torch.multinomial(
                torch.softmax(logits / temperature, dim=-1),
                num_samples=1,
            ).item()
        )


def stop_when_in(stop_tokens: Tokens) -> StopCriteria:
    """A stop criteria that stops when the token is in the stop tokens."""
    return lambda tokens: len(tokens) > 0 and tokens[-1] in stop_tokens


@dataclass
class GenerationOutput:
    """The output of a generation."""

    tokens: Sequence[int]
    """The generated tokens."""
    token_probs: Sequence[float]
    """The probability of each generated token."""


class LanguageModel(abc.ABC):
    """Protocol for language models: "tokens in, logits out"."""

    @property
    @abc.abstractmethod
    def vocab_size(self) -> int:
        """The vocabulary size."""

    @property
    @abc.abstractmethod
    def max_sequence_length(self) -> int:
        """The maximum sequence length supported by the model."""

    @abc.abstractmethod
    def generate_logits(self, tokens: Tokens) -> LogitsGenerator:
        """Generate logits given a token sequence.

        To allow implementations to efficiently cache logits, only a single generator is
        allowed to be open at a given time.

        Args:
            tokens: A list of tokens as input.

        Returns:
            A logits generator that can be used to generate logits.
            - the first value yielded by the co-routine is a tensor of [len(tokens) x
              vocab_size] of logits corresponding to `tokens`.
            - callers can send subsequent tokens (typically sampled from the logits) to
              the co-routine, and will receive logits corresponding to these tokens.

        Raises:
            RuntimeError: if a generator is already open on this model.
        """


def log_likelihood_continuation(
    model: LanguageModel,
    input_tokens: Tokens,
    target_tokens: Tokens,
) -> float:
    """Compute the log-likelihood of the target conditioned on the input.

    Note: if you want to compute the likelihood of a single sequence, you probably
    want to do:
    >>> log_likelihood_continuation(model, tokens[:1], tokens[1:])

    Args:
        input_tokens: The input tokens.
        target_tokens: The target tokens.
    Returns:
        The log-likelihood of the target conditioned on the input.
    """
    assert input_tokens, "Must have at least one input token for scoring."
    (logits,) = list(model.generate_logits(input_tokens + target_tokens))
    return -torch.nn.functional.cross_entropy(
        logits[len(input_tokens) - 1 : -1],
        torch.tensor(target_tokens, dtype=torch.long, device=logits.device),
    ).item()


def generate_tokens(
    model: LanguageModel,
    input_tokens: Tokens,
    max_generated_tokens: int,
    sample_token_fn: TokenSampler = greedy_sample,
    should_stop_fn: StopCriteria = lambda _: False,
    with_token_probs: bool = True,
) -> GenerationOutput:
    """Generate a sequence of tokens following the input tokens.

    Args:
        input_tokens: The input tokens.
        max_generated_tokens: The maximum number of tokens to generate.
        sample_token_fn: Function to use to sample a token from logits.
        should_stop_fn: Function that checks whether the current generation should
            be stopped. Cancellations also fall into this category.
            It is given the whole generated token sequence so far.
        with_token_probs: Whether to return token probabilities.

    Returns:
        The generated tokens and token probs.
    """
    tokens, token_probs = [], []
    # Make sure we acquire any locks before generating tokens.
    logits_gen = model.generate_logits(input_tokens)

    # The first time through we process the context, cache it, and sample a token
    last_logits = next(logits_gen)[-1]
    last_token = sample_token_fn(last_logits)
    tokens.append(last_token)
    if with_token_probs:
        token_probs.append(torch.softmax(last_logits, dim=-1)[last_token].item())

    for _ in range(1, max_generated_tokens):
        if should_stop_fn(tokens):
            break

        # On subsequent iterations we only send the the model the last sampled
        # token -- the rest is cached.
        last_logits = logits_gen.send([last_token])[-1]
        last_token = sample_token_fn(last_logits)
        tokens.append(last_token)
        if with_token_probs:
            token_probs.append(torch.softmax(last_logits, dim=-1)[last_token].item())
    logits_gen.close()
    return GenerationOutput(tokens=tokens, token_probs=token_probs)

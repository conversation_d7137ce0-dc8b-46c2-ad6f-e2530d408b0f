# Research Models

This directory contains code for running inference on trained models, both
our own models and external ones, using a unified interface.

Quick synopsis (works on a node with A100):
```
# Imports definitions without implementations.
from augment.research.models import GenerationOptions, GenerativeLanguageModel

# Import specific model implementations.
from augment.research.models import get_model
from augment.research.models import starcoder_models

# Import all model implementations.
from augment.research.models.all_models import get_model

# Create the model object by class or name.
model = starcoder_models.StarCoderBase()
model = get_model("starcoderbase")

# Load the model -- takes about 30 seconds
model.load()

# Generate some text
prompt = "def hello_world():"
generated_text = model.generate(prompt)
print(prompt + generated_text)
```

The library supports several models out of the box, including the CodeGen models,
StarCoder, and our own fine-tuned models (e.g. Indiana). Models trained with
GPT NeoX can be loaded directly, without writing additional code.

The library supports the following use cases without writing additional code:

* Load and run inference on models (generation and log-prob scoring) in a notebook or script (see examples at `notebooks/run_models.ipynb`).
* Retrieval-augmented generation (see examples at `notebooks/run_models_with_dense_retrieval.ipynb`).
* Early stopping (see examples at `notebooks/run_stopper.ipynb`).
* Easily adapt new models, either internal using GPT NeoX, or external using HuggingFace or other frameworks.
* Serve models on your own instance so they can be used from our VSCode extension. See details at `augment/research/model_server`.

## High-level Design

The library includes the following main interfaces and classes. This design is
work in progress and is subject to change -- please refer to the code for the
latest design details.

Language modeling:

* `GenerativeLanguageModel`: An interface that represents a model that supports text generation (`generate()`) and log-prob scoring (`log_likelihood()`). Implementations are responsible for prompt preparation, tokenization/detokenization, and fill-in-the-middle (if supported).
* `GPTNeoXModel`: A concrete implementation of `GenerativeLanguageModel` that supports loading a GPT NeoX model and running inference on it. Prefix-based generation is supported, but fill-in-the-middle is not because the FIM prompt is model-specific. This class can be instantiated by doing `model = GPTNeoXModel("/path/to/checkpoint")`, where the path contains the checkpoint and a `config.yml` file defining the model -- the usual output of a GPT NeoX training run.
* `CodeGen_350M_Multi`, `CodeGen_2B_Multi`, `CodeGen_2B_FIM`, ...: Concrete implementations of `GenerativeLanguageModel` that load a few common GPT NeoX models. These mostly rely on `GPTNeoXModel`, but they contain the correct path to the checkpoint so that one does not have to go hunt for checkpoint paths. For some models, especially FIM-enabled ones, they contain the custom FIM prompting code.
* `StarCoderBase`: A concrete implementation of `GenerativeLanguageModel` for loading StarCoder. This model uses the HuggingFace library rather than GPT NeoX.

Retrieval:

* `Document` and `Chunk`: A `Document` is a contiguous piece of text, typically a single source file. A `Chunk` is the basic unit of retrieval, representing a subset of a `Document`. A `Chunk` can be for example a line span within a document, or a logical unit (e.g. a function or class) within a document.
* `RetrievalDatabase`: A concrete class that maintains a database of `Document`s and `Chunk`s to do retrieval on. The actual retrieval strategy is delegated to a `RetrievalDatabaseScorer`.
* `RetrievalDatabaseScorer`: The interface that supports looking up `Chunk`s based on a given query.
* `GoodEnoughBM25Scorer`, `DenseRetrievalScorer`: Two implementations of `RetrievalDatabaseScorer` that support retrieval using BM25 and a trained dense retrieval.

For end-to-end examples of retrieval-augmented generation, see `modeling_notebook.ipynb`.

### Best Practice to Contribute New Models
- If possible, please reuse `GPTNeoXModel` or `HuggingFaceModel` classes to build the new models.
- Decorate the model class with `@register_model(MODEL_NAME_ABBREVIATION))` to regist the new model.
- Update `tests/test_all_models.py` to make sure the total number of registed models are expected.

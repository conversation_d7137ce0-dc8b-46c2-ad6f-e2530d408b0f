"""Tests for research.models.language_models.fastforward."""

import pytest
import torch

from base.fastforward.starcoder.sample_data import load_sample_data
from base.tokenizers import StarCoder2Tokenizer
from research.core.constants import AUGMENT_EFS_ROOT
from research.models.language_model import (
    LanguageModel,
    generate_tokens,
    log_likelihood_continuation,
    stop_when_in,
)


@pytest.fixture
def model() -> LanguageModel:
    # NOTE(arun): Importing here to avoid a weird interaction between pytest --forked
    # and CUDA.
    from research.models.language_models.fastforward import (
        create_from_starcoder2_checkpoint,
    )

    return create_from_starcoder2_checkpoint(
        # Using `AUGMENT_EFS_ROOT` here to make sure we use the right CI mount.
        AUGMENT_EFS_ROOT / "checkpoints/starcoder2/fastforward/starcoder2-100m",
        checkpoint_sha256="0b569e9e819001145de6eb2276f1f5b12946d8dbf16a63b030a14438e2d9f420",
    )


@pytest.fixture
def sample_data():
    """Load sample data for testing.

    Same as in base.fastforward.starcoder.fwd_starcoder2_test.
    """
    return load_sample_data()


def test_generate_logits(model: LanguageModel):
    """Test that the model generates the correct outputs."""
    logits_gen = model.generate_logits([1, 2, 3])
    logits = next(logits_gen)
    assert logits.shape == (3, model.vocab_size)
    next_logits = logits_gen.send([4])
    assert next_logits.shape == (1, model.vocab_size)
    assert next_logits.device == logits.device


def test_log_likelihood_continuation(model: LanguageModel):
    """Test that the model generates the correct outputs."""
    tokenizer = StarCoder2Tokenizer()
    prompt = tokenizer.tokenize_safe("def hello_")
    target = tokenizer.tokenize_safe("world():")

    ppl = log_likelihood_continuation(model, prompt, target)
    torch.testing.assert_close(
        torch.tensor(ppl), torch.tensor(-9.3125), atol=1e-2, rtol=1e-2
    )


@pytest.mark.parametrize("with_token_probs", [True, False])
def test_generate_tokens(
    model: LanguageModel, sample_data: dict, with_token_probs: bool
):
    """Test that the model generates the correct outputs.

    Same as in base.fastforward.starcoder.fwd_starcoder2_test.
    """
    prompt = sample_data["inputs"]
    target = sample_data["outputs"]

    output = generate_tokens(
        model, prompt, len(target), with_token_probs=with_token_probs
    )
    assert output.tokens == target
    if with_token_probs:
        assert len(output.token_probs) == len(target)
        assert all(x > 0 for x in output.token_probs)
    else:
        assert output.token_probs == []


def test_generate_tokens_early_stopping(model: LanguageModel, sample_data: dict):
    """Test that the model generates the correct outputs.

    Same as in base.fastforward.starcoder.fwd_starcoder2_test.
    """
    prompt = sample_data["inputs"]
    target = sample_data["outputs"]

    output = generate_tokens(
        model,
        prompt,
        len(target),
        # Stop when we hit the 3rd token.
        should_stop_fn=stop_when_in([target[2]]),
        with_token_probs=False,
    )
    assert output.tokens == target[:3]

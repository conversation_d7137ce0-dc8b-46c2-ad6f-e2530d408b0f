"""Definition of the data format for chat in research."""

import dataclasses
import typing

from base.third_party_clients.third_party_model_client import ToolDefinition
from base.prompt_format_chat.prompt_formatter import ChatPromptInput


@dataclasses.dataclass(frozen=True)
class ResearchChatPromptInput(ChatPromptInput):
    """The input to the chat model."""

    label: typing.Optional[str] = None
    """The label for the input. If None, there is no target."""

    model_reply: typing.Optional[str] = None
    """The model reply. If None, there is no target. This is different from having an empty string as target."""

    doc_ids: typing.Optional[typing.Collection[str]] = None
    """If set, the doc ids to filter on."""

    target: typing.Optional[str] = None
    """If set, the ground truth target."""

    tool_definitions: list[ToolDefinition] = dataclasses.field(default_factory=list)
    """If set, the tool definitions to include in the prompt."""

    extra: dict[str, typing.Any] = dataclasses.field(default_factory=dict)
    """Extra fields for easy extensibility."""

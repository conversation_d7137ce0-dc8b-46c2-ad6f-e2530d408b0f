"""Unit tests for core/ui_sugar.py."""

from __future__ import annotations

import functools
from typing import cast

from research.core.ui_sugar import UISugar


def test_different_funcs_to_get_keys():
    """Test get_.._keys."""

    class A(UISugar):
        b: ...

        def __init__(self, x):
            if x is not None:
                self.b = x

    x1 = A(None)
    x2 = A(1)
    assert not x1.ui_get_cls_keys()
    assert not x1.ui_get_keys()
    assert str(x1) == "A()"
    assert not x2.ui_get_cls_keys()
    assert x2.ui_get_keys() == ["b"]
    assert str(x2) == "A(b = 1)"


def test_circular_reference():
    """Test whether the repr works with circular reference."""

    class A(UISugar):
        b: ...
        c: ...

        def __init__(self, x):
            self.b = x

    class B(UISugar):
        a: ...

        def __init__(self, x):
            self.a = x

    a = A(None)
    b = B(a)
    a.b = b
    assert str(a) == r"""A(b = B(a = A(...)))"""

    a.c = 3
    assert (
        str(a)
        == r"""
A(b = B(a = A(...)),
  c = 3)""".lstrip()
    )


class UA(UISugar):
    """A mock class for the unit test."""

    b: ...

    def __init__(self, x):
        if x is not None:
            self.b = x


class UB(UISugar):
    """A mock class for the unit test."""

    x: int = 1

    y = UISugar()

    values: ...


class UC(UISugar):
    """A mock class without defining the class-level attributes for the unit test."""

    def __init__(self, x: int = 4, y: str = "hello"):
        self.x = x
        self.y = y

    @functools.cached_property
    def value(self):
        return 1


def _check_serialize_and_deserialize(obj: UISugar, expected_length: int | None = None):
    json_str = obj.ui_to_json()
    if expected_length is not None:
        assert len(json_str.split("\n")) == expected_length
    deserialize_obj = UISugar.ui_from_json(json_str)
    assert str(obj) == str(deserialize_obj)
    return deserialize_obj


def test_serialization():
    """Test the ui_to_json function."""

    # Test an empty object
    _check_serialize_and_deserialize(UA(None), 4)

    # Test a hierarchy one
    x = UB()
    x.y.v = 1  # type: ignore
    _check_serialize_and_deserialize(x, 10)

    # Test the list case
    x = UB()
    x._values = [UA(1), UA(2), UA(3)]  # type: ignore
    _check_serialize_and_deserialize(x)

    # Test the dict case
    x = UB()
    x.values = {"a": UA(1), "b": UA(2), "c": UA(3)}
    _check_serialize_and_deserialize(x)

    # Test the circular reference
    x.values["c"] = x  # type: ignore
    json_str = x.ui_to_json()
    deserialize_x = UISugar.ui_from_json(json_str)
    deserialize_x = cast(UB, deserialize_x)
    assert x.x == deserialize_x.x
    assert str(x.y) == str(deserialize_x.y)
    assert str(x.values["a"]) == str(deserialize_x.values["a"])
    assert str(x.values["b"]) == str(deserialize_x.values["b"])

    # Test the __init__ function
    x = UC()
    assert x.value == 1
    x.value = 10  # reset its cached_property
    deserialize_x = _check_serialize_and_deserialize(x)
    if not isinstance(deserialize_x, UC):
        raise TypeError(f"{type(deserialize_x)}")
    assert x.x == deserialize_x.x
    assert x.y == deserialize_x.y
    assert x.value == deserialize_x.value
    deserialize_x.value = 25
    x.value = 25
    assert x.value == deserialize_x.value

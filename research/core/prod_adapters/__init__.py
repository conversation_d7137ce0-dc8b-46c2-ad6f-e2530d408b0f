"""The production adapters."""

from research.core.prod_adapters.completion_formatter_wrapper import (
    ProdDocumentPromptFormatterAdapter,
    ProdPromptFormatterAdapter,
    ProdRetrieverPromptFormatterAdapter,
)
from research.core.prod_adapters.edit_formatter_wrapper import (
    ProdEditPromptFormatterAdapter,
)

__all__ = [
    "ProdDocumentPromptFormatterAdapter",
    "ProdRetrieverPromptFormatterAdapter",
    "ProdEditPromptFormatterAdapter",
    "ProdPromptFormatterAdapter",
]

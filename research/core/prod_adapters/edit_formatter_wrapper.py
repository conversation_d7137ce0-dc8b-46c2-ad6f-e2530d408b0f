"""Wraps the code edit production prompt formatter into the research interface so that they can be used in AbstractSystem."""

import abc
import logging
import typing

from base.prompt_format_edit import prompt_formatter
from base.tokenizers import tokenizer as prod_tokenizer
from research.core.abstract_prompt_formatter import Abs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>atter
from research.core.model_input import ModelInput

logger = logging.getLogger(__name__)


def _model_input_to_edit_prompt_input(
    model_input: ModelInput,
) -> prompt_formatter.EditPromptInput:
    """Convert a ModelInput to a EditPromptInput to be read by the prompt formatter."""
    retrieved_chunks = []
    for chunk in model_input.retrieved_chunks:
        path = "" if chunk.parent_doc.path is None else chunk.parent_doc.path
        retrieved_chunks.append(
            prompt_formatter.PromptChunk(
                text=chunk.text,
                path=path,
                origin="",
                char_start=chunk.char_offset,
                char_end=chunk.char_offset + chunk.length,
            )
        )
    return prompt_formatter.EditPromptInput(
        path=model_input.path,
        prefix=model_input.prefix,
        selected_code=model_input.extra["selected_code"],
        suffix=model_input.suffix,
        instruction=model_input.extra["instruction"],
        prefix_begin=model_input.extra.get("prefix_begin", 0),
        suffix_end=model_input.extra.get("suffix_end", 0),
        retrieved_chunks=retrieved_chunks,
    )


class ProdEditPromptFormatterAdapter(AbstractPromptFormatter):
    """The adapter for the code edit prompt formatter in prod.

    This can be used to run hydra against the production prompt formatters.
    The new hyperparameters are passed to the production prompt formatter as part of the token apportionment config, thus
    they need to be consistent with base/prompt_format_edit/prompt_formatter.py.
    """

    prod_prompt_formatter_factory: typing.Callable[
        [prod_tokenizer.Tokenizer, prompt_formatter.EditTokenApportionment],
        prompt_formatter.EditPromptFormatter,
    ]

    _prod_prompt_formatter: typing.Optional[prompt_formatter.EditPromptFormatter] = None
    """The production prompt formatter, created by prod_prompt_formatter_factory(...)."""

    prefix_len: int = 1024
    """The number of tokens of the prefix to include."""

    suffix_len: int = 1024
    """The number of tokens of the suffix to include."""

    max_context_len: int = 8192
    """The maximum number of tokens in the prompt."""

    # --------------------------------------------------------------------
    # TODO (c-flaherty, AU-4764): Deprecate by 2024-04-05

    path_len: int = 256
    """The number of tokens of the path to include."""

    instruction_len: int = 100
    """The number of tokens of the instruction to include."""

    selected_code_len: int = 1024
    """The number of tokens of the selected_code to include."""

    # --------------------------------------------------------------------

    def _create_config(self):
        return prompt_formatter.EditTokenApportionment(
            prefix_len=self.prefix_len,
            suffix_len=self.suffix_len,
            max_context_len=self.max_context_len,
            # TODO (c-flaherty, AU-4764): Deprecate by 2024-04-05
            dynamic_resizing=False,
            path_len=self.path_len,
            instruction_len=self.instruction_len,
            selected_code_len=self.selected_code_len,
            max_prompt_len=self.max_prompt_tokens,
        )

    @abc.abstractmethod
    def create_default_tokenizer(self) -> prod_tokenizer.Tokenizer:
        """A function to create the default tokenizer."""

    def prepare_prompt(self, model_input: ModelInput) -> tuple[list[int], dict]:
        """Create a simple prompt for the code edit model.

        Args:
            input: an instance of ModelInput class, containing all raw input.

        Returns:
            A prompt of length at most self.max_prompt_tokens, in tokens.
            A metadata dict containing:
                num_prefix_chars_post_truncation and num_suffix_chars_post_truncation,
                which are set to 0 to deactivate overlap filtering.
        """
        if self._prod_prompt_formatter is None:
            if self.prod_prompt_formatter_factory is None:
                raise ValueError("The prod_prompt_formatter_factory is missing!")
            self._prod_prompt_formatter = self.prod_prompt_formatter_factory(
                self.tokenizer,
                self._create_config(),
            )

        prompt_input = _model_input_to_edit_prompt_input(model_input)
        result = self._prod_prompt_formatter.format_prompt(prompt_input)
        metadata = {
            "num_prefix_chars_post_truncation": 0,
            "num_suffix_chars_post_truncation": 0,
        }
        return result.tokens, metadata

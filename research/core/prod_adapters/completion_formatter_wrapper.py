"""Wraps the code completion production prompt formatter into the research interface so that they can be used in AbstractSystem."""

import logging
from typing import Callable

from base.tokenizers import Tokenizer
from base.prompt_format_completion import prompt_formatter, token_apportionment
from base.prompt_format_retrieve.prompt_formatter import Retriever<PERSON><PERSON><PERSON><PERSON><PERSON>atter
from base.tokenizers import tokenizer as prod_tokenizer
from research.core.abstract_prompt_formatter import Abstract<PERSON>romptFormatter
from research.core.model_input import ModelInput

logger = logging.getLogger(__name__)


def _model_input_to_completion_prompt_input(
    model_input: ModelInput,
) -> prompt_formatter.PromptInput:
    """Convert a ModelInput to a PromptInput to be read by the prompt formatter."""
    retrieved_chunks = []
    for chunk in model_input.retrieved_chunks:
        if chunk.parent_doc.path is None:
            raise ValueError(
                f"Chunk with path=None cannot be converted to PromptChunk: {chunk=}"
            )
        retrieved_chunks.append(
            prompt_formatter.PromptChunk(text=chunk.text, path=chunk.parent_doc.path)
        )
    prefix_begin = (
        model_input.cursor_position - len(model_input.prefix)
        if model_input.cursor_position is not None
        else 0
    )
    return prompt_formatter.PromptInput(
        prefix=model_input.prefix,
        suffix=model_input.suffix,
        prefix_begin=prefix_begin,
        path=model_input.path,
        lang=None,  # ModelInput does not have a lang field at the moment
        retrieved_chunks=retrieved_chunks,
    )


class ProdPromptFormatterAdapter(AbstractPromptFormatter):
    """The adapter for the prompt formatter in prod.

    This can be used to run hydra against the production prompt formatters. See
    the class PromptFormatterRogueProd for an example.
    """

    max_output_token_count: int = -1
    """The maximal number of tokens that can be generated."""
    max_content_len: int = 1_000_000
    """The maximal size of the prompt (after any potential generation of tokens)"""
    input_fraction: float = 1.0
    """The fraction of the prompt for the input (prefix/path/suffix) as opposed to retrieved documents"""
    prefix_fraction: float = 0.5
    """A hint on the fraction of the input that should be apportioned to the prefix."""
    max_path_tokens: int = 25
    """The maximal number of tokens that should be included for any path"""

    def __init__(
        self,
        special_tokens_factory: Callable[..., prod_tokenizer.SpecialTokens],
        prod_prompt_formatter_factory: Callable[
            [token_apportionment.TokenApportionmentConfig, prod_tokenizer.Tokenizer],
            prompt_formatter.CompletionPromptFormatter,
        ],
        research_tokenizer_factory: Callable[[], Tokenizer],
        *,
        max_output_token_count: int = -1,
        max_content_len: int = 1_000_000,
        input_fraction: float = 1.0,
        prefix_fraction: float = 0.5,
        max_path_tokens: int = 25,
    ):
        """Create a new instance of the prompt formatter.

        Args:
            special_tokens_factory: a factory function that returns a SpecialTokens object.
            prod_prompt_formatter_factory: a factory function that returns a PromptFormatter object.
            research_tokenizer_factory: a factory function that returns a research tokenizer, which
                is to be wrapped to look like a prod tokenizer and used to tokenize the input.
            max_output_token_count: the maximal number of tokens that can be generated.
            max_content_len: the maximal size of the prompt (after any potential generation of tokens).
            input_fraction: the fraction of the prompt for the input (prefix/path/suffix) as opposed to
                retrieved documents.
            prefix_fraction: a hint on the fraction of the input that should be apportioned to the prefix.
            max_path_tokens: the maximal number of tokens that should be included for any path.
        """
        # Ideally max_output_token_count would be given in `prepare_prompt`.
        super().__init__(
            max_output_token_count=max_output_token_count,
            max_content_len=max_content_len,
            input_fraction=input_fraction,
            prefix_fraction=prefix_fraction,
            max_path_tokens=max_path_tokens,
        )
        self._special_tokens_factory = special_tokens_factory
        self._prod_prompt_formatter_factory = prod_prompt_formatter_factory
        self._prod_prompt_formatter = None
        self._research_tokenizer_factory = research_tokenizer_factory

        # Trigger the setter to create the prompt formatter.
        self.tokenizer = self.create_default_tokenizer()

    def _create_config(self):
        return token_apportionment.TokenApportionmentConfig(
            max_content_len=self.max_content_len,
            input_fraction=self.input_fraction,
            prefix_fraction=self.prefix_fraction,
            max_path_tokens=self.max_path_tokens,
        )

    def create_default_tokenizer(self) -> Tokenizer:
        return self._research_tokenizer_factory()

    def prepare_prompt(self, model_input: ModelInput) -> tuple[list[int], dict]:
        """Create a simple prompt for the code completion model.

        Args:
            input: an instance of ModelInput class, containing all raw input.

        Returns:
            A prompt of length at most self.max_prompt_tokens, in tokens.
            A metadata dict containing:
                num_prefix_chars_post_truncation and num_suffix_chars_post_truncation,
                which are set to 0 to deactivate overlap filtering.
        """
        if self._prod_prompt_formatter is None:
            self._prod_prompt_formatter = self._prod_prompt_formatter_factory(
                self._create_config(),
                self.tokenizer,
            )

        prompt_input = _model_input_to_completion_prompt_input(model_input)
        result = self._prod_prompt_formatter.format_prompt(
            prompt_input, max_output_token_count=self.max_output_token_count
        ).tokens()
        metadata = {
            "num_prefix_chars_post_truncation": 0,
            "num_suffix_chars_post_truncation": 0,
        }
        return result, metadata


class ProdRetrieverPromptFormatterAdapter(ProdPromptFormatterAdapter):
    """The adapter for the retriever prompt formatter in prod.

    This can be used to run hydra against the production retriever prompt formatters. See
    the class PromptFormatterRogueProd for an example.
    """

    def __init__(
        self,
        special_tokens_factory: Callable[..., prod_tokenizer.SpecialTokens],
        prod_prompt_formatter_factory: Callable[
            [token_apportionment.TokenApportionmentConfig, prod_tokenizer.Tokenizer],
            RetrieverPromptFormatter,
        ],
        research_tokenizer_factory: Callable[[], Tokenizer],
        *,
        max_output_token_count: int = -1,
        max_content_len: int = 1_000_000,
        input_fraction: float = 1.0,
        prefix_fraction: float = 0.5,
        max_path_tokens: int = 25,
    ):
        """Create a new instance of the prompt formatter.

        Args:
            special_tokens_factory: a factory function that returns a SpecialTokens object.
            prod_prompt_formatter_factory: a factory function that returns a PromptFormatter object.
            research_tokenizer_factory: a factory function that returns a research tokenizer, which
                is to be wrapped to look like a prod tokenizer and used to tokenize the input.
            max_output_token_count: the maximal number of tokens that can be generated.
            max_content_len: the maximal size of the prompt (after any potential generation of tokens).
            input_fraction: the fraction of the prompt for the input (prefix/path/suffix) as opposed to
                retrieved documents.
            prefix_fraction: a hint on the fraction of the input that should be apportioned to the prefix.
            max_path_tokens: the maximal number of tokens that should be included for any path.
        """
        # Ideally max_output_token_count would be given in `prepare_prompt`.
        super(ProdPromptFormatterAdapter, self).__init__(
            max_output_token_count=max_output_token_count,
            max_content_len=max_content_len,
            input_fraction=input_fraction,
            prefix_fraction=prefix_fraction,
            max_path_tokens=max_path_tokens,
        )
        self._special_tokens_factory = special_tokens_factory
        self._prod_prompt_formatter_factory = prod_prompt_formatter_factory
        self._prod_prompt_formatter = None
        self._research_tokenizer_factory = research_tokenizer_factory

        # Trigger the setter to create the prompt formatter.
        self.tokenizer = self.create_default_tokenizer()


class ProdDocumentPromptFormatterAdapter(ProdRetrieverPromptFormatterAdapter):
    """The adapter for the document prompt formatter in prod.

    This adapter resolves a discrepancy in how the document prompt formatter input
    is interpretted in research vs. production.

    For more details, see: AU-802
    """

    def prepare_prompt(self, model_input: ModelInput) -> tuple[list[int], dict]:
        """Adapt the model input for the prod prompt formatter.

        The production prompt formatter and the research prompt formatter expect
        different representations of the chunk text:

        * production prompt adapter expects the chunk to be in the input prefix
        * research prompt formatter expects the chunk to be represented as a single
          retrieved chunk
        """
        path = model_input.retrieved_chunks[0].parent_doc.path
        model_input = ModelInput(
            prefix=model_input.retrieved_chunks[0].text,
            path=path if path is not None else "",
        )
        return super().prepare_prompt(model_input)

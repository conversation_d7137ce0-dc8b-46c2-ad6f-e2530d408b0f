"""Basic types shared across research components."""

from base.datasets import completion
from base.prompt_format.recency_info import (
    RecencyInfo,
    TabSwitchEvent,
    GitDiffFileInfo,
    ReplacementText,
)


def convert_from_datasets_recency_info(recency_info: completion.RecencyInfo):
    return RecencyInfo(
        tab_switch_events=[
            TabSwitchEvent(
                path=event.path,
                file_blob_name=event.file_blob_name,
            )
            for event in recency_info.tab_switch_events
        ],
        git_diff_info=[
            GitDiffFileInfo(
                content_blob_name=info.content_blob_name,
                file_blob_name=info.file_blob_name,
            )
            for info in recency_info.git_diff_info
        ],
        recent_changes=[
            ReplacementText(
                blob_name=change.blob_name,
                path=change.path,
                char_start=change.crange.start,
                char_end=change.crange.stop,
                replacement_text=change.replacement_text,
                present_in_blob=change.present_in_blob,
            )
            for change in recency_info.recent_changes
        ],
    )

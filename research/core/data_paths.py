import typing
from pathlib import Path

from research.core.constants import (
    AUGMENT_CHECKPOINTS_ROOT,
    AUGMENT_EFS_ROOT,
    AUGMENT_ROOT,
)

PathLikeType = typing.Union[Path, str]


def canonicalize_path(
    path: PathLikeType,
    base_path: Path = Path("/mnt/efs/augment"),
    new_path: Path = AUGMENT_EFS_ROOT,
) -> PathLikeType:
    """Canonicalize the given path.

    This won't change the type of the path passed in.
    """
    canonical = None
    try:
        if Path(path).is_relative_to(base_path):
            canonical = new_path / Path(path).relative_to(base_path)
        else:
            canonical = new_path / Path(path)
        if canonical.exists():
            if isinstance(path, str):
                return str(canonical)
            return canonical
    except:  # noqa: E722
        print(f"Failed to canonicalize {path}; checked whether {str(canonical)} exists")
        # Not a path relative to base, move on
        pass
    return path


def absolute_path(
    maybe_relative: PathLikeType,
    search_paths: typing.Iterable[PathLikeType] = (
        AUGMENT_ROOT,
        AUGMENT_EFS_ROOT,
        AUGMENT_CHECKPOINTS_ROOT,
    ),
) -> Path:
    """Return the full path of the given path, searching the given paths."""
    if isinstance(maybe_relative, str):
        maybe_relative = Path(maybe_relative)
    if maybe_relative.is_absolute():
        return maybe_relative

    for search_path in search_paths:
        full_path = search_path / maybe_relative
        if full_path.exists():
            return full_path

    return maybe_relative

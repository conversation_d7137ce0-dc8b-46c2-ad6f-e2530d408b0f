"""A minimal class for representing character-level (or line-level) diffs.

See `research/notebooks/str_diff_tutorial.ipynb` for example usages.
"""

import colorama
from typing import Iterable
from typing_extensions import Literal, assert_never
import fast_diff_match_patch as dmp

from base.diff_utils.str_diff import (
    AddedSpan,
    DeletedSpan,
    DiffSpan,
    ModSpan,
    NoopSpan,
    StrDiff,
    cleanup_diff_spans,
    line_diff,
    precise_char_diff,
    precise_line_diff,
)

__reexported__ = [
    AddedSpan,
    DeletedSpan,
    DiffSpan,
    ModSpan,
    NoopSpan,
    StrDiff,
    line_diff,
    precise_line_diff,
    precise_char_diff,
]

DiffAlgName = Literal[
    "dmp",  # to be deprecated
    "linediff",
    "line_dmp",  # to be deprecated
    "precise_linediff",
    "precise_chardiff",
]


def build_str_diff(
    before: str, after: str, algorithm: DiffAlgName = "line_dmp"
) -> StrDiff:
    """Build a diff between two strings using the specified algorithm."""
    if algorithm == "precise_chardiff":
        return precise_char_diff(before, after)
    elif algorithm == "precise_linediff":
        return precise_line_diff(before, after)
    elif algorithm == "linediff":
        return line_diff(before, after)
    if algorithm == "line_dmp":
        return line_dmp_diff(before, after)
    elif algorithm == "dmp":
        return dmp_diff(before, after)
    else:
        assert_never(algorithm)


def line_dmp_diff(before: str, after: str) -> StrDiff:
    """Take a character diff by combining line diff and dmp."""
    spans = list[DiffSpan]()
    for span in precise_line_diff(before, after).spans:
        if not isinstance(span, ModSpan):
            spans.append(span)
            continue
        inner_diff = dmp_diff(span.before, span.after).spans
        spans.extend(inner_diff)
    spans = cleanup_diff_spans(spans)
    return StrDiff(tuple(spans))


def dmp_diff(before: str, after: str) -> StrDiff:
    """Take a character diff using diff-match-patch."""

    i = 0  # current offset in the old string
    j = 0  # current offset in the new string

    def build_span(entry: tuple[str, int]):
        """Build a DiffOp from an entry returned by dmp.diff."""
        nonlocal i, j
        op_type, count = entry
        if op_type == "=":
            unchanged = before[i : i + count]
            i += count
            j += count
            return NoopSpan(unchanged)
        elif op_type == "+":
            added = after[j : j + count]
            j += count
            return AddedSpan(added)
        elif op_type == "-":
            deleted = before[i : i + count]
            i += count
            return DeletedSpan(deleted)
        else:
            raise ValueError(f"Unknown op type: {op_type}")

    def _build_mod_spans(spans: Iterable[DiffSpan]) -> list[DiffSpan]:
        """Turn adjacent deletions and additions into modifications."""
        spans = list(spans)
        i = 0  # the index to swap for
        for j in range(1, len(spans)):
            op1 = spans[i]
            op2 = spans[j]
            if isinstance(op1, DeletedSpan) and isinstance(op2, AddedSpan):
                spans[i] = ModSpan(op1.deleted, op2.inserted)
            elif isinstance(op1, AddedSpan) and isinstance(op2, DeletedSpan):
                spans[i] = ModSpan(op2.deleted, op1.inserted)
            else:
                i += 1
                spans[i] = op2
        return spans[: i + 1]

    spans = tuple(build_span(entry) for entry in dmp.diff(before, after))
    spans = _build_mod_spans(spans)
    spans = cleanup_diff_spans(spans)
    result = StrDiff(tuple(spans))
    if i != len(before) or j != len(after):
        raise AssertionError(
            f"dmp diff failed: {i=}, {j=}, {before=}, {after=}, {spans=}"
        )
    return result


def print_diff(diff: StrDiff) -> None:
    """Pretty-print the diff to terminal.

    Blue text indicates additions, and red text indicates deletions.
    """

    def highlight_spaces(text: str):
        return text.replace(" ", "·").replace("\n", "↩\n")

    def add_style(text: str):
        text = highlight_spaces(text)
        text = (
            f"{colorama.Style.BRIGHT}{colorama.Fore.BLUE}"
            f"{text}{colorama.Style.RESET_ALL}"
        )
        return text

    def del_style(text: str):
        text = highlight_spaces(text)
        text = (
            f"{colorama.Style.DIM}{colorama.Fore.RED}"
            f"{text}{colorama.Style.RESET_ALL}"
        )
        return text

    def normal_style(text: str):
        text = highlight_spaces(text)
        return text

    def mod_style(old: str, new: str):
        # make sure the deleted parts is shown differently in a mod span.
        deleted = highlight_spaces(old)
        deleted = (
            f"{colorama.Style.DIM}{colorama.Fore.YELLOW}"
            f"{deleted}{colorama.Style.RESET_ALL}"
        )
        added = highlight_spaces(new)
        added = (
            f"{colorama.Style.BRIGHT}{colorama.Fore.CYAN}"
            f"{added}{colorama.Style.RESET_ALL}"
        )
        return f"{deleted}{added}"

    for op in diff.spans:
        if isinstance(op, AddedSpan):
            print(add_style(op.inserted), end="")
        elif isinstance(op, DeletedSpan):
            print(del_style(op.deleted), end="")
        elif isinstance(op, ModSpan):
            print(mod_style(op.before, op.after), end="")
        elif isinstance(op, NoopSpan):
            print(normal_style(op.text), end="")
        else:
            assert_never(op)

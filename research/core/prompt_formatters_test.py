"""Test different prompt formatters under core.

pytest research/core/prompt_formatters_test.py
"""

import dataclasses
import typing
import unittest
from textwrap import dedent

from parameterized import parameterized

import base.tokenizers as prod_tokenizers

# pylint: disable-next=unused-import
import research.core.llama_prompt_formatters  # noqa
from base.tokenizers import (
    DeepSeekCoderBaseTokenizer,
    Llama3BaseTokenizer,
    StarCoder2Tokenizer,
    TiktokenStarCoderTokenizer,
)
from base.tokenizers.tokenizer import Tokenizer
from research.core.all_prompt_formatters import (
    ChunkLayout,
    PromptFormatterEnder,
    PromptFormatterRogue,
    PromptFormatterStarCoder,
    get_prompt_formatter,
)
from research.core.model_input import ModelInput
from research.core.prompt_formatters import (
    PromptFormatterRogueProd,
    PromptFormatterRogueSLCache,
)
from research.core.types import Chunk, Document
from research.core.ui_sugar import UISugar
from research.retrieval.utils import Span

# Build some test data examples, which are used in many different unit tests


def get_example_01():
    doc = Document(
        id="doc1", text="This is a fake doc.", path="example_doc.py", meta={}
    )
    return ModelInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        retrieved_chunks=[
            Chunk(
                id="chunk1",
                text="# You can aggregate\n# with a pooling function.\n",
                parent_doc=doc,
                char_offset=93,
                length=49,
                line_offset=1,
                length_in_lines=2,
                header="",
                meta={},
            ),
            Chunk(
                id="chunk2",
                text="# You can aggregate\n# with a maxing\n# function.\n",
                parent_doc=doc,
                char_offset=54,
                length=51,
                line_offset=5,
                length_in_lines=3,
                header="test/header",
                meta={},
            ),
        ],
    )


def get_example_02():
    doc = Document(
        id="doc1", text="This is a fake doc.", path="example_doc.py", meta={}
    )
    return ModelInput(
        path="src/example.py",
        prefix="c" * 2048,  # 1024 tokens
        suffix="b" * 2048,  # 1024 tokens
        retrieved_chunks=[
            Chunk(
                id=f"chunk{i}",
                text="z" * 64,  # 32 tokens
                parent_doc=doc,
                char_offset=93,
                length=32,
                line_offset=1,
                length_in_lines=1,
                meta={},
            )
            for i in range(100)  # so there are 3,200 tokens across all chunks in corpus
        ],
    )


def get_example_03() -> ModelInput:
    doc = Document(
        id="doc1", text="This is a fake doc.", path="example_doc.py", meta={}
    )
    context_doc = Document(
        id="ctx_doc", text="This is a fake doc.", path="hello.py", meta={}
    )
    return ModelInput(
        path="hello.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        retrieved_chunks=[
            Chunk(
                id="overlap_chunk1",
                text="OVERLAP_TEXT",
                parent_doc=context_doc,
                char_offset=0,
                length=7,
                line_offset=0,
                length_in_lines=1,
                meta={},
            ),
            Chunk(
                id="overlap_chunk2",
                text="overlap_text",
                parent_doc=context_doc,
                char_offset=39,
                length=6,
                line_offset=1,
                length_in_lines=1,
                meta={},
            ),
            Chunk(
                id="chunk1",
                text="# You can aggregate\n# with a pooling function.\n",
                parent_doc=doc,
                char_offset=93,
                length=49,
                line_offset=1,
                length_in_lines=2,
                meta={},
            ),
            Chunk(
                id="chunk2",
                text="# You can aggregate\n# with a maxing\n# function.\n",
                parent_doc=doc,
                char_offset=54,
                length=51,
                line_offset=5,
                length_in_lines=3,
                meta={},
            ),
        ],
    )


def serialization_check(obj: UISugar, expected_cls):
    # old_type = type(obj)
    obj_json_str = obj.ui_to_json()
    new_obj = UISugar.ui_from_json(obj_json_str)
    # TODO(Xuanyi): something weird happened here during the unit test.
    # both expected_cls and type(new_obj) have the same module name
    # but their id are different, so that caused isinstance failed.
    #
    # if not isinstance(new_obj, expected_cls):
    #     raise TypeError(
    #         f"The type is incorrect: {type(new_obj)} vs. {old_type}"
    #         f"\nexpected_cls.__module__ = {expected_cls.__module__} vs. {type(new_obj).__module__}"
    #         f"\nid(expected_cls) = {id(expected_cls)}"
    #         f"\nid(type(new_obj)) = {id(type(new_obj))}"
    #     )
    return typing.cast(expected_cls, new_obj)


class TestEndersPromptFormatter(unittest.TestCase):
    """Test Enders prompt formatter."""

    def setUp(self):
        self.inputs = get_example_01()
        doc = Document(
            id="doc1", text="This is a fake doc.", path="example_doc.py", meta={}
        )
        self.inputs.extra = {
            "signature_chunks": [
                Chunk(
                    id="sig1",
                    text="""# class1.py\nclass 1 body""",
                    parent_doc=doc,
                    char_offset=0,
                    length=0,
                    line_offset=0,
                    length_in_lines=0,
                    meta={},
                ),
                Chunk(
                    id="sig2",
                    text="""# class2.py\nclass 2 body""",
                    parent_doc=doc,
                    char_offset=0,
                    length=0,
                    line_offset=0,
                    length_in_lines=0,
                    meta={},
                ),
            ],
        }

    def get_prompter(self, tokenizer: Tokenizer | None):
        prompter = PromptFormatterEnder(
            max_prefix_tokens=1000,
            max_suffix_tokens=1000,
            max_signature_tokens=1000,
        )
        if tokenizer is not None:
            prompter.rebind(tokenizer=tokenizer)
        return prompter

    # fmt: off
    @parameterized.expand(
        [
            (None, [5, 1617, 33, 2763, 32, 978, 203, 49155, 49156, 5, 2763, 81, 1427, 32, 978, 49157, 21, 2448, 883, 19089, 203, 21, 623, 312, 1769, 299, 203, 21, 667, 32, 203, 49156, 5, 2763, 81, 1427, 32, 978, 49157, 21, 2448, 883, 19089, 203, 21, 623, 312, 49094, 667, 32, 203, 49161, 203, 21, 443, 36, 32, 978, 203, 823, 225, 36, 3361, 478, 21, 443, 35, 32, 978, 203, 823, 225, 35, 3361, 203, 49162, 1, 589, 19089, 26, 83, 30, 84, 711, 203, 3, 203, 601, 43268, 81, 2024, 203, 2]),
            (TiktokenStarCoderTokenizer(), [5, 1617, 33, 2763, 32, 978, 203, 49155, 49156, 5, 2763, 81, 1427, 32, 978, 49157, 21, 2448, 883, 19089, 203, 21, 623, 312, 1769, 299, 203, 21, 667, 32, 203, 49156, 5, 2763, 81, 1427, 32, 978, 49157, 21, 2448, 883, 19089, 203, 21, 623, 312, 49094, 667, 32, 203, 49161, 203, 21, 443, 36, 32, 978, 203, 823, 225, 36, 3361, 478, 21, 443, 35, 32, 978, 203, 823, 225, 35, 3361, 203, 49162, 1, 589, 19089, 26, 83, 30, 84, 711, 203, 3, 203, 601, 43268, 81, 2024, 203, 2]),
            (StarCoder2Tokenizer(), [6, 1634, 52, 2783, 51, 997, 222, 21, 22, 6, 2783, 100, 1446, 51, 997, 23, 40, 2469, 902, 19081, 222, 40, 642, 331, 1788, 318, 222, 40, 686, 51, 222, 22, 6, 2783, 100, 1446, 51, 997, 23, 40, 2469, 902, 19081, 222, 40, 642, 331, 49099, 686, 51, 222, 49161, 222, 40, 462, 55, 51, 997, 222, 842, 244, 55, 3376, 499, 40, 462, 54, 51, 997, 222, 842, 244, 54, 3376, 222, 49162, 1, 610, 19081, 45, 102, 49, 103, 731, 222, 3, 222, 620, 43364, 100, 2051, 222, 2]),
            (prod_tokenizers.StarCoder2Tokenizer(), [6, 1634, 52, 2783, 51, 997, 222, 21, 22, 6, 2783, 100, 1446, 51, 997, 23, 40, 2469, 902, 19081, 222, 40, 642, 331, 1788, 318, 222, 40, 686, 51, 222, 22, 6, 2783, 100, 1446, 51, 997, 23, 40, 2469, 902, 19081, 222, 40, 642, 331, 49099, 686, 51, 222, 49161, 222, 40, 462, 55, 51, 997, 222, 842, 244, 55, 3376, 499, 40, 462, 54, 51, 997, 222, 842, 244, 54, 3376, 222, 49162, 1, 610, 19081, 45, 102, 49, 103, 731, 222, 3, 222, 620, 43364, 100, 2051, 222, 2]),
            (prod_tokenizers.TiktokenStarCoderTokenizer(), [5, 1617, 33, 2763, 32, 978, 203, 49155, 49156, 5, 2763, 81, 1427, 32, 978, 49157, 21, 2448, 883, 19089, 203, 21, 623, 312, 1769, 299, 203, 21, 667, 32, 203, 49156, 5, 2763, 81, 1427, 32, 978, 49157, 21, 2448, 883, 19089, 203, 21, 623, 312, 49094, 667, 32, 203, 49161, 203, 21, 443, 36, 32, 978, 203, 823, 225, 36, 3361, 478, 21, 443, 35, 32, 978, 203, 823, 225, 35, 3361, 203, 49162, 1, 589, 19089, 26, 83, 30, 84, 711, 203, 3, 203, 601, 43268, 81, 2024, 203, 2]),
            (Llama3BaseTokenizer(), [128000, 128025, 3632, 66282, 7345, 198, 128015, 128016, 128025, 8858, 19401, 7345, 128017, 2, 1472, 649, 24069, 198, 2, 449, 264, 1973, 287, 198, 2, 734, 627, 128016, 128025, 8858, 19401, 7345, 128017, 2, 1472, 649, 24069, 198, 2, 449, 264, 75510, 734, 627, 128021, 198, 2, 538, 17, 7345, 198, 1058, 220, 17, 2547, 271, 2, 538, 16, 7345, 198, 1058, 220, 16, 2547, 198, 128022, 128012, 755, 24069, 2948, 8568, 997, 128013, 198, 693, 71922, 7800, 198, 128014]),
            (DeepSeekCoderBaseTokenizer(), [32013, 32034, 7364, 14, 8501, 13, 4016, 185, 32024, 32025, 32034, 8501, 62, 7676, 13, 4016, 32026, 2, 1255, 482, 25658, 185, 2, 365, 245, 3034, 272, 185, 2, 1155, 13, 185, 32025, 32034, 8501, 62, 7676, 13, 4016, 32026, 2, 1255, 482, 25658, 185, 2, 365, 245, 7435, 272, 1155, 13, 185, 32030, 185, 2, 757, 17, 13, 4016, 185, 2176, 207, 17, 3110, 185, 185, 2, 757, 16, 13, 4016, 185, 2176, 207, 16, 3110, 185, 32031, 32016, 1551, 25658, 7, 64, 11, 65, 1772, 185, 32015, 185, 2125, 14682, 612, 62, 8164, 185, 32017]),
            (prod_tokenizers.DeepSeekCoderV2Tokenizer(), [100000, 100030, 7362, 14, 8500, 13, 4027, 185, 100020, 100021, 100030, 8500, 62, 7671, 13, 4027, 100022, 2, 1257, 481, 25551, 185, 2, 366, 245, 3044, 272, 185, 2, 1157, 13, 185, 100021, 100030, 8500, 62, 7671, 13, 4027, 100022, 2, 1257, 481, 25551, 185, 2, 366, 245, 52803, 1157, 13, 185, 100027, 185, 2, 762, 17, 13, 4027, 185, 2186, 207, 17, 3123, 185, 185, 2, 762, 16, 13, 4027, 185, 2186, 207, 16, 3123, 185, 100028, 100003, 1558, 25551, 7, 64, 11, 65, 1780, 185, 100002, 185, 2136, 58075, 62, 8157, 185, 100004]),
        ]
    )
    # fmt: on
    def test_enders_prompt_formatter_defaults(
        self, tokenizer: Tokenizer | None, expected_tokens: list[int]
    ):
        """Default settings."""
        prompter = self.get_prompter(tokenizer)
        tokens, _ = prompter.prepare_prompt(self.inputs)
        self.assertEqual(tokens, expected_tokens)

        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        # This is the result of StarCoder 1.
        expected = dedent(
            """\
            <filename>src/example.py
            <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a maxing
            # function.
            <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a pooling function.
            <|sig-begin|>
            # class2.py
            class 2 body

            # class1.py
            class 1 body
            <|sig-end|><fim_prefix>def aggregate(a,b):
            <fim_suffix>
            return aggregated_output
            <fim_middle>"""
        )
        if isinstance(
            tokenizer, (StarCoder2Tokenizer, prod_tokenizers.StarCoder2Tokenizer)
        ):
            # TODO(michiel): refactor tests to be more sane with multi tokenizer.
            expected = expected.replace("<filename>", "<file_sep>")
            expected = expected.replace("<|retrieval_section|>", "<pr_base>")
            expected = expected.replace("<|ret-start|>", "<pr_file>")
            expected = expected.replace("<|ret-body|>", "<pr_base_code>")
        if isinstance(tokenizer, Llama3BaseTokenizer):
            expected = (
                tokenizer.detokenize(tokenizer.special_tokens.begin_sequence) + expected
            )
            expected = expected.replace("<fim_prefix>", "<|fim_prefix|>")
            expected = expected.replace("<fim_suffix>", "<|fim_suffix|>")
            expected = expected.replace("<fim_middle>", "<|fim_middle|>")
        if isinstance(tokenizer, DeepSeekCoderBaseTokenizer):
            expected = (
                tokenizer.detokenize(tokenizer.special_tokens.begin_sequence) + expected
            )
            expected = expected.replace("<fim_prefix>", "<｜fim▁begin｜>")
            expected = expected.replace("<fim_suffix>", "<｜fim▁hole｜>")
            expected = expected.replace("<fim_middle>", "<｜fim▁end｜>")
        if isinstance(tokenizer, prod_tokenizers.DeepSeekCoderV2Tokenizer):
            expected = (
                tokenizer.detokenize(tokenizer.special_tokens.begin_sequence) + expected
            )
            expected = expected.replace("<fim_prefix>", "<｜fim▁begin｜>")
            expected = expected.replace("<fim_suffix>", "<｜fim▁hole｜>")
            expected = expected.replace("<fim_middle>", "<｜fim▁end｜>")
            expected = expected.replace("<filename>", "<|filename|>")
        self.assertEqual(prompt, expected)

    @parameterized.expand([None, TiktokenStarCoderTokenizer()])
    def test_overlap_detection(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        prompter.max_signature_tokens = 0

        # Without ground truth, we should not detect overlaps
        model_input: ModelInput = get_example_03()
        model_input = dataclasses.replace(model_input, extra={"signature_chunks": []})
        text, _ = prompter.prepare_prompt_text(model_input)
        assert "OVERLAP_TEXT" in text, text
        assert "overlap_text" in text, text

        # With ground truth, we should detect overlaps
        model_input: ModelInput = get_example_03()
        model_input = dataclasses.replace(
            model_input,
            cursor_position=20,
            extra={
                "signature_chunks": [],
            },
        )
        text, _ = prompter.prepare_prompt_text(model_input)
        assert "OVERLAP_TEXT" not in text, text
        assert "overlap_text" not in text, text

    @parameterized.expand([None, TiktokenStarCoderTokenizer(), StarCoder2Tokenizer()])
    def test_path_truncation(self, tokenizer: Tokenizer | None):
        """Test path is not incorrectly truncated."""
        prompter = self.get_prompter(tokenizer)
        prompter.max_filename_tokens = 8
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
            <filename>src/example.py
            <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a maxing
            # function.
            <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a pooling function.
            <|sig-begin|>
            # class2.py
            class 2 body

            # class1.py
            class 1 body
            <|sig-end|><fim_prefix>def aggregate(a,b):
            <fim_suffix>
            return aggregated_output
            <fim_middle>"""
        )
        if isinstance(tokenizer, StarCoder2Tokenizer):
            expected = expected.replace("<filename>", "<file_sep>")
            expected = expected.replace("<|retrieval_section|>", "<pr_base>")
            expected = expected.replace("<|ret-start|>", "<pr_file>")
            expected = expected.replace("<|ret-body|>", "<pr_base_code>")
        self.assertEqual(prompt, expected)

    @parameterized.expand([None, TiktokenStarCoderTokenizer(), StarCoder2Tokenizer()])
    def test_shuffle_retrieved_chunks(self, tokenizer: Tokenizer | None):
        """Test shuffling retrieved chunks."""
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(shuffle_retrieved_chunks=True)
        test_inputs = self.inputs
        test_inputs.retrieved_chunks.append(
            Chunk(
                id="chunk1",
                text="# shuffle test\n",
                parent_doc=Document(id="", text="", path="src/foo.py"),
                char_offset=93,
                length=49,
                line_offset=1,
                length_in_lines=2,
            )
        )
        prompt, _ = prompter.prepare_prompt_text(test_inputs)
        expected = dedent(
            """\
            <filename>src/example.py
            <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a maxing
            # function.
            <|ret-start|><filename>src/foo.py<|ret-body|># shuffle test
            <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a pooling function.
            <|sig-begin|>
            # class2.py
            class 2 body

            # class1.py
            class 1 body
            <|sig-end|><fim_prefix>def aggregate(a,b):
            <fim_suffix>
            return aggregated_output
            <fim_middle>"""
        )
        if isinstance(tokenizer, StarCoder2Tokenizer):
            expected = expected.replace("<filename>", "<file_sep>")
            expected = expected.replace("<|retrieval_section|>", "<pr_base>")
            expected = expected.replace("<|ret-start|>", "<pr_file>")
            expected = expected.replace("<|ret-body|>", "<pr_base_code>")

        print("prompt", prompt)
        self.assertEqual(prompt, expected)

    @parameterized.expand([None, TiktokenStarCoderTokenizer(), StarCoder2Tokenizer()])
    def test_nearby_prefix(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(
            nearby_prefix_token_len=6,
            component_order=(
                "retrieval",
                "signature",
                "prefix",
                "suffix",
                "nearby_prefix",
            ),
        )
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
            <filename>src/example.py
            <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a maxing
            # function.
            <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a pooling function.
            <|sig-begin|>
            # class2.py
            class 2 body

            # class1.py
            class 1 body
            <|sig-end|><|far_prefix|>def aggregate<fim_suffix>
            return aggregated_output
            <fim_prefix>(a,b):
            <fim_middle>"""
        )
        if isinstance(tokenizer, StarCoder2Tokenizer):
            expected = expected.replace("<filename>", "<file_sep>")
            expected = expected.replace("<|retrieval_section|>", "<pr_base>")
            expected = expected.replace("<|ret-start|>", "<pr_file>")
            expected = expected.replace("<|ret-body|>", "<pr_base_code>")
        self.assertEqual(prompt, expected)

    @parameterized.expand([None, TiktokenStarCoderTokenizer(), StarCoder2Tokenizer()])
    def test_overlap(self, tokenizer: Tokenizer | None):
        # Test overlap
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(
            component_order=(
                "retrieval",
                "signature",
                "prefix",
                "suffix",
                "nearby_prefix",
            ),
            nearby_prefix_token_len=6,
            nearby_prefix_token_overlap=1,
        )
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
            <filename>src/example.py
            <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a maxing
            # function.
            <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a pooling function.
            <|sig-begin|>
            # class2.py
            class 2 body

            # class1.py
            class 1 body
            <|sig-end|><|far_prefix|>def aggregate(<fim_suffix>
            return aggregated_output
            <fim_prefix>(a,b):
            <fim_middle>"""
        )
        if isinstance(tokenizer, StarCoder2Tokenizer):
            expected = expected.replace("<filename>", "<file_sep>")
            expected = expected.replace("<|retrieval_section|>", "<pr_base>")
            expected = expected.replace("<|ret-start|>", "<pr_file>")
            expected = expected.replace("<|ret-body|>", "<pr_base_code>")
        self.assertEqual(prompt, expected)

    @parameterized.expand([None, TiktokenStarCoderTokenizer(), StarCoder2Tokenizer()])
    def test_quantization(self, tokenizer: Tokenizer | None):
        # Test quantization
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(
            component_order=(
                "retrieval",
                "signature",
                "prefix",
                "suffix",
                "nearby_prefix",
            ),
            nearby_prefix_token_len=6,
            nearby_prefix_token_overlap=1,
            context_quant_token_len=2,
            max_prefix_tokens=2,
        )
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
            <filename>src/example.py
            <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a maxing
            # function.
            <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a pooling function.
            <|sig-begin|>
            # class2.py
            class 2 body

            # class1.py
            class 1 body
            <|sig-end|><|far_prefix|>(<fim_suffix>
            return aggregated_output
            <fim_prefix>(a,b):
            <fim_middle>"""
        )
        if isinstance(tokenizer, StarCoder2Tokenizer):
            expected = expected.replace("<filename>", "<file_sep>")
            expected = expected.replace("<|retrieval_section|>", "<pr_base>")
            expected = expected.replace("<|ret-start|>", "<pr_file>")
            expected = expected.replace("<|ret-body|>", "<pr_base_code>")
        self.assertEqual(prompt, expected)

    @parameterized.expand([None, TiktokenStarCoderTokenizer(), StarCoder2Tokenizer()])
    def test_nearby_suffix(self, tokenizer: Tokenizer | None):
        """Test nearby suffix."""
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(
            component_order=(
                "retrieval",
                "signature",
                "prefix",
                "suffix",
                "nearby_suffix",
                "nearby_prefix",
            ),
            nearby_prefix_token_len=6,
            nearby_prefix_token_overlap=1,
            nearby_suffix_token_len=4,
            nearby_suffix_token_overlap=1,
            context_quant_token_len=2,
            max_prefix_tokens=2,
        )
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
            <filename>src/example.py
            <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a maxing
            # function.
            <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a pooling function.
            <|sig-begin|>
            # class2.py
            class 2 body

            # class1.py
            class 1 body
            <|sig-end|><|far_prefix|>(<|far_suffix|>_output
            <fim_suffix>
            return aggregated_<fim_prefix>(a,b):
            <fim_middle>"""
        )
        if isinstance(tokenizer, StarCoder2Tokenizer):
            expected = expected.replace("<filename>", "<file_sep>")
            expected = expected.replace("<|retrieval_section|>", "<pr_base>")
            expected = expected.replace("<|ret-start|>", "<pr_file>")
            expected = expected.replace("<|ret-body|>", "<pr_base_code>")
        self.assertEqual(prompt, expected)

    @parameterized.expand(
        [
            None,
            TiktokenStarCoderTokenizer(),
            StarCoder2Tokenizer(),
            Llama3BaseTokenizer(),
            DeepSeekCoderBaseTokenizer(),
        ]
    )
    def test_nearby_component_order_present_assert(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(
            nearby_prefix_token_len=6,
            nearby_prefix_token_overlap=1,
            nearby_suffix_token_len=6,
            nearby_suffix_token_overlap=1,
            context_quant_token_len=3,
            max_prefix_tokens=12,
            component_order=(
                "retrieval",
                "signature",
                "prefix",
                "suffix",
            ),
        )
        with self.assertRaises(AssertionError):
            _, _ = prompter.prepare_prompt_text(self.inputs)

    @parameterized.expand(
        [
            None,
            TiktokenStarCoderTokenizer(),
            StarCoder2Tokenizer(),
            Llama3BaseTokenizer(),
            DeepSeekCoderBaseTokenizer(),
        ]
    )
    def test_nearby_len_present_assert(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(
            nearby_prefix_token_len=0,
            nearby_prefix_token_overlap=1,
            nearby_suffix_token_len=0,
            nearby_suffix_token_overlap=1,
            context_quant_token_len=3,
            max_prefix_tokens=12,
            component_order=(
                "retrieval",
                "signature",
                "prefix",
                "suffix",
                "nearby_prefix",
                "nearby_suffix",
            ),
        )
        with self.assertRaises(AssertionError):
            _, _ = prompter.prepare_prompt_text(self.inputs)

    def overlap_from_start(self, prompt1, prompt2):
        for idx, char in enumerate(prompt1):
            if char != prompt2[idx]:
                return idx
        return len(prompt1)

    @parameterized.expand(
        [
            None,
            TiktokenStarCoderTokenizer(),
        ]
    )
    def test_typing_caching(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        context_quant_token_len = 3
        prompter.rebind(
            nearby_prefix_token_len=6,
            nearby_prefix_token_overlap=1,
            nearby_suffix_token_len=3,
            nearby_suffix_token_overlap=1,
            context_quant_token_len=context_quant_token_len,
            max_prefix_tokens=12,
            max_suffix_tokens=12,
            component_order=(
                "prefix",
                "suffix",
                "signature",
                "retrieval",
                "nearby_suffix",
                "nearby_prefix",
            ),
        )

        model_input = self.inputs
        starting_prefix = model_input.prefix
        num_comparisons = 9
        num_hits = 0
        single_token_word = "Seat"

        last_prompt, _ = prompter.prepare_prompt(self.inputs)
        for idx in range(num_comparisons):
            # Mimic typing by repeatedly appending special token
            # (will start failing if we tokenize safe)
            model_input.prefix = starting_prefix + (idx + 1) * single_token_word
            prompt, _ = prompter.prepare_prompt(model_input)
            overlap = self.overlap_from_start(prompt, last_prompt)
            if overlap / len(last_prompt) > 0.9:
                num_hits += 1
            last_prompt = prompt

        # We expect to get a cache miss every context_quant_token_len tokens
        expected_hit_rato = (context_quant_token_len - 1) / context_quant_token_len
        actual_hit_ratio = num_hits / num_comparisons

        assert actual_hit_ratio == expected_hit_rato

    @parameterized.expand(
        [
            None,
            TiktokenStarCoderTokenizer(),
        ]
    )
    def test_header(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(
            component_order=(
                "retrieval",
                "prefix",
                "suffix",
            ),
            max_header_tokens=5,
        )
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
            <filename>src/example.py
            <|retrieval_section|><|ret-start|><filename>example_doc.py
            test/header<|ret-body|># You can aggregate
            # with a maxing
            # function.
            <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a pooling function.
            <fim_prefix>def aggregate(a,b):
            <fim_suffix>
            return aggregated_output
            <fim_middle>"""
        )
        print("prompt", prompt)
        self.assertEqual(prompt, expected)


class TestRogueProdPromptFormatters(unittest.TestCase):
    """Test the Rogue prompt formatter."""

    def setUp(self):
        self.prompter = PromptFormatterRogueProd(
            max_output_token_count=64,
            max_content_len=2048,
            input_fraction=0.5,
            prefix_fraction=0.75,
            max_path_tokens=20,
        )
        self.inputs = get_example_01()

    def test_agrees_with_research_rogue(self):
        """Produces the same output that the rogue formatter in research produces."""
        prompt, _ = self.prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
                          <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a maxing
                          # function.
                          <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a pooling function.
                          <fim_prefix><filename>src/example.py<|prefix-body|>def aggregate(a,b):
                          <fim_suffix>
                          return aggregated_output
                          <fim_middle>"""
        )
        self.assertEqual(prompt, expected)


class TestRoguePromptFormatter(unittest.TestCase):
    """Test the Rogue prompt formatter."""

    def setUp(self):
        self.prompter = PromptFormatterRogue(
            max_prefix_tokens=1000,
            max_suffix_tokens=1000,
        )
        self.inputs = get_example_01()

    def test_rogue_prompt_formatter_defaults(self):
        """Default settings."""
        prompt, _ = self.prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
                          <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a maxing
                          # function.
                          <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a pooling function.
                          <fim_prefix><filename>src/example.py<|prefix-body|>def aggregate(a,b):
                          <fim_suffix>
                          return aggregated_output
                          <fim_middle>"""
        )
        self.assertEqual(prompt, expected)
        tokens, _ = self.prompter.prepare_prompt(self.inputs)
        # fmt: off
        expected_tokens = [49155, 49156, 5, 2763, 81, 1427, 32, 978, 49157, 21, 2448, 883, 19089, 203, 21, 623, 312, 1769, 299, 203, 21, 667, 32, 203, 49156, 5, 2763, 81, 1427, 32, 978, 49157, 21, 2448, 883, 19089, 203, 21, 623, 312, 49094, 667, 32, 203, 1, 5, 1617, 33, 2763, 32, 978, 49158, 589, 19089, 26, 83, 30, 84, 711, 203, 3, 203, 601, 43268, 81, 2024, 203, 2]
        # fmt: on
        self.assertEqual(tokens, expected_tokens)

    def test_deepseek_tokenizer_with_prepend_bos(self):
        """Prepend BOS token."""
        self.prompter.rebind(tokenizer=DeepSeekCoderBaseTokenizer())
        prompt, _ = self.prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
                          <｜begin▁of▁sentence｜><|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a maxing
                          # function.
                          <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a pooling function.
                          <｜fim▁begin｜><filename>src/example.py<|prefix-body|>def aggregate(a,b):
                          <｜fim▁hole｜>
                          return aggregated_output
                          <｜fim▁end｜>"""
        )
        self.assertEqual(prompt, expected)

    def test_limit_retrieved_tokens(self):
        """Limit the number of retrieved tokens."""
        # Expect no retrieval context
        self.prompter.rebind(max_retrieved_chunk_tokens=10)
        prompt, _ = self.prompter.prepare_prompt_text(self.inputs)
        expected: str = dedent(
            """\
                        <|retrieval_section|><fim_prefix><filename>src/example.py<|prefix-body|>def aggregate(a,b):
                        <fim_suffix>
                        return aggregated_output
                        <fim_middle>"""
        )
        self.assertEqual(prompt, expected)

    def test_limit_retrieved_tokens_and_suffix(self):
        # Expect no retrieval context and only part of suffix
        self.prompter.rebind(max_retrieved_chunk_tokens=10, max_suffix_tokens=3)
        prompt, _ = self.prompter.prepare_prompt_text(self.inputs)
        expected: str = dedent(
            """\
                        <|retrieval_section|><fim_prefix><filename>src/example.py<|prefix-body|>def aggregate(a,b):
                        <fim_suffix>
                        return aggregated<fim_middle>"""
        )
        self.assertEqual(prompt, expected)

    def test_no_suffix(self):
        # Expect no retrieval context and no suffix
        self.prompter.rebind(max_retrieved_chunk_tokens=10, max_suffix_tokens=0)
        prompt, _ = self.prompter.prepare_prompt_text(self.inputs)
        expected: str = dedent(
            """\
                        <|retrieval_section|><fim_prefix><filename>src/example.py<|prefix-body|>def aggregate(a,b):
                        <fim_middle>"""
        )
        self.assertEqual(prompt, expected)

    def test_empty_suffix_uses_suffix_token(self):
        # Expect no retrieval context and no suffix, but use suffix token
        self.prompter.rebind(
            max_retrieved_chunk_tokens=10,
            max_suffix_tokens=0,
            always_use_suffix_token=True,
        )
        prompt, _ = self.prompter.prepare_prompt_text(self.inputs)
        expected: str = dedent(
            """\
                        <|retrieval_section|><fim_prefix><filename>src/example.py<|prefix-body|>def aggregate(a,b):
                        <fim_suffix><fim_middle>"""
        )
        self.assertEqual(prompt, expected)

    def test_reverse_order(self):
        # Reverse order of prefix and suffix
        self.prompter.rebind(prefix_after_suffix=True)
        prompt, _ = self.prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
                          <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a maxing
                          # function.
                          <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a pooling function.
                          <fim_suffix>
                          return aggregated_output
                          <fim_prefix><filename>src/example.py<|prefix-body|>def aggregate(a,b):
                          <fim_middle>"""
        )
        self.assertEqual(prompt, expected)

    def test_retrieval_after_context(self):
        self.prompter.rebind(add_retrieval_after_context=True)
        prompt, _ = self.prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
                          <fim_prefix><filename>src/example.py<|prefix-body|>def aggregate(a,b):
                          <fim_suffix>
                          return aggregated_output
                          <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a maxing
                          # function.
                          <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a pooling function.
                          <fim_middle>"""
        )
        self.assertEqual(prompt, expected)

    def test_only_truncate_true_prefix(self):
        # If only_truncate_true_prefix is activated, the filepath should not be truncated and num prefix chars should only be from the true prefix
        self.prompter.rebind(only_truncate_true_prefix=True, max_prefix_tokens=11)
        prompt, metadata = self.prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
                          <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a maxing
                          # function.
                          <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a pooling function.
                          <fim_prefix><filename>src/example.py<|prefix-body|>,b):
                          <fim_suffix>
                          return aggregated_output
                          <fim_middle>"""
        )
        self.assertEqual(prompt, expected)
        self.assertEqual(metadata["num_prefix_chars_post_truncation"], 5)

    def test_nearby_prefix(self):
        self.prompter.rebind(nearby_prefix_char_len=10)
        prompt, _ = self.prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
                          <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a maxing
                          # function.
                          <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a pooling function.
                          <fim_prefix><filename>src/example.py<|prefix-body|>def aggregate(a,b):
                          <fim_suffix>
                          return aggregated_output
                          <|nearby_prefix|>ate(a,b):
                          <fim_middle>"""
        )
        self.assertEqual(prompt, expected)

    def test_prefix_char_offset(self):
        self.prompter.rebind(nearby_prefix_char_len=10, prefix_char_offset=10)
        prompt, _ = self.prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
                          <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a maxing
                          # function.
                          <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a pooling function.
                          <fim_prefix><filename>src/example.py<|prefix-body|>def aggreg<fim_suffix>
                          return aggregated_output
                          <|nearby_prefix|>ate(a,b):
                          <fim_middle>"""
        )
        self.assertEqual(prompt, expected)

    def test_overlap_detection(self):
        # Without ground truth, we should not detect overlaps
        model_input: ModelInput = get_example_03()
        text, _ = self.prompter.prepare_prompt_text(model_input)
        assert "OVERLAP_TEXT" in text, text
        assert "overlap_text" in text, text

        # With ground truth, we should detect overlaps
        model_input: ModelInput = get_example_03()
        model_input = dataclasses.replace(model_input, cursor_position=20)
        text, _ = self.prompter.prepare_prompt_text(model_input)
        assert "OVERLAP_TEXT" not in text, text
        assert "overlap_text" not in text, text


class TestRogueStatelessCachePromptFormatter(unittest.TestCase):
    """Test the Rogue stateless cache prompt formatter."""

    def setUp(self):
        self.inputs = get_example_01()

    def get_prompter(self, tokenizer: Tokenizer | None):
        prompter = PromptFormatterRogueSLCache(
            max_prefix_tokens=1000,
            max_suffix_tokens=1000,
        )
        if tokenizer is not None:
            prompter.rebind(tokenizer=tokenizer)
        return prompter

    # fmt: off
    @parameterized.expand(
        [
            (None, [5, 1617, 33, 2763, 32, 978, 203, 49155, 49156, 5, 2763, 81, 1427, 32, 978, 49157, 21, 2448, 883, 19089, 203, 21, 623, 312, 1769, 299, 203, 21, 667, 32, 203, 49156, 5, 2763, 81, 1427, 32, 978, 49157, 21, 2448, 883, 19089, 203, 21, 623, 312, 49094, 667, 32, 203, 1, 589, 19089, 26, 83, 30, 84, 711, 203, 3, 203, 601, 43268, 81, 2024, 203, 2]),
            (TiktokenStarCoderTokenizer(), [5, 1617, 33, 2763, 32, 978, 203, 49155, 49156, 5, 2763, 81, 1427, 32, 978, 49157, 21, 2448, 883, 19089, 203, 21, 623, 312, 1769, 299, 203, 21, 667, 32, 203, 49156, 5, 2763, 81, 1427, 32, 978, 49157, 21, 2448, 883, 19089, 203, 21, 623, 312, 49094, 667, 32, 203, 1, 589, 19089, 26, 83, 30, 84, 711, 203, 3, 203, 601, 43268, 81, 2024, 203, 2]),
        ]
    )
    # fmt: on
    def test_rogue_prompt_formatter_defaults(
        self, tokenizer: Tokenizer | None, expected_tokens: list[int]
    ):
        prompter = self.get_prompter(tokenizer)
        tokens, _ = prompter.prepare_prompt(self.inputs)
        self.assertEqual(tokens, expected_tokens )
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
                          <filename>src/example.py
                          <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a maxing
                          # function.
                          <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a pooling function.
                          <fim_prefix>def aggregate(a,b):
                          <fim_suffix>
                          return aggregated_output
                          <fim_middle>"""
        )
        self.assertEqual(prompt, expected)

    @parameterized.expand([DeepSeekCoderBaseTokenizer(), DeepSeekCoderBaseTokenizer()])
    def test_deepseek_tokenizer_with_prepend_bos(self, tokenizer: Tokenizer):
        """Prepend BOS token."""
        prompter = self.get_prompter(tokenizer)
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
                          <｜begin▁of▁sentence｜><filename>src/example.py
                          <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a maxing
                          # function.
                          <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                          # with a pooling function.
                          <｜fim▁begin｜>def aggregate(a,b):
                          <｜fim▁hole｜>
                          return aggregated_output
                          <｜fim▁end｜>"""
        )
        self.assertEqual(prompt, expected)

    @parameterized.expand([None, TiktokenStarCoderTokenizer()])
    def test_limit_retrieved_tokens(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(max_retrieved_chunk_tokens=10)
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected: str = dedent(
            """\
                        <filename>src/example.py
                        <|retrieval_section|><fim_prefix>def aggregate(a,b):
                        <fim_suffix>
                        return aggregated_output
                        <fim_middle>"""
        )
        self.assertEqual(prompt, expected)

    @parameterized.expand([None, TiktokenStarCoderTokenizer()])
    def test_limit_retrieved_suffix(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        # Expect no retrieval context and only part of suffix
        prompter.rebind(max_retrieved_chunk_tokens=10, max_suffix_tokens=3)
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected: str = dedent(
            """\
                        <filename>src/example.py
                        <|retrieval_section|><fim_prefix>def aggregate(a,b):
                        <fim_suffix>
                        return aggregated<fim_middle>"""
        )
        self.assertEqual(prompt, expected)

        # Expect no retrieval context and no suffix
        prompter.rebind(max_retrieved_chunk_tokens=10, max_suffix_tokens=0)
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected: str = dedent(
            """\
                        <filename>src/example.py
                        <|retrieval_section|><fim_prefix>def aggregate(a,b):
                        <fim_suffix><fim_middle>"""
        )
        self.assertEqual(prompt, expected)

    @parameterized.expand([None, TiktokenStarCoderTokenizer(), StarCoder2Tokenizer()])
    def test_reverse_prefix_suffix(self, tokenizer: Tokenizer | None):
        """Reverse prefix and suffix."""
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(component_order=("retrieval", "suffix", "prefix"))
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
                        <filename>src/example.py
                        <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                        # with a maxing
                        # function.
                        <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                        # with a pooling function.
                        <fim_suffix>
                        return aggregated_output
                        <fim_prefix>def aggregate(a,b):
                        <fim_middle>"""
        )
        if isinstance(tokenizer, StarCoder2Tokenizer):
            expected = expected.replace("<filename>", "<file_sep>")
            expected = expected.replace("<|retrieval_section|>", "<pr_base>")
            expected = expected.replace("<|ret-start|>", "<pr_file>")
            expected = expected.replace("<|ret-body|>", "<pr_base_code>")
        self.assertEqual(prompt, expected)

    @parameterized.expand([None, TiktokenStarCoderTokenizer(), StarCoder2Tokenizer()])
    def test_retrieval_last(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(component_order=("prefix", "suffix", "retrieval"))
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
                        <filename>src/example.py
                        <fim_prefix>def aggregate(a,b):
                        <fim_suffix>
                        return aggregated_output
                        <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                        # with a maxing
                        # function.
                        <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                        # with a pooling function.
                        <fim_middle>"""
        )
        if isinstance(tokenizer, StarCoder2Tokenizer):
            expected = expected.replace("<filename>", "<file_sep>")
            expected = expected.replace("<|retrieval_section|>", "<pr_base>")
            expected = expected.replace("<|ret-start|>", "<pr_file>")
            expected = expected.replace("<|ret-body|>", "<pr_base_code>")
        self.assertEqual(prompt, expected)

    @parameterized.expand([None, TiktokenStarCoderTokenizer(), StarCoder2Tokenizer()])
    def test_nearby_prefix(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(
            nearby_prefix_token_len=6,
            component_order=("retrieval", "prefix", "suffix", "nearby_prefix"),
        )
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
                        <filename>src/example.py
                        <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                        # with a maxing
                        # function.
                        <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                        # with a pooling function.
                        <fim_prefix>def aggregate<fim_suffix>
                        return aggregated_output
                        <|nearby_prefix|>(a,b):
                        <fim_middle>"""
        )
        if isinstance(tokenizer, StarCoder2Tokenizer):
            expected = expected.replace("<filename>", "<file_sep>")
            expected = expected.replace("<|retrieval_section|>", "<pr_base>")
            expected = expected.replace("<|ret-start|>", "<pr_file>")
            expected = expected.replace("<|ret-body|>", "<pr_base_code>")
        self.assertEqual(prompt, expected)

    @parameterized.expand([None, TiktokenStarCoderTokenizer(), StarCoder2Tokenizer()])
    def test_overlap(self, tokenizer: Tokenizer | None):
        """Test overlap."""
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(
            component_order=("retrieval", "prefix", "suffix", "nearby_prefix"),
            nearby_prefix_token_len=6,
            nearby_prefix_token_overlap=1,
        )
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
                        <filename>src/example.py
                        <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                        # with a maxing
                        # function.
                        <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                        # with a pooling function.
                        <fim_prefix>def aggregate(<fim_suffix>
                        return aggregated_output
                        <|nearby_prefix|>(a,b):
                        <fim_middle>"""
        )
        if isinstance(tokenizer, StarCoder2Tokenizer):
            expected = expected.replace("<filename>", "<file_sep>")
            expected = expected.replace("<|retrieval_section|>", "<pr_base>")
            expected = expected.replace("<|ret-start|>", "<pr_file>")
            expected = expected.replace("<|ret-body|>", "<pr_base_code>")
        self.assertEqual(prompt, expected)

    @parameterized.expand([None, TiktokenStarCoderTokenizer(), StarCoder2Tokenizer()])
    def test_quantization(self, tokenizer: Tokenizer | None):
        # Test quantization
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(
            component_order=("retrieval", "prefix", "suffix", "nearby_prefix"),
            nearby_prefix_token_len=6,
            nearby_prefix_token_overlap=1,
            context_quant_token_len=3,
            max_prefix_tokens=12,
        )
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
                        <filename>src/example.py
                        <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                        # with a maxing
                        # function.
                        <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                        # with a pooling function.
                        <fim_prefix>def aggregate(a<fim_suffix>
                        return aggregated_output
                        <|nearby_prefix|>a,b):
                        <fim_middle>"""
        )
        if isinstance(tokenizer, StarCoder2Tokenizer):
            expected = expected.replace("<filename>", "<file_sep>")
            expected = expected.replace("<|retrieval_section|>", "<pr_base>")
            expected = expected.replace("<|ret-start|>", "<pr_file>")
            expected = expected.replace("<|ret-body|>", "<pr_base_code>")
        self.assertEqual(prompt, expected)

    @parameterized.expand([None, TiktokenStarCoderTokenizer()])
    def test_nearby_suffix(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(
            nearby_prefix_token_len=6,
            nearby_prefix_token_overlap=1,
            nearby_suffix_token_len=3,
            nearby_suffix_token_overlap=1,
            context_quant_token_len=3,
            max_prefix_tokens=12,
            component_order=(
                "retrieval",
                "prefix",
                "suffix",
                "nearby_suffix",
                "nearby_prefix",
            ),
        )
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
                        <filename>src/example.py
                        <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                        # with a maxing
                        # function.
                        <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                        # with a pooling function.
                        <fim_prefix>def aggregate(a<fim_suffix> aggregated_output
                        <|nearby_suffix|>
                        return aggregated<|nearby_prefix|>a,b):
                        <fim_middle>"""
        )
        self.assertEqual(prompt, expected)

    @parameterized.expand([None, TiktokenStarCoderTokenizer()])
    def test_far_tokens(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(
            nearby_prefix_token_len=6,
            nearby_prefix_token_overlap=1,
            nearby_suffix_token_len=3,
            nearby_suffix_token_overlap=1,
            context_quant_token_len=3,
            max_prefix_tokens=12,
            component_order=(
                "retrieval",
                "prefix",
                "suffix",
                "nearby_suffix",
                "nearby_prefix",
            ),
            use_far_prefix_token=True,
            use_far_suffix_token=True,
        )
        prompt, _ = prompter.prepare_prompt_text(self.inputs)
        print("prompt:", prompt)
        expected = dedent(
            """\
                        <filename>src/example.py
                        <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                        # with a maxing
                        # function.
                        <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
                        # with a pooling function.
                        <|far_prefix|>def aggregate(a<|far_suffix|> aggregated_output
                        <fim_suffix>
                        return aggregated<fim_prefix>a,b):
                        <fim_middle>"""
        )
        self.assertEqual(prompt, expected)

    @parameterized.expand(
        [
            None,
            TiktokenStarCoderTokenizer(),
            StarCoder2Tokenizer(),
            Llama3BaseTokenizer(),
            DeepSeekCoderBaseTokenizer(),
        ]
    )
    def test_nearby_component_order_present_assert(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(
            nearby_prefix_token_len=6,
            nearby_prefix_token_overlap=1,
            nearby_suffix_token_len=6,
            nearby_suffix_token_overlap=1,
            context_quant_token_len=3,
            max_prefix_tokens=12,
            component_order=(
                "retrieval",
                "prefix",
                "suffix",
            ),
        )
        with self.assertRaises(AssertionError):
            _, _ = prompter.prepare_prompt_text(self.inputs)

    @parameterized.expand(
        [
            None,
            TiktokenStarCoderTokenizer(),
            StarCoder2Tokenizer(),
            Llama3BaseTokenizer(),
            DeepSeekCoderBaseTokenizer(),
        ]
    )
    def test_nearby_len_present_assert(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        prompter.rebind(
            nearby_prefix_token_len=0,
            nearby_prefix_token_overlap=1,
            nearby_suffix_token_len=0,
            nearby_suffix_token_overlap=1,
            context_quant_token_len=3,
            max_prefix_tokens=12,
            component_order=(
                "retrieval",
                "prefix",
                "suffix",
                "nearby_prefix",
                "nearby_suffix",
            ),
        )
        with self.assertRaises(AssertionError):
            _, _ = prompter.prepare_prompt_text(self.inputs)

    def overlap_from_start(self, prompt1, prompt2):
        for idx, char in enumerate(prompt1):
            if char != prompt2[idx]:
                return idx
        return len(prompt1)

    @parameterized.expand(
        [
            None,
            TiktokenStarCoderTokenizer(),
            StarCoder2Tokenizer(),
            Llama3BaseTokenizer(),
        ]
    )
    def test_typing_caching(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        context_quant_token_len = 3
        prompter.rebind(
            nearby_prefix_token_len=6,
            nearby_prefix_token_overlap=1,
            nearby_suffix_token_len=3,
            nearby_suffix_token_overlap=1,
            context_quant_token_len=context_quant_token_len,
            max_prefix_tokens=12,
            max_suffix_tokens=12,
            component_order=(
                "prefix",
                "suffix",
                "retrieval",
                "nearby_suffix",
                "nearby_prefix",
            ),
        )

        model_input = self.inputs
        starting_prefix = model_input.prefix
        num_comparisons = 9
        num_hits = 0
        single_token_word = "Seat"

        last_prompt, _ = prompter.prepare_prompt(self.inputs)
        for idx in range(num_comparisons):
            model_input.prefix = starting_prefix + (idx + 1) * single_token_word
            prompt, _ = prompter.prepare_prompt(model_input)
            overlap = self.overlap_from_start(prompt, last_prompt)
            if overlap / len(last_prompt) > 0.9:
                num_hits += 1
            last_prompt = prompt

        # We expect to get a cache miss every context_quant_token_len tokens
        expected_hit_rato = (context_quant_token_len - 1) / context_quant_token_len
        actual_hit_ratio = num_hits / num_comparisons
        assert actual_hit_ratio == expected_hit_rato

    @parameterized.expand(
        [
            None,
            TiktokenStarCoderTokenizer(),
            StarCoder2Tokenizer(),
            Llama3BaseTokenizer(),
            # ResearchTokenizerUseProdDeepSeekCoderBase(),
        ]
    )
    def test_del_caching(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        context_quant_token_len = 3
        prompter.rebind(
            nearby_prefix_token_len=6,
            nearby_prefix_token_overlap=1,
            nearby_suffix_token_len=3,
            nearby_suffix_token_overlap=1,
            context_quant_token_len=context_quant_token_len,
            max_prefix_tokens=12,
            max_suffix_tokens=10,
            component_order=(
                "prefix",
                "suffix",
                "retrieval",
                "nearby_suffix",
                "nearby_prefix",
            ),
        )

        model_input = self.inputs
        single_token_word = "Seat"
        starting_suffix = single_token_word * 9 + model_input.suffix
        model_input.suffix = starting_suffix
        num_comparisons = 9
        num_hits = 0

        last_prompt, _ = prompter.prepare_prompt(model_input)
        for idx in range(num_comparisons):
            model_input.suffix = starting_suffix[len(single_token_word) * (idx + 1) :]
            prompt, _ = prompter.prepare_prompt(model_input)
            overlap = self.overlap_from_start(prompt, last_prompt)

            if overlap / len(last_prompt) > 0.8:
                num_hits += 1
            last_prompt = prompt

        # We expect to get a cache miss every context_quant_token_len tokens
        expected_hit_ratio = (context_quant_token_len - 1) / context_quant_token_len
        actual_hit_ratio = num_hits / num_comparisons

        assert actual_hit_ratio == expected_hit_ratio

    @parameterized.expand(
        [
            None,
            TiktokenStarCoderTokenizer(),
            StarCoder2Tokenizer(),
            Llama3BaseTokenizer(),
            DeepSeekCoderBaseTokenizer(),
        ]
    )
    def test_overlap_detection(self, tokenizer: Tokenizer | None):
        prompter = self.get_prompter(tokenizer)
        # Without ground truth, we should not detect overlaps
        model_input: ModelInput = get_example_03()
        text, _ = prompter.prepare_prompt_text(model_input)
        assert "OVERLAP_TEXT" in text, text
        assert "overlap_text" in text, text

        # With ground truth, we should detect overlaps
        model_input: ModelInput = get_example_03()
        model_input = dataclasses.replace(model_input, cursor_position=20)
        text, _ = prompter.prepare_prompt_text(model_input)
        assert "OVERLAP_TEXT" not in text, text
        assert "overlap_text" not in text, text


class TestStarCoderPromptFormatters(unittest.TestCase):
    """Test the StarCoder prompt formatters."""

    @parameterized.expand([True, False])
    def test_starcoder_prompt_formatter(self, apply_seralization: bool):
        """Test the PromptFormatterStarCoder class."""
        prompter = PromptFormatterStarCoder()
        if apply_seralization:
            prompter = serialization_check(prompter, PromptFormatterStarCoder)
        inputs = get_example_01()
        prompt, _ = prompter.prepare_prompt_text(inputs)
        expected = dedent(
            """\
                          <fim_prefix># You can aggregate
                          # with a maxing
                          # function.

                          # You can aggregate
                          # with a pooling function.

                          def aggregate(a,b):
                          <fim_suffix>
                          return aggregated_output
                          <fim_middle>"""
        )

        self.assertEqual(prompt, expected)

        prompter = PromptFormatterStarCoder()
        if apply_seralization:
            prompter = serialization_check(prompter, PromptFormatterStarCoder)
        inputs = get_example_01()
        inputs.path = "/path/to/my_file.py"
        prompter.include_filename_in_prompt = True
        prompter.github_stars = 500
        prompt, _ = prompter.prepare_prompt_text(inputs)
        expected = dedent(
            """\
                          <fim_prefix><filename>my_file.py<gh_stars>500
                          # You can aggregate
                          # with a maxing
                          # function.

                          # You can aggregate
                          # with a pooling function.

                          def aggregate(a,b):
                          <fim_suffix>
                          return aggregated_output
                          <fim_middle>"""
        )

        self.assertEqual(prompt, expected)

        # Expect no retrieval context
        prompter = PromptFormatterStarCoder()
        prompter.rebind(max_retrieved_chunk_tokens=10)
        if apply_seralization:
            prompter = serialization_check(prompter, PromptFormatterStarCoder)
        prompt, _ = prompter.prepare_prompt_text(inputs)
        expected: str = dedent(
            """\
                                <fim_prefix>def aggregate(a,b):
                                <fim_suffix>
                                return aggregated_output
                                <fim_middle>"""
        )
        self.assertEqual(prompt, expected)

        # Expect no retrieval context and only part of suffix
        prompter.rebind(max_retrieved_chunk_tokens=10, max_suffix_tokens=3)
        if apply_seralization:
            prompter = serialization_check(prompter, PromptFormatterStarCoder)
        prompt, _ = prompter.prepare_prompt_text(inputs)
        expected: str = dedent(
            """\
                                <fim_prefix>def aggregate(a,b):
                                <fim_suffix>
                                return aggregated<fim_middle>"""
        )
        self.assertEqual(prompt, expected)

        # Expect no retrieval context and only part of suffix
        prompter.rebind(max_retrieved_chunk_tokens=10, max_suffix_tokens=0)
        if apply_seralization:
            prompter = serialization_check(prompter, PromptFormatterStarCoder)
        prompt, _ = prompter.prepare_prompt_text(inputs)
        expected: str = dedent("""def aggregate(a,b):\n""")
        self.assertEqual(prompt, expected)

        # Expect FIM when max_suffix_tokens is 0 and always_fim_style is enabled
        prompter.rebind(
            max_retrieved_chunk_tokens=10, max_suffix_tokens=0, always_fim_style=True
        )
        if apply_seralization:
            prompter = serialization_check(prompter, PromptFormatterStarCoder)
        prompt, _ = prompter.prepare_prompt_text(inputs)
        expected: str = dedent(
            """\
                                <fim_prefix>def aggregate(a,b):
                                <fim_suffix><fim_middle>"""
        )
        self.assertEqual(prompt, expected)

        # Expect FIM when max_suffix_tokens is positive and always_fim_style is enabled
        prompter.rebind(
            max_retrieved_chunk_tokens=10, max_suffix_tokens=3, always_fim_style=True
        )
        if apply_seralization:
            prompter = serialization_check(prompter, PromptFormatterStarCoder)
        prompt, _ = prompter.prepare_prompt_text(inputs)
        expected: str = dedent(
            """\
                                <fim_prefix>def aggregate(a,b):
                                <fim_suffix>
                                return aggregated<fim_middle>"""
        )
        self.assertEqual(prompt, expected)

        # Expect no FIM max_suffix_tokens is 0 and always_fim_style is disabled
        prompter.rebind(
            max_retrieved_chunk_tokens=10,
            max_suffix_tokens=0,
            always_fim_style=False,
        )
        prompt, _ = prompter.prepare_prompt_text(inputs)
        expected: str = dedent(
            """\
                                def aggregate(a,b):
                                """
        )

        self.assertEqual(prompt, expected)

    def test_layout_for_retrieved_chunks(self):
        prompter = PromptFormatterStarCoder()
        prompter.retrieval_layout_style = ChunkLayout.COMMENT
        model_input = get_example_01()
        out, _ = prompter.prepare_prompt_text(model_input)
        baseline = dedent(
            """\
                          <fim_prefix># Here are some relevant code fragments from other files of the repo:
                          # --------------------------------------------------
                          # the below code fragment can be found in:
                          # example_doc.py
                          # --------------------------------------------------
                          # # You can aggregate
                          # # with a maxing
                          # # function.
                          # --------------------------------------------------
                          # the below code fragment can be found in:
                          # example_doc.py
                          # --------------------------------------------------
                          # # You can aggregate
                          # # with a pooling function.
                          # --------------------------------------------------
                          def aggregate(a,b):
                          <fim_suffix>
                          return aggregated_output
                          <fim_middle>"""
        )
        self.assertEqual(out, baseline)

        prompter.retrieval_layout_style = ChunkLayout.GENERIC
        out, _ = prompter.prepare_prompt_text(model_input)
        baseline_generic = dedent(
            """\
                          <fim_prefix># You can aggregate
                          # with a maxing
                          # function.

                          # You can aggregate
                          # with a pooling function.

                          def aggregate(a,b):
                          <fim_suffix>
                          return aggregated_output
                          <fim_middle>"""
        )
        self.assertEqual(out, baseline_generic)

    def test_overlap_detection(self):
        prompter = PromptFormatterStarCoder()

        # Without ground truth, we should not detect overlaps
        model_input: ModelInput = get_example_03()
        text, _ = prompter.prepare_prompt_text(model_input)
        assert "OVERLAP_TEXT" in text, text
        assert "overlap_text" in text, text

        # With ground truth, we should detect overlaps
        model_input: ModelInput = dataclasses.replace(model_input, cursor_position=20)
        text, _ = prompter.prepare_prompt_text(model_input)
        assert "OVERLAP_TEXT" not in text, text
        assert "overlap_text" not in text, text


class TestLLAMA2PromptFormatters(unittest.TestCase):
    """Test the LLAMA2 prompt formatters."""

    @parameterized.expand(["llama", "llama2"])
    def test_simple(self, name: str):
        """Test some simple functionalities."""
        prompter = get_prompt_formatter(name)
        inputs = ModelInput(prefix="0" * 100)
        prompt, _ = prompter.prepare_prompt_text(inputs)
        self.assertEqual(prompt, "0" * 100)
        prompt_tokens, _ = prompter.prepare_prompt(inputs)
        self.assertEqual(prompt_tokens[0], 1)
        prompter = prompter.rebind(max_prompt_tokens=10)
        prompt_tokens, _ = prompter.prepare_prompt(inputs)
        self.assertEqual(prompt_tokens[0], 1)
        self.assertEqual(len(prompt_tokens), 10)

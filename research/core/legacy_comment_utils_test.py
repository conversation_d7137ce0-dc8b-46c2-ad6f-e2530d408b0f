"""Tests for legacy_comment_utils."""

from research.core.legacy_comment_utils import make_comment_block


def test_make_comment_block():
    """Test make_comment_block."""
    assert make_comment_block("python", "some text") == "# some text", "python"
    assert make_comment_block("python", "some text\n") == "# some text\n", "python"
    assert (
        make_comment_block("python", "some text\nmore text\n")
        == "# some text\n# more text\n"
    ), "multi-line python"
    assert (
        make_comment_block("java", "some text\nmore text\n")
        == "// some text\n// more text\n"
    ), "multi-line java"
    assert (
        make_comment_block("java", "    some indented text\n    more indented text\n")
        == "    // some indented text\n    // more indented text\n"
    ), "indented text"
    assert (
        make_comment_block("java", "\tsome indented text\n\tmore indented text\n")
        == "\t// some indented text\n\t// more indented text\n"
    ), "indented text with tabs"
    assert (
        make_comment_block(
            "java", "no-indent\n    some indented text\n    more indented text\n"
        )
        == "// no-indent\n//     some indented text\n//     more indented text\n"
    ), "partially indented text"


def test_make_common_block_no_common_indentation_option():
    assert (
        make_comment_block(
            "python", "some text", comment_after_common_indentation=False
        )
        == "# some text"
    ), "python"
    assert (
        make_comment_block(
            "python", "some text\n", comment_after_common_indentation=False
        )
        == "# some text\n"
    ), "python"
    assert (
        make_comment_block(
            "java",
            "    some indented text\n    more indented text\n",
            comment_after_common_indentation=False,
        )
        == "//     some indented text\n//     more indented text\n"
    ), "indented text"
    assert (
        make_comment_block(
            "java",
            "\tsome indented text\n\tmore indented text\n",
            comment_after_common_indentation=False,
        )
        == "// \tsome indented text\n// \tmore indented text\n"
    ), "indented text with tabs"
    assert (
        make_comment_block(
            "java",
            "no-indent\n    some indented text\n    more indented text\n",
            comment_after_common_indentation=False,
        )
        == "// no-indent\n//     some indented text\n//     more indented text\n"
    ), "partially indented text"

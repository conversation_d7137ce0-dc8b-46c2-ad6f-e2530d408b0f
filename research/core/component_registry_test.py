"""Test for the component registry module."""

from dataclasses import dataclass
from typing import Optional

from research.core.component_registry import (
    autocreate_registry,
    create_components,
)


# Creating these widgets as dataclasses to make it convenient to test equality.
@dataclass
class SimpleWidget:
    """A simple widget."""

    param: str


@dataclass
class ComplexWidget:
    a_widget: SimpleWidget
    widgets: list[SimpleWidget]
    widget_map: dict[str, SimpleWidget]
    maybe_widget: Optional[SimpleWidget] = None


def test_autocreate_registry():
    configs = {
        "main_widget": {
            "$component_name": "research.core.component_registry_test.ComplexWidget",
            "a_widget": "$foo_widget",
            "widgets": ["$foo_widget", "$bar_widget"],
            "widget_map": {"foo": "$foo_widget", "bar": "$bar_widget"},
        },
        "foo_widget": {
            "$component_name": "research.core.component_registry_test.SimpleWidget",
            "param": "foo",
        },
        "bar_widget": {
            "$component_name": "research.core.component_registry_test.SimpleWidget",
            "param": "bar",
        },
    }
    registry = autocreate_registry(configs)

    assert SimpleWidget in registry
    assert ComplexWidget in registry


def test_create_components():
    configs = {
        "main_widget": {
            "$component_name": "research.core.component_registry_test.ComplexWidget",
            "a_widget": "$foo_widget",
            "widgets": ["$foo_widget", "$bar_widget"],
            "widget_map": {"foo": "$foo_widget", "bar": "$bar_widget"},
        },
        "foo_widget": {
            "$component_name": "research.core.component_registry_test.SimpleWidget",
            "param": "foo",
        },
        "bar_widget": {
            "$component_name": "research.core.component_registry_test.SimpleWidget",
            "param": "bar",
        },
    }
    # Look ma, no registries!
    components = create_components(configs)
    simple_foo = SimpleWidget("foo")
    simple_bar = SimpleWidget("bar")
    complex_widget = ComplexWidget(
        a_widget=simple_foo,
        widgets=[simple_foo, simple_bar],
        widget_map={"foo": simple_foo, "bar": simple_bar},
    )

    assert components.get_with_type("foo_widget", SimpleWidget) == simple_foo
    assert components.get_with_type("bar_widget", SimpleWidget) == simple_bar
    assert components.get_with_type("main_widget", ComplexWidget) == complex_widget

"""Test LLAMA prompt formatters.

pytest research/core/llama_prompt_formatters_test.py
"""

import tempfile
import unittest

import llama.generation as llama_generation
import llama.tokenizer as llama_tokenizer
from parameterized import parameterized

from research.core.constants import AUGMENT_CHECKPOINTS_ROOT
from research.core.llama_prompt_formatters import (
    CodeEditTemplateBasedPromptFormatter,
    CodeLlamaChatFormatter,
    CodeLlamaPromptFormatter,
    DeepSeekCoderBaseFormatter,
    DeepSeekCoderInstructFormatter,
    DeepSeekLLMChatFormatter,
    Dialog,
    WizardCoderChatFormatter,
)
from research.core.model_input import ModelInput

# Build some test data examples, which are used in many different unit tests


def get_example_01():
    return ModelInput(
        prefix=r'''def remove_non_ascii(s: str) -> str:
    """ ''',
        suffix=r"""
    return result""",
    )


def get_example_02():
    return ModelInput(
        prefix=r"""def quick_sort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[0]
    left = []
    right = []
""",
        suffix=r"""
        if arr[i] < pivot:
            left.append(arr[i])
        else:
            right.append(arr[i])
    return quick_sort(left) + [pivot] + quick_sort(right)""",
    )


def get_example_dialog_01():
    return ModelInput(
        prefix="In Bash, how do I list all text files in the current directory (excluding subdirectories) that have been modified in the last month?"
    )


def get_chat_question(i):
    return f"this is question {i}"


def get_chat_answer(i):
    return f"this is answer {i}"


# NOTE(arun): The following two functions correspond to the prompt preparation logic
#   in llama_generation.Llama and we duplicate them here to make sure we provide the
#   same input as llama.
B_INST, E_INST = llama_generation.B_INST, llama_generation.E_INST
B_SYS, E_SYS = llama_generation.B_SYS, llama_generation.E_SYS


def prepare_dialog_prompt(tokenizer, dialog: llama_generation.Dialog):
    if dialog[0]["role"] == "system":
        dialog = [
            llama_generation.Message(
                role=dialog[1]["role"],
                content=B_SYS + dialog[0]["content"] + E_SYS + dialog[1]["content"],
            )
        ] + dialog[2:]
    assert all([msg["role"] == "user" for msg in dialog[::2]]) and all(
        [msg["role"] == "assistant" for msg in dialog[1::2]]
    ), (
        "model only supports 'system', 'user' and 'assistant' roles, "
        "starting with 'system', then 'user' and alternating (u/a/u/a/u...)"
    )
    dialog_tokens: list[int] = sum(
        [
            tokenizer.encode(
                f"{B_INST} {(prompt['content']).strip()} {E_INST} {(answer['content']).strip()} ",
                bos=True,
                eos=True,
            )
            for prompt, answer in zip(
                dialog[::2],
                dialog[1::2],
            )
        ],
        [],
    )
    assert (
        dialog[-1]["role"] == "user"
    ), f"Last message must be from user, got {dialog[-1]['role']}"
    dialog_tokens += tokenizer.encode(
        f"{B_INST} {(dialog[-1]['content']).strip()} {E_INST}",
        bos=True,
        eos=False,
    )
    return dialog_tokens


def prepare_completion_prompt(tokenizer, text):
    return tokenizer.encode(text, bos=True, eos=False)


class TestCodeLlama(unittest.TestCase):
    """Test different kinds of CodeLlama completion formatter."""

    def _get_reference_tokenizer(self):
        return llama_tokenizer.Tokenizer(
            model_path=str(
                AUGMENT_CHECKPOINTS_ROOT / "llama/CodeLlama-7b/tokenizer.model"
            )
        )

    def test_completion(self):
        """Test the completion prompt."""
        model_input = ModelInput(prefix="def hello():")

        reference_tokenizer = self._get_reference_tokenizer()
        expected_tokens = prepare_completion_prompt(
            reference_tokenizer, model_input.prefix
        )

        formatter = CodeLlamaPromptFormatter(always_fim_style=False)
        actual_tokens, _ = formatter.prepare_prompt(model_input)

        self.assertEqual(expected_tokens, actual_tokens)
        self.assertEqual("def hello():", formatter.tokenizer.detokenize(actual_tokens))

    @parameterized.expand([True, False])
    def test_fim(self, suffix_first):
        """Test the infilling prompt."""
        model_input = get_example_01()

        reference_tokenizer = self._get_reference_tokenizer()
        expected_tokens = llama_generation.infilling_prompt_tokens(
            tokenizer=reference_tokenizer,
            pre=model_input.prefix,
            suf=model_input.suffix,
            suffix_first=suffix_first,
        )

        formatter = CodeLlamaPromptFormatter(
            always_fim_style=True, suffix_first=suffix_first
        )
        actual_tokens, _ = formatter.prepare_prompt(model_input)
        self.assertEqual(expected_tokens, actual_tokens)

    def test_chat(self):
        """Test the chat prompt."""
        model_input = get_example_dialog_01()
        formatter = CodeLlamaChatFormatter()

        reference_tokenizer = self._get_reference_tokenizer()
        expected_tokens = prepare_dialog_prompt(
            reference_tokenizer,
            [llama_generation.Message(role="user", content=model_input.prefix)],
        )

        actual_tokens, _ = formatter.prepare_prompt(model_input)
        self.assertEqual(expected_tokens, actual_tokens)

    @parameterized.expand([(1, True), (1, False), (5, True), (5, False)])
    def test_multi_turn_chat(self, num_turns, with_system_prompt):
        """Test the chat prompt."""
        formatter = CodeLlamaChatFormatter()
        reference_tokenizer = self._get_reference_tokenizer()

        if with_system_prompt:
            system_prompt = "this is the system prompt"
            llama_dialog = [
                llama_generation.Message(role="system", content=system_prompt)
            ]
        else:
            system_prompt = None
            llama_dialog = []

        for i in range(num_turns):
            llama_dialog.append(
                llama_generation.Message(role="user", content=get_chat_question(i))
            )
            if i < num_turns - 1:
                llama_dialog.append(
                    llama_generation.Message(
                        role="assistant", content=get_chat_answer(i)
                    )
                )

        expected_tokens = prepare_dialog_prompt(reference_tokenizer, llama_dialog)

        messages = []
        for i in range(num_turns):
            messages.append(get_chat_question(i))
            if i < num_turns - 1:
                messages.append(get_chat_answer(i))
        our_dialog = Dialog(messages=messages, system_prompt=system_prompt)

        actual_tokens = formatter.prepare_chat_prompt(our_dialog)
        self.assertEqual(expected_tokens, actual_tokens)

    def test_chat_with_step_token(self):
        """Applies to models with step token like Code LLaMA 70B Instruct.

        Example from: https://huggingface.co/codellama/CodeLlama-70b-Instruct-hf
        """
        formatter = CodeLlamaChatFormatter(model_with_step_token=True)

        dialog = Dialog(
            messages=[
                "First user query",
                "Model response to first query",
                "Second user query",
            ],
            system_prompt="System prompt    ",
        )

        prompt = formatter.prepare_chat_prompt(dialog)
        expected_prompt = "<s>Source: system\n\n System prompt <step> Source: user\n\n First user query <step> Source: assistant\n\n Model response to first query <step> Source: user\n\n Second user query <step> Source: assistant\nDestination: user\n\n "
        assert formatter.tokenizer.detokenize(prompt) == expected_prompt


class TestWizardCoder(unittest.TestCase):
    """Test WizardCoder prompt formatting."""

    def test_single_turn_chat(self):
        formatter = WizardCoderChatFormatter()

        # Without system prompt
        expected = """\
Below is an instruction that describes a task. Write a response that appropriately completes the request.

### Instruction:

Write a script that prints itself

### Response:
"""

        dialog = Dialog(messages=["Write a script that prints itself"])
        actual_tokens = formatter.prepare_chat_prompt(dialog)
        actual = formatter.tokenizer.detokenize(actual_tokens)
        assert expected == actual, f"\n*** expected:\n{expected}\n*** actual:\n{actual}"

        # With system prompt
        expected2 = """\
Perform the following task.

### Instruction:

Write a script that prints itself

### Response:
"""

        dialog2 = Dialog(
            system_prompt="Perform the following task.",
            messages=["Write a script that prints itself"],
        )
        actual_tokens2 = formatter.prepare_chat_prompt(dialog2)
        actual2 = formatter.tokenizer.detokenize(actual_tokens2)
        assert expected2 == actual2

    def test_multi_turn_chat(self):
        formatter = WizardCoderChatFormatter()

        dialog = Dialog(
            messages=[
                "Write a script that prints itself",
                "There you go: print(self)",
                "Now in bash.",
            ]
        )

        expected = """\
Below is an instruction that describes a task. Write a response that appropriately completes the request.

### Instruction:

Write a script that prints itself

There you go: print(self)

Now in bash.

### Response:
"""

        actual_tokens = formatter.prepare_chat_prompt(dialog)
        actual = formatter.tokenizer.detokenize(actual_tokens)
        assert expected == actual

    def test_custom_turn_separators(self):
        """Test dialog turn separators."""
        formatter = WizardCoderChatFormatter(
            question_prefix="\n\nUser: ",
            answer_prefix="\n\nAssistant: ",
        )

        dialog = Dialog(
            messages=[
                "Write a script that prints itself",
                "There you go: print(self)",
                "Now in bash.",
            ]
        )

        expected = """\
Below is an instruction that describes a task. Write a response that appropriately completes the request.

### Instruction:

User: Write a script that prints itself

Assistant: There you go: print(self)

User: Now in bash.

### Response:
"""

        actual_tokens = formatter.prepare_chat_prompt(dialog)
        actual = formatter.tokenizer.detokenize(actual_tokens)
        assert expected == actual


class TestDeepSeekPromptFormatter(unittest.TestCase):
    """Test the family of DeepSeek prompt formatter."""

    def test_get_example_01_fim(self):
        formatter = DeepSeekCoderBaseFormatter(always_fim_style=True)
        actual_token_ids, _ = formatter.prepare_prompt(get_example_01())
        actual = formatter.tokenizer.detokenize(actual_token_ids)
        expected = '''<｜begin▁of▁sentence｜><｜fim▁begin｜>def remove_non_ascii(s: str) -> str:
    """ <｜fim▁hole｜>
    return result<｜fim▁end｜>'''
        assert expected == actual

    def test_get_example_02_fim(self):
        formatter = DeepSeekCoderBaseFormatter(always_fim_style=True)
        actual_token_ids, _ = formatter.prepare_prompt(get_example_02())
        actual = formatter.tokenizer.detokenize(actual_token_ids)
        expected = """<｜begin▁of▁sentence｜><｜fim▁begin｜>def quick_sort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[0]
    left = []
    right = []
<｜fim▁hole｜>
        if arr[i] < pivot:
            left.append(arr[i])
        else:
            right.append(arr[i])
    return quick_sort(left) + [pivot] + quick_sort(right)<｜fim▁end｜>"""

        assert expected == actual

    def test_get_example_01_nofim(self):
        formatter = DeepSeekCoderBaseFormatter(
            always_fim_style=False, max_suffix_tokens=0
        )
        actual_token_ids, _ = formatter.prepare_prompt(get_example_01())
        actual = formatter.tokenizer.detokenize(actual_token_ids)
        expected = '''<｜begin▁of▁sentence｜>def remove_non_ascii(s: str) -> str:
    """ '''
        assert expected == actual

    def test_get_example_02_nofim(self):
        formatter = DeepSeekCoderBaseFormatter(
            always_fim_style=False, max_suffix_tokens=0
        )
        actual_token_ids, _ = formatter.prepare_prompt(get_example_02())
        actual = formatter.tokenizer.detokenize(actual_token_ids)
        expected = """<｜begin▁of▁sentence｜>def quick_sort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[0]
    left = []
    right = []
"""
        assert expected == actual

    def test_instruct_01(self):
        formatter = DeepSeekCoderInstructFormatter()
        actual_token_ids, _ = formatter.prepare_prompt(get_example_dialog_01())
        actual = formatter.tokenizer.detokenize(actual_token_ids)
        expected = """<｜begin▁of▁sentence｜>You are an AI programming assistant, and you only answer questions related to computer science. For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.
### Instruction:
In Bash, how do I list all text files in the current directory (excluding subdirectories) that have been modified in the last month?
### Response:
"""
        assert expected == actual

    def test_llm_chat_01(self):
        formatter = DeepSeekLLMChatFormatter()
        actual_token_ids, _ = formatter.prepare_prompt(get_example_dialog_01())
        actual = formatter.tokenizer.detokenize(actual_token_ids)
        expected = """<｜begin▁of▁sentence｜>User: In Bash, how do I list all text files in the current directory (excluding subdirectories) that have been modified in the last month?

Assistant:"""
        assert expected == actual
        assert actual_token_ids[0] == 100000

    def test_llm_chat_02(self):
        formatter = DeepSeekLLMChatFormatter()
        messages = [
            get_chat_question(0),
            get_chat_answer(0),
            get_chat_question(1),
        ]
        our_dialog = Dialog(messages=messages)
        actual_token_ids, _ = formatter.prepare_prompt(
            ModelInput(extra={"dialog": our_dialog})
        )
        actual = formatter.tokenizer.detokenize(actual_token_ids)
        expected = """<｜begin▁of▁sentence｜>User: this is question 0

Assistant: this is answer 0<｜end▁of▁sentence｜>User: this is question 1

Assistant:"""
        assert expected == actual


class TestCodeEditTemplateBasedPromptFormatter(unittest.TestCase):
    """Test the family of CodeEditTemplateBasedPromptFormatter."""

    def test_format(self):
        """Test a simple example."""

        template = """Basic template:
instruction={instruction}
prefix={prefix}
selected_code={selected_code}
suffix={suffix}"""

        example = ModelInput(
            prefix=r'''def remove_non_ascii(s: str) -> str:
    """ ''',
            suffix=r"""
    return s""",
            extra={
                "instruction": "add comments",
                "selected_code": "s = s.replace(r'[^\\x00-\\x7F]', '')",
            },
        )

        expected_tokens = template.format(
            instruction=example.extra["instruction"],
            prefix=example.prefix,
            selected_code=example.extra["selected_code"],
            suffix=example.suffix,
        )

        # Test formatter with specified `template`.
        formatter = CodeEditTemplateBasedPromptFormatter(
            template=template, tokenizer_name="deepseekcoderinstructtokenizer"
        )
        actual_token_ids, _ = formatter.prepare_prompt(example)
        actual_tokens = formatter.tokenizer.detokenize(actual_token_ids)
        assert actual_tokens == expected_tokens

        # Test formatter with specified `template_file`.
        with tempfile.NamedTemporaryFile(mode="w+t", delete=False) as temp_file:
            temp_file.write(template)
            temp_file_name = temp_file.name

        formatter = CodeEditTemplateBasedPromptFormatter(
            template_file=temp_file_name,
            tokenizer_name="deepseekcoderinstructtokenizer",
        )

        actual_token_ids, _ = formatter.prepare_prompt(example)
        actual_tokens = formatter.tokenizer.detokenize(actual_token_ids)
        assert actual_tokens == expected_tokens

    def test_format_ignore_context(self):
        """Test a simple example."""

        template = """Basic template:
instruction={instruction}
selected_code={selected_code}"""

        example = ModelInput(
            prefix=r'''def remove_non_ascii(s: str) -> str:
    """ ''',
            suffix=r"""
    return s""",
            extra={
                "instruction": "add comments",
                "selected_code": "s = s.replace(r'[^\\x00-\\x7F]', '')",
            },
        )

        expected_tokens = template.format(
            instruction=example.extra["instruction"],
            selected_code=example.extra["selected_code"],
        )

        # Test formatter with specified `template`.
        formatter = CodeEditTemplateBasedPromptFormatter(
            template=template, tokenizer_name="deepseekcoderinstructtokenizer"
        )
        actual_token_ids, _ = formatter.prepare_prompt(example)
        actual_tokens = formatter.tokenizer.detokenize(actual_token_ids)
        assert actual_tokens == expected_tokens

"""A centralized place to collect tokenizers for research.

In future, all tokenizer should be base.tokenizers.tokenizer.Tokenizer.
We should gradually migrate to Tokenizer's interface instead of the megatron one.
"""

from megatron.tokenizer.tokenizer import (
    CodeGenTokenizer,
    CodestralTokenizer,
    DeepSeekCoderBaseTokenizer,
    DeepSeekCoderInstructTokenizer,
    DeepSeek<PERSON>MChatTokenizer,
    LLama3BaseTokenizer,
    LLama3InstructTokenizer,
    StarCoder2Tokenizer,
    StarCoderTokenizer,
)

from base.tokenizers import (
    Tokenizer,
)
from base.tokenizers import (
    create_tokenizer_by_name as create_prod_tokenizer_by_name,
)
from base.tokenizers import (
    list_tokenizers as list_prod_tokenizers,
)

# NOTE(Xuanyi): I did not include everything in megatron.tokenizer.tokenizer, as some tokenizers are never used in our codebase now.
# In general, we should try to not use these tokenizers if they have a production counterpart.

# fmt: off
MEGATRON_TOKENIZER = {}
MEGATRON_TOKENIZER["CodeGenTokenizer".lower()] = CodeGenTokenizer
MEGATRON_TOKENIZER["CodestralTokenizer".lower()] = CodestralTokenizer
MEGATRON_TOKENIZER["StarCoderTokenizer".lower()] = StarCoderTokenizer
MEGATRON_TOKENIZER["StarCoder2Tokenizer".lower()] = StarCoder2Tokenizer
MEGATRON_TOKENIZER["DeepSeekCoderBaseTokenizer".lower()] = DeepSeekCoderBaseTokenizer
MEGATRON_TOKENIZER["DeepSeekCoderInstructTokenizer".lower()] = DeepSeekCoderInstructTokenizer
MEGATRON_TOKENIZER["DeepSeekLLMChatTokenizer".lower()] = DeepSeekLLMChatTokenizer
MEGATRON_TOKENIZER["LLama3BaseTokenizer".lower()] = LLama3BaseTokenizer
MEGATRON_TOKENIZER["LLama3InstructTokenizer".lower()] = LLama3InstructTokenizer
# fmt: on


def get_tokenizer(name: str) -> Tokenizer:
    """Get a tokenizer by its *registered name*."""
    if name in list_prod_tokenizers():  # production tokenizer
        return create_prod_tokenizer_by_name(name)
    if name.lower() in MEGATRON_TOKENIZER:
        tokenizer_cls = MEGATRON_TOKENIZER[name.lower()]
        tokenizer = tokenizer_cls()
        return tokenizer
    else:
        raise ValueError(
            f"Invalid tokenizer: {name}.\n"
            f"Valid production tokenizer names: {list_prod_tokenizers()}.\n"
            f"Valid research tokenizer names: {list(MEGATRON_TOKENIZER.keys())}."
        )


def get_all_special_tokens(tokenizer: Tokenizer) -> list[str]:
    """Get all special tokens for a tokenizer.

    NOTE(Xuanyi): This function is incorrect w.r.t. its name! It returns all vocab keys of the form '<...>'  as
    the behavior of our AbstractTokenizer.all_special_tokens() to make things competiable.
    Currently, I believe this is the only used for visualization and debugging purpose -> so that we should be good.
    TODO: we should fix its returned values to the correct special tokens.
    """
    return [
        k.decode()
        for k in tokenizer.vocab.keys()
        if k.startswith(b"<") and k.endswith(b">")
    ]

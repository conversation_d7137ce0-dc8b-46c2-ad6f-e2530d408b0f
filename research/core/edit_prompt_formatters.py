"""Different kinds of code edit prompt formatter."""

import logging
import typing

from base.prompt_format_edit import (
    DroidEditPromptFormatter as DroidProdEditPromptFormatter,
)
from base.prompt_format_edit import prompt_formatter
from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer
from research.core import prod_adapters
from research.core.abstract_prompt_formatter import register_prompt_formatter

logger = logging.getLogger(__name__)


@register_prompt_formatter("droid")
class DroidEditPromptFormatter(prod_adapters.ProdEditPromptFormatterAdapter):
    """The Droid code edit prompt formatter, a wrapper of the production one."""

    prod_prompt_formatter_factory: typing.Callable[
        [
            DeepSeekCoderInstructTokenizer,
            prompt_formatter.EditTokenApportionment,
        ],
        DroidProdEditPromptFormatter,
    ] = DroidProdEditPromptFormatter

    def create_default_tokenizer(self) -> DeepSeekCoderInstructTokenizer:
        """A function to create the default tokenizer."""
        return DeepSeekCoderInstructTokenizer()

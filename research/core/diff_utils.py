"""Utilities for dealing with diffs on repositories."""

import copy
import re
import subprocess
import tempfile
from dataclasses import dataclass
from pathlib import Path
from typing import Callable, Iterable, Optional, Sequence

import unidiff
import unidiff.constants
from unidiff import PatchedFile, PatchSet, UnidiffParseError
from unidiff.patch import Hunk

from base.diff_utils.diff_utils import File
from base.ranges.range_types import LineRange
from research.core.types import check_not_none
from research.core.utils_for_file import read_file_plain, write_file_plain

# Monkey-patch the unidiff regexp so they better handle paths with spaces in them.
#
# Motivation:
# As-is, unidiff cannot handle patches generated by git-diff that look like this:
#
#    diff --git a/name has spaces.txt a/name has spaces.txt
#    deleted file mode 100644
#    index e69de29..0000000
#    diff --git b/renamed file name.txt b/renamed file name.txt
#    new file mode 100644
#    index 0000000..7898192
#    --- /dev/null
#    +++ b/renamed file name.txt
#    @@ -0,0 +1 @@
#    +a
#
# It incorrectly parses the "diff --git" lines, extracting the wrong path from
# them, and then gets confused when it gets to the "+++" line and crashes
# because the paths don't match.
#
# Solution:
# This new regexp makes the assumption that there are no spaces in the extension
# of the filename. This is then used to figure out which is the first and which
# is the second file.
#
# The same logic is applied to the case with a/, b/ prefixes, and to the case
# without these prefixes.
#
unidiff.patch.RE_DIFF_GIT_HEADER = re.compile(  # type: ignore
    r"^diff --git (?P<source>a/[^\t\n\.]+\.?\S*) (?P<target>b/[^\t\n\.]+\.?\S*)"
)
unidiff.patch.RE_DIFF_GIT_HEADER_NO_PREFIX = re.compile(  # type: ignore
    r"^diff --git (?P<source>[^\t\n\.]+\.?\S*) (?P<target>[^\t\n\.]+\.?\S*)"
)

_BEFORE_PATH = "a"
_AFTER_PATH = "b"
_PATCH_INFO_REWRITES = {
    "rename from a/": "rename from ",
    "rename to b/": "rename to ",
}


@dataclass(frozen=True)
class Repository:
    """A snapshot of a repository."""

    files: Sequence[File]
    """The files in the repository."""

    def save(self, path: Path):
        """Save the repository to the given path."""
        path.mkdir(parents=True, exist_ok=True)
        for file in self.files:
            full_file_path = path / file.path
            full_file_path.parent.mkdir(parents=True, exist_ok=True)
            write_file_plain(full_file_path, file.contents)

    @staticmethod
    def load(path: Path) -> "Repository":
        """Load a repository from the given path."""
        files = []
        for file_path in path.rglob("*"):
            if not file_path.is_file():
                continue
            try:
                files.append(
                    File(
                        str(file_path.relative_to(path)),
                        read_file_plain(file_path),
                    )
                )
            except UnicodeDecodeError:
                pass  # skip non-utf8 files
        return Repository(files=files)

    def __eq__(self, other):
        if len(self.files) != len(other.files):
            return False

        def sorted_by_path(files: Sequence):
            return sorted(files, key=lambda file: file.path)

        for this_file, that_file in zip(
            sorted_by_path(self.files), sorted_by_path(other.files)
        ):
            if this_file.path != that_file.path:
                return False
            if this_file.contents != that_file.contents:
                return False

        return True


ShouldKeepFn = Callable[[PatchedFile, Optional[Hunk]], bool]
"""A function that takes a PatchedFile and optional Hunk and returns True if the
object (file or hunk) should be kept in a subset.

The hunk will be None if the file has no hunks. In that case, this function
should determine whether the whole file change should be kept. This happens for
example if the file was renamed (in which case file.is_rename is True).

Args:
    patched_file: The PatchedFile to consider.
    hunk: The Hunk to consider, if it exists.
"""


class CommandFailedError(Exception):
    """Raised when a command fails."""

    pass


def _get_command_output(
    cmd: str, input_text: Optional[str] = None, check: bool = True
) -> tuple[str, int]:
    """Execute the given command and return the output.

    If the command fails and check=True, raises a ValueError.

    Returns: The output of the command and the return code.
    """

    try:
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            check=check,
            input=input_text.encode("utf-8") if isinstance(input_text, str) else None,
            timeout=30,
        )
        return result.stdout.decode("utf-8"), result.returncode
    except subprocess.CalledProcessError as exc:

        def _truncate(s: str, num_lines: int = 10) -> str:
            lines = s.splitlines(keepends=True)
            truncated = "".join(lines[:num_lines])
            if len(lines) > num_lines:
                truncated += "...\n"
            return truncated

        stdout_head = _truncate(exc.stdout.decode("utf-8"))
        stderr_head = _truncate(exc.stderr.decode("utf-8"))
        raise CommandFailedError(
            f"Command '{cmd}' failed:\nstdout:\n{stdout_head}\nstderr:\n{stderr_head}"
        ) from exc


def verify_patch_set(patch_set: PatchSet):
    """Check that the given PatchSet is valid.

    Valid PatchSet can be generated with parse_git_diff_output().

    Raises ValueError if the PatchSet is invalid.
    """
    message_suffix = "Please create PatchSets using parse_git_diff_output()."

    for patched_file in patch_set:
        if patched_file.source_timestamp is not None:
            raise ValueError("PatchSet has a source_timestamp. " + message_suffix)
        if patched_file.target_timestamp is not None:
            raise ValueError("PatchSet has a target_timestamp. " + message_suffix)
        for line in check_not_none(patched_file.patch_info):
            for from_str in _PATCH_INFO_REWRITES:
                if line.startswith(from_str):
                    raise ValueError(
                        f"PatchSet has an invalid rename line '{line}'. "
                        + message_suffix
                    )


def _get_sub_repos_for_diff(
    repo_before: Repository, repo_after: Repository
) -> tuple[Repository, Repository]:
    """Pare down the repositories to only contain the files that are modified.

    Returns:
        A tuple of the before and after repositories, with only the files that
        are modified.
    """
    before_files = set(repo_before.files)
    after_files = set(repo_after.files)
    common_files = before_files.intersection(after_files)
    before_files.difference_update(common_files)
    after_files.difference_update(common_files)
    return (
        Repository(list(before_files)),
        Repository(list(after_files)),
    )


def parse_git_diff_output(diff: str, remove_timestamps: bool = False) -> PatchSet:
    """Load a PatchSet from the output of git diff and clean it up.

    The result can be used with the apply_diff() function. Loading the PatchSet
    directly while avoiding this cleanup is not recommended, because of some
    idiosyncracies in how git diff presents file renames.

    Args:
        diff: The output of git diff (with default settings).
        remove_timestamps: If True, remove the timestamps from each file.
            This is useful for unit testing.

    Returns:
        A PatchSet which represents the diff and can be used with apply_diff().
    """

    # Fix the rename lines to remove the a/ b/ prefixes from them, because
    # git diff --no-prefix does not do this.
    # See here: https://stackoverflow.com/questions/62858586/does-git-apply-p-leading-slash-removal-not-work-for-renames
    def fix_patch_info_line(line: str):
        for from_str, to_str in _PATCH_INFO_REWRITES.items():
            if line.startswith(from_str):
                line = to_str + line[len(from_str) :]
        return line

    diff_lines = diff.splitlines(keepends=True)
    diff_lines = [fix_patch_info_line(line) for line in diff_lines]
    try:
        patch_set = PatchSet("".join(diff_lines))
    except UnidiffParseError as e:
        raise UnidiffParseError(
            f"{e}\nFailed to parse git diff output (shown below):\n{diff}"
        ) from e

    if remove_timestamps:
        for patched_file in patch_set:
            patched_file.source_timestamp = None
            patched_file.target_timestamp = None

    return patch_set


# TODO(guy) combine this with diff_files
def compute_single_file_diff(
    file_before: File, file_after: File, num_context_lines: int = 3
) -> PatchSet:
    """Compute the diff between two files, returning the diff as a PatchSet.

    Args:
        file_before: The file before the change.
        file_after: The file after the change.
        num_context_lines: Number of context lines to include in a hunk. Hunks
            whose context lines overlap get merged.
    """
    diff = compute_repo_diff(
        Repository([file_before]), Repository([file_after]), num_context_lines
    )
    return diff


def compute_repo_diff_str(
    repo_before: Repository,
    repo_after: Repository,
    num_context_lines: int = 3,
    ignore_whitespace: bool = False,
) -> str:
    """Compute the diff between two repositories, returning the diff string.

    The returned diff contains BEFORE_PATH and AFTER_PATH as path prefixes for
    each file.

    Args:
        repo_before: The repository before the change.
        repo_after: The repository after the change.
        num_context_lines: Number of context lines to include in a hunk. Hunks
            whose context lines overlap get merged.
    """
    with tempfile.TemporaryDirectory() as tmp_dir:
        before_path = Path(tmp_dir) / _BEFORE_PATH
        after_path = Path(tmp_dir) / _AFTER_PATH
        before_path.mkdir()
        after_path.mkdir()

        sub_repo_before, sub_repo_after = _get_sub_repos_for_diff(
            repo_before, repo_after
        )

        sub_repo_before.save(before_path)
        sub_repo_after.save(after_path)

        diff_cmd = (
            f"git diff --no-index --no-color --no-prefix "
            f"-U{num_context_lines} "
            f"{'--ignore-all-space' if ignore_whitespace else ''} "
            f"{_BEFORE_PATH}/ {_AFTER_PATH}/"
        )
        diff, rc = _get_command_output(
            f"cd {tmp_dir} && {diff_cmd}",
            check=False,
        )

        # for diff, 0 means no changes, 1 means changes, and other code means actual error
        if rc not in (0, 1):
            raise ValueError(f"Command '{diff_cmd}' failed with return code {rc}")

        return diff


def compute_repo_diff(
    repo_before: Repository,
    repo_after: Repository,
    num_context_lines: int = 3,
    ignore_whitespace: bool = False,
) -> PatchSet:
    """Compute the diff between two repositories, returning the diff as a PatchSet.

    The returned diff contains BEFORE_PATH and AFTER_PATH as path prefixes for
    each file.

    Args:
        repo_before: The repository before the change.
        repo_after: The repository after the change.
        num_context_lines: Number of context lines to include in a hunk. Hunks
            whose context lines overlap get merged.
        ignore_whitespace: Whether to ignore whitespace changes.
    """
    diff = compute_repo_diff_str(
        repo_before, repo_after, num_context_lines, ignore_whitespace
    )
    patch_set = parse_git_diff_output(diff)
    return patch_set


# TODO(guy) maybe optimize this so it only saves/loads the files that the patch affects
def apply_diff(
    repo: Repository, diff: PatchSet, strip_path_elems: int = 1, reverse: bool = False
) -> Repository:
    """Apply the diff to a repository, returning the patched repository.

    Args:
        repo: Repository to apply the patch to
        diff: The patch to apply
        strip_path_elems: Number of path elements to strip from the diff.
            The default value is compatible with the diff produced by
            compute_repo_diff.
        reverse: Whether to reverse or "undo" the diff.
    """
    verify_patch_set(diff)
    with tempfile.TemporaryDirectory() as tmp_dir:
        tmp_path = Path(tmp_dir)
        repo.save(tmp_path)

        if reverse:
            reverse_flag = "--reverse"
        else:
            reverse_flag = ""

        # Why --unidiff-zero? git-apply has safeguards for applying diffs correctly,
        # which fail when there are zero-context patches. --unidiff-zero disables
        # these safeguards.
        _get_command_output(
            f"cd {tmp_dir} && git apply --allow-empty --unidiff-zero -p{strip_path_elems} {reverse_flag} -",
            input_text=str(diff),
        )

        # Documenting an alternative way to apply the patch, in case git-apply
        # causes problems. This doesn't have full support for renames, but it
        # may work better for normal diffs. For example, it doesn't require a
        # special --unidiff-zero flag to handle zero-context patches.
        #
        # This can be used instead of git-apply, or as a fallback if git-apply
        # fails.
        #
        #     _get_command_output(
        #         f"cd {tmp_dir} && patch -p{strip_path_elems} --fuzz 0 {reverse_flag}",
        #         input_text=str(diff),
        #     )

        return Repository.load(tmp_path)


def diff_subset(diff: PatchSet, should_keep_fn: ShouldKeepFn) -> PatchSet:
    """Compute a subset of the given diff.

    Args:
        diff: The diff to select from.
        should_keep_hunk_fn: Called for each Hunk. If it returns True, the hunk is kept.

    Returns:
        The selected subset of the diff.
    """
    verify_patch_set(diff)

    # clone the input diff so we can change its properties without affecting
    # the original
    candidate_diff = copy.deepcopy(diff)
    result_diff = PatchSet("")

    # keep chunks according to should_keep_hunk_fn
    for orig_patched_file, candidate_patched_file in zip(diff, candidate_diff):
        delta_target_lines = 0
        num_removed = 0
        if len(orig_patched_file) == 0:
            if should_keep_fn(orig_patched_file, None):
                result_diff.append(candidate_patched_file)
        else:
            for i, hunk in enumerate(orig_patched_file):
                hunk_idx_in_patched_file = i - num_removed
                if should_keep_fn(orig_patched_file, hunk):
                    candidate_patched_file[
                        hunk_idx_in_patched_file
                    ].target_start += delta_target_lines
                else:
                    candidate_patched_file.pop(hunk_idx_in_patched_file)
                    num_removed += 1
                    delta_target_lines -= hunk.added - hunk.removed
            if len(candidate_patched_file) > 0:
                result_diff.append(candidate_patched_file)

    return result_diff


def get_modified_ranges(pfile: PatchedFile) -> Iterable[LineRange]:
    """Compute the modified lines in the after version of the file.

    NOTE: This function reports deleted lines as single line changes while other
    functions decide to report them as zero-length changes. You have been warned.

    E.g.,

    @@ -1,5 +1,2 @@
    0
    -1
    -2
    -3
    4

    would be returned as [LineRange(1, 2)].
    """
    modified_lines = set[int]()
    for hunk in pfile:
        last_line = 0
        for line in hunk:
            if line.is_added:
                # We use an appending semantics for added line, while PachedFile uses a
                # prepending semantics for added line, so we need to offset the
                # reported location by -1.
                last_line = check_not_none(line.target_line_no)
                modified_lines.add(last_line - 1)
            elif line.is_removed:
                # Mark the "next" line as the modified one.
                modified_lines.add(last_line)
            # target_line_no can be None if the trailing new line is removed.
            elif line.target_line_no is not None:
                last_line = check_not_none(line.target_line_no)
    # Merge them into line ranges.
    ranges = [LineRange(line, line + 1) for line in sorted(modified_lines)]
    result_ranges = list[LineRange]()
    for r in ranges:
        if result_ranges and result_ranges[-1].adjoins(r):
            result_ranges[-1] = result_ranges[-1].merge(r)
        else:
            result_ranges.append(r)
    return result_ranges


def get_source_path(pfile: PatchedFile) -> str | None:
    """Returns the path of the source file of the given diff file if it exists.

    Needed because the unidiff library returns the target path for renamed files.
    """
    # NOTE(arun): PatchedFile.is_added_file does not work with -U0 diffs.
    if pfile.source_file == unidiff.constants.DEV_NULL:
        return None
    path = pfile.source_file
    if path.startswith("a/"):
        path = path[2:]
    return path


def get_target_path(pfile: PatchedFile) -> str | None:
    """Returns the path of the target file of the given diff file if it exists.

    Here for consistency with `get_source_path`.
    """
    # NOTE(arun): PatchedFile.is_removed_file does not work with -U0 diffs.
    if pfile.target_file is None or pfile.target_file == unidiff.constants.DEV_NULL:
        return None
    path = pfile.target_file
    if path.startswith("b/"):
        path = path[2:]
    return path

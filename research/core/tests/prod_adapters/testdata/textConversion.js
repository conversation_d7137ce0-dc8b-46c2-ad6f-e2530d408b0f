// MIT License
//
// Copyright (c) 2017 沐霖
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in
// all
// copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
// SOFTWARE.
//简体翻译
function charjt(){
return '啊阿埃挨哎唉哀皑癌蔼矮艾碍爱隘鞍氨安俺按';
}
//繁体翻译
function charft()
{
return '啊阿埃挨哎唉哀皚癌藹矮艾礙愛隘鞍氨安俺';
}
//火星文翻译
function charhx()
{
return '娿婀埃挨餀呃哀皑癌蔼婑銰碍嬡隘鞍氨鮟';
}

//繁体字转换
var toCht = function(str){
  var rs='';
  for(var i=0;i<str.length;i++){
    if(charjt().indexOf(str.charAt(i))!=-1)
      rs+=charft().charAt(charjt().indexOf(str.charAt(i)));
    else if(charhx().indexOf(str.charAt(i))!=-1)
      rs+=charft().charAt(charhx().indexOf(str.charAt(i)));
    else
      rs+=str.charAt(i);
  }
  return rs;
}

// 简体字转换
var toZh = function(str){
  var rs='';
  for(var i=0;i<str.length;i++){
    if(charft().indexOf(str.charAt(i))!=-1)
      rs+=charjt().charAt(charft().indexOf(str.charAt(i)));
    else if(charhx().indexOf(str.charAt(i))!=-1)
      rs+=charjt().charAt(charhx().indexOf(str.charAt(i)));
    else
      rs+=str.charAt(i);
  }
  return rs;
}

// 火星文转换
var toHx = function(str){
  var rs='';
  for(var i=0;i<str.length;i++){
    if(charjt().indexOf(str.charAt(i))!=-1)
      rs+=charhx().charAt(charjt().indexOf(str.charAt(i)));
    else if(charft().indexOf(str.charAt(i))!=-1)
      rs+=charhx().charAt(charft().indexOf(str.charAt(i)));
    else
      rs+=str.charAt(i);
  }
  return rs;
}

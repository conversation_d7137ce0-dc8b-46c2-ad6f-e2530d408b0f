"""Tests for prod vs research tokenizers.

pytest research/core/tests/prod_adapters/test_tokenization.py
"""

import pathlib

import pytest
from megatron.tokenizer import get_tokenizer as create_research_tokenizer

from base.tokenizers import create_tokenizer_by_name as create_prod_tokenizer

TEST_DATA_ROOT = pathlib.Path(__file__).parent / "testdata"

RESEARCH_PROD_NAME_PAIRS = [
    ("DeepSeekCoderBaseTokenizer", "deepseek_coder_base"),
    ("DeepSeekCoderInstructTokenizer", "deepseek_coder_instruct"),
    ("StarCoderTokenizer", "starcoder"),
    ("CodeGenTokenizer", "codegen"),
]
"""Pairs of research tokenizer and corresponding production tokenizer names."""


@pytest.mark.parametrize(
    "expression",
    [
        # Simple expression with whitespace
        "if a == 1:\n    print(a)\nelse:\n\t\tprint(a)\n",
        # More complex expression with multiple indentation levels.
        """\
def foo():
    if a == 1:
        print(a, arg ="foo")
    return a

""",
        # Target edge-cases for the DeepSeek regex pattern by combining whitespace and
        # non-alphanumeric characters.
        "  \n~()()  \n\nfoo",
        # Make sure we parse numbers correctly.
        "11111111111",
    ],
)
@pytest.mark.parametrize(
    "tokenizer_name_research,tokenizer_name_prod", RESEARCH_PROD_NAME_PAIRS
)
def test_tokenizer_equivalence(
    tokenizer_name_research: str, tokenizer_name_prod: str, expression: str
):
    """Test that research and production tokenizers produce equivalent tokens."""
    tokenizer_research = create_research_tokenizer(tokenizer_name_research)
    tokenizer_prod = create_prod_tokenizer(tokenizer_name_prod)

    tokens_research = tokenizer_research.tokenize(expression)
    tokens_prod = tokenizer_prod.tokenize_safe(expression)

    assert tokens_research == tokens_prod


@pytest.mark.parametrize(
    "filename",
    [
        # Test file containing a mix of code, English and Asian characters.
        "textConversion.js",
    ],
)
@pytest.mark.parametrize(
    "tokenizer_name_research,tokenizer_name_prod", RESEARCH_PROD_NAME_PAIRS
)
def test_tokenizer_equivalence_from_file(
    tokenizer_name_research: str, tokenizer_name_prod: str, filename: str
):
    """Test that research and production tokenizers produce equivalent tokens."""
    tokenizer_research = create_research_tokenizer(tokenizer_name_research)
    tokenizer_prod = create_prod_tokenizer(tokenizer_name_prod)

    expression = (TEST_DATA_ROOT / filename).read_text(encoding="utf8")
    tokens_research = tokenizer_research.tokenize(expression)
    tokens_prod = tokenizer_prod.tokenize_safe(expression)

    assert tokens_research == tokens_prod

"""Unit tests for core/model_input.py."""

from research.core.model_input import ModelInput


class TestModelInput:
    """Test the ModelInput class."""

    def test_default_value(self):
        data = ModelInput()
        assert data.prefix == ""
        assert data.suffix == ""
        assert data.retrieved_chunks == []

    def test_visualization(self):
        """This test helps to guarantee not silently change the default values."""
        data = ModelInput(prefix="prefix\n", suffix="suffix\n")
        view = str(data)
        expected_view = r"""ModelInput(
  prefix = 'prefix\n',
  suffix = 'suffix\n',
  selected_code = '',
  retrieved_chunks = [],
  path = '',
  target = None,
  doc_ids = None,
  recency_info = None,
  chat_input = None,
  cursor_position = None,
  edit_events = None,
  extra = {}
)"""
        assert view == expected_view

    def test_save_load_basic_type(self, tmpdir):
        """Test the serialization capability for attributes with basic types."""
        data = ModelInput(prefix="123", suffix="456", target=" = 579")
        temp_path = tmpdir.join("temp.json")
        data.save_to_json(temp_path)
        loaded_data = ModelInput.load_from_json(temp_path)
        assert loaded_data == data

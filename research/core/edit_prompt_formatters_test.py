"""Test the code edit prompt formatters under core.

pytest research/core/edit_prompt_formatters_test.py
"""

import unittest

from research.core.edit_prompt_formatters import DroidEditPromptFormatter
from research.core.model_input import Chunk, Document, ModelInput


def get_example_01():
    return ModelInput(
        path="src/example.py",
        prefix="import pathlib\n",
        suffix="    print(x)\n",
        extra={
            "selected_code": r"""file = pathlib.Path("foo")
for x in file.open():
""",
            "instruction": "fix bugs",
        },
    )


def get_example_02():
    return ModelInput(
        path="src/example.py",
        prefix="import pathlib\n",
        suffix="    print(x)\n",
        retrieved_chunks=[
            Chunk(
                id="chunk1",
                text="# You can aggregate\n# with a pooling function.\n",
                parent_doc=Document(id="", text="", path="src/foo.py"),
                char_offset=93,
                length=49,
                line_offset=1,
                length_in_lines=2,
            ),
            Chunk(
                id="chunk2",
                text="# You can aggregate\n# with a maxing\n# function.\n",
                parent_doc=Document(id="", text="", path="src/bar.py"),
                char_offset=54,
                length=51,
                line_offset=5,
                length_in_lines=3,
            ),
        ],
        extra={
            "selected_code": r"""file = pathlib.Path("foo")
for x in file.open():
""",
            "instruction": "fix bugs",
        },
    )


class TestDroidPromptFormatter(unittest.TestCase):
    """Test the Droid code edit prompt formatter."""

    def test_none_retrieval(self):
        """Tests the behavior with no retrieved chunks."""
        prompter = DroidEditPromptFormatter(
            path_len=100,
            instruction_len=100,
            prefix_len=2048,
            selected_code_len=2048,
            suffix_len=2048,
            max_prompt_tokens=8192 + 10,
        )
        expected_prompt = """<｜begin▁of▁sentence｜>Path: src/example.py
Instruction: fix bugs
Prefix:
```
import pathlib

```

Suffix:
```
    print(x)

```

Selected Code:
```
file = pathlib.Path("foo")
for x in file.open():

```

Updated Code:
```
"""
        prompt, _ = prompter.prepare_prompt_text(get_example_01())
        self.assertEqual(prompt, expected_prompt)

    def test_retrieval(self):
        """Tests the behavior with retrieved chunks."""
        prompter = DroidEditPromptFormatter(
            path_len=100,
            instruction_len=100,
            prefix_len=2048,
            selected_code_len=2048,
            suffix_len=2048,
            max_prompt_tokens=8192 + 10,
        )
        expected_prompt = """<｜begin▁of▁sentence｜>Path: src/example.py
Instruction: fix bugs
See (src/bar.py):
```
# You can aggregate
# with a maxing
# function.

```
See (src/foo.py):
```
# You can aggregate
# with a pooling function.

```
Prefix:
```
import pathlib

```

Suffix:
```
    print(x)

```

Selected Code:
```
file = pathlib.Path("foo")
for x in file.open():

```

Updated Code:
```
"""
        prompt, _ = prompter.prepare_prompt_text(get_example_02())
        self.assertEqual(prompt, expected_prompt)

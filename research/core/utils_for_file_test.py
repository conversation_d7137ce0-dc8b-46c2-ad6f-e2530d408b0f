from random import Random
from research.core import utils_for_file
import pathlib
import tempfile

from research.core.testing_utils import random_str, run_property_test


def test_save_then_read_is_identity():
    """Test that saving and then reading is the identity function."""

    def test(rng: Random):
        with tempfile.TemporaryDirectory() as tmp_dir:
            tmp_path = pathlib.Path(tmp_dir) / "test.txt"
            contents = random_str(rng, max_len=100)
            utils_for_file.write_file_plain(tmp_path, contents)
            assert utils_for_file.read_file_plain(tmp_path) == contents

    run_property_test(test)

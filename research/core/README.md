# Research Core

Our high-level principles:
- Be flexible for users to explore different crazy ideas
- Be simple for users to use multiple off-the-shelf models, prompt formatters, etc.
- Collect most commonly used and shared components under this `core` folder.

## Design Detail

### Data Format
The data class (`ModelInput`) was defined at [basic_data_format.py](basic_data_format.py).
Basically, it is a dataclass with additional functions:
- `rebind(...)` for the inplace modification.
- `save_to_json(filename)` for serialization.
- `load_from_json(filename)` for deserialization. This is a classmethod.

Principle for this data class:
- Keep as minimal library dependency as possible
- Be memory efficient

### Prompt Formatter

The prompt formatter (`AbstractPromptFormatter`) is responsible to build the prompt given an input in the format of `ModelInput`.

```
PromptFormatter:
  - attributes:
      - tokenizer: Tokenizer()
      - max_prefix_tokens, etc
  - interface:
      - prepare_prompt(input: ModelInput(...)) -> <PERSON>ple[List[int], dict]:
      - prepare_prompt_text(input: ModelInput(...)) -> Tuple[str, dict]:
```

## Demo Examples

- [model_inputs.ipynb](notebooks/model_inputs.ipynb): functionality for our data class.
- [prompt-formatter.ipynb](notebooks/prompt-formatter.ipynb): how to use the prompt formatter.


## Historical Design Docs:
- https://www.notion.so/RFC-New-Research-Models-and-Research-core-841ed40b1fcb4cc2ad89f3bcf530ac4f

"""This file contains some lightweight utils for working with S3."""

from typing import Optional

import boto3

from research.core import augment_secrets

SERVICE_NAME = "s3"
S3_ENDPOINT_URL = "https://object.las1.coreweave.com"


def get_s3_client(
    access_key: Optional[str] = None,
    secret_key: Optional[str] = None,
):
    """Get a boto3 client for S3."""
    if access_key is None or secret_key is None:
        access_key, secret_key = augment_secrets.get_coreweave_keys()

    return boto3.client(
        SERVICE_NAME,
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_key,
        endpoint_url=S3_ENDPOINT_URL,
    )


def cleanup_dir(
    bucket: str,
    subdir: str,
    access_key: Optional[str] = None,
    secret_key: Optional[str] = None,
):
    """Delete all files under a directory."""
    s3 = get_s3_client(access_key, secret_key)

    ls_result = s3.list_objects_v2(Bucket=bucket, Prefix=subdir)
    if "Contents" not in ls_result:
        return
    for obj in ls_result["Contents"]:
        s3.delete_object(Bucket=bucket, Key=obj["Key"])

{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Data Format Notebook\n", "\n", "This notebook shows how to use `ModelInput` in different ways."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[34mModelInput(\n", "  prefix = '123',\n", "  suffix = ' 567',\n", "  middle = '',\n", "  retrieved_chunks = [],\n", "  path = '',\n", "  target = '',\n", "  extra = {}\n", ")\u001b[0m\n", "\n", "\n", "Inplace change its attributes:\n", "\n", "\n", "\u001b[34mModelInput(\n", "  prefix = '1',\n", "  suffix = ' 567',\n", "  middle = 'x',\n", "  retrieved_chunks = [],\n", "  path = '',\n", "  target = '',\n", "  extra = {}\n", ")\u001b[0m\n"]}], "source": ["from termcolor import colored\n", "from research.core.model_input import ModelInput\n", "\n", "# Create a simple model input\n", "data = ModelInput(prefix=\"123\", suffix=\" 567\")\n", "print(colored(data, color=\"blue\"))\n", "\n", "print(\"\\n\\n\" + \"Inplace change its attributes:\" + \"\\n\\n\")\n", "\n", "# Rebind new values to its attributes\n", "data.rebind(prefix=\"1\", middle=\"x\")\n", "print(colored(data, color=\"blue\"))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The test file path: /tmp/tmp37hnq85k/tmp89ee8b1d.json.\n", "The original object == reloaded one: True\n"]}], "source": ["# Save the object and reload object\n", "import tempfile\n", "test_dir = tempfile.mkdtemp()\n", "test_path = tempfile.mktemp(dir=test_dir, suffix=\".json\")\n", "print(f\"The test file path: {test_path}.\")\n", "\n", "data.save_to_json(test_path)\n", "reloaded_data = ModelInput.load_from_json(test_path)\n", "print(f\"The original object == reloaded one: {reloaded_data == data}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import pyglove as pg\n", "\n", "# Out-of-place change: first clone and then rebind\n", "data_01 = data.clone().rebind(prefix=\"this\", middle=\"y\")\n", "data_02 = data.clone().rebind(path=\"test.py\")\n", "\n", "# Easily inspect the difference between two objects.\n", "# print(\"Inspect the diff between two objects:\" + \"\\n\\n\")\n", "# print(colored(pg.diff(data_01, data_02), color=\"blue\"))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
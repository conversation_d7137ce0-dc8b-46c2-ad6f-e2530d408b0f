{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Prompt Formatter Notebook\n", "\n", "This notebook shows how to create and use a prompt formatter."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Create the prompt formatter"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PromptFormatterCodeGen(preamble = ,\n", "  max_prefix_tokens = -1,\n", "  max_suffix_tokens = -1,\n", "  tokenizer = <megatron.tokenizer.tokenizer.CodeGenTokenizer object at 0x7f8f041308e0>,\n", "  fill_to_context_window = False,\n", "  _max_prompt_tokens = 9223372036854775807,\n", "  separator_for_retrieved_chunks = \n", ")\n"]}], "source": ["\"\"\"Create a single CodeGen prompt formatter.\"\"\"\n", "from research.core.ui_sugar import UISugar\n", "from research.core.all_prompt_formatters import PromptFormatterCodeGen\n", "\n", "prompter = PromptFormatterCodeGen()\n", "print(prompter)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Serialization\n", "json_str = prompter.ui_to_json()\n", "print(json_str)\n", "\n", "deserialized_prompter = UISugar.ui_by_json(json_str)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "----------------------------------------------------------------------------------------------------\n", "\n", "PromptFormatterCodeGen(preamble = ,\n", "  max_prefix_tokens = -1,\n", "  max_suffix_tokens = -1,\n", "  tokenizer = <megatron.tokenizer.tokenizer.CodeGenTokenizer object at 0x7f8f04130ee0>,\n", "  fill_to_context_window = False,\n", "  _max_prompt_tokens = 9223372036854775807,\n", "  separator_for_retrieved_chunks = \n", ")\n"]}], "source": ["# Deserialization\n", "print(\"\\n\" + \"-\" * 100 + \"\\n\")\n", "print(deserialized_prompter)\n", "prompter = deserialized_prompter"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Apply the prompt formatter"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The model input:\n", "\u001b[34mModelInput(\n", "  prefix = 'def aggregate(a,b):\\n',\n", "  suffix = '',\n", "  middle = '',\n", "  retrieved_chunks = [Chunk(range=93:142, line_range=1:3, path=example_doc.py), Chunk(range=54:105, line_range=5:8, path=example_doc.py)],\n", "  path = 'src/example.py',\n", "  target = None,\n", "  extra = {}\n", ")\n", "\u001b[0m\n", "The prompt tokens:\n", "\u001b[34m[2, 921, 460, 19406, 198, 2, 351, 257, 3509, 278, 198, 2, 2163, 13, 198, 198, 2, 921, 460, 19406, 198, 2, 351, 257, 5933, 278, 2163, 13, 198, 198, 4299, 19406, 7, 64, 11, 65, 2599, 198]\n", "\u001b[0m\n", "The prompt text:\n", "\u001b[34m# You can aggregate\n", "# with a maxing\n", "# function.\n", "\n", "# You can aggregate\n", "# with a pooling function.\n", "\n", "def aggregate(a,b):\n", "\n", "\u001b[0m\n"]}], "source": ["from termcolor import colored\n", "from research.core.model_input import ModelInput\n", "from research.core.types import Chunk, Document\n", "\n", "doc = Document(id=\"doc1\", text=\"This is a fake doc.\", path=\"example_doc.py\", meta={})\n", "raw_input = ModelInput(\n", "    path=\"src/example.py\",\n", "    prefix=\"def aggregate(a,b):\\n\",\n", "    suffix=\"\\nreturn aggregated_output\\n\",\n", "    retrieved_chunks=[\n", "        Chunk(\n", "            id=\"chunk1\",\n", "            text=\"# You can aggregate\\n# with a pooling function.\\n\",\n", "            parent_doc=doc,\n", "            char_offset=93,\n", "            length=49,\n", "            line_offset=1,\n", "            length_in_lines=2,\n", "            meta={},\n", "        ),\n", "        Chunk(\n", "            id=\"chunk2\",\n", "            text=\"# You can aggregate\\n# with a maxing\\n# function.\\n\",\n", "            parent_doc=doc,\n", "            char_offset=54,\n", "            length=51,\n", "            line_offset=5,\n", "            length_in_lines=3,\n", "            meta={},\n", "        ),\n", "    ],\n", ")\n", "\n", "# Build the input data\n", "inputs = raw_input.rebind(suffix=\"\")\n", "print(f\"The model input:\")\n", "print(colored(str(inputs) + \"\\n\", \"blue\"))\n", "\n", "prompt_tokens, _ = prompter.prepare_prompt(inputs)\n", "\n", "print(f\"The prompt tokens:\")\n", "print(colored(str(prompt_tokens) + \"\\n\", \"blue\"))\n", "print(f\"The prompt text:\")\n", "prompt = prompter.tokenizer.detokenize(prompt_tokens)\n", "print(colored(str(prompt) + \"\\n\", \"blue\"))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32mPrompt Formatter Name: base\u001b[0m\n", "\u001b[34mPromptFormatterCodeGen(preamble = ,\n", "  max_prefix_tokens = -1,\n", "  max_suffix_tokens = -1,\n", "  tokenizer = <megatron.tokenizer.tokenizer.CodeGenTokenizer object at 0x7f8f04130880>,\n", "  fill_to_context_window = False,\n", "  _max_prompt_tokens = 9223372036854775807,\n", "  separator_for_retrieved_chunks = \n", ")\n", "\u001b[0m\n", "\u001b[32mPrompt Formatter Name: indiana\u001b[0m\n", "\u001b[34mPromptFormatterCodeGenIndiana(suffix_for_path = \n", ",\n", "  preamble = ,\n", "  separator_for_fim = <|fim-sep|>,\n", "  max_prefix_tokens = -1,\n", "  max_suffix_tokens = -1,\n", "  tokenizer = <megatron.tokenizer.tokenizer.CodeGenTokenizer object at 0x7f8e37ebb9a0>,\n", "  fill_to_context_window = False,\n", "  max_retrieved_chunk_tokens = -1,\n", "  _max_prompt_tokens = 9223372036854775807,\n", "  separator_for_retrieved_chunks = <|ret-endofdoc|>)\n", "\u001b[0m\n", "\u001b[32mPrompt Formatter Name: fim\u001b[0m\n", "\u001b[34mPromptFormatterCodeGenFIM(preamble = ,\n", "  separator_for_fim = <|fim-sep|>,\n", "  max_prefix_tokens = -1,\n", "  max_suffix_tokens = -1,\n", "  tokenizer = <megatron.tokenizer.tokenizer.CodeGenTokenizer object at 0x7f8c5a75e7f0>,\n", "  _max_prompt_tokens = 9223372036854775807)\n", "\u001b[0m\n", "\u001b[32mPrompt Formatter Name: starcoder\u001b[0m\n", "\u001b[34mPromptFormatterStarCoder(retrieval_layout_style = ChunkLayout.GENERIC,\n", "  preamble = ,\n", "  max_prefix_tokens = -1,\n", "  always_fim_style = False,\n", "  max_suffix_tokens = -1,\n", "  tokenizer = <megatron.tokenizer.tokenizer.StarCoderTokenizer object at 0x7f8c4a12a340>,\n", "  max_retrieved_chunk_tokens = -1,\n", "  _max_prompt_tokens = 9223372036854775807,\n", "  separator_for_retrieved_chunks = \n", ")\n", "\u001b[0m\n"]}], "source": ["\"\"\"Create multiple prompt formatter.\"\"\"\n", "from research.core.prompt_formatters import (\n", "    PromptFormatterCodeGen,\n", "    PromptFormatterCodeGenIndiana,\n", "    PromptFormatterCodeGenFIM,\n", "    PromptFormatterStarCoder,\n", ")\n", "\n", "prompt_formatters = dict(\n", "    base=PromptFormatterCodeGen(),\n", "    indiana=PromptFormatterCodeGenIndiana(),\n", "    fim=PromptFormatterCodeGenFIM(),\n", "    starcoder=PromptFormatterStarCoder(),\n", ")\n", "for name, prompter in prompt_formatters.items():\n", "    print(colored(f\"Prompt Formatter Name: {name}\", \"green\"))\n", "    print(colored(str(prompter) + \"\\n\", \"blue\"))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32mThe prompt built by StarCoder:\n", "\u001b[0m\n", "\u001b[34m# You can aggregate\n", "# with a maxing\n", "# function.\n", "\n", "# You can aggregate\n", "# with a pooling function.\n", "\n", "def aggregate(a,b):\n", "\n", "\u001b[0m\n"]}], "source": ["# Use StarCoder Prompt Formatter\n", "from termcolor import colored\n", "from megatron.tokenizer import get_tokenizer\n", "from research.core.tests.test_prompt_formatter import get_example_01\n", "from research.core.prompt_formatters import PromptFormatterStarCoder\n", "\n", "\n", "prompter = PromptFormatterStarCoder()\n", "prompt, _ = prompter.prepare_prompt_text(raw_input.clone())\n", "\n", "print(colored(\"The prompt built by StarCoder:\\n\", \"green\"))\n", "print(colored(str(prompt) + \"\\n\", \"blue\"))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
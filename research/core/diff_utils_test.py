"""Tests for diff_utils."""

import tempfile
from pathlib import Path

import pytest
from unidiff import PatchSet

from base.ranges.range_types import LineRange
from research.core.diff_utils import (
    CommandFailedError,
    File,
    Repository,
    _get_sub_repos_for_diff,
    apply_diff,
    compute_repo_diff,
    diff_subset,
    get_modified_ranges,
    parse_git_diff_output,
    verify_patch_set,
)

a_before_contents = """\
1
2
3
4
5
"""

b_before_contents = """\
foo
bar
hello
world
"""

a_after_contents = """\
1
2
A line
A line
A line
3
4
5
B line
B line
"""

b_after_contents = """\
C line
foo
bar
D line
D line
D line
world
"""

c_contents = "a\nb\nc\nd\n"


diff = """\
diff --git a/a.txt b/a.txt
index 8a1218a..6310241 100644
--- a/a.txt
+++ b/a.txt
@@ -1,5 +1,10 @@
 1
 2
+A line
+A line
+A line
 3
 4
 5
+B line
+B line
diff --git a/b.txt b/b.txt
index 6b34e8b..36aef8f 100644
--- a/b.txt
+++ b/b.txt
@@ -1,4 +1,7 @@
+C line
 foo
 bar
-hello
+D line
+D line
+D line
 world
diff --git a/c.txt b/c_renamed.txt
similarity index 100%
rename from a/c.txt
rename to b/c_renamed.txt
"""


diff_with_U1 = """\
diff --git a/a.txt b/a.txt
index 8a1218a..6310241 100644
--- a/a.txt
+++ b/a.txt
@@ -2,2 +2,5 @@
 2
+A line
+A line
+A line
 3
@@ -5,1 +8,3 @@
 5
+B line
+B line
diff --git a/b.txt b/b.txt
index 6b34e8b..36aef8f 100644
--- a/b.txt
+++ b/b.txt
@@ -1,4 +1,7 @@
+C line
 foo
 bar
-hello
+D line
+D line
+D line
 world
diff --git a/c.txt b/c_renamed.txt
similarity index 100%
rename from c.txt
rename to c_renamed.txt
"""


# A diff with incorrect context lines
bad_diff = """\
diff --git a/a.txt b/a.txt
index 8a1218a..6310241 100644
--- a/a.txt
+++ b/a.txt
@@ -1,5 +1,10 @@
 1
 2 BAD
+A line
+A line
+A line
 3
 4
 5
+B line
+B line
diff --git a/b.txt b/b.txt
index 6b34e8b..36aef8f 100644
--- a/b.txt
+++ b/b.txt
@@ -1,4 +1,7 @@
+C line
 foo
 bar
-hello
+D line
+D line
+D line
 world
diff --git a/c.txt b/c_renamed.txt
similarity index 100%
rename from a/c.txt
rename to b/c_renamed.txt
"""


before_repo = Repository(
    files=[
        File(path="a.txt", contents=a_before_contents),
        File(path="b.txt", contents=b_before_contents),
        File(path="c.txt", contents=c_contents),
    ]
)

after_repo = Repository(
    files=[
        File(path="a.txt", contents=a_after_contents),
        File(path="b.txt", contents=b_after_contents),
        File(path="c_renamed.txt", contents=c_contents),
    ]
)


def test_compute_repo_diff():
    patch_set = compute_repo_diff(before_repo, after_repo)
    assert str(patch_set) == str(parse_git_diff_output(diff, remove_timestamps=True))


def test_compute_repo_diff_with_U1():
    patch_set = compute_repo_diff(before_repo, after_repo, num_context_lines=1)
    assert str(patch_set) == str(
        parse_git_diff_output(diff_with_U1, remove_timestamps=True)
    )


def test_compute_repo_diff_and_apply():
    patch_set = compute_repo_diff(before_repo, after_repo)
    patched_repo = apply_diff(before_repo, patch_set)
    assert patched_repo == after_repo
    assert patched_repo != before_repo

    empty_patch_set = compute_repo_diff(before_repo, before_repo)
    patched_repo = apply_diff(before_repo, empty_patch_set)
    assert patched_repo == before_repo
    assert patched_repo != after_repo


def test_apply_diff():
    patched_repo = apply_diff(before_repo, parse_git_diff_output(diff))
    assert set(after_repo.files) == set(patched_repo.files)


def test_apply_diff_with_bad_diff():
    with pytest.raises(CommandFailedError):
        apply_diff(before_repo, parse_git_diff_output(bad_diff))


def test_parse_git_diff_output():
    patch_set = parse_git_diff_output(diff)
    expected_diff = """\
diff --git a/a.txt b/a.txt
index 8a1218a..6310241 100644
--- a/a.txt
+++ b/a.txt
@@ -1,5 +1,10 @@
 1
 2
+A line
+A line
+A line
 3
 4
 5
+B line
+B line
diff --git a/b.txt b/b.txt
index 6b34e8b..36aef8f 100644
--- a/b.txt
+++ b/b.txt
@@ -1,4 +1,7 @@
+C line
 foo
 bar
-hello
+D line
+D line
+D line
 world
diff --git a/c.txt b/c_renamed.txt
similarity index 100%
rename from c.txt
rename to c_renamed.txt
"""
    assert str(patch_set) == expected_diff
    assert str(parse_git_diff_output(expected_diff)) == expected_diff


def test_diff_subset_keep_all():
    subset = diff_subset(parse_git_diff_output(diff), lambda file, hunk: True)
    assert str(subset) == str(parse_git_diff_output(diff))


def test_diff_subset_keep_none():
    subset = diff_subset(parse_git_diff_output(diff), lambda file, hunk: False)
    assert str(subset) == ""


def test_diff_subset_drop_one():
    # Drop the first hunk in a.txt, and the renamed file
    def should_keep(file, hunk):
        if hunk is None:
            return False
        if file.path != "a.txt":
            return True
        if hunk.target_start != 2:
            return True
        return False

    subset = diff_subset(parse_git_diff_output(diff_with_U1), should_keep)
    expected_diff_subset_with_U1 = """\
diff --git a/a.txt b/a.txt
index 8a1218a..6310241 100644
--- a/a.txt
+++ b/a.txt
@@ -5,1 +5,3 @@
 5
+B line
+B line
diff --git a/b.txt b/b.txt
index 6b34e8b..36aef8f 100644
--- a/b.txt
+++ b/b.txt
@@ -1,4 +1,7 @@
+C line
 foo
 bar
-hello
+D line
+D line
+D line
 world
"""
    assert str(subset) == expected_diff_subset_with_U1


def test_diff_and_apply_with_complicated_repos():
    """Test diff and apply with added files, deleted files, renamed files, and subdirectories."""
    complex_before_repo = Repository(
        files=[
            File(path="a.txt", contents=a_before_contents),
            # File(path="b.txt", contents=b_before_contents),
            File(path="c.txt", contents=c_contents),
            File(path="subdir/a.txt", contents=a_before_contents),
            File(path="subdir/b.txt", contents=b_before_contents),
            File(path="subdir/c.txt", contents=c_contents),
        ]
    )

    complex_after_repo = Repository(
        files=[
            # File(path="a.txt", contents=a_after_contents),
            File(path="b.txt", contents=b_after_contents),
            File(path="c_renamed.txt", contents=c_contents),
            File(path="subdir/a.txt", contents=a_after_contents),
            File(path="subdir/b.txt", contents=b_after_contents),
            File(path="subdir/c_renamed.txt", contents=c_contents),
        ]
    )

    patch_set = compute_repo_diff(complex_before_repo, complex_after_repo)
    patched_repo = apply_diff(complex_before_repo, patch_set)
    assert patched_repo == complex_after_repo
    assert patched_repo != complex_before_repo


def test_diff_subset_without_deleted_file():
    complex_before_repo = Repository(
        files=[
            File(path="a.txt", contents=a_before_contents),
            # File(path="b.txt", contents=b_before_contents),
            File(path="c.txt", contents=c_contents),
        ]
    )

    complex_after_repo = Repository(
        files=[
            # File(path="a.txt", contents=a_after_contents),
            File(path="b.txt", contents=b_after_contents),
            File(path="c_renamed.txt", contents=c_contents),
        ]
    )

    def should_keep(file, hunk):
        del hunk
        if file.path.endswith("a.txt"):
            return False
        return True

    subset = diff_subset(
        compute_repo_diff(complex_before_repo, complex_after_repo),
        should_keep,
        # lambda file, hunk: file.path != "a.txt",
    )

    expected_repo = Repository(
        files=[
            File(path="a.txt", contents=a_before_contents),
            File(path="b.txt", contents=b_after_contents),
            File(path="c_renamed.txt", contents=c_contents),
        ]
    )

    patched_repo = apply_diff(complex_before_repo, subset)
    assert expected_repo == patched_repo


def test_diff_subset_without_added_file():
    complex_before_repo = Repository(
        files=[
            File(path="a.txt", contents=a_before_contents),
            # File(path="b.txt", contents=b_before_contents),
            File(path="c.txt", contents=c_contents),
        ]
    )

    complex_after_repo = Repository(
        files=[
            # File(path="a.txt", contents=a_after_contents),
            File(path="b.txt", contents=b_after_contents),
            File(path="c_renamed.txt", contents=c_contents),
        ]
    )

    def should_keep(file, hunk):
        del hunk
        if file.path.endswith("b.txt"):
            return False
        return True

    subset = diff_subset(
        compute_repo_diff(complex_before_repo, complex_after_repo),
        should_keep,
        # lambda file, hunk: file.path != "a.txt",
    )

    expected_repo = Repository(
        files=[
            # File(path="a.txt", contents=a_before_contents),
            # File(path="b.txt", contents=b_after_contents),
            File(path="c_renamed.txt", contents=c_contents),
        ]
    )

    patched_repo = apply_diff(complex_before_repo, subset)
    assert expected_repo == patched_repo


def test_diff_subset_without_renamed_file():
    complex_before_repo = Repository(
        files=[
            File(path="a.txt", contents=a_before_contents),
            # File(path="b.txt", contents=b_before_contents),
            File(path="c.txt", contents=c_contents),
        ]
    )

    complex_after_repo = Repository(
        files=[
            # File(path="a.txt", contents=a_after_contents),
            File(path="b.txt", contents=b_after_contents),
            File(path="c_renamed.txt", contents=c_contents),
        ]
    )

    def should_keep(file, hunk):
        del hunk
        if file.path.endswith("c_renamed.txt"):
            return False
        return True

    subset = diff_subset(
        compute_repo_diff(complex_before_repo, complex_after_repo),
        should_keep,
        # lambda file, hunk: file.path != "a.txt",
    )

    expected_repo = Repository(
        files=[
            # File(path="a.txt", contents=a_before_contents),
            File(path="b.txt", contents=b_after_contents),
            File(path="c.txt", contents=c_contents),
        ]
    )

    patched_repo = apply_diff(complex_before_repo, subset)
    assert expected_repo == patched_repo


def test_diff_apply_with_added_file_in_deep_subdir():
    my_before_repo = Repository(files=[])
    my_after_repo = Repository(
        files=[File(path="subdir1/subdir2/subdir3/a.txt", contents="a")]
    )
    diff = compute_repo_diff(my_before_repo, my_after_repo)
    patched_repo = apply_diff(my_before_repo, diff)
    assert patched_repo == my_after_repo


def test_repo_equality():
    assert Repository(files=[]) == Repository(files=[])
    assert Repository(files=[]) != Repository(files=[File(path="a.txt", contents="a")])
    assert Repository(files=[File(path="a.txt", contents="a")]) != Repository(
        files=[File(path="a.txt", contents="b")]
    )
    assert Repository(files=[File(path="a.txt", contents="a")]) == Repository(
        files=[File(path="a.txt", contents="a")]
    )
    assert Repository(
        files=[
            File(path="a.txt", contents="a"),
            File(path="b.txt", contents="b"),
        ]
    ) == Repository(
        [
            File(path="b.txt", contents="b"),
            File(path="a.txt", contents="a"),
        ]
    )


def test_save_and_load_repo():
    repo = Repository(
        files=[
            File(path="a.txt", contents="a"),
            File(path="subdir/b.txt", contents="b"),
        ]
    )

    with tempfile.TemporaryDirectory() as tmp_dir:
        repo.save(Path(tmp_dir))
        loaded_repo = Repository.load(Path(tmp_dir))
        assert repo == loaded_repo


def test_save_and_load_repo_with_newlines():
    repo = Repository(
        files=[
            File(path="a.txt", contents="a\r\nb"),
        ]
    )

    with tempfile.TemporaryDirectory() as tmp_dir:
        repo.save(Path(tmp_dir))
        loaded_repo = Repository.load(Path(tmp_dir))
        assert repo == loaded_repo


def test_verify_patch_set():
    verify_patch_set(parse_git_diff_output(diff))
    with pytest.raises(ValueError):
        verify_patch_set(PatchSet(diff))
    with pytest.raises(ValueError):
        apply_diff(before_repo, PatchSet(diff))
    with pytest.raises(ValueError):
        diff_subset(PatchSet(diff), lambda file, hunk: True)


def test_git_apply_failure():
    """A failure of git-apply with zero context lines which the library should handle.

    git apply fails to apply a zero-context-lines patch that git diff created.
    This can be resolved with --unidiff-zero.  To reproduce, run the following
    commands in an empty directory with no git repository:

        # create some files: a/a.txt and b/a.txt
        mkdir a
        mkdir b

        echo line > a/a.txt
        echo hello >> a/a.txt
        echo line >> a/a.txt

        echo line > b/a.txt
        echo goodbye >> b/a.txt
        echo line >> b/a.txt

        # save the diff between a/ and b/
        git diff --no-index --no-color --no-prefix -U0 a b > diff.txt

        # try to apply the diff to a/
        cd a
        git apply -p1 ../diff.txt

    The last command gives the error:
        error: patch failed: a.txt:2
        error: a.txt: patch does not apply

    On the other hand, running patch does work:
        patch -p1 --fuzz 0 < ../diff.txt

    The diff is:
        diff --git a/a.txt b/a.txt
        index 66b8ca1..c0b0496 100644
        --- a/a.txt
        +++ b/a.txt
        @@ -2 +2 @@ line
        -hello
        +goodbye
    """
    my_before_repo = Repository(
        files=[File(path="a.txt", contents="line\nhello\nline\n")]
    )
    my_after_repo = Repository(
        files=[File(path="a.txt", contents="line\ngoodbye\nline\n")]
    )
    diff = compute_repo_diff(my_before_repo, my_after_repo)
    patched_repo = apply_diff(my_before_repo, diff)
    assert patched_repo == my_after_repo


def test_reverse_apply_diff():
    maybe_before_repo = apply_diff(
        after_repo, parse_git_diff_output(diff), reverse=True
    )
    assert set(before_repo.files) == set(maybe_before_repo.files)


@pytest.mark.xfail(
    reason="Not handled correctly because of ' b/' in the path", run=False
)
def test_rename_with_whitespaces_in_file_name0():
    my_before_repo = Repository(
        files=[File(path="file b/name has spaces.txt", contents="")],
    )
    my_after_repo = Repository(files=[File(path="renamed file name.txt", contents="a")])
    diff: PatchSet = compute_repo_diff(my_before_repo, my_after_repo)
    assert diff[0].source_file == "a/file b/name has spaces.txt", str(diff)
    assert diff[1].target_file == "b/renamed file name.txt", str(diff)
    patched_repo = apply_diff(my_before_repo, diff)
    assert patched_repo == my_after_repo, str(diff)


def test_rename_with_whitespaces_in_file_name1():
    my_before_repo = Repository(
        files=[File(path="name has spaces.txt", contents="")],
    )
    my_after_repo = Repository(files=[File(path="renamed file name.txt", contents="a")])
    diff: PatchSet = compute_repo_diff(my_before_repo, my_after_repo)
    assert diff[0].source_file == "a/name has spaces.txt", str(diff)
    assert diff[1].target_file == "b/renamed file name.txt", str(diff)
    patched_repo = apply_diff(my_before_repo, diff)
    assert patched_repo == my_after_repo, str(diff)


def test_rename_with_whitespaces_in_file_name2():
    my_before_repo = Repository(
        files=[File(path="a folder/name has spaces.txt", contents="")],
    )
    my_after_repo = Repository(files=[File(path="renamed file name.txt", contents="a")])
    diff: PatchSet = compute_repo_diff(my_before_repo, my_after_repo)
    assert diff[0].source_file == "a/a folder/name has spaces.txt", str(diff)
    assert diff[1].target_file == "b/renamed file name.txt", str(diff)
    patched_repo = apply_diff(my_before_repo, diff)
    assert patched_repo == my_after_repo, str(diff)


def test_rename_with_whitespaces_in_file_name3():
    my_before_repo = Repository(
        files=[File(path="a folder/name has spaces.txt.bak", contents="")],
    )
    my_after_repo = Repository(
        files=[File(path="another folder/renamed file name.txt", contents="a")]
    )
    diff: PatchSet = compute_repo_diff(my_before_repo, my_after_repo)
    assert diff[0].source_file == "a/a folder/name has spaces.txt.bak", str(diff)
    assert diff[1].target_file == "b/another folder/renamed file name.txt", str(diff)
    patched_repo = apply_diff(my_before_repo, diff)
    assert patched_repo == my_after_repo, str(diff)


def test_rename_with_whitespaces_in_file_name4():
    my_before_repo = Repository(
        files=[File(path="the folder/name has spaces.txt.bak", contents="hello")],
    )
    my_after_repo = Repository(
        files=[File(path="the folder/name has spaces.txt.bak", contents="goodbye")],
    )
    diff: PatchSet = compute_repo_diff(my_before_repo, my_after_repo)
    assert diff[0].source_file == "a/the folder/name has spaces.txt.bak", str(diff)
    assert diff[0].target_file == "b/the folder/name has spaces.txt.bak", str(diff)
    patched_repo = apply_diff(my_before_repo, diff)
    assert patched_repo == my_after_repo, str(diff)


def test_get_sub_repos_for_diff():
    _before_repo = Repository(
        files=[
            File(path="a.txt", contents=a_before_contents),
            File(path="b.txt", contents=b_before_contents),
            File(path="c.txt", contents=c_contents),
            File(path="unchanged1.txt", contents="unchanged1"),
            File(path="unchanged2.txt", contents="unchanged2"),
            File(path="deleted.txt", contents="deleted"),
        ]
    )

    _after_repo = Repository(
        files=[
            File(path="a.txt", contents=a_after_contents),
            File(path="b.txt", contents=b_after_contents),
            File(path="c_renamed.txt", contents=c_contents),
            File(path="unchanged1.txt", contents="unchanged1"),
            File(path="unchanged2.txt", contents="unchanged2"),
            File(path="added.txt", contents="added"),
        ]
    )

    sub_before_repo, sub_after_repo = _get_sub_repos_for_diff(_before_repo, _after_repo)

    assert sub_before_repo == Repository(
        files=[
            File(path="a.txt", contents=a_before_contents),
            File(path="b.txt", contents=b_before_contents),
            File(path="c.txt", contents=c_contents),
            File(path="deleted.txt", contents="deleted"),
        ]
    )

    assert sub_after_repo == Repository(
        files=[
            File(path="a.txt", contents=a_after_contents),
            File(path="b.txt", contents=b_after_contents),
            File(path="c_renamed.txt", contents=c_contents),
            File(path="added.txt", contents="added"),
        ]
    )


def test_initialize_file_with_path_instead_of_str():
    with pytest.raises(TypeError):
        File(path=Path("a.txt"), contents="a")  # type: ignore


@pytest.mark.parametrize(
    "diff, expected_result",
    [
        (
            """\
--- /tmp/a
+++ /tmp/b
@@ -1,5 +1,2 @@
 1
-2
-3
-4
 5
@@ -7,2 +4,7 @@
 7
+a
+b
+c
+d
+e
 8""",
            [
                LineRange(1, 2),
                LineRange(4, 9),
            ],
        ),
        (
            """\
--- /tmp/a
+++ /tmp/b
@@ -2,7 +2,7 @@
 2
 3
 4
-5
+42
 6
 7
 8""",
            [
                LineRange(4, 5),
            ],
        ),
        (
            """\
--- /tmp/a
+++ /tmp/b
@@ -1,4 +1,4 @@
-1
+42
 2
 3
 4
@@ -6,4 +6,4 @@
 6
 7
 8
-9
+42
""",
            [
                LineRange(0, 1),
                LineRange(8, 9),
            ],
        ),
        (
            """\
--- /tmp/a
+++ /tmp/b
@@ -1,3 +1,4 @@
+42
 1
 2
 3
@@ -7,3 +8,4 @@
 7
 8
 9
+42
""",
            [
                LineRange(0, 1),
                LineRange(10, 11),
            ],
        ),
        (
            """\
--- /dev/null
+++ b/a
@@ -0,0 +1,3 @@
+a
+b
+c
""",
            [
                LineRange(0, 3),
            ],
        ),
        (
            r"""\
--- a/foo.txt
+++ b/foo.txt
@@ -1,2 +1,2 @@
 Here are some words.
-Here are some more words.
+Here are some more words.
\ No newline at end of file
""",
            [
                LineRange(1, 2),
            ],
        ),
        (
            r"""\
--- a/foo.txt
+++ b/foo.txt
@@ -1,2 +1,2 @@
 Here are some words.
-Here are some more words.
\ No newline at end of file
+Here are some more words.
""",
            [
                LineRange(1, 2),
            ],
        ),
    ],
)
def test_get_modified_ranges(diff: str, expected_result: list[LineRange]):
    pfile = parse_git_diff_output(diff)[0]
    assert get_modified_ranges(pfile) == expected_result

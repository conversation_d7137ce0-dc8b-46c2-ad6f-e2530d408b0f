"""Utility class to represent generic changes."""

from __future__ import annotations

from dataclasses import dataclass
from typing import Callable, Generic, TypeVar, Union, assert_never

from base.diff_utils.changes import (
    Added,
    Changed,
    Deleted,
    MaybeChanged,
    Modified,
    Unchanged,
    get_before,
    get_after,
    reverse_changed,
    reverse_maybe_changed,
)

__reexported__ = [
    "Added",
    "Changed",
    "Deleted",
    "MaybeChanged",
    "Modified",
    "Unchanged",
    "get_before",
    "get_after",
    "reverse_changed",
    "reverse_maybe_changed",
]

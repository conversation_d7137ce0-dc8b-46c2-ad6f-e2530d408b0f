"""Unit tests for core/utils_for_str.py.

pytest research/core/tests/test_utils_for_str.py
"""

import random

from termcolor import colored

from research.core import utils_for_str


def test_parsing():
    """Test some parse functions."""

    colors = [
        "red",
        "green",
        "yellow",
        "blue",
        "magenta",
        "cyan",
        "white",
        "light_grey",
        "dark_grey",
        "light_red",
        "light_green",
        "light_yellow",
    ]
    for color in colors:
        string = colored("hello", color=color, force_color=True)
        parts = utils_for_str.extract_colored_parts(string)
        assert parts == ["hello"], f"{color} failed on {string}"

        colored_str = (
            "xx "
            + colored("hello", color=color, force_color=True)
            + "1"
            + colored("PASS", color=color, force_color=True)
        )
        parts = utils_for_str.extract_colored_parts(colored_str)
        assert parts == ["hello", "PASS"], f"{color} failed on {string}"
        assert utils_for_str.remove_color_pattern(colored_str) == "xx hello1PASS"


def test_contain_substrings():
    """Test the contain_substrings func."""
    assert not utils_for_str.contain_substrings("123", [])
    assert utils_for_str.contain_substrings("hello world", ("hel",))
    assert utils_for_str.contain_substrings("hello world", ("hel", "wo"))
    assert utils_for_str.contain_substrings("hel\tlo world", ("hel", "wo"))


def test_trim_string():
    """Test the trim_string func."""
    assert utils_for_str.trim_string("hello world", " ") == "hello"
    assert utils_for_str.trim_string("hello\tworld", "\t") == "hello"


def test_sanitize_filename():
    """Test the sanitize_filename func."""
    assert utils_for_str.sanitize_filename("a/b") == "a_b"
    assert utils_for_str.sanitize_filename("a.log") == "a.log"
    assert utils_for_str.sanitize_filename("a-log") == "a-log"
    assert utils_for_str.sanitize_filename("a[log]") == "alog"
    assert utils_for_str.sanitize_filename("a:c") == "a-c"


def test_max_common_prefix_length():
    """Test the max_common_prefix_length func."""
    str1, str2 = "abcdef", "abcxyz"
    assert utils_for_str.max_common_prefix_length(str1, str2) == 3


def test_max_common_prefix_lines():
    """Test the max_common_prefix_lines func."""
    str1, str2 = "abc\n1", "abc\n2"
    assert utils_for_str.max_common_prefix_lines(str1, str2) == 1
    str1, str2 = "abc\nxx\n1", "abc\nxx"
    assert utils_for_str.max_common_prefix_lines(str1, str2) == 2


def test_merge_str_as_matrix():
    """Test the merge_str_as_matrix func."""
    answer = utils_for_str.merge_str_as_matrix(
        ["item_1", "item_2", "item_3", "item_4"], items_per_row=3
    )
    assert (
        answer
        == r"""
item_1 | item_2 | item_3
------------------------
item_4""".lstrip()
    )
    answer = utils_for_str.merge_str_as_matrix(
        ["item_1\nxx", "item_2", "item_3\n123456", "item_4"], items_per_row=3
    )
    assert (
        answer
        == r"""
item_1 | item_2 | item_3
xx     |        | 123456
------------------------
item_4""".lstrip()
    )


def test_get_first_n_lines_and_get_last_n_lines():
    """Test the get_first_n_lines and get_last_n_lines funcs."""
    docs = "1\n2\n3\n4\n"
    assert utils_for_str.get_first_n_lines(docs, 2) == "1\n2\n"
    assert utils_for_str.get_last_n_lines(docs, 2) == "3\n4\n"


def test_add_and_del_line_number():
    """Test the add_line_number and delete_line_number func."""

    def _generate_lines(num_lines):
        line_starts = ["", " ", "\t", "\n", "   "]
        return [
            random.choice(line_starts) + "This is line " + str(i + 1)
            for i in range(num_lines)
        ]

    for _ in range(100):
        num_lines = random.randint(1, 10)
        content = "\n".join(_generate_lines(num_lines))
        assert content == utils_for_str.delete_line_number(
            utils_for_str.add_line_number(content)
        )


def test_delete_prefix_suffix_from_content():
    """Test the delete_prefix_suffix_from_content func."""
    assert utils_for_str.delete_prefix_suffix_from_content("123", "1", "3") == (
        "2",
        (True, True),
    )
    assert utils_for_str.delete_prefix_suffix_from_content("1\n\n23", "1", None) == (
        "\n\n23",
        (True, False),
    )
    assert utils_for_str.delete_prefix_suffix_from_content(
        "x" * 10, "x" * 7, "x" * 7
    ) == ("x" * 3, (True, False))
    assert utils_for_str.delete_prefix_suffix_from_content("I-love-u", None, "-u") == (
        "I-love",
        (False, True),
    )
    assert utils_for_str.delete_prefix_suffix_from_content(
        "x" * 10, "y" * 7, "y" * 7
    ) == ("x" * 10, (False, False))


def tets_delete_lines_from_content():
    """Test the delete_lines_from_content func."""
    assert utils_for_str.delete_lines_from_content("1\n2\n3\n", 1, 0) == "2\n3\n"
    assert utils_for_str.delete_lines_from_content("1\n2\n3\n", 1, 1) == "2\n"


def test_get_shared_prefix_suffix_lines():
    """Test the get_shared_prefix_suffix_lines func."""
    assert utils_for_str.get_shared_prefix_suffix_lines("1\n2\n3\n", "1\n2\n3\n") == (
        3,
        0,
    )
    assert utils_for_str.get_shared_prefix_suffix_lines("1\n2\n3\n", "1\n2\n4\n") == (
        2,
        0,
    )
    assert utils_for_str.get_shared_prefix_suffix_lines("1\n2\n3\n", "1\n3\n3\n") == (
        1,
        1,
    )
    assert utils_for_str.get_shared_prefix_suffix_lines("3\n2\n3\n", "1\n2\n3\n") == (
        0,
        2,
    )


def test_extract_the_last_markdown_block():
    """Test the extract_the_last_markdown_block func."""

    assert utils_for_str.extract_the_last_markdown_block("hello") is None
    assert utils_for_str.extract_the_last_markdown_block("hello\n") is None
    assert utils_for_str.extract_the_last_markdown_block("\n") is None

    assert (
        utils_for_str.extract_the_last_markdown_block(
            """```python
```"""
        )
        == ""
    )

    # NOTE: special test where input has a space " " just before closing backticks.
    assert (
        utils_for_str.extract_the_last_markdown_block(
            """```python\n    print("hello") \n```"""
        )
        == """    print("hello") """
    )

    assert (
        utils_for_str.extract_the_last_markdown_block(
            """```python
print("hello")
```"""
        )
        == """print("hello")"""
    )

    assert (
        utils_for_str.extract_the_last_markdown_block(
            """```print("hello")
```"""
        )
        is None
    )

    assert (
        utils_for_str.extract_the_last_markdown_block(
            """
# Title

This is a markdown block.

```python
print("hello")
```

This is another markdown block.

```python
print("world")
```
"""
        )
        == """print("world")"""
    )
    assert (
        utils_for_str.extract_the_last_markdown_block(
            """```typescript
Promise<CodeEditResult> {};
```
"""
        )
        == """Promise<CodeEditResult> {};"""
    )
    for _ in range(100):
        lines = random.randint(0, 5)
        text = "\n".join([random.choice("abcdeft") for _ in range(lines)])
        assert (
            utils_for_str.extract_the_last_markdown_block(
                utils_for_str.create_markdown_block(text)
            )
            == text
        )

    assert (
        utils_for_str.extract_the_last_markdown_block(
            """``` \nPyObject *re_module = PyImport_ImportModule(\"regex\");\n```\n"""
        )
        == """PyObject *re_module = PyImport_ImportModule(\"regex\");"""
    )


def test_count_min_indentation():
    """Test the count_min_indentation func."""
    assert utils_for_str.count_min_indentation("hello") == 0
    assert utils_for_str.count_min_indentation("  hello") == 2
    assert utils_for_str.count_min_indentation("  hello\n") == 2
    assert utils_for_str.count_min_indentation("  hello\n  world") == 2
    assert utils_for_str.count_min_indentation("  hello\n   world\n") == 2
    assert utils_for_str.count_min_indentation("  hello\n world\n") == 1


def test_edit_similarity():
    """Test the edit_similarity func."""
    assert utils_for_str.compute_edit_similarity("hello", "hello") == 1
    assert abs(utils_for_str.compute_edit_similarity("hello", "world") - 0.2) < 1e-6

"""Research extension of base.component_registry.

This package wraps around the base component registry and exposes some convenient
utilities for registering components that aren't safe for production usage.

See the documentation in base/component_registry for more details on the general
framework.
"""

import functools
import importlib
import logging
from collections.abc import Mapping, Sequence
from pathlib import Path
from typing import Optional

from base.component_registry.component_registry import (
    COMPONENT_NAME_KEY,
    REFERENCE_PREFIX,
    ComponentMap,
    ComponentRegistry,
    # These are re-exported functions.
    Serializable,  # noqa: F401
    serialize_component_configs,  # noqa: F401
)
from base.component_registry.component_registry import (
    create_components as _create_components,
)

logger = logging.getLogger(__name__)


def autocreate_registry(
    components: Mapping[str, dict], base_registry: ComponentRegistry | None = None
) -> ComponentRegistry:
    """Create a registry by importing and autoregistering the given components.

    Args:
        components: A dictionary mapping component names to their components.
            The format of this dictionary, like with base.component_registry is:
            {
                "my_component":  {
                    # Fully qualified component name.
                    "$component_name": "research.package.MyComponent",
                    "param": "foo",
                }
            }
        base_registry: If provided, a base registry to which we will autoregister
          additional components.

    """
    if base_registry is None:
        base_registry = ComponentRegistry()

    # NOTE(arun): We set up dataclasses_json here first to prevent "Path" types from
    # getting converted to strings.
    _setup_dataclasses_json()

    for _, component_config in components.items():
        if COMPONENT_NAME_KEY in component_config:
            component_name = component_config[COMPONENT_NAME_KEY]
        elif REFERENCE_PREFIX + COMPONENT_NAME_KEY in component_config:
            component_name = component_config[REFERENCE_PREFIX + COMPONENT_NAME_KEY]
        else:
            raise ValueError(
                f"Could not find required field {REFERENCE_PREFIX}{COMPONENT_NAME_KEY} "
                f"in config: {component_config}"
            )

        if component_name in base_registry:
            logger.debug(f"Skipping {component_name} because it is already registered.")
            continue

        # Try to resolve this component class from the registries.
        module_name, cls_or_func_name = component_name.rsplit(".", 1)
        module = importlib.import_module(module_name)
        if not hasattr(module, cls_or_func_name):
            raise ValueError(
                f"Could not find component {component_name} in {module_name}."
            )
        cls_or_func = getattr(module, cls_or_func_name)
        base_registry.autoregister(cls_or_func)

    return base_registry


def create_components(
    component_configs: Mapping[str, dict],
    registries: Sequence[ComponentRegistry] | None = None,
    existing_components: ComponentMap | dict | None = None,
    validate_only: bool = False,
) -> ComponentMap:
    """Create components from the given configurations.

    This function validates configurations by checking:
    1. All component kinds are registered.
    2. All configs are valid.
    3. All refs exist without cycles.
    4. All required components are present.

    Args:
        config_dcts: A dictionary mapping component names to their configurations.
        registries: The component registries to use. We will resolve the component kinds
            from these registries, with the first registry that contains the component
            kind taking precedence.
        existing_components: The existing components to use.
        validate_only: If true, we will not create any components, only validate
            the configurations.

    Returns:
        A map from component names to their components.
    """
    if registries is None:
        registries = [autocreate_registry(component_configs)]
    return _create_components(
        component_configs, registries, existing_components, validate_only
    )


# Because this takes no arguments, this is a simple way of only running this function
# once.
@functools.lru_cache()
def _setup_dataclasses_json():
    """Setup dataclasses_json to handle commonly used types.

    This function should be run before any dataclass is converted to JSON.
    """
    import dataclasses_json
    from marshmallow import fields as mm_fields

    dataclasses_json.global_config.encoders[Path] = str
    dataclasses_json.global_config.decoders[Path] = Path
    dataclasses_json.global_config.mm_fields[Path] = mm_fields.String()
    # NOTE(arun): This is a hack to make Optional[Path] work.
    dataclasses_json.global_config.encoders[Optional[Path]] = str  # type: ignore
    dataclasses_json.global_config.decoders[Optional[Path]] = Path  # type: ignore
    dataclasses_json.global_config.mm_fields[Optional[Path]] = mm_fields.String()  # type: ignore

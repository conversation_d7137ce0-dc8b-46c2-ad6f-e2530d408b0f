"""Definition of the data format for next edit locations in research."""

from dataclasses import dataclass
from typing import Optional, Sequence

from unidiff import Hunk, PatchSet

from base.prompt_format_next_edit.common import NextEditPromptInput
from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Range
from research.core.types import <PERSON><PERSON>, NTuple, Scored
from research.core.utils import <PERSON>Logger
from research.next_edits.diagnostics import Diagnostic


@dataclass(frozen=True)
class FileLocation:
    """A location in a file."""

    path: str
    """The path of the file."""

    range: LineRange
    """The range in the file."""

    def __str__(self) -> str:
        return f"{self.path}:{str(self.range)}"

    def intersect(self, other: "FileLocation") -> Optional[LineRange]:
        """Return the intersection of this range with another range."""
        if self.path != other.path:
            return None
        return self.range.intersect(other.range)


@dataclass(frozen=True)
class FileLocationWithHeader(FileLocation):
    """A file location with a header summarizing the containing blocks (e.g. class, function)."""

    header: str
    """The header of the file location."""


@dataclass(frozen=True)
class NextEditLocationLabel:
    """The label for the next edit location model."""

    locations: tuple[FileLocation, ...]
    """List of locations that were actually changed.

    NOTE: locations is a tuple to make it hashable.
    """


@dataclass(frozen=True)
class PathAndDiff:
    """A path and diff."""

    path: str
    """The path of the file."""

    diff_hunk: Hunk
    """The diff hunk."""

    @property
    def diff_text(self) -> str:
        """The header of the diff."""
        return str(self.diff_hunk)

    @property
    def source_lrange(self) -> LineRange:
        """The source line range of the diff."""
        return LineRange(
            self.diff_hunk.source_start,
            self.diff_hunk.source_start + self.diff_hunk.source_length,
        )

    @property
    def target_lrange(self) -> LineRange:
        """The target line range of the diff."""
        return LineRange(
            self.diff_hunk.target_start,
            self.diff_hunk.target_start + self.diff_hunk.target_length,
        )

    @property
    def header(self) -> str:
        """The header of the diff."""
        return "@@ -%d,%d +%d,%d @@%s\n" % (
            self.diff_hunk.source_start,
            self.diff_hunk.source_length,
            self.diff_hunk.target_start,
            self.diff_hunk.target_length,
            " " + self.diff_hunk.section_header
            if self.diff_hunk.section_header
            else "",
        )


@dataclass(frozen=True)
class NextEditLocationSystemInput(NextEditPromptInput):
    """The input to the next edit location model."""

    doc_ids: frozenset[str]
    """The list of doc ids to use for context when generating a completion."""

    top_k: int = 32
    """The number of locations to return in the output."""

    diagnostics: NTuple[Diagnostic] = ()
    """Diagnostics, with the most recent first."""

    restrict_to_file: str | None = None
    """If set, will only try to edit the given file path."""

    selected_range: Optional[CharRange] = None
    """The currently selected range of characters."""

    label: Optional[NextEditLocationLabel] = None
    """The label for the input.

    If None, there is no target.
    """

    past_diff: Optional[str] = None
    """The diff used to construct the recent changes.

    This is only used for manually investigating examples and should not be
    used in normal code.
    """

    future_diff: Optional[str] = None
    """The diff used to construct the label.

    This is only used for manually investigating examples and should not be
    used in normal code.
    """

    @property
    def past_hunks(self) -> Sequence[PathAndDiff]:
        """The past diff hunks.

        This is only used for manually investigating examples and should not be
        used in normal code.
        """
        return (
            [
                PathAndDiff(path=patch_file.path, diff_hunk=hunk)
                for patch_file in PatchSet(self.past_diff)
                if not patch_file.is_removed_file
                for hunk in patch_file
            ]
            if self.past_diff
            else []
        )

    @property
    def future_hunks(self) -> Sequence[PathAndDiff]:
        """The future diff hunks.

        This is only used for manually investigating examples and should not be
        used in normal code.
        """
        return (
            [
                PathAndDiff(path=patch_file.path, diff_hunk=hunk)
                for patch_file in PatchSet(self.future_diff)
                if not patch_file.is_added_file
                for hunk in patch_file
            ]
            if self.future_diff
            else []
        )


# kw_only=True so that we can subclass and add fields without default values
@dataclass(kw_only=True)
class NextEditLocationOutput:
    """The output of the next edit location model."""

    scored_candidates: Sequence[Scored[FileLocation]]
    """Sorted list of candidate locations."""

    debug_info: dict[str, str]
    """Key-value pairs stored for debugging."""

    top_chunks: Optional[list[Chunk]] = None
    """The top scoring chunks."""

    def log_to_file(self, file_logger: FileLogger) -> None:
        """Log the output via a FileLogger."""
        locations_str = "\n".join(
            f"location={str(loc)}, score={loc.score:.3g}"
            for loc in self.scored_candidates
        )
        file_logger.log("candidate_locations.txt", locations_str)
        for k, v in self.debug_info.items():
            file_logger.log(f"{k}.txt", v)


@dataclass
class DiffSpan:
    """A diff span."""

    original: CharRange
    """The original span."""

    updated: CharRange
    """The updated span."""


@dataclass
class ScoredFileHunk:
    """A scored region of a changed file."""

    file_location: FileLocation
    """The file region of the change."""

    original_code: str
    """The original code in the region."""

    updated_code: str
    """The updated code in the region."""

    truncation_char: int | None
    """The character offset in `updated_code` where the change was truncated.

    None means that the change was not truncated.
    """

    localization_score: float
    """The score of this region according to the location model."""

    editing_score: float
    """The score of this region according to the editing/reranking model."""

    change_description: str
    """A natural language description of the change."""

    diff_spans: list[DiffSpan]
    """Character-level diff information."""

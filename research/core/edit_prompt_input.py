"""Definition of the data format for code edits research."""

import copy
import dataclasses
import typing

from base.prompt_format_edit.prompt_formatter import EditPromptInput
from base.ranges.range_types import LineRange
from research.core.model_input import ModelInput
from research.core.types import Chunk


@dataclasses.dataclass(frozen=True)
class ResearchEditPromptInput:
    """Extensible adaptation of the EditPromptInput for research.

    TODO: Update to inherit from base.prompt_format_edit.prompt_formatter.EditPromptInput.
    In order to do so, we need to migrate the research codebase from using the research.core.type.Chunk
    abstraction to the base.prompt_format_completion.prompt_formatter.PromptChunk abstraction.

    We also need to add the prefix_begin and suffix_end fields.
    """

    path: str
    """The file path."""

    prefix: str
    """The content before the selection."""

    selected_code: str
    """The content that the user selected."""

    suffix: str
    """The content after the selection, where its end location is at the cursor."""

    instruction: str
    """The user's instruction about how to edit the selected code based on context."""

    retrieved_chunks: list[Chunk]
    """The retrieved chunks, sorted from high to low relevancy."""

    updated_code: typing.Optional[str] = None
    """Result of changing `selected_code` according to `instruction`.

    If None, there is no target. This is different from having an empty string as target.
    """

    doc_ids: typing.Optional[typing.Collection[str]] = None
    """If set, the doc ids to filter on.

    Note that historically, we have stored this list in the `extra` field.
    Going forward, we should use this field instead.
    """

    extra: dict[str, typing.Any] = dataclasses.field(default_factory=dict)
    """Extra fields for easy extensibility."""

    def clone(self):
        """Returns a deep copy of the instance."""
        return copy.deepcopy(self)


@dataclasses.dataclass(frozen=True)
class ResearchPREditPromptInput(EditPromptInput):
    """Extensible adaptation of the EditPromptInput for PR edits.

    Notice that selected_code field contains text to which PR comment is attached.
    Unlike in code edits, here we are not restricted to modifying only selected_code.
    """

    generated_suggestion: str
    """Generated suggestion that resolves corresponding PR comment."""

    range_to_replace: LineRange
    """Denotes which region of original code is supposed to be replaced by `generated_suggestion`.
    Indices in range are counted from the first line of prefix.
    Should be treated as [start, stop).
    """


def convert_research_edit_prompt_input_to_model_input(
    model_input: ResearchEditPromptInput,
) -> ModelInput:
    """Convert a ResearchEditPromptInput to a ModelInput.

    TODO: Improve codebase such that this adapter is not necessary anymore.
    """
    return ModelInput(
        path=model_input.path,
        prefix=model_input.prefix,
        suffix=model_input.suffix,
        retrieved_chunks=model_input.retrieved_chunks,
        target=model_input.updated_code,
        doc_ids=model_input.doc_ids,
        extra={
            **model_input.extra,
            "instruction": model_input.instruction,
            "selected_code": model_input.selected_code,
        },
    )


def convert_model_input_to_research_edit_prompt_input(
    model_input: ModelInput,
) -> ResearchEditPromptInput:
    """Convert to ResearchEditPromptInput from a ModelInput.

    TODO: Improve codebase such that this adapter is not necessary anymore.
    """
    new_extra = {
        k: v
        for k, v in model_input.extra.items()
        if k not in ["instruction", "selected_code"]
    }
    return ResearchEditPromptInput(
        path=model_input.path,
        prefix=model_input.prefix,
        suffix=model_input.suffix,
        instruction=model_input.extra["instruction"],
        selected_code=model_input.extra["selected_code"],
        retrieved_chunks=model_input.retrieved_chunks,
        updated_code=model_input.target,
        doc_ids=model_input.doc_ids,
        extra=new_extra,
    )

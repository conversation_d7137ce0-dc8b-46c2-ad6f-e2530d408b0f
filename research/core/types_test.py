"""Tests for /home/<USER>/augment/research/core/types.py."""

import pytest

from research.core import types


def test_empty_document():
    """Tests `EMPTY_DOCUMENT` is immutable."""
    empty_doc = types.EMPTY_DOCUMENT
    with pytest.raises(AttributeError):
        empty_doc.text = "some text"


def test_empty_chunk():
    """Tests `EMPTY_DOCUMENT` is immutable."""
    empty_chunk = types.EMPTY_CHUNK
    with pytest.raises(AttributeError):
        empty_chunk.text = "some text"

#!/usr/bin/env python
"""Run evaluation on a set of checkpoints, saving the results on the shared drive."""

from __future__ import annotations

import glob
import json
import logging
import socket
import subprocess
import urllib.parse
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Final, Union

import shortuuid
import yaml
from determined.common.experimental.experiment import ExperimentState
from determined.experimental import client as det_client
from jobs.internal.augment_configs import DeterminedConfig

from research.core.constants import AUGMENT_EFS_ROOT
from research.core.utils_for_log import setup_logging

DEFAULT_JOB_ROOT = AUGMENT_EFS_ROOT / "eval/jobs"
DOWNLOAD_ROOT = AUGMENT_EFS_ROOT / "temporary_checkpoints"
THIS_DIRECTORY = Path(__file__).parent.resolve()

_SUMMARY_FORMAT_VERSION: Final = 2


logger = logging.getLogger(__name__)


def unique_job(job_root: str) -> Path:
    # shorten the job name just to be friendly
    return Path(job_root) / shortuuid.uuid()[:8]


def get_configs(
    config_globs: list[str],
    model_name: str,
    checkpoint: str,
    podspec: str,
    job_name_suffix: str,
    overrides: list[ConfigOverride] | None = None,
    additional_required_keys: set[str] | None = None,
) -> list[dict]:
    """Takes a list of config globs and returns a list of configs."""
    config_files = sum(
        [glob.glob(pattern, recursive=True) for pattern in config_globs], []
    )
    assert config_files, "No config files after globbing."
    logger.info(
        "Found %s config files:\n%s", len(config_files), "\n".join(config_files)
    )

    configs = []
    for config_file in config_files:
        config = build_config([config_file], additional_required_keys, overrides)
        if model_name:
            config["system"]["model"]["name"] = model_name
        if checkpoint:
            config["system"]["model"]["checkpoint_path"] = checkpoint
        if podspec:
            config["podspec"] = podspec + ".yaml"
        if job_name_suffix:
            config["determined"]["name"] += f"-{job_name_suffix}"
        configs.append(config)
    return configs


def run_bazel():
    """Builds the tokenizer artifacts with bazel before we push the directory."""
    # Bazel conveniently doesn't care where we call it from
    subprocess.check_call(
        ["bazel", "run", "-c", "opt", "//base:install"],
    )


def run_local_inner(config: dict, job_root: str, dry_run: bool = False):
    from research.eval.harness.harness import EvalHarness

    job_path = get_job_path(job_root)

    # Eval results will have the hostname as a prefix, to prevent collisions
    # should multiple hosts be writing into the same job directory
    hostname = socket.gethostname()
    prefix = f"{hostname}_"
    logfile = job_path / f"{hostname}.log"

    # Configure logging to save to logfile
    setup_logging(logfile)
    logger.info("Job output will be written to %s.", job_path)
    config_path = job_path / f"{prefix}config.yml"
    logger.info("Saving configuration to %s.", config_path)
    config_path.write_text(yaml.dump(config))

    harness = EvalHarness.from_config(config, job_path, prefix)

    if dry_run:
        harness.dry_run()
    else:
        results = harness.run()

        results_file = job_path / f"{prefix}results.jsonl"
        logger.info("Saving results to %s.", results_file)
        results_json = [json.dumps(r) for r in results]
        results_file.write_text("\n".join(results_json) + "\n")

        logger.info("Job output written to %s.", job_path)

    # Return a dummy DeterminedEvalRun object with job_path + config.
    return DeterminedEvalRun(job_path, "", -1, config)


def run_local(
    configs: list[dict],
    job_root: str,
    skip_bazel: bool,
    dry_run: bool = False,
) -> dict[str, dict[str, Any]]:
    if not skip_bazel:
        run_bazel()

    for config in configs:
        run_local_inner(config, job_root, dry_run)
    return {"overview": {}, "runs": {}}


def create_metaconfig(config, ns):
    gpt_neox_dir = (Path(__file__).parent.parent / "gpt-neox").resolve()

    # set includes[] after we write the dynamic config
    determined_config = config["determined"]

    # Create a metaconfig file
    metaconfig_file = gpt_neox_dir / determined_config["metaconfig"]
    with metaconfig_file.open(encoding="utf-8") as f:
        metaconfig = yaml.safe_load(f)

    # set determined.workspace
    metaconfig["determined"]["workspace"] = determined_config["workspace"]
    # set determined.project
    metaconfig["determined"]["project"] = determined_config["project"]
    # set determined.name
    metaconfig["determined"]["name"] = determined_config["name"]
    # set determined.description
    metaconfig["determined"]["description"] = "null"

    # copy over any additional augment fields.
    metaconfig["augment"].update(config.get("augment", {}))

    # DEPRECATED:
    # set augment.eval_tasks
    metaconfig["augment"]["eval_tasks"] = ""

    # set augment.eval_results_prefix
    outdir = ns.parent
    metaconfig["augment"]["eval_outdir"] = f"{outdir.as_posix()}"

    eval_idx = 0
    metaconfig["augment"]["eval_prefix"] = f"{eval_idx:03}_"

    if "wandb_run_id" in determined_config:
        metaconfig["augment"]["wandb_run_id"] = determined_config["wandb_run_id"]
    # set load
    if "model" in config["system"] and "checkpoint_path" in config["system"]["model"]:
        metaconfig["overrides"]["load"] = config["system"]["model"]["checkpoint_path"]
    else:
        metaconfig["overrides"]["load"] = "null"

    metaconfig["augment"]["podspec_path"] = config["podspec"]

    return metaconfig


def launch_determined_eval(
    config: dict[str, Any],
    ns: Path,
    cluster: str | None = None,
    tolerations: dict[str, str] | None = None,
) -> int:
    """Launch an evaluation in determined, return the experiment ID."""

    metaconfig = create_metaconfig(config, ns)

    # Where does this go with Bazel?
    logfile = Path("./eval_launch.log")

    experiment_id = -1

    with logfile.open("a+", encoding="utf-8") as log:
        metaconfig["includes"] = []
        metaconfig["augment"]["eval_spec"] = json.dumps(config)

        log.write("Launching with metaconfig\n")
        log.write(yaml.dump(metaconfig))
        dc = DeterminedConfig(
            metaconfig, hyperparameters_page_content=config, cluster=cluster
        )
        if tolerations:
            print(f"Adding tolerations {tolerations}")
            for key, value in tolerations.items():
                dc.add_toleration(key, value)

        log.write(f"Experiment config:\n{dc}")
        experiment_id = dc.run()
        assert experiment_id is not None, "Could not determine experiment ID"
        log.write(f"Created experiment {experiment_id}\n")
        log.flush()
        print(f"Created experiment {experiment_id}")
        print("\n")
        print(
            urllib.parse.urljoin(dc.determined_url, f"det/experiments/{experiment_id}")
        )

    return experiment_id


def get_experiment_url(experiment_id):
    """Return an experiment's URL."""
    exp = det_client.get_experiment(experiment_id)
    # pylint: disable=protected-access
    return f"{exp._session.master}/det/experiments/{exp.id}"
    # pylint: enable=protected-access


def describe_experiment(experiment_id, wait=True):
    """Describe an experiment in determined."""
    exp = det_client.get_experiment(experiment_id)
    if wait:
        state = exp.wait()
    else:
        exp.reload()
        state = exp.state
    return state


def wait_experiment(experiment_id: int, fail_on_noncompletion=True):
    """Wait for an experiment to complete successfully."""
    state = describe_experiment(experiment_id)
    # Dump the last N lines on failure
    if state != ExperimentState.COMPLETED:
        trial = det_client.get_experiment(experiment_id).get_trials()[0]
        tail = trial.iter_logs(tail=50)
        for line in tail:
            print(line)
        url = get_experiment_url(experiment_id)
        if fail_on_noncompletion:
            raise RuntimeError(f"Experiment {url} ended with state {state}.")
    return experiment_id


def _get_legacy_label(config: dict[str, Any]) -> str:
    label = config["task"]["name"]
    if "dataset" in config["task"]:
        label += "_" + config["task"]["dataset"]
    return label


class DeterminedEvalRun:
    """Object representing a collection of evaluation experiment on Determined.

    Note that this object may represent experiments in any status, i.e. it does not
    guarantee that the experiments have completed.

    Attributes:
        job_path: :class:`Path` to the mounted directory holding the job's outputs.
        label: The label for the job in the summarization.
        experiment_id: Determined's experiment ID.
        config: The configuration used to launch the experiment.
        task_name: The evaluation task name.
        dataset_name: The evaluation dataset name.
    """

    def __init__(
        self,
        job_path: Union[Path, str],
        label: str,
        experiment_id: int,
        config: dict[str, Any],
    ) -> None:
        """Creates a new :class:`DeterminedEvalRun` object.

        Args:
            job_path: :class:`Path` to the mounted directory holding the job's outputs.
            label: The label for the job in the summarization.
            experiment_id: Determined's experiment ID.
            config: The configuration used to launch the experiment.
        """
        self.job_path: Path = Path(job_path)
        self.label: str = label
        self.experiment_id: int = experiment_id
        self.config: dict[str, Any] = config
        self.task_name: str = self.config["task"]["name"]
        self.dataset_name: str = (
            self.config["task"]["dataset"] if "dataset" in self.config["task"] else ""
        )

    def wait(self) -> None:
        """Wait for the experiment to complete."""
        wait_experiment(self.experiment_id, fail_on_noncompletion=False)

    @property
    def status(self) -> str:
        """Return the status of the experiment."""
        experiment_state = describe_experiment(self.experiment_id, wait=False)
        status = experiment_state.value if experiment_state else "UNINITIALIZED"
        return status

    @property
    def results(self) -> dict:
        """Return a summary of the experiments."""
        if self.status != ExperimentState.COMPLETED.value:
            return {}

        results = get_experiment_results(self.job_path)

        if self.task_name in {"humaneval", "humaneval_fim", "mbpp", "aug_humaneval"}:
            results["overview_metric_name"] = "pass@1"
            results["overview_metric"] = results["metrics"]["pass_at_k"]["pass@1"]
        elif self.task_name == "humaneval_instruct":
            results["overview_metric_name"] = "pass@1"
            results["overview_metric"] = results["metrics"]["pass_at_1"]
        elif self.task_name == "api":
            results["overview_metric_name"] = "pass_count"
            results["overview_metric"] = 0
            if (
                "metric@hydra-unit-test-pass" in results
                and "passed" in results["metric@hydra-unit-test-pass"]
            ):
                results["overview_metric"] = results["metric@hydra-unit-test-pass"][
                    "passed"
                ]
        elif self.task_name == "hydra":
            results["overview_metric_name"] = "pass_count"
            results["overview_metric"] = 0
            if "exec_pass_counts" in results and "TOTAL" in results["exec_pass_counts"]:
                results["overview_metric"] = results["exec_pass_counts"]["TOTAL"]
        elif self.task_name == "cceval":
            results["overview_metric_name"] = "exact_match"
            results["overview_metric"] = results["metrics"]["total"]["exact_match"]
        elif self.task_name == "chat_eval_task":
            # chat_eval_task doesn't calculate quantitative metrics, but generates a report.html file
            results["overview_metric_name"] = "N/A"
            results["overview_metric"] = 0
        elif self.task_name == "augment_qa":
            results["overview_metric_name"] = "answer_keyword_recall"
            results["overview_metric"] = results["metrics"]["answer_keyword_recall"]
        else:
            raise ValueError(f"Unknown task name: {self.task_name}")
        return results

    def serialize(self) -> dict[str, Any]:
        """Return a dictionary representation of this object."""
        return {
            "status": self.status,
            "job_path": str(self.job_path),
            "label": self.label,
            "experiment_id": self.experiment_id,
            "config": self.config,
            "results": self.results,
        }

    @classmethod
    def deserialize(cls, serialization: dict[str, Any]) -> DeterminedEvalRun:
        """Create a :class:`DeterminedEvalRun` from a dictionary."""
        if "label" in serialization:
            label = serialization["label"]
        else:
            label = _get_legacy_label(serialization["config"])
        return cls(
            serialization["job_path"],
            label,
            serialization["experiment_id"],
            serialization["config"],
        )


def run_determined_inner(
    config: dict[str, Any],
    job_root: str,
    label: str,
    cluster: str | None = None,
    tolerations: dict[str, str] | None = None,
) -> DeterminedEvalRun:
    hostname = socket.gethostname()
    job_path = get_job_path(job_root, create=False)
    ns = job_path / hostname

    sys_name = config["system"]["name"]

    print(f"Evaluating system {sys_name}...")
    print(f"Check directory {job_path} for evaluation results")

    config["metadata"] = config.get("metadata", {})
    config["metadata"]["commit_hash"] = (
        subprocess.check_output(["git", "rev-parse", "HEAD"]).decode("utf-8").strip()
    )

    parallelize_task_n_ways = config.get("parallelize_task_n_ways", 1)
    if parallelize_task_n_ways > 1:
        assert sys_name == "agent"
        print(f"Parallelizing task {parallelize_task_n_ways} ways")

        config["task"]["num_workers"] = parallelize_task_n_ways
        experiment_ids = []
        for i in range(parallelize_task_n_ways):
            config["task"]["worker_idx"] = i
            experiment_id = launch_determined_eval(
                config=config, ns=ns, cluster=cluster, tolerations=tolerations
            )
            experiment_ids.append(experiment_id)
            print(f"Launched experiment {experiment_id} for worker {i}")

        print(
            f"When runs are done, please run:\n----\npython research/eval/swe_bench/agent_qa/swe_bench/consolidate_det_runs.py {' '.join(map(str, experiment_ids))}\n----\n"
        )

        return DeterminedEvalRun(
            job_path=job_path,
            label=label,
            experiment_id=experiment_ids[0],
            config=config,
        )
    else:
        experiment_id = launch_determined_eval(
            config=config, ns=ns, cluster=cluster, tolerations=tolerations
        )

        return DeterminedEvalRun(
            job_path=job_path, label=label, experiment_id=experiment_id, config=config
        )


def run_determined(
    configs: list[dict],
    job_root: str,
    skip_bazel: bool,
    cluster: str,
    tolerations: dict[str, str] | None = None,
) -> list[DeterminedEvalRun]:
    """Wrapper of `run_determined` for near-same-level abstraction of `run_local`."""
    if not skip_bazel:
        run_bazel()

    eval_runs: list[DeterminedEvalRun] = []
    for config in configs:
        label = config["determined"]["name"]
        # The call to run_determined will throw an exception if the experiment fails.
        eval_run = run_determined_inner(
            config, job_root, label, cluster, tolerations=tolerations
        )
        eval_runs.append(eval_run)
    return eval_runs


def build_config(
    config_files: list[str],
    additional_required_keys: set[str] | None = None,
    overrides: list[ConfigOverride] | None = None,
) -> dict:
    config = {}
    for config_file in config_files:
        with Path(config_file).open(encoding="utf-8") as f:
            cfg = yaml.safe_load(f)
            config.update(cfg)

    if overrides:
        config = apply_overrides(config, overrides)

    if "systems" in config or "tasks" in config:
        print(
            """
Warning: We are migrating to expect just 1 system and just 1 task per evaluation job.
This means:
- Some keys are renamed: 'systems' -> 'system', 'tasks' -> 'task'
- The above keys now just accept a dictionary, not a list of dictionaries, eg
system:
    name:
    ...
task:
    name:
    ...
Your config has been upgraded to the new format in order to enable backwards
compatibility.
        """
        )
        if "systems" in config:
            config["system"] = config.pop("systems")[0]
        if "tasks" in config:
            config["task"] = config.pop("tasks")[0]

    required_keys = {"system", "task"}

    if additional_required_keys is not None:
        required_keys |= additional_required_keys
    assert required_keys <= config.keys(), (
        "The configuration file does not contain all of the required keys: "
        f"{config.keys()} vs {required_keys}"
    )
    return config


def get_job_path(job_root: str, create=True):
    """If the default job root is used, uniquify it and fail if it exists."""
    assert isinstance(job_root, str), "`job_root` must be a string"
    if job_root == str(DEFAULT_JOB_ROOT):
        job_path = unique_job(job_root)
        if create:
            job_path.mkdir(parents=True, exist_ok=False)
    else:
        job_path = Path(job_root)
        if create:
            job_path.mkdir(parents=True, exist_ok=True)
    return job_path


def get_experiment_results(job_path: Path) -> dict[str, Any]:
    with (job_path / "000_results.jsonl").open("r") as file_:
        return json.load(file_)


def summarize(
    eval_runs: list[DeterminedEvalRun],
    existing_summary: dict[str, Any] | None = None,
    wait_for_completion: bool = False,
) -> dict[str, Any]:
    """Summarize the results of the eval runs into the summary json file."""
    summary = {
        "version": _SUMMARY_FORMAT_VERSION,
        "overview": {},
        "runs": {},
    }
    if existing_summary:
        assert (
            existing_summary["version"] == _SUMMARY_FORMAT_VERSION
        ), "Outdated summary format."
        summary.update(existing_summary)

    for index, eval_run in enumerate(eval_runs):
        if wait_for_completion:
            print(
                f"Waiting for job {len(eval_runs) - index}/{len(eval_runs)}:"
                f" {eval_run.label}."
            )
            eval_run.wait()
        results = eval_run.results
        summary["runs"][eval_run.label] = eval_run.serialize()
        if eval_run.status == ExperimentState.COMPLETED.value:
            overview_label = f"{eval_run.label}_{results['overview_metric_name']}"
            summary["overview"][overview_label] = results["overview_metric"]

    return summary


def complete_summary(
    summary_path: Path, wait_for_completion: bool = False
) -> dict[str, Any]:
    """Completes a summary file.

    If the summary file is already complete, returns the summary. If the summary file is
    incomplete, waits for the jobs to complete and returns the summary.
    """
    with summary_path.open() as file_:
        summary = json.load(file_)
    assert summary["version"] == _SUMMARY_FORMAT_VERSION, "Outdated summary format."
    eval_runs = [DeterminedEvalRun.deserialize(x) for x in summary["runs"].values()]
    final_summary = summarize(eval_runs, summary, wait_for_completion)
    return final_summary


def evaluate(
    config_files: list[str],
    summary_path: str = "",
    job_root: str = str(DEFAULT_JOB_ROOT),
    wait_for_completion: bool | None = False,
    override_summary: bool = False,
    model_name: str = "",
    checkpoint: str = "",
    podspec: str = "",
    job_name_suffix: str = "",
    dry_run: bool = False,
    skip_bazel: bool = False,
    cluster: str = "GCP-US1",
    local: bool = False,
    overrides: list[ConfigOverride] | None = None,
) -> dict[str, Any]:
    """Main logic for evaluation."""
    logger.info(
        "Running evaluation with args: %s",
        " ".join([f"--{k}={v}" for k, v in locals().items()]),
    )
    if wait_for_completion is None:
        wait_for_completion = bool(summary_path)
    if summary_path:
        summary_file_path = Path(summary_path)
        if summary_file_path.exists() and not override_summary:
            final_summary = complete_summary(summary_file_path, wait_for_completion)
            with summary_file_path.open("w", encoding="utf-8") as file_:
                json.dump(final_summary, file_, indent=4)
            return final_summary

    # Assume a locally non-existent location is a Determined storage ID.
    # TODO(zhuoran): If users are OK, migrate to requiring the S3 prefix for both
    # evaluation and fastbackward.
    if not Path(checkpoint).exists() and not (
        checkpoint.startswith("s3://") or checkpoint.startswith("gs://")
    ):
        checkpoint = f"s3://dev-training-dai/{checkpoint}"
    logger.info("Evaluating checkpoint %s...", checkpoint)

    additional_required_keys = None if local else {"determined", "podspec"}
    configs = get_configs(
        config_globs=config_files,
        model_name=model_name,
        checkpoint=checkpoint,
        podspec=podspec,
        job_name_suffix=job_name_suffix,
        overrides=overrides,
        additional_required_keys=additional_required_keys,
    )
    logger.debug("Found %s configs: %s", len(configs), json.dumps(configs, indent=2))
    if local:
        if summary_path:
            raise ValueError("Summary is not supported in local mode.")
        return run_local(configs, job_root, skip_bazel, dry_run)

    eval_runs = run_determined(configs, job_root, skip_bazel, cluster)

    # Save a running summary in case the script is interrupted.
    if summary_path:
        running_summary = summarize(eval_runs)
        with Path(summary_path).open("w", encoding="utf-8") as file_:
            json.dump(running_summary, file_, indent=4)

    summary = summarize(eval_runs, wait_for_completion=wait_for_completion)
    if summary_path:
        with Path(summary_path).open("w", encoding="utf-8") as file_:
            json.dump(summary, file_, indent=4)
    return summary


@dataclass
class ConfigOverride:
    """Specifies part of the JSON data to be overriden via key paths."""

    key_path: list[str]
    """A key path to the JSON data to be overriden."""
    value: Any
    """The new value."""

    @staticmethod
    def from_yaml_str(raw: str) -> "ConfigOverride":
        key_path, value = raw.split("=", 1)
        key_path = key_path.split(".")
        value = yaml.safe_load(value)
        return ConfigOverride(key_path, value)


def apply_overrides(config: dict, overrides: list[ConfigOverride]) -> dict:
    """Apply a list of overrides to a config. Note: the config is modified."""
    for override in overrides:
        # cut parents from the final leaf key
        parents, leaf = override.key_path[:-1], override.key_path[-1]

        # recurse into nested config
        cur = config
        for parent in parents:
            cur = cur.setdefault(parent, {})

        # set the leaf key to value
        cur[leaf] = override.value
    return config

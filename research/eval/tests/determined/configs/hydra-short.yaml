system:
  name: basic_rag
  model:
    name: starcoderbase_1b_fastforward
    prompt:
      max_prefix_tokens: 1768
      max_suffix_tokens: 0
      max_prompt_tokens: 1768
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    name: bm25
    chunker:
      name: line_level
      max_lines_per_chunk: 20
    max_query_lines: 4
  experimental:
    trim_on_dedent: True

task:
  name: hydra
  dataset: repoeval_functions
  repos: ["leopard-ai/betty"]

podspec: l4.yaml

determined:
  name: Hydra Eval Test
  workspace: GH
  project: eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

augment:
  project_group: automation

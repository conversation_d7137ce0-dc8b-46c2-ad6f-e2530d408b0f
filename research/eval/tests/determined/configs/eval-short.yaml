# short eval unit test
# This eval is tuned for the QUADRO_5000, which is the same thing the standard
# gh runner pod uses.
checkpoints:
  - path: /mnt/efs/augment/checkpoints/codegen-2B.ida.batch_distract.2022-10-11/
    podspec: QUADRO_5000.yaml

# Tasks
#   specify the evaluation tasks for each checkpoint

tasks:
  - gitrepo-java_projects/1160-Scouting-App

# Podspec - set the default podspec for all checkpoints
# The default podspec provides a single RTX_A5000 GPU
podspec: QUADRO_5000.yaml

# neox_args are passed as overrides to the neox configuration found in the checkpoints.
neox_args:
  eval_with_memories: True
  eval_batch_size: 1

# Determined
# name, workspace, project control location and display in the determined UI.

determined:
  name: GH Eval Short
  description: Short Eval Test
  workspace: GH
  project: eval
  # relative to research/gpt-neox
  metaconfig: jobs/templates/batch-eval.yaml

# Optional dictionary of tags added to the eval results json file.
# eval_tags: {}

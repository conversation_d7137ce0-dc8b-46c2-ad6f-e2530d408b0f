#
# This file contains an example of an evaluation config
#

# Sections:
#   Systems - specify the system configuration to evaluate
#   Tasks - specify the evaluation tasks for each system
#   Podspec - overide the default podspec, if necessary
#   Determined - name, workspace, project in the determined UI.

systems:
  - name: basic_rag
    model:
      name: starcoderbase_1b
      prompt:
        max_prefix_tokens: 1024
        max_suffix_tokens: 0
        max_prompt_tokens: 1768
        fill_to_context_window: True
      # Model does validation of inputs so only want arguments the model recognizes
    retriever:
      name: bm25
      chunker: line_level
      max_chunk: 20
    generation_options:
      max_generated_tokens: 280
    experimental:
      remove_suffix: False



# Tasks
#   specify the evaluation tasks for each checkpoint
#
tasks:
  - name: hydra
    # dataset: function
    limit: 10
    exec: False

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: A40.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: Hydra - 1B, BM25 New Test Harness
  workspace: Dev
  project: playground
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

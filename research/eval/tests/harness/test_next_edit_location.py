import textwrap
from typing import Sequence

import pytest

from base.ranges.range_types import <PERSON>Range
from research.core import diff_utils
from research.core.next_edit_location_prompt_input import (
    FileLocation,
    NextEditLocationOutput,
)
from research.core.types import Scored
from research.eval.harness.tasks.next_edit_location_eval_task import (
    RankingMetrics,
    compute_metrics,
    compute_modified_ranges,
)


def fake_location(
    path: str = "a.py",
    range: LineRange = LineRange(0, 1),  # noqa
) -> FileLocation:
    return FileLocation(path=path, range=range)


def fake_output(locations: list[Scored[FileLocation]]) -> NextEditLocationOutput:
    return NextEditLocationOutput(
        scored_candidates=locations,
        debug_info={},
    )


@pytest.mark.parametrize(
    "output, golden, expected",
    [
        # Perfect suggestion
        (
            fake_output([Scored[FileLocation](item=fake_location(), score=42)]),
            [fake_location()],
            (
                RankingMetrics(
                    mean_rr=1.0,
                    mean_ap=1.0,
                    mean_rank=1.0,
                    mean_top_rank=1.0,
                    mean_bottom_rank=1.0,
                    mean_p1=1.0,
                    mean_gold_prob=1.0,
                    mean_recall=1.0,
                    mean_hard_recall=1.0,
                    count=1,
                ),
                [True],
            ),
        ),
        # Empty (wrong) suggestion
        (
            fake_output([]),
            [fake_location()],
            (
                RankingMetrics(
                    mean_rr=0.0,
                    mean_ap=0.0,
                    mean_rank=257.0,
                    mean_top_rank=257.0,
                    mean_bottom_rank=257.0,
                    mean_p1=0.0,
                    mean_gold_prob=0.0,
                    mean_recall=0.0,
                    mean_hard_recall=0.0,
                    count=1,
                ),
                [],
            ),
        ),
        # Larger suggestion
        (
            fake_output(
                [
                    Scored(item=fake_location(), score=42),
                    Scored(item=fake_location(path="b.py"), score=42),
                    Scored(item=fake_location(path="c.py"), score=42),
                    Scored(item=fake_location(path="d.py"), score=42),
                ]
            ),
            [
                fake_location(path="b.py"),
                fake_location(path="d.py"),
            ],
            (
                RankingMetrics(
                    mean_rr=1 / 2,
                    mean_ap=(1 / 2 + 2 / 4) / 2,
                    mean_rank=2.5,
                    mean_top_rank=2,
                    mean_bottom_rank=3,
                    mean_p1=0.0,
                    mean_gold_prob=0.25,
                    mean_recall=1.0,
                    mean_hard_recall=1.0,
                    count=1,
                ),
                [False, True, False, True],
            ),
        ),
    ],
)
def test_metrics(
    output: NextEditLocationOutput,
    golden: Sequence[FileLocation],
    expected: RankingMetrics,
):
    metrics = compute_metrics(output, golden, top_k=256)

    assert metrics == expected


@pytest.mark.parametrize("ignore_whitespace", [True, False])
def test_compute_modified_ranges(ignore_whitespace: bool):
    wip_repo = diff_utils.Repository(
        [
            diff_utils.File(
                "a",
                textwrap.dedent("""\
                    0
                    1
                    2
                    3
                    4
                    5
                    """),
            ),
            diff_utils.File("deleted", "deleted-text."),
            diff_utils.File(
                "moved-from",
                # NOTE(arun): Adding a bunch of lines that don't change so that git's
                # diff logic registers this as a file rename.
                (
                    "lorem ipsum dolors\n" * 20
                    + "text that will change.\n"
                    + "lorem ipsum dolors\n" * 20
                ),
            ),
        ]
    )
    future_repo = diff_utils.Repository(
        [
            diff_utils.File(
                "a",
                textwrap.dedent("""\
                    added
                    0
                    1
                    3
                    changed
                    5
                    added'
                    """),
            ),
            diff_utils.File("added", "added-stuff."),
            diff_utils.File(
                "moved-to",
                (
                    "lorem ipsum dolors\n" * 20
                    + "text that changed.\n"
                    + "lorem ipsum dolors\n" * 20
                ),
            ),
        ]
    )

    changed_ranges = compute_modified_ranges(wip_repo, future_repo, ignore_whitespace)
    assert changed_ranges == [
        FileLocation(path="a", range=LineRange(0, 0)),  # added
        FileLocation(path="a", range=LineRange(2, 3)),  # deleted
        FileLocation(path="a", range=LineRange(4, 5)),  # changed
        FileLocation(path="a", range=LineRange(5, 5)),  # added'
        FileLocation(path="deleted", range=LineRange(0, 1)),
        FileLocation(path="moved-from", range=LineRange(20, 21)),
    ]


def test_compute_modified_ranges_with_whitespace():
    wip_repo = diff_utils.Repository(
        [
            diff_utils.File(
                "a",
                textwrap.dedent("""\
                    0
                    1
                      2 + 2
                      3 - 3
                      4 * 4
                    5
                    """),
            ),
        ]
    )
    future_repo = diff_utils.Repository(
        [
            diff_utils.File(
                "a",
                textwrap.dedent("""\
                    added
                    0
                      2 + 2
                    3-3
                      4*4
                    5
                    added'
                    """),
            ),
        ]
    )

    changed_ranges = compute_modified_ranges(
        wip_repo, future_repo, ignore_whitespace=False
    )
    assert changed_ranges == [
        FileLocation(path="a", range=LineRange(0, 0)),  # added
        FileLocation(path="a", range=LineRange(1, 2)),  # deleted
        FileLocation(path="a", range=LineRange(3, 5)),  # changed with whitespace.
        FileLocation(path="a", range=LineRange(5, 5)),  # added'
    ]

    changed_ranges = compute_modified_ranges(
        wip_repo, future_repo, ignore_whitespace=True
    )
    assert changed_ranges == [
        FileLocation(path="a", range=LineRange(0, 0)),  # added
        FileLocation(path="a", range=LineRange(1, 2)),  # deleted
        # Ignores the changes in whitespace.
        FileLocation(path="a", range=LineRange(5, 5)),  # added'
    ]

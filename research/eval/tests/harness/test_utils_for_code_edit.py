"""Tests for the util_lib module.

pytest research/eval/tests/harness/test_utils_for_code_edit.py
"""

from research.core.edit_prompt_input import ResearchEditPromptInput
from research.eval.harness.systems.utils_for_code_edit import (
    add_code_edit_default_extras,
    postprocess_model_response_for_edit,
)


def test_postprocess_model_response_for_edit():
    """Test the postprocess_model_response_for_edit func."""

    assert (
        postprocess_model_response_for_edit(
            """
# Title

This is a markdown block.

```python
print("hello")
```

This is another markdown block.

```python
print("world")
```
"""
        )
        == """print("world")"""
    )

    assert (
        postprocess_model_response_for_edit(
            """
```javascript
  }
}

@@@@@@@@@
function folder (arg, options, subCommand) {
  const operation = operations[subCommand._name]
  if(operation){
    operation(arg, options)
  } else {
    console.log('Invalid operation');
  }
&&&&&&&&&

module.exports = folder
```
)""",
            start_symbols="@@@@@@@@@",
            end_symbols="&&&&&&&&&",
            prefix="",
            suffix="\nmodule.exports = folder\n",
        )
        == """function folder (arg, options, subCommand) {
  const operation = operations[subCommand._name]
  if(operation){
    operation(arg, options)
  } else {
    console.log('Invalid operation');
  }
"""
    )

    assert (
        postprocess_model_response_for_edit(
            """

```python
    messages,
    system_prompt,
    temperature = 0.2,
    max_tokens = 256,
    model = "gpt-4"
```
"""
        )
        == """
    messages,
    system_prompt,
    temperature = 0.2,
    max_tokens = 256,
    model = "gpt-4"
""".strip("\n")
    )

    assert (
        postprocess_model_response_for_edit(
            """
class Model:
    def __init__(self, home):
        self.home = home

    def build_graph(self):
        x = tf.placeholder(tf.float32, [None, 64, 64, 3], name='x')
        y = tf.placeholder(tf.float32, shape=[None])
        dropout_prob = tf.placeholder(tf.float32, name='dropout_prob')

        filter1 = tf.get_variable('filter1', [3, 3, 3, 32])
        filter2 = tf.get_variable('filter2', [5, 5, 32, 64])

******************************
        h = tf.nn.conv2d(x, filter1, [1, 2, 2, 1], 'SAME')
        h = tf.nn.relu(h)
        h = tf.nn.max_pool(h, [1, 3, 3, 1], [1, 2, 2, 1], 'SAME')
******************************

        h = tf.nn.conv2d(h, filter2, [1, 2, 2, 1], 'SAME')
        h = tf.nn.relu(h)
        h = tf.nn.max_pool(h, [1, 3, 3, 1], [1, 2, 2, 1], 'SAME')
```)
""",
            start_symbols="******************************",
            end_symbols="******************************",
        )
        == """        h = tf.nn.conv2d(x, filter1, [1, 2, 2, 1], 'SAME')
        h = tf.nn.relu(h)
        h = tf.nn.max_pool(h, [1, 3, 3, 1], [1, 2, 2, 1], 'SAME')
"""
    )

    assert (
        postprocess_model_response_for_edit(
            """
```
const client = require('./lib/client')

@@@@@@@@@
const operations = {
  get: async (folderId) => {
    const folder = await client.folders.get(folderId)
    console.log(folder)
  },
  create: async (folderName, { parent }) => {
    const folder = await client.folders.create(parent, folderName)
    console.log(folder)
  },
  delete: async (folderId) => {
    await client.folders.delete(folderId)
    console.log('Folder deleted')
  }
}
&&&&&&&&&

function folder (arg, options, subCommand) {
  const operation = operations[subCommand._name]
```
""",
            start_symbols="@@@@@@@@@",
            end_symbols="&&&&&&&&&",
            prefix="const client = require('./lib/client')\n\n",
            suffix="\nfunction folder (arg, options, subCommand) {\n  const operation = operations[subCommand._name]\n  operation(arg, options)\n}\n\nmodule.exports = folder\n",
        )
        == """const operations = {
  get: async (folderId) => {
    const folder = await client.folders.get(folderId)
    console.log(folder)
  },
  create: async (folderName, { parent }) => {
    const folder = await client.folders.create(parent, folderName)
    console.log(folder)
  },
  delete: async (folderId) => {
    await client.folders.delete(folderId)
    console.log('Folder deleted')
  }
}
"""
    )

    assert (
        postprocess_model_response_for_edit(
            """
```
PATH = '~/datasets/edit/commitpackft/data.jsonl'

@@@@@@@@@
from pathlib import Path
data = []

with Path(PATH).open() as f:
    for line in f:
&&&&&&&&&
        datum = json.loads(line[:-1])
        data.append(datum)
```""",
            start_symbols="@@@@@@@@@",
            end_symbols="&&&&&&&&&",
            prefix="PATH = '~/datasets/edit/commitpackft/data.jsonl'\n\n",
            suffix="        datum = json.loads(line[:-1])\n        data.append(datum)\n",
        )
        == """from pathlib import Path
data = []

with Path(PATH).open() as f:
    for line in f:
"""
    )

    assert (
        postprocess_model_response_for_edit(
            """                else:
                    logger.error(
                        "Request has no tokens to process",
                        request_id=request.request_id,
                    )
                    request.mark_error(
                        InferenceException(
                            grpc.StatusCode.UNKNOWN, "Request has no tokens to process."
                        )
                    )
                assert request.is_done()
                requests_to_remove.add(r_idx)
                request.release_cache()
                continue
""",
            prefix="""                else:
                    logger.error(
                        "Request has no tokens to process",
                        request_id=request.request_id,
                    )
                    request.mark_error(
                        InferenceException(
                            grpc.StatusCode.UNKNOWN, "Request has no tokens to process."
                        )
                    )
""",
            suffix="""                requests_to_remove.add(r_idx)
                request.release_cache()
                continue
""",
        )
        == "                assert request.is_done()\n"
    )


def test_add_code_edit_default_extras():
    test_input = ResearchEditPromptInput(
        path="",
        prefix="",
        selected_code="",
        suffix="",
        instruction="",
        retrieved_chunks=[],
        updated_code=None,
    )
    input = add_code_edit_default_extras(
        test_input,
        default_selected_code="print('hello')",
        default_instruction="print('world')",
    )
    # Locking in current behavior
    assert input.selected_code == "\tprint('hello')"
    assert input.instruction == "print('world')"

    input = add_code_edit_default_extras(
        test_input, default_selected_code="", default_instruction=""
    )
    assert input.selected_code == test_input.selected_code
    assert input.instruction == test_input.instruction

    input = add_code_edit_default_extras(
        test_input, default_selected_code=None, default_instruction=None
    )
    assert input.selected_code == test_input.selected_code
    assert input.instruction == test_input.instruction

import logging
from pathlib import Path

from research.core.chat_prompt_input import ResearchChatPromptInput
from research.core.data_paths import canonicalize_path
from research.core.types import Document
from research.core.utils_for_file import read_jsonl_zst
from research.eval.harness.systems.remote_chat_system import RemoteChatSystem
import pytest


@pytest.mark.manual
def test_chat_demo():
    DOCS = canonicalize_path(
        "data/processed/human_eval_instruct.v1/augment_jun_08_2024.json.zst"
    )

    remote_chat_config = {
        "client": {
            "url": "https://dogfood.api.augmentcode.com",
        },
    }

    chat_model_input = {
        "message": "what is the data structure for code edit samples in base/datasets",
        "path": "",
        "prefix": "",
        "selected_code": "",
        "suffix": "",
        "chat_history": [],
        "prefix_begin": 0,
        "suffix_end": 0,
        "retrieved_chunks": [],
    }
    chat_model_input = ResearchChatPromptInput(**chat_model_input)

    def prepare_retrieval_docs(path: Path) -> list[Document]:
        doc_set_obj = read_jsonl_zst(path)
        doc_set = doc_set_obj[0]["docs"]
        all_docs = [Document.new(text=doc["text"], path=doc["path"]) for doc in doc_set]
        # Filter .git files from the docs
        docs = [doc for doc in all_docs if ".git" not in Path(doc.path).parts]
        if len(docs) != len(all_docs):
            logging.info(
                "Filtered %d docs from .git directory", len(all_docs) - len(docs)
            )
        return docs

    src_files = prepare_retrieval_docs(Path(DOCS))
    system = RemoteChatSystem.from_yaml_config(remote_chat_config)
    system.load()
    system.add_docs(src_files)
    result = system.generate(chat_model_input)
    system.unload()
    logging.info("%s", result.generated_text)
    assert "EditDatum" in result.generated_text

import pytest

from research.eval.eval_lib import ConfigOverride, apply_overrides


@pytest.mark.parametrize(
    "raw, expected",
    [
        ("a.b.c=10", ConfigOverride(["a", "b", "c"], 10)),
        ("a.b.c=foo", ConfigOverride(["a", "b", "c"], "foo")),
        ("a.b.c=true", ConfigOverride(["a", "b", "c"], True)),
        ("a={foo: bar}", ConfigOverride(["a"], {"foo": "bar"})),
    ],
)
def test_config_override_from_yaml_str(raw, expected):
    assert ConfigOverride.from_yaml_str(raw) == expected


@pytest.mark.parametrize(
    "config, overrides, expected",
    [
        (
            {"a": {"b": {"c": 1}}, "d": {"e": {"f": 2}}},
            [ConfigOverride(["a", "b", "c"], 10)],
            {"a": {"b": {"c": 10}}, "d": {"e": {"f": 2}}},
        ),
        (
            {"a": {"b": {"c": 1}}, "d": {"e": {"f": 2}}},
            [ConfigOverride(["a", "b", "d"], 10)],
            {"a": {"b": {"c": 1, "d": 10}}, "d": {"e": {"f": 2}}},
        ),
        (
            {"a": {"b": {"c": 1}}, "d": {"e": {"f": 2}}},
            [ConfigOverride(["d", "e", "f"], "x"), ConfigOverride(["a", "b", "c"], 42)],
            {"a": {"b": {"c": 42}}, "d": {"e": {"f": "x"}}},
        ),
        (
            {"a": {"b": {"c": 1, "d": 2}}, "e": {"f": 3}},
            [ConfigOverride(["a", "b"], {"x": 10, "y": 20})],
            {"a": {"b": {"x": 10, "y": 20}}, "e": {"f": 3}},
        ),
    ],
)
def test_apply_overrides(config, overrides, expected):
    assert apply_overrides(config, overrides) == expected

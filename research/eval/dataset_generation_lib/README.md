# Dataset Generation Library

This library enables researchers to build datasets of patches against which to run Hydra evaluations.

Once repositories our supported by Hydra (ie they have images in our Docker registry), then this library can be used to:
- Pull repository contents from our Docker registry and generate patch suggestions.
- Generate patch suggestions of different kins (function-level, random multiline, etc).
- Run filters on suggested patches to filter out low-quality patches, and excessively easy patches.
- Dehydrate and save patches to our git repository.
- Run all of the above in end-to-end pipeline via script or notebook.

## Example scripts and notebooks

Included in this library are some examples of how to use it. You will be able to use these artifacts will minimal changes

to create datasets for new repositories.
- `dataset_generation_lib/notebooks_and_scripts/single_repo_patches_generation.ipynb`: End-to-end Construction of dataset for single repo, in a notebook.
- `dataset_generation_lib/notebooks_and_scripts/single_repo_patches_generation.py`: End-to-end Construction of dataset for single repo, in a script.
- `dataset_generation_lib/notebooks_and_scripts/run_weak_system_on_existing_dataset.py`: Run the weak system filter over an entire dataset and dump result to our git.repository.


## Patch Generators

The core logic for generations candidate patches can be found in `dataset_generation_lib/basic_patch_generators.py`.

There are a couple of supported generators:
- `all_functions_from_path`: Generate functions.
- `fim_sample`: Generate patches based on our FIMv1 sampler.
- `get_lines_from_function`: Construct multiline patches uniformly sampled from functions.
    - You run this function on the output of `all_functions_from_path`.
- `get_last_n_lines_from_function`: Construct multiline patches from end of functions.
    - You run this function on the output of `all_functions_from_path`.

## Patch Filters

The core logic for filtering low-quality or otherwise unwanted candidate patches can be found in
`dataset_generation_lib/basic_patch_filters.py`.

Most of the filters run various systems against the patches and filter patches based on the Hydra
results of those systems. In `dataset_generation_lib/utils.py`, there are functions
`run_system_filter` and `run_system_filter_entire_dataset` that enable this workflow in both
notebooks and scripts.

There are a variety of supported filters:
- `get_gold_system_config`: Return a configuration for a gold system. Keep passing output.
- `get_failing_assert_system_config`: Return a configuration for a failing assert system. Keep failing output.
- `get_null_system_config`: Return a configuration for a null system. Keep failing output.
- `get_weak_model_no_retrieval_config`: Return a configuration for a "weak" model, to be used to filter out easy patches. Keep failing output.

Then, there are a couple of other utilities:
- `filter_by_exact_string_match`: Filter by exact string match.
- `uniform_reservoir_sample`: Uniform reservoir sampling from a stream of patches.

## Utilities for creating scripts and notebooks

In `dataset_generation_lib/utils.py`, there are various utilities for
creating scripts and notebooks.
- `run_system_filter` and `run_system_filter_entire_dataset` run various systems against
    the patches and filter patches based on the Hydra results of those systems.
- `assert_system_result`: Assert that a system has a particular output for all patches.
- `advance_symlink`: Update a symlink to point to a new file. This is useful for creating notebooks
    and scripts that can be restarted without rerunning already completed cells.

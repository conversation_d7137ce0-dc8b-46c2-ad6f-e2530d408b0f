"""Get different systems."""

from __future__ import annotations

import pathlib

from research.eval.dataset_generation_lib.exps.xlib import (
    BasicPostProcessor,
    BasicPreProcessor,
    GeneralizedSystem,
    RAGPreProcessor,
    TrimCompletion,
)
from research.eval.harness.factories import create_retriever
from research.models import GenerationOptions
from research.models.all_models import get_model


def get_basic_system(
    model_name: str,
    model_ckp: str | pathlib.Path = "",
    max_prefix_tokens: int = 1024,
    max_suffix_tokens: int = 1024,
    max_prompt_tokens: int = 4096,
    use_trim: bool = False,
) -> GeneralizedSystem:
    model = get_model(model_name, checkpoint_path=model_ckp)
    model.prompt_formatter.max_prefix_tokens = max_prefix_tokens  # type: ignore
    model.prompt_formatter.max_suffix_tokens = max_suffix_tokens  # type: ignore
    model.prompt_formatter.max_prompt_tokens = max_prompt_tokens
    model.prompt_formatter.max_retrieved_chunk_tokens = 0  # type: ignore
    model.prompt_formatter.always_fim_style = True  # type: ignore
    # model.prompt_formatter.retrieval_layout_style = "comment2"
    generation_options = GenerationOptions(
        temperature=0,
        top_k=0,
        top_p=0,
        max_generated_tokens=512,
    )
    system = GeneralizedSystem(
        model,
        None,
        preprocessor=BasicPreProcessor(),
        postprocessor=TrimCompletion() if use_trim else BasicPostProcessor(),
        generation_options=generation_options,
    )
    return system


def get_bm25_system(
    model_name: str,
    model_ckp: str | pathlib.Path = "",
    max_prefix_tokens: int = 2048,
    max_suffix_tokens: int = 2048,
    max_retrieved_chunk_tokens: int = 2048,
    max_prompt_tokens: int = 6200,
) -> GeneralizedSystem:
    model = get_model(model_name, checkpoint_path=model_ckp)
    model.prompt_formatter.max_prefix_tokens = max_prefix_tokens  # type: ignore
    model.prompt_formatter.max_suffix_tokens = max_suffix_tokens  # type: ignore
    model.prompt_formatter.max_prompt_tokens = max_prompt_tokens
    model.prompt_formatter.max_retrieved_chunk_tokens = max_retrieved_chunk_tokens  # type: ignore
    model.prompt_formatter.always_fim_style = True  # type: ignore
    model.prompt_formatter.retrieval_layout_style = "comment2"  # type: ignore
    retriever_config = {
        "name": "bm25",
        "chunker": "line_level",
        "max_chunk": 40,
        "max_query_lines": 10,
    }
    retriever = create_retriever(retriever_config)

    generation_options = GenerationOptions(
        temperature=0,
        top_k=0,
        top_p=0,
        max_generated_tokens=512,
    )
    system = GeneralizedSystem(
        model,
        retriever,
        preprocessor=RAGPreProcessor(),
        postprocessor=BasicPostProcessor(),
        generation_options=generation_options,
    )
    return system


def get_system(name: str) -> GeneralizedSystem:
    if name == "basic-sc-1b":
        return get_basic_system("starcoderbase_1b")
    elif name == "basic-sc-3b":
        return get_basic_system("starcoderbase_3b")
    elif name == "trim-sc-3b":
        return get_basic_system("starcoderbase_3b", use_trim=True)
    elif name == "basic-sc-7b":
        return get_basic_system("starcoderbase_7b")
    elif name == "basic-sc-16b":
        return get_basic_system("starcoderbase_16b")
    elif name == "fimv2-16b-4kl":
        return get_basic_system(
            "starcoderbase_16b",
            model_ckp=pathlib.Path(
                "/mnt/efs/augment/checkpoints/fima-v2/fim-v2.4-starcoder16B-python-1M_batch-1Mfiles"
            ),
            max_prefix_tokens=2048,
            max_suffix_tokens=2048,
            max_prompt_tokens=5120,
        )
    elif name == "rogue-6k":
        return get_bm25_system(
            "starcoderbase_16b",
            model_ckp=pathlib.Path("/mnt/efs/augment/checkpoints/rogue/pydiff2m_7b"),
            max_prefix_tokens=2048,
            max_suffix_tokens=2048,
            max_retrieved_chunk_tokens=2048,
            max_prompt_tokens=6200,
        )
    else:
        raise ValueError(f"Unknown name: {name}")

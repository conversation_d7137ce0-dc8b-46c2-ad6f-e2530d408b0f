"""."""

from __future__ import annotations

import abc
import copy

import torch

from research.core.abstract_prompt_formatter import AbstractPromptFormatter
from research.core.model_input import ModelInput
from research.core.ui_sugar import UISugar
from research.eval.harness.systems.abs_system import (
    CodeCompleteSystem,
    CompletionResult,
)
from research.eval.harness.systems.libraries.completion_trimmers import trim_on_dedent
from research.models import (
    ExtraGenerationOutputs,
    GenerationOptions,
    GenerativeLanguageModel,
)
from research.retrieval.types import Document, DocumentIndex


class AbstractPreProcessor(UISugar):
    """The Abstract PreProcessor for the system."""

    @abc.abstractmethod
    def __call__(
        self,
        model_input: ModelInput,
        retriever: DocumentIndex | None = None,
        prompt_formatter: AbstractPromptFormatter | None = None,
    ) -> ModelInput:
        """."""


class BasicPreProcessor(AbstractPreProcessor):
    """The Basic PostProcessor for the system."""

    def __call__(
        self,
        model_input: ModelInput,
        retriever: DocumentIndex | None = None,
        prompt_formatter: AbstractPromptFormatter | None = None,
    ) -> ModelInput:
        return model_input


class RAGPreProcessor(BasicPreProcessor):
    """."""

    retriever_top_k: int = 25

    def __call__(
        self,
        model_input: ModelInput,
        retriever: DocumentIndex | None = None,
        prompt_formatter: AbstractPromptFormatter | None = None,
    ) -> ModelInput:
        model_input = copy.deepcopy(model_input)
        if retriever is None or prompt_formatter is None:
            raise ValueError("The retriever or prompt_formatter is None.")
        with torch.inference_mode():
            model_input.retrieved_chunks, _ = retriever.query(
                ModelInput(
                    prefix=model_input.prefix,
                    suffix="",
                    path=model_input.path,
                ),
                top_k=self.retriever_top_k,
            )
        return model_input


class AbstractPostProcessor(UISugar):
    """The Abstract PostProcessor for the system."""

    @abc.abstractmethod
    def __call__(self, completion: str, model_input: ModelInput | None = None) -> str:
        """."""


class BasicPostProcessor(AbstractPostProcessor):
    """The Basic PostProcessor for the system."""

    def __call__(self, completion: str, model_input: ModelInput | None = None) -> str:
        return completion


class TrimCompletion(AbstractPostProcessor):
    def __call__(self, completion: str, model_input: ModelInput | None = None) -> str:
        return trim_on_dedent(completion)


class GeneralizedSystem(CodeCompleteSystem):
    """A Generalized Single Model-based System."""

    model: GenerativeLanguageModel
    """The LM model used to make prediction."""

    retriever: DocumentIndex | None = None
    """The retriever to retrieve the relevant code snippet."""

    preprocessor: AbstractPreProcessor

    postprocessor: AbstractPostProcessor

    generation_options: GenerationOptions

    def __init__(
        self,
        model: GenerativeLanguageModel,
        retriever: DocumentIndex | None = None,
        preprocessor: AbstractPreProcessor = BasicPreProcessor(),
        postprocessor: AbstractPostProcessor = BasicPostProcessor(),
        generation_options: GenerationOptions = GenerationOptions(
            max_generated_tokens=64
        ),
    ):
        super().__init__()
        self.model = model
        self.retriever = retriever
        self.preprocessor = preprocessor
        self.postprocessor = postprocessor
        self.generation_options = generation_options
        self.__loaded = False

    def load(self):
        if not self.__loaded:
            self.model.load()
            if hasattr(self.retriever, "scorer"):
                self.retriever.scorer.load()  # type: ignore
        self.__loaded = True

    def unload(self):
        if self.__loaded:
            self.model.unload()
            if hasattr(self.retriever, "scorer"):
                self.retriever.scorer.unload()  # type: ignore
            self.__loaded = False

    def add_docs(self, src_files: list[Document]):
        if self.retriever is not None:
            self.retriever.add_docs(src_files)

    def clear_retriever(self):
        """Clear any stored documents from the retriever."""
        if self.retriever is not None:
            self.retriever.remove_all_docs()

    def generate(
        self,
        model_input: ModelInput,
    ) -> CompletionResult:
        """Generate a completion."""
        model_input = self.preprocessor(
            model_input, self.retriever, self.model.prompt_formatter
        )
        extra_out = ExtraGenerationOutputs()
        generation = self.model.generate(
            model_input, self.generation_options, extra_out
        )
        final_generation = self.postprocessor(generation, model_input)
        return CompletionResult(
            generated_text=final_generation,
            prompt_tokens=extra_out.prompt_tokens or [],
            retrieved_chunks=model_input.retrieved_chunks,
        )

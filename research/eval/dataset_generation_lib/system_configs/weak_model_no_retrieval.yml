system:
  name: basic_rag
  model:
    name: starcoderbase_1b
    prompt:
      always_fim_style: true
      max_prefix_tokens: 512
      max_suffix_tokens: 512
      max_prompt_tokens: 1024
      retrieval_layout_style: comment2
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    name: null
    chunker: line_level
    max_chunk: 40
    max_query_lines: 20
  experimental:
    retriever_top_k: 0
    trim_on_dedent: true
    trim_on_max_lines: 3
    use_fim_when_possible: true

task:
  name: hydra
  dataset_path: null
  hydra_block_resource_internet_access: True


podspec: gpu-small.yaml

determined:
  name: null
  workspace: Dev
  project: playground
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

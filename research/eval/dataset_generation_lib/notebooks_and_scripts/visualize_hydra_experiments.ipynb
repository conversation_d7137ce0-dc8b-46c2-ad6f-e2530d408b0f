{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Use this notebook to visualize and compare Hydra experiments."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Imports and Utilities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "import importlib\n", "from pathlib import Path\n", "\n", "import ipywidgets as widgets\n", "import pandas as pd\n", "from IPython.display import HTML, clear_output, display\n", "\n", "from research.eval.harness import utils\n", "from research.eval.patch_lib import Patch\n", "\n", "\n", "@dataclasses.dataclass\n", "class Experiment:\n", "    poc: str\n", "    eval_dir: str\n", "    description: str = \"\"\n", "    weight: float = 1.0\n", "\n", "\n", "def load(experiment: Experiment):\n", "    \"\"\"Returns dict[patch_id, results].\"\"\"\n", "    eval_dir = experiment.eval_dir\n", "    results = read_results(eval_dir)\n", "    patch_results = read_jsonl_zst(eval_dir) or read_pkl(eval_dir)\n", "    if len(patch_results) != len(results):\n", "        raise ValueError(\"Inconsistent records!\")\n", "    metadata = {}\n", "    # return patch_results, results\n", "    for pr, r in zip(patch_results, results):\n", "        patch_obj = pr[\"patch\"]\n", "        run_metadata = {\n", "            \"prompt\": pr[\"prompt\"],\n", "            \"generation\": pr[\"generation\"],\n", "            \"completion\": pr[\"completion\"],\n", "            \"ground_truth\": r[\"file_content\"][r[\"char_start\"] : r[\"char_end\"]],\n", "            \"filename\": patch_obj.file_name\n", "            if isinstance(patch_obj, <PERSON>)\n", "            else patch_obj[\"file_name\"],\n", "            \"file_content\": patch_obj.file_content\n", "            if isinstance(patch_obj, <PERSON>)\n", "            else patch_obj[\"file_content\"],\n", "            \"result\": r[\"_extra\"][\"result\"],\n", "            \"run_output\": r[\"_extra\"][\"run_output\"],\n", "            \"patch_rst\": pr,\n", "            \"json_rst\": r,\n", "        }\n", "\n", "        if (\n", "            \"retrieval_metadata\" in pr\n", "            and \"retriever_prompt\" in pr[\"retrieval_metadata\"]\n", "        ):\n", "            run_metadata[\"retriever_prompt\"] = pr[\"retrieval_metadata\"][\n", "                \"retriever_prompt\"\n", "            ]\n", "        else:\n", "            run_metadata[\"retriever_prompt\"] = \"\"\n", "        metadata[r[\"patch_id\"]] = run_metadata\n", "    return metadata\n", "\n", "\n", "def read_jsonl_zst(eval_dir: str):\n", "    \"\"\"Reads patch results from the jsonl.zst file.\"\"\"\n", "    matched = list(Path(eval_dir).glob(\"*_completed_patches.jsonl.zst\"))\n", "    if len(matched) != 1:\n", "        return None\n", "    path = matched[0]\n", "    return utils.read_jsonl_zst(path)\n", "\n", "\n", "def read_pkl(eval_dir: str):\n", "    \"\"\"Reads patch results from the pickle file.\"\"\"\n", "    matched = list(Path(eval_dir).glob(\"*_completed_patches.pkl\"))\n", "    if len(matched) != 1:\n", "        return None\n", "    path = matched[0]\n", "    with open(path, \"rb\") as f:\n", "        return pickle.load(f)\n", "\n", "\n", "def read_results(eval_dir: str):\n", "    \"\"\"Reads Hydra results from the *_hydra.jsonl file.\"\"\"\n", "    matching_files = list(Path(eval_dir).glob(\"*_hydra.jsonl\"))\n", "    print(len(matching_files))\n", "    if len(matching_files) != 1:\n", "        raise ValueError(\n", "            f\"Expected 1 Hydra jsonl file under {eval_dir}, found {len(matching_files)}\"\n", "        )\n", "    hydra_results_path = matching_files[0]\n", "    with hydra_results_path.open(\"r\") as f:\n", "        return [json.loads(x) for x in f]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Register Experiments\n", "\n", "Add experiments that you want to compare. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# We added the following experiments to compare retrieval augmented systems to various baselines.\n", "# Note: `weight` is used to sort patch ids for visualization.\n", "\n", "EXPERIMENTS = {\n", "    # Baselines\n", "    \"EmptyCompletion\": Experiment(\n", "        poc=\"vzhao\",\n", "        eval_dir=\"/mnt/efs/augment/eval/jobs/RxkjHdTh\",\n", "        description=\"Sanity Check with empty completion. All test cases should fail.\",\n", "        weight=128.0,\n", "    ),\n", "    \"GoldCompletion\": Experiment(\n", "        poc=\"vzhao\",\n", "        eval_dir=\"/mnt/efs/augment/eval/jobs/mnnvX3tZ\",\n", "        description=\"Sanity Check with gold completion. All test cases should pass.\",\n", "        weight=128.0,\n", "    ),\n", "    \"StarCoderBase+NoRetrieval\": Experiment(\n", "        poc=\"colin\",\n", "        eval_dir=\"/mnt/efs/augment/eval/jobs/DU6XaJTk\",\n", "        description=\"StarCoder prompted with prefix. No RAG.\",\n", "        weight=64.0,\n", "    ),\n", "    \"RagFIMAV17B+NoRetrieval\": Experiment(\n", "        poc=\"colin\",\n", "        eval_dir=\"/mnt/efs/augment/eval/jobs/XAPHwL3r\",\n", "        description=\"RAG finetuned StarCoder + No retrieval augmented.\",\n", "        weight=32.0,\n", "    ),\n", "    # pyd<PERSON>7b, <PERSON><PERSON><PERSON>\n", "    \"RagFIMAV17B+Top1\": Experiment(\n", "        poc=\"vzhao\",\n", "        eval_dir=\"/mnt/efs/augment/eval/jobs/L9kTWMQj\",\n", "        description=\"RAG finetuned + Top 1 retrieval.\",\n", "        weight=1.0,\n", "    ),\n", "    \"RagFIMAV17B+Top2\": Experiment(\n", "        poc=\"vzhao\",\n", "        eval_dir=\"/mnt/efs/augment/eval/jobs/GWkQiefu\",\n", "        description=\"RAG finetuned + Top 2 retrieval.\",\n", "        weight=1.0,\n", "    ),\n", "    \"RagFIMAV17B+Top10\": Experiment(\n", "        poc=\"vzhao\",\n", "        eval_dir=\"/mnt/efs/augment/eval/jobs/f2d3poDc\",\n", "        description=\"RAG finetuned + Top 10 retrieval.\",\n", "        weight=1.0,\n", "    ),\n", "    # pydiff7b, <PERSON>\n", "    \"RagFIMAV17B+OracleTop1\": Experiment(\n", "        poc=\"vzhao\",\n", "        eval_dir=\"/mnt/efs/augment/eval/jobs/MVS875TU\",\n", "        description=\"RAG finetuned + Top 1 oracle reranked chunk (reranking top 1000)\",\n", "        weight=16.0,\n", "    ),\n", "    \"RagFIMAV17B+OracleTop2\": Experiment(\n", "        poc=\"vzhao\",\n", "        eval_dir=\"/mnt/efs/augment/eval/jobs/BvjDubA9\",\n", "        description=\"RAG finetuned + Top 2 oracle reranked chunk (reranking top 1000)\",\n", "        weight=16.0,\n", "    ),\n", "    \"RagFIMAV17B+OracleTop10\": Experiment(\n", "        poc=\"vzhao\",\n", "        eval_dir=\"/mnt/efs/augment/eval/jobs/NXeCus8a\",\n", "        description=\"RAG finetuned + Top 10 oracle reranked chunk (reranking top 1000)\",\n", "        weight=16.0,\n", "    ),\n", "}\n", "\n", "path_to_json = \"/mnt/efs/augment/user/vzhao/data/hydra/multiline_patch_ids_small.json\"\n", "import json\n", "\n", "# For multiline test cases, we remove patch ids 1) empty string can pass 2) gold can fail 3) downsample easy cases.\n", "ALLOW_DENY_IDS = json.load(open(path_to_json, \"r\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Statistics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "\n", "def helper(key, exp):\n", "    results = load(exp)\n", "    output = []\n", "    for pid in results:\n", "        # This works for 'comment2' style prompt.\n", "        num_chunks_in_prompt = sum(\n", "            [\n", "                \"# the below code fragment can be found in:\" == line\n", "                for line in results[pid][\"prompt\"].splitlines()\n", "            ]\n", "        )\n", "        # This works for <PERSON> prompt.\n", "        num_chunks_in_prompt = max( num_chunks_in_prompt, len(re.findall('<filename>', results[pid][\"prompt\"])))\n", "\n", "        output.append(\n", "            \n", "            {\n", "                \"experiment\": key,\n", "                \"patch_id\": pid,\n", "                \"pass/fail\": results[pid][\"result\"],\n", "                \"num_chunks_in_prompt\": num_chunks_in_prompt,\n", "                \"line_prompt_minus_line_suffix\": len(\n", "                    results[pid][\"prompt\"].split(\"<fim_middle>\")[0]\n", "                ),\n", "                \"weight\": exp.weight,\n", "            }\n", "        )\n", "    return output\n", "\n", "\n", "data: list[dict] = []\n", "for key, exp in EXPERIMENTS.items():\n", "    data.extend(helper(key, exp))\n", "\n", "df = pd.DataFrame(data)\n", "\n", "# [Optional]: Filter patch ids.\n", "df = df.loc[df[\"patch_id\"].apply(lambda x: x in ALLOW_DENY_IDS[\"allowed\"]), :]\n", "\n", "clear_output(wait=True)\n", "df.groupby([\"experiment\", \"pass/fail\"]).agg(\n", "    {\n", "        \"patch_id\": \"count\",\n", "        \"num_chunks_in_prompt\": \"mean\",\n", "        \"line_prompt_minus_line_suffix\": \"mean\",\n", "    }\n", ").reset_index().pivot(columns=[\"pass/fail\"], index=\"experiment\").fillna(0).sort_values(\n", "    [(\"patch_id\", \"PASSED\")]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Pass/Fail HeatMap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "value_mapping = {\n", "    \"PASSED\": 1,\n", "    \"FAILED\": -1,\n", "    \"OTHER\": 0,\n", "    \"TIMEOUT\": 0,\n", "}\n", "foo = (\n", "    df.set_index(\"experiment\")\n", "    .loc[list(EXPERIMENTS.keys()), :]\n", "    .reset_index()\n", "    .pivot(index=\"experiment\", columns=\"patch_id\", values=\"pass/fail\")\n", "    .replace(value_mapping)\n", ")\n", "\n", "weights = (\n", "    df.set_index(\"experiment\")\n", "    .loc[list(EXPERIMENTS.keys()), :]\n", "    .reset_index()\n", "    .pivot(index=\"experiment\", columns=\"patch_id\", values=\"weight\")\n", ")\n", "weighted = foo * weights\n", "# Sort columns.\n", "foo = foo[weighted.sum(axis=0).sort_values(ascending=False).index]\n", "# Sort rows\n", "foo = foo.loc[foo.sum(axis=1).sort_values().index]\n", "\n", "cmap = sns.color_palette(\"Spectral\", 16)\n", "\n", "# Option 1: Plot one heatmap for all patch ids.\n", "plt.figure(figsize=(20, len(foo.index) * 0.3))\n", "s = sns.heatmap(\n", "    foo,\n", "    cmap=[cmap[5], cmap[0], cmap[-1]],\n", "    xticklabels=False,\n", ")\n", "print(foo.shape)\n", "\n", "# # Option 2: Zoom in and plot mutliple heatmaps.\n", "# patch_ids_per_fig = 80\n", "# for i in range(4):\n", "#     plt.figure(figsize=(20, len(foo.index) * 0.3))\n", "#     sns.heatmap(\n", "#         foo.iloc[:, i * patch_ids_per_fig : (i + 1) * patch_ids_per_fig],\n", "#         cmap=[cmap[5], cmap[0], cmap[-1]],\n", "#     )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Side by Side Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Helper functions.\n", "\n", "import difflib\n", "\n", "\n", "def get_diff_html(left: str, right: str, fromdesc=\"\", todesc=\"\") -> str:\n", "    \"\"\"Returns HTML to compare `left` and `right`.\"\"\"\n", "    diff_obj = difflib.HtmlDiff(tabsize=2)\n", "    diff_obj._legend = \"\"\n", "    # This CSS thing is important.\n", "    display(\n", "        HTML(\n", "            f\"\"\"\n", "        <style type=\"text/css\">\n", "            {difflib.HtmlDiff()._styles}\n", "            td {{ text-align: left; }}\n", "            :not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td {{ text-align: left; }}\n", "        </style>\n", "        \"\"\"\n", "        )\n", "    )\n", "    return diff_obj.make_table(\n", "        left.splitlines(),\n", "        right.splitlines(),\n", "        fromdesc=fromdesc,\n", "        todesc=todesc,\n", "    )\n", "\n", "\n", "def print_last_n_prefix_lines(result: dict, n=5):\n", "    \"\"\"Prints last n lines of prefix.\"\"\"\n", "    print(\"\\n\".join(result[\"patch_rst\"][\"prefix\"].splitlines()[-n:]))\n", "\n", "\n", "def print_first_n_suffix_lines(result: dict, n=5):\n", "    \"\"\"Prints first\"\"\"\n", "    print(\"\\n\".join(result[\"patch_rst\"][\"suffix\"].splitlines()[:n]))\n", "\n", "\n", "def generation_sxs(left_rst: dict, right_rst: dict):\n", "    \"\"\"Model generations SxS.\"\"\"\n", "    display(\n", "        HTML(\n", "            get_diff_html(\n", "                left_rst[\"completion\"],\n", "                right_rst[\"completion\"],\n", "                fromdesc=f\"Left: ({left_rst['result']})\",\n", "                todesc=f\"Right: ({right_rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "\n", "\n", "def prompt_sxs(left_rst: dict, right_rst: dict):\n", "    \"\"\"Prompts SxS.\"\"\"\n", "    display(\n", "        HTML(\n", "            get_diff_html(\n", "                left_rst[\"prompt\"],\n", "                right_rst[\"prompt\"],\n", "                fromdesc=f\"Left: ({left_rst['result']})\",\n", "                todesc=f\"Right: ({right_rst['result']})\",\n", "            )\n", "        )\n", "    )\n", "\n", "\n", "def generation_vs_ground_truth(rst: dict):\n", "    display(\n", "        HTML(\n", "            get_diff_html(\n", "                rst[\"ground_truth\"],\n", "                rst[\"generation\"],\n", "                fromdesc=\"Ground Truth\",\n", "                todesc=f\"Model Generation: ({rst['result']})\",\n", "            )\n", "        )\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 1: Select what you want to compare.\n", "# NOTE: If you rerun this cell, remember to clear the output of this cell first.\n", "select_left_experiment = widgets.Dropdown(\n", "    options=list(EXPERIMENTS.keys()),\n", "    value=list(EXPERIMENTS.keys())[0],\n", "    description=\"Left Patch ID:\",\n", "    disabled=False,\n", ")\n", "select_right_experiment = widgets.Dropdown(\n", "    options=list(EXPERIMENTS.keys()),\n", "    value=list(EXPERIMENTS.keys())[0],\n", "    description=\"Right Patch ID:\",\n", "    disabled=False,\n", ")\n", "clear_output(wait=True)\n", "display(select_left_experiment, select_right_experiment)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 2: Load results for left and right.\n", "import json\n", "from research.eval.patch_lib import Patch\n", "\n", "print(\"left\", select_left_experiment.value, \"right\", select_right_experiment.value)\n", "\n", "left_id = select_left_experiment.value\n", "left_results = load(EXPERIMENTS[select_left_experiment.value])\n", "for pid in left_results:\n", "    # Normalize to patch object.\n", "    cache = left_results[pid][\"patch_rst\"][\"patch\"]\n", "    if isinstance(cache, dict):\n", "        left_results[pid][\"patch_rst\"][\"patch\"] = Patch.from_json(json.dumps(cache))\n", "\n", "right_id = select_right_experiment.value\n", "right_results = load(EXPERIMENTS[select_right_experiment.value])\n", "for pid in right_results:\n", "    # Normalize to patch object.\n", "    cache = right_results[pid][\"patch_rst\"][\"patch\"]\n", "    if isinstance(cache, dict):\n", "        right_results[pid][\"patch_rst\"][\"patch\"] = Patch.from_json(json.dumps(cache))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 3: Get the slice of patch ids to inspect.\n", "# NOTE: In this example, get patch ids that `RagFIMAV17B+NoRetrieval` failed but\n", "# `RagFIMAV17B+OracleTop1/2/10` all passed. \n", "want_pids = {\n", "    'RagFIMAV17B+NoRetrieval': 'FAILED',\n", "    'RagFIMAV17B+OracleTop1': 'PASSED',\n", "    'RagFIMAV17B+OracleTop2': 'PASSED',\n", "    'RagFIMAV17B+OracleTop10': 'PASSED',\n", "}\n", "\n", "\n", "pid_list = set(df['patch_id'])\n", "for exp, pf in want_pids.items():\n", "    _ids = set(df.loc[(df['experiment'] == exp) & (df['pass/fail'] == pf)]['patch_id'])\n", "    pid_list = pid_list & _ids\n", "pid_list = list(sorted(pid_list))\n", "\n", "print(f'Number of selected patches: {len(pid_list)}.')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 4: Select a patch id and see side-by-side results.\n", "select_patch_id = widgets.Dropdown(\n", "    options=pid_list,\n", "    value=pid_list[0],\n", "    description='Left Patch ID:',\n", "    disabled=False,\n", ")\n", "display(select_patch_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pid = select_patch_id.value\n", "left_rst, right_rst = left_results[pid], right_results[pid]\n", "\n", "clear_output(wait=True)\n", "print(f'\\033[1mPatch ID\\033[0m: {pid}')\n", "print(\"\\033[1mLeft\\033[0m\", select_left_experiment.value, \"\\033[1mRight\\033[0m\", select_right_experiment.value)\n", "\n", "print('\\033[1mPREFIX\\033[0m')\n", "print_last_n_prefix_lines(left_rst, 20)\n", "\n", "print('\\033[1mGENERATION\\033[0m')\n", "generation_sxs(left_rst, right_rst)\n", "generation_vs_ground_truth(right_rst)\n", "\n", "print('\\033[1mSUFFIX\\033[0m')\n", "print_first_n_suffix_lines(left_rst, 20)\n", "\n", "print('\\033[1mPROMPT\\033[0m')\n", "prompt_sxs(left_rst, right_rst)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
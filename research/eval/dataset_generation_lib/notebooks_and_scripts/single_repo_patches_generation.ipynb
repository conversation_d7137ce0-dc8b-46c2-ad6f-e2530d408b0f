{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Example Notebook: End-to-end Construction of dataset for single repo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import shutil\n", "\n", "from research.eval.dataset_generation_lib.utils import (\n", "    get_patches_filepath,\n", "    validate_unique_patch_ids,\n", "    get_retrieval_filepath,\n", "    get_git_sha,\n", "    advance_symlink,\n", "    save_patches_as_hydrated_dataset,\n", "    run_system_filter,\n", "    assert_system_result,\n", ")\n", "from research.eval.dataset_generation_lib.basic_patch_generators import (\n", "    all_functions_from_path,\n", "    get_lines_from_function\n", ")\n", "\n", "from research.eval.dataset_generation_lib.basic_patch_filters import (\n", "    uniform_reservoir_sample,\n", "    get_gold_system_config,\n", "    get_failing_assert_system_config,\n", "    get_null_system_config,\n", "    get_weak_model_no_retrieval_config,\n", "    filter_by_exact_string_match,\n", ")\n", "from research.eval.patch_lib import Patch\n", "from research.eval.dataset_generation_lib.utils import (\n", "    get_dehydrated_dataset_path,\n", "    save_patches_as_dehydrated_file,\n", ")\n", "from research.data.eval.datasets.utils.populate_mount import (\n", "    copy_code_from_docker,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### User configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ds_name = \"resilience4j.dev\"\n", "# org = \"resilience4j\"\n", "# repo = \"resilience4j\"\n", "\n", "ds_name = \"epoxy.dev\"\n", "org = \"airbnb\"\n", "repo = \"epoxy\"\n", "\n", "full_version = \"v1.0\"\n", "language = \"java\"\n", "num_samples_to_try = 50000"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Some initial setup code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["working_dir = Path(f\"/tmp/{ds_name}\")\n", "if not working_dir.exists():\n", "    print(f\"Making working dir: {working_dir}.\")\n", "    working_dir.mkdir()\n", "else:\n", "    print(f\"Working dir already exists: {working_dir}.\")\n", "\n", "# This path will be a symlink to different intermediate sets of patches.\n", "symlink_to_most_recent_patches = get_patches_filepath(ds_name, org, repo, working_dir)\n", "print(f\"symlink_to_most_recent_patches is: {symlink_to_most_recent_patches}\")\n", "if symlink_to_most_recent_patches.is_symlink():\n", "    print(f\"Unlinking symlink_to_most_recent_patches: {symlink_to_most_recent_patches}\")\n", "    symlink_to_most_recent_patches.unlink()\n", "\n", "# Copy code down from Docker image\n", "if not (working_dir / \"code\").exists():\n", "    local_copy_of_code_root = copy_code_from_docker(\n", "        org, repo, full_version, working_dir\n", "    )\n", "    print(f\"Creating local_copy_of_code_root: {local_copy_of_code_root}\")\n", "else:\n", "    local_copy_of_code_root = working_dir / \"code\"\n", "    print(f\"local_copy_of_code_root already exists: {local_copy_of_code_root}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sample patches."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Generate patches if they haven't already been generated.\n", "\n", "patches_filepath = get_patches_filepath(\n", "    ds_name, org, repo, working_dir, custom_suffix=\"unfiltered\"\n", ")\n", "\n", "if patches_filepath.exists():\n", "    print(f\"Patches already exist at: {patches_filepath}\")\n", "    patches = [Patch.from_json(x) for x in patches_filepath.read_text().splitlines()]\n", "    validate_unique_patch_ids(patches)\n", "else:\n", "    print(f\"Generating patches for: {patches_filepath}\")\n", "    commit_sha = get_git_sha(str(local_copy_of_code_root))\n", "\n", "    all_function_patches = all_functions_from_path(\n", "        local_copy_of_code_root, repo, org, commit_sha\n", "    )\n", "\n", "    all_patches = get_lines_from_function(\n", "        all_function_patches, min_lines=2, max_lines=4\n", "    )\n", "\n", "    sampled_n_line_patches = uniform_reservoir_sample(\n", "        all_patches, num_samples=num_samples_to_try\n", "    )\n", "    print(\n", "        f\"After resevoir sampling, we found {len(sampled_n_line_patches)} patches.\"\n", "    )\n", "\n", "    save_patches_as_hydrated_dataset(sampled_n_line_patches, patches_filepath)\n", "\n", "advance_symlink(\n", "    symlink=symlink_to_most_recent_patches,\n", "    to_file=patches_filepath,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Filter by string match\n", "\n", "file_name_substrings = [\"Test\", \"test\", \"build\"]\n", "file_contents_substrings = [\n", "    \"Generated by the protocol buffer compiler.  DO NOT EDIT!\",\n", "    \"// Generated from E:\",\n", "]\n", "\n", "previous_patches_filepath = patches_filepath\n", "patches_filepath = filter_by_exact_string_match(\n", "    working_dir,\n", "    ds_name,\n", "    file_name_substrings=file_name_substrings,\n", "    file_contents_substrings=file_contents_substrings,\n", ")\n", "\n", "advance_symlink(\n", "    symlink=symlink_to_most_recent_patches,\n", "    from_file=previous_patches_filepath,\n", "    to_file=patches_filepath,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate a placeholder empty retrieval corpus\n", "retrieval_corpus_filepath = get_retrieval_filepath(ds_name, org, repo, working_dir)\n", "retrieval_corpus_filepath.touch()\n", "print(f\"Created an empty file at: {retrieval_corpus_filepath}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Run Filters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Filter patches that don't fail when a failing assert or exception\n", "# is injected.\n", "\n", "system_name = \"failing_assert\"\n", "system_config = get_failing_assert_system_config(\n", "    working_dir,\n", "    ds_name,\n", "    language,\n", ")\n", "keep_patch_if_result_is = \"FAILED\"\n", "\n", "previous_patches_filepath = patches_filepath\n", "patches_filepath = run_system_filter(\n", "    symlink_to_most_recent_patches=symlink_to_most_recent_patches,\n", "    working_dir=working_dir,\n", "    ds_name=ds_name,\n", "    org=org,\n", "    repo=repo,\n", "    system_name=system_name,\n", "    system_config=system_config,\n", "    keep_patch_if_result_is=keep_patch_if_result_is,\n", ")\n", "advance_symlink(\n", "    symlink=symlink_to_most_recent_patches,\n", "    from_file=previous_patches_filepath,\n", "    to_file=patches_filepath,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run null system\n", "\n", "system_name = \"failing_null\"\n", "system_config = get_null_system_config(\n", "    working_dir,\n", "    ds_name,\n", ")\n", "keep_patch_if_result_is = \"FAILED\"\n", "\n", "previous_patches_filepath = patches_filepath\n", "patches_filepath = run_system_filter(\n", "    symlink_to_most_recent_patches=symlink_to_most_recent_patches,\n", "    working_dir=working_dir,\n", "    ds_name=ds_name,\n", "    org=org,\n", "    repo=repo,\n", "    system_name=system_name,\n", "    system_config=system_config,\n", "    keep_patch_if_result_is=keep_patch_if_result_is,\n", ")\n", "advance_symlink(\n", "    symlink=symlink_to_most_recent_patches,\n", "    from_file=previous_patches_filepath,\n", "    to_file=patches_filepath,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run weak system without retrieval\n", "\n", "system_name = \"weak_system\"\n", "system_config = get_weak_model_no_retrieval_config(\n", "    working_dir,\n", "    ds_name,\n", ")\n", "keep_patch_if_result_is = \"FAILED\"\n", "\n", "previous_patches_filepath = patches_filepath\n", "patches_filepath = run_system_filter(\n", "    symlink_to_most_recent_patches=symlink_to_most_recent_patches,\n", "    working_dir=working_dir,\n", "    ds_name=ds_name,\n", "    org=org,\n", "    repo=repo,\n", "    system_name=system_name,\n", "    system_config=system_config,\n", "    keep_patch_if_result_is=keep_patch_if_result_is,\n", ")\n", "advance_symlink(\n", "    symlink=symlink_to_most_recent_patches,\n", "    from_file=previous_patches_filepath,\n", "    to_file=patches_filepath,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Confirm results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run hydra failing assert test and ensure everything fails\n", "\n", "system_config = get_failing_assert_system_config(\n", "    working_dir,\n", "    ds_name,\n", "    language,\n", ")\n", "assert_system_result(system_config, expected_result=\"FAILED\", working_dir=working_dir)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run hydra null test and ensure everything fails\n", "\n", "system_config = get_null_system_config(\n", "    working_dir,\n", "    ds_name,\n", ")\n", "assert_system_result(system_config, expected_result=\"FAILED\", working_dir=working_dir)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run hydra gold test and ensure everything passes\n", "\n", "system_config = get_gold_system_config(working_dir, ds_name, language)\n", "assert_system_result(system_config, expected_result=\"PASSED\", working_dir=working_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Save dehydrated dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["major_version = full_version.split(\".\")[0]  # v1.0 -> v1\n", "dehydrated_dataset_path = get_dehydrated_dataset_path(ds_name, org, repo, major_version)\n", "patches = [\n", "    Patch.from_json(x) for x in symlink_to_most_recent_patches.read_text().splitlines()\n", "]\n", "\n", "save_patches_as_dehydrated_file(patches, dehydrated_dataset_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Delete working directory\n", "shutil.rmtree(working_dir)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
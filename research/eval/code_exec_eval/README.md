# Code Execution Evaluation System

This is a system for evaluating fixes to git repositories that change a test in a github action from failing to passing.  This is accomplished by:
- checking out a known broken state
- running the action, confirming it fails (the "red" run)
- applying the fix
- re-running the action, confirming it passes (the "green" run)

The evaluation is considered a success if (red, green) are (fail, pass); anything else is considered a failure (including if the red run passes).

## Quick Start

The final cell of `playground.ipynb` shows an example of how to generate a random evaluation set from parquet files of git repository graphs, and run those evaluations to identify which complete successfully.  Similar code can be used to run pre-existing evaluations with fixes generated by a system such as autofix.

Or from the command line,

```
python3 ./eval.py config/sample_config.yaml
```

The earlier cells in the notebook include other code samples of possible interest.

## TL;DR

There is a sample config in config/sample_config.yaml.  This includes comments on possible options.

BatchEvaluator is the main entry point, usually created from a config file.

This instantiates the following components:

- Orchestrator - responsible for batching jobs, generally use MultiProcessBatchOrchestrator
- ActEvaluator - responsible for running the actual evaluations, generally use K8sActEvaluator to run remotely in kubernetes.
- RepoProvider - responsible for fetching repos and patches, generally use CachedGitRepoProvider for cached repos.

### Running locally

If you want to run against a local docker daemon, you can do this by using LocalActEvaluator in your configuration; otherwise all invocations are the same.  Local evaluations will *not* clean up the containers from the local docker daemon.  You can run a "local" evaluation against a remote docker context by specifying evaluator.docker_host in your config.

(There is really no reason to do this, and your DevPod probably doesn't support it.)

### Known issues/missing capabilities

- Generating random evaluations from parquet files can use a lot of memory that the notebook holds on to; it's not difficult to crash your homepod with extremely large runs.
- The GithubRepoProvider does not support authentication, so can only be used against public repositories; use the CachedGitRepoProvider to avoid this (and to avoid GitHub rate limiting.)
- It's not currently possible to run these evals through determined.
- Kubernetes pods are launched with 4 CPU and 8G RAM, matching GitHub runner capabilities.
- A garbage collection job will clean up evaluation resources in kubernetes an hour after they complete.

### A note on imports

Because this code is frequently executed in small k8s pods, outside of the augment.research python environment, all of the imports are local by default:

```
from code_exec_config import CodeExecConfig
```

instead of this, which will fail in kubernetes:

```
from research.eval.code_exec_eval.code_exec_config import CodeExecConfig
```

## Key Components

### Configuration

- Uses YAML configuration files for evaluation parameters
- `CodeExecConfig` class holds configuration settings

### Evaluation Set

- `EvalSet` class represents a set of evaluation items
- `EvalItem` class represents individual evaluation items

### Batch Evaluation

- `BatchEvaluator` class handles batch evaluation of code

### Results

- `EvalResult` objects store evaluation results
- `Outcome` class/enum represents different execution outcomes (e.g., success, failure, timeout)

### Notebook Interface

- `playground.ipynb` provides an interface for running and experimenting with code evaluations

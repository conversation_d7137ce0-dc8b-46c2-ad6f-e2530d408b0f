import copy
import logging
import multiprocessing
import os
import time
import traceback
from collections import defaultdict
from datetime import datetime, timed<PERSON><PERSON>
from typing import Callable

from act_evaluator import ActEvaluator
from code_exec_utils import (
    EvalResult,
    ExecutionResult,
)
from evalset import EvalItem
from tqdm import tqdm

logger = logging.getLogger(__name__)

# This is a global because we can't pickle it for multiprocessing
pb = None
concurrency_pb = None

_running_concurrency = None
_max_concurrency = None


class BatchOrchestrator:
    def __init__(self, progress_callback: Callable | None = None, **kwargs):
        self._results = {}
        self._progress_callback = progress_callback

    def run_batch(
        self,
        act_eval: ActEvaluator,
        evals: list[EvalItem],
        timeout: int = 120,
        **kwargs,
    ):
        raise NotImplementedError()

    def get_results(self) -> dict[str, EvalResult]:
        return self._results

    def display_pb(self):
        """Display progress bar if not running in determined"""
        return os.getenv("DET_WORKDIR") is None

    def get_progress(self) -> int:
        return len(self._results)

    def get_stats(self) -> dict[str, int]:
        stats = defaultdict(int)
        for result in self._results.values():
            stats[str(result.outcome)] += 1
        return stats

    def get_lifecycle_stats(self) -> dict[str, int]:
        pod_count = 0
        submissions = []
        pendings = []
        runnings = []
        totals = []

        for result in self._results.values():
            pod_count += 1
            if (
                result.red_result.data is not None
                and "pod_lifecycle" in result.red_result.data
            ):
                pl = result.red_result.data["pod_lifecycle"]
            elif (
                result.green_result.data is not None
                and "pod_lifecycle" in result.green_result.data
            ):
                pl = result.green_result.data["pod_lifecycle"]
            else:
                continue

            if pl["submission_time"] is not None:
                submission_time = pl["submission_time"] - pl["start_time"]
                submissions.append(submission_time)
                if pl["running_time"] is not None:
                    pending_time = pl["running_time"] - pl["submission_time"]
                    pendings.append(pending_time)
                    if pl["end_time"] is not None:
                        running_time = pl["end_time"] - pl["running_time"]
                        runnings.append(running_time)
                        totals.append(pending_time + running_time)

        submissions.sort()
        pendings.sort()
        runnings.sort()
        totals.sort()
        stats = {
            "pod_count": pod_count,
            "submitted_count": len(submissions),
            "started_count": len(pendings),
            "completed_count": len(runnings),
            "submission_mean": submissions[len(submissions) // 2]
            if len(submissions) > 0
            else 0,
            "submission_90": submissions[len(submissions) * 9 // 10]
            if len(submissions) > 0
            else 0,
            "pending_mean": pendings[len(pendings) // 2] if len(pendings) > 0 else 0,
            "pending_90": pendings[len(pendings) * 9 // 10] if len(pendings) > 0 else 0,
            "running_mean": runnings[len(runnings) // 2] if len(runnings) > 0 else 0,
            "running_90": runnings[len(runnings) * 9 // 10] if len(runnings) > 0 else 0,
            "total_mean": totals[len(totals) // 2] if len(totals) > 0 else 0,
            "total_90": totals[len(totals) * 9 // 10] if len(totals) > 0 else 0,
        }
        return stats


class SingleProcessBatchOrchestrator(BatchOrchestrator):
    def run_batch(
        self,
        act_eval: ActEvaluator,
        evals: list[EvalItem],
        timeout: int = 120,
        **kwargs,
    ):
        logger.info(f"Running {len(evals)} evals")
        for eval in evals:
            st = datetime.now()
            red_result, green_result = act_eval.test_repo(eval, timeout=timeout)
            dt = datetime.now() - st
            er = EvalResult(red_result, green_result, dt, eval)
            self._results[eval.item_key()] = er

            if self._progress_callback is not None:
                self._progress_callback(eval_result=er, total_evals=len(evals))


class MultiProcessBatchOrchestrator(BatchOrchestrator):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.concurrency = kwargs.get("concurrency", multiprocessing.cpu_count())
        global _running_concurrency, _max_concurrency
        _running_concurrency = multiprocessing.Value("i", 0)
        _max_concurrency = multiprocessing.Value("i", 0)

    def run_batch(
        self,
        act_eval: ActEvaluator,
        evals: list[EvalItem],
        timeout: int = 120,
        **kwargs,
    ):
        print(f"Starting pool with {self.concurrency} workers")
        print(f"Running {len(evals)} evals with a timeout of {timeout}")
        # If gcloud runs in parallel it can throw an erorr trying to access its
        # database; so we pass in a lock to prevent failures in k8s calls

        st = time.time()
        global pb
        global concurrency_pb
        effective_concurrency = (
            len(evals) if len(evals) < self.concurrency else self.concurrency
        )
        if self.display_pb():
            pb = tqdm(total=len(evals), desc="Completed evaluations")
            concurrency_pb = tqdm(
                total=effective_concurrency, desc="Effective concurrency"
            )
        with multiprocessing.Manager() as manager:
            lock = manager.BoundedSemaphore(25)
            self._results = manager.dict()
            with multiprocessing.Pool(self.concurrency) as pool:
                for eval in evals:
                    pool.apply_async(
                        self._run_eval,
                        args=(act_eval, eval, timeout, lock, len(evals)),
                        callback=self._handle_result,
                        error_callback=self._handle_error,
                    )
                pool.close()
                self._results = dict(self._results)
                pool.join()
        conc = f"Requested concurrency {self.concurrency}"
        global _max_concurrency
        if _max_concurrency is not None:
            conc += f", maximum effective concurrency was {_max_concurrency.value}"
        print(conc)
        if pb is not None:
            pb.close()
        if concurrency_pb is not None:
            concurrency_pb.close()
        dt = time.time() - st
        print(f"Complete {len(self._results)} runs in {dt:.2f} seconds")

    def inc_concurrency(self):
        global _running_concurrency, _max_concurrency
        if _running_concurrency is not None and _max_concurrency is not None:
            with _running_concurrency.get_lock():
                _running_concurrency.value += 1
                with _max_concurrency.get_lock():
                    _max_concurrency.value = max(
                        _max_concurrency.value, _running_concurrency.value
                    )
                global concurrency_pb
                if concurrency_pb is not None:
                    concurrency_pb.reset()
                    concurrency_pb.update(_running_concurrency.value)
                    concurrency_pb.refresh()

    def dec_concurrency(self):
        global _running_concurrency, _max_concurrency
        if _running_concurrency is not None and _max_concurrency is not None:
            with _running_concurrency.get_lock():
                _running_concurrency.value -= 1
                if _running_concurrency.value < 0:
                    _running_concurrency.value = 0
                global concurrency_pb
                if concurrency_pb is not None:
                    concurrency_pb.reset()
                    concurrency_pb.update(_running_concurrency.value)
                    concurrency_pb.refresh()

    def _run_eval(
        self,
        act_eval: ActEvaluator,
        eval: EvalItem,
        timeout: int,
        lock: multiprocessing.Lock,  # type: ignore
        total_evals: int,
    ) -> EvalResult:
        try:
            # print(f"Entered _run_eval with {eval}")
            # self.inc_concurrency()
            st = datetime.now()
            red_result, green_result = act_eval.test_repo(
                eval,
                timeout=timeout,
                lock=lock,
                start_callback=self.inc_concurrency,
                end_callback=self.dec_concurrency,
            )
            dt = datetime.now() - st
            er = EvalResult(red_result, green_result, dt, eval)
            # self.dec_concurrency()

            if self._progress_callback is not None:
                self._progress_callback(eval_result=er, total_evals=total_evals)
            return er
        except Exception as e:
            print(f"Error in _run_eval for {eval.run_name}: {str(e)}")
            logger.error(f"Error in _run_eval for {eval.run_name}: {str(e)}")
            logger.error(traceback.format_exc())
            print(traceback.format_exc())
            return EvalResult(
                ExecutionResult.NoneResult(),
                ExecutionResult.NoneResult(),
                timedelta(seconds=0),
                eval,
            )

    def _handle_result(self, result: EvalResult):
        if result is None:
            logger.error(f"_handle_result got {result}")
            return
        self._results[result.eval_item.item_key()] = result
        global pb
        if pb is not None:
            pb.update(1)

    def _handle_error(self, error):
        logger.error(f"_handle_error got {error}")


# TODO(marcmac) launch through determined
class DeterminedOrchestrator(BatchOrchestrator):
    pass


def from_config(cf: dict) -> BatchOrchestrator:
    cfg = copy.deepcopy(cf)
    orchestrator_type = cfg.pop("type", None)
    if orchestrator_type == "SingleProcessBatchOrchestrator":
        return SingleProcessBatchOrchestrator(**cfg)
    elif orchestrator_type == "MultiProcessBatchOrchestrator":
        return MultiProcessBatchOrchestrator(**cfg)
    else:
        raise ValueError(f"Unknown orchestrator type {orchestrator_type}")

{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Base Config and imports\n", "\n", "# Required for imports in the notebook because the code_exec_eval stuff\n", "# all uses local imports, since the location changes in a k8s pod\n", "import sys\n", "\n", "sys.path.append(\"/home/<USER>/augment/research/eval/code_exec_eval\")\n", "\n", "import json\n", "import multiprocessing\n", "import os\n", "import time\n", "import yaml\n", "\n", "from concurrent.futures import ThreadPoolExecutor\n", "from pathlib import Path\n", "\n", "from research.eval.code_exec_eval.eval import (\n", "    BatchEvaluator,\n", ")\n", "\n", "from research.eval.code_exec_eval.code_exec_config import CodeExecConfig\n", "from research.eval.code_exec_eval.code_exec_utils import Outcome, EvalResult\n", "from research.eval.code_exec_eval.evalset import EvalItem, EvalSet\n", "\n", "sample_repo_file = Path(\n", "    \"/home/<USER>/augment/research/eval/code_exec_eval/config/test_evals.yaml\"\n", ")\n", "repos = list(yaml.safe_load(sample_repo_file.read_text()))\n", "\n", "base_config = {\n", "    \"evaluator\": {\n", "        \"type\": \"K8sActEvaluator\",\n", "        \"k8s_context\": \"gcp-us1\",\n", "        \"k8s_namespace\": \"gcp-us1\",\n", "        \"debug_pod_shutdown\": <PERSON><PERSON><PERSON>,\n", "        \"debug_pod_startup\": <PERSON><PERSON><PERSON>,\n", "        \"hard_timeout_sec\": 600,  # We kill the pod at this point\n", "        \"skip_cleanup\": <PERSON><PERSON><PERSON>,\n", "        # Additional tolerations if necessary\n", "        # \"tolerations\": [\n", "        #     {\n", "        #         \"key\": \"r.augmentcode.com/foo\",\n", "        #         \"operator\": \"Equal\",\n", "        #         \"effect\": \"NoSchedule\",\n", "        #         \"value\": \"bar\",\n", "        #     },\n", "        # ]\n", "    },\n", "    # \"evaluator\": {\n", "    #     \"type\": \"LocalActEvaluator\",\n", "    #     \"act_path\": \"/home/<USER>/git/act/dist/local/act\",\n", "    #     \"docker_host\": \"ssh://augment@marcmac-docker\",\n", "    # },\n", "    \"orchestrator\": {\n", "        \"type\": \"MultiProcessBatchOrchestrator\",\n", "        \"concurrency\": 50,\n", "        # \"progress_callback\": \"display_results_callback\",\n", "    },\n", "    # \"orchestrator\": {\n", "    #     \"type\": \"SingleProcessBatchOrchestrator\",\n", "    #     # \"progress_callback\": \"display_results_callback\",\n", "    # },\n", "    \"eval_set\": {\n", "        \"type\": \"EvalSet\",\n", "        # \"eval_path\": \"/home/<USER>/augment/research/eval/code_exec_eval/config/test_evals.yaml\",\n", "        \"evals\": repos,\n", "    },\n", "    # K8sActEvaluator passes this to the pod\n", "    \"repo_provider\": {\n", "        \"type\": \"CachedGitRepoProvider\",\n", "        # \"type\": \"GithubRepoProvider\",\n", "    },\n", "    # this is the timeout passed into test_repo\n", "    # LocalActEvaluator uses it for each run (red, green) where it is eventually used\n", "    # in the call to \"act push\", so the total timeout can be 2x this.\n", "    \"timeout\": 600,\n", "    \"log_level\": \"info\",\n", "}"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Globals and display functions\n", "lk = multiprocessing.Lock()\n", "rigor_results: dict[str, list[Outcome]] = {}\n", "full_results: list[dict[str, EvalResult]] = []\n", "\n", "\n", "def display_result_logs(result: EvalResult):\n", "    if result.red_result:\n", "        print(\"===========RED=============\")\n", "        print(result.red_result.stdout)\n", "        print(\"===========================\")\n", "    if result.green_result:\n", "        print(\"==========GREEN============\")\n", "        print(result.green_result.stdout)\n", "        print(\"===========================\")\n", "\n", "\n", "def display_pod_logs(result: EvalResult):\n", "    if result.red_result:\n", "        print(\"===========POD=============\")\n", "        print(result.red_result.data[\"pod_logs\"])\n", "        print(\"===========================\")\n", "    elif result.green_result:\n", "        print(\"===========POD=============\")\n", "        print(result.green_result.data[\"pod_logs\"])\n", "        print(\"===========================\")\n", "\n", "\n", "def display_results(results: dict[str, EvalResult]):\n", "    for k, r in sorted(\n", "        [(k, r) for k, r in results.items()],\n", "        key=lambda x: x[1].eval_item.repo_id.lower(),\n", "    ):\n", "        print(k, r)\n", "\n", "\n", "def display_rigor_results(rigor_results: dict[str, list[Outcome]]):\n", "    for k in rigor_results:\n", "        if len(set(rigor_results[k])) > 1:\n", "            print(\"**********Unstable results: \", (k, set(rigor_results[k])))\n", "\n", "\n", "def display_pod_lifecycles(results: list[dict[str, EvalResult]]):\n", "    for run in results:\n", "        for k, result in run.items():\n", "            r = result.red_result if result.red_result else result.green_result\n", "            if r:\n", "                pl = result.red_result.data.get(\"pod_lifecycle\", None)\n", "                if pl:\n", "                    pending = pl[\"running_time\"] - pl[\"start_time\"]\n", "                    running = pl[\"end_time\"] - pl[\"running_time\"]\n", "                    print(f\"{k} {pending:.2f} {running:.2f}\")\n", "\n", "\n", "def eval_rigor(config: dict, rigor: int):\n", "    \"\"\"Run an evalset multiple times in sequence and check for consistent results.\"\"\"\n", "\n", "    cf = CodeExecConfig(config)\n", "    rigor_results = {}\n", "\n", "    for _ in range(rigor):\n", "        st = time.time()\n", "        ke = BatchEvaluator.run_from_config(cf)\n", "        dt = time.time() - st\n", "        results = ke.get_results()\n", "        print(f\"Complete {len(results)} runs in {dt:.2f} seconds\")\n", "        print(ke.get_stats())\n", "        for r in results.values():\n", "            rigor_results.setdefault(r.eval_item.item_key(), []).append(r.outcome)\n", "    display_rigor_results(rigor_results)\n", "    return rigor_results"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:act_evaluator:K8s evaluator setting 'aug.eval_id=538fhyzy'\n", "INFO:act_evaluator:K8s logs writing to /mnt/efs/augment/eval/code_exec_eval/2024-12-13/538fhyzy\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 40 evals in 0:00:00.659024\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:act_evaluator:There are currently 0 evaluation containers running in gcp-us1\n", "INFO:orchestrator:Starting pool with 40 workers\n", "INFO:orchestrator:Running 40 evals with a timeout of 240\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Created config in 1.43s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Completed evaluations:   0%|          | 0/40 [00:00<?, ?it/s]ERROR:act_evaluator:Error in test_repo for alphagov/accessible-autocomplete e3fdcefe092b138a1d4060f29851ca374499c80a Patch too large (size 1622282 bytes > 1000000 bytes)\n", "Completed evaluations: 100%|██████████| 40/40 [04:51<00:00, 10.15s/it]INFO:orchestrator:Requested concurrency 40, maximum effective concurrency was 39\n", "Completed evaluations: 100%|██████████| 40/40 [04:51<00:00,  7.29s/it]\n", "Effective concurrency:   0%|          | 0/40 [04:51<?, ?it/s]\n", "INFO:orchestrator:Complete 40 runs in 291.68 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["defaultdict(<class 'int'>, {'NO_RESULTS_FOUND': 12, 'RED_PASSED': 5, 'JOB_ID_NOT_FOUND': 3, 'GREEN_FAILED': 5, 'SUCCESS': 9, 'RED_TIMEOUT': 6})\n", "9/40 successful evals found (22.50%)\n", "Pods requested/submitted/started/completed: 40/39/39/39\n", "Submission mean: 0.29s\n", "Submission 90th: 0.39s\n", "Pending mean: 10.04s\n", "Pending 90th: 10.06s\n", "Running mean: 80.12s\n", "Running 90th: 250.60s\n", "Total mean: 90.16s\n", "Total 90th: 260.64s\n", "9 successful evals found in 0:04:53.772578 (293.77s)\n", "Nominal max time: 480.00s (-186.23s)\n"]}], "source": ["# Search for passing evaluations\n", "\n", "from datetime import datetime\n", "from research.eval.code_exec_eval.evalset_generator import EvalSetGenerator\n", "\n", "from pathlib import Path\n", "from candidate_search import candidate_search\n", "\n", "st = datetime.now()\n", "all_successes = []\n", "eval_file = Path(\"/home/<USER>/augment/research/eval/code_exec_eval/test_small.yaml\")\n", "num_files = 0\n", "random_state = None\n", "concurrency = 0\n", "timeout = 240\n", "\n", "success_evals, evals, ke, conc = candidate_search(\n", "    eval_file=eval_file,\n", "    num_files=num_files,\n", "    random_state=random_state,\n", "    concurrency=concurrency,\n", "    timeout=timeout,\n", ")\n", "eval_count = len(evals)\n", "all_successes.extend(success_evals)\n", "stats = ke.get_lifecycle_stats()\n", "print(\n", "    f\"Pods requested/submitted/started/completed: {stats['pod_count']}/{stats['submitted_count']}/{stats['started_count']}/{stats['completed_count']}\"\n", ")\n", "print(f\"Submission mean: {stats['submission_mean']:.2f}s\")\n", "print(f\"Submission 90th: {stats['submission_90']:.2f}s\")\n", "print(f\"Pending mean: {stats['pending_mean']:.2f}s\")\n", "print(f\"Pending 90th: {stats['pending_90']:.2f}s\")\n", "print(f\"Running mean: {stats['running_mean']:.2f}s\")\n", "print(f\"Running 90th: {stats['running_90']:.2f}s\")\n", "print(f\"Total mean: {stats['total_mean']:.2f}s\")\n", "print(f\"Total 90th: {stats['total_90']:.2f}s\")\n", "dt = datetime.now() - st\n", "nominal_max = (conc // eval_count) * (timeout * 2)\n", "print(\n", "    f\"{len(all_successes)} successful evals found in {dt} ({dt.total_seconds():.2f}s)\"\n", ")\n", "print(\n", "    f\"Nominal max time: {nominal_max:.2f}s ({dt.total_seconds() - nominal_max:+.2f}s)\"\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
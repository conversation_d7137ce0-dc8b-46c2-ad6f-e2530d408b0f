import argparse
import json
import logging
import os
import time
import traceback
from collections import defaultdict
from pathlib import Path

from act_evaluator import ActEvaluator, LocalActEvaluator
from code_exec_config import CodeExecConfig
from code_exec_utils import EvalResult, Outcome
from evalset import EvalSet
from orchestrator import BatchOrchestrator, SingleProcessBatchOrchestrator

logger = logging.getLogger(__name__)

_act_path: str = os.path.expanduser("~/git/act/dist/local/act")


class BatchEvaluator:
    def __init__(
        self,
        timeout: int = 120,
        evaluator: ActEvaluator | None = None,
        orchestrator: BatchOrchestrator | None = None,
        log_level: int = logging.INFO,
    ):
        self.configure_logging(log_level=log_level)
        if orchestrator is None:
            self.orchestrator = SingleProcessBatchOrchestrator()
        else:
            self.orchestrator = orchestrator
        if evaluator is None:
            self.act_eval = LocalActEvaluator(_act_path)
        else:
            self.act_eval = evaluator
        self.timeout = timeout

    @classmethod
    def configure_logging(cls, log_level=logging.INFO):
        logging.basicConfig(level=log_level)

    def run_batch(self, es: EvalSet):
        try:
            self.orchestrator.run_batch(
                self.act_eval,
                es.evals,
                timeout=self.timeout,
            )
        except:
            traceback.print_exc()
            raise

    def print_results(self):
        stats = defaultdict(int)
        for result in sorted(
            self.get_results().values(), key=lambda x: x.eval_item.repo_id
        ):
            stats[str(result.outcome)] += 1
            print(result)
        print(f"Stats: {json.dumps(stats, indent=2)}")

    def get_success_results(self) -> dict[str, EvalResult]:
        return self.get_results(outcome=Outcome.SUCCESS)

    def get_results(self, outcome: Outcome | None = None) -> dict[str, EvalResult]:
        return {
            k: r
            for k, r in self.orchestrator.get_results().items()
            if outcome is None or r.outcome == outcome
        }

    def get_stats(self) -> dict[str, int]:
        return self.orchestrator.get_stats()

    def get_lifecycle_stats(self) -> dict[str, int]:
        return self.orchestrator.get_lifecycle_stats()

    @classmethod
    def from_config(cls, cf: CodeExecConfig) -> "BatchEvaluator":
        ll = logging.getLevelNamesMapping().get(
            cf.get("log_level", "INFO").upper(), logging.INFO
        )
        # Do this before we instantiate so all the logging is set up first
        cls.configure_logging(log_level=ll)
        return cls(
            evaluator=cf.evaluator(),
            orchestrator=cf.orchestrator(),
            timeout=cf.get("timeout", 120),
            log_level=ll,
        )

    @classmethod
    def run_from_config(cls, cf: CodeExecConfig) -> "BatchEvaluator":
        """Convenience method to create and run a BatchEvaluator from a config"""
        st = time.time()
        be = cls.from_config(cf)
        evals = cf.eval_set()
        dt = time.time() - st
        print(f"Created config in {dt:.2f}s")
        be.run_batch(evals)
        return be


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--termination-log", type=Path, default=None)
    parser.add_argument("config_file", type=Path)
    args = parser.parse_args()

    be = BatchEvaluator.run_from_config(CodeExecConfig.load_config(args.config_file))
    be.print_results()
    if args.termination_log is not None:
        print("Writing termination log")
        for v in be.get_results().values():
            print(v)
            print(v.to_json())
        args.termination_log.write_text(
            "\n".join([r.to_json() for r in be.get_results().values()])
        )

"""Config class for code-exec-eval"""

import copy
import pathlib
from dataclasses import dataclass
from typing import Any, Dict

import act_evaluator
import code_exec_utils
import evalset
import orchestrator
import repo_provider
import yaml


class CodeExecConfig(dict):
    @classmethod
    def load_config(cls, config_file: pathlib.Path | str):
        """Loads the configuration from a file."""
        config_file = pathlib.Path(config_file)
        return cls(yaml.safe_load(config_file.read_text()))

    # Convenience methods
    def evaluator(self) -> act_evaluator.ActEvaluator:
        eval_config = self.get("evaluator", None)
        assert eval_config is not None, "No evaluator definition"
        # TODO(marcmac) ideally we wouldn't instantiate the provider here,
        # TODO(marcmac) we would do it in the evaluator in case it's remote
        return act_evaluator.from_config(eval_config, self.repo_provider())

    def orchestrator(self) -> orchestrator.BatchOrchestrator:
        orchestrator_config = self.get("orchestrator", None)
        assert orchestrator_config is not None, "No orchestrator definition"
        if "progress_callback" in orchestrator_config:
            orchestrator_config = copy.deepcopy(orchestrator_config)
            orchestrator_config["progress_callback"] = code_exec_utils.get_callback(
                orchestrator_config["progress_callback"]
            )
        return orchestrator.from_config(orchestrator_config)

    def eval_set(self) -> evalset.EvalSet:
        eval_config = self.get("eval_set", None)
        assert eval_config is not None, "No eval set definition"
        if eval_config["type"] == "EvalSet":
            es = eval_config["evalset"]
        elif eval_config["type"] == "EvalSetGenerator":
            esg = evalset.EvalSetGenerator()
            es = esg.generate_set(**eval_config["params"])
        elif eval_config["type"] == "EvalSetFile":
            es = evalset.EvalSet.from_parquet(eval_config["path"])
        elif eval_config["type"] == "EvalList":
            es = evalset.EvalSet([evalset.EvalItem(**e) for e in eval_config["evals"]])
        else:
            raise NotImplementedError(f"Unknown eval set type {eval_config['type']}")
        return es

    def repo_provider(self) -> repo_provider.RepoProvider:
        provider_config = self.get("repo_provider", {})
        return repo_provider.from_config(provider_config)

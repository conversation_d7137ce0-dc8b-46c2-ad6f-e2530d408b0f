import json
from dataclasses import dataclass, field
from datetime import timed<PERSON><PERSON>
from enum import IntEnum
from pathlib import Path
from typing import Callable

from evalset import EvalItem


class Outcome(IntEnum):
    JOB_ID_NOT_FOUND = 0
    GREEN_FAILED = 1
    GREEN_TIMEOUT = 2
    RED_TIMEOUT = 3
    RED_PASSED = 4
    SUCCESS = 5
    NO_RESULTS_FOUND = 6

    def __str__(self):
        return self.name


class ResultType(IntEnum):
    SUCCESS = 0
    FAILURE = 1
    TIMEOUT = 2
    JOB_ID_NOT_FOUND = 3
    NONE = 4

    def __str__(self):
        return self.name


@dataclass
class ExecutionResult:
    stdout_path: Path | None
    stderr_path: Path | None
    returncode: int
    result_type: ResultType
    data: dict = field(default_factory=dict)
    _stdout: str | None = None
    _stderr: str | None = None

    def errors(self) -> list[str]:
        return [ln for ln in self.stdout.splitlines() if "::error" in ln] + [
            ln for ln in self.stderr.splitlines()
        ]

    @property
    def stdout(self) -> str:
        if self._stdout is not None:
            return self._stdout
        if self.stdout_path is not None:
            with self.stdout_path.open() as sout:
                return sout.read()
        return ""

    @property
    def stderr(self) -> str:
        if self._stderr is not None:
            return self._stderr
        if self.stderr_path is not None:
            with self.stderr_path.open() as serr:
                return serr.read()
        return ""

    @classmethod
    def NoneResult(cls, stdout_path=None, _stdout=None, data=None):
        return cls(
            stdout_path=stdout_path,
            stderr_path=None,
            _stdout=_stdout,
            returncode=0,
            result_type=ResultType.NONE,
            data=data if data else dict(),
        )

    @classmethod
    def NotFoundResult(cls):
        return cls(
            stdout_path=None,
            stderr_path=None,
            returncode=0,
            result_type=ResultType.JOB_ID_NOT_FOUND,
        )

    @classmethod
    def TimeoutResult(cls):
        return cls(
            stdout_path=None,
            stderr_path=None,
            returncode=0,
            result_type=ResultType.TIMEOUT,
        )


class PodFailureException(Exception):
    def __init__(self, *args, **kwargs):
        log_path = kwargs.pop("log_path", "")
        data = kwargs.pop("data", None)
        super().__init__(*args, **kwargs)
        self.log_path = log_path
        self.data = data


@dataclass
class EvalResult:
    red_result: ExecutionResult
    green_result: ExecutionResult
    elapsed_time: timedelta
    eval_item: EvalItem

    def __str__(self):
        return f"{self.eval_item.repo_id} {self.eval_item.run_name} Outcome: {self.outcome} in {self.elapsed_time}"

    def to_json(self, indent: int = 0) -> str:
        return json.dumps(
            {
                "repo_id": self.eval_item.repo_id,
                "run_name": self.eval_item.run_name,
                "outcome": self.outcome,
                "elapsed_time": self.elapsed_time,
                "red_result_type": self.red_result.result_type
                if self.red_result is not None
                else None,
                "green_result_type": self.green_result.result_type
                if self.green_result is not None
                else None,
                "red_returncode": self.red_result.returncode
                if self.red_result is not None
                else None,
                "green_returncode": self.green_result.returncode
                if self.green_result is not None
                else None,
            },
            indent=indent,
            default=str,
        )

    @classmethod
    def NoneResult(cls, repo_id="", run_name="", sha="", fix_sha=""):
        return cls(
            red_result=ExecutionResult.NoneResult(),
            green_result=ExecutionResult.NoneResult(),
            elapsed_time=timedelta(seconds=0),
            eval_item=EvalItem(
                run_name=run_name, repo_id=repo_id, sha=sha, fix_sha=fix_sha
            ),
        )

    @property
    def outcome(self) -> Outcome:
        """Turn Execution Results into an Outcome.

        There is no guarantee that an orchestrator will run both tests, or that they will
        run in a particular order.
        """

        rrt = self.red_result.result_type
        grt = self.green_result.result_type

        if (rrt, grt) == (ResultType.NONE, ResultType.NONE):
            return Outcome.NO_RESULTS_FOUND

        if ResultType.JOB_ID_NOT_FOUND in (rrt, grt):
            return Outcome.JOB_ID_NOT_FOUND

        if (rrt, grt) == (ResultType.FAILURE, ResultType.SUCCESS):
            return Outcome.SUCCESS

        # Note that this is arbitrarily biased toward reporting red failures first.
        if rrt == ResultType.SUCCESS:
            return Outcome.RED_PASSED
        if rrt == ResultType.TIMEOUT:
            return Outcome.RED_TIMEOUT

        if grt == ResultType.TIMEOUT:
            return Outcome.GREEN_TIMEOUT
        if grt == ResultType.FAILURE:
            return Outcome.GREEN_FAILED

        raise ValueError(f"Unexpected result types: {rrt}, {grt}")


def simple_progress_callback(*args, **kwargs):
    print(".", end="", flush=True)


def display_results_callback(*args, **kwargs):
    er = kwargs.get("eval_result", None)
    if er is not None:
        print(er)


def get_callback(cb: str | None) -> Callable | None:
    if cb is None:
        return None
    if cb == "simple_progress_callback":
        return simple_progress_callback
    if cb == "display_results_callback":
        return display_results_callback
    raise ValueError(f"Unknown callback: {cb}")

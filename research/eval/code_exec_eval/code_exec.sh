#!/bin/bash

# This is used within determined to launch a candidate search.

AUGMENT_DIR=$(dirname $0)/../../../
source $AUGMENT_DIR/research/gpt-neox/jobs/entrypoint_utils.sh
entrypoint_setup

ulimit -a
env | sort

EVAL_DIR=/mnt/efs/augment/eval/code_exec_eval/e2e/${DET_EXPERIMENT_ID}
mkdir -p ${EVAL_DIR}

echo "Running candidate search in ${EVAL_DIR}"

get_success_count() {
    local _tc=0
    for file in ${EVAL_DIR}/*/*.parquet; do
        # Files are named success_$idx_$count.zstd.parquet
        local _file_base=$(basename $file .zstd.parquet)
        local _file_count=${_file_base#success_*_}
        _tc=$((_tc + _file_count))
    done
    echo $_tc
}

if [[ -z ${EVAL_BATCH_SIZE} ]]; then
    echo "EVAL_BATCH_SIZE not set, exiting"
    exit 1
fi
if [[ -z ${EVAL_TARGET_COUNT} ]]; then
    echo "EVAL_TARGET_COUNT not set, exiting"
    exit 1
fi
if [[ -z ${EVAL_MIN_REPO_SIZE} ]]; then
    echo "EVAL_MIN_REPO_SIZE not set, defaulting to 0"
    export EVAL_MIN_REPO_SIZE=0
fi
if [[ -z ${EVAL_MAX_REPO_SIZE} ]]; then
    echo "EVAL_MAX_REPO_SIZE not set, defaulting to unlimited"
    export EVAL_MAX_REPO_SIZE=0
fi

MAX_ITERATIONS=100

total_count=0
while :; do
    python3 ${AUGMENT_DIR}/research/eval/code_exec_eval/candidate_search.py \
        --generate_evals ${EVAL_BATCH_SIZE} \
        --min_repo_size ${EVAL_MIN_REPO_SIZE} \
        --max_repo_size ${EVAL_MAX_REPO_SIZE} \
        --success_dir ${EVAL_DIR} \
        --log_level info
    total_count=$(get_success_count)
    echo "Total successful evals: $total_count/${EVAL_TARGET_COUNT}"
    ls -latr ${EVAL_DIR}/*/*.parquet
    MAX_ITERATIONS=$((MAX_ITERATIONS - 1))
    if [[ $total_count -ge ${EVAL_TARGET_COUNT} ]]; then
        break
    fi
    if [[ $MAX_ITERATIONS -le 0 ]]; then
        echo "Max iterations reached, exiting"
        break
    fi
done

ls -latr ${EVAL_DIR}/*/*.parquet

outfile=${EVAL_DIR}/success_${total_count}
${AUGMENT_DIR}/research/eval/code_exec_eval/evalset.py consolidate --output_path ${outfile} ${EVAL_DIR}/*/*.parquet

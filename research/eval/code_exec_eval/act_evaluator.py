import copy
import json
import logging
import multiprocessing
import os
import re
import subprocess
import tempfile
import time
import traceback
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Any, Callable, Dict, List, Tuple

from code_exec_utils import (  # pyright: ignore[reportMissingImports]
    ExecutionResult,
    PodFailureException,
    ResultType,
)
from evalset import EvalItem  # pyright: ignore[reportMissingImports]
from kubernetes import client, config
from repo_provider import RepoProvider  # pyright: ignore[reportMissingImports]
from shortuuid import ShortUUID

logger = logging.getLogger(__name__)


class ActEvaluator:
    def __init__(self, **kwargs):
        rp = kwargs.get("repo_provider", RepoProvider())
        self.repo_provider: RepoProvider = rp
        pass

    # This is the only method required
    def test_repo(self, *args, **kwargs) -> Tuple[ExecutionResult, ExecutionResult]:
        """Test a repo.

        The order the tests run in is not specified, but the return
        is always (red,green)
        """
        raise NotImplementedError()

    def log_system_resource_usage(self):
        pass


class LocalActEvaluator(ActEvaluator):
    """Driver for local evaluations.

    Depends on https://github.com/nektos/act
    """

    def __init__(self, act_path: str, docker_host: str | None = None, **kwargs):
        super().__init__(**kwargs)
        self.act_path = act_path
        self.docker_host = docker_host
        self.run_log_dir = kwargs.get("run_log_dir", "")

    @property
    def run_log_dir(self):
        return self._run_log_dir

    @run_log_dir.setter
    def run_log_dir(self, value):
        if value:
            self._run_log_dir = Path(value)
            self._run_log_dir.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Run log dir set to {self._run_log_dir}")
        else:
            self._run_log_dir = None

    def _run_act_cmd(
        self, args: list[str], cwd: str, timeout: int = 30
    ) -> subprocess.CompletedProcess[str]:
        cmd = [self.act_path] + args
        logging.debug(f"Running {' '.join([str(c) for c in cmd])} in {cwd}")
        env = os.environ.copy()
        if self.docker_host is not None:
            env["DOCKER_HOST"] = self.docker_host
        output = subprocess.run(
            cmd,
            check=True,
            cwd=cwd,
            timeout=timeout,
            capture_output=True,
            text=True,
            env=env,
        )
        return output

    def _execute_eval(
        self,
        code_directory: str,
        checkrun_name: str,
        timeout: int = 30,
    ) -> ExecutionResult:
        try:
            job_id = self._run_name_2_job_id(code_directory, checkrun_name)
            if job_id is None:
                return ExecutionResult.NotFoundResult()
            output = self._run_act_cmd(
                [
                    "push",
                    "--container-architecture",
                    "linux/amd64",
                    "--action-offline-mode",
                    "-j",
                    job_id,
                    "--rm",
                ],
                cwd=code_directory,
                timeout=timeout,
            )
            # [logger.debug(l) for l in output.stdout.splitlines()]
            # [logger.debug(l) for l in output.stderr.splitlines()]
            return ExecutionResult(
                stdout_path=None,
                stderr_path=None,
                _stdout=output.stdout,
                _stderr=output.stderr,
                returncode=output.returncode,
                result_type=ResultType.SUCCESS,
            )
        except subprocess.TimeoutExpired:
            return ExecutionResult.TimeoutResult()
        except subprocess.CalledProcessError as e:
            return ExecutionResult(
                stdout_path=None,
                stderr_path=None,
                _stdout=e.stdout,
                _stderr=e.stderr,
                returncode=e.returncode,
                result_type=ResultType.FAILURE,
            )

    def _run_name_2_job_id(self, cwd: str, checkrun_name: str) -> str | None:
        act_output = self._run_act_cmd(
            ["-l", "--container-architecture", "linux/amd64"],
            cwd=cwd,
            timeout=5,
        ).stdout

        # Parse the output of act -l, line by line
        for line in act_output.splitlines():
            cols = line.split()
            if len(cols) < 3:
                continue
            if checkrun_name.startswith(cols[2]):
                return cols[1]
        return None

    def _run_one(
        self,
        cwd: str,
        run_name: str,
        timeout: int = 30,
    ) -> ExecutionResult:
        result = self._execute_eval(cwd, run_name, timeout)

        for line in result.stdout.splitlines():
            logger.debug(line)
        for line in result.stderr.splitlines():
            logger.debug(line)
        logger.debug(f"Result: {result.result_type}")
        return result

    def log_results(self, result: ExecutionResult, result_tag: str):
        """Log the results to files.

        Mostly used by the k8s evaluator to separate out logs.
        """
        if not self.run_log_dir:
            return
        self.run_log_dir.mkdir(parents=True, exist_ok=True)
        logger.debug(
            f"Logging {result_tag} results to {self.run_log_dir} (len {len(result.stdout)} {len(result.stderr)})"
        )
        (self.run_log_dir / f"{result_tag}_stdout.log").write_text(result.stdout)
        (self.run_log_dir / f"{result_tag}_stderr.log").write_text(result.stderr)

    def test_repo(
        self,
        eval: EvalItem,
        timeout: int,
        **kwargs,
    ) -> Tuple[ExecutionResult, ExecutionResult]:  # red, green
        """Test a repo."""
        start_cb = kwargs.get("start_callback", None)
        end_cb = kwargs.get("end_callback", None)

        with self.repo_provider.checkout(eval, apply_patch=False) as rp:
            if start_cb is not None:
                start_cb()
            try:
                red_result = self._run_one(rp.cwd, eval.run_name, timeout)
                self.log_results(red_result, "red")
                # TODO(marcmac) configurable fast failure
                if red_result.result_type != ResultType.FAILURE:
                    return (red_result, ExecutionResult.NoneResult())
                # apply the patch
                rp.patch(eval)
                green_result = self._run_one(rp.cwd, eval.run_name, timeout)
                self.log_results(green_result, "green")
                return (red_result, green_result)
            finally:
                if end_cb is not None:
                    end_cb()


class K8sActEvaluator(ActEvaluator):
    _default_tolerations = [
        {
            "key": "r.augmentcode.com/pool-type",
            "operator": "Equal",
            "effect": "NoSchedule",
            "value": "hydra",
        }
    ]

    _default_run_log_dir = Path("/mnt/efs/augment/eval/code_exec_eval")

    def __init__(
        self,
        k8s_context: str,
        k8s_namespace: str,
        debug_pod_startup=False,
        debug_pod_shutdown=False,
        hard_timeout_sec: int = 600,
        skip_cleanup: bool = False,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.k8s_context = k8s_context
        self.k8s_namespace = k8s_namespace
        self._v1API = None
        self._debug_pod_startup = debug_pod_startup
        self._debug_pod_shutdown = debug_pod_shutdown
        self.hard_timeout_sec = hard_timeout_sec
        self.pending_timeout_sec = 180
        self._uuid = K8sActEvaluator.sanitize_k8s_string(ShortUUID().random(8))
        self._skip_cleanup = skip_cleanup
        self._shared_log_dir = (
            Path(kwargs.get("run_log_dir", K8sActEvaluator._default_run_log_dir))
            / datetime.now(timezone.utc).strftime("%Y-%m-%d")
            / self._uuid
        )
        self.act_image = kwargs.get("act_image", "act")

        self._tolerations = [
            client.V1Toleration(
                key=t["key"],
                operator=t["operator"],
                effect=t["effect"],
                value=t["value"] if "value" in t else None,
            )
            for t in self._default_tolerations + kwargs.get("tolerations", [])
        ]

        logger.info(f"K8s evaluator setting 'aug.eval_id={self._uuid}'")
        logger.info(f"K8s logs writing to {self._shared_log_dir}")
        resources = self.get_system_resource_usage()
        logger.warning(
            f"There are currently {resources['us'] + resources['them']} evaluation containers running in {self.k8s_namespace}"
        )

    def log_system_resource_usage(self):
        resources = self.get_system_resource_usage()
        logger.info(
            f"Globally running evaluations (you/others/total): {resources['us']}/{resources['them']}/{resources['us'] + resources['them']} running containers"
        )

    def load_kube_config(self) -> client.CoreV1Api | None:
        try:
            config.load_kube_config(context=self.k8s_context)
            _v1_api = client.CoreV1Api()
            return _v1_api
        except config.config_exception.ConfigException as e:
            logger.debug(f"Failed to load kube config ({e})  Trying in-cluster config.")
            # Try the in-cluster config
            config.load_incluster_config()
            _v1_api = client.CoreV1Api()
            return _v1_api

    def get_system_resource_usage(self) -> dict[str, int]:
        resources = {
            "us": 0,
            "them": 0,
        }

        # Don't use the global item or we fail trying to pickle it.
        _v1API = self.load_kube_config()
        assert _v1API is not None

        pods = _v1API.list_namespaced_pod(self.k8s_namespace)
        for pod in pods.items:
            if pod.metadata.labels is not None and "aug.eval_id" in pod.metadata.labels:
                if pod.status.phase not in ("Running", "Pending"):
                    continue
                if pod.metadata.labels["aug.eval_id"] == self._uuid:
                    resources["us"] += 1
                else:
                    resources["them"] += 1
        return resources

    @staticmethod
    def sanitize_k8s_string(s: str) -> str:
        san = (
            s.replace("/", "-").replace("_", "-").replace(".", "-").lower()[0:63]
        )  # Max length of 63
        # This regex came straight from a k8s error message
        k8s_re = r"(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])?$"
        if not re.match(k8s_re, san):
            # We *probably* aren't ending with an alphanumeric
            san = san[0:-1]
        if not re.match(k8s_re, san):
            raise ValueError(f"Unable to sanitize {s}")

        return san

    def _meta(
        self, name: str, eval: EvalItem, name_suffix: str = ""
    ) -> client.V1ObjectMeta:
        return client.V1ObjectMeta(
            name=name + name_suffix,
            namespace=self.k8s_namespace,
            labels={  # use these to clean up
                "aug.app": "code-exec-eval",
                "repo_name": K8sActEvaluator.sanitize_k8s_string(eval.repo_id),
                "commit_hash": K8sActEvaluator.sanitize_k8s_string(eval.sha),
                "aug.eval_id": self._uuid,
            },
        )

    def _create_configmaps(self, name: str, eval: EvalItem) -> List[client.V1ConfigMap]:
        eval_cm = client.V1ConfigMap()
        eval_cm.metadata = self._meta(name, eval, "-eval")
        include_files = [
            "act_evaluator.py",
            "code_exec_config.py",
            "code_exec_utils.py",
            "eval.py",
            "evalset.py",
            "orchestrator.py",
            "repo_provider.py",
        ]
        eval_cm.data = dict(
            (f, (Path(__file__).parent / f).read_text()) for f in include_files
        )
        max_patch_size = 1000000
        if eval.patch_text:
            if len(eval.patch_text) > max_patch_size:
                raise ValueError(
                    f"Patch too large (size {len(eval.fix_text)} bytes > {max_patch_size} bytes)"
                )
            eval_cm.data["fix.patch"] = eval.patch_text

        k8s_cm = client.V1ConfigMap()
        k8s_cm.metadata = self._meta(name, eval, "-k8s")
        k8s_files = [
            Path(__file__).parent / "k8s" / f for f in ["run.sh", "requirements.txt"]
        ]

        k8s_cm.data = dict((f.name, f.read_text()) for f in k8s_files)
        return [eval_cm, k8s_cm]

    def _create_pod(self, name: str, eval: EvalItem, timeout: int):
        pod = client.V1Pod()
        pod.metadata = self._meta(name, eval)
        pod.metadata.annotations = {"gke-gcsfuse/volumes": "true"}
        patch_file_path = "/configmap/fix.patch" if eval.patch_text else ""
        pod.spec = client.V1PodSpec(
            restart_policy="Never",
            tolerations=self._tolerations,
            affinity=client.V1Affinity(
                node_affinity=client.V1NodeAffinity(
                    preferred_during_scheduling_ignored_during_execution=[
                        client.V1PreferredSchedulingTerm(
                            weight=50,
                            preference=client.V1NodeSelectorTerm(
                                match_expressions=[
                                    client.V1NodeSelectorRequirement(
                                        key="r.augmentcode.com/pool-type",
                                        operator="In",
                                        values=["hydra"],
                                    ),
                                ]
                            ),
                        )
                    ],
                )
            ),
            containers=[
                client.V1Container(
                    name="act-eval",
                    image="us-central1-docker.pkg.dev/augment-research-gsc/docker-us-central1/code-exec-eval:2024-11-15-01",
                    command=["/bin/bash", "/configmap/k8s/run.sh"],
                    env=[
                        client.V1EnvVar(
                            name="DEBUG_POD_STARTUP",
                            value="1" if self._debug_pod_startup else "",
                        ),
                        client.V1EnvVar(
                            name="DEBUG_POD_SHUTDOWN",
                            value="1" if self._debug_pod_shutdown else "",
                        ),
                        client.V1EnvVar(
                            name="TIMEOUT",
                            value=str(timeout),
                        ),
                        client.V1EnvVar(
                            name="REPO_ID",
                            value=eval.repo_id,
                        ),
                        client.V1EnvVar(
                            name="SHA",
                            value=eval.sha,
                        ),
                        client.V1EnvVar(
                            name="FIX_SHA",
                            value=eval.fix_sha,
                        ),
                        client.V1EnvVar(
                            name="PATCH_FILE",
                            value=patch_file_path,
                        ),
                        client.V1EnvVar(
                            name="RUN_NAME",
                            value=eval.run_name,
                        ),
                        client.V1EnvVar(
                            name="RUN_LOG_DIR",
                            value=str(self._shared_log_dir / str(pod.metadata.name)),
                        ),
                        # TODO(marcmac) mechanism for passing provider args
                        client.V1EnvVar(
                            name="PROVIDER_TYPE",
                            value=self.repo_provider.__class__.__name__,
                        ),
                        client.V1EnvVar(
                            name="ACT_IMAGE",
                            value=self.act_image,
                        ),
                    ],
                    volume_mounts=[
                        client.V1VolumeMount(
                            name="k8s-volume",
                            mount_path="/configmap/k8s",
                        ),
                        client.V1VolumeMount(
                            name="eval-volume",
                            mount_path="/configmap",
                        ),
                        client.V1VolumeMount(
                            name="shared-volume",
                            mount_path="/mnt/efs/augment/eval",
                        ),
                        client.V1VolumeMount(
                            name="spark-data",
                            mount_path="/mnt/efs/spark-data",
                        ),
                    ],
                    resources=client.V1ResourceRequirements(
                        limits={"cpu": "4", "memory": "8Gi"},
                    ),
                    security_context=client.V1SecurityContext(
                        privileged=True,  # Needed to run docker
                    ),
                )
            ],
            volumes=[
                client.V1Volume(
                    name="eval-volume",
                    config_map=client.V1ConfigMapVolumeSource(
                        name=name + "-eval",
                    ),
                ),
                client.V1Volume(
                    name="k8s-volume",
                    config_map=client.V1ConfigMapVolumeSource(
                        name=name + "-k8s",
                    ),
                ),
                client.V1Volume(
                    name="shared-volume",
                    csi=client.V1CSIVolumeSource(
                        driver="gcsfuse.csi.storage.gke.io",
                        volume_attributes={
                            "bucketName": "gcp-us1-eval",
                            "mountOptions": "implicit-dirs,uid=1000,gid=1000,file-mode=0660,dir-mode=0770,o=noexec,o=noatime",
                        },
                    ),
                ),
                client.V1Volume(
                    name="spark-data",
                    csi=client.V1CSIVolumeSource(
                        driver="gcsfuse.csi.storage.gke.io",
                        volume_attributes={
                            "bucketName": "gcp-us1-spark-data",
                            "mountOptions": "implicit-dirs,uid=1000,gid=1000,file-mode=0660,dir-mode=0770,o=noexec,o=noatime,o=ro",
                        },
                    ),
                ),
            ],
        )
        return pod

    def cleanup_objects(self, name: str, delete_pod=True, delete_configmaps=True):
        """Try to cleanup anything we've created.

        This should never throw an exception.
        """
        if delete_configmaps:
            for suffix in ("-eval", "-k8s"):
                try:
                    logger.debug(f"Deleting configmap {name}{suffix}")
                    self.v1API.delete_namespaced_config_map(
                        name=name + suffix,
                        namespace=self.k8s_namespace,
                    )
                except client.ApiException as e:
                    if e.status != 404:
                        logger.warning(
                            f"Failed to delete configmap {name}{suffix}: {e}"
                        )
                except Exception as e:  # pylint: disable=broad-exception-caught
                    logger.warning(f"Failed to delete configmap {name}{suffix}: {e}")

        if not delete_pod:
            return

        try:
            logger.debug(f"Deleting pod {name}")
            self.v1API.delete_namespaced_pod(
                name=name,
                namespace=self.k8s_namespace,
                grace_period_seconds=0,
            )
        except client.ApiException as e:
            if e.status != 404:
                logger.warning(f"Failed to delete pod {name}: {e}")
        except Exception as e:  # pylint: disable=broad-exception-caught
            logger.warning(f"Failed to delete pod {name}: {e}")

    def test_repo(
        self,
        eval: EvalItem,
        timeout: int,
        lock: multiprocessing.Lock,  # type: ignore
        **kwargs,
    ) -> Tuple[ExecutionResult, ExecutionResult]:
        """Test a repo.

        test_repo always returns a tuple of (red, green)
        """
        # Create a name for this run that will be unique and no more than 50 chars long.
        run_uuid = ShortUUID().random(6)
        name = K8sActEvaluator.sanitize_k8s_string(
            f"{eval.repo_id[0:30]}-{eval.sha[0:12]}-{run_uuid}"
        )
        start_cb = kwargs.get("start_callback", None)
        end_cb = kwargs.get("end_callback", None)

        try:
            red_result, green_result = self.run(
                name,
                eval,
                timeout,
                lock=lock,
                start_callback=start_cb,
                end_callback=end_cb,
            )

            return (red_result, green_result)
        except PodFailureException as e:
            logger.debug(traceback.format_exc())
            logger.debug(f"Pod Error in test_repo for {eval.repo_id} {eval.sha}")
            return (
                ExecutionResult.NoneResult(stdout_path=e.log_path, data=e.data),
                ExecutionResult.NoneResult(stdout_path=e.log_path, data=e.data),
            )
        except Exception as e:
            logger.debug(traceback.format_exc())
            logger.error(f"Error in test_repo for {eval.repo_id} {eval.sha} {e}")
            return (ExecutionResult.NoneResult(), ExecutionResult.NoneResult())
        finally:
            # TODO(marcmac) skip only for failures?
            if not self._skip_cleanup:
                with lock:
                    self.cleanup_objects(name)

    def get_run_logs(self, pod_name: str) -> Tuple[Path, Path, Path, Path]:
        """Get the run logs.

        We don't clean these up, they're garbage collected externally.
        """
        pod_dir = self._shared_log_dir / pod_name
        return (
            pod_dir / "red_stdout.log",
            pod_dir / "red_stderr.log",
            pod_dir / "green_stdout.log",
            pod_dir / "green_stderr.log",
        )

    @property
    def v1API(self):
        if self._v1API is None:
            self._v1API = self.load_kube_config()
            assert self._v1API is not None
        return self._v1API

    def save_pod_logs(self, pod_name: str, namespace: str, container: str) -> Path:
        log_path = self._shared_log_dir / pod_name / "pod_stdout.log"
        with log_path.open("w") as f:
            f.write(
                self.v1API.read_namespaced_pod_log(
                    name=pod_name, namespace=namespace, container=container
                )
            )
        return log_path

    def _launch_pod(self, pod: client.V1Pod, lock: multiprocessing.Lock):  # type: ignore
        try:
            with lock:
                self.v1API.create_namespaced_pod(self.k8s_namespace, pod)
        except client.ApiException as e:
            if e.status != 201:
                logger.error(f"Pod creation failed {e.status} {e.reason}")
                raise e

    def run(
        self,
        name: str,
        eval: EvalItem,
        timeout: int,
        lock: multiprocessing.Lock,  # type: ignore
        start_callback: Callable[[], None] | None = None,
        end_callback: Callable[[], None] | None = None,
    ) -> Tuple[ExecutionResult, ExecutionResult]:
        # wait for pod to finish
        pod_lifecycle = {
            "start_time": time.time(),
            "submission_time": None,
            "running_time": None,
            "end_time": None,
        }
        pod_data: Dict[str, Any] = {"log_path": None, "pod_lifecycle": pod_lifecycle}

        cms = self._create_configmaps(name, eval)
        logger.debug(f"Creating configmaps for {eval.repo_id} {eval.sha}")
        for cm in cms:
            logger.debug(f"Creating configmap {cm.metadata.name}")  # type: ignore
            try:
                with lock:
                    self.v1API.create_namespaced_config_map(self.k8s_namespace, cm)
            except client.ApiException as e:
                logger.error(
                    f"Config map creation failed for {cm.metadata.name} ({eval.repo_id} {eval.sha}):"  # type: ignore
                )
                logger.debug(f"{e.status} {e.reason}")
                if e.status != 201:
                    raise e

        self._shared_log_dir.mkdir(parents=True, exist_ok=True)
        # create pod
        pod = self._create_pod(name, eval, timeout)
        self._launch_pod(pod, lock)

        pod_lifecycle["submission_time"] = time.time()
        stat = ""
        container_result = None
        _started = False
        resubmission_count = 0
        try:
            while (
                elapsed := time.time() - pod_lifecycle["start_time"]
            ) < self.hard_timeout_sec:
                pod_info = self.v1API.read_namespaced_pod(
                    name=pod.metadata.name,  # type: ignore
                    namespace=pod.metadata.namespace,  # type: ignore
                )
                stat = pod_info.status  # type: ignore

                phase = str(stat.phase).lower()
                try:
                    if phase == "succeeded":
                        pod_lifecycle["end_time"] = time.time()
                        try:
                            container_result = json.loads(
                                stat.container_statuses[0].state.terminated.message
                            )  # type: ignore
                        except Exception as e:
                            logger.debug(
                                f"Error parsing container result for pod {pod.metadata.name} ({eval.repo_id} {eval.sha}): {e}"  # type: ignore
                            )
                            raise e
                        break

                    elif phase in ("pending", "running"):
                        if (
                            phase == "pending"
                            and time.time() - pod_lifecycle["submission_time"]
                            > self.pending_timeout_sec
                        ):
                            resubmission_count += 1
                            if resubmission_count > 3:
                                raise RuntimeError(
                                    f"Pod {pod.metadata.name} is pending for more than {self.pending_timeout_sec} seconds, giving up"  # type: ignore
                                )
                            logger.warning(
                                f"Pod {pod.metadata.name} is pending for more than {self.pending_timeout_sec} seconds, deleting and resubmitting"  # type: ignore
                            )
                            print(stat.to_dict())
                            self.cleanup_objects(
                                pod.metadata.name,  # type: ignore
                                delete_configmaps=False,
                            )
                            self._launch_pod(pod, lock)
                            pod_lifecycle["start_time"] = time.time()
                            pod_lifecycle["submission_time"] = time.time()
                        if phase == "running" and pod_lifecycle["running_time"] is None:
                            pod_lifecycle["running_time"] = time.time()
                            if start_callback is not None:
                                _started = True
                                start_callback()
                        logger.debug(
                            f"({elapsed:.2f}s) Waiting for pod {pod.metadata.name} to finish ({phase})"  # type: ignore
                        )  # type: ignore
                        time.sleep(10)
                        continue
                    elif phase == "unknown":
                        raise RuntimeError("Pod unknown")
                    elif phase == "failed":
                        raise RuntimeError("Pod failed")
                    else:
                        raise RuntimeError(f"Unknown phase {phase}")
                except Exception as e:
                    log_path = None
                    try:
                        logger.debug(traceback.format_exc())
                        if (
                            stat.container_statuses is not None
                            and len(stat.container_statuses) > 0
                        ):
                            logger.debug(f"Pod failure: {e}, fetching logs")
                            log_path = self.save_pod_logs(
                                pod_name=pod.metadata.name,  # type: ignore
                                namespace=pod.metadata.namespace,  # type: ignore
                                container=stat.container_statuses[0].name,  # type: ignore
                            )
                    except Exception:
                        logger.debug(traceback.format_exc())
                        logger.debug(
                            f"Failed to read pod logs from {pod.metadata.name}"  # type: ignore
                        )
                    finally:
                        raise PodFailureException(
                            log_path=log_path, data=pod_data
                        ) from e
        finally:
            # TODO(marcmac) I should probably *always* call this and pass in whether or not the
            # start ever got called.
            if end_callback is not None and _started:
                end_callback()

        if container_result is None:  # timeout
            container_result = {
                "repo_id": eval.repo_id,
                "run_name": eval.run_name,
                "elapsed_time": str(timedelta(seconds=self.hard_timeout_sec)),
                "red_returncode": -1,
                "green_returncode": -1,
                "red_result_type": ResultType.TIMEOUT,
                "green_result_type": ResultType.TIMEOUT,
            }

        # get logs
        log_path = self.save_pod_logs(
            pod_name=pod.metadata.name,  # type: ignore
            namespace=pod.metadata.namespace,  # type: ignore
            container=stat.container_statuses[0].name,  # type: ignore
        )

        pod_data["pod_logs"] = log_path
        red_stdout, red_stderr, green_stdout, green_stderr = self.get_run_logs(
            pod.metadata.name  # type: ignore
        )

        return (
            ExecutionResult(
                stdout_path=red_stdout,
                stderr_path=red_stderr,
                returncode=container_result["red_returncode"],
                result_type=ResultType(container_result["red_result_type"]),
                data=pod_data,
            )
            if container_result["red_result_type"] is not None
            else ExecutionResult.NoneResult(data=pod_data),
            ExecutionResult(
                stdout_path=green_stdout,
                stderr_path=green_stderr,
                returncode=container_result["green_returncode"],
                result_type=ResultType(container_result["green_result_type"]),
                data=pod_data,
            )
            if container_result["green_result_type"] is not None
            else ExecutionResult.NoneResult(data=pod_data),
        )


def from_config(cf: dict, repo_provider: RepoProvider) -> ActEvaluator:  #
    cfg = copy.deepcopy(cf)
    eval_type = cfg.pop("type", None)
    cfg["repo_provider"] = repo_provider
    if eval_type == "LocalActEvaluator":
        return LocalActEvaluator(**cfg)
    elif eval_type == "K8sActEvaluator":
        return K8sActEvaluator(**cfg)
    else:
        raise ValueError(f"Unknown evaluator type {eval_type}")

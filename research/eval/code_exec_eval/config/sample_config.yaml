# Sample config for code_exec_eval

# K8sActEvaluator will run all jobs in k8s pods that are launched
# and cleaned up by the evaluator
evaluator:
  type: K8sActEvaluator
  k8s_context: gcp-us1
  k8s_namespace: gcp-us1
  debug_pod_shutdown: false
  debug_pod_startup: false
  hard_timeout_sec: 600
  skip_cleanup: true
  act_image: act

  # To use the full act image, set this to "full".  This improves the compatibility
  # with standard github runners which may enable some jobs to run that otherwise would
  # not, but the image is VERY LARGE and will slow down the testing.
  # (Seriously, increase your timeout.  The standard images are 1.5G, the full images are 40G.)
  #
  # act_image: full

  # Additional tolerations if necessary
  # tolerations:
  #   - key: r.augmentcode.com/foo
  #     operator: Equal
  #     effect: NoSchedule
  #     value: bar

# LocalActEvaluator will run all jobs against the local docker
# daemon.  The k8s evaluator uses this in the launched pods
# evaluator:
#   type: LocalActEvaluator
#   act_path: /home/<USER>/git/act/dist/local/act
#   docker_host: ssh://augment@marcmac-docker

# Concurrent processing
orchestrator:
  type: MultiProcessBatchOrchestrator
  concurrency: 50
  # progress_callback: display_results_callback  # Displays results as they come in

# Single process, mostly for debugging
# orchestrator:
#   type: SingleProcessBatchOrchestrator
#   # progress_callback: simple_progress_callback  # simple '.' progress display


eval_set:
  type: EvalSet
  # Path to a yaml file containing a list of eval items
  eval_path: /home/<USER>/augment/research/eval/code_exec_eval/config/test_evals.yaml

  # inline list
  # evals:
    # - sha: "83c6fcfac017fa4545cdaad5df58b60f38031755",
    #   fix_sha: "104834e985471c6791b6c50a4b15f8380d0c68e7",
    #   repo_id: "likec4/likec4",
    #   run_name: "check-packages",
    # - sha: "1ac396647d2c675378cfe20c03d1b62b3fb3b3d6",
    #   fix_sha: "a7edc54d4f1421671136c6945fca964a3344c196",
    #   repo_id: "bcgov/des-notifybc",
    #   run_name: "install-build-lint-and-test",


repo_provider:
  # Fetch repos, patches from local cache in spark-data
  type: CachedGitRepoProvider

  # Fetch repos, patches from github - note this may be subject to rate limiting
  # type: GithubRepoProvider


# this is the timeout passed into test_repo
# LocalActEvaluator uses it for each run (red, green) where it is eventually used
# in the call to "act push", so the total timeout can be 2x this.
timeout: 120

log_level: info

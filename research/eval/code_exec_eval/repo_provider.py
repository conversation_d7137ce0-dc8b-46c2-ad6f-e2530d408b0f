import copy
import hashlib
import logging
import shutil
import subprocess
import tempfile
from contextlib import contextmanager
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict

import requests
from evalset import EvalItem

logger = logging.getLogger(__name__)


class RepoProvider:
    def __init__(self):
        self.cwd: str = ""
        pass

    # TODO(marcmac) add a timing decorator
    def get_repo(self, eval_item: EvalItem):
        raise NotImplementedError()

    def fetch_patch(self, eval_item: EvalItem) -> str | None:
        """Fetch the patch from the source of the eval_item.

        This is used to apply the patch to the repo.
        """
        return eval_item.fix_text

    def patch(self, eval_item: EvalItem):
        raise NotImplementedError()

    @contextmanager
    def checkout(self, eval_item: EvalItem, apply_patch: bool = False):
        try:
            self.get_repo(eval_item)
            if apply_patch:
                self.patch(eval_item)
            yield self
        finally:
            self.cleanup()

    def cleanup(self):
        # Implement any cleanup logic here
        pass


class GitRepoProvider(RepoProvider):
    """Base class with some git stuff, not used directly."""

    def _run_git_command(
        self, cmd: list[str], cwd: str
    ) -> subprocess.CompletedProcess[str]:
        logger.debug(f"Running {' '.join([str(c) for c in cmd])} in {cwd}")
        try:
            sp = subprocess.run(
                cmd,
                check=True,
                cwd=cwd,
                capture_output=True,
                text=True,
            )
            return sp
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to run command {cmd}: {e.stderr}")
            raise

    def patch(self, eval_item: EvalItem):
        assert self.cwd, "No checkout to patch"
        patch = self.fetch_patch(eval_item=eval_item)
        if patch is not None:
            logger.info(f"Applying patch in {self.cwd} (len {len(patch)})")
            with tempfile.NamedTemporaryFile(mode="w") as f:
                f.write(patch)
                f.flush()
                self._run_git_command(["git", "apply", f.name], self.cwd)

    def cleanup(self):
        if self.cwd is not None:
            logger.debug(f"Cleaning up {self.cwd}")
            shutil.rmtree(self.cwd)
            self.cwd = ""


# TODO(marcmac) add support for authentication
class GithubRepoProvider(GitRepoProvider):
    """GitRepoProvider gets repositories and patches directly from github.

    This provider has an external dependency on github
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def get_repo(self, eval_item: EvalItem):
        self.cwd = tempfile.mkdtemp()
        logger.debug(f"Checking out {eval_item.repo_id}@{eval_item.sha} to {self.cwd}")
        self._run_git_command(["git", "init", self.cwd], self.cwd)
        url = f"https://github.com/{eval_item.repo_id}.git"
        self._run_git_command(
            ["git", "remote", "add", "origin", url],
            self.cwd,
        )
        self._run_git_command(
            ["git", "fetch", "--quiet", "--depth", "1", "origin", eval_item.sha],
            self.cwd,
        )
        self._run_git_command(
            ["git", "-c", "advice.detachedHead=false", "checkout", "FETCH_HEAD"],
            cwd=self.cwd,
        )
        self._run_git_command(["git", "checkout", "-b", "test"], self.cwd)

    def fetch_patch(self, eval_item: EvalItem) -> str:
        """Get the patch from github.

        We use the compare view to get the patch.
        https://docs.github.com/en/rest/reference/repos#compare-two-commits
        """
        patch = super().fetch_patch(eval_item)
        if patch is not None:
            return patch
        assert eval_item.fix_sha is not None, "No fix sha provided"
        patch_url = f"https://github.com/{eval_item.repo_id}/compare/{eval_item.sha}...{eval_item.fix_sha}.patch"
        r = requests.get(patch_url)
        r.raise_for_status()
        return r.text


class CachedGitRepoProvider(GitRepoProvider):
    """Git repo provider with locally cached repositories."""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def get_repo(self, eval_item: EvalItem):
        self.cwd = tempfile.mkdtemp()
        logger.debug(f"Checking out {eval_item.repo_id}@{eval_item.sha} to {self.cwd}")
        repo_path = self.get_tar_full_path(eval_item.repo_id)
        if repo_path.exists():
            logger.error(f"Found cached repo {repo_path}")
        else:
            logger.error(f"No cached repo found at {repo_path}")
            raise FileNotFoundError(f"No cached repo found at {repo_path}")

        subprocess.run(
            ["tar", "-C", self.cwd, "-xzf", str(repo_path)],
            check=True,
            cwd=self.cwd,
        )
        self._run_git_command(
            ["git", "config", "--global", "--add", "safe.directory", self.cwd],
            self.cwd,
        )
        # Use reset instead of checkout so that the tests don't have a detached head
        self._run_git_command(
            ["git", "reset", "--hard", eval_item.sha],
            self.cwd,
        )

    def fetch_patch(self, eval_item: EvalItem) -> str:
        """Get the patch from the repo.

        git diff  --patch sha...fix_sha
        """
        patch = super().fetch_patch(eval_item)
        if patch is not None:
            return patch

        assert eval_item.fix_sha is not None, "No fix sha provided"
        patch = self._run_git_command(
            ["git", "diff", "--patch", eval_item.sha, eval_item.fix_sha],
            self.cwd,
        ).stdout

        return patch

    # These values and static methods are all copied from research/data/utils/pr_v2.py
    # We replicate them here since we don't have the full augment research tree
    # in our execution environment
    SPARK_DATA_BUCKET = "gcp-us1-spark-data"

    # Base directory for all PR v2 data
    REPO_NAME_HASH_SIZE = 1000
    # We're mounting the spark-data bucket here for simplicity
    PR_V2_BASE = "/mnt/efs/spark-data/shared/pr_v2"
    DOWNLOADED_REPOS = f"{PR_V2_BASE}/downloaded_repos"
    REPO_TAR_EXT = ".tar.gz"

    @classmethod
    def hash_repo_name(cls, repo_name: str) -> int:
        """Hash the repo name to a number between 0 and REPO_NAME_HASH_SIZE - 1."""
        return (
            int(hashlib.sha256(repo_name.encode()).hexdigest(), 16)
            % cls.REPO_NAME_HASH_SIZE
        )

    @classmethod
    def convert_repo_to_filename(cls, repo_name: str) -> str:
        """Convert the full repo name to a valid filename without extension."""
        return repo_name.replace("/", "__")  # double underscores

    @classmethod
    def get_tar_dir_path(
        cls,
        repo_name: str,
        root: str = DOWNLOADED_REPOS,
    ) -> Path:
        """Get the directory path that contains the tar file for the given repo name."""
        return Path(root) / str(cls.hash_repo_name(repo_name))

    @classmethod
    def get_tar_filename(cls, repo_name: str) -> str:
        """Get the filename of the saved tar file for the given repo name."""
        return f"{cls.convert_repo_to_filename(repo_name)}{cls.REPO_TAR_EXT}"

    @classmethod
    def get_tar_full_path(
        cls,
        repo_name: str,
        root: str = DOWNLOADED_REPOS,
    ) -> Path:
        """Get the full path to the saved tar file for the given repo name."""
        return cls.get_tar_dir_path(repo_name, root) / cls.get_tar_filename(repo_name)


def from_config(cf: dict) -> RepoProvider:
    cfg = copy.deepcopy(cf)
    provider_type = cfg.pop("type", None)
    if provider_type == "GithubRepoProvider":
        return GithubRepoProvider(**cfg)
    if provider_type == "CachedGitRepoProvider":
        return CachedGitRepoProvider(**cfg)
    elif (
        provider_type is None
    ):  # Some evaluators don't need a provider so we pass the stub
        return RepoProvider()
    else:
        raise ValueError(f"Unknown provider type {provider_type}")

echo "Updating image"
pip install -r /configmap/k8s/requirements.txt

if [[ -f "${PATCH_FILE}" ]]; then
    echo "Loading patch from ${PATCH_FILE}"
    PATCH_TEXT=$(cat ${PATCH_FILE})
fi

echo "Creating config files"
cat>/tmp/config.yaml<<EOF
evaluator:
  type: LocalActEvaluator
  act_path: /usr/bin/act
  run_log_dir: ${RUN_LOG_DIR}
orchestrator:
  type: SingleProcessBatchOrchestrator
eval_set:
  type: EvalList
  evals:
    - sha: ${SHA}
      fix_sha: ${FIX_SHA}
      repo_id: ${REPO_ID}
      run_name: ${RUN_NAME}
      patch_text: ${PATCH_TEXT}
repo_provider:
  type: ${PROVIDER_TYPE}
timeout: ${TIMEOUT}
log_level: debug
EOF

image_name="act"
if [[ "${ACT_IMAGE}" == "full" ]]; then
    echo "Using full act image"
    image_name="full"
fi

mkdir -p /root/.config/act
cat>/root/.config/act/actrc<<EOF
-P ubuntu-latest=us-central1-docker.pkg.dev/augment-research-gsc/docker-us-central1/catthehacker/ubuntu:${image_name}-latest
-P ubuntu-22.04=us-central1-docker.pkg.dev/augment-research-gsc/docker-us-central1/catthehacker/ubuntu:${image_name}-22.04
-P ubuntu-20.04=us-central1-docker.pkg.dev/augment-research-gsc/docker-us-central1/catthehacker/ubuntu:${image_name}-20.04
-P ubuntu-18.04=us-central1-docker.pkg.dev/augment-research-gsc/docker-us-central1/catthehacker/ubuntu:${image_name}-18.04
EOF

echo "Starting docker"
dockerd > /tmp/docker.log 2>&1 &

echo "================CONFIG==============="
cat /tmp/config.yaml
echo "================CONFIG==============="
echo ""

if [[ -n "$DEBUG_POD_STARTUP" ]]; then
    while :; do
        if [[ ! -f /tmp/debug ]]; then
            echo "Waiting for /tmp/debug"
            sleep 10
        else
            echo "Starting"
            rm -f /tmp/debug
            break
        fi
    done
fi

set -x
time python3 /configmap/eval.py \
    --termination-log /dev/termination-log \
    /tmp/config.yaml

if [[ -n "$DEBUG_POD_SHUTDOWN" ]]; then
    while :; do
        if [[ ! -f /tmp/debug ]]; then
            echo "Waiting for /tmp/debug"
            sleep 10
        else
            echo "Starting"
            rm -f /tmp/debug
            break
        fi
    done
fi

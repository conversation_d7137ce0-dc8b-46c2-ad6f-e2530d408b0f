FROM python:3.11-slim

RUN apt-get update -y > /dev/null 2>&1 && \
    apt-get install -y \
    apt-transport-https \
    ca-certificates \
    curl \
    git \
    gnupg

RUN echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" > \
    /etc/apt/sources.list.d/google-cloud-sdk.list
RUN curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | \
    apt-key --keyring /usr/share/keyrings/cloud.google.gpg add -
RUN apt-get update -y && \
    apt-get install -y \
    google-cloud-cli \
    google-cloud-cli-gke-gcloud-auth-plugin

RUN curl --proto '=https' --tlsv1.2 \
    -sSf https://raw.githubusercontent.com/nektos/act/master/install.sh \
    | bash

COPY requirements.txt /requirements.txt

RUN pip install -r /requirements.txt
RUN rm -f /requirements.txt

COPY install_docker.sh /install_docker.sh
RUN bash /install_docker.sh

RUN mkdir -p /root/.config/act && \
    mkdir -p /root/.docker

COPY docker_config.json /root/.docker/config.json

COPY run.sh /run.sh

CMD ["/run.sh"]

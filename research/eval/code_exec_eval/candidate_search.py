#!/usr/bin/env python3
# Search for passing evaluations

import argparse
import copy
import logging
from shortuuid import ShortUUID
from datetime import datetime
from pathlib import Path
from typing import List

from research.eval.code_exec_eval.code_exec_config import CodeExecConfig
from research.eval.code_exec_eval.eval import BatchEvaluator, EvalResult
from research.eval.code_exec_eval.evalset import EvalSet, EvalSetGenerator

base_config = {
    "evaluator": {
        "type": "K8sActEvaluator",
        "k8s_context": "gcp-us1",
        "k8s_namespace": "gcp-us1",
        "debug_pod_shutdown": False,
        "debug_pod_startup": False,
        "hard_timeout_sec": 600,  # We kill the pod at this point
        "skip_cleanup": False,  # Set to True to leave the k8s objects available for debugging
    },
    "orchestrator": {
        "type": "MultiProcessBatchOrchestrator",
        "concurrency": 50,
    },
    # K8sActEvaluator passes this to the pod
    "repo_provider": {
        "type": "CachedGitRepoProvider",
    },
    # this is the timeout passed into test_repo
    # LocalActEvaluator uses it for each run (red, green) where it is eventually used
    # in the call to "act push", so the total timeout can be 2x this.
    "timeout": 600,
    "log_level": "info",
}


def candidate_search(
    eval_file: Path | None = None,
    timeout: int = 120,
    concurrency: int = 50,
    eval_count: int = 10,
    min_repo_size: int = 0,
    max_repo_size: int = 0,
) -> tuple[List[EvalResult], EvalSet, BatchEvaluator, int]:
    """Runs a random sample of files and checks for passing evaluations.

    Returns the evaluator and the random seed used for reproducibility.

    NOTE: setting the concurrency too high can crash the container your notebook runs in, if
    your homepod doesn't have enough memory.
    """
    config = copy.deepcopy(base_config)
    config["timeout"] = timeout

    st = datetime.now()
    if eval_file is None:
        esg = EvalSetGenerator()
        es = esg.generate_set(
            eval_count=eval_count,
            min_repo_size=min_repo_size,
            max_repo_size=max_repo_size,
        )
        dt = datetime.now() - st
        print(f"Generated {len(es)} evals in {dt}")
    else:
        es = EvalSet.from_parquet(eval_file)
        dt = datetime.now() - st
        print(f"Loaded {len(es)} evals in {dt}")

    config["eval_set"] = {"type": "EvalSet", "evalset": es}

    if concurrency == 0:
        concurrency = len(es)

    config["orchestrator"]["concurrency"] = concurrency

    cf = CodeExecConfig(config)

    be = BatchEvaluator.run_from_config(cf)
    st = be.get_stats()
    print(f"{'/'.join(st.keys())}: {'/'.join([str(v) for v in st.values()])}")

    success_results = list(be.get_success_results().values())
    print(
        f"{len(success_results)}/{len(es)} successful evals found ({(len(success_results)/len(es)*100):.2f}%)"
    )
    return success_results, es, be, concurrency


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    esa = parser.add_mutually_exclusive_group(required=True)
    esa.add_argument(
        "--eval_file", type=Path, help="Path to a predefined evaluation file."
    )
    esa.add_argument(
        "--generate_evals",
        type=int,
        help="Generate a new set of evals.",
    )
    parser.add_argument(
        "--concurrency",
        type=int,
        default=0,
        help="Number of concurrent evaluations. (default size of eval set)",
    )
    parser.add_argument(
        "--timeout",
        type=int,
        default=240,
        help="Timeout for each evaluation. (default 240)",
    )
    parser.add_argument(
        "--success_dir",
        type=Path,
        default=None,
        help="Path to save successful evals.",
        required=True,
    )
    parser.add_argument(
        "--min_repo_size",
        type=int,
        default=0,
        help="Minimum number of files in a repo to include, default 0.",
    )
    parser.add_argument(
        "--max_repo_size",
        type=int,
        default=0,
        help="Maximum number of files in a repo to include, default unlimited.",
    )
    parser.add_argument(
        "--log_level",
        type=str,
        default=None,
        help="Log level. (default info)",
    )
    args = parser.parse_args()

    # Use the CLI log level if provided, otherwise set it from the config later
    if args.log_level:
        base_config["log_level"] = args.log_level
        # Add a handler to send logs to stdout
        console_handler = logging.StreamHandler()
        console_handler.setLevel(args.log_level.upper())
        logging.getLogger().addHandler(console_handler)
        logging.getLogger().setLevel(args.log_level.upper())

    eval_count = 0
    conc = 0
    if args.eval_file:
        success_results, evals, ke, conc = candidate_search(
            eval_file=args.eval_file,
            eval_count=args.generate_evals,
            concurrency=args.concurrency,
            timeout=args.timeout,
        )
        eval_count = len(evals)
    else:
        total_evals = args.generate_evals
        max_batch_size = 1000
        idx = 0
        run_id = ShortUUID().random(6)

        success_dir = args.success_dir / run_id
        success_dir.mkdir(parents=True, exist_ok=True)
        while eval_count < total_evals:
            batch_size = min(max_batch_size, total_evals - eval_count)
            success_results, evals, ke, conc = candidate_search(
                eval_count=batch_size,
                concurrency=args.concurrency,
                timeout=args.timeout,
                min_repo_size=args.min_repo_size,
                max_repo_size=args.max_repo_size,
            )
            eval_count += len(evals)
            success_evals = EvalSet([er.eval_item for er in success_results])
            success_path = success_dir / f"success_{idx}_{len(success_evals)}.parquet"
            ppath = success_evals.write_parquet(success_path)
            print(f"EvalSet written to {ppath}")
            idx += 1

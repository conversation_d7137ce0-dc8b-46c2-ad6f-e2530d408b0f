#!/usr/bin/env python

"""Command-line tool to launch a candidate search in determined."""

import argparse
import sys
import urllib.parse
from pathlib import Path

import yaml
from determined.experimental import client as dc
from jobs.internal.augment_configs import DeterminedConfig

from research.environments import get_provider, list_providers, sync, detect_provider

auto_sync_dirs = [
    "load",
    "data_path",
    "train_data_paths",
    "eval_data_paths",
    "test_data_paths",
]


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "metaconfig", type=Path, default=None, help="Path to metaconfig file."
    )

    parser.add_argument(
        "--cluster",
        default="GCP-US1",
        required=False,
        help="Set the desired cluster",
        type=str,
        choices=list_providers(),
    )
    parser.add_argument(
        "--dump_config",
        default=False,
        action="store_true",
        required=False,
        help="Dump the augment config file only, do not start experiment",
    )
    parser.add_argument(
        "--dump_experiment_config",
        default=False,
        action="store_true",
        required=False,
        help="Dump the determined experiment config file only, do not start experiment",
    )
    parser.add_argument(
        "--skip_bazel",
        default=False,
        action="store_true",
        required=False,
        help="Skip the bazel build step before launching.  May result in no tokenizer in the determined environment.",
    )

    parser.add_argument(
        "--include_files",
        default=[],
        nargs="+",
        help="Additional include files to use for the experiment",
    )
    args, extra_args = parser.parse_known_args()

    return args, extra_args


def main():
    args, extra_args = parse_args()

    if args.metaconfig is not None:
        print(f"Loading metaconfig from {args.metaconfig}")
        metaconfig = yaml.safe_load(args.metaconfig.read_text())
        config = DeterminedConfig(
            metaconfig=metaconfig, cluster=args.cluster, skip_neox_args=True
        )
        config.augment["command_line"] = " ".join(sys.argv)
        if args.dump_config:
            print(str(config))
            return 0
        if args.dump_experiment_config:
            print(yaml.dump(config.experiment_config, default_flow_style=False))
            return 0

        # Fail early if we're not logged into determined
        if config.verify_determined_login() is None:
            return 1

        # Include files from the command line may be relative, we need to resolve them
        include_files = [Path(f).resolve() for f in args.include_files]
        # AU-626 for interactive runs, make sure we build the tokenizer artifacts
        # with bazel before we push the directory.
        experiment_id = config.run(
            run_bazel=not args.skip_bazel, include_files=include_files
        )
        if experiment_id is not None:
            print(f"Created experiment {experiment_id}\n")
            print(
                urllib.parse.urljoin(
                    config.determined_url, f"det/experiments/{experiment_id}"
                )
            )
            return 0

        return 1


if __name__ == "__main__":
    sys.exit(main())

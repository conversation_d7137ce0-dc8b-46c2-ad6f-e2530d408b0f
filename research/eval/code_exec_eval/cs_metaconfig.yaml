determined:
  name: null
  description: null
  workspace: CodeExecEval
  project: CandidateSearch
  max_restarts: 0
# augment is a dictionary of augment specific arguments for our code extensions
augment:
  service_account_name: determined-service-account
  slack_notifications:
    enabled: False             # Enable slack notifications
    slack_user: Uxxxxxxxxxx    # Set to your slack user ID to send notifications.
    notify_validations: False  # Set to true to send a message on every validation pass - may be noisy
  # Common args for both training and evaluation
  podspec_path: "CPU.yaml"  # Path to the podspec file.  Absolute, or relative to "templates"
  gpu_count: 0  # How many GPUs to ask for

  # For the candidate search, a 10:1 ratio of batch size to target count is a good rule of thumb
  environment_variables:
    EVAL_BATCH_SIZE: 1000   # How many eval batches to run, also used for concurrency
    EVAL_TARGET_COUNT: 100  # How many successful evals we're looking for
    # EVAL_MIN_REPO_SIZE: 0  # Min, max repo sizes (0 for no limits)
    # EVAL_MAX_REPO_SIZE: 1000

  # candidate search entrypoint
  entrypoint: "/run/determined/workdir/research/eval/code_exec_eval/code_exec.sh"

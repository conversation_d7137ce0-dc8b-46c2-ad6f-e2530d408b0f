import pytest
import subprocess
import sys
import tempfile
from pathlib import Path

import yaml


@pytest.mark.parametrize(
    "config, args",
    [
        # write a yaml string for a gold code instruction system that I can run locally
        pytest.param(
            {"system": {"name": "gold_system"}, "task": {"name": "cceval", "limit": 1}},
            [],
        ),
        pytest.param(
            {
                "system": {"name": "gold_system"},
                "task": {"name": "cceval", "limit": "x"},
            },
            ["--limit", "1"],
            marks=[pytest.mark.skip("Should be tested by examining config")],
        ),
        pytest.param(
            {
                "system": {"name": "gold_system"},
                "task": {"name": "cceval", "limit": "x"},
            },
            ["--overrides", "task.limit=1", "--"],
            marks=[pytest.mark.skip("Should be tested by examining config")],
        ),
    ],
)
def test_eval_local(config: dict, args: list):
    """Test local evals."""

    cwd = Path(__file__).parent.resolve()
    # use a temporary file for config.yml
    with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
        yaml.dump(config, f)
        f.flush()
        subprocess.check_call(
            [
                "python",
                "eval.py",
                "--local",
                *args,
                f.name,
            ],
            cwd=cwd,
            stdout=sys.stdout,
            stderr=sys.stderr,
        )


def test_eval_error():
    """Checks that the eval throws an error with limit=x."""

    config = {
        "system": {"name": "gold_system"},
        "task": {"name": "cceval", "limit": "x"},
    }
    cwd = Path(__file__).parent.resolve()
    # use a temporary file for config.yml
    with pytest.raises(subprocess.CalledProcessError):
        with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
            yaml.dump(config, f)
            f.flush()
            subprocess.check_call(
                [
                    "python",
                    "eval.py",
                    "--local",
                    f.name,
                ],
                cwd=cwd,
                stdout=sys.stdout,
                stderr=sys.stderr,
            )

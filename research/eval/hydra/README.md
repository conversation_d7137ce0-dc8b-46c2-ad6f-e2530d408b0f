# Hydra Test Suite

## Table of content
### - [Command-line Interface](#cli)
### - [Test a completion's correctness](#run-test)
### - [Run in Determined](#determined)
### - [Hydra's Python library](#python-lib)
### - [Glossary](#glossary)
### - [Advanced usage: add new repositories and completion sites](#add-new-repo)
### - [Advanced usage: add new repositories with coverage info](#add-new-repo-cov)


# <br><a name="cli"></a>Command-line Interface (CLI)
Hydra has a CLI that is automatically installed with `augment_research`. To test
your installation:
```bash
(docker) $ hydra --help
usage: hydra [-h] {init,clean,snap,build,push,show-remote,run,suggest} ...

positional arguments:
  {init,clean,snap,build,push,show-remote,run,suggest}

optional arguments:
  -h, --help            show this help message and exit
```

If for any reason, you need to install `hydra` manually, then you can do the following:
```bash
(docker) $ cd ~/src/augment/research
(docker) $ pip install -e .
(docker) $ export PATH="${PATH}:/home/<USER>/.local/bin"
(docker) $ which hydra
/home/<USER>/.local/bin/hydra
(docker) $ hydra --help
# same output as above
```

# <br><a name="run-test"></a>Test a completion's correctness
The simplest use of `hydra` is: given a list of [completion
sites](#completion-sites), runs each of the site and tells whether the site is
correct or not.  A site is counted as correct if it passes all [unit
tests](#unit-tests) in the site's [repository](#respository).

`hydra` typically expects completion sites to be stored in a JSONL file, which
is just a file of multiple lines where each line encodes a valid JSON entry.
This file is usually called a `patches_file`.

Given a JSONL file of completion sites, the following command-line would invoke
`hydra` to test all sites in the file:
```bash
d="your-name" \
f="/mnt/efs/augment/path/to/your/completion_sites.jsonl" \
hydra run \
    --patches-file="${f}.jsonl" \
    --result-path="${f}.hydra" \
    --driver-name="${d}" \
    --max-parallel-runs=20 \
    --local-timeout=450 \
    --global-timeout=1800 \
    --patch-limit=2
```
To understand the meanings of these flags, you can run `hydra run --help`. The
following output might be outdated:
```bash
(docker) $ hydra run --help
usage: hydra run [-h] [--driver-name DRIVER_NAME]
                 [--result-path RESULT_PATH]
                 [--result-file RESULT_FILE]
                 [--global-timeout GLOBAL_TIMEOUT]
                 [--max-parallel-runs MAX_PARALLEL_RUNS]
                 [--local-timeout LOCAL_TIMEOUT]
                 [--patch-limit PATCH_LIMIT]
                 [--patches-file PATCHES_FILE]
                 [--docker-imgs-file DOCER_IMGS_FILE]
                 [--random-seed RANDOM_SEED] [--run-local]

optional arguments:
  -h, --help            show this help message and exit
  --driver-name DRIVER_NAME
                        Driver name in K8s. If not provided, a random name is used.
  --result-path RESULT_PATH
                        Path to save a `result.json` file.
  --result-file RESULT_FILE
                        If provided, will save the result to this file name.
  --global-timeout GLOBAL_TIMEOUT
                        Absolute timeout for the entire eval job. After this number of seconds, all running pods will be force-stopped.
  --max-parallel-runs MAX_PARALLEL_RUNS
                        Maximum number of eval jobs to run in parallel.
  --local-timeout LOCAL_TIMEOUT
                        Number of seconds until the test timeout for each eval pod.
  --patch-limit PATCH_LIMIT
                        If positive, only submit and test this number of patches.
  --patches-file PATCHES_FILE
                        Path to a JSONL file that contains the patches to replace.
  --docker-imgs-file DOCKER_IMGS_FILE
                        Path to docker imgs file (like included_reops.yaml)
  --random-seed RANDOM_SEED
                        A common random seed.
```

# <br><a name="determined"></a>Run in Determined
Please refer to this [Notion page](https://www.notion.so/Hydra-7c0f6c3e35334fd48d8c2bfab39bd083).

# <br><a name="python-lib"></a>Hydra's Python library
Another way to use Hydra is via its Python library. We currently expose two
tools from Hydra: the class `Driver` and the function `run_in_batch`.

- `run_in_batch` points to the function [`run`](../hydra/driver.py). See the
function's documentation for the detailed argument. Essentially, `run` creates a
`Driver` object and then uses the object to orchestrate the completions provided
in `patches_file` to run in Augment's shared Kubernetes cluster.

- `Driver` provides a finer-grained approach to run tests. Most of its arguments
come from those of the `run` function above, so you can learn their meanings
there. It is highly encouraged that you specify a `driver_name` when you create
a `Driver` object, as the name will appear on our shared Kubernetes cluster.
Providing an easily recognizable name helps to track your experiments and to
cleanup orphaned jobs. If no `driver_name` is provided, a `uuid` one will be
used.

This [notebook](notebooks/research_model.ipynb) shows a simple example where
a `CodeGen_350M_Multi` is loaded to generate completion sites, and then the
completion sites are sent to a `hydra` `Driver` to evaluate.

# <br><a name="glossary"></a>Glossary

**Completion sites** or **test sites** or **test cases**
refers to an example that you pass into a language model to get a generation.

**Repository** aka **repo** means a repository that we use as context for
*completion sites.
If you want to use a repo to run tests `hydra`, you need to register the repo
with `hydra`, typically by `hydra init`, `hydra build`, and `hydra push` (see
[Advanced usage](#add-new-repo)).

**Tests** generally refer to the unit tests in repositories.



# <br><a name="add-new-repo"></a>Advanced usage: add new repositories and completion sites

The first step is to identify a repository that you want to add to `hydra`.
Ideally, the repo should have unit tests written in it, so that `hydra` can
later test whether a completion site in the repo is correct or not. Once you
have identified the repo, clone or copy it into a path under `/mnt/efs/augment`.
For demonstration purposes, we look at the repo
`/mnt/efs/augment/user/hieu/repos/scrapy`. You can "register" this repo into
`hydra` by following the steps below.

## 1. Initialize `hydra` at your repo path:
This step requires only a simple command:
```bash
(docker) scrapy $ hydra init
(docker) scrapy $ ls -lah
drwxrwxr-x  2 <USER> <GROUP>    6 Jun 28 20:06 .hydra
# [...] more files and folders
```
You should notice a `.hydra` folder in your repo path. This is where
`hydra` stores relevant information about your repo.

`hydra` provides a few start guidances for certain languages. You can use
them by `hydra init --lang=<your-language>`. The default language is Python.

Once you are done with `hydra`, you can can run `hydra clean`, which simply
removes the `.hydra` folder that `hydra init` created.

## 2. Iterate to build your test environment
**[This step assumes that you know `docker`]**
Now you need to create an environment where you can run the tests in your repo.
An environment here refers to a Docker container that will run your completion sites.
If you look into your `.hydra` folder, you will see the following files:
```bash
(docker) scrapy $ ls .hydra
-rw-r--r--  1 <USER> <GROUP>  74 Jun 15 03:17 .dockerignore
-rw-r--r--  1 <USER> <GROUP> 318 Jun 15 03:17 Dockerfile
-rwxr-xr-x  1 <USER> <GROUP> 474 Jun 15 03:17 build.sh
-rw-r--r--  1 <USER> <GROUP> 746 Jun 15 23:30 requirements.txt
-rwxr-xr-x  1 <USER> <GROUP>  40 Jun 15 03:17 run.sh
```

Let's inspect the `Dockerfile`:
```bash
(docker) scrapy $ cat .hydra/Dockerfile
## Stage 1: Builder
FROM python:3.9 AS BUILDER

COPY . /code/.

RUN useradd -ms /bin/bash hydra
USER hydra

RUN cd /code && \
    pip install --no-cache-dir --user -r .hydra/requirements.txt

# Second stage: create the final image
FROM python:3.9-slim

RUN useradd -ms /bin/bash hydra
USER hydra
COPY --chown=hydra:hydra --from=BUILDER /home/<USER>/.local /home/<USER>/.local
COPY --chown=hydra:hydra --from=BUILDER /code /code
ENV PATH=/hydra/.local/bin:$PATH
```

That's quite a vanilla `Dockerfile` for `python`. The directives in this
file simply install the dependecies for the repo from `requirements.txt`.

Now you will need to manually figure out these dependencies. Some Python
repos come with their own `requirements.txt` files, so you can append them
to `.hydra/requirements.txt`. For other repos, you might need to install
the repos by yourself to actually figure out the dependencies.

We suggest the following workflow:
```bash
(docker) scrapy $ hydra build
# Build a Docker image using the existing Dockerfile. By default, only the
# pytest library will be installed. If you are a Docker guru, feel free to
# change the Dockerfile as you see fit.

(docker) scrapy $ docker image ls
REPOSITORY                   TAG       IMAGE ID       CREATED          SIZE
scrapy                       latest    827b07af3f9e   14 minutes ago   225MB
# verify that you have the image named after your repo

(docker) scrapy $ docker run -it --rm --name container $(basename ${PWD}) bash
# this will take you into the Docker container `scrapy`.

hydra@f84efbf34d35:/$ whoami
hydra

hydra@f84efbf34d35:/$ cd /code
# /code is your repo path in the containter

hydra@f84efbf34d35:/$ .hydra/run.sh
```
`.hydra/run.sh` is the default test command for your repo. We expect the above
to run all unit tests in your repo, but we cannot be sure since this highly depends
on the repo's organizing structure.

As a result, you will need to manually install more dependencies, perhaps
even change the `.hydra/run.sh` file to disable certain tests, so that your
run passes.

You have learned what dependencies need to be installed, you can either
`docker commit` the running containter as a future image, or you can go
back and modify your `Dockerfile` to reflect all the setup steps needed,
and finally `hydra build` to get the working container.

Once you have the working containter, you can do:
```bash
(docker) scrapy $ hydra push
```
This will push the container that you built to Augment's shared Docker registry.

## 2.a Tag your image/container and update hydra config
Hydra will only work with images that it's been told to use. Each image must
have an explicit tag (not latest). For a given repo, Hydra will construct the
image name from the repo name and it will look up the tag from this config file:

   src/augment/research/eval/hydra/supported_repos.yaml

Here are the commands for pulling an image, tagging it, and pushing it back:
```bash
(docker) scrapy $ docker pull au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/exec-eval/scrapy
(docker) scrapy $ docker image tag au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/exec-eval/scrapy au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/exec-eval/scrapy:1
(docker) scrapy $ docker push au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/exec-eval/scrapy:1
```
Having done this, you should then edit the supported_repos.yaml, to add your
repo with it's corresponding tag.

## 3. Identify completion sites
Hydra provides two methods to create completion sites for a repository.

## Automatic
*The first method* randomly suggests a completion site for you. For instance:
```bash
(docker) scrapy $ hydra suggest
--- scrapy/spiderloader.py
+++ scrapy/spiderloader.py
@@ -82,9 +82,6 @@
         """
         Return the list of spider names that can handle the given request.
         """
-        return [
-            name for name, cls in self._spiders.items() if cls.handles_request(request)
-        ]

     def list(self):
         """
```
This output simply means that `hydra` suggests that you can:
  - Go to line `82` in the file `scrapy/spiderloader.py` and delete those three lines.
  - Ask your coding model to generate some code to fill in these lines.
  - Test whether your model's generated code passes all tests.

You can add use `hydra suggest --random-seed=31415` to ensure that `hydra`
suggests the same thing across runs. You need to change the seed to have a
different suggestion.

If you see a suggestion you would like to use, you can save it to a patches file:
```bash
(docker) scrapy $ hydra suggest --random-seed=31415 --output-json >> /path/to/your/patches_file.jsonl
```

## Manual
*The second method* lets you manually edit some code in the repo, then run the
tests using the modified changes, see whether you would like your model to fill
in the code you edited, and then potentially save the completion site.

You can do this using the commands:
```bash
(docker) scrapy $ hydra snap take  # takes a snapshot of the repo

(docker) scrapy $ vim scrapy/...  # manually edit a function in a file

(docker) scrapy $ hydra snap diff
# View the edits that you just made. This is how Hydra will store in a JSONL
# file to run tests.

(docker) scrapy $ hydra snap diff --output-json
# Same as above, but the output is ready to be a line in a JSONL file.

(docker) scrapy $ hydra snap diff --output-json >> /path/to/your/patches_file.jsonl
# Save the diff to be run
```

### - [Advanced usage: add new repositories with coverage info](#add-new-repo-cov)



# <br><a name="add-new-repo-cov"></a>Advanced usage: add new repositories and completion sites
# Step 1: Prepare the `docker` image
Initialize, build the image, and run it.
```bash
USER_NAME=""
REPO_NAME=""
REPO_SHA=$(git rev-parse HEAD)

hydra init --user-name=${USER_NAME} --repo-name=${REPO_NAME} --repo-sha=${REPO_SHA}

hydra build

hydra container start \
  --image-name=$(echo "$(basename ${PWD})" | tr '[:upper:]' '[:lower:]')
```

Next, make sure that you can pass the `pytest` runs:
```bash
# this should install most dependencies, but sometimes not all
hydra container exec \
  --container-name=$(echo "$(basename ${PWD})-container" | tr '[:upper:]' '[:lower:]') \
  --command-line="pip install ."

# this will just fail, since you don't have any depdendencies
hydra container exec \
  --container-name=$(echo "$(basename ${PWD})-container" | tr '[:upper:]' '[:lower:]') \
  --command-line="pytest"
```

Just keep doing `pytest` and installing missing stuffs until `pytest` passes.

Some tests are very slow, so you might want to skip them by changing `.hydra/run.sh`

Once you are done, save the dependencies you installed because you will have to
rebuild your image.

NOTE: you will need to manually remove the dependency that is the repo itself, such as
`patchcore @ file:///code`

```bash
hydra container exec \
  --container-name=$(echo "$(basename ${PWD})-container" | tr '[:upper:]' '[:lower:]') \
  --command-line="pip freeze" > .hydra/requirements.txt

hydra container stop \
  --container-name=$(echo "$(basename ${PWD})-container" | tr '[:upper:]' '[:lower:]') \
  --remove
```

Now you can rebuild and push the image to Augment's Docker registry
```bash
hydra build

hydra push --image-name=$(echo "$(basename ${PWD})" | tr '[:upper:]' '[:lower:]')
```

# Step 2: Collect coverage information
You need a container running, so do this:
```bash
hydra container start \
  --image-name=$(echo "$(basename ${PWD})" | tr '[:upper:]' '[:lower:]')
```

Now collect the coverage information by running them. Note that if you repo has
many tests, you might want `-n 10`.
```bash
hydra container exec \
  --container-name=$(echo "$(basename ${PWD})-container" | tr '[:upper:]' '[:lower:]') \
  --command-line="pytest --cov=. --cov-config=.hydra/.coveragerc --cov-report=json"
```

Depending on the repo, the command above can fail. In such cases, run the two
commands below. They usually work. However, further failures might happen, and
you might need to resolve them by yourself using specific knowledge about your
repo.

```bash
# If you did not use `-n` in the previous command, you can skip this
hydra container exec \
  --container-name=$(echo "$(basename ${PWD})-container" | tr '[:upper:]' '[:lower:]') \
  --command-line="coverage combine"

# Get the coverage information file
hydra container exec \
  --container-name=$(echo "$(basename ${PWD})-container" | tr '[:upper:]' '[:lower:]') \
  --command-line="coverage json --show-contexts --ignore-errors"
```

If you succeed, you will have a `coverage.json` file in your container. This will show
the file:
```bash
hydra container exec \
  --container-name=$(echo "$(basename ${PWD})-container" | tr '[:upper:]' '[:lower:]') \
  --command-line="ls -lah coverage.json"
```

# Step 3: Call `hydra cov prepare` with the coverage info

First, copy the following files out of the container into `.hydra`

NOTE: The way this code snippet copies the files is by `cat`-ing and directing
the outputs. You can try `docker copy` if you know what you are doing.

You probably want to ensure that the files exist by `ls` in your container.

```bash
hydra container exec \
  --container-name=$(echo "$(basename ${PWD})-container" | tr '[:upper:]' '[:lower:]') \
  --command-line="cat .pytest_cache/v/cache/lastfailed" > .hydra/failed_ids.json

hydra container exec \
  --container-name=$(echo "$(basename ${PWD})-container" | tr '[:upper:]' '[:lower:]') \
  --command-line="cat .pytest_cache/v/cache/nodeids" > .hydra/nodeids.json

hydra container exec \
  --container-name=$(echo "$(basename ${PWD})-container" | tr '[:upper:]' '[:lower:]') \
  --command-line="cat coverage.json" > .hydra/coverage.json
```

Once you have done those, you are read for:
```bash
hydra cov prepare
```

One sanity check is to make sure that you have this file `.hydra/cov_info.json`
*outside of your container*.
```bash
ls .hydra/cov_info.json
```

# Step 4: Happy using the coverage info
At this point, you should be able to get suggestions with coverage info

```bash
hydra suggest -n 1 -c -m "3-7-lines"
```

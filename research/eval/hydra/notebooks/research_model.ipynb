{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Protopyting `research_models` and `hydra`"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import os\n", "# set single GPU environment\n", "os.environ['CUDA_VISIBLE_DEVICES'] = '0'\n", "import threading\n", "\n", "from research import core\n", "from research.models.all_models import get_model, GenerationOptions"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Load `research_models`'s checkpoint"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = get_model('starcoderbase_1b')\n", "model.load()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Load data to generate completions"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Load 46 repo patches.\n"]}], "source": ["import pathlib\n", "from research.eval import patch_lib\n", "\n", "patches_path = \"/mnt/efs/augment/data/eval/hydra/datasets/repoeval_functions/CarperAI/trlx_patches.jsonl\"\n", "with pathlib.Path(patches_path).open() as f:\n", "    repo_patches = [patch_lib.Patch.from_json(line) for line in f.readlines()]\n", "print(f\"Load {len(repo_patches)} repo patches.\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": []}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Creates a `hydra Driver` to execute patches.\n", "\n", "In each step of the following `for` loop:\n", "\n", "  * The research model receives a `patch_json` and generates a `completion`.\n", "\n", "  * The `completion` is sent to the Hydra `Driver` to run and evaluate for PASSED/FAILED via `driver.dispatch`.\n", "    \n", "    - Note that all the `dispatch` are done in parallel to utilize all the parallel features of Hydra's driver.\n", "\n", "    - But the completion generations are done sequentially, since we don't expect `model` to handle multiple requests at once (or do we?)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import getpass\n", "from research.eval import hydra\n", "\n", "HYDRA_DRIVER_NAME = f\"{getpass.getuser()}-dev-hydra-notebook-example\"\n", "HYDRA_LOCAL_TIMEOUT_SECS = 30\n", "HYDRA_GLOBAL_TIMEOUT_SECS = 180\n", "HYDRA_NUM_PATCHES_LIMIT = 5\n", "\n", "with hydra.Driver(\n", "    driver_name=HYDRA_DRIVER_NAME,\n", "    local_timeout_secs=HYDRA_LOCAL_TIMEOUT_SECS,\n", "    global_timeout_secs=HYDRA_GLOBAL_TIMEOUT_SECS,\n", ") as driver:\n", "\n", "    dispatch_threads = []\n", "\n", "    for patch in repo_patches[:HYDRA_NUM_PATCHES_LIMIT]:\n", "        print(f\"\\rGenerate completion for {patch.patch_id}.\")\n", "        prefix = patch.file_content[:patch.char_start]\n", "        suffix = patch.file_content[patch.char_end:]\n", "        gen_limit = 128\n", "        len_limit = model.seq_length - gen_limit\n", "        prefix = prefix[-len_limit:] if len(prefix) > len_limit else prefix\n", "        completion = model.generate(\n", "            core.ModelInput(prefix=prefix),\n", "            GenerationOptions(temperature=0., max_generated_tokens=gen_limit))\n", "        patch = patch.with_patch_content(completion)\n", "\n", "        thread = threading.Thread(target=driver.dispatch, args=(patch,))\n", "        thread.start()\n", "        dispatch_threads.append(thread)\n", "\n", "\n", "    for thread in dispatch_threads:\n", "        thread.join()\n", "    driver.collect_result()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
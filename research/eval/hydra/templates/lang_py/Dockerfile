# Stage 1: Builder
FROM python:3.9 AS BUILDER

COPY . /code/.

RUN useradd -ms /bin/bash hydra && \
    usermod -aG sudo hydra && \
    echo 'myuser ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers

USER hydra

RUN cd /code && \
    pip install --no-cache-dir --user -r .hydra/requirements.txt

# Stage 2: Final image
FROM python:3.9-slim

# Install essential build tools and Go
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    sudo \
    ca-certificates \
    curl \
    git \
    && \
    rm -rf /var/lib/apt/lists/*

# Create a non-root user "hydra" and add "hydra" user to the sudoers list
RUN useradd -ms /bin/bash hydra
RUN usermod -aG sudo hydra

# Allow "hydra" user to use sudo without password
RUN echo "hydra ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

USER hydra
COPY --chown=hydra:hydra --from=BUILDER /home/<USER>/.local /home/<USER>/.local
COPY --chown=hydra:hydra --from=BUILDER /code /code
ENV PATH="/home/<USER>/.local/bin:${PATH}"
ENV PYTHONPATH="/home/<USER>/.local/lib/python3.9/site-packages:/home/<USER>/.local/bin:${PYTHONPATH}"

# Some repos might download from HuggingFace, causing non-deterministic test results.
# We explicitly disallow them.
#
# As a result, some repos will have tests that fail by default.
# It is the responsibility of the person who adds new repos to ensure that the tests
# pass, e.g., by caching from HuggingFace manually, or by disabling the relevant tests.
# (normally, those tests that require HuggingFace are not important).
ENV HF_DATASETS_OFFLINE=1
ENV TRANSFORMERS_OFFLINE=1

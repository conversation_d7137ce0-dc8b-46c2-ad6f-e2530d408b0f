# Use the official Python 3.9-slim image as the base
FROM python:3.9

# Install essential build tools and Go
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    sudo \
    ca-certificates \
    curl \
    git \
    && \
    rm -rf /var/lib/apt/lists/* && \
    curl -L https://golang.org/dl/go1.20.linux-amd64.tar.gz | tar -C /usr/local -xzf -

# Set Go environment variables
ENV PATH="/usr/local/go/bin:${PATH}"
ENV GOPATH="/home/<USER>/go"
ENV GOBIN="/home/<USER>/go/bin"
ENV GOCACHE="/home/<USER>/.cache"

# Copy the Go application source code into the container
COPY . /code

# Create a non-root user "hydra" and change the ownership of /code to "hydra" user
RUN useradd -ms /bin/bash hydra && \
    chown -R hydra:hydra /code

# Add "hydra" user to the sudoers list
RUN usermod -aG sudo hydra

# Allow "hydra" user to use sudo without password
RUN echo "hydra ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Switch to the "hydra" user
USER hydra

# Download Go modules
COPY go.mod go.sum /code/
RUN cd /code && \
    go mod download

# Build the Go repository
RUN cd /code && \
    go build -v -buildvcs=false ./...

# Go has great dependency checking, so let's cache test results and trust that
# it will rebuild / rerun appropriately for changed files
RUN cd /code && \
    go test ./...

# Set the PATH environment variable to include the Go binaries
ENV PATH="${GOPATH}/bin:${PATH}"

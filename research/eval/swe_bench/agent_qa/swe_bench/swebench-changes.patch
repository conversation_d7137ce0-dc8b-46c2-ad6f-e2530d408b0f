diff --git a/swebench/harness/docker_build.py b/swebench/harness/docker_build.py
index d7ebb30..8dbb6b0 100644
--- a/swebench/harness/docker_build.py
+++ b/swebench/harness/docker_build.py
@@ -184,6 +184,7 @@ def build_base_images(
     base_images = {
         x.base_image_key: (x.base_dockerfile, x.platform) for x in test_specs
     }
+    print(f"Building base images: {list(base_images.keys())}")

     # Build the base images
     for image_name, (dockerfile, platform) in base_images.items():
@@ -278,7 +279,7 @@ def build_env_images(
         env_image_keys = {x.env_image_key for x in get_test_specs_from_dataset(dataset)}
         for key in env_image_keys:
             remove_image(client, key, "quiet")
-    build_base_images(client, dataset, force_rebuild)
+    build_base_images(client, dataset, force_rebuild=force_rebuild)
     configs_to_build = get_env_configs_to_build(client, dataset)
     if len(configs_to_build) == 0:
         print("No environment images need to be built.")
@@ -329,7 +330,8 @@ def build_instance_images(
     if force_rebuild:
         for spec in test_specs:
             remove_image(client, spec.instance_image_key, "quiet")
-    _, env_failed = build_env_images(client, test_specs, force_rebuild, max_workers)
+
+    _, env_failed = build_env_images(client, test_specs, False, max_workers)

     if len(env_failed) > 0:
         # Don't build images for instances that depend on failed-to-build env images
@@ -413,7 +415,7 @@ def build_instance_image(
             platform=test_spec.platform,
             client=client,
             build_dir=build_dir,
-            nocache=nocache,
+            nocache=True,
         )
     else:
         logger.info(f"Image {image_name} already exists, skipping build.")
diff --git a/swebench/harness/docker_utils.py b/swebench/harness/docker_utils.py
index c8ebd75..05d4997 100644
--- a/swebench/harness/docker_utils.py
+++ b/swebench/harness/docker_utils.py
@@ -14,6 +14,7 @@ from docker.models.containers import Container

 HEREDOC_DELIMITER = "EOF_1399519320"  # different from dataset HEREDOC_DELIMITERs!

+_docker_lock = threading.Lock()

 def copy_to_container(container: Container, src: Path, dst: Path):
     """
@@ -261,9 +262,20 @@ def list_images(client: docker.DockerClient):
     """
     List all images from the Docker client.
     """
-    # don't use this in multi-threaded context
-    return {tag for i in client.images.list(all=True) for tag in i.tags}
-
+    try:
+        # Get raw list of images first
+        resp = client.api.images()
+        images = []
+        for r in resp:
+            try:
+                images.append(client.images.get(r["Id"]))
+            except docker.errors.ImageNotFound:
+                # Skip images that were removed during listing
+                continue
+        return {tag for img in images for tag in img.tags}
+    except docker.errors.APIError as e:
+        logging.warning(f"Error listing images: {e}")
+        return set()

 def clean_images(
         client: docker.DockerClient,

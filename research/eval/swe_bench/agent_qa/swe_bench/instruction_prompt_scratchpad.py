PROMPT = """
<uploaded_files>
{location}
</uploaded_files>
I've uploaded a python code repository in the directory {location} (not in /tmp/inputs). Consider the following PR description:

<pr_description>
{pr_description}
</pr_description>

Can you help me implement the necessary changes to the repository so that the requirements specified in the <pr_description> are met?
I've already taken care of all changes to any of the test files described in the <pr_description>. This means you DON'T have to modify the testing logic or any of the tests in any way!

Your task is to make the minimal changes to non-tests files in the {location} directory to ensure the <pr_description> is satisfied.

Follow these steps to resolve the issue:
1. As a first step, it might be a good idea to explore the repo to familiarize yourself with its structure.
2. Create a script to reproduce the error and execute it with `python <filename.py>` using the BashTool, to confirm the error
3. Edit the sourcecode of the repo to resolve the issue
4. Rerun your reproduce script and confirm that the error is fixed!
5. Think about edgecases and make sure your fix handles them as well

Your thinking should be thorough and so it's fine if it's very long.

You have access to a scratchpad tool. Here are some guidelines for using it:
- Use the scratchpad tool to jot down your thoughts, plans, and progress throughout your work. This is especially important for:
    - Breaking down complex tasks into steps
    - Tracking your progress and what you've learned
    - Documenting your understanding of the codebase
    - Recording important commands or test procedures
    - Noting issues you encounter and how you resolved them
    - Keeping track of ideas for future improvements
- Before implementing your solution, first summarize your plan in the scratchpad.
- The scratchpad persists across turns, so you can refer back to your notes as you work.
- Make it a habit to update the scratchpad regularly as you make progress. This will help you stay organized and focused.
- If you notice yourself going around in circles, or going down a rabbit hole, review your scratchpad and try something different.
"""

{"cells": [{"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from datasets import Dataset, load_dataset\n", "from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import (\n", "    get_dataset_name,\n", "    issue_to_agent,\n", ")\n", "from collections import defaultdict\n", "\n", "dataset_name = get_dataset_name(\"verified\")\n", "dataset: Dataset = load_dataset(dataset_name, split=\"test\")  # type: ignore\n", "\n", "samples = [\n", "    issue_to_agent(example)\n", "    for example in dataset  # type: ignore\n", "]\n", "\n", "repo_groups = defaultdict(list)\n", "for example in samples:\n", "    repo_name = example[\"repo\"]  # adjust key if repo name is stored differently\n", "    repo_groups[repo_name].append(example)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["astropy/astropy: 22\n", "django/django: 231\n", "matplotlib/matplotlib: 34\n", "mwaskom/seaborn: 2\n", "pallets/flask: 1\n", "psf/requests: 8\n", "pydata/xarray: 22\n", "pylint-dev/pylint: 10\n", "pytest-dev/pytest: 19\n", "scikit-learn/scikit-learn: 32\n", "sphinx-doc/sphinx: 44\n", "sympy/sympy: 75\n"]}], "source": ["for repo, samples in repo_groups.items():\n", "    print(f\"{repo}: {len(samples)}\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# Break into chunks of ~50 examples while keeping repos together\n", "target_chunk_size = 50\n", "chunks = []\n", "current_chunk = []\n", "\n", "for repo, repo_examples in repo_groups.items():\n", "    while len(repo_examples) > 0:\n", "        assert len(current_chunk) <= target_chunk_size, \"Current chunk is too large\"\n", "        remaining_space = target_chunk_size - len(current_chunk)\n", "        current_chunk.extend(repo_examples[:remaining_space])\n", "        repo_examples = repo_examples[remaining_space:]\n", "        if len(current_chunk) >= target_chunk_size:\n", "            chunks.append(current_chunk)\n", "            current_chunk = []\n", "\n", "# Add the last chunk if it has any examples\n", "if len(current_chunk) > 0:\n", "    chunks.append(current_chunk)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created 10 chunks\n", "Chunk 0: 50 examples from 2 repos\n", "Chunk 1: 50 examples from 1 repos\n", "Chunk 2: 50 examples from 1 repos\n", "Chunk 3: 50 examples from 1 repos\n", "Chunk 4: 50 examples from 1 repos\n", "Chunk 5: 50 examples from 6 repos\n", "Chunk 6: 50 examples from 4 repos\n", "Chunk 7: 50 examples from 2 repos\n", "Chunk 8: 50 examples from 2 repos\n", "Chunk 9: 50 examples from 1 repos\n"]}], "source": ["print(f\"Created {len(chunks)} chunks\")\n", "for i, chunk in enumerate(chunks):\n", "    print(\n", "        f\"Chunk {i}: {len(chunk)} examples from {len(set(ex['repo'] for ex in chunk))} repos\"\n", "    )"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# save chunks as a compressed pickle\n", "from compress_pickle import dump, loads\n", "\n", "output_path = \"/mnt/efs/augment/data/swebench_processed_eval/v1.pickle.gz\"\n", "with open(output_path, \"wb\") as f:\n", "    dump(chunks, f, compression=\"gzip\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
PROMPT = """
<uploaded_files>
{location}
</uploaded_files>
I've uploaded a python code repository in the directory {location} (not in /tmp/inputs).

<pr_description>
{pr_description}
</pr_description>

I have a candidate solution that addresses the <pr_description>, but I need to make sure it doesn't break any existing tests.
Run all tests to see if any fail. If a test fails, alert me with the "complete_filter_step" tool by setting "found_failing_test" to true.


Follow these steps to resolve the issue:
1. Figure out how to run all tests in the repository. Spend some time analyzing how to run the tests and look for whether a custom harness might be needed. Not all repos support "pytest". Use the sequential_thinking tool to help you figure out how to run all the tests.
2. Run all tests.
3. If a failing test is found, call the "found_failing_test" tool with "found_failing_test" set to true to alert me to it. If no failing tests are found, call the "found_failing_test" tool with "found_failing_test" set to false.

GUIDE FOR HOW TO USE "sequential_thinking" TOOL:
- Your thinking should be thorough and so it's fine if it's very long. Set totalThoughts to at least 5, but setting it up to 25 is fine as well. You'll need more total thoughts when you are considering multiple possible solutions or root causes for an issue.
- Use this tool as much as you find necessary to improve the quality of your answers.
- You can run bash commands (like tests, a reproduction script, or 'grep'/'find' to find relevant context) in between thoughts.
- The sequential_thinking tool can help you break down complex problems, analyze issues step-by-step, and ensure a thorough approach to problem-solving.
- Don't hesitate to use it multiple times throughout your thought process to enhance the depth and accuracy of your solutions.

TIPS:
- Do NOT make tool calls inside thoughts passed to sequential_thinking tool. For example, do NOT do this: {{'thought': 'I need to look at the actual implementation of `apps.get_models()` in this version of Django to see if there\'s a bug. Let me check the Django apps module:\n\n<function_calls>\n<invoke name="str_replace_editor">\n<parameter name="command">view</parameter>\n<parameter name="path">django/apps/registry.py</parameter></invoke>', 'path': 'django/apps/registry.py'}}
- Do NOT run "git init" or "git commit" or "git add". Do NOT commit your changes.
- Respect the tool specifications. If a field is required, make sure to provide a value for it. For example "thoughtNumber" is required by the sequential_thinking tool.
- When you run "ls" or variants of it, you may see a symlink like "fileA -> /data/vol/fileA". You can safely ignore the symlink and just use "fileA" as the path when read, editing, or executing the file.
- When you need to find information about the codebase, use "grep" and "find" to search for relevant files and code with the bash tool
- Use your bash tool to set up any necessary environment variables, such as those needed to run tests.
"""

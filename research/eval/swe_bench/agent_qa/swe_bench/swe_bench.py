#!/usr/bin/env python3

import argparse
import pickle
import docker
import json
import logging
import os
import requests
import subprocess
import shutil
import sys
import time
from dataclasses import dataclass
from functools import partial

from typing import Any, Dict, List, Set, Tuple

from datasets import Dataset, load_dataset

from datetime import datetime
from multiprocessing import Pool, Manager, Semaphore
from pathlib import Path
from research.eval.swe_bench.agent_qa.swe_bench import (
    instruction_prompt_scratchpad,
    instruction_prompt_sequential_thinking,
    instruction_prompt_sequential_thinking_retrieval,
    instruction_prompt_sequential_thinking_ensemble,
    instruction_prompt_sequential_thinking_orientation,
    instruction_fix_regression,
    instruction_filter_regression,
)

AUGMENT_ROOT = Path(__file__).parent.parent.parent.parent.parent.parent
NAMESPACE = "us-central1-docker.pkg.dev/augment-research-gsc/docker-us-central1"
SWEBENCH_SHORT_REF = "b10f7a5"

MAX_DOCKER_CONCURRENCY = 4

# See https://github.com/swe-bench/SWE-bench/issues/228
REVERSE_PATCH_REPOS = [
    "sphinx-doc/sphinx",
]

RC_TIMEOUT = 2
RC_ERROR = 3
RC_READ_TIMEOUT = 4

instructions_base: str = """
The issue you need to solve is:
{issue}

Your process for solving this issue is:
- the repository is checked out for you into your workdir
- the repository is set to the base commit
- do not check out any more code
- edit the codebase to solve the issue
- verify the solution by running the tests to see if they pass.
- All existing tests should pass.

"""


def generate_patch(git_repo, reverse=False):
    """Generate the patch for the prediction."""
    logging.info(f"Generating patch in {git_repo}")
    cmd = [
        "git",
        "--no-pager",
        "diff",
        "-U5",  # Include 5 lines of context
        "--no-color",  # Don't include color codes in the output
        "HEAD",  # Compare against the current commit
    ]
    if reverse:
        cmd.append("-R")
    max_retries = 3
    for attempt in range(max_retries):
        try:
            diff = subprocess.check_output(
                cmd,
                cwd=git_repo,
                text=True,
                errors="backslashreplace",
            )
            return diff
        except Exception as e:
            if attempt < max_retries - 1:
                logging.warning(
                    f"Error {e} occurred. Retrying... (Attempt {attempt + 1}/{max_retries})"
                )
                time.sleep(5)  # Add a small delay before retrying
            else:
                logging.error(
                    f"Failed to decode git diff output after {max_retries} attempts."
                )
                raise


@dataclass
class AgentInput:
    input: dict
    eager_eval: bool
    use_direct_client: bool


@dataclass
class AgentOutput:
    return_code: int


def generate_prediction(cwd: Path) -> str | None:
    """Generate a prediction for a single issue."""

    issue = json.loads((cwd / "issue.json").read_text())
    predictions_file = cwd / "predictions.json"
    prediction = {
        "instance_id": issue["instance_id"],
        "model_name_or_path": "Augment_Agent",
        "model_patch": "",
    }

    prediction["model_patch"] = generate_patch(
        cwd / issue["instance_id"], reverse=issue["repo"] in REVERSE_PATCH_REPOS
    )
    predictions_file.write_text(json.dumps([prediction], indent=2))
    return prediction["model_patch"]


def issues_for_repo(
    full_set: Dataset, repo: str, limit_per_repo: int | None = None
) -> Dataset:
    """Filter dataset to get issues for a specific repository."""
    out = full_set.filter(lambda x: x["repo"] == repo)
    if limit_per_repo is None:
        return out
    return out.select(range(limit_per_repo))


def issue_to_agent(issue: Dict[str, Any]) -> Dict[str, Any]:
    """Convert a SWE-bench issue to the agent-compatible format."""
    return {
        "repo": issue["repo"],
        "instance_id": issue["instance_id"],
        "base_commit": issue["base_commit"],
        "problem_statement": issue["problem_statement"],
    }


def sample_one_repo(
    full_set: Dataset, repo: str, limit_per_repo: int | None = None
) -> List[Dict[str, Any]]:
    """Get a random sample of issues from a specific repository."""
    issues = issues_for_repo(full_set, repo)
    limit_per_repo = (
        min(limit_per_repo, len(issues)) if limit_per_repo is not None else len(issues)
    )
    return [
        issue_to_agent(issue)  # type: ignore
        for issue in issues.select(range(limit_per_repo))
    ]


def seconds_to_human_readable(total_seconds: float) -> str:
    hours, remainder = divmod(total_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{int(hours)} hours {int(minutes)} mins {int(seconds)} seconds"


def sample_repos(
    full_set: Dataset, limit_per_repo: int | None = None
) -> List[Dict[str, Any]]:
    """Get a random sample of issues from all repositories."""
    # Get unique repositories from the dataset and sample issues
    swebench_repos: Set[str] = set([issue["repo"] for issue in full_set])  # type: ignore
    samples: List[Dict[str, Any]] = []
    for repo in swebench_repos:
        rs = sample_one_repo(full_set, repo, limit_per_repo)
        samples.extend(rs)
    return samples


def get_issue_image_name(issue: Dict[str, Any], workspace: Path) -> str:
    """Fetch a docker image for the issue."""
    issue_key = issue["instance_id"].replace("__", "_1776_")
    return f"{NAMESPACE}/sweb.eval.x86_64.{issue_key}:{os.environ.get('SWEBENCH_SHORT_REF', SWEBENCH_SHORT_REF)}"


def set_volume_permissions(container_id, volume_path: Path):
    # Make sure we can read the volume
    # Docker is running as root, we may be running as augment.
    my_uid = os.getuid()
    my_gid = os.getgid()
    logging.info(f"Fixing permissions for {volume_path} to {my_uid}:{my_gid}")
    env = os.environ.copy()
    try:
        subprocess.check_call(
            [
                "sudo",
                "chmod",
                "a+rx",
            ]
            + [p.as_posix() for p in volume_path.parents],
            env=env,
        )
    except subprocess.CalledProcessError as e:
        logging.warning(f"Failed to chmod {volume_path}: {e}")
        raise
    # Change the owner to the current user
    try:
        container_out = subprocess.check_output(
            [
                "sudo",
                "chown",
                "-R",
                f"{my_uid}:{my_gid}",
                volume_path.as_posix(),
            ],
            env=env,
            text=True,
            errors="backslashreplace",
        )
        logging.debug(container_out)
    except subprocess.CalledProcessError as e:
        logging.warning(f"Failed to chown {volume_path}: {e}")
        raise


def start_container(workspace: Path, issue: Dict[str, Any], semaphore: Any) -> str:
    """Start a docker container for the issue."""
    stop_container(f"sweb.augment.{issue['instance_id']}")
    image_name = get_issue_image_name(issue, workspace)
    logging.info(f"Starting container for {issue['instance_id']}")
    client = docker.from_env()
    logging.info(f"Pulling image {image_name}")
    with semaphore:
        logging.info(f"Starting pull for {image_name}")
        client.images.pull(image_name)
        logging.info(f"Finished pulling image {image_name}")
    logging.info(f"Running docker run for {image_name} in {workspace}")

    with semaphore:
        logging.info(f"Starting run for {image_name}")
        container = client.containers.run(
            name=f"sweb.augment.{issue['instance_id']}",
            image=image_name,
            detach=True,
            volumes=["/testbed"],
            command="bash -c 'git config --global user.email a && git config --global user.name a && git commit --allow-empty -am augment && sleep 7200'",  # Time out and die, eventually, if we are interrupted
        )
        logging.info(f"Finished startup for {image_name}")
    # Give it a second to start
    time.sleep(10)
    container_id = container.id
    assert container_id is not None
    logging.info(f"Started {container_id} for {issue['instance_id']}")
    # Get the volume path from the container
    volume_info = container.attrs["Mounts"]
    volume_path = Path(volume_info[0]["Source"])
    (workspace / issue["instance_id"]).unlink(missing_ok=True)
    (workspace / issue["instance_id"]).symlink_to(volume_path)

    retry = True
    while True:
        try:
            set_volume_permissions(container_id, volume_path)
            break
        except Exception as e:
            logging.warning(f"Failed to set permissions: {e}")
            if not retry:
                # Don't fail for this, it may not be fatal.
                break
            retry = False
            time.sleep(10)
    return container_id


def remove_container_image(image_name: str) -> None:
    """Remove a docker image."""
    try:
        client = docker.from_env()
        client.images.remove(image=image_name, force=True)
        logging.info(f"Removed image {image_name}")
    except docker.errors.APIError as e:  # type: ignore
        logging.warning(f"Failed to remove image {image_name}: {e}")


def stop_container(container_id: str, remove_image: str = "") -> None:
    """Stop a docker container for the issue."""
    container = None
    try:
        client = docker.from_env()
        container = client.containers.get(container_id)
    except Exception as e:
        logging.info(f"Container {container_id} not found: {e}")

    if container:
        try:
            logging.info(f"Stopping container {container_id}")
            container.stop()
            logging.info(f"Stopped container {container_id}")
        except docker.errors.NotFound as e:  # type: ignore
            logging.warning(f"Failed to stop container {container_id}: {e}")
        except docker.errors.APIError as e:  # type: ignore
            logging.warning(f"Failed to stop container {container_id}: {e}")
        try:
            logging.info(f"Removing container {container_id}")
            container.remove()
            time.sleep(10)
            logging.info(f"Removed container {container_id}")
        except docker.errors.NotFound as e:  # type: ignore
            logging.warning(f"Failed to stop container {container_id}: {e}")
        except docker.errors.APIError as e:  # type: ignore
            logging.warning(f"Failed to stop container {container_id}: {e}")

    if remove_image:
        # Add a small delay to ensure container removal is complete
        time.sleep(5)
        remove_container_image(remove_image)


def setup_workspace(
    workspace: Path, issue: Dict[str, Any], lock: Any, semaphore: Any
) -> Tuple[Dict[str, str], str]:
    """Setup the workspace for the agent."""
    env: Dict[str, str] = os.environ.copy()

    # Create a conda environment; we don't use it, but it protects the
    # agent's environment from changes.
    logging.debug(f"Creating conda enviroment in {workspace}")
    workspace.mkdir(parents=True, exist_ok=True)
    # Multiple simultaneous conda installs are no good.
    with lock:
        subprocess.check_output(
            [
                "conda",
                "create",
                "-y",
                "-q",
                "-p",
                str(workspace / "conda_3.9"),
                "python==3.9",
            ]
        )

    env["ISSUE_ID"] = issue["instance_id"]
    env["SWEBENCH_WORKSPACE"] = str(workspace)
    env["PATH"] = f"{workspace}/python_wrappers/bin:{workspace}/conda_3.9/bin" + (
        f":{env['PATH']}" if "PATH" in env else ""
    )
    env["PYTHONPATH"] = f"{AUGMENT_ROOT}" + (
        f":{env['PYTHONPATH']}" if "PYTHONPATH" in env else ""
    )
    for k, v in env.items():
        logging.debug(f"ENV {k}=={v}")

    # Copy the python wrapper into the workspace
    container_id = start_container(workspace, issue, semaphore)
    python_bin = workspace / "python_wrappers/bin"
    python_bin.mkdir(parents=True, exist_ok=True)
    (python_bin / "python_wrapper.sh").write_text(
        Path(__file__)
        .parent.joinpath("python_wrapper.sh")
        .read_text()
        .replace("@@CONTAINER_ID@@", container_id)
    )
    (python_bin / "python_wrapper.sh").chmod(0o755)

    # Create symlinks for python, python3, pip, pip3
    for cmd in ["python", "python3", "pip", "pip3", "pytest"]:
        (python_bin / cmd).unlink(missing_ok=True)
        (python_bin / cmd).symlink_to("python_wrapper.sh")

    # Copy the issue into the workspace.
    (workspace / "issue.json").write_text(json.dumps(issue, indent=2))

    return env, container_id


def get_instruction(
    add_scratchpad: bool,
    add_sequential_thinking: bool,
    add_retrieval: bool,
    issue: Dict[str, Any],
    instruction_workspace: str,
    orientation_stage: bool = False,
    candidate_for_refinement_path: str | None = None,
    ensemble_candidates_path: str | None = None,
    candidate_for_filtering_path: str | None = None,
) -> str:
    """Get the instruction for the agent."""
    del add_scratchpad  # Not used
    assert add_sequential_thinking, "Only sequential thinking is implemented"
    assert not orientation_stage, "Not implemented"
    instance_id = issue["instance_id"]

    if candidate_for_refinement_path is not None:
        assert not ensemble_candidates_path, ensemble_candidates_path
        assert not add_retrieval

        return instruction_fix_regression.PROMPT.format(
            location=instruction_workspace,
            pr_description=issue["problem_statement"],
        )

    if candidate_for_filtering_path is not None:
        assert not ensemble_candidates_path, ensemble_candidates_path
        assert not add_retrieval

        return instruction_filter_regression.PROMPT.format(
            location=instruction_workspace,
            pr_description=issue["problem_statement"],
        )

    if ensemble_candidates_path is not None:
        with open(ensemble_candidates_path, "rb") as f:
            ensemble_candidates = pickle.load(f)
        assert not add_retrieval
        candidate_diffs = []
        for candidate_dct in ensemble_candidates[instance_id].values():
            candidate_diffs.append(candidate_dct["patch_diff"])
        assert (
            len(candidate_diffs) > 2
        ), f"Too few candidates for {instance_id}: {len(candidate_diffs)}"

        candidates_str = ""
        for i, candidate in enumerate(candidate_diffs):
            candidates_str += f"""
<candidate_solution index={i+1}>
{candidate}
</candidate_solution index={i+1}>

"""
        return instruction_prompt_sequential_thinking_ensemble.PROMPT.format(
            location=instruction_workspace,
            pr_description=issue["problem_statement"],
            candidate_diffs=candidates_str,
        )

    if add_retrieval:
        print("Using retrieval instruction")
        return instruction_prompt_sequential_thinking_retrieval.PROMPT.format(
            location=instruction_workspace,
            pr_description=issue["problem_statement"],
        )

    return instruction_prompt_sequential_thinking.PROMPT.format(
        location=instruction_workspace,
        pr_description=issue["problem_statement"],
        step7_str="Run select tests from the repo to make sure that your fix doesn't break anything else.",
    )


def build_agent_cmd(
    workspace: Path,
    issue: Dict[str, Any],
    instruction: str,
    log_dir: Path,
    use_direct_client: bool,
    modal: bool,
    use_low_qos_server: bool,
    swebench_sparse_system_prompt: bool,
    use_container_workspace: bool,
    auth_token_file: Path,
    container_id: str,
    add_scratchpad: bool,
    add_sequential_thinking: bool,
    add_retrieval: bool,
    orientation_instruction: str | None = None,
    thinking_tokens: int = 0,
    candidate_for_filtering_path: str | None = None,
) -> List[str]:
    """Build the command to run the agent."""
    (workspace / "instructions.txt").write_text(instruction)

    agent_cmd: List[str] = [
        sys.executable,
        "-W",
        "ignore",
        f"{AUGMENT_ROOT}/experimental/guy/agent_qa/interactive_agent.py",
        "--knowledge-path",
        f"{AUGMENT_ROOT}/research/eval/swe_bench/agent_qa/swe_bench/knowledge",
        "--workspace-root",
        (workspace / issue["instance_id"]).as_posix(),
        "--no-integration-warnings",
        "--instruction-file",
        (workspace / "instructions.txt").as_posix(),
        "--approve-command-execution",
        "--log-file",
        f"{log_dir}/agent_log.txt",
        "--pickle-log-file",
        f"{log_dir}/agent_log.pickle",
        "--prompt-budgeting-log-file",
        f"{log_dir}/prompt_budgeting_stats.txt",
        "-q",
        "--max-retries",
        "50",
        "--max-turns",
        "500",
        "--remove-env-var",
        "PYTHON_PATH",
        "--auth-token-file",
        auth_token_file.as_posix(),
        "--use-prompt-budgeting",
        "--swebench-mode",
        "--use-direct-str-replace-tool",
        "--retry-on-failure",
    ]

    if orientation_instruction is not None:
        (workspace / "orientation_instruction.txt").write_text(orientation_instruction)
        agent_cmd.extend(
            [
                "--orientation-instruction-file",
                (workspace / "orientation_instruction.txt").as_posix(),
            ]
        )

    if thinking_tokens > 0:
        agent_cmd.extend(["--thinking-tokens", str(thinking_tokens)])

    # specify tools
    tool_names = [
        "str_replace_editor",
        # "read_file",
        # "save_file",
        # "read_file_outline",
        # "launch_process",
        # "read_process",
        # "kill_process",
        # "write_process",
        # "list_processes",
        # "request_codebase_information",
    ]
    if candidate_for_filtering_path is not None:
        agent_cmd.extend(["--enable-regression-filtering"])
        tool_names = []  # Disable file editing tool if we just want to filter examples

    for tool_name in tool_names:
        logging.warning(f"Using tool {tool_name}")
        agent_cmd.extend(["--specify-tool", tool_name])
    agent_cmd.append("--enable-bash-tool")
    if use_container_workspace:
        agent_cmd.extend(["--docker-container-id", container_id])
        agent_cmd.extend(["--use-container-workspace", "/testbed"])
    logging.warning("Using bash tool")
    if add_sequential_thinking:
        agent_cmd.extend(["--specify-tool", "sequential_thinking"])
        logging.warning("Using sequential thinking")
    if add_scratchpad:
        agent_cmd.extend(["--specify-tool", "scratchpad"])
        logging.warning("Using scratchpad")
    if add_retrieval:
        agent_cmd.extend(["--specify-tool", "request_codebase_information"])
        logging.warning("Using codebase retrieval")

    if use_low_qos_server:
        agent_cmd.append("--use-anthropic-direct")
        logging.warning("Using direct client")
        agent_cmd.append("--use-low-qos-server")
        logging.warning("Using low QoS server")
    else:
        if use_direct_client:
            agent_cmd.append("--use-anthropic-direct")
            logging.warning("Using direct client")
        else:
            logging.warning("Using vertex client")

    if modal:
        logging.warning("Using modal")
        agent_cmd.append("--modal")
    if swebench_sparse_system_prompt:
        agent_cmd.append("--swebench-sparse-system-prompt")

    return agent_cmd


def run_one(
    issue: Dict[str, Any],
    use_direct_client: bool,
    workdir: Path,
    logdir: Path,
    agent_timeout: int,
    lock: Any,
    semaphore: Any,
    auth_token_file: Path,
    evaluate: bool = False,
    dataset: str = "verified",
    remove_image: bool = False,
    modal: bool = False,
    add_sequential_thinking: bool = True,
    add_scratchpad: bool = False,
    add_retrieval: bool = False,
    use_low_qos_server: bool = False,
    swebench_sparse_system_prompt: bool = False,
    use_container_workspace: bool = False,
    orientation_stage: bool = False,
    thinking_tokens: int = 0,
    interactive: bool = False,
    candidate_for_refinement_path: str | None = None,
    ensemble_candidates_path: str | None = None,
    candidate_for_filtering_path: str | None = None,
) -> Tuple[str, int]:
    """Run the agent on a single issue."""
    st = datetime.now()
    workspace = workdir.absolute() / f"workspace_{issue['instance_id']}"
    log_dir = logdir.absolute() / f"{issue['instance_id']}"
    container_id = None
    image_name = get_issue_image_name(issue, workspace)

    # FIXME (c-flaherty): uncomment below if you want to revert testbed functionality
    # use_container_workspace = False

    instruction_workspace = (
        "/testbed" if use_container_workspace else workspace.as_posix()
    )
    instruction = get_instruction(
        add_scratchpad=add_scratchpad,
        add_sequential_thinking=add_sequential_thinking,
        add_retrieval=add_retrieval,
        issue=issue,
        instruction_workspace=instruction_workspace,
        orientation_stage=orientation_stage,
        candidate_for_refinement_path=candidate_for_refinement_path,
        ensemble_candidates_path=ensemble_candidates_path,
        candidate_for_filtering_path=candidate_for_filtering_path,
    )

    agent_returncode = 0
    stdout = stderr = ""
    retryable = [RC_ERROR, RC_READ_TIMEOUT, RC_TIMEOUT]
    current_try = 0
    max_tries = 3
    agent_duration = None
    while True:
        try:
            if current_try >= max_tries:
                break
            if log_dir.exists():
                log_dir.rename(log_dir.parent / f"{log_dir.name}_retry_{current_try}")

            env, container_id = setup_workspace(workspace, issue, lock, semaphore)

            if orientation_stage:
                orientation_instruction = (
                    instruction_prompt_sequential_thinking_orientation.PROMPT.format(
                        location=instruction_workspace,
                        pr_description=issue["problem_statement"],
                    )
                )
            else:
                orientation_instruction = None

            agent_cmd: List[str] = build_agent_cmd(
                workspace=workspace,
                issue=issue,
                instruction=instruction,
                log_dir=log_dir,
                use_direct_client=use_direct_client,
                modal=modal,
                use_low_qos_server=use_low_qos_server,
                swebench_sparse_system_prompt=swebench_sparse_system_prompt,
                use_container_workspace=use_container_workspace,
                auth_token_file=auth_token_file,
                container_id=container_id,
                add_scratchpad=add_scratchpad,
                add_sequential_thinking=add_sequential_thinking,
                add_retrieval=add_retrieval,
                orientation_instruction=orientation_instruction,
                thinking_tokens=thinking_tokens,
                candidate_for_filtering_path=candidate_for_filtering_path,
            )

            if interactive:
                print(f"cd {workspace} && {' '.join(agent_cmd)}")
                return (issue["instance_id"], 0)
            logging.warning(f"Running {' '.join(agent_cmd)} in {workspace}")

            if (
                candidate_for_refinement_path is not None
                or candidate_for_filtering_path is not None
            ):
                print("Applying candidate patch")

                def apply_patch(workspace, patch):
                    patch_path = os.path.join(workspace.parent, "candidate_patch.diff")
                    with open(patch_path, "w") as f:
                        f.write(patch)
                    subprocess.run(
                        ["git", "apply", patch_path],
                        cwd=workspace / issue["instance_id"],
                        check=True,
                    )

                target_path = (
                    candidate_for_refinement_path
                    if candidate_for_refinement_path is not None
                    else candidate_for_filtering_path
                )
                assert target_path is not None
                with open(target_path, "rb") as f:
                    candidate_for_refinement = pickle.load(f)
                candidate_patch = candidate_for_refinement.get(
                    issue["instance_id"], None
                )
                if candidate_patch is not None:
                    apply_patch(workspace, candidate_patch)

            start_time = time.time()
            agent: subprocess.CompletedProcess = subprocess.run(
                agent_cmd,
                env=env,
                cwd=workspace,
                capture_output=True,
                timeout=agent_timeout,
            )
            end_time = time.time()
            agent_duration = end_time - start_time
            stdout = agent.stdout.decode()
            stderr = agent.stderr.decode()
            agent_returncode = agent.returncode
        except subprocess.TimeoutExpired as e:
            td = datetime.now() - st
            logging.warning(
                f"Agent run for {issue['instance_id']} timed out after {td}"
            )
            stdout = e.stdout.decode() if e.stdout else ""
            stderr = e.stderr.decode() if e.stderr else ""
            agent_returncode = RC_TIMEOUT
        except requests.exceptions.ReadTimeout as e:
            td = datetime.now() - st
            logging.warning(
                f"Agent run for {issue['instance_id']} timed out (read timeout) after {td}"
            )
            stderr = str(e)
            agent_returncode = RC_READ_TIMEOUT
        except subprocess.CalledProcessError as e:
            td = datetime.now() - st
            logging.warning(
                f"Agent run for {issue['instance_id']} failed (rc: {e.returncode}) in {td}"
            )
            stdout = e.stdout.decode() if e.stdout else ""
            stderr = e.stderr.decode() if e.stderr else ""
            agent_returncode = RC_ERROR
        except OSError as e:
            td = datetime.now() - st
            logging.warning(
                f"Agent run for {issue['instance_id']} failed (OSError) in {td}: {e}"
            )
            agent_returncode = RC_ERROR

        if agent_returncode in retryable:
            logging.info(f"Retrying agent run for {issue['instance_id']}")
            current_try += 1
            continue

        # Try to generate a prediction
        patch = None
        try:
            patch = generate_prediction(workspace)
        except subprocess.CalledProcessError as e:
            logging.warning(f"Failed to generate prediction for {issue['instance_id']}")
            if e.stdout:
                Path(workspace, "prediction_stdout.txt").write_text(e.stdout.decode())
            if e.stderr:
                Path(workspace, "prediction_stderr.txt").write_text(e.stderr.decode())
            evaluate = False
        except UnicodeDecodeError as e:
            Path(workspace, "prediction_stderr.txt").write_text(
                f"Failed to generate patch: {e}"
            )
            logging.warning(f"Failed to generate patch: {e}")
            evaluate = False
        except FileNotFoundError as e:
            logging.warning(
                f"Failed to find prediction for {issue['instance_id']}; no issue.json file found"
            )
            logging.warning(e)
            evaluate = False

        if patch:
            break
        current_try += 1
        logging.warning(f"Empty patch for {issue['instance_id']}, rerunning agent")

    td = datetime.now() - st
    logging.info(
        f"Agent run for {issue['instance_id']} completed with return code: {agent_returncode} in {td}"
    )
    if stdout:
        Path(workspace, "agent_stdout.txt").write_text(stdout)
    if stderr:
        Path(workspace, "agent_stderr.txt").write_text(stderr)

    if container_id is not None:
        stop_container(container_id)

    # Log remotely then copy locally in case of catastrophic failure
    if (log_dir / "agent_log.txt").exists():
        shutil.copy(log_dir / "agent_log.txt", workspace / "agent_log.txt")
    if (log_dir / "agent_log.pickle").exists():
        shutil.copy(log_dir / "agent_log.pickle", workspace / "agent_log.pickle")
    if (log_dir / "prompt_budgeting_stats.txt").exists():
        shutil.copy(
            log_dir / "prompt_budgeting_stats.txt",
            workspace / "prompt_budgeting_stats.txt",
        )

    run_id = workdir.name
    if evaluate:
        logging.info(f"Running evaluation for {issue['instance_id']}")
        try:
            run_evaluation(
                predictions_file=workspace / "predictions.json",
                dataset=get_dataset_name(
                    "full"
                ),  # Always use the full dataset for evaluation.
                run_id=run_id,
                num_processes=1,
                retry_docker_startup=True,
            )
            eval_file = workspace / f"Augment_Agent.{run_id}.json"
            result_report(eval_file)
        except FileNotFoundError as exc:
            logging.warning(f"Failed to report results for {issue['instance_id']}")
            logging.warning(exc)

    if remove_image:
        remove_container_image(image_name)

    (workspace / "agent_rc.txt").write_text(str(agent_returncode))
    (workspace / "agent_duration.txt").write_text(
        str(
            seconds_to_human_readable(agent_duration)
            if agent_duration is not None
            else "None"
        )
    )

    return (issue["instance_id"], agent_returncode)


def consolidate_predictions(base_dir: Path) -> List[Any]:
    # Consolidate all patches into a single predictions file
    all_predictions: List[Any] = []
    prediction_files = base_dir.glob("*/predictions.json")
    for predictions_file in prediction_files:
        prediction = json.loads(predictions_file.read_text())[0]
        all_predictions.append(prediction)
        logging.info(f"Added prediction for {prediction['instance_id']}")
    return all_predictions


def result_report(results_file: Path):
    ds_total = 500
    results = json.loads(results_file.read_text())
    print(f"Dataset total\t{ds_total}\t% total")
    for itype in (
        "submitted",
        "completed",
        "resolved",
        "unresolved",
        "error",
        "empty_patch",
    ):
        print(
            f"{itype.capitalize()}\t{results[f'{itype}_instances']}\t{results[f'{itype}_instances']/ds_total*100:.1f}"
        )

    srp = (
        results["resolved_instances"] / results["submitted_instances"] * 100
        if results["submitted_instances"] > 0
        else 0
    )
    print(f"Submitted resolve pct\t{srp:.2f}")
    crp = (
        results["resolved_instances"] / results["completed_instances"] * 100
        if results["completed_instances"] > 0
        else 0
    )
    print(f"Completed resolve pct\t{crp:.2f}")


def run_evaluation(
    predictions_file: Path,
    dataset: str,
    run_id: str,
    num_processes: int = 4,
    docker_host: str | None = None,
    capture_output: bool = False,
    swebench_venv_path: Path | None = None,
    retry_docker_startup: bool = False,
):
    """Run the SWE-bench evaluation on the predictions file."""
    et = datetime.now()
    env = os.environ.copy()
    if docker_host is not None:
        env["DOCKER_HOST"] = docker_host
    instance_id = json.loads(predictions_file.read_text())[0].get("instance_id")
    report_dir = predictions_file.parent
    if swebench_venv_path is None:
        swebench_venv_path = (
            predictions_file.parent.parent.parent / "swebench_env" / "bin" / "python"
        )
    cmd = [
        swebench_venv_path.as_posix(),
        "-m",
        "swebench.harness.run_evaluation",
        "--dataset_name",
        dataset,
        "--predictions_path",
        predictions_file.name,
        "--run_id",
        run_id,
        "--max_workers",
        str(num_processes),
        "--report_dir",
        report_dir.as_posix(),
        "--cache_level",
        "instance",
        "--namespace",
        NAMESPACE,
        "--instance_image_tag",
        os.environ.get("SWEBENCH_SHORT_REF", SWEBENCH_SHORT_REF),
    ]
    logging.info(f"Running {' '.join(cmd)} in {predictions_file.parent}")
    max_tries = 5 if retry_docker_startup else 1
    retry_backoff = 10  # seconds
    tries = 1
    while True:
        if instance_id is not None:
            stop_container(f"sweb.eval.{instance_id}.swe_work")
        evaluation: subprocess.CompletedProcess = subprocess.run(
            cmd,
            env=env,
            cwd=predictions_file.parent,
            capture_output=capture_output,
        )
        tries += 1
        want_retry = False
        eval_log_dir = predictions_file.parent / "logs"
        if eval_log_dir.exists():
            eval_log_file = list(eval_log_dir.rglob("run_instance.log"))
            if eval_log_file:
                eval_log_file = eval_log_file[0]
                eval_report_file = (
                    predictions_file.parent / f"Augment_Agent.{run_id}.json"
                )
                if not eval_report_file.exists():
                    # Eval ran, but not prediction.  Retry.
                    logging.warning(
                        f"Failed to find report file {eval_report_file} for {instance_id}"
                    )
                    want_retry = True
                if "ERROR - Error creating container" in eval_log_file.read_text():
                    # Docker startup failed.  Retry.
                    logging.warning(
                        f"Failed to run evaluation for {instance_id} (rc {evaluation.returncode})"
                    )
                    want_retry = True
            else:
                logging.warning(
                    f"No eval log for {instance_id} (rc {evaluation.returncode})"
                )
                want_retry = True

        if not want_retry or tries > max_tries:
            break
        else:
            logging.info(f"Retrying evaluation for {instance_id}")
            if eval_log_dir.exists():
                eval_log_dir.rename(
                    eval_log_dir.parent / f"{eval_log_dir.name}_{tries}"
                )
            time.sleep(retry_backoff)
            retry_backoff *= 2
    logging.warning(
        f"Evaluations completed for {predictions_file.name} in {datetime.now() - et} ({(datetime.now() - et).total_seconds():.2f}s) rc {evaluation.returncode}"
    )
    return evaluation


def get_dataset_name(dataset: str) -> str:
    """Get the dataset name for the specified dataset."""
    return {
        "verified": "princeton-nlp/SWE-bench_Verified",
        "full": "princeton-nlp/SWE-bench",
        "lite": "princeton-nlp/SWE-bench_Lite",
    }[dataset]


def get_task_filters(args: argparse.Namespace) -> Tuple[List[str], List[str]]:
    include_filter = []
    exclude_filter = []

    if args.task_file is not None:
        logging.info(f"Loading tasks from {args.task_file}")
        include_filter = args.task_file.read_text().splitlines()
        logging.info(f"Loaded {len(include_filter)} tasks")
    elif args.task_id != "":
        include_filter = args.task_id
    elif args.failures_from is not None:
        logging.info(f"Loading failures from {args.failures_from}")
        include_filter = json.loads(args.failures_from.read_text())["unresolved_ids"]
    if args.exclude_resolved is not None:
        logging.info(f"Loading successful tasks from {args.exclude_resolved}")
        for ef in args.exclude_resolved:
            exclude_filter.extend(json.loads(ef.read_text())["resolved_ids"])
    if args.exclude_submitted is not None:
        logging.info(f"Loading submitted tasks from {args.exclude_submitted}")
        for ef in args.exclude_submitted:
            exclude_filter.extend(json.loads(ef.read_text())["submitted_ids"])
    return include_filter, exclude_filter


def run_bench(args: argparse.Namespace):
    st = datetime.now()

    logging.info("Running SWE-bench samples through the agent")

    dataset = get_dataset_name(args.dataset)
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    # Load the SWE-bench dataset from princeton-nlp
    logging.info(f"Loading dataset {dataset}")
    swebench: Dataset = load_dataset(dataset, split="test")  # type: ignore
    assert swebench is not None
    logging.info(f"Loaded {len(swebench)} samples from {dataset}")

    if args.repo:
        logging.info(f"Running samples from {args.repo}")
        samples = []
        for repo in args.repo:
            samples += [
                issue_to_agent(issue)  # type: ignore
                for issue in issues_for_repo(swebench, repo, args.limit_per_repo)
            ]
    elif args.limit_per_repo is None:
        logging.info("Running all samples")
        samples = [issue_to_agent(issue) for issue in swebench]  # type: ignore
    else:
        # Process each sample through the agent
        samples: List[Dict[str, Any]] = sample_repos(swebench, args.limit_per_repo)

    # Include, then exclude
    include_filter, exclude_filter = get_task_filters(args)
    if include_filter:
        samples = [
            sample for sample in samples if sample["instance_id"] in include_filter
        ]
    if exclude_filter:
        samples = [
            sample for sample in samples if sample["instance_id"] not in exclude_filter
        ]

    samples.sort(key=lambda x: x["instance_id"])

    logging.info(f"Filtered to {len(samples)} samples")

    logging.info(f"Running {len(samples)}")
    logging.info(f"Pool size {args.num_processes}")
    args.workdir.mkdir(parents=True, exist_ok=True)

    user = args.user
    if args.user is None:
        try:
            user = subprocess.check_output(
                [
                    "augi",
                    "whoami",
                ],
                errors="backslashreplace",
                text=True,
            ).strip()
        except subprocess.CalledProcessError:
            logging.warning("Failed to get user name, using 'augment'")
            user = "augment"

    if args.log_base is None:
        log_base = Path(f"/mnt/efs/augment/user/{user}/agent_logs")
    else:
        log_base = args.log_base

    log_dir = log_base / datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    log_dir.mkdir(parents=True, exist_ok=True)
    logging.info(f"Agents logging to {log_dir}")

    with Manager() as manager:
        lock = manager.Lock()
        semaphore = manager.Semaphore(MAX_DOCKER_CONCURRENCY)
        with Pool(processes=args.num_processes) as pool:
            results = pool.starmap(
                partial(
                    run_one,
                    use_direct_client=True,
                    workdir=args.workdir,
                    logdir=log_dir,
                    agent_timeout=args.agent_timeout,
                    lock=lock,
                    semaphore=semaphore,
                    auth_token_file=args.auth_token_file,
                    evaluate=args.evaluate,
                    dataset=args.dataset,
                    remove_image=args.evaluate,
                    add_sequential_thinking=True,
                    use_container_workspace=True,
                    interactive=args.interactive,
                ),
                [(sample,) for sample in samples],
            )

            if args.interactive:
                return

    failure_count = sum(1 for _, rc in results if rc != 0)
    logging.info(
        f"Failed {failure_count} of {len(results)} runs ({failure_count/len(results)*100:.2f}%)"
    )

    all_predictions = consolidate_predictions(args.workdir)
    logging.info(f"Found {len(all_predictions)} predictions")
    logging.info(
        f"Predictions completed in {datetime.now() - st} ({(datetime.now() - st).total_seconds():.2f}s)"
    )
    predictions_file = Path(args.workdir, "consolidated_predictions.json")
    predictions_file.write_text(json.dumps(all_predictions, indent=2))
    logging.info(f"Predictions saved to {predictions_file}")

    if args.evaluate:
        results_files = sorted(list(args.workdir.rglob("Augment_Agent*json")))
        merge_reports(results_files, args.workdir / "Augment_Agent.json")

    logging.info(
        f"Total time: {datetime.now() - st} ({(datetime.now() - st).total_seconds():.2f}s)"
    )


def merge_results(result1, result2):
    result3 = {}
    result3["submitted_ids"] = sorted(
        list(set(result1["submitted_ids"] + result2["submitted_ids"]))
    )
    result3["resolved_ids"] = sorted(
        list(set(result1["resolved_ids"] + result2["resolved_ids"]))
    )
    result3["completed_ids"] = sorted(
        list(set(result1["completed_ids"] + result2["completed_ids"]))
    )

    result3["incomplete_ids"] = sorted(
        list(set(result3["submitted_ids"]) - set(result3["completed_ids"]))
    )
    result3["unresolved_ids"] = sorted(
        list(set(result3["submitted_ids"]) - set(result3["resolved_ids"]))
    )

    result3["empty_patch_ids"] = sorted(
        list(set(result1["empty_patch_ids"]) & set(result2["empty_patch_ids"]))
    )
    result3["error_ids"] = sorted(
        list(set(result1["error_ids"]) & set(result2["error_ids"]))
    )
    result3["empty_patch_ids"] = sorted(
        list(set(result1["empty_patch_ids"]) & set(result2["empty_patch_ids"]))
    )

    result3["submitted_instances"] = len(result3["submitted_ids"])

    result3["completed_instances"] = len(result3["completed_ids"])
    result3["resolved_instances"] = len(result3["resolved_ids"])
    result3["unresolved_instances"] = len(result3["unresolved_ids"])
    result3["error_instances"] = len(result3["error_ids"])
    result3["empty_patch_instances"] = len(result3["empty_patch_ids"])

    return result3


def merge_reports(report_files: List[Path], output_file: Path):
    if len(report_files) < 1:
        logging.error("No result files found, nothing to merge")
        return
    elif len(report_files) == 1:
        merged_results = json.loads(report_files[0].read_text())
    else:
        merged_results = json.loads(report_files[0].read_text())
        idx = 1
        while idx < len(report_files):
            merged_results = merge_results(
                merged_results, json.loads(report_files[idx].read_text())
            )
            idx += 1
    if output_file is not None:
        output_file.write_text(json.dumps(merged_results, indent=2))
        logging.info(f"Merged report saved to {output_file}")
    result_report(output_file)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Run SWE-bench samples through the agent"
    )
    subp = parser.add_subparsers(dest="command")

    run_parser = subp.add_parser("run")
    run_parser.add_argument(
        "--workdir",
        "-w",
        type=Path,
        required=True,
        help="Base working directory for the agents.",
    )

    run_parser.add_argument(
        "--limit-per-repo",
        type=int,
        default=None,
        help="Number of samples per repository, default all.",
    )
    run_parser.add_argument(
        "--num-processes",
        type=int,
        default=4,
        help="Number of processes to use for parallel execution, defaults to 4.",
    )
    run_parser.add_argument(
        "--repo",
        type=str,
        default=[],
        action="append",
        help="Run only samples from this repository.  If not specified, run all repositories.  Can be specified multiple times.",
    )
    run_parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging",
    )
    run_parser.add_argument(
        "--evaluate",
        action="store_true",
        help="Perform the swe-bench evaluation run after the agent run",
    )
    run_parser.add_argument(
        "--user",
        type=str,
        default=None,
        help="User name to use for logging, defaults to augi user",
    )

    run_parser.add_argument(
        "--agent-timeout",
        type=int,
        default=1200,
        help="Timeout for the agent in seconds, defaults to 1200 (20 minutes)",
    )

    run_parser.add_argument(
        "--dataset",
        type=str,
        choices=["verified", "full", "lite"],
        default="verified",
        help="Dataset to use, defaults to verified",
    )

    run_parser.add_argument(
        "--log-base",
        type=Path,
        default=None,
        help="Base directory for agent logs, defaults to '/mnt/efs/augment/user/{user}/agent_logs'",
    )

    run_parser.add_argument(
        "--auth-token-file",
        type=Path,
        default=Path("/home/<USER>/.augment/token"),
        help="The file containing the API token",
    )

    run_parser.add_argument(
        "--run-id",
        type=str,
        help="Run ID to use for evaluation, defaults to the predictions file's parent directory name.",
    )

    run_parser.add_argument(
        "--remove-image",
        action="store_true",
        help="Remove the container image after the agent run",
    )
    run_parser.add_argument(
        "--interactive",
        action="store_true",
        help="Run the agent interactively, don't actually run the agent, just start the container and build the command",
    )

    task_filter = run_parser.add_mutually_exclusive_group()
    task_filter.add_argument(
        "--task-id",
        type=str,
        default="",
        nargs="*",
        help="Run a specific task or tasks",
    )

    task_filter.add_argument(
        "--task-file", type=Path, default=None, help="Run tasks from a file"
    )

    task_filter.add_argument(
        "--failures-from",
        type=Path,
        default=None,
        help="Run failures from an evaluation report file.",
    )

    task_filter.add_argument(
        "--exclude-resolved",
        default=None,
        type=Path,
        action="append",
        help="Exclude successful tasks from an evaluation report file.  Can specify multiple times.",
    )

    task_filter.add_argument(
        "--exclude-submitted",
        default=None,
        type=Path,
        action="append",
        help="Exclude submitted tasks from an evaluation report file.  Can specify multiple times.",
    )

    eval_parser = subp.add_parser("evaluate")
    eval_parser.add_argument(
        "predictions_file",
        type=Path,
        help="Path to the predictions file.",
    )

    eval_parser.add_argument(
        "--dataset",
        type=str,
        choices=["verified", "full", "lite"],
        default="verified",
        help="Dataset to use, defaults to verified",
    )

    eval_parser.add_argument(
        "--docker-host",
        type=str,
        default=None,
        help="Docker host to use for evaluation.  Defaults to localhost.",
    )

    eval_parser.add_argument(
        "--num-processes",
        type=int,
        default=4,
        help="Number of processes to use for parallel execution, defaults to 4.",
    )

    eval_parser.add_argument(
        "--run-id",
        type=str,
        help="Run ID to use for evaluation, defaults to the predictions file's parent directory name.",
    )

    consolidate_parser = subp.add_parser("consolidate")
    consolidate_parser.add_argument(
        "base-dir",
        type=Path,
        help="Base directory containing the predictions.",
    )

    merge_parser = subp.add_parser("merge")
    merge_parser.add_argument(
        "--output-file",
        "-o",
        type=Path,
        help="Output file for the merged results.",
    )
    merge_parser.add_argument(
        "result_file",
        type=Path,
        help="File(s) to merge, minimum 2.",
        nargs="+",
    )

    args = parser.parse_args()

    datestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

    # Log to stdout and a file
    Path("run_logs").mkdir(exist_ok=True)
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(f"run_logs/swe_bench_{datestamp}.log"),
            logging.StreamHandler(),
        ],
    )
    logging.info(f"Logging to run_logs/swe_bench_{datestamp}.log")

    if args.command == "run":
        # TODO marcmac add iterations
        # args, _= run_parser.parse_known_args()
        run_bench(args)
    elif args.command == "evaluate":
        # args, _ = eval_parser.parse_known_args()
        run_id = (
            args.run_id
            if args.run_id is not None
            else args.predictions_file.parent.name
        )
        run_evaluation(
            args.predictions_file,
            get_dataset_name(args.dataset),
            run_id,
            args.num_processes,
            args.docker_host,
        )
        eval_file = args.predictions_file.parent / Path(f"Augment_Agent.{run_id}.json")
        result_report(eval_file)

    elif args.command == "consolidate":
        all_predictions = consolidate_predictions(args.base_dir)
        predictions_file = Path(args.base_dir, "consolidated_predictions.json")
        predictions_file.write_text(json.dumps(all_predictions, indent=2))
        logging.info(f"Predictions saved to {predictions_file}")

    elif args.command == "merge":
        merge_reports(args.result_file, args.output_file)
    else:
        parser.print_help()
        exit(1)

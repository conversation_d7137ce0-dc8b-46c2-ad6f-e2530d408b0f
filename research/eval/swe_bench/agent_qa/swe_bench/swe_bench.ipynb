{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datasets import load_dataset\n", "\n", "swebench = load_dataset(\"princeton-nlp/SWE-bench\", split=\"test\")\n", "print(len(swebench))\n", "swelite = load_dataset(\"princeton-nlp/SWE-bench_Lite\", split=\"test\")\n", "print(len(swelite))\n", "sweverified = load_dataset(\"princeton-nlp/SWE-bench_Verified\", split=\"test\")\n", "print(len(sweverified))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "\n", "\n", "def merge_results(r1, r2):\n", "    r3 = {}\n", "    r3[\"submitted_ids\"] = sorted(list(set(r1[\"submitted_ids\"] + r2[\"submitted_ids\"])))\n", "    r3[\"resolved_ids\"] = sorted(list(set(r1[\"resolved_ids\"] + r2[\"resolved_ids\"])))\n", "    r3[\"completed_ids\"] = sorted(list(set(r1[\"completed_ids\"] + r2[\"completed_ids\"])))\n", "\n", "    r3[\"incomplete_ids\"] = sorted(\n", "        list(set(r3[\"submitted_ids\"]) - set(r3[\"completed_ids\"]))\n", "    )\n", "    r3[\"unresolved_ids\"] = sorted(\n", "        list(set(r3[\"submitted_ids\"]) - set(r3[\"resolved_ids\"]))\n", "    )\n", "\n", "    r3[\"empty_patch_ids\"] = sorted(\n", "        list(set(r1[\"empty_patch_ids\"]) & set(r2[\"empty_patch_ids\"]))\n", "    )\n", "    r3[\"error_ids\"] = sorted(list(set(r1[\"error_ids\"]) & set(r2[\"error_ids\"])))\n", "    r3[\"empty_patch_ids\"] = sorted(\n", "        list(set(r1[\"empty_patch_ids\"]) & set(r2[\"empty_patch_ids\"]))\n", "    )\n", "\n", "    r3[\"submitted_instances\"] = len(r3[\"submitted_ids\"])\n", "\n", "    r3[\"completed_instances\"] = len(r3[\"completed_ids\"])\n", "    r3[\"resolved_instances\"] = len(r3[\"resolved_ids\"])\n", "    r3[\"unresolved_instances\"] = len(r3[\"unresolved_ids\"])\n", "    r3[\"error_instances\"] = len(r3[\"error_ids\"])\n", "    r3[\"empty_patch_instances\"] = len(r3[\"empty_patch_ids\"])\n", "\n", "    return r3\n", "\n", "\n", "def result_type(r1, item):\n", "    for itype in (\"resolved\", \"error\", \"unresolved\", \"completed\", \"submitted\"):\n", "        if item in r1[f\"{itype}_ids\"]:\n", "            return itype\n", "\n", "\n", "def result_report(r1):\n", "    ds_total = 500\n", "    print(f\"Dataset total\\t{ds_total}\\t% total\")\n", "    for itype in (\n", "        \"submitted\",\n", "        \"completed\",\n", "        \"resolved\",\n", "        \"unresolved\",\n", "        \"error\",\n", "        \"empty_patch\",\n", "    ):\n", "        print(\n", "            f\"{itype.capitalize()}\\t{r1[f'{itype}_instances']}\\t{r1[f'{itype}_instances']/ds_total*100:.1f}\"\n", "        )\n", "\n", "    print(\n", "        f\"Submitted resolve pct\\t{r1['resolved_instances']/r1['submitted_instances']*100:.2f}\"\n", "    )\n", "    print(\n", "        f\"Completed resolve pct\\t{r1['resolved_instances']/r1['completed_instances']*100:.2f}\"\n", "    )\n", "\n", "\n", "base = Path(\"/opt/swebench/swebench\")\n", "first_run = json.loads(\n", "    Path(\n", "        \"/mnt/efs/augment/user/marcmac/agent/swe_bench/evaluation_results/full_run_2025-01-23/Augment_Agent.full_run_2025-01-23_02-30-28.json\"\n", "    ).read_text()\n", ")\n", "retry = json.loads(\n", "    (\n", "        base\n", "        / \"full_run_2025-01-23_02-30-28_full_retry_1/Augment_Agent.full_run_2025-01-23_02-30-28_full_retry_1.json\"\n", "    ).read_text()\n", ")\n", "merged_run = merge_results(first_run, retry)\n", "Path(base / \"full_run_2025-01-23_02-30-28_full_retry_1/merged.json\").write_text(\n", "    json.dumps(merged_run, indent=2)\n", ")\n", "result_report(merged_run)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = json.loads(\n", "    Path(\n", "        \"/opt/swebench/swebench/full_run_2025-01-23_02-30-28_full_retry_4/merged.json\"\n", "    ).read_text()\n", ")\n", "print(len(results[\"resolved_ids\"]))\n", "print(len(set(results[\"resolved_ids\"])))\n", "result_report(results)\n", "print(json.dumps(results, indent=2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q = json.loads(\n", "    Path(\n", "        \"/opt/swebench/swebench/full_run_2025-01-23_02-30-28_full_retry_5/consolidated_predictions.json\"\n", "    ).read_text()\n", ")\n", "print(len(q))\n", "z = set([x[\"instance_id\"] for x in q])\n", "print(len(z))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from random import randint\n", "import json\n", "from pathlib import Path\n", "\n", "\n", "def repo_issues(full_set, repo):\n", "    return full_set.filter(lambda x: x[\"repo\"] == repo)\n", "\n", "\n", "def issue_to_agent(issue):\n", "    return {\n", "        \"repo\": issue[\"repo\"],\n", "        \"instance_id\": issue[\"instance_id\"],\n", "        \"base_commit\": issue[\"base_commit\"],\n", "        \"problem_statement\": issue[\"problem_statement\"],\n", "        \"test_cases\": issue[\"FAIL_TO_PASS\"],\n", "    }\n", "\n", "\n", "def repo_sample(full_set, repo, sample_size):\n", "    repo = repo_issues(full_set, repo)\n", "    return [\n", "        issue_to_agent(issue) for issue in repo.shuffle().select(range(sample_size))\n", "    ]\n", "\n", "\n", "def consolidate_predictions(run_base: Path | str):\n", "    run_base = Path(run_base)\n", "    all_predictions = []\n", "    for prediction_file in run_base.glob(\"*/predictions.json\"):\n", "        if prediction_file.exists():\n", "            try:\n", "                all_predictions.extend(json.loads(prediction_file.read_text()))\n", "            except json.JSONDecodeError as e:\n", "                print(\"Failed to load predictions from\", prediction_file)\n", "                print(e)\n", "    return all_predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "\n", "run_dir = \"/home/<USER>/agent/swe_bench/test_run_sympy_sympy_1\"\n", "all_predictions = consolidate_predictions(run_dir)\n", "print(len(all_predictions))\n", "\n", "Path(run_dir, \"consolidated_predictions.json\").write_text(\n", "    json.dumps(all_predictions, indent=2)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "agent_issues = []\n", "for idx, issue in enumerate(swebench):\n", "    # print(idx, issue[\"instance_id\"])\n", "    agent_issues.append(issue_to_agent(issue))\n", "Path(\"agent_issues.json\").write_text(json.dumps(agent_issues, indent=2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Unique repos in swebench\n", "swebench_repos = set([issue[\"repo\"] for issue in swebench])\n", "samples = []\n", "for repo in swebench_repos:\n", "    print(repo, len(repo_issues(swebench, repo)))\n", "    rs = repo_sample(swebench, repo, 2)\n", "    samples.extend(rs)\n", "print(len(swebench))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import subprocess\n", "import os\n", "\n", "\n", "def run_one(issue):\n", "    env = os.environ.copy()\n", "    instructions_base = \"\"\"\n", "    The issue you need to solve is:\n", "    {issue}\n", "\n", "    Do not forget to start by checking out the specified repository and syncing to the correct commit.\n", "\n", "    When you have a patch, make sure the patch applies with a simple \"patch\" cmake sure you write out a predictions file.  \n", "    The predictions file should be written to \"predictions.json\" in your workspace.\n", "    After writing the file, read it in to verify.\n", "    The predictions file should be longer than 1 byte.  If the file is 1 byte in length or less, re-write the prediction to the file.\n", "    \"\"\"\n", "    workspace = f\"workspace_{issue['instance_id']}\"\n", "    agent_cmd = [\n", "        \"bash\",\n", "        \"./launch_agent.sh\",\n", "        \"-i\",\n", "        \"Read instructions.txt in your workspace and follow the commands\",\n", "        \"--approve-command-execution\",\n", "        \"--log-file\",\n", "        f\"{workspace}/agent_log.txt\",\n", "        \"-q\",\n", "    ]\n", "    env[\"WS\"] = workspace\n", "    Path(workspace).mkdir(parents=True, exist_ok=True)\n", "    Path(workspace, \"instructions.txt\").write_text(\n", "        instructions_base.format(issue=json.dumps(issue, indent=2))\n", "    )\n", "\n", "    subprocess.run(\n", "        agent_cmd,\n", "        env=env,\n", "        cwd=\"/home/<USER>/augment/research/eval/swe_bench/agent_qa/swe_bench\",\n", "    )\n", "\n", "\n", "for sample in samples:\n", "    print(sample)\n", "    run_one(sample)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "all_predictions = []\n", "for sample in samples:\n", "    prediction_file = Path(f\"workspace_{sample['instance_id']}/predictions.json\")\n", "    if prediction_file.exists():\n", "        try:\n", "            all_predictions.extend(json.loads(prediction_file.read_text()))\n", "        except:  # noqa: E722 pylint: disable=bare-except\n", "            print(\"Failed to load predictions from\", prediction_file)\n", "\n", "print(f\"Found {len(all_predictions)} predictions\")\n", "\n", "Path(\"consolidated_predictions.json\").write_text(json.dumps(all_predictions, indent=2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.llm_client import (\n", "    # LLMClient,\n", "    AnthropicDirectClient,\n", "    AnthropicVertexClient,\n", ")\n", "from research.llm_apis.llm_client import LLMMessages\n", "from research.llm_apis.llm_client import TextPrompt\n", "\n", "model = \"claude-3-5-sonnet-20240620\"\n", "model = \"claude-3-5-sonnet-v2@20241022\"\n", "model = \"claude-3-5-sonnet-20241022\"\n", "anthropic_client = AnthropicDirectClient(\n", "    model_name=model,\n", ")\n", "\n", "messages: LLMMessages = []\n", "messages.append([TextPrompt(text=\"Write hello world in Python\")])\n", "# messages = [{\"role\": \"user\", \"content\": [{\"type\": \"text\", \"text\": \"Hello world\"}]}]\n", "anthropic_client.generate(\n", "    messages=messages,\n", "    max_tokens=1024,\n", "    temperature=0,\n", ")"]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from collections import defaultdict\n", "from typing import List\n", "import json\n", "import re\n", "\n", "from google.cloud.storage import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Bucket, transfer_manager\n", "\n", "\n", "def run_dir(base_path: Path, id: str):\n", "    return base_path / f\"workspace_{id}\"\n", "\n", "\n", "def index_evaluations(eval_paths: List[str]):\n", "    id_keys = [\n", "        \"submitted_ids\",\n", "        \"resolved_ids\",\n", "        \"completed_ids\",\n", "        \"unresolved_ids\",\n", "        \"error_ids\",\n", "        \"empty_patch_ids\",\n", "    ]\n", "    all_ids = defaultdict(\n", "        lambda: dict(\n", "            {\n", "                \"submitted_ids\": [],\n", "                \"resolved_ids\": [],\n", "                \"completed_ids\": [],\n", "                \"unresolved_ids\": [],\n", "                \"error_ids\": [],\n", "                \"empty_patch_ids\": [],\n", "                \"evaluations\": [],\n", "            }\n", "        )\n", "    )\n", "    client = Client(project=\"augment-research-gsc\")\n", "    for eval_path in eval_paths:\n", "        url = eval_path.replace(\"gs://\", \"\")\n", "        bucket_name = url.split(\"/\")[0]\n", "        bucket_path = url.split(\"/\", 1)[-1]\n", "\n", "        if not bucket_path.endswith(\"/\"):\n", "            bucket_path += \"/\"\n", "        bucket = client.get_bucket(bucket_name)\n", "        print(f\"Listing blobs in gs://{bucket_name}/{bucket_path}\")\n", "        blobs = bucket.list_blobs(\n", "            prefix=bucket_path, match_glob=\"**Augment_Agent*.json\", delimiter=\"/\"\n", "        )\n", "\n", "        log_path = f\"{bucket_path}logs/run_evaluation/\"\n", "\n", "        for blob in blobs:\n", "            print(f\"Checking {blob.name}\")\n", "            evaluation = json.loads(blob.download_as_text())\n", "            for id_key in id_keys:\n", "                for id in evaluation[id_key]:\n", "                    all_ids[id][id_key].append(\n", "                        f\"gs://{bucket_name}/{bucket_path}workspace_{id}\"\n", "                    )\n", "                    if id_key == \"submitted_ids\":\n", "                        match_glob = f\"**/Augment_Agent/{id}/*\"\n", "                        log_dirs = bucket.list_blobs(\n", "                            prefix=log_path,\n", "                            match_glob=match_glob,\n", "                        )\n", "                        log_dirs = list(log_dirs)\n", "                        if len(log_dirs):\n", "                            log_dir = log_dirs[0]\n", "                            lp = re.sub(\"/[^/]*$\", \"/\", log_dir.name)\n", "                            all_ids[id][\"evaluations\"].append(\n", "                                f\"gs://{bucket_name}/{lp}\"\n", "                            )\n", "\n", "    return all_ids\n", "\n", "\n", "def htmlify_index(all_ids: dict, stage_dest: str):\n", "    \"\"\"Creates an HTML index of SWE-bench evaluation results and stages associated files.\n", "\n", "    This function processes evaluation results and creates an organized HTML view of the runs,\n", "    copying relevant files (excluding workspace checkouts) to a staging destination.\n", "\n", "    Args:\n", "        all_ids: A dictionary mapping task IDs to their evaluation results. Structure:\n", "            {\n", "                \"task_id\": {\n", "                    \"submitted_ids\": [Path],\n", "                    \"resolved_ids\": [Path],\n", "                    \"completed_ids\": [Path],\n", "                    \"unresolved_ids\": [Path],\n", "                    \"error_ids\": [Path],\n", "                    \"empty_patch_ids\": [Path]\n", "                }\n", "            }\n", "        stage_dest: str the destination directory where files will be staged; should be a gcs path\n", "\n", "    Returns:\n", "        None\n", "\n", "    Example:\n", "        >>> all_ids = index_evaluations(eval_paths)\n", "        >>> htmlify_index(all_ids, \"gs://bucket/path/to/staging\")\n", "    \"\"\"\n", "    dataset_total = 500\n", "    submitted_ids = set([k for k, v in all_ids.items() if v[\"submitted_ids\"]])\n", "    resolved_ids = set([k for k, v in all_ids.items() if v[\"resolved_ids\"]])\n", "    html_header = (\n", "        \"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>SWE-bench Evaluation Results</title>\n", "    <style>\n", "        body {\n", "            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;\n", "            line-height: 1.6;\n", "            margin: 20px;\n", "            background-color: #f0f4f8;\n", "            color: #333;\n", "        }\n", "        \n", "        .container {\n", "            max-width: 1200px;\n", "            margin: 0 auto;\n", "            padding: 20px;\n", "        }\n", "        \n", "        h1 {\n", "            color: #2c3e50;\n", "            margin-bottom: 20px;\n", "        }\n", "        \n", "        table {\n", "            width: 100%;\n", "            border-collapse: separate;\n", "            border-spacing: 0;\n", "            background-color: #fff;\n", "            border-radius: 8px;\n", "            overflow: hidden;\n", "            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n", "            margin: 20px 0;\n", "        }\n", "        \n", "        th {\n", "            background-color: #0074d9;\n", "            color: #fff;\n", "            text-align: left;\n", "            padding: 12px 15px;\n", "            font-weight: 500;\n", "        }\n", "        \n", "        td {\n", "            padding: 12px 15px;\n", "            border-bottom: 1px solid #e0e0e0;\n", "        }\n", "        \n", "        tr:hover {\n", "            background-color: #f5f9ff;\n", "        }\n", "        \n", "        tr:last-child td {\n", "            border-bottom: none;\n", "        }\n", "\n", "        tr.resolved {\n", "            background-color: #f0fff4;  /* pale green */\n", "        }\n", "        \n", "        tr.resolved:hover {\n", "            background-color: #e6ffed;  /* slightly darker on hover */\n", "        }\n", "\n", "        .status-yes {\n", "            color: #2ecc71;\n", "            font-weight: 500;\n", "        }\n", "        \n", "        .status-no {\n", "            color: #e74c3c;\n", "            font-weight: 500;\n", "        }\n", "        \n", "        a {\n", "            color: #0074d9;\n", "            text-decoration: none;\n", "            margin-right: 10px;\n", "        }\n", "        \n", "        a:hover {\n", "            text-decoration: underline;\n", "        }\n", "        \n", "        .summary {\n", "            background-color: #e8f4fc;\n", "            padding: 15px;\n", "            border-radius: 6px;\n", "            margin-bottom: 20px;\n", "        }\n", "        .header-container {\n", "            display: flex;\n", "            align-items: center;\n", "            justify-content: space-between;\n", "            margin-bottom: 20px;\n", "        }\n", "        \n", "        .header-container h1 {\n", "            margin: 0;  /* Remove default margin to align properly */\n", "        }        \n", "        .help-link {\n", "            display: inline-block;\n", "            padding: 8px 16px;\n", "            background-color: #0074d9;\n", "            color: white;\n", "            border-radius: 4px;\n", "            text-decoration: none;\n", "            margin: 10px 0;\n", "            transition: background-color 0.2s ease;\n", "        }\n", "        \n", "        .help-link:hover {\n", "            background-color: #0063b8;\n", "            text-decoration: none;\n", "        }        \n", "    </style>\n", "    </head>\n", "<body>\n", "\"\"\"\n", "        + f\"\"\"\n", "    <div class=\"container\">\n", "        <div class=\"header-container\">\n", "            <h1>SWE-bench Evaluation Results</h1>\n", "            <a href=\"https://www.notion.so/Running-the-SWE-Bench-with-the-augment-agent-186bba10175a8000a4f4f94d68f4c6e6?pvs=4#186bba10175a807ea70fc7f2a3267355\" class=\"help-link\" target=\"_blank\">Help interpreting these results</a>\n", "        </div>\n", "        <div class=\"summary\">\n", "            Submission rate: {len(submitted_ids)} / {dataset_total} = {len(submitted_ids)*100/dataset_total:.2f}%<br>\n", "            Resolution rate: {len(resolved_ids)} / {dataset_total} = {len(resolved_ids)*100/dataset_total:.2f}%<br>\n", "        </div>\n", "\"\"\"\n", "    )\n", "\n", "    html_table = \"\"\"\n", "    <table>\n", "        <tr>\n", "            <th>Task ID</th>\n", "            <th>Resolved?</th>\n", "            <th>Runs</th>\n", "            <th>Evaluations</th>\n", "        </tr>\n", "\"\"\"\n", "    for item_id, item_results in all_ids.items():\n", "        resolved = \"Yes\" if item_results[\"resolved_ids\"] else \"No\"\n", "        status_class = \"status-yes\" if resolved == \"Yes\" else \"status-no\"\n", "        row_class = \"resolved\" if resolved == \"Yes\" else \"\"\n", "        html_table += f\"\"\"\n", "        <tr class=\"{row_class}\">\n", "            <td>{item_id}</td>\n", "            <td class=\"{status_class}\">{resolved}</td>\n", "\"\"\"\n", "        html_table += \"\"\"\n", "        <td>\n", "        \"\"\"\n", "        for id_path in sorted(item_results[\"submitted_ids\"]):\n", "            display_name = Path(id_path).parent.name\n", "            # Take advantage of Path breaking gs:// urls in a useful way\n", "            relative_path = Path(id_path).relative_to(Path(stage_dest))\n", "            # append a / to the relative_path so nginx provides a file listing\n", "            html_table += f\"\"\"\n", "            <a href=\"{relative_path}/\">{display_name}</a><br>\n", "            \"\"\"\n", "        html_table += \"\"\"\n", "        </td>\n", "\"\"\"\n", "        if len(item_results[\"evaluations\"]) == 0:\n", "            html_table += \"\"\"\n", "            <td>&nbsp;</td>\n", "\"\"\"\n", "        else:\n", "            html_table += \"\"\"\n", "            <td>\n", "            \"\"\"\n", "            for eval_path in sorted(item_results[\"evaluations\"]):\n", "                display_name = Path(eval_path).parent.parent.name\n", "                # Take advantage of Path breaking gs:// urls in a useful way\n", "                relative_path = Path(eval_path).relative_to(Path(stage_dest))\n", "                # append a / to the relative_path so nginx provides a file listing\n", "                html_table += f\"\"\"\n", "                <a href=\"{relative_path}/\">{display_name}</a><br>\n", "\"\"\"\n", "            html_table += \"\"\"\n", "            </td>\n", "\"\"\"\n", "        html_table += \"\"\"\n", "        </tr>\n", "\"\"\"\n", "\n", "    html_footer = \"\"\"\n", "    </table>\n", "</body>\n", "</html>\n", "\"\"\"\n", "    client = Client(project=\"augment-research-gsc\")\n", "    url = stage_dest.replace(\"gs://\", \"\")\n", "    bucket_name = url.split(\"/\")[0]\n", "    bucket_path = url.split(\"/\", 1)[-1]\n", "    bucket = Bucket(client, bucket_name)\n", "\n", "    index_blob = Blob(f\"{bucket_path}/index.html\", bucket)\n", "    index_blob.upload_from_string(html_header + html_table + html_footer, client=client)\n", "\n", "\n", "# base = \"gs://gcp-us1-public-html/marcmac/swe_bench/\"\n", "# eval_paths = [\n", "#     base + \"full_run_2025-01-23_02-30-28\",\n", "#     base + \"full_run_2025-01-23_02-30-28_full_retry_1\",\n", "#     base + \"full_run_2025-01-23_02-30-28_full_retry_2\",\n", "#     base + \"full_run_2025-01-23_02-30-28_full_retry_3\",\n", "#     base + \"full_run_2025-01-23_02-30-28_full_retry_4\",\n", "#     base + \"full_run_2025-01-23_02-30-28_full_retry_5\",\n", "# ]\n", "# all_ids = index_evaluations(list(eval_paths))\n", "# htmlify_index(all_ids, \"gs://gcp-us1-public-html/marcmac/swe_bench\")\n", "\n", "# for item_id, item_results in all_ids.items():\n", "#     print(item_id, len(item_results[\"resolved_ids\"]))\n", "# print(json.dumps(all_ids, indent=2, default=str))\n", "# stage_evaluation_results(all_ids, \"gs://gcp-us1-public-html/marcmac/swe_bench\")\n", "\n", "# htmlify_index(all_ids, )\n", "# resolved_ids = {k:v for k,v in all_ids.items() if v[\"resolved_ids\"]}\n", "# print(json.dumps(resolved_ids, indent=2, default=str))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def stage_results_to_gcs(results_base: Path, stage_dest: str):\n", "    \"\"\"Stages evaluation results and associated files to a specified destination.\"\"\"\n", "    client = Client(project=\"augment-research-gsc\")\n", "    url = stage_dest.replace(\"gs://\", \"\")\n", "    bucket_name = url.split(\"/\")[0]\n", "    bucket_path = url.split(\"/\", 1)[-1]\n", "    if not bucket_path.endswith(\"/\"):\n", "        bucket_path += \"/\"\n", "    bucket = client.get_bucket(bucket_name)\n", "\n", "    evaluations = list(results_base.glob(\"Augment_Agent*\"))\n", "    files_to_stage = [\n", "        (x.as_posix(), Blob(bucket_path + f\"{x.relative_to(results_base)}\", bucket))\n", "        for x in evaluations\n", "    ]\n", "    eval_logs = list(\n", "        [p for p in results_base.rglob(\"logs/run_evaluation/**/*\") if p.is_file()]\n", "    )\n", "    files_to_stage.extend(\n", "        [\n", "            (x.as_posix(), Blob(bucket_path + f\"{x.relative_to(results_base)}\", bucket))\n", "            for x in eval_logs\n", "        ]\n", "    )\n", "    workdirs = results_base.glob(\"workspace_*\")\n", "    for workdir in workdirs:\n", "        item_id = workdir.name.replace(\"workspace_\", \"\")\n", "        print(item_id)\n", "        file_list = workdir.rglob(\"*\")\n", "        for file in file_list:\n", "            relative = file.relative_to(workdir)\n", "            if file.is_file() and not (\n", "                relative.as_posix().startswith(item_id)\n", "                or relative.as_posix().startswith(\"conda_3.9\")\n", "                or relative.as_posix().startswith(\"python_wrappers\")\n", "            ):\n", "                # Copy the file to the staging destination\n", "                dest = Blob(bucket_path + f\"{file.relative_to(results_base)}\", bucket)\n", "                files_to_stage.append((file.as_posix(), dest))\n", "\n", "    print(f\"Staging {len(files_to_stage)} files to {stage_dest}\")\n", "    print(f\"Staging to {bucket_name}/{bucket_path}\")\n", "\n", "    # for f in files_to_stage:\n", "    #     print(f)\n", "    transfer_manager.upload_many(\n", "        files_to_stage,\n", "        skip_if_exists=True,\n", "    )\n", "\n", "\n", "stage_results_to_gcs(\n", "    Path(\"/opt/swebench/swebench/local_docker_4\"),\n", "    \"gs://gcp-us1-public-html/marcmac/swe_bench/local_docker/20250129/local_docker_4\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["local_idx = index_evaluations(\n", "    [\n", "        \"gs://gcp-us1-public-html/marcmac/swe_bench/local_docker/20250129/local_docker_1\",\n", "        \"gs://gcp-us1-public-html/marcmac/swe_bench/local_docker/20250129/local_docker_2\",\n", "        \"gs://gcp-us1-public-html/marcmac/swe_bench/local_docker/20250129/local_docker_3\",\n", "        \"gs://gcp-us1-public-html/marcmac/swe_bench/local_docker/20250129/local_docker_4\",\n", "    ]\n", ")\n", "# print(json.dumps(local_idx, indent=2, default=str))"]}, {"cell_type": "code", "execution_count": 120, "metadata": {}, "outputs": [], "source": ["htmlify_index(\n", "    local_idx, \"gs://gcp-us1-public-html/marcmac/swe_bench/local_docker/20250129\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(local_idx[\"astropy__astropy-14598\"][\"evaluations\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
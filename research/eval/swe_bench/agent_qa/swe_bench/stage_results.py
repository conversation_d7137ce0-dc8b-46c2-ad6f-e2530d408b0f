#!/usr/bin/env python3
"""Stage evaluation results to GCS, indexes them and creates an html page."""

from datetime import datetime
from pathlib import Path
from collections import defaultdict
import argparse

from google.cloud.storage import C<PERSON>, <PERSON><PERSON>b, Bucket, transfer_manager

import json
import re
from datetime import timedelta

from natsort import natsorted
from typing import List


def parse_duration_string(duration_str: str) -> timedelta:
    """Parse a duration string like '0 hours 16 mins 54 seconds' into a timedelta object.

    Args:
        duration_str: A string in the format 'X hours Y mins Z seconds'

    Returns:
        A timedelta object representing the duration
    """
    # Extract hours, minutes, and seconds using regex
    hours_match = re.search(r"(\d+)\s+hours?", duration_str)
    mins_match = re.search(r"(\d+)\s+mins?", duration_str)
    seconds_match = re.search(r"(\d+)\s+seconds?", duration_str)

    # Default values if not found
    hours = int(hours_match.group(1)) if hours_match else 0
    minutes = int(mins_match.group(1)) if mins_match else 0
    seconds = int(seconds_match.group(1)) if seconds_match else 0

    # Create timedelta object
    return timedelta(hours=hours, minutes=minutes, seconds=seconds)


def index_evaluations(eval_paths: List[str]):
    id_keys = [
        "submitted_ids",
        "resolved_ids",
        "completed_ids",
        "unresolved_ids",
        "error_ids",
        "empty_patch_ids",
    ]
    all_ids = defaultdict(
        lambda: dict(
            {
                "submitted_ids": [],
                "resolved_ids": [],
                "completed_ids": [],
                "unresolved_ids": [],
                "error_ids": [],
                "empty_patch_ids": [],
                "evaluations": [],
                "agent_rc": [],
                "directory_present_ids": [],
                "durations": [],
            }
        )
    )
    client = Client(project="augment-research-gsc")

    for eval_path in eval_paths:
        url = eval_path.replace("gs://", "")
        bucket_name = url.split("/")[0]
        bucket_path = url.split("/", 1)[-1]

        if not bucket_path.endswith("/"):
            bucket_path += "/"
        bucket = client.get_bucket(bucket_name)
        print(f"Listing blobs in gs://{bucket_name}/{bucket_path}")
        blobs = bucket.list_blobs(
            prefix=bucket_path, match_glob="**Augment_Agent*.json", delimiter="/"
        )

        for blob in blobs:
            print(f"Checking {blob.name}")
            evaluation = json.loads(blob.download_as_text())
            for id_key in id_keys:
                for id in evaluation[id_key]:
                    all_ids[id][id_key].append(
                        f"gs://{bucket_name}/{bucket_path}workspace_{id}"
                    )
                    if id_key == "submitted_ids":
                        log_path = f"{bucket_path}workspace_{id}/logs/run_evaluation/"
                        match_glob = f"**/Augment_Agent/{id}/*"
                        log_dirs = bucket.list_blobs(
                            prefix=log_path,
                            match_glob=match_glob,
                        )
                        log_dirs = list(log_dirs)
                        if len(log_dirs):
                            log_dir = log_dirs[0]
                            lp = re.sub("/[^/]*$", "/", log_dir.name)
                            all_ids[id]["evaluations"].append(
                                f"gs://{bucket_name}/{lp}"
                            )
            work_dirs = bucket.list_blobs(
                prefix=bucket_path, match_glob="**/agent_rc.txt"
            )
            for work_dir in work_dirs:
                wd = work_dir.name.replace(bucket_path, "").replace("/agent_rc.txt", "")
                if wd.startswith("workspace_"):
                    id = wd.replace("workspace_", "")
                    duration_file = bucket.blob(
                        f"{bucket_path}workspace_{id}/agent_duration.txt"
                    )
                    if duration_file.exists():
                        duration_str = duration_file.download_as_text()
                        ts = parse_duration_string(duration_str)
                        all_ids[id]["durations"].append(float(ts.total_seconds()))
                    all_ids[id]["directory_present_ids"].append(
                        f"gs://{bucket_name}/{bucket_path}{wd}"
                    )
                    rc_file = f"{bucket_path}workspace_{id}/agent_rc.txt"
                    rc_blob = bucket.blob(rc_file)
                    if rc_blob.exists():
                        rc = str(rc_blob.download_as_text())
                        all_ids[id]["agent_rc"] = [rc]

    return all_ids


def htmlify_index(all_ids: dict, stage_dest: str):
    """Creates an HTML index of SWE-bench evaluation results and stages associated files.

    This function processes evaluation results and creates an organized HTML view of the runs,
    copying relevant files (excluding workspace checkouts) to a staging destination.

    Args:
        all_ids: A dictionary mapping task IDs to their evaluation results. Structure:
            {
                "task_id": {
                    "submitted_ids": [Path],
                    "resolved_ids": [Path],
                    "completed_ids": [Path],
                    "unresolved_ids": [Path],
                    "error_ids": [Path],
                    "empty_patch_ids": [Path]
                }
            }
        stage_dest: str the destination directory where files will be staged; should be a gcs path

    Returns:
        None

    Example:
        >>> all_ids = index_evaluations(eval_paths)
        >>> htmlify_index(all_ids, "gs://bucket/path/to/staging")
    """
    dataset_total = 500
    submitted_ids = set([k for k, v in all_ids.items() if v["submitted_ids"]])
    resolved_ids = set([k for k, v in all_ids.items() if v["resolved_ids"]])
    present_ids = set([k for k, v in all_ids.items() if v["directory_present_ids"]])

    # Group submitted/resolved by repo name
    submitted_by_repo = defaultdict(int)
    resolved_by_repo = defaultdict(int)
    total_by_repo = defaultdict(int)
    for k, v in all_ids.items():
        repo_name = k.rsplit("-", 1)[0]
        if v["submitted_ids"]:
            submitted_by_repo[repo_name] += 1
        if v["resolved_ids"]:
            resolved_by_repo[repo_name] += 1
        if v["directory_present_ids"]:
            total_by_repo[repo_name] += 1
    total_by_repo = dict(sorted(total_by_repo.items(), key=lambda x: x[0]))

    # Separate durations by success/failure
    all_durations = []
    successful_durations = []
    unsuccessful_durations = []

    nonzero_rc_by_repo = {k: 0 for k in total_by_repo.keys()}
    for k, v in all_ids.items():
        repo_name = k.rsplit("-", 1)[0]
        is_successful = bool(v["resolved_ids"])

        if v["agent_rc"] and v["agent_rc"][0] != "0":
            nonzero_rc_by_repo[repo_name] += 1

        if v["durations"]:
            for duration in v["durations"]:
                all_durations.append(duration)
                if is_successful:
                    successful_durations.append(duration)
                else:
                    unsuccessful_durations.append(duration)

    # Calculate statistics for all durations
    all_durations.sort()
    successful_durations.sort() if successful_durations else None
    unsuccessful_durations.sort() if unsuccessful_durations else None

    # Function to calculate duration statistics
    def calculate_duration_stats(durations):
        if not durations:
            return {
                "min": "N/A",
                "max": "N/A",
                "p25": "N/A",
                "median": "N/A",
                "p75": "N/A",
            }
        return {
            "min": f"{durations[0]/60:.2f}m",
            "max": f"{durations[-1]/60:.2f}m",
            "p25": f"{durations[len(durations) // 4]/60:.2f}m",
            "median": f"{durations[len(durations) // 2]/60:.2f}m",
            "p75": f"{durations[len(durations) * 3 // 4]/60:.2f}m",
        }

    # Calculate statistics for each group
    all_stats = calculate_duration_stats(all_durations)
    successful_stats = calculate_duration_stats(successful_durations)
    unsuccessful_stats = calculate_duration_stats(unsuccessful_durations)

    nonzero_rc_by_repo = dict(sorted(nonzero_rc_by_repo.items(), key=lambda x: x[0]))

    total_rate_by_repo = {k: v / dataset_total * 100 for k, v in total_by_repo.items()}
    nonzero_rc_rate_by_repo = {
        k: nonzero_rc_by_repo[k] / v * 100 for k, v in total_by_repo.items()
    }
    resolution_rate_by_repo = {
        k: resolved_by_repo[k] / v * 100 for k, v in total_by_repo.items()
    }
    submission_rate_by_repo = {
        k: submitted_by_repo[k] / v * 100 for k, v in total_by_repo.items()
    }

    now_str = str(datetime.now())
    html_header = """
<!DOCTYPE html>
<html>
<head>
    <title>SWE-bench Evaluation Results</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f0f4f8;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background-color: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin: 20px 0;
        }

        th {
            background-color: #0074d9;
            color: #fff;
            text-align: left;
            padding: 12px 15px;
            font-weight: 500;
        }

        td {
            padding: 12px 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        tr:hover {
            background-color: #f5f9ff;
        }

        tr:last-child td {
            border-bottom: none;
        }

        tr.resolved {
            background-color: #f0fff4;  /* pale green */
        }

        tr.resolved:hover {
            background-color: #e6ffed;  /* slightly darker on hover */
        }

        .status-yes {
            color: #2ecc71;
            font-weight: 500;
        }

        .status-no {
            color: #e74c3c;
            font-weight: 500;
        }

        a {
            color: #0074d9;
            text-decoration: none;
            margin-right: 10px;
        }

        a:hover {
            text-decoration: underline;
        }

        .summary {
            background-color: #e8f4fc;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .header-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .header-container h1 {
            margin: 0;  /* Remove default margin to align properly */
        }
        .help-link {
            display: inline-block;
            padding: 8px 16px;
            background-color: #0074d9;
            color: white;
            border-radius: 4px;
            text-decoration: none;
            margin: 10px 0;
            transition: background-color 0.2s ease;
        }

        .help-link:hover {
            background-color: #0063b8;
            text-decoration: none;
        }
    </style>
    </head>
<body>
"""
    srr = len(resolved_ids) / len(submitted_ids) * 100 if len(submitted_ids) > 0 else 0
    html_header += f"""
    <div class="container">
        <div class="header-container">
            <h1>SWE-bench Evaluation Results</h1>
            <a href="https://www.notion.so/Running-the-SWE-Bench-with-the-augment-agent-186bba10175a8000a4f4f94d68f4c6e6?pvs=4#186bba10175a807ea70fc7f2a3267355" class="help-link" target="_blank">Help interpreting these results</a>
        </div>
        <div class="summary">
            <div style='margin: 10px 0; float: right; display: flex; justify-content: flex-end;'>
                <table style='width: 50%; margin-bottom: 15px;'>
                    <tr>
                        <th>Run Type</th>
                        <th>Min</th>
                        <th>25th%</th>
                        <th>Median</th>
                        <th>75th%</th>
                        <th>Max</th>
                        <th>Count</th>
                    </tr>
                    <tr>
                        <td><strong>All Runs</strong></td>
                        <td>{all_stats["min"]}</td>
                        <td>{all_stats["p25"]}</td>
                        <td>{all_stats["median"]}</td>
                        <td>{all_stats["p75"]}</td>
                        <td>{all_stats["max"]}</td>
                        <td>{len(all_durations)}</td>
                    </tr>
                    <tr style='background-color: #e6ffed;'>
                        <td><strong>Successful</strong></td>
                        <td>{successful_stats["min"]}</td>
                        <td>{successful_stats["p25"]}</td>
                        <td>{successful_stats["median"]}</td>
                        <td>{successful_stats["p75"]}</td>
                        <td>{successful_stats["max"]}</td>
                        <td>{len(successful_durations)}</td>
                    </tr>
                    <tr style='background-color: #ffebe9;'>
                        <td><strong>Unsuccessful</strong></td>
                        <td>{unsuccessful_stats["min"]}</td>
                        <td>{unsuccessful_stats["p25"]}</td>
                        <td>{unsuccessful_stats["median"]}</td>
                        <td>{unsuccessful_stats["p75"]}</td>
                        <td>{unsuccessful_stats["max"]}</td>
                        <td>{len(unsuccessful_durations)}</td>
                    </tr>
                </table>
            </div>
            This page was generated on {now_str}.<br>
            {len(present_ids)} agent runs found.<br>
            Submission rate: {len(submitted_ids)} / {dataset_total} = {len(submitted_ids)*100/dataset_total:.2f}%<br>
            Resolution rate: {len(resolved_ids)} / {dataset_total} = {len(resolved_ids)*100/dataset_total:.2f}%<br>
            Submitted resolve rate: {srr:.2f}%<br>
            <details open>
                <summary>Resolutions by repo</summary>
                {' '.join([f'{repo}: {resolved_by_repo[repo]} / {total_by_repo[repo]} = {rate:.2f}%<br>' for repo, rate in resolution_rate_by_repo.items()])}
            </details>
            <details closed>
                <summary>Total example by repo</summary>
                {' '.join([f'{repo}: {count} / {dataset_total} = ({total_rate_by_repo[repo]:.2f}%)<br>' for repo, count in total_by_repo.items()])}
            </details>
            <details closed>
                <summary>Submission by repo</summary>
                {' '.join([f'{repo}: {submitted_by_repo[repo]} / {total_by_repo[repo]} = {rate:.2f}%<br>' for repo, rate in submission_rate_by_repo.items()])}
            </details>
            <details closed>
                <summary>Non-zero RC counts by repo</summary>
                {' '.join([f'{repo}: {nonzero_rc_by_repo[repo]} / {total_by_repo[repo]} = ({rate:.2f}%)<br>' for repo, rate in nonzero_rc_rate_by_repo.items()])}
            </details>
        </div>
"""

    html_table = """
    <table>
        <tr>
            <th>Task ID</th>
            <th>Resolved?</th>
            <th>Runs</th>
            <th>Evaluations</th>
        </tr>
"""
    all_keys = natsorted(all_ids.keys())
    for item_id in all_keys:
        item_results = all_ids[item_id]
        resolved = "Yes" if item_results["resolved_ids"] else "No"
        status_class = "status-yes" if resolved == "Yes" else "status-no"
        row_class = "resolved" if resolved == "Yes" else ""
        agent_rc = item_results["agent_rc"][0] if item_results["agent_rc"] else "-"
        html_table += f"""
        <tr class="{row_class}">
            <td>{item_id}</td>
            <td class="{status_class}">{resolved} ({agent_rc})</td>
"""
        html_table += """
        <td>
        """
        for id_path in natsorted(item_results["directory_present_ids"]):
            display_name = Path(id_path).parent.name
            # Take advantage of Path breaking gs:// urls in a useful way
            relative_path = Path(id_path).relative_to(Path(stage_dest))
            # append a / to the relative_path so nginx provides a file listing
            html_table += f"""
            <a href="{relative_path}/">{display_name}</a><br>
            """
        html_table += """
        </td>
"""
        if len(item_results["evaluations"]) == 0:
            html_table += """
            <td>&nbsp;</td>
"""
        else:
            html_table += """
            <td>
            """
            for eval_path in natsorted(item_results["evaluations"]):
                display_name = Path(eval_path).parent.parent.name
                # Take advantage of Path breaking gs:// urls in a useful way
                relative_path = Path(eval_path).relative_to(Path(stage_dest))
                # append a / to the relative_path so nginx provides a file listing
                html_table += f"""
                <a href="{relative_path}/">{display_name}</a><br>
"""
            html_table += """
            </td>
"""
        html_table += """
        </tr>
"""

    html_footer = """
    </table>
</body>
</html>
"""
    client = Client(project="augment-research-gsc")
    url = stage_dest.replace("gs://", "")
    bucket_name = url.split("/")[0]
    bucket_path = url.split("/", 1)[-1]
    if bucket_path.endswith("/"):
        bucket_path = bucket_path[:-1]
    bucket = Bucket(client, bucket_name)

    index_blob = Blob(f"{bucket_path}/index.html", bucket)
    index_blob.upload_from_string(html_header + html_table + html_footer, client=client)
    print(f"Staged index.html to {stage_dest}")
    print(
        f"View at https://webserver.gcp-us1.r.augmentcode.com/{bucket_path}/index.html"
    )


def stage_results_to_gcs(results_base: Path, stage_dest: str):
    """Stages evaluation results and associated files to a specified destination."""
    client = Client(project="augment-research-gsc")
    url = stage_dest.replace("gs://", "")
    bucket_name = url.split("/")[0]
    bucket_path = url.split("/", 1)[-1]
    if not bucket_path.endswith("/"):
        bucket_path += "/"
    bucket = client.get_bucket(bucket_name)

    evaluations = list(results_base.glob("Augment_Agent*"))
    files_to_stage = [
        (x.as_posix(), Blob(bucket_path + f"{x.relative_to(results_base)}", bucket))
        for x in evaluations
    ]
    eval_logs = list(
        [p for p in results_base.rglob("logs/run_evaluation/**/*") if p.is_file()]
    )
    files_to_stage.extend(
        [
            (x.as_posix(), Blob(bucket_path + f"{x.relative_to(results_base)}", bucket))
            for x in eval_logs
        ]
    )
    workdirs = results_base.glob("workspace_*")
    for workdir in workdirs:
        item_id = workdir.name.replace("workspace_", "")
        print(item_id)
        file_list = workdir.rglob("*")
        for file in file_list:
            relative = file.relative_to(workdir)
            if file.is_file() and not (
                relative.as_posix().startswith(item_id)
                or relative.as_posix().startswith("conda_3.9")
                or relative.as_posix().startswith("python_wrappers")
            ):
                # Copy the file to the staging destination
                dest = Blob(bucket_path + f"{file.relative_to(results_base)}", bucket)
                files_to_stage.append((file.as_posix(), dest))

    print(f"Staging {len(files_to_stage)} files to {stage_dest}")
    print(f"Staging to {bucket_name}/{bucket_path}")

    # for f in files_to_stage:
    #     print(f)
    transfer_manager.upload_many(
        files_to_stage,
        skip_if_exists=True,
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "results_base",
        type=Path,
        help="Base directory containing the results.",
    )
    parser.add_argument(
        "stage_dest",
        type=str,
        help="Destination to stage the results to.",
    )
    parser.add_argument(
        "--html-only",
        action="store_true",
        help="Only generate the HTML index, do not stage the results.",
    )
    args = parser.parse_args()

    if not args.html_only:
        stage_results_to_gcs(args.results_base, args.stage_dest)
    all_ids = index_evaluations([args.stage_dest])
    htmlify_index(all_ids, args.stage_dest)

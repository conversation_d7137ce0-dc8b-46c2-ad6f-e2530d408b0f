#!/usr/bin/env python3
"""Analyze trends across multiple evaluation results and generate a report."""

import argparse
from collections import defaultdict, Counter
import json
from pathlib import Path
import re
from typing import Dict, List, Set, Tuple, Any
from urllib.parse import urlparse
import sys

from stage_results import index_evaluations  # type: ignore
from diff_results import url_to_gcs_path  # type: ignore


def get_repo_from_task_id(task_id: str) -> str:
    """
    Extract repository name from task ID by removing the numeric suffix.

    Args:
        task_id: Task ID in the format "repo-name-12345"

    Returns:
        Repository name
    """
    # Match the pattern of repo name followed by dash and numbers
    match = re.match(r"(.*?)-\d+$", task_id)
    if match:
        return match.group(1)
    return task_id  # Return original if no match


def analyze_trends(locations: List[str]) -> Dict:
    """
    Analyze trends across multiple evaluation results.

    Args:
        locations: List of GCS paths or URLs containing evaluation results

    Returns:
        Dictionary containing trend analysis
    """
    # Convert URLs to GCS paths
    gcs_locations = [url_to_gcs_path(loc) for loc in locations]

    # Sort locations lexically
    sorted_locations = sorted(zip(locations, gcs_locations))
    original_locations = [loc[0] for loc in sorted_locations]
    sorted_gcs_locations = [loc[1] for loc in sorted_locations]

    print(f"Analyzing trends across {len(locations)} locations")
    print("Locations in sorted order:")
    for i, loc in enumerate(sorted_gcs_locations):
        print(f"  {i+1}. {loc}")

    # Index all locations
    all_results = []
    all_task_ids = set()

    for i, gcs_loc in enumerate(sorted_gcs_locations):
        print(
            f"\nIndexing evaluations from {gcs_loc} ({i+1}/{len(sorted_gcs_locations)})"
        )
        results = index_evaluations([gcs_loc])
        all_results.append(results)
        all_task_ids.update(results.keys())

    # Initialize trend analysis
    trends = {
        "summary": {
            "locations": original_locations,
            "gcs_locations": sorted_gcs_locations,
            "total_tasks": len(all_task_ids),
            "tasks_in_all_locations": 0,
            "tasks_with_varying_results": 0,
            "tasks_trending_positive": 0,
            "tasks_trending_negative": 0,
            "tasks_stable": 0,
            "total_pass_rate": 0.0,
            "overall_stability": 0.0,
        },
        "tasks": {},
        "repositories": defaultdict(
            lambda: {
                "tasks": [],
                "pass_rate": 0.0,
                "stability": 0.0,
                "pass_rates_by_run": [],
                "total_passes": 0,
                "total_runs": 0,
            }
        ),
    }

    # Analyze each task
    tasks_in_all_locations = 0
    tasks_with_varying_results = 0
    tasks_trending_positive = 0
    tasks_trending_negative = 0
    tasks_stable = 0
    total_passes = 0
    total_runs = 0

    # Initialize repository pass rates by run
    repo_passes_by_run = defaultdict(lambda: [0] * len(sorted_gcs_locations))
    repo_runs_by_run = defaultdict(lambda: [0] * len(sorted_gcs_locations))

    for task_id in sorted(all_task_ids):
        task_trend = {
            "present_in": [],
            "resolution_history": [],
            "agent_rc_history": [],
            "trend": "unknown",
            "stability": 0.0,
            "pass_rate": 0.0,
            "locations": [],
            "repository": get_repo_from_task_id(task_id),
        }

        # Collect history for this task
        for i, results in enumerate(all_results):
            if task_id in results:
                task_trend["present_in"].append(i)

                # Resolution status
                resolved = bool(results[task_id]["resolved_ids"])
                task_trend["resolution_history"].append(resolved)

                if resolved:
                    total_passes += 1
                    repo_passes_by_run[task_trend["repository"]][i] += 1
                total_runs += 1
                repo_runs_by_run[task_trend["repository"]][i] += 1

                # Agent return code
                rc = (
                    results[task_id]["agent_rc"][0]
                    if results[task_id]["agent_rc"]
                    else None
                )
                task_trend["agent_rc_history"].append(rc)

                # Store location info
                location_info = {
                    "workspace": results[task_id]["directory_present_ids"][0]
                    if results[task_id]["directory_present_ids"]
                    else None,
                    "evaluations": results[task_id]["evaluations"][0]
                    if results[task_id]["evaluations"]
                    else None,
                    "resolved": resolved,
                    "agent_rc": rc,
                }
                task_trend["locations"].append(location_info)
            else:
                task_trend["resolution_history"].append(None)
                task_trend["agent_rc_history"].append(None)
                task_trend["locations"].append(None)

        # Calculate trend and stability
        if len(task_trend["present_in"]) == len(all_results):
            tasks_in_all_locations += 1

            # Check if results vary
            resolution_history = task_trend["resolution_history"]

            # Calculate pass rate for this task
            task_passes = sum(1 for x in resolution_history if x)
            task_trend["pass_rate"] = (
                task_passes / len(resolution_history) if resolution_history else 0.0
            )

            if len(set(resolution_history)) > 1:
                tasks_with_varying_results += 1

                # Calculate trend direction
                if len(resolution_history) >= 2:
                    # Simple trend: compare first half with second half
                    mid_point = len(resolution_history) // 2
                    first_half = resolution_history[:mid_point]
                    second_half = resolution_history[mid_point:]

                    first_half_success_rate = (
                        sum(1 for x in first_half if x) / len(first_half)
                        if first_half
                        else 0
                    )
                    second_half_success_rate = (
                        sum(1 for x in second_half if x) / len(second_half)
                        if second_half
                        else 0
                    )

                    if second_half_success_rate > first_half_success_rate:
                        task_trend["trend"] = "positive"
                        tasks_trending_positive += 1
                    elif second_half_success_rate < first_half_success_rate:
                        task_trend["trend"] = "negative"
                        tasks_trending_negative += 1
                    else:
                        task_trend["trend"] = "fluctuating"
            else:
                task_trend["trend"] = "stable"
                tasks_stable += 1

            # Calculate stability (percentage of consistent results)
            most_common_result = Counter(resolution_history).most_common(1)[0][0]
            stability = sum(
                1 for x in resolution_history if x == most_common_result
            ) / len(resolution_history)
            task_trend["stability"] = stability

        trends["tasks"][task_id] = task_trend

        # Add task to repository
        repo_name = task_trend["repository"]
        trends["repositories"][repo_name]["tasks"].append(task_id)

    # Update summary
    trends["summary"]["tasks_in_all_locations"] = tasks_in_all_locations
    trends["summary"]["tasks_with_varying_results"] = tasks_with_varying_results
    trends["summary"]["tasks_trending_positive"] = tasks_trending_positive
    trends["summary"]["tasks_trending_negative"] = tasks_trending_negative
    trends["summary"]["tasks_stable"] = tasks_stable
    trends["summary"]["total_pass_rate"] = (
        total_passes / total_runs if total_runs > 0 else 0.0
    )

    # Calculate overall stability across all tasks
    if tasks_in_all_locations > 0:
        overall_stability = (
            sum(
                trends["tasks"][task_id]["stability"]
                for task_id in trends["tasks"]
                if len(trends["tasks"][task_id]["present_in"]) == len(all_results)
            )
            / tasks_in_all_locations
        )
        trends["summary"]["overall_stability"] = overall_stability

    # Calculate repository statistics
    for repo_name, repo_data in trends["repositories"].items():
        # Calculate pass rates by run
        repo_data["pass_rates_by_run"] = [
            repo_passes_by_run[repo_name][i] / repo_runs_by_run[repo_name][i]
            if repo_runs_by_run[repo_name][i] > 0
            else 0.0
            for i in range(len(sorted_gcs_locations))
        ]

        # Calculate overall pass rate
        repo_total_passes = sum(repo_passes_by_run[repo_name])
        repo_total_runs = sum(repo_runs_by_run[repo_name])
        repo_data["total_passes"] = repo_total_passes
        repo_data["total_runs"] = repo_total_runs
        repo_data["pass_rate"] = (
            repo_total_passes / repo_total_runs if repo_total_runs > 0 else 0.0
        )

        # Calculate stability (consistency of results across runs)
        repo_tasks_complete = [
            task_id
            for task_id in repo_data["tasks"]
            if len(trends["tasks"][task_id]["present_in"]) == len(all_results)
        ]

        if repo_tasks_complete:
            repo_data["stability"] = sum(
                trends["tasks"][task_id]["stability"] for task_id in repo_tasks_complete
            ) / len(repo_tasks_complete)
        else:
            repo_data["stability"] = 0.0

    return trends


def generate_trend_report(trends: Dict, output_path: str = "") -> str:
    """
    Generate a human-readable report from the trend analysis.

    Args:
        trends: Dictionary containing trend analysis
        output_path: Optional path to save the report to

    Returns:
        String containing the report
    """
    summary = trends["summary"]

    report = [
        "# SWE-bench Evaluation Trends Report",
        "",
        "## Locations Analyzed (Sorted Lexically)",
        "",
    ]

    # Add locations
    for i, loc in enumerate(summary["locations"]):
        report.append(f"{i+1}. {loc}")

    report.extend(
        [
            "",
            "## Summary",
            "",
            f"- Total tasks: {summary['total_tasks']}",
            f"- Tasks present in all locations: {summary['tasks_in_all_locations']}",
            f"- Tasks with varying results: {summary['tasks_with_varying_results']}",
            f"- Tasks trending positive: {summary['tasks_trending_positive']}",
            f"- Tasks trending negative: {summary['tasks_trending_negative']}",
            f"- Tasks with stable results: {summary['tasks_stable']}",
            f"- Total pass rate: {summary['total_pass_rate']:.2f}",
            f"- Overall stability: {summary['overall_stability']:.2f}",
            "",
        ]
    )

    # Add repository summary section
    report.extend(
        [
            "## Repository Summary (Sorted by Name)",
            "",
        ]
    )

    # Sort repositories by name
    sorted_repos = sorted(trends["repositories"].items())

    # Create a table header for repository summary with better spacing
    report.extend(
        [
            "| Repository | Pass Rate | Stability | Tasks |",
            "|:-----------|:----------|:----------|:------|",
        ]
    )

    # Add repository rows
    for repo_name, repo_data in sorted_repos:
        pass_rate = repo_data["pass_rate"]
        stability = repo_data["stability"]
        task_count = len(repo_data["tasks"])
        report.append(
            f"| {repo_name} | {pass_rate:.2f} | {stability:.2f} | {task_count} |"
        )

    # Add detailed repository information
    report.extend(
        [
            "",
            "### Repository Details",
            "",
        ]
    )

    for repo_name, repo_data in sorted_repos:
        pass_rate = repo_data["pass_rate"]
        stability = repo_data["stability"]
        task_count = len(repo_data["tasks"])

        report.append(f"#### {repo_name}")
        report.append("")
        report.append(f"- **Pass Rate**: {pass_rate:.2f}")
        report.append(f"- **Stability**: {stability:.2f}")
        report.append(f"- **Tasks**: {task_count}")

        # Add pass rates by run on a single line
        run_rates = []
        for run_pass_rate in repo_data["pass_rates_by_run"]:
            run_rates.append(f"{run_pass_rate:.2f}")

        report.append(f"- **Pass Rates by Run**: {', '.join(run_rates)}")
        report.append("")

    # Add task results section
    report.extend(
        [
            "## Task Results (Sorted by Pass Rate, Stability, Name)",
            "",
        ]
    )

    # Collect all complete tasks
    complete_tasks = []
    incomplete_tasks = []

    for task_id, task_trend in trends["tasks"].items():
        if len(task_trend["present_in"]) == len(summary["locations"]):
            complete_tasks.append(
                (
                    task_id,
                    task_trend["pass_rate"],
                    task_trend["stability"],
                    task_trend["trend"],
                )
            )
        else:
            incomplete_tasks.append(task_id)

    # Sort tasks by pass rate (descending), then stability (descending), then name (ascending)
    complete_tasks.sort(key=lambda x: (-x[1], -x[2], x[0]))
    incomplete_tasks.sort()

    # Add complete tasks
    if complete_tasks:
        report.append("### Tasks Present in All Locations")
        report.append("")
        for task_id, pass_rate, stability, trend in complete_tasks:
            task_trend = trends["tasks"][task_id]
            history = "".join(
                ["✓" if x else "✗" for x in task_trend["resolution_history"]]
            )
            trend_indicator = {
                "positive": "↗️",
                "negative": "↘️",
                "fluctuating": "↕️",
                "stable": "→",
                "unknown": "?",
            }.get(trend, "?")

            report.append(
                f"- **{task_id}** (Pass Rate: {pass_rate:.2f}, Stability: {stability:.2f}, Trend: {trend_indicator})"
            )
            report.append(f"  - Repository: {task_trend['repository']}")
            report.append(f"  - History: {history}")
            report.append(f"  - Return codes: {task_trend['agent_rc_history']}")
            report.append("")

    # Add incomplete tasks
    if incomplete_tasks:
        report.append("### Tasks Not Present in All Locations")
        report.append("")
        for task_id in incomplete_tasks:
            task_trend = trends["tasks"][task_id]
            present_in = ", ".join([str(i + 1) for i in task_trend["present_in"]])
            history = "".join(
                [
                    "✓" if x else "✗" if x is False else "·"
                    for x in task_trend["resolution_history"]
                ]
            )
            report.append(f"- **{task_id}**")
            report.append(f"  - Repository: {task_trend['repository']}")
            report.append(f"  - Present in locations: {present_in}")
            report.append(f"  - History: {history}")
            report.append("")

    report_text = "\n".join(report)

    # Save report to file if output path is provided
    if output_path:
        with open(output_path, "w") as f:
            f.write(report_text)
        print(f"Report saved to {output_path}")

    return report_text


def save_json_trends(trends: Dict, output_path: str):
    """
    Save the trend analysis to a JSON file.

    Args:
        trends: Dictionary containing trend analysis
        output_path: Path to save the JSON file to
    """
    with open(output_path, "w") as f:
        json.dump(trends, f, indent=2)
    print(f"JSON trends saved to {output_path}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Analyze trends across multiple evaluation results"
    )
    parser.add_argument(
        "locations",
        type=str,
        nargs="+",
        help="GCS paths or URLs containing evaluation results",
    )
    parser.add_argument("--output", "-o", type=str, help="Path to save the report to")
    parser.add_argument(
        "--json", "-j", type=str, help="Path to save the JSON trends to"
    )
    args = parser.parse_args()

    # Ensure at least two locations are provided
    if len(args.locations) < 2:
        parser.error("At least two locations are required for trend analysis")

    # Analyze trends
    trends = analyze_trends(args.locations)

    # Generate and print report
    report = generate_trend_report(trends, args.output)
    if not args.output:
        print(report)

    # Save JSON trends if requested
    if args.json:
        save_json_trends(trends, args.json)

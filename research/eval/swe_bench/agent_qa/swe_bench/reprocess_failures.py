#!/usr/bin/env python3
"""Reprocess failures in a report directory.

Failures in a report directory that look like infra issues will be
re-evaluated and the results will be merged into the original report.

After running this, the HTML can be recreated with stage_results.py
"""

import argparse
import json
from pathlib import Path
import time

from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import (
    get_dataset_name,
    merge_reports,
    run_evaluation,
    stop_container,
)


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "report_dir",
        type=Path,
        help="Report directory to reprocess.",
    )
    parser.add_argument(
        "--swebench-venv-path",
        type=Path,
        default=Path("/opt/swebench/swebench/swebench_env/bin/python"),
        help="Path to the swebench venv python binary.",
    )
    args = parser.parse_args()
    return args


def reprocess(report_dir: Path, swebench_venv_path: Path):
    work_dirs = report_dir.glob("workspace_*")
    for work_dir in work_dirs:
        reprocess = False
        run_logs = work_dir.glob(
            "logs/run_evaluation/swe_work/Augment_Agent/*/run_instance.log"
        )
        predictions_file = work_dir / "predictions.json"
        if not predictions_file.exists():
            # print(f"Missing {predictions_file}")
            continue
        prediction = json.loads(predictions_file.read_text())[0]
        if not prediction.get("model_patch", ""):
            # print(f"Missing model_patch in {predictions_file}")
            continue
        report_file = work_dir / "Augment_Agent.swe_work.json"
        if not report_file.exists():
            print(f"Missing {report_file}, reprocessing")
            reprocess = True
        else:
            for log in run_logs:
                if "ERROR - Error creating container" in log.read_text():
                    reprocess = True
                    break
        if not reprocess:
            continue
        print(f"Reprocessing {work_dir}")
        # Stop any running containers
        instance_id = json.loads(predictions_file.read_text())[0].get("instance_id")
        if instance_id is not None:
            stop_container(f"sweb.eval.{instance_id}.swe_work")
        # Move the existing log
        ts = time.time()
        if (work_dir / "logs").exists():
            (work_dir / "logs").rename(work_dir / f"_logs_{ts}")
        # Run evaluation
        run_evaluation(
            predictions_file,
            get_dataset_name("full"),
            "swe_work",
            num_processes=1,
            swebench_venv_path=swebench_venv_path,
        )
        # eval_file = workspace / f"Augment_Agent.swe_bench.json"
        # result_report(eval_file)

    results_files = sorted(list(report_dir.glob("*/Augment_Agent.swe_work.json")))
    merge_reports(results_files, report_dir / "Augment_Agent.json")


if __name__ == "__main__":
    args = parse_args()
    reprocess(args.report_dir, args.swebench_venv_path)

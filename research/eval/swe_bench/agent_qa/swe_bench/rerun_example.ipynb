{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import yaml\n", "from research.eval.harness.systems.agent_system import AgentSystem\n", "from research.eval.harness.tasks.swe_bench_task import SWEBenchTask\n", "import logging\n", "\n", "workdir = \"/home/<USER>/agent-workdir\"\n", "\n", "cfg = f\"\"\"\n", "system:\n", "  num_processes: 8\n", "  workdir: {workdir}\n", "  log_dir_base: /mnt/efs/augment/user/colin/agent_logs/\n", "  agent_timeout: 2400\n", "  auth_token_file: /home/<USER>/.augment/token\n", "\n", "task:\n", "  limit_by_example_names:\n", "  - sympy__sympy-15349\n", "  - sympy__sympy-13878\n", "  dataset_path: /mnt/efs/augment/data/swebench_processed_eval/v1.pickle.gz\n", "  workdir: {workdir}\n", "  num_eval_processes: 8\n", "  skip_generation: false\n", "  `eager_eval: true`\n", "\"\"\"\n", "# set log level to print everything\n", "logging.basicConfig(level=logging.DEBUG)\n", "\n", "config = yaml.safe_load(cfg)\n", "\n", "logging.info(\"Loading system...\")\n", "system = AgentSystem.from_yaml_config(config[\"system\"])\n", "system.load()\n", "logging.info(\"System loaded.\")\n", "\n", "logging.info(\"Load task...\")\n", "task = SWEBenchTask.from_yaml_config(config[\"task\"])\n", "\n", "result = task.run(system, workdir)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
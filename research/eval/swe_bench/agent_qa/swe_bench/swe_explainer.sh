#!/bin/bash


proto="/home/<USER>/augment/experimental/rich/rubrik/chat_proto.py"
report_file=$1
base_dir=$(dirname $report_file)

instruct="You are analyzing the results of an AI agent's attempt to solve a programming task.

1. Look at the file "./instructions.txt" in the workspace to see the promt the agent received.
2. Look at file "./agent_log.txt" in the workspace to see what the agent did.
3. Look the file 'logs/run_evaluation/swe_work/Augment_Agent/TASK_ID/test_output.txt' to see the tests that were run.

Please provide:
1. A summary of what the agent did.
2. A summary of what went wrong.
3. Suggestions for what the agent should have done.
4. Suggestions for prompt changes for the agent.
"

# Set this > 0 to limit the number of workspaces to process
count=-1
for unresolved_id in $(jq -r '.unresolved_ids[]' $report_file); do
    workspace=$base_dir/workspace_$unresolved_id
    rm -f $workspace/agent_summary.txt
    id_instruct=$(echo "$instruct" | sed "s/TASK_ID/$unresolved_id/g")
    echo python $proto --workspace $workspace --chat "$id_instruct" --signin -o $workspace/agent_summary.txt
    time python $proto --workspace $workspace --chat "$id_instruct" --signin -o $workspace/agent_summary.txt
    # cat $workspace/agent_summary.txt
    count=$((count - 1))
    if [ $count -eq 0 ]; then
        break
    fi
done

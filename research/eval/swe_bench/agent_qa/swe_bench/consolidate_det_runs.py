import subprocess
from research.eval.swe_bench.agent_qa.swe_bench.stage_results import (
    index_evaluations,
    htmlify_index,
)
from pathlib import Path
import argparse
import glob


def main(det_ids: list[int]):
    print(f"Det ids: {det_ids}")
    str_of_det_ids = "_".join([str(det_id) for det_id in det_ids])
    output_dir = f"gs://gcp-us1-public-html/swebench/consolidated_runs/{str_of_det_ids}"
    ouptut_dir_efs = (
        f"/mnt/efs/augment/public_html/swebench/consolidated_runs/{str_of_det_ids}"
    )
    Path(ouptut_dir_efs).mkdir(parents=True, exist_ok=True)
    print(f"Output dir: {output_dir}")

    if args.regenerate_html:
        print("Removing existing index...")
        path = Path(ouptut_dir_efs) / "index.html"
        if path.exists():
            path.unlink()
        print("Regenerating HTML index...")
        all_ids = index_evaluations([output_dir])
        htmlify_index(all_ids, output_dir)
        print("Done regenerating HTML index.")
        return

    def stage_unconsolidated_run_artifacts(det_id: int, output_dir: str):
        """Stages unconsolidated run artifacts from a determined experiment to a specified output directory."""
        input_dir = (
            f"gs://gcp-us1-public-html/swebench/determined-unconsolidated/{det_id}"
        )

        cmd = ["gcloud", "storage", "cp", "-r", f"{input_dir}/workspace*", output_dir]
        try:
            subprocess.run(cmd, check=True, capture_output=True)
        except subprocess.CalledProcessError as e:
            print(f"Failed to copy workspace for det_id: {det_id}")
            print(e.stderr.decode())

    print("Staging unconsolidated run artifacts...")
    for det_id in det_ids:
        print(f"Staging artifacts for det_id: {det_id}")
        stage_unconsolidated_run_artifacts(det_id, output_dir)

    # Consolidate data
    print("Consolidating data...")
    cmd = [
        "python",
        f"{Path(__file__).parent}/swe_bench.py",
        "merge",
        *[
            f"/mnt/efs/augment/public_html/swebench/determined-unconsolidated/{det_id}/Augment_Agent.json"
            for det_id in det_ids
        ],
        "--output-file",
        f"{ouptut_dir_efs}/Augment_Agent.json",
    ]
    subprocess.run(cmd, check=True)

    # HTML-ify data
    print("HTML-ifying data...")
    all_ids = index_evaluations([output_dir])
    htmlify_index(all_ids, output_dir)

    # HTMLify each agent logs
    print("HTML-ifying agent logs...")
    for path in glob.glob(
        f"/mnt/efs/augment/public_html/swebench/consolidated_runs/{str_of_det_ids}/workspace*/agent_log.pickle"
    ):
        cmd = [
            "python",
            f"{Path(__file__).parent}/convert_agent_log_to_html.py",
            "--pickle_log_file",
            path,
        ]
        subprocess.run(cmd, check=True)

    print(
        f"All done! View at https://webserver.gcp-us1.r.augmentcode.com/swebench/consolidated_runs/{str_of_det_ids}/index.html"
    )


if __name__ == "__main__":
    # main([
    #    10145,
    #    10146,
    # ])

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "det_ids",
        type=int,
        nargs="+",
        help="List of Determined experiment IDs to consolidate.",
    )
    parser.add_argument(
        "--regenerate-html",
        action="store_true",
        help="Regenerate the HTML index even if it already exists.",
    )
    args = parser.parse_args()

    main(args.det_ids)

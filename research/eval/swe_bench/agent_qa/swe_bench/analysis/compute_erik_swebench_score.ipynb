{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from glob import glob\n", "import json\n", "\n", "paths = glob(\n", "    \"/home/<USER>/experiments/evaluation/verified/20250224_tools_claude-3-7-sonnet/logs/*/report.json\"\n", ")\n", "\n", "pass_ct = 0\n", "total_ct = 0\n", "for path in paths:\n", "    with open(path, \"r\") as f:\n", "        data = json.load(f)\n", "    is_resolved = list(data.values())[0][\"resolved\"]\n", "    total_ct += 1\n", "    if is_resolved:\n", "        pass_ct += 1\n", "assert total_ct == 500, total_ct\n", "print(\"Pass rate: \", pass_ct / total_ct)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
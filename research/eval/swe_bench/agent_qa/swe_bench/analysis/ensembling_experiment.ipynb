{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["run_dirs_old = [\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/\",\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11601_11602_11603_11604_11605_11606_11607_11608_11609_11610/\",\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11671_11672_11673_11674_11675_11676_11677_11678_11679_11680/\",\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11754_11755_11756_11757_11758_11759_11760_11761_11762_11763/\",\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11768_11769_11770_11771_11772_11773_11774_11776_11777_11778/\",\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11794_11795_11796_11797_11798_11799_11800_11801_11802_11803/\",\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11882_11884_11886_11888_11890_11892_11894_11896_11899_11901/\",\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11883_11885_11887_11889_11891_11893_11895_11897_11898_11900/\",\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11906_11907_11908_11909_11910_11911_11912_11913_11914_11915/\",\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/12264_12266_12268_12270_12272_12273_12274_12275_12276_12277/\",\n", "]\n", "\n", "run_dirs = [\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11794_11795_11796_11797_11798_11799_11800_11801_11802_11803/\",\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/12264_12266_12268_12270_12272_12273_12274_12275_12276_12277/\",\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/12349_12350_12351_12352_12353_12354_12355_12356_12357_12358/\",\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/12359_12360_12361_12362_12363_12364_12365_12366_12367_12368/\",\n", "    # less good oens but still good\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/12303_12304_12305_12306_12307_12308_12309_12310_12311_12312/\",\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11671_11672_11673_11674_11675_11676_11677_11678_11679_11680/\",\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/\",\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11883_11885_11887_11889_11891_11893_11895_11897_11898_11900/\",\n", "]\n", "\n", "\n", "def get_consolidated_run_id(dir: str) -> str:\n", "    return dir.split(\"consolidated_runs/\")[1].split(\"/\")[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from glob import glob\n", "import json\n", "from functools import lru_cache\n", "import pickle\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "from research.eval.swe_bench.agent_qa.swe_bench import (\n", "    instruction_prompt_sequential_thinking,\n", ")\n", "\n", "\n", "@lru_cache(maxsize=100)\n", "def get_example_eval_dirs(run_dir):\n", "    return glob(run_dir + \"workspace_*/logs/run_evaluation/swe_work/Augment_Agent/*/\")\n", "\n", "\n", "def get_example_pickle(dir):\n", "    path = Path(dir).parent.parent.parent.parent.parent / \"agent_log.pickle\"\n", "    with open(path, \"rb\") as f:\n", "        data = pickle.load(f)\n", "    return data\n", "\n", "\n", "def get_answer_explanation(data):\n", "    complete_call = None\n", "    for entry in data:\n", "        if (\n", "            hasattr(entry, \"tool\")\n", "            and hasattr(entry.tool, \"name\")\n", "            and entry.tool.name == \"complete\"\n", "        ):\n", "            complete_call = entry\n", "\n", "    return complete_call.tool_input[\"answer\"]\n", "\n", "\n", "instance_id_to_run_results = {}\n", "for run_dir in tqdm(run_dirs):\n", "    run_id = get_consolidated_run_id(run_dir)\n", "    for example_eval_dir in get_example_eval_dirs(run_dir):\n", "        patch_diff_path = example_eval_dir + \"patch.diff\"\n", "        report_path = example_eval_dir + \"report.json\"\n", "\n", "        try:\n", "            example_pickle = get_example_pickle(example_eval_dir)\n", "            answer_explanation = get_answer_explanation(example_pickle)\n", "        except Exception as e:\n", "            print(f\"Failed to load answer explanation for {example_eval_dir}: {e}\")\n", "            answer_explanation = None\n", "\n", "        with open(patch_diff_path, \"r\") as f:\n", "            patch_diff = f.read()\n", "        try:\n", "            with open(report_path, \"r\") as f:\n", "                report = json.load(f)\n", "            instance_id, eval_res = list(report.items())[0]\n", "        except FileNotFoundError:\n", "            instance_id = str(Path(report_path).parent.name)\n", "            eval_res = {\"resolved\": False}\n", "        is_success = eval_res[\"resolved\"]\n", "\n", "        issue_path = (\n", "            Path(example_eval_dir).parent.parent.parent.parent.parent / \"issue.json\"\n", "        )\n", "        with open(issue_path, \"r\") as f:\n", "            issue = json.load(f)\n", "        problem_statement = issue[\"problem_statement\"]\n", "\n", "        location = f\"/run/determined/workdir/swe_work/workspace_{instance_id}\"\n", "        instruction = instruction_prompt_sequential_thinking.PROMPT.format(\n", "            location=location,\n", "            pr_description=problem_statement,\n", "            step7_str=\"Run select tests from the repo to make sure that your fix doesn't break anything else.\",\n", "        )\n", "\n", "        if instance_id not in instance_id_to_run_results:\n", "            instance_id_to_run_results[instance_id] = {}\n", "        instance_id_to_run_results[instance_id][run_id] = {\n", "            \"patch_diff\": patch_diff,\n", "            \"is_success\": is_success,\n", "            \"instruction\": instruction,\n", "            \"answer_explanation\": answer_explanation,\n", "        }\n", "\n", "\n", "dataset_save_dir = \"/mnt/efs/augment/user/colin/swe_bench_ensembling_experiment6-add_answer_explanations.pickle\"\n", "with open(dataset_save_dir, \"wb\") as f:\n", "    pickle.dump(instance_id_to_run_results, f)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "# dataset_save_dir = \"/mnt/efs/augment/user/colin/swe_bench_ensembling_experiment3.pickle\"\n", "# dataset_save_dir = \"/mnt/efs/augment/user/colin/swe_bench_ensembling_experiment5.pickle\"\n", "dataset_save_dir = \"/mnt/efs/augment/user/colin/swe_bench_ensembling_experiment6-add_answer_explanations.pickle\"\n", "with open(dataset_save_dir, \"rb\") as f:\n", "    instance_id_to_run_results = pickle.load(f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Summary stats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(instance_id_to_run_results))\n", "print(list(instance_id_to_run_results[\"django__django-11099\"].values())[0].keys())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "# separate success rates\n", "\n", "num_succeses = {get_consolidated_run_id(dir): [] for dir in run_dirs}\n", "success_percs = []\n", "for instance_id, res_dct in instance_id_to_run_results.items():\n", "    for i, (run_id, run_data) in enumerate(res_dct.items()):\n", "        num_succeses[run_id].append(run_data[\"is_success\"])\n", "for i, (dir, successes) in enumerate(num_succeses.items()):\n", "    success_percs.append(sum(successes) / 500)\n", "    print(f\"Run {i}: {sum(successes) / 500 } (dir: {dir})\")\n", "\n", "print(\"Mean score: \", np.mean(success_percs))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pass rate if we get best of n for each instance\n", "num_successes = 0\n", "for k, res_ls in instance_id_to_run_results.items():\n", "    if any(res[\"is_success\"] for res in res_ls.values()):\n", "        num_successes += 1\n", "print(num_successes / len(instance_id_to_run_results))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# compute avg pair-wise num of differing examples\n", "import numpy as np\n", "\n", "run_ids = [get_consolidated_run_id(dir) for dir in run_dirs]\n", "num_differing_ls = []\n", "for run_id_1 in run_ids:\n", "    for run_id_2 in run_ids:\n", "        if run_id_1 == run_id_2:\n", "            continue\n", "        # num overlapping successes\n", "        num_differing = 0\n", "        found_good_run = True\n", "        for k, res_dct in instance_id_to_run_results.items():\n", "            res_1 = res_dct.get(run_id_1, {\"is_success\": False})[\"is_success\"]\n", "            res_2 = res_dct.get(run_id_2, {\"is_success\": False})[\"is_success\"]\n", "            if (res_1 and not res_2) or (not res_1 and res_2):\n", "                num_differing += 1\n", "        num_differing_ls.append(num_differing)\n", "print(num_differing_ls)\n", "\n", "print(\"Avg num differing: \", np.mean(num_differing_ls))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# compute number of examples that succeed for at least N runs\n", "instance_id_to_success_ct = {}\n", "for k, res_dct in instance_id_to_run_results.items():\n", "    success_ct = 0\n", "    for run_id, run_data in res_dct.items():\n", "        if run_data[\"is_success\"]:\n", "            success_ct += 1\n", "    instance_id_to_success_ct[k] = success_ct\n", "\n", "# for i = 1...len(run_dirs), print the number of instances that have at least i successes\n", "for i in range(1, len(run_dirs) + 1):\n", "    print(\n", "        f\"Percentage of instances with at least {i} successes: {sum(v >= i for v in instance_id_to_success_ct.values())/len(instance_id_to_success_ct)}\"\n", "    )"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.llm_client import AnthropicDirectClient\n", "from research.llm_apis.llm_client import OpenAIDirectClient\n", "from research.llm_apis.llm_client import TextPrompt\n", "from tqdm import tqdm\n", "from functools import partial\n", "from concurrent.futures import ThreadPoolExecutor\n", "import concurrent.futures\n", "import random\n", "\n", "anthropic_client = AnthropicDirectClient(\n", "    model_name=\"claude-3-7-sonnet-20250219\",\n", "    max_retries=50,\n", "    use_low_qos_server=True,\n", ")\n", "\n", "openai_client = OpenAIDirectClient(\n", "    model_name=\"o1-2024-12-17\",\n", "    max_retries=50,\n", "    cot_model=True,\n", ")\n", "\n", "\n", "def compute_success_output(\n", "    res_ls,\n", "    prompt_generator,\n", "    client,\n", "    verbose=False,\n", "    shuffle=False,\n", "    use_answer_explanations=False,\n", "):\n", "    instruction = list(res_ls.values())[0][\"instruction\"]\n", "    diffs = [res[\"patch_diff\"] for res in res_ls.values()]\n", "    if use_answer_explanations:\n", "        answer_explanations = [res[\"answer_explanation\"] for res in res_ls.values()]\n", "        assert len(answer_explanations) == len(diffs)\n", "    else:\n", "        answer_explanations = None\n", "    if shuffle:\n", "        random.shuffle(diffs)\n", "\n", "    prompt = prompt_generator(instruction, diffs, answer_explanations)\n", "\n", "    messages = [[TextPrompt(text=prompt)]]\n", "    for i in range(3):\n", "        try:\n", "            output = client.generate(messages, max_tokens=16384, temperature=0.0)\n", "            break\n", "        except ValueError as e:\n", "            print(\"Failed to generate response, retrying.\")\n", "            if i == 2:\n", "                raise e\n", "\n", "    try:\n", "        solution_index = (\n", "            int(\n", "                output[0][0]\n", "                .text.split(\"<solution_index>\")[-1]\n", "                .split(\"</solution_index>\", 1)[0]\n", "            )\n", "            - 1\n", "        )\n", "    except (IndexError, ValueError) as e:\n", "        print(\n", "            f\"Failed to parse solution index from with exception ({e}).  Output: {'-' * 30}\\n\\n {output[0][0].text}. {'-' * 30}\\n\\n Just picking the first solution.\"\n", "        )\n", "        solution_index = -1\n", "\n", "    solution_res = (\n", "        list(res_ls.values())[solution_index]\n", "        if solution_index >= 0\n", "        else list(res_ls.values())[0]\n", "    )\n", "    is_success = solution_res[\"is_success\"]\n", "\n", "    if verbose:\n", "        print(\"PROMPT\")\n", "        print(prompt)\n", "        print(\"RESPONSE\")\n", "        print(output[0][0].text)\n", "\n", "    return solution_index, is_success"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Ensembling experiments"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Best-first"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["USE_ANSWER_EXPLANATIONS = True\n", "\n", "\n", "def get_llm_judge_prompt_anthropic_bestofn(\n", "    instruction: str, diffs: list[str], answer_explanations: list[str] | None = None\n", ") -> str:\n", "    prompt = \"\"\n", "\n", "    prompt += f\"\"\"\\\n", "\n", "I am a software engineer. I am working on a task in my codebase. Here is the task:\n", "\n", "<instruction>\n", "{instruction}\n", "</instruction>\n", "\n", "I have generated {len(diffs)} different solutions to this task. Please evaluate each solution and respond with the number of the solution that best solves the task. \n", "There is always at least 1 correct solution, so responding that there is no correct solution is not an option. Here are the solutions:\n", "\n", "\"\"\"\n", "\n", "    for i, diff in enumerate(diffs):\n", "        prompt += f\"\"\"\\\n", "\n", "<candidate_solution index={i+1}>\n", "{diff}\n", "</candidate_solution index={i+1}>\n", "\"\"\"\n", "\n", "        if answer_explanations is not None:\n", "            assert len(answer_explanations) == len(diffs)\n", "            prompt += f\"\"\"\\\n", "\n", "<candidate_explanation index={i+1}>\n", "{answer_explanations[i]}\n", "</candidate_explanation index={i+1}>\n", "\"\"\"\n", "\n", "    prompt += \"\"\"\\\n", "\n", "Follow these steps to pick the best solution:\n", "1. Analyze each solution and understand what it does.\n", "2. Describe your thinking process on how to pick the best solution.\n", "3. Compare how the different candidate solutions address the task.\n", "4. Pick the best solution. Explicitly write the number of the best solution inside XML tags <solution_index>...</solution_index>. Do not put anything inside the XML tags other than the number.\n", "\n", "\"\"\"\n", "\n", "    return prompt\n", "\n", "\n", "def process_instance_bestofn_vote(item):\n", "    k, res_ls = item\n", "    shuffle = False\n", "    for i in range(3):\n", "        try:\n", "            solution_index, is_success = compute_success_output(\n", "                res_ls,\n", "                get_llm_judge_prompt_anthropic_bestofn,\n", "                openai_client,\n", "                shuffle=shuffle,\n", "                use_answer_explanations=USE_ANSWER_EXPLANATIONS,\n", "            )\n", "            return k, solution_index, is_success\n", "        except Exception:\n", "            print(\"Failed to generate response, retrying.\")\n", "            shuffle = True\n", "    raise ValueError(\"Failed to generate response after 3 retries\")\n", "\n", "\n", "results = []\n", "example_id_to_soln_run_idx = {}\n", "with ThreadPoolExecutor(max_workers=32) as executor:\n", "    # Submit all tasks\n", "    futures = [\n", "        executor.submit(process_instance_bestofn_vote, item)\n", "        for item in list(instance_id_to_run_results.items())[:1]\n", "    ]\n", "\n", "    # Process results as they complete\n", "    for future in tqdm(concurrent.futures.as_completed(futures), total=len(futures)):\n", "        k, solution_index, is_success = future.result()\n", "        results.append(is_success)\n", "        example_id_to_soln_run_idx[k] = solution_index\n", "\n", "# Count successes\n", "success_ct = sum(results)\n", "print(success_ct / 500)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Majority voting"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["USE_ANSWER_EXPLANATIONS = True\n", "\n", "\n", "def get_llm_judge_prompt_anthropic_majority_vote(\n", "    instruction: str, diffs: list[str], answer_explanations: list[str] | None = None\n", ") -> str:\n", "    prompt = \"\"\n", "\n", "    prompt += f\"\"\"\\\n", "\n", "I am a software engineer. I am working on a task in my codebase. Here is the task:\n", "\n", "<instruction>\n", "{instruction}\n", "</instruction>\n", "\n", "I have generated {len(diffs)} different solutions to this task. Please evaluate each solution below. Each solution is in a <candidate_solution> tag. Along,\n", "with each solution, there is a <candidate_explanation> tag that provides a justification for the solution, along with some additional context about it.\n", "\n", "\"\"\"\n", "\n", "    for i, diff in enumerate(diffs):\n", "        prompt += f\"\"\"\\\n", "\n", "<candidate_solution index={i+1}>\n", "{diff}\n", "</candidate_solution index={i+1}>\n", "\"\"\"\n", "        if answer_explanations is not None:\n", "            assert len(answer_explanations) == len(diffs)\n", "            prompt += f\"\"\"\\\n", "\n", "<candidate_explanation index={i+1}>\n", "{answer_explanations[i]}\n", "</candidate_explanation index={i+1}>\n", "\"\"\"\n", "\n", "    prompt += \"\"\"\\\n", "\n", "Follow these steps to pick the best solution:\n", "1. Analyze each solution, along with its explanation, and understand what it does.\n", "2. <PERSON><PERSON><PERSON> and contrast the different approaches to the solution. Evaluate the pros and cons of each solution.\n", "3. Pick the majority vote solution. Explicitly write the number of one example of the majority vote solution inside XML tags <solution_index>...</solution_index>. Do not put anything inside the XML tags other than the number.\n", "\n", "\"\"\"\n", "\n", "    return prompt\n", "\n", "\n", "def process_instance_majority_vote(item):\n", "    k, res_ls = item\n", "    solution_index, is_success = compute_success_output(\n", "        res_ls,\n", "        get_llm_judge_prompt_anthropic_majority_vote,\n", "        openai_client,\n", "        use_answer_explanations=USE_ANSWER_EXPLANATIONS,\n", "    )\n", "    return k, solution_index, is_success\n", "\n", "\n", "results = []\n", "example_id_to_soln_run_idx = {}\n", "example_id_to_is_success = {}\n", "with ThreadPoolExecutor(max_workers=32) as executor:\n", "    # Submit all tasks\n", "    futures = [\n", "        executor.submit(process_instance_majority_vote, item)\n", "        for item in list(instance_id_to_run_results.items())\n", "    ]\n", "\n", "    # Process results as they complete\n", "    for future in tqdm(concurrent.futures.as_completed(futures), total=len(futures)):\n", "        k, solution_index, is_success = future.result()\n", "        results.append(is_success)\n", "        example_id_to_soln_run_idx[k] = solution_index\n", "        example_id_to_is_success[k] = is_success\n", "\n", "# Count successes\n", "success_ct = sum(results)\n", "print(success_ct / 500)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Count successes\n", "success_ct = sum(results)\n", "print(success_ct / 500)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### analyze results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["success_rate_by_repo = {}\n", "example_ct_by_repo = {}\n", "for k, is_success in example_id_to_is_success.items():\n", "    repo = \"-\".join(k.split(\"-\")[:-1]).replace(\"__\", \"/\")\n", "    success_rate_by_repo[repo] = success_rate_by_repo.get(repo, 0) + int(is_success)\n", "    example_ct_by_repo[repo] = example_ct_by_repo.get(repo, 0) + 1\n", "for repo, success_ct in sorted(success_rate_by_repo.items(), key=lambda x: x[0]):\n", "    print(f\"{repo}: {success_ct} / {example_ct_by_repo[repo]}\")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["with open(\n", "    \"/mnt/efs/augment/user/colin/swe_bench_ensembling_experiment/mar15perc_results.json\",\n", "    \"w\",\n", ") as f:\n", "    output = {\n", "        \"run_dirs\": run_dirs,\n", "        \"example_id_to_soln_run_idx\": example_id_to_soln_run_idx,\n", "        \"success_rate\": success_ct / 500,\n", "    }\n", "    json.dump(output, f, indent=2)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
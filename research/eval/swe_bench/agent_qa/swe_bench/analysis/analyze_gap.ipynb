{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["str replace issue\n", "\n", "grep -r \"Invalid \\`view_range\\`\" /mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/*/agent_stdout.txt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Look at failure gap in django\n", "\n", "curl -o /mnt/efs/augment/user/colin/20241221_codestory_midwit_claude-3-5-sonnet_swe-search_results.json https://raw.githubusercontent.com/SWE-bench/experiments/main/evaluation/verified/20241221_codestory_midwit_claude-3-5-sonnet_swe-search/results/results.json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "codestory_results = \"/mnt/efs/augment/user/colin/20241221_codestory_midwit_claude-3-5-sonnet_swe-search_results.json\"\n", "\n", "with open(codestory_results, \"r\") as f:\n", "    codestory_results = json.load(f)\n", "print(codestory_results.keys())\n", "codestory_resolved_keys = codestory_results[\"resolved\"]\n", "print(codestory_resolved_keys[0])\n", "print(len(codestory_resolved_keys))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from glob import glob\n", "\n", "our_results = \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/Augment_Agent.json\"\n", "with open(our_results, \"r\") as f:\n", "    our_results = json.load(f)\n", "print(our_results.keys())\n", "our_resolved_keys = our_results[\"resolved_ids\"]\n", "print(our_resolved_keys[0])\n", "\n", "our_id_to_diff = {}\n", "for dir in glob(\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/workspace_*/\"\n", "):\n", "    try:\n", "        id = dir.split(\"/\")[-2].replace(\"workspace_\", \"\")\n", "        with open(dir + \"predictions.json\", \"rb\") as f:\n", "            predictions = json.load(f)\n", "        our_id_to_diff[id] = predictions[0][\"model_patch\"]\n", "    except FileNotFoundError as e:\n", "        print(f\"Error loading {dir}: {e}\")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo</th>\n", "      <th>instance_id</th>\n", "      <th>base_commit</th>\n", "      <th>patch</th>\n", "      <th>test_patch</th>\n", "      <th>problem_statement</th>\n", "      <th>hints_text</th>\n", "      <th>created_at</th>\n", "      <th>version</th>\n", "      <th>FAIL_TO_PASS</th>\n", "      <th>PASS_TO_PASS</th>\n", "      <th>environment_setup_commit</th>\n", "      <th>difficulty</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>astropy/astropy</td>\n", "      <td>astropy__astropy-12907</td>\n", "      <td>d16bfe05a744909de4b27f5875fe0d4ed41ce607</td>\n", "      <td>diff --git a/astropy/modeling/separable.py b/a...</td>\n", "      <td>diff --git a/astropy/modeling/tests/test_separ...</td>\n", "      <td>Modeling's `separability_matrix` does not comp...</td>\n", "      <td></td>\n", "      <td>2022-03-03T15:14:54Z</td>\n", "      <td>4.3</td>\n", "      <td>[\"astropy/modeling/tests/test_separable.py::te...</td>\n", "      <td>[\"astropy/modeling/tests/test_separable.py::te...</td>\n", "      <td>298ccb478e6bf092953bca67a3d29dc6c35f6752</td>\n", "      <td>15 min - 1 hour</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>astropy/astropy</td>\n", "      <td>astropy__astropy-13033</td>\n", "      <td>298ccb478e6bf092953bca67a3d29dc6c35f6752</td>\n", "      <td>diff --git a/astropy/timeseries/core.py b/astr...</td>\n", "      <td>diff --git a/astropy/timeseries/tests/test_sam...</td>\n", "      <td>TimeSeries: misleading exception when required...</td>\n", "      <td>The relevant code that produces the misleading...</td>\n", "      <td>2022-03-31T23:28:27Z</td>\n", "      <td>4.3</td>\n", "      <td>[\"astropy/timeseries/tests/test_sampled.py::te...</td>\n", "      <td>[\"astropy/timeseries/tests/test_sampled.py::te...</td>\n", "      <td>298ccb478e6bf092953bca67a3d29dc6c35f6752</td>\n", "      <td>15 min - 1 hour</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>astropy/astropy</td>\n", "      <td>astropy__astropy-13236</td>\n", "      <td>6ed769d58d89380ebaa1ef52b300691eefda8928</td>\n", "      <td>diff --git a/astropy/table/table.py b/astropy/...</td>\n", "      <td>diff --git a/astropy/table/tests/test_mixin.py...</td>\n", "      <td>Consider removing auto-transform of structured...</td>\n", "      <td>@mhvk - I'm happy to do this PR if you think i...</td>\n", "      <td>2022-05-09T14:16:30Z</td>\n", "      <td>5.0</td>\n", "      <td>[\"astropy/table/tests/test_mixin.py::test_ndar...</td>\n", "      <td>[\"astropy/table/tests/test_mixin.py::test_attr...</td>\n", "      <td>cdf311e0714e611d48b0a31eb1f0e2cbffab7f23</td>\n", "      <td>15 min - 1 hour</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>astropy/astropy</td>\n", "      <td>astropy__astropy-13398</td>\n", "      <td>6500928dc0e57be8f06d1162eacc3ba5e2eff692</td>\n", "      <td>diff --git a/astropy/coordinates/builtin_frame...</td>\n", "      <td>diff --git a/astropy/coordinates/tests/test_in...</td>\n", "      <td>A direct approach to ITRS to Observed transfor...</td>\n", "      <td>cc @StuartLittlefair, @adrn, @eteq, @eerovaher...</td>\n", "      <td>2022-06-24T15:22:11Z</td>\n", "      <td>5.0</td>\n", "      <td>[\"astropy/coordinates/tests/test_intermediate_...</td>\n", "      <td>[\"astropy/coordinates/tests/test_intermediate_...</td>\n", "      <td>cdf311e0714e611d48b0a31eb1f0e2cbffab7f23</td>\n", "      <td>1-4 hours</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>astropy/astropy</td>\n", "      <td>astropy__astropy-13453</td>\n", "      <td>19cc80471739bcb67b7e8099246b391c355023ee</td>\n", "      <td>diff --git a/astropy/io/ascii/html.py b/astrop...</td>\n", "      <td>diff --git a/astropy/io/ascii/tests/test_html....</td>\n", "      <td>ASCII table output to HTML does not support su...</td>\n", "      <td>Welcome to Astropy 👋 and thank you for your fi...</td>\n", "      <td>2022-07-14T10:04:40Z</td>\n", "      <td>5.0</td>\n", "      <td>[\"astropy/io/ascii/tests/test_html.py::test_wr...</td>\n", "      <td>[\"astropy/io/ascii/tests/test_html.py::test_li...</td>\n", "      <td>cdf311e0714e611d48b0a31eb1f0e2cbffab7f23</td>\n", "      <td>15 min - 1 hour</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>495</th>\n", "      <td>sympy/sympy</td>\n", "      <td>sympy__sympy-24213</td>\n", "      <td>e8c22f6eac7314be8d92590bfff92ced79ee03e2</td>\n", "      <td>diff --git a/sympy/physics/units/unitsystem.py...</td>\n", "      <td>diff --git a/sympy/physics/units/tests/test_qu...</td>\n", "      <td>collect_factor_and_dimension does not detect e...</td>\n", "      <td></td>\n", "      <td>2022-11-03T14:00:09Z</td>\n", "      <td>1.12</td>\n", "      <td>[\"test_issue_24211\"]</td>\n", "      <td>[\"test_str_repr\", \"test_eq\", \"test_convert_to\"...</td>\n", "      <td>c6cb7c5602fa48034ab1bd43c2347a7e8488f12e</td>\n", "      <td>15 min - 1 hour</td>\n", "    </tr>\n", "    <tr>\n", "      <th>496</th>\n", "      <td>sympy/sympy</td>\n", "      <td>sympy__sympy-24443</td>\n", "      <td>809c53c077485ca48a206cee78340389cb83b7f1</td>\n", "      <td>diff --git a/sympy/combinatorics/homomorphisms...</td>\n", "      <td>diff --git a/sympy/combinatorics/tests/test_ho...</td>\n", "      <td>`_check_homomorphism` is broken on Permutation...</td>\n", "      <td></td>\n", "      <td>2022-12-30T14:43:19Z</td>\n", "      <td>1.12</td>\n", "      <td>[\"test_homomorphism\"]</td>\n", "      <td>[\"test_isomorphisms\"]</td>\n", "      <td>c6cb7c5602fa48034ab1bd43c2347a7e8488f12e</td>\n", "      <td>15 min - 1 hour</td>\n", "    </tr>\n", "    <tr>\n", "      <th>497</th>\n", "      <td>sympy/sympy</td>\n", "      <td>sympy__sympy-24539</td>\n", "      <td>193e3825645d93c73e31cdceb6d742cc6919624d</td>\n", "      <td>diff --git a/sympy/polys/rings.py b/sympy/poly...</td>\n", "      <td>diff --git a/sympy/polys/tests/test_rings.py b...</td>\n", "      <td>`PolyElement.as_expr()` not accepting symbols\\...</td>\n", "      <td></td>\n", "      <td>2023-01-17T17:26:42Z</td>\n", "      <td>1.12</td>\n", "      <td>[\"test_PolyElement_as_expr\"]</td>\n", "      <td>[\"test_PolyRing___init__\", \"test_PolyRing___ha...</td>\n", "      <td>c6cb7c5602fa48034ab1bd43c2347a7e8488f12e</td>\n", "      <td>&lt;15 min fix</td>\n", "    </tr>\n", "    <tr>\n", "      <th>498</th>\n", "      <td>sympy/sympy</td>\n", "      <td>sympy__sympy-24562</td>\n", "      <td>b1cb676cf92dd1a48365b731979833375b188bf2</td>\n", "      <td>diff --git a/sympy/core/numbers.py b/sympy/cor...</td>\n", "      <td>diff --git a/sympy/core/tests/test_numbers.py ...</td>\n", "      <td>Rational calc value error\\npython 3.11, sympy ...</td>\n", "      <td>This should probably raise an error. The expec...</td>\n", "      <td>2023-01-21T12:06:36Z</td>\n", "      <td>1.12</td>\n", "      <td>[\"test_issue_24543\"]</td>\n", "      <td>[\"test_seterr\", \"test_mod\", \"test_divmod\", \"te...</td>\n", "      <td>c6cb7c5602fa48034ab1bd43c2347a7e8488f12e</td>\n", "      <td>&lt;15 min fix</td>\n", "    </tr>\n", "    <tr>\n", "      <th>499</th>\n", "      <td>sympy/sympy</td>\n", "      <td>sympy__sympy-24661</td>\n", "      <td>a36caf5c74fe654cedc488e8a8a05fad388f8406</td>\n", "      <td>diff --git a/sympy/parsing/sympy_parser.py b/s...</td>\n", "      <td>diff --git a/sympy/parsing/tests/test_sympy_pa...</td>\n", "      <td>The evaluate=False parameter to `parse_expr` i...</td>\n", "      <td>Actually this problem is not only for this but...</td>\n", "      <td>2023-02-05T19:15:22Z</td>\n", "      <td>1.12</td>\n", "      <td>[\"test_issue_24288\"]</td>\n", "      <td>[\"test_sympy_parser\", \"test_rationalize\", \"tes...</td>\n", "      <td>c6cb7c5602fa48034ab1bd43c2347a7e8488f12e</td>\n", "      <td>15 min - 1 hour</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>500 rows × 13 columns</p>\n", "</div>"], "text/plain": ["                repo             instance_id  \\\n", "0    astropy/astropy  astropy__astropy-12907   \n", "1    astropy/astropy  astropy__astropy-13033   \n", "2    astropy/astropy  astropy__astropy-13236   \n", "3    astropy/astropy  astropy__astropy-13398   \n", "4    astropy/astropy  astropy__astropy-13453   \n", "..               ...                     ...   \n", "495      sympy/sympy      sympy__sympy-24213   \n", "496      sympy/sympy      sympy__sympy-24443   \n", "497      sympy/sympy      sympy__sympy-24539   \n", "498      sympy/sympy      sympy__sympy-24562   \n", "499      sympy/sympy      sympy__sympy-24661   \n", "\n", "                                  base_commit  \\\n", "0    d16bfe05a744909de4b27f5875fe0d4ed41ce607   \n", "1    298ccb478e6bf092953bca67a3d29dc6c35f6752   \n", "2    6ed769d58d89380ebaa1ef52b300691eefda8928   \n", "3    6500928dc0e57be8f06d1162eacc3ba5e2eff692   \n", "4    19cc80471739bcb67b7e8099246b391c355023ee   \n", "..                                        ...   \n", "495  e8c22f6eac7314be8d92590bfff92ced79ee03e2   \n", "496  809c53c077485ca48a206cee78340389cb83b7f1   \n", "497  193e3825645d93c73e31cdceb6d742cc6919624d   \n", "498  b1cb676cf92dd1a48365b731979833375b188bf2   \n", "499  a36caf5c74fe654cedc488e8a8a05fad388f8406   \n", "\n", "                                                 patch  \\\n", "0    diff --git a/astropy/modeling/separable.py b/a...   \n", "1    diff --git a/astropy/timeseries/core.py b/astr...   \n", "2    diff --git a/astropy/table/table.py b/astropy/...   \n", "3    diff --git a/astropy/coordinates/builtin_frame...   \n", "4    diff --git a/astropy/io/ascii/html.py b/astrop...   \n", "..                                                 ...   \n", "495  diff --git a/sympy/physics/units/unitsystem.py...   \n", "496  diff --git a/sympy/combinatorics/homomorphisms...   \n", "497  diff --git a/sympy/polys/rings.py b/sympy/poly...   \n", "498  diff --git a/sympy/core/numbers.py b/sympy/cor...   \n", "499  diff --git a/sympy/parsing/sympy_parser.py b/s...   \n", "\n", "                                            test_patch  \\\n", "0    diff --git a/astropy/modeling/tests/test_separ...   \n", "1    diff --git a/astropy/timeseries/tests/test_sam...   \n", "2    diff --git a/astropy/table/tests/test_mixin.py...   \n", "3    diff --git a/astropy/coordinates/tests/test_in...   \n", "4    diff --git a/astropy/io/ascii/tests/test_html....   \n", "..                                                 ...   \n", "495  diff --git a/sympy/physics/units/tests/test_qu...   \n", "496  diff --git a/sympy/combinatorics/tests/test_ho...   \n", "497  diff --git a/sympy/polys/tests/test_rings.py b...   \n", "498  diff --git a/sympy/core/tests/test_numbers.py ...   \n", "499  diff --git a/sympy/parsing/tests/test_sympy_pa...   \n", "\n", "                                     problem_statement  \\\n", "0    Modeling's `separability_matrix` does not comp...   \n", "1    TimeSeries: misleading exception when required...   \n", "2    Consider removing auto-transform of structured...   \n", "3    A direct approach to ITRS to Observed transfor...   \n", "4    ASCII table output to HTML does not support su...   \n", "..                                                 ...   \n", "495  collect_factor_and_dimension does not detect e...   \n", "496  `_check_homomorphism` is broken on Permutation...   \n", "497  `PolyElement.as_expr()` not accepting symbols\\...   \n", "498  Rational calc value error\\npython 3.11, sympy ...   \n", "499  The evaluate=False parameter to `parse_expr` i...   \n", "\n", "                                            hints_text            created_at  \\\n", "0                                                       2022-03-03T15:14:54Z   \n", "1    The relevant code that produces the misleading...  2022-03-31T23:28:27Z   \n", "2    @mhvk - I'm happy to do this PR if you think i...  2022-05-09T14:16:30Z   \n", "3    cc @StuartLittlefair, @adrn, @eteq, @eerovaher...  2022-06-24T15:22:11Z   \n", "4    Welcome to Astropy 👋 and thank you for your fi...  2022-07-14T10:04:40Z   \n", "..                                                 ...                   ...   \n", "495                                                     2022-11-03T14:00:09Z   \n", "496                                                     2022-12-30T14:43:19Z   \n", "497                                                     2023-01-17T17:26:42Z   \n", "498  This should probably raise an error. The expec...  2023-01-21T12:06:36Z   \n", "499  Actually this problem is not only for this but...  2023-02-05T19:15:22Z   \n", "\n", "    version                                       FAIL_TO_PASS  \\\n", "0       4.3  [\"astropy/modeling/tests/test_separable.py::te...   \n", "1       4.3  [\"astropy/timeseries/tests/test_sampled.py::te...   \n", "2       5.0  [\"astropy/table/tests/test_mixin.py::test_ndar...   \n", "3       5.0  [\"astropy/coordinates/tests/test_intermediate_...   \n", "4       5.0  [\"astropy/io/ascii/tests/test_html.py::test_wr...   \n", "..      ...                                                ...   \n", "495    1.12                               [\"test_issue_24211\"]   \n", "496    1.12                              [\"test_homomorphism\"]   \n", "497    1.12                       [\"test_PolyElement_as_expr\"]   \n", "498    1.12                               [\"test_issue_24543\"]   \n", "499    1.12                               [\"test_issue_24288\"]   \n", "\n", "                                          PASS_TO_PASS  \\\n", "0    [\"astropy/modeling/tests/test_separable.py::te...   \n", "1    [\"astropy/timeseries/tests/test_sampled.py::te...   \n", "2    [\"astropy/table/tests/test_mixin.py::test_attr...   \n", "3    [\"astropy/coordinates/tests/test_intermediate_...   \n", "4    [\"astropy/io/ascii/tests/test_html.py::test_li...   \n", "..                                                 ...   \n", "495  [\"test_str_repr\", \"test_eq\", \"test_convert_to\"...   \n", "496                              [\"test_isomorphisms\"]   \n", "497  [\"test_PolyRing___init__\", \"test_PolyRing___ha...   \n", "498  [\"test_seterr\", \"test_mod\", \"test_divmod\", \"te...   \n", "499  [\"test_sympy_parser\", \"test_rationalize\", \"tes...   \n", "\n", "                     environment_setup_commit       difficulty  \n", "0    298ccb478e6bf092953bca67a3d29dc6c35f6752  15 min - 1 hour  \n", "1    298ccb478e6bf092953bca67a3d29dc6c35f6752  15 min - 1 hour  \n", "2    cdf311e0714e611d48b0a31eb1f0e2cbffab7f23  15 min - 1 hour  \n", "3    cdf311e0714e611d48b0a31eb1f0e2cbffab7f23        1-4 hours  \n", "4    cdf311e0714e611d48b0a31eb1f0e2cbffab7f23  15 min - 1 hour  \n", "..                                        ...              ...  \n", "495  c6cb7c5602fa48034ab1bd43c2347a7e8488f12e  15 min - 1 hour  \n", "496  c6cb7c5602fa48034ab1bd43c2347a7e8488f12e  15 min - 1 hour  \n", "497  c6cb7c5602fa48034ab1bd43c2347a7e8488f12e      <15 min fix  \n", "498  c6cb7c5602fa48034ab1bd43c2347a7e8488f12e      <15 min fix  \n", "499  c6cb7c5602fa48034ab1bd43c2347a7e8488f12e  15 min - 1 hour  \n", "\n", "[500 rows x 13 columns]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from datasets import load_dataset\n", "\n", "# Login using e.g. `huggingface-cli login` to access this dataset\n", "full_dataset = load_dataset(\"princeton-nlp/SWE-bench_Verified\")[\"test\"].to_pandas()\n", "\n", "full_dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# find examples that pass for codestory but not for us, in django__django\n", "codestory_resolved = set(codestory_resolved_keys)\n", "our_resolved = set(our_resolved_keys)\n", "codestory_resolved_django = set(\n", "    [example for example in codestory_resolved if example.startswith(\"django__django\")]\n", ")\n", "our_resolved_django = set(\n", "    [example for example in our_resolved if example.startswith(\"django__django\")]\n", ")\n", "codestory_only_django = codestory_resolved_django - our_resolved_django\n", "our_only_django = our_resolved_django - codestory_resolved_django\n", "\n", "print(f\"codestory_only_django: {len(codestory_only_django)}\")\n", "\n", "print(f\"-----\\n\\ncodestory_only_django ({len(codestory_only_django)}):\")\n", "for example in codestory_only_django:\n", "    print(example)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from unidiff import PatchSet\n", "from research.core.diff_utils import get_source_path, get_target_path\n", "\n", "# for codestory_only_django, print our diff and the gold diff paths only\n", "num_differing_path_examples = 0\n", "id_to_diffs = {}\n", "for example in codestory_only_django:\n", "    print(f\"-----\\n\\n{example}\")\n", "    our_diff = PatchSet.from_string(our_id_to_diff[example])\n", "    our_diff_paths = [get_source_path(pfile) for pfile in our_diff]\n", "    gold_diff = full_dataset[full_dataset[\"instance_id\"] == example][\"patch\"]\n", "    gold_diff = PatchSet.from_string(gold_diff.values[0])\n", "    gold_diff_paths = [get_source_path(pfile) for pfile in gold_diff]\n", "    print(\"-----\")\n", "    print(f\"our_diff_paths: {our_diff_paths}\")\n", "    print(f\"gold_diff_paths: {gold_diff_paths}\")\n", "\n", "    if set(our_diff_paths) != set(gold_diff_paths):\n", "        num_differing_path_examples += 1\n", "\n", "    id_to_diffs[example] = {\n", "        \"our_diff\": our_diff,\n", "        \"gold_diff\": gold_diff,\n", "    }\n", "\n", "percent_differing_path_examples = num_differing_path_examples / len(\n", "    codestory_only_django\n", ")\n", "print(\n", "    f\"\\n ========= \\npercent_differing_path_examples: {percent_differing_path_examples}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diffs = id_to_diffs[\"django__django-12262\"]\n", "print(\"OUR DIFF\")\n", "print(diffs[\"our_diff\"])\n", "print(\"GOLD DIFF\")\n", "print(diffs[\"gold_diff\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["https://webserver.gcp-us1.r.augmentcode.com/swebench/consolidated_runs/11601_11602_11603_11604_11605_11606_11607_11608_11609_11610/workspace_sphinx-doc__sphinx-8548/prompt_budgeting_stats.txt"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "path = \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/workspace_django__django-12262/agent_log.pickle\"\n", "\n", "with open(path, \"rb\") as f:\n", "    data = pickle.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print out all unique class names in data\n", "all_class_names = set()\n", "for item in data:\n", "    all_class_names.add(item.__class__.__name__)\n", "\n", "print(all_class_names)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# filter for logged tool calls\n", "tool_calls = [item for item in data if item.__class__.__name__ == \"LoggedToolCall\"]\n", "print(f\"num tool calls: {len(tool_calls)}\")\n", "\n", "# print out all unique tool names\n", "all_tool_names = set()\n", "for tool_call in tool_calls:\n", "    all_tool_names.add(tool_call.tool.name)\n", "\n", "print(all_tool_names)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print out longest tool\n", "import json\n", "\n", "for i, call in enumerate(tool_calls):\n", "    tool_call_len = len(json.dumps(call.tool_input))\n", "    tool_name = call.tool.name\n", "    print(f\"{i}: {tool_name} ({tool_call_len}) (fields: {call.tool_input.keys()})\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "path = \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11436_11437_11438_11439_11440_11441_11442_11443_11444_11445/workspace_django__django-7530/agent_log.pickle\"\n", "with open(path, \"rb\") as f:\n", "    data = pickle.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k, v in data[-3].__dict__.items():\n", "    print(f\"KEY: {k}\\n\")\n", "    if isinstance(v, list):\n", "        for sub_v in v:\n", "            print(f\"  - {sub_v}\\n\")\n", "    else:\n", "        print(f\"  {v}\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# command to run to detect failures\n", "grep -r \"Invalid tool input:\" /mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/*/agent_log.txt"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
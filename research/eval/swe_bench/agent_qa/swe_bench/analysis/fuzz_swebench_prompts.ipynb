{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from datasets import load_dataset\n", "\n", "# Login using e.g. `huggingface-cli login` to access this dataset\n", "ds = load_dataset(\"princeton-nlp/SWE-bench_Verified\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.llm_client import AnthropicDirectClient\n", "\n", "anthropic_client = AnthropicDirectClient(\n", "    model_name=\"claude-3-7-sonnet-20250219\",\n", "    max_retries=50,\n", "    use_low_qos_server=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print an instruction\n", "\n", "print(ds[\"test\"].to_pandas()[\"problem_statement\"].iloc[0])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.llm_client import TextPrompt\n", "\n", "fuzzing_prompt = \"\"\"\n", "\n", "Here is a PR description:\n", "\n", "<pr_description>\n", "{pr_description}\n", "</pr_description>\n", "\n", "It is written very explicitly. As you know, this is not a very realistic writing style for most software engineers.\n", "Most software engineers, including very good ones, write in a more natural language style. They may not have symbol names. \n", "They may not have code snippets. \n", "\n", "Please rewrite the PR description to be more realistic. Output it in a <realistic_pr_description> tag. \n", "\n", "Here are some requirements for the rewritten PR description:\n", "- Drop ALL code snippets.\n", "- Replace ALL symbol names with natural language descriptions. For example, instead of \"Add a FutureWarning\", say \"add a future warning\".\n", "- Keep the main points of the PR description, but drop some of the less important details. If there are sections with headers, like \"### Testing\" or \"### Reproduction Steps\", drop those headers to make the text more unstructured.\n", "- Continue to instruct the reader that they need to address the issue and that there is indeed an issue. For example, instead of saying \"Can someone fix this?\" say \"Please fix this.\"\n", "\"\"\"\n", "\n", "\n", "def generate_realistic_pr_description(pr_description):\n", "    prompt = fuzzing_prompt.format(pr_description=pr_description)\n", "    messages = [[TextPrompt(text=prompt)]]\n", "    output = anthropic_client.generate(messages, max_tokens=16384, temperature=0.0)\n", "    output_str = output[0][0].text\n", "    output_str = output_str.split(\"<realistic_pr_description>\")[1].split(\n", "        \"</realistic_pr_description>\"\n", "    )[0]\n", "    return output_str"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(5):\n", "    input_pr_desc = ds[\"test\"].to_pandas()[\"problem_statement\"].iloc[i]\n", "    output_pr_desc = generate_realistic_pr_description(input_pr_desc)\n", "    print(\"Original PR Description:\")\n", "    print(input_pr_desc)\n", "    print(f\"------ {i} -------\")\n", "    print(\"Rewritten PR Description:\")\n", "    print(output_pr_desc)\n", "    print((\"\\n\" + \"=\" * 40) * 4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Fuzz entire dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from compress_pickle import load as compress_load, dump as compress_dump\n", "from concurrent.futures import ThreadPoolExecutor\n", "import concurrent.futures\n", "import tqdm\n", "\n", "old_dataset_path = \"/mnt/efs/augment/data/swebench_processed_eval/v1.pickle.gz\"\n", "new_dataset_path = (\n", "    \"/mnt/efs/augment/data/swebench_processed_eval/v1-fuzzed-strong.pickle.gz\"\n", ")\n", "\n", "with open(old_dataset_path, \"rb\") as f:\n", "    dataset = compress_load(f)\n", "\n", "\n", "def process_instance(instance):\n", "    print(f\"Processing ID: {instance['instance_id']}\")\n", "    instance[\"problem_statement\"] = generate_realistic_pr_description(\n", "        instance[\"problem_statement\"]\n", "    )\n", "    return instance\n", "\n", "\n", "def process_shard(shard):\n", "    # Use a nested ThreadPoolExecutor for processing instances within a shard\n", "    with ThreadPoolExecutor(\n", "        max_workers=10\n", "    ) as inner_executor:  # Adjust inner parallelism\n", "        # Submit all instances for processing\n", "        futures = [\n", "            inner_executor.submit(process_instance, instance) for instance in shard\n", "        ]\n", "\n", "        # Collect results\n", "        processed_instances = []\n", "        for future in concurrent.futures.as_completed(futures):\n", "            processed_instances.append(future.result())\n", "\n", "    return processed_instances\n", "\n", "\n", "# Parallelize processing with ThreadPoolExecutor\n", "num_workers = 10  # Adjust based on your CPU cores\n", "with ThreadPoolExecutor(max_workers=num_workers) as executor:\n", "    # Submit all shards for processing\n", "    futures = [executor.submit(process_shard, shard) for shard in dataset]\n", "\n", "    # Process results as they complete with progress bar\n", "    processed_shards = []\n", "    for future in tqdm.tqdm(\n", "        concurrent.futures.as_completed(futures),\n", "        total=len(futures),\n", "        desc=\"Processing shards\",\n", "    ):\n", "        processed_shards.append(future.result())\n", "\n", "    # Replace dataset with processed shards\n", "    new_dataset = processed_shards\n", "\n", "# Save the processed dataset\n", "with open(new_dataset_path, \"wb\") as f:\n", "    compress_dump(new_dataset, f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from compress_pickle import load as compress_load, dump as compress_dump\n", "from concurrent.futures import ThreadPoolExecutor\n", "import concurrent.futures\n", "import tqdm\n", "\n", "new_dataset_path = (\n", "    \"/mnt/efs/augment/data/swebench_processed_eval/v1-fuzzed-strong.pickle.gz\"\n", ")\n", "\n", "with open(new_dataset_path, \"rb\") as f:\n", "    dataset = compress_load(f)\n", "\n", "dataset"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
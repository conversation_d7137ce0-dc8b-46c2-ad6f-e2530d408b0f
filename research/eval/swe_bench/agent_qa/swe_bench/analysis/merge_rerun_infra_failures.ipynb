{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["core_path = \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11794_11795_11796_11797_11798_11799_11800_11801_11802_11803/\"\n", "reruns_path = \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11833_11834/\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from glob import glob\n", "from pathlib import Path\n", "\n", "for path in glob(reruns_path + \"workspace_*\"):\n", "    dir_name = Path(path).name\n", "    new_dir_path = core_path + dir_name\n", "    print(f\"Moving {path} to {new_dir_path}\")\n", "\n", "    if False:\n", "        # put all contents in new_dir_path into a subdirectory \"prev_attempt1\"\n", "        prev_attempt_path = new_dir_path + \"/prev_attempt1\"\n", "        Path(prev_attempt_path).mkdir(parents=True, exist_ok=True)\n", "        for file_path in glob(new_dir_path + \"/*\"):\n", "            if \"prev_attempt1\" in file_path:\n", "                continue\n", "            print(f\"  Moving {file_path} to {prev_attempt_path}\")\n", "            Path(file_path).rename(prev_attempt_path + \"/\" + Path(file_path).name)\n", "\n", "    # move all contents in path to new_dir_path\n", "    for file_path in glob(path + \"/*\"):\n", "        print(f\"  Moving {file_path} to {new_dir_path}\")\n", "        Path(file_path).rename(new_dir_path + \"/\" + Path(file_path).name)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["submitted_ids: <class 'list'>\n", "resolved_ids: <class 'list'>\n", "completed_ids: <class 'list'>\n", "incomplete_ids: <class 'list'>\n", "unresolved_ids: <class 'list'>\n", "empty_patch_ids: <class 'list'>\n", "error_ids: <class 'list'>\n", "submitted_instances: <class 'int'>\n", "completed_instances: <class 'int'>\n", "resolved_instances: <class 'int'>\n", "unresolved_instances: <class 'int'>\n", "error_instances: <class 'int'>\n", "empty_patch_instances: <class 'int'>\n", "Num target ids: 11\n"]}], "source": ["import json\n", "\n", "augment_agent_json = core_path + \"Augment_Agent.json\"\n", "augment_agent_rerun_json = reruns_path + \"Augment_Agent.json\"\n", "with open(augment_agent_json, \"r\") as f:\n", "    augment_agent = json.load(f)\n", "with open(augment_agent_rerun_json, \"r\") as f:\n", "    augment_agent_rerun = json.load(f)\n", "\n", "for k, v in augment_agent.items():\n", "    print(f\"{k}: {type(v)}\")\n", "\n", "target_ids = set(\n", "    augment_agent_rerun[\"submitted_ids\"]\n", "    + augment_agent_rerun[\"resolved_ids\"]\n", "    + augment_agent_rerun[\"completed_ids\"]\n", "    + augment_agent_rerun[\"incomplete_ids\"]\n", "    + augment_agent_rerun[\"unresolved_ids\"]\n", "    + augment_agent_rerun[\"empty_patch_ids\"]\n", "    + augment_agent_rerun[\"error_ids\"]\n", ")\n", "print(f\"Num target ids: {len(target_ids)}\")\n", "\n", "# remove target ideas everywhere from augment_agent\n", "augment_agent[\"submitted_ids\"] = list(set(augment_agent[\"submitted_ids\"]) - target_ids)\n", "augment_agent[\"resolved_ids\"] = list(set(augment_agent[\"resolved_ids\"]) - target_ids)\n", "augment_agent[\"completed_ids\"] = list(set(augment_agent[\"completed_ids\"]) - target_ids)\n", "augment_agent[\"incomplete_ids\"] = list(\n", "    set(augment_agent[\"incomplete_ids\"]) - target_ids\n", ")\n", "augment_agent[\"unresolved_ids\"] = list(\n", "    set(augment_agent[\"unresolved_ids\"]) - target_ids\n", ")\n", "augment_agent[\"empty_patch_ids\"] = list(\n", "    set(augment_agent[\"empty_patch_ids\"]) - target_ids\n", ")\n", "augment_agent[\"error_ids\"] = list(set(augment_agent[\"error_ids\"]) - target_ids)\n", "\n", "# merge augment_agent_rerun into augment_agent\n", "for k, v in augment_agent_rerun.items():\n", "    if isinstance(v, list):\n", "        augment_agent[k] = list(set(augment_agent[k] + v))\n", "\n", "augment_agent[\"submitted_instances\"] = len(augment_agent[\"submitted_ids\"])\n", "augment_agent[\"completed_instances\"] = len(augment_agent[\"completed_ids\"])\n", "augment_agent[\"resolved_instances\"] = len(augment_agent[\"resolved_ids\"])\n", "augment_agent[\"unresolved_instances\"] = len(augment_agent[\"unresolved_ids\"])\n", "augment_agent[\"error_instances\"] = len(augment_agent[\"error_ids\"])\n", "augment_agent[\"empty_patch_instances\"] = len(augment_agent[\"empty_patch_ids\"])\n", "\n", "with open(augment_agent_json, \"w\") as f:\n", "    json.dump(augment_agent, f, indent=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Then run script to regenerate consolidated html\n", "\n", "`python research/eval/swe_bench/agent_qa/swe_bench/consolidate_det_runs.py --regenerate-html 11794 11795 11796 11797 11798 11799 11800 11801 11802 11803`"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import compress_pickle as pickle\n", "from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import get_issue_image_name\n", "from pathlib import Path\n", "from multiprocessing import Manager\n", "from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import (\n", "    setup_workspace,\n", "    MAX_DOCKER_CONCURRENCY,\n", "    stop_container,\n", "    remove_container_image,\n", ")\n", "from research.eval.swe_bench.agent_qa.swe_bench import (\n", "    instruction_prompt_sequential_thinking,\n", ")\n", "\n", "data_path = \"/mnt/efs/augment/data/swebench_processed_eval/v1.pickle.gz\"\n", "with open(data_path, \"rb\") as f:\n", "    data = pickle.load(f)\n", "\n", "issue = data[0][0]\n", "print(issue)\n", "\n", "workspace = Path(f\"/tmp/run_problem/workspace_{issue['instance_id']}\")\n", "container_id = None\n", "image_name = get_issue_image_name(issue, workspace)\n", "print(image_name)\n", "\n", "manager = Manager()\n", "lock = manager.Lock()\n", "semaphore = manager.Semaphore(MAX_DOCKER_CONCURRENCY)\n", "env, container_id = setup_workspace(workspace, issue, lock, semaphore)\n", "\n", "instruction = instruction_prompt_sequential_thinking.PROMPT.format(\n", "    location=\"/testbed\",\n", "    pr_description=issue[\"problem_statement\"],\n", "    step7_str=\"Run select tests from the repo to make sure that your fix doesn't break anything else.\",\n", ")\n", "(workspace / \"instructions.txt\").write_text(instruction)\n", "\n", "print(container_id)\n", "\n", "print(f\"\"\"Run the following command off agent.\n", "-----\n", "python experimental/guy/agent_qa/interactive_agent.py -w {(workspace / issue[\"instance_id\"]).as_posix()} -y \\\\\n", "--swebench-mode --specify-tool str_replace_editor --enable-bash-tool --specify-tool sequential_thinking \\\\\n", "--use-low-qos-server --use-anthropic-direct --docker-container-id {container_id} --use-container-workspace /testbed \\\\\n", "--instruction-file /tmp/run_problem/instructions.txt --log-file /tmp/run_problem/agent_log.txt --pickle-log-file /tmp/run_problem/agent_log.pickle \\\\\n", "--no-integration-warnings --approve-command-execution --max-retries 50 --max-turns 500 --remove-env-var PYTHON_PATH --auth-token-file /home/<USER>/.augment/token \\\\\n", "--use-prompt-budgeting --use-direct-str-replace-too\n", "\"\"\")\n", "\n", "print(\"Follow along at /tmp/run_problem/agent_log.txt\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["workspace"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import (\n", "    generate_prediction,\n", "    run_evaluation,\n", "    get_dataset_name,\n", ")\n", "\n", "import os\n", "\n", "del os.environ[\"PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION\"]\n", "\n", "\"\"\"\n", "If you hit \"AttributeError: type object 'FieldOptions' has no attribute 'RegisterExtension'\" error,\n", "make sure PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION is unset\n", "\"\"\"\n", "\n", "patch = generate_prediction(workspace)\n", "run_id = issue[\"instance_id\"]\n", "run_evaluation(\n", "    predictions_file=workspace / \"predictions.json\",\n", "    dataset=get_dataset_name(\"full\"),  # Always use the full dataset for evaluation.\n", "    run_id=run_id,\n", "    num_processes=1,\n", "    retry_docker_startup=True,\n", "    swebench_venv_path=Path(\"/home/<USER>/swebench_env/bin/python\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open(\n", "    \"/tmp/run_problem/logs/run_evaluation/astropy__astropy-12907/Augment_Agent/astropy__astropy-12907/patch.diff\"\n", ") as f:\n", "    patch_diff = f.read()\n", "with open(\n", "    \"/tmp/run_problem/logs/run_evaluation/astropy__astropy-12907/Augment_Agent/astropy__astropy-12907/report.json\"\n", ") as f:\n", "    report = json.load(f)\n", "with open(\n", "    \"/tmp/run_problem/logs/run_evaluation/astropy__astropy-12907/Augment_Agent/astropy__astropy-12907/run_instance.log\"\n", ") as f:\n", "    run_instance_log = f.read()\n", "with open(\n", "    \"/tmp/run_problem/logs/run_evaluation/astropy__astropy-12907/Augment_Agent/astropy__astropy-12907/test_output.txt\"\n", ") as f:\n", "    test_output = f.read()\n", "\n", "print(\"PATCH DIFF\")\n", "print(\"----\")\n", "print(patch_diff)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"----\")\n", "print(\"REPORT\")\n", "print(\"----\")\n", "print(report)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"----\")\n", "print(\"RUN INSTANCE LOG\")\n", "print(\"----\")\n", "print(run_instance_log)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"----\")\n", "print(\"TEST OUTPUT\")\n", "print(\"----\")\n", "print(test_output)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["if container_id is not None:\n", "    stop_container(container_id)\n", "remove_container_image(image_name)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
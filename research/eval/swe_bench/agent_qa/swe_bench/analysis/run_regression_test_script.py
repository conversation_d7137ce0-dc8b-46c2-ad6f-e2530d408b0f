import os
from glob import glob
import json
from functools import lru_cache
from pathlib import Path
from tqdm import tqdm
from research.eval.swe_bench.agent_qa.swe_bench import (
    instruction_prompt_sequential_thinking,
)
import compress_pickle as pickle
from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import (
    REVERSE_PATCH_REPOS,
    get_issue_image_name,
)
from multiprocessing import Manager
from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import (
    setup_workspace,
    MAX_DOCKER_CONCURRENCY,
    stop_container,
    remove_container_image,
)
import difflib
import subprocess
from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import (
    generate_prediction,
    run_evaluation,
    get_dataset_name,
)
import traceback
from concurrent.futures import ThreadPoolExecutor
import contextlib
import io
import sys

data_path = "/mnt/efs/augment/data/swebench_processed_eval/v1.pickle.gz"
with open(data_path, "rb") as f:
    data = pickle.load(f)

run_dir = "/mnt/efs/augment/public_html/swebench/consolidated_runs/12359_12360_12361_12362_12363_12364_12365_12366_12367_12368/"


@lru_cache(maxsize=100)
def get_example_eval_dirs(run_dir):
    return glob(run_dir + "workspace_*/logs/run_evaluation/swe_work/Augment_Agent/*/")


def get_consolidated_run_id(dir: str) -> str:
    return dir.split("consolidated_runs/")[1].split("/")[0]


instance_id_to_run_results = {}
run_id = get_consolidated_run_id(run_dir)
for example_eval_dir in get_example_eval_dirs(run_dir):
    patch_diff_path = example_eval_dir + "patch.diff"
    report_path = example_eval_dir + "report.json"

    fails_fail_to_pass = None
    fails_pass_to_pass = None
    with open(patch_diff_path, "r") as f:
        patch_diff = f.read()
    try:
        with open(report_path, "r") as f:
            report = json.load(f)
        instance_id, eval_res = list(report.items())[0]

        fails_fail_to_pass = (
            len(list(report.values())[0]["tests_status"]["FAIL_TO_PASS"]["failure"])
            != 0
        )
        fails_pass_to_pass = (
            len(list(report.values())[0]["tests_status"]["PASS_TO_PASS"]["failure"])
            != 0
        )
        names_of_fails_pass_to_pass = list(report.values())[0]["tests_status"][
            "PASS_TO_PASS"
        ]["failure"]
    except FileNotFoundError:
        instance_id = str(Path(report_path).parent.name)
        eval_res = {"resolved": False}
        names_of_fails_pass_to_pass = []
    is_success = eval_res["resolved"]

    issue_path = (
        Path(example_eval_dir).parent.parent.parent.parent.parent / "issue.json"
    )
    with open(issue_path, "r") as f:
        issue = json.load(f)
    problem_statement = issue["problem_statement"]

    location = f"/run/determined/workdir/swe_work/workspace_{instance_id}"
    instruction = instruction_prompt_sequential_thinking.PROMPT.format(
        location=location,
        pr_description=problem_statement,
        step7_str="Run select tests from the repo to make sure that your fix doesn't break anything else.",
    )

    instance_id_to_run_results[instance_id] = {
        "patch_diff": patch_diff,
        "is_success": is_success,
        "instruction": instruction,
        "fails_fail_to_pass": fails_fail_to_pass,
        "fails_pass_to_pass": fails_pass_to_pass,
        "names_of_fails_pass_to_pass": names_of_fails_pass_to_pass,
    }


failure_ids = [
    "django__django-11141",
    "django__django-11490",
    "django__django-11885",
    "django__django-12273",
    "django__django-14170",
    "django__django-14631",
    "django__django-15503",
    "django__django-16100",
    "django__django-16454",
    "matplotlib__matplotlib-20488",
    "psf__requests-1766",
    "psf__requests-1921",
    "psf__requests-2931",
    "pydata__xarray-3305",
    "pydata__xarray-3993",
    "pydata__xarray-4094",
    "pydata__xarray-6721",
    "pytest-dev__pytest-6197",
    "scikit-learn__scikit-learn-13124",
    "sphinx-doc__sphinx-10323",
]
success_ids = [
    "astropy__astropy-7671",
    "django__django-7530",
    "django__django-10880",
    "django__django-10973",
    "django__django-11211",
    "django__django-14089",
    "matplotlib__matplotlib-20859",
    "psf__requests-1142",
    "pydata__xarray-4075",
    "pylint-dev__pylint-6528",
    "scikit-learn__scikit-learn-11310",
]

ids = success_ids

manager = Manager()
lock = manager.Lock()
semaphore = manager.Semaphore(MAX_DOCKER_CONCURRENCY)


def process_id(instance_id) -> dict:
    instance_id_to_issue = {}
    for shard in data:
        for issue in shard:
            instance_id_to_issue[issue["instance_id"]] = issue

    issue = instance_id_to_issue[instance_id]

    print(issue)

    workspace = Path(f"/tmp/run_problem/workspace_{issue['instance_id']}")
    container_id = None
    image_name = get_issue_image_name(issue, workspace)
    print(image_name)
    env, container_id = setup_workspace(workspace, issue, lock, semaphore)

    # instruction = instruction_prompt_sequential_thinking.PROMPT.format(
    #    location="/testbed",
    #    pr_description=issue["problem_statement"],
    #    step7_str="Run select tests from the repo to make sure that your fix doesn't break anything else.",
    # )

    print(container_id)

    location = "/testbed"
    pr_description = issue["problem_statement"]
    instruction = f"""
    <uploaded_files>
    {location}
    </uploaded_files>
    I've uploaded a python code repository in the directory {location} (not in /tmp/inputs).

    <pr_description>
    {pr_description}
    </pr_description>

    I have addressed the <pr_description> already, but some tests are failing. Can you help me help me fix these tests, while still addressing the PR description?
    Also, please revert any changes to any test_*.py files in the git diff.
    I've already taken care of all changes to any of the test files in the codebase. This means you DON'T have to modify the testing logic or any of the tests in any way!

    Follow these steps to resolve the issue:
    1. As a first step, revert any changes to any test_*.py files in the git diff.
    2. Create a script reproduce_pr_task.py to reproduce the error and execute it with `python reproduce_pr_task.py` using the BashTool. While we are focused on fixing regressions on existing tests, we want to make sure we don't break new functionality.
    3. Find the relevant failing tests and run them to see the failures.
    4. Use the sequential_thinking tool to plan any fixes.
    5. Edit the sourcecode of the repo to resolve the failing tests while making sure we still pass reproduce_pr_task.py. Repeat steps 3-5 until all tests are passing and reproduce_pr_task.py is still passing.
    6. Re-run all tests to make sure that all previously failing tests are now passing.


    GUIDE FOR HOW TO USE "sequential_thinking" TOOL:
    - Your thinking should be thorough and so it's fine if it's very long. Set totalThoughts to at least 5, but setting it up to 25 is fine as well. You'll need more total thoughts when you are considering multiple possible solutions or root causes for an issue.
    - Use this tool as much as you find necessary to improve the quality of your answers.
    - You can run bash commands (like tests, a reproduction script, or 'grep'/'find' to find relevant context) in between thoughts.
    - The sequential_thinking tool can help you break down complex problems, analyze issues step-by-step, and ensure a thorough approach to problem-solving.
    - Don't hesitate to use it multiple times throughout your thought process to enhance the depth and accuracy of your solutions.

    TIPS:
    - Do NOT make tool calls inside thoughts passed to sequential_thinking tool. For example, do NOT do this: {{'thought': 'I need to look at the actual implementation of `apps.get_models()` in this version of Django to see if there\'s a bug. Let me check the Django apps module:\n\n<function_calls>\n<invoke name="str_replace_editor">\n<parameter name="command">view</parameter>\n<parameter name="path">django/apps/registry.py</parameter></invoke>', 'path': 'django/apps/registry.py'}}
    - Do NOT run "git init" or "git commit" or "git add". Do NOT commit your changes.
    - Respect the tool specifications. If a field is required, make sure to provide a value for it. For example "thoughtNumber" is required by the sequential_thinking tool.
    - When you run "ls" or variants of it, you may see a symlink like "fileA -> /data/vol/fileA". You can safely ignore the symlink and just use "fileA" as the path when read, editing, or executing the file.
    - When you need to find information about the codebase, use "grep" and "find" to search for relevant files and code with the bash tool
    - Use your bash tool to set up any necessary environment variables, such as those needed to run tests.
    """

    (workspace / "instructions.txt").write_text(instruction)

    # apply diff
    def apply_patch(workspace, patch):
        patch_path = os.path.join(workspace.parent, "patch.diff")
        with open(patch_path, "w") as f:
            f.write(patch)
        repo = workspace.name
        if "sphinx" in repo:
            subprocess.run(
                ["git", "apply", "--reverse", patch_path], cwd=workspace, check=True
            )
        else:
            subprocess.run(["git", "apply", patch_path], cwd=workspace, check=True)

    apply_patch(
        workspace / issue["instance_id"],
        instance_id_to_run_results[instance_id]["patch_diff"],
    )
    print(instance_id_to_run_results[instance_id]["patch_diff"])

    cmd = f"""\
python experimental/guy/agent_qa/interactive_agent.py -w {(workspace / issue["instance_id"]).as_posix()} -y \\
--swebench-mode --specify-tool str_replace_editor --enable-bash-tool --specify-tool sequential_thinking \\
--use-anthropic-direct --docker-container-id {container_id} --use-container-workspace /testbed \\
--instruction-file {workspace}/instructions.txt --log-file {workspace}/agent_log.txt --pickle-log-file {workspace}/agent_log.pickle \\
--no-integration-warnings --approve-command-execution --max-retries 50 --max-turns 500 --remove-env-var PYTHON_PATH --auth-token-file /home/<USER>/.augment/token \\
--use-prompt-budgeting  --use-direct-str-replace-tool --quit
"""
    # run command
    output = subprocess.run(cmd, shell=True, check=True, capture_output=True)
    stdout = output.stdout.decode()
    stderr = output.stderr.decode()

    with open(f"{workspace}/stdout.txt", "w") as f:
        f.write(stdout)
    with open(f"{workspace}/stderr.txt", "w") as f:
        f.write(stderr)

    # print if there is a non-zero return code
    if output.returncode != 0:
        print(f"Non-zero return code: {output.returncode} for {instance_id}")
        return {}

    generate_prediction(workspace)
    run_id = issue["instance_id"]
    print(f"Running evaluation for {run_id}")
    output = run_evaluation(
        predictions_file=workspace / "predictions.json",
        dataset=get_dataset_name("full"),  # Always use the full dataset for evaluation.
        run_id=run_id,
        num_processes=1,
        retry_docker_startup=True,
        swebench_venv_path=Path("/home/<USER>/swebench_env/bin/python"),
    )
    print(f"Run evaluation result: {output} for {instance_id}")

    with open(
        glob(
            f'{workspace}/logs/run_evaluation/{issue["instance_id"]}/Augment_Agent/*/report.json'
        )[0]
    ) as f:
        report = json.load(f)

    report_data = report[list(report.keys())[0]]

    return report_data


@contextlib.contextmanager
def suppress_output():
    """Suppress stdout and stderr within a context.

    Usage:
        with suppress_output():
            print("This will not be visible")
    """
    stdout, stderr = sys.stdout, sys.stderr
    stream = io.StringIO()
    sys.stdout = sys.stderr = stream
    try:
        yield
    finally:
        sys.stdout, sys.stderr = stdout, stderr


def safe_process_id(id):
    try:
        print(f"Processing {id}")
        return id, process_id(id)
    except Exception as e:
        trace = traceback.format_exc()
        print(f"Failed to process {id}: {e} \n\n {trace}")
        return id, {"Result": f"Failed to process {id}: {e} \n\n {trace}"}


def process_all_ids():
    with ThreadPoolExecutor(max_workers=8) as pool:
        res_ls = []
        for id, result in pool.map(safe_process_id, ids):
            res_ls.append((id, result))
        print("-------\n" * 10)
        with open("RESULTS.txt", "w") as f:
            for id, result in res_ls:
                f.write(f"\n-------\nRESULTS FOR ID: {id}\n")
                if "resolved" in result:
                    f.write(f"Resolved: {result['resolved']}\n")
                    failing_new_tests = "\n- ".join(
                        result["tests_status"]["FAIL_TO_PASS"]["failure"]
                    )
                    failing_existing_tests = "\n- ".join(
                        result["tests_status"]["PASS_TO_PASS"]["failure"]
                    )
                    f.write(f"Failing new tests: {failing_new_tests}\n")
                    f.write(f"Failing existing tests: {failing_existing_tests}\n")
                else:
                    f.write(f"Error: {result}\n")


if __name__ == "__main__":
    results = process_all_ids()

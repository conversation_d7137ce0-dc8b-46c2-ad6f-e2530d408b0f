{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Step 1 get pass-to-pass failure examples"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "from glob import glob\n", "import json\n", "from functools import lru_cache\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "from research.eval.swe_bench.agent_qa.swe_bench import (\n", "    instruction_prompt_sequential_thinking,\n", ")\n", "\n", "run_dir = \"/mnt/efs/augment/public_html/swebench/consolidated_runs/12359_12360_12361_12362_12363_12364_12365_12366_12367_12368/\"\n", "\n", "\n", "@lru_cache(maxsize=100)\n", "def get_example_eval_dirs(run_dir):\n", "    return glob(run_dir + \"workspace_*/logs/run_evaluation/swe_work/Augment_Agent/*/\")\n", "\n", "\n", "def get_consolidated_run_id(dir: str) -> str:\n", "    return dir.split(\"consolidated_runs/\")[1].split(\"/\")[0]\n", "\n", "\n", "instance_id_to_run_results = {}\n", "run_id = get_consolidated_run_id(run_dir)\n", "for example_eval_dir in get_example_eval_dirs(run_dir):\n", "    patch_diff_path = example_eval_dir + \"patch.diff\"\n", "    report_path = example_eval_dir + \"report.json\"\n", "\n", "    fails_fail_to_pass = None\n", "    fails_pass_to_pass = None\n", "    with open(patch_diff_path, \"r\") as f:\n", "        patch_diff = f.read()\n", "    try:\n", "        with open(report_path, \"r\") as f:\n", "            report = json.load(f)\n", "        instance_id, eval_res = list(report.items())[0]\n", "\n", "        fails_fail_to_pass = (\n", "            len(list(report.values())[0][\"tests_status\"][\"FAIL_TO_PASS\"][\"failure\"])\n", "            != 0\n", "        )\n", "        fails_pass_to_pass = (\n", "            len(list(report.values())[0][\"tests_status\"][\"PASS_TO_PASS\"][\"failure\"])\n", "            != 0\n", "        )\n", "        names_of_fails_pass_to_pass = list(report.values())[0][\"tests_status\"][\n", "            \"PASS_TO_PASS\"\n", "        ][\"failure\"]\n", "    except FileNotFoundError:\n", "        instance_id = str(Path(report_path).parent.name)\n", "        eval_res = {\"resolved\": False}\n", "        names_of_fails_pass_to_pass = []\n", "    is_success = eval_res[\"resolved\"]\n", "\n", "    issue_path = (\n", "        Path(example_eval_dir).parent.parent.parent.parent.parent / \"issue.json\"\n", "    )\n", "    with open(issue_path, \"r\") as f:\n", "        issue = json.load(f)\n", "    problem_statement = issue[\"problem_statement\"]\n", "\n", "    location = f\"/run/determined/workdir/swe_work/workspace_{instance_id}\"\n", "    instruction = instruction_prompt_sequential_thinking.PROMPT.format(\n", "        location=location,\n", "        pr_description=problem_statement,\n", "        step7_str=\"Run select tests from the repo to make sure that your fix doesn't break anything else.\",\n", "    )\n", "\n", "    instance_id_to_run_results[instance_id] = {\n", "        \"patch_diff\": patch_diff,\n", "        \"is_success\": is_success,\n", "        \"instruction\": instruction,\n", "        \"fails_fail_to_pass\": fails_fail_to_pass,\n", "        \"fails_pass_to_pass\": fails_pass_to_pass,\n", "        \"names_of_fails_pass_to_pass\": names_of_fails_pass_to_pass,\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "\"\"\"\n", "# print ids of fails pass to pass\n", "for instance_id, res_dct in instance_id_to_run_results.items():\n", "    if res_dct[\"fails_pass_to_pass\"]:\n", "        print(instance_id)\n", "\"\"\"\n", "\n", "num_fails_fail_to_pass = 0\n", "num_fails_pass_to_pass = 0\n", "num_both = 0\n", "num_failing_tests_per_fails_pass_to_pass = []\n", "total_tests_per_fails_pass_to_pass = []\n", "\n", "for instance_id, res_dct in instance_id_to_run_results.items():\n", "    if res_dct[\"fails_fail_to_pass\"]:\n", "        num_fails_fail_to_pass += 1\n", "    if res_dct[\"fails_pass_to_pass\"]:\n", "        num_fails_pass_to_pass += 1\n", "    if res_dct[\"fails_fail_to_pass\"] and res_dct[\"fails_pass_to_pass\"]:\n", "        num_both += 1\n", "    if res_dct[\"fails_pass_to_pass\"]:\n", "        num_failing_tests_per_fails_pass_to_pass.append(\n", "            len(res_dct[\"names_of_fails_pass_to_pass\"])\n", "        )\n", "        total_tests_per_fails_pass_to_pass.append(\n", "            len(res_dct[\"names_of_fails_pass_to_pass\"])\n", "            + len(list(report.values())[0][\"tests_status\"][\"PASS_TO_PASS\"][\"success\"])\n", "        )\n", "perc_failing_tests_per_fails_pass_to_pass = []\n", "for num_failing, total in zip(\n", "    num_failing_tests_per_fails_pass_to_pass, total_tests_per_fails_pass_to_pass\n", "):\n", "    perc_failing_tests_per_fails_pass_to_pass.append(num_failing / total)\n", "\n", "print(f\"Num fails fail to pass: {num_fails_fail_to_pass}\")\n", "print(f\"Num fails pass to pass: {num_fails_pass_to_pass}\")\n", "print(f\"Num both: {num_both}\")\n", "\n", "\n", "# show quantiles for perc_failing_tests_per_fails_pass_to_pass\n", "print(\n", "    f\"Quantiles for perc_failing_tests_per_fails_pass_to_pass (0, 0.25, 0.5, 0.75, 1): {np.quantile(perc_failing_tests_per_fails_pass_to_pass, [0, 0.25, 0.5, 0.75, 1])}\"\n", ")\n", "# show quntiles for num_failing_tests_per_fails_pass_to_pass\n", "print(\n", "    f\"Quantiles for num_failing_tests_per_fails_pass_to_pass (0, 0.25, 0.5, 0.75, 1): {np.quantile(num_failing_tests_per_fails_pass_to_pass, [0, 0.25, 0.5, 0.75, 1])}\"\n", ")\n", "\n", "# show numbes of tests where only 1 pass_to_pass test is failing, and no fail_to_pass tests are failing\n", "num_only_one_pass_to_pass_failing = 0\n", "names_only_one_pass_to_pass_failing = []\n", "for instance_id, res_dct in instance_id_to_run_results.items():\n", "    if not res_dct[\"fails_fail_to_pass\"] and len(\n", "        res_dct[\"names_of_fails_pass_to_pass\"]\n", "    ) in [i for i in range(1, 10)]:\n", "        num_only_one_pass_to_pass_failing += 1\n", "        names_only_one_pass_to_pass_failing.append(instance_id)\n", "print(f\"Num only one pass to pass failing: {num_only_one_pass_to_pass_failing}\")\n", "\n", "print(\"\\nNames of only one pass to pass failing:\")\n", "for id in names_only_one_pass_to_pass_failing:\n", "    print(id)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### step 2: fix regression tests"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# get patch_diff for django__django-15503\n", "instance_id = \"django__django-16454\"\n", "print(instance_id_to_run_results[instance_id][\"patch_diff\"])\n", "print(\"-------------\\n\\n\")\n", "print(\"Names of fails pass to pass:\")\n", "print(instance_id_to_run_results[instance_id][\"names_of_fails_pass_to_pass\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import compress_pickle as pickle\n", "from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import get_issue_image_name\n", "from pathlib import Path\n", "from multiprocessing import Manager\n", "from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import (\n", "    setup_workspace,\n", "    MAX_DOCKER_CONCURRENCY,\n", "    stop_container,\n", "    remove_container_image,\n", ")\n", "from research.eval.swe_bench.agent_qa.swe_bench import (\n", "    instruction_prompt_sequential_thinking,\n", ")\n", "\n", "data_path = \"/mnt/efs/augment/data/swebench_processed_eval/v1.pickle.gz\"\n", "with open(data_path, \"rb\") as f:\n", "    data = pickle.load(f)\n", "\n", "instance_id_to_issue = {}\n", "for shard in data:\n", "    for issue in shard:\n", "        instance_id_to_issue[issue[\"instance_id\"]] = issue\n", "\n", "issue = instance_id_to_issue[instance_id]\n", "\n", "print(issue)\n", "\n", "workspace = Path(f\"/tmp/run_problem/workspace_{issue['instance_id']}\")\n", "container_id = None\n", "image_name = get_issue_image_name(issue, workspace)\n", "print(image_name)\n", "\n", "manager = Manager()\n", "lock = manager.Lock()\n", "semaphore = manager.Semaphore(MAX_DOCKER_CONCURRENCY)\n", "env, container_id = setup_workspace(workspace, issue, lock, semaphore)\n", "\n", "# instruction = instruction_prompt_sequential_thinking.PROMPT.format(\n", "#    location=\"/testbed\",\n", "#    pr_description=issue[\"problem_statement\"],\n", "#    step7_str=\"Run select tests from the repo to make sure that your fix doesn't break anything else.\",\n", "# )\n", "\n", "print(container_id)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### instruction 1: show pr description and ask to fix  failing tests (show failing test)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["location = \"/testbed\"\n", "pr_description = (issue[\"problem_statement\"],)\n", "instruction = f\"\"\"\n", "<uploaded_files>\n", "{location}\n", "</uploaded_files>\n", "I've uploaded a python code repository in the directory {location} (not in /tmp/inputs). \n", "\n", "<pr_description>\n", "{pr_description}\n", "</pr_description>\n", "\n", "I have addressed the <pr_description> already, but some tests are failing. Can you help me help me fix these tests, while still addressing the PR description? \n", "Also, please revert any changes to any test_*.py files in the git diff.\n", "I've already taken care of all changes to any of the test files in the codebase. This means you DON'T have to modify the testing logic or any of the tests in any way!\n", "\n", "Here are the relevant failing tests:\n", "- test_load_empty_dir (migrations.test_loader.LoaderTests)\n", "\n", "Follow these steps to resolve the issue:\n", "1. As a first step, revert any changes to any test_*.py files in the git diff.\n", "2. Create a script reproduce_pr_task.py to reproduce the error and execute it with `python reproduce_pr_task.py` using the BashTool. While we are focused on fixing regressions on existing tests, we want to make sure we don't break new functionality.\n", "3. Find the relevant failing tests and run them to see the failures.\n", "4. Use the sequential_thinking tool to plan any fixes.\n", "5. Edit the sourcecode of the repo to resolve the failing tests while making sure we still pass reproduce_pr_task.py. Repeat steps 3-5 until all tests are passing and reproduce_pr_task.py is still passing.\n", "6. Re-run all tests to make sure that all previously failing tests are now passing.\n", "\n", "\n", "GUIDE FOR HOW TO USE \"sequential_thinking\" TOOL:\n", "- Your thinking should be thorough and so it's fine if it's very long. Set totalThoughts to at least 5, but setting it up to 25 is fine as well. You'll need more total thoughts when you are considering multiple possible solutions or root causes for an issue.\n", "- Use this tool as much as you find necessary to improve the quality of your answers.\n", "- You can run bash commands (like tests, a reproduction script, or 'grep'/'find' to find relevant context) in between thoughts.\n", "- The sequential_thinking tool can help you break down complex problems, analyze issues step-by-step, and ensure a thorough approach to problem-solving.\n", "- Don't hesitate to use it multiple times throughout your thought process to enhance the depth and accuracy of your solutions.\n", "\n", "TIPS:\n", "- Do NOT make tool calls inside thoughts passed to sequential_thinking tool. For example, do NOT do this: {{'thought': 'I need to look at the actual implementation of `apps.get_models()` in this version of Django to see if there\\'s a bug. Let me check the Django apps module:\\n\\n<function_calls>\\n<invoke name=\"str_replace_editor\">\\n<parameter name=\"command\">view</parameter>\\n<parameter name=\"path\">django/apps/registry.py</parameter></invoke>', 'path': 'django/apps/registry.py'}}\n", "- Respect the tool specifications. If a field is required, make sure to provide a value for it. For example \"thoughtNumber\" is required by the sequential_thinking tool.\n", "- When you run \"ls\" or variants of it, you may see a symlink like \"fileA -> /data/vol/fileA\". You can safely ignore the symlink and just use \"fileA\" as the path when read, editing, or executing the file.\n", "- When you need to find information about the codebase, use \"grep\" and \"find\" to search for relevant files and code with the bash tool\n", "- Use your bash tool to set up any necessary environment variables, such as those needed to run tests.\n", "\"\"\"\n", "\n", "(workspace / \"instructions.txt\").write_text(instruction)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### instruction 1b: show pr description and ask to fix  failing tests (show how to run tests)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["location = \"/testbed\"\n", "pr_description = (issue[\"problem_statement\"],)\n", "instruction = f\"\"\"\n", "<uploaded_files>\n", "{location}\n", "</uploaded_files>\n", "I've uploaded a python code repository in the directory {location} (not in /tmp/inputs). \n", "\n", "<pr_description>\n", "{pr_description}\n", "</pr_description>\n", "\n", "I have addressed the <pr_description> already, but some tests are failing. Can you help me help me fix these tests, while still addressing the PR description? \n", "Also, please revert any changes to any test_*.py files in the git diff.\n", "I've already taken care of all changes to any of the test files in the codebase. This means you DON'T have to modify the testing logic or any of the tests in any way!\n", "\n", "Use the tests/runtests.py script to run tests. In order to only run only one test, you can do something like\n", "cd tests && ./runtests.py migrations.test_loader.LoaderTests\n", "\n", "Follow these steps to resolve the issue:\n", "1. As a first step, revert any changes to any test_*.py files in the git diff.\n", "2. Create a script reproduce_pr_task.py to reproduce the error and execute it with `python reproduce_pr_task.py` using the BashTool. While we are focused on fixing regressions on existing tests, we want to make sure we don't break new functionality.\n", "3. Find the relevant failing tests and run them to see the failures.\n", "4. Use the sequential_thinking tool to plan any fixes.\n", "5. Edit the sourcecode of the repo to resolve the failing tests while making sure we still pass reproduce_pr_task.py. Repeat steps 3-5 until all tests are passing and reproduce_pr_task.py is still passing.\n", "6. Re-run all tests to make sure that all previously failing tests are now passing.\n", "\n", "\n", "GUIDE FOR HOW TO USE \"sequential_thinking\" TOOL:\n", "- Your thinking should be thorough and so it's fine if it's very long. Set totalThoughts to at least 5, but setting it up to 25 is fine as well. You'll need more total thoughts when you are considering multiple possible solutions or root causes for an issue.\n", "- Use this tool as much as you find necessary to improve the quality of your answers.\n", "- You can run bash commands (like tests, a reproduction script, or 'grep'/'find' to find relevant context) in between thoughts.\n", "- The sequential_thinking tool can help you break down complex problems, analyze issues step-by-step, and ensure a thorough approach to problem-solving.\n", "- Don't hesitate to use it multiple times throughout your thought process to enhance the depth and accuracy of your solutions.\n", "\n", "TIPS:\n", "- Do NOT make tool calls inside thoughts passed to sequential_thinking tool. For example, do NOT do this: {{'thought': 'I need to look at the actual implementation of `apps.get_models()` in this version of Django to see if there\\'s a bug. Let me check the Django apps module:\\n\\n<function_calls>\\n<invoke name=\"str_replace_editor\">\\n<parameter name=\"command\">view</parameter>\\n<parameter name=\"path\">django/apps/registry.py</parameter></invoke>', 'path': 'django/apps/registry.py'}}\n", "- Respect the tool specifications. If a field is required, make sure to provide a value for it. For example \"thoughtNumber\" is required by the sequential_thinking tool.\n", "- When you run \"ls\" or variants of it, you may see a symlink like \"fileA -> /data/vol/fileA\". You can safely ignore the symlink and just use \"fileA\" as the path when read, editing, or executing the file.\n", "- When you need to find information about the codebase, use \"grep\" and \"find\" to search for relevant files and code with the bash tool\n", "- Use your bash tool to set up any necessary environment variables, such as those needed to run tests.\n", "\"\"\"\n", "\n", "(workspace / \"instructions.txt\").write_text(instruction)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### instruction 1c: show pr description and ask to fix  failing tests (don't show failing tests or how to run tests)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["location = \"/testbed\"\n", "pr_description = (issue[\"problem_statement\"],)\n", "instruction = f\"\"\"\n", "<uploaded_files>\n", "{location}\n", "</uploaded_files>\n", "I've uploaded a python code repository in the directory {location} (not in /tmp/inputs). \n", "\n", "<pr_description>\n", "{pr_description}\n", "</pr_description>\n", "\n", "I have addressed the <pr_description> already, but some tests are failing. Can you help me help me fix these tests, while still addressing the PR description? \n", "Also, please revert any changes to any test_*.py files in the git diff.\n", "I've already taken care of all changes to any of the test files in the codebase. This means you DON'T have to modify the testing logic or any of the tests in any way!\n", "\n", "Follow these steps to resolve the issue:\n", "1. As a first step, revert any changes to any test_*.py files in the git diff.\n", "2. Create a script reproduce_pr_task.py to reproduce the error and execute it with `python reproduce_pr_task.py` using the BashTool. While we are focused on fixing regressions on existing tests, we want to make sure we don't break new functionality.\n", "3. Find the relevant failing tests and run them to see the failures.\n", "4. Use the sequential_thinking tool to plan any fixes.\n", "5. Edit the sourcecode of the repo to resolve the failing tests while making sure we still pass reproduce_pr_task.py. Repeat steps 3-5 until all tests are passing and reproduce_pr_task.py is still passing.\n", "6. Re-run all tests to make sure that all previously failing tests are now passing.\n", "\n", "\n", "GUIDE FOR HOW TO USE \"sequential_thinking\" TOOL:\n", "- Your thinking should be thorough and so it's fine if it's very long. Set totalThoughts to at least 5, but setting it up to 25 is fine as well. You'll need more total thoughts when you are considering multiple possible solutions or root causes for an issue.\n", "- Use this tool as much as you find necessary to improve the quality of your answers.\n", "- You can run bash commands (like tests, a reproduction script, or 'grep'/'find' to find relevant context) in between thoughts.\n", "- The sequential_thinking tool can help you break down complex problems, analyze issues step-by-step, and ensure a thorough approach to problem-solving.\n", "- Don't hesitate to use it multiple times throughout your thought process to enhance the depth and accuracy of your solutions.\n", "\n", "TIPS:\n", "- Do NOT make tool calls inside thoughts passed to sequential_thinking tool. For example, do NOT do this: {{'thought': 'I need to look at the actual implementation of `apps.get_models()` in this version of Django to see if there\\'s a bug. Let me check the Django apps module:\\n\\n<function_calls>\\n<invoke name=\"str_replace_editor\">\\n<parameter name=\"command\">view</parameter>\\n<parameter name=\"path\">django/apps/registry.py</parameter></invoke>', 'path': 'django/apps/registry.py'}}\n", "- Do NOT commit your changes.\n", "- Respect the tool specifications. If a field is required, make sure to provide a value for it. For example \"thoughtNumber\" is required by the sequential_thinking tool.\n", "- When you run \"ls\" or variants of it, you may see a symlink like \"fileA -> /data/vol/fileA\". You can safely ignore the symlink and just use \"fileA\" as the path when read, editing, or executing the file.\n", "- When you need to find information about the codebase, use \"grep\" and \"find\" to search for relevant files and code with the bash tool\n", "- Use your bash tool to set up any necessary environment variables, such as those needed to run tests.\n", "\"\"\"\n", "\n", "(workspace / \"instructions.txt\").write_text(instruction)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### instruction 2: do not show pr description and ask to fix tests"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["location = \"/testbed\"\n", "pr_description = (issue[\"problem_statement\"],)\n", "instruction = f\"\"\"\n", "<uploaded_files>\n", "{location}\n", "</uploaded_files>\n", "I've uploaded a python code repository in the directory {location} (not in /tmp/inputs). \n", "\n", "I have made some recent changes to address a feature request, but some tests are failing. Can you help me help me fix these tests? \n", "I've already taken care of all changes to any of the test files in the codebase. This means you DON'T have to modify the testing logic or any of the tests in any way!\n", "\n", "Follow these steps to resolve the issue:\n", "1. As a first step, it would be a good idea to figure out how to run all tests.\n", "2. Run all tests to find the failures.\n", "3. Use the sequential_thinking tool to plan any fixes.\n", "4. Edit the sourcecode of the repo to resolve the failing tests.\n", "6. Re-run all tests to make sure that all tests are passing.\n", "\n", "\n", "GUIDE FOR HOW TO USE \"sequential_thinking\" TOOL:\n", "- Your thinking should be thorough and so it's fine if it's very long. Set totalThoughts to at least 5, but setting it up to 25 is fine as well. You'll need more total thoughts when you are considering multiple possible solutions or root causes for an issue.\n", "- Use this tool as much as you find necessary to improve the quality of your answers.\n", "- You can run bash commands (like tests, a reproduction script, or 'grep'/'find' to find relevant context) in between thoughts.\n", "- The sequential_thinking tool can help you break down complex problems, analyze issues step-by-step, and ensure a thorough approach to problem-solving.\n", "- Don't hesitate to use it multiple times throughout your thought process to enhance the depth and accuracy of your solutions.\n", "\n", "TIPS:\n", "- Do NOT make tool calls inside thoughts passed to sequential_thinking tool. For example, do NOT do this: {{'thought': 'I need to look at the actual implementation of `apps.get_models()` in this version of Django to see if there\\'s a bug. Let me check the Django apps module:\\n\\n<function_calls>\\n<invoke name=\"str_replace_editor\">\\n<parameter name=\"command\">view</parameter>\\n<parameter name=\"path\">django/apps/registry.py</parameter></invoke>', 'path': 'django/apps/registry.py'}}\n", "- Respect the tool specifications. If a field is required, make sure to provide a value for it. For example \"thoughtNumber\" is required by the sequential_thinking tool.\n", "- When you run \"ls\" or variants of it, you may see a symlink like \"fileA -> /data/vol/fileA\". You can safely ignore the symlink and just use \"fileA\" as the path when read, editing, or executing the file.\n", "- When you need to find information about the codebase, use \"grep\" and \"find\" to search for relevant files and code with the bash tool\n", "- Use your bash tool to set up any necessary environment variables, such as those needed to run tests.\n", "\"\"\"\n", "\n", "(workspace / \"instructions.txt\").write_text(instruction)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### instruction 3: hardcode some failing tests and see if it works"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["location = \"/testbed\"\n", "pr_description = (issue[\"problem_statement\"],)\n", "instruction = f\"\"\"\n", "<uploaded_files>\n", "{location}\n", "</uploaded_files>\n", "I've uploaded a python code repository in the directory {location} (not in /tmp/inputs). \n", "\n", "<pr_description>\n", "{pr_description}\n", "</pr_description>\n", "\n", "I have addressed the <pr_description> already, but some tests are failing. Can you help me help me fix all the failing tests in the repo, while still addressing the PR description? \n", "Also, please revert any changes to any test_*.py files in the git diff.\n", "I've already taken care of all changes to any of the test files in the codebase. This means you DON'T have to modify the testing logic or any of the tests in any way!\n", "\n", "Here are the relevant failing tests:\n", "- test_load_empty_dir (migrations.test_loader.LoaderTests)\n", "\n", "Use the tests/runtests.py script to run tests. In order to only run only one test, you can do something like\n", "cd tests && ./runtests.py migrations.test_loader.LoaderTests\n", "\n", "Follow these steps to resolve the issue:\n", "1. As a first step, revert any changes to any test_*.py files in the git diff.\n", "2. Create a script reproduce_pr_task.py to reproduce the error and execute it with `python reproduce_pr_task.py` using the BashTool. While we are focused on fixing regressions on existing tests, we want to make sure we don't break new functionality.\n", "3. Run all failing tests to find the failures.\n", "4. Use the sequential_thinking tool to plan any fixes.\n", "5. Edit the sourcecode of the repo to resolve the failing tests while making sure we still pass reproduce_pr_task.py. Repeat steps 3-5 until all tests are passing and reproduce_pr_task.py is still passing.\n", "6. Re-run all tests to make sure that all previously failing tests are now passing.\n", "\n", "\n", "GUIDE FOR HOW TO USE \"sequential_thinking\" TOOL:\n", "- Your thinking should be thorough and so it's fine if it's very long. Set totalThoughts to at least 5, but setting it up to 25 is fine as well. You'll need more total thoughts when you are considering multiple possible solutions or root causes for an issue.\n", "- Use this tool as much as you find necessary to improve the quality of your answers.\n", "- You can run bash commands (like tests, a reproduction script, or 'grep'/'find' to find relevant context) in between thoughts.\n", "- The sequential_thinking tool can help you break down complex problems, analyze issues step-by-step, and ensure a thorough approach to problem-solving.\n", "- Don't hesitate to use it multiple times throughout your thought process to enhance the depth and accuracy of your solutions.\n", "\n", "TIPS:\n", "- Do NOT make tool calls inside thoughts passed to sequential_thinking tool. For example, do NOT do this: {{'thought': 'I need to look at the actual implementation of `apps.get_models()` in this version of Django to see if there\\'s a bug. Let me check the Django apps module:\\n\\n<function_calls>\\n<invoke name=\"str_replace_editor\">\\n<parameter name=\"command\">view</parameter>\\n<parameter name=\"path\">django/apps/registry.py</parameter></invoke>', 'path': 'django/apps/registry.py'}}\n", "- Respect the tool specifications. If a field is required, make sure to provide a value for it. For example \"thoughtNumber\" is required by the sequential_thinking tool.\n", "- When you run \"ls\" or variants of it, you may see a symlink like \"fileA -> /data/vol/fileA\". You can safely ignore the symlink and just use \"fileA\" as the path when read, editing, or executing the file.\n", "- When you need to find information about the codebase, use \"grep\" and \"find\" to search for relevant files and code with the bash tool\n", "- Use your bash tool to set up any necessary environment variables, such as those needed to run tests.\n", "\"\"\"\n", "\n", "(workspace / \"instructions.txt\").write_text(instruction)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### apply failing diff"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# apply diff\n", "import difflib\n", "import subprocess\n", "\n", "\n", "def apply_patch(workspace, patch):\n", "    patch_path = os.path.join(workspace.parent, \"patch.diff\")\n", "    with open(patch_path, \"w\") as f:\n", "        f.write(patch)\n", "    subprocess.run([\"git\", \"apply\", patch_path], cwd=workspace, check=True)\n", "\n", "\n", "apply_patch(\n", "    workspace / issue[\"instance_id\"],\n", "    instance_id_to_run_results[instance_id][\"patch_diff\"],\n", ")\n", "print(instance_id_to_run_results[instance_id][\"patch_diff\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Now run the agent to fix regressions!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"\"\"Run the following command off agent.\n", "-----\n", "python experimental/guy/agent_qa/interactive_agent.py -w {(workspace / issue[\"instance_id\"]).as_posix()} -y \\\\\n", "--swebench-mode --specify-tool str_replace_editor --enable-bash-tool --specify-tool sequential_thinking \\\\\n", "--use-anthropic-direct --docker-container-id {container_id} --use-container-workspace /testbed \\\\\n", "--instruction-file {workspace}/instructions.txt --log-file {workspace}/agent_log.txt --pickle-log-file {workspace}/agent_log.pickle \\\\\n", "--no-integration-warnings --approve-command-execution --max-retries 50 --max-turns 500 --remove-env-var PYTHON_PATH --auth-token-file /home/<USER>/.augment/token \\\\\n", "--use-prompt-budgeting  --use-direct-str-replace-tool\n", "\"\"\")\n", "# --use-low-qos-server\n", "\n", "print(f\"Follow along at {workspace}/agent_log.txt\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Now look at the diffs (initial suggestion vs final diff, gold vs final diff)"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [], "source": ["import tempfile\n", "\n", "\n", "def diff_of_diffs(old_patch_str: str, new_patch_str: str) -> str:\n", "    \"\"\"\n", "    Produce a unified diff describing how `old_patch_str` differs from `new_patch_str`.\n", "    Requires the `interdiff` utility from patchutils to be installed on your system.\n", "    \"\"\"\n", "    with tempfile.TemporaryDirectory() as tmpdir:\n", "        old_patch_path = os.path.join(tmpdir, \"old.patch\")\n", "        new_patch_path = os.path.join(tmpdir, \"new.patch\")\n", "\n", "        # Write the patch contents to temp files\n", "        with open(old_patch_path, \"w\", encoding=\"utf-8\") as f:\n", "            f.write(old_patch_str)\n", "        with open(new_patch_path, \"w\", encoding=\"utf-8\") as f:\n", "            f.write(new_patch_str)\n", "\n", "        # Run `interdiff` on the two patch files\n", "        result = subprocess.run(\n", "            [\"interdiff\", old_patch_path, new_patch_path],\n", "            capture_output=True,\n", "            text=True,\n", "            check=False,\n", "        )\n", "\n", "        # If interdiff fails or returns nothing, handle that\n", "        if result.returncode != 0:\n", "            raise RuntimeError(f\"interdiff failed:\\n{result.stderr}\")\n", "\n", "        # `result.stdout` contains the \"diff of diffs\"\n", "        return result.stdout"]}, {"cell_type": "markdown", "metadata": {}, "source": ["initial diff"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import (\n", "    generate_patch,\n", "    REVERSE_PATCH_REPOS,\n", ")\n", "from datasets import load_dataset\n", "\n", "\n", "cwd = workspace / issue[\"instance_id\"]\n", "initial_diff = instance_id_to_run_results[instance_id][\"patch_diff\"]\n", "patch_diff = generate_patch(cwd, reverse=issue[\"repo\"] in REVERSE_PATCH_REPOS)\n", "assert patch_diff is not None\n", "\n", "\n", "raw_ds = load_dataset(\"princeton-nlp/SWE-bench_Verified\")\n", "raw_df = raw_ds[\"test\"].to_pandas()  # type: ignore\n", "gold_diff = raw_df[raw_df[\"instance_id\"] == issue[\"instance_id\"]][\"patch\"].iloc[0]\n", "\n", "print(initial_diff)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Initial diff vs gold diff"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(diff_of_diffs(initial_diff, gold_diff))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Initial diff vs final diff"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assert patch_diff is not None\n", "print(diff_of_diffs(initial_diff, patch_diff))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["final diff vs gold diff"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assert patch_diff is not None\n", "print(diff_of_diffs(patch_diff, gold_diff))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Now run eval and see results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if False:\n", "    apply_patch(workspace / issue[\"instance_id\"], patch_diff)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import (\n", "    generate_prediction,\n", "    run_evaluation,\n", "    get_dataset_name,\n", ")\n", "import json\n", "\n", "\"\"\"\n", "If you hit \"AttributeError: type object 'FieldOptions' has no attribute 'RegisterExtension'\" error,\n", "make sure PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION is unset\n", "\"\"\"\n", "\n", "patch = generate_prediction(workspace)\n", "run_id = issue[\"instance_id\"]\n", "print(f\"Running evaluation for {run_id}\")\n", "run_evaluation(\n", "    predictions_file=workspace / \"predictions.json\",\n", "    dataset=get_dataset_name(\"full\"),  # Always use the full dataset for evaluation.\n", "    run_id=run_id,\n", "    num_processes=1,\n", "    retry_docker_startup=True,\n", "    swebench_venv_path=Path(\"/home/<USER>/swebench_env/bin/python\"),\n", ")\n", "\n", "with open(\n", "    glob(\n", "        f'{workspace}/logs/run_evaluation/{issue[\"instance_id\"]}/Augment_Agent/*/patch.diff'\n", "    )[0]\n", ") as f:\n", "    patch_diff = f.read()\n", "with open(\n", "    glob(\n", "        f'{workspace}/logs/run_evaluation/{issue[\"instance_id\"]}/Augment_Agent/*/report.json'\n", "    )[0]\n", ") as f:\n", "    report = json.load(f)\n", "with open(\n", "    glob(\n", "        f'{workspace}/logs/run_evaluation/{issue[\"instance_id\"]}/Augment_Agent/*/run_instance.log'\n", "    )[0]\n", ") as f:\n", "    run_instance_log = f.read()\n", "with open(\n", "    glob(\n", "        f'{workspace}/logs/run_evaluation/{issue[\"instance_id\"]}/Augment_Agent/*/test_output.txt'\n", "    )[0]\n", ") as f:\n", "    test_output = f.read()\n", "\n", "\n", "print(\"----\")\n", "print(\"REPORT\")\n", "print(\"----\")\n", "\n", "report_data = report[list(report.keys())[0]]\n", "\n", "patch_is_none = report_data[\"patch_is_None\"]\n", "patch_exists = report_data[\"patch_exists\"]\n", "patch_successfully_applied = report_data[\"patch_successfully_applied\"]\n", "resolved = report_data[\"resolved\"]\n", "num_fail_to_pass = len(report_data[\"tests_status\"][\"FAIL_TO_PASS\"][\"failure\"]) + len(\n", "    report_data[\"tests_status\"][\"FAIL_TO_PASS\"][\"success\"]\n", ")\n", "num_pass_to_pass = len(report_data[\"tests_status\"][\"PASS_TO_PASS\"][\"failure\"]) + len(\n", "    report_data[\"tests_status\"][\"PASS_TO_PASS\"][\"success\"]\n", ")\n", "num_fail_to_pass_failures = len(report_data[\"tests_status\"][\"FAIL_TO_PASS\"][\"failure\"])\n", "num_pass_to_pass_failures = len(report_data[\"tests_status\"][\"PASS_TO_PASS\"][\"failure\"])\n", "\n", "names_of_fails_pass_to_pass = report_data[\"tests_status\"][\"PASS_TO_PASS\"][\"failure\"]\n", "names_of_fails_fail_to_pass = report_data[\"tests_status\"][\"FAIL_TO_PASS\"][\"failure\"]\n", "\n", "# pretty print variables\n", "print(f\"patch_is_none: {patch_is_none}\")\n", "print(f\"patch_exists: {patch_exists}\")\n", "print(f\"patch_successfully_applied: {patch_successfully_applied}\")\n", "print(f\"resolved: {resolved}\")\n", "print(f\"num_fail_to_pass: {num_fail_to_pass}, failures: {num_fail_to_pass_failures}\")\n", "print(f\"num_pass_to_pass: {num_pass_to_pass}, failures: {num_pass_to_pass_failures}\")\n", "\n", "print(\"----\")\n", "print(\"NAMES OF FAILS PASS TO PASS\")\n", "print(\"----\")\n", "print(names_of_fails_pass_to_pass)\n", "print(\"----\")\n", "print(\"NAMES OF FAILS FAIL TO PASS\")\n", "print(\"----\")\n", "print(names_of_fails_fail_to_pass)\n", "\n", "report_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def diff_of_diffs(old_patch_str: str, new_patch_str: str) -> str:\n", "    \"\"\"\n", "    Produce a unified diff describing how `old_patch_str` differs from `new_patch_str`.\n", "    Requires the `interdiff` utility from patchutils to be installed on your system.\n", "    \"\"\"\n", "    with tempfile.TemporaryDirectory() as tmpdir:\n", "        old_patch_path = os.path.join(tmpdir, \"old.patch\")\n", "        new_patch_path = os.path.join(tmpdir, \"new.patch\")\n", "\n", "        # Write the patch contents to temp files\n", "        with open(old_patch_path, \"w\", encoding=\"utf-8\") as f:\n", "            f.write(old_patch_str)\n", "        with open(new_patch_path, \"w\", encoding=\"utf-8\") as f:\n", "            f.write(new_patch_str)\n", "\n", "        # Run `interdiff` on the two patch files\n", "        result = subprocess.run(\n", "            [\"interdiff\", old_patch_path, new_patch_path],\n", "            capture_output=True,\n", "            text=True,\n", "            check=False,\n", "        )\n", "\n", "        # If interdiff fails or returns nothing, handle that\n", "        if result.returncode != 0:\n", "            raise RuntimeError(f\"interdiff failed:\\n{result.stderr}\")\n", "\n", "        # `result.stdout` contains the \"diff of diffs\"\n", "        return result.stdout\n", "\n", "\n", "diff_of_diffs(initial_diff, patch_diff)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["if container_id is not None:\n", "    stop_container(container_id)\n", "remove_container_image(image_name)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
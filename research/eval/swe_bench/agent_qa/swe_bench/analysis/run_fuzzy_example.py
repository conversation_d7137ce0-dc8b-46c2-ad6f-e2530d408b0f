import os
from glob import glob
import json
from functools import lru_cache
from pathlib import Path
from tqdm import tqdm
from research.eval.swe_bench.agent_qa.swe_bench import (
    instruction_prompt_sequential_thinking,
)
import compress_pickle as pickle
from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import (
    REVERSE_PATCH_REPOS,
    get_issue_image_name,
)
from multiprocessing import Manager
from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import (
    setup_workspace,
    MAX_DOCKER_CONCURRENCY,
    stop_container,
    remove_container_image,
)
import difflib
import subprocess
from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import (
    generate_prediction,
    run_evaluation,
    get_dataset_name,
)
import traceback
from concurrent.futures import ThreadPoolExecutor
import contextlib
import io
import sys

WITH_RETRIEVAL = True

data_path = "/mnt/efs/augment/data/swebench_processed_eval/v1-fuzzed1.pickle.gz"
with open(data_path, "rb") as f:
    data = pickle.load(f)

success_ids = [
    "astropy__astropy-7671",
    "django__django-11211",
    "django__django-14089",
    "matplotlib__matplotlib-20859",
    "django__django-16100",
    "django__django-16454",
    "matplotlib__matplotlib-20488",
    "psf__requests-1766",
]

ids = success_ids

manager = Manager()
lock = manager.Lock()
semaphore = manager.Semaphore(MAX_DOCKER_CONCURRENCY)


def process_id(instance_id) -> dict:
    instance_id_to_issue = {}
    for shard in data:
        for issue in shard:
            instance_id_to_issue[issue["instance_id"]] = issue

    issue = instance_id_to_issue[instance_id]

    print(issue)

    workspace = Path(
        f"/tmp/run_problem-fuzzy{'-withretrieval' if WITH_RETRIEVAL else ''}/workspace_{issue['instance_id']}"
    )
    container_id = None
    image_name = get_issue_image_name(issue, workspace)
    print(image_name)
    env, container_id = setup_workspace(workspace, issue, lock, semaphore)

    # instruction = instruction_prompt_sequential_thinking.PROMPT.format(
    #    location="/testbed",
    #    pr_description=issue["problem_statement"],
    #    step7_str="Run select tests from the repo to make sure that your fix doesn't break anything else.",
    # )

    print(container_id)

    location = "/testbed"
    pr_description = issue["problem_statement"]

    retrieval_tool_guide = """
GUIDE FOR HOW TO USE "request_codebase_information" TOOL:
- You can ask this tool questions about the codebase, just like talking to a smart colleague, such as "Where is the function that handles user authentication?" or "What tests are there for the login functionality?"
- Use this tool as much as you find necessary to improve the quality of your answers.
- Use it to help you find relevant tests to run.
- Don't hesitate to use it multiple times throughout your thought process to enhance the depth and accuracy of your solutions.
"""

    instruction = f"""
<uploaded_files>
{location}
</uploaded_files>
I've uploaded a python code repository in the directory {location} (not in /tmp/inputs). Consider the following PR description:

<pr_description>
{pr_description}
</pr_description>

Can you help me implement the necessary changes to the repository so that the requirements specified in the <pr_description> are met?
I've already taken care of all changes to any of the test files described in the <pr_description>. This means you DON'T have to modify the testing logic or any of the tests in any way!

Your task is to make the minimal changes to non-tests files in the {location} directory to ensure the <pr_description> is satisfied.

Follow these steps to resolve the issue:
1. As a first step, it would be a good idea to explore the repo to familiarize yourself with its structure. {"Use the request_codebase_information tool for this." if WITH_RETRIEVAL else ""}
2. Create a script to reproduce the error and execute it with `python <filename.py>` using the BashTool, to confirm the error
3. Use the sequential_thinking tool to plan your fix{" and use the request_codebase_information tool to find relevant code and configuration files." if WITH_RETRIEVAL else ""}. Reflect on 5-7 different possible sources of the problem, distill those down to 1-2 most likely sources, and then add logs to validate your assumptions before moving onto implementing the actual code fix
4. Edit the sourcecode of the repo to resolve the issue
5. Rerun your reproduce script and confirm that the error is fixed!
6. Think about edgecases and make sure your fix handles them as well
7. Run select tests from the repo to make sure that your fix doesn't break anything else. {"Use the request_codebase_information tool to find relevant tests to run." if WITH_RETRIEVAL else ""}


GUIDE FOR HOW TO USE "sequential_thinking" TOOL:
- Your thinking should be thorough and so it's fine if it's very long. Set totalThoughts to at least 5, but setting it up to 25 is fine as well. You'll need more total thoughts when you are considering multiple possible solutions or root causes for an issue.
- Use this tool as much as you find necessary to improve the quality of your answers.
- You can run bash commands (like tests, a reproduction script, or 'grep'/'find' to find relevant context) in between thoughts.
- The sequential_thinking tool can help you break down complex problems, analyze issues step-by-step, and ensure a thorough approach to problem-solving.
- Don't hesitate to use it multiple times throughout your thought process to enhance the depth and accuracy of your solutions.

{retrieval_tool_guide if WITH_RETRIEVAL else ""}

TIPS:
- You must make changes in the {location} directory in order to ensure the requirements specified in the <pr_description> are met. Leaving the directory unchanged is not a valid solution.
- Do NOT make tool calls inside thoughts passed to sequential_thinking tool. For example, do NOT do this: {{'thought': 'I need to look at the actual implementation of `apps.get_models()` in this version of Django to see if there\'s a bug. Let me check the Django apps module:\n\n<function_calls>\n<invoke name="str_replace_editor">\n<parameter name="command">view</parameter>\n<parameter name="path">django/apps/registry.py</parameter></invoke>', 'path': 'django/apps/registry.py'}}
- Respect the tool specifications. If a field is required, make sure to provide a value for it. For example "thoughtNumber" is required by the sequential_thinking tool.
- When you run "ls" or variants of it, you may see a symlink like "fileA -> /data/vol/fileA". You can safely ignore the symlink and just use "fileA" as the path when read, editing, or executing the file.
- When you need to find information about the codebase, use "grep" and "find" to search for relevant files and code with the bash tool
- Use your bash tool to set up any necessary environment variables, such as those needed to run tests.
    """

    (workspace / "instructions.txt").write_text(instruction)

    cmd = f"""\
python experimental/guy/agent_qa/interactive_agent.py -w {(workspace / issue["instance_id"]).as_posix()} -y \\
--swebench-mode --specify-tool str_replace_editor --enable-bash-tool --specify-tool sequential_thinking \\
--use-anthropic-direct --docker-container-id {container_id} --use-container-workspace /testbed \\
--instruction-file {workspace}/instructions.txt --log-file {workspace}/agent_log.txt --pickle-log-file {workspace}/agent_log.pickle \\
--no-integration-warnings --approve-command-execution --max-retries 50 --max-turns 500 --remove-env-var PYTHON_PATH --auth-token-file /home/<USER>/.augment/token \\
--use-prompt-budgeting  --use-direct-str-replace-tool --quit
"""
    # run command
    output = subprocess.run(cmd, shell=True, check=True, capture_output=True)
    stdout = output.stdout.decode()
    stderr = output.stderr.decode()

    with open(f"{workspace}/stdout.txt", "w") as f:
        f.write(stdout)
    with open(f"{workspace}/stderr.txt", "w") as f:
        f.write(stderr)

    # print if there is a non-zero return code
    if output.returncode != 0:
        print(f"Non-zero return code: {output.returncode} for {instance_id}")
        return {}

    generate_prediction(workspace)
    run_id = issue["instance_id"]
    print(f"Running evaluation for {run_id}")
    output = run_evaluation(
        predictions_file=workspace / "predictions.json",
        dataset=get_dataset_name("full"),  # Always use the full dataset for evaluation.
        run_id=run_id,
        num_processes=1,
        retry_docker_startup=True,
        swebench_venv_path=Path("/home/<USER>/swebench_env/bin/python"),
    )
    print(f"Run evaluation result: {output} for {instance_id}")

    with open(
        glob(
            f'{workspace}/logs/run_evaluation/{issue["instance_id"]}/Augment_Agent/*/report.json'
        )[0]
    ) as f:
        report = json.load(f)

    report_data = report[list(report.keys())[0]]

    return report_data


@contextlib.contextmanager
def suppress_output():
    """Suppress stdout and stderr within a context.

    Usage:
        with suppress_output():
            print("This will not be visible")
    """
    stdout, stderr = sys.stdout, sys.stderr
    stream = io.StringIO()
    sys.stdout = sys.stderr = stream
    try:
        yield
    finally:
        sys.stdout, sys.stderr = stdout, stderr


def safe_process_id(id):
    try:
        print(f"Processing {id}")
        return id, process_id(id)
    except Exception as e:
        trace = traceback.format_exc()
        print(f"Failed to process {id}: {e} \n\n {trace}")
        return id, {"Result": f"Failed to process {id}: {e} \n\n {trace}"}


def process_all_ids():
    with ThreadPoolExecutor(max_workers=8) as pool:
        res_ls = []
        for id, result in pool.map(safe_process_id, ids):
            res_ls.append((id, result))
        print("-------\n" * 10)
        with open(
            f"RESULTS_FUZZY{'-withretrieval' if WITH_RETRIEVAL else ''}.txt", "w"
        ) as f:
            for id, result in res_ls:
                f.write(f"\n-------\nRESULTS FOR ID: {id}\n")
                if "resolved" in result:
                    f.write(f"Resolved: {result['resolved']}\n")
                    failing_new_tests = "\n- ".join(
                        result["tests_status"]["FAIL_TO_PASS"]["failure"]
                    )
                    failing_existing_tests = "\n- ".join(
                        result["tests_status"]["PASS_TO_PASS"]["failure"]
                    )
                    f.write(f"Failing new tests: {failing_new_tests}\n")
                    f.write(f"Failing existing tests: {failing_existing_tests}\n")
                else:
                    f.write(f"Error: {result}\n")


if __name__ == "__main__":
    results = process_all_ids()

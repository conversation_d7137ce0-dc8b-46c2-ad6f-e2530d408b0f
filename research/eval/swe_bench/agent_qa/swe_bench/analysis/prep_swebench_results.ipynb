{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from glob import glob\n", "import yaml\n", "import json\n", "import shutil"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Num runs:  8\n", "Success rate:  0.654\n"]}], "source": ["input_data_path = \"/mnt/efs/augment/user/colin/swe_bench_ensembling_experiment/mar14_65-4perc_results.json\"\n", "with open(input_data_path, \"r\") as f:\n", "    input_data = json.load(f)\n", "input_base_dirs = input_data[\"run_dirs\"]\n", "example_id_to_soln_run_idx = input_data[\"example_id_to_soln_run_idx\"]\n", "success_rate = input_data[\"success_rate\"]\n", "\n", "print(\"Num runs: \", len(input_base_dirs))\n", "print(\"Success rate: \", success_rate)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Copying trajs...\n", "Failed to find /mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/workspace_matplotlib__matplotlib-26291/agent_log.txt\n", "Failed to find /mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/workspace_matplotlib__matplotlib-26342/agent_log.txt\n", "Failed to find /mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/workspace_matplotlib__matplotlib-26466/agent_log.txt\n", "Failed to find /mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/workspace_pydata__xarray-7233/agent_log.txt\n", "Done copying trajs.\n", "Copying preds and logs...\n", "Found predictions and logs at index 7 for matplotlib__matplotlib-26466.\n", "Failed to find predictions and logs at index 7 for scikit-learn__scikit-learn-14710. Last place I looked: /mnt/efs/augment/public_html/swebench/consolidated_runs/11883_11885_11887_11889_11891_11893_11895_11897_11898_11900/workspace_scikit-learn__scikit-learn-14710/logs/run_evaluation/swe_work/Augment_Agent/scikit-learn__scikit-learn-14710 and /mnt/efs/augment/public_html/swebench/consolidated_runs/11883_11885_11887_11889_11891_11893_11895_11897_11898_11900/workspace_scikit-learn__scikit-learn-14710/predictions.json\n", "Found predictions and logs at index 6 for scikit-learn__scikit-learn-14710.\n", "Done copying preds and logs.\n", "Writing metadata...\n", "Done writing metadata.\n", "Writing README...\n", "Done writing README.\n"]}], "source": ["example_ids = []\n", "for dir in glob(\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/workspace_*\"\n", "):\n", "    example_ids.append(Path(dir).name.replace(\"workspace_\", \"\"))\n", "assert len(example_ids) == 500\n", "\n", "# logs_glob_path = glob(str(input_base_dir / f'workspace_*/logs/run_evaluation/swe_work/Augment_Agent/*/'))\n", "# predictions_glob_path = glob(str(input_base_dir / f'workspace_*/predictions.json'))\n", "output_base_dir = Path(\n", "    \"/home/<USER>/experiments/evaluation/verified/20250316_augment_agent_v0/\"\n", ")\n", "output_base_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# 5 things to copy:\n", "# - trajs/\n", "# - all_preds.jsonl\n", "# - logs/\n", "# - metadata.yaml\n", "# - README.md\n", "\n", "if True:\n", "    # trajs/ (includes rollouts and )\n", "    print(\"Copying trajs...\")\n", "    (output_base_dir / \"trajs\").mkdir(parents=True, exist_ok=True)\n", "    for run_idx, input_base_dir in enumerate(input_base_dirs):\n", "        for example_dir in glob(f\"{input_base_dir}/workspace_*/\"):\n", "            example_id = Path(example_dir).name.replace(\"workspace_\", \"\")\n", "            agent_log_path = Path(example_dir) / \"agent_log.txt\"\n", "            new_traj_path = (\n", "                Path(output_base_dir) / \"trajs\" / f\"{example_id}_rollout{run_idx}.txt\"\n", "            )\n", "\n", "            # load up text\n", "            try:\n", "                with agent_log_path.open(\"r\") as f:\n", "                    agent_log = f.read()\n", "            except FileNotFoundError:\n", "                print(f\"Failed to find {agent_log_path}\")\n", "                agent_log = \"Infra failure.\"\n", "                continue\n", "\n", "            # delete instruction and system prompt\n", "            target_str = \"llm responded\"\n", "            # drop everything before last instance of target str\n", "            agent_log = agent_log.split(target_str, 1)[-1]\n", "            # save\n", "            with new_traj_path.open(\"w\") as f:\n", "                f.write(agent_log)\n", "\n", "            # once per example, save the ensemble step logs\n", "            if run_idx == 0:\n", "                ensemble_step_logs = \"\"\"\n", "Generating ensemble step prompt with instructions and candidates solutions...\n", "Majority vote solution identified.\n", "\"\"\"\n", "                ensemble_traj_path = (\n", "                    Path(output_base_dir) / \"trajs\" / f\"{example_id}_ensemble_step.txt\"\n", "                )\n", "                with ensemble_traj_path.open(\"w\") as f:\n", "                    f.write(ensemble_step_logs)\n", "\n", "    print(\"Done copying trajs.\")\n", "\n", "print(\"Copying preds and logs...\")\n", "# copy all_preds.jsonl and logs/\n", "all_preds = []\n", "for example_id in example_ids:\n", "    run_idx = example_id_to_soln_run_idx[example_id]\n", "    input_base_dir = input_base_dirs[run_idx]\n", "    predictions_path = (\n", "        Path(input_base_dir) / f\"workspace_{example_id}\" / \"predictions.json\"\n", "    )\n", "    logs_path = (\n", "        Path(input_base_dir)\n", "        / f\"workspace_{example_id}\"\n", "        / \"logs\"\n", "        / \"run_evaluation\"\n", "        / \"swe_work\"\n", "        / \"Augment_Agent\"\n", "        / example_id\n", "    )\n", "\n", "    need_to_find_backup = False\n", "    if not predictions_path.exists() or not (logs_path / \"report.json\").exists():\n", "        need_to_find_backup = True\n", "\n", "    if need_to_find_backup:\n", "        for iter_run_idx in reversed(range(len(input_base_dirs))):\n", "            input_base_dir = input_base_dirs[iter_run_idx]\n", "            predictions_path = (\n", "                Path(input_base_dir) / f\"workspace_{example_id}\" / \"predictions.json\"\n", "            )\n", "            logs_path = (\n", "                Path(input_base_dir)\n", "                / f\"workspace_{example_id}\"\n", "                / \"logs\"\n", "                / \"run_evaluation\"\n", "                / \"swe_work\"\n", "                / \"Augment_Agent\"\n", "                / example_id\n", "            )\n", "            if predictions_path.exists() and (logs_path / \"report.json\").exists():\n", "                break\n", "            print(\n", "                f\"Failed to find predictions and logs at index {iter_run_idx} for {example_id}. Last place I looked: {logs_path} and {predictions_path}\"\n", "            )\n", "        else:\n", "            print(\n", "                f\"Failed to find predictions and logs for {example_id}. Last place I looked: {logs_path} and {predictions_path}\"\n", "            )\n", "            raise ValueError(\n", "                f\"Failed to find any predictions and logs for {example_id}\"\n", "            )\n", "\n", "        print(f\"Found predictions and logs at index {iter_run_idx} for {example_id}.\")\n", "\n", "    # save prediction\n", "    with open(predictions_path, \"r\") as f:\n", "        preds = json.load(f)\n", "        all_preds.extend(preds)\n", "\n", "    # copy eval output\n", "    dir_name = Path(logs_path).name\n", "    output_dir = output_base_dir / \"logs\" / dir_name\n", "    output_dir.mkdir(parents=True, exist_ok=True)\n", "    file_paths_to_move = list(Path(logs_path).glob(\"*\"))\n", "    if len(file_paths_to_move) == 0:\n", "        print(f\"Failed to find any files to move for {example_id} at {logs_path}\")\n", "\n", "    for file in Path(logs_path).glob(\"*\"):\n", "        # make a copy of file in output_dir\n", "        shutil.copy(file, output_dir / file.name)\n", "\n", "with (output_base_dir / \"all_preds.jsonl\").open(\"w\") as f:\n", "    for pred in all_preds:\n", "        f.write(json.dumps(pred) + \"\\n\")\n", "print(\"Done copying preds and logs.\")\n", "\n", "# metadata.yaml\n", "print(\"Writing metadata...\")\n", "metadata = {\n", "    \"name\": \"Augment Agent v0\",\n", "    \"oss\": <PERSON><PERSON><PERSON>,\n", "    \"site\": \"www.augmentcode.com\",\n", "}\n", "metadata_path = output_base_dir / \"metadata.yaml\"\n", "with metadata_path.open(\"w\") as f:\n", "    f.write(yaml.dump(metadata))\n", "print(\"Done writing metadata.\")\n", "\n", "# README.md\n", "print(\"Writing README...\")\n", "readme = \"\"\"# Augment\n", "\n", "The best AI coding assistant built for professional software engineers and large codebases.\n", "\n", "In this submision, we achieve a score of 65.4% with an agent using Sonnet 3.7 and basic tools\n", "like bash and file editing. Our agent design is inspired by <PERSON><PERSON><PERSON>'s. We also include a simple\n", "majority voting ensemble step that uses OpenAI's O1 model.\n", "\"\"\"\n", "readme_path = output_base_dir / \"README.md\"\n", "with readme_path.open(\"w\") as f:\n", "    f.write(readme)\n", "print(\"Done writing README.\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["['/mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/',\n", " '/mnt/efs/augment/public_html/swebench/consolidated_runs/11601_11602_11603_11604_11605_11606_11607_11608_11609_11610/',\n", " '/mnt/efs/augment/public_html/swebench/consolidated_runs/11671_11672_11673_11674_11675_11676_11677_11678_11679_11680/',\n", " '/mnt/efs/augment/public_html/swebench/consolidated_runs/11754_11755_11756_11757_11758_11759_11760_11761_11762_11763/',\n", " '/mnt/efs/augment/public_html/swebench/consolidated_runs/11768_11769_11770_11771_11772_11773_11774_11776_11777_11778/',\n", " '/mnt/efs/augment/public_html/swebench/consolidated_runs/11794_11795_11796_11797_11798_11799_11800_11801_11802_11803/',\n", " '/mnt/efs/augment/public_html/swebench/consolidated_runs/11882_11884_11886_11888_11890_11892_11894_11896_11899_11901/',\n", " '/mnt/efs/augment/public_html/swebench/consolidated_runs/11883_11885_11887_11889_11891_11893_11895_11897_11898_11900/',\n", " '/mnt/efs/augment/public_html/swebench/consolidated_runs/11906_11907_11908_11909_11910_11911_11912_11913_11914_11915/']"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["input_base_dirs"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
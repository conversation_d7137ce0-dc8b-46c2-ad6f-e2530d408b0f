{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "import glob\n", "import pickle\n", "from collections import Counter\n", "from research.agents.tools import LoggedLanguageModelCall, LoggedToolCall\n", "import numpy as np\n", "from functools import lru_cache\n", "from pickle import UnpicklingError\n", "\n", "\n", "@lru_cache(maxsize=5)\n", "def load_examples(dir: str):\n", "    cum_path = Path(dir) / \"Augment_Agent.json\"\n", "\n", "    with open(cum_path, \"r\") as f:\n", "        cum_data = json.load(f)\n", "    resolved_ids = cum_data[\"resolved_ids\"]\n", "    unresolved_ids = cum_data[\"unresolved_ids\"]\n", "\n", "    example_paths = glob.glob(f\"{dir}/workspace_*/agent_log.pickle\")\n", "    examples = {}\n", "    example_id_to_return_code = {}\n", "\n", "    pickle_fails = 0\n", "    for p in example_paths:\n", "        try:\n", "            example_id = p.split(\"/\")[-2].replace(\"workspace_\", \"\")\n", "            with open(p, \"rb\") as f:\n", "                examples[example_id] = pickle.load(f)\n", "\n", "            rc_path = p.replace(\"agent_log.pickle\", \"agent_rc.txt\")\n", "            with open(rc_path, \"r\") as f:\n", "                rc = int(f.read())\n", "            example_id_to_return_code[example_id] = rc\n", "        except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) as e:\n", "            print(f\"Failed to load {p}: {e}\")\n", "            pickle_fails += 1\n", "\n", "    print(f\"Failed to load {pickle_fails} pickles\")\n", "\n", "    resolved_examples = {k: v for k, v in examples.items() if k in resolved_ids}\n", "    unresolved_examples = {k: v for k, v in examples.items() if k in unresolved_ids}\n", "    unresolved_examples_by_rc = {}\n", "    for example_id, value in unresolved_examples.items():\n", "        rc_code = example_id_to_return_code[example_id]\n", "        if rc_code not in unresolved_examples_by_rc:\n", "            unresolved_examples_by_rc[rc_code] = {}\n", "        unresolved_examples_by_rc[rc_code][example_id] = value\n", "\n", "    return resolved_examples, unresolved_examples_by_rc\n", "\n", "\n", "def gen_summary_stats(examples):\n", "    agg_num_tool_calls_by_tool_choice = {}\n", "    agg_max_edits_for_single_file = []\n", "\n", "    for example in examples:\n", "        num_tool_calls_by_tool_choice = {}\n", "        num_lang_model_calls = 0\n", "        edit_file_fps = []\n", "        save_file_fps = []\n", "\n", "        for i in range(len(example)):\n", "            if isinstance(example[i], LoggedLanguageModelCall) and example[i].started:\n", "                num_lang_model_calls += 1\n", "            elif isinstance(example[i], LoggedToolCall) and example[i].started:\n", "                tool_name = example[i].tool.name\n", "                if tool_name not in num_tool_calls_by_tool_choice:\n", "                    num_tool_calls_by_tool_choice[tool_name] = 0\n", "                num_tool_calls_by_tool_choice[tool_name] += 1\n", "\n", "                if tool_name == \"edit_file_agent\":\n", "                    edit_file_fps.append(example[i].tool_input[\"file_path\"])\n", "                elif tool_name == \"save_file\":\n", "                    save_file_fps.append(example[i].tool_input[\"file_path\"])\n", "\n", "        for tool_name, count in num_tool_calls_by_tool_choice.items():\n", "            if tool_name not in agg_num_tool_calls_by_tool_choice:\n", "                agg_num_tool_calls_by_tool_choice[tool_name] = []\n", "            agg_num_tool_calls_by_tool_choice[tool_name].append(count)\n", "\n", "        edit_file_fps_counter = Counter(edit_file_fps)\n", "\n", "        max_edits_for_single_file = max(edit_file_fps_counter.values(), default=0)\n", "        agg_max_edits_for_single_file.append(max_edits_for_single_file)\n", "\n", "    # create summary stats for agg_num_tool_calls_by_tool_choice in a new dict\n", "    summary_stats = {}\n", "    for tool_name, count in agg_num_tool_calls_by_tool_choice.items():\n", "        summary_stats[tool_name] = {\n", "            #'min': np.min(count),\n", "            #'perc25': np.percentile(count, 25),\n", "            \"median\": np.median(count),\n", "            #'perc75': np.percentile(count, 75),\n", "            #'max': np.max(count),\n", "        }\n", "\n", "    summary_stats[\"max_edits_for_single_file\"] = {\n", "        \"median\": np.median(agg_max_edits_for_single_file),\n", "    }\n", "\n", "    return summary_stats"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def viz_turn_counts(path: str):\n", "    print(f\"Results for path: {path}\")\n", "    resolved_examples, unresolved_examples_by_rc = load_examples(path)\n", "    resolved_stats = gen_summary_stats(resolved_examples.values())\n", "    unresolved_stats_by_rc = {}\n", "    num_ids_by_rc = {}\n", "    for rc, examples in unresolved_examples_by_rc.items():\n", "        num_ids_by_rc[rc] = len(examples)\n", "    for rc in unresolved_examples_by_rc:\n", "        unresolved_stats_by_rc[rc] = gen_summary_stats(\n", "            unresolved_examples_by_rc[rc].values()\n", "        )\n", "\n", "    print(f\"Num resolutions= {len(resolved_examples)}\")\n", "    print(\n", "        f\"Num IDs BY RC: 0: {num_ids_by_rc.get(0, 0)}, 1: {num_ids_by_rc.get(1, 0)}, 2: {num_ids_by_rc.get(2, 0)}\"\n", "    )\n", "    print(\n", "        \"Tool name (resolved vs unresolved with different error codes): median num calls per agent trace\"\n", "    )\n", "    k_max_len = 40\n", "    for k in resolved_stats:\n", "        extra_k_len = k_max_len - len(k)\n", "        resolved_v = resolved_stats[k][\"median\"]\n", "        try:\n", "            unresolved_v_rc0 = unresolved_stats_by_rc[0][k][\"median\"]\n", "        except KeyError:\n", "            unresolved_v_rc0 = -1\n", "        try:\n", "            unresolved_v_rc1 = unresolved_stats_by_rc[1][k][\"median\"]\n", "        except KeyError:\n", "            unresolved_v_rc1 = -1\n", "        try:\n", "            unresolved_v_rc2 = unresolved_stats_by_rc[2][k][\"median\"]\n", "        except KeyError:\n", "            unresolved_v_rc2 = -1\n", "        print(\n", "            f\"{k}{' ' * extra_k_len}: (successes) {resolved_v:06.2f} ..... (failures) rc0={unresolved_v_rc0:06.2f}, rc1={unresolved_v_rc1:06.2f}, rc2={unresolved_v_rc2:06.2f}\"\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["viz_turn_counts(\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11467_11468_11469_11470_11471_11472_11473_11474_11475_11476\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from matplotlib.colors import ListedColormap\n", "import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "from matplotlib.patches import Patch\n", "\n", "resolved_examples_run1, unresolved_examples_by_rc_run1 = load_examples(\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11467_11468_11469_11470_11471_11472_11473_11474_11475_11476\"\n", ")\n", "# possible outcomes: \"success\", \"agent_failure\", \"timeout\", \"other_error\"\n", "example_id_to_outcome_run1 = {}\n", "for example_id, example in resolved_examples_run1.items():\n", "    example_id_to_outcome_run1[example_id] = \"success\"\n", "for example_id, example in unresolved_examples_by_rc_run1[0].items():\n", "    example_id_to_outcome_run1[example_id] = \"agent_failure\"\n", "if 1 in unresolved_examples_by_rc_run1:\n", "    for example_id, example in unresolved_examples_by_rc_run1[1].items():\n", "        example_id_to_outcome_run1[example_id] = \"other_error\"\n", "if 2 in unresolved_examples_by_rc_run1:\n", "    for example_id, example in unresolved_examples_by_rc_run1[2].items():\n", "        example_id_to_outcome_run1[example_id] = \"timeout\"\n", "\n", "resolved_examples_run2, unresolved_examples_by_rc_run2 = load_examples(\n", "    \"/mnt/efs/augment/public_html/swebench/consolidated_runs/10836_10837_10838_10839_10840\"\n", ")\n", "\n", "example_id_to_outcome_run2 = {}\n", "for example_id, example in resolved_examples_run2.items():\n", "    example_id_to_outcome_run2[example_id] = \"success\"\n", "for example_id, example in unresolved_examples_by_rc_run2[0].items():\n", "    example_id_to_outcome_run2[example_id] = \"agent_failure\"\n", "for example_id, example in unresolved_examples_by_rc_run2[1].items():\n", "    example_id_to_outcome_run2[example_id] = \"other_error\"\n", "for example_id, example in unresolved_examples_by_rc_run2[2].items():\n", "    example_id_to_outcome_run2[example_id] = \"timeout\"\n", "\n", "total_successes_across_runs = 0\n", "for example_id, outcome in example_id_to_outcome_run1.items():\n", "    outcome2 = example_id_to_outcome_run2.get(example_id, None)\n", "    if outcome == \"success\" or outcome2 == \"success\":\n", "        total_successes_across_runs += 1\n", "\n", "print(f\"Num successes in run 1: {len(resolved_examples_run1)}\")\n", "print(f\"Num successes in run 2: {len(resolved_examples_run2)}\")\n", "print(f\"Total unique successes across runs: {total_successes_across_runs}\")\n", "\n", "# visualize heatmap. there should be two columns (one for each run) and then one row per example.\n", "# have a legend that maps each outcome type to a color\n", "\n", "# Create a DataFrame with outcomes for both runs\n", "all_example_ids = set(example_id_to_outcome_run1.keys()) | set(\n", "    example_id_to_outcome_run2.keys()\n", ")\n", "data = {\"example_id\": [], \"run\": [], \"outcome\": []}\n", "\n", "for example_id in all_example_ids:\n", "    data[\"example_id\"].append(example_id)\n", "    data[\"run\"].append(\"Run 1\")\n", "    data[\"outcome\"].append(example_id_to_outcome_run1.get(example_id, \"missing\"))\n", "\n", "    data[\"example_id\"].append(example_id)\n", "    data[\"run\"].append(\"Run 2\")\n", "    data[\"outcome\"].append(example_id_to_outcome_run2.get(example_id, \"missing\"))\n", "\n", "df = pd.DataFrame(data)\n", "\n", "# Pivot the data for the heatmap\n", "pivot_df = df.pivot(index=\"example_id\", columns=\"run\", values=\"outcome\")\n", "\n", "# Sort by Run 1 outcomes, putting successes first\n", "outcome_priority = {\n", "    \"success\": 0,\n", "    \"agent_failure\": 1,\n", "    \"timeout\": 2,\n", "    \"other_error\": 3,\n", "    \"missing\": 4,\n", "}\n", "pivot_df[\"sort_key\"] = pivot_df[\"Run 1\"].map(outcome_priority)\n", "pivot_df = pivot_df.sort_values(\"sort_key\")\n", "pivot_df = pivot_df.drop(\"sort_key\", axis=1)\n", "\n", "# Create a categorical color map\n", "outcome_categories = [\"success\", \"agent_failure\", \"timeout\", \"other_error\", \"missing\"]\n", "colors = [\n", "    \"#2ecc71\",\n", "    \"#e74c3c\",\n", "    \"#f1c40f\",\n", "    \"#9b59b6\",\n", "    \"#95a5a6\",\n", "]  # green, red, yellow, purple, gray\n", "color_map = dict(zip(outcome_categories, colors))\n", "\n", "code_map = {\n", "    \"success\": 0,\n", "    \"agent_failure\": 1,\n", "    \"timeout\": 2,\n", "    \"other_error\": 3,\n", "    \"missing\": 4,\n", "}\n", "pivot_codes = pivot_df.applymap(code_map.get)\n", "cmap = ListedColormap([\"#2ecc71\", \"#e74c3c\", \"#f1c40f\", \"#9b59b6\", \"#95a5a6\"])\n", "\n", "\n", "# Create the heatmap\n", "plt.figure(figsize=(6, 100))\n", "sns.heatmap(\n", "    pivot_codes,\n", "    cmap=cmap,\n", "    cbar=False,\n", "    xticklabels=[\"Run 1\", \"Run 2\"],\n", "    yticklabels=pivot_df.index,  # type: ignore\n", ")\n", "plt.yticks(rotation=0, fontsize=6)\n", "\n", "# Add a custom legend\n", "legend_elements = [\n", "    Patch(facecolor=color, label=cat) for cat, color in zip(outcome_categories, colors)\n", "]\n", "plt.legend(handles=legend_elements, title=\"Outcomes\", bbox_to_anchor=(1.05, 1))\n", "\n", "plt.title(\"Outcome Comparison between Runs\")\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
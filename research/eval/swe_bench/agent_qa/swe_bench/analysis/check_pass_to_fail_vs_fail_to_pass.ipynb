{"cells": [{"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing /mnt/efs/augment/public_html/swebench/consolidated_runs/11794_11795_11796_11797_11798_11799_11800_11801_11802_11803/workspace_scikit-learn__scikit-learn-14710/logs/run_evaluation/swe_work/Augment_Agent/scikit-learn__scikit-learn-14710/report.json\n", "Missing /mnt/efs/augment/public_html/swebench/consolidated_runs/11794_11795_11796_11797_11798_11799_11800_11801_11802_11803/workspace_sphinx-doc__sphinx-10673/logs/run_evaluation/swe_work/Augment_Agent/sphinx-doc__sphinx-10673/report.json\n", "Missing /mnt/efs/augment/public_html/swebench/consolidated_runs/11794_11795_11796_11797_11798_11799_11800_11801_11802_11803/workspace_sphinx-doc__sphinx-8056/logs/run_evaluation/swe_work/Augment_Agent/sphinx-doc__sphinx-8056/report.json\n", "Fail to pass failure rate: 0.332\n", "Pass to pass failure rate: 0.11\n", "Fail to fail failure rate: 0.0\n", "Pass to fail failure rate: 0.0\n", "Both failure types rate: 0.066\n"]}], "source": ["from glob import glob\n", "import json\n", "from functools import lru_cache\n", "from pathlib import Path\n", "\n", "run_dir = \"/mnt/efs/augment/public_html/swebench/consolidated_runs/11794_11795_11796_11797_11798_11799_11800_11801_11802_11803/\"\n", "\n", "\n", "@lru_cache(maxsize=100)\n", "def get_example_eval_dirs(run_dir):\n", "    return glob(run_dir + \"workspace_*/logs/run_evaluation/swe_work/Augment_Agent/*/\")\n", "\n", "\n", "fail_to_pass_failure_ct = 0\n", "fail_to_fail_failure_ct = 0\n", "pass_to_pass_failure_ct = 0\n", "pass_to_fail_failure_ct = 0\n", "both_failure_types_ct = 0\n", "for example_eval_dir in get_example_eval_dirs(run_dir):\n", "    report_path = example_eval_dir + \"report.json\"\n", "\n", "    if not Path(report_path).exists():\n", "        print(f\"Missing {report_path}\")\n", "        continue\n", "\n", "    with open(report_path, \"r\") as f:\n", "        report = json.load(f)\n", "\n", "    fail_to_pass_failure_ct += int(\n", "        len(list(report.values())[0][\"tests_status\"][\"FAIL_TO_PASS\"][\"failure\"]) != 0\n", "    )\n", "    pass_to_pass_failure_ct += int(\n", "        len(list(report.values())[0][\"tests_status\"][\"PASS_TO_PASS\"][\"failure\"]) != 0\n", "    )\n", "\n", "    fail_to_fail_failure_ct += int(\n", "        len(list(report.values())[0][\"tests_status\"][\"FAIL_TO_FAIL\"][\"failure\"]) != 0\n", "    )\n", "    pass_to_fail_failure_ct += int(\n", "        len(list(report.values())[0][\"tests_status\"][\"PASS_TO_FAIL\"][\"failure\"]) != 0\n", "    )\n", "\n", "    both_failure_types_ct += int(\n", "        len(list(report.values())[0][\"tests_status\"][\"FAIL_TO_PASS\"][\"failure\"]) != 0\n", "        and len(list(report.values())[0][\"tests_status\"][\"PASS_TO_PASS\"][\"failure\"])\n", "        != 0\n", "    )\n", "\n", "\n", "print(f\"Fail to pass failure rate: {fail_to_pass_failure_ct / 500}\")\n", "print(f\"Pass to pass failure rate: {pass_to_pass_failure_ct / 500}\")\n", "print(f\"Fail to fail failure rate: {fail_to_fail_failure_ct / 500}\")\n", "print(f\"Pass to fail failure rate: {pass_to_fail_failure_ct / 500}\")\n", "print(f\"Both failure types rate: {both_failure_types_ct / 500}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
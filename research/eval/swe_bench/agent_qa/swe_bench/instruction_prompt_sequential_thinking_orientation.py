PROMPT = """
<uploaded_files>
{location}
</uploaded_files>
I've uploaded a python code repository in the directory {location} (not in /tmp/inputs). Consider the following PR description:

<pr_description>
{pr_description}
</pr_description>

Can you help me pick some tests to run to validate candidate solutions don't break existing functionality? Please identify at least 5. Also, can you figure out how to run them? "pytest" is a common test runner for
many repositories we work with, but some repositories have custom test harnesses. Once you have an answer to both of these questions, create a script called "run_regression_tests.sh" that runs all the tests you identified.
It should be an executable script that can be run with "bash run_regression_tests.sh".

Follow these steps:
1. As a first step, it would be a good idea to explore the repo to familiarize yourself with where tests are located.
2. Use terminal commands like grep/find to figure out how to run all tests in the repository. Spend some time analyzing how to run the tests and look for whether a custom harness might be needed.
4. Run the tests and make sure they pass. Use "timeout" with a timeout of 5 minutes to make sure the tests don't run for too long. If they time out, you can try to run a smaller subset of tests.
5. Create the "run_regression_tests.sh" script and make sure it runs all the tests you identified. Only include tests that are passing!

"""

#!/bin/bash

set -e
set -x # Noisy, but silent failures are a drag

BASE=/run/determined/workdir
source ${BASE}/research/gpt-neox/jobs/entrypoint_utils.sh
entrypoint_setup

mkdir -p ${BASE}/swe_work
(cd ${BASE} && python -m venv swebench_env)

SWEBENCH_REF="b10f7a570e20b191f65a45e24c165312e00d562c"
git clone https://github.com/augmentcode/SWE-bench.git ${BASE}/swebench
(source ${BASE}/swebench_env/bin/activate \
    && cd ${BASE}/swebench \
    && git checkout ${SWEBENCH_REF} \
    && pip install -e . \
    && deactivate)
SWEBENCH_SHORT_REF=$(cd ${BASE}/swebench && git rev-parse --short ${SWEBENCH_REF})

export SWEBENCH_SHORT_REF
env

apt-get install -y \
    docker-buildx \
    docker.io \
    docker

containerd > /tmp/containerd.log 2>&1 &
sleep 5

dockerd -H unix:///var/run/docker.sock \
    --containerd=/run/containerd/containerd.sock \
    --max-concurrent-downloads 1 \
    --data-root=/home/<USER>/docker > /tmp/docker.log 2>&1 &
sleep 5

gcloud auth configure-docker us-central1-docker.pkg.dev < /dev/null

pip install gql requests-toolbelt natsort

# while true; do
#     if [[ -f /tmp/debug ]]; then
#         break
#     fi
#     sleep 10
# done

agent_workdir=${BASE}/swe_work
mkdir -p ${agent_workdir}

# TODO(marcmac) metaconfig params for all these params
python ${BASE}/research/eval/swe_bench/agent_qa/swe_bench/swe_bench.py run \
    --workdir ${agent_workdir} \
    --dataset verified \
    --evaluate \
    --agent-timeout 3600 \
    --log-base /mnt/efs/augment/eval/agent_eval/exp-${DET_EXPERIMENT_ID} \
    --auth-token-file /run/determined/secrets/augment-token/token \
    --run-id exp-${DET_EXPERIMENT_ID} \
    --remove-image \
    --num-processes 8 \
    --repo pylint-dev/pylint
    # --task-id astropy__astropy-7166
    # --repo django/django
    # --repo astropy/astropy
    #--repo matplotlib/matplotlib
    # --repo pydata/xarray
    # --repo psf/requests \
    # --repo mwaskom/seaborn

    # --repo pylint-dev/pylint \
    # --repo pytest-dev/pytest \
    # --repo sphinx-doc/sphinx
    # --repo scikit-learn/scikit-learn \
    # --repo sympy/sympy

    # --repo pallets/flask \
    # --repo astropy/astropy \
    # --repo django/django \
    # --repo matplotlib/matplotlib

    # --repo sphinx-doc/sphinx

# Results will be in /run/determined/workdir/swe_work

for log in /run/determined/workdir/swe_work/workspace*/agent_log.pickle; do
    python ${BASE}/experimental/guy/agent_qa/convert_agent_log_to_html.py --pickle_log_file $log
done

python ${BASE}/research/eval/swe_bench/agent_qa/swe_bench/stage_results.py \
    /run/determined/workdir/swe_work \
    gs://gcp-us1-public-html/marcmac/swe_bench/determined/${DET_EXPERIMENT_ID}

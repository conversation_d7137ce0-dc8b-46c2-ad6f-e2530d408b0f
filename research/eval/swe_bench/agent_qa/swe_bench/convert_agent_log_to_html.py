import argparse
import pickle
from pathlib import Path

from research.agents.tools import ToolCallLogger


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--pickle_log_file",
        type=Path,
        required=True,
        help="Path to the .pickle log file",
    )

    args = parser.parse_args()

    path = args.pickle_log_file

    try:
        logger = ToolCallLogger.from_pickle_file(path)
        html = (
            logger.get_html_representation(truncate_long_outputs=False, full_page=True)
            + "\n"
        )
        html_path = path.with_suffix(".html")
        html_path.write_text(html)
    except Exception:
        print(f"Failed to load {path}")


if __name__ == "__main__":
    main()

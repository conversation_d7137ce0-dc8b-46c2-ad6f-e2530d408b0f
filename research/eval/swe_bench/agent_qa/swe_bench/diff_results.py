#!/usr/bin/env python3
"""Compare evaluation results between two locations and generate a diff report."""

import argparse
from collections import defaultdict
from pathlib import Path
import json
import re
from typing import Dict, List, Set, Tuple
from urllib.parse import urlparse

from stage_results import index_evaluations  # type: ignore


def url_to_gcs_path(url: str) -> str:
    """
    Convert a URL to a GCS path if it's a web URL or file path.

    Args:
        url: URL or GCS path

    Returns:
        GCS path
    """
    # If it's already a GCS path, return it as is
    if url.startswith("gs://"):
        return url
    if url.startswith("/"):
        return url.replace("/mnt/efs/augment/public_html/", "gs://gcp-us1-public-html/")

    # Parse the URL
    parsed_url = urlparse(url)

    # Check if it's a valid URL (has scheme and netloc)
    if parsed_url.scheme and parsed_url.netloc:
        # Check if it's a webserver.gcp-us1.r.augmentcode.com URL
        if "webserver.gcp-us1.r.augmentcode.com" in parsed_url.netloc:
            # Extract the path from the URL
            path = parsed_url.path.lstrip("/")

            # Convert to GCS path with the correct bucket prefix
            gcs_path = f"gs://gcp-us1-public-html/{path}"
            print(f"Converting URL {url} to GCS path {gcs_path}")
            return gcs_path

    # If it's not a recognized URL format, return it unchanged
    return url


def diff_evaluations(location1: str, location2: str) -> Dict:
    """
    Compare evaluation results between two locations.

    Args:
        location1: First GCS path or URL containing evaluation results
        location2: Second GCS path or URL containing evaluation results

    Returns:
        Dictionary containing the differences between the two locations
    """
    # Convert URLs to GCS paths if needed
    gcs_location1 = url_to_gcs_path(location1)
    gcs_location2 = url_to_gcs_path(location2)

    print(f"Indexing evaluations from {gcs_location1}")
    results1 = index_evaluations([gcs_location1])

    print(f"Indexing evaluations from {gcs_location2}")
    results2 = index_evaluations([gcs_location2])

    # Get all task IDs from both locations
    all_ids = set(results1.keys()) | set(results2.keys())

    # Initialize diff dictionary
    diff = {
        "summary": {
            "location1": location1,  # Store original location (URL or GCS path)
            "location2": location2,  # Store original location (URL or GCS path)
            "gcs_location1": gcs_location1,  # Store converted GCS path
            "gcs_location2": gcs_location2,  # Store converted GCS path
            "total_tasks": len(all_ids),
            "only_in_location1": 0,
            "only_in_location2": 0,
            "in_both_locations": 0,
            "resolution_changes": {
                "resolved_to_unresolved": 0,
                "unresolved_to_resolved": 0,
                "no_change": 0,
            },
        },
        "tasks": {},
    }

    # Compare each task
    for task_id in sorted(all_ids):
        task_diff = {}

        # Check if task exists in both locations
        in_location1 = task_id in results1
        in_location2 = task_id in results2

        if in_location1 and in_location2:
            diff["summary"]["in_both_locations"] += 1

            # Check resolution status
            resolved1 = bool(results1[task_id]["resolved_ids"])
            resolved2 = bool(results2[task_id]["resolved_ids"])

            if resolved1 and not resolved2:
                diff["summary"]["resolution_changes"]["resolved_to_unresolved"] += 1
                task_diff["resolution_change"] = "resolved_to_unresolved"
            elif not resolved1 and resolved2:
                diff["summary"]["resolution_changes"]["unresolved_to_resolved"] += 1
                task_diff["resolution_change"] = "unresolved_to_resolved"
            else:
                diff["summary"]["resolution_changes"]["no_change"] += 1
                task_diff["resolution_change"] = "no_change"

            # Compare agent return codes
            rc1 = (
                results1[task_id]["agent_rc"][0]
                if results1[task_id]["agent_rc"]
                else None
            )
            rc2 = (
                results2[task_id]["agent_rc"][0]
                if results2[task_id]["agent_rc"]
                else None
            )

            if rc1 != rc2:
                task_diff["agent_rc_change"] = {"location1": rc1, "location2": rc2}

            # Store paths for reference
            task_diff["location1"] = {
                "workspace": results1[task_id]["directory_present_ids"][0]
                if results1[task_id]["directory_present_ids"]
                else None,
                "evaluations": results1[task_id]["evaluations"][0]
                if results1[task_id]["evaluations"]
                else None,
            }
            task_diff["location2"] = {
                "workspace": results2[task_id]["directory_present_ids"][0]
                if results2[task_id]["directory_present_ids"]
                else None,
                "evaluations": results2[task_id]["evaluations"][0]
                if results2[task_id]["evaluations"]
                else None,
            }

        elif in_location1:
            diff["summary"]["only_in_location1"] += 1
            task_diff["only_in"] = "location1"
            task_diff["location1"] = {
                "workspace": results1[task_id]["directory_present_ids"][0]
                if results1[task_id]["directory_present_ids"]
                else None,
                "evaluations": results1[task_id]["evaluations"][0]
                if results1[task_id]["evaluations"]
                else None,
                "resolved": bool(results1[task_id]["resolved_ids"]),
                "agent_rc": results1[task_id]["agent_rc"][0]
                if results1[task_id]["agent_rc"]
                else None,
            }

        elif in_location2:
            diff["summary"]["only_in_location2"] += 1
            task_diff["only_in"] = "location2"
            task_diff["location2"] = {
                "workspace": results2[task_id]["directory_present_ids"][0]
                if results2[task_id]["directory_present_ids"]
                else None,
                "evaluations": results2[task_id]["evaluations"][0]
                if results2[task_id]["evaluations"]
                else None,
                "resolved": bool(results2[task_id]["resolved_ids"]),
                "agent_rc": results2[task_id]["agent_rc"][0]
                if results2[task_id]["agent_rc"]
                else None,
            }

        # Add task diff to the overall diff
        diff["tasks"][task_id] = task_diff

    return diff


def generate_report(diff: Dict, output_path: str = "") -> str:
    """
    Generate a human-readable report from the diff dictionary.

    Args:
        diff: Dictionary containing the differences between two locations
        output_path: Optional path to save the report to

    Returns:
        String containing the report
    """
    summary = diff["summary"]

    report = [
        "# Evaluation Results Comparison Report",
        "",
        f"Location 1: {summary['location1']}",
        f"Location 2: {summary['location2']}",
        "",
        "## Summary",
        "",
        f"Total tasks: {summary['total_tasks']}",
        f"Tasks only in location 1: {summary['only_in_location1']}",
        f"Tasks only in location 2: {summary['only_in_location2']}",
        f"Tasks in both locations: {summary['in_both_locations']}",
        "",
        "### Resolution Changes",
        "",
        f"Tasks that changed from resolved to unresolved: {summary['resolution_changes']['resolved_to_unresolved']}",
        f"Tasks that changed from unresolved to resolved: {summary['resolution_changes']['unresolved_to_resolved']}",
        f"Tasks with no change in resolution status: {summary['resolution_changes']['no_change']}",
        "",
        "## Task Details",
        "",
    ]

    # Group tasks by change type for better readability
    resolved_to_unresolved = []
    unresolved_to_resolved = []
    only_in_location1 = []
    only_in_location2 = []
    other_changes = []

    for task_id, task_diff in diff["tasks"].items():
        if "only_in" in task_diff:
            if task_diff["only_in"] == "location1":
                only_in_location1.append(task_id)
            else:
                only_in_location2.append(task_id)
        elif "resolution_change" in task_diff:
            if task_diff["resolution_change"] == "resolved_to_unresolved":
                resolved_to_unresolved.append(task_id)
            elif task_diff["resolution_change"] == "unresolved_to_resolved":
                unresolved_to_resolved.append(task_id)
            elif "agent_rc_change" in task_diff:
                other_changes.append(task_id)

    # Add tasks that changed from resolved to unresolved
    if resolved_to_unresolved:
        report.append("### Tasks that changed from resolved to unresolved")
        report.append("")
        for task_id in sorted(resolved_to_unresolved):
            task_diff = diff["tasks"][task_id]
            report.append(f"- **{task_id}**")
            if "agent_rc_change" in task_diff:
                report.append(
                    f"  - Agent RC changed: {task_diff['agent_rc_change']['location1']} → {task_diff['agent_rc_change']['location2']}"
                )
            report.append(f"  - Location 1: {task_diff['location1']['workspace']}")
            report.append(f"  - Location 2: {task_diff['location2']['workspace']}")
            report.append("")

    # Add tasks that changed from unresolved to resolved
    if unresolved_to_resolved:
        report.append("### Tasks that changed from unresolved to resolved")
        report.append("")
        for task_id in sorted(unresolved_to_resolved):
            task_diff = diff["tasks"][task_id]
            report.append(f"- **{task_id}**")
            if "agent_rc_change" in task_diff:
                report.append(
                    f"  - Agent RC changed: {task_diff['agent_rc_change']['location1']} → {task_diff['agent_rc_change']['location2']}"
                )
            report.append(f"  - Location 1: {task_diff['location1']['workspace']}")
            report.append(f"  - Location 2: {task_diff['location2']['workspace']}")
            report.append("")

    # Add tasks only in location 1
    if only_in_location1:
        report.append("### Tasks only in location 1")
        report.append("")
        for task_id in sorted(only_in_location1):
            task_diff = diff["tasks"][task_id]
            report.append(f"- **{task_id}**")
            report.append(f"  - Resolved: {task_diff['location1']['resolved']}")
            report.append(f"  - Agent RC: {task_diff['location1']['agent_rc']}")
            report.append(f"  - Workspace: {task_diff['location1']['workspace']}")
            report.append("")

    # Add tasks only in location 2
    if only_in_location2:
        report.append("### Tasks only in location 2")
        report.append("")
        for task_id in sorted(only_in_location2):
            task_diff = diff["tasks"][task_id]
            report.append(f"- **{task_id}**")
            report.append(f"  - Resolved: {task_diff['location2']['resolved']}")
            report.append(f"  - Agent RC: {task_diff['location2']['agent_rc']}")
            report.append(f"  - Workspace: {task_diff['location2']['workspace']}")
            report.append("")

    # Add tasks with other changes
    if other_changes:
        report.append("### Tasks with other changes")
        report.append("")
        for task_id in sorted(other_changes):
            task_diff = diff["tasks"][task_id]
            report.append(f"- **{task_id}**")
            if "agent_rc_change" in task_diff:
                report.append(
                    f"  - Agent RC changed: {task_diff['agent_rc_change']['location1']} → {task_diff['agent_rc_change']['location2']}"
                )
            report.append(f"  - Location 1: {task_diff['location1']['workspace']}")
            report.append(f"  - Location 2: {task_diff['location2']['workspace']}")
            report.append("")

    report_text = "\n".join(report)

    # Save report to file if output path is provided
    if output_path:
        with open(output_path, "w") as f:
            f.write(report_text)
        print(f"Report saved to {output_path}")

    return report_text


def save_json_diff(diff: Dict, output_path: str):
    """
    Save the diff dictionary to a JSON file.

    Args:
        diff: Dictionary containing the differences between two locations
        output_path: Path to save the JSON file to
    """
    with open(output_path, "w") as f:
        json.dump(diff, f, indent=2)
    print(f"JSON diff saved to {output_path}")


def test_url_to_gcs_path():
    """
    Test the url_to_gcs_path function with various inputs.
    """
    test_cases = [
        # GCS paths should remain unchanged
        ("gs://bucket/path", "gs://bucket/path"),
        # Webserver URLs should be converted to GCS paths
        (
            "https://webserver.gcp-us1.r.augmentcode.com/swebench/runs/123/",
            "gs://gcp-us1-public-html/swebench/runs/123/",
        ),
        (
            "https://webserver.gcp-us1.r.augmentcode.com/swebench/consolidated_runs/11906_11907_11908_11909_11910_11911_11912_11913_11914_11915/",
            "gs://gcp-us1-public-html/swebench/consolidated_runs/11906_11907_11908_11909_11910_11911_11912_11913_11914_11915/",
        ),
        # URLs without leading slash in path
        (
            "https://webserver.gcp-us1.r.augmentcode.com/path/to/data",
            "gs://gcp-us1-public-html/path/to/data",
        ),
        # URLs with leading slash in path
        (
            "https://webserver.gcp-us1.r.augmentcode.com//path/with/extra/slash",
            "gs://gcp-us1-public-html/path/with/extra/slash",
        ),
        # Non-webserver URLs should remain unchanged
        ("https://example.com/path", "https://example.com/path"),
        # Non-URL strings should remain unchanged
        ("not-a-url", "not-a-url"),
    ]

    passed = 0
    failed = 0

    for input_url, expected_output in test_cases:
        actual_output = url_to_gcs_path(input_url)
        if actual_output == expected_output:
            print(f"✓ PASS: '{input_url}' -> '{actual_output}'")
            passed += 1
        else:
            print(
                f"✗ FAIL: '{input_url}' -> '{actual_output}' (expected: '{expected_output}')"
            )
            failed += 1

    print(f"\nTest results: {passed} passed, {failed} failed")
    return passed == len(test_cases)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Compare evaluation results between two locations"
    )
    parser.add_argument(
        "--test",
        action="store_true",
        help="Run tests for the URL to GCS path conversion",
    )
    parser.add_argument(
        "location1",
        type=str,
        nargs="?",
        help="First location containing evaluation results (GCS path or webserver URL)",
    )
    parser.add_argument(
        "location2",
        type=str,
        nargs="?",
        help="Second location containing evaluation results (GCS path or webserver URL)",
    )
    parser.add_argument("--output", "-o", type=str, help="Path to save the report to")
    parser.add_argument("--json", "-j", type=str, help="Path to save the JSON diff to")
    args = parser.parse_args()

    # Run tests if requested
    if args.test:
        test_url_to_gcs_path()
        exit(0)

    # Ensure both locations are provided for comparison
    if not args.location1 or not args.location2:
        parser.error("Both location1 and location2 are required for comparison")

    diff = diff_evaluations(args.location1, args.location2)

    # Generate and print report
    report = generate_report(diff, args.output)
    if not args.output:
        print(report)

    # Save JSON diff if requested
    if args.json:
        save_json_diff(diff, args.json)

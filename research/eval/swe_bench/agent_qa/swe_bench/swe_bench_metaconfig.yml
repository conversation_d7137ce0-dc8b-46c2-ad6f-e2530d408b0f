# includes is an ordered list of gpt-neox config files to be loaded
includes:
# determined is a dictionary of determined.ai specific arguments that have no corresponding gpt-neox arguments
determined:
  name: null
  description: null
  workspace: Dev
  project: Default
  max_restarts: 0
# augment is a dictionary of augment specific arguments for our code extensions
augment:
  privileged: True
  # user_include_files:          # Absolute paths or relative to AUGMENT_ROOT
  # - experimental/me/include_file
  slack_notifications:
    enabled: False             # Enable slack notifications
    slack_user: Uxxxxxxxxxx    # Set to your slack user ID to send notifications.
    notify_validations: False  # Set to true to send a message on every validation pass - may be noisy
  # Common args for both training and evaluation
  podspec_path: "1xH100.yaml"  # Path to the podspec file.  Absolute, or relative to "templates"
  gpu_count: 1  # How many GPUs to ask for
  save_trial_best: 0  # How many of the best checkpoints to save
  secrets:
  - name: augment-token
    secret: augment-api-key  # pragma: allowlist secret


  # Environment variables to pass to every worker process.
  environment_variables:
  #   ENV_VAR: "<value>"

  entrypoint: "/run/determined/workdir/research/eval/swe_bench/agent_qa/swe_bench/determined_wrapper.sh"

  # service_account_name: spark-sa

# overrides is a dictionary of gpt-neox args to override the values in the above list of included config files
overrides:
  # WandB options. WANDB_API_KEY will come from the environment, or ~/.netrc if you are logged in.
  wandb_name: trial-$TRIAL_ID
  wandb_project: my_project   # This probably needs to already exist in your wandb dashboard
  wandb_group: exp-$EXPERIMENT_ID
  # wandb_team: my_team

  # save: is ignored by determined; checkpoints are saved in s3
  # save: /mnt/efs/augment/checkpoints/pref_signal/test1_ft_beta0.95_s2048_lr1.6e-5_const

  train_batch_size: 288
  train_micro_batch_size_per_gpu: 12
  gradient_accumulation_steps: 24

  eval_interval: 20
  early_stopping: False
  early_stopping_threshold: 0.005

  train_iters: 20
  lr_decay_iters: 100  # If not set, defaults to train_iters
  warmup: 0.
  lr_decay_style: constant

  optimizer:
      params:
          betas:
          - 0.9
          - 0.95
          eps: 1.0e-08
          lr: 1.6e-05
      type: Adam

  save_interval: 10
  keep_last_n_checkpoints: 2
  # to keep all checkpoints, also disable determined.enable_checkpoint_gc
  #keep_last_n_checkpoints: null
  no_save_optim: False

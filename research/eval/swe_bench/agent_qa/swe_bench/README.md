# Setup

## SWE-bench

Install SWE-bench per https://github.com/swe-bench/SWE-bench/blob/main/assets/evaluation.md

I got failures on any commit *after* `d3e2207cae7bb2920987ff453062e480dabd74a8` due to modal errors.  (As of 20250118).  We're not using modal (docker in the cloud) so I don't consider this a problem.

This requires docker to be available to run the evaluation step.

### Verified dataset

By default, we use the SWE-bench Verified dataset.  This is a subset of the full dataset that has been verified to have a reasonable solution based on the evaluation harness.  (https://openai.com/index/introducing-swe-bench-verified/)

## Docker

You need a devpod with docker support.
You can create one with this command:
`augi devpod apply --docker ${pod-name} --cpu-count=4 --home-size=500 -c gcp-us1 --allow-reboot`
Devpods with docker support might not have efs mounted which is needed for swe_bench script
<NAME_EMAIL> if that's the case.

# Running the benchmark with the Augment Agent.

**READ THE PAINFUL LESSONS SECTION BEFORE YOU RUN THIS**

*Don't run with high parallelism* or the Anthropic API will just rate limit you (and everyone else).  We use the direct Anthropic client by default (not the Vertex client).

./swe_bench.py -h for options

*NOTE on the first time you run the benchmark*
`experimental/guy/agent_qa/interactive_agent.sh` will offer to install requirements for you on the first run.  Do this by hand before you run the benchmark, to avoid hangs.  This must be run from a working directory inside the augment repo, for bazel.  TODO(marcmac) fix this.

### Run a single repo
./swe_bench run --workdir /home/<USER>/agent/swe_bench/test_run_sympy_sympy_1 --repo sympy/sympy

### Run a slice of all repos (2 per repo)
./swe_bench run  --sample-size 2 --workdir /home/<USER>/agent/swe_bench/test_run_sympy_sympy_1

# Painful lessons

*Don't use a workspace in your augment repo* - if the agent tries to check something out and fails,
it may go on to run additional git commands in the workdir anyway, which will affect your repository.
(Ask me how I know).

*TWICE* the agent has deleted my entire home directory while running unattended; I now run it
- in a separate filesystem
- invoked from a working directory outside of my git repository.
- logging to a shared mount so failures can be analyzed.

Also, I make sure every edit I have locally is pushed before I run.

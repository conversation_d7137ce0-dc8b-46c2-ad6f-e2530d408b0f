PROMPT = """
<uploaded_files>
{location}
</uploaded_files>
I've uploaded a python code repository in the directory {location} (not in /tmp/inputs).

<pr_description>
{pr_description}
</pr_description>

I have addressed the <pr_description> already, but some tests are failing. Can you help me help me fix these tests, while still addressing the PR description?
Also, please revert any changes to any test_*.py files in the git diff.
I've already taken care of all changes to any of the test files in the codebase. This means you DON'T have to modify the testing logic or any of the tests in any way!

Follow these steps to resolve the issue:
1. As a first step, revert any changes to any test_*.py files in the git diff.
2. Create a script reproduce_pr_task.py to reproduce the error and execute it with `python reproduce_pr_task.py` using the BashTool. While we are focused on fixing regressions on existing tests, we want to make sure we don't break new functionality.
3. Find the relevant failing tests and run them to see the failures. Use "git diff" to see the recent changes, which will help you identify possible failing tests.
4. Use the sequential_thinking tool to plan any fixes.
5. Edit the sourcecode of the repo to resolve the failing tests while making sure we still pass reproduce_pr_task.py. Repeat steps 3-5 until all tests are passing and reproduce_pr_task.py is still passing.
6. Re-run all tests to make sure that all previously failing tests are now passing.


GUIDE FOR HOW TO USE "sequential_thinking" TOOL:
- Your thinking should be thorough and so it's fine if it's very long. Set totalThoughts to at least 5, but setting it up to 25 is fine as well. You'll need more total thoughts when you are considering multiple possible solutions or root causes for an issue.
- Use this tool as much as you find necessary to improve the quality of your answers.
- You can run bash commands (like tests, a reproduction script, or 'grep'/'find' to find relevant context) in between thoughts.
- The sequential_thinking tool can help you break down complex problems, analyze issues step-by-step, and ensure a thorough approach to problem-solving.
- Don't hesitate to use it multiple times throughout your thought process to enhance the depth and accuracy of your solutions.

TIPS:
- Do NOT make tool calls inside thoughts passed to sequential_thinking tool. For example, do NOT do this: {{'thought': 'I need to look at the actual implementation of `apps.get_models()` in this version of Django to see if there\'s a bug. Let me check the Django apps module:\n\n<function_calls>\n<invoke name="str_replace_editor">\n<parameter name="command">view</parameter>\n<parameter name="path">django/apps/registry.py</parameter></invoke>', 'path': 'django/apps/registry.py'}}
- Do NOT run "git init" or "git commit" or "git add". Do NOT commit your changes.
- Respect the tool specifications. If a field is required, make sure to provide a value for it. For example "thoughtNumber" is required by the sequential_thinking tool.
- When you run "ls" or variants of it, you may see a symlink like "fileA -> /data/vol/fileA". You can safely ignore the symlink and just use "fileA" as the path when read, editing, or executing the file.
- When you need to find information about the codebase, use "grep" and "find" to search for relevant files and code with the bash tool
- Use your bash tool to set up any necessary environment variables, such as those needed to run tests.
"""

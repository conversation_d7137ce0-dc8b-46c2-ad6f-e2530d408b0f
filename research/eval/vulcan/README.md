# Vulcan: A model checklist to evaluate basic code generation quality

The dataset includes hand-generated examples aimed at evaluating a model's
ability to generate high-quality completions in a variety of settings. The goal
is to have a small number of short, easy to understand examples that clearly
test a code generation model's core abilities.

<PERSON><PERSON><PERSON>, from Roman mythology, is a deity associated with craftsmanship,
blacksmithing, and fire. He is often depicted as a skilled and talented artisan,
known for his ability to forge extraordinary creations.

# Test Format
Tests in the Vulcan suite are checked into the `testdata/` directory, split by language,
with one test per file. Each test is structured as follows (example in Python):
```
# ---
# tags: [foo, bar]
# description: A short description of the expected behavior of this code.
# expected: |
#   expected implementation
# ---
# Everything below this line is provided as is to the model
def foo(n):
    # We start completing at the token position of `<FILL-HERE>`.
    <FILL-HERE>

primes = foo(100)
```

{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Vulcan: A model checklist to evaluate basic code generation quality\n", "\n", "This notebook runs the examples in the Vulcan test suite (see `//augment/research/eval/vulcan/testdata`).\n", "\n", "Intermediate results and takeaways are logged on [Notion](https://www.notion.so/Vulcan-Test-Suite-13fb0e07af6a45089908f20b2da9faaf) and [this spreadsheet](https://docs.google.com/spreadsheets/d/1I9Tc_GDn8GbKE2vS04rI6BVIypusd9pYa3KWfcpBViU/edit#gid=962563454)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load models.\n", "import sys\n", "import logging\n", "logging.basicConfig(level=logging.WARNING)\n", "\n", "from research.models.all_models import get_model, list_models, GenerationOptions\n", "list_models()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the tests\n", "from research.eval.vulcan import load_tests, VulcanTest\n", "from research.eval.patch_lib import Patch\n", "# NOTE: Set test_pattern below to load a specific test.\n", "TESTS = load_tests()\n", "print(f\"Loaded {len(TESTS)} tests.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display tests\n", "import datetime\n", "import jsonlines\n", "import pathlib\n", "from termcolor import colored\n", "\n", "VULCAN_EVAL_ROOT = pathlib.Path(\"/mnt/efs/augment/eval/vulcan/\")\n", "\n", "\n", "def display_output(test: VulcanTest, model_output: str, extra_header: str = \"\"):\n", "    correct = model_output.strip() == test.expected.strip()\n", "\n", "    name = pathlib.Path(test.filename).name\n", "\n", "    print(\"========================================================================================\")\n", "    print(f\"EXAMPLE: {name} {'✓' if correct else '✗'} | {extra_header}\")\n", "    print(\"----------------------------------------------------------------------------------------\")\n", "    print(\n", "        colored(test.prefix, \"blue\") +\n", "        colored(model_output, \"white\", \"on_black\") +\n", "        colored(test.suffix, \"blue\")\n", "    )\n", "    print(\"========================================================================================\")\n", "    print()\n", "    print()\n", "\n", "def run_all_tests(model, model_name: str,\n", "                  tests:list[VulcanTest],\n", "                  save_results: bool = False,\n", "                  n_samples: int = 1,\n", "                  temperature: float = 0,\n", "                  ):\n", "    patches = []\n", "\n", "    print(\"========================================================================================\")\n", "    print(f\"MODEL: {model_name}\")\n", "    print(\"----------------------------------------------------------------------------------------\")\n", "    for test in tests:\n", "        for sample in range(n_samples):\n", "            predicted = test.run(model, GenerationOptions(temperature=temperature))\n", "            model_output = test.as_patch(predicted)\n", "            patches.append(model_output)\n", "            display_output(test, model_output.patch_content,\n", "                           extra_header=f\"model={model_name}, temp={temperature}, sample={sample}\")\n", "\n", "    if save_results and patches:\n", "        # Save output to a patches file.\n", "        output = (VULCAN_EVAL_ROOT / f\"{datetime.date.today():%Y%m%d}_{model_name}.jsonl\")\n", "        with output.open(\"w\") as fp:\n", "            jsonlines.Writer(fp).write_all([patch.to_json() for patch in patches])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_name = \"starcoderbase_1b\"\n", "model = get_model(model_name)\n", "model.load()\n", "run_all_tests(\n", "    model, model_name, TESTS, save_results=True,\n", "    n_samples=1, temperature=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Try out your own test.\n", "from textwrap import dedent\n", "\n", "code = dedent(\"\"\"\\\n", "    import test\n", "    # BEGIN\n", "    this is a test\n", "    # END\n", "\"\"\")\n", "\n", "test = VulcanTest.from_text(code, \"test.py\")\n", "\n", "model_output = test.as_patch(test.run(model))\n", "display_output(test, model_output.patch_content,\n", "                extra_header=f\"model={model_name}\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
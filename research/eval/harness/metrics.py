"""Metrics for systems."""

import dataclasses
import logging
from collections import defaultdict
from collections.abc import Mapping, MutableMapping
from dataclasses import dataclass, field
from typing import Literal, Optional

import editdistance
import fast_diff_match_patch as dmp
import numpy as np
import torch
import torch.nn.functional as F
from dataclasses_json import DataClassJsonMixin

from base.languages.language_guesser import guess_language
from base.languages.languages import to_vscode_name
from base.languages.unit_test_guesser import is_unit_test
from research.core.chat_prompt_input import ResearchChatPromptInput
from research.core.edit_prompt_input import ResearchEditPromptInput
from research.core.model_input import ModelInput
from research.core.utils_for_str import get_first_n_lines, max_common_prefix_length
from research.eval.harness.systems.abs_system import (
    ChatSystem,
    CodeCompleteInput,
    CodeCompleteSystem,
    CodeInstructInput,
    CodeInstructSystem,
)
from research.models import ModelForwardOutput

logger = logging.getLogger(__name__)


def whitespace_norm(code: str):
    """Normalize the code by calling strip on each line and dropping empty lines."""
    lines = [line.strip() for line in code.splitlines() if line.strip()]
    return "\n".join(lines)


def compute_exact_match(
    target: str, predictions: list[str], normalize_whitespace: bool = True
) -> float:
    """Return whether any predicted string equals the target.

    The strings are normalized such that empty lines shouldn't be counted.
    """
    if normalize_whitespace:
        target = whitespace_norm(target)
        predictions = [whitespace_norm(p) for p in predictions]

    return float(any(target == prediction for prediction in predictions))


def compute_edit_similarity(
    target: str, predictions: list[str], normalize_whitespace: bool = True
) -> float:
    """Return the max edit similarity between any predicted string with the target.

    The strings are normalized such that empty lines shouldn't be counted.
    """
    if normalize_whitespace:
        target = whitespace_norm(target)
        predictions = [whitespace_norm(p) for p in predictions]

    ES_scores: list[float] = []
    for prediction in predictions:
        levenstein_distance = editdistance.eval(target, prediction)
        max_chars = max(len(target), len(prediction))
        # We add one to handle cases where one str might be empty. This acts as if
        # each string had a special terminator character at the end. And in the case
        # that target is empty, we penalize longer predictions.
        score = 1 - levenstein_distance / (max_chars + 1)
        ES_scores.append(score)
    return max(ES_scores)


@dataclass(frozen=True, order=True)
class PrecisionRecallMetrics:
    """Class for computing f1, precision, and recall.

    Ordered so that max() will return the best f1 score.
    To handle cases where prediction or target are empty, in many cases we may add one
    to both the numerator and denominator.
    """

    f1: float
    """F1: number of matches divided by mean length of prediction and target."""

    precision: float
    """Precision: number of matches divided by prediction length."""

    recall: float
    """Recall: number of matches divided by target length"""


def compute_prefix_precision_recall(
    target: str, predictions: list[str]
) -> PrecisionRecallMetrics:
    """Return the max prefix f1 and its associated precision and recall between any
    predicted string with the target."""

    scores = []
    for prediction in predictions:
        matches = max_common_prefix_length(target, prediction)
        precision = (matches + 1) / (len(prediction) + 1)
        recall = (matches + 1) / (len(target) + 1)
        f1 = 2 * (matches + 1) / (len(target) + len(prediction) + 2)
        scores.append(PrecisionRecallMetrics(f1=f1, precision=precision, recall=recall))

    return max(scores)


def compute_lcs_precision_recall(
    target: str, predictions: list[str]
) -> PrecisionRecallMetrics:
    """Return the max longest common subsequence (lcs) f1 and its associated precision
    and recall between any predicted string with the target.

    This metric is basically ROUGE-L, but at the character level.
    """

    scores = []
    for prediction in predictions:
        # fast_diff_match_patch is a fast C++ implementation of Myers diff algorithm.
        # Returns a list of tuples (op_type, count) where op_type = "=" for matches.
        # We turn off all the tricks and post-processing to get the actual LCS.
        diff_ops = dmp.diff(target, prediction, checklines=False, cleanup="No")
        matches = sum([count for op_type, count in diff_ops if op_type == "="])
        precision = (matches + 1) / (len(prediction) + 1)
        recall = (matches + 1) / (len(target) + 1)
        f1 = 2 * (matches + 1) / (len(target) + len(prediction) + 2)
        scores.append(PrecisionRecallMetrics(f1=f1, precision=precision, recall=recall))

    return max(scores)


def compute_mean_log_likelihood(
    logits: torch.Tensor, labels: torch.Tensor, target_mask: torch.Tensor
) -> float:
    labels = labels.masked_fill(target_mask.logical_not(), -100)
    return -F.cross_entropy(logits, labels).item()


def compute_token_accuracy(
    logits: torch.Tensor, labels: torch.Tensor, target_mask: torch.Tensor
) -> float:
    accuracy = (logits.argmax(dim=-1) == labels)[target_mask]
    accuracy = accuracy.float().mean().item()
    return accuracy


def safe_log_likelihood(system: CodeCompleteSystem, model_input: ModelInput):
    """Version of log_likelihood that doesn't throw exceptions."""
    try:
        assert model_input.target is not None
        return (
            system.log_likelihood_continuation(model_input, model_input.target) or 0.0
        )
    except NotImplementedError:
        # Some of the systems (e.g. based on Remote llama.cpp) does not
        # support log_likelihood_continuation method.
        return 0.0


def safe_edits_log_likelihood(
    system: CodeInstructSystem,
    model_input: ResearchEditPromptInput,
):
    """Version of log_likelihood that doesn't throw exceptions."""
    try:
        assert model_input.updated_code is not None
        return (
            system.log_likelihood_continuation(model_input, model_input.updated_code)
            or 0.0
        )
    except NotImplementedError:
        # Some of the systems (e.g. based on Remote llama.cpp) does not
        # support log_likelihood_continuation method.
        return 0.0


def safe_chat_log_likelihood(
    system: ChatSystem,
    model_input: ResearchChatPromptInput,
):
    """Version of log_likelihood that doesn't throw exceptions."""
    try:
        assert model_input.target is not None
        return (
            system.log_likelihood_continuation(model_input, model_input.target) or 0.0
        )
    except NotImplementedError:
        # Some of the systems (e.g. based on Remote llama.cpp) does not
        # support log_likelihood_continuation method.
        return 0.0


def safe_forward_pass(
    system: CodeCompleteSystem, model_input: ModelInput
) -> Optional[list[ModelForwardOutput]]:
    """Version of forward_pass that doesn't throw exceptions.

    Note: logits may be large, try to release memory when finished processing.
    Note: if running with empty target, you likely want to skip computation.
    """
    try:
        return system.forward_pass([model_input])
    except NotImplementedError:
        return None


def safe_edits_forward_pass(
    system: CodeInstructSystem, model_input: ResearchEditPromptInput
) -> Optional[list[ModelForwardOutput]]:
    """Version of forward_pass that doesn't throw exceptions.

    Note: logits may be large, try to release memory when finished processing.
    Note: if running with empty target, you likely want to skip computation.
    """
    try:
        return system.forward_pass([model_input])
    except NotImplementedError:
        return None


def safe_chat_forward_pass(
    system: ChatSystem, model_input: ResearchChatPromptInput
) -> Optional[list[ModelForwardOutput]]:
    """Version of forward_pass that doesn't throw exceptions for chat tasks."""
    try:
        return system.forward_pass([model_input])
    except NotImplementedError:
        return None


@dataclass
class ForwardMetrics:
    """Output class for metrics collected from a system forward pass."""

    log_likelihood: float
    """The mean log likelihood of the target."""

    token_accuracy: float
    """The token accuracy of the target."""


# This and safe_edit_forward_metrics look exactly the same. Can they be merged?
def safe_forward_metrics(
    system: CodeCompleteSystem,
    model_input: CodeCompleteInput,
) -> ForwardMetrics:
    """Get log_likelihood and token_accuracy safely."""
    # If no target, skip computation and return max values.
    if not model_input.target and "target_tokens" not in model_input.extra:
        return ForwardMetrics(log_likelihood=0.0, token_accuracy=1.0)

    outputs = safe_forward_pass(system, model_input)

    # Try fallback on log likelihood, and return special value for accuracy.
    if outputs is None:
        if model_input.target is None:  # highly possible the input has target_tokens
            return ForwardMetrics(log_likelihood=0.0, token_accuracy=1.0)
        else:
            return ForwardMetrics(safe_log_likelihood(system, model_input), -1.0)

    outputs = outputs[0]
    # If somehow tokenized a non-empty target to empty tokens, return max values.
    if outputs.target_mask.long().sum().item() == 0:
        return ForwardMetrics(log_likelihood=0.0, token_accuracy=1.0)

    mean_ll = compute_mean_log_likelihood(
        outputs.logits, outputs.label_tokens, outputs.target_mask
    )
    token_acc = compute_token_accuracy(
        outputs.logits, outputs.label_tokens, outputs.target_mask
    )

    # Release memory, in case anyone tries to refactor this code elsewhere.
    del outputs
    return ForwardMetrics(log_likelihood=mean_ll, token_accuracy=token_acc)


def safe_edits_forward_metrics(
    system: CodeInstructSystem,
    model_input: CodeInstructInput,
) -> ForwardMetrics:
    """Get log_likelihood and token_accuracy safely."""
    # If no target, skip computation and return max values.
    if not model_input.updated_code:
        return ForwardMetrics(log_likelihood=0.0, token_accuracy=1.0)

    outputs = safe_edits_forward_pass(system, model_input)

    # Try fallback on log likelihood, and return special value for accuracy.
    if outputs is None:
        return ForwardMetrics(safe_edits_log_likelihood(system, model_input), -1.0)

    outputs = outputs[0]
    # If somehow tokenized a non-empty target to empty tokens, return max values.
    if outputs.target_mask.long().sum().item() == 0:
        return ForwardMetrics(log_likelihood=0.0, token_accuracy=1.0)

    mean_ll = compute_mean_log_likelihood(
        outputs.logits, outputs.label_tokens, outputs.target_mask
    )
    token_acc = compute_token_accuracy(
        outputs.logits, outputs.label_tokens, outputs.target_mask
    )

    # Release memory, in case anyone tries to refactor this code elsewhere.
    del outputs
    return ForwardMetrics(log_likelihood=mean_ll, token_accuracy=token_acc)


def chat_safe_forward_metrics(
    system: ChatSystem,
    model_input: ResearchChatPromptInput,
) -> ForwardMetrics:
    """Get log_likelihood and token_accuracy safely for chat tasks."""

    # If no target, skip computation and return max values.
    if not model_input.target:
        return ForwardMetrics(log_likelihood=0.0, token_accuracy=1.0)

    outputs = safe_chat_forward_pass(system, model_input)

    # Try fallback on log likelihood, and return special value for accuracy.
    if outputs is None:
        return ForwardMetrics(safe_chat_log_likelihood(system, model_input), -1.0)

    outputs = outputs[0]
    # If somehow tokenized a non-empty target to empty tokens, return max values.
    if outputs.target_mask.long().sum().item() == 0:
        return ForwardMetrics(log_likelihood=0.0, token_accuracy=1.0)

    mean_ll = compute_mean_log_likelihood(
        outputs.logits, outputs.label_tokens, outputs.target_mask
    )
    token_acc = compute_token_accuracy(
        outputs.logits, outputs.label_tokens, outputs.target_mask
    )

    # Release memory, in case anyone tries to refactor this code elsewhere.
    del outputs
    return ForwardMetrics(log_likelihood=mean_ll, token_accuracy=token_acc)


@dataclass(frozen=True, order=True)
class DatumTag(DataClassJsonMixin):
    """A tag, used to group examples. Each datum can have multiple tags."""

    category: str
    """The tag category, e.g. "lang"."""

    value: str
    """The tag value, e.g. "python"."""


@dataclass(frozen=True)
class CodeCompleteMetrics(DataClassJsonMixin):
    """The metrics for code completion tasks.

    All member variables should be of type `float | None`.
    """

    exact_match: float | None
    """Exact match."""

    exact_match_1_line: float | None
    """Exact match after truncating to one line."""

    prefix_f1: float | None
    """Prefix F1."""

    prefix_precision: float | None
    """Prefix precision."""

    prefix_recall: float | None
    """Prefix recall."""

    lcs_f1: float | None
    """Longest common subsequence F1."""

    lcs_precision: float | None
    """Longest common subsequence precision."""

    lcs_recall: float | None
    """Longest common subsequence recall."""

    log_likelihood: float | None
    """Log likelihood of ground truth tokens, conditioned on previous tokens."""

    token_accuracy: float | None
    """Accuracy of ground truth tokens, conditioned on previous tokens."""

    def to_float_dict(self) -> dict[str, float]:
        """Converts the metrics to a dict of floats.

        In contrast with the `to_dict` method provided by dataclasses_json,
        this method is type-checked and omits the None values.
        """
        assert all(
            isinstance(v, float)
            for v in dataclasses.asdict(self).values()
            if v is not None
        )
        return {k: v for k, v in dataclasses.asdict(self).items() if v is not None}


def compute_code_complete_metrics(
    prediction: str, target: str, forward_metrics: ForwardMetrics | None = None
) -> CodeCompleteMetrics:
    """Get code completion metrics from outputs."""
    # NOTE(Xuanyi): the first line exact match should not contain the last "\n" char.
    target_1_line = get_first_n_lines(target, n=1).rstrip("\n")
    prediction_1_line = get_first_n_lines(prediction, n=1).rstrip("\n")
    prefix_metrics = compute_prefix_precision_recall(target, [prediction])
    lcs_metrics = compute_lcs_precision_recall(target, [prediction])
    return CodeCompleteMetrics(
        exact_match=compute_exact_match(
            target, [prediction], normalize_whitespace=False
        ),
        exact_match_1_line=compute_exact_match(
            target_1_line, [prediction_1_line], normalize_whitespace=False
        ),
        prefix_f1=prefix_metrics.f1,
        prefix_precision=prefix_metrics.precision,
        prefix_recall=prefix_metrics.recall,
        lcs_f1=lcs_metrics.f1,
        lcs_precision=lcs_metrics.precision,
        lcs_recall=lcs_metrics.recall,
        token_accuracy=forward_metrics.token_accuracy if forward_metrics else None,
        log_likelihood=forward_metrics.log_likelihood if forward_metrics else None,
    )


def compute_common_code_complete_tags(model_input: ModelInput):
    """Get common code completion tags from model input."""
    lang = guess_language(model_input.path)
    lang = to_vscode_name(lang) or "other"
    # Special case for Jupyter notebooks.
    if model_input.path.endswith(".ipynb"):
        lang = "jupyter"

    test = is_unit_test(model_input.path)
    test = "test" if test else "non_test"

    return [
        DatumTag(category="all", value="all"),
        DatumTag(category="lang", value=lang),
        DatumTag(category="test", value=test),
    ]


@dataclass(frozen=True, order=True)
class MetricIdentifier:
    """A class that identifies a metric by its name and aggregation type."""

    tag_category: str
    """The tag category, e.g. "lang"."""
    tag_value: str
    """The tag value, e.g. "python"."""
    metric: str
    """The metric name, e.g. "exact_match"."""
    agg_type: Literal["avg", "stderr", "count"]
    """The aggregation type, e.g. "avg"."""

    def __post_init__(self):
        if not MetricIdentifier.check_init(
            self.tag_category, self.tag_value, self.metric, self.agg_type
        ):
            raise ValueError(
                "MetricIdentifier should not contain '.' in its components."
            )

    def __str__(self) -> str:
        return ".".join(dataclasses.astuple(self))

    @staticmethod
    def from_string(s: str) -> "MetricIdentifier":
        """Parses a MetricIdentifier from a dot-delimited string."""
        tag_category, tag_value, metric, agg_type = s.split(".")
        if agg_type not in ("avg", "stderr", "count"):
            raise ValueError(f"Invalid agg_type: {agg_type}")
        return MetricIdentifier(
            tag_category=tag_category,
            tag_value=tag_value,
            metric=metric,
            agg_type=agg_type,
        )

    @staticmethod
    def check_init(*args, **kwargs) -> bool:
        """Returns True if the kwargs are valid for initialization."""
        return "." not in "".join(args) + "".join(kwargs.values())

    @staticmethod
    def safe_init(*args, **kwargs) -> "MetricIdentifier":
        """Replaces '.' with '_' before initializing."""
        args = tuple(arg.replace(".", "_") for arg in args)
        kwargs = {k: v.replace(".", "_") for k, v in kwargs.items()}

        return MetricIdentifier(*args, **kwargs)


@dataclass
class MetricsAggregator:
    """A class that aggregates metrics by tag.

    Currently will compute all the aggregation types available for each metric, e.g.
    each metric will have avg, stderr, count, etc. with no option to specify per metric.
    The current implementation also groups all the datapoints, and actually performs
    the aggregation in the compute() method (does not compute aggregations online).
    """

    meters: MutableMapping[tuple[DatumTag, str], list[float]] = field(
        default_factory=lambda: defaultdict(list)
    )

    def add(self, metrics: dict[str, float], tags: list[DatumTag]):
        """Add metrics from a single sample to the aggregator."""
        for k, v in metrics.items():
            for tag in tags:
                self.meters[tag, k].append(v)

    def compute(self) -> dict[MetricIdentifier, float]:
        """Compute the aggregations."""
        metrics: dict[MetricIdentifier, float] = {}
        for (tag, metric), values in sorted(self.meters.items()):
            if not MetricIdentifier.check_init(tag.category, tag.value, metric, "avg"):
                logger.warning(
                    "Converting '.' to '_' in MetricIdentifier: %s, %s, %s, %s",
                    tag.category,
                    tag.value,
                    metric,
                    "avg",
                )

            metric_id = MetricIdentifier.safe_init(
                tag.category, tag.value, metric, "avg"
            )

            avg_id = dataclasses.replace(metric_id, agg_type="avg")
            metrics[avg_id] = (np.mean(values)).item()
            stderr_id = dataclasses.replace(metric_id, agg_type="stderr")
            metrics[stderr_id] = (np.std(values) / np.sqrt(len(values))).item()
            count_id = dataclasses.replace(metric_id, agg_type="count")
            metrics[count_id] = len(values)

        return metrics


def metrics_to_str_dict(metrics: Mapping[MetricIdentifier, float]) -> dict[str, float]:
    """Convert metric identifiers to str keys."""
    return {str(k): v for k, v in metrics.items()}


def str_dict_to_metrics(metrics: Mapping[str, float]) -> dict[MetricIdentifier, float]:
    """Convert str keys to metric identifiers."""
    return {MetricIdentifier.from_string(k): v for k, v in metrics.items()}


def _default_dict_to_dict(d):
    if isinstance(d, defaultdict):
        d = {k: _default_dict_to_dict(v) for k, v in d.items()}
    return d


def nest_metrics(
    metrics: Mapping[MetricIdentifier, float],
) -> Mapping[str, Mapping[str, Mapping[str, Mapping[str, float]]]]:
    """Nest a dictionary of metric identifiers."""
    nested = defaultdict(
        lambda: defaultdict(lambda: defaultdict(lambda: defaultdict(float)))
    )
    for k, v in metrics.items():
        nested[k.tag_category][k.tag_value][k.metric][k.agg_type] = v
    return _default_dict_to_dict(nested)


def unnest_metrics(
    metrics: Mapping[str, Mapping[str, Mapping[str, Mapping[str, float]]]],
) -> Mapping[MetricIdentifier, float]:
    """Unnest a dictionary of metric identifiers."""
    unnested = {}
    for tag_category, tag_value_dict in metrics.items():
        for tag_value, metric_dict in tag_value_dict.items():
            for metric, agg_type_dict in metric_dict.items():
                for agg_type, v in agg_type_dict.items():
                    if agg_type not in ("avg", "stderr", "count"):
                        raise ValueError(f"Invalid agg_type: {agg_type}")
                    if not MetricIdentifier.check_init(
                        tag_category, tag_value, metric, agg_type
                    ):
                        logger.warning(
                            "Converting '.' to '_' in MetricIdentifier: %s, %s, %s, %s",
                            tag_category,
                            tag_value,
                            metric,
                            agg_type,
                        )
                    metric_id = MetricIdentifier.safe_init(
                        tag_category, tag_value, metric, agg_type
                    )
                    unnested[metric_id] = v
    return unnested

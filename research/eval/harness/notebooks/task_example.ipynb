{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Running an Evaluation task\n", "## Example: how to run Hydra against a simple system"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import tempfile\n", "from research.eval.harness.tasks.hydra_task import HydraTask\n", "from research.eval.harness.factories import create_system\n", "\n", "# Create a system composed of a model and BM25 retriever\n", "system_config = {\n", "    \"name\": \"basic_rag\",\n", "    \"model\": {\"name\": \"starcoderbase_1b\", \"prompt\": {\"max_prefix_tokens\": 1024, \"max_prompt_tokens\": (2048 - 64)}},\n", "    \"retriever\": {\"name\": \"bm25\", \"chunker\": \"line_level\"},\n", "    \"generation_options\": {\"max_generated_tokens\": 64},\n", "    \"experimental\": {},\n", "}\n", "\n", "sys = create_system(system_config)\n", "task = HydraTask(limit=1, execute=False)\n", "with tempfile.TemporaryDirectory() as scratch:\n", "    report = task.run(sys, scratch)\n", "display(report)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run the same task with a different chunker"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system_config[\"retriever\"] = {\"name\": \"bm25\", \"chunker\": \"scope_aware\"}\n", "sys = create_system(system_config)\n", "with tempfile.TemporaryDirectory() as scratch:\n", "    report = task.run(sys, scratch)\n", "display(report)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create a task that runs unit tests for multiple patches\n", "### Read artifact files produced from the run"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.utils import read_jsonl_zst\n", "import json\n", "\n", "task = HydraTask(limit=10, execute=True)\n", "with tempfile.TemporaryDirectory() as scratch:\n", "    report = task.run(sys, scratch)\n", "\n", "    display(report)\n", "\n", "    # The report from the task contains task-specific metrics, including possible artifacts produced during execution\n", "    # Input patches\n", "    a = read_jsonl_zst(Path(scratch) / str(report[\"artifact\"]))\n", "    display(a[0])\n", "\n", "    # Execution results\n", "    with (Path(scratch) / str(report[\"exec_artifact\"])).open(\"r\") as f:\n", "        b = [json.loads(x) for x in f]\n", "    display(b[0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Create a task and execute a single completion"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.hydra_task import HydraTask\n", "\n", "task = HydraTask()\n", "inputs, repo = task[0]\n", "results = task.execute(inputs, inputs.target, timeout=60.)  # type: ignore\n", "\n", "for key, value in results.items():\n", "    print(f\"Key={key}\")\n", "    print(f\">>> {value}\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.human_eval import HumanEval\n", "\n", "task = HumanEval()\n", "inputs, _ = task[0]\n", "completion: str = inputs.target  # type: ignore\n", "results = task.execute(inputs, completion, timeout=5.0)\n", "\n", "for key, value in results.items():\n", "    print(f\"Key={key}\")\n", "    print(f\">>> {value}\\n\")\n", "\n", "results = task.execute(inputs, completion, timeout=0.01)\n", "\n", "for key, value in results.items():\n", "    print(f\"Key={key}\")\n", "    print(f\">>> {value}\\n\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
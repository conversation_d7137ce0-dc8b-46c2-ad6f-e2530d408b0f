{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Running the HumanEval FIM task"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "import logging\n", "from pathlib import Path\n", "import tempfile\n", "\n", "from research.eval.harness.utils import read_jsonl_zst\n", "from research.eval.harness.tasks.human_eval_fim import HumanEvalFim\n", "from research.eval.harness.tasks.human_eval import HumanEvalOutput\n", "from research.eval.harness.factories import create_system\n", "\n", "logging.basicConfig(level=logging.INFO)\n", "\n", "\n", "# Create a system composed of a model and BM25 retriever\n", "system_config = {\n", "    \"name\": \"basic_rag\",\n", "    \"model\": {\n", "        \"name\": \"starcoderbase_1b\",\n", "        \"prompt\": {\"max_prefix_tokens\": 1024, \"max_prompt_tokens\": (2048 - 64)},\n", "    },\n", "    \"retriever\": {\"name\": None, \"chunker\": \"line_level\"},\n", "    \"generation_options\": {\n", "        \"max_generated_tokens\": 64,\n", "        \"temperature\": 0.2,\n", "        \"top_p\": 0.95,\n", "        \"top_k\": 0,\n", "    },\n", "    \"experimental\": {\n", "        \"remove_suffix\": False\n", "    },\n", "}\n", "\n", "sys = create_system(system_config)\n", "task = HumanEvalFim(iterations=1, limit=10, execute=True, variant=\"singleline\")\n", "\n", "with tempfile.TemporaryDirectory() as scratch:\n", "    report = task.run(sys, scratch)\n", "    artifact = report[\"artifact\"]\n", "    recs = read_jsonl_zst(Path(scratch) / artifact)\n", "\n", "    counts = {}\n", "    for rec in recs:\n", "        p = HumanEvalOutput(**rec)\n", "        if p.patch[\"task_id\"] not in counts:\n", "            counts[p.patch[\"task_id\"]] = Counter()\n", "        counts[p.patch[\"task_id\"]].update([p.patch[\"passed\"]])\n", "        print(\">>>>\")\n", "        print(p.prompt)\n", "        print(\"<<<<\")\n", "        print(p.generation)\n", "        print(\"*ground-truth*\")\n", "        print(p.patch[\"canonical_solution\"])\n", "        print(\"----\")\n", "        print(p.completion)\n", "        print(\"--result--\")\n", "        print(p.patch[\"result\"])\n", "\n", "display(report)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Using a System"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example of trying different retrievers with a task"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from research.eval.harness.factories import create_model, create_retriever\n", "from research.eval.harness.systems.basic_RAG_system import (\n", "    RAGSystem,\n", "    MiscRAGSystemConfig,\n", ")\n", "from research.eval.harness.tasks.hydra_task import HydraTask\n", "from research.models.meta_model import (\n", "    GenerationOptions,\n", ")\n", "\n", "# Create a system composed of a model and BM25 retriever\n", "model_config = {\"name\": \"starcoderbase_1b\"}\n", "retriever_configs = [\n", "    {\"name\": \"bm25\", \"chunker\": \"line_level\"},\n", "    {\"name\": \"bm25\", \"chunker\": \"scope_aware\"},\n", "    {\"name\": \"contrastive_350m\", \"chunker\": \"line_level\"},\n", "]\n", "\n", "# Setting retriever to None because we'll load different retrievers in\n", "# the loop below\n", "model = create_model(model_config)\n", "sys = RAGSystem(\n", "    model,\n", "    retriever=None,\n", "    generation_options=GenerationOptions(max_generated_tokens=64),\n", "    experimental_config=MiscRAGSystemConfig(),\n", ")\n", "sys.load()\n", "\n", "# Create a namespace for any output.\n", "scratch = Path(\"/tmp/scratch\")\n", "scratch.mkdir(exist_ok=True)\n", "task = HydraTask(limit=1, execute=False)\n", "for config in retriever_configs:\n", "    retriever = create_retriever(config)\n", "    sys.retriever = retriever\n", "    # Dense retriever's need to be loaded. This is awkward at the moment\n", "    # but we're expected to remove the load()/unload() methods.\n", "    if hasattr(sys.retriever, \"scorer\"):\n", "        sys.retriever.scorer.load()\n", "    report = task.run(sys, scratch)\n", "    display(report)\n", "sys.unload()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example of using the System outside of the task"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.model_input import ModelInput\n", "from research.retrieval.utils import Span\n", "from research.core.types import Document\n", "\n", "src_files = [Document(id=str(hash(\"hello\")), path=\"foo.py\", text=\"hello\")]\n", "\n", "mi = ModelInput(\n", "    prefix=\"hello world\",\n", "    path=\"bar.py\",\n", "    extra={\"char_span\": Span(0, 1), \"gt_span\": Span(0, 1)},\n", ")\n", "\n", "retriever_config = model = create_model(model_config)\n", "retriever = create_retriever({\"name\": \"bm25\", \"chunker\": \"line_level\"})\n", "sys = RAGSystem(\n", "    model,\n", "    retriever=retriever,\n", "    generation_options=GenerationOptions(max_generated_tokens=64),\n", "    experimental_config=MiscRAGSystemConfig(),\n", ")\n", "sys.load()\n", "sys.add_docs(src_files)\n", "display(len(sys.retriever.documents))\n", "display(sys.generate(mi))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
# Note(yuri): Code in this file is borrowed from https://infi-coder.github.io/inficoder-eval/

# ruff: noqa

import importlib
import os
import re

# Note(yuri): Trick to prevent importing `evaluate` from gpt-neox
import sys
from typing import Dict, List, Optional, Tuple, Union

removed_paths = []
for _path in sys.path:
    if "gpt-neox" in _path:
        removed_paths.append(_path)
        sys.path.remove(_path)
import evaluate

sys.path.extend(removed_paths)

import numpy as np


def grade_responses(
    config: dict,
    case_dir: str,
    responses: Optional[List[str]],
    reduce_mode: str,
    suite_full_score: float,
    suite_null_score: float,
) -> Tuple[float, float, List[dict]]:
    """

    :param config:
    :param case_dir:
    :param responses:
    :param reduce_mode:
    :param suite_full_score:
    :param suite_null_score:
    :return: current score, question full score, and list of dict explaining score for each response
    """
    full_score = config.get("full_score", suite_full_score)
    null_score = config.get("null_score", suite_null_score)
    if responses is None:
        return null_score, full_score, []
    else:
        all_scores_details = [
            grade_response(config, case_dir, response, full_score, null_score)
            for response in responses
        ]
        all_scores = [s for s, d in all_scores_details]
        all_details = [d for s, d in all_scores_details]
        if reduce_mode == "avg":
            score = float(np.average(all_scores))
            std = 0.0
        elif reduce_mode == "max":
            score = float(np.max(all_scores))
            std = 0.0
        elif reduce_mode == "min":
            score = float(np.min(all_scores))
            std = 0.0
        elif reduce_mode.startswith("avg_max_"):
            batch = int(reduce_mode[len("avg_max_") :])
            buk_score = []
            for i in range(0, len(all_scores), batch):
                buk_score.append(float(np.max(all_scores[i : i + batch])))
            score = float(np.mean(buk_score))
            if len(buk_score) > 1:
                std = float(np.std(buk_score, ddof=1))
            else:
                std = 0.0
        else:
            raise NotImplementedError(
                f"Unsupported score reduction mode: {reduce_mode}"
            )
        return score, std, all_details


def grade_response(
    config: dict, case_dir: str, response: str, full_score: float, null_score: float
) -> Tuple[float, dict]:
    """
        Return current score and detail explanation
    :param config:
    :param case_dir:
    :param response:
    :param full_score:
    :param null_score:
    :return:
    """
    status = dict()
    ans = 0.0
    tot = 0.0

    if "lang" not in config:
        print(f'Warning: language not specified in case {config["id"]}')
    if "type" not in config:
        print(f'Warning: question type not specified in case {config["id"]}')
    elif config["type"] not in [
        "code debugging",
        "code completion",
        "knowledge question-answering",
        "non-code debugging",
    ]:
        print(f'Warning: unusual question type (config["type"]) in case {config["id"]}')

    # four types of evaluation metrics + customized
    if "keywords" in config["grading"]:
        """
            keywords:
                - "xxxxx"
                - content: "xxxxxx"
                  *weight: 2.0
                  *to_lower: true (default: false)
                - content:
                    content: "xxxx"
                    or:
                        xxx (recursive)
                    and:
                        xxx (recursive)
                    *cond: "context[-1] == 'match'"
                    *regex: true (by default false)
        """

        now_ans, now_tot = 0.0, 0.0

        default_weight = 1.0

        post_handler = None

        status["keywords"] = list()
        for rule in config["grading"]["keywords"]:
            if isinstance(rule, str):
                rule_content = rule
                rule_weight = default_weight
                to_lower = False
                neg = False
            else:
                if "post_handler" in rule:
                    post_handler = rule["post_handler"]
                    continue
                rule_content = rule["content"]
                rule_weight = float(rule.get("weight", default_weight))
                to_lower = bool(rule.get("to_lower", False))
                neg = bool(rule.get("neg", False))

            if keyword_match(
                rule_content, to_lower, False, response, status["keywords"]
            ):
                status["keywords"].append("match")
                now_ans += rule_weight if not neg else -rule_weight
            else:
                status["keywords"].append("unmatch")
            now_tot += rule_weight if not neg else 0.0

        if post_handler is not None:
            # post_process the score
            module_name = post_handler["module"]
            func_name = post_handler["func"]

            custom_module = importlib.import_module(module_name)
            new_ans, new_tot, new_detail = getattr(custom_module, func_name)(
                now_ans, now_tot, status["keywords"]
            )
            status["post_handler_detail"] = new_detail
            now_ans = new_ans
            now_tot = new_tot

        status["keywords_score"] = now_ans
        status["keywords_totscore"] = now_tot
        ans, tot = ans + now_ans, tot + now_tot

    if "blank_filling" in config["grading"]:
        """
            blank_filling:
                template: "xxxxx[blank]xxxx xxxx [blank] xxx"
                *blank_str: [blank]
                *escape: " '\""
                *prefix: ""
                targets:
                    - aaa
                    - content:
                        - bbb
                        - bbbbb
                    - ccc
                    - content: ddd
                      *weight: 2.0
                      *to_lower: true (default: false)
                    - content:
                        - eee
                        - fff
                        - content: ggg (no recursion)
                          *cond: grading_details[-1].startswith("unmatch")
                          *regex: true (default: false)
                      *weight: 2.0
                      *to_lower: true (default: false)
                    - ...
        """

        blank_filling_config = config["grading"]["blank_filling"]

        default_escape = " '\"`"

        template = blank_filling_config["template"]
        blank_str = blank_filling_config.get("blank_str", "[blank]")
        escape = blank_filling_config.get("escape", default_escape)
        targets = blank_filling_config["targets"]
        prefix = blank_filling_config.get("prefix", "")
        post_handler = blank_filling_config.get("post_handler", None)

        now_ans, now_tot, now_detail, post_handler_detail = blank_filling_match(  # pyright: ignore
            template, blank_str, escape, targets, prefix + response, post_handler
        )

        status["blank_filling_score"] = now_ans
        status["blank_filling_totscore"] = now_tot
        status["blank_filling_detail"] = now_detail
        if post_handler_detail is not None:
            status["blank_filling_post_handler_detail"] = post_handler_detail
        ans, tot = ans + now_ans, tot + now_tot

    if "unit_test" in config["grading"]:
        """
            unittest:
                *lang: "java/python/javascript/c++/c" # overwrite the test case language
                tests:
                    - "xxxxxx"
                    - path: "xxxxx"
                      *prefix: "xxxxx" (usually contains signature etc)
                      *weight: 2.0 (default: 1.0)
                    - content: "xxxxx"
                      *prefix: "xxxxx"
        """
        raise RuntimeError(
            "To keep things simple, we don't use unit_test functionality for now"
        )

        unit_test_config = config["grading"]["unit_test"]

        lang = config["lang"]
        if "lang" in unit_test_config:
            lang = unit_test_config["lang"]

        if lang == "python":
            lang = "python"
        elif lang in ["c", "c++", "cpp", "c/c++", "c++/c", "cpp/c", "c/cpp"]:
            lang = "cpp"
        elif lang in ["js", "javascript"]:
            lang = "javascript"
        # elif lang == 'custom-py':
        #     lang = 'custom-py'
        elif lang == "java":
            lang = "java"
        elif lang in ["c#", "c-sharp", "csharp", "cs"]:
            lang = "c#"
        elif lang in ["ts", "typescript"]:
            lang = "typescript"
        elif lang == "r":
            lang = "r"
        elif lang == "go":
            lang = "go"
        else:
            raise NotImplementedError(f"Does not support this language yet: {lang}.")

        now_ans, now_tot, now_detail = unit_test_execution(
            lang, response, unit_test_config["tests"], case_dir
        )

        status["unit_test_score"] = now_ans
        status["unit_test_totscore"] = now_tot
        status["unit_test_detail"] = now_detail
        ans, tot = ans + now_ans, tot + now_tot

    if "similarity" in config["grading"]:
        """
            similarity:
                - metric: rouge1/rouge2/rougeL/rougeLsum
                  references:
                    - xxxx
                    - path: xxxx
                    - ...
                  *max_score: 0.xx (default 0.51 for others, 0.53 for rouge1)
                  *min_score: 0.xx (default 0.30 for rougeL, and same for rouge1)
                  *weight: xxx (default 1.0)
            min/max score set according to https://docs.oneai.com/docs/rouge-metrics-for-summary-headline
            => means the final score would be "sum(weight * clip( (raw_score - min_score) / (max_score - min_score), 0, 1))"
        """

        similarity_config = config["grading"]["similarity"]
        now_ans, now_tot, now_detail = similarity_assessment(
            response, similarity_config, case_dir
        )

        status["similarity_score"] = now_ans
        status["similarity_totscore"] = now_tot
        status["similarity_detail"] = now_detail

        ans, tot = ans + now_ans, tot + now_tot

    if "customized" in config["grading"]:
        customized_config = config["grading"]["customized"]
        module_name = customized_config["module"]
        func_name = customized_config["func"]

        custom_module = importlib.import_module(module_name)
        now_ans, now_tot, now_detail = getattr(custom_module, func_name)(response)

        status["custom_score"] = now_ans
        status["custom_totscore"] = now_tot
        status["custom_detail"] = now_detail

        ans, tot = ans + now_ans, tot + now_tot

    if "max_score" in config["grading"]:
        tot = config["grading"]["max_score"]
        status["max_score"] = config["grading"]["max_score"]
        ans = min(ans, tot)
    if "min_score" in config["grading"]:
        ans = max(ans, config["grading"]["min_score"])
        status["min_score"] = config["grading"]["min_score"]

    if ans < 0.0:
        print(f'Warning: negative score ({ans}) found in case id {config["id"]}')

    # print(f'Score: {ans} / {tot} -> {(ans / tot) * full_score:.3f}')
    return (ans / tot) * full_score, status


# from execution_utils import preprocess, get_exec_results


def keyword_match(
    rule: Union[str, Dict],
    to_lower: bool,
    regex: bool,
    response: str,
    context: List[str],
) -> bool:
    if isinstance(rule, str):
        if to_lower:
            rule, response = rule.lower(), response.lower()
        if regex:
            return re.search(rule, response) is not None
        else:
            return rule in response
    else:
        # is a dict

        regex = regex
        # overwrite
        if "regex" in rule:
            regex = rule["regex"]

        if "or" in rule:
            # OR clause
            ans = any(
                [
                    keyword_match(clause, to_lower, regex, response, context)
                    for clause in rule["or"]
                ]
            )
        elif "and" in rule:
            # AND clause
            ans = all(
                [
                    keyword_match(clause, to_lower, regex, response, context)
                    for clause in rule["and"]
                ]
            )
        else:
            assert "content" in rule
            ans = keyword_match(rule["content"], to_lower, regex, response, context)
        if "cond" in rule:
            # available variables:
            #   context: ["unmatch", "match", "match", ....]
            #   ans: current judged sat status in bool
            ans = ans and eval(rule["cond"])
        return ans


def LCS(template: str, tgt: str) -> Tuple[np.ndarray, np.ndarray]:
    """
        Longest Common Subsequence via dynamic programming
    :param template:
    :param tgt:
    :return: matched length map f, matched index map s
    """
    f = np.zeros((int(len(template)), int(len(tgt))), dtype="int")
    s_all = np.zeros((int(len(template)), int(len(tgt))), dtype="int")

    for i in range(0, len(template)):
        for j in range(0, len(tgt)):
            if i * j > 0:
                if template[i] == tgt[j]:
                    f[i][j] = f[i - 1][j - 1] + 1
                    s_all[i][j] = j
                else:
                    f[i][j] = max(f[i][j - 1], f[i - 1][j])
                    if f[i][j - 1] == f[i - 1][j]:
                        # break tie by preferring the latter match
                        s_all[i][j] = max(s_all[i][j - 1], s_all[i - 1][j])
                    else:
                        s_all[i][j] = (
                            s_all[i][j - 1]
                            if f[i][j - 1] >= f[i - 1][j]
                            else s_all[i - 1][j]
                        )
            else:
                if template[i] == tgt[j]:
                    f[i][j] = 1
                    s_all[i][j] = j

    s = np.zeros(len(template), dtype="int")
    p = s_all[len(template) - 1][len(tgt) - 1]
    for i in range(len(template) - 1, -1, -1):
        if template[i] == tgt[p]:
            s[i] = p
        else:
            # lookahead for one char
            s[i] = p + 1
        if i > 0:
            if template[i] == tgt[p]:
                p = s_all[i - 1][p - 1]

    return f, s


def blank_filling_match(
    template: str,
    blank_str: str,
    escape: str,
    targets: List[Union[dict, str]],
    response: str,
    post_handler: Optional[dict] = None,
) -> Tuple[float, float, List[str]]:
    if response == "":
        response = " "  # avoid the empty string problem
    f, s = LCS(template, response)
    n_blank = template.count(blank_str)
    blank_places = [
        i for i in range(len(template)) if template.startswith(blank_str, i)
    ]
    assert n_blank == len(blank_places) and len(blank_places) == len(
        targets
    ), "Number of targets should be equal to number of blanks"

    matched_rate = f[len(template) - 1][len(response) - 1] / (
        len(template) - n_blank * len(blank_str)
    )
    # print(f'Template following rate = {matched_rate:.3f}')

    now_score, tot_score = 0.0, 0.0
    grading_details = []

    for no, target in enumerate(targets):
        weight = 1.0 if isinstance(target, str) else target.get("weight", 1.0)
        to_lower = False if isinstance(target, str) else target.get("to_lower", False)
        substr_match = (
            False if isinstance(target, str) else target.get("substr_match", False)
        )
        tot_score += weight

        if matched_rate < 0.8:
            grading_details.append(f"unmatched: match rate too low - {matched_rate}")
        else:
            if blank_places[no] == 0:
                response_str = response[
                    s[blank_places[no]] : s[blank_places[no] + len(blank_str)]
                ]
            else:
                # match longer
                response_str = response[
                    s[blank_places[no] - 1] + 1 : s[blank_places[no] + len(blank_str)]
                ]
            response_str = response_str.strip(escape)

            # anses are or-clauses
            if isinstance(target, str):
                anses = [target]
            else:
                anses = target["content"]
                if isinstance(anses, str):
                    anses = [anses]
                if isinstance(anses, str):
                    anses = [anses]

            matched = False
            now_status = "unmatched"
            for ans in anses:
                ans_str = ans if isinstance(ans, str) else ans["content"]
                ans_cond = None if isinstance(ans, str) else ans.get("cond", None)
                ans_re = False if isinstance(ans, str) else ans.get("regex", False)
                now_status = f"response string: {response_str}, ans: {ans_str}"
                if to_lower:
                    response_str, ans_str = response_str.lower(), ans_str.lower()
                # for ans_cond predicate, the available context is
                # - grading_details: list of string starting with "matched" or "unmatched"
                # - ans_str: str
                # - ans_re: bool
                # - response_str: str
                if not substr_match:
                    if (
                        (not ans_re and response_str == ans_str)
                        or (ans_re and re.fullmatch(ans_str, response_str) is not None)
                    ) and (ans_cond is None or eval(ans_cond)):
                        now_score += weight
                        now_status = "matched: " + now_status
                        matched = True
                        break
                else:
                    if (
                        (not ans_re and response_str.count(ans_str) > 0)
                        or (ans_re and re.search(ans_str, response_str))
                    ) and (ans_cond is None or eval(ans_cond)):
                        now_score += weight
                        now_status = "matched: " + now_status
                        matched = True
                        break

            if matched:
                grading_details.append(now_status)
            else:
                grading_details.append("unmatched: " + now_status)

    post_handler_detail = None
    if post_handler is not None:
        # post_process the score
        module_name = post_handler["module"]
        func_name = post_handler["func"]

        custom_module = importlib.import_module(module_name)
        new_ans, new_tot, post_handler_detail = getattr(custom_module, func_name)(
            now_score, tot_score, grading_details
        )
        now_score = new_ans
        tot_score = new_tot

    return now_score, tot_score, grading_details, post_handler_detail  # pyright: ignore


def unit_test_execution(
    lang: str, response: str, unit_tests: List[Union[str, Dict]], case_dir: str
) -> Tuple[float, float, List[Dict[str, str]]]:
    raise NotImplemented()


def similarity_assessment(
    response: str, similarity_metrics: List[Dict], case_dir: str
) -> Tuple[float, float, List[Dict[str, float]]]:
    now_score, tot_score = 0.0, 0.0
    grading_details = []

    rouge = evaluate.load("rouge")  # pyright: ignore
    for metric in similarity_metrics:
        references = metric["references"]
        passages = []
        for item in references:
            if isinstance(item, str):
                passages.append(item)
            else:
                with open(os.path.join(case_dir, item["path"]), "r") as f:
                    passages.append(f.read())
        results = rouge.compute(predictions=[response], references=[passages])
        results = dict([(k, float(v)) for k, v in results.items()])  # pyright: ignore
        raw_score = results[metric["metric"]]

        if "min_score" in metric:
            min_score = float(metric["min_score"])
        else:
            min_score = 0.3
        if "max_score" in metric:
            max_score = float(metric["max_score"])
        elif metric["metric"] == "rouge1":
            max_score = 0.53
        else:
            max_score = 0.51
        normalized_score = max(
            min((raw_score - min_score) / (max_score - min_score), 1.0), 0.0
        )
        weight = metric.get("weight", 1.0)
        tot_score += weight
        now_score += normalized_score * weight

        results["normalized_score"] = normalized_score
        grading_details.append(results)

    return now_score, tot_score, grading_details

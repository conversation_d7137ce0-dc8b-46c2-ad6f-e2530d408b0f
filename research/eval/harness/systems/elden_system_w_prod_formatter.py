"""Elden system combining dense and signature retrieval.

Different from the other systems, this system:
- directly uses the production prompt formatter and tokenizer.
- make line_chunk-based retriever and signature_chunk-based retriever optional.

The config should contain the following field:
- tokenizer: the name of the tokenizer.
- model: the model config.
- dense_retriever: the dense retriever config.
- signature_retriever: the signature retriever config.
- generation_options: the generation options.
- config: the json dictionary to create EldenSystemConfig.
- formatter_config:
  - name: the name of the prompt formatter.
  - prompt_formatter_config: the optional config for the prompt formatter config in production.
  - apportionment_config: the optional config for the token apportionment.
"""

from __future__ import annotations

import copy
import dataclasses
import logging
import typing

import dataclasses_json
import marshmallow
import torch

from base.prompt_format.common import PromptChunk
from base.prompt_format_completion import get_completion_prompt_formatter_by_name
from base.prompt_format_completion.prompt_formatter import (
    CompletionPromptFormatter,
    PromptInput,
)
from base.tokenizers import create_tokenizer_by_name
from base.tokenizers.tokenizer import RagSpecialTokens, Tokenizer
from research.core.model_input import ModelInput
from research.core.types import Chunk as ResearchChunk
from research.core.types import Document, DocumentId
from research.core.utils_for_str import get_first_n_lines
from research.eval.harness import factories
from research.eval.harness.systems.abs_system import (
    CodeCompleteSystem,
    CompletionResult,
    register_system,
)
from research.eval.harness.systems.libraries.completion_fim_handling import (
    fim_postprocess_generation,
)
from research.eval.harness.systems.recency_lib import (
    recent_chunk_to_prod_retrieval_chunk,
)
from research.models import (
    ExtraGenerationOutputs,
    GenerationOptions,
    GenerativeLanguageModel,
)
from research.models.meta_model import FimGenMode, ModelForwardOutput
from research.retrieval.types import DocumentIndex

logger = logging.getLogger(__name__)


@dataclasses.dataclass
class EldenSystemConfig(dataclasses_json.DataClassJsonMixin):
    """Miscellaneous config options for Elden system."""

    line_chunk_retriever_top_k: int = dataclasses.field(
        metadata=dataclasses_json.config(
            mm_field=marshmallow.fields.Int(),
        ),
    )
    """The line chunk retriever's top-k value."""

    sig_chunk_retriever_top_k: int = dataclasses.field(
        metadata=dataclasses_json.config(
            mm_field=marshmallow.fields.Int(),
        ),
    )
    """The signature chunk retriever's top-k value."""

    fim_gen_mode: FimGenMode = dataclasses.field(
        default=FimGenMode.evaluation,
        metadata=dataclasses_json.config(
            mm_field=marshmallow.fields.Enum(FimGenMode, by_value=False)
        ),
    )
    """The FIM generation mode."""


@register_system("prod_elden")
class ProdEldenSystem(CodeCompleteSystem):
    """LM with line and signature dense retrievers."""

    def __init__(
        self,
        model: GenerativeLanguageModel,
        dense_retriever: DocumentIndex | None,
        signature_retriever: DocumentIndex | None,
        prompt_formatter: CompletionPromptFormatter,
        tokenizer: Tokenizer,
        generation_options: GenerationOptions,
        config: EldenSystemConfig,
    ):
        self.model = model
        self.dense_retriever = dense_retriever
        self.signature_retriever = signature_retriever
        self.generation_options = generation_options
        self.prompt_formatter = prompt_formatter
        self.tokenizer = tokenizer
        # The model's prompt formatter does not matter in this case, as we only its token-based interface.
        # However, the tokenizer must be consistent with the model's tokenizer, as its eod or pad tokens will be used.
        self.model.prompt_formatter.tokenizer = tokenizer
        self.config = config
        self._loaded = False

    def load(self):
        if not self._loaded:
            self.model.load()
            if self.dense_retriever is not None:
                self.dense_retriever.load()
            if self.signature_retriever is not None:
                self.signature_retriever.load()
        self._loaded = True

    def unload(self):
        if self._loaded:
            self.model.unload()
            if self.dense_retriever is not None:
                self.dense_retriever.unload()
            if self.signature_retriever is not None:
                self.signature_retriever.unload()
            self._loaded = False

    def add_docs(self, src_files: typing.Collection[Document]):
        """Ingest a copy of the source code repository.

        Args:
          src_files: list of Documents to add
        """
        logger.info(f"Adding {len(list(src_files))} documents.")
        if self.dense_retriever is None:
            logger.info("Dense retriever is not set. Skipping.")
        else:
            self.dense_retriever.add_docs(src_files)  # type: ignore
            total_docs = len(self.dense_retriever.get_doc_ids())
            logger.info(f"There are now {total_docs} total docs in dense retriever.")

        if self.signature_retriever is None:
            logger.info("Signature retriever is not set. Skipping.")
        else:
            self.signature_retriever.add_docs(src_files)  # type: ignore
            total_docs = len(self.signature_retriever.get_doc_ids())
            logger.info(
                f"There are now {total_docs} total docs in signature retriever."
            )

    def remove_docs(self, doc_ids: typing.Collection[DocumentId]):
        """Remove documents from the retrievers."""
        logger.info(f"Removing {len(list(doc_ids))} documents.")

        if self.dense_retriever is None:
            logger.info("Dense retriever is not set. Skipping.")
        else:
            self.dense_retriever.remove_docs(doc_ids)
            total_docs = len(self.dense_retriever.get_doc_ids())
            logger.info(f"There are now {total_docs} total docs in dense retriever.")

        if self.signature_retriever is None:
            logger.info("Signature retriever is not set. Skipping.")
        else:
            self.signature_retriever.remove_docs(doc_ids)
            total_docs = len(self.signature_retriever.get_doc_ids())
            logger.info(
                f"There are now {total_docs} total docs in signature retriever."
            )

    def get_doc_ids(self) -> typing.AbstractSet[DocumentId]:
        """Get the set of indexed documents ids for the dense retriever."""
        doc_ids: typing.AbstractSet[DocumentId] = set()
        if self.dense_retriever is not None:
            doc_ids = doc_ids.union(self.dense_retriever.get_doc_ids())
        if self.signature_retriever is not None:
            doc_ids = doc_ids.union(self.signature_retriever.get_doc_ids())
        return doc_ids

    def clear_retriever(self):
        """Clear any stored documents from the retriever."""
        if self.dense_retriever is None:
            logger.info("Dense retriever is not set. Skipping.")
        else:
            self.dense_retriever.remove_all_docs()
        if self.signature_retriever is None:
            logger.info("Signature retriever is not set. Skipping.")
        else:
            self.signature_retriever.remove_all_docs()

    def get_model(self) -> GenerativeLanguageModel:
        return self.model

    def forward_pass(self, inputs: list[ModelInput]) -> list[ModelForwardOutput]:
        outputs: list[ModelForwardOutput] = []
        for model_input in inputs:
            # Prepare the propmt tokens
            chunks_in_prod, _ = self._retrieve(model_input)
            prefix_begin = (
                model_input.cursor_position - len(model_input.prefix)
                if model_input.cursor_position is not None
                else 0
            )
            prompt_input = PromptInput(
                prefix=model_input.prefix,
                suffix=model_input.suffix,
                prefix_begin=prefix_begin,
                path=model_input.path,
                retrieved_chunks=chunks_in_prod,
                lang=None,
            )
            assert self.generation_options.max_generated_tokens is not None
            formatter_outputs = self.prompt_formatter.format_prompt(
                prompt_input,
                max_output_token_count=self.generation_options.max_generated_tokens,
            )
            prompt_tokens = formatter_outputs.tokens()
            # Prepare the target tokens
            if model_input.target is not None:
                # TODO: target tokens should also contain the EOS token.
                target_tokens = self.tokenizer.tokenize_safe(model_input.target)
            elif "target_tokens" in model_input.extra:
                target_tokens: list[int] = model_input.extra["target_tokens"]
            else:
                raise ValueError("target field is required")
            if len(target_tokens) > self.generation_options.max_generated_tokens:
                logger.warning(
                    f"Target tokens are too long: {len(target_tokens)} > {self.generation_options.max_generated_tokens} -- truncating."
                )
                target_tokens = target_tokens[
                    : self.generation_options.max_generated_tokens
                ]
            all_tokens = prompt_tokens + target_tokens
            all_tokens = torch.tensor(all_tokens, dtype=torch.long, device="cuda")
            input_tokens, label_tokens = all_tokens[:-1], all_tokens[1:]

            # Handles the case where target_tokens may be empty
            target_mask = [0 for _ in prompt_tokens] + [1 for _ in target_tokens]
            target_mask = torch.tensor(target_mask, dtype=torch.bool, device="cuda")
            target_mask = target_mask[1:]
            logits = self.model.forward_pass_single_logits(input_tokens)
            outputs.append(
                ModelForwardOutput(
                    logits=logits,
                    input_tokens=input_tokens,
                    label_tokens=label_tokens,
                    target_mask=target_mask,
                )
            )
        return outputs

    def log_likelihood_continuation(
        self, model_input: ModelInput, continuation: str
    ) -> float | None:
        """Returns the conditional log likelihood of the continuation, conditioned on the model input."""
        raise NotImplementedError()

    def generate(
        self,
        model_input: ModelInput,
    ) -> CompletionResult:
        """Generate a completion."""
        chunks_in_prod, chunks_in_research = self._retrieve(model_input)
        generation = self._generate(model_input, chunks_in_prod)
        generation.retrieved_chunks = chunks_in_research
        return generation

    def _retrieve(
        self,
        model_input: ModelInput,
    ) -> typing.Tuple[list[PromptChunk], list[ResearchChunk]]:
        if model_input.doc_ids is None:
            logger.warning("No doc_ids provided, using all doc_ids.")

        if self.dense_retriever is None:
            logger.info("Dense retriever is not set. Skipping.")
            retrieved_line_chunks = []
        else:
            retrieved_line_chunks, _ = self.dense_retriever.query(
                model_input,
                doc_ids=model_input.doc_ids,
                top_k=self.config.line_chunk_retriever_top_k,
            )
        line_chunks_in_prod: list[PromptChunk] = []
        for chunk in retrieved_line_chunks:
            line_chunks_in_prod.append(
                PromptChunk(
                    text=chunk.text,
                    path=chunk.parent_doc.path,
                    unique_id=chunk.id,
                    origin="dense_retriever",
                    char_start=chunk.char_offset,
                    char_end=chunk.char_offset + chunk.length,
                    blob_name=chunk.parent_doc.id,
                    header=chunk.header,
                )
            )
        if self.signature_retriever is None:
            logger.info("Signature retriever is not set. Skipping.")
            retrieved_sig_chunks = []
        else:
            retrieved_sig_chunks, _ = self.signature_retriever.query(
                model_input,
                doc_ids=model_input.doc_ids,
                top_k=self.config.sig_chunk_retriever_top_k,
            )
        sig_chunks_in_prod: list[PromptChunk] = []
        for chunk in retrieved_sig_chunks:
            sig_chunks_in_prod.append(
                PromptChunk(
                    text=chunk.text,
                    path=chunk.parent_doc.path,
                    unique_id=chunk.id,
                    origin="signature_retriever",
                    char_start=chunk.char_offset,
                    char_end=chunk.char_offset + chunk.length,
                    blob_name=chunk.parent_doc.id,
                    header=chunk.header,
                )
            )
        if model_input.recency_info is None:
            logger.info("No recency info provided, skipping.")
            recency_chunks = []
        else:
            recency_chunks = [
                recent_chunk_to_prod_retrieval_chunk(change)
                for change in model_input.recency_info.recent_changes
            ]
        # NOTE: I don't care about the research chunks so did not
        # add recency_chunks for the returned research chunks
        return (
            recency_chunks + line_chunks_in_prod + sig_chunks_in_prod,
            list(retrieved_line_chunks) + list(retrieved_sig_chunks),
        )

    def _generate(
        self, model_input: ModelInput, chunks_in_prod: list[PromptChunk]
    ) -> CompletionResult:
        prefix_begin = (
            model_input.cursor_position - len(model_input.prefix)
            if model_input.cursor_position is not None
            else 0
        )
        prompt_input = PromptInput(
            prefix=model_input.prefix,
            suffix=model_input.suffix,
            prefix_begin=prefix_begin,
            path=model_input.path,
            retrieved_chunks=chunks_in_prod,
            lang=None,
        )
        assert self.generation_options.max_generated_tokens is not None
        formatter_outputs = self.prompt_formatter.format_prompt(
            prompt_input,
            max_output_token_count=self.generation_options.max_generated_tokens,
        )
        prompt_tokens = formatter_outputs.tokens()

        special_tokens = self.tokenizer.special_tokens
        assert isinstance(special_tokens, RagSpecialTokens)
        # Create new generation options to avoid mutating the original, stop tokens should be set on a per generation basis.
        # We did not rely on the self.config.fim_gen_mode to set the stop tokens, because we hope to get all the tokens generated by the model.
        # Instead of being truncated by skip or pause.
        new_generation_options = copy.deepcopy(self.generation_options)
        new_generation_options.stop_tokens = [
            special_tokens.eos,
            special_tokens.padding,
        ]
        raw_generated_output = self.model.raw_generate_tokens(
            prompt_tokens=prompt_tokens, options=new_generation_options
        )
        raw_generated_output_tokens = raw_generated_output.tokens

        processed_tokens, _ = fim_postprocess_generation(
            generated_tokens=copy.deepcopy(raw_generated_output_tokens),
            eod_id=special_tokens.eos,
            skip_id=special_tokens.skip,
            pause_id=special_tokens.pause,
            fim_gen_mode=self.config.fim_gen_mode,
        )

        all_tokens = prompt_tokens + processed_tokens
        final_text = self.tokenizer.detokenize(all_tokens)
        prompt_text = self.tokenizer.detokenize(prompt_tokens)
        generated_text = final_text[len(prompt_text) :]

        return CompletionResult(
            generated_text=generated_text,
            prompt_tokens=prompt_tokens,
            retrieved_chunks=[],
            retrieved_chunks_in_prod=chunks_in_prod,
            generated_tokens=copy.deepcopy(raw_generated_output_tokens),
            extra_output=ExtraGenerationOutputs(prompt_tokens=prompt_tokens),
        )

    @classmethod
    def from_yaml_config(cls, config: dict) -> ProdEldenSystem:
        """Returns a System object constructed using a config dictionary."""
        prod_tokenizer = create_tokenizer_by_name(config["tokenizer"])
        dense_retriever_config = config.get("dense_retriever", None)
        if dense_retriever_config is None:
            dense_retriever = None
        else:
            dense_retriever = factories.create_retriever(dense_retriever_config)
        signature_retriever_config = config.get("signature_retriever", None)
        if signature_retriever_config is None:
            signature_retriever = None
        else:
            signature_retriever = factories.create_retriever(signature_retriever_config)
        model = factories.create_model(config["model"])
        system_config = EldenSystemConfig.schema().load(config["config"])
        generation_options = GenerationOptions(**config["generation_options"])

        formatter_config = config["formatter_config"]
        formatter_name = formatter_config.pop("name")
        prompt_formatter_config = formatter_config.pop("prompt_formatter_config", None)
        apportionment_config = formatter_config.pop("apportionment_config", None)
        prod_formatter = get_completion_prompt_formatter_by_name(
            formatter_name,
            prod_tokenizer,
            apportionment_config,
            prompt_formatter_config,
        )
        return ProdEldenSystem(
            model=model,
            dense_retriever=dense_retriever,
            signature_retriever=signature_retriever,
            prompt_formatter=prod_formatter,
            tokenizer=prod_tokenizer,
            generation_options=generation_options,
            config=system_config,
        )


@register_system("prod_elden_w_reward")
class ProdEldenWRewardSystem(ProdEldenSystem):
    """The Production Elden System + Reject Sampling Support.

    For each completion, we will randomly sample `trials` different completions and then use the reward model to score each of them.
    The one with the highest score will be returned.
    In addition, the first trial is always the greedy decoding result -- so that we can compare the reward of the greedy decoding result and the random sampling result.
    """

    def __init__(
        self,
        model: GenerativeLanguageModel,
        reward_model: GenerativeLanguageModel,
        dense_retriever: DocumentIndex | None,
        signature_retriever: DocumentIndex | None,
        prompt_formatter: CompletionPromptFormatter,
        tokenizer: Tokenizer,
        generation_options: GenerationOptions,
        num_trials: int,
        config: EldenSystemConfig,
    ):
        super().__init__(
            model=model,
            dense_retriever=dense_retriever,
            signature_retriever=signature_retriever,
            prompt_formatter=prompt_formatter,
            tokenizer=tokenizer,
            generation_options=generation_options,
            config=config,
        )
        self.reward_model = reward_model
        self.num_trials = num_trials

    def load(self):
        self.reward_model.load()
        super().load()

    def unload(self):
        self.reward_model.unload()
        super().unload()

    def _score(self, prompt_tokens: list[int], candidate_tokens: list[int]) -> float:
        """Compute the reward of the candidate tokens."""
        outputs = self.reward_model.raw_generate_tokens(
            prompt_tokens + candidate_tokens,
            GenerationOptions(temperature=0.0, max_generated_tokens=1),
        )
        token_prob = outputs.logits[0].softmax(dim=-1)
        good_score = token_prob[self.tokenizer.special_tokens.good].item()  # type: ignore
        bad_score = token_prob[self.tokenizer.special_tokens.bad].item()  # type: ignore
        reward = good_score - bad_score
        return reward

    def _generate(
        self, model_input: ModelInput, chunks_in_prod: list[PromptChunk]
    ) -> CompletionResult:
        prefix_begin = (
            model_input.cursor_position - len(model_input.prefix)
            if model_input.cursor_position is not None
            else 0
        )
        prompt_input = PromptInput(
            prefix=model_input.prefix,
            suffix=model_input.suffix,
            prefix_begin=prefix_begin,
            path=model_input.path,
            retrieved_chunks=chunks_in_prod,
            lang=None,
        )
        assert self.generation_options.max_generated_tokens is not None
        formatter_outputs = self.prompt_formatter.format_prompt(
            prompt_input,
            max_output_token_count=self.generation_options.max_generated_tokens,
        )
        prompt_tokens = formatter_outputs.tokens()

        special_tokens = self.tokenizer.special_tokens
        assert isinstance(special_tokens, RagSpecialTokens)
        # Create new generation options to avoid mutating the original, stop tokens should be set on a per generation basis.
        # We did not rely on the self.config.fim_gen_mode to set the stop tokens, because we hope to get all the tokens generated by the model.
        # Instead of being truncated by skip or pause.
        new_generation_options = copy.deepcopy(self.generation_options)
        new_generation_options.stop_tokens = [
            special_tokens.eos,
            special_tokens.padding,
        ]
        trials: list[tuple[list[int], float, str]] = []
        for idx in range(self.num_trials):
            cur_gen_options = copy.deepcopy(new_generation_options)
            if idx == 0:  # always include the greedy generation
                cur_gen_options.temperature = 0
            raw_generated_output = self.model.raw_generate_tokens(
                prompt_tokens=prompt_tokens, options=cur_gen_options
            )
            raw_generated_output_tokens = raw_generated_output.tokens
            assert cur_gen_options.stop_tokens is not None
            if raw_generated_output_tokens[-1] in cur_gen_options.stop_tokens:
                cur_out_tokens = raw_generated_output_tokens[:-1]
            else:
                cur_out_tokens = raw_generated_output_tokens
            # Score the output.
            if idx == 0:
                reward = self._score(prompt_tokens, cur_out_tokens)
                trials.append((raw_generated_output_tokens, reward, "greedy"))
            elif raw_generated_output_tokens != trials[0][0]:
                reward = self._score(prompt_tokens, cur_out_tokens)
                trials.append((raw_generated_output_tokens, reward, "random"))
        trials.sort(key=lambda x: x[1], reverse=True)
        for i, trial in enumerate(trials):
            cur_first_line = get_first_n_lines(
                self.tokenizer.detokenize(trials[i][0]), n=1
            )
            label = f"{trial[1]:.4f} {trial[2]}"
            logging.info(f"Trial {i}: {label} : {cur_first_line}")
        raw_generated_output_tokens = trials[0][0]

        processed_tokens, _ = fim_postprocess_generation(
            generated_tokens=copy.deepcopy(raw_generated_output_tokens),
            eod_id=special_tokens.eos,
            skip_id=special_tokens.skip,
            pause_id=special_tokens.pause,
            fim_gen_mode=self.config.fim_gen_mode,
        )

        all_tokens = prompt_tokens + processed_tokens
        final_text = self.tokenizer.detokenize(all_tokens)
        prompt_text = self.tokenizer.detokenize(prompt_tokens)
        generated_text = final_text[len(prompt_text) :]

        return CompletionResult(
            generated_text=generated_text,
            prompt_tokens=prompt_tokens,
            retrieved_chunks=[],
            retrieved_chunks_in_prod=chunks_in_prod,
            generated_tokens=copy.deepcopy(raw_generated_output_tokens),
            extra_output=ExtraGenerationOutputs(prompt_tokens=prompt_tokens),
        )

    @classmethod
    def from_yaml_config(cls, config: dict) -> ProdEldenSystem:
        """Returns a System object constructed using a config dictionary."""
        prod_tokenizer = create_tokenizer_by_name(config["tokenizer"])
        dense_retriever_config = config.get("dense_retriever", None)
        if dense_retriever_config is None:
            dense_retriever = None
        else:
            dense_retriever = factories.create_retriever(dense_retriever_config)
        signature_retriever_config = config.get("signature_retriever", None)
        if signature_retriever_config is None:
            signature_retriever = None
        else:
            signature_retriever = factories.create_retriever(signature_retriever_config)
        model = factories.create_model(config["model"])
        reward_model = factories.create_model(config["reward_model"])
        system_config = EldenSystemConfig.schema().load(config["config"])
        generation_options = GenerationOptions(**config["generation_options"])

        formatter_config = config["formatter_config"]
        formatter_name = formatter_config.pop("name")
        prompt_formatter_config = formatter_config.pop("prompt_formatter_config", None)
        apportionment_config = formatter_config.pop("apportionment_config", None)
        prod_formatter = get_completion_prompt_formatter_by_name(
            formatter_name,
            prod_tokenizer,
            apportionment_config,
            prompt_formatter_config,
        )
        return ProdEldenWRewardSystem(
            model=model,
            reward_model=reward_model,
            dense_retriever=dense_retriever,
            signature_retriever=signature_retriever,
            prompt_formatter=prod_formatter,
            tokenizer=prod_tokenizer,
            generation_options=generation_options,
            num_trials=config["num_trails"],
            config=system_config,
        )

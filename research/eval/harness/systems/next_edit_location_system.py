"""A next edit location system that can suggest next edits."""

import logging
import typing

from cachetools import LRUCache

from base.diff_utils.diff_utils import File
from base.languages.language_guesser import guess_language
from base.prompt_format_next_edit.location_prompt_formatter import (
    LocalizationNextEditPromptInput,
)
from base.ranges.range_types import Char<PERSON>ang<PERSON>
from research.core.diff_utils import (
    get_modified_ranges,
)
from research.core.next_edit_location_prompt_input import (
    FileLocation,
    NextEditLocationOutput,
    NextEditLocationSystemInput,
)
from research.core.types import Document, DocumentId, Scored, check_not_none
from research.eval.harness import factories
from research.eval.harness.systems.abs_system import (
    AbstractSystem,
    register_system,
)
from research.models.meta_model import GenerativeLanguageModel
from research.retrieval.chunking_functions import SmartLineLevelChunker
from research.retrieval.retrieval_database import RetrievalDatabase
from research.retrieval.scorers.dense_scorer_v2 import DenseRetrievalScorerV2
from research.utils.repo_change_utils import Repo<PERSON>hange, patchset_from_repo_change


@register_system("next_edit_location")
class BasicNextEditLocationSystem(
    AbstractSystem[NextEditLocationSystemInput, NextEditLocationOutput]
):
    """A next edit location system that generates next edit locations."""

    def __init__(
        self,
        retriever: RetrievalDatabase,
        filter_input_ranges: bool = False,
        drop_instructions: bool = False,
    ):
        """Create a new next edit location system.

        Args:
            retriever: The retriever to use for retrieving chunks.
            filter_input_ranges: Whether to filter out retrieved chunks that intersect
                with a recently-changed hunk in the input.
            drop_instructions: Whether to drop the instruction from the prompt.
        """
        self._retriever = retriever
        self.__loaded = False
        self._filter_input_ranges = filter_input_ranges
        self._generate_cache: LRUCache[
            NextEditLocationSystemInput,
            NextEditLocationOutput,
        ] = LRUCache(maxsize=500)
        # Add in any existing documents to the seen docs.
        self._seen_docs: dict[DocumentId, Document] = {
            doc.id: doc for doc in retriever.get_docs(retriever.get_doc_ids())
        }
        self._drop_instructions = drop_instructions

    def generate(
        self,
        model_input: NextEditLocationSystemInput,
    ) -> NextEditLocationOutput:
        if model_input in self._generate_cache:
            return self._generate_cache[model_input]

        doc_ids = model_input.doc_ids

        # Filter out documents that do not match the restrict_to_file path
        if model_input.restrict_to_file is not None:
            doc_ids = frozenset(
                [
                    doc_id
                    for doc_id in doc_ids
                    if self._seen_docs[doc_id].path == model_input.restrict_to_file
                ]
            )

            # Log an error if we did not find 1 document.
            if len(doc_ids) != 1:
                logging.error(
                    f"{model_input.restrict_to_file=}, but found {len(doc_ids)} matching documents and they are: {doc_ids}."
                )
                return NextEditLocationOutput(
                    scored_candidates=[], top_chunks=[], debug_info={}
                )

        scorer = self._retriever.scorer
        assert isinstance(scorer, DenseRetrievalScorerV2)

        query_type = scorer.query_formatter.input_type
        assert query_type == LocalizationNextEditPromptInput, f"{query_type=}"
        assert isinstance(model_input, NextEditLocationSystemInput)

        query = LocalizationNextEditPromptInput(
            instruction="" if self._drop_instructions else model_input.instruction,
            recent_changes=model_input.recent_changes,
            current_file=File("", ""),
            edit_region=model_input.selected_range or CharRange(0, 0),
        )
        chunks, scores = self._retriever.query(
            query,
            doc_ids=doc_ids,
            top_k=model_input.top_k,
        )
        scored_chunks_with_path = [
            Scored(chunk, score) for chunk, score in zip(chunks, scores) if chunk.path
        ]

        if self._filter_input_ranges:
            # Filter out chunks that intersect with a recently-changed hunk.
            # Some retrievers prefer the same chunks we gave it as input, which hurts
            # the ranking of other chunks. Of course, by filtering out these chunks, we
            # cannot suggest locations within already-modified chunks, which hurts
            # recall.
            # Note that if this ever becomes a performance issue, we can use
            # the intervaltree library to perform more efficient intersections
            # or simply ignore cases with too many hunks.
            recent_changes = model_input.recent_changes
            # TODO(arun): Use diff_formatter.format_file_changes
            repo_change = RepoChange.from_file_changes(recent_changes)
            patchset0 = patchset_from_repo_change(
                repo_change,
                # These are defaults only used for the ethanol baseline.
                num_context_lines=0,
                ignore_whitespace=True,
            )

            target_ranges_by_path = {
                pfile.path: get_modified_ranges(pfile) for pfile in patchset0
            }
            scored_chunks_with_path = [
                scored_chunk
                for scored_chunk in scored_chunks_with_path
                if scored_chunk.item.path
                and not any(
                    scored_chunk.item.range.intersect(target_range) is not None
                    for target_range in target_ranges_by_path.get(
                        scored_chunk.item.path, []
                    )
                )
            ]

        candidate_locations = [
            Scored(
                FileLocation(scored_chunk.item.path, scored_chunk.item.line_range),
                scored_chunk.score,
            )
            for scored_chunk in scored_chunks_with_path
            if scored_chunk.item.path
        ]

        candidate_locations = candidate_locations[: model_input.top_k]
        top_chunks = [
            scored_chunk.item
            for scored_chunk in scored_chunks_with_path[: model_input.top_k]
        ]

        output = NextEditLocationOutput(
            scored_candidates=candidate_locations,
            top_chunks=top_chunks,
            debug_info={},
        )
        self._generate_cache[model_input] = output
        return output

    def get_documents(self, doc_ids: typing.Iterable[DocumentId]) -> list[Document]:
        """Return all documents indexed by the retriever."""
        return [
            self._seen_docs[doc_id] for doc_id in doc_ids if doc_id in self._seen_docs
        ]

    @classmethod
    def from_yaml_config(cls, config: dict):
        retriever = factories.create_retriever(config["retriever"])
        assert isinstance(retriever, RetrievalDatabase)
        kwargs = {}
        if "filter_input_ranges" in config:
            kwargs["filter_input_ranges"] = config["filter_input_ranges"]
        if "top_k" in config:
            kwargs["top_k"] = config["top_k"]
        return cls(retriever=retriever, **kwargs)

    def load(self):
        """Load the model."""
        if not self.__loaded:
            self._retriever.load()
        self.__loaded = True

    def unload(self):
        """Unload the model."""
        if self.__loaded:
            self._retriever.unload()
            self.__loaded = False

    def get_model(self) -> GenerativeLanguageModel:
        raise NotImplementedError()

    def add_docs(self, src_files: typing.Collection[Document]):
        # We maintain the set of seen documents to avoid reporting these as missing
        # blobs back to the client.
        self._seen_docs.update((doc.id, doc) for doc in src_files)

        # Filter out any documents that are not .
        src_files = [
            doc
            for doc in src_files
            # Filter out any documents with unrecognized languages (like .lock files)
            # and .ipynb files (which just look like a huge JSON file :-/).
            if guess_language(doc.path) is not None and not doc.path.endswith(".ipynb")
        ]
        self._retriever.add_docs(src_files)

    def remove_docs(self, doc_ids: typing.Collection[DocumentId]):
        for doc_id in doc_ids:
            self._seen_docs.pop(doc_id, None)
        self._retriever.remove_docs(doc_ids)

    def get_doc_ids(self) -> typing.AbstractSet[DocumentId]:
        return self._seen_docs.keys()

    def clear_retriever(self):
        self._retriever.remove_all_docs()
        self._seen_docs.clear()


@register_system("next_edit_single_file_location")
class SingleFileNextEditLocationSystem(
    AbstractSystem[NextEditLocationSystemInput, NextEditLocationOutput]
):
    """A system that generates next edit locations in a single file heuristically.
    Used to get fast localization on single-file requests.
    Only implemented in single-file mode.
    """

    _seen_docs: dict[DocumentId, Document]
    _chunker: SmartLineLevelChunker

    def __init__(
        self,
        max_chunk_chars: int = 2000,
    ):
        """Create a single file location system.

        Args:
            max_chunk_chars: the maximum number of characters in a chunk produced by this system.
        """
        self._chunker = SmartLineLevelChunker(max_chunk_chars)
        self._seen_docs = {}
        self._generate_cache: LRUCache[
            NextEditLocationSystemInput,
            NextEditLocationOutput,
        ] = LRUCache(maxsize=500)

    def generate(
        self,
        model_input: NextEditLocationSystemInput,
    ) -> NextEditLocationOutput:
        if model_input in self._generate_cache:
            return self._generate_cache[model_input]

        if model_input.restrict_to_file is None:
            raise ValueError(
                "model_input.restrict_to_file must be set to use the single file location system."
            )

        # Get all the docs with path matching the restrict_to_file.
        docs = [
            doc
            for doc_id in model_input.doc_ids
            if (doc := self._seen_docs.get(doc_id))
            and doc.path == model_input.restrict_to_file
        ]

        # Make sure we only find a single doc
        if len(docs) != 1:
            logging.error(
                f"{model_input.restrict_to_file=}, but found {len(docs)} matching documents and they are:\n{docs}."
            )
            return NextEditLocationOutput(
                scored_candidates=[], top_chunks=[], debug_info={}
            )

        # Split the text of the document into chunks
        chunk_list = self._chunker.split_into_chunks(docs[0])

        if model_input.selected_range is not None:
            # Sort the chunks based on distance to center of selected range
            selected_range_center = (
                model_input.selected_range.start + model_input.selected_range.stop
            ) / 2
            chunk_list.sort(
                key=lambda chunk: abs(
                    selected_range_center - (chunk.char_offset + chunk.length / 2)
                )
            )
        else:
            # Sort the chunks by the order in which they appear in the file
            chunk_list.sort(key=lambda chunk: chunk.char_offset)

        top_chunks = chunk_list[: model_input.top_k]

        # Give a zero score to each chunk to put them in the location output form
        candidate_locations = [
            Scored(FileLocation(check_not_none(chunk.path), chunk.line_range), 0)
            for chunk in top_chunks
        ]

        output = NextEditLocationOutput(
            scored_candidates=candidate_locations,
            top_chunks=top_chunks,
            debug_info={},
        )

        # Cache the output before we return it
        self._generate_cache[model_input] = output

        return output

    def get_documents(self, doc_ids: typing.Iterable[DocumentId]) -> list[Document]:
        """Return all indexed docuemnts with the given ids."""
        return [
            self._seen_docs[doc_id] for doc_id in doc_ids if doc_id in self._seen_docs
        ]

    @classmethod
    def from_yaml_config(cls, config: dict):
        return cls(**config)

    def load(self):
        """There is no model to load for the single file system."""
        pass

    def unload(self):
        """There is no model to unload for the single file system."""
        pass

    def get_model(self) -> GenerativeLanguageModel:
        raise NotImplementedError()

    def add_docs(self, src_files: typing.Collection[Document]):
        """Add the documents with the given ids to the system's seen_docs."""
        # We maintain the set of seen documents to avoid reporting these as missing
        # blobs back to the client.
        self._seen_docs.update((doc.id, doc) for doc in src_files)

    def remove_docs(self, doc_ids: typing.Collection[DocumentId]):
        """Removes the documents with the given ids from the system's seen_docs."""
        for doc_id in doc_ids:
            self._seen_docs.pop(doc_id, None)

    def get_doc_ids(self) -> typing.AbstractSet[DocumentId]:
        """Get all ids of documents seen by this system."""
        return self._seen_docs.keys()

    def clear_retriever(self):
        """There is no retriver model to clear for the single file system."""
        self._seen_docs.clear()

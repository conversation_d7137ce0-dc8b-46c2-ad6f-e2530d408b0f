"""This is a system that is used for remote evaluation."""

import logging
import time
import typing
from collections import Counter
from dataclasses import dataclass, field

from research.core.artifacts import artifacts_enabled, post_artifact
from research.core.types import Document, DocumentId
from research.eval.harness.systems.abs_system import (
    CodeCompleteInput,
    CodeCompleteSystem,
    CompletionResult,
    register_system,
)
from research.eval.harness.systems.remote_lib import (
    AugmentClientConfig,
    RemoteCompletionConfig,
    RemoteCompletionManager,
    RemoteRetriever,
    RemoteRetrieverConfig,
    get_augment_client,
    get_model_info,
)
from research.core.tokenizers import get_tokenizer
from research.models.all_models import get_model
from research.models.meta_model import ExtraGenerationOutputs, GenerativeLanguageModel

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


@register_system("remote_completion")
class RemoteCompletionSystem(CodeCompleteSystem):
    """Code completion system that calls out to a remote instance via AugmentClient."""

    def __init__(
        self,
        client_config: AugmentClientConfig,
        retriever_config: RemoteRetrieverConfig,
        completion_config: RemoteCompletionConfig,
        model_name: str | None = None,
        tokenizer_name: str | None = None,
    ):
        self.requested_model_name = model_name
        self.client = get_augment_client(client_config)
        self.retriever_config = retriever_config
        self.completion_config = completion_config

        # TODO(jeff): AbstractSystem requires a get_model() interface, so we use
        # null model just to provide a dummy model so things don't break.
        self.model = get_model("null")
        self._retriever: RemoteRetriever | None = None
        self.completion_manager: RemoteCompletionManager | None = None
        if tokenizer_name is not None:
            self.model.prompt_formatter.rebind(tokenizer=get_tokenizer(tokenizer_name))

    def load(self):
        """Load the system."""
        get_model_response = self.client.get_models()
        model_info = get_model_info(get_model_response, self.requested_model_name)

        logger.info("Remote client: %s", str(self.client))
        logger.info("Using remote model: %s", model_info)
        logger.info("Supported languages: %s", get_model_response.languages)
        logger.info("Feature flags: %s", get_model_response.feature_flags)

        model_client = self.client.client_for_model(model_info.name)

        assert get_model_response.feature_flags is not None
        max_upload_size_bytes = get_model_response.feature_flags.max_upload_size_bytes
        languages = (
            None
            if get_model_response.feature_flags.bypass_language_filter
            else list(get_model_response.languages)
        )

        self._retriever = RemoteRetriever(
            client=self.client,
            model_info=model_info,
            config=self.retriever_config,
            max_upload_size_bytes=max_upload_size_bytes,
            languages=languages,
        )
        self.completion_manager = RemoteCompletionManager(
            model_client=model_client,
            model_info=model_info,
            config=self.completion_config,
        )

    def unload(self):
        """Unload the system."""
        # TODO(jeff): log filtered out documents retriever stats.
        self._retriever = None
        self.completion_manager = None

    @property
    def retriever(self) -> RemoteRetriever:
        """Gets the remote retriever. Should only be called while loaded."""
        assert self._retriever is not None
        return self._retriever

    def generate(self, model_input: CodeCompleteInput) -> CompletionResult:
        """Returns a Completion object.

        This object contains completion text, prompt tokens, retrieved chunks,
        and additional model outputs.
        """
        assert self.completion_manager is not None

        blob_names = model_input.doc_ids
        if blob_names is None:
            logger.warning("No doc_ids provided, using all blob_names.")
            blob_names = list(self.retriever.get_blob_names())

        blobs = self.retriever.get_blobs(blob_names)
        remote_result = self.completion_manager.generate(model_input, blobs)
        # TODO(jeff): completion tasks use artifacts, whereas edit eval uses
        # CompletionResult.extra_outputs. It'd be better if there were some canonical
        # way of logging artifacts.
        if artifacts_enabled():
            post_artifact(
                {
                    "generated_text_per_step": remote_result.generated_text_per_step,
                    "request_ids": remote_result.request_ids,
                }
            )
        return CompletionResult(
            generated_text=remote_result.generated_text,
            prompt_tokens=[],
            retrieved_chunks=[],
            extra_output=ExtraGenerationOutputs(
                additional_info={"request_ids": remote_result.request_ids},
            ),
        )

    def add_docs(self, src_files: typing.Collection[Document]):
        """Ingest a copy of the source code repository."""
        start = time.time()
        resp = self.retriever.add_docs(src_files)
        elapsed = time.time() - start
        extension_counts = Counter(
            blob_info.extension for blob_info in resp.filtered_by_extension
        )
        extension_counts = dict(extension_counts.most_common())
        logger.info(
            "Indexed %d documents in %.1f seconds, "
            "filtered %d by id, %d by size, %d by extension (%s), "
            "not indexed due to timeout (%s)",
            len(resp.successful_blobs),
            elapsed,
            len(resp.filtered_by_id),
            len(resp.filtered_by_size),
            len(resp.filtered_by_extension),
            extension_counts,
            len(resp.nonindexed_blob_names),
        )

    def remove_docs(self, doc_ids: typing.Collection[DocumentId]):
        """Remove documents from the retriever."""
        self.retriever.remove_docs(doc_ids)

    def get_doc_ids(self) -> set[DocumentId]:
        # TODO(AU-2552): temporarily return private state until we
        # implement the new add_docs api.
        return set(self.retriever.get_blob_names())

    def get_num_docs(self):
        return self.retriever.get_num_docs()

    def clear_retriever(self):
        """Clear any stored documents from the retriever."""
        self.retriever.clear_docs()

    def get_model(self) -> GenerativeLanguageModel:
        """Return the model."""
        return self.model

    @dataclass(frozen=True)
    class _RemoteCompletionSystemConfig:
        """Schema for configuring a RemoteCompletionSystem."""

        client: dict
        """The client config. Url is required."""

        retriever: dict = field(default_factory=dict)
        """The retriever config."""

        completion: dict = field(default_factory=dict)
        """The completion config."""

        model_name: str | None = None
        """The desired model name, None to select the default model."""

        tokenizer_name: str | None = None
        """The tokenizer name, None to select the default tokenizer."""

    @classmethod
    def from_yaml_config(cls, config: dict) -> "RemoteCompletionSystem":
        """Returns a System object constructed using a config dictionary."""
        xconfig = cls._RemoteCompletionSystemConfig(**config)
        return RemoteCompletionSystem(
            client_config=AugmentClientConfig(**xconfig.client),
            retriever_config=RemoteRetrieverConfig(**xconfig.retriever),
            completion_config=RemoteCompletionConfig(**xconfig.completion),
            model_name=xconfig.model_name,
            tokenizer_name=xconfig.tokenizer_name,
        )

"""Code edit system that does the inference using a remote instance."""

import logging
import time
import typing
from collections import Counter
from dataclasses import dataclass, field

from research.core.artifacts import artifacts_enabled, post_artifact
from research.core.types import Document, DocumentId
from research.eval.harness.systems.abs_system import (
    CodeEditResult,
    CodeInstructInput,
    CodeInstructSystem,
    register_system,
)
from research.eval.harness.systems.remote_lib import (
    AugmentClientConfig,
    RemoteEditConfig,
    RemoteEditManager,
    RemoteRetriever,
    RemoteRetrieverConfig,
    get_augment_client,
    get_model_info,
)
from research.models.all_models import GenerativeLanguageModel, get_model

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


@register_system("remote_edit")
class RemoteEditSystem(CodeInstructSystem):
    """Code edit system that calls out to a remote instance via AugmentClient."""

    def __init__(
        self,
        client_config: AugmentClientConfig,
        retriever_config: RemoteRetrieverConfig,
        edit_config: RemoteEditConfig,
        model_name: str | None = None,
    ):
        self.requested_model_name = model_name
        self.client = get_augment_client(client_config)
        self.retriever_config = retriever_config
        self.edit_config = edit_config

        # TODO(jeff): AbstractSystem requires a get_model() interface, so we use
        # null model just to provide a dummy model so things don't break.
        self.model = get_model("null")
        self._retriever: RemoteRetriever | None = None
        self.edit_manager: RemoteEditManager | None = None

    def load(self):
        """Load the system."""
        get_model_response = self.client.get_models()
        model_info = get_model_info(get_model_response, self.requested_model_name)

        logger.info("Remote client: %s", str(self.client))
        logger.info("Using remote model: %s", model_info)
        logger.info("Supported languages: %s", get_model_response.languages)
        logger.info("Feature flags: %s", get_model_response.feature_flags)

        model_client = self.client.client_for_model(model_info.name)
        assert get_model_response.feature_flags is not None
        max_upload_size_bytes = get_model_response.feature_flags.max_upload_size_bytes
        languages = (
            None
            if get_model_response.feature_flags.bypass_language_filter
            else list(get_model_response.languages)
        )

        self._retriever = RemoteRetriever(
            client=self.client,
            model_info=model_info,
            config=self.retriever_config,
            max_upload_size_bytes=max_upload_size_bytes,
            languages=languages,
        )
        self.edit_manager = RemoteEditManager(
            model_client=model_client,
            model_info=model_info,
            config=self.edit_config,
        )

    def unload(self):
        """Unload the system."""
        # TODO(jeff): log filtered out documents retriever stats.
        self._retriever = None
        self.edit_manager = None

    @property
    def retriever(self) -> RemoteRetriever:
        """Gets the remote retriever. Should only be called while loaded."""
        assert self._retriever is not None
        return self._retriever

    def generate(self, model_input: CodeInstructInput) -> CodeEditResult:
        """Returns a Completion object.

        This object contains completion text, prompt tokens, retrieved chunks,
        and additional model outputs.
        """
        assert self.edit_manager is not None
        blob_names = model_input.doc_ids
        if blob_names is None:
            logger.warning("No doc_ids provided, using all blob_names.")
            blob_names = list(self.retriever.get_blob_names())
        blobs = self.retriever.get_blobs(blob_names)
        remote_result = self.edit_manager.edit(model_input, blobs)

        if remote_result.filtered_reason:
            # TODO(jeff): raise a special error and let the task handle it.
            # This would need to be documented in the abs_system interface.
            logger.warning(
                "Edit path=%s was skipped because: %s",
                model_input.path,
                remote_result.filtered_reason,
            )

        # TODO(jeff): completion tasks use artifacts, whereas edit eval uses
        # CompletionResult.extra_outputs. It'd be better if there were some canonical
        # way of logging artifacts.
        if artifacts_enabled():
            post_artifact(
                {
                    "request_id": remote_result.request_id,
                    "filtered_reason": remote_result.filtered_reason,
                }
            )
        result = CodeEditResult(
            generated_text=remote_result.generated_text,
            prompt_tokens=[],
            retrieved_chunks=[],
        )
        result.extra_output.additional_info["request_id"] = remote_result.request_id
        result.extra_output.additional_info["session_id"] = str(
            self.client.request_session_id
        )
        return result

    def add_docs(self, src_files: typing.Collection[Document]):
        """Ingest a copy of the source code repository."""
        start = time.time()
        resp = self.retriever.add_docs(src_files)
        elapsed = time.time() - start
        extension_counts = Counter(
            blob_info.extension for blob_info in resp.filtered_by_extension
        )
        extension_counts = dict(extension_counts.most_common())
        logger.info(
            "Indexed %d documents in %.1f seconds, "
            "filtered %d by id, %d by size, %d by extension (%s)",
            len(resp.successful_blobs),
            elapsed,
            len(resp.filtered_by_id),
            len(resp.filtered_by_size),
            len(resp.filtered_by_extension),
            extension_counts,
        )

    def remove_docs(self, doc_ids: typing.Collection[DocumentId]):
        """Remove documents from the retriever."""
        self.retriever.remove_docs(doc_ids)

    def get_num_docs(self):
        return self.retriever.get_num_docs()

    def clear_retriever(self):
        """Clear any stored documents from the retriever."""
        self.retriever.clear_docs()

    def get_model(self) -> GenerativeLanguageModel:
        """Return the model."""
        return self.model

    @dataclass(frozen=True)
    class _RemoteEditSystemConfig:
        """Schema for configuring a RemoteEditSystem."""

        client: dict
        """The client config. Url is required."""
        retriever: dict = field(default_factory=dict)
        """The retriever config."""
        edit: dict = field(default_factory=dict)
        """The edit config."""
        model_name: str | None = None
        """The desired model name, None to select the default model."""

    @classmethod
    def from_yaml_config(cls, config: dict) -> "RemoteEditSystem":
        """Returns a System object constructed using a config dictionary."""
        xconfig = cls._RemoteEditSystemConfig(**config)

        # TODO(jeff): HACK: currently find missing does not work for edit models
        # so we need to override the internal defaults so that we do not issue
        # find missing calls. We do the retries during the actual edit calls instead.
        # When find_missing support edit models, we can remove these without having
        # to change configs.
        xconfig.retriever.setdefault("disable_wait_indexing", True)
        xconfig.edit.setdefault("indexing_retry_count", 3)

        return RemoteEditSystem(
            client_config=AugmentClientConfig(**xconfig.client),
            retriever_config=RemoteRetrieverConfig(**xconfig.retriever),
            edit_config=RemoteEditConfig(**xconfig.edit),
            model_name=xconfig.model_name,
        )

"""System for basic retrieval-augmented generation."""

import logging
import typing
import dataclasses

from base.prompt_format.common import (
    Exchange,
    get_request_message_as_text,
    get_response_message_as_text,
)
from base.tokenizers import create_tokenizer_by_name, Tokenizer
from base.prompt_format_chat.prompt_formatter import (
    Prompt<PERSON>hunk,
    ChatPromptFormatter,
    StructuredChatPromptOutput,
    TokenizedChatPromptOutput,
)
from base.prompt_format_chat.legacy_binks.binks_llama3_prompt_formatter import (
    DENSE_RETRIEVER_ORIGIN,
)
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from base.prompt_format_chat import (
    get_chat_prompt_formatter_by_name,
    get_structured_chat_prompt_formatter_by_name,
    PromptFormatterName,
)
from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment
from base.prompt_format_rerank.prompt_formatter import Chat<PERSON><PERSON>kerPromptInput

from research.core.chat_prompt_input import ResearchChatPromptInput
from research.core.model_input import ModelInput, ChatInput, DialogTurn
from research.core.types import Chunk, Document, DocumentId
from research.eval.harness import factories
from research.eval.harness.systems.abs_system import (
    ChatSystem,
    ChatResult,
    register_system,
)
from research.models import (
    ExtraGenerationOutputs,
    GenerationOptions,
    GenerativeLanguageModel,
)
from research.models.meta_model import FimGenMode
from research.retrieval.rerankers.reranker_interface import Reranker_V2
from research.retrieval.types import DocumentIndex
from research.utils.inspect_indexed_dataset import (
    escape_control_chars,
)

logger = logging.getLogger(__name__)


@dataclasses.dataclass
class MiscRAGSystemConfig:
    """This contains experimental config that has not yet been upstreamed to system components.

    Consider upstreaming arguments in here if they become long-living.
    """

    retriever_top_k: int = 32


@register_system("chat_rag")
class ChatRAGSystem(ChatSystem):
    """Repo-aware chat system."""

    def __init__(
        self,
        model: GenerativeLanguageModel,
        retriever: DocumentIndex,
        generation_options: GenerationOptions,
        experimental_config: MiscRAGSystemConfig,
        tokenizer_name: str,
        prompt_formatter_name: str,
        token_apportionment: ChatTokenApportionment,
        reranker: Reranker_V2 | None = None,
        verbose: bool = False,
        retrieve_only: bool = False,
    ):
        self.model = model
        self.retriever = retriever
        self.generation_options = generation_options
        self.experimental_config = experimental_config
        self.verbose = verbose
        self.tokenizer_name = tokenizer_name
        self.prompt_formatter_name = prompt_formatter_name
        self.token_apportionment = token_apportionment
        self.reranker = reranker
        self.retrieve_only = retrieve_only
        self.__loaded = False

    def load(self):
        if not self.__loaded:
            self.model.load()
            self.retriever.load()
            self.tokenizer = create_tokenizer_by_name(self.tokenizer_name)
            try:
                self.prompt_formatter = get_chat_prompt_formatter_by_name(
                    self.prompt_formatter_name,
                    self.tokenizer,
                    self.token_apportionment,
                )
            except ValueError:
                self.prompt_formatter = get_structured_chat_prompt_formatter_by_name(
                    self.prompt_formatter_name,  # pyright: ignore
                    self.token_apportionment,
                )
            if self.reranker is not None:
                self.reranker.load()
        self.__loaded = True

    def unload(self):
        if self.__loaded:
            # The unload crashes with LLaMA 70B and usually isn't needed.
            # Therefore, we disable it for the time being.
            # See discussion here https://augment-wic8570.slack.com/archives/C0439560PAR/p1721975555484809.
            # self.model.unload()
            self.retriever.unload()
            del self.tokenizer
            del self.prompt_formatter
            if self.reranker is not None:
                self.reranker.unload()
                del self.reranker
            self.__loaded = False

    def add_docs(self, src_files: typing.Collection[Document]):
        """Ingest a copy of the source code repository.

        Args:
          src_files: list of Documents to add
        """
        logger.info(f"Adding {len(list(src_files))} documents.")
        self.retriever.add_docs(src_files)  # type: ignore
        total_docs = len(self.retriever.get_doc_ids())
        logger.info(f"There are now {total_docs} total docs.")

    def remove_docs(self, doc_ids: typing.Collection[DocumentId]):
        """Remove documents from the retriever."""
        logger.info(f"Removing {len(list(doc_ids))} documents.")
        self.retriever.remove_docs(doc_ids)
        total_docs = len(self.retriever.get_doc_ids())
        logger.info(f"There are now {total_docs} total docs.")

    def get_doc_ids(self) -> typing.AbstractSet[DocumentId]:
        return self.retriever.get_doc_ids()

    def get_docs(self, doc_ids: typing.Iterable[DocumentId]) -> list[Document]:
        return self.retriever.get_docs(doc_ids)

    def clear_retriever(self):
        """Clear any stored documents from the retriever."""
        self.retriever.remove_all_docs()

    def get_model(self) -> GenerativeLanguageModel:
        return self.model

    def log_likelihood_continuation(
        self, model_input: ResearchChatPromptInput, continuation: str
    ) -> float | None:
        """Returns the conditional log likelihood of the continuation, conditioned on the model input."""
        raise NotImplementedError()

    def generate(
        self,
        model_input: ResearchChatPromptInput,
    ) -> ChatResult:
        """Generate a completion."""
        model_input, id2chunk = self._retrieve(model_input)
        generation = self._generate(model_input, id2chunk)

        if self.verbose:
            prompt = escape_control_chars(
                self.tokenizer.detokenize(generation.prompt_tokens)
            )
            logger.info("Full prompt:")
            logger.info(prompt)
            logger.info("Original response:")
            logger.info(escape_control_chars(generation.generated_text))

        return generation

    @dataclasses.dataclass
    class _RAGSystemConfig:
        """Schema for configuring a System."""

        model: dict
        retriever: dict
        generation_options: dict
        experimental: dict
        prompt_formatter: dict
        reranker: dict | None = None
        verbose: bool = False
        retrieve_only: bool = False

    @classmethod
    def from_yaml_config(cls, config: dict) -> "ChatRAGSystem":
        """Returns a System object constructed using a config dictionary."""
        # prompt configuration happens as part of creating the model
        xconfig = cls._RAGSystemConfig(**config)
        model = factories.create_model(xconfig.model)
        retriever = factories.create_retriever(xconfig.retriever)
        reranker = (
            factories.create_reranker_v2(xconfig.reranker) if xconfig.reranker else None
        )
        generation_options = GenerationOptions(**xconfig.generation_options)
        experimental_config = MiscRAGSystemConfig(**xconfig.experimental)
        tokenizer_name = xconfig.prompt_formatter.pop("tokenizer_name")
        prompt_formatter_name = xconfig.prompt_formatter.pop("prompt_formatter_name")
        token_apportionment = ChatTokenApportionment(**xconfig.prompt_formatter)
        return ChatRAGSystem(
            model,
            retriever,
            generation_options,
            experimental_config=experimental_config,
            tokenizer_name=tokenizer_name,
            prompt_formatter_name=prompt_formatter_name,
            token_apportionment=token_apportionment,
            reranker=reranker,
            verbose=xconfig.verbose,
            retrieve_only=xconfig.retrieve_only,
        )

    # --------------- INTERNAL METHODS -------------------------------
    def _retrieve(
        self,
        prompt_input: ResearchChatPromptInput,
    ) -> tuple[ResearchChatPromptInput, dict[str, Chunk]]:
        """Retrieve chunks from the retriever."""
        retriever_prompt_input = ChatRetrieverPromptInput(
            prefix=prompt_input.prefix,
            suffix=prompt_input.suffix,
            path=prompt_input.path,
            message=get_request_message_as_text(prompt_input.message),
            selected_code=prompt_input.selected_code,
            chat_history=list(prompt_input.chat_history),
        )
        retrieved_chunks, _ = self.retriever.query(
            retriever_prompt_input,
            doc_ids=prompt_input.doc_ids,
            top_k=self.experimental_config.retriever_top_k,
        )

        if self.reranker is not None:
            reranked_chunks, scores = self.reranker.rerank(
                ChatRerankerPromptInput(
                    message=get_request_message_as_text(prompt_input.message),
                    dialogue_history=[],
                    candidate_chunks=[],
                ),
                retrieved_chunks,
            )
            reranked_chunks_with_scores = list(zip(reranked_chunks, scores))
            reranked_chunks_with_scores.sort(key=lambda x: x[1], reverse=True)
            retrieved_chunks = [c for c, _ in reranked_chunks_with_scores]

        prompt_chunks = []
        id2chunk = {}
        for chunk in retrieved_chunks:
            path = "" if chunk.parent_doc.path is None else chunk.parent_doc.path
            blob_name = "" if chunk.parent_doc.id is None else chunk.parent_doc.id
            prompt_chunks.append(
                PromptChunk(
                    text=chunk.text,
                    path=path,
                    unique_id=chunk.id,
                    origin=DENSE_RETRIEVER_ORIGIN,
                    blob_name=blob_name,
                    char_start=chunk.char_offset,
                    char_end=chunk.char_offset + chunk.length,
                )
            )
            id2chunk[chunk.id] = chunk
        prompt_input = dataclasses.replace(prompt_input, retrieved_chunks=prompt_chunks)
        return prompt_input, id2chunk

    def _generate(
        self, model_input: ResearchChatPromptInput, id2chunk: dict[str, Chunk]
    ) -> ChatResult:
        extra_output = ExtraGenerationOutputs()
        prompt_output = self.prompt_formatter.format_prompt(model_input)
        if isinstance(prompt_output, StructuredChatPromptOutput):
            # transform prompt_output to a ModelInput object
            generated_text = (
                self.model.generate(
                    _convert_to_model_input(prompt_output),
                    self.generation_options,
                    extra_output,
                )
                if not self.retrieve_only
                else ""
            )
        else:
            assert isinstance(prompt_output, TokenizedChatPromptOutput)
            generated_text = (
                self.model.raw_generate(prompt_output.tokens, self.generation_options)
                if not self.retrieve_only
                else ""
            )

        retrieved_chunks_ids_in_prompt = {
            chunk.unique_id for chunk in prompt_output.retrieved_chunks_in_prompt
        }
        retrieved_chunks = []
        # Iterative over the original chunks to preserve the order.
        for prompt_chunk in model_input.retrieved_chunks:
            assert prompt_chunk.unique_id is not None
            if prompt_chunk.unique_id not in retrieved_chunks_ids_in_prompt:
                continue
            retrieved_chunks.append(id2chunk[prompt_chunk.unique_id])

        return ChatResult(
            generated_text=generated_text,
            prompt_tokens=prompt_output.tokens
            if isinstance(prompt_output, TokenizedChatPromptOutput)
            else [],
            retrieved_chunks=retrieved_chunks,
            extra_output=extra_output,
        )


# Different from `research.eval.harness.tasks.chat_eval_task.convert_to_model_input`
#
def _convert_to_model_input(out: StructuredChatPromptOutput) -> ModelInput:
    model_input = ModelInput(
        prefix="",
        suffix="",
        retrieved_chunks=[],
        path="",
        selected_code="",
        target="",
        chat_input=ChatInput(
            history=[_exchange_to_dialog_turn(e) for e in out.chat_history],
            request=get_request_message_as_text(out.message),
            system_prompt=out.system_prompt,
        ),
    )
    return model_input


def _exchange_to_dialog_turn(exchange: Exchange) -> DialogTurn:
    return DialogTurn(
        request=get_request_message_as_text(exchange.request_message),
        response=get_response_message_as_text(exchange.response_text or ""),
    )

"""A system for generating next edits using llama."""

from __future__ import annotations

import re
from dataclasses import dataclass, field
from typing import Callable, Iterable, Literal, Optional, Tuple

from research.core.types import Document, DocumentId
from research.eval.harness.systems.abs_system import AbstractSystem, register_system
from research.eval.harness.systems.next_edit_gen_system import (
    EditGenSystemInput,
    EditGenSystemOutput,
    GenerationCanceledError,
)
from research.llm_apis.chat_utils import ChatClient
from research.models.meta_model import GenerationOptions, GenerativeLanguageModel
from research.models.null_model import NullModel
from research.next_edits.diff_formatter import format_file_changes


@register_system("next_edit_llama_gen")
@dataclass
class NextEditGenLlamaSystem(AbstractSystem[EditGenSystemInput, EditGenSystemOutput]):
    """A suggested edit generation system that can suggest next edits."""

    chat_client: ChatClient
    prompt_type: Literal["llama", "sonnet"]
    generation_options: GenerationOptions
    docs: list[Document] = field(default_factory=list)

    def _generate_edit_sonnet(
        self, sys_input: EditGenSystemInput
    ) -> Tuple[Optional[str], Optional[str]]:
        input_diff = format_file_changes(
            changes=sys_input.recent_changes,
            diff_context_lines=5,
        )

        current_code = sys_input.prefix + sys_input.selected_code + sys_input.suffix

        system_prompt = """\
You are a helpful software engineering assistant, that can suggest the next edit
in a code repository given previous edits that have been performed so far.
The code you generated should be final. It should be the developer that the user will
want to check into their codebase.
"""

        if sys_input.instruction:
            instruction_prompt = f"""\

This is my task:

{sys_input.instruction}

"""
        else:
            instruction_prompt = ""

        header = f"""\
I am a developer working on a task in my codebase.
{instruction_prompt}
Here is the change I made so far:

```
{input_diff}
```

I would like to continue on my task and edit the file `{sys_input.path}`.
Here is selected code I would like to edit:

```
{current_code}
```
"""

        edit_prompt = f"""\
{header}

Please suggest how to edit the selected code, to continue the task.
Use exactly the following format:

Task description: ... describe the task I am doing ...

Modified code:
```
... modified code ...
```

Change description: ... briefly describe the change ...
"""

        dialog = []

        max_tokens = self.generation_options.max_generated_tokens or 2048

        def get_response(prompt, add_to_dialog: bool):
            print("")
            print("=" * 120)
            print(f"Prompt: ({len(prompt)} chars)")
            print(prompt)

            if add_to_dialog:
                dialog.append(prompt)
            response = self.chat_client.generate(
                messages=dialog,
                system_prompt=system_prompt,
                max_tokens=max_tokens,
            )
            if add_to_dialog:
                dialog.append(response)

            print("Response:")
            print(response)

            return response

        edit_response = get_response(edit_prompt, add_to_dialog=True)

        match = re.search(
            r"Modified code:\n```\w*\n(.*\n)```\n\nChange description: (.*)",
            edit_response,
            re.DOTALL | re.MULTILINE,
        )
        if match:
            modified_code = match.group(1)
            description = match.group(2)

            # print("\nModified code:")
            # print(modified_code)
            print("\nDescription:")
            print(description)
            # return modified_code
            return modified_code, description
        else:
            print("No match")
            return None, None

        # response = re.sub(r"\s*#\s*\[tango-leema-zebra:.*\]", "", response)
        # print(response)

    def _generate_edit_llama(self, sys_input: EditGenSystemInput) -> Optional[str]:
        input_diff = format_file_changes(
            changes=sys_input.recent_changes,
            diff_context_lines=5,
        )

        # current_code = sys_input.prefix + sys_input.selected_code + sys_input.suffix

        system_prompt = """\
You are a helpful software engineering assistant, that can suggest the next edit
in a code repository given previous edits that have been performed so far.
The code you generated should be final. It should be the developer that the user will
want to check into their codebase.
"""

        if sys_input.instruction:
            instruction_prompt = f"""\

This is my task:

{sys_input.instruction}

"""
        else:
            instruction_prompt = ""

        #         header = f"""\
        # I am a developer working on a task in my codebase.
        # {instruction_prompt}
        # Here is the change I made so far:

        # ```
        # {input_diff}
        # ```

        # I would like to continue on my task and edit the file `{sys_input.path}`.
        # Here is the contents of the file, with the selected code omitted:

        # ```
        # {sys_input.prefix}
        # [selected code]
        # {sys_input.suffix}
        # ```

        # Here is the selected code, which should be edited:
        # ```
        # {sys_input.selected_code}
        # ```
        # """

        header = f"""\
I am a developer working on a task in my codebase.
{instruction_prompt}
Here is the change I made so far:

```
{input_diff}
```

I would like to continue on my task and edit the file `{sys_input.path}`.
Here is selected code I would like to edit:

```
{sys_input.selected_code}
```
"""

        #         needs_edit_prompt = f"""\
        # {header}

        # Please determine whether or not this code needs to be edited.
        # Use exactly the following format:

        # Task description: ... describe the task I am doing ...

        # Should the code be edited? Yes or No
        # """

        edit_prompt = f"""\
{header}

Please suggest how to edit the selected code, to continue the task.
Use exactly the following format:

Task description: ... describe the task I am doing ...

Modified code:
```
... modified code ...
```

Description: ... briefly describe the change ...
"""

        apply_prompt = "Great, now rewrite the selected code to apply these changes."

        dialog = []

        max_tokens = self.generation_options.max_generated_tokens or 2048

        def get_response(prompt, add_to_dialog: bool):
            print("")
            print("=" * 120)
            print(f"Prompt: ({len(prompt)} chars)")
            print(prompt)

            if add_to_dialog:
                dialog.append(prompt)
            response = self.chat_client.generate(
                messages=dialog,
                system_prompt=system_prompt,
                max_tokens=max_tokens,
            )
            if add_to_dialog:
                dialog.append(response)

            print("Response:")
            print(response)

            return response

        # TODO(guy) this was a failed attempt at filtering responses
        # needs_edit_response = get_response(needs_edit_prompt, add_to_dialog=False)
        # if not needs_edit_response.endswith("Yes"):
        #     print("Model decided that no change is needed")
        #     return None
        # print("Model decided that a change is needed, proceeding.")

        edit_response = get_response(edit_prompt, add_to_dialog=True)

        match = re.search(
            r"Modified code:\n```\w*\n(.*\n)```\n\nDescription: (.*)",
            edit_response,
            re.DOTALL | re.MULTILINE,
        )
        if match:
            # modified_code = match.group(1)
            description = match.group(2)

            # print("\nModified code:")
            # print(modified_code)
            print("\nDescription:")
            print(description)
            # return modified_code
        else:
            print("No match")
            return None

        # Now ask the model to actually make the change
        apply_response = get_response(apply_prompt, add_to_dialog=True)

        match = re.search(
            r"```\w*\n(.*\n)```",
            apply_response,
            re.DOTALL | re.MULTILINE,
        )

        if match:
            modified_code = match.group(1)
            print("\nModified code:")
            print(modified_code)
            return modified_code
        else:
            print("No match (second turn)")
            return None

        # response = re.sub(r"\s*#\s*\[tango-leema-zebra:.*\]", "", response)
        # print(response)

    def _generate_edit(
        self, sys_input: EditGenSystemInput
    ) -> Tuple[Optional[str], Optional[str]]:
        if self.prompt_type == "llama":
            return self._generate_edit_llama(sys_input), ""
        elif self.prompt_type == "sonnet":
            return self._generate_edit_sonnet(sys_input)
        else:
            raise ValueError(f"Unknown prompt type: {self.prompt_type}")

    def generate(
        self,
        model_input: EditGenSystemInput,
        should_cancel: Callable[[], bool] = lambda: False,
    ) -> EditGenSystemOutput:
        if should_cancel():
            raise GenerationCanceledError()

        print("Selected text:")
        print(model_input.selected_code)

        replacement, change_description = self._generate_edit(model_input)
        truncation_char = None  # TODO: replace with actual value

        print("Replacement:")
        print(replacement)

        if not replacement:
            return EditGenSystemOutput(
                replacement="",
                changed=False,
                truncation_char=truncation_char,
                prob_changed=0,
                change_confidence=0,
                change_description="",
                debug_info={},
            )

        assert change_description is not None

        if should_cancel():
            raise GenerationCanceledError()

        changed = True
        confidence = 1 if changed else 0

        output = EditGenSystemOutput(
            replacement,
            changed=changed,
            truncation_char=truncation_char,
            prob_changed=confidence,
            change_confidence=confidence,
            change_description=change_description,
            debug_info={},
        )
        return output

    def add_docs(self, src_files: Iterable[Document]):
        self.docs.extend(src_files)

    def load(self):
        pass

    def unload(self):
        pass

    def clear_retriever(self):
        self.docs = []

    def get_doc_ids(self):
        return set(doc.id for doc in self.docs)

    def get_documents(self, doc_ids) -> list[Document]:
        """Return all documents with the given ids indexed by the retriever."""
        return [doc for doc in self.docs if doc.id in doc_ids]

    def remove_docs(self, doc_ids: Iterable[DocumentId]):
        self.docs = [doc for doc in self.docs if doc.id not in doc_ids]

    def get_model(self) -> GenerativeLanguageModel:
        return NullModel()

"""Elden system combining dense and signature retrieval."""

import logging
import typing
from dataclasses import dataclass

from research.core.model_input import ModelInput
from research.core.types import Document, DocumentId
from research.eval.harness import factories
from research.eval.harness.systems.abs_system import register_system
from research.eval.harness.systems.basic_RAG_system import (
    MiscRAGSystemConfig,
    RAGSystem,
)
from research.models import GenerationOptions, GenerativeLanguageModel
from research.models.meta_model import FimGenMode
from research.retrieval.types import DocumentIndex

logger = logging.getLogger(__name__)


@dataclass
class MiscEldenSystemConfig(MiscRAGSystemConfig):
    """Miscellaneous config options for Elden system."""

    signature_retriever_top_k: int = 32


@register_system("elden")
class EldenSystem(RAGSystem):
    """LM with line and signature dense retrievers."""

    def __init__(  # pylint: disable=super-init-not-called
        self,
        model: GenerativeLanguageModel,
        dense_retriever: DocumentIndex,
        signature_retriever: DocumentIndex,
        generation_options: GenerationOptions,
        experimental_config: MiscEldenSystemConfig,
        verbose: bool = False,
    ):
        self.model = model
        self.dense_retriever = dense_retriever
        self.signature_retriever = signature_retriever
        self.generation_options = generation_options
        self.experimental_config = experimental_config
        self.verbose = verbose
        self.__loaded = False

    def load(self):
        if not self.__loaded:
            self.model.load()
            self.dense_retriever.load()
            self.signature_retriever.load()
        self.__loaded = True

    def unload(self):
        if self.__loaded:
            self.model.unload()
            self.dense_retriever.unload()
            self.signature_retriever.unload()
            self.__loaded = False

    def add_docs(self, src_files: typing.Collection[Document]):
        """Ingest a copy of the source code repository.

        Args:
          src_files: list of Documents to add
        """
        logger.info(f"Adding {len(list(src_files))} documents.")
        self.dense_retriever.add_docs(src_files)  # type: ignore
        total_docs = len(self.dense_retriever.get_doc_ids())
        logger.info(f"There are now {total_docs} total docs in dense retriever.")

        self.signature_retriever.add_docs(src_files)  # type: ignore
        total_docs = len(self.signature_retriever.get_doc_ids())
        logger.info(f"There are now {total_docs} total docs in signature retriever.")

    def remove_docs(self, doc_ids: typing.Collection[DocumentId]):
        """Remove documents from the retrievers."""
        logger.info(f"Removing {len(list(doc_ids))} documents.")
        self.dense_retriever.remove_docs(doc_ids)
        total_docs = len(self.dense_retriever.get_doc_ids())
        logger.info(f"There are now {total_docs} total docs in dense retriever.")

        self.signature_retriever.remove_docs(doc_ids)
        total_docs = len(self.signature_retriever.get_doc_ids())
        logger.info(f"There are now {total_docs} total docs in signature retriever.")

    def get_doc_ids(self) -> typing.AbstractSet[DocumentId]:
        """Get the set of indexed documents ids for the dense retriever."""
        return self.dense_retriever.get_doc_ids()

    def clear_retriever(self):
        """Clear any stored documents from the retriever."""
        self.dense_retriever.remove_all_docs()
        self.signature_retriever.remove_all_docs()

    @dataclass
    class _EldenSystemConfig:
        """Schema for configuring a System."""

        model: dict
        dense_retriever: dict
        signature_retriever: dict
        generation_options: dict
        experimental: dict
        fim_gen_mode: str = "evaluation"
        verbose: bool = False

    @classmethod
    def from_yaml_config(cls, config: dict) -> "EldenSystem":
        """Returns a System object constructed using a config dictionary."""
        # prompt configuration happens as part of creating the model
        xconfig = cls._EldenSystemConfig(**config)
        model = factories.create_model(xconfig.model)
        dense_retriever = factories.create_retriever(xconfig.dense_retriever)
        signature_retriever = factories.create_retriever(xconfig.signature_retriever)
        generation_options = GenerationOptions(**xconfig.generation_options)
        fim_gen_mode = FimGenMode[
            xconfig.experimental.pop("fim_gen_mode", "evaluation")
        ]
        experimental_config = MiscEldenSystemConfig(
            fim_gen_mode=fim_gen_mode, **xconfig.experimental
        )

        return EldenSystem(
            model=model,
            dense_retriever=dense_retriever,
            signature_retriever=signature_retriever,
            generation_options=generation_options,
            experimental_config=experimental_config,
            verbose=xconfig.verbose,
        )

    def _retrieve(
        self,
        model_input: ModelInput,
    ) -> ModelInput:
        if model_input.doc_ids is None:
            logger.warning("No doc_ids provided, using all doc_ids.")

        model_input.retrieved_chunks, _ = self.dense_retriever.query(
            model_input,
            doc_ids=model_input.doc_ids,
            top_k=self.experimental_config.retriever_top_k,
        )
        model_input.extra["signature_chunks"], _ = self.signature_retriever.query(
            model_input,
            doc_ids=model_input.doc_ids,
            top_k=self.experimental_config.signature_retriever_top_k,
        )
        return model_input

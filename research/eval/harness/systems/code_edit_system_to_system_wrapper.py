"""This wraps a subclass of AbstractCodeEditSystem to turn it into a subclass of AbstractSystem."""

import typing

from research.core.edit_prompt_input import (
    ResearchEditPromptInput,
    convert_model_input_to_research_edit_prompt_input,
)
from research.core.model_input import ModelInput
from research.core.types import Document, DocumentId
from research.eval.harness import factories
from research.eval.harness.systems.abs_system import (
    CodeCompleteSystem,
    CodeInstructSystem,
    CompletionResult,
    register_system,
)
from research.models.all_models import GenerativeLanguageModel


@register_system("wrapped_code_edit_system")
class WrappedCodeEditSystem(CodeCompleteSystem):
    """This wraps a AbstractCodeEditSystem to turn it into a AbstractSystem."""

    def __init__(
        self,
        code_edit_system: CodeInstructSystem,
    ):
        self.code_edit_system = code_edit_system

    def load(self):
        """Load the system."""
        self.code_edit_system.load()

    def unload(self):
        """Unload the system."""
        self.code_edit_system.unload()

    def generate(self, model_input: ModelInput) -> CompletionResult:
        """Returns a tuple of (completion, prompt tokens, retrieved chunks).

        The prompt and retrieved-chunks are returned for analysis or logging.
        """
        code_edit_input = convert_model_input_to_research_edit_prompt_input(model_input)
        return self.code_edit_system.generate(code_edit_input)

    def add_docs(self, src_files: typing.Collection[Document]):
        """Ingest a copy of the source code repository."""
        self.code_edit_system.add_docs(src_files)

    def remove_docs(self, doc_ids: typing.Collection[DocumentId]):
        """Remove documents from the retriever."""
        self.code_edit_system.remove_docs(doc_ids)

    def clear_retriever(self):
        """Clear any documents from the retriever.

        This is useful for clearing out the current repo from the retriever.
        """
        self.code_edit_system.clear_retriever()

    def log_likelihood_continuation(
        self, model_input: ModelInput, continuation: str
    ) -> float | None:
        """Returns the conditional log likelihood of the continuation, conditioned on the model input.

        Returns None for the undefined case of an empty continuation.
        """
        code_edit_input = convert_model_input_to_research_edit_prompt_input(model_input)
        return self.code_edit_system.log_likelihood_continuation(
            code_edit_input, continuation
        )

    def get_model(self) -> GenerativeLanguageModel:
        return self.code_edit_system.get_model()

    @classmethod
    def from_yaml_config(cls, config: dict):
        """Create a wrapped code edit system."""

        code_edit_system = factories.create_system(config)

        # This line asserts that the loaded system has the input type ResearchEditPromptInput
        assert (
            typing.get_args(code_edit_system.__class__.__orig_bases__[0])[0]  # type: ignore
            is ResearchEditPromptInput
        )

        return WrappedCodeEditSystem(code_edit_system)

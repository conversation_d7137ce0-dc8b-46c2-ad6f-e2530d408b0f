"""A system for generating next edits."""

from __future__ import annotations

import copy
import dataclasses
import math
import time
from dataclasses import dataclass
from typing import (
    AbstractSet,
    Callable,
    Collection,
    FrozenSet,
    Iterable,
    Literal,
    Sequence,
)

import torch
from cachetools import LRUCache

from base.diff_utils.diff_utils import compute_file_diff
from base.prompt_format_next_edit.gen_prompt_formatter import EditGenFormatterConfig
from base.prompt_format_next_edit.retrieval_prompt_formatter import (
    EditGenRetrievalPromptInput,
)
from base.prompt_format_retrieve.prompt_formatter import InstructRetrieverPromptInput
from base.ranges.range_types import Char<PERSON>ange
from base.ranges.string_utils import shorten_str
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from base.tokenizers.tiktoken_starcoder_tokenizer import StarCoderTokenizer
from research.core.changes import Changed
from research.core.diff_utils import File
from research.core.types import Chunk, Document, DocumentId, NTuple
from research.core.utils import FileLogger
from research.eval.harness.systems.abs_system import AbstractSystem, register_system
from research.llm_apis.chat_utils import ChatClient
from research.models.fastforward_llama_models import (
    FastForwardQwen25CoderModel,
    LLAMA_FastForwardModel,
)
from research.models.fastforward_models import (
    FastForwardModel,
    GenerationCanceledError,
    StarCoder2_FastForward,
    StarCoder_FastForward,
)
from research.models.meta_model import GenerationOptions
from research.next_edits.edit_gen_formatters import (
    EditGenPromptFormatter,
    EditGenPromptInput,
    fix_truncation_newline,
)
from research.next_edits.edit_gen_sampler import EditGenOutput
from research.retrieval.retrieval_database import RetrievalDatabase
from research.retrieval.scorers.dense_scorer_v2 import DenseRetrievalScorerV2
from research.retrieval.types import DocumentIndex


# Note: we make the class below fully immutable in order to safely cache the system
# result by input.
@dataclass(frozen=True)
class EditGenSystemInput:
    """The input to the next edit generation system."""

    path: str
    """Path of the current file."""

    prefix: str
    """The content before the selection."""

    selected_code: str
    """The content that the user selected."""

    suffix: str
    """The content after the selection."""

    instruction: str
    """The user's instruction about how to make the edits."""

    recent_changes: NTuple[Changed[File]]
    """The changes made to the files since the last commit.

    This can either be a sequence of Changed[File] objects, or a diff str holding the
    output of `git diff -U<N>` where <N> is the number of context lines that the
    model was trained on. (See `diff_context_lines` in `EditGenPromptFormatter`.)
    """

    generate_description: bool
    """If true, generate a natural language description of the change.

    This is only used when the model generates an output, as otherwise there's
    no point in generating a description.
    """

    doc_ids: FrozenSet[DocumentId] | None = None
    """The set of document ids that are available for retrieval.
    None means all documents in the index should be used.
    """

    must_change: bool = False
    """If true, this indicates that the model must modify the selected code.

    Leaving this to False will let the model decide whether there should be a
    change to the selected code. Note that even if this is true, the changes are not
    guaranteed since then model may still output code that contains no change.
    """

    stop_on_pause: bool = True
    """If true, stop generation when the pause token is generated."""

    def summary(self, max_str_len: int = 200) -> str:
        """Return a shortened summary of the input fields."""

        def shorten(
            s: str, omit_mode: Literal["middle", "right", "left"] = "middle"
        ) -> str:
            return repr(shorten_str(s, max_str_len, omit_mode))

        file_change_str = shorten(str(self.recent_changes), "right")
        lines = [
            f"path={shorten(str(self.path))}, ",
            f"instruction={shorten(self.instruction)}, ",
            f"must_change={self.must_change}, ",
            f"prefix={shorten(self.prefix, 'right')}, ",
            f"selected_code={shorten(self.selected_code, 'middle')}, ",
            f"suffix={shorten(self.suffix, 'left')}, ",
            f"recent_file_changes={file_change_str}",
        ]
        return "\n".join(lines)


@dataclass
class EditGenSystemOutput(EditGenOutput):
    """The output of the next edit generation system."""

    replacement: str
    """The suggested replacement text."""

    changed: bool
    """True if the model suggested a change, False otherwise."""

    truncation_char: int | None
    """The character offset in `replacement` where the change was truncated.

    None means that the change was not truncated.
    """

    prob_changed: float | None
    """The probability of the model suggesting a change.

    This is None if the input has specified `must_change=True`.
    """

    change_confidence: float
    """The probability of the predicted diff conditioned on having a change.

    When `prob_changed` is not None, multiplying this with `prob_changed` gives the
    joint probability of the full model output.
    """

    change_description: str
    """A natural language description of the change."""

    debug_info: dict[str, str] = dataclasses.field(default_factory=dict)
    """Extra key-value pairs stored for debugging."""

    def log_to_file(self, file_logger: FileLogger):
        """Log the output via a FileLogger."""
        if self.prob_changed is not None:
            prob_text = f"({self.prob_changed=:.1%}, {self.change_confidence=:.1%})\n"
        else:
            prob_text = f"(forced change: {self.change_confidence=:.1%})\n"
        file_logger.log("change_confidence.txt", prob_text)
        file_logger.log("replacement.txt", self.replacement)
        file_logger.log("change_description.txt", self.change_description)
        for k, v in self.debug_info.items():
            if k == "output_tokens":
                v = prob_text + v
            # TODO: find a proper way to handle name collision
            file_logger.log(f"{k}.txt", v)


@dataclass
class SuggestInstructionInput:
    instruction_prefix: str
    """The prefix of the instruction."""

    recent_changes: Sequence[Changed[File]]
    """The changes made to the files since the last commit."""


@dataclass
class SuggestInstructionOutput:
    instruction: str
    """The instruction suggested by the model."""

    debug_info: dict[str, str] = dataclasses.field(default_factory=dict)
    """Extra key-value pairs stored for debugging."""

    def log_to_file(self, file_logger: FileLogger):
        """Log the output via a FileLogger."""
        file_logger.log("instruction.txt", self.instruction)
        for k, v in self.debug_info.items():
            file_logger.log(f"{k}.txt", v)


@register_system("next_edit_gen")
@dataclass
class NextEditGenSystem(AbstractSystem[EditGenSystemInput, EditGenSystemOutput]):
    """A suggested edit generation system that can suggest next edits."""

    model: FastForwardModel | LLAMA_FastForwardModel
    generation_options: GenerationOptions
    retriever: RetrievalDatabase | None
    prompt_formatter: EditGenPromptFormatter
    chat_client: ChatClient | None
    """This is currently used only to generate diff descriptions (when applicable)."""
    max_retrieved_chunks: int = 30
    use_gold_output_to_retrieve_chunks: bool = False
    """If true, use the gold output to retrieved chunks."""

    def __post_init__(self):
        self._loaded = False
        self._generate_cache: LRUCache[EditGenSystemInput, EditGenSystemOutput] = (
            LRUCache(maxsize=500)
        )
        """Cache system output by input."""
        self._model_cache: LRUCache[FrozenRawModelInput, _CachedModelOutput] = LRUCache(
            maxsize=500
        )
        """Cache raw model output by raw input."""
        self._description_gen_cache: LRUCache[str, _ChangeDescriptionOutput] = LRUCache(
            maxsize=500
        )
        """Cache change description generation output by diff."""
        # We rely on some fields of DenseRetrievalScorerV2, so assert it here.
        assert not self.retriever or isinstance(
            self.retriever.scorer, DenseRetrievalScorerV2
        )
        retriever = self.retriever
        existing_docs = retriever.get_docs(retriever.get_doc_ids()) if retriever else []
        self._seen_docs: dict[DocumentId, Document] = {
            doc.id: doc for doc in existing_docs
        }

    def generate(
        self,
        model_input: EditGenSystemInput,
        should_cancel: Callable[[], bool] = lambda: False,
        expected_output: str | None = None,
    ) -> EditGenSystemOutput:
        sys_input = model_input
        if sys_input in self._generate_cache:
            return self._generate_cache[sys_input]
        if should_cancel():
            raise GenerationCanceledError()

        current_code = sys_input.prefix + sys_input.selected_code + sys_input.suffix
        selected_range = CharRange(
            len(sys_input.prefix),
            len(sys_input.prefix) + len(sys_input.selected_code),
        )
        doc_ids = sys_input.doc_ids
        assert doc_ids is not None

        ret_chunks, debug_info = self.retrieve_chunks(
            current_code,
            selected_range,
            sys_input.path,
            sys_input.recent_changes,
            doc_ids=doc_ids,
            expected_output=expected_output,
            instruction=sys_input.instruction,
        )

        if should_cancel():
            raise GenerationCanceledError()

        prompt_input = EditGenPromptInput(
            current_file=File(sys_input.path, current_code),
            edit_region=selected_range,
            instruction=sys_input.instruction,
            recent_changes=sys_input.recent_changes,
            retrieval_chunks=[c.to_prompt_chunk() for c in ret_chunks],
        )
        prompt_tokens = self.prompt_formatter.format_input_prompt(prompt_input).tokens
        tkn = self.prompt_formatter.tokenizer
        has_change_id = self.prompt_formatter.special_tokens.has_change
        pause_id = self.prompt_formatter.special_tokens.pause
        eos_id = self.prompt_formatter.special_tokens.eos
        no_change_id = self.prompt_formatter.special_tokens.no_change

        if sys_input.must_change:
            # we add the has_change token to the prompt to force a change
            prompt_tokens.append(has_change_id)

        stop_tokens = {eos_id, no_change_id}
        if sys_input.stop_on_pause:
            stop_tokens.add(pause_id)

        raw_input = FrozenRawModelInput(
            tuple(prompt_tokens),
            max_generated_tokens=self.generation_options.max_generated_tokens,
            stop_tokens=frozenset(stop_tokens),
        )
        cached_output = self._model_cache.get(raw_input)
        if cached_output is None:
            gen_options = copy.copy(self.generation_options)
            gen_options.stop_tokens = stop_tokens
            raw_output = self.model.raw_generate_tokens(
                prompt_tokens, gen_options, should_cancel=should_cancel
            )
            output_tokens = raw_output.tokens
            token_probs = raw_output.token_probs()
            if sys_input.must_change:
                prob_changed = None
                change_confidence = math.prod(token_probs)
            else:
                first_tk_probs = torch.softmax(raw_output.logits[0], dim=-1)
                prob_changed = float(first_tk_probs[has_change_id].item())
                change_confidence = math.prod(token_probs[1:])
            cached_output = _CachedModelOutput(
                output_tokens=tuple(output_tokens),
                prob_changed=prob_changed,
                change_confidence=change_confidence,
            )
            self._model_cache[raw_input] = cached_output
        else:
            output_tokens = list(cached_output.output_tokens)
            prob_changed = cached_output.prob_changed
            change_confidence = cached_output.change_confidence

        if sys_input.must_change:
            # we prepend the has_change token to output to pretend it was generated
            output_tokens = [has_change_id] + output_tokens

        # now parse the output
        decoded = self.prompt_formatter.decode_output_tokens(
            output_tokens, sys_input.selected_code
        )
        decoded = fix_truncation_newline(decoded)

        recent_changes_summary = "\n".join(
            str(c.map(lambda x: x.path)) for c in sys_input.recent_changes
        )

        debug_info = {
            "prompt_tokens": tkn.detokenize(prompt_tokens),
            "output_tokens": tkn.detokenize(output_tokens),
            "recent_changes_summary": recent_changes_summary,
        }

        change_description = ""
        if sys_input.generate_description and decoded.changed and self.chat_client:
            start_t = time.time()
            description_output = self._generate_change_description(
                sys_input, decoded.replacement
            )
            time_taken = time.time() - start_t
            debug_info["change_description_time_taken"] = f"{time_taken:.2f}s"
            debug_info["change_description_prompt"] = description_output.full_prompt
            change_description = description_output.description

        output = EditGenSystemOutput(
            decoded.replacement,
            changed=decoded.changed,
            truncation_char=decoded.truncation_char,
            prob_changed=prob_changed,
            change_confidence=cached_output.change_confidence,
            change_description=change_description,
            debug_info={
                "prompt_tokens": tkn.detokenize(prompt_tokens),
                "output_tokens": tkn.detokenize(output_tokens),
                "recent_changes_summary": recent_changes_summary,
                **debug_info,
            },
        )
        self._generate_cache[sys_input] = output
        return output

    def _generate_change_description(
        self, sys_input: EditGenSystemInput, replacement: str
    ) -> _ChangeDescriptionOutput:
        """Generate a natural language description of the change."""
        if not self.chat_client:
            raise ValueError("chat_client is not set")
        current_code = sys_input.prefix + sys_input.selected_code + sys_input.suffix
        new_code = sys_input.prefix + replacement + sys_input.suffix
        diff = compute_file_diff(
            before_file=File(path=sys_input.path, contents=current_code),
            after_file=File(path=sys_input.path, contents=new_code),
            use_smart_header=True,
            num_context_lines=50,
        )
        if cached_output := self._description_gen_cache.get(diff):
            return cached_output

        example_diff = compute_file_diff(
            before_file=File(
                path="example.py",
                contents="""\
synth_instruct_path = None
        if not repo_list_path.exists():
            save_bare_repo_list_disk(repo_list_path, max_repos=200_000)
        data_source = FromGitRepos(repo_list_path)
""",
            ),
            after_file=File(
                path="example.py",
                contents="""\
synth_instruct_path = None
        if not repo_list_path.exists():
            save_bare_repo_list_disk(repo_list_path, max_repos=300_000)
        data_source = FromGitRepos(repo_list_path)
""",
            ),
            use_smart_header=True,
            num_context_lines=50,
        )

        prompt = f"""\
Write a short description or summary of the diff below using imperative verb form.
Write only a few words if it's a single line change, and write a short sentence if
there are multiple line changes. Assume the reader sees the change as well, so you
can skip any inessential details. e.g., if the change is
```
{example_diff}
```
just say "update max_repos to 300_000".

Now do the same for the diff below. Directly start the response with the
description and say nothing else.
```
{diff}
```
"""
        response = self.chat_client.generate(
            messages=[prompt],
            temperature=0,
            max_tokens=60,
        )
        output = _ChangeDescriptionOutput(
            description=response,
            full_prompt=prompt,
        )
        self._description_gen_cache[diff] = output
        return output

    def suggest_instruction(
        self,
        instruction_input: SuggestInstructionInput,
        max_generated_tokens: int = 120,
    ) -> SuggestInstructionOutput:
        """Suggest an instruction based on diffs."""
        tkn = self.prompt_formatter.tokenizer
        instruction_prefix = instruction_input.instruction_prefix
        if instruction_prefix.strip() == "":
            # we need a nonempty instruction prefix for the pause token to be inserted
            instruction_prefix = "PR summary below:\n"
        prompt_input = EditGenPromptInput(
            current_file=File("", ""),
            edit_region=CharRange(0, 0),
            instruction=instruction_prefix,
            recent_changes=instruction_input.recent_changes,
            retrieval_chunks=(),
        )
        prompt_tokens = self.prompt_formatter.format_input_prompt(prompt_input).tokens
        pause_id = self.prompt_formatter.special_tokens.pause
        eod_id = self.prompt_formatter.special_tokens.eos
        assert pause_id in prompt_tokens
        # remove everything after the pause token and let the model generate from there
        prompt_tokens = prompt_tokens[: prompt_tokens.index(pause_id)]
        if isinstance(self.model, FastForwardModel):
            self.model.stop_tokens = {eod_id, pause_id}
        gen_options = copy.copy(self.generation_options)
        gen_options.max_generated_tokens = max_generated_tokens
        raw_output = self.model.raw_generate_tokens(prompt_tokens, gen_options)
        output_tokens = raw_output.tokens
        if output_tokens and output_tokens[-1] == pause_id:
            output_tokens = output_tokens[:-1]
        instruction = instruction_prefix + tkn.detokenize(output_tokens)
        debug_info = {
            "prompt_tokens": tkn.detokenize(prompt_tokens),
            "output_tokens": tkn.detokenize(output_tokens),
        }
        return SuggestInstructionOutput(instruction, debug_info=debug_info)

    def retrieve_chunks(
        self,
        current_code: str,
        selected_range: CharRange,
        current_path: str,
        recent_changes: Sequence[Changed[File]],
        doc_ids: Collection[DocumentId],
        instruction: str,
        expected_output: str | None = None,
    ) -> tuple[list[Chunk], dict]:
        if self.retriever is None:
            return [], {}
        if expected_output is not None and self.use_gold_output_to_retrieve_chunks:
            selected_code = expected_output
        else:
            selected_code = current_code[selected_range.start : selected_range.stop]

        assert isinstance(self.retriever.scorer, DenseRetrievalScorerV2)
        query_formatter = self.retriever.scorer.query_formatter
        if query_formatter.input_type == InstructRetrieverPromptInput:
            query = InstructRetrieverPromptInput(
                prefix=current_code[: selected_range.start],
                suffix=current_code[selected_range.stop :],
                path=current_path,
                instruction=instruction,
                selected_code=selected_code,
            )
        elif query_formatter.input_type == EditGenRetrievalPromptInput:
            query = EditGenRetrievalPromptInput(
                current_file=File(current_path, current_code),
                edit_region=selected_range,
                instruction=instruction,
                recent_changes=recent_changes,
            )
        else:
            raise ValueError(
                f"Unknown query formatter type: {self.retriever.scorer.query_formatter.input_type}"
            )

        # model_input.doc_ids = doc_ids  # to be extra safe
        chunks, scores = self.retriever.query(
            query, doc_ids=doc_ids, top_k=self.max_retrieved_chunks
        )
        debug_info = {
            "query_tokens": query_formatter.tokenizer.detokenize(
                query_formatter.format_prompt(query).tokens()
            ),
            "retrieved_chunks": "\n".join(
                f"{c.path}: ({s:.2f})\n{c.text} " for c, s in zip(chunks, scores)
            ),
        }
        return chunks, debug_info

    def add_docs(self, src_files: Collection[Document]):
        for f in src_files:
            self._seen_docs[f.id] = f
        if self.retriever is None:
            return
        indexed_ids = self.retriever.get_doc_ids()
        files = [f for f in src_files if f.id not in indexed_ids]
        self.retriever.add_docs(files)

    def remove_docs(self, doc_ids: Collection[DocumentId]):
        for doc_id in doc_ids:
            self._seen_docs.pop(doc_id, None)
        if self.retriever is None:
            return
        self.retriever.remove_docs(doc_ids)

    def load(self):
        if self._loaded:
            return
        self.model.load()
        if self.retriever:
            self.retriever.load()
        self._loaded = True

    def unload(self):
        if not self._loaded:
            return
        self.model.unload()
        if self.retriever:
            self.retriever.unload()
        self._loaded = False

    def clear_retriever(self):
        self._seen_docs.clear()
        if self.retriever:
            self.retriever.remove_all_docs()

    def get_doc_ids(self) -> AbstractSet[DocumentId]:
        return self._seen_docs.keys()

    def get_documents(self, doc_ids: Iterable[DocumentId]) -> list[Document]:
        """Return all documents with the given ids indexed by the retriever."""
        return [doc for doc_id in doc_ids if (doc := self._seen_docs.get(doc_id))]

    def get_model(self) -> FastForwardModel | LLAMA_FastForwardModel:
        return self.model

    @classmethod
    def from_yaml_config(cls, config: dict) -> NextEditGenSystem:
        """Returns a System object constructed using a config dictionary."""
        from research.eval.harness import factories

        model = factories.create_model(config["model"])
        assert isinstance(model, FastForwardModel)

        generation_options = GenerationOptions(**config["generation_options"])
        retriever = None
        if "retriever" in config:
            retriever = factories.create_retriever(config["retriever"])
            assert isinstance(retriever, RetrievalDatabase)
        if isinstance(model, StarCoder_FastForward):
            tokenizer = StarCoderTokenizer()
        elif isinstance(model, StarCoder2_FastForward):
            tokenizer = StarCoder2Tokenizer()
        elif isinstance(model, FastForwardQwen25CoderModel):
            tokenizer = Qwen25CoderTokenizer()
        else:
            raise NotImplementedError(
                f"Model {model} is not supported for next edit generation."
            )
        prompt_formatter = EditGenPromptFormatter(
            tokenizer, config=EditGenFormatterConfig(**config["prompt_formatter"])
        )

        return NextEditGenSystem(
            model=model,
            generation_options=generation_options,
            retriever=retriever,
            prompt_formatter=prompt_formatter,
            chat_client=None,
            use_gold_output_to_retrieve_chunks=config.get(
                "use_gold_output_to_retrieve_chunks", False
            ),
        )


@dataclass
class _ChangeDescriptionOutput:
    """The output of the change description generation."""

    description: str
    """The generated description."""

    full_prompt: str
    """The full prompt fed to the chat model."""


@dataclass(frozen=True)
class FrozenRawModelInput:
    """A fully immutable raw model input suitable for caching."""

    full_prompt: NTuple[int]
    """The full prompt tokens."""

    max_generated_tokens: int | None
    """The maximum number of tokens to generate."""

    stop_tokens: FrozenSet[int]
    """The stop tokens to use."""


@dataclass(frozen=True)
class _CachedModelOutput:
    output_tokens: NTuple[int]
    prob_changed: float | None
    change_confidence: float

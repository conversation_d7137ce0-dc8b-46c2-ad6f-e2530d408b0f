"""A collection of functions for interacting with recency information."""

from base.prompt_format.recency_info import ReplacementText
from base.prompt_format.common import PromptChunk


def recent_chunk_to_prod_retrieval_chunk(
    recent_change: ReplacementText,
) -> PromptChunk:
    """Converts a recent chunk to a retrieval chunk."""
    return PromptChunk(
        text=recent_change.replacement_text,
        path=recent_change.path,
        unique_id=None,
        origin="recency_retriever",
        char_start=recent_change.char_start,
        char_end=recent_change.char_end,
        blob_name=recent_change.blob_name,
    )

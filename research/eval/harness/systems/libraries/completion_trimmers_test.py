"""Tests for research/eval/harness/systems/libraries/completion_trimmers.

pytest research/eval/harness/systems/libraries/completion_trimmers_test.py
"""

import pytest

from base.tokenizers.tokenizer import Tokenizer
from research.core.tokenizers import get_tokenizer
from research.eval.harness.systems.libraries.completion_trimmers import (
    trim_on_dedent,
    trim_on_max_lines,
    trim_on_token_count,
)


@pytest.fixture
def tokenizer() -> Tokenizer:
    """Create a tokenizer."""
    return get_tokenizer("CodeGenTokenizer")


@pytest.mark.parametrize(
    "completion,max_generated_tokens,expected",
    [
        ('print("Hello World!")', 10, 'print("Hello World!")'),
        ('print("Hello World!")', 3, 'print("Hello'),
        ('print("Hello World!")', 0, ""),
    ],
)
def test_trim_on_token_count(
    completion: str,
    max_generated_tokens: int,
    expected: str,
    tokenizer: Tokenizer,
):
    """Test trimming on token count."""
    assert trim_on_token_count(completion, tokenizer, max_generated_tokens) == expected


@pytest.mark.parametrize(
    "completion,max_lines,expected",
    [
        ("test\n" * 5, 10, "test\n" * 5),
        ("test\n" * 5, 2, "test\n" * 2),
        ("test\r\n" * 5, 10, "test\r\n" * 5),
        ("test\r\n" * 5, 2, "test\r\n" * 2),
        ("    test\n" * 5, 10, "    test\n" * 5),
        ("    test\n" * 5, 2, "    test\n" * 2),
        ("\n" * 5, 10, "\n" * 5),
        ("\n" * 5, 2, "\n" * 2),
        ("\r\n\r" * 5, 10, "\r\n\r" * 5),
        ("\r\n\r" * 5, 3, "\r\n\r" + "\r\n"),
    ],
)
def test_trim_on_max_lines(completion, max_lines, expected):
    """Test trimming on max lines."""
    assert trim_on_max_lines(completion, max_lines) == expected


@pytest.mark.parametrize(
    "completion,expected",
    [
        ("", ""),
        ("foo", "foo"),
        ("  foo", "  foo"),
        ("  foo\nbar", "  foo\n"),
        ("  foo bar\n  baz", "  foo bar\n  baz"),
        ("  foo\n    bar\n  baz", "  foo\n    bar\n  baz"),
        ("  foo\n    bar\nbaz", "  foo\n    bar\n"),
        ("  foo\n\n\n  bar", "  foo\n\n\n  bar"),
        ("  foo\n\n\nbar", "  foo\n\n\n"),
        ("    foo\n  \n    bar", "    foo\n  \n    bar"),
        ("    \n  foo\n  bar", "    \n  foo\n  bar"),
        ("    \n  foo\nbar", "    \n  foo\n"),
        ("  foo\r\n\r\n  baz", "  foo\r\n\r\n  baz"),
        ("  foo\r\n\r\nbaz", "  foo\r\n\r\n"),
        ("\tfoo\n\t\tbar\n\tbaz", "\tfoo\n\t\tbar\n\tbaz"),
        ("\tfoo\n\t\tbar\nbaz", "\tfoo\n\t\tbar\n"),
        ("\tfoo\n    bar\nbaz", "\tfoo\n    bar\n"),
    ],
)
def test_trim_on_dedent(completion, expected):
    """Test trimming on dedent."""
    assert trim_on_dedent(completion) == expected

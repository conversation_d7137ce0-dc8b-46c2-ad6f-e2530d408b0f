"""Helper functions for postprocessing FIM generation."""

from megatron.tokenizer.tokenizer import ResearchSpecialTokens

from base.tokenizers.tokenizer import RagSpecialTokens, Tokenizer
from research.models.meta_model import FimGenMode


def get_stop_tokens(
    tokenizer: Tokenizer,
    fim_gen_mode: FimGenMode,
) -> set[int]:
    """Set stop tokens based on the FIM generation mode."""
    special_tokens = tokenizer.special_tokens
    assert isinstance(special_tokens, (ResearchSpecialTokens, RagSpecialTokens))
    if fim_gen_mode == FimGenMode.interactive:
        stop_tokens_set = {special_tokens.eos, special_tokens.pause}
    elif fim_gen_mode == FimGenMode.evaluation:
        stop_tokens_set = {special_tokens.eos, special_tokens.skip}
    elif fim_gen_mode == FimGenMode.raw:
        stop_tokens_set = {special_tokens.eos}
    else:
        raise ValueError(f"Unrecognized FimGenMode: {fim_gen_mode}")

    return stop_tokens_set


def extract_generation_before_stop_tokens(
    generated: list[int], stop_token_ids: list[int | None]
) -> tuple[list[int], int | None]:
    """Extract generation before stop tokens."""
    stop_tokens_ids_set = {
        token_id for token_id in stop_token_ids if token_id is not None
    }
    fim_stop_token_id = None
    for index in range(len(generated)):
        if generated[index] in stop_tokens_ids_set:
            fim_stop_token_id = generated[index]
            generated = generated[:index]
            break
    return generated, fim_stop_token_id


def fim_postprocess_generation(
    generated_tokens: list[int],
    eod_id: int | None,
    skip_id: int | None,
    pause_id: int | None,
    fim_gen_mode: FimGenMode,
) -> tuple[list[int], int | None]:
    """Perform FIM postprocessing of generated tokens.

    This function modifies the list of generated tokens depending on the specified
    FimGenMode (File Inclusion Mode). It applies different rules for halting token
    generation and for handling special tokens like end-of-document, skip, and pause.

    Args:
        tokens: A list of token IDs including prompt tokens + model generated tokens.
        num_prompt_tokens: The number of tokens in the prompt, used to differentiate
                           between prompt and generated content.
        eod_id: Optional. The ID of the end-of-document (EOS) token.
        skip_id: Optional. The ID of the skip token, used to skip certain parts of generation.
        pause_id: Optional. The ID of the pause token, used to introduce pauses in generation.
        fim_gen_mode: The mode of generation which determines the postprocessing behavior.
                      Can be 'raw', 'interactive', or 'evaluation'.

    Returns:
        A list of postprocessed token IDs. The postprocessing involves removing or
        rearranging tokens based on the generation mode and the presence of special
        control tokens like eod_id, skip_id, and pause_id.

    Raises:
        ValueError: If an unrecognized FimGenMode is provided.
    """
    if fim_gen_mode == FimGenMode.raw:
        # In general case (raw mode), stop generation only upon encountering the EOS token.
        return extract_generation_before_stop_tokens(generated_tokens, [eod_id])
    elif fim_gen_mode == FimGenMode.interactive:
        # In interactive mode, consider EOS, skip, and pause tokens for stopping generation.
        return extract_generation_before_stop_tokens(
            generated_tokens, [eod_id, skip_id, pause_id]
        )
    elif fim_gen_mode == FimGenMode.evaluation:
        # In evaluation mode, handle tokens differently to potentially recover from
        # generation stopped at max length.
        original_length = len(generated_tokens)
        generated_tokens, fim_stop_token_id = extract_generation_before_stop_tokens(
            generated_tokens, [eod_id, skip_id]
        )
        generation_stopped_at_max_length = original_length == len(generated_tokens)
        if generation_stopped_at_max_length and pause_id is not None:
            # If generation stopped at max length, truncate at the last pause token to
            # potentially recover from non-runnable code.
            for index in reversed(range(len(generated_tokens))):
                if generated_tokens[index] == pause_id:
                    generated_tokens = generated_tokens[:index]
                    break
        # During evaluation, remove all pause tokens starting from the prompt.
        generated_tokens = [token for token in generated_tokens if token != pause_id]
        return generated_tokens, fim_stop_token_id
    else:
        raise ValueError(f"Unrecognized FimGenMode: {fim_gen_mode}")

"""A code edit system that can generate the replacement."""

import dataclasses
import logging
import typing

from research.core.edit_prompt_input import (
    ResearchEditPromptInput,
    convert_research_edit_prompt_input_to_model_input,
)
from research.core.llama_prompt_formatters import CodeEditTemplateBasedPromptFormatter
from research.core.model_input import ModelInput
from research.core.tokenizers import get_all_special_tokens
from research.core.types import Document, DocumentId
from research.core.utils_for_str import (
    count_min_indentation,
    extract_the_last_markdown_block,
)
from research.eval.harness import factories
from research.eval.harness.systems.abs_system import (
    CodeInstructSystem,
    CompletionResult,
    register_system,
)
from research.eval.harness.systems.libraries.completion_trimmers import trim_on_dedent
from research.eval.harness.systems.utils_for_code_edit import (
    compute_actual_prefix_and_suffix,
    postprocess_model_response_for_edit,
)
from research.models.meta_model import (
    ExtraGenerationOutputs,
    GenerationOptions,
    GenerativeLanguageModel,
    ModelForwardOutput,
)
from research.retrieval.types import DocumentIndex
from research.utils.inspect_indexed_dataset import (
    escape_control_chars,
    highlight_special_tokens,
)

logger = logging.getLogger(__name__)


@register_system("rag_code_edit")
class RAGCodeEditSystem(CodeInstructSystem):
    """A code edit system that generates replacement for a code edit request."""

    def __init__(
        self,
        model: GenerativeLanguageModel,
        retriever: DocumentIndex,
        generation_options: GenerationOptions,
        context_free_prompt_formatter: CodeEditTemplateBasedPromptFormatter,
        context_dependent_prompt_formatter: CodeEditTemplateBasedPromptFormatter,
        input_token_budget: int,
        default_instruction: str | None = None,
        add_output_trimming: bool = False,
    ):
        """Construct the model.

        Args:
            model: Model to use for completions.
            retriever: Model for retrieving documents.
            generation_options: Options to use for inference
            context_free_prompt_formatter: The prompt formatter for context-free generation.
            context_dependent_prompt_formatter: The prompt formatter for context-dependent generation.
            input_token_budget: Soft upper bound on how much tokens should prefix+suffix+code use in prompt.
            default_instruction: Optionally provide a default instruction for when no instruction is provided,
                e.g. for use in hydra
            add_output_trimming: Whether to trim the output on dedent.
        """
        self.model = model
        self.retriever = retriever
        self.generation_options = generation_options
        self.context_free_prompt_formatter = context_free_prompt_formatter
        self.context_dependent_prompt_formatter = context_dependent_prompt_formatter
        self.input_token_budget = input_token_budget
        self.default_instruction = default_instruction
        self.add_output_trimming = add_output_trimming
        self.__loaded = False

    def load(self):
        """Load the model."""
        if not self.__loaded:
            self.model.load()
            self.retriever.load()
        self.__loaded = True

    def unload(self):
        """Unload the model."""
        if self.__loaded:
            self.model.unload()
            self.retriever.unload()
            self.__loaded = False

    def add_docs(self, src_files: typing.Collection[Document]):
        """Ingest a copy of the source code repository.

        Args:
          src_files: list of Documents to add
        """
        logger.info(f"Adding {len(src_files)} documents.")
        self.retriever.add_docs(src_files)  # type: ignore
        total_docs = len(self.retriever.get_doc_ids())
        logger.info(f"There are now {total_docs} total docs.")

    def remove_docs(self, doc_ids: typing.Collection[DocumentId]):
        """Remove documents from the retriever."""
        logger.info(f"Removing {len(doc_ids)} documents.")
        self.retriever.remove_docs(doc_ids)
        total_docs = len(self.retriever.get_doc_ids())
        logger.info(f"There are now {total_docs} total docs.")

    def clear_retriever(self):
        """Clear any stored documents from the retriever."""
        self.retriever.remove_all_docs()

    def _forward_pass_preprocess(
        self, model_input: ResearchEditPromptInput
    ) -> ModelInput:
        """Shared preprocessing for forward pass."""
        modified_model_input = model_input.clone()
        modified_model_input = self._retrieve(modified_model_input)
        modified_model_input_legacy_type = (
            convert_research_edit_prompt_input_to_model_input(modified_model_input)
        )

        return modified_model_input_legacy_type

    def forward_pass(
        self, inputs: list[ResearchEditPromptInput]
    ) -> list[ModelForwardOutput]:
        # Right now, our generative model expects a ModelInput
        inputs_legacy = [self._forward_pass_preprocess(x) for x in inputs]
        return self.model.forward_pass(inputs_legacy)

    def log_likelihood_continuation(
        self, model_input: ResearchEditPromptInput, continuation: str
    ) -> float | None:
        if not continuation:
            logger.warning("Skip computing log likelihood for empty continuation.")
            return None

        # Right now, our generative model expects a ModelInput
        model_input_legacy_type = self._forward_pass_preprocess(model_input)
        scores = self.model.log_likelihood_continuation(
            [model_input_legacy_type], [continuation]
        )
        return scores[0]

    def generate(  # pylint: disable=arguments-differ
        self, model_input: ResearchEditPromptInput
    ) -> CompletionResult:
        """Generate a completion."""

        model_input = model_input.clone()  # to avoid unintentional side-effects
        if model_input.extra.get("instruction", None) is None:
            if self.default_instruction is not None:
                model_input.extra["instruction"] = self.default_instruction
            else:
                raise ValueError(
                    "No selected code in model input and no default instruction."
                )
        if model_input.extra.get("selected_code", None) is None:
            logger.info(
                "No selected code in model input. Assuming it should be an empty string."
            )
            last_line = [s for s in model_input.prefix.splitlines() if s.strip()][-1]
            indent_str = last_line[: (len(last_line) - len(last_line.lstrip()))]
            model_input.extra["selected_code"] = (
                indent_str + "# TODO: Need to implement this.\n"
            )

        if model_input.extra.get("lines_in_prefix_suffix", None) is None:
            logger.info(
                "No lines_in_prefix_suffix in model input. Assuming it should be to use 50 lines each from prefix and suffix)."
            )
            model_input.extra["lines_in_prefix_suffix"] = 50

        (
            is_context_free,
            actual_prefix,
            actual_suffix,
        ) = compute_actual_prefix_and_suffix(
            model_input.prefix,
            model_input.suffix,
            model_input.extra["selected_code"],
            model_input.extra["lines_in_prefix_suffix"],
            self.input_token_budget,
        )

        model_input = dataclasses.replace(
            model_input, prefix=actual_prefix, suffix=actual_suffix
        )

        # Grab the documents from the retriever.
        model_input = self._retrieve(model_input)

        extra_outputs = ExtraGenerationOutputs()
        if is_context_free:
            prompter = self.context_free_prompt_formatter
        else:
            prompter = self.context_dependent_prompt_formatter

        # Right now, our generative model expects a ModelInput
        model_input_legacy_type = convert_research_edit_prompt_input_to_model_input(
            model_input
        )
        prompt, _ = prompter.prepare_prompt_text(model_input_legacy_type)

        # This is a temporary hack to make sure we can alternate between
        # context-free and context-dependent prompts.
        # Thus, for the model.prompt_formatter, the user needs to make sure
        # that it does not add additional text to the prompt.
        generation = self.model.generate(
            ModelInput(prefix=prompt), self.generation_options, extra_outputs
        )
        completion = CompletionResult(
            generated_text=generation,
            prompt_tokens=extra_outputs.prompt_tokens or [],
            retrieved_chunks=model_input.retrieved_chunks,
            extra_output=extra_outputs,
        )

        prompt = escape_control_chars(
            self.model.tokenizer.detokenize(completion.prompt_tokens)
        )
        logger.info("Full prompt:")
        # TODO(dxy): this func prints empty string, check the reason.
        logger.info(
            highlight_special_tokens(
                prompt, get_all_special_tokens(self.model.tokenizer)
            )
        )
        logger.info("\n" + ("-" * 80))  # pylint: disable=logging-not-lazy
        logger.info("Original response:")
        logger.info(escape_control_chars(completion.generated_text))
        logger.info("\n" + ("-" * 80))  # pylint: disable=logging-not-lazy

        if is_context_free:
            cleaned_generated_text = extract_the_last_markdown_block(
                completion.generated_text
            )
            if cleaned_generated_text is None:
                cleaned_generated_text = completion.generated_text
        else:
            cleaned_generated_text = postprocess_model_response_for_edit(
                generated_text=completion.generated_text,
                start_symbols="@@@@@@@@@",
                end_symbols="&&&&&&&&&",
                prefix=None if is_context_free else actual_prefix,
                suffix=None if is_context_free else actual_suffix,
            )

        # This is to fix the trailing newline of the response.
        if (
            len(model_input.extra["selected_code"]) > 0
            and len(cleaned_generated_text) > 0
            and model_input.extra["selected_code"][-1] == "\n"
            and cleaned_generated_text[-1] != "\n"
        ):
            cleaned_generated_text = cleaned_generated_text + "\n"

        # This is to fix the leading indentation of the response.
        min_spaces_for_original = count_min_indentation(
            model_input.extra["selected_code"]
        )
        min_spaces_for_modified = count_min_indentation(cleaned_generated_text)
        extra_identation = max(min_spaces_for_original - min_spaces_for_modified, 0)
        if extra_identation > 0:
            cleaned_generated_lines = []
            for xline in cleaned_generated_text.splitlines(keepends=True):
                cleaned_generated_lines.append(" " * extra_identation + xline)
            cleaned_generated_text = "".join(cleaned_generated_lines)

        cleaned_generated_text = self._post_processing(cleaned_generated_text)

        logger.debug("Postprocessed response:")
        logger.debug(escape_control_chars(cleaned_generated_text))

        return CompletionResult(
            generated_text=cleaned_generated_text,
            prompt_tokens=extra_outputs.prompt_tokens or [],
            retrieved_chunks=model_input.retrieved_chunks,
            extra_output=extra_outputs,
        )

    def get_model(self) -> GenerativeLanguageModel:
        return self.model

    @classmethod
    def from_yaml_config(cls, config: dict) -> "RAGCodeEditSystem":
        """Returns a System object constructed using a config dictionary."""
        model = factories.create_model(config["model"])
        retriever = factories.create_retriever(config["retriever"])
        generation_options = GenerationOptions(**config["generation_options"])
        context_free_prompt_formatter = CodeEditTemplateBasedPromptFormatter(
            **config["context_free_prompt_formatter"]
        )
        context_dependent_prompt_formatter = CodeEditTemplateBasedPromptFormatter(
            **config["context_dependent_prompt_formatter"]
        )
        assert (
            "input_token_budget" in config
        ), "input_token_budget field is missing from config"
        default_instruction = config.get("default_instruction", None)
        add_output_trimming = config.get("add_output_trimming", False)
        return RAGCodeEditSystem(
            model=model,
            retriever=retriever,
            generation_options=generation_options,
            context_free_prompt_formatter=context_free_prompt_formatter,
            context_dependent_prompt_formatter=context_dependent_prompt_formatter,
            input_token_budget=config["input_token_budget"],
            default_instruction=default_instruction,
            add_output_trimming=add_output_trimming,
        )

    # --------------- INTERNAL METHODS -------------------------------
    def _retrieve(
        self,
        model_input: ResearchEditPromptInput,
    ) -> ResearchEditPromptInput:
        """Copied from RAGSystem."""
        modified_model_input = model_input.clone()

        modified_model_input_legacy_type = (
            convert_research_edit_prompt_input_to_model_input(modified_model_input)
        )

        modified_model_input_legacy_type.prefix += (
            model_input.extra["selected_code"] + "\n" + model_input.extra["instruction"]
        )
        modified_model_input_legacy_type.suffix = ""

        if model_input.doc_ids is None:
            logger.warning("No doc_ids provided, using all doc_ids.")
        model_input = dataclasses.replace(
            model_input,
            retrieved_chunks=self.retriever.query(
                modified_model_input_legacy_type,
                doc_ids=model_input.doc_ids,
                top_k=10,  # TODO (c-flaherty): make this configurable
            ),
        )
        return model_input

    def _post_processing(
        self,
        completion: str,
    ) -> str:
        """Todo: (c-flaherty): explore other trimming logic."""
        if self.add_output_trimming:
            completion = trim_on_dedent(completion)
        return completion

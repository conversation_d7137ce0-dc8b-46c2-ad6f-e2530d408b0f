"""A code edit system based on in-house Droid model."""

import logging
import typing

from research.core.edit_prompt_input import (
    ResearchEditPromptInput,
    convert_research_edit_prompt_input_to_model_input,
)
from research.core.prod_adapters.edit_formatter_wrapper import (
    ProdEditPromptFormatterAdapter,
)
from research.core.types import Document, DocumentId
from research.eval.harness import factories
from research.eval.harness.systems.abs_system import (
    CodeEditResult,
    CodeInstructSystem,
    register_system,
)
from research.eval.harness.systems.utils_for_code_edit import (
    add_code_edit_default_extras,
    postprocess_fn,
)

# flake8: noqa
from research.models.fastforward_llama_models import (  # pylint: disable=unused-import
    FastForwardDroid,
)
from research.models.meta_model import (
    ExtraGenerationOutputs,
    GenerationOptions,
    GenerativeLanguageModel,
    ModelForwardOutput,
)

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


@register_system("droid_code_edit")
class DroidCodeEditSystem(CodeInstructSystem):
    """A code edit system based on in-house Droid model."""

    def __init__(
        self,
        model: GenerativeLanguageModel,
        generation_options: GenerationOptions,
        override_instruction: str | None = None,
        override_selected_code: str | None = None,
        post_process_strategy: str | None = None,
    ):
        self.model = model
        self.generation_options = generation_options
        # TODO: this 3 parameters are temporarily to enable code edit system can be used in Hydra task.
        # and they will be deprecated in the future once we finished the interface refactor and code edit evaluation task.
        self.override_instruction = override_instruction
        self.override_selected_code = override_selected_code
        self.post_process_strategy = post_process_strategy
        self.__loaded = False

    def generate(self, model_input: ResearchEditPromptInput) -> CodeEditResult:
        """Generate a completion."""
        model_input = add_code_edit_default_extras(
            model_input, self.override_instruction, self.override_selected_code
        )

        inner_extra_outputs = (
            ExtraGenerationOutputs()
        )  # Not exposed to caller of `generate`

        # Right now, our generative model expects a ModelInput
        model_input_legacy_type = convert_research_edit_prompt_input_to_model_input(
            model_input
        )
        full_response = self.model.generate(
            model_input_legacy_type, self.generation_options, inner_extra_outputs
        )
        updated_code = postprocess_fn(full_response, self.post_process_strategy)

        assert (
            inner_extra_outputs.prompt_tokens is not None
        )  # this is to let pylint happy
        full_prompt = self.model.tokenizer.detokenize(inner_extra_outputs.prompt_tokens)

        misc_output = {
            "full_response": full_response,
            "prompt": full_prompt,
            "num_input_tokens": len(inner_extra_outputs.prompt_tokens),
            "num_output_tokens": inner_extra_outputs.additional_info[
                "num_output_tokens"
            ],
        }

        logger.info(f"{'-' * 20} FULL PROMPT {'-' * 20}\n{full_prompt}{'-' * 80}")

        result = CodeEditResult(
            generated_text=updated_code,
            prompt_tokens=inner_extra_outputs.prompt_tokens,
            retrieved_chunks=[],
            extra_output=ExtraGenerationOutputs(additional_info=misc_output),
        )

        return result

    def forward_pass(
        self, inputs: list[ResearchEditPromptInput]
    ) -> list[ModelForwardOutput]:
        # Right now, our generative model expects a ModelInput
        inputs_legacy = [
            convert_research_edit_prompt_input_to_model_input(x) for x in inputs
        ]
        return self.model.forward_pass(inputs_legacy)

    def get_model(self) -> GenerativeLanguageModel:
        return self.model

    @classmethod
    def from_yaml_config(cls, config: dict):
        assert (
            config["name"] == "droid_code_edit"
        ), f"Invalid system name: {config['name']}"

        model = factories.create_model(config["model"])

        # Note(yuri): it's an intricate moment:
        # We are using prompt formatter from base/ via the wrapper : `ProdEditPromptFormatterAdapter`
        #
        # We need a way to control apportionment of tokens in the prompt formatter using config["model"], but since we don't directly
        # create prompt formatter, we are using rebinding here: https://github.com/augmentcode/augment/blob/70d26653acb1bea67544a9799a1dfa4fcfe051e6/research/eval/harness/factories.py#L81
        #
        # We need these asserts (especially the second one) to make sure that rebinded values from config["model"]["prompt"] are actually being used to create prompt formatter.
        # If the _prod_prompt_formatter is not None, it means that it was already created, likely before our rebinding happened.
        # _prod_prompt_formatter is created here: https://github.com/yurijvolkov/augment/blob/91d787de5a8eb3d5b18e993cafc86e8dbe701b83/research/core/prod_adapters/edit_formatter_wrapper.py#L84
        assert isinstance(model.prompt_formatter, ProdEditPromptFormatterAdapter)
        assert (  # pylint: disable=protected-access
            model.prompt_formatter._prod_prompt_formatter is None
        )

        generation_options = GenerationOptions(**config["generation_options"])

        override_instruction = config.get("override_instruction", "")
        override_selected_code = config.get("override_selected_code", "")
        post_process_strategy = config.get("post_process_strategy", "droid")

        return DroidCodeEditSystem(
            model=model,
            generation_options=generation_options,
            override_instruction=override_instruction,
            override_selected_code=override_selected_code,
            post_process_strategy=post_process_strategy,
        )

    def load(self):
        if not self.__loaded:
            self.model.load()
        self.__loaded = True

    def unload(self):
        if self.__loaded:
            self.model.unload()
            self.__loaded = False

    def clear_retriever(self):
        pass

    def add_docs(self, src_files: typing.Collection[Document]):
        del src_files

    def remove_docs(self, doc_ids: typing.Collection[DocumentId]):
        del doc_ids

    def get_num_docs(self):
        return 0

"""A code edit system based on in-house Droid model."""

import dataclasses
import logging
import typing

from research.core.model_input import ModelInput
from research.core.edit_prompt_input import (
    ResearchEditPromptInput,
    convert_research_edit_prompt_input_to_model_input,
)
from research.core.types import Document, DocumentId, JSONSerializableType
from research.eval.harness import factories
from research.eval.harness.systems.abs_system import (
    CodeEditResult,
    CodeInstructSystem,
    register_system,
)
from research.eval.harness.systems.utils_for_code_edit import (
    add_code_edit_default_extras,
)

from research.models.meta_model import (
    ExtraGenerationOutputs,
    GenerationOptions,
    GenerativeLanguageModel,
    ModelForwardOutput,
)
from research.retrieval.types import DocumentIndex

logger = logging.getLogger(__name__)


@register_system("droid_repo_code_edit")
class DroidRepoCodeEditSystem(CodeInstructSystem):
    """A code edit system based on in-house Droid model."""

    def __init__(
        self,
        model: GenerativeLanguageModel,
        generation_options: GenerationOptions,
        retriever: DocumentIndex | None = None,
        override_instruction: str | None = None,
        override_selected_code: str | None = None,
        grab_instruction_and_selected_code_from_extra: bool = False,
    ):
        self.model = model
        self.generation_options = generation_options
        self.retriever = retriever
        self.override_instruction = override_instruction
        self.override_selected_code = override_selected_code
        self.grab_instruction_and_selected_code_from_extra = (
            grab_instruction_and_selected_code_from_extra
        )
        self.__loaded = False
        self.num_retrieval_chunks = 128  # TODO (c-flaherty): make this configurable

    def _retrieve(
        self,
        model_input: ModelInput,
    ) -> ModelInput:
        """Retrieve relevant documents from database if retriever is enabled."""
        if self.retriever is None:
            return model_input
        doc_ids = model_input.doc_ids
        retrieved_chunks = self.retriever.query(
            model_input,
            doc_ids=doc_ids,
            top_k=self.num_retrieval_chunks,
        )[0]
        dataclasses.replace(model_input, retrieved_chunks=retrieved_chunks)
        return model_input

    def generate(self, model_input: ResearchEditPromptInput) -> CodeEditResult:
        """Generate a completion."""
        model_input = add_code_edit_default_extras(
            model_input, self.override_instruction, self.override_selected_code
        )

        extra_outputs = ExtraGenerationOutputs()
        # Right now, our generative model expects a ModelInput
        model_input_legacy_type = convert_research_edit_prompt_input_to_model_input(
            model_input
        )
        model_input_legacy_type = self._retrieve(model_input_legacy_type)

        logger.debug("start model generate")
        full_response = self.model.generate(
            model_input_legacy_type, self.generation_options, extra_outputs
        )
        logger.debug("end model generate")

        # TODO(rich): The attribute post_process_response doesn't exist in all
        # prompt formatters, but does in the experimental droid formatters. This
        # needs to be fixed at some point. The fmt directives prevent the line from
        # being reformatted to lose the pyright-ignore flag.
        # fmt: off
        updated_code = self.model.prompt_formatter.post_process_response(full_response)  # pyright: ignore [reportAttributeAccessIssue]
        # fmt: on

        assert extra_outputs.prompt_tokens is not None
        full_prompt = self.model.tokenizer.detokenize(extra_outputs.prompt_tokens)

        # logger.info(f"{'-' * 20} FULL PROMPT {'-' * 20}\n{full_prompt}{'-' * 80}")

        misc_output: dict[str, JSONSerializableType] = {
            "full_response": full_response,
            "prompt": full_prompt,
            "actual_prefix_in_prompt": model_input.prefix,
            "actual_suffix_in_prompt": model_input.suffix,
            "num_input_tokens": len(extra_outputs.prompt_tokens),
            "num_output_tokens": extra_outputs.additional_info["num_output_tokens"],
        }

        extra_output = ExtraGenerationOutputs(additional_info=misc_output)

        result = CodeEditResult(
            generated_text=updated_code,
            prompt_tokens=extra_outputs.prompt_tokens,
            retrieved_chunks=model_input.retrieved_chunks,
            extra_output=extra_output,
        )

        return result

    def forward_pass(
        self, inputs: list[ResearchEditPromptInput]
    ) -> list[ModelForwardOutput]:
        # Right now, our generative model expects a ModelInput
        inputs_legacy = [
            convert_research_edit_prompt_input_to_model_input(x) for x in inputs
        ]
        inputs_legacy = [self._retrieve(x) for x in inputs_legacy]
        return self.model.forward_pass(inputs_legacy)

    def get_model(self) -> GenerativeLanguageModel:
        return self.model

    @classmethod
    def from_yaml_config(cls, config: dict):
        assert (
            config["name"] == "droid_repo_code_edit"
        ), f"Invalid system name: {config['name']}"

        model = factories.create_model(config["model"])
        generation_options = GenerationOptions(**config["generation_options"])
        retriever = (
            factories.create_retriever(config["retriever"])
            if "retriever" in config
            else None
        )

        override_instruction = config.get("override_instruction", None)
        override_selected_code = config.get("override_selected_code", None)
        grab_instruction_and_selected_code_from_extra = config.get(
            "grab_instruction_and_selected_code_from_extra", False
        )
        return DroidRepoCodeEditSystem(
            model=model,
            generation_options=generation_options,
            retriever=retriever,
            override_instruction=override_instruction,
            override_selected_code=override_selected_code,
            grab_instruction_and_selected_code_from_extra=grab_instruction_and_selected_code_from_extra,
        )

    def load(self):
        if not self.__loaded:
            self.model.load()
            if self.retriever is not None:
                self.retriever.load()
            self.__loaded = True

    def unload(self):
        if self.__loaded:
            self.model.unload()
            if self.retriever is not None:
                self.retriever.unload()
            self.__loaded = False

    def clear_retriever(self):
        if self.retriever is not None:
            self.retriever.remove_all_docs()

    def add_docs(self, src_files: typing.Collection[Document]):
        if self.retriever is not None:
            self.retriever.add_docs(src_files)

    def remove_docs(self, doc_ids: typing.Collection[DocumentId]):
        if self.retriever is not None:
            self.retriever.remove_docs(doc_ids)

"""System for online retrieval-augmented generation, where we retrieve during the generation.

For example, if tokens_per_retrievestep=8, we will update the retrieved chunks for every 8 decoded tokens.
"""

from __future__ import annotations

import logging
import typing

from research.core.model_input import ModelInput
from research.core.types import Document, DocumentId
from research.eval.harness import factories
from research.eval.harness.systems.abs_system import (
    CodeCompleteSystem,
    CompletionResult,
    register_system,
)
from research.models import (
    ExtraGenerationOutputs,
    GenerationOptions,
    GenerativeLanguageModel,
    ModelForwardOutput,
)
from research.models.meta_model import FimGenMode, postprocess_generation
from research.retrieval.types import DocumentIndex

logger = logging.getLogger(__name__)


@register_system("online_rag")
class OnlineRAGSystem(CodeCompleteSystem):
    """Simple LM with single retriever."""

    def __init__(
        self,
        model: GenerativeLanguageModel,
        retriever: DocumentIndex,
        generation_options: GenerationOptions,
        fim_generation_mode: FimGenMode = FimGenMode.evaluation,
        retriever_top_k: int = 25,
        tokens_per_retrieval_step: int = 8,
        verbose: bool = False,
    ):
        self.model = model
        self.retriever = retriever
        self.generation_options = generation_options
        self.fim_generation_mode = fim_generation_mode
        self.retriever_top_k = retriever_top_k
        self.tokens_per_retrieval_step = tokens_per_retrieval_step
        self.verbose = verbose
        self.__loaded = False

    def load(self):
        if not self.__loaded:
            self.model.load()
            self.retriever.load()
        self.__loaded = True

    def unload(self):
        if self.__loaded:
            self.model.unload()
            self.retriever.unload()
            self.__loaded = False

    def add_docs(self, src_files: typing.Collection[Document]):
        """Ingest a copy of the source code repository.

        Args:
          src_files: list of Documents to add
        """
        logger.info(f"Adding {len(list(src_files))} documents.")
        self.retriever.add_docs(src_files)  # type: ignore
        total_docs = len(self.retriever.get_doc_ids())
        logger.info(f"There are now {total_docs} total docs.")

    def remove_docs(self, doc_ids: typing.Collection[DocumentId]):
        """Remove documents from the retriever."""
        logger.info(f"Removing {len(list(doc_ids))} documents.")
        self.retriever.remove_docs(doc_ids)
        total_docs = len(self.retriever.get_doc_ids())
        logger.info(f"There are now {total_docs} total docs.")

    def get_doc_ids(self) -> typing.AbstractSet[DocumentId]:
        return self.retriever.get_doc_ids()

    def clear_retriever(self):
        """Clear any stored documents from the retriever."""
        self.retriever.remove_all_docs()

    def get_model(self) -> GenerativeLanguageModel:
        return self.model

    def forward_pass(self, inputs: list[ModelInput]) -> list[ModelForwardOutput]:
        raise NotImplementedError()

    def log_likelihood_continuation(
        self, model_input: ModelInput, continuation: str
    ) -> float | None:
        """Returns the conditional log likelihood of the continuation, conditioned on the model input."""
        raise NotImplementedError()

    def generate(
        self,
        model_input: ModelInput,
    ) -> CompletionResult:
        """Generate a completion via iterativly retrieving and generating.

        We generate "tokens_per_retrieval_step" tokens each time and then update the retrieved chunks.
        """
        model_input = model_input.clone()
        assert (
            self.generation_options.max_generated_tokens is not None
        ), "For now, we expect this field to be populated in System."
        max_iters = max(
            self.generation_options.max_generated_tokens
            // self.tokens_per_retrieval_step,
            1,
        )
        tokens_per_step = []
        for i in range(max_iters):
            if i + 1 == max_iters:
                tokens_per_step.append(
                    self.generation_options.max_generated_tokens - sum(tokens_per_step)
                )
            else:
                tokens_per_step.append(self.tokens_per_retrieval_step)
        assert all(
            x > 0 for x in tokens_per_step
        ), f"tokens_per_step must be positive: {tokens_per_step}"
        tokenizer = self.model.tokenizer
        prompt_tokens, _ = self.model.prompt_formatter.prepare_prompt(model_input)
        generated_tokens: list[int] = []
        # Find the generated tokens.
        cur_model_input = model_input.clone()
        for i, expected_num_tokens in enumerate(tokens_per_step):
            cur_gen_options = self.generation_options.clone()
            cur_gen_options.max_generated_tokens = expected_num_tokens
            cur_model_input = self._retrieve(cur_model_input)
            cur_prompt_tokens, _ = self.model.prompt_formatter.prepare_prompt(
                cur_model_input
            )
            # It is extremely slow right now due to the context processing time would have
            # to be repeated for each raw_generate_tokens.
            cur_raw_outputs = self.model.raw_generate_tokens(
                cur_prompt_tokens, cur_gen_options
            )
            cur_raw_tokens = postprocess_generation(
                cur_raw_outputs.tokens,
                len(cur_prompt_tokens),
                tokenizer.special_tokens.eos,
                tokenizer.special_tokens.skip,  # type: ignore
                tokenizer.special_tokens.pause,  # type: ignore
                self.fim_generation_mode,
            )
            generated_tokens.extend(cur_raw_tokens)
            if len(generated_tokens) < expected_num_tokens:
                break
            if not cur_raw_tokens:
                break
            # Update the current model input
            cur_model_input = model_input.clone()
            generated_text = tokenizer.detokenize(prompt_tokens + generated_tokens)
            generated_text = generated_text[len(tokenizer.detokenize(prompt_tokens)) :]
            cur_model_input.prefix += generated_text
            if self.verbose:
                logger.info(f"Iteration {i + 1}")
                for ick, chunk in enumerate(cur_model_input.retrieved_chunks[:3]):
                    logger.info(
                        f"The {ick}-th chunk: {chunk.parent_doc.path} {chunk.range}:\n{chunk.text}"
                    )
                logger.info(f"Generated text:\n{generated_text}")

        generated_text = tokenizer.detokenize(prompt_tokens + generated_tokens)
        generated_text = generated_text[len(tokenizer.detokenize(prompt_tokens)) :]

        generation = CompletionResult(
            generated_text=generated_text,
            prompt_tokens=prompt_tokens,
            retrieved_chunks=model_input.retrieved_chunks,
            extra_output=ExtraGenerationOutputs(),
        )
        generation.retrieved_chunks = model_input.retrieved_chunks
        return generation

    @classmethod
    def from_yaml_config(cls, config: dict) -> OnlineRAGSystem:
        """Returns a System object constructed using a config dictionary."""
        # Prompt configuration happens as part of creating the model
        model = factories.create_model(config["model"])
        # We should do nothing about post-processing than just stop tokens,
        # the logic of handling post-processing should be done in the system.
        model.fim_gen_mode = FimGenMode.raw
        retriever = factories.create_retriever(config["retriever"])
        generation_options = GenerationOptions(**config["generation_options"])
        return OnlineRAGSystem(
            model,
            retriever,
            generation_options,
            fim_generation_mode=FimGenMode[config["fim_gen_mode"]],
            retriever_top_k=config["retriever_top_k"],
            tokens_per_retrieval_step=config["tokens_per_retrievestep"],
            verbose=config.get("verbose", False),
        )

    def _retrieve(
        self,
        model_input: ModelInput,
    ) -> ModelInput:
        if model_input.doc_ids is None:
            logger.warning("No doc_ids provided, using all doc_ids.")
        model_input.retrieved_chunks, _ = self.retriever.query(
            model_input,
            doc_ids=model_input.doc_ids,
            top_k=self.retriever_top_k,
        )
        return model_input

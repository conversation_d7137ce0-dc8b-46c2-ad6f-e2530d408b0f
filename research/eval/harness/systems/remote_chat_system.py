"""Code chat system that does the inference using a remote instance."""

import bisect
import hashlib
import logging
import time
from collections import Counter
from dataclasses import dataclass, field
from typing import Collection, Optional

import numpy as np

from base.augment_client.client import Model, UploadContent, WorkspaceFileChunk
from research.core.artifacts import artifacts_enabled, post_artifact
from research.core.chat_prompt_input import ResearchChatPromptInput
from research.core.types import Chunk, Document, DocumentId
from research.eval.harness.systems.abs_system import (
    ChatResult,
    ChatSystem,
    register_system,
)
from research.eval.harness.systems.remote_lib import (
    AugmentClientConfig,
    RemoteChatConfig,
    RemoteChatManager,
    RemoteRetriever,
    RemoteRetrieverConfig,
    get_augment_client,
    get_model_info,
)
from research.models.all_models import GenerativeLanguageModel, get_model
from research.models.meta_model import ExtraGenerationOutputs

logger = logging.getLogger(__name__)


@register_system("remote_chat")
class RemoteChatSystem(ChatSystem):
    """Code chat system that calls out to a remote instance via AugmentClient."""

    def __init__(
        self,
        client_config: AugmentClientConfig,
        retriever_config: RemoteRetrieverConfig,
        chat_config: RemoteChatConfig,
        model_name: Optional[str] = None,
    ):
        self.requested_model_name = model_name
        self.client = get_augment_client(client_config)
        self.retriever_config = retriever_config
        self.chat_config = chat_config

        # We provide a dummy model to satisfy the AbstractSystem interface.
        self.model = get_model("null")
        self._retriever: Optional[RemoteRetriever] = None
        self.chat_manager: Optional[RemoteChatManager] = None

    def load(self):
        """Load the system."""
        get_model_response = self.client.get_models()
        model_info = get_model_info(get_model_response, self.requested_model_name)

        logger.info("Remote client: %s", str(self.client))
        logger.info("Using remote model: %s", model_info)
        logger.info("Supported languages: %s", get_model_response.languages)
        logger.info("Feature flags: %s", get_model_response.feature_flags)

        model_client = self.client.client_for_model(model_info.name)
        assert get_model_response.feature_flags is not None
        max_upload_size_bytes = get_model_response.feature_flags.max_upload_size_bytes
        languages = (
            None
            if get_model_response.feature_flags.bypass_language_filter
            else list(get_model_response.languages)
        )

        self._retriever = RemoteRetriever(
            client=self.client,
            model_info=model_info,
            config=self.retriever_config,
            max_upload_size_bytes=max_upload_size_bytes,
            languages=languages,
        )
        self.chat_manager = RemoteChatManager(
            model_client=model_client,
            model_info=model_info,
            config=self.chat_config,
        )
        self.doc_id_to_content_mapping: dict[str, Document] = {}

    def unload(self):
        """Unload the system."""
        self._retriever = None
        self.chat_manager = None

    @property
    def retriever(self) -> RemoteRetriever:
        """Gets the remote retriever. Should only be called while loaded."""
        assert self._retriever is not None
        return self._retriever

    def convert_to_chunk_object(self, wfc: WorkspaceFileChunk) -> Chunk:
        """
        Converts a WorkspaceFileChunk to a Chunk, by computing line offsets
        from character offsets via binary search.
        """
        doc = self.doc_id_to_content_mapping[wfc.blob_name]
        doc_line_lengths = [len(line) for line in doc.text.splitlines(keepends=True)]
        doc_line_offsets = [0] + np.cumsum(doc_line_lengths).tolist()
        line_start = bisect.bisect_left(doc_line_offsets, wfc.char_start)
        line_end = bisect.bisect_right(doc_line_offsets, wfc.char_end)

        return Chunk(
            id=wfc.blob_name,
            text=doc.text[wfc.char_start : wfc.char_end],
            parent_doc=doc,
            char_offset=wfc.char_start,
            length=wfc.char_end - wfc.char_start,
            line_offset=line_start,
            length_in_lines=line_end - line_start,
        )

    def generate(self, model_input: ResearchChatPromptInput) -> ChatResult:
        """Evaluate the input against the chat model."""
        assert self.chat_manager is not None
        blob_names = model_input.doc_ids
        if blob_names is None:
            logger.warning("No doc_ids provided, using all blob_names.")
            blob_names = list(self.retriever.get_blob_names())
        blobs = self.retriever.get_blobs(blob_names)
        logger.debug(
            "Blobs: %s, %d, %d",
            blobs.checkpoint_id,
            len(blobs.added_blobs),
            len(blobs.deleted_blobs),
        )
        remote_result = self.chat_manager.chat(model_input, blobs)

        retrieved_chunks = [
            self.convert_to_chunk_object(wfc)
            for wfc in remote_result.workspace_file_chunks or []
            if wfc.blob_name in self.doc_id_to_content_mapping
        ]

        if remote_result.filtered_reason:
            logger.warning(
                "Chat path=%s was skipped because: %s",
                model_input.path,
                remote_result.filtered_reason,
            )

        if artifacts_enabled():
            post_artifact(
                {
                    "request_id": remote_result.request_id,
                    "filtered_reason": remote_result.filtered_reason,
                }
            )

        return ChatResult(
            generated_text=remote_result.generated_text,
            prompt_tokens=[],
            retrieved_chunks=retrieved_chunks,
            nodes=remote_result.nodes,
            extra_output=ExtraGenerationOutputs(
                additional_info={"request_id": remote_result.request_id},
            ),
        )

    def add_docs(self, src_files: Collection[Document]):
        """Ingest a copy of the source code repository."""
        logger.info("Adding %s documents.", len(src_files))
        start = time.time()
        resp = self.retriever.add_docs(src_files)
        doc_id_to_doc_mapping = {doc.id: doc for doc in src_files}
        successful_blob_ids = [blob.doc_id for blob in resp.successful_blobs]
        successful_docs = [
            doc_id_to_doc_mapping[doc_id] for doc_id in successful_blob_ids
        ]
        self.doc_id_to_content_mapping |= {doc.id: doc for doc in successful_docs}

        elapsed = time.time() - start
        extension_counts = Counter(
            blob_info.extension for blob_info in resp.filtered_by_extension
        )
        extension_counts = dict(extension_counts.most_common())
        logger.info(
            "Indexed %d documents in %.1f seconds, "
            "filtered %d by id, %d by size, %d by extension (%s)",
            len(resp.successful_blobs),
            elapsed,
            len(resp.filtered_by_id),
            len(resp.filtered_by_size),
            len(resp.filtered_by_extension),
            extension_counts,
        )

    def remove_docs(self, doc_ids: Collection[DocumentId]):
        """Remove documents from the retriever."""
        self.retriever.remove_docs(doc_ids)
        for doc_id in doc_ids:
            self.doc_id_to_content_mapping.pop(doc_id, None)

    def get_num_docs(self):
        return self.retriever.get_num_docs()

    def get_doc_ids(self) -> set[DocumentId]:
        """Return the set of indexed documents ids."""
        return self.retriever.get_doc_ids()

    def clear_retriever(self):
        """Clear any stored documents from the retriever."""
        self.retriever.clear_docs()

    def get_model(self) -> GenerativeLanguageModel:
        """Return the model."""
        return self.model

    @dataclass(frozen=True)
    class _RemoteChatSystemConfig:
        """Schema for configuring a RemoteChatSystem."""

        client: dict
        """The client config. Url is required."""
        retriever: dict = field(default_factory=dict)
        """The retriever config."""
        chat: dict = field(default_factory=dict)
        """The chat config."""
        model_name: Optional[str] = None
        """The desired model name, None to select the default model."""

    @classmethod
    def from_yaml_config(cls, config: dict) -> "RemoteChatSystem":
        """Returns a System object constructed using a config dictionary."""
        xconfig = cls._RemoteChatSystemConfig(**config)

        # Currently find missing does not work for chat models so we need to
        # override the internal defaults so that we do not issue find missing
        # calls. We do the retries during the actual chat calls instead.
        xconfig.retriever.setdefault("disable_wait_indexing", True)
        xconfig.chat.setdefault("indexing_retry_count", 3)

        return RemoteChatSystem(
            client_config=AugmentClientConfig(**xconfig.client),
            retriever_config=RemoteRetrieverConfig(**xconfig.retriever),
            chat_config=RemoteChatConfig(**xconfig.chat),
            model_name=xconfig.model_name,
        )

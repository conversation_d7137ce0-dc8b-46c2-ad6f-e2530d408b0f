"""The system for running the agent."""

from dataclasses import dataclass, field
from glob import glob
import json
from pathlib import Path
from research.eval.harness.systems.abs_system import AbstractSystem, register_system
from multiprocessing import Pool, Manager
from datetime import datetime
from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import (
    run_one,
    AgentInput,
    AgentOutput,
    MAX_DOCKER_CONCURRENCY,
)
import logging
from typing import Collection
from research.core.types import Document, DocumentId
from functools import lru_cache, partial
import pickle
import uuid


@dataclass
class AgentSystemConfig:
    num_processes: int = field(default=25)
    workdir: Path = field(default_factory=lambda: Path("/tmp/swe-bench-workspace/"))
    log_dir_base: Path = field(
        default_factory=lambda: Path("/mnt/efs/augment/user/colin/agent_logs/")
    )
    agent_timeout: int = field(default=1800)
    auth_token_file: Path = field(
        default_factory=lambda: Path("/home/<USER>/.augment/token")
    )
    modal: bool = field(default=False)
    """Whether to use the modal system."""
    add_sequential_thinking: bool = field(default=False)
    """Whether to add the sequential thinking tool."""
    add_scratchpad: bool = field(default=False)
    """Whether to add the scratchpad tool."""
    add_retrieval: bool = field(default=False)
    """Whether to add the retrieval tool, and update instruction to nudge towards using it."""
    use_low_qos_server: bool = field(default=False)
    """Whether to use the low QoS server for Anthropic direct calls."""
    swebench_sparse_system_prompt: bool = field(default=False)
    """Whether to use a sparse system prompt in SWE bench mode."""
    orientation_stage: bool = field(default=False)
    """Whether we are in the orientation stage."""
    thinking_tokens: int = field(default=0)
    """Number of tokens to use for the sequential thinking tool."""

    # ALTERNATE STAGES: ONLY ONE OF THESE SHOULD BE SET
    candidate_for_refinement_dir: str | None = field(default=None)
    """Path to a directory containing candidates to use for refinement."""
    ensemble_candidates_path: str | None = field(default=None)
    """Path to a file containing candidates to use for ensembling."""
    candidate_for_filtering_dir: str | None = field(default=None)
    """Path to a directory containing candidates to use for filtering."""


@lru_cache(maxsize=100)
def get_example_eval_dirs(run_dir):
    return glob(run_dir + "workspace_*/logs/run_evaluation/swe_work/Augment_Agent/*/")


def get_consolidated_run_id(dir: str) -> str:
    return dir.split("consolidated_runs/")[1].split("/")[0]


@register_system("agent")
@dataclass
class AgentSystem(AbstractSystem[AgentInput, AgentOutput]):
    def __init__(self, config: AgentSystemConfig):
        self.config = config
        self.config.workdir = Path(config.workdir)
        self.config.log_dir_base = Path(config.log_dir_base)
        self.log_dir = self.config.log_dir_base / datetime.now().strftime(
            "%Y-%m-%d_%H-%M-%S"
        )
        self.config.auth_token_file = Path(self.config.auth_token_file)

        self.ensemble_candidates = None
        if self.config.ensemble_candidates_path is not None:
            with open(self.config.ensemble_candidates_path, "rb") as f:
                self.ensemble_candidates = pickle.load(f)

        self.instance_id_to_candidate_patch = None
        if self.config.candidate_for_refinement_dir is not None:
            assert (
                self.ensemble_candidates is None
            ), "Cannot set both ensemble_candidates_path and candidate_for_refinement_dir"
            self.instance_id_to_candidate_patch = {}

            self.instance_id_to_candidate_patch = {}
            for example_eval_dir in get_example_eval_dirs(
                self.config.candidate_for_refinement_dir
            ):
                patch_diff_path = example_eval_dir + "patch.diff"
                report_path = example_eval_dir + "report.json"

                try:
                    with open(patch_diff_path, "r") as f:
                        patch_diff = f.read()
                    with open(report_path, "r") as f:
                        report = json.load(f)
                    instance_id, _ = list(report.items())[0]
                except FileNotFoundError:
                    continue
                self.instance_id_to_candidate_patch[instance_id] = patch_diff

        self.instance_id_to_candidate_patch_for_filtering = None
        if self.config.candidate_for_filtering_dir is not None:
            assert (
                self.ensemble_candidates is None
            ), "Cannot set both ensemble_candidates_path and candidate_for_filtering_dir"
            assert (
                self.instance_id_to_candidate_patch is None
            ), "Cannot set both candidate_for_refinement_dir and candidate_for_filtering_dir"
            self.instance_id_to_candidate_patch_for_filtering = {}

            for example_eval_dir in get_example_eval_dirs(
                self.config.candidate_for_filtering_dir
            ):
                patch_diff_path = example_eval_dir + "patch.diff"
                report_path = example_eval_dir + "report.json"

                try:
                    with open(patch_diff_path, "r") as f:
                        patch_diff = f.read()
                    with open(report_path, "r") as f:
                        report = json.load(f)
                    instance_id, _ = list(report.items())[0]
                except FileNotFoundError:
                    continue
                self.instance_id_to_candidate_patch_for_filtering[instance_id] = (
                    patch_diff
                )

    @classmethod
    def from_yaml_config(cls, config: dict):
        formatted_config = AgentSystemConfig(**config)
        return cls(formatted_config)

    def generate_parallel(self, model_inputs: list[AgentInput]) -> list[AgentOutput]:
        should_eager_eval = model_inputs[0].eager_eval
        assert all(
            sample.eager_eval == should_eager_eval for sample in model_inputs
        ), "All samples must have the same eager_eval value"

        # (c-flaherty) two bugs in other code path to fix:
        # - some weird timeout: UnixHTTPConnectionPool(host='localhost', port=None): Read timed out. (read timeout=60)
        # - evaluation logs not getting staged correctly by stage_results
        assert should_eager_eval, "The other code path is buggy right now"

        # save candidates
        if self.ensemble_candidates is not None:
            ensemble_candidates_path = f"/tmp/ensemble_candidates_{uuid.uuid4()}.pkl"
            with open(ensemble_candidates_path, "wb") as f:
                pickle.dump(self.ensemble_candidates, f)
        else:
            ensemble_candidates_path = None

        if self.instance_id_to_candidate_patch is not None:
            candidate_for_refinement_path = (
                f"/tmp/candidate_for_refinement_{uuid.uuid4()}.pkl"
            )
            with open(candidate_for_refinement_path, "wb") as f:
                pickle.dump(self.instance_id_to_candidate_patch, f)
        else:
            candidate_for_refinement_path = None

        if self.instance_id_to_candidate_patch_for_filtering is not None:
            candidate_for_filtering_path = (
                f"/tmp/candidate_for_filtering_{uuid.uuid4()}.pkl"
            )
            with open(candidate_for_filtering_path, "wb") as f:
                pickle.dump(self.instance_id_to_candidate_patch_for_filtering, f)
        else:
            candidate_for_filtering_path = None

        with Manager() as manager:
            lock = manager.Lock()
            semaphore = manager.Semaphore(MAX_DOCKER_CONCURRENCY)
            with Pool(processes=self.config.num_processes) as pool:
                results = pool.starmap(
                    partial(
                        run_one,
                        workdir=self.config.workdir,
                        logdir=self.log_dir,
                        agent_timeout=self.config.agent_timeout,
                        lock=lock,
                        semaphore=semaphore,
                        auth_token_file=self.config.auth_token_file,
                        evaluate=should_eager_eval,
                        dataset="verified",  # Hard-coded because we only support this one right now
                        remove_image=should_eager_eval,
                        modal=self.config.modal,
                        add_sequential_thinking=self.config.add_sequential_thinking,
                        add_scratchpad=self.config.add_scratchpad,
                        add_retrieval=self.config.add_retrieval,
                        use_low_qos_server=self.config.use_low_qos_server,
                        swebench_sparse_system_prompt=self.config.swebench_sparse_system_prompt,
                        use_container_workspace=True,
                        orientation_stage=self.config.orientation_stage,
                        thinking_tokens=self.config.thinking_tokens,
                        # ONLY ONE WILL BE NON-NONE
                        candidate_for_refinement_path=candidate_for_refinement_path,
                        ensemble_candidates_path=ensemble_candidates_path,
                        candidate_for_filtering_path=candidate_for_filtering_path,
                    ),
                    [
                        (sample.input, sample.use_direct_client)
                        for sample in model_inputs
                    ],
                )

        failure_count = sum(1 for _, rc in results if rc != 0)
        logging.info(
            f"Failed {failure_count} of {len(results)} runs ({failure_count/len(results)*100:.2f}%)"
        )
        return [AgentOutput(rc) for _, rc in results]

    def load(self):
        pass

    def unload(self):
        pass

    def generate(self, model_input: AgentInput) -> AgentOutput:
        raise NotImplementedError("generate method not implemented")

    def get_model(self):
        raise NotImplementedError("get_model method not implemented")

    def add_docs(self, src_files: Collection[Document]):
        raise NotImplementedError("add_docs method not implemented")

    def remove_docs(self, doc_ids: Collection[DocumentId]):
        raise NotImplementedError("remove_docs method not implemented")

    def clear_retriever(self):
        """Clear any documents from the retriever.

        This is useful for clearing out the current repo from the retriever.
        """
        pass

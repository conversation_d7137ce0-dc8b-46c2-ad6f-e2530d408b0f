import copy
import dataclasses
import logging
from collections import defaultdict
from dataclasses import dataclass
from typing import (
    AbstractSet,
    Callable,
    Collection,
    Iterable,
    Mapping,
    Sequence,
    assert_never,
)

from intervaltree import IntervalTree
from tqdm import tqdm

from base.languages.language_guesser import guess_language
from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>, LineRange
from base.retrieval.chunking.line_based_chunking import LineChunkContents
from base.static_analysis.common import check_not_none, groupby
from research.core.next_edit_location_prompt_input import (
    FileLocation,
    FileLocationWithHeader,
    NextEditLocationOutput,
    NextEditLocationSystemInput,
)
from research.core.types import Chunk, Document, DocumentId, NTuple, Scored
from research.core.utils import FileLogger
from research.eval.harness.systems.abs_system import AbstractSystem, register_system
from research.eval.harness.systems.next_edit_gen_system import (
    EditGenSystemInput,
    NextEditGenSystem,
)
from research.eval.harness.systems.next_edit_location_system import (
    BasicNextEditLocationSystem,
)
from research.models.fastforward_models import GenerationCanceledError
from research.next_edits.diagnostics import <PERSON>agnos<PERSON>
from research.next_edits.smart_chunking import SmartChunker
from research.retrieval.chunking_functions import get_chunk_id


# kw_only=True so that we can add fields without default values after base
# class has fields with default values
@dataclass(kw_only=True)
class NextEditRerankerOutput(NextEditLocationOutput):
    """A subclass of the NextEditLocationOutput that includes the reranking info."""

    @dataclass
    class DebugInfo:
        """Stores extra information for debugging."""

        before_reranking: Sequence[Scored[FileLocation]]
        """The candidate locations before reranking."""

        diagnostics: NTuple[Diagnostic]
        """Diagnostics, with the most recent first."""

        diagnostic_locations: Sequence[Scored[FileLocation]]
        """The diagnostic locations."""

        def log_to_file(self, file_logger: FileLogger):
            file_logger.log(
                "before_reranking.txt",
                "\n".join(str(loc) for loc in self.before_reranking),
            )
            diagnostics_text = "\n".join(
                f"path={r.location.path}, range={str(LineRange(r.location.line_start, r.location.line_end))}, message={r.message}, severity={r.severity}"
                for r in self.diagnostics
            )
            diagnostic_locations_text = "\n".join(
                f"location={str(r.item)}, score={r.score:.4g}"
                for r in self.diagnostic_locations
            )
            SEP = "~=" * 40
            combined_results_text = (
                f"diagnostics:\n{diagnostics_text}\n"
                f"{SEP}\n"
                f"diagnostic_locations:\n{diagnostic_locations_text}\n"
            )
            file_logger.log("diagnostics.txt", combined_results_text)

    reranker_debug_info: DebugInfo
    """Stores extra information for debugging."""

    def log_to_file(self, file_logger: FileLogger):
        super().log_to_file(file_logger)
        self.reranker_debug_info.log_to_file(file_logger)


@dataclass
class _ScoredChunk:
    """A Chunk with a retrieval score."""

    chunk: Chunk
    score: float


@register_system("next_edit_reranker")
@dataclass
class NextEditRerankerSystem(
    AbstractSystem[NextEditLocationSystemInput, NextEditRerankerOutput]
):
    """A system that reranks a localization system's output using an editing system.

    Note that this system itself can be used as a localization system.
    """

    localizer: BasicNextEditLocationSystem
    """The localization system."""

    reranker: NextEditGenSystem
    """The reranking system."""

    rechunker: SmartChunker | None = None
    """If provided, will remap the locations to chunks given by this chunker."""

    filter_results_threshold: float | None = None
    """If set, filter results with scores above threshold and do not re-order."""

    record_all_locations: bool = False
    """If set, the reranker_debug_info.before_reranking will contain all locations."""

    def generate(
        self,
        model_input: NextEditLocationSystemInput,
        should_cancel: Callable[[], bool] = lambda: False,
    ) -> NextEditRerankerOutput:
        sys_input = model_input

        # First call the localization system to get candidate locations.
        localization_sys_input = sys_input
        assert self.record_all_locations
        if self.record_all_locations:
            localization_sys_input = dataclasses.replace(sys_input, top_k=None)
        location_output = self.localizer.generate(localization_sys_input)
        if should_cancel():
            raise GenerationCanceledError()

        before_reranking = list(location_output.scored_candidates)
        candidate_locations = list(location_output.scored_candidates)
        if self.record_all_locations:
            # add top_k filter back because we only want to record all locations in before_reranking.
            candidate_locations = candidate_locations[: sys_input.top_k]
        all_docs = self.get_documents(sys_input.doc_ids)
        path_to_doc = {doc.path: doc for doc in all_docs}

        # Add diagnostic locations as additional candidates.
        diagnostics = sys_input.diagnostics
        diagnostic_candidate_locations = self._get_diagnostic_candidates(
            diagnostics, path_to_doc
        )

        candidate_locations.extend(diagnostic_candidate_locations)

        if self.rechunker:
            candidate_locations = self._remap_all_locations(
                candidate_locations, path_to_doc
            )
        else:
            candidate_locations = self._combine_overlapping_candidates(
                candidate_locations
            )

        reranked_locations_and_chunks = list[
            Scored[tuple[FileLocationWithHeader, Chunk]]
        ]()

        # Now run the reranking system
        for scored_loc in tqdm(candidate_locations, desc="Reranking"):
            if should_cancel():
                raise GenerationCanceledError()
            file_loc = scored_loc.item
            file_doc = path_to_doc[file_loc.path]
            file_lines = file_doc.text.splitlines(keepends=True)
            prefix_lines = file_lines[: file_loc.range.start]
            selected_lines = file_lines[file_loc.range.to_slice()]
            suffix_lines = file_lines[file_loc.range.stop :]

            prefix = "".join(prefix_lines)
            selected_code = "".join(selected_lines)

            gen_sys_input = EditGenSystemInput(
                path=file_loc.path,
                prefix=prefix,
                selected_code=selected_code,
                suffix="".join(suffix_lines),
                instruction=sys_input.instruction or "",
                recent_changes=tuple(sys_input.recent_changes),
                generate_description=False,
                doc_ids=sys_input.doc_ids,
                must_change=False,
            )
            options = self.reranker.generation_options
            old_options = copy.copy(options)
            options.max_generated_tokens = 1
            reranker_output = self.reranker.generate(
                gen_sys_input, should_cancel=should_cancel
            )
            self.reranker.generation_options = old_options

            # The change probability is the score
            prob_changed = check_not_none(reranker_output.prob_changed)

            chunk = Chunk(
                id=get_chunk_id(doc_id=file_doc.id, chunk_text=selected_code),
                text=selected_code,
                parent_doc=file_doc,
                char_offset=len(prefix),
                length=len(selected_code),
                line_offset=file_loc.range.start,
                length_in_lines=len(selected_lines),
            )

            header = (
                file_loc.header if isinstance(file_loc, FileLocationWithHeader) else ""
            )
            file_loc = FileLocationWithHeader(
                path=file_loc.path,
                range=file_loc.range,
                header=header,
            )
            reranked_locations_and_chunks.append(
                Scored(item=(file_loc, chunk), score=prob_changed)
            )

        if self.filter_results_threshold is not None:
            reranked_locations_and_chunks = [
                x
                for x in reranked_locations_and_chunks
                if x.score > self.filter_results_threshold
            ]
        else:
            # Sort by score, highest score first
            reranked_locations_and_chunks.sort(key=lambda x: x.score, reverse=True)

        reranked_locations: list[Scored[FileLocationWithHeader]] = [
            Scored(x.item[0], x.score) for x in reranked_locations_and_chunks
        ]
        chunks: list[Chunk] = [x.item[1] for x in reranked_locations_and_chunks]

        return NextEditRerankerOutput(
            scored_candidates=reranked_locations,
            top_chunks=chunks,
            debug_info=location_output.debug_info,
            reranker_debug_info=NextEditRerankerOutput.DebugInfo(
                before_reranking=before_reranking,
                diagnostics=sys_input.diagnostics,
                diagnostic_locations=diagnostic_candidate_locations,
            ),
        )

    def _remap_all_locations(
        self, locations: list[Scored[FileLocation]], path_to_doc: Mapping[str, Document]
    ) -> list[Scored[FileLocationWithHeader]]:
        new_locations = list[Scored[FileLocationWithHeader]]()
        path_to_locations = groupby(locations, lambda x: x.item.path)
        for path, locations in path_to_locations.items():
            doc = path_to_doc[path]
            new_locations.extend(self._remap_doc_locations(locations, doc))
        new_locations.sort(key=lambda x: x.score, reverse=True)
        return new_locations

    def _remap_doc_locations(
        self, locations: Sequence[Scored[FileLocation]], doc: Document
    ) -> list[Scored[FileLocationWithHeader]]:
        """Remap the scores of the locations to the new chunks.

        This function assumes all locations are from the provided doc
        """
        chunker = self.rechunker
        assert chunker is not None
        chunks = chunker.split_chunks(doc.text, lang=guess_language(doc.path))
        chunks_tree = IntervalTree.from_tuples(
            (chunk.lrange().start, chunk.lrange().stop, chunk) for chunk in chunks
        )

        chunk_header_dict = {chunk.lrange(): chunk.header for chunk in chunks}

        chunk_scores = defaultdict[CharRange, float](lambda: float("-inf"))
        for loc in locations:
            assert loc.item.path == doc.path
            start = loc.item.range.start
            stop = loc.item.range.stop
            # find all chunks that overlap with the location
            all_overlaps: list[LineChunkContents]
            all_overlaps = [item.data for item in chunks_tree.overlap(start, stop)]
            if len(all_overlaps) > 2:
                # We only take at most two overlapped chunks to bound the number of
                # chunks to rerank later.
                logging.warning(
                    f"Found {len(all_overlaps)} overlapping chunks for {loc.item}."
                )
                all_overlaps.sort(key=lambda x: x.lrange().start)
                all_overlaps = all_overlaps[:2]
            for chunk in all_overlaps:
                chunk_range = chunk.lrange()
                chunk_scores[chunk_range] = max(chunk_scores[chunk_range], loc.score)
        new_locations = [
            Scored(
                FileLocationWithHeader(
                    path=doc.path, range=lrange, header=chunk_header_dict[lrange]
                ),
                score,
            )
            for lrange, score in chunk_scores.items()
        ]
        new_locations.sort(key=lambda x: x.score, reverse=True)
        return new_locations

    def _get_diagnostic_candidates(
        self, diagnostics: Sequence[Diagnostic], path_to_doc: dict[str, Document]
    ) -> list[Scored[FileLocation]]:
        num_lines_to_expand = 10
        max_num_diagnostics = 5
        candidate_locations = list[Scored[FileLocation]]()
        for diag in diagnostics:
            if diag.location.path not in path_to_doc:
                continue
            range = LineRange(diag.location.line_start, diag.location.line_end)
            doc_lrange = path_to_doc[diag.location.path].line_range
            range_start = max(range.start - num_lines_to_expand, 0)
            range_stop = min(
                range.stop + num_lines_to_expand,
                doc_lrange.stop,
            )
            if range_stop < range_start:
                logging.error(
                    f"Incorrect diagnostic range: {range_start=}, {range_stop=}\n"
                    f"{diag.location.path=}, {range=}, {doc_lrange=}\n",
                    f"{diag.message=}",
                )
                continue
            candidate_range = LineRange(range_start, range_stop)
            if len(candidate_range) > 80:
                # drop giant errors
                continue
            # Lower severity is more important.
            # Using this as a score isn't comparable to the normal scores, though.
            match diag.severity:
                case "ERROR":
                    score = 0
                case "WARNING":
                    score = -1
                case "INFORMATION":
                    score = -2
                case "HINT":
                    score = -3
                case _:
                    assert_never(diag.severity)
            candidate_locations.append(
                Scored(
                    FileLocation(diag.location.path, candidate_range),
                    score,
                )
            )
        # We combine overlaps here even though we'll do it again once we
        # combine with the normal candidates since we are also capping the
        # number of diagnostic candidates.  Otherwise we might later combine
        # them all into one and get too few diagnostic candidates.
        candidate_locations = self._combine_overlapping_candidates(candidate_locations)
        return candidate_locations[:max_num_diagnostics]

    def _combine_overlapping_candidates(
        self, candidates: list[Scored[FileLocation]]
    ) -> list[Scored[FileLocation]]:
        max_candidate_lines = 60
        # Sort the candidates so we only have to compare adjacent candidates.
        candidates = sorted(candidates, key=lambda x: (x.item.path, x.item.range.start))
        candidate_locations = list[Scored[FileLocation]]()
        for cur in candidates:
            # Merge adjacent candidates if they intersect and the combined
            # range isn't too large.
            if (
                candidate_locations
                and (prev := candidate_locations[-1])
                and prev.item.path == cur.item.path
                and prev.item.range.intersect(cur.item.range) is not None
                and (merged_range := prev.item.range.merge(cur.item.range))
                and len(merged_range) < max_candidate_lines
            ):
                cur = Scored(
                    FileLocation(prev.item.path, merged_range),
                    # These scores don't really matter.
                    max(prev.score, cur.score),
                )
                candidate_locations.pop()
            candidate_locations.append(cur)
        candidate_locations = sorted(
            candidate_locations, key=lambda x: x.score, reverse=True
        )
        return candidate_locations

    # boring methods below

    def get_documents(self, doc_ids: Iterable[DocumentId]) -> list[Document]:
        """Return all documents with the given ids indexed by the retriever."""
        return self.localizer.get_documents(doc_ids)

    def load(self):
        self.localizer.load()
        self.reranker.load()

    def unload(self):
        self.localizer.unload()
        self.reranker.unload()

    def get_model(self):
        raise NotImplementedError()

    def add_docs(self, src_files: Collection[Document]):
        self.localizer.add_docs(src_files)
        self.reranker.add_docs(src_files)

    def remove_docs(self, doc_ids: Collection[DocumentId]):
        self.localizer.remove_docs(doc_ids)
        self.reranker.remove_docs(doc_ids)

    def get_doc_ids(self) -> AbstractSet[DocumentId]:
        return self.localizer.get_doc_ids()

    def clear_retriever(self):
        self.localizer.clear_retriever()
        self.reranker.clear_retriever()

    @property
    def _retriever(self):
        # this property is required by `next_edit_location_task.py`.
        return self.localizer._retriever

    @classmethod
    def from_yaml_config(cls, config: dict):
        config = copy.copy(config)
        localizer = BasicNextEditLocationSystem.from_yaml_config(
            config.pop("localizer")
        )
        reranker = NextEditGenSystem.from_yaml_config(config.pop("reranker"))
        return cls(localizer, reranker, **config)

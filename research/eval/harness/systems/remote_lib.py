"""A collection of functions for interacting with remote systems."""

import dataclasses
import json
import logging
import time
from dataclasses import dataclass, field
from typing import Collection

from base.augment_client.client import (
    AugmentClient,
    AugmentModelClient,
    BlobsJson,
    ChatResponse,
    ChatResultNode,
    ClientException,
    EditResponse,
    Exchange,
    Language,
    Model,
    UploadContent,
    WorkspaceFileChunk,
)
from base.augment_client.remote_lib import (
    AddDocsResponse,
    AugmentClientConfig,
    BlobInfo,
    RemoteCompletionConfig,
    RemoteCompletionInput,
    RemoteCompletionResult,
    RemoteRetrieverConfig,
    batched,
    get_augment_client,
    get_model_info,
)
from base.augment_client.remote_lib import (
    RemoteCompletionManager as RemoteCompletionManagerBase,
)
from base.augment_client.remote_lib import (
    RemoteRetriever as RemoteRetrieverBase,
)
from base.diff_utils.edit_events import GranularEditEvent, SingleEdit
from base.prompt_format.common import (
    get_request_message_as_text,
    get_response_message_as_text,
)
from base.prompt_format.recency_info import RecencyInfo
from base.python.au_functools.wrappers import method_wrap
from research.core.chat_prompt_input import ResearchChatPromptInput
from research.core.edit_prompt_input import ResearchEditPromptInput
from research.core.model_input import ModelInput
from research.core.types import CharRange
from research.retrieval.types import Document, DocumentId

logger = logging.getLogger(__name__)


# TODO(jeff): Unit test this and completion manager via a fake client.
@dataclass
class RemoteRetriever:
    """A helper class for managing a retriever state with a remote system."""

    client: AugmentClient
    """The client to use for the retriever."""
    model_info: Model
    """"The model information for the retriever."""
    config: RemoteRetrieverConfig
    """The configuration for the retriever."""
    max_upload_size_bytes: int
    """The maximum size of a document that should be uploaded for retrieval."""
    languages: list[Language] | None = None
    """Optional list of supported languages for the retriever."""

    def __post_init__(self):
        self.retriever = RemoteRetrieverBase(
            client=self.client,
            model_info=self.model_info,
            config=self.config,
            max_upload_size_bytes=self.max_upload_size_bytes,
            languages=self.languages,
        )

    def get_blobs(self, blob_names: Collection[str]) -> BlobsJson:
        return self.retriever.get_blobs(blob_names)

    def get_num_docs(self) -> int:
        return self.retriever.get_num_docs()

    def get_doc_ids(self) -> set[DocumentId]:
        return self.retriever.get_doc_ids()

    def add_docs(self, docs: Collection[Document]) -> AddDocsResponse:
        new_docs = [UploadContent(doc.text, doc.path) for doc in docs]
        return self.retriever.add_docs(new_docs)

    def remove_docs(self, doc_ids: Collection[DocumentId]):
        """Remove documents from the retriever."""
        new_ids = [str(doc_id) for doc_id in doc_ids]
        self.retriever.remove_docs(new_ids)

    def clear_docs(self):
        """Clear any stored documents from the retriever."""
        self.retriever.clear_docs()

    def get_blob_names(self) -> set[str]:
        return self.retriever._blob_names


def recency_info_to_dict(recency_info: RecencyInfo) -> dict:
    return {
        "tab_switch_events": [
            {
                "path": event.path,
                "file_blob_name": event.file_blob_name,
            }
            for event in recency_info.tab_switch_events
        ],
        "git_diff_file_info": [
            {
                "content_blob_name": info.content_blob_name,
                "file_blob_name": info.file_blob_name,
            }
            for info in recency_info.git_diff_info
        ],
        "recent_changes": [
            {
                "blob_name": change.blob_name,
                "path": change.path,
                "char_start": change.char_start,
                "char_end": change.char_end,
                "replacement_text": change.replacement_text,
                "present_in_blob": change.present_in_blob,
            }
            for change in recency_info.recent_changes
        ],
    }


def edit_events_to_dict(edit_events: list[GranularEditEvent]) -> list[dict]:
    return [
        {
            "path": event.path,
            "before_blob_name": event.before_blob_name,
            "after_blob_name": event.after_blob_name,
            "edits": [
                {
                    "before_start": edit.before_start,
                    "after_start": edit.after_start,
                    "before_text": edit.before_text,
                    "after_text": edit.after_text,
                }
                for edit in event.edits
            ],
        }
        for event in edit_events
    ]


@dataclass
class RemoteCompletionManager:
    """A helper class for managing completion calls with a remote system."""

    model_client: AugmentModelClient
    """The client to use for the remote system."""

    model_info: Model
    """The model information from the remote system."""

    config: RemoteCompletionConfig
    """The configuration for the remote system."""

    def __post_init__(self):
        self.remote_completion_manager = RemoteCompletionManagerBase(
            model_client=self.model_client,
            model_info=self.model_info,
            config=self.config,
        )

    def generate(
        self, model_input: ModelInput, blobs: BlobsJson
    ) -> RemoteCompletionResult:
        assert self.model_info is not None
        assert self.model_client is not None

        assert model_input.cursor_position is not None

        """Generate a completion for the given model input."""
        new_input = RemoteCompletionInput(
            prefix=model_input.prefix,
            path=model_input.path,
            suffix=model_input.suffix,
            recency_info=(
                recency_info_to_dict(model_input.recency_info)
                if model_input.recency_info is not None
                else None
            ),
            cursor_position=model_input.cursor_position,
            edit_events=edit_events_to_dict(model_input.edit_events)
            if model_input.edit_events is not None
            else None,
        )

        return self.remote_completion_manager.generate(new_input, blobs)


@dataclass(frozen=True)
class RemoteEditConfig:
    """Client-side config settings for the edit system."""

    # TODO(jeff): We can remove retries here once find_missing supports edit hosts.
    indexing_retry_sleep_secs: float = 20.0
    """Seconds to sleep between tries waiting for indexing to complete."""
    indexing_retry_count: int = 0
    """Number of retries waiting for indexing to complete."""
    max_selected_text_chars: int = (16384 - 3072) // 2 * 3
    """If selected text exceeds this, return an empty result.
    The above budget assumes a 16k max context tokens with 3k slack.
    """


@dataclass(frozen=True)
class RemoteEditResult:
    """The result of a remote edit."""

    generated_text: str
    """The generated text."""
    request_id: str
    """The request ID for the remote edit."""

    # TODO(jeff): it's a bit error prone to always have to check this.
    # Maybe replace with some Expected[RemoteEditResult, Error] type.
    filtered_reason: str = ""
    """If not empty, the edit did not pass validation."""


@dataclass
class RemoteEditManager:
    """A helper class for managing edit calls with a remote system."""

    model_client: AugmentModelClient
    """The client to use for the remote system."""

    model_info: Model
    """The model information from the remote system."""

    config: RemoteEditConfig
    """The configuration for the remote system."""

    def edit(
        self, model_input: ResearchEditPromptInput, blobs: BlobsJson
    ) -> RemoteEditResult:
        """Generate a completion for the given model input."""
        assert self.model_info is not None
        assert self.model_client is not None
        if len(model_input.selected_code) > self.config.max_selected_text_chars:
            reason = (
                "selected_text_too_long: "
                f"{len(model_input.selected_code)} > "
                f"{self.config.max_selected_text_chars}"
            )
            return RemoteEditResult(
                generated_text="",
                request_id="",
                filtered_reason=reason,
            )

        # NOTE: prefix_begin and suffix_end are currently only used to detect overlap:
        # https://github.com/augmentcode/augment/blob/main/base/prompt_format_edit/droid_prompt_formatter.py#L170
        prefix_begin = 0
        suffix_end = (
            prefix_begin
            + len(model_input.prefix)
            + len(model_input.selected_code)
            + len(model_input.suffix)
        )

        for retry in range(self.config.indexing_retry_count + 1):
            # AugmentClient.edit expects blob_names, even if empty.
            edit: EditResponse = self.model_client.edit(
                selected_text=model_input.selected_code,
                instruction=model_input.instruction,
                prefix=model_input.prefix,
                suffix=model_input.suffix,
                prefix_begin=prefix_begin,
                suffix_end=suffix_end,
                path=model_input.path,
                blob_names=[],  # deprecated. use blobs instead!
                blobs=blobs,
            )

            if edit.checkpoint_not_found:
                raise ValueError(f"Checkpoint not found: {blobs.checkpoint_id}")

            if not edit.unknown_blob_names:
                return RemoteEditResult(
                    generated_text=edit.text,
                    request_id=edit.request_id,
                )

            # TODO(jeff): Because find_missing doesn't currently support edit hosts, we
            # need to check the edit response and retry if missing blobs are reported.
            # We can remove this once find_missing supports edit hosts.
            if retry == self.config.indexing_retry_count:
                raise ValueError(f"Unknown blob names: {edit.unknown_blob_names}")
            else:
                time.sleep(self.config.indexing_retry_sleep_secs)

        raise AssertionError("Unreachable code")


@dataclass(frozen=True)
class RemoteChatConfig:
    """Config settings for RemoteChatManager."""

    indexing_retry_sleep_secs: float = 20.0
    """DEPRECATED: Use `retry_sleep_secs` instead."""

    indexing_retry_count: int = 0
    """DEPRECATED: Use `retry_count` instead."""

    fail_on_unknown_blobs: bool = True
    """If true, fail if unknown blobs are reported."""

    retry_count: int = 0
    """Number of retries when encountering a client error or unknown blobs."""

    retry_sleep_seconds: float = 0.0
    """Sleep time between tries.

    Sleep time between tries due to e.g. encountering unknown blobs,  due to cache
    timeouts, or certain error codes.
    """

    max_retry_sleep_seconds: float = 3_600.0
    """Maximum sleep time between retries capping the exponential backoff."""

    retry_client_closed_request: bool = False
    """If true, retry on client closure (HTTP 408)."""

    retry_internal_server_error: bool = False
    """If true, retry the chat request if an internal server error is detected."""

    exponential_retry_delay: bool = False
    """If true, use an exponential backoff delay between retries."""


@dataclass
class RemoteChatResult:
    """The result of a chat request to a remote system."""

    generated_text: str
    """The generated text from the chat request."""

    request_id: str
    """The request ID for the chat request."""

    filtered_reason: str = ""
    """An optional reason why the chat response did not pass validation."""

    workspace_file_chunks: list[WorkspaceFileChunk] | None = None
    """The workspace file's context info for the chat result."""

    nodes: list[ChatResultNode] | None = None
    """The nodes of the structured chat response."""


@dataclass
class RemoteChatManager:
    """A helper class for managing chat calls with a remote system."""

    model_client: AugmentModelClient
    """The client to use for the remote system."""

    model_info: Model
    """The model information from the remote system."""

    config: RemoteChatConfig
    """The configuration for the remote system."""

    def chat(
        self,
        model_input: ResearchChatPromptInput,
        blobs: BlobsJson,
        print_unknown_blobs: bool = False,
    ) -> RemoteChatResult:
        """Generate a chat response for the given model input."""
        assert self.model_info is not None
        assert self.model_client is not None

        # Backwards compatibility for indexing_retry_count.
        retry_count = max(self.config.indexing_retry_count, self.config.retry_count)

        # NOTE: prefix_begin and suffix_end are currently only used to detect overlap:
        # https://github.com/augmentcode/augment/blob/main/base/prompt_format_edit/droid_prompt_formatter.py#L170
        prefix_begin = model_input.prefix_begin
        suffix_end = model_input.suffix_end

        # There are two types named Exchange, one in the client and one in the
        # prompt formatter, and they are not the same. Note, we are not
        # populating the request id of the exchange.
        chat_history = [
            Exchange(
                get_request_message_as_text(x.request_message),
                get_response_message_as_text(x.response_text),
            )
            for x in model_input.chat_history
        ]
        for retry in range(retry_count + 1):
            response = None
            try:
                response = self.model_client.chat(
                    selected_code=model_input.selected_code,
                    message=get_request_message_as_text(model_input.message),
                    prefix=model_input.prefix,
                    suffix=model_input.suffix,
                    path=model_input.path,
                    prefix_begin=prefix_begin,
                    suffix_end=suffix_end,
                    blobs=blobs,
                    chat_history=chat_history,
                    user_guided_blobs=model_input.user_guided_blobs,
                    context_code_exchange_request_id=model_input.context_code_exchange_request_id,
                    user_guidelines=model_input.user_guidelines,
                    workspace_guidelines=model_input.workspace_guidelines,
                    support_raw_output=True,
                )
            except ClientException as e:
                if retry < retry_count:
                    if (
                        e.response.status_code == 499
                        and self.config.retry_client_closed_request
                    ):
                        continue
                    elif (
                        e.response.status_code == 500
                        and self.config.retry_internal_server_error
                    ):
                        continue
                raise e

            assert response

            if response.checkpoint_not_found:
                raise ValueError(f"Checkpoint not found: {blobs.checkpoint_id}")

            # Because find_missing doesn't currently support chat hosts, we need
            # to check the response and retry if missing blobs are reported.
            if retry == retry_count and self.config.fail_on_unknown_blobs:
                raise ValueError(
                    f"{len(response.unknown_blob_names)} unknown blob names:"
                    f" {response.unknown_blob_names}"
                )

            if response.unknown_blob_names and print_unknown_blobs:
                print(
                    f"{len(response.unknown_blob_names)} unknown blob names:"
                    f" {response.unknown_blob_names}"
                )

            if not response.unknown_blob_names or retry == retry_count:
                return RemoteChatResult(
                    generated_text=response.text,
                    request_id=response.request_id,
                    workspace_file_chunks=response.workspace_file_chunks,
                    nodes=response.nodes,
                )

            retry_sleep_seconds = (
                self.config.retry_sleep_seconds or self.config.indexing_retry_sleep_secs
            )
            if self.config.exponential_retry_delay:
                retry_sleep_seconds = min(
                    retry_sleep_seconds * 2**retry, self.config.max_retry_sleep_seconds
                )
            time.sleep(retry_sleep_seconds)

        raise AssertionError("Unreachable code")

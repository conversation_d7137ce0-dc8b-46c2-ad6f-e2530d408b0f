"""A NextEditCombinedSystem combines a NextEditLocationSystem and NextEditGenSystem."""

from __future__ import annotations

from collections.abc import Generator
import logging
from dataclasses import dataclass
from typing import AbstractSet, Any, Callable, Collection, Iterable, Optional, Sequence

from tqdm import tqdm

from base.diff_utils.str_diff import precise_char_diff
from base.ranges.range_types import <PERSON>r<PERSON>ang<PERSON>, LineRange
from base.static_analysis.common import check_not_none, shorten_str
from research.core.changes import Changed
from research.core.diff_utils import File
from research.core.next_edit_location_prompt_input import (
    DiffSpan,
    FileLocation,
    NextEditLocationSystemInput,
    ScoredFileHunk,
)
from research.core.types import Document, DocumentId, NTuple
from research.core.utils import FileLogger
from research.eval.harness.systems.abs_system import (
    AbstractSystem,
    register_system,
)
from research.eval.harness.systems.next_edit_gen_system import (
    EditGenSystemInput,
    EditGenSystemOutput,
    NextEditGenSystem,
)
from research.eval.harness.systems.next_edit_location_system import (
    BasicNextEditLocationSystem,
    NextEditLocationOutput,
    SingleFileNextEditLocationSystem,
)
from research.eval.harness.systems.next_edit_reranker_system import (
    NextEditRerankerOutput,
    NextEditRerankerSystem,
)
from research.models.fastforward_models import GenerationCanceledError
from research.next_edits.diagnostics import Diagnostic
from research.next_edits.diff_formatter import format_file_changes
from research.next_edits.edit_gen_formatters import equal_modulo_spaces


@dataclass
class NextEditCombinedSystemInput:
    """The input to NextEditCombined system."""

    instruction: str
    """The user's instruction about the ongoing task/changes."""

    recent_changes: NTuple[Changed[File]]
    """The changes made to the files since the last commit, as a diff string."""

    diagnostics: Sequence[Diagnostic]
    """Diagnostics, with the most recent first."""

    doc_ids: frozenset[DocumentId]
    """The sequence of document ids that are available for retrieval."""

    blocked_locations: frozenset[FileLocation]
    """A set of file locations that should be blocked from being returned."""

    restrict_to_file: str | None
    """If set, will only try to edit the given file path."""

    max_changes_to_return: int
    """The max number of `ChangedFileRegion`s to return in each generate call."""

    max_changes_to_attempt: int | None = None
    """The max number of times that we can call the next edit generation system.

    If not set, we will use the default value of `default_max_changes_to_attempt`.
    """

    selection_begin_char: Optional[int] = None
    """The character offset where the selection region begins from the beginning of the current file."""

    selection_end_char: Optional[int] = None
    """The character offset where the selection region ends from the beginning of the current file."""

    def summary(self, max_str_len: int = 200):
        """Return a shortened summary of the input fields."""

        def shorten(s: str) -> str:
            return repr(shorten_str(s, max_len=max_str_len))

        diff_str = format_file_changes(self.recent_changes, diff_context_lines=3)
        lines = [
            f"instruction: {shorten(self.instruction)}",
            f"num_doc_ids: {len(self.doc_ids)}",
            f"blocked_locations: {len(self.blocked_locations)}",
            f"recent_changes: {shorten(diff_str)}",
            f"num_diagnostics: {len(self.diagnostics)}",
            f"max_changes_to_attempt: {self.max_changes_to_attempt}",
            f"selection_begin_char: {self.selection_begin_char}",
            f"selection_end_char: {self.selection_end_char}",
        ]
        return "\n".join(lines)


@dataclass
class NextEditCombinedSystemStreamedOutput:
    """The output of NextEditCombined system with streaming."""

    generator: Generator[NextEditStreamedEdit, Any, None]


@dataclass
class NextEditStreamedEdit:
    """A single streamed edit result."""

    changed_file_regions: Sequence[ScoredFileHunk]
    """A list of changed file regions annotated with scores.

    The scores are predicted by the next edit location model.
    """

    debug_info: DebugInfo
    """Stores extra information for debugging."""

    def log_to_file(self, file_logger: FileLogger):
        """Log the output via a FileLogger."""
        info = self.debug_info
        blocked_locations_text = "\n".join(str(loc) for loc in info.blocked_locations)

        if isinstance(info.location_output, NextEditRerankerOutput):
            candidate_locations = (
                info.location_output.reranker_debug_info.before_reranking
            )
            reranked_locations = info.location_output.scored_candidates
        else:
            candidate_locations = info.location_output.scored_candidates
            reranked_locations = None

        candidate_locations_text = "\n".join(
            f"location={str(loc.item)}, score={loc.score:.4g}"
            for loc in candidate_locations
        )
        if reranked_locations is None:
            reranked_locations_text = "No reranking"
        else:
            reranked_locations_text = "\n".join(
                f"location={str(r.item)}, score={r.score:.4g}"
                for r in reranked_locations
            )
        changes_found_text = "\n".join(
            f"location={str(scored.file_location)}, "
            f"edit_score={scored.editing_score:.4g}, "
            f"loc_score={scored.localization_score:.4g}, "
            for scored in self.changed_file_regions
        )
        rejected_locations_text = "\n".join(
            f"location={str(scored.location)}, "
            f"edit_score={scored.editing_score:.4g}, "
            f"loc_score={scored.localization_score:.4g}, "
            for scored in info.edit_rejected_locations
        )
        SEP = "~=" * 40
        combined_results_text = (
            f"blocked_locations:\n{blocked_locations_text}\n"
            f"{SEP}\n"
            f"candidate_locations:\n{candidate_locations_text}\n"
            f"{SEP}\n"
            f"reranked_locations:\n{reranked_locations_text}\n"
            f"{SEP}\n"
            f"changes_found:\n{changes_found_text}\n"
            f"{SEP}\n"
            f"rejected_locations:\n{rejected_locations_text}\n"
        )
        file_logger.log("combined_results.txt", combined_results_text)
        info.location_output.log_to_file(file_logger)
        for i, output in enumerate(info.accepted_gen_outputs):
            inner_logger = FileLogger(file_logger.log_dir / f"accepted-{i}")
            output.log_to_file(inner_logger)

    @dataclass
    class DebugInfo:
        """Stores extra information for debugging."""

        location_output: NextEditLocationOutput
        """The output of the location system."""

        accepted_gen_outputs: list[EditGenSystemOutput]
        """The generation system outputs that got accepted."""

        edit_rejected_locations: list[RejectedLocation]
        """The locations that were rejected by the generation system."""

        edit_rejected_gen_outputs: list[EditGenSystemOutput]
        """The generation system outputs that got rejected."""

        blocked_locations: frozenset[FileLocation]
        """The locations that were blocked by the location system."""


@dataclass
class RejectedLocation:
    """A rejected file location."""

    location: FileLocation
    """The location that was rejected."""

    localization_score: float
    """The score of this region according to the location model."""

    editing_score: float
    """The score of this region according to the generation model."""


@register_system("next_edit_combined")
@dataclass
class NextEditCombinedSystem(
    AbstractSystem[NextEditCombinedSystemInput, NextEditCombinedSystemStreamedOutput]
):
    """A system that runs NextEditGen on the results from NextEditLocation."""

    edit_location_system: BasicNextEditLocationSystem | NextEditRerankerSystem
    """The next edit location system."""

    single_file_location_system: SingleFileNextEditLocationSystem
    """Single file location system.

    Based on heuristics instead of models for faster generation.
    """

    edit_gen_system: NextEditGenSystem
    """The next edit generation system."""

    max_changes_to_return: int
    """The max number of `ChangedFileRegion`s to return in each generate call.

    This can be overriden by the `max_changes_to_return` field in the system input.
    """

    default_max_changes_to_attempt: int
    """The max number of times that we can call the next edit generation system.

    This can be overriden by the `max_changes_to_attempt` field in the system input.

    When this limit is hit, even if we have not found `max_changes_to_return` changes,
    we will return the changes we have found so far.
    """

    changes_per_yield: int = 1
    """The number of changes to generate before yielding."""

    location_score_filter: float | None = None
    """If set, will not edit any location with a score below this threshold.

    Leave this unset to only rely on the editing system to reject locations.
    """

    def __post_init__(self):
        if self.max_changes_to_return > self.default_max_changes_to_attempt:
            raise ValueError(
                f"{self.max_changes_to_return=} cannot be greater than "
                f"{self.default_max_changes_to_attempt=}"
            )

    def generate(
        self,
        model_input: NextEditCombinedSystemInput,
        should_cancel: Callable[[], bool] = lambda: False,
    ) -> NextEditCombinedSystemStreamedOutput:
        sys_input = model_input

        # Step 1: run the localization system
        max_changes_to_return = (
            sys_input.max_changes_to_return or self.max_changes_to_return
        )
        max_changes_to_attempt = (
            sys_input.max_changes_to_attempt or self.default_max_changes_to_attempt
        )

        if max_changes_to_return < 0:
            raise ValueError(f"{self.max_changes_to_return=} cannot be negative.")
        if max_changes_to_return > self.default_max_changes_to_attempt:
            raise ValueError(
                f"{max_changes_to_return=} cannot be greater than "
                f"{self.default_max_changes_to_attempt=}"
            )
        if max_changes_to_attempt < 0:
            raise ValueError(f"{max_changes_to_attempt=} cannot be negative.")

        # Create the character range to pass into the location system
        if (begin_char := sys_input.selection_begin_char) and (
            end_char := sys_input.selection_end_char
        ):
            if begin_char > end_char:
                raise ValueError(f"begin_char {begin_char} > end_char {end_char}")
            selected_range = CharRange(begin_char, end_char)
        else:
            selected_range = None

        location_sys_input = NextEditLocationSystemInput(
            current_file=File("", ""),
            edit_region=CharRange(0, 0),
            instruction=sys_input.instruction,
            recent_changes=sys_input.recent_changes,
            doc_ids=sys_input.doc_ids,
            top_k=max_changes_to_attempt,
            diagnostics=tuple(sys_input.diagnostics),
            restrict_to_file=sys_input.restrict_to_file,
            selected_range=selected_range,
        )

        if model_input.restrict_to_file is None:
            location_output = self.edit_location_system.generate(location_sys_input)
            if len(location_output.scored_candidates) < max_changes_to_attempt:
                logging.warning(
                    f"Only found {len(location_output.scored_candidates)} changes, but "
                    f"max_changes_to_attempt is {max_changes_to_attempt}."
                )
        else:
            location_output = self.single_file_location_system.generate(
                location_sys_input
            )
            assert len(location_output.scored_candidates) <= max_changes_to_attempt

        candidate_locations = list(location_output.scored_candidates)

        # filter out blocked locations from the candidate list
        blocked_locations = dict[str, set[LineRange]]()
        for loc in sys_input.blocked_locations:
            if loc.range.is_point():
                logging.error(f"Blocked location is a point: {loc}")
            blocked_locations.setdefault(loc.path, set()).add(loc.range)

        def is_blocked(loc: FileLocation) -> bool:
            if loc.path not in blocked_locations:
                return False
            return any(
                loc.range.overlaps(blocked_range)
                for blocked_range in blocked_locations[loc.path]
            )

        candidate_locations = [
            loc for loc in candidate_locations if not is_blocked(loc.item)
        ]

        candidate_locations.sort(key=lambda x: x.score, reverse=True)
        candidate_locations = candidate_locations[:max_changes_to_attempt]

        # pre-filter locations when not in single-file mode
        if (
            self.location_score_filter is not None
            and model_input.restrict_to_file is None
        ):
            candidate_locations = [
                loc
                for loc in candidate_locations
                if loc.score >= self.location_score_filter
            ]

        all_docs = self.edit_location_system.get_documents(sys_input.doc_ids)
        path_to_doc = {doc.path: doc for doc in all_docs}

        def edit_generator():
            # Step 2: run the editing system
            edit_rejected_locations = list[RejectedLocation]()
            edit_rejected_gen_outputs = list[EditGenSystemOutput]()
            accepted_gen_outputs = list[EditGenSystemOutput]()
            changes_found = list[ScoredFileHunk]()
            num_changes_found = 0

            def get_result():
                if not changes_found:
                    return

                assert len(changes_found) <= max_changes_to_return

                # sort by editing model's score
                changes_found.sort(key=lambda x: x.editing_score, reverse=True)

                return NextEditStreamedEdit(
                    changes_found,
                    NextEditStreamedEdit.DebugInfo(
                        location_output=location_output,
                        accepted_gen_outputs=accepted_gen_outputs,
                        edit_rejected_locations=edit_rejected_locations,
                        edit_rejected_gen_outputs=edit_rejected_gen_outputs,
                        blocked_locations=sys_input.blocked_locations,
                    ),
                )

            for scored_loc in tqdm(candidate_locations, desc="Editing", position=1):
                if should_cancel():
                    raise GenerationCanceledError()
                file_loc = scored_loc.item
                file_doc = path_to_doc[file_loc.path]
                file_lines = file_doc.text.splitlines(keepends=True)
                prefix_lines = file_lines[: file_loc.range.start]
                selected_lines = file_lines[file_loc.range.to_slice()]
                suffix_lines = file_lines[file_loc.range.stop :]

                gen_sys_input = EditGenSystemInput(
                    path=file_loc.path,
                    prefix="".join(prefix_lines),
                    selected_code="".join(selected_lines),
                    suffix="".join(suffix_lines),
                    instruction=sys_input.instruction,
                    recent_changes=sys_input.recent_changes,
                    generate_description=True,
                    doc_ids=sys_input.doc_ids,
                    must_change=False,
                )
                edit_gen_output = self.edit_gen_system.generate(
                    gen_sys_input, should_cancel=should_cancel
                )

                diff = precise_char_diff(
                    gen_sys_input.selected_code, edit_gen_output.replacement
                )
                spans = diff.span_ranges()
                diff_spans = [
                    DiffSpan(original=span[0], updated=span[1]) for span in spans
                ]

                change_found = ScoredFileHunk(
                    file_location=file_loc,
                    original_code=gen_sys_input.selected_code,
                    updated_code=edit_gen_output.replacement,
                    truncation_char=edit_gen_output.truncation_char,
                    localization_score=scored_loc.score,
                    editing_score=check_not_none(edit_gen_output.prob_changed),
                    change_description=edit_gen_output.change_description,
                    diff_spans=diff_spans,
                )
                if equal_modulo_spaces(
                    change_found.updated_code, change_found.original_code
                ):
                    if edit_gen_output.changed:
                        # we reject any pure-whitespace changes
                        logging.warning(
                            f"Rejecting pure whitespace change: {file_loc.path}"
                        )
                    edit_rejected_locations.append(
                        RejectedLocation(
                            location=file_loc,
                            localization_score=scored_loc.score,
                            editing_score=check_not_none(edit_gen_output.prob_changed),
                        )
                    )
                    edit_rejected_gen_outputs.append(edit_gen_output)
                else:
                    changes_found.append(change_found)
                    num_changes_found += 1
                    accepted_gen_outputs.append(edit_gen_output)
                    if len(changes_found) >= self.changes_per_yield:
                        if result := get_result():
                            yield result
                        changes_found = []
                    if num_changes_found >= max_changes_to_return:
                        break
            if result := get_result():
                yield result

        return NextEditCombinedSystemStreamedOutput(edit_generator())

    def add_docs(self, src_files: Collection[Document]):
        if len(src_files) > 1:
            logging.info("Indexing the location system...")
        self.edit_location_system.add_docs(src_files)
        if len(src_files) > 1:
            logging.info("Indexing the single_file location system...")
        self.single_file_location_system.add_docs(src_files)
        if len(src_files) > 1:
            logging.info("Indexing the generation system...")
        self.edit_gen_system.add_docs(src_files)

    def remove_docs(self, doc_ids: Collection[DocumentId]):
        self.edit_location_system.remove_docs(doc_ids)
        self.single_file_location_system.remove_docs(doc_ids)
        self.edit_gen_system.remove_docs(doc_ids)

    def load(self):
        self.edit_location_system.load()
        self.single_file_location_system.load()
        self.edit_gen_system.load()

    def unload(self):
        self.edit_location_system.unload()
        self.single_file_location_system.unload()
        self.edit_gen_system.unload()

    def clear_retriever(self):
        self.edit_location_system.clear_retriever()
        self.single_file_location_system.clear_retriever()
        self.edit_gen_system.clear_retriever()

    def get_doc_ids(self) -> AbstractSet[DocumentId]:
        return self.edit_location_system.get_doc_ids()

    def get_documents(self, doc_ids: Iterable[DocumentId]) -> list[Document]:
        """Return all documents with the given ids indexed by the retriever."""
        return self.edit_location_system.get_documents(doc_ids)

    def get_model(self):
        raise NotImplementedError("Which model to return here?")

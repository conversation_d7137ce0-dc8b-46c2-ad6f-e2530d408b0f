# Eval Harness

The eval harness mainly consists of 3 parts:
- Task (implemented in `tasks`), such HydraTask and HumanEval.
- System (implemented in `systems`), where each system can call the `generate` function to generate a code completion.
- EvalHarness (implemented in `harness.py`) as a wrapper to create system and task and evaluate a system over all data in a task.

Other utilities:
- `factories.py`: multiple functions to convert a dict-based config into concrete objects for Model, Retrieval, System, etc.
- `launch_harness.py`: the entry point to run an evaluation experiment.
- `config_sweeper.py`: the entry point to generate many yaml configs based on sweeping over the Cartesian product of interventions.

"""The Next Edit Location eval task."""

import copy
import dataclasses
import logging
from collections import defaultdict
from dataclasses import asdict, dataclass, replace
from pathlib import Path
from typing import Optional, Protocol, Self, Sequence, Union, runtime_checkable

from base.diff_utils.edit_events import GranularEditEvent
from base.ranges.line_map import LineMap
import torch
import unidiff
from dataclasses_json import DataClassJsonMixin

from base.diff_utils.diff_utils import File
from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>, LineRange
from base.static_analysis.common import groupby
from research.core import diff_utils
from research.core.next_edit_location_prompt_input import (
    FileLocation,
    NextEditLocationLabel,
    NextEditLocationOutput,
    NextEditLocationSystemInput,
)
from research.core.types import Document, DocumentId, Scored
from research.eval.harness import utils
from research.eval.harness.systems.abs_system import AbstractSystem
from research.eval.harness.systems.next_edit_reranker_system import (
    NextEditRerankerOutput,
    NextEditRerankerSystem,
)
from research.eval.harness.tasks.abs_task import AbstractTask

# TODO(arun): We should probably move these metrics to a central location.
from research.fastbackward.retrieval_models import (
    RankingMetrics,
    compute_ranking_metrics_from_ranking,
)
from research.next_edits.next_edits_dataset import Datum, GroupedDatum
from research.retrieval.retrieval_database import RetrievalDatabase
from research.utils.dataclass_utils import fromdict
from research.utils.repo_change_utils import FileTuple, repo_change_from_patchset

logger = logging.getLogger(__name__)


# Default data paths. This is used mostly for testing, please set paths in all your
# evaluation configurations.
_DIFFS_PATH = Path("/mnt/efs/augment/data/eval/next_edits/manual.v3.diffs.jsonl.zst")
_FILES_PATH = Path("/mnt/efs/augment/data/eval/next_edits/manual.v3.files.jsonl.zst")


RankingMetricsByK = dict[int, RankingMetrics]
"""A mapping from top-k to the metrics."""


@dataclass(frozen=True)
class TaskInput(DataClassJsonMixin):
    """The dataset for the next edit location eval task."""

    repo_name: str
    """The repo name."""

    group_id: str
    """The group id."""

    group_sequence_id: int
    """The sequence id within the group."""

    prompt: NextEditLocationSystemInput
    """The prompt."""

    documents: list[Document]
    """The documents."""

    id: str = ""
    """The id of the example."""


@dataclass
class Output(NextEditLocationOutput):
    """The output of the next edit location task."""

    metrics: RankingMetrics = dataclasses.field(default_factory=RankingMetrics)
    """The metrics for the output."""

    metrics_by_k: RankingMetricsByK = dataclasses.field(
        default_factory=lambda: defaultdict(RankingMetrics)
    )
    """The metrics for the output."""

    scored_candidate_labels: Sequence[bool] = dataclasses.field(default_factory=list)
    """Labels for whether or not the candidate locations were correct."""

    # NOTE(arun): Adding default fields for backwards compatibility, but these fields
    # must be set to correlate output with input data.
    repo_name: str = ""
    """The repo name."""

    group_id: str = ""
    """The group id."""

    group_sequence_id: int = 0
    """The sequence id within the group."""

    id: str = ""
    """The id of the example."""


@dataclass
class OutputMetrics(DataClassJsonMixin):
    """The metrics for the output."""

    artifact: str

    macro_metrics: RankingMetricsByK
    """The macro metrics."""

    micro_metrics: RankingMetricsByK
    """The micro metrics."""

    per_group_metrics: dict[str, RankingMetricsByK]
    """The per group metrics."""


def compute_metrics(
    output: NextEditLocationOutput,
    gold_locations: Sequence[FileLocation],
    top_k: int,
    all_candidate_locations: Sequence[FileLocation] = (),
) -> tuple[RankingMetrics, list[bool]]:
    assert gold_locations, "Must have at least one gold location for metrics."
    path_to_locations = groupby(gold_locations, lambda x: x.path)

    def is_correct(loc: FileLocation) -> bool:
        return any(
            loc.range.intersect(gold_loc.range) is not None
            for gold_loc in path_to_locations.get(loc.path, [])
        )

    # Get the total number of gold examples
    total_gold_count = sum(
        [is_correct(location.item) for location in output.scored_candidates]
    )

    scored_candidates = output.scored_candidates[:top_k]

    # Get all non-scored candidates that are correct so that we can put them at the
    # end of the list of candidates as having the "maximum" penalty.
    seen_correct_candidates = {
        loc.item for loc in scored_candidates if is_correct(loc.item)
    }
    missing_correct_count = min(
        sum(
            1
            for loc in all_candidate_locations
            if loc not in seen_correct_candidates and is_correct(loc)
        ),
        # Limit the number of missing correct candidates to the number of slots left in
        # the top-k: if all the slots are full of correct candidates, we shouldn't be
        # penalized for not finding more!
        top_k - len(seen_correct_candidates),
    )

    pred_logits_K = torch.tensor(
        [location.score for location in scored_candidates],
        dtype=torch.float,
    )
    pred_ranking_K = pred_logits_K.argsort(dim=-1, descending=True)
    pred_lprobs_K = torch.log_softmax(pred_logits_K, dim=-1)
    labels_K = torch.tensor(
        [is_correct(location.item) for location in scored_candidates]
    )
    # We couldn't find any gold labels that intersect with the candidate locations.
    # We'll return some really sad 0 metrics.
    if not labels_K.any():
        return RankingMetrics(
            mean_rr=0.0,
            mean_ap=0.0,
            # Mark all these as being ranked as the *very* bottom.
            mean_rank=top_k + 1.0,
            mean_top_rank=top_k + 1.0,
            mean_bottom_rank=top_k + 1.0,
            mean_p1=0.0,
            mean_gold_prob=0.0,
            mean_recall=0.0,
            mean_hard_recall=0.0,
            count=1,
        ), labels_K.tolist()

    ranking_metrics = compute_ranking_metrics_from_ranking(
        pred_lprobs_K,
        pred_ranking_K,
        labels_K,
        missing_correct_count=missing_correct_count,
        total_gold_count=total_gold_count,
    )

    return ranking_metrics, labels_K.tolist()


NextEditLocationSystem = AbstractSystem[
    NextEditLocationSystemInput, NextEditLocationOutput
]


class NextEditLocationEvalTask(
    AbstractTask[NextEditLocationSystemInput, NextEditLocationOutput]
):
    """The Next Edit Location eval task."""

    def __init__(
        self,
        diffs_path: Path | str = _DIFFS_PATH,
        files_path: Path | str = _FILES_PATH,
        limit_examples: Optional[int] = None,
        limit_of_num_files_in_repo: Optional[int] = None,
        top_ks: Sequence[int] = (3, 8, 32, 256),
        drop_instructions: bool = False,
    ):
        """Create the task.

        Args:
            diffs_path: The path to the diffs part of the dataset.
            files_path: The path to the files part of the dataset.
            limit_examples: The maximum number of examples to load.
            limit_of_num_files_in_repo: The maximum number of files in a repo, if a repo
                has more than this number of files, the corresponding repo and examples
                will be ignored.
            top_ks: The number of candidates to generate and score.
            drop_instruction: Whether to drop the instruction and instruction-only
                samples.
        """
        assert top_ks, "Must have at least one top-k value."
        self._data_path = diffs_path
        self._files_path = files_path

        self._limit_examples = limit_examples
        self._limit_of_num_files_in_repo = limit_of_num_files_in_repo
        self._top_ks = tuple(sorted(top_ks))
        self._drop_instructions = drop_instructions

    def _load_dataset(self) -> list[TaskInput]:
        typed_dataset = load_dataset(
            self._data_path, self._files_path, self._limit_examples
        )
        if self._limit_of_num_files_in_repo:
            original_len = len(typed_dataset)
            typed_dataset = [
                dataset
                for dataset in typed_dataset
                if len(dataset.wip_files) <= self._limit_of_num_files_in_repo
            ]
            logger.info(
                f"Limited the dataset to {len(typed_dataset)} examples from {original_len} examples."
            )
        if self._limit_examples:
            original_len = len(typed_dataset)
            typed_dataset = typed_dataset[: self._limit_examples]
            logger.info(
                f"Limited the dataset to {len(typed_dataset)} examples from {original_len} examples."
            )
        return [
            task
            for datum in typed_dataset
            if (
                task := convert_to_task_input(
                    datum, drop_instructions=self._drop_instructions
                )
            )
            is not None
        ]

    def run(
        self,
        system: NextEditLocationSystem,
        output_path: Union[str, Path],
        output_prefix: str = "",
    ) -> dict:
        """Run the task against the provided system."""
        dataset = self._load_dataset()

        output_path = Path(output_path)
        output_path.mkdir(exist_ok=True, parents=True)
        logger.info(f"Save results into {output_path}")

        files = set[DocumentId]()
        outputs = list[Output]()

        micro_metrics = {k: RankingMetrics(count=0) for k in self._top_ks}
        per_group_metrics = defaultdict(
            lambda: {k: RankingMetrics(count=0) for k in self._top_ks}
        )
        for i, datum in enumerate(dataset):
            for k in micro_metrics:
                logger.info(f"{i}/{len(dataset)} (K={k})): {micro_metrics[k]}")
            cur_files = {doc.id: doc for doc in datum.documents}
            system.add_docs(
                [cur_files[doc_id] for doc_id in (cur_files.keys() - files)]
            )
            files.update(cur_files.keys())

            is_reranker_system = isinstance(system, NextEditRerankerSystem)
            if is_reranker_system:
                logger.info("Running with reranker system.")
                # For reranker system, we want to rerank the top_k locations and then just grab the rest from before_reranking.
                assert system.record_all_locations, "We need to record all locations to run this eval with a reranker, in order to compute recall."
                prompt = dataclasses.replace(datum.prompt, top_k=max(self._top_ks))
                assert isinstance(prompt, NextEditLocationSystemInput), type(prompt)
                result = system.generate(prompt)
                before_reranking = result.reranker_debug_info.before_reranking
                result.scored_candidates = list(result.scored_candidates) + list(
                    before_reranking[len(result.scored_candidates) :]
                )
            else:
                logger.info("Running without reranker system.")
                # For non-reranker system, we want to grab all locations, so that we can compute recall.
                prompt = dataclasses.replace(datum.prompt, top_k=None)
                assert isinstance(prompt, NextEditLocationSystemInput), type(prompt)
                result = system.generate(prompt)

            assert datum.prompt.label is not None
            if len(datum.prompt.label.locations) == 0:
                # Skip examples with no gold labels
                continue
            all_candidate_locations = get_all_candidate_locations(
                system, prompt.doc_ids
            )
            metrics_by_k, scored_candidate_labels = {}, []
            for top_k in self._top_ks:
                metrics, scored_candidate_labels = compute_metrics(
                    result,
                    datum.prompt.label.locations,
                    top_k,
                    all_candidate_locations=all_candidate_locations,
                )
                metrics_by_k[top_k] = metrics
                per_group_metrics[datum.group_id][top_k].update(metrics)
                micro_metrics[top_k].update(metrics)

            outputs.append(
                Output(
                    scored_candidates=result.scored_candidates,
                    metrics=metrics_by_k[max(self._top_ks)],
                    metrics_by_k=metrics_by_k,
                    scored_candidate_labels=scored_candidate_labels,
                    debug_info=result.debug_info,
                    repo_name=datum.repo_name,
                    group_id=datum.group_id,
                    group_sequence_id=datum.group_sequence_id,
                    id=datum.id,
                )
            )
        logger.info(f"{len(dataset)}/{len(dataset)}: {micro_metrics[32]}")

        # Compute macro-averaged metrics by averaging over each group's average.
        macro_metrics = {k: RankingMetrics(count=0) for k in self._top_ks}
        for group_metrics in per_group_metrics.values():
            for k, group_metrics_k in group_metrics.items():
                macro_metrics[k].update(replace(group_metrics_k, count=1))

        out_file = self.write_output_file(outputs, output_path, output_prefix)
        metrics = tuple(map(lambda x: x.metrics, outputs))
        return {
            "artifact": str(out_file),
            "micro_metrics": {
                top_k: metrics.to_dict() for top_k, metrics in micro_metrics.items()
            },
            "macro_metrics": {
                top_k: metrics.to_dict() for top_k, metrics in macro_metrics.items()
            },
            "per_group_metrics": {
                group_id: {
                    top_k: metrics.to_dict() for top_k, metrics in group_metrics.items()
                }
                for group_id, group_metrics in per_group_metrics.items()
            },
        }

    def write_output_file(
        self,
        output: Sequence[Output],
        output_path: Path,
        output_prefix: str,
    ) -> Path:
        if output_path.exists():
            file_name = f"next_edit_location_{self.name}"
            out_file = Path(
                output_path / f"{output_prefix}{file_name}_output.jsonl.zst"
            )
            logger.info(f"Writing results to {out_file}")

            recs = [asdict(doc) for doc in output]
            utils.write_jsonl_zst(out_file, recs)
            return out_file
        return Path("")

    @classmethod
    def from_yaml_config(cls, config: dict) -> Self:
        """Returns a NextEditLocationEvalTask constructed using a config dictionary."""
        config = copy.copy(config)
        if "dataset_path" in config:
            dataset_path = config.pop("dataset_path")
            assert ".jsonl.zst" in dataset_path
            prefix = dataset_path.split(".jsonl.zst")[0]
            config["diffs_path"] = prefix + ".diffs.jsonl.zst"
            config["files_path"] = prefix + ".files.jsonl.zst"
        return cls(**config)

    def __len__(self) -> int:
        raise NotImplementedError()

    def execute(
        self,
        model_input: NextEditLocationSystemInput,
        generation: str,
        timeout: Optional[float] = None,
    ) -> dict:
        raise NotImplementedError()

    def __getitem__(self, index: int):
        raise NotImplementedError()


def compute_modified_ranges(
    wip_repo: diff_utils.Repository,
    future_repo: diff_utils.Repository,
    ignore_whitespace: bool = True,
    num_context_lines: int = 0,
) -> list[FileLocation]:
    """Get the label locations for a next edit datum.

    Note that when num_context_lines is not 0, the reported line range will include the
    diff context lines.
    """

    # We need to compute a 0 context diff to include exactly the line ranges that have
    # been changed as positive labels.
    diff0 = diff_utils.compute_repo_diff(
        wip_repo,
        future_repo,
        num_context_lines=num_context_lines,
        ignore_whitespace=ignore_whitespace,
    )
    # Expressing this as a for-loop to add type annotations.
    labels = []
    for file in diff0:
        file: unidiff.PatchedFile
        if file.is_added_file:
            # Files added in the future don't exist in the WIP repo and don't have
            # locations to report.
            continue
        # NOTE: we'll still report the past locations for files that were moved or
        # deleted.

        # When a file is moved `file.path` gets the path to the new file, but we want
        # paths in the original file. `file.source_file` includes the before path
        # (a/name), so we need to strip it here.
        source_path = file.source_file
        # TODO(arun): We should either move this function into diff_utils or make
        # `_BEFORE_PATH` a public constant.
        if source_path.startswith(f"{diff_utils._BEFORE_PATH}/"):
            source_path = source_path[len(diff_utils._BEFORE_PATH) + 1 :]

        for hunk in file:
            hunk: unidiff.Hunk

            labels.append(
                FileLocation(
                    path=source_path,
                    range=LineRange(
                        # Hunk lines start from 1, but line ranges start at 0.
                        # However, when lines are added at the top of the file, they
                        # start at 1. We consider this to be a change at line 0, and
                        # that is safe for our evaluation purposes.
                        max(0, hunk.source_start - 1),
                        max(0, hunk.source_start + hunk.source_length - 1),
                    ),
                )
            )

    return labels


def get_label_locations(
    datum: Datum, ignore_whitespace: bool = True
) -> list[FileLocation]:
    """Get the label locations for a next edit datum."""

    # We need to compute a 0 context diff to include exactly the line ranges that have
    # been changed as positive labels.
    wip_repo = diff_utils.Repository(files=datum.wip_files)
    future_repo = diff_utils.apply_diff(
        wip_repo, diff_utils.parse_git_diff_output(datum.wip_to_future_diff)
    )
    return compute_modified_ranges(wip_repo, future_repo, ignore_whitespace)


def convert_to_task_input(
    datum: Datum,
    exclude_whitespace_only_locations: bool = True,
    drop_instructions: bool = False,
) -> TaskInput | None:
    """Converts a next edit data point into next edit location task input."""
    try:
        locations = tuple(get_label_locations(datum, exclude_whitespace_only_locations))

        if drop_instructions and not datum.past_to_wip_diff:
            return None

        documents = [
            Document.new(text=file.contents, path=file.path) for file in datum.wip_files
        ]

        repo_change = repo_change_from_patchset(
            diff_utils.Repository(files=datum.wip_files),
            diff_utils.parse_git_diff_output(datum.past_to_wip_diff),
            reverse=True,
        )
        recent_changes = tuple(
            change.map(FileTuple.to_file) for change in repo_change.changed_files
        )

        prompt = NextEditLocationSystemInput(
            instruction=datum.instruction if not drop_instructions else "",
            recent_changes=recent_changes,
            label=NextEditLocationLabel(
                locations=locations,
            ),
            current_file=File("", ""),
            edit_region=CharRange(0, 0),
            past_diff=datum.past_to_wip_diff,
            future_diff=datum.wip_to_future_diff,
            doc_ids=frozenset([doc.id for doc in documents]),
        )

        return TaskInput(
            datum.commit_meta.repo_name if datum.commit_meta else "",
            _sanitize_key(datum.group_id),
            datum.group_sequence_id,
            prompt=prompt,
            documents=documents,
            id=datum.id,
        )
    except diff_utils.CommandFailedError:
        # This can happen if the diff fails to apply.
        return None


def load_dataset(
    diffs_path: str | Path, files_path: str | Path, limit: int | None = None
) -> list[Datum]:
    """Load the dataset from the given path.

    The structure of the dataset is:
    - diffs.jsonl.zst: a list of dicts, each dict is a Datum with the `wip_files` field
      replaced with a list of ids into the files object above.
    - files.jsonl.zst: a list of dicts, each dict is a File with an `id`.

    As files are highly redundant across samples, we store them separately. This
    function "re-hydrates" the wip_files from the files above.

    Args:
        diffs_path: Path to the "diffs.jsonl.zst" part of the dataset.
        files_path: Path to the "files.jsonl.zst" part of the dataset.
        limit: If set, only load this many examples.

    Returns:
        A dataset of examples for the next edit location task.
    """
    files_path = Path(files_path)
    if files_path.suffix == ".zst":
        files = utils.read_jsonl_zst(files_path)
    else:
        files = utils.read_jsonl(files_path)
    files = {file.pop("id"): file for file in files}

    diffs_path = Path(diffs_path)
    if diffs_path.suffix == ".zst":
        raw_dataset = utils.read_jsonl_zst(diffs_path, limit)
    else:
        raw_dataset = utils.read_jsonl(diffs_path, limit)

    # Re-hydrate the files.
    for grouped_datum in raw_dataset:
        for datum in grouped_datum["data"]:
            datum["wip_files"] = [files[id_] for id_ in datum["wip_files"]]

    data: list[Datum] = [
        datum
        for grouped_datum in raw_dataset
        for datum in GroupedDatum.schema().load(grouped_datum).data
    ]

    return data


def load_output(output_path: Path, limit: int | None = None) -> list[Output]:
    """Load the output of the next edit location task from a given path.

    This can be useful when parsing output for error analysis in a notebook, e.g.

    Args:
        output_path: the path to the output.

    Returns:
        A list of parsed next edit location outputs.
    """
    if output_path.is_dir():
        output_path = next(output_path.glob("*.jsonl.zst"))

    raw_dataset = utils.read_jsonl_zst(output_path, limit)
    for datum in raw_dataset:
        if "all_candidates" in datum:
            del datum["all_candidates"]
        if "candidate_labels" in datum:
            datum["scored_candidate_labels"] = datum.pop("candidate_labels")

    dataset = [fromdict(Output, **datum) for datum in raw_dataset]
    # For reasons, fromdict doesn't parse the locations.
    for datum in dataset:
        datum.scored_candidates = [
            Scored[FileLocation](
                FileLocation(
                    path=location["item"]["path"],  # type: ignore
                    range=LineRange(
                        location["item"]["range"]["start"],  # type: ignore
                        location["item"]["range"]["stop"],  # type: ignore
                    ),
                ),
                location["score"],  # type: ignore
            )
            for location in datum.scored_candidates
        ]
    return dataset


@runtime_checkable
class HasRetriever(Protocol):
    """A protocol for systems that have a retriever.

    This is a hack to extract all the candidate locations for accurate metrics from
    systems.
    """

    _retriever: RetrievalDatabase


def get_all_candidate_locations(
    system: NextEditLocationSystem, doc_ids: frozenset[str]
) -> list[FileLocation]:
    """Gets all candidate locations for a given set of doc_ids."""
    assert isinstance(system, HasRetriever)

    # Lists all possible candidates, used to better compute metrics.
    return [
        FileLocation(chunk.path, chunk.line_range)
        for doc in system._retriever.documents.values()
        if doc.doc.id in doc_ids
        for chunk in doc.chunk_blobs.values()
        if chunk.path
    ]


def _sanitize_key(key: str) -> str:
    return key.replace(".", "_")

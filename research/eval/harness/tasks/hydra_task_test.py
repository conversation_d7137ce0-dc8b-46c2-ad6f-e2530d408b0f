"""Unit test for HydraTask.

pytest research/eval/tests/tasks/test_hydratask.py
"""

import unittest

from mock import patch

from research.eval.harness import tasks
from research.eval.tests.hydra import mock_k8s_utils


class TestHydra(unittest.TestCase):
    """Test __getitem__ and execute functions."""

    def test_core(self):
        task = tasks.HydraTask()
        # Additional args passed into the driver.Driver() call in the task
        task.additional_driver_args = {
            "max_containers_per_pod": 1,
            "max_queue_latency_sec": 0.1,
            "queue_sleep_sec": 0.0,
        }
        mock_k8 = mock_k8s_utils.MockK8("succeeded")
        # Patch the throttle to be a no-op, otherwise this test will be very slow.
        # The k8s_utils and docker patches ensure we aren't launching pods.
        with (
            patch(
                "research.eval.hydra.driver.Driver.LaunchThrottle.throttle",
                new=lambda x: None,
            ),
            patch(
                "research.eval.hydra.driver.Driver._generate_guid",
                new=lambda x: "mock-container",
            ),
            patch(
                "research.eval.hydra.driver.k8s_utils",
                new=mock_k8,
            ),
            patch(
                "research.eval.hydra.driver._provider",
                new=mock_k8s_utils.mock_provider(),
            ),
        ):
            for index, (inputs, _) in enumerate(task):
                target_completion = inputs.target
                if target_completion is None:
                    raise ValueError(
                        f"{task.name}[{index}] should have non-empty target."
                    )
                results = task.execute(inputs, target_completion)
                assert results["pass"], f"{task.name}[{index}] should pass."

import json
from pathlib import Path
from typing import Optional, Union

import yaml

from research.core import utils_for_str
from research.core.chat_prompt_input import ResearchChatPromptInput
from research.core.model_input import ChatInput, ModelInput
from research.eval.harness.inficoder_utils import grade_responses
from research.eval.harness.systems.abs_system import AbstractSystem
from research.eval.harness.tasks.abs_task import AbstractTask


def sample_to_model_input(sample: dict) -> ResearchChatPromptInput:
    return ResearchChatPromptInput(
        message=sample["content_prompt"],
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
    )


class InfiCoderEvalTask(AbstractTask):
    """Evaluation task for the InfiCoder benchmark."""

    def __init__(self, dataset_path: str):
        with open(dataset_path) as f:
            samples = json.load(f)

        self.samples = samples
        self.config_dir = Path(dataset_path).parent

    def run(
        self,
        system: AbstractSystem,
        output_path: Union[str, Path],
        output_prefix: str = "",
    ) -> dict:
        output_dir = (
            Path(output_path)
            / f"{output_prefix}_{system.name}_{utils_for_str.get_random_str(4)}"
        )
        output_dir.mkdir(exist_ok=True)
        cache_dir = output_dir / "cache"
        cache_dir.mkdir(exist_ok=True, parents=False)

        grades = []
        for idx, sample in enumerate(self.samples):
            model_input = sample_to_model_input(sample)
            generation = system.generate(model_input)

            with open(self.config_dir / sample["filename"]) as f:
                sample_config = yaml.load(f, yaml.Loader)

            grade = grade_responses(
                sample_config,
                str(self.config_dir / "cases_dev"),
                [generation.generated_text],
                "avg",
                1.0,
                0.0,
            )
            grades.append(grade)

            info_to_log = {
                "sample": sample,
                "generated_text": generation.generated_text,
                "grad": grade,
                "config": sample_config,
            }
            with open(cache_dir / f"{idx}.json", "w") as f:
                json.dump(info_to_log, f, indent=2)

        return self.compute_metrics(grades)

    def compute_metrics(self, grades):
        metric = sum(map(lambda g: g[0], grades))
        return {
            "metric": metric,
            "total_samples": len(grades),
        }

    @classmethod
    def from_yaml_config(cls, config: dict) -> "InfiCoderEvalTask":
        """Returns ChatEvalTask object constructed using a config dictionary."""
        kwargs = {"dataset_path": config["dataset_path"]}
        return cls(**kwargs)

    def __getitem__(self, index: int):
        return self.samples[0]

    def __len__(self) -> int:
        return len(self.samples)

    def execute(
        self,
        model_input: ModelInput,
        generation: str,
        timeout: Optional[float] = None,
    ) -> dict:
        raise NotImplementedError()

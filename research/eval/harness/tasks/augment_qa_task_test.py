import json
import pytest
from textwrap import dedent
import shutil
import tempfile
from research.eval.harness import utils
from pathlib import Path
import zstandard as zstd
from research.eval.harness.systems.gold_system import GoldChatSystem
from research.eval.harness.tasks.augment_qa_task import (
    AugmentQATask,
    count_keywords,
)


@pytest.fixture
def test_dataset_path(tmpdir):
    dataset_path = Path(tmpdir) / "test_dataset"
    repo_dir = dataset_path / "repos"
    repo_dir.mkdir(parents=True, exist_ok=True)
    files = [
        {
            "id": "foo_py",
            "text": dedent("""
                # foo.py
                def greet(name: str) -> None:
                    print(f"Hello, {name}!")

                greet("Alice")
            """),
            "path": "foo.py",
        },
        {
            "id": "bar_cpp",
            "text": dedent("""
                // bar.cpp
                #include <iostream>

                int main() {
                    std::cout << "Hello, World!" << std::endl;
                    return 0;
                }
            """),
            "path": "bar.cpp",
        },
    ]

    with zstd.open(repo_dir / "test_repo.jsonl.zst", "w", encoding="utf-8") as f:
        for file in files:
            json.dump(file, f)
            f.write("\n")

    samples_json = dataset_path / "samples.json"
    with open(samples_json, "w") as f:
        json.dump(
            [
                {
                    "repo": "test_repo",
                    "message": "What is the purpose of the foo.py file?",
                    "target": dedent("""
                    The foo.py file contains a Python function `greet` that prints a greeting message.
                """),
                    "keywords": ["foo.py", "Python"],
                    "gold_paths": ["foo.py"],
                },
                {
                    "repo": "test_repo",
                    "message": "What is the purpose of the bar.cpp file?",
                    "selected_code": dedent("""\
                        int main() {
                            printf("Hello, World!");
                            return 0;
                        }
                    """),
                    "target": dedent("""
                    The bar.cpp file contains a C++ program that prints "Hello, World!" to the console.
                """),
                    "keywords": ["bar.cpp", "C++"],
                    "gold_paths": ["bar.cpp"],
                },
            ],
            f,
            indent=2,
        )

    return dataset_path


def test_fixture(test_dataset_path):
    """Test that the test_repo fixture works."""
    assert (test_dataset_path / "repos/test_repo.jsonl.zst").exists()
    assert (test_dataset_path / "samples.json").exists()


def test_augment_qa_init(test_dataset_path):
    """Test that the task can be initialized."""
    config = {"dataset_path": test_dataset_path}
    task = AugmentQATask.from_yaml_config(config)
    assert len(task) > 0


def test_augment_qa_run(test_dataset_path):
    """Test that the task can be run."""
    config = {"dataset_path": test_dataset_path}
    task = AugmentQATask.from_yaml_config(config)
    system = GoldChatSystem()  # gold chat system object

    output_path = tempfile.mkdtemp(prefix="test_output_")
    result = task.run(system, output_path)
    assert "metrics" in result
    assert result["metrics"]["answer_keyword_recall"] == 1.0
    assert result["metrics"]["samples"] > 0
    assert result["metrics"]["samples"] == len(task)
    assert result["run_duration"] > 0
    assert result["start_time"] > 0

    samples = utils.read_jsonl_zst(Path(result["artifact"]) / "samples.jsonl.zst")
    assert result["metrics"]["samples"] == len(samples)
    shutil.rmtree(output_path)


def test_augment_qa_run_with_html_report(test_dataset_path):
    """Test that the task can be run."""
    html_report_dir = tempfile.mkdtemp(prefix="test_html_report_")
    config = {
        "dataset_path": test_dataset_path,
        "html_report_output_dir": html_report_dir,
    }
    task = AugmentQATask.from_yaml_config(config)
    system = GoldChatSystem()  # gold chat system object

    output_path = tempfile.mkdtemp(prefix="test_output_")
    result = task.run(system, output_path)
    assert "metrics" in result
    assert result["metrics"]["answer_keyword_recall"] == 1.0
    assert result["metrics"]["samples"] > 0
    assert result["metrics"]["samples"] == len(task)
    assert result["run_duration"] > 0
    assert result["start_time"] > 0

    samples = utils.read_jsonl_zst(Path(result["artifact"]) / "samples.jsonl.zst")
    assert result["metrics"]["samples"] == len(samples)
    shutil.rmtree(output_path)

    html_report_path = Path(result["html_report_path"])
    assert html_report_path.exists()
    assert html_report_path.is_relative_to(html_report_dir)
    shutil.rmtree(html_report_dir)


def test_augment_qa_run_with_doc_ids(test_dataset_path):
    """Test that the task can be run with doc_ids specified in samples."""
    config = {"dataset_path": test_dataset_path}

    # Modify samples.json to include doc_ids
    samples_json = test_dataset_path / "samples.json"
    with open(samples_json, "r") as f:
        samples = json.load(f)

    # Add doc_ids to first sample, leave second sample without doc_ids
    samples[0]["doc_ids"] = ["foo_py"]

    with open(samples_json, "w") as f:
        json.dump(samples, f, indent=2)

    task = AugmentQATask.from_yaml_config(config)
    system = GoldChatSystem()

    output_path = tempfile.mkdtemp(prefix="test_output_")
    result = task.run(system, output_path)

    assert "metrics" in result
    assert result["metrics"]["samples"] == len(task)

    shutil.rmtree(output_path)


def test_augment_qa_run_with_empty_doc_ids(test_dataset_path):
    """Test that the task raises an error when empty doc_ids are provided."""
    config = {"dataset_path": test_dataset_path}

    # Modify samples.json to include empty doc_ids
    samples_json = test_dataset_path / "samples.json"
    with open(samples_json, "r") as f:
        samples = json.load(f)

    samples[0]["doc_ids"] = []

    with open(samples_json, "w") as f:
        json.dump(samples, f, indent=2)

    task = AugmentQATask.from_yaml_config(config)
    system = GoldChatSystem()

    output_path = tempfile.mkdtemp(prefix="test_output_")
    with pytest.raises(
        ValueError, match="If `doc_ids` is provided, it must be non-empty."
    ):
        task.run(system, output_path)

    shutil.rmtree(output_path)


def test_single_keyword():
    text = "This is a sample text with Python."
    keywords = ["Python"]
    assert count_keywords(text, keywords) == 1


def test_multiple_keywords():
    text = "This is a sample text with Python, coding, and fun."
    keywords = ["Python", "coding", "fun"]
    assert count_keywords(text, keywords) == 3


def test_case_sensitive():
    text = "This is a sample text with python."
    keywords = ["Python"]
    assert count_keywords(text, keywords) == 0


def test_no_keywords():
    text = "This is a sample text with no keywords."
    keywords = ["Python", "coding", "fun"]
    assert count_keywords(text, keywords) == 0


def test_keyword_appears_twice():
    text = "This is a sample text with Python, and Python is fun."
    keywords = ["Python"]
    assert count_keywords(text, keywords) == 1


def test_empty_text():
    text = ""
    keywords = ["Python", "coding", "fun"]
    assert count_keywords(text, keywords) == 0


def test_empty_keywords():
    text = "This is a sample text with Python, coding, and fun."
    keywords = ["   "]  # keyword with only whitespace
    with pytest.raises(ValueError):
        count_keywords(text, keywords)


def test_text_with_only_punctuation():
    text = "!,.@#$%^&*()"
    keywords = ["Python", "coding", "fun"]
    assert count_keywords(text, keywords) == 0


def test_keywords_with_only_punctuation():
    text = "This is a sample text with Python, coding, and fun."
    keywords = ["!", ".", "@#$%^&*()"]
    assert count_keywords(text, keywords) == 0


def test_text_with_non_ascii_characters():
    text = "This is a sample text with Python, coding, and fun café."
    keywords = ["Python", "coding", "fun"]
    assert count_keywords(text, keywords) == 3


def test_text_with_slashes():
    text = "The answer is located in `a/b/c.py` file."
    assert count_keywords(text, ["a"]) == 1
    assert count_keywords(text, ["b"]) == 1
    assert count_keywords(text, ["c"]) == 1
    assert count_keywords(text, ["c.py"]) == 1
    assert count_keywords(text, ["b/c.py"]) == 1
    assert count_keywords(text, ["c.", "c.p", "d"]) == 0


def test_multiple_texts():
    texts = ["This is a sample text with Python.", "Another text with coding and fun."]
    keywords = ["Python", "coding", "fun"]
    assert count_keywords(texts, keywords) == 3


def test_multiple_texts_with_duplicates():
    texts = ["This is a sample text with Python.", "Another text with Python and fun."]
    keywords = ["Python", "coding", "fun"]
    assert count_keywords(texts, keywords) == 2


def test_multiple_texts_with_no_keywords():
    texts = ["This is a sample text with Java.", "Another text with JavaScript."]
    keywords = ["Python", "coding", "fun"]
    assert count_keywords(texts, keywords) == 0


def test_empty_list_of_texts():
    texts = []
    keywords = ["Python", "coding", "fun"]
    assert count_keywords(texts, keywords) == 0


def test_count_keywords_that_are_prefix_of_each_other():
    text = """\
Based on the provided excerpt from `research/model_server/launch_model_server.py`, the following endpoints are registered in the research model server:

- `/get-models`
- `/get-models-api-auth`

Note that some of these endpoints have both authenticated and unauthenticated versions (e.g., `/get-models` and `/get-models-api-auth`).
"""
    keywords = ["/get-models", "/get-models-api-auth"]
    assert count_keywords(text, keywords) == 2

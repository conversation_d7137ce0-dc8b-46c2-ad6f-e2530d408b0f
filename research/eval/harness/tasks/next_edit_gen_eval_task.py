"""The Next Edit Generation eval task."""

import copy
import dataclasses
import difflib
import json
import logging
from dataclasses import dataclass
from pathlib import Path
import pickle
import random
from typing import Self, Sequence, assert_never, cast

import numpy as np
from tqdm import tqdm

from base.blob_names.python.blob_names import FilePath, get_blob_name
from base.diff_utils.diff_utils import File
from base.diff_utils.str_diff import NoopSpan
from base.languages.language_guesser import guess_language
from base.logging.secret_logging import IgnoreAllLogger
from base.ranges.range_types import CharRange
from base.ranges.line_map import LineMap
from base.static_analysis.common import (
    assert_eq,
    check_not_none,
    groupby,
    show_str_diff,
)
from research.core import diff_utils
from research.core.changes import get_after, get_before
from research.core.str_diff import precise_line_diff
from research.core.types import Document
from research.core.utils import FileLogger
from research.eval.harness.systems.abs_system import AbstractSystem
from research.eval.harness.systems.next_edit_gen_system import (
    EditGenSystemInput,
    EditGenSystemOutput,
    NextEditGenSystem,
)
from research.eval.harness.tasks import next_edit_location_eval_task as loc_task
from research.eval.harness.tasks.abs_task import AbstractTask
from research.eval.harness.tasks.next_edit_location_eval_task import (
    compute_modified_ranges,
)
from research.next_edits.edit_gen_formatters import equal_modulo_spaces
from research.next_edits.edit_gen_sampler import EditGenOutput, EditGenProblem
from research.next_edits.next_edits_dataset import (
    Datum as NextEditDatum,
    HindsightEditGenProblem,
)
from research.utils.repo_change_utils import repo_change_from_patchset
from research.utils.sampling_utils import downsample_to

logger = logging.getLogger(__name__)


@dataclass
class EvalSourceJson:
    """The eval data source is a jsonl file."""

    diffs_path: Path | str
    """The path to the dataset diffs jsonl file."""

    files_path: Path | str
    """The path to the dataset files jsonl file."""


@dataclass
class EvalSourcePickle:
    """The eval data source is a pickle file."""

    path: Path | str
    """The path to the dataset pickle file."""


@dataclass
class EvalSourceHindsight:
    """The eval data source is a hindsight dataset."""

    path: Path | str
    """The path to the dataset pickle file."""


EvalDataSource = EvalSourceJson | EvalSourcePickle | EvalSourceHindsight


@dataclass
class EvalExampleOutput:
    """The model output on a single example."""

    path: FilePath
    edit_region: CharRange
    predicted_has_change: bool
    predicted_code: str | None
    request_id: str | None


@dataclass
class NextEditGenEvalTask(AbstractTask[EditGenSystemInput, EditGenSystemOutput]):
    """The task for evaluating next edit generation models."""

    data_source: EvalDataSource
    """The data source for the task."""

    edit_group_sizes: Sequence[int] = (1, 5)
    """The maximum number of events in each group.

    See the description in `group_edit_events` for more details.
    """

    limit_examples: int | None = None
    """The maximum number of EditGenProblem to load."""

    VERSION = "1.4"
    """The version number of this eval task.

    Change log:
    - v1.1: switch to use precise_line_diff in datum_to_problems.
    - v1.2: support EvalSourcePickle.
    - v1.3: rewrite the eval loop to better suit the new Hindsight eval.
    - v1.4: add support for group_edit_events.
    """

    def run(
        self,
        system: AbstractSystem[EditGenSystemInput, EditGenSystemOutput],
        output_path: str | Path,
        output_prefix: str = "",
    ) -> dict:
        assert isinstance(system, NextEditGenSystem)
        output_path = Path(output_path)
        output_path.mkdir(exist_ok=True, parents=True)
        logger.info(f"Saving results into {output_path}")
        (output_path / "VERSION.txt").write_text(self.VERSION)

        problems = load_problems_from_eval_source(self.data_source, self.limit_examples)
        logger.info(f"Loaded {len(problems)} problems.")

        max_output_tokens = system.generation_options.max_generated_tokens

        # first index all documents
        all_files = set[File]()
        for prob in problems:
            if isinstance(prob, HindsightEditGenProblem):
                files = prob.current_files
            else:
                files = prob.repo_change.after_files
            all_files.update(
                File(path=str(path), contents=code)
                for path, code in files.items()
                if guess_language(path) is not None
            )
        system.add_docs([Document.new(f.contents, path=f.path) for f in all_files])

        # evaluate the system on each problem
        positive_has_suggestion = list[bool]()
        negative_no_suggestion = list[bool]()
        edits_are_correct = list[bool]()
        edits_partial_correct = list[bool]()
        eval_outputs = list[EvalExampleOutput]()
        for i, prob in enumerate(tqdm(problems, desc="Evaluating", smoothing=0)):
            if isinstance(prob, HindsightEditGenProblem):
                all_files = prob.current_files
            else:
                all_files = prob.repo_change.after_files

            if isinstance(prob, HindsightEditGenProblem):
                recent_changes = tuple(
                    prob.squashable_edits.convert_edit_events_to_modified_files(
                        safe_logger=IgnoreAllLogger(),
                        group_sizes=self.edit_group_sizes,
                    )
                )
                current_file = prob.current_files[prob.current_path]
                prefix = current_file[: prob.edit_region.start]
                selected_code = current_file[prob.edit_region.to_slice()]
                suffix = current_file[prob.edit_region.stop :]
                expected_output = prob.ground_truth
            else:
                # convert FileTuple changes to File changes
                recent_changes = tuple(
                    c.map(lambda x: x.to_file()) for c in prob.repo_change.changed_files
                )
                prefix = prob.current_code[: prob.edit_region.start]
                selected_code = prob.current_code[prob.edit_region.to_slice()]
                suffix = prob.current_code[prob.edit_region.stop :]
                expected_output = prob.output

            doc_ids = {
                get_blob_name(str(path), code)
                for path, code in all_files.items()
                if guess_language(path) is not None
            }
            sys_input = EditGenSystemInput(
                path=str(prob.current_path),
                prefix=prefix,
                selected_code=selected_code,
                suffix=suffix,
                instruction="",
                recent_changes=recent_changes,
                generate_description=False,
                doc_ids=frozenset(doc_ids),
                stop_on_pause=False,  # for eval, we don't stop on pause
            )
            # evaluate the system once
            log_dir = output_path / f"{output_prefix}_problem_{i}"
            file_logger = FileLogger(log_dir)
            should_change = selected_code != expected_output.replacement
            if should_change:
                system.generation_options.max_generated_tokens = max_output_tokens
            else:
                # Save some decoding time since we don't need an edit in such cases
                system.generation_options.max_generated_tokens = 1
            sys_output = system.generate(
                sys_input,
                expected_output=expected_output.replacement
                if expected_output.changed
                else None,
            )
            sys_output.log_to_file(file_logger)

            if not should_change:
                # this is a negative example
                no_suggestion = not sys_output.changed
                negative_no_suggestion.append(no_suggestion)
                file_logger.log("negative_no_suggestion.txt", str(no_suggestion))
            else:
                positive_has_suggestion.append(sys_output.changed)

                edit_is_correct = equal_modulo_spaces(
                    sys_output.replacement, expected_output.replacement
                )
                edits_are_correct.append(edit_is_correct)
                file_logger.log("edit_is_correct.txt", str(edit_is_correct))

                edit_is_partial_correct = is_edit_partial_correct(
                    selected_code,
                    sys_output.replacement,
                    expected_output.replacement,
                )
                edits_partial_correct.append(edit_is_partial_correct)
                file_logger.log(
                    "edit_is_partial_correct.txt", str(edit_is_partial_correct)
                )

                predicted_change = show_str_diff(selected_code, sys_output.replacement)
                file_logger.log("predicted_change.txt", predicted_change)
            gold_change = show_str_diff(selected_code, expected_output.replacement)
            file_logger.log("gold_change.txt", gold_change)
            request_id = None
            if isinstance(prob, HindsightEditGenProblem):
                request_id = prob.origin.request_id
            eval_outputs.append(
                EvalExampleOutput(
                    path=str(prob.current_path),
                    edit_region=prob.edit_region,
                    predicted_has_change=sys_output.changed,
                    predicted_code=sys_output.replacement if should_change else None,
                    request_id=request_id,
                )
            )

        with open(output_path / "eval_outputs.pkl", "wb") as f:
            pickle.dump(eval_outputs, f)

        result_dict = {
            "artifacts": str(output_path),
            "positive_has_suggestion": counted_percentage(positive_has_suggestion),
            "negative_no_suggestion": counted_percentage(negative_no_suggestion),
            "edits_are_correct": counted_percentage(edits_are_correct),
            "edits_partial_correct": counted_percentage(edits_partial_correct),
        }

        # pretty print the result
        metric_lines = ["Metrics:\n"]
        for k, v in result_dict.items():
            metric_lines.append(f"  {k}: {v}\n")
        print("".join(metric_lines))

        return result_dict

    @classmethod
    def from_yaml_config(cls, config: dict) -> Self:
        config = copy.copy(config)
        eval_source_name = config.pop("eval_source_name")
        if eval_source_name == "EvalSourcePickle":
            dataset_path: str = config.pop("dataset_path")
            assert dataset_path.endswith(".pkl")
            data_source = EvalSourcePickle(dataset_path)
        elif eval_source_name == "EvalSourceHindsight":
            dataset_path: str = config.pop("dataset_path")
            assert dataset_path.endswith(".pkl")
            data_source = EvalSourceHindsight(dataset_path)
        elif eval_source_name == "EvalSourceJson":
            if "dataset_path" in config:
                dataset_path: str = config.pop("dataset_path")
                prefix = dataset_path.split(".jsonl.zst")[0]
                data_source = EvalSourceJson(
                    diffs_path=prefix + ".diffs.jsonl.zst",
                    files_path=prefix + ".files.jsonl.zst",
                )
            else:
                diffs_path = config.pop("diffs_path")
                files_path = config.pop("files_path")
                data_source = EvalSourceJson(diffs_path, files_path)
        else:
            raise ValueError(f"Unknown eval source: {eval_source_name}")
        return cls(data_source=data_source, **config)

    def __len__(self) -> int:
        raise NotImplementedError()

    def execute(
        self,
        model_input,
        generation: str,
        timeout=None,
    ) -> dict:
        raise NotImplementedError()

    def __getitem__(self, index: int):
        raise NotImplementedError()


def load_problems_from_eval_source(
    data_source: EvalDataSource, limit_examples: int | None = None
) -> list[EditGenProblem] | list[HindsightEditGenProblem]:
    if isinstance(data_source, EvalSourceJson):
        logger.info("Loading and converting the dataset...")
        problems = load_edit_problems_from_next_edit_data(
            data_source.diffs_path,
            data_source.files_path,
            limit_examples=limit_examples,
        )
    elif isinstance(data_source, EvalSourcePickle):
        logger.info(f"Loading pickled dataset from {data_source.path}...")
        with open(data_source.path, "rb") as f:
            problems = cast(list[EditGenProblem], pickle.load(f))
            if limit_examples is not None:
                problems = downsample_to(random.Random(42), problems, limit_examples)
    elif isinstance(data_source, EvalSourceHindsight):
        logger.info(f"Loading hindsight dataset from {data_source.path}...")
        with open(data_source.path, "rb") as f:
            problems = cast(list[HindsightEditGenProblem], pickle.load(f))
            if limit_examples is not None:
                problems = downsample_to(random.Random(42), problems, limit_examples)
    else:
        assert_never(data_source)
    if not problems:
        raise ValueError(f"No problems found from {data_source}")
    logger.info(f"Loaded {len(problems)} problems.")
    return problems


def load_edit_problems_from_next_edit_data(
    diffs_path: Path | str,
    files_path: Path | str,
    edit_region_context_lines: int = 5,
    max_repo_size: int = 8000,
    limit_examples: int | None = None,
) -> list[EditGenProblem]:
    logger.info("Loading the location dataset...")
    next_edit_data = loc_task.load_dataset(diffs_path, files_path, limit_examples)
    grouped_data = groupby(next_edit_data, lambda d: d.group_id).values()
    all_problems = list[EditGenProblem]()
    failed_conversions = list[str]()
    oversized_data = list[str]()
    for datum_group in tqdm(
        grouped_data,
        desc="datum_to_problems",
        mininterval=1,
        smoothing=0,
        unit="group",
    ):
        datum_group.sort(key=lambda d: d.group_sequence_id)
        for t, datum in enumerate(datum_group):
            if len(datum.wip_files) > max_repo_size:
                oversized_data.append(datum.id)
                continue
            next_datum = datum_group[t + 1] if t + 1 < len(datum_group) else None
            problems = datum_to_problems(datum, next_datum, edit_region_context_lines)
            if problems is None:
                failed_conversions.append(datum.id)
                continue
            if (
                limit_examples is not None
                and len(all_problems) + len(problems) > limit_examples
            ):
                problems = problems[: limit_examples - len(all_problems)]
                all_problems.extend(problems)
                break
            all_problems.extend(problems)
    print(
        f"Converted {len(all_problems)} problems from {len(next_edit_data)} data:\n"
        f"  {len(failed_conversions)} failed datum conversions: {failed_conversions}\n"
        f"  {len(oversized_data)} oversized data: {oversized_data}"
    )
    return all_problems


def datum_to_problems(
    datum: NextEditDatum,
    next_datum: NextEditDatum | None,
    edit_region_context_lines: int = 5,
) -> list[EditGenProblem] | None:
    """Convert a NextEditDatum into a series of EditGenProblems, one for each diff hunk.

    Args:
        datum: The NextEditDatum to sample from.
        next_datum: If provided, only the code regions that have changed between\
            `datum` and `next_datum` will be picked as the editing targets.
        edit_region_context_lines: The diff hunk size used to compute the edit regions.\
            This is independent of how much diff context we show to the model.

    """
    try:
        wip_repo = diff_utils.Repository(files=datum.wip_files)

        wip_to_future_patch = diff_utils.parse_git_diff_output(datum.wip_to_future_diff)
        future_repo = diff_utils.apply_diff(wip_repo, wip_to_future_patch)

        past_to_wip_patch = diff_utils.parse_git_diff_output(datum.past_to_wip_diff)
        past_repo = diff_utils.apply_diff(wip_repo, past_to_wip_patch, reverse=True)

        past_to_wip_change = repo_change_from_patchset(past_repo, past_to_wip_patch)
        wip_to_future_change = repo_change_from_patchset(wip_repo, wip_to_future_patch)

        if next_datum is not None:
            # when there is a next datum from the same commit/PR, we only pick the
            # locations that have changed between the two data points. This avoids
            # picking the same editing region multiple times when there are multiple
            # data from the same commit/PR.
            assert_eq(next_datum.group_id, datum.group_id)
            assert_eq(next_datum.group_sequence_id, datum.group_sequence_id + 1)
            next_repo = diff_utils.Repository(files=next_datum.wip_files)
        else:
            next_repo = future_repo
        # problems are only created from locations that have changed between the
        # current and next repo state.
        prob_locations = compute_modified_ranges(
            wip_repo,
            next_repo,
            ignore_whitespace=True,
            num_context_lines=edit_region_context_lines,
        )
    except diff_utils.CommandFailedError:
        return None

    # This maps the current path to the previous path for each renamed file.
    # We use this to get the prev_path required by the EditGenProblem.
    current_path_to_prev = dict[Path, Path | None]()
    for change in past_to_wip_change.changed_files:
        if after_file := get_before(change):
            current_path_to_prev[after_file.path] = get_after(
                change.map(lambda x: x.path)
            )
    current_path_to_future_code = dict[Path, str]()
    for change in wip_to_future_change.changed_files:
        if (before_file := get_before(change)) and (after_file := get_after(change)):
            current_path_to_future_code[before_file.path] = after_file.code

    problems = list[EditGenProblem]()
    for loc in prob_locations:
        current_path = Path(loc.path)
        future_code = current_path_to_future_code.get(current_path)
        if future_code is None:
            logger.warning(
                "Skipping example with no future code: " f"{loc.path=}, {datum.id=}",
            )
            continue
        prev_path = current_path_to_prev.get(current_path)
        if prev_path is None and current_path in past_to_wip_change.before_files:
            # Current file didn't get renamed, so prev_path is the same as current_path.
            prev_path = current_path

        current_code = past_to_wip_change.after_files[current_path]
        lmap = LineMap(current_code)
        current_to_future_diff = precise_line_diff(current_code, future_code)

        # We perform an extra map-then-unmap operation based on the diff alignment to
        # extend the edit region into a more stable range. This makes it more likely
        # that edit_region_before and edit_region_after can be unambiguously mapped
        # to each other.
        edit_region = lmap.lrange_to_crange(loc.range)
        future_edit_region = current_to_future_diff.before_range_to_after(edit_region)
        edit_region = current_to_future_diff.after_range_to_before(future_edit_region)
        future_edit_region = current_to_future_diff.before_range_to_after(edit_region)

        replacement = future_code[future_edit_region.to_slice()]
        selected_code = current_code[edit_region.to_slice()]
        if equal_modulo_spaces(selected_code, replacement):
            if selected_code == replacement:
                logger.warning(
                    "Skipping example with no change: "
                    f"{edit_region=}, {current_path=}, {datum.id=}",
                )
            continue
        output = EditGenOutput(replacement=replacement, changed=True)
        prob = EditGenProblem(
            current_path=current_path,
            prev_path=prev_path,
            current_code=current_code,
            final_code=future_code,
            edit_region=edit_region,
            instruction=datum.instruction,
            repo_change=past_to_wip_change,
            output=output,
            commit_meta=check_not_none(datum.commit_meta),
        )
        problems.append(prob)
    return problems


@dataclass
class NextEditGenEvalInfo:
    """The logged debugging info for a next edit gen eval task example."""

    save_path: str
    predicted_change: str
    gold_change: str
    prompt_tokens: str
    output_tokens: str
    replacement: str

    @staticmethod
    def load_from_dir(path: Path) -> "NextEditGenEvalInfo":
        """Load the NextEditGenEvalInfo from a given directory."""
        return NextEditGenEvalInfo(
            save_path=str(path),
            predicted_change=(path / "predicted_change.txt").read_text("utf-8"),
            gold_change=(path / "gold_change.txt").read_text("utf-8"),
            prompt_tokens=(path / "prompt_tokens.txt").read_text("utf-8"),
            output_tokens=(path / "output_tokens.txt").read_text("utf-8"),
            replacement=(path / "replacement.txt").read_text("utf-8"),
        )

    def to_dict(self) -> dict[str, str]:
        return dataclasses.asdict(self)


def count_changed_lines(before: str, after: str) -> int:
    """Count the number of lines that have changed between two strings."""
    before_lines = before.splitlines(keepends=True)
    after_lines = after.splitlines(keepends=True)
    cruncher = difflib.SequenceMatcher(None, before_lines, after_lines)
    changed_lines = 0
    for tag, i1, i2, j1, j2 in cruncher.get_opcodes():
        if tag == "equal":
            continue
        changed_lines += i2 - i1 + j2 - j1
    return changed_lines


def counted_percentage(values: Sequence[bool], digits: int = 1) -> str:
    """Return the average of a list of values, with the number of values in parentheses."""
    if not values:
        return "NaN (0)"
    return f"{np.mean(values):.{digits}%} ({len(values)})"


def is_edit_partial_correct(
    selected_code: str, predicted: str, ground_truth: str
) -> bool:
    """Check against ground truth only on lines where the ground truth has changed."""
    if predicted == ground_truth:
        return True
    predicted_diff = precise_line_diff(selected_code, predicted)
    gt_diff = precise_line_diff(selected_code, ground_truth)

    # find out the before range that covers the ground truth changes
    changed_before_range = None
    for span, span_range in zip(gt_diff.spans, gt_diff.span_ranges_in_before):
        if isinstance(span, NoopSpan):
            continue
        if changed_before_range is None:
            changed_before_range = span_range
        else:
            changed_before_range = changed_before_range.merge(span_range)

    if changed_before_range is None:
        return ground_truth == predicted

    # compare only on the range covered by the ground truth changes
    gt_after_range = gt_diff.before_range_to_after(changed_before_range)
    predicted_after_range = predicted_diff.before_range_to_after(changed_before_range)
    return equal_modulo_spaces(
        ground_truth[gt_after_range.to_slice()],
        predicted[predicted_after_range.to_slice()],
    )

"""The FIM-Problem Eval Task.

This task aims to evaluate the FIM problem, including:
- whether our special pause, skip, or other tokens are correctly predicted.
- whether the model can generate the correct code.
So far, we only support three metrics:
- token accuracy over the middle spans
- log likelihood over the middle spans
- exact match over the first middle span
TODO: we should support more metrics.

The dataset for this task should consist of a set of FIM problems and its retrieval database.
It accepts a list of pickle files `repo_list`, each of which contains a dict with the following keys:
- all_docs: a list of Document objects.
- fim_problems_w_tag: a list of tuples, each of which contains a FIM problem and a tag.
By default, repo_list is None, which will then use "augment-ae6ecc4f0.pkl".

Currently, "augment-ae6ecc4f0.pkl" contains 1145 samples using the default sampler with tag "basic" and 1374 samples using the customized unittest sampler with tag "unittest".
"""

import copy
import json
import logging
import pathlib
import pickle
import time

from research.core.data_paths import canonicalize_path
from research.core.model_input import ModelInput
from research.core.types import Document
from research.eval.harness.metrics import (
    Da<PERSON>Tag,
    MetricsAggregator,
    compute_common_code_complete_tags,
    nest_metrics,
    safe_forward_metrics,
)
from research.eval.harness.systems.abs_system import (
    CodeCompleteSystem,
    CompletionResult,
)
from research.eval.harness.tasks.abs_task import CodeCompleteTask, DocsType
from research.fim.fim_prompt import _format_middle
from research.fim.fim_sampling import FimProblem
from research.retrieval.utils import Span

logger = logging.getLogger(__name__)


class FIMEval(CodeCompleteTask):
    """The FIM-Problem Eval Task. Given a set of FIM problems and its retrieval database,
    generate a set of documents. Score the generation using exact match and edit similarity.
    """

    ROOT_DIR: pathlib.Path = pathlib.Path(canonicalize_path("data/eval/fim_problems"))

    def __init__(
        self, repo_list: tuple[str, ...] | None = None, limit: int | None = None
    ):
        if repo_list is None:
            repo_list = ("augment-ae6ecc4f0.pkl",)
            logger.info(f"Using default repo list: {repo_list}")
        assert isinstance(repo_list, tuple)
        self._repos: list[
            tuple[tuple[Document, ...], list[tuple[FimProblem, str]]]
        ] = []
        for idx, repo in enumerate(repo_list):
            repo_file_path = FIMEval.ROOT_DIR / repo
            assert repo_file_path.is_file(), f"FIMEval: {repo_file_path} is not a file"
            cur_repo_data = pickle.load(open(repo_file_path, "rb"))
            all_docs: list[Document] = cur_repo_data["all_docs"]
            fim_problems_w_tag: list[tuple[FimProblem, str]] = cur_repo_data[
                "fim_problems_w_tag"
            ]
            for doc in all_docs:
                assert isinstance(doc, Document)
            for fim_problem, tag in fim_problems_w_tag:
                assert isinstance(fim_problem, FimProblem)
                assert isinstance(tag, str)
            unified_docs = tuple([Document.new(doc.text, doc.path) for doc in all_docs])
            self._repos.append((unified_docs, fim_problems_w_tag))
            logger.info(
                f"Loaded the {idx}-th repo: {repo} with {len(fim_problems_w_tag)} problems"
            )
            # Check whether each fim is in the repo
            doc_by_path: dict[str, Document] = {doc.path: doc for doc in unified_docs}
            for fim_problem, _ in fim_problems_w_tag:
                assert str(fim_problem.file_path) in doc_by_path
        self.limit = limit

    def execute(
        self,
        model_input: ModelInput,
        generation: str,
        timeout: float | None = None,
    ) -> dict:
        raise NotImplementedError()

    def __getitem__(self, index: int) -> tuple[ModelInput, DocsType]:
        """Get the index-th example in this task."""
        raise NotImplementedError()

    def __len__(self) -> int:
        """Return the total number of examples in this task."""
        total = sum(len(problems) for _, problems in self._repos)
        if self.limit:
            return min(self.limit, total)
        return total

    def run(
        self,
        system: CodeCompleteSystem,
        output_path: str | pathlib.Path,
        output_prefix: str = "",
    ) -> dict:
        """Run the task with the given system and save the results into output_path/output_prefix_xxx."""
        del output_prefix
        start_timestamp = time.time()
        start_time = time.monotonic()

        output_path = pathlib.Path(output_path)
        output_path.mkdir(exist_ok=True, parents=True)
        logger.info(f"Save results into {output_path}")

        tokenizer = system.get_model().tokenizer
        special_tokens = tokenizer.special_tokens
        metrics_aggregator = MetricsAggregator()

        all_user_defined_tags = set()
        num_visited_problems = 0
        for irepo, (all_docs, problems_with_tag) in enumerate(self._repos):
            logger.info(
                f"Processing the {irepo}/{len(self._repos)}-th repo with {len(problems_with_tag)} problems"
            )
            system.clear_retriever()
            system.add_docs(list(all_docs))
            doc_by_path: dict[str, Document] = {doc.path: doc for doc in all_docs}
            default_repo_ids: set[str] = {x.id for x in all_docs}
            for idx, (fim_problem, tag) in enumerate(problems_with_tag):
                # Update the current doc in the system's retriever to avoid leaking the ground truth
                existing_doc = doc_by_path[str(fim_problem.file_path)]
                system.remove_docs([existing_doc.id])
                new_content = fim_problem.prefix.code + fim_problem.suffix.code
                new_doc = Document.new(text=new_content, path=fim_problem.file_path)
                system.add_docs([new_doc])
                current_doc_ids = copy.deepcopy(default_repo_ids)
                current_doc_ids.remove(existing_doc.id)
                current_doc_ids.add(new_doc.id)
                # Build the model input.
                model_input = ModelInput(
                    prefix=fim_problem.prefix.code,
                    suffix=fim_problem.suffix.code,
                    path=str(fim_problem.file_path),
                    target=None,
                    cursor_position=len(fim_problem.prefix.code),
                    doc_ids=list(current_doc_ids),
                )
                # Build our custom target tokens for FIM
                target_tokens = _format_middle(
                    fim_problem.middle_spans,
                    tokenize_span=lambda span: tokenizer.tokenize_safe(span.code),
                    skip_id=special_tokens.skip,  # type: ignore
                    pause_id=special_tokens.pause,  # type: ignore
                    fim_stop_id=special_tokens.eos,
                )
                model_input.extra["target_tokens"] = target_tokens
                result: CompletionResult = system.generate(model_input)
                forward_metrics = safe_forward_metrics(system, model_input)
                # Collect the output
                cur_tags = compute_common_code_complete_tags(model_input)
                cur_tags.append(DatumTag(category="user-defined", value=tag))
                # Compute the exact match on the first span
                if len(fim_problem.middle_spans) > 0:
                    first_span = fim_problem.middle_spans[0].content.code
                    exact_match = first_span == result.generated_text[: len(first_span)]
                else:
                    exact_match = result.generated_text == ""
                metric_dict = {
                    "log_likelihood": forward_metrics.log_likelihood,
                    "token_accuracy": forward_metrics.token_accuracy,
                    "exact_match_1_span": float(exact_match),
                }
                metrics_aggregator.add(metric_dict, cur_tags)
                all_user_defined_tags.add(tag)
                logger.info(
                    f"Processed [{irepo}/{len(self._repos)}] {idx+1}/{len(problems_with_tag)} problems"
                )
                # Add the removed document back to the system and delete the temporary document
                system.remove_docs([new_doc.id])
                system.add_docs([existing_doc])
                num_visited_problems += 1
                if self.limit and num_visited_problems >= self.limit:
                    break
            if self.limit and num_visited_problems >= self.limit:
                break

        metrics = nest_metrics(metrics_aggregator.compute())
        # Display the metrics for each user-defined tag
        for tag in all_user_defined_tags:
            cur_metrics = metrics["user-defined"][tag]
            cur_metrics_str = json.dumps(cur_metrics, indent=2)
            logger.info(f"Metrics for tag {tag}:\n{cur_metrics_str}")

        end_time = time.monotonic()

        return {
            "metrics": metrics,
            "start_time": start_timestamp,
            "run_duration": end_time - start_time,
        }

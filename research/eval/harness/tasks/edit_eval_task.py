"""The task for evaluating edit models.

Expected data format under the root directory:
- `examples`: each json file in this folder is an asdict result of the ResearchEditPromptInput instance. It contains the following info
--- "repo_url" in the extra field, which will be used to identify the repository.
--- (optional) "category" in the extra field, which will be used to identify the category.
- `repos`: a folder contains a list of json files, where each json file is a dict of:
--- "repo_url": the repo url with commit sha
--- "docs": a list of asdict result of the Document instance.

See an example of how to prepare the dataset in experimental/dxy/edits/scripts/generate_91.py.
"""

import dataclasses
import logging
import pathlib
import time
from collections import defaultdict
from typing import Any, Optional, Union

import numpy as np
from requests.exceptions import JSONDecodeError

from base.prompt_format_edit.prompt_formatter import ExceedContextLength
from research.core import (
    utils_for_dataclass,
    utils_for_file,
    utils_for_log,
    utils_for_str,
)
from research.core.data_paths import canonicalize_path
from research.core.edit_prompt_input import ResearchEditPromptInput
from research.core.types import Document
from research.eval.harness.metrics import ForwardMetrics, safe_edits_forward_metrics
from research.eval.harness.systems.abs_system import (
    CodeInstructSystem,
    CompletionResult,
)
from research.eval.harness.tasks.abs_task import CodeInstructTask

logger = utils_for_log.create_logger("tasks.edit_eval_task", log_level=logging.INFO)


PATH_BASIC_EVAL = str(canonicalize_path("data/eval/code_edit_eval/clean_basic_task"))
PATH_PR_EVAL = str(canonicalize_path("data/eval/code_edit_eval/pr_eval_2024FEB22"))


class EditEvalTask(CodeInstructTask):
    """The task for evaluating edit models."""

    def __init__(
        self,
        dataset_path: str = PATH_BASIC_EVAL,
        limit_of_num_files_in_repo: Optional[int] = None,
    ):
        """Initialize the task.

        Args:
            dataset_path: the path to the dataset.
            limit_of_num_files_in_repo: the maximum number of files in a repo, if a repo has more than this number of files, the corresponding repo and examples will be ignored.
        """
        super().__init__()
        init_time = time.time()
        example_dir = pathlib.Path(dataset_path) / "examples"
        repo_dir = pathlib.Path(dataset_path) / "repos"
        assert (
            example_dir.exists() and repo_dir.exists()
        ), f"{dataset_path} did not find examples.jsonl and repos.jsonl."
        # Load the repo information from repo_dir
        self.repo2docs: dict[str, list[Document]] = {}
        self._ignored_repo_urls: set[str] = set()
        for repo_file in repo_dir.glob("*.json"):
            raw_repo_data = utils_for_file.read_json(repo_file)
            repo_url = raw_repo_data["repo_url"]
            docs = raw_repo_data["docs"]
            docs = [utils_for_dataclass.create_from_dict(Document, doc) for doc in docs]
            if (
                limit_of_num_files_in_repo is not None
                and len(docs) > limit_of_num_files_in_repo
            ):
                logger.warning(
                    f"Ignored {repo_url} with {len(docs)} files because it has more than {limit_of_num_files_in_repo} files."
                )
                self._ignored_repo_urls.add(repo_url)
                continue
            self.repo2docs[repo_url] = docs
        # Load the examples from example_dir
        self.examples: list[ResearchEditPromptInput] = []
        all_files = sorted(list(example_dir.glob("*.json")))
        for index, ex_file in enumerate(all_files):
            ex = utils_for_file.read_json(ex_file)
            example = utils_for_dataclass.create_from_dict(ResearchEditPromptInput, ex)
            assert (
                "repo_url" in example.extra
            ), f"Did not find repo_url in {example.extra}"
            # Check if the repo_url should be ignored or not.
            if example.extra["repo_url"] in self._ignored_repo_urls:
                continue
            if "category" not in example.extra:
                example.extra["category"] = "<uncategoried>"
            example.extra["index_in_dataset"] = index
            self.examples.append(example)
        logger.info(
            f"Loaded {len(self.examples)} examples with {len(self.repo2docs)} repos in {time.time() - init_time:.2f}s."
            f"\tIgnored {len(self._ignored_repo_urls)} repos and the corresponding examples due to the limit of {limit_of_num_files_in_repo} files in a repo."
        )

    def _input_preprocessing(
        self, sample: ResearchEditPromptInput
    ) -> ResearchEditPromptInput:
        """This is a hook to enable PREditEvalTask."""
        return sample

    def run(
        self,
        system: CodeInstructSystem,
        output_path: Union[str, pathlib.Path],
        output_prefix: str = "",
    ) -> dict:
        output_dir = (
            pathlib.Path(output_path)
            / f"{output_prefix}_{system.name}_{utils_for_str.get_random_str(4)}"
        )
        output_dir.mkdir(exist_ok=True, parents=False)
        cache_dir = output_dir / "cache"
        cache_dir.mkdir(exist_ok=True, parents=False)

        results: list[tuple[CompletionResult, ForwardMetrics]] = []
        system.clear_retriever()
        last_repo_url = None
        for idx, sample in enumerate(self.examples):
            cur_repo_url = sample.extra["repo_url"]
            # Add the documents for the current repo to the system
            if cur_repo_url != last_repo_url:
                logger.info(
                    f"At {idx:3d}-th sample, adding {len(self.repo2docs[cur_repo_url])} docs for {cur_repo_url}"
                )
                system.clear_retriever()
                system.add_docs(self.repo2docs[cur_repo_url])
                last_repo_url = cur_repo_url
                logger.info(
                    f"At {idx:3d}-th sample, finished adding {len(self.repo2docs[cur_repo_url])} docs for {cur_repo_url}"
                )
            sample = self._input_preprocessing(sample)
            try:
                result = system.generate(sample)
                metrics = safe_edits_forward_metrics(system, sample)
                cur_info_to_log = self._create_log_dict(sample, result, metrics, True)
            except (ExceedContextLength, JSONDecodeError) as e:
                logger.warning(f"Caught {e}: on {idx}-th sample {sample.path}")
                result = CompletionResult(
                    generated_text="",
                    prompt_tokens=[],
                    retrieved_chunks=[],
                )
                metrics = ForwardMetrics(
                    log_likelihood=0.0,
                    token_accuracy=0.0,
                )
                cur_info_to_log = self._create_log_dict(sample, result, metrics, False)
            # The following is just for logging purpose.
            logger.info(
                f"Evaluating {idx:3d}/{len(self.examples)}"
                f" currently has {system.get_num_docs()} docs in the retriever."
            )
            utils_for_file.write_json(
                cache_dir / f"{idx}.json", cur_info_to_log, indent=2
            )
            results.append((result, metrics))
        system.clear_retriever()
        return self.compute_metrics(results)

    def _create_log_dict(
        self,
        sample: ResearchEditPromptInput,
        result: CompletionResult,
        metrics: ForwardMetrics,
        success: bool,
    ) -> dict:
        info_to_log = {
            "index": sample.extra["index_in_dataset"],
            "generated_text": result.generated_text,
            "success": success,
            "path": sample.path,
            "repo_url": sample.extra["repo_url"],
            "metrics": dataclasses.asdict(metrics),
        }
        if "request_id" in result.extra_output.additional_info:
            info_to_log["request_id"] = result.extra_output.additional_info[
                "request_id"
            ]
        if "session_id" in result.extra_output.additional_info:
            info_to_log["session_id"] = result.extra_output.additional_info[
                "session_id"
            ]
        return info_to_log

    @classmethod
    def from_yaml_config(cls, config: dict) -> "EditEvalTask":
        """Returns EditEvalTask object constructed using a config dictionary."""
        kwargs = {}
        if "dataset_path" in config:
            kwargs["dataset_path"] = config["dataset_path"]
        return cls(**kwargs)

    def __getitem__(self, index: int) -> tuple[ResearchEditPromptInput, list[Document]]:
        example = self.examples[index]
        repo_url = example.extra["repo_url"]
        return example, self.repo2docs[repo_url]

    def __len__(self) -> int:
        return len(self.examples)

    def execute(
        self,
        model_input: ResearchEditPromptInput,
        generation: str,
        timeout: Optional[float] = None,
    ) -> dict:
        raise NotImplementedError()

    def compute_metrics(
        self, results: list[tuple[CompletionResult, ForwardMetrics]]
    ) -> dict[str, Any]:
        """Computes final evaluation metrics based on generated edits."""
        em_total_list = []
        em_list_per_category = defaultdict(list)
        ll_total_list = []
        ll_list_per_category = defaultdict(list)
        token_accuracy_total_list = []
        token_accuracy_list_per_category = defaultdict(list)

        for sample, (result, metrics) in zip(self.examples, results):
            cur_em = int(sample.updated_code == result.generated_text)
            em_total_list.append(cur_em)
            em_list_per_category[sample.extra["category"]].append(cur_em)

            cur_ll = metrics.log_likelihood
            ll_total_list.append(cur_ll)
            ll_list_per_category[sample.extra["category"]].append(cur_ll)

            cur_token_acc = metrics.token_accuracy
            token_accuracy_total_list.append(cur_token_acc)
            token_accuracy_list_per_category[sample.extra["category"]].append(
                cur_token_acc
            )

        per_category_em_metric = {
            f"exact_match@{category}": np.mean(em_list).item()
            for (category, em_list) in em_list_per_category.items()
        }
        per_category_ll_metric = {
            f"loglikelihood@{category}": np.mean(ll_list).item()
            for (category, ll_list) in ll_list_per_category.items()
        }
        per_category_token_acc_metric = {
            f"token_accuracy@{category}": np.mean(token_accuracy_list).item()
            for (
                category,
                token_accuracy_list,
            ) in token_accuracy_list_per_category.items()
        }
        return {
            "exact_match (mean)": np.mean(em_total_list).item(),
            "total number of examples": len(em_total_list),
            "loglikelihood (mean)": np.mean(ll_total_list).item(),
            "token_accuracy (mean)": np.mean(token_accuracy_total_list).item(),
            **per_category_em_metric,
            **per_category_ll_metric,
            **per_category_token_acc_metric,
        }


def mark_lines(code: str, line_range: list[int]) -> str:
    result = []
    for i, line in enumerate(code.splitlines(True)):
        if line_range[0] <= i < line_range[1]:
            result.append(f"|>{line}")
        else:
            result.append(line)
    return "".join(result)


class PREditEvalTask(EditEvalTask):
    """The task for evaluating PR-edit model."""

    def _input_preprocessing(
        self, sample: ResearchEditPromptInput
    ) -> ResearchEditPromptInput:
        """This hook augments the input that we process."""
        line = sample.extra["commented_line"]
        selected_code = mark_lines(sample.selected_code, [line, line + 1])
        instruction = f"Fix PR comment: {sample.instruction}"

        return dataclasses.replace(
            sample,
            instruction=instruction,
            selected_code=selected_code,
        )

import argparse
import json
import os
from datetime import datetime
from pathlib import Path

import zstandard as zstd
from jinja2 import Environment

from research.eval.harness.tasks.augment_qa_utils import AugmentQAOutput

_REPORT_TEMPLATE_PATH = (
    Path(__file__).parents[0] / "data/augment_qa_visualization_template_multi.html"
)


def _parse_arguments() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Compare 1 to 3 eval results and generate an HTML report."
    )
    parser.add_argument(
        "--input_paths",
        nargs="+",
        type=str,
        default=[],
        help="Paths to the input JSONL files (1 to 3) in the format 'model:path'.",
    )
    parser.add_argument(
        "--output_path",
        type=str,
        default="",
        help="Path to the output HTML file.",
    )
    return parser.parse_args()


def _load_eval_results(file_path: Path) -> list[AugmentQAOutput]:
    if file_path.suffix == ".zst":
        with zstd.open(file_path, "rt", encoding="utf-8") as f:
            return [AugmentQAOutput(**json.loads(line.strip())) for line in f]
    elif file_path.suffix == ".jsonl":
        with open(file_path, "r", encoding="utf-8") as f:
            return [AugmentQAOutput(**json.loads(line.strip())) for line in f]
    else:
        raise ValueError(f"Unsupported file format: {file_path.suffix}")


def _compute_metrics(results: list[AugmentQAOutput]) -> dict[str, float]:
    if not results:
        return {}
    n_samples = float(len(results))
    return {
        key: sum(r.metrics[key] for r in results) / n_samples
        for key in results[0].metrics.keys()
    }


def _render_html_report(eval_results: list[dict]) -> str:
    env = Environment(keep_trailing_newline=True)
    env.globals.update(zip=zip)
    template = env.from_string(_REPORT_TEMPLATE_PATH.read_text("utf-8"))
    header = f"AugmentQA Comparison -- {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    return template.render(eval_results=eval_results, header=header)


def render_eval_results(input_paths: list[tuple[str, str]]) -> str:
    if not (1 <= len(input_paths) <= 3):
        raise ValueError("Only 1 to 3 input paths are allowed")

    eval_results_data = []
    for model, path in input_paths:
        file_path = Path(path)
        if not file_path.exists():
            raise FileNotFoundError(f"Path {path} does not exist")

        eval_result = _load_eval_results(file_path)
        metrics = _compute_metrics(eval_result)
        print(f"{model}: {metrics}")

        eval_results_data.append(
            {
                "header": model,
                "file_path": str(file_path),
                "metrics": metrics,
                "outputs": eval_result,
            }
        )

    return _render_html_report(eval_results_data)


if __name__ == "__main__":
    args = _parse_arguments()
    input_paths = [model_path.split(":", 1) for model_path in args.input_paths]
    output_path = args.output_path

    # # Uncomment and modify if you prefer setting parameters in Python
    # input_paths = [
    #     (
    #         "V14.2",
    #         "/mnt/efs/augment/eval/jobs/DeLcjEFr/",
    #     ),
    #     (
    #         "V13",
    #         "/mnt/efs/augment/eval/jobs/eYh7ie2s/",
    #     ),
    # ]
    # input_paths = [
    #     (model, os.path.join(path, "000_samples.jsonl.zst"))
    #     for model, path in input_paths
    # ]
    # output_path = "/mnt/efs/augment/public_html/zhuoran_compare/v14_2_v13.html"

    if not output_path:
        raise ValueError(f"{output_path} is empty")

    print(f"Input paths: {input_paths}")
    print(f"Output path: {output_path}")

    html = render_eval_results(input_paths)
    with open(output_path, "w") as f:
        f.write(html)

    public_html_root = "/mnt/efs/augment/public_html/"
    if output_path.startswith(public_html_root):
        print(
            f"Published at:"
            f" https://webserver.gcp-us1.r.augmentcode.com/{output_path[len(public_html_root):]}"
        )

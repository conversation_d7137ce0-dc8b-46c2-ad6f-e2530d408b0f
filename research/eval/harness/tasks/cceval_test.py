"""Unit test for CCEval.

pytest research/eval/tests/tasks/test_cceval.py
"""

import typing
import unittest

import parameterized

from research.eval.harness.tasks import CCEval


class TestCCEvalTask(unittest.TestCase):
    """Test all the functionality in the CCEval task."""

    @parameterized.parameterized.expand(
        [
            (CCEval, 9711),
            (lambda: CCEval(variant=CCEval.Variant.MULTI), 9711),
            (lambda: CCEval(variant=CCEval.Variant.COMBINED), 9711),
        ]
    )
    def test_total_examples(
        self, task_factory: typing.Callable[[], CCEval], total: int
    ):
        dataset = task_factory()
        self.assertEqual(
            len(dataset), total, msg=f"{dataset} expect to have {total} examples."
        )

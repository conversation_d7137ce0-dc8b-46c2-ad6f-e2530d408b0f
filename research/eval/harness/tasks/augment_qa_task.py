"""AugmentQA task.

The AugmentQATask expects a dataset directory with the following structure:
A dataset directory containing a 'repos' subdirectory with repository files
and a 'samples.json' file. For example, if the dataset directory is '/path/to/dataset',
the structure could be:

/path/to/dataset
|-- repos
| |-- test_repo.jsonl.zst
| |-- another_repo.jsonl.zst
|-- samples.json

The 'repos' subdirectory contains multiple repository files.
These files contain JSON lines (zstd compressed), where each line is a JSON
representation of a research.core.types.Document. The list of Document objects
can be generated using the convert_repository_to_documents method
from research.retrieval.utils.

The 'samples.json' file contains a JSON list of research.eval.harness.tasks.augment_qa_task.Sample objects.
Each Sample object is a subclass of base.prompt_format_chat.prompt_formatter.ChatPromptInput,
and contains a "repo" field, which corresponds to a repository file in
dataset_path/repos/ (e.g., test_repo.jsonl.zst). It is expected that a repository
file exists in dataset_path/repos/ for each "repo" field in the Sample objects.
"""

import dataclasses
import json
import logging
import re
import time
from collections import defaultdict
from datetime import datetime
from pathlib import Path

import zstandard as zstd
from jinja2 import Environment

from research.core import utils_for_str
from research.core.chat_prompt_input import ResearchChatPromptInput
from research.core.model_input import ModelInput
from research.core.types import Document
from research.eval.harness import utils
from research.eval.harness.systems.abs_system import (
    ChatSystem,
    CompletionResult,
)
from research.eval.harness.tasks.abs_task import ChatTask, DocsType
from research.eval.harness.tasks.augment_qa_eval_compare import render_eval_results
from research.eval.harness.tasks.augment_qa_utils import AugmentQAOutput, Sample

PUBLIC_HTML_ROOT = "/mnt/efs/augment/public_html/"

logger = logging.getLogger(__name__)


def count_keywords(texts: list[str] | str, keywords: list[str]) -> int:
    """
    Count the number of keywords that appear in at least one text.

    Args:
        texts (list[str] or str): A list of texts or a single text to search for keywords.
        keywords (list[str]): A list of keywords to search for.

    Returns:
        int: The number of unique keywords found in all texts.

    Raises:
        ValueError: If any of the keywords are empty.
    """
    if not all(keyword.strip() for keyword in keywords):
        raise ValueError("All keywords must be non-empty")
    if len(keywords) == 0:
        return 0
    n_matched_keywords = 0
    for keyword in keywords:
        regex = rf"(?:(?<=\W)|^)({re.escape(keyword)})(?:(?=\W)|$)"
        if isinstance(texts, str):
            texts = [texts]
        for text in texts:
            if re.search(regex, text):
                n_matched_keywords += 1
                break
    return n_matched_keywords


def score_response(
    sample: Sample, response: CompletionResult, generation_time: float = 0.0
) -> AugmentQAOutput:
    """Grade a response to a sample.

    Args:
        sample: The sample to grade.
        response: The response to grade.
        generation_time: The time it took to generate the response.
    """
    n_keywords = float(len(set(sample.keywords)))
    n_matched_keywords = count_keywords(response.generated_text, sample.keywords)
    answer_keyword_recall = n_matched_keywords / n_keywords

    # When we look for whether or not keywords are in the retrieved chunks,
    # we look both in the chunk text and chunk path. Since sometimes
    # the keywords are just the path (that the user is asking about).
    n_matched_keywords_in_retrievals = count_keywords(
        (
            [chunk.text for chunk in response.retrieved_chunks]
            + [
                chunk.path
                for chunk in response.retrieved_chunks
                if chunk.path is not None
            ]
        ),
        sample.keywords,
    )
    retrievals_keyword_recall = n_matched_keywords_in_retrievals / n_keywords

    retrieved_path_to_rank = {}
    for index, chunk in enumerate(response.retrieved_chunks):
        if chunk.path not in retrieved_path_to_rank:
            retrieved_path_to_rank[chunk.path] = float(index + 1)

    gold_path_ranks = [
        retrieved_path_to_rank.get(path, 0) for path in sample.gold_paths
    ]
    gold_paths_recall = sum(1.0 for rank in gold_path_ranks if rank > 0) / len(
        sample.gold_paths
    )
    gold_paths_mrr = sum(
        1.0 / rank if rank > 0 else 0.0 for rank in gold_path_ranks
    ) / len(sample.gold_paths)

    answer_length = len(response.generated_text)

    return AugmentQAOutput(
        sample=sample,
        result=response,
        answer_keyword_recall=answer_keyword_recall,
        retrievals_keyword_recall=retrievals_keyword_recall,
        gold_paths_recall=gold_paths_recall,
        gold_paths_mrr=gold_paths_mrr,
        generation_time=generation_time,
        answer_length=answer_length,
    )


class AugmentQATask(ChatTask):
    """Evaluation task for the in-house AugmentQA benchmark.

    Example config:

    task:
        dataset_path: The path to the dataset.
        html_report_output_dir: The path to the directory to write the HTML report.
        user_guidelines: The user prompt to use (optional).
        user_guidelines_path: The path to the user prompt file (optional).
        workspace_guidelines: The workspace prompt to use (optional).
        baseline_result_path: The path to the baseline result for comparison (optional).
    """

    REPORT_TEMPLATE_PATH = Path(
        Path(__file__).parents[4]
        / "research/eval/harness/tasks/data/augment_qa_visualization_template.txt"
    )

    def __init__(
        self,
        dataset_path: Path,
        html_report_output_dir: Path | None = None,
        user_guidelines: str | None = None,
        user_guidelines_path: str | None = None,
        workspace_guidelines: str | None = None,
        baseline_result_path: Path | None = None,
        limit: int | None = None,
    ):
        self.dataset_path = Path(dataset_path)
        if not self.dataset_path.is_dir():
            raise ValueError(
                f"{self.dataset_path} is not a directory or does not exist."
            )
        samples = utils.read_json(self.dataset_path / "samples.json")
        self.samples = [Sample.from_dict(sample_dict) for sample_dict in samples]
        if limit:
            self.samples = self.samples[:limit]
        self.limit = limit
        for sample in self.samples:
            repo_path = self.dataset_path / f"repos/{sample.repo}.jsonl.zst"
            if not repo_path.exists():
                raise ValueError(f"{repo_path} does not exist.")
        self.template = Path(self.REPORT_TEMPLATE_PATH).read_text("utf-8")
        self.html_report_output_dir = html_report_output_dir
        if (
            self.html_report_output_dir is not None
            and not self.html_report_output_dir.is_dir()
        ):
            raise ValueError(f"{self.html_report_output_dir} is not a directory.")
        assert not user_guidelines or not user_guidelines_path
        if user_guidelines_path:
            self.user_guidelines = Path(user_guidelines_path).read_text("utf-8")
        else:
            self.user_guidelines = user_guidelines
        self.workspace_guidelines = workspace_guidelines
        self.baseline_result_path = baseline_result_path

    def __getitem__(self, index: int) -> tuple[ResearchChatPromptInput, DocsType]:
        del index
        raise ValueError("Cannot randomly index into this task.")

    def __len__(self) -> int:
        """Return the total number of examples in this task."""
        return len(self.samples)

    @classmethod
    def from_yaml_config(cls, config: dict) -> "AugmentQATask":
        """Returns a Task object constructed using a config dictionary."""
        config["dataset_path"] = Path(config["dataset_path"])
        config["html_report_output_dir"] = (
            Path(config["html_report_output_dir"])
            if "html_report_output_dir" in config
            else None
        )
        config["baseline_result_path"] = (
            Path(config["baseline_result_path"])
            if "baseline_result_path" in config
            else None
        )
        return cls(**config)

    def render_html_report(self, metrics: dict, scored_samples: list[AugmentQAOutput]):
        env = Environment(keep_trailing_newline=True)
        env.globals.update(zip=zip)
        template = env.from_string(self.template)

        header = f"AugmentQA -- {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        rendered_html = template.render(
            metrics=metrics,
            outputs=scored_samples,
            header=header,
        )
        return rendered_html

    def compute_metrics(self, results: list[AugmentQAOutput]):
        metrics = {}
        n_samples = len(results)
        if n_samples == 0:
            return metrics
        n_samples = float(n_samples)
        for key in results[0].metrics.keys():
            metrics[key] = sum(r.metrics[key] for r in results) / n_samples
        metrics["samples"] = len(results)
        return metrics

    def _get_run_name(self, system: ChatSystem, output_prefix: str = "") -> str:
        if len(output_prefix) > 0:
            run_name = f"{output_prefix}_{system.name}"
        else:
            run_name = f"{system.name}"
        return run_name + f"_{utils_for_str.get_random_str(4)}"

    def run(
        self,
        system: ChatSystem,
        output_path: str | Path,
        output_prefix: str = "",
    ) -> dict:
        """Return the results from running the task against the model.

        May have side effects; e.g. generating code and saving in a file.

        Args:
          system: The System to run the task against.
          output_path: The path to an existing directory to which artifacts can be written.
          output_prefix: Optional prefix to include with files to prevent collisions.
        """
        run_start_timestamp = time.time()
        run_start_time = time.monotonic()

        output_samples_path = Path(output_path) / f"{output_prefix}samples.jsonl.zst"
        html_report_path = Path(output_path) / f"{output_prefix}report.html"
        html_compare_path = Path(output_path) / f"{output_prefix}compare.html"

        run_name = self._get_run_name(system, output_prefix)
        html_report_path2 = (
            self.html_report_output_dir / f"{run_name}.html"
            if self.html_report_output_dir is not None
            else None
        )
        html_compare_path2 = (
            self.html_report_output_dir / f"{run_name}_compare.html"
            if self.html_report_output_dir is not None
            else None
        )

        samples_grouped_by_repo = defaultdict(list)
        for sample in self.samples:
            sample = dataclasses.replace(
                sample,
                user_guidelines=self.user_guidelines,
                workspace_guidelines=self.workspace_guidelines,
            )
            samples_grouped_by_repo[sample.repo].append(sample)

        scored_responses = []
        with zstd.open(output_samples_path, "w", encoding="utf-8") as f:
            current_index = 0
            for repo_name, samples in samples_grouped_by_repo.items():
                logger.info(
                    f"Starting evaluation for {repo_name} repository with "
                    f"{len(samples)} samples"
                )

                # Load the documents for the current repo.
                repo_path = self.dataset_path / f"repos/{repo_name}.jsonl.zst"
                docs = {}
                for doc_dict in utils.read_jsonl_zst(repo_path):
                    docs[doc_dict["id"]] = Document(**doc_dict)
                all_doc_ids = set(docs.keys())
                logger.info(f"Loaded {len(docs)} files for {repo_name}")

                # Clear the retriever since we are switching to a new repo.
                system.clear_retriever()

                for sample in samples:
                    # First, we index the documents for the current sample.
                    if sample.doc_ids is not None:
                        if len(sample.doc_ids) == 0:
                            raise ValueError(
                                "If `doc_ids` is provided, it must be non-empty."
                            )
                        doc_ids = set(sample.doc_ids)
                    else:
                        doc_ids = all_doc_ids

                    # Remove old documents from the index.
                    system.remove_docs(system.get_doc_ids() - doc_ids)

                    # Add new documents to the index.
                    new_doc_ids = doc_ids - system.get_doc_ids()
                    system.add_docs([docs[doc_id] for doc_id in new_doc_ids])

                    # Generate the response.
                    start_time = time.time()
                    result = system.generate(sample)
                    generation_time = time.time() - start_time
                    scored_response = score_response(sample, result, generation_time)
                    scored_responses.append(scored_response)
                    logger.info(f"Sample {current_index} / {len(self.samples)}")
                    logger.info(
                        f"Request ID: {result.extra_output.additional_info.get('request_id')}"
                    )
                    logger.info(f"Generation time: {generation_time}")
                    logger.info(
                        f"Answer: \n {'-' * 80} \n{scored_response.generated_text}\n {'-' * 80}"
                    )
                    logger.info(
                        f"Ground truth: \n {'-' * 80} \n{scored_response.target}\n {'-' * 80}"
                    )
                    logger.info(
                        f"Scores: \n {'-' * 80} \n{scored_response.metrics_short_str}\n {'-' * 80}"
                    )
                    json.dump(dataclasses.asdict(scored_response), f)
                    f.write("\n")
                    current_index += 1

        metrics = self.compute_metrics(scored_responses)
        for key, value in metrics.items():
            logger.info(f"Metric {key}: {value}")

        run_end_time = time.monotonic()

        result = {
            "artifact": str(output_path) + "/",
            "metrics": metrics,
            "limit": self.limit,
            "start_time": run_start_timestamp,
            "run_duration": run_end_time - run_start_time,
        }

        html_report = self.render_html_report(metrics, scored_responses)
        html_report_path.write_text(html_report, encoding="utf-8")
        if html_report_path2:
            html_report_path2.write_text(html_report, encoding="utf-8")
            result["html_report_path"] = str(html_report_path2)
            if html_report_path2.is_relative_to(PUBLIC_HTML_ROOT):
                result["html_report_url"] = (
                    f"https://webserver.gcp-us1.r.augmentcode.com/{html_report_path2.as_posix()[len(PUBLIC_HTML_ROOT):]}"
                )

        if self.baseline_result_path is not None:
            compare_report = render_eval_results(
                [
                    ("Baseline", str(self.baseline_result_path)),
                    ("Current", str(output_samples_path)),
                ]
            )
            html_compare_path.write_text(compare_report, encoding="utf-8")
            if html_compare_path2:
                html_compare_path2.write_text(compare_report, encoding="utf-8")
                result["comparison_report_path"] = str(html_compare_path2)
                if html_compare_path2.is_relative_to(PUBLIC_HTML_ROOT):
                    result["comparison_report_url"] = (
                        f"https://webserver.gcp-us1.r.augmentcode.com/{html_compare_path2.as_posix()[len(PUBLIC_HTML_ROOT):]}"
                    )

        return result

    def execute(
        self,
        model_input: ModelInput,
        generation: str,
        timeout: float | None = None,
    ) -> dict:
        raise NotImplementedError()

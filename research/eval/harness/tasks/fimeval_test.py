"""Unit test for FIM-Problem Eval.

pytest research/eval/harness/tasks/fimeval_test.py
"""

import unittest

import parameterized

from research.eval.harness.tasks.fimeval import FIMEval


class TestFIMEvalTask(unittest.TestCase):
    """Test all the functionality in FIMEval."""

    @parameterized.parameterized.expand(
        [
            (None, 2519),
            (100, 100),
        ]
    )
    def test_total_examples(self, limit: int | None, total: int):
        dataset = FIMEval(limit=limit)
        self.assertEqual(
            len(dataset), total, msg=f"{dataset} expect to have {total} examples."
        )

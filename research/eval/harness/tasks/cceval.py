"""The CCEval Task.

A benchmark of code generation tasks where the code the complete includes an api
defined in an external file. It includes patch sites for ~1000 repos across 4
different languages: C#, Java, Python, and TypeScript.

This task is adapted from that described in the paper: CROSSCODEEVAL: A Diverse
and Multilingual Benchmark for Cross-File Code Completion
https://arxiv.org/pdf/2310.11248.pdf
"""

import logging
import random
import time
from collections import defaultdict
from dataclasses import asdict, dataclass
from enum import Enum
from pathlib import Path, PurePath
from typing import Iterable, NamedTuple, Optional, Union

from base.languages.unit_test_guesser import is_unit_test
from base.static_analysis.parsing import compare_asts
from research.core.artifacts import collect_artifacts
from research.core.data_paths import canonicalize_path
from research.core.model_input import ModelInput
from research.core.types import Char<PERSON>ange, Document
from research.core.utils_for_dataclass import create_from_dict
from research.core.utils_for_file import read_jsonl
from research.core.utils_for_str import get_first_n_lines
from research.eval.harness import utils
from research.eval.harness.metrics import (
    ForwardMetrics,
    compute_edit_similarity,
    compute_exact_match,
    safe_forward_metrics,
)
from research.eval.harness.systems.abs_system import (
    CodeCompleteSystem,
    CompletionResult,
)
from research.eval.harness.tasks.abs_task import CodeCompleteTask, DocsType

logger = logging.getLogger("tasks.cceval")


@dataclass
class CCEvalScore:
    """The metrics of the EM Task should look like the output of HumanEval."""

    exact_match: float
    """Exact match after normalizing on end-of-statement criteria."""

    exact_match_strict: float
    """Line-by-line exact match score with minimal post-processing."""

    exact_match_1_line: float
    """Exact match after truncating to one line."""

    edit_similarity: float
    """The edit similarity score."""

    ground_truth_log_likelihood: float
    """The log likelihood of the ground truth, conditioned on model input."""

    ground_truth_token_accuracy: float
    """The model accuracy on the ground truth tokens, conditioned on previous tokens."""

    ast_match: float
    """The AST match score."""

    samples: int
    """The number of samples run for this eval."""


@dataclass
class CCEvalOutput:
    """The output of the CCEval Task.

    One such record will be produced per evaluated patch.
    """

    prefix: str
    """The prefix string for the task."""

    suffix: str
    """The suffix string for the task."""

    prompt: str
    """The actual prompt used for generation.

    The prefix, suffix, and chunks may be changed to form the actual prompt,
    based on budget constraints.
    """

    prompt_tokens: list[int]
    """The prompt tokens."""

    generation: str
    """The full output text from generation."""

    ground_truth: str
    """The ground truth text."""

    patch_id: str
    """The patch id."""

    repository: str
    """The repository name from which this task originates."""

    metrics: CCEvalScore
    """The scores for this completions."""

    artifacts: list[dict]
    """Any additional artifacts to record for the patch."""


@dataclass
class CCEvalInput:
    r"""Input type.

    Example format:
    {"prompt": "// Copyright (c) ... } else",
     "groundtruth": " if (Date.now() > start + defaultTimeout * 0.9) {",
     "right_context": "\n            throw new Error(`Timed out ...    });\n}\n",
     "metadata": {
        "task_id": "project_cc_typescript/4267",
        "repository": "Azure/azure-functions-nodejs-e2e-tests",
        "file": "src/global.test.ts",
        "context_start_lineno": 0,
        "groundtruth_start_lineno": 69,
        "right_context_start_lineno": 70
        }
    }
    """

    @dataclass
    class Meta:
        """Metadata section of the input record."""

        task_id: str
        repository: str
        file: str
        context_start_lineno: int
        groundtruth_start_lineno: int
        right_context_start_lineno: int

    prompt: str
    """The prompt prefix."""

    right_context: str
    """The prompt suffix"""

    groundtruth: str
    """The groundtruth."""

    metadata: Meta
    """Metadata section"""


def get_bracket_lang_statement(completion):
    end_idx = None
    for i in range(len(completion)):
        if completion[i] in [";", "}", "{"]:
            end_idx = i
            break
    return completion[: end_idx + 1] if end_idx else completion


def postprocess_code_lines(completion: str, lang: str) -> str:
    if lang in ["java", "csharp", "typescript"]:
        line = get_bracket_lang_statement(completion)
        return " ".join(line.split())
    elif lang == "python":
        # NOTE: the original test suite used a complicated procedure for
        # normalizing the python code. But taking the first line was equivalent
        # and much simpler. If we extend this dataset to include multi-line
        # completions for python, this logic will need to change.
        return completion.splitlines()[0] if completion else completion
    else:
        raise ValueError(f"Unknown language {lang}")


def extract_lang_from_rec(rec: CCEvalInput) -> str:
    """Extract the language key from the task.

    The language key is encoded in the task-id.
    """
    return rec.metadata.task_id.split("project_cc_")[1].split("/")[0]


InternalResultT = tuple[
    CCEvalInput, ModelInput, CompletionResult, list[dict], CCEvalScore
]


class _PerRepoResult(NamedTuple):
    rec: CCEvalInput
    mi: ModelInput
    result: CompletionResult
    forward_metrics: ForwardMetrics
    artifacts: list[dict]


class CCEval(CodeCompleteTask):
    """The CCEval Task.

    Given a set of patches and a retrieval database, generate a set of documents.
    Score the generation using exact match and edit similarity.
    """

    DEFAULT_DATASET_PATH = Path(canonicalize_path("data/eval/cceval"))

    class Variant(str, Enum):
        """Variants for the CCEval task."""

        MULTI = "multi"
        """Treat each repo as a separate retrieval dataset."""

        COMBINED = "combined"
        """Combine all repos under a common root, to form a single retrieval dataset."""

    def __init__(
        self,
        limit: Optional[int] = None,
        score_interval: Optional[int] = None,
        dataset_path: Optional[str] = None,
        variant: Variant = Variant.MULTI,
        result_labels: Optional[dict[str, str]] = None,
        unit_test_only: bool = False,
    ):
        self.limit = limit
        self.score_interval = score_interval
        self.dataset_path = (
            Path(dataset_path) if dataset_path else self.DEFAULT_DATASET_PATH
        )
        self.variant = variant
        self.result_labels = result_labels
        all_patches_by_repo, all_documents_by_repo = _prepare_dataset(self.dataset_path)

        # For the single "combined" repo variant, we combine all patches and
        # documents into a single repository *after* ordering the repos, since
        # this will be the same order in which we process the non-combined
        # dataset.
        if self.variant == CCEval.Variant.COMBINED:
            all_patches_by_repo, all_documents_by_repo = _combine_dataset(
                all_patches_by_repo=all_patches_by_repo,
                all_documents_by_repo=all_documents_by_repo,
            )

        # Flatten the patches into a single list, to simplify applying limits
        all_patches = [
            (repo, rec) for repo, recs in all_patches_by_repo.items() for rec in recs
        ]
        self.all_patches_by_repo = all_patches_by_repo
        self.all_documents_by_repo = all_documents_by_repo
        logger.info(f"Loaded {len(all_patches)} patches in total.")
        if self.limit is not None:
            all_patches = all_patches[: self.limit]
            logger.info(
                f"Kept {len(all_patches)} patches after the limit={self.limit} filter."
            )
        # TODO: replace this with the category breakdown
        if unit_test_only:
            all_patches = [
                patch for patch in all_patches if is_unit_test(patch[1].metadata.file)
            ]
            logger.info(f"Kept {len(all_patches)} patches after the unit test filter.")
        self.all_patches = all_patches

    def execute(
        self,
        model_input: ModelInput,
        generation: str,
        timeout: Optional[float] = None,
    ):
        raise NotImplementedError()

    def __getitem__(self, index: int) -> tuple[ModelInput, DocsType]:
        """Get the index-th example in this task."""
        # TODO(Xuanyi): returns exactly what should be passed to system.
        logger.warning(
            "CCEval's __getitem__ is not returning the same documents and retrieval chunks as run."
            " You need to process the doc_ids outside of this function to get the same results."
        )
        repo, rec = self.all_patches[index]
        documents = self.all_documents_by_repo[repo]
        model_input: ModelInput = self.create_model_input(rec, [])
        return model_input, documents

    def __len__(self) -> int:
        """Return the total number of examples in this task."""
        return len(self.all_patches)

    def create_cc_eval_score(self, out: _PerRepoResult) -> CCEvalScore:
        rec, mi, result, forward_metrics, artifacts = out
        score_em_strict = compute_exact_match(
            mi.extra["ground_truth"], [result.generated_text]
        )
        score_es = compute_edit_similarity(
            mi.extra["ground_truth"], [result.generated_text]
        )
        lang = extract_lang_from_rec(rec)
        norm_generated = postprocess_code_lines(result.generated_text, lang)
        norm_gt = postprocess_code_lines(mi.extra["ground_truth"], lang)
        score_em = norm_generated == norm_gt
        score_ast = (
            compare_asts(
                mi.prefix + result.generated_text + mi.suffix,
                mi.prefix + mi.extra["ground_truth"] + mi.suffix,
                lang,
                path=Path(mi.path),
                ignore_comments=False,
            )
            or False
        )
        score_em_1_line = get_first_n_lines(norm_generated, n=1).rstrip("\n") == (
            get_first_n_lines(norm_gt, n=1).rstrip("\n")
        )
        score = CCEvalScore(
            exact_match=score_em,
            exact_match_strict=score_em_strict,
            exact_match_1_line=score_em_1_line,
            edit_similarity=score_es,
            ground_truth_log_likelihood=forward_metrics.log_likelihood,
            ground_truth_token_accuracy=forward_metrics.token_accuracy,
            ast_match=score_ast,
            samples=1,
        )
        return score

    def run(
        self,
        system: CodeCompleteSystem,
        output_path: Union[str, Path],
        output_prefix: str = "",
    ) -> dict:
        """Run the task with the given system and save the results into output_path/output_prefix_xxx."""

        start_timestamp = time.time()
        start_time = time.monotonic()

        output_path = Path(output_path)
        output_path.mkdir(exist_ok=True, parents=True)
        logger.info(f"Save results into {output_path}")

        all_patches = self.all_patches

        def calc_avg(scores: list[float]):
            return sum(scores) / len(scores)

        def calc_avg_scores(scores: list[CCEvalScore]) -> CCEvalScore:
            return CCEvalScore(
                exact_match=calc_avg([x.exact_match for x in scores]),
                exact_match_strict=calc_avg([x.exact_match_strict for x in scores]),
                exact_match_1_line=calc_avg([x.exact_match_1_line for x in scores]),
                edit_similarity=calc_avg([x.edit_similarity for x in scores]),
                ground_truth_log_likelihood=calc_avg(
                    [x.ground_truth_log_likelihood for x in scores]
                ),
                ground_truth_token_accuracy=calc_avg(
                    [x.ground_truth_token_accuracy for x in scores]
                ),
                ast_match=calc_avg([x.ast_match for x in scores]),
                samples=len(scores),
            )

        def formatted_score(lang, score: CCEvalScore) -> str:
            return " ".join(
                [
                    f" {lang:<10}",
                    f"em={score.exact_match:.3f}",
                    f"em_strict={score.exact_match_strict:.3f}",
                    f"es={score.edit_similarity:.3f}",
                    f"ll={score.ground_truth_log_likelihood:.3f}",
                    f"acc={score.ground_truth_token_accuracy:.3f}",
                    f"am={score.ast_match:.3f}",
                    f"samples={score.samples}",
                ]
            )

        per_repo_results = defaultdict[str, list[_PerRepoResult]](list)
        documents: list[Document] = []
        last_repo = None
        per_repo_scores: dict[str, list[InternalResultT]] = defaultdict(list)
        per_lang_scores: dict[str, list[CCEvalScore]] = defaultdict(list)
        for idx, (repo, rec) in enumerate(all_patches):
            if last_repo != repo:
                logger.info(f"Indexing repo {repo}...")
                # TODO(rich): Consider having a retriever context that automatically
                # clears the retriever, since it's error prone to remember to clear
                # the retriever on every loop.
                documents = self.all_documents_by_repo[repo]
                system.clear_retriever()
                system.add_docs(documents)
                last_repo = repo

            # Add modified prompt document to the system
            logger.debug(f"Processing patch with path {rec.metadata.file}")
            new_content = rec.prompt + rec.right_context
            new_doc = Document.new(text=new_content, path=rec.metadata.file)
            system.add_docs([new_doc])

            # Replace the original document with our modified prompt doc in the
            # list of documents to retrieve from
            new_doc_ids = {x.id for x in documents if x.path != new_doc.path}
            new_doc_ids.add(new_doc.id)
            assert len(new_doc_ids) == len(documents)
            # Ensure that we're including only those doc ids that have been
            # added and not filtered due to size.
            new_doc_ids.intersection_update(system.get_doc_ids())

            # Specify the doc list from which to retrieve
            model_input: ModelInput = self.create_model_input(rec, list(new_doc_ids))

            with collect_artifacts() as collector:
                result: CompletionResult = system.generate(model_input)
                artifacts = collector.get_artifacts()
            forward_metrics = safe_forward_metrics(system, model_input)
            out = _PerRepoResult(rec, model_input, result, forward_metrics, artifacts)
            per_repo_results[repo].append(out)
            # Logging the progress
            patch_progress = f"{idx+1}/{len(all_patches)}"
            logger.info(f"Processed {patch_progress} patches")

            # Compute the score
            score = self.create_cc_eval_score(out)
            rec, mi, result, forward_metrics, artifacts = out
            per_repo_scores[repo].append((rec, mi, result, artifacts, score))
            lang = extract_lang_from_rec(rec)
            per_lang_scores[lang].append(score)

            if self.score_interval and (idx + 1) % self.score_interval == 0:
                # Aggregate scores per language
                agg_scores_per_language = {}
                for lang in per_lang_scores:
                    agg_scores_per_language[lang] = calc_avg_scores(
                        per_lang_scores[lang]
                    )

                # Log aggregated scores per language
                output_string = f"Scores after {patch_progress} patches\n"
                for lang in sorted(agg_scores_per_language.keys()):
                    output_string += (
                        formatted_score(lang, agg_scores_per_language[lang]) + "\n"
                    )

                # Aggregate total score
                total = []
                for lang in per_lang_scores:
                    total.extend(per_lang_scores[lang])
                total_score = calc_avg_scores(total)
                output_string += formatted_score("Total", total_score) + "\n"
                logger.info(output_string)

        # Aggregate scores per language
        agg_scores_per_language = {}
        for lang in per_lang_scores:
            agg_scores_per_language[lang] = calc_avg_scores(per_lang_scores[lang])

        # Log aggregated scores per language
        logger.info("Final Scores")
        for lang in sorted(agg_scores_per_language.keys()):
            logger.info(formatted_score(lang, agg_scores_per_language[lang]))

        total = []
        for lang in per_lang_scores:
            total.extend(per_lang_scores[lang])
        total_score = calc_avg_scores(total)
        logger.info(formatted_score("Total", total_score))

        output_results = list[CCEvalOutput]()
        for repo in per_repo_scores:
            for rec, mi, result, artifacts, score in per_repo_scores[repo]:
                output_results.append(
                    CCEvalOutput(
                        prefix=mi.prefix,
                        suffix=mi.suffix,
                        prompt=system.get_model().tokenizer.detokenize(
                            result.prompt_tokens
                        ),
                        prompt_tokens=result.prompt_tokens,
                        generation=result.generated_text,
                        ground_truth=mi.extra["ground_truth"],
                        patch_id=mi.extra["patch_id"],
                        repository=rec.metadata.repository,
                        metrics=score,
                        artifacts=artifacts,
                    )
                )

        if output_path.exists():
            # where does file_name come from?
            file_name = f"{system.get_model().name}_{self.name}"
            pathname = Path(
                output_path / f"{output_prefix}{file_name}_completed_patches.jsonl.zst"
            )
            logger.info(f"Writing results to pathname {pathname}")

            def save_artifact(docs: list[CCEvalOutput]):
                recs = [asdict(doc) for doc in docs]
                utils.write_jsonl_zst(pathname, recs)
                return pathname

            handle_artifacts = save_artifact
        else:

            def print_artifact(docs):
                print(docs)
                return ""

            handle_artifacts = print_artifact

        out_file = handle_artifacts(output_results)

        end_time = time.monotonic()

        return {
            "artifact": str(out_file),
            "metrics": {
                "per_language": {
                    lang: asdict(score)
                    for lang, score in agg_scores_per_language.items()
                },
                "total": asdict(total_score),
            },
            "variant": self.variant.value,
            "limit": self.limit,
            "start_time": start_timestamp,
            "run_duration": end_time - start_time,
            "labels": self.result_labels if self.result_labels else {},
        }

    @classmethod
    def from_yaml_config(cls, config: dict) -> "CCEval":
        """Returns a Task object constructed using a config dictionary."""
        limit: Optional[int] = config.get("limit", None)
        score_interval: Optional[int] = config.get("score_interval", None)
        result_labels: Optional[dict[str, str]] = config.get("result_labels", None)
        dataset_path: Optional[str] = config.get("dataset_path", None)
        variant_str: str = config.get("variant", CCEval.Variant.MULTI)
        variant = CCEval.Variant(variant_str)
        unit_test_only = config.get("unit_test_only", False)
        return CCEval(
            limit=limit,
            score_interval=score_interval,
            dataset_path=dataset_path,
            variant=variant,
            result_labels=result_labels,
            unit_test_only=unit_test_only,
        )

    def create_model_input(self, rec: CCEvalInput, doc_ids: list[str]) -> ModelInput:
        # Generate the patch
        model_input = ModelInput(
            prefix=rec.prompt,
            suffix=rec.right_context,
            path=rec.metadata.file,
            target=rec.groundtruth,
            doc_ids=doc_ids,
            cursor_position=len(rec.prompt),
            extra={
                "patch_id": rec.metadata.task_id,
                "ground_truth": rec.groundtruth,
            },
        )
        return model_input


def _prepare_dataset(dataset_path: Path):
    """Returns the dataset and prompts in a deterministic order.

    Returns a tuple of ('prompts', 'documents'), where 'prompts' maps repo names
    to a list of input prompts, and 'documents' maps repo names to a list of
    documents for retrieval.
    """
    logger.info(f"Load data from {dataset_path}")
    if not Path(dataset_path).exists():
        raise ValueError(f"Dataset path {dataset_path} does not exist.")

    all_patches_by_repo: dict[str, list[CCEvalInput]] = {}
    all_documents_by_repo: dict[str, list[Document]] = {}

    # Note each directory contains a single repo's worth of patches and
    # documents. Because the glob is non-deterministic, we sort + shuffle the
    # repos after loading to provide a determinstic order.
    for patches_path in dataset_path.glob("*/*_patches.jsonl"):
        assert patches_path.is_file(), f"{patches_path} is not a file"
        patches = [create_from_dict(CCEvalInput, x) for x in read_jsonl(patches_path)]

        all_repo_names = [patch.metadata.repository for patch in patches]
        repo_name = patches_path.name.rsplit("_patches.jsonl")[0]
        org_repo = patches_path.parent.name + "/" + repo_name
        assert set(all_repo_names) == set(
            [org_repo]
        ), "The dataset should only contain one repo"

        docs_filename = patches_path.name.replace(
            "_patches.jsonl", "_retrieval_db.jsonl"
        )
        docs_path = patches_path.parent / docs_filename
        assert docs_path.is_file(), f"{docs_path} is not a file"
        documents = read_jsonl(docs_path)
        documents = [Document(**doc) for doc in documents]

        all_patches_by_repo.update({org_repo: patches})
        all_documents_by_repo.update({org_repo: documents})

    # Ensure a consistent ordering of the repos by a deterministic shuffle.
    # People may be running this task for a limited number of patches, so
    # changing the order will affect their results.
    random.seed(42)
    ordered_repo_names = sorted(all_patches_by_repo.keys())
    random.shuffle(ordered_repo_names)

    # Note that insertion order is preserved in Python 3.7+
    all_patches_by_repo = {
        repo: all_patches_by_repo[repo] for repo in ordered_repo_names
    }
    all_documents_by_repo = {
        repo: all_documents_by_repo[repo] for repo in ordered_repo_names
    }

    # Filter out files in .git directories
    for repo in all_documents_by_repo:
        orig_docs = all_documents_by_repo[repo]
        documents = _filter_out_git_files(orig_docs)
        count = len(orig_docs) - len(documents)
        logger.info(
            f"Repo {repo}: filtered {count} .git files from {len(orig_docs)} docs, {len(documents)} remaining."
        )
        all_documents_by_repo[repo] = documents

    return all_patches_by_repo, all_documents_by_repo


def _combine_dataset(
    all_patches_by_repo: dict[str, list[CCEvalInput]],
    all_documents_by_repo: dict[str, list[Document]],
) -> tuple[dict[str, list[CCEvalInput]], dict[str, list[Document]]]:
    """Given the dataset split by repository, combine all into a single repository.

    For each document and patch, we add its respective repo name as a prefix to
    the path. This means, for example, that a path like 'src/foo.py' will
    become 'Azure/azure-sdk-for-js/src/foo.py'. The documents and patches will
    then be returned as a single, unified repository.
    """

    # The single, meta-repo name
    unified_repo_name = "workspace"

    combined_patches_by_repo: dict[str, list[CCEvalInput]] = {unified_repo_name: []}
    for repo, patches in all_patches_by_repo.items():
        # Prefixing paths in patch metadata with the original repository name
        for patch in patches:
            patch.metadata.file = repo + "/" + patch.metadata.file

        combined_patches_by_repo[unified_repo_name].extend(patches)

    combined_documents_by_repo: dict[str, list[Document]] = {unified_repo_name: []}
    for repo, docs in all_documents_by_repo.items():
        # Prefixing document paths with the original repository name
        assert all([doc.path for doc in docs])
        updated_docs = [
            Document.new(text=doc.text, path=repo + "/" + doc.path) for doc in docs
        ]

        # Instead of separating by repository, we aggregate all patches and documents.
        combined_documents_by_repo[unified_repo_name].extend(updated_docs)

    return combined_patches_by_repo, combined_documents_by_repo


def _filter_out_git_files(documents: Iterable[Document]) -> list[Document]:
    """Filter out those documents in .git directories.

    Note the order of documents is preserved.
    """
    # Returns list of documents without .git directories
    docs_without_git = []
    for doc in documents:
        logger.debug(f"Document has path {doc.path}")
        assert doc.path is not None
        if ".git" in PurePath(doc.path).parts:
            logger.debug(f"Skipping {doc.path}")
            continue
        docs_without_git.append(doc)

    return docs_without_git

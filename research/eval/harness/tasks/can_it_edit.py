"""The CanItEdit Task.

The dataset consists of curated code-instruction tasks in Python,
along with unit tests for measuring accuracy. This task is similar
to human eval, except we use a code-instruction system to execute
the instruction and generate code.

There are two variants of this task:
- descriptive: the instruction is a more descriptive, and less likely to what a
 human would type.
- lazy: the instruction is less descriptive, and closer to what a human is
 likely to type.

See https://arxiv.org/abs/2312.12450 for information about this task.
"""

from __future__ import annotations

import collections
import dataclasses
import logging
import pathlib
from enum import Enum
from typing import Optional

import numpy as np

from research.core.data_paths import canonicalize_path
from research.eval.generation import evaluation, execution
from research.eval.harness import utils
from research.eval.harness.metrics import (
    compute_edit_similarity,
    compute_exact_match,
    safe_edits_forward_metrics,
)
from research.eval.harness.systems import abs_system
from research.eval.harness.tasks.abs_task import CodeInstructTask


# TODO(rich): this is very similar to the Metrics class in the PinocchioTask.
# Maybe we should converge on a single Metrics class.
@dataclasses.dataclass
class Scores:
    """Schema for recording per-patch metrics in artifact file."""

    exact_match: float
    edit_similarity: float
    log_likelihood: float
    token_accuracy: float
    count: int = 1


def aggregate_scores(scores: list[Scores]) -> Scores:
    return Scores(
        float(np.mean([x.exact_match for x in scores])),
        float(np.mean([x.edit_similarity for x in scores])),
        float(np.mean([x.log_likelihood for x in scores])),
        float(np.mean([x.token_accuracy for x in scores])),
        # convert the count to an int, to keep it json serializable
        int(np.sum([x.count for x in scores])),
    )


@dataclasses.dataclass
class CanItEditOutput:
    """Schema for recording output information in artifact file."""

    patch: dict
    """The Patch object from the task's dataset, representing the corruption site."""

    prefix: str
    """The prefix string for the task."""

    suffix: str
    """The suffix string for the task."""

    prompt: str
    """The actual prompt used for generation.

    The prefix, suffix, and chunks may be changed to form the actual prompt,
    based on budget constraints.
    """

    generation: str
    """The full output text from generation."""

    metrics: Scores
    """Metrics for the task."""

    exec_metrics: Optional[dict] = None
    """Additional metrics from executing the code."""


class CanItEdit(CodeInstructTask):
    """The CanItEdit Task."""

    class VariantType(Enum):
        """Variant types for the CanItEdit task."""

        DESCRIPTIVE = "descriptive"
        LAZY = "lazy"

    def __init__(
        self,
        iterations: int = 1,
        limit: Optional[int] = None,
        execute: bool = True,
        variant: str = "descriptive",
    ):
        self.json_input = pathlib.Path(
            canonicalize_path("data/eval/can_it_edit/dataset.v2.jsonl")
        )
        self.iterations = iterations
        self.limit = limit
        self.exec = execute
        if variant in ["descriptive", "lazy"]:
            self.variant = CanItEdit.VariantType(variant.lower())
        else:
            raise ValueError(f"Did not find {variant} in [descriptive, lazy]")

    def __getitem__(self, index: int):
        raise NotImplementedError()

    def __len__(self) -> int:
        raise NotImplementedError()

    def run(
        self,
        system: abs_system.CodeInstructSystem,
        output_path: str | pathlib.Path,
        output_prefix: str = "",
    ) -> dict:
        """Run the task against the provided system."""

        output_path = pathlib.Path(output_path)

        # Example JSON record format
        # pylint: disable-next=pointless-string-statement
        r"""
        {"id":10,
         "name":"csv_parser",
         "full_name":"10_csv_parser",
         "before":"class CSVParser:\n    ...",
         "after":"class CSVParser:\n    ...",
         "tests":"if True:  # pragma: no cover...",
         "instruction_descriptive":"Add a function called `header` which returns the first row of a csv file as a list of strings, where\nevery element in the list is a column in the row.",
         "taxonomy":{"change_kind":"evolve","libraries":[],"topic":"Language"}
         "instruction_lazy":"Add a method called `header` which returns the header of a csv file as a list",
         }
        """

        # read a jsonl file based on the above example, and create a model input for a code edit system
        patches = utils.read_jsonl(self.json_input)
        reject_list = [
            "59_standard_scaling",  # fails, Setting with Copy Warning
            "78_llm_inference",  # uses vllm
            "54_strategy",  # uses inpsect
            # uses inspect.getsource(sys.modules[__name__]) in the test, which does not work with the sandbox
            "38_high_order",
            # Want to run a process through Popen
            "61_ridge_regression",
            "88_correlation_clustering",
            "95_dbscan",
            "96_distribution_clustering",
            "118_principal_component_analysis",
        ]
        patches = [patch for patch in patches if patch["full_name"] not in reject_list]

        model_inputs = list[abs_system.CodeInstructInput]()

        if self.variant == CanItEdit.VariantType.DESCRIPTIVE:
            instruction_key = "instruction_descriptive"
        elif self.variant == CanItEdit.VariantType.LAZY:
            instruction_key = "instruction_lazy"
        else:
            raise ValueError(f"Unrecognized variant type of {self.variant}")

        for patch in patches:
            model_input = abs_system.CodeInstructInput(
                path="",
                prefix="",
                suffix="",
                selected_code=patch["before"],
                updated_code=patch["after"],
                instruction=patch[instruction_key],
                # TODO(rich): A tuple should be supported here.
                retrieved_chunks=list(),
            )

            # We stuff the tests field here for convenience when calling the execution method, but it should
            # really live in its own argument
            model_input.extra["tests"] = patch["tests"]

            model_inputs.append(model_input)

        results: list[Optional[abs_system.CompletionResult]] = []
        metrics: list[Scores] = []
        for patch, model_input in zip(patches, model_inputs):
            try:
                result = system.generate(model_input)
                generated_text = result.generated_text if result is not None else ""
                assert model_input.updated_code is not None

                score_em = compute_exact_match(
                    model_input.updated_code, [result.generated_text]
                )
                score_es = compute_edit_similarity(
                    model_input.updated_code, [result.generated_text]
                )
                forward_metrics = safe_edits_forward_metrics(system, model_input)
                score = Scores(
                    score_em,
                    score_es,
                    forward_metrics.log_likelihood,
                    forward_metrics.token_accuracy,
                )

                results.append(result)
                metrics.append(score)
            except ValueError as e:
                logging.warning(f"Caught {e}: on {patch['full_name']}")
                results.append(None)
                metrics.append(Scores(0, 0, 0, 0))

        exec_results: list[Optional[dict]] = []
        for model_input, result in zip(model_inputs, results):
            generated_text = result.generated_text if result is not None else ""
            if self.exec:
                exec_result = self.execute(model_input, generated_text)
            else:
                exec_result = None
            exec_results.append(exec_result)

        # TODO(rich): Remote systems do not have access to model and tokenizer.
        # prompts: list[str] = [system.model.tokenizer.detokenize(x.prompt_tokens) for x in results]
        prompts: list[str] = [x["before"] for x in patches]

        completed_patches_for_analytics = []

        for rec in zip(patches, model_inputs, prompts, results, metrics, exec_results):
            patch, model_input, prompt, result, metric, exec_result = rec

            completed_patches_for_analytics.append(
                CanItEditOutput(
                    patch=patch,
                    prefix=model_input.prefix,
                    suffix=model_input.suffix,
                    prompt=prompt,
                    generation=result.generated_text if result is not None else "",
                    metrics=metric,
                    exec_metrics=exec_result,
                )
            )

        # Output

        def print_artifact(docs):
            print(docs)
            return ""

        def save_artifact(docs: list[CanItEditOutput]):
            # TODO(rich) model.name is not available from the remote system
            # file_name = f"{system.model.name}_{self.name}"
            file_name = f"can_it_edit_{self.name}"
            pathname = pathlib.Path(
                output_path / f"{output_prefix}{file_name}_completed_patches.jsonl.zst"
            )
            logging.info(f"Writing results to pathname {pathname}")

            recs = [dataclasses.asdict(doc) for doc in docs]
            utils.write_jsonl_zst(pathname, recs)
            return pathname

        handle_artifacts = save_artifact if output_path.exists() else print_artifact
        out_file = handle_artifacts(completed_patches_for_analytics)

        avg = aggregate_scores(metrics)
        pass_at_k = {}
        if self.exec:
            docs_by_task = collections.defaultdict(list)
            for idx, doc in enumerate(completed_patches_for_analytics):
                docs_by_task[doc.patch["full_name"]].append((idx, doc.exec_metrics))

            pass_at_k = evaluation.calc_pass_at_k(docs_by_task, (1, 10, 100))
            logging.debug(f"Pass@k: {pass_at_k}")

        return {
            "artifact": str(out_file),
            "metrics": dataclasses.asdict(avg),
            "exec_metrics": {"pass_at_k": pass_at_k},
        }

    def execute(
        self,
        model_input: abs_system.CodeInstructInput,
        generation: str,
        timeout: Optional[float] = 120.0,
    ) -> dict:
        """Execute the codes derived from the model input and generation and return the feedbacks."""
        if timeout is None:
            timeout = 3600.0  # if timeout is None, we set one hour as timeout.

        code = "\n".join([generation, model_input.extra["tests"]])

        raw_results, _, _ = execution.sandbox_execute(code, timeout)
        if raw_results == "passed":
            xpass, error_info = True, None
        else:
            xpass, error_info = False, raw_results
            logging.debug(f"Failed to execute code: {code} \n {raw_results}")

        return {
            "code": code,
            "passed": xpass,
            "error_info": error_info,
            "raw_results": raw_results,
        }

    @classmethod
    def from_yaml_config(cls, config: dict) -> "CanItEdit":
        """Returns a Task object constructed using a config dictionary."""
        iterations = config.get("iterations", 1)
        limit = config.get("limit", None)
        execute = config.get("exec", True)
        variant = config.get("variant", "descriptive")
        variant = variant.lower()
        return cls(iterations=iterations, limit=limit, execute=execute, variant=variant)

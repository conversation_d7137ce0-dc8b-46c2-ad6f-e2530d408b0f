from collections import Counter, defaultdict
from collections.abc import Iterable
import dataclasses
from itertools import accumulate
import pickle
import random
import time
from typing import Any, Optional, Self, Sequence

import logging
from tqdm import tqdm

from dataclasses_json import dataclass_json

from base.blob_names.python.blob_names import <PERSON><PERSON><PERSON><PERSON><PERSON>, FilePath
from base.datasets.gcs_blob_cache import GCSBlobCache, PathAndContent
from base.diff_utils.edit_events import (
    GranularEditEvent,
    SquashableEdits,
)
from base.logging.secret_logging import UnsafeLogger
from base.ranges.range_types import CharRange
from base.caching.cache import InsertStats
from base.datasets.hindsight_next_edit import NextEditIntermediateType
from base.datasets.hindsight_next_edit_intermediate_dataset import (
    DatasetWithGroundTruths,
    GroundTruthEdit,
)
from base.retrieval.chunking.line_based_chunking import LineChunkContents
from base.retrieval.chunking.smart_chunking import SmartChunker
from base.datasets import next_edit
from base.datasets.replay_utils import get_blob_cache
from base.datasets.tenants import DatasetTenant, get_tenant
from base.diff_utils.diff_utils import File
from base.static_analysis.common import groupby
from research.utils.sampling_utils import downsample_to
from research.core.types import Document, NTuple, Scored
from research.eval.harness import utils
from research.eval.harness.systems.next_edit_location_system import (
    NextEditLocationOutput,
    NextEditLocationSystemInput,
)
from research.eval.harness.systems.next_edit_reranker_system import (
    NextEditRerankerSystem,
)
from research.eval.harness.tasks.abs_task import AbstractTask
from dataclasses import asdict, dataclass, field
from pathlib import Path
from research.eval.harness.tasks.next_edit_location_eval_task import (
    NextEditLocationSystem,
)
from research.next_edits.diagnostics import Diagnostic, DiagnosticFileLocation
from research.core.next_edit_location_prompt_input import (
    FileLocation,
    NextEditLocationLabel,
)
from research.eval.harness.utils import read_jsonl_zst

logger = logging.getLogger(__name__)


@dataclass
class NextEditHindsightLocationOutput(NextEditLocationOutput):
    """A subclass of the NextEditLocationOutput that includes the ground truth."""

    ground_truth: GroundTruthEdit
    """The ground truth for the prompt."""


@dataclass
class NextEditHindsightLocationDiagnosticsOutput(NextEditHindsightLocationOutput):
    """A subclass of the NextEditHindsightLocationOutput that includes the diagnostics."""

    cursor_path: FilePath
    """The path of the cursor."""

    diagnostics: NTuple[Diagnostic]
    """The diagnostics for the prompt."""


@dataclass_json
@dataclass
class NextEditHindsightTaskInput:
    prompt: NextEditLocationSystemInput
    """The prompt."""

    documents: list[Document]
    """The documents."""

    squashable_edits: SquashableEdits
    """The events where already squash into the recent changes intside the prompt.
    We save the raw edits to disk to allow downstream experimentation with the squashing parameters."""

    ground_truth: GroundTruthEdit
    """The ground truth for the prompt.
    Within the hindsight eval, we use this field instead of the
    'label' field inside the prompt. The label was used by the previous eval
    but doesn't make sense to repurpose for this eval"""

    id: str = ""
    """The id of the example."""


@dataclass
class NextEditHindsightMetrics:
    _current_file_recall_counter: Counter[int] = field(default_factory=Counter)
    """The recall counter for when the ground truth is in the current file."""

    _non_current_file_recall_counter: Counter[int] = field(default_factory=Counter)
    """The recall counter when the ground truth is *not* in the current file."""

    def update(
        self,
        cursor_path: FilePath,
        scored_locations: Sequence[Scored[FileLocation]],
        ground_truth: GroundTruthEdit,
    ):
        """Update the metrics with the given cursor position and scored locations."""
        touches: Counter[int] = count_touches(scored_locations, ground_truth)

        if cursor_path == ground_truth.path:
            # If the cursor is in a different file, we consider it far away.
            self._current_file_recall_counter += touches
        else:
            self._non_current_file_recall_counter += touches

    def get_current_file_recall(self) -> dict[int, float]:
        """Return the recall at each k for locations close to the cursor."""
        return counter_to_cumulative_dict(self._current_file_recall_counter)

    def get_non_current_file_recall(self) -> dict[int, float]:
        """Return the recall at each k for locations far from the cursor."""
        return counter_to_cumulative_dict(self._non_current_file_recall_counter)

    def write_metric_file(self, path: Path, file_prefix: str = "") -> None:
        """Write the metrics to a .jsonl.zst file."""
        logger.info(f"Writing metrics to {path}")

        if not path.exists():
            logger.warning(f"Metrics output path {path} does not exist. Skipping.")
            return

        out_file: Path = (
            Path(path / f"{file_prefix}_metrics.jsonl.zst")
            if file_prefix
            else Path(path / "metrics.jsonl.zst")
        )

        metric_data: list[dict[str, Any]] = [
            {"current_file_count": self._current_file_recall_counter.total()},
            {"non_current_file_count": self._non_current_file_recall_counter.total()},
            {"current_file_recall": list(self.get_current_file_recall().values())},
            {
                "non_current_file_recall": list(
                    self.get_non_current_file_recall().values()
                )
            },
        ]

        utils.write_jsonl_zst(out_file, metric_data)

        logger.info(f"Finished writing metrics to {out_file}")


class NextEditHindsightLocationEvalTask(
    AbstractTask[NextEditLocationSystemInput, NextEditLocationOutput]
):
    """The next edit location eval task using hindsight data."""

    def __init__(
        self,
        datset_path: Path | str,
        limit_examples: Optional[int] = None,
        top_ks: Sequence[int] = (1, 3, 8, 32),
        drop_instructions: bool = False,
    ):
        """Create the task.

        Args:
            dataset_path: The path to pickled list of NextEditHindsightTaskInput.
            limit_examples: The maximum number of examples to load.
            top_ks: The number of candidates to generate and score.
            drop_instruction: Whether to drop the instruction and instruction-only
                samples.
        """
        assert top_ks, "Must have at least one top-k value."
        self._dataset_path = datset_path
        self._limit_examples = limit_examples
        self._top_ks = tuple(sorted(top_ks))
        self._drop_instructions = drop_instructions
        self._metrics = NextEditHindsightMetrics()

    def _load_dataset(self) -> Iterable[NextEditHindsightTaskInput]:
        """Load DatasetWithGroundTruths from the given path.

        The dataset is assumed to be stored in a pickle file.

        Args:
            path: The path to pickled DatasetWithGroundTruths file.
            limit: If set, only load this many examples.

        Returns:
            A dataset of examples for the next edit location task.
        """
        path = Path(self._dataset_path)
        if path.suffix == ".pkl":
            logger.info(f"Loading pickled dataset from {path}...")
            try:
                with open(path, "rb") as f:
                    task_inputs: list[NextEditHindsightTaskInput] = pickle.load(
                        f
                    )  # Already in NextEditHindsightTaskInput format
                    return task_inputs[: self._limit_examples]
            except Exception as e:
                logger.error(f"Failed to load pickled dataset from {path}. Error: {e}")
                raise
        else:
            raise ValueError(f"Unsupported file format: {path.suffix}")

    def run(
        self,
        system: NextEditLocationSystem,
        output_path: Path | str,
        output_prefix: str = "",
        run_with_diagnostics: bool = False,
    ) -> dict:
        """Run the task against the provided system."""
        task_inputs: list[NextEditHindsightTaskInput] = list(self._load_dataset())
        logger.info(f"Loaded {len(task_inputs)} examples.")

        output_path = Path(output_path)
        output_path.mkdir(exist_ok=True, parents=True)
        logger.info(f"Save results into {output_path}")

        # id's of files that have already been seen by the system
        added_doc_ids = set[str]()

        # the outputs generated by the system
        outputs: list[NextEditHindsightLocationDiagnosticsOutput] = []
        if isinstance(system, NextEditRerankerSystem):
            logger.info("Running with reranker system.")
        else:
            logger.info("Running without reranker system.")

        for task_input in tqdm(
            task_inputs, desc="Generating model outputs", smoothing=0
        ):
            new_docs: list[Document] = [
                doc for doc in task_input.documents if doc.id not in added_doc_ids
            ]
            system.add_docs(new_docs)
            added_doc_ids.update(doc.id for doc in new_docs)

            result: NextEditLocationOutput = self._get_location_output(
                system, task_input.prompt
            )

            diagnostics: NTuple[Diagnostic] = ()
            if run_with_diagnostics:
                diagnostics = task_input.prompt.diagnostics

            self._metrics.update(
                cursor_path=task_input.prompt.current_file.path,
                scored_locations=result.scored_candidates,
                ground_truth=task_input.ground_truth,
            )

            outputs.append(
                NextEditHindsightLocationDiagnosticsOutput(
                    cursor_path=task_input.prompt.current_file.path,
                    scored_candidates=result.scored_candidates[: max(self._top_ks)],
                    debug_info=result.debug_info,
                    ground_truth=task_input.ground_truth,
                    diagnostics=diagnostics,
                )
            )

        # write the output to a file
        self.write_output_file(outputs, output_path, output_prefix)
        self._metrics.write_metric_file(output_path, output_prefix)

        return {
            "current_file_count": self._metrics._current_file_recall_counter.total(),
            "non_current_file_count": self._metrics._non_current_file_recall_counter.total(),
            "current_file_recall": [
                f"{k}={self._metrics.get_current_file_recall()[k]:.4f}"
                for k in self._top_ks
            ],
            "non_current_file_recall": [
                f"{k}={self._metrics.get_non_current_file_recall()[k]:.4f}"
                for k in self._top_ks
            ],
        }

    def _get_location_output(
        self, system: NextEditLocationSystem, prompt: NextEditLocationSystemInput
    ) -> NextEditLocationOutput:
        if isinstance(system, NextEditRerankerSystem):
            # For reranker system, we want to rerank the top_k locations and then just grab the rest from before_reranking.
            assert system.record_all_locations, "We need to record all locations to run this eval with a reranker, in order to compute recall."
            prompt = dataclasses.replace(prompt, top_k=max(self._top_ks))
            assert isinstance(prompt, NextEditLocationSystemInput), type(prompt)
            result = system.generate(prompt)
            before_reranking = result.reranker_debug_info.before_reranking
            result.scored_candidates = list(result.scored_candidates) + list(
                before_reranking[len(result.scored_candidates) :]
            )
        else:
            # For non-reranker system, we want to grab all locations, so that we can compute recall.
            prompt = dataclasses.replace(prompt, top_k=None)
            assert isinstance(prompt, NextEditLocationSystemInput), type(prompt)
            result = system.generate(prompt)

        return result

    def write_output_file(
        self,
        output: Sequence[NextEditHindsightLocationDiagnosticsOutput],
        output_path: Path,
        output_prefix: str,
    ):
        if not output_path.exists():
            logger.warning(f"Output path {output_path} does not exist. Skipping.")
            return None

        file_name = f"next_edit_hindsight_location_{self.name}"
        out_file = Path(output_path / f"{output_prefix}{file_name}_output.pkl")
        logger.info(f"Writing results to {out_file}")
        with open(out_file, "wb") as f:
            pickle.dump(output, f)
        return out_file

    @classmethod
    def from_yaml_config(cls, config: dict) -> Self:
        """Returns a NextEditLocationEvalTask constructed using a config dictionary."""
        return cls(**config)

    def __len__(self) -> int:
        raise NotImplementedError()

    def execute(
        self,
        model_input: NextEditLocationSystemInput,
        generation: str,
        timeout: Optional[float] = None,
    ) -> dict:
        raise NotImplementedError()

    def __getitem__(self, index: int):
        raise NotImplementedError()


def convert_dataset_with_ground_truths_to_task_inputs(
    input_path: Path | str,
    output_path: Path | str,
    limit_examples: Optional[int] = None,
    service_account_file: Optional[Path] = None,
    blob_cache_size_bytes: int = 10 * 2**30,
    blob_cache_num_threads: int = 32,
):
    """Convert a DatasetWithGroundTruths into a sequence of next edit task inputs."""
    path = Path(input_path)

    # Check if output is already stored
    if Path(output_path).exists():
        logger.info(f"Output path {output_path} already exists. Skipping.")
        return

    if path.suffix == ".pkl":
        logger.info(f"Loading pickled dataset from {path}...")
        with open(path, "rb") as f:
            dataset: DatasetWithGroundTruths = pickle.load(f)
            tenant: DatasetTenant = get_tenant(dataset.tenant_name)

            # limit the number of datapoints
            if limit_examples is not None:
                dataset.data = downsample_to(
                    random.Random(42), dataset.data, limit_examples
                )

            # sort by timestamp
            grouped_data_by_session = groupby(
                dataset.data, keyfunc=lambda x: x[0].session_id
            )
            # make a list
            dataset.data = [
                item for group in grouped_data_by_session.values() for item in group
            ]

            task_inputs: list[NextEditHindsightTaskInput] = list(
                convert_labeled_hindsight_data_to_task_inputs(
                    data=dataset.data,
                    dataset_tenant=tenant,
                    service_account_file=service_account_file,
                    blob_cache_size_bytes=blob_cache_size_bytes,
                    blob_cache_num_threads=blob_cache_num_threads,
                )
            )

        logger.info(f"Saving task inputs to {output_path}...")
        with open(output_path, "wb") as f:
            pickle.dump(task_inputs, f)
            logger.info("Tasks saved!")
    else:
        raise ValueError(f"Unsupported file format: {path.suffix}")


def convert_labeled_hindsight_data_to_task_inputs(
    data: Sequence[tuple[NextEditIntermediateType, GroundTruthEdit]],
    dataset_tenant: DatasetTenant,
    service_account_file: Optional[Path] = None,
    blob_cache_size_bytes: int = 2**30,
    blob_cache_num_threads: int = 32,
) -> Iterable[NextEditHindsightTaskInput]:
    """Convert a sequence of NextEditIntermediateType into a sequence of next edit task inputs.

    Args:
        intermediate_types: A sequence of NextEditIntermediateType objects to convert.
        dataset_tenant: The dataset tenant information.
        service_account_file: Optional path to the service account file.
        blob_cache_size_bytes: The size of the blob cache in bytes.
            If the cache does not fit all the blobs, there is a risk we store duplicate blobs.
        blob_cache_num_threads: The number of threads to use for the blob cache.

    Returns:
        An iterable of NextEditHindsightTaskInput objects converted from the input intermediate types.

    """
    # sharing the blob cache is important to reference the same documents
    # referencing the same documents reduces memory usage
    blob_cache: GCSBlobCache = get_blob_cache(
        tenant=dataset_tenant,
        service_account_file=service_account_file,
        blob_cache_size_bytes=blob_cache_size_bytes,
        blob_cache_num_threads=blob_cache_num_threads,
    )

    total_insertions: int = 0
    total_skips: int = 0
    total_evictions: int = 0

    # to monitor our cache usage, we use an insert listener
    def blob_cache_insert_listener(insert_stats: InsertStats):
        nonlocal total_insertions, total_skips, total_evictions
        total_insertions += insert_stats.insertion_count
        total_skips += insert_stats.skip_count
        total_evictions += insert_stats.eviction_count

    blob_cache.set_insert_listener(blob_cache_insert_listener)
    blob_cache.logging_fn = lambda _: None

    current_evictions: int = 0
    current_skips: int = 0
    for intermediate_type, ground_truth in tqdm(
        data, desc="Converting hindsight data to task inputs", smoothing=0
    ):
        hindsight_task_input: Optional[NextEditHindsightTaskInput] = (
            get_hindsight_task_input(
                request=intermediate_type.request,
                # this is a superset of the reconstructed files
                reconstruced_files=list(intermediate_type.files_for_events.values()),
                ground_truth=ground_truth,
                blob_cache=blob_cache,
            )
        )

        if hindsight_task_input is None:
            logger.warning(
                f"Skipping example {intermediate_type.request.request_id} because we could not generate the NextEditHindsightTaskInput."
            )
        else:
            yield hindsight_task_input

        # log the number of evictions and skips when the change
        # these logs inform the user if their cache is too small
        if total_evictions > current_evictions:
            current_evictions = total_evictions
            logger.warning(
                f"Total insertions: {total_insertions}, total evictions: {total_evictions}, fraction evicted: {total_evictions / total_insertions}"
            )

        if total_skips > current_skips:
            current_skips = total_skips
            logger.warning(f"Total skips: {total_skips}")


def get_hindsight_task_input(
    request: next_edit.NextEditRequest,
    reconstruced_files: Sequence[File],
    blob_cache: GCSBlobCache,
    ground_truth: GroundTruthEdit,
) -> Optional[NextEditHindsightTaskInput]:
    """Converts a hindsight intermediate type into next edit location task input."""

    # Step 1: Get the up-to-date files for this request
    request_files: dict[BlobName, File] = get_request_files(
        blob_names=request.blob_names,
        blob_cache=blob_cache,
        reconstruced_files=reconstruced_files,
        request_id=request.request_id,
    )

    # Step 2: Get the current file
    if request.blob_name in request_files:
        # if the request knows the blob name, we use that
        current_file: File = request_files[request.blob_name]
    else:
        # else we reconstruct from prefix + selected_text + suffix
        current_file = File(
            request.path,
            request.prefix + request.selected_text + request.suffix,
        )
        # make sure the current file is part of the request files
        request_files[current_file.blob_name] = current_file

    # Step 3: Get some squahable edits
    squashable_edits: SquashableEdits = get_squashable_edits(
        edit_events=request.edit_events,
        request_files=request_files,
        request_id=request.request_id,
    )

    # Step 4: Transform one diagnostic into another
    diagnostics = tuple(
        Diagnostic(
            location=DiagnosticFileLocation(
                path=diag.location.path,
                line_start=diag.location.line_start,
                line_end=diag.location.line_end,
            ),
            message=diag.message,
            severity=diag.severity.name,
        )
        for diag in request.diagnostics
    )

    # Step 5: Get the selected range
    selected_range: CharRange = CharRange(
        request.selection_begin_char, request.selection_end_char
    )

    # Step 6: Convert the ground truth into a NextEditLocaitionLabel (for backwards compatibility)
    label: NextEditLocationLabel = NextEditLocationLabel(
        locations=(
            FileLocation(
                path=ground_truth.path,
                range=ground_truth.before_lrange,
            ),
        )
    )

    # Step 6: Convert from files to documents
    # As long as the files come from the same cache, they will be identical objects.
    # If two documents are created from the same file object, they will reference the
    # same contents and path in memory, even though they are technically different objects.
    # This will keep the memory usage low after pickling the NextEditHindsightTaskInput.
    documents: list[Document] = [
        Document(id=file.blob_name, text=file.contents, path=file.path)
        for file in request_files.values()
    ]

    # Step 7: Piece together the system input
    location_system_input: NextEditLocationSystemInput = NextEditLocationSystemInput(
        doc_ids=frozenset(
            doc.id for doc in documents
        ),  # these documents should be added to the system before generating.
        top_k=32,  # should this be a parameter somehow? Is it even looked at?.
        diagnostics=diagnostics,
        selected_range=selected_range,
        current_file=current_file,
        edit_region=selected_range,
        instruction=request.instruction,
        recent_changes=tuple(
            squashable_edits.convert_edit_events_to_modified_files(
                safe_logger=UnsafeLogger(),  # we are fine logging everything here.
            )
        ),  # this has to be a tuple bc we hash the input to see if it's cached
        label=label,
        past_diff=None,  # the 'diff' fields are only used to inspect events so we ignore them here.
        future_diff=None,
        restrict_to_file=None,  # we don't restrict to a file for now -- could be useful in the future.
    )

    # Step 8: Return the hindsight task input
    return NextEditHindsightTaskInput(
        documents=documents,
        id=request.request_id,
        prompt=location_system_input,
        squashable_edits=squashable_edits,
        ground_truth=ground_truth,
    )


def get_request_files(
    blob_names: Sequence[BlobName],
    blob_cache: GCSBlobCache,
    reconstruced_files: Sequence[File],
    request_id: str,
) -> dict[BlobName, File]:
    """Get the files for the request from the blob cache."""
    # get the blobs from the cache
    request_path_and_content: list[PathAndContent | None] = list(
        blob_cache.get(blob_names)
    )

    # map the path to its content
    path_to_content: dict[FilePath, BlobName] = {
        str(pc.path): pc.content for pc in request_path_and_content if pc is not None
    }

    # log the number of missing files
    if len(blob_names) > len(path_to_content):
        logger.warning(
            f"Missing {len(blob_names) - len(path_to_content)} blobs for request {request_id}."
        )

    # overwrite paths with reconstruced files
    for file in reconstruced_files:
        path_to_content[file.path] = file.contents

    # map blob names to files
    request_files: dict[BlobName, File] = {
        file.blob_name: file
        for path in path_to_content
        for file in [File(path, path_to_content[path])]
    }

    return request_files


def get_squashable_edits(
    edit_events: Sequence[GranularEditEvent],
    request_files: dict[BlobName, File],
    request_id: str,
) -> SquashableEdits:
    """Get the recent changes from the edit events."""
    # get the last blob name for each path in the past edit events
    # this assumes past edit events are ordered older -> newer
    path_to_after_blob_name: dict[str, BlobName] = {
        event.path: event.after_blob_name for event in edit_events
    }

    # map each path to the latest content
    path_to_expected_content: dict[str, str] = {
        file.path: file.contents
        for blob_name in path_to_after_blob_name.values()
        if (file := request_files.get(blob_name))
    }

    # log the number of missing edit event files
    num_missing_edit_event_blobs: int = len(path_to_after_blob_name) - len(
        path_to_expected_content
    )
    if num_missing_edit_event_blobs > 0:
        logger.warning(
            f"Missing {num_missing_edit_event_blobs} files for edit events in request {request_id}."
        )

    squashable_edits: SquashableEdits = SquashableEdits(
        edit_events=list(edit_events),
        path_to_current_content=path_to_expected_content,
    )

    return squashable_edits


def count_touches(
    scored_candidates: Sequence[Scored[FileLocation]],
    ground_truth: GroundTruthEdit,
) -> Counter[int]:
    """Check if the output is correct."""
    # sort candidates from highest to lowest score
    locations = [
        scored.item
        for scored in sorted(scored_candidates, key=lambda x: x.score, reverse=True)
    ]

    # return the indices where the ground truth is touched by the candidate location
    # NOTE: this comparison is between line ranges
    return Counter(
        i
        for i, location in enumerate(locations)
        if location.path == ground_truth.path
        and location.range.touches(ground_truth.before_lrange)
    )


def counter_to_cumulative_dict(counter: Counter[int]) -> defaultdict[int, float]:
    min_key = min(counter.keys(), default=0)
    max_key = max(counter.keys(), default=0)
    counts = [
        counter.get(i, 0) / max(counter.total(), 1) for i in range(min_key, max_key + 1)
    ]
    # all values higher than the max key in the counter will simply return the max
    return defaultdict(lambda: counts[-1], enumerate(accumulate(counts)))


def any_touch_ground_truth(
    scored_candidates: Sequence[FileLocation],
    ground_truth: GroundTruthEdit,
) -> bool:
    """Check if the output is correct."""
    # returns 1 if any of the candidates touch the ground truth, 0 otherwise
    return any(
        location.path == ground_truth.path
        and location.range.touches(ground_truth.before_lrange)
        for location in scored_candidates
    )

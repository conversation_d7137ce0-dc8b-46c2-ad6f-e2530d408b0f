"""The task for evaluating the agent system on the SWE Bench dataset."""

from dataclasses import dataclass, field
from pathlib import Path
from research.eval.harness.systems.abs_system import AbstractSystem
from multiprocessing import Pool
from datetime import datetime
import logging
import json
from research.eval.swe_bench.agent_qa.swe_bench.swe_bench import (
    consolidate_predictions,
    get_dataset_name,
    merge_reports,
    result_report,
    run_evaluation,
    AgentInput,
    AgentOutput,
)
from research.eval.harness.tasks.abs_task import AbstractTask, DocsType
from datasets import Dataset, load_dataset
from compress_pickle import load as compress_load


@dataclass
class SWEBenchTaskConfig:
    limit_examples: int | None = field(default=None)
    """Limit the total number of examples to run."""

    limit_examples_per_shard: int | None = field(default=None)
    """Limit the number of examples per shard to run."""

    limit_by_example_names: list[str] | None = field(default=None)
    """Overrides limit_examples. Limit the evaluation to the given examples."""

    limit_by_repos: list[str] | None = field(default=None)
    """Limit the evaluation to the given repositories."""

    dataset_path: str = field(default="verified")
    """The name of the dataset to use.  One of verified, full, or lite."""

    docker_host: str | None = field(default=None)
    """The docker host to use for evaluation.  Defaults to localhost."""

    workdir: Path = field(default_factory=lambda: Path("/tmp/swe-bench-workspace/"))
    """The working directory for the agents."""

    num_eval_processes: int = field(default=25)
    """The number of processes to use for evaluation."""

    skip_generation: bool = field(default=False)
    """Skip the generation step and only run evaluation."""

    num_workers: int = field(default=1)
    """The number of ways to parallelize the task.  This is used to distribute the work across multiple workers."""

    worker_idx: int = field(default=0)
    """The index of the worker.  This is used to distribute the work across multiple workers."""

    eager_eval: bool = field(default=False)
    """Whether to run evaluation immediately after generation."""


class SWEBenchTask(AbstractTask[AgentInput, AgentOutput]):
    def __init__(self, config: SWEBenchTaskConfig):
        self.config = config
        self.config.workdir = Path(self.config.workdir)
        self.dataset_name = get_dataset_name("verified")

        logging.info(f"Loading dataset from {self.config.dataset_path}")
        with open(self.config.dataset_path, "rb") as f:
            dataset_shards = compress_load(f, compression="gzip")

        coalesced_examples = []
        for shard in dataset_shards:
            coalesced_examples.extend(shard)

        if self.config.limit_by_example_names is not None:
            coalesced_examples = [
                sample
                for sample in coalesced_examples
                if sample["instance_id"] in self.config.limit_by_example_names
            ]
            logging.info(f"Filtered to {len(coalesced_examples)} samples")
        elif self.config.limit_by_repos is not None:
            for repo in self.config.limit_by_repos:
                logging.info(f"Limiting to {repo}")
            repo_names = set([example["repo"] for example in coalesced_examples])
            logging.info(f"All repo names: {repo_names}")
            coalesced_examples = [
                sample
                for sample in coalesced_examples
                if sample["repo"] in self.config.limit_by_repos
            ]
            logging.info(
                f"Filtered to {len(coalesced_examples)} samples. Filtered to include only {self.config.limit_by_repos}"
            )
        elif self.config.limit_examples is not None:
            coalesced_examples = coalesced_examples[: self.config.limit_examples]
            logging.info(f"Filtered to {len(coalesced_examples)} samples")

        target_shard_ct = self.config.num_workers
        logging.info(
            f"Found {len(dataset_shards)} shards, coalescing into {target_shard_ct} shards"
        )
        coalesced_shards = [[]]
        num_examples_per_shard = len(coalesced_examples) // target_shard_ct
        if self.config.limit_examples_per_shard is not None:
            num_examples_per_shard = min(
                num_examples_per_shard, self.config.limit_examples_per_shard
            )
        while len(coalesced_examples) > 0:
            if (
                len(coalesced_shards) < target_shard_ct
                and len(coalesced_shards[-1]) >= num_examples_per_shard
            ):
                coalesced_shards.append([])
            coalesced_shards[-1].append(coalesced_examples.pop(0))
        self.samples = coalesced_shards[self.config.worker_idx]

        logging.info(
            f"Found {len(self.samples)} samples in shard {self.config.worker_idx}"
        )

        logging.info("Listing repositories in the (possibly filtered) dataset")
        for repo in set([example["repo"] for example in self.samples]):  # type: ignore
            logging.info(repo)

    @classmethod
    def from_yaml_config(cls, config: dict) -> "SWEBenchTask":
        """Returns SWEBenchTask object constructed using a config dictionary."""
        formatted_config = SWEBenchTaskConfig(**config)
        return cls(formatted_config)

    def run(
        self,
        system: AbstractSystem[AgentInput, AgentOutput],
        output_path: str | Path,
        output_prefix: str = "",
    ) -> dict:
        st = datetime.now()
        self.config.workdir.mkdir(parents=True, exist_ok=True)
        logging.info(f"Agents will work in {self.config.workdir}.")

        if not self.config.skip_generation:
            model_inputs = [
                AgentInput(
                    input=sample,
                    eager_eval=self.config.eager_eval,
                    use_direct_client=self.config.worker_idx % 2 == 0,
                )
                for sample in self.samples
            ]
            # TODO (c-flaherty): Should we have this return something to avoid leaky interface?
            system.generate_parallel(model_inputs)

            # Consolidate predictions
            all_predictions = consolidate_predictions(self.config.workdir)
            logging.info(f"Found {len(all_predictions)} predictions")
            logging.info(
                f"Predictions completed in {datetime.now() - st} ({(datetime.now() - st).total_seconds():.2f}s)"
            )
            predictions_file = self.config.workdir / "consolidated_predictions.json"
            predictions_file.write_text(json.dumps(all_predictions, indent=2))
            logging.info(f"Predictions saved to {predictions_file}")
        else:
            predictions_file = self.config.workdir / "consolidated_predictions.json"

        if not self.config.eager_eval:
            run_evaluation(
                predictions_file,
                self.dataset_name,
                self.config.workdir.name,
                num_processes=self.config.num_eval_processes,
                docker_host=self.config.docker_host,
            )
            eval_file = self.config.workdir / Path(
                f"Augment_Agent.{self.config.workdir.name}.json"
            )
            result_report(eval_file)
        else:
            results_files = sorted(
                list(self.config.workdir.rglob("Augment_Agent*json"))
            )
            merge_reports(results_files, self.config.workdir / "Augment_Agent.json")

        logging.info(
            f"Total time: {datetime.now() - st} ({(datetime.now() - st).total_seconds():.2f}s)"
        )

        return {}

    def __getitem__(self, index: int) -> tuple[AgentInput, DocsType]:
        """Get the index-th example in this task."""
        raise NotImplementedError("This task does not support random indexing.")

    def __len__(self) -> int:
        """Return the total number of examples in this task."""
        return len(self.samples)

    def execute(
        self,
        model_input: AgentInput,
        generation: str,
        timeout: float | None = None,
    ) -> dict:
        raise NotImplementedError("This task does not support the execute method.")

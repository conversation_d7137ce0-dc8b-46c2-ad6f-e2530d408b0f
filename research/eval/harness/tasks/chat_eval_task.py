from __future__ import annotations

import copy
import json
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Optional

from jinja2 import Environment
from markdown import markdown

from research.core import utils_for_file, utils_for_str
from research.core.model_input import ChatInput, ModelInput
from research.core.types import Document
from research.eval.harness.systems.abs_system import (
    AbstractSystem,
    CompletionResult,
)
from research.eval.harness.tasks.abs_task import AbstractTask


# NOTE(yuri): It's just a temporary interface,
# until we implement a proper one in research/core
@dataclass
class ResearchChatPromptInput:
    question: str
    expected_reply: Optional[str]
    extra: dict


def convert_to_model_input(chat_input: ResearchChatPromptInput) -> ModelInput:
    model_input = ModelInput(
        prefix="",
        suffix="",
        retrieved_chunks=[],
        path="",
        selected_code="",
        target=None,
        chat_input=ChatInput(
            history=[],
            request=chat_input.question,
        ),
        extra={
            "expected_reply": chat_input.expected_reply,
        },
    )

    return model_input


REPORT_TEMPLATE_PATH = (
    Path(__file__).parents[4] / "experimental/yuri/chat/binks_eval_template.txt"
)


class ChatEvalTask(AbstractTask):
    def __init__(
        self,
        dataset_path: str,
        html_report_template_path: str = str(REPORT_TEMPLATE_PATH),
    ):
        repos_dir = Path(dataset_path) / "repos"

        repo2docs: dict[str, list[Document]] = {}
        for repo_file in repos_dir.glob("*.json"):
            raw_repo_data = utils_for_file.read_json(repo_file)
            repo_url = raw_repo_data["repo_url"]
            docs = [Document(**d) for d in raw_repo_data["docs"]]
            repo2docs[repo_url] = docs

        examples: list[ResearchChatPromptInput] = []
        with open(Path(dataset_path) / "examples.json") as f:
            examples_raw = json.load(f)
            for ex in examples_raw:
                example = ResearchChatPromptInput(**ex)
                examples.append(example)

        examples_to_render = copy.deepcopy(examples)
        for e in examples_to_render:
            if e.expected_reply is None:
                e.expected_reply = ""
            else:
                e.expected_reply = markdown(
                    e.expected_reply, extensions=["fenced_code"]
                )

        self.html_report_template_path = html_report_template_path
        self.repo2docs = repo2docs
        self.examples = examples
        self.examples_to_render = examples_to_render

    def run(
        self,
        system: AbstractSystem,
        output_path: str | Path,
        output_prefix: str = "",
    ) -> dict:
        output_dir = (
            Path(output_path)
            / f"{output_prefix}_{system.name}_{utils_for_str.get_random_str(4)}"
        )
        output_dir.mkdir(exist_ok=True)
        cache_dir = output_dir / "cache"
        cache_dir.mkdir(exist_ok=True, parents=False)

        all_repo_urls = set(map(lambda ex: ex.extra["repo_url"], self.examples))

        results = []
        for repo_url in all_repo_urls:
            assert repo_url in self.repo2docs, f"Did not find repository {repo_url}."

            system.clear_retriever()
            system.add_docs(self.repo2docs[repo_url])

            cur_repo_samples = filter(
                lambda ex: ex.extra["repo_url"] == repo_url,
                self.examples,
            )

            for sample in cur_repo_samples:
                result = system.generate(convert_to_model_input(sample))
                results.append(result)

        html_report_path = output_dir / "report.html"
        rendered_html = self.render_html_report(results)
        with html_report_path.open("w") as f:
            f.write(rendered_html)

        return {
            "html_report_path": str(html_report_path),
        }

    def render_html_report(self, results: list[CompletionResult]):
        template_txt = Path(self.html_report_template_path).read_text()

        env = Environment(keep_trailing_newline=True)
        env.globals.update(zip=zip)
        template = env.from_string(template_txt)

        header = f"Chat evaluation -- {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        for r in results:
            r.generated_text = markdown(r.generated_text, extensions=["fenced_code"])
        rendered_html = template.render(
            samples=self.examples_to_render, results=results, eval_header=header
        )

        return rendered_html

    @classmethod
    def from_yaml_config(cls, config: dict) -> "ChatEvalTask":
        """Returns ChatEvalTask object constructed using a config dictionary."""
        kwargs = {"dataset_path": config["dataset_path"]}
        if "html_report_template_path" in config:
            kwargs["html_report_template_path"] = config["html_report_template_path"]
        return cls(**kwargs)

    def __getitem__(self, index: int):
        raise NotImplementedError()

    def __len__(self) -> int:
        return len(self.examples)

    def execute(
        self,
        model_input: ModelInput,
        generation: str,
        timeout: Optional[float] = None,
    ) -> dict:
        raise NotImplementedError()

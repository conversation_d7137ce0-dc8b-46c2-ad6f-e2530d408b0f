"""The Pinocchio Instruct Task.

A benchmark of code edit tasks based on real completions from users.
"""

import logging
from dataclasses import asdict, dataclass, field
from pathlib import Path
from typing import Mapping, Optional, Sequence, Union

import dataclasses_json
import zstandard as zstd
from dataclasses_json import dataclass_json
from marshmallow import fields

from base.datasets.edit import EditDatum
from base.datasets.edit_dataset import BlobStore, EditDataset
from base.datasets.gcs_blob_cache import PathAndContent
from base.datasets.tenants import get_tenant
from research.core.artifacts import collect_artifacts
from research.core.types import Char<PERSON><PERSON>e, Document
from research.eval.harness.metrics import (
    compute_edit_similarity,
    compute_exact_match,
    safe_edits_forward_metrics,
)
from research.eval.harness.systems.abs_system import (
    CodeEditResult,
    CodeInstructInput,
    CodeInstructSystem,
)
from research.eval.harness.tasks.abs_task import AbstractTask, DocsType

logger = logging.getLogger(__name__)


@dataclass_json
@dataclass
class Metrics:
    """The metrics for the task."""

    exact_match: float
    """Exact match after normalizing on end-of-statement criteria."""

    edit_similarity: float
    """The edit similarity score."""

    log_likelihood: float
    """The log likelihood score."""

    token_accuracy: float
    """The token accuracy score."""

    count: int = 1
    """The number of datapoints these metrics approximate."""

    def __str__(self) -> str:
        return " ".join(
            [
                f"em={self.exact_match:.3f}",
                f"es={self.edit_similarity:.3f}",
                f"ll={self.log_likelihood:.3f}",
                f"acc={self.token_accuracy:.3f}",
            ]
        )

    def _get_update(self, avg_a, count_a, avg_b, count_b):
        """Calculates how much to increase avg_a to reach the weighted average."""
        return (avg_b - avg_a) * (count_b) / (count_a + count_b)

    def update(self, instance: "Metrics"):
        """Computes the weighted average incrementally."""
        self.exact_match += self._get_update(
            self.exact_match, self.count, instance.exact_match, instance.count
        )
        self.edit_similarity += self._get_update(
            self.edit_similarity, self.count, instance.edit_similarity, instance.count
        )
        self.log_likelihood += self._get_update(
            self.log_likelihood, self.count, instance.log_likelihood, instance.count
        )
        self.token_accuracy += self._get_update(
            self.token_accuracy, self.count, instance.token_accuracy, instance.count
        )

        self.count += instance.count


@dataclass_json
@dataclass
class PinocchioInstructOutput:
    """The output of the Pinocchio Task.

    One such record will be produced per evaluated patch.
    """

    request_id: str
    """Example request id."""

    datum: EditDatum
    """Full completion data."""

    prefix: str
    """The prefix string for the task."""

    suffix: str
    """The suffix string for the task."""

    prompt: str
    """The actual prompt used for generation.

    The prefix, suffix, and chunks may be changed to form the actual prompt,
    based on budget constraints.
    """

    generation: str
    """The full output text from generation."""

    ground_truth: str
    """The ground truth text."""

    metrics: Metrics
    """The scores for this generation."""

    artifacts: list[dict]
    """Any additional artifacts to record for the patch."""

    path: str = ""
    """The path of the file being updated."""


CodeInstructTask = AbstractTask[CodeInstructInput, CodeEditResult]


class PinocchioInstructTask(CodeInstructTask):
    """The Pinocchio Instruct Task.

    Given a set of patches and a retrieval database, generate a set of documents.
    Score the generation using exact match and edit similarity.
    """

    @dataclass_json
    @dataclass
    class Config:
        """Configuration for the Pinocchio Instruct task."""

        version: Optional[str] = None
        """If provided, use this pre-baked version of the dataset."""

        tenant_name: Optional[str] = None
        """The name of the tenant configuration to use during evaluation.

        This should match one of the keys in `base/datasets/tenants.py`.

        Either `tenant_name` or `tenant_config` must be provided.
        """

        filters: Optional[EditDataset.Filters] = None
        """Filters to apply to the dataset."""

        order_by: EditDataset.OrderBy = None
        """Order the dataset by this field."""

        limit: Optional[int] = None
        "Maximum number of examples to use."

        service_account_file: Optional[Path] = field(
            default=None,
            metadata=dataclasses_json.config(
                encoder=str, decoder=Path, mm_field=fields.Str()
            ),
        )
        """Path to optional GCP Service Account .json file.

        If the file doesn't exist, this argument will be treated as if it
        hadn't been set.
        """

        datum_file: Optional[Path] = field(
            default=None,
            metadata=dataclasses_json.config(
                encoder=str, decoder=Path, mm_field=fields.Str()
            ),
        )

        use_annotated: bool = False

    def __init__(
        self,
        dataset: Sequence[EditDatum],
        blob_store: BlobStore,
        use_annotated: bool = False,
        limit: Optional[int] = None,
    ):
        self._dataset = dataset
        self._blob_store = blob_store
        self._use_annotated = use_annotated
        self._limit = limit
        self._seen_doc_ids = set()

    def execute(
        self,
        model_input: CodeInstructInput,
        generation: str,
        timeout: Optional[float] = None,
    ):
        del model_input, generation, timeout
        # There is nothing to execute in this task.
        raise NotImplementedError()

    def __getitem__(self, index: int) -> tuple[CodeInstructInput, DocsType]:
        """Get the index-th example in this task."""
        raise ValueError("Cannot randomly index into this task.")

    def __len__(self) -> int:
        """Return the total number of examples in this task."""
        raise NotImplementedError("Cannot randomly index into this task.")

    def _update_index(
        self,
        system: CodeInstructSystem,
        blobs: Mapping[str, PathAndContent],
    ):
        """Update the blob cache with the given blobs."""
        # A document id is created as a side effect of constructing the document.
        # Generated blob names should match the original blob names.
        docs = [Document.new(blob.content, blob.path) for blob in blobs.values()]

        # Validate new blob names are same as original.
        for doc, original_name in zip(docs, blobs.keys()):
            assert (
                doc.id == original_name
            ), f"Generated name {doc.id} does not match original {original_name}"

        # Add only those documents we haven't already added to the system.
        unseen_docs = [x for x in docs if x.id not in self._seen_doc_ids]
        system.add_docs(unseen_docs)
        self._seen_doc_ids.update(x.id for x in unseen_docs)
        logger.info("Added %d new docs to the index.", len(unseen_docs))
        logger.info("The index now contains %d docs.", len(self._seen_doc_ids))

        # Return the full list of doc ids, not just the new ones.
        return [x.id for x in docs]

    def run_one(
        self,
        system: CodeInstructSystem,
        datum: EditDatum,
    ) -> PinocchioInstructOutput:
        """Run the task with the given system and save the results into output_path/output_prefix_xxx."""

        # TODO(rich): handle unknown blobs by removing them from the requested blobs
        blob_map = {
            blob_name: maybe
            for blob_name, maybe in zip(
                datum.request.blob_names,
                self._blob_store.get_blobs(datum.request.blob_names),
            )
        }
        empty_blobs = [
            blob_name for blob_name, blob in blob_map.items() if blob is None
        ]
        if empty_blobs:
            logger.warning(
                "Id: %s found only %d / %d blobs",
                datum.request_id,
                len(datum.request.blob_names) - len(empty_blobs),
                len(datum.request.blob_names),
            )
            logger.debug("First empty blob name: %s", empty_blobs[0])

        blob_map = {
            blob_name: blob for blob_name, blob in blob_map.items() if blob is not None
        }

        doc_ids = self._update_index(system, blob_map)

        # XXX If we're running this against production and research, there may be some slight differences
        # in overlap detection, .... anything else?
        if self._use_annotated:
            assert datum.annotated_text is not None
            updated_code = datum.annotated_text
        else:
            updated_code = datum.response.text

        model_input = CodeInstructInput(
            path=datum.request.path,
            prefix=datum.request.prefix,
            suffix=datum.request.suffix,
            selected_code=datum.request.selected_text,
            instruction=datum.request.instruction,
            updated_code=updated_code,
            retrieved_chunks=[],
            doc_ids=doc_ids,
            extra={
                # NOTE(arun): Why do we have this if we have `target`?
                "ground_truth": updated_code,
            },
        )

        with collect_artifacts() as collector:
            result = system.generate(model_input)
            artifacts = collector.get_artifacts()
        forward_metrics = safe_edits_forward_metrics(system, model_input)

        # TODO(rich): should we rename updated_code to target, to be consistent with completions?
        target = model_input.updated_code
        assert target is not None
        scores = Metrics(
            exact_match=compute_exact_match(target, [result.generated_text]),
            edit_similarity=compute_edit_similarity(target, [result.generated_text]),
            log_likelihood=forward_metrics.log_likelihood,
            token_accuracy=forward_metrics.token_accuracy,
        )

        try:
            prompt_toks = system.get_model().tokenizer.detokenize(result.prompt_tokens)
        except NotImplementedError:
            prompt_toks = ""

        return PinocchioInstructOutput(
            request_id=datum.request_id,
            path=datum.request.path,
            prefix=model_input.prefix,
            suffix=model_input.suffix,
            datum=datum,
            prompt=prompt_toks,
            generation=result.generated_text,
            ground_truth=updated_code,
            metrics=scores,
            artifacts=artifacts,
        )

    def run(
        self,
        system: CodeInstructSystem,
        output_path: Union[str, Path],
        output_prefix: str = "",
    ) -> dict:
        """Run the task with the given system and save the results into output_path/output_prefix_xxx."""

        output_path = Path(output_path)
        output_path.mkdir(exist_ok=True, parents=True)
        logger.info(f"Save results into {output_path}")

        results = list[PinocchioInstructOutput]()
        agg_metrics = Metrics(0, 0, 0, 0, 0)

        for i, datum in enumerate(self._dataset[: self._limit]):
            logger.info(
                "Evaluating on request %s (%d/%d)",
                datum.request_id,
                i + 1,
                len(self._dataset),
            )
            logger.info(
                "Original response for request %s: model name %s, %d missing blobs",
                datum.request_id,
                datum.response.model_name,
                len(datum.response.unknown_blob_names),
            )

            output = self.run_one(system, datum)
            agg_metrics.update(output.metrics)
            results.append(output)

        # Log aggregated scores per language
        logger.info("Scores: %s", agg_metrics)

        if output_path.exists():
            # where does file_name come from?
            try:
                model_name = system.get_model().name
            except NotImplementedError:
                model_name = "unknown"

            file_name = f"{model_name}_{self.name}"  # type: ignore
            out_file = Path(
                output_path / f"{output_prefix}{file_name}_completed_patches.jsonl.zst"
            )
            logger.info(f"Writing results to {out_file}")

            with zstd.open(out_file, "w", encoding="utf-8") as f:
                for doc in results:
                    f.write(doc.to_json())  # type: ignore
                    f.write("\n")
        else:
            out_file = ""

        return {
            "artifact": str(out_file),
            "metrics": asdict(agg_metrics),
        }

    @classmethod
    def from_yaml_config(cls, config: dict) -> "PinocchioInstructTask":
        """Returns a Task object constructed using a config dictionary."""
        # pylint: disable-next=no-member
        # fmt: off
        cfg: PinocchioInstructTask.Config = PinocchioInstructTask.Config.schema().load(config)  # type: ignore
        # fmt: on

        assert cfg.tenant_name, "You must set a verion or set a tenant_name."
        logger.info("Running with configuration %s", cfg)

        if cfg.service_account_file and not cfg.service_account_file.is_file():
            logger.warning(
                "Couldn't find service account credentials at: %s. Ignore warning if running locally.",
                cfg.service_account_file,
            )

        assert cfg.datum_file, "You must set a datum_file."
        with cfg.datum_file.open() as f:
            dataset = [EditDatum.schema().loads(line) for line in f]  # pyright: ignore[reportAttributeAccessIssue]

        blob_store = BlobStore(
            tenant=get_tenant(cfg.tenant_name),
            service_account_file=cfg.service_account_file,
        )

        return PinocchioInstructTask(
            dataset=dataset,
            blob_store=blob_store,
            use_annotated=cfg.use_annotated,
            limit=cfg.limit,
        )

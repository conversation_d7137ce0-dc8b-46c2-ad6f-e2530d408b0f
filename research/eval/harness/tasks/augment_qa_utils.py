"""Utilities for AugmentQA."""

import dataclasses
import logging
from typing import Iterable

from markdown import markdown

from base.prompt_format.common import PromptChunk
from base.prompt_format_chat.prompt_formatter import Exchange
from research.core.chat_prompt_input import ResearchChatPromptInput
from research.eval.harness.systems.abs_system import (
    CompletionResult,
)

logger = logging.getLogger(__name__)


@dataclasses.dataclass(frozen=True)
class Sample(ResearchChatPromptInput):
    """A sample from the AugmentQA dataset."""

    repo: str = ""
    """The name of the repo the sample comes from."""

    keywords: list[str] = dataclasses.field(default_factory=list)
    """The keywords for the sample."""

    gold_paths: list[str] = dataclasses.field(default_factory=list)
    """The gold paths for the sample."""

    # Provide default values for some of the ChatPromptInput fields
    path: str = ""
    prefix: str = ""
    selected_code: str = ""
    suffix: str = ""
    chat_history: list[Exchange] = dataclasses.field(default_factory=list)
    prefix_begin: int = 0
    suffix_end: int = 0
    retrieved_chunks: Iterable[PromptChunk] = dataclasses.field(default_factory=list)

    def __post_init__(self):
        if len(self.keywords) == 0:
            raise ValueError("All samples must have at least one keyword.")
        if len(self.gold_paths) == 0:
            raise ValueError("All samples must have at least one gold path.")

    @classmethod
    def from_dict(cls, d: dict):
        d_copy = {k: v for k, v in d.items()}
        if "chat_history" in d:
            d_copy["chat_history"] = [Exchange(**x) for x in d["chat_history"]]
        else:
            d_copy["chat_history"] = []
        if "retrieved_chunks" in d and len(d["retrieved_chunks"]) > 0:
            raise ValueError("Samples should not have retrieved_chunks")
        return cls(**d_copy)


@dataclasses.dataclass(frozen=True)
class AugmentQAOutput:
    """A sample from the AugmentQA dataset, with a score."""

    sample: Sample
    """The sample."""

    result: CompletionResult
    """The result of the generation."""

    answer_keyword_recall: float
    """The proportion of keywords that were mentioned in the answer."""

    retrievals_keyword_recall: float
    """The proportion of keywords that were successfully retrieved."""

    gold_paths_recall: float
    """The proportion of gold paths that were successfully retrieved."""

    gold_paths_mrr: float
    """The MRR of the gold paths."""

    generation_time: float
    """The time it took to generate the response."""

    answer_length: int = 0
    """The length of the answer in characters."""

    @property
    def metrics(self) -> dict[str, float]:
        """Return the score."""
        return {
            "answer_keyword_recall": self.answer_keyword_recall,
            "retrievals_keyword_recall": self.retrievals_keyword_recall,
            "gold_paths_recall": self.gold_paths_recall,
            "gold_paths_mrr": self.gold_paths_mrr,
            "generation_time": self.generation_time,
            "answer_length": self.answer_length,
        }

    @property
    def metrics_short_str(self) -> str:
        """Return the score."""
        return (
            f"answer_keyword_recall "
            f"{self.answer_keyword_recall:.2f}, "
            f"retrievals_keyword_recall "
            f"{self.retrievals_keyword_recall:.2f}, "
            f"gold_paths_recall "
            f"{self.gold_paths_recall:.2f}"
        )

    @property
    def selected_code(self) -> str:
        """Convert markdown in the selected_code into HTML."""
        return self.sample.selected_code

    @property
    def target(self) -> str:
        """Convert markdown in the target into HTML."""
        assert self.sample.target is not None
        return self.sample.target

    @property
    def target_html(self) -> str:
        """Convert markdown in the target into HTML."""
        return markdown(self.target, extensions=["fenced_code"])

    @property
    def generated_text(self) -> str:
        """Convert markdown in the generated_text into HTML."""
        return self.result.generated_text

    @property
    def selected_code_html(self) -> str:
        """Convert markdown in the selected_code into HTML."""
        return markdown(self.sample.selected_code, extensions=["fenced_code"])

    @property
    def generated_text_html(self) -> str:
        """Convert markdown in the generated_text into HTML."""
        return markdown(self.generated_text, extensions=["fenced_code"])

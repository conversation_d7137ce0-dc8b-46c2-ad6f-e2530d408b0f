"""HumanEvalInstruct dataset."""

import copy
import logging
import re
import time
from dataclasses import asdict, dataclass
from pathlib import Path
from typing import Optional, Union

from tqdm import tqdm

from research.core.artifacts import collect_artifacts
from research.core.chat_prompt_input import ResearchChatPromptInput
from research.core.data_paths import canonicalize_path
from research.core.model_input import ModelInput
from research.core.types import Chunk, Document
from research.eval import patch_lib
from research.eval.generation import execution
from research.eval.harness import utils
from research.eval.harness.systems.abs_system import ChatSystem
from research.eval.harness.tasks.abs_task import ChatTask, DocsType

logger = logging.getLogger(__name__)


@dataclass
class Score:
    """The metrics for the HumanEvalInstruct task."""

    pass_at_1: float
    """Accuracy"""

    samples: int
    """Samples"""


@dataclass
class HumanEvalInstructOutput:
    """Schema for recording patch information in artifact file."""

    patch: dict
    """The Patch object from the task's dataset, representing the corruption site."""

    prefix: str
    """The prefix string for the task."""

    suffix: str
    """The suffix string for the task."""

    prompt: str
    """The actual prompt used for generation.

    The prefix, suffix, and chunks may be changed to form the actual prompt,
    based on budget constraints.
    """

    filtered_chunks: list[Chunk]
    """Chunks considered for the prompt.

    The chunks, after removing those overlapping with the ground truth, the
    prefix, and the suffix.
    """

    generation: str
    """The full output text from generation."""

    completion: str
    """The generation text after post-processing.

    This is what will be executed.
    """

    score: Score
    """score"""

    artifacts: list[dict]
    """Any additional artifacts to record for the patch."""

    @classmethod
    def from_dict(cls, obj: dict):
        for chunk_dict in obj["filtered_chunks"]:
            chunk_dict["parent_doc"] = Document(**chunk_dict["parent_doc"])
        obj["filtered_chunks"] = [Chunk(**x) for x in obj["filtered_chunks"]]
        obj["patch"] = patch_lib.Patch(**obj["patch"])
        return cls(**obj)


def extract_code(completion: str):
    """Extract code from the completion.

    The code is extracted only from the _first_ markdown block.
    """
    # It's possible that the language is included immediately after the first backticks,
    # so we ignore everything on the first line.
    pattern = r"```[^\n]*?\n(.*?)\n```"
    match = re.search(pattern, completion, re.DOTALL)
    if match:
        return str(match.group(1))
    else:
        logger.warning(f"Could not extract code from {completion}")
        return None


def gen_exec_code(rec, code: str):
    """Return executable code from a human_eval json record."""
    # Taken from the original execution code of HumanEval
    code = "\n".join(
        [rec["prompt"] + code, rec["test"], f"check({rec['entry_point']})"]
    )
    return code


def prepare_retrieval_docs(path: Path):
    """Prepare the retrieval docs for the task."""

    doc_set_obj = utils.read_jsonl_zst(path)
    doc_set = doc_set_obj[0]["docs"]
    all_docs = [Document.new(text=doc["text"], path=doc["path"]) for doc in doc_set]
    # Filter .git files from the docs
    docs = [doc for doc in all_docs if ".git" not in Path(doc.path).parts]
    if len(docs) != len(all_docs):
        logger.info("Filtered %d docs from .git directory", len(all_docs) - len(docs))
    return docs


class HumanEvalInstruct(ChatTask):
    """Evaluation task for the HumanEvalInstruct benchmark.

    Example config:

    task:
        name: humaneval_instruct
    """

    version: str = "1.0"

    _DATASETS_ROOT = canonicalize_path("data/processed/human_eval_instruct.v1")

    DATASETS = {
        "standard": f"{_DATASETS_ROOT}/human_eval_instruct.jsonl.zst",
        "synthetic": f"{_DATASETS_ROOT}/aug_human_eval_instruct.jsonl.zst",
    }

    # Dataset of docs to use for retrieval. The dataset doesn't depend on
    # retrieval, but we add some in to ensure it doesn't affect the quality of
    # the results.
    DOCS = canonicalize_path(
        "data/processed/human_eval_instruct.v1/augment_jun_08_2024.json.zst"
    )

    def __init__(
        self,
        experiment_name: str = "humaneval_instruct",
        limit: Optional[int] = None,
        execute: bool = True,
        variant: str = "standard",
        user_guidelines: Optional[str] = None,
        user_guidelines_path: Optional[str] = None,
        workspace_guidelines: Optional[str] = None,
    ):
        self.name = experiment_name
        self.variant = variant.lower()
        dataset = self.DATASETS[self.variant]
        self.dataset = utils.read_jsonl_zst(Path(dataset))
        if limit is not None:
            logger.info(f"Limiting to {limit} examples.")
            self.dataset = self.dataset[:limit]
        self.exec = execute
        self.docs = prepare_retrieval_docs(Path(self.DOCS))
        assert not user_guidelines or not user_guidelines_path
        if user_guidelines_path:
            self.user_guidelines = Path(user_guidelines_path).read_text("utf-8")
        else:
            self.user_guidelines = user_guidelines
        self.workspace_guidelines = workspace_guidelines

    def __getitem__(self, index: int) -> tuple[ModelInput, DocsType]:
        raise ValueError("Cannot randomly index into this task.")

    def __len__(self) -> int:
        """Return the total number of examples in this task."""
        return len(self.dataset)

    @classmethod
    def _build_executable_code(cls, xdict: dict) -> str:
        for required_key in ("prompt", "test", "entry_point", "completion"):
            if required_key not in xdict:
                raise ValueError(
                    f"Did not find {required_key} in the model_input.extra."
                )
            if not isinstance(xdict[required_key], str):
                raise TypeError(
                    f"Key {required_key}'s type is {type(xdict[required_key])} instead of str."
                )
        code = "\n".join(
            [
                xdict["prompt"] + xdict["completion"],
                xdict["test"],
                f"check({xdict['entry_point']})",
            ]
        )
        return code

    def execute(
        self, model_input: ModelInput, generation: str, timeout: Optional[float] = 3.0
    ) -> dict:
        """Execute the codes derived from the model input and generation and return the feedbacks."""
        if timeout is None:
            timeout = 3600.0  # if timeout is None, we set one hour as timeout.

        dict_to_build_code = {
            **copy.deepcopy(model_input.extra),
            "completion": generation,
        }
        code = self._build_executable_code(dict_to_build_code)
        raw_results, _, _ = execution.sandbox_execute(code, timeout)
        if raw_results == "passed":
            xpass, error_info = True, None
        else:
            xpass, error_info = False, raw_results
        return {
            "pass": xpass,
            "error_info": error_info,
            "code": code,
            "raw_results": raw_results,
        }

    @classmethod
    def from_yaml_config(cls, config: dict) -> "HumanEvalInstruct":
        """Returns a Task object constructed using a config dictionary."""
        limit = config.get("limit", None)
        execute = config.get("exec", True)
        variant = config.get("variant", "standard")
        user_guidelines = config.get("user_guidelines", None)
        user_guidelines_path = config.get("user_guidelines_path", None)
        workspace_guidelines = config.get("workspace_guidelines", None)
        return cls(
            limit=limit,
            execute=execute,
            variant=variant,
            user_guidelines=user_guidelines,
            user_guidelines_path=user_guidelines_path,
            workspace_guidelines=workspace_guidelines,
        )

    def run(
        self,
        system: ChatSystem,
        output_path: Union[str, Path],
        output_prefix: str = "",
    ) -> dict:
        """Return the results from running the task against the model.

        May have side effects; e.g. generating code and saving in a file.

        Args:
          system: The System to run the task against.
          output_path: The path to an existing directory to which artifacts can be written.
          output_prefix: Optional prefix to include with files to prevent collisions.
        """
        start_timestamp = time.time()
        run_start_time = time.monotonic()

        output_path = Path(output_path)

        file_name = f"{system.get_model().name}_{self.name}"

        total_exec_time = 0
        completed_patches_for_analytics: list[HumanEvalInstructOutput] = []

        doc_ids = [doc.id for doc in self.docs]
        system.add_docs(self.docs)
        for doc in tqdm(self.dataset):
            # Note, it's important to create a unique copy of the rec rather than
            # duplicate references to an existing record, since we'll be modifying
            # this along through the pipeline.
            doc = doc.copy()

            logger.debug(f"\rGenerate completion for {doc['task_id']}.")
            logger.debug(f"\rDoc ids {len(doc_ids)}")

            prefix = ""
            suffix = ""
            target = f"Here is the canonical solution:\n```\n{doc['canonical_solution']}\n```"
            model_input = ResearchChatPromptInput(
                path="foo.py",
                prefix=prefix,
                selected_code="",
                suffix=suffix,
                message=doc["instruction"],
                chat_history=[],
                prefix_begin=0,
                suffix_end=0,
                retrieved_chunks=[],
                label=None,
                model_reply=None,
                doc_ids=doc_ids.copy(),
                target=target,
                user_guidelines=self.user_guidelines,
                workspace_guidelines=self.workspace_guidelines,
            )

            with collect_artifacts() as collector_manager:
                completion = system.generate(model_input)
                artifacts = collector_manager.get_artifacts()

            # TODO(rah): need safe forward metrics for chat at some point
            # forward_metrics = chat_safe_forward_metrics(system, model_input)
            prompt = system.get_model().tokenizer.detokenize(completion.prompt_tokens)

            # Note: the generated text includes comments around the generated code.
            # Also, the generated code includes the complete signature
            code = extract_code(completion.generated_text)
            if code is None:
                logger.warning(f"Completion of {doc['task_id']} is None")
                code = ""
            code_for_exec = gen_exec_code(doc, code)

            doc["completion"] = code_for_exec

            if self.exec:
                start_time = time.time()
                raw_results, _, _ = execution.sandbox_execute(
                    doc["completion"], timeout=10
                )
                doc["result"] = raw_results
                doc["passed"] = raw_results == "passed"
                total_exec_time += time.time() - start_time

            target_function = f"def {doc['signature']}:\n{model_input.target}"
            pass_at_1 = int(doc["passed"])

            if not doc["passed"]:
                # Leaving this at info until we have confidence in this task
                logger.info("Code did not pass:")
                logger.info("Code:\n%s", code)
                logger.info("Target:\n%s", target_function)
                logger.info("EXEC RESULT\n%s", doc["result"])

            score = Score(
                pass_at_1=pass_at_1,
                samples=1,
            )

            completed_patches_for_analytics.append(
                HumanEvalInstructOutput(
                    patch=doc,
                    prefix=prefix,
                    suffix=suffix,
                    prompt=prompt,
                    filtered_chunks=[],
                    generation=completion.generated_text,
                    completion=code_for_exec,
                    score=score,
                    artifacts=artifacts,
                )
            )

        logger.info(f"Total exec time: {total_exec_time}")
        logger.info(
            f"Writing results to pathname {output_path}, prefix {output_prefix}"
        )
        if output_path.exists():
            pathname = Path(
                output_path / f"{output_prefix}{file_name}_completed_patches.jsonl.zst"
            )
            logger.info(f"Writing results to pathname {pathname}")

            def save_artifact(docs: list[HumanEvalInstructOutput]):
                recs = [asdict(doc) for doc in docs]
                utils.write_jsonl_zst(pathname, recs)
                return pathname

            handle_artifacts = save_artifact
        else:

            def print_artifact(docs):
                print(docs)
                return ""

            handle_artifacts = print_artifact

        avg_pass_at_1 = sum(
            doc.score.pass_at_1 for doc in completed_patches_for_analytics
        ) / len(completed_patches_for_analytics)
        out_file = handle_artifacts(completed_patches_for_analytics)

        run_end_time = time.monotonic()
        return {
            "artifact": str(out_file),
            "metrics": {
                "pass_at_1": avg_pass_at_1,
                "samples": len(completed_patches_for_analytics),
            },
            "variant": self.variant,
            "start_time": start_timestamp,
            "run_duration": run_end_time - run_start_time,
        }

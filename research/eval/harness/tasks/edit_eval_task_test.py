"""Unit test for the edit eval task.

pytest research/eval/tests/tasks/test_edit_eval_task.py
"""

import typing
import unittest

import parameterized

from research.eval.harness.tasks.edit_eval_task import (
    PATH_BASIC_EVAL,
    PATH_PR_EVAL,
    EditEvalTask,
)


class TestRandomIndex(unittest.TestCase):
    """Test __getitem__ and execute functions."""

    @parameterized.parameterized.expand(
        [
            (PATH_BASIC_EVAL, 89, 89, None),
            (PATH_PR_EVAL, 94, 94, 2560),
        ]
    )
    def test_target_as_solution(
        self,
        path: str,
        expected_num_examples: int,
        expected_num_repos: int,
        limit_of_num_files_in_repo: typing.Optional[int],
    ):
        task = EditEvalTask(
            dataset_path=path, limit_of_num_files_in_repo=limit_of_num_files_in_repo
        )
        for index in range(len(task)):
            inputs, repos = task[index]
            assert "category" in inputs.extra
            assert isinstance(repos, list)
        assert len(task) == expected_num_examples
        assert len(task.repo2docs) == expected_num_repos

    @parameterized.parameterized.expand(
        [
            (PATH_BASIC_EVAL, None),
            (PATH_PR_EVAL, 2560),
        ]
    )
    def test_example_in_the_repo(
        self,
        path: str,
        limit_of_num_files_in_repo: typing.Optional[int],
    ):
        task = EditEvalTask(
            dataset_path=path, limit_of_num_files_in_repo=limit_of_num_files_in_repo
        )
        for index in range(len(task)):
            inputs, repos = task[index]
            assert "category" in inputs.extra
            assert isinstance(repos, list)
            all_paths = [doc.path for doc in repos]
            assert inputs.path in all_paths

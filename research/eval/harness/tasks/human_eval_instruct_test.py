from research.eval.harness.systems.gold_system import GoldChatSystem
from research.eval.harness.tasks.human_eval_instruct import (
    HumanEvalInstruct,
    extract_code,
)


def test_human_eval_instruct_init():
    config = {"limit": 10, "exec": True, "variant": "standard"}
    task = HumanEvalInstruct.from_yaml_config(config)
    assert len(task) == 10
    assert task.exec
    assert task.variant == "standard"


def test_human_eval_instruct_init_synthetic_variant():
    config = {"limit": 10, "exec": True, "variant": "synthetic"}
    task = HumanEvalInstruct.from_yaml_config(config)
    assert len(task) == 10
    assert task.exec
    assert task.variant == "synthetic"


def test_human_eval_instruct_run():
    config = {"limit": 10, "exec": True}
    task = HumanEvalInstruct.from_yaml_config(config)
    system = GoldChatSystem()  # gold chat system object
    output_path = "test_output"
    result = task.run(system, output_path)
    assert "artifact" in result
    assert "metrics" in result


def test_extract_code_markdown():
    completion = "```\nprint('Hello, World!')\n```"
    assert extract_code(completion) == "print('Hello, World!')"


def test_extract_code_markdown_include_langauge():
    completion = "```python\nprint('Hello, World!')\n```"
    assert extract_code(completion) == "print('Hello, World!')"


def test_extract_code_no_markdown():
    completion = "print('Hello, World!')"
    assert extract_code(completion) is None


def test_extract_code_multiple_markdown():
    # We capture only from the first markdown block
    completion = "```\nprint('Hello, World!')\n```\n```\nfoo = 1\n```\n"
    assert extract_code(completion) == "print('Hello, World!')"


def test_extract_code_empty_markdown():
    completion = "```\n\n```"
    assert extract_code(completion) == ""


def test_extract_code_no_code():
    completion = "This is a prompt with no code"
    assert extract_code(completion) is None

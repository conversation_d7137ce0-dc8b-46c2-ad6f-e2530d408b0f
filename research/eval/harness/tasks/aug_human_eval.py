"""Execute the Augmented HumanEval benchmark v0.

This is an additional set of data generated to expand the original HumanEval
benchmark in order to reduce its high variance.

See `experimental/guy/augmented_human_eval`
"""

from research.core.data_paths import canonicalize_path
from research.eval.harness.tasks.human_eval import HumanEval


class AugHumanEval(HumanEval):
    """Evaluation task for the MBPP benchmark."""

    version: str = "0.0"

    _DATASETS_ROOT = canonicalize_path("data/processed/aug_human_eval.v0")

    DATASETS = {
        "0-shot": f"{_DATASETS_ROOT}/0-shot.jsonl.zst",
    }

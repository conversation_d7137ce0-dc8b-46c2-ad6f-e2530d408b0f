"""Measures how accurate the model is at predicting has-change/no-change."""

import copy
import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Self, cast

from tqdm import tqdm


from base.languages.language_guesser import guess_language
from research.core.types import Document
from research.core.utils import FileLogger
from research.eval.harness.systems.abs_system import AbstractSystem
from research.eval.harness.systems.next_edit_gen_system import (
    EditGenSystemInput,
    EditGenSystemOutput,
    NextEditGenSystem,
)
from research.eval.harness.tasks.abs_task import AbstractTask
from research.eval.harness.tasks.next_edit_gen_eval_task import (
    EvalDataSource,
    EvalSourceJson,
    EvalSourcePickle,
    load_problems_from_eval_source,
)
from research.next_edits.edit_gen_sampler import EditGenProblem

logger = logging.getLogger(__name__)


@dataclass
class NextEditClassificationTask(AbstractTask[EditGenSystemInput, EditGenSystemOutput]):
    """The task for the model's accuracy at predicting has-change/no-change.

    The task name used in yaml config is `next_edit_classification`.
    """

    data_source: EvalDataSource
    """The data source for the task."""

    limit_examples: int | None = None
    """The maximum number of EditGenProblem to load."""

    use_instruction: bool = False
    """Whether to use the instruction in the prompt."""

    VERSION = "1.0"
    """The version number of this eval task.

    Change log:
    """

    def run(
        self,
        system: AbstractSystem[EditGenSystemInput, EditGenSystemOutput],
        output_path: str | Path,
        output_prefix: str = "",
    ) -> dict:
        assert isinstance(system, NextEditGenSystem)
        output_path = Path(output_path)
        output_path.mkdir(exist_ok=True, parents=True)
        logger.info(f"Saving results into {output_path}")
        (output_path / "VERSION.txt").write_text(self.VERSION)

        problems = load_problems_from_eval_source(self.data_source, self.limit_examples)
        # FIXME: this eval task does not support HindsightEditGenProblem yet.
        problems = cast(list[EditGenProblem], problems)
        logger.info(f"Loaded {len(problems)} problems.")
        # we only need to decode one token for classification
        system.generation_options.max_generated_tokens = 1

        n_examples = 0
        n_predicted_changes = 0
        n_has_change = 0
        n_predicted_changes_correct = 0
        n_correct = 0

        # evaluate the system on each problem
        for i, prob in enumerate(tqdm(problems, desc="Evaluating", smoothing=0.1)):
            documents = [
                Document.new(text=code, path=str(path))
                for path, code in prob.repo_change.after_files.items()
                if guess_language(str(path)) is not None
            ]
            # the system will skip any already indexed documents
            system.add_docs(documents)

            # convert FileTuple changes to File changes
            recent_changes = tuple(
                c.map(lambda x: x.to_file()) for c in prob.repo_change.changed_files
            )

            sys_input = EditGenSystemInput(
                path=str(prob.current_path),
                prefix=prob.current_code[: prob.edit_region.start],
                selected_code=prob.current_code[prob.edit_region.to_slice()],
                suffix=prob.current_code[prob.edit_region.stop :],
                instruction=prob.instruction if self.use_instruction else "",
                recent_changes=recent_changes,
                generate_description=False,
                doc_ids=frozenset({doc.id for doc in documents}),
                must_change=False,
                stop_on_pause=False,  # for eval, we don't stop on pause
            )
            log_dir = output_path / f"{output_prefix}_problem_{i}"
            file_logger = FileLogger(log_dir)
            sys_output = system.generate(
                sys_input,
                expected_output=prob.output.replacement
                if prob.output.changed
                else None,
            )
            sys_output.log_to_file(file_logger)

            is_correct = sys_output.changed == prob.output.changed
            summary = f"""\
            predicted = {sys_output.changed}
            expected = {prob.output.changed}
            is_correct = {is_correct}
            """
            file_logger.log("summary.txt", summary)

            n_examples += 1
            if sys_output.changed:
                n_predicted_changes += 1
            if prob.output.changed:
                n_has_change += 1
            if is_correct:
                n_correct += 1
                if sys_output.changed:
                    n_predicted_changes_correct += 1

        precision = (
            n_predicted_changes_correct / n_predicted_changes
            if n_predicted_changes
            else float("nan")
        )
        recall = (
            n_predicted_changes_correct / n_has_change if n_has_change else float("nan")
        )
        f1 = (
            2 * precision * recall / (precision + recall)
            if precision + recall
            else float("nan")
        )
        accuracy = n_correct / n_examples if n_examples else float("nan")

        return {
            "artifacts": str(output_path),
            "precision": precision,
            "recall": recall,
            "f1": f1,
            "accuracy": accuracy,
            "n_examples": n_examples,
        }

    @classmethod
    def from_yaml_config(cls, config: dict) -> Self:
        config = copy.copy(config)
        if "dataset_path" in config:
            dataset_path: str = config["dataset_path"]
            assert dataset_path.endswith(".jsonl.zst") or dataset_path.endswith(
                ".pkl"
            ), f"{dataset_path=} must end with .jsonl.zst or .pkl"
            if dataset_path.endswith(".pkl"):
                data_source = EvalSourcePickle(dataset_path)
            else:
                prefix = dataset_path.split(".jsonl.zst")[0]
                data_source = EvalSourceJson(
                    diffs_path=prefix + ".diffs.jsonl.zst",
                    files_path=prefix + ".files.jsonl.zst",
                )
        else:
            diffs_path = config["diffs_path"]
            files_path = config["files_path"]
            data_source = EvalSourceJson(diffs_path, files_path)
        return cls(
            data_source=data_source,
            limit_examples=config.get("limit_examples", None),
        )

    def __len__(self) -> int:
        raise NotImplementedError()

    def execute(
        self,
        model_input,
        generation: str,
        timeout=None,
    ) -> dict:
        raise NotImplementedError()

    def __getitem__(self, index: int):
        raise NotImplementedError()

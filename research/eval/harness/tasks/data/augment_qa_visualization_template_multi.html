<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AugmentQA Eval Comparison</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f9f9f9;
      margin: 0;
      padding: 0;
    }

    .container {
      padding: 20px;
    }

    .eval-title {
      font-size: 1.2em;
      margin: 0 0 5px 0;
      word-wrap: break-word;
    }

    .eval-path {
      font-size: 0.9em;
      color: #6c757d;
      word-wrap: break-word;
      overflow-wrap: break-word;
      hyphens: auto;
    }

    .eval-results-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }

    .eval-result {
      flex: 1 1 300px;
      /* Adjust the 300px value as needed */
      min-width: 0;
    }

    @media (max-width: 768px) {
      .eval-result {
        flex-basis: 100%;
      }
    }

    .clickable-row:hover {
      background-color: #f1f1f1;
    }

    .sample-number {
      font-weight: bold;
      margin-right: 10px;
      color: #333;
    }

    .score {
      font-size: 0.9em;
      color: #007bff;
      background-color: #e7f1ff;
      padding: 5px 10px;
      border-radius: 5px;
      display: inline-block;
    }

    .message {
      margin-left: 10px;
      font-size: 0.9em;
      color: #333;
      background-color: #e7f1ff;
      padding: 5px 10px;
      border-radius: 5px;
      display: inline-block;
      max-width: 600px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .toggle-content {
      display: none;
      padding: 10px;
      background-color: #f1f1f1;
      border-radius: 5px;
    }

    .metrics-container {
      background-color: #fff;
      padding: 10px;
      border-radius: 5px;
      border: 1px solid #ddd;
      margin-bottom: 20px;
    }

    .metric {
      display: inline-block;
      margin-right: 10px;
      cursor: pointer;
      transition: background-color 0.3s ease;
      padding: 5px 10px;
      border-radius: 5px;
    }

    .metric:hover {
      background-color: #e7f1ff;
    }

    .metric.active {
      background-color: #007bff;
      color: white;
    }

    .sort-indicator {
      margin-left: 5px;
    }

    .card {
      background-color: #fff;
      margin-bottom: 1rem;
      border: 1px solid #ddd;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .clickable-row {
      padding: 10px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    .metrics-row {
      padding: 5px 10px;
      background-color: #f8f9fa;
      border-top: 1px solid #eee;
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
    }

    .metric-value {
      font-size: 0.9em;
      padding: 2px 5px;
      border-radius: 3px;
    }

    /* Highlight styles */
    .metric-highlight-1 {
      background-color: #c8e6c9;
    }

    .metric-highlight-2 {
      background-color: #fff9c4;
    }

    .metric-highlight-3 {
      background-color: #ffcdd2;
    }

    .metric-value.metric-highlight-1 {
      background-color: #c8e6c9;
      padding: 2px 5px;
      border-radius: 3px;
    }

    .metric-value.metric-highlight-2 {
      background-color: #fff9c4;
      padding: 2px 5px;
      border-radius: 3px;
    }

    .metric-value.metric-highlight-3 {
      background-color: #ffcdd2;
      padding: 2px 5px;
      border-radius: 3px;
    }

    .clear-sort {
      background-color: #f8f9fa;
      border: 1px solid #ddd;
      padding: 5px 10px;
      border-radius: 5px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    .clear-sort:hover {
      background-color: #e2e6ea;
    }

    .clear-sort.hidden {
      display: none;
    }

    .global-toggle {
      background: #007bff;
      color: white;
      padding: 10px 20px;
      margin: 20px 0;
      cursor: pointer;
      border: none;
      border-radius: 5px;
      display: inline-block;
      font-size: 1em;
      transition: background 0.3s ease, transform 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .global-toggle:hover {
      background: #0056b3;
      transform: scale(1.05);
    }

    .global-toggle:active {
      background: #003f7f;
      transform: scale(0.95);
    }

    .toggle-content {
      padding: 10px;
      background-color: #f1f1f1;
      border-radius: 5px;
    }

    .extra-info {
      padding: 10px;
      background-color: #f5f5f5;
      margin-top: 5px;
      border-radius: 5px;
    }

    .markdown-content {
      border: 1px solid #ddd;
      padding: 10px;
      border-radius: 5px;
      background-color: #fff;
      white-space: pre-wrap;
      word-wrap: break-word;
    }

    .chunk-info {
      background-color: #f0f0f0;
      margin: 5px 0;
      padding: 5px;
      border-radius: 5px;
    }

    .chunk-path {
      cursor: pointer;
    }

    .chunk-path-blue {
      color: #007bff;
    }

    .chunk-path-pink {
      color: #ff007b;
    }

    .chunk-text {
      display: none;
      margin-top: 5px;
      background-color: #fff;
      padding: 5px;
      border-radius: 3px;
    }

    .metric-toggles {
      margin-bottom: 20px;
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      border: 1px solid #e9ecef;
    }

    .metric-toggles h3 {
      margin-top: 0;
      margin-bottom: 10px;
    }

    .toggle-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }

    .metric-toggle {
      padding: 5px 10px;
      background-color: #e9ecef;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      transition: background-color 0.3s;
    }

    .metric-toggle.active {
      background-color: #007bff;
      color: white;
    }

    .metric-toggle:hover {
      background-color: #007bff;
      color: white;
    }

    .sticky-header {
      position: sticky;
      top: 0;
      background-color: #fff;
      z-index: 1000;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .eval-result {
      position: relative;
    }

    .eval-header {
      padding: 10px;
      margin-bottom: 15px;
    }
  </style>
</head>
</head>

<body>
  <div class="container">
    <h2>Augment QA Eval Comparison</h2>
    <div class="metric-toggles">
      <h3>Toggle Metrics:</h3>
      <div class="toggle-buttons">
        {% for metric in eval_results[0].metrics.keys() %}
        <button class="metric-toggle active" data-metric="{{ metric }}">{{ metric }}</button>
        {% endfor %}
      </div>
    </div>

    <div class="eval-results-container">
      {% for eval_result in eval_results %}
      <div class="eval-result">
        <div class="sticky-header eval-header">
          <h3 class="eval-title">{{ eval_result.header }}</h3>
          <div class="eval-path">{{ eval_result.file_path }}</div>
        </div>

        <div class="metrics-container">
          {% for key, value in eval_result.metrics.items() %}
          <div class="metric" data-metric="{{ key }}">
            <strong>{{ key }}</strong>
            <span>{{ "%0.3g"|format(value) }}</span>
            <span class="sort-indicator"></span>
          </div>
          {% endfor %}
          <button class="clear-sort hidden">Clear Sorting</button>
        </div>

        <button class="global-toggle">Toggle All Samples</button>

        <div class="output-container">
          {% for output in eval_result.outputs %}
          <div class="card" data-metrics='{{ output.metrics | tojson }}' data-output-index="{{ loop.index0 }}">
            <div class="clickable-row">
              <span class="sample-number">#{{ loop.index }}</span>
              <span class="message">{{ output.sample.message | truncate(50) }}</span>
            </div>
            <div class="metrics-row">
              {% for key, value in output.metrics.items() %}
              <span class="metric-value" data-metric="{{ key }}">{{ key }}: {{ "%0.3f"|format(value) }}</span>
              {% endfor %}
            </div>
            <div class="toggle-content">
              {% if output.result.extra_output.additional_info.get("request_id") %}
              <div class="extra-info">
                <strong>Request ID:</strong> {{ output.result.extra_output.additional_info.get("request_id") }}
              </div>
              {% endif %}
              {% if output.sample.chat_history %}
              <div class="extra-info">
                <strong>Chat History:</strong>
                {% for exchange in output.sample.chat_history %}
                <div class="markdown-content">
                  <strong>User:</strong> {{ exchange.request_message | escape }}<br>
                  <strong>AI:</strong> {{ exchange.response_text | escape }}
                </div>
                {% endfor %}
              </div>
              {% endif %}
              <div class="extra-info">
                <strong>Question:</strong> {{ output.sample.message | escape }}
              </div>
              <div class="extra-info">
                <strong>Generated Text:</strong>
                <div class="markdown-content">
                  {{- output.result.generated_text | escape -}}
                </div>
              </div>
              <div class="extra-info">
                <strong>Expected Reply:</strong>
                <div class="markdown-content">
                  {{- output.sample.target | escape -}}
                </div>
              </div>
              {% if output.sample.keywords %}
              <div class="extra-info">
                <strong>Keywords:</strong>
                {% for keyword in output.sample.keywords %}
                <code>{{ keyword | escape }}</code>{% if not loop.last %}, {% endif %}
                {% endfor %}
              </div>
              {% endif %}
              {% if output.sample.gold_paths %}
              <div class="extra-info">
                <strong>Gold Paths:</strong>
                {% for path in output.sample.gold_paths %}
                <code>{{ path | escape }}</code>{% if not loop.last %}, {% endif %}
                {% endfor %}
              </div>
              {% endif %}
              {% if output.result.retrieved_chunks %}
              <div class="extra-info">
                <strong>Retrieved chunks:</strong>
                {% for chunk in output.result.retrieved_chunks|sort(attribute='score', reverse=True) %}
                <div class="chunk-info">
                  <span
                    class="chunk-path {% if chunk.path in output.sample.gold_paths %}chunk-path-blue{% else %}chunk-path-pink{% endif %}">{{
                    chunk.parent_doc.path | escape }}:{{ chunk.line_offset }}-{{ chunk.line_offset +
                    chunk.length_in_lines
                    }}</span>
                  <div class="chunk-text">
                    <pre>{{ chunk.text | escape }}</pre>
                  </div>
                </div>
                {% endfor %}
              </div>
              {% endif %}
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
  <script>
    document.addEventListener("DOMContentLoaded", function () {
      const evalResults = document.querySelectorAll('.eval-result');
      const outputContainers = document.querySelectorAll('.output-container');

      // Toggle chunk text visibility
      document.querySelectorAll('.chunk-info').forEach(row => {
        row.addEventListener('click', function (event) {
          event.stopPropagation();
          const content = this.querySelector('.chunk-text');
          content.style.display = content.style.display === "none" || content.style.display === "" ? "block" : "none";
        });
      });

      // Toggle corresponding outputs
      function toggleCorrespondingOutputs(outputIndex) {
        const cards = document.querySelectorAll(`.card[data-output-index="${outputIndex}"]`);
        const isAnyVisible = Array.from(cards).some(card => card.querySelector('.toggle-content').style.display === "block");
        cards.forEach(card => {
          card.querySelector('.toggle-content').style.display = isAnyVisible ? "none" : "block";
        });
      }

      // Highlight best metrics
      function highlightBestMetrics() {
        const metricKeys = Array.from(evalResults[0].querySelectorAll('.metric')).map(m => m.dataset.metric);

        metricKeys.forEach(metricKey => {
          const metricValues = Array.from(evalResults).map(evalResult => ({
            element: evalResult.querySelector(`.metric[data-metric="${metricKey}"]`),
            value: parseFloat(evalResult.querySelector(`.metric[data-metric="${metricKey}"] span`).textContent)
          }));

          metricValues.forEach(mv => mv.element.classList.remove('metric-highlight-1', 'metric-highlight-2', 'metric-highlight-3'));

          // Check if the metric is related to generation time
          const isTimeMetric = metricKey.toLowerCase().includes('time') || metricKey.toLowerCase().includes('latency');

          if (metricValues.length === 2) {
            // For two values, directly compare them
            if (metricValues[0].value > metricValues[1].value) {
              // Invert colors for time metrics
              if (isTimeMetric) {
                metricValues[0].element.classList.add('metric-highlight-3');
                metricValues[1].element.classList.add('metric-highlight-1');
              } else {
                metricValues[0].element.classList.add('metric-highlight-1');
                metricValues[1].element.classList.add('metric-highlight-3');
              }
            } else if (metricValues[0].value < metricValues[1].value) {
              // Invert colors for time metrics
              if (isTimeMetric) {
                metricValues[0].element.classList.add('metric-highlight-1');
                metricValues[1].element.classList.add('metric-highlight-3');
              } else {
                metricValues[0].element.classList.add('metric-highlight-3');
                metricValues[1].element.classList.add('metric-highlight-1');
              }
            } else {
              // If they're equal, both get the middle color
              metricValues[0].element.classList.add('metric-highlight-2');
              metricValues[1].element.classList.add('metric-highlight-2');
            }
          } else {
            // For more than two values, use the ranking approach
            metricValues.sort((a, b) => isTimeMetric ? a.value - b.value : b.value - a.value);

            ['metric-highlight-1', 'metric-highlight-2', 'metric-highlight-3'].forEach((cls, i) => {
              if (metricValues[i]) metricValues[i].element.classList.add(cls);
            });
          }
        });
      }

      // Highlight best output metrics
      function highlightBestOutputMetrics() {
        const outputIndices = Array.from(document.querySelectorAll('.card')).map(card => card.dataset.outputIndex);

        outputIndices.forEach(outputIndex => {
          const cards = document.querySelectorAll(`.card[data-output-index="${outputIndex}"]`);
          const metricKeys = Object.keys(JSON.parse(cards[0].dataset.metrics));

          metricKeys.forEach(metricKey => {
            const metricValues = Array.from(cards).map(card => ({
              element: card.querySelector(`.metric-value[data-metric="${metricKey}"]`),
              value: JSON.parse(card.dataset.metrics)[metricKey]
            }));

            // Check if the metric is related to generation time
            const isTimeMetric = metricKey.toLowerCase().includes('time') || metricKey.toLowerCase().includes('latency');

            // For time metrics in individual comparisons, always use yellow
            if (isTimeMetric) {
              metricValues.forEach(mv => {
                mv.element.classList.remove('metric-highlight-1', 'metric-highlight-2', 'metric-highlight-3');
                mv.element.classList.add('metric-highlight-2');  // Always yellow for time metrics
              });
              return;  // Skip the rest of the comparison logic for time metrics
            }

            if (metricValues.length === 2) {
              // For two values, directly compare them (non-time metrics only)
              if (metricValues[0].value > metricValues[1].value) {
                metricValues[0].element.classList.add('metric-highlight-1');
                metricValues[1].element.classList.add('metric-highlight-3');
              } else if (metricValues[0].value < metricValues[1].value) {
                metricValues[0].element.classList.add('metric-highlight-3');
                metricValues[1].element.classList.add('metric-highlight-1');
              } else {
                metricValues[0].element.classList.add('metric-highlight-2');
                metricValues[1].element.classList.add('metric-highlight-2');
              }
            } else {
              // For more than two values, use the median approach (non-time metrics only)
              const sortedValues = metricValues.map(mv => mv.value).sort((a, b) => b - a);
              const median = sortedValues[Math.floor(sortedValues.length / 2)];

              metricValues.forEach(mv => {
                mv.element.classList.remove('metric-highlight-1', 'metric-highlight-2', 'metric-highlight-3');
                if (mv.value > median) {
                  mv.element.classList.add('metric-highlight-1');
                } else if (mv.value === median) {
                  mv.element.classList.add('metric-highlight-2');
                } else {
                  mv.element.classList.add('metric-highlight-3');
                }
              });
            }
          });
        });
      }

      // Sorting functionality
      let currentSortMetric = null;
      let sortDirection = 'desc';
      let currentSortedEvalIndex = null;

      function sortOutputs(containerIndex, metric) {
        const container = outputContainers[containerIndex];
        const cards = Array.from(container.getElementsByClassName('card'));

        cards.sort((a, b) => {
          const metricsA = JSON.parse(a.dataset.metrics);
          const metricsB = JSON.parse(b.dataset.metrics);
          return sortDirection === 'asc' ? metricsA[metric] - metricsB[metric] : metricsB[metric] - metricsA[metric];
        });

        outputContainers.forEach(otherContainer => {
          const otherCards = Array.from(otherContainer.getElementsByClassName('card'));
          cards.forEach(card => {
            const correspondingCard = otherCards.find(c => c.dataset.outputIndex === card.dataset.outputIndex);
            otherContainer.appendChild(correspondingCard);
          });
        });

        highlightBestMetrics();
        highlightBestOutputMetrics();
      }

      function updateSortIndicators(evalIndex, clickedMetric) {
        evalResults.forEach((evalResult, index) => {
          evalResult.querySelectorAll('.metric').forEach(metric => {
            const indicator = metric.querySelector('.sort-indicator');
            if (index === evalIndex && metric.dataset.metric === clickedMetric) {
              indicator.textContent = sortDirection === 'asc' ? '▲' : '▼';
              metric.classList.add('active');
            } else {
              indicator.textContent = '';
              metric.classList.remove('active');
            }
          });
          evalResult.querySelector('.clear-sort').classList.toggle('hidden', index !== evalIndex || !clickedMetric);
        });
      }

      function clearAllSort() {
        currentSortMetric = null;
        sortDirection = 'desc';
        currentSortedEvalIndex = null;
        updateSortIndicators(null, null);

        const originalOrder = Array.from(outputContainers[0].getElementsByClassName('card'))
          .sort((a, b) => parseInt(a.dataset.outputIndex) - parseInt(b.dataset.outputIndex));

        outputContainers.forEach(container => {
          originalOrder.forEach(card => {
            const correspondingCard = container.querySelector(`.card[data-output-index="${card.dataset.outputIndex}"]`);
            container.appendChild(correspondingCard);
          });
        });

        highlightBestMetrics();
        highlightBestOutputMetrics();
      }

      // Event listeners
      document.querySelectorAll('.metric-toggle').forEach(toggle => {
        toggle.addEventListener('click', function () {
          const metric = this.dataset.metric;
          this.classList.toggle('active');
          const isActive = this.classList.contains('active');

          document.querySelectorAll(`.metric[data-metric="${metric}"], .metric-value[data-metric="${metric}"]`)
            .forEach(el => el.style.display = isActive ? '' : 'none');
        });
      });

      evalResults.forEach((evalResult, index) => {
        evalResult.querySelectorAll('.metric').forEach(metric => {
          metric.addEventListener('click', function () {
            const clickedMetric = this.dataset.metric;

            if (currentSortMetric === clickedMetric && currentSortedEvalIndex === index) {
              sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
              currentSortMetric = clickedMetric;
              sortDirection = 'desc';
            }

            currentSortedEvalIndex = index;
            sortOutputs(index, clickedMetric);
            updateSortIndicators(index, clickedMetric);
          });
        });
      });

      document.querySelectorAll('.clear-sort').forEach(button => {
        button.addEventListener('click', clearAllSort);
      });

      document.querySelectorAll('.global-toggle').forEach(globalToggle => {
        globalToggle.addEventListener('click', function () {
          const allContents = document.querySelectorAll('.toggle-content');
          const isAnyVisible = Array.from(allContents).some(content => content.style.display === "block");
          allContents.forEach(content => {
            content.style.display = isAnyVisible ? "none" : "block";
          });
        });
      });

      document.querySelectorAll('.clickable-row').forEach(row => {
        row.addEventListener('click', function () {
          const card = this.closest('.card');
          const outputIndex = card.dataset.outputIndex;
          toggleCorrespondingOutputs(outputIndex);
        });
      });

      // Initial highlighting
      highlightBestMetrics();
      highlightBestOutputMetrics();
    });
  </script>
</body>

</html>

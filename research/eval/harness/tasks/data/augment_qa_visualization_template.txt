<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>AugmentQA Eval</title>
<style>
  body { font-family: Arial, sans-serif; background-color: #f9f9f9; margin: 0; padding: 0; }
  .container { padding: 20px; }
  .card { background-color: #fff; margin-bottom: 1rem; border: 1px solid #ddd; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
  .clickable-row { padding: 10px; cursor: pointer; display: flex; align-items: center; transition: background-color 0.3s ease; }
  .clickable-row:hover { background-color: #f1f1f1; }
  .sample-number { font-weight: bold; margin-right: 10px; color: #333; }
  .score { font-size: 0.9em; color: #007bff; background-color: #e7f1ff; padding: 5px 10px; border-radius: 5px; display: inline-block; }
  .message { margin-left: 10px; font-size: 0.9em; color: #333; background-color: #e7f1ff; padding: 5px 10px; border-radius: 5px; display: inline-block; max-width: 600px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
  .toggle-content, .chunk-text { display: none; padding: 10px; background-color: #f1f1f1; border-radius: 5px; }
  .extra-info { padding: 10px; background-color: #f5f5f5; margin-top: 5px; border-radius: 5px; }
  .chunk-info { background-color: #f0f0f0; margin: 5px 0; padding: 5px; border-radius: 5px; cursor: pointer; }
  .chunk-path { font-size: 0.8em; }
  .markdown-content { border: 1px solid #ddd; padding: 10px; border-radius: 5px; background-color: #fff; }
  .metrics-container { background-color: #fff; padding: 10px; border-radius: 5px; border: 1px solid #ddd; margin-bottom: 20px; }
  .metric { display: inline-block; margin-right: 10px; }
  .metric strong { display: block; }
  code { background-color: #f1f1f1; padding: 2px 4px; border-radius: 3px; }
  .chunk-path-blue { color: #007bff; }
  .chunk-path-pink { color: #ff007b; }
  .metric-value {
    font-size: 0.9em;
    color: #007bff;
    background-color: #e7f1ff;
    padding: 5px 10px;
    border-radius: 5px;
    display: inline-block;
    margin-right: 10px;
  }

  .global-toggle {
    background: #007bff;
    color: white;
    padding: 10px 20px;
    margin: 20px 0;
    cursor: pointer;
    border: none;
    border-radius: 5px;
    display: inline-block;
    font-size: 1em;
    transition: background 0.3s ease, transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .global-toggle:hover {
    background: #0056b3;
    transform: scale(1.05);
  }

  .global-toggle:active {
    background: #003f7f;
    transform: scale(0.95);
  }
</style>
</head>
<body>
<div class="container">

  <h2>{{ header }}</h2>

  <div class="metrics-container">
    {% for key, value in metrics.items() %}
      <div class="metric">
        <strong>{{ key }}</strong>
        <span>{{ "%0.3g"|format(value) }}</span>
      </div>
    {% endfor %}
  </div>

  <button class="global-toggle">Toggle All Samples</button>

  {% for output in outputs %}
    <div class="card">
      <div class="clickable-row">
        <span class="sample-number">#{{ loop.index }}</span>
        <span class="score"> {{ output.metrics_short_str }}</span>
        <span class="message">{{ output.sample.message | escape }}</span>
      </div>
      <div class="toggle-content">
        <div class="extra-info">
          {% for key, value in output.metrics.items() %}
            <span>{{ key }}:</span> <span class="metric-value">{{ "%0.3f"|format(value) }}</span>
          {% endfor %}
        </div>
        {% if output.result.extra_output.additional_info.get("request_id") %}
          <div class="extra-info">
            <strong>Request ID:</strong> {{ output.result.extra_output.additional_info.get("request_id") }}
          </div>
        {% endif %}
        {% if output.sample.chat_history %}
          <div class="extra-info">
            <strong>Chat History:</strong>
            {% for exchange in output.sample.chat_history %}
              <div class="markdown-content">
                <strong>User:</strong> {{ exchange.request_message | escape }}<br>
                <strong>AI:</strong> {{ exchange.response_text | escape }}
              </div>
            {% endfor %}
          </div>
        {% endif %}
        {% if output.sample.selected_code %}
          <div class="extra-info">
            <strong>Selected code:</strong>
            <div class="markdown-content">
              {{ output.selected_code_html | safe }}
            </div>
          </div>
        {% endif %}
        <div class="extra-info">
          <strong>Question:</strong> {{ output.sample.message | escape }}
        </div>
        <div class="extra-info">
          <strong>Generated Text:</strong>
          <div class="markdown-content">
            {{ output.generated_text_html | safe }}
          </div>
        </div>
        <div class="extra-info">
          <strong>Expected Reply:</strong>
          <div class="markdown-content">
            {{ output.target_html | safe }}
          </div>
        </div>
        <div class="extra-info">
          <strong>Keywords:</strong>
          {% for keyword in output.sample.keywords %}
            <code>{{ keyword | escape }}</code>{% if not loop.last %}, {% endif %}
          {% endfor %}
        </div>
        <div class="extra-info">
          <strong>Gold Paths:</strong>
          {% for path in output.sample.gold_paths %}
            <code>{{ path | escape }}</code>{% if not loop.last %}, {% endif %}
          {% endfor %}
        </div>
        <div class="extra-info">
          <strong>Retrieved chunks:</strong>
          {% for chunk in output.result.retrieved_chunks %}
            <div class="chunk-info">
              <span class="chunk-path {% if chunk.path in output.sample.gold_paths %}chunk-path-blue{% else %}chunk-path-pink{% endif %}">{{ chunk.path | escape }}:{{ chunk.line_range.start }}-{{ chunk.line_range.stop }}</span>
              <div class="chunk-text">
                <pre>{{ chunk.text | escape }}</pre>
              </div>
            </div>
          {% endfor %}
        </div>
      </div>
    </div>
  {% endfor %}

</div>

<script>
document.addEventListener("DOMContentLoaded", function() {
  document.querySelectorAll('.clickable-row').forEach(row => {
    row.addEventListener('click', function() {
      const content = this.nextElementSibling;
      if (content.style.display === "none" || content.style.display === "") {
        content.style.display = "block";
      } else {
        content.style.display = "none";
      }
    });
  });

  document.querySelectorAll('.chunk-info').forEach(row => {
    row.addEventListener('click', function() {
      const content = this.querySelector('.chunk-text');
      if (content.style.display === "none" || content.style.display === "") {
        content.style.display = "block";
      } else {
        content.style.display = "none";
      }
    });
  });

  const globalToggleButton = document.querySelector('.global-toggle');
  globalToggleButton.addEventListener('click', function() {
    const allContents = document.querySelectorAll('.toggle-content');
    const isAnyVisible = Array.from(allContents).some(content => content.style.display === "block");
    allContents.forEach(content => {
      content.style.display = isAnyVisible ? "none" : "block";
    });
  });
});
</script>
</body>
</html>

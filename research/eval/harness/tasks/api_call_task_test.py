"""Unit test for APICallTask.

pytest research/eval/tests/tasks/test_api_call_task.py
"""

import unittest

import mock
import parameterized

from research.eval.harness.tasks import SUPPORTED_DATASET2DIR, ApiCallTask, create_task
from research.eval.tests.hydra import mock_k8s_utils


class TestAPICallTask(unittest.TestCase):
    """Test all the functionality in APICallTask."""

    @parameterized.parameterized.expand(
        [
            "finegrained-python.small.v1.0",
            "finegrained-python.large.v1.0",
        ]
    )
    def test_finegrained_datasets(self, dataset_name: str):
        task = ApiCallTask(dataset_dir=SUPPORTED_DATASET2DIR[dataset_name])
        # Additional args passed into the driver.Driver() call in the task
        task.additional_driver_args = {
            "max_containers_per_pod": 1,
            "max_queue_latency_sec": 0.1,
            "queue_sleep_sec": 0.0,
        }

        mock_k8 = mock_k8s_utils.MockK8("succeeded")
        with (
            mock.patch(
                "research.eval.hydra.driver.Driver.LaunchThrottle.throttle",
                new=lambda _: None,
            ),
            mock.patch(
                "research.eval.hydra.driver.Driver._generate_guid",
                new=lambda x: "mock-container",
            ),
            mock.patch("research.eval.hydra.driver.k8s_utils", new=mock_k8),
            mock.patch(
                "research.eval.hydra.driver._provider",
                new=mock_k8s_utils.MockProvider(
                    {"google/pyglove": "v1.0", "pydantic/pydantic": "v1.0"}
                ),
            ),
        ):
            for index in range(len(task)):
                target_completion = task[index].model_input.target
                if target_completion is None:
                    raise ValueError(
                        f"{task.name}[{index}] should have non-empty target."
                    )
                results = task.execute(task[index].model_input, target_completion)
                assert results["pass"], f"{task.name}[{index}] should pass."

    @parameterized.parameterized.expand(
        [
            ("finegrained-python.small.v1.0", 180),
            ("finegrained-python.large.v1.0", 1053),
        ]
    )
    def test_total_examples(self, dataset_name: str, total: int):
        dataset = create_task({"name": "api", "dataset": dataset_name})
        self.assertEqual(
            len(dataset), total, msg=f"{dataset} expect to have {total} examples."
        )

    @parameterized.parameterized.expand(
        [
            ("finegrained-python.small.v1.0", 180, 100),
            ("finegrained-python.large.v1.0", 1053, 100),
            ("finegrained-python.large.v1.0", 1053, 0),
            ("finegrained-python.large.v1.0", 1053, None),
            ("finegrained-python.large.v1.0", 1053, 100),
        ]
    )
    def test_total_examples_with_limit(self, dataset_name: str, total: int, limit: int):
        dataset = create_task({"name": "api", "dataset": dataset_name, "limit": limit})
        size = limit or total
        self.assertEqual(
            len(dataset),
            size,
            msg=(
                f"{dataset} with limit {limit} should have {size} examples, but has"
                f" {len(dataset)} examples."
            ),
        )

    @parameterized.parameterized.expand(
        [
            ("repoeval_functions", 455),
            ("repoeval_2-3lines", 554),
            ("all_languages_2-3lines_medium_to_hard.v1.0", 1221),
        ]
    )
    def test_for_non_finegrained_dataset(self, dataset_name: str, total: int):
        dataset = create_task({"name": "api", "dataset": dataset_name})
        self.assertEqual(
            len(dataset), total, msg=f"{dataset} expect to have {total} examples."
        )

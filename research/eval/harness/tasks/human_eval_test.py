"""Unit test for human eval task and its sub-classed tasks.

pytest research/eval/tests/tasks/test_humaneval.py
"""

import unittest
from typing import Type

from parameterized import parameterized

from research.eval.harness import tasks
from research.eval.harness.tasks.abs_task import CodeCompleteTask


class TestRandomIndex(unittest.TestCase):
    """Test __getitem__ and execute functions."""

    def _get_tasks(self, task_cls, variants: list[str]) -> list[CodeCompleteTask]:
        if variants:
            return [task_cls(variant=variant) for variant in variants]
        else:
            return [task_cls()]

    @parameterized.expand(
        [
            (
                tasks.HumanEval,
                (0, 11, 38, 88, 163),
                ["0-shot", "1-shot", "2-shot", "5-shot"],
            ),
            (tasks.HumanEvalFim, (0, 8, 88, 888, 5814), []),
            (tasks.MBPP, (0, 1, 8, 88, 499), []),
        ]
    )
    def test_target_as_solution(
        self,
        task_cls: Type[CodeCompleteTask],
        indexes: tuple[int, ...],
        variants: list[str],
    ):
        tasks_to_test = self._get_tasks(task_cls, variants)
        for task in tasks_to_test:
            for index in indexes:
                inputs, _ = task[index]
                target_completion = inputs.target
                if target_completion is None:
                    raise ValueError(
                        f"{task_cls.__name__}[{index}] should have non-empty target."
                    )
                results = task.execute(inputs, target_completion, timeout=3.0)
                assert results["pass"], f"{task_cls.__name__}[{index}] should pass."

                timeout = 0.00001
                results = task.execute(inputs, target_completion, timeout=timeout)
                assert not results[
                    "pass"
                ], f"{task_cls.__name__}[{index}] should timeout when we set timeout as {timeout}."

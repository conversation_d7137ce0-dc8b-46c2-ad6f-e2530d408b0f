"""The Hindsight Task.

A benchmark of code generation tasks based on real completions from users,
with ground truths derived heuristically from the code they wrote after.
"""

import logging
import time
from collections.abc import Sized
from dataclasses import dataclass, field
from pathlib import Path
from typing import Mapping, Optional, Union

import dataclasses_json
import zstandard as zstd
from dataclasses_json import dataclass_json
from marshmallow import fields

from base.datasets import completion
from base.diff_utils.edit_events import GranularEditEvent, SingleEdit
from base.datasets.gcs_blob_cache import PathAndContent
from base.datasets.hindsight_completion import HindsightCompletionDatum
from base.datasets.hindsight_completion_dataset import HindsightCompletionDataset
from base.datasets.tenants import get_tenant
from base.languages.comment_guesser import (
    guess_is_multi_line_comment,
    guess_is_single_line_comment,
)
from research.core.artifacts import collect_artifacts
from research.core.data_paths import canonicalize_path
from research.core.model_input import ModelInput
from research.core.recency_info import convert_from_datasets_recency_info
from research.core.types import <PERSON><PERSON><PERSON><PERSON><PERSON>, Document
from research.eval.harness.metrics import (
    CodeCompleteMetrics,
    DatumTag,
    MetricsAggregator,
    compute_code_complete_metrics,
    compute_common_code_complete_tags,
    nest_metrics,
    safe_forward_metrics,
)
from research.eval.harness.systems.abs_system import (
    CodeCompleteInput,
    CodeCompleteSystem,
)
from research.eval.harness.tasks.abs_task import CodeCompleteTask, DocsType

logger = logging.getLogger(__name__)


@dataclass
class HindsightOutput(dataclasses_json.DataClassJsonMixin):
    """The output of the Hindsight Task.

    One such record will be produced per evaluated patch.
    We don't store the actual datum, since those objects can be rather large,
    and they are the same as in the dataset file.
    """

    request_id: str
    """Example request id."""

    prefix: str
    """The prefix string for the task."""

    suffix: str
    """The suffix string for the task."""

    prompt: str
    """The actual prompt used for generation.

    The prefix, suffix, and chunks may be changed to form the actual prompt,
    based on budget constraints.
    """

    prompt_tokens: list[int]
    """The prompt tokens."""

    generation: str
    """The full output text from generation."""

    ground_truth: str
    """The ground truth text."""

    metrics: CodeCompleteMetrics
    """The scores for this completions."""

    tags: list[DatumTag]
    """Tags for this example."""

    artifacts: list[dict]
    """Any additional artifacts to record for the patch."""

    path: str = ""
    """The path of the file being completed."""


def convert_edit_events(
    edit_events: list[completion.GranularEditEvent],
) -> list[GranularEditEvent]:
    return [
        GranularEditEvent(
            path=event.path,
            before_blob_name=event.before_blob_name,
            after_blob_name=event.after_blob_name,
            edits=[
                SingleEdit(
                    before_start=edit.before_start,
                    after_start=edit.after_start,
                    before_text=edit.before_text,
                    after_text=edit.after_text,
                )
                for edit in event.edits
            ],
        )
        for event in edit_events
    ]


def convert_hindsight_datum_to_model_input(
    datum: HindsightCompletionDatum,
) -> ModelInput:
    request = datum.completion.request
    blob_names = list(request.blob_names)

    # base/datasets removes the blob_name of the current file, so add it back.
    if (
        request.position is not None
        and request.position.blob_name
        and request.position.blob_name not in blob_names
    ):
        blob_names.append(request.position.blob_name)

    recency_info = (
        convert_from_datasets_recency_info(datum.completion.request.recency_info)
        if datum.completion.request.recency_info is not None
        else None
    )
    cursor_position = (
        request.position.cursor_position if request.position is not None else None
    )

    edit_events = (
        convert_edit_events(request.edit_events)
        if request.edit_events is not None
        else None
    )

    model_input = CodeCompleteInput(
        prefix=request.prefix,
        suffix=request.suffix,
        target=datum.ground_truth,
        path=request.path,
        doc_ids=blob_names,
        recency_info=recency_info,
        cursor_position=cursor_position,
        edit_events=edit_events,
        extra={
            # NOTE(arun): Why do we have this if we have `target`?
            "ground_truth": datum.ground_truth,
        },
    )
    return model_input


def get_hindsight_tags(
    model_input: ModelInput, datum: HindsightCompletionDatum
) -> list[DatumTag]:
    tags = compute_common_code_complete_tags(model_input)
    tags.append(
        DatumTag(category="response_model", value=datum.completion.response.model)
    )

    if guess_is_multi_line_comment(
        model_input.prefix, model_input.suffix, model_input.path
    ):
        comment = "multi_line_comment"
    elif guess_is_single_line_comment(
        model_input.prefix, model_input.suffix, model_input.path
    ):
        comment = "single_line_comment"
    else:
        comment = "code"

    tags.append(
        DatumTag(
            category="comment",
            value=comment,
        )
    )
    return tags


class HindsightCompletionTask(CodeCompleteTask):
    """The Hindsight Task.

    Given a set of patches and a retrieval database, generate a set of documents.
    Score the generation using exact match and edit similarity.
    """

    @dataclass_json
    @dataclass
    class Config:
        """Configuration for the Hindsight task.

        Looks for {dataset_base_dir}/{dataset}/{tenant_name}/data.jsonl.zst
        (or data.jsonl if that doesn't exist).
        """

        dataset: str
        """The dataset subdirectory."""

        tenant_name: str
        """The name of the tenant configuration to use during evaluation.

        This should match one of the keys in `base/datasets/tenants.py`.
        """

        limit: Optional[int] = None
        """Maximum number of examples to use."""

        blob_limit: Optional[int] = None
        """Maximum number of blobs. If set, disables retrieval for that example."""

        order: Optional[str] = None
        """Order the dataset, which can be useful for testing stateful systems.
        Options:
          - None: original dataset order, which is by request_id (effectively random)
          - session_time: order by (tenant, user, session_id, time).
        """

        service_account_file: Optional[Path] = field(
            default=None,
            metadata=dataclasses_json.config(
                encoder=str, decoder=Path, mm_field=fields.Str()
            ),
        )
        """Path to optional GCP Service Account .json file.

        If the file doesn't exist, this argument will be treated as if it
        hadn't been set.
        """

        dataset_base_dir: Path = field(
            default=Path(canonicalize_path("data/eval/hindsight/")),
            metadata=dataclasses_json.config(
                encoder=str, decoder=Path, mm_field=fields.Str()
            ),
        )
        """The base directory for datasets."""

        result_labels: dict[str, str] | None = None
        """Arbitrary labels to include in the result metrics."""

    def __init__(
        self,
        dataset: HindsightCompletionDataset,
        cfg: Config | None = None,
    ):
        self._dataset = dataset
        self._cfg = cfg
        # Maintains the list of blobs seen so far. We keep track of this as the
        # system abstraction doesn't let us easily access the retrievers and figure
        # out which blobs they currently store.
        self._seen_blobs = set[str]()
        self._signature_blobs = dict[str, Path]()

    def execute(
        self,
        model_input: ModelInput,
        generation: str,
        timeout: Optional[float] = None,
    ):
        raise NotImplementedError()

    def __getitem__(self, index: int) -> tuple[ModelInput, DocsType]:
        """Get the index-th example in this task."""
        raise ValueError("Cannot randomly index into this task.")

    def __len__(self) -> int:
        """Return the total number of examples in this task."""
        raise ValueError("Cannot measure the size of this task.")

    def _update_index(
        self,
        system: CodeCompleteSystem,
        new_blobs: Mapping[str, Optional[PathAndContent]],
    ):
        """Update the blob cache with the given blobs."""
        # These are the genuinely new blobs.
        docs_to_add = [
            Document(
                id=blob_name,
                text=blob.content,
                path=str(blob.path),
                meta=None,
            )
            for blob_name in (new_blobs.keys() - self._seen_blobs)
            if (blob := new_blobs.get(blob_name)) is not None
        ]

        system.add_docs(docs_to_add)
        self._seen_blobs.update(new_blobs.keys() - self._seen_blobs)
        logger.info("The index now contains %d docs.", len(self._seen_blobs))

    def run_one(
        self,
        system: CodeCompleteSystem,
        datum: HindsightCompletionDatum,
        blobs: Mapping[str, Optional[PathAndContent]],
    ) -> HindsightOutput:
        """Run the task with the given system and save the results into output_path/output_prefix_xxx."""
        self._update_index(system, blobs)

        model_input = convert_hindsight_datum_to_model_input(datum)
        tags = get_hindsight_tags(model_input, datum)

        with collect_artifacts() as collector:
            result = system.generate(model_input)
            artifacts = collector.get_artifacts()
        forward_metrics = safe_forward_metrics(system, model_input)

        assert model_input.target is not None
        metrics = compute_code_complete_metrics(
            result.generated_text, model_input.target, forward_metrics
        )
        return HindsightOutput(
            request_id=datum.completion.request_id,
            path=datum.completion.request.path,
            prefix=model_input.prefix,
            suffix=model_input.suffix,
            prompt=system.get_model().tokenizer.detokenize(result.prompt_tokens),
            prompt_tokens=result.prompt_tokens,
            generation=result.generated_text,
            ground_truth=datum.ground_truth,
            metrics=metrics,
            tags=tags,
            artifacts=artifacts,
        )

    def run(
        self,
        system: CodeCompleteSystem,
        output_path: Union[str, Path],
        output_prefix: str = "",
    ) -> dict:
        """Run the task with the given system and save the results into output_path/output_prefix_xxx."""

        start_timestamp = time.time()
        start_time = time.monotonic()

        output_path = Path(output_path)
        output_path.mkdir(exist_ok=True, parents=True)
        logger.info(f"Save results into {output_path}")

        results = list[HindsightOutput]()
        metrics_aggregator = MetricsAggregator()

        dataset_len = (
            len(self._dataset.data) if isinstance(self._dataset.data, Sized) else None
        )
        for datum, blobs in self._dataset:
            logger.info(
                "Evaluating on request %s (%d/%s)",
                datum.completion.request_id,
                len(results) + 1,
                dataset_len,
            )
            output = self.run_one(system, datum, blobs)
            metrics_aggregator.add(output.metrics.to_float_dict(), tags=output.tags)
            results.append(output)

        metrics = nest_metrics(metrics_aggregator.compute())
        # Make sure summary metrics are at end of dict
        assert isinstance(metrics, dict)
        metrics["all"] = metrics.pop("all")
        logger.info("Scores: %s", metrics["all"]["all"])

        if output_path.exists():
            # where does file_name come from?
            file_name = f"{system.model.name}_{self.name}"  # type: ignore
            out_file = Path(
                output_path / f"{output_prefix}{file_name}_completed_patches.jsonl.zst"
            )
            logger.info(f"Writing results to {out_file}")

            with zstd.open(out_file, "w", encoding="utf-8") as f:
                for doc in results:
                    f.write(doc.to_json())  # type: ignore
                    f.write("\n")
        else:
            out_file = ""

        end_time = time.monotonic()

        ret = {
            "artifact": str(out_file),
            "metrics": metrics,
            "start_time": start_timestamp,
            "run_duration": end_time - start_time,
        }
        if cfg := self._cfg:
            ret.update(
                {
                    "limit": cfg.limit,
                    "dataset": cfg.dataset,
                    "labels": cfg.result_labels if cfg.result_labels else {},
                }
            )
        return ret

    @staticmethod
    def _order_dataset(
        data: list[HindsightCompletionDatum], order: str
    ) -> list[HindsightCompletionDatum]:
        """Order the dataset by the given order."""
        if order == "session_time":
            # TODO(jeff): We currently don't store session_id.
            # Sort by (user_id, time) should be good enough for now.
            return sorted(
                data,
                key=lambda datum: (
                    datum.completion.user_id,
                    datum.completion.request.timestamp,
                ),
            )

        raise ValueError(f"Unknown order: {order}")

    @classmethod
    def from_yaml_config(cls, config: dict) -> "HindsightCompletionTask":
        """Returns a Task object constructed using a config dictionary."""
        # pylint: disable-next=no-member
        cfg: HindsightCompletionTask.Config = (
            HindsightCompletionTask.Config.schema().load(config)  # type: ignore
        )
        logger.info("Running with configuration %s", cfg)

        dataset_path = (
            cfg.dataset_base_dir / cfg.dataset / cfg.tenant_name / "data.jsonl.zst"
        )
        if dataset_path.exists():
            logger.info("Loading dataset from %s", dataset_path)
            with zstd.open(dataset_path, "r", encoding="utf-8") as f:
                data = HindsightCompletionDataset.load_data(f, cfg.limit)
        else:
            dataset_path = dataset_path.with_name("data.jsonl")
            logger.info("Loading dataset from %s", dataset_path)
            with dataset_path.open() as f:
                data = HindsightCompletionDataset.load_data(f, cfg.limit)

        if cfg.limit is not None:
            assert len(data) <= cfg.limit, f"Loaded {len(data)} > {cfg.limit} examples."

        if cfg.blob_limit is not None:
            over_blob_limit_count = 0
            for datum in data:
                blob_names = datum.completion.request.blob_names
                if len(blob_names) > cfg.blob_limit:
                    logger.info(
                        "Request over blob limit: %s", datum.completion.request_id
                    )
                    blob_names.clear()
                    over_blob_limit_count += 1
            logger.info(
                "Cleared blobs for %d requests over blob limit.", over_blob_limit_count
            )

        if cfg.order is not None:
            data = cls._order_dataset(data, cfg.order)

        service_account_file = cfg.service_account_file
        if service_account_file and not service_account_file.is_file():
            logger.warning(
                "Couldn't find service account credentials at: %s. Ignore warning if running locally.",
                service_account_file,
            )
            service_account_file = None
        blobs = HindsightCompletionDataset.create_blobs_from_gcs(
            get_tenant(cfg.tenant_name),
            service_account_file=service_account_file,
        )

        dataset = HindsightCompletionDataset(data=data, blobs=blobs)

        return HindsightCompletionTask(dataset, cfg)

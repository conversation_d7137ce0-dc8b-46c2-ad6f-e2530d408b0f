"""Unit tests for metrics."""

from dataclasses import astuple
from random import Random

import pytest

from research.core.model_input import ModelInput
from research.eval.harness.metrics import (
    CodeCompleteMetrics,
    DatumTag,
    ForwardMetrics,
    MetricIdentifier,
    MetricsAggregator,
    PrecisionRecallMetrics,
    compute_code_complete_metrics,
    compute_common_code_complete_tags,
    compute_edit_similarity,
    compute_exact_match,
    compute_lcs_precision_recall,
    compute_prefix_precision_recall,
    metrics_to_str_dict,
    nest_metrics,
    str_dict_to_metrics,
    unnest_metrics,
)


@pytest.mark.parametrize(
    "target, predictions, expected",
    [
        ("abc", ["abc"], 1.0),
        ("abc", ["a bc"], 0.0),
        ("a\nb\nc", ["a\nb\nc"], 1.0),
        ("a\nb\nc", ["a\nb\nc\nd"], 0.0),
        ("", [""], 1.0),
        ("", ["abc"], 0.0),
        ("a\nb\nc", ["a\nb\nc\nd", "a\nb\nc"], 1.0),
        ("a\nb\nc", ["a\nb\nc\nd", "abc"], 0.0),
        ("", ["", ""], 1.0),
        ("abc", ["abc", "abc"], 1.0),
        ("a\nb\nc", ["a\nb\nc", "a\nb\nc"], 1.0),
    ],
)
def test_compute_exact_match(target, predictions, expected):
    assert compute_exact_match(target, predictions) == expected


@pytest.mark.parametrize(
    "target, predictions, expected",
    [
        (" abc ", ["\tabc  "], 1.0),
        (" abc ", [" a bc "], 0.0),
        (" a \n\n b \n c ", ["\ta \nb\n\n c  "], 1.0),
        (" a \n\n b \n c ", ["\ta \nb\n\n  c d "], 0.0),
        ("  ", ["\t "], 1.0),
        ("  ", [" abc"], 0.0),
        (" a \n\n b \n c ", ["abc", "\ta \nb\n\n c  "], 1.0),
        (" a \n\n b \n c ", ["abc", "\ta \nb\n\n  c d "], 0.0),
        ("  ", ["\t ", ""], 1.0),
        (" abc ", ["\tabc  ", "  abc  "], 1.0),
        (" a \n\n b \n c ", ["\ta \nb\n\n c  ", "a\nb\nc"], 1.0),
    ],
)
def test_compute_exact_match_strip(target, predictions, expected):
    assert compute_exact_match(target, predictions) == expected


@pytest.mark.parametrize(
    "target, predictions, expected",
    [
        ("abc", ["abc"], 1.0),
        ("abc", ["a bc"], 0.8),
        ("a\nb\nc", ["a\nb\nc"], 1.0),
        ("a\nb\nc", ["a\nb\nc\nd"], 0.75),
        ("", [""], 1.0),
        ("", ["abc"], 0.25),
        ("a\nb\nc", ["a\nb\nc\nd", "a\nb\nc"], 1.0),
        ("a\nb\nc", ["a\nb\nc\nd", "abc"], 0.75),
        ("", ["", ""], 1.0),
        ("abc", ["abc", "abc"], 1.0),
        ("a\nb\nc", ["a\nb\nc", "a\nb\nc"], 1.0),
    ],
)
def test_edit_similarity(target, predictions, expected):
    assert compute_edit_similarity(target, predictions) == pytest.approx(expected)


@pytest.mark.parametrize(
    "target, predictions, expected",
    [
        (" abc ", ["\tabc  "], 1.0),
        (" abc ", [" a bc "], 0.8),
        (" a \n\n b \n c ", ["\ta \nb\n\n c  "], 1.0),
        (" a \n\n b \n c ", ["\ta \nb\n\n  c d "], 0.75),
        ("  ", ["\t "], 1.0),
        ("  ", [" abc"], 0.25),
        (" a \n\n b \n c ", ["abc", "\ta \nb\n\n c  "], 1.0),
        (" a \n\n b \n c ", ["abc", "\ta \nb\n\n  c d "], 0.75),
        ("  ", ["\t ", ""], 1.0),
        (" abc ", ["\tabc  ", "  abc  "], 1.0),
        (" a \n\n b \n c ", ["\ta \nb\n\n c  ", "a\nb\nc"], 1.0),
    ],
)
def test_edit_similarity_strip(target, predictions, expected):
    assert compute_edit_similarity(target, predictions) == pytest.approx(expected)


@pytest.mark.parametrize(
    "target, predictions, prefix_f1, prefix_precision, prefix_recall",
    [
        ("abc", ["abc"], 1.0, 1.0, 1.0),
        ("abc", ["a bc"], 4 / 9, 2 / 5, 2 / 4),
        ("a\nb\nc", ["a\nb\nc"], 1.0, 1.0, 1.0),
        ("a\nb\nc", ["a\nb\nc\nd"], 6 / 7, 3 / 4, 3 / 3),
        ("", [""], 1.0, 1.0, 1.0),
        ("", ["abc"], 0.4, 0.25, 1.0),
        ("a\nb\nc", ["a\nb\nc\nd", "a\nb\nc"], 1.0, 1.0, 1.0),
        ("a\nb\nc", ["a\nb\nc\nd", "abc"], 6 / 7, 3 / 4, 3 / 3),
        ("", ["", ""], 1.0, 1.0, 1.0),
        ("abc", ["abc", "abc"], 1.0, 1.0, 1.0),
        ("a\nb\nc", ["a\nb\nc", "a\nb\nc"], 1.0, 1.0, 1.0),
        (" abc ", ["\tabc  "], 2 / 13, 1 / 7, 1 / 6),
    ],
)
def test_prefix_precision_recall(
    target, predictions, prefix_f1, prefix_precision, prefix_recall
):
    expected = PrecisionRecallMetrics(
        f1=prefix_f1, precision=prefix_precision, recall=prefix_recall
    )
    actual = compute_prefix_precision_recall(target, predictions)
    assert astuple(actual) == tuple(pytest.approx(field) for field in astuple(expected))


@pytest.mark.parametrize(
    "target, predictions, lcs_f1, lcs_precision, lcs_recall",
    [
        ("abc", ["abc"], 1.0, 1.0, 1.0),
        ("abc", ["a bc"], 8 / 9, 4 / 5, 4 / 4),
        ("a\nb\nc", ["a\nb\nc"], 1.0, 1.0, 1.0),
        ("a\nb\nc", ["a\nb\nc\nd"], 6 / 7, 3 / 4, 3 / 3),
        ("", [""], 1.0, 1.0, 1.0),
        ("", ["abc"], 0.4, 0.25, 1.0),
        ("a\nb\nc", ["a\nb\nc\nd", "a\nb\nc"], 1.0, 1.0, 1.0),
        ("a\nb\nc", ["a\nb\nc\nd", "abc"], 6 / 7, 3 / 4, 3 / 3),
        ("", ["", ""], 1.0, 1.0, 1.0),
        ("abc", ["abc", "abc"], 1.0, 1.0, 1.0),
        ("a\nb\nc", ["a\nb\nc", "a\nb\nc"], 1.0, 1.0, 1.0),
        (" abc ", ["\tabc  "], 10 / 13, 5 / 7, 5 / 6),
    ],
)
def test_lcs_precision_recall(target, predictions, lcs_f1, lcs_precision, lcs_recall):
    expected = PrecisionRecallMetrics(
        f1=lcs_f1, precision=lcs_precision, recall=lcs_recall
    )
    actual = compute_lcs_precision_recall(target, predictions)
    assert astuple(actual) == tuple(pytest.approx(field) for field in astuple(expected))


@pytest.mark.parametrize(
    "target, predictions, lcs_f1, lcs_precision, lcs_recall",
    [
        ("cat in the hat", ["ox in the box"], 18 / 29, 9 / 14, 9 / 15),
        ("A X X X X B", ["C X X X X D"], 20 / 24, 10 / 12, 10 / 12),
        (
            "a\nb\nc\n" * 10 + "y01010101\n" * 10,
            ["x01010101\n" * 10 + "a\nb\n" * 10],
            182 / 302,
            91 / 141,
            91 / 161,
        ),
        ("ABXYZCD", ["12XYZ34"], 8 / 16, 4 / 8, 4 / 8),
        ("Hovering", ["My government"], 12 / 23, 6 / 14, 6 / 9),
    ],
)
def test_lcs_precision_recall_edge_cases(
    target, predictions, lcs_f1, lcs_precision, lcs_recall
):
    """Tests several edge cases to differentiate the LCS from other algorithm
    (see https://neil.fraser.name/writing/diff/):
    1. prefix/suffix special cases
    2. repeated text (vs. Heckel's algorithm)
    3. line-based diff (only triggers above 100 chars for fast diff match patch)
    4. post-processing steps (efficiency, semantic)
    """
    expected = PrecisionRecallMetrics(
        f1=lcs_f1, precision=lcs_precision, recall=lcs_recall
    )
    actual = compute_lcs_precision_recall(target, predictions)
    assert astuple(actual) == tuple(pytest.approx(field) for field in astuple(expected))


def lcs(a: str, b: str) -> int:
    """LCS via BFS."""
    q = []
    q.append((0, 0))
    seen = set()
    seen.add((0, 0))

    steps = 0
    while q:
        next_q = []
        for i, j in q:
            while i < len(a) and j < len(b) and a[i] == b[j]:
                i += 1
                j += 1
            if i == len(a) and j == len(b):
                assert (len(a) + len(b) - steps) % 2 == 0
                return (len(a) + len(b) - steps) // 2
            if i < len(a):
                if (i + 1, j) not in seen:
                    next_q.append((i + 1, j))
                    seen.add((i + 1, j))
            if j < len(b):
                if (i, j + 1) not in seen:
                    next_q.append((i, j + 1))
                    seen.add((i, j + 1))

        q = next_q

        steps += 1

    raise AssertionError("Unreachable code")


def test_lcs_precision_recall_random():
    rng = Random(42)
    char_set = "AB\n"
    for _ in range(100):
        char_set = "AB\n"
        target = "".join(rng.choice(char_set) for _ in range(rng.randint(0, 200)))
        prediction = "".join(rng.choice(char_set) for _ in range(rng.randint(0, 200)))
        matches = lcs(target, prediction)
        precision = (matches + 1) / (len(prediction) + 1)
        recall = (matches + 1) / (len(target) + 1)
        f1 = 2 * (matches + 1) / (len(target) + len(prediction) + 2)

        expected = PrecisionRecallMetrics(f1=f1, precision=precision, recall=recall)
        actual = compute_lcs_precision_recall(target, [prediction])
        assert astuple(actual) == tuple(
            pytest.approx(field) for field in astuple(expected)
        )


@pytest.mark.parametrize(
    "target, prediction, forward_metrics, expected",
    [
        (
            "abc",
            "abc",
            ForwardMetrics(log_likelihood=-0.5, token_accuracy=0.9),
            CodeCompleteMetrics(
                exact_match=1.0,
                exact_match_1_line=1.0,
                prefix_f1=1.0,
                prefix_precision=1.0,
                prefix_recall=1.0,
                lcs_f1=1.0,
                lcs_precision=1.0,
                lcs_recall=1.0,
                log_likelihood=-0.5,
                token_accuracy=0.9,
            ),
        ),
        (
            "abc",
            "abcd",
            ForwardMetrics(log_likelihood=-0.1, token_accuracy=0.0),
            CodeCompleteMetrics(
                exact_match=0.0,
                exact_match_1_line=0.0,
                prefix_f1=8 / 9,
                prefix_precision=4 / 5,
                prefix_recall=4 / 4,
                lcs_f1=8 / 9,
                lcs_precision=4 / 5,
                lcs_recall=4 / 4,
                log_likelihood=-0.1,
                token_accuracy=0.0,
            ),
        ),
        (
            "ab\n c",
            "ab\nc",
            None,
            CodeCompleteMetrics(
                exact_match=0.0,
                exact_match_1_line=1.0,
                prefix_f1=8 / 11,
                prefix_precision=4 / 5,
                prefix_recall=4 / 6,
                lcs_f1=10 / 11,
                lcs_precision=5 / 5,
                lcs_recall=5 / 6,
                log_likelihood=None,
                token_accuracy=None,
            ),
        ),
    ],
)
def test_code_complete_metrics(target, prediction, forward_metrics, expected):
    assert (
        compute_code_complete_metrics(prediction, target, forward_metrics) == expected
    )


def test_to_float_dict():
    metrics = CodeCompleteMetrics(
        exact_match=0.0,
        exact_match_1_line=1.0,
        prefix_f1=8 / 12,
        prefix_precision=4 / 5,
        prefix_recall=4 / 7,
        lcs_f1=10 / 12,
        lcs_precision=5 / 5,
        lcs_recall=5 / 7,
        log_likelihood=None,
        token_accuracy=None,
    )
    assert metrics.to_float_dict() == {
        "exact_match": 0.0,
        "exact_match_1_line": 1.0,
        "prefix_f1": 8 / 12,
        "prefix_precision": 4 / 5,
        "prefix_recall": 4 / 7,
        "lcs_f1": 10 / 12,
        "lcs_precision": 5 / 5,
        "lcs_recall": 5 / 7,
    }


@pytest.mark.parametrize(
    "model_input, expected",
    [
        (
            ModelInput(
                prefix='"""Module for foo.\n\n',
                suffix='"""',
                path="foo.py",
                target="Does foo things.",
            ),
            [
                DatumTag(category="all", value="all"),
                DatumTag(category="lang", value="python"),
                DatumTag(category="test", value="non_test"),
            ],
        ),
        (
            ModelInput(
                prefix="// this is cpp\n",
                suffix="}",
                path="foo_test.cpp",
                target="int main() {",
            ),
            [
                DatumTag(category="all", value="all"),
                DatumTag(category="lang", value="cpp"),
                DatumTag(category="test", value="test"),
            ],
        ),
    ],
)
def test_compute_common_code_complete_tags(model_input, expected):
    assert compute_common_code_complete_tags(model_input) == expected


def test_metric_identifier():
    metric_id = MetricIdentifier("a", "b", "c_d", "avg")
    assert metric_id == MetricIdentifier.from_string("a.b.c_d.avg")
    assert str(metric_id) == "a.b.c_d.avg"
    assert metric_id == MetricIdentifier.safe_init("a", "b", "c.d", "avg")
    assert metric_id.check_init("a", "b", "c_d", "avg")
    assert not metric_id.check_init("a", "b", "c.d", "avg")


def test_metrics_dict_conversions():
    metrics_dict = {
        MetricIdentifier("a", "b", "c", "avg"): 0.1,
        MetricIdentifier("a", "b", "c", "count"): 10,
        MetricIdentifier("a", "b", "d", "avg"): 0.2,
        MetricIdentifier("a", "b", "d", "count"): 20,
        MetricIdentifier("a", "c", "e", "avg"): 0.3,
        MetricIdentifier("a", "c", "e", "count"): 30,
        MetricIdentifier("x", "y", "z", "avg"): 0.4,
        MetricIdentifier("x", "y", "z", "count"): 40,
    }
    str_dict = {
        "a.b.c.avg": 0.1,
        "a.b.c.count": 10,
        "a.b.d.avg": 0.2,
        "a.b.d.count": 20,
        "a.c.e.avg": 0.3,
        "a.c.e.count": 30,
        "x.y.z.avg": 0.4,
        "x.y.z.count": 40,
    }
    nest_dict = {
        "a": {
            "b": {
                "c": {"avg": 0.1, "count": 10},
                "d": {"avg": 0.2, "count": 20},
            },
            "c": {"e": {"avg": 0.3, "count": 30}},
        },
        "x": {"y": {"z": {"avg": 0.4, "count": 40}}},
    }
    assert str_dict == metrics_to_str_dict(metrics_dict)
    assert metrics_dict == str_dict_to_metrics(str_dict)
    assert nest_dict == nest_metrics(metrics_dict)
    assert metrics_dict == unnest_metrics(nest_dict)


def test_aggregation():
    aggregator = MetricsAggregator()
    aggregator.add({"em": 1.0, "es": 0.5}, [DatumTag("lang", "java"), DatumTag("", "")])
    aggregator.add({"em": 0.0, "es": 0.9}, [DatumTag("lang", "java"), DatumTag("", "")])
    aggregator.add({"em": 1.0, "es": 0.1}, [DatumTag("lang", "cpp"), DatumTag("", "")])
    aggregator.add({"em": 1.0, "es": 0.1}, [DatumTag("lang", "cpp"), DatumTag("", "")])
    actual = metrics_to_str_dict(aggregator.compute())
    expected = {
        "..em.avg": 0.75,
        "..em.stderr": 0.21650635094610965,
        "..em.count": 4,
        "..es.avg": 0.4,
        "..es.stderr": 0.16583123951777,
        "..es.count": 4,
        "lang.cpp.em.avg": 1.0,
        "lang.cpp.em.stderr": 0.0,
        "lang.cpp.em.count": 2,
        "lang.cpp.es.avg": 0.1,
        "lang.cpp.es.stderr": 0.0,
        "lang.cpp.es.count": 2,
        "lang.java.em.avg": 0.5,
        "lang.java.em.stderr": 0.35355339059327373,
        "lang.java.em.count": 2,
        "lang.java.es.avg": 0.7,
        "lang.java.es.stderr": 0.1414213562373095,
        "lang.java.es.count": 2,
    }
    assert actual == {k: pytest.approx(v) for k, v in expected.items()}

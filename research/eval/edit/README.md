# Evaluation for edit models

All supported models(systems) are located in `./experimental_edit_systems.py` and decorated with `@code_edit_system()`.

Data:
Current data used for evaluations is located [here](https://github.com/augmentcode/augment/tree/main/experimental/dxy/edits/eval/data).
See `experimental/yuri/notebooks/explore_collected.ipynb` for brief data exploration.

Prompts used for evaluations are located in `research/eval/edit/prompts.py`.

## Usage

It's better to run evaluation on H100, because 30B+ models will run much faster than on multiple A40.

- To get list of all available models run: ```python research/eval/edit/eval.py --list_systems```
- To evaluate a specific model run: ```python research/eval/edit/eval.py -s $system -i $data_path -p $prompt_name -o ${output_file}```
- To evaliate all available models run: ```bash evaluate_all.sh $data_path $prompt_name $output_directory```

Examples:

- ```python research/eval/edit/eval.py -i research/eval/edit/data -s RemoteFlaskCodeEditSystem -o ~/cache/CodeEditEval/DeployedModel```
- ```python research/eval/edit/eval.py -i research/eval/edit/data -s DeepSeekLLMChat67BCodeEditSystemHF -p prompt_v1 -o ~/cache/CodeEditEval/DS_LLM_Chat67B```
- ```CUDA_VISIBLE_DEVICES=0 python research/eval/edit/eval.py -i research/eval/edit/data -s CodeLLamaInstruct7BCodeEditSystemHF -p prompt_v1 -o ~/cache/CodeEditEval/CodeLLaMA_Instruct7B```
- ```CUDA_VISIBLE_DEVICES=0 python research/eval/edit/eval.py -i research/eval/edit/data -s CodeLLamaInstruct13BCodeEditSystemHF -p prompt_v1 -o ~/cache/CodeEditEval/CodeLLaMA_Instruct13B```
- ```CUDA_VISIBLE_DEVICES=0 python research/eval/edit/eval.py -i research/eval/edit/data -s WizardCoderPython13BCodeEditSystemHF -p prompt_v1 -o ~/cache/CodeEditEval/WizardCoder_Python13B```
- ```bash evaluate_all.sh research/eval/edit/data prompt_v1 result_folder```

## Data Format under `data`

The `data` folder is a concise collection of some representative edit data for evaluation only.
In the folder, each pair of `xxxx.py` and `xxxx.txt` is a subset of edit data collection.
The `xxxx.txt` contains the entire source file, `xxxx.py` defines the edit data in a list of dictionary.
In the `xxxx.py` file, each item in the list is a dictionary contains the following 3 keys:

- `lrange`: the line range of the selected code in `xxxx.txt`, where the index starts from 0.
- `instruction`: a list of string indicating alternative instructions.
- `response`: a string indicates what we should use to replace the selected code.

`test.ipynb` is a demo notebook to show how to load all the edit data from this folder, visualize, and test an edit end point.

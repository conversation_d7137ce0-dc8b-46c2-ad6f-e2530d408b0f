<<<<<<<
@code_edit_system()
class CodeLLamaInstruct13BCodeEditSystemHF(CodeEditSystemHF):
    CKPT_PATH = Path("/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-13b-Instruct-hf")
    MODEL_NAME = "codellama_instruct_hf"
    SKIP_TOKENIZER_LOADING = True


@code_edit_system()
class CodeLLamaInstruct34BCodeEditSystemHF(CodeEditSystemHF):
    CKPT_PATH = Path("/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-34b-Instruct-hf")
    MODEL_NAME = "codellama_instruct_hf"
    SKIP_TOKENIZER_LOADING = True


@code_edit_system()
class WizardCoder7BCodeEditSystemHF(CodeEditSystemHF):
    CKPT_PATH = Path("/mnt/efs/augment/checkpoints/llama/hf/WizardCoder-Python-7B-V1.0")
    MODEL_NAME = "wizardcoder_python_hf"
=======
class CodeLLamaInstruct13BCodeEditSystemHF(CodeEditSystemHF):
    CKPT_PATH = Path("/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-13b-Instruct-hf")
    MODEL_NAME = "codellama_instruct_hf"
    SKIP_TOKENIZER_LOADING = True


class CodeLLamaInstruct34BCodeEditSystemHF(CodeEditSystemHF):
    CKPT_PATH = Path("/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-34b-Instruct-hf")
    MODEL_NAME = "codellama_instruct_hf"
    SKIP_TOKENIZER_LOADING = True


class WizardCoder7BCodeEditSystemHF(CodeEditSystemHF):
    CKPT_PATH = Path("/mnt/efs/augment/checkpoints/llama/hf/WizardCoder-Python-7B-V1.0")
    MODEL_NAME = "wizardcoder_python_hf"
>>>>>>>

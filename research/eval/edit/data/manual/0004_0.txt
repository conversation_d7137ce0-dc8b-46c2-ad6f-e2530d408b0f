    # Create system(s)
    systems = [get_system(name) for name in args.system_name]
    for idx, system in enumerate(systems):
<<<<<<<
        with (real_output_dir / f"system-{idx}-of-{len(systems)}.repr").open(
            "w"
        ) as xfile:
            xfile.write(str(system) + "\n")
        with (real_output_dir / f"system-{idx}-of-{len(systems)}.json").open(
            "w"
        ) as xfile:
            xfile.write(system.ui_to_json())
=======
        with (real_output_dir / "system-{}-of-{}.repr".format(idx, len(systems))).open(
            "w"
        ) as xfile:
            xfile.write(str(system) + "\n")
        with (real_output_dir / "system-{}-of-{}.json".format(idx, len(systems))).open(
            "w"
        ) as xfile:
            xfile.write(system.ui_to_json())
>>>>>>>
    for system in systems:
        system.load()
        system.add_docs(repo_for_system)
    logger.info(f"Finish loading the system, output_dir={real_output_dir}")
    logger.info(f"Add the repo with {len(repo_for_system)} documents.")

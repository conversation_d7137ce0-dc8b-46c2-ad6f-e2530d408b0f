# **************:entria/apiWithLog.git
# <NAME_EMAIL>:${1}.git $2
# cd $2
# git reset --hard [ENTER HERE THE COMMIT HASH YOU WANT]

<<<<<<<
#!/bin/bash

# List of Git repositories
# repos=("https://github.com/example/repo1.git" "https://github.com/example/repo2.git" "https://github.com/example/repo3.git")

if [ "$#" -ne 3 ]; then
    echo "Usage: $0 <org> <proj> <target_sha>"
    exit 1
fi

# SHA to checkout
target_sha=$3

# Directory to clone repositories into
clone_dir="repos/$2"

# Create the directory if it doesn't exist
mkdir -p "$clone_dir"

# **************:entria/apiWithLog.git
# Extract repository name from the URL
repo_url="**************:$1/$2.git"

# Clone the repository
git clone "$repo_url" "$clone_dir"

# Change to the repository directory
cd "$clone_dir" || exit

# Checkout the specified commit SHA
git reset --hard "$target_sha"

# Return to the original directory
cd - || exit

echo "Cloning and checkout completed."
=======
import sys
import os
import subprocess

# Check if the correct number of arguments is provided
if len(sys.argv) != 4:
    print(f"Usage: {sys.argv[0]} <org> <proj> <target_sha>")
    sys.exit(1)

# SHA to checkout
target_sha = sys.argv[3]

# Directory to clone repositories into
clone_dir = f"repos/{sys.argv[2]}"

# Create the directory if it doesn't exist
os.makedirs(clone_dir, exist_ok=True)

# Construct the repository URL
repo_url = f"**************:{sys.argv[1]}/{sys.argv[2]}.git"

# Clone the repository
subprocess.run(["git", "clone", repo_url, clone_dir], check=True)

# Change to the repository directory
os.chdir(clone_dir)

# Checkout the specified commit SHA
subprocess.run(["git", "reset", "--hard", target_sha], check=True)

# Return to the original directory
os.chdir("..")

print("Cloning and checkout completed.")>>>>>>>

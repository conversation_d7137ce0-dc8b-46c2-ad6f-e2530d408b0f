#!/bin/bash

# Set your URL and data fields
<<<<<<<
URL="http://example.com/api"
PREFIX="YourPrefix"
SUFFIX="YourSuffix"
SELECTED_CODE="YourCode"

# Sending POST request with curl
curl -X POST $URL \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "prefix=$PREFIX&suffix=$SUFFIX&selected_code=$SELECTED_CODE"
=======
import requests

# Set your URL and data fields
url = "http://example.com/api"
data = {
    "prefix": "YourPrefix",
    "suffix": "YourSuffix",
    "selected_code": "YourCode"
}

# Sending POST request
response = requests.post(url, data=data)
>>>>>>>

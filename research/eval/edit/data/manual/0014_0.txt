"""Show the contents of the file store."""

import argparse

from research.model_server.file_store import FileStore


<<<<<<<
def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--storage_path",
        type=str,
        required=True,
        help="path to the file store",
    )
    args = parser.parse_args()
    storage_path = args.storage_path
    store = FileStore(Path(storage_path))
    docs: list[Document] = store.get_files()
    for doc in docs:
        print(doc.id, doc.path)
        print(doc.text)
=======
def print_files(docs: list[Document]):
    for doc in docs:
        print(doc.id, doc.path)
        print(doc.text)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--storage_path",
        type=str,
        required=True,
        help="path to the file store",
    )
    args = parser.parse_args()
    storage_path = args.storage_path
    store = FileStore(Path(storage_path))
    docs: list[Document] = store.get_files()
    print_files(docs)
>>>>>>>


if __name__ == "__main__":
    main()

<<<<<<<
PATH = '~/datasets/edit/commitpackft/data.jsonl'

data = []

with open(PATH) as f:
    for line in f:
        datum = json.loads(line[:-1])
        data.append(datum)
=======
import json
import pathlib

path_to_data = pathlib.Path("~/datasets/edit/commitpackft/data.jsonl").expanduser()
data = []

with path_to_data.open("r") as f:
    for line in f:
        datum = json.loads(line[:-1])
        data.append(datum)
>>>>>>>

import argparse

parser = argparse.ArgumentParser(
    formatter_class=argparse.ArgumentDefaultsHelpFormatter
)

parser.add_argument(
    "--input_data_path",
    "-i",
    type=str,
    help="Directory with input data.",
)
<<<<<<<
parser.add_argument(
    "--system",
    type=str,
    help="The system factory name.",
)
parser.add_argument(
    "--list_systems", action="store_true", help="List all available systems."
)
=======
parser.add_argument(
    "--system",
    "-s",
    type=str,
    help="The system factory name.",
)
parser.add_argument(
    "--list_systems", "-l", action="store_true", help="List all available systems."
)
>>>>>>>
parser.add_argument(
    "--output_path", "-o", type=str, help="JSON file to store result."
)
parser.add_argument(
    "--prompt",
    "-p",
    type=str,
    help="Editing prompt from research.eval.edit.prompts.REGISTRY.",
)

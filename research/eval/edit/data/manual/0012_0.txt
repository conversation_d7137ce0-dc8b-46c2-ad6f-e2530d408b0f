data = load_data(Path(args.input_data_path))
gen_options = GenerationOptions(
    temperature=args.temperature,
    top_k=args.top_k,
    top_p=args.top_p,
    max_generated_tokens=args.max_generated_tokens,
)
try:
    system = SYSTEMS_REGISTRY.get(args.system)()
except KeyError:
<<<<<<<
    print("Can't find requested system")
    raise

try:
    prompt = PROMPTS_REGISTRY.get(args.prompt)()
except KeyError:
    print("Can't find requested prompt")
    raise
=======
    logging.error("Can't find requested system")
    raise

try:
    prompt = PROMPTS_REGISTRY.get(args.prompt)()
except KeyError:
    logging.error("Can't find requested prompt")
    raise
>>>>>>>

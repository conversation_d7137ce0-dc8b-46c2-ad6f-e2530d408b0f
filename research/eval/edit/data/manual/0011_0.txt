<<<<<<<
model_input = ModelInput(prefix="Hello World")
inputs_gen = model.prompt_formatter.prepare_prompt(model_input)[0]

result = model.raw_generate(inputs_gen)

parsed_result = parse_edit_response(result)
=======
modelInput = ModelInput(prefix="Hello World")
inputsGen = model.prompt_formatter.prepare_prompt(modelInput)[0]

result = model.raw_generate(inputsGen)

parsedResult = parse_edit_response(result)
>>>>>>>

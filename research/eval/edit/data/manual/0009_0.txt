try:
<<<<<<<
    logging.warning("Service A is running.")
    if isinstance(self.model.prompt_formatter, CodeLlamaChatFormatter):
        bos_token_id = None
        eos_token_id = self.model.tokenizer.eos_id  # type: ignore
    else:
        bos_token_id = self.model.tokenizer._raw_tokenizer.bos_token_id  # type: ignore
        eos_token_id = self.model.tokenizer._raw_tokenizer.eos_token_id  # type: ignore
except AttributeError:
    logging.warning("Accessing special tokens failed.")
=======
    logging.error("Service A is running.")
    if isinstance(self.model.prompt_formatter, CodeLlamaChatFormatter):
        bos_token_id = None
        eos_token_id = self.model.tokenizer.eos_id  # type: ignore
    else:
        bos_token_id = self.model.tokenizer._raw_tokenizer.bos_token_id  # type: ignore
        eos_token_id = self.model.tokenizer._raw_tokenizer.eos_token_id  # type: ignore
except AttributeError:
    logging.error("Accessing special tokens failed.")
>>>>>>>

"""Prepare Data for Edit."""
import logging
import typing

# import tenacity
import openai
from openai import openai_object

# from openai import error as openai_error

<<<<<<<
OpenAIChatModels = typing.Literal["gpt-4", "gpt-3.5-turbo"]

logger = logging.getLogger(__name__)


def generate_response_via_chat(
    messages: list[str],
    system_prompt: str,
    temperature: float = 0.2,
    max_tokens: int = 256,
    model: OpenAIChatModels = "gpt-4",
) -> str:
    request_messages = [{"role": "system", "content": system_prompt}]
    for idx, message in enumerate(messages):
        role = "user" if idx == 0 else "assistant"
        request_messages.append({"role": role, "content": message})
=======
OpenAIChatModels = typing.Literal['gpt-4', 'gpt-3.5-turbo']

logger = logging.getLogger(__name__)


def generate_response_via_chat(
    messages: list[str],
    system_prompt: str,
    temperature: float = 0.2,
    max_tokens: int = 256,
    model: OpenAIChatModels = 'gpt-4',
) -> str:
    request_messages = [{'role': 'system', 'content': system_prompt}]
    for idx, message in enumerate(messages):
        role = 'user' if idx == 0 else 'assistant'
        request_messages.append({'role': role, 'content': message})
>>>>>>>
    response = openai.ChatCompletion.create(
        model=model,
        messages=request_messages,
        temperature=temperature,
        max_tokens=max_tokens,
        top_p=1,
        frequency_penalty=0,
        presence_penalty=0,
    )
    response = typing.cast(openai_object.OpenAIObject, response)
    return response.choices[0].message.content

[
  {
    "type": "https",
    "instruction": "what is my ip",
    "command": "hostname -i"
  },
  {
    "type": "configuring",
    "instruction": "list all configuration files",
    "command": "ls -l /etc"
  },
  {
    "type": "compression",
    "instruction": "compress the file.txt",
    "command": "gzip file.txt"
  },
  {
<<<<<<<
    type terminal
    instruction list the environment variables
    command env
  },
=======
    "type": "terminal",
    "instruction": "list the environment variables",
    "command": "env"
  },
>>>>>>>
  {
    "type": "versioning",
    "instruction": "get the current branch",
    "command": "git branch"
  }
]

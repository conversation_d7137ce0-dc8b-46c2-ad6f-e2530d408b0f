<<<<<<<
for sample in samples:
    instruction = sample["message"]
    lines_before = sample["old_contents"].splitlines(keepends=True)
=======
for i, sample in enumerate(samples):
    instruction = sample["message"]
    lines_before = sample["old_contents"].splitlines(keepends=True)
>>>>>>>
    lines_after = sample["new_contents"].splitlines(keepends=True)
    result = differ.compare(lines_before, lines_after)
    print("\n=" * 80)
    print(f"[{i}] instruction: {instruction}")
    for line in result:
        if line.startswith("-"):
            print(Fore.RED + line, end="")
        elif line.startswith("+"):
            print(Fore.GREEN + line, end="")
        else:
            print(line, end="")

def main(args):
    """The real main function."""
    args = parse_cmd()
    output_dir = pathlib.Path(args.data_dir)
    if not output_dir.exists():
        raise ValueError(f"{output_dir} does not exist.")

<<<<<<<
    real_output_dir: pathlib.Path = output_dir / (
        args.target_doc_name + "-" + "-".join(args.system_name)
    )
    real_output_dir.mkdir(parents=True, exist_ok=True)

    logger = prepare_to_start(real_output_dir)
    logger.info(f"Args: {args}")
    logger.info(f"Save everything into {real_output_dir}")
=======
    xyz: pathlib.Path = output_dir / (
        args.target_doc_name + "-" + "-".join(args.system_name)
    )
    xyz.mkdir(parents=True, exist_ok=True)

    logger = prepare_to_start(xyz)
    logger.info(f"Args: {args}")
    logger.info(f"Save everything into {xyz}")
>>>>>>>

    load_data = torch.load(output_dir / args.source_doc_name)
    doc_dicts, image_name = load_data["documents"], load_data["image_name"]
    repo_for_system: list[Document] = load_data["repo_for_system"]

    augmented_docs: list[AugmentedParsedFile] = []
    for doc_dict in doc_dicts:
        augmented_docs.append(AugmentedParsedFile.from_dict(doc_dict))
    logger.info(
        f"Load {len(doc_dicts)} documents with"
        f" {patch_generate_lib.get_number_of_nodes(augmented_docs)} nodes from {args.source_doc_name}"
    )

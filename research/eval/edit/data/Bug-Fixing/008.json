{"repo_url": "https://github.com/sot/ska_file/blob/339fad1cc989900f3411d775c198a6e7f08fdc4e", "file_name": "Ska/File.py", "instructions": ["Fix exception messages in this function", "Exception messages are incorrect, fix them"], "category": "Bug-Fixing", "language": "python", "inverse_instructions": ["Introduce logical error in exception messages", "Introduce bug into exception messages by swapping maxfiles with minfiles and nfiles with fileglob"]}
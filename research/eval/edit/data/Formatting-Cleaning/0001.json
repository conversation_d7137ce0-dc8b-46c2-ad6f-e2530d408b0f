{"repo_url": "https://github.com/diluga/inkscope/tree/4e784eef5c217212cb43dbc5644f7380c3260842", "file_name": "inkscopeCtrl/poolsCtrlSalt.py", "instructions": ["keep a single space after each , and a single space before and after +", "Randomly adjust the spaces after commas , and around plus signs + in the code", "Use 1 or 0 spaces after commas , and around plus signs + in the code to create an irregular and inconsistent spacing pattern"], "category": "Formatting-Cleaning", "language": "python", "inverse_instructions": ["Mess up the spaces after commas , and around plus signs + in the code"]}
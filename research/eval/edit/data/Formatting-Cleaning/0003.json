{"repo_url": "https://github.com/enpassant/jrestful/commit/0cd56cff5be3372d0dec0163bfd26a08a96c2c0c", "file_name": "modules/jrestful-core/src/main/java/jrestful/client/ClientState.java", "instructions": ["remove MediaType", "delete MediaType, just keep group", "delete MediaType objects but keep the groups"], "category": "Formatting-Cleaning", "language": "java", "inverse_instructions": ["wrap groups in MediaType"]}
{"repo_url": "https://github.com/hochschule-darmstadt/metaautoml/commit/cb427dbc712bc094b09ee48bfb6ca1cca5f6eb00", "file_name": "adapters/AutoSklearn/AutoMLs/AutoSklearnAdapter.py", "instructions": ["Correct it as dict format"], "category": "Formatting-Cleaning", "language": "python", "inverse_instructions": ["pollute its syntax by replacing key-value pair as key = value", "make syntax errors in definition of dict by replacing key-value pair as key = value"]}
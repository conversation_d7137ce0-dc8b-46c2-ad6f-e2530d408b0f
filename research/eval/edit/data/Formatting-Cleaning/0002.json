{"repo_url": "https://github.com/enpassant/jrestful/commit/0cd56cff5be3372d0dec0163bfd26a08a96c2c0c", "file_name": "modules/jrestful-core/src/main/java/jrestful/link/RelLink.java", "instructions": ["change in, out to type String", "make out, in be String", "make in, out be String and remove MediaType import"], "category": "Formatting-Cleaning", "language": "java", "inverse_instructions": ["change in, out to type MediaType", "change in, out to MediaType and add import"]}
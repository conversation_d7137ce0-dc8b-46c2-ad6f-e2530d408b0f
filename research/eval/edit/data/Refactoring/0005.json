{"repo_url": "https://github.com/laurica/VCS/commit/4df468f41bb53e422c60c01c1acc95d4c30284dd", "file_name": "src/OperationAccumulator.cc", "language": "cpp", "category": "Refactoring", "instructions": ["refactor writing out added files to writeOutAddedFiles", "split writing added files to separate function"], "inverse_instructions": ["inline writeOutAddedFiles", "merge writeOutAddedFiles into writeOutCommit"]}
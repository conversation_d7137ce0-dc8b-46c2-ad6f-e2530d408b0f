{"repo_url": "https://github.com/pwyf/aid-transparency-tracker/commit/05164c334cc750f3a6a709a6e8c3822ef30b856a", "file_name": "iatidq/test_queue.py", "instructions": ["move code starting at get_result_hierarchy to new check_data function", "refactor everything after if not xml_parsed block to check_data()"], "category": "Refactoring", "language": "python", "inverse_instructions": ["inline check_data and delete", "move check_data code into check_file"]}
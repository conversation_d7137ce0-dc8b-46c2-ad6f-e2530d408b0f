{"repo_url": "https://github.com/pwyf/aid-transparency-tracker/commit/5900007833b9a3de244733451a57c8be671f6698", "file_name": "iatidataquality/organisations.py", "instructions": ["refactor try-except to get_summary_data function", "move _organisation_indicators_summary block to separate get_summary_data func"], "category": "Refactoring", "language": "python", "inverse_instructions": ["inline get_summary_data"]}
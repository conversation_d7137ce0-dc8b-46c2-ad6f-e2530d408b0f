{"repo_url": "https://github.com/SrNightmare09/jazzman-music-player/commit/aebe85fc3025deb3e85798f9d1b173c5d4398d2a", "file_name": "src/main.js", "language": "javascript", "category": "Refactoring", "instructions": ["make all function names camel case", "make all function names camelCase", "change all function names to lookLikeThis"], "inverse_instructions": ["rename some functions to snake case", "rename some functions to snake_case", "make a couple of function names like_this"]}
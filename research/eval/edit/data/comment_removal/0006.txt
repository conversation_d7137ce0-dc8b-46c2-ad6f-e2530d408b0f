import logging
import os
import typing

from openai import Async<PERSON>penAI, OpenAI
from openai.types.chat import (
    ChatCompletionAssistantMessageParam,
    ChatCompletionMessageParam,
    ChatCompletionSystemMessageParam,
    ChatCompletionUserMessageParam,
)
from openai.types.chat.completion_create_params import ResponseFormat

client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))
async_client = AsyncOpenAI(api_key=os.environ.get("OPENAI_API_KEY"))

<<<<<<<
OpenAIChatModels = typing.Literal[
    "gpt-4-1106-preview",  # 128,000 tokens
    "gpt-4-vision-preview",
    "gpt-3.5-turbo-1106",  # 16,385 tokens
    "gpt-4-0613",  # 8192 tokens
    "gpt-4-0314",  # 8192 tokens
]
# ResponseFormat = typing.Literal["json_object"]
=======
OpenAIChatModels = typing.Literal[
    "gpt-4-1106-preview",
    "gpt-4-vision-preview",
    "gpt-3.5-turbo-1106",
    "gpt-4-0613",
    "gpt-4-0314",
]
>>>>>>>
logger = logging.getLogger(__name__)


def _prepare_chat_request_argument(
    messages: list[str],
    system_prompt: typing.Optional[str] = None,
    temperature: float = 0.2,
    max_tokens: int = 256,
    model: OpenAIChatModels = "gpt-3.5-turbo-1106",
    seed: typing.Optional[int] = None,
    num_completion: int = 1,
    use_json: bool = False,
):
    request_messages: list[ChatCompletionMessageParam] = []
    if system_prompt is not None:
        request_messages.append(
            ChatCompletionSystemMessageParam(content=system_prompt, role="system")
        )
    for idx, message in enumerate(messages):
        if idx == 0:
            request_messages.append(
                ChatCompletionUserMessageParam(content=message, role="user")
            )
        else:
            request_messages.append(
                ChatCompletionAssistantMessageParam(content=message, role="assistant")
            )
    if use_json:
        response_format = ResponseFormat(type="json_object")
    else:
        response_format = ResponseFormat(type="text")
    arguments = {
        "model": model,
        "messages": request_messages,
        "temperature": temperature,
        "max_tokens": max_tokens,
        "top_p": 1,
        "n": num_completion,
        "frequency_penalty": 0,
        "presence_penalty": 0,
        "seed": seed,
        "response_format": response_format,
    }
    return arguments


def generate_response_via_chat(
    messages: list[str],
    system_prompt: typing.Optional[str] = None,
    temperature: float = 0.2,
    max_tokens: int = 256,
    model: OpenAIChatModels = "gpt-3.5-turbo-1106",
    seed: typing.Optional[int] = None,
    num_completion: int = 1,
    use_json: bool = False,
) -> typing.Tuple[str, ...]:
    arguments = _prepare_chat_request_argument(
        messages,
        system_prompt,
        temperature,
        max_tokens,
        model,
        seed,
        num_completion,
        use_json,
    )
    response = client.chat.completions.create(**arguments)
    results: list[str] = []
    for choice in response.choices:
        content = choice.message.content
        assert isinstance(content, str)
        results.append(content)
    return tuple(results)
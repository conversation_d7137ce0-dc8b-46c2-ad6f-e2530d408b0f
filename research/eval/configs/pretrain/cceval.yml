system:
  name: basic_rag
  model:
    name: starcoderbase_1b_hf
    prompt:
      max_prefix_tokens: 1280
      max_suffix_tokens: 768
      max_prompt_tokens: 3816
      preamble: ""
      always_fim_style: True
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 50
  retriever:
    name: null
    chunker:
      name: line_level
      max_lines_per_chunk: 40
  experimental:
    retriever_top_k: 25
    trim_on_dedent: True
    trim_on_max_lines: null
    remove_suffix: False
    trim_trailing_newline_on_prefix: True

task:
  name: cceval

podspec: ampere-small.yaml

determined:
  name: cceval
  workspace: Dev
  project: pretrain-eval
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

# This file contains an example config for next edit location, and is not the
# recommended configuration.

system:
  name: next_edit_location
  group_by_path: True
  filter_input_ranges: True
  retriever:
    scorer:
      name: generic_neox
      checkpoint_path: /mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_doc_2000
    chunker:
      name: line_level
      max_lines_per_chunk: 30
    query_formatter:
      name: ethanol6_query
      max_tokens: 4096
      add_path: true
      add_suffix: true
      prefix_ratio: 0.9
    document_formatter:
      name: ethanol6_document
      max_tokens: 999
      add_path: true

task:
  name: next_edit_location
  limit_examples: 100
  dataset_path: /mnt/efs/augment/data/eval/next_edits/prs.v7.jsonl.zst


# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: A40.yaml

# Determined
# name, workspace, project control location and display in the determined UI.

determined:
  name: Next Edit Location
  workspace: Dev
  project: playground
  # relative to research/gpt-neox
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

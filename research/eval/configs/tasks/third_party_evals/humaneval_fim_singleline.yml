#
# This file contains an example of an evaluation config
# for humaneval-fim, singleline variant.
#

systems:
  - name: basic_rag
    model:
      name: starcoderbase_1b
      prompt:
        max_prefix_tokens: 960
        max_suffix_tokens: 960
        max_prompt_tokens: 1920
    generation_options:
      temperature: 0
      max_generated_tokens: 128
    retriever:
      name: null
      chunker: line_level
      max_chunk: 20
    experimental:
      remove_suffix: False

tasks:
 - name: humaneval_fim
   variant: singleline
   # limit: 10
   exec: True
   iterations: 1

# Podspec - set the default podspec for all checkpoints
# Use the following for larger models (>=2B)
podspec: A40.yaml
# podspec: 1xA100.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
determined:
  name: HumanEvalFim - StarCoder Base 1B, Temp 0
  workspace: Dev
  project: Eval
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

# This creates all configs for an oracle reranker experiment
# (including baselines).

system:
  name: basic_rag
  model:
    name: null
    prompt:
      max_prefix_tokens: null
      max_suffix_tokens: null
      max_prompt_tokens: null
      fill_to_context_window: True
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    name: null
    chunker: line_level
    max_chunk: 20
    max_query_lines: 6
  reranker:
    name: null
    top_k: null
    batchsize: 2
  experimental:
    remove_suffix: False
    retriever_top_k: 1000
    trim_on_dedent: null
    trim_on_max_lines: null

task:
  name: hydra
  dataset: repoeval_functions

podspec: A40.yaml

determined:
  name: null
  workspace: Dev
  project: Eval
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

interventions:
  experiment_name: oracle_reranker_experiment

  sweep_generative_models:
    keys:
      - system.model.name
      - system.model.prompt.max_prefix_tokens
      - system.model.prompt.max_suffix_tokens
      - system.model.prompt.max_prompt_tokens
    values:
      - [starcoderbase, 1024, 1024, 7912]

  sweep_datasets:
    keys:
      - task.dataset
      - system.experimental.trim_on_dedent
      - system.experimental.trim_on_max_lines
    values:
      - [repoeval_functions, True, null]
      - [repoeval_2-3lines, True, 6]

  sweep_retrievers:
    keys:
      - system.retriever.name
    values:
      - [null]
      - [contrieve_350m]

  sweep_rerankers:
    keys:
      - system.reranker.name
      - system.reranker.top_k
    values:
      - [null, 50]
      - [oracle_perplexity_reranker, 250]
      - [oracle_perplexity_reranker, 1000]

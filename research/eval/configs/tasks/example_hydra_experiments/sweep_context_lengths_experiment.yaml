# This creates all configs for sweeping context lengths
# (including baselines).

system:
  name: rag_with_reranker
  model:
    name: null
    prompt:
      max_prefix_tokens: null
      max_suffix_tokens: null
      max_prompt_tokens: null
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    name: null
    chunker: line_level
    max_chunk: 20
    max_query_lines: 6
  reranker:
    name: null
    top_k: null
    batchsize: 2
  experimental:
    remove_suffix: False
    retriever_top_k: 1000
    trim_on_dedent: True
    trim_on_max_lines: null

task:
  name: hydra
  dataset: repoeval_functions

podspec: 1xA100.yaml
determined:
  name: null
  workspace: Dev
  project: Eval
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

interventions:
  experiment_name: sweep_context_lengths_experiment

  sweep_generative_models:
    keys:
      - system.model.name
    values:
      - [starcoderbase]

  sweep_context_lengths:
    keys:
      - system.model.prompt.max_prefix_tokens
      - system.model.prompt.max_suffix_tokens
      - system.model.prompt.max_prompt_tokens
    values:
      # 2k local context, varying retrieval context
      - [1024, 1024, 7912]
      - [1024, 1024, 3816]

      # 4k local context, varying retrieval context
      - [1908, 1908, 7912]
      - [1908, 1908, 3816]

  sweep_datasets:
    keys:
      - task.dataset
      - system.experimental.trim_on_dedent
      - system.experimental.trim_on_max_lines
    values:
      - [repoeval_functions, True, null]
      - [repoeval_2-3lines, True, 6]

  sweep_retrievers:
    keys:
      - system.retriever.name
    values:
      - [contrieve_350m]

  sweep_rerankers:
    keys:
      - system.reranker.name
      - system.reranker.top_k
    values:
      - [null, 1000]
      - [oracle_perplexity_reranker, 1000]

# This creates all configs for a usage scorer experiment
# (including baselines).

systems:
  - name: basic_rag
    model:
      name: null
      prompt:
        max_prefix_tokens: null
        max_suffix_tokens: null
        max_prompt_tokens: null
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      name: null
      chunker: line_level
      max_chunk: 20
      max_query_lines: null
    reranker:
      name: null
      top_k: null
      batchsize: 2
    experimental:
      remove_suffix: False
      retriever_top_k: 1000
      trim_on_dedent: null
      trim_on_max_lines: null
tasks:
  - name: hydra
    dataset: null

podspec: 1xA100.yaml
determined:
  name: null
  workspace: Dev
  project: Eval
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

interventions:
  experiment_name: usage_scorer_experiments

  # Just test on StarCoderBase w/ 8k context for now
  sweep_generative_models:
    keys:
      - system.model.name
      - system.model.prompt.max_prefix_tokens
      - system.model.prompt.max_suffix_tokens
      - system.model.prompt.max_prompt_tokens
    values:
      - [starcoderbase, 1024, 1024, 7912]

  # Just test on multiline dataset for now
  sweep_datasets:
    keys:
      - task.dataset
      - system.experimental.trim_on_dedent
      - system.experimental.trim_on_max_lines
    values:
      - [repoeval_2-3lines, True, 6]

  # Sweep over different retrievers
  sweep_retrievers:
    keys:
      - system.retriever.name
      - system.retriever.max_query_lines
    values:
      - [null, 6]
      - [contrieve_350m, 6]
      - [definition_lookup, 6]
      - [contrieve_350m_with_definition_lookup, 6]

  # Try adding reranking
  sweep_rerankers:
    keys:
      - system.reranker.name
      - system.reranker.top_k
    values:
      - [null, 1000]
      - [oracle_perplexity_reranker, 1000]

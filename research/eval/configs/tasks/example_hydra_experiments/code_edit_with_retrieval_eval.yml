system:
  name: rag_code_edit
  model:
    name: deepseek_coder_instruct_remote
    url: http://127.0.0.1:8086
    override_prompt_formatter:
      name: deepseek_coder_instruct
      DEFAULT_SYSTEM_PROMPT: ""
      PROMPT_TEMPLATE: "{system_prompt}{instruction}"
  retriever:
    name: bm25
    chunker:
      name: line_level
      max_lines_per_chunk: 20
    max_query_lines: 30
  context_free_prompt_formatter:
    template_file: /home/<USER>/src/augment/research/model_server/prompts/codedit/deepseek_context_free_template.txt
    tokenizer_name: deepseekcoderinstructtokenizer
  context_dependent_prompt_formatter:
    template_file: /home/<USER>/src/augment/research/model_server/prompts/codedit/deepseek_context_dependent_with_retrieval_template.txt
    tokenizer_name: deepseekcoderinstructtokenizer
    use_retrieval: True
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 1024
  input_token_budget: 1500
  default_instruction: "implement this function"
  add_output_trimming: False

task:
  name: hydra
  dataset: repoeval_functions
  limit: 100
  exec: True
  hydra_block_resource_internet_access: True

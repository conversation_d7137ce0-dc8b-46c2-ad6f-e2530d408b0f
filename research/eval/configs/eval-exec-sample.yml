#
# This file contains an example of an evaluation config
#

# Sections:
#   Checkpoints - specify the checkpoints to evaluate
#   Tasks - specify the evaluation tasks for each checkpoint
#   Podspec - overide the default podspec, if necessary
#   NEOX args - additional NEOX args applied to each run
#   Determined - name, workspace, project in the determined UI.
#   eval_tags - Optional dictionary of tags added to the eval results json file.

# Checkpoints
# Fields:
#  path: path or S3 URI to the checkpoint  -- REQUIRED
#  iteration: iteration at which to run the evaluation -- OPTIONAL
#  podspec: checkpoint specific podspec to override the default -- OPTIONAL
#  tags: dictionary of tags to associate with the checkpoint -- OPTIONAL

checkpoints:
  - path: /mnt/efs/augment/checkpoints/test_25000
    iteration: 25000

# Tasks
#   specify the evaluation tasks for each checkpoint

tasks:
  - gitrepo_poly_C_small

# Podspec - set the default podspec for all checkpoints

# The default podspec provides a single RTX_A5000 GPU
podspec: eval-exec-container.yaml
#podspec: a100-podspec.yaml
#podspec: a40-podspec.yaml


# neox_args are passed as overrides to the neox configuration found in the checkpoints.
neox_args:
  temperature: 0
  top_p: 0
  top_k: 0
  eval_with_memories: False

# Determined
# name, workspace, project control location and display in the determined UI.

determined:
  name: Eval Test
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  metaconfig: jobs/templates/eval-exec-metaconfig.yaml

# Optional dictionary of tags added to the eval results json file.
# eval_tags: {}

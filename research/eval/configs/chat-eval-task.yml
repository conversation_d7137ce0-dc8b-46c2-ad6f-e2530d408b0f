system:
  name: "basic_rag"
  model:
    name: "fastbackward_deepseek_instruct"
    checkpoint_path: "/mnt/efs/augment/checkpoints/yury/binks/binks_v2_50781/"
    model_parallel_size: 4
    seq_length: 16384
    override_prompt_formatter:
      name: "chat_template"
      template_file: "/mnt/efs/augment/user/yuri/deepseek_instruct_with_retrieval_template.txt"
      tokenizer_name: "deepseekcoderinstructtokenizer"
      lines_in_prefix_suffix: 0
      lines_in_prefix_suffix_empty_selection: 75
      last_n_turns_in_chat_history: 10
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 1024
  retriever:
    scorer:
      name: "ethanol"
      checkpoint_path: "ethanol/ethanol6-16.1"
    chunker:
      name: "line_level"
      max_lines_per_chunk: 30
    query_formatter:
      name: "ethanol6_query_simple_chat"
      max_tokens: 1023
      add_path: True
      verbose: True
    document_formatter:
      name: "ethanol6_document"
      max_tokens: 999
      add_path: True
  experimental:
    remove_suffix: False
    retriever_top_k: 25
    trim_on_dedent: False
    trim_on_max_lines: null
  verbose: True

task:
  name: chat_eval_task
  dataset_path: "/mnt/efs/augment/data/eval/chat/basic_eval26_apr15"

podspec: 4xA100.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: chat-eval-task-apr15-v8-standalone
  project: yuri
  workspace: Dev

augment:
  gpu_count: 4

system:
    name: remote_chat

    # The model name is optional, will select default if not present
    # model_name: claude-sonnet-3-5-16k-v11-2-chat

    client:
        # Set a url to run the remote system.
        # url: https://dev-<USER>.us-central.api.augmentcode.com
        url: https://staging-shard-0.api.augmentcode.com

        # If not running on determined, the client searchs for the API token
        # in $AUGMENT_TOKEN or ~/.config/augment/api_token
        # To generate your own API token, see
        # https://www.notion.so/Runbook-How-to-generate-API-tokens-a7ede88059604149867f03c2cf6f434b
        # api_token_env_var: AUGMENT_TOKEN
        # api_token_path: ~/.config/augment/api_token

        # These control global retry settings for the client.
        # Likely something is wrong if you need to change these, but if your pods
        # are restarting often, you could try increasing retry_sleep or retry_count.
        # timeout: 60
        # retry_count: 2
        # retry_sleep: 0.1

task:
    name: augment_qa
    dataset_path: /mnt/efs/augment/data/processed/augment_qa/v3
    limit: 1

podspec: gpu-small.yaml
determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: AugmentQA, default model, remote, dogfood
  project: playground
  workspace: Dev

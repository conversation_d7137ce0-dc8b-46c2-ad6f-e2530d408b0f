#
# This file contains an example of an evaluation config
#

# Sections:
#   Checkpoints - specify the checkpoints to evaluate
#   Tasks - specify the evaluation tasks for each checkpoint
#   Podspec - overide the default podspec, if necessary
#   NEOX args - additional NEOX args applied to each run
#   Determined - name, workspace, project in the determined UI.
#   eval_tags - Optional dictionary of tags added to the eval results json file.

# Checkpoints
# Fields:
#  path: path or S3 URI to the checkpoint  -- REQUIRED
#  iteration: iteration at which to run the evaluation -- OPTIONAL
#  podspec: checkpoint specific podspec to override the default -- OPTIONAL

checkpoints:
  - path: /mnt/efs/augment/checkpoints/codegen-350M-multi
  # - path: /mnt/efs/augment/checkpoints/codegen-2B-multi
  # - path: /mnt/efs/augment/checkpoints/codegen-6B-multi
  # - path: /mnt/efs/augment/checkpoints/codegen-16B-multi
  # v-(Indiana-16B)-v
  # - path: /mnt/efs/augment/checkpoints/codegen-16B-finetune-52Btok-merged
  # v-(StarCoder-16B)-v
  # - path: /mnt/efs/augment-ord1/checkpoints/starcoderbase/conv_split


# Tasks
#   specify the evaluation tasks for each checkpoint
#
# The task names encode the type of test, retriever, and prompt style.
#  repo_coder_api_* represent api completion tests
#  repo_coder_line_* represent single line completion tests
#  repo_coder_function_* represent whole function completion
#
# *_zero_shot has no retrieval.
# *_rg1 has jaccard-similarity based retrieval, with prompts in RepoCoder-format.
#  (rg1 = retrieve and generate once)
# *_rg1_indiana uses the Indiana-format prompt rather than RepoCoder-format.
# *_bm25 uses bm25 for retrieval with prompts in Indiana-format.
# *_bm25_repocoder uses RepoCoder-format prompt rather than Indiana-format.
# *_oracle uses a retrieval-query that includes part of the ground truth.
#
tasks:
  - repo_coder_function_zero_shot
  # - repo_coder_api_oracle
  # - repo_coder_api_zero_shot
  # - repo_coder_api_rg1
  # - repo_coder_line_oracle
  # - repo_coder_line_zero_shot
  # - repo_coder_line_rg1
  # - repo_coder_function_oracle
  # - repo_coder_function_rg1
  # - repo_coder_function_bm25
  # - repo_coder_function_bm25_repocoder
  # - repo_coder_function_rg1_indiana

# Podspec - set the default podspec for all checkpoints

# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: A40.yaml

# neox_args are passed as overrides to the neox configuration found in the checkpoints.
neox_args:
  maximum_tokens: 280
  max_position_embeddings: 2048
  seq_length: 2048
  temperature: 0
  top_p: 0
  top_k: 0
  eval_with_memories: False
  eval_batch_size: 10

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: Hydra Eval Test
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-metaconfig.yaml

# Optional dictionary of tags added to the eval results json file.
# eval_tags: {}

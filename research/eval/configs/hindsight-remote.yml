system:
    name: remote_completion
    client:
        url: https://staging-shard-0.api.augmentcode.com

task:
  name: hindsight
  dataset: 2024-03-15-v0.5
  tenant_name: dogfood
  # Required to query GCS for blobs.
  # Populated by augment.dai_gcp_service_accounts below.
  service_account_file: /mnt/augment/secrets/cw-ri-importer/cw-ri-importer.json
  limit: 100

augment:
  # Mapping of secret name to mountpoint *when not running with --local*.
  dai_gcp_service_accounts:
    - secret: aug-prod-cw-ri-importer  # pragma: allowlist secret
      mountpoint: /mnt/augment/secrets/cw-ri-importer

podspec: gpu-small.yaml
determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: hindsight/2024-03-15-v0.5/dogfood, default model, remote, dogfood
  project: playground
  workspace: Dev

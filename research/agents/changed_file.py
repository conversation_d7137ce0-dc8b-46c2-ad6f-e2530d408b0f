"""Definition of ChangedFile class for tracking workspace changes."""

from dataclasses import dataclass
from typing import Any, Dict, Literal, Optional

import dataclasses_json


# Define the possible file change types as a Literal type
FileChangeType = Literal["ADDED", "DELETED", "MODIFIED", "RENAMED"]


@dataclass
class ChangedFile(dataclasses_json.DataClassJsonMixin):
    """Represents a file that has changed in the workspace.

    This class mirrors the ChangedFile message in public_api.proto.
    """

    old_path: str
    """Path to the file before the change. Empty if file was added."""

    new_path: str
    """Path to the file after the change. Empty if file was deleted."""

    old_contents: str
    """Contents of the file before the change."""

    new_contents: str
    """Contents of the file after the change."""

    change_type: FileChangeType
    """Type of change made to the file."""

    @classmethod
    def added(cls, new_path: str, new_contents: str) -> "ChangedFile":
        """Create a ChangedFile for an added file."""
        return cls(
            old_path="",
            new_path=new_path,
            old_contents="",
            new_contents=new_contents,
            change_type="ADDED",
        )

    @classmethod
    def deleted(cls, old_path: str, old_contents: str) -> "ChangedFile":
        """Create a ChangedFile for a deleted file."""
        return cls(
            old_path=old_path,
            new_path="",
            old_contents=old_contents,
            new_contents="",
            change_type="DELETED",
        )

    @classmethod
    def modified(cls, path: str, old_contents: str, new_contents: str) -> "ChangedFile":
        """Create a ChangedFile for a modified file."""
        return cls(
            old_path=path,
            new_path=path,
            old_contents=old_contents,
            new_contents=new_contents,
            change_type="MODIFIED",
        )

    @classmethod
    def renamed(
        cls, old_path: str, new_path: str, old_contents: str, new_contents: str
    ) -> "ChangedFile":
        """Create a ChangedFile for a renamed file."""
        return cls(
            old_path=old_path,
            new_path=new_path,
            old_contents=old_contents,
            new_contents=new_contents,
            change_type="RENAMED",
        )

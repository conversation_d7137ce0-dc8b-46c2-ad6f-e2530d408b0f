system:
  name: agent
  num_processes: 8
  workdir: /run/determined/workdir/swe_work
  log_dir_base: /mnt/efs/augment/user/colin/agent_logs/
  agent_timeout: 7200
  auth_token_file: /run/determined/secrets/augment-token/token
  use_low_qos_server: true

  # agent system config
  modal: false
  add_sequential_thinking: true
  add_scratchpad: false
  add_retrieval: false
  swebench_sparse_system_prompt: false

parallelize_task_n_ways: 2
task:
  name: swe_bench
  limit_by_example_names:
  - django__django-12858
  - django__django-14725
  - django__django-15629
  - matplotlib__matplotlib-25775
  - pylint-dev__pylint-8898
  - sphinx-doc__sphinx-9230
  - sympy__sympy-13031
  - sympy__sympy-13877
  - sympy__sympy-17630
  - sympy__sympy-20428

  dataset_path: /mnt/efs/augment/data/swebench_processed_eval/v1.pickle.gz
  workdir: /run/determined/workdir/swe_work
  num_eval_processes: 8
  skip_generation: false
  eager_eval: true

podspec: 1xH100-gcp.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig-swebench.yaml
  name: swe-bench-verified-v45-fork-processfailures
  project: swebench
  workspace: Dev

augment:
  user_include_files:
  - research/agents/configs/.custom_detinclude

system:
  name: agent
  num_processes: 8
  workdir: /run/determined/workdir/swe_work
  log_dir_base: /mnt/efs/augment/user/colin/agent_logs/
  agent_timeout: 7200
  auth_token_file: /run/determined/secrets/augment-token/token
  use_low_qos_server: true

  # agent system config
  modal: false
  add_sequential_thinking: true
  add_scratchpad: false
  add_retrieval: false
  swebench_sparse_system_prompt: false
  orientation_stage: false

  candidate_for_filtering_dir: /mnt/efs/augment/public_html/swebench/consolidated_runs/12359_12360_12361_12362_12363_12364_12365_12366_12367_12368/

parallelize_task_n_ways: 10
task:
  name: swe_bench
  limit_examples_per_shard: 50
  #limit_by_example_names:
  #- astropy__astropy-8707
  dataset_path: /mnt/efs/augment/data/swebench_processed_eval/v1.pickle.gz
  workdir: /run/determined/workdir/swe_work
  num_eval_processes: 8
  skip_generation: false
  eager_eval: true

podspec: 1xH100-gcp.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig-swebench.yaml
  name: swe-bench-verified-v50-filter-full-12359_12360_12361_12362_12363_12364_12365_12366_12367_12368
  project: swebench
  workspace: Dev

augment:
  user_include_files:
  - research/agents/configs/.custom_detinclude

system:
  name: agent
  num_processes: 8
  workdir: /run/determined/workdir/swe_work
  log_dir_base: /mnt/efs/augment/user/colin/agent_logs/
  agent_timeout: 7200
  auth_token_file: /run/determined/secrets/augment-token/token
  use_low_qos_server: true

  # agent system config
  modal: false
  add_sequential_thinking: true
  add_scratchpad: false
  add_retrieval: false
  swebench_sparse_system_prompt: true

parallelize_task_n_ways: 3
task:
  name: swe_bench
  limit_by_example_names:
  - django__django-12273
  - django__django-13279
  - django__django-13964
  - django__django-11149
  - django__django-12262
  - django__django-16032
  - django__django-12965
  - django__django-14404
  - django__django-12193
  - django__django-13297
  - django__django-14376
  - django__django-13590
  - django__django-12754
  - django__django-15916
  - django__django-11532
  - django__django-14608
  - django__django-13401
  - django__django-13315
  - django__django-15987
  - django__django-11964
  - django__django-15128
  limit_examples_per_shard: 8
  dataset_path: /mnt/efs/augment/data/swebench_processed_eval/v1.pickle.gz
  workdir: /run/determined/workdir/swe_work
  num_eval_processes: 8
  skip_generation: false
  eager_eval: true

podspec: 1xH100-gcp.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig-swebench.yaml
  name: swe-bench-verified-v42-django-challengeset-1-sparse-system-prompt
  project: swebench
  workspace: Dev

augment:
  user_include_files:
  - research/agents/configs/.custom_detinclude

system:
  name: agent
  num_processes: 8
  workdir: /run/determined/workdir/swe_work
  log_dir_base: /mnt/efs/augment/user/colin/agent_logs/
  agent_timeout: 7200
  auth_token_file: /run/determined/secrets/augment-token/token

parallelize_task_n_ways: 5
task:
  name: swe_bench
  limit_examples: 10
  dataset_path: /mnt/efs/augment/data/swebench_processed_eval/v1.pickle.gz
  workdir: /run/determined/workdir/swe_work
  num_eval_processes: 8
  skip_generation: false
  eager_eval: true

podspec: 1xH100-gcp.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig-swebench.yaml
  name: swe-bench-verified-v31-lim50
  project: swebench
  workspace: Dev

augment:
  user_include_files:
  - research/agents/configs/.custom_detinclude

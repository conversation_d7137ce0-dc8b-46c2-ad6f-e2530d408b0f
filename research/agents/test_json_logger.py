"""Tests for the JSON logging functionality in ToolCallLogger."""

import json
import tempfile
from pathlib import Path
from typing import Any, Optional, cast
from unittest.mock import Mock

import pytest

from research.agents.tools import (
    LLMTool,
    ToolCallLogger,
    ToolCallParameters,
    ToolImplOutput,
)
from research.llm_apis.llm_client import (
    AssistantContentBlock,
    GeneralContentBlock,
    TextPrompt,
    TextResult,
)


class MockTool(LLMTool):
    def __init__(self, name: str, output: str):
        # Use a Mock object for tool_call_logger
        super().__init__(tool_call_logger=Mock())
        self.name = name
        self.description = f"Mock tool named {name}"
        self.output = output
        self.input_schema = {
            "type": "object",
            "properties": {
                "test_input": {"type": "string", "description": "Test input"}
            },
            "required": ["test_input"],
        }

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[Any] = None,
    ) -> ToolImplOutput:
        return ToolImplOutput(
            tool_output=self.output,
            tool_result_message="Mock tool called",
            auxiliary_data={},
        )


def test_tool_call_logger_json_output():
    """Test ToolCallLogger JSON file output functionality."""
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create temporary file paths
        json_log_file = Path(temp_dir) / "test_log.json"

        # Create a ToolCallLogger with JSON file output
        logger = ToolCallLogger(
            verbose=True,
            json_log_file=json_log_file,
        )

        # Create a mock tool
        mock_tool = MockTool("test_tool", "test_output")

        # Log a tool call
        logger.tool_call_started(
            called_tool=mock_tool,
            tool_input={"test_input": "test"},
            tool_started_message="Starting test tool",
        )

        logger.tool_call_ended(
            called_tool=mock_tool,
            tool_input={"test_input": "test"},
            tool_output="Test output",
            tool_result_message="Test tool completed",
            duration=0.5,
        )

        # Verify JSON file was created
        assert json_log_file.exists()

        # Read the JSON file
        with json_log_file.open("r") as f:
            json_data = json.load(f)

        # Verify JSON data structure
        assert isinstance(json_data, list)
        assert len(json_data) == 2  # One entry for start, one for end

        # Verify tool call start entry
        start_entry = json_data[0]
        assert start_entry["type"] == "tool_call"
        assert start_entry["started"] is True
        assert start_entry["tool_name"] == "test_tool"
        assert start_entry["tool_input"] == {"test_input": "test"}
        assert start_entry["tool_message"] == "Starting test tool"

        # Verify tool call end entry
        end_entry = json_data[1]
        assert end_entry["type"] == "tool_call"
        assert end_entry["started"] is False
        assert end_entry["tool_name"] == "test_tool"
        assert end_entry["tool_input"] == {"test_input": "test"}
        assert end_entry["tool_output"] == "Test output"
        assert end_entry["tool_message"] == "Test tool completed"
        assert end_entry["duration"] == 0.5


def test_tool_call_logger_json_llm_call():
    """Test ToolCallLogger JSON file output for LLM calls."""
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create temporary file paths
        json_log_file = Path(temp_dir) / "test_llm_log.json"

        # Create a ToolCallLogger with JSON file output
        logger = ToolCallLogger(
            verbose=True,
            json_log_file=json_log_file,
        )

        # Mock LLM call parameters using proper TextPrompt objects
        messages_raw = [[TextPrompt("test prompt")]]
        # Cast to the expected type
        messages = cast(list[list[GeneralContentBlock]], messages_raw)
        max_tokens = 100
        system_prompt = "test system prompt"
        temperature = 0.7
        tools = []
        tool_choice = None

        # Log LLM call start
        logger.language_model_call_started(
            messages=messages,
            max_tokens=max_tokens,
            system_prompt=system_prompt,
            temperature=temperature,
            tools=tools,
            tool_choice=tool_choice,
        )

        # Log LLM call end
        response_raw = [TextResult("test response")]
        # Cast to the expected type
        response = cast(list[AssistantContentBlock], response_raw)
        response_metadata = {"metadata": "test"}

        logger.language_model_call_ended(
            messages=messages,
            max_tokens=max_tokens,
            system_prompt=system_prompt,
            temperature=temperature,
            tools=tools,
            tool_choice=tool_choice,
            response=response,
            response_metadata=response_metadata,
            duration=0.3,
        )

        # Verify JSON file was created
        assert json_log_file.exists()

        # Read the JSON file
        with json_log_file.open("r") as f:
            json_data = json.load(f)

        # Verify JSON data structure
        assert isinstance(json_data, list)
        assert len(json_data) == 2  # One entry for start, one for end

        # Verify LLM call start entry
        start_entry = json_data[0]
        assert start_entry["type"] == "llm_call"
        assert start_entry["started"] is True
        assert start_entry["max_tokens"] == max_tokens
        assert start_entry["system_prompt"] == system_prompt
        assert start_entry["temperature"] == temperature

        # Verify LLM call end entry
        end_entry = json_data[1]
        assert end_entry["type"] == "llm_call"
        assert end_entry["started"] is False
        assert end_entry["max_tokens"] == max_tokens
        assert end_entry["system_prompt"] == system_prompt
        assert end_entry["temperature"] == temperature
        assert end_entry["duration"] == 0.3
        assert end_entry["response_metadata"] == response_metadata


def test_tool_call_logger_finish_with_json():
    """Test ToolCallLogger finish method with JSON file output."""
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create temporary file paths
        json_log_file = Path(temp_dir) / "test_finish.json"

        # Create a ToolCallLogger with JSON file output
        logger = ToolCallLogger(
            verbose=True,
            json_log_file=json_log_file,
        )

        # Create a mock tool
        mock_tool = MockTool("test_tool", "test_output")

        # Log a tool call
        logger.tool_call_started(
            called_tool=mock_tool,
            tool_input={"test_input": "test"},
            tool_started_message="Starting test tool",
        )

        # Call finish method
        logger.finish()

        # Verify JSON file was created
        assert json_log_file.exists()

        # Read the JSON file
        with json_log_file.open("r") as f:
            json_data = json.load(f)

        # Verify JSON data structure
        assert isinstance(json_data, list)
        assert len(json_data) == 1  # Only the start entry
        assert json_data[0]["tool_name"] == "test_tool"

        # Log another tool call
        logger.tool_call_ended(
            called_tool=mock_tool,
            tool_input={"test_input": "test"},
            tool_output="Test output",
            tool_result_message="Test tool completed",
            duration=0.5,
        )

        # Call finish method again
        logger.finish()

        # Read the JSON file again
        with json_log_file.open("r") as f:
            json_data = json.load(f)

        # Verify JSON data structure was updated
        assert isinstance(json_data, list)
        assert len(json_data) == 2  # Now includes both start and end entries

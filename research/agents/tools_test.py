"""Tests for tools.py."""

import pytest
from research.agents.tools import (
    DialogMessages,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolCallParameters,
)
from experimental.michiel.research.agentqa.tools import ToolFormattedResult


def test_dialog_messages():
    """Test DialogMessages class."""
    dialog = DialogMessages()

    # Add user prompt
    dialog.add_user_prompt("Hello")
    assert dialog.is_assistant_turn()

    # Add model response
    dialog.add_model_response([TextResult(text="Hi")])
    assert dialog.is_user_turn()

    # Add tool call
    dialog.add_user_prompt("Run tool")
    assert dialog.is_assistant_turn()

    dialog.add_model_response(
        [
            TextResult(text="Let me run the tool"),
            ToolCall(
                tool_call_id="test1",
                tool_name="test_tool",
                tool_input={"input": "test"},
            ),
        ]
    )
    assert dialog.is_user_turn()

    # Add tool result
    dialog.add_tool_call_result(
        ToolCallParameters(
            tool_call_id="test1", tool_name="test_tool", tool_input={"input": "test"}
        ),
        "Tool result",
    )
    assert dialog.is_assistant_turn()

    # Get messages for LLM client
    messages = dialog.get_messages_for_llm_client()
    assert len(messages) == 5  # 5 turns
    assert isinstance(messages[0][0], TextPrompt)  # First user prompt
    assert messages[0][0].text == "Hello"
    assert isinstance(messages[1][0], TextResult)  # First model response
    assert messages[1][0].text == "Hi"
    assert isinstance(messages[2][0], TextPrompt)  # Second user prompt
    assert messages[2][0].text == "Run tool"
    assert isinstance(messages[3][0], TextResult)  # Second model response
    assert messages[3][0].text == "Let me run the tool"
    assert isinstance(messages[3][1], ToolCall)  # Tool call
    assert messages[3][1].tool_name == "test_tool"
    assert messages[3][1].tool_input == {"input": "test"}
    assert isinstance(messages[4][0], ToolFormattedResult)  # Tool result
    assert messages[4][0].tool_name == "test_tool"
    assert messages[4][0].tool_output == "Tool result"


def test_dialog_messages_get_last_model_text_response():
    """Test getting last model text response."""
    dialog = DialogMessages()

    # Add user prompt and model response
    dialog.add_user_prompt("Hello")
    dialog.add_model_response([TextResult(text="Hi")])

    # Add another user prompt and model response with tool call
    dialog.add_user_prompt("Run tool")
    dialog.add_model_response(
        [
            TextResult(text="Let me run the tool"),
            ToolCall(
                tool_call_id="test1",
                tool_name="test_tool",
                tool_input={"input": "test"},
            ),
        ]
    )

    # Get last model text response
    assert dialog.get_last_model_text_response() == "Let me run the tool"


def test_dialog_messages_get_pending_tool_calls():
    """Test getting pending tool calls."""
    dialog = DialogMessages()

    # Add user prompt and model response with tool call
    dialog.add_user_prompt("Run tool")
    dialog.add_model_response(
        [
            TextResult(text="Let me run the tool"),
            ToolCall(
                tool_call_id="test1",
                tool_name="test_tool",
                tool_input={"input": "test"},
            ),
        ]
    )

    # Get pending tool calls
    pending_calls = dialog.get_pending_tool_calls()
    assert len(pending_calls) == 1
    assert pending_calls[0].tool_name == "test_tool"
    assert pending_calls[0].tool_input == {"input": "test"}


def test_dialog_messages_get_last_user_prompt():
    """Test getting last user prompt."""
    dialog = DialogMessages()

    # Add first user prompt and model response
    dialog.add_user_prompt("Hello")

    # Get last user prompt
    assert dialog.get_last_user_prompt() == "Hello"

    # Add model response and second user prompt
    dialog.add_model_response([TextResult(text="Hi")])
    dialog.add_user_prompt("How are you?")

    # Get last user prompt
    assert dialog.get_last_user_prompt() == "How are you?"

    # Test error when not assistant's turn
    dialog.add_model_response([TextResult(text="I'm good")])
    with pytest.raises(
        AssertionError,
        match="Can only get/replace last user prompt on assistant's turn",
    ):
        dialog.get_last_user_prompt()


def test_dialog_messages_replace_last_user_prompt():
    """Test replacing last user prompt."""
    dialog = DialogMessages()

    # Add first user prompt and model response
    dialog.add_user_prompt("Hello")

    # Replace last user prompt
    dialog.replace_last_user_prompt("What's up?")

    # Verify prompt was replaced
    assert dialog.get_last_user_prompt() == "What's up?"

    # Add model response and second user prompt
    dialog.add_model_response([TextResult(text="Hi")])
    dialog.add_user_prompt("How are you?")

    # Replace last user prompt
    dialog.replace_last_user_prompt("New prompt")

    # Verify prompt was replaced
    assert dialog.get_last_user_prompt() == "New prompt"

    # Test error when not assistant's turn
    dialog.add_model_response([TextResult(text="I'm good")])
    with pytest.raises(
        AssertionError,
        match="Can only get/replace last user prompt on assistant's turn",
    ):
        dialog.replace_last_user_prompt("Another prompt")


def test_dialog_messages_replace_last_user_prompt_with_tool_call():
    """Test replacing last user prompt when there's a tool call."""
    dialog = DialogMessages()

    # Add user prompt and model response with tool call
    dialog.add_user_prompt("Run tool")
    dialog.add_model_response(
        [
            TextResult(text="Let me run the tool"),
            ToolCall(
                tool_call_id="test1",
                tool_name="test_tool",
                tool_input={"input": "test"},
            ),
        ]
    )

    # Add tool result and model response
    dialog.add_tool_call_result(
        ToolCallParameters(
            tool_call_id="test1", tool_name="test_tool", tool_input={"input": "test"}
        ),
        "Tool result",
    )
    dialog.add_model_response([TextResult(text="Tool completed")])

    # Add new user prompt
    dialog.add_user_prompt("Next prompt")

    # Replace last user prompt
    dialog.replace_last_user_prompt("New prompt")

    # Verify prompt was replaced
    assert dialog.get_last_user_prompt() == "New prompt"

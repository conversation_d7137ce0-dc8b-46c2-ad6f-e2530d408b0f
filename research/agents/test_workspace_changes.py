"""Tests for workspace changes logging."""

import json
import unittest
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from research.agents.tools import Tool<PERSON>allLogger, LoggedWorkspaceChanges
from research.agents.changed_file import ChangedFile, FileChangeType


class TestWorkspaceChanges(unittest.TestCase):
    """Tests for workspace changes logging."""

    def test_workspace_changed(self):
        """Test that ToolCallLogger correctly logs workspace changes."""
        # Create a logger
        logger = ToolCallLogger(verbose=True)

        # Create some changed files
        changed_files = [
            ChangedFile.added(
                new_path="new_file.txt",
                new_contents="New file contents",
            ),
            ChangedFile.modified(
                path="modified_file.txt",
                old_contents="Old contents",
                new_contents="New contents",
            ),
            ChangedFile.deleted(
                old_path="deleted_file.txt",
                old_contents="Deleted file contents",
            ),
            ChangedFile.renamed(
                old_path="old_name.txt",
                new_path="new_name.txt",
                old_contents="File contents",
                new_contents="File contents",
            ),
        ]

        # Log workspace changes
        logger.workspace_changed(
            changed_files=changed_files,
            description="Test workspace changes",
        )

        # Check that the changes were logged
        self.assertEqual(len(logger.logged_calls), 1)
        workspace_changes = logger.logged_calls[0]
        self.assertIsInstance(workspace_changes, LoggedWorkspaceChanges)

        # Type cast to LoggedWorkspaceChanges to avoid pyright errors
        workspace_changes_typed: LoggedWorkspaceChanges = workspace_changes  # type: ignore

        self.assertEqual(len(workspace_changes_typed.changed_files), 4)
        self.assertEqual(workspace_changes_typed.description, "Test workspace changes")

        # Check the types of changes
        change_types = [cf.change_type for cf in workspace_changes_typed.changed_files]
        self.assertEqual(
            change_types,
            [
                "ADDED",
                "MODIFIED",
                "DELETED",
                "RENAMED",
            ],
        )

        # Check the paths
        self.assertEqual(
            workspace_changes_typed.changed_files[0].new_path, "new_file.txt"
        )
        self.assertEqual(
            workspace_changes_typed.changed_files[1].new_path, "modified_file.txt"
        )
        self.assertEqual(
            workspace_changes_typed.changed_files[2].old_path, "deleted_file.txt"
        )
        self.assertEqual(
            workspace_changes_typed.changed_files[3].old_path, "old_name.txt"
        )
        self.assertEqual(
            workspace_changes_typed.changed_files[3].new_path, "new_name.txt"
        )

    def test_changed_file_json_serialization(self):
        """Test that ChangedFile objects can be serialized to JSON."""
        # Create a ChangedFile
        changed_file = ChangedFile.added(
            new_path="new_file.txt",
            new_contents="New file contents",
        )

        # Convert to JSON
        json_str = changed_file.to_json()

        # Parse the JSON
        json_dict = json.loads(json_str)

        # Check that the JSON contains the expected fields
        self.assertEqual(json_dict["old_path"], "")
        self.assertEqual(json_dict["new_path"], "new_file.txt")
        self.assertEqual(json_dict["old_contents"], "")
        self.assertEqual(json_dict["new_contents"], "New file contents")
        self.assertEqual(json_dict["change_type"], "ADDED")

        # Test that we can convert back to a ChangedFile
        changed_file2 = ChangedFile.from_json(json_str)
        self.assertEqual(changed_file2.old_path, "")
        self.assertEqual(changed_file2.new_path, "new_file.txt")
        self.assertEqual(changed_file2.old_contents, "")
        self.assertEqual(changed_file2.new_contents, "New file contents")
        self.assertEqual(changed_file2.change_type, "ADDED")


if __name__ == "__main__":
    unittest.main()

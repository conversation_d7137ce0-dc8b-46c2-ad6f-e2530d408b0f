"""Tests for the tools module."""

from typing import Any, Optional, cast
from unittest.mock import Mock, ANY, patch
import datetime
import json
import tempfile
from pathlib import Path

import jsonschema
import pytest

from research.agents.tools import (
    DialogMessages,
    LLMTool,
    LoggedLanguageModelCall,
    LoggedToolCall,
    LoggingLLMClient,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolCallLogger,
    ToolCallParameters,
    ToolImplOutput,
    ToolParam,
    call_tools,
    ToolFormattedResult,
)
from research.llm_apis.llm_client import (
    AssistantContentBlock,
    GeneralContentBlock,
    LLMClient,
    LLMMessages,
)


@pytest.fixture()
def anthropic_client():
    from research.llm_apis.llm_client import AnthropicVertexClient

    return AnthropicVertexClient(model_name="claude-3-5-sonnet-v2@20241022")


class MockTool(LLMTool):
    def __init__(self, name: str, output: str):
        super().__init__(tool_call_logger=Mock())
        self.name = name
        self.description = f"Mock tool named {name}"
        self.output = output
        self.input_schema = {
            "type": "object",
            "properties": {
                "test_input": {"type": "string", "description": "Test input"}
            },
            "required": ["test_input"],
        }

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        return ToolImplOutput(
            tool_output=self.output,
            tool_result_message="Mock tool called",
            auxiliary_data={},
        )


def test_logging_llm_client_init():
    """Test LoggingLLMClient initialization."""
    mock_client = Mock(spec=LLMClient)
    mock_logger = Mock(spec=ToolCallLogger)

    logging_client = LoggingLLMClient(client=mock_client, tool_call_logger=mock_logger)

    assert logging_client.client == mock_client
    assert logging_client.tool_call_logger == mock_logger


def test_logging_llm_client_generate():
    """Test LoggingLLMClient generate method logs calls correctly."""
    # Setup
    mock_client = Mock(spec=LLMClient)
    mock_logger = Mock(spec=ToolCallLogger)

    # Mock the client's generate method to return some results
    expected_response = (
        [TextResult(text="test response")],
        {"metadata": "test"},
    )
    mock_client.generate.return_value = expected_response

    logging_client = LoggingLLMClient(client=mock_client, tool_call_logger=mock_logger)

    # Test parameters
    messages: LLMMessages = [[TextPrompt(text="test prompt")]]
    max_tokens = 100
    system_prompt = "test system prompt"
    temperature = 0.7
    tools = []
    tool_choice = None

    # Call generate
    response, metadata = logging_client.generate(
        messages=messages,
        max_tokens=max_tokens,
        system_prompt=system_prompt,
        temperature=temperature,
        tools=tools,
        tool_choice=tool_choice,
    )

    # Verify the client was called with correct parameters
    mock_client.generate.assert_called_once_with(
        messages=messages,
        max_tokens=max_tokens,
        system_prompt=system_prompt,
        temperature=temperature,
        tools=tools,
        tool_choice=tool_choice,
    )

    # Verify logger was called correctly
    mock_logger.language_model_call_started.assert_called_once_with(
        messages=messages,
        max_tokens=max_tokens,
        system_prompt=system_prompt,
        temperature=temperature,
        tools=tools,
        tool_choice=tool_choice,
    )

    mock_logger.language_model_call_ended.assert_called_once_with(
        messages=messages,
        max_tokens=max_tokens,
        system_prompt=system_prompt,
        temperature=temperature,
        tools=tools,
        duration=ANY,
        tool_choice=tool_choice,
        response=expected_response[0],
        response_metadata=expected_response[1],
    )

    # Verify the response is correct
    assert response == expected_response[0]
    assert metadata == expected_response[1]


def test_logging_llm_client_generate_with_error():
    """Test LoggingLLMClient generate method handles errors correctly."""
    # Setup
    mock_client = Mock(spec=LLMClient)
    mock_logger = Mock(spec=ToolCallLogger)

    # Mock the client's generate method to raise an exception
    mock_client.generate.side_effect = ValueError("test error")

    logging_client = LoggingLLMClient(client=mock_client, tool_call_logger=mock_logger)

    # Test parameters
    messages: LLMMessages = [[TextPrompt(text="test prompt")]]
    max_tokens = 100

    # Call generate and expect exception
    with pytest.raises(ValueError, match="test error"):
        logging_client.generate(messages=messages, max_tokens=max_tokens)

    # Verify logger was called correctly for start but not for end (due to error)
    mock_logger.language_model_call_started.assert_called_once_with(
        messages=messages,
        max_tokens=max_tokens,
        system_prompt=None,
        temperature=0.0,
        tools=[],
        tool_choice=None,
    )

    # Verify language_model_call_ended was not called due to error
    mock_logger.language_model_call_ended.assert_not_called()


def test_drop_final_assistant_turn():
    dialog = DialogMessages()
    dialog.add_user_prompt("Hello")
    dialog.add_model_response([TextResult("Hi there!")])

    assert dialog.is_user_turn()
    assert len(dialog._message_lists) == 2

    dialog.drop_final_assistant_turn()

    assert dialog.is_assistant_turn()
    assert len(dialog._message_lists) == 1
    assert isinstance(dialog._message_lists[0][0], TextPrompt)


def test_drop_tool_calls_from_final_turn():
    dialog = DialogMessages()
    dialog.add_user_prompt("Hello")
    dialog.add_model_response(
        [
            TextResult("Let me check that for you."),
            ToolCall(
                tool_call_id="1", tool_name="search", tool_input={"query": "test"}
            ),
        ]
    )

    assert dialog.is_user_turn()
    assert len(dialog._message_lists) == 2
    assert len(dialog._message_lists[1]) == 2

    dialog.drop_tool_calls_from_final_turn()

    assert dialog.is_user_turn()
    assert len(dialog._message_lists) == 2
    assert len(dialog._message_lists[1]) == 1
    assert isinstance(dialog._message_lists[1][0], TextResult)


def test_drop_final_assistant_turn_on_user_turn():
    dialog = DialogMessages()
    dialog.add_user_prompt("Hello")

    assert dialog.is_assistant_turn()
    assert len(dialog._message_lists) == 1

    dialog.drop_final_assistant_turn()

    assert dialog.is_assistant_turn()
    assert len(dialog._message_lists) == 1


def test_drop_tool_calls_from_final_turn_with_no_tool_calls():
    dialog = DialogMessages()
    dialog.add_user_prompt("Hello")
    dialog.add_model_response([TextResult("Hi there!")])

    assert dialog.is_user_turn()
    assert len(dialog._message_lists) == 2

    dialog.drop_tool_calls_from_final_turn()

    assert dialog.is_user_turn()
    assert len(dialog._message_lists) == 2
    assert len(dialog._message_lists[1]) == 1
    assert isinstance(dialog._message_lists[1][0], TextResult)


def test_allow_append_to_tool_call_results():
    dialog = DialogMessages()
    dialog.add_user_prompt("Hello")
    dialog.add_model_response(
        [
            TextResult("Let me check that for you."),
            ToolCall(
                tool_call_id="1", tool_name="search", tool_input={"query": "test"}
            ),
        ]
    )
    dialog.add_tool_call_result(
        ToolCallParameters(
            tool_call_id="1", tool_name="search", tool_input={"query": "test"}
        ),
        "Results",
    )
    dialog.add_user_prompt("Thanks!", allow_append_to_tool_call_results=True)

    assert dialog.is_assistant_turn()
    assert len(dialog._message_lists) == 3
    assert len(dialog._message_lists[2]) == 2


def test_allow_append_to_tool_call_results_with_anthropic(anthropic_client):
    tools = [
        ToolParam(
            name="web_search",
            description="Search the internet for information.",
            input_schema={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The search query."}
                },
                "required": ["query"],
            },
        )
    ]

    dialog = DialogMessages()
    dialog.add_user_prompt(
        "find the most common boy's name in the US, use the supplied tool"
    )

    messages = dialog.get_messages_for_llm_client()
    response, metadata = anthropic_client.generate(
        messages,
        max_tokens=1024,
        tools=tools,
        system_prompt="You are a helpful assistant.",
    )

    dialog.add_model_response(response)

    pending_tool_calls = dialog.get_pending_tool_calls()
    assert len(pending_tool_calls) == 1
    assert pending_tool_calls[0].tool_name == "web_search"
    assert "boy" in pending_tool_calls[0].tool_input["query"]

    dialog.add_tool_call_result(
        ToolCallParameters(
            tool_call_id=pending_tool_calls[0].tool_call_id,
            tool_name="search",
            tool_input={"query": "test"},
        ),
        "The most common name is John",
    )

    dialog.add_user_prompt(
        "Thanks! What's the most common girl's name?",
        allow_append_to_tool_call_results=True,
    )

    messages = dialog.get_messages_for_llm_client()
    response, metadata = anthropic_client.generate(
        messages,
        max_tokens=1024,
        tools=tools,
        system_prompt="You are a helpful assistant.",
    )

    dialog.add_model_response(response)

    pending_tool_calls = dialog.get_pending_tool_calls()
    assert len(pending_tool_calls) == 1
    assert pending_tool_calls[0].tool_name == "web_search"
    assert "girl" in pending_tool_calls[0].tool_input["query"]


def test_call_tools():
    tool1 = MockTool("tool1", output="Output from tool1")
    tool2 = MockTool("tool2", output="Output from tool2")
    tools: list[LLMTool] = [tool1, tool2]

    calls_to_make = [
        ToolCallParameters(
            tool_call_id="1", tool_name="tool1", tool_input={"test_input": "test"}
        ),
        ToolCallParameters(
            tool_call_id="2", tool_name="tool2", tool_input={"test_input": "test"}
        ),
    ]

    results = call_tools(tools, calls_to_make)
    assert results == ["Output from tool1", "Output from tool2"]


def test_call_tools_with_missing_tool():
    tool1 = MockTool("tool1", "Output from tool1")
    tools: list[LLMTool] = [tool1]

    calls_to_make = [
        ToolCallParameters(
            tool_call_id="1",
            tool_name="non_existent_tool",
            tool_input={"test_input": "test"},
        )
    ]

    with pytest.raises(StopIteration):
        call_tools(tools, calls_to_make)


def test_call_tools_with_empty_inputs():
    results = call_tools([], [])
    assert results == []


def test_generate_summary_statistics():
    """Test generate_summary_statistics method."""
    dialog = DialogMessages()

    # Add various types of messages
    dialog.add_user_prompt("Hello")  # Text prompt
    dialog.add_model_response(
        [
            TextResult("Let me help you"),  # Text result
            ToolCall(
                tool_call_id="1", tool_name="read_file", tool_input={"path": "test.txt"}
            ),  # Tool call
        ]
    )
    dialog.add_tool_call_result(  # Tool formatted result
        ToolCallParameters(
            tool_call_id="1", tool_name="read_file", tool_input={"path": "test.txt"}
        ),
        "File contents",
    )

    # Get and parse stats
    stats_str = dialog.generate_summary_statistics()
    stats = json.loads(stats_str)

    # Verify structure and content
    assert isinstance(stats, dict)
    assert "total_tokens" in stats
    assert "total_turns" in stats
    assert stats["total_turns"] == 3

    # Check token count lists
    assert isinstance(stats["text_prompt_tokens"], dict)
    assert isinstance(stats["text_result_tokens"], dict)
    assert isinstance(stats["tool_formatted_result_tokens"], dict)
    assert isinstance(stats["tool_call_tokens"], dict)

    # Verify tool-specific token counts
    assert "read_file_tokens" in stats
    assert "launch_process_tokens" in stats
    assert "other_tool_tokens" in stats

    # Check truncation history - can be empty list or "No values found"
    assert "truncation_history_token_cts" in stats
    assert (
        stats["truncation_history_token_cts"] == []
        or stats["truncation_history_token_cts"] == "No values found."
    )


def test_run_truncation_strategy():
    """Test run_truncation_strategy method."""
    dialog = DialogMessages(use_prompt_budgeting=True)
    dialog.truncate_all_but_N = 2  # Keep last 2 turns
    dialog.token_budget_to_trigger_truncation = 1  # Force truncation

    # Add several turns with large tool results to ensure truncation
    large_content = "x" * 1000  # Large content to ensure truncation
    for i in range(5):
        # User turn
        dialog.add_user_prompt(f"Turn {i}")
        # Assistant turn with tool call
        dialog.add_model_response(
            [
                TextResult(f"Response {i}"),
                ToolCall(
                    tool_call_id=str(i),
                    tool_name="test_tool",
                    tool_input={"input": f"test {i}"},
                ),
            ]
        )
        # User turn with tool result
        dialog.add_tool_call_result(
            ToolCallParameters(
                tool_call_id=str(i),
                tool_name="test_tool",
                tool_input={"input": f"test {i}"},
            ),
            f"Tool result {i}: {large_content}",
        )
        # Assistant turn with final response
        dialog.add_model_response([TextResult(f"Final response {i}")])

    # Get initial token count
    initial_tokens = dialog.count_tokens()

    # Run truncation
    dialog.run_truncation_strategy()

    # Verify truncation occurred
    final_tokens = dialog.count_tokens()
    assert final_tokens < initial_tokens

    # Verify truncation history was recorded
    assert len(dialog.truncation_history_token_cts) == 1
    assert dialog.truncation_history_token_cts[0] == initial_tokens - final_tokens

    # Verify early turns were truncated but last N turns were preserved
    messages = dialog.get_messages_for_llm_client()

    # Check early turns are truncated
    truncated_found = False
    for turn in messages[:-2]:  # Check turns before last 2
        for msg in turn:
            if isinstance(msg, ToolFormattedResult):
                if "[Truncated..." in msg.tool_output:
                    truncated_found = True
                    break
    assert truncated_found, "Expected to find truncated content in early turns"

    # Verify last N turns are not truncated
    for turn in messages[-2:]:  # Check last 2 turns
        for msg in turn:
            if isinstance(msg, ToolFormattedResult):
                assert (
                    "[Truncated..." not in msg.tool_output
                ), "Last N turns should not be truncated"


def test_use_prompt_budgeting_flag():
    """Test use_prompt_budgeting flag behavior."""
    # Test with budgeting disabled
    dialog_no_budget = DialogMessages(use_prompt_budgeting=False)
    dialog_no_budget.token_budget_to_trigger_truncation = (
        1  # Set very low to force truncation
    )

    # Add messages
    dialog_no_budget.add_user_prompt("Test message")
    dialog_no_budget.add_model_response([TextResult("Test response")])
    dialog_no_budget.add_tool_call_result(
        ToolCallParameters(
            tool_call_id="1", tool_name="test_tool", tool_input={"input": "test"}
        ),
        "Tool result with lots of content "
        * 100,  # Make it long enough to trigger truncation
    )
    dialog_no_budget.add_model_response([TextResult("Final response")])

    # Get messages - should not trigger truncation
    dialog_no_budget.get_messages_for_llm_client()
    assert len(dialog_no_budget.truncation_history_token_cts) == 0

    # Test with budgeting enabled
    dialog_with_budget = DialogMessages(use_prompt_budgeting=True)
    dialog_with_budget.token_budget_to_trigger_truncation = (
        1  # Set very low to force truncation
    )

    # Add same messages
    dialog_with_budget.add_user_prompt("Test message")
    dialog_with_budget.add_model_response([TextResult("Test response")])
    dialog_with_budget.add_tool_call_result(
        ToolCallParameters(
            tool_call_id="1", tool_name="test_tool", tool_input={"input": "test"}
        ),
        "Tool result with lots of content "
        * 100,  # Make it long enough to trigger truncation
    )
    dialog_with_budget.add_model_response([TextResult("Final response")])

    # Get messages - should trigger truncation
    dialog_with_budget.get_messages_for_llm_client()
    assert len(dialog_with_budget.truncation_history_token_cts) > 0


def test_tool_call_logger_json_output():
    """Test ToolCallLogger JSON file output functionality."""
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create temporary file paths
        json_log_file = Path(temp_dir) / "test_log.json"

        # Create a ToolCallLogger with JSON file output
        logger = ToolCallLogger(
            verbose=True,
            json_log_file=json_log_file,
        )

        # Create a mock tool
        mock_tool = MockTool("test_tool", "test_output")

        # Log a tool call
        logger.tool_call_started(
            called_tool=mock_tool,
            tool_input={"test_input": "test"},
            tool_started_message="Starting test tool",
        )

        logger.tool_call_ended(
            called_tool=mock_tool,
            tool_input={"test_input": "test"},
            tool_output="Test output",
            tool_result_message="Test tool completed",
            duration=0.5,
        )

        # Verify JSON file was created
        assert json_log_file.exists()

        # Read the JSON file
        with json_log_file.open("r") as f:
            json_data = json.load(f)

        # Verify JSON data structure
        assert isinstance(json_data, list)
        assert len(json_data) == 2  # One entry for start, one for end

        # Verify tool call start entry
        start_entry = json_data[0]
        assert start_entry["type"] == "tool_call"
        assert start_entry["started"] is True
        assert start_entry["tool_name"] == "test_tool"
        assert start_entry["tool_input"] == {"test_input": "test"}
        assert start_entry["tool_message"] == "Starting test tool"

        # Verify tool call end entry
        end_entry = json_data[1]
        assert end_entry["type"] == "tool_call"
        assert end_entry["started"] is False
        assert end_entry["tool_name"] == "test_tool"
        assert end_entry["tool_input"] == {"test_input": "test"}
        assert end_entry["tool_output"] == "Test output"
        assert end_entry["tool_message"] == "Test tool completed"
        assert end_entry["duration"] == 0.5


def test_tool_call_logger_json_llm_call():
    """Test ToolCallLogger JSON file output for LLM calls."""
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create temporary file paths
        json_log_file = Path(temp_dir) / "test_llm_log.json"

        # Create a ToolCallLogger with JSON file output
        logger = ToolCallLogger(
            verbose=True,
            json_log_file=json_log_file,
        )

        # Mock LLM call parameters
        messages_raw = [[TextPrompt(text="test prompt")]]
        # Cast to the expected type
        messages = cast(list[list[GeneralContentBlock]], messages_raw)
        max_tokens = 100
        system_prompt = "test system prompt"
        temperature = 0.7
        tools = []
        tool_choice = None

        # Log LLM call start
        logger.language_model_call_started(
            messages=messages,
            max_tokens=max_tokens,
            system_prompt=system_prompt,
            temperature=temperature,
            tools=tools,
            tool_choice=tool_choice,
        )

        # Log LLM call end
        response_raw = [TextResult(text="test response")]
        # Cast to the expected type
        response = cast(list[AssistantContentBlock], response_raw)
        response_metadata = {"metadata": "test"}

        logger.language_model_call_ended(
            messages=messages,
            max_tokens=max_tokens,
            system_prompt=system_prompt,
            temperature=temperature,
            tools=tools,
            tool_choice=tool_choice,
            response=response,
            response_metadata=response_metadata,
            duration=0.3,
        )

        # Verify JSON file was created
        assert json_log_file.exists()

        # Read the JSON file
        with json_log_file.open("r") as f:
            json_data = json.load(f)

        # Verify JSON data structure
        assert isinstance(json_data, list)
        assert len(json_data) == 2  # One entry for start, one for end

        # Verify LLM call start entry
        start_entry = json_data[0]
        assert start_entry["type"] == "llm_call"
        assert start_entry["started"] is True
        assert start_entry["max_tokens"] == max_tokens
        assert start_entry["system_prompt"] == system_prompt
        assert start_entry["temperature"] == temperature

        # Verify LLM call end entry
        end_entry = json_data[1]
        assert end_entry["type"] == "llm_call"
        assert end_entry["started"] is False
        assert end_entry["max_tokens"] == max_tokens
        assert end_entry["system_prompt"] == system_prompt
        assert end_entry["temperature"] == temperature
        assert end_entry["duration"] == 0.3
        assert end_entry["response_metadata"] == response_metadata


def test_tool_call_logger_finish_with_json():
    """Test ToolCallLogger finish method with JSON file output."""
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create temporary file paths
        json_log_file = Path(temp_dir) / "test_finish.json"

        # Create a ToolCallLogger with JSON file output
        logger = ToolCallLogger(
            verbose=True,
            json_log_file=json_log_file,
        )

        # Create a mock tool
        mock_tool = MockTool("test_tool", "test_output")

        # Log a tool call
        logger.tool_call_started(
            called_tool=mock_tool,
            tool_input={"test_input": "test"},
            tool_started_message="Starting test tool",
        )

        # Call finish method
        logger.finish()

        # Verify JSON file was created
        assert json_log_file.exists()

        # Read the JSON file
        with json_log_file.open("r") as f:
            json_data = json.load(f)

        # Verify JSON data structure
        assert isinstance(json_data, list)
        assert len(json_data) == 1  # Only the start entry
        assert json_data[0]["tool_name"] == "test_tool"

        # Log another tool call
        logger.tool_call_ended(
            called_tool=mock_tool,
            tool_input={"test_input": "test"},
            tool_output="Test output",
            tool_result_message="Test tool completed",
            duration=0.5,
        )

        # Call finish method again
        logger.finish()

        # Read the JSON file again
        with json_log_file.open("r") as f:
            json_data = json.load(f)

        # Verify JSON data structure was updated
        assert isinstance(json_data, list)
        assert len(json_data) == 2  # Now includes both start and end entries

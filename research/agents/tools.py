"""Tool definitions and utilities."""

import copy
import html
import json
import pickle
import re
import time
from collections.abc import Callable
from dataclasses import dataclass, field
from datetime import datetime
from functools import cached_property
from hashlib import sha256
from pathlib import Path
from typing import (
    Any,
    Dict,
    List,
    Optional,
    Protocol,
    Sequence,
    Tuple,
    Union,
    cast,
)

from research.agents.changed_file import ChangedFile

import dataclasses_json
import jsonschema
import numpy as np
from anthropic import BadRequestError
from termcolor import colored
from typing_extensions import final

from base.blob_names.python.blob_names import get_blob_name
from base.third_party_clients.token_counter.token_counter_claude import (
    <PERSON><PERSON><PERSON>Counter,
)
from research.llm_apis.llm_client import (
    AnthropicRedactedThinking<PERSON>lock,
    AnthropicThinking<PERSON>lock,
    ToolCall,
    ToolFormattedResult,
)
from research.llm_apis.llm_client import (
    AssistantContentBlock,
    GeneralContentBlock,
    LLMClient,
    LLMMessages,
    TextPrompt,
    TextResult,
    ToolParam,
)


class DialogMessagesUpdateListener(Protocol):
    """Interface for objects that want to be notified when a DialogMessages object is updated."""

    def on_update(self, dialog: "DialogMessages") -> None:
        """Called when the dialog is updated.

        Args:
            dialog: The updated dialog object
        """
        pass


class ToolCallLoggerUpdateListener(Protocol):
    """Interface for objects that want to be notified when a ToolCallLogger is updated."""

    def on_tool_call_logger_update(self, tool_call_logger: "ToolCallLogger") -> None:
        """Called when a ToolCallLogger is updated.

        Args:
            tool_call_logger: The updated ToolCallLogger object.
        """
        pass


# Custom JSON encoder that can handle datetime objects and other complex types
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        # Handle GeneralContentBlock objects (TextPrompt, TextResult, ToolCall, etc.)
        if hasattr(obj, "__class__") and obj.__class__.__name__ in [
            "TextPrompt",
            "TextResult",
            "ToolCall",
            "ToolFormattedResult",
            "GeneralContentBlock",
            "AssistantContentBlock",
            "TextBlock",
        ]:
            return {
                "__type__": obj.__class__.__name__,
                "attributes": {
                    k: v for k, v in obj.__dict__.items() if not k.startswith("_")
                },
            }
        # Handle DialogMessages objects
        if hasattr(obj, "__class__") and obj.__class__.__name__ == "DialogMessages":
            return {
                "__type__": "DialogMessages",
                "message_lists": getattr(obj, "_message_lists", []),
            }
        # Handle Message objects
        if hasattr(obj, "__class__") and obj.__class__.__name__ == "Message":
            return {
                "__type__": "Message",
                "attributes": {
                    k: v for k, v in obj.__dict__.items() if not k.startswith("_")
                },
            }
        # Handle Usage objects
        if hasattr(obj, "__class__") and obj.__class__.__name__ == "Usage":
            return {
                "__type__": "Usage",
                "attributes": {
                    k: v for k, v in obj.__dict__.items() if not k.startswith("_")
                },
            }
        # Handle ToolUseBlock objects
        if hasattr(obj, "__class__") and obj.__class__.__name__ == "ToolUseBlock":
            return {
                "__type__": "ToolUseBlock",
                "id": getattr(obj, "id", ""),
                "input": getattr(obj, "input", {}),
                "name": getattr(obj, "name", ""),
                "type": getattr(obj, "type", "tool_use"),
            }
        return super().default(obj)


ToolInputSchema = dict[str, Any]
"""A JSON schema describing the input to a tool."""


RIGHT = ""  # "▶"
LEFT = ""  # "◀"


@dataclass
class ToolCallParameters:
    tool_call_id: str
    tool_name: str
    tool_input: Any


@dataclass
class ToolImplOutput:
    """Output from an LLM tool implementation.

    Attributes:
        tool_output: The main output string that will be shown to the model.
        tool_result_message: A description of what the tool did, for logging purposes.
        auxiliary_data: Additional data that the tool wants to pass along for logging only.
    """

    tool_output: str
    tool_result_message: str
    auxiliary_data: dict[str, Any] = field(default_factory=dict)


class DialogMessages:
    """Keeps track of messages that compose a dialog.

    A dialog alternates between user and assistant turns. Each turn consists
    of one or more messages, represented by GeneralContentBlock.

    A user turn consists of one or more prompts and tool results.
    An assistant turn consists of a model answer and tool calls.
    """

    def __init__(
        self,
        use_prompt_budgeting: bool = False,
        update_listener: Optional[DialogMessagesUpdateListener] = None,
    ):
        self._message_lists: LLMMessages = []
        self.token_counter = ClaudeTokenCounter()
        self.use_prompt_budgeting = use_prompt_budgeting
        self.truncation_history_token_cts: list[int] = []
        # c-flaherty: This trigger for token budgeting is chosen because of the following stat Lior computed:
        # "With 120k, you'll get 99.9% chance of not exceeding the context size"
        self.token_budget_to_trigger_truncation = 120_000
        # c-flaherty: This is the number of turns we don't truncate. We can experiment with it.
        self.truncate_all_but_N = 3
        self._update_listener = update_listener
        # Map from turn index to list of changed files
        self._changed_files: Dict[int, List[ChangedFile]] = {}

    def __getstate__(self):
        """Return state values to be pickled."""
        state = self.__dict__.copy()
        # Don't pickle the update listener
        if "_update_listener" in state:
            del state["_update_listener"]
        return state

    @property
    def update_listener(self) -> Optional[DialogMessagesUpdateListener]:
        """Get the update listener."""
        return getattr(self, "_update_listener", None)

    @update_listener.setter
    def update_listener(self, listener: Optional[DialogMessagesUpdateListener]):
        """Set the update listener."""
        self._update_listener = listener

    def save_messages(self, path: Path) -> None:
        """Save the dialog to a file.

        Args:
            path: Path to save the dialog to
        """
        # Ensure the parent directory exists
        path.parent.mkdir(parents=True, exist_ok=True)

        # Save the entire object to the file
        with open(path, "wb") as f:
            pickle.dump(self, f)

    @classmethod
    def from_saved_messages(
        cls, path: Path, use_prompt_budgeting: bool = False
    ) -> "DialogMessages":
        """Create a DialogMessages object from a saved file.

        Args:
            path: Path to load the dialog from
            use_prompt_budgeting: Whether to use prompt budgeting (will override the saved value)

        Returns:
            A loaded DialogMessages object
        """
        # Load the object from the file
        with open(path, "rb") as f:
            dialog = pickle.load(f)

        # Set use_prompt_budgeting if specified
        if use_prompt_budgeting:
            dialog.use_prompt_budgeting = use_prompt_budgeting

        return dialog

    def generate_summary_statistics(self) -> str:
        """Count the total number of tokens in the dialog.
        Returns:
            A dictionary with token counts for different message types and total.
        """
        token_counts = {
            "total_tokens": 0,
            "total_turns": len(self._message_lists),
            "text_prompt_tokens": [],
            "text_result_tokens": [],
            "tool_formatted_result_tokens": [],
            "tool_call_tokens": [],
            "truncation_history_token_cts": self.truncation_history_token_cts,
            # tool formatted results
            "read_file_tokens": [],
            "launch_process_tokens": [],
            "other_tool_tokens": [],
            "bash_tokens": [],
            "str_replace_editor_tokens": [],
        }

        for message_list in self._message_lists:
            for message in message_list:
                if isinstance(message, TextPrompt):
                    tokens = self.token_counter.count_tokens(message.text)
                    token_counts["text_prompt_tokens"].append(tokens)
                    token_counts["total_tokens"] += tokens
                elif isinstance(message, TextResult):
                    tokens = self.token_counter.count_tokens(message.text)
                    token_counts["text_result_tokens"].append(tokens)
                    token_counts["total_tokens"] += tokens
                elif isinstance(message, ToolFormattedResult):
                    tokens = self.token_counter.count_tokens(message.tool_output)
                    token_counts["tool_formatted_result_tokens"].append(tokens)
                    token_counts["total_tokens"] += tokens

                    # c-flaherty: This is temporary code so we break in the tools abstraction.
                    if message.tool_name == "read_file":
                        token_counts["read_file_tokens"].append(tokens)
                    elif message.tool_name == "launch_process":
                        token_counts["launch_process_tokens"].append(tokens)
                    elif message.tool_name == "bash":
                        token_counts["bash_tokens"].append(tokens)
                    elif message.tool_name == "str_replace_editor":
                        token_counts["str_replace_editor_tokens"].append(tokens)
                    else:
                        token_counts["other_tool_tokens"].append(tokens)
                elif isinstance(message, ToolCall):
                    tokens = self.token_counter.count_tokens(
                        json.dumps(message.tool_input)
                    )
                    token_counts["tool_call_tokens"].append(tokens)
                    token_counts["total_tokens"] += tokens

        # generate summary stats for each list in token_counts
        for key, value in token_counts.items():
            if isinstance(value, list):
                if len(value) == 0:
                    token_counts[key] = "No values found."
                else:
                    token_counts[key] = {
                        "num_items": len(value),
                        "sum_tokens": sum(value),
                        "min": min(value),
                        "all_values": value,
                        "median": np.median(value),
                        "max": max(value),
                    }

        return json.dumps(token_counts, indent=2)

    def add_user_prompt(
        self, message: str, allow_append_to_tool_call_results: bool = False
    ):
        """Add a user prompt to the dialog.

        Args:
            message: The message to add.
            allow_append_to_tool_call_results: If True, and if the last message
                is a tool call result, then the message will be appended to that
                turn.
        """
        if self.is_user_turn():
            self._message_lists.append([TextPrompt(message)])
            self._notify_update()
        else:
            if allow_append_to_tool_call_results:
                user_messages = self._message_lists[-1]
                for user_message in user_messages:
                    if isinstance(user_message, TextPrompt):
                        raise ValueError(
                            f"Last user turn already contains a text prompt: {user_message}"
                        )
                user_messages.append(TextPrompt(message))
                self._notify_update()
            else:
                self._assert_user_turn()

    def add_tool_call_result(
        self,
        parameters: ToolCallParameters,
        result: str,
        changed_files: Optional[List[ChangedFile]] = None,
    ):
        """Add the result of a tool call to the dialog.

        Args:
            parameters: The parameters of the tool call.
            result: The result of the tool call.
            changed_files: Optional list of files that were changed by the tool call.
        """
        # Add the tool call result to the dialog
        self._assert_user_turn()

        # Add the tool call result to the dialog
        messages = [
            ToolFormattedResult(
                tool_call_id=parameters.tool_call_id,
                tool_name=parameters.tool_name,
                tool_output=result,
            )
        ]
        # Cast to list[GeneralContentBlock] to satisfy type checker
        self._message_lists.append(cast(List[GeneralContentBlock], messages))

        # Add workspace changes if provided
        if changed_files:
            self.add_workspace_changes(changed_files)

        self._notify_update()

    def add_tool_call_results(
        self,
        parameters: list[ToolCallParameters],
        results: list[str],
    ):
        """Add the result of a tool call to the dialog.

        Args:
            parameters: The parameters of the tool calls.
            results: The results of the tool calls.
        """
        self._assert_user_turn()

        # Add the tool call results to the dialog
        messages = [
            ToolFormattedResult(
                tool_call_id=params.tool_call_id,
                tool_name=params.tool_name,
                tool_output=result,
            )
            for params, result in zip(parameters, results)
        ]
        # Cast to list[GeneralContentBlock] to satisfy type checker
        self._message_lists.append(cast(List[GeneralContentBlock], messages))

        self._notify_update()

    def add_workspace_changes(self, changed_files: List[ChangedFile]) -> None:
        """Add workspace changes to the current turn.

        Args:
            changed_files: List of files that were changed.
        """
        # Get the current turn index
        turn_index = len(self._message_lists) - 1

        # Add the changed files to the current turn
        if turn_index not in self._changed_files:
            self._changed_files[turn_index] = []

        self._changed_files[turn_index].extend(changed_files)

        # Notify the update listener
        self._notify_update()

    def get_workspace_changes(self, turn_index: int) -> List[ChangedFile]:
        """Get the workspace changes for a specific turn.

        Args:
            turn_index: The index of the turn to get changes for.

        Returns:
            List of changed files for the specified turn.
        """
        return self._changed_files.get(turn_index, [])

    def get_all_workspace_changes(self) -> Dict[int, List[ChangedFile]]:
        """Get all workspace changes.

        Returns:
            Dictionary mapping turn indices to lists of changed files.
        """
        return self._changed_files.copy()

    def add_model_response(self, response: list[AssistantContentBlock]):
        """Add the result of a model call to the dialog."""
        self._assert_assistant_turn()
        self._message_lists.append(cast(list[GeneralContentBlock], response))
        self._notify_update()

    def count_tokens(self) -> int:
        """Count the total number of tokens in the dialog."""
        total_tokens = 0
        for i, message_list in enumerate(self._message_lists):
            is_last_message_list = i == len(self._message_lists) - 1
            for message in message_list:
                if isinstance(message, (TextPrompt, TextResult)):
                    total_tokens += self.token_counter.count_tokens(message.text)
                elif isinstance(message, ToolFormattedResult):
                    total_tokens += self.token_counter.count_tokens(message.tool_output)
                elif isinstance(message, ToolCall):
                    total_tokens += self.token_counter.count_tokens(
                        json.dumps(message.tool_input)
                    )
                elif isinstance(message, AnthropicRedactedThinkingBlock):
                    total_tokens += 0
                elif isinstance(message, AnthropicThinkingBlock):
                    total_tokens += (
                        self.token_counter.count_tokens(message.thinking)
                        if is_last_message_list
                        else 0
                    )
                else:
                    raise ValueError(f"Unknown message type: {type(message)}")
        return total_tokens

    def run_truncation_strategy(self) -> None:
        """Truncate all the tool results apart from the last N turns."""

        print(
            colored(
                f" [dialog_messages] Truncating all but the last {self.truncate_all_but_N} turns as we hit the token budget {self.token_budget_to_trigger_truncation}.",
                "yellow",
            )
        )

        old_token_ct = self.count_tokens()

        new_message_lists: LLMMessages = copy.deepcopy(self._message_lists)

        for message_list in new_message_lists[: -self.truncate_all_but_N]:
            for message in message_list:
                if isinstance(message, ToolFormattedResult):
                    message.tool_output = (
                        "[Truncated...re-run tool if you need to see output again.]"
                    )
                elif isinstance(message, ToolCall):
                    if message.tool_name == "sequential_thinking":
                        message.tool_input["thought"] = (
                            "[Truncated...re-run tool if you need to see input/output again.]"
                        )
                    elif message.tool_name == "str_replace_editor":
                        if "file_text" in message.tool_input:
                            message.tool_input["file_text"] = (
                                "[Truncated...re-run tool if you need to see input/output again.]"
                            )
                        if "old_str" in message.tool_input:
                            message.tool_input["old_str"] = (
                                "[Truncated...re-run tool if you need to see input/output again.]"
                            )
                        if "new_str" in message.tool_input:
                            message.tool_input["new_str"] = (
                                "[Truncated...re-run tool if you need to see input/output again.]"
                            )

        self._message_lists = new_message_lists
        self._notify_update()

        new_token_ct = self.count_tokens()
        print(
            colored(
                f" [dialog_messages] Token count after truncation: {new_token_ct}",
                "yellow",
            )
        )

        self.truncation_history_token_cts.append(old_token_ct - new_token_ct)

    def get_messages_for_llm_client(self) -> LLMMessages:
        """Returns messages in the format the LM client expects."""

        if (
            self.use_prompt_budgeting
            and self.count_tokens() > self.token_budget_to_trigger_truncation
        ):
            self.run_truncation_strategy()

        return list(self._message_lists)

    def drop_final_assistant_turn(self):
        """Remove the final assistant turn.

        This allows dialog messages to be passed to tools as they are called,
        without containing the final tool call.
        """
        if self.is_user_turn():
            self._message_lists.pop()
            self._notify_update()

    def drop_tool_calls_from_final_turn(self):
        """Remove tool calls from the final assistant turn.

        This allows dialog messages to be passed to tools as they are called,
        without containing the final tool call.
        """
        if self.is_user_turn():
            new_turn_messages = [
                message
                for message in self._message_lists[-1]
                if not isinstance(message, ToolCall)
            ]
            self._message_lists[-1] = cast(list[GeneralContentBlock], new_turn_messages)
            self._notify_update()

    def get_pending_tool_calls(self) -> list[ToolCallParameters]:
        """Returns the tool calls from the last assistant turn.

        Returns an empty list of no tool calls are pending.
        """
        self._assert_user_turn()
        if len(self._message_lists) == 0:
            return []
        tool_calls = []
        for message in self._message_lists[-1]:
            if isinstance(message, ToolCall):
                tool_calls.append(
                    ToolCallParameters(
                        tool_call_id=message.tool_call_id,
                        tool_name=message.tool_name,
                        tool_input=message.tool_input,
                    )
                )
        return tool_calls

    def get_last_model_text_response(self):
        """Returns the last model response as a string."""
        self._assert_user_turn()
        for message in self._message_lists[-1]:
            if isinstance(message, TextResult):
                return message.text
        raise ValueError("No text response found in last model response")

    def get_last_user_prompt(self) -> str:
        """Returns the last user prompt."""
        self._assert_assistant_turn()
        for message in self._message_lists[-1]:
            if isinstance(message, TextPrompt):
                return message.text
        raise ValueError("No text prompt found in last user prompt")

    def replace_last_user_prompt(self, new_prompt: str):
        """Replace the last user prompt with a new one."""
        self._assert_assistant_turn()
        for i, message in enumerate(self._message_lists[-1]):
            if isinstance(message, TextPrompt):
                self._message_lists[-1][i] = TextPrompt(new_prompt)
                return
        raise ValueError("No text prompt found in last user prompt")

    def _notify_update(self):
        """Notify the update listener if one is set."""
        if self.update_listener is not None:
            self.update_listener.on_update(self)

    def clear(self):
        """Delete all messages."""
        self._message_lists = []
        self._notify_update()

    def is_user_turn(self):
        return len(self._message_lists) % 2 == 0

    def is_assistant_turn(self):
        return len(self._message_lists) % 2 == 1

    def __str__(self) -> str:
        json_serializable = [
            [message.to_dict() for message in message_list]
            for message_list in self._message_lists
        ]
        return json.dumps(json_serializable, indent=2)

    def get_summary(self, max_str_len: int = 100) -> str:
        """Returns a summary of the dialog."""

        def truncate_strings(obj):
            # Truncate all leaf strings to 100 characters
            if isinstance(obj, str):
                if len(obj) > max_str_len:
                    return obj[:max_str_len] + "..."
            elif isinstance(obj, dict):
                return {k: truncate_strings(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [truncate_strings(item) for item in obj]
            return obj

        json_serializable = truncate_strings(
            [
                [message.to_dict() for message in message_list]
                for message_list in self._message_lists
            ]
        )
        return json.dumps(json_serializable, indent=2)

    def _assert_user_turn(self):
        assert self.is_user_turn(), "Can only add user prompts on user's turn"

    def _assert_assistant_turn(self):
        assert (
            self.is_assistant_turn()
        ), "Can only get/replace last user prompt on assistant's turn"


class Tool:
    """A tool that can be called by an LLM.

    A general tool may require additional parameters that the model does not
    provide. It may also return arbitrary structured output. Therefore, a
    general tool does not have a well-defined interface for calling it.
    """

    name: str
    description: str
    input_schema: ToolInputSchema


class LLMTool:
    """A tool that fits into the standard LLM tool-calling paradigm.

    An LLM tool can be called by supplying the parameters specified in its
    input_schema, and returns a string that is then shown to the model.
    """

    name: str
    description: str
    input_schema: ToolInputSchema

    def __init__(self, tool_call_logger: "ToolCallLogger"):
        self.tool_call_logger = tool_call_logger

    @property
    def should_stop(self) -> bool:
        """Whether the tool wants to stop the current agentic run."""
        return False

    # Final is here to indicate that subclasses should override run_impl(), not
    # run(). There may be a reason in the future to override run() itself, and
    # if such a reason comes up, this @final decorator can be removed.
    @final
    def run(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> str:
        """Run the tool.

        Args:
            tool_input: The input to the tool.
            dialog_messages: The dialog messages so far, if available. The tool
                is allowed to modify this object, so the caller should make a copy
                if that's not desired. The dialog messages should not contain
                pending tool calls. They should end where it's the user's turn.
        """
        if dialog_messages:
            assert dialog_messages.is_user_turn()

        self.tool_call_logger.tool_call_started(
            called_tool=self,
            tool_input=tool_input,
            tool_started_message=self.get_tool_start_message(tool_input),
        )
        start_time = time.time()

        auxiliary_data = {}
        try:
            self._validate_tool_input(tool_input)
            result = self.run_impl(tool_input, dialog_messages)
            tool_output = result.tool_output
            tool_result_message = result.tool_result_message
            auxiliary_data = result.auxiliary_data
        except jsonschema.ValidationError as exc:
            tool_output = "Invalid tool input: " + exc.message
            tool_result_message = tool_output
        except BadRequestError as exc:
            raise RuntimeError("Bad request: " + exc.message)

        end_time = time.time()
        self.tool_call_logger.tool_call_ended(
            called_tool=self,
            tool_input=tool_input,
            tool_output=tool_output,
            tool_result_message=tool_result_message,
            auxiliary_data=auxiliary_data,
            duration=end_time - start_time,
        )
        return tool_output

    def get_tool_start_message(self, tool_input: ToolInputSchema) -> str:
        """Return a user-friendly message to be shown to the model when the tool is called."""
        return f"Calling tool '{self.name}'"

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Subclasses should implement this.

        Returns:
            A ToolImplOutput containing the output string, description, and any auxiliary data.
        """
        raise NotImplementedError()

    def get_tool_param(self) -> ToolParam:
        return ToolParam(
            name=self.name,
            description=self.description,
            input_schema=self.input_schema,
        )

    def _validate_tool_input(self, tool_input: dict[str, Any]):
        """Validates the tool input.

        Raises:
            jsonschema.ValidationError: If the tool input is invalid.
        """
        jsonschema.validate(instance=tool_input, schema=self.input_schema)


@dataclass
class LoggedToolCall(dataclasses_json.DataClassJsonMixin):
    started: bool
    """True if this is the beginning of a call, False if it is the end."""

    tool: ToolParam
    """Which tool is called."""

    tool_input: ToolInputSchema
    """The input to the tool."""

    tool_output: Optional[str]
    """The output of the tool (only relevant if started=False)."""

    tool_message: str
    """A user-friendly message describing the call state."""

    auxiliary_data: dict[str, Any] = field(default_factory=dict)
    """Arbitrary data attached to the call."""

    timestamp: datetime = field(default_factory=datetime.now)
    """When this tool call was logged."""

    duration: Optional[float] = None
    """The duration of the tool call in seconds, should only be non-None if started=False."""

    def __post_init__(self):
        if self.started and self.duration is not None:
            raise ValueError("duration should be None if started=True")

    def __str__(self):
        return json.dumps(
            {
                "started": self.started,
                "tool_name": self.tool.name,
                "tool_input": self.tool_input,
                "tool_output": self.tool_output,
                "tool_message": self.tool_message,
            },
            indent=2,
        )


@dataclass
class LoggedWorkspaceChanges(dataclasses_json.DataClassJsonMixin):
    """A record of changes made to the workspace."""

    changed_files: List[ChangedFile]
    """Files that were changed."""

    timestamp: datetime = field(default_factory=datetime.now)
    """When these changes were logged."""

    description: str = ""
    """Optional description of the changes."""

    def __str__(self):
        return json.dumps(
            {
                "timestamp": self.timestamp.isoformat(),
                "description": self.description,
                "changed_files_count": len(self.changed_files),
            },
            indent=2,
        )


@dataclass
class LoggedLanguageModelCall(dataclasses_json.DataClassJsonMixin):
    started: bool
    messages: LLMMessages
    max_tokens: int
    system_prompt: Optional[str]
    temperature: float
    tools: list[ToolParam]
    tool_choice: Optional[dict[str, str]]

    response: Optional[list[AssistantContentBlock]]
    response_metadata: Optional[dict[str, Any]]

    timestamp: datetime = field(default_factory=datetime.now)
    """When this language model call was logged."""

    duration: Optional[float] = None
    """The duration of the language model call in seconds, should only be non-None if started=False."""

    def __post_init__(self):
        if self.started and self.duration is not None:
            raise ValueError("duration should be None if started=True")

    @cached_property
    def all_messages(
        self,
    ) -> list[
        list[GeneralContentBlock] | list[AssistantContentBlock] | str | ToolParam
    ]:
        """Returns all messages in the call, including system prompt and tools.

        This is used for computing the prefix hashes of the call.
        """
        all_messages = [
            self.system_prompt or "",
            *self.tools,
            *self.messages,
        ]
        if self.response:
            all_messages.append(self.response)
        return all_messages

    @cached_property
    def all_hashes(self):
        """Returns the cumulative hashes of all_messages."""
        hasher = sha256()
        hashes: list[str] = []
        for message in self.all_messages:
            if isinstance(message, list):
                for block in message:
                    hasher.update(block.to_json().encode())
            elif isinstance(message, ToolParam):
                hasher.update(message.to_json().encode())
            else:
                hasher.update(message.encode())
            hashes.append(hasher.hexdigest())
        return hashes


class ToolCallLogger:
    def __init__(
        self,
        verbose: bool = False,
        verbose_llm_calls: bool = False,
        use_tool_supplied_messages: bool = False,
        log_file: Optional[Path] = None,
        pickle_log_file: Optional[Path] = None,
        html_log_file: Optional[Path] = None,
        json_log_file: Optional[Path] = None,
        update_listeners: Optional[List[Any]] = None,
    ):
        """Initialize a ToolCallLogger.

        Args:
            verbose: If True, shows all tool calls and their results. If False, only shows
                important messages and errors.
            verbose_llm_calls: If True, shows all LLM inputs and outputs during tool calls.
                If False, hides these for cleaner output. Only applies when verbose is True.
            use_tool_supplied_messages: Whether to use tool-supplied messages for logging.
            log_file: Optional path to write logs to a text file.
            pickle_log_file: Optional path to write logs to a pickle file.
            html_log_file: Optional path to write logs to an HTML file.
            json_log_file: Optional path to write logs to a JSON file.
            update_listeners: Optional list of listeners to notify when the logger is updated.
                Listeners must implement the ToolCallLoggerUpdateListener interface.
        """
        self.logged_calls: list[
            LoggedToolCall | LoggedLanguageModelCall | LoggedWorkspaceChanges
        ] = []
        self.verbose = verbose
        self.verbose_llm_calls = verbose_llm_calls
        self.use_tool_supplied_messages = use_tool_supplied_messages
        self.log_file = log_file
        self.pickle_log_file = pickle_log_file
        self.html_log_file = html_log_file
        self.json_log_file = json_log_file
        self.update_listeners = update_listeners or []

    @staticmethod
    def from_pickle_file(pickle_log_file: Path) -> "ToolCallLogger":
        logged_calls = pickle.load(pickle_log_file.open("rb"))
        logger = ToolCallLogger()
        logger.logged_calls = logged_calls
        return logger

    def tool_call_started(
        self,
        called_tool: LLMTool,
        tool_input: ToolInputSchema,
        tool_started_message: str,
        auxiliary_data: Optional[dict[str, Any]] = None,
    ):
        tool_info = ToolParam(
            name=called_tool.name,
            description=called_tool.description,
            input_schema=called_tool.input_schema,
        )
        self.logged_calls.append(
            LoggedToolCall(
                started=True,
                tool=tool_info,
                tool_input=tool_input,
                tool_output=None,
                auxiliary_data=auxiliary_data or {},
                tool_message=tool_started_message,
                duration=None,
            )
        )
        self._log_to_file()
        if self.verbose:
            self._print_last_call()

    def tool_call_ended(
        self,
        called_tool: LLMTool,
        tool_input: ToolInputSchema,
        tool_output: str,
        tool_result_message: str,
        auxiliary_data: Optional[dict[str, Any]] = None,
        duration: Optional[float] = None,
    ):
        tool_info = ToolParam(
            name=called_tool.name,
            description=called_tool.description,
            input_schema=called_tool.input_schema,
        )
        self.logged_calls.append(
            LoggedToolCall(
                started=False,
                tool=tool_info,
                tool_input=tool_input,
                tool_output=tool_output,
                auxiliary_data=auxiliary_data or {},
                tool_message=tool_result_message,
                duration=duration,
            )
        )

        self._log_to_file()
        if self.verbose:
            self._print_last_call()

    def language_model_call_started(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None,
        temperature: float,
        tools: list[ToolParam],
        tool_choice: dict[str, str] | None,
    ):
        self.logged_calls.append(
            LoggedLanguageModelCall(
                started=True,
                messages=messages,
                max_tokens=max_tokens,
                system_prompt=system_prompt,
                temperature=temperature,
                tools=tools,
                tool_choice=tool_choice,
                response=None,
                response_metadata=None,
            )
        )
        self._log_to_file()
        if self.verbose:
            self._print_last_call()

    def language_model_call_ended(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None,
        temperature: float,
        tools: list[ToolParam],
        tool_choice: dict[str, str] | None,
        response: list[AssistantContentBlock],
        response_metadata: dict[str, Any],
        duration: Optional[float] = None,
    ):
        self.logged_calls.append(
            LoggedLanguageModelCall(
                started=False,
                messages=messages,
                max_tokens=max_tokens,
                system_prompt=system_prompt,
                temperature=temperature,
                tools=tools,
                tool_choice=tool_choice,
                response=response,
                response_metadata=response_metadata,
                duration=duration,
            )
        )
        self._log_to_file()
        if self.verbose:
            self._print_last_call()

    def workspace_changed(
        self,
        changed_files: List[ChangedFile],
        description: str = "",
    ):
        """Log changes to the workspace.

        Args:
            changed_files: List of files that were changed.
            description: Optional description of the changes.
        """
        self.logged_calls.append(
            LoggedWorkspaceChanges(
                changed_files=changed_files,
                description=description,
            )
        )
        self._log_to_file()
        if self.verbose:
            self._print_last_call()

    def _truncate_for_printing(
        self, text: Optional[str], max_len: Optional[int] = 1000
    ) -> Optional[str]:
        if not text:
            return text
        if max_len and len(text) > max_len:
            return text[:max_len] + "..."
        return text

    def _log_with_retries(self, fn: Callable[[], None], num_retries: int = 3) -> bool:
        retries_left = num_retries
        while retries_left:
            try:
                fn()
                break
            except InterruptedError:
                print(f"Caught Interrupted exception, {retries_left} retries left")
            retries_left -= 1
            time.sleep(0.5)  # Wait for other processes to finish writing

        return retries_left > 0

    def _get_json_serializable_calls(self) -> list[dict]:
        """Convert logged calls to a JSON-serializable format."""

        json_calls = []
        for call in self.logged_calls:
            if isinstance(call, LoggedToolCall):
                # Convert to dict and then use the custom encoder to handle datetime and other complex types
                call_dict_str = json.dumps(call.to_dict(), cls=CustomJSONEncoder)
                call_dict = json.loads(call_dict_str)

                # Add type field for distinguishing between tool calls and LLM calls
                call_dict["type"] = "tool_call"

                # Add tool name and description for easier access
                call_dict["tool_name"] = call.tool.name
                call_dict["tool_description"] = call.tool.description

                json_calls.append(call_dict)
            elif isinstance(call, LoggedLanguageModelCall):
                # Convert to dict and then use the custom encoder to handle datetime and other complex types
                call_dict_str = json.dumps(call.to_dict(), cls=CustomJSONEncoder)
                call_dict = json.loads(call_dict_str)

                # Add type field for distinguishing between tool calls and LLM calls
                call_dict["type"] = "llm_call"

                json_calls.append(call_dict)
            elif isinstance(call, LoggedWorkspaceChanges):
                # Convert to dict and then use the custom encoder to handle datetime and other complex types
                call_dict_str = json.dumps(call.to_dict(), cls=CustomJSONEncoder)
                call_dict = json.loads(call_dict_str)

                # Add type field for distinguishing between different call types
                call_dict["type"] = "workspace_changes"

                json_calls.append(call_dict)

        return json_calls

    def _log_to_file(self, num_retries: int = 3):
        if self.log_file:

            def write_log_file():
                assert self.log_file is not None
                self.log_file.parent.mkdir(parents=True, exist_ok=True)
                # For the log file, write with full verbosity.
                self.log_file.write_text(
                    self.get_string_representation(
                        truncate_long_outputs=False,
                        verbose_llm_calls=True,
                        vv_llm_calls=True,
                    )
                )

            if not self._log_with_retries(write_log_file, num_retries):
                raise RuntimeError(f"Failed to write to {self.log_file}")

        if self.pickle_log_file:

            def write_pickle_log_file():
                assert self.pickle_log_file is not None
                self.pickle_log_file.parent.mkdir(parents=True, exist_ok=True)
                with self.pickle_log_file.open("wb") as f:
                    pickle.dump(self.logged_calls, f)

            if not self._log_with_retries(write_pickle_log_file, num_retries):
                raise RuntimeError(f"Failed to write to {self.pickle_log_file}")

        if self.json_log_file:

            def write_json_log_file():
                assert self.json_log_file is not None
                self.json_log_file.parent.mkdir(parents=True, exist_ok=True)
                json_data = self._get_json_serializable_calls()

                # Use the module-level CustomJSONEncoder

                with self.json_log_file.open("w") as f:
                    json.dump(json_data, f, indent=2, cls=CustomJSONEncoder)

            if not self._log_with_retries(write_json_log_file, num_retries):
                raise RuntimeError(f"Failed to write to {self.json_log_file}")

        # Notify listeners
        self._notify_listeners()

    def _notify_listeners(self):
        """Notify all update listeners that the logger has been updated."""
        for listener in self.update_listeners:
            listener.on_tool_call_logger_update(self)

    def add_update_listener(self, listener: Any) -> None:
        """Add a listener to be notified when the logger is updated.

        Args:
            listener: An object that implements the ToolCallLoggerUpdateListener interface.
        """
        if listener not in self.update_listeners:
            self.update_listeners.append(listener)

    def remove_update_listener(self, listener: Any) -> None:
        """Remove a listener from the list of listeners.

        Args:
            listener: The listener to remove.
        """
        if listener in self.update_listeners:
            self.update_listeners.remove(listener)

    def _print_last_call(self):
        print(
            self.get_string_representation(
                len(self.logged_calls) - 1,
                use_tool_supplied_messages=self.use_tool_supplied_messages,
                verbose_llm_calls=self.verbose_llm_calls,
            )
        )

    @staticmethod
    def _add_llm_call_details(
        call: LoggedLanguageModelCall,
        i: int,
        append_result: Callable[[int, str], None],
        seen_llm_calls: set[str],
    ):
        """Add details about the LLM call to the log, dedupes from existing calls by hash."""
        # Find the last cached message
        for j in range(len(call.all_messages) - 1, -1, -1):
            if call.all_hashes[j] in seen_llm_calls:
                break
        else:
            j = -1

        # Log all messages after the last cached message
        cur_hash = call.all_hashes[-1] if call.all_hashes else ""
        last_hash = call.all_hashes[j] if j >= 0 else ""
        seen_llm_calls.add(cur_hash)
        append_result(i, f"{'-' * 6}\nLLM call details:")
        append_result(i, f'from: "{last_hash}", to "{cur_hash}":')
        for message in call.all_messages[j + 1 :]:
            if isinstance(message, str):
                append_result(i, f"{'-' * 6}\nSystem prompt:\n{message}")
                continue

            if isinstance(message, ToolParam):
                append_result(i, f"{'-' * 6}\nTool:")
                append_result(i, f"{message.name}: {message.description}")
                continue

            for block in message:
                if isinstance(block, TextPrompt):
                    append_result(i, f"{'-' * 6}\nUser prompt:\n{block.text}")
                elif isinstance(block, TextResult):
                    append_result(i, f"{'-' * 6}\nLLM response:\n{block.text}")
                elif isinstance(block, ToolCall):
                    append_result(
                        i,
                        f"{'-' * 6}\nTool call:\n{block.tool_name}:{block.tool_call_id}",
                    )
                elif isinstance(block, ToolFormattedResult):
                    append_result(
                        i,
                        f"{'-' * 6}\nTool result:\n{block.tool_name}:{block.tool_call_id}",
                    )
                elif isinstance(block, AnthropicRedactedThinkingBlock):
                    append_result(i, f"{'-' * 6}\n[redacted thinking]")
                elif isinstance(block, AnthropicThinkingBlock):
                    append_result(i, f"{'-' * 6}\n[thinking]: {block.thinking}")
                else:
                    append_result(i, f"{'-' * 6}\nUnknown message block type: {block}")

    def get_string_representation(
        self,
        call_index: Optional[int] = None,
        truncate_long_outputs: bool = True,
        use_tool_supplied_messages: bool = False,
        verbose_llm_calls: bool = False,
        vv_llm_calls: bool = False,
    ) -> str:
        """Return a tree representation of the tool calls."""
        verbose_llm_calls = verbose_llm_calls or vv_llm_calls
        result = []
        indent_amount = 0
        active_tools = []

        GREEN = "\033[92m"
        CYAN = "\033[96m"
        DEFAULT = "\033[0m"

        def indent(text):
            lines = text.splitlines()
            for i, line in enumerate(lines):
                lines[i] = "    " * indent_amount + line
            return "\n".join(lines)

        def truncate(text):
            if truncate_long_outputs:
                return self._truncate_for_printing(text)
            return text

        def append_result(idx: int, text: str, use_color: bool = False):
            if call_index and idx != call_index:
                return
            if use_color:
                text = GREEN + text + DEFAULT
            result.append(indent(text))

        seen_llm_calls = set[str]()
        for i, call in enumerate(self.logged_calls):
            # Workspace changes
            if isinstance(call, LoggedWorkspaceChanges):
                # Display workspace changes
                if call.description:
                    append_result(i, f"Workspace changes: {call.description}", True)
                else:
                    append_result(i, "Workspace changes:", True)

                append_result(i, f"Changed files: {len(call.changed_files)} files")
                for changed_file in call.changed_files:
                    change_type = changed_file.change_type

                    if change_type == "ADDED":
                        append_result(i, f"  ADDED: {changed_file.new_path}")
                    elif change_type == "DELETED":
                        append_result(i, f"  DELETED: {changed_file.old_path}")
                    elif change_type == "RENAMED":
                        append_result(
                            i,
                            f"  RENAMED: {changed_file.old_path} -> {changed_file.new_path}",
                        )
                    else:  # MODIFIED
                        append_result(i, f"  MODIFIED: {changed_file.new_path}")
                continue

            # LM call
            elif isinstance(call, LoggedLanguageModelCall):
                if call.started:
                    message_text = ""
                    for message in call.messages[-1]:
                        if isinstance(message, TextPrompt):
                            message_text = message.text

                    if message_text and verbose_llm_calls and not vv_llm_calls:
                        log_message = f"{RIGHT} calling llm: {truncate(message_text)}"
                    else:
                        # LM called just with tool output, no user prompt
                        log_message = f"{RIGHT} calling llm"

                    append_result(i, log_message, True)
                    if vv_llm_calls:
                        self._add_llm_call_details(
                            call, i, append_result, seen_llm_calls
                        )

                else:
                    if call.duration is None:
                        duration_str = "unknown"
                    else:
                        minutes, seconds = divmod(call.duration or 0, 60)
                        duration_str = f"{int(minutes)}m {seconds:.1f}s"

                    if verbose_llm_calls and not vv_llm_calls:
                        result_text = f"{LEFT} llm response (duration: {duration_str}):"
                    else:
                        result_text = (
                            f"{LEFT} llm responded (duration: {duration_str}):"
                        )
                    if call.response_metadata:
                        try:
                            token_usage_str = (
                                f" (input_tokens: {call.response_metadata['input_tokens']}, "
                                f"output_tokens: {call.response_metadata['output_tokens']}, "
                                f"cache_creation_input_tokens: {call.response_metadata['cache_creation_input_tokens']}, "
                                f"cache_read_input_tokens: {call.response_metadata['cache_read_input_tokens']})"
                            )
                            result_text += token_usage_str
                        except KeyError as e:
                            result_text += f" (missing key in token usage: {e})"
                    append_result(i, result_text, True)

                    if vv_llm_calls:
                        self._add_llm_call_details(
                            call, i, append_result, seen_llm_calls
                        )
                    else:
                        assert call.response is not None
                        for message in call.response:
                            if isinstance(message, TextResult):
                                if verbose_llm_calls:
                                    append_result(i, message.text)
                            elif isinstance(message, ToolCall):
                                if verbose_llm_calls:
                                    append_result(
                                        i,
                                        f"{CYAN}[invoking {message.tool_name}]{DEFAULT} Tool input:\n{truncate(json.dumps(message.tool_input))}",
                                    )
                                else:
                                    append_result(
                                        i,
                                        f"{CYAN}[invoking {message.tool_name}]{DEFAULT}",
                                    )
                            elif isinstance(message, AnthropicRedactedThinkingBlock):
                                append_result(i, "[redacted thinking]")
                            elif isinstance(message, AnthropicThinkingBlock):
                                append_result(i, f"[thinking] {message.thinking}")
                            else:
                                append_result(i, f"Unknown message type: {message}")
                continue

            # Tool call
            assert isinstance(call, LoggedToolCall)
            if call.started:
                if use_tool_supplied_messages:
                    log_message = (
                        f"{CYAN}{RIGHT} [{call.tool.name}]{DEFAULT} {call.tool_message}"
                    )
                    append_result(i, log_message, False)
                else:
                    append_result(
                        i, f"{RIGHT} calling tool '{call.tool.name}' with input:", True
                    )

                    output = ""
                    for k, v in call.tool_input.items():
                        output += f"{k}\n-------\n"
                        output += f"{v}\n-------\n"
                    append_result(i, output)
                active_tools.append(call.tool)
                indent_amount += 1
            else:
                indent_amount -= 1
                active_tools.pop()

                if call.duration is None:
                    duration_str = "(duration: unknown)"
                else:
                    minutes, seconds = divmod(call.duration or 0, 60)
                    duration_str = f"(duration: {int(minutes)}m {seconds:.1f}s)"

                if use_tool_supplied_messages:
                    log_message = f"{CYAN}{LEFT} [{call.tool.name}]{duration_str}{DEFAULT} {call.tool_message}"
                    append_result(i, log_message, False)
                else:
                    append_result(
                        i,
                        f"{LEFT} tool '{call.tool.name}' output{duration_str}:",
                        True,
                    )
                    append_result(i, f"{truncate(call.tool_output)}")

        return "\n".join(result)

    def get_html_representation(
        self,
        call_index: Optional[int] = None,
        truncate_long_outputs: bool = True,
        use_tool_supplied_messages: bool = False,
        full_page: bool = False,
    ) -> str:
        """Return an HTML representation of the tool calls with collapsible sections."""
        result = []
        indent_amount = 0
        active_tools = []
        section_id = 0

        def indent(text):
            return "&nbsp;" * (4 * indent_amount) + text

        def truncate(text):
            if truncate_long_outputs:
                return html.escape(self._truncate_for_printing(text) or "")
            return html.escape(text)

        def sanitize(text):
            """Look for terminal escape sequences in the text and remove them."""
            return re.sub(r"\x1b\[[\d;]+m", "", text)

        def append_result(
            idx: int,
            text: str,
            is_tool_call: bool = False,
            collapsible: bool = False,
        ):
            nonlocal section_id
            if call_index and idx != call_index:
                return
            class_name = "tool-call" if is_tool_call else "llm-call"
            if collapsible:
                section_id += 1
                result.append(
                    f'<div class="{class_name} collapsible" onclick="toggleSection({section_id})">'
                )
                result.append(f'<span class="toggle-arrow">▼</span>{indent(text)}')
                result.append("</div>")
                result.append(f'<div id="section-{section_id}" class="content">')
            else:
                result.append(f'<div class="{class_name}">{indent(text)}</div>')

        def close_section():
            result.append("</div>")

        for i, call in enumerate(self.logged_calls):
            if isinstance(call, LoggedWorkspaceChanges):
                # Display workspace changes
                if call.description:
                    append_result(
                        i,
                        f'<span class="workspace-changes">Workspace changes: {html.escape(call.description)}</span>',
                    )
                else:
                    append_result(
                        i,
                        '<span class="workspace-changes">Workspace changes:</span>',
                    )

                changed_files_html = f'<div class="changed-files">Changed files: {len(call.changed_files)} files</div>'
                for changed_file in call.changed_files:
                    change_type = changed_file.change_type

                    if change_type == "ADDED":
                        changed_files_html += f'<div class="file-added">ADDED: {html.escape(changed_file.new_path)}</div>'
                    elif change_type == "DELETED":
                        changed_files_html += f'<div class="file-deleted">DELETED: {html.escape(changed_file.old_path)}</div>'
                    elif change_type == "RENAMED":
                        changed_files_html += f'<div class="file-renamed">RENAMED: {html.escape(changed_file.old_path)} -> {html.escape(changed_file.new_path)}</div>'
                    else:  # MODIFIED
                        changed_files_html += f'<div class="file-modified">MODIFIED: {html.escape(changed_file.new_path)}</div>'

                append_result(i, changed_files_html)
            elif isinstance(call, LoggedLanguageModelCall):
                if call.started:
                    message_text = ""
                    for message in call.messages[-1]:
                        if isinstance(message, TextPrompt):
                            message_text = message.text

                    if message_text:
                        log_message = f'calling llm: <pre class="llm-input">{truncate(message_text)}</pre>'
                    else:
                        log_message = "calling llm"

                    append_result(
                        i,
                        f'<span class="llm-start">{RIGHT}</span> {log_message}',
                        collapsible=True,
                    )
                else:
                    if call.duration is None:
                        duration_str = "unknown"
                    else:
                        minutes, seconds = divmod(call.duration or 0, 60)
                        duration_str = f"{int(minutes)}m {seconds:.1f}s"

                    append_result(
                        i,
                        f'<span class="llm-end">{LEFT}</span> llm response (duration: {duration_str}):',
                    )
                    assert call.response is not None
                    for message in call.response:
                        if isinstance(message, TextResult):
                            append_result(
                                i,
                                f'<pre class="llm-response">{html.escape(message.text)}</pre>',
                            )
                        elif isinstance(message, ToolCall):
                            append_result(
                                i,
                                f'<span class="tool-invocation">[invoking {message.tool_name}]</span>',
                            )
                        elif isinstance(message, AnthropicRedactedThinkingBlock):
                            append_result(
                                i,
                                '<span class="llm-response">[redacted thinking]</span>',
                            )
                        elif isinstance(message, AnthropicThinkingBlock):
                            append_result(
                                i,
                                f'<pre class="llm-response">[thinking] {html.escape(message.thinking)}</pre>',
                            )
                        else:
                            append_result(
                                i,
                                f'<span class="unknown-message">Unknown message type: {message}</span>',
                            )
                    close_section()
            elif isinstance(call, LoggedToolCall):
                if call.started:
                    instruction = call.tool_input.get("instruction", "")
                    instruction_text = (
                        f' - "{truncate(instruction)}"' if instruction else ""
                    )

                    if use_tool_supplied_messages:
                        log_message = f'<span class="tool-name">[{call.tool.name}]</span> {html.escape(call.tool_message)}{instruction_text}'
                        append_result(
                            i,
                            f'<span class="tool-start">{RIGHT}</span> {log_message}',
                            True,
                            collapsible=True,
                        )
                    else:
                        append_result(
                            i,
                            f'<span class="tool-start">{RIGHT}</span> calling tool "{call.tool.name}" with input:{instruction_text}',
                            True,
                            collapsible=True,
                        )
                        output = ""
                        for k, v in call.tool_input.items():
                            output += f"{k}\n-------\n"
                            output += f"{v}\n-------\n"
                        append_result(
                            i,
                            f'<pre class="tool-input">{html.escape(output)}</pre>',
                        )
                    active_tools.append(call.tool)
                    indent_amount += 1
                else:
                    indent_amount -= 1
                    active_tools.pop()

                    if call.duration is None:
                        duration_str = "unknown"
                    else:
                        minutes, seconds = divmod(call.duration or 0, 60)
                        duration_str = f"{int(minutes)}m {seconds:.1f}s"

                    if use_tool_supplied_messages:
                        log_message = f'<span class="tool-name">[{call.tool.name}] (duration: {duration_str})</span> {html.escape(call.tool_message)}'
                        append_result(
                            i,
                            f'<span class="tool-end">{LEFT}</span> {log_message}',
                            True,
                        )
                    else:
                        append_result(
                            i,
                            f'<span class="tool-end">{LEFT}</span> tool "{call.tool.name}" output (duration: {duration_str}):',
                            True,
                        )
                    append_result(
                        i,
                        f'<pre class="tool-output">{sanitize(truncate(call.tool_output))}</pre>',
                    )
                    close_section()

        css = """
        <style>
            .tool-call { color: #4CAF50; }
            .llm-call { color: #2196F3; }
            .llm-start, .llm-end, .tool-start, .tool-end { font-weight: bold; }
            .tool-name { color: #9C27B0; }
            .tool-invocation { color: #FF9800; }
            .llm-response, .tool-input, .tool-output, .llm-input {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px;
                white-space: pre-wrap;
                word-wrap: break-word;
            }
            .unknown-message { color: #F44336; }
            .collapsible { cursor: pointer; }
            .content { display: none; padding-left: 20px; }
            .toggle-arrow { display: inline-block; width: 1em; }
            .workspace-changes { color: #673AB7; font-weight: bold; }
            .changed-files { font-weight: bold; margin-top: 10px; }
            .file-added { color: #4CAF50; margin-left: 20px; }
            .file-deleted { color: #F44336; margin-left: 20px; }
            .file-modified { color: #2196F3; margin-left: 20px; }
            .file-renamed { color: #FF9800; margin-left: 20px; }
        </style>
        """

        js = """
        <script>
        function toggleSection(id) {
            var content = document.getElementById('section-' + id);
            var arrow = content.previousElementSibling.querySelector('.toggle-arrow');
            if (content.style.display === 'block') {
                content.style.display = 'none';
                arrow.textContent = '▼';
            } else {
                content.style.display = 'block';
                arrow.textContent = '▲';
            }
        }
        </script>
        """

        html_content = "".join(result)

        if full_page:
            return f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Tool Call Log</title>
                {css}
            </head>
            <body>
                {html_content}
                {js}
            </body>
            </html>
            """

        return css + js + html_content

    def __str__(self):
        return self.get_string_representation(verbose_llm_calls=self.verbose_llm_calls)

    def finish(self):
        if self.html_log_file:
            self.html_log_file.write_text(
                self.get_html_representation(
                    truncate_long_outputs=False, full_page=True
                )
                + "\n"
            )

        # Ensure the JSON file is updated with the final state
        if self.json_log_file:
            self.json_log_file.parent.mkdir(parents=True, exist_ok=True)
            json_data = self._get_json_serializable_calls()

            # Use the module-level CustomJSONEncoder

            with self.json_log_file.open("w") as f:
                json.dump(json_data, f, indent=2, cls=CustomJSONEncoder)


class LoggingLLMClient(LLMClient):
    """An LLM client that logs LM calls to a ToolCallLogger."""

    def __init__(self, client: LLMClient, tool_call_logger: ToolCallLogger):
        self.client = client
        self.tool_call_logger = tool_call_logger

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: list[ToolParam] = [],
        tool_choice: dict[str, str] | None = None,
    ) -> Tuple[list[AssistantContentBlock], dict[str, Any]]:
        self.tool_call_logger.language_model_call_started(
            messages=messages,
            max_tokens=max_tokens,
            system_prompt=system_prompt,
            temperature=temperature,
            tools=tools,
            tool_choice=tool_choice,
        )
        start_time = time.time()

        model_response, metadata = self.client.generate(
            messages=messages,
            max_tokens=max_tokens,
            system_prompt=system_prompt,
            temperature=temperature,
            tools=tools,
            tool_choice=tool_choice,
        )

        end_time = time.time()
        self.tool_call_logger.language_model_call_ended(
            messages=messages,
            max_tokens=max_tokens,
            system_prompt=system_prompt,
            temperature=temperature,
            tools=tools,
            tool_choice=tool_choice,
            response=model_response,
            response_metadata=metadata,
            duration=end_time - start_time,
        )

        return model_response, metadata


def call_tools(
    tools: list[LLMTool],
    calls_to_make: list[ToolCallParameters],
    dialog_messages: Optional[DialogMessages] = None,
) -> list[str]:
    """Call the requested tools and return their outputs.

    Args:
        tools: The tools to call.
        calls_to_make: The calls to make.
        dialog_messages: If supplied, the tool call results will be recorded here.
    """
    tool_outputs = []
    for call in calls_to_make:
        tool = next(t for t in tools if t.name == call.tool_name)
        tool_outputs.append(tool.run(call.tool_input))

    if dialog_messages:
        # Add the tool call results to the dialog
        dialog_messages.add_tool_call_results(calls_to_make, tool_outputs)

    return tool_outputs

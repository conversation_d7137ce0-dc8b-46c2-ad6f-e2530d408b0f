# mirror of nvidia/cuda:11.3.0-devel-ubuntu20.04
FROM 829650725646.dkr.ecr.us-west-2.amazonaws.com/nvidia_cuda_ubuntu:11.7.1-devel-ubuntu20.04
# use docker buildx build for this Dockerfile.

ENV DEBIAN_FRONTEND=noninteractive

#### System package (uses Python 3.9 version in Ubuntu 20.04)
RUN --mount=type=cache,target=/var/cache/apt apt-get update -y && \
    apt-get install -y \
        git python3.9 python3.9-dev libpython3.9-dev python3-pip sudo pdsh \
        htop llvm-9-dev tmux zstd software-properties-common build-essential autotools-dev \
        nfs-common pdsh cmake g++ gcc curl wget vim less unzip htop iftop iotop ca-certificates ssh \
        rsync iputils-ping net-tools libcupti-dev libmlx4-1 infiniband-diags ibutils ibverbs-utils \
        rdmacm-utils perftest rdma-core nano && \
    curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py && \
    python3.9 get-pip.py && \
    update-alternatives --install /usr/bin/python python /usr/bin/python3.9 1 && \
    update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.9 1 && \
    update-alternatives --install /usr/bin/python3-config python-config /usr/bin/python3.9-config 1 && \
    pip install --upgrade pip && \
    pip install gpustat

### SSH
# Set password
RUN echo 'password' >> password.txt && \
    mkdir /var/run/sshd && \
    echo "root:`cat password.txt`" | chpasswd && \
    # Allow root login with password
    sed -i 's/PermitRootLogin without-password/PermitRootLogin yes/' /etc/ssh/sshd_config && \
    # Prevent user being kicked off after login
    sed -i 's@session\s*required\s*pam_loginuid.so@session optional pam_loginuid.so@g' /etc/pam.d/sshd && \
    echo 'AuthorizedKeysFile     .ssh/authorized_keys' >> /etc/ssh/sshd_config && \
    echo 'PasswordAuthentication yes' >> /etc/ssh/sshd_config && \
    # FIX SUDO BUG: https://github.com/sudo-project/sudo/issues/42
    echo "Set disable_coredump false" >> /etc/sudo.conf && \
    # Clean up
    rm password.txt

# Expose SSH port
EXPOSE 22

#### OPENMPI
ENV OPENMPI_BASEVERSION=4.1
ENV OPENMPI_VERSION=${OPENMPI_BASEVERSION}.0
RUN mkdir -p /build && \
    cd /build && \
    wget -q -O - https://download.open-mpi.org/release/open-mpi/v${OPENMPI_BASEVERSION}/openmpi-${OPENMPI_VERSION}.tar.gz | tar xzf - && \
    cd openmpi-${OPENMPI_VERSION} && \
    ./configure --prefix=/usr/local/openmpi-${OPENMPI_VERSION} && \
    make -j"$(nproc)" install && \
    ln -s /usr/local/openmpi-${OPENMPI_VERSION} /usr/local/mpi && \
    # Sanity check:
    test -f /usr/local/mpi/bin/mpic++ && \
    cd ~ && \
    rm -rf /build

# Needs to be in docker PATH if compiling other items & bashrc PATH (later)
ENV PATH=/usr/local/mpi/bin:${PATH} \
    LD_LIBRARY_PATH=/usr/local/lib:/usr/local/mpi/lib:/usr/local/mpi/lib64:${LD_LIBRARY_PATH}

# Create a wrapper for OpenMPI to allow running as root by default
RUN mv /usr/local/mpi/bin/mpirun /usr/local/mpi/bin/mpirun.real && \
    echo '#!/bin/bash' > /usr/local/mpi/bin/mpirun && \
    echo 'mpirun.real --allow-run-as-root --prefix /usr/local/mpi "$@"' >> /usr/local/mpi/bin/mpirun && \
    chmod a+x /usr/local/mpi/bin/mpirun

#### User account
RUN useradd --create-home --uid 1000 --shell /bin/bash augment && \
    usermod -aG sudo augment && \
    echo "augment ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers

## SSH config and bashrc
RUN mkdir -p /home/<USER>/.ssh /job && \
    echo 'Host *' > /home/<USER>/.ssh/config && \
    echo '    StrictHostKeyChecking no' >> /home/<USER>/.ssh/config && \
    echo 'export PDSH_RCMD_TYPE=ssh' >> /home/<USER>/.bashrc && \
    echo 'export PATH=/home/<USER>/.local/bin:$PATH' >> /home/<USER>/.bashrc && \
    echo 'export PATH=/usr/local/mpi/bin:$PATH' >> /home/<USER>/.bashrc && \
    echo 'export LD_LIBRARY_PATH=/usr/local/lib:/usr/local/mpi/lib:/usr/local/mpi/lib64:$LD_LIBRARY_PATH' >> /home/<USER>/.bashrc

#### Python packages
RUN --mount=type=cache,target=~/.cache pip install torch==1.13.1+cu117 torchvision==0.14.1+cu117 torchaudio==0.13.1+cu117 --extra-index-url https://download.pytorch.org/whl/cu117
COPY ../requirements.txt .
RUN --mount=type=cache,target=~/.cache pip install -r requirements.txt && pip cache purge
RUN rm requirements.txt

COPY megatron/fused_kernels /opt/fused_kernels
RUN --mount=type=cache,target=~/.cache pip install /opt/fused_kernels/

COPY --from=lm . /workspace/research/lm-evaluation-harness
RUN pip install -e  /workspace/research/lm-evaluation-harness

# Clear staging
RUN mkdir -p /tmp && chmod 0777 /tmp

#### SWITCH TO augment USER
USER augment
WORKDIR /home/<USER>

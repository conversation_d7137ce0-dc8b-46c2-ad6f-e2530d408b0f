#!/bin/bash

# Sample script to run an eval, then trigger a post-processing step

set -e
set -x

BASE=/run/determined/workdir/

EVAL_MOUNT="/mnt/efs/augment/eval/executions/${DET_EXPERIMENT_ID}"

mkdir -p ${EVAL_MOUNT}

source $(dirname $0)/entrypoint_utils.sh
entrypoint_setup

EXPERIMENT_ID=$DET_EXPERIMENT_ID
TRIAL_ID=$DET_TRIAL_ID
EVAL_OUTDIR=`eval echo ${EVAL_OUTDIR}`

# create virtualenv for python
(cd ${BASE} && python -m venv swebench_env)

SWEBENCH_REF="b10f7a570e20b191f65a45e24c165312e00d562c"
git clone https://github.com/augmentcode/SWE-bench.git ${BASE}/swebench
(source ${BASE}/swebench_env/bin/activate \
    && cd ${BASE}/swebench \
    && git checkout ${SWEBENCH_REF} \
    && pip install -e . \
    && deactivate)
SWEBENCH_SHORT_REF=$(cd ${BASE}/swebench && git rev-parse --short ${SWEBENCH_REF})
export SWEBENCH_SHORT_REF

env

apt-get install -y \
    docker-buildx \
    docker.io \
    docker

containerd > /tmp/containerd.log 2>&1 &
sleep 5

dockerd -H unix:///var/run/docker.sock \
    --containerd=/run/containerd/containerd.sock \
    --data-root=/home/<USER>/docker > /tmp/docker.log 2>&1 &
sleep 5

gcloud auth configure-docker us-central1-docker.pkg.dev < /dev/null

pip install gql requests-toolbelt natsort

agent_workdir=${BASE}/swe_work
mkdir -p ${agent_workdir}

echo "CHECKPOINT:$CHECKPOINT"
echo "EVAL_OUTDIR:$EVAL_OUTDIR"
echo "EVAL_PREFIX:$EVAL_PREFIX"
echo "EVAL_SPEC:$EVAL_SPEC"

sync_remote_checkpoint

install_gpt

if [[ -n $EVAL_OUTDIR ]]; then
    mkdir -p -m 0777 $EVAL_OUTDIR
    chown 1000:1000 $EVAL_OUTDIR
fi

# Create a temporary file to hold the configuration
TEMP_FILE=$(mktemp)
echo "$EVAL_SPEC" > "$TEMP_FILE"

# Don't launch from eval/harness to prevent types.py from causing problems
pushd ../eval
echo "python harness/launch_harness.py $TEMP_FILE --output $EVAL_OUTDIR --prefix $EVAL_PREFIX"
python harness/launch_harness.py $TEMP_FILE --output $EVAL_OUTDIR --prefix $EVAL_PREFIX
popd
rm "$TEMP_FILE"

python ${BASE}/research/eval/swe_bench/agent_qa/swe_bench/stage_results.py \
    /run/determined/workdir/swe_work \
    gs://gcp-us1-public-html/swebench/determined-unconsolidated/${DET_EXPERIMENT_ID}


date
echo "Complete"

# Uncomment to hang so you can kubectl exec in and poke around.
#while true; do
#    if [[ -f /tmp/debug ]]; then
#        break
#    fi
#    sleep 10
#done

includes:
- augment_configs/codegen-H/model/codegen-350M.stage1.yml
- augment_configs/codegen-H/train/big_batch.f.yml
- augment_configs/codegen-H/train/train_memory.yml
- augment_configs/codegen-H/lr/3e-4.yml
- augment_configs/codegen-H/mem/prelayerL16.yml
- augment_configs/codegen-H/mem/ignore_eod.yml
- augment_configs/codegen-H/mem/memory_train_on_gpu.yml
overrides:
  # Override gpt_neox args here
  # If the data path in your configuration files needs to be changed, do that here
  data_path: /mnt/efs/augment/data/processed/github/github_small_text_document
augment: {}
  # Add or modify default augment-specific runtime values here
determined: {}
  # Add or modify default determined system arguments here

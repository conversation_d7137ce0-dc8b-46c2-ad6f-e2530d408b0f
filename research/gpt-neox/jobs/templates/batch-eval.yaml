# includes is an ordered list of gpt-neox config files to be loaded
includes:
- /mnt/efs/augment/configs/codegen-H/train/small_batch.yml
# determined is a dictionary of determined.ai specific arguments that have no corresponding gpt-neox arguments
determined:
  name: None
  description: None
  workspace: Dev
  project: Default
  max_restarts: 1
  perform_initial_validation: True  # Do a validation at iteration 0
# augment is a dictionary of augment specific arguments for our code extensions
augment:
  # Common args for both training and evaluation
  gpu_count: 1  # How many GPUs to ask for
  max_slots: 8
  save_trial_best: 0  # How many of the best checkpoints to save

  # Evaluation args (comment out for experiment)
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/eval.sh"  # For eval

# overrides is a dictionary of gpt-neox args to override the values in the above list of included config files
overrides:
  save_interval: 0
  eval_interval: 0
  train_iters: 1

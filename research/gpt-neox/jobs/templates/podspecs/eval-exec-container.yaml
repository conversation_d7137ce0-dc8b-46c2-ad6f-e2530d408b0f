# Podspec for eval-exec experiment
spec:
  serviceAccountName: spark-sa
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: topology.kubernetes.io/region
            operator: In
            values:
            - LAS1
          - key: gpu.nvidia.com/class
            operator: In
            values:
            - RTX_A5000
  containers:
  - name: determined-container
    # If you're using more than 1 GPU, you need >16G memory or you'll get an opaque exit 247 on startup.
    resources:
      limits:
        cpu: '4'
        memory: 64Gi
      requests:
        cpu: '4'
        memory: 64Gi
    volumeMounts:
    - mountPath: /mnt/efs/augment
      name: aug-cw-las1
  priorityClassName: determined-system-priority
  volumes:
  - emptyDir:
      medium: Memory
    name: dshm
  - name: aug-cw-las1
    persistentVolumeClaim:
      claimName: aug-cw-las1

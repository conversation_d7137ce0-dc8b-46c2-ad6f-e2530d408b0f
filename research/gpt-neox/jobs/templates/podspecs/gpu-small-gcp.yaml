# Default podspec for determined experiments.
# Uses RTX_A5000 GPUs
spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: cloud.google.com/gke-accelerator
            operator: In
            values:
            - nvidia-tesla-t4
  containers:
  - name: determined-container
    # If you're using more than 1 GPU, you need >16G memory or you'll get an opaque exit 247 on startup.
    resources:
      limits:
        cpu: '4'
        memory: 40Gi
      requests:
        cpu: '4'
        memory: 40Gi
  priorityClassName: determined-system-priority

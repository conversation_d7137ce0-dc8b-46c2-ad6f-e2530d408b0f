# Podspec when requesting exactly 4x A100 GPUs to keep the node shareable
spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: topology.kubernetes.io/region
            operator: In
            values:
            - LAS1
          - key: gpu.nvidia.com/class
            operator: In
            values:
            - A100_NVLINK_80GB
  containers:
  - name: determined-container
    resources:
      limits:
        cpu:  48
        memory: 384Gi
        nvidia.com/gpu: 4
      requests:
        cpu:  48
        memory: 384Gi
        nvidia.com/gpu: 4
  priorityClassName: determined-system-priority

spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: topology.kubernetes.io/region
            operator: In
            values:
            - LAS1
          - key: gpu.nvidia.com/class
            operator: In
            values:
            - A100_NVLINK_80GB
            - H100_NVLINK_80GB
  containers:
  - name: determined-container
    # If you're using more than 1 GPU, you need >16G memory or you'll get an opaque exit 247 on startup.
    resources:
      limits:
        cpu: 96
        memory: 768Gi
        nvidia.com/gpu: 8
        rdma/ib: 1
      requests:
        cpu: 96
        memory: 768Gi
        nvidia.com/gpu: 8
        rdma/ib: 1
  priorityClassName: determined-system-priority

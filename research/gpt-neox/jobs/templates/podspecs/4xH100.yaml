spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: topology.kubernetes.io/region
            operator: In
            values:
            - LAS1
          - key: gpu.nvidia.com/class
            operator: In
            values:
            - H100_NVLINK_80GB
  containers:
  - name: determined-container
    resources:
      limits:
        cpu: 54
        memory: 900Gi
        nvidia.com/gpu: 4
        rdma/ib: 1
      requests:
        cpu: 54
        memory: 900Gi
        nvidia.com/gpu: 4
        rdma/ib: 1
  priorityClassName: determined-system-priority

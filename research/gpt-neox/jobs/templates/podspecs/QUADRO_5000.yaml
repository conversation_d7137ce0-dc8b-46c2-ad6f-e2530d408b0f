# podspec for determined experiments.
# Uses Quadro_RTX_5000 GPUs
spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: topology.kubernetes.io/region
            operator: In
            values:
            - LAS1
          - key: gpu.nvidia.com/class
            operator: In
            values:
            - Quadro_RTX_5000
  containers:
  - name: determined-container
    resources:
      limits:
        cpu: '4'
        memory: 64Gi
      requests:
        cpu: '4'
        memory: 64Gi
    volumeMounts:
    # - mountPath: /dev/shm
    #   name: dshm
    - mountPath: /mnt/efs/augment
      name: aug-cw-las1
  priorityClassName: determined-system-priority
  volumes:
  - emptyDir:
      medium: Memory
    name: dshm
  - name: aug-cw-las1
    persistentVolumeClaim:
      claimName: aug-cw-las1

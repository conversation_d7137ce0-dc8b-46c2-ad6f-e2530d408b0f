# Podspec when requesting exactly x A100 GPU to keep the node shareable
spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: cloud.google.com/gke-accelerator
            operator: In
            values:
            - nvidia-a100-80gb
  containers:
  - name: determined-container
    resources:
      limits:
        cpu:  12
        memory: 96Gi
        nvidia.com/gpu: 1
      requests:
        cpu:  12
        memory: 96Gi
        nvidia.com/gpu: 1
  priorityClassName: determined-system-priority

# Podspec when requesting exactly x A100 GPU to keep the node shareable
template:
  metadata:
    labels:
      clusteringHint:  shareANode
spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: topology.kubernetes.io/region
            operator: In
            values:
            - LAS1
          - key: gpu.nvidia.com/class
            operator: In
            values:
            - A100_NVLINK_80GB
    podAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchLabels:
              clusteringHint:  shareANode
          topologyKey: "kubernetes.io/hostname"
  containers:
  - name: determined-container
    resources:
      limits:
        cpu:  12
        memory: 96Gi
        nvidia.com/gpu: 1
      requests:
        cpu:  12
        memory: 96Gi
        nvidia.com/gpu: 1
  priorityClassName: determined-system-priority

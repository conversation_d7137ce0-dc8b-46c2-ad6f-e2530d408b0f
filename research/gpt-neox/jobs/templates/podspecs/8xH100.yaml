spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: topology.kubernetes.io/region
            operator: In
            values:
            - LAS1
          - key: gpu.nvidia.com/class
            operator: In
            values:
            - H100_NVLINK_80GB
  containers:
  - name: determined-container
    resources:
      limits:
        cpu: 110
        memory: 1800Gi
        nvidia.com/gpu: 8
        rdma/ib: 1
        ephemeral-storage: 2000Gi
      requests:
        cpu: 110
        memory: 1800Gi
        nvidia.com/gpu: 8
        rdma/ib: 1
        ephemeral-storage: 2000Gi
  priorityClassName: determined-system-priority
  volumes:
  - emptyDir:
      medium: Memory
    name: dshm

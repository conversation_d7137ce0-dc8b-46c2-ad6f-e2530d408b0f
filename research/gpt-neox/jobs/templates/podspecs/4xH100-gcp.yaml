# Podspec when requesting exactly x H100 GPU to keep the node shareable
spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: cloud.google.com/gke-accelerator
            operator: In
            values:
            - nvidia-h100-mega-80gb
  containers:
  - name: determined-container
    volumeMounts:
    - name: aperture-devices
      mountPath: /dev/aperture_devices
    - name: libraries
      mountPath: /usr/local/nvidia/lib64
    resources:
      limits:
        cpu:  103
        memory: 850Gi
        nvidia.com/gpu: 4
      requests:
        cpu:  103
        memory: 850Gi
        nvidia.com/gpu: 4
  - name: tcpxo-daemon
    image: us-docker.pkg.dev/gce-ai-infra/gpudirect-tcpxo/tcpgpudmarxd-dev:latest
    command: ["/bin/sh", "-c"]
    args:
      - |
        set -ex
        chmod 755 /fts/entrypoint_rxdm_container.sh
        /fts/entrypoint_rxdm_container.sh --num_hops=2 --num_nics=8 --uid= --alsologtostderr
    securityContext:
      capabilities:
        add:
          - NET_ADMIN
          - NET_BIND_SERVICE
    volumeMounts:
    - name: libraries
      mountPath: /usr/local/nvidia/lib64
    - name: sys
      mountPath: /hostsysfs
    - name: proc-sys
      mountPath: /hostprocsysfs
    env:
      - name: LD_LIBRARY_PATH
        value: "/usr/local/nvidia/lib64"
  priorityClassName: determined-system-priority
  volumes:
  - name: libraries
    hostPath:
      path: /home/<USER>/bin/nvidia/lib64
  - name: sys
    hostPath:
      path: /sys
  - name: proc-sys
    hostPath:
      path: /proc/sys
  - name: aperture-devices
    hostPath:
      path: /dev/aperture_devices

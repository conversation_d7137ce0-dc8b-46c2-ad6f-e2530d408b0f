# CPU podspec for GCP
# Podspec when requesting exactly x H100 GPU to keep the node shareable
spec:
  affinity:
    nodeAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - preference:
          matchExpressions:
          - key: r.augmentcode.com/pool-type
            operator: In
            values:
            - spark-highmem
        weight: 50
  containers:
  - name: determined-container
    resources:
      limits:  # 1:8 CPU:RAM ratio
        cpu:  50
        memory: 400Gi
      requests:
        cpu:  50
        memory: 400Gi
  priorityClassName: determined-system-priority
  volumes:
  - name: sys
    hostPath:
      path: /sys
  - name: proc-sys
    hostPath:
      path: /proc/sys

spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: topology.kubernetes.io/region
            operator: In
            values:
            - LAS1
          - key: gpu.nvidia.com/class
            operator: In
            values:
            - H100_NVLINK_80GB
  containers:
  - name: determined-container
    resources:
      limits:
        cpu: 13.5
        memory: 240Gi
        nvidia.com/gpu: 1
        rdma/ib: 1
      requests:
        cpu: 13.5
        memory: 240Gi
        nvidia.com/gpu: 1
        rdma/ib: 1
  priorityClassName: determined-system-priority

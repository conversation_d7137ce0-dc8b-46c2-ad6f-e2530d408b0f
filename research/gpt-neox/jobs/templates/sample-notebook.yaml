# Default notebook spec for jupyter notebooks in determined.
# Uses 2xRTX_A5000 GPUs
#
# Assuming you have the determined CLI installed, you can launch this with:
#   det notebook start --config-file sample-notebook.yaml
#
description: test-notebook
resources:
  slots: 2
environment:
  image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/augment_gpu:cuda-11.7-py-3.9-pytorch-2.0.1-gpt-neox-deepspeed-gpu-0.19.12-2
  force_pull_image: True
  pod_spec:
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: topology.kubernetes.io/region
                operator: In
                values:
                - LAS1
              - key: gpu.nvidia.com/class
                operator: In
                values:
                - RTX_A5000
      containers:
      - name: determined-container
        # If you're using more than 1 GPU, you need >16G memory or you'll get an opaque exit 247 on startup.
        resources:
          limits:
            cpu: '4'
            memory: 64Gi
          requests:
            cpu: '4'
            memory: 64Gi
        volumeMounts:
        # - mountPath: /dev/shm
        #   name: dshm
        - mountPath: /mnt/efs/augment
          name: aug-cw-las1
        - mountPath: /run/determined/workdir/shared_fs
          name: aug-cw-las1
      priorityClassName: determined-system-priority
      volumes:
      - emptyDir:
          medium: Memory
        name: dshm
      - name: aug-cw-las1
        persistentVolumeClaim:
          claimName: aug-cw-las1

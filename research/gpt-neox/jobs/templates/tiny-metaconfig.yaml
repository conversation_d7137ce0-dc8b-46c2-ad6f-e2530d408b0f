# includes is an ordered list of gpt-neox config files to be loaded
includes:
- augment_configs/codegen-H/model/codegen-350M.stage1.yml
- augment_configs/codegen-H/train/big_batch.f.yml
- augment_configs/codegen-H/lr/3e-4.yml
- augment_configs/codegen-H/mem/prelayerL16.yml
- augment_configs/codegen-H/mem/ignore_eod.yml
# determined is a dictionary of determined.ai specific arguments that have no corresponding gpt-neox arguments
determined:
  name: null
  description: null
  workspace: Dev
  project: Default
  max_restarts: 1
  perform_initial_validation: True  # Do a validation at iteration 0
# augment is a dictionary of augment specific arguments for our code extensions
augment:
  # user_include_files:          # Absolute paths or relative to AUGMENT_ROOT
  # - experimental/me/include_file
  slack_notifications:
    enabled: False             # Enable slack notifications
    slack_user: Uxxxxxxxxxx    # Set to your slack user ID to send notifications.
    notify_validations: False  # Set to true to send a message on every validation pass - may be noisy
  # Common args for both training and evaluation
  podspec_path: "gpu-tiny.yaml"  # Path to the podspec file.  Absolute, or relative to "templates"
  gpu_count: 1  # How many GPUs to ask for
  save_trial_best: 0  # How many of the best checkpoints to save
  service_account_name: determined-service-account
  # Optional for debugging - log every N tensors on rank 0
  # data_log_interval: 100

  # Environment variables to pass to every worker process.
  environment_variables:
  #   ENV_VAR: "<value>"

  # If you want to start the trial by loading weights from an existing
  # determined CP, shifting the data, etc (eg, "continuing") - put the UUID here.
  # source_checkpoint: <UUID>

  # If you want to skip a few data iterations ("batches") before resuming training, set
  # the field here:
  # skip_iteration: <number-of-iterations-to-skip>

  # Experiment args (comment out for eval)
  # checkpoint_handling:
  #   action: eval     # eval == run an eval job on the checkpoint
  #   # action: gc       # gc == garbage collect checkpoints
  #   # action: nowrite  # nowrite == don't copy checkpoints to s3
  #   #
  #   # Eval options
  #   config: eval-sample.yml    # in research/eval/configs
  #   # All eval args are exactly as documented in the eval-sample.yml file
  #   tasks:
  #     - gitrepo_poly_C_small
  #     - gitrepo_poly_C++_small
  #   # podspec: 1xA100.yaml
  #   # neox_args:
  #   #   eval_with_memories: False
  #   determined:
  #       name: exp-$EXPERIMENT_ID Eval
  #       workspace: Dev
  #       project: marcmac
  #       # relative to research/gpt-neox
  #       metaconfig: jobs/templates/batch-eval.yaml
    # eval_tags:
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"  # For training

  # Evaluation args (comment out for experiment)
  # eval_tasks: gitrepo_poly_C_small  # Space separated list of eval tasks
  # eval_results_prefix: /mnt/efs/augment/user/username/eval_results # Path and prefix for evaluation results
  # entrypoint: "/run/determined/research/workdir/gpt-neox/jobs/eval.sh"  # For eval

# overrides is a dictionary of gpt-neox args to override the values in the above list of included config files
overrides:
  # WandB options. WANDB_API_KEY will come from the environment, or ~/.netrc if you are logged in.
  wandb_name: trial-$TRIAL_ID
  wandb_project: my_project   # This probably needs to already exist in your wandb dashboard
  wandb_group: exp-$EXPERIMENT_ID
  # wandb_team: my_team

  # save: is ignored by determined; checkpoints are saved in s3
  # save: /mnt/efs/augment/checkpoints/pref_signal/test1_ft_beta0.95_s2048_lr1.6e-5_const

  data_path: /mnt/efs/augment/data/processed/github/github_small_text_document
  #train_data_paths: [/mnt/efs/augment/data/processed/the-stack-dedup.2022-11-19/doc_text_document]
  #valid_data_paths: []
  #test_data_paths: []

  load: /mnt/efs/augment/checkpoints/codegen-350M-multi

  train_batch_size: 288
  train_micro_batch_size_per_gpu: 12
  gradient_accumulation_steps: 24

  eval_interval: 20
  early_stopping: False
  early_stopping_threshold: 0.005

  train_iters: 40
  lr_decay_iters: 100  # If not set, defaults to train_iters
  warmup: 0.
  lr_decay_style: constant

  optimizer:
      params:
          betas:
          - 0.9
          - 0.95
          eps: 1.0e-08
          lr: 1.6e-05
      type: Adam

  save_interval: 10
  keep_last_n_checkpoints: 2
  # to keep all checkpoints, also disable determined.enable_checkpoint_gc
  #keep_last_n_checkpoints: null
  no_save_optim: False

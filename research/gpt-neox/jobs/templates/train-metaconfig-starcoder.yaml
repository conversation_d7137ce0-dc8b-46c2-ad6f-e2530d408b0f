includes:
- augment_configs/starcoder/model/starcoder.yml
- augment_configs/pretrain/datasets/starcoder.yml
determined:
  name: starcoder-finetune
  description: null
  workspace: Dev
  project: pretrain
  labels: ["starcoder"]
  max_restarts: 0
  perform_initial_validation: False

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: starcoder-finetune
  wandb_group: starcoder-finetune

  # 8k tokens per GPU
  train_micro_batch_size_per_gpu: 4
  gradient_accumulation_steps: 24
  train_batch_size: 384

  train_iters: 1000
  lr_decay_iters: 1000  # If not set, defaults to train_iters
  warmup: 0.01
  lr_decay_style: constant

  optimizer:
      params:
          betas:
          - 0.9
          - 0.95
          eps: 1.0e-08
          lr: 1.6e-05
      type: <PERSON>

  # Eval/save frequency
  eval_interval: 1000
  save_interval: 1000

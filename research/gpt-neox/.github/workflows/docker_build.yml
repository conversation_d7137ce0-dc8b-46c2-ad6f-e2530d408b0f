name: docker_build

on:
  push:
    branches:
      - '**'

jobs:
  main:
    runs-on: ubuntu-latest
    steps:
      -
        name: Checkout
        uses: actions/checkout@v2

      -
        name: Docker meta
        id: docker_meta
        uses: crazy-max/ghaction-docker-meta@v1
        with:
          images: leogao2/gpt-neox # list of Docker images to use as base name for tags
          tag-sha: true # add git short SHA as Docker tag

      -
        name: Set up QEMU
        uses: docker/setup-qemu-action@v1

      -
        name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      -
        name: Login to DockerHub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      -
        name: Build and push
        id: docker_build
        uses: docker/build-push-action@v2
        with:
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.docker_meta.outputs.tags }}
          labels: ${{ steps.docker_meta.outputs.labels }}

      -
        name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}

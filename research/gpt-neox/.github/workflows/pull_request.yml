name: Pull Request

on: [pull_request]

jobs:
  pre-commit:
    runs-on: ubuntu-20.04
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-python@v2
        with:
          python-version: 3.8
      - uses: pre-commit/action@v2.0.3

  update-documentation:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.ref}}
      - run: |
          rm megatron/__init__.py
          pip install shortuuid
          rm megatron/neox_arguments/__init__.py
          python configs/gen_docs.py
          git config user.name github-actions
          git config user.email <EMAIL>
          git add configs/neox_arguments.md
          git commit -m "Update NeoXArgs docs automatically"
          git push
  run-tests:
    runs-on: self-hosted
    steps:
      - uses: actions/checkout@v2
      - name: prepare data
        run: python prepare_data.py
      - name: Run Tests
        run: pytest --forked tests

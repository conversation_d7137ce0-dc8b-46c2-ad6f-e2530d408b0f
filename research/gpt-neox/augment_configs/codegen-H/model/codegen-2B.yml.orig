finetune: true

#activation_function: gelu_new
#
#attn_pdrop: 0.0
attention-dropout: 0.0

#bos_token_id: 1

#embd_pdrop: 0.0
hidden-dropout: 0.0

#eos_token_id: 2

#gradient_checkpointing: false

#initializer_range: 0.02

#layer_norm_epsilon: 1e-05

#model_type: codegen

#n_embd: 2560
hidden-size: 2560

#n_head: 32
num-attention-heads: 32

#n_layer: 32
num-layers: 32

#n_positions: 2048
max-position-embeddings: 512
seq-length: 256

#rotary_dim: 64
pos-emb: rotary
rotary_pct: 0.8
rotary_interleave: true

#summary_activation: null
#summary_first_dropout: 0.1
#summary_proj_to_labels: true
#summary_type: cls_index
#summary_use_proj: true


#torch_dtype: float16
fp16:
  enabled: true
  hysteresis: 2
  loss_scale: 0
  loss_scale_window: 1000
  min_loss_scale: 1


#use_cache: true

#vocab_size: 51200
make_vocab_size_divisible_by: 51200
tokenizer_type: CodeGenTokenizer
vocab_file: none
no_weight_tying: true

gpt_j_residual: true
use_post_attn_norm: false
final_linear_bias: true


checkpoint-activations: true
checkpoint-num-layers: 1
data-impl: mmap
data-path: /mnt/efs/fs1/data/dataCodeGen/github/github_text_document
distributed-backend: nccl
finetune: true
gradient_clipping: 1.0
keep-last-n-checkpoints: 1
log-interval: 100
lr-decay-style: cosine
max-position-embeddings: 512
mem_friendly_batch: true
memorize_mode: train
norm: layernorm
partition-activations: true
pipe-parallel-size: 1
scaled-upper-triang-masked-softmax-fusion: false
steps_per_print: 10
synchronize-each-layer: true
train_micro_batch_size_per_gpu: 12
wall_clock_breakdown: true
wandb_project: codegen
warmup: 0.01
weight-decay: 0.1
zero_optimization:
  allgather_bucket_size: 500000000
  allgather_partitions: true
  contiguous_gradients: true
  cpu_offload: false
  overlap_comm: true
  reduce_bucket_size: 500000000
  reduce_scatter: true
  stage: 0

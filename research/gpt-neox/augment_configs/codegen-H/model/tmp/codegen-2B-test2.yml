attention-dropout: 0.0
checkpoint-activations: true
checkpoint-num-layers: 1
data-impl: mmap
data-path: /mnt/efs/augment/data/github/github_small_text_document
distributed-backend: nccl
final_linear_bias: true
finetune: true
fp16:
  enabled: true
  hysteresis: 2
  loss_scale: 65536
  loss_scale_window: 1000
  min_loss_scale: 1
gpt_j_residual: true
gradient_clipping: 1.0
hidden-dropout: 0.0
hidden-size: 2560
keep-last-n-checkpoints: 1
log-interval: 100
lr-decay-style: cosine
make_vocab_size_divisible_by: 51200
max-position-embeddings: 512
mem_friendly_batch: true
memorize_mode: train
no_weight_tying: true
norm: layernorm
num-attention-heads: 32
num-layers: 32
partition-activations: true
pipe-parallel-size: 1
pos-emb: rotary
rotary_interleave: true
rotary_pct: 0.8
scaled-upper-triang-masked-softmax-fusion: false
seq-length: 256
steps_per_print: 10
synchronize-each-layer: true
tokenizer_type: CodeGenTokenizer
train_micro_batch_size_per_gpu: 12
use_post_attn_norm: false
vocab_file: none
wall_clock_breakdown: true
wandb_project: codegen
warmup: 0.01
weight-decay: 0.1
zero_optimization:
  allgather_bucket_size: 500000000
  allgather_partitions: true
  contiguous_gradients: true
  cpu_offload: false
  overlap_comm: true
  reduce_bucket_size: 500000000
  reduce_scatter: true
  stage: 1

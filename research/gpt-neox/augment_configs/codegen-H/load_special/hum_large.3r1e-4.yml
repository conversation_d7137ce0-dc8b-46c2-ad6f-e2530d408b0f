load: /mnt/efs/fs1/augment/checkpoints/hum_large

# model/large.yml + LR=3e-4
attention-dropout: 0.0
bias-gelu-fusion: false
checkpoint-activations: true
checkpoint-num-layers: 1
data-impl: mmap
data-path: /mnt/efs/fs1/data/pile_00/pile_00_text_document
distributed-backend: nccl
fp16:
  enabled: true
  hysteresis: 2
  loss_scale: 0
  loss_scale_window: 1000
  min_loss_scale: 1
finetune: True
gradient_clipping: 1.0
hidden-dropout: 0
hidden-size: 1536
keep-last-n-checkpoints: 4
log-interval: 100
lr-decay-style: cosine
max-position-embeddings: 512
mem_friendly_batch: true
memorize_mode: train
memory_size: 4096
merge-file: /mnt/efs/fs1/models/tokenizers/gpt2-merges.txt
model-parallel-size: 1
no-weight-tying: true
norm: layernorm
num-attention-heads: 16
num-layers: 24
optimizer:
  params:
    betas:
    - 0.9
    - 0.999
    eps: 1.0e-08
    lr: 0.0003
  type: Adam
partition-activations: true
pipe-parallel-size: 1
pos-emb: rotary
scaled-upper-triang-masked-softmax-fusion: false
seq-length: 256
split: 949,50,1
steps_per_print: 10
synchronize-each-layer: true
train_micro_batch_size_per_gpu: 12
vocab-file: /mnt/efs/fs1/models/tokenizers/gpt2-vocab.json
wall_clock_breakdown: true
wandb_project: memorize
warmup: 0.01
weight-decay: 0.1
zero_optimization:
  allgather_bucket_size: 500000000
  allgather_partitions: true
  contiguous_gradients: true
  cpu_offload: false
  overlap_comm: true
  reduce_bucket_size: 500000000
  reduce_scatter: true
  stage: 0

# attn/memL12_24.yml
attention_config: [
    'global', 'global', 'global', 'global', 'global','global', 'global', 'global', 'global', 'global', 'global', 'knn_both',
    'global', 'global', 'global', 'global', 'global','global', 'global', 'global', 'global', 'global', 'global', 'global',
]

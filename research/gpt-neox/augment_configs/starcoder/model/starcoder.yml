# Training configuration for the 16B StarCoder model.
#
# NOTE(arun): This configuration has been adapted using the HuggingFace configuration
#   here: https://huggingface.co/bigcode/starcoder/blob/main/config.json
#   docs: https://huggingface.co/docs/transformers/v4.31.0/en/model_doc/gpt_bigcode#transformers.GPTBigCodeConfig
# Differences from the HuggingFace version:
#   - We do not need to set `scale_attn_weights` as NeoX does this by default.
#   - We do not apply any dropout (HF uses a dropout of 0.1).
#   - We don't need to set `attention_softmax_in_fp32` because we use flash_attention.
#

activation: gelu
attention-dropout: 0.0
bias-gelu-fusion: true
checkpoint-activations: true
checkpoint-num-layers: 1
distributed-backend: nccl
final_linear_bias: false
flash_attention: true
# This is needed for numerical stability when training with >=4k sequence length
attention_precision: bfloat16
fp16:
  enabled: true
  hysteresis: 2
  loss_scale: 0
  loss_scale_window: 1000
  min_loss_scale: 1
gpt_j_residual: false
gradient_clipping: 1.0
hidden-dropout: 0.0
hidden-size: 6144
load: /mnt/efs/augment/checkpoints/starcoderbase-16b_neox_mp2
log-interval: 100
make_vocab_size_divisible_by: 51200
max-position-embeddings: 8192
model-parallel-size: 2
multiquery_attention: true
no_weight_tying: true
norm: layernorm
num-attention-heads: 48
num-layers: 40
partition-activations: true
pipe-parallel-size: 1
pos-emb: learned
scaled-upper-triang-masked-softmax-fusion: true
seq-length: 8192
steps_per_print: 10
synchronize-each-layer: true
tokenizer_type: StarCoderTokenizer
use_post_attn_norm: true
vocab_file: /mnt/efs/augment/checkpoints/tokenizers/starcoderbase
wall_clock_breakdown: true
zero_optimization:
  allgather_bucket_size: 500000000
  allgather_partitions: true
  contiguous_gradients: true
  cpu_offload: false
  overlap_comm: true
  reduce_bucket_size: 500000000
  reduce_scatter: true
  stage: 1
no_save_optim: true

# Optimization configuration for fine tuning as reported in the paper.
# NOTE: The paper assumes a training batch size of 4M tokens, which would require
#   either a lot of gradient accumulation or 64 nodes. Please adjust your lr
#   accordingly.

train_micro_batch_size_per_gpu: 2   # Fills up a single A100 with 8k seq. length.
finetune: true
warmup: 0.01  # Typically this is ~1k iterations.
lr_decay_style: cosine
min_lr: 5.0e-6
optimizer:
  params:
    betas:
      _ 0.9
      _ 0.95
    eps: 1.0e-08
    lr: 5.0e-05
  type: Adam
weight_decay: 0.1

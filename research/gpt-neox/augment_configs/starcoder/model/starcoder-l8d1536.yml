# See https://www.notion.so/Logs-training-small-neural-model-for-speculative-decoding-ee830d3cf799488a95daf9e23a5f6ea2
activation: gelu_tanh_approximation
# Fusion doesn't work for the gelu approximation.
bias-gelu-fusion: false
hidden-size: 1536
model-parallel-size: 1
num-attention-heads: 12
num-layers: 8

# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/29382
load: /mnt/efs/augment/checkpoints/yury/starcoder-l8d1536-roguesl

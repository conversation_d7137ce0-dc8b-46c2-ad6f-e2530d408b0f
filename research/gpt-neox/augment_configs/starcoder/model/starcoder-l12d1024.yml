# See https://www.notion.so/Logs-training-small-neural-model-for-speculative-decoding-ee830d3cf799488a95daf9e23a5f6ea2
activation: gelu_tanh_approximation
# Fusion doesn't work for the gelu approximation.
bias-gelu-fusion: false
hidden-size: 1024
model-parallel-size: 1
num-attention-heads: 8
num-layers: 12

# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/25477
load: /mnt/efs/augment/checkpoints/yury/starcoder-125M/all-languages-dp0.1

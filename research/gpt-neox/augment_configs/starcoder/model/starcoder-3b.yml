# NOTE: The 1B and 3B models use a different activation function.
activation: gelu_tanh_approximation
# Fusion doesn't work with the gelu approximation.
bias-gelu-fusion: false
hidden-size: 2816
load: /mnt/efs/augment/checkpoints/starcoderbase-3b_neox
model-parallel-size: 1
num-attention-heads: 22
num-layers: 36

# NOTE: Learning rate, etc. weren't provided for the 3b model.
train_micro_batch_size_per_gpu: 8   # Fills up a single A100 with 8k seq. length.

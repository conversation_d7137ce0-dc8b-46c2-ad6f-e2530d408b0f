# See https://www.notion.so/Logs-training-small-neural-model-for-speculative-decoding-ee830d3cf799488a95daf9e23a5f6ea2
activation: gelu_tanh_approximation
# Fusion doesn't work for the gelu approximation.
bias-gelu-fusion: false
hidden-size: 2048
model-parallel-size: 1
num-attention-heads: 16
num-layers: 6

# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/29586
load: /mnt/efs/augment/checkpoints/yury/starcoder-l6d2048-longtraining

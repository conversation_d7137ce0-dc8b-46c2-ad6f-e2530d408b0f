attention-dropout: 0
bias-gelu-fusion: true
checkpoint-activations: true
checkpoint-num-layers: 1
data-impl: mmap
data-path: /mnt/augment/data/processed/the-stack-dedup.2022-11-19/doc_text_document
distributed-backend: nccl
fp16:
  enabled: true
  fp16: true
  hysteresis: 2
  initial_scale_power: 12
  loss_scale: 0
  loss_scale_window: 1000
  min_loss_scale: 1
gpt-j-residual: true
gradient_clipping: 1.0
hidden-dropout: 0
hidden-size: 1024
init_method: small_init
log-interval: 100
lr-decay-style: cosine
max-position-embeddings: 2048
min_lr: 3.0e-05
model-parallel-size: 1
no-weight-tying: true
num-attention-heads: 16
num-layers: 24
num_workers: 1
optimizer:
  params:
    betas:
    - 0.9
    - 0.95
    eps: 1.0e-08
    lr: 0.0003
  type: Adam
output-layer-parallelism: column
output_layer_init_method: wang_init
partition-activations: true
pipe-parallel-size: 1
pos-emb: rotary
rotary-pct: 0.25
scaled-upper-triang-masked-softmax-fusion: true
seq-length: 2048
steps_per_print: 10
synchronize-each-layer: true
tokenizer-type: CodeGenTokenizer
use_wandb: true
wall_clock_breakdown: true
wandb_project: pythia
weight-decay: 0.1
zero_optimization:
  allgather_bucket_size: 500000000
  allgather_partitions: true
  contiguous_gradients: true
  cpu_offload: false
  overlap_comm: true
  reduce_bucket_size: 500000000
  reduce_scatter: true
  stage: 1

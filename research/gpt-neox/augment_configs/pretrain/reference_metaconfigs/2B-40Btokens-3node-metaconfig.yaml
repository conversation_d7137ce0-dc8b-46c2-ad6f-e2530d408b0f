includes:
- augment_configs/pretrain/datasets/starcoder.yml
- augment_configs/pretrain/arch/conan/gptj-arch-common.yml
- augment_configs/pretrain/arch/conan/gptj-runtime-common.yml
- augment_configs/pretrain/arch/conan/sizes/2B-model-def.yml
- augment_configs/pretrain/arch/conan/runs/2B-run-def-40Btokens-3node.yml

determined:
  name: 2B-40Btoken-pretrain
  description: null
  workspace: Dev
  project: pretrain
  labels: ["2B", "pretrain"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 24
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: 2B-40Btoken-pretrain-seqlen2048
  wandb_group: 2B-pretrain

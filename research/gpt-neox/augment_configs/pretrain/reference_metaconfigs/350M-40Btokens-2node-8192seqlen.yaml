# Pretrain a 350M parameter (including 100M word embedding) model to the same quality
# as a chinchilla optimal model of twice the size.

includes:
- augment_configs/pretrain/datasets/starcoder.yml
- augment_configs/pretrain/arch/conan/gptj-arch-common.yml
- augment_configs/pretrain/arch/conan/gptj-runtime-common.yml
- augment_configs/pretrain/arch/conan/sizes/350M-model-def.yml
- augment_configs/pretrain/arch/conan/runs/350M-run-def-7Btokens-3node.yml

determined:
  name: 350M-40Btoken-pretrain-seqlen8192
  description: null
  workspace: Dev
  project: pretrain
  labels: ["350M", "pretrain"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 16
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: 350M-40Btoken-pretrain-seqlen8192
  wandb_group: 350M-pretrain

  # 512k tokens per batch
  train_micro_batch_size_per_gpu: 4
  gradient_accumulation_steps: 1
  train_batch_size: 64
  max-position-embeddings: 8192
  seq-length: 8192

  # 80 steps @ 512k tokens ~ 40B tokens
  lr_decay_iters: 80000
  train_iters: 80000
  warmup: 0.0375  # 3k iters

# Pretrain a 830M parameter (including 150M word embedding) model to the same quality
# as a chinchilla optimal model of twice the size.

includes:
- augment_configs/pretrain/datasets/starcoder.yml
- augment_configs/pretrain/arch/conan/gptj-arch-common.yml
- augment_configs/pretrain/arch/conan/gptj-runtime-common.yml
- augment_configs/pretrain/arch/conan/sizes/830M-model-def.yml

determined:
  name: 830M-80Btoken-pretrain-seqlen2048-8node
  description: null
  workspace: Dev
  project: pretrain
  labels: ["830", "pretrain"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 64
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: 830M-80Btoken-pretrain-seqlen2048-8node
  wandb_group: 830M-80Btoken-pretrain-seqlen2048-8node

  # 512k tokens per batch
  train_micro_batch_size_per_gpu: 4
  gradient_accumulation_steps: 1
  train_batch_size: 256
  max-position-embeddings: 2048
  seq-length: 2048

  # 160k steps @ 512k tokens ~ 80B tokens
  lr_decay_iters: 160000
  train_iters: 160000
  warmup: 0.0175  # 3k iters

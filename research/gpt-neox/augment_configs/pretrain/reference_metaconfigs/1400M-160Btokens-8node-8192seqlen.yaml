# Pretrain a 1400M parameter (including 200M word embedding) model to the same quality
# as a chinchilla optimal model of twice the size.

includes:
- augment_configs/pretrain/datasets/starcoder.yml
- augment_configs/pretrain/arch/conan/gptj-arch-common.yml
- augment_configs/pretrain/arch/conan/gptj-runtime-common.yml
- augment_configs/pretrain/arch/conan/sizes/1400M-model-def.yml

determined:
  name: 1400M-160Btoken-pretrain-seqlen8192-8node
  description: null
  workspace: Dev
  project: pretrain
  labels: ["1400", "pretrain"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 64
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: 1400M-160Btoken-pretrain-seqlen8192-8node
  wandb_group: 1400M-160Btoken-pretrain-seqlen8192-8node

  # 1024k tokens per batch
  train_micro_batch_size_per_gpu: 2
  gradient_accumulation_steps: 1
  train_batch_size: 128
  max-position-embeddings: 8192
  seq-length: 8192

  # 160k steps @ 1024k tokens ~ 160B tokens
  lr_decay_iters: 160000
  train_iters: 160000
  warmup: 0.01875  # 3k iters

{
 "pipe-parallel-size": 1,
 "model-parallel-size": 1,

 "num-layers": 24,
 "hidden-size": 1024,
 "num-attention-heads": 16,
 "seq-length": 2048,
 "max-position-embeddings": 2048,
 "pos-emb": "rotary",
 "rotary-pct": 0.25,
 "no-weight-tying": true,
 "gpt-j-residual": true,
 "output-layer-parallelism": "column",

 "scaled-upper-triang-masked-softmax-fusion": true,
 "bias-gelu-fusion": true,

 "init_method": "small_init",
 "output_layer_init_method": "wang_init",

 "optimizer": {
 "type": "Adam",
 "params": {
 "lr": 0.0003,
 "betas": [0.9, 0.95],
 "eps": 1.0e-8
 }
 },
 "min_lr": 0.00003,

 "zero_optimization": {
 "stage": 1,
 "allgather_partitions": true,
 "allgather_bucket_size": 500000000,
 "overlap_comm": true,
 "reduce_scatter": true,
 "reduce_bucket_size": 500000000,
 "contiguous_gradients": true,
 "cpu_offload": false
 },

 #"train_micro_batch_size_per_gpu": 16,
 #"gradient-accumulation-steps": 2,
 "data-impl": "mmap",
 "num_workers": 1,

 "checkpoint-activations": true,
 "checkpoint-num-layers": 1,
 "partition-activations": true,
 "synchronize-each-layer": true,

 "gradient_clipping": 1.0,
 "weight-decay": 0.1,
 "hidden-dropout": 0,
 "attention-dropout": 0,

 "fp16": {
 "fp16": true,
 "enabled": true,
 "loss_scale": 0,
 "loss_scale_window": 1000,
 "initial_scale_power": 12,
 "hysteresis": 2,
 "min_loss_scale": 1
 },

 #"train-iters": 572000,
 #"lr-decay-iters": 572000,
 "distributed-backend": "nccl",
 "lr-decay-style": "cosine",
 "warmup": 0.01,
 #"checkpoint-factor": 4000,
 #"eval-interval": 40000,
 #"eval-iters": 10,

 "log-interval": 100,
 "steps_per_print": 10,
 "wall_clock_breakdown": true,

 "data-path": "/mnt/augment/data/processed/the-stack-dedup.2022-11-19/doc_text_document",

 #"vocab-file": "/fsx/pile/20B_tokenizer.json",
 #"tokenizer-type": "HFTokenizer",
 "tokenizer-type": "CodeGenTokenizer",

 "use_wandb": true,
 "wandb_project": "pythia"

}

attention-dropout: 0.0
bias-gelu-fusion: true
checkpoint-activations: true
checkpoint-num-layers: 1
data-impl: mmap
data-path: /mnt/augment/data/processed/the-stack-dedup.2022-11-19/doc_text_document
distributed-backend: nccl
final_linear_bias: true
finetune: true
fp16:
  enabled: true
  hysteresis: 2
  initial_scale_power: 12
  loss_scale: 0
  loss_scale_window: 1000
  min_loss_scale: 1
gpt_j_residual: true
gradient_clipping: 1.0
hidden-dropout: 0.0
hidden-size: 4096
init_method: small_init
keep-last-n-checkpoints: 1
log-interval: 100
lr-decay-style: cosine
make_vocab_size_divisible_by: 51200
max-position-embeddings: 2048
memorize_mode: train
min_lr: 1.2e-5
no_weight_tying: true
norm: layernorm
num-attention-heads: 16
num-layers: 33
optimizer:
  params:
    betas:
    - 0.9
    - 0.95
    eps: 1.0e-08
    lr: 0.00012
  type: Adam
output_layer_init_method: wang_init
partition-activations: true
pipe-parallel-size: 1
pos-emb: rotary
rotary_interleave: true
rotary_pct: 0.25
scaled-upper-triang-masked-softmax-fusion: true
seq-length: 2048
steps_per_print: 10
synchronize-each-layer: true
tokenizer_type: CodeGenTokenizer
train_micro_batch_size_per_gpu: 12
use_post_attn_norm: false
vocab_file: none
wall_clock_breakdown: true
wandb_project: codegen
weight-decay: 0.1
zero_optimization:
  allgather_bucket_size: 500000000
  allgather_partitions: true
  contiguous_gradients: true
  cpu_offload: false
  overlap_comm: true
  reduce_bucket_size: 500000000
  reduce_scatter: true
  stage: 1

# Data Card: https://www.notion.so/StarCoder-pretraining-dataset-with-FIM-39bc0e401bac488f8807aad7298d332d
# NOTE: this data was generated using a sequence length of 4096 and using the StarCoder tokenizer.
tokenizer_type: StarCoderTokenizer
data_path: null
train_data_paths: ["/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_split/dataset"]
valid_data_paths: ["/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_split/validation_dataset"]
test_data_paths: ["/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_split/validation_dataset"]
# Full dataset contains 16_575 examples. 16_512 is divisble by 128 and 192.
max_valid_data_size: 16_512
data_impl: mmap

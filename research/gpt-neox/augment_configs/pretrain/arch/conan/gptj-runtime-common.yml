# Tokenizer
tokenizer_type: CodeGenTokenizer

# Model parallelism
model_parallel_size: 1
pipe_parallel_size: 1  # TODO(carl): update to 0 by default

# Misc optimizations
bias_gelu_fusion: true
checkpoint_activations: true
checkpoint_num_layers: 1
distributed_backend: nccl
partition_activations: true
flash_attention: True
scaled_upper_triang_masked_softmax_fusion: true
synchronize_each_layer: true

# Deepspeed: fp16 and zero
fp16:
  enabled: true
  hysteresis: 2
  initial_scale_power: 12
  loss_scale: 0
  loss_scale_window: 1000
  min_loss_scale: 1
wall_clock_breakdown: true
zero_optimization:
  allgather_bucket_size: 500000000
  allgather_partitions: true
  contiguous_gradients: true
  cpu_offload: false
  overlap_comm: false
  reduce_bucket_size: 500000000
  reduce_scatter: true
  stage: 1

# Checkpointing and logging
finetune: true  # TODO(carl): this actually means "pretrain"
keep_last_n_checkpoints: 1
log_interval: 100
steps_per_print: 10
wandb_project: pretrain

# Eval/save frequency
eval_interval: 500
save_interval: 2500

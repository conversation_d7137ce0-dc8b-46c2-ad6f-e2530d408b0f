# Core architecture
gpt_j_residual: false
final_linear_bias: false
no_weight_tying: true
self_attention_bias: false
mlp_bias: false
activation: "swiglu"

# Positional embeddings
pos_emb: rotary
rotary_interleave: true

# Seqlen and vocab
max_position_embeddings: 4096
seq_length: 4096
make_vocab_size_divisible_by: 51200

# Regularization and normalization
attention_dropout: 0.0
hidden_dropout: 0.0
norm: rmsnorm
use_post_attn_norm: true
weight_decay: 0.1

# Initialization
init_method: small_init
output_layer_init_method: wang_init

# Optimization
gradient_clipping: 1.0
lr_decay_style: cosine

# Eval config to report scores for individual java repos
eval_batch_size: 1
eval_tasks: [
    "gitrepo-java_projects/1160-Scouting-App",
    "gitrepo-java_projects/AndProject",
    "gitrepo-java_projects/Clara",
    "gitrepo-java_projects/Journal.IO",
    "gitrepo-java_projects/MCBans",
    "gitrepo-java_projects/PipesJ2ME",
    "gitrepo-java_projects/RPS",
    "gitrepo-java_projects/SnippingTool",
    "gitrepo-java_projects/android-tether",
    "gitrepo-java_projects/bioclipse.opentox",
    "gitrepo-java_projects/camel-aries-blueprint-tutorial",
    "gitrepo-java_projects/confcaller",
    "gitrepo-java_projects/disruptor_1",
    "gitrepo-java_projects/edsdk4j",
    "gitrepo-java_projects/gat",
    "gitrepo-java_projects/hands-on-spring-batch",
    "gitrepo-java_projects/ibatis-handling-joins",
    "gitrepo-java_projects/jbpm-plugin",
    "gitrepo-java_projects/koku-service-api",
    "gitrepo-java_projects/logmx",
    "gitrepo-java_projects/mod-installer",
    "gitrepo-java_projects/naturvielfalt_android",
    "gitrepo-java_projects/osgiutils_1",
    "gitrepo-java_projects/postmark-java",
    "gitrepo-java_projects/servletjspdemo",
    "gitrepo-java_projects/spring-social-google",
    "gitrepo-java_projects/thucydides-showcase",
    "gitrepo-java_projects/twitterdroid",
    "gitrepo-java_projects/vraptor-dash",
    "gitrepo-java_projects/xebia-petclinic",
    "gitrepo-java_projects/GrandUI",
    "gitrepo-java_projects/fasthat",
    "gitrepo-java_projects/jADT",
    "gitrepo-java_projects/d2rq",
]

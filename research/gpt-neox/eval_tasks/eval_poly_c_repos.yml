# Eval config to report scores for individual C repos without memories
eval_batch_size: 1
eval_tasks: [
## POLY_C_SMALL
"gitrepo-poly-jtoty/Libftest",
"gitrepo-poly-SkBlaz/rakun",
"gitrepo-poly-gbdev/rgbds",
"gitrepo-poly-dashanji/FreeFlyOS",
"gitrepo-poly-zpl-c/zpl",
## POLY_C
"gitrepo-poly-TheOfficialFloW/Adrenaline",
"gitrepo-poly-openlgtv/epk2extract",
"gitrepo-poly-fastpm/fastpm",
"gitrepo-poly-seahorn/clam",
"gitrepo-poly-stellar/stellar-core",
"gitrepo-poly-aerospike/aerospike-client-c",
"gitrepo-poly-nmikhailov/Validity90",
"gitrepo-poly-solanum-ircd/solanum",
"gitrepo-poly-xiaoyeli/superlu",
"gitrepo-poly-gmc-holle/xfdashboard",
"gitrepo-poly-PIGDevUff/PigDev",
"gitrepo-poly-epics-base/epics-base",
"gitrepo-poly-Squirrel-Engine/Squirrel-Engine",
"gitrepo-poly-OP-TEE/optee_test",
"gitrepo-poly-emweb/wt",
"gitrepo-poly-tongban/Learning-DIY-RTOS",
"gitrepo-poly-openzfsonwindows/ZFSin",
"gitrepo-poly-RT-Thread/rtthread-nano",
"gitrepo-poly-FreeRDP/FreeRDP",
"gitrepo-poly-msm8916-mainline/lk2nd",
"gitrepo-poly-alibaba/genie-bt-mesh-stack",
"gitrepo-poly-openvehicles/Open-Vehicle-Monitoring-System-3",
"gitrepo-poly-Caltech-IPAC/Montage",
"gitrepo-poly-CloverHackyColor/CloverBootloader",
"gitrepo-poly-Esenthel/EsenthelEngine",
]

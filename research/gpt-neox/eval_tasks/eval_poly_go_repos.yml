# Eval config to report scores for individual Go repos without memories
eval_batch_size: 1
eval_tasks: [
## POLY_GO_SMALL
"gitrepo-poly-Azure/custom-script-extension-linux",
"gitrepo-poly-railwayapp/cli",
"gitrepo-poly-apolloconfig/agollo",
"gitrepo-poly-cep21/circuit",
"gitrepo-poly-seashell/drago",
## POLY_GO
"gitrepo-poly-bspaans/jit-compiler",
"gitrepo-poly-royalrick/weapp",
"gitrepo-poly-stelligent/mu",
"gitrepo-poly-tsaikd/gogstash",
"gitrepo-poly-i-love-flamingo/flamingo",
"gitrepo-poly-okta/okta-sdk-golang",
"gitrepo-poly-banzaicloud/istio-operator",
"gitrepo-poly-mitchellh/go-glint",
"gitrepo-poly-kubernetes-sigs/gateway-api",
"gitrepo-poly-Tnze/go-mc",
"gitrepo-poly-blushft/go-diagrams",
"gitrepo-poly-tendermint/liquidity",
"gitrepo-poly-go-spring/go-spring",
"gitrepo-poly-cloudwan/gohan",
"gitrepo-poly-VictoriaMetrics/operator",
"gitrepo-poly-purpleidea/mgmt",
"gitrepo-poly-XTLS/Xray-core",
"gitrepo-poly-influxdata/influx-stress",
"gitrepo-poly-kubermatic/kubermatic",
"gitrepo-poly-gochain/gochain",
"gitrepo-poly-crossplane/provider-aws",
"gitrepo-poly-ZTE/Knitter",
"gitrepo-poly-dddengyunjie/fabric",
"gitrepo-poly-openshift/machine-config-operator",
]

# Eval config to report scores for individual C++ repos without memories
eval_batch_size: 1
eval_tasks: [
## POLY_CPP_SMALL
"gitrepo-poly-Dev-XYS/Algorithms",
"gitrepo-poly-jatinchowdhury18/AnalogTapeModel",
"gitrepo-poly-adafruit/Adafruit_EPD",
"gitrepo-poly-kharchenkolab/dropEst",
"gitrepo-poly-TranslucentTB/TranslucentTB",
## POLY_CPP
"gitrepo-poly-StrikerX3/virt86",
"gitrepo-poly-openzim/libzim",
"gitrepo-poly-ad-freiburg/pfaedle",
"gitrepo-poly-thuhcsi/Crystal",
"gitrepo-poly-cogsys-tuebingen/gerona",
"gitrepo-poly-Apress/pro-TBB",
"gitrepo-poly-RoboCup-SSL/ssl-vision",
"gitrepo-poly-x64dbg/TitanEngine",
"gitrepo-poly-gtri/scrimmage",
"gitrepo-poly-arximboldi/immer",
"gitrepo-poly-mongodb/mongo-cxx-driver",
"gitrepo-poly-eric2003/OneFLOW",
"gitrepo-poly-TheCherno/Hazel",
"gitrepo-poly-SourMesen/Mesen",
"gitrepo-poly-paboyle/Grid",
"gitrepo-poly-KDE/kdenlive",
"gitrepo-poly-LunarG/gfxreconstruct",
"gitrepo-poly-ripple/rippled",
"gitrepo-poly-apache/incubator-pagespeed-mod",
"gitrepo-poly-HTMLCOIN/HTMLCOIN",
"gitrepo-poly-opencurve/curve",
"gitrepo-poly-id-Software/DOOM-3-BFG",
"gitrepo-poly-spring/spring",
"gitrepo-poly-scp-fs2open/fs2open.github.com",
"gitrepo-poly-crossuo/crossuo",
]

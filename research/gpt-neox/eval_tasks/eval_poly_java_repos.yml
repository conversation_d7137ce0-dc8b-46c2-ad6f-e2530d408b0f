# Eval config to report scores for individual java repos without memories
eval_batch_size: 1
eval_tasks: [
## POLY_JAVA_SMALL
"gitrepo-poly-codecentric/cxf-spring-boot-starter",
"gitrepo-poly-kekingcn/kkbinlog",
"gitrepo-poly-maxpower47/PinDroid",
"gitrepo-poly-kinow/testlink-java-api",
"gitrepo-poly-protocol7/quincy",
## POLY_JAVA
"gitrepo-poly-AlmasB/FXGLGames",
"gitrepo-poly-LMBishop/Quests",
"gitrepo-poly-perye/dokit",
"gitrepo-poly-divolte/divolte-collector",
"gitrepo-poly-stevensouza/jamonapi",
"gitrepo-poly-xilibi2003/Upchain-wallet",
"gitrepo-poly-stephenc/java-iso-tools",
"gitrepo-poly-nvllsvm/Audinaut",
"gitrepo-poly-dunwu/java-tutorial",
"gitrepo-poly-IanDarwin/Android-Cookbook-Examples",
"gitrepo-poly-microsoft/gctoolkit",
"gitrepo-poly-caojx-git/learn-java-codes",
"gitrepo-poly-amaembo/streamex",
"gitrepo-poly-metaborg/spoofax",
"gitrepo-poly-pranavpandey/dynamic-support",
"gitrepo-poly-lwjglgamedev/vulkanbook",
"gitrepo-poly-ThreeTen/threetenbp",
"gitrepo-poly-openremote/openremote",
"gitrepo-poly-DarLiner/openzaly",
"gitrepo-poly-h2database/h2database",
"gitrepo-poly-tronprotocol/java-tron",
"gitrepo-poly-eclipse/jetty.project",
"gitrepo-poly-eclipse/deeplearning4j",
"gitrepo-poly-consulo/consulo",
]

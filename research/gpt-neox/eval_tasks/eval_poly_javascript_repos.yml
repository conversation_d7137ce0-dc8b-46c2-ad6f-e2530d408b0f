# Eval config to report scores for individual javascript repos without memories
eval: 1
eval_tasks: [
## POLY_JAVASCRIPT_SMALL
"gitrepo-poly-thlorenz/browserify-shim",
"gitrepo-poly-Pocket/extension-save-to-pocket",
"gitrepo-poly-trailheadapps/lwc-recipes-oss",
"gitrepo-poly-service-mocker/service-mocker",
"gitrepo-poly-apify/apify-js",
## POLY_JAVASCRIPT
"gitrepo-poly-daydaylee1227/Blog",
"gitrepo-poly-emotion-js/next",
"gitrepo-poly-fossasia/badgeyay",
"gitrepo-poly-jrgarciadev/dev-cover",
"gitrepo-poly-synclounge/synclounge",
"gitrepo-poly-trazyn/ieaseMusic",
"gitrepo-poly-Popmotion/popmotion",
"gitrepo-poly-opentok/opentok-rtc",
"gitrepo-poly-eBay/ebayui-core",
"gitrepo-poly-showdownjs/showdown",
"gitrepo-poly-johnko<PERSON>huk/grabient",
"gitrepo-poly-commercetools/ui-kit",
"gitrepo-poly-pryv/open-pryv.io",
"gitrepo-poly-ibm-js/delite",
"gitrepo-poly-theintern/intern-examples",
"gitrepo-poly-0wczar/airframe-react",
"gitrepo-poly-felipemanga/ProjectABE",
"gitrepo-poly-Heavy-Division/B78XH",
"gitrepo-poly-openforis/sepal",
"gitrepo-poly-KaliedaRik/Scrawl-canvas",
"gitrepo-poly-overleaf/web",
"gitrepo-poly-hobu/usgs-lidar",
"gitrepo-poly-geoadmin/mf-geoadmin3",
"gitrepo-poly-ONLYOFFICE/sdkjs",
"gitrepo-poly-Tencent/omi",
]

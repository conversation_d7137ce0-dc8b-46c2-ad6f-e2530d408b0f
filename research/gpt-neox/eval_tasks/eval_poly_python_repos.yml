# Eval config to report scores for individual python repos without memories
eval_batch_size: 1
eval_tasks: [
## POLY_PYTHON_SMALL
"gitrepo-poly-betamaxpy/betamax",
"gitrepo-poly-rapidsms/rapidsms",
"gitrepo-poly-wal-e/wal-e",
"gitrepo-poly-gaogaotiantian/viztracer",
"gitrepo-poly-happyjared/python-learning",
## POLY_PYTHON
"gitrepo-poly-cpnota/autonomous-learning-library",
"gitrepo-poly-araffin/robotics-rl-srl",
"gitrepo-poly-errbotio/errbot",
"gitrepo-poly-lululxvi/deepxde",
"gitrepo-poly-python-discord/sir-lancebot",
"gitrepo-poly-freelunchtheorem/Conditional_Density_Estimation",
"gitrepo-poly-PythonJS/PythonJS",
"gitrepo-poly-ganglia/gmond_python_modules",
"gitrepo-poly-jim-schwoebel/voicebook",
"gitrepo-poly-gpodder/gpodder",
"gitrepo-poly-ARISE-Initiative/robosuite",
"gitrepo-poly-jimmysong/programmingbitcoin",
"gitrepo-poly-thunlp/BERT-KPE",
"gitrepo-poly-mozilla-services/socorro",
"gitrepo-poly-Freeseer/freeseer",
"gitrepo-poly-SHI-Labs/Decoupled-Classification-Refinement",
"gitrepo-poly-facebookresearch/ReAgent",
"gitrepo-poly-Clinical-Genomics/scout",
"gitrepo-poly-sio2project/oioioi",
"gitrepo-poly-pytorch/vision",
"gitrepo-poly-Yelp/paasta",
"gitrepo-poly-vawser/Cinders-DS3",
"gitrepo-poly-alan-turing-institute/sktime",
"gitrepo-poly-web2py/web2py",
"gitrepo-poly-ansible-collections/community.general",
]

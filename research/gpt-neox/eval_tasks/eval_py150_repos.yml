
eval_batch_size: 1
eval_tasks: [
  "gitrepo-aldebaran/qibuild",
  "gitrepo-openstack/python-openstacksdk",
  "gitrepo-omab/python-social-auth",
  "gitrepo-BrightcoveOS/Diamond",
  "gitrepo-ReactiveX/RxPY",
  "gitrepo-django-leonardo/django-leonardo",
  "gitrepo-MirantisWorkloadMobility/CloudFerry",
  "gitrepo-Exa-Networks/exabgp",
  "gitrepo-hazelcast/hazelcast-python-client",
  "gitrepo-smart-on-fhir/client-py",
  "gitrepo-daviddrysdale/python-phonenumbers",
  "gitrepo-StackStorm/st2contrib",
  "gitrepo-divio/django-cms",
  "gitrepo-dokterbob/satchmo",
  "gitrepo-mollyproject/mollyproject",
  "gitrepo-gevent/gevent",
  "gitrepo-Esri/solutions-geoprocessing-toolbox",
  "gitrepo-openhatch/oh-mainline",
  "gitrepo-glue-viz/glue",
  "gitrepo-torchbox/wagtail",
  "gitrepo-iskandr/parakeet",
  "gitrepo-robotframework/robotframework",
  "gitrepo-django-oscar/django-oscar",
  "gitrepo-openstack/solum",
  "gitrepo-OpenCobolIDE/OpenCobolIDE",
  "gitrepo-EricssonResearch/calvin-base",
  "gitrepo-yasoob/youtube-dl-GUI",
  "gitrepo-mozilla/moztrap",
  "gitrepo-spulec/moto",
  "gitrepo-fabric-engine/Kraken",
]

# includes is an ordered list of gpt-neox config files to be loaded
includes:
- augment_configs/codegen-H/model/codegen-350M.stage1.yml
- augment_configs/codegen-H/train/big_batch.f.yml
- augment_configs/codegen-H/train/train_memory.yml
- augment_configs/codegen-H/lr/3e-4.yml
- augment_configs/codegen-H/mem/prelayerL16.yml
- augment_configs/codegen-H/mem/ignore_eod.yml
- augment_configs/codegen-H/mem/memory_train_on_gpu.yml
# determined is a dictionary of determined.ai specific arguments that have no corresponding gpt-neox arguments
determined:
  name: GH GC
  description: Short train w/GC
  workspace: GH
  project: training
  max_restarts: 0
  perform_initial_validation: False  # Do a validation at iteration 0
# augment is a dictionary of augment specific arguments for our code extensions
augment:
  # Common args for both training and evaluation
  podspec_path: "gpu-small.yaml"  # Path to the podspec file.  Absolute, or relative to "templates"
  gpu_count: 1  # How many GPUs to ask for
  save_trial_best: 0  # How many of the best checkpoints to save
  environment_variables:
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"  # For training
  checkpoint_handling:
    action: gc  # "gc", "eval" or None
overrides:
  # WandB options. WANDB_API_KEY will come from the environment, or ~/.netrc if you are logged in.
  wandb_name: trial-$TRIAL_ID
  wandb_project: my_project   # This probably needs to already exist in your wandb dashboard
  wandb_group: exp-$EXPERIMENT_ID
  data_path: /mnt/efs/augment/data/processed/github/github_small_text_document
  load: /mnt/efs/augment/checkpoints/codegen-350M-multi
  train_batch_size: 288
  train_micro_batch_size_per_gpu: 12
  gradient_accumulation_steps: 24

  early_stopping: False
  early_stopping_threshold: 0.005

  warmup: 0.
  lr_decay_style: constant

  optimizer:
      params:
          betas:
          - 0.9
          - 0.95
          eps: 1.0e-08
          lr: 1.6e-05
      type: Adam

  keep_last_n_checkpoints: 2
  # to keep all checkpoints, also disable determined.enable_checkpoint_gc
  #keep_last_n_checkpoints: null
  no_save_optim: False

  eval_interval: 100
  train_iters: 10
  lr_decay_iters: 10  # If not set, defaults to train_iters
  save_interval: 2  # Save every other iteration to increase the CP count to GC

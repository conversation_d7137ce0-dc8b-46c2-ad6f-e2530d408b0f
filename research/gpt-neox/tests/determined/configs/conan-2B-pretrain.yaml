includes:
- augment_configs/pretrain/conan/model/conan-2B.yml

determined:
  name: Conan 2B Pretrain Test
  description: Pretraining 2B model for accuracy and throughput
  workspace: GH
  project: training
  perform_initial_validation: True  # Do a validation at iteration 0
  scheduling_unit: 100

augment:
  podspec_path: "8xA100.yaml"  # Path to the podspec file.  Absolute, or relative to "templates"
  gpu_count: 8  # How many GPUs to ask for
  checkpoint_handling:
    action: None

overrides:
  use_wandb: False
  data_path: /mnt/efs/augment/data/processed/github/github_small_text_document

  train-iters: 100
  eval-interval: 50
  eval-iters: 32

  lr-decay-iters: 15000
  warmup: 0.2

  train_batch_size: 96
  train_micro_batch_size_per_gpu: 12
  gradient_accumulation_steps: 1

  # Try not to save checkpoints (determined still takes an end-of-experiment checkpoint)
  save_interval: 1000
  no_save_optim: true
  keep_last_n_checkpoints: 0

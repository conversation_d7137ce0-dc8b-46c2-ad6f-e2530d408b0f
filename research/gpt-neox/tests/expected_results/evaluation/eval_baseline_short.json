{"results": {"gitrepo-java_projects/1160-Scouting-App": {"word_perplexity": 6.468915820608409, "byte_perplexity": 1.1558670827096005, "bits_per_byte": 0.2089755067133163, "token_perplexity": 1.5270472224053744}}, "versions": {"gitrepo-java_projects/1160-Scouting-App": 3.0}, "config": {"model": "neox", "model_args": {"contrastive": false, "total_model_params": 2805908481, "embedding_model_params": 262195200, "memory_config": {"15": {"style": "prelayer"}}, "memory_size": 32768, "memorize_mode": "host", "memory_invalid_query_mode": "ignore_eod", "num_memory_write_heads": null, "memory_save": "mem", "memory_load": "mem", "memory_train_on_gpu": true, "memory_partition_count": 24, "memorize_files": null, "memorize_filelist": null, "memory_knn_top_k": 32, "memorize_report": null, "distributed_backend": "nccl", "local_rank": 0, "rank": 0, "lazy_mpu_init": false, "short_seq_prob": 0.1, "attn_mask_mode": "causal", "loss_mask_mode": "none", "adlr_autoresume": false, "adlr_autoresume_interval": 1000, "seed": 1234, "onnx_safe": false, "deepscale": false, "deepscale_config": null, "deepspeed_mpi": false, "user_script": "evaluate.py", "iteration": 56000, "do_train": null, "do_valid": null, "do_test": null, "global_num_gpus": 1, "text_gen_type": "unconditional", "temperature": 0.0, "top_p": 0.0, "top_k": 0, "maximum_tokens": 64, "sample_input_file": null, "sample_output_file": "samples.txt", "num_samples": 1, "recompute": false, "eval_results_prefix": "eval_check", "eval_tasks": ["gitrepo-java_projects/1160-Scouting-App"], "memory_object_names": null, "memory_object_names_file": null, "eval_batch_size": 1, "eval_with_memories": true, "eval_tags": null, "retrieval_index": null, "similarity_cutoff": 0.5, "ngram_size": 20, "path_delimiter": "\n", "document_delimiter": "<|ret-endofdoc|>", "use_wandb": true, "wandb_group": "Fkwu92TBncmjD8paVPSSTi", "wandb_team": null, "wandb_project": "codegen", "wandb_host": "https://api.wandb.ai", "wandb_name": "batch_distract codegen-2B.stage1 big_batch.f.p4 train_memory 3e-4 prelayerL16 ignore_eod memory_train_on_gpu memsize-32768", "wandb_init_all_ranks": false, "git_hash": "ecd9ef1b", "log_dir": null, "tensorboard_dir": null, "log_interval": 100, "log_grad_pct_zeros": false, "log_param_norm": false, "log_grad_norm": false, "log_optimizer_states": false, "log_gradient_noise_scale": false, "gradient_noise_scale_n_batches": 5, "gradient_noise_scale_cpu_offload": false, "pipe_parallel_size": 1, "model_parallel_size": 1, "pipe_partition_method": "type:transformer|mlp", "world_size": 1, "is_pipe_parallel": true, "data_path": "/mnt/efs/augment/data/github/github_small_text_document", "train_data_paths": null, "test_data_paths": null, "valid_data_paths": null, "dataset_type": "gpt2", "train_data_weights": null, "valid_data_weights": null, "test_data_weights": null, "weight_by_num_documents": false, "weighted_sampler_alpha": 0.3, "data_impl": "mmap", "mmap_warmup": false, "save": null, "config_files": {"config.yml": "attention-dropout: 0.0\ncheckpoint-activations: true\ncheckpoint-num-layers: 1\ndata-impl: mmap\ndata-path: /mnt/efs/augment/data/github/github_small_text_document\ndistributed-backend: nccl\neval-interval: 40\neval-iters: 32\nfinal_linear_bias: true\nfinetune: true\nfp16:\n  enabled: true\n  hysteresis: 2\n  loss_scale: 0\n  loss_scale_window: 1000\n  min_loss_scale: 1\ngpt_j_residual: true\ngradient_accumulation_steps: 24\ngradient_clipping: 1.0\nhidden-dropout: 0.0\nhidden-size: 2560\nkeep-last-n-checkpoints: 1\nload: /mnt/efs/augment/checkpoints/codegen-2B.ida.batch_distract.2022-10-11\nlog-interval: 100\nlr-decay-iters: 100000\nlr-decay-style: cosine\nmake_vocab_size_divisible_by: 51200\nmax-position-embeddings: 2048\nmem_friendly_batch: true\nmemorize_mode: train\nmemory_config:\n  15:\n    style: prelayer\nmemory_invalid_query_mode: ignore_eod\nmemory_load: /mnt/efs/augment/mem\nmemory_save: /mnt/efs/augment/mem\nmemory_size: 32768\nmemory_train_on_gpu: true\nno_weight_tying: true\nnorm: layernorm\nnum-attention-heads: 32\nnum-layers: 32\noptimizer:\n  params:\n    betas:\n    - 0.9\n    - 0.999\n    eps: 1.0e-08\n    lr: 0.0003\n  type: Adam\npartition-activations: true\npipe-parallel-size: 1\npos-emb: rotary\nrotary_interleave: true\nrotary_pct: 0.8\nsave-interval: 500\nscaled-upper-triang-masked-softmax-fusion: false\nseq-length: 1024\nsteps_per_print: 10\nsynchronize-each-layer: true\ntokenizer_type: CodeGenTokenizer\ntrain-iters: 100000\ntrain_batch_size: 288\ntrain_micro_batch_size_per_gpu: 12\ntrain_only: dummy|_mem\nuse_post_attn_norm: false\nvocab_file: none\nwall_clock_breakdown: true\nwandb_name: batch_distract codegen-2B.stage1 big_batch.f.p4 train_memory 3e-4 prelayerL16\n  ignore_eod memory_train_on_gpu memsize-32768\nwandb_project: codegen\nwarmup: 0.01\nweight-decay: 0.1\nzero_optimization:\n  allgather_bucket_size: 500000000\n  allgather_partitions: true\n  contiguous_gradients: true\n  cpu_offload: false\n  overlap_comm: true\n  reduce_bucket_size: 500000000\n  reduce_scatter: true\n  stage: 1\n", "small_batch.yml": "\neval_batch_size: 1\n", "use_memory.yml": "eval_with_memories: True\n"}, "load": "/mnt/efs/augment/checkpoints/codegen-2B.ida.batch_distract.2022-10-11", "checkpoint_validation_with_forward_pass": false, "save_interval": 500, "no_save_optim": false, "no_save_rng": false, "no_load_optim": true, "no_load_rng": true, "finetune": false, "batch_size": 12, "train_iters": 100000, "eval_iters": 32, "keep_last_n_checkpoints": 1, "eval_interval": 40, "early_stopping": false, "early_stopping_metric": "lm_loss", "early_stopping_threshold": 0.01, "split": "969, 30, 1", "vocab_file": "none", "merge_file": null, "num_workers": 2, "exit_interval": null, "attention_dropout": 0.0, "hidden_dropout": 0.0, "weight_decay": 0.1, "checkpoint_activations": false, "checkpoint_num_layers": 1, "deepspeed_activation_checkpointing": true, "contiguous_checkpointing": false, "checkpoint_in_cpu": false, "synchronize_each_layer": true, "profile_backward": false, "partition_activations": false, "gas": 24, "clip_grad": 1.0, "hysteresis": 2, "dynamic_loss_scale": true, "loss_scale": null, "loss_scale_window": 1000.0, "min_scale": 1.0, "char_level_ppl": false, "mem_friendly_batch": true, "train_only": "dummy|_mem", "tokenizer_type": "CodeGenTokenizer", "padded_vocab_size": 51200, "optimizer_type": "<PERSON>", "use_bnb_optimizer": false, "zero_stage": 0, "zero_reduce_scatter": true, "zero_contiguous_gradients": false, "zero_reduce_bucket_size": 500000000, "zero_allgather_bucket_size": 500000000, "lr": 0.0003, "lr_decay_style": "cosine", "lr_decay_iters": 100000, "min_lr": 0.0, "warmup": 0.01, "override_lr_scheduler": false, "use_checkpoint_lr_scheduler": false, "precision": "fp16", "num_layers": 32, "hidden_size": 2560, "num_attention_heads": 32, "seq_length": 1024, "max_position_embeddings": 2048, "norm": "layernorm", "layernorm_epsilon": 1e-05, "rms_norm_epsilon": 1e-08, "scalenorm_epsilon": 1e-08, "pos_emb": "rotary", "rotary_interleave": true, "rpe_num_buckets": 32, "rpe_max_distance": 128, "no_weight_tying": true, "attention_config": ["global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global"], "sparsity_config": {}, "num_unique_layers": null, "param_sharing_style": "grouped", "make_vocab_size_divisible_by": 51200, "activation": "gelu", "scaled_upper_triang_masked_softmax_fusion": false, "scaled_masked_softmax_fusion": false, "bias_gelu_fusion": false, "bias_dropout_fusion": false, "fp16_lm_cross_entropy": false, "init_method_std": 0.02, "apply_query_key_layer_scaling": false, "use_cpu_initialization": false, "attention_softmax_in_fp32": false, "rotary_pct": 0.8, "rotary_emb_base": 10000, "init_method": "normal", "output_layer_init_method": "scaled_normal", "gmlp_attn_dim": 64, "gpt_j_residual": true, "soft_prompt_tuning": null, "output_layer_parallelism": "column", "use_post_attn_norm": false, "final_linear_bias": true, "deepspeed": true, "train_batch_size": 288, "train_micro_batch_size_per_gpu": 12, "gradient_accumulation_steps": 24, "optimizer": {"params": {"betas": [0.9, 0.999], "eps": 1e-08, "lr": 0.0003}, "type": "<PERSON>"}, "scheduler": null, "fp32_allreduce": false, "prescale_gradients": false, "gradient_predivide_factor": 1.0, "sparse_gradients": false, "fp16": {"enabled": true, "hysteresis": 2, "loss_scale": 0, "loss_scale_window": 1000, "min_loss_scale": 1}, "amp": null, "gradient_clipping": 1.0, "zero_optimization": {"stage": 0, "allgather_partitions": true, "allgather_bucket_size": 500000000, "overlap_comm": false, "reduce_scatter": true, "reduce_bucket_size": 500000000, "contiguous_gradients": false, "cpu_offload": false}, "steps_per_print": 10, "wall_clock_breakdown": true, "dump_state": false, "flops_profiler": null, "zero_allow_untested_optimizer": false, "hostfile": null, "include": null, "exclude": null, "num_nodes": -1, "num_gpus": null, "master_port": 29500, "master_addr": null, "launcher": "pdsh", "detect_nvlink_pairs": false}, "num_fewshot": 0, "batch_size": 1, "device": "cuda:0", "no_cache": false, "limit": null, "bootstrap_iters": 10000, "description_dict": null, "duration": 18.448922157287598, "eval_with_memories": true, "eval_tags": null}}
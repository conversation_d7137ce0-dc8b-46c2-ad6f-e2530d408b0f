# Parameters used for text generation
# Make sure `load` is specified somewhere else
{
  # Text gen type: `input-file`, `unconditional` or `interactive`
  "text-gen-type": "unconditional",

  # Params for all
  "maximum_tokens": 102,
  "temperature": 1.0,
  "top_p": 0.0,
  "top_k": 0,
  "recompute": false,

  # `unconditional`: samples
  "num-samples": 10,

  # input/output file
  "sample-input-file": "sample_input.txt",
  "sample-output-file": "sample_output.txt",
}

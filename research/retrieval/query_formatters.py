"""Library for query prompt formatters."""

import logging

from megatron.tokenizer.tokenizer import CodeGenTokenizer, ResearchSpecialTokens
from typing_extensions import override

from base.languages.language_guesser import guess_comment_prefix, guess_language
from base.prompt_format.common import PromptFormatterOutput
from base.prompt_format_retrieve.prompt_formatter import Retriever<PERSON><PERSON><PERSON><PERSON><PERSON>atter
from base.tokenizers.tokenizer import Tokenizer
from research.core import abstract_prompt_formatter
from research.core.model_input import ModelInput
from research.core.tokenizers import get_all_special_tokens, get_tokenizer
from research.utils.inspect_indexed_dataset import (
    escape_control_chars,
    highlight_special_tokens,
)

AbstractPromptFormatter = abstract_prompt_formatter.AbstractPromptFormatter
register_prompt_formatter = abstract_prompt_formatter.register_prompt_formatter

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class AbstractQueryPromptFormatter(
    AbstractPromptFormatter, RetrieverPromptFormatter[ModelInput]
):
    """A query prompt formatter.

    This subclass is a thin wrapper around the abstract prompt formatter that implements
    the base `RetrieverPromptFormatter` interface.
    """

    input_type = ModelInput

    @override
    def format_prompt(self, prompt_input: ModelInput) -> PromptFormatterOutput:
        tokens, _ = self.prepare_prompt(prompt_input)
        return PromptFormatterOutput([tokens])


@register_prompt_formatter("simple_query")
class SimpleQueryFormatter(AbstractQueryPromptFormatter):
    """A query formatter."""

    max_tokens: int = -1
    "Maximum number of allowed tokens. -1 means unlimited."

    max_lines: int = -1
    """Maximum number of allowed lines. -1 means unlimited. """

    add_path: bool = False
    """Whether to prepend path to the query."""

    add_sos: bool = False
    """Whether to prepend the <|startofsequence|> token to the query."""

    tokenizer_name: str = "CodeGenTokenizer"
    """Name of the tokenizer to use."""

    def create_default_tokenizer(self) -> Tokenizer:
        return get_tokenizer(self.tokenizer_name)

    def prepare_prompt(self, model_input: ModelInput) -> tuple[list[int], dict]:
        """Returns tokenized prompt and metadata."""
        text = model_input.prefix
        if self.max_lines and self.max_lines > 0:
            lines = text.splitlines(keepends=True)
            text = "".join(lines[-self.max_lines :])

        # Optionally start with the <|startofsequence|> token
        header_tokens: list[int] = []
        if self.add_sos:
            sos_id = self.tokenizer.special_tokens.start_of_key  # type: ignore
            assert sos_id is not None
            header_tokens = [sos_id]

        # Optionally add path
        if self.add_path and model_input.path:
            header_tokens += self.tokenizer.tokenize_safe(f"{model_input.path}\n")

        # Do not tokenize special tokens for user query.
        prefix_tokens = self.tokenizer.tokenize_safe(text)

        # Trim the prompt to fit into the max_tokens constraint.
        prompt_tokens = None
        if (
            not self.max_tokens
            or self.max_tokens <= 0
            or self.max_tokens >= len(header_tokens) + len(prefix_tokens)
        ):
            # No trimming is needed.
            prompt_tokens = header_tokens + prefix_tokens

        elif self.max_tokens > len(header_tokens):
            # The header fits, but the prefix does not. Trim the prefix from the left.
            prompt_tokens = (
                header_tokens + prefix_tokens[-(self.max_tokens - len(header_tokens)) :]
            )

        else:
            # The header alone does not fit. Trim the header from the right.
            prompt_tokens = header_tokens[: self.max_tokens]

        return prompt_tokens, {}


@register_prompt_formatter("ethanol3_query")
class Ethanol3QueryFormatter(AbstractQueryPromptFormatter):
    """Experimental query formatter for Ethanol3 models.

    This is an experimental formatter because the way we are representing metadata
    (e.g., paths) may change in future models. In particular, using a newline
    character as a terminator for metadata is problematic because it introduces
    the ambiguity of whether the newline is meant to be a character (and so
    potentially fused with adjacent characters) or its own token. We work around this
    with the `retokenize` code path, which in this implementation is not compatible
    with a custom suffix.
    """

    max_tokens: int = -1
    """Maximum number of allowed tokens. -1 means unlimited."""

    max_lines: int = -1
    """Maximum number of allowed lines. -1 means unlimited. """

    add_path: bool = False
    """Whether to prepend path to the query."""

    add_sos: bool = False
    """Whether to prepend the <|startofsequence|> token to the query."""

    add_suffix: bool = False
    """Whether to add a suffix."""

    retokenize: bool = False
    """Whether to retokenize the query to normalize the path metadata."""

    def create_default_tokenizer(self) -> Tokenizer:
        return CodeGenTokenizer()

    def prepare_prompt(self, model_input: ModelInput) -> tuple[list[int], dict]:
        """Returns tokenized prompt and metadata."""
        text = model_input.prefix
        if self.max_lines and self.max_lines > 0:
            lines = text.splitlines(keepends=True)
            text = "".join(lines[-self.max_lines :])

        # Optionally start with the <|startofsequence|> token
        start_of_key = self.tokenizer.special_tokens.start_of_key  # type: ignore
        assert start_of_key is not None
        header_tokens = [start_of_key] if self.add_sos else []

        # Optionally add path
        if self.add_path and model_input.path:
            header_tokens += self.tokenizer.tokenize_safe(f"{model_input.path}\n")

        # Do not tokenize special tokens for user query.
        prefix_tokens = self.tokenizer.tokenize_safe(text)

        if self.add_suffix:
            assert not self.retokenize
            suffix_tokens = [
                self.tokenizer.vocab[b"<|fim-sep|>"]
            ] + self.tokenizer.tokenize_safe(model_input.suffix)
        else:
            suffix_tokens = []

        # Trim the prompt to fit into the max_tokens constraint.

        if (
            not self.max_tokens
            or self.max_tokens <= 0
            or self.max_tokens
            >= len(header_tokens) + len(prefix_tokens) + len(suffix_tokens)
        ):
            # No trimming is needed.
            prompt = header_tokens + prefix_tokens + suffix_tokens

        elif self.max_tokens > len(header_tokens):
            token_budget = self.max_tokens - len(header_tokens)
            # The header fits, but the prefix+suffix do not. Trim prefix/suffix.
            if len(suffix_tokens) == 0:
                prompt = header_tokens + prefix_tokens[-token_budget:]
            else:
                # Prefix + suffix don't entirely fit. Use 2/3 of the remaining space
                # for prefix and remainder for suffix
                prefix_budget = (token_budget * 2) // 3
                suffix_budget = token_budget - prefix_budget
                prompt = (
                    header_tokens
                    + prefix_tokens[-prefix_budget:]
                    + suffix_tokens[:suffix_budget]
                )

        else:
            # The header alone does not fit. Trim the header from the right.
            prompt = header_tokens[: self.max_tokens]

        # Retokenize if needed
        if self.retokenize:
            prompt = self.tokenizer.tokenize_safe(self.tokenizer.detokenize(prompt))[
                : self.max_tokens
            ]
        return prompt, {}


@register_prompt_formatter("ethanol6_query")
class Ethanol6QueryFormatter(AbstractQueryPromptFormatter):
    """Experimental query formatter for Ethanol6 models."""

    max_tokens: int = -1
    """Maximum number of allowed tokens. -1 means unlimited."""

    max_lines: int = -1
    """Maximum number of allowed lines. -1 means unlimited."""

    add_path: bool = False
    """Whether to prepend path to the query."""

    add_suffix: bool = False
    """Whether to add a suffix."""

    prefix_ratio: float = 0.9
    """The ratio of the prefix to the total prompt."""

    tokenizer_name: str = "CodeGenTokenizer"
    """Name of the tokenizer to use."""

    def create_default_tokenizer(self) -> Tokenizer:
        return get_tokenizer(self.tokenizer_name)

    def prepare_prompt(self, model_input: ModelInput) -> tuple[list[int], dict]:
        """Returns tokenized prompt and metadata."""
        text = model_input.prefix
        if self.max_lines and self.max_lines > 0:
            lines = text.splitlines(keepends=True)
            text = "".join(lines[-self.max_lines :])

        special_tokens = self.tokenizer.special_tokens
        assert isinstance(special_tokens, ResearchSpecialTokens)

        # Header tokens
        header_tokens = []
        header_tokens.extend(special_tokens.begin_sequence)

        # Optionally add path
        if self.add_path and model_input.path:
            header_tokens += self.tokenizer.tokenize_safe(model_input.path)
            header_tokens.append(special_tokens.fim_prefix)

        # Do not tokenize special tokens for user query.
        prefix_tokens = self.tokenizer.tokenize_safe(text)

        if self.add_suffix:
            suffix_tokens = [special_tokens.fim_suffix] + self.tokenizer.tokenize_safe(
                model_input.suffix
            )
        else:
            suffix_tokens = []

        # Trim the prompt to fit into the max_tokens constraint.

        if (
            not self.max_tokens
            or self.max_tokens <= 0
            or self.max_tokens
            >= len(header_tokens) + len(prefix_tokens) + len(suffix_tokens)
        ):
            # No trimming is needed.
            prompt = header_tokens + prefix_tokens + suffix_tokens

        elif self.max_tokens > len(header_tokens):
            token_budget = self.max_tokens - len(header_tokens)
            # The header fits, but the prefix+suffix do not. Trim prefix/suffix.
            if len(suffix_tokens) == 0:
                prompt = header_tokens + prefix_tokens[-token_budget:]
            else:
                # Prefix + suffix don't entirely fit. Split the remaining space
                # between the prefix and the suffix proportionately based on
                # prefix_ratio.
                prefix_budget = round(token_budget * self.prefix_ratio)
                suffix_budget = token_budget - prefix_budget
                if prefix_budget > len(prefix_tokens):
                    suffix_budget += prefix_budget - len(prefix_tokens)
                    prefix_budget = len(prefix_tokens)
                if suffix_budget > len(suffix_tokens):
                    prefix_budget += suffix_budget - len(suffix_tokens)
                    suffix_budget = len(suffix_tokens)
                prompt = (
                    header_tokens
                    + prefix_tokens[-prefix_budget:]
                    + suffix_tokens[:suffix_budget]
                )

        else:
            # The header alone does not fit. Trim the header from the right.
            prompt = header_tokens[: self.max_tokens]

        return prompt, {}


@register_prompt_formatter("ethanol6_query_simple_chat")
class Ethanol6QuerySimpleChatFormatter(AbstractQueryPromptFormatter):
    """Experimental query formatter for Ethanol6 models, simplier chat version."""

    max_tokens: int = -1
    """Maximum number of allowed tokens. -1 means unlimited."""

    add_path: bool = False
    """Whether to prepend path to the query."""

    tokenizer_name: str = "CodeGenTokenizer"
    """Name of the tokenizer to use."""

    verbose: bool = False
    """Whether to print debug information."""

    def create_default_tokenizer(self) -> Tokenizer:
        return get_tokenizer(self.tokenizer_name)

    def _get_lang_comment_prefix(self, model_input: ModelInput) -> str:
        lang_comment_prefix = None
        if "lang" in model_input.extra:
            lang = model_input.extra["lang"]
            lang_comment_prefix = guess_comment_prefix(lang)

        if lang_comment_prefix is None:
            lang = guess_language(model_input.path)
            lang_comment_prefix = guess_comment_prefix(lang)

        # Otherwise just default to Python-style comments.
        if lang_comment_prefix is None:
            lang_comment_prefix = "# "
        return lang_comment_prefix

    def _maybe_log_prompt(self, prompt: list[int]):
        """Print query if `verbose` logging is enabled."""
        if self.verbose:
            prompt_text = escape_control_chars(self.tokenizer.detokenize(prompt))
            logger.info("Retriever prompt:")
            logger.info(
                highlight_special_tokens(
                    prompt_text, get_all_special_tokens(self.tokenizer)
                )
            )

    def prepare_prompt(self, model_input: ModelInput) -> tuple[list[int], dict]:
        """Returns tokenized prompt and metadata."""

        assert "message" in model_input.extra
        special_tokens = self.tokenizer.special_tokens
        assert isinstance(special_tokens, ResearchSpecialTokens)
        message = model_input.extra["message"]

        selected_code = model_input.extra.get("selected_code", "")
        # Selected code could be None when user doesn't have any code tab opened.
        selected_code = selected_code or ""

        if len(selected_code.strip()) == 0:
            # Use the message as the search query if no code is selected in the editor.
            # Including the currently opened file's content can distract,
            # especially if it's unrelated to the user's message.
            # The current model cannot process unrelated context effectively.
            prompt = self.tokenizer.tokenize_safe(message)
            if self.max_tokens >= 0:
                prompt = prompt[: self.max_tokens]
            self._maybe_log_prompt(prompt)
            return prompt, {}

        # Otherwise, if user selected parts of the code, we deem that code important
        # and include it in the search query together with the current path.
        prompt = []
        # Optionally add path
        if self.add_path and model_input.path:
            prompt += self.tokenizer.tokenize_safe(model_input.path)
            prompt.append(special_tokens.fim_prefix)

        message_with_comment = (
            self._get_lang_comment_prefix(model_input) + message + "\n"
        )
        prompt += self.tokenizer.tokenize_safe(message_with_comment)

        prompt += self.tokenizer.tokenize_safe(selected_code)

        if self.max_tokens >= 0:
            prompt = prompt[: self.max_tokens]
        self._maybe_log_prompt(prompt)
        return prompt, {}

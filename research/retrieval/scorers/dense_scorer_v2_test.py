"""Test DenseRetrievalScorerV2.

pytest research/retrieval/scorers/dense_scorer_v2_test.py
"""

from unittest.mock import MagicMock

import pytest
import torch

from base.prompt_format_retrieve.prompt_formatter import (
    CompletionRetrieverPromptInput,
)
from base.tokenizers import RetrievalSpecialTokens, create_tokenizer_by_name
from research.core.types import Chunk, Document
from research.models.embedding_model import EmbeddingModel
from research.retrieval.scorers.dense_scorer_v2 import DenseRetrievalScorerV2


def _scorer_with_research_tokenizers() -> DenseRetrievalScorerV2:
    from megatron.tokenizer.tokenizer import StarCoderTokenizer

    from research.retrieval.chunk_formatters import Ethanol<PERSON><PERSON>hunk<PERSON>ormatter
    from research.retrieval.query_formatters import Ethanol6QueryFormatter

    tokenizer = StarCoderTokenizer()
    assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)

    mock_query_model = MagicMock(
        EmbeddingModel,
        embedding_dim=128,
        embedding_token_id=tokenizer.special_tokens.end_of_query,
    )
    mock_doc_model = MagicMock(
        EmbeddingModel,
        embedding_dim=128,
        embedding_token_id=tokenizer.special_tokens.end_of_key,
    )
    query_formatter = Ethanol6QueryFormatter(tokenizer_name="StarCoderTokenizer")
    doc_formatter = Ethanol6ChunkFormatter(tokenizer_name="StarCoderTokenizer")

    return DenseRetrievalScorerV2(
        mock_query_model,
        mock_doc_model,
        query_formatter,
        doc_formatter,
    )


def _scorer_with_base_tokenizers() -> (
    DenseRetrievalScorerV2[CompletionRetrieverPromptInput]
):
    from base.prompt_format_retrieve.ethanol_embedding_prompt_formatter import (
        Ethanol6DocumentFormatter,
        Ethanol6QueryFormatter,
    )

    tokenizer = create_tokenizer_by_name("starcoder")
    assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)

    mock_query_model = MagicMock(
        EmbeddingModel,
        embedding_dim=128,
        embedding_token_id=tokenizer.special_tokens.end_of_query,
    )
    mock_doc_model = MagicMock(
        EmbeddingModel,
        embedding_dim=128,
        embedding_token_id=tokenizer.special_tokens.end_of_key,
    )
    query_formatter = Ethanol6QueryFormatter(
        apportionment_config=None,
        tokenizer=tokenizer,
        add_path=True,
        add_suffix=True,
    )
    doc_formatter = Ethanol6DocumentFormatter(
        apportionment_config=None,
        tokenizer=tokenizer,
        add_path=True,
    )

    return DenseRetrievalScorerV2[CompletionRetrieverPromptInput](
        mock_query_model,
        mock_doc_model,
        query_formatter,
        doc_formatter,
    )


@pytest.fixture(params=["research", "base"])
def scorer(
    request: pytest.FixtureRequest,
) -> DenseRetrievalScorerV2:
    if request.param == "research":
        return _scorer_with_research_tokenizers()
    elif request.param == "base":
        return _scorer_with_base_tokenizers()
    else:
        raise ValueError(f"Unknown parameter: {request.param}")


def make_doc_with_chunks(
    doc_id: str, chunk_texts: list[str]
) -> tuple[Document, list[Chunk]]:
    """Create a document with chunks."""
    doc = Document(
        id=doc_id,
        path=doc_id,
        text="".join(chunk_texts),
    )
    line_offset = 0
    char_offset = 0
    chunks = []
    for i, chunk_text in enumerate(chunk_texts):
        line_count = len(chunk_text.splitlines(keepends=True))
        chunks.append(
            Chunk(
                id=f"{doc_id}_{i}",
                text=chunk_text,
                parent_doc=doc,
                char_offset=char_offset,
                length=len(chunk_text),
                line_offset=line_offset,
                length_in_lines=line_count,
            )
        )
        char_offset += len(chunk_text)
        line_offset += line_count

    return doc, chunks


def test_add_docs(scorer: DenseRetrievalScorerV2):
    """Test add docs."""
    assert isinstance(scorer.doc_embedder, MagicMock)

    scorer.add_docs({})
    assert not scorer.doc_embedder.called

    doc1, chunks1 = make_doc_with_chunks("doc1", ["chunk 1\n", "chunk 2\n"])
    doc2, chunks2 = make_doc_with_chunks("doc2", ["chunk 3\n"])

    scorer.doc_embedder.embed_batch.return_value = torch.zeros(3, 128)
    scorer.add_docs({doc1.id: chunks1, doc2.id: chunks2})
    # embed_batch should be called once, with a batch size of 3 for the 3 chunks.
    assert scorer.doc_embedder.embed_batch.call_count == 1
    assert len(scorer.doc_embedder.embed_batch.call_args[0][0]) == 3
    tokenizer = scorer.doc_formatter.tokenizer
    for chunk, chunk_tokens in zip(
        chunks1 + chunks2, scorer.doc_embedder.embed_batch.call_args[0][0]
    ):
        assert chunk.text in tokenizer.detokenize(chunk_tokens)

    # Adding doc1 again should do nothing.
    scorer.doc_embedder.reset_mock()
    scorer.add_docs({doc1.id: chunks1})
    assert not scorer.doc_embedder.called


def test_add_docs_empty_docs(scorer: DenseRetrievalScorerV2):
    """Test add docs."""
    assert isinstance(scorer.doc_embedder, MagicMock)

    scorer.add_docs({})
    assert not scorer.doc_embedder.called

    doc1, chunks1 = make_doc_with_chunks("doc1", ["chunk 1\n", "chunk 2\n"])
    doc2, chunks2 = make_doc_with_chunks("doc2", [])
    doc3, chunks3 = make_doc_with_chunks("doc3", ["chunk 3\n"])

    scorer.add_doc(doc2.id, chunks2)
    # Make sure the empty doc is in the index.
    assert doc2.id in scorer.scoring_data.keys()

    scorer.remove_all_docs()
    # Embedders should see the number of chunks.
    scorer.doc_embedder.embed_batch.return_value = torch.zeros(3, 128)
    scorer.add_docs({doc1.id: chunks1, doc2.id: chunks2, doc3.id: chunks3})
    # embed_batch should be called once, with a batch size of 3 for the 3 chunks.
    assert scorer.doc_embedder.embed_batch.call_count == 1
    assert len(scorer.doc_embedder.embed_batch.call_args[0][0]) == 3

    # Make sure the empty doc is in the index.
    assert doc2.id in scorer.scoring_data.keys()


def test_score(scorer: DenseRetrievalScorerV2):
    """Test scoring."""
    assert isinstance(scorer.query_embedder, MagicMock)
    assert isinstance(scorer.doc_embedder, MagicMock)

    doc1, chunks1 = make_doc_with_chunks("doc1", ["chunk 1\n", "chunk 2\n"])
    doc2, chunks2 = make_doc_with_chunks("doc2", ["chunk 3\n"])
    scorer.doc_embedder.embed_batch.return_value = torch.eye(3, 128)
    scorer.add_docs({doc1.id: chunks1, doc2.id: chunks2})

    # This is a 128 dimensional vector that looks like: [1, 0, 1, 0, ...]
    # As a result, it will have high inner product with chunk1 and chunk3, which we use
    # in our tests.
    query_emb = (1 - torch.arange(128) % 2).reshape(1, -1)
    scorer.query_embedder.embed_batch.return_value = query_emb
    scored_chunks, scores = scorer.score(
        CompletionRetrieverPromptInput(
            prefix="Hi AI!",
            suffix="",
            path="foo/foo.py",
        )
    )
    # embed_batch should be called once, with a batch size of 1 for the query.
    assert scorer.query_embedder.embed_batch.call_count == 1
    assert len(scorer.query_embedder.embed_batch.call_args[0][0]) == 1

    # The scored chunks are in order.
    assert scores == [1, 1, 0]
    assert scored_chunks == [
        (doc1.id, chunks1[0].id),
        (doc2.id, chunks2[0].id),
        (doc1.id, chunks1[1].id),
    ]

    # We'll now filter to doc1, which means we ignore chunk3 in our results.
    scored_chunks, scores = scorer.score(
        CompletionRetrieverPromptInput(
            prefix="Hi AI!",
            suffix="",
            path="foo/foo.py",
        ),
        doc_ids={doc1.id},
    )
    # The scored chunks are in order.
    assert scores == [1, 0]
    assert scored_chunks == [
        (doc1.id, chunks1[0].id),
        (doc1.id, chunks1[1].id),
    ]


def test_score_with_empty_docs(scorer: DenseRetrievalScorerV2):
    """Test scoring."""
    assert isinstance(scorer.query_embedder, MagicMock)
    assert isinstance(scorer.doc_embedder, MagicMock)

    doc1, chunks1 = make_doc_with_chunks("doc1", ["chunk 1\n", "chunk 2\n"])
    doc2, chunks2 = make_doc_with_chunks("doc2", ["chunk 3\n"])
    doc3, chunks3 = make_doc_with_chunks("doc3", [])
    scorer.doc_embedder.embed_batch.return_value = torch.eye(3, 128)
    scorer.add_docs({doc1.id: chunks1, doc2.id: chunks2})
    # Adding an empty doc on its own triggers a different path in our code that we need
    # to test.
    scorer.add_docs({doc3.id: chunks3})

    # This is a 128 dimensional vector that looks like: [1, 0, 1, 0, ...]
    # As a result, it will have high inner product with chunk1 and chunk3, which we use
    # in our tests.
    scorer.query_embedder.embed_batch.return_value = (
        1 - torch.arange(128) % 2
    ).reshape(1, -1)
    scored_chunks, scores = scorer.score(
        CompletionRetrieverPromptInput(
            prefix="Hi AI!",
            suffix="",
            path="foo/foo.py",
        ),
    )
    assert scores == [1, 1, 0]
    assert scored_chunks == [
        (doc1.id, chunks1[0].id),
        (doc2.id, chunks2[0].id),
        (doc1.id, chunks1[1].id),
    ]


def test_score_with_no_docs(scorer: DenseRetrievalScorerV2):
    assert isinstance(scorer.query_embedder, MagicMock)
    assert isinstance(scorer.doc_embedder, MagicMock)

    # This is a 128 dimensional vector that looks like: [1, 0, 1, 0, ...]
    # As a result, it will have high inner product with chunk1 and chunk3, which we use
    # in our tests.
    query_emb = (1 - torch.arange(128) % 2).reshape(1, -1)
    scorer.query_embedder.embed_batch.return_value = query_emb
    scored_chunks, scores = scorer.score(
        CompletionRetrieverPromptInput(
            prefix="Hi AI!",
            suffix="",
            path="foo/foo.py",
        ),
    )
    assert scored_chunks == []
    assert scores == []


def test_score_with_missing_docs(scorer: DenseRetrievalScorerV2):
    """Test scoring."""
    assert isinstance(scorer.query_embedder, MagicMock)
    assert isinstance(scorer.doc_embedder, MagicMock)

    doc1, chunks1 = make_doc_with_chunks("doc1", ["chunk 1\n", "chunk 2\n"])
    doc2, chunks2 = make_doc_with_chunks("doc2", ["chunk 3\n"])
    doc3, chunks3 = make_doc_with_chunks("doc3", [])
    scorer.doc_embedder.embed_batch.return_value = torch.eye(3, 128)
    scorer.add_docs({doc1.id: chunks1, doc2.id: chunks2, doc3.id: chunks3})

    # This is a 128 dimensional vector that looks like: [1, 0, 1, 0, ...]
    # As a result, it will have high inner product with chunk1 and chunk3, which we use
    # in our tests.
    query_emb = (1 - torch.arange(128) % 2).reshape(1, -1)
    scorer.query_embedder.embed_batch.return_value = query_emb
    scored_chunks, scores = scorer.score(
        CompletionRetrieverPromptInput(
            prefix="Hi AI!",
            suffix="",
            path="foo/foo.py",
        ),
        # doc4 is missing.
        doc_ids={doc1.id, doc2.id, "doc4"},
    )
    # embed_batch should be called once, with a batch size of 1 for the query.
    assert scorer.query_embedder.embed_batch.call_count == 1
    assert len(scorer.query_embedder.embed_batch.call_args[0][0]) == 1

    # The scored chunks are in order.
    assert scores == [1, 1, 0]
    assert scored_chunks == [
        (doc1.id, chunks1[0].id),
        (doc2.id, chunks2[0].id),
        (doc1.id, chunks1[1].id),
    ]


def test_mismatched_tokenizers_fails():
    from megatron.tokenizer.tokenizer import StarCoderTokenizer

    from research.retrieval.chunk_formatters import Ethanol6ChunkFormatter
    from research.retrieval.query_formatters import Ethanol6QueryFormatter

    tokenizer = StarCoderTokenizer()
    assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)

    mock_query_model = MagicMock(
        EmbeddingModel,
        embedding_dim=128,
        embedding_token_id=tokenizer.special_tokens.end_of_query,
    )
    mock_doc_model = MagicMock(
        EmbeddingModel,
        embedding_dim=128,
        embedding_token_id=tokenizer.special_tokens.end_of_key,
    )
    query_formatter = Ethanol6QueryFormatter(tokenizer_name="CodeGenTokenizer")
    doc_formatter = Ethanol6ChunkFormatter(tokenizer_name="CodeGenTokenizer")

    with pytest.raises(AssertionError):
        return DenseRetrievalScorerV2(
            mock_query_model,
            mock_doc_model,
            query_formatter,
            doc_formatter,
        )

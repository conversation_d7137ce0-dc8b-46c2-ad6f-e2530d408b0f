"""A database for dense retrieval."""

import collections
from collections import Counter, defaultdict
from collections.abc import Collection, Mapping
from dataclasses import dataclass
from typing import Optional

import numpy as np
from megatron.tokenizer.tokenizer import CodeGenTokenizer

from research.core.abstract_prompt_formatter import Abstract<PERSON>rom<PERSON><PERSON><PERSON>atter
from research.core.model_input import ModelInput
from research.retrieval import chunk_formatters, query_formatters
from research.retrieval.chunk_formatters import AbstractChunkFormatter
from research.retrieval.scorers.scoring_interface import (
    RetrievalDatabaseScorer,
    register_scorer,
)
from research.retrieval.types import (
    Chunk,
    ChunkId,
    DocumentId,
    RetrievalScore,
)


@dataclass
class DocGoodEnoughBm25ScoringData:
    """This is a data structure storing BM25 stats for chunks in a document."""

    chunk_to_tok_tfs: dict[ChunkId, defaultdict[int, int]]
    chunk_to_tf_score_factors: dict[ChunkId, dict[int, float]]
    chunk_to_toks: dict[ChunkId, list[int]]


@register_scorer("bm25")
class GoodEnoughBM25Scorer(RetrievalDatabaseScorer[ModelInput]):
    """A good enough BM25 scorer, to be used with RetrievalDatabase.

    Note that we don't explicitly set max chunk length. This is because we let
    InferenceModel load this from the loaded model's settings.
    """

    def __init__(
        self,
        query_formatter: Optional[AbstractPromptFormatter] = None,
        document_formatter: Optional[AbstractChunkFormatter] = None,
        k1=1.5,
        b=0.75,
        delta=0.5,
    ):
        default_tokenizer = CodeGenTokenizer()
        if query_formatter is None:
            query_formatter = query_formatters.SimpleQueryFormatter(max_tokens=50)
            query_formatter.tokenizer = default_tokenizer
        self.query_formatter = query_formatter

        if document_formatter is None:
            document_formatter = chunk_formatters.SimpleChunkFormatter()
            document_formatter.tokenizer = default_tokenizer
        self.document_formatter = document_formatter

        self.k1 = k1
        self.b = b
        self.delta = delta
        self.default_score_factor = (
            (self.k1 + 1) * (0 + self.delta) / (self.k1 + 0 + self.delta)
        )
        self.rebuild_index_threshold = 0.05
        self.avg_dl = None

        # State

        self.tok_to_chunks: dict[int, set[tuple[DocumentId, ChunkId]]] = (
            collections.defaultdict(set)
        )
        self.scoring_data: dict[DocumentId, DocGoodEnoughBm25ScoringData] = {}

        super().__init__()

    def load(self):
        """Load the model. This is a no-op for BM25."""
        return

    def unload(self):
        """Unload the model. This is a no-op for BM25."""
        return

    def _recompute_doc_score_factors(
        self, doc_scoring_data: DocGoodEnoughBm25ScoringData
    ) -> None:
        """This is an internal helper to recompute chunk_to_tf_score_factors for a doc."""
        assert self.avg_dl is not None
        doc_scoring_data.chunk_to_tf_score_factors = {}
        for chunk_id in doc_scoring_data.chunk_to_toks:
            tok_freqs = doc_scoring_data.chunk_to_tok_tfs[chunk_id]
            chunk_toks = doc_scoring_data.chunk_to_toks[chunk_id]
            doc_scoring_data.chunk_to_tf_score_factors[chunk_id] = {}
            chunk_len = len(chunk_toks)

            ctd = [
                token_freq / (1 - self.b + self.b * chunk_len / self.avg_dl)
                for token_freq in tok_freqs.values()
            ]
            for token_idx, token in enumerate(tok_freqs):
                doc_scoring_data.chunk_to_tf_score_factors[chunk_id][token] = (
                    (self.k1 + 1)
                    * (ctd[token_idx] + self.delta)
                    / (self.k1 + ctd[token_idx] + self.delta)
                )

    def _maybe_rebuild_index(self) -> None:
        """This an internal helper to recompute all chunk_to_tf_score_factors when avg_dl changes too much."""
        new_avg_dl = float(
            np.mean(
                [
                    len(chunk_toks)
                    for doc_scoring_data in self.scoring_data.values()
                    for chunk_toks in doc_scoring_data.chunk_to_toks.values()
                ]
            )
        )
        if (
            self.avg_dl is None
            or np.abs(self.avg_dl - new_avg_dl) / self.avg_dl
            > self.rebuild_index_threshold
        ):
            rebuild_index = True
            self.avg_dl = new_avg_dl
        else:
            rebuild_index = False

        if not rebuild_index:
            return

        for doc_scoring_data in self.scoring_data.values():
            self._recompute_doc_score_factors(doc_scoring_data)

    def add_doc(self, doc_id: DocumentId, chunks: list[Chunk]) -> None:
        """Compute and store BM25 statistics for all chunks in a doc."""
        # TODO (@guy): Add comments explaining the various computations.
        ###
        # The primary BM25 bookkeeping for every chunk is:
        # 1) tokenize the chunk and store in BM25DocumentRow.chunk_to_toks,
        # 2) compute token frequencies and store them in BM25DocumentRow.chunk_to_tok_tfs,
        # 3) and compute score factors to to store in BM25DocumentRow.chunk_to_tf_score_factors
        ###
        if len(chunks) == 0:
            return

        doc = chunks[0].parent_doc
        for chunk in chunks:
            assert chunk.parent_doc.id == doc.id, (chunk.parent_doc.id, doc.id)

        self.scoring_data[doc.id] = DocGoodEnoughBm25ScoringData(
            chunk_to_toks={}, chunk_to_tok_tfs={}, chunk_to_tf_score_factors={}
        )
        doc_scoring_data = self.scoring_data[doc.id]

        for chunk in chunks:
            chunk_toks = self.document_formatter.format(chunk).tokens
            doc_scoring_data.chunk_to_toks[chunk.id] = chunk_toks
            tok_freqs = collections.defaultdict(lambda: 0, Counter(chunk_toks))
            doc_scoring_data.chunk_to_tok_tfs[chunk.id] = tok_freqs
            for tok in tok_freqs:
                self.tok_to_chunks[tok].add((doc.id, chunk.id))

        if self.avg_dl is None:
            self.avg_dl = float(
                np.mean(
                    [
                        len(chunk_toks)
                        for doc_scoring_data in self.scoring_data.values()
                        for chunk_toks in doc_scoring_data.chunk_to_toks.values()
                    ]
                )
            )

        self._recompute_doc_score_factors(doc_scoring_data)

        ###
        # Rebuilding the index means recomputing BM25DocumentRow.chunk_to_tf_score_factors
        ###
        self._maybe_rebuild_index()

    def add_docs(self, doc_chunks: Mapping[DocumentId, list[Chunk]]) -> None:
        """Hook to add multiple documents."""
        for doc_id, chunks in doc_chunks.items():
            self.add_doc(doc_id, chunks)

    def remove_doc(self, doc_id: DocumentId) -> None:
        """Remove all chunks in a doc."""
        if doc_id in self.scoring_data:
            for relevant_chunk_set in self.tok_to_chunks.values():
                relevant_chunk_set.difference_update(
                    (doc_id, chunk_id)
                    for chunk_id in self.scoring_data[doc_id].chunk_to_toks
                )
            del self.scoring_data[doc_id]

            self._maybe_rebuild_index()

    def remove_all_docs(self) -> None:
        """Remove all docs."""
        self.avg_dl = None
        self.tok_to_chunks = collections.defaultdict(set)
        self.scoring_data = {}

    def _compute_score(
        self,
        distinct_query_tokens: list[int],
        query_token_counts_times_idf,
        doc_id: DocumentId,
        chunk_id: ChunkId,
    ) -> float:
        """This is an internal helper to score a chunk against a retrieval query."""
        tf_score_factors_for_doc = self.scoring_data[doc_id].chunk_to_tf_score_factors[
            chunk_id
        ]
        tf_score_factors = np.array(
            [
                tf_score_factors_for_doc.get(token, self.default_score_factor)
                for token in distinct_query_tokens
            ]
        )

        return float(np.sum(query_token_counts_times_idf * tf_score_factors))

    def score(
        self, query: ModelInput, doc_ids: Optional[Collection[DocumentId]] = None
    ) -> tuple[list[tuple[DocumentId, ChunkId]], list[RetrievalScore]]:
        """Score the corpus of chunks against a retrieval query."""
        query_chunk_toks, _ = self.query_formatter.prepare_prompt(query)
        query_token_counter = Counter(query_chunk_toks)
        distinct_tokens = list(query_token_counter.keys())
        query_token_counts = np.array(list(query_token_counter.values()))

        # indexed by token index
        num_chunks_per_tok = [
            len(self.tok_to_chunks[token]) for token in distinct_tokens
        ]
        num_chunks_in_db = len(
            [1 for doc in self.scoring_data.values() for _ in doc.chunk_to_toks]
        )
        # The below term is usually called "inverse document frequency" but to avoid confusion let's
        # call it "inverse chunk frequency."
        inverse_chunk_frequency = np.array(
            [
                min(num_chunks, 1)
                * (np.log(num_chunks_in_db + 1) - np.log(num_chunks + 0.5))
                for num_chunks in num_chunks_per_tok
            ]
        )

        # This has shape (number of unique tokens in query,)
        query_token_counts_times_idf = query_token_counts * inverse_chunk_frequency

        ids = [
            (doc_id, chunk_id)
            for doc_id in self.scoring_data
            for chunk_id in self.scoring_data[doc_id].chunk_to_toks
        ]

        # Get chunk relevance scores
        retrieval_scores = [
            self._compute_score(
                distinct_tokens, query_token_counts_times_idf, doc_id, chunk_id
            )
            for doc_id in self.scoring_data
            for chunk_id in self.scoring_data[doc_id].chunk_to_toks
        ]

        return ids, retrieval_scores

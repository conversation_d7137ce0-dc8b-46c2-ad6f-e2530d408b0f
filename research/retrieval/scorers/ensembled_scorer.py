"""A scorer that ensembles other scorers."""

from collections import defaultdict
from collections.abc import Collection, Mapping
from typing import Optional, TypeVar

import numpy as np

from research.retrieval.scorers.scoring_interface import (
    RetrievalDatabaseScorer,
)
from research.retrieval.types import (
    Chunk,
    ChunkId,
    DocumentId,
    RetrievalScore,
)

QueryT = TypeVar("QueryT")


class EnsembledRetrievalScorer(RetrievalDatabaseScorer[QueryT]):
    """This implements a scoring technique, which hooks into methods in RetrievalDatabase. It is mostly stateless, as RetrievalDatabase manages state."""

    def __init__(self, scorers: list[RetrievalDatabaseScorer[QueryT]]):
        self.scorers = scorers

        super().__init__()

    def load(self):  # pylint: disable=arguments-differ
        """Load the model."""
        for scorer in self.scorers:
            scorer.load()

    def unload(self):
        """Unload the model and free GPU memory."""
        for scorer in self.scorers:
            scorer.unload()

    def add_doc(self, doc_id: DocumentId, chunks: list[Chunk]) -> None:
        """Hook for adding a doc."""
        self.add_docs({doc_id: chunks})

    def add_docs(self, doc_chunks: Mapping[DocumentId, list[Chunk]]) -> None:
        """Hook to add multiple documents."""
        for scorer in self.scorers:
            scorer.add_docs(doc_chunks)

    def remove_doc(self, doc_id: DocumentId) -> None:
        """Hook for removing a doc."""
        for scorer in self.scorers:
            scorer.remove_doc(doc_id)

    def remove_all_docs(self) -> None:
        """Remove all docs."""
        for scorer in self.scorers:
            scorer.remove_all_docs()

    def score(
        self,
        query: QueryT,
        doc_ids: Optional[Collection[DocumentId]] = None,
    ) -> tuple[list[tuple[DocumentId, ChunkId]], list[RetrievalScore]]:
        """Internal method to compute scores for a query, possibly unsorted."""
        id_to_ls_of_scores = defaultdict(list)

        for scorer in self.scorers:
            doc_and_chunk_ids, scores = scorer.score(query)

            scores = np.array(scores)
            scores -= np.mean(scores)
            scores /= np.var(scores)
            scores = list(scores)

            for doc_and_chunk_id, score in zip(doc_and_chunk_ids, scores):
                id_to_ls_of_scores[doc_and_chunk_id].append(score)

        id_to_agg_scores = {}
        for doc_and_chunk_id, scores in id_to_ls_of_scores.items():
            id_to_agg_scores[doc_and_chunk_id] = sum(scores)

        return list(id_to_agg_scores.keys()), list(id_to_agg_scores.values())

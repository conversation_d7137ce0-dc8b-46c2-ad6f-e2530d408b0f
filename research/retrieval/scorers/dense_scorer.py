"""A database for dense retrieval."""

import functools
import logging
import warnings
from collections.abc import Collection, Mapping
from dataclasses import dataclass
from pathlib import Path
from typing import Optional

import numpy as np
from megatron.inference.inference_model import InferenceModel
from megatron.inference.process_wrap import ProcessWrappedObject
from typing_extensions import deprecated

from research.core import data_paths
from research.core.abstract_prompt_formatter import Abstract<PERSON>romptFormatter
from research.core.artifacts import post_artifact
from research.core.constants import AUGMENT_ROOT
from research.core.model_input import ModelInput
from research.retrieval import chunk_formatters, query_formatters
from research.retrieval.chunk_formatters import Abstract<PERSON>hunkFormatter
from research.retrieval.scorers.scoring_interface import (
    RetrievalDatabaseScorer,
    register_scorer,
)
from research.retrieval.types import (
    Chunk,
    ChunkId,
    DocumentId,
    RetrievalScore,
)

logger = logging.getLogger(__name__)


@dataclass
class DocDenseScoringData:
    """This is a data structure storing embedding array of chunks in a document."""

    chunk_ids: list[ChunkId]
    emb_arr: np.ndarray


@deprecated("Please use DenseRetrievalScorerV2 instead.")
class DenseRetrievalScorer(RetrievalDatabaseScorer[ModelInput]):
    """A dense scorer, to be used with RetrievalDatabase."""

    def __init__(
        self,
        yaml_files,
        overwrite_values,
        query_formatter: Optional[AbstractPromptFormatter] = None,
        document_formatter: Optional[chunk_formatters.AbstractChunkFormatter] = None,
    ):
        self.yaml_files = yaml_files
        self.overwrite_values = overwrite_values
        self._embedding_model: Optional[InferenceModel] = None

        self.query_formatter = query_formatter or query_formatters.SimpleQueryFormatter(
            max_tokens=50
        )
        self.document_formatter = (
            document_formatter or chunk_formatters.SimpleChunkFormatter()
        )

        self.scoring_data: dict[DocumentId, DocDenseScoringData] = dict()
        super().__init__()

    @property
    def embedding_model(self) -> InferenceModel:
        assert self._embedding_model is not None, "Model not loaded."
        return self._embedding_model

    @embedding_model.setter
    def embedding_model(self, value):
        self._embedding_model = value

    def load(self):
        """Load the model."""
        embedding_model = ProcessWrappedObject(
            InferenceModel,
            yaml_files=self.yaml_files,
            overwrite_values=self.overwrite_values,
            use_cache=False,
        )
        self.embedding_model = embedding_model

        # Loading the process-wrapped inference model is non-blocking, so we
        # query the model to synchronize model loading with this process.
        # If we don't block on loading the embedding model, then it may load at the
        # same time as another model loads in this process. Each model will try to grab a port
        # for torch.distributed to bind to. This causes a race condition that can result in multiple
        # models trying to bind to the same port. Therfore, we synchronize model loading.
        self.embedding_model.contrastive_embed([], [])

        # Makes `query_formatter` and `document_formatter` use tokenizer of `embedding_model`.
        self.query_formatter.tokenizer = embedding_model.get_tokenizer()
        self.document_formatter.tokenizer = embedding_model.get_tokenizer()

    def unload(self):
        """Unload the model and free GPU memory."""
        self.embedding_model = None

    def _get_view_of_embeddings(
        self,
        doc_ids: Collection[DocumentId],
    ) -> tuple[list[tuple[DocumentId, ChunkId]], np.array]:  # type: ignore
        """This groups all embedding arrays across documents, for querying."""
        ids = list(
            (doc_id, chunk_id)
            for doc_id, scoring_data in self.scoring_data.items()
            if doc_id in doc_ids
            for chunk_id in scoring_data.chunk_ids
        )

        @functools.lru_cache(maxsize=1)
        def _get_cached_embedding_view(_ids: tuple):
            del _ids
            return np.concatenate(
                [
                    scoring_data.emb_arr
                    for doc_id, scoring_data in self.scoring_data.items()
                    if doc_id in doc_ids and scoring_data.emb_arr.shape[0] > 0
                ]
            )

        embedding_view = _get_cached_embedding_view(tuple(ids))

        assert len(ids) == len(embedding_view), (len(ids), len(embedding_view))

        return ids, embedding_view

    def add_doc(self, doc_id: DocumentId, chunks: list[Chunk]) -> None:
        """Compute and store embeddings for all chunks in a doc."""
        if len(chunks) == 0:
            self.scoring_data[doc_id] = DocDenseScoringData(
                chunk_ids=[], emb_arr=np.array([])
            )
            return

        doc = chunks[0].parent_doc
        for chunk in chunks:
            assert chunk.parent_doc.id == doc.id, (chunk.parent_doc.id, doc.id)

        # Compute embeddings
        chunk_toks = [self.document_formatter.format(chunk).tokens for chunk in chunks]
        _, chunk_embs, _ = self.embedding_model.contrastive_embed([], chunk_toks)  # type: ignore
        emb_arr = chunk_embs.cpu().numpy().reshape(len(chunks), -1)

        self.scoring_data[doc.id] = DocDenseScoringData(
            chunk_ids=[chunk.id for chunk in chunks],
            emb_arr=emb_arr,
        )

        post_artifact(
            {
                "doc": doc.path,
                "chunks": [
                    self.embedding_model.detokenize(chunk) for chunk in chunk_toks
                ],
            }
        )

    def add_docs(self, doc_chunks: Mapping[DocumentId, list[Chunk]]) -> None:
        """Hook to add multiple documents."""
        for doc_id, chunks in doc_chunks.items():
            self.add_doc(doc_id, chunks)

    def remove_doc(self, doc_id: DocumentId) -> None:
        """Remove embeddings for a doc."""
        del self.scoring_data[doc_id]

    def remove_all_docs(self) -> None:
        """Remove all docs."""
        self.scoring_data = {}

    def _query_embedding(self, model_input: ModelInput) -> tuple[np.ndarray, list[int]]:
        query_chunk_toks, _ = self.query_formatter.prepare_prompt(model_input)
        query_emb, _, _ = self.embedding_model.contrastive_embed([query_chunk_toks], [])
        query_emb = query_emb.reshape(-1, 1).cpu().numpy()
        return query_emb, query_chunk_toks

    def score(
        self, query: ModelInput, doc_ids: Collection[DocumentId] | None = None
    ) -> tuple[list[tuple[DocumentId, ChunkId]], list[RetrievalScore]]:
        """Score the corpus of chunks against a retrieval query."""
        if len(self.scoring_data) == 0:
            return [], []
        if doc_ids is not None:
            available_doc_ids = set(doc_ids) & self.scoring_data.keys()
            if available_doc_ids != doc_ids:
                logger.warning(
                    f"{len(available_doc_ids)}/{len(doc_ids)} doc ids are available.\n"
                    f"These are missing: {set(doc_ids).difference(available_doc_ids)}"
                )
            doc_ids = available_doc_ids
        if doc_ids is not None and len(doc_ids) == 0:
            return [], []
        query_emb, query_chunk_toks = self._query_embedding(query)

        doc_ids = set(doc_ids) if doc_ids is not None else self.scoring_data.keys()

        embedding_ids, key_embeddings = self._get_view_of_embeddings(doc_ids)
        similarity_scores = np.matmul(key_embeddings, query_emb).flatten().tolist()
        assert len(similarity_scores) == key_embeddings.shape[0], (
            len(similarity_scores),
            key_embeddings.shape,
        )

        post_artifact(
            {"retriever_prompt": self.embedding_model.detokenize(query_chunk_toks)}
        )

        return embedding_ids, similarity_scores

    # TODO: Shall we depreciate the `Rerank` interface and just use `Scorer` for
    # reranking?
    def rerank(self, model_input: ModelInput) -> tuple[ModelInput, list[float]]:
        """Rerank the corpus of chunks against a retrieval query."""
        # Computes chunk embeddings.
        chunks = model_input.retrieved_chunks
        chunk_toks = [self.document_formatter.format(chunk).tokens for chunk in chunks]
        _, chunk_embs, _ = self.embedding_model.contrastive_embed([], chunk_toks)  # type: ignore
        # Array of shape [num_chunks, embedding_size].
        chunk_embs = chunk_embs.cpu().numpy().reshape(len(chunks), -1)

        # Computes query embedding.
        query_emb, _ = self._query_embedding(model_input)
        similarity_scores = np.matmul(chunk_embs, query_emb).flatten().tolist()
        assert len(similarity_scores) == chunk_embs.shape[0], (
            len(similarity_scores),
            chunk_embs.shape,
        )

        # Sorts chunks by scores in the descending order.
        sorted_chunks, sorted_scores = zip(
            *sorted(zip(chunks, similarity_scores), key=lambda x: x[1], reverse=True)
        )
        output = model_input.clone()
        output.retrieved_chunks = list(sorted_chunks)

        return output, list(sorted_scores)  # pyright: ignore


_checkpoints_root = data_paths.canonicalize_path("checkpoints")

_yaml_files = [
    data_paths.canonicalize_path(
        "user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml"
    ),
    data_paths.canonicalize_path(
        "user/igor/configs/2023-04-17_contrastive/special/contrastive.yml"
    ),
    data_paths.canonicalize_path(
        "user/igor/configs/2023-04-17_contrastive/train/350M-12000.yml"
    ),
]


@register_scorer("contrieve_350m")
class Contrieve_350M_16k_Scorer(DenseRetrievalScorer):
    """A 350M dense retriever trained using contrastive learning.

    Trained using Contriever-style same-file chunking, with a 16k batch size.
    """

    def __init__(
        self,
        checkpoints_root=_checkpoints_root,
        query_formatter: Optional[AbstractPromptFormatter] = None,
        document_formatter: Optional[AbstractChunkFormatter] = None,
    ):
        super().__init__(
            yaml_files=_yaml_files,
            overwrite_values={
                "load": str(
                    Path(
                        checkpoints_root,
                        "testing/1fb59e7c-6e10-4d9d-96ac-131fda053762",
                    )
                ),
            },
            query_formatter=query_formatter,
            document_formatter=document_formatter,
        )


@register_scorer("contrastive_350m")
class Commit_Contrastive_350M_Scorer(DenseRetrievalScorer):
    """A 350M dense retriever trained using contrastive learning.

    Trained using same-commit chunks.
    """

    def __init__(
        self,
        checkpoints_root=_checkpoints_root,
        query_formatter: Optional[AbstractPromptFormatter] = None,
        document_formatter: Optional[AbstractChunkFormatter] = None,
    ):
        super().__init__(
            yaml_files=_yaml_files,
            overwrite_values={
                "load": str(
                    Path(
                        checkpoints_root,
                        "diffs-retriever-v1-2023-06-26",
                    )
                ),
            },
            query_formatter=query_formatter,
            document_formatter=document_formatter,
        )


@register_scorer("diff_boykin")
class Contrastive_350M_Scorer(DenseRetrievalScorer):
    """A 350M dense retriever trained using contrastive learning.

    Trained using same-commit chunks.
    """

    def __init__(
        self,
        checkpoints_root=_checkpoints_root,
        query_formatter: Optional[AbstractPromptFormatter] = None,
        document_formatter: Optional[AbstractChunkFormatter] = None,
        checkpoint="diff-retriever-boykin",
    ):
        super().__init__(
            yaml_files=_yaml_files,
            overwrite_values={
                "load": str(Path(checkpoints_root, checkpoint)),
            },
            query_formatter=query_formatter,
            document_formatter=document_formatter,
        )


# TODO (marcmac): why does this one have different argument names?
@register_scorer("starcoder_1b")
class Starcoder_1B_Scorer(DenseRetrievalScorer):
    """A 350M dense retriever trained using contrastive learning.

    Trained using Contriever-style same-file chunking, with a 16k batch size.
    """

    def __init__(
        self,
        checkpoints_root=_checkpoints_root,
        query_formatter: Optional[AbstractPromptFormatter] = None,
        document_formatter: Optional[AbstractChunkFormatter] = None,
        checkpoint_path: str = "starcoder-1b",
        additional_yaml_files: Optional[list[str]] = None,
    ):
        """Constructor.

        Args:
            checkpoints_root: The root directory of the checkpoints.
            query_formatter: The query formatter.
            document_formatter: The document formatter.
            checkpoint_path: The path to the checkpoint.
            additional_yaml_files: Additional yaml files to load.
        """
        additional_yaml_files = additional_yaml_files or []
        super().__init__(
            yaml_files=[
                data_paths.canonicalize_path(
                    "research/gpt-neox/augment_configs/starcoder/model/starcoder.yml",
                    new_path=AUGMENT_ROOT,
                ),
                data_paths.canonicalize_path(
                    "research/gpt-neox/augment_configs/starcoder/model/starcoder-1b.yml",
                    new_path=AUGMENT_ROOT,
                ),
                data_paths.canonicalize_path(
                    "user/igor/configs/2023-04-17_contrastive/special/contrastive.yml"
                ),
            ]
            + [data_paths.canonicalize_path(f) for f in additional_yaml_files],
            overwrite_values={
                "load": str(Path(checkpoints_root, checkpoint_path)),
            },
            query_formatter=query_formatter,
            document_formatter=document_formatter,
        )


@register_scorer("ethanol")
class Ethanol_350M_Scorer(Contrastive_350M_Scorer):
    """A 350M dense retriever trained using perplexity distillation."""

    def __init__(
        self,
        query_formatter: Optional[AbstractPromptFormatter] = None,
        document_formatter: Optional[AbstractChunkFormatter] = None,
        checkpoint_path: str = "",
    ):
        super().__init__(
            checkpoint=checkpoint_path,
            query_formatter=query_formatter,
            document_formatter=document_formatter,
        )


@register_scorer("generic_neox")
class GenericNeoXScorer(DenseRetrievalScorer):
    """A general purpose neox dense retriever configured from it's checkpoint."""

    def __init__(
        self,
        checkpoint_path: str,
        query_formatter: Optional[AbstractPromptFormatter] = None,
        document_formatter: Optional[AbstractChunkFormatter] = None,
    ):
        """Constructor.

        Args:
            checkpoint_path: The path to the checkpoint.
            query_formatter: The query formatter.
            document_formatter: The document formatter.
        """
        warnings.warn(
            "This scorer is deprecated. Please use `dense_scorer_v2_fbwd_neox` instead."
        )

        checkpoint_location = data_paths.absolute_path(checkpoint_path)
        super().__init__(
            yaml_files=[str(checkpoint_location / "config.yml")],
            overwrite_values={
                "load": str(checkpoint_location),
                # We rely on some special magic in the contrastive loss to actually
                # extract embeddings for inference, so we need to switch out any ppl
                # distillation loss for a contrastive loss.
                "contrastive": True,
                "ppl_distill": False,
            },
            query_formatter=query_formatter,
            document_formatter=document_formatter,
        )

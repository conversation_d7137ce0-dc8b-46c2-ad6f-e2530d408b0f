"""A scorer that reranks another scorer using recency signals.

Recency signals include things like tab switch history. For more details:
https://www.notion.so/RFC-Recency-signals-for-retrieval-e0543a2232e543e4a77a3e05280cc371?pvs=4
"""

import logging
from collections import defaultdict
from collections.abc import Collection, Mapping
from dataclasses import dataclass
from typing import Iterable, Optional, Sequence

from unidiff import PatchedFile, PatchSet

from base.ranges.range_types import LineRange
from research.core.abstract_prompt_formatter import AbstractPromptFormatter
from research.core.diff_utils import get_source_path, get_target_path
from research.core.model_input import ModelInput
from research.core.types import IntRange
from research.retrieval.chunk_formatters import (
    AbstractChunkFormatter,
    SimpleChunkFormatter,
)
from research.retrieval.query_formatters import SimpleQueryFormatter
from research.retrieval.scorers.scoring_interface import (
    RetrievalDatabaseScorer,
    get_scorer,
    register_scorer,
)
from research.retrieval.types import (
    Chunk,
    ChunkId,
    DocumentId,
    RetrievalScore,
)

logger = logging.getLogger(__name__)


RECENCY_EXTRA_KEY = "recency_feature"
"""Recency data dict key for ModelInput.extra."""


@dataclass
class RecencyExtra:
    """Recency data goes in ModelInput.extra[RECENCY_EXTRA_KEY]."""

    tab_switch_paths: Optional[list[str]] = None
    """Tab switch event history (paths)."""

    tab_switch_blob_names: Optional[list[str]] = None
    """Tab switch event history (blob names)."""

    client_git_diff_patchset: Optional[PatchSet] = None
    """Client-local git diff."""


@dataclass
class ScoredChunk:
    """A Chunk with a retrieval score.

    A ScoredChunk can be sorted by score.
    """

    chunk: Chunk
    score: float
    rank: Optional[float] = None

    @property
    def path(self) -> Optional[str]:
        """The parent doc's path."""
        return self.chunk.parent_doc.path

    def __lt__(self, other):
        return self.score < other.score

    def __le__(self, other):
        return self.score <= other.score

    def __hash__(self):
        return hash(self.chunk)


def target_overlaps(target_range: IntRange, candidate_ranges: list[IntRange]) -> bool:
    """Whether the target range overlaps with any of the given candidate_ranges.

    Args:
        target_range: The target range to check.
        candidate_ranges: The candidate ranges to check against.

    Returns:
        True if the chunk (minus the ignore_boundary_lines) overlaps with any of the
        given line_ranges.
    """
    for line_range in candidate_ranges:
        # The intersect() method returns either an IntRange or None. None is
        # returned if the given ranges do not intersect. An IntRange is returned
        # if they intersect, even if that intersection is empty.
        #
        # We count the text as overlapping as long as intersect() returns an
        # IntRange. This covers two cases:
        # - a line range is non-empty and intersects with chunk range
        # - a line range is empty and intersects with the chunk range:
        #   this happens if the diff hunk includes only deleted lines.
        #   if those lines happened in a chunk, we want to count that as an
        #   overlap because the user edited code in that chunk.
        if line_range.intersect(target_range) is not None:
            return True
    return False


def get_trimmed_diff_range(hunk, source_range: bool) -> Optional[IntRange]:
    """Get the source or target range of the hunk, ignoring empty lines.

    Only hunks produced with -U0 (no context lines) are supported.
    Otherwise, raises a ValueError.

    Implementation note: Unified diff has unexpected behavior for chunks of size
    0, observed empirically and also referenced here:

    > If the chunk size is 0, the first number is one lower than one would expect
    > (it is the line number after which the chunk should be inserted or deleted;
    > in all other cases it gives the first line number or the replaced range of
    > lines).

    Source: https://www.artima.com/weblogs/viewpost.jsp?thread=164293

    This especially affects diffs generated with -U0 (no contest lines), so it
    is handled here.
    """
    for line in hunk:
        if str(line).startswith(" "):
            raise ValueError("Hunks with context lines are not supported")

    # Precise definition of "line_offset":
    # It is a 0-based line number. Imagine the cursor is at the beginning of
    # this line. Then that is the precise location at which the changes from
    # this hunk will be applied.
    if source_range:
        line_offset = hunk.source_start - 1
        hunk_length = hunk.source_length
        lines = hunk.source_lines()
    else:
        line_offset = hunk.target_start - 1
        hunk_length = hunk.target_length
        lines = hunk.target_lines()

    # See implementation note about 0-length chunks in the docstring
    if hunk_length == 0:
        line_offset += 1

    line_stop = line_offset + hunk_length

    lines = [line.value for line in lines]
    for line in lines:
        if line.strip() == "":
            line_offset += 1
        else:
            break

    assert line_offset >= 0
    assert line_offset <= line_stop

    if line_offset == line_stop:
        return IntRange(start=line_offset, stop=line_stop)

    for line in reversed(lines):
        if line.strip() == "":
            line_stop -= 1
        else:
            break

    assert line_offset <= line_stop
    return IntRange(start=line_offset, stop=line_stop)


def get_trimmed_target_range(hunk) -> Optional[IntRange]:
    return get_trimmed_diff_range(hunk, source_range=False)


def get_diff_line_ranges(
    git_diff: PatchSet, source_range: bool
) -> dict[str, list[IntRange]]:
    """Returns path -> hunk line ranges from the given git diff.

    Args:
        source_range: If True, returns the line ranges of the source file.
            Otherwise, returns the line ranges of the target file.

    Returned values give the list of line ranges affected by the given diff.
    The lines are computed relative to the target file of the diff (as opposed
    to the source file).
    """
    diff_line_ranges: dict[str, list[IntRange]] = {}
    for diff_file in git_diff:
        assert isinstance(diff_file, PatchedFile)
        path = (
            get_source_path(diff_file) if source_range else get_target_path(diff_file)
        )

        if not path:
            continue

        diff_line_ranges[path] = []

        for hunk in diff_file:
            line_range = get_trimmed_diff_range(hunk, source_range)
            if line_range is None:
                logging.debug("\tpath=%s empty range", path)
                continue
            diff_line_ranges[path].append(line_range)
            logger.debug(
                "\tpath=%s line_range=[%d, %d] inclusive",
                path,
                line_range.start,
                line_range.stop - 1,
            )
    return diff_line_ranges


class RerankingScorer(RetrievalDatabaseScorer[ModelInput]):
    """A scorer that uses an inner scorer for reranking."""

    def __init__(
        self,
        query_formatter: Optional[AbstractPromptFormatter] = None,
        document_formatter: Optional[AbstractChunkFormatter] = None,
        inner_scorer_name: Optional[str] = None,
        inner_scorer: Optional[RetrievalDatabaseScorer] = None,
    ):
        if (inner_scorer_name is None) == (inner_scorer is None):
            raise ValueError(
                "Exactly one of inner_scorer_name, inner_scorer should be specified"
            )
        if inner_scorer is None:
            assert inner_scorer_name is not None
            if query_formatter is None:
                query_formatter = SimpleQueryFormatter()
                query_formatter.add_path = True
            if document_formatter is None:
                document_formatter = SimpleChunkFormatter()
                document_formatter.add_path = True
            self._inner_scorer = get_scorer(
                inner_scorer_name,
                query_formatter=query_formatter,
                document_formatter=document_formatter,
            )
        else:
            self._inner_scorer = inner_scorer

        self.id_to_chunk: dict[ChunkId, Chunk] = {}

    @property
    def inner_scorer(self):
        return self._inner_scorer

    def load(self):
        self.inner_scorer.load()

    def unload(self):
        self.inner_scorer.unload()

    def add_doc(self, doc_id: DocumentId, chunks: list[Chunk]) -> None:
        self.add_docs({doc_id: chunks})

    def add_docs(self, doc_chunks: Mapping[DocumentId, list[Chunk]]) -> None:
        """Hook to add multiple documents."""
        if overwritten_docs := self.id_to_chunk.keys() & doc_chunks.keys():
            logger.warning(
                "Overwritting %d docs: %s", len(overwritten_docs), overwritten_docs
            )
        self.id_to_chunk.update(
            {
                chunk.id: chunk
                for doc_id, chunks in doc_chunks.items()
                for chunk in chunks
            }
        )
        self.inner_scorer.add_docs(doc_chunks)

    def remove_doc(self, doc_id: DocumentId) -> None:
        for chunk in list(self.id_to_chunk.values()):
            if chunk.parent_doc.id == doc_id:
                del self.id_to_chunk[chunk.id]
        self.inner_scorer.remove_doc(doc_id)

    def remove_all_docs(self) -> None:
        self.id_to_chunk.clear()
        self.inner_scorer.remove_all_docs()

    def _to_scored_chunks(
        self,
        ids: Sequence[tuple[DocumentId, ChunkId]],
        scores: Sequence[RetrievalScore],
    ) -> list[ScoredChunk]:
        """Transforms the output of a score() function to a list of scored chunks."""
        return [
            ScoredChunk(
                chunk=self.id_to_chunk[cur_id[1]],
                score=score,
            )
            for cur_id, score in zip(ids, scores)
        ]

    def _from_scored_chunks(
        self,
        scored_chunks: list[ScoredChunk],
    ) -> tuple[list[tuple[DocumentId, ChunkId]], list[RetrievalScore]]:
        """Invert _to_scored_chunks."""
        ids = [(sc.chunk.parent_doc.id, sc.chunk.id) for sc in scored_chunks]
        scores = [reranked_chunk.score for reranked_chunk in scored_chunks]
        return ids, scores


def combine_chunks_by_category_and_score(
    chunk_categories: Iterable[Iterable[ScoredChunk]],
) -> list[ScoredChunk]:
    """Combine scored-chunk categories into a single list.

    Within each category, chunks are sorted by score (higher is better), and
    chunks in earlier categories are always ranked ahead of those in later
    categories. Chunk scores are updated to reflect the new ordering.

    For example, for these chunks: (here 'a', 'b' etc. is the chunk ID, and the
    number is the score)

    [(('a', 1), ('b', 2)), (('c', 3), ('d', 0))]
        ^^^^ category 1 ^^^^  ^^^^ category 2 ^^^^

    The resulting chunks will be:
    (scores here are made up, but the ordering is correct)

    [('b', 100), ('a', 99), ('c', 98), ('d', 97)]
    """
    result: list[ScoredChunk] = []

    for category in chunk_categories:
        result.extend(sorted(category, key=lambda sc: sc.score, reverse=True))

    # Overwrite the scores across the combined chunks
    for i, sc in enumerate(result):
        sc.score = -i

    return result


def _is_overlapping_chunk(
    chunk: Chunk,
    diff_line_ranges: dict[str, list[IntRange]],
    ignore_boundary_lines: int = 0,
) -> bool:
    """Whether the chunk overlaps with any of the given line_ranges.

    Args:
        chunk: The chunk to check.
        diff_line_ranges: The line ranges to check against as returned by
            get_diff_line_ranges.
        ignore_boundary_lines: If > 0, do not label overlaps with the diff if their
            overlapping region is within `ignore_boundary_lines` lines from the
            beginning or end of the chunk.
    """
    if chunk.path not in diff_line_ranges:
        return False

    if 2 * ignore_boundary_lines >= len(chunk.line_range):
        # If the chunk is smaller than the ignore_boundary_lines, then the chunks never overlap
        return False

    chunk_start = (
        # Ignore boundary lines at the beginning of the document.
        0
        if chunk.line_range.start == 0
        else chunk.line_range.start + ignore_boundary_lines
    )
    chunk_stop = (
        # Ignore boundary lines at the end of the document.
        chunk.parent_doc.line_range.stop
        if chunk.line_range.stop >= chunk.parent_doc.line_range.stop
        else chunk.line_range.stop - ignore_boundary_lines
    )
    return target_overlaps(
        LineRange(chunk_start, chunk_stop), diff_line_ranges[chunk.path]
    )


def label_overlapping_chunks(
    chunks: Sequence[Chunk],
    diff_line_ranges: dict[str, list[IntRange]],
    ignore_boundary_lines: int = 0,
) -> list[bool]:
    """Label chunks that overlap with the given diff.

    Args:
        chunks: The chunks to search.
        diff_line_ranges: A mapping from path to line ranges.
        ignore_boundary_lines: If > 0, do not label overlaps with the diff if their
            overlapping region is within `ignore_boundary_lines` lines from the
            beginning or end of the chunk.

    Returns:
        A list booleans of length len(chunks), where each element is True iff
        the corresponding chunk overlaps with the diff.
    """
    return [
        _is_overlapping_chunk(chunk, diff_line_ranges, ignore_boundary_lines)
        for chunk in chunks
    ]


def find_overlapping_chunks(
    chunks: Sequence[Chunk],
    diff: PatchSet,
    chunks_from_source: bool,
    ignore_boundary_lines: int = 0,
) -> list[Chunk]:
    """Find chunks that overlap with the given diff.

    Args:
        chunks: The chunks to search.
        diff: The diff to search for.
        chunks_from_source: If True, treat chunks as coming from the diff's source
            file. If False, treat chunks as coming from the target file.
        ignore_boundary_lines: If > 0, do not label overlaps with the diff if their
            overlapping region is within `ignore_boundary_lines` lines from the
            beginning or end of the chunk.

    Returns:
        A list of chunks that overlap with the diff.
    """
    diff_line_ranges = get_diff_line_ranges(diff, chunks_from_source)
    labels = label_overlapping_chunks(chunks, diff_line_ranges, ignore_boundary_lines)
    return [chunk for chunk, label in zip(chunks, labels) if label]


@register_scorer("recency_scorer")
class RecencyScorer(RerankingScorer):
    """A scorer that retrieves using recency signals, and then reranks.

    Reranking is performed with another scorer.
    """

    num_most_recent_paths: int = 5
    """Number of most recent paths in the tab history to consider for scoring."""

    def score(
        self, query: ModelInput, doc_ids: Collection[DocumentId] | None = None
    ) -> tuple[list[tuple[DocumentId, ChunkId]], list[RetrievalScore]]:
        # call the underlying scorer
        ids, scores = self.inner_scorer.score(query)

        all_scored_chunks: list[ScoredChunk] = self._to_scored_chunks(ids, scores)

        # doc_ids is the list of document ids that the user can see.
        # If it is provided, we filter on that list before reranking.
        # Otherwise, reranking will be contaminated by chunks that will end
        # up not being returned to the user, which will throw off the reranking
        # algorithm.
        if query.doc_ids is not None:
            scored_chunks = [
                sc
                for sc in all_scored_chunks
                if sc.chunk.parent_doc.id in query.doc_ids
            ]
        else:
            scored_chunks = all_scored_chunks

        scored_chunks = list(sorted(scored_chunks, reverse=True))

        if RECENCY_EXTRA_KEY not in query.extra:
            logger.debug(
                "RecencyScorer found no recency info in extra, returning no chunks"
            )
            return [], []

        recency_extra: RecencyExtra = query.extra[RECENCY_EXTRA_KEY]

        git_diff_chunks: set[ScoredChunk] = set(
            self._filter_chunks_on_git_diff_signal(scored_chunks, recency_extra)
        )

        tab_switch_chunks: set[ScoredChunk] = set(
            self._filter_chunks_on_tab_switch_signal(scored_chunks, recency_extra)
        )

        git_diff_and_tab_switch_chunks: set[ScoredChunk] = git_diff_chunks.intersection(
            tab_switch_chunks
        )
        just_git_diff_chunks = git_diff_chunks - git_diff_and_tab_switch_chunks
        just_tab_switch_chunks = tab_switch_chunks - git_diff_and_tab_switch_chunks

        reranked_scored_chunks = combine_chunks_by_category_and_score(
            [
                git_diff_and_tab_switch_chunks,
                just_git_diff_chunks,
                just_tab_switch_chunks,
            ]
        )

        logger.debug("RecencyScorer will return the following reranked chunks:")
        for i, sc in enumerate(reranked_scored_chunks):
            if i < len(git_diff_and_tab_switch_chunks):
                chunk_type = "git_diff_and_tab_switches"
            elif i < len(git_diff_and_tab_switch_chunks) + len(just_git_diff_chunks):
                chunk_type = "git_diff"
            else:
                chunk_type = "tab_switch"
            logger.debug(
                f"  [{i}] type={chunk_type} score={sc.score} path={sc.chunk.parent_doc.path}"
            )
            logger.debug(f"\n{sc.chunk.text}")

        return self._from_scored_chunks(reranked_scored_chunks)

    def _filter_chunks_on_git_diff_signal(
        self, scored_chunks: list[ScoredChunk], recency_extra: RecencyExtra
    ) -> list[ScoredChunk]:
        if recency_extra.client_git_diff_patchset is None:
            logger.debug("No git diff information found")
            return []

        logger.debug("Found git-diff extra field")
        git_diff: PatchSet = recency_extra.client_git_diff_patchset
        # For recency, the diff is between the previous version of the file
        # and the current one, so we want to look at the target file.
        diff_line_ranges = get_diff_line_ranges(git_diff, source_range=False)
        return [
            sc
            for sc in scored_chunks
            if _is_overlapping_chunk(sc.chunk, diff_line_ranges)
        ]

    def _filter_chunks_on_tab_switch_signal(
        self, scored_chunks: list[ScoredChunk], recency_extra: RecencyExtra
    ) -> list[ScoredChunk]:
        if recency_extra.tab_switch_paths is None:
            logger.debug("No tab-switch events for reranking")
            return []

        logger.debug("Found tab-switch extra field for reranking")
        # paths of file most recently accessed according to the history.
        # make it a set for faster lookups.
        tab_switch_paths = set()

        if self.num_most_recent_paths > 0:
            tab_switch_paths = set(
                recency_extra.tab_switch_paths[-self.num_most_recent_paths :]
            )

        def is_recent_chunk(sc: ScoredChunk) -> bool:
            return sc.path in tab_switch_paths

        return [sc for sc in scored_chunks if is_recent_chunk(sc)]


@register_scorer("recency_reranker")
class RecencyReranker(RerankingScorer):
    """A scorer that reranks another scorer using recency signals."""

    tab_switch_rank_boost_amount: float = 10
    """Additive boost for the rank of chunks that were recently accessed via tab switches."""

    git_diff_rank_boost_amount: float = 100
    """Additive boost for the rank of chunks that were recently accessed via git diff."""

    num_most_recent_paths: int = 5
    """Number of most recent paths in the tab history to consider for scoring."""

    def __init__(
        self,
        query_formatter: Optional[AbstractPromptFormatter] = None,
        document_formatter: Optional[AbstractChunkFormatter] = None,
        scorer=None,
    ):
        inner_scorer_name = None
        if scorer is None:
            inner_scorer_name = "diff_boykin"
        super().__init__(query_formatter, document_formatter, inner_scorer_name, scorer)

    def rerank(
        self,
        scored_chunks: list[ScoredChunk],
        chunk_rank_boosts: dict[ChunkId, float],
    ):
        """Boost the rank of chunks that were recently accessed.

        Updates the ranks in place.

        Args:
            scored_chunks: The chunks, their scores, and their ranks.
                The ranks and scores will be updated in-place.
            chunk_rank_boosts: Maps ChunkId to its rank boost -- by how much
                its rank should be boosted. This is modeled as a float to
                allow for more flexibility, because the formula doesn't care if
                it's an int.
        """
        epsilon = 1e-6

        logging.debug("Starting rank boost on %d chunks:", len(scored_chunks))
        for sc in scored_chunks:
            if sc.chunk.id in chunk_rank_boosts:
                # move the rank up. the epsilon will make it have a slightly
                # lower rank than the current item at (rank - rank_boost),
                # avoiding a rank tie.
                assert sc.rank is not None
                rank_boost_amount = chunk_rank_boosts[sc.chunk.id]
                if rank_boost_amount > 0:
                    rank_boost_amount += epsilon
                sc.rank -= rank_boost_amount

                if rank_boost_amount > 0:
                    logger.debug(
                        "Boosting score of chunk: %s lines=[offset=%d,length=%d] rank_boost=%f new_rank=%f",
                        sc.chunk.parent_doc.path,
                        sc.chunk.line_offset,
                        sc.chunk.length_in_lines,
                        rank_boost_amount,
                        sc.rank,
                    )
        logging.debug("Rank boost complete")

        for sc in scored_chunks:
            assert sc.rank is not None
            sc.score = -sc.rank

    def update_rank_boosts_from_tab_switches(
        self,
        scored_chunks: list[ScoredChunk],
        chunk_rank_boosts: dict[ChunkId, float],
        tab_switch_paths: list[str],
    ):
        """Update the recency counts from tab switches."""
        for path in tab_switch_paths:
            for sc in scored_chunks:
                if sc.path == path:
                    chunk_rank_boosts[sc.chunk.id] += self.tab_switch_rank_boost_amount

    def update_rank_boosts_from_git_diff(
        self,
        scored_chunks: list[ScoredChunk],
        chunk_rank_boosts: dict[ChunkId, float],
        git_diff: PatchSet,
    ):
        """Update the chunk recency counts based on the git diff.

        A chunk whose line range overlaps with a diff hunk gets a +1 recency count.
        """

        diff_line_ranges = get_diff_line_ranges(git_diff, source_range=False)

        for sc in scored_chunks:
            if sc.path in diff_line_ranges:
                if target_overlaps(sc.chunk.line_range, diff_line_ranges[sc.path]):
                    chunk_rank_boosts[sc.chunk.id] += self.git_diff_rank_boost_amount

    def score(
        self, query: ModelInput, doc_ids: Collection[DocumentId] | None = None
    ) -> tuple[Sequence[tuple[DocumentId, ChunkId]], list[RetrievalScore]]:
        # call the underlying scorer
        ids, scores = self.inner_scorer.score(query)

        all_scored_chunks: list[ScoredChunk] = self._to_scored_chunks(ids, scores)

        # doc_ids is the list of document ids that the user can see.
        # If it is provided, we filter on that list before reranking.
        # Otherwise, reranking will be contaminated by chunks that will end
        # up not being returned to the user, which will throw off the reranking
        # algorithm.
        if query.doc_ids is not None:
            scored_chunks = [
                sc
                for sc in all_scored_chunks
                if sc.chunk.parent_doc.id in query.doc_ids
            ]
        else:
            scored_chunks = all_scored_chunks

        scored_chunks = list(sorted(scored_chunks, reverse=True))

        for i, scored_chunk in enumerate(scored_chunks):
            scored_chunk.rank = i

        chunk_rank_boosts: dict[ChunkId, float] = defaultdict(float)

        if RECENCY_EXTRA_KEY not in query.extra:
            logger.debug("No recency extra key found, returning no chunks")
            return [], []

        recency_extra: RecencyExtra = query.extra[RECENCY_EXTRA_KEY]

        # Tab-switch recency counts
        if recency_extra.tab_switch_paths is not None:
            logger.debug("Found tab-switch extra field for reranking")
            # paths of file most recently accessed according to the history
            tab_switch_paths = recency_extra.tab_switch_paths[
                -self.num_most_recent_paths :
            ]
            self.update_rank_boosts_from_tab_switches(
                scored_chunks, chunk_rank_boosts, tab_switch_paths
            )
        else:
            logger.debug("No tab switche events found for reranking")

        # Git-diff recency count
        if recency_extra.client_git_diff_patchset is not None:
            logger.debug("Found git-diff extra field for reranking")
            git_diff_patchset: PatchSet = recency_extra.client_git_diff_patchset
            self.update_rank_boosts_from_git_diff(
                scored_chunks, chunk_rank_boosts, git_diff_patchset
            )
        else:
            logger.debug("No git diff found for reranking")

        self.rerank(scored_chunks, chunk_rank_boosts)
        reranked_ids, reranked_scores = self._from_scored_chunks(scored_chunks)
        return reranked_ids, reranked_scores

"""A scorer for dense retrieval that uses the EmbeddingModel interface.

For instructions on how to migrate from DenseScorer (V1), please see the docstring in
`create_dense_scorer_with_fastbackward_from_neox_checkpoint`.
"""

import logging
from collections.abc import Collection, Mapping, Sequence
from dataclasses import dataclass
from pathlib import Path
from typing import TypeVar, cast

import numpy as np
import torch

from base.fastforward import fwd, fwd_utils
from base.fastforward.starcoder import fwd_starcoder_fp8
import base.fastforward.starcoder.model_specs as starcoder_model_specs
from base.prompt_format_retrieve import (
    DocumentRetrieverPromptInput,
    RetrieverPromptFormatter,
)
from base.tokenizers.tokenizer import RetrievalSpecialTokens
from base.tokenizers import create_tokenizer_by_name
from research.core.artifacts import post_artifact
from research.models.embedding_model import EmbeddingModel
from research.models.embedding_models.cached import CachedEmbeddingModel
from research.models.embedding_models.fastforward import <PERSON>ForwardEmbeddingModel
from research.retrieval.scorers.scoring_interface import (
    Retrieval<PERSON><PERSON><PERSON><PERSON>corer,
    register_scorer,
)
from research.retrieval.types import (
    <PERSON><PERSON>,
    <PERSON>kId,
    DocumentId,
    RetrievalScore,
)

logger = logging.getLogger(__name__)

Tokens = list[int]


@dataclass
class DocDenseScoringData:
    """This is a data structure storing embedding array of chunks in a document."""

    chunk_ids: list[ChunkId]
    emb_arr: np.ndarray


QueryT = TypeVar("QueryT")
# Research prompt formatters use `Chunk`s while the base ones use
# `DocumentRetrieverPromptInput`.
DocumentPromptFormatter = (
    RetrieverPromptFormatter[DocumentRetrieverPromptInput]
    | RetrieverPromptFormatter[Chunk]
)


class DenseRetrievalScorerV2(RetrievalDatabaseScorer[QueryT]):
    """A dense scorer, to be used with RetrievalDatabase."""

    def __init__(
        self,
        query_embedder: EmbeddingModel,
        doc_embedder: EmbeddingModel,
        query_formatter: RetrieverPromptFormatter[QueryT],
        document_formatter: DocumentPromptFormatter,
    ):
        self.query_embedder = query_embedder
        self.doc_embedder = doc_embedder

        self.query_formatter = query_formatter
        self.doc_formatter = document_formatter

        assert isinstance(
            query_formatter.tokenizer.special_tokens, RetrievalSpecialTokens
        ), f"The {query_formatter.tokenizer=} must have RetrievalSpecialTokens."
        assert (
            query_formatter.tokenizer.special_tokens.end_of_query
            == query_embedder.embedding_token_id
        ), (
            f"The {query_formatter.tokenizer.special_tokens.end_of_query=} does not match"
            f" {query_embedder.embedding_token_id=}. Check your tokenizer config."
        )
        assert isinstance(
            document_formatter.tokenizer.special_tokens, RetrievalSpecialTokens
        ), f"The {document_formatter.tokenizer=} must have RetrievalSpecialTokens."
        assert (
            document_formatter.tokenizer.special_tokens.end_of_key
            == doc_embedder.embedding_token_id
        ), (
            f"The {document_formatter.tokenizer.special_tokens.end_of_key=} does not match"
            f" {doc_embedder.embedding_token_id=}. Check your tokenizer config."
        )

        self.scoring_data = dict[DocumentId, DocDenseScoringData]()

        super().__init__()

    def load(self):
        # Nothing to do here.
        pass

    def unload(self):
        # Nothing to do here.
        pass

    def _get_view_of_embeddings(
        self, doc_ids: Collection[DocumentId]
    ) -> tuple[Sequence[tuple[DocumentId, ChunkId]], np.ndarray]:  # type: ignore
        """Groups embeddings across relevant documents for querying.

        Args:
            doc_ids: Optional list of document IDs to use for retrieval.
                Default is to use all available documents.
                Prefer using a set or similar for efficient lookup performance.
        """
        doc_ids = [doc_id for doc_id in self.scoring_data if doc_id in doc_ids]
        doc_chunk_ids = tuple(
            (doc_id, chunk_id)
            for doc_id in doc_ids
            for chunk_id in self.scoring_data[doc_id].chunk_ids
        )
        if not doc_chunk_ids:
            return [], np.zeros((0, self.doc_embedder.embedding_dim))

        embedding_view = np.concatenate(
            [self.scoring_data[doc_id].emb_arr for doc_id in doc_ids]
        )

        assert len(doc_chunk_ids) == len(embedding_view), (
            len(doc_chunk_ids),
            len(embedding_view),
        )

        return doc_chunk_ids, embedding_view

    def add_docs(self, doc_chunks: Mapping[DocumentId, list[Chunk]]):
        doc_chunks = {
            doc_id: chunks
            for doc_id, chunks in doc_chunks.items()
            # Don't bother with docs already in the index.
            if doc_id not in self.scoring_data
        }
        if not doc_chunks:
            return

        # Maps document ids to slices in flat_chunks and the resultant embeddings.
        doc_chunk_tokens = dict[DocumentId, list[tuple[Chunk, Tokens]]]()

        # Tokenize chunks.
        end_of_key_id = self.doc_embedder.embedding_token_id

        if self.doc_formatter.input_type == Chunk:
            doc_formatter = cast(
                RetrieverPromptFormatter[Chunk],
                self.doc_formatter,
            )

            for doc_id, chunks in doc_chunks.items():
                chunks_tokens = []
                for chunk in chunks:
                    chunk_tokens = doc_formatter.format_prompt(chunk).tokens()

                    # NOTE(arun): For Reasons, some research chunk formatters do not
                    # append the <end-of-key> token, because the code in Megatron does.
                    # We always append it here.
                    if chunk_tokens[-1] != end_of_key_id:
                        chunk_tokens.append(end_of_key_id)
                    chunks_tokens.append((chunk, chunk_tokens))
                doc_chunk_tokens[doc_id] = chunks_tokens
        else:
            assert self.doc_formatter.input_type == DocumentRetrieverPromptInput
            doc_formatter = cast(
                RetrieverPromptFormatter[DocumentRetrieverPromptInput],
                self.doc_formatter,
            )

            for doc_id, chunks in doc_chunks.items():
                doc_chunk_tokens[doc_id] = [
                    (
                        chunk,
                        doc_formatter.format_prompt(
                            DocumentRetrieverPromptInput(
                                text=chunk.text,
                                path=chunk.parent_doc.path or "",
                            )
                        ).tokens(),
                    )
                    for chunk in chunks
                ]

        self.add_doc_tokens(doc_chunk_tokens)

    def add_doc_tokens(
        self, doc_chunk_tokens: Mapping[DocumentId, list[tuple[Chunk, Tokens]]]
    ):
        doc_chunk_tokens = {
            doc_id: chunk_tokens
            for doc_id, chunk_tokens in doc_chunk_tokens.items()
            # Don't bother with docs already in the index.
            if doc_id not in self.scoring_data
        }

        # Flatten the chunks into a single list for more efficient batching.
        # Maps document ids to slices in flat_chunks and the resultant embeddings.
        chunk_slices = dict[DocumentId, slice]()
        flat_tokens = list[Tokens]()
        for doc_id, chunk_tokens in doc_chunk_tokens.items():
            for i, (_, tokens) in enumerate(chunk_tokens):
                assert (
                    not tokens or tokens[-1] == self.doc_embedder.embedding_token_id
                ), f"{doc_id=}/chunk {i=} does not end with the embedding token."
            chunk_slices[doc_id] = slice(
                len(flat_tokens), len(flat_tokens) + len(chunk_tokens)
            )
            flat_tokens.extend([tokens for _, tokens in chunk_tokens])

        if not flat_tokens:
            # If there are no tokens, it means all the chunks must have been empty.
            # We'll still insert placeholders in the index.
            assert all(not chunks for _, chunks in doc_chunk_tokens.items())
            self.scoring_data.update(
                {
                    doc_id: DocDenseScoringData(
                        chunk_ids=[],
                        # Make sure that the embedding array we add has the right shape.
                        emb_arr=np.zeros((0, self.doc_embedder.embedding_dim)),
                    )
                    for doc_id in doc_chunk_tokens
                }
            )
            return

        emb_BD = self.doc_embedder.embed_batch(flat_tokens).float().cpu().numpy()
        assert emb_BD.shape == (len(flat_tokens), self.doc_embedder.embedding_dim), (
            emb_BD.shape,
            (len(flat_tokens), self.doc_embedder.embedding_dim),
        )

        self.scoring_data.update(
            {
                doc_id: DocDenseScoringData(
                    chunk_ids=[chunk.id for chunk, _ in doc_chunk_tokens[doc_id]],
                    emb_arr=emb_BD[chunk_slice],
                )
                for (doc_id, chunk_slice) in chunk_slices.items()
            }
        )

    def add_doc(self, doc_id: DocumentId, chunks: list[Chunk]) -> None:
        """Compute and store embeddings for all chunks in a doc."""
        self.add_docs({doc_id: chunks})

    def remove_doc(self, doc_id: DocumentId) -> None:
        """Remove embeddings for a doc."""
        # Don't complain if the doc is not in the index.
        if doc_id in self.scoring_data:
            del self.scoring_data[doc_id]

    def remove_all_docs(self) -> None:
        """Remove all docs."""
        self.scoring_data = {}

    def score(
        self,
        query: QueryT,
        doc_ids: Collection[DocumentId] | None = None,
    ) -> tuple[Sequence[tuple[DocumentId, ChunkId]], list[RetrievalScore]]:
        """Score the corpus of chunks against a retrieval query."""
        if len(self.scoring_data) == 0:
            return [], []

        query_tokens = self.query_formatter.format_prompt(query).tokens()
        post_artifact(
            {
                "retriever_prompt": self.query_formatter.tokenizer.detokenize(
                    query_tokens
                )
            }
        )

        return self.score_tokens(query_tokens, doc_ids)

    def score_tokens(
        self,
        query_tokens: list[int],
        doc_ids: Collection[DocumentId] | None = None,
    ) -> tuple[Sequence[tuple[DocumentId, ChunkId]], list[RetrievalScore]]:
        """Score the corpus of chunks against a retrieval query."""
        if len(self.scoring_data) == 0:
            return [], []

        doc_ids = _get_available_doc_ids(doc_ids, self.scoring_data)
        if not doc_ids:
            return [], []

        # TODO(arun): The research formatters do not append the <end-of-query> token,
        # because they expect model code in neox to. We should fix this behavior when we
        # deprecate neox.
        assert isinstance(
            self.query_formatter.tokenizer.special_tokens, RetrievalSpecialTokens
        )
        end_of_query_token = self.query_formatter.tokenizer.special_tokens.end_of_query
        if query_tokens[-1] != end_of_query_token:
            query_tokens.append(end_of_query_token)

        query_emb = (
            self.query_embedder.embed_batch([query_tokens]).float().cpu().numpy()[0]
        )

        embedding_ids, key_embeddings = self._get_view_of_embeddings(doc_ids)
        similarity_scores = np.matmul(key_embeddings, query_emb).flatten()
        assert len(similarity_scores) == key_embeddings.shape[0], (
            len(similarity_scores),
            key_embeddings.shape,
        )

        # Sort chunks by scores in the descending order.
        ranking = np.argsort(-similarity_scores)
        embedding_ids = [embedding_ids[i] for i in ranking]
        similarity_scores = similarity_scores[ranking]

        return embedding_ids, similarity_scores.tolist()


@register_scorer("dense_scorer_v2_fbwd")
def create_dense_scorer_from_fastbackward_checkpoint(
    checkpoint_path: str | Path,
    query_formatter: RetrieverPromptFormatter[QueryT],
    document_formatter: DocumentPromptFormatter,
    max_batch_size: int = 32,
    dtype: str | torch.dtype = torch.bfloat16,
    cache_dir: Path | str | None = None,
    model_key: str = "model",
    **ignored,
) -> DenseRetrievalScorerV2[QueryT]:
    from research.fastbackward import distributed
    from research.models.embedding_models.fastbackward import (
        create_from_dual_encoder_checkpoint,
    )

    # We need to initialize fastbackward's distributed environment before loading
    # models.
    distributed.init_distributed_for_training(1)

    if isinstance(dtype, str):
        dtype = _cast_dtype(dtype)

    # load configuration
    query_model, doc_model = create_from_dual_encoder_checkpoint(
        checkpoint_path,
        max_batch_size=max_batch_size,
        dtype=dtype,
        model_key=model_key,
    )

    # Derive cache path from checkpoint path.
    if cache_dir:
        cache_path = Path(cache_dir) / Path(checkpoint_path).name
        doc_model = CachedEmbeddingModel(doc_model, cache_path=cache_path)

    return DenseRetrievalScorerV2(
        query_embedder=query_model,
        doc_embedder=doc_model,
        query_formatter=query_formatter,
        document_formatter=document_formatter,
    )


@register_scorer("dense_scorer_v2_fbwd_neox")
def create_dense_scorer_with_fastbackward_from_neox_checkpoint(
    checkpoint_path: str | Path,
    query_formatter: RetrieverPromptFormatter[QueryT],
    document_formatter: DocumentPromptFormatter,
    max_batch_size: int = 32,
    dtype: str | torch.dtype = torch.bfloat16,
    cache_dir: Path | str | None = None,
    **ignored,
) -> DenseRetrievalScorerV2[QueryT]:
    """Create a dense scorer from a neox checkpoint.

    How to migrate from the `generic_neox` and other DenseScorer(V1)s to this scorer:
    Simply update your `scorer` section in your config.
    ```yaml
      scorer:
        # old
        # name: generic_neox
        # checkpoint_path: menthol/methanol_0416.4_1250
        # new
        name: dense_scorer_v2_fbwd_neox
        checkpoint_path: /mnt/efs/augment/checkpoints/menthol/methanol_0416.4_1250/global_step1250/
    ```

    Note that you will need to specify a full path to the checkpoint folder containing
    the weights, as in the example above.
    """

    from research.fastbackward import distributed
    from research.models.embedding_models.fastbackward import (
        create_from_neox_starethanol_checkpoint,
    )

    # We need to initialize fastbackward's distributed environment before loading
    # models.
    distributed.init_distributed_for_training(1)

    if isinstance(dtype, str):
        dtype = _cast_dtype(dtype)

    # load configuration
    query_model, doc_model = create_from_neox_starethanol_checkpoint(
        checkpoint_path,
        max_batch_size=max_batch_size,
        dtype=dtype,
    )

    # Derive cache path from checkpoint path.
    if cache_dir:
        cache_path = Path(cache_dir) / Path(checkpoint_path).name
        doc_model = CachedEmbeddingModel(doc_model, cache_path=cache_path)

    return DenseRetrievalScorerV2(
        query_embedder=query_model,
        doc_embedder=doc_model,
        query_formatter=query_formatter,
        document_formatter=document_formatter,
    )


@register_scorer("dense_scorer_v2_ffwd")
def create_dense_scorer_from_fastforward_checkpoint(
    checkpoint_path: str | Path,
    query_formatter: RetrieverPromptFormatter[QueryT],
    document_formatter: DocumentPromptFormatter,
    dtype: torch.dtype | str | None = None,
    cache_dir: Path | str | None = None,
    **ignored,
) -> DenseRetrievalScorerV2[QueryT]:
    from research.models.embedding_models.fastforward import (
        create_from_starethanol_neox_checkpoint,
    )

    if isinstance(dtype, str):
        dtype = _cast_dtype(dtype)

    # load configuration
    query_model, doc_model = create_from_starethanol_neox_checkpoint(
        checkpoint_path,
        dtype=dtype,
    )

    # Derive cache path from checkpoint path.
    if cache_dir:
        cache_path = Path(cache_dir) / Path(checkpoint_path).name
        doc_model = CachedEmbeddingModel(doc_model, cache_path=cache_path)

    return DenseRetrievalScorerV2(
        query_embedder=query_model,
        doc_embedder=doc_model,
        query_formatter=query_formatter,
        document_formatter=document_formatter,
    )


# NOTE:
# - if only `checkpoint_path` is provided, that checkpoint is use for both the query and document
#   models.
# - if both `checkpoint_path` and `doc_model_checkpoint_path` are provided, the `checkpoint_path`
#   is used for the _query_ model, and the `doc_model_checkpoint_path` is used for the _document_
#   model.
# - obviously this is insane, but historically there was only `checkpoint_path`, so renaming that
#   argument would require updating a million experimental configs.
@register_scorer("dense_scorer_ffwd_starcoder_fp8")
def create_dense_scorer_from_fastforward_starcoder_fp8_checkpoint(
    checkpoint_path: str | Path,
    checkpoint_sha256: str,
    output_projection_dim: int,
    query_formatter: RetrieverPromptFormatter[QueryT],
    document_formatter: DocumentPromptFormatter,
    cache_dir: Path | str | None = None,
    doc_model_checkpoint_path: str | Path | None = None,
    doc_model_checkpoint_sha256: str | None = None,
    **ignored,
) -> DenseRetrievalScorerV2[QueryT]:
    ms = starcoder_model_specs.get_starcoder_model_spec(
        model_name="starcoder-1b",
        checkpoint_path=checkpoint_path,
        checkpoint_sha256=checkpoint_sha256,
    )
    ms.output_projection_dim = output_projection_dim
    round_sizes = (32, 128, 512, 2048)
    query_model = fwd_starcoder_fp8.generate_step_fn(
        ms,
        output_type=fwd.OutputTensorType.EMBEDDING,
    )
    query_model = fwd_utils.pad_and_step(query_model, round_sizes=round_sizes)
    if doc_model_checkpoint_path is not None:
        assert (
            doc_model_checkpoint_sha256 is not None
        ), "Must provide sha256 for doc model."
        doc_ms = starcoder_model_specs.get_starcoder_model_spec(
            model_name="starcoder-1b",
            checkpoint_path=doc_model_checkpoint_path,
            checkpoint_sha256=doc_model_checkpoint_sha256,
        )
        doc_ms.output_projection_dim = output_projection_dim
    else:
        doc_ms = ms
    doc_model = fwd_starcoder_fp8.generate_step_fn(
        doc_ms,
        output_type=fwd.OutputTensorType.EMBEDDING,
    )
    doc_model = fwd_utils.pad_and_step(doc_model, round_sizes=round_sizes)
    attn_factory = fwd_starcoder_fp8.StarcoderAttentionFactory(ms)
    tokenizer = create_tokenizer_by_name("starcoder")
    assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
    query_model = FastForwardEmbeddingModel(
        query_model,
        attn_factory,
        embedding_token_id=tokenizer.special_tokens.end_of_query,
        embedding_dim=output_projection_dim,
    )
    doc_model = FastForwardEmbeddingModel(
        doc_model,
        attn_factory,
        embedding_token_id=tokenizer.special_tokens.end_of_key,
        embedding_dim=output_projection_dim,
    )

    # Derive cache path from checkpoint path.
    if cache_dir:
        cache_path = Path(cache_dir) / Path(checkpoint_path).name
        doc_model = CachedEmbeddingModel(doc_model, cache_path=cache_path)

    return DenseRetrievalScorerV2(
        query_embedder=query_model,
        doc_embedder=doc_model,
        query_formatter=query_formatter,
        document_formatter=document_formatter,
    )


@register_scorer("dense_scorer_v2_ffwd_llama")
def create_dense_scorer_from_fastforward_llama_checkpoint(
    model_name: str,
    tokenizer_name: str,
    checkpoint_path: str | Path,
    query_formatter: RetrieverPromptFormatter[QueryT],
    document_formatter: DocumentPromptFormatter,
    sha256: str | None = None,
    dtype: torch.dtype | str | None = torch.bfloat16,
    cache_dir: Path | str | None = None,
    use_fp8: bool = False,
    **ignored,
) -> DenseRetrievalScorerV2[QueryT]:
    from research.models.embedding_models.fastforward import (
        create_from_llama_retriever_checkpoint,
    )

    if isinstance(dtype, str):
        dtype = _cast_dtype(dtype)

    # load configuration
    query_model, doc_model = create_from_llama_retriever_checkpoint(
        model_name=model_name,
        tokenizer_name=tokenizer_name,
        checkpoint_path=checkpoint_path,
        dtype=dtype,
        sha256=sha256,
        use_fp8=use_fp8,
    )

    # Derive cache path from checkpoint path.
    if cache_dir:
        cache_path = Path(cache_dir) / Path(checkpoint_path).name
        doc_model = CachedEmbeddingModel(doc_model, cache_path=cache_path)

    return DenseRetrievalScorerV2(
        query_embedder=query_model,
        doc_embedder=doc_model,
        query_formatter=query_formatter,
        document_formatter=document_formatter,
    )


@register_scorer("dense_scorer_v2_ffwd_llama_fp8")
def create_dense_scorer_from_fastforward_llama_fp8_checkpoint(
    model_name: str,
    tokenizer_name: str,
    checkpoint_path: str | Path,
    query_formatter: RetrieverPromptFormatter[QueryT],
    document_formatter: DocumentPromptFormatter,
    sha256: str | None = None,
    dtype: torch.dtype | str | None = torch.bfloat16,
    cache_dir: Path | str | None = None,
    **ignored,
) -> DenseRetrievalScorerV2[QueryT]:
    return create_dense_scorer_from_fastforward_llama_checkpoint(
        model_name=model_name,
        tokenizer_name=tokenizer_name,
        checkpoint_path=checkpoint_path,
        query_formatter=query_formatter,
        document_formatter=document_formatter,
        sha256=sha256,
        dtype=dtype,
        cache_dir=cache_dir,
        use_fp8=True,
    )


@register_scorer("dense_scorer_v2_neox")
def create_dense_scorer_from_neox_checkpoint(
    checkpoint_path: str | Path,
    query_formatter: RetrieverPromptFormatter[QueryT],
    document_formatter: DocumentPromptFormatter,
    cache_dir: Path | str | None = None,
    **ignored,
) -> DenseRetrievalScorerV2[QueryT]:
    from research.models.embedding_models.neox import create_from_neox_checkpoint

    # load configuration
    query_model, doc_model = create_from_neox_checkpoint(checkpoint_path)

    # Derive cache path from checkpoint path.
    if cache_dir:
        cache_path = Path(cache_dir) / Path(checkpoint_path).name
        doc_model = CachedEmbeddingModel(doc_model, cache_path=cache_path)

    return DenseRetrievalScorerV2(
        query_embedder=query_model,
        doc_embedder=doc_model,
        query_formatter=query_formatter,
        document_formatter=document_formatter,
    )


def _cast_dtype(dtype: str) -> torch.dtype:
    if dtype == "float16":
        return torch.float16
    elif dtype == "bfloat16":
        return torch.bfloat16
    elif dtype == "float32":
        return torch.float32
    else:
        raise ValueError(f"Invalid dtype: {dtype}")


def _get_available_doc_ids(
    doc_ids: Collection[DocumentId] | None,
    scoring_data: dict[DocumentId, DocDenseScoringData],
) -> set[DocumentId]:
    if doc_ids is None:
        return set(scoring_data.keys())
    else:
        available_doc_ids = set(doc_ids) & set(scoring_data.keys())
        missing_doc_ids = set(doc_ids).difference(available_doc_ids)
        missing_docs_string = ", ".join(list(missing_doc_ids)[:10])
        if len(missing_doc_ids) > 10:
            missing_docs_string += f"... and {len(missing_doc_ids) - 10} more."
        if available_doc_ids != doc_ids:
            logger.warning(
                f"{len(available_doc_ids)}/{len(doc_ids)} doc ids are available.\n"
                f"These are missing: {missing_docs_string}"
            )
        return available_doc_ids

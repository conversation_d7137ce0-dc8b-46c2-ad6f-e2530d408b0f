"""This is an interface for creating scoring techniques for retrieval."""

import logging
import threading
from collections.abc import Collection, Mapping, Sequence
from typing import Optional, Protocol, TypeVar

from research.retrieval.types import (
    Chunk,
    ChunkId,
    DocumentId,
    RetrievalScore,
)

QueryT = TypeVar("QueryT", contravariant=True)

logger = logging.getLogger(__name__)


class RetrievalDatabaseScorer(Protocol[QueryT]):
    """Interface for a scorer, used by RetrievalDatabase.

    Scorers typically maintain a cache of embeddings for documents,
    and a cache of embeddings for queries, while the database
    maintains a cache of documents.
    """

    def load(self):
        """Load the model."""

    def unload(self):
        """Unload the model and free GPU memory."""

    def add_doc(self, doc_id: DocumentId, chunks: list[Chunk]) -> None:
        """Hook for adding a doc. Prefer using add_docs."""

    def add_docs(self, doc_chunks: Mapping[DocumentId, list[Chunk]]) -> None:
        """Hook to add multiple documents."""
        for doc_id, chunks in doc_chunks.items():
            self.add_doc(doc_id, chunks)

    def remove_doc(self, doc_id: DocumentId) -> None:
        """Hook for removing a doc and all its chunks."""

    def remove_all_docs(self) -> None:
        """Hook for removing all docs."""

    def score(
        self, query: QueryT, doc_ids: Optional[Collection[DocumentId]] = None
    ) -> tuple[Sequence[tuple[DocumentId, ChunkId]], Sequence[RetrievalScore]]:
        """Internal method to compute scores for a query, possibly unsorted."""
        raise NotImplementedError()


class _RetrievalDatabaseScorerRegistry:
    """Retrieval database scorer registry."""

    def __init__(self):
        self._registry = dict()

    def get(self, name) -> type[RetrievalDatabaseScorer]:
        """Returns the retrieval database scorer class by name."""
        if name not in self._registry:
            raise KeyError(
                f"retrieval database scorer {name!r} is not registered."
                f"\nHere are the available retrieval database scorers: {list(self._registry.keys())}"
            )
        return self._registry[name]

    def register(
        self, name: str, retrieval_database_scorer_cls: type[RetrievalDatabaseScorer]
    ):
        """Register a function with a scheme name."""
        if name in self._registry:
            raise KeyError(f"Retrieval database scorer {name!r} already registered.")
        logger.debug(
            f"Register the {name} retrieval database scorer as {retrieval_database_scorer_cls}"
        )
        self._registry[name] = retrieval_database_scorer_cls

    def cleanup(self):
        """Removing all the existing registry."""
        logger.debug(
            f"Clean up all {len(self._registry)} existing retrieval database scorer."
        )
        self._registry = dict()

    @property
    def names(self) -> list[str]:
        """Returns registered scheme names."""
        return list(self._registry.keys())


_RETRIEVAL_DATABASE_SCORER_REGISTRY = _RetrievalDatabaseScorerRegistry()
_REGISTRY_LOCKER = threading.Lock()


def register_scorer(name: Optional[str] = None):
    """Decorate a retrieval database scorer class and register it."""

    def _decorator(cls):
        """Returns the decorated retrieval database scorer class."""
        with _REGISTRY_LOCKER:
            _RETRIEVAL_DATABASE_SCORER_REGISTRY.register(name or cls.__name__, cls)
        return cls

    return _decorator


def list_scorers() -> list[str]:
    """List all avaliable retrieval_database_scorers."""
    with _REGISTRY_LOCKER:
        return _RETRIEVAL_DATABASE_SCORER_REGISTRY.names


def cleanup_scorers():
    with _REGISTRY_LOCKER:
        _RETRIEVAL_DATABASE_SCORER_REGISTRY.cleanup()


def get_scorer(name: str, **kwargs) -> RetrievalDatabaseScorer:
    """Create a retrieval_database_scorer by its class name."""
    with _REGISTRY_LOCKER:
        cls = _RETRIEVAL_DATABASE_SCORER_REGISTRY.get(name)
    return cls(**kwargs)

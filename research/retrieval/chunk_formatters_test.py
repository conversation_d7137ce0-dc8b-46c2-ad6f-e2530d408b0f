"""Test research/retrieval/chunk_formatters.py.

pytest research/retrieval/chunk_formatters_test.py
"""

from collections.abc import Sequence

import numpy as np
import torch
from absl.testing import absltest

from base.tokenizers.tokenizer import (
    RetrievalSpecialTokens,
    Tokenizer,
)
from research.core.types import Chunk, Document
from research.retrieval import chunk_formatters


class MockSpecialTokens(RetrievalSpecialTokens):
    """Special tokens for testing."""

    def __init__(self):
        super().__init__()
        self.fim_prefix = 0
        self.fim_suffix = 1
        self.fim_middle = 2
        self.padding = 3
        self.eos = 4
        self.newline = 5
        self.skip = 6
        self.pause = 7
        self.begin_sequence: tuple[int, ...] = ()
        self.retrieval_section = 8
        self.ret_start = 9
        self.ret_body = 10
        self.prefix_body = 11
        self.start_of_key = 101
        self.end_of_query = 102
        self.end_of_key = 103


class MockTokenizer(Tokenizer):
    """Tokenizer for testing."""

    def __init__(self):
        self.name = "mock_tokenizer"
        self._special_tokens = MockSpecialTokens()

    def tokenize_safe(self, text: str):
        def tok_to_int(tok):
            tok_ord = ord(tok)
            if tok_ord >= ord("a") and tok_ord <= ord("z"):
                return tok_ord - ord("a") + 1
            if tok == "\n":
                return 100
            return 0

        return [tok_to_int(tok) for tok in text]

    def tokenize_unsafe(self, text: str):
        return self.tokenize_safe(text)

    def detokenize(self, token_ids: Sequence[int] | torch.Tensor | np.ndarray) -> str:
        if isinstance(token_ids, torch.Tensor):
            token_ids = token_ids.tolist()
        elif isinstance(token_ids, np.ndarray):
            token_ids = token_ids.tolist()

        def int_to_tok(tok_id):
            if tok_id >= 1 and tok_id <= 26:
                return chr(ord("a") + tok_id - 1)
            if tok_id == 100:
                return "\n"
            return ""

        return "".join(int_to_tok(tok_id) for tok_id in token_ids)

    @property
    def vocab_size(self):
        return -1

    @property
    def vocab(self):
        return {}

    @property
    def inv_vocab(self):
        return {}

    @property
    def special_tokens(self):
        """Return an object with attributes for special tokens."""
        return self._special_tokens

    def detokenize_with_offsets(
        self, token_ids: Sequence[int]
    ) -> tuple[str, list[int]]:
        del token_ids
        return "", []


class TestChunkFormatter(absltest.TestCase):
    """Tests chunk formatters and its registry."""

    def test_clear_chunk_formatter_registry(self):
        """Tests `clear_chunk_formatter_registry` clear the registry."""
        chunk_formatters.clear_chunk_formatter_registry()
        self.assertEmpty(chunk_formatters.list_chunk_formatters())

    def test_register_chunk_formatter(self):
        """Tests the register."""
        chunk_formatters.clear_chunk_formatter_registry()
        self.assertEmpty(chunk_formatters.list_chunk_formatters())

        # pylint: disable=unused-variable
        @chunk_formatters.register_chunk_formatter("dummy")
        class DummyChunkFormatter(chunk_formatters.AbstractChunkFormatter):
            """Chunk Formatter for testing."""

            def create_default_tokenizer(self) -> Tokenizer:
                return chunk_formatters.get_tokenizer("CodeGenTokenizer")

            def format(
                self, chunk: chunk_formatters.Chunk
            ) -> chunk_formatters.FormattedChunk:
                return chunk_formatters.FormattedChunk(tokens=[], text="")

        self.assertListEqual(chunk_formatters.list_chunk_formatters(), ["dummy"])
        chunk_formatters.clear_chunk_formatter_registry()

    def test_simple_chunk_formatter(self):
        text = "b\n" * 50
        formatter = chunk_formatters.SimpleChunkFormatter()
        formatter.tokenizer = MockTokenizer()
        formatted_chunk = formatter.format(
            Chunk(
                id="",
                text=text,
                parent_doc=Document(id="", text=""),
                char_offset=-1,
                length=-1,
                line_offset=-1,
                length_in_lines=-1,
            )
        )
        self.assertListEqual(formatted_chunk.tokens, [2, 100] * 50)
        self.assertEqual(formatted_chunk.text, text)

    def test_simple_chunk_formatter_with_path(self):
        """Tests the formatter with path."""
        text = "b\n" * 50
        path = "cccdddeee"
        formatter = chunk_formatters.SimpleChunkFormatter(add_path=True)
        formatter.tokenizer = MockTokenizer()
        formatted_chunk = formatter.format(
            Chunk(
                id="",
                text=text,
                parent_doc=Document(id="", text="", path=path),
                char_offset=-1,
                length=-1,
                line_offset=-1,
                length_in_lines=-1,
            )
        )
        expected = (
            [3, 3, 3, 4, 4, 4, 5, 5, 5]  # Path
            + [100]  # Newline separator
            + [2, 100] * 50  # Document
        )
        self.assertListEqual(formatted_chunk.tokens, expected)
        self.assertEqual(formatted_chunk.text, path + "\n" + text)

    def test_simple_chunk_formatter_max_tokens(self):
        """Tests the formatter max tokens."""
        text = "b\n" * 50
        path = "cccdddeee"
        formatter = chunk_formatters.SimpleChunkFormatter(max_tokens=20, add_path=True)
        formatter.tokenizer = MockTokenizer()
        formatted_chunk = formatter.format(
            Chunk(
                id="",
                text=text,
                parent_doc=Document(id="", text="", path=path),
                char_offset=-1,
                length=-1,
                line_offset=-1,
                length_in_lines=-1,
            )
        )
        expected = (
            [3, 3, 3, 4, 4, 4, 5, 5, 5]  # Path
            + [100]  # Newline separator
            + [2, 100] * 5  # Document
        )
        self.assertListEqual(formatted_chunk.tokens, expected)
        self.assertEqual(formatted_chunk.text, path + "\n" + text[:10])


if __name__ == "__main__":
    absltest.main()

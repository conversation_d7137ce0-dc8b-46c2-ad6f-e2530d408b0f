"""Classes that are used to build prompts for `Chunk`."""

import abc

from attr import dataclass

from base.prompt_format.common import PromptFormatterOutput
from base.prompt_format_retrieve.prompt_formatter import RetrieverPromptFormatter
from base.static_analysis.parsing import <PERSON>ope<PERSON>rS<PERSON>, SrcSpan
from base.tokenizers.tokenizer import (
    RetrievalSpecialTokens,
    Tokenizer,
)
from research.core.tokenizers import get_tokenizer
from research.core.types import Chunk
from research.core.ui_sugar import UISugar

_CHUNK_FORMATTER_REGISTRY = {}


@dataclass
class FormattedChunk:
    """A class to store the formatted chunk."""

    tokens: list[int]
    """A list of token ids."""

    text: str
    """String representation of the formatted chunks."""

    metadata: dict | None = None
    """Additional metadata about the formatted chunk."""


class AbstractChunkFormatter(UISugar, RetrieverPromptFormatter[Chunk]):
    """Base class for chunk prompt formatter."""

    def __init__(self, **kwargs):
        self._tokenizer = None
        self.rebind(**kwargs)

    @abc.abstractmethod
    def create_default_tokenizer(self) -> Tokenizer:
        """A function to create the default tokenizer."""

    @property
    def tokenizer(self) -> Tokenizer:
        """Returns the tokenizer.

        To return a custom tokenizer, users can simply do the following.
        <AbstractPromptFormatter>.tokenizer = AnotherTokenizer()
        """
        if self._tokenizer is None:
            self._tokenizer = self.create_default_tokenizer()
        return self._tokenizer

    @tokenizer.setter
    def tokenizer(self, value: Tokenizer):
        self._tokenizer = value

    @abc.abstractmethod
    def format(self, chunk: Chunk) -> FormattedChunk:
        """Build tokenized prompt.

        Args:
            input: ModelInput object describing input.

        Returns:
            prompt as list of tokens,
            dictionary of metdata about prompt formatting (e.g. # of tokens truncated).
        """

    # vv RetrieverPromptFormatter protocol implementation vv.

    input_type = Chunk

    def format_prompt(self, prompt_input: Chunk) -> PromptFormatterOutput:
        ret = self.format(prompt_input)
        return PromptFormatterOutput([ret.tokens])


def register_chunk_formatter(name: str):
    """Decorator used to register a new chunk formatter."""

    def _decorator(cls):
        if name in _CHUNK_FORMATTER_REGISTRY:
            raise ValueError(
                f"Cannot register the chunk formatter: {name}. Already exists."
            )
        _CHUNK_FORMATTER_REGISTRY[name] = cls
        return cls

    return _decorator


def list_chunk_formatters() -> list[str]:
    """Returns a list of Chunk formatters."""
    return list(_CHUNK_FORMATTER_REGISTRY)


def get_chunk_formatter(name, **kwargs) -> AbstractChunkFormatter:
    """Returns a chunk formatter if available."""
    if name not in _CHUNK_FORMATTER_REGISTRY:
        raise KeyError(
            f"Cannot find chunk formatter: {name}. The followings are available chunkers: {list_chunk_formatters()}."
        )
    return _CHUNK_FORMATTER_REGISTRY[name](**kwargs)


def clear_chunk_formatter_registry():
    """Removes all from the registry."""
    _CHUNK_FORMATTER_REGISTRY.clear()


@register_chunk_formatter("simple_document")
class SimpleChunkFormatter(AbstractChunkFormatter):
    """A document formatter."""

    add_path: bool = False
    "If True, prepend path to prefix."

    tokenizer_name: str = "CodeGenTokenizer"
    """Name of the tokenizer to use."""

    max_tokens: int = -1
    """Maximum number of allowed tokens. -1 means unlimited."""

    def create_default_tokenizer(self) -> Tokenizer:
        return get_tokenizer(self.tokenizer_name)

    def format(self, chunk: Chunk) -> FormattedChunk:
        """Returns tokenized prompt."""
        text = chunk.text
        if self.add_path and chunk.parent_doc.path:
            text = f"{chunk.parent_doc.path}\n{text}"
        # Do not tokenize special tokens for user documents.
        tokens = self.tokenizer.tokenize_safe(text)
        if self.max_tokens >= 0 and len(tokens) > self.max_tokens:
            tokens = tokens[: self.max_tokens]
            text = self.tokenizer.detokenize(tokens)

        return FormattedChunk(tokens=tokens, text=text)


@register_chunk_formatter("signature_document")
class SignatureChunkFormatter(AbstractChunkFormatter):
    """A simple document formatter using `Chunk.key`."""

    def format(self, chunk: Chunk) -> FormattedChunk:
        """Returns tokenized prompt."""
        if chunk.meta is None or "src_scope" not in chunk.meta:
            raise ValueError(
                'Cannot format this chunk. This formatter requires `chunk.meta["src_scope"]`.'
            )
        text = scope_as_stub(chunk.meta["src_scope"])
        return FormattedChunk(tokens=self.tokenizer.tokenize_safe(text), text=text)


def scope_as_stub(s: ScopeOrSpan) -> str:
    """Emits function bodies and returns source code."""
    if isinstance(s, SrcSpan):
        return s.code
    output = s.prefix.code
    if s.kind == "function":
        return output + "...\n"
    for child in s.children:
        output += scope_as_stub(child)
    output += s.suffix.code
    return output


# TODO(jeff): add a test for this.
@register_chunk_formatter("ethanol6_document")
class Ethanol6ChunkFormatter(AbstractChunkFormatter):
    """Experimental document formatter for Ethanol6 models."""

    max_tokens: int = -1
    """Maximum number of allowed tokens. -1 means unlimited."""

    add_path: bool = False
    """If True, add file path to the prompt."""

    add_prefix: bool = False
    """If True, add chunk prefix to the prompt."""

    add_suffix: bool = False
    """If True, add chunk suffix to the prompt."""

    est_chars_per_token: int = 4
    """Estimated number of characters per token."""

    tokenizer_name: str = "CodeGenTokenizer"
    """Name of the tokenizer to use."""

    def create_default_tokenizer(self) -> Tokenizer:
        return get_tokenizer(self.tokenizer_name)

    def format(self, chunk: Chunk) -> FormattedChunk:
        """Returns tokenized prompt."""
        text = chunk.text
        special_tokens = self.tokenizer.special_tokens
        assert isinstance(special_tokens, RetrievalSpecialTokens)
        text_tokens = [special_tokens.fim_middle] + self.tokenizer.tokenize_safe(text)

        tokens = []

        tokens.extend(special_tokens.begin_sequence)
        tokens.append(special_tokens.start_of_key)

        if self.add_path:
            if chunk.parent_doc.path is None:
                raise ValueError("chunk.parent_doc.path is None")
            tokens.extend(self.tokenizer.tokenize_safe(chunk.parent_doc.path))

        if self.add_prefix:
            chunk_start = chunk.char_offset

            prefix_text = chunk.parent_doc.text[:chunk_start]
            prefix_est_chars = self.max_tokens * self.est_chars_per_token
            if self.max_tokens > 0 and len(prefix_text) > prefix_est_chars:
                prefix_text = prefix_text[len(prefix_text) - prefix_est_chars :]

            prefix_tokens = [special_tokens.fim_prefix] + self.tokenizer.tokenize_safe(
                prefix_text
            )
        else:
            prefix_tokens = []

        if self.add_suffix:
            chunk_end = chunk.char_offset + chunk.length
            suffix = chunk.parent_doc.text[chunk_end:]
            suffix_tokens = [special_tokens.fim_suffix] + self.tokenizer.tokenize_safe(
                suffix
            )
        else:
            suffix_tokens = []

        if (
            not self.max_tokens
            or self.max_tokens <= 0
            or self.max_tokens
            >= len(tokens) + len(prefix_tokens) + len(suffix_tokens) + len(text_tokens)
        ):
            # No trimming is needed.
            tokens.extend(prefix_tokens)
            tokens.extend(text_tokens)
            tokens.extend(suffix_tokens)
        elif self.max_tokens > len(tokens) + len(text_tokens):
            token_budget = self.max_tokens - len(tokens) - len(text_tokens)

            # The header and text fit, but the prefix+suffix do not. Trim prefix/suffix.

            # Split the budget, giving 2/3 to the prefix and 1/3 to the suffix.
            prefix_budget = (token_budget * 2) // 3
            suffix_budget = token_budget - prefix_budget

            # If the budget for prefix is more than needed, reassign the slack to the
            # suffix and vice versa.
            if prefix_budget > len(prefix_tokens):
                suffix_budget += prefix_budget - len(prefix_tokens)
                prefix_budget = len(prefix_tokens)
            elif suffix_budget > len(suffix_tokens):
                prefix_budget += suffix_budget - len(suffix_tokens)
                suffix_budget = len(suffix_tokens)

            # Clip the budgets to the numbers of tokens we actually have.
            prefix_budget = min(prefix_budget, len(prefix_tokens))
            suffix_budget = min(suffix_budget, len(suffix_tokens))

            # Validate the budgets.
            assert prefix_budget + suffix_budget <= token_budget
            assert prefix_budget <= len(prefix_tokens)
            assert suffix_budget <= len(suffix_tokens)

            if prefix_budget > 0:
                tokens.append(prefix_tokens[0])
                tokens.extend(prefix_tokens[len(prefix_tokens) - (prefix_budget - 1) :])
            tokens.extend(text_tokens)
            tokens.extend(suffix_tokens[:suffix_budget])

            assert len(tokens) == self.max_tokens
        elif self.max_tokens > len(tokens):
            # The text itself doesn't fit. Trim the text from the right.
            tokens.extend(text_tokens[: self.max_tokens - len(tokens)])
            assert len(tokens) == self.max_tokens
        else:
            # The header alone does not fit. Trim the header from the right.
            tokens = tokens[: self.max_tokens]

        formatted_chunk = FormattedChunk(
            tokens=tokens, text=self.tokenizer.detokenize(tokens)
        )
        return formatted_chunk

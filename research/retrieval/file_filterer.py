"""A simple utility to filter files we don't want to retrieve from."""

from base.static_analysis.parsing import _scope_tree_supports
from research.retrieval.types import Document
from research.static_analysis.common import guess_lang_from_fp


def basic_file_filterer(doc: Document) -> bool:
    """Return true if file is supported by static analysis."""
    return guess_lang_from_fp(doc.path) is None


def scope_aware_file_filterer(doc: Document) -> bool:
    """Return true if file has scope tree support."""
    return guess_lang_from_fp(doc.path) not in _scope_tree_supports

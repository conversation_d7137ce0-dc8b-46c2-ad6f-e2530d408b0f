"""This file implements a generic retrieval database with customization via dependencies.

It includes state management and interoperable chunking, formatting, and scoring strategies.
"""

import logging
from typing import Iterable, Optional

from research.core.model_input import ModelInput
from research.retrieval.retrieval_database import RetrievalDatabase
from research.retrieval.types import (
    Chunk,
    ChunkId,
    Document,
    DocumentId,
    DocumentIndex,
    RetrievalScore,
)

logger = logging.getLogger(__name__)
logger.setLevel(level=logging.INFO)


class MultiRetrievalDatabase(DocumentIndex):
    """This is a wrapper that merges the outputs of multiple retrieval databases."""

    def __init__(
        self,
        retrieval_databases: dict[str, RetrievalDatabase],
        database_weights: dict[str, int],
    ):
        """Initialize.

        Args:
            retrieval_databases: dict of database name to database
            database_weights: dict of database name to weight. The weight is an integer
                that represents how many samples we take from each database for each
                step in the loop over databases. In general, the samples we take from
                each database ends up roughly proportional to the weight (barring
                overlap and running out of samples for a database).
        """
        self.retrieval_databases = retrieval_databases
        self.database_weights = database_weights

    ###
    # GET DOCS
    ###
    def is_in(self, ids: list[DocumentId]) -> list[bool]:
        """Return list of bools indicating whether the document is present."""
        raise NotImplementedError

    def get_doc(self, doc_id: DocumentId) -> Optional[Document]:
        """Get a document from the database."""
        raise NotImplementedError

    def get_docs(self, ids: Iterable[DocumentId]) -> list[Document]:
        """Get a list of documents, ignoring missing docs."""
        raise NotImplementedError

    def get_doc_ids(self):
        """Add documents to index, and use name to reference."""
        raise NotImplementedError

    def get_chunks_ids_in_doc(self, doc_id: DocumentId) -> list[ChunkId]:
        """Get ChunkId's for all chunks in a document.

        If chunks have been deleted, then this function may return less chunks than the document actually contains.
        """
        raise NotImplementedError

    ###
    # ADD DOCS
    ###

    def add_doc(self, doc: Document) -> None:
        """Process a document and add to each database."""
        for database in self.retrieval_databases.values():
            database.add_doc(doc)

    def add_docs(self, docs: Iterable[Document]) -> None:
        """Process a list of documents and add to the database."""
        for database in self.retrieval_databases.values():
            database.add_docs(docs)

    def remove_doc(self, doc_id: DocumentId) -> None:
        """Remove a document from a database."""
        for database in self.retrieval_databases.values():
            database.remove_doc(doc_id)

    def remove_docs(self, ids: Iterable[DocumentId]) -> None:
        """Remove a list of document from a database."""
        for doc_id in ids:
            self.remove_doc(doc_id)

    def remove_all_docs(self) -> None:
        """Remove all documents from a database."""
        for database in self.retrieval_databases.values():
            database.remove_all_docs()

    ###
    # QUERY DOCS
    ###

    def merge_chunks_and_scores(
        self,
        top_k: Optional[int],
        database_chunks_and_scores: dict[str, tuple[list[Chunk], list[RetrievalScore]]],
    ):
        """Merge chunks and scores from multiple databases."""

        # Fill up a merged list by repeatedly looping over databases
        merged_chunks, merged_scores, chunk_ids = [], [], []
        while any(chunks for chunks, _ in database_chunks_and_scores.values()) and (
            top_k is None or len(merged_chunks) < top_k
        ):
            # Loop over databases
            for name, chunks_and_scores in database_chunks_and_scores.items():
                chunks, scores = chunks_and_scores
                # Get the next [weight] chunks from the database. Of those, add the
                # chunks that are not yet in the merged list.
                weight = self.database_weights[name]
                for _ in range(min(weight, len(chunks))):
                    chunk, score = chunks.pop(), scores.pop()
                    # Check if the chunk is already in the merged list
                    if chunk.id not in chunk_ids:
                        merged_chunks.append(chunk)
                        merged_scores.append(score)
                        chunk_ids.append(chunk.id)

        merged_chunks = merged_chunks[:top_k]
        merged_scores = merged_scores[:top_k]

        return merged_chunks, merged_scores

    def query(
        self,
        model_input: ModelInput,
        query_meta: Optional[dict] = None,
        doc_ids: Optional[list[DocumentId]] = None,
        top_k: Optional[int] = None,
    ) -> tuple[list[Chunk], list[RetrievalScore]]:
        """Return list of document chunks for the given query.

        This function merges the results from multiple databases. In particular, it
        repeatedly loops over the results from each database according to its weight
        until the top_k is filled up.

        Args:
            prefix: prefix of query
            suffix: suffix of query (when present the model will retrieve in FIM mode)
            path: path to filename
            query_meta: retriever-specific metadata that can guide the query
            doc_ids: optional list of document IDs to use for retrieval.
                Default is to use all available documents.
            top_k: optional limit to the number of retrieved documents. Default
                is no limit.

        Returns:
            A list of chunks, and a list of corresponding retrieval scores.
            The chunks are ordered from high to low score.
        """

        # Get chunks from each database
        database_chunks_and_scores = {}
        for name, database in self.retrieval_databases.items():
            chunks, scores = database.query(
                model_input,
                query_meta=query_meta,
                doc_ids=doc_ids,
                top_k=top_k,
            )
            # Reverse to pop higher scores from the end
            chunks, scores = list(reversed(chunks)), list(reversed(scores))
            database_chunks_and_scores[name] = (chunks, scores)

        merged_chunks, merged_scores = self.merge_chunks_and_scores(
            top_k, database_chunks_and_scores
        )

        return merged_chunks, merged_scores

    def load(self):
        """Loads components."""
        for database in self.retrieval_databases.values():
            database.load()

    def unload(self):
        """Unloads components."""
        for database in self.retrieval_databases.values():
            database.unload()

    @classmethod
    def from_yaml_config(cls, config: dict) -> "MultiRetrievalDatabase":
        """Instantiates MultiRetrievalDatabase from the config."""

        if "retrieval_database_configs" not in config:
            raise ValueError("Must provide retrieval_database_configs in config.")
        retrieval_databases = {
            db_name: RetrievalDatabase.from_yaml_config(db_config)
            for db_name, db_config in config["retrieval_database_configs"].items()
        }
        if "database_weights" in config:
            database_weights = config["database_weights"]
        else:
            database_weights = {name: 1 for name in retrieval_databases.keys()}
        return MultiRetrievalDatabase(retrieval_databases, database_weights)

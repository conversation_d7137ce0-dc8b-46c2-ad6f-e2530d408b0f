"""Common utilities for working with retrievers."""

import hashlib
import math
import tarfile
import tempfile
from itertools import accumulate
from pathlib import Path
from typing import Iterator, Sequence, TypeVar

from research.core.types import <PERSON>r<PERSON><PERSON><PERSON>

from .types import Chunk, Document

Span = Char<PERSON>ange


def get_line_offsets(text: str) -> list[int]:
    lines = text.splitlines(keepends=True)
    line_lengths = [len(line) for line in lines]
    line_offsets = list(accumulate(line_lengths, initial=0))
    return line_offsets


def filter_overlap_chunks(
    query_file_name: str, query_span: Span, chunks: list[Chunk]
) -> list[Chunk]:
    filtered_chunks = []
    for chunk in chunks:
        if chunk.parent_doc.path == query_file_name and chunk.range.intersect(
            query_span
        ):
            continue
        filtered_chunks.append(chunk)
    return filtered_chunks


def convert_repository_tarball_to_documents(tarball_path: str) -> list[Document]:
    """Convert repository in a tarball to a list of documents."""
    with tempfile.TemporaryDirectory() as temp_dir:
        # extract all contents into the temporary directory
        with tarfile.open(tarball_path) as tar:
            tar.extractall(path=temp_dir)

        dir_name = Path(tarball_path).stem
        repo_path = f"{temp_dir}/{dir_name}"

        docs = convert_repository_to_documents(repo_path)

    return docs


def convert_repository_to_documents(path: str) -> list[Document]:
    """Convert repository in directory structure to a list of documents."""
    docs = []
    total_docs = 0
    skipped_docs = 0

    for filepath in Path(path).rglob("*"):
        total_docs += 1

        if not filepath.is_file():
            skipped_docs += 1
            continue

        if (
            str(filepath).endswith(".bin")
            or str(filepath).endswith(".png")
            or str(filepath).endswith(".rmeta")
            or str(filepath).endswith(".o")
            or str(filepath).endswith(".so")
        ):
            skipped_docs += 1
            continue

        try:
            with filepath.open("r", encoding="utf8") as file:
                relative_filepath = filepath.relative_to(Path(path))
                text = file.read()
                digester = hashlib.sha256()
                digester.update(bytearray(str(relative_filepath) + text, "utf8"))
                document_id = digester.hexdigest()
                doc = Document(text=text, id=document_id, path=str(relative_filepath))
                docs.append(doc)
        except UnicodeDecodeError as e:
            skipped_docs += 1
            print(f"Failed on file {filepath} with error {e}. Skipping...")

    print(
        f"Processed {total_docs} total docs, skipping {skipped_docs} of them ({int(skipped_docs/total_docs * 100)}%)"
    )

    return docs


def parse_yaml_config(config: dict) -> tuple[str, dict]:
    """Returns class name and kwargs."""
    cls_name = config["name"]
    kwargs = config.copy()
    del kwargs["name"]
    return cls_name, kwargs


T = TypeVar("T")


def batched(values: Sequence[T], size: int) -> Iterator[Sequence[T]]:
    """Yields batches of size `size` from `values`."""
    for i in range(0, len(values), size):
        yield values[i : i + size]


def batched_uniform(values: Sequence[T], size: int) -> Iterator[Sequence[T]]:
    """Yields batches with (approximately) the same size from `values`."""
    num_batches = math.ceil(len(values) / size)
    # Splits `values` into batches of size `min_batch_size + 1` or `min_batch_size`.
    min_batch_size = len(values) // num_batches
    remainder = len(values) % num_batches
    start_idx = 0
    for i in range(num_batches):
        batch_size = min_batch_size + (1 if i < remainder else 0)
        yield values[start_idx : (start_idx + batch_size)]
        start_idx += batch_size

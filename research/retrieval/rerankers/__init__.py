from base.prompt_format_rerank.prompt_formatter import ChatRerankerPromptInput
from research.retrieval.rerankers.reranker_expand import (
    create_chat_reranker_expander_from_fastbackward_checkpoint,
)
from research.retrieval.rerankers.reranker_interface import Reranker_V2


def get_reranker_v2(name: str, **kwargs) -> Reranker_V2:
    """Create a reranker by its class name."""
    if name == "reranker_expander_chat_fbwd":
        return create_chat_reranker_expander_from_fastbackward_checkpoint(**kwargs)
    else:
        raise ValueError(f"Unknown reranker: {name}")

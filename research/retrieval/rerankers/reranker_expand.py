import dataclasses
from base.prompt_format_rerank.prompt_formatter import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ChatRerankerPromptInput,
)

from research.core.types import Chunk
from research.retrieval.rerankers.reranker_interface import Reranker_V2

from base.prompt_format.common import PromptChunk
from base.prompt_format_rerank.prompt_formatter import InputT as RerankerInputT

import torch

import abc
from dataclasses import dataclass
from pathlib import Path
from tensordict import TensorDict


@dataclass
class RerankerScores:
    """A dataclass holding the results of a reranking request."""

    gain: float | None
    gain_left: float | None
    gain_right: float | None


class RerankerExpanderBase(Reranker_V2[RerankerInputT]):
    """Base class for rerankers that can also expand chunks."""

    def __init__(
        self,
        model,
        abs_gain_score: bool,
        prompt_formatter: RerankerPromptFormatter[RerankerInputT],
        expand_factor: float = 3.0,
    ):
        if expand_factor <= 1.0:
            raise ValueError(f"Invalid expand factor: {expand_factor}")

        self.model = model
        self.abs_gain_score = abs_gain_score
        self.prompt_formatter = prompt_formatter
        self.expand_factor = expand_factor

    def load(self):
        # Nothing to do here.
        pass

    def unload(self):
        # Nothing to do here.
        pass

    @abc.abstractmethod
    def compute_predictions(
        self, prompt: RerankerInputT, chunks: list[Chunk]
    ) -> list[RerankerScores]:
        pass

    def _expand(self, chunk: Chunk, to_left: bool):
        from experimental.igor.systems import chatanol

        expand_by = round(chunk.length_in_lines * (self.expand_factor - 1))
        return chatanol.select_chunk_lines(
            chunk.parent_doc,
            chunk.line_offset - (expand_by if to_left else 0),
            chunk.length_in_lines + expand_by,
        )

    def rerank(
        self, prompt: RerankerInputT, chunks: list[Chunk]
    ) -> tuple[list[Chunk], list[float]]:
        scores_list = self.compute_predictions(prompt, chunks)

        expanded_chunks_with_scores: list[tuple[Chunk, float, str]] = []
        for chunk, score_obj in zip(chunks, scores_list):
            gain = score_obj.gain
            assert gain is not None
            # normalize by length (the + 1 protects against empty strings)
            if not self.abs_gain_score:
                gain /= len(chunk.text) + 1

            # add chunk
            assert score_obj.gain is not None
            expanded_chunks_with_scores.append((chunk, gain, "No expansion"))

            # add expanded chunks
            for new_chunk, score, exp_type in [
                (
                    self._expand(chunk, to_left=True),
                    score_obj.gain_left,
                    "Left expansion",
                ),
                (
                    self._expand(chunk, to_left=False),
                    score_obj.gain_right,
                    "Right expansion",
                ),
            ]:
                assert score is not None

                if not self.abs_gain_score:
                    # expansion score should always be greater than no expansion
                    score += score_obj.gain
                    # normalize by length (the + 1 protects against empty strings)
                    score /= len(new_chunk.text) + 1

                expanded_chunks_with_scores.append((new_chunk, score, exp_type))

        # sort by scores
        expanded_chunks_with_scores.sort(key=lambda x: x[1], reverse=True)

        return (
            [chunk for chunk, _, _ in expanded_chunks_with_scores],
            [score for _, score, _ in expanded_chunks_with_scores],
        )


class RerankerExpanderFastBackward(RerankerExpanderBase[RerankerInputT]):
    """Reranker/expander from FastBackward checkpoints."""

    def compute_predictions(
        self,
        prompt: RerankerInputT,
        chunks: list[Chunk],
    ) -> list[RerankerScores]:
        prompt = dataclasses.replace(
            prompt,
            candidate_chunks=[
                PromptChunk(
                    text=chunk.text,
                    path=chunk.parent_doc.path,
                    unique_id=chunk.id,
                )
                for chunk in chunks
            ],
        )
        prompt_out = self.prompt_formatter.format_prompt(prompt)
        prompt_batch = list(prompt_out.batched_token_lists)
        batched_chunk_indices = [
            i for batch_i in prompt_out.batched_chunk_indices for i in batch_i
        ]

        # Score
        raw_reranker_output = self._reranker_forward(prompt_batch)

        # Unshuffle round robin scores
        unshuffled_reranker_output = sorted(
            zip(batched_chunk_indices, raw_reranker_output), key=lambda x: x[0]
        )
        # Throw away indices
        reranker_output = [x[1] for x in unshuffled_reranker_output]

        return reranker_output

    def _reranker_forward(self, prompt_batch: list[list[int]]) -> list[RerankerScores]:
        predictions = self.model.forward(
            TensorDict(
                {
                    "known_chunk_query_tokens_BLq": torch.tensor(
                        prompt_batch, device=torch.device("cuda")
                    ).unsqueeze(1),
                },
                batch_size=[len(prompt_batch)],
            )
        )
        gain_tensor = predictions["gains_BCD"]

        return [
            RerankerScores(
                gain=gain_tensor[i, 0].item(),
                gain_left=gain_tensor[i, 1].item(),
                gain_right=gain_tensor[i, 2].item(),
            )
            for i in range(gain_tensor.size(0))
        ]


def create_chat_reranker_expander_from_fastbackward_checkpoint(
    checkpoint_path: str | Path,
    tokenizer_name: str,
    prompt_formatter_name: str,
    model_key: str = "model",
    dtype: str | torch.dtype = torch.bfloat16,
    abs_gain_score: bool = False,
    **ignored,
) -> Reranker_V2[ChatRerankerPromptInput]:
    """Create a chat reranker/expander from a FastBackward checkpoint."""
    from research.fastbackward.retrieval_models import load_checkpoint
    from research.fastbackward import distributed
    from base.prompt_format_rerank import get_reranker_prompt_formatter_by_name
    from base.tokenizers import create_tokenizer_by_name

    if isinstance(dtype, str):
        dtype = {
            "float16": torch.float16,
            "bfloat16": torch.bfloat16,
            "float32": torch.float32,
        }[dtype]

    # load configuration
    distributed.init_distributed_for_training(1)
    components = load_checkpoint(checkpoint_path)
    model = components.get_with_type(model_key, torch.nn.Module)
    model.to(dtype=dtype, device=torch.device("cuda"))

    # create prompt formatter
    tokenizer = create_tokenizer_by_name(tokenizer_name)
    prompt_formatter = get_reranker_prompt_formatter_by_name(
        prompt_formatter_name, tokenizer=tokenizer
    )

    # create reranker/expander
    return RerankerExpanderFastBackward[ChatRerankerPromptInput](
        model=model,
        prompt_formatter=prompt_formatter,
        abs_gain_score=abs_gain_score,
    )

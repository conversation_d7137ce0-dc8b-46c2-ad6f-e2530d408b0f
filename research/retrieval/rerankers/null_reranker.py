"""This is a reranker that does not rerank."""

from research.core.model_input import ModelInput
from research.retrieval.rerankers.reranker_interface import Reranker


class <PERSON>ull<PERSON><PERSON>ker(Reranker):
    """This is a reranker that does not rerank."""

    def load(self):
        """Load reranking model."""
        pass

    def unload(self):
        """Unload reranking model."""
        pass

    def rerank(self, model_input: ModelInput) -> tuple[ModelInput, list[float]]:
        """Method that reranks model_input.retrieved_chunks based on other information in input."""
        return model_input, [1 for _ in range(len(model_input.retrieved_chunks))]

"""This is an interface for creating retrieval rerankers."""

from abc import ABC, abstractmethod
from typing import Generic

from base.prompt_format_rerank.prompt_formatter import InputT as RerankerInputT

from research.core.model_input import ModelInput
from research.core.types import Chunk


class Reranker(ABC):
    """This implements a reranker."""

    @abstractmethod
    def load(self):
        """Load reranking model."""

    @abstractmethod
    def unload(self):
        """Unload reranking model."""

    @abstractmethod
    def rerank(self, model_input: ModelInput) -> tuple[ModelInput, list[float]]:
        """Method that reranks model_input.retrieved_chunks based on other information in input."""


class Reranker_V2(ABC, Generic[RerankerInputT]):
    """Reranker interface with support for base prompt formatters."""

    @abstractmethod
    def load(self):
        """Load reranking model."""

    @abstractmethod
    def unload(self):
        """Unload reranking model."""

    @abstractmethod
    def rerank(
        self,
        prompt: RerankerInputT,
        chunks: list[Chunk],
    ) -> tuple[list[Chunk], list[float]]:
        """Method that reranks model_input.retrieved_chunks based on other information in input."""

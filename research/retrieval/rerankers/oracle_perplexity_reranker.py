"""This file implements a perplexity reranker for retrieval candidates."""

import logging

from research.core.model_input import ModelInput
from research.models import GenerativeLanguageModel
from research.retrieval.rerankers.reranker_interface import Reranker

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class OraclePerplexityReranker(Reranker):
    """Ranks retrieval candidates based on contribution to model perplexity."""

    def __init__(
        self,
        model: GenerativeLanguageModel,
        top_k: int,
        batchsize: int = 10,
    ):
        self.model = model
        self.top_k = top_k
        self.batchsize = batchsize

    def load(self):
        """Load reranking model."""
        if not self.model.is_loaded:
            self.model.load()

    def unload(self):
        """Unload reranking model."""
        self.model.unload()

    def _score(
        self,
        model_input: ModelInput,
        ground_truth: str,
    ) -> list[float]:
        """Returns a score for each retrieval candidate based on resulting perplexity.

        Args:
          model_input: `ModelInput` contains prefix, suffix, path, and a list of
           retrieved chunks.
          ground_truth: The ground truth for the middle.
          reduction: The reduction method to use, 'none' | 'mean' | 'sum'. If 'none',
            returns log likelihood for all tokens. Otherwise, returns aggregated log
            likelihood.

        Returns:
          A list of scores, one score for each retrieval candidate. If
          `reduction`=='none', returns a list of lists of scores. One list of scores for
          each retrieval candidate for per-token log likelihood.

        """
        scores: list[float] = []
        chunks = model_input.retrieved_chunks

        # List of chunks -> a model input
        def model_input_builder(_chunks):
            return ModelInput(
                prefix=model_input.prefix,
                suffix=model_input.suffix,
                path=model_input.path,
                retrieved_chunks=_chunks,
            )

        for i in range(0, len(chunks), self.batchsize):
            seqs_to_score = []
            for chunk in chunks[i : (i + self.batchsize)]:
                iter_input = model_input_builder([chunk])
                seqs_to_score.append(iter_input)

            scores += self.model.log_likelihood_continuation(
                seqs_to_score,
                [ground_truth for _ in range(len(seqs_to_score))],
                reduction="mean",
            )

        assert isinstance(scores, list)
        assert all(isinstance(x, float) for x in scores)
        return scores

    def rerank(self, model_input: ModelInput) -> tuple[ModelInput, list[float]]:
        """Finds lowest perplexity retrievals for model + prompt combination."""
        # Temporary: assume target field is not populated, until we decide what to do with it.
        if model_input.target not in [None, ""]:
            logging.info(
                "OraclePerplexityReranker::rerank: model_input.target is not empty.\n"
                f"model_input.target={model_input.target}\n"
                f'model_input.extra["ground_truth"]={model_input.extra["ground_truth"]}'
            )
        assert isinstance(model_input.extra["ground_truth"], str), (
            model_input.extra["ground_truth"],
            type(model_input.extra["ground_truth"]),
        )

        reranked_scores = self._score(model_input, model_input.extra["ground_truth"])

        sorted_chunks_and_scores = sorted(
            zip(model_input.retrieved_chunks, reranked_scores),
            key=lambda text_and_score: text_and_score[1],
            reverse=True,
        )
        sorted_chunks = [chunk for chunk, _ in sorted_chunks_and_scores]
        sorted_scores = [score for _, score in sorted_chunks_and_scores]

        new_model_input = model_input.clone()
        new_model_input.retrieved_chunks = sorted_chunks[: self.top_k]

        return new_model_input, sorted_scores[: self.top_k]

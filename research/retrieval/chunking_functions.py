"""This implements various chunking strategies."""

from __future__ import annotations

import bisect
import dataclasses
import logging
import zlib
from pathlib import Path
from dataclasses import dataclass
from base.retrieval.chunking.utils.jupyternb_conversion import convert_file

from base.languages.language_guesser import guess_language
from base.ranges import LineRange
from base.retrieval.chunking import split_line_chunks
from base.static_analysis.common import is_parsing_supported_lang
from base.static_analysis.signature_utils import (
    FileSignatureInfo,
    SignaturePrinter,
    is_signature_supported_lang,
)
from base.static_analysis.usage_analysis import FileSummary, ParsedFile
from research.next_edits.smart_chunking import SmartChunker
from research.retrieval import utils as rutils
from research.retrieval.types import (
    Chunk,
    <PERSON>ker,
    ChunkId,
    Document,
    DocumentId,
)
from research.static_analysis import parsing
from research.static_analysis.common import (
    are_comments_supported,
    guess_lang_from_fp,
    make_comment_block,
)

__CHUNKER_REGISTRY = {}
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


def register_chunker(name: str):
    """Decorate a `Chunker` class and register it."""

    def _decorator(cls):
        if name in __CHUNKER_REGISTRY:
            raise ValueError(f"Cannot register the chunker: {name}. Already exist.")
        __CHUNKER_REGISTRY[name] = cls
        return cls

    return _decorator


def list_chunker():
    """Lists all available chunker."""
    return list(sorted(__CHUNKER_REGISTRY.keys()))


def get_chunker(name, **kwargs) -> Chunker:
    """Returns the chunk if available."""
    if name not in __CHUNKER_REGISTRY:
        raise KeyError(
            f"Cannot find chunker: {name}. The followings are available chunkers: {list_chunker()}."
        )
    return __CHUNKER_REGISTRY[name](**kwargs)


def clear_registry():
    """Removes all from the registry."""
    __CHUNKER_REGISTRY.clear()


@dataclasses.dataclass
class _LineAnnotation:
    """A class to store the line annotation."""

    scope_path: tuple[str, ...]
    """Represents the scope path of the line."""
    line_number_in_scope: int
    """Represents the line number within the scope. The first line of a scope is 0."""
    line_number_in_file: int
    """Represents the line number within the file. The first line of a file is 0."""


@register_chunker("line_level")
class LineLevelChunker(Chunker):
    """Split a document into chunks based on line-level chunking."""

    def __init__(
        self,
        max_lines_per_chunk: int,
        max_chunk_size: int = 1_000_000,
        overlap_lines: int = 0,
        include_scope_annotation: bool = False,
    ):
        """Constructor.

        Args:
          max_lines_per_chunk: Max number of lines in each chunk.
          max_chunk_size: Max number of characters in each chunk.
          overlap_lines: the number of overlaping lines between adjacent lines.
          include_scope_annotation: If True, include relevant scope annotations in the
            `chunk.meta`.
        """
        if max_lines_per_chunk <= 0:
            raise ValueError(
                f"max_lines_per_chunk must be positive, got {max_lines_per_chunk}"
            )
        self.max_lines_per_chunk = max_lines_per_chunk
        self.max_chunk_size = max_chunk_size
        self.overlap_lines = overlap_lines
        self.include_scope_annotation = include_scope_annotation

    def split_into_chunks(self, doc: Document) -> list[Chunk]:
        """This function takes a document and returns a list of chunks."""
        scope_annotations = []
        if self.include_scope_annotation:
            scope_annotations = _doc_to_scope_paths(
                doc, parse_errored_root=False, merge_empty_chunks=True
            )

        def make_chunk(
            text: str,
            char_offset: int,
            line_offset: int,
            length_in_lines: int,
            header: str,
        ):
            meta = {}
            if self.include_scope_annotation:
                line_range = LineRange(line_offset, line_offset + length_in_lines)
                # Only keep scope paths that overlap with the chunk.
                selected = _range_search_scope_paths(scope_annotations, line_range)
                line_annotations: dict[int, _LineAnnotation] = create_line_annotations(
                    selected, line_range
                )

                meta["line_annotations"] = {
                    # Converts `_LineAnnotation` to dictionary for json serialization.
                    key: dataclasses.asdict(value)
                    for key, value in line_annotations.items()
                }

            return Chunk(
                id=get_chunk_id(doc.id, text),
                text=text,
                parent_doc=doc,
                char_offset=char_offset,
                length=len(text),
                line_offset=line_offset,
                length_in_lines=length_in_lines,
                header=header,
                meta=meta,
            )

        chunks: list[Chunk] = []
        for contents in split_line_chunks(
            doc.text,
            chunk_max_lines=self.max_lines_per_chunk,
            chunk_max_chars=self.max_chunk_size,
            overlap_lines=self.overlap_lines,
        ):
            chunk = make_chunk(
                text=contents.text,
                char_offset=contents.char_offset,
                line_offset=contents.line_offset,
                length_in_lines=contents.length_in_lines,
                header=contents.header,
            )
            chunks.append(chunk)
        logger.debug(f"[LineLevelChunker] {doc.id} has {len(chunks)} chunks.")
        return chunks


@register_chunker("scope_aware")
class ScopeAwareChunker(Chunker):
    """Split a document into chunks based on scope-aware chunking."""

    def __init__(
        self,
        max_lines_per_chunk: int,
        parse_errored_root: bool = False,
        keep_empty_chunks: bool = False,
        merge_empty_chunks: bool = True,
        include_scope_path_in_chunk_text: bool = False,
    ):
        """Constructor.

        Args:
          max_lines_per_chunk: Max number of lines for each chunk.
          parse_errored_root: If True, allow parse errored root node.
          keep_empty_chunks: If True, keep chunks with only whitespace characters.
          merge_empty_chunks: If True, merge whitespace-only chunks into the previous
            chunk.
          include_scope_path_in_chunk_text: If True, the scope path is included as
            a comment with the goal of improving retrieval
        """
        self.parser = parsing.ScopeTreeParser(
            parse_errored_root=parse_errored_root,
            merge_empty_spans=merge_empty_chunks,
        )
        self.max_lines_per_chunk = max_lines_per_chunk
        self.keep_empty_chunks = keep_empty_chunks
        self.include_scope_path_in_chunk_text = include_scope_path_in_chunk_text

    def split_into_chunks(self, doc: Document) -> list[Chunk] | None:
        line_map = parsing.LineMap(doc.text)

        lang = guess_lang_from_fp(doc.path)
        assert lang is not None, f"Cannot guess the language from file path: {doc.path}"
        try:
            root_scope = self.parser.parse(doc.text, doc.path or "", lang)
        except parsing.ParsingFailedError:
            repo_identifier = (doc.meta or {}).get("repo_identifier", "")
            logger.warning(
                "ParsingFailedError: Failed to parse the file: %s and %s.",
                repo_identifier,
                doc.id,
            )
            return None

        chunks: list[Chunk] = []

        def add_chunk(x: parsing.ScopeOrSpan, scope_path: tuple[str, ...]) -> None:
            assert lang is not None
            assert is_parsing_supported_lang(lang)
            char_start, _ = x.range.to_tuple()
            line_start, _ = line_map.crange_to_lrange(x.range).to_tuple()
            scope_lines = x.get_code().splitlines(keepends=True)

            chunk_line_offset, chunk_char_offset = line_start, char_start
            for chunk_lines in rutils.batched_uniform(
                scope_lines, self.max_lines_per_chunk
            ):
                assert len(chunk_lines) <= self.max_lines_per_chunk

                chunk_text = "".join(chunk_lines)
                if not self.keep_empty_chunks and chunk_text.isspace():
                    continue

                # TODO(vzhao): Consider move the `include_scope_path_in_chunk_text`
                # logic into prompt formatter. Chunking function should return raw
                # chunks.
                if self.include_scope_path_in_chunk_text and are_comments_supported(
                    lang
                ):
                    # TODO(guy) "." is a python-specific separator. This is in a
                    # comment so maybe it doesn't matter much, but e.g. C++ uses "::".
                    if len(scope_path) == 0:
                        scope_path_header = ""
                    else:
                        scope_path_header = make_comment_block(
                            lang=lang, text=".".join(scope_path) + "\n"
                        )
                    chunk_text = scope_path_header + chunk_text

                length = sum(len(line) for line in chunk_lines)
                length_in_lines = len(chunk_lines)
                chunk = Chunk(
                    id=get_chunk_id(doc.id, chunk_text),
                    text=chunk_text,
                    parent_doc=doc,
                    char_offset=chunk_char_offset,
                    length=length,
                    line_offset=chunk_line_offset,
                    length_in_lines=length_in_lines,
                    meta={"scope_path": scope_path},
                )
                chunks.append(chunk)

                chunk_line_offset += length_in_lines
                chunk_char_offset += length

        def add_chunks_recursively(
            x: parsing.ScopeOrSpan, scope_path: tuple[str, ...]
        ) -> None:
            if isinstance(x, parsing.SrcSpan):
                add_chunk(x, scope_path)
            elif isinstance(x, parsing.SrcScope):
                if x.kind == "function":
                    # For functions, don't separete the prefixes from the body.
                    add_chunk(x, scope_path + (x.name,))
                else:
                    pdoc = x.prefix_with_doc()
                    if x.kind == "file":
                        # do not add file name to the path.
                        new_path = scope_path
                    else:
                        new_path = scope_path + (x.name,)
                    if pdoc.range:
                        add_chunk(pdoc, new_path)
                    for child in x.children:
                        add_chunks_recursively(child, new_path)
                    if x.suffix.range:
                        add_chunk(x.suffix, new_path)

        add_chunks_recursively(root_scope, scope_path=())
        logger.debug(f"[ScopeAwareChunker] {doc.id} has {len(chunks)} chunks.")
        return chunks


_robust_parser = parsing.ScopeTreeParser(parse_errored_root=True)


@register_chunker("signature")
@dataclasses.dataclass
class SignatureChunker(Chunker):
    """Split a document into signature chunks."""

    max_docstr_chars: int = 0
    """Maximum number of docstring characters to include in signatures."""

    show_full_method_signatures: bool = False
    """Whether to show full method signatures in classes."""

    show_private_members: bool = False
    """Whether to show private members in signatures."""

    timeout_s: float = 10.0
    """Timeout to process a single file."""

    def __post_init__(self):
        self.signature_printer = SignaturePrinter(
            max_docstr_chars=self.max_docstr_chars,
            show_full_method_signatures=self.show_full_method_signatures,
        )

    @staticmethod
    def _get_signature_info(
        doc: Document,
        signature_printer: SignaturePrinter,
        show_private_members: bool,
    ) -> FileSignatureInfo | None:
        lang = guess_lang_from_fp(doc.path)
        if doc.path is None or lang is None or not is_signature_supported_lang(lang):
            return None
        try:
            pfile = ParsedFile.parse(
                Path(doc.path), lang, doc.text, parser=_robust_parser
            )
            summary = FileSummary.from_pfile(pfile)
            sig_info = signature_printer.get_signature_info(
                summary, pfile, show_private=show_private_members
            )
        except (parsing.ParsingFailedError, parsing.FileTypeNotSupportedError) as e:
            raise e
        return sig_info

    def split_into_chunks(self, doc: Document) -> list[Chunk] | None:
        try:
            sig_info = self._get_signature_info(
                doc, self.signature_printer, self.show_private_members
            )
        except (
            parsing.ParsingFailedError,
            parsing.FileTypeNotSupportedError,
            TimeoutError,
        ) as e:
            logger.warning(f"Failed to parse file: {doc.id} with error {e}")
            return None
        if sig_info is None:
            return None
        all_sigs = [sig_info.module_signature] + list(
            sig_info.symbol_signatures.values()
        )
        signature_chunks = list[Chunk]()
        for sig in all_sigs:
            chunk = Chunk(
                id=get_chunk_id(doc.id, sig.text),
                text=sig.text,
                parent_doc=doc,
                char_offset=sig.crange.start,
                length=len(sig.text),
                line_offset=sig.lrange.start,
                length_in_lines=len(sig.lrange),
                meta={"kind": "signature"},
            )
            signature_chunks.append(chunk)
        logger.debug(f"[SignatureChunker] {doc.id} has {len(signature_chunks)} chunks.")
        return signature_chunks


@register_chunker("signature_and_line")
@dataclasses.dataclass
class SignatureAndLineChunker(Chunker):
    """A chunker that combines `SignatureChunker` and `LineLevelChunker`."""

    max_lines_per_chunk: int
    """Maximum number of lines per chunk."""

    max_docstr_chars: int = 0
    """Maximum number of docstring characters to include in signatures."""

    show_full_method_signatures: bool = False
    """Whether to show full method signatures in classes."""

    show_private_members: bool = False
    """Whether to show private members in signatures."""

    def __post_init__(self):
        self.line_chunker = LineLevelChunker(
            max_lines_per_chunk=self.max_lines_per_chunk
        )
        self.signature_chunker = SignatureChunker(
            max_docstr_chars=self.max_docstr_chars,
            show_full_method_signatures=self.show_full_method_signatures,
            show_private_members=self.show_private_members,
        )

    def split_into_chunks(self, doc: Document) -> list[Chunk] | None:
        line_chunks = self.line_chunker.split_into_chunks(doc)
        signature_chunks = self.signature_chunker.split_into_chunks(doc)
        if line_chunks is None and signature_chunks is None:
            return None
        return (line_chunks or []) + (signature_chunks or [])


def get_chunk_id(doc_id: DocumentId, chunk_text: str) -> ChunkId:
    """Create a unique ChunkId by hashing the chunk's text and adding the document id (to ensure uniqueness at repo-level)."""
    chunk_hash = zlib.adler32(str.encode(chunk_text))
    return f"{doc_id}_{chunk_hash}"


def _maybe_parse_document(
    doc: Document, parser: parsing.ScopeTreeParser
) -> parsing.SrcScope | None:
    """Parse a document into a scope tree."""
    if (lang := guess_lang_from_fp(doc.path)) is None:
        return None
    try:
        return parser.parse(doc.text, doc.path or "", lang)
    except parsing.ParsingFailedError:
        repo_identifier = (doc.meta or {}).get("repo_identifier", "")
        logger.warning(
            "ParsingFailedError: Failed to parse the file: %s and %s.",
            repo_identifier,
            doc.id,
        )
        return None


@dataclasses.dataclass
class _ScopePath:
    """Represents a scope path."""

    scope_path: tuple[str, ...]
    """Scope path of a SrcSpan."""
    line_range: LineRange
    """Line range of a SrcSpan."""


def _doc_to_scope_paths(
    doc: Document,
    parse_errored_root: bool = False,
    merge_empty_chunks: bool = True,
) -> list[_ScopePath]:
    """Returns a sorted list of `_ScopePath`."""
    parser = parsing.ScopeTreeParser(
        parse_errored_root=parse_errored_root,
        merge_empty_spans=merge_empty_chunks,
    )
    if (root_scope := _maybe_parse_document(doc, parser)) is None:
        return []
    line_map = parsing.LineMap(doc.text)
    # List of (start line, end line, scope path)
    results: list[_ScopePath] = []

    def _maybe_append(sp: _ScopePath):
        if sp.scope_path:
            # If the scope path is empty, it means the scope is a file.
            results.append(sp)

    def get_scope_paths(x: parsing.ScopeOrSpan, scope_path: tuple[str, ...]) -> None:
        """Travers a scope tree and returns a list of (start line, end line, scope path)."""
        if isinstance(x, parsing.SrcSpan):
            line_range = line_map.crange_to_lrange(x.range)
            _maybe_append(_ScopePath(scope_path=scope_path, line_range=line_range))
        elif isinstance(x, parsing.SrcScope):
            new_scope_path = scope_path + (x.name,)
            if x.kind == "file":
                new_scope_path = scope_path

            if x.kind == "function":
                line_range = line_map.crange_to_lrange(x.range)
                _maybe_append(
                    _ScopePath(scope_path=new_scope_path, line_range=line_range)
                )
            else:
                pdoc = x.prefix_with_doc()
                if pdoc.range:
                    line_range = line_map.crange_to_lrange(pdoc.range)
                    _maybe_append(
                        _ScopePath(scope_path=new_scope_path, line_range=line_range)
                    )
                for child in x.children:
                    get_scope_paths(child, new_scope_path)
                if x.suffix.range:
                    line_range = line_map.crange_to_lrange(x.suffix.range)
                    _maybe_append(
                        _ScopePath(scope_path=scope_path, line_range=line_range)
                    )

    get_scope_paths(root_scope, scope_path=())
    return results


def _binary_search(values: list[int], target: int) -> int | None:
    """Find idx s.t. values[idx] <= target < values[idx + 1]."""
    idx = bisect.bisect(values, target)
    if idx == 0:
        return None
    return idx - 1


def _range_search_scope_paths(
    scope_paths: list[_ScopePath],
    target_line_range: LineRange,
) -> list[_ScopePath]:
    """Returns scope paths that overlaps `range`.

    Args:
        scope_paths: A sorted list of `_ScopePath`.
        target_line_range: Target line range.
    """
    if not scope_paths:
        return []
    chunk_line_start, chunk_line_end = target_line_range.to_tuple()
    # Find left such that
    #   scope_paths[left].start <= chunk_line_start < scope_paths[left].stop
    left = _binary_search([sp.line_range.start for sp in scope_paths], chunk_line_start)
    if left is None:
        left = 0
    if chunk_line_start >= scope_paths[left].line_range.stop:
        left += 1

    # Find right such that
    #   scope_paths[right][0].start < chunk_line_end <= scope_paths[right].stop
    right = bisect.bisect_left(
        [sp.line_range.start for sp in scope_paths], chunk_line_end
    )
    return scope_paths[left:right]


def _line_search_scope_paths(
    scope_paths: list[_ScopePath], line_num: int
) -> _ScopePath | None:
    """Returns `_ScopePath` if the line is within the range."""
    idx = _binary_search([sp.line_range.start for sp in scope_paths], line_num)
    if idx is None or line_num >= scope_paths[idx].line_range.stop:
        return None

    assert (
        scope_paths[idx].line_range.start <= line_num < scope_paths[idx].line_range.stop
    ), (scope_paths[idx].line_range, line_num)
    return scope_paths[idx]


def create_line_annotations(
    scope_paths: list[_ScopePath],
    line_range: LineRange,
) -> dict[int, _LineAnnotation]:
    """Creates line annotations from scope paths.

    Create `_LineAnnotation` for a line if the scope path of the line is different from
    that of the previous line.
    """
    seen = set()
    # dict[line_number_in_file, LineAnnotation]
    sp_annotations: dict[int, _LineAnnotation] = {}
    for line_idx in range(*line_range.to_tuple()):
        for sp in scope_paths:
            if line_idx in sp.line_range and sp.scope_path not in seen:
                # sp_annotations[line_idx] =  (sp[1], line_idx - sp[0].start)
                sp_annotations[line_idx] = _LineAnnotation(
                    scope_path=sp.scope_path,
                    line_number_in_scope=line_idx - sp.line_range.start,
                    line_number_in_file=line_idx,
                )
                seen.add(sp.scope_path)
                break
    return sp_annotations


@register_chunker("smart_line_level")
class SmartLineLevelChunker(SmartChunker, Chunker):
    def split_into_chunks(self, doc: Document) -> list[Chunk]:
        return [
            Chunk(
                id=get_chunk_id(doc.id, chunk.text),
                text=chunk.text,
                parent_doc=doc,
                char_offset=chunk.char_offset,
                length=len(chunk.text),
                line_offset=chunk.line_offset,
                length_in_lines=chunk.length_in_lines,
                header=chunk.header,
            )
            for chunk in self.split_chunks(doc.text, guess_language(doc.path))
        ]


@dataclass
class TransformChunker(Chunker):
    chunker: Chunker

    def split_into_chunks(
        self, doc: Document, max_output_length: int = 0
    ) -> list[Chunk]:
        try:
            if doc.path is not None and doc.path.endswith(".ipynb"):
                output = convert_file(doc.text, max_output_length=max_output_length)
                if output is None:
                    output = ""
            else:
                output = doc.text
        except Exception:
            output = doc.text

        textdoc = Document(
            id=doc.id,
            text=output,
            path=doc.path,
            meta=doc.meta,
        )

        chunks = self.chunker.split_into_chunks(textdoc)
        return chunks or []


@register_chunker("transform_smart_line_level")
class TransformSmartLineLevelChunker(TransformChunker):
    def __init__(self, max_chunk_chars: int, max_headers: int = 3):
        chunker = SmartLineLevelChunker(
            max_chunk_chars=max_chunk_chars, max_headers=max_headers
        )
        super().__init__(chunker=chunker)

"""Test document chunking.

Note: We have separate tests for the underlying chunking logic in:
- base/retrieval/chunking/line_based_chunking_test.py
- base/retrieval/chunking/smart_chunking_test.py
New tests should be added there and the tests in this file are safe to deprecate.

pytest research/retrieval/tests/test_chunking_functions.py
"""

import unittest
from pathlib import Path
from textwrap import dedent

from parameterized import parameterized

from base.ranges import LineRange
from research.core.utils import check_not_none
from research.retrieval import chunking_functions, utils
from research.retrieval.tests.data import patchcore_test_data
from research.retrieval.types import Chunk, Chunker, Document

# pylint: disable=unused-variable
# pylint: disable=protected-access

DOCUMENTS = [
    Document(
        text=patchcore_test_data.COMMON_PY,
        id="common.py_uuid",
        path="common.py",
        meta={},
    ),
    Document(
        text=patchcore_test_data.METRICS_PY,
        id="metrics.py_uuid",
        path="common.py",
        meta={},
    ),
]


class TestChunkers(unittest.TestCase):
    """Tests `chunking_functions`."""

    def test_line_level_chunker(self):
        """Tests `LineLevelChunker`."""
        text = dedent(
            """
            a
            b
            c
            dd
            ee
            ff
            ggg
            hhh
            iii
            jjjj
        """
        ).strip()
        doc = Document(id="1", text=text)
        chunker = chunking_functions.LineLevelChunker(
            max_lines_per_chunk=3, max_chunk_size=10_000
        )
        chunks = chunker.split_into_chunks(doc)

        assert len(chunks) == 4
        assert chunks[0].text == "a\nb\nc\n"
        assert chunks[1].text == "dd\nee\nff\n"
        assert chunks[2].text == "ggg\nhhh\niii\n"
        assert chunks[3].text == "jjjj"

        assert chunks[0].char_offset == 0
        assert chunks[0].length == len(chunks[0].text)
        assert chunks[0].line_offset == 0
        assert chunks[0].length_in_lines == 3

        assert chunks[1].char_offset == text.index("d")
        assert chunks[1].length == len(chunks[1].text)
        assert chunks[1].line_offset == 3
        assert chunks[1].length_in_lines == 3

        assert chunks[2].char_offset == text.index("ggg")
        assert chunks[2].length == len(chunks[2].text)
        assert chunks[2].line_offset == 6
        assert chunks[2].length_in_lines == 3

        assert chunks[3].char_offset == text.index("jjjj")
        assert chunks[3].length == len(chunks[3].text)
        assert chunks[3].line_offset == 9
        assert chunks[3].length_in_lines == 1

    @parameterized.expand(
        [
            (
                20,
                [
                    (
                        "    def _calc_idf(self, nd):\n"
                        "        for word, freq in nd.items():\n"
                        "            idf = math.log((self.corpus_size + 1) / freq)\n"
                        "            self.idf[word] = idf\n"
                        "\n"
                    ),
                    (
                        "    def _initialize(self, corpus):\n"
                        "        nd = {}  # word -> number of documents with word\n"
                        "        num_doc = 0\n"
                        "        for document in corpus:\n"
                        "            self.doc_len.append(len(document))\n"
                        "            num_doc += len(document)\n"
                        "\n"
                        "            frequencies = {}\n"
                        "            for word in document:\n"
                        "                if word not in frequencies:\n"
                        "                    frequencies[word] = 0\n"
                        "                frequencies[word] += 1\n"
                        "            self.doc_freqs.append(frequencies)\n"
                    ),
                    (
                        "\n"
                        "            for word, freq in frequencies.items():\n"
                        "                try:\n"
                        "                    nd[word]+=1\n"
                        "                except KeyError:\n"
                        "                    nd[word] = 1\n"
                        "\n"
                        "            self.corpus_size += 1\n"
                        "\n"
                        "        self.avgdl = num_doc / self.corpus_size\n"
                        "        return nd\n"
                        "\n"
                    ),
                ],
                [
                    ("BM25Plus", "_calc_idf"),
                    # `BM25._initialize` has more than 20 lines and is splitted into two
                    #  chunks.
                    ("BM25", "_initialize"),
                    ("BM25", "_initialize"),
                ],
                [
                    # Note: The line numbers are 0-based.
                    LineRange(183, 188),
                    LineRange(30, 43),
                    LineRange(43, 55),
                ],
            ),
        ]
    )
    def test_scope_aware_chunker(
        self,
        max_lines_per_chunk,
        expected: list[str],
        scope_paths: list[tuple],
        line_ranges: list[LineRange],
    ):
        """Tests `ScopeAwareChunker`."""
        test_file = Path(__file__).parent / "data" / "sample_corpus.txt"

        doc = Document(
            text=test_file.read_text(), id=str(test_file), path="sample.py", meta={}
        )

        code = test_file.read_text()
        scope_aware_chunker = chunking_functions.ScopeAwareChunker(
            max_lines_per_chunk=max_lines_per_chunk
        )
        chunks = scope_aware_chunker.split_into_chunks(doc)
        if chunks is None:
            raise ValueError("Expect scope_aware_chunker to success")
        for gt, scope_path, line_range in zip(expected, scope_paths, line_ranges):
            found = [x for x in chunks if x.text == gt]
            assert len(found) == 1, f"Expecting: {gt}."

            chunk = found[0]
            fn_body = "".join(gt.splitlines(keepends=True))
            chunk_span = chunk.range
            source = code[chunk_span.to_slice()]
            assert fn_body == source

            assert chunk.line_range == line_range
            assert check_not_none(chunk.meta)["scope_path"] == scope_path
            assert len(chunk.text) == chunk.length

    @parameterized.expand(
        [
            (DOCUMENTS[0], 60),
            (DOCUMENTS[1], 7),
        ]
    )
    def test_scope_aware_chunker_test_documents(
        self, document: Document, num_chunks: int
    ):
        """Tests `ScopeAwareChunker` of testing documents."""
        scope_aware_chunker = chunking_functions.ScopeAwareChunker(
            max_lines_per_chunk=20
        )
        chunks = scope_aware_chunker.split_into_chunks(document)
        assert chunks is not None
        for chunk in chunks:
            assert len(chunk.text.splitlines()) <= 20
        assert len(chunks) == num_chunks

    @parameterized.expand(["scope_aware", "line_level"])
    def test_filter_overlap(self, chunker_name):
        """Tests `filter_overlap_chunks`."""
        test_file = Path(__file__).parent / "data" / "sample_corpus.txt"

        docs: list[Document] = []
        for fname in ["sample1.py", "sample2.py"]:
            doc = Document(text=test_file.read_text(), id=fname, path=fname, meta={})
            docs.append(doc)

        text = test_file.read_text().splitlines()

        span_size = len(text) // 3
        query_span = utils.Span(span_size, 2 * span_size)

        if chunker_name == "scope_aware":
            chunker = chunking_functions.ScopeAwareChunker(max_lines_per_chunk=20)
        elif chunker_name == "line_level":
            chunker = chunking_functions.LineLevelChunker(20, max_chunk_size=10_000)
        else:
            raise ValueError(f"Unknown chunker: {chunker_name}")

        chunks: list[Chunk] = (chunker.split_into_chunks(docs[0]) or []) + (
            chunker.split_into_chunks(docs[1]) or []
        )

        chunk_table = {}
        for chunk in chunks:
            chunk_table[chunk.id] = chunk

        # Filter just one document
        print("Filtering span: ", docs[0].path, query_span)
        filtered_chunks = utils.filter_overlap_chunks(
            docs[0].path or "", query_span, chunks
        )
        non_overlap_ids = set(x.id for x in filtered_chunks)
        original_ids = set(chunk_table.keys())
        overlap_ids = original_ids - non_overlap_ids

        assert len(overlap_ids) > 0, f"{query_span=}, {chunks=}"
        assert len(non_overlap_ids) > 0, f"{query_span=}, {chunks=}"
        assert not overlap_ids.intersection(non_overlap_ids)

        overlap_chunks: list[Chunk] = [chunk_table[id] for id in overlap_ids]
        for chunk in overlap_chunks:
            assert (
                chunk.range.intersect(query_span)
                and chunk.parent_doc.path == docs[0].path
            )

        non_overlap_chunks: list[Chunk] = [chunk_table[id] for id in non_overlap_ids]
        for chunk in non_overlap_chunks:
            assert (
                not chunk.range.intersect(query_span)
                or chunk.parent_doc.path != docs[0].path
            )

    def test_doc_to_scope_paths(self):
        """Tests `doc_to_scope_paths`."""
        test_file = Path(
            "/home/<USER>/augment/research/retrieval/tests/data/sample_corpus.txt"
        )

        doc = Document(
            text=test_file.read_text("utf-8"),
            id=str(test_file),
            path="sample.py",
            meta={},
        )
        scope_paths = chunking_functions._doc_to_scope_paths(doc)
        _ScopePath = chunking_functions._ScopePath
        expected = [
            _ScopePath(("BM25",), LineRange(15, 16)),
            _ScopePath(("BM25", "__init__"), LineRange(16, 30)),
            _ScopePath(("BM25", "_initialize"), LineRange(30, 55)),
        ]
        for e in expected:
            assert e in scope_paths

    @parameterized.expand(
        [
            (0, None),
            (1, 0),
            (2, 1),
            (3, 1),
            (4, 2),
            (9, 3),
            (100, 3),
        ]
    )
    def test_binary_search(self, target: int, expected):
        values = [1, 2, 4, 8]
        assert chunking_functions._binary_search(values, target) == expected

    def test_binary_search_empty(self):
        assert chunking_functions._binary_search([], 1) is None

    @parameterized.expand(
        [
            (1, 0),
            (15, 0),
            (16, 1),
            (20, 2),
        ]
    )
    def test_line_search_scope_paths(self, line_num: int, expected_idx: int):
        scope_paths = [
            chunking_functions._ScopePath((), LineRange(1, 16)),
            chunking_functions._ScopePath((), LineRange(16, 17)),
            chunking_functions._ScopePath((), LineRange(20, 21)),
        ]

        assert (
            chunking_functions._line_search_scope_paths(scope_paths, line_num)
            == scope_paths[expected_idx]
        )

    @parameterized.expand([0, 17, 18, 22])
    def test_line_search_scope_paths_return_none(self, line_num: int):
        scope_paths = [
            chunking_functions._ScopePath((), LineRange(1, 16)),
            chunking_functions._ScopePath((), LineRange(16, 17)),
            chunking_functions._ScopePath((), LineRange(20, 21)),
        ]
        assert (
            chunking_functions._line_search_scope_paths(scope_paths, line_num) is None
        )

    @parameterized.expand(
        [
            (LineRange(0, 1), (0, 0)),
            (LineRange(0, 2), (0, 1)),
            (LineRange(0, 16), (0, 1)),
            (LineRange(0, 17), (0, 2)),
            (LineRange(5, 17), (0, 2)),
            (LineRange(5, 25), (0, 3)),
            (LineRange(16, 25), (1, 3)),
            (LineRange(17, 25), (2, 3)),
            (LineRange(20, 25), (2, 3)),
            (LineRange(21, 25), (3, 3)),
            (LineRange(22, 25), (3, 3)),
        ]
    )
    def test_range_search_scope_paths(
        self, line_range: LineRange, expected_idx: tuple[int, int]
    ):
        scope_paths = [
            chunking_functions._ScopePath((), LineRange(1, 16)),
            chunking_functions._ScopePath((), LineRange(16, 17)),
            chunking_functions._ScopePath((), LineRange(20, 21)),
        ]
        rst = chunking_functions._range_search_scope_paths(scope_paths, line_range)
        assert rst == scope_paths[expected_idx[0] : expected_idx[1]]

    def test_range_search_scope_paths_empty(self):
        rst = chunking_functions._range_search_scope_paths([], LineRange(5, 17))
        assert rst == []

    def test_signature_chunker(self):
        """Tests `SignatureChunker`."""
        test_file = Path(__file__).parent / "data" / "sample_corpus.txt"
        doc = Document(
            text=test_file.read_text(), id=str(test_file), path="sample.py", meta={}
        )
        chunker = chunking_functions.SignatureChunker()
        chunks = chunker.split_into_chunks(doc)
        assert chunks is not None
        assert len(chunks) >= 30, f"{len(chunks)=}"


class TestRegistry(unittest.TestCase):
    """Tests chunker registry."""

    def setUp(self):
        super().setUp()
        chunking_functions.clear_registry()

    def tearDown(self):
        super().tearDown()
        chunking_functions.clear_registry()

    def test_register_prompt_formatter(self):
        @chunking_functions.register_chunker("dummy")
        class DummyChunker(Chunker):
            def split_into_chunks(self, doc: Document) -> list[Chunk]:
                return []

        self.assertIn("dummy", chunking_functions.list_chunker())

    def test_register_prompt_formatter_raise(self):
        @chunking_functions.register_chunker("dummy")
        class DummyChunker(Chunker):
            def split_into_chunks(self, doc: Document) -> list[Chunk]:
                return []

        with self.assertRaises(ValueError):

            @chunking_functions.register_chunker("dummy")
            class AnotherDummyChunker(Chunker):
                def split_into_chunks(self, doc: Document) -> list[Chunk]:
                    return []

    def test_get_chunker(self):
        @chunking_functions.register_chunker("dummy")
        class DummyChunker(Chunker):
            def split_into_chunks(self, doc: Document) -> list[Chunk]:
                return []

        chunker = chunking_functions.get_chunker("dummy")
        self.assertIsInstance(chunker, DummyChunker)


-- Schemas for repo table.  Repos includes all forks.

CREATE TABLE repo (
    id uuid NOT NULL DEFAULT uuid_generate_v4(),      	-- Unique ID of the repo
    owner varchar(500) NOT NULL, 		      	-- length limits: github 39 gitlab 255 bitbucket 30
    repo_name varchar(500) NOT NULL, 		     	-- length limits: github 100, gitlab 256, bitbucket 62?
    description varchar,    			      	-- Description string if present
    first_seen_at timestamptz NOT NULL DEFAULT NOW(),	-- Time at which we became aware of the repo
    created_at timestamptz,  				-- The time of creation of the repo on vendor
    updated_at timestamptz,				-- Last time repo was edited according to vendor
    license VARCHAR,					-- Primary license
    size_kb BIGINT, 					-- Repo size in kb
    stars BIGINT,
    watchers BIGINT,
    subscribers BIGINT,
    forks BIGINT,
    default_branch VARCHAR(500),
    primary_language VARCHAR(100),
    languages JSONB,
    open_issues BIGINT,
    last_api_call_at TIMESTAMPTZ,   			--  Time we lasted updated the entry with github api
    last_response_code VARCHAR(100) NOT NULL DEFAULT '',
    additional_info JSONB NOT NULL DEFAULT '{}'::JSONB,
    is_root BOOLEAN,    				-- If this is a root repo that didn’t fork anything
    vendor_name VARCHAR NOT NULL DEFAULT 'github',    	-- Which vendor this is from. github, gitlab etc
    vcs_type VARCHAR NOT NULL DEFAULT 'git',            -- Type of version control system.  git, hg, svn etc
    vendor_record_id VARCHAR,  				-- The id assigned by the vendor.  For example, github id
    source_record_id VARCHAR,	 			-- Vendor assigned ID of the ultimate source repo of fork tree,
    source VARCHAR NOT NULL DEFAULT 'the-stack',  	-- Where did we learn of this repo
    canonical_owner varchar GENERATED ALWAYS AS (lower(owner)) STORED,   -- lower case owner name
    canonical_name varchar GENERATED ALWAYS AS (lower(owner) || '/' || lower(repo_name)) STORED
);
CREATE UNIQUE INDEX idx_repo_full_name ON repo USING btree(repo_name, owner);
CREATE UNIQUE INDEX idx_repo_full_name_ci ON repo USING btree(canonical_name);
CREATE UNIQUE INDEX idx_repo_id ON repo USING btree(id);
CREATE INDEX idx_repo_owner_repos ON repo USING btree(owner);
CREATE INDEX idx_repo_canonical_owner ON repo USING btree(canonical_owner);


-- Each correspond to a copy of the repo that we have stored
-- For now we will constrain it to have only one storage per repo
CREATE TABLE repo_storage (
    id uuid NOT NULL DEFAULT uuid_generate_v4(),
    repo_id uuid NOT NULL,    		-- ID of the repo as in repo table
    owner VARCHAR(100) NOT NULL,     	-- Repo own at the time of download
    repo_name VARCHAR(100) NOT NULL,     	-- Repo name at the time of download
    storage_type VARCHAR,     		-- S3, shared drive etc
    license VARCHAR,              		-- Primary license of repo at the time of download
    location VARCHAR NOT NULL,   		-- Storage location, eg s3://augment-github/bare-repos/
    flags JSONB NOT NULL DEFAULT '{}'::jsonb,	-- Flags used to download this repo
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),  -- Time the download is initiated
    status VARCHAR NOT NULL DEFAULT 'started',   	-- The status of the repo download and processing job
    branch VARCHAR,          			-- The current branch, generally the default branch
    head VARCHAR,             		-- Hash of current HEAD
    last_commit_at TIMESTAMPTZ,        	-- Time of the last commit
    finished_at TIMESTAMPTZ,             	-- Time the download is finished
    error_info JSONB NOT NULL DEFAULT '{}'::jsonb,
    additional_info JSONB NOT NULL DEFAULT '{}'::jsonb,
    commits BIGINT,     			-- Number of commits available
    history_truncated BOOLEAN,		-- Whether the history was truncated with --depth
    repo_root VARCHAR   			-- The source of the repo. Ie root repo it is forked from. vendor/vendor_id, such as github/2114,
);
-- Eventually we will relax this but it is not 100% clear yet how we do this once we got to multiple branches
CREATE INDEX idx_repo_storage_repo_id ON repo_storage USING btree(repo_id);
CREATE UNIQUE INDEX idx_repo_storage_id ON repo_storage USING btree(id);


-- Schema for github contents i.e. one unique version of file.  Each content can belong to different repos / forks
CREATE TABLE file_content (
    sha char(40) NOT NULL,      -- The SHA-1 of the content as from github API or the stack.  This is unique
    size BIGINT,                -- File size in bytes
    first_repo VARCHAR(1000),   -- The first repo where this file is seen at
    first_pathname varchar,               -- Filename
    first_seen_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),     -- First time content is known,
    pct_modified INT NOT NULL DEFAULT 200,   -- Percentage of the file that has been modified by commits.  >100 means it's on HEAD
    is_binary BOOLEAN NOT NULL DEFAULT FALSE      -- if true the content is encoded with base64.  otherwise it's normal text.
);
CREATE UNIQUE INDEX idx_content_sha ON content USING btree(sha);


-- Associates repos with the files inside
-- We do not store explicit path here because same content can appear multiple different places in same repo.
CREATE TABLE repo_file (
    repo_id UUID NOT NULL,
    content_sha char(40) NOT NULL,
    repo_storage_id UUID NOT NULL,            -- The storage from which we extracted the relationship
    pathname VARCHAR NOT NULL,                -- Full pathname of the file within that particular repo
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_repo_file_repo_id_sha ON repo_file USING btree(repo_id, content_sha);


-- All known commits.  We call it git_commit because commit is a reserved keyword
CREATE TABLE git_commit (
    sha CHAR(40) NOT NULL,
    parent_sha CHAR(40),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_commit_sha ON git_commit USING btree(sha);


-- Which commits do each repo contain
CREATE TABLE repo_commit (
    repo_id uuid NOT NULL,
    commit_sha CHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_repo_commit_repo_id_sha ON repo_commit USING btree(repo_id, commit_sha);


-- Stores API keys that we use.
CREATE TABLE bot_api_key(
    id uuid not null default uuid_generate_v4(),
    vendor varchar not null,                            -- for example, github
    api_key varchar not null,                           -- proper api key on the vendor
    last_called_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),  -- Last time this api key is used
    cooldown_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),     -- Earliest time next it can be used again
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),      -- Time this api key was registered
    expires_at TIMESTAMPTZ NOT NULL DEFAULT ,           -- Expiration date if any
    created_by varchar,                                 -- email of the creator of this API key,
    additional_info JSONB NOT NULL DEFAULT '{}'::jsonb,
    disabled boolean                                    -- When set to True bots will be aborted when trying to use this key.
);


-- Stores user account data on VCS vendors. ie github usernames for now
CREATE TABLE vendor_user (
    id uuid NOT NULL DEFAULT uuid_generate_v4(),      -- Unique ID of the repo
    username varchar(500) NOT NULL,                   -- length limits: github 39 gitlab 255 bitbucket 30
    vendor varchar(500) NOT NULL DEFAULT 'github',    -- name of the vendor.  for now just `github`
    kind varchar,                -- user or organization
    last_api_call TIMESTAMPTZ,   -- last time an api call was made to this user
    last_response_code VARCHAR,  -- response from the last api call
    new_repos INTEGER NOT NULL DEFAULT 0,  -- how many repos are new on this user
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_seen_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),  -- last time we see this user in any events
    vendor_id VARCHAR           -- ID assigned by the vendor, ie github index
);
CREATE UNIQUE INDEX idx_user_name ON vendor_user USING btree(username, vendor);
CREATE UNIQUE INDEX idx_user_id ON vendor_user USING btree(id);


-- Store manually identified license info
CREATE TABLE license_override (
    id uuid NOT NULL DEFAULT uuid_generate_v4(),
    repo_id uuid NOT NULL,       -- ID of the repo in repo table
    license VARCHAR NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
-- uniqueness on repo_id to ensure that we do not accidentally input license for the same repo twice
CREATE UNIQUE INDEX idx_license_override_repo ON license_override USING btree(license_override);


-- Information and success status of jobs
CREATE TABLE batch_job (
    id uuid NOT NULL DEFAULT uuid_generate_v4(),
    name varchar NOT NULL,         -- Name of the job
    partition_name varchar,        -- partition of the job if available
    start_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),    -- time the job is started
    end_time TIMESTAMPTZ,                             -- time of termination.  NULL if not terminated
    status VARCHAR NOT NULL,            -- job status string
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),    -- time this record is created
    additional_info JSONB NOT NULL DEFAULT '{}'::jsonb,
		error_info JSONB not null default '{}'::jsonb
);
-- We do not use a clustered key here because we have no need to cluster
-- nearby keys physically.  ie UUIDs are randomly accessed as opposed to
-- incremental keys accessed progressively
CREATE UNIQUE INDEX idx_batch_job_id ON batch_job USING btree(id);
CREATE INDEX idx_batch_name_partition ON batch_job USING btree(name, partition_name);


CREATE TABLE license_info (
    key character varying,
    permissive boolean,
    description text
);

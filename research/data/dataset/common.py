"""Defines AugmentDataset here."""

from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Any, Union

import numpy as np
import torch
import torch.utils.data as torch_data

IntLike = Union[int, np.integer]


class AugmentDataset(ABC, torch_data.Dataset):
    """A minimal interface for all our Dataset types.

    A subclass should override the abstract methods with more precise signatures.
    """

    @abstractmethod
    def __getitem__(self, idx: IntLike) -> Any:
        """Note that currently, not all our datasets support slice as index."""

    @abstractmethod
    def __len__(self) -> int: ...

    def get(self, idx: IntLike) -> Any:
        """By default, this method returns self[index]."""
        return self[idx]


# Copyright (c) 2020, NVIDIA CORPORATION.  All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


def print_rank_0(*message):
    """If distributed is initialized print only on rank 0."""
    if torch.distributed.is_initialized():
        if torch.distributed.get_rank() == 0:
            print(*message, flush=True)
    else:
        print(*message, flush=True)

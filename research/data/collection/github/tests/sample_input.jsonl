["david<PERSON><PERSON><PERSON>", "davesbingrewardsbot", 28635890, "gh-archive"]
["jmoon018", "rshell-unit-tester", 26392647, "gh-archive"]
["ch<PERSON><PERSON><PERSON><PERSON>", "Vadek", 28677542, "gh-archive"]
["square", "okhttp", 5152285, "gh-archive"]
["git4ruby", "movie_review1", 28520835, "gh-archive"]
["tlgkccampbell", "ultraviolet", 25334511, "gh-archive"]
["Vilyan01", "ILP", 21995378, "gh-archive"]
["xndcn", "d-statistics", 15526570, "gh-archive"]
["team3cord", "mc-dotfiles", 20268125, "gh-archive"]
["greyia", "port2container", 27156833, "gh-archive"]
["piscisaureus", "slurp-logs", 3633660, "gh-archive"]
["sean-smith", "website_status", 27858779, "gh-archive"]
["caleb-eades", "MinecraftServers", 20469468, "gh-archive"]
["sundaymtn", "waterline", 24147122, "gh-archive"]
["joaopedronardari", "joaopedronardari.github.com", 28677492, "gh-archive"]
["ingydotnet", "zilla-dist-pm", 20117626, "gh-archive"]
["uwarc", "website", 27288715, "gh-archive"]
["vinniejames", "vinniejames.github.io", 28676854, "gh-archive"]
["selfhub", "selfhub", 28109544, "gh-archive"]
["SCEDC", "SCEDC-catalogs", 5089162, "gh-archive"]
["adamatan", "flip_classroom_hackathon", 15119415, "gh-archive"]
["katsyoshi", "mikutter", 2941890, "gh-archive"]
["stones", "ansible-role-wordpress-nginx", 28677545, "gh-archive"]
["bartnijssen", "VIC", 11134801, "gh-archive"]
["ingydotnet", "swim-pm", 18904157, "gh-archive"]
["ju2ta2", "VB_lessons", 28625970, "gh-archive"]
["missionsix", "Transmissionbt", 19073780, "gh-archive"]
["greatfire", "z", 18126008, "gh-archive"]
["ProjetPP", "integration", 26583093, "gh-archive"]
["SeleniumHQ", "irc-logs", 9457897, "gh-archive"]
["ingydotnet", "swim-plugin-badge-pm", 21285335, "gh-archive"]
["sunyc", "oow1", 15032738, "gh-archive"]
["haskell", "containers", 1924888, "gh-archive"]
["ingydotnet", "lexicals-pm", 1755571, "gh-archive"]
["su-github-machine-user", "github-nagios-check", 10314483, "gh-archive"]
["bunchopunch", "NerdHat", 27407975, "gh-archive"]
["FrankM1", "radium-one-click-demo-install", 20390101, "gh-archive"]
["eggfly", "raspberrypi", 17136520, "gh-archive"]
["xenim", "livestatus-publicpage", 7588969, "gh-archive"]
["greatfire", "wiki", 15100395, "gh-archive"]
["wp-plugins", "creative-commons-configurator-1", 14201058, "gh-archive"]
["fsharp", "FAKE", 507569, "gh-archive"]
["machchk", "report", 21783823, "gh-archive"]
["HouseMonitor", "Logs2014-2015", 24030380, "gh-archive"]
["docker-library", "docs", 22484898, "gh-archive"]
["qdm", "qdm.github.io", 25173910, "gh-archive"]
["treckstar", "yolo-octo-hipster", 17101123, "gh-archive"]
["AnirudhBhat", "cliWiki.py", 5558210, "gh-archive"]
["d3stats", "d3.fuzz.me.uk", 9317463, "gh-archive"]
["kanaka", "websockify", 1248263, "gh-archive"]
["numenta", "nupic.core", 16366081, "gh-archive"]
["AmmoniteNetworks", "CalendR", 28677532, "gh-archive"]
["flynn", "flynn", 11290232, "gh-archive"]
["konjac", "calendars", 24664906, "gh-archive"]
["burgerbecky", "glslvisualstudio", 28677547, "gh-archive"]
["gridaphobe", "target", 16334926, "gh-archive"]
["dimster2013", "backup_mywork", 28677548, "gh-archive"]
["amadeus", "macvim", 2900569, "gh-archive"]
["pivotalsoftware", "PivotalR", 9801943, "gh-archive"]
["TelescopeJS", "Telescope", 5710848, "gh-archive"]
["LinuxStandardBase", "lsb", 18297319, "gh-archive"]
["InternetDevels", "drupalcores", 20291263, "gh-archive"]
["philhagen", "for572logstash", 28635402, "gh-archive"]
["llvm-mirror", "llvm", 3287301, "gh-archive"]
["slayerfat", "sistemaJAG", 26303656, "gh-archive"]
["dannomac", "freebsd", 5440447, "gh-archive"]
["apache", "spark", 17165658, "gh-archive"]
["inf0rmer", "blanket", 28229924, "gh-archive"]
["nopjmp", "node-llsd", 28656308, "gh-archive"]
["khy", "granmal.com", 16972230, "gh-archive"]
["ankidroid", "Anki-Android", 7716883, "gh-archive"]
["tarekkazak", "budget", 24087301, "gh-archive"]
["mwean", "right-books", 27141908, "gh-archive"]
["floor-17", "webchat", 27664199, "gh-archive"]
["MobileAppTracking", "tune-reporting-php", 25269682, "gh-archive"]
["JuliaLang", "julia", 1644196, "gh-archive"]
["wphuman", "heroku-buildpack-php", 28575703, "gh-archive"]
["davidhend", "QR-Code-Generator", 11389511, "gh-archive"]
["SenhW", "programming-by-doing", 28489784, "gh-archive"]
["nimirea", "XECryption-Decrypt", 28677493, "gh-archive"]
["SLUGIS", "incidents", 10672637, "gh-archive"]
["badrsony", "icloudin-support-", 26731988, "gh-archive"]
["parrt", "cs652", 28676020, "gh-archive"]
["iamthejonsmith", "MyLibrary", 27897523, "gh-archive"]
["Tanginatushar", "HTML_form", 28677465, "gh-archive"]
["LiamSchauerman", "fantasyApi", 27842993, "gh-archive"]
["lon3wolf", "SyncRadioRocki", 28675131, "gh-archive"]
["gkovacs", "feedlearn2", 28426815, "gh-archive"]
["mozilla", "releng-jacuzzis", 17179173, "gh-archive"]
["derUli", "ulicms-pkg", 11879934, "gh-archive"]
["350dotorg", "megamap-data", 11516694, "gh-archive"]
["amath574w2015", "amath574w2015.github.com", 28677494, "gh-archive"]
["msabramo", "tsuru", 23370782, "gh-archive"]
["jscs-dev", "gulp-jscs", 15463351, "gh-archive"]
["micahyoung", "citibike-data", 25281621, "gh-archive"]
["Addepar", "ember-table-addon", 26200611, "gh-archive"]
["paddelkraft", "TfsKanbanBuddy", 23848067, "gh-archive"]
["alcap-org", "alcap-org.github.io", 20542797, "gh-archive"]
["bstockus", "KwikFormation", 28677380, "gh-archive"]
["micahyoung", "cbstats-data", 16091467, "gh-archive"]

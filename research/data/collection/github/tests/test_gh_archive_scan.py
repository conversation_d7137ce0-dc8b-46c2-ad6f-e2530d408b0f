"""Test if the gh archive insert logic is correct."""
import json
from pathlib import Path

import research.data.postgres_metastore as pg
from research.data.collection.github.controllers import process_gh_archive

# Use a special source name to demarcate generated test data
# in the long run we should not do this in prod db, but
# as long as we are absolutely sure this source is not used
# in prod and we only touch rows with this source, we are ok
# for the moment.
TEST_SOURCE = "testing-gh-archive"


def count_missing(batch):
    """Count the number of records in a batch that are not in repo."""
    for (n,) in pg.batch_execute_generator(
        """
            SELECT
               count(*)
            FROM (VALUES %s) AS v(owner, name, gh_id, source)
            LEFT JOIN repo
                ON repo.canonical_name=lower(v.owner)||'/'||lower(v.name)
            WHERE repo.owner IS NULL
        """,
        batch,
    ):
        print("Number of repos missing:", n)
        return n


def count_inserted():
    """Count the number of records inserted in tests."""
    [(n,)] = pg.execute("SELECT COUNT(*) FROM repo where source=%s", (TEST_SOURCE,))
    return n


def cleanup():
    """Tears down all test records."""
    if TEST_SOURCE in ("bigquery", "the-stack", "deps.dev", "manual", "gh-archive"):
        raise RuntimeError("Do not use production source for testing!!!")
    pg.execute("DELETE FROM repo WHERE source=%s", (TEST_SOURCE,))


def test_single_batch():
    data = []
    with Path("sample_input.jsonl").open() as bf:
        for line in bf:
            data.append(json.loads(line))

    cleanup()
    # First take a note how many repos are missing
    n_missing = count_missing(data)
    repo_list = {entry[2]: f"{entry[0]}/{entry[1]}" for entry in data}
    n_reported = process_gh_archive.insert_repos(repo_list, source=TEST_SOURCE)
    # Check if all rows are now in repo table
    assert count_missing(data) == 0, "There are still missing rows after insert!"
    # check if reported insertion count matches up with missing count
    assert (
        n_reported == n_missing
    ), "Reported insertion count does not match missing repo count"
    # check if actual new repos match with missing count
    assert (
        count_inserted() == n_missing
    ), "New records in table does not match missing repo count"
    cleanup()


def test_single_segment(segment="2015-01-02-7"):
    """Process one single segment and check correctness."""
    cleanup()
    n_reported = process_gh_archive.process_segment(segment, source=TEST_SOURCE)
    n_inserted = count_inserted()
    # Check the total insertion count (by summing up the reported insertion count from
    # each operation) matches the actual number of new repos in the db
    assert (
        n_reported == n_inserted
    ), "Number of new records in table did not match reported total count."
    cleanup()

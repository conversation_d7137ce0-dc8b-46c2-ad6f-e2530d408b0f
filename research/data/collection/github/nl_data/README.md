Natural language data from github website
==================

GitHub contains large amount of NL data in addition to git.

Here, we do not count NL available in git such as commit messages.  Rather, this directory
contains scripts to collect NL from the website and format them.


For Issues and PRs, we collect data from a hidden but publicly queryable BigQuery project `githubarchive`.
The dataset is in raw  JSON format and combines all API types, so it needs to be converted first to be used.
We ingest and parse the raw data to create several tables for individual event types in `github_events` dataset.
Then these are joined to create the actual dataset we need where comments are aggregated per issue and
reviews are aggregated on PRs.


Discussions need to be collected from GraphQL API.


Github.io pages requires us to identify the appropriate branch and pull it.

Wiki are very low usage, and we do not plan to use them in the short term.

-- First extract out the subset of issue and comments events.
-- This to avoid repeated scans of the biggest source table and reduce cost

CREATE OR REPLACE TABLE github_events.issue_and_commit_comments AS
SELECT
  CASE type
  WHEN 'IssueCommentEvent' THEN
    STRUCT(
      'issue_comment' AS type,
      JSON_EXTRACT(payload, '$.comment') AS content,
      JSON_EXTRACT(payload, '$.issue.id') AS associated_id,
      created_at,
      JSON_EXTRACT(payload, '$.action') AS action
    )
  WHEN 'IssuesEvent' THEN
    STRUCT(
      'issue' AS type,
      JSON_EXTRACT(payload, '$.issue') AS content,
      '' AS associated_id,
      created_at,
      JSON_EXTRACT(payload, '$.action') AS action
    )
  WHEN 'CommitCommentEvent' THEN
    STRUCT(
      'commit_comment' AS type,
      JSON_EXTRACT(payload, '$.comment') AS content,
      '' AS associated_id,
      created_at,
      JSON_EXTRACT(payload, '$.action') AS action
    )
  END AS entry,
  repo.name AS repo_name,
  actor.login AS actor
FROM `githubarchive.year.*`
WHERE TYPE in ('IssueCommentEvent', 'IssuesEvent', 'CommitCommentEvent')
AND (
  JSON_EXTRACT(payload, '$.comment') IS NOT NULL
  OR
  JSON_EXTRACT(payload, '$.issue') IS NOT NULL
)

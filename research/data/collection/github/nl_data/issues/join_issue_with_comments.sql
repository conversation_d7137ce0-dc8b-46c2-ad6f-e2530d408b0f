-- Join issues with their comments

CREATE OR REPLACE TABLE
  github_generated.issues_with_comments AS
SELECT
  i.id,
  i.repo_name,
  i.number,
  i.author_association,
  i.title,
  i.body,
  i.created_at,
  i.updated_at,
  i.closed_at,
  i.state,
  i.state_reason,
  i.comments AS comment_count,
  i.locked,
  STRUCT( i.user.login,
    i.user.type,
    i.user.site_admin ) AS user,
  ARRAY_AGG(
  IF
    (c.created_at IS NULL, NULL, STRUCT( c.body,
        c.created_at,
        c.author_association,
        STRUCT( c.user.login,
          c.user.type,
          c.user.site_admin ) AS user )) IGNORE NULLS
  ORDER BY
    c.created_at DESC
  LIMIT
    1000 ) AS comments
FROM
  `github_events.issues` AS i
LEFT JOIN
  `github_events.issue_comments` AS c
ON
  c.issue_id=i.id
WHERE
  i.user.type != 'Bot'
  AND (c.created_at IS NULL
    OR c.user.type != 'Bot')
GROUP BY
  1,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  i.user.login,
  i.user.type,
  i.user.site_admin;

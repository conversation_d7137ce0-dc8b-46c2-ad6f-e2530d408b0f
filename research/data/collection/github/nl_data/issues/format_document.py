"""Convert structured issue/comments dataset to formatted text documents."""
import pandas as pd

from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.utils import k8s_session


def create_document(issue_series: pd.Series):
    """Create a document from an issue in dict form."""
    issue = issue_series.to_dict()
    issue["comments"] = [
        comment.asDict() if isinstance(comment, pd.Series) else comment
        for comment in issue["comments"]
    ]

    # determine filename from issue number and title, if available.
    path = f'issues/{issue["number"]}_{issue["title"]}.md'
    # Change all non-alphanum to underlines.
    path = "".join(c if c.isalnum() else "_" for c in path)

    # Format things the same way as the GitHub website.
    # First format the comments.
    comments = [
        f"""
### {comment['user']['login']} commented on {comment['created_at']}

{comment['body']}
"""
        for comment in issue["comments"]
    ]
    comment_str = "\n\n".join(comments)

    # Determine the state.
    state_str = f'** {issue["state"].capitalize()} **'
    if issue["locked"]:
        state_str += " (locked)"

    # Format the main document.
    markdown_body = f"""
# {issue['title']} (#{issue['number']})

{state_str} | {issue['user']['login']} opened this issue on {issue['created_at']}

## Description

{issue['body']}

## Comments

{comment_str}
"""
    # Drop the fields that are already in the document.  The rest are metadata.
    to_drop = [
        "id",
        "comments",
        "body",
        "user",
        "state",
        "locked",
        "created_at",
        "repo_name",
    ]
    meta = {key: value for key, value in issue.items() if key not in to_drop}
    return pd.Series(
        {
            "id": "gh-issue-" + issue["id"],
            "content": markdown_body,
            "repo_name": issue["repo_name"],
            "path": path,
            "meta": meta,
        }
    )


def format_issues(issues: pd.DataFrame):
    """Format a DataFrame of issues into a dataframe of documents."""
    return issues.apply(create_document, axis=1)


if __name__ == "__main__":
    spark = k8s_session(max_workers=20)

    map_parquet.apply(
        spark,
        format_issues,
        input_path="/mnt/efs/spark-data/shared/nl-datasets/gh_issue_comments/structured",
        output_path="/mnt/efs/spark-data/shared/nl-datasets/gh_issue_comments/formatted",
        batch_size=100,
        drop_original_columns=True,
        ignore_error=True,
    )

    spark.stop()

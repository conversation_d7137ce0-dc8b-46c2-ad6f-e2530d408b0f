-- Impose schema and also deduplicate the issues

CREATE OR REPLACE TABLE
  github_events.issues AS
WITH
  numbered AS (
  SELECT
    JSON_EXTRACT_SCALAR(entry.content, '$.id') AS id,
    repo_name,
    actor,
    JSON_EXTRACT_SCALAR(entry.content, '$.number') AS number,
    JSON_EXTRACT_SCALAR(entry.content, '$.title') AS title,
    JSON_EXTRACT_SCALAR(entry.content, '$.body') AS body,
    STRUCT( JSON_EXTRACT_SCALAR(entry.content, '$.user.login') AS login,
      JSON_EXTRACT_SCALAR(entry.content, '$.user.id') AS id,
      JSON_EXTRACT_SCALAR(entry.content, '$.user.type') AS type,
      JSON_EXTRACT_SCALAR(entry.content, '$.user.site_admin') AS site_admin ) AS user,
    JSON_EXTRACT_SCALAR(entry.content, '$.author_association') AS author_association,
    JSON_EXTRACT_SCALAR(entry.content, '$.created_at') AS created_at,
    JSON_EXTRACT_SCALAR(entry.content, '$.updated_at') AS updated_at,
    JSON_EXTRACT_SCALAR(entry.content, '$.closed_at') AS closed_at,
    JSON_EXTRACT_SCALAR(entry.content, '$.state') AS state,
    JSON_EXTRACT_SCALAR(entry.content, '$.state_reason') AS state_reason,
    JSON_EXTRACT_SCALAR(entry.content, '$.locked') AS locked,
    JSON_EXTRACT_SCALAR(entry.content, '$.timeline_url') AS timeline_url,
    CAST(JSON_EXTRACT(entry.content, '$.comments') AS INT64) AS comments,
    JSON_EXTRACT(entry.content, '$.assignees') AS assignees,
    JSON_EXTRACT(entry.content, '$.labels') AS labels,
    JSON_EXTRACT(entry.content, '$.reactions') AS reactions,
    ROW_NUMBER() OVER (PARTITION BY JSON_EXTRACT_SCALAR(entry.content, '$.id')
    ORDER BY
      JSON_EXTRACT_SCALAR(entry.content, '$.updated_at') DESC) AS rn
  FROM
    github_events.issue_and_commit_comments
  WHERE
    entry.type = 'issue' )
SELECT
  * EXCEPT(rn)
FROM
  numbered
WHERE
  rn = 1

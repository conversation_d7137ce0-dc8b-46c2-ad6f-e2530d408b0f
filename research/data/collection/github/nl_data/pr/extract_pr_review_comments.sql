CREATE OR REPLACE TABLE
  github_events.pull_request_review_comments AS
SELECT
  id AS event_id,
  created_at AS event_time,
  repo.id AS repo_id,
  repo.name AS repo_name,
  repo.url AS repo_url,
  actor.id AS actor_id,
  actor.login AS actor_login,
  actor.url AS actor_url,
  org.id AS org_id,
  org.login AS org_login,
  org.url AS org_url,
  JSON_EXTRACT_SCALAR(payload, '$.comment.pull_request_review_id') AS pr_review_id,
  JSON_EXTRACT_SCALAR(payload, '$.comment.id') AS comment_id,
  JSON_EXTRACT_SCALAR(payload, '$.comment.diff_hunk') AS diff_hunk,
  JSON_EXTRACT_SCALAR(payload, '$.comment.path') AS path,
  JSON_EXTRACT_SCALAR(payload, '$.comment.commit_id') AS commit_id,
  JSON_EXTRACT_SCALAR(payload, '$.comment.original_commit_id') AS original_commit_id,
  CAST(JSON_EXTRACT_SCALAR(payload, '$.comment.position')AS INT64) AS position,
  CAST(JSON_EXTRACT_SCALAR(payload, '$.comment.original_position')AS INT64) AS original_position,
  CAST(JSON_EXTRACT_SCALAR(payload, '$.comment.start_line') AS INT64) AS start_line,
  CAST(JSON_EXTRACT_SCALAR(payload, '$.comment.original_start_line')AS INT64) AS original_start_line,
  CAST(JSON_EXTRACT_SCALAR(payload, '$.comment.line') AS INT64) AS line,
  CAST(JSON_EXTRACT_SCALAR(payload, '$.comment.original_line')AS INT64) AS original_line,
  JSON_EXTRACT_SCALAR(payload, '$.comment.subject_type') AS subject_type,
  JSON_EXTRACT_SCALAR(payload, '$.comment.body') AS body,
  JSON_EXTRACT(payload, '$.comment.reactions') AS reactions,
  STRUCT( JSON_EXTRACT_SCALAR(payload, '$.comment.user.login') AS login,
    JSON_EXTRACT_SCALAR(payload, '$.comment.user.id') AS id,
    JSON_EXTRACT_SCALAR(payload, '$.comment.user.type') AS type,
    JSON_EXTRACT_SCALAR(payload, '$.comment.user.site_admin') AS site_admin ) AS user,
  JSON_EXTRACT_SCALAR(payload, '$.comment.created_at') AS created_at,
  JSON_EXTRACT_SCALAR(payload, '$.comment.updated_at') AS updated_at,
  JSON_EXTRACT_SCALAR(payload, '$.comment.in_reply_to_id') AS in_reply_to_id,
  JSON_EXTRACT_SCALAR(payload, '$.pull_request.id') AS pr_id,
FROM
  `githubarchive.month.*`
WHERE
  TYPE='PullRequestReviewCommentEvent'

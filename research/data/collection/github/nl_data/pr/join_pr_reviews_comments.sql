CREATE OR R<PERSON>LACE TABLE
  github_generated.pr_with_reviews_comments AS
WITH
  intermediate AS (
  SELECT
    pr.pr_id AS pr_id,
    ANY_VALUE(pr.number) AS number,
    ANY_VALUE(pr.state) AS state,
    ANY_VALUE(pr.title) AS title,
    ANY_VALUE(pr.body) AS body,
    ANY_VALUE(pr.user) AS user,
    ANY_VALUE(pr.created_at) AS created_at,
    ANY_VALUE(pr.updated_at) AS updated_at,
    ANY_VALUE(pr.closed_at) AS closed_at,
    ANY_VALUE(pr.merged_at) AS merged_at,
    ANY_VALUE(pr.merged) AS merged,
    ANY_VALUE(pr.merge_commit_sha) AS merge_commit_sha,
    ANY_VALUE(pr.assignees) AS assignees,
    ANY_VALUE(pr.requested_reviewers) AS requested_reviewers,
    ANY_VALUE(pr.requested_teams) AS requested_teams,
    ANY_VALUE(pr.labels) AS labels,
    ANY_VALUE(pr.author_association) AS author_association,
    ANY_VALUE(pr.comments) AS comments,
    ANY_VALUE(pr.review_comments) AS review_comments,
    ANY_VALUE(pr.commits) AS commits,
    ANY_VALUE(pr.additions) AS additions,
    ANY_VALUE(pr.deletions) AS deletions,
    ANY_VALUE(pr.changed_files) AS changed_files,
    ARRAY_AGG(
    IF
      (review.review_id IS NULL, NULL, STRUCT(review.review_id,
          review.user,
          review.body,
          review.commit_id,
          review.submitted_at,
          review.state,
          review.author_association)) IGNORE NULLS
    ORDER BY
      review.submitted_at) AS reviews_array,
    ANY_VALUE(pr.base) AS base,
    ANY_VALUE(pr.head) AS head
  FROM (
    SELECT
      *
    FROM
      `github_events.pr_latest`
    WHERE
      base.repo_full_name IS NOT NULL
      AND user.type != 'Bot') AS pr
  LEFT JOIN (
    SELECT
      *
    FROM
      `github_events.pull_request_reviews`
    WHERE
      LENGTH(body) < 1e4) AS review
  ON
    pr.pr_id = review.pr_id
  GROUP BY
    pr.pr_id )
SELECT
  ANY_VALUE(CONCAT("https://github.com/", pr.base.repo_full_name, "/pull/", number)) AS pr_url,
  pr.pr_id AS pr_id,
  ANY_VALUE(pr.number) AS number,
  ANY_VALUE(pr.state) AS state,
  ANY_VALUE(pr.title) AS title,
  ANY_VALUE(pr.body) AS body,
  ANY_VALUE(pr.user) AS user,
  ANY_VALUE(pr.created_at) AS created_at,
  ANY_VALUE(pr.updated_at) AS updated_at,
  ANY_VALUE(pr.closed_at) AS closed_at,
  ANY_VALUE(pr.merged_at) AS merged_at,
  ANY_VALUE(pr.merged) AS merged,
  ANY_VALUE(pr.merge_commit_sha) AS merge_commit_sha,
  ANY_VALUE(pr.assignees) AS assignees,
  ANY_VALUE(pr.requested_reviewers) AS requested_reviewers,
  ANY_VALUE(pr.requested_teams) AS requested_teams,
  ANY_VALUE(pr.labels) AS labels,
  ANY_VALUE(pr.author_association) AS author_association,
  ANY_VALUE(pr.comments) AS comments,
  ANY_VALUE(pr.review_comments) AS review_comments,
  ANY_VALUE(pr.commits) AS commits,
  ANY_VALUE(pr.additions) AS additions,
  ANY_VALUE(pr.deletions) AS deletions,
  ANY_VALUE(pr.changed_files) AS changed_files,
  ANY_VALUE(pr.reviews_array) AS reviews_array,
  ARRAY_AGG(
  IF
    (comment.comment_id IS NULL, NULL, STRUCT(comment.pr_review_id,
        comment.comment_id,
        comment.in_reply_to_id,
      IF
        (comment.in_reply_to_id IS NULL, NULL, comment.diff_hunk) AS diff_hunk,
        comment.path,
        comment.commit_id,
        comment.original_commit_id,
        comment.position,
        comment.original_position,
        comment.start_line,
        comment.original_start_line,
        comment.line,
        comment.original_line,
        comment.subject_type,
        comment.body,
        comment.reactions,
        comment.user,
        comment.created_at,
        comment.updated_at )) IGNORE NULLS
  ORDER BY
    comment.created_at ) AS comments_array,
  ANY_VALUE(pr.base) AS base,
  ANY_VALUE(pr.head) AS head
FROM
  intermediate AS pr
LEFT JOIN (
  SELECT
    *
  FROM
    `github_events.pull_request_review_comments`
  WHERE
    LENGTH(body) < 1e4) AS comment
ON
  pr.pr_id = comment.pr_id
GROUP BY
  pr.pr_id

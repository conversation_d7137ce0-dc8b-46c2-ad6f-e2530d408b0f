"""Convert structured PR and reviews dataset to formatted text documents.

Each document is a review comment thread.
"""

import pandas as pd

from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.utils import k8s_session


def format_thread(comments: dict[str, dict], thread: list[str]):
    """Format a thread of comments into a string."""
    # select my comments, and sort by created_at
    thread_comments = [comments[comment_id] for comment_id in thread]
    # format each comment
    current_hunk = None
    thread_str = ""
    for comment in thread_comments:
        diff_hunk = comment["diff_hunk"]
        if diff_hunk and current_hunk != diff_hunk:
            thread_str += f'\nRegarding changes to file {comment["path"]}:\n```diff\n{diff_hunk}\n```\n'
            current_hunk = diff_hunk
        thread_str += f"\n{comment['user']['login']} commented on {comment['created_at']}\n{comment['body']}\n"
    return thread_str


def create_document(pr_item: pd.Series):
    """Create a set of documents from a PR."""
    repo_name = pr_item.base["repo_full_name"]
    filename = f"pull_requests/{pr_item.number}_{pr_item.title}.md"
    filename = "".join(
        c if c.isalnum() or c in ("/", "-", "_", ".") else "_" for c in filename
    )
    # Format things the same way as the GitHub website.
    header = f"""{repo_name}

# {pr_item.title} (#{pr_item.number})

{pr_item.user['login']} wants to merge {pr_item.stats['commits']} commits into {pr_item.base['ref']} from {pr_item['head']['ref']}

## Pull Request Message

{pr_item.body}

## Comments
"""
    # First, convert review comments into dicts and build lookup table
    sorted_comments = sorted(
        pr_item["review_comments"], key=lambda comment: comment["created_at"]
    )
    comments_lookup = {comment["id"]: comment for comment in sorted_comments}

    # Cluster comments into threads
    threads = []
    for comment in sorted_comments:
        for thread in threads:
            if comment["in_reply_to_id"] in thread:
                thread.append(comment["id"])
                break
        else:
            threads.append([comment["id"]])
    if not threads:
        yield pd.Series(
            {
                "id": f"pr-{pr_item.id}-x",
                "repo_name": repo_name.lower(),
                "path": filename,
                "content": header + "No comments.",
                "lang": "github-pr-review",
            }
        )
    else:
        for ithread, thread in enumerate(threads):
            yield pd.Series(
                {
                    "id": f"pr-{pr_item.id}-t{ithread}",
                    "repo_name": repo_name.lower(),
                    "path": filename,
                    "content": header + format_thread(comments_lookup, thread),
                    "lang": "github-pr-review",
                }
            )


def format_pr_reviews(pr: pd.DataFrame):
    """Format a DataFrame of PR reviews into a dataframe of documents."""
    for _, row in pr.iterrows():
        yield from create_document(row)


def main():
    spark = k8s_session(max_workers=20)
    map_parquet.apply_pandas(
        spark,
        format_pr_reviews,
        input_path="/mnt/efs/spark-data/shared/nl-datasets/gh_pr_reviews/structured/",
        output_path="/mnt/efs/spark-data/shared/nl-datasets/gh_pr_reviews/formatted/",
        batch_size=100,
        output_column="content",
        drop_original_columns=True,
        ignore_error=True,
        task_info_location="/mnt/efs/spark-data/temp_weekly/map_parquet_task_info/github-pr-reviews/",
    )
    spark.stop()


if __name__ == "__main__":
    main()

CREATE OR REPLACE TABLE
  github_events.pull_request_reviews AS
SELECT
  id AS event_id,
  created_at AS event_time,
  repo.id AS repo_id,
  repo.name AS repo_name,
  repo.url AS repo_url,
  actor.id AS actor_id,
  actor.login AS actor_login,
  actor.url AS actor_url,
  org.id AS org_id,
  org.login AS org_login,
  org.url AS org_url,
  JSON_EXTRACT_SCALAR(payload, '$.action') AS action,
  JSON_EXTRACT_SCALAR(payload, '$.review.id') AS review_id,
  STRUCT( JSON_EXTRACT_SCALAR(payload, '$.review.user.login') AS login,
    JSON_EXTRACT_SCALAR(payload, '$.review.user.id') AS id,
    JSON_EXTRACT_SCALAR(payload, '$.review.user.type') AS type,
    JSON_EXTRACT_SCALAR(payload, '$.review.user.site_admin') AS site_admin ) AS user,
  JSON_EXTRACT_SCALAR(payload, '$.review.body') AS body,
  JSON_EXTRACT_SCALAR(payload, '$.review.commit_id') AS commit_id,
  JSON_EXTRACT_SCALAR(payload, '$.review.submitted_at') AS submitted_at,
  JSON_EXTRACT_SCALAR(payload, '$.review.state') AS state,
  JSON_EXTRACT_SCALAR(payload, '$.pull_request.id') AS pr_id,
  JSON_EXTRACT_SCALAR(payload, '$.author_association') AS author_association
FROM
  `githubarchive.month.*`
WHERE
  TYPE='PullRequestReviewEvent'

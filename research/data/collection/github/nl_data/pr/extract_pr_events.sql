CREATE OR REPLACE TABLE
  github_events.pull_request_events AS
SELECT
  id AS event_id,
  created_at AS event_time,
  repo.id AS repo_id,
  repo.name AS repo_name,
  repo.url AS repo_url,
  actor.id AS actor_id,
  actor.login AS actor_login,
  actor.url AS actor_url,
  org.id AS org_id,
  org.login AS org_login,
  org.url AS org_url,
  JSON_EXTRACT_SCALAR(payload, '$.action') AS action,
  CAST(JSON_EXTRACT_SCALAR(payload, '$.number') AS INT64) AS number,
  JSON_EXTRACT_SCALAR(payload, '$.pull_request.id') AS pr_id,
  JSON_EXTRACT_SCALAR(payload, '$.pull_request.state') AS state,
  JSON_EXTRACT_SCALAR(payload, '$.pull_request.title') AS title,
  STRUCT( JSON_EXTRACT_SCALAR(payload, '$.pull_request.user.login') AS login,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.user.id') AS id,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.user.type') AS type,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.user.site_admin') AS site_admin ) AS user,
  JSON_EXTRACT_SCALAR(payload, '$.pull_request.body') AS body,
  JSON_EXTRACT_SCALAR(payload, '$.pull_request.created_at') AS created_at,
  JSON_EXTRACT_SCALAR(payload, '$.pull_request.updated_at') AS updated_at,
  JSON_EXTRACT_SCALAR(payload, '$.pull_request.closed_at') AS closed_at,
  JSON_EXTRACT_SCALAR(payload, '$.pull_request.merged_at') AS merged_at,
  JSON_EXTRACT_SCALAR(payload, '$.pull_request.merge_commit_sha') AS merge_commit_sha,
  JSON_EXTRACT(payload, '$.pull_request.assignees') AS assignees,
  JSON_EXTRACT(payload, '$.pull_request.requested_reviewers') AS requested_reviewers,
  JSON_EXTRACT(payload, '$.pull_request.requested_teams') AS requested_teams,
  JSON_EXTRACT(payload, '$.pull_request.labels') AS labels,
  JSON_EXTRACT_SCALAR(payload, '$.pull_request.draft') AS draft,
  STRUCT( JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.label') AS label,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.ref') AS ref,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.sha') AS sha,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.id') AS repo_id,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.owner.login') AS repo_owner,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.name') AS repo_name,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.full_name') AS repo_full_name,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.description') AS description,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.fork') AS fork,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.created_at') AS created_at,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.updated_at') AS updated_at,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.pushed_at') AS pushed_at,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.homepage') AS homepage,
    CAST(JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.size') AS INT64) AS size,
    CAST(JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.stargazers_count') AS INT64) AS stargazers_count,
    CAST(JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.watchers_count') AS INT64) AS watchers_count,
    CAST(JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.forks_count') AS INT64) AS forks_count,
    CAST(JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.open_issues_count') AS INT64) AS open_issues_count,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.language') AS repo_language,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.has_issues') AS has_issues,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.has_projects') AS has_projects,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.has_downloads') AS has_downloads,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.has_wiki') AS has_wiki,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.has_pages') AS has_pages,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.has_discussions') AS has_discussions,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.allow_forking') AS allow_forking,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.is_template') AS is_template,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.archived') AS archived,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.disabled') AS disabled,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.license.key') AS license,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.head.repo.default_branch') AS default_branch,
    JSON_EXTRACT(payload, '$.pull_request.head.repo.topics') AS topics ) AS head,
  STRUCT( JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.label') AS label,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.ref') AS ref,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.sha') AS sha,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.id') AS repo_id,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.owner.login') AS repo_owner,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.name') AS repo_name,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.full_name') AS repo_full_name,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.description') AS description,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.fork') AS fork,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.created_at') AS created_at,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.updated_at') AS updated_at,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.pushed_at') AS pushed_at,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.homepage') AS homepage,
    CAST(JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.size') AS INT64) AS size,
    CAST(JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.stargazers_count') AS INT64) AS stargazers_count,
    CAST(JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.watchers_count') AS INT64) AS watchers_count,
    CAST(JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.forks_count') AS INT64) AS forks_count,
    CAST(JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.open_issues_count') AS INT64) AS open_issues_count,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.language') AS repo_language,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.has_issues') AS has_issues,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.has_projects') AS has_projects,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.has_downloads') AS has_downloads,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.has_wiki') AS has_wiki,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.has_pages') AS has_pages,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.has_discussions') AS has_discussions,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.allow_forking') AS allow_forking,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.is_template') AS is_template,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.archived') AS archived,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.disabled') AS disabled,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.license.key') AS license,
    JSON_EXTRACT_SCALAR(payload, '$.pull_request.base.repo.default_branch') AS default_branch,
    JSON_EXTRACT(payload, '$.pull_request.base.repo.topics') AS topics ) AS base,
  JSON_EXTRACT_SCALAR(payload, '$.pull_request.author_association') AS author_association,
  JSON_EXTRACT_SCALAR(payload, '$.pull_request.merged') AS merged,
  CAST(JSON_EXTRACT_SCALAR(payload, '$.pull_request.comments') AS INT64) AS comments,
  CAST(JSON_EXTRACT_SCALAR(payload, '$.pull_request.review_comments') AS INT64) AS review_comments,
  CAST(JSON_EXTRACT_SCALAR(payload, '$.pull_request.commits') AS INT64) AS commits,
  CAST(JSON_EXTRACT_SCALAR(payload, '$.pull_request.additions') AS INT64) AS additions,
  CAST(JSON_EXTRACT_SCALAR(payload, '$.pull_request.deletions') AS INT64) AS deletions,
  CAST(JSON_EXTRACT_SCALAR(payload, '$.pull_request.changed_files') AS INT64) AS changed_files
FROM
  `githubarchive.month.*`
WHERE
  TYPE='PullRequestEvent'

CREATE OR REPLACE TABLE
  `augment-387916.github_generated.pr_candidates` AS (
  SELECT
    pr.pr_id AS id,
    pr.* EXCEPT(pr_id),
    diff.pr_diff
  FROM
    `augment-387916.github_generated.pr_with_reviews_comments` AS pr
  LEFT JOIN
    `augment-387916.github_generated.pr_diff_deduped` AS diff
  ON
    pr.pr_id = diff.id
  WHERE
    diff.pr_diff IS NOT NULL
    AND diff.pr_diff != "DIFF_SIZE_OVER_LIMIT"
    AND merged='true'
    AND base.license IN (
    SELECT
      name
    FROM
      `augment-387916.github_generated.permissive_licenses`) )

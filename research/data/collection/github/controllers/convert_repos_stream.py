"""Stream raw JSON files for GitHub repo user API calls into Delta.

Also add enrichments like language, tags, line count, line length
statistics, and alphanumeric ratio.
"""
from research.data.spark import k8s_session, stream_from_files, stream_to_files

INPUT_SAMPLE = "s3a://augment-github/api_response/repos/"
INPUT_BASE = "s3a://augment-github/api_response/user_repos/raw/"
OUTPUT_PATH = "s3a://augment-github/repo/converted/"
CHECKPOINT_PATH = "s3a://augment-github/repo/.checkpoint/live/"

# Maximum allowed file size is 10MB. Note that PDF and HTML have already
# been converted to text.


if __name__ == "__main__":
    spark = k8s_session(max_workers=1)

    df = stream_from_files(
        spark,
        INPUT_BASE,
        input_format="json",
        sample_path=INPUT_SAMPLE,
        sample_format="parquet",
        mode="DROPMALFORMED",
        maxFilesPerTrigger=100,
        processing_time="2 minutes",
    )
    query = stream_to_files(
        df.repartition(5, "date"),
        OUTPUT_PATH,
        CHECKPOINT_PATH,
        partition_by="date",
    )

    try:
        query.awaitTermination()
    finally:
        spark.stop()

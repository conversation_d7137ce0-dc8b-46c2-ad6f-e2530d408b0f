"""Calculate language size metric."""

import argparse
from datetime import datetime, timedelta

import pyspark.sql.functions as F

import research.data.postgres_metastore as pg
from research.data.spark import k8s_session
from research.data.train.datasets.github import github_converted


def create_output_table():
    """Create output table if it does not exist."""
    query = """
    CREATE TABLE IF NOT EXISTS lang_size_metric (
        date VARCHAR,
        lang VARCHAR,
        file_count BIGINT,
        sum_char_size BIGINT,
        max_char_size BIGINT);
    CREATE UNIQUE INDEX IF NOT EXISTS date_lang_idx
        ON lang_size_metric USING btree(date, lang);
    """
    pg.execute(query)


def main(args):
    create_output_table()

    spark = k8s_session(max_workers=32)

    input_df = spark.read.format(github_converted.format).load(
        github_converted.location
    )

    end_date = (
        datetime.strptime(args.end_date, "%Y-%m-%d")
        if args.end_date
        else datetime.now()
    )

    for days_ago in range(args.days):
        date = (end_date - timedelta(days=days_ago)).strftime("%Y-%m-%d")
        print(f"Processing {date}")

        results = (
            input_df.filter(F.col("date") == date)
            .filter(F.col("is_head"))
            .withColumn("len_content", F.length(F.col("content")))
            .groupBy(F.col("lang"))
            .agg(
                F.count(F.col("content")).alias("file_count"),
                F.sum(F.col("len_content")).alias("sum_char_size"),
                F.max(F.col("len_content")).alias("max_char_size"),
            )
            .collect()
        )

        values = []
        for line in results:
            lang = line["lang"] if line["lang"] else "unknown"
            file_count = line["file_count"]
            sum_char_size = line["sum_char_size"]
            max_char_size = line["max_char_size"]
            values.append((date, lang, file_count, sum_char_size, max_char_size))

        if values:
            query = """
            INSERT INTO lang_size_metric (date, lang, file_count, sum_char_size, max_char_size)
            VALUES %s
            ON CONFLICT (date, lang) DO UPDATE
                SET file_count=EXCLUDED.file_count,
                sum_char_size=EXCLUDED.sum_char_size,
                max_char_size=EXCLUDED.max_char_size
            RETURNING file_count, sum_char_size;
            """
            returns = pg.batch_execute_generator(query, values)

            total_files = 0
            total_chars = 0
            for file_count, sum_char_size in returns:
                total_files += file_count
                total_chars += sum_char_size

            print(f"Total files: {total_files}")
            print(f"Total chars: {total_chars}")
        else:
            print("No results")

    spark.stop()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--end_date",
        type=str,
        default=None,
        help="End date to process",
    )
    parser.add_argument(
        "--days",
        type=int,
        default=1,
        help="Number of days to process",
    )

    main_args = parser.parse_args()
    main(main_args)

"""Refresh unknown repo count for each user."""
from itertools import permutations

import research.data.postgres_metastore as pg

template = """
SET enable_seqscan = OFF;
WITH unknown_count AS (
    SELECT
       canonical_owner, count(*) AS new_repos
    FROM repo
    WHERE
       {} AND {}
       AND source != 'the-stack'
       AND last_response_code = ''
    GROUP BY 1
)
INSERT INTO vendor_user (username, new_repos)
SELECT * FROM unknown_count
ON CONFLICT(username_lower, vendor) DO UPDATE
SET new_repos=EXCLUDED.new_repos;"""


def count_unknown():
    """Refresh unknown repo count."""
    alphanum = [chr(i) for i in range(48, 58)] + [chr(i) for i in range(97, 123)]
    milestones = sorted("".join(item) for item in permutations(alphanum, 2))
    for c1, c2 in zip([None] + milestones, milestones + [None]):
        print("Working on segment: ", c1, c2, flush=True)
        if c1:
            cond1 = f"canonical_owner >= '{c1}'"
        else:
            cond1 = "TRUE"
        if c2:
            cond2 = f"canonical_owner < '{c2}'"
        else:
            cond2 = "TRUE"

        query = template.format(cond1, cond2)
        pg.execute(query)


if __name__ == "__main__":
    count_unknown()
    # reapply overrides
    pg.execute(
        """UPDATE repo
            SET license=o.license
            FROM license_override AS o
            WHERE o.repo_id = repo.id;
        """
    )

"""Download contents of a github repo.

The raw repos will be stored as tar.gz in s3.
File contents, commit messages and diffs will also be saved in s3.
metadata will be updated in postgres tables.
"""
import argparse
import base64
import gzip
import hashlib
import io
import json
import os
import subprocess
import sys
import tarfile
import time
import traceback as tb
import uuid
from contextlib import suppress
from datetime import datetime
from itertools import chain, count
from pathlib import Path
from tempfile import TemporaryDirectory
from typing import Any, Generator, Iterable, Mapping, Optional, Sequence, Union

import boto3
import cchardet
import pygit2
from html2text import html2text
from PyPDF2 import PdfReader
from PyPDF2.errors import PdfReadError
from retry import retry

import research.data.postgres_metastore as pg

# Time limit to download repo.  this should cover the largeset repos
DEFAULT_EXECUTE_TIMEOUT = 1800

# Extensions to ignore and not include in content table
SKIP_EXT = (
    ".json",
    ".yml",
    ".yaml",
    ".csv",
    ".jsonl",
    ".geojson",
    ".topojson",
    ".yaml-tmlanguage",
    ".tsv",
    ".xml",
    ".xsd",
    ".log",
    ".out",
    ".shuffled",
    ".in",
    ".dat",
    ".info",
    ".e_tsv",
    ".changes",
    ".snapshot",
    ".data",
)

NO_SKIP = (
    "package.json",
    "pubspec.yaml",
    "composer.json",
    "package-lock.json",
)


@retry(tries=3, delay=10)
def execute(
    cmd: Union[str, Sequence[str]],
    timeout: Union[int, float, None] = DEFAULT_EXECUTE_TIMEOUT,
    check: bool = True,
    cwd: Optional[str] = None,
) -> str:
    """Execute a shell command.

    Args:
        cmd:        Command to execute
        timeout:    Timeout in seconds for the execution.  No timeout if None
        check:      When True (default), raise exception if the command has non-zero return
        cwd:        Working directory this shell command will be executed in

    Returns:
        The content of the standard output of this command as a string.
    """
    if isinstance(cmd, str):
        cmd_line = cmd
    else:
        cmd_line = " ".join(cmd)
    print(f"> {cmd_line}")
    p = subprocess.run(
        cmd, timeout=timeout, check=check, stdout=subprocess.PIPE, cwd=cwd
    )
    return p.stdout.decode("utf-8")


def pdf_to_text(blob: bytes) -> str:
    """Convert a pdf blob to text.

    Note: we have verified that this preserves indentation in code blocks
    """
    # Convert blob to a file-like object
    pdf_file = io.BytesIO(blob)
    pdf_reader = PdfReader(pdf_file)
    text = ""
    for page in pdf_reader.pages:
        text += page.extract_text()
    return text


def get_repo_to_download(
    path: str,
    bucket: str = "augment-github",
    max_repos: int = 1,
    batch_size: int = 1,
) -> Generator[dict[str, Any], None, None]:
    """Query repo table to identify repos that need to be downloaded.

    Args:
        path:           base download path
        bucket:         which bucket to download to
        max_repos:      How many repos to return
        batch_size:     How many repos to retrieve from database each batch

    Returns a dict containing info of this repo, with following fields:
        repo_id:   UUID of the repo to be downloaded
        owner:     Repo owner
        repo_name: Name of the repo
    """
    # get task ID.  tasks are partitioned by day and hour for analysis and later ETLs
    # this task_id will be used as the row id in the repo_storage table.
    now = datetime.now()
    partition = f"date_{now.strftime('%Y-%m-%d')}/hour_{now.strftime('%H')}"
    while max_repos > 0:
        this_batch = min(batch_size, max_repos)
        max_repos -= this_batch
        # The select, insert and return are combined into a single query.
        # This is to make sure the reservation is atomic
        # If we have many concurrent runs, they will not end up retrieving the same item
        query = f"""
          WITH repo_ids AS (
            SELECT repo_id
            FROM repo_download_queue
            WHERE NOT processed
            LIMIT {this_batch}
            FOR UPDATE SKIP LOCKED
          ),
          new_entry AS (
            UPDATE repo_download_queue AS q
            SET processed = TRUE
            FROM repo_ids
            WHERE repo_ids.repo_id = q.repo_id
            RETURNING task_id, q.repo_id, owner, repo_name, license, repo_root
          )
          INSERT INTO repo_storage (
            id, repo_id, owner, repo_name, license, storage_type, location, repo_root
          )
          SELECT
             task_id,
             repo_id, owner, repo_name, license,
             's3', %s || task_id || '_' || owner || '_' || repo_name || '.tar.gz',
             repo_root
          FROM new_entry
          RETURNING
              id, repo_id, owner, repo_name, license, location, repo_root
        """
        prefix = f"s3://{bucket}/{path}/{partition}/"
        print(f"Retrieving at most {this_batch} repos to download", flush=True)
        results = pg.execute(query, [prefix])
        # if there are nothing to work on then wait a bit and try again.
        while not results:
            print("Waiting 30 seconds to obtain more repos", flush=True)
            time.sleep(30)
            results = pg.execute(query, [prefix])
        # The result is a list, but it can only have either 0 or 1 items.  Here we get the first row
        print(f"Retrieved {len(results)} repos to process", flush=True)
        for (
            task_id,
            repo_id,
            owner,
            repo_name,
            license_key,
            location,
            repo_root,
        ) in results:
            print(f"New repo identified: {owner}/{repo_name}  ({repo_id})", flush=True)
            # The returned dict contains the content of the newly inserted row in repo_storage table
            yield {
                "task_id": task_id,
                "repo_id": repo_id,
                "owner": owner,
                "repo_name": repo_name,
                "license": license_key,
                "location": location,
                "repo_root": repo_root,
            }


# pylint: disable=no-member
def iterate_text_files(
    repo: pygit2.Repository,
    tree: Optional[pygit2.Tree] = None,
    path: Optional[str] = None,
    skip_ext: Sequence[str] = SKIP_EXT,
    no_skip: Sequence[str] = NO_SKIP,
    size_limit: Union[float, int, None] = 2e7,
    skip_threshold: int = 100000,
) -> Generator[tuple[str, str, int, bool, int], None, None]:
    """Iterate through the path, hex and size of all text files in a repo.

    Only text files are returned

    Note: this does not return file contents!  Because we still need to dedupe based on hash, and
    therefore can skip actually obtaining and decoding the contents for files we have already seen.

    Args:
        repo:           Repo to work on.  A pygit2 Repository object
        tree:           Tree to work on.  Default is root of HEAD. can be any folder in any commit.
        path:           Current path of this tree within the repo. Nothing means root
        skip_ext:       Extensions to mark as skipped.
        no_skip:        These filenames are preserved even if a skipped extension is encountered.
        size_limit:     Files larger than this will not be returned.
        skip_threshold: Files smaller than this are never skipped.

    This is a generator that yields triples in the form of path, sha
        path:           Full path of the object inside the repo
        sha:            SHA-1 hash (40 characters) of the object
        size:           The size of the blob
        is_binary:      If the file content is binary
        pct_modified:   Percentage modified in edits, up to 100 in int.  >100 if in head
    """
    # if tree is not supplied (usually means being called as the initial call), use the root
    # the tree will generally be set in recursive calls
    if tree is None:
        tree = repo[repo.head.target].tree
    # iterate through the tree and grab all non-binary files
    for entry in tree:
        # path aggregates the path variable if it is a recursive call
        # if path is not yet set, meaning it is the root level
        # Sometimes paths are not unicode encoded! #WINDOWS
        try:
            entry_name = str(entry.name)
        except UnicodeDecodeError:
            try:  # try and do a fit on a best effort basis
                detected = cchardet.detect(entry.name)
                entry_name = entry.name.decode(detected["encoding"])
            # if we can't fix will just throw away bad chars.  For commits that's enough
            except UnicodeDecodeError:
                entry_name = entry.name.decode("utf-8", error="ignore")
        obj_path = f"{path}/{entry_name}" if path else entry_name
        # this object was skipped due to size or other filters.
        # this can actually happen if filters are used in clone
        if entry.hex not in repo:
            continue
        obj = repo[entry.hex]
        # pylint: disable=no-member
        if entry.type == pygit2.GIT_OBJ_BLOB:  # BLOB means a file
            # retrieve the file extension.  Note that python includes the dot in there.
            ext = Path(obj_path).suffix.lower()
            # a few binary types that might contain valuable info that we can transform and get
            # potentially you can do llvm-dis to get .bc back to llvm for example.  But we
            # are lazy and are just gonna put the binary here, because we might not use them
            if obj.is_binary and ext not in (".bc", ".o", ".obj", ".pdf"):
                continue
            # pdf is a special case that is easy to convert to text
            if (
                size_limit is not None
                and obj.size > size_limit
                and ext not in (".pdf", ".html")
            ):
                print(f"File {obj_path} is too large ({obj.size}). Skipping")
                continue
            # file types that are in the skip list are those that do not carry information
            if (
                ext in skip_ext
                and not any(obj_path.endswith(filename) for filename in no_skip)
                and obj.size > skip_threshold
            ):
                continue
            # For future work:  compressed files like tar.gz etc should be expanded into
            # logical folders and the documents within can be extracted.
            yield obj_path, str(entry.hex), int(obj.size), obj.is_binary, 200
        # pylint: disable=no-member
        elif entry.type == pygit2.GIT_OBJ_TREE:  # This is a directory, traverse it
            yield from iterate_text_files(
                repo,
                obj,
                obj_path,
                skip_ext=skip_ext,
                no_skip=no_skip,
                size_limit=size_limit,
                skip_threshold=skip_threshold,
            )


def process_commits(
    repo_id: str,
    repo: pygit2.Repository,
    files: Mapping[str, Path],
) -> tuple[int, bool, int, int, dict[str, tuple[str, str, int, bool, int]]]:
    """Process through all commits in a repo.

    It updates the metadata in postgres and also writes the JSONL files with commits and patches

    Args:
        repo_id:        the id of the repo in the repo table.
        repo:           pygit2 Repository object
        files:          Maps each type of content to the file path: commit, patch, hunk

    Returns:
        A tuple containing:
            - number of commits in this repo
            - whether history is truncated
            - number of new commits
            - total number of patches (i.e. files that changed)
            - dict file involved in diffs which maps their SHA to file info.  info contains these
              fields, which are intentionally the same as returned by iterate_text_files():
                - relative path, in the form {commit_hash}/{relative_path}/{filename}
                - sha, a 40 character str
                - file size
                - whether this file is binary
                - percentage of the file changed
    """
    # user a walker to traverse commits back from head and retrieve all commits
    walker = repo.walk(repo.head.target)
    commits = {}
    sha = []
    try:
        for commit in walker:
            commits[commit.hex] = commit
            if commit.parents:
                parent_sha = commit.parents[0].hex
                parents = json.dumps([p.hex for p in commit.parents])
                n_parents = len(commit.parents)
            else:
                parent_sha = None
                parents = "[]"
                n_parents = 0
            sha.append((commit.hex, parent_sha, parents, n_parents))
    # if the parent sha does not exist in the repo, we know history is truncated
    except KeyError:
        truncated = True
    else:
        truncated = False

    # Populate the postgres tables and identify any new commits

    # Try to insert all commits into the git_commit table and use the
    # unique index on sha to automatically dedupe.  This query will return
    # anything that is actually successfully inserted and new.
    # Keeping the lookup and insert in the same query ensures that the
    # operation is atomic and consistent across many workers
    new_commits = pg.batch_execute_list(
        """INSERT INTO git_commit
            (sha, parent_sha, parents, num_parents)
            VALUES %s
            ON CONFLICT DO NOTHING
            RETURNING sha, parent_sha, parents
        """,
        sha,
    )
    # also populate the links between repos and commits.
    repo_commits = pg.batch_execute_list(
        """INSERT INTO repo_commit (repo_id, commit_sha)
            VALUES %s
            ON CONFLICT DO NOTHING
            returning *
        """,
        [(repo_id, entry[0]) for entry in sha],
    )
    print(f"Saved {len(repo_commits)} commits ({len(new_commits)} new)")

    # we are all done here if there are no new commits at all.
    if not new_commits:
        return len(commits), truncated, 0, 0, {}

    # now that we have all the NEW commits, comb through them for diffs
    rand = str(uuid.uuid4())[:6]  # a random string used to anonymize the emails
    total_patches = 0
    files_in_diff = {}  # this tracks all the files seen in diffs
    with files["git_commit"].open("w+") as commit_file, files["patch"].open(
        "w+"
    ) as patch_file, files["hunk"].open("w+") as hunk_file:
        for sha, parent_sha, parents in new_commits:
            commit = commits[sha]
            # anonymize the user's email to get an identity hash
            try:
                author = rand + "||" + commit.author.email
            except LookupError:
                author = "unknown author"
            author_hash = hashlib.sha256(author.encode()).hexdigest()[:6]
            # get diff set
            diff: Iterable[Any]
            if parent_sha:
                diff = chain.from_iterable(
                    repo.diff(p.tree, commit.tree) for p in commit.parents
                )
            else:
                diff = []
            # get all patches
            n_patch = 0
            for patch in diff:
                d = patch.delta
                n_context, n_add, n_remove = patch.line_stats
                n_change = n_add + n_remove
                if n_change:
                    pct_modified = 100 * n_change // (n_change + n_context)
                else:  # skip changes that have no additions or deletions.
                    continue
                old_file_sha = str(d.old_file.id)
                new_file_sha = str(d.new_file.id)
                # here we do NOT save whole file addition, deletions, rename or copy.
                action = d.status_char()
                if action in ("A", "D", "C", "R"):
                    diff_content = None
                else:
                    diff_content = patch.text.split("\n", 1)[-1]
                    files_in_diff[old_file_sha] = (
                        f"{sha}:{d.old_file.path}",
                        old_file_sha,
                        d.old_file.size,
                        False,
                        pct_modified,
                    )
                    for hunk in patch.hunks:
                        if not hunk.lines:
                            continue
                        hdoc = {
                            "commit_sha": sha,
                            "old_file_sha": old_file_sha,
                            "new_file_sha": new_file_sha,
                            "old_start": hunk.old_start,
                            "new_start": hunk.new_start,
                            "old_lines": hunk.old_lines,
                            "new_lines": hunk.new_lines,
                            "num_lines": len(hunk.lines),
                            "lines": [
                                {"origin": line.origin, "content": line.content}
                                for line in hunk.lines
                            ],
                        }
                        hunk_file.write(json.dumps(hdoc) + "\n")
                n_patch += 1
                # append to the patches file
                pdoc = {
                    "commit_sha": sha,
                    "new_file_sha": new_file_sha,
                    "old_file_sha": old_file_sha,
                    "action": action,
                    "diff": diff_content,
                    "n_add": n_add,
                    "n_remove": n_remove,
                    "n_context": n_context,
                    "pct_modified": pct_modified,
                }
                patch_file.write(json.dumps(pdoc) + "\n")
            try:
                message = commit.message
            # sometimes the encoding stored in commit isn't even correct. fun times.
            except UnicodeDecodeError:
                try:  # try and do a fit on a best effort basis
                    detected = cchardet.detect(commit.raw_message)
                    message = commit.raw_message.decode(detected["encoding"])
                # if we can't fix will just throw away bad chars.  For commits that's enough
                except UnicodeDecodeError:
                    message = commit.raw_message.decode("utf-8", error="ignore")
            # append to the commits file
            cdoc = {
                "sha": commit.hex,
                "author_hash": author_hash,
                "timestamp": commit.author.time,
                "n_patches": n_patch,
                "parent_sha": parent_sha,
                "parents": parents,
                "num_parents": len(parents),
                "message": message,
            }
            total_patches += n_patch
            commit_file.write(json.dumps(cdoc) + "\n")
    return len(commits), truncated, len(new_commits), total_patches, files_in_diff


def process_new_files(
    repo_dir: Path,
    task: Mapping[str, Any],
    files: Mapping[str, Path],
    batch_size: int = 100,
    skip_ext: Sequence[str] = SKIP_EXT,
    no_skip: Sequence[str] = NO_SKIP,
    size_limit: Union[int, float, None] = 2e7,
    skip_threshold: int = 100000,
) -> dict[str, Any]:
    """Process through all the text files in batches."""
    owner = task["owner"]
    repo_name = task["repo_name"]
    repo_id = task["repo_id"]
    full_name = f"{owner}/{repo_name}"
    repo_root = task["repo_root"]
    repo_id = task["repo_id"]
    repo = pygit2.Repository(repo_dir)
    last_commit = repo[repo.head.target]
    commits, truncated, new_commits, patches, commit_files = process_commits(
        repo_id, repo, files
    )
    info = {
        "head": repo.head.target.hex,
        "branch": repo.head.shorthand,
        "commits": commits,
        "history_truncated": truncated,
        "last_commit_at": last_commit.commit_time,
        "new_commits": new_commits,
        "patches": patches,
    }
    n = 0
    n_current = 0
    n_new = 0
    n_current_new = 0
    n_binary = 0
    total_size = 0
    new_size = 0
    with files["file_content"].open("w+") as f, files["repo_file"].open("w+") as rf:
        # first get all the fresh fils at HEAD
        text_files = chain(
            iterate_text_files(
                repo,
                skip_ext=skip_ext,
                no_skip=no_skip,
                size_limit=size_limit,
                skip_threshold=skip_threshold,
            ),
            # if there are files involved in commits, also process through those
            commit_files.values(),
        )
        # put head and diff files together and batch them into given size
        for batch in pg.batch_generator(text_files, batch_size):
            # First save the repo_files links to s3
            ts_now = datetime.utcnow().isoformat() + "Z"
            # files = []
            for obj_path, content_sha, size, is_binary, pct_modified in batch:
                n += 1
                total_size += size
                if pct_modified > 100:
                    n_current += 1
                    is_head = True
                else:
                    is_head = False
                # files.append((repo_id, content_sha, task['task_id'], obj_path))
                rf.write(
                    json.dumps(
                        {
                            "repo_id": repo_id,
                            "content_sha": content_sha,
                            "repo_storage_id": task["task_id"],
                            "pathname": obj_path,
                            "created_at": ts_now,
                            "is_head": is_head,
                        }
                    )
                    + "\n"
                )
            # pg.batch_execute(
            #     """INSERT INTO repo_file (repo_id, content_sha, repo_storage_id, pathname)
            #         VALUES %s
            #     """,
            #     files
            # )
            new_files = pg.batch_execute_list(
                """INSERT INTO file_content
                        (first_pathname, sha, size, is_binary, pct_modified, first_repo)
                        VALUES %s
                        ON CONFLICT DO NOTHING
                    RETURNING first_pathname, sha, size, is_binary, pct_modified
                """,
                [list(entry) + [full_name] for entry in batch],
            )
            size_updates: dict[str, Optional[int]] = {}
            # For files that are new, we need to extract content and append to the writer
            for path, sha, size, is_binary, pct_modified in new_files:
                n_new += 1
                if pct_modified > 100:
                    n_current_new += 1
                try:
                    data = repo[sha].data
                except (AttributeError, KeyError):
                    size_updates[sha] = None
                    continue
                ext = Path(path).suffix.lower()
                if ext == ".pdf":
                    # try and convert pdf to pure text
                    try:
                        data = pdf_to_text(data)
                        is_binary = False
                        size = sys.getsizeof(data)
                        size_updates[sha] = size
                    # Malformed PDFs can cause KeyErrors
                    except (
                        PdfReadError,
                        KeyError,
                        TypeError,
                        ValueError,
                        IndexError,
                        UnboundLocalError,
                        RecursionError,
                        AttributeError,
                    ):
                        size_updates[sha] = None
                        continue
                elif ext == ".html":
                    try:
                        data = html2text(data)
                        size = sys.getsizeof(data)
                        size_updates[sha] = size
                    except (AttributeError, TypeError):
                        pass
                if not data:
                    size_updates[sha] = None
                    continue
                if is_binary:
                    n_binary += 1
                    data = base64.b64encode(data)
                try:
                    if isinstance(data, str):
                        content = data
                    else:
                        content = data.decode("utf-8")
                except UnicodeDecodeError:
                    try:
                        detected = cchardet.detect(data)
                        if not detected or not detected["encoding"]:
                            size_updates[sha] = None
                            continue
                        content = data.decode(detected["encoding"])
                    # Python does not support embeddings from TW, causing lookup error
                    except (UnicodeDecodeError, LookupError):
                        print(f"Error encoding {path} for repo {full_name}. sha: {sha}")
                        print("This is likely not a text file!  Skipping")
                        n_binary += 1
                else:
                    doc = {
                        "sha": sha,
                        "path": path,
                        "repo_name": full_name,
                        "content": content,
                        "repo_root": repo_root,
                        "repo_id": repo_id,
                        "is_binary": is_binary,
                        "pct_modified": pct_modified,
                        "size": size,
                    }
                    f.write(json.dumps(doc) + "\n")
                new_size += size
            # Some sizes have changed
            if size_updates:
                print("Number of size updates: ", len(size_updates), flush=True)
            pg.batch_execute(
                """UPDATE file_content
                        SET size=u.size::BIGINT
                    FROM (VALUES %s) AS u(sha, size)
                    WHERE (u.sha)::char(40) = file_content.sha
                """,
                size_updates.items(),
            )
    info["files"] = n
    info["new_files"] = n_new
    info["current_files"] = n_current
    info["new_current_files"] = n_current_new
    info["binary_files"] = n_binary
    info["total_size"] = total_size
    info["new_file_size"] = new_size
    print(
        f"Found {n_current} valid files in repo. {n_current_new} files are new. "
        f"({n} total / {n_new} new counting historic versions referenced in commits)"
    )
    return info


def compress_and_upload(
    task_id: str,
    repo_dir: Path,
    tar_file: Path,
    files: Mapping[str, Path],
    s3: Any,
    location: str,
    max_file_size: float = 5e8,
) -> None:
    """Compress and upload a bare git repo to target location.

    The caller controls where to store the intermediate files, instead of use temp files here,
    to make it a bit easier to debug and perform individual downloads out of the main flow

    Args:
        task_id:        The id of the task.  This will be the record id in `repo_storage` table
        repo_dir:       Location of the local repository directory
        tar_file:       The path of the tar file to be created
        files:          Maps output type to the JSONL file for that output
        s3:             s3 resource for upload
        location:       s3 location to upload the tar.gz file.
        max_file_size:  if files are bigger than this, break into multipel parts before upload
    """
    partition = datetime.now().strftime("%Y-%m-%d-%H")
    # create a tar.gz file
    with tarfile.open(tar_file, "w:gz") as tarf:
        tarf.add(repo_dir, repo_dir.name)
    if tar_file.stat().st_size > 1e9:
        print(f"Skipping upload of tar.gz file of size {tar_file.stat().st_size}")
    else:
        # upload the file to designated s3 location
        bucket_name, object_name = location[len("s3://") :].split("/", 1)
        bucket = s3.Bucket(bucket_name)
        print(f"Uploading {tar_file} to s3://{bucket_name}/{object_name}", flush=True)
        bucket.upload_file(str(tar_file), object_name)
    for kind, filepath in files.items():
        if not filepath.exists() or filepath.stat().st_size == 0:  # skip empty files
            continue
        # No multi-part downlaod needed
        with TemporaryDirectory() as tmpdir:
            path = Path(tmpdir)
            with filepath.open("rt") as f:
                for part in count():
                    chunk_size = 0
                    part_path = path / f"{task_id}-part-{part:05d}.json.gz"
                    obj_path = (
                        f"{kind}/raw/part={partition}/{task_id}-part-{part:05d}.json.gz"
                    )
                    try:
                        with gzip.open(part_path, "wt") as gz_file:
                            for line in f:
                                gz_file.write(line)
                                chunk_size += len(line)
                                if chunk_size >= max_file_size:
                                    break
                            else:
                                break
                    except Exception as e:  # pylint: disable=broad-except
                        print("Exception occured: ", e)
                        tb.print_exc()
                    finally:
                        with suppress(Exception):
                            if part_path.stat().st_size > 0:
                                print(f"Uploading {part_path} to {obj_path}")
                                bucket.upload_file(part_path, obj_path)


def register_success(task: Mapping[str, Any], info: Mapping[str, Any]) -> None:
    """Register the success of a job and fill out the storage record with information from info."""
    additional_info_fields = (
        "files",
        "new_files",
        "binary_files",
        "total_size",
        "new_file_size",
    )
    additional_info = {key: info.get(key) for key in additional_info_fields}
    pg.execute(
        """UPDATE repo_storage
           SET
             status='done',
             finished_at=NOW(),
             flags=%s,
             branch=%s,
             head=%s,
             last_commit_at=to_timestamp(%s),
             commits=%s,
             history_truncated=%s,
             additional_info=%s
          WHERE id=%s""",
        [
            json.dumps(task["flags"]),
            info.get("branch", ""),
            info["head"],
            info["last_commit_at"],
            info["commits"],
            info["history_truncated"],
            json.dumps(additional_info),
            task["task_id"],
        ],
    )


def download_repo(task: Mapping[str, Any], output: str) -> None:
    """Git clone to local, analyze its content, and upload compressed files to s3."""
    flags = task["flags"]
    owner = task["owner"]
    repo_name = task["repo_name"]
    url = f"https://github.com/{owner}/{repo_name}.git"
    cmd = ["git", "-c", "core.askPass=true", "clone", "--bare", url, output]
    for key, value in flags.items():
        cmd.append(f"--{key}={value}")
    execute(cmd)


def process_task(task: Mapping[str, Any], s3: Any) -> None:
    """Process a download task.

    Args:
        task:  A dict containing task specifications
        s3:    s3 resource for uploads
    """
    # First try and download the repo
    with TemporaryDirectory() as tmpdir:
        tmp_path = Path(tmpdir)
        repo_dir = tmp_path / (task["repo_name"] + ".git")
        files = {
            kind: tmp_path / f"{kind}.json"
            for kind in ("file_content", "git_commit", "patch", "hunk", "repo_file")
        }
        try:
            download_repo(task, output=str(repo_dir))
            info = process_new_files(repo_dir, task, files, batch_size=100)
        except Exception as e:  # pylint: disable=broad-except
            print(f"Processing failed for repo. info: {task}", flush=True)
            pg.execute(
                """UPDATE repo_storage
                    SET status='failed', finished_at=NOW(), error_info=%s
                WHERE id=%s""",
                [
                    json.dumps({"message": str(e), "traceback": tb.format_exc()}),
                    task["task_id"],
                ],
            )
        else:
            register_success(task, info)
            print("Process success for task ", task["task_id"])
        finally:
            # Even if it fails halfway we should upload the successful half
            tar_file = tmp_path / "compressed-repo.tar.gz"
            with suppress(Exception):
                compress_and_upload(
                    task["task_id"],
                    repo_dir,
                    tar_file,
                    files,
                    s3,
                    task["location"],
                )


def main(args: Any) -> None:
    """Simply keep looking for new repos and work on them.  No priorities for now."""
    s3 = boto3.resource(
        "s3",
        aws_access_key_id=args.access_key or os.environ.get("CW_ACCESS_KEY_ID"),
        aws_secret_access_key=args.secret_key or os.environ.get("CW_SECRET_ACCESS_KEY"),
        endpoint_url=args.endpoint_url,
    )

    git_clone_flags = {"depth": args.depth}
    if args.size_limit:
        git_clone_flags["filter"] = f"blob:limit={args.size_limit}"
    task_generator = get_repo_to_download(
        "bare-repos",
        bucket=args.target_bucket,
        max_repos=args.max_repos,
    )
    for task in task_generator:
        # acquire a download task
        task["flags"] = git_clone_flags
        # now process it
        print("Working on task: ", task, flush=True)
        process_task(task, s3)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--access-key",
        type=str,
        default=None,
        help="S3 access key",
    )
    parser.add_argument(
        "--secret-key",
        type=str,
        default=None,
        help="S3 secret key",
    )
    parser.add_argument(
        "--endpoint-url",
        type=str,
        default="https://object.las1.coreweave.com",
        help="S3 endpoint URL",
    )
    parser.add_argument(
        "--target-bucket",
        type=str,
        default="augment-github",
        help="Target bucket to upload compressed repos to",
    )
    parser.add_argument(
        "--size-limit",
        type=str,
        default=None,
        help="Maximum allowed file size. Objects larger than this are skipped during clone",
    )
    parser.add_argument(
        "--depth",
        type=int,
        default=1000,
        help="Max history depth. Default is 1000",
    )
    parser.add_argument(
        "--max-repos",
        type=int,
        default=100,
        help="Max number of repos to process before exit",
    )
    main_args = parser.parse_args()
    main(main_args)

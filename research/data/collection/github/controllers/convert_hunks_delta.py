"""Stream raw JSON files for hunk to Delta table."""
from datetime import datetime, timedelta

import pyspark.sql.functions as F

from research.data.spark import k8s_session

INPUT_BASE = "s3a://augment-github/hunk/raw/"
OUTPUT_PATH = "s3a://augment-github/hunk/delta/"

N_PARTS = 500


def main():
    spark = k8s_session(max_workers=10)

    raw_df = spark.read.option("mode", "DROPMALFORMED").json(INPUT_BASE)
    # get the start and end date to work on
    [(last_run_time,)] = (
        spark.read.format("delta").load(OUTPUT_PATH).agg(F.max("date")).collect()
    )
    print("Last run time:", last_run_time, flush=True)
    start_date = datetime.strptime(last_run_time, "%Y-%m-%d").date()
    # the day before today is the last date
    today = datetime.utcnow().date()

    days = (today - start_date).days
    if days <= 1:
        print("No new dates to work with", flush=True)
        return
    print(f"Will convert {days - 1} days of data", flush=True)
    for day in range(1, days):
        date_str = (start_date + timedelta(days=day)).strftime("%Y-%m-%d")
        print(f"Working on partition {date_str}", flush=True)
        part_df = raw_df.filter(F.col("part").startswith(date_str)).withColumn(
            "date", F.lit(date_str)
        )
        part_df.repartition(N_PARTS).write.format("delta").mode("append").partitionBy(
            "date"
        ).save(OUTPUT_PATH)


if __name__ == "__main__":
    main()

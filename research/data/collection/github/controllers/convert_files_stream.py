"""Stream raw JSON files for file_content to Delta table.

Also add enrichments like language, tags, line count, line length
statistics, and alphanumeric ratio.
"""
from pathlib import Path

import pyspark.sql.functions as F
from pyspark.sql.types import StringType

from research.data.collection.github.controllers.languages import lookup
from research.data.spark import k8s_session, stream_from_files, stream_to_files

INPUT_SAMPLE = "s3a://augment-github/file_content/sample_schema/"
INPUT_BASE = "s3a://augment-github/file_content/raw/"
OUTPUT_PATH = "s3a://augment-github/file_content/converted/"
CHECKPOINT_PATH = "s3a://augment-github/file_content/.checkpoint/live/"

# Maximum allowed file size is 10MB. Note that PDF and HTML have already
# been converted to text.

MAX_LENGTH = 1e7


if __name__ == "__main__":
    spark = k8s_session(max_workers=3)

    @F.udf(StringType())
    def get_lang(filename: str) -> str:
        ext = Path(filename).suffix
        lang = lookup.get(ext, "")
        return lang.lower()

    df = stream_from_files(
        spark,
        INPUT_BASE,
        sample_path=INPUT_SAMPLE,
        mode="DROPMALFORMED",
        maxFilesPerTrigger=1000,
    )
    df = (
        df.withColumn("lang", get_lang(F.col("path")))
        .filter(F.length("content") < MAX_LENGTH)
        .withColumn("is_head", F.col("pct_modified") > 100)
        .withColumn("line_lengths", F.transform(F.split("content", "\n"), F.length))
        .withColumn(
            "avg_line_length",
            # line_lengths is an array of lengths for each row.  take row-wise average
            F.expr(
                "AGGREGATE(line_lengths, CAST(0 AS DOUBLE), (a, x) -> a + x, a -> a / SIZE(line_lengths))"
            ),
        )
        .withColumn("max_line_length", F.array_max("line_lengths"))
        .withColumn(
            "alphanum_fraction",
            F.length(F.regexp_replace("content", "[^a-zA-Z0-9]", ""))
            / F.length("content").cast("double"),
        )
        .drop("line_lengths")
        .withColumn("date", F.substring("part", 1, 10))
    )
    query = stream_to_files(
        df.repartition(40, "date"),
        OUTPUT_PATH,
        CHECKPOINT_PATH,
        partition_by="date",
    )

    try:
        query.processAllAvailable()
    finally:
        query.stop()
        spark.stop()

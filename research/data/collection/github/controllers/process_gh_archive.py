"""Process GH archive segments.

They are stored as .json.gz files at locations
https://data.gharchive.org/{YY}-{MM}-{DD}-{h}.json.gz
like this 2015-01-01-3.json.gz
"""
import argparse
import gzip
import io
import json
import time
from typing import Mapping

import requests

from research.data.postgres_metastore import batch_execute_list, batch_job, execute


def insert_repos(
    repos: Mapping[str, str], source: str = "gh-archive", batch_size: int = 500
) -> int:
    """Insert a set of repos into postgres repo table.

    Args:
        repos:       Mapping from GitHub ID to full account name in user/repo format
        source:      The value of `source` in repo table. Default `gh-archive`
        batch_size:  How many rows to insert at a time

    Returns:
        Number of new repos inserted.
    """
    batch_query = """
        INSERT INTO repo(owner, repo_name, vendor_record_id, source)
        VALUES %s
        ON CONFLICT DO NOTHING
        RETURNING id, owner, repo_name
    """
    batch = []
    inserted = 0
    for gh_id, full_name in repos.items():
        if full_name.count("/") != 1:
            print("Repo name is problematic. Skipping ", full_name)
            continue
        user, repo = full_name.split("/")
        batch.append((user, repo, gh_id, source))
        if len(batch) == batch_size:
            new_rows = batch_execute_list(batch_query, batch)
            inserted += len(new_rows)
            batch.clear()
    if batch:
        new_rows = batch_execute_list(batch_query, batch)
        inserted += len(new_rows)
    return inserted


def process_segment(segment: str, source="gh-archive") -> int:
    """Process a single GH Archive segment and extract unique users/repos.

    Args:
        segment:  GH Archive segment name.  In format `2021-3-19-7`

    Returns:
        number of new repos inserted
    """
    print("Processing segment ", segment)
    url = f"https://data.gharchive.org/{segment}.json.gz"
    response = requests.get(url, stream=True, timeout=120)

    # This might not exist
    if response.status_code == 404:
        raise batch_job.MissingSegment(response.text)
    # Make sure the request was successful
    response.raise_for_status()

    # Use BytesIO to handle the streamed content as a file-like object
    gzipped_file = io.BytesIO(response.content)

    # For now it's only ~50k repos per hour, so we can easily save that in
    # memory for then entire segment and at least first dedupe locally before
    # firing off to pg.  May need to revisit years later
    users = {}
    repos = {}
    # Decompress the gzipped content
    with gzip.GzipFile(fileobj=gzipped_file) as f:
        n = 0
        for line in f:
            try:
                doc = json.loads(line)
            except json.decoder.JSONDecodeError:
                print("Malformed line: ", line)
            else:
                if doc.get("actor") and doc["actor"].get("login"):
                    users[doc["actor"]["login"]] = doc["actor"]
                if doc.get("repo"):
                    repos[doc["repo"]["id"]] = doc["repo"]["name"]
                n += 1
    print(f"Number of entries: {n}. Actors: {len(users)}.  Repos: {len(repos)}")

    # For now we are discarding the user names.  but definitely should
    # revisit this later if we have more capacity
    inserted = insert_repos(repos, source)
    print("Number of new repos discovered: ", inserted)
    return inserted


def process_day(day: str) -> int:
    """Process one single day of dumps.

    Args:
        day:  Specifies which day of GH archive to process, like `2023-05-21`

    Returns:
        Number of new repos discovered
    """
    print("Processing GH archive dumps for day:", day)
    inserted = 0
    for hour in range(24):
        inserted += process_segment(f"{day}-{hour}")
    print(f"Total new repos found for the day: {inserted}")
    return inserted


def print_repo_stats() -> None:
    """Count repo stats for verification purposes."""
    print("  Source    |    Count\n-----------------")
    query = """
        SELECT source, count(*) AS count
        FROM repo GROUP BY 1 ORDER BY 2
    """
    for row in execute(query, readonly=True):
        print("{:12s}  {}".format(*row))


def main(args):
    # if ran as a script, grab a batch job entry, and process that day
    print("Repo stats before run:")
    print_repo_stats()
    for i in range(args.days):
        print(f"Processing {i} out of {args.days} days")
        try:
            with batch_job.get_batch_job(
                target_names=["gh-archive-processing"], start_date="2015-01-01"
            ) as job:
                print("Batch job record: ", job._asdict())
                job.additional_info["new_repos"] = process_day(job.partition_name)
        except batch_job.NoJobsLeft:
            print(
                "No more GH archive dumps to process. Waiting an hour to try again",
                flush=True,
            )
            time.sleep(3600)
    print("Repo stats after run:")
    print_repo_stats()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--days",
        type=int,
        default=30,
        help="Number of days to process",
    )
    main_args = parser.parse_args()
    main(main_args)

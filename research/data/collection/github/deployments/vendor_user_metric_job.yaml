apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: vendor-user-metric-job
spec:
  schedule: "0 3 * * *"   # Cron schedule for 3 am every day
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: vendor-user-metric-worker
            image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/github-download-worker:spark-3.4.2-28
            imagePullPolicy: IfNotPresent
            command:
            - psql
            - -c
            - |
              CREATE TABLE IF NOT EXISTS vendor_user_metric (
                  metric_timestamp TIMESTAMPTZ,
                  total_vendor_users BIGINT,
                  ok_vendor_users BIGINT,
                  unknown_vendor_users BIGINT,
                  total_new_repos BIGINT
              );
              INSERT INTO vendor_user_metric (
                  metric_timestamp,
                  total_vendor_users,
                  ok_vendor_users,
                  unknown_vendor_users,
                  total_new_repos
              )
              SELECT
                  NOW() AS metric_timestamp,
                  (SELECT count(*) FROM vendor_user) AS total_vendor_users,
                  (SELECT count(*) FROM vendor_user WHERE last_response_code = '200') AS ok_vendor_users,
                  (SELECT count(*) FROM vendor_user WHERE last_response_code IS NULL) AS unknown_vendor_users,
                  (SELECT sum(new_repos) FROM vendor_user) AS total_new_repos;
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: metastore-postgresql
                  key: password
            - name: PGHOST
              value: "metastore-postgresql"
            - name: PGDATABASE
              value: "metastore"
            - name: PGUSER
              value: "augment"
            resources:
              limits:
                cpu: "1"
              requests:
                cpu: "1"
                memory: "1Gi"
          restartPolicy: OnFailure
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                    - key: topology.kubernetes.io/region
                      operator: In
                      values:
                        - LAS1

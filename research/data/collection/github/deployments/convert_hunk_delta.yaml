apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: convert-hunk-delta
spec:
  schedule: "0 1 * * *"
  #  schedule: "0 1 * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          initContainers:
          - name: set-spark-driver-host
            image: busybox
            command:
            - "/bin/sh"
            - "-c"
            - "echo spark.driver.host=$MY_POD_IP > /opt/spark/conf/spark-defaults.conf"
            env:
            - name: MY_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            volumeMounts:
            - name: spark-config-volume
              mountPath: /opt/spark/conf
          containers:
          - name: hunk-convert-worker
            image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/github-download-worker:spark-3.4.2-28
            imagePullPolicy: Always
            command: ["python"]
            args:
            - "-m"
            - "data.collection.github.controllers.convert_hunks_delta"
            ports:
            - containerPort: 8080
            resources:
              limits:
                cpu: "1"
              requests:
                cpu: "1"
                memory: "2Gi"
            volumeMounts:
            - mountPath: /mnt/efs/augment
              name: viofs-aug-cw-las1
            - name: spark-config-volume
              mountPath: /opt/spark/conf
          restartPolicy: OnFailure
          volumes:
          - name: viofs-aug-cw-las1
            persistentVolumeClaim:
              claimName: aug-cw-las1
          - name: spark-config-volume
            emptyDir: {}
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                    - key: topology.kubernetes.io/region
                      operator: In
                      values:
                        - LAS1

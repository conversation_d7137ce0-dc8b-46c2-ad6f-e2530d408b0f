apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: refresh-download-queue
spec:
  schedule: "35 * * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: refresh-download-queue-worker
            image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/github-download-worker:spark-3.4.2-28
            imagePullPolicy: IfNotPresent
            command:
            - psql
            - -c
            - |
              DROP TABLE IF EXISTS tmp_new_q;
              CREATE TABLE tmp_new_q AS SELECT
                uuid_generate_v4() AS task_id,
                repo.id AS repo_id, repo.owner, repo.repo_name, repo.license,
                ' github/' || COALESCE(repo.source_record_id, repo.vendor_record_id) AS repo_root
              FROM repo
              JOIN license_info ON license_info.key = repo.license AND license_info.permissive
              LEFT JOIN repo_storage
                ON repo.id = repo_storage.repo_id
              AND (repo_storage.status != 'started'
                OR repo_storage.created_at > TIMESTAMPTZ 'yesterday')
              LEFT JOIN repo_download_queue AS q
                ON q.repo_id = repo.id
              WHERE repo_storage.repo_id IS NULL
                AND q.repo_id IS NULL
                AND repo.last_response_code = '200'
                AND repo.is_root and source != 'the-stack';
              INSERT INTO repo_download_queue
                (task_id, repo_id, owner, repo_name, license, repo_root)
              SELECT task_id, repo_id, owner, repo_name, license, repo_root
              FROM tmp_new_q
              ON CONFLICT DO NOTHING;
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: metastore-postgresql
                  key: password
            - name: PGHOST
              value: "metastore-postgresql"
            - name: PGDATABASE
              value: "metastore"
            - name: PGUSER
              value: "augment"
            resources:
              limits:
                cpu: "1"
              requests:
                cpu: "1"
                memory: "1Gi"
          restartPolicy: OnFailure
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                    - key: topology.kubernetes.io/region
                      operator: In
                      values:
                        - LAS1

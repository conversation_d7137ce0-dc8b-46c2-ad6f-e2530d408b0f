apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: repo-metric-job
spec:
  schedule: "0 */6 * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: repo-metric-worker
            image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/github-download-worker:spark-3.4.2-28
            imagePullPolicy: IfNotPresent
            command:
            - psql
            - -c
            - |
              INSERT INTO repo_metric(
                  metric_timestamp, source, license, total_count, api_called_count, root_repo_count, download_attempted_count,
                  successful_download_count, total_file_count
              )
              SELECT
                  NOW() AS metric_timestamp,
                  source,
                  repo.license,
                  COUNT(*) AS total_count,
                  SUM(CASE
                      WHEN repo.last_response_code != '' THEN 1
                      ELSE 0
                  END) AS api_called_count,
                  SUM(CASE WHEN is_root THEN 1 ELSE 0 END) AS root_repo_count,
                  COUNT(s.repo_id) AS download_attempted_count,
                  SUM(CASE WHEN s.status='done' THEN 1 ELSE 0 END) AS successful_download_count,
                  SUM(CAST(s.additional_info->'new_files' AS INTEGER)) AS total_file_count
              FROM
                  repo
              LEFT JOIN
                  repo_storage AS s
                  ON s.repo_id = repo.id
              GROUP BY
                  1, 2, 3;
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: metastore-postgresql
                  key: password
            - name: PGHOST
              value: "metastore-postgresql"
            - name: PGDATABASE
              value: "metastore"
            - name: PGUSER
              value: "augment"
            resources:
              limits:
                cpu: "1"
              requests:
                cpu: "1"
                memory: "1Gi"
          restartPolicy: OnFailure
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                    - key: topology.kubernetes.io/region
                      operator: In
                      values:
                        - LAS1

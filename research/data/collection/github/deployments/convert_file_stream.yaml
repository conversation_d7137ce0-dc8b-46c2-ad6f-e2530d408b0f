apiVersion: apps/v1
kind: Deployment
metadata:
  name: gh-file-content-convert
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gh-file-content-convert
  template:
    metadata:
      labels:
        app: gh-file-content-convert
    spec:
      initContainers:
      - name: set-spark-driver-host
        image: busybox
        command:
        - "/bin/sh"
        - "-c"
        - "echo spark.driver.host=$MY_POD_IP > /opt/spark/conf/spark-defaults.conf"
        env:
        - name: MY_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        volumeMounts:
        - name: spark-config-volume
          mountPath: /opt/spark/conf
      containers:
      - name: gh-file-content-converter
        image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/github-download-worker:spark-3.4.2-28
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        resources:
          requests:
            cpu: 1
            memory: "2Gi"      # Set the requested memory
        command: ["python"]
        args:
        - "-m"
        - "research.data.collection.github.controllers.convert_files_stream"
        volumeMounts:
        - mountPath: /mnt/efs/augment
          name: viofs-aug-cw-las1
        - name: spark-config-volume
          mountPath: /opt/spark/conf
      volumes:
      - name: viofs-aug-cw-las1
        persistentVolumeClaim:
          claimName: aug-cw-las1
      - name: spark-config-volume
        emptyDir: {}
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                - key: topology.kubernetes.io/region
                  operator: In
                  values:
                    - LAS1

apiVersion: apps/v1
kind: Deployment
metadata:
  name: gh-download-process
spec:
  replicas: 10
  selector:
    matchLabels:
      app: gh-download-process
  template:
    metadata:
      labels:
        app: gh-download-process
    spec:
      containers:
      - name: gh-download-processor
        image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/github-download-worker:spark-3.4.2-28
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        resources:
          requests:
            cpu: 1
            memory: "2Gi"      # Set the requested memory
        command: ["python"]
        args:
        - "-m"
        - "research.data.collection.github.controllers.git_clone_worker"
        - "--max-repos=1000"
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: metastore-postgresql
              key: password
        - name: PGHOST
          value: "metastore-postgresql"
        - name: PGDATABASE
          value: "metastore"
        - name: PGUSER
          value: "augment"
        - name: CW_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: tenant-augment-eng-determined-app-obj-store-creds
              key: accessKey
        - name: CW_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: tenant-augment-eng-determined-app-obj-store-creds
              key: secretKey
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                - key: topology.kubernetes.io/region
                  operator: In
                  values:
                    - LAS1

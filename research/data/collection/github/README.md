Github Processing Pipeline
======
Several pipelines for discovery and process of github repos, files and commits

Each task is a separate kubernetes deployement that can be separately scaled or shutdown.

Task scheduling, error handling and logs are all through kubernetes.

Tasks
------
* `gh-download-process`
Clone Github repos, store them and analyze and find unique new files and commits.
New files and commits are appended into JSON files and whole repos are stored as .tar.gz files.
These are all stored in S3.

* `gh-archive-scan`
Scan GH archive stream for new repos


Structure
------
```bash
github
├── README.md                   This file
├── controllers                 Python package for business logic
│   ├── __init__.py
│   ├── batch_job.py            Batch job utils
│   ├── languages.py            Suffix-language mapping
│   ├── git_clone_worker.py     Download and process repos
│   └── process_gh_archive.py   Scan GH archive
├── deployments                 k8s manifests for each tasks
│   ├── download_processor.yaml
│   └── gh_archive_scan.yaml
└── worker_builder              Building worker image
    ├── Dockerfile
    ├── build_worker.sh
    └── requirements.txt
```

Building image
------
run `worker_builder/build_worker.sh`

The resulting image are pushed to image github-download-worker:<tag> and used by
all tasks.

Deployment
-------
To create new deployments or update them after modifcation, run
```bash
kubectl apply -f <task manifest>
```

To scale an individual deployement,
```bash
kubectl scale deployment <task name> --replicas=<concurrency>
```

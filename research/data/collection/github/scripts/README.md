Imports metadata from Starcoder 2, and import their license information


The actual license detected in Starcoder 2 will be in the `starcoder2_license` table.
This is done by `import_sc2.py` script.

Then we use this info to update the original license in `repo` table.  If we do not
have a license but it is permissive in starcoder 2, we update the license to
`starcoder2-detected-permissive`.  This is added as a permissive license in `license_info`.
This update is done in `update_license.sql` query.

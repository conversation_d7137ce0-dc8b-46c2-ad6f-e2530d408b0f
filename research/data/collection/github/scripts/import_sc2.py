"""Import Starcoder 2 metadata and use the license info."""
import os

import pyspark.sql.functions as F

from research.data.spark import k8s_session

PATH = "/mnt/efs/spark-data/shared/starcoder2/metadata/full/datasets--bigcode--the-stack-v2"
SNAPSHOT = "cd59b1d3a13644604d13ee28eb4e47945e11131c"
LICENSE_PATH = "/mnt/efs/spark-data/shared/starcoder2/generated/starcoder2_licenses/"


if __name__ == "__main__":
    spark = k8s_session(
        max_workers=20,
        conf={
            # SC2 parquet needs this to correctly load
            "spark.sql.legacy.parquet.nanosAsLong": "true"
        },
    )

    # Load all permissive part of SC2
    df = spark.read.parquet(f"{PATH}/snapshots/{SNAPSHOT}/data/*/*.parquet").filter(
        "license_type='permissive'"
    )

    # Save on copy in JSONL the format of our own license detector
    df.select(
        F.col("branch_name").alias("default_branch"),
        F.lit("LICENSE.md").alias("filename"),
        F.lower(F.col("repo_name")).alias("full_name"),
        F.concat(
            F.array(
                F.struct(
                    F.lit("").alias("file"),
                    F.lit("starcoder2-detected-permissive").alias("license"),
                    F.lit(1.0).alias("confidence"),
                )
            ),
            F.expr(
                "transform(detected_licenses, license -> struct('' as file, license as license, 0.9 as confidence))"
            ),
        ).alias("result"),
        F.lit(True).alias("success"),
        F.lit("").alias("url"),
    ).dropDuplicates(["full_name"]).write.mode("overwrite").json(
        "/mnt/efs/spark-data/user/xiaolei/license_info/date=from-starcoder2/"
    )

    # Save another copy as parquet
    # We will use this to load the postgres table
    df.select(
        F.lower("repo_name").alias("repo_name"),
        "detected_licenses",
        "license_type",
        F.coalesce(
            F.col("gha_license_id"), F.lower(F.col("detected_licenses").getItem(0))
        ),
    ).dropDuplicates(["repo_name"]).write.mode("overwrite").parquet(LICENSE_PATH)

    properties = {
        "user": os.environ["PGUSER"],
        "password": os.environ["PGPASSWORD"],
        "driver": "org.postgresql.Driver",
    }

    # Load that parquet table into Postgres metastore
    # HAVE TO HAVE A SMALL NUMBER OF PARTITIONS!
    # Otherwise it will blow the connection limit of the database
    # This creates the starcoder2_license table
    spark.read.parquet(LICENSE_PATH).repartition(5).write.jdbc(
        url="*****************************************************",
        table="starcoder2_license",
        mode="overwrite",
        properties=properties,
    )

    spark.stop()

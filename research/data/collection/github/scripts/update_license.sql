-- Determine all the updates we need to do on the main repo table
CREATE TABLE tmp_update_license AS SELECT
repo.id AS repo_id
FROM repo
JOIN starcoder2_license as sc2 ON sc2.repo_name = repo.canonical_name
LEFT JOIN license_info AS l
  ON l.key = repo.license and l.permissive
WHERE l.key is NULL and sc2.license_type='permissive';

-- Update what we can on the repo table.
-- We don't want to lock forever, so just skip any locked
-- rows, due to other concurrent jobs
WITH to_update AS (
   SELECT repo_id FROM tmp_update_license
   LIMIT 10000 FOR UPDATE SKIP LOCKED
), sc2 AS
(
   delete from tmp_update_license
   USING to_update
   WHER<PERSON> to_update.repo_id = tmp_update_license.repo_id
   RETURNING to_update.repo_id
)
UPDATE repo
set license = 'starcoder2-detected-permissive'
FROM sc2
WHERE sc2.repo_id = repo.id;


-- remove the temporary table
DROP TABLE tmp_update_license;

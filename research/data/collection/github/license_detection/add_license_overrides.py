"""Analyze license info and add to license overrides table.

Usage

    > python add_license_overrides.py <input_json>

"""
import json
import sys
from pathlib import Path

import research.data.postgres_metastore.utils as pg_utils


def get_least_permissive(licenses, nonpermissive_list):
    """Return the least permissive license name.

    If there are any non-permissive license > 0.1 confidence, return the
    highest confidence item among those.

    Otherwise, return the highest confidence license, if confidence > 0.9.

    If no permissive license > 0.9 is found, return "".
    """
    max_confidence = -1
    max_conf_name = ""
    max_nonperm_confidence = -1
    max_nonperm_name = ""
    for entry in licenses:
        name = entry["license"].lower()
        if name in nonpermissive_list:
            if entry["confidence"] > max_nonperm_confidence:
                max_nonperm_confidence = entry["confidence"]
                max_nonperm_name = name
        if entry["confidence"] > max_confidence:
            max_confidence = entry["confidence"]
            max_conf_name = name
    if max_nonperm_confidence > 0.1:
        return max_nonperm_name
    elif max_confidence > 0.9:
        return max_conf_name
    else:
        return ""


def main():
    """Add license overrides to a file."""

    license_info = dict(pg_utils.execute("SELECT key, permissive FROM license_info"))

    nonpermissive = set(
        key.lower() for key, value in license_info.items() if key and not value
    )

    overrides = []
    in_file = Path(sys.argv[1])
    with in_file.open() as f:
        for line in f:
            entry = json.loads(line)
            license_name = get_least_permissive(entry["result"], nonpermissive)
            if license_name:
                overrides.append((entry["full_name"], license_name))

    print(f"Trying to add {len(overrides)} overrides to the database")
    pg_utils.batch_execute(
        """INSERT INTO license_override (repo_id, license)
            SELECT
                repo.id,
                override.license
            FROM repo
            JOIN (VALUES %s) AS override(repo_name, license)
            ON lower(override.repo_name) = repo.canonical_name
            ON CONFLICT (repo_id)  DO UPDATE
                SET license = EXCLUDED.license
        """,
        overrides,
    )
    pg_utils.execute(
        """UPDATE repo
        SET license=o.license
        FROM license_override AS o
        WHERE o.repo_id = repo.id
    """
    )


if __name__ == "__main__":
    main()

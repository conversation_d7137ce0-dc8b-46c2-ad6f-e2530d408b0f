License Detection
==================

Add license detection to licenses unrecognized by github.


Following Starcoder, we use [go-license-detector](https://github.com/go-enry/go-license-detector) to detect the license of a
repository.

We download the binary directly from the release page at https://github.com/go-enry/go-license-detector/releases/tag/v4.3.0


Ensuring the license detection does not let non-permissive licenses through
------------------------------------------

After a manual test on 100 undetected repo licenses, we find that it can misidentify
GPL licenses when many other licenses also appear in the repo.  This is undesirable, so
some intentionally broad match based filters are added to pre-classify any files that contain
any mention of several licenses, or any mention of non-commercial as non-permissive.

This is not likely to be very accurate, but should have fairly good coverage.   For example if someone
says this repo is not GPL licensed or compatible with GPL, we are just gonna excluded it for now until we
have a more reliable classifier.

The prefiltered licences are:
- GPL and any variants
- MPL
- EPL
- non-commercial

With this prefiltering, the process correctly excluded all GPL licenses from the whole test set.

Determining licenses without full repo
-------------------------------
Starcoder downloads all repos then detect their licenses.  This is unnecessarily computationally and
storage intensive, and we do not want to download and store 10 times more repos.

Instead, because we already know the primary branch name and repo name, we use the HTTP endpoint of
github to directly try to retrieve a series of files that are most likely containing license info.

The list of filenames from guess_license.py are:
- LICENSE
- LICENSE.md
- LICENSE.txt
- LICENSE.rst
- license.txt
- license
- COPYING
- COPYING.md
- COPYING.txt
- COPYING.rst
- COPYRIGHT
- COPYRIGHT.md
- COPYRIGHT.txt
- COPYRIGHT.rst
- NOTICE
- README.md
- README
- README.rst
- README.txt
- README.markdown
- readme.md
- readme
- readme.txt
- readme.rst
- readme.markdown
- readme.asciidoc
- readme.adoc

When we detect the presense of one of these files, we would download it and create a
dummpy repo with just this file, and use go-license-detector to detect the license.
This is done by `guess_license.py`, and the content of the license-containing file, along
with all detected license info, are stored in the shared drive for later analysis.


Determining most likely license
-----------------------------
This is done in `add_license_overrides.py`.
The license detect returns a list of possible licenses and their likelihood.  We need
to identify the least permissive probably license.  To do that, we go through the license
found previously, and find the highlest likelihood non-permissive license and the highest
permissive license.

We want to error on the side of caution.

If there are any non-permissive license > 0.1 confidence, we return the highest confidence item among those.

Otherwise, we return the highest confidence license, if confidence > 0.9.

If no permissive license > 0.9 is found, we return "".

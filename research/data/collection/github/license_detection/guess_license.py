"""Guess the license of a repo based on presence of license files or readmes."""
import json
import re
import subprocess
import tempfile
import time
import traceback as tb
import uuid
from dataclasses import dataclass
from pathlib import Path
from typing import Iterator, Optional

import pyspark.sql.functions as F
import requests

from research.data.spark.utils import k8s_session


@dataclass
class LicenseFileData:
    """Data class for license file data."""

    full_name: str
    default_branch: str
    filename: str
    url: str
    content: str


LICENSE_INFO_PATH = "/mnt/efs/spark-data/user/xiaolei/license_known/"

# To be doubly sure we don't miss GPL/LGPL/AGPL/MPL/EPL files
PRECHECK_STR = {
    "likely-GPL": ["gpl", "gnu"],
    "likely-EPL": ["epl", "Eclipse Public License"],
    "likely-MPL": ["mpl", "mozilla public license"],
    "likely-non-commercial": ["non-commercial", "noncommercial"],
}

PRECHECK_PATTERN = {
    key: [re.compile(f"\\b{word}\\b", re.IGNORECASE) for word in words]
    for key, words in PRECHECK_STR.items()
}


def guess_license(full_name: str, filename: str, content: str):
    """Guess the license of a file based on its content."""
    if not content:
        return []
    repo_name = full_name.split("/", 1)[-1]
    for key, patterns in PRECHECK_PATTERN.items():
        for pattern in patterns:
            if pattern.search(content):
                return [
                    {
                        "license": key,
                        "confidence": 1.0,
                        "file": filename,
                    }
                ]
    # Create a temporary directory and place the file there
    with tempfile.TemporaryDirectory() as tmpdir:
        repo_dir = Path(tmpdir) / repo_name
        repo_dir.mkdir()
        with (repo_dir / filename).open("w") as f:
            f.write(content)
        # use go-license-detector to detech license
        proc = subprocess.run(
            f"/usr/local/local_user_base/bin/license-detector -f json '{repo_dir.as_posix()}'",
            shell=True,
            check=True,
            capture_output=True,
            text=True,
        )
        err_out = proc.stderr
        if err_out:
            raise RuntimeError("license-detector failed: " + err_out)
        result = json.loads(proc.stdout)
        if len(result) != 1:
            raise RuntimeError("Should return exactly one project.")
        entry = result[0]
        if "error" in entry:
            raise RuntimeError("license-detector gave an error: " + entry["error"])
        if "matches" not in entry:
            raise RuntimeError("license-detector gave no matches.")
        return entry["matches"]


FILE_NAMES = [
    "LICENSE",
    "LICENSE.md",
    "LICENSE.txt",
    "LICENSE.rst",
    "license.txt",
    "license",
    "COPYING",
    "COPYING.md",
    "COPYING.txt",
    "COPYING.rst",
    "COPYRIGHT",
    "COPYRIGHT.md",
    "COPYRIGHT.txt",
    "COPYRIGHT.rst",
    "NOTICE",
    "README.md",
    "README",
    "README.rst",
    "README.txt",
    "README.markdown",
    "readme.md",
    "readme.txt",
    "readme",
    "readme.rst",
    "README.asciidoc",
    "README.adoc",
    "readme.asciidoc",
    "readme.adoc",
]


def get_license_files(
    limit: Optional[int] = None, filter_expr: Optional[str] = None
) -> Iterator[LicenseFileData]:
    """Get the license files from the GitHub repo."""
    spark = k8s_session(max_workers=1)
    done_df = (
        spark.read.json(LICENSE_INFO_PATH)
        .select(F.lower(F.col("full_name")).alias("canonical_name"), "success")
        .cache()
    )
    df = spark.read.parquet("/mnt/efs/spark-data/shared/augment-github/repo/deduped/")
    df = df.filter("license.key is null or license.key='other'").withColumn(
        "canonical_name", F.lower("full_name")
    )
    if filter_expr:
        df = df.filter(filter_expr)
    df = df.join(F.broadcast(done_df), on="canonical_name", how="left_anti")
    if limit:
        df = df.limit(limit)
    sample = df.select("full_name", "default_branch").collect()
    for repo, branch in sample:
        print("Working on", repo, branch)
        # get the raw content url to the license file
        # Try the 10 most likely license file names
        for filename in FILE_NAMES:
            try:
                # print(f"Trying {filename} in {repo} on branch {branch}")
                url = f"https://raw.githubusercontent.com/{repo}/{branch}/{filename}"
                time.sleep(0.1)
                r = requests.get(url, timeout=10)
                if r.status_code == 200:
                    content = r.text.strip()
                    if not content:
                        continue
                    yield LicenseFileData(
                        full_name=repo,
                        default_branch=branch,
                        filename=filename,
                        url=url,
                        content=content,
                    )
                    break
            except Exception as exc:  # pylint: disable=broad-except
                print(f"Failed to get {filename} in {repo} on branch {branch}: {exc}")
                tb.print_exc()
        else:
            yield LicenseFileData(
                full_name=repo, default_branch=branch, filename="", url="", content=""
            )
    spark.stop()


def main():
    results = []
    output_file = Path(LICENSE_INFO_PATH) / f"license_data_{uuid.uuid4()}.json"
    print(f"Writing to {output_file}")
    with output_file.open("w+") as f:
        for data in get_license_files(
            limit=5000,
            filter_expr="not fork",
        ):
            item = {
                "full_name": data.full_name,
                "default_branch": data.default_branch,
                "filename": data.filename,
                "url": data.url,
                "result": [],
                "success": True,
                "error": None,
            }
            try:
                item["result"] = guess_license(
                    data.full_name, data.filename, data.content
                )
            except Exception as exc:  # pylint: disable=broad-except
                print(
                    f"Failed to get license for {data.full_name} {data.filename} {data.url}: {exc}"
                )
                item["success"] = False
                item["error"] = str(exc)
            f.write(json.dumps(item) + "\n")
            results.append(item)
    print("DONE. Result can be found at >>>>>>", output_file)


if __name__ == "__main__":
    main()

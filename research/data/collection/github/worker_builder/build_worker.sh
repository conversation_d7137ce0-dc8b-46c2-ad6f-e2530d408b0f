#!/usr/bin/env bash

# First find out where augment is.
script_path=$(readlink -f "$0")
augment_path=$(dirname "$(dirname "$(dirname "$(dirname "$(dirname "$(dirname "$script_path")")")")")")
echo Using augment located at $augment_path

# Create a temp dir for building
workdir=$(mktemp -d)
echo Building image at $workdir
cp $HOME/.s3cfg $workdir/.tmp_docker_build_s3cfg
cp $HOME/.kube/config $workdir/.tmp_kubeconfig
cp Dockerfile requirements.txt $workdir/
mkdir -p $workdir/augment
cp -r $augment_path/pyproject.toml $augment_path/research $augment_path/experimental $augment_path/base $workdir/augment
# Build the container
# Base image tag is in file ../../../environments/spark_cpu_tag.txt
BASE_IMAGE_TAG=$(cat $augment_path/research/environments/spark_cpu_tag.txt)
echo Using base image tag $BASE_IMAGE_TAG

declare -r CLUSTER=gcp-us1
read REGISTRY BASE_IMAGE < <(jsonnet -Se "local C = (import '$augment_path/research/infra/cfg/clusters/clusters.jsonnet').cluster('$CLUSTER'); C.registry + ' ' + C.images.spark_cpu")
declare -r REGISTRY BASE_IMAGE

IMAGE=$REGISTRY/augment-research-cpu-with-spark
# extract the part after `:` in the base image tag to be the tag for the new image
TAG=${BASE_IMAGE_TAG#*:}

docker pull $BASE_IMAGE
# Build the image
echo Building $IMAGE:$TAG
docker build --no-cache \
             --build-arg BASE_IMAGE_TAG=$BASE_IMAGE_TAG \
             --build-arg REGISTRY=$REGISTRY \
             -t $IMAGE:$TAG $workdir

# After build, tag it then push it
# probably shouldn't use latest but will worry about that later
docker tag $IMAGE:$TAG $REGISTRY/$IMAGE:$TAG
docker push $REGISTRY/$IMAGE:$TAG

rm -rf $workdir

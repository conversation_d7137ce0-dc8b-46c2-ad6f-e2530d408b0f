ARG BASE_IMAGE_TAG
ARG REGISTRY
FROM $REGISTRY/${BASE_IMAGE_TAG}

USER root
# install psql
RUN apt update -y && \
    apt install -y --no-install-recommends libpq-dev && \
    apt install -y postgresql-client && \
    apt clean

WORKDIR /augment-runner
RUN mkdir -p /augment-runner/augment && chown augment:augment /augment-runner/augment
COPY --chown=augment:augment augment/research ./augment/research
COPY --chown=augment:augment augment/base ./augment/base
COPY --chown=augment:augment augment/experimental ./augment/experimental
COPY --chown=augment:augment augment/pyproject.toml ./augment/pyproject.toml

# Install python packages
COPY requirements.txt .
USER augment
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir s3cmd==2.3.0

# && \
RUN pwd && ls augment && cd augment && \
    pip install --no-deps -q -e . --user && \
    cd -

ENTRYPOINT []

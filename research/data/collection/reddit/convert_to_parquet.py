"""Convert Reddit dump jsonl to parquet.

There are too many columns, so we need to use Arctic Shift's schema.
Trying to let Spark determine schema will cause it to crash.

Some columns are always NULLs, or structs with no fields, or arrays
that are always empty.  They have to be stripped otherwise we won't
be able to save to parquet.

Note that this JSON is converted from Typescript, and in true TS
fashion each field is a union of arbitrary number of types.

However, the only frequent case is something | null, which we
simply translate to a NULLABLE column.  The other situation is
int | float, which we just consider as float.
"""
import json
from pathlib import Path

from pyspark.sql.types import (
    ArrayType,
    BooleanType,
    FloatType,
    IntegerType,
    MapType,
    StringType,
    StructField,
    StructType,
)

from research.data.spark.utils import k8s_session


def parse_object_schema(schema: dict):
    """Convert a schema of an object to a PySpark StructType schema.

    Args:
    - json_schema (dict): The JSON schema to convert, must be a dict with type `object`

    Returns:
    - StructType: The corresponding PySpark schema.
    """

    if schema["type"] != "object":
        raise ValueError("Schema must be of type object")
    if not schema["schema"]:
        return None
    if "type" in schema["schema"] and schema["schema"]["type"] == "key-value":
        # This is a mapping, not a struct
        value_type, nullable = parse_type(schema["schema"]["schema"])
        return MapType(StringType(), value_type, nullable)
    if any(not isinstance(item, list) for item in schema["schema"].values()):
        raise ValueError(f"Cannot convert object: {schema}")
    fields = []
    for field_name, field_types in schema["schema"].items():
        field_type, nullable = parse_type(field_types)
        if field_type is None:
            continue
        field = StructField(field_name, field_type, nullable)
        fields.append(field)

    return StructType(fields)


def parse_type(allowed_types: list[dict]):
    """Convert a list of possible types to a PySpark type, along with nullability."""
    if not allowed_types:
        return None, True
    if any(not isinstance(item, dict) for item in allowed_types):
        raise ValueError(f"Cannot convert schema with type: {allowed_types}")
    all_types = set(item["type"] for item in allowed_types)
    nullable = False
    if "null" in all_types:
        all_types.remove("null")
        if not all_types:
            return None, True
        nullable = True
    if len(all_types) > 1:
        if sorted(all_types) == ["float", "int"]:
            return FloatType(), nullable
    # Get the first non-null element from alled_types
    items = [item for item in allowed_types if item["type"] != "null"]
    if not items:
        raise ValueError(f"Cannot convert schema with type: {allowed_types}")
    item = items[0]
    item_type = item["type"]
    if item_type == "string":
        return StringType(), nullable
    if item_type == "boolean":
        return BooleanType(), nullable
    if item_type == "int":
        return IntegerType(), nullable
    if item_type == "float":
        return FloatType(), nullable
    if item_type == "bool":
        return BooleanType(), nullable
    if item_type == "array":
        element_type, _ = parse_type(item["schema"]["schema"])
        if element_type is None:
            return None, nullable
        return ArrayType(element_type), nullable
    if item_type == "object":
        return parse_object_schema(item), nullable
    raise ValueError(f"Cannot convert schema with type: {all_types}")


def convert_dump(spark, jsonl_path, parquet_path, schema_path):
    """Convert a particular JSONL dump to parquet with a given schema."""
    with Path(schema_path).open() as f:
        schema_doc = json.load(f)
    schema, _ = parse_type(schema_doc)
    df = spark.read.schema(schema).json(jsonl_path)
    df.write.mode("overwrite").parquet(parquet_path)


def main():
    date = "2024-01"
    base = "/mnt/efs/spark-data/shared/nl-datasets/reddit"
    spark = k8s_session(max_workers=20)
    for table in ["posts", "comments"]:
        convert_dump(
            spark,
            f"{base}/jsonl/{table}/",
            f"{base}/parquet/{table}/",
            f"{base}/schema/{table}-{date}.json",
        )

    spark.stop()


if __name__ == "__main__":
    main()

- total rows: 41235982
- uncompressed size: 196065138290


Schema:

```typescript
interface RedditPostData {
	_meta: object
	ad_business?: null
	ad_promoted_user_posts?: null
	ad_supplementary_text_md?: null|String
	ad_user_targeting?: null
	adserver_click_url?: null
	adserver_imp_pixel?: null
	all_awardings: Array
	allow_live_comments: boolean
	app_store_data?: null
	approved_at_utc: null
	approved_by: null
	archived: boolean
	author: String
	author_cakeday?: boolean
	author_flair_background_color?: null|String
	author_flair_css_class?: null|String
	author_flair_richtext?: Array
	author_flair_template_id?: null|String
	author_flair_text?: null|String
	author_flair_text_color?: null|String
	author_flair_type?: String
	author_fullname?: String
	author_id?: String
	author_is_blocked: boolean
	author_patreon_flair?: boolean
	author_premium?: boolean
	awarders: Array
	banned_at_utc: null
	banned_by: null
	call_to_action?: String
	campaign_id?: null
	can_gild: boolean
	can_mod_post: boolean
	category?: null|String
	clicked: boolean
	collections?: Array
	content_categories?: null|Array
	contest_mode: boolean
	created: integer
	created_utc: integer
	crosspost_parent?: String
	crosspost_parent_list?: Array
	discussion_type: null
	distinguished?: null|String
	domain: String
	domain_override?: String|null
	downs: integer
	edited?: boolean|integer
	embed_type?: null|String
	embed_url?: null|String
	event_end?: integer
	event_is_live?: boolean
	event_start?: integer
	events?: Array
	eventsOnRender?: Array
	gallery_data?: object|null
	gilded: integer
	gildings: object
	hidden: boolean
	hide_score: boolean
	href_url?: String
	id: String
	impression_id?: null
	impression_id_str?: null
	is_blank?: boolean
	is_created_from_ads_ui: boolean
	is_crosspostable: boolean
	is_gallery?: boolean
	is_meta: boolean
	is_original_content: boolean
	is_reddit_media_domain: boolean
	is_robot_indexable: boolean
	is_self: boolean
	is_survey_ad?: boolean
	is_video: boolean
	likes: null
	link_flair_background_color?: String|null
	link_flair_css_class?: null|String
	link_flair_richtext: Array
	link_flair_template_id?: String
	link_flair_text?: null|String
	link_flair_text_color?: String|null
	link_flair_type: String
	locked: boolean
	media?: null|object
	media_embed: object
	media_metadata?: object|null
	media_only: boolean
	mobile_ad_url?: String
	mod_note: null
	mod_reason_by: null
	mod_reason_title: null
	mod_reports: Array
	name: String
	no_follow: boolean
	num_comments: integer
	num_crossposts: integer
	num_reports: null
	original_link?: null
	outbound_link?: object
	over_18: boolean
	parent_whitelist_status?: null|String
	permalink: String
	pinned: boolean
	poll_data?: object|null
	post_hint?: String
	preview?: object
	priority_id?: null
	product_ids?: Array
	promo_layout?: null|String
	promoted?: boolean
	promoted_by?: null
	promoted_display_name?: null
	promoted_url?: null
	pwls?: null|integer
	quarantine: boolean
	removal_reason?: null|String
	removed_by: null
	removed_by_category?: null|String
	report_reasons: null
	retrieved_on: integer
	saved: boolean
	score: integer
	secure_media?: null|object
	secure_media_embed: object
	selftext: String
	send_replies: boolean
	show_media?: boolean
	sk_ad_network_data?: null
	spoiler: boolean
	stickied: boolean
	subcaption?: String
	subreddit: String
	subreddit_id: String
	subreddit_name_prefixed: String
	subreddit_subscribers: integer
	subreddit_type: String
	suggested_sort?: null|String
	third_party_trackers?: Array
	third_party_tracking?: null
	third_party_tracking_2?: null
	thumbnail?: String|null
	thumbnail_height?: integer|null
	thumbnail_width?: integer|null
	title: String
	top_awarded_type: null
	total_awards_received: integer
	treatment_tags: Array
	unrepliable_reason?: String
	ups: integer
	upvote_ratio?: integer|float
	url: String
	url_overridden_by_dest?: String
	user_reports: Array
	view_count: null
	visited: boolean
	whitelist_status?: null|String
	wls?: null|integer
}
```

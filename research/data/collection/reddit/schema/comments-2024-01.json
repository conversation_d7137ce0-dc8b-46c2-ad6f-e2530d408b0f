[{"type": "object", "usage": "always", "schema": {"_meta": [{"type": "object", "usage": "always", "schema": {"retrieved_2nd_on": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1706875209, "avr_value": 1705545822.39}}], "is_edited": [{"type": "bool", "usage": "always", "schema": {"true": 16095646, "false": 0}}], "removal_type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 7, "avr_length": 7.0, "values": [{"value": "deleted", "usage": 0.62}, {"value": "removed", "usage": 0.38}]}}], "was_deleted_later": [{"type": "bool", "usage": "always", "schema": {"true": 8747465, "false": 0}}], "note": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 21, "avr_length": 17.77, "values": [{"value": "no_2nd_retrieval", "usage": 0.65}, {"value": "initially_unavailable", "usage": 0.35}]}}], "was_initially_deleted": [{"type": "bool", "usage": "always", "schema": {"true": 1435505, "false": 0}}]}}], "all_awardings": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "approved_at_utc": [{"type": "null", "usage": "always", "schema": null}], "approved_by": [{"type": "null", "usage": "always", "schema": null}], "archived": [{"type": "bool", "usage": "always", "schema": {"true": 12736, "false": 284067892}}], "associated_award": [{"type": "null", "usage": "always", "schema": null}], "author": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 29, "avr_length": 12.71}}], "author_flair_background_color": [{"type": "null", "usage": 0.84, "schema": null}, {"type": "string", "usage": 0.16, "schema": {"min_length": 0, "max_length": 11, "avr_length": 4.98}}], "author_flair_css_class": [{"type": "null", "usage": 0.94, "schema": null}, {"type": "string", "usage": 0.06, "schema": {"min_length": 0, "max_length": 4338, "avr_length": 8.51}}], "author_flair_template_id": [{"type": "null", "usage": 0.88, "schema": null}, {"type": "string", "usage": 0.12, "schema": {"min_length": 0, "max_length": 36, "avr_length": 36.0}}], "author_flair_text": [{"type": "null", "usage": 0.85, "schema": null}, {"type": "string", "usage": 0.15, "schema": {"min_length": 0, "max_length": 64, "avr_length": 18.07}}], "author_flair_text_color": [{"type": "null", "usage": 0.81, "schema": null}, {"type": "string", "usage": 0.19, "schema": {"min_length": 0, "max_length": 5, "avr_length": 4.14, "values": [{"value": "dark", "usage": 0.83}, {"value": "light", "usage": 0.16}, {"value": "", "usage": 0.01}]}}], "author_is_blocked": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 284080628}}], "awarders": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "banned_at_utc": [{"type": "null", "usage": "always", "schema": null}], "banned_by": [{"type": "null", "usage": "always", "schema": null}], "body": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 10033, "avr_length": 169.98}}], "can_gild": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 284080628}}], "can_mod_post": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 284080628}}], "collapsed": [{"type": "bool", "usage": "always", "schema": {"true": 11040133, "false": 273040495}}], "collapsed_because_crowd_control": [{"type": "null", "usage": "always", "schema": null}], "collapsed_reason": [{"type": "null", "usage": 1.0, "schema": null}, {"type": "string", "usage": 0.0, "schema": {"min_length": 0, "max_length": 29, "avr_length": 27.5, "values": [{"value": "comment score below threshold", "usage": 0.87}, {"value": "potentially toxic", "usage": 0.13}]}}], "collapsed_reason_code": [{"type": "null", "usage": 0.98, "schema": null}, {"type": "string", "usage": 0.02, "schema": {"min_length": 0, "max_length": 17, "avr_length": 7.0, "values": [{"value": "DELETED", "usage": 1.0}, {"value": "LOW_SCORE", "usage": 0.0}, {"value": "POTENTIALLY_TOXIC", "usage": 0.0}]}}], "comment_type": [{"type": "null", "usage": "always", "schema": null}], "controversiality": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1, "avr_value": 0.02}}], "created": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1706745599, "avr_value": 1705414106.45}}], "created_utc": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1706745599, "avr_value": 1705414106.45}}], "distinguished": [{"type": "null", "usage": 0.96, "schema": null}, {"type": "string", "usage": 0.04, "schema": {"min_length": 0, "max_length": 9, "avr_length": 9.0, "values": [{"value": "moderator", "usage": 1.0}, {"value": "admin", "usage": 0.0}]}}], "downs": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 0, "avr_value": 0.0}}], "edited": [{"type": "bool", "usage": 1.0, "schema": {"true": 0, "false": 284044285}}, {"type": "int", "usage": 0.0, "schema": {"min_value": 0, "max_value": 1706740218, "avr_value": 1706432582.66}}], "gilded": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 0, "avr_value": 0.0}}], "gildings": [{"type": "object", "usage": "always", "schema": {}}], "id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 7, "avr_length": 7.0}}], "is_submitter": [{"type": "bool", "usage": "always", "schema": {"true": 30061781, "false": 254018847}}], "likes": [{"type": "null", "usage": "always", "schema": null}], "link_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 10, "avr_length": 9.99}}], "locked": [{"type": "bool", "usage": "always", "schema": {"true": 4095155, "false": 279985473}}], "mod_note": [{"type": "null", "usage": "always", "schema": null}], "mod_reason_by": [{"type": "null", "usage": "always", "schema": null}], "mod_reason_title": [{"type": "null", "usage": "always", "schema": null}], "mod_reports": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 10, "avr_length": 10.0}}], "no_follow": [{"type": "bool", "usage": "always", "schema": {"true": 266277435, "false": 17803193}}], "num_reports": [{"type": "null", "usage": "always", "schema": null}], "parent_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 10, "avr_length": 10.0}}], "permalink": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 102, "avr_length": 76.44}}], "removal_reason": [{"type": "null", "usage": 1.0, "schema": null}, {"type": "string", "usage": 0.0, "schema": {"min_length": 0, "max_length": 5, "avr_length": 5.0, "values": [{"value": "legal", "usage": "always"}]}}], "replies": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "values": [{"value": "", "usage": "always"}]}}], "report_reasons": [{"type": "null", "usage": "always", "schema": null}], "retrieved_on": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1706871326, "avr_value": 1705414418.86}}], "saved": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 284080628}}], "score": [{"type": "int", "usage": "always", "schema": {"min_value": -7239, "max_value": 45613, "avr_value": 6.64}}], "score_hidden": [{"type": "bool", "usage": "always", "schema": {"true": 7267192, "false": 276813436}}], "send_replies": [{"type": "bool", "usage": "always", "schema": {"true": 272559845, "false": 11520783}}], "stickied": [{"type": "bool", "usage": "always", "schema": {"true": 7012848, "false": 277067780}}], "subreddit": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 22, "avr_length": 10.78}}], "subreddit_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 8.24}}], "subreddit_name_prefixed": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 23, "avr_length": 12.77}}], "subreddit_type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 10, "avr_length": 6.02, "values": [{"value": "public", "usage": 0.98}, {"value": "restricted", "usage": 0.01}, {"value": "user", "usage": 0.01}, {"value": "private", "usage": 0.0}, {"value": "archived", "usage": 0.0}]}}], "top_awarded_type": [{"type": "null", "usage": "always", "schema": null}], "total_awards_received": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 0, "avr_value": 0.0}}], "treatment_tags": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "unrepliable_reason": [{"type": "null", "usage": 1.0, "schema": null}, {"type": "string", "usage": 0.0, "schema": {"min_length": 0, "max_length": 12, "avr_length": 12.0, "values": [{"value": "NEAR_BLOCKER", "usage": "always"}]}}], "ups": [{"type": "int", "usage": "always", "schema": {"min_value": -7239, "max_value": 45613, "avr_value": 6.64}}], "user_reports": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "author_flair_richtext": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 21, "avr_length": 0.17, "schema": [{"type": "object", "usage": "always", "schema": {"e": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 5, "avr_length": 4.43, "values": [{"value": "emoji", "usage": 0.43}, {"value": "text", "usage": 0.57}]}}], "t": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 64, "avr_length": 14.64}}], "a": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 26, "avr_length": 8.99}}], "u": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 78, "avr_length": 60.14}}]}}]}}], "author_flair_type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 8, "avr_length": 4.41, "values": [{"value": "text", "usage": 0.9}, {"value": "richtext", "usage": 0.1}]}}], "author_fullname": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 12, "avr_length": 10.55}}], "author_patreon_flair": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 273119983}}], "author_premium": [{"type": "bool", "usage": "always", "schema": {"true": 13113892, "false": 260006091}}], "media_metadata": [{"type": "object", "usage": "always", "schema": {"type": "key-value", "schema": [{"type": "object", "usage": "always", "schema": {"status": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 11, "avr_length": 5.01, "values": [{"value": "valid", "usage": 1.0}, {"value": "unprocessed", "usage": 0.0}, {"value": "invalid", "usage": 0.0}]}}], "e": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 13, "avr_length": 7.04, "values": [{"value": "AnimatedImage", "usage": 0.26}, {"value": "Image", "usage": 0.74}]}}], "id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 41, "avr_length": 16.52}}], "m": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 10, "avr_length": 9.57, "values": [{"value": "image/gif", "usage": 0.26}, {"value": "image/jpeg", "usage": 0.57}, {"value": "image/png", "usage": 0.17}]}}], "s": [{"type": "object", "usage": "always", "schema": {"x": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 16384, "avr_value": 928.0}}], "y": [{"type": "int", "usage": "always", "schema": {"min_value": -32268, "max_value": 32766, "avr_value": 1004.13}}], "u": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 119, "avr_length": 114.63}}], "gif": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 146, "avr_length": 127.36}}], "mp4": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 157, "avr_length": 146.69}}]}}], "p": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 3.92, "schema": [{"type": "object", "usage": "always", "schema": {"u": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 129, "avr_length": 113.78}}], "x": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1080, "avr_value": 446.45}}], "y": [{"type": "int", "usage": "always", "schema": {"min_value": -32268, "max_value": 2160, "avr_value": 504.37}}]}}]}}], "t": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 7, "avr_length": 5.14, "values": [{"value": "giphy", "usage": 0.65}, {"value": "emoji", "usage": 0.28}, {"value": "sticker", "usage": 0.07}]}}], "ext": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 43, "avr_length": 39.15}}]}}]}}], "author_cakeday": [{"type": "bool", "usage": "always", "schema": {"true": 1492421, "false": 0}}], "expression_asset_data": [{"type": "object", "usage": "always", "schema": {"type": "key-value", "schema": [{"type": "object", "usage": "always", "schema": {"avatar": [{"type": "object", "usage": "always", "schema": {"e": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 5, "avr_length": 5.0, "values": [{"value": "Image", "usage": "always"}]}}], "m": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 9.0, "values": [{"value": "image/png", "usage": "always"}]}}], "s": [{"type": "object", "usage": "always", "schema": {"u": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 174, "avr_length": 170.01}}], "x": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 380, "avr_value": 380.0}}], "y": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 600, "avr_value": 600.0}}]}}]}}], "expression": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 4.11, "schema": [{"type": "object", "usage": "always", "schema": {"e": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 6.0, "values": [{"value": " Image", "usage": "always"}]}}], "l": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 5, "avr_length": 4.73, "values": [{"value": "FRONT", "usage": 0.73}, {"value": "BACK", "usage": 0.27}]}}], "m": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 9.0, "values": [{"value": "image/png", "usage": "always"}]}}], "n": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 14, "avr_length": 6.07, "values": [{"value": "starstruck", "usage": 0.07}, {"value": "bravo", "usage": 0.41}, {"value": "holo_card", "usage": 0.04}, {"value": "heart", "usage": 0.11}, {"value": "clown", "usage": 0.06}, {"value": "fire", "usage": 0.08}, {"value": "dizziness", "usage": 0.03}, {"value": "webman", "usage": 0.02}, {"value": "winner", "usage": 0.03}, {"value": "take_my_energy", "usage": 0.06}, {"value": "cry", "usage": 0.03}, {"value": "laugh", "usage": 0.04}, {"value": "nani", "usage": 0.02}]}}], "s": [{"type": "object", "usage": "always", "schema": {"u": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 68, "avr_length": 57.88}}], "x": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1000, "avr_value": 418.39}}], "y": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 500, "avr_value": 316.67}}]}}]}}]}}], "perspective": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 7.49, "values": [{"value": "CROPPED", "usage": 0.76}, {"value": "FULL_BODY", "usage": 0.24}]}}], "position": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 5.93, "values": [{"value": "CENTER", "usage": 0.97}, {"value": "LEFT", "usage": 0.03}]}}], "size": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 10, "avr_length": 10.0, "values": [{"value": "SIZE_1_X_2", "usage": 0.39}, {"value": "SIZE_1_X_1", "usage": 0.61}]}}]}}]}}], "editable": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 1086}}], "rte_mode": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 8, "avr_length": 8.0, "values": [{"value": "markdown", "usage": "always"}]}}]}}]
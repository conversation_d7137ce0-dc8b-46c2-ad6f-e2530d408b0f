[{"type": "object", "usage": "always", "schema": {"_meta": [{"type": "object", "usage": "always", "schema": {"retrieved_2nd_on": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1706875209, "avr_value": 1705751898.55}}], "note": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 21, "avr_length": 16.0, "values": [{"value": "no_2nd_retrieval", "usage": 1.0}, {"value": "initially_unavailable", "usage": 0.0}]}}], "removal_type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 18, "avr_length": 7.52, "values": [{"value": "deleted", "usage": 0.58}, {"value": "reddit", "usage": 0.19}, {"value": "moderator", "usage": 0.19}, {"value": "automod_filtered", "usage": 0.03}, {"value": "content_takedown", "usage": 0.01}, {"value": "author", "usage": 0.0}, {"value": "copyright_takedown", "usage": 0.0}, {"value": "community_ops", "usage": 0.0}, {"value": "anti_evil_ops", "usage": 0.0}]}}], "was_deleted_later": [{"type": "bool", "usage": "always", "schema": {"true": 6589293, "false": 0}}], "is_edited": [{"type": "bool", "usage": "always", "schema": {"true": 889788, "false": 0}}], "was_initially_deleted": [{"type": "bool", "usage": "always", "schema": {"true": 539248, "false": 0}}]}}], "all_awardings": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "allow_live_comments": [{"type": "bool", "usage": "always", "schema": {"true": 9509, "false": 41226473}}], "approved_at_utc": [{"type": "null", "usage": "always", "schema": null}], "approved_by": [{"type": "null", "usage": "always", "schema": null}], "archived": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 41235982}}], "author": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 20, "avr_length": 13.43}}], "author_flair_background_color": [{"type": "null", "usage": 0.93, "schema": null}, {"type": "string", "usage": 0.07, "schema": {"min_length": 0, "max_length": 11, "avr_length": 5.46}}], "author_flair_css_class": [{"type": "null", "usage": 0.97, "schema": null}, {"type": "string", "usage": 0.03, "schema": {"min_length": 0, "max_length": 4338, "avr_length": 7.39}}], "author_flair_template_id": [{"type": "null", "usage": 0.94, "schema": null}, {"type": "string", "usage": 0.06, "schema": {"min_length": 0, "max_length": 36, "avr_length": 36.0}}], "author_flair_text": [{"type": "null", "usage": 0.93, "schema": null}, {"type": "string", "usage": 0.07, "schema": {"min_length": 0, "max_length": 64, "avr_length": 15.5}}], "author_flair_text_color": [{"type": "null", "usage": 0.92, "schema": null}, {"type": "string", "usage": 0.08, "schema": {"min_length": 0, "max_length": 5, "avr_length": 4.21, "values": [{"value": "dark", "usage": 0.75}, {"value": "", "usage": 0.01}, {"value": "light", "usage": 0.24}]}}], "author_is_blocked": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 41235982}}], "awarders": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "banned_at_utc": [{"type": "null", "usage": "always", "schema": null}], "banned_by": [{"type": "null", "usage": "always", "schema": null}], "can_gild": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 41235982}}], "can_mod_post": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 41235982}}], "category": [{"type": "null", "usage": 1.0, "schema": null}, {"type": "string", "usage": 0.0, "schema": {"min_length": 0, "max_length": 20, "avr_length": 14.04, "values": [{"value": "diy_and_crafts", "usage": 0.93}, {"value": "entertainment", "usage": 0.02}, {"value": "drawing_and_painting", "usage": 0.03}, {"value": "photography", "usage": 0.01}, {"value": "animals", "usage": 0.0}, {"value": "gaming", "usage": 0.01}, {"value": "music", "usage": 0.0}, {"value": "memes", "usage": 0.0}]}}], "clicked": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 41235982}}], "content_categories": [{"type": "null", "usage": 1.0, "schema": null}, {"type": "array", "usage": 0.0, "schema": {"min_length": 0, "max_length": 2, "avr_length": 1.0, "schema": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 20, "avr_length": 12.22, "values": [{"value": "photography", "usage": 0.16}, {"value": "entertainment", "usage": 0.12}, {"value": "diy_and_crafts", "usage": 0.14}, {"value": "drawing_and_painting", "usage": 0.25}, {"value": "gaming", "usage": 0.17}, {"value": "music", "usage": 0.05}, {"value": "comics", "usage": 0.04}, {"value": "videos", "usage": 0.02}, {"value": "writing", "usage": 0.04}]}}]}}], "contest_mode": [{"type": "bool", "usage": "always", "schema": {"true": 23217, "false": 41212765}}], "created": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1706745599, "avr_value": 1705404987.35}}], "created_utc": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1706745599, "avr_value": 1705404987.35}}], "discussion_type": [{"type": "null", "usage": "always", "schema": null}], "distinguished": [{"type": "null", "usage": 1.0, "schema": null}, {"type": "string", "usage": 0.0, "schema": {"min_length": 0, "max_length": 9, "avr_length": 9.0, "values": [{"value": "moderator", "usage": 1.0}, {"value": "admin", "usage": 0.0}]}}], "domain": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 195, "avr_length": 12.98}}], "downs": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 0, "avr_value": 0.0}}], "edited": [{"type": "bool", "usage": 1.0, "schema": {"true": 0, "false": 41229899}}, {"type": "int", "usage": 0.0, "schema": {"min_value": 0, "max_value": 1706752290, "avr_value": 1706176827.13}}], "gilded": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 0, "avr_value": 0.0}}], "gildings": [{"type": "object", "usage": "always", "schema": {}}], "hidden": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 41235982}}], "hide_score": [{"type": "bool", "usage": "always", "schema": {"true": 8363780, "false": 32872202}}], "id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 7, "avr_length": 7.0}}], "is_created_from_ads_ui": [{"type": "bool", "usage": "always", "schema": {"true": 100596, "false": 41135386}}], "is_crosspostable": [{"type": "bool", "usage": "always", "schema": {"true": 31253755, "false": 9982227}}], "is_meta": [{"type": "bool", "usage": "always", "schema": {"true": 5, "false": 41235977}}], "is_original_content": [{"type": "bool", "usage": "always", "schema": {"true": 496422, "false": 40739560}}], "is_reddit_media_domain": [{"type": "bool", "usage": "always", "schema": {"true": 14775408, "false": 26460574}}], "is_robot_indexable": [{"type": "bool", "usage": "always", "schema": {"true": 33374405, "false": 7861577}}], "is_self": [{"type": "bool", "usage": "always", "schema": {"true": 16441763, "false": 24794219}}], "is_video": [{"type": "bool", "usage": "always", "schema": {"true": 1092300, "false": 40143682}}], "likes": [{"type": "null", "usage": "always", "schema": null}], "link_flair_background_color": [{"type": "string", "usage": 0.99, "schema": {"min_length": 0, "max_length": 11, "avr_length": 2.31}}, {"type": "null", "usage": 0.01, "schema": null}], "link_flair_css_class": [{"type": "null", "usage": 0.64, "schema": null}, {"type": "string", "usage": 0.36, "schema": {"min_length": 0, "max_length": 63, "avr_length": 1.84}}], "link_flair_richtext": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 15, "avr_length": 0.18, "schema": [{"type": "object", "usage": "always", "schema": {"e": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 5, "avr_length": 4.11, "values": [{"value": "text", "usage": 0.89}, {"value": "emoji", "usage": 0.11}]}}], "t": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 64, "avr_length": 10.28}}], "a": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 26, "avr_length": 9.26}}], "u": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 78, "avr_length": 60.42}}]}}]}}], "link_flair_text": [{"type": "null", "usage": 0.62, "schema": null}, {"type": "string", "usage": 0.38, "schema": {"min_length": 0, "max_length": 64, "avr_length": 11.12}}], "link_flair_text_color": [{"type": "string", "usage": 0.99, "schema": {"min_length": 0, "max_length": 5, "avr_length": 4.17, "values": [{"value": "light", "usage": 0.17}, {"value": "dark", "usage": 0.83}]}}, {"type": "null", "usage": 0.01, "schema": null}], "link_flair_type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 8, "avr_length": 4.65, "values": [{"value": "richtext", "usage": 0.16}, {"value": "text", "usage": 0.84}]}}], "locked": [{"type": "bool", "usage": "always", "schema": {"true": 2141053, "false": 39094929}}], "media": [{"type": "null", "usage": 0.91, "schema": null}, {"type": "object", "usage": 0.09, "schema": {"type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 51, "avr_length": 11.45}}], "oembed": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": 0.99, "schema": {"min_value": 0, "max_value": 13308, "avr_value": 650.35}}, {"type": "null", "usage": 0.01, "schema": null}], "html": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 5045, "avr_length": 254.71}}], "provider_name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 15, "avr_length": 7.04}}], "provider_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 115, "avr_length": 23.76}}], "type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 5, "avr_length": 4.94, "values": [{"value": "video", "usage": 0.94}, {"value": "rich", "usage": 0.06}]}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 600, "avr_value": 525.75}}], "version": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 3, "avr_length": 3.0, "values": [{"value": "1.0", "usage": "always"}]}}], "thumbnail_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 615, "avr_length": 86.07}}], "title": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 684, "avr_length": 25.94}}], "thumbnail_height": [{"type": "int", "usage": 0.99, "schema": {"min_value": 0, "max_value": 4320, "avr_value": 1149.02}}, {"type": "null", "usage": 0.01, "schema": null}], "thumbnail_width": [{"type": "int", "usage": 0.99, "schema": {"min_value": 0, "max_value": 7680, "avr_value": 970.66}}, {"type": "null", "usage": 0.01, "schema": null}], "author_name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 450, "avr_length": 14.02}}], "author_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 186, "avr_length": 38.05}}], "description": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 1155, "avr_length": 103.49}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 1360, "avr_length": 50.83}}], "cache_age": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 3153600000, "avr_value": 3136936747.07}}], "mean_alpha": [{"type": "float", "usage": 0.58, "schema": {"min_value": 0, "max_value": 254.978333333, "avr_value": 115.15}}, {"type": "int", "usage": 0.42, "schema": {"min_value": 0, "max_value": 255, "avr_value": 13.77}}], "marginheight": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 0, "avr_value": 0.0}}], "marginwidth": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 0, "avr_value": 0.0}}]}}], "reddit_video": [{"type": "object", "usage": "always", "schema": {"transcoding_status": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 8.98, "values": [{"value": "completed", "usage": 1.0}, {"value": "error", "usage": 0.0}]}}], "height": [{"type": "int", "usage": 1.0, "schema": {"min_value": 0, "max_value": 1920, "avr_value": 1069.13}}, {"type": "null", "usage": 0.0, "schema": null}], "width": [{"type": "int", "usage": 1.0, "schema": {"min_value": 0, "max_value": 1920, "avr_value": 992.04}}, {"type": "null", "usage": 0.0, "schema": null}], "bitrate_kbps": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 5000, "avr_value": 3007.42}}], "dash_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 165, "avr_length": 165.0}}], "duration": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 900, "avr_value": 45.13}}], "fallback_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 61, "avr_length": 60.39}}], "hls_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 165, "avr_length": 165.0}}], "is_gif": [{"type": "bool", "usage": "always", "schema": {"true": 31751, "false": 1002036}}], "scrubber_media_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 43, "avr_length": 43.0}}], "has_audio": [{"type": "bool", "usage": "always", "schema": {"true": 940015, "false": 92723}}], "transcoding_message": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 82236, "avr_length": 220.67}}]}}], "event_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 13, "avr_length": 13.0}}]}}], "media_embed": [{"type": "object", "usage": "always", "schema": {"content": [{"type": "string", "usage": 1.0, "schema": {"min_length": 0, "max_length": 5045, "avr_length": 254.7}}, {"type": "null", "usage": 0.0, "schema": null}], "height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 13308, "avr_value": 643.39}}], "scrolling": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 2702743}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 710, "avr_value": 525.68}}]}}], "media_only": [{"type": "bool", "usage": "always", "schema": {"true": 624, "false": 41235358}}], "mod_note": [{"type": "null", "usage": "always", "schema": null}], "mod_reason_by": [{"type": "null", "usage": "always", "schema": null}], "mod_reason_title": [{"type": "null", "usage": "always", "schema": null}], "mod_reports": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 10, "avr_length": 10.0}}], "no_follow": [{"type": "bool", "usage": "always", "schema": {"true": 33167238, "false": 8068744}}], "num_comments": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 98672, "avr_value": 5.15}}], "num_crossposts": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 325, "avr_value": 0.02}}], "num_reports": [{"type": "null", "usage": "always", "schema": null}], "over_18": [{"type": "bool", "usage": "always", "schema": {"true": 19634328, "false": 21601654}}], "parent_whitelist_status": [{"type": "null", "usage": 0.64, "schema": null}, {"type": "string", "usage": 0.36, "schema": {"min_length": 0, "max_length": 16, "avr_length": 7.11, "values": [{"value": "all_ads", "usage": 0.89}, {"value": "some_ads", "usage": 0.11}, {"value": "house_only", "usage": 0.0}, {"value": "promo_all", "usage": 0.0}, {"value": "promo_adult_nsfw", "usage": 0.0}]}}], "permalink": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 94, "avr_length": 67.13}}], "pinned": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 41235982}}], "pwls": [{"type": "null", "usage": 0.64, "schema": null}, {"type": "int", "usage": 0.36, "schema": {"min_value": 0, "max_value": 7, "avr_value": 6.11}}], "quarantine": [{"type": "bool", "usage": "always", "schema": {"true": 591, "false": 41235391}}], "removal_reason": [{"type": "null", "usage": 1.0, "schema": null}, {"type": "string", "usage": 0.0, "schema": {"min_length": 0, "max_length": 5, "avr_length": 5.0, "values": [{"value": "legal", "usage": "always"}]}}], "removed_by": [{"type": "null", "usage": "always", "schema": null}], "removed_by_category": [{"type": "null", "usage": 0.81, "schema": null}, {"type": "string", "usage": 0.19, "schema": {"min_length": 0, "max_length": 18, "avr_length": 8.58, "values": [{"value": "moderator", "usage": 0.43}, {"value": "reddit", "usage": 0.41}, {"value": "automod_filtered", "usage": 0.13}, {"value": "deleted", "usage": 0.04}, {"value": "author", "usage": 0.0}, {"value": "content_takedown", "usage": 0.0}, {"value": "copyright_takedown", "usage": 0.0}]}}], "report_reasons": [{"type": "null", "usage": "always", "schema": null}], "retrieved_on": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1706874112, "avr_value": 1705405128.49}}], "saved": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 41235982}}], "score": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 91594, "avr_value": 25.54}}], "secure_media": [{"type": "null", "usage": 0.91, "schema": null}, {"type": "object", "usage": 0.09, "schema": {"type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 51, "avr_length": 11.45}}], "oembed": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": 0.99, "schema": {"min_value": 0, "max_value": 13308, "avr_value": 650.35}}, {"type": "null", "usage": 0.01, "schema": null}], "html": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 5045, "avr_length": 254.71}}], "provider_name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 15, "avr_length": 7.04}}], "provider_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 115, "avr_length": 23.76}}], "type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 5, "avr_length": 4.94, "values": [{"value": "video", "usage": 0.94}, {"value": "rich", "usage": 0.06}]}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 600, "avr_value": 525.75}}], "version": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 3, "avr_length": 3.0, "values": [{"value": "1.0", "usage": "always"}]}}], "thumbnail_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 615, "avr_length": 86.07}}], "title": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 684, "avr_length": 25.94}}], "thumbnail_height": [{"type": "int", "usage": 0.99, "schema": {"min_value": 0, "max_value": 4320, "avr_value": 1149.02}}, {"type": "null", "usage": 0.01, "schema": null}], "thumbnail_width": [{"type": "int", "usage": 0.99, "schema": {"min_value": 0, "max_value": 7680, "avr_value": 970.66}}, {"type": "null", "usage": 0.01, "schema": null}], "author_name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 450, "avr_length": 14.02}}], "author_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 186, "avr_length": 38.05}}], "description": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 1155, "avr_length": 103.49}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 1360, "avr_length": 50.83}}], "cache_age": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 3153600000, "avr_value": 3136936747.07}}], "mean_alpha": [{"type": "float", "usage": 0.58, "schema": {"min_value": 0, "max_value": 254.978333333, "avr_value": 115.15}}, {"type": "int", "usage": 0.42, "schema": {"min_value": 0, "max_value": 255, "avr_value": 13.77}}], "marginheight": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 0, "avr_value": 0.0}}], "marginwidth": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 0, "avr_value": 0.0}}]}}], "reddit_video": [{"type": "object", "usage": "always", "schema": {"transcoding_status": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 8.98, "values": [{"value": "completed", "usage": 1.0}, {"value": "error", "usage": 0.0}]}}], "height": [{"type": "int", "usage": 1.0, "schema": {"min_value": 0, "max_value": 1920, "avr_value": 1069.13}}, {"type": "null", "usage": 0.0, "schema": null}], "width": [{"type": "int", "usage": 1.0, "schema": {"min_value": 0, "max_value": 1920, "avr_value": 992.04}}, {"type": "null", "usage": 0.0, "schema": null}], "bitrate_kbps": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 5000, "avr_value": 3007.42}}], "dash_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 165, "avr_length": 165.0}}], "duration": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 900, "avr_value": 45.13}}], "fallback_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 61, "avr_length": 60.39}}], "hls_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 165, "avr_length": 165.0}}], "is_gif": [{"type": "bool", "usage": "always", "schema": {"true": 31751, "false": 1002036}}], "scrubber_media_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 43, "avr_length": 43.0}}], "has_audio": [{"type": "bool", "usage": "always", "schema": {"true": 940015, "false": 92723}}], "transcoding_message": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 82236, "avr_length": 220.67}}]}}], "event_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 13, "avr_length": 13.0}}]}}], "secure_media_embed": [{"type": "object", "usage": "always", "schema": {"content": [{"type": "string", "usage": 1.0, "schema": {"min_length": 0, "max_length": 5045, "avr_length": 254.7}}, {"type": "null", "usage": 0.0, "schema": null}], "height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 13308, "avr_value": 643.39}}], "media_domain_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 46, "avr_length": 46.0}}], "scrolling": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 2702743}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 710, "avr_value": 525.68}}]}}], "selftext": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 40000, "avr_length": 178.28}}], "send_replies": [{"type": "bool", "usage": "always", "schema": {"true": 38750971, "false": 2485011}}], "spoiler": [{"type": "bool", "usage": "always", "schema": {"true": 409433, "false": 40826549}}], "stickied": [{"type": "bool", "usage": "always", "schema": {"true": 90625, "false": 41145357}}], "subreddit": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 22, "avr_length": 12.02}}], "subreddit_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 8.49}}], "subreddit_name_prefixed": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 23, "avr_length": 13.93}}], "subreddit_subscribers": [{"type": "int", "usage": "always", "schema": {"min_value": -1, "max_value": 56381095, "avr_value": 845130.86}}], "subreddit_type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 10, "avr_length": 6.05, "values": [{"value": "public", "usage": 0.93}, {"value": "restricted", "usage": 0.03}, {"value": "user", "usage": 0.04}, {"value": "private", "usage": 0.0}]}}], "suggested_sort": [{"type": "null", "usage": 0.83, "schema": null}, {"type": "string", "usage": 0.17, "schema": {"min_length": 0, "max_length": 13, "avr_length": 4.68, "values": [{"value": "new", "usage": 0.28}, {"value": "qa", "usage": 0.25}, {"value": "confidence", "usage": 0.27}, {"value": "old", "usage": 0.05}, {"value": "top", "usage": 0.15}, {"value": "live", "usage": 0.0}, {"value": "controversial", "usage": 0.0}, {"value": "random", "usage": 0.0}]}}], "thumbnail": [{"type": "string", "usage": 1.0, "schema": {"min_length": 0, "max_length": 214, "avr_length": 26.05}}, {"type": "null", "usage": 0.0, "schema": null}], "title": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 300, "avr_length": 50.08}}], "top_awarded_type": [{"type": "null", "usage": "always", "schema": null}], "total_awards_received": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 0, "avr_value": 0.0}}], "treatment_tags": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "ups": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 91594, "avr_value": 25.54}}], "upvote_ratio": [{"type": "int", "usage": 0.71, "schema": {"min_value": 0, "max_value": 1, "avr_value": 1.0}}, {"type": "float", "usage": 0.29, "schema": {"min_value": 0, "max_value": 0.99, "avr_value": 0.77}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 2041, "avr_length": 61.98}}], "user_reports": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "view_count": [{"type": "null", "usage": "always", "schema": null}], "visited": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 41235982}}], "whitelist_status": [{"type": "null", "usage": 0.64, "schema": null}, {"type": "string", "usage": 0.36, "schema": {"min_length": 0, "max_length": 16, "avr_length": 7.11, "values": [{"value": "all_ads", "usage": 0.89}, {"value": "some_ads", "usage": 0.11}, {"value": "house_only", "usage": 0.0}, {"value": "promo_all", "usage": 0.0}, {"value": "promo_adult_nsfw", "usage": 0.0}]}}], "wls": [{"type": "null", "usage": 0.64, "schema": null}, {"type": "int", "usage": 0.36, "schema": {"min_value": 0, "max_value": 7, "avr_value": 6.11}}], "thumbnail_height": [{"type": "int", "usage": 0.58, "schema": {"min_value": 0, "max_value": 140, "avr_value": 125.16}}, {"type": "null", "usage": 0.42, "schema": null}], "thumbnail_width": [{"type": "int", "usage": 0.58, "schema": {"min_value": 0, "max_value": 140, "avr_value": 139.95}}, {"type": "null", "usage": 0.42, "schema": null}], "author_flair_richtext": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 21, "avr_length": 0.05, "schema": [{"type": "object", "usage": "always", "schema": {"e": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 5, "avr_length": 4.38, "values": [{"value": "emoji", "usage": 0.38}, {"value": "text", "usage": 0.62}]}}], "t": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 64, "avr_length": 14.76}}], "a": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 26, "avr_length": 9.19}}], "u": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 78, "avr_length": 60.36}}]}}]}}], "author_flair_type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 8, "avr_length": 4.14, "values": [{"value": "richtext", "usage": 0.03}, {"value": "text", "usage": 0.97}]}}], "author_fullname": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 12, "avr_length": 11.2}}], "author_patreon_flair": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 40776061}}], "author_premium": [{"type": "bool", "usage": "always", "schema": {"true": 474269, "false": 40301792}}], "url_overridden_by_dest": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 2041, "avr_length": 43.46}}], "post_hint": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 12, "avr_length": 5.79, "values": [{"value": "self", "usage": 0.05}, {"value": "image", "usage": 0.67}, {"value": "hosted:video", "usage": 0.05}, {"value": "rich:video", "usage": 0.12}, {"value": "link", "usage": 0.11}, {"value": "gallery", "usage": 0.0}, {"value": "video", "usage": 0.0}]}}], "preview": [{"type": "object", "usage": "always", "schema": {"enabled": [{"type": "bool", "usage": "always", "schema": {"true": 14119870, "false": 6876939}}], "images": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 1, "avr_length": 1.0, "schema": [{"type": "object", "usage": "always", "schema": {"id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 85, "avr_length": 43.73}}], "resolutions": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 4.83, "schema": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": -31736, "max_value": 2160, "avr_value": 549.52}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 185, "avr_length": 129.08}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1080, "avr_value": 467.32}}]}}]}}], "source": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": -32768, "max_value": 32481, "avr_value": 1570.08}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 163, "avr_length": 109.94}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 24464, "avr_value": 1342.2}}]}}], "variants": [{"type": "object", "usage": "always", "schema": {"obfuscated": [{"type": "object", "usage": "always", "schema": {"resolutions": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 4.81, "schema": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": -26871, "max_value": 2160, "avr_value": 609.95}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 202, "avr_length": 141.46}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1080, "avr_value": 464.49}}]}}]}}], "source": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": -32768, "max_value": 29143, "avr_value": 1789.5}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 181, "avr_length": 122.46}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 24464, "avr_value": 1389.51}}]}}]}}], "nsfw": [{"type": "object", "usage": "always", "schema": {"resolutions": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 4.81, "schema": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": -26871, "max_value": 2160, "avr_value": 610.95}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 202, "avr_length": 141.4}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1080, "avr_value": 464.43}}]}}]}}], "source": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": -32768, "max_value": 29143, "avr_value": 1792.8}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 181, "avr_length": 122.42}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 24464, "avr_value": 1389.83}}]}}]}}], "gif": [{"type": "object", "usage": "always", "schema": {"resolutions": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 3.8, "schema": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 2160, "avr_value": 491.51}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 162, "avr_length": 107.09}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1080, "avr_value": 357.67}}]}}]}}], "source": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 15000, "avr_value": 1262.26}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 140, "avr_length": 86.18}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 24464, "avr_value": 923.81}}]}}]}}], "mp4": [{"type": "object", "usage": "always", "schema": {"resolutions": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 3.8, "schema": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 2160, "avr_value": 491.51}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 162, "avr_length": 107.09}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1080, "avr_value": 357.67}}]}}]}}], "source": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 15000, "avr_value": 1262.26}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 151, "avr_length": 97.18}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 24464, "avr_value": 923.81}}]}}]}}]}}]}}]}}], "reddit_video_preview": [{"type": "object", "usage": "always", "schema": {"dash_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 48, "avr_length": 48.0}}], "duration": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1509, "avr_value": 17.64}}], "fallback_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 45, "avr_length": 44.72}}], "height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1920, "avr_value": 1400.37}}], "hls_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 48, "avr_length": 48.0}}], "is_gif": [{"type": "bool", "usage": "always", "schema": {"true": 1839475, "false": 0}}], "scrubber_media_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 43, "avr_length": 43.0}}], "transcoding_status": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 9.0, "values": [{"value": "completed", "usage": "always"}]}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1920, "avr_value": 1088.75}}], "bitrate_kbps": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 5000, "avr_value": 4000.99}}]}}]}}], "link_flair_template_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 36, "avr_length": 36.0}}], "media_metadata": [{"type": "object", "usage": 0.86, "schema": {"type": "key-value", "schema": [{"type": "object", "usage": "always", "schema": {"status": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 11, "avr_length": 5.01, "values": [{"value": "valid", "usage": 1.0}, {"value": "unprocessed", "usage": 0.0}, {"value": "failed", "usage": 0.0}]}}], "e": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 13, "avr_length": 5.11, "values": [{"value": "Image", "usage": 0.99}, {"value": "AnimatedImage", "usage": 0.01}, {"value": "RedditVideo", "usage": 0.0}]}}], "id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 13, "avr_length": 13.0}}], "m": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 9.0, "values": [{"value": "image/jpg", "usage": 0.88}, {"value": "image/gif", "usage": 0.01}, {"value": "image/png", "usage": 0.11}]}}], "p": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 5.27, "schema": [{"type": "object", "usage": "always", "schema": {"u": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 118, "avr_length": 115.14}}], "x": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1080, "avr_value": 497.87}}], "y": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 2160, "avr_value": 644.22}}]}}]}}], "s": [{"type": "object", "usage": "always", "schema": {"x": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 16384, "avr_value": 1623.93}}], "y": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 58460, "avr_value": 1994.43}}], "u": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 118, "avr_length": 116.6}}], "gif": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 35, "avr_length": 35.0}}], "mp4": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 95, "avr_length": 95.0}}]}}], "o": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 1, "avr_length": 1.0, "schema": [{"type": "object", "usage": "always", "schema": {"u": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 125, "avr_length": 124.3}}], "x": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 16384, "avr_value": 1356.97}}], "y": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 52403, "avr_value": 1756.5}}]}}]}}], "dashUrl": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 184, "avr_length": 184.0}}], "hlsUrl": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 184, "avr_length": 184.0}}], "isGif": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 20922}}], "x": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1920, "avr_value": 1242.51}}], "y": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1920, "avr_value": 978.78}}]}}]}}, {"type": "null", "usage": 0.14, "schema": null}], "gallery_data": [{"type": "object", "usage": 0.86, "schema": {"items": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 20, "avr_length": 4.43, "schema": [{"type": "object", "usage": "always", "schema": {"id": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 398268769, "avr_value": 390242763.36}}], "media_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 13, "avr_length": 13.0}}], "caption": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 181, "avr_length": 46.62}}], "outbound_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 844, "avr_length": 73.74}}], "display_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 145, "avr_length": 19.06}}], "call_to_action": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 13, "avr_length": 8.28, "values": [{"value": "Shop Now", "usage": 0.78}, {"value": "View More", "usage": 0.02}, {"value": "Sign Up", "usage": 0.03}, {"value": "Order Now", "usage": 0.01}, {"value": "Download", "usage": 0.01}, {"value": "Watch Now", "usage": 0.01}, {"value": "Learn More", "usage": 0.11}, {"value": "Contact Us", "usage": 0.0}, {"value": "Install", "usage": 0.01}, {"value": "Get a Quote", "usage": 0.0}, {"value": "Get Showtimes", "usage": 0.0}, {"value": "Apply Now", "usage": 0.0}, {"value": "Play Now", "usage": 0.0}, {"value": "Pre-order Now", "usage": 0.0}, {"value": "See <PERSON><PERSON>", "usage": 0.0}]}}], "product": [{"type": "object", "usage": "always", "schema": {"description": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 4739, "avr_length": 763.3}}], "title": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 138, "avr_length": 49.73}}], "price": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 7.51}}]}}]}}]}}]}}, {"type": "null", "usage": 0.14, "schema": null}], "is_gallery": [{"type": "bool", "usage": "always", "schema": {"true": 3610176, "false": 0}}], "crosspost_parent": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 10, "avr_length": 9.97}}], "crosspost_parent_list": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 1, "avr_length": 1.0, "schema": [{"type": "object", "usage": "always", "schema": {"all_awardings": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "allow_live_comments": [{"type": "bool", "usage": "always", "schema": {"true": 65898, "false": 1324100}}], "approved_at_utc": [{"type": "null", "usage": "always", "schema": null}], "approved_by": [{"type": "null", "usage": "always", "schema": null}], "archived": [{"type": "bool", "usage": "always", "schema": {"true": 17567, "false": 1372431}}], "author": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 20, "avr_length": 12.8}}], "author_flair_background_color": [{"type": "null", "usage": 0.88, "schema": null}, {"type": "string", "usage": 0.12, "schema": {"min_length": 0, "max_length": 11, "avr_length": 6.01}}], "author_flair_css_class": [{"type": "null", "usage": 0.96, "schema": null}, {"type": "string", "usage": 0.04, "schema": {"min_length": 0, "max_length": 78, "avr_length": 7.26}}], "author_flair_template_id": [{"type": "null", "usage": 0.9, "schema": null}, {"type": "string", "usage": 0.1, "schema": {"min_length": 0, "max_length": 36, "avr_length": 36.0}}], "author_flair_text": [{"type": "null", "usage": 0.88, "schema": null}, {"type": "string", "usage": 0.12, "schema": {"min_length": 0, "max_length": 64, "avr_length": 15.24}}], "author_flair_text_color": [{"type": "null", "usage": 0.87, "schema": null}, {"type": "string", "usage": 0.13, "schema": {"min_length": 0, "max_length": 5, "avr_length": 4.21, "values": [{"value": "dark", "usage": 0.7}, {"value": "light", "usage": 0.28}, {"value": "", "usage": 0.02}]}}], "author_is_blocked": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 1389998}}], "awarders": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "banned_at_utc": [{"type": "null", "usage": "always", "schema": null}], "banned_by": [{"type": "null", "usage": "always", "schema": null}], "can_gild": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 1389998}}], "can_mod_post": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 1389998}}], "category": [{"type": "null", "usage": 1.0, "schema": null}, {"type": "string", "usage": 0.0, "schema": {"min_length": 0, "max_length": 20, "avr_length": 1.39, "values": [{"value": "", "usage": 0.9}, {"value": "diy_and_crafts", "usage": 0.09}, {"value": "drawing_and_painting", "usage": 0.01}, {"value": "videos", "usage": 0.0}, {"value": "food", "usage": 0.0}, {"value": "gaming", "usage": 0.0}]}}], "clicked": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 1389998}}], "content_categories": [{"type": "null", "usage": 0.99, "schema": null}, {"type": "array", "usage": 0.01, "schema": {"min_length": 0, "max_length": 1, "avr_length": 1.0, "schema": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 20, "avr_length": 11.16, "values": [{"value": "drawing_and_painting", "usage": 0.17}, {"value": "photography", "usage": 0.28}, {"value": "entertainment", "usage": 0.07}, {"value": "comics", "usage": 0.13}, {"value": "gaming", "usage": 0.08}, {"value": "diy_and_crafts", "usage": 0.09}, {"value": "writing", "usage": 0.15}, {"value": "music", "usage": 0.01}, {"value": "videos", "usage": 0.02}]}}]}}], "contest_mode": [{"type": "bool", "usage": "always", "schema": {"true": 1103, "false": 1388895}}], "created": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1706745503, "avr_value": 1701600074.37}}], "created_utc": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1706745503, "avr_value": 1701600074.37}}], "discussion_type": [{"type": "null", "usage": 1.0, "schema": null}, {"type": "string", "usage": 0.0, "schema": {"min_length": 0, "max_length": 4, "avr_length": 4.0, "values": [{"value": "CHAT", "usage": "always"}]}}], "distinguished": [{"type": "null", "usage": 0.99, "schema": null}, {"type": "string", "usage": 0.01, "schema": {"min_length": 0, "max_length": 9, "avr_length": 8.98, "values": [{"value": "moderator", "usage": 0.99}, {"value": "admin", "usage": 0.01}]}}], "domain": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 91, "avr_length": 11.02}}], "downs": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 0, "avr_value": 0.0}}], "edited": [{"type": "bool", "usage": 0.99, "schema": {"true": 0, "false": 1370748}}, {"type": "int", "usage": 0.01, "schema": {"min_value": 0, "max_value": 1706744476, "avr_value": 1698455132.04}}], "gilded": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 71, "avr_value": 0.0}}], "gildings": [{"type": "object", "usage": "always", "schema": {}}], "hidden": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 1389998}}], "hide_score": [{"type": "bool", "usage": "always", "schema": {"true": 471890, "false": 918108}}], "id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 7, "avr_length": 6.97}}], "is_created_from_ads_ui": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 1389998}}], "is_crosspostable": [{"type": "bool", "usage": "always", "schema": {"true": 1306365, "false": 83633}}], "is_meta": [{"type": "bool", "usage": "always", "schema": {"true": 1, "false": 1389997}}], "is_original_content": [{"type": "bool", "usage": "always", "schema": {"true": 20679, "false": 1369319}}], "is_reddit_media_domain": [{"type": "bool", "usage": "always", "schema": {"true": 583109, "false": 806889}}], "is_robot_indexable": [{"type": "bool", "usage": "always", "schema": {"true": 1388767, "false": 1231}}], "is_self": [{"type": "bool", "usage": "always", "schema": {"true": 182121, "false": 1207877}}], "is_video": [{"type": "bool", "usage": "always", "schema": {"true": 114915, "false": 1275083}}], "likes": [{"type": "null", "usage": "always", "schema": null}], "link_flair_background_color": [{"type": "string", "usage": 0.98, "schema": {"min_length": 0, "max_length": 11, "avr_length": 2.24}}, {"type": "null", "usage": 0.02, "schema": null}], "link_flair_css_class": [{"type": "null", "usage": 0.64, "schema": null}, {"type": "string", "usage": 0.36, "schema": {"min_length": 0, "max_length": 46, "avr_length": 1.57}}], "link_flair_richtext": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 12, "avr_length": 0.17, "schema": [{"type": "object", "usage": "always", "schema": {"e": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 5, "avr_length": 4.14, "values": [{"value": "text", "usage": 0.86}, {"value": "emoji", "usage": 0.14}]}}], "t": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 64, "avr_length": 10.33}}], "a": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 26, "avr_length": 9.21}}], "u": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 78, "avr_length": 60.45}}]}}]}}], "link_flair_text": [{"type": "null", "usage": 0.63, "schema": null}, {"type": "string", "usage": 0.37, "schema": {"min_length": 0, "max_length": 64, "avr_length": 10.99}}], "link_flair_text_color": [{"type": "string", "usage": 0.98, "schema": {"min_length": 0, "max_length": 5, "avr_length": 4.15, "values": [{"value": "dark", "usage": 0.85}, {"value": "light", "usage": 0.15}]}}, {"type": "null", "usage": 0.02, "schema": null}], "link_flair_type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 8, "avr_length": 4.58, "values": [{"value": "text", "usage": 0.85}, {"value": "richtext", "usage": 0.15}]}}], "locked": [{"type": "bool", "usage": "always", "schema": {"true": 25113, "false": 1364885}}], "media": [{"type": "null", "usage": 0.69, "schema": null}, {"type": "object", "usage": 0.31, "schema": {"type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 38, "avr_length": 11.86}}], "oembed": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": 0.98, "schema": {"min_value": 0, "max_value": 13308, "avr_value": 666.09}}, {"type": "null", "usage": 0.02, "schema": null}], "html": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 5045, "avr_length": 204.23}}], "provider_name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 14, "avr_length": 7.0}}], "provider_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 115, "avr_length": 23.86}}], "type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 5, "avr_length": 4.98, "values": [{"value": "video", "usage": 0.98}, {"value": "rich", "usage": 0.02}]}}], "version": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 3, "avr_length": 3.0, "values": [{"value": "1.0", "usage": "always"}]}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 600, "avr_value": 575.69}}], "title": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 153, "avr_length": 17.9}}], "thumbnail_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 598, "avr_length": 96.9}}], "thumbnail_height": [{"type": "int", "usage": 0.97, "schema": {"min_value": 0, "max_value": 3840, "avr_value": 1194.15}}, {"type": "null", "usage": 0.03, "schema": null}], "thumbnail_width": [{"type": "int", "usage": 0.97, "schema": {"min_value": 0, "max_value": 3884, "avr_value": 1175.54}}, {"type": "null", "usage": 0.03, "schema": null}], "author_name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 130, "avr_length": 13.89}}], "author_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 89, "avr_length": 37.79}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 163, "avr_length": 55.71}}], "cache_age": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 3153600000, "avr_value": 3152933436.14}}], "description": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 704, "avr_length": 126.13}}], "mean_alpha": [{"type": "float", "usage": 0.89, "schema": {"min_value": 0, "max_value": 254.805, "avr_value": 128.15}}, {"type": "int", "usage": 0.11, "schema": {"min_value": 0, "max_value": 255, "avr_value": 86.0}}]}}], "reddit_video": [{"type": "object", "usage": "always", "schema": {"transcoding_status": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 9.0, "values": [{"value": "completed", "usage": 1.0}, {"value": "error", "usage": 0.0}]}}], "height": [{"type": "int", "usage": 1.0, "schema": {"min_value": 0, "max_value": 1920, "avr_value": 992.81}}, {"type": "null", "usage": 0.0, "schema": null}], "width": [{"type": "int", "usage": 1.0, "schema": {"min_value": 0, "max_value": 1920, "avr_value": 830.7}}, {"type": "null", "usage": 0.0, "schema": null}], "dash_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 165, "avr_length": 165.0}}], "duration": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1776, "avr_value": 63.69}}], "fallback_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 172, "avr_length": 60.31}}], "hls_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 165, "avr_length": 165.0}}], "is_gif": [{"type": "bool", "usage": "always", "schema": {"true": 2786, "false": 111862}}], "scrubber_media_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 43, "avr_length": 42.99}}], "bitrate_kbps": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 5000, "avr_value": 2535.04}}], "has_audio": [{"type": "bool", "usage": "always", "schema": {"true": 103200, "false": 7732}}], "transcoding_message": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 78, "avr_length": 72.6}}]}}], "event_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 13, "avr_length": 13.0, "values": [{"value": "18hnzysb1elcs", "usage": "always"}]}}]}}], "media_embed": [{"type": "object", "usage": "always", "schema": {"content": [{"type": "string", "usage": 1.0, "schema": {"min_length": 0, "max_length": 5045, "avr_length": 204.23}}, {"type": "null", "usage": 0.0, "schema": null}], "height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 13308, "avr_value": 659.01}}], "scrolling": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 312218}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 710, "avr_value": 575.68}}]}}], "media_only": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 1389998}}], "mod_note": [{"type": "null", "usage": "always", "schema": null}], "mod_reason_by": [{"type": "null", "usage": "always", "schema": null}], "mod_reason_title": [{"type": "null", "usage": "always", "schema": null}], "mod_reports": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 10, "avr_length": 9.97}}], "no_follow": [{"type": "bool", "usage": "always", "schema": {"true": 414617, "false": 975381}}], "num_comments": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 79453, "avr_value": 63.12}}], "num_crossposts": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 2452, "avr_value": 6.17}}], "num_reports": [{"type": "null", "usage": "always", "schema": null}], "over_18": [{"type": "bool", "usage": "always", "schema": {"true": 814882, "false": 575116}}], "parent_whitelist_status": [{"type": "null", "usage": 0.72, "schema": null}, {"type": "string", "usage": 0.28, "schema": {"min_length": 0, "max_length": 10, "avr_length": 7.17, "values": [{"value": "all_ads", "usage": 0.83}, {"value": "some_ads", "usage": 0.17}, {"value": "house_only", "usage": 0.0}]}}], "permalink": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 94, "avr_length": 66.22}}], "pinned": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 1389998}}], "pwls": [{"type": "null", "usage": 0.72, "schema": null}, {"type": "int", "usage": 0.28, "schema": {"min_value": 0, "max_value": 7, "avr_value": 6.17}}], "quarantine": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 1389998}}], "removal_reason": [{"type": "null", "usage": 1.0, "schema": null}, {"type": "string", "usage": 0.0, "schema": {"min_length": 0, "max_length": 5, "avr_length": 5.0, "values": [{"value": "legal", "usage": "always"}]}}], "removed_by": [{"type": "null", "usage": "always", "schema": null}], "removed_by_category": [{"type": "null", "usage": 1.0, "schema": null}, {"type": "string", "usage": 0.0, "schema": {"min_length": 0, "max_length": 18, "avr_length": 8.01, "values": [{"value": "reddit", "usage": 0.27}, {"value": "moderator", "usage": 0.31}, {"value": "copyright_takedown", "usage": 0.04}, {"value": "deleted", "usage": 0.33}, {"value": "content_takedown", "usage": 0.02}, {"value": "automod_filtered", "usage": 0.01}, {"value": "author", "usage": 0.02}]}}], "report_reasons": [{"type": "null", "usage": "always", "schema": null}], "saved": [{"type": "bool", "usage": "always", "schema": {"true": 2, "false": 1389996}}], "score": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 621648, "avr_value": 927.01}}], "secure_media": [{"type": "null", "usage": 0.69, "schema": null}, {"type": "object", "usage": 0.31, "schema": {"type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 38, "avr_length": 11.86}}], "oembed": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": 0.98, "schema": {"min_value": 0, "max_value": 13308, "avr_value": 666.09}}, {"type": "null", "usage": 0.02, "schema": null}], "html": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 5045, "avr_length": 204.23}}], "provider_name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 14, "avr_length": 7.0}}], "provider_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 115, "avr_length": 23.86}}], "type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 5, "avr_length": 4.98, "values": [{"value": "video", "usage": 0.98}, {"value": "rich", "usage": 0.02}]}}], "version": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 3, "avr_length": 3.0, "values": [{"value": "1.0", "usage": "always"}]}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 600, "avr_value": 575.69}}], "title": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 153, "avr_length": 17.9}}], "thumbnail_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 598, "avr_length": 96.9}}], "thumbnail_height": [{"type": "int", "usage": 0.97, "schema": {"min_value": 0, "max_value": 3840, "avr_value": 1194.15}}, {"type": "null", "usage": 0.03, "schema": null}], "thumbnail_width": [{"type": "int", "usage": 0.97, "schema": {"min_value": 0, "max_value": 3884, "avr_value": 1175.54}}, {"type": "null", "usage": 0.03, "schema": null}], "author_name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 130, "avr_length": 13.89}}], "author_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 89, "avr_length": 37.79}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 163, "avr_length": 55.71}}], "cache_age": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 3153600000, "avr_value": 3152933436.14}}], "description": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 704, "avr_length": 126.13}}], "mean_alpha": [{"type": "float", "usage": 0.89, "schema": {"min_value": 0, "max_value": 254.805, "avr_value": 128.15}}, {"type": "int", "usage": 0.11, "schema": {"min_value": 0, "max_value": 255, "avr_value": 86.0}}]}}], "reddit_video": [{"type": "object", "usage": "always", "schema": {"transcoding_status": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 9.0, "values": [{"value": "completed", "usage": 1.0}, {"value": "error", "usage": 0.0}]}}], "height": [{"type": "int", "usage": 1.0, "schema": {"min_value": 0, "max_value": 1920, "avr_value": 992.81}}, {"type": "null", "usage": 0.0, "schema": null}], "width": [{"type": "int", "usage": 1.0, "schema": {"min_value": 0, "max_value": 1920, "avr_value": 830.7}}, {"type": "null", "usage": 0.0, "schema": null}], "dash_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 165, "avr_length": 165.0}}], "duration": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1776, "avr_value": 63.69}}], "fallback_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 172, "avr_length": 60.31}}], "hls_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 165, "avr_length": 165.0}}], "is_gif": [{"type": "bool", "usage": "always", "schema": {"true": 2786, "false": 111862}}], "scrubber_media_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 43, "avr_length": 42.99}}], "bitrate_kbps": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 5000, "avr_value": 2535.04}}], "has_audio": [{"type": "bool", "usage": "always", "schema": {"true": 103200, "false": 7732}}], "transcoding_message": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 78, "avr_length": 72.6}}]}}], "event_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 13, "avr_length": 13.0, "values": [{"value": "18hnzysb1elcs", "usage": "always"}]}}]}}], "secure_media_embed": [{"type": "object", "usage": "always", "schema": {"content": [{"type": "string", "usage": 1.0, "schema": {"min_length": 0, "max_length": 5045, "avr_length": 204.23}}, {"type": "null", "usage": 0.0, "schema": null}], "height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 13308, "avr_value": 659.01}}], "media_domain_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 46, "avr_length": 45.95}}], "scrolling": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 312218}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 710, "avr_value": 575.68}}]}}], "selftext": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 39999, "avr_length": 183.28}}], "selftext_html": [{"type": "null", "usage": 0.79, "schema": null}, {"type": "string", "usage": 0.21, "schema": {"min_length": 0, "max_length": 148364, "avr_length": 1037.34}}], "send_replies": [{"type": "bool", "usage": "always", "schema": {"true": 1309203, "false": 80795}}], "spoiler": [{"type": "bool", "usage": "always", "schema": {"true": 7239, "false": 1382759}}], "stickied": [{"type": "bool", "usage": "always", "schema": {"true": 18265, "false": 1371733}}], "subreddit": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 22, "avr_length": 12.09}}], "subreddit_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 8.48}}], "subreddit_name_prefixed": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 23, "avr_length": 13.96}}], "subreddit_subscribers": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 273691929, "avr_value": 1225436.67}}], "subreddit_type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 10, "avr_length": 6.03, "values": [{"value": "public", "usage": 0.89}, {"value": "user", "usage": 0.07}, {"value": "restricted", "usage": 0.04}, {"value": "archived", "usage": 0.0}]}}], "suggested_sort": [{"type": "null", "usage": 0.82, "schema": null}, {"type": "string", "usage": 0.18, "schema": {"min_length": 0, "max_length": 13, "avr_length": 4.39, "values": [{"value": "new", "usage": 0.16}, {"value": "qa", "usage": 0.38}, {"value": "confidence", "usage": 0.25}, {"value": "top", "usage": 0.18}, {"value": "old", "usage": 0.03}, {"value": "live", "usage": 0.0}, {"value": "controversial", "usage": 0.0}, {"value": "random", "usage": 0.0}]}}], "thumbnail": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 214, "avr_length": 36.88}}], "thumbnail_height": [{"type": "int", "usage": 0.87, "schema": {"min_value": 0, "max_value": 140, "avr_value": 122.33}}, {"type": "null", "usage": 0.13, "schema": null}], "thumbnail_width": [{"type": "int", "usage": 0.87, "schema": {"min_value": 0, "max_value": 140, "avr_value": 139.97}}, {"type": "null", "usage": 0.13, "schema": null}], "title": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 300, "avr_length": 47.87}}], "top_awarded_type": [{"type": "null", "usage": "always", "schema": null}], "total_awards_received": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 0, "avr_value": 0.0}}], "treatment_tags": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "ups": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 621648, "avr_value": 927.01}}], "upvote_ratio": [{"type": "int", "usage": 0.52, "schema": {"min_value": 0, "max_value": 1, "avr_value": 1.0}}, {"type": "float", "usage": 0.48, "schema": {"min_value": 0, "max_value": 0.99, "avr_value": 0.91}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 1475, "avr_length": 49.76}}], "user_reports": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "view_count": [{"type": "null", "usage": "always", "schema": null}], "visited": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 1389998}}], "whitelist_status": [{"type": "null", "usage": 0.72, "schema": null}, {"type": "string", "usage": 0.28, "schema": {"min_length": 0, "max_length": 10, "avr_length": 7.17, "values": [{"value": "all_ads", "usage": 0.83}, {"value": "some_ads", "usage": 0.17}, {"value": "house_only", "usage": 0.0}]}}], "wls": [{"type": "null", "usage": 0.72, "schema": null}, {"type": "int", "usage": 0.28, "schema": {"min_value": 0, "max_value": 7, "avr_value": 6.17}}], "author_flair_richtext": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 16, "avr_length": 0.09, "schema": [{"type": "object", "usage": "always", "schema": {"e": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 5, "avr_length": 4.38, "values": [{"value": "emoji", "usage": 0.38}, {"value": "text", "usage": 0.62}]}}], "t": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 64, "avr_length": 13.71}}], "a": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 26, "avr_length": 9.79}}], "u": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 78, "avr_length": 60.95}}]}}]}}], "author_flair_type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 8, "avr_length": 4.23, "values": [{"value": "text", "usage": 0.94}, {"value": "richtext", "usage": 0.06}]}}], "author_fullname": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 12, "avr_length": 11.04}}], "author_patreon_flair": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 1378054}}], "author_premium": [{"type": "bool", "usage": "always", "schema": {"true": 66106, "false": 1311948}}], "url_overridden_by_dest": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 1475, "avr_length": 43.26}}], "post_hint": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 12, "avr_length": 7.15, "values": [{"value": "rich:video", "usage": 0.3}, {"value": "hosted:video", "usage": 0.11}, {"value": "image", "usage": 0.48}, {"value": "link", "usage": 0.09}, {"value": "self", "usage": 0.03}, {"value": "video", "usage": 0.0}, {"value": "gallery", "usage": 0.0}]}}], "preview": [{"type": "object", "usage": "always", "schema": {"enabled": [{"type": "bool", "usage": "always", "schema": {"true": 492941, "false": 537149}}], "images": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 1, "avr_length": 1.0, "schema": [{"type": "object", "usage": "always", "schema": {"id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 60, "avr_length": 44.77}}], "resolutions": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 4.39, "schema": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 2160, "avr_value": 472.14}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 185, "avr_length": 137.8}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1080, "avr_value": 421.84}}]}}]}}], "source": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 28966, "avr_value": 1221.84}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 163, "avr_length": 118.86}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 16320, "avr_value": 1086.22}}]}}], "variants": [{"type": "object", "usage": "always", "schema": {"obfuscated": [{"type": "object", "usage": "always", "schema": {"resolutions": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 4.2, "schema": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 2160, "avr_value": 469.29}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 193, "avr_length": 152.1}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1080, "avr_value": 399.19}}]}}]}}], "source": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 22489, "avr_value": 1218.49}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 171, "avr_length": 133.7}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 16320, "avr_value": 1034.87}}]}}]}}], "nsfw": [{"type": "object", "usage": "always", "schema": {"resolutions": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 4.19, "schema": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 2160, "avr_value": 469.26}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 193, "avr_length": 152.08}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1080, "avr_value": 398.91}}]}}]}}], "source": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 22489, "avr_value": 1217.86}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 171, "avr_length": 133.69}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 16320, "avr_value": 1033.85}}]}}]}}], "gif": [{"type": "object", "usage": "always", "schema": {"resolutions": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 3.37, "schema": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 2160, "avr_value": 329.2}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 145, "avr_length": 107.29}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1080, "avr_value": 286.47}}]}}]}}], "source": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 4825, "avr_value": 705.44}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 123, "avr_length": 86.3}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 5184, "avr_value": 590.21}}]}}]}}], "mp4": [{"type": "object", "usage": "always", "schema": {"resolutions": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 3.37, "schema": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 2160, "avr_value": 329.2}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 145, "avr_length": 107.29}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1080, "avr_value": 286.47}}]}}]}}], "source": [{"type": "object", "usage": "always", "schema": {"height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 4825, "avr_value": 705.44}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 134, "avr_length": 97.3}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 5184, "avr_value": 590.21}}]}}]}}]}}]}}]}}], "reddit_video_preview": [{"type": "object", "usage": "always", "schema": {"dash_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 48, "avr_length": 48.0}}], "duration": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 131, "avr_value": 29.79}}], "fallback_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 45, "avr_length": 44.67}}], "height": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1920, "avr_value": 1187.32}}], "hls_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 48, "avr_length": 48.0}}], "is_gif": [{"type": "bool", "usage": "always", "schema": {"true": 316678, "false": 0}}], "scrubber_media_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 43, "avr_length": 43.0}}], "transcoding_status": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 9.0, "values": [{"value": "completed", "usage": "always"}]}}], "width": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1920, "avr_value": 1184.58}}], "bitrate_kbps": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 5000, "avr_value": 3798.11}}]}}]}}], "link_flair_template_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 36, "avr_length": 36.0}}], "media_metadata": [{"type": "object", "usage": 1.0, "schema": {"type": "key-value", "schema": [{"type": "object", "usage": "always", "schema": {"status": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 11, "avr_length": 5.0, "values": [{"value": "valid", "usage": 1.0}, {"value": "failed", "usage": 0.0}, {"value": "unprocessed", "usage": 0.0}]}}], "e": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 13, "avr_length": 5.39, "values": [{"value": "Image", "usage": 0.95}, {"value": "AnimatedImage", "usage": 0.05}, {"value": "RedditVideo", "usage": 0.0}]}}], "id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 13, "avr_length": 13.0}}], "m": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 9.0, "values": [{"value": "image/jpg", "usage": 0.84}, {"value": "image/png", "usage": 0.11}, {"value": "image/gif", "usage": 0.05}]}}], "p": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 5.07, "schema": [{"type": "object", "usage": "always", "schema": {"u": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 118, "avr_length": 115.17}}], "x": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1080, "avr_value": 479.45}}], "y": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 2160, "avr_value": 598.98}}]}}]}}], "s": [{"type": "object", "usage": "always", "schema": {"x": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 16000, "avr_value": 1429.06}}], "y": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 36704, "avr_value": 1730.54}}], "u": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 118, "avr_length": 116.54}}], "gif": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 35, "avr_length": 35.0}}], "mp4": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 95, "avr_length": 95.0}}]}}], "o": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 1, "avr_length": 1.0, "schema": [{"type": "object", "usage": "always", "schema": {"u": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 125, "avr_length": 123.86}}], "x": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 14204, "avr_value": 1283.14}}], "y": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 21600, "avr_value": 1628.65}}]}}]}}], "dashUrl": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 184, "avr_length": 183.99}}], "hlsUrl": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 184, "avr_length": 183.99}}], "isGif": [{"type": "bool", "usage": "always", "schema": {"true": 3, "false": 1161}}], "x": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1920, "avr_value": 1244.26}}], "y": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1920, "avr_value": 995.44}}]}}]}}, {"type": "null", "usage": 0.0, "schema": null}], "gallery_data": [{"type": "object", "usage": 1.0, "schema": {"items": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 20, "avr_length": 6.35, "schema": [{"type": "object", "usage": "always", "schema": {"id": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 398268054, "avr_value": 380188800.47}}], "media_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 13, "avr_length": 13.0}}], "caption": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 180, "avr_length": 46.49}}], "outbound_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 839, "avr_length": 50.26}}]}}]}}]}}, {"type": "null", "usage": 0.0, "schema": null}], "is_gallery": [{"type": "bool", "usage": "always", "schema": {"true": 195020, "false": 0}}], "author_cakeday": [{"type": "bool", "usage": "always", "schema": {"true": 5939, "false": 0}}], "poll_data": [{"type": "object", "usage": 1.0, "schema": {"is_prediction": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 2266}}], "options": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 3.71, "schema": [{"type": "object", "usage": "always", "schema": {"id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 8, "avr_length": 8.0}}], "text": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 120, "avr_length": 20.2}}], "vote_count": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 2580, "avr_value": 124.04}}]}}]}}], "prediction_status": [{"type": "null", "usage": "always", "schema": null}], "resolved_option_id": [{"type": "null", "usage": "always", "schema": null}], "total_stake_amount": [{"type": "null", "usage": "always", "schema": null}], "total_vote_count": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 4668, "avr_value": 64.66}}], "tournament_id": [{"type": "null", "usage": "always", "schema": null}], "user_selection": [{"type": "null", "usage": "always", "schema": null}], "user_won_amount": [{"type": "null", "usage": "always", "schema": null}], "vote_updates_remained": [{"type": "null", "usage": "always", "schema": null}], "voting_end_timestamp": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1707334673650, "avr_value": 1705236803685.16}}]}}, {"type": "null", "usage": 0.0, "schema": null}], "collections": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 1, "avr_length": 1.0, "schema": [{"type": "object", "usage": "always", "schema": {"author_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 12, "avr_length": 11.21}}], "author_name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 20, "avr_length": 12.18}}], "collection_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 36, "avr_length": 36.0}}], "created_at_utc": [{"type": "float", "usage": "always", "schema": {"min_value": 0, "max_value": 1706741083.2, "avr_value": 1693624679.71}}], "description": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 458, "avr_length": 14.31}}], "display_layout": [{"type": "null", "usage": 0.95, "schema": null}, {"type": "string", "usage": 0.05, "schema": {"min_length": 0, "max_length": 8, "avr_length": 7.15, "values": [{"value": "GALLERY", "usage": 0.85}, {"value": "TIMELINE", "usage": 0.15}]}}], "last_update_utc": [{"type": "float", "usage": "always", "schema": {"min_value": 0, "max_value": 1706741089.4, "avr_value": 1703346260.27}}], "link_ids": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 100, "avr_length": 17.42, "schema": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 10, "avr_length": 9.81}}]}}], "permalink": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 94, "avr_length": 85.64}}], "subreddit_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 8.9}}], "title": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 82, "avr_length": 19.19}}], "sr_detail": [{"type": "object", "usage": "always", "schema": {"banner_img": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "values": [{"value": "", "usage": "always"}]}}], "banner_size": [{"type": "null", "usage": "always", "schema": null}], "community_icon": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 132, "avr_length": 132.0}}], "created": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1672530785, "avr_value": 1662009584.5}}], "created_utc": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1672530785, "avr_value": 1662009584.5}}], "display_name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 8.25, "values": [{"value": "workcules", "usage": 0.5}, {"value": "vktoons", "usage": 0.25}, {"value": "<PERSON><PERSON><PERSON>", "usage": 0.25}]}}], "display_name_prefixed": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 11, "avr_length": 10.25, "values": [{"value": "r/workcules", "usage": 0.5}, {"value": "r/vktoons", "usage": 0.25}, {"value": "r/lovehair", "usage": 0.25}]}}], "icon_img": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "values": [{"value": "", "usage": "always"}]}}], "icon_size": [{"type": "null", "usage": "always", "schema": null}], "key_color": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "values": [{"value": "", "usage": "always"}]}}], "mod_permissions": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 9.0, "values": [{"value": "t5_7ovi2c", "usage": 0.5}, {"value": "t5_65xl5n", "usage": 0.25}, {"value": "t5_6g68ad", "usage": 0.25}]}}], "over_18": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 4}}], "primary_color": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 7, "avr_length": 3.5, "values": [{"value": "#000000", "usage": 0.5}, {"value": "", "usage": 0.5}]}}], "sr": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 8.25, "values": [{"value": "workcules", "usage": 0.5}, {"value": "vktoons", "usage": 0.25}, {"value": "<PERSON><PERSON><PERSON>", "usage": 0.25}]}}], "sr_display_name_prefixed": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 11, "avr_length": 10.25, "values": [{"value": "r/workcules", "usage": 0.5}, {"value": "r/vktoons", "usage": 0.25}, {"value": "r/lovehair", "usage": 0.25}]}}], "subreddit_type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 10, "avr_length": 9.0, "values": [{"value": "restricted", "usage": 0.75}, {"value": "public", "usage": 0.25}]}}], "subscribers": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 30732, "avr_value": 7700.5}}], "title": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 8.25, "values": [{"value": "Workcules", "usage": 0.5}, {"value": "vktoons", "usage": 0.25}, {"value": "<PERSON><PERSON><PERSON>", "usage": 0.25}]}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 13, "avr_length": 12.25, "values": [{"value": "/r/workcules/", "usage": 0.5}, {"value": "/r/vktoons/", "usage": 0.25}, {"value": "/r/lovehair/", "usage": 0.25}]}}], "user_can_crosspost": [{"type": "bool", "usage": "always", "schema": {"true": 4, "false": 0}}], "whitelist_status": [{"type": "null", "usage": "always", "schema": null}]}}]}}]}}], "call_to_action": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "values": [{"value": "", "usage": "always"}]}}], "event_end": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1582873200, "avr_value": 1582873200.0}}], "event_is_live": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 1}}], "event_start": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1582858800, "avr_value": 1582858800.0}}]}}]}}], "author_cakeday": [{"type": "bool", "usage": "always", "schema": {"true": 121057, "false": 0}}], "ad_business": [{"type": "null", "usage": "always", "schema": null}], "ad_promoted_user_posts": [{"type": "null", "usage": "always", "schema": null}], "ad_supplementary_text_md": [{"type": "null", "usage": 0.91, "schema": null}, {"type": "string", "usage": 0.09, "schema": {"min_length": 0, "max_length": 429, "avr_length": 56.95}}], "ad_user_targeting": [{"type": "null", "usage": "always", "schema": null}], "adserver_click_url": [{"type": "null", "usage": "always", "schema": null}], "adserver_imp_pixel": [{"type": "null", "usage": "always", "schema": null}], "app_store_data": [{"type": "null", "usage": "always", "schema": null}], "author_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 12, "avr_length": 11.28}}], "campaign_id": [{"type": "null", "usage": "always", "schema": null}], "domain_override": [{"type": "string", "usage": 0.77, "schema": {"min_length": 0, "max_length": 195, "avr_length": 17.78}}, {"type": "null", "usage": 0.23, "schema": null}], "embed_type": [{"type": "null", "usage": 1.0, "schema": null}, {"type": "string", "usage": 0.0, "schema": {"min_length": 0, "max_length": 6, "avr_length": 6.0, "values": [{"value": "scrape", "usage": 0.88}, {"value": "iframe", "usage": 0.12}]}}], "embed_url": [{"type": "null", "usage": 1.0, "schema": null}, {"type": "string", "usage": 0.0, "schema": {"min_length": 0, "max_length": 142, "avr_length": 42.75}}], "events": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "eventsOnRender": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "href_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 786, "avr_length": 92.28}}], "impression_id": [{"type": "null", "usage": "always", "schema": null}], "impression_id_str": [{"type": "null", "usage": "always", "schema": null}], "is_blank": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 100596}}], "is_survey_ad": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 100596}}], "mobile_ad_url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 85, "avr_length": 44.92}}], "original_link": [{"type": "null", "usage": "always", "schema": null}], "outbound_link": [{"type": "object", "usage": "always", "schema": {}}], "priority_id": [{"type": "null", "usage": "always", "schema": null}], "product_ids": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "promo_layout": [{"type": "null", "usage": 0.97, "schema": null}, {"type": "string", "usage": 0.03, "schema": {"min_length": 0, "max_length": 15, "avr_length": 7.03, "values": [{"value": "PRODUCT", "usage": 1.0}, {"value": "SPOTLIGHT_VIDEO", "usage": 0.0}]}}], "promoted": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 100596}}], "promoted_by": [{"type": "null", "usage": "always", "schema": null}], "promoted_display_name": [{"type": "null", "usage": "always", "schema": null}], "promoted_url": [{"type": "null", "usage": "always", "schema": null}], "show_media": [{"type": "bool", "usage": "always", "schema": {"true": 100596, "false": 0}}], "sk_ad_network_data": [{"type": "null", "usage": "always", "schema": null}], "third_party_trackers": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "third_party_tracking": [{"type": "null", "usage": "always", "schema": null}], "third_party_tracking_2": [{"type": "null", "usage": "always", "schema": null}], "poll_data": [{"type": "object", "usage": 0.92, "schema": {"is_prediction": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 90368}}], "options": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 6, "avr_length": 3.59, "schema": [{"type": "object", "usage": "always", "schema": {"id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 8, "avr_length": 8.0}}], "text": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 120, "avr_length": 17.07}}], "vote_count": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 513, "avr_value": 63.81}}]}}]}}], "prediction_status": [{"type": "null", "usage": "always", "schema": null}], "resolved_option_id": [{"type": "null", "usage": "always", "schema": null}], "total_stake_amount": [{"type": "null", "usage": "always", "schema": null}], "total_vote_count": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 2947, "avr_value": 3.36}}], "tournament_id": [{"type": "null", "usage": "always", "schema": null}], "user_selection": [{"type": "null", "usage": "always", "schema": null}], "user_won_amount": [{"type": "null", "usage": "always", "schema": null}], "vote_updates_remained": [{"type": "null", "usage": "always", "schema": null}], "voting_end_timestamp": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1707350227846, "avr_value": 1705708236400.55}}]}}, {"type": "null", "usage": 0.08, "schema": null}], "call_to_action": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 13, "avr_length": 8.78, "values": [{"value": "Shop Now", "usage": 0.2}, {"value": "View More", "usage": 0.04}, {"value": "Install", "usage": 0.07}, {"value": "Download", "usage": 0.09}, {"value": "Contact Us", "usage": 0.02}, {"value": "Learn More", "usage": 0.34}, {"value": "Get a Quote", "usage": 0.01}, {"value": "Watch Now", "usage": 0.02}, {"value": "Sign Up", "usage": 0.1}, {"value": "Play Now", "usage": 0.03}, {"value": "Order Now", "usage": 0.02}, {"value": "Apply Now", "usage": 0.03}, {"value": "See <PERSON><PERSON>", "usage": 0.0}, {"value": "Pre-order Now", "usage": 0.01}, {"value": "Get Showtimes", "usage": 0.01}]}}], "collections": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 1, "avr_length": 1.0, "schema": [{"type": "object", "usage": "always", "schema": {"author_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 12, "avr_length": 10.6}}], "author_name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 20, "avr_length": 12.27}}], "collection_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 36, "avr_length": 36.0}}], "created_at_utc": [{"type": "float", "usage": 1.0, "schema": {"min_value": 0, "max_value": 1706741083.2, "avr_value": 1691916853.98}}, {"type": "int", "usage": 0.0, "schema": {"min_value": 0, "max_value": 1706101011, "avr_value": 1692503807.57}}], "description": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 500, "avr_length": 17.45}}], "display_layout": [{"type": "null", "usage": 0.96, "schema": null}, {"type": "string", "usage": 0.04, "schema": {"min_length": 0, "max_length": 8, "avr_length": 7.49, "values": [{"value": "GALLERY", "usage": 0.51}, {"value": "TIMELINE", "usage": 0.49}]}}], "last_update_utc": [{"type": "float", "usage": 1.0, "schema": {"min_value": 0, "max_value": 1706744010.06, "avr_value": 1704875579.33}}, {"type": "int", "usage": 0.0, "schema": {"min_value": 0, "max_value": 1706441222, "avr_value": 1705570537.71}}], "link_ids": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 100, "avr_length": 23.05, "schema": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 10, "avr_length": 9.86}}]}}], "permalink": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 94, "avr_length": 85.83}}], "subreddit_id": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 8.75}}], "title": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 295, "avr_length": 20.63}}], "sr_detail": [{"type": "object", "usage": "always", "schema": {"banner_img": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 80, "avr_length": 4.21}}], "banner_size": [{"type": "null", "usage": 0.95, "schema": null}, {"type": "array", "usage": 0.05, "schema": {"min_length": 0, "max_length": 2, "avr_length": 2.0, "schema": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1280, "avr_value": 749.33}}]}}], "community_icon": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 132, "avr_length": 115.54}}], "created": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1706670827, "avr_value": 1617790356.09}}], "created_utc": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1706670827, "avr_value": 1617790356.09}}], "display_name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 21, "avr_length": 13.53}}], "display_name_prefixed": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 23, "avr_length": 15.53}}], "icon_img": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 80, "avr_length": 8.42}}], "icon_size": [{"type": "null", "usage": 0.89, "schema": null}, {"type": "array", "usage": 0.11, "schema": {"min_length": 0, "max_length": 2, "avr_length": 2.0, "schema": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 256, "avr_value": 253.33}}]}}], "key_color": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 7, "avr_length": 0.98, "values": [{"value": "", "usage": 0.86}, {"value": "#ffb000", "usage": 0.02}, {"value": "#7e53c1", "usage": 0.04}, {"value": "#0079d3", "usage": 0.02}, {"value": "#ea0027", "usage": 0.02}, {"value": "#46d160", "usage": 0.02}, {"value": "#ddbd37", "usage": 0.02}, {"value": "#24a0ed", "usage": 0.02}]}}], "mod_permissions": [{"type": "array", "usage": "always", "schema": {"min_length": 0, "max_length": 0, "avr_length": 0.0, "schema": []}}], "name": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 9, "avr_length": 8.75}}], "over_18": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 57}}], "primary_color": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 7, "avr_length": 3.93}}], "sr": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 21, "avr_length": 13.53}}], "sr_display_name_prefixed": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 23, "avr_length": 15.53}}], "subreddit_type": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 10, "avr_length": 6.56, "values": [{"value": "public", "usage": 0.86}, {"value": "restricted", "usage": 0.14}]}}], "subscribers": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 854256, "avr_value": 24471.32}}], "title": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 52, "avr_length": 15.88}}], "url": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 25, "avr_length": 17.53}}], "user_can_crosspost": [{"type": "bool", "usage": 0.96, "schema": {"true": 55, "false": 0}}, {"type": "null", "usage": 0.04, "schema": null}], "whitelist_status": [{"type": "null", "usage": 0.84, "schema": null}, {"type": "string", "usage": 0.16, "schema": {"min_length": 0, "max_length": 8, "avr_length": 7.11, "values": [{"value": "all_ads", "usage": 0.89}, {"value": "some_ads", "usage": 0.11}]}}]}}]}}]}}], "subcaption": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 8, "avr_length": 6.17}}], "event_end": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1705978800, "avr_value": 1705187571.43}}], "event_is_live": [{"type": "bool", "usage": "always", "schema": {"true": 0, "false": 7}}], "event_start": [{"type": "int", "usage": "always", "schema": {"min_value": 0, "max_value": 1705964400, "avr_value": 1705173171.43}}], "unrepliable_reason": [{"type": "string", "usage": "always", "schema": {"min_length": 0, "max_length": 5, "avr_length": 5.0, "values": [{"value": "BLOCK", "usage": "always"}]}}]}}]
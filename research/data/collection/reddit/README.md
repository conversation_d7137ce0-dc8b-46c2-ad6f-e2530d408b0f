Reddit Posts and Comments
========================

Up-to-date Reddit dumps can be downloaded from the Arctic Shift project.

Most recent versions are found on the [Releases page](https://github.com/ArthurHeitmann/arctic_shift/releases).

Note that please use the direct download option instead of P2P.



Downloading
------------

As of the last update, direct download is found at [filen](https://drive.filen.io/f/fb67389b-2eb2-42e8-9d2f-474ca153e105#cgZ5eW2NWXuS9n9rVhdnTkPDmZAeuOhk).

This contains all recent dumps, so please only download one of them.

Please make sure you also download associated schema JSON definition from the [repo](https://github.com/ArthurHeitmann/arctic_shift/tree/master/schemas) for
the particular date dump.  Every dump may have a slightly different schema!


Decompression
--------------

The files are in a special `zst_blocks` format.  This can be decompressed using the cli tools from Arctic Shift,
found at [`ArthurHeitmann/zst_blocks_format`](https://github.com/ArthurHeitmann/zst_blocks_format/tree/81f2010feba9ec15c233327b0f61d5d6f817d42f/python_cli)

To convert it to JSONL

```bash
python zst_blocks.py decode -i /path/to/file.zst_block -o /path/to/file.jsonl
```


Processing
-------------
Comments and posts are each in one giant JSONL file.  This is very hard to process, so we use
split to split them into smaller files

```bash
split -l 100000 some-file.jsonl some-file-part- -a 6 -d --additional-suffix=.jsonl

gzip *.jsonl
```

Then we can use spark to convert it to parquet with the provided script.

Note that posts and comments have 159 and 75 columns respectively.  DO NOT TRY TO USE SPARK TO AUTOMATICALLY DETERMINE THE SCHEMA.

It will crash the cluster.

- total rows: 284080628
- uncompressed size: 511671768804


Schema:

```typescript
interface RedditCommentData {
	_meta: object
	all_awardings: Array
	approved_at_utc: null
	approved_by: null
	archived: boolean
	associated_award: null
	author: String
	author_cakeday?: boolean
	author_flair_background_color?: null|String
	author_flair_css_class?: null|String
	author_flair_richtext?: Array
	author_flair_template_id?: null|String
	author_flair_text?: null|String
	author_flair_text_color?: null|String
	author_flair_type?: String
	author_fullname?: String
	author_is_blocked: boolean
	author_patreon_flair?: boolean
	author_premium?: boolean
	awarders: Array
	banned_at_utc: null
	banned_by: null
	body: String
	can_gild: boolean
	can_mod_post: boolean
	collapsed: boolean
	collapsed_because_crowd_control: null
	collapsed_reason?: null|String
	collapsed_reason_code?: null|String
	comment_type: null
	controversiality: integer
	created: integer
	created_utc: integer
	distinguished?: null|String
	downs: integer
	editable?: boolean
	edited?: boolean|integer
	expression_asset_data?: object
	gilded: integer
	gildings: object
	id: String
	is_submitter: boolean
	likes: null
	link_id: String
	locked: boolean
	media_metadata?: object
	mod_note: null
	mod_reason_by: null
	mod_reason_title: null
	mod_reports: Array
	name: String
	no_follow: boolean
	num_reports: null
	parent_id: String
	permalink: String
	removal_reason?: null|String
	replies: String
	report_reasons: null
	retrieved_on: integer
	rte_mode?: String
	saved: boolean
	score: integer
	score_hidden: boolean
	send_replies: boolean
	stickied: boolean
	subreddit: String
	subreddit_id: String
	subreddit_name_prefixed: String
	subreddit_type: String
	top_awarded_type: null
	total_awards_received: integer
	treatment_tags: Array
	unrepliable_reason?: null|String
	ups: integer
	user_reports: Array
}
```

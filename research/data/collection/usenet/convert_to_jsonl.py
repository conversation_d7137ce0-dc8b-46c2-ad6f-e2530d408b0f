"""Convert mbox dumps to json format, also extract key fields and compute text hash."""
import argparse
import re
from pathlib import Path

from research.data.collection.utils.mbox import mbox_to_threads


def extract_tags(filename: str) -> dict[str, str]:
    """Extract group name and timestamp as tags."""
    # Match the timestamp part
    match = re.match(r"(.+?)(\d{8})", filename)

    if match:
        group_name = match.group(1).rstrip(".")
        timestamp = match.group(2)
        return {
            "group": group_name.lower(),
            "timestamp": timestamp,
            "filename": filename,
        }
    else:
        return {}


def extract_group_name(filename: str):
    """Extract group name from filename."""
    tags = extract_tags(filename)
    if "group" in tags:
        return tags["group"]
    else:
        return "unknown"


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convert mbox dump files to JSONL.")
    parser.add_argument(
        "input_path", type=str, help="Input directory containing UseNet mbox dump files"
    )
    parser.add_argument(
        "output_path", type=str, help="Output directory to store JSONL files"
    )
    args = parser.parse_args()

    mbox_to_threads(
        Path(args.input_path),
        "*.mbox",
        Path(args.output_path),
        extract_group_name,
        extract_tags,
    )

Usenet dataset
=====

Here, all Usenet dumps are downloaded from `archive.org.  These are, unfortunately, quite
old.  Generally the newest comprehensive dumps are in 2015.


Many of the groups are also available in Google Groups.  In fact, Google Groups were created from
UseNet groups when Google acquired Deja in 2001.  As a result, we can get newer contents from
GGs.  However, the quality degrades significantly in recent years, and many groups are now dominated
by spam.  It will therefore be difficult to evaluate their quality without more effort.

Google Groups are also requires a scraper which adds further complications.

As a result, this particular dataset only deals with "Old" UseNet groups up to 2015 from `archive.org`.


Overall statistics:

- comp      17G
- sci       7.2G
- microsoft 59G
- other     29G

Total size: 111G


The computers hierarchy ( `comp` )
------------------
This is the main hierarchy concerning computer programming as well as hardware.  Almost all contents are
highly relevant, and earlier contents are significantly more technical than more recent forums.

A quick manual inspection is done and removed the following groups

 - comp.admin
 - comp.file-sharing
 - comp.fonts
 - comp.forsale
 - comp.job
 - comp.jobs
 - comp.jobsoffered
 - comp.laser-pointers
 - comp.mulitmedia
 - comp.sex

All other groups are kept.


Microsoft hosted forums ( `microsoft` )
---------------------
All contents of the `microsoft.public` group hosted by Microsoft.

Relevant Scientific groups ( `sci` )
-------------
Includes math, statistics, physics, various engineering and general research groups.

- sci.answers
- sci.astro
- sci.chemistry
- sci.electronics
- sci.engr
- sci.logic
- sci.materials
- sci.math
- sci.mech
- sci.physics
- sci.research
- sci.space
- sci.stat


Other groups
------------------
The following groups

- adobe
- alt.apache
- alt.atari
- alt.cad
- alt.chrome
- alt.cobol
- alt.comp
- alt.html
- alt.hypertext
- alt.lang
- alt.linux
- alt.msdos
- alt.os
- alt.unix
- borland.public
- cern
- corelcommunity
- coreldevelopers
- dbase
- freebsd
- gnu
- hackercorp
- ibm
- intel
- misc.legal.computing
- misc.writing
- nersc
- netbeans
- netbsd
- netscape
- news.software
- openbsd
- perl
- pgsql
- php
- sourceforge
- su
- ucb

import html2text
import pypandoc

from base.retrieval.chunking.utils.html_to_md_conversion import HTMLToMarkdownConverter


class PandocHTMLToMarkdownConverter(HTMLToMarkdownConverter):
    """Uses pandoc to convert HTML to markdown."""

    def convert(self, html: str) -> str:
        return pypandoc.convert_text(
            html, "markdown_github-raw_html-native_divs-native_spans", format="html"
        )


class HTML2TextConverter(HTMLToMarkdownConverter):
    """Uses html2text to convert HTML to markdown."""

    def convert(self, html: str) -> str:
        return html2text.html2text(html)

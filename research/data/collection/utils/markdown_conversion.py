from concurrent.futures import ThreadPoolExecutor
from typing import overload

from base.retrieval.chunking.utils.markdown_conversion import (
    remove_data_urls,
    simple_conversion,
    convert_html_to_md as convert_html_to_md_original,
    retrieve_text_from_schema,
    _has_display_none_style,
    cleanup,
    replace_excessive_newlines,
    remove_links,
    robust_conversion as robust_conversion_original,
)

from research.data.collection.utils.html_to_md_conversion import (
    HTMLToMarkdownConverter,
    PandocHTMLToMarkdownConverter,
    HTML2TextConverter,
)


def convert_html_to_md(html_text: str, timeout_ms: int = 1000) -> str:
    """Placeholder for research backwards compatibility."""
    executor = ThreadPoolExecutor(max_workers=1)
    markdown_converter = [
        PandocHTMLToMarkdownConverter(),
        HTML2TextConverter(),
    ]
    return convert_html_to_md_original(
        html_text, executor, markdown_converter, timeout_ms
    )


def robust_conversion(
    html_text: str,
    timeout_ms: int = 1000,
    cleanup_html: bool = True,
    keep_links: bool = True,
    use_meta_text: bool = False,
) -> str:
    """Placeholder for research backwards compatibility."""
    executor = ThreadPoolExecutor(max_workers=1)
    text = robust_conversion_original(
        html_text,
        executor,
        timeout_ms,
        cleanup_html,
        keep_links,
        use_meta_text,
        [PandocHTMLToMarkdownConverter(), HTML2TextConverter()],
    )
    if not text:
        return ""
    return text

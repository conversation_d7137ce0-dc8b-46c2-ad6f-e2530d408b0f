"""Tokenize a batch of documents and mask out boilerplates.

With NL dataset a common need is to mask out specific contents.
This needs to be done along with the tokenization step.
"""

from dataclasses import dataclass
from typing import Callable, Iterable

import pandas as pd
from dataclasses_json import dataclass_json

from base.tokenizers.tokenizer import RagSpecialTokens, RetrievalSpecialTokens
from base.tokenizers import create_tokenizer_by_name


@dataclass_json
@dataclass
class MaskableText:
    """A single text entry from a dataset that can be masked."""

    text: str
    """The text of the entry."""

    masked: bool
    """Whether the text should be masked out during tokenization."""


MaskableTextGenerator = Callable[[dict], Iterable[MaskableText]]


def tokenize_and_pack(
    batch: pd.DataFrame,
    text_generator: MaskableTextGenerator,
    tokenizer_name: str = "deepseek_coder_base",
    seq_length: int = 4096,
    add_one_token: bool = True,
    max_tail_pad: int = 410,
    output_column: str = "packed_samples",
    transform_fn: Callable[[list[int]], list[int]] = lambda x: x,
) -> pd.DataFrame | None:
    """Tokenize a batch of documents and mask out boilerplates.

    batch: (pd.DataFrame) A pandas dataframe.  No specific schema requirements; rows are passed to callback.
    text_generator: (Callable) A function that takes a row and returns an iterable of MaskableText.
    tokenizer_name: (str) The name of the base.tokenizer to use.
    seq_length: (int) The length of the sequence to pack.
    add_one_token: (bool) Whether to add one token to the sequence length.
    max_tail_pad: (int) The maximum number of tokens to pad at the end of a document.
        If after one document is packed, the remaining space is less than this,
        and there is no enough room to pack the next document, the document will be padded with
        pad_token_id.  This is to avoid breaking documents into very small fragments that breaks
        the context of documents.
    """

    tokenizer = create_tokenizer_by_name(tokenizer_name)
    special_tokens = tokenizer.special_tokens
    assert isinstance(special_tokens, (RagSpecialTokens, RetrievalSpecialTokens))

    bos_token = list(special_tokens.begin_sequence)

    # Tokens that have not yet been cut into samples.
    buffer: list[int] = []

    # The packed samples, each a list with equal length.
    packed_samples: list[list[int]] = []

    # Padding tokens are masked
    pad_token_id = -special_tokens.padding
    token_length = seq_length + (1 if add_one_token else 0)

    for _, row in batch.iterrows():
        # Each doc is a list of DataEntry
        doc = row.to_dict()
        doc_tokens = []
        for entry in text_generator(doc):
            tokenized = tokenizer.tokenize_safe(entry.text)
            if entry.masked:
                tokenized = [-item for item in tokenized]
            doc_tokens.extend(tokenized)
        if doc_tokens:
            doc_tokens = bos_token + doc_tokens
            doc_tokens.append(special_tokens.eos)
            # Check if we extend or start a new doc
            if (
                len(buffer) + len(doc_tokens) <= token_length
                or len(buffer) < token_length - max_tail_pad
            ):
                buffer.extend(doc_tokens)
            else:
                buffer.extend([pad_token_id] * (token_length - len(buffer)))
                packed_samples.append(transform_fn(buffer))
                buffer = doc_tokens
        while len(buffer) > token_length:
            packed_samples.append(transform_fn(buffer[:token_length]))
            buffer = buffer[token_length:]
    if buffer:
        buffer.extend([pad_token_id] * (token_length - len(buffer)))
        packed_samples.append(transform_fn(buffer))
    if len(packed_samples) > 0:
        return pd.DataFrame({output_column: packed_samples})

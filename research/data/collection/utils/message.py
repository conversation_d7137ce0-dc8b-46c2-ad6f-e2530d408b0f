"""Utility to process email messages and fill key fields into dicts."""

import gzip
import json
import logging
import mailbox
import sys
import uuid
from collections import defaultdict
from copy import deepcopy
from dataclasses import dataclass, field
from email.message import Message as EmailMessage
from pathlib import Path
from typing import Iterable, Optional, Union

import html2text
from dataclasses_json import config, dataclass_json

from research.data.collection.github.controllers import languages

logger = logging.getLogger(__name__)

FIELDS = {
    "subject": "subject",
    "date": "date",
    "from": "from_",
    "to": "to",
    "in-reply-to": "in_reply_to",
    "message-id": "message_id",
    "references": "references",
    "list-id": "list_id",
}

# Maximum allowed thread depth in recursions
MAX_THREAD_DEPTH = 10000
# Set the recursion limit to 5000 because this is the thread depth limit
sys.setrecursionlimit(max(sys.getrecursionlimit(), MAX_THREAD_DEPTH + 20))


@dataclass_json
@dataclass
class Message:
    """A message with key fields."""

    subject: str
    date: Optional[str]
    from_: str
    to: Optional[str]
    in_reply_to: Optional[str]
    message_id: str
    references: list[str]
    list_id: Optional[str]
    body: str
    signature: str
    attachments: dict[str, str] = field(
        metadata={
            "dataclasses_json": {
                "encoder": lambda x: [
                    {"filename": key, "content": value} for key, value in x.items()
                ]
            }
        }
    )
    meta: dict[str, str] = field(metadata={"dataclasses_json": {"encoder": json.dumps}})


@dataclass_json
@dataclass
class Thread:
    """A thread of messages."""

    subject: str
    messages: list[Message]
    member_ids: list[str]
    additional_info: dict[str, str]
    references: set[str] = field(
        default_factory=set, metadata=config(exclude=lambda x: True)
    )
    markdown: Optional[str] = None
    suffix_stats: Optional[dict[str, int]] = field(
        default=None, metadata=config(exclude=lambda x: True)
    )


# Construct the default list of suffixes for attachments to keep
# Common patch file formats
DEFAULT_SUFFIXES = {".txt", ".quilt", ".hg", ".git", ".stgit", ".svn"}
for suffixes in languages.language_mapping.values():
    DEFAULT_SUFFIXES.update(suffixes)
DEFAULT_SUFFIXES = frozenset(DEFAULT_SUFFIXES)


def process_message(
    message: Union[EmailMessage, mailbox.mboxMessage],
    attachment_suffixes: Optional[Iterable[str]] = None,
    exclude_suffixes: Iterable[str] = (".html",),
    split_signatures: bool = True,
    max_attachment_size: int = 20000,
) -> Message:
    """Process a message and return a dictionary of key fields and body.

    The following fields will be parsed:
    - subject
    - from
    - to
    - date
    - body
    - signature
    - message-id
    - references
    - in-reply-to
    - list-id
    - attachments

    Args:
        message (email.message.Message | mbox.mboxMessage): The message to process.
        attachment_suffixes (set | frozenset): The set of suffixes to keep attachments with.
        exclude_suffixes (set): The set of suffixes to exclude attachments with.
        split_signatures (bool): Whether to split the signature from the body.
        max_attachment_size (int): The maximum size of an attachment to keep.

    Returns:
        dict: A dictionary of key fields and body.
    """
    if attachment_suffixes is None:
        attachment_suffixes = DEFAULT_SUFFIXES
    attachment_suffixes = set(attachment_suffixes)
    for item in exclude_suffixes or []:
        if item in attachment_suffixes:
            attachment_suffixes.remove(item)
    # Convert mailbox.Message to email.message.EmailMessage
    msg_dict = {}
    for key, value in message.items():
        # make sure value is json serializable; decode if if it is `bytes`
        if isinstance(value, bytes):
            value = value.decode("utf-8", errors="replace")
        if not isinstance(value, (str, int, float, bool, type(None), list)):
            value = str(value)
        if isinstance(value, str):
            value = value.strip()
        msg_dict[key.lower()] = value

    result = {field_name: msg_dict.pop(key, None) for key, field_name in FIELDS.items()}

    result["references"] = [
        item for item in (result["references"] or "").split() if item
    ]

    body = ""
    result["attachments"] = attachments = {}
    if message.is_multipart():
        plaintext: list[str] = []
        html: list[str] = []
        for part in message.walk():
            content_type = part.get_content_maintype()
            content_disposition = part.get_content_disposition()
            if content_disposition and content_disposition.lower() == "attachment":
                fname = part.get_filename()
                attachment_content = part.get_payload(decode=True)
                if (
                    not fname
                    or Path(fname).suffix not in attachment_suffixes
                    or not isinstance(attachment_content, bytes)
                    or len(attachment_content) > max_attachment_size
                ):
                    continue
                # Try to decode the attachment
                try:
                    attachment_content = attachment_content.decode(
                        part.get_content_charset() or "utf-8"
                    )
                    attachments[fname] = attachment_content
                except (UnicodeDecodeError, LookupError):
                    # Do not include attachment if it is not decodable
                    pass
                continue
            if content_type and content_type.lower() != "text":
                continue
            part_payload = part.get_payload(decode=True)
            if isinstance(part_payload, bytes):
                try:
                    part_body = part_payload.decode(
                        part.get_content_charset() or "utf-8"
                    )
                except (UnicodeDecodeError, LookupError):
                    part_body = part_payload.decode("utf-8", errors="replace")
            else:
                part_body = str(part_payload)
            if not part_body:
                continue
            if content_type and content_type.lower() == "text/html":
                html.append(part_body)
            else:
                plaintext.append(part_body)
        if plaintext:
            body = "\n\n".join(plaintext)
        elif html:
            body = "\n\n".join(
                html2text.html2text(
                    item,
                )
                for item in html
            )
    else:
        payload = message.get_payload(decode=True)
        if isinstance(payload, bytes):
            try:
                charset = message.get_content_charset()
                if charset == "undefined" or not charset:
                    charset = "utf-8"
                body = payload.decode(charset)
            except (UnicodeDecodeError, LookupError, UnicodeError):
                body = payload.decode("utf-8", errors="replace")
        else:
            body = str(payload)

    # Split body and signature
    if split_signatures:
        parts = body.split(
            "\n-- \n", 1
        )  # Splitting at the first occurrence of the delimiter
        result["body"] = parts[0]
        result["signature"] = (
            parts[1] if len(parts) > 1 else ""
        )  # Signature is the second part if present
    else:
        result["body"] = body
        result["signature"] = None

    # Handle additional headers (metadata)
    result["meta"] = msg_dict
    # Convert dict to Message
    return Message(**result)


def merge_threads(thread1: Thread, thread2: Thread):
    """Merge two threads into one."""
    # now, thread1 is old thread, thread2 the new one
    result = deepcopy(thread1)
    result.messages.extend(thread2.messages)
    result.member_ids.extend(thread2.member_ids)
    result.references.update(thread2.references)
    return result


@dataclass_json
@dataclass
class Tree:
    """A tree structure for messages."""

    message: Message
    """The message on a note."""

    children: list["Tree"] = field(default_factory=list)
    """All children of the node."""

    parent: Optional["Tree"] = None
    """Parent of the node."""

    descendents: set = field(init=False)
    """Set of the ids of all descendent messages."""

    def __post_init__(self):
        self.descendents = {self.message.message_id}

    def is_subtree(self, other: "Tree") -> bool:
        """Returns whether the other tree is a subtree of this tree."""
        # check overlap
        # Sometimes upstream graph might be messed up and we will just be
        # conservative and not merge trees that can potentially create
        # circular references
        return len(other.descendents.intersection(self.descendents)) > 0

    def add_subtree(self, other: "Tree"):
        """Adds the other tree as a subtree of this tree."""
        self.children.append(other)
        other.parent = self
        node = self
        traversed = set()
        while node:
            if node.message.message_id in traversed:
                logger.warning(
                    "Circular reference for message %s", node.message.message_id
                )
                logger.warning("Traversed: %s", str(traversed))
                break
            if len(traversed) > MAX_THREAD_DEPTH:
                logger.warning(
                    "Too many parents traversed for message %s", node.message.message_id
                )
                logger.warning("Traversed: %s", str(traversed))
                break
            node.descendents.update(other.descendents)
            traversed.add(node.message.message_id)
            node = node.parent


def build_message_trees(messages: Iterable[Message]) -> list[Tree]:
    """Builds a tree structure from the list of messages, ensuring a message does not reference itself.

    Args:
        messages (list of dict): List of message dictionaries.

    Returns:
        list: List of tuples, each containing a message and its children, without self-references.
    """
    indexed = {msg.message_id: Tree(msg, []) for msg in messages}

    roots: list[Tree] = []
    for msg_id, node in sorted(indexed.items(), key=lambda x: x[1].message.date or ""):
        for ref in node.message.references:
            if ref not in indexed:
                continue
            parent = indexed[ref]
            # Ensure the parent is not the message itself
            if parent.message.message_id != msg_id:
                # Ensure there is no circular reference
                if parent.is_subtree(node):
                    continue
                parent.add_subtree(node)
                break
        else:
            roots.append(node)
    if len(roots) > 1:
        logger.warning("Multiple roots found! %d", len(roots))
    if not roots:
        # Circular reference found!
        raise ValueError("No root found! Must be a circular reference!")
    return roots


def generate_markdown_and_sort(trees: Iterable[Tree], depth: int = 0):
    """Generates markdown from a message tree and returns a sorted list of messages.

    Args:
        trees (list of messages): Each item contains a message and its children.
        depth (int): Current depth in the message tree for indentation purposes.

    Returns:
        tuple: Markdown string and a list of messages sorted according to their appearance.
    """
    markdown = ""
    sorted_messages = []

    for tree in trees:
        indent = "  " * depth
        body = tree.message.body.replace("\n", f"\n{indent}    ")
        markdown += f"{indent}- **{tree.message.subject}** from **{tree.message.from_}** on **{tree.message.date}**:\n{indent}    {body}\n"

        for filename, content in tree.message.attachments.items():
            content = content.replace("\n", f"\n{indent}    ")
            markdown += f"{indent}    Attachment: {filename}\n{indent}    {content}\n"

        sorted_messages.append(tree.message)

        if depth >= MAX_THREAD_DEPTH:
            logger.warning("Maximum thread depth reached. Skipping remaining children.")
        elif tree.children:
            child_markdown, child_sorted_messages = generate_markdown_and_sort(
                tree.children, depth + 1
            )
            markdown += child_markdown
            sorted_messages.extend(child_sorted_messages)

    return markdown, sorted_messages


def collect_threads(
    source: Iterable[Message],
    additional_values: Optional[dict[str, str]] = None,
    thread_pool: Optional[list[Thread]] = None,
    max_thread_size=500,
    max_thread_pool=5000,
    known: Optional[dict[str, Message]] = None,
    max_common_suffix_lines: int = 10,
    max_suffix_samples: int = 1000,
):
    """Parse an mbox file and collect individual messages into threads.

    Args:
        source: The source of messages
        additional_values:  If specified, all entries will have these additional keys.
        thread_pool:   Supply this to add item to existing list of threads.
        max_thread_size:  Maximum size of a thread.
        max_thread_pool:  Maximum number of threads to track
        known:     All known message IDs.  For dedupe purpose.
        max_common_suffix_lines:  Number of lines to analyze and identify common suffixes.
        max_suffix_samples:  Max number of messages used to compute suffix statistics


    We take the last N lines of each message and count the occurrences of each suffix lines.
    Those that occur more than the threshold are moved from the tail of the document when
    compiling threads.
    """
    if max_thread_size > MAX_THREAD_DEPTH // 2:
        logger.warning(
            "Currently supported maximum thread depth is %d. max_thread_size % is too high.",
            MAX_THREAD_DEPTH // 2,
            max_thread_size,
        )
        max_thread_size = MAX_THREAD_DEPTH // 2

    if thread_pool is None:
        thread_pool = []
        has_pool = False
    else:
        has_pool = True
    if known is None:
        known = {}
    suffix_stats = defaultdict(int)
    suffix_stats["<|total|>"] = 0
    additional_values = additional_values or {}
    dupes = 0
    thread_count = 0

    for entry in source:
        entry.message_id = (entry.message_id or str(uuid.uuid4())).lower()
        if entry.message_id in known:
            dupes += 1
            # update message
            known[entry.message_id].body = entry.body
            known[entry.message_id].attachments = entry.attachments
            known[entry.message_id].meta = entry.meta
            known[entry.message_id].subject = entry.subject
            continue
        known[entry.message_id] = entry

        # Aggregate suffix analysis
        if suffix_stats["<|total|>"] < max_suffix_samples:
            suffix_stats["<|total|>"] += 1
            for line in entry.body.splitlines()[-max_common_suffix_lines:]:
                suffix_stats[line] += 1
        entry.references = [item.lower() for item in entry.references if item]
        if entry.message_id not in entry.references:
            entry.references.append(entry.message_id)
        if entry.in_reply_to:
            entry.in_reply_to = entry.in_reply_to.lower()
            if entry.in_reply_to not in entry.references:
                entry.references.append(entry.in_reply_to)

        new_thread = Thread(
            subject=entry.subject,
            messages=[entry],
            member_ids=[entry.message_id],
            references=set(entry.references),
            additional_info=additional_values,
            suffix_stats=suffix_stats,
        )
        # Check if it belongs to an old thread
        merged = True
        while merged:
            merged = False
            for i, old_thread in enumerate(thread_pool):
                if new_thread.references.intersection(old_thread.references):
                    new_thread = merge_threads(old_thread, new_thread)
                    thread_pool.pop(i)
                    if len(new_thread.messages) >= max_thread_size:
                        thread_count += 1
                        yield new_thread
                    else:
                        merged = True
                    break
            else:
                thread_pool.append(new_thread)
                if len(thread_pool) >= max_thread_pool:
                    thread_count += 1
                    yield thread_pool[0]
                    thread_pool[:] = thread_pool[1:]
    logger.info("  Thread generated during processing: %d", thread_count)
    if not has_pool:
        logger.info("  Returning remaining %d threads...", len(thread_pool))
        yield from thread_pool
    logger.info(
        "  Final count of unique messages: %d.",
        len(known),
    )
    if dupes:
        logger.info(
            "  Duplicate messages: %d",
            dupes,
        )


def write_batch(
    output_path: Path,
    filename: str,
    file_index: int,
    batch: list[Thread],
    compress: bool,
):
    """Write a batch of threads to disk."""
    output_path.mkdir(parents=True, exist_ok=True)
    if compress:
        output_file = output_path / f"{filename}-{file_index}.jsonl.gz"
        logger.info("  Compressing a batch of %d to %s", len(batch), output_file)
        with gzip.open(output_file, "wt") as f:
            for item in batch:
                f.write(item.to_json().strip() + "\n")
    else:
        output_file = output_path / f"{filename}-{file_index}.jsonl"
        logger.info("  Saving a batch of %d to %s", len(batch), output_file)
        with output_file.open("w+") as f:
            for item in batch:
                f.write(item.to_json().strip() + "\n")
    batch.clear()


def remove_common_suffix(thread: Thread, threshold: float = 0.5):
    """Remove suffix lines that are present in too many messages."""
    # Make sure we have the stats to do this
    if not thread.suffix_stats:
        return
    # compute the threshold in counts
    total = thread.suffix_stats.get("<|total|>", 0)
    if total < 3:
        return
    count_threshold = total * threshold
    lines_to_remove = {
        line
        for line, count in thread.suffix_stats.items()
        if count >= count_threshold and line != "<|total|>"
    }
    for message in thread.messages:
        lines = [
            line
            for line in message.body.splitlines()
            if not any("> " * n + line in lines_to_remove for n in range(1, 5))
        ]
        while lines:
            if not lines[-1] or lines[-1] in lines_to_remove:
                lines.pop()
            else:
                break
        message.body = "\n".join(lines) + "\n"


def post_process_thread(thread: Thread, common_suffix_threshold: float = 0.5):
    """Post process a thread.

    Removing common suffixes, break into multiple trees if needed, and generate markdown.
    """
    remove_common_suffix(thread, threshold=common_suffix_threshold)
    roots = build_message_trees(thread.messages)
    for i_root, root in enumerate(roots):
        markdown_doc, sorted_messages = generate_markdown_and_sort([root])
        if not sorted_messages:
            raise RuntimeError(
                f"Tree size {len(root.descendents)}. "
                + f"#{i_root} out of {len(roots)} roots "
                + f"has {len(sorted_messages)} sorted messages"
            )
        yield Thread(
            subject=root.message.subject,
            messages=sorted_messages,
            member_ids=[m.message_id for m in sorted_messages],
            references=set(),
            additional_info=thread.additional_info,
            markdown=markdown_doc,
        )


def save_threads(
    output_path: Path,
    filename: str,
    threads: Iterable[Thread],
    output_index=0,
    max_threads=5000,
    compress: bool = False,
    common_suffix_threshold: float = 0.5,
):
    """Take a list of threads, process them and save to file.

    Message lists are sorted into trees, and also converted to markdown format.

    Returns how many files have be written.
    """
    batch = []
    received = 0
    skipped = 0
    for thread in threads:
        received += 1
        if not thread.messages:
            skipped += 1
            logger.info("Thread with no messages: %s", thread.subject)
            continue
        batch.extend(
            post_process_thread(thread, common_suffix_threshold=common_suffix_threshold)
        )
        if len(batch) >= max_threads:
            write_batch(output_path, filename, output_index, batch, compress)
            output_index += 1
    if batch:
        write_batch(output_path, filename, output_index, batch, compress)
        output_index += 1
    logger.info(
        "  Total number of threads received: %d. Empty threads skipped: %d",
        received,
        skipped,
    )
    return output_index

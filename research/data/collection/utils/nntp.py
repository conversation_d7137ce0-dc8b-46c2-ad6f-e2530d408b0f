"""NNTP utilities."""

import logging
import nntplib
import time
from collections import deque
from contextlib import contextmanager, suppress
from email.parser import Bytes<PERSON>arser
from typing import Optional

from research.data.collection.utils.message import process_message

logger = logging.getLogger(__name__)

# Increase the maximum line length to 3M
nntplib._MAXLINE = 3000000  # pylint: disable=protected-access


@contextmanager
def connect(
    server: str,
    username: Optional[str] = None,
    password: Optional[str] = None,
    timeout: int = 10,
    port: int = 119,
    secured: bool = False,
):
    """Create a context manager for an NNTP connection."""
    if secured:
        logger.info("Creating NNTP connection to %s through SSL", server)
        nntp_client = nntplib.NNTP_SSL(server, port=port, timeout=timeout)
    else:
        nntp_client = nntplib.NNTP(server, port=port, timeout=timeout)
    try:
        if username:
            nntp_client.login(username, password)
        yield nntp_client
    finally:
        with suppress(Exception):
            nntp_client.quit()


def list_groups(nntp_client: nntplib.NNTP):
    # List all available newsgroups
    newsgroups = nntp_client.list()[1]
    # Convert GroupInfo objects to dictionaries
    newsgroups_dicts = [
        {
            "name": group.group,
            "first": int(group.first),
            "last": int(group.last),
            "count": int(group.last) - int(group.first) + 1,
            "posting_allowed": group.flag,
        }
        for group in newsgroups
    ]

    return newsgroups_dicts


def parse_article(article: nntplib.ArticleInfo):
    """Convert an ArticleInfo object into structured dict."""
    parser = BytesParser()
    raw = b"\n".join(article.lines)
    message = parser.parsebytes(raw)
    return process_message(message)


class NNTPArticleSource:
    """A source of NNTP articles.

    Extract bulk list of articles from a specific group and server.
    """

    def __init__(
        self,
        server: str,
        group: str,
        start: int,
        limit: int,
        username: Optional[str],
        password: Optional[str],
        max_batch_size: int = 50000,
        cooldown: float = 0.2,
        port: int = 119,
        secured: bool = False,
    ):
        """Initialize the source.

        Args:
            server: The NNTP server to connect to.
            group: The group to fetch from.
            start: The first article to fetch.
            limit: The number of articles to fetch.
            username: The username to use for authentication.
            password: The password to use for authentication.
            max_batch_size: The maximum number of articles to fetch in a single batch when identifying articles.
            cooldown: The cooldown time between requests.
        """
        self.server = server
        self.port = port
        self.group = group
        self.username = username
        self.password = password
        self.max_batch_size = max_batch_size
        self.start = start
        self.limit = limit
        self.cooldown = cooldown
        self.skipped = []
        self.secured = secured

        # This is to track how many articles don't actually exist.
        # Used to increment the pointer if the scan endup returning
        # nothing.
        self.last_nonexist = None
        self.indexes: deque[int] = self.find_indexes()

        self.processed = []

    def find_indexes(self):
        """Find a list of article IDs that exist in the group.

        User OVER command to pull valid article IDs until we have enough.
        """
        with connect(
            self.server,
            self.username,
            self.password,
            port=self.port,
            secured=self.secured,
        ) as client:
            # Extract group info and identify articles to scrape
            _, count, first, last, _ = client.group(self.group)
            logger.info(
                "Group: %s has %d articles, range %d to %d",
                self.group,
                count,
                first,
                last,
            )
            if self.start < first:
                logger.warning(
                    "Start index %d is less than first article %d", self.start, first
                )
                self.start = first
            if self.start > last:
                logger.warning(
                    "Start index %d is greater than last article %d", self.start, last
                )
                return deque()
            indexes: deque[int] = deque()
            while len(indexes) < self.limit and self.start <= last:
                batch_size = min(self.max_batch_size, self.limit - len(indexes))
                end = min(self.start + batch_size - 1, last)
                try:
                    resp, results = client.over((self.start, end))
                except nntplib.NNTPTemporaryError as e:
                    if "423 No articles in" in str(e):
                        # There are not articles in this block.
                        logger.info(
                            "No articles in range %d to %d; striding through",
                            self.start,
                            end,
                        )
                        self.start += batch_size
                        if self.last_nonexist != -1:
                            self.last_nonexist = end
                        continue
                    logger.warning("Temporary error: %s", e)
                    break
                self.last_nonexist = -1
                if not resp.startswith("224 "):
                    logger.warning("Unexpected response: %s", resp)
                    break
                for article_id, _ in results:
                    indexes.append(article_id)
                self.start += batch_size
        return indexes

    def iter_articles(self):
        """Fetch through the list of articles."""
        i = None
        with connect(
            self.server,
            self.username,
            self.password,
            port=self.port,
            secured=self.secured,
        ) as client:
            client.group(self.group)
            while self.indexes:
                i = self.indexes.popleft()
                try:
                    _, article = client.article(i)
                    logger.debug("Successfully fetched article %d", i)
                    yield parse_article(article)
                    time.sleep(
                        self.cooldown
                    )  # Be respectful to the server, avoid hammering it
                except nntplib.NNTPProtocolError:
                    logger.info(
                        "Incorrect protocol for article %d for group %s. "
                        "Most likely the article is longer than line length limit. "
                        "Skipping article.",
                        i,
                        self.group,
                    )
                    # Put it back into the queue
                    self.skipped.append(i)
                except Exception as e:  # pylint: disable=broad-except
                    if "No such article" in str(e):
                        logger.info(
                            "Article %d does not exist in group %s", i, self.group
                        )
                        self.skipped.append(i)
                    elif "line too long" in str(e):
                        logger.info(
                            "Group %s Article %d has lines exceeding limit.",
                            self.group,
                            i,
                        )
                        self.skipped.append(i)
                    elif "Illegal char" in str(e):
                        logger.info(
                            "Group %s Article %d has illegal characters.",
                            self.group,
                            i,
                        )
                        self.skipped.append(i)
                    else:
                        logger.error(
                            "Error fetching article %d for group %s",
                            i,
                            self.group,
                            exc_info=e,
                        )
                        # Put it back into the queue
                        self.indexes.appendleft(i)
                        # A slightly longer cooldown in the event of failures
                        time.sleep(self.cooldown * 10)
                        break
                self.processed.append(i)
        if i is None:
            logger.info(
                "Nothing fetched. Last scanned index: %d", self.last_nonexist or 0
            )
        else:
            logger.info("Done fetching articles. Last processed index: %d", i)
            logger.info("Number of articles processed: %d", len(self.processed))
            logger.info("Number of articles skipped: %d", len(self.skipped))

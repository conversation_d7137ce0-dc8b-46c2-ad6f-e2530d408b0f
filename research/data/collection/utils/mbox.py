"""Utility to parse mbox file and fill key fields into dicts."""

import mailbox
from itertools import chain
from pathlib import Path
from typing import Callable, Iterable, Iterator, Optional, Union

from research.data.collection.utils.message import (
    Message,
    collect_threads,
    process_message,
    save_threads,
)


class UTF8Mbox(mailbox.mbox):
    """An updated mailbox.mbox that is stable against utf-8 encoded dumps."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def get_message(self, key):
        """Retrieve a message by key."""
        start, stop = self._lookup(key)
        self._file.seek(start)
        from_line = self._file.readline().replace(b"\n", b"")
        # Decode the from_line more leniently
        from_line = from_line.decode("utf-8", errors="replace")
        msg = mailbox.mboxMessage(self._file.read(stop - self._file.tell()))
        msg.set_from(from_line[5:], True)
        return msg


def parse_mbox(
    mbox_file: Union[Path, str],
    split_signatures: bool = True,
    attachment_suffixes: Optional[Iterable[str]] = None,
    exclude_suffixes: Iterable[str] = (".html",),
    max_attachment_size: int = 20000,
) -> Iterator[Message]:
    """Parse a mailbox file containg a usenet dump.

    Args:
        mbox_bile:          Path to the mbox file to be parsed.
        split_signatures:   When True, signature is removed from body and
                            becomes a separate field.
        attachment_suffixes: Which suffixes to include for attachments. By default, include
                            all known code suffixes.
        exclude_suffixes:   Exclude specific suffixes.  By default, exclude html
        max_attachment_size: Attachments larger than this is skipped.

    Yields:
        Message objects
    """

    for message in UTF8Mbox(mbox_file):
        yield process_message(
            message,
            split_signatures=split_signatures,
            attachment_suffixes=attachment_suffixes,
            exclude_suffixes=exclude_suffixes,
            max_attachment_size=max_attachment_size,
        )


def mbox_to_threads(
    input_path: Path,
    glob: str,
    output_path: Path,
    filename: Union[str, Callable[[str], str]] = "threads",
    tags: Union[dict[str, str], None, Callable[[str], dict[str, str]]] = None,
    directory_structure: bool = False,
):
    """Convert set of mbox files to a set of thread files.

    This is a convenience function that collects threads from a collection of mbox files.
    Additional fields can be either supplied.
    Messages are deduplicated withing each unique output filename.

    Args:
        input_path:  Path to the mbox file.
        glob:        Glob pattern to match the mbox file.
        output_path: Path to the output folder.
        filename:    Output filename to use for the output, or a function that returns it from the input filename.
        tags:        Tags to add to the threads, or a function that returns them from the input filename.
        directory_structure: Whether to recreate the directory structure under the input path.
    """
    if tags is None:
        tags = {}
    last_filename = ""
    last_output_path = Path()
    thread_pool = []
    generators = []
    known = {}
    for input_file in input_path.glob(glob):
        # Determine whether to recreate the directory structure
        if directory_structure:
            current_output = output_path / input_file.parent.relative_to(input_path)
        else:
            current_output = output_path
        # Determine the output filename from the input filename
        if callable(filename):
            current_filename = filename(input_file.relative_to(input_path).as_posix())
        else:
            current_filename = filename
        if current_filename != last_filename:
            if generators:
                output_count = save_threads(
                    last_output_path, last_filename, chain(*generators, thread_pool)
                )
                generators.clear()
                print(f"Created {output_count} thread filess for `{last_filename}`.")
            thread_pool.clear()
            known.clear()
            last_filename = current_filename
            print(f"Working on `{current_filename}`")
        # Get the tags if needed
        if callable(tags):
            my_tags = tags(input_file.relative_to(input_path).as_posix())
        else:
            my_tags = tags
        generators.append(
            collect_threads(parse_mbox(input_file), my_tags, thread_pool, known=known)
        )
        last_output_path = current_output
    if generators:
        output_count = save_threads(
            last_output_path, last_filename, chain(*generators, thread_pool)
        )
        print(f"Created {output_count} thread files for `{last_filename}`.")

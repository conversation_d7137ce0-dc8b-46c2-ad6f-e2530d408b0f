"""Process discourse sites.

- Extract site info with the site.json endpoint
- Pagenate and refresh the list of topics for all sites
- Refresh topic data (WIP)
"""

import argparse
import datetime
import gzip
import json
import logging
import random
import time
import uuid
from dataclasses import dataclass
from itertools import count
from pathlib import Path

import requests

import research.data.postgres_metastore.utils as pg_utils
from research.data.utils.request_manager import RequestTaskManager, TaskRequest

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# A standard header to satisfy moderately picky sites.
# The really strict ones, such as Cloudflare community, would do js challenges and we
# will need a headless browser for that.  We will respect their wishes to not be disturbed.
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}


@dataclass
class TopicListingPage:
    """One page of topic metadata listings."""

    status: str
    page: int
    users: list[dict]
    topics: list[dict]
    has_next_page: bool


def get_new_site_info():
    """Check if there are any new sites with no info.

    Try to retrieve site info for all of them.
    """
    updates = []
    for site_url, verify in pg_utils.execute(
        "SELECT base_url, verify FROM discourse_site WHERE info = '{}'"
    ):
        logger.info("Retrieving information from site %s", site_url)
        try:
            response = requests.get(
                f"{site_url}site.json", headers=HEADERS, verify=verify, timeout=30
            )
            # Check if the request was successful
            if response.status_code == 200:
                data = response.json()  # Parse the JSON data from the response
                updates.append(
                    (site_url, json.dumps(data))
                )  # Append a tuple of site_url and data to the updates list
            else:
                logger.info(
                    "Failed to retrieve site info for %s, status code: %s",
                    site_url,
                    response.status_code,
                )
        except requests.exceptions.Timeout:
            logger.info("Request timed out for %s", site_url)
        except requests.exceptions.RequestException as e:
            # For handling other types of exceptions
            logger.info("An error occurred for %s: %s", site_url, e)

    if updates:
        pg_utils.batch_execute(
            """
            UPDATE discourse_site
            SET info = (v.info)::jsonb
            FROM (VALUES %s) AS v(base_url, info)
            WHERE discourse_site.base_url = v.base_url
        """,
            updates,
        )


def retrieve_topic_listing(
    site_url: str, page: int, verify: bool = True, cooldown: float = 0.5
) -> TopicListingPage:
    """Retrieve one page of topic listing from a specific site."""
    # This API endpoint is undocumented, but works
    url = f"{site_url}latest.json"
    params = {
        "page": page,
        "order": "activity",
    }
    try:
        time.sleep(random.uniform(cooldown / 2, cooldown))
        response = requests.get(
            url, params=params, headers=HEADERS, verify=verify, timeout=30
        )
        # Check if the request was successful
        if response.status_code == 200:
            data = response.json()  # Parse the JSON data from the response
        else:
            logger.info(
                "Failed to retrieve topic listing for %s page %d, status code: %d",
                site_url,
                page,
                response.status_code,
            )
            return TopicListingPage("failed", page, [], [], False)
    except requests.exceptions.Timeout:
        logger.info("Request timed out for %s page %d", site_url, page)
        return TopicListingPage("timeout", page, [], [], False)
    except (requests.exceptions.RequestException, json.decoder.JSONDecodeError) as e:
        # For handling other types of exceptions
        logger.info("An error occurred for %s page %d: %s", site_url, page, e)
        return TopicListingPage("failed", page, [], [], False)
    users = data.get("users", [])
    topics = data.setdefault("topic_list", {}).get("topics", [])
    has_next_page = "more_topics_url" in data["topic_list"]
    return TopicListingPage("success", page, users, topics, has_next_page)


def insert_topics(site_id: str, site_url: str, topic_listing: TopicListingPage):
    if not topic_listing.topics:
        return
    values = [
        (topic["id"], topic["title"], topic["created_at"], topic["bumped_at"])
        for topic in topic_listing.topics
    ]
    pg_utils.batch_execute(
        f"""
        INSERT INTO discourse_topic (site_id, site_url, topic_id, title, created_at, bumped_at)
        SELECT
            '{site_id}', '{site_url}', v.topic_id, v.title, v.created_at::timestamptz, v.bumped_at::timestamptz
        FROM (VALUES %s) AS v(topic_id, title, created_at, bumped_at)
        ON CONFLICT (site_url, topic_id) DO
            UPDATE SET title=EXCLUDED.title, created_at=EXCLUDED.created_at, bumped_at=EXCLUDED.bumped_at
    """,
        values,
    )


def update_one_site(cooldown: float = 0.5):
    """Identify one site and update more pages of topics.

    Identify one site.  Process new updates until we see the updates earlier than
    the history we already have for that site.

    Returns:
        updated:  base_url for the updated site.
    """
    # Extract one site to update
    # Here we pick the most stale one
    query = """
        WITH target AS (
            SELECT
                id
            FROM discourse_site
            WHERE (
                  last_queried_at IS NULL
                  OR last_queried_at < 'yesterday'
                ) AND NOT disabled
            ORDER BY
                last_queried_at nulls first
            LIMIT 1
            FOR UPDATE SKIP LOCKED
        )
        UPDATE discourse_site
            SET last_queried_at = NOW()
        FROM target
        WHERE discourse_site.id = target.id

        RETURNING
            target.id,
            base_url,
            COALESCE(last_bumped_at, '2013-01-01'),
            verify
    """
    target = pg_utils.execute(query)
    if not target:
        return None
    site_id, base_url, last_bumped_at, verify = target[0]
    last_bumped_at = str(last_bumped_at)
    logger.info("Retrieving from %s until %s", base_url, last_bumped_at)

    newest_bumped_at = None
    for page in count():
        result = retrieve_topic_listing(base_url, page, verify, cooldown=cooldown)
        insert_topics(site_id, base_url, result)
        if not result.has_next_page or not result.topics:
            break
        bumped_at = max(entry["bumped_at"] for entry in result.topics)
        if newest_bumped_at is None or newest_bumped_at < bumped_at:
            newest_bumped_at = bumped_at
        if bumped_at < last_bumped_at:
            break
        time.sleep(1.0)

    if newest_bumped_at is None:
        newest_bumped_at = last_bumped_at
    pg_utils.execute(
        """
            UPDATE discourse_site
            SET last_bumped_at=%s, last_query_status=%s
            WHERE id=%s
        """,
        [
            newest_bumped_at,
            result.status,
            site_id,
        ],
    )
    return base_url, newest_bumped_at


def refresh_topics_registry(max_sites: int = 100, cooldown: float = 0.5):
    """Paginate through latest.json endpoint to get all topic metadata."""
    for _ in range(max_sites):
        result = update_one_site(cooldown=cooldown)
        if result is None:
            break
        site_url, newest_bumped_at = result
        logger.info(
            "Site updated: %s. Most recent post: %s", site_url, newest_bumped_at
        )


def download_topic_content(output_path: Path, n: int = 5000, cooldown: float = 0.5):
    """Identify a batch of at most n topics that need updates and download them."""
    query = """
        WITH target AS (
            SELECT site_id, topic_id
            FROM discourse_topic
            WHERE last_stored_post IS NULL OR last_stored_post < bumped_at
                AND (
                    last_retrieved_at < CURRENT_TIMESTAMP - INTERVAL '10 days'
                    OR last_retrieved_at IS NULL
                )
            ORDER BY
                bumped_at - last_stored_post DESC NULLS FIRST,
                DATE(last_retrieved_at) NULLS FIRST,
                RANDOM()
            LIMIT %s
            FOR UPDATE SKIP LOCKED
        ),
        updated AS (
            UPDATE discourse_topic AS t
            SET last_retrieved_at = NOW(),
                status='processing'
            FROM target
            WHERE t.site_id = target.site_id AND t.topic_id = target.topic_id
            RETURNING t.site_id, t.site_url, t.topic_id, t.bumped_at
        )
        SELECT
            updated.site_id,
            updated.site_url,
            updated.topic_id,
            s.verify
        FROM updated
        JOIN discourse_site AS s
            ON s.id = updated.site_id AND NOT s.disabled
    """

    tasks = []
    task_lookup = {}

    for site_id, site_url, topic_id, verify in pg_utils.execute(query, [n]):
        url = f'{site_url.rstrip("/")}/raw/{topic_id}'
        task = TaskRequest(site_url, url, verify=verify, decode_json=False)
        tasks.append(task)
        task_lookup[task.task_id] = site_id, site_url, topic_id
    manager = RequestTaskManager(
        cooldown_sec=cooldown, retry_cooldown=5.0, max_retries=2
    )

    # content:  site_url, topic_id, response code, status, stored timestamp, word_count
    metadata = []
    json_data = []
    ts_now = datetime.datetime.now().isoformat()
    for resp in manager.make_all_requests_sync(tasks):
        site_id, site_url, topic_id = task_lookup[resp.task_id]
        if resp.success and resp.result:
            raw_markdown = str(resp.result)
            doc = {
                "site_id": site_id,
                "site_url": site_url,
                "topic_id": topic_id,
                "retrieved_at": ts_now,
                "raw_markdown": raw_markdown,
            }
            json_data.append(doc)
            word_count = len(raw_markdown.split())
            metadata.append(
                [site_url, topic_id, resp.response_code, "success", ts_now, word_count]
            )
        else:
            if resp.response_code in (404, 410):
                new_ts = ts_now
            else:
                new_ts = "2013-01-01"
            if resp.response_code == -2:
                status = "blocked"
            else:
                status = "failed"
            metadata.append([site_url, topic_id, resp.response_code, status, new_ts, 0])

    logger.info("Number of metadata entries: %d", len(metadata))
    logger.info("Number of data entries: %d", len(json_data))
    # Store the json to target location
    output_file = output_path / f"{uuid.uuid4()}.json.gz"
    logger.info("Saving to %s", output_file)
    with gzip.open(output_file, "wt+") as f:
        for data in json_data:
            f.write(json.dumps(data) + "\n")

    logger.info("Updating metadata table")
    # Update metadata table
    update_query = """
        UPDATE discourse_topic AS t
           SET last_response_code=u.last_response_code,
               status=u.status,
               last_stored_post=GREATEST(t.bumped_at, (u.last_stored_post)::timestamptz),
               word_count=COALESCE(u.word_count, t.word_count)
        FROM (VALUES %s) AS u (
            site_url, topic_id, last_response_code, status, last_stored_post, word_count
        )
        WHERE t.site_url = u.site_url
            AND t.topic_id = u.topic_id
    """
    pg_utils.batch_execute(update_query, metadata)


def main():
    current_date = datetime.date.today()
    parser = argparse.ArgumentParser(description="Update discourse data.")
    parser.add_argument(
        "--storage-path",
        type=str,
        default=f"/mnt/efs/spark-data/shared/nl-datasets/discourse/topics/date={current_date}/",
        help="Path to store the JSONL files.",
    )
    parser.add_argument(
        "--batchsize",
        type=int,
        default=5000,
        help="Number of topics to update per batch",
    )
    parser.add_argument(
        "--cooldown",
        type=float,
        default=5.0,
        help="Cooldown time between requests to same site.",
    )
    parser.add_argument(
        "--batches", type=int, default=10, help="Number of batches to update."
    )
    args = parser.parse_args()

    logger.info("Retrieving info for new sites")
    get_new_site_info()

    logger.info("Refreshing topic registry")
    refresh_topics_registry(cooldown=args.cooldown)

    storage_path = Path(args.storage_path)
    logger.info("Downloading topics. Output will be stored at %s", storage_path)

    storage_path.mkdir(parents=True, exist_ok=True)
    for i in range(args.batches):
        logger.info("Batch #%d. Processing %d topics", i, args.batchsize)
        download_topic_content(storage_path, n=args.batchsize, cooldown=args.cooldown)


if __name__ == "__main__":
    logging.basicConfig(level="INFO")
    main()

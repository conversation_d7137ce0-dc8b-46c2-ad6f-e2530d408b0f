CREATE TABLE discourse_site(
    id uuid not null default uuid_generate_v4(),
    base_url varchar not null,
    info jsonb not null default '{}'::jsonb,
    created_at timestamptz not null default NOW(),
    updated_at timestamptz not null default NOW(),
    last_bumped_at timestamptz, -- timestamp of the last updates synced from server
    last_queried_at timestamptz,
    last_query_status varchar not null default '',
    verify boolean not null default true, --- If we verify SSH certificate through requests
    disabled boolean not null default false
);

CREATE UNIQUE INDEX idx_discourse_site_url ON discourse_site USING btree(base_url);



CREATE TABLE discourse_topic(
    site_id uuid not null,
    site_url varchar not null,
    topic_id bigint not null,
    title varchar,
    created_at timestamptz not null,
    bumped_at timestamptz not null,     -- When was a topic last bumped based on metadata
    last_stored_post timestamptz,       -- The most recent post in a topic that were already stored
    last_retrieved_at timestamptz,
    last_response_code int,
    word_count int,
    status varchar not null default 'discovered'
);

CREATE UNIQUE INDEX idx_discourse_site_url_site_id
   ON discourse_topic USING btree(site_url, topic_id);

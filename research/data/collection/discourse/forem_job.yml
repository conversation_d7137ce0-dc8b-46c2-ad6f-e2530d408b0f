apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: discourse-refresh-job
spec:
  schedule: "5 9 * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: discourse-refresh-worker
            image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/github-download-worker:spark-3.4.2-28
            imagePullPolicy: IfNotPresent
            command: ["python"]
            args:
            - "-m"
            - "research.data.collection.discourse.process_discourse"
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: metastore-postgresql
                  key: password
            - name: PGHOST
              value: "metastore-postgresql"
            - name: PGDATABASE
              value: "metastore"
            - name: PGUSER
              value: "augment"
            resources:
              limits:
                cpu: "1"
              requests:
                cpu: "1"
                memory: "2Gi"
          restartPolicy: OnFailure
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                    - key: topology.kubernetes.io/region
                      operator: In
                      values:
                        - LAS1

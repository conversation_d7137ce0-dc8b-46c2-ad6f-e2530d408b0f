"""This inspects the readme file and add any missing sites to the table."""

import sys
from pathlib import Path

import research.data.postgres_metastore.utils as pg_utils

if __name__ == "__main__":
    if len(sys.argv) > 1:
        filename = sys.argv[1]
    else:
        filename = "README.md"
    batch = []
    with Path(filename).open() as f:
        for line in f:
            if not line.startswith("https://") and not line.startswith("http://"):
                continue
            line = line.strip()
            line = line.rstrip("/") + "/"
            batch.append([line])

    inserted = pg_utils.batch_execute_list(
        """
        INSERT INTO discourse_site(base_url)
        VALUES %s
        ON CONFLICT DO NOTHING
        returning base_url
    """,
        batch,
    )
    print(f"Inserted {len(inserted)} new sites:")
    for site in inserted:
        print(site)

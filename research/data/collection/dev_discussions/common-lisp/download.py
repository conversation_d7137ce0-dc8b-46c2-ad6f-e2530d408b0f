"""Script for downloading all common-list mail archives."""
from pathlib import Path

import requests
from bs4 import BeautifulSoup

if __name__ == "__main__":
    base_url = "https://mailman.common-lisp.net/pipermail/"
    response = requests.get(base_url, timeout=60)
    soup = BeautifulSoup(response.text, "html.parser")

    # Find all folder links
    folder_links = [
        a["href"]
        for a in soup.find_all("a", href=True)
        if not a["href"].endswith("-cvs/")
    ]

    for folder in folder_links:
        folder_name = folder.strip("/")
        folder_path = Path(".") / folder_name  # Adjust the path as needed
        folder_path.mkdir(parents=True, exist_ok=True)

        # Fetch the folder page
        folder_url = base_url + folder
        folder_response = requests.get(folder_url, timeout=60)
        folder_soup = BeautifulSoup(folder_response.text, "html.parser")

        # Find all .txt and .txt.gz file links
        file_links = [
            a["href"]
            for a in folder_soup.find_all("a", href=True)
            if a["href"].endswith(".txt") or a["href"].endswith(".txt.gz")
        ]

        for file_link in file_links:
            file_url = folder_url + file_link
            # Use wget or requests to download the file
            # For example, using requests:
            file_response = requests.get(file_url, timeout=60)
            file_path = folder_path / file_link
            with file_path.open("wb") as f:
                f.write(file_response.content)

            print(f"Downloaded {file_link} to {folder_path}")

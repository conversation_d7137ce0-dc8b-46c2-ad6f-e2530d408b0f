#!/bin/bash

# Squeak project started in 1997
start_year=1997
# Define the end condition as the current year and month
end_year=$(date +"%Y")
end_month=$(date +"%m")

# Read each line in sites.txt as a domain name
while IFS= read -r site; do
    # Skip empty lines
    if [ -z "$site" ]; then
        continue
    fi

    domain="${site}@lists.squeakfoundation.org"
    # Iterate through each year and month, downloading the archives
    for ((year=start_year; year<=end_year; year++)); do
        for ((month=1; month<=12; month++)); do
            # Break the loop if the current year and month are reached
            if [ "$year" -eq "$end_year" ] && [ "$month" -gt "$end_month" ]; then
                break
            fi

            # Zero-padding for month
            printf -v padded_month "%02d" $month

            # Calculate the last day of the month
            last_day=$(cal $month $year | awk 'NF {DAYS = $NF}; END {print DAYS}')

            # Construct the URL
            url="https://lists.squeakfoundation.org/archives/list/${domain}/export/${domain}-${year}${padded_month}.mbox.gz?start=${year}-${padded_month}-01&end=${year}-${padded_month}-${last_day}"

            # Define the directory and file path
            directory="${domain}"
            file_path="${directory}/${year}-${padded_month}.mbox.gz"

            # Create directory if it doesn't exist
            mkdir -p "$directory"

            # Download the file if it is not empty
            wget --spider "$url" 2>&1 | grep -q 'Length: [^0]'
            if [ $? -eq 0 ]; then
                echo "Downloading: $url"
                wget -O "$file_path" "$url"
                echo "Saved to: $file_path"
            else
                echo "No data for $domain in ${year}-${padded_month}, skipping."
            fi
        done
    done
done < "sites.txt"

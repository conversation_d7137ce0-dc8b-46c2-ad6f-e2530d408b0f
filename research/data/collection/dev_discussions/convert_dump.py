"""Convert mbox dumps to json format, also extract key fields and compute text hash."""
import argparse
import re
from pathlib import Path

from research.data.collection.utils.mbox import mbox_to_threads


def get_tags(filename: str):
    """Extract group name from directory structure.

    Directory structure is generally:
    `site_name/list_name/file_name.mbox`

    We need to extract the `site_name` and `list_name`
    """
    match = re.match(r"(.+?)/(.+?)/(.+?)\.mbox", filename)
    if match:
        site_name = match.group(1)
        list_name = match.group(2)
        return {
            "site": site_name,
            "list": list_name,
        }
    else:
        return {}


def get_filename(filename: str):
    """Compute file name from directory structure.

    Filename is concatenated from site and list names.
    """
    tags = get_tags(filename)
    if "site" in tags and "list" in tags:
        return f"{tags['site']}_{tags['list']}"
    else:
        return filename


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Convert mbox dumps to json format and extract key fields."
    )
    parser.add_argument(
        "input_path",
        type=str,
        help="The input directory path where mbox files are located.",
    )
    parser.add_argument(
        "output_path",
        type=str,
        help="The output directory path where jsonl files will be saved.",
    )

    args = parser.parse_args()

    mbox_to_threads(
        Path(args.input_path),
        "*/*/*.mbox",
        Path(args.output_path),
        get_filename,
        get_tags,
        True,
    )

#!/bin/bash

# Fedora started in 2002
start_year=2002
# Define the end condition as the current year and month
end_year=$(date +"%Y")
end_month=$(date +"%m")

# Read each line in sites.txt as a domain name
while IFS= read -r domain; do
    # Skip empty lines
    if [ -z "$domain" ]; then
        continue
    fi

    # Iterate through each year and month, downloading the archives
    for ((year=start_year; year<=end_year; year++)); do
        for ((month=1; month<=12; month++)); do
            # Break the loop if the current year and month are reached and we're about to process the next month
            if [ "$year" -eq "$end_year" ] && [ "$month" -gt "$end_month" ]; then
                break
            fi

            # Zero-padding for month
            printf -v padded_month "%02d" $month

            # Calculate the start of the next month
            next_month=$((month + 1))
            next_month_year=$year
            # If the next month is January, increment the year
            if [ "$next_month" -eq 13 ]; then
                next_month=1
                next_month_year=$((year + 1))
            fi
            printf -v padded_next_month "%02d" $next_month

            # Construct the URL to go from the first day of the current month to the first day of the next month
            url="https://lists.fedoraproject.org/archives/list/${domain}/export/${domain}-${year}${padded_month}.mbox.gz?start=${year}-${padded_month}-01&end=${next_month_year}-${padded_next_month}-01"

            # Define the directory and file path
            directory="${domain}"
            file_path="${directory}/${year}-${padded_month}.mbox.gz"

            # Create directory if it doesn't exist
            mkdir -p "$directory"

            # Download the file if it is not empty
            wget --spider "$url" 2>&1 | grep -q 'Length: [^0]'
            if [ $? -eq 0 ]; then
                echo "Downloading: $url"
                wget -O "$file_path" "$url" \
                    --wait=1 --random-wait \
                    --waitretry=30 \
                    -e robots=off
                echo "Saved to: $file_path"
            else
                echo "No data for $domain in ${year}-${padded_month}, skipping."
            fi
        done
    done
done < "$1"

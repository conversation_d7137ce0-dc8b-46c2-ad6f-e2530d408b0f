#!/bin/bash

# Base URL
base_url="https://mail.zope.dev/pipermail/"

# Read from lists.txt
while IFS= read -r list; do
    # Create a directory for the list
    mkdir -p "$list"

    # Use wget to download the specific file types directly linked on the page
    wget --recursive --level=1 --no-parent --no-directories \
         --accept txt,txt.gz,mbox,mbox.gz \
         --directory-prefix="$list" \
         --no-clobber \
	 -e robots=off --random-wait --wait=3 \
         "$base_url/$list/"

done < "lists.txt"

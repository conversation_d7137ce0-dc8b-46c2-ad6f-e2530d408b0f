"""Convert mbox dumps to json format, also extract key fields and compute text hash."""
import re
from pathlib import Path

from research.data.collection.utils.mbox import mbox_to_threads


def get_tags(filename: str):
    """Extract group name from directory structure.

    The apache directory structure is:
    `site=site_name/list=list_name/file_name.mbox`

    We need to extract the `site_name` and `list_name`
    """
    match = re.match(r"site=(.+?)/list=(.+?)/(.+?)\.mbox", filename)
    if match:
        site_name = match.group(1)
        list_name = match.group(2)
        return {
            "site": site_name,
            "list": list_name,
        }
    else:
        return {}


def get_filename(filename: str):
    """Compute file name from directory structure.

    Filename is concatenated from site and list names.
    """
    tags = get_tags(filename)
    if not tags:
        return "unknown"
    return f"{tags['site']}-{tags['list']}"


if __name__ == "__main__":
    base_path = Path("/mnt/efs/spark-data/shared/nl-datasets/dev-discussions")
    input_path = base_path / "mbox/apache"
    output_path = base_path / "threads/apache"

    mbox_to_threads(
        input_path,
        "site=*/list=*/*.mbox",
        output_path,
        get_filename,
        get_tags,
        directory_structure=True,
    )

#!/bin/bash

# Define start and end years
start_year=1995
end_year=2024

# Read each line in apache-sites.txt
while IFS= read -r site; do
    # Iterate over each list type
    for list in dev user; do
        # Loop through the years
        directory="site=${site}/list=${list}"
        # Create the directory if it doesn't exist
        mkdir -p "$directory"
        for year in $(seq $start_year $end_year); do
            # Loop through the months
            for month in {01..12}; do
                # Format the date
                date="${year}-${month}"

                # Define the URL
                url="https://lists.apache.org/api/mbox.lua?list=${list}&domain=${site}&d=${date}"

                # Define the directory structure and filename
                filename="${directory}/${list}-${date}.mbox"


                # Use wget to download the file temporarily
                wget "$url" -O temp.mbox

                # Check if the downloaded file is empty
                if [ -s temp.mbox ]; then
                    # If the file is not empty, move it to the correct location
                    mv temp.mbox "$filename"
                    echo "Downloaded and saved: $filename"
                else
                    # If the file is empty, delete the temporary file
                    rm temp.mbox
                fi
            done
        done
    done
done < "apache-sites.txt"

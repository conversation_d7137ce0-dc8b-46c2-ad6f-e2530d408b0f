Generally, we look at `https://marc.info/` to find worthy lists to download.
Apache, FreeBSD, Postgres and Python etc got their own system of lists so we go directly there.


Developer Mailing Lists
===============

- Apache        [apache-2.0]    70G
- Python        [python]        7.2G + 5.3G
   - both mailman2 and mailman3 versions
   - discourse version not yet scraped
- SourceHut     [mit]           96M
- FreeBSD       [bsd-2-clause]  11G
- PostgreSQL    [postgresql]    14G
- Ruby          [bsd-2-clause]  33M
- nginx         [bsd-2-clause]  288M
- anl lists     [see readme]    697M
- xiph          [bsd-3-clause]  577M
- openstack     [apache-2.0]    1.1G
- squeak        [MIT+apache]    2.5G
- llvm          [llvm]          1.1G
- tor           [bsd-3-clause]  110M
- common lisp   [apache-2.0]    204M
- OpenStreetMap [apache+isc]    445M
- erlang        [apache-2.0]    232M
- MacPorts      [bsd-3-clause]  193M
- openssh       [openssh]       82M
- openssl       [apache-2.0]    48M
- DragonflyBSD  [bsd]           3.8G
- Fedora        [mit]           12G
- MacOSForge    [bsd&apache]	817M
- zope          [zpl(bsd like)] 833M
- swift         [apache-2.0]    261M
- mono          [apache-2.0]    565M
Non-developer usage mailing lists
===============

For general usage guidance mail list that contains no development of the package itself,
no license restrictions are made
- owasp                         51M
- cryptography and bitcoin      196M
- CMU connectionists            148M
- SIAM                          764k
- TeX user group                350M

#!/usr/bin/env bash

script_path=$(readlink -f "$0")

# Create a temp dir for building
workdir=$(mktemp -d)
echo Building image at $workdir
cp Dockerfile *.py $workdir/

# Build the container
# Base image tag is in file ../../../../environments/spark_cpu_tag.txt
CPU_IMAGE_TAG=$(cat ../../../../environments/spark_cpu_tag.txt)
# extract the part after `:` in the base image tag to be the tag for the new image
TAG=${CPU_IMAGE_TAG#*:}
REGISTRY=au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud
BASE_IMAGE_TAG="github-download-worker:$TAG"
echo Using base image tag $BASE_IMAGE_TAG

IMAGE=wikimedia-processor
docker pull $REGISTRY/$BASE_IMAGE_TAG
# Build the image
echo Building $BASE_IMAGE_TAG
docker build --no-cache \
             --build-arg BASE_IMAGE_TAG=$BASE_IMAGE_TAG \
             --build-arg REGISTRY=$REGISTRY \
             -t $IMAGE:$TAG $workdir

# After build, tag it then push it
# probably shouldn't use latest but will worry about that later
docker tag $IMAGE:$TAG $REGISTRY/$IMAGE:$TAG
docker push $REGISTRY/$IMAGE:$TAG

rm -rf $workdir

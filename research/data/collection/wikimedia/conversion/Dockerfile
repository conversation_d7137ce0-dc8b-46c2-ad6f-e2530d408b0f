# This should be the github worker image
ARG BASE_IMAGE_TAG
ARG REGISTRY
FROM $REGISTRY/${BASE_IMAGE_TAG}

USER root

# Install pandoc 3.11 and install it
RUN wget https://github.com/jgm/pandoc/releases/download/3.1.11.1/pandoc-3.1.11.1-linux-amd64.tar.gz && \
    tar -xvf pandoc-3.1.11.1-linux-amd64.tar.gz && \
    mv pandoc-3.1.11.1/bin/pandoc /usr/local/bin/pandoc && \
    rm -rf pandoc-3.1.11.1-linux-amd64.tar.gz && \
    rm -rf pandoc-3.1.11.1

# Install most recent mwparserfromhell from github
RUN git clone https://github.com/earwig/mwparserfromhell.git && \
    cd mwparserfromhell && \
    python3 setup.py install && \
    cd .. && \
    rm -rf mwparserfromhell

USER augment

COPY run_wiki_dump_conversion.py .
COPY wikitext_adaptor.py .

ENTRYPOINT []

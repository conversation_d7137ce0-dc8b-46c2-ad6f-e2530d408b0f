"""Convert wikipedia dump articles to markdown.

This takes a wikipedia dump articles xml.bz2 file, and extract
the page contents.  The input contents are converted from
mediawiki format to markdown and saved to jsonl file then
compressed.

Conversion is first done with pandoc; for pages where the
conversion failed, we use mwparserfromhell to convert them
to near plain text.

The output is a jsonl file with the following schema:
"""
import bz2
import gzip
import json
import tempfile
import traceback as tb
import xml.etree.ElementTree as cET
from contextlib import contextmanager
from dataclasses import dataclass
from pathlib import Path
from typing import Iterable, Iterator, Optional

import boto3
from dataclasses_json import dataclass_json
from wikitext_adaptor import WikiTextAdaptor, WikiTextContent

import research.data.postgres_metastore.utils as pg_utils
from research.core.augment_secrets import get_coreweave_keys


@dataclass_json
@dataclass
class Page:
    """A wikipedia page."""

    title: str
    """The title of the page."""

    redirect: Optional[str]
    """The title of the redirect page, if any."""

    page_id: str
    """The page id."""

    raw_wikitext: str
    """The raw wikitext of the page."""

    content: Optional[WikiTextContent] = None
    """The processed content of the page."""


class Writer:
    """Writer handles the writing of the jsonl file and compression."""

    def __init__(
        self,
        output_path: Path,
        name: str,
        batch_size: int = 5000,
        compress: bool = False,
    ):
        """Initialize the writer.

        Args:
            output_path: path to the output file.
            name: name of the dump.
            batch_size: number of pages to buffer before writing to disk.
            compress: whether to compress the output file.
        """
        self.output_path = Path(output_path)
        self.name = name
        self.buffer: list[Page] = []
        self.unknowns = 0
        self.batch_id = 0
        self.batch_size = batch_size
        self.compress = compress
        # Create the output directory if not yet exists
        self.output_path.mkdir(parents=True, exist_ok=True)

    def write(self, page: Page):
        if not page.page_id and not page.content and not page.raw_wikitext:
            # skip completely empty page
            return
        if not page.title and page.raw_wikitext:
            page.title = f"Unknown title {self.unknowns}"
            self.unknowns += 1
        self.buffer.append(page)
        if len(self.buffer) >= self.batch_size:
            self.flush()

    def flush(self):
        """Flush the buffer to the output file."""
        target = self.output_path / f"{self.name}-part_{self.batch_id}.jsonl"
        if self.compress:
            target = target.with_suffix(".jsonl.gz")
            out_file = gzip.open(target, "wt")
        else:
            out_file = target.open("w")
        print(f"Saving {len(self.buffer)} docs to {target}", flush=True)
        with out_file:
            for page in self.buffer:
                out_file.write(page.to_json() + "\n")
        self.buffer.clear()
        self.batch_id += 1


def get_pages(source: Path) -> Iterable[Page]:
    """Extract and filter the pages from the xml file.

    Args:
        source: path to the input file.  can be an xml or bz2 file.

    Yields:
        Page objects
    """
    # if the input is a bz2 file, we need to use the library to wrap it
    if source.suffix == ".bz2":
        source = bz2.open(source, "rt", encoding="utf-8")

    for _, elem in cET.iterparse(source, events=("end",)):
        if not elem.tag.endswith("}page"):
            continue
        namespace = elem.tag.rsplit("}", maxsplit=1)[0] + "}"
        title_elem = elem.find(f"./{namespace}title") or elem.find(f"{namespace}title")
        if title_elem is None:
            title = None
        else:
            title = title_elem.text
        ns_elem = elem.find(f"./{namespace}ns") or elem.find(f"{namespace}ns")
        if ns_elem is None:
            ns = None
        else:
            ns = ns_elem.text
        page_id = (elem.find(f"./{namespace}id") or elem.find(f"{namespace}id")).text
        redirect_elem = elem.find(f"./{namespace}redirect") or elem.find(
            f"{namespace}redirect"
        )
        if redirect_elem is None:
            redirect = None
        else:
            redirect = redirect_elem.attrib.get("title", redirect_elem.text)

        # Filter pages that are not in the "main" namespace.
        # see https://en.wikipedia.org/wiki/Wikipedia:Namespace
        if ns != "0":
            elem.clear()
            continue

        text = elem.find(f"./{namespace}revision/{namespace}text").text
        elem.clear()

        yield Page(title, redirect, page_id, text)


def convert_dump(
    input_path: Path,
    output_path: Path,
    lang: str,
    dump_name: str,
):
    """Convert a wikipedia dump articles xml.bz2 file to jsonl.

    Args:
        input_path: path to the input file.  can be an xml or bz2 file.
        output_path: path to the output directory.
        lang: language of the wiki.
        dump_name: name of the dump.
    """
    writer = Writer(output_path, dump_name, compress=True)
    for page in get_pages(input_path):
        # filter out empty redirect pages
        if page.redirect is not None:
            # No conversion if redirect page
            writer.write(page=page)
            continue
        page_adaptor = WikiTextAdaptor(page.raw_wikitext, lang)

        try:
            content = page_adaptor.extract_content()
            page.content = content
        except Exception as e:  # pylint: disable=broad-except
            print(f"Failed to convert {page.title} with mwparserfromhell: {e}")
            tb.print_exc()

        writer.write(page=page)

    writer.flush()


@dataclass
class Task:
    """A task to be processed."""

    task_id: str
    """The task id."""

    object_path: str
    """The path to the object to be processed."""


@contextmanager
def get_task() -> Iterator[Task]:
    """Get the next task from the queue."""
    query = """
        WITH available AS (
           SELECT id
           FROM wiki_process_queue
           WHERE status = 'queued'
           ORDER BY created_at ASC
           LIMIT 1
           FOR UPDATE SKIP LOCKED
        )
        UPDATE wiki_process_queue
        SET
            status = 'processing',
            updated_at = NOW(),
            started_at = NOW()
        FROM available
        WHERE wiki_process_queue.id = available.id
            AND wiki_process_queue.status = 'queued'
        RETURNING wiki_process_queue.id, object_path
    """
    results = pg_utils.execute(query)
    if not results:
        raise RuntimeError("No more tasks left to process.")
    task_id, object_path = results[0]
    task = Task(task_id, object_path)
    try:
        yield task
    except Exception as e:  # pylint: disable=broad-except
        print(f"Failed during task: {e}")
        tb.print_exc()
        # set the task to failed
        pg_utils.execute(
            """
            UPDATE wiki_process_queue
            SET status = 'failed',
                updated_at = NOW(),
                error_info = %s
            WHERE id = %s
            """,
            [
                json.dumps({"message": str(e), "traceback": tb.format_exc()}),
                task.task_id,
            ],
        )
    else:
        pg_utils.execute(
            """
            UPDATE wiki_process_queue
            SET status = 'done', updated_at = NOW(), deleted_at = NOW()
            WHERE id = %s
            """,
            [task.task_id],
        )


def main():
    with get_task() as task:
        print(f"Processing task {task.task_id} with object {task.object_path}")
        # first download the object from s3
        if not task.object_path.startswith("raw/wikimedia/"):
            raise RuntimeError("Can only process wiki dumps")
        collection = task.object_path.split("/")[2]
        dump_name = task.object_path.split("/")[3]
        dump_date = task.object_path.split("/")[4]
        if not dump_name.endswith(collection):
            raise RuntimeError(f"Incorrect dump name: {dump_name}")
        lang = dump_name[:-4]

        access_key, secret_key = get_coreweave_keys()
        s3 = boto3.client(
            "s3",
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            endpoint_url="https://object.las1.coreweave.com",
        )
        with tempfile.TemporaryDirectory() as tmpdir:
            local_source = Path(tmpdir) / f"dump-{task.task_id}.xml.bz2"
            print(f"Downloading {task.object_path} to {local_source}")
            s3.download_file(
                "nl-datasets",
                task.object_path,
                str(local_source),
            )
            local_output = Path(tmpdir) / "converted"
            convert_dump(
                local_source,
                local_output,
                lang,
                task.task_id,
            )
            print(f"Uploading output files in {local_output} to s3")
            for file in local_output.glob("*.jsonl.gz"):
                upload_path = f"processed/wikimedia_jsonl/collection={collection}/lang={lang}/dump_date={dump_date}/{file.name}"
                s3.upload_file(
                    str(file),
                    "nl-datasets",
                    upload_path,
                )


if __name__ == "__main__":
    main()

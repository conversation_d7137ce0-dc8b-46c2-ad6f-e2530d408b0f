apiVersion: apps/v1
kind: Deployment
metadata:
  name: wikimedia-convert
  labels:
    app: wikimedia-convert
spec:
  replicas: 32
  selector:
    matchLabels:
      app: wikimedia-convert
  template:
    metadata:
      labels:
        app: wikimedia-convert
    spec:
      containers:
      - name: wikimedia-convert-worker
        image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/wikimedia-processor:spark-3.4.2-16
        imagePullPolicy: Always
        resources:
          requests:
            cpu: 1
            memory: "2Gi"      # Set the requested memory
        command: ["python"]
        args:
        - "run_wiki_dump_conversion.py"
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: metastore-postgresql
              key: password
        - name: PGHOST
          value: "metastore-postgresql"
        - name: PGDATABASE
          value: "metastore"
        - name: PGUSER
          value: "augment"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                - key: topology.kubernetes.io/region
                  operator: In
                  values:
                    - LAS1

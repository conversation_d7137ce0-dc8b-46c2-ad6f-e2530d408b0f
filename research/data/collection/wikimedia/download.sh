# download raw wikimedia dumps from a mirror
# we use the california mirror here `https://wikimedia.bringyour.com/`

SITE="https://wikimedia.bringyour.com"

# the s3 folder where we store the raw dumps
S3_FOLDER="s3://nl-datasets/raw/wikimedia"

# get all the files to download and save it to file `url_list.txt`
wget $SITE/rsync-filelist-last-1-good.txt -O url_list.txt

# For each file, download the raw files, and save it to the s3 folder
# Terminate the script if any downlaod fails
for url in $(cat url_list.txt); do
    # if the first character is '/' in the url, remove it
    if [[ $url == /* ]]; then
        url=${url:1}
    fi
    # download raw file and preserve directory structure
    local_filename="raw_dump/$url"
    # $url contains path.  extract and create the directory
    local_dirname=$(dirname "$local_filename")
    mkdir -p $local_dirname
    echo "Downloading $url to $local_filename"
    wget -q -O $local_filename $SITE/$url
    # check exit status and exit if any error
    if [ $? -ne 0 ]; then
        echo "Error downloading $url"
        exit 1
    fi
    # upload to s3; eject out of the script if any error occurs
    s3cmd put $local_filename $S3_FOLDER/$url
    if [ $? -ne 0 ]; then
        echo "Error uploading $url"
        exit 1
    fi
    # clean up the local file
    rm $local_filename
done

"""Filter and convert wikimedia dump articles to markdown documents."""

import pyspark.sql.functions as F

from research.data.spark import k8s_session

spark = k8s_session(
    max_workers=20,
)

df = spark.read.parquet("s3a://nl-datasets/processed/wikimedia/")

# In v1 only include top languages
TOP_LANG = [
    "en",
    "ru",
    "de",
    "fr",
    "es",
    "it",
    "ceb",
    "ja",
    "zh",
    "uk",
    "pl",
    "pt",
    "nl",
    "ar",
    "sv",
    "he",
    "cs",
    "hu",
    "vi",
    "id",
]

df = (
    df.filter(df.lang.isin(TOP_LANG) & df.redirect.isNull())
    .select(
        F.col("title"),
        <PERSON>.col("page_id"),
        <PERSON>.col("lang"),
        F.col("collection"),
        <PERSON>.col("redirect"),
        <PERSON><PERSON>concat(
            F.lit("# "),
            <PERSON><PERSON>col("title"),
            <PERSON>.lit("\n\n"),
            <PERSON>.col("content.text"),
        ).alias("content"),
        <PERSON>.col("content.categories"),
        <PERSON>.col("content.code_languages"),
        <PERSON>.col("content.wikilinks"),
        F.col("content.external_links"),
        F.col("content.has_code"),
    )
    .repartition(5000)
    .write.mode("overwrite")
    .parquet("/mnt/efs/spark-data/shared/nl-datasets/wikimedia/formatted/")
)


spark.stop()

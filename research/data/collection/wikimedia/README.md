# Download and process wikimedia dumps

Download the most recent dump of files from wikimedia, which are XML files, where
the content block is with a MediaWiki markup format.  The files are compressed with 7z and bz2.

These need to be converted into markdown/plain text so that we can train on them.
They also need to move from the hard to use but human readable XML format to parquet.

This generally is divided into 3 steps:

- Download and store in s3 storage
- Swarm of processes to read individual .xml.bz2 file and convert the contained mediawiki to markdown, and save as .jsonl.gz
- Batch job that converts the .jsonl.gz files to parquet.

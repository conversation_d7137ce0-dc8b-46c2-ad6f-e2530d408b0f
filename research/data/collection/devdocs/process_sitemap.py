"""Process sitemap.xml to text and apply filters to keep only and tutorial files."""

import argparse
import re
from pathlib import Path

import lxml.etree as ET


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "input",
        type=str,
        help="The input directory path where sitemap.xml is located.",
    )
    parser.add_argument(
        "output",
        type=str,
        help="The output directory path where text files will be saved.",
    )
    parser.add_argument(
        "--field",
        type=str,
        default="url",
        help="Which field to extract urls from. Default url",
    )
    return parser.parse_args()


def process_sitemap(input_path: Path, output_path: Path, field: str = "url"):
    """Process sitemap.xml to text and apply filters to keep only and tutorial files.

    Creates one sitemap file per language. (en, es, zh-CN etc)
    """
    writers = {}
    output_path.mkdir(parents=True, exist_ok=True)
    ns = "http://www.sitemaps.org/schemas/sitemap/0.9"
    version_pattern = re.compile(r"\/\d+\.\d+\.\d+\/|\/\d+\.\d+\/")
    known = set()
    for file_path in input_path.rglob("*.xml"):
        print(f"Processing {file_path}")
        root = ET.parse(file_path, ET.XMLParser(remove_blank_text=True)).getroot()
        for url in root.findall(f"{{{ns}}}{field}"):
            path = url.find(f"{{{ns}}}loc").text
            # Do not keep those that have a version number like `/0.34.0/` or `/10.5/`;
            # For versioned docs, only keep latest version
            if version_pattern.search(path):
                continue
            if "?hl=" in path:
                path, lang = path.split("?hl=", 1)
            else:
                lang = "en"
            if "?" in path:
                path = path.split("?")[0]
            if path in known:
                continue
            known.add(path)
            if "?" in lang:
                print(path, lang)
            if lang not in writers:
                writers[lang] = open(output_path / f"{lang}.txt", "w+")
            writers[lang].write(f"{path}\n")
    for writer in writers.values():
        writer.close()


if __name__ == "__main__":
    args = parse_args()
    process_sitemap(Path(args.input), Path(args.output), args.field)

"""Process devdocs HTML files to readable dataset.

Raw files cannot even be stored on a Vast drive because they contain special characters.
They cannot be opened with spark either.

We convert them to json first, then perform distributed conversion
from HTML to markdown later in a different script.

For processing devdoc.io or nvidia docs, see README.md

In principle, you can download any documentation site and use this to convert all HTMLs
to a raw jsonl doc set.
"""

import argparse
import re
from itertools import chain
from pathlib import Path

from research.data.collection.devdocs.doc_entry import DocEntry, save_docs_as_jsonl


def get_components(file_path: Path, root_path: Path, path_as_url: bool) -> DocEntry:
    """Extract components from file path.

    Components include:
    - project
    - version: None if not specified
    - path
    - filename: this does not include the suffix.
    - url: network url
    """
    file_path = file_path.resolve()
    root_path = root_path.resolve()
    if not file_path.is_relative_to(root_path):
        raise ValueError(f"{file_path} is not inside {root_path}")
    relative_path = file_path.relative_to(root_path)
    first_part = relative_path.parts[0]
    parts = first_part.split("~")
    project = parts[0]
    if len(parts) == 2:
        version = parts[1]
    elif len(parts) > 2:
        raise ValueError(f"Path {relative_path} has incorrect top level format")
    else:
        version = None
    path = Path(*relative_path.parts[1:]).with_suffix(".md")

    name = Path(relative_path.parts[-1]).stem
    content = file_path.read_text()
    if path_as_url:
        url = "https://" + Path(*relative_path.parts[1:]).with_suffix("").as_posix()
    else:
        url = ""
    return DocEntry(
        project=project,
        version=version,
        doc_type=None,
        path=str(path),
        page_name=name,
        content=content,
        doc_format=file_path.suffix.lstrip(".").lower(),
        url=url,
    )


def iterate_html(
    html_path: Path,
    reject_regex_path: str = "",
    reject_regex_content: str = "",
    suffixes: tuple[str, ...] = ("html", "htm", "md", "tex", "adoc", "rst"),
    dedupe_filename: bool = False,
    path_as_url: bool = False,
):
    """Collect files from a folder of nested HTMLs to json.

    Fields to collect:
    project, version, path, filename, content
    """
    current_project = None
    skipped = 0
    rejected = 0
    converted = 0
    known = set()
    if reject_regex_path:
        path_reject_pattern = re.compile(reject_regex_path)
    else:
        path_reject_pattern = None

    if reject_regex_content:
        content_reject_pattern = re.compile(reject_regex_content)
    else:
        content_reject_pattern = None

    for file_id, path in enumerate(
        chain(*(html_path.rglob(f"*.{suffix}") for suffix in suffixes))
    ):
        # Skip directories
        if not path.is_file():
            continue
        if dedupe_filename:
            if path.name in known:
                print(f"Skipping {path} due to deduplication.")
                skipped += 1
                continue
            known.add(path.name)
        if path_reject_pattern and path_reject_pattern.search(str(path)):
            rejected += 1
            print(f"Rejected {path} due to regex {reject_regex_path}")
            continue
        print(path)
        if path.name.startswith("._"):
            skipped += 1
            continue
        try:
            file_entry = get_components(path, html_path, path_as_url)
            if content_reject_pattern and content_reject_pattern.search(
                file_entry.content
            ):
                rejected += 1
                print(f"Rejected content of {path} due to regex {reject_regex_content}")
                continue
            if ">Member-only story<" in file_entry.content:
                print(content_reject_pattern)
                print(file_entry.content)
                raise RuntimeError("Member-only content!")
        except UnicodeDecodeError:
            print(f"Unable to process file {path} due to decoding.")
            raw_content = path.read_bytes()
            decoded = raw_content.decode("utf-8", errors="replace")
            num_non_decodable_bytes = decoded.count("�")
            print(
                f"Length {len(raw_content)}. Non-decodable byptes: {num_non_decodable_bytes}"
            )
            continue
        if file_entry.project != current_project:
            current_project = file_entry.project
            print(f"Working on project {current_project}. Files processed: {file_id}")
        converted += 1
        yield file_entry
    print(
        f"Number of files skipped: {skipped} rejected: {rejected} converted: {converted}"
    )


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "name",
        type=str,
        help="The name of the dataset.",
    )
    parser.add_argument(
        "input",
        type=str,
        default="/home/<USER>/devdocs/public/docs",
        help="The input directory path where HTML files are located. "
        "For devdocs, this should point to the public/docs folder.",
    )
    parser.add_argument(
        "output",
        type=str,
        default="/mnt/efs/spark-data/shared/nl-datasets/devdocs/raw/",
        help="The output directory path where jsonl files will be saved.",
    )
    parser.add_argument(
        "--reject-regex-path",
        type=str,
        default="",
        help="Regex to reject files if specified. Useful for excluding files that are not docs.",
    )
    parser.add_argument(
        "--reject-regex-content",
        type=str,
        default="",
        help="Regex to reject content HTML if specified. Useful for excluding files that are not docs.",
    )
    parser.add_argument(
        "--dedupe-filename",
        action="store_true",
        help="If specified, deduplicate based on the filename; useful for medium.",
    )
    parser.add_argument(
        "--path-as-url",
        action="store_true",
        help="If specified, relative path will be stored as URL",
    )
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()

    source = iterate_html(
        Path(args.input),
        reject_regex_path=args.reject_regex_path,
        reject_regex_content=args.reject_regex_content,
        dedupe_filename=args.dedupe_filename,
        path_as_url=args.path_as_url,
    )
    save_docs_as_jsonl(source, Path(args.output), filename=args.name)

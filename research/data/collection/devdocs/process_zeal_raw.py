"""Process docsets from Zeal or Dash docsets."""

import argparse
import json
import sqlite3
import urllib.parse
from pathlib import Path

from bs4 import BeautifulSoup

from research.data.collection.devdocs.doc_entry import DocEntry, save_docs_as_jsonl


def strip_tags_and_unescape(html):
    if "<" in html:
        # Use BeautifulSoup to strip HTML tags
        soup = BeautifulSoup(html, "html.parser")
        text = soup.get_text()
    else:
        text = html

    # Use urllib.parse to unescape URL-encoded characters
    unescaped_text = urllib.parse.unquote(text)

    return unescaped_text


def remove_parameters_and_hashtags(url):
    parsed_url = urllib.parse.urlparse(url)
    cleaned_url = urllib.parse.urlunparse(
        (parsed_url.scheme, parsed_url.netloc, parsed_url.path, "", "", "")
    )
    return cleaned_url


def find_case_insensitive_file(path):
    # Convert the input path to a Path object
    target_path = Path(path)
    directory = target_path.parent
    target_file = target_path.name

    # Ensure the directory exists
    if not directory.is_dir():
        print(f"Directory '{directory}' does not exist.")
        return None

    # Search for files in the directory that match the target file name case-insensitively
    for file in directory.rglob("*"):
        if file.is_file() and file.name.lower() == target_file.lower():
            return file

    return None


def iterate_docset(path: Path):
    """Process a single docset."""
    metafile = path / "meta.json"
    if metafile.exists():
        metadata = json.loads(metafile.read_text())
        project = metadata.get("name")
        if not project:
            project = path.stem
        version = metadata.get("version")
    else:
        project = path.stem
        version = None
    resource_path = path / "Contents/Resources"
    db = sqlite3.connect(resource_path / "docSet.dsidx")
    cursor = db.cursor()
    # Query to check if the 'searchIndex' table exists
    cursor.execute(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='searchIndex';"
    )

    # Fetch the result
    table_exists = cursor.fetchone()

    # If the table does not exist, exit the function
    if not table_exists:
        print(f"Table 'searchIndex' does not exist for {project} {version}.  Skipping.")
        return
    print(f"Processing {project} {version} at {path}")
    known = set()
    for name, doc_type, doc_path in cursor.execute(
        "SELECT name, type, path FROM searchIndex"
    ):
        if doc_path.startswith("http://"):
            print(f"Skipping network file {doc_path}.")
            continue
        if doc_path.startswith("file://"):
            doc_path = doc_path[7:]
        if doc_path.startswith("/"):
            doc_path = doc_path.lstrip("/")
        doc_path = strip_tags_and_unescape(doc_path)
        doc_path = remove_parameters_and_hashtags(doc_path)
        if doc_path in known:
            continue
        known.add(doc_path)
        # print("NAME, TYPE, DOCPATH: ", name, doc_type, doc_path)
        doc_path = Path(doc_path)
        content_file = resource_path / "Documents" / doc_path
        if not content_file.exists():
            matched_file = find_case_insensitive_file(content_file)
            if matched_file is None:
                print(f"No content found for {content_file}!!")
                continue
            print(f"Filename case-insensitive match: {content_file} -> {matched_file}")
            content_file = matched_file
        if not content_file.is_file():
            print(f"Skipping non-file {content_file}")
            continue
        try:
            content = content_file.read_text()
        except UnicodeDecodeError:
            print(f"Error processing file {content_file} due to decoding.")
            content_bytes = content_file.read_bytes()
            decoded = content_bytes.decode("utf-8", errors="replace")
            non_decodable_bytes = decoded.count("�")
            print(
                f"Length {len(content_bytes)}. Non-decodable byptes: {non_decodable_bytes}"
            )
            if non_decodable_bytes < len(content_bytes) * 0.01:
                content = content_bytes.decode("utf-8", errors="ignore")
            else:
                # Find the first non-decodable byte and show its context
                for i, c in enumerate(decoded):
                    if c == "�":
                        print(f"First non-decodable byte at position {i}")
                        print(decoded[max(0, i - 100) : i + 100])
                        break
                print("Skipping file due to too many non-decodable bytes.")
                continue
        if "Automatic redirection failed, please go to" in content:
            print(f"Skipping {doc_path} due to redirection.")
            continue
        yield DocEntry(
            project=project,
            version=version,
            doc_type=doc_type,
            path=doc_path.with_suffix(".md").as_posix(),
            page_name=name,
            content=content,
            doc_format=doc_path.suffix.lstrip(".").lower(),
        )


def parse_args():
    parser = argparse.ArgumentParser(description="Process Zeal docsets.")
    parser.add_argument(
        "--input",
        type=str,
        default="/home/<USER>/.local/share/Zeal/Zeal/docsets",
        help="The input directory path where HTML files are located. "
        "For devdocs, this should point to the public/docs folder.",
    )
    parser.add_argument(
        "--output",
        type=str,
        default="/mnt/efs/spark-data/shared/nl-datasets/devdocs/json/",
        help="The output directory path where jsonl files will be saved.",
    )
    parser.add_argument(
        "--name",
        type=str,
        default="zeal",
        help="The name of the dataset.",
    )
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()

    zeal_path = Path(args.input)
    for path in zeal_path.glob("*.docset"):
        if not path.is_dir():
            continue
        name = path.stem
        save_docs_as_jsonl(
            iterate_docset(path), Path(args.output), filename=f"{args.name}-{name}"
        )

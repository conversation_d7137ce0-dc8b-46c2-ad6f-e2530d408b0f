Developer documentation dataset
===========================

A collection of developer documentations from major languages, packages and frameworks.

The main data sources are
- Zeal which have a collection of docsets
- devdoc.io


Raw HTML sources (devdocs)
---------------

The `devdocs.io` pipeline can be used to add any docsets in the form of a collection of raw HTML files.
The main devdoc source data can be downloaded by cloning repo `https://github.com/freeCodeCamp/devdocs`, the follow the
instruction inside to install ruby and install the package, then run `bundle exec thor docs:download --default`
Note: the current version uses ruby 3.3.1 and it does not build on the dev pod.  However, 3.3.0 works just fine;
you can simply update `.ruby-version` and `Gemfile` to ruby 3.3.0 and it would work just fine.

We also download the nvidia site and use the same script to add its content.


Dash docsets (Zeal)
-------------

The zeal pipeline can be used to add any Dash docsets, which consists of a folder with files and a sqlite3 database
that contains the pointers and metadata for each file.
The easiest way to obtain the zeal dataset is to install zeal on the devpod,

```
sudo add-apt-repository ppa:zeal-developers/ppa
sudo apt-get update
sudo apt-get install zeal
```

Then open up the app and select the docsets to download from the GUI.  Downloaded content can be found at `~/.local/share/Zeal/Zeal/docsets`.
Note that this is a graphical app, so you need to add `-X -Y` when SSHing into the pod, and make sure you have X11 installed to run it.

Alternatively, you can directly obtain the docsets manually using info here `https://github.com/Kapeli/feeds`

Conversion to markdown, format and tokenization
----------------------

Both scripts by default store jsonl files into `/mnt/efs/spark-data/shared/nl-datasets/devdocs/raw/` folder
and the `convert_docs_and_tokenize.py` script converts these HTML contents to markdown, adds header
comments for the project name, version and path of the current page, and tokenize these into a dataset.

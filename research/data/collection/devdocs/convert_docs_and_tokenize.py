"""Convert HTML source in JSON to markdown and create tokenized documents."""

import argparse
import functools
import logging
import uuid

import pandas as pd
import pyspark.sql.functions as F
from pyspark.sql import Window

from research.data.collection.utils.markdown_conversion import robust_conversion
from research.data.collection.utils.tokenize_with_masking import (
    MaskableText,
    tokenize_and_pack,
)
from research.data.spark import k8s_session
from research.data.spark.pipelines.utils import map_parquet

BASE_PATH = "gs://gcp-us1-spark-data/shared/nl-datasets/devdocs"
CONTEXT_LENGTH = 4096 * 4
TOKENIZER = "llama3_instruct"
MARKDOWN_PATH = f"{BASE_PATH}/markdown"
PARQUET_PATH = f"{BASE_PATH}/parquet"


def format_doc(entry: dict[str, str | None], short_header: bool = False):
    """Create a formatted doc from documentation.

    Mainly: prefix some context before the doc.

    Remove copyright notice at the end."""
    content = entry.get("content")
    if not content:
        print(f"No content found for {entry}.")
        return

    if short_header:
        header = "<!--\n"
        if entry.get("project"):
            header += f"This is a page for: {entry['project']}\n"
    else:
        header = (
            "<!--\nThis page is part of the documentation for: "
            + (entry.get("project") or "unknown")
            + "\n"
        )
        if entry.get("version"):
            header += f"Version: {entry['version']}\n"

        doc_type = entry.get("doc_type")
        if doc_type:
            header += f"Doc Type: {doc_type}\n"

    # Print out doc path
    doc_format = entry.get("doc_format", "html")
    if doc_format in ("html", "htm"):
        path = entry.get("filename", "index.md")
    else:
        path = entry.get("filename", f"index.{doc_format}")
        path = f"{entry['path']}/{path}"
    header += f"Path: {path}\n-->\n\n"
    yield MaskableText(header, True)

    if "\n(C) " in content[-350:]:
        content = content.rsplit("\n(C) ", 1)[0] + "\n"
    yield MaskableText(content, False)


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--tokenizer",
        type=str,
        default=TOKENIZER,
        help="The tokenizer to use for the padding token.",
    )
    parser.add_argument(
        "--context-length",
        type=int,
        default=CONTEXT_LENGTH,
        help="The context length to use for packing.",
    )
    parser.add_argument(
        "--input-path",
        type=str,
        required=True,
        default=f"{BASE_PATH}/json",
        help="Input parquet location",
    )
    parser.add_argument(
        "--parquet-path",
        type=str,
        required=True,
        default=PARQUET_PATH,
        help="HTML parquet location",
    )
    parser.add_argument(
        "--markdown-path",
        type=str,
        required=True,
        default=MARKDOWN_PATH,
        help="Markdown parquet location",
    )
    parser.add_argument(
        "--output-path",
        type=str,
        required=True,
        default=f"{BASE_PATH}/tokenized",
        help="Output packed dataset location",
    )
    parser.add_argument(
        "--workers", type=int, default=100, help="number of spark workers to use"
    )
    parser.add_argument(
        "--max-trail-pad",
        type=int,
        default=410,
        help="Max number of tokens in sample prompts.",
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=100,
        help="Batch size for mapInPandas.",
    )
    parser.add_argument(
        "--timeout-seconds",
        type=int,
        default=30 * 60,
        help="Timeout in seconds for processing each file.",
    )
    parser.add_argument(
        "--skip-conversion",
        action="store_true",
        default=False,
        help="Skip the conversion step to markdown.",
    )
    parser.add_argument(
        "--skip-tokenize",
        action="store_true",
        default=False,
        help="Skip the tokenization step",
    )
    parser.add_argument(
        "--short-header",
        action="store_true",
        default=False,
        help="Use a short header.",
    )
    parser.add_argument(
        "--task-info-location",
        type=str,
        default="/mnt/efs/spark-data/temp_weekly/tokenize_and_pack_task_logs/",
    )
    parser.add_argument(
        "--partitions",
        type=int,
        default=5000,
        help="Number of partitions to use for the parquet files.",
    )
    return parser.parse_args()


def normalize_names(df):
    """normalize project and version names."""
    return df.withColumn(
        "project", F.regexp_replace(F.lower(F.col("project")), r"[\s-]", "_")
    ).withColumn(
        # for versions, replace `-` with `.`
        "version",
        F.regexp_replace(F.lower(F.col("version")), r"-", "."),
    )


def main(args: argparse.Namespace):
    spark = k8s_session(
        max_workers=args.workers,
        conf={
            "spark.executor.memory": "120G",
            "spark.executor.pyspark.memory": "500G",
        },
    )

    # First, convert to markdown
    def convert_to_markdown(input_df: pd.DataFrame):
        """Convert HTML to markdown."""
        outputs = []
        for _, row in input_df.iterrows():
            text = row["content"].strip()
            doc_format = row["doc_format"] or "html"
            if doc_format.lower() not in ("html", "htm"):
                outputs.append(text)
                continue
            try:
                converted = robust_conversion(text, keep_links=False, timeout_ms=500)
                outputs.append(converted)
            except Exception as e:  # pylint: disable=broad-except
                print(f"Failed to convert answer body to text: {e}")
        if outputs:
            return pd.DataFrame({"content": outputs})

    if args.skip_conversion:
        print("Skipping conversion step.")
    else:
        # first find duplicates.
        # if two docsets have the file count and total size exactly they are considered possibly dupes
        stats = (
            normalize_names(spark.read.json(args.input_path))
            .groupBy("project", "version")
            .agg(
                F.count("*").alias("files"),
                F.sum(F.length("content")).alias("sum_size"),
            )
        )
        window = Window.partitionBy("project", "files", "sum_size").orderBy(
            F.col("version").desc_nulls_last()
        )
        stats = stats.withColumn(
            "dupe_rank",
            F.row_number().over(window),
        )
        dupes = stats.filter("dupe_rank > 1").select("project", "version").collect()
        blacklist = []
        if len(dupes) > 0:
            print("Duplicate docsets founds.  Project/versions to remove:")
            for project, version in dupes:
                print(f"{project} {version}")
                blacklist.append(f"{project}~{version or ''}")

        banned_paths = "feed", "tag", "tags", "category", "categories", "privacy", "rss"
        banned_cond = F.col("path").startswith("cdn-")
        for path in banned_paths:
            banned_cond |= F.col("path").contains(f"/{path}/")
        print("Converting JSON to parquet")
        normalize_names(spark.read.json(args.input_path)).repartition(
            args.partitions, "source", "project", "version", "path"
        ).filter(
            ~F.concat(
                F.col("project"), F.lit("~"), F.coalesce("version", F.lit(""))
            ).isin(blacklist)
        ).withColumn("head", F.lower(F.substring(F.trim("content"), 0, 1000))).filter(
            (
                F.col("head").startswith("<")
                | F.col("head").contains("<html")
                | F.col("head").contains("<!doctype html>")
                | F.col("head").contains("<h1>")
                | F.col("head").contains("<h1 ")
                | F.col("head").contains("<h2>")
                | F.col("head").contains("<h2 ")
                | F.col("head").contains("<head>")
                | ~F.lower("doc_format").startswith("htm")
            )
            & ~banned_cond
        ).withColumn(
            "doc_format", F.coalesce(F.col("doc_format"), F.lit(""))
        ).withColumn("doc_type", F.coalesce(F.col("doc_type"), F.lit(""))).repartition(
            args.partitions, "source", "project", "version", "path"
        ).write.mode("overwrite").parquet(args.parquet_path)
        print("Converting to markdown")
        map_parquet.apply_pandas(
            spark,
            convert_to_markdown,
            args.parquet_path,
            args.markdown_path,
            batch_size=args.batch_size,
            timeout=args.timeout_seconds,
            ignore_error=True,
            task_info_location=f"{args.task_info_location}/convert_to_markdown",
        )

    if args.skip_tokenize:
        print("Tokenization step is skipped")
    else:
        # Then, tokenize and pack
        process = functools.partial(
            tokenize_and_pack,
            text_generator=functools.partial(
                format_doc, short_header=args.short_header
            ),
            tokenizer_name=args.tokenizer,
            seq_length=args.context_length,
            add_one_token=True,
            max_tail_pad=args.max_trail_pad,
        )

        print("Tokenize and pack.")
        map_parquet.apply_pandas(
            spark,
            process,
            args.markdown_path,
            args.output_path,
            batch_size=args.batch_size,
            timeout=args.timeout_seconds,
            ignore_error=True,
            task_info_location=f"{args.task_info_location}/tokenize_and_pack",
        )
    spark.stop()


if __name__ == "__main__":
    logging.basicConfig(level="INFO")
    main(parse_args())

"""Convert Katana scraped data to the right schema.

input jsonl files are scraped with katana -j option.
for example

>> katana -u https://ui.shadcn.com/docs -cs docs -o shadcn~2.1.2/shadcn.jsonl -j

The input folder should contain each new docset as a folder.  The name of the
folder should be of the format `project~version`

The output is the same schema and format as the current json dumps.
Some information is missing in the katana output, namely project and version.
These are taken from the directory name.
"""

import re
import pandas as pd
import argparse
import json
from pathlib import Path


def convert_katana(
    request: pd.Series,
    response: pd.Series,
    project: str,
    version: str | None,
    content_type_regex: str = r"html",
) -> list[dict]:
    """Convert Katana scraped data to the right schema.

    The schema:
    root
    |-- content: string (nullable = true)
    |-- doc_format: string (nullable = true)
    |-- doc_type: string (nullable = true)
    |-- page_name: string (nullable = true)
    |-- path: string (nullable = true)
    |-- project: string (nullable = true)
    |-- url: string (nullable = true)
    |-- version: string (nullable = true)

    Kanata sample data:
    request: {
        'method': 'GET',
        'endpoint': 'https://ui.shadcn.com/docs',
        'raw': 'GET /docs HTTP/1.1\r\nHost: ...'
    }

    response: {
        'status_code': 200,
        'headers': {
            'server': 'Vercel',
            'x_vercel_id': 'sfo1::bwwhj-...',
            'cache_control': 'public, max-age=0, must-revalidate',
            'content_disposition': 'inline',
            'access_control_allow_origin': '*',
            'x_vercel_cache': 'HIT',
            'x_matched_path': '/docs',
            'vary': 'RSC, Next-Router-State-Tree, Next-Router-Prefetch',
            'strict_transport_security': 'max-age=63072000',
            'etag': 'W/"..."',
            'date': 'Sat, 26 Oct 2024 03:59:52 GMT',
            'age': '49509',
            'content_type': 'text/html; charset=utf-8'
        },
        'body': '<!DOCTYPE html><html lang="en">...',
        'technologies': ['Vercel', 'HSTS'],
        'raw': 'HTTP/1.1 200 OK\r\nTransfer-Encoding: ...'
    }
    """
    converted_data: list[dict[str, str]] = []

    if content_type_regex:
        content_type_parttern = re.compile(content_type_regex)
    else:
        content_type_parttern = None

    for i, (req, resp) in enumerate(zip(request, response)):
        # skip failed requests
        if not isinstance(resp, dict):
            print(f"Row {i} cannot be converted because it was an invalid request")
            continue
        url = req["endpoint"]
        if "body" not in resp or not resp["body"]:
            continue
        content = resp["body"]

        # Extract page_name from the URL
        url_parts = url.split("/")
        page_name = url_parts[-1] if url_parts[-1] else "index"

        # Determine doc_format based on content_type
        content_type = resp["headers"].get("content-type", "").lower()
        if content_type_parttern and not content_type_parttern.search(content_type):
            print(f"row {i} of content type {content_type} is skipped.")
            continue

        # Convert content type to doc format
        if "html" in content_type:
            doc_format = "html"
        elif "markdown" in content_type:
            doc_format = "md"
        else:
            doc_format = "text"

        converted_item = {
            "content": content,
            "doc_format": doc_format,
            "doc_type": "documentation",  # Assuming all scraped data is documentation
            "page_name": page_name,
            "path": "/".join(url_parts[3:]),  # Path after the domain
            "project": project,
            "url": url,
            "version": version,
        }

        converted_data.append(converted_item)

    return converted_data


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "input",
        type=str,
        help="The input directory path where JSONL dump from katana is located.",
    )
    parser.add_argument(
        "output",
        type=str,
        help="The output directory path where converted jsonl docset files will be saved.",
    )
    return parser.parse_args()


def main():
    args = parse_args()
    # find all subdirectories in the input directory.
    # Each directories would be a separate docset containing a collection of jsonl files.
    for docset_path in Path(args.input).iterdir():
        if not docset_path.is_dir():
            continue
        docset_name = docset_path.name
        if "~" in docset_name:
            project, version = docset_name.split("~", maxsplit=1)
        else:
            project = docset_name
            version = None
        print(f"Processing {project}, version={version}")
        output_folder = Path(args.output) / docset_name
        output_folder.mkdir(parents=True, exist_ok=True)
        for i, jsonl_file in enumerate(docset_path.glob("*.jsonl")):
            print(f"Processing #{i+1}: {jsonl_file}.")
            df = pd.read_json(jsonl_file, lines=True)
            converted_data = convert_katana(
                df["request"], df["response"], project, version
            )
            output_file = output_folder / f"part-{i}.jsonl"
            with output_file.open("w") as f:
                for item in converted_data:
                    f.write(json.dumps(item) + "\n")


if __name__ == "__main__":
    main()

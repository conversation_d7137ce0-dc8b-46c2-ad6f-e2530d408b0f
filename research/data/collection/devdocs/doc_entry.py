"""The dataclass and function used to parse and store the documentation information."""

from dataclasses import dataclass
from pathlib import Path
from typing import Iterable

from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class DocEntry:
    """The dataclass used to store the documentation information."""

    project: str
    """The project name which the documentation page belongs to."""

    version: str | None
    doc_type: str | None
    path: str | None
    page_name: str
    content: str
    doc_format: str | None
    url: str


def write_batch(batch: list[str], output_file: Path):
    with output_file.open("w+") as f:
        for entry in batch:
            f.write(entry + "\n")
    batch.clear()


def save_docs_as_jsonl(
    source: Iterable[DocEntry], output_path: Path, filename: str = "docs"
):
    batch = []
    batch_id = 0
    output_path.mkdir(exist_ok=True, parents=True)

    for entry in source:
        batch.append(entry.to_json())
        if len(batch) >= 1000:
            write_batch(batch, output_path / f"{filename}-{batch_id}.jsonl")
            batch_id += 1
    if batch:
        write_batch(batch, output_path / f"{filename}-{batch_id}.jsonl")

"""<PERSON><PERSON><PERSON> to create the input data for AutoFix data collection.

Note that this job requires to load data from BigQuery. Please be aware that it
will incur additional charges if it is executed from a different cloud.
"""

import pyspark.sql.functions as F

from research.data.spark import k8s_session
from research.data.utils.pr_v2 import REPO_PRS_GCS

# BigQuery table containing the PRs to collect
BQ_TABLE = "augment-research-gsc.autofix.prs"

# Minimum number of PRs for a repo to be included in the input data
PR_THRESHOLD = 20


def main():
    spark = k8s_session(
        max_workers=32,
        conf={
            "spark.executor.cores": "4",
            "spark.executor.memory": "64G",
        },
    )

    (
        spark.read.format("bigquery")
        .option("table", BQ_TABLE)
        .load()
        .filter(F.size(F.col("pr_list")) >= PR_THRESHOLD)
        .select("repo_name", F.explode(F.col("pr_list.pr_number")).alias("pr_number"))
        .distinct()
        .groupBy("repo_name")
        .agg(F.collect_list("pr_number").alias("pr_list"))
        .repartition(1000)
        .write.mode("overwrite")
        .parquet(REPO_PRS_GCS)
    )

    # Show statistics of the input data
    input_data = spark.read.parquet(REPO_PRS_GCS)
    input_data.printSchema()
    print(f"Repo Count: {input_data.count()}")
    print(f"PR Count: {input_data.select(F.explode(F.col('pr_list'))).count()}")

    spark.stop()


if __name__ == "__main__":
    main()

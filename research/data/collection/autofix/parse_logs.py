"""Script to parse and show the log files generated by spark_runner.py."""

import argparse
from pathlib import Path


def main(log_root: str):
    log_files = Path(log_root).glob("*.txt")
    output = []

    for file in log_files:
        with file.open() as f:
            lines = f.readlines()

        for line_idx in range(len(lines)):
            if "Starting partition_id" in lines[line_idx]:
                exec_id = lines[line_idx - 1].split("exec_id=")[1].strip()
                partition_id = lines[line_idx].split("partition_id=")[1].split(" ")[0]
                if line_idx + 1 < len(lines):
                    # already finished partition
                    duration = lines[line_idx + 2].split("duration=")[1].strip()
                    text = f"partition_id={partition_id}  \tfinished by exec_id={exec_id}\tin {duration}"
                else:
                    text = (
                        f"partition_id={partition_id}  \tRUNNING by exec_id={exec_id}\t"
                    )
                output.append(text)

    print("=" * 80)
    sorted_by_partition_id = sorted(
        output,
        key=lambda x: int(x.split("partition_id=")[1].split(" ")[0]),
    )
    for line in sorted_by_partition_id:
        print(line)

    print("=" * 80)
    sorted_by_exec_id = sorted(
        [text for text in sorted_by_partition_id if "RUNNING" in text],
        key=lambda x: int(x.split("exec_id=")[1].split("\t")[0]),
    )
    if len(sorted_by_exec_id) == 0:
        print("No Running Partition")
    else:
        print("Still Running:")
    for line in sorted_by_exec_id:
        print(line)


if __name__ == "__main__":
    argparser = argparse.ArgumentParser()
    argparser.add_argument(
        "--log_root",
        type=str,
        required=True,
        help="Root directory for saving job progress log files",
    )
    args = argparser.parse_args()
    main(args.log_root)

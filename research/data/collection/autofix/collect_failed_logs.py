"""<PERSON><PERSON><PERSON> to collect failed logs for check runs.

This job relies on the output from `collect_check_runs.py` to get the metadata for each
check run, so that we can identify the failed ones.

It directly uses the output directory of `collect_check_runs.py` as the input data.
We consider a check run failed if its conclusion is explicitly set to "FAILURE".

The default retention period for check run logs is 90 days on GitHub.
Set TIME_THRESHOLD to filer out check runs that are too old to have fetchable logs.

To avoid oversized logs, we only save the last SIZE_LIMIT bytes of the log.
"""

import argparse
import gc
import time
from pathlib import Path
from typing import List

import pandas as pd
import requests

from research.data.collection.autofix.collect_check_runs import (
    OUTPUT_DIR as CHECK_RUNS_DIR,
)
from research.data.collection.autofix.dataclasses.failed_log import FailedLog
from research.data.collection.autofix.utils import (
    GITHUB_URL,
    TIMEOUT_SECOND,
    append_to_parquet,
    get_hash_key,
    get_header,
    get_job_logger,
    get_rate_limit,
    get_saved_keys,
)
from research.data.utils.pr_v2 import PR_FAILED_LOGS

# Time threshold to request check run logs
# Check runs completed before this time (completed_at < TIME_THRESHOLD) will be skipped
# to save rate limit
TIME_THRESHOLD = "2024-07"

# Maximum size (in bytes) of log to save
SIZE_LIMIT = int(1e6)

# Maximum size (in bytes) of each output file
OUTPUT_FILE_SIZE_LIMIT = int(1e8)

# Minimum sleep time between requests
REQUEST_SLEEP_TIME = 1

# Output directory for saving the collected data
# Adding the suffix "_new" to avoid interfering with the existing data
OUTPUT_DIR = PR_FAILED_LOGS + "_new"

# Frequency to print the remaining rate limit
PRINT_RATE_LIMIT_FREQ = 100

# Threshold to flush the output buffer
BUFFER_FLUSH_THRESHOLD = 20

logger = get_job_logger(__name__)


def get_all_output_paths(output_dir: str, path_prefix: str) -> List[Path]:
    """Get all output paths for the given prefix in the output directory."""
    return list(Path(output_dir).glob(f"{path_prefix}*.parquet"))


def get_next_output_path(output_dir: str, path_prefix: str) -> Path:
    """Get the next output path for the given prefix in the output directory.

    The current output path for writing data is always:
    - output_dir/path_prefix.parquet

    The other existing paths are 1-indexed:
    - output_dir/path_prefix-1.parquet
    - output_dir/path_prefix-2.parquet
    - ...
    - output_dir/path_prefix-N.parquet

    The next output path will be:
    - output_dir/path_prefix-(N+1).parquet
    """
    size = len(get_all_output_paths(output_dir, path_prefix))
    new_path = Path(f"{output_dir}/{path_prefix}-{size}.parquet")
    if new_path.exists():
        raise RuntimeError(f"Output path {new_path} already exists")
    return new_path


def main(input_from: int, input_to: int, token: str):
    logger.info(
        f"Args: input_from={input_from}, input_to={input_to}, token={token[:8]}..."
    )

    headers = get_header(token)
    input_files = list(Path(CHECK_RUNS_DIR).glob("*.parquet"))[input_from:input_to]
    logger.info(f"Found {len(input_files)} input files")

    for path in input_files:
        logger.info(f"Start processing file {path}")
        path_prefix = path.name[:10]
        output_path = f"{Path(OUTPUT_DIR) / path_prefix}.parquet"
        logger.info(f"Output path = {output_path}")

        # allow this job to resume from saved data
        # note that this job uses database_id as the index_col rather than pr_number
        all_output_files = get_all_output_paths(OUTPUT_DIR, path_prefix)
        logger.info(f"Found {len(all_output_files)} existing output files")
        saved_keys = set()
        for output_file in all_output_files:
            saved_keys |= get_saved_keys(str(output_file), index_col="database_id")
        if saved_keys:
            logger.info(f"Found {len(saved_keys)} saved keys")
        else:
            logger.info("No saved keys found")

        input_data = pd.read_parquet(path)
        failed_runs = input_data[input_data["conclusion"] == "FAILURE"]
        logger.info(f"Found {len(failed_runs)} failed runs")

        output_buffer: List[FailedLog] = []
        request_count = 0
        for _, row in failed_runs.iterrows():
            # iterate over one check run per row
            owner = row["owner"]
            name = row["name"]
            pr_number = row["pr_number"]
            commit_sha = row["commit_sha"]
            check_run_name = row["check_run_name"]
            database_id = row["database_id"]
            completed_at = row["completed_at"]

            if get_hash_key(owner, name, database_id) in saved_keys:
                # skip this check run if it is already processed
                continue
            if completed_at < TIME_THRESHOLD:
                continue

            log_url = (
                f"{GITHUB_URL}/repos/{owner}/{name}/actions/jobs/{database_id}/logs"
            )
            response = None
            status_code = -1
            retrieved_log = ""

            logger.info(f"Requesting log at {log_url}")
            request_count += 1
            try:
                # trial request to get log size
                trial_response = requests.get(
                    url=log_url,
                    headers=dict(headers) | {"Range": "bytes=0-100"},
                    timeout=TIMEOUT_SECOND,
                )
                if trial_response is None:
                    raise RuntimeError("Got None trial_response")
                if not trial_response.ok:
                    logger.warning(f"status_code = {trial_response.status_code}")
                    logger.warning(f"text = {trial_response.text}")
                    raise RuntimeError("Got non-ok trial_response")

                log_size = int(trial_response.headers["Content-Range"].split("/")[-1])
                logger.info(f"Got log size = {log_size}")

                range_expr = "bytes=0-"
                if log_size > SIZE_LIMIT:
                    range_expr = f"bytes={log_size - SIZE_LIMIT}-"

                # actual request to get size-limited log
                response = requests.get(
                    url=log_url,
                    headers=dict(headers) | {"Range": range_expr},
                    timeout=TIMEOUT_SECOND,
                )

                time.sleep(REQUEST_SLEEP_TIME)

                if response is None:
                    raise RuntimeError("Got None response")

                if request_count % PRINT_RATE_LIMIT_FREQ == 0:
                    logger.info(f"Remaining limit = {get_rate_limit(response)}")

                status_code = response.status_code
                if response.ok:
                    response.encoding = "utf-8"
                    text_size = len(response.text)
                    if text_size <= SIZE_LIMIT:
                        retrieved_log = response.text
                    else:
                        logger.warning(f"Got oversize log ({text_size})")
                        retrieved_log = response.text[-int(SIZE_LIMIT) :]
                else:
                    logger.warning(f"status_code = {response.status_code}")
                    logger.warning(f"text = {response.text}")
                    raise RuntimeError("Got non-ok response")
            except Exception as e:
                logger.warning(f"Failed to request log at {log_url}")
                logger.warning(f"{e.__class__.__name__}: {e}")

            failed_log = FailedLog(
                owner=owner,
                name=name,
                pr_number=pr_number,
                commit_sha=commit_sha,
                check_run_name=check_run_name,
                database_id=database_id,
                status_code=status_code,
                log=retrieved_log,
            )

            output_buffer.append(failed_log)
            logger.info(f"Added log size = {len(retrieved_log)}")

            buffer_size = len(output_buffer)
            logger.info(f"Current buffer size = {buffer_size}")

            if buffer_size >= BUFFER_FLUSH_THRESHOLD:
                logger.info(f"Saving {buffer_size} data to {output_path}")
                append_to_parquet(output_buffer, output_path)
                logger.info("Complete saving one batch of data")
                output_buffer = []
                gc.collect()

            if Path(output_path).exists():
                file_size = Path(output_path).stat().st_size
                if file_size > OUTPUT_FILE_SIZE_LIMIT:
                    logger.warning(f"File {output_path} has size {file_size}")
                    new_output_path = get_next_output_path(OUTPUT_DIR, path_prefix)
                    logger.warning(f"Moving {output_path} to {new_output_path}")
                    Path(output_path).rename(new_output_path)
        # end of for _, row in failed_runs.iterrows()

        buffer_size = len(output_buffer)
        if buffer_size > 0:
            logger.info(f"Saving {buffer_size} data to {output_path}")
            append_to_parquet(output_buffer, output_path)
            logger.info("Complete saving one batch of data")
            output_buffer = []
            gc.collect()
    # end of for path in input_files


if __name__ == "__main__":
    argparser = argparse.ArgumentParser()
    argparser.add_argument(
        "--input_from",
        type=int,
        default=0,
        help="Index of input file in the path list to start from (inclusive)",
    )
    argparser.add_argument(
        "--input_to",
        type=int,
        default=1000,
        help="Index of input file in the path list to end at (exclusive)",
    )
    argparser.add_argument(
        "--token",
        type=str,
        required=True,
        help="Github API token to use",
    )
    args = argparser.parse_args()
    main(args.input_from, args.input_to, args.token)

"""Utilities for working with AutoFix data."""

import logging
import time
from pathlib import Path
from typing import Any, List, Mapping, Set

import pandas as pd
import requests

import research.data.postgres_metastore as pg

# GitHub API URLs
GITHUB_URL = "https://api.github.com"
GRAPHQL_URL = f"{GITHUB_URL}/graphql"

# Default settings for GitHub API requests
MAX_RETRY = 3
TIMEOUT_SECOND = 60  # seconds
MIN_SLEEP_TIME = 60  # seconds

# Error message for repo not found
MISSING_REPO_ERROR = "Could not resolve to a Repository with the name"


def get_github_tokens() -> List[str]:
    """Get the GitHub tokens."""
    github_tokens = pg.execute("SELECT token FROM github_tokens;")
    return [row[0] for row in github_tokens]


def get_job_logger(name: str, level: int = logging.INFO) -> logging.Logger:
    """Get the logger for an AutoFix data job."""
    logging.basicConfig(
        format="%(asctime)s [%(levelname)s] %(message)s",
        datefmt="%Y-%m-%d-%H:%M:%S",
        level=level,
    )
    return logging.getLogger(name)


def get_header(token: str) -> Mapping[str, str]:
    """Get the header for GitHub API requests."""
    return {
        "Authorization": f"Bearer {token}",
    }


def get_hash_key(owner: str, name: str, index: int) -> str:
    """Get the hash key for a repo artifact, e.g. PR, log, etc."""
    return f"{owner}/{name}/{index}"


def get_saved_keys(path: str, index_col: str = "pr_number") -> Set[str]:
    """Get the saved keys from a parquet file.

    The parquet file should have the following columns:
        - "owner"
        - "name"
        - `index_col`

    Returned keys should match the format used in `get_hash_key`.
    """
    if Path(path).exists():
        saved_data = pd.read_parquet(
            path,
            columns=["owner", "name", index_col],
        )
        return set(
            saved_data["owner"]
            + "/"
            + saved_data["name"]
            + "/"
            + saved_data[index_col].astype(str)
        )
    return set()


def has_errors(response: requests.Response) -> bool:
    """Check if the response has errors."""
    return "errors" in response.json()


def is_valid_response(response: requests.Response) -> bool:
    """Check if the response is valid."""
    return response.status_code == 200 and not has_errors(response)


def repo_not_found(response: requests.Response) -> bool:
    """Check whether the repo is not found."""
    return has_errors(response) and MISSING_REPO_ERROR in response.text


def get_rate_limit(response: requests.Response) -> int:
    """Get the remaining rate limit from a response."""
    key = "x-ratelimit-remaining"
    if key not in response.headers:
        return 0
    return int(response.headers[key])


def rate_limit_exceeded(response: requests.Response) -> bool:
    """Check if the rate limit is exceeded."""
    if get_rate_limit(response) <= 0:
        return True
    if has_errors(response):
        errors = response.json()["errors"]
        for error in errors:
            if "type" in error and error["type"] == "RATE_LIMITED":
                return True
    return False


def handle_response(response: requests.Response, logger: logging.Logger) -> bool:
    """Handle a GitHub API response and sleep if necessary.

    Returns True if the response is valid.
    """
    if rate_limit_exceeded(response):
        logger.warning("Rate limit exceeded")
        logger.warning(f"response.headers = {response.headers}")
        logger.warning(f"response.text = {response.text}")

        sleep_time = MIN_SLEEP_TIME

        key = "x-ratelimit-reset"
        if key in response.headers:
            rate_limit_reset = float(response.headers[key])
            logger.info(f"Found {key}: {rate_limit_reset}")
            sleep_time = max(sleep_time, rate_limit_reset - time.time())

        key = "retry-after"
        if key in response.headers:
            retry_after = float(response.headers[key])
            logger.info(f"Found {key}: {retry_after}")
            sleep_time = max(sleep_time, retry_after)

        logger.warning(f"Sleeping for {sleep_time} seconds")
        time.sleep(sleep_time)

    return is_valid_response(response)


def append_to_parquet(data: List[Any], path: str) -> None:
    """Append data to a new or existing parquet file."""
    df = pd.DataFrame(data)
    if Path(path).exists():
        existing_df = pd.read_parquet(path)
        combined_df = pd.concat([existing_df, df], ignore_index=True)

        tmp_path = f"{path}.tmp"
        combined_df.to_parquet(tmp_path, index=False)

        time.sleep(1)
        # explicitly delete the dataframes to avoid oom and memory leak
        del existing_df, combined_df
        # atomic rename to commit the write
        Path(tmp_path).rename(path)
    else:
        df.to_parquet(path, index=False)
    del df

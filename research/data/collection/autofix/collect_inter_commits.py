"""<PERSON><PERSON><PERSON> to collect intermediate commits for selected PRs.

This is the first step of the AutoFix data collection process. We need intermediate
commits in selected PRs to further collect check runs and failed logs.

Selected PRs for collecting commits need to be placed in the REPO_PRS directory.
Two columns are required in the input data:
- repo_name: the full name of the repository, in the format of "owner/name"
- pr_list: a list of PR numbers to collect commits for.

Output files will be saved in the "{PR_INTER_COMMITS}_new" directory. After a cycle
of data collection is completely finished, it is recommended to manually combine the
two directories, i.e. PR_INTER_COMMITS and "{PR_INTER_COMMITS}_new".
"""

import argparse
from pathlib import Path
from typing import List

import pandas as pd
import requests

from research.data.collection.autofix.dataclasses.inter_commit import InterCommit
from research.data.collection.autofix.utils import (
    GRAPHQL_URL,
    MAX_RETRY,
    TIMEOUT_SECOND,
    append_to_parquet,
    get_hash_key,
    get_header,
    get_job_logger,
    get_rate_limit,
    get_saved_keys,
    handle_response,
    repo_not_found,
)
from research.data.utils.pr_v2 import PR_INTER_COMMITS, REPO_PRS

# GraphQL query to get intermediate commits.
# Note that due to the rate limit, we only get the last 100 commits for each PR and
# the last 10 parents for each commit. Modify corresponding parameters in the below
# query if needed.
QUERY = """
query($owner: String!, $name: String!, $prNumber: Int!) {
  repository(owner: $owner, name: $name) {
    pullRequest(number: $prNumber) {
      title
      headRefName
      baseRefName
      mergedAt
      commits(last: 100) {
        edges {
          node {
            commit {
              oid
              message
              committedDate
              parents(last: 10) {
                edges {
                  node {
                    oid
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
"""

# Output directory for saving the collected data
# Adding the suffix "_new" to avoid interfering with the existing data
OUTPUT_DIR = PR_INTER_COMMITS + "_new"

# Frequency to print the remaining rate limit for monitoring purposes
PRINT_RATE_LIMIT_FREQ = 100

# Threshold to flush the output buffer
BUFFER_FLUSH_THRESHOLD = 1000

logger = get_job_logger(__name__)


def main(input_from: int, input_to: int, token: str):
    logger.info(
        f"Args: input_from={input_from}, input_to={input_to}, token={token[:8]}..."
    )

    headers = dict(get_header(token))
    input_files = list(Path(REPO_PRS).glob("*.parquet"))[input_from:input_to]
    logger.info(f"Found {len(input_files)} input files to process")

    for path in input_files:
        logger.info(f"Start processing file {path}")
        output_path = f"{Path(OUTPUT_DIR) / path.name[:10]}.parquet"
        logger.info(f"Output path = {output_path}")

        # allow this job to resume from saved data
        saved_keys = get_saved_keys(output_path)
        if saved_keys:
            logger.info(f"Found {len(saved_keys)} saved keys")
        else:
            logger.info("No saved keys found")

        input_data = pd.read_parquet(path)
        output_buffer: List[InterCommit] = []

        request_count = 0
        for _, row in input_data.iterrows():
            # iterate over one repo per row (repo_name, pr_list)
            repo_name = str(row["repo_name"])
            owner, name = repo_name.split("/", 1)
            pr_list = sorted([int(pr) for pr in row["pr_list"]], reverse=True)
            logger.info(f"Processing {repo_name} with {len(pr_list)} PRs")

            for pr_number in pr_list:
                if get_hash_key(owner, name, pr_number) in saved_keys:
                    # skip this PR if it is already processed
                    continue

                json = {
                    "query": QUERY,
                    "variables": {
                        "owner": owner,
                        "name": name,
                        "prNumber": pr_number,
                    },
                }

                attempt = 0
                response = None
                while attempt < MAX_RETRY:
                    attempt += 1
                    request_count += 1
                    try:
                        response = requests.post(
                            url=GRAPHQL_URL,
                            json=json,
                            headers=headers,
                            timeout=TIMEOUT_SECOND,
                        )
                    except Exception:
                        logger.warning(
                            f"Failed to request PR {pr_number} (attempt={attempt}))"
                        )
                        continue

                    if request_count % PRINT_RATE_LIMIT_FREQ == 0:
                        logger.info(f"Remaining limit = {get_rate_limit(response)}")

                    if handle_response(response, logger):
                        # successfully get a valid response
                        break
                # end of while attempt < MAX_RETRY

                if response is None:
                    logger.warning(f"Failed to get response for PR {pr_number}")
                    continue

                if repo_not_found(response):
                    logger.warning(f"Repo {repo_name} not found")
                    break

                try:
                    json_data = response.json()
                    pr_data = json_data["data"]["repository"]["pullRequest"]
                    commits = pr_data["commits"]["edges"]
                except Exception:
                    logger.warning(f"Failed to extract commits in PR {pr_number}")
                    logger.warning(f"response.status_code = {response.status_code}")
                    logger.warning(f"response.text = {response.text}")
                    continue

                if len(commits) == 0:
                    logger.warning(f"No commits found in PR {pr_number}")
                    continue

                for commit in commits:
                    commit_sha = ""
                    try:
                        commit_node = commit["node"]["commit"]
                        commit_sha = commit_node["oid"]
                        commit_parents = commit_node["parents"]["edges"]
                        inter_commit = InterCommit(
                            owner=owner,
                            name=name,
                            pr_number=pr_number,
                            title=pr_data["title"],
                            head_ref=pr_data["headRefName"],
                            base_ref=pr_data["baseRefName"],
                            merged_at=pr_data["mergedAt"],
                            sha=commit_sha,
                            message=commit_node["message"],
                            committed_at=commit_node["committedDate"],
                            parents=[x["node"]["oid"] for x in commit_parents],
                        )
                    except Exception:
                        logger.warning(f"Failed to extract commit {commit_sha}")
                        logger.warning(f"response.status_code = {response.status_code}")
                        logger.warning(f"response.text = {response.text}")
                        continue

                    # not flush the buffer in the middle of processing a PR
                    output_buffer.append(inter_commit)
                # end of for commit in commits

                buffer_size = len(output_buffer)
                if buffer_size >= BUFFER_FLUSH_THRESHOLD:
                    logger.info(f"Saving {buffer_size} data to {output_path}")
                    append_to_parquet(output_buffer, output_path)
                    logger.info("Complete saving one batch of data")
                    output_buffer = []
            # end of for pr_number in pr_list

            buffer_size = len(output_buffer)
            if buffer_size > 0:
                logger.info(f"Saving {buffer_size} data to {output_path}")
                append_to_parquet(output_buffer, output_path)
                logger.info("Complete saving one batch of data")
                output_buffer = []
        # end of for _, row in input_data.iterrows()
    # end of for path in input_files


if __name__ == "__main__":
    argparser = argparse.ArgumentParser()
    argparser.add_argument(
        "--input_from",
        type=int,
        default=0,
        help="Index of input file in the path list to start from (inclusive)",
    )
    argparser.add_argument(
        "--input_to",
        type=int,
        default=1000,
        help="Index of input file in the path list to end at (exclusive)",
    )
    argparser.add_argument(
        "--token",
        type=str,
        required=True,
        help="Github API token to use",
    )
    args = argparser.parse_args()
    main(args.input_from, args.input_to, args.token)

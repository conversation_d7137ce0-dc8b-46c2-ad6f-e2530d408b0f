"""Collect contents of intermediate commits for PRs."""

import json
import subprocess
import tempfile
import time
from pathlib import Path

import pyspark.sql.functions as F

from research.data.collection.autofix.utils import get_job_logger
from research.data.spark import k8s_session
from research.data.utils.pr_v2 import (
    PR_FAILED_LOGS,
    PR_INTER_COMMITS,
    SAVED_SHAS,
    get_download_log_full_path,
    get_tar_full_path,
)

# Spark settings
NUM_WORKERS = 100
NUM_CORES = 8
MEMORY_GB = 64
LOCAL_DISK_GB = 500

# Timeout for git clone/fetch in seconds
GIT_CLONE_TIMEOUT = 1800
GIT_FETCH_TIMEOUT = 300

# Maximum size (in bytes) of tar file to be saved
TAR_SIZE_LIMIT = 2e9


def download_repo(repo_name: str, tar_path: Path):
    logger = get_job_logger(__name__)
    with tempfile.TemporaryDirectory() as repo_dir:
        try:
            logger.info(f"Downloading {repo_name} to {repo_dir}")
            subprocess.run(
                [
                    "git",
                    "clone",
                    "--single-branch",
                    "--depth=1000",
                    f"https://github.com/{repo_name}.git",
                    repo_dir,
                ],
                timeout=GIT_CLONE_TIMEOUT,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
            )
        except Exception:  # pylint: disable=broad-except
            logger.warning(f"Failed to clone {repo_name}")
            return

        try:
            logger.info(f"Archiving {repo_dir} to {tar_path}")
            subprocess.run(
                ["tar", "-C", repo_dir, "-czf", str(tar_path), "."],
                check=True,
            )
        except Exception:  # pylint: disable=broad-except
            logger.warning(f"Failed to tar {repo_dir} to {tar_path}")
            return

        # check size of the tar file
        tar_size = tar_path.stat().st_size
        if tar_size > TAR_SIZE_LIMIT:
            logger.warning(f"Removing oversized tar file {tar_path} (size={tar_size})")
            tar_path.unlink()
            return

        logger.info(f"Finish downloading {repo_name} (size={tar_size})")


def collect_repo_commits(iterator):
    logger = get_job_logger(__name__)
    for owner, name, sha_list in iterator:
        full_repo_name = f"{owner}/{name}"
        logger.info(f"Processing {full_repo_name} with {len(sha_list)} commits")

        tar_path = get_tar_full_path(full_repo_name)
        if not tar_path.exists():
            logger.warning(f"Missing tar file {tar_path}")
            download_repo(full_repo_name, tar_path)
            if not tar_path.exists():
                logger.warning(f"Failed to download {full_repo_name}")
                continue

        log_data = None
        saved_shas = set()

        log_path = get_download_log_full_path(full_repo_name)
        if log_path.exists():
            logger.info(f"Found log file {log_path}")
            with log_path.open("r", encoding="utf-8") as log_file:
                log_data = json.load(log_file)
                if SAVED_SHAS in log_data:
                    saved_shas = set(log_data[SAVED_SHAS])
                    logger.info(f"Found {len(saved_shas)} saved shas")
                else:
                    logger.warning(f"Missing key {SAVED_SHAS} in log file")
        else:
            logger.warning(f"Missing log file {log_path}")

        shas_to_fetch = set(sha_list) - saved_shas
        if len(shas_to_fetch) == 0:
            logger.info("No new commits to fetch")
            continue

        logger.info(f"Found {len(shas_to_fetch)} new commits to fetch")
        with tempfile.TemporaryDirectory() as tmp_dir:
            try:
                subprocess.run(
                    ["tar", "-C", tmp_dir, "-xzf", str(tar_path)],
                    check=True,
                )
            except Exception:  # pylint: disable=broad-except
                logger.warning(f"Failed to untar {tar_path} to local")
                continue

            for sha in shas_to_fetch:
                try:
                    subprocess.run(
                        ["git", "-C", tmp_dir, "fetch", "origin", str(sha)],
                        timeout=GIT_FETCH_TIMEOUT,
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL,
                        check=True,
                    )
                except Exception:  # pylint: disable=broad-except
                    logger.warning(f"Failed to fetch {sha} for {full_repo_name}")
                    continue

            # use 2-stage commit to avoid failure during tar creation
            tmp_tar_path = f"{tar_path}.tmp"
            try:
                subprocess.run(
                    ["tar", "-C", tmp_dir, "-czf", tmp_tar_path, "."],
                    check=True,
                )
                time.sleep(1)
                # atomic rename to commit the write
                Path(tmp_tar_path).rename(tar_path)
            except Exception:  # pylint: disable=broad-except
                logger.warning(f"Failed to re-tar/commit {tar_path}")
                if Path(tmp_tar_path).exists():
                    logger.warning(f"Removing uncommitted file {tmp_tar_path}")
                    Path(tmp_tar_path).unlink()
                continue

            # update log file to record the new shas
            if log_data is None:
                log_data = {}
            log_data[SAVED_SHAS] = list(saved_shas.union(shas_to_fetch))
            with log_path.open("w", encoding="utf-8") as log_file:
                logger.info(f"Updating log file {log_path}")
                json.dump(log_data, log_file)

        logger.info(f"Finish processing {full_repo_name}")


def main():
    spark = k8s_session(
        max_workers=NUM_WORKERS,
        conf={
            "spark.executor.cores": f"{NUM_CORES}",
            "spark.task.cpus": f"{NUM_CORES}",
            "spark.executor.memory": f"{MEMORY_GB}G",
        },
        ephemeral_storage_gb=LOCAL_DISK_GB,
    )

    prs_with_failed_logs = (
        spark.read.parquet(PR_FAILED_LOGS)
        .filter(F.col("log") != "")
        .select("owner", "name", "pr_number")
        .distinct()
    )

    commits_to_collect = (
        spark.read.parquet(PR_INTER_COMMITS)
        .join(
            prs_with_failed_logs,
            on=["owner", "name", "pr_number"],
            how="inner",
        )
        .groupBy("owner", "name")
        .agg(F.collect_set("sha").alias("sha_list"))
    )

    commits_to_collect.foreachPartition(collect_repo_commits)

    spark.stop()


if __name__ == "__main__":
    main()

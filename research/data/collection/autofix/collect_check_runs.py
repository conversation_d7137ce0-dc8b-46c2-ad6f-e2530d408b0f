"""Sc<PERSON>t to collect check runs for commits in selected PRs.

This job relies on the output from `collect_inter_commits.py` to get the accurate
number of commits for each PR, so that we can save a large amount of rate limit.

It must use the same input data in the REPO_PRS directory as `collect_inter_commits.py`.
Otherwise, files/partitions will be mismatched.

Set PR_PER_REPO to control the number of PRs to process for each repo. Use None to
process all PRs in the repo.
"""

import argparse
import time
from pathlib import Path
from typing import List, Mapping

import pandas as pd
import requests

from research.data.collection.autofix.dataclasses.check_run import (
    CheckRun,
    create_empty_check_run,
)
from research.data.collection.autofix.collect_inter_commits import (
    OUTPUT_DIR as INTER_COMMITS_DIR,
)
from research.data.collection.autofix.utils import (
    GITHUB_URL,
    GRAPHQL_URL,
    MAX_RETRY,
    TIMEOUT_SECOND,
    append_to_parquet,
    get_hash_key,
    get_header,
    get_job_logger,
    get_rate_limit,
    get_saved_keys,
    handle_response,
    is_valid_response,
    repo_not_found,
)
from research.data.utils.pr_v2 import PR_CHECK_RUNS, REPO_PRS

# Number of latest PRs to process for each repo
# If None, process all PRs in the repo
PR_PER_REPO: int | None = None

# Default number of commits to collect check runs for each PR
DEFAULT_COMMIT_SIZE = 10

# Maximum number of commits to collect check runs for each PR
MAX_COMMIT_SIZE = 100

# Buffer to avoid missing commits
COMMIT_SIZE_BUFFER = 2

# Minimum sleep time between requests
REQUEST_SLEEP_TIME = 1

# GraphQL query to get check runs.
# Note that due to the rate limit, we will pass in the number of commits `$commitSize`
# for each PR. Then, we will only get the last 20 check suites for each commit, and
# the last 100 check runs for each check suite. Modify corresponding parameters in the
# below query if needed.
QUERY = """
query($owner: String!, $name: String!, $prNumber: Int!, $commitSize: Int!) {
  repository(owner: $owner, name: $name) {
    pullRequest(number: $prNumber) {
      commits(last: $commitSize) {
        edges {
          node {
            commit {
              oid
              checkSuites(last: 20) {
                edges {
                  node {
                    status
                    databaseId
                    workflowRun {
                      event
                      runNumber
                      databaseId
                      url
                      workflow {
                        name
                      }
                    }
                    checkRuns (last: 100) {
                      edges {
                        node {
                          name
                          status
                          conclusion
                          databaseId
                          startedAt
                          completedAt
                          detailsUrl
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
"""

# Output directory for saving the collected data
# Adding the suffix "_new" to avoid interfering with the existing data
OUTPUT_DIR = PR_CHECK_RUNS + "_new"

# Frequency to print the remaining rate limit
PRINT_RATE_LIMIT_FREQ = 20

# Threshold to flush the output buffer
BUFFER_FLUSH_THRESHOLD = 1000

logger = get_job_logger(__name__)


def has_workflows(repo_name: str, headers: Mapping[str, str]) -> bool:
    """Check if the repo has workflows configured."""
    response = None
    try:
        response = requests.get(
            url=f"{GITHUB_URL}/repos/{repo_name}/actions/workflows",
            headers=headers,
            timeout=TIMEOUT_SECOND,
        )
        key = "total_count"
        if is_valid_response(response) and key in response.json():
            total_count = response.json()[key]
            logger.info(f"Found {total_count} workflows for repo {repo_name}")
            return total_count > 0
    except Exception:
        logger.warning(f"Failed to request workflows for repo {repo_name}")
        if response is not None:
            logger.warning(f"response.status_code = {response.status_code}")
            logger.warning(f"response.text = {response.text}")
    return False


def main(input_from: int, input_to: int, token: str):
    logger.info(
        f"Args: input_from={input_from}, input_to={input_to}, token={token[:8]}..."
    )

    headers = get_header(token)
    input_files = list(Path(REPO_PRS).glob("*.parquet"))[input_from:input_to]
    logger.info(f"Found {len(input_files)} input files to process")

    for path in input_files:
        logger.info(f"Start processing file {path}")
        output_path = f"{Path(OUTPUT_DIR) / path.name[:10]}.parquet"
        logger.info(f"Output path = {output_path}")

        # allow this job to resume from saved data
        saved_keys = get_saved_keys(output_path)
        if saved_keys:
            logger.info(f"Found {len(saved_keys)} saved keys")
        else:
            logger.info("No saved keys found")

        # collect commit size for each PR
        commit_data_path = f"{Path(INTER_COMMITS_DIR) / path.name[:10]}.parquet"
        if Path(commit_data_path).exists():
            grouped_commits = (
                pd.read_parquet(commit_data_path)
                .groupby(["owner", "name", "pr_number"])
                .agg(cnt=pd.NamedAgg(column="sha", aggfunc="count"))
                .reset_index()
            )
            commit_size_dict = {
                get_hash_key(
                    row["owner"],
                    row["name"],
                    row["pr_number"],
                ): int(row["cnt"])
                for _, row in grouped_commits.iterrows()
            }
            assert all(cnt > 0 for cnt in commit_size_dict.values())
        else:
            # inter_commits data must be collected beforehand
            raise FileNotFoundError(f"File {commit_data_path} not found")

        input_data = pd.read_parquet(path)
        output_buffer: List[CheckRun] = []

        request_count = 0
        for _, row in input_data.iterrows():
            # iterate over one repo per row (repo_name, pr_list)
            repo_name = str(row["repo_name"])
            if not has_workflows(repo_name, headers):
                logger.warning(f"Skipping {repo_name} without workflows")
                continue
            owner, name = repo_name.split("/", 1)

            pr_list = sorted([int(pr) for pr in row["pr_list"]], reverse=True)
            if PR_PER_REPO is not None:
                pr_list = pr_list[:PR_PER_REPO]
            logger.info(f"Processing {repo_name} with {len(pr_list)} PRs")

            for pr_number in pr_list:
                hash_key = get_hash_key(owner, name, pr_number)
                if hash_key in saved_keys:
                    # skip this PR if it is already processed
                    continue
                if hash_key not in commit_size_dict:
                    logger.warning(f"Cannot find any commit for PR {pr_number}")
                    continue

                accurate_commit_size = commit_size_dict[hash_key] + COMMIT_SIZE_BUFFER
                commit_size = min(
                    MAX_COMMIT_SIZE,
                    max(accurate_commit_size, DEFAULT_COMMIT_SIZE),
                )

                json = {
                    "query": QUERY,
                    "variables": {
                        "owner": owner,
                        "name": name,
                        "prNumber": pr_number,
                        "commitSize": commit_size,
                    },
                }

                attempt = 0
                response = None
                while attempt < MAX_RETRY:
                    attempt += 1
                    request_count += 1
                    try:
                        response = requests.post(
                            url=GRAPHQL_URL,
                            json=json,
                            headers=headers,
                            timeout=TIMEOUT_SECOND,
                        )
                    except Exception:
                        logger.warning(
                            f"Failed to request PR {pr_number} (attempt={attempt}))"
                        )
                        continue

                    # sleep to avoid frequently hitting rate limit
                    time.sleep(REQUEST_SLEEP_TIME)

                    if request_count % PRINT_RATE_LIMIT_FREQ == 0:
                        logger.info(f"Remaining limit = {get_rate_limit(response)}")

                    if handle_response(response, logger):
                        # successfully get a valid response
                        break
                # end of while attempt < MAX_RETRY

                if response is None:
                    logger.warning(f"Failed to get response for PR {pr_number}")
                    continue

                if repo_not_found(response):
                    logger.warning(f"Repo {repo_name} not found")
                    break

                try:
                    json_data = response.json()
                    pr_data = json_data["data"]["repository"]["pullRequest"]
                    commits = pr_data["commits"]["edges"]
                except Exception:
                    logger.warning(f"Failed to extract commits for PR {pr_number}")
                    logger.warning(f"response.status_code = {response.status_code}")
                    logger.warning(f"response.text = {response.text}")
                    continue

                # add an empty check run for each PR as an indicator of successful request
                output_buffer.append(create_empty_check_run(owner, name, pr_number))

                for commit in commits:
                    commit_sha = ""
                    try:
                        commit_node = commit["node"]["commit"]
                        commit_sha = commit_node["oid"]
                        check_suites = commit_node["checkSuites"]["edges"]
                    except Exception:
                        logger.warning(
                            f"Failed to extract check suites for commit {commit_sha}"
                        )
                        logger.warning(f"response.status_code = {response.status_code}")
                        logger.warning(f"response.text = {response.text}")
                        continue

                    for check_suite in check_suites:
                        try:
                            check_suite_node = check_suite["node"]
                            workflow_run = check_suite_node["workflowRun"]
                            check_runs = check_suite_node["checkRuns"]["edges"]
                        except Exception:
                            logger.warning(
                                f"Failed to extract workflow/check runs for commit {commit_sha}"
                            )
                            logger.warning(f"response.text = {response.text}")
                            continue

                        for check_run in check_runs:
                            try:
                                check_run_node = check_run["node"]
                                check_run = CheckRun(
                                    owner=owner,
                                    name=name,
                                    pr_number=pr_number,
                                    commit_sha=commit_sha,
                                    check_run_name=check_run_node["name"],
                                    status=check_run_node["status"],
                                    conclusion=check_run_node["conclusion"],
                                    database_id=check_run_node["databaseId"],
                                    started_at=check_run_node["startedAt"],
                                    completed_at=check_run_node["completedAt"],
                                    details_url=check_run_node["detailsUrl"],
                                    check_suite_status=check_suite_node["status"],
                                    check_suite_db_id=check_suite_node["databaseId"],
                                )
                                if workflow_run is not None:
                                    check_run.update_workflow_run(workflow_run)
                            except Exception:
                                logger.warning(
                                    f"Failed to construct check run for commit {commit_sha}"
                                )
                                logger.warning(f"response.text = {response.text}")
                                continue

                            # not flush the buffer in the middle of processing a PR
                            output_buffer.append(check_run)
                        # end of for check_run in check_runs
                    # end of for check_suite in check_suites
                # end of for commit in commits

                buffer_size = len(output_buffer)
                if buffer_size >= BUFFER_FLUSH_THRESHOLD:
                    logger.info(f"Saving {buffer_size} data to {output_path}")
                    append_to_parquet(output_buffer, output_path)
                    logger.info("Complete saving one batch of data")
                    output_buffer = []
            # end of for pr_number in pr_list

            buffer_size = len(output_buffer)
            if buffer_size > 0:
                logger.info(f"Saving {buffer_size} data to {output_path}")
                append_to_parquet(output_buffer, output_path)
                logger.info("Complete saving one batch of data")
                output_buffer = []
        # end of for _, row in input_data.iterrows()
    # end of for path in input_files


if __name__ == "__main__":
    argparser = argparse.ArgumentParser()
    argparser.add_argument(
        "--input_from",
        type=int,
        default=0,
        help="Index of input file in the path list to start from (inclusive)",
    )
    argparser.add_argument(
        "--input_to",
        type=int,
        default=1000,
        help="Index of input file in the path list to end at (exclusive)",
    )
    argparser.add_argument(
        "--token",
        type=str,
        required=True,
        help="Github API token to use",
    )
    args = argparser.parse_args()
    main(args.input_from, args.input_to, args.token)

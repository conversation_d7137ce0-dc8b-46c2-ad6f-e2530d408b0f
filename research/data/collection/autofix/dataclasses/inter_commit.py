"""Dataclass for intermediate commits of pull requests."""

from dataclasses import dataclass
from typing import List


@dataclass
class InterCommit:
    """
    The value in [...] indicates which level the attribute belongs to:
    - [Repo]: Repository-level information.
    - [PR]: Pull request-specific information.
    - [commit]: Commit-level information.
    """

    owner: str
    """[Repo] The user owner of the repository."""

    name: str
    """[Repo] The name of the repository."""

    pr_number: int
    """[PR] The number for the pull request."""

    title: str
    """[PR] Identifies the pull request title."""

    head_ref: str
    """[PR] Identifies the name of the head Ref associated with the pull request, even if the ref has been deleted."""

    base_ref: str
    """[PR] Identifies the name of the base Ref associated with the pull request, even if the ref has been deleted."""

    merged_at: str
    """[PR] The date and time that the pull request was merged, e.g. 2024-08-05T04:47:29Z."""

    sha: str
    """[commit] The Git object ID of the commit, a 40-character string computed using SHA-1 cryptographic hash."""

    message: str
    """[commit] The Git commit message."""

    committed_at: str
    """[commit] The datetime when this commit was committed, e.g. 2024-08-05T04:18:06Z."""

    parents: List[str]
    """[commit] The Git object IDs of parent commits to this commit."""

    def __post_init__(self):
        self.parents = [sha for sha in self.parents if sha]

"""Dataclass for failed logs of check runs."""

from dataclasses import dataclass


@dataclass
class FailedLog:
    owner: str
    """The user owner of the repository."""

    name: str
    """The name of the repository."""

    pr_number: int
    """The number for the pull request."""

    commit_sha: str
    """The Git object ID of the commit, a 40-character string computed using SHA-1 cryptographic hash."""

    check_run_name: str
    """The name of the check run."""

    database_id: int
    """The GitHub's internal unique identifier for the check run."""

    status_code: int
    """The status code of the request to retrieve the log."""

    log: str
    """The log content of the check run."""

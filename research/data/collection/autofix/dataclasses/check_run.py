"""Dataclass for check runs of pull requests."""

from dataclasses import dataclass
from typing import Any, Mapping


@dataclass
class CheckRun:
    owner: str
    """The user owner of the repository."""

    name: str
    """The name of the repository."""

    pr_number: int
    """The number for the pull request."""

    commit_sha: str
    """The Git object ID of the commit, a 40-character string computed using SHA-1 cryptographic hash."""

    # Check run information
    check_run_name: str
    status: str
    conclusion: str
    database_id: int  # GitHub's internal unique ID
    started_at: str
    completed_at: str
    details_url: str

    # Check suite information
    check_suite_status: str
    check_suite_db_id: int  # GitHub's internal unique ID

    # Workflow run information
    workflow_name: str = ""
    workflow_run_event: str = ""
    workflow_run_number: int = 0
    workflow_run_db_id: int = 0  # GitHub's internal unique ID
    workflow_run_url: str = ""

    def update_workflow_run(self, workflow_run: Mapping[str, Any]):
        """Update the workflow run information."""
        self.workflow_name = workflow_run["workflow"]["name"]
        self.workflow_run_event = workflow_run["event"]
        self.workflow_run_number = workflow_run["runNumber"]
        self.workflow_run_db_id = workflow_run["databaseId"]
        self.workflow_run_url = workflow_run["url"]


def create_empty_check_run(owner: str, name: str, pr_number: int) -> CheckRun:
    """Create an empty check run for a given PR."""
    return CheckRun(
        owner=owner,
        name=name,
        pr_number=pr_number,
        commit_sha="",
        check_run_name="",
        status="",
        conclusion="",
        database_id=0,
        started_at="",
        completed_at="",
        details_url="",
        check_suite_status="",
        check_suite_db_id=0,
    )

"""Spark runner script to execute data collection jobs at a large scale."""

import argparse
import os
import time
from pathlib import Path

from pyspark import TaskContext

from research.data.collection.autofix.collect_check_runs import main as d2_main
from research.data.collection.autofix.collect_failed_logs import main as d3_main
from research.data.collection.autofix.collect_inter_commits import main as d1_main
from research.data.collection.autofix.utils import get_github_tokens, get_job_logger
from research.data.spark import k8s_session

# List of available GitHub API tokens
TOKENS = get_github_tokens()

logger = get_job_logger(__name__)
logger.info(f"Found {len(TOKENS)} GitHub API tokens")


def main(dataset: int, input_from: int, input_to: int, batch_size: int, log_root: str):
    logger.info(
        f"Args: dataset={dataset}, input_from={input_from}, input_to={input_to}, "
        + f"batch_size={batch_size}, log_root={log_root}"
    )

    log_root_path = Path(log_root)
    if not log_root_path.exists():
        logger.info(f"Creating log root directory: {log_root_path}")
        log_root_path.mkdir(parents=True)
    else:
        logger.info(f"Log root directory already exists: {log_root_path}")
        logger.info("Cleaning up log files in the log root directory")
        for path in log_root_path.glob("*.txt"):
            path.unlink()
        logger.info("Finished cleaning up log files")

    spark = k8s_session(
        max_workers=len(TOKENS),
        conf={
            "spark.executor.cores": "8",
            "spark.task.cpus": "8",
            "spark.executor.memory": "64G",
        },
        # We can't afford to launch 50 nodes in our GCP cluster
        # with many of them just running a single spark worker pod
        # It can cost over $10K a day
        pod_anti_affinity="preferred",
        ephemeral_storage_gb=50,
    )

    num_tasks = (input_to - input_from + batch_size - 1) // batch_size
    # Values in the dataframe don't matter
    # Only the number of partitions/tasks matters
    df = spark.range(
        start=0,
        end=10_000,
        step=1,
        numPartitions=num_tasks,
    )

    job_func = {
        1: d1_main,
        2: d2_main,
        3: d3_main,
    }[dataset]

    def process_partition(partition):
        pod_name = os.environ.get("HOSTNAME", default="")
        # Hostname index is 1-based; convert to 0-based
        exec_id = int(pod_name.split("-")[-1]) - 1
        token = TOKENS[exec_id]

        task_context = TaskContext.get()
        if task_context is None:
            raise ValueError("Got None TaskContext")

        # Spark partition id is already 0-based
        partition_id = task_context.partitionId()
        task_input_from = input_from + partition_id * batch_size
        task_input_to = min(task_input_from + batch_size, input_to)

        # Write job progress to separate log files to avoid race conditions
        log_file_path = log_root_path / f"log-{exec_id}.txt"
        with open(log_file_path, "a") as f:
            f.write(f"hostname={pod_name}, exec_id={exec_id}\n")
            start_t = time.localtime()
            time_str = time.strftime("%Y-%m-%d %H:%M:%S", start_t)
            f.write(
                f"{time_str} Starting partition_id={partition_id} "
                + f"[{task_input_from}, {task_input_to})\n"
            )

        job_func(task_input_from, task_input_to, token)

        with open(log_file_path, "a") as f:
            end_t = time.localtime()
            time_str = time.strftime("%Y-%m-%d %H:%M:%S", end_t)
            f.write(
                f"{time_str} Finished partition_id={partition_id} "
                + f"[{task_input_from}, {task_input_to})\n"
            )
            # Compute and log job duration in minutes
            duration_min = int((time.mktime(end_t) - time.mktime(start_t))) // 60
            f.write(f"duration={duration_min} minutes\n")

    df.foreachPartition(process_partition)

    spark.stop()


if __name__ == "__main__":
    argparser = argparse.ArgumentParser()
    argparser.add_argument(
        "--dataset",
        type=int,
        default=1,
        help="Index of the dataset to collect: "
        + "1=inter-commits, 2=check-runs, 3=failed-logs, 4=repo-commits",
    )
    argparser.add_argument(
        "--input_from",
        type=int,
        default=0,
        help="Index of input file in the path list to start from (inclusive)",
    )
    argparser.add_argument(
        "--input_to",
        type=int,
        default=1000,
        help="Index of input file in the path list to end at (exclusive)",
    )
    argparser.add_argument(
        "--batch_size",
        type=int,
        default=1,
        help="Number of files to process per batch/task",
    )
    argparser.add_argument(
        "--log_root",
        type=str,
        required=True,
        help="Root directory for saving job progress log files",
    )
    args = argparser.parse_args()
    main(
        args.dataset,
        args.input_from,
        args.input_to,
        args.batch_size,
        args.log_root,
    )

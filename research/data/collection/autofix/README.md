Data Collection Scripts and Utils for AutoFix
==========================

# Overview

This directory contains scripts and utils for collecting data for AutoFix.
Jobs need to be executed sequentially in the following order:
    1. collect_inter_commits.py
    2. collect_check_runs.py
    3. collect_failed_logs.py
    4. collect_repo_commits.py

We also provide a Spark runner script to execute these jobs at a large scale.
Please note that many jobs will query the GitHub APIs, so it is recommended
to run at most one Spark executor per physical node. It can be achieved by
setting the `pod_anti_affinity` parameter to `required` in k8s_session().

Please find below the details of each dataset.

# Dataset-1: Intermediate Commits (inter_commits)

This dataset contains metadata of intermediate commits, such as commit sha, commit
message, parent commits, etc. for selected pull requests (PRs). Intermediate commits
are those uploaded by the PR author during the lifecycle of the PR. Once the PR is
merged, these commits may or may not be included in the default branch, depending on
actions such as rebasing, squashing, or other operations. In most cases, these
intermediate commits will be detached from the default branch of the repo.

To accurately reconstruct the PR history and restore intermediate states, it is
essential to collect these intermediate commits first.

We leverage the GitHub GraphQL API to collect this dataset. By providing the repo
owner, repo name, and number of the PR, we can retrieve the metadata of intermediate
commits for that PR. The retrieved data will be the same as presented in the GitHub
UI `https://github.com/{owner}/{name}/pull/{prNumber}/commits`. Therefore, it doesn't
cover the following cases:
- the base commit from the PR base ref;
- the final merge commit after the PR is checked in;
- previous commits that are overwritten by force-push operations.

Please view the full query (variable `QUERY`) in
`research/data/collection/autofix/collect_inter_commits.py` for more details.

We also retrieve and include some PR metadata in this dataset, such as PR title,
PR head ref, PR base ref, and PR merged time, for simplicity (not necessary to join
with the PR dataset). Since we need to query the `pullRequest` node to retrieve the
`commits`, the cost to obtain the PR metadata is low and partially covered. To view
the full schema of the dataset, please refer to
`research/data/collection/autofix/dataclasses/inter_commit.py`.

This is the first step of the AutoFix data collection process. We need intermediate
commits in selected PRs to further collect check runs and failed logs.

# Dataset-2: Check Runs (check_runs)

This dataset contains metadata of check runs for collected intermediate commits.

Concepts:
- Check Run: Represents the result of an individual CI process on GitHub, such as
running unit tests, building a project, or performing static code analysis.
- Check Suite: A collection of multiple check runs associated with a specific commit.
GitHub automatically creates a check suite when code is pushed or a pull request is
opened, grouping together all related check runs triggered for that particular commit
or pull request.
- Workflow Run: Specific to GitHub Actions, a workflow run refers to the execution of
a defined workflow. Each workflow run may consist of one or more jobs, and each job
can generate its own check run. Workflow runs are typically triggered by events such
as a push, pull request, or a scheduled event.

We also leverage the GitHub GraphQL API to collect this dataset. By providing the repo
owner, repo name, and number of the PR, we can retrieve the metadata of check runs
for that PR. The retrieved data will be the same as presented in the GitHub UI
`https://github.com/{owner}/{name}/pull/{prNumber}/checks`.

Please view the full query (variable `QUERY`) in
`research/data/collection/autofix/collect_check_runs.py` for more details.

We use a flattened structure (check run + check suite + workflow run) to represent this
dataset. Please note that workflow run information is only available for GitHub Actions
and will be empty for other CI systems. To view the full schema, please refer to
`research/data/collection/autofix/dataclasses/check_run.py`.

# Dataset-3: Failed Logs (failed_logs)

This dataset contains the contents of failed logs for check runs.

It takes the output from `collect_check_runs.py` as the input data. Then, we directly
use GitHub's API to collect the failed logs for check runs. We consider a check run
failed if its conclusion is explicitly set to "FAILURE". To avoid oversized logs, we
only save the last `SIZE_LIMIT` (set in the script) bytes of the log.

To view the full schema of the dataset, please refer to
`research/data/collection/autofix/dataclasses/failed_log.py`.

# Dataset-4: Repo Commits (repo_commits)

TODO

# Step-By-Step Guide to Collect Data

- Step 1: Run the `autofix.prs.generator` query on BigQuery to generate PRs for data collection (~1min)

Make sure to update line 24 (the `created_at` filter) to limit the query size. Ideally, we
only want to collect most recent PRs created within the last 3 months. The output table
should be saved to `augment-research-gsc.autofix.prs`.

It's expected to have ~15M PRs in the 3-month period.

Query Link: https://console.cloud.google.com/bigquery?ws=!1m7!1m6!12m5!1m3!1saugment-research-gsc!2sus-central1!3sedd520ac-0536-4632-a4d7-0587087891f9!2e1

Table Link: https://console.cloud.google.com/bigquery?ws=!1m5!1m4!4m3!1saugment-research-gsc!2sautofix!3sprs

- Step 2: Run `create_repo_prs.py` to generate the input data for next steps (~10min)

```
python research/data/collection/autofix/create_repo_prs.py
```

Results (input data to other steps) will be saved to `gs://gcp-us1-spark-data/shared/pr_v2/repo_prs`
with default 1000 partitions. Basic statistics will be printed when the script is finished.

It's expected to keep 60% data after PR size filtering on repos, around >100K repos and 10M PRs.

Data Schema:
```
root
 |-- repo_name: string (nullable = true)
 |-- pr_list: array (nullable = false)
 |    |-- element: long (containsNull = false)
```

- Step 3: Run `collect_inter_commits.py` to collect intermediate commits.

```
python research/data/collection/autofix/spark_runner.py --dataset 1 --log_root /mnt/efs/spark-data/user/zhewei/logs
```

Output files will be saved to `gs://gcp-us1-spark-data/shared/pr_v2/inter_commits_new`.

To track job progress, run below command to parse the log files (same in step 4 and 5).
```
python research/data/collection/autofix/parse_logs.py --log_root /mnt/efs/spark-data/user/zhewei/logs
```

- Step 4: Run `collect_check_runs.py` to collect check runs.

```
python research/data/collection/autofix/spark_runner.py --dataset 2 --log_root /mnt/efs/spark-data/user/zhewei/logs
```

Output files will be saved to `/mnt/efs/spark-data/shared/pr_v2/check_runs_new`.

- Step 5: Run `collect_failed_logs.py` to collect failed logs.

```
python research/data/collection/autofix/spark_runner.py --dataset 3 --log_root /mnt/efs/spark-data/user/zhewei/logs
```

Output files will be saved to `/mnt/efs/spark-data/shared/pr_v2/failed_logs_new`.

- Step 6: Merge `_new` folders to the original data folders.

TODO

- Step 7: Run `collect_repo_commits.py`

TODO

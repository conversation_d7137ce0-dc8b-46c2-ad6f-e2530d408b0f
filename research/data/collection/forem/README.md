Forem sites scrapers
=====


forem is in principle a forum builder, but mainly powers blog-like sites


Most notable sites include

- DEV.to
- Julia forem
- Ops community
- Forem builders
- Wasm builders
- TypeOverflow
- COSS community
- CodeNewbie


The API is not super convenient as we can only pull one article at a time when we use
the `/api/articles/{id}` endpoint.

`/api/articles/latest?page=XXX` can be useful for more effective extraction, but the
page number is not very reliable.  This is because we can only start from the most recent
page and the page number each post belongs to would change over time.

To deal with this, we store the current page number and oldest/newest post seen for each
site.  In general we proceed along the page count but we also try and estimate how many pages
we are missing on either end, and if we should skip some pages.


We are currently not scraping comments, because they are mostly non-informative ones
like "great article" and "thank you"

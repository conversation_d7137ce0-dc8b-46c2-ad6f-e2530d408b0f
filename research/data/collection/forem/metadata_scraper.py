"""Scrape forem sites to extract page metadata."""

import argparse
import contextlib
import datetime
import json
import logging
import time
import uuid
from dataclasses import dataclass
from pathlib import Path

import requests

import research.data.postgres_metastore.utils as pg_utils

logger = logging.getLogger(__name__)


HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"
}


@dataclass
class ForemSite:
    """A site to scrape."""

    site_id: str
    """The site ID."""

    url: str
    """The url of the site."""

    page: int
    """The current page we are scraping."""

    latest_id: int
    """The latest article ID we have scraped."""

    latest_at: str
    """Publication time of the latest article we have scraped."""

    earliest_id: int
    """The earliest article ID we have scraped."""

    earliest_at: str
    """Publication time of the earliest article we have scraped."""

    has_earlier_articles: bool
    """Whether we have reached the earliest article."""

    last_refresh_at: str
    """Time of the last refresh from the latest article."""

    last_extended_at: str
    """Time of the last time we extended the site."""

    refresh_run: bool = False
    """Whether we are refreshing from latest after a complete scan."""


@contextlib.contextmanager
def acquire_site(unlock_days=2):
    """Yields a site to scrape and lock it for the duration."""
    query = """
       WITH target AS (
           SELECT
               id
           FROM forem_site
           WHERE NOT disabled AND
              (NOT locked or locked_at < NOW() - INTERVAL %s)
              AND
              (
                has_earlier_articles
                -- OR last_refresh_at < NOW() - INTERVAL
              )
           ORDER BY
                DATE(last_extended_at) DESC NULLS LAST,
                earliest_article_id DESC
           LIMIT 1
           FOR UPDATE SKIP LOCKED
       )
       UPDATE forem_site
       SET locked=TRUE, locked_at=NOW()
       FROM target
       WHERE forem_site.id = target.id
       RETURNING
           forem_site.id,
           url, current_page,
           latest_article_id,
           latest_article_at,
           earliest_article_id,
           earliest_article_at,
           has_earlier_articles,
           last_refresh_at,
           last_extended_at

    """
    result = pg_utils.execute(query, [f"{unlock_days} DAYS"])
    if not result:
        yield None
        return

    try:
        site = ForemSite(*result[0])
        if not site.has_earlier_articles:
            # Not yet implement
            raise NotImplementedError("Refreshing is not yet implemented.")
            # Reset page count if we have reached the end
            # site.page = 1
            # site.refresh_run = True
            # site.last_refresh_at = datetime.datetime.now().isoformat()
        logger.info("Scraping %s page %s", site.url, site.page)
        yield site
        # The entries will be updated.  Now save to db
        query = """
            UPDATE forem_site
            SET locked=FALSE,
                locked_at=NULL,
                current_page = %s,
                latest_article_id = %s,
                latest_article_at = %s,
                earliest_article_id = %s,
                earliest_article_at = %s,
                has_earlier_articles = %s,
                last_refresh_at = %s,
                last_extended_at = %s
            WHERE id = %s
        """
        logger.info("Saving scraped info for %s.", site.url)
        pg_utils.execute(
            query,
            [
                site.page,
                site.latest_id,
                site.latest_at,
                site.earliest_id,
                site.earliest_at,
                site.has_earlier_articles,
                site.last_refresh_at,
                site.last_extended_at,
                site.site_id,
            ],
        )
    except Exception as e:  # pylint: disable=broad-except
        logger.error("Failed to acquire site: %s", e)
        query = """
            UPDATE forem_site
            SET locked=FALSE,
                locked_at=NULL
            WHERE id = %s
        """
        pg_utils.execute(query, [site.site_id])


def extract_page(
    site: ForemSite,
    retries: int = 3,
    retry_delay: float = 10,
    retry_backoff_factor: float = 2,
    timeout: float = 10,
):
    """Extract a single page of articles."""
    request_url = f"{site.url}/api/articles/latest?page={site.page}"
    logger.debug("Sending request to %s", request_url)
    for _ in range(retries):
        try:
            resp = requests.get(request_url, headers=HEADERS, timeout=timeout)
            resp.raise_for_status()
            result = resp.json()
            logger.debug("Received %d articles", len(result))
            if result:
                # update latest and earliest article ids
                result.sort(key=lambda x: x["id"])
                if site.latest_id is None or result[-1]["id"] > site.latest_id:
                    site.latest_id = result[-1]["id"]
                    site.latest_at = result[-1]["published_timestamp"]
                if site.earliest_id is None or result[0]["id"] < site.earliest_id:
                    site.earliest_id = result[0]["id"]
                    site.earliest_at = result[0]["published_timestamp"]
                site.last_extended_at = datetime.datetime.now().isoformat()
                site.has_earlier_articles = True
                site.page += 1
            else:
                # Depleted all articles
                logger.info("No more articles to extract for %s", site.url)
                site.has_earlier_articles = False
            break
        except Exception as e:  # pylint: disable=broad-except
            logger.error("Failed to extract page: %s", e)
            logger.info("Retrying in %s seconds", retry_delay)
            time.sleep(retry_delay)
            retry_delay *= retry_backoff_factor
            result = None
    return result


def refresh_site(site: ForemSite, pages: int, delay_seconds: float):
    """Refresh a site."""
    if site.refresh_run:
        raise NotImplementedError("Refreshing is not yet implemented.")
    results = []
    for _ in range(pages):
        logger.info("Refreshing %s page %s", site.url, site.page)
        new_result = extract_page(site)
        if not new_result or not site.has_earlier_articles:
            break
        results.extend(new_result)
        time.sleep(delay_seconds)
    return results


def refresh_one_site(storage_path: Path, pages: int, delay_seconds: float):
    """Identify and refresh a single site and save the results to disk."""
    with acquire_site() as site:
        if site is None:
            logger.info("No more sites to refresh")
            return
        results = refresh_site(site, pages, delay_seconds)
        if results:
            min_id = min(result["id"] for result in results)
            max_id = max(result["id"] for result in results)
            output_id = str(uuid.uuid4())[:8]
            site_domain = site.url.split("://")[1].rstrip("/")
            output_path = (
                storage_path
                / f"site={site_domain}"
                / f"articles_{min_id}-{max_id}_{output_id}.json"
            )
            output_path.parent.mkdir(parents=True, exist_ok=True)
            logger.info(
                "Saving %d articles for %s at %s",
                len(results),
                site.url,
                output_path.as_posix(),
            )

            with output_path.open("w+", encoding="utf8") as file:
                for entry in results:
                    file.write(json.dumps(entry) + "\n")


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s %(levelname)s %(message)s",
    )
    parser = argparse.ArgumentParser(description="Scrape forem sites.")
    parser.add_argument(
        "-p",
        "--pages",
        type=int,
        default=1000,
        help="Number of pages to scrape per site.",
    )
    parser.add_argument(
        "-d",
        "--delay",
        type=float,
        default=0.5,
        help="Delay between requests to the same site.",
    )
    parser.add_argument(
        "-s",
        "--storage",
        type=str,
        default="/mnt/efs/spark-data/shared/nl-datasets/forem/metadata/",
        help="Path to store scraped data.",
    )
    args = parser.parse_args()

    refresh_one_site(Path(args.storage), args.pages, args.delay)

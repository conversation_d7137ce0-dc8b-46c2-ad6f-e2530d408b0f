CREATE TABLE forem_site (
   id uuid not null default uuid_generate_v4(),
   name varchar,
   url varchar not null,
   created_at timestamptz not null default NOW(),
   current_page bigint not null default 1,
   latest_article_at timestamptz,       -- Newest known publication time
   latest_article_id bigint,            -- Index of the newest article scraped
   earliest_article_at timestamptz,     -- Oldest known publication time
   earliest_article_id bigint,          -- Index of the oldest article scraped
   has_earlier_articles boolean not null default TRUE,    -- Have we reached the oldest article yet?
   last_refresh_at timestamptz,         -- Time of the last refresh from the latest article.
   last_extended_at timestamptz,        -- Time of the last time we extended the site.
   disabled boolean not null default false,
   locked boolean not null default false,
   locked_at timestamptz                -- When was this article locked
);



INSERT INTO forem_site (name, url)
VALUES
  ('DEV.to', 'https://dev.to'),
  ('COSS', 'https://www.coss.community'),
  ('<PERSON>em', 'https://forem.julialang.org'),
  ('TypeOverflow', 'https://typeoverflow.com'),
  ('Forem Builders', 'https://forem.dev'),
  ('Wasm Builders', 'https://www.wasm.builders'),
  ('CodeNewbie', 'https://community.codenewbie.org'),
  ('Ops Community', 'https://community.ops.io'),
  ('Interledger', 'https://community.interledger.org');

"""Scrape forem sites to extract article content."""

import argparse
import json
import logging
import time
from pathlib import Path
from urllib.parse import urlparse

import requests

logger = logging.getLogger(__name__)


HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"
}

REFERENCE_PATH = Path("/mnt/efs/spark-data/shared/nl-datasets/forem/metadata/")


def download_articles(
    input_path: Path,
    output_path: Path,
    delay: float = 1.0,
    reference_path: Path = REFERENCE_PATH,
):
    """Download articles from a list of sites."""
    if not input_path.exists():
        raise ValueError(f"Input path {input_path} does not exist.")
    if not input_path.is_file():
        raise ValueError(f"Input path {input_path} is not a file.")
    input_path = input_path.expanduser().resolve()
    logger.info("Extracting metadata from %s", input_path)
    output_path = output_path.expanduser().resolve()
    if input_path.is_relative_to(reference_path):
        output_file = output_path / input_path.relative_to(reference_path)
    else:
        output_file = output_path / input_path.name
    output_file.parent.mkdir(parents=True, exist_ok=True)
    logger.info("Downloading articles to %s", output_file)

    seen: set[int] = set()
    with (
        input_path.open("r", encoding="utf8") as input_file,
        output_file.open("w+", encoding="utf8") as output_file,
    ):
        for line in input_file:
            doc = json.loads(line)
            article_id = doc["id"]
            if article_id in seen:
                continue
            seen.add(article_id)
            raw_url = doc["url"]
            # Extract site domain from page url and use id to construct api url
            site_domain = urlparse(raw_url).netloc
            api_url = f"https://{site_domain}/api/articles/{article_id}"
            # try 3 times
            wait_time = delay
            for _ in range(5):
                try:
                    time.sleep(wait_time)
                    resp = requests.get(api_url, headers=HEADERS)
                    # 404 means article is not available, which shouldn't be retried
                    if resp.status_code == 404:
                        logger.info(
                            "Article %s is not available. (URL=%s)", article_id, api_url
                        )
                        break
                    resp.raise_for_status()
                    result = resp.json()
                    output_file.write(json.dumps(result) + "\n")
                    break
                except Exception as e:  # pylint: disable=broad-except
                    logger.error("Failed to extract article: %s", e)
                    logger.info("Retrying in %s seconds", wait_time)
                    wait_time *= 2


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s %(levelname)s %(message)s",
    )
    parser = argparse.ArgumentParser(description="Scrape forem sites.")
    parser.add_argument(
        "--input",
        type=str,
        required=True,
        help="Input metadata file.",
    )
    parser.add_argument(
        "-d",
        "--delay",
        type=float,
        default=0.5,
        help="Delay between requests to the same site.",
    )
    parser.add_argument(
        "--output",
        type=str,
        default="/mnt/efs/spark-data/shared/nl-datasets/forem/articles/",
        help="Path to store scraped articles.",
    )
    parser.add_argument(
        "--reference",
        type=str,
        default=REFERENCE_PATH,
        help="Path to reference articles.",
    )
    args = parser.parse_args()

    download_articles(
        Path(args.input), Path(args.output), args.delay, Path(args.reference)
    )
    print("Done")

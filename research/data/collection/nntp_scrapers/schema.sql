CREATE TABLE nntp_server (
   id uuid not null default uuid_generate_v4(),
   name varchar not null,
   description varchar,
   url varchar not null,
   port int not null default 119,
   username varchar,
   password varchar,
   disabled boolean not null default true
);

-- Table to store Gmane group informtion
CREATE TABLE nntp_group (
    server_id uuid not null,    -- ID of the NNTP server
    name varchar not null,
    start_index int not null,
    end_index int not null,
    created_at timestamptz not null default NOW(),
    updated_at timestamptz not null default NOW(),
    disabled boolean not null default True,
    locked boolean not null default False,
    last_processed_index int,
    additional_info jsonb not null default '{}'::jsonb,
    last_processed_at timestamptz
);

CREATE UNIQUE INDEX nntp_group_name_idx ON nntp_group (server_id, name);

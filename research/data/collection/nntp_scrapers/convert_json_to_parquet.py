"""Convert JSON dumps to a parquet snapshot, and do some basic filtering and cleanup.

Clean up include removing signatures, nested quotes, and trailing quotes.

Filtering remove threads with only one message from gmane.
"""

import logging

import pyspark.sql.functions as F

from research.data.spark import k8s_session

DISABLE = [
    "gmane.os.freebsd.devel.pkg-fallout",
    "gmane.comp.gcc.test-results",
    "gmane.comp.gdb.testing",
]
logging.basicConfig(level=logging.INFO)
spark = k8s_session(
    max_workers=100,
)

MAX_SIGNATURE_LINES = 15


def cleanup_message_body(text):
    """Remove signatures, nested and trailing quote blocks."""
    text = text.encode(
        encoding="utf-8", errors="ignore"
    ).decode()  # remove invalid characters
    lines = text.splitlines()
    lines = [
        line
        for line in lines
        if not (line.strip().startswith(">>") or line.strip().startswith("> >"))
        and line.strip().lower() != "[[alternative html version deleted]]"
    ]

    # scan from the last line to find signature separator.
    separator_chars = ("-", "=", "\u2013", "\u2014", "_", "*", "+", "#")
    last_line = ""
    for i in range(len(lines) - 2, max(0, len(lines) - MAX_SIGNATURE_LINES), -1):
        line = lines[i].strip()
        if (
            line.startswith(separator_chars)
            and len(set(line)) == 1
            and (
                "subscribe" in last_line
                or "reply" in last_line
                or "mailing list" in last_line
                or "mailing-list" in last_line
            )
        ):
            lines = lines[:i]
            break
        if line:
            last_line = line.lower()

    # Remove the trailing quote
    for i in range(len(lines) - 1, 0, -1):
        line = lines[i].strip()
        if not line:
            continue
        if not line.startswith(">"):
            end = i
            while end and not lines[end - 1].strip():
                end -= 1
            if end and lines[end - 1].strip().endswith(":"):
                end -= 1
            lines = lines[: end + 1]
            break

    # Remove original message
    for i, line in enumerate(lines):
        if "- original message -" in line.strip().lower():
            lines = lines[:i]
            break
    return "\n".join(lines) + "\n"


df = spark.read.json("/mnt/efs/spark-data/shared/nl-datasets/nntp/threads/")
df = df.select("additional_info.group", "server", "subject", "messages").filter(
    ~F.col("group").isin(DISABLE)
)
df = df.withColumn(
    "source",
    F.when(F.col("server") == "eternal-september", "usenet")
    .when(
        F.col("group").startswith("gwene."),
        "gwene",
    )
    .otherwise("gmane"),
).filter(
    ~F.col("source").startswith("gmane")
    | (F.size("messages") > 1)
    | (F.col("subject").startswith("Re: "))
)


@F.udf(df.schema["messages"].dataType)
def fix_messages(messages):
    output = []
    for message in messages:
        doc = message.asDict()
        doc["body"] = cleanup_message_body(doc["body"])
        if doc["body"].strip():
            output.append(doc)
    return output


df.withColumn("messages", fix_messages("messages")).repartition(2000).write.partitionBy(
    "source"
).mode("overwrite").parquet("/mnt/efs/spark-data/shared/nl-datasets/nntp/parquet/")

spark.stop()

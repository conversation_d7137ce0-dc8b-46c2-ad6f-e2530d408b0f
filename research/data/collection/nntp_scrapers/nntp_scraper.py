"""Utils to scrape NNTP servers.

This handles the business logic of identifying servers and
groups to scrape, bookkeeping with the postgres table,
queue tasks and uploading the results.
"""

import argparse
import datetime
import logging
import uuid
from pathlib import Path
from typing import Optional, Union

import research.data.postgres_metastore.utils as pg_utils
from research.data.collection.utils import nntp
from research.data.collection.utils.message import collect_threads, save_threads

logger = logging.getLogger(__name__)


def refresh_group_list(
    server_id: Union[str, uuid.UUID],
    url: str,
    username: Optional[str],
    password: Optional[str],
    port: int = 119,
    secured: bool = False,
):
    """Extract all group info from a site and refresh the group table."""
    with nntp.connect(url, username, password, port=port, secured=secured) as client:
        newsgroups = nntp.list_groups(client)
        query = """
            INSERT INTO nntp_group (server_id, name, start_index, end_index)
            VALUES %s
            ON CONFLICT (server_id, name) DO UPDATE
                SET start_index=EXCLUDED.start_index,
                end_index=EXCLUDED.end_index,
                updated_at=NOW();
        """
        values = [
            (server_id, group["name"], group["first"], group["last"])
            for group in newsgroups
        ]
        pg_utils.batch_execute(query, values)


def refresh_all_group_lists(
    allowed_servers: tuple[str, ...] = ("eternal-september", "gmane"),
):
    """Refresh the group list info of all known groups."""
    query = """
        SELECT id, url, username, password, port, secured
        FROM nntp_server
        WHERE name in %s
    """
    for server_id, url, username, password, port, secured in pg_utils.execute(
        query, [allowed_servers]
    ):
        logger.info("Refreshing group list for NNTP server at %s", url)
        refresh_group_list(
            server_id, url, username, password, port=port, secured=secured
        )


def update_one_group(
    storage_path: Path,
    limit: int = 20000,
    cooldown=0.2,
    allowed_servers: tuple[str, ...] = ("eternal-september", "gmane"),
    username: str = "",
    password: str = "",
):
    """Pick a single group and fetch new messages."""
    # first identify a group to update
    job_id = str(uuid.uuid4())[:6]
    query = """
        WITH available AS (
            SELECT g.server_id, g.name
            FROM nntp_group AS g
            JOIN nntp_server AS s ON s.id = g.server_id
            WHERE NOT g.disabled AND NOT g.locked AND not s.disabled
                AND s.name IN %s AND g.end_index > COALESCE(last_processed_index, start_index-1)
                AND (last_processed_at IS NULL OR last_processed_at < NOW() - INTERVAL '1 day')
            ORDER BY end_index - COALESCE(last_processed_index, start_index-1) DESC NULLS FIRST
            LIMIT 1
            FOR UPDATE SKIP LOCKED
        ),
        updated AS (
            UPDATE nntp_group
            SET last_processed_at=NOW(),
                locked=TRUE
            FROM available
            WHERE nntp_group.name = available.name AND nntp_group.server_id = available.server_id
            RETURNING
              nntp_group.server_id,
              nntp_group.name,
              COALESCE(last_processed_index + 1, start_index) AS start_index
        )
        SELECT
            updated.server_id,
            s.name AS server_name,
            s.url AS server_url,
            s.username, s.password,
            updated.name AS group_name,
            updated.start_index,
            s.port,
            secured
        FROM updated
        JOIN nntp_server AS s ON s.id = updated.server_id
    """
    results = pg_utils.execute(query, [allowed_servers])
    if not results:
        logger.warning("No groups to update")
        return
    server_id, server_name, url, user, pw, group_name, start, port, secured = results[0]
    logger.info(
        "[%s] Updating group %s at %s from %d with limit %d",
        job_id,
        group_name,
        url,
        start,
        limit,
    )
    username = username or user
    password = password or pw

    logger.info("    Using user %s to access the group.", username)
    tags = {
        "server": server_name,
        "server_id": server_id,
        "server_url": url,
        "group": group_name,
        "job_id": job_id,
        "accessed_by": username,
    }
    # Fetch messages and store to JSONL files
    results = []
    known = {}
    try:
        article_source = nntp.NNTPArticleSource(
            url,
            group_name,
            start=start,
            limit=limit,
            username=username,
            password=password,
            cooldown=cooldown,
            port=port,
            secured=secured,
        )

        logger.debug("Number of articles to fetch: %d", len(article_source.indexes))
        save_threads(
            storage_path / f"server={server_name}",
            f"{group_name}_{job_id}",
            collect_threads(article_source.iter_articles(), tags, known=known),
            compress=True,
        )
    except Exception:  # pylint: disable=broad-except
        logger.exception("Exception while processing threads.")
        logger.info("Resetting lock status")
        query = """
            UPDATE nntp_group
            SET last_processed_at=NOW(),
                locked=FALSE
            WHERE server_id=%s and name=%s;
        """
        pg_utils.execute(query, (server_id, group_name))
        return
    if article_source.indexes:
        logger.warning(
            "Not targeted articles were processed. %d remain",
            len(article_source.indexes),
        )
    if article_source.processed:
        logger.info("Last processed index: %d", article_source.processed[-1])
        logger.info(
            "Articles skipped due to problems: %s",
            ", ".join(map(str, article_source.skipped)) or "None",
        )
        query = """
            UPDATE nntp_group
            SET last_processed_index=%s,
                last_processed_at=NOW(),
                locked=FALSE
            WHERE server_id=%s and name=%s;
        """
        pg_utils.execute(query, (article_source.processed[-1], server_id, group_name))
    else:
        logger.info("No articles processed")
        pg_utils.execute(
            """UPDATE nntp_group
                SET last_processed_at=NOW(),
                    locked=FALSE,
                    last_processed_index=%s
                WHERE server_id=%s and name=%s
            """,
            (
                max(article_source.last_nonexist, start)
                if article_source.last_nonexist
                else start,
                server_id,
                group_name,
            ),
        )


def get_args():
    current_date = datetime.date.today()

    parser = argparse.ArgumentParser(description="Update nntp groups.")
    parser.add_argument(
        "--storage-path",
        type=str,
        default=f"/mnt/efs/spark-data/shared/nl-datasets/nntp/threads/date={current_date}/",
        help="Path to store the JSONL files.",
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=50000,
        help="Limit the number of articles to fetch.",
    )
    parser.add_argument(
        "--cooldown", type=float, default=0.2, help="Cooldown time between requests."
    )
    parser.add_argument(
        "--groups", type=int, default=5, help="Number of groups to update."
    )
    parser.add_argument(
        "--servers",
        type=str,
        nargs="+",
        default=["gmane", "eternal-september"],
        help="Servers to include.",
    )
    parser.add_argument(
        "--username", type=str, default="", help="Optionally override the username."
    )
    parser.add_argument(
        "--password", type=str, default="", help="Optionally override the password."
    )
    parser.add_argument(
        "--refresh", action="store_true", help="Also refresh the group list"
    )
    parser.add_argument(
        "--debug-logs", action="store_true", help="enable debug logging"
    )
    args = parser.parse_args()
    return args


def main():
    args = get_args()
    if args.debug_logs:
        logging.basicConfig(level=logging.DEBUG)
    else:
        logging.basicConfig(level=logging.INFO)

    storage_path = Path(args.storage_path)
    storage_path.mkdir(parents=True, exist_ok=True)

    servers = tuple(args.servers)

    if args.refresh:
        refresh_all_group_lists(allowed_servers=servers)

    for _ in range(args.groups):
        update_one_group(
            storage_path,
            args.limit,
            args.cooldown,
            allowed_servers=servers,
            username=args.username,
            password=args.password,
        )


if __name__ == "__main__":
    main()

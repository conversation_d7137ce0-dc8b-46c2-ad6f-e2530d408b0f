apiVersion: apps/v1
kind: Deployment
metadata:
  name: gmane-scrape
spec:
  replicas: 10
  selector:
    matchLabels:
      app: gmane-scrape
  template:
    metadata:
      labels:
        app: gmane-scrape
    spec:
      containers:
      - name: gmane-scraper
        image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/github-download-worker:spark-3.4.2-28
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        resources:
          requests:
            cpu: 1
            memory: "4Gi"      # Set the requested memory
        command: ["python"]
        args:
        - "-m"
        - "research.data.collection.nntp_scrapers.nntp_scraper"
        - "--servers"
        - "gmane"
        - "--cooldown"
        - "0.1"
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: metastore-postgresql
              key: password
        - name: PGHOST
          value: "metastore-postgresql"
        - name: PGDATABASE
          value: "metastore"
        - name: PGUSER
          value: "augment"
        - name: CW_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: tenant-augment-eng-determined-app-obj-store-creds
              key: accessKey
        - name: CW_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: tenant-augment-eng-determined-app-obj-store-creds
              key: secretKey
        volumeMounts:
        - mountPath: /mnt/efs/spark-data
          name: viofs-aug-cw-las1-spark-data
      volumes:
      - name: viofs-aug-cw-las1-spark-data
        persistentVolumeClaim:
          claimName: aug-cw-las1-spark-data
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                - key: topology.kubernetes.io/region
                  operator: In
                  values:
                    - LAS1

"""Process discourse texts, tokenize, mask out headers, and pack into samples."""

import argparse
import logging
import re
from functools import partial
from typing import Any

from research.data.collection.utils.markdown_conversion import (
    remove_data_urls,
    robust_conversion,
)
from research.data.collection.utils.tokenize_with_masking import (
    MaskableText,
    tokenize_and_pack,
)
from research.data.spark import k8s_session
from research.data.spark.pipelines.utils import map_parquet

logging.basicConfig(level=logging.INFO)

BASE_PATH = "/mnt/efs/spark-data/shared/nl-datasets/nntp/"
CONTENT_COL = "messages"

WORD_SEPERATE = re.compile(r"\W+")


def check_word_size_ok(
    text: str, max_word_size: int = 64, max_average_word_size: int = 16
) -> bool:
    """Check if the block has long words.

    This is a heuristic to filter out blocks that are likely to be encoded data.
    """
    words = WORD_SEPERATE.split(text)
    word_sizes = [len(word) for word in words]
    return (
        max(word_sizes) < max_word_size
        and sum(word_sizes) / len(word_sizes) < max_average_word_size
    )


def split_blocks(message: str, keep_single_quotes: bool = False) -> list[MaskableText]:
    """Convert a message to a list of blocks.

    Each block is either a quote block or a content block.
    Quote blocks will be masked out.

    Returns a list of MaskableText objects.
    """
    lines = message.splitlines()
    blocks: list[MaskableText] = []
    current_block = []
    in_quote = False
    for i, line in enumerate(lines):
        chars = set(line.strip())
        if (
            i < len(lines) - 1
            and (len(chars) == 1 or chars == {"+", "-"})
            and line.strip()[0] in ("-", "=", "\u2013", "\u2014", "_", "*", "+", "#")
            and (
                "reply" in lines[i + 1].lower()
                or "subscribe" in lines[i + 1].lower()
                or "mailing list" in lines[i + 1].lower()
                or "notification" in lines[i + 1].lower()
                or "mailing-list" in lines[i + 1].lower()
                or "notify" in lines[i + 1].lower()
            )
        ):
            lines = lines[:i]
            break
    for line in lines:
        # Skip double quotes
        if line.strip().startswith(">>") or line.strip().startswith("> >"):
            continue
        is_quote = line.strip().startswith(">")
        if is_quote != in_quote and current_block:
            if (
                is_quote
                and len([line for line in current_block if line.strip()]) == 1
                and current_block[0].endswith(":")
            ):
                in_quote = True
            elif line.strip():  # empty lines alone do not break quotes
                new_text = "\n".join(current_block).strip() + "\n\n"
                if check_word_size_ok(new_text):
                    blocks.append(MaskableText(new_text, in_quote))
                current_block = []
                in_quote = is_quote
        current_block.append(line)
    if current_block and any(line.strip() for line in current_block):
        new_text = "\n".join(current_block).strip() + "\n\n"
        if check_word_size_ok(new_text):
            blocks.append(MaskableText(new_text, in_quote))

    # Clean up quotations if necessary
    if not keep_single_quotes:
        quote_len = [len(block.text.splitlines()) for block in blocks if block.masked]
        # Remove single long quotes
        if len(quote_len) == 1 and quote_len[0] > 3:
            # If that quote is the first block, remove it
            if blocks[0].masked:
                blocks = blocks[1:]
            # otherwise only keep the last lines of the quote block
            else:
                for block in blocks:
                    if block.masked:
                        block.text = "\n".join(block.text.splitlines()[-1:])

    for block in blocks:
        if (
            block.text.count("</span>") >= 3
            or block.text.count("<p>") >= 3
            or block.text.strip().startswith("<")
        ):
            try:
                block.text = (
                    robust_conversion(block.text, keep_links=False)
                    .encode("utf-8", errors="ignore")
                    .decode("utf-8")
                )
            except Exception:
                pass
    return blocks


def process_gwene(thread: dict[str, Any]):
    """Gwene threads are not conversations, and they are HTML."""
    if not thread["messages"] or "body" not in thread["messages"][0]:
        return
    text = thread["messages"][0]["body"]
    if text.strip().startswith("<") or text.count("<") > 10:
        text = robust_conversion(text, timeout_ms=500, keep_links=False)
    yield MaskableText(text, False)


def process_thread(
    thread: dict[str, Any],
    separator="\n---\n\n",
    body_length_limit: int = 10000,
):
    """Process a single thread and return MaskableText."""
    for i, message in enumerate(thread["messages"]):
        if not message.get("body") and not message.get("attachments"):
            continue
        # Format header
        header = f"{message['from_']} | {message['date']}\n\n"
        if not i:
            header = f"## {thread['subject']}\n\n" + header
        else:
            header = separator + header
        yield MaskableText(header, True)

        # Format content body
        text = remove_data_urls(message["body"])
        if len(text) > body_length_limit:
            text = text[:body_length_limit].rsplit("\n", 1)[0]
            text = text + "\n\n[Long message truncated]\n\n"
        yield from split_blocks(text)
        for attachment in message["attachments"]:
            yield MaskableText("### Attached file content:\n\n", True)
            yield MaskableText(remove_data_urls(attachment["content"]) + "\n\n", False)


def main(args: argparse.Namespace):
    spark = k8s_session(
        max_workers=args.workers,
        conf={"spark.executor.memory": "100G"},
    )

    for source in ["gwene", "usenet", "gmane"]:
        tokenize = partial(
            tokenize_and_pack,
            text_generator=process_gwene if source == "gwene" else process_thread,
            tokenizer_name=args.tokenizer,
            seq_length=args.context_length,
            add_one_token=not args.no_add_one_token,
            max_tail_pad=args.max_trail_pad,
        )

        map_parquet.apply_pandas(
            spark,
            tokenize,
            f"{args.input_path}/source={source}",
            f"{args.output_path}/source={source}",
            batch_size=args.batch_size,
            timeout=args.timeout_seconds,
            task_info_location=args.task_info_location,
            ignore_error=True,
        )
    spark.stop()


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--tokenizer", type=str, default="deepseek_coder_base")
    parser.add_argument("--context-length", type=int, default=4096)
    parser.add_argument("--no-add-one-token", action="store_true", default=False)
    parser.add_argument("--max-trail-pad", type=int, default=410)
    parser.add_argument("--workers", type=int, default=50)
    parser.add_argument("--batch-size", type=int, default=500)
    parser.add_argument("--timeout-seconds", type=int, default=60 * 30)
    parser.add_argument(
        "--task-info-location",
        type=str,
        default="/mnt/efs/spark-data/temp_weekly/nntp/tokenize/",
    )
    parser.add_argument(
        "--input-path",
        type=str,
        default=f"{BASE_PATH}/parquet",
        help="Input text dataframe location",
    )
    parser.add_argument(
        "--output-path",
        type=str,
        default=f"{BASE_PATH}/tokenized",
        help="Output packed dataset location",
    )
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()
    main(args)

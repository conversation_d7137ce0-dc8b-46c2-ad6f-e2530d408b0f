import pyspark.sql.functions as F

from research.data.spark import k8s_session

spark = k8s_session(
    max_workers=30,
)


df = (
    spark.read.parquet("/mnt/efs/spark-data/shared/aug-stack/kaggle_notebooks/parquet/")
    .filter(F.col("lang") != "ipynb")
    .select("lang", "content")
)

df = df.union(
    spark.read.parquet(
        "/mnt/efs/spark-data/shared/aug-stack/kaggle_notebooks/converted"
    ).select(
        F.lit("ipynb").alias("lang"),
        F.col("converted-script").alias("content"),
    )
)


def filter_word_length(df, pattern: str, max_len: int, max_avg_len: int):
    df = (
        df.withColumn("words", F.split(F.col("content"), "\\W+"))
        .withColumn("word_lengths", F.transform(F.col("words"), F.length))
        .drop("words")
        .withColumn("max_word_length", F.array_max("word_lengths"))
        .withColumn(
            "average_word_length",
            F.expr(
                "AGGREGATE(word_lengths, CAST(0 AS DOUBLE), (a, x) -> a + x, a -> a / SIZE(word_lengths))"
            ),
        )
        .filter(
            (F.col("max_word_length") < max_len)
            & (F.col("average_word_length") < max_avg_len)
        )
    )
    return df.select("lang", "content")


df = filter_word_length(df, "\\W+", 64, 16)
df = filter_word_length(df, "\\s+", 150, 20)

df.write.parquet("/mnt/efs/spark-data/shared/aug-stack/kaggle_notebooks/filtered")

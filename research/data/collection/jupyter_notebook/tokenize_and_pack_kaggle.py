"""Tokenize and pack ipynb files."""

import logging
import re
from functools import partial
from typing import Iterable

import numpy as np

from research.data.collection.utils.tokenize_with_masking import (
    MaskableText,
    tokenize_and_pack,
)
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.utils import k8s_session

logging.basicConfig(level=logging.INFO)


# What ratios of output would be unmasked.
OUTPUT_FIT_RATE = 0.2


def mask_ipynb(row: dict) -> Iterable[MaskableText]:
    """Mask out non-human generated content from ipynb files.

    This includes:
    - Cell separators (`# %%`)
    - Input and output indicator (`# In [9]:`, `# Out[1]:` etc)
    - Cell output (i.e. anything from # Out line to the next `# %%`) have a
    chance to be masked out.
    """
    current_block_masked = False
    current_block_lines = []
    in_output = False
    mask_output = False

    in_pattern = re.compile(r"# In ?\[[0-9]+\]:")
    out_pattern = re.compile(r"# Out ?\[[0-9]+\]:")

    for line in row["content"].splitlines():
        line_masked = True
        if line == "# %%" or in_pattern.fullmatch(line):
            in_output = False
        elif out_pattern.fullmatch(line):
            # roll the dice to see if we want to mask this output
            mask_output = OUTPUT_FIT_RATE < np.random.rand()
            in_output = True
        else:
            line_masked = in_output and (
                mask_output
                or line == "# ..."
                or line.startswith("# <")
                and line.endswith(">")
            )

        if line_masked != current_block_masked:
            if current_block_lines:
                yield MaskableText(
                    "\n".join(current_block_lines) + "\n", current_block_masked
                )
            current_block_masked = line_masked
            current_block_lines = []
        current_block_lines.append(line)

    if current_block_lines and not current_block_masked:
        yield MaskableText("\n".join(current_block_lines) + "\n", current_block_masked)


tokenize = partial(
    tokenize_and_pack,
    text_generator=mask_ipynb,
    seq_length=4096 + 1,
    tokenizer_name="llama3_instruct",
)


def main():
    spark = k8s_session(
        max_workers=100,
        conf={
            "spark.executor.memory": "180G",
            "spark.executor.pyspark.memory": "150G",
        },
    )

    map_parquet.apply_pandas(
        spark,
        tokenize,
        "/mnt/efs/spark-data/shared/aug-stack/kaggle_notebooks/deduped/",
        "/mnt/efs/spark-data/shared/aug-stack/kaggle_notebooks/tokenized/llama3_instruct-4k/",
        batch_size=100,
        timeout=60 * 60,
        task_info_location="/mnt/efs/spark-data/temp_weekly/map_parquet_task_logs/tokenize_and_pack/",
        ignore_error=True,
    )

    spark.stop()


if __name__ == "__main__":
    main()

"""Job to convert ipynb files to scripts.

Raw ipynb are filtered from augment collected dataset by extension:

s3://augment-github/file_content/converted

"""

import logging

from research.data.collection.utils.jupyternb_conversion import convert_files
from research.data.spark import k8s_session
from research.data.spark.pipelines.utils import map_parquet

logging.basicConfig(level="INFO")
spark = k8s_session(
    max_workers=100,
)

map_parquet.apply_pandas(
    spark,
    convert_files,
    "/mnt/efs/spark-data/shared/aug-stack/kaggle_notebooks/parquet/lang=ipynb/",
    "/mnt/efs/spark-data/shared/aug-stack/kaggle_notebooks/converted",
    ignore_error=True,
    task_info_location="/mnt/efs/spark-data/temp_weekly/map_parquet_task_logs/convert_ipynb/",
)

spark.stop()

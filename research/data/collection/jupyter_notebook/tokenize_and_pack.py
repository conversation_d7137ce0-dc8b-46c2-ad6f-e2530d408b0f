"""Tokenize and pack ipynb files."""

import argparse
import logging
import re
from functools import partial
from typing import Iterable

import numpy as np

from research.data.collection.utils.tokenize_with_masking import (
    MaskableText,
    tokenize_and_pack,
)
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.utils import k8s_session

logging.basicConfig(level=logging.INFO)


# What ratios of output would be unmasked.
OUTPUT_FIT_RATE = 0.2


def mask_ipynb(row: dict) -> Iterable[MaskableText]:
    """Mask out non-human generated content from ipynb files.

    This includes:
    - Cell separators (`# %%`)
    - Input and output indicator (`# In [9]:`, `# Out[1]:` etc)
    - Cell output (i.e. anything from # Out line to the next `# %%`) have a
    chance to be masked out.
    """
    current_block_masked = False
    current_block_lines = []
    in_output = False
    mask_output = False

    in_pattern = re.compile(r"# In ?\[[0-9]+\]:")
    out_pattern = re.compile(r"# Out ?\[[0-9]+\]:")

    for line in row["converted-script"].splitlines():
        line_masked = True
        if line == "# %%" or in_pattern.fullmatch(line):
            in_output = False
        elif out_pattern.fullmatch(line):
            # roll the dice to see if we want to mask this output
            mask_output = OUTPUT_FIT_RATE < np.random.rand()
            in_output = True
        else:
            line_masked = in_output and (
                mask_output
                or line == "# ..."
                or line.startswith("# <")
                and line.endswith(">")
            )

        if line_masked != current_block_masked:
            if current_block_lines:
                yield MaskableText(
                    "\n".join(current_block_lines) + "\n", current_block_masked
                )
            current_block_masked = line_masked
            current_block_lines = []
        current_block_lines.append(line)

    if current_block_lines and not current_block_masked:
        yield MaskableText("\n".join(current_block_lines) + "\n", current_block_masked)


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--tokenizer", type=str, default="deepseek_coder_base")
    parser.add_argument("--context-length", type=int, default=4096)
    parser.add_argument("--no-add-one-token", action="store_true", default=False)
    parser.add_argument("--batch-size", type=int, default=100)
    parser.add_argument("--timeout-seconds", type=int, default=60 * 60)
    parser.add_argument(
        "--input-path",
        type=str,
        default="/mnt/efs/spark-data/shared/aug-stack/ipynb/deduped",
    )
    parser.add_argument(
        "--output-path",
        type=str,
        default=None,
    )

    return parser.parse_args()


def main(args: argparse.Namespace):
    spark = k8s_session(
        max_workers=100,
        conf={
            "spark.executor.memory": "180G",
            "spark.executor.pyspark.memory": "150G",
        },
    )
    tokenize = partial(
        tokenize_and_pack,
        text_generator=mask_ipynb,
        seq_length=args.context_length,
        add_one_token=not args.no_add_one_token,
        tokenizer_name=args.tokenizer,
    )

    map_parquet.apply_pandas(
        spark,
        tokenize,
        args.input_path,
        args.output_path
        or f"/mnt/efs/spark-data/shared/aug-stack/ipynb/tokenized/{args.tokenizer}-{args.context_length}",
        batch_size=args.batch_size,
        timeout=args.timeout_seconds,
        task_info_location="/mnt/efs/spark-data/temp_weekly/map_parquet_task_logs/tokenize_and_pack/",
        ignore_error=True,
    )

    spark.stop()


if __name__ == "__main__":
    main(parse_args())

"""Job to generate the StackExchange dataset.

## Generate the Stack Exchange dataset.
This is the full multi-site StackExchange from archive.org.

A manual filtering step is applied to remove irrelevant sites.

We start from `stackexchange_qa` dataset and perform the following steps:

1. Sort the answers and format structured data into text documents
2. Tokenize
3. Pack equal-length samples into binary format.
4. Write the results as parquet files to s3.

Extensive masking is done to prevent the model from generating spoiler
plate information like `## Answer`, auther names or star counts.
"""

import argparse
import functools
import typing
from dataclasses import dataclass

from research.data.collection.utils.markdown_conversion import robust_conversion
from research.data.collection.utils.tokenize_with_masking import (
    MaskableText,
    tokenize_and_pack,
)
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.utils import k8s_session
from research.data.train.common.read_utils import get_single_source
from research.data.train.datasets.stackexchange import stackexchange_tokenized


@dataclass
class StackExchangeFormatOptions:
    """Options for formatting the StackExchange dataset."""

    show_tags: bool = True
    """Whether to show tags in the text document."""

    show_question_favorites: bool = True
    """Whether to show the number of favorites for the question."""

    show_owners: bool = True
    """Whether to show the author of the question and answers.
    Note that answers and comments contain names already!
    """

    show_reputation: bool = False
    """Whether to show the reputation of the author of the question."""

    show_date_and_views: bool = True
    """Whether to show the date and number of views of the question and answers."""

    anonymize_users: bool = False
    """Whether to anonymize the names of the authors of the question and answers.
    It will show UserXXXX.  We will also do a best effort anonymization of the
    names in the comments and answers.
    """

    show_question_comments: bool = True
    """Whether to show comments for the questions."""

    show_answer_comments: bool = True
    """Whether to show comments for the answers."""

    max_answers: int = 15
    """Maximum number of answers to show for a question."""

    max_comments: int = 6
    """Maximum number of comments to show for a question or answer."""

    show_negative_questions: bool = True
    """Whether to show questions with negative scores."""

    show_negative_answers: bool = False
    """Whether to show answers with negative scores."""

    show_negative_comments: bool = False
    """Whether to show comments with negative scores."""

    show_no_score_comments: bool = True
    """Whether to show comments with no score."""

    show_question_scores: bool = True
    """Whether to show the score of the question."""

    show_answer_scores: bool = True
    """Whether to show the score of the answer."""

    mark_accepted_answers: bool = True
    """Whether to mark accepted answers with a special marker."""

    conversion_timeout_ms: int = 1000
    """Timeout for conversion of a single question in milliseconds."""

    show_questions_without_answers: bool = False
    """Whether to show questions without answers or comments."""

    def __post_init__(self):
        if self.anonymize_users:
            raise NotImplementedError("Anonymization not implemented yet.")
        if self.show_reputation:
            raise NotImplementedError("Reputation not implemented yet.")
        if not self.show_owners:
            raise NotImplementedError(
                "Disabling displaying owners not implemented yet."
            )
        if not self.show_negative_questions:
            raise NotImplementedError(
                "Filtering negative questions not implemented yet."
            )


def format_comments(
    comments: list[dict[str, typing.Any]], options: StackExchangeFormatOptions
) -> list[MaskableText]:
    """Format comments from StackExchange as a list of strings.

    Comments are not sorted by score! Instead they preserve their time order.

    When number of comment is bigger than max_comments option, we would first
    trim comments based on scores.
    """
    results: list[tuple[float, int, list[MaskableText]]] = []
    """Entires are: (score, index, comment)"""
    for comment in comments:
        if comment["text"] is None:
            continue
        score = comment["score"]
        if not score and not options.show_no_score_comments:
            continue
        if score and score < 0 and not options.show_negative_comments:
            continue
        if score:
            score_tag = f"[⭐ {score:+}]"
        else:
            score_tag = ""
        results.append(
            (
                score or 0,
                len(results),
                [
                    MaskableText(f"- {score_tag} ", True),
                    MaskableText(comment["text"], False),
                    MaskableText(f" - {comment['owner']['display_name']}\n", True),
                ],
            )
        )
    if options.max_comments and len(results) > options.max_comments:
        results.sort(reverse=True)
        results = results[: options.max_comments]
        results.sort(key=lambda x: x[1])

    output: list[MaskableText] = []
    for _, _, items in results:
        output.extend(items)
    return output


def format_answers(
    answers: list[dict[str, typing.Any]], options: StackExchangeFormatOptions
):
    """Format a list of answers as a text document."""
    parts: list[MaskableText] = []
    answers_added = 0
    for i, answer in enumerate(
        sorted(answers, key=lambda x: (x["is_accepted"], x["score"]), reverse=True)
    ):
        if answers_added == options.max_answers:
            break
        if (
            not answer["body"]
            or answer["score"]
            and answer["score"] < 0
            and not options.show_negative_answers
        ):
            continue
        answers_added += 1
        body = robust_conversion(
            answer["body"], timeout_ms=options.conversion_timeout_ms
        )
        if answer["score"]:
            score_tag = f"[⭐ {answer['score']:+}]"
        else:
            score_tag = ""
        headline = f"\n### {score_tag} Answer {i+1}"
        if answer["is_accepted"]:
            # show a checkmark for accepted answers
            headline += " ( ✔ Accepted )"
        headline += f" | by {answer['owner']['display_name']}"
        if options.show_date_and_views:
            headline += f" on: {answer['created_at'][:10]}"
        headline += "\n"
        parts.append(MaskableText(headline, True))
        parts.append(MaskableText(body, False))
        # Add comments if needed
        if options.show_answer_comments:
            comments = format_comments(answer["comments"], options)
            if comments:
                parts.append(MaskableText("\n#### Comments:\n\n", True))
                parts.extend(comments)
    return parts


def format_question(
    question: dict[str, typing.Any], options: StackExchangeFormatOptions | None = None
):
    """Format each question from stackoverflow as a partially masked text document.

    Starting with the question as the title.

    The return is a list of DataEntry objects indicating if that particular text entry
    is masked or not.
    """
    options = options or StackExchangeFormatOptions()

    # Skip questions with no answers and comments if option is set.
    if not options.show_questions_without_answers and len(question["answers"]) == 0:
        return
    if options.show_date_and_views:
        date_line = f"*Asked on:* {question['created_at'][:10]} ({question['view_count']} views)"
    else:
        date_line = ""

    yield MaskableText(f"\n# {question['title']}\n\n{date_line}\n\n## Question:", True)

    if len(question["tags"]) > 0 and options.show_tags:
        tags_line = "*Tags:* " + ", ".join(map("`{}`".format, question["tags"])) + "\n"
    else:
        tags_line = ""
    question_body = robust_conversion(
        question["question_body"], timeout_ms=options.conversion_timeout_ms
    )
    yield MaskableText(question_body + "\n\n", False)

    score_line = (
        f"Score: [⭐ {question['score']:+}] | by: {question['owner']['display_name']}"
    )
    yield MaskableText(f"{tags_line}\n\n{score_line}\n\n", True)

    if options.show_question_comments:
        comments = format_comments(question["comments"], options)
        if comments:
            yield MaskableText(f"\n## {len(comments)} Comments:\n", True)
            yield from comments

    answers = format_answers(question["answers"], options=options)
    if answers:
        yield MaskableText(f"\n## {len(answers)} Answers sorted by score:\n", True)
        yield from answers
    else:
        yield MaskableText("\n## No answers\n", True)


def main(args: argparse.Namespace):
    spark = k8s_session(
        max_workers=50,
        conf={
            "spark.executor.memory": "120G",
            # make sure if we oom the task is killed so that the driver doesn't
            # die from lost connection with executors.
            "spark.executor.pyspark.memory": "100G",
        },
    )

    process = functools.partial(
        tokenize_and_pack,
        text_generator=format_question,
        tokenizer_name=args.tokenizer,
        seq_length=args.context_length,
        add_one_token=not args.no_add_one_token,
        output_column=args.output_column,
        max_tail_pad=args.max_trail_pad,
    )

    print("Formatting text docs")
    map_parquet.apply_pandas(
        spark,
        process,
        get_single_source(stackexchange_tokenized).location,
        stackexchange_tokenized.location,
        batch_size=args.batch_size,
        timeout=args.timeout_seconds,
        task_info_location=args.task_info_location,
    )
    spark.stop()


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--tokenizer", type=str, default="deepseek_coder_base")
    parser.add_argument("--context-length", type=int, default=4096)
    parser.add_argument("--no-add-one-token", action="store_true", default=False)
    parser.add_argument("--output-column", type=str, default="packed_samples")
    parser.add_argument("--batch-size", type=int, default=100)
    parser.add_argument("--timeout-seconds", type=int, default=60 * 60)
    parser.add_argument(
        "--task-info-location",
        type=str,
        default="/mnt/efs/spark-data/temp_weekly/tokenize_and_pack_task_logs/",
    )
    parser.add_argument("--max-trail-pad", type=int, default=410)

    return parser.parse_args()


if __name__ == "__main__":
    main(parse_args())

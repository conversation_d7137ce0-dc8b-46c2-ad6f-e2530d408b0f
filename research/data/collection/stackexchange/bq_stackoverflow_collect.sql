--- BigQuery query to collect StackOverflow data into structured format
--- Joins several tables to produce a table in QA format.
--- Each row is a question, with an array of answers objects and other data.


EXPORT DATA
OPTIONS(
  uri='gs://bigquery-data-staging/stackoverflow/denormalized_qa/data-part-*.parquet',
  format='PARQUET',
  overwrite=true,
  compression='zstd'
) AS
WITH answers AS (
  SELECT
    a.id,
    a.parent_id AS question_id,
    a.body AS body,
    FORMAT_TIMESTAMP("%Y-%m-%d %H:%M:%E*S", a.creation_date) AS created_at,
    FORMAT_TIMESTAMP("%Y-%m-%d %H:%M:%E*S", a.last_activity_date) AS last_activity_at,
    a.score,
    STRUCT(user.id, user.display_name, user.reputation, user.up_votes, user.down_votes) AS owner,
    ARRAY_AGG(
      STRUCT(c.score, c.text,
        STRUCT(u_c.id, u_c.display_name, u_c.reputation, u_c.up_votes, u_c.down_votes) AS owner,
        FORMAT_TIMESTAMP("%Y-%m-%d %H:%M:%E*S", c.creation_date) AS created_at
      )
      ORDER BY c.creation_date
    ) AS comments
  FROM `bigquery-public-data.stackoverflow.posts_answers` AS a
  JOIN `bigquery-public-data.stackoverflow.users` AS user
     ON user.id = a.owner_user_id
  LEFT JOIN `bigquery-public-data.stackoverflow.comments` AS c
     ON c.post_id = a.id
  LEFT JOIN `bigquery-public-data.stackoverflow.users` AS u_c
     ON u_c.id = c.user_id
  GROUP BY 1,2,3,4,5,6, user.id, user.display_name, user.reputation, user.up_votes, user.down_votes
),
questions AS (
  SELECT
    q.id,
    q.title,
    q.body,
    q.favorite_count,
    FORMAT_TIMESTAMP("%Y-%m-%d %H:%M:%E*S", q.creation_date) AS created_at,
    FORMAT_TIMESTAMP("%Y-%m-%d %H:%M:%E*S", q.last_activity_date) AS last_activity_at,
    q.view_count,
    q.score,
    q.accepted_answer_id,
    SPLIT(q.tags, '|') AS tags,
    STRUCT(user.id, user.display_name, user.reputation, user.up_votes, user.down_votes) AS owner,
    ARRAY_AGG(
      STRUCT(
        c.score,
        c.text,
        STRUCT(u_c.id, u_c.display_name, u_c.reputation, u_c.up_votes, u_c.down_votes) AS owner,
        FORMAT_TIMESTAMP("%Y-%m-%d %H:%M:%E*S", c.creation_date) AS created_at
      )
      ORDER BY c.creation_date
    ) AS comments
  FROM `bigquery-public-data.stackoverflow.posts_questions` AS q
  JOIN `bigquery-public-data.stackoverflow.users` AS user
     ON user.id = q.owner_user_id
  LEFT JOIN `bigquery-public-data.stackoverflow.comments` AS c
     ON c.post_id = q.id
  LEFT JOIN `bigquery-public-data.stackoverflow.users` AS u_c
     ON u_c.id = c.user_id
  GROUP BY 1,2,3,4,5,6,7,8,9, q.tags, user.id, user.display_name, user.reputation, user.up_votes, user.down_votes
),
question_answers AS (
  SELECT
    q.id AS question_id,
    ARRAY_AGG(
      STRUCT(
        a.id,
        a.body,
        a.created_at,
        a.last_activity_at,
        a.score,
        a.owner,
        a.comments,
        (a.id = q.accepted_answer_id) AS is_accepted
      )
      ORDER BY a.score DESC
    ) AS answers
  FROM `bigquery-public-data.stackoverflow.posts_questions` AS q
  JOIN answers AS a
     on a.question_id = q.id
  GROUP BY 1
)
SELECT
q.id AS question_id,
q.title,
q.body AS question_body,
q.favorite_count,
q.created_at,
q.last_activity_at,
q.view_count,
q.score,
q.accepted_answer_id,
q.tags,
q.owner,
q.comments,
qa.answers

FROM questions AS q
LEFT JOIN question_answers AS qa
     on qa.question_id = q.id;


CREATE OR REPLACE EXTERNAL TABLE `augment-387916.raw_datasets.stackoverflow`
OPTIONS (
  format = 'PARQUET',
  uris = ['gs://bigquery-data-staging/stackoverflow/denormalized_qa/data-part-*.parquet']
);

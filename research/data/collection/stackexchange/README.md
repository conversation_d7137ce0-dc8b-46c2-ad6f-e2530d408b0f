## Download and processing the stackexchange dataset

We have two difference sources.  One simple source is BigQuery.  This
source is publicly available and can directly be joined and queried on BigQuery.
Despite its convenience, it is stale and only contains one site (StackOverflow).

The second source is archive.org, which has regular updates of the most recent full
StackExchange dump.  However processing is a bit more complex as we have to download
individual dumps, then uncompress and process the XML files into separate tables first
before we can use them in data pipelines.

For both datasets, each site (for example, stackoverflow.com or superuser.com) has the
following set of tables:

- Posts
- PostHistory
- PostLinks
- Tags
- Users
- Votes
- Comments
- Badges

The schemas are also identical between the sources.

### Download the BigQuery dataset

BigQuery has each of the above tables in its public dataset `bigquery-public-data.stackoverflow`.

To process the data we use a SQL script `bq_stackoverflow_collect.sql` which joins the tables into a single table.
The results are saved as parquet files in the GCS bucket `gs://bigquery-data-staging/stackoverflow/denormalized_qa`.


### Download the StackExchange dataset

The following script downloads the most recent dump of the *full* Stack Exchange data from archive.org
Content updates constantly; please check https://archive.org/details/stackexchange for details

The download is performed with the script `download_from_archive_org.sh`, which also uncompresses the files.

The full list of available sites is in `sites` file (not including stackoverflow.com, which is separately stored upstream).
We performed hand curation and excluded a list of sites in `excluded_sites`.
The rest of the sites are in `included_sites`, which is used to construct our dataset.

We also compile the list of language communities, but since we do want to improve the model's language abilities these
are not excluded from the dataset.

The uncompressed XML folders are converted to JSON with this [repository](https://github.com/makersacademy/stackexchange-xml-converter).
This is done site by site, except for stackoverflow, which is done table by table.

We keep all non-meta sites from stackexchange; for a full list of sites see `sites.txt` and the
conversion script is in `convert_xml_to_json.sh`.

### Creating the tokenized dataset

Note that this does not contain the formatting and tokenization.  All contents here are used
to produce raw relational tables.  The tokenization is done in the Spark pipeline in
`research.data.train.jobs`

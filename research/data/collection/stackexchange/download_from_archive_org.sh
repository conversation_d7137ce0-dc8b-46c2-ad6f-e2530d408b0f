# Download the most recent dump of the *full* Stack Exchange data from archive.org
# Content updates constantly; please check https://archive.org/details/stackexchange for details
# This will create two directories in the current path:
# - raw: contains the downloaded 7z compressed files, with filenames like `iot.stackexchange.com.7z`
# - uncompressed: contains the uncompressed files, with directory names like `iot.stackexchange.com`
# Each uncompressed directories would have a set of XML files each correpsonding to a table for that particular community

# Create the directories.  Error out if they already exist
# NOTE THIS NEEDS between 500 and 600GB disk space.

echo "======= CREATE DIRECTORIES ======="
for dirname in raw uncompressed; do
    if [ -d "$dirname" ]; then
        echo "Error: $dirname already exists"
        exit 1
    else
        mkdir "$dirname"
    fi
done

echo "======= DOWNLOAD FILES ======="
echo "Downloading the most recent dump of the *full* Stack Exchange data from archive.org"
# Download the files into raw
wget -r -l1 -H -t1 -nd -N -np -A.7z -erobots=off -P raw https://archive.org/download/stackexchange


echo "======= UNCOMPRESS FILES ======="
# Uncompress the files into uncompressed
for filename in raw/*.7z; do
    echo "Uncompressing $filename"
    # Extract the filename without the path
    filename_no_path=$(basename "$filename")
    # Extract the filename without the extension
    filename_no_ext="${filename_no_path%.*}"
    # Uncompress file into uncompressed/filename_no_ext
    7z x "$filename" -ouncompressed/"$filename_no_ext"
done

--- <PERSON><PERSON><PERSON>y query to collect StackExchange data into structured format
--- Joins several tables to produce a table in QA format.
--- Each row is a question, with an array of answers objects and other data.

CREATE OR REPLACE TABLE `augment-387916.raw_datasets.stackexchange` AS

WITH answers AS (
  SELECT
    a.site,
    a.id,
    a.ParentId AS question_id,
    a.body AS body,
    a.CreationDate AS created_at,
    a.LastActivityDate AS last_activity_at,
    CAST(a.score AS INT64) AS score,
    STRUCT(
      user.id,
      user.DisplayName as display_name,
      user.reputation,
      user.UpVotes as up_votes,
      user.DownVotes as down_votes
    ) AS owner,
    ARRAY_AGG(
      STRUCT(
        CAST(c.score AS INT64) AS score,
        c.text,
        STRUCT(
          u_c.id,
          u_c.DisplayName as display_name,
          u_c.reputation,
          u_c.UpVotes as up_votes,
          u_c.DownVotes as down_votes
        ) AS owner,
        c.CreationDate AS created_at
      )
      ORDER BY c.CreationDate
    ) AS comments
  FROM `augment-387916.stackexchange.answers` AS a
  JOIN `augment-387916.stackexchange.users` AS user
     ON user.id = a.OwnerUserId AND a.site=user.site
  LEFT JOIN `augment-387916.stackexchange.comments` AS c
     ON c.PostId = a.id AND a.site = c.site
  LEFT JOIN `augment-387916.stackexchange.users` AS u_c
     ON u_c.id = c.UserId AND u_c.site = c.site
  GROUP BY 1,2,3,4,5,6, 7, user.id, user.DisplayName, user.reputation, user.UpVotes, user.DownVotes
),
questions AS (
  SELECT
    q.site,
    q.id,
    q.title,
    q.body,
    CAST(q.FavoriteCount AS INT64) AS favorite_count,
    q.CreationDate AS created_at,
    q.LastActivityDate AS last_activity_at,
    CAST(q.ViewCount AS INT64) as view_count,
    CAST(q.score AS INT64) AS score,
    q.AcceptedAnswerId as accepted_answer_id,
    q.tags,
    STRUCT(
      user.id,
      user.DisplayName as display_name,
      user.reputation,
      CAST(user.UpVotes AS INT64) as up_votes,
      CAST(user.DownVotes AS INT64) as down_votes
    ) AS owner,
    ARRAY_AGG(
      STRUCT(
        CAST(c.score AS INT64) AS score,
        c.text,
        STRUCT(
          u_c.id,
          u_c.DisplayName as display_name,
          CAST(u_c.reputation AS INT64) AS reputation,
          CAST(u_c.UpVotes AS INT64) as up_votes,
          CAST(u_c.DownVotes AS INT64) as down_votes
        ) AS owner,
        c.CreationDate AS created_at
      )
      ORDER BY c.CreationDate
    ) AS comments
  FROM `augment-387916.stackexchange.questions` AS q
  JOIN `augment-387916.stackexchange.users` AS user
     ON user.id = q.OwnerUserId AND user.site=q.site
  LEFT JOIN `augment-387916.stackexchange.comments` AS c
     ON c.PostId = q.id AND c.site=q.site
  LEFT JOIN `augment-387916.stackexchange.users` AS u_c
     ON u_c.id = c.UserId AND u_c.site=c.site
  GROUP BY 1,2,3,4,5,6,7,8,9, 10, 11, user.id, user.DisplayName, user.reputation, user.UpVotes, user.DownVotes
),
question_answers AS (
  SELECT
    q.site,
    q.id AS question_id,
    ARRAY_AGG(
      STRUCT(
        a.id,
        a.body,
        a.created_at,
        a.last_activity_at,
        a.score,
        a.owner,
        a.comments,
        (a.id = q.AcceptedAnswerId) AS is_accepted
      )
      ORDER BY a.score DESC
    ) AS answers
  FROM `augment-387916.stackexchange.questions` AS q
  JOIN answers AS a
     on a.question_id = q.id AND a.site=q.site
  GROUP BY 1, 2
)
SELECT
q.id AS question_id,
q.title,
q.body AS question_body,
q.favorite_count,
q.created_at,
q.last_activity_at,
q.view_count,
q.score,
q.accepted_answer_id,
SPLIT(REGEXP_REPLACE(REGEXP_REPLACE(q.tags, r'><', ','), r'^<|>$', ''), ',') AS tags,
q.owner,
q.comments,
qa.answers,
q.site

FROM questions AS q
LEFT JOIN question_answers AS qa
     on qa.question_id = q.id AND qa.site = q.site;



EXPORT DATA
OPTIONS(
  uri='gs://bigquery-data-staging/stackexchange/denormalized_qa/data-part-*.parquet',
  format='PARQUET',
  overwrite=true,
  compression='zstd'
) AS

SELECT * FROM `augment-387916.raw_datasets.stackexchange`;

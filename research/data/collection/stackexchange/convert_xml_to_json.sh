#!/bin/bash

# Script to convert stackexchange xml files to json files

for x in `cat included_sites`
do
  echo ===== Processing $x   =====
  mkdir converted/$x
  stackexchange-xml-converter/stackexchange-xml-converter -result-format=json -source-path=uncompressed/$x  -store-to-dir=converted/$x
  # Remove empty files.  its a bug in stackexchange-xml-converter
  find converted/$x -type f -size 0 -delete
  cd converted/$x/
  gzip *.json
  cd ../../
done

# Download the stackoverflow dataset from bigquery

# Create a temporary directory and download the files into it
# Once we setup the GCP connectors in spark we should switch to use spark

SOURCE=gs://bigquery-data-staging/stackoverflow/denormalized_qa
DESTINATION=s3://nl-datasets/processed/stackoverflow_qa/

TMP_DIR=$(mktemp -d)
cd $TMP_DIR

# Download the files from bigquery
gsutil -m cp $SOURCE/* .

# Copy the files into the correct location
s5cmd rm --recursive $DESTINATION
s5cmd put data-part-*.parquet $DESTINATION

# Clean up
cd ..
rm -rf $TMP_DIR

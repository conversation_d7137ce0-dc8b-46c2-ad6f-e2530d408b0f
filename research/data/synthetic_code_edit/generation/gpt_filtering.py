"""Filtering script for synthetic data.

<PERSON><PERSON>t sorts input samples into 3 directories:
- good: samples that passed filtering
- bad: samples that didn't pass filtering
- failed: samples that failed to be filtered
"""

import argparse
import json
from multiprocessing import Pool
from pathlib import Path

from tqdm import tqdm

from research.data.synthetic_code_edit.api_lib import <PERSON><PERSON><PERSON><PERSON><PERSON>
from research.data.synthetic_code_edit.util_lib import get_unified_diff, render_list

FILTERING_PROMPT = """Here is the original source code:
```
{selected_code}
```

Here is updated code obtained by applying instruction ('{instruction}') to the original source code:
```
{updated_code}
```

And here is the diff between original source code and updated code:
```
{diff}
```

Please evaluate whether this updated code meets these criterias:
{criterias}

If the updated code doesn't meet at least one of these criterias, mark this evaluation as failed.

Follow this steps to complete this task:
1. Analyze the original code, updated code, diff and criterias.
2. Describe your thinking process on how to complete the evaluation.
3. Explicitly write whether updated code passed evaluation or not.
"""


FILTERING_EXTRACT_PROMPT = """Return results of evaluation as a JSON with 2 keys:
- success
- feedback

If updated code passed evaluation, then success must be True and feedback empty.
If updated code didn't pass evaluation, then success must be False and feedback should contain why code didn't pass evaluation.
"""

FILTERING_CRITERIAS = [
    "Indentations and comments of the original source code should not be changed, unless changing them is required by instruction",
    "Updated code must contain all modifications required by the instruction",
    "Updated code must NOT contain any modifications not required by the instruction",
    "Updated code must NOT contain any comments related to modifications made, unless it is required by instruction",
]

FilteringArgType = tuple[dict, GptWrapper, Path, str]


def filter_sample(filter_args: FilteringArgType):
    sample, gpt, result_dir, sample_idx = filter_args

    for _dir in ["good", "bad", "failed"]:
        if (result_dir / _dir / f"{sample_idx}.json").exists():
            return

    diff = get_unified_diff(sample["old_middle"], sample["new_middle"])

    prompt = FILTERING_PROMPT.format(
        selected_code=sample["old_middle"],
        updated_code=sample["new_middle"],
        instruction=sample["instruction"],
        criterias=render_list(FILTERING_CRITERIAS),
        diff=diff,
    )

    try:
        messages = [{"role": "user", "content": prompt}]
        gpt4_response = gpt(messages, model="gpt-4-1106-preview", temperature=0)

        messages.append({"role": "assistant", "content": gpt4_response})
        messages.append({"role": "user", "content": FILTERING_EXTRACT_PROMPT})

        filtering_result = gpt(
            messages, model="gpt-3.5-turbo-1106", use_json=True, temperature=0
        )
    except KeyboardInterrupt:  # pylint: disable=W0706
        raise
    except Exception as e:  # pylint: disable=W0718
        print(f"Sample {sample_idx} failed with exception: {e}")
        filtering_result = dict()

    if set(filtering_result.keys()) != {"success", "feedback"}:
        target_dir = "failed"
    elif filtering_result["success"] is True:
        target_dir = "good"
    elif filtering_result["success"] is False:
        target_dir = "bad"
    else:
        target_dir = "failed"

    sample["filtering_result"] = filtering_result

    with (result_dir / target_dir / f"{sample_idx}.json").open("w") as f:
        json.dump(sample, f, indent=2)


def main(
    input_json: Path,
    output_dir: Path,
    num_processes: int = 1,
    num_samples: int = int(1e12),
):
    with input_json.open("r") as f:
        data_samples = json.load(f)
    print(f"Loaded {len(data_samples)} samples.")

    output_dir.mkdir(exist_ok=True)
    for dir_name in ["good", "bad", "failed"]:
        (output_dir / dir_name).mkdir(exist_ok=True)

    gpt = GptWrapper()
    tasks = []
    for i, sample in enumerate(data_samples[:num_samples]):
        tasks.append((sample, gpt, output_dir, i))

    if num_processes <= 1:
        for task in tqdm(tasks):
            filter_sample(task)
    else:
        with Pool(num_processes) as pool:
            _ = list(tqdm(pool.imap(filter_sample, tasks), total=len(tasks)))

    print(f"GPT usage:\n{gpt.get_stats()}")


def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script for automatically filtering synthetic data.",
    )
    parser.add_argument(
        "--input_json",
        "-i",
        type=Path,
        required=True,
        help="Path to input json with synthetic data.",
    )
    parser.add_argument(
        "--output_dir",
        "-o",
        type=Path,
        required=True,
        help="Output directory to store results",
    )
    parser.add_argument(
        "--num_processes",
        "-np",
        type=int,
        default=1,
        help="Number of parallel processes",
    )
    parser.add_argument(
        "--num_samples",
        "-n",
        type=int,
        default=int(1e12),
        help="Number of samples to filter",
    )
    args = parser.parse_args()

    return args


if __name__ == "__main__":
    cli_args = parse_args()
    main(
        cli_args.input_json,
        cli_args.output_dir,
        cli_args.num_processes,
        cli_args.num_samples,
    )

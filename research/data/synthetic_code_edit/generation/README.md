# Synthetic data generation for editing

Disclaimer: This is a work in progress. The code might evolve rapidly.

## Examples:

- ```python  synthetic_generation.py -i /mnt/efs/augment/user/yuri/data/1k-in-codeedit.jsonl.zst -o ./output_dir -n 10 -p v1 -np 10 --pipeline_kwargs seed_instructions_file_path=/home/<USER>/repos/augment/research/data/synthetic_code_edit/seeds/generic_instructions.json```

- ```python  synthetic_generation.py -i /mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03/raw-edit-scopes.jsonl.zst --use_edit_scopes -o ./output_dir -n 10 -p v1 -np 10 --pipeline_kwargs seed_instructions_file_path=/home/<USER>/repos/augment/research/data/synthetic_code_edit/seeds/generic_instructions.json```

### Manual filtering tool

For example format of input data, check this directory: `/mnt/efs/augment/user/yuri/data/synthetic_data_dec_21/all`.

Samples will be displayed one by one, and you will be asked whether to move it to the 'good' or 'bad' directory.

- ```python filtering_tool.py -i ./input_dir -o ./output_dir```

### Automatic (GPT4-based) filtering tool

For example format of input data, check this directory: `/mnt/efs/augment/user/igor/data/droid/synth/mix1.2024-01-09_15-28-44`.

Samples will be sorted into 3 directories:
- good: samples that passed filtering
- bad: samples that didn't pass filtering
- failed: samples that failed to be filtered

- ```python gpt_filtering.py -i ~/tmp/mix1.2024-01-09_15-28-44 -o /tmp/filtering_result -np 5 -n 5```

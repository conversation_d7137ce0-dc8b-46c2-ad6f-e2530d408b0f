{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Explore the sampled updated_code for code edit"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import collections\n", "from research.core import constants\n", "from research.data.synthetic_code_edit import sampling_lib"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["file_path = (\n", "    constants.AUGMENT_ROOT\n", "    / \"research\"\n", "    / \"data\"\n", "    / \"synthetic_code_edit\"\n", "    / \"seeds\"\n", "    / \"Code-Formatting\"\n", "    / \"001.txt\"\n", ")\n", "content = file_path.read_text()\n", "lranges = sampling_lib.find_lrange_via_consecutive_sibling_nodes(\n", "    content,\n", "    max_consecutive_nodes=8,\n", "    max_lines=40,\n", ")\n", "lines = content.splitlines(keepends=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["counts = collections.defaultdict(lambda: 0)\n", "for x in lranges:\n", "    counts[x.stop - x.start] += 1\n", "keys = sorted(counts.keys())\n", "for key in keys:\n", "    print(f\"The amount of data with #lines = {key} is {counts[key]}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = random.randint(0, len(lranges) - 1)\n", "lrange = lranges[index]\n", "print(\"\".join(lines[lrange.start : lrange.stop]))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
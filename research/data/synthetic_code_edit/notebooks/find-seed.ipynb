{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to find new seed data and contribute to code edit"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pyspark.sql.functions as F\n", "from pyspark.sql.functions import rand\n", "from research.data.spark import k8s_session\n", "\n", "spark = k8s_session(\n", "    max_workers=100,\n", "    conf={\n", "        \"spark.sql.parquet.columnarReaderBatchSize\": \"128\",\n", "        \"spark.sql.execution.arrow.maxRecordsPerBatch\": \"128\",\n", "        \"spark.task.maxFailures\": \"10\",\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["files_df = spark.read.parquet(\n", "    \"s3a://augment-github/starcoder_format/sorted_by_id_2023-09-30/\"\n", ")\n", "py_files_df = (\n", "    files_df.select(\n", "        F.col(\"max_stars_repo_name\").alias(\"repo\"),\n", "        F.col(\"max_stars_repo_path\").alias(\"file_path\"),\n", "        F.col(\"id\").alias(\"file_sha\"),\n", "        <PERSON><PERSON>col(\"lang\"),\n", "        <PERSON><PERSON>col(\"content\"),\n", "        <PERSON>.col(\"repo_size\"),\n", "    )\n", "    .filter(F.col(\"file_path\").endswith(\".py\"))\n", "    .filter(F.length(\"content\") < 1e6)\n", "    .withColumn(\"num_lines\", F.size(F.split(F.col(\"content\"), \"\\n\")))\n", ")\n", "py_files_df = py_files_df.filter(F.col(\"num_lines\") <= 600)\n", "print(f\"There are {py_files_df.count()} files.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sampled_py_files_df = py_files_df.orderBy(rand()).limit(100)\n", "sampled_pandas_df = sampled_py_files_df.toPandas()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 2\n", "\n", "repo = sampled_pandas_df.iloc[index].repo\n", "commit, path = sampled_pandas_df.iloc[index].file_path.split(\":\", 1)\n", "content = sampled_pandas_df.iloc[index].content\n", "repo_url = f\"https://github.com/{repo}/commit/{commit}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sample code for finding the seed for a given file.\n", "import random\n", "from research.data.synthetic_code_edit.util_lib import (\n", "    get_pretty_str_in_git_merge_style,\n", "    get_pretty_str_in_diff_style\n", ")\n", "from research.data.synthetic_code_edit.sampling_lib import (\n", "    find_lrange_via_consecutive_sibling_nodes,\n", ")\n", "\n", "lranges = find_lrange_via_consecutive_sibling_nodes(\n", "    content,\n", "    max_consecutive_nodes=8,\n", "    max_lines=40,\n", ")\n", "lranges_bigger_than_10lines = [\n", "    lrange for lrange in lranges if lrange.stop - lrange.start > 10\n", "]\n", "print(f\"There are {len(lranges_bigger_than_10lines)} lranges.\")\n", "lrange = random.choice(lranges_bigger_than_10lines)\n", "lines = content.splitlines(keepends=True)\n", "view = get_pretty_str_in_git_merge_style(\n", "    prefix=\"\".join(lines[ : lrange.start]),\n", "    selected_code=\"\",\n", "    suffix=\"\".join(lines[lrange.stop :]),\n", "    updated_code=\"\".join(lines[lrange.start : lrange.stop]),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(view)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# An example of the expected json file for annotation\n", "{\n", "    \"repo_url\": repo_url,\n", "    \"path\": path,\n", "    \"instructions\": [],\n", "    \"category\": \"Formatting-Cleaning\",\n", "    \"language\": \"python\",\n", "    \"inverse_instructions\": [],\n", "}"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
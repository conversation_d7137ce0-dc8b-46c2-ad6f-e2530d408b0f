# Synthetic Data Generation

See details in <https://www.notion.so/Synthetic-Code-Edit-Data-Generation-d0cb2429a2b04dcf800030b0c1fbfa09>.

## Code Structure

The code is structured as a Python package.

- `research/data/synthetic_code_edit/types.py` defines the data types.
- `research/data/synthetic_code_edit/seed_lib.py` defines the functions to load the seeds.
- `research/data/synthetic_code_edit/util_lib.py` defines the utility functions.
- `research/data/synthetic_code_edit/sampling_lib.py` defines the sampling functions.
- `research/data/synthetic_code_edit/api_lib.py` defines the API wrappers for OpenAI with price counting feature.

## Generated Data

### V0

Raw data is at `/mnt/efs/augment/user/igor/nlp/2023-12-21_02-03-25/tokenized.json`.
The converted IndexedDataset is at `/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/`.

## Instructions on Fine-tuning

First of all, get out of the container.

```bash
cd research/fastbackward/multinode-scripts

# Allocate 4 nodes with 8 H100 GPUs for fine-tuning.
./multinode_launch.sh H100 edit-train 4

# An example script of fine-tuning.
./multinode_torchrun.sh edit-train 4 /mnt/efs/augment/user/dxy/src/augment "train.py configs/deepseek_base_33b.py --learning_rate=2e-5 --max_iters=0 --max_epochs=1 --decay_lr=False --warmup_iters=0 --weight_decay=1.0 --batch_size=2 --gradient_accumulation_steps=1 --eval_interval=10 --eval_iters=128 --block_size=4096 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-33b-base/ --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/full-S4096-DSCI --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/valid-S4096-DSCI --out_dir=/mnt/efs/augment/user/dxy/logs/edit.12.21/ --wandb_log=True --wandb_project=edit-exps-v2 --wandb_group=DXY-V3 --wandb_run_name=DSC-B-ALL-S4K_WUP0_110-WD1_0-b4x8x2x1-lr_2e-5_constant"
```

## Instructions on Launching the Flask Server

```bash
python experimental/dxy/edits/notebooks/random/launch_ft.py --port 5005 --edit_ckp "/mnt/efs/augment/user/dxy/logs/edit.12.21/DSC-B-ALL-S4K_WUP0_110-WD1_0-b4x8x2x1-lr_2e-5_constant/checkpoint_llama_iteration_120"
```

Remember to set `linesUsedInPrefixSuffix` as a large number, e.g.,:

```json
"codeEdit": {
    "url": "http://10.144.17.26:5005",
    "topP": 0.9,
    "temperature": 0.0,
    "linesUsedInPrefixSuffix": 1000,
}
```

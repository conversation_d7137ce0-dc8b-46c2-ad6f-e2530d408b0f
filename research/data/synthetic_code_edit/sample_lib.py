"""Sample data for the code edit."""

import dataclasses
import random
import typing

import tqdm
import tree_sitter as ts

from base.ranges.range_types import LineRange
from base.static_analysis.usage_analysis import ParsedFile
from research.core import utils_for_str
from research.core.types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from research.eval.dataset_generation_lib.finegrained_patch_generators import (
    find_func_head_for_api_call,
    find_max_self_contained_code_snippet_for_simple_api_call,
)
from research.static_analysis.common import iter_ts_nodes_via_bfs, tsnode_to_crange


@dataclasses.dataclass
class SimpleRawEditScope:
    """The simple raw edit scope."""

    repo: str
    """The repository name."""

    file_name: str
    """The file name."""

    content: str
    """The file content."""

    scope: LineRange
    """The line range of the scope."""

    middle_is_selected_code: bool = True

    misc: dict[str, typing.Any] = dataclasses.field(default_factory=dict)

    @property
    def lines(self) -> typing.List[str]:
        return self.content.splitlines(keepends=True)

    @property
    def num_lines(self) -> int:
        return len(self.lines)

    @property
    def prefix(self):
        start, stop = self.scope.start, self.scope.stop
        lines = self.lines
        assert (
            0 <= start <= stop <= len(lines)
        ), f"Expect 0 <= {start} <= {stop} <= {len(lines)}"
        return "".join(lines[:start])

    @property
    def suffix(self):
        start, stop = self.scope.start, self.scope.stop
        lines = self.lines
        assert (
            0 <= start <= stop <= len(lines)
        ), f"Expect 0 <= {start} <= {stop} <= {len(lines)}"
        return "".join(lines[stop:])

    @property
    def middle(self):
        start, stop = self.scope.start, self.scope.stop
        lines = self.lines
        assert (
            0 <= start <= stop <= len(lines)
        ), f"Expect 0 <= {start} <= {stop} <= {len(lines)}"
        return "".join(lines[start:stop])

    @property
    def selected_code(self) -> str:
        if self.middle_is_selected_code:
            return self.middle
        elif "selected_code" in self.misc:
            code = self.misc["selected_code"]
            if not isinstance(code, str):
                raise ValueError(f"Code not a string: {type(code)}: {code}")
            return code
        else:
            raise ValueError(f"The selected code is not found in {self.misc.keys()}.")

    @property
    def updated_code(self) -> str:
        if not self.middle_is_selected_code:
            return self.middle
        elif "updated_code" in self.misc:
            code = self.misc["updated_code"]
            if not isinstance(code, str):
                raise ValueError(f"Code not a string: {type(code)}: {code}")
            return code
        else:
            raise ValueError(f"The updated code is not found in {self.misc.keys()}.")

    def has_key(self, key: str) -> bool:
        return key in self.misc or key in (
            "prefix",
            "suffix",
            "middle",
            "selected_code",
            "lines",
            "file_name",
        )

    def get_value(self, key: str):
        if key in self.misc:
            return self.misc[key]
        else:
            return getattr(self, key)

    def show_code(self, max_context_lines: int = 100):
        """Show the code."""
        utils_for_str.show_edit_results(
            utils_for_str.get_last_n_lines(self.prefix, max_context_lines),
            self.middle,
            utils_for_str.get_first_n_lines(self.suffix, max_context_lines),
            updated_code=None,
            mode="diff",
        )

    def show_code_w_updated_code(self, max_context_lines: int = 100):
        """Show the code with the updated code."""
        if "updated_code" in self.misc:
            updated_code = self.misc["updated_code"]
            utils_for_str.show_edit_results(
                utils_for_str.get_last_n_lines(self.prefix, max_context_lines),
                self.selected_code,
                utils_for_str.get_first_n_lines(self.suffix, max_context_lines),
                updated_code=updated_code,
                mode="diff",
            )
        else:
            raise ValueError(f"The updated code is not found in {self.misc.keys()}.")

    def show_code_w_updated_code_and_instruction(self, max_context_lines: int = 100):
        """Show the code with the updated code and instruction."""
        if "updated_code" in self.misc and "instruction" in self.misc:
            updated_code = self.misc["updated_code"]
            instruction = self.misc["instruction"]
            if ("reverse_instruction" in self.misc) and isinstance(
                self.misc["reverse_instruction"], str
            ):
                reverse_instruction = self.misc["reverse_instruction"]
                instruction = f"{instruction} || Reverse: {reverse_instruction}"
            utils_for_str.show_edit_results(
                utils_for_str.get_last_n_lines(self.prefix, max_context_lines),
                self.selected_code,
                utils_for_str.get_first_n_lines(self.suffix, max_context_lines),
                updated_code=updated_code,
                instruction=instruction,
                mode="diff",
            )
        else:
            raise ValueError(
                f"The updated code or instruction is not found in {self.misc.keys()}."
            )


def get_text(doc: ParsedFile, node: ts.Node) -> str:
    """Get the text of the node."""
    crange: CharRange = tsnode_to_crange(node, doc.bmap)
    return doc.code[crange.start : crange.stop]


def get_line_range(doc: ParsedFile, node: ts.Node) -> LineRange:
    """Get the line range of the node."""
    crange: CharRange = tsnode_to_crange(node, doc.bmap)
    return doc.lmap.crange_to_lrange(crange)


def find_edit_data_scope(
    documents: list[tuple[str, ParsedFile]],
) -> list[SimpleRawEditScope]:
    """Find all the potential scopes for the edit data.

    Args:
        documents (list[ParsedFile]): A list of documents to be processed.

    Return:
        list[SimpleRawEditScope]: A list of all the potential scopes for the edit data.
    """

    all_scopes: list[SimpleRawEditScope] = []
    for repo, doc in tqdm.tqdm(documents, total=len(documents), desc="EDIT"):
        scopes_in_this_doc: list[LineRange] = []
        all_lines = doc.code.splitlines(keepends=True)
        for node in iter_ts_nodes_via_bfs(doc.ts_tree):
            cur_node = find_func_head_for_api_call(node)
            if cur_node is not None:
                lrange_func = get_line_range(doc, cur_node)
                lrange_func_head_tail_symbol = get_line_range(doc, node)
                # Add the function head
                scopes_in_this_doc.append(
                    LineRange(lrange_func.start, lrange_func_head_tail_symbol.stop)
                )
                # Add the entire function
                scopes_in_this_doc.append(get_line_range(doc, cur_node))
                # Add the function body
                scopes_in_this_doc.append(
                    LineRange(lrange_func_head_tail_symbol.stop, lrange_func.stop)
                )
                # # Add some random lines between them
                # start_line = random.randint(lrange_func.start, lrange_func.stop - 1)
                # end_line = min(start_line + random.randint(1, 30), len(all_lines))
                # scopes_in_this_doc.append(LineRange(start_line, end_line))
            cur_node = find_max_self_contained_code_snippet_for_simple_api_call(node)
            if cur_node is not None:
                lrange_func_call = get_line_range(doc, cur_node)
                scopes_in_this_doc.append(lrange_func_call)
        # # Each doc add a random scope
        # start_line = random.randint(0, len(all_lines) - 1)
        # end_line = min(start_line + random.randint(1, 30), len(all_lines))
        # scopes_in_this_doc.append(LineRange(start_line, end_line))
        # Deduplicate
        scopes_in_this_doc = list(set(scopes_in_this_doc))
        # Remove the scopes that are too long
        scopes_in_this_doc = [
            scope
            for scope in scopes_in_this_doc
            if (scope.stop - scope.start < 40) and (scope.stop > scope.start)
        ]
        # Remove the prefix and suffix are too large
        scopes_in_this_doc = [
            scope
            for scope in scopes_in_this_doc
            if scope.start >= 640 or len(all_lines) - scope.stop >= 640
        ]
        # Remove the scopes that are empty
        scopes_in_this_doc = [
            scope
            for scope in scopes_in_this_doc
            if "".join(all_lines[scope.start : scope.stop]).strip()
        ]
        # Random Shuffle
        random.shuffle(scopes_in_this_doc)
        for scope in scopes_in_this_doc:
            all_scopes.append(
                SimpleRawEditScope(
                    repo=repo,
                    file_name=str(doc.path),
                    content=doc.code,
                    scope=scope,
                    middle_is_selected_code=False,
                    misc={},
                )
            )
    print(f"Collect {len(all_scopes)} scopes from {len(documents)} documents.")
    return all_scopes


# if __name__ == "__main__":
#     from experimental.dxy.edits.util_lib import load_edit_eval_tests, load_repo_data

#     raw_repo_data = load_repo_data()
#     raw_edit_scopes = find_edit_data_scope(raw_repo_data)
#     print("-")
#     import pdb

#     pdb.set_trace()
#     print("-")

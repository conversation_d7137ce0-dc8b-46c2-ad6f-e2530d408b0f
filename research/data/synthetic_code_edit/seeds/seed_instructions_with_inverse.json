[{"instruction": "Add type annotations to this function", "inverse_instruction": "Delete type annotations from this function", "categories": ["Formatting-Cleaning"]}, {"instruction": "Change log level to error", "inverse_instruction": "Change log level to info", "categories": ["Logging-Debugging"]}, {"instruction": "Change np.mean to torch.mean", "inverse_instruction": "Change torch.mean to np.mean", "categories": ["Refactoring", "Library-Integration"]}, {"instruction": "Change numbers into floats", "inverse_instruction": "Change floats into numbers", "categories": ["Refactoring"]}, {"instruction": "Change numbers to strings", "inverse_instruction": "Change strings to numbers", "categories": ["Refactoring"]}, {"instruction": "Change variables to camel case", "inverse_instruction": "Change variables to snake case", "categories": ["Refactoring", "Formatting-Cleaning"]}, {"instruction": "Write it in one line", "inverse_instruction": "Format the code into multiple lines", "categories": ["Refactoring"]}, {"instruction": "Add a comment explaining the code", "inverse_instruction": "Remove the comment explaining the code", "categories": ["Documentation"]}, {"instruction": "Add error handling", "inverse_instruction": "Remove error handling", "categories": ["Error-<PERSON>ling"]}, {"instruction": "Add missing import", "inverse_instruction": "Remove one of the necessary imports", "categories": ["Bug-Fixing"]}, {"instruction": "Add the type annotations", "inverse_instruction": "Remove the type annotations", "categories": ["Formatting-Cleaning"]}, {"instruction": "Convert to python code", "inverse_instruction": "Convert from python code to pseudocode", "categories": ["Translation"]}, {"instruction": "Delete all the type annotations", "inverse_instruction": "Add type annotations", "categories": ["Formatting-Cleaning"]}, {"instruction": "Remove tqdm", "inverse_instruction": "Add tqdm", "categories": ["Library-Integration"]}, {"instruction": "Replace \" with ' anywhere", "inverse_instruction": "Replace ' with \" anywhere", "categories": ["Formatting-Cleaning"]}, {"instruction": "Replace double quotes with single quote", "inverse_instruction": "Replace single quote with double quote", "categories": ["Formatting-Cleaning"]}, {"instruction": "Rewrite using pathlib", "inverse_instruction": "Rewrite without using pathlib", "categories": ["Library-Integration", "Refactoring"]}, {"instruction": "Turn this into Python code", "inverse_instruction": "Convert the Python code to another programming language", "categories": ["Translation"]}, {"instruction": "Use # instead of ##", "inverse_instruction": "Use ## instead of #", "categories": ["Formatting-Cleaning"]}, {"instruction": "Use a 2 space indent", "inverse_instruction": "Use a 4 space indent", "categories": ["Formatting-Cleaning"]}, {"instruction": "Use a variable to make the if condition more readable", "inverse_instruction": "Inline the variable in the if condition", "categories": ["Refactoring"]}, {"instruction": "Use format instead", "inverse_instruction": "Use string concatenation instead", "categories": ["Refactoring"]}, {"instruction": "Use pathlib library to open file", "inverse_instruction": "Use traditional file opening methods instead of pathlib", "categories": ["Library-Integration", "Refactoring"]}, {"instruction": "Write more comments", "inverse_instruction": "Reduce the number of comments", "categories": ["Documentation"]}, {"instruction": "Fix typo", "inverse_instruction": "Introduce a typo", "categories": ["Bug-Fixing"]}, {"instruction": "Add an error handler", "inverse_instruction": "Remove the error handler", "categories": ["Error-<PERSON>ling"]}, {"instruction": "Catch Jedi SystemError.", "inverse_instruction": "Remove catch for Jedi SystemError.", "categories": ["Error-<PERSON>ling"]}, {"instruction": "Fix bug in data setting", "inverse_instruction": "Introduce a bug in data setting", "categories": ["Bug-Fixing"]}, {"instruction": "Fix the bug of infinite loop", "inverse_instruction": "Introduce a bug that causes an infinite loop", "categories": ["Bug-Fixing"]}, {"instruction": "Make the 'do' method static.", "inverse_instruction": "Make the 'do' method non-static.", "categories": ["Refactoring"]}, {"instruction": "Remove unnecessary comments.", "inverse_instruction": "Add unnecessary comments.", "categories": ["Documentation"]}, {"instruction": "Remove try/except in test_psd", "inverse_instruction": "Add try/except in test_psd", "categories": ["Error-<PERSON>ling", "Refactoring"]}, {"instruction": "Fix typo in exception message.", "inverse_instruction": "Introduce a typo in exception message.", "categories": ["Bug-Fixing", "Error-<PERSON>ling"]}, {"instruction": "Reformat to make flake8 happy.", "inverse_instruction": "Reformat to make flake8 unhappy.", "categories": ["Formatting-Cleaning"]}, {"instruction": "Consistent task name formatting", "inverse_instruction": "Inconsistent task name formatting", "categories": ["Refactoring", "Formatting-Cleaning"]}, {"instruction": "Catch exception on hook failure.", "inverse_instruction": "Remove exception catching on hook failure.", "categories": ["Error-<PERSON>ling"]}, {"instruction": "Fix an index out of bounds error", "inverse_instruction": "Introduce an index out of bounds error", "categories": ["Bug-Fixing", "Error-<PERSON>ling"]}, {"instruction": "Add choices for --average-method", "inverse_instruction": "Remove choices for --average-method", "categories": ["Refactoring"]}, {"instruction": "Make the condition more readable.", "inverse_instruction": "Make the condition less readable.", "categories": ["Refactoring", "Formatting-Cleaning"]}, {"instruction": "Remove the debug print statement.", "inverse_instruction": "Add a debug print statement.", "categories": ["Logging-Debugging"]}, {"instruction": "Validate input before assignment.", "inverse_instruction": "Remove input validation before assignment.", "categories": ["Hardening"]}, {"instruction": "Fix ImportError when using Pillow.", "inverse_instruction": "Introduce ImportError when using Pillow.", "categories": ["Bug-Fixing", "Error-<PERSON>ling", "Library-Integration"]}, {"instruction": "Add proper indentation to the code.", "inverse_instruction": "Remove proper indentation from the code.", "categories": ["Formatting-Cleaning"]}, {"instruction": "Use / instead of // in rotate_bound", "inverse_instruction": "Use // instead of / in rotate_bound", "categories": ["Formatting-Cleaning"]}, {"instruction": "Remove the import of nddata module.", "inverse_instruction": "Add the import of nddata module.", "categories": ["Refactoring", "Library-Integration"]}, {"instruction": "<PERSON>le exception while getting repo.", "inverse_instruction": "Remove exception handling while getting repo.", "categories": ["Error-<PERSON>ling"]}, {"instruction": "Simplify by ignoring the except cases", "inverse_instruction": "Complicate by handling the except cases", "categories": ["Error-<PERSON>ling", "Refactoring"]}, {"instruction": "Remove the workaround for ParseError.", "inverse_instruction": "Add a workaround for ParseError.", "categories": ["Error-<PERSON>ling", "Refactoring"]}, {"instruction": "Cast the output of T<PERSON>stack to \"int8\".", "inverse_instruction": "Do not cast the output of T.stack to \"int8\".", "categories": ["Implementation", "Refactoring"]}, {"instruction": "Replace 'self' with 'repl' in the code.", "inverse_instruction": "Replace 'repl' with 'self' in the code.", "categories": ["Refactoring"]}, {"instruction": "Fix wrong typehint of icon_custom_emoji_id", "inverse_instruction": "Introduce a wrong typehint for icon_custom_emoji_id", "categories": ["Bug-Fixing"]}, {"instruction": "Change 'asd' to 'spec' in the example code.", "inverse_instruction": "Change 'spec' to 'asd' in the example code.", "categories": ["Refactoring"]}, {"instruction": "Use OperationalError for error code >= 1000.", "inverse_instruction": "Do not use OperationalError for error code >= 1000.", "categories": ["Error-<PERSON>ling"]}, {"instruction": "Replace self.app.renderer with self.renderer", "inverse_instruction": "Replace self.renderer with self.app.renderer", "categories": ["Refactoring"]}, {"instruction": "Replace 'chat' with 'm' in the if statements.", "inverse_instruction": "Replace 'm' with 'chat' in the if statements.", "categories": ["Refactoring"]}, {"instruction": "Convert the output of zip function to a list.", "inverse_instruction": "Keep the output of zip function as it is.", "categories": ["Refactoring", "Implementation"]}, {"instruction": "Arrange import statements in alphabetical order.", "inverse_instruction": "Disarrange import statements from alphabetical order.", "categories": ["Formatting-Cleaning"]}, {"instruction": "Fix the case where the output is not valid JSON.", "inverse_instruction": "Introduce a case where the output is not valid JSON.", "categories": ["Bug-Fixing"]}, {"instruction": "Use min/max rather than conditional assignment.", "inverse_instruction": "Use conditional assignment instead of min/max.", "categories": ["Refactoring"]}, {"instruction": "Use string format() instead of f-string", "inverse_instruction": "Use f-string instead of string format()", "categories": ["Refactoring"]}, {"instruction": "Make all for loops enumerate", "inverse_instruction": "Convert enumerated for loops to regular for loops", "categories": ["Refactoring"]}, {"instruction": "Add single-dash flags to all arguments", "inverse_instruction": "Remove single-dash flags from all arguments", "categories": ["Refactoring"]}, {"instruction": "Make all fields in lowercase", "inverse_instruction": "Make all fields in uppercase", "categories": ["Refactoring", "Formatting-Cleaning"]}, {"instruction": "Change all local variables to camel case.", "inverse_instruction": "Change all local variables to snake case.", "categories": ["Refactoring", "Formatting-Cleaning"]}, {"instruction": "Replace prints with logging.error", "inverse_instruction": "Replace logging.error with prints", "categories": ["Logging-Debugging"]}, {"instruction": "Implement function", "inverse_instruction": "Delete function body", "categories": ["Implementation"]}]
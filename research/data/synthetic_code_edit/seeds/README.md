# Code Edit Seed Data

All code edit categories are at `research/data/synthetic_code_edit/categories.json`.

## Golden Seed Code Edit Data

```bash
|-- category_name_01
|-- 0001.txt (in git merging format)
|---- 0001.json (indent=2, containing the following fields)
|------ repo_url: str
|------ file_name: str
|------ category: str
|------ instructions: list[union[str, dict]]
|------ inverse_instructions: list[union[str, dict]]
|------ language: str
|------ (optional) feature@selected_code: list[float]
|------ (optional) feature@instruction: list[float]
|------ etc
|---- category_name_02
|-- ...
```

where if an instruction is a dict, it should have two keys `instruction` and `tags`.

An example of `0001.txt` is:

```python
prefix
<<<<<<<
selected code
=======
updated code
>>>>>>>
suffix
```

## Golden Seed Instructions for Code Edit

```bash
|-- category_name_01.instructions.json (a list of tuple of two strings)
|-- category_name_02.instructions.json
|-- ...
|-- unknown_category.instructions.json
```

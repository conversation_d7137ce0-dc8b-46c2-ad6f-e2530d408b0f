["replace a part of the code with a language-specific placeholder such as `// TODO` or `throw new NotImplementedException();` or `...`", "remove something important from the code", "replace a function name in the code with a made-up name", "replace a type name in the code with a made-up name", "mess up the logic", "remove comments", "change code structure", "replace a syntactic structure with equivalent", "swap the order of operations", "alter variable names to be misleading or non-descriptive", "change the scope of a variable or function", "insert an unnecessary or redundant piece of code", "replace literal value or values", "modify error handling to ignore or improperly handle errors", "alter the data types in a way that causes type mismatches or errors", "introduce typos", "introduce an inconsistency", "make a mistake", "mess up the formatting", "change the formatting", "delete function body", "remove a code path", "remove a conditional branch", "replace a logical chunk of code with TODO comments", "replace a logical chunk of code with commented-out pseudocode", "replace chunks of code with TODOs", "make a syntactic change", "change representation of some literals"]
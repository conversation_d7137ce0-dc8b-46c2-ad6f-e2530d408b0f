{"Formatting-Cleaning": ["format-code", "format-text", "make-readable"], "Refactoring": ["rename", "choose-better-name", "use-different-syntax", "extract-function", "extract-class"], "Bug-Fixing": ["fix-reasoning", "handle-edge-case"], "Testing": ["implement-unit-test", "implement-mock", "implement-end-to-end-test", "generate-test-data"], "Optimization": ["optimize-speed", "optimize-memory"], "Error-Handling": ["handle-exception", "handle-error", "handle-incorrect-input"], "Library-Integration": ["use-existing-internal-library", "use-existing-external-library"], "Implementation": ["implement-function", "implement-feature", "implement-algorithm", "define-interface", "inherit"], "Hardening": ["fix-security-problem", "sanitize-input"], "Logging-Debugging": ["add-logs", "add-debugging-logs", "setup-logger"], "Translation": ["translate-data-format", "translate-code-to-code", "translate-docstring-to-code", "implement-pseudocode"], "Documentation": ["add-documentation", "improve-documentation", "add-inline-comments"], "Reviewing": ["address-reviewer-comment"]}
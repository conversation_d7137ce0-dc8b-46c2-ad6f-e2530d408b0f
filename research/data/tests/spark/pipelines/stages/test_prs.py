"""Test the PRs data pipeline."""

from pathlib import Path

from unidiff import PatchSet

from base.tokenizers import Tokenizer, create_tokenizer_by_name
from research.core.types import Chunk, Document
from research.data.spark.pipelines.stages.prs import (
    CreatePrDatasetForNextEditLocationConfig,
    get_next_edit_location_sample,
)


def test_sample_formatting():
    """Test that an empty query returns an empty result."""
    tokenizer: Tokenizer = create_tokenizer_by_name("starcoder")
    pr = {
        "repo_name": "myrepo",
        "number": 123,
        "title": "",
        "body": "",
        "pr_diff": "",
    }

    doc = Document.new("text", "doc.py")

    chunks = [
        Chunk(
            id="chunk1",
            text="chunk1",
            parent_doc=doc,
            char_offset=0,
            length=6,
            line_offset=0,
            length_in_lines=1,
            meta={},
        ),
        Chunk(
            id="chunk2",
            text="chunk2",
            parent_doc=doc,
            char_offset=0,
            length=6,
            line_offset=0,
            length_in_lines=1,
            meta={},
        ),
    ]

    overlapping_chunk_labels = [True, False]

    config = CreatePrDatasetForNextEditLocationConfig(
        name="test",
        input=Path("input.parquet"),
        output=Path("output.parquet"),
        max_lines_per_chunk=10,
        max_negative_documents_per_sample=10,
        max_positive_documents_per_sample=10,
        add_path_to_prompt=True,
        max_query_token_length=1000,
        max_document_token_length=10,
        num_context_lines_in_diff=3,
    )

    diff_text = """\
diff --git a/foo.py b/foo.py
index 8ad95ccaa..a7c1bcb7a 100644
--- a/foo.py
+++ b/foo.py
@@ -2,2 +2,3 @@
 line1
+line2
 line3
@@ -16,3 +17,2 @@ def foo():
     line1
-        line2
         line3
@@ -58,2 +58,3 @@ def bar():
         line4
+        line5
     line6
@@ -63,2 +64,3 @@ def xyz():
         line7
+        line8
         line9
"""

    patch_set = PatchSet(diff_text)
    assert str(patch_set) == diff_text

    sample = get_next_edit_location_sample(
        tokenizer=tokenizer,
        pr=pr,
        past_to_wip_diff=patch_set,
        wip_to_future_diff=PatchSet(""),
        chunks=chunks,
        overlapping_chunk_labels=overlapping_chunk_labels,
        config=config,
    )
    assert sample is not None

    text = tokenizer.detokenize(sample.sample_tokens)
    expected_text = f"{diff_text}<|ret-endofquery|><|startofsequence|>doc.py<fim_middle>chunk1<|ret-endofkey|>0<|ret-endofkey|><|startofsequence|>doc.py<fim_middle>chunk2<|ret-endofkey|>-1<|ret-endofkey|>"
    assert text == expected_text


def test_empty_query():
    """Test that an empty query returns an empty result."""
    tokenizer: Tokenizer = create_tokenizer_by_name("starcoder")
    pr = {
        "title": "",
        "body": "",
    }

    doc = Document.new("text", "doc.py")

    chunks = [
        Chunk(
            id="chunk1",
            text="chunk1",
            parent_doc=doc,
            char_offset=0,
            length=6,
            line_offset=0,
            length_in_lines=1,
            meta={},
        ),
        Chunk(
            id="chunk2",
            text="chunk2",
            parent_doc=doc,
            char_offset=0,
            length=6,
            line_offset=0,
            length_in_lines=1,
            meta={},
        ),
    ]

    diff = PatchSet("")

    overlapping_chunk_labels = [True, False]

    config = CreatePrDatasetForNextEditLocationConfig(
        name="test",
        input=Path("input.parquet"),
        output=Path("output.parquet"),
        max_lines_per_chunk=10,
        max_negative_documents_per_sample=10,
        max_positive_documents_per_sample=10,
        add_path_to_prompt=True,
        max_query_token_length=10,
        max_document_token_length=10,
        num_context_lines_in_diff=3,
    )

    sample = get_next_edit_location_sample(
        tokenizer=tokenizer,
        pr=pr,
        past_to_wip_diff=diff,
        wip_to_future_diff=diff,
        chunks=chunks,
        overlapping_chunk_labels=overlapping_chunk_labels,
        config=config,
    )

    assert sample is None

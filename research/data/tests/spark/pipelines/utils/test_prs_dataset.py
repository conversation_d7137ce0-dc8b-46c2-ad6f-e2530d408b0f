"""Test PR dataset utilities."""

from pathlib import Path

import pandas
import pytest

from research.data.spark.pipelines.utils.prs_dataset import (
    RepositoryFileManager,
)


@pytest.mark.skip_in_ci(reason="needs access to /mnt/efs/spark-data")
def test_repository_file_manager():
    """Sanity check for the repository file manager.

    Add repository files to the manager, then pull up the files from the PRs.
    """
    pr_repo_reference_parquet_file = Path(
        "/mnt/efs/spark-data/shared/gh_pr_repo_ref.parquet"
    )
    manager = RepositoryFileManager(
        pr_repo_reference_parquet_file=pr_repo_reference_parquet_file
    )

    repo_name1 = "aio-libs/aiohttp-jinja2"
    repo_name2 = "seek-oss/rynovate"

    # Add files to the manager
    manager.add_repository_files(repo_name1)
    assert len(manager.repo_files) == 1
    manager.add_repository_files(repo_name2)
    assert len(manager.repo_files) == 2

    # Adding the same repo again should not change anything
    manager.add_repository_files(repo_name2)
    assert len(manager.repo_files) == 2

    # Look at PRs from the first repo, make sure sensible files were loaded
    pr_parquet_file, _ = manager.get_pr_repo_join_data(repo_name1)
    prs_df = pandas.read_parquet(pr_parquet_file)

    repo_prs: list[dict] = prs_df[prs_df["repo_name"] == repo_name1].to_dict(
        orient="records"
    )

    for pr in repo_prs[:5]:
        files = manager.get_pr_files(pr).files
        assert len(files) == len(pr["files"])
        for returned_file, existing_file in list(zip(files, pr["files"]))[:5]:
            assert returned_file.path == existing_file["path"]

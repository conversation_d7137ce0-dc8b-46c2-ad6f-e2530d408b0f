"""Test the get_session module that creates a spark session."""

import pytest
import pyspark.sql.functions as F

from research.data.spark import k8s_session


@pytest.mark.skip_in_ci(reason="works locally but not in CI")
def test_pyspark_memory_scaling():
    """Test that pyspark memory is scaled correctly due to the spark bug."""
    spark = k8s_session(
        max_workers=1,
        conf={
            "spark.task.cpus": "5",
            "spark.executor.pyspark.memory": "20G",
        },
    )
    actual_pyspark_memory = spark.sparkContext.getConf().get(
        "spark.executor.pyspark.memory"
    )
    assert (
        actual_pyspark_memory == "100G"
    ), "pyspark memory should be scaled by the number of cpus"

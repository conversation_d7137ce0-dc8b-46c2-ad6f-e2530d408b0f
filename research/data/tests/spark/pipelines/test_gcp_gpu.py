"""Test if gpu pods are working correctly on GCP."""

import pytest
import torch

from pyspark.sql import functions as F
from research.data.spark.utils import k8s_session


@pytest.mark.skip_in_ci(reason="works locally but not in CI")
def test_spark_gpu():
    spark = k8s_session(
        name="xz-test-notebook",
        max_workers=1,
        gpu_type="nvidia-l4",
    )

    @F.udf(returnType="boolean")
    def cuda_there(x: int):
        import _gojsonnet  # do some imports to make sure environment works
        import research

        return torch.cuda.is_available()

    df = spark.range(1).withColumn("cuda_there", cuda_there(F.col("id"))).collect()
    has_cuda = df[0].cuda_there
    assert has_cuda is True, "Cuda not working in workers! has_cuda: " + str(has_cuda)


if __name__ == "__main__":
    test_spark_gpu()

"""Tests for the stackexchange collection, formatting and tokenization.

pytest research/data/tests/collection/test_stackexchange.py
"""

from typing import Iterable

import pytest

from research.data.collection.stackexchange.gen_stackexchange_dataset import (
    StackExchangeFormatOptions,
    format_answers,
    format_comments,
    format_question,
)
from research.data.collection.utils.tokenize_with_masking import MaskableText

DEFAULT_OWNER = {"display_name": "PERSON"}


def get_answer(
    body="Answer body",
    score=1,
    owner=None,
    comments=(),
    is_accepted=False,
    created_at="2021-01-01",
):
    return {
        "body": body,
        "score": score,
        "owner": owner or DEFAULT_OWNER,
        "comments": comments,
        "is_accepted": is_accepted,
        "created_at": created_at,
    }


def get_comment(text="Comment body", score=1, owner=None):
    return {
        "text": text,
        "score": score,
        "owner": owner or DEFAULT_OWNER,
    }


def get_question(
    title="Question title",
    question_body="Question body",
    score=1,
    owner=None,
    comments=(),
    answers=(),
):
    pass


def strip_masked(items: Iterable[MaskableText]) -> list[str]:
    return [item.text.strip() for item in items if not item.masked]


def test_format_question_no_answer():
    """Questions without answers are skipped."""
    options = StackExchangeFormatOptions(show_questions_without_answers=False)
    blocks = list(format_question({"answers": []}, options))
    assert len(blocks) == 0


# Test comment formatting

COMMENT_SCORES = [1, 3, -1, 2]
COMMENTS = [
    get_comment(text=f"comment {score}", score=score) for score in COMMENT_SCORES
]


@pytest.mark.parametrize(
    "show_negative_comments, max_comments, expected_results",
    [
        (
            True,
            10,
            [
                "comment 1",
                "comment 3",
                "comment -1",
                "comment 2",
            ],
        ),
        (
            False,
            10,
            [
                "comment 1",
                "comment 3",
                "comment 2",
            ],
        ),
        (
            True,
            2,
            [
                "comment 3",
                "comment 2",
            ],
        ),
    ],
)
def test_format_comments(show_negative_comments, max_comments, expected_results):
    """Comments are formatted.

    Spoilerplates should be masked out.
    They should preserve their original order.
    Negative scored ones are filtered out when options are present.
    When comment limit is breached, remove lowest scored items.
    """
    results = format_comments(
        COMMENTS,
        StackExchangeFormatOptions(
            show_negative_comments=show_negative_comments, max_comments=max_comments
        ),
    )
    assert strip_masked(results) == expected_results


# Test answer formatting

ANSWER_SCORES = [4, 3, -1, 2]
ACCEPTED_ID = 3
ANSWERS = [
    get_answer(body=f"answer {score}", score=score, is_accepted=i == ACCEPTED_ID)
    for i, score in enumerate(ANSWER_SCORES)
]


@pytest.mark.parametrize(
    "show_negative_answers, max_answers, expected_results",
    [
        (
            True,
            None,
            [
                "answer 2",
                "answer 4",
                "answer 3",
                "answer -1",
            ],
        ),
        (
            False,
            None,
            [
                "answer 2",
                "answer 4",
                "answer 3",
            ],
        ),
        (
            True,
            2,
            [
                "answer 2",
                "answer 4",
            ],
        ),
    ],
)
def test_format_answers(show_negative_answers, max_answers, expected_results):
    """Answers are formatted.

    Spoilerplates should be masked out.
    The accepted answers come first, followed by other
    answers ordered by score.
    Negative scored ones are filtered out when options are present.
    When answer limit is breached, remove lowest scored items.
    """
    results = format_answers(
        ANSWERS,
        StackExchangeFormatOptions(
            show_negative_answers=show_negative_answers, max_answers=max_answers
        ),
    )
    assert strip_masked(results) == expected_results


# Test question formatting


def test_format_question():
    """Questions are formatted in an overall example."""

    q = {
        "answers": ANSWERS,
        "question_body": "QUESTION",
        "comments": COMMENTS,
        "created_at": "2021-01-01",
        "owner": DEFAULT_OWNER,
        "score": 1234,
        "view_count": 4321,
        "title": "TITLE",
        "tags": ["TAG1", "TAG2"],
    }
    results = list(
        format_question(q, StackExchangeFormatOptions(max_answers=3, max_comments=2))
    )
    assert strip_masked(results) == [
        "QUESTION",
        "comment 3",
        "comment 2",
        "answer 2",
        "answer 4",
        "answer 3",
    ]

    masked_part = "\n".join(item.text for item in results if item.masked)
    # Title, tags, score and view count etc are masked
    assert "TITLE" in masked_part
    assert "TAG1" in masked_part
    assert "TAG2" in masked_part
    assert "+1234" in masked_part
    assert "4321" in masked_part

"""Tests for tokenize_with_mask.

pytest research/data/tests/collection/test_tokenize_with_mask.py
"""

from typing import Iterable

import pandas as pd
import pytest

from base.tokenizers import (
    DeepSeekCoderBaseTokenizer,
    SpecialTokens,
    create_tokenizer_by_name,
)
from research.data.collection.utils.tokenize_with_masking import (
    MaskableText,
    tokenize_and_pack,
)


def text_generator(row: dict[str, str]) -> Iterable[MaskableText]:
    """A text generator that returns two MaskableText objects."""
    yield MaskableText(row["masked"], True)
    yield MaskableText(row["unmasked"], False)


# the masked and unmasked text are each 5-7 tokens.  Each doc is between 10 and 20 tokens.
SAMPLE_DATA = pd.DataFrame(
    {
        "masked": ["This is a masked text", "This is another masked text"],
        "unmasked": ["This is an unmasked text", "This is another unmasked text"],
    }
)


def trim_padding(tokens: list[int], special_tokens: SpecialTokens) -> list[int]:
    """Util function to trim padding from a then end of a list of tokens.

    Padding tokens may or may not be masked.  In the case of unmasked padding that
    equals to the EOS token, the first unmasked EOS should not be trimmed.
    """
    padding = special_tokens.padding
    end = len(tokens)
    for i in range(len(tokens) - 1, 0, -1):
        if abs(tokens[i]) == padding:
            end = i
        else:
            break
    if (
        end < len(tokens)
        and abs(tokens[end]) == special_tokens.eos
        and special_tokens.eos == padding
    ):
        end += 1
    return tokens[:end]


def check_tokenized_sample(
    samples: pd.DataFrame,
    column: str,
    expected_length: int,
    add_one_token: bool,
    expected_samples: int | None,
) -> list[list[int]]:
    """Check if a set of tokenized samples match the expected characteristics."""
    if expected_samples is not None:
        assert (
            len(samples) == expected_samples
        ), f"Expected {expected_samples} samples, got {len(samples)}"

    # check output column name
    assert column in samples.columns, f"Column {column} not found in output dataframe"

    output = samples[column]

    if add_one_token:
        expected_length += 1

    # Check length
    assert set(output.apply(len)) == {
        expected_length
    }, f"Lengths are not equal to expected value of {expected_length}"

    return output.tolist()


@pytest.mark.parametrize(
    "tokenizer_name",
    [
        "deepseek_coder_base",
        "starcoder",
    ],
)
@pytest.mark.parametrize(
    "mask_padding",
    [
        True,
        False,
    ],
)
def test_trim_padding(tokenizer_name: str, mask_padding: bool):
    """Test the trim_padding util function is right."""
    tokenizer = create_tokenizer_by_name(tokenizer_name)
    special_tokens = tokenizer.special_tokens
    padding = special_tokens.padding
    if mask_padding:
        padding = -padding
    eos = special_tokens.eos
    original = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    assert trim_padding(original, special_tokens) == original
    assert trim_padding(original + [eos], special_tokens) == original + [eos]
    assert trim_padding(original + [eos, padding], special_tokens) == original + [eos]
    assert trim_padding(
        original + [eos, padding, padding], special_tokens
    ) == original + [eos]


OUTPUT_COL = "packed_samples"


# Each doc is between 10 and 20 tokens.
@pytest.mark.parametrize(
    "tokenizer_name",
    [
        "deepseek_coder_base",
        "starcoder",
    ],
)
@pytest.mark.parametrize(
    "sequence_length, add_one_token, max_tail_pad, expected_samples",
    [
        # Very short sequences should still assemble back to the right strings.
        (1, False, 0, None),
        # Seq length 10+1 because that's right below the starcoder actual token length
        # So each doc needs to be split.
        # Scenario 1, no dense packing (max_tail_pad = full length) each doc should be split
        # into 2 samples and padded to the end.  They should not be packed together to minimize
        # truncation, making 4 samples.
        (10, True, 10, 4),
        # Scenario 2, tail length is 0 and completely dense packing.  2 docs can fit into 3 samples
        (10, True, 0, 3),
        # Very long sequence, everything should fit into 1 sample
        (4096, True, 410, 1),
        # Even we enable tail padding and prevent truncation with max_tail_pad ~ full length,
        # everything should still fit into 1 sample because they won't need to be truncated.
        (4096, False, 4095, 1),
    ],
)
def test_tokenize_and_pack_dense(
    tokenizer_name: str,
    sequence_length: int,
    add_one_token: bool,
    max_tail_pad: int,
    expected_samples: int,
):
    """Test the tokenize_and_pack function with dense packing"""
    tokenizer = create_tokenizer_by_name(tokenizer_name)
    if isinstance(tokenizer, DeepSeekCoderBaseTokenizer):
        bos_seq = list(tokenizer.special_tokens.begin_sequence)
    else:
        bos_seq = []
    raw_result = tokenize_and_pack(
        SAMPLE_DATA,
        text_generator,
        tokenizer_name,
        output_column=OUTPUT_COL,
        seq_length=sequence_length,
        add_one_token=add_one_token,
        max_tail_pad=max_tail_pad,
    )
    assert raw_result is not None
    result = check_tokenized_sample(
        raw_result,
        OUTPUT_COL,
        sequence_length,
        add_one_token=add_one_token,
        expected_samples=expected_samples,
    )
    trim_result = [trim_padding(x, tokenizer.special_tokens) for x in result]
    expected = []
    for masked, unmasked in zip(SAMPLE_DATA.masked, SAMPLE_DATA.unmasked):
        expected.extend(bos_seq)
        expected.extend(-item for item in tokenizer.tokenize_safe(masked))
        expected.extend(tokenizer.tokenize_safe(unmasked))
        expected.append(tokenizer.special_tokens.eos)
    assert [item for row in trim_result for item in row] == expected

"""Tests for markdown conversion."""

import pytest
from concurrent.futures import ThreadPoolExecutor

from research.data.collection.utils.markdown_conversion import (
    convert_html_to_md,
    remove_data_urls,
    replace_excessive_newlines,
    robust_conversion,
)


def test_convert_html_to_md():
    html = "<p>Hello <b>world</b>!</p>"
    expected_md = "Hello **world**!"
    assert convert_html_to_md(html).strip() == expected_md


def test_replace_excessive_newlines():
    md = "Hello\n\n\nWorld!\n\n"
    expected_md = "Hello\n\nWorld!"
    assert replace_excessive_newlines(md).strip() == expected_md


def test_remove_data_urls():
    md = "![Image](data:image/png;base64,iVBORw0KGg...)"
    expected_md = "![Image Removed]"
    assert remove_data_urls(md) == expected_md


def test_remove_data_urls_html_single_quote():
    html = "one,<img src='data:image/png;base64,iVBORw0KGg...'>two"
    expected_md = "one,<img src=''>two"
    assert remove_data_urls(html) == expected_md


def test_remove_data_urls_html_double_quote():
    html = 'one,<img src="data:image/png;base64,iVBORw0KGg...">two'
    expected_md = 'one,<img src="">two'
    assert remove_data_urls(html) == expected_md


def test_robust_conversion():
    html = "<p>Hello <b>world</b>!</p>"
    expected_md = "Hello **world**!"
    assert robust_conversion(html).strip() == expected_md


def test_cleanup_article():
    """If there are <article> or <main> tags, only keep the contents in those."""
    html = """Outside text should disappear
    <article>
    <p>Hello <b>world</b>!</p>
    </article>

    Outside text should disappear

    <article>
    <p>Hello <b>world</b>!</p>
    </article>
    """
    expected_md = "Hello **world**!\n\nHello **world**!"
    assert robust_conversion(html).strip() == expected_md


def test_code_in_and_out_of_pre():
    """Code should be single backtick quoted outside pre but not inside pre.

    Pre-block can be presented with either literal quote (4 spaces in markdown)
    or triple backticks.
    """
    html = """<code>quoted code</code>
<pre><code>unquoted</code> <code>code</code></pre>
    """
    expected_md1 = "`quoted code`\n\n    unquoted code"
    expected_md2 = "`quoted code`\n\n```\nunquoted code\n```"
    assert robust_conversion(html).strip() in [expected_md1, expected_md2]


def test_remove_ads():
    """Ads should be removed."""
    html = """<div id="ad-container">top ad</div>
    hello world!
    <div id="ad-container">bottom ad</div>
    """
    expected_md = "hello world!"
    assert robust_conversion(html).strip() == expected_md


def test_textarea_to_pre():
    """<textarea> should be converted to <pre>"""
    html = """here:\n<textarea>hello    world!</textarea>"""
    expected_md = [
        "here:\n```\nhello    world!\n```",
        "here:\n\n```\nhello    world!\n```",
        "here:\n\n    hello    world!",
    ]
    assert robust_conversion(html).strip() in expected_md

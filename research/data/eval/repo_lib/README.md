# Repository Management Tools

## Detailed Functionality

**`coverage.py` includes the data class and functions to parse and store the coverage information.**

- `CoverageInfo` is a dataclass contains the coverage information of a file.
- `parse_pytest_coverage_xml` can parse a `xml`'s `ElementTree`, which contains the coverage information generated by pytest, into a list of `CoverageInfo`.
- `parse_pytest_coverage_xml_file` is similar to `parse_pytest_coverage_xml` but take the xml file as input.

**`repo.py` includes the functions to process the reposotiries.**

- `get_git_sha` takes the repo root directory as input and return the SHA string.
- `copy_files_from_docker` takes the `image_name` str as input and copy the files from the docker contrainer for this image into the local machine. The return value is the copied file in the local machine.
- `parse_docs_from_docker_image` parsed all the documents from a given docker image.

Contact <PERSON><PERSON><PERSON> (dxy@) if u have any questions about this lib.

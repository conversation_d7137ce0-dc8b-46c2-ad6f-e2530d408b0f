FROM amd64/ubuntu

# General installs
RUN apt -y update && apt -y upgrade
RUN apt -y install sudo wget git gcc g++ build-essential curl vim
RUN apt-get -y install python3.9
RUN cp /usr/bin/python3 /usr/bin/python
WORKDIR /code

# Copy in run script
RUN mkdir .hydra
COPY ./run.sh .hydra/run.sh
RUN chmod ugo+rwx .hydra/run.sh

# Support programming language: Java
RUN apt -y install openjdk-11-jdk

# Install git repo and apply patch
COPY ./gephi.diff /data/gephi.diff
ARG GIT_SHA=a42603fd0cb2c209f96c79481dcc1f7195058822
RUN git clone https://github.com/gephi/gephi.git
RUN cp -rT /code/gephi /code
RUN rm -fr /code/gephi/
RUN git checkout ${GIT_SHA}
RUN git apply /data/gephi.diff

# Support build system: Maven
ARG MAVEN_VERSION=3.9.4
ARG BASE_URL=https://apache.osuosl.org/maven/maven-3/${MAVEN_VERSION}/binaries
RUN mkdir -p /usr/share/maven /usr/share/maven/ref \
 && curl -fsSL -o /tmp/apache-maven.tar.gz ${BASE_URL}/apache-maven-${MAVEN_VERSION}-bin.tar.gz \
 && tar -xzf /tmp/apache-maven.tar.gz -C /usr/share/maven --strip-components=1 \
 && rm -f /tmp/apache-maven.tar.gz \
 && ln -s /usr/share/maven/bin/mvn /usr/bin/mvn
ENV MAVEN_HOME /usr/share/maven
ENV JAVA_HOME /usr/lib/jvm/java-11-openjdk-amd64

# In order to install dependences and cache test results
RUN mvn test -PenableTests

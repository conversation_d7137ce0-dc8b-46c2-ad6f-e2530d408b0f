FROM amd64/ubuntu

# General installs
RUN apt -y update && apt -y upgrade &&  apt-get update
RUN apt -y install sudo wget git gcc g++ build-essential vim curl
RUN apt-get -y install python3.9
RUN cp /usr/bin/python3 /usr/bin/python
WORKDIR /code

# Copy in run script
RUN mkdir .hydra
COPY ./run.sh .hydra/run.sh
RUN chmod ugo+rwx .hydra/run.sh

# Download and patch git repo
#COPY ./<repo_name>.patch /data/<repo_name>.patch
ARG GIT_SHA=56b2eddbc6a22a67e844d1451e6bad9fefdbec68
RUN git clone https://github.com/resilience4j/resilience4j.git
RUN cp -rT /code/resilience4j /code
RUN rm -fr /code/resilience4j/
RUN git checkout ${GIT_SHA}

# These are flakey
RUN rm resilience4j-bulkhead/src/test/java/io/github/resilience4j/bulkhead/*Test.java
RUN rm resilience4j-bulkhead/src/test/java/io/github/resilience4j/event/*Test.java
RUN rm resilience4j-bulkhead/src/test/java/io/github/resilience4j/internal/*Test.java
RUN rm resilience4j-micrometer/src/test/java/io/github/resilience4j/micrometer/*Test.java
RUN rm resilience4j-micrometer/src/test/java/io/github/resilience4j/micrometer/tagged/*Test.java

# RUN git apply /data/<repo_name>.patch

# Support programming language: Java
RUN apt -y install openjdk-17-jdk

# Setup gradle
RUN ./gradlew --no-daemon

# Run tests to pre-build everything
RUN ./gradlew test --no-daemon

FROM amd64/ubuntu

# General installs
RUN apt -y update && apt -y upgrade
RUN apt -y install sudo wget git gcc g++ build-essential curl vim
RUN apt-get -y install python3.9
RUN cp /usr/bin/python3 /usr/bin/python
WORKDIR /code

# Copy in run script
RUN mkdir .hydra
COPY run.sh .hydra/run.sh
RUN chmod ugo+rwx .hydra/run.sh

# Install git repo and apply patch
ARG GIT_SHA=7103ea096fffd851a5efd67fd6d30cf65bc38873
RUN git clone https://github.com/caddyserver/caddy.git
RUN cp -rT /code/caddy /code
RUN rm -fr /code/caddy/
RUN git checkout ${GIT_SHA}

# Install essential build tools and Go
RUN apt-get update
RUN apt-get install -y --no-install-recommends ca-certificates
RUN rm -rf /var/lib/apt/lists/*
RUN curl -L https://golang.org/dl/go1.20.linux-amd64.tar.gz | tar -C /usr/local -xzf -

# Set Go environment variables
ENV PATH="/usr/local/go/bin:${PATH}"


# Download Go modules
RUN cd /code && \
    go mod download

# Build the Go repository
RUN cd /code && \
    go build -v -buildvcs=false ./...

# Go has great dependency checking, so let's cache test results and trust that
# it will rebuild / rerun appropriately for changed files
RUN cd /code && \
    go test ./caddyconfig/... ./cmd/... ./internal/... ./modules/...

# Set the PATH environment variable to include the Go binaries
ENV PATH="${GOPATH}/bin:${PATH}"

FROM amd64/ubuntu

# General installs
RUN apt -y update && apt -y upgrade
RUN apt -y install sudo wget git gcc g++ build-essential curl vim
RUN apt-get -y install python3.9
RUN cp /usr/bin/python3 /usr/bin/python
WORKDIR /code

# Copy in run script
RUN mkdir .hydra
COPY run.sh .hydra/run.sh
RUN chmod ugo+rwx .hydra/run.sh

# Install git repo and apply patch
ARG GIT_SHA=3021d314f71b9ce0707e8f5a443dd49b6a73d7da
RUN git clone https://github.com/jaegertracing/jaeger.git
RUN cp -rT /code/jaeger /code
RUN rm -fr /code/jaeger/
RUN git checkout ${GIT_SHA}

# Install essential build tools and Go
RUN apt-get update
RUN apt-get install -y --no-install-recommends ca-certificates
RUN rm -rf /var/lib/apt/lists/*
RUN curl -L https://golang.org/dl/go1.20.linux-amd64.tar.gz | tar -C /usr/local -xzf -

# Set Go environment variables
ENV PATH="/usr/local/go/bin:${PATH}"


# Download Go modules
RUN cd /code && \
    go mod download

# Build the Go repository
RUN cd /code && \
    go build -v -buildvcs=false ./...

# Go has great dependency checking, so let's cache test results and trust that
# it will rebuild / rerun appropriately for changed files
RUN cd /code && \
    go test ./cmd/agent/... ./cmd/all-in-one/... ./cmd/collector/... ./cmd/env ./cmd/es-index-cleaner/app ./cmd/es-rollover/... ./cmd/esmapping-generator/... ./cmd/flags ./cmd/ingester/... ./cmd/status ./crossdock/services ./examples/hotrod/pkg/tracing/rpcmetrics ./internal/... ./model/... ./pkg/bearertoken ./pkg/cache ./pkg/cassandra/metrics ./pkg/clientcfg/clientcfghttp ./pkg/config/... ./pkg/es/... ./pkg/fswatcher ./pkg/gogocodec ./pkg/gzipfs ./pkg/healthcheck ./pkg/hostname ./pkg/httpfs ./pkg/httpmetrics ./pkg/metrics ./pkg/netutils ./pkg/normalizer ./pkg/queue ./pkg/recoveryhandler ./pkg/tenancy ./pkg/testutils ./plugin/metrics/... ./plugin/pkg/distributedlock/cassandra ./plugin/sampling/... ./plugin/storage/cassandra/... ./plugin/storage/es/... ./plugin/storage/grpc/... ./plugin/storage/integration ./plugin/storage/memory ./ports ./storage/...

# Set the PATH environment variable to include the Go binaries
ENV PATH="${GOPATH}/bin:${PATH}"

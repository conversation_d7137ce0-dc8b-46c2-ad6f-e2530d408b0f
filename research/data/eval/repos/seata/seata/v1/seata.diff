diff --git a/common/src/test/java/io/seata/common/util/NetUtilTest.java b/common/src/test/java/io/seata/common/util/NetUtilTest.java
index c830e0fc..7181a45e 100644
--- a/common/src/test/java/io/seata/common/util/NetUtilTest.java
+++ b/common/src/test/java/io/seata/common/util/NetUtilTest.java
@@ -159,26 +159,4 @@ public class NetUtilTest {
     public void testGetLocalAddress() {
         assertThat(NetUtil.getLocalAddress()).isNotNull();
     }
-
-    @Test
-    public void testIsValidIp() {
-        String localIp = "127.0.0.1";
-        String someIp = "************";
-        String someHostName = "seata.io";
-        String unknownHost = "knownHost";
-        assertThat(NetUtil.isValidIp(localIp, true)).isTrue();
-        assertThat(NetUtil.isValidIp(localIp, false)).isFalse();
-
-        assertThat(NetUtil.isValidIp(someIp, true)).isTrue();
-        assertThat(NetUtil.isValidIp(someIp, false)).isTrue();
-
-        assertThat(NetUtil.isValidIp(someHostName, true)).isTrue();
-        assertThat(NetUtil.isValidIp(someHostName, false)).isTrue();
-
-        assertThatThrownBy(() -> {
-            NetUtil.isValidIp(unknownHost, false);
-        }).isInstanceOf(RuntimeException.class).hasMessageContaining("UnknownHostException");
-
-    }
-
 }
diff --git a/compressor/seata-compressor-zstd/src/test/java/io/seata/compressor/zstd/ZstdCompressorTest.java b/compressor/seata-compressor-zstd/src/test/java/io/seata/compressor/zstd/ZstdCompressorTest.java
deleted file mode 100644
index 4f08ce2a..00000000
--- a/compressor/seata-compressor-zstd/src/test/java/io/seata/compressor/zstd/ZstdCompressorTest.java
+++ /dev/null
@@ -1,53 +0,0 @@
-/*
- *  Copyright 1999-2019 Seata.io Group.
- *
- *  Licensed under the Apache License, Version 2.0 (the "License");
- *  you may not use this file except in compliance with the License.
- *  You may obtain a copy of the License at
- *
- *       http://www.apache.org/licenses/LICENSE-2.0
- *
- *  Unless required by applicable law or agreed to in writing, software
- *  distributed under the License is distributed on an "AS IS" BASIS,
- *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- *  See the License for the specific language governing permissions and
- *  limitations under the License.
- */
-package io.seata.compressor.zstd;
-
-import org.junit.jupiter.api.Test;
-
-import java.nio.charset.StandardCharsets;
-import java.util.UUID;
-
-/**
- * the Zstd Compressor test
- *
- * <AUTHOR>
- */
-public class ZstdCompressorTest {
-
-    @Test
-    public void testCompressAndDecompress() {
-        StringBuilder sb = new StringBuilder();
-        for (int i = 0; i < 100000; i++) {
-            sb.append(UUID.randomUUID().toString().replace("-", ""));
-        }
-
-        byte[] bytes = sb.toString().getBytes(StandardCharsets.UTF_8);
-
-        ZstdCompressor compressor = new ZstdCompressor();
-        long start = 0;
-        for (int i = 0; i < 1010; i ++) {
-            if (i == 10) {
-                start = System.currentTimeMillis();
-            }
-
-            bytes = compressor.compress(bytes);
-            bytes = compressor.decompress(bytes);
-        }
-        System.out.println("bytes size=" + bytes.length + "; usage=" + (System.currentTimeMillis() - start));
-        bytes = compressor.compress(bytes);
-        System.out.println("compressed size=" + bytes.length);
-    }
-}
diff --git a/core/src/test/java/io/seata/core/protocol/MessageFutureTest.java b/core/src/test/java/io/seata/core/protocol/MessageFutureTest.java
deleted file mode 100644
index b7d3699c..00000000
--- a/core/src/test/java/io/seata/core/protocol/MessageFutureTest.java
+++ /dev/null
@@ -1,198 +0,0 @@
-/*
- *  Copyright 1999-2019 Seata.io Group.
- *
- *  Licensed under the Apache License, Version 2.0 (the "License");
- *  you may not use this file except in compliance with the License.
- *  You may obtain a copy of the License at
- *
- *       http://www.apache.org/licenses/LICENSE-2.0
- *
- *  Unless required by applicable law or agreed to in writing, software
- *  distributed under the License is distributed on an "AS IS" BASIS,
- *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- *  See the License for the specific language governing permissions and
- *  limitations under the License.
- */
-package io.seata.core.protocol;
-
-import com.alibaba.fastjson.JSON;
-import org.junit.jupiter.api.Assertions;
-import org.junit.jupiter.api.Test;
-
-import java.util.HashMap;
-import java.util.concurrent.CountDownLatch;
-import java.util.concurrent.ExecutorService;
-import java.util.concurrent.Executors;
-import java.util.concurrent.TimeUnit;
-import java.util.concurrent.TimeoutException;
-
-import static org.assertj.core.api.Assertions.assertThat;
-
-/**
- * The type Message future test.
- *
- * <AUTHOR>
- */
-public class MessageFutureTest {
-
-    private static final String BODY_FIELD = "test_body";
-    private static final int ID_FIELD = 100;
-    private static final byte CODEC_FIELD = 1;
-    private static final byte COMPRESS_FIELD = 2;
-    private static final byte MSG_TYPE_FIELD = 3;
-    private static final HashMap<String, String> HEAD_FIELD = new HashMap<>();
-    private static final long TIME_OUT_FIELD = 100L;
-
-    /**
-     * Test field set get.
-     */
-    @Test
-    public void testFieldSetGet() {
-        String fromJson = "{\n" +
-            "\t\"requestMessage\":{\n" +
-            "\t\t\"body\":\"" + BODY_FIELD + "\",\n" +
-            "\t\t\"codec\":" + CODEC_FIELD + ",\n" +
-            "\t\t\"compressor\":" + COMPRESS_FIELD + ",\n" +
-            "\t\t\"headMap\":" + HEAD_FIELD + ",\n" +
-            "\t\t\"id\":" + ID_FIELD + ",\n" +
-            "\t\t\"messageType\":" + MSG_TYPE_FIELD + "\n" +
-            "\t},\n" +
-            "\t\"timeout\":" + TIME_OUT_FIELD + "\n" +
-            "}";
-        MessageFuture fromJsonFuture = JSON.parseObject(fromJson, MessageFuture.class);
-        assertThat(fromJsonFuture.getTimeout()).isEqualTo(TIME_OUT_FIELD);
-        MessageFuture toJsonFuture = new MessageFuture();
-        toJsonFuture.setRequestMessage(buildRepcMessage());
-        toJsonFuture.setTimeout(TIME_OUT_FIELD);
-        String toJson = JSON.toJSONString(toJsonFuture, true);
-        assertThat(toJson).isEqualTo(fromJson);
-    }
-
-    /**
-     * Test is time out.
-     *
-     * @throws Exception the exception
-     */
-    @Test
-    public void testIsTimeOut() throws Exception {
-        MessageFuture messageFuture = new MessageFuture();
-        messageFuture.setTimeout(TIME_OUT_FIELD);
-        assertThat(messageFuture.isTimeout()).isFalse();
-        Thread.sleep(TIME_OUT_FIELD + 1);
-        assertThat(messageFuture.isTimeout()).isTrue();
-
-    }
-
-    /**
-     * Test get no result with time out exception.
-     */
-    @Test
-    public void testGetNoResultWithTimeOutException() {
-        Assertions.assertThrows(TimeoutException.class, () -> {
-            MessageFuture messageFuture = new MessageFuture();
-            messageFuture.setRequestMessage(buildRepcMessage());
-            messageFuture.setTimeout(TIME_OUT_FIELD);
-            messageFuture.get(TIME_OUT_FIELD, TimeUnit.MILLISECONDS);
-        });
-    }
-
-    /**
-     * Test get has result with time out exception.
-     */
-    @Test
-    public void testGetHasResultWithTimeOutException() {
-        Assertions.assertThrows(TimeoutException.class, () -> {
-            MessageFuture messageFuture = new MessageFuture();
-            messageFuture.setRequestMessage(buildRepcMessage());
-            messageFuture.setTimeout(TIME_OUT_FIELD);
-            ExecutorService executorService = Executors.newSingleThreadExecutor();
-            CountDownLatch downLatch = new CountDownLatch(1);
-            executorService.execute(() -> {
-                try {
-                    downLatch.await();
-                    messageFuture.setResultMessage("has_result");
-                } catch (InterruptedException e) {
-
-                }
-            });
-            messageFuture.get(TIME_OUT_FIELD, TimeUnit.MILLISECONDS);
-            downLatch.countDown();
-        });
-    }
-
-    /**
-     * Test get has result with run time exception.
-     */
-    @Test
-    public void testGetHasResultWithRunTimeException() {
-        Assertions.assertThrows(RuntimeException.class, () -> {
-            MessageFuture messageFuture = new MessageFuture();
-            messageFuture.setRequestMessage(buildRepcMessage());
-            messageFuture.setTimeout(TIME_OUT_FIELD);
-            messageFuture.setResultMessage(new RuntimeException());
-            messageFuture.get(TIME_OUT_FIELD, TimeUnit.MILLISECONDS);
-        });
-    }
-
-    /**
-     * Test get has result with run time exception with message.
-     *
-     * @throws Exception the exception
-     */
-    @Test
-    public void testGetHasResultWithRunTimeExceptionWithMessage() throws Exception {
-        MessageFuture messageFuture = new MessageFuture();
-        messageFuture.setRequestMessage(buildRepcMessage());
-        messageFuture.setTimeout(TIME_OUT_FIELD);
-        RuntimeException runtimeException = new RuntimeException("test_runtime");
-        messageFuture.setResultMessage(runtimeException);
-        try {
-            messageFuture.get(TIME_OUT_FIELD, TimeUnit.MILLISECONDS);
-        } catch (Exception e) {
-            assertThat(e.getMessage()).isEqualTo(runtimeException.getMessage());
-        }
-    }
-
-    /**
-     * Test get has result with throwable.
-     */
-    @Test
-    public void testGetHasResultWithThrowable() {
-        Assertions.assertThrows(RuntimeException.class, () -> {
-            MessageFuture messageFuture = new MessageFuture();
-            messageFuture.setRequestMessage(buildRepcMessage());
-            messageFuture.setTimeout(TIME_OUT_FIELD);
-            messageFuture.setResultMessage(new Throwable());
-            messageFuture.get(TIME_OUT_FIELD, TimeUnit.MILLISECONDS);
-        });
-    }
-
-    /**
-     * Test get has result with throwable with message.
-     *
-     * @throws Exception the exception
-     */
-    @Test
-    public void testGetHasResultWithThrowableWithMessage() throws Exception {
-        MessageFuture messageFuture = new MessageFuture();
-        messageFuture.setRequestMessage(buildRepcMessage());
-        messageFuture.setTimeout(TIME_OUT_FIELD);
-        Throwable throwable = new Throwable("test_throwable");
-        messageFuture.setResultMessage(throwable);
-        try {
-            messageFuture.get(TIME_OUT_FIELD, TimeUnit.MILLISECONDS);
-        } catch (Exception e) {
-            assertThat(e.getMessage()).isEqualTo(new RuntimeException(throwable).getMessage());
-        }
-    }
-
-    private RpcMessage buildRepcMessage() {
-        RpcMessage rpcMessage = new RpcMessage();
-        rpcMessage.setId(ID_FIELD);
-        rpcMessage.setMessageType(MSG_TYPE_FIELD);
-        rpcMessage.setCodec(CODEC_FIELD);
-        rpcMessage.setCompressor(COMPRESS_FIELD);
-        rpcMessage.setBody(BODY_FIELD);
-        return rpcMessage;
-    }
-}
diff --git a/core/src/test/java/io/seata/core/rpc/netty/TmNettyClientTest.java b/core/src/test/java/io/seata/core/rpc/netty/TmNettyClientTest.java
deleted file mode 100644
index 7b73fdfa..00000000
--- a/core/src/test/java/io/seata/core/rpc/netty/TmNettyClientTest.java
+++ /dev/null
@@ -1,147 +0,0 @@
-/*
- *  Copyright 1999-2019 Seata.io Group.
- *
- *  Licensed under the Apache License, Version 2.0 (the "License");
- *  you may not use this file except in compliance with the License.
- *  You may obtain a copy of the License at
- *
- *       http://www.apache.org/licenses/LICENSE-2.0
- *
- *  Unless required by applicable law or agreed to in writing, software
- *  distributed under the License is distributed on an "AS IS" BASIS,
- *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- *  See the License for the specific language governing permissions and
- *  limitations under the License.
- */
-package io.seata.core.rpc.netty;
-
-import io.netty.bootstrap.Bootstrap;
-import io.netty.channel.Channel;
-import io.netty.channel.ChannelFactory;
-import io.netty.channel.ChannelOption;
-import io.netty.channel.socket.nio.NioSocketChannel;
-import org.apache.commons.pool.impl.GenericKeyedObjectPool;
-import org.junit.jupiter.api.Assertions;
-import org.junit.jupiter.api.Test;
-
-import java.lang.reflect.Field;
-import java.util.Map;
-import java.util.concurrent.LinkedBlockingQueue;
-import java.util.concurrent.ThreadPoolExecutor;
-import java.util.concurrent.TimeUnit;
-
-/**
- * The type Tm rpc client test.
- *
- * @<NAME_EMAIL>
- */
-public class TmNettyClientTest {
-
-    private static final ThreadPoolExecutor
-        workingThreads = new ThreadPoolExecutor(100, 500, 500, TimeUnit.SECONDS,
-        new LinkedBlockingQueue<>(20000), new ThreadPoolExecutor.CallerRunsPolicy());
-
-    /**
-     * Test get instance.
-     *
-     * @throws Exception the exceptionDataSourceManager.
-     */
-    @Test
-    public void testGetInstance() throws Exception {
-        String applicationId = "app 1";
-        String transactionServiceGroup = "group A";
-        TmNettyRemotingClient tmNettyRemotingClient = TmNettyRemotingClient.getInstance(applicationId, transactionServiceGroup);
-        Field nettyClientKeyPoolField = getDeclaredField(tmNettyRemotingClient.getClientChannelManager(), "nettyClientKeyPool");
-        nettyClientKeyPoolField.setAccessible(true);
-        GenericKeyedObjectPool nettyClientKeyPool = (GenericKeyedObjectPool) nettyClientKeyPoolField.get(tmNettyRemotingClient.getClientChannelManager());
-        NettyClientConfig defaultNettyClientConfig = new NettyClientConfig();
-        Assertions.assertEquals(defaultNettyClientConfig.getMaxPoolActive(), nettyClientKeyPool.getMaxActive());
-        Assertions.assertEquals(defaultNettyClientConfig.getMinPoolIdle(), nettyClientKeyPool.getMinIdle());
-        Assertions.assertEquals(defaultNettyClientConfig.getMaxAcquireConnMills(), nettyClientKeyPool.getMaxWait());
-        Assertions.assertEquals(defaultNettyClientConfig.isPoolTestBorrow(), nettyClientKeyPool.getTestOnBorrow());
-        Assertions.assertEquals(defaultNettyClientConfig.isPoolTestReturn(), nettyClientKeyPool.getTestOnReturn());
-        Assertions.assertEquals(defaultNettyClientConfig.isPoolLifo(), nettyClientKeyPool.getLifo());
-    }
-
-    /**
-     * Do connect.
-     *
-     * @throws Exception the exception
-     */
-    @Test
-    public void testInit() throws Exception {
-        String applicationId = "app 1";
-        String transactionServiceGroup = "group A";
-        TmNettyRemotingClient tmNettyRemotingClient = TmNettyRemotingClient.getInstance(applicationId, transactionServiceGroup);
-
-        tmNettyRemotingClient.init();
-
-        //check if attr of tmNettyClient object has been set success
-        Field clientBootstrapField = getDeclaredField(tmNettyRemotingClient, "clientBootstrap");
-        clientBootstrapField.setAccessible(true);
-        NettyClientBootstrap clientBootstrap = (NettyClientBootstrap)clientBootstrapField.get(tmNettyRemotingClient);
-        Field bootstrapField = getDeclaredField(clientBootstrap, "bootstrap");
-        bootstrapField.setAccessible(true);
-        Bootstrap bootstrap = (Bootstrap) bootstrapField.get(clientBootstrap);
-
-        Assertions.assertNotNull(bootstrap);
-        Field optionsField = getDeclaredField(bootstrap, "options");
-        optionsField.setAccessible(true);
-        Map<ChannelOption<?>, Object> options = (Map<ChannelOption<?>, Object>)optionsField.get(bootstrap);
-        Assertions.assertEquals(Boolean.TRUE, options.get(ChannelOption.TCP_NODELAY));
-        Assertions.assertEquals(Boolean.TRUE, options.get(ChannelOption.SO_KEEPALIVE));
-        Assertions.assertEquals(10000, options.get(ChannelOption.CONNECT_TIMEOUT_MILLIS));
-        Assertions.assertEquals(Boolean.TRUE, options.get(ChannelOption.SO_KEEPALIVE));
-        Assertions.assertEquals(153600, options.get(ChannelOption.SO_RCVBUF));
-
-        Field channelFactoryField = getDeclaredField(bootstrap, "channelFactory");
-        channelFactoryField.setAccessible(true);
-        ChannelFactory<? extends Channel>
-            channelFactory = (ChannelFactory<? extends Channel>)channelFactoryField.get(bootstrap);
-        Assertions.assertNotNull(channelFactory);
-        Assertions.assertTrue(channelFactory.newChannel() instanceof NioSocketChannel);
-
-    }
-
-    /**
-     * Gets application id.
-     *
-     * @throws Exception the exception
-     */
-    @Test
-    public void getApplicationId() throws Exception {
-
-    }
-
-    /**
-     * Sets application id.
-     *
-     * @throws Exception the exception
-     */
-    @Test
-    public void setApplicationId() throws Exception {
-
-    }
-
-    /**
-     * get private field in parent class
-     *
-     * @param object    the object
-     * @param fieldName the field name
-     * @return declared field
-     */
-    public static Field getDeclaredField(Object object, String fieldName) {
-        Field field = null;
-        Class<?> clazz = object.getClass();
-        for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
-            try {
-                field = clazz.getDeclaredField(fieldName);
-                return field;
-            } catch (Exception e) {
-
-            }
-        }
-
-        return null;
-    }
-}
diff --git a/integration/motan/src/test/java/io/seata/integration/motan/MotanTransactionFilterTest.java b/integration/motan/src/test/java/io/seata/integration/motan/MotanTransactionFilterTest.java
deleted file mode 100644
index ef054422..00000000
--- a/integration/motan/src/test/java/io/seata/integration/motan/MotanTransactionFilterTest.java
+++ /dev/null
@@ -1,81 +0,0 @@
-/*
- *  Copyright 1999-2019 Seata.io Group.
- *
- *  Licensed under the Apache License, Version 2.0 (the "License");
- *  you may not use this file except in compliance with the License.
- *  You may obtain a copy of the License at
- *
- *       http://www.apache.org/licenses/LICENSE-2.0
- *
- *  Unless required by applicable law or agreed to in writing, software
- *  distributed under the License is distributed on an "AS IS" BASIS,
- *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- *  See the License for the specific language governing permissions and
- *  limitations under the License.
- */
-package io.seata.integration.motan;
-
-import com.weibo.api.motan.config.ProtocolConfig;
-import com.weibo.api.motan.config.RefererConfig;
-import com.weibo.api.motan.config.RegistryConfig;
-import com.weibo.api.motan.config.ServiceConfig;
-import io.seata.core.context.RootContext;
-import org.junit.jupiter.api.Assertions;
-import org.junit.jupiter.api.Test;
-
-/**
- * <AUTHOR>
- */
-class MotanTransactionFilterTest {
-
-    private static final String SERVICE_GROUP = "motan";
-    private static final String SERVICE_VERSION = "1.0.0";
-    private static final int SERVICE_PORT = 8004;
-    private static final String PROTOCOL_ID = "motan";
-    private static final String PROTOCOL_NAME = "motan";
-    private static final String XID = "127.0.0.1:8091:87654321";
-    private static final int REQUEST_TIMEOUT = 1000;
-
-    @Test
-    void testGetProviderXID() {
-        RootContext.bind(XID);
-        providerStart();
-        consumerStart();
-        RootContext.unbind();
-    }
-
-    public void providerStart() {
-        ServiceConfig<XIDService> serviceConfig = new ServiceConfig<>();
-        serviceConfig.setInterface(XIDService.class);
-        serviceConfig.setRef(new XIDServiceImpl());
-        serviceConfig.setGroup(SERVICE_GROUP);
-        serviceConfig.setVersion(SERVICE_VERSION);
-        RegistryConfig registryConfig = new RegistryConfig();
-        registryConfig.setRegProtocol("local");
-        registryConfig.setCheck(false);
-        serviceConfig.setRegistry(registryConfig);
-        ProtocolConfig protocol = new ProtocolConfig();
-        protocol.setId(PROTOCOL_ID);
-        protocol.setName(PROTOCOL_NAME);
-        serviceConfig.setProtocol(protocol);
-        serviceConfig.setExport("motan:" + SERVICE_PORT);
-        serviceConfig.export();
-    }
-
-    private void consumerStart() {
-        RefererConfig<XIDService> refererConfig = new RefererConfig<>();
-        refererConfig.setInterface(XIDService.class);
-        refererConfig.setGroup(SERVICE_GROUP);
-        refererConfig.setVersion(SERVICE_VERSION);
-        refererConfig.setRequestTimeout(REQUEST_TIMEOUT);
-        RegistryConfig registry = new RegistryConfig();
-        refererConfig.setRegistry(registry);
-        ProtocolConfig protocol = new ProtocolConfig();
-        protocol.setId(PROTOCOL_ID);
-        protocol.setName(PROTOCOL_NAME);
-        refererConfig.setProtocol(protocol);
-        refererConfig.setDirectUrl("localhost:" + SERVICE_PORT);
-        XIDService service = refererConfig.getRef();
-        Assertions.assertEquals(service.getXid(), XID);
-    }
-}
diff --git a/server/src/test/java/io/seata/server/coordinator/DefaultCoordinatorTest.java b/server/src/test/java/io/seata/server/coordinator/DefaultCoordinatorTest.java
deleted file mode 100644
index 18904236..00000000
--- a/server/src/test/java/io/seata/server/coordinator/DefaultCoordinatorTest.java
+++ /dev/null
@@ -1,287 +0,0 @@
-/*
- *  Copyright 1999-2019 Seata.io Group.
- *
- *  Licensed under the Apache License, Version 2.0 (the "License");
- *  you may not use this file except in compliance with the License.
- *  You may obtain a copy of the License at
- *
- *       http://www.apache.org/licenses/LICENSE-2.0
- *
- *  Unless required by applicable law or agreed to in writing, software
- *  distributed under the License is distributed on an "AS IS" BASIS,
- *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- *  See the License for the specific language governing permissions and
- *  limitations under the License.
- */
-package io.seata.server.coordinator;
-
-import java.io.IOException;
-import java.util.Collection;
-import java.util.concurrent.ExecutorService;
-import java.util.concurrent.TimeUnit;
-import java.util.concurrent.TimeoutException;
-import java.util.stream.Stream;
-
-import io.netty.channel.Channel;
-import io.seata.common.DefaultValues;
-import io.seata.common.XID;
-import io.seata.common.loader.EnhancedServiceLoader;
-import io.seata.common.util.NetUtil;
-import io.seata.common.util.ReflectionUtil;
-import io.seata.config.Configuration;
-import io.seata.config.ConfigurationFactory;
-import io.seata.core.constants.ConfigurationKeys;
-import io.seata.core.exception.TransactionException;
-import io.seata.core.model.BranchStatus;
-import io.seata.core.model.BranchType;
-import io.seata.core.protocol.RpcMessage;
-import io.seata.core.protocol.transaction.BranchCommitRequest;
-import io.seata.core.protocol.transaction.BranchCommitResponse;
-import io.seata.core.protocol.transaction.BranchRollbackRequest;
-import io.seata.core.protocol.transaction.BranchRollbackResponse;
-import io.seata.core.rpc.RemotingServer;
-import io.seata.core.rpc.processor.RemotingProcessor;
-import io.seata.server.metrics.MetricsManager;
-import io.seata.server.session.GlobalSession;
-import io.seata.server.session.SessionHolder;
-import io.seata.server.store.StoreConfig.SessionMode;
-import io.seata.server.util.StoreUtil;
-import org.junit.jupiter.api.AfterAll;
-import org.junit.jupiter.api.AfterEach;
-import org.junit.jupiter.api.Assertions;
-import org.junit.jupiter.api.BeforeAll;
-import org.junit.jupiter.api.BeforeEach;
-import org.junit.jupiter.api.Disabled;
-import org.junit.jupiter.api.Test;
-import org.junit.jupiter.api.condition.DisabledOnJre;
-import org.junit.jupiter.api.condition.JRE;
-import org.junit.jupiter.params.ParameterizedTest;
-import org.junit.jupiter.params.provider.Arguments;
-import org.junit.jupiter.params.provider.MethodSource;
-import org.springframework.boot.test.context.SpringBootTest;
-import org.springframework.context.ApplicationContext;
-
-/**
- * The type DefaultCoordinator test.
- *
- * <AUTHOR>
- */
-@SpringBootTest
-public class DefaultCoordinatorTest {
-    private static DefaultCoordinator defaultCoordinator;
-
-    private static final String applicationId = "demo-child-app";
-
-    private static final String txServiceGroup = "default_tx_group";
-
-    private static final String txName = "tx-1";
-
-    private static final int timeout = 3000;
-
-    private static final String resourceId = "tb_1";
-
-    private static final String clientId = "c_1";
-
-    private static final String lockKeys_1 = "tb_1:11";
-
-    private static final String lockKeys_2 = "tb_1:12";
-
-    private static final String applicationData = "{\"data\":\"test\"}";
-
-    private static DefaultCore core;
-
-    private static final Configuration CONFIG = ConfigurationFactory.getInstance();
-
-    @BeforeAll
-    public static void beforeClass(ApplicationContext context) throws Exception {
-        EnhancedServiceLoader.unload(AbstractCore.class);
-        XID.setIpAddress(NetUtil.getLocalIp());
-        RemotingServer remotingServer = new MockServerMessageSender();
-        defaultCoordinator =DefaultCoordinator.getInstance(remotingServer);
-        defaultCoordinator.setRemotingServer(remotingServer);
-        core = new DefaultCore(remotingServer);
-    }
-
-    @BeforeEach
-    public void tearUp() throws IOException {
-        deleteAndCreateDataFile();
-    }
-
-    @Test
-    public void branchCommit() throws TransactionException {
-        BranchStatus result = null;
-        String xid = null;
-        GlobalSession globalSession = null;
-        try {
-            xid = core.begin(applicationId, txServiceGroup, txName, timeout);
-            Long branchId = core.branchRegister(BranchType.AT, resourceId, clientId, xid, applicationData, lockKeys_1);
-            globalSession = SessionHolder.findGlobalSession(xid);
-            result = core.branchCommit(globalSession, globalSession.getBranch(branchId));
-        } catch (TransactionException e) {
-            Assertions.fail(e.getMessage());
-        }
-        Assertions.assertEquals(result, BranchStatus.PhaseTwo_Committed);
-        globalSession = SessionHolder.findGlobalSession(xid);
-        Assertions.assertNotNull(globalSession);
-        globalSession.end();
-    }
-
-    @Disabled
-    @ParameterizedTest
-    @MethodSource("xidAndBranchIdProviderForRollback")
-    public void branchRollback(String xid, Long branchId) {
-        BranchStatus result = null;
-        GlobalSession globalSession = SessionHolder.findGlobalSession(xid);
-        try {
-            result = core.branchRollback(globalSession, globalSession.getBranch(branchId));
-        } catch (TransactionException e) {
-            Assertions.fail(e.getMessage());
-        }
-        Assertions.assertEquals(result, BranchStatus.PhaseTwo_Rollbacked);
-    }
-
-
-    @Test
-    public void test_handleRetryRollbacking() throws TransactionException, InterruptedException {
-
-        String xid = core.begin(applicationId, txServiceGroup, txName, 10);
-        Long branchId = core.branchRegister(BranchType.AT, "abcd", clientId, xid, applicationData, lockKeys_2);
-
-        Assertions.assertNotNull(branchId);
-
-        Thread.sleep(100);
-        defaultCoordinator.timeoutCheck();
-        defaultCoordinator.handleRetryRollbacking();
-
-        GlobalSession globalSession = SessionHolder.findGlobalSession(xid);
-        Assertions.assertNull(globalSession);
-
-    }
-
-    @Test
-    @DisabledOnJre(JRE.JAVA_17) // `ReflectionUtil.modifyStaticFinalField` does not supported java17
-    public void test_handleRetryRollbackingTimeOut() throws TransactionException, InterruptedException, NoSuchFieldException, IllegalAccessException {
-        String xid = core.begin(applicationId, txServiceGroup, txName, 10);
-        Long branchId = core.branchRegister(BranchType.AT, "abcd", clientId, xid, applicationData, lockKeys_2);
-
-        GlobalSession globalSession = SessionHolder.findGlobalSession(xid);
-        Assertions.assertNotNull(globalSession);
-        Assertions.assertNotNull(globalSession.getBranchSessions());
-        Assertions.assertNotNull(branchId);
-
-        ReflectionUtil.modifyStaticFinalField(defaultCoordinator.getClass(), "MAX_ROLLBACK_RETRY_TIMEOUT", 10L);
-        ReflectionUtil.modifyStaticFinalField(defaultCoordinator.getClass(), "ROLLBACK_RETRY_TIMEOUT_UNLOCK_ENABLE", false);
-        TimeUnit.MILLISECONDS.sleep(100);
-        globalSession.queueToRetryRollback();
-        defaultCoordinator.handleRetryRollbacking();
-        int lockSize = globalSession.getBranchSessions().get(0).getLockHolder().size();
-        try {
-            Assertions.assertTrue(lockSize > 0);
-        } finally {
-            globalSession.closeAndClean();
-            ReflectionUtil.modifyStaticFinalField(defaultCoordinator.getClass(), "MAX_ROLLBACK_RETRY_TIMEOUT",
-                ConfigurationFactory.getInstance().getLong(ConfigurationKeys.MAX_ROLLBACK_RETRY_TIMEOUT, DefaultValues.DEFAULT_MAX_ROLLBACK_RETRY_TIMEOUT));
-        }
-    }
-
-    @Test
-    @DisabledOnJre(JRE.JAVA_17) // `ReflectionUtil.modifyStaticFinalField` does not supported java17
-    public void test_handleRetryRollbackingTimeOut_unlock() throws TransactionException, InterruptedException,
-        NoSuchFieldException, IllegalAccessException {
-        String xid = core.begin(applicationId, txServiceGroup, txName, 10);
-        Long branchId = core.branchRegister(BranchType.AT, "abcd", clientId, xid, applicationData, lockKeys_2);
-
-        GlobalSession globalSession = SessionHolder.findGlobalSession(xid);
-        Assertions.assertNotNull(globalSession);
-        Assertions.assertNotNull(globalSession.getBranchSessions());
-        Assertions.assertNotNull(branchId);
-
-        ReflectionUtil.modifyStaticFinalField(defaultCoordinator.getClass(), "MAX_ROLLBACK_RETRY_TIMEOUT", 10L);
-        ReflectionUtil.modifyStaticFinalField(defaultCoordinator.getClass(), "ROLLBACK_RETRY_TIMEOUT_UNLOCK_ENABLE", true);
-        TimeUnit.MILLISECONDS.sleep(100);
-
-        globalSession.queueToRetryRollback();
-        defaultCoordinator.handleRetryRollbacking();
-
-        int lockSize = globalSession.getBranchSessions().get(0).getLockHolder().size();
-        try {
-            Assertions.assertTrue(lockSize == 0);
-        } finally {
-            globalSession.closeAndClean();
-            ReflectionUtil.modifyStaticFinalField(defaultCoordinator.getClass(), "MAX_ROLLBACK_RETRY_TIMEOUT",
-                ConfigurationFactory.getInstance().getLong(ConfigurationKeys.MAX_ROLLBACK_RETRY_TIMEOUT, DefaultValues.DEFAULT_MAX_ROLLBACK_RETRY_TIMEOUT));
-        }
-    }
-
-    @AfterAll
-    public static void afterClass() throws Exception {
-
-        Collection<GlobalSession> globalSessions = SessionHolder.getRootSessionManager().allSessions();
-        Collection<GlobalSession> asyncGlobalSessions = SessionHolder.getRootSessionManager().allSessions();
-        for (GlobalSession asyncGlobalSession : asyncGlobalSessions) {
-            asyncGlobalSession.closeAndClean();
-        }
-        for (GlobalSession globalSession : globalSessions) {
-            globalSession.closeAndClean();
-        }
-    }
-
-    private static void deleteAndCreateDataFile() throws IOException {
-        StoreUtil.deleteDataFile();
-        SessionHolder.init(SessionMode.FILE);
-    }
-
-    @AfterEach
-    public void tearDown() throws IOException {
-        MetricsManager.get().getRegistry().clearUp();
-        StoreUtil.deleteDataFile();
-    }
-
-    static Stream<Arguments> xidAndBranchIdProviderForRollback() throws Exception {
-        String xid = core.begin(applicationId, txServiceGroup, txName, timeout);
-        Long branchId = core.branchRegister(BranchType.AT, resourceId, clientId, xid, applicationData, lockKeys_2);
-        return Stream.of(
-            Arguments.of(xid, branchId)
-        );
-    }
-
-
-    public static class MockServerMessageSender implements RemotingServer {
-
-        @Override
-        public Object sendSyncRequest(String resourceId, String clientId, Object message, boolean tryOtherApp)
-            throws TimeoutException {
-            if (message instanceof BranchCommitRequest) {
-                final BranchCommitResponse branchCommitResponse = new BranchCommitResponse();
-                branchCommitResponse.setBranchStatus(BranchStatus.PhaseTwo_Committed);
-                return branchCommitResponse;
-            } else if (message instanceof BranchRollbackRequest) {
-                final BranchRollbackResponse branchRollbackResponse = new BranchRollbackResponse();
-                branchRollbackResponse.setBranchStatus(BranchStatus.PhaseTwo_Rollbacked);
-                return branchRollbackResponse;
-            } else {
-                return null;
-            }
-        }
-
-        @Override
-        public Object sendSyncRequest(Channel clientChannel, Object message) throws TimeoutException {
-            return null;
-        }
-
-        @Override
-        public void sendAsyncRequest(Channel channel, Object msg) {
-
-        }
-
-        @Override
-        public void sendAsyncResponse(RpcMessage request, Channel channel, Object msg) {
-
-        }
-
-        @Override
-        public void registerProcessor(int messageType, RemotingProcessor processor, ExecutorService executor) {
-
-        }
-    }
-}
\ No newline at end of file
diff --git a/server/src/test/java/io/seata/server/event/DefaultCoreForEventBusTest.java b/server/src/test/java/io/seata/server/event/DefaultCoreForEventBusTest.java
deleted file mode 100644
index bb9c4a15..00000000
--- a/server/src/test/java/io/seata/server/event/DefaultCoreForEventBusTest.java
+++ /dev/null
@@ -1,166 +0,0 @@
-/*
- *  Copyright 1999-2019 Seata.io Group.
- *
- *  Licensed under the Apache License, Version 2.0 (the "License");
- *  you may not use this file except in compliance with the License.
- *  You may obtain a copy of the License at
- *
- *       http://www.apache.org/licenses/LICENSE-2.0
- *
- *  Unless required by applicable law or agreed to in writing, software
- *  distributed under the License is distributed on an "AS IS" BASIS,
- *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- *  See the License for the specific language governing permissions and
- *  limitations under the License.
- */
-package io.seata.server.event;
-
-import java.io.IOException;
-import java.util.Map;
-import java.util.Optional;
-import java.util.concurrent.ConcurrentHashMap;
-import java.util.concurrent.CountDownLatch;
-import java.util.concurrent.TimeUnit;
-import java.util.concurrent.atomic.AtomicInteger;
-
-import com.google.common.eventbus.AllowConcurrentEvents;
-import com.google.common.eventbus.Subscribe;
-import io.seata.core.event.GlobalTransactionEvent;
-import io.seata.core.exception.TransactionException;
-import io.seata.core.model.GlobalStatus;
-import io.seata.core.rpc.RemotingServer;
-import io.seata.metrics.registry.Registry;
-import io.seata.server.coordinator.DefaultCoordinator;
-import io.seata.server.coordinator.DefaultCoordinatorTest;
-import io.seata.server.coordinator.DefaultCore;
-import io.seata.server.metrics.MetricsManager;
-import io.seata.server.session.SessionHolder;
-import io.seata.server.store.StoreConfig;
-import io.seata.server.store.StoreConfig.SessionMode;
-import io.seata.server.util.StoreUtil;
-import org.junit.jupiter.api.AfterAll;
-import org.junit.jupiter.api.Assertions;
-import org.junit.jupiter.api.BeforeAll;
-import org.junit.jupiter.api.Test;
-import org.springframework.boot.test.context.SpringBootTest;
-import org.springframework.context.ApplicationContext;
-
-/**
- * Test events come from Default Core.
- *
- * <AUTHOR>
- */
-@SpringBootTest
-public class DefaultCoreForEventBusTest {
-
-    private static final boolean DELAY_HANDLE_SESSION = StoreConfig.getSessionMode() != SessionMode.FILE;
-
-    @BeforeAll
-    public static void setUp(ApplicationContext context) throws InterruptedException {
-        StoreUtil.deleteDataFile();
-        Thread.sleep(5000);
-    }
-
-    @Test
-    public void test() throws IOException, TransactionException, InterruptedException {
-        class GlobalTransactionEventSubscriber {
-            private final Map<String, AtomicInteger> eventCounters;
-            private CountDownLatch downLatch;
-
-            public Map<String, AtomicInteger> getEventCounters() {
-                return eventCounters;
-            }
-
-            public GlobalTransactionEventSubscriber() {
-                this.eventCounters = new ConcurrentHashMap<>();
-            }
-
-            @Subscribe
-            @AllowConcurrentEvents
-            public void processGlobalTransactionEvent(GlobalTransactionEvent event) {
-                AtomicInteger counter = eventCounters.computeIfAbsent(event.getStatus(),
-                        status -> new AtomicInteger(0));
-                counter.addAndGet(1);
-                //System.out.println("current status:" + event.getName() + "," + event.getStatus() + "," + eventCounters.size());
-                if (null != downLatch) {
-                    downLatch.countDown();
-                }
-            }
-
-            public void setDownLatch(CountDownLatch countDownLatch) {
-                this.downLatch = countDownLatch;
-            }
-
-            public CountDownLatch getDownLatch() {
-                return downLatch;
-            }
-
-            public void resetDownLatch() {
-                if (null != downLatch) {
-                    downLatch = null;
-                }
-            }
-        }
-        RemotingServer remotingServer = new DefaultCoordinatorTest.MockServerMessageSender();
-        DefaultCoordinator coordinator = DefaultCoordinator.getInstance(remotingServer);
-        coordinator.init();
-        GlobalTransactionEventSubscriber subscriber = null;
-        try {
-            DefaultCore core = new DefaultCore(remotingServer);
-            SessionHolder.init(null);
-            subscriber = new GlobalTransactionEventSubscriber();
-            EventBusManager.get().unregisterAll();
-            EventBusManager.get().register(subscriber);
-
-            //start and commit a transaction
-            subscriber.setDownLatch(new CountDownLatch(DELAY_HANDLE_SESSION ? 3 : 4));
-            String xid = core.begin("test_app_id", "default_group", "test_tran_name", 30000);
-            core.commit(xid);
-
-
-            //we need sleep for a short while because default canBeCommittedAsync() is true
-            subscriber.getDownLatch().await();
-            Assertions.assertEquals(1, subscriber.getEventCounters().get(GlobalStatus.Begin.name()).get());
-            Assertions.assertEquals(1, subscriber.getEventCounters().get(GlobalStatus.AsyncCommitting.name()).get());
-            // after event and sync event
-            Assertions.assertEquals(DELAY_HANDLE_SESSION ? 1 : 2,
-                subscriber.getEventCounters().get(GlobalStatus.Committed.name()).get());
-
-            //start and rollback transaction
-            subscriber.setDownLatch(new CountDownLatch(3));
-            xid = core.begin("test_app_id", "default_group", "test_tran_name2", 30000);
-            core.rollback(xid);
-            //sleep for retryRollback
-            Thread.sleep(1500);
-            //check
-            subscriber.getDownLatch().await();
-            Assertions.assertEquals(2, subscriber.getEventCounters().get(GlobalStatus.Begin.name()).get());
-            //Because of the delayed deletion of GlobalSession, and without changing the status of the Session,
-            Assertions.assertEquals(1, subscriber.getEventCounters().get(GlobalStatus.Rollbacking.name()).get());
-            Assertions.assertNotNull(subscriber.getEventCounters().get(GlobalStatus.Rollbacked.name()));
-
-            //start more one new transaction for test timeout and let this transaction immediately timeout
-            subscriber.setDownLatch(new CountDownLatch(1));
-            core.begin("test_app_id", "default_group", "test_tran_name3", 0);
-
-            //sleep for check ->  DefaultCoordinator.timeoutCheck
-            Thread.sleep(2000);
-
-            //at lease retry once because DefaultCoordinator.timeoutCheck is 1 second
-            subscriber.downLatch.await(5000, TimeUnit.MILLISECONDS);
-            Assertions.assertTrue(subscriber.getEventCounters().get(GlobalStatus.TimeoutRollbacking.name()).get() >= 1);
-        } finally {
-            // call SpringContextShutdownHook
-            if (null != subscriber) {
-                EventBusManager.get().unregister(subscriber);
-            }
-        }
-    }
-
-    @AfterAll
-    public static void setDown() throws InterruptedException {
-        Optional.ofNullable(DefaultCoordinator.getInstance()).ifPresent(DefaultCoordinator::destroy);
-        Optional.ofNullable(MetricsManager.get().getRegistry()).ifPresent(Registry::clearUp);
-    }
-
-}
diff --git a/server/src/test/java/io/seata/server/store/SessionStoreTest.java b/server/src/test/java/io/seata/server/store/SessionStoreTest.java
deleted file mode 100644
index 9e61b21c..00000000
--- a/server/src/test/java/io/seata/server/store/SessionStoreTest.java
+++ /dev/null
@@ -1,382 +0,0 @@
-/*
- *  Copyright 1999-2019 Seata.io Group.
- *
- *  Licensed under the Apache License, Version 2.0 (the "License");
- *  you may not use this file except in compliance with the License.
- *  You may obtain a copy of the License at
- *
- *       http://www.apache.org/licenses/LICENSE-2.0
- *
- *  Unless required by applicable law or agreed to in writing, software
- *  distributed under the License is distributed on an "AS IS" BASIS,
- *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- *  See the License for the specific language governing permissions and
- *  limitations under the License.
- */
-package io.seata.server.store;
-
-import java.io.File;
-
-import io.seata.common.XID;
-import io.seata.config.Configuration;
-import io.seata.config.ConfigurationFactory;
-import io.seata.core.constants.ConfigurationKeys;
-import io.seata.core.model.BranchStatus;
-import io.seata.core.model.BranchType;
-import io.seata.core.model.GlobalStatus;
-import io.seata.server.lock.LockManager;
-import io.seata.server.lock.file.FileLockManagerForTest;
-import io.seata.server.session.BranchSession;
-import io.seata.server.session.GlobalSession;
-import io.seata.server.session.SessionHelper;
-import io.seata.server.session.SessionHolder;
-import io.seata.server.store.StoreConfig.SessionMode;
-import org.junit.jupiter.api.Assertions;
-import org.junit.jupiter.api.BeforeAll;
-import org.junit.jupiter.api.BeforeEach;
-import org.junit.jupiter.api.Test;
-import org.springframework.boot.test.context.SpringBootTest;
-import org.springframework.context.ApplicationContext;
-
-import static io.seata.common.DefaultValues.DEFAULT_TX_GROUP;
-
-/**
- * The type Session store test.
- */
-@SpringBootTest
-public class SessionStoreTest {
-
-    @BeforeAll
-    public static void setUp(ApplicationContext context) {
-
-    }
-
-    /**
-     * The constant RESOURCE_ID.
-     */
-    public static final String RESOURCE_ID = "mysql:xxx";
-
-    private static Configuration CONFIG = ConfigurationFactory.getInstance();
-
-    /**
-     * Clean.
-     *
-     * @throws Exception the exception
-     */
-    @BeforeEach
-    public void clean() throws Exception {
-        String sessionStorePath = CONFIG.getConfig(ConfigurationKeys.STORE_FILE_DIR);
-        File rootDataFile = new File(sessionStorePath + File.separator + SessionHolder.ROOT_SESSION_MANAGER_NAME);
-        File rootDataFileHis = new File(
-            sessionStorePath + File.separator + SessionHolder.ROOT_SESSION_MANAGER_NAME + ".1");
-
-        if (rootDataFile.exists()) {
-            rootDataFile.delete();
-        }
-        if (rootDataFileHis.exists()) {
-            rootDataFileHis.delete();
-        }
-        LockManager lockManager = new FileLockManagerForTest();
-        lockManager.cleanAllLocks();
-    }
-
-    /**
-     * Test restored from file.
-     *
-     * @throws Exception the exception
-     */
-    @Test
-    public void testRestoredFromFile() throws Exception {
-        try {
-            SessionHolder.init(SessionMode.FILE);
-            GlobalSession globalSession = new GlobalSession("demo-app", DEFAULT_TX_GROUP, "test", 6000);
-            String xid = XID.generateXID(globalSession.getTransactionId());
-            globalSession.setXid(xid);
-
-            globalSession.begin();
-
-            BranchSession branchSession1 = SessionHelper.newBranchByGlobal(globalSession, BranchType.AT, RESOURCE_ID,
-                "ta:1,2;tb:3", "xxx");
-            branchSession1.setXid(xid);
-            branchSession1.lock();
-            globalSession.addBranch(branchSession1);
-
-            LockManager lockManager = new FileLockManagerForTest();
-
-            String otherXID = XID.generateXID(0L);
-
-            Assertions.assertFalse(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:1"));
-            Assertions.assertFalse(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:2"));
-            Assertions.assertFalse(lockManager.isLockable(otherXID, RESOURCE_ID, "tb:3"));
-
-            Assertions.assertTrue(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:4"));
-            Assertions.assertTrue(lockManager.isLockable(otherXID, RESOURCE_ID, "tb:5"));
-
-            lockManager.cleanAllLocks();
-
-            Assertions.assertTrue(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:1"));
-            Assertions.assertTrue(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:2"));
-            Assertions.assertTrue(lockManager.isLockable(otherXID, RESOURCE_ID, "tb:3"));
-
-            // Re-init SessionHolder: restore sessions from file
-            SessionHolder.init(SessionMode.FILE);
-
-            long tid = globalSession.getTransactionId();
-            GlobalSession reloadSession = SessionHolder.findGlobalSession(globalSession.getXid());
-            Assertions.assertNotNull(reloadSession);
-            Assertions.assertFalse(globalSession == reloadSession);
-            Assertions.assertEquals(globalSession.getApplicationId(), reloadSession.getApplicationId());
-
-            Assertions.assertFalse(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:1"));
-            Assertions.assertFalse(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:2"));
-            Assertions.assertFalse(lockManager.isLockable(otherXID, RESOURCE_ID, "tb:3"));
-            Assertions.assertTrue(lockManager.isLockable(xid, RESOURCE_ID, "tb:3"));
-
-            //clear
-            reloadSession.end();
-        } finally {
-            SessionHolder.destroy();
-        }
-    }
-
-    /**
-     * Test restored from file 2.
-     *
-     * @throws Exception the exception
-     */
-    //@Test
-    public void testRestoredFromFile2() throws Exception {
-        try {
-            SessionHolder.init(SessionMode.FILE);
-            GlobalSession globalSession = new GlobalSession("demo-app", DEFAULT_TX_GROUP, "test", 6000);
-
-            globalSession.begin();
-
-            // Re-init SessionHolder: restore sessions from file
-            SessionHolder.init(SessionMode.FILE);
-        } finally {
-            SessionHolder.destroy();
-        }
-    }
-
-    /**
-     * Test restored from file async committing.
-     *
-     * @throws Exception the exception
-     */
-    @Test
-    public void testRestoredFromFileAsyncCommitting() throws Exception {
-        try {
-            SessionHolder.init(SessionMode.FILE);
-            GlobalSession globalSession = new GlobalSession("demo-app", DEFAULT_TX_GROUP, "test", 6000);
-
-            String xid = XID.generateXID(globalSession.getTransactionId());
-            globalSession.setXid(xid);
-
-            globalSession.begin();
-
-            BranchSession branchSession1 = SessionHelper.newBranchByGlobal(globalSession, BranchType.AT, RESOURCE_ID,
-                "ta:1", "xxx");
-            Assertions.assertTrue(branchSession1.lock());
-            globalSession.addBranch(branchSession1);
-
-            LockManager lockManager = new FileLockManagerForTest();
-
-            String otherXID = XID.generateXID(0L);
-
-            Assertions.assertFalse(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:1"));
-
-            globalSession.changeGlobalStatus(GlobalStatus.AsyncCommitting);
-
-            lockManager.cleanAllLocks();
-
-            Assertions.assertTrue(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:1"));
-
-            // Re-init SessionHolder: restore sessions from file
-            SessionHolder.init(SessionMode.FILE);
-
-            long tid = globalSession.getTransactionId();
-            GlobalSession reloadSession = SessionHolder.findGlobalSession(globalSession.getXid());
-            Assertions.assertEquals(reloadSession.getStatus(), GlobalStatus.AsyncCommitting);
-
-            GlobalSession sessionInAsyncCommittingQueue = SessionHolder.getRootSessionManager()
-                .findGlobalSession(globalSession.getXid());
-            Assertions.assertTrue(reloadSession == sessionInAsyncCommittingQueue);
-
-            // No locking for session in AsyncCommitting status
-            Assertions.assertTrue(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:1"));
-
-            //clear
-            reloadSession.end();
-        } finally {
-            SessionHolder.destroy();
-        }
-    }
-
-    /**
-     * Test restored from file commit retry.
-     *
-     * @throws Exception the exception
-     */
-    @Test
-    public void testRestoredFromFileCommitRetry() throws Exception {
-        try {
-            SessionHolder.init(SessionMode.FILE);
-            GlobalSession globalSession = new GlobalSession("demo-app", DEFAULT_TX_GROUP, "test", 6000);
-
-            String xid = XID.generateXID(globalSession.getTransactionId());
-            globalSession.setXid(xid);
-
-            globalSession.begin();
-
-            BranchSession branchSession1 = SessionHelper.newBranchByGlobal(globalSession, BranchType.AT, RESOURCE_ID,
-                "ta:1", "xxx");
-            branchSession1.lock();
-            globalSession.addBranch(branchSession1);
-
-            LockManager lockManager = new FileLockManagerForTest();
-
-            String otherXID = XID.generateXID(0L);
-
-            Assertions.assertFalse(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:1"));
-
-            globalSession.changeGlobalStatus(GlobalStatus.Committing);
-            globalSession.changeBranchStatus(branchSession1, BranchStatus.PhaseTwo_CommitFailed_Retryable);
-            globalSession.changeGlobalStatus(GlobalStatus.CommitRetrying);
-
-            lockManager.cleanAllLocks();
-
-            Assertions.assertTrue(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:1"));
-
-            // Re-init SessionHolder: restore sessions from file
-            SessionHolder.init(SessionMode.FILE);
-
-            long tid = globalSession.getTransactionId();
-            GlobalSession reloadSession = SessionHolder.findGlobalSession(globalSession.getXid());
-            Assertions.assertEquals(reloadSession.getStatus(), GlobalStatus.CommitRetrying);
-
-            GlobalSession sessionInRetryCommittingQueue = SessionHolder.getRootSessionManager()
-                .findGlobalSession(globalSession.getXid());
-            Assertions.assertTrue(reloadSession == sessionInRetryCommittingQueue);
-            BranchSession reloadBranchSession = reloadSession.getBranch(branchSession1.getBranchId());
-            Assertions.assertEquals(reloadBranchSession.getStatus(), BranchStatus.PhaseTwo_CommitFailed_Retryable);
-
-            // CommitRetrying status will never hold the lock
-            Assertions.assertTrue(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:1"));
-
-            //clear
-            reloadSession.end();
-        } finally {
-            SessionHolder.destroy();
-        }
-    }
-
-    /**
-     * Test restored from file rollback retry.
-     *
-     * @throws Exception the exception
-     */
-    @Test
-    public void testRestoredFromFileRollbackRetry() throws Exception {
-        try {
-            SessionHolder.init(SessionMode.FILE);
-
-            GlobalSession globalSession = new GlobalSession("demo-app", DEFAULT_TX_GROUP, "test", 6000);
-
-            String xid = XID.generateXID(globalSession.getTransactionId());
-            globalSession.setXid(xid);
-
-            globalSession.begin();
-
-            BranchSession branchSession1 = SessionHelper.newBranchByGlobal(globalSession, BranchType.AT, RESOURCE_ID,
-                "ta:1", "xxx");
-            branchSession1.lock();
-            globalSession.addBranch(branchSession1);
-
-            LockManager lockManager = new FileLockManagerForTest();
-
-            String otherXID = XID.generateXID(0L);
-
-            Assertions.assertFalse(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:1"));
-
-            globalSession.changeGlobalStatus(GlobalStatus.Rollbacking);
-            globalSession.changeBranchStatus(branchSession1, BranchStatus.PhaseTwo_RollbackFailed_Retryable);
-            globalSession.changeGlobalStatus(GlobalStatus.RollbackRetrying);
-
-            lockManager.cleanAllLocks();
-
-            Assertions.assertTrue(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:1"));
-
-            // Re-init SessionHolder: restore sessions from file
-            SessionHolder.init(SessionMode.FILE);
-
-            long tid = globalSession.getTransactionId();
-            GlobalSession reloadSession = SessionHolder.findGlobalSession(globalSession.getXid());
-            Assertions.assertEquals(reloadSession.getStatus(), GlobalStatus.RollbackRetrying);
-
-            GlobalSession sessionInRetryRollbackingQueue = SessionHolder.getRootSessionManager()
-                .findGlobalSession(globalSession.getXid());
-            Assertions.assertTrue(reloadSession == sessionInRetryRollbackingQueue);
-            BranchSession reloadBranchSession = reloadSession.getBranch(branchSession1.getBranchId());
-            Assertions.assertEquals(reloadBranchSession.getStatus(), BranchStatus.PhaseTwo_RollbackFailed_Retryable);
-
-            // Lock is held by session in RollbackRetrying status
-            Assertions.assertFalse(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:1"));
-
-            //clear
-            reloadSession.end();
-        } finally {
-            SessionHolder.destroy();
-        }
-    }
-
-    /**
-     * Test restored from file rollback failed.
-     *
-     * @throws Exception the exception
-     */
-    @Test
-    public void testRestoredFromFileRollbackFailed() throws Exception {
-        try {
-            SessionHolder.init(SessionMode.FILE);
-
-            GlobalSession globalSession = new GlobalSession("demo-app", DEFAULT_TX_GROUP, "test", 6000);
-
-            String xid = XID.generateXID(globalSession.getTransactionId());
-            globalSession.setXid(xid);
-
-            globalSession.begin();
-
-            BranchSession branchSession1 = SessionHelper.newBranchByGlobal(globalSession, BranchType.AT, RESOURCE_ID,
-                "ta:1", "xxx");
-            branchSession1.lock();
-            globalSession.addBranch(branchSession1);
-
-            LockManager lockManager = new FileLockManagerForTest();
-
-            String otherXID = XID.generateXID(0L);
-
-            Assertions.assertFalse(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:1"));
-
-            globalSession.changeGlobalStatus(GlobalStatus.Rollbacking);
-            globalSession.changeBranchStatus(branchSession1, BranchStatus.PhaseTwo_CommitFailed_Unretryable);
-            SessionHelper.endRollbackFailed(globalSession, false);
-
-            // Lock is released.
-            Assertions.assertFalse(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:1"));
-
-            lockManager.cleanAllLocks();
-
-            Assertions.assertTrue(lockManager.isLockable(otherXID, RESOURCE_ID, "ta:1"));
-
-            // Re-init SessionHolder: restore sessions from file
-            SessionHolder.init(SessionMode.FILE);
-
-            long tid = globalSession.getTransactionId();
-            GlobalSession reloadSession = SessionHolder.findGlobalSession(globalSession.getXid());
-            Assertions.assertNull(reloadSession);
-        } finally {
-            SessionHolder.destroy();
-        }
-    }
-}
diff --git a/test/src/test/java/io/seata/core/rpc/netty/TmNettyClientTest.java b/test/src/test/java/io/seata/core/rpc/netty/TmNettyClientTest.java
deleted file mode 100644
index a2ac66f3..00000000
--- a/test/src/test/java/io/seata/core/rpc/netty/TmNettyClientTest.java
+++ /dev/null
@@ -1,155 +0,0 @@
-/*
- *  Copyright 1999-2019 Seata.io Group.
- *
- *  Licensed under the Apache License, Version 2.0 (the "License");
- *  you may not use this file except in compliance with the License.
- *  You may obtain a copy of the License at
- *
- *       http://www.apache.org/licenses/LICENSE-2.0
- *
- *  Unless required by applicable law or agreed to in writing, software
- *  distributed under the License is distributed on an "AS IS" BASIS,
- *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- *  See the License for the specific language governing permissions and
- *  limitations under the License.
- */
-package io.seata.core.rpc.netty;
-
-import java.lang.management.ManagementFactory;
-import java.util.concurrent.LinkedBlockingQueue;
-import java.util.concurrent.ThreadPoolExecutor;
-import java.util.concurrent.TimeUnit;
-
-import io.netty.channel.Channel;
-import io.seata.common.XID;
-import io.seata.common.util.NetUtil;
-import io.seata.core.protocol.ResultCode;
-import io.seata.core.protocol.transaction.BranchRegisterRequest;
-import io.seata.core.protocol.transaction.BranchRegisterResponse;
-import io.seata.saga.engine.db.AbstractServerTest;
-import io.seata.server.UUIDGenerator;
-import io.seata.server.coordinator.DefaultCoordinator;
-import io.seata.server.session.SessionHolder;
-import org.junit.jupiter.api.Assertions;
-import org.junit.jupiter.api.Test;
-
-/**
- * <AUTHOR>
- */
-public class TmNettyClientTest extends AbstractServerTest {
-
-    public static ThreadPoolExecutor initMessageExecutor() {
-        return new ThreadPoolExecutor(100, 500, 500, TimeUnit.SECONDS,
-                new LinkedBlockingQueue(20000), new ThreadPoolExecutor.CallerRunsPolicy());
-    }
-
-    /**
-     * Client rely on server's starting first
-     *
-     * @throws Exception
-     */
-    @Test
-    public void testDoConnect() throws Exception {
-        ThreadPoolExecutor workingThreads = initMessageExecutor();
-        NettyRemotingServer nettyRemotingServer = new NettyRemotingServer(workingThreads);
-        //start services server first
-        Thread thread = new Thread(() -> {
-            nettyRemotingServer.setHandler(DefaultCoordinator.getInstance(nettyRemotingServer));
-            // set registry
-            XID.setIpAddress(NetUtil.getLocalIp());
-            XID.setPort(8091);
-            // init snowflake for transactionId, branchId
-            UUIDGenerator.init(1L);
-            System.out.println("pid info: "+ ManagementFactory.getRuntimeMXBean().getName());
-            nettyRemotingServer.init();
-        });
-        thread.start();
-
-        //then test client
-        Thread.sleep(3000);
-
-        String applicationId = "app 1";
-        String transactionServiceGroup = "group A";
-        TmNettyRemotingClient tmNettyRemotingClient = TmNettyRemotingClient.getInstance(applicationId, transactionServiceGroup);
-
-        tmNettyRemotingClient.init();
-        String serverAddress = "0.0.0.0:8091";
-        Channel channel = TmNettyRemotingClient.getInstance().getClientChannelManager().acquireChannel(serverAddress);
-        Assertions.assertNotNull(channel);
-        nettyRemotingServer.destroy();
-        tmNettyRemotingClient.destroy();
-    }
-
-    /**
-     * Client rely on server's starting first
-     *
-     * @throws Exception
-     */
-    @Test
-    public void testReconnect() throws Exception {
-        ThreadPoolExecutor workingThreads = initMessageExecutor();
-        NettyRemotingServer nettyRemotingServer = new NettyRemotingServer(workingThreads);
-        //start services server first
-        Thread thread = new Thread(() -> {
-            nettyRemotingServer.setHandler(DefaultCoordinator.getInstance(nettyRemotingServer));
-            // set registry
-            XID.setIpAddress(NetUtil.getLocalIp());
-            XID.setPort(8091);
-            // init snowflake for transactionId, branchId
-            UUIDGenerator.init(1L);
-            nettyRemotingServer.init();
-        });
-        thread.start();
-
-        //then test client
-        Thread.sleep(3000);
-
-        String applicationId = "app 1";
-        String transactionServiceGroup = "default_tx_group";
-        TmNettyRemotingClient tmNettyRemotingClient = TmNettyRemotingClient.getInstance(applicationId, transactionServiceGroup);
-
-        tmNettyRemotingClient.init();
-
-        TmNettyRemotingClient.getInstance().getClientChannelManager().reconnect(transactionServiceGroup);
-        nettyRemotingServer.destroy();
-        tmNettyRemotingClient.destroy();
-    }
-
-    @Test
-    public void testSendMsgWithResponse() throws Exception {
-        ThreadPoolExecutor workingThreads = initMessageExecutor();
-        NettyRemotingServer nettyRemotingServer = new NettyRemotingServer(workingThreads);
-        new Thread(() -> {
-            SessionHolder.init(null);
-            nettyRemotingServer.setHandler(DefaultCoordinator.getInstance(nettyRemotingServer));
-            // set registry
-            XID.setIpAddress(NetUtil.getLocalIp());
-            XID.setPort(8091);
-            // init snowflake for transactionId, branchId
-            UUIDGenerator.init(1L);
-            nettyRemotingServer.init();
-        }).start();
-        Thread.sleep(3000);
-
-        String applicationId = "app 1";
-        String transactionServiceGroup = "default_tx_group";
-        TmNettyRemotingClient tmNettyRemotingClient = TmNettyRemotingClient.getInstance(applicationId, transactionServiceGroup);
-        tmNettyRemotingClient.init();
-
-        String serverAddress = "0.0.0.0:8091";
-        Channel channel = TmNettyRemotingClient.getInstance().getClientChannelManager().acquireChannel(serverAddress);
-        Assertions.assertNotNull(channel);
-
-        BranchRegisterRequest request = new BranchRegisterRequest();
-        request.setXid("127.0.0.1:8091:1249853");
-        request.setLockKey("lock key testSendMsgWithResponse");
-        request.setResourceId("resoutceId1");
-        BranchRegisterResponse branchRegisterResponse = (BranchRegisterResponse) tmNettyRemotingClient.sendSyncRequest(request);
-        Assertions.assertNotNull(branchRegisterResponse);
-        Assertions.assertEquals(ResultCode.Failed, branchRegisterResponse.getResultCode());
-        Assertions.assertEquals("TransactionException[Could not found global transaction xid = 127.0.0.1:8091:1249853, may be has finished.]",
-                branchRegisterResponse.getMsg());
-        nettyRemotingServer.destroy();
-        tmNettyRemotingClient.destroy();
-    }
-}
diff --git a/test/src/test/java/io/seata/saga/engine/db/StateMachineDBTests.java b/test/src/test/java/io/seata/saga/engine/db/StateMachineDBTests.java
deleted file mode 100644
index 9cdc0c96..00000000
--- a/test/src/test/java/io/seata/saga/engine/db/StateMachineDBTests.java
+++ /dev/null
@@ -1,1141 +0,0 @@
-/*
- *  Copyright 1999-2019 Seata.io Group.
- *
- *  Licensed under the Apache License, Version 2.0 (the "License");
- *  you may not use this file except in compliance with the License.
- *  You may obtain a copy of the License at
- *
- *       http://www.apache.org/licenses/LICENSE-2.0
- *
- *  Unless required by applicable law or agreed to in writing, software
- *  distributed under the License is distributed on an "AS IS" BASIS,
- *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- *  See the License for the specific language governing permissions and
- *  limitations under the License.
- */
-package io.seata.saga.engine.db;
-
-import io.seata.common.LockAndCallback;
-import io.seata.common.SagaCostPrint;
-import io.seata.common.exception.FrameworkErrorCode;
-import io.seata.common.exception.StoreException;
-import io.seata.core.context.RootContext;
-import io.seata.core.exception.TransactionException;
-import io.seata.core.model.GlobalStatus;
-import io.seata.saga.engine.AsyncCallback;
-import io.seata.saga.engine.StateMachineEngine;
-import io.seata.saga.engine.exception.EngineExecutionException;
-import io.seata.saga.engine.impl.DefaultStateMachineConfig;
-import io.seata.saga.engine.mock.DemoService.Engineer;
-import io.seata.saga.engine.mock.DemoService.People;
-import io.seata.saga.proctrl.ProcessContext;
-import io.seata.saga.statelang.domain.DomainConstants;
-import io.seata.saga.statelang.domain.ExecutionStatus;
-import io.seata.saga.statelang.domain.StateMachineInstance;
-import io.seata.tm.api.GlobalTransaction;
-import io.seata.tm.api.GlobalTransactionContext;
-import org.junit.jupiter.api.AfterAll;
-import org.junit.jupiter.api.Assertions;
-import org.junit.jupiter.api.BeforeAll;
-import org.junit.jupiter.api.Disabled;
-import org.junit.jupiter.api.Test;
-import org.springframework.context.ApplicationContext;
-import org.springframework.context.support.ClassPathXmlApplicationContext;
-
-import java.util.ArrayList;
-import java.util.HashMap;
-import java.util.List;
-import java.util.Map;
-import java.util.concurrent.CountDownLatch;
-import java.util.concurrent.TimeUnit;
-
-/**
- * State machine tests with db log store
- *
- * <AUTHOR>
- */
-public class StateMachineDBTests extends AbstractServerTest {
-
-    private static StateMachineEngine stateMachineEngine;
-
-    private static int sleepTime = 1500;
-
-    private static int sleepTimeLong = 2500;
-
-    @BeforeAll
-    public static void initApplicationContext() throws InterruptedException {
-
-        startSeataServer();
-
-        ApplicationContext applicationContext = new ClassPathXmlApplicationContext("classpath:saga/spring/statemachine_engine_db_test.xml");
-        stateMachineEngine = applicationContext.getBean("stateMachineEngine", StateMachineEngine.class);
-    }
-
-    @AfterAll
-    public static void destory() throws InterruptedException {
-        stopSeataServer();
-    }
-
-    private GlobalTransaction getGlobalTransaction(StateMachineInstance instance) {
-        GlobalTransaction globalTransaction = null;
-        Map<String, Object> params = instance.getContext();
-        if (params != null) {
-            globalTransaction = (GlobalTransaction) params.get(DomainConstants.VAR_NAME_GLOBAL_TX);
-        }
-        if (globalTransaction == null) {
-            try {
-                globalTransaction = GlobalTransactionContext.reload(instance.getId());
-            } catch (TransactionException e) {
-                e.printStackTrace();
-            }
-        }
-        return globalTransaction;
-    }
-
-    @Test
-    public void testSimpleStateMachine() {
-
-        stateMachineEngine.start("simpleTestStateMachine", null, new HashMap<>());
-    }
-
-    @Test
-    public void testSimpleStateMachineWithChoice() throws Exception {
-        String stateMachineName = "simpleChoiceTestStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-1", () -> {
-            Map<String, Object> paramMap = new HashMap<>(1);
-            paramMap.put("a", 1);
-
-            stateMachineEngine.start(stateMachineName, null, paramMap);
-        });
-
-        SagaCostPrint.executeAndPrint("3-2", () -> {
-            Map<String, Object> paramMap = new HashMap<>(1);
-            paramMap.put("a", 2);
-
-            stateMachineEngine.start(stateMachineName, null, paramMap);
-        });
-    }
-
-    @Test
-    public void testSimpleStateMachineWithChoiceNoDefault() throws Exception {
-        String stateMachineName = "simpleChoiceNoDefaultTestStateMachine";
-
-        try {
-            SagaCostPrint.executeAndPrint("3-3", () -> {
-                Map<String, Object> paramMap = new HashMap<>(1);
-                paramMap.put("a", 3);
-
-                stateMachineEngine.start(stateMachineName, null, paramMap);
-            });
-        } catch (EngineExecutionException e) {
-            Assertions.assertEquals(FrameworkErrorCode.StateMachineNoChoiceMatched, e.getErrcode());
-            e.printStackTrace(System.out);
-        }
-    }
-
-    @Test
-    public void testSimpleStateMachineWithChoiceAndEnd() throws Exception {
-        String stateMachineName = "simpleChoiceAndEndTestStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-4", () -> {
-            Map<String, Object> paramMap = new HashMap<>(1);
-            paramMap.put("a", 1);
-
-            stateMachineEngine.start(stateMachineName, null, paramMap);
-        });
-
-        SagaCostPrint.executeAndPrint("3-5", () -> {
-            Map<String, Object> paramMap = new HashMap<>(1);
-            paramMap.put("a", 3);
-
-            stateMachineEngine.start(stateMachineName, null, paramMap);
-        });
-    }
-
-    @Test
-    public void testSimpleInputAssignmentStateMachine() throws Exception {
-        String stateMachineName = "simpleInputAssignmentStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-6", () -> {
-            Map<String, Object> paramMap = new HashMap<>(1);
-            paramMap.put("a", 1);
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-
-            String businessKey = inst.getStateList().get(0).getBusinessKey();
-            Assertions.assertNotNull(businessKey);
-            System.out.println("====== businessKey :" + businessKey);
-
-            String contextBusinessKey = (String) inst.getEndParams().get(
-                    inst.getStateList().get(0).getName() + DomainConstants.VAR_NAME_BUSINESSKEY);
-            Assertions.assertNotNull(contextBusinessKey);
-            System.out.println("====== context businessKey :" + businessKey);
-        });
-    }
-
-    @Test
-    public void testSimpleCatchesStateMachine() throws Exception {
-        String stateMachineName = "simpleCachesStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-7", () -> {
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("a", 1);
-            paramMap.put("barThrowException", "true");
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-
-            Assertions.assertNotNull(inst.getException());
-            Assertions.assertEquals(ExecutionStatus.FA, inst.getStatus());
-
-            GlobalTransaction globalTransaction = getGlobalTransaction(inst);
-            Assertions.assertNotNull(globalTransaction);
-            Assertions.assertEquals(GlobalStatus.Finished, globalTransaction.getStatus());
-        });
-    }
-
-    @Test
-    public void testSimpleRetryStateMachine() throws Exception {
-        String stateMachineName = "simpleRetryStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-8", () -> {
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("a", 1);
-            paramMap.put("barThrowException", "true");
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-
-            Assertions.assertNotNull(inst.getException());
-            Assertions.assertEquals(ExecutionStatus.FA, inst.getStatus());
-        });
-    }
-
-    @Test
-    public void testStatusMatchingStateMachine() throws Exception {
-        String stateMachineName = "simpleStatusMatchingStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-9", () -> {
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("a", 1);
-            paramMap.put("barThrowException", "true");
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-
-            Assertions.assertNotNull(inst.getException());
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-
-            GlobalTransaction globalTransaction = getGlobalTransaction(inst);
-            Assertions.assertNotNull(globalTransaction);
-            System.out.println(globalTransaction.getStatus());
-            Assertions.assertEquals(GlobalStatus.CommitRetrying, globalTransaction.getStatus());
-        });
-    }
-
-    @Test
-    public void testStateMachineWithComplexParams() throws Exception {
-        String stateMachineName = "simpleStateMachineWithComplexParamsJackson";
-
-        SagaCostPrint.executeAndPrint("3-10", () -> {
-            People people = new People();
-            people.setName("lilei");
-            people.setAge(18);
-
-            Engineer engineer = new Engineer();
-            engineer.setName("programmer");
-
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("people", people);
-            paramMap.put("career", engineer);
-
-            StateMachineInstance instance = stateMachineEngine.start(stateMachineName, null, paramMap);
-
-            People peopleResult = (People)instance.getEndParams().get("complexParameterMethodResult");
-            Assertions.assertNotNull(peopleResult);
-            Assertions.assertEquals(people.getName(), peopleResult.getName());
-
-            Assertions.assertEquals(ExecutionStatus.SU, instance.getStatus());
-        });
-    }
-
-    @Test
-    public void testCompensationStateMachine() throws Exception {
-        String stateMachineName = "simpleCompensationStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-11", () -> {
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("a", 1);
-            paramMap.put("barThrowException", "true");
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-            Assertions.assertEquals(ExecutionStatus.SU, inst.getCompensationStatus());
-
-            GlobalTransaction globalTransaction = getGlobalTransaction(inst);
-            Assertions.assertNotNull(globalTransaction);
-            //End with Rollbacked = Finished
-            Assertions.assertEquals(GlobalStatus.Finished, globalTransaction.getStatus());
-        });
-    }
-
-    @Test
-    public void testCompensationAndSubStateMachine() throws Exception {
-        String stateMachineName = "simpleStateMachineWithCompensationAndSubMachine";
-
-        SagaCostPrint.executeAndPrint("3-12", () -> {
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("a", 2);
-            paramMap.put("barThrowException", "true");
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-
-            GlobalTransaction globalTransaction = getGlobalTransaction(inst);
-            Assertions.assertNotNull(globalTransaction);
-            Assertions.assertEquals(GlobalStatus.CommitRetrying, globalTransaction.getStatus());
-        });
-    }
-
-    @Test
-    public void testCompensationAndSubStateMachineLayout() throws Exception {
-        String stateMachineName = "simpleStateMachineWithCompensationAndSubMachine_layout";
-
-        SagaCostPrint.executeAndPrint("3-13", () -> {
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("a", 2);
-            paramMap.put("barThrowException", "true");
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-
-            GlobalTransaction globalTransaction = getGlobalTransaction(inst);
-            Assertions.assertNotNull(globalTransaction);
-            Assertions.assertEquals(GlobalStatus.CommitRetrying, globalTransaction.getStatus());
-        });
-    }
-
-    @Test
-    @Disabled("FIXME: Sometimes it takes a lot of time")
-    public void testCompensationStateMachineForRecovery() throws Exception {
-        String stateMachineName = "simpleCompensationStateMachineForRecovery";
-
-        SagaCostPrint.executeAndPrint("3-14", () -> {
-            Map<String, Object> paramMap = new HashMap<>();
-            paramMap.put("a", 1);
-            paramMap.put("fooThrowExceptionRandomly", "true");
-            paramMap.put("barThrowExceptionRandomly", "true");
-            paramMap.put("compensateFooThrowExceptionRandomly", "true");
-            paramMap.put("compensateBarThrowExceptionRandomly", "true");
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-
-            GlobalTransaction globalTransaction = getGlobalTransaction(inst);
-            Assertions.assertNotNull(globalTransaction);
-            System.out.println("====== GlobalStatus: " + globalTransaction.getStatus());
-
-            // waiting for global transaction recover
-            while (!(ExecutionStatus.SU.equals(inst.getStatus()) || ExecutionStatus.SU.equals(inst.getCompensationStatus()))) {
-                System.out.println("====== GlobalStatus: " + globalTransaction.getStatus());
-                Thread.sleep(1000);
-                inst = stateMachineEngine.getStateMachineConfig().getStateLogStore().getStateMachineInstance(inst.getId());
-            }
-        });
-    }
-
-    @Test
-    public void testReloadStateMachineInstance() {
-        StateMachineInstance instance = stateMachineEngine.getStateMachineConfig().getStateLogStore().getStateMachineInstance(
-                "10.15.232.93:8091:2019567124");
-        System.out.println(instance);
-    }
-
-    @Test
-    public void testSimpleStateMachineWithAsyncState() throws Exception {
-        String stateMachineName = "simpleStateMachineWithAsyncState";
-
-        SagaCostPrint.executeAndPrint("3-15", () -> {
-            Map<String, Object> paramMap = new HashMap<>(1);
-            paramMap.put("a", 1);
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-
-            Assertions.assertEquals(ExecutionStatus.SU, inst.getStatus());
-
-            try {
-                Thread.sleep(500);
-            } catch (InterruptedException e) {
-                e.printStackTrace();
-            }
-        });
-    }
-
-    @Test
-    public void testSimpleCatchesStateMachineAsync() throws Exception {
-        String stateMachineName = "simpleCachesStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-16", () -> {
-
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("a", 1);
-            paramMap.put("barThrowException", "true");
-
-            LockAndCallback lockAndCallback = new LockAndCallback();
-            StateMachineInstance inst = stateMachineEngine.startAsync(stateMachineName, null, paramMap, lockAndCallback.getCallback());
-            lockAndCallback.waittingForFinish(inst);
-
-            Assertions.assertNotNull(inst.getException());
-            Assertions.assertEquals(ExecutionStatus.FA, inst.getStatus());
-        });
-    }
-
-    @Test
-    public void testSimpleRetryStateMachineAsync() throws Exception {
-        String stateMachineName = "simpleRetryStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-17", () -> {
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("a", 1);
-            paramMap.put("barThrowException", "true");
-
-            LockAndCallback lockAndCallback = new LockAndCallback();
-            StateMachineInstance inst = stateMachineEngine.startAsync(stateMachineName, null, paramMap, lockAndCallback.getCallback());
-            lockAndCallback.waittingForFinish(inst);
-
-            Assertions.assertNotNull(inst.getException());
-            Assertions.assertEquals(ExecutionStatus.FA, inst.getStatus());
-        });
-    }
-
-    @Test
-    public void testStatusMatchingStateMachineAsync() throws Exception {
-        String stateMachineName = "simpleStatusMatchingStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-18", () -> {
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("a", 1);
-            paramMap.put("barThrowException", "true");
-
-            LockAndCallback lockAndCallback = new LockAndCallback();
-            StateMachineInstance inst = stateMachineEngine.startAsync(stateMachineName, null, paramMap, lockAndCallback.getCallback());
-            lockAndCallback.waittingForFinish(inst);
-
-            Assertions.assertNotNull(inst.getException());
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-
-            GlobalTransaction globalTransaction = getGlobalTransaction(inst);
-            Assertions.assertNotNull(globalTransaction);
-            Assertions.assertEquals(GlobalStatus.CommitRetrying, globalTransaction.getStatus());
-        });
-    }
-
-    @Disabled("https://github.com/seata/seata/issues/2564")
-    public void testCompensationStateMachineAsync() throws Exception {
-        String stateMachineName = "simpleCompensationStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-19", () -> {
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("a", 1);
-            paramMap.put("barThrowException", "true");
-
-            LockAndCallback lockAndCallback = new LockAndCallback();
-            StateMachineInstance inst = stateMachineEngine.startAsync(stateMachineName, null, paramMap, lockAndCallback.getCallback());
-            lockAndCallback.waittingForFinish(inst);
-
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-            Assertions.assertEquals(ExecutionStatus.SU, inst.getCompensationStatus());
-
-            GlobalTransaction globalTransaction = getGlobalTransaction(inst);
-            Assertions.assertNotNull(globalTransaction);
-            Assertions.assertEquals(GlobalStatus.Finished, globalTransaction.getStatus());
-        });
-    }
-
-    @Test
-    @Disabled("https://github.com/seata/seata/issues/2414#issuecomment-639546811")
-    public void simpleChoiceTestStateMachineAsyncConcurrently() throws Exception {
-        String stateMachineName = "simpleCompensationStateMachine";
-
-        final int l1 = 10, l2 = 10;
-        final CountDownLatch countDownLatch = new CountDownLatch(l1 * l2);
-        final List<Exception> exceptions = new ArrayList<>();
-
-        final AsyncCallback asyncCallback = new AsyncCallback() {
-            @Override
-            public void onFinished(ProcessContext context, StateMachineInstance stateMachineInstance) {
-                countDownLatch.countDown();
-            }
-
-            @Override
-            public void onError(ProcessContext context, StateMachineInstance stateMachineInstance, Exception exp) {
-                exceptions.add(exp);
-                countDownLatch.countDown();
-            }
-        };
-
-        long start = System.nanoTime();
-        for (int i = 0; i < l1; i++) {
-            final int iValue = i;
-            Thread t = new Thread(() -> {
-                for (int j = 0; j < l2; j++) {
-                    try {
-                        SagaCostPrint.executeAndPrint("3-20_" + iValue + "-" + j, () -> {
-                            Map<String, Object> paramMap = new HashMap<>(2);
-                            paramMap.put("a", 1);
-                            paramMap.put("barThrowException", "false");
-
-                            try {
-                                stateMachineEngine.startAsync(stateMachineName, null, paramMap, asyncCallback);
-                            } catch (Exception e) {
-                                exceptions.add(e);
-                                countDownLatch.countDown();
-                            }
-                        });
-                    } catch (Exception e) {
-                        throw new RuntimeException("startAsync failed", e);
-                    }
-                }
-            });
-            t.start();
-        }
-
-        countDownLatch.await(10000, TimeUnit.MILLISECONDS);
-
-        long cost = (System.nanoTime() - start) / 1000_000;
-        System.out.println("====== cost3-20: " + cost + " ms");
-
-        if (exceptions.size() > 0) {
-            Assertions.fail(exceptions.get(0));
-        }
-    }
-
-    @Test
-    @Disabled("https://github.com/seata/seata/issues/2414#issuecomment-651526068")
-    public void testCompensationAndSubStateMachineAsync() throws Exception {
-        String stateMachineName = "simpleStateMachineWithCompensationAndSubMachine";
-
-        SagaCostPrint.executeAndPrint("3-21", () -> {
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("a", 2);
-            paramMap.put("barThrowException", "true");
-
-            LockAndCallback lockAndCallback = new LockAndCallback();
-            StateMachineInstance inst = stateMachineEngine.startAsync(stateMachineName, null, paramMap, lockAndCallback.getCallback());
-            lockAndCallback.waittingForFinish(inst);
-
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-
-            GlobalTransaction globalTransaction = getGlobalTransaction(inst);
-            Assertions.assertNotNull(globalTransaction);
-            Assertions.assertEquals(GlobalStatus.CommitRetrying, globalTransaction.getStatus());
-        });
-    }
-
-    @Test
-    @Disabled("https://github.com/seata/seata/issues/2414#issuecomment-640432396")
-    public void testCompensationAndSubStateMachineAsyncWithLayout() throws Exception {
-        String stateMachineName = "simpleStateMachineWithCompensationAndSubMachine_layout";
-
-        SagaCostPrint.executeAndPrint("3-22", () -> {
-            Map<String, Object> paramMap = new HashMap<>(1);
-            paramMap.put("a", 2);
-            paramMap.put("barThrowException", "true");
-
-            LockAndCallback lockAndCallback = new LockAndCallback();
-            StateMachineInstance inst = stateMachineEngine.startAsync(stateMachineName, null, paramMap, lockAndCallback.getCallback());
-            lockAndCallback.waittingForFinish(inst);
-
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-
-            GlobalTransaction globalTransaction = getGlobalTransaction(inst);
-            Assertions.assertNotNull(globalTransaction);
-            Assertions.assertEquals(GlobalStatus.CommitRetrying, globalTransaction.getStatus());
-        });
-    }
-
-    @Test
-    public void testAsyncStartSimpleStateMachineWithAsyncState() throws Exception {
-        String stateMachineName = "simpleStateMachineWithAsyncState";
-
-        SagaCostPrint.executeAndPrint("3-23", () -> {
-            Map<String, Object> paramMap = new HashMap<>(1);
-            paramMap.put("a", 1);
-
-            LockAndCallback lockAndCallback = new LockAndCallback();
-            StateMachineInstance inst = stateMachineEngine.startAsync(stateMachineName, null, paramMap, lockAndCallback.getCallback());
-            lockAndCallback.waittingForFinish(inst);
-
-            Assertions.assertEquals(ExecutionStatus.SU, inst.getStatus());
-        });
-
-        try {
-            Thread.sleep(500);
-        } catch (InterruptedException e) {
-            e.printStackTrace();
-        }
-    }
-
-    @Test
-    @Disabled("FIXME: Sometimes it takes a lot of time")
-    public void testStateMachineTransTimeout() throws Exception {
-        ((DefaultStateMachineConfig)stateMachineEngine.getStateMachineConfig()).setTransOperationTimeout(1500);
-
-        //first state timeout
-        Map<String, Object> paramMap = new HashMap<>(3);
-        paramMap.put("a", 1);
-
-        //timeout rollback after state machine finished (first state success)
-        paramMap.put("fooSleepTime", sleepTime);
-        doTestStateMachineTransTimeout(paramMap, 1);
-
-        //timeout rollback before state machine finished (first state success)
-        paramMap.put("fooSleepTime", sleepTimeLong);
-        doTestStateMachineTransTimeout(paramMap, 2);
-
-        //timeout rollback after state machine finished (first state fail)
-        paramMap.put("fooSleepTime", sleepTime);
-        paramMap.put("fooThrowException", "true");
-        doTestStateMachineTransTimeout(paramMap, 3);
-
-        //timeout rollback before state machine finished (first state fail)
-        paramMap.put("fooSleepTime", sleepTimeLong);
-        paramMap.put("fooThrowException", "true");
-        doTestStateMachineTransTimeout(paramMap, 4);
-
-
-        //last state timeout
-        paramMap = new HashMap<>(3);
-        paramMap.put("a", 1);
-
-        //timeout rollback after state machine finished (last state success)
-        paramMap.put("barSleepTime", sleepTime);
-        doTestStateMachineTransTimeout(paramMap, 5);
-
-        //timeout rollback before state machine finished (last state success)
-        paramMap.put("barSleepTime", sleepTimeLong);
-        doTestStateMachineTransTimeout(paramMap, 6);
-
-        //timeout rollback after state machine finished (last state fail)
-        paramMap.put("barSleepTime", sleepTime);
-        paramMap.put("barThrowException", "true");
-        doTestStateMachineTransTimeout(paramMap, 7);
-
-        //timeout rollback before state machine finished (last state fail)
-        paramMap.put("barSleepTime", sleepTimeLong);
-        paramMap.put("barThrowException", "true");
-        doTestStateMachineTransTimeout(paramMap, 8);
-
-        ((DefaultStateMachineConfig)stateMachineEngine.getStateMachineConfig()).setTransOperationTimeout(60000 * 30);
-    }
-
-    @Test
-    @Disabled("FIXME: Sometimes it takes a lot of time")
-    public void testStateMachineTransTimeoutAsync() throws Exception {
-        ((DefaultStateMachineConfig)stateMachineEngine.getStateMachineConfig()).setTransOperationTimeout(1500);
-
-        //first state timeout
-        Map<String, Object> paramMap = new HashMap<>(3);
-        paramMap.put("a", 1);
-
-        //timeout rollback after state machine finished (first state success)
-        paramMap.put("fooSleepTime", sleepTime);
-        doTestStateMachineTransTimeoutAsync(paramMap, 1);
-
-        //timeout rollback before state machine finished (first state success)
-        paramMap.put("fooSleepTime", sleepTimeLong);
-        doTestStateMachineTransTimeoutAsync(paramMap, 2);
-
-        //timeout rollback after state machine finished (first state fail)
-        paramMap.put("fooSleepTime", sleepTime);
-        paramMap.put("fooThrowException", "true");
-        doTestStateMachineTransTimeoutAsync(paramMap, 3);
-
-        //timeout rollback before state machine finished (first state fail)
-        paramMap.put("fooSleepTime", sleepTimeLong);
-        paramMap.put("fooThrowException", "true");
-        doTestStateMachineTransTimeoutAsync(paramMap, 4);
-
-
-        //last state timeout
-        paramMap = new HashMap<>(3);
-        paramMap.put("a", 1);
-
-        //timeout rollback after state machine finished (last state success)
-        paramMap.put("barSleepTime", sleepTime);
-        doTestStateMachineTransTimeoutAsync(paramMap, 5);
-
-        //timeout rollback before state machine finished (last state success)
-        paramMap.put("barSleepTime", sleepTimeLong);
-        doTestStateMachineTransTimeoutAsync(paramMap, 6);
-
-        //timeout rollback after state machine finished (last state fail)
-        paramMap.put("barSleepTime", sleepTime);
-        paramMap.put("barThrowException", "true");
-        doTestStateMachineTransTimeoutAsync(paramMap, 7);
-
-        //timeout rollback before state machine finished (last state fail)
-        paramMap.put("barSleepTime", sleepTimeLong);
-        paramMap.put("barThrowException", "true");
-        doTestStateMachineTransTimeoutAsync(paramMap, 8);
-
-        ((DefaultStateMachineConfig)stateMachineEngine.getStateMachineConfig()).setTransOperationTimeout(60000 * 30);
-    }
-
-    @Test
-    public void testStateMachineRecordFailed() throws Exception {
-        String stateMachineName = "simpleTestStateMachine";
-        String businessKey = "bizKey";
-
-        SagaCostPrint.executeAndPrint("3-24", () -> {
-            Assertions.assertDoesNotThrow(() -> stateMachineEngine.startWithBusinessKey(stateMachineName, null, businessKey, new HashMap<>()));
-        });
-
-        SagaCostPrint.executeAndPrint("3-25", () -> {
-            // use same biz key to mock exception
-            Assertions.assertThrows(StoreException.class, () -> stateMachineEngine.startWithBusinessKey(stateMachineName, null, businessKey, new HashMap<>()));
-            Assertions.assertNull(RootContext.getXID());
-        });
-    }
-
-    @Test
-    public void testSimpleRetryStateAsUpdateMode() throws Exception {
-        String stateMachineName = "simpleUpdateStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-26", () -> {
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("a", 1);
-            paramMap.put("barThrowException", "true");
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-
-            Assertions.assertNotNull(inst.getException());
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-
-            Thread.sleep(sleepTime);
-
-            inst = stateMachineEngine.getStateMachineConfig().getStateLogStore().getStateMachineInstance(inst.getId());
-            Assertions.assertEquals(2, inst.getStateList().size());
-        });
-    }
-
-    @Test
-    @Disabled("FIXME")
-    public void testSimpleCompensateStateAsUpdateMode() throws Exception {
-        String stateMachineName = "simpleUpdateStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-27", () -> {
-            Map<String, Object> paramMap = new HashMap<>(3);
-            paramMap.put("a", 2);
-            paramMap.put("barThrowException", "true");
-            paramMap.put("compensateBarThrowException", "true");
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-
-            Assertions.assertNotNull(inst.getException());
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-
-            Thread.sleep(sleepTime);
-
-            inst = stateMachineEngine.getStateMachineConfig().getStateLogStore().getStateMachineInstance(inst.getId());
-            // FIXME: some times, the size is 4
-            Assertions.assertEquals(3, inst.getStateList().size());
-        });
-    }
-
-    @Test
-    public void testSimpleSubRetryStateAsUpdateMode() throws Exception {
-        String stateMachineName = "simpleStateMachineWithCompensationAndSubMachine";
-
-        SagaCostPrint.executeAndPrint("3-28", () -> {
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("a", 3);
-            paramMap.put("barThrowException", "true");
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-
-            Thread.sleep(sleepTime);
-
-            inst = stateMachineEngine.getStateMachineConfig().getStateLogStore().getStateMachineInstance(inst.getId());
-            Assertions.assertEquals(2, inst.getStateList().size());
-        });
-    }
-
-    @Test
-    public void testSimpleSubCompensateStateAsUpdateMode() throws Exception {
-        String stateMachineName = "simpleStateMachineWithCompensationAndSubMachine";
-
-        SagaCostPrint.executeAndPrint("3-29", () -> {
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("a", 4);
-            paramMap.put("barThrowException", "true");
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-
-            Thread.sleep(sleepTime);
-
-            inst = stateMachineEngine.getStateMachineConfig().getStateLogStore().getStateMachineInstance(inst.getId());
-            Assertions.assertEquals(2, inst.getStateList().size());
-        });
-    }
-
-    @Test
-    public void testSimpleStateMachineWithLoop() throws Exception {
-        String stateMachineName = "simpleLoopTestStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-30", () -> {
-            List<Integer> loopList = new ArrayList<>();
-            for (int i = 0; i < 10; i++) {
-                loopList.add(i);
-            }
-
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("a", 1);
-            paramMap.put("collection", loopList);
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-
-            Assertions.assertEquals(ExecutionStatus.SU, inst.getStatus());
-        });
-    }
-
-    @Test
-    public void testSimpleStateMachineWithLoopForward() throws Exception {
-        String stateMachineName = "simpleLoopTestStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-31", () -> {
-            List<Integer> loopList = new ArrayList<>();
-            for (int i = 0; i < 10; i++) {
-                loopList.add(i);
-            }
-
-            Map<String, Object> paramMap = new HashMap<>(3);
-            paramMap.put("a", 1);
-            paramMap.put("collection", loopList);
-            paramMap.put("fooThrowException", "true");
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-
-            Thread.sleep(sleepTime);
-
-            inst = stateMachineEngine.getStateMachineConfig().getStateLogStore().getStateMachineInstance(inst.getId());
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-        });
-    }
-
-    @Test
-    public void testSimpleStateMachineWithLoopCompensate() throws Exception {
-        String stateMachineName = "simpleLoopTestStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-32", () -> {
-            List<Integer> loopList = new ArrayList<>();
-            for (int i = 0; i < 10; i++) {
-                loopList.add(i);
-            }
-
-            Map<String, Object> paramMap = new HashMap<>(3);
-            paramMap.put("a", 1);
-            paramMap.put("collection", loopList);
-            paramMap.put("barThrowException", "true");
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-            Assertions.assertEquals(ExecutionStatus.SU, inst.getCompensationStatus());
-        });
-    }
-
-    @Test
-    public void testSimpleStateMachineWithLoopCompensateForRecovery() throws Exception {
-        String stateMachineName = "simpleLoopTestStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-33", () -> {
-            List<Integer> loopList = new ArrayList<>();
-            for (int i = 0; i < 10; i++) {
-                loopList.add(i);
-            }
-
-            Map<String, Object> paramMap = new HashMap<>(4);
-            paramMap.put("a", 1);
-            paramMap.put("collection", loopList);
-            paramMap.put("barThrowException", "true");
-            paramMap.put("compensateFooThrowException", "true");
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getCompensationStatus());
-
-            Thread.sleep(sleepTime);
-
-            inst = stateMachineEngine.getStateMachineConfig().getStateLogStore().getStateMachineInstance(inst.getId());
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getCompensationStatus());
-        });
-    }
-
-    @Test
-    public void testSimpleStateMachineWithLoopSubMachine() throws Exception {
-        String stateMachineName = "simpleLoopTestStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-34", () -> {
-            List<Integer> loopList = new ArrayList<>();
-            for (int i = 0; i < 10; i++) {
-                loopList.add(i);
-            }
-
-            Map<String, Object> paramMap = new HashMap<>(2);
-            paramMap.put("a", 2);
-            paramMap.put("collection", loopList);
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-
-            Assertions.assertEquals(ExecutionStatus.SU, inst.getStatus());
-        });
-    }
-
-    @Test
-    public void testSimpleStateMachineWithLoopSubMachineForward() throws Exception {
-        String stateMachineName = "simpleLoopTestStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-35", () -> {
-            List<Integer> loopList = new ArrayList<>();
-            for (int i = 0; i < 10; i++) {
-                loopList.add(i);
-            }
-
-            Map<String, Object> paramMap = new HashMap<>(3);
-            paramMap.put("a", 2);
-            paramMap.put("collection", loopList);
-            paramMap.put("barThrowException", "true");
-
-            StateMachineInstance inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-
-            Thread.sleep(sleepTime);
-
-            inst = stateMachineEngine.getStateMachineConfig().getStateLogStore().getStateMachineInstance(inst.getId());
-            Assertions.assertEquals(ExecutionStatus.UN, inst.getStatus());
-        });
-    }
-
-    private void doTestStateMachineTransTimeout(Map<String, Object> paramMap, int i) throws Exception {
-        String stateMachineName = "simpleCompensationStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-36-" + i, () -> {
-            StateMachineInstance inst;
-            try {
-                inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-            } catch (EngineExecutionException e) {
-                e.printStackTrace();
-
-                inst = stateMachineEngine.getStateMachineConfig().getStateLogStore().getStateMachineInstance(e.getStateMachineInstanceId());
-            }
-
-            GlobalTransaction globalTransaction = getGlobalTransaction(inst);
-            Assertions.assertNotNull(globalTransaction);
-            System.out.println("====== GlobalStatus: " + globalTransaction.getStatus());
-
-            // waiting for global transaction recover
-            while (!ExecutionStatus.SU.equals(inst.getCompensationStatus())) {
-                System.out.println("====== GlobalStatus: " + globalTransaction.getStatus());
-                Thread.sleep(2500);
-                inst = stateMachineEngine.getStateMachineConfig().getStateLogStore().getStateMachineInstance(inst.getId());
-            }
-
-            Assertions.assertTrue(ExecutionStatus.UN.equals(inst.getStatus())
-                    || ExecutionStatus.SU.equals(inst.getStatus()));
-            Assertions.assertEquals(ExecutionStatus.SU, inst.getCompensationStatus());
-        });
-    }
-
-    private void doTestStateMachineTransTimeoutAsync(Map<String, Object> paramMap, int i) throws Exception {
-        String stateMachineName = "simpleCompensationStateMachine";
-
-        SagaCostPrint.executeAndPrint("3-37-" + i, () -> {
-            LockAndCallback lockAndCallback = new LockAndCallback();
-            StateMachineInstance inst = stateMachineEngine.startAsync(stateMachineName, null, paramMap, lockAndCallback.getCallback());
-            lockAndCallback.waittingForFinish(inst);
-
-            GlobalTransaction globalTransaction = getGlobalTransaction(inst);
-            Assertions.assertNotNull(globalTransaction);
-            System.out.println("====== GlobalStatus: " + globalTransaction.getStatus());
-
-            // waiting for global transaction recover
-            while (!ExecutionStatus.SU.equals(inst.getCompensationStatus())) {
-                System.out.println("====== GlobalStatus: " + globalTransaction.getStatus());
-                Thread.sleep(2500);
-                inst = stateMachineEngine.getStateMachineConfig().getStateLogStore().getStateMachineInstance(inst.getId());
-            }
-
-            Assertions.assertTrue(ExecutionStatus.UN.equals(inst.getStatus())
-                    || ExecutionStatus.SU.equals(inst.getStatus()));
-            Assertions.assertEquals(ExecutionStatus.SU, inst.getCompensationStatus());
-        });
-    }
-
-    @Test
-    @Disabled("FIXME")
-    public void testStateMachineCustomRecoverStrategyOnTimeout() throws Exception {
-        ((DefaultStateMachineConfig)stateMachineEngine.getStateMachineConfig()).setTransOperationTimeout(1500);
-
-        //first state timeout
-        Map<String, Object> paramMap = new HashMap<>(3);
-        paramMap.put("a", 1);
-
-        //timeout forward after state machine finished (first state success)
-        paramMap.put("fooSleepTime", sleepTime);
-        doTestStateMachineCustomRecoverStrategyOnTimeout(paramMap, 1);
-
-        //timeout forward before state machine finished (first state success)
-        paramMap.put("fooSleepTime", sleepTimeLong);
-        doTestStateMachineCustomRecoverStrategyOnTimeout(paramMap, 2);
-
-        //timeout forward after state machine finished (first state fail randomly)
-        paramMap.put("fooSleepTime", sleepTime);
-        paramMap.put("fooThrowExceptionRandomly", "true");
-        doTestStateMachineCustomRecoverStrategyOnTimeout(paramMap, 3);
-
-        //timeout forward before state machine finished (first state fail randomly)
-        paramMap.put("fooSleepTime", sleepTimeLong);
-        paramMap.put("fooThrowExceptionRandomly", "true");
-        doTestStateMachineCustomRecoverStrategyOnTimeout(paramMap, 4);
-
-
-        //last state timeout
-        paramMap = new HashMap<>(3);
-        paramMap.put("a", 1);
-
-        //timeout forward after state machine finished (last state success)
-        paramMap.put("barSleepTime", sleepTime);
-        doTestStateMachineCustomRecoverStrategyOnTimeout(paramMap, 5);
-
-        //timeout forward before state machine finished (last state success)
-        paramMap.put("barSleepTime", sleepTimeLong);
-        doTestStateMachineCustomRecoverStrategyOnTimeout(paramMap, 6);
-
-        //timeout forward after state machine finished (last state fail randomly)
-        paramMap.put("barSleepTime", sleepTime);
-        paramMap.put("barThrowExceptionRandomly", "true");
-        doTestStateMachineCustomRecoverStrategyOnTimeout(paramMap, 7);
-
-        //timeout forward before state machine finished (last state fail randomly)
-        paramMap.put("barSleepTime", sleepTimeLong);
-        paramMap.put("barThrowExceptionRandomly", "true");
-        doTestStateMachineCustomRecoverStrategyOnTimeout(paramMap, 8);
-
-        ((DefaultStateMachineConfig)stateMachineEngine.getStateMachineConfig()).setTransOperationTimeout(60000 * 30);
-    }
-
-    private void doTestStateMachineCustomRecoverStrategyOnTimeout(Map<String, Object> paramMap, int i) throws Exception {
-        String stateMachineName = "simpleStateMachineWithRecoverStrategy";
-
-        SagaCostPrint.executeAndPrint("3-38-" + i, () -> {
-            StateMachineInstance inst;
-            try {
-                inst = stateMachineEngine.start(stateMachineName, null, paramMap);
-            } catch (EngineExecutionException e) {
-                e.printStackTrace();
-
-                inst = stateMachineEngine.getStateMachineConfig().getStateLogStore().getStateMachineInstance(e.getStateMachineInstanceId());
-            }
-
-            GlobalTransaction globalTransaction = getGlobalTransaction(inst);
-            Assertions.assertNotNull(globalTransaction);
-            System.out.println("====== GlobalStatus: " + globalTransaction.getStatus());
-
-            // waiting for global transaction recover
-            while (!(ExecutionStatus.SU.equals(inst.getStatus())
-                    && GlobalStatus.Finished.equals(globalTransaction.getStatus()))) {
-                System.out.println("====== GlobalStatus: " + globalTransaction.getStatus());
-                System.out.println("====== StateMachineInstanceStatus: " + inst.getStatus());
-                Thread.sleep(2500);
-                inst = stateMachineEngine.getStateMachineConfig().getStateLogStore().getStateMachineInstance(inst.getId());
-            }
-
-            Assertions.assertEquals(ExecutionStatus.SU, inst.getStatus());
-            Assertions.assertNull(inst.getCompensationStatus());
-        });
-    }
-
-    @Test
-    @Disabled("FIXME")
-    public void testStateMachineCustomRecoverStrategyOnTimeoutAsync() throws Exception {
-        ((DefaultStateMachineConfig)stateMachineEngine.getStateMachineConfig()).setTransOperationTimeout(1500);
-
-        //first state timeout
-        Map<String, Object> paramMap = new HashMap<>(3);
-        paramMap.put("a", 1);
-
-        //timeout forward after state machine finished (first state success)
-        paramMap.put("fooSleepTime", sleepTime);
-        doTestStateMachineCustomRecoverStrategyOnTimeoutAsync(paramMap, 1);
-
-        //timeout forward before state machine finished (first state success)
-        paramMap.put("fooSleepTime", sleepTimeLong);
-        doTestStateMachineCustomRecoverStrategyOnTimeoutAsync(paramMap, 2);
-
-        //timeout forward after state machine finished (first state fail randomly)
-        paramMap.put("fooSleepTime", sleepTime);
-        paramMap.put("fooThrowExceptionRandomly", "true");
-        doTestStateMachineCustomRecoverStrategyOnTimeoutAsync(paramMap, 3);
-
-        //timeout forward before state machine finished (first state fail randomly)
-        paramMap.put("fooSleepTime", sleepTimeLong);
-        paramMap.put("fooThrowExceptionRandomly", "true");
-        doTestStateMachineCustomRecoverStrategyOnTimeoutAsync(paramMap, 4);
-
-
-        //last state timeout
-        paramMap = new HashMap<>(3);
-        paramMap.put("a", 1);
-
-        //timeout forward after state machine finished (last state success)
-        paramMap.put("barSleepTime", sleepTime);
-        doTestStateMachineCustomRecoverStrategyOnTimeoutAsync(paramMap, 5);
-
-        //timeout forward before state machine finished (last state success)
-        paramMap.put("barSleepTime", sleepTimeLong);
-        doTestStateMachineCustomRecoverStrategyOnTimeoutAsync(paramMap, 6);
-
-        //timeout forward after state machine finished (last state fail randomly)
-        paramMap.put("barSleepTime", sleepTime);
-        paramMap.put("barThrowExceptionRandomly", "true");
-        doTestStateMachineCustomRecoverStrategyOnTimeoutAsync(paramMap, 7);
-
-        //timeout forward before state machine finished (last state fail randomly)
-        paramMap.put("barSleepTime", sleepTimeLong);
-        paramMap.put("barThrowExceptionRandomly", "true");
-        doTestStateMachineCustomRecoverStrategyOnTimeoutAsync(paramMap, 8);
-
-        ((DefaultStateMachineConfig)stateMachineEngine.getStateMachineConfig()).setTransOperationTimeout(60000 * 30);
-    }
-
-    private void doTestStateMachineCustomRecoverStrategyOnTimeoutAsync(Map<String, Object> paramMap, int i) throws Exception {
-        String stateMachineName = "simpleStateMachineWithRecoverStrategy";
-
-        SagaCostPrint.executeAndPrint("3-39-" + i, () -> {
-            LockAndCallback lockAndCallback = new LockAndCallback();
-            StateMachineInstance inst = stateMachineEngine.startAsync(stateMachineName, null, paramMap, lockAndCallback.getCallback());
-            lockAndCallback.waittingForFinish(inst);
-
-            GlobalTransaction globalTransaction = getGlobalTransaction(inst);
-            Assertions.assertNotNull(globalTransaction);
-            System.out.println("====== GlobalStatus: " + globalTransaction.getStatus());
-
-            // waiting for global transaction recover
-            while (!(ExecutionStatus.SU.equals(inst.getStatus())
-                    && GlobalStatus.Finished.equals(globalTransaction.getStatus()))) {
-                System.out.println("====== GlobalStatus: " + globalTransaction.getStatus());
-                System.out.println("====== StateMachineInstanceStatus: " + inst.getStatus());
-                Thread.sleep(2500);
-                inst = stateMachineEngine.getStateMachineConfig().getStateLogStore().getStateMachineInstance(inst.getId());
-            }
-
-            Assertions.assertEquals(ExecutionStatus.SU, inst.getStatus());
-            Assertions.assertNull(inst.getCompensationStatus());
-        });
-    }
-}
diff --git a/tm/src/test/java/io/seata/tm/api/DefaultFailureHandlerImplTest.java b/tm/src/test/java/io/seata/tm/api/DefaultFailureHandlerImplTest.java
deleted file mode 100644
index cb387d5a..00000000
--- a/tm/src/test/java/io/seata/tm/api/DefaultFailureHandlerImplTest.java
+++ /dev/null
@@ -1,137 +0,0 @@
-/*
- *  Copyright 1999-2019 Seata.io Group.
- *
- *  Licensed under the Apache License, Version 2.0 (the "License");
- *  you may not use this file except in compliance with the License.
- *  You may obtain a copy of the License at
- *
- *       http://www.apache.org/licenses/LICENSE-2.0
- *
- *  Unless required by applicable law or agreed to in writing, software
- *  distributed under the License is distributed on an "AS IS" BASIS,
- *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- *  See the License for the specific language governing permissions and
- *  limitations under the License.
- */
-package io.seata.tm.api;
-
-
-import io.netty.util.HashedWheelTimer;
-import io.seata.core.context.RootContext;
-import io.seata.core.exception.TransactionException;
-import io.seata.core.model.GlobalStatus;
-import io.seata.core.model.TransactionManager;
-import io.seata.tm.TransactionManagerHolder;
-import io.seata.tm.api.transaction.MyRuntimeException;
-import org.junit.jupiter.api.Assertions;
-import org.junit.jupiter.api.BeforeAll;
-import org.junit.jupiter.api.Test;
-import org.slf4j.Logger;
-import org.slf4j.LoggerFactory;
-
-import java.lang.reflect.Field;
-
-/**
- * <AUTHOR>
- */
-class DefaultFailureHandlerImplTest {
-    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultFailureHandlerImplTest.class);
-
-    private static final String DEFAULT_XID = "**********";
-    private static GlobalStatus globalStatus = GlobalStatus.Begin;
-
-    @BeforeAll
-    public static void init() {
-
-        TransactionManagerHolder.set(new TransactionManager() {
-            @Override
-            public String begin(String applicationId, String transactionServiceGroup, String name, int timeout)
-                    throws TransactionException {
-                return DEFAULT_XID;
-            }
-
-            @Override
-            public GlobalStatus commit(String xid) throws TransactionException {
-                return GlobalStatus.Committed;
-            }
-
-            @Override
-            public GlobalStatus rollback(String xid) throws TransactionException {
-                return GlobalStatus.Rollbacked;
-            }
-
-            @Override
-            public GlobalStatus getStatus(String xid) throws TransactionException {
-                return globalStatus;
-            }
-
-            @Override
-            public GlobalStatus globalReport(String xid, GlobalStatus globalStatus) throws TransactionException {
-                return globalStatus;
-            }
-        });
-    }
-
-    @Test
-    void onBeginFailure() {
-        RootContext.bind(DEFAULT_XID);
-        GlobalTransaction tx = GlobalTransactionContext.getCurrentOrCreate();
-        FailureHandler failureHandler = new DefaultFailureHandlerImpl();
-        failureHandler.onBeginFailure(tx, new MyRuntimeException("").getCause());
-    }
-
-    @Test
-    void onCommitFailure() throws Exception{
-
-        RootContext.bind(DEFAULT_XID);
-        GlobalTransaction tx = GlobalTransactionContext.getCurrentOrCreate();
-        FailureHandler failureHandler = new DefaultFailureHandlerImpl();
-        failureHandler.onCommitFailure(tx, new MyRuntimeException("").getCause());
-
-        // get timer
-        Class<?> c = Class.forName("io.seata.tm.api.DefaultFailureHandlerImpl");
-        Field field = c.getDeclaredField("TIMER");
-        field.setAccessible(true);
-        HashedWheelTimer timer = (HashedWheelTimer) field.get(failureHandler);
-        // assert timer pendingCount: first time is 1
-        Long pendingTimeout = timer.pendingTimeouts();
-        Assertions.assertEquals(pendingTimeout,1L);
-        //set globalStatus
-        globalStatus= GlobalStatus.Committed;
-        Thread.sleep(25*1000L);
-        pendingTimeout = timer.pendingTimeouts();
-        LOGGER.info("pendingTimeout {}" ,pendingTimeout);
-        //all timer is done
-        Assertions.assertEquals(pendingTimeout,0L);
-    }
-
-    @Test
-    void onRollbackFailure() throws Exception {
-
-
-        RootContext.bind(DEFAULT_XID);
-        GlobalTransaction tx = GlobalTransactionContext.getCurrentOrCreate();
-        FailureHandler failureHandler = new DefaultFailureHandlerImpl();
-        failureHandler.onRollbackFailure(tx, new MyRuntimeException("").getCause());
-
-        // get timer
-        Class<?> c = Class.forName("io.seata.tm.api.DefaultFailureHandlerImpl");
-        Field field = c.getDeclaredField("TIMER");
-        field.setAccessible(true);
-        HashedWheelTimer timer = (HashedWheelTimer) field.get(failureHandler);
-        // assert timer pendingCount: first time is 1
-        Long pendingTimeout = timer.pendingTimeouts();
-        Assertions.assertEquals(pendingTimeout,1L);
-        //set globalStatus
-        globalStatus= GlobalStatus.Rollbacked;
-        Thread.sleep(25*1000L);
-        pendingTimeout = timer.pendingTimeouts();
-        LOGGER.info("pendingTimeout {}" ,pendingTimeout);
-        //all timer is done
-        Assertions.assertEquals(pendingTimeout,0L);
-
-
-    }
-
-
-}
diff --git a/server/src/test/java/io/seata/server/coordinator/DefaultCoordinatorMetricsTest.java b/server/src/test/java/io/seata/server/coordinator/DefaultCoordinatorMetricsTest.java
deleted file mode 100644
index 2650548b..00000000
--- a/server/src/test/java/io/seata/server/coordinator/DefaultCoordinatorMetricsTest.java
+++ /dev/null
@@ -1,166 +0,0 @@
-/*
- *  Copyright 1999-2019 Seata.io Group.
- *
- *  Licensed under the Apache License, Version 2.0 (the "License");
- *  you may not use this file except in compliance with the License.
- *  You may obtain a copy of the License at
- *
- *       http://www.apache.org/licenses/LICENSE-2.0
- *
- *  Unless required by applicable law or agreed to in writing, software
- *  distributed under the License is distributed on an "AS IS" BASIS,
- *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- *  See the License for the specific language governing permissions and
- *  limitations under the License.
- */
-package io.seata.server.coordinator;
-
-import java.io.IOException;
-import java.util.HashMap;
-import java.util.Map;
-
-import io.seata.common.loader.EnhancedServiceLoader;
-import io.seata.core.exception.TransactionException;
-import io.seata.core.protocol.transaction.GlobalBeginRequest;
-import io.seata.core.protocol.transaction.GlobalBeginResponse;
-import io.seata.core.protocol.transaction.GlobalCommitRequest;
-import io.seata.core.protocol.transaction.GlobalCommitResponse;
-import io.seata.core.protocol.transaction.GlobalRollbackRequest;
-import io.seata.core.protocol.transaction.GlobalRollbackResponse;
-import io.seata.core.rpc.RpcContext;
-import io.seata.metrics.Measurement;
-import io.seata.server.metrics.MetricsManager;
-import io.seata.server.session.SessionHolder;
-import org.junit.jupiter.api.Assertions;
-import org.junit.jupiter.api.BeforeAll;
-import org.junit.jupiter.api.Disabled;
-import org.junit.jupiter.api.Test;
-import org.springframework.boot.test.context.SpringBootTest;
-import org.springframework.context.ApplicationContext;
-
-import static io.seata.server.coordinator.DefaultCoordinatorTest.MockServerMessageSender;
-
-/**
- * Test Metrics
- *
- * <AUTHOR>
- */
-@SpringBootTest
-@Disabled
-public class DefaultCoordinatorMetricsTest {
-
-    @BeforeAll
-    public static void setUp(ApplicationContext context) throws InterruptedException {
-        EnhancedServiceLoader.unloadAll();
-        //wait for the pre test asynCommit operation finished.
-        Thread.sleep(2000);
-        MetricsManager.get().getRegistry().clearUp();
-    }
-
-    @Test
-    public void test() throws IOException, TransactionException, InterruptedException {
-        DefaultCoordinator coordinator = DefaultCoordinator.getInstance(null);
-        coordinator.setRemotingServer(new MockServerMessageSender());
-        SessionHolder.init(null);
-        try {
-            //start a transaction
-            GlobalBeginRequest request = new GlobalBeginRequest();
-            request.setTransactionName("test_transaction");
-            GlobalBeginResponse response = new GlobalBeginResponse();
-            coordinator.doGlobalBegin(request, response, new RpcContext());
-            Thread.sleep(2000);
-            Map<String, Measurement> measurements = new HashMap<>();
-            MetricsManager.get().getRegistry().measure().forEach(
-                measurement -> measurements.put(measurement.getId().toString(), measurement));
-
-            Assertions.assertEquals(1, measurements.size());
-            Assertions.assertEquals(1,
-                measurements.get("seata.transaction(applicationId=null,group=null,meter=counter,role=tc,status=active)")
-                    .getValue(), 0);
-
-            //commit this transaction
-            GlobalCommitRequest commitRequest = new GlobalCommitRequest();
-            commitRequest.setXid(response.getXid());
-            coordinator.doGlobalCommit(commitRequest, new GlobalCommitResponse(), new RpcContext());
-
-            measurements.clear();
-            //we need sleep for a short while because default canBeCommittedAsync() is true
-            Thread.sleep(2000);
-
-            MetricsManager.get().getRegistry().measure().forEach(
-                measurement -> measurements.put(measurement.getId().toString(), measurement));
-            Assertions.assertEquals(9, measurements.size());
-            Assertions.assertEquals(0,
-                measurements.get("seata.transaction(applicationId=null,group=null,meter=counter,role=tc,status=active)")
-                    .getValue(), 0);
-            Assertions.assertEquals(1, measurements
-                .get("seata.transaction(applicationId=null,group=null,meter=counter,role=tc,status=committed)")
-                .getValue(), 0);
-            Assertions.assertEquals(1, measurements.get(
-                "seata.transaction(applicationId=null,group=null,meter=summary,role=tc,statistic=count,"
-                    + "status=committed)")
-                .getValue(), 0);
-            Assertions.assertEquals(1, measurements.get(
-                "seata.transaction(applicationId=null,group=null,meter=summary,role=tc,statistic=total,"
-                    + "status=committed)")
-                .getValue(), 0);
-            Assertions.assertEquals(1, measurements.get(
-                "seata.transaction(applicationId=null,group=null,meter=timer,role=tc,statistic=count,status=committed)")
-                .getValue(), 0);
-
-            //start another new transaction
-            request = new GlobalBeginRequest();
-            request.setTransactionName("test_transaction_2");
-            response = new GlobalBeginResponse();
-            coordinator.doGlobalBegin(request, response, new RpcContext());
-
-            //rollback this transaction
-            GlobalRollbackRequest rollbackRequest = new GlobalRollbackRequest();
-            rollbackRequest.setXid(response.getXid());
-            coordinator.doGlobalRollback(rollbackRequest, new GlobalRollbackResponse(), new RpcContext());
-
-            measurements.clear();
-            Thread.sleep(2000);
-            MetricsManager.get().getRegistry().measure().forEach(
-                measurement -> measurements.put(measurement.getId().toString(), measurement));
-            Assertions.assertEquals(17, measurements.size());
-            Assertions.assertEquals(0,
-                measurements.get("seata.transaction(applicationId=null,group=null,meter=counter,role=tc,status=active)")
-                    .getValue(), 0);
-
-            Assertions.assertEquals(1, measurements
-                .get("seata.transaction(applicationId=null,group=null,meter=counter,role=tc,status=committed)")
-                .getValue(), 0);
-            Assertions.assertEquals(0, measurements.get(
-                "seata.transaction(applicationId=null,group=null,meter=summary,role=tc,statistic=count,"
-                    + "status=committed)")
-                .getValue(), 0);
-            Assertions.assertEquals(0, measurements.get(
-                "seata.transaction(applicationId=null,group=null,meter=summary,role=tc,statistic=total,"
-                    + "status=committed)")
-                .getValue(), 0);
-            Assertions.assertEquals(0, measurements.get(
-                "seata.transaction(applicationId=null,group=null,meter=timer,role=tc,statistic=count,status=committed)")
-                .getValue(), 0);
-
-            Assertions.assertEquals(1, measurements
-                .get("seata.transaction(applicationId=null,group=null,meter=counter,role=tc,status=rollbacked)")
-                .getValue(), 0);
-            Assertions.assertEquals(1, measurements.get(
-                "seata.transaction(applicationId=null,group=null,meter=summary,role=tc,statistic=count,"
-                    + "status=rollbacked)")
-                .getValue(), 0);
-            Assertions.assertEquals(1, measurements.get(
-                "seata.transaction(applicationId=null,group=null,meter=summary,role=tc,statistic=total,"
-                    + "status=rollbacked)")
-                .getValue(), 0);
-            Assertions.assertEquals(1, measurements.get(
-                "seata.transaction(applicationId=null,group=null,meter=timer,role=tc,statistic=count,"
-                    + "status=rollbacked)")
-                .getValue(), 0);
-        } finally {
-            // call SpringContextShutdownHook
-        }
-    }
-
-}
diff --git a/server/src/test/java/io/seata/server/coordinator/DefaultCoreTest.java b/server/src/test/java/io/seata/server/coordinator/DefaultCoreTest.java
deleted file mode 100644
index fafc13af..00000000
--- a/server/src/test/java/io/seata/server/coordinator/DefaultCoreTest.java
+++ /dev/null
@@ -1,389 +0,0 @@
-/*
- *  Copyright 1999-2019 Seata.io Group.
- *
- *  Licensed under the Apache License, Version 2.0 (the "License");
- *  you may not use this file except in compliance with the License.
- *  You may obtain a copy of the License at
- *
- *       http://www.apache.org/licenses/LICENSE-2.0
- *
- *  Unless required by applicable law or agreed to in writing, software
- *  distributed under the License is distributed on an "AS IS" BASIS,
- *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- *  See the License for the specific language governing permissions and
- *  limitations under the License.
- */
-package io.seata.server.coordinator;
-
-import java.util.Collection;
-import java.util.stream.Stream;
-
-import io.seata.core.exception.TransactionException;
-import io.seata.core.model.BranchStatus;
-import io.seata.core.model.BranchType;
-import io.seata.core.model.GlobalStatus;
-import io.seata.core.rpc.RemotingServer;
-import io.seata.server.session.BranchSession;
-import io.seata.server.session.GlobalSession;
-import io.seata.server.session.SessionHelper;
-import io.seata.server.session.SessionHolder;
-import io.seata.server.store.StoreConfig.SessionMode;
-import org.junit.jupiter.api.AfterAll;
-import org.junit.jupiter.api.AfterEach;
-import org.junit.jupiter.api.Assertions;
-import org.junit.jupiter.api.BeforeAll;
-import org.junit.jupiter.api.Test;
-import org.junit.jupiter.params.ParameterizedTest;
-import org.junit.jupiter.params.provider.Arguments;
-import org.junit.jupiter.params.provider.MethodSource;
-import org.springframework.boot.test.context.SpringBootTest;
-import org.springframework.context.ApplicationContext;
-
-/**
- * The type Default core test.
- *
- * <AUTHOR> @gmail.com
- */
-@SpringBootTest
-public class DefaultCoreTest {
-
-    private static DefaultCore core;
-    private static RemotingServer remotingServer;
-
-    private static final String applicationId = "demo-child-app";
-
-    private static final String txServiceGroup = "default_tx_group";
-
-    private static final String txName = "tx-1";
-
-    private static final int timeout = 3000;
-
-    private static final String resourceId = "tb_1";
-
-    private static final String clientId = "c_1";
-
-    private static final String lockKeys_1 = "tb_11:11";
-
-    private static final String lockKeys_2 = "tb_12:12";
-
-    private static final String applicationData = "{\"data\":\"test\"}";
-
-    private GlobalSession globalSession;
-
-    /**
-     * Init session manager.
-     *
-     * @throws Exception the exception
-     */
-    @BeforeAll
-    public static void initSessionManager(ApplicationContext context) throws Exception {
-        SessionHolder.init(SessionMode.FILE);
-        remotingServer = new DefaultCoordinatorTest.MockServerMessageSender();
-        core = new DefaultCore(remotingServer);
-    }
-
-    /**
-     * Destroy session manager.
-     */
-    @AfterAll
-    public static void destroySessionManager() {
-        SessionHolder.destroy();
-    }
-
-    /**
-     * Clean.
-     *
-     * @throws TransactionException the transaction exception
-     */
-    @AfterEach
-    public synchronized void clean() throws TransactionException, InterruptedException {
-        if (globalSession != null) {
-            int n = 10;
-            while (n-- > 0) {
-                try {
-                    globalSession.end();
-                    return;
-                } catch (TransactionException e) {
-                    throw e;
-                } catch (Exception e) {
-                    e.printStackTrace();
-                    Thread.sleep(100);
-                    if (n == 0) {
-                       throw e;
-                    }
-                }
-            }
-            globalSession = null;
-        }
-    }
-
-    /**
-     * Branch register test.
-     *
-     * @param xid the xid
-     * @throws Exception the exception
-     */
-    @ParameterizedTest
-    @MethodSource("xidProvider")
-    public void branchRegisterTest(String xid) throws Exception {
-        core.branchRegister(BranchType.AT, resourceId, clientId, xid, "abc", lockKeys_1);
-        globalSession = SessionHolder.findGlobalSession(xid);
-        Assertions.assertEquals(globalSession.getSortedBranches().size(), 1);
-    }
-
-    /**
-     * Branch report test.
-     *
-     * @param xid      the xid
-     * @param branchId the branch id
-     * @throws Exception the exception
-     */
-    @ParameterizedTest
-    @MethodSource("xidAndBranchIdProvider")
-    public void branchReportTest(String xid, Long branchId) throws Exception {
-        core.branchReport(BranchType.AT, xid, branchId, BranchStatus.PhaseOne_Done, applicationData);
-        globalSession = SessionHolder.findGlobalSession(xid);
-        BranchSession branchSession = globalSession.getBranch(branchId);
-        Assertions.assertEquals(branchSession.getStatus(), BranchStatus.PhaseOne_Done);
-    }
-
-    /**
-     * Begin test.
-     *
-     * @throws Exception the exception
-     */
-    @Test
-    public void beginTest() throws Exception {
-        String xid = core.begin(applicationId, txServiceGroup, txName, timeout);
-        globalSession = SessionHolder.findGlobalSession(xid);
-        Assertions.assertNotNull(globalSession);
-
-    }
-
-    /**
-     * Commit test.
-     *
-     * @param xid the xid
-     * @throws Exception the exception
-     */
-    @ParameterizedTest
-    @MethodSource("xidProvider")
-    public void commitTest(String xid) throws Exception {
-        GlobalStatus globalStatus = core.commit(xid);
-        Assertions.assertNotEquals(globalStatus, GlobalStatus.Begin);
-    }
-
-    /**
-     * Do global commit test.
-     *
-     * @param xid the xid
-     * @throws Exception the exception
-     */
-    @ParameterizedTest
-    @MethodSource("xidProvider")
-    public void doGlobalCommitCommitTest(String xid) throws Exception {
-        globalSession = SessionHolder.findGlobalSession(xid);
-        BranchSession branchSession = SessionHelper.newBranchByGlobal(globalSession, BranchType.XA, resourceId,
-            applicationData, "t1:1", clientId);
-        globalSession.addBranch(branchSession);
-        globalSession.changeBranchStatus(branchSession, BranchStatus.PhaseOne_Done);
-        core.mockCore(BranchType.XA,
-            new MockCore(BranchStatus.PhaseTwo_Committed, BranchStatus.PhaseOne_Done));
-        core.doGlobalCommit(globalSession, true);
-        Assertions.assertEquals(globalSession.getStatus(), GlobalStatus.Committed);
-    }
-
-    /**
-     * Do global commit test.
-     *
-     * @param xid the xid
-     * @throws Exception the exception
-     */
-    @ParameterizedTest
-    @MethodSource("xidProvider")
-    public void doGlobalCommitUnretryableTest(String xid) throws Exception {
-        globalSession = SessionHolder.findGlobalSession(xid);
-        BranchSession branchSession = SessionHelper.newBranchByGlobal(globalSession, BranchType.TCC, resourceId,
-            applicationData, "t1:1", clientId);
-        globalSession.addBranch(branchSession);
-        globalSession.changeBranchStatus(branchSession, BranchStatus.PhaseOne_Done);
-        core.mockCore(BranchType.TCC,
-                new MockCore(BranchStatus.PhaseTwo_CommitFailed_Unretryable, BranchStatus.PhaseOne_Done));
-        core.doGlobalCommit(globalSession, false);
-        Assertions.assertEquals(globalSession.getStatus(), GlobalStatus.CommitFailed);
-    }
-
-    /**
-     * Do global commit test.
-     *
-     * @param xid the xid
-     * @throws Exception the exception
-     */
-    @ParameterizedTest
-    @MethodSource("xidProvider")
-    public void doGlobalCommitExpTest(String xid) throws Exception {
-        globalSession = SessionHolder.findGlobalSession(xid);
-        BranchSession branchSession = SessionHelper.newBranchByGlobal(globalSession, BranchType.XA, resourceId,
-            applicationData, "t1:1", clientId);
-        globalSession.addBranch(branchSession);
-        globalSession.changeBranchStatus(branchSession, BranchStatus.PhaseOne_Done);
-        core.mockCore(BranchType.XA,
-                new MockCore(BranchStatus.PhaseOne_Timeout, BranchStatus.PhaseOne_Done));
-        core.doGlobalCommit(globalSession, false);
-        Assertions.assertEquals(globalSession.getStatus(), GlobalStatus.CommitRetrying);
-    }
-
-    /**
-     * Roll back test.
-     *
-     * @param xid the xid
-     * @throws Exception the exception
-     */
-    @ParameterizedTest
-    @MethodSource("xidProvider")
-    public void rollBackTest(String xid) throws Exception {
-        GlobalStatus globalStatus = core.rollback(xid);
-        Assertions.assertEquals(globalStatus, GlobalStatus.Rollbacked);
-    }
-
-    /**
-     * Do global roll back test.
-     *
-     * @param xid the xid
-     * @throws Exception the exception
-     */
-    @ParameterizedTest
-    @MethodSource("xidProvider")
-    public void doGlobalRollBackRollbackedTest(String xid) throws Exception {
-        globalSession = SessionHolder.findGlobalSession(xid);
-        BranchSession branchSession = SessionHelper.newBranchByGlobal(globalSession, BranchType.AT, resourceId,
-            applicationData, "t1:1", clientId);
-        globalSession.addBranch(branchSession);
-        globalSession.changeBranchStatus(branchSession, BranchStatus.PhaseOne_Done);
-        core.mockCore(BranchType.AT,
-                new MockCore(BranchStatus.PhaseTwo_Committed, BranchStatus.PhaseTwo_Rollbacked));
-        core.doGlobalRollback(globalSession, false);
-        Assertions.assertEquals(globalSession.getStatus(), GlobalStatus.Rollbacked);
-    }
-
-    /**
-     * Do global roll back test.
-     *
-     * @param xid the xid
-     * @throws Exception the exception
-     */
-    @ParameterizedTest
-    @MethodSource("xidProvider")
-    public void doGlobalRollBackUnretryableTest(String xid) throws Exception {
-        globalSession = SessionHolder.findGlobalSession(xid);
-        BranchSession branchSession = SessionHelper.newBranchByGlobal(globalSession, BranchType.AT, resourceId,
-            applicationData, "t1:1", clientId);
-        globalSession.addBranch(branchSession);
-        globalSession.changeBranchStatus(branchSession, BranchStatus.PhaseOne_Done);
-        core.mockCore(BranchType.AT, new MockCore(BranchStatus.PhaseTwo_Committed,
-            BranchStatus.PhaseTwo_RollbackFailed_Unretryable));
-        core.doGlobalRollback(globalSession, false);
-        Assertions.assertEquals(globalSession.getStatus(), GlobalStatus.RollbackFailed);
-    }
-
-    /**
-     * Do global roll back test.
-     *
-     * @param xid the xid
-     * @throws Exception the exception
-     */
-    @ParameterizedTest
-    @MethodSource("xidProvider")
-    public void doGlobalRollBackRetryableExpTest(String xid) throws Exception {
-        globalSession = SessionHolder.findGlobalSession(xid);
-        BranchSession branchSession = SessionHelper.newBranchByGlobal(globalSession, BranchType.AT, resourceId,
-            applicationData, "t1:1", clientId);
-        globalSession.addBranch(branchSession);
-        globalSession.changeBranchStatus(branchSession, BranchStatus.PhaseOne_Done);
-        core.mockCore(BranchType.AT, new MockCore(BranchStatus.PhaseTwo_Committed,
-            BranchStatus.PhaseTwo_RollbackFailed_Retryable));
-        core.doGlobalRollback(globalSession, false);
-        Assertions.assertEquals(globalSession.getStatus(), GlobalStatus.RollbackRetrying);
-    }
-
-    /**
-     * Xid provider object [ ] [ ].
-     *
-     * @return the object [ ] [ ]
-     * @throws Exception the exception
-     */
-    static Stream<Arguments> xidProvider() throws Exception {
-        String xid = core.begin(applicationId, txServiceGroup, txName, timeout);
-        Assertions.assertNotNull(xid);
-        return Stream.of(
-            Arguments.of(xid)
-        );
-    }
-
-    /**
-     * Xid and branch id provider object [ ] [ ].
-     *
-     * @return the object [ ] [ ]
-     * @throws Exception the exception
-     */
-    static Stream<Arguments> xidAndBranchIdProvider() throws Exception {
-        String xid = core.begin(applicationId, txServiceGroup, txName, timeout);
-        Long branchId = core.branchRegister(BranchType.AT, resourceId, clientId, xid, null, lockKeys_2);
-        Assertions.assertNotNull(xid);
-        Assertions.assertTrue(branchId != 0);
-        return Stream.of(
-            Arguments.of(xid, branchId)
-        );
-    }
-
-    /**
-     * Release session manager.
-     *
-     * @throws Exception the exception
-     */
-    @AfterEach
-    public void releaseSessionManager() throws Exception {
-        Collection<GlobalSession> globalSessions = SessionHolder.getRootSessionManager().allSessions();
-        Collection<GlobalSession> asyncGlobalSessions = SessionHolder.getRootSessionManager().allSessions();
-        for (GlobalSession asyncGlobalSession : asyncGlobalSessions) {
-            asyncGlobalSession.closeAndClean();
-        }
-        for (GlobalSession globalSession : globalSessions) {
-            globalSession.closeAndClean();
-        }
-    }
-
-    private static class MockCore extends AbstractCore {
-
-        private BranchStatus commitStatus;
-        private BranchStatus rollbackStatus;
-
-        /**
-         * Instantiates a new Mock resource manager inbound.
-         *
-         * @param commitStatus   the commit status
-         * @param rollbackStatus the rollback status
-         */
-        public MockCore(BranchStatus commitStatus, BranchStatus rollbackStatus) {
-            super(new DefaultCoordinatorTest.MockServerMessageSender());
-            this.commitStatus = commitStatus;
-            this.rollbackStatus = rollbackStatus;
-        }
-
-        @Override
-        public BranchStatus branchCommit(GlobalSession globalSession, BranchSession branchSession) throws TransactionException {
-            return commitStatus;
-        }
-
-        @Override
-        public BranchStatus branchRollback(GlobalSession globalSession, BranchSession branchSession) throws TransactionException {
-            return rollbackStatus;
-        }
-
-        @Override
-        public BranchType getHandleBranchType() {
-            return BranchType.AT;
-        }
-    }
-
-}
diff --git a/server/src/test/java/io/seata/server/session/redis/RedisDistributedLockerTest.java b/server/src/test/java/io/seata/server/session/redis/RedisDistributedLockerTest.java
deleted file mode 100644
index ccb0c349..00000000
--- a/server/src/test/java/io/seata/server/session/redis/RedisDistributedLockerTest.java
+++ /dev/null
@@ -1,143 +0,0 @@
-/*
- *  Copyright 1999-2019 Seata.io Group.
- *
- *  Licensed under the Apache License, Version 2.0 (the "License");
- *  you may not use this file except in compliance with the License.
- *  You may obtain a copy of the License at
- *
- *       http://www.apache.org/licenses/LICENSE-2.0
- *
- *  Unless required by applicable law or agreed to in writing, software
- *  distributed under the License is distributed on an "AS IS" BASIS,
- *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- *  See the License for the specific language governing permissions and
- *  limitations under the License.
- */
-package io.seata.server.session.redis;
-
-import java.io.IOException;
-import java.net.UnknownHostException;
-
-import io.seata.common.XID;
-import io.seata.common.loader.EnhancedServiceLoader;
-import org.junit.jupiter.api.AfterAll;
-import org.junit.jupiter.api.Assertions;
-import org.junit.jupiter.api.BeforeAll;
-import org.junit.jupiter.api.Test;
-import org.springframework.boot.test.context.SpringBootTest;
-import org.springframework.context.ApplicationContext;
-import redis.clients.jedis.Jedis;
-import io.seata.core.store.DistributedLockDO;
-import io.seata.core.store.DistributedLocker;
-import io.seata.server.lock.distributed.DistributedLockerFactory;
-import io.seata.server.session.SessionHolder;
-import io.seata.server.storage.redis.JedisPooledFactory;
-import io.seata.server.store.StoreConfig.SessionMode;
-import static io.seata.server.store.StoreConfig.StoreMode;
-
-/**
- * @description redis distributed lock test
- *
- * <AUTHOR>
- * <AUTHOR>
- */
-@SpringBootTest
-public class RedisDistributedLockerTest {
-
-    private String retryRollbacking = "RetryRollbacking";
-    private String retryCommiting = "RetryCommiting";
-    private String lockValue = "*********:9081";
-    private static DistributedLocker distributedLocker;
-    private static Jedis jedis;
-
-    @BeforeAll
-    public static void start(ApplicationContext context) throws IOException {
-        EnhancedServiceLoader.unload(DistributedLocker.class);
-        MockRedisServer.getInstance();
-        DistributedLockerFactory.cleanLocker();
-        distributedLocker = DistributedLockerFactory.getDistributedLocker(StoreMode.REDIS.getName());
-        jedis = JedisPooledFactory.getJedisInstance();
-    }
-
-    @AfterAll
-    public static void after() throws IOException {
-        EnhancedServiceLoader.unload(DistributedLocker.class);
-        DistributedLockerFactory.cleanLocker();
-        DistributedLockerFactory.getDistributedLocker(StoreMode.FILE.getName());
-        jedis.close();
-    }
-
-    @Test
-    public void test_acquireScheduledLock_success() {
-        boolean acquire = distributedLocker.acquireLock(new DistributedLockDO(retryRollbacking, lockValue, 60000L));
-        Assertions.assertTrue(acquire);
-        String lockValueExisted = jedis.get(retryRollbacking);
-        Assertions.assertEquals(lockValue, lockValueExisted);
-        boolean release = distributedLocker.releaseLock(new DistributedLockDO(retryRollbacking, lockValue, null));
-        Assertions.assertTrue(release);
-        Assertions.assertNull(jedis.get(retryRollbacking));
-    }
-
-    @Test
-    public void test_acquireScheduledLock_success_() throws UnknownHostException {
-        SessionHolder.init(SessionMode.REDIS);
-        boolean accquire = SessionHolder.acquireDistributedLock(retryRollbacking);
-        Assertions.assertTrue(accquire);
-        String lockValueExisted = jedis.get(retryRollbacking);
-        Assertions.assertEquals(XID.getIpAddressAndPort(), lockValueExisted);
-        boolean release = SessionHolder.releaseDistributedLock(retryRollbacking);
-        Assertions.assertTrue(release);
-        Assertions.assertNull(jedis.get(retryRollbacking));
-    }
-
-    @Test
-    public void test_acquireLock_concurrent() {
-        //acquire the lock success
-        boolean accquire = distributedLocker.acquireLock(new DistributedLockDO(retryRollbacking, lockValue, 60000l));
-        Assertions.assertTrue(accquire);
-        String lockValueExisted = jedis.get(retryRollbacking);
-        Assertions.assertEquals(lockValue,lockValueExisted);
-
-        // concurrent acquire
-       for(int i = 0;i < 10;i++){
-           boolean b = distributedLocker.acquireLock(new DistributedLockDO(retryRollbacking, lockValue + i, 60000l));
-           Assertions.assertFalse(b);
-       }
-
-       //release the lock
-       boolean release = distributedLocker.releaseLock(new DistributedLockDO(retryRollbacking, lockValue ,null));
-       Assertions.assertTrue(release);
-       Assertions.assertNull(jedis.get(retryRollbacking));
-
-       // other acquire the lock success
-       boolean c = distributedLocker.acquireLock(new DistributedLockDO(retryRollbacking, lockValue + 1, 2000L));
-        Assertions.assertTrue(c);
-
-        //other2 acquire the lock failed
-        boolean d = distributedLocker.acquireLock(new DistributedLockDO(retryRollbacking, lockValue + 2, 2000L));
-        Assertions.assertFalse(d);
-
-       //sleep 60s
-        try {
-            Thread.sleep(2000);
-        } catch (InterruptedException e) {
-            e.printStackTrace();
-        }
-
-        //other2 acquire the lock
-        boolean e = distributedLocker.acquireLock(new DistributedLockDO(retryRollbacking, lockValue + 2, 60000l));
-        Assertions.assertTrue(e);
-
-        //clear
-        boolean f = distributedLocker.releaseLock(new DistributedLockDO(retryRollbacking, lockValue + 2,null));
-    }
-
-    @Test
-    public void test_acquireLock_false() {
-        String set = jedis.set(retryCommiting, lockValue);
-        Assertions.assertEquals("OK",set);
-        boolean acquire = distributedLocker.acquireLock(new DistributedLockDO(retryCommiting, lockValue, 60000l));
-        Assertions.assertFalse(acquire);
-    }
-
-}
diff --git a/discovery/seata-discovery-zk/src/test/java/io/seata/discovery/registry/zk/ZookeeperRegisterServiceImplTest.java b/discovery/seata-discovery-zk/src/test/java/io/seata/discovery/registry/zk/ZookeeperRegisterServiceImplTest.java
deleted file mode 100644
index 4698bbd0..00000000
--- a/discovery/seata-discovery-zk/src/test/java/io/seata/discovery/registry/zk/ZookeeperRegisterServiceImplTest.java
+++ /dev/null
@@ -1,106 +0,0 @@
-/*
- *  Copyright 1999-2019 Seata.io Group.
- *
- *  Licensed under the Apache License, Version 2.0 (the "License");
- *  you may not use this file except in compliance with the License.
- *  You may obtain a copy of the License at
- *
- *       http://www.apache.org/licenses/LICENSE-2.0
- *
- *  Unless required by applicable law or agreed to in writing, software
- *  distributed under the License is distributed on an "AS IS" BASIS,
- *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- *  See the License for the specific language governing permissions and
- *  limitations under the License.
- */
-package io.seata.discovery.registry.zk;
-
-import java.net.InetSocketAddress;
-import java.util.ArrayList;
-import java.util.List;
-import java.util.concurrent.CountDownLatch;
-import java.util.concurrent.TimeUnit;
-
-import io.seata.common.util.NetUtil;
-import io.seata.config.exception.ConfigNotFoundException;
-import org.I0Itec.zkclient.IZkChildListener;
-import org.I0Itec.zkclient.ZkClient;
-import org.apache.curator.test.TestingServer;
-import org.junit.jupiter.api.AfterAll;
-import org.junit.jupiter.api.Assertions;
-import org.junit.jupiter.api.BeforeAll;
-import org.junit.jupiter.api.Test;
-import org.junit.jupiter.api.function.Executable;
-
-/**
- * <AUTHOR> Zhang
- */
-public class ZookeeperRegisterServiceImplTest {
-    protected static TestingServer server = null;
-
-    @BeforeAll
-    public static void adBeforeClass() throws Exception {
-        server = new TestingServer(2181, true);
-        server.start();
-    }
-
-    @AfterAll
-    public static void adAfterClass() throws Exception {
-        if (server != null) {
-            server.stop();
-        }
-    }
-
-    ZookeeperRegisterServiceImpl service = (ZookeeperRegisterServiceImpl) new ZookeeperRegistryProvider().provide();
-
-    @Test
-    public void getInstance() {
-        ZookeeperRegisterServiceImpl service1 = ZookeeperRegisterServiceImpl.getInstance();
-        Assertions.assertEquals(service1, service);
-    }
-
-    @Test
-    public void buildZkTest() {
-        ZkClient client = service.buildZkClient("127.0.0.1:2181", 5000, 5000);
-        Assertions.assertTrue(client.exists("/zookeeper"));
-    }
-
-    @Test
-    public void testAll() throws Exception {
-        service.register(new InetSocketAddress(NetUtil.getLocalAddress(), 33333));
-
-        Assertions.assertThrows(ConfigNotFoundException.class, new Executable() {
-            @Override
-            public void execute() throws Throwable {
-                service.lookup("xxx");
-            }
-        });
-        List<InetSocketAddress> lookup2 = service.doLookup("default");
-        Assertions.assertEquals(1, lookup2.size());
-
-        final List<String> data = new ArrayList<>();
-        final CountDownLatch latch = new CountDownLatch(1);
-        IZkChildListener listener = (s, list) -> {
-            data.clear();
-            data.addAll(list);
-            latch.countDown();
-        };
-        service.subscribe("default", listener);
-        final CountDownLatch latch2 = new CountDownLatch(1);
-        final List<String> data2 = new ArrayList<>();
-        IZkChildListener listener2 = (s, list) -> {
-            data2.clear();
-            data2.addAll(list);
-            latch2.countDown();
-        };
-        service.subscribe("default", listener2);
-
-        service.unregister(new InetSocketAddress(NetUtil.getLocalAddress(), 33333));
-        latch2.await(1000, TimeUnit.MILLISECONDS);
-        Assertions.assertEquals(0, data2.size());
-
-        service.unsubscribe("default", listener);
-        service.unsubscribe("default", listener2);
-    }
-
-}

FROM amd64/ubuntu

# General installs
RUN apt -y update && apt -y upgrade
RUN apt -y install sudo wget git gcc g++ build-essential curl vim
RUN apt-get -y install python3.9
RUN cp /usr/bin/python3 /usr/bin/python
WORKDIR /code

# Copy in run script
RUN mkdir .hydra
COPY ./run.sh .hydra/run.sh
RUN chmod ugo+rwx .hydra/run.sh

# Support programming language: Java
RUN apt -y install openjdk-11-jdk

# Download and patch git repo
COPY ./seata.diff /data/seata.diff
ARG GIT_SHA=5b920e8eeab164c0e35625ad6ab7cd4a22c2c04b
RUN git clone https://github.com/seata/seata.git
RUN cp -rT /code/seata /code
RUN rm -fr /code/seata/
RUN git checkout ${GIT_SHA}
RUN git apply /data/seata.diff

# Install build system: Maven
ARG MAVEN_VERSION=3.9.4
ARG BASE_URL=https://apache.osuosl.org/maven/maven-3/${MAVEN_VERSION}/binaries
RUN mkdir -p /usr/share/maven /usr/share/maven/ref \
 && curl -fsSL -o /tmp/apache-maven.tar.gz ${BASE_URL}/apache-maven-${MAVEN_VERSION}-bin.tar.gz \
 && tar -xzf /tmp/apache-maven.tar.gz -C /usr/share/maven --strip-components=1 \
 && rm -f /tmp/apache-maven.tar.gz \
 && ln -s /usr/share/maven/bin/mvn /usr/bin/mvn
ENV MAVEN_HOME /usr/share/maven
ENV JAVA_HOME /usr/lib/jvm/java-11-openjdk-amd64

# In order to install dependences and cache test results
RUN mvn test

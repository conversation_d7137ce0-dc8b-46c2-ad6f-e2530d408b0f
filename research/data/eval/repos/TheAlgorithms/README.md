# Repos Belong to TheAlgorithms

## Python (All Algorithms implemented in Python)

The repo is cloned from `https://github.com/TheAlgorithms/Python.git`, here are details:

| version | #unit test | SHA     |
| ------- | ---------- | ------- |
| v1.0    | 1819       | 0a94380 |

note that this version is the not the as the version tag in the original repo.

**Reproduce the last image**:

```
docker build -t thepython -f Dockerfile .

docker run -it --rm thepython bash

# Tag the docker image with the target registry and version
docker image tag thepython au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/exec-eval/thealgorithms/python:v1.0

# Push the image to the CoreWeave registry
docker push au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/exec-eval/thealgorithms/python:v1.0
```

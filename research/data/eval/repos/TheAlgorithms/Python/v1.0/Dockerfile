FROM docker.io/amd64/ubuntu:22.04

# General installs
RUN apt -y update && apt -y upgrade
RUN apt -y install sudo wget git gcc g++ build-essential vim htop
RUN apt -y install python3 python3-pip
RUN ln -s /usr/bin/python3 /usr/local/bin/python
WORKDIR /code

# Copy in run script
RUN mkdir .hydra
COPY ./run.sh .hydra/run.sh

# Install the repo
ARG SHA=0a94380
RUN git clone https://github.com/TheAlgorithms/Python.git tmp-src && \
	    cd tmp-src && \
	    git checkout ${SHA}
RUN cp -rT /code/tmp-src /code
RUN rm -rf /code/tmp-src
RUN chmod +xxx /code/.hydra/run.sh

# Install the required Python environment
RUN python -m pip install pytest pytest-xdist pytest-cov
RUN python -m pip install -r requirements.txt

# Generate the coverage report
RUN pytest --cov=. --cov-report=xml --doctest-modules \
		 --ignore=machine_learning/xgboost_regressor.py \
		 arithmetic_analysis/ audio_filters/ \
		 backtracking/ bit_manipulation/ blockchain/ boolean_algebra/ \
		 cellular_automata/ ciphers/ conversions/ compression/burrows_wheeler.py compression/lz77.py compression/run_length_encoding.py \
		 data_structures/ divide_and_conquer/ dynamic_programming/ \
		 electronics/ file_transfer/ g* hashes knapsack l* m* n* \
		 o* physics/ project_euler/ \
		 s*
RUN mv coverage.xml /code/.hydra/coverage.xml

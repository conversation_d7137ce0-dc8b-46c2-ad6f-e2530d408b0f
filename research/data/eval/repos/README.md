# Instructions

The repo structure is organized as `organization_name/repo_name/our_in_house_version`.
- The `Dockerfile` file is mandatory to include, defining how to built the customized docker image for that repo.
- The `run.sh` file is generally required to run the unit test for the repo.

The built docker image should have all the repo codes under `/code` and the following files:
- `/code/.hydra/run.sh` to run the unit test and are expected to throw exit code of 1 when any unit test failed.
- (optional) `/code/.hydra/coverage.xml` for the Python repo with the source attribute as `/code` (this was currently used for Xuanyi's patch generation pipeline and was unofficial)


The general workflow to add a new repo:
- Step 1: find awesome repos on GitHub
- Step 2: create `Dockerfile` and put it into the right folder
  - You can find instructions and a template for creating Dockerfiles for different languages/build systems [in this guide on Notion.](https://www.notion.so/Guide-to-adding-Hydra-Repositories-5c17a915daee4bc8b3273fc6cc3d6aa3?pvs=4)
- Step 3: build docker image via `docker build -t temporary_test -f Dockerfile .`
- Step 4: run the image and log into it to test whether everything works file via `docker run -it --rm pyglove-1.0 bash`
- Step 5: (optional) make necessary changes to the repo to skip flaky, unstable, failed unit tests; record your changes via `git diff > <repo_name>.patch` and update `Dockerfile`.
- Step 6: iterate between `Step 3` to `Step 5` until everything works file.
- Step 7: push the final image with the version tag to our CW registry.
- Step 8: TODO(Xuanyi) run a simple notebook to make sure patches works well with the docker image, if anything failed, then go to re-iterate on between `Step 3` to `Step 5` to fix the issue and override the old image.


## Miscellaneous

Here are some useful docker commands:
```
# This can delete all dangling images at once
docker rmi $(docker images -f "dangling=true" -q)

# Build
docker run -it --rm [IMAGE_NAME] bash
```

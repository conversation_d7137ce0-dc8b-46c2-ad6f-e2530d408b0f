FROM docker.io/amd64/ubuntu:22.04

# General installs
RUN apt -y update && apt -y upgrade
RUN apt -y install sudo wget git gcc g++ build-essential vim htop
RUN apt -y install python3 python3-pip
RUN ln -s /usr/bin/python3 /usr/local/bin/python
WORKDIR /code

# Copy in run script
RUN mkdir .hydra
COPY ./run.sh .hydra/run.sh

# Install the repo
ARG SHA=e10d48137075fb23c322ff51598b542e132f9ceb
RUN git clone https://github.com/google/pyglove.git tmp-src && \
	    cd tmp-src && \
	    git checkout ${SHA}
RUN cp -rT /code/tmp-src /code
RUN rm -rf /code/tmp-src
RUN chmod +xxx /code/.hydra/run.sh

# Install the required Python environment
RUN python -m pip install pytest pytest-xdist pytest-cov
RUN python -m pip install -r requirements.txt
RUN python -m pip install -r docs/requirements.txt

# Generate the coverage report
RUN pytest -n auto --cov=. --cov-report=xml
RUN mv coverage.xml .hydra/

FROM amd64/ubuntu

# General installs
RUN apt -y update && apt -y upgrade
RUN apt -y install sudo wget git gcc g++ build-essential curl vim emacs
RUN apt-get -y install python3.9
RUN cp /usr/bin/python3 /usr/bin/python
WORKDIR /code

# Copy in run script
RUN mkdir .hydra
COPY ./run.sh .hydra/run.sh
RUN chmod ugo+rwx .hydra/run.sh

# Support programming language: Java
RUN apt -y install openjdk-8-jdk

# Install git repo and apply patch
COPY ./guava.diff /data/guava.diff
ARG GIT_SHA=068e94d5dc81b6c4eac9941c474295294324a520
RUN git clone https://github.com/google/guava.git guava_repo
RUN cp -rT /code/guava_repo/ /code
RUN rm -fr /code/guava_repo
RUN git checkout ${GIT_SHA}
RUN git apply /data/guava.diff

# Install build system: Maven
ARG MAVEN_VERSION=3.9.4
ARG BASE_URL=https://apache.osuosl.org/maven/maven-3/${MAVEN_VERSION}/binaries
RUN mkdir -p /usr/share/maven /usr/share/maven/ref \
 && curl -fsSL -o /tmp/apache-maven.tar.gz ${BASE_URL}/apache-maven-${MAVEN_VERSION}-bin.tar.gz \
 && tar -xzf /tmp/apache-maven.tar.gz -C /usr/share/maven --strip-components=1 \
 && rm -f /tmp/apache-maven.tar.gz \
 && ln -s /usr/share/maven/bin/mvn /usr/bin/mvn
ENV MAVEN_HOME /usr/share/maven
ENV JAVA_HOME /usr/lib/jvm/java-8-openjdk-amd64

# In order to install dependences and cache test results
RUN mvn test

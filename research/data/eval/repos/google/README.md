# Repos Belong to the Google Org

## Py<PERSON>love (Manipulating Python Programs)

The repo is cloned from `https://github.com/google/pyglove.git`, here are details:

| version | #unit test | SHA |
| ------- | ---------- | --- |
| v1.0    | 1346       | e10d48137075fb23c322ff51598b542e132f9ceb |

note that this version is the not the as the version tag in the original repo.

**Reproduce the last image**:

```
docker build -t pyglove-1.0 -f Dockerfile .

docker run -it --rm pyglove-1.0 bash

# Tag the docker image with the target registry and version
docker image tag pyglove-1.0 au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/exec-eval/google/pyglove:v1.0

# Push the image to the CoreWeave registry
docker push au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/exec-eval/google/pyglove:v1.0
```

## guava (Google core libraries for Java)

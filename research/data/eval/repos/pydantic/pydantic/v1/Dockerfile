FROM docker.io/amd64/ubuntu:22.04

# General installs
RUN apt -y update && apt -y upgrade
RUN apt -y install sudo wget git gcc g++ build-essential vim htop
RUN apt -y install python3 python3-pip
RUN ln -s /usr/bin/python3 /usr/local/bin/python
WORKDIR /code

# Copy in run script
RUN mkdir .hydra
COPY ./run.sh .hydra/run.sh

# Install the repo
ARG SHA=26f0eab8ed0aef014db5b86767d3b52b091f0af4
RUN git clone https://github.com/pydantic/pydantic.git tmp-src && \
	    cd tmp-src && \
	    git checkout ${SHA}
RUN cd /
RUN cp -rT /code/tmp-src /code
RUN rm -rf /code/tmp-src
RUN chmod +xxx /code/.hydra/run.sh

# Install the required Python environment
RUN python -m pip install pytest pytest-xdist pytest-cov
RUN python -m pip install "typing-extensions>=4.6.1"
RUN python -m pip install "annotated-types>=0.4.0"
RUN python -m pip install "pydantic-core==2.6.3"
RUN python -m pip install pdm
RUN pdm install -G linting -G email
RUN pdm install -G docs
RUN ln -s .venv/lib/python*/site-packages/pydantic_core pydantic_core
RUN ln -s .venv/lib/python*/site-packages/pydantic_settings pydantic_settings
RUN ln -s .venv/lib/python*/site-packages/pydantic_extra_types pydantic_extra_types
RUN pdm run mkdocs build
RUN pdm install -G testing
RUN pdm install -G testing-extra

# Generate the coverage report
RUN pdm run coverage erase
RUN echo "[run]\nsource = /code" > /code/.coveragerc
RUN pdm run coverage run -m pytest --durations=10 --ignore=tests/mypy/ --ignore=tests/test_docs.py
RUN pdm run coverage xml -o .hydra/coverage.xml

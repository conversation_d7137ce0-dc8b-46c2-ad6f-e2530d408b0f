# Repos Belong to the Pydantic org

## Pydantic

The repo is cloned from `https://github.com/pydantic/pydantic.git`, here are details:

| version | #unit tests | SHA |
| ------- | ----------- |  --- |
| v1.0    | 3790 passed + 7 xfailed | 26f0eab8ed0aef014db5b86767d3b52b091f0af4 |

**Reproduce the last image**:

```
docker build -t pydantic-1.0 -f Dockerfile .

docker run -it --rm pydantic-1.0 bash

# Tag the docker image with the target registry and version
docker image tag pydantic-1.0 au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/exec-eval/pydantic/pydantic:v1.0

# Push the image to the CoreWeave registry
docker push au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/exec-eval/pydantic/pydantic:v1.0
```

FROM amd64/ubuntu

# General installs
RUN apt -y update && apt -y upgrade
RUN apt -y install sudo wget git gcc g++ build-essential curl vim
RUN apt-get -y install python3.9
RUN cp /usr/bin/python3 /usr/bin/python
WORKDIR /code

# Copy in run script
RUN mkdir .hydra
COPY ./run.sh .hydra/run.sh
RUN chmod ugo+rwx .hydra/run.sh

# Support programming language: Java
RUN apt -y install openjdk-11-jdk

# Install Android SDK
RUN apt -y install android-sdk
RUN apt -y install sdkmanager
ENV ANDROID_HOME /usr/lib/android-sdk
RUN yes | sdkmanager --licenses

# Download and patch git repo
COPY ./epoxy.diff /data/epoxy.diff
ARG GIT_SHA=9a43af4d043e71f29afe4cbeadf8c008740a5184
RUN git clone https://github.com/airbnb/epoxy.git
RUN cp -rT /code/epoxy /code
RUN rm -fr /code/epoxy/
RUN git checkout ${GIT_SHA}
RUN git apply /data/epoxy.diff

# Install gradle
RUN ./gradlew --no-daemon

# Build and test
RUN ./gradlew test --no-daemon
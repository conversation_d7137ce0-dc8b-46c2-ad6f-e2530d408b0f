FROM amd64/ubuntu

# General installs
RUN apt -y update && apt -y upgrade
RUN apt -y install sudo wget git gcc g++ build-essential curl vim
RUN apt-get -y install python3.9
RUN cp /usr/bin/python3 /usr/bin/python
WORKDIR /code

# Copy in run script
RUN mkdir .hydra
COPY run.sh .hydra/run.sh
RUN chmod ugo+rwx .hydra/run.sh

# Install git repo and apply patch
ARG GIT_SHA=67979022aa8ea6a96b618c086f60b49af7d2a568
RUN git clone https://github.com/ethereum/go-ethereum.git
RUN cp -rT /code/go-ethereum /code
RUN rm -fr /code/go-ethereum/
RUN git checkout ${GIT_SHA}

# Install essential build tools and Go
RUN apt-get update
RUN apt-get install -y --no-install-recommends ca-certificates
RUN rm -rf /var/lib/apt/lists/*
RUN curl -L https://golang.org/dl/go1.20.linux-amd64.tar.gz | tar -C /usr/local -xzf -

# Set Go environment variables
ENV PATH="/usr/local/go/bin:${PATH}"

# Download Go modules
RUN cd /code && \
    go mod download

# Build the Go repository
RUN cd /code && \
    go build -v -buildvcs=false ./...

# Go has great dependency checking, so let's cache test results and trust that
# it will rebuild / rerun appropriately for changed files
RUN cd /code && \
    go test ./accounts/abi/bind/backends ./cmd/abigen ./cmd/clef ./cmd/devp2p ./cmd/ethkey ./cmd/evm ./cmd/faucet ./cmd/rlpdump ./cmd/utils ./common/... ./consensus/... ./console ./core/asm ./core/bloombits ./core/forkid ./core/rawdb ./core/state/... ./core/types ./core/vm/... ./crypto/... ./eth/fetcher ./eth/filters ./eth/gasprice ./eth/tracers/... ./ethclient/gethclient ./ethdb/... ./ethstats ./event ./graphql ./internal/... ./light ./log ./metrics/... ./node ./p2p/dnsdisc ./p2p/enode ./p2p/enr ./p2p/msgrate ./p2p/nat ./p2p/netutil ./p2p/nodestate ./p2p/rlpx ./p2p/simulations/... ./params ./rlp/... ./signer/... ./tests/...

# Set the PATH environment variable to include the Go binaries
ENV PATH="${GOPATH}/bin:${PATH}"

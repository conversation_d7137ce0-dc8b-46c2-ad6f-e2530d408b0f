FROM amd64/ubuntu

# General installs
RUN apt -y update && apt -y upgrade
RUN apt -y install sudo wget git gcc g++ build-essential vim curl pip
RUN ln -s /usr/bin/python3 /usr/bin/python
WORKDIR /code

# Copy in run script
RUN mkdir .hydra
COPY run.sh .hydra/run.sh
RUN chmod ugo+rwx .hydra/run.sh

# Install git repo and apply any patches
ARG OWNER=pallets
ARG REPO=flask
ARG GIT_SHA=90967cc
RUN git clone https://github.com/${OWNER}/${REPO}.git code-tmp
RUN cp -rT /code/code-tmp /code
RUN rm -fr /code/code-tmp
RUN git checkout ${GIT_SHA}

RUN pip install pytest
RUN pip install -e .

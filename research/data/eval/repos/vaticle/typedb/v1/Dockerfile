FROM amd64/ubuntu

# General installs
RUN apt -y update && apt -y upgrade
RUN apt -y install sudo wget git gcc g++ build-essential curl vim
RUN apt-get -y install python3.9
RUN cp /usr/bin/python3 /usr/bin/python
WORKDIR /code

# Copy in run script
RUN mkdir .hydra
COPY ./run.sh .hydra/run.sh
RUN chmod ugo+rwx .hydra/run.sh

# Support programming language: Java
RUN apt -y install openjdk-8-jdk

# Download and patch git repo
COPY ./typedb.diff /data/typedb.diff
ARG GIT_SHA=22b3a8fb744322d6b7f00dc8c5ef8e748b828987
RUN git clone https://github.com/vaticle/typedb.git
RUN cp -rT /code/typedb /code
RUN rm -fr /code/typedb/
RUN git checkout ${GIT_SHA}
RUN git apply /data/typedb.diff

# Install build system: <PERSON>zel
RUN wget https://github.com/bazelbuild/bazelisk/releases/download/v1.15.0/bazelisk-linux-amd64
RUN chmod u+x bazelisk-linux-amd64
RUN mv bazelisk-linux-amd64 /bin/bazel

RUN bazel build //...
# RUN bazel test //...

###
# You can debug latency with
# `bazel test //... --test_timeout=n`.
# It will show you which tests take more than n
# seconds.
###

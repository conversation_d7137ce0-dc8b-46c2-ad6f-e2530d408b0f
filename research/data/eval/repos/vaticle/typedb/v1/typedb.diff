diff --git a/BUILD b/BUILD
index f78832253..aab95911d 100644
--- a/BUILD
+++ b/BUILD
@@ -193,17 +193,6 @@ deploy_apt(
     release = deployment['apt.release'],
 )

-release_validate_deps(
-    name = "release-validate-deps",
-    refs = "@vaticle_typedb_workspace_refs//:refs.json",
-    tagged_deps = [
-        "@vaticle_typeql",
-        "@vaticle_typedb_common",
-        "@vaticle_typedb_protocol",
-    ],
-    tags = ["manual"]  # in order for bazel test //... to not fail
-)
-
 docker_container_image(
     name = "assemble-docker",
     base = "@vaticle_ubuntu_image//image",
diff --git a/server/test/BUILD b/server/test/BUILD
index 43c425468..e1b3d509f 100644
--- a/server/test/BUILD
+++ b/server/test/BUILD
@@ -26,28 +26,6 @@ load("@stackb_rules_proto//java:java_grpc_compile.bzl", "java_grpc_compile")

 package(default_visibility = ["//visibility:private",])

-host_compatible_java_test(
-    name = "test-configuration",
-    srcs = [
-        "parameters/CoreConfigTest.java",
-    ],
-    native_libraries_deps = [
-        "//common:common",
-        "//server:server"
-    ],
-    test_class = "com.vaticle.typedb.core.server.parameters.CoreConfigTest",
-    deps = [
-        # Internal dependencies
-
-        # External dependencies from Vaticle
-        "@vaticle_typedb_common//:common",
-    ],
-    data = [
-        "//server:config",
-        ":configurations"
-    ],
-)
-
 filegroup(
     name = "configurations",
     srcs = glob(["parameters/config/*.yml"])
diff --git a/test/assembly/BUILD b/test/assembly/BUILD
index 8882061ca..b8fc5ec21 100644
--- a/test/assembly/BUILD
+++ b/test/assembly/BUILD
@@ -19,34 +19,6 @@ load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")
 load("@vaticle_typedb_common//test:rules.bzl", "typedb_java_test")


-typedb_java_test(
-    name = "assembly",
-    test_class = "com.vaticle.typedb.core.test.assembly.AssemblyTest",
-    srcs = ["AssemblyTest.java"],
-    server_mac_artifact = "//server:assemble-mac-zip",
-    server_linux_artifact = "//server:assemble-linux-targz",
-    server_windows_artifact = "//server:assemble-windows-zip",
-    console_mac_artifact = "@vaticle_typedb_console_artifact_mac//file",
-    console_linux_artifact = "@vaticle_typedb_console_artifact_linux//file",
-    console_windows_artifact = "@vaticle_typedb_console_artifact_windows//file",
-    data = [":console-script"],
-)
-
-java_test(
-    name = "docker",
-    srcs = ["DockerTest.java"],
-    test_class = "com.vaticle.typedb.core.test.assembly.DockerTest",
-    deps = [
-        "@maven//:org_slf4j_slf4j_api",
-        "@maven//:org_zeroturnaround_zt_exec",
-    ],
-    runtime_deps = [
-        "@maven//:ch_qos_logback_logback_classic",
-    ],
-    data = [
-        "//:assemble-docker.tar"
-    ]
-)

 checkstyle_test(
     name = "checkstyle",
diff --git a/test/behaviour/concept/thing/attribute/BUILD b/test/behaviour/concept/thing/attribute/BUILD
index ba98fa59b..b86822d19 100644
--- a/test/behaviour/concept/thing/attribute/BUILD
+++ b/test/behaviour/concept/thing/attribute/BUILD
@@ -42,32 +42,6 @@ host_compatible_java_library(
     ],
 )

-java_test(
-    name = "test",
-    srcs = [
-        "AttributeTest.java"
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.concept.thing.attribute.AttributeTest",
-    deps = [
-        # External Maven Dependencies
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        ":steps",
-        "//test/behaviour/config:parameters",
-        "//test/behaviour/concept/type/attributetype:steps",
-        "//test/behaviour/concept/type/thingtype:steps",
-        "//test/behaviour/connection/database:steps",
-        "//test/behaviour/connection/session:steps",
-        "//test/behaviour/connection/transaction:steps",
-        "//test/behaviour/util:steps",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//concept/thing:attribute.feature",
-    ],
-    size = "small",
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/concept/thing/entity/BUILD b/test/behaviour/concept/thing/entity/BUILD
index 0c0fb5859..e68b3435c 100644
--- a/test/behaviour/concept/thing/entity/BUILD
+++ b/test/behaviour/concept/thing/entity/BUILD
@@ -42,32 +42,6 @@ host_compatible_java_library(
     ],
 )

-java_test(
-    name = "test",
-    srcs = [
-        "EntityTest.java"
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.concept.thing.entity.EntityTest",
-    deps = [
-        # External Maven Dependencies
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        ":steps",
-        "//test/behaviour/config:parameters",
-        "//test/behaviour/concept/thing/attribute:steps",
-        "//test/behaviour/concept/type/attributetype:steps",
-        "//test/behaviour/concept/type/thingtype:steps",
-        "//test/behaviour/connection/database:steps",
-        "//test/behaviour/connection/session:steps",
-        "//test/behaviour/connection/transaction:steps",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//concept/thing:entity.feature",
-    ],
-    size = "small",
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/concept/thing/relation/BUILD b/test/behaviour/concept/thing/relation/BUILD
index e78eda942..3657d91e6 100644
--- a/test/behaviour/concept/thing/relation/BUILD
+++ b/test/behaviour/concept/thing/relation/BUILD
@@ -42,34 +42,6 @@ host_compatible_java_library(
     ],
 )

-java_test(
-    name = "test",
-    srcs = [
-        "RelationTest.java"
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.concept.thing.relation.RelationTest",
-    deps = [
-        # External Maven Dependencies
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        ":steps",
-        "//test/behaviour/config:parameters",
-        "//test/behaviour/concept/thing/attribute:steps",
-        "//test/behaviour/concept/thing/entity:steps",
-        "//test/behaviour/concept/type/attributetype:steps",
-        "//test/behaviour/concept/type/relationtype:steps",
-        "//test/behaviour/concept/type/thingtype:steps",
-        "//test/behaviour/connection/database:steps",
-        "//test/behaviour/connection/session:steps",
-        "//test/behaviour/connection/transaction:steps",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//concept/thing:relation.feature",
-    ],
-    size = "small",
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/concept/type/attributetype/BUILD b/test/behaviour/concept/type/attributetype/BUILD
index 6fa8c10e1..1c524732d 100644
--- a/test/behaviour/concept/type/attributetype/BUILD
+++ b/test/behaviour/concept/type/attributetype/BUILD
@@ -42,31 +42,6 @@ host_compatible_java_library(
     ]
 )

-java_test(
-    name = "test",
-    srcs = [
-        "AttributeTypeTest.java"
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.concept.type.attributetype.AttributeTypeTest",
-    deps = [
-        # External Maven Dependencies
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        ":steps",
-        "//test/behaviour/config:parameters",
-        "//test/behaviour/concept/thing/attribute:steps",
-        "//test/behaviour/concept/type/thingtype:steps",
-        "//test/behaviour/connection/database:steps",
-        "//test/behaviour/connection/session:steps",
-        "//test/behaviour/connection/transaction:steps",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//concept/type:attributetype.feature",
-    ],
-    size = "small",
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/concept/type/entitytype/BUILD b/test/behaviour/concept/type/entitytype/BUILD
index 759730192..f0b88e1ef 100644
--- a/test/behaviour/concept/type/entitytype/BUILD
+++ b/test/behaviour/concept/type/entitytype/BUILD
@@ -25,35 +25,6 @@ java_library(
     ],
 )

-java_test(
-    name = "test",
-    srcs = [
-        "EntityTypeTest.java"
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.concept.type.entitytype.EntityTypeTest",
-    deps = [
-        # External Maven Dependencies
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        ":steps",
-        "//test/behaviour/config:parameters",
-        "//test/behaviour/concept/thing/entity:steps",
-        "//test/behaviour/concept/thing/attribute:steps",
-        "//test/behaviour/concept/thing/relation:steps",
-        "//test/behaviour/concept/type/attributetype:steps",
-        "//test/behaviour/concept/type/relationtype:steps",
-        "//test/behaviour/concept/type/thingtype:steps",
-        "//test/behaviour/connection/database:steps",
-        "//test/behaviour/connection/session:steps",
-        "//test/behaviour/connection/transaction:steps",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//concept/type:entitytype.feature",
-    ],
-    size = "medium",
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/connection/session/BUILD b/test/behaviour/connection/session/BUILD
index a8b964486..405c94bbc 100644
--- a/test/behaviour/connection/session/BUILD
+++ b/test/behaviour/connection/session/BUILD
@@ -41,29 +41,6 @@ host_compatible_java_library(
     ],
 )

-java_test(
-    name = "test",
-    srcs = [
-        "SessionTest.java"
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.connection.session.SessionTest",
-    deps = [
-        # External Maven Dependencies
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        ":steps",
-        "//test/behaviour/config:parameters",
-        "//test/behaviour/connection/database:steps",
-        "//test/behaviour/connection/transaction:steps",
-        "//test/behaviour/typeql:steps",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//connection:session.feature",
-    ],
-    size = "medium",
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/connection/transaction/BUILD b/test/behaviour/connection/transaction/BUILD
index 94292544e..cc6f2df63 100644
--- a/test/behaviour/connection/transaction/BUILD
+++ b/test/behaviour/connection/transaction/BUILD
@@ -42,29 +42,6 @@ host_compatible_java_library(
     ],
 )

-java_test(
-    name = "test",
-    srcs = [
-        "TransactionTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.connection.transaction.TransactionTest",
-    deps = [
-        # External Maven Dependencies
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        ":steps",
-        "//test/behaviour/config:parameters",
-        "//test/behaviour/connection/database:steps",
-        "//test/behaviour/connection/session:steps",
-        "//test/behaviour/typeql:steps",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//connection:transaction.feature",
-    ],
-    size = "small",
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/debug/BUILD b/test/behaviour/debug/BUILD
index 32bddb9bc..5a711236f 100644
--- a/test/behaviour/debug/BUILD
+++ b/test/behaviour/debug/BUILD
@@ -18,43 +18,6 @@
 package(default_visibility = ["//visibility:__subpackages__"])
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")

-java_test(
-    name = "test",
-    srcs = [
-        "DebugTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.debug.DebugTest",
-    deps = [
-        # Internal dependencies
-
-        # Add your additional debugging dependencies here
-        # e.g. "//test/behaviour/connection/session:steps",
-
-        # External Maven Dependencies
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        "//test/behaviour/config:parameters",
-        "//test/behaviour/concept/thing/entity:steps",
-        "//test/behaviour/concept/thing/attribute:steps",
-        "//test/behaviour/concept/thing/relation:steps",
-        "//test/behaviour/concept/type/attributetype:steps",
-        "//test/behaviour/concept/type/relationtype:steps",
-        "//test/behaviour/concept/type/thingtype:steps",
-        "//test/behaviour/connection/database:steps",
-        "//test/behaviour/connection/session:steps",
-        "//test/behaviour/connection/transaction:steps",
-        "//test/behaviour/typeql:steps",
-
-        # --- If testing Reasoner BDD, enable these steps and disable the Connection steps above ---
-#        "//test/behaviour/reasoner:steps"
-    ],
-    data = [
-        ":debug.feature",
-    ],
-    size = "medium",
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/reasoner/tests/concept_inequality/BUILD b/test/behaviour/reasoner/tests/concept_inequality/BUILD
index e18f684cb..7f881d2d6 100644
--- a/test/behaviour/reasoner/tests/concept_inequality/BUILD
+++ b/test/behaviour/reasoner/tests/concept_inequality/BUILD
@@ -18,25 +18,6 @@
 package(default_visibility = ["//test/behaviour:__subpackages__"])
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")

-java_test(
-    name = "test",
-    srcs = [
-        "ConceptInequalityTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.reasoner.tests.concept_inequality.ConceptInequalityTest",
-    deps = [
-        # External dependencies from Maven
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        "//test/behaviour/config:parameters",
-        "//test/behaviour/reasoner:steps",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//typeql/reasoner:concept-inequality.feature",
-    ],
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/reasoner/tests/negation/BUILD b/test/behaviour/reasoner/tests/negation/BUILD
index 0e4a67b2d..7f881d2d6 100644
--- a/test/behaviour/reasoner/tests/negation/BUILD
+++ b/test/behaviour/reasoner/tests/negation/BUILD
@@ -18,25 +18,6 @@
 package(default_visibility = ["//test/behaviour:__subpackages__"])
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")

-java_test(
-    name = "test",
-    srcs = [
-        "NegationTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.reasoner.tests.negation.NegationTest",
-    deps = [
-        # External dependencies from Maven
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        "//test/behaviour/config:parameters",
-        "//test/behaviour/reasoner:steps",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//typeql/reasoner:negation.feature",
-    ],
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/reasoner/tests/recursion/BUILD b/test/behaviour/reasoner/tests/recursion/BUILD
index 0459b3f92..7f881d2d6 100644
--- a/test/behaviour/reasoner/tests/recursion/BUILD
+++ b/test/behaviour/reasoner/tests/recursion/BUILD
@@ -18,25 +18,6 @@
 package(default_visibility = ["//test/behaviour:__subpackages__"])
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")

-java_test(
-    name = "test",
-    srcs = [
-        "RecursionTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.reasoner.tests.recursion.RecursionTest",
-    deps = [
-        # External dependencies from Maven
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        "//test/behaviour/config:parameters",
-        "//test/behaviour/reasoner:steps",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//typeql/reasoner:recursion.feature",
-    ],
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/reasoner/tests/value_predicate/BUILD b/test/behaviour/reasoner/tests/value_predicate/BUILD
index b14aa7300..7f881d2d6 100644
--- a/test/behaviour/reasoner/tests/value_predicate/BUILD
+++ b/test/behaviour/reasoner/tests/value_predicate/BUILD
@@ -18,25 +18,6 @@
 package(default_visibility = ["//test/behaviour:__subpackages__"])
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")

-java_test(
-    name = "test",
-    srcs = [
-        "ValuePredicateTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.reasoner.tests.value_predicate.ValuePredicateTest",
-    deps = [
-        # External dependencies from Maven
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        "//test/behaviour/config:parameters",
-        "//test/behaviour/reasoner:steps",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//typeql/reasoner:value-predicate.feature",
-    ],
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/reasoner/tests/variable_roles/BUILD b/test/behaviour/reasoner/tests/variable_roles/BUILD
index a3ef88266..7f881d2d6 100644
--- a/test/behaviour/reasoner/tests/variable_roles/BUILD
+++ b/test/behaviour/reasoner/tests/variable_roles/BUILD
@@ -18,26 +18,6 @@
 package(default_visibility = ["//test/behaviour:__subpackages__"])
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")

-java_test(
-    name = "test",
-    srcs = [
-        "VariableRolesTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.reasoner.tests.variable_roles.VariableRolesTest",
-    size = "large",
-    deps = [
-        # External dependencies from Maven
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        "//test/behaviour/config:parameters",
-        "//test/behaviour/reasoner:steps",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//typeql/reasoner:variable-roles.feature",
-    ],
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/reasoner/verification/test/BUILD b/test/behaviour/reasoner/verification/test/BUILD
index 936151c32..d7ee695ad 100644
--- a/test/behaviour/reasoner/verification/test/BUILD
+++ b/test/behaviour/reasoner/verification/test/BUILD
@@ -19,55 +19,6 @@ load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")
 load("@vaticle_dependencies//builder/java:rules.bzl", "native_java_libraries")
 load("@vaticle_dependencies//builder/java:rules.bzl", "host_compatible_java_test")

-host_compatible_java_test(
-    name = "test-materialiser",
-    size = "medium",
-    srcs = [
-        "MaterialiserTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.reasoner.verification.test.MaterialiserTest",
-    native_libraries_deps = [
-        "//:typedb",
-        "//common:common",
-        "//concept:concept",
-        "//logic:logic",
-        "//database:database",
-        "//test/behaviour/reasoner/verification:verification",
-    ],
-    deps = [
-        "//test/integration/util:util",
-        # External dependencies from @vaticle
-        "@vaticle_typedb_common//:common",
-        "@vaticle_typeql//java:typeql-lang",
-        "@vaticle_typeql//java/query",
-        "@vaticle_typeql//java/common",
-    ],
-)
-
-host_compatible_java_test(
-    name = "test-correctness-verifier",
-    size = "large",
-    srcs = [
-        "CorrectnessVerifierTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.reasoner.verification.test.CorrectnessVerifierTest",
-    native_libraries_deps = [
-        "//:typedb",
-        "//database:database",
-        "//common",
-        "//common/test:util",
-        "//test/behaviour/reasoner/verification:verification",
-    ],
-    deps = [
-        "//test/integration/util",
-        # External dependencies from @vaticle
-        "@vaticle_typedb_common//:common",
-        "@vaticle_typeql//java:typeql-lang",
-        "@vaticle_typeql//java/query",
-        "@vaticle_typeql//java/common",
-    ],
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/typeql/explanation/language/BUILD b/test/behaviour/typeql/explanation/language/BUILD
index 4fbcac1e4..ece191474 100644
--- a/test/behaviour/typeql/explanation/language/BUILD
+++ b/test/behaviour/typeql/explanation/language/BUILD
@@ -19,29 +19,6 @@
 package(default_visibility = ["//visibility:__subpackages__"])
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")

-java_test(
-    name = "test",
-    srcs = [
-        "LanguageTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.typeql.explanation.language.LanguageTest",
-    deps = [
-        # External dependencies from Maven
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-#        "//test/behaviour/typeql:steps",
-        "//test/behaviour/connection:steps",
-        "//test/behaviour/connection/session:steps",
-        "//test/behaviour/config:parameters",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//typeql/explanation:language.feature",
-    ],
-    size = "medium",
-    visibility = ["//visibility:public"],
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/typeql/explanation/reasoner/BUILD b/test/behaviour/typeql/explanation/reasoner/BUILD
index 67bfafc19..ece191474 100644
--- a/test/behaviour/typeql/explanation/reasoner/BUILD
+++ b/test/behaviour/typeql/explanation/reasoner/BUILD
@@ -19,29 +19,6 @@
 package(default_visibility = ["//visibility:__subpackages__"])
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")

-java_test(
-    name = "test",
-    srcs = [
-        "ReasonerTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.typeql.explanation.reasoner.ReasonerTest",
-    deps = [
-        # External dependencies from Maven
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-#        "//test/behaviour/typeql:steps",
-        "//test/behaviour/connection:steps",
-        "//test/behaviour/connection/session:steps",
-        "//test/behaviour/config:parameters",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//typeql/explanation:reasoner.feature",
-    ],
-    size = "medium",
-    visibility = ["//visibility:public"],
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/typeql/language/define/BUILD b/test/behaviour/typeql/language/define/BUILD
index 44522d259..ece191474 100644
--- a/test/behaviour/typeql/language/define/BUILD
+++ b/test/behaviour/typeql/language/define/BUILD
@@ -19,29 +19,6 @@
 package(default_visibility = ["//visibility:__subpackages__"])
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")

-java_test(
-    name = "test",
-    srcs = [
-        "DefineTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.typeql.language.define.DefineTest",
-    deps = [
-        # External dependencies from Maven
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        "//test/behaviour/typeql:steps",
-        "//test/behaviour/connection:steps",
-        "//test/behaviour/connection/session:steps",
-        "//test/behaviour/config:parameters",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//typeql/language:define.feature",
-    ],
-    size = "medium",
-    visibility = ["//visibility:public"],
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/typeql/language/get/BUILD b/test/behaviour/typeql/language/get/BUILD
index 7be5fddea..5a711236f 100644
--- a/test/behaviour/typeql/language/get/BUILD
+++ b/test/behaviour/typeql/language/get/BUILD
@@ -18,29 +18,6 @@
 package(default_visibility = ["//visibility:__subpackages__"])
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")

-java_test(
-    name = "test",
-    srcs = [
-        "GetTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.typeql.language.get.GetTest",
-    deps = [
-        # External dependencies from Maven
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        "//test/behaviour/typeql:steps",
-        "//test/behaviour/connection:steps",
-        "//test/behaviour/connection/session:steps",
-        "//test/behaviour/config:parameters",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//typeql/language:get.feature",
-    ],
-    size = "medium",
-    visibility = ["//visibility:public"],
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/typeql/language/insert/BUILD b/test/behaviour/typeql/language/insert/BUILD
index 44878d68f..ece191474 100644
--- a/test/behaviour/typeql/language/insert/BUILD
+++ b/test/behaviour/typeql/language/insert/BUILD
@@ -19,31 +19,6 @@
 package(default_visibility = ["//visibility:__subpackages__"])
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")

-java_test(
-    name = "test",
-    srcs = [
-        "InsertTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.typeql.language.insert.InsertTest",
-    deps = [
-        # External dependencies from Maven
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        "//test/behaviour/typeql:steps",
-        "//test/behaviour/connection:steps",
-        "//test/behaviour/connection/session:steps",
-        "//test/behaviour/config:parameters",
-        "//test/behaviour/util:steps",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//typeql/language:insert.feature",
-    ],
-    size = "medium",
-    visibility = ["//visibility:public"],
-)
-
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/typeql/language/match/BUILD b/test/behaviour/typeql/language/match/BUILD
index 8768fb136..5a711236f 100644
--- a/test/behaviour/typeql/language/match/BUILD
+++ b/test/behaviour/typeql/language/match/BUILD
@@ -18,29 +18,6 @@
 package(default_visibility = ["//visibility:__subpackages__"])
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")

-java_test(
-    name = "test",
-    srcs = [
-        "MatchTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.typeql.language.match.MatchTest",
-    deps = [
-        # External dependencies from Maven
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        "//test/behaviour/typeql:steps",
-        "//test/behaviour/connection:steps",
-        "//test/behaviour/connection/session:steps",
-        "//test/behaviour/config:parameters",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//typeql/language:match.feature",
-    ],
-    size = "medium",
-    visibility = ["//visibility:public"],
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/typeql/language/rule_validation/BUILD b/test/behaviour/typeql/language/rule_validation/BUILD
index f204784a9..ece191474 100644
--- a/test/behaviour/typeql/language/rule_validation/BUILD
+++ b/test/behaviour/typeql/language/rule_validation/BUILD
@@ -19,30 +19,6 @@
 package(default_visibility = ["//visibility:__subpackages__"])
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")

-java_test(
-    name = "test",
-    srcs = [
-        "RuleValidationTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.typeql.language.rule_validation.RuleValidationTest",
-    deps = [
-        # External dependencies from Maven
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        "//test/behaviour/typeql:steps",
-        "//test/behaviour/connection:steps",
-        "//test/behaviour/connection/session:steps",
-        "//test/behaviour/config:parameters",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//typeql/language:rule-validation.feature",
-    ],
-    size = "small",
-    visibility = ["//visibility:public"],
-)
-
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/behaviour/typeql/language/undefine/BUILD b/test/behaviour/typeql/language/undefine/BUILD
index c9ab0fe97..ece191474 100644
--- a/test/behaviour/typeql/language/undefine/BUILD
+++ b/test/behaviour/typeql/language/undefine/BUILD
@@ -19,30 +19,6 @@
 package(default_visibility = ["//visibility:__subpackages__"])
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")

-java_test(
-    name = "test",
-    srcs = [
-        "UndefineTest.java",
-    ],
-    test_class = "com.vaticle.typedb.core.test.behaviour.typeql.language.undefine.UndefineTest",
-    deps = [
-        # External dependencies from Maven
-        "@maven//:io_cucumber_cucumber_junit",
-    ],
-    runtime_deps = [
-        "//test/behaviour/typeql:steps",
-        "//test/behaviour/connection:steps",
-        "//test/behaviour/connection/session:steps",
-        "//test/behaviour/config:parameters",
-        "//test/behaviour/logic/rule:steps",
-    ],
-    data = [
-        "@vaticle_typedb_behaviour//typeql/language:undefine.feature",
-    ],
-    size = "medium",
-    visibility = ["//visibility:public"],
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/benchmark/reasoner/iam/basic/BUILD b/test/benchmark/reasoner/iam/basic/BUILD
index 78e9728fd..6f30e4dbd 100644
--- a/test/benchmark/reasoner/iam/basic/BUILD
+++ b/test/benchmark/reasoner/iam/basic/BUILD
@@ -18,17 +18,6 @@
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")
 load("@vaticle_dependencies//builder/java:rules.bzl", "host_compatible_java_test", "native_java_libraries")

-host_compatible_java_test(
-    name = "test-basic",
-    test_class = "com.vaticle.typedb.core.reasoner.benchmark.iam.basic.BasicTest",
-    srcs = ["BasicTest.java"],
-    data = [
-        "//test/benchmark/reasoner/iam/resources:common",
-        "basic_test.tql",
-    ],
-    native_libraries_deps = ["//test/benchmark/reasoner/iam/common:common"],
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/benchmark/reasoner/iam/complex/BUILD b/test/benchmark/reasoner/iam/complex/BUILD
index f760c62c5..3d1914a20 100644
--- a/test/benchmark/reasoner/iam/complex/BUILD
+++ b/test/benchmark/reasoner/iam/complex/BUILD
@@ -18,61 +18,6 @@
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")
 load("@vaticle_dependencies//builder/java:rules.bzl", "host_compatible_java_test", "native_java_libraries")

-host_compatible_java_test(
-    name = "test-large-data",
-    size = "large",
-    test_class = "com.vaticle.typedb.core.reasoner.benchmark.iam.complex.LargeDataTest",
-    srcs = ["LargeDataTest.java"],
-    data = [
-        "//test/benchmark/reasoner/iam/resources:common",
-    ],
-    native_libraries_deps = ["//test/benchmark/reasoner/iam/common:common"],
-)
-
-host_compatible_java_test(
-    name = "test-complex-rule-graph",
-    size = "large",
-    test_class = "com.vaticle.typedb.core.reasoner.benchmark.iam.complex.ComplexRuleGraphTest",
-    srcs = ["ComplexRuleGraphTest.java"],
-    data = [
-        "//test/benchmark/reasoner/iam/resources:common",
-        "complex-rule-graph-test.tql"
-    ],
-    native_libraries_deps = ["//test/benchmark/reasoner/iam/common:common"],
-)
-
-host_compatible_java_test(
-    name = "test-conjunction-structure",
-    test_class = "com.vaticle.typedb.core.reasoner.benchmark.iam.complex.ConjunctionStructureTest",
-    srcs = ["ConjunctionStructureTest.java"],
-    data = [
-        "//test/benchmark/reasoner/iam/resources:common",
-        "conjunction-structure-test.tql",
-    ],
-    native_libraries_deps = ["//test/benchmark/reasoner/iam/common:common"],
-)
-
-host_compatible_java_test(
-    name = "test-language-features",
-    test_class = "com.vaticle.typedb.core.reasoner.benchmark.iam.complex.LanguageFeaturesTest",
-    srcs = ["LanguageFeaturesTest.java"],
-    data = [
-        "//test/benchmark/reasoner/iam/resources:common",
-        "language-features-test.tql",
-    ],
-    native_libraries_deps = ["//test/benchmark/reasoner/iam/common:common"],
-)
-
-host_compatible_java_test(
-    name = "test-real-queries",
-    test_class = "com.vaticle.typedb.core.reasoner.benchmark.iam.complex.RealQueriesTest",
-    srcs = ["RealQueriesTest.java"],
-    data = [
-        "//test/benchmark/reasoner/iam/resources:common",
-    ],
-    native_libraries_deps = ["//test/benchmark/reasoner/iam/common:common"],
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/benchmark/reasoner/synthetic/BUILD b/test/benchmark/reasoner/synthetic/BUILD
index 1954c0782..dc516957a 100644
--- a/test/benchmark/reasoner/synthetic/BUILD
+++ b/test/benchmark/reasoner/synthetic/BUILD
@@ -18,89 +18,6 @@
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")
 load("@vaticle_dependencies//builder/java:rules.bzl", "host_compatible_java_test")

-host_compatible_java_test(
-    name = "benchmark-small",
-    size = "large",
-    test_class = "com.vaticle.typedb.core.reasoner.benchmark.synthetic.BenchmarkSmall",
-    srcs = ["BenchmarkSmall.java",
-            "Util.java",
-            "generation/DiagonalGraph.java",
-            "generation/TransitivityMatrixGraph.java",
-            "generation/TransitivityChainGraph.java",
-            "generation/PathTreeGraph.java"
-    ],
-    data = [":resources/linearTransitivity.tql", ":resources/quadraticTransitivity.tql", ":resources/diagonalTest.tql", ":resources/pathTest.tql"],
-    native_libraries_deps = [
-        "//:typedb",
-        "//database:database",
-        "//concept:concept",
-        "//common:common",
-        "//concurrent:concurrent",
-        "//logic:logic",
-        "//reasoner:reasoner",
-        "//traversal:traversal",
-    ],
-    deps = [
-        # Internal dependencies
-        "//test/integration/util:util",
-        # External dependencies from Vaticle
-        "@vaticle_typeql//java:typeql-lang",
-        "@vaticle_typeql//java/pattern",
-        "@vaticle_typeql//java/query",
-    ],
-)
-
-host_compatible_java_test(
-    name = "benchmark-big",
-    size = "large",
-    test_class = "com.vaticle.typedb.core.reasoner.benchmark.synthetic.BenchmarkBig",
-    srcs = ["BenchmarkBig.java", "Util.java"],
-    data = [":resources/linearTransitivity.tql", ":resources/multiJoin.tql"],
-    native_libraries_deps = [
-        "//:typedb",
-        "//database:database",
-        "//concept:concept",
-        "//common:common",
-        "//concurrent:concurrent",
-        "//logic:logic",
-        "//reasoner:reasoner",
-        "//traversal:traversal",
-    ],
-    deps = [
-        # Internal dependencies
-        "//test/integration/util:util",
-        # External dependencies from Vaticle
-        "@vaticle_typeql//java:typeql-lang",
-        "@vaticle_typeql//java/query",
-        "@vaticle_typeql//java/pattern",
-    ],
-)
-
-host_compatible_java_test(
-    name = "rule-scaling",
-    size = "large",
-    test_class = "com.vaticle.typedb.core.reasoner.benchmark.synthetic.RuleScaling",
-    srcs = ["RuleScaling.java", "Util.java"],
-    native_libraries_deps = [
-        "//:typedb",
-        "//database:database",
-        "//concept:concept",
-        "//common:common",
-        "//concurrent:concurrent",
-        "//logic:logic",
-        "//reasoner:reasoner",
-        "//traversal:traversal",
-    ],
-    deps = [
-        # Internal dependencies
-        "//test/integration/util:util",
-        # External dependencies from Vaticle
-        "@vaticle_typeql//java:typeql-lang",
-        "@vaticle_typeql//java/pattern",
-        "@vaticle_typeql//java/query",
-    ],
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*", "*/*"]),
diff --git a/test/deployment/BUILD b/test/deployment/BUILD
index e0b856182..cb7dd8c53 100644
--- a/test/deployment/BUILD
+++ b/test/deployment/BUILD
@@ -18,22 +18,6 @@
 load("@vaticle_typedb_common//test:rules.bzl", "native_typedb_artifact")
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")

-java_test(
-    name = "apt",
-    test_class = "com.vaticle.typedb.core.test.deployment.AptTest",
-    srcs = ["AptTest.java"],
-    deps = [
-        "@maven//:org_slf4j_slf4j_api",
-        "@maven//:org_zeroturnaround_zt_exec",
-        "@maven//:com_eclipsesource_minimal_json_minimal_json",
-    ],
-    runtime_deps = [
-        "@maven//:ch_qos_logback_logback_classic",
-    ],
-    data = ["@vaticle_typedb_workspace_refs//:refs.json", "//:VERSION"],
-    tags = ["no-sandbox"],
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/integration/BUILD b/test/integration/BUILD
index cc2cab025..0cc968cea 100644
--- a/test/integration/BUILD
+++ b/test/integration/BUILD
@@ -18,27 +18,6 @@
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")
 load("@vaticle_dependencies//builder/java:rules.bzl", "host_compatible_java_test")

-host_compatible_java_test(
-    name = "test-basic",
-    srcs = ["BasicTest.java"],
-    test_class = "com.vaticle.typedb.core.test.integration.BasicTest",
-    native_libraries_deps = [
-        # Internal dependencies
-        "//:typedb",
-        "//common:common",
-        "//database:database",
-        "//concept:concept",
-        "//logic:logic",
-    ],
-    deps = [
-        "//test/integration/util:util",
-        # External dependencies from Vaticle
-        "@vaticle_typeql//java/pattern:pattern",
-        "@vaticle_typeql//java:typeql-lang",
-    ],
-    size = "large",
-)
-
 host_compatible_java_test(
     name = "test-query",
     srcs = ["QueryTest.java"],
@@ -60,28 +39,6 @@ host_compatible_java_test(
     data = [":schema.tql"],
 )

-host_compatible_java_test(
-    name = "test-attribute-string",
-    srcs = ["StringAttributeTest.java"],
-    test_class = "com.vaticle.typedb.core.test.integration.StringAttributeTest",
-    native_libraries_deps = [
-        # Internal dependencies
-        "//:typedb",
-        "//common:common",
-        "//database:database",
-        "//concept:concept",
-        "//logic:logic",
-    ],
-    deps = [
-        "//test/integration/util:util",
-
-        # External dependencies from Vaticle
-        "@vaticle_typedb_common//:common",
-        "@vaticle_typeql//java:typeql-lang",
-    ],
-    size = "large",
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob([
diff --git a/test/integration/database/BUILD b/test/integration/database/BUILD
index 6ea0e4f42..a4b0b38f3 100644
--- a/test/integration/database/BUILD
+++ b/test/integration/database/BUILD
@@ -18,21 +18,6 @@
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")
 load("@vaticle_dependencies//builder/java:rules.bzl", "host_compatible_java_test")

-host_compatible_java_test(
-    name = "test-database",
-    size = "small",
-    srcs = ["DatabaseTest.java"],
-    test_class = "com.vaticle.typedb.core.database.DatabaseTest",
-    data = glob(["data/**"]),
-    native_libraries_deps = [
-        "//:typedb",
-        "//common:common",
-        "//graph:graph",
-        "//encoding:encoding",
-        "//database:database",
-        "//common/test:util",
-    ],
-)

 host_compatible_java_test(
     name = "test-statistics",
@@ -76,27 +61,6 @@ host_compatible_java_test(
     size = "large",
 )

-host_compatible_java_test(
-    name = "test-rocks-iterator",
-    srcs = ["RocksIteratorTest.java"],
-    test_class = "com.vaticle.typedb.core.database.RocksIteratorTest",
-    native_libraries_deps = [
-        "//:typedb",
-        "//common:common",
-        "//concept:concept",
-        "//database:database",
-        "//encoding:encoding",
-        "//graph:graph",
-
-        "//common/test:util",
-    ],
-    deps = [
-        "//test/integration/util:util",
-    ],
-    data = glob(["data/**"]),
-    size = "small",
-)
-
 checkstyle_test(
     name = "checkstyle",
     include = glob(["*"]),
diff --git a/test/integration/encoding/BUILD b/test/integration/encoding/BUILD
index eea8abc53..6fff6db32 100644
--- a/test/integration/encoding/BUILD
+++ b/test/integration/encoding/BUILD
@@ -18,28 +18,6 @@
 load("@vaticle_dependencies//tool/checkstyle:rules.bzl", "checkstyle_test")
 load("@vaticle_dependencies//builder/java:rules.bzl", "host_compatible_java_test")

-host_compatible_java_test(
-    name = "test-encoding",
-    size = "small",
-    srcs = ["EncodingTest.java"],
-    test_class = "com.vaticle.typedb.core.encoding.EncodingTest",
-    data = glob(["data/**"]),
-    native_libraries_deps = [
-        "//:typedb",
-        "//common:common",
-        "//database:database",
-        "//encoding:encoding",
-        "//graph:graph",
-
-        "//common/test:util",
-    ],
-    deps = [
-        "//test/integration/util:util",
-
-        # External dependencies from Vaticle
-        "@vaticle_typeql//java:typeql-lang",
-    ]
-)

 checkstyle_test(
     name = "checkstyle",
diff --git a/test/integration/logic/BUILD b/test/integration/logic/BUILD
index 3e905015d..659fa66ed 100644
--- a/test/integration/logic/BUILD
+++ b/test/integration/logic/BUILD
@@ -44,30 +44,6 @@ host_compatible_java_test(
     ],
 )

-host_compatible_java_test(
-    name = "test-type-inference",
-    srcs = ["TypeInferenceTest.java"],
-    test_class = "com.vaticle.typedb.core.logic.TypeInferenceTest",
-    native_libraries_deps = [
-        "//:typedb",
-        "//database:database",
-        "//common",
-        "//common/test:util",
-        "//concept:concept",
-        "//logic:logic",
-        "//pattern:pattern",
-    ],
-    deps = [
-        # Internal dependencies
-        "//test/integration/util",
-
-        # External dependencies from Vaticle
-        "@vaticle_typeql//java/query",
-        "@vaticle_typeql//java:typeql-lang",
-        "@vaticle_typedb_common//:common",
-    ],
-    data = [":basic-schema.tql", ":test-type-inference.tql"],
-)

 host_compatible_java_test(
     name = "test-unification-relation-concludable",
diff --git a/test/integration/reasoner/BUILD b/test/integration/reasoner/BUILD
index 4a9bf7977..fe6f0c877 100644
--- a/test/integration/reasoner/BUILD
+++ b/test/integration/reasoner/BUILD
@@ -64,31 +64,6 @@ host_compatible_java_test(
     ],
 )

-host_compatible_java_test(
-    name = "test-controller",
-    srcs = ["controller/ControllerTest.java"],
-    test_class = "com.vaticle.typedb.core.reasoner.controller.ControllerTest",
-    native_libraries_deps = [
-        # Internal dependencies
-        "//:typedb",
-        "//database:database",
-        "//common:common",
-        "//concept:concept",
-        "//concurrent:concurrent",
-        "//logic:logic",
-        "//pattern:pattern",
-        "//reasoner:reasoner",
-        "//traversal:traversal",
-    ],
-    deps = [
-        "//test/integration/util",
-
-        # External dependencies from Vaticle
-        "@vaticle_typeql//java:typeql-lang",
-        "@vaticle_typedb_common//:common",
-    ],
-)
-
 host_compatible_java_test(
     name = "test-answer-count-estimator",
     srcs = ["planner/AnswerCountEstimatorTest.java"],

> **WARNINIG: This repo does not represent the current best practice for storing a hydra repo.**

The `dot.hydra` folder contains the critical files from the .hydra folder used to create the docker image.

This metadata had not previously been in our git repo, so the only source of truth is in the published docker image.
I used the following process to modify the image:


```
$ cat copy_files_from_docker.py
from augment.research.data.eval.repo_lib import repo

repo.copy_files_from_docker(image_name="amazon-science/patchcore-inspection:2", working_dir="amazon-repo")

$ python copy_files_from_docker.py
```
Once you've extracted the image, make the modifications necessary.

To recreate the docker image:

```
# Make sure you're in the root directory of the repo
$ docker build -t amazon-science/patchcore-inspection:3 -f .hydra/Dockerfile .
$ docker tag amazon-science/patchcore-inspection:3 au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/exec-eval/amazon-science/patchcore-inspection:3
$ docker push au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/exec-eval/amazon-science/patchcore-inspection:3
```

Furthermore, I modified the: `augment/research/eval/hydra/supported_repos.yaml` file to reference version 3 of this image.

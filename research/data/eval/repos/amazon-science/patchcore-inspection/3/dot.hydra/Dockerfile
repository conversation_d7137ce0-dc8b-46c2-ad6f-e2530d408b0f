# Stage 1: Builder
FROM python:3.9 AS BUILDER

COPY . /code/.

RUN useradd -ms /bin/bash hydra && \
    usermod -aG sudo hydra && \
    echo 'myuser ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers

USER hydra

RUN cd /code && \
    pip install --no-cache-dir --user -r .hydra/requirements.txt

# Stage 2: Final image
FROM python:3.9-slim

RUN useradd -ms /bin/bash hydra
USER hydra
COPY --chown=hydra:hydra --from=BUILDER /home/<USER>/.local /home/<USER>/.local
COPY --chown=hydra:hydra --from=BUILDER /code /code
ENV PATH="/home/<USER>/.local/bin:${PATH}"
ENV PYTHONPATH="/home/<USER>/.local/lib/python3.9/site-packages:/home/<USER>/.local/bin:/code/src:${PYTHONPATH}"

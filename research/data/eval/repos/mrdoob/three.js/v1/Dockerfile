FROM amd64/ubuntu

# General installs
RUN apt -y update && apt -y upgrade
RUN apt -y install sudo wget git gcc g++ build-essential vim curl
# Upgrade node to v19
RUN apt remove -y libnode-dev
RUN curl -fsSL https://deb.nodesource.com/setup_19.x | bash - && apt-get install -y nodejs
RUN apt autoremove
RUN ln -s /usr/bin/python3 /usr/bin/python
WORKDIR /code

# Copy in run script
RUN mkdir .hydra
COPY run.sh .hydra/run.sh
RUN chmod ugo+rwx .hydra/run.sh

# Install git repo and apply any patches
ARG GIT_SHA=a645ee1
RUN git clone https://github.com/mrdoob/three.js.git
RUN cp -rT /code/three.js /code
RUN rm -fr /code/three.js/
RUN git checkout ${GIT_SHA}

RUN npm install

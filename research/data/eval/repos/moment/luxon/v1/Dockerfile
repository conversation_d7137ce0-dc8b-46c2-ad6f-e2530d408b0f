FROM amd64/ubuntu

# General installs
RUN apt -y update && apt -y upgrade
RUN apt -y install sudo wget git gcc g++ build-essential vim curl
# Upgrade node to v19
RUN apt remove -y libnode-dev
RUN curl -fsSL https://deb.nodesource.com/setup_19.x | bash - && apt-get install -y nodejs
RUN apt autoremove
RUN ln -s /usr/bin/python3 /usr/bin/python
WORKDIR /code

# Copy in run script
RUN mkdir .hydra
COPY run.sh .hydra/run.sh
COPY luxon.diff .hydra/luxon.diff
COPY lang .hydra/lang
RUN chmod ugo+rwx .hydra/run.sh

# Install git repo and apply any patches
ARG GIT_SHA=9575754a
RUN git clone https://github.com/moment/luxon.git
RUN cp -rT /code/luxon /code
RUN rm -fr /code/luxon/
RUN git checkout ${GIT_SHA}
RUN git apply .hydra/luxon.diff

RUN npm install

# Final repo specific commands
RUN npm install full-icu

ENV HUSKY_SKIP_INSTALL=1

ENV LANG=en_US.utf8
ENV LIMIT_JEST=yes
ENV CI=yes
ENV TZ=America/New_York

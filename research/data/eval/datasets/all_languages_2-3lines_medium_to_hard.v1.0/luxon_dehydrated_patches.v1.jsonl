{"file_content": "", "char_start": 713, "char_end": 859, "patch_content": "", "patch_id": "moment/luxon/fiUvBiqy", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/interval.js", "_extra": {"func_start": 473, "func_end": 892, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3575, "char_end": 3611, "patch_content": "", "patch_id": "moment/luxon/mJvGS5Rs", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/duration.js", "_extra": {"func_start": 3575, "func_end": 3611, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3742, "char_end": 3807, "patch_content": "", "patch_id": "moment/luxon/Ln4Po39u", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/duration.js", "_extra": {"func_start": 3701, "func_end": 3949, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4105, "char_end": 4190, "patch_content": "", "patch_id": "moment/luxon/bVJpPMeD", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/duration.js", "_extra": {"func_start": 4019, "func_end": 4267, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1344, "char_end": 1430, "patch_content": "", "patch_id": "moment/luxon/cCGbHfS9", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/datetime.js", "_extra": {"func_start": 1344, "func_end": 1430, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1928, "char_end": 1978, "patch_content": "", "patch_id": "moment/luxon/9HQ4S8L3", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/datetime.js", "_extra": {"func_start": 1840, "func_end": 2041, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2620, "char_end": 2656, "patch_content": "", "patch_id": "moment/luxon/Bpugf8wZ", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/datetime.js", "_extra": {"func_start": 2259, "func_end": 2971, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3108, "char_end": 3134, "patch_content": "", "patch_id": "moment/luxon/LrnXdWXy", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/datetime.js", "_extra": {"func_start": 3080, "func_end": 3369, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3460, "char_end": 3513, "patch_content": "", "patch_id": "moment/luxon/eB4YKLxY", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/datetime.js", "_extra": {"func_start": 3460, "func_end": 3513, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4371, "char_end": 4428, "patch_content": "", "patch_id": "moment/luxon/ezGT2WLG", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/datetime.js", "_extra": {"func_start": 3618, "func_end": 4688, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4911, "char_end": 5072, "patch_content": "", "patch_id": "moment/luxon/ATRGGG5X", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/datetime.js", "_extra": {"func_start": 4877, "func_end": 5341, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5496, "char_end": 5580, "patch_content": "", "patch_id": "moment/luxon/ZPKF2GUQ", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/datetime.js", "_extra": {"func_start": 5496, "func_end": 5666, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5882, "char_end": 5925, "patch_content": "", "patch_id": "moment/luxon/JP7e6tMk", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/datetime.js", "_extra": {"func_start": 5703, "func_end": 6053, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8571, "char_end": 8649, "patch_content": "", "patch_id": "moment/luxon/g3G54GMQ", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/datetime.js", "_extra": {"func_start": 7978, "func_end": 8672, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6354, "char_end": 6360, "patch_content": "", "patch_id": "moment/luxon/rCswvpMA", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/datetime.js", "_extra": {"func_start": 8888, "func_end": 9540, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10230, "char_end": 10293, "patch_content": "", "patch_id": "moment/luxon/dFDFvcFH", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/datetime.js", "_extra": {"func_start": 9585, "func_end": 10412, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10444, "char_end": 10549, "patch_content": "", "patch_id": "moment/luxon/NguhgzwQ", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/datetime.js", "_extra": {"func_start": 10444, "func_end": 10721, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 290, "char_end": 358, "patch_content": "", "patch_id": "moment/luxon/CGBMBz92", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/zones/IANAZone.js", "_extra": {"func_start": 166, "func_end": 489, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 946, "char_end": 1011, "patch_content": "", "patch_id": "moment/luxon/QM2sasF7", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/zones/IANAZone.js", "_extra": {"func_start": 946, "func_end": 1292, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1174, "char_end": 1263, "patch_content": "", "patch_id": "moment/luxon/EpFX9bVD", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/regexParser.js", "_extra": {"func_start": 1174, "func_end": 1263, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1459, "char_end": 1563, "patch_content": "", "patch_id": "moment/luxon/TtKKniPE", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/regexParser.js", "_extra": {"func_start": 1310, "func_end": 1592, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3660, "char_end": 3782, "patch_content": "", "patch_id": "moment/luxon/kJyqkDAN", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/regexParser.js", "_extra": {"func_start": 3660, "func_end": 3880, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3925, "char_end": 4028, "patch_content": "", "patch_id": "moment/luxon/GSZL3c3u", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/regexParser.js", "_extra": {"func_start": 3925, "func_end": 4028, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4794, "char_end": 4810, "patch_content": "", "patch_id": "moment/luxon/YuzwbuKt", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/regexParser.js", "_extra": {"func_start": 4456, "func_end": 5281, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7929, "char_end": 8072, "patch_content": "", "patch_id": "moment/luxon/5qLiJpwq", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/regexParser.js", "_extra": {"func_start": 8107, "func_end": 8341, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 386, "char_end": 501, "patch_content": "", "patch_id": "moment/luxon/f8Bv3Ej3", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/conversions.js", "_extra": {"func_start": 364, "func_end": 502, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 632, "char_end": 684, "patch_content": "", "patch_id": "moment/luxon/FZyJZ3bG", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/conversions.js", "_extra": {"func_start": 544, "func_end": 743, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 790, "char_end": 865, "patch_content": "", "patch_id": "moment/luxon/rHvbiRqc", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/conversions.js", "_extra": {"func_start": 790, "func_end": 865, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 974, "char_end": 1095, "patch_content": "", "patch_id": "moment/luxon/MZsKxELK", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/conversions.js", "_extra": {"func_start": 911, "func_end": 1096, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 168, "char_end": 277, "patch_content": "", "patch_id": "moment/luxon/KY2cW8Sc", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/diff.js", "_extra": {"func_start": 75, "func_end": 277, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1388, "char_end": 1474, "patch_content": "", "patch_id": "moment/luxon/WfSQTwm9", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/diff.js", "_extra": {"func_start": 328, "func_end": 2191, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 518, "char_end": 576, "patch_content": "", "patch_id": "moment/luxon/CKmoAmEJ", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/tokenParser.js", "_extra": {"func_start": 518, "func_end": 576, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 854, "char_end": 929, "patch_content": "", "patch_id": "moment/luxon/ZvFXi8hA", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/tokenParser.js", "_extra": {"func_start": 736, "func_end": 929, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 967, "char_end": 1107, "patch_content": "", "patch_id": "moment/luxon/hMVEydyT", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/tokenParser.js", "_extra": {"func_start": 967, "func_end": 1128, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1416, "char_end": 1427, "patch_content": "", "patch_id": "moment/luxon/k2G5Xq6H", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/tokenParser.js", "_extra": {"func_start": 1169, "func_end": 1427, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1463, "char_end": 1532, "patch_content": "", "patch_id": "moment/luxon/8B7L8gQU", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/tokenParser.js", "_extra": {"func_start": 1463, "func_end": 1532, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1560, "char_end": 1599, "patch_content": "", "patch_id": "moment/luxon/nqEUVShH", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/tokenParser.js", "_extra": {"func_start": 1560, "func_end": 1599, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7951, "char_end": 8062, "patch_content": "", "patch_id": "moment/luxon/5hrrL5ms", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/tokenParser.js", "_extra": {"func_start": 7951, "func_end": 8062, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8182, "char_end": 8278, "patch_content": "", "patch_id": "moment/luxon/aVbV575T", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/tokenParser.js", "_extra": {"func_start": 8106, "func_end": 8606, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10221, "char_end": 10314, "patch_content": "", "patch_id": "moment/luxon/6yG6cv4P", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/tokenParser.js", "_extra": {"func_start": 10221, "func_end": 10345, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10396, "char_end": 10440, "patch_content": "", "patch_id": "moment/luxon/FLjSoYrD", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/tokenParser.js", "_extra": {"func_start": 10396, "func_end": 10660, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 410, "char_end": 471, "patch_content": "", "patch_id": "moment/luxon/aHcUnH9s", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/locale.js", "_extra": {"func_start": 331, "func_end": 518, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1515, "char_end": 1614, "patch_content": "", "patch_id": "moment/luxon/bujK4YoR", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/locale.js", "_extra": {"func_start": 1453, "func_end": 1619, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2449, "char_end": 2497, "patch_content": "", "patch_id": "moment/luxon/jRCUXbcX", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/locale.js", "_extra": {"func_start": 1662, "func_end": 2753, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3274, "char_end": 3297, "patch_content": "", "patch_id": "moment/luxon/tDu2No6v", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/locale.js", "_extra": {"func_start": 3182, "func_end": 3311, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3340, "char_end": 3436, "patch_content": "", "patch_id": "moment/luxon/bsYWCrUE", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/locale.js", "_extra": {"func_start": 3340, "func_end": 3474, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3749, "char_end": 3829, "patch_content": "", "patch_id": "moment/luxon/WuRWnUTv", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/locale.js", "_extra": {"func_start": 3749, "func_end": 4048, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6465, "char_end": 6569, "patch_content": "", "patch_id": "moment/luxon/d4rAaXNZ", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/locale.js", "_extra": {"func_start": 4900, "func_end": 6703, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6885, "char_end": 6972, "patch_content": "", "patch_id": "moment/luxon/fjhtodXX", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/locale.js", "_extra": {"func_start": 6721, "func_end": 7027, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7583, "char_end": 7622, "patch_content": "", "patch_id": "moment/luxon/GSjNTpPx", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/locale.js", "_extra": {"func_start": 7583, "func_end": 7622, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7713, "char_end": 7838, "patch_content": "", "patch_id": "moment/luxon/Cuy2g6mx", "repository": "moment/luxon", "commit_sha": "9575754a", "file_name": "src/impl/locale.js", "_extra": {"func_start": 7713, "func_end": 7845, "mode": "2-3-lines"}}

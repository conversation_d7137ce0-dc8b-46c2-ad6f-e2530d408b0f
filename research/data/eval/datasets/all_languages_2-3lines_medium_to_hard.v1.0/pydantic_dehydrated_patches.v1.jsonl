{"file_content": "", "char_start": 2327, "char_end": 2536, "patch_content": "", "patch_id": "pydantic/pydantic/nPJrkfrD", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/functional_validators.py", "_extra": {"func_start": 2210, "func_end": 2536, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6632, "char_end": 6750, "patch_content": "", "patch_id": "pydantic/pydantic/7RmF78Qv", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/functional_validators.py", "_extra": {"func_start": 6516, "func_end": 6839, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10908, "char_end": 11020, "patch_content": "", "patch_id": "pydantic/pydantic/TD45UDJ8", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/functional_validators.py", "_extra": {"func_start": 15402, "func_end": 15731, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2770, "char_end": 2879, "patch_content": "", "patch_id": "pydantic/pydantic/MztSUBin", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/type_adapter.py", "_extra": {"func_start": 2691, "func_end": 3085, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3357, "char_end": 3418, "patch_content": "", "patch_id": "pydantic/pydantic/knhWut9W", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/type_adapter.py", "_extra": {"func_start": 3243, "func_end": 3557, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6775, "char_end": 6876, "patch_content": "", "patch_id": "pydantic/pydantic/LgstRA3B", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/networks.py", "_extra": {"func_start": 6775, "func_end": 6876, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7108, "char_end": 7199, "patch_content": "", "patch_id": "pydantic/pydantic/rQWZWaZs", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/networks.py", "_extra": {"func_start": 7064, "func_end": 7200, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7450, "char_end": 7568, "patch_content": "", "patch_id": "pydantic/pydantic/3rUJiz9z", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/networks.py", "_extra": {"func_start": 7329, "func_end": 7780, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7988, "char_end": 8106, "patch_content": "", "patch_id": "pydantic/pydantic/hWwVLz7F", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/networks.py", "_extra": {"func_start": 7898, "func_end": 8106, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8394, "char_end": 8475, "patch_content": "", "patch_id": "pydantic/pydantic/TvKre32M", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/networks.py", "_extra": {"func_start": 8381, "func_end": 8656, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9094, "char_end": 9230, "patch_content": "", "patch_id": "pydantic/pydantic/o7PKV4L5", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/networks.py", "_extra": {"func_start": 10367, "func_end": 10514, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9155, "char_end": 9240, "patch_content": "", "patch_id": "pydantic/pydantic/rWDABwUb", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/networks.py", "_extra": {"func_start": 11804, "func_end": 11951, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13321, "char_end": 13509, "patch_content": "", "patch_id": "pydantic/pydantic/MzCbmMRE", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/networks.py", "_extra": {"func_start": 12955, "func_end": 13645, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10350, "char_end": 10577, "patch_content": "", "patch_id": "pydantic/pydantic/biYSCMdG", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/dataclasses.py", "_extra": {"func_start": 10096, "func_end": 11133, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1680, "char_end": 1713, "patch_content": "", "patch_id": "pydantic/pydantic/L8ZCMS99", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/color.py", "_extra": {"func_start": 1680, "func_end": 1713, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4280, "char_end": 4366, "patch_content": "", "patch_id": "pydantic/pydantic/cqoatNah", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/color.py", "_extra": {"func_start": 4254, "func_end": 4367, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5959, "char_end": 6116, "patch_content": "", "patch_id": "pydantic/pydantic/TvMySx5N", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/color.py", "_extra": {"func_start": 5959, "func_end": 6325, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6607, "char_end": 6742, "patch_content": "", "patch_id": "pydantic/pydantic/mYdTZPkm", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/color.py", "_extra": {"func_start": 6440, "func_end": 6809, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8082, "char_end": 8245, "patch_content": "", "patch_id": "pydantic/pydantic/7nSDezyj", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/color.py", "_extra": {"func_start": 7912, "func_end": 8246, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9118, "char_end": 9205, "patch_content": "", "patch_id": "pydantic/pydantic/XVHekCZD", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/color.py", "_extra": {"func_start": 8935, "func_end": 9324, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9362, "char_end": 9429, "patch_content": "", "patch_id": "pydantic/pydantic/FVnhpzwW", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/color.py", "_extra": {"func_start": 9362, "func_end": 9429, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9880, "char_end": 9924, "patch_content": "", "patch_id": "pydantic/pydantic/mP4EZnDQ", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/color.py", "_extra": {"func_start": 9880, "func_end": 9924, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10663, "char_end": 10748, "patch_content": "", "patch_id": "pydantic/pydantic/kPwttpJd", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/color.py", "_extra": {"func_start": 10548, "func_end": 10928, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12369, "char_end": 12426, "patch_content": "", "patch_id": "pydantic/pydantic/fnpe3KhG", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/color.py", "_extra": {"func_start": 11544, "func_end": 12693, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13308, "char_end": 13416, "patch_content": "", "patch_id": "pydantic/pydantic/Ur6vnUnz", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/color.py", "_extra": {"func_start": 13308, "func_end": 13416, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13994, "char_end": 14054, "patch_content": "", "patch_id": "pydantic/pydantic/QXq3f97r", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/color.py", "_extra": {"func_start": 13817, "func_end": 14263, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15149, "char_end": 15270, "patch_content": "", "patch_id": "pydantic/pydantic/QeSFmN2x", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/color.py", "_extra": {"func_start": 14701, "func_end": 15270, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8286, "char_end": 8378, "patch_content": "", "patch_id": "pydantic/pydantic/rYMHAMpG", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 8286, "func_end": 8378, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10468, "char_end": 10584, "patch_content": "", "patch_id": "pydantic/pydantic/6m7RH4FR", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 9735, "func_end": 11286, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14156, "char_end": 14227, "patch_content": "", "patch_id": "pydantic/pydantic/hrKL4iJq", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 14101, "func_end": 14489, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16319, "char_end": 16368, "patch_content": "", "patch_id": "pydantic/pydantic/5VvJ5jgk", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 15970, "func_end": 16369, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 17184, "char_end": 17302, "patch_content": "", "patch_id": "pydantic/pydantic/4qDWVeAD", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 17150, "func_end": 17302, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10251, "char_end": 10279, "patch_content": "", "patch_id": "pydantic/pydantic/KJ5iCsFm", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/types.py", "_extra": {"func_start": 10070, "func_end": 10366, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 21150, "char_end": 21266, "patch_content": "", "patch_id": "pydantic/pydantic/aEUGZmNs", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 20041, "func_end": 21716, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 22563, "char_end": 22712, "patch_content": "", "patch_id": "pydantic/pydantic/CE3kM7FW", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 22428, "func_end": 22713, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 28586, "char_end": 28737, "patch_content": "", "patch_id": "pydantic/pydantic/D8KieXds", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 27245, "func_end": 30231, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 34096, "char_end": 34175, "patch_content": "", "patch_id": "pydantic/pydantic/5XR7DwjN", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 33596, "func_end": 35889, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 37044, "char_end": 37186, "patch_content": "", "patch_id": "pydantic/pydantic/mxzHpvyg", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 36980, "func_end": 37370, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 37430, "char_end": 37686, "patch_content": "", "patch_id": "pydantic/pydantic/p83FQgpP", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 37430, "func_end": 37748, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 40299, "char_end": 40367, "patch_content": "", "patch_id": "pydantic/pydantic/fGcKe89J", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 40299, "func_end": 40367, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 41162, "char_end": 41200, "patch_content": "", "patch_id": "pydantic/pydantic/rJJeF8S9", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 41162, "func_end": 41200, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 41582, "char_end": 41615, "patch_content": "", "patch_id": "pydantic/pydantic/ojBS3bYv", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 41467, "func_end": 41615, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 41869, "char_end": 41990, "patch_content": "", "patch_id": "pydantic/pydantic/WFbSZr7p", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 41846, "func_end": 42035, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16097, "char_end": 16156, "patch_content": "", "patch_id": "pydantic/pydantic/5fEBK5mu", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 42488, "func_end": 42850, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 43799, "char_end": 44029, "patch_content": "", "patch_id": "pydantic/pydantic/JHN5S9MP", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 43484, "func_end": 44293, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 48326, "char_end": 48434, "patch_content": "", "patch_id": "pydantic/pydantic/n9b42XHv", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 48127, "func_end": 48590, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 51735, "char_end": 51823, "patch_content": "", "patch_id": "pydantic/pydantic/3YpSx79h", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 50492, "func_end": 52140, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 52466, "char_end": 52662, "patch_content": "", "patch_id": "pydantic/pydantic/8ccDsqY8", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 52466, "func_end": 52662, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 53181, "char_end": 53276, "patch_content": "", "patch_id": "pydantic/pydantic/g5e5sMRv", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 53034, "func_end": 53462, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 54144, "char_end": 54264, "patch_content": "", "patch_id": "pydantic/pydantic/gFXxECEP", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 54121, "func_end": 54419, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 55206, "char_end": 55333, "patch_content": "", "patch_id": "pydantic/pydantic/ghWm5Zwx", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/main.py", "_extra": {"func_start": 55079, "func_end": 55334, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1711, "char_end": 1776, "patch_content": "", "patch_id": "pydantic/pydantic/Hn3VU7xQ", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/validate_call.py", "_extra": {"func_start": 1310, "func_end": 1777, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 629, "char_end": 726, "patch_content": "", "patch_id": "pydantic/pydantic/hSb8NZeE", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/alias_generators.py", "_extra": {"func_start": 629, "func_end": 727, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 940, "char_end": 1115, "patch_content": "", "patch_id": "pydantic/pydantic/s3iYVzTx", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/alias_generators.py", "_extra": {"func_start": 940, "func_end": 1141, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1113, "char_end": 1217, "patch_content": "", "patch_id": "pydantic/pydantic/Pkoe4EET", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/warnings.py", "_extra": {"func_start": 1002, "func_end": 1217, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1867, "char_end": 1947, "patch_content": "", "patch_id": "pydantic/pydantic/GvQ4tE2u", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/warnings.py", "_extra": {"func_start": 1867, "func_end": 1947, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9647, "char_end": 9741, "patch_content": "", "patch_id": "pydantic/pydantic/rWN3SUgd", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/class_validators.py", "_extra": {"func_start": 9647, "func_end": 10819, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9122, "char_end": 9382, "patch_content": "", "patch_id": "pydantic/pydantic/4fZ6LLNp", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/functional_serializers.py", "_extra": {"func_start": 9121, "func_end": 9456, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5024, "char_end": 5101, "patch_content": "", "patch_id": "pydantic/pydantic/ZzsBiYN6", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/__init__.py", "_extra": {"func_start": 4858, "func_end": 5140, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5442, "char_end": 5554, "patch_content": "", "patch_id": "pydantic/pydantic/g7fY42gA", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/fields.py", "_extra": {"func_start": 5442, "func_end": 5554, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14372, "char_end": 14382, "patch_content": "", "patch_id": "pydantic/pydantic/4SV3Vet6", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/mypy.py", "_extra": {"func_start": 14055, "func_end": 14382, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 17875, "char_end": 18075, "patch_content": "", "patch_id": "pydantic/pydantic/HjXbbn5R", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/mypy.py", "_extra": {"func_start": 17576, "func_end": 18585, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 40710, "char_end": 40727, "patch_content": "", "patch_id": "pydantic/pydantic/BWvHhKDN", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/mypy.py", "_extra": {"func_start": 40568, "func_end": 41264, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 41943, "char_end": 42011, "patch_content": "", "patch_id": "pydantic/pydantic/DHtCYf7F", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/mypy.py", "_extra": {"func_start": 41703, "func_end": 42037, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 330, "char_end": 374, "patch_content": "", "patch_id": "pydantic/pydantic/EyC3ykwu", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/version.py", "_extra": {"func_start": 330, "func_end": 374, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1661, "char_end": 1812, "patch_content": "", "patch_id": "pydantic/pydantic/BiuQAs6o", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/root_model.py", "_extra": {"func_start": 1565, "func_end": 1975, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2505, "char_end": 2580, "patch_content": "", "patch_id": "pydantic/pydantic/73TTM5Ke", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/root_model.py", "_extra": {"func_start": 2505, "func_end": 2580, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2683, "char_end": 2762, "patch_content": "", "patch_id": "pydantic/pydantic/cToeBnns", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/root_model.py", "_extra": {"func_start": 2627, "func_end": 2762, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4914, "char_end": 4946, "patch_content": "", "patch_id": "pydantic/pydantic/RHRRgQL5", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/root_model.py", "_extra": {"func_start": 4914, "func_end": 4946, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6343, "char_end": 6402, "patch_content": "", "patch_id": "pydantic/pydantic/ccDo6Ppn", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/fields.py", "_extra": {"func_start": 6054, "func_end": 7906, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11147, "char_end": 11211, "patch_content": "", "patch_id": "pydantic/pydantic/Emoki5Cp", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/fields.py", "_extra": {"func_start": 9988, "func_end": 11211, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15866, "char_end": 16001, "patch_content": "", "patch_id": "pydantic/pydantic/GNvdWSGY", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/fields.py", "_extra": {"func_start": 14932, "func_end": 16028, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16581, "char_end": 16621, "patch_content": "", "patch_id": "pydantic/pydantic/UKXuJgSN", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/fields.py", "_extra": {"func_start": 16503, "func_end": 17116, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18576, "char_end": 18638, "patch_content": "", "patch_id": "pydantic/pydantic/6pMn4rPc", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/fields.py", "_extra": {"func_start": 18344, "func_end": 18931, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 19557, "char_end": 19652, "patch_content": "", "patch_id": "pydantic/pydantic/Hd2REKaB", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/fields.py", "_extra": {"func_start": 19557, "func_end": 19768, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 19947, "char_end": 20029, "patch_content": "", "patch_id": "pydantic/pydantic/MqSAw3mE", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/fields.py", "_extra": {"func_start": 19947, "func_end": 20029, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20437, "char_end": 20582, "patch_content": "", "patch_id": "pydantic/pydantic/XLHW78iC", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/fields.py", "_extra": {"func_start": 20372, "func_end": 20582, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 21336, "char_end": 21505, "patch_content": "", "patch_id": "pydantic/pydantic/zx6zzHd5", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/fields.py", "_extra": {"func_start": 21336, "func_end": 21505, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23280, "char_end": 23305, "patch_content": "", "patch_id": "pydantic/pydantic/HGVrivrS", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/fields.py", "_extra": {"func_start": 23280, "func_end": 23305, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 24201, "char_end": 24277, "patch_content": "", "patch_id": "pydantic/pydantic/7ifEp5ba", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/fields.py", "_extra": {"func_start": 24030, "func_end": 24278, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 31483, "char_end": 31545, "patch_content": "", "patch_id": "pydantic/pydantic/PaiuZGv5", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/fields.py", "_extra": {"func_start": 30039, "func_end": 33714, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 35905, "char_end": 36016, "patch_content": "", "patch_id": "pydantic/pydantic/46UZeL64", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/fields.py", "_extra": {"func_start": 35905, "func_end": 36016, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 37299, "char_end": 37346, "patch_content": "", "patch_id": "pydantic/pydantic/cSgXYApM", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/fields.py", "_extra": {"func_start": 37104, "func_end": 37346, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2286, "char_end": 2347, "patch_content": "", "patch_id": "pydantic/pydantic/gTDj4eVB", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/errors.py", "_extra": {"func_start": 2286, "func_end": 2464, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7124, "char_end": 7188, "patch_content": "", "patch_id": "pydantic/pydantic/HD3EG7NQ", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 7031, "func_end": 7188, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9769, "char_end": 9808, "patch_content": "", "patch_id": "pydantic/pydantic/rs35SerP", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 9747, "func_end": 10061, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16534, "char_end": 16639, "patch_content": "", "patch_id": "pydantic/pydantic/iSFXxnzF", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 16420, "func_end": 16640, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16789, "char_end": 16855, "patch_content": "", "patch_id": "pydantic/pydantic/LyqDqNuZ", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 16789, "func_end": 16855, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18224, "char_end": 18295, "patch_content": "", "patch_id": "pydantic/pydantic/QJfLn9X2", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 17942, "func_end": 18380, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 19080, "char_end": 19216, "patch_content": "", "patch_id": "pydantic/pydantic/HYzrYw98", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 18978, "func_end": 19255, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20850, "char_end": 20904, "patch_content": "", "patch_id": "pydantic/pydantic/f2muzAZC", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 20850, "func_end": 20904, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 21955, "char_end": 21991, "patch_content": "", "patch_id": "pydantic/pydantic/tXUXn7Ld", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 21955, "func_end": 21991, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 22023, "char_end": 22088, "patch_content": "", "patch_id": "pydantic/pydantic/Mr2Hyybz", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 22023, "func_end": 22088, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 24427, "char_end": 24485, "patch_content": "", "patch_id": "pydantic/pydantic/HXxizDr4", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 24049, "func_end": 24523, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 25364, "char_end": 25422, "patch_content": "", "patch_id": "pydantic/pydantic/RArEwnfw", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 24678, "func_end": 25423, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 25987, "char_end": 26043, "patch_content": "", "patch_id": "pydantic/pydantic/d9w2x5qT", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 25987, "func_end": 26043, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 26211, "char_end": 26276, "patch_content": "", "patch_id": "pydantic/pydantic/VRbFai8s", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 26211, "func_end": 26276, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 27628, "char_end": 27760, "patch_content": "", "patch_id": "pydantic/pydantic/pBr5K86U", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 27505, "func_end": 27761, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23791, "char_end": 23868, "patch_content": "", "patch_id": "pydantic/pydantic/SDRUoMj6", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/schema.py", "_extra": {"func_start": 23103, "func_end": 24118, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 28426, "char_end": 28565, "patch_content": "", "patch_id": "pydantic/pydantic/p5k27io8", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 28426, "func_end": 28565, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 29083, "char_end": 29209, "patch_content": "", "patch_id": "pydantic/pydantic/zZPUos3w", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 28718, "func_end": 29209, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 30003, "char_end": 30100, "patch_content": "", "patch_id": "pydantic/pydantic/3ALGjy3m", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 29454, "func_end": 30557, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 31301, "char_end": 31376, "patch_content": "", "patch_id": "pydantic/pydantic/nxAG46xv", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 31301, "func_end": 31376, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 31843, "char_end": 31879, "patch_content": "", "patch_id": "pydantic/pydantic/PFCBF3Ec", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 31481, "func_end": 32119, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 33587, "char_end": 33618, "patch_content": "", "patch_id": "pydantic/pydantic/3LA9jQ3t", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 33388, "func_end": 33619, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 33796, "char_end": 33953, "patch_content": "", "patch_id": "pydantic/pydantic/YfRhqFZu", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 33796, "func_end": 33954, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 36060, "char_end": 36151, "patch_content": "", "patch_id": "pydantic/pydantic/NBDodxKD", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 36060, "func_end": 36220, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 40907, "char_end": 40948, "patch_content": "", "patch_id": "pydantic/pydantic/PsUuunh4", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 40907, "func_end": 40948, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 41580, "char_end": 41689, "patch_content": "", "patch_id": "pydantic/pydantic/fEVo5dLu", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 41536, "func_end": 41690, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 41905, "char_end": 42083, "patch_content": "", "patch_id": "pydantic/pydantic/V87E8fa2", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 41844, "func_end": 42094, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 43517, "char_end": 43546, "patch_content": "", "patch_id": "pydantic/pydantic/R5Cuouhn", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/types.py", "_extra": {"func_start": 43517, "func_end": 43546, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 46758, "char_end": 46912, "patch_content": "", "patch_id": "pydantic/pydantic/on3m5Yg8", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/fields.py", "_extra": {"func_start": 46721, "func_end": 46983, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14728, "char_end": 14862, "patch_content": "", "patch_id": "pydantic/pydantic/bGMyjuGB", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 14441, "func_end": 15164, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16766, "char_end": 16935, "patch_content": "", "patch_id": "pydantic/pydantic/YLw58Q3F", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 16611, "func_end": 17646, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 19479, "char_end": 19601, "patch_content": "", "patch_id": "pydantic/pydantic/9FQ9SfsZ", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 18199, "func_end": 20095, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 22042, "char_end": 22073, "patch_content": "", "patch_id": "pydantic/pydantic/7hKXNqX7", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 20373, "func_end": 26159, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 26452, "char_end": 26470, "patch_content": "", "patch_id": "pydantic/pydantic/pwS6gZoG", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 26452, "func_end": 26470, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 27026, "char_end": 27061, "patch_content": "", "patch_id": "pydantic/pydantic/EYvjTJ9v", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 27026, "func_end": 27061, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 29286, "char_end": 29341, "patch_content": "", "patch_id": "pydantic/pydantic/K3vei22K", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 28398, "func_end": 29402, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 30090, "char_end": 30266, "patch_content": "", "patch_id": "pydantic/pydantic/oqJjtptg", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 30090, "func_end": 30267, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 22046, "char_end": 22073, "patch_content": "", "patch_id": "pydantic/pydantic/ETsh6yaJ", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 30529, "func_end": 30703, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 31625, "char_end": 31681, "patch_content": "", "patch_id": "pydantic/pydantic/d7CCQb7b", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 31625, "func_end": 31681, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 32513, "char_end": 32629, "patch_content": "", "patch_id": "pydantic/pydantic/8yTV449Q", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 31952, "func_end": 32897, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 33247, "char_end": 33357, "patch_content": "", "patch_id": "pydantic/pydantic/St2YcbcH", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 33247, "func_end": 33357, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 34098, "char_end": 34187, "patch_content": "", "patch_id": "pydantic/pydantic/6xzjrmcg", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 34098, "func_end": 34187, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 34443, "char_end": 34612, "patch_content": "", "patch_id": "pydantic/pydantic/oqMfcSDx", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 34443, "func_end": 34729, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 42861, "char_end": 42965, "patch_content": "", "patch_id": "pydantic/pydantic/N8PyS9o2", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 42861, "func_end": 43367, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 48896, "char_end": 49076, "patch_content": "", "patch_id": "pydantic/pydantic/rCpNN7dM", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 48896, "func_end": 49077, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 50377, "char_end": 50435, "patch_content": "", "patch_id": "pydantic/pydantic/TzpExui9", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 50377, "func_end": 50435, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 52566, "char_end": 52716, "patch_content": "", "patch_id": "pydantic/pydantic/boUDBsk3", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 51985, "func_end": 52939, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 39391, "char_end": 39444, "patch_content": "", "patch_id": "pydantic/pydantic/5hpiBcdG", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 54538, "func_end": 54591, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 55237, "char_end": 55297, "patch_content": "", "patch_id": "pydantic/pydantic/sevLozdy", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 55237, "func_end": 55297, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 50911, "char_end": 51020, "patch_content": "", "patch_id": "pydantic/pydantic/VSneHCaA", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 59371, "func_end": 60154, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 60669, "char_end": 60766, "patch_content": "", "patch_id": "pydantic/pydantic/Y2MySPzc", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 60494, "func_end": 60841, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 61632, "char_end": 61770, "patch_content": "", "patch_id": "pydantic/pydantic/HLVUAfms", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 61632, "func_end": 62003, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 63783, "char_end": 63909, "patch_content": "", "patch_id": "pydantic/pydantic/Aj8Mj4ec", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 63098, "func_end": 63991, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 64775, "char_end": 64890, "patch_content": "", "patch_id": "pydantic/pydantic/LQ3RuJgq", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 64295, "func_end": 65926, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 71736, "char_end": 71937, "patch_content": "", "patch_id": "pydantic/pydantic/RspouwEa", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 71632, "func_end": 71938, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 72194, "char_end": 72246, "patch_content": "", "patch_id": "pydantic/pydantic/9r457Ebf", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 72194, "func_end": 72246, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 74749, "char_end": 74795, "patch_content": "", "patch_id": "pydantic/pydantic/3QsA9oWn", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 74749, "func_end": 74795, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 76769, "char_end": 76842, "patch_content": "", "patch_id": "pydantic/pydantic/sVRJdybX", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 76769, "func_end": 76842, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 79925, "char_end": 79977, "patch_content": "", "patch_id": "pydantic/pydantic/6iMRXvqX", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 79657, "func_end": 80405, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 82887, "char_end": 83067, "patch_content": "", "patch_id": "pydantic/pydantic/stPuJo9k", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 82836, "func_end": 83068, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 84066, "char_end": 84232, "patch_content": "", "patch_id": "pydantic/pydantic/Zx3TngUe", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 84066, "func_end": 84233, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 87359, "char_end": 87452, "patch_content": "", "patch_id": "pydantic/pydantic/ERd6CH3r", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 87359, "func_end": 87452, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 87740, "char_end": 87802, "patch_content": "", "patch_id": "pydantic/pydantic/kH5RL42p", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 87648, "func_end": 87802, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 88836, "char_end": 88896, "patch_content": "", "patch_id": "pydantic/pydantic/YpoiGWSJ", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 88789, "func_end": 88896, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 89016, "char_end": 89200, "patch_content": "", "patch_id": "pydantic/pydantic/ewZYRFpy", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 88966, "func_end": 89403, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 89958, "char_end": 90056, "patch_content": "", "patch_id": "pydantic/pydantic/ZYFAqP7g", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 89481, "func_end": 90056, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 91330, "char_end": 91550, "patch_content": "", "patch_id": "pydantic/pydantic/DxjoTuUk", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 91162, "func_end": 91551, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 93914, "char_end": 94000, "patch_content": "", "patch_id": "pydantic/pydantic/SFGY3VLe", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 93914, "func_end": 94000, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 94208, "char_end": 94298, "patch_content": "", "patch_id": "pydantic/pydantic/bbMBVhbs", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 94058, "func_end": 94299, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 94582, "char_end": 94710, "patch_content": "", "patch_id": "pydantic/pydantic/EmTzyAqY", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 94398, "func_end": 94966, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 96196, "char_end": 96339, "patch_content": "", "patch_id": "pydantic/pydantic/5BkiJeug", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/json_schema.py", "_extra": {"func_start": 96045, "func_end": 96390, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2072, "char_end": 2134, "patch_content": "", "patch_id": "pydantic/pydantic/LQ3ij8cX", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_typing_extra.py", "_extra": {"func_start": 2072, "func_end": 2134, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2183, "char_end": 2253, "patch_content": "", "patch_id": "pydantic/pydantic/MkZAzxfx", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_typing_extra.py", "_extra": {"func_start": 2183, "func_end": 2253, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2312, "char_end": 2339, "patch_content": "", "patch_id": "pydantic/pydantic/Z5Re3NeW", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_typing_extra.py", "_extra": {"func_start": 2312, "func_end": 2339, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2669, "char_end": 2778, "patch_content": "", "patch_id": "pydantic/pydantic/tGBnooHt", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_typing_extra.py", "_extra": {"func_start": 2611, "func_end": 2779, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2822, "char_end": 2899, "patch_content": "", "patch_id": "pydantic/pydantic/MukZy8UX", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_typing_extra.py", "_extra": {"func_start": 2822, "func_end": 2972, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3190, "char_end": 3265, "patch_content": "", "patch_id": "pydantic/pydantic/mAgNVBvs", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_typing_extra.py", "_extra": {"func_start": 3147, "func_end": 3265, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4360, "char_end": 4495, "patch_content": "", "patch_id": "pydantic/pydantic/ATcJrz4i", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_typing_extra.py", "_extra": {"func_start": 4342, "func_end": 4496, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5687, "char_end": 5717, "patch_content": "", "patch_id": "pydantic/pydantic/X8tne6cN", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_typing_extra.py", "_extra": {"func_start": 5493, "func_end": 5717, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5862, "char_end": 5958, "patch_content": "", "patch_id": "pydantic/pydantic/j4hfXKxG", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_typing_extra.py", "_extra": {"func_start": 5811, "func_end": 6352, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6868, "char_end": 6992, "patch_content": "", "patch_id": "pydantic/pydantic/ZyJrsjTL", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_typing_extra.py", "_extra": {"func_start": 6853, "func_end": 7192, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7581, "char_end": 7745, "patch_content": "", "patch_id": "pydantic/pydantic/XFoRrman", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_typing_extra.py", "_extra": {"func_start": 7416, "func_end": 7767, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2244, "char_end": 2405, "patch_content": "", "patch_id": "pydantic/pydantic/GMZs3HvG", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_model_construction.py", "_extra": {"func_start": 2109, "func_end": 2406, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10619, "char_end": 10656, "patch_content": "", "patch_id": "pydantic/pydantic/WGS2xJv8", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_model_construction.py", "_extra": {"func_start": 10619, "func_end": 10656, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10843, "char_end": 10942, "patch_content": "", "patch_id": "pydantic/pydantic/MAvnMzgb", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_model_construction.py", "_extra": {"func_start": 10843, "func_end": 10942, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11279, "char_end": 11453, "patch_content": "", "patch_id": "pydantic/pydantic/szzPwcN2", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_model_construction.py", "_extra": {"func_start": 11078, "func_end": 11716, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16568, "char_end": 16693, "patch_content": "", "patch_id": "pydantic/pydantic/CsbXDfUZ", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_model_construction.py", "_extra": {"func_start": 13722, "func_end": 17646, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 17977, "char_end": 18102, "patch_content": "", "patch_id": "pydantic/pydantic/kuuuSEJV", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_model_construction.py", "_extra": {"func_start": 17740, "func_end": 18366, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 24008, "char_end": 24074, "patch_content": "", "patch_id": "pydantic/pydantic/jzDW5Ebr", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_model_construction.py", "_extra": {"func_start": 22602, "func_end": 25289, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3403, "char_end": 3445, "patch_content": "", "patch_id": "pydantic/pydantic/jajgtjHT", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/types.py", "_extra": {"func_start": 3403, "func_end": 3446, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1648, "char_end": 1731, "patch_content": "", "patch_id": "pydantic/pydantic/XeipujEM", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators_v1.py", "_extra": {"func_start": 1648, "func_end": 1731, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1484, "char_end": 1581, "patch_content": "", "patch_id": "pydantic/pydantic/m2BVzeLH", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_mock_val_ser.py", "_extra": {"func_start": 1288, "func_end": 1652, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1809, "char_end": 1935, "patch_content": "", "patch_id": "pydantic/pydantic/GggoPBHA", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_mock_val_ser.py", "_extra": {"func_start": 1693, "func_end": 1956, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3802, "char_end": 3907, "patch_content": "", "patch_id": "pydantic/pydantic/S4MGG2zB", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_mock_val_ser.py", "_extra": {"func_start": 3600, "func_end": 4305, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3220, "char_end": 3357, "patch_content": "", "patch_id": "pydantic/pydantic/e6w6oW7Y", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_dataclasses.py", "_extra": {"func_start": 3220, "func_end": 3394, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5720, "char_end": 5809, "patch_content": "", "patch_id": "pydantic/pydantic/rBwFWYbb", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_dataclasses.py", "_extra": {"func_start": 4267, "func_end": 7314, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10386, "char_end": 10489, "patch_content": "", "patch_id": "pydantic/pydantic/YQYe4xNm", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_dataclasses.py", "_extra": {"func_start": 10278, "func_end": 10489, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1555, "char_end": 1687, "patch_content": "", "patch_id": "pydantic/pydantic/NT6Zwve5", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_metadata.py", "_extra": {"func_start": 1405, "func_end": 1688, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1977, "char_end": 2084, "patch_content": "", "patch_id": "pydantic/pydantic/s6sDwhH8", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_metadata.py", "_extra": {"func_start": 1900, "func_end": 2197, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6416, "char_end": 6523, "patch_content": "", "patch_id": "pydantic/pydantic/7qQEip72", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 6375, "func_end": 6563, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6680, "char_end": 6757, "patch_content": "", "patch_id": "pydantic/pydantic/pnLcGxsD", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 6680, "func_end": 6757, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6878, "char_end": 6976, "patch_content": "", "patch_id": "pydantic/pydantic/CgUHKLXp", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 6878, "func_end": 7062, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7156, "char_end": 7259, "patch_content": "", "patch_id": "pydantic/pydantic/scavqqpT", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 7156, "func_end": 7260, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7372, "char_end": 7417, "patch_content": "", "patch_id": "pydantic/pydantic/BKvLMCLp", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 7372, "func_end": 7417, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9665, "char_end": 9752, "patch_content": "", "patch_id": "pydantic/pydantic/E4Wejoq8", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 9665, "func_end": 9819, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10036, "char_end": 10118, "patch_content": "", "patch_id": "pydantic/pydantic/ZkLTBhv9", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 10036, "func_end": 10192, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11583, "char_end": 11643, "patch_content": "", "patch_id": "pydantic/pydantic/8HQYgQeu", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 10388, "func_end": 11767, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12532, "char_end": 12638, "patch_content": "", "patch_id": "pydantic/pydantic/GJezZPoi", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 12490, "func_end": 12698, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 19246, "char_end": 19333, "patch_content": "", "patch_id": "pydantic/pydantic/4cTkLUho", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 15184, "func_end": 19912, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20798, "char_end": 20832, "patch_content": "", "patch_id": "pydantic/pydantic/ZGsn7t8X", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 20353, "func_end": 21234, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23225, "char_end": 23353, "patch_content": "", "patch_id": "pydantic/pydantic/sdtSwsWX", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 23225, "func_end": 23585, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 24594, "char_end": 24660, "patch_content": "", "patch_id": "pydantic/pydantic/twV6RjS3", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 24063, "func_end": 24685, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 26796, "char_end": 26887, "patch_content": "", "patch_id": "pydantic/pydantic/R2NtrPEo", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 26741, "func_end": 26925, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 28278, "char_end": 28347, "patch_content": "", "patch_id": "pydantic/pydantic/ZbYeqwpa", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 27692, "func_end": 28642, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 29430, "char_end": 29493, "patch_content": "", "patch_id": "pydantic/pydantic/jZcz5Vwo", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 29190, "func_end": 29540, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 29594, "char_end": 29677, "patch_content": "", "patch_id": "pydantic/pydantic/5HwfSpvY", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 29594, "func_end": 29677, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 30104, "char_end": 30183, "patch_content": "", "patch_id": "pydantic/pydantic/BagqqFfs", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_decorators.py", "_extra": {"func_start": 30104, "func_end": 30211, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 934, "char_end": 1071, "patch_content": "", "patch_id": "pydantic/pydantic/3BPkbJ98", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_validators.py", "_extra": {"func_start": 727, "func_end": 1580, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4675, "char_end": 4830, "patch_content": "", "patch_id": "pydantic/pydantic/Tzj6SS99", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_validators.py", "_extra": {"func_start": 4519, "func_end": 4830, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5276, "char_end": 5375, "patch_content": "", "patch_id": "pydantic/pydantic/iEU2TDk8", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_validators.py", "_extra": {"func_start": 4904, "func_end": 5459, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8108, "char_end": 8174, "patch_content": "", "patch_id": "pydantic/pydantic/kH28cc7L", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_validators.py", "_extra": {"func_start": 8020, "func_end": 8265, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9317, "char_end": 9406, "patch_content": "", "patch_id": "pydantic/pydantic/Ve3FrKS6", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_validators.py", "_extra": {"func_start": 9282, "func_end": 9406, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9819, "char_end": 9928, "patch_content": "", "patch_id": "pydantic/pydantic/fR6qPHMe", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_validators.py", "_extra": {"func_start": 9733, "func_end": 9928, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1336, "char_end": 1398, "patch_content": "", "patch_id": "pydantic/pydantic/CrXiMgpP", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_fields.py", "_extra": {"func_start": 1285, "func_end": 1698, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1979, "char_end": 2012, "patch_content": "", "patch_id": "pydantic/pydantic/7jQ7ud4q", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_fields.py", "_extra": {"func_start": 1979, "func_end": 2012, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6861, "char_end": 6985, "patch_content": "", "patch_id": "pydantic/pydantic/VjuMyf9t", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_fields.py", "_extra": {"func_start": 3162, "func_end": 8403, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8728, "char_end": 8758, "patch_content": "", "patch_id": "pydantic/pydantic/pzgkoE7C", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_fields.py", "_extra": {"func_start": 8476, "func_end": 8779, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9735, "char_end": 9938, "patch_content": "", "patch_id": "pydantic/pydantic/QaqtAVtT", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_fields.py", "_extra": {"func_start": 9242, "func_end": 10879, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10925, "char_end": 10961, "patch_content": "", "patch_id": "pydantic/pydantic/MZrn7A3g", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_fields.py", "_extra": {"func_start": 10925, "func_end": 10961, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 994, "char_end": 1126, "patch_content": "", "patch_id": "pydantic/pydantic/fjqcUyi2", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_discriminated_union.py", "_extra": {"func_start": 716, "func_end": 1267, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2335, "char_end": 2422, "patch_content": "", "patch_id": "pydantic/pydantic/7wfU4Nbc", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_discriminated_union.py", "_extra": {"func_start": 2335, "func_end": 2422, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11040, "char_end": 11191, "patch_content": "", "patch_id": "pydantic/pydantic/iEMSEbDg", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_discriminated_union.py", "_extra": {"func_start": 8665, "func_end": 11535, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13337, "char_end": 13502, "patch_content": "", "patch_id": "pydantic/pydantic/YQWFZgDJ", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_discriminated_union.py", "_extra": {"func_start": 12240, "func_end": 14485, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14936, "char_end": 15051, "patch_content": "", "patch_id": "pydantic/pydantic/oFVMFRDa", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_discriminated_union.py", "_extra": {"func_start": 14936, "func_end": 15219, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18347, "char_end": 18441, "patch_content": "", "patch_id": "pydantic/pydantic/97SKZ2tJ", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_discriminated_union.py", "_extra": {"func_start": 15592, "func_end": 18894, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 19549, "char_end": 19635, "patch_content": "", "patch_id": "pydantic/pydantic/sHTsgL7P", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_discriminated_union.py", "_extra": {"func_start": 19801, "func_end": 20210, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 19431, "char_end": 19635, "patch_content": "", "patch_id": "pydantic/pydantic/6MrvYFTj", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_discriminated_union.py", "_extra": {"func_start": 20381, "func_end": 20840, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2971, "char_end": 3027, "patch_content": "", "patch_id": "pydantic/pydantic/NYJwg5HM", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_std_types_schema.py", "_extra": {"func_start": 1980, "func_end": 5956, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6534, "char_end": 6631, "patch_content": "", "patch_id": "pydantic/pydantic/Nnm8tfCi", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_std_types_schema.py", "_extra": {"func_start": 6392, "func_end": 6657, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7426, "char_end": 7537, "patch_content": "", "patch_id": "pydantic/pydantic/fotAyQRD", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_std_types_schema.py", "_extra": {"func_start": 6942, "func_end": 7538, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8145, "char_end": 8274, "patch_content": "", "patch_id": "pydantic/pydantic/3iCSuG8Y", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_std_types_schema.py", "_extra": {"func_start": 7688, "func_end": 8523, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8853, "char_end": 8943, "patch_content": "", "patch_id": "pydantic/pydantic/8oHmYe7Y", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_std_types_schema.py", "_extra": {"func_start": 8773, "func_end": 8944, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10415, "char_end": 10472, "patch_content": "", "patch_id": "pydantic/pydantic/7T9eXeWe", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_std_types_schema.py", "_extra": {"func_start": 9097, "func_end": 10890, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12053, "char_end": 12098, "patch_content": "", "patch_id": "pydantic/pydantic/pnAG8HZ4", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_std_types_schema.py", "_extra": {"func_start": 11748, "func_end": 12098, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16091, "char_end": 16207, "patch_content": "", "patch_id": "pydantic/pydantic/etRziwGF", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_std_types_schema.py", "_extra": {"func_start": 15568, "func_end": 16323, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18539, "char_end": 18591, "patch_content": "", "patch_id": "pydantic/pydantic/aeFESHNs", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_std_types_schema.py", "_extra": {"func_start": 17518, "func_end": 19881, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20242, "char_end": 20268, "patch_content": "", "patch_id": "pydantic/pydantic/rWDuZgwd", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_std_types_schema.py", "_extra": {"func_start": 20242, "func_end": 20268, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20578, "char_end": 20624, "patch_content": "", "patch_id": "pydantic/pydantic/FGS6BAc7", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_std_types_schema.py", "_extra": {"func_start": 20376, "func_end": 22577, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 27015, "char_end": 27087, "patch_content": "", "patch_id": "pydantic/pydantic/3Mh2YCCf", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_std_types_schema.py", "_extra": {"func_start": 23968, "func_end": 27637, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1241, "char_end": 1299, "patch_content": "", "patch_id": "pydantic/pydantic/nz6eMgLC", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_utils.py", "_extra": {"func_start": 1241, "func_end": 1299, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2273, "char_end": 2380, "patch_content": "", "patch_id": "pydantic/pydantic/oFV2iWqJ", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_utils.py", "_extra": {"func_start": 2254, "func_end": 3440, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6397, "char_end": 6602, "patch_content": "", "patch_id": "pydantic/pydantic/FJ2sd2xV", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_utils.py", "_extra": {"func_start": 6293, "func_end": 6626, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6714, "char_end": 6758, "patch_content": "", "patch_id": "pydantic/pydantic/nBEE2MyN", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_utils.py", "_extra": {"func_start": 6714, "func_end": 6758, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7037, "char_end": 7144, "patch_content": "", "patch_id": "pydantic/pydantic/4RmLNACp", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_utils.py", "_extra": {"func_start": 6847, "func_end": 7144, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7332, "char_end": 7426, "patch_content": "", "patch_id": "pydantic/pydantic/d8QHi5j9", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_utils.py", "_extra": {"func_start": 7249, "func_end": 7426, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9244, "char_end": 9330, "patch_content": "", "patch_id": "pydantic/pydantic/3FGesUVu", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_utils.py", "_extra": {"func_start": 9157, "func_end": 9330, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9244, "char_end": 9329, "patch_content": "", "patch_id": "pydantic/pydantic/3Z7HajLN", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_utils.py", "_extra": {"func_start": 9715, "func_end": 9888, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10988, "char_end": 11073, "patch_content": "", "patch_id": "pydantic/pydantic/tkVfuUUg", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_utils.py", "_extra": {"func_start": 10753, "func_end": 11074, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11669, "char_end": 11749, "patch_content": "", "patch_id": "pydantic/pydantic/PmyDHPpw", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_utils.py", "_extra": {"func_start": 11589, "func_end": 11749, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12057, "char_end": 12166, "patch_content": "", "patch_id": "pydantic/pydantic/MbSNW3dS", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_utils.py", "_extra": {"func_start": 11853, "func_end": 12189, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13154, "char_end": 13315, "patch_content": "", "patch_id": "pydantic/pydantic/r4PDtR6w", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_utils.py", "_extra": {"func_start": 13154, "func_end": 13316, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14188, "char_end": 14343, "patch_content": "", "patch_id": "pydantic/pydantic/XBinWiN5", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_utils.py", "_extra": {"func_start": 14479, "func_end": 15416, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 17228, "char_end": 17363, "patch_content": "", "patch_id": "pydantic/pydantic/quHKfGAD", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_utils.py", "_extra": {"func_start": 17150, "func_end": 17364, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20311, "char_end": 20330, "patch_content": "", "patch_id": "pydantic/pydantic/X3RWdra5", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_core_utils.py", "_extra": {"func_start": 18295, "func_end": 22649, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4131, "char_end": 4225, "patch_content": "", "patch_id": "pydantic/pydantic/YR4tt7Xc", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 3996, "func_end": 4243, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8004, "char_end": 8079, "patch_content": "", "patch_id": "pydantic/pydantic/FzSrbx2w", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 7624, "func_end": 8231, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9827, "char_end": 9902, "patch_content": "", "patch_id": "pydantic/pydantic/8v4YSLmL", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 9827, "func_end": 9902, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11387, "char_end": 11474, "patch_content": "", "patch_id": "pydantic/pydantic/AiZBZpAq", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 11387, "func_end": 11606, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12000, "char_end": 12032, "patch_content": "", "patch_id": "pydantic/pydantic/WqjiZ2uh", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 11789, "func_end": 12033, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12088, "char_end": 12148, "patch_content": "", "patch_id": "pydantic/pydantic/L8K5wyEn", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 12088, "func_end": 12148, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12235, "char_end": 12275, "patch_content": "", "patch_id": "pydantic/pydantic/N77Zpnpw", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 12235, "func_end": 12275, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13082, "char_end": 13165, "patch_content": "", "patch_id": "pydantic/pydantic/oev4pZNV", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 13082, "func_end": 13165, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13253, "char_end": 13408, "patch_content": "", "patch_id": "pydantic/pydantic/XYqZt9L8", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 13253, "func_end": 13408, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13700, "char_end": 13866, "patch_content": "", "patch_id": "pydantic/pydantic/FF89PjLk", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 13470, "func_end": 14004, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14962, "char_end": 15022, "patch_content": "", "patch_id": "pydantic/pydantic/oipu98xC", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 14773, "func_end": 15022, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 41846, "char_end": 41963, "patch_content": "", "patch_id": "pydantic/pydantic/WUUo88w4", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/schema.py", "_extra": {"func_start": 41313, "func_end": 41986, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 22967, "char_end": 23060, "patch_content": "", "patch_id": "pydantic/pydantic/Mmr4ToZR", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 22897, "func_end": 23153, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23488, "char_end": 23620, "patch_content": "", "patch_id": "pydantic/pydantic/K45CDbYU", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 23353, "func_end": 23681, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 24898, "char_end": 25097, "patch_content": "", "patch_id": "pydantic/pydantic/GJc32SfN", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 24103, "func_end": 25611, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 28529, "char_end": 28619, "patch_content": "", "patch_id": "pydantic/pydantic/aX2pLLkp", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 28075, "func_end": 28791, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 30027, "char_end": 30130, "patch_content": "", "patch_id": "pydantic/pydantic/4TDG98pm", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 29595, "func_end": 32773, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 35460, "char_end": 35540, "patch_content": "", "patch_id": "pydantic/pydantic/jtjQgfop", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 35381, "func_end": 35877, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 35688, "char_end": 35819, "patch_content": "", "patch_id": "pydantic/pydantic/nNtzWF3G", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 36109, "func_end": 36569, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 36972, "char_end": 37058, "patch_content": "", "patch_id": "pydantic/pydantic/NXoCr2XM", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 36831, "func_end": 37423, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 46273, "char_end": 46410, "patch_content": "", "patch_id": "pydantic/pydantic/K8YPNhSZ", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 45398, "func_end": 48766, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 51281, "char_end": 51382, "patch_content": "", "patch_id": "pydantic/pydantic/4GMKR8yB", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 50764, "func_end": 51726, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 53499, "char_end": 53648, "patch_content": "", "patch_id": "pydantic/pydantic/AJUJgds5", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 53451, "func_end": 53659, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 55456, "char_end": 55634, "patch_content": "", "patch_id": "pydantic/pydantic/Meg4qRuJ", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 54827, "func_end": 55779, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 57438, "char_end": 57613, "patch_content": "", "patch_id": "pydantic/pydantic/PvJZxXYL", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 57390, "func_end": 57624, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 60327, "char_end": 60415, "patch_content": "", "patch_id": "pydantic/pydantic/gkgXpG7S", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 57821, "func_end": 61041, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 62634, "char_end": 62773, "patch_content": "", "patch_id": "pydantic/pydantic/m8Q2xBPT", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 61260, "func_end": 63251, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13910, "char_end": 13954, "patch_content": "", "patch_id": "pydantic/pydantic/jEkf2Rfg", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 63348, "func_end": 63678, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 66592, "char_end": 66755, "patch_content": "", "patch_id": "pydantic/pydantic/mz3htCXw", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 66462, "func_end": 66982, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 69406, "char_end": 69594, "patch_content": "", "patch_id": "pydantic/pydantic/LNWjTb8X", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 69244, "func_end": 72253, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 75174, "char_end": 75349, "patch_content": "", "patch_id": "pydantic/pydantic/a5xHnT3b", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 74234, "func_end": 75641, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 78422, "char_end": 78509, "patch_content": "", "patch_id": "pydantic/pydantic/gBtg7fQY", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 79261, "func_end": 80763, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 85408, "char_end": 85474, "patch_content": "", "patch_id": "pydantic/pydantic/aD5UFajA", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 85069, "func_end": 86423, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 39736, "char_end": 39779, "patch_content": "", "patch_id": "pydantic/pydantic/pn3XfBZG", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/schema.py", "_extra": {"func_start": 39206, "func_end": 39779, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 89330, "char_end": 89366, "patch_content": "", "patch_id": "pydantic/pydantic/QaTc6BhB", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generate_schema.py", "_extra": {"func_start": 89111, "func_end": 89366, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4125, "char_end": 4328, "patch_content": "", "patch_id": "pydantic/pydantic/KeDoWCzt", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_validate_call.py", "_extra": {"func_start": 4125, "func_end": 4348, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2095, "char_end": 2197, "patch_content": "", "patch_id": "pydantic/pydantic/XJWsG49a", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_repr.py", "_extra": {"func_start": 2095, "func_end": 2197, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2920, "char_end": 3009, "patch_content": "", "patch_id": "pydantic/pydantic/CmFGnRsX", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_repr.py", "_extra": {"func_start": 2837, "func_end": 3010, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3111, "char_end": 3179, "patch_content": "", "patch_id": "pydantic/pydantic/XKMobH37", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_repr.py", "_extra": {"func_start": 3111, "func_end": 3179, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3842, "char_end": 4008, "patch_content": "", "patch_id": "pydantic/pydantic/4jAGXjwz", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_repr.py", "_extra": {"func_start": 3386, "func_end": 4210, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4306, "char_end": 4501, "patch_content": "", "patch_id": "pydantic/pydantic/fRjz6LyH", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_known_annotated_metadata.py", "_extra": {"func_start": 4072, "func_end": 4811, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12682, "char_end": 12866, "patch_content": "", "patch_id": "pydantic/pydantic/T9sN2zSb", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_known_annotated_metadata.py", "_extra": {"func_start": 12287, "func_end": 13781, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14418, "char_end": 14547, "patch_content": "", "patch_id": "pydantic/pydantic/9JuVxbL3", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_known_annotated_metadata.py", "_extra": {"func_start": 14332, "func_end": 14548, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6438, "char_end": 6666, "patch_content": "", "patch_id": "pydantic/pydantic/d6Fbmfga", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generics.py", "_extra": {"func_start": 6376, "func_end": 6836, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 26705, "char_end": 26780, "patch_content": "", "patch_id": "pydantic/pydantic/q8SW47PS", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/schema.py", "_extra": {"func_start": 26312, "func_end": 26989, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8096, "char_end": 8193, "patch_content": "", "patch_id": "pydantic/pydantic/fhbEGUVB", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generics.py", "_extra": {"func_start": 7948, "func_end": 8194, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8536, "char_end": 8624, "patch_content": "", "patch_id": "pydantic/pydantic/VDHLkiZ5", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generics.py", "_extra": {"func_start": 8507, "func_end": 8963, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13961, "char_end": 14056, "patch_content": "", "patch_id": "pydantic/pydantic/q64Eax2b", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generics.py", "_extra": {"func_start": 13523, "func_end": 14420, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14924, "char_end": 15090, "patch_content": "", "patch_id": "pydantic/pydantic/FKQtxcft", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generics.py", "_extra": {"func_start": 14800, "func_end": 15090, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 31997, "char_end": 32127, "patch_content": "", "patch_id": "pydantic/pydantic/VHBK8X72", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/fields.py", "_extra": {"func_start": 31565, "func_end": 32471, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 19212, "char_end": 19296, "patch_content": "", "patch_id": "pydantic/pydantic/JmNvyVMr", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generics.py", "_extra": {"func_start": 18998, "func_end": 19296, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20339, "char_end": 20465, "patch_content": "", "patch_id": "pydantic/pydantic/axvBDjNd", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generics.py", "_extra": {"func_start": 20274, "func_end": 20605, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 21227, "char_end": 21296, "patch_content": "", "patch_id": "pydantic/pydantic/CWdTbm3D", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_generics.py", "_extra": {"func_start": 21227, "func_end": 21296, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2185, "char_end": 2279, "patch_content": "", "patch_id": "pydantic/pydantic/fJHRYBxy", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_utils.py", "_extra": {"func_start": 2176, "func_end": 2375, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2943, "char_end": 3018, "patch_content": "", "patch_id": "pydantic/pydantic/Bgbxt3a9", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_utils.py", "_extra": {"func_start": 2943, "func_end": 3018, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3618, "char_end": 3689, "patch_content": "", "patch_id": "pydantic/pydantic/8gormds9", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_utils.py", "_extra": {"func_start": 3618, "func_end": 3689, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4622, "char_end": 4746, "patch_content": "", "patch_id": "pydantic/pydantic/UexzmYjz", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_utils.py", "_extra": {"func_start": 4580, "func_end": 4793, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5926, "char_end": 5963, "patch_content": "", "patch_id": "pydantic/pydantic/knrmw4jm", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/class_validators.py", "_extra": {"func_start": 5889, "func_end": 5963, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6094, "char_end": 6335, "patch_content": "", "patch_id": "pydantic/pydantic/7B8Wwqbk", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_utils.py", "_extra": {"func_start": 6017, "func_end": 7340, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8282, "char_end": 8400, "patch_content": "", "patch_id": "pydantic/pydantic/FpPPj5bt", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_utils.py", "_extra": {"func_start": 8035, "func_end": 8873, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9157, "char_end": 9230, "patch_content": "", "patch_id": "pydantic/pydantic/oyhQqY9o", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_utils.py", "_extra": {"func_start": 8980, "func_end": 9329, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9411, "char_end": 9523, "patch_content": "", "patch_id": "pydantic/pydantic/BNbsEFmv", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_utils.py", "_extra": {"func_start": 9411, "func_end": 9524, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10842, "char_end": 11052, "patch_content": "", "patch_id": "pydantic/pydantic/BLmKpXvu", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_utils.py", "_extra": {"func_start": 10512, "func_end": 11091, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11539, "char_end": 11655, "patch_content": "", "patch_id": "pydantic/pydantic/9vmV7bkU", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_utils.py", "_extra": {"func_start": 11539, "func_end": 11697, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11769, "char_end": 11786, "patch_content": "", "patch_id": "pydantic/pydantic/qGcmotPz", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/types.py", "_extra": {"func_start": 17589, "func_end": 17784, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4170, "char_end": 4202, "patch_content": "", "patch_id": "pydantic/pydantic/JTCDH6tE", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_config.py", "_extra": {"func_start": 3342, "func_end": 4202, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6595, "char_end": 6631, "patch_content": "", "patch_id": "pydantic/pydantic/THGxRpKU", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_config.py", "_extra": {"func_start": 5148, "func_end": 6632, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6657, "char_end": 6767, "patch_content": "", "patch_id": "pydantic/pydantic/hzYcnwSp", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_config.py", "_extra": {"func_start": 6657, "func_end": 6767, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8022, "char_end": 8172, "patch_content": "", "patch_id": "pydantic/pydantic/3bWNeL8p", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_config.py", "_extra": {"func_start": 7933, "func_end": 8274, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9487, "char_end": 9785, "patch_content": "", "patch_id": "pydantic/pydantic/UAJNA5s4", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_config.py", "_extra": {"func_start": 9202, "func_end": 9830, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1318, "char_end": 1364, "patch_content": "", "patch_id": "pydantic/pydantic/ijDa4feq", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_schema_generation_shared.py", "_extra": {"func_start": 1182, "func_end": 1364, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2004, "char_end": 2169, "patch_content": "", "patch_id": "pydantic/pydantic/DjRg6gZL", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_schema_generation_shared.py", "_extra": {"func_start": 1956, "func_end": 2418, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6693, "char_end": 6789, "patch_content": "", "patch_id": "pydantic/pydantic/sMYoBKU9", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/class_validators.py", "_extra": {"func_start": 6422, "func_end": 7090, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3539, "char_end": 3593, "patch_content": "", "patch_id": "pydantic/pydantic/hHsC3Ls3", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_schema_generation_shared.py", "_extra": {"func_start": 3539, "func_end": 3593, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3671, "char_end": 3739, "patch_content": "", "patch_id": "pydantic/pydantic/ihaFusPA", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_schema_generation_shared.py", "_extra": {"func_start": 3671, "func_end": 3739, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4403, "char_end": 4580, "patch_content": "", "patch_id": "pydantic/pydantic/7XGh5gEH", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/_internal/_schema_generation_shared.py", "_extra": {"func_start": 4141, "func_end": 4740, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3099, "char_end": 3127, "patch_content": "", "patch_id": "pydantic/pydantic/kx9iDM4o", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/deprecated/tools.py", "_extra": {"func_start": 2960, "func_end": 3278, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 684, "char_end": 815, "patch_content": "", "patch_id": "pydantic/pydantic/rKcmTQcg", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/deprecated/config.py", "_extra": {"func_start": 550, "func_end": 815, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1307, "char_end": 1394, "patch_content": "", "patch_id": "pydantic/pydantic/AitTEHd9", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/deprecated/config.py", "_extra": {"func_start": 1223, "func_end": 1645, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2138, "char_end": 2205, "patch_content": "", "patch_id": "pydantic/pydantic/ijKAuf4K", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/deprecated/config.py", "_extra": {"func_start": 2011, "func_end": 2254, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1276, "char_end": 1339, "patch_content": "", "patch_id": "pydantic/pydantic/R8rPvNAh", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/deprecated/parse.py", "_extra": {"func_start": 858, "func_end": 1706, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2356, "char_end": 2480, "patch_content": "", "patch_id": "pydantic/pydantic/8Hq7giwX", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/deprecated/parse.py", "_extra": {"func_start": 2021, "func_end": 2481, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3561, "char_end": 3587, "patch_content": "", "patch_id": "pydantic/pydantic/3693chQr", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/deprecated/copy_internals.py", "_extra": {"func_start": 1374, "func_end": 3587, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4012, "char_end": 4062, "patch_content": "", "patch_id": "pydantic/pydantic/a6p6tXL9", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/deprecated/copy_internals.py", "_extra": {"func_start": 3814, "func_end": 4290, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2907, "char_end": 2976, "patch_content": "", "patch_id": "pydantic/pydantic/33zyUVjD", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/deprecated/json.py", "_extra": {"func_start": 2451, "func_end": 3197, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4349, "char_end": 4465, "patch_content": "", "patch_id": "pydantic/pydantic/oXWFAxdP", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/deprecated/json.py", "_extra": {"func_start": 4172, "func_end": 4465, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5597, "char_end": 5682, "patch_content": "", "patch_id": "pydantic/pydantic/7dS5w3it", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/deprecated/decorator.py", "_extra": {"func_start": 5597, "func_end": 5682, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7626, "char_end": 7721, "patch_content": "", "patch_id": "pydantic/pydantic/hMiKfyZ7", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/deprecated/decorator.py", "_extra": {"func_start": 7355, "func_end": 8438, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 50393, "char_end": 50483, "patch_content": "", "patch_id": "pydantic/pydantic/9UbXxdVk", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/fields.py", "_extra": {"func_start": 50393, "func_end": 50483, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1629, "char_end": 1639, "patch_content": "", "patch_id": "pydantic/pydantic/Bem6RPyV", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/env_settings.py", "_extra": {"func_start": 10962, "func_end": 11164, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4421, "char_end": 4486, "patch_content": "", "patch_id": "pydantic/pydantic/qGvCEyWP", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/networks.py", "_extra": {"func_start": 4293, "func_end": 4487, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 34439, "char_end": 34486, "patch_content": "", "patch_id": "pydantic/pydantic/BdyYtZN9", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/fields.py", "_extra": {"func_start": 34382, "func_end": 36293, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10241, "char_end": 10382, "patch_content": "", "patch_id": "pydantic/pydantic/ES2uA6ZW", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/dataclasses.py", "_extra": {"func_start": 9442, "func_end": 12776, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6459, "char_end": 6465, "patch_content": "", "patch_id": "pydantic/pydantic/CtKRnHFo", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/dataclasses.py", "_extra": {"func_start": 16896, "func_end": 17103, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 21427, "char_end": 21554, "patch_content": "", "patch_id": "pydantic/pydantic/MLWDvdVM", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/fields.py", "_extra": {"func_start": 21427, "func_end": 22158, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14530, "char_end": 14658, "patch_content": "", "patch_id": "pydantic/pydantic/fbhdW8K3", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/main.py", "_extra": {"func_start": 14133, "func_end": 14784, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23109, "char_end": 23176, "patch_content": "", "patch_id": "pydantic/pydantic/JB2j6cZq", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/main.py", "_extra": {"func_start": 22893, "func_end": 23348, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23747, "char_end": 23845, "patch_content": "", "patch_id": "pydantic/pydantic/6Kq2szEt", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/main.py", "_extra": {"func_start": 23609, "func_end": 23880, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 31952, "char_end": 32038, "patch_content": "", "patch_id": "pydantic/pydantic/4qCeUvNU", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/main.py", "_extra": {"func_start": 30614, "func_end": 33000, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 37387, "char_end": 37451, "patch_content": "", "patch_id": "pydantic/pydantic/q5vUgVni", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/main.py", "_extra": {"func_start": 37370, "func_end": 37573, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5409, "char_end": 5474, "patch_content": "", "patch_id": "pydantic/pydantic/CjR8bWAG", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/utils.py", "_extra": {"func_start": 5350, "func_end": 5474, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15739, "char_end": 15824, "patch_content": "", "patch_id": "pydantic/pydantic/gdYEXVEb", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/utils.py", "_extra": {"func_start": 15739, "func_end": 15824, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13043, "char_end": 13105, "patch_content": "", "patch_id": "pydantic/pydantic/ccdfewef", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/typing.py", "_extra": {"func_start": 13043, "func_end": 13105, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10894, "char_end": 10955, "patch_content": "", "patch_id": "pydantic/pydantic/popTg2qN", "repository": "pydantic/pydantic", "commit_sha": "b1912b80a72e6019100041513fa482f19686a716", "file_name": "pydantic/v1/class_validators.py", "_extra": {"func_start": 10894, "func_end": 10955, "mode": "2-3-lines"}}

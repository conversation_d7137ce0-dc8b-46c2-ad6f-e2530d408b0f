{"file_content": "", "char_start": 2038, "char_end": 2233, "patch_content": "", "patch_id": "seata/3d989ff6585d7ca4", "repository": "seata/seata", "commit_sha": "", "file_name": "integration-tx-api/src/main/java/io/seata/integration/tx/api/remoting/parser/DefaultRemotingParser.java", "_extra": {"func_start": 2038, "func_end": 2300, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3115, "char_end": 3175, "patch_content": "", "patch_id": "seata/07a92daeb51227b3", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/util/LowerCaseLinkHashMap.java", "_extra": {"func_start": 3115, "func_end": 3175, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13469, "char_end": 13607, "patch_content": "", "patch_id": "seata/3c58df860c5fe2c1", "repository": "seata/seata", "commit_sha": "", "file_name": "spring/src/main/java/io/seata/spring/annotation/GlobalTransactionScanner.java", "_extra": {"func_start": 12966, "func_end": 13750, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4120, "char_end": 4223, "patch_content": "", "patch_id": "seata/e80359545ee66ab1", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/lock/AbstractLockManager.java", "_extra": {"func_start": 3924, "func_end": 4424, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2561, "char_end": 2620, "patch_content": "", "patch_id": "seata/92a6905e9a9c2494", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/exception/FrameworkException.java", "_extra": {"func_start": 2561, "func_end": 2620, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3910, "char_end": 3931, "patch_content": "", "patch_id": "seata/f65e7bedc7747089", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-druid/src/main/java/io/seata/sqlparser/druid/sqlserver/SqlServerDeleteRecognizer.java", "_extra": {"func_start": 4068, "func_end": 4089, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6987, "char_end": 7042, "patch_content": "", "patch_id": "seata/aeedc0e228bebed5", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/DataCompareUtils.java", "_extra": {"func_start": 6924, "func_end": 7104, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4513, "char_end": 4698, "patch_content": "", "patch_id": "seata/5a5ca9b60213cae0", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine-store/src/main/java/io/seata/saga/engine/store/db/DbStateLangStore.java", "_extra": {"func_start": 4106, "func_end": 4893, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10703, "char_end": 10745, "patch_content": "", "patch_id": "seata/738c9d8665092c8f", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/xa/ConnectionProxyXA.java", "_extra": {"func_start": 10589, "func_end": 10745, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5870, "char_end": 5924, "patch_content": "", "patch_id": "seata/2cf9e8357165cd35", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/DataCompareUtils.java", "_extra": {"func_start": 5776, "func_end": 6596, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12830, "char_end": 13019, "patch_content": "", "patch_id": "seata/7506466ffe1f18fb", "repository": "seata/seata", "commit_sha": "", "file_name": "discovery/seata-discovery-redis/src/main/java/io/seata/discovery/registry/redis/RedisRegistryServiceImpl.java", "_extra": {"func_start": 12324, "func_end": 13409, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2266, "char_end": 2287, "patch_content": "", "patch_id": "seata/f65e7bedc7747089", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-antlr/src/main/java/io/seata/sqlparser/antlr/mysql/AntlrMySQLSelectRecognizer.java", "_extra": {"func_start": 2508, "func_end": 2529, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4123, "char_end": 4258, "patch_content": "", "patch_id": "seata/5610eeaf04d30121", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-druid/src/main/java/io/seata/sqlparser/druid/oracle/OracleInsertRecognizer.java", "_extra": {"func_start": 3768, "func_end": 5361, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5766, "char_end": 5929, "patch_content": "", "patch_id": "seata/7bca8892556cf786", "repository": "seata/seata", "commit_sha": "", "file_name": "config/seata-config-core/src/main/java/io/seata/config/ConfigurationCache.java", "_extra": {"func_start": 4306, "func_end": 6074, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2417, "char_end": 2518, "patch_content": "", "patch_id": "seata/6e99ed4ff88a820e", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/protocol/RegisterTMRequest.java", "_extra": {"func_start": 1883, "func_end": 2705, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5966, "char_end": 6031, "patch_content": "", "patch_id": "seata/ccee9f4c3c1916e6", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/loader/EnhancedServiceLoader.java", "_extra": {"func_start": 5966, "func_end": 6031, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1724, "char_end": 1904, "patch_content": "", "patch_id": "seata/cdda2b4beb64e857", "repository": "seata/seata", "commit_sha": "", "file_name": "serializer/seata-serializer-protobuf/src/main/java/io/seata/serializer/protobuf/convertor/RegisterRMResponseConvertor.java", "_extra": {"func_start": 1419, "func_end": 2911, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2918, "char_end": 2977, "patch_content": "", "patch_id": "seata/61331533fbb67790", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/util/ArrayUtils.java", "_extra": {"func_start": 2526, "func_end": 2977, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2370, "char_end": 2572, "patch_content": "", "patch_id": "seata/701b25c673046bbb", "repository": "seata/seata", "commit_sha": "", "file_name": "tcc/src/main/java/io/seata/rm/tcc/remoting/parser/LocalTCCRemotingParser.java", "_extra": {"func_start": 1533, "func_end": 2889, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3320, "char_end": 3446, "patch_content": "", "patch_id": "seata/bb57e45640257936", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/rpc/netty/NettyRemotingServer.java", "_extra": {"func_start": 3282, "func_end": 3472, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15527, "char_end": 15613, "patch_content": "", "patch_id": "seata/414b64eb552aefc5", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/exec/mysql/MySQLUpdateJoinExecutor.java", "_extra": {"func_start": 15527, "func_end": 15613, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2550, "char_end": 2561, "patch_content": "", "patch_id": "seata/bc77e9c89fe42c7c", "repository": "seata/seata", "commit_sha": "", "file_name": "tm/src/main/java/io/seata/tm/api/GlobalTransactionContext.java", "_extra": {"func_start": 2225, "func_end": 2561, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2658, "char_end": 2746, "patch_content": "", "patch_id": "seata/a20dcd9442597c27", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/undo/mysql/MySQLUndoLogManager.java", "_extra": {"func_start": 2341, "func_end": 2959, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 35763, "char_end": 35900, "patch_content": "", "patch_id": "seata/20f8aab522e5fea5", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine-store/src/main/java/io/seata/saga/engine/store/db/DbAndReportTcStateLogStore.java", "_extra": {"func_start": 35726, "func_end": 36521, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 25051, "char_end": 25184, "patch_content": "", "patch_id": "seata/fcf7f2758631fb66", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine-store/src/main/java/io/seata/saga/engine/store/db/DbAndReportTcStateLogStore.java", "_extra": {"func_start": 24669, "func_end": 25666, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9774, "char_end": 9946, "patch_content": "", "patch_id": "seata/73f34a7a9a74c6af", "repository": "seata/seata", "commit_sha": "", "file_name": "config/seata-config-etcd3/src/main/java/io/seata/config/etcd3/EtcdConfiguration.java", "_extra": {"func_start": 9653, "func_end": 9994, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14351, "char_end": 14401, "patch_content": "", "patch_id": "seata/26a7ea2f92b7ed93", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/loader/EnhancedServiceLoader.java", "_extra": {"func_start": 14351, "func_end": 14401, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15766, "char_end": 15933, "patch_content": "", "patch_id": "seata/450e613a6d2616ab", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/loader/EnhancedServiceLoader.java", "_extra": {"func_start": 15301, "func_end": 16229, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1067, "char_end": 1106, "patch_content": "", "patch_id": "seata/a0f6e94b3993f223", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-antlr/src/main/java/io/seata/sqlparser/antlr/mysql/stream/ANTLRNoCaseStringStream.java", "_extra": {"func_start": 991, "func_end": 1163, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2983, "char_end": 3102, "patch_content": "", "patch_id": "seata/f80da188dfebb60f", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine-store/src/main/java/io/seata/saga/engine/pcext/interceptors/InSagaBranchHandlerInterceptor.java", "_extra": {"func_start": 2983, "func_end": 3243, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20572, "char_end": 20747, "patch_content": "", "patch_id": "seata/fea3e5c986c39b23", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/loader/EnhancedServiceLoader.java", "_extra": {"func_start": 20013, "func_end": 23122, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2923, "char_end": 3102, "patch_content": "", "patch_id": "seata/7775952fee13bcb8", "repository": "seata/seata", "commit_sha": "", "file_name": "discovery/seata-discovery-core/src/main/java/io/seata/discovery/loadbalance/ConsistentHashLoadBalance.java", "_extra": {"func_start": 2905, "func_end": 3117, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10849, "char_end": 10951, "patch_content": "", "patch_id": "seata/0eb1b6d48d1e9c73", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/ConnectionContext.java", "_extra": {"func_start": 10757, "func_end": 10951, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3089, "char_end": 3241, "patch_content": "", "patch_id": "seata/b4631bd101d244fa", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/console/impl/redis/GlobalLockRedisServiceImpl.java", "_extra": {"func_start": 2153, "func_end": 3242, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9680, "char_end": 9774, "patch_content": "", "patch_id": "seata/4cbaaa01bbac0e6e", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/storage/file/store/FileTransactionStoreManager.java", "_extra": {"func_start": 8528, "func_end": 9848, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5249, "char_end": 5355, "patch_content": "", "patch_id": "seata/7cc2b6fe96641630", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine/src/main/java/io/seata/saga/engine/pcext/handlers/ServiceTaskStateHandler.java", "_extra": {"func_start": 2444, "func_end": 5613, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2037, "char_end": 2154, "patch_content": "", "patch_id": "seata/966124dba77d83a2", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine-store/src/main/java/io/seata/saga/engine/store/db/DbStateLangStore.java", "_extra": {"func_start": 1924, "func_end": 2218, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2821, "char_end": 2875, "patch_content": "", "patch_id": "seata/92c195c8f39273df", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/storage/db/store/LogStoreDataBaseDAO.java", "_extra": {"func_start": 22989, "func_end": 23043, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2715, "char_end": 2844, "patch_content": "", "patch_id": "seata/1ca9874bbc82a9bd", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-druid/src/main/java/io/seata/sqlparser/druid/mysql/MySQLInsertRecognizer.java", "_extra": {"func_start": 2600, "func_end": 2975, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2846, "char_end": 2856, "patch_content": "", "patch_id": "seata/1f096ecbbde38e24", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine/src/main/java/io/seata/saga/engine/impl/ProcessCtrlStateMachineEngine.java", "_extra": {"func_start": 22203, "func_end": 25566, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2879, "char_end": 2923, "patch_content": "", "patch_id": "seata/48c3f26163c8cb18", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/undo/postgresql/PostgresqlUndoDeleteExecutor.java", "_extra": {"func_start": 2879, "func_end": 2923, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9229, "char_end": 9250, "patch_content": "", "patch_id": "seata/346da341dcff301e", "repository": "seata/seata", "commit_sha": "", "file_name": "tm/src/main/java/io/seata/tm/api/DefaultGlobalTransaction.java", "_extra": {"func_start": 9229, "func_end": 9250, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1213, "char_end": 1260, "patch_content": "", "patch_id": "seata/049c417cb626d531", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/protocol/RegisterRMResponse.java", "_extra": {"func_start": 1213, "func_end": 1260, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1752, "char_end": 1827, "patch_content": "", "patch_id": "seata/f1be46863bac37a9", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-druid/src/main/java/io/seata/sqlparser/druid/oracle/OracleDeleteRecognizer.java", "_extra": {"func_start": 1752, "func_end": 1827, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6323, "char_end": 6405, "patch_content": "", "patch_id": "seata/92aa253f19b4e8f9", "repository": "seata/seata", "commit_sha": "", "file_name": "spring/src/main/java/io/seata/rm/fence/SpringFenceHandler.java", "_extra": {"func_start": 5858, "func_end": 7492, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1271, "char_end": 1359, "patch_content": "", "patch_id": "seata/153269b89a4df179", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-antlr/src/main/java/io/seata/sqlparser/antlr/mysql/listener/UpdateSpecificationSqlListener.java", "_extra": {"func_start": 1270, "func_end": 1359, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6095, "char_end": 6150, "patch_content": "", "patch_id": "seata/cca8e2e92fef6d1c", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/session/BranchSession.java", "_extra": {"func_start": 6095, "func_end": 6150, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8810, "char_end": 8867, "patch_content": "", "patch_id": "seata/bf59868838acc25a", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/util/NetUtil.java", "_extra": {"func_start": 8762, "func_end": 9053, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3219, "char_end": 3435, "patch_content": "", "patch_id": "seata/a45455bdd3a5df2a", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/util/JdbcUtils.java", "_extra": {"func_start": 3071, "func_end": 4111, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1440, "char_end": 1494, "patch_content": "", "patch_id": "seata/d24ca595a695c169", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/transaction/xa/XACore.java", "_extra": {"func_start": 1360, "func_end": 1505, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3101, "char_end": 3237, "patch_content": "", "patch_id": "seata/205322a0be929d97", "repository": "seata/seata", "commit_sha": "", "file_name": "serializer/seata-serializer-protobuf/src/main/java/io/seata/serializer/protobuf/convertor/RegisterRMResponseConvertor.java", "_extra": {"func_start": 3027, "func_end": 3781, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3413, "char_end": 3469, "patch_content": "", "patch_id": "seata/a3d529ab66a66da2", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/AbstractTCInboundHandler.java", "_extra": {"func_start": 10872, "func_end": 11680, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12791, "char_end": 12892, "patch_content": "", "patch_id": "seata/090dfe98ee9f3f33", "repository": "seata/seata", "commit_sha": "", "file_name": "discovery/seata-discovery-zk/src/main/java/io/seata/discovery/registry/zk/ZookeeperRegisterServiceImpl.java", "_extra": {"func_start": 12791, "func_end": 12892, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1033, "char_end": 1106, "patch_content": "", "patch_id": "seata/2df761fb48f6e373", "repository": "seata/seata", "commit_sha": "", "file_name": "integration-tx-api/src/main/java/io/seata/integration/tx/api/remoting/parser/AbstractedRemotingParser.java", "_extra": {"func_start": 1033, "func_end": 1106, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2080, "char_end": 2172, "patch_content": "", "patch_id": "seata/ab52754203f1186e", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/rpc/processor/client/RmBranchCommitProcessor.java", "_extra": {"func_start": 1838, "func_end": 2172, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5223, "char_end": 5412, "patch_content": "", "patch_id": "seata/98d40f1b593f6f6d", "repository": "seata/seata", "commit_sha": "", "file_name": "discovery/seata-discovery-sofa/src/main/java/io/seata/discovery/registry/sofa/SofaRegistryServiceImpl.java", "_extra": {"func_start": 5185, "func_end": 6247, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1852, "char_end": 1900, "patch_content": "", "patch_id": "seata/59abbe751f8e39ec", "repository": "seata/seata", "commit_sha": "", "file_name": "tm/src/main/java/io/seata/tm/api/transaction/TransactionHookManager.java", "_extra": {"func_start": 1557, "func_end": 1900, "mode": "2-3-lines"}}

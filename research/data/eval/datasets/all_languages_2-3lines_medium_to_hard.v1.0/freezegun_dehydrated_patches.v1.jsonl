{"file_content": "", "char_start": 393, "char_end": 474, "patch_content": "", "patch_id": "spulec/freezegun/HsD3JJgZ", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/config.py", "_extra": {"func_start": 393, "func_end": 474, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3208, "char_end": 3367, "patch_content": "", "patch_id": "spulec/freezegun/GXPYN24S", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 3006, "func_end": 3368, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4815, "char_end": 4849, "patch_content": "", "patch_id": "spulec/freezegun/ya6bKRgq", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 4815, "func_end": 4849, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4927, "char_end": 5056, "patch_content": "", "patch_id": "spulec/freezegun/XZVdMFiH", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 4868, "func_end": 5057, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5864, "char_end": 5908, "patch_content": "", "patch_id": "spulec/freezegun/7RwyF8yY", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 5761, "func_end": 5909, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6059, "char_end": 6166, "patch_content": "", "patch_id": "spulec/freezegun/5SpejvXU", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 6008, "func_end": 6167, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7872, "char_end": 7988, "patch_content": "", "patch_id": "spulec/freezegun/pLYauBT9", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 7872, "func_end": 8197, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8514, "char_end": 8554, "patch_content": "", "patch_id": "spulec/freezegun/6FLPJiiL", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 8403, "func_end": 8554, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8860, "char_end": 8958, "patch_content": "", "patch_id": "spulec/freezegun/nUUK45ea", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 8860, "func_end": 8958, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9004, "char_end": 9038, "patch_content": "", "patch_id": "spulec/freezegun/Kqn2bkKp", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 9004, "func_end": 9038, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9081, "char_end": 9111, "patch_content": "", "patch_id": "spulec/freezegun/qGtBXdr7", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 9081, "func_end": 9111, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9652, "char_end": 9725, "patch_content": "", "patch_id": "spulec/freezegun/cRGhsbts", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 9563, "func_end": 9726, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10069, "char_end": 10171, "patch_content": "", "patch_id": "spulec/freezegun/ABUnipZo", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 10046, "func_end": 10172, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10253, "char_end": 10378, "patch_content": "", "patch_id": "spulec/freezegun/dP2HbLRU", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 10230, "func_end": 10494, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10552, "char_end": 10672, "patch_content": "", "patch_id": "spulec/freezegun/TAAyyVXS", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 10520, "func_end": 10672, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10717, "char_end": 10790, "patch_content": "", "patch_id": "spulec/freezegun/hyCgq8q5", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 10717, "func_end": 10972, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10993, "char_end": 11031, "patch_content": "", "patch_id": "spulec/freezegun/F5AQueat", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 10993, "func_end": 11031, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11340, "char_end": 11453, "patch_content": "", "patch_id": "spulec/freezegun/FCx5Qukg", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 11340, "func_end": 11453, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11499, "char_end": 11566, "patch_content": "", "patch_id": "spulec/freezegun/oVbG95E5", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 11499, "func_end": 11566, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11910, "char_end": 12053, "patch_content": "", "patch_id": "spulec/freezegun/XMvvmCqh", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 11910, "func_end": 12080, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12199, "char_end": 12246, "patch_content": "", "patch_id": "spulec/freezegun/JKnrHMDj", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 12152, "func_end": 12253, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13412, "char_end": 13446, "patch_content": "", "patch_id": "spulec/freezegun/9Bf4rQqf", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 13362, "func_end": 13498, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13675, "char_end": 13747, "patch_content": "", "patch_id": "spulec/freezegun/LQNDZCjT", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 13675, "func_end": 13747, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14136, "char_end": 14190, "patch_content": "", "patch_id": "spulec/freezegun/6uqTKh8P", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 13982, "func_end": 14191, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14678, "char_end": 14724, "patch_content": "", "patch_id": "spulec/freezegun/4SGMbqNp", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 14636, "func_end": 14725, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14758, "char_end": 14880, "patch_content": "", "patch_id": "spulec/freezegun/kqcfenHd", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 14758, "func_end": 14881, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15827, "char_end": 15967, "patch_content": "", "patch_id": "spulec/freezegun/Qrs8MXZw", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 15748, "func_end": 15968, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16730, "char_end": 16845, "patch_content": "", "patch_id": "spulec/freezegun/HVp2o7Xj", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 16006, "func_end": 18280, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18306, "char_end": 18334, "patch_content": "", "patch_id": "spulec/freezegun/9tsPuRKb", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 18306, "func_end": 18334, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20952, "char_end": 21037, "patch_content": "", "patch_id": "spulec/freezegun/F3By4e7h", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 18408, "func_end": 23116, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 25870, "char_end": 25917, "patch_content": "", "patch_id": "spulec/freezegun/jZiLKmKb", "repository": "spulec/freezegun", "commit_sha": "bd501e7a2d91c5a6eb20f4986670b95e31e7c0f1", "file_name": "freezegun/api.py", "_extra": {"func_start": 25870, "func_end": 25917, "mode": "2-3-lines"}}

{"file_content": "", "char_start": 976, "char_end": 1094, "patch_content": "", "patch_id": "jaegertracing/jaeger/LR6NJyXr", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/memory/options.go", "_extra": {"func_start": 976, "func_end": 1094, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5456, "char_end": 5487, "patch_content": "", "patch_id": "jaegertracing/jaeger/QjvzknPi", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/cassandra/spanstore/reader.go", "_extra": {"func_start": 5456, "func_end": 5487, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2221, "char_end": 2258, "patch_content": "", "patch_id": "jaegertracing/jaeger/VFa2oLX4", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/agent/app/agent.go", "_extra": {"func_start": 1737, "func_end": 2259, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12908, "char_end": 13016, "patch_content": "", "patch_id": "jaegertracing/jaeger/f9rJCKyD", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/sampling/strategystore/adaptive/processor.go", "_extra": {"func_start": 12744, "func_end": 13417, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3789, "char_end": 3864, "patch_content": "", "patch_id": "jaegertracing/jaeger/SoqGefxp", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/factory.go", "_extra": {"func_start": 3789, "func_end": 3864, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8204, "char_end": 8230, "patch_content": "", "patch_id": "jaegertracing/jaeger/ZzQPmiop", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/spanstore/reader.go", "_extra": {"func_start": 7696, "func_end": 8231, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1251, "char_end": 1297, "patch_content": "", "patch_id": "jaegertracing/jaeger/Que7nNS3", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/mappings/mapping.go", "_extra": {"func_start": 1227, "func_end": 1339, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2965, "char_end": 2997, "patch_content": "", "patch_id": "jaegertracing/jaeger/WtxAWGeM", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/agent/app/reporter/metrics.go", "_extra": {"func_start": 2828, "func_end": 2997, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18101, "char_end": 18133, "patch_content": "", "patch_id": "jaegertracing/jaeger/CruLefHm", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/spanstore/reader.go", "_extra": {"func_start": 18003, "func_end": 18447, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5092, "char_end": 5257, "patch_content": "", "patch_id": "jaegertracing/jaeger/BRNbD3yx", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/sampling/strategystore/adaptive/processor.go", "_extra": {"func_start": 4309, "func_end": 5531, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1663, "char_end": 1737, "patch_content": "", "patch_id": "jaegertracing/jaeger/FjH8UsLi", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/es-rollover/app/index_options.go", "_extra": {"func_start": 1663, "func_end": 1737, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8202, "char_end": 8245, "patch_content": "", "patch_id": "jaegertracing/jaeger/ghb2JtCx", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/memory/memory.go", "_extra": {"func_start": 8202, "func_end": 8245, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2280, "char_end": 2308, "patch_content": "", "patch_id": "jaegertracing/jaeger/ckXHhJdF", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/ingester/app/processor/decorator/retry.go", "_extra": {"func_start": 2246, "func_end": 2308, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5225, "char_end": 5250, "patch_content": "", "patch_id": "jaegertracing/jaeger/jPWaTCXh", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/spanstore/writer.go", "_extra": {"func_start": 5225, "func_end": 5250, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1220, "char_end": 1255, "patch_content": "", "patch_id": "jaegertracing/jaeger/JbMqknWP", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "crossdock/services/query.go", "_extra": {"func_start": 1197, "func_end": 1256, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1255, "char_end": 1299, "patch_content": "", "patch_id": "jaegertracing/jaeger/an5tdJqp", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "model/process.go", "_extra": {"func_start": 1196, "func_end": 1299, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1563, "char_end": 1586, "patch_content": "", "patch_id": "jaegertracing/jaeger/Ht3t4n3k", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "internal/metrics/expvar/cache.go", "_extra": {"func_start": 1475, "func_end": 1597, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7701, "char_end": 7762, "patch_content": "", "patch_id": "jaegertracing/jaeger/T2WmLG5a", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/cassandra/spanstore/reader.go", "_extra": {"func_start": 7701, "func_end": 7762, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1721, "char_end": 1824, "patch_content": "", "patch_id": "jaegertracing/jaeger/VmgA3Dc2", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "examples/hotrod/pkg/tracing/rpcmetrics/endpoints.go", "_extra": {"func_start": 1647, "func_end": 1863, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1578, "char_end": 1618, "patch_content": "", "patch_id": "jaegertracing/jaeger/Azygur45", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "model/converter/thrift/jaeger/to_domain.go", "_extra": {"func_start": 1578, "func_end": 1618, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2209, "char_end": 2281, "patch_content": "", "patch_id": "jaegertracing/jaeger/LCHMUXWS", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/es-index-cleaner/app/flags.go", "_extra": {"func_start": 1944, "func_end": 2281, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4399, "char_end": 4457, "patch_content": "", "patch_id": "jaegertracing/jaeger/Xi3WDaq3", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/tenancy/grpc.go", "_extra": {"func_start": 4242, "func_end": 4628, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2780, "char_end": 2901, "patch_content": "", "patch_id": "jaegertracing/jaeger/QV5kNvny", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/spanstore/service_operation.go", "_extra": {"func_start": 2248, "func_end": 2950, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1648, "char_end": 1937, "patch_content": "", "patch_id": "jaegertracing/jaeger/kNUyEGz8", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/config/tlscfg/flags.go", "_extra": {"func_start": 1557, "func_end": 2309, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3039, "char_end": 3144, "patch_content": "", "patch_id": "jaegertracing/jaeger/gERSfrkf", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/memory/factory.go", "_extra": {"func_start": 2955, "func_end": 3144, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2870, "char_end": 2932, "patch_content": "", "patch_id": "jaegertracing/jaeger/sWYYDzTg", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/config/tlscfg/cert_watcher.go", "_extra": {"func_start": 2759, "func_end": 3059, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3208, "char_end": 3342, "patch_content": "", "patch_id": "jaegertracing/jaeger/Xj33sace", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/config/tlscfg/cert_watcher.go", "_extra": {"func_start": 3144, "func_end": 3435, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3722, "char_end": 3775, "patch_content": "", "patch_id": "jaegertracing/jaeger/pL9wWEQB", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/config/tlscfg/cert_watcher.go", "_extra": {"func_start": 3481, "func_end": 3924, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1468, "char_end": 1518, "patch_content": "", "patch_id": "jaegertracing/jaeger/39uQ2ZJf", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "internal/metrics/jlibadapter/adapter.go", "_extra": {"func_start": 1384, "func_end": 1519, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7565, "char_end": 7611, "patch_content": "", "patch_id": "jaegertracing/jaeger/QnLfQxec", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "internal/metricstest/local.go", "_extra": {"func_start": 7547, "func_end": 7611, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1425, "char_end": 1486, "patch_content": "", "patch_id": "jaegertracing/jaeger/MgoJqD8S", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/gzipfs/gzip.go", "_extra": {"func_start": 1425, "func_end": 1486, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4592, "char_end": 4804, "patch_content": "", "patch_id": "jaegertracing/jaeger/bJHg9FMT", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/factory.go", "_extra": {"func_start": 4592, "func_end": 5649, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2613, "char_end": 2682, "patch_content": "", "patch_id": "jaegertracing/jaeger/pemXvaz7", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/cassandra/factory.go", "_extra": {"func_start": 2613, "func_end": 2835, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6527, "char_end": 6585, "patch_content": "", "patch_id": "jaegertracing/jaeger/UDtuvgKR", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/memory/memory.go", "_extra": {"func_start": 6434, "func_end": 6605, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1173, "char_end": 1307, "patch_content": "", "patch_id": "jaegertracing/jaeger/dM5rAynX", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/cassandra/metrics/table.go", "_extra": {"func_start": 1137, "func_end": 1307, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2531, "char_end": 2593, "patch_content": "", "patch_id": "jaegertracing/jaeger/Js33zuwu", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "internal/metrics/prometheus/cache.go", "_extra": {"func_start": 2531, "func_end": 2593, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 22348, "char_end": 22408, "patch_content": "", "patch_id": "jaegertracing/jaeger/GHfeaGmS", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/spanstore/reader.go", "_extra": {"func_start": 22348, "func_end": 22409, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1340, "char_end": 1387, "patch_content": "", "patch_id": "jaegertracing/jaeger/r4aL5dhf", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/memory/sampling.go", "_extra": {"func_start": 1340, "func_end": 1387, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2606, "char_end": 2655, "patch_content": "", "patch_id": "jaegertracing/jaeger/EJ6ph7D5", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/metrics/prometheus/metricsstore/dbmodel/to_domain.go", "_extra": {"func_start": 2547, "func_end": 2656, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3622, "char_end": 3672, "patch_content": "", "patch_id": "jaegertracing/jaeger/knVB7Anp", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/ingester/app/processor/decorator/retry.go", "_extra": {"func_start": 3453, "func_end": 3672, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2726, "char_end": 2752, "patch_content": "", "patch_id": "jaegertracing/jaeger/C2pyyPLk", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/fswatcher/fswatcher.go", "_extra": {"func_start": 2657, "func_end": 3028, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8216, "char_end": 8425, "patch_content": "", "patch_id": "jaegertracing/jaeger/dcPZ4HTb", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/cassandra/options.go", "_extra": {"func_start": 8028, "func_end": 8647, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1837, "char_end": 1892, "patch_content": "", "patch_id": "jaegertracing/jaeger/E6E5bNrp", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "model/adjuster/adjuster.go", "_extra": {"func_start": 1837, "func_end": 1892, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3991, "char_end": 4044, "patch_content": "", "patch_id": "jaegertracing/jaeger/c863H7cn", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/grpc/shared/grpc_client.go", "_extra": {"func_start": 3991, "func_end": 4044, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9076, "char_end": 9239, "patch_content": "", "patch_id": "jaegertracing/jaeger/EmspVkqi", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/sampling/strategystore/static/strategy_store.go", "_extra": {"func_start": 8783, "func_end": 9279, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1926, "char_end": 2334, "patch_content": "", "patch_id": "jaegertracing/jaeger/VmnCMtAw", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/metrics/prometheus/options.go", "_extra": {"func_start": 1738, "func_end": 2335, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5416, "char_end": 5511, "patch_content": "", "patch_id": "jaegertracing/jaeger/LwttkAMe", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/spanstore/dbmodel/to_domain.go", "_extra": {"func_start": 5416, "func_end": 6471, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1791, "char_end": 1887, "patch_content": "", "patch_id": "jaegertracing/jaeger/CxoheGvX", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/agent/app/servers/thriftudp/transport.go", "_extra": {"func_start": 1532, "func_end": 1932, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1928, "char_end": 1959, "patch_content": "", "patch_id": "jaegertracing/jaeger/Wc3hBhrA", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "internal/metrics/expvar/metrics.go", "_extra": {"func_start": 1928, "func_end": 1959, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1036, "char_end": 1070, "patch_content": "", "patch_id": "jaegertracing/jaeger/5mbLVdLs", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "model/converter/thrift/jaeger/sampling_from_domain.go", "_extra": {"func_start": 972, "func_end": 1489, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4570, "char_end": 4651, "patch_content": "", "patch_id": "jaegertracing/jaeger/ZQpX2T72", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/grpc/shared/grpc_client.go", "_extra": {"func_start": 4456, "func_end": 4752, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4157, "char_end": 4270, "patch_content": "", "patch_id": "jaegertracing/jaeger/K6ZXShpo", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/clientcfg/clientcfghttp/handler.go", "_extra": {"func_start": 4064, "func_end": 4296, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7481, "char_end": 7550, "patch_content": "", "patch_id": "jaegertracing/jaeger/tNoQb6e6", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/clientcfg/clientcfghttp/handler.go", "_extra": {"func_start": 7381, "func_end": 7635, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4390, "char_end": 4440, "patch_content": "", "patch_id": "jaegertracing/jaeger/tviPuWA4", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/spanstore/service_operation.go", "_extra": {"func_start": 4390, "func_end": 4512, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1118, "char_end": 1175, "patch_content": "", "patch_id": "jaegertracing/jaeger/YaTvzGJi", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/spanstore/dbmodel/to_domain.go", "_extra": {"func_start": 1118, "func_end": 1175, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13867, "char_end": 13936, "patch_content": "", "patch_id": "jaegertracing/jaeger/MQneKB3M", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/sampling/strategystore/adaptive/processor.go", "_extra": {"func_start": 13844, "func_end": 14037, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2722, "char_end": 2826, "patch_content": "", "patch_id": "jaegertracing/jaeger/Qn3ZMo3g", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/memory/sampling.go", "_extra": {"func_start": 2656, "func_end": 2827, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 934, "char_end": 1054, "patch_content": "", "patch_id": "jaegertracing/jaeger/MxrxySF9", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/testutils/logger.go", "_extra": {"func_start": 934, "func_end": 1055, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2035, "char_end": 2148, "patch_content": "", "patch_id": "jaegertracing/jaeger/6Et9eV74", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/testutils/logger.go", "_extra": {"func_start": 1980, "func_end": 2169, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2580, "char_end": 2643, "patch_content": "", "patch_id": "jaegertracing/jaeger/UH2iyHMe", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/cassandra/spanstore/dbmodel/cql_udt.go", "_extra": {"func_start": 3847, "func_end": 4135, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1716, "char_end": 1732, "patch_content": "", "patch_id": "jaegertracing/jaeger/kxcgUgP5", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/cache/lru.go", "_extra": {"func_start": 1641, "func_end": 2082, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2243, "char_end": 2299, "patch_content": "", "patch_id": "jaegertracing/jaeger/anDxxsAj", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/cache/lru.go", "_extra": {"func_start": 2243, "func_end": 2344, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4427, "char_end": 4617, "patch_content": "", "patch_id": "jaegertracing/jaeger/A8FJSEt2", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/cassandra/spanstore/reader.go", "_extra": {"func_start": 4342, "func_end": 5326, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3218, "char_end": 3296, "patch_content": "", "patch_id": "jaegertracing/jaeger/VcCKp2V4", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/httpmetrics/metrics.go", "_extra": {"func_start": 3005, "func_end": 3332, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2446, "char_end": 2452, "patch_content": "", "patch_id": "jaegertracing/jaeger/rchB6dzS", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/cassandra/spanstore/dbmodel/tag_filter_exact_match.go", "_extra": {"func_start": 2290, "func_end": 2474, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2463, "char_end": 2492, "patch_content": "", "patch_id": "jaegertracing/jaeger/h2nYmW5n", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/cassandra/factory.go", "_extra": {"func_start": 2463, "func_end": 2492, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2021, "char_end": 2047, "patch_content": "", "patch_id": "jaegertracing/jaeger/QKDYXQBY", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "internal/metricstest/local.go", "_extra": {"func_start": 1665, "func_end": 2092, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5242, "char_end": 5267, "patch_content": "", "patch_id": "jaegertracing/jaeger/kfKFUZsq", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/cassandra/spanstore/dbmodel/converter.go", "_extra": {"func_start": 4984, "func_end": 5268, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1119, "char_end": 1163, "patch_content": "", "patch_id": "jaegertracing/jaeger/RVPX5d7J", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "model/converter/thrift/zipkin/process_hashtable.go", "_extra": {"func_start": 1035, "func_end": 1164, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 842, "char_end": 876, "patch_content": "", "patch_id": "jaegertracing/jaeger/tUdNePL4", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/httpfs/prefixed.go", "_extra": {"func_start": 821, "func_end": 877, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2683, "char_end": 2797, "patch_content": "", "patch_id": "jaegertracing/jaeger/5iizhVrd", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/es/client/index_client.go", "_extra": {"func_start": 1770, "func_end": 2827, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3980, "char_end": 4005, "patch_content": "", "patch_id": "jaegertracing/jaeger/mZ2xqoos", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/es/client/index_client.go", "_extra": {"func_start": 3532, "func_end": 4214, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1694, "char_end": 1743, "patch_content": "", "patch_id": "jaegertracing/jaeger/JJk2mtio", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/sampling/strategystore/static/constants.go", "_extra": {"func_start": 1597, "func_end": 1754, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4877, "char_end": 5061, "patch_content": "", "patch_id": "jaegertracing/jaeger/hQd76syj", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/spanstore/reader.go", "_extra": {"func_start": 4877, "func_end": 6311, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6940, "char_end": 7001, "patch_content": "", "patch_id": "jaegertracing/jaeger/dD8y3Ej2", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/grpc/shared/grpc_client.go", "_extra": {"func_start": 6090, "func_end": 7091, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1687, "char_end": 1852, "patch_content": "", "patch_id": "jaegertracing/jaeger/Aqmmkohq", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/es-index-cleaner/app/index_filter.go", "_extra": {"func_start": 1382, "func_end": 2299, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1608, "char_end": 1655, "patch_content": "", "patch_id": "jaegertracing/jaeger/rdcHWpoJ", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/es/filter/alias.go", "_extra": {"func_start": 1411, "func_end": 1675, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1680, "char_end": 1742, "patch_content": "", "patch_id": "jaegertracing/jaeger/a6ucg5XB", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/memory/memory.go", "_extra": {"func_start": 1680, "func_end": 1742, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1693, "char_end": 1897, "patch_content": "", "patch_id": "jaegertracing/jaeger/cDKHuQhW", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/agent/app/flags.go", "_extra": {"func_start": 1531, "func_end": 2310, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1500, "char_end": 1513, "patch_content": "", "patch_id": "jaegertracing/jaeger/DsawscXi", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/grpc/shared/streaming_writer.go", "_extra": {"func_start": 1308, "func_end": 1513, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 25944, "char_end": 26006, "patch_content": "", "patch_id": "jaegertracing/jaeger/aE4ykXiV", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/spanstore/reader.go", "_extra": {"func_start": 25944, "func_end": 26007, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2750, "char_end": 2784, "patch_content": "", "patch_id": "jaegertracing/jaeger/kYskvLqX", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "ports/ports.go", "_extra": {"func_start": 2701, "func_end": 2784, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2166, "char_end": 2226, "patch_content": "", "patch_id": "jaegertracing/jaeger/SqMxsxdc", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "internal/metrics/expvar/factory.go", "_extra": {"func_start": 2166, "func_end": 2376, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1243, "char_end": 1352, "patch_content": "", "patch_id": "jaegertracing/jaeger/s7EZWqvb", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "pkg/recoveryhandler/zap.go", "_extra": {"func_start": 1203, "func_end": 1352, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7628, "char_end": 7715, "patch_content": "", "patch_id": "jaegertracing/jaeger/hGP55QBP", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/memory/memory.go", "_extra": {"func_start": 7346, "func_end": 8047, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2772, "char_end": 2830, "patch_content": "", "patch_id": "jaegertracing/jaeger/TDZa2UTZ", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/spanstore/dbmodel/from_domain.go", "_extra": {"func_start": 2772, "func_end": 2847, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7938, "char_end": 8001, "patch_content": "", "patch_id": "jaegertracing/jaeger/EL8MD36F", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/sampling/strategystore/adaptive/options.go", "_extra": {"func_start": 6066, "func_end": 8001, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1862, "char_end": 1936, "patch_content": "", "patch_id": "jaegertracing/jaeger/jBMLXBmc", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/agent/app/reporter/grpc/flags.go", "_extra": {"func_start": 1625, "func_end": 2006, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1878, "char_end": 1934, "patch_content": "", "patch_id": "jaegertracing/jaeger/oQUKjkji", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/agent/app/customtransport/buffered_read_transport.go", "_extra": {"func_start": 1846, "func_end": 1934, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4827, "char_end": 4944, "patch_content": "", "patch_id": "jaegertracing/jaeger/mr2c5VrT", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/agent/app/servers/thriftudp/transport.go", "_extra": {"func_start": 4727, "func_end": 4957, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6194, "char_end": 6298, "patch_content": "", "patch_id": "jaegertracing/jaeger/nbciZzHk", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/ingester/app/consumer/deadlock_detector.go", "_extra": {"func_start": 6165, "func_end": 6298, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6359, "char_end": 6430, "patch_content": "", "patch_id": "jaegertracing/jaeger/gL8yu9hE", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/ingester/app/consumer/deadlock_detector.go", "_extra": {"func_start": 6359, "func_end": 6430, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2083, "char_end": 2230, "patch_content": "", "patch_id": "jaegertracing/jaeger/nogTEE2r", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/ingester/app/consumer/consumer_metrics.go", "_extra": {"func_start": 2083, "func_end": 2230, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1299, "char_end": 1471, "patch_content": "", "patch_id": "jaegertracing/jaeger/gHvmQRjV", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/agent/app/reporter/grpc/flags.go", "_extra": {"func_start": 1096, "func_end": 1471, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1681, "char_end": 1714, "patch_content": "", "patch_id": "jaegertracing/jaeger/a2uysNrS", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "internal/metrics/expvar/metrics.go", "_extra": {"func_start": 1681, "func_end": 1714, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3854, "char_end": 3961, "patch_content": "", "patch_id": "jaegertracing/jaeger/sS4bKXMj", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "internal/metricstest/local.go", "_extra": {"func_start": 3804, "func_end": 3982, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5072, "char_end": 5118, "patch_content": "", "patch_id": "jaegertracing/jaeger/gpJPiFxe", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/agent/app/servers/thriftudp/transport.go", "_extra": {"func_start": 5072, "func_end": 5118, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 852, "char_end": 988, "patch_content": "", "patch_id": "jaegertracing/jaeger/gbzejCVq", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "model/adjuster/bad_span_references.go", "_extra": {"func_start": 852, "func_end": 1042, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1633, "char_end": 1672, "patch_content": "", "patch_id": "jaegertracing/jaeger/tmZcoWgw", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "model/adjuster/adjuster.go", "_extra": {"func_start": 1633, "func_end": 1672, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1480, "char_end": 1550, "patch_content": "", "patch_id": "jaegertracing/jaeger/ekCUsKGQ", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/metrics/prometheus/metricsstore/dbmodel/to_domain.go", "_extra": {"func_start": 1406, "func_end": 1619, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3309, "char_end": 3428, "patch_content": "", "patch_id": "jaegertracing/jaeger/qMFjpe8j", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/metrics/prometheus/metricsstore/reader.go", "_extra": {"func_start": 3919, "func_end": 4460, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6759, "char_end": 6876, "patch_content": "", "patch_id": "jaegertracing/jaeger/LtMPjhZb", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/collector/app/span_processor.go", "_extra": {"func_start": 6759, "func_end": 6876, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1195, "char_end": 1198, "patch_content": "", "patch_id": "jaegertracing/jaeger/FCJee6dy", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/metrics/prometheus/factory.go", "_extra": {"func_start": 1448, "func_end": 1582, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5649, "char_end": 5690, "patch_content": "", "patch_id": "jaegertracing/jaeger/Qe2wpxLu", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/ingester/app/flags.go", "_extra": {"func_start": 5649, "func_end": 5690, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2900, "char_end": 2994, "patch_content": "", "patch_id": "jaegertracing/jaeger/CCE5yrwk", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/dependencystore/storage.go", "_extra": {"func_start": 2900, "func_end": 3090, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 25790, "char_end": 25885, "patch_content": "", "patch_id": "jaegertracing/jaeger/hgHxRzM6", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/spanstore/reader.go", "_extra": {"func_start": 25746, "func_end": 25885, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8698, "char_end": 8718, "patch_content": "", "patch_id": "jaegertracing/jaeger/9Eb2xcUu", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/cassandra/spanstore/reader.go", "_extra": {"func_start": 14202, "func_end": 14650, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1204, "char_end": 1300, "patch_content": "", "patch_id": "jaegertracing/jaeger/mmWNToSP", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/collector/app/root_span_handler.go", "_extra": {"func_start": 926, "func_end": 1494, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8625, "char_end": 8697, "patch_content": "", "patch_id": "jaegertracing/jaeger/UVvJkFcF", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/cassandra/spanstore/reader.go", "_extra": {"func_start": 8625, "func_end": 9018, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1788, "char_end": 1816, "patch_content": "", "patch_id": "jaegertracing/jaeger/A8eR5vk7", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/sampling/strategystore/adaptive/factory.go", "_extra": {"func_start": 1788, "func_end": 1816, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3939, "char_end": 3999, "patch_content": "", "patch_id": "jaegertracing/jaeger/pKcoyfTb", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/memory/memory.go", "_extra": {"func_start": 2943, "func_end": 4019, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1370, "char_end": 1407, "patch_content": "", "patch_id": "jaegertracing/jaeger/Fv9suULR", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "crossdock/services/agent.go", "_extra": {"func_start": 1370, "func_end": 1407, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1225, "char_end": 1308, "patch_content": "", "patch_id": "jaegertracing/jaeger/UqSLQqVM", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/es-index-cleaner/app/index_filter.go", "_extra": {"func_start": 1225, "func_end": 1309, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5137, "char_end": 5234, "patch_content": "", "patch_id": "jaegertracing/jaeger/GX4UPRuu", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "model/converter/json/from_domain.go", "_extra": {"func_start": 5091, "func_end": 5247, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1609, "char_end": 1694, "patch_content": "", "patch_id": "jaegertracing/jaeger/Wn4VeX9D", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/grpc/shared/archive.go", "_extra": {"func_start": 1498, "func_end": 1795, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1605, "char_end": 1705, "patch_content": "", "patch_id": "jaegertracing/jaeger/P5S9rSJy", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/agent/app/reporter/connect_metrics.go", "_extra": {"func_start": 1483, "func_end": 1754, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1648, "char_end": 1720, "patch_content": "", "patch_id": "jaegertracing/jaeger/8VWvJFvA", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/collector/app/handler/http_thrift_handler.go", "_extra": {"func_start": 1648, "func_end": 1720, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2472, "char_end": 2717, "patch_content": "", "patch_id": "jaegertracing/jaeger/n7BcNq6R", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/agent/app/agent.go", "_extra": {"func_start": 2472, "func_end": 2994, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1929, "char_end": 2001, "patch_content": "", "patch_id": "jaegertracing/jaeger/m9uJAbQV", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/collector/app/handler/otlp_receiver.go", "_extra": {"func_start": 1816, "func_end": 2005, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2105, "char_end": 2123, "patch_content": "", "patch_id": "jaegertracing/jaeger/rzj6yeHH", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/ingester/app/processor/decorator/retry.go", "_extra": {"func_start": 2071, "func_end": 2123, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1066, "char_end": 1084, "patch_content": "", "patch_id": "jaegertracing/jaeger/ZSx7npR5", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "model/hash.go", "_extra": {"func_start": 1012, "func_end": 1108, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6165, "char_end": 6220, "patch_content": "", "patch_id": "jaegertracing/jaeger/R9fdnztc", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/collector/app/handler/otlp_receiver.go", "_extra": {"func_start": 6165, "func_end": 6220, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1722, "char_end": 1806, "patch_content": "", "patch_id": "jaegertracing/jaeger/TRUkosnn", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/collector/app/handler/grpc_handler.go", "_extra": {"func_start": 1703, "func_end": 1807, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 25377, "char_end": 25535, "patch_content": "", "patch_id": "jaegertracing/jaeger/4Uf9e9yv", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/spanstore/reader.go", "_extra": {"func_start": 25323, "func_end": 25655, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1068, "char_end": 1104, "patch_content": "", "patch_id": "jaegertracing/jaeger/Pntd5mdG", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/collector/app/sanitizer/sanitizer.go", "_extra": {"func_start": 1044, "func_end": 1105, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2225, "char_end": 2283, "patch_content": "", "patch_id": "jaegertracing/jaeger/oStchzyg", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/agent/app/reporter/grpc/reporter.go", "_extra": {"func_start": 2154, "func_end": 2338, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16317, "char_end": 16358, "patch_content": "", "patch_id": "jaegertracing/jaeger/Qq2aQo7E", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/options.go", "_extra": {"func_start": 16317, "func_end": 16358, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2762, "char_end": 2832, "patch_content": "", "patch_id": "jaegertracing/jaeger/8Zziryi4", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/ingester/app/consumer/offset/manager.go", "_extra": {"func_start": 2590, "func_end": 3015, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1365, "char_end": 1418, "patch_content": "", "patch_id": "jaegertracing/jaeger/s7DB39Az", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "model/adjuster/span_id_deduper.go", "_extra": {"func_start": 1234, "func_end": 1423, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1436, "char_end": 1492, "patch_content": "", "patch_id": "jaegertracing/jaeger/KMAjZMDC", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/collector/app/sanitizer/service_name_sanitizer.go", "_extra": {"func_start": 1301, "func_end": 1493, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9558, "char_end": 9630, "patch_content": "", "patch_id": "jaegertracing/jaeger/McpFSrDf", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/grpc/shared/grpc_handler.go", "_extra": {"func_start": 9430, "func_end": 9679, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14211, "char_end": 14268, "patch_content": "", "patch_id": "jaegertracing/jaeger/diWVSLcH", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/sampling/strategystore/adaptive/processor.go", "_extra": {"func_start": 14084, "func_end": 14268, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4478, "char_end": 4558, "patch_content": "", "patch_id": "jaegertracing/jaeger/kGsQFfge", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/collector/app/sanitizer/cache/auto_refresh_cache.go", "_extra": {"func_start": 4241, "func_end": 4604, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1865, "char_end": 1938, "patch_content": "", "patch_id": "jaegertracing/jaeger/G8V25k5Q", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/collector/app/zipkin/http_handler.go", "_extra": {"func_start": 1743, "func_end": 1938, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6031, "char_end": 6131, "patch_content": "", "patch_id": "jaegertracing/jaeger/emPQFmZH", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/collector/app/zipkin/http_handler.go", "_extra": {"func_start": 5929, "func_end": 6147, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2510, "char_end": 2671, "patch_content": "", "patch_id": "jaegertracing/jaeger/FyGdbs2p", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/cassandra/samplingstore/storage.go", "_extra": {"func_start": 2461, "func_end": 2671, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3594, "char_end": 3655, "patch_content": "", "patch_id": "jaegertracing/jaeger/Z4hRe27G", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/es/spanstore/dbmodel/from_domain.go", "_extra": {"func_start": 3480, "func_end": 3754, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1877, "char_end": 1920, "patch_content": "", "patch_id": "jaegertracing/jaeger/VWHt2nYz", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/cassandra/spanstore/writer_options.go", "_extra": {"func_start": 1795, "func_end": 2074, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1005, "char_end": 1160, "patch_content": "", "patch_id": "jaegertracing/jaeger/7QMWRRTw", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/flags/flags.go", "_extra": {"func_start": 1005, "func_end": 1160, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4184, "char_end": 4271, "patch_content": "", "patch_id": "jaegertracing/jaeger/g8fJK9J6", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/sampling/strategystore/static/strategy_store.go", "_extra": {"func_start": 4184, "func_end": 4406, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1493, "char_end": 1569, "patch_content": "", "patch_id": "jaegertracing/jaeger/QYVPcSeS", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/collector/app/zipkin/zipkindeser/annotation.go", "_extra": {"func_start": 1400, "func_end": 1624, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1119, "char_end": 1204, "patch_content": "", "patch_id": "jaegertracing/jaeger/MStL5HxP", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/sampling/strategystore/adaptive/cache.go", "_extra": {"func_start": 1088, "func_end": 1205, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4003, "char_end": 4060, "patch_content": "", "patch_id": "jaegertracing/jaeger/cG729PfQ", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/collector/app/zipkin/zipkindeser/json.go", "_extra": {"func_start": 4003, "func_end": 4060, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1542, "char_end": 1584, "patch_content": "", "patch_id": "jaegertracing/jaeger/DffbSEZC", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "internal/metricstest/metricstest.go", "_extra": {"func_start": 1442, "func_end": 1710, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6464, "char_end": 6566, "patch_content": "", "patch_id": "jaegertracing/jaeger/sZ7NZygR", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/collector/app/zipkin/zipkindeser/json.go", "_extra": {"func_start": 6464, "func_end": 6581, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1158, "char_end": 1197, "patch_content": "", "patch_id": "jaegertracing/jaeger/97wXEwAE", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/metrics/prometheus/factory.go", "_extra": {"func_start": 1140, "func_end": 1198, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8267, "char_end": 8392, "patch_content": "", "patch_id": "jaegertracing/jaeger/rtpVfd36", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/cassandra/samplingstore/storage.go", "_extra": {"func_start": 8237, "func_end": 8412, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3187, "char_end": 3246, "patch_content": "", "patch_id": "jaegertracing/jaeger/Fb6cwCeG", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/collector/app/zipkin/zipkindeser/jsonv2.go", "_extra": {"func_start": 3112, "func_end": 3602, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6203, "char_end": 6223, "patch_content": "", "patch_id": "jaegertracing/jaeger/drzk7uUq", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/storage/memory/memory.go", "_extra": {"func_start": 6150, "func_end": 6309, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5446, "char_end": 5493, "patch_content": "", "patch_id": "jaegertracing/jaeger/PenBeV2m", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/collector/app/zipkin/zipkindeser/jsonv2.go", "_extra": {"func_start": 5184, "func_end": 5494, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1629, "char_end": 1758, "patch_content": "", "patch_id": "jaegertracing/jaeger/5dCo25GC", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "internal/metrics/metricsbuilder/builder.go", "_extra": {"func_start": 1596, "func_end": 1885, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1029, "char_end": 1133, "patch_content": "", "patch_id": "jaegertracing/jaeger/ehMqeqm5", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "cmd/es-rollover/app/rollover/flags.go", "_extra": {"func_start": 1029, "func_end": 1133, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7173, "char_end": 7198, "patch_content": "", "patch_id": "jaegertracing/jaeger/qzpMb2WL", "repository": "jaegertracing/jaeger", "commit_sha": "3021d314f71b9ce0707e8f5a443dd49b6a73d7da", "file_name": "plugin/metrics/prometheus/metricsstore/reader.go", "_extra": {"func_start": 6917, "func_end": 7629, "mode": "2-3-lines"}}

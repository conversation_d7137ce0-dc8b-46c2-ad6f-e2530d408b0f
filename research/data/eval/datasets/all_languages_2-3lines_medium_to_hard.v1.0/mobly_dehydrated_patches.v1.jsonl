{"file_content": "", "char_start": 1556, "char_end": 1686, "patch_content": "", "patch_id": "google/mobly/gnym6bU7", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controller_manager.py", "_extra": {"func_start": 1261, "func_end": 1722, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2196, "char_end": 2339, "patch_content": "", "patch_id": "google/mobly/4B5yCkPU", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controller_manager.py", "_extra": {"func_start": 2106, "func_end": 2340, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3561, "char_end": 3709, "patch_content": "", "patch_id": "google/mobly/mQRv62Bc", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controller_manager.py", "_extra": {"func_start": 3524, "func_end": 5670, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6153, "char_end": 6243, "patch_content": "", "patch_id": "google/mobly/Exq6ekrk", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controller_manager.py", "_extra": {"func_start": 5826, "func_end": 6244, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7405, "char_end": 7539, "patch_content": "", "patch_id": "google/mobly/R6J3QWzk", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controller_manager.py", "_extra": {"func_start": 6711, "func_end": 7597, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1535, "char_end": 1637, "patch_content": "", "patch_id": "google/mobly/PGQhX2Zo", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/asserts.py", "_extra": {"func_start": 1412, "func_end": 1784, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7440, "char_end": 7522, "patch_content": "", "patch_id": "google/mobly/sR9rVheG", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/asserts.py", "_extra": {"func_start": 7440, "func_end": 7631, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8269, "char_end": 8389, "patch_content": "", "patch_id": "google/mobly/MnNggrk2", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/asserts.py", "_extra": {"func_start": 8269, "func_end": 8468, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11821, "char_end": 11886, "patch_content": "", "patch_id": "google/mobly/MHrxh5LW", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/asserts.py", "_extra": {"func_start": 11719, "func_end": 11886, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13185, "char_end": 13220, "patch_content": "", "patch_id": "google/mobly/dBnCKCAs", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/asserts.py", "_extra": {"func_start": 13185, "func_end": 13221, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16609, "char_end": 16736, "patch_content": "", "patch_id": "google/mobly/HFgeY55k", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/asserts.py", "_extra": {"func_start": 16464, "func_end": 17310, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1440, "char_end": 1496, "patch_content": "", "patch_id": "google/mobly/tN3GLmgb", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/runtime_test_info.py", "_extra": {"func_start": 1343, "func_end": 1496, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1728, "char_end": 1805, "patch_content": "", "patch_id": "google/mobly/Y4X2m3jt", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/runtime_test_info.py", "_extra": {"func_start": 1728, "func_end": 1805, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2538, "char_end": 2589, "patch_content": "", "patch_id": "google/mobly/g3Zb3PzH", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/utils.py", "_extra": {"func_start": 2538, "func_end": 2589, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4548, "char_end": 4651, "patch_content": "", "patch_id": "google/mobly/GTnmk3Je", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/utils.py", "_extra": {"func_start": 4548, "func_end": 4739, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8708, "char_end": 8782, "patch_content": "", "patch_id": "google/mobly/ioDJxovP", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/utils.py", "_extra": {"func_start": 8061, "func_end": 8967, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14458, "char_end": 14535, "patch_content": "", "patch_id": "google/mobly/UxvXBCnd", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/utils.py", "_extra": {"func_start": 13559, "func_end": 14799, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18274, "char_end": 18366, "patch_content": "", "patch_id": "google/mobly/qCTZCEAG", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/utils.py", "_extra": {"func_start": 18274, "func_end": 18842, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20048, "char_end": 20177, "patch_content": "", "patch_id": "google/mobly/LxozFUEv", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/utils.py", "_extra": {"func_start": 20033, "func_end": 20195, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20763, "char_end": 20826, "patch_content": "", "patch_id": "google/mobly/iDbXNnRT", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/utils.py", "_extra": {"func_start": 20567, "func_end": 20826, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 21347, "char_end": 21428, "patch_content": "", "patch_id": "google/mobly/pKN3DRnt", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/utils.py", "_extra": {"func_start": 21257, "func_end": 21531, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2238, "char_end": 2341, "patch_content": "", "patch_id": "google/mobly/qRYZzZJn", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/config_parser.py", "_extra": {"func_start": 2160, "func_end": 2653, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6488, "char_end": 6519, "patch_content": "", "patch_id": "google/mobly/VWjRxGJ4", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/config_parser.py", "_extra": {"func_start": 6488, "func_end": 6519, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6575, "char_end": 6644, "patch_content": "", "patch_id": "google/mobly/cJRGz4Rh", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/config_parser.py", "_extra": {"func_start": 6541, "func_end": 6644, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1641, "char_end": 1668, "patch_content": "", "patch_id": "google/mobly/RGm5hJbz", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/records.py", "_extra": {"func_start": 1579, "func_end": 1848, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3968, "char_end": 3995, "patch_content": "", "patch_id": "google/mobly/KW249aRu", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/records.py", "_extra": {"func_start": 3968, "func_end": 3995, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8313, "char_end": 8355, "patch_content": "", "patch_id": "google/mobly/fMF3NGfo", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/records.py", "_extra": {"func_start": 8313, "func_end": 8645, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11351, "char_end": 11464, "patch_content": "", "patch_id": "google/mobly/GkomvmCV", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/records.py", "_extra": {"func_start": 11195, "func_end": 11488, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12476, "char_end": 12552, "patch_content": "", "patch_id": "google/mobly/Yu8fbPTE", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/records.py", "_extra": {"func_start": 12476, "func_end": 12552, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12675, "char_end": 12793, "patch_content": "", "patch_id": "google/mobly/pkhZL8ED", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/records.py", "_extra": {"func_start": 12675, "func_end": 12793, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13113, "char_end": 13226, "patch_content": "", "patch_id": "google/mobly/WgXoXMtN", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/records.py", "_extra": {"func_start": 13113, "func_end": 13288, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13617, "char_end": 13730, "patch_content": "", "patch_id": "google/mobly/FaWpwzcv", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/records.py", "_extra": {"func_start": 13591, "func_end": 13972, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15720, "char_end": 15830, "patch_content": "", "patch_id": "google/mobly/VCCeDS8H", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/records.py", "_extra": {"func_start": 15610, "func_end": 16039, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16614, "char_end": 16781, "patch_content": "", "patch_id": "google/mobly/5y36z7Gj", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/records.py", "_extra": {"func_start": 16551, "func_end": 17442, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18820, "char_end": 18908, "patch_content": "", "patch_id": "google/mobly/Z8EYhcuF", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/records.py", "_extra": {"func_start": 18541, "func_end": 18931, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 19389, "char_end": 19512, "patch_content": "", "patch_id": "google/mobly/o973EajU", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/records.py", "_extra": {"func_start": 19257, "func_end": 19648, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 21332, "char_end": 21395, "patch_content": "", "patch_id": "google/mobly/P7FTY2kH", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/records.py", "_extra": {"func_start": 21287, "func_end": 21440, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 21546, "char_end": 21741, "patch_content": "", "patch_id": "google/mobly/9FSti45G", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/records.py", "_extra": {"func_start": 21546, "func_end": 21777, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 22045, "char_end": 22107, "patch_content": "", "patch_id": "google/mobly/JWKLExEA", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/records.py", "_extra": {"func_start": 22045, "func_end": 22107, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23124, "char_end": 23170, "patch_content": "", "patch_id": "google/mobly/XFYBQQTG", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/records.py", "_extra": {"func_start": 22926, "func_end": 23170, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3584, "char_end": 3641, "patch_content": "", "patch_id": "google/mobly/92sZwbLN", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/test_runner.py", "_extra": {"func_start": 3100, "func_end": 4398, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4736, "char_end": 4888, "patch_content": "", "patch_id": "google/mobly/RRcWuzdn", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/test_runner.py", "_extra": {"func_start": 4729, "func_end": 5033, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6674, "char_end": 6735, "patch_content": "", "patch_id": "google/mobly/gAv6TsHf", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/test_runner.py", "_extra": {"func_start": 6674, "func_end": 6820, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8338, "char_end": 8441, "patch_content": "", "patch_id": "google/mobly/T9egcDQB", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/test_runner.py", "_extra": {"func_start": 8196, "func_end": 8441, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8489, "char_end": 8567, "patch_content": "", "patch_id": "google/mobly/pnFvQnxY", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/test_runner.py", "_extra": {"func_start": 8489, "func_end": 8567, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9798, "char_end": 9879, "patch_content": "", "patch_id": "google/mobly/MySwUtT9", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/test_runner.py", "_extra": {"func_start": 9661, "func_end": 9879, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10866, "char_end": 10940, "patch_content": "", "patch_id": "google/mobly/FuxpTFH2", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/test_runner.py", "_extra": {"func_start": 10527, "func_end": 10992, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12035, "char_end": 12232, "patch_content": "", "patch_id": "google/mobly/EP36VfBP", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/test_runner.py", "_extra": {"func_start": 11751, "func_end": 12482, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13221, "char_end": 13267, "patch_content": "", "patch_id": "google/mobly/4AqeKeHe", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/test_runner.py", "_extra": {"func_start": 12948, "func_end": 13267, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10012, "char_end": 10114, "patch_content": "", "patch_id": "google/mobly/g4nHczcd", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 9446, "func_end": 10450, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11706, "char_end": 11744, "patch_content": "", "patch_id": "google/mobly/XbjFiqz8", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 11706, "func_end": 11744, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12212, "char_end": 12334, "patch_content": "", "patch_id": "google/mobly/ejY2JPzR", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 12053, "func_end": 12335, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12914, "char_end": 13012, "patch_content": "", "patch_id": "google/mobly/3BZmobAT", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 12793, "func_end": 13150, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14718, "char_end": 14769, "patch_content": "", "patch_id": "google/mobly/SSMbqVF9", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 14613, "func_end": 15028, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15372, "char_end": 15487, "patch_content": "", "patch_id": "google/mobly/rFmDsEU5", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 15266, "func_end": 15876, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16789, "char_end": 16836, "patch_content": "", "patch_id": "google/mobly/pwHjGoTc", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 16685, "func_end": 16836, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 17130, "char_end": 17241, "patch_content": "", "patch_id": "google/mobly/YifqTzwz", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 17130, "func_end": 17241, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 17867, "char_end": 17972, "patch_content": "", "patch_id": "google/mobly/EuMUoQSW", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 17704, "func_end": 18505, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20604, "char_end": 20640, "patch_content": "", "patch_id": "google/mobly/NBEyxoXV", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 19733, "func_end": 20931, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 21642, "char_end": 21670, "patch_content": "", "patch_id": "google/mobly/4f7dNLGt", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 21484, "func_end": 21739, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23524, "char_end": 23719, "patch_content": "", "patch_id": "google/mobly/gTTznoSu", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 23501, "func_end": 23961, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 25062, "char_end": 25163, "patch_content": "", "patch_id": "google/mobly/Ei2E89xe", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 24533, "func_end": 25163, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 25698, "char_end": 25864, "patch_content": "", "patch_id": "google/mobly/56EuEZQx", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 25698, "func_end": 26129, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 26783, "char_end": 26905, "patch_content": "", "patch_id": "google/mobly/fXREKrJS", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 26562, "func_end": 27544, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 28038, "char_end": 28113, "patch_content": "", "patch_id": "google/mobly/gYDDMTMB", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 28038, "func_end": 28113, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 28809, "char_end": 28939, "patch_content": "", "patch_id": "google/mobly/ZxppRQp7", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 28743, "func_end": 29561, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 31068, "char_end": 31245, "patch_content": "", "patch_id": "google/mobly/JQS72jAi", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 31068, "func_end": 31246, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 31944, "char_end": 32067, "patch_content": "", "patch_id": "google/mobly/ekNypeHo", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 31809, "func_end": 32237, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 33876, "char_end": 33934, "patch_content": "", "patch_id": "google/mobly/Pwg3zNcd", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_instrumentation_test.py", "_extra": {"func_start": 33265, "func_end": 33934, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5087, "char_end": 5189, "patch_content": "", "patch_id": "google/mobly/Mf3ZJVrn", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/suite_runner.py", "_extra": {"func_start": 4901, "func_end": 5631, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3018, "char_end": 3056, "patch_content": "", "patch_id": "google/mobly/ZyxB3Jji", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 3635, "func_end": 3964, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6669, "char_end": 6788, "patch_content": "", "patch_id": "google/mobly/KEEZEkSE", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 6245, "func_end": 7244, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8549, "char_end": 8638, "patch_content": "", "patch_id": "google/mobly/iaRRDLRh", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 8429, "func_end": 9218, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12624, "char_end": 12701, "patch_content": "", "patch_id": "google/mobly/m8qCnsax", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 12447, "func_end": 12701, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13069, "char_end": 13120, "patch_content": "", "patch_id": "google/mobly/AsU3ciHc", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 12885, "func_end": 13699, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20236, "char_end": 20261, "patch_content": "", "patch_id": "google/mobly/Lo3qBVby", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 20236, "func_end": 20261, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20926, "char_end": 20974, "patch_content": "", "patch_id": "google/mobly/TM6bK6mh", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 20889, "func_end": 20975, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23141, "char_end": 23258, "patch_content": "", "patch_id": "google/mobly/Sim8JYCm", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 22678, "func_end": 23259, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23811, "char_end": 23949, "patch_content": "", "patch_id": "google/mobly/tUH5CXnu", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 23745, "func_end": 23949, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 24501, "char_end": 24562, "patch_content": "", "patch_id": "google/mobly/nr8mEP7J", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 24386, "func_end": 25018, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 25857, "char_end": 25915, "patch_content": "", "patch_id": "google/mobly/45BzsuFD", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 25743, "func_end": 26623, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 29525, "char_end": 29565, "patch_content": "", "patch_id": "google/mobly/ByvYPPBP", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 27631, "func_end": 30991, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 33522, "char_end": 33628, "patch_content": "", "patch_id": "google/mobly/rZKjWrtV", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 32605, "func_end": 33840, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 34856, "char_end": 34983, "patch_content": "", "patch_id": "google/mobly/HH3fqEZV", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 34836, "func_end": 35048, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 36042, "char_end": 36118, "patch_content": "", "patch_id": "google/mobly/7P6qhFB9", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 35530, "func_end": 36118, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 36574, "char_end": 36715, "patch_content": "", "patch_id": "google/mobly/WGSzk3iK", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 36406, "func_end": 36786, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 37732, "char_end": 37823, "patch_content": "", "patch_id": "google/mobly/mPVFo8xG", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 37410, "func_end": 39626, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 39982, "char_end": 40120, "patch_content": "", "patch_id": "google/mobly/AaZmTYmF", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_test.py", "_extra": {"func_start": 39703, "func_end": 40439, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1224, "char_end": 1272, "patch_content": "", "patch_id": "google/mobly/DkMu5dtD", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/signals.py", "_extra": {"func_start": 1133, "func_end": 1389, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1459, "char_end": 1505, "patch_content": "", "patch_id": "google/mobly/D7WFSA4S", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/expects.py", "_extra": {"func_start": 1459, "func_end": 1505, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1860, "char_end": 1887, "patch_content": "", "patch_id": "google/mobly/67AFjiC5", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/expects.py", "_extra": {"func_start": 1860, "func_end": 1887, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2324, "char_end": 2423, "patch_content": "", "patch_id": "google/mobly/8ZwqCC9m", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/expects.py", "_extra": {"func_start": 2324, "func_end": 2424, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3403, "char_end": 3548, "patch_content": "", "patch_id": "google/mobly/NeVkWMiX", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/expects.py", "_extra": {"func_start": 3396, "func_end": 3575, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1159, "char_end": 1218, "patch_content": "", "patch_id": "google/mobly/YUWw8oHD", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_suite.py", "_extra": {"func_start": 1159, "func_end": 1218, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2004, "char_end": 2099, "patch_content": "", "patch_id": "google/mobly/H5Qt7AJc", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/base_suite.py", "_extra": {"func_start": 1985, "func_end": 2099, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3193, "char_end": 3280, "patch_content": "", "patch_id": "google/mobly/sxaaZjgQ", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/logger.py", "_extra": {"func_start": 3193, "func_end": 3319, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3844, "char_end": 3947, "patch_content": "", "patch_id": "google/mobly/JPQMLQqD", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/logger.py", "_extra": {"func_start": 3809, "func_end": 3948, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6695, "char_end": 6765, "patch_content": "", "patch_id": "google/mobly/867VcqXp", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/logger.py", "_extra": {"func_start": 6661, "func_end": 6782, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7074, "char_end": 7185, "patch_content": "", "patch_id": "google/mobly/pKUQn7RX", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/logger.py", "_extra": {"func_start": 7074, "func_end": 7185, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8571, "char_end": 8633, "patch_content": "", "patch_id": "google/mobly/MajD43Ga", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/logger.py", "_extra": {"func_start": 8434, "func_end": 8634, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8995, "char_end": 9131, "patch_content": "", "patch_id": "google/mobly/WGJdupum", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/logger.py", "_extra": {"func_start": 8918, "func_end": 9426, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11236, "char_end": 11415, "patch_content": "", "patch_id": "google/mobly/LjYfFuz9", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/logger.py", "_extra": {"func_start": 11026, "func_end": 11497, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3695, "char_end": 3810, "patch_content": "", "patch_id": "google/mobly/6Xf5JBxP", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 3422, "func_end": 4010, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4900, "char_end": 5063, "patch_content": "", "patch_id": "google/mobly/rXkhtnWb", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 4746, "func_end": 5064, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5472, "char_end": 5551, "patch_content": "", "patch_id": "google/mobly/bfEWSdFC", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 5367, "func_end": 5924, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6667, "char_end": 6718, "patch_content": "", "patch_id": "google/mobly/Jh9CqNHW", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 6486, "func_end": 6869, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8350, "char_end": 8404, "patch_content": "", "patch_id": "google/mobly/4r2xr6Tr", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 8276, "func_end": 8404, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10349, "char_end": 10407, "patch_content": "", "patch_id": "google/mobly/nXoUQTyx", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 10317, "func_end": 10408, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11151, "char_end": 11248, "patch_content": "", "patch_id": "google/mobly/6C5Co68c", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 10915, "func_end": 11301, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11815, "char_end": 11902, "patch_content": "", "patch_id": "google/mobly/nhzGy5ZB", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 11814, "func_end": 12018, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13022, "char_end": 13111, "patch_content": "", "patch_id": "google/mobly/6Ng6qR7Y", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 12813, "func_end": 13266, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16500, "char_end": 16618, "patch_content": "", "patch_id": "google/mobly/jnVEScfr", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 15633, "func_end": 16682, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16705, "char_end": 16754, "patch_content": "", "patch_id": "google/mobly/iKZWFtpb", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 16705, "func_end": 16754, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 19874, "char_end": 19912, "patch_content": "", "patch_id": "google/mobly/9d83Erp8", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 19874, "func_end": 19912, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20072, "char_end": 20137, "patch_content": "", "patch_id": "google/mobly/FFVRcxFZ", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 20029, "func_end": 20137, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20763, "char_end": 20843, "patch_content": "", "patch_id": "google/mobly/QfA3mZee", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 20240, "func_end": 20843, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 26125, "char_end": 26214, "patch_content": "", "patch_id": "google/mobly/K3XyDUTp", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 26125, "func_end": 26305, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 27209, "char_end": 27259, "patch_content": "", "patch_id": "google/mobly/BVosovyV", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 27209, "func_end": 27259, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 27662, "char_end": 27737, "patch_content": "", "patch_id": "google/mobly/5ofra5MQ", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 27662, "func_end": 27737, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 29706, "char_end": 29879, "patch_content": "", "patch_id": "google/mobly/mpsXJiDx", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 29674, "func_end": 29960, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 31397, "char_end": 31480, "patch_content": "", "patch_id": "google/mobly/BePmXiPn", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 31189, "func_end": 31487, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 32782, "char_end": 32931, "patch_content": "", "patch_id": "google/mobly/ge9fuQTJ", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 32664, "func_end": 33368, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 34476, "char_end": 34560, "patch_content": "", "patch_id": "google/mobly/oans9Abf", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 34109, "func_end": 35740, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 37707, "char_end": 37795, "patch_content": "", "patch_id": "google/mobly/ZLrNEqTK", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 37662, "func_end": 38128, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 38235, "char_end": 38361, "patch_content": "", "patch_id": "google/mobly/VZQLwpc3", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 38235, "func_end": 38397, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 39381, "char_end": 39476, "patch_content": "", "patch_id": "google/mobly/8xZvTKtq", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 39381, "func_end": 39516, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 40073, "char_end": 40158, "patch_content": "", "patch_id": "google/mobly/4GStFTbP", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device.py", "_extra": {"func_start": 40073, "func_end": 40158, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4227, "char_end": 4322, "patch_content": "", "patch_id": "google/mobly/fqSdPTLN", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/jsonrpc_client_base.py", "_extra": {"func_start": 3964, "func_end": 4323, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4345, "char_end": 4367, "patch_content": "", "patch_id": "google/mobly/LPKsrPcH", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/jsonrpc_client_base.py", "_extra": {"func_start": 4345, "func_end": 4367, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7990, "char_end": 8048, "patch_content": "", "patch_id": "google/mobly/gXPxmFZZ", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/jsonrpc_client_base.py", "_extra": {"func_start": 7990, "func_end": 8145, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8441, "char_end": 8537, "patch_content": "", "patch_id": "google/mobly/HWBFMp2F", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/jsonrpc_client_base.py", "_extra": {"func_start": 8418, "func_end": 8537, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9383, "char_end": 9511, "patch_content": "", "patch_id": "google/mobly/BUqs7KdW", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/jsonrpc_client_base.py", "_extra": {"func_start": 9245, "func_end": 9894, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11284, "char_end": 11496, "patch_content": "", "patch_id": "google/mobly/sxeWyFCH", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/jsonrpc_client_base.py", "_extra": {"func_start": 10659, "func_end": 11724, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12401, "char_end": 12462, "patch_content": "", "patch_id": "google/mobly/3sSpFmyE", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/jsonrpc_client_base.py", "_extra": {"func_start": 12400, "func_end": 12483, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13169, "char_end": 13204, "patch_content": "", "patch_id": "google/mobly/62i5rgmJ", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/jsonrpc_client_base.py", "_extra": {"func_start": 13108, "func_end": 13204, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2444, "char_end": 2541, "patch_content": "", "patch_id": "google/mobly/mi385ggA", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/adb.py", "_extra": {"func_start": 2363, "func_end": 2541, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3107, "char_end": 3225, "patch_content": "", "patch_id": "google/mobly/cbUcT4yD", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/adb.py", "_extra": {"func_start": 3107, "func_end": 3225, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7759, "char_end": 7813, "patch_content": "", "patch_id": "google/mobly/9WT5ztZy", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/adb.py", "_extra": {"func_start": 7304, "func_end": 8376, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9279, "char_end": 9372, "patch_content": "", "patch_id": "google/mobly/m2GwuoR2", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/adb.py", "_extra": {"func_start": 9029, "func_end": 9705, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9778, "char_end": 9919, "patch_content": "", "patch_id": "google/mobly/pJU43wME", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/adb.py", "_extra": {"func_start": 9778, "func_end": 9935, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10227, "char_end": 10302, "patch_content": "", "patch_id": "google/mobly/sMcijqNq", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/adb.py", "_extra": {"func_start": 10056, "func_end": 10302, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11000, "char_end": 11089, "patch_content": "", "patch_id": "google/mobly/eDr5rTS4", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/adb.py", "_extra": {"func_start": 10570, "func_end": 11109, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12160, "char_end": 12288, "patch_content": "", "patch_id": "google/mobly/eHi88A9r", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/adb.py", "_extra": {"func_start": 12160, "func_end": 12620, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13126, "char_end": 13234, "patch_content": "", "patch_id": "google/mobly/LeZa8gYq", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/adb.py", "_extra": {"func_start": 13126, "func_end": 13235, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14206, "char_end": 14337, "patch_content": "", "patch_id": "google/mobly/VMhv32tw", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/adb.py", "_extra": {"func_start": 13652, "func_end": 14357, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14951, "char_end": 15017, "patch_content": "", "patch_id": "google/mobly/exxew6hZ", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/adb.py", "_extra": {"func_start": 14951, "func_end": 15186, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16843, "char_end": 16964, "patch_content": "", "patch_id": "google/mobly/JbmeST4c", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/adb.py", "_extra": {"func_start": 16266, "func_end": 17375, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 17941, "char_end": 18038, "patch_content": "", "patch_id": "google/mobly/CvhFhFU7", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/adb.py", "_extra": {"func_start": 17833, "func_end": 18605, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5788, "char_end": 5877, "patch_content": "", "patch_id": "google/mobly/ACtwTzfu", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 5510, "func_end": 5878, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6503, "char_end": 6605, "patch_content": "", "patch_id": "google/mobly/GXDGQKyA", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 6503, "func_end": 6606, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9067, "char_end": 9219, "patch_content": "", "patch_id": "google/mobly/XkUs9g6R", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 9067, "func_end": 9289, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10357, "char_end": 10448, "patch_content": "", "patch_id": "google/mobly/QXhs6T4Z", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 9786, "func_end": 10882, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11080, "char_end": 11175, "patch_content": "", "patch_id": "google/mobly/9uqr5QHs", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 10989, "func_end": 11175, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11488, "char_end": 11635, "patch_content": "", "patch_id": "google/mobly/fW9SRHCa", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 11278, "func_end": 11756, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12659, "char_end": 12695, "patch_content": "", "patch_id": "google/mobly/PMa6eNht", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 12550, "func_end": 12695, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14696, "char_end": 14763, "patch_content": "", "patch_id": "google/mobly/MbLtUb9v", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 14696, "func_end": 14798, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14911, "char_end": 15042, "patch_content": "", "patch_id": "google/mobly/7AYP4pQS", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 14884, "func_end": 15042, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15698, "char_end": 15886, "patch_content": "", "patch_id": "google/mobly/EATaF83o", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 15516, "func_end": 16350, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 17657, "char_end": 17722, "patch_content": "", "patch_id": "google/mobly/qAxKQc63", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 17245, "func_end": 17752, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 19892, "char_end": 20005, "patch_content": "", "patch_id": "google/mobly/DTs8rkUz", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 19839, "func_end": 20050, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20490, "char_end": 20573, "patch_content": "", "patch_id": "google/mobly/CxgXraiq", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 20455, "func_end": 20850, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 21200, "char_end": 21391, "patch_content": "", "patch_id": "google/mobly/r9WGYWFo", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 21200, "func_end": 21437, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 22614, "char_end": 22723, "patch_content": "", "patch_id": "google/mobly/ZV6N5wRh", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 22583, "func_end": 22766, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23360, "char_end": 23444, "patch_content": "", "patch_id": "google/mobly/kyhwNiFt", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 23295, "func_end": 23509, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23769, "char_end": 23868, "patch_content": "", "patch_id": "google/mobly/caKMgxu9", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 23712, "func_end": 23904, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 24094, "char_end": 24122, "patch_content": "", "patch_id": "google/mobly/M9vr5W78", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 24001, "func_end": 24122, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 26802, "char_end": 26972, "patch_content": "", "patch_id": "google/mobly/rW6wCv9U", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 26435, "func_end": 27115, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 27549, "char_end": 27655, "patch_content": "", "patch_id": "google/mobly/Q8bNi7Xt", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 27522, "func_end": 27655, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 28241, "char_end": 28273, "patch_content": "", "patch_id": "google/mobly/AK6y3QSq", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client_v2.py", "_extra": {"func_start": 28186, "func_end": 28297, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1162, "char_end": 1283, "patch_content": "", "patch_id": "google/mobly/evsjSeRh", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/fastboot.py", "_extra": {"func_start": 1023, "func_end": 1326, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1863, "char_end": 1928, "patch_content": "", "patch_id": "google/mobly/FvdLc6Tr", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/fastboot.py", "_extra": {"func_start": 1863, "func_end": 1928, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1729, "char_end": 1847, "patch_content": "", "patch_id": "google/mobly/NrDP6aL9", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/callback_handler_v2.py", "_extra": {"func_start": 1546, "func_end": 2080, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2518, "char_end": 2554, "patch_content": "", "patch_id": "google/mobly/cxCiq2PD", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/sl4a_client.py", "_extra": {"func_start": 2129, "func_end": 3120, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1210, "char_end": 1289, "patch_content": "", "patch_id": "google/mobly/qyPrmFmx", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/service_manager.py", "_extra": {"func_start": 1210, "func_end": 1290, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1567, "char_end": 1608, "patch_content": "", "patch_id": "google/mobly/mfkXivi2", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/service_manager.py", "_extra": {"func_start": 1567, "func_end": 1608, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1755, "char_end": 1801, "patch_content": "", "patch_id": "google/mobly/UwCoxr4f", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/service_manager.py", "_extra": {"func_start": 1704, "func_end": 1819, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2682, "char_end": 2791, "patch_content": "", "patch_id": "google/mobly/bHVLAwYi", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/service_manager.py", "_extra": {"func_start": 2352, "func_end": 2972, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3332, "char_end": 3411, "patch_content": "", "patch_id": "google/mobly/pdjbiRke", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/service_manager.py", "_extra": {"func_start": 3186, "func_end": 3561, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3848, "char_end": 4032, "patch_content": "", "patch_id": "google/mobly/FqP84W9A", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/service_manager.py", "_extra": {"func_start": 3773, "func_end": 4033, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4386, "char_end": 4453, "patch_content": "", "patch_id": "google/mobly/dBfw4wn9", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/service_manager.py", "_extra": {"func_start": 4305, "func_end": 4454, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5069, "char_end": 5162, "patch_content": "", "patch_id": "google/mobly/obFjNM4m", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/service_manager.py", "_extra": {"func_start": 4894, "func_end": 5188, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5332, "char_end": 5435, "patch_content": "", "patch_id": "google/mobly/UYdoU4NT", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/service_manager.py", "_extra": {"func_start": 5332, "func_end": 5436, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6192, "char_end": 6329, "patch_content": "", "patch_id": "google/mobly/X6REazym", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/service_manager.py", "_extra": {"func_start": 6069, "func_end": 6385, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6608, "char_end": 6743, "patch_content": "", "patch_id": "google/mobly/HXwtsoHp", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/service_manager.py", "_extra": {"func_start": 6532, "func_end": 6848, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7065, "char_end": 7251, "patch_content": "", "patch_id": "google/mobly/4aQgqLtH", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/service_manager.py", "_extra": {"func_start": 6989, "func_end": 7276, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7943, "char_end": 8037, "patch_content": "", "patch_id": "google/mobly/HoocQfP8", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/service_manager.py", "_extra": {"func_start": 7820, "func_end": 8105, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1129, "char_end": 1231, "patch_content": "", "patch_id": "google/mobly/HbFqu7Sj", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_event.py", "_extra": {"func_start": 1069, "func_end": 1279, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1797, "char_end": 1891, "patch_content": "", "patch_id": "google/mobly/DkECgB8R", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_event.py", "_extra": {"func_start": 1797, "func_end": 1913, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1936, "char_end": 2086, "patch_content": "", "patch_id": "google/mobly/fJcp4oR5", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_event.py", "_extra": {"func_start": 1936, "func_end": 2126, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3602, "char_end": 3669, "patch_content": "", "patch_id": "google/mobly/ZTe7Wgxi", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client.py", "_extra": {"func_start": 3556, "func_end": 3717, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4820, "char_end": 4871, "patch_content": "", "patch_id": "google/mobly/3corY4s6", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client.py", "_extra": {"func_start": 4739, "func_end": 4872, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5248, "char_end": 5346, "patch_content": "", "patch_id": "google/mobly/9DHPzeUq", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client.py", "_extra": {"func_start": 5097, "func_end": 5714, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7499, "char_end": 7650, "patch_content": "", "patch_id": "google/mobly/HiSS48Vb", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client.py", "_extra": {"func_start": 6160, "func_end": 7848, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8739, "char_end": 8817, "patch_content": "", "patch_id": "google/mobly/4kt4Z4Ee", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client.py", "_extra": {"func_start": 8421, "func_end": 9092, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10210, "char_end": 10314, "patch_content": "", "patch_id": "google/mobly/TnfEndf5", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client.py", "_extra": {"func_start": 10050, "func_end": 10314, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12307, "char_end": 12407, "patch_content": "", "patch_id": "google/mobly/Pv7EspkY", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client.py", "_extra": {"func_start": 11255, "func_end": 12440, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14420, "char_end": 14458, "patch_content": "", "patch_id": "google/mobly/TsPY8EoJ", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client.py", "_extra": {"func_start": 13982, "func_end": 14458, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14985, "char_end": 15062, "patch_content": "", "patch_id": "google/mobly/pqs5MrpK", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/snippet_client.py", "_extra": {"func_start": 14985, "func_end": 15096, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2344, "char_end": 2439, "patch_content": "", "patch_id": "google/mobly/7PxFDtMF", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/callback_handler.py", "_extra": {"func_start": 2344, "func_end": 2494, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4478, "char_end": 4585, "patch_content": "", "patch_id": "google/mobly/W3xfKdYu", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/callback_handler.py", "_extra": {"func_start": 4130, "func_end": 4722, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6361, "char_end": 6507, "patch_content": "", "patch_id": "google/mobly/5CkropD3", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/callback_handler.py", "_extra": {"func_start": 5718, "func_end": 6508, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3933, "char_end": 4023, "patch_content": "", "patch_id": "google/mobly/P9YFXm4G", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/event_dispatcher.py", "_extra": {"func_start": 3908, "func_end": 4157, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1026, "char_end": 1090, "patch_content": "", "patch_id": "google/mobly/rMKDwcaL", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/errors.py", "_extra": {"func_start": 851, "func_end": 1121, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1402, "char_end": 1517, "patch_content": "", "patch_id": "google/mobly/M8SvjnaB", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/errors.py", "_extra": {"func_start": 1402, "func_end": 1517, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1221, "char_end": 1327, "patch_content": "", "patch_id": "google/mobly/RPANX9jY", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/sl4a_service.py", "_extra": {"func_start": 1221, "func_end": 1327, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1403, "char_end": 1434, "patch_content": "", "patch_id": "google/mobly/PZZ34SwW", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/sl4a_service.py", "_extra": {"func_start": 1346, "func_end": 1434, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1622, "char_end": 1662, "patch_content": "", "patch_id": "google/mobly/Mc9N9wsg", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/sl4a_service.py", "_extra": {"func_start": 1576, "func_end": 1662, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1713, "char_end": 1760, "patch_content": "", "patch_id": "google/mobly/oN7wh9yE", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/sl4a_service.py", "_extra": {"func_start": 1713, "func_end": 1760, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1549, "char_end": 1573, "patch_content": "", "patch_id": "google/mobly/3oeHtXs4", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/base_service.py", "_extra": {"func_start": 1549, "func_end": 1573, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1513, "char_end": 1592, "patch_content": "", "patch_id": "google/mobly/rwNftnpp", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/snippet_management_service.py", "_extra": {"func_start": 1513, "func_end": 1592, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1813, "char_end": 1891, "patch_content": "", "patch_id": "google/mobly/XFnVQnwd", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/snippet_management_service.py", "_extra": {"func_start": 1813, "func_end": 1892, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3576, "char_end": 3687, "patch_content": "", "patch_id": "google/mobly/86Te7Qpv", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/snippet_management_service.py", "_extra": {"func_start": 3534, "func_end": 3706, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4819, "char_end": 4851, "patch_content": "", "patch_id": "google/mobly/gr3kMr68", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/snippet_management_service.py", "_extra": {"func_start": 4694, "func_end": 4851, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5319, "char_end": 5353, "patch_content": "", "patch_id": "google/mobly/WWe2W2bY", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/snippet_management_service.py", "_extra": {"func_start": 5276, "func_end": 5393, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1487, "char_end": 1626, "patch_content": "", "patch_id": "google/mobly/FJ5ZapgP", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/logcat.py", "_extra": {"func_start": 1487, "func_end": 1627, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1992, "char_end": 2094, "patch_content": "", "patch_id": "google/mobly/UWLrbd4o", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/logcat.py", "_extra": {"func_start": 1946, "func_end": 2278, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3131, "char_end": 3252, "patch_content": "", "patch_id": "google/mobly/CazZxP59", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/logcat.py", "_extra": {"func_start": 2375, "func_end": 3301, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4502, "char_end": 4592, "patch_content": "", "patch_id": "google/mobly/9psQuNis", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/logcat.py", "_extra": {"func_start": 4048, "func_end": 4760, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4795, "char_end": 4850, "patch_content": "", "patch_id": "google/mobly/T8xh2tRQ", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/logcat.py", "_extra": {"func_start": 4795, "func_end": 4850, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5052, "char_end": 5157, "patch_content": "", "patch_id": "google/mobly/tWni2Why", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/logcat.py", "_extra": {"func_start": 4915, "func_end": 5265, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8460, "char_end": 8557, "patch_content": "", "patch_id": "google/mobly/qEV7SEAg", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/logcat.py", "_extra": {"func_start": 7639, "func_end": 8597, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8656, "char_end": 8703, "patch_content": "", "patch_id": "google/mobly/htvMkYsJ", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/logcat.py", "_extra": {"func_start": 8656, "func_end": 8703, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8833, "char_end": 8917, "patch_content": "", "patch_id": "google/mobly/o96CKVgQ", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/controllers/android_device_lib/services/logcat.py", "_extra": {"func_start": 8774, "func_end": 9013, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 952, "char_end": 1056, "patch_content": "", "patch_id": "google/mobly/kx5UbAK6", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/snippet/callback_event.py", "_extra": {"func_start": 843, "func_end": 1057, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1493, "char_end": 1548, "patch_content": "", "patch_id": "google/mobly/9X54X7Z5", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/snippet/callback_event.py", "_extra": {"func_start": 1493, "func_end": 1609, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1645, "char_end": 1790, "patch_content": "", "patch_id": "google/mobly/9NRmy4cH", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/snippet/callback_event.py", "_extra": {"func_start": 1632, "func_end": 1791, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3214, "char_end": 3264, "patch_content": "", "patch_id": "google/mobly/RUVQd75e", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/snippet/client_base.py", "_extra": {"func_start": 3128, "func_end": 3329, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4789, "char_end": 4926, "patch_content": "", "patch_id": "google/mobly/A8NXSPrH", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/snippet/client_base.py", "_extra": {"func_start": 4192, "func_end": 5538, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6453, "char_end": 6519, "patch_content": "", "patch_id": "google/mobly/npd963XS", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/snippet/client_base.py", "_extra": {"func_start": 6453, "func_end": 6519, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9778, "char_end": 9882, "patch_content": "", "patch_id": "google/mobly/Tq73TZVk", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/snippet/client_base.py", "_extra": {"func_start": 9434, "func_end": 10451, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11468, "char_end": 11541, "patch_content": "", "patch_id": "google/mobly/kEGdAUjm", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/snippet/client_base.py", "_extra": {"func_start": 11386, "func_end": 11542, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14168, "char_end": 14247, "patch_content": "", "patch_id": "google/mobly/FaBkiY25", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/snippet/client_base.py", "_extra": {"func_start": 13963, "func_end": 14247, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2811, "char_end": 3030, "patch_content": "", "patch_id": "google/mobly/TtJ9NVv4", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/snippet/callback_handler_base.py", "_extra": {"func_start": 2525, "func_end": 3135, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7568, "char_end": 7680, "patch_content": "", "patch_id": "google/mobly/R784dwpq", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/snippet/callback_handler_base.py", "_extra": {"func_start": 7220, "func_end": 8073, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8383, "char_end": 8511, "patch_content": "", "patch_id": "google/mobly/ViqkC8fv", "repository": "google/mobly", "commit_sha": "cc0c7e1fd6cbbf6e32245303169abc4463398a94", "file_name": "mobly/snippet/callback_handler_base.py", "_extra": {"func_start": 8383, "func_end": 8511, "mode": "2-3-lines"}}

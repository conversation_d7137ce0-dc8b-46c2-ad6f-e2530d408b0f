
# Polycoder
Scripts to create a multi-lingual evaluation dataset from a subset of the PolyCoder dataset

The PolyCoder project:
Github: https://github.com/VHellendoorn/Code-LMs
paper: https://arxiv.org/pdf/2202.13169.pdf

Polycoder identified repos across multiple languages that had at least 25 stars
on github, so their dataset is of higher quality than taking random repos.
Furthermore, the project published the scripts they used to query and clone
repos. These scripts and dataset created the starting point for selecting and
archiving github repos for use in our own evaluation.

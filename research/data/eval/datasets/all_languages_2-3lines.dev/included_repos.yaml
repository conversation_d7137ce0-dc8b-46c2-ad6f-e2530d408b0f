# This is where we enforce the policy that containers used by hydra must be
# tagged (we don't use latest) and that configuration must be checked into
# git. To add a new repo/container or to upgrade one for a given dataset,
# please update this file.

# Dictionary of repositories and the required container:tag

jaegertracing/jaeger: v1.0
ethereum/go-ethereum: v1.0
moment/luxon: v1.0
mrdoob/three.js: v1.0
seata/seata: v1.0
caddyserver/caddy: v1.0
google/mobly: v1.0
spulec/freezegun: v1.0
pallets/flask: v1.0
pydantic/pydantic: v1.0
airbnb/epoxy: v1.0

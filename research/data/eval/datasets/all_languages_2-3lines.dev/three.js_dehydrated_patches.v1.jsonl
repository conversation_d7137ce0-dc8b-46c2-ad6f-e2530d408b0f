{"file_content": "", "char_start": 3831, "char_end": 3896, "patch_content": "", "patch_id": "mrdoob/three.js/HFs5TgyD", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/animation/PropertyBinding.js", "_extra": {"func_start": 3830, "func_end": 3896, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 514, "char_end": 529, "patch_content": "", "patch_id": "mrdoob/three.js/9ChkxPig", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Plane.js", "_extra": {"func_start": 565, "func_end": 634, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5232, "char_end": 5274, "patch_content": "", "patch_id": "mrdoob/three.js/c32Lj742", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix4.js", "_extra": {"func_start": 5231, "func_end": 5274, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 441, "char_end": 460, "patch_content": "", "patch_id": "mrdoob/three.js/jSfrUMav", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector3.js", "_extra": {"func_start": 402, "func_end": 477, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8491, "char_end": 8535, "patch_content": "", "patch_id": "mrdoob/three.js/BtWjZiFy", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Quaternion.js", "_extra": {"func_start": 8490, "func_end": 8784, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1603, "char_end": 1654, "patch_content": "", "patch_id": "mrdoob/three.js/DYdAHZLU", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Ray.js", "_extra": {"func_start": 1449, "func_end": 1791, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4099, "char_end": 4217, "patch_content": "", "patch_id": "mrdoob/three.js/ehxLGfpp", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/geometries/TorusKnotGeometry.js", "_extra": {"func_start": 4099, "func_end": 4218, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2599, "char_end": 2721, "patch_content": "", "patch_id": "mrdoob/three.js/sHd5h47a", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix3.js", "_extra": {"func_start": 2480, "func_end": 2723, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 749, "char_end": 790, "patch_content": "", "patch_id": "mrdoob/three.js/LPxW56es", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/core/Interpolations.js", "_extra": {"func_start": 749, "func_end": 792, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 507, "char_end": 522, "patch_content": "", "patch_id": "mrdoob/three.js/CCfshgQ8", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix3.js", "_extra": {"func_start": 4600, "func_end": 4671, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 17674, "char_end": 17679, "patch_content": "", "patch_id": "mrdoob/three.js/8ucLcoHW", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/PMREMGenerator.js", "_extra": {"func_start": 17352, "func_end": 19454, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2495, "char_end": 2514, "patch_content": "", "patch_id": "mrdoob/three.js/Hf7rSY8R", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Quaternion.js", "_extra": {"func_start": 2494, "func_end": 2514, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 680, "char_end": 870, "patch_content": "", "patch_id": "mrdoob/three.js/LA7TNQJX", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix4.js", "_extra": {"func_start": 587, "func_end": 888, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2556, "char_end": 2584, "patch_content": "", "patch_id": "mrdoob/three.js/T5MaRokg", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Quaternion.js", "_extra": {"func_start": 8960, "func_end": 9456, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8395, "char_end": 8475, "patch_content": "", "patch_id": "mrdoob/three.js/8dhBEFZd", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector4.js", "_extra": {"func_start": 8395, "func_end": 8477, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9989, "char_end": 10052, "patch_content": "", "patch_id": "mrdoob/three.js/YZU6oD5L", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector3.js", "_extra": {"func_start": 9960, "func_end": 10070, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 448, "char_end": 453, "patch_content": "", "patch_id": "mrdoob/three.js/Xfd5gwZX", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/SphericalHarmonics3.js", "_extra": {"func_start": 2946, "func_end": 3050, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 695, "char_end": 736, "patch_content": "", "patch_id": "mrdoob/three.js/LsQ3bk5i", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/geometries/IcosahedronGeometry.js", "_extra": {"func_start": 160, "func_end": 761, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 441, "char_end": 462, "patch_content": "", "patch_id": "mrdoob/three.js/PMbqwA5s", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/loaders/CompressedTextureLoader.js", "_extra": {"func_start": 441, "func_end": 463, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 788, "char_end": 792, "patch_content": "", "patch_id": "mrdoob/three.js/V3cvGKTk", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/animation/AnimationAction.js", "_extra": {"func_start": 11504, "func_end": 12077, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1476, "char_end": 1563, "patch_content": "", "patch_id": "mrdoob/three.js/cPuk2p5E", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/ColorManagement.js", "_extra": {"func_start": 1427, "func_end": 1563, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 522, "char_end": 539, "patch_content": "", "patch_id": "mrdoob/three.js/pdY2Yuit", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/curves/LineCurve.js", "_extra": {"func_start": 337, "func_end": 539, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3892, "char_end": 3958, "patch_content": "", "patch_id": "mrdoob/three.js/3f68XxJr", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Triangle.js", "_extra": {"func_start": 3891, "func_end": 3958, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2577, "char_end": 2674, "patch_content": "", "patch_id": "mrdoob/three.js/QD6jEgvC", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector3.js", "_extra": {"func_start": 2458, "func_end": 2692, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2685, "char_end": 2706, "patch_content": "", "patch_id": "mrdoob/three.js/npamKn9c", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/loaders/ObjectLoader.js", "_extra": {"func_start": 2685, "func_end": 2707, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 284, "char_end": 305, "patch_content": "", "patch_id": "mrdoob/three.js/qGHu82gR", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/loaders/FileLoader.js", "_extra": {"func_start": 283, "func_end": 305, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1356, "char_end": 1387, "patch_content": "", "patch_id": "mrdoob/three.js/dyqP5Jf4", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector4.js", "_extra": {"func_start": 1355, "func_end": 1465, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9097, "char_end": 9173, "patch_content": "", "patch_id": "mrdoob/three.js/XvsbBtsz", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Color.js", "_extra": {"func_start": 10395, "func_end": 10561, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2466, "char_end": 2574, "patch_content": "", "patch_id": "mrdoob/three.js/46UsUybv", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/geometries/TorusGeometry.js", "_extra": {"func_start": 2465, "func_end": 2574, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2143, "char_end": 2225, "patch_content": "", "patch_id": "mrdoob/three.js/gJrVdk6t", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Sphere.js", "_extra": {"func_start": 2022, "func_end": 2245, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13091, "char_end": 13154, "patch_content": "", "patch_id": "mrdoob/three.js/XDcTivn8", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Color.js", "_extra": {"func_start": 13062, "func_end": 13172, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 147, "char_end": 186, "patch_content": "", "patch_id": "mrdoob/three.js/5H96sKCC", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector3.js", "_extra": {"func_start": 147, "func_end": 230, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1247, "char_end": 1358, "patch_content": "", "patch_id": "mrdoob/three.js/LSHfUGnd", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Box2.js", "_extra": {"func_start": 1247, "func_end": 1359, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7530, "char_end": 7615, "patch_content": "", "patch_id": "mrdoob/three.js/cZwPjTre", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector3.js", "_extra": {"func_start": 7529, "func_end": 7617, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3928, "char_end": 3965, "patch_content": "", "patch_id": "mrdoob/three.js/DdN6FNXE", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix3.js", "_extra": {"func_start": 3880, "func_end": 4097, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4653, "char_end": 4723, "patch_content": "", "patch_id": "mrdoob/three.js/DnjE6hyE", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/core/Curve.js", "_extra": {"func_start": 4503, "func_end": 4883, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9495, "char_end": 9546, "patch_content": "", "patch_id": "mrdoob/three.js/6whPKKTh", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector3.js", "_extra": {"func_start": 9495, "func_end": 9547, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3809, "char_end": 3899, "patch_content": "", "patch_id": "mrdoob/three.js/p9e3Ntka", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/geometries/LatheGeometry.js", "_extra": {"func_start": 3809, "func_end": 3900, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 904, "char_end": 959, "patch_content": "", "patch_id": "mrdoob/three.js/eLfkoQj3", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Spherical.js", "_extra": {"func_start": 903, "func_end": 960, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2836, "char_end": 2887, "patch_content": "", "patch_id": "mrdoob/three.js/RyFqo7ea", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/geometries/TorusKnotGeometry.js", "_extra": {"func_start": 323, "func_end": 3943, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6528, "char_end": 6579, "patch_content": "", "patch_id": "mrdoob/three.js/mf3zyUSN", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector3.js", "_extra": {"func_start": 6528, "func_end": 6580, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 893, "char_end": 925, "patch_content": "", "patch_id": "mrdoob/three.js/HAkJcNT4", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Triangle.js", "_extra": {"func_start": 664, "func_end": 926, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3522, "char_end": 3561, "patch_content": "", "patch_id": "mrdoob/three.js/UyZJtMgi", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector2.js", "_extra": {"func_start": 3522, "func_end": 3562, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 185, "char_end": 206, "patch_content": "", "patch_id": "mrdoob/three.js/3p65G2vy", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/loaders/ImageLoader.js", "_extra": {"func_start": 184, "func_end": 206, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 806, "char_end": 863, "patch_content": "", "patch_id": "mrdoob/three.js/h8CRxsCB", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/animation/AnimationClip.js", "_extra": {"func_start": 761, "func_end": 1040, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4457, "char_end": 4476, "patch_content": "", "patch_id": "mrdoob/three.js/Xi248Kta", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/animation/KeyframeTrack.js", "_extra": {"func_start": 4386, "func_end": 5054, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 481, "char_end": 501, "patch_content": "", "patch_id": "mrdoob/three.js/oZoh3UYv", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/loaders/DataTextureLoader.js", "_extra": {"func_start": 480, "func_end": 502, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 366, "char_end": 459, "patch_content": "", "patch_id": "mrdoob/three.js/fZZ9Hktw", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix3.js", "_extra": {"func_start": 336, "func_end": 524, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 381, "char_end": 396, "patch_content": "", "patch_id": "mrdoob/three.js/schah8U5", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector2.js", "_extra": {"func_start": 1360, "func_end": 1420, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 877, "char_end": 892, "patch_content": "", "patch_id": "mrdoob/three.js/ZmEe82wh", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/lights/RectAreaLight.js", "_extra": {"func_start": 767, "func_end": 893, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 508, "char_end": 523, "patch_content": "", "patch_id": "mrdoob/three.js/a2qqoyiy", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix3.js", "_extra": {"func_start": 4504, "func_end": 4573, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6137, "char_end": 6164, "patch_content": "", "patch_id": "mrdoob/three.js/BAL7dZtm", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/textures/Texture.js", "_extra": {"func_start": 6137, "func_end": 6226, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 861, "char_end": 1025, "patch_content": "", "patch_id": "mrdoob/three.js/bhhMtBaU", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/helpers/HemisphereLightHelper.js", "_extra": {"func_start": 604, "func_end": 1281, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 17827, "char_end": 17908, "patch_content": "", "patch_id": "mrdoob/three.js/LZP4Buj8", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix4.js", "_extra": {"func_start": 17606, "func_end": 18423, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8046, "char_end": 8073, "patch_content": "", "patch_id": "mrdoob/three.js/tqFqQAeU", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/animation/AnimationObjectGroup.js", "_extra": {"func_start": 7530, "func_end": 8412, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3839, "char_end": 3937, "patch_content": "", "patch_id": "mrdoob/three.js/fa3kUBKq", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Color.js", "_extra": {"func_start": 3746, "func_end": 3939, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4471, "char_end": 4487, "patch_content": "", "patch_id": "mrdoob/three.js/NQ5f5R3r", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Color.js", "_extra": {"func_start": 11490, "func_end": 11553, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 174, "char_end": 208, "patch_content": "", "patch_id": "mrdoob/three.js/UHgvkcuM", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/lights/AmbientLightProbe.js", "_extra": {"func_start": 174, "func_end": 480, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 478, "char_end": 507, "patch_content": "", "patch_id": "mrdoob/three.js/d7qXatcU", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Euler.js", "_extra": {"func_start": 788, "func_end": 841, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1515, "char_end": 1559, "patch_content": "", "patch_id": "mrdoob/three.js/jfZdLC2G", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/core/Curve.js", "_extra": {"func_start": 1493, "func_end": 1635, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5597, "char_end": 5658, "patch_content": "", "patch_id": "mrdoob/three.js/7UL3QzE5", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix3.js", "_extra": {"func_start": 5567, "func_end": 5910, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 349, "char_end": 379, "patch_content": "", "patch_id": "mrdoob/three.js/47eXW2HT", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/geometries/ConeGeometry.js", "_extra": {"func_start": 250, "func_end": 597, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 263, "char_end": 283, "patch_content": "", "patch_id": "mrdoob/three.js/VsL33uKJ", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/loaders/CubeTextureLoader.js", "_extra": {"func_start": 263, "func_end": 285, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1283, "char_end": 1300, "patch_content": "", "patch_id": "mrdoob/three.js/SWDyj3kk", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector3.js", "_extra": {"func_start": 1248, "func_end": 1317, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2447, "char_end": 2477, "patch_content": "", "patch_id": "mrdoob/three.js/eFkcKHtn", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/curves/CatmullRomCurve3.js", "_extra": {"func_start": 2414, "func_end": 4446, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3801, "char_end": 3865, "patch_content": "", "patch_id": "mrdoob/three.js/LF9grDdt", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Triangle.js", "_extra": {"func_start": 3800, "func_end": 3865, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 792, "char_end": 859, "patch_content": "", "patch_id": "mrdoob/three.js/nfwaweF4", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Spherical.js", "_extra": {"func_start": 767, "func_end": 876, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1439, "char_end": 1489, "patch_content": "", "patch_id": "mrdoob/three.js/dcVJtJoG", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/objects/Mesh.js", "_extra": {"func_start": 1403, "func_end": 1549, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 513, "char_end": 529, "patch_content": "", "patch_id": "mrdoob/three.js/Q2uzFyPJ", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Plane.js", "_extra": {"func_start": 1086, "func_end": 1174, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 111, "char_end": 140, "patch_content": "", "patch_id": "mrdoob/three.js/LWYBozx7", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Quaternion.js", "_extra": {"func_start": 111, "func_end": 202, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7768, "char_end": 7843, "patch_content": "", "patch_id": "mrdoob/three.js/FoRnnETr", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Quaternion.js", "_extra": {"func_start": 7731, "func_end": 7884, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13886, "char_end": 13903, "patch_content": "", "patch_id": "mrdoob/three.js/D5JxvzwH", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix4.js", "_extra": {"func_start": 13818, "func_end": 13971, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 911, "char_end": 947, "patch_content": "", "patch_id": "mrdoob/three.js/XGjrK4eJ", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/core/Interpolations.js", "_extra": {"func_start": 911, "func_end": 948, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11362, "char_end": 11417, "patch_content": "", "patch_id": "mrdoob/three.js/qMLXewZU", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Quaternion.js", "_extra": {"func_start": 10983, "func_end": 11453, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6608, "char_end": 6661, "patch_content": "", "patch_id": "mrdoob/three.js/afQqYB5G", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector3.js", "_extra": {"func_start": 6607, "func_end": 6661, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3766, "char_end": 3880, "patch_content": "", "patch_id": "mrdoob/three.js/ggQ3Yftr", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Box3.js", "_extra": {"func_start": 3708, "func_end": 3882, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1769, "char_end": 1773, "patch_content": "", "patch_id": "mrdoob/three.js/MoMqCWXA", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/objects/Sprite.js", "_extra": {"func_start": 1152, "func_end": 1926, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 134, "char_end": 158, "patch_content": "", "patch_id": "mrdoob/three.js/rdW8S8b9", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/materials/RawShaderMaterial.js", "_extra": {"func_start": 134, "func_end": 231, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 202, "char_end": 223, "patch_content": "", "patch_id": "mrdoob/three.js/SHEkhsXG", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/loaders/TextureLoader.js", "_extra": {"func_start": 201, "func_end": 223, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1911, "char_end": 1956, "patch_content": "", "patch_id": "mrdoob/three.js/Nez9C9sn", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector2.js", "_extra": {"func_start": 1910, "func_end": 1956, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 350, "char_end": 378, "patch_content": "", "patch_id": "mrdoob/three.js/9PgdwrZT", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/cameras/CubeCamera.js", "_extra": {"func_start": 338, "func_end": 1205, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 354, "char_end": 397, "patch_content": "", "patch_id": "mrdoob/three.js/tgKoVepn", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/cameras/PerspectiveCamera.js", "_extra": {"func_start": 195, "func_end": 593, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 858, "char_end": 905, "patch_content": "", "patch_id": "mrdoob/three.js/4cKCvThH", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/curves/LineCurve3.js", "_extra": {"func_start": 857, "func_end": 906, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3216, "char_end": 3267, "patch_content": "", "patch_id": "mrdoob/three.js/SZJXfyDR", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/Object3D.js", "_extra": {"func_start": 3183, "func_end": 3269, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3629, "char_end": 3732, "patch_content": "", "patch_id": "mrdoob/three.js/kV6yseeP", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/core/CurvePath.js", "_extra": {"func_start": 3024, "func_end": 3788, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3051, "char_end": 3121, "patch_content": "", "patch_id": "mrdoob/three.js/kf57f8sJ", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Quaternion.js", "_extra": {"func_start": 3050, "func_end": 3121, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5447, "char_end": 5493, "patch_content": "", "patch_id": "mrdoob/three.js/Vxy5FEcx", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/Object3D.js", "_extra": {"func_start": 5102, "func_end": 5763, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6885, "char_end": 6911, "patch_content": "", "patch_id": "mrdoob/three.js/qxVsEdPq", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/Object3D.js", "_extra": {"func_start": 6826, "func_end": 6934, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 588, "char_end": 646, "patch_content": "", "patch_id": "mrdoob/three.js/Aqyqo3b6", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/curves/CubicBezierCurve3.js", "_extra": {"func_start": 490, "func_end": 759, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7809, "char_end": 7860, "patch_content": "", "patch_id": "mrdoob/three.js/sVzbikWr", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/Object3D.js", "_extra": {"func_start": 7809, "func_end": 7862, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8841, "char_end": 8858, "patch_content": "", "patch_id": "mrdoob/three.js/BY6wyRWm", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/Object3D.js", "_extra": {"func_start": 8739, "func_end": 8860, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5275, "char_end": 5306, "patch_content": "", "patch_id": "mrdoob/three.js/Da5GubZ3", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/Object3D.js", "_extra": {"func_start": 9706, "func_end": 9837, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6054, "char_end": 6076, "patch_content": "", "patch_id": "mrdoob/three.js/ik4c56Cj", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/animation/AnimationAction.js", "_extra": {"func_start": 6053, "func_end": 6076, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4191, "char_end": 4246, "patch_content": "", "patch_id": "mrdoob/three.js/FCgKpymj", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Color.js", "_extra": {"func_start": 4104, "func_end": 4488, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 187, "char_end": 201, "patch_content": "", "patch_id": "mrdoob/three.js/dTnkvdzP", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector3.js", "_extra": {"func_start": 494, "func_end": 526, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 332, "char_end": 376, "patch_content": "", "patch_id": "mrdoob/three.js/RhesLcuX", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Euler.js", "_extra": {"func_start": 307, "func_end": 401, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 249, "char_end": 294, "patch_content": "", "patch_id": "mrdoob/three.js/W8qdiKLU", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/InstancedInterleavedBuffer.js", "_extra": {"func_start": 177, "func_end": 295, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1246, "char_end": 1267, "patch_content": "", "patch_id": "mrdoob/three.js/LkWCFn6m", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/BufferGeometry.js", "_extra": {"func_start": 2162, "func_end": 2184, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3091, "char_end": 3120, "patch_content": "", "patch_id": "mrdoob/three.js/fvgiRH56", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/BufferGeometry.js", "_extra": {"func_start": 3161, "func_end": 3280, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3010, "char_end": 3054, "patch_content": "", "patch_id": "mrdoob/three.js/aeSLKH27", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix3.js", "_extra": {"func_start": 2739, "func_end": 3534, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 606, "char_end": 621, "patch_content": "", "patch_id": "mrdoob/three.js/ViFsRXTg", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Triangle.js", "_extra": {"func_start": 577, "func_end": 621, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 342, "char_end": 410, "patch_content": "", "patch_id": "mrdoob/three.js/5BzvXYop", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/InterleavedBuffer.js", "_extra": {"func_start": 162, "func_end": 453, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14965, "char_end": 15058, "patch_content": "", "patch_id": "mrdoob/three.js/VabWYtc4", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix4.js", "_extra": {"func_start": 14760, "func_end": 15563, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 498, "char_end": 538, "patch_content": "", "patch_id": "mrdoob/three.js/Eb2V8Efj", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Sphere.js", "_extra": {"func_start": 466, "func_end": 874, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 658, "char_end": 751, "patch_content": "", "patch_id": "mrdoob/three.js/MrXaFde5", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/BufferAttribute.js", "_extra": {"func_start": 458, "func_end": 915, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 265, "char_end": 306, "patch_content": "", "patch_id": "mrdoob/three.js/LodePSYt", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/curves/EllipseCurve.js", "_extra": {"func_start": 264, "func_end": 554, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5360, "char_end": 5388, "patch_content": "", "patch_id": "mrdoob/three.js/it6csUfX", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Color.js", "_extra": {"func_start": 5119, "func_end": 5583, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3731, "char_end": 3782, "patch_content": "", "patch_id": "mrdoob/three.js/CtJP2aLx", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/BufferAttribute.js", "_extra": {"func_start": 3730, "func_end": 3856, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5615, "char_end": 5688, "patch_content": "", "patch_id": "mrdoob/three.js/YhQkPnqw", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/BufferAttribute.js", "_extra": {"func_start": 5615, "func_end": 5690, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15527, "char_end": 15642, "patch_content": "", "patch_id": "mrdoob/three.js/esnUHChx", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/Earcut.js", "_extra": {"func_start": 15486, "func_end": 15643, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4136, "char_end": 4149, "patch_content": "", "patch_id": "mrdoob/three.js/o8MRa6CK", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/BufferAttribute.js", "_extra": {"func_start": 8169, "func_end": 8312, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8720, "char_end": 8783, "patch_content": "", "patch_id": "mrdoob/three.js/4WmHShBA", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/BufferAttribute.js", "_extra": {"func_start": 8662, "func_end": 8800, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 413, "char_end": 428, "patch_content": "", "patch_id": "mrdoob/three.js/6a4u2M6R", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/InstancedBufferAttribute.js", "_extra": {"func_start": 335, "func_end": 429, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5652, "char_end": 5827, "patch_content": "", "patch_id": "mrdoob/three.js/8g96JM28", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/geometries/CylinderGeometry.js", "_extra": {"func_start": 5652, "func_end": 5828, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5990, "char_end": 6012, "patch_content": "", "patch_id": "mrdoob/three.js/FroMeNPa", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Quaternion.js", "_extra": {"func_start": 5612, "func_end": 6840, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4744, "char_end": 4835, "patch_content": "", "patch_id": "mrdoob/three.js/ioj8mK4t", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Color.js", "_extra": {"func_start": 4640, "func_end": 4853, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3978, "char_end": 4027, "patch_content": "", "patch_id": "mrdoob/three.js/bwm9HjQn", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/MathUtils.js", "_extra": {"func_start": 3888, "func_end": 4029, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3687, "char_end": 3772, "patch_content": "", "patch_id": "mrdoob/three.js/byJixFPR", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Triangle.js", "_extra": {"func_start": 3687, "func_end": 3773, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2305, "char_end": 2356, "patch_content": "", "patch_id": "mrdoob/three.js/dahd4xM2", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector2.js", "_extra": {"func_start": 2268, "func_end": 2358, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11359, "char_end": 11424, "patch_content": "", "patch_id": "mrdoob/three.js/LM7o7Qj4", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/animation/AnimationMixer.js", "_extra": {"func_start": 11071, "func_end": 12486, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 278, "char_end": 297, "patch_content": "", "patch_id": "mrdoob/three.js/YWDF2cdZ", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Line3.js", "_extra": {"func_start": 255, "func_end": 297, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1223, "char_end": 1311, "patch_content": "", "patch_id": "mrdoob/three.js/WfG4maZZ", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/helpers/GridHelper.js", "_extra": {"func_start": 425, "func_end": 1373, "mode": "2-3-lines"}}

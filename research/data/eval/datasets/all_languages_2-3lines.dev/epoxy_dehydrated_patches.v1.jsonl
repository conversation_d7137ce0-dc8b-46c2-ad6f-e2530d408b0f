{"file_content": "", "char_start": 412, "char_end": 456, "patch_content": "    return R.layout.view_holder_empty_view;\n", "patch_id": "airbnb/epoxy/fxYT6bu3", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/HiddenEpoxyModel.java", "_extra": {"func_start": 412, "func_end": 456}}
{"file_content": "", "char_start": 415, "char_end": 483, "patch_content": "    super(async ? AYSNC_MAIN_THREAD_HANDLER : MAIN_THREAD_HANDLER);\n", "patch_id": "airbnb/epoxy/4rLejKSN", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/MainThreadExecutor.java", "_extra": {"func_start": 415, "func_end": 483}}
{"file_content": "", "char_start": 513, "char_end": 612, "patch_content": "    for (int i = 0; i < currentModels.size(); i++) {\n      EpoxyModel model = currentModels.get(i);", "patch_id": "airbnb/epoxy/ZfwczYjH", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ControllerHelper.java", "_extra": {"func_start": 429, "func_end": 742}}
{"file_content": "", "char_start": 824, "char_end": 868, "patch_content": "    model.controllerToStageTo = controller;\n", "patch_id": "airbnb/epoxy/mY854cKc", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ControllerHelper.java", "_extra": {"func_start": 824, "func_end": 868}}
{"file_content": "", "char_start": 1391, "char_end": 1463, "patch_content": "    }\n\n    final int adapterPosition = epoxyHolder.getAdapterPosition();", "patch_id": "airbnb/epoxy/WKyiMRr8", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/WrappedEpoxyModelCheckedChangeListener.java", "_extra": {"func_start": 884, "func_end": 1689}}
{"file_content": "", "char_start": 2124, "char_end": 2177, "patch_content": "    return originalCheckedChangeListener.hashCode();\n", "patch_id": "airbnb/epoxy/SaKj9Ztr", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/WrappedEpoxyModelCheckedChangeListener.java", "_extra": {"func_start": 2124, "func_end": 2177}}
{"file_content": "", "char_start": 843, "char_end": 966, "patch_content": "    this.adapter = adapter;\n    this.immutableModels = immutableModels;\n    adapter.registerAdapterDataObserver(observer);\n", "patch_id": "airbnb/epoxy/REheFn87", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffHelper.java", "_extra": {"func_start": 843, "func_end": 966}}
{"file_content": "", "char_start": 4263, "char_end": 4294, "patch_content": "    buildDiff(updateOpHelper);\n", "patch_id": "airbnb/epoxy/Qyu6s8Kg", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffHelper.java", "_extra": {"func_start": 4204, "func_end": 4571}}
{"file_content": "", "char_start": 4913, "char_end": 4959, "patch_content": "          break;\n        case UpdateOp.REMOVE:", "patch_id": "airbnb/epoxy/aoe8URJQ", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffHelper.java", "_extra": {"func_start": 4632, "func_end": 5488}}
{"file_content": "", "char_start": 7051, "char_end": 7084, "patch_content": "    currentStateList = tempList;\n", "patch_id": "airbnb/epoxy/3GbyLV9B", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffHelper.java", "_extra": {"func_start": 6690, "func_end": 7693}}
{"file_content": "", "char_start": 7859, "char_end": 8036, "patch_content": "    ModelState state = ModelState.build(model, position, immutableModels);\n\n    ModelState previousValue = currentStateMap.put(state.id, state);\n    if (previousValue != null) {", "patch_id": "airbnb/epoxy/JEqSHJY8", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffHelper.java", "_extra": {"func_start": 7758, "func_end": 8429}}
{"file_content": "", "char_start": 9227, "char_end": 9341, "patch_content": "      state.pair = currentStateMap.get(state.id);\n      if (state.pair != null) {\n        state.pair.pair = state;", "patch_id": "airbnb/epoxy/X3n25gwz", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffHelper.java", "_extra": {"func_start": 8773, "func_end": 9412}}
{"file_content": "", "char_start": 9834, "char_end": 9889, "patch_content": "\n    for (ModelState itemToInsert : currentStateList) {", "patch_id": "airbnb/epoxy/5x7Y2oQD", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffHelper.java", "_extra": {"func_start": 9766, "func_end": 10280}}
{"file_content": "", "char_start": 11093, "char_end": 11144, "patch_content": "                  previousItem.position);\n        }", "patch_id": "airbnb/epoxy/S68N7RxN", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffHelper.java", "_extra": {"func_start": 10429, "func_end": 11403}}
{"file_content": "", "char_start": 12789, "char_end": 12946, "patch_content": "      }\n\n      // We could iterate through only the new list and move each\n      // item that is out of place, however in cases such as moving the first item", "patch_id": "airbnb/epoxy/RUBgp5fL", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffHelper.java", "_extra": {"func_start": 11571, "func_end": 15377}}
{"file_content": "", "char_start": 16099, "char_end": 16138, "patch_content": "        item.position++;\n      }\n    }\n", "patch_id": "airbnb/epoxy/PuDAFSc6", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffHelper.java", "_extra": {"func_start": 15709, "func_end": 16167}}
{"file_content": "", "char_start": 16517, "char_end": 16588, "patch_content": "        // Skip this one and go on to the next\n        nextItem = null;", "patch_id": "airbnb/epoxy/BFxjYPbH", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffHelper.java", "_extra": {"func_start": 16362, "func_end": 16625}}
{"file_content": "", "char_start": 853, "char_end": 941, "patch_content": "\n    op.type = type;\n    op.positionStart = positionStart;\n    op.itemCount = itemCount;", "patch_id": "airbnb/epoxy/GijCUbaY", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOp.java", "_extra": {"func_start": 819, "func_end": 987}}
{"file_content": "", "char_start": 1087, "char_end": 1125, "patch_content": "    return positionStart + itemCount;\n", "patch_id": "airbnb/epoxy/tXDFAtRs", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOp.java", "_extra": {"func_start": 1087, "func_end": 1125}}
{"file_content": "", "char_start": 1164, "char_end": 1201, "patch_content": "    return position < positionStart;\n", "patch_id": "airbnb/epoxy/pGQ4uDsc", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOp.java", "_extra": {"func_start": 1164, "func_end": 1201}}
{"file_content": "", "char_start": 1319, "char_end": 1385, "patch_content": "    return position >= positionStart && position < positionEnd();\n", "patch_id": "airbnb/epoxy/RsB2DeXi", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOp.java", "_extra": {"func_start": 1319, "func_end": 1385}}
{"file_content": "", "char_start": 1886, "char_end": 1919, "patch_content": "    }\n\n    payloads.add(payload);", "patch_id": "airbnb/epoxy/qbFpHHag", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOp.java", "_extra": {"func_start": 1443, "func_end": 1920}}
{"file_content": "", "char_start": 2172, "char_end": 2249, "patch_content": "    setHasStableIds(true);\n    spanSizeLookup.setSpanIndexCacheEnabled(true);", "patch_id": "airbnb/epoxy/RZvpFBVR", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/BaseEpoxyAdapter.java", "_extra": {"func_start": 2030, "func_end": 2250}}
{"file_content": "", "char_start": 2996, "char_end": 3102, "patch_content": "    // so that the id stays constant when gone vs shown\n    return getCurrentModels().get(position).id();\n", "patch_id": "airbnb/epoxy/ea5oqrHs", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/BaseEpoxyAdapter.java", "_extra": {"func_start": 2883, "func_end": 3102}}
{"file_content": "", "char_start": 3164, "char_end": 3251, "patch_content": "    return viewTypeManager.getViewTypeAndRememberModel(getModelForPosition(position));\n", "patch_id": "airbnb/epoxy/jVbQqm4o", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/BaseEpoxyAdapter.java", "_extra": {"func_start": 3164, "func_end": 3251}}
{"file_content": "", "char_start": 3425, "char_end": 3541, "patch_content": "    View view = model.buildView(parent);\n    return new EpoxyViewHolder(parent, view, model.shouldSaveViewState());\n", "patch_id": "airbnb/epoxy/aEPuWop8", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/BaseEpoxyAdapter.java", "_extra": {"func_start": 3346, "func_end": 3541}}
{"file_content": "", "char_start": 3629, "char_end": 3694, "patch_content": "    onBindViewHolder(holder, position, Collections.emptyList());\n", "patch_id": "airbnb/epoxy/SswKXi2f", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/BaseEpoxyAdapter.java", "_extra": {"func_start": 3629, "func_end": 3694}}
{"file_content": "", "char_start": 4121, "char_end": 4237, "patch_content": "\n    if (payloads.isEmpty()) {\n      // We only apply saved state to the view on initial bind, not on model updates.", "patch_id": "airbnb/epoxy/qyk964Qi", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/BaseEpoxyAdapter.java", "_extra": {"func_start": 3805, "func_end": 4667}}
{"file_content": "", "char_start": 4706, "char_end": 4724, "patch_content": "    return false;\n", "patch_id": "airbnb/epoxy/J7qrnnsb", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/BaseEpoxyAdapter.java", "_extra": {"func_start": 4706, "func_end": 4724}}
{"file_content": "", "char_start": 5919, "char_end": 5964, "patch_content": "    return getCurrentModels().get(position);\n", "patch_id": "airbnb/epoxy/LpgWV9bh", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/BaseEpoxyAdapter.java", "_extra": {"func_start": 5919, "func_end": 5964}}
{"file_content": "", "char_start": 6107, "char_end": 6208, "patch_content": "\n    EpoxyModel<?> model = holder.getModel();\n    holder.unbind();\n    onModelUnbound(holder, model);", "patch_id": "airbnb/epoxy/9PqQdY78", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/BaseEpoxyAdapter.java", "_extra": {"func_start": 6036, "func_end": 6209}}
{"file_content": "", "char_start": 7016, "char_end": 7106, "patch_content": "    return ((EpoxyModel) holder.getModel()).onFailedToRecycleView(holder.objectToBind());\n", "patch_id": "airbnb/epoxy/c83cvLvH", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/BaseEpoxyAdapter.java", "_extra": {"func_start": 6978, "func_end": 7106}}
{"file_content": "", "char_start": 9340, "char_end": 9365, "patch_content": "        return i;\n      }", "patch_id": "airbnb/epoxy/6CAvzzP3", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/BaseEpoxyAdapter.java", "_extra": {"func_start": 9213, "func_end": 9388}}
{"file_content": "", "char_start": 4706, "char_end": 4724, "patch_content": "    return false;\n", "patch_id": "airbnb/epoxy/eepEuwGz", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/BaseEpoxyAdapter.java", "_extra": {"func_start": 11218, "func_end": 11236}}
{"file_content": "", "char_start": 889, "char_end": 1042, "patch_content": "    this.parent = parent;\n    if (saveInitialState) {\n      // We save the initial state of the view when it is created so that we can reset this initial", "patch_id": "airbnb/epoxy/NFFTJ4SM", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyViewHolder.java", "_extra": {"func_start": 871, "func_end": 1269}}
{"file_content": "", "char_start": 1872, "char_end": 2004, "patch_content": "    if (model instanceof GeneratedModel) {\n      // The generated method will enforce that only a properly typed listener can be set", "patch_id": "airbnb/epoxy/fAcoCTXQ", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyViewHolder.java", "_extra": {"func_start": 1556, "func_end": 2804}}
{"file_content": "", "char_start": 2846, "char_end": 2903, "patch_content": "    return epoxyHolder != null ? epoxyHolder : itemView;\n", "patch_id": "airbnb/epoxy/HpWNDRFr", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyViewHolder.java", "_extra": {"func_start": 2846, "func_end": 2903}}
{"file_content": "", "char_start": 2952, "char_end": 3021, "patch_content": "    // noinspection unchecked\n    epoxyModel.unbind(objectToBind());\n", "patch_id": "airbnb/epoxy/qBQ56kqV", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyViewHolder.java", "_extra": {"func_start": 2933, "func_end": 3066}}
{"file_content": "", "char_start": 3162, "char_end": 3266, "patch_content": "    // noinspection unchecked\n    epoxyModel.onVisibilityStateChanged(visibilityState, objectToBind());\n", "patch_id": "airbnb/epoxy/QsJYhTVg", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyViewHolder.java", "_extra": {"func_start": 3143, "func_end": 3266}}
{"file_content": "", "char_start": 3651, "char_end": 3690, "patch_content": "        visibleWidth, objectToBind());\n", "patch_id": "airbnb/epoxy/J2vhwvdR", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyViewHolder.java", "_extra": {"func_start": 3509, "func_end": 3690}}
{"file_content": "", "char_start": 3814, "char_end": 3856, "patch_content": "    assertBound();\n    return epoxyModel;\n", "patch_id": "airbnb/epoxy/6YDQeF6L", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyViewHolder.java", "_extra": {"func_start": 3814, "func_end": 3856}}
{"file_content": "", "char_start": 3896, "char_end": 3938, "patch_content": "    assertBound();\n    return epoxyHolder;", "patch_id": "airbnb/epoxy/PmSAGtuP", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyViewHolder.java", "_extra": {"func_start": 3896, "func_end": 3939}}
{"file_content": "", "char_start": 4202, "char_end": 4288, "patch_content": "        + \", view=\" + itemView\n        + \", super=\" + super.toString()\n        + '}';\n", "patch_id": "airbnb/epoxy/cPBhMsbP", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyViewHolder.java", "_extra": {"func_start": 4135, "func_end": 4288}}
{"file_content": "", "char_start": 1658, "char_end": 1762, "patch_content": "        percentVisibleHeight, percentVisibleWidth,\n        visibleHeight, visibleWidth,\n        holder);", "patch_id": "airbnb/epoxy/ocnx66u3", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelWithHolder.java", "_extra": {"func_start": 1627, "func_end": 1763}}
{"file_content": "", "char_start": 1174, "char_end": 1224, "patch_content": "    this(Collections.singletonList(changedItem));\n", "patch_id": "airbnb/epoxy/f8QC7uFm", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffPayload.java", "_extra": {"func_start": 1174, "func_end": 1224}}
{"file_content": "", "char_start": 1836, "char_end": 1974, "patch_content": "          return diffPayload.singleModel;\n        }\n      } else {\n        EpoxyModel<?> modelForId = diffPayload.modelsById.get(modelId);", "patch_id": "airbnb/epoxy/4Pxnvp3k", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffPayload.java", "_extra": {"func_start": 1586, "func_end": 2080}}
{"file_content": "", "char_start": 2745, "char_end": 2776, "patch_content": "      }\n    }\n\n    return true;", "patch_id": "airbnb/epoxy/eF6hEwcU", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffPayload.java", "_extra": {"func_start": 2153, "func_end": 2777}}
{"file_content": "", "char_start": 469, "char_end": 497, "patch_content": "    this.handler = handler;\n", "patch_id": "airbnb/epoxy/TWS8MSNr", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/HandlerExecutor.java", "_extra": {"func_start": 469, "func_end": 497}}
{"file_content": "", "char_start": 720, "char_end": 767, "patch_content": "    } else {\n      handler.post(command);\n    }", "patch_id": "airbnb/epoxy/gxwhQkak", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/HandlerExecutor.java", "_extra": {"func_start": 565, "func_end": 768}}
{"file_content": "", "char_start": 994, "char_end": 1030, "patch_content": "    this(model, \"\", modelPosition);\n", "patch_id": "airbnb/epoxy/GXaGAit4", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ImmutableModelException.java", "_extra": {"func_start": 994, "func_end": 1030}}
{"file_content": "", "char_start": 1146, "char_end": 1226, "patch_content": "    super(buildMessage(model, descriptionOfWhenChangeHappened, modelPosition));\n", "patch_id": "airbnb/epoxy/rhDEbHru", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ImmutableModelException.java", "_extra": {"func_start": 1146, "func_end": 1226}}
{"file_content": "", "char_start": 757, "char_end": 777, "patch_content": "    this.tag = tag;\n", "patch_id": "airbnb/epoxy/tRqrfTSL", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyDiffLogger.java", "_extra": {"func_start": 757, "func_end": 777}}
{"file_content": "", "char_start": 2637, "char_end": 2670, "patch_content": "\n    return new Handler(looper);\n", "patch_id": "airbnb/epoxy/XAozL8Dh", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAsyncUtil.java", "_extra": {"func_start": 1955, "func_end": 2670}}
{"file_content": "", "char_start": 1034, "char_end": 1111, "patch_content": "        ITEM_CALLBACK\n    );\n    registerAdapterDataObserver(notifyBlocker);\n", "patch_id": "airbnb/epoxy/3L34nVwX", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyControllerAdapter.java", "_extra": {"func_start": 917, "func_end": 1111}}
{"file_content": "", "char_start": 1339, "char_end": 1375, "patch_content": "    return differ.getCurrentList();\n", "patch_id": "airbnb/epoxy/H6FFpjTD", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyControllerAdapter.java", "_extra": {"func_start": 1339, "func_end": 1375}}
{"file_content": "", "char_start": 3077, "char_end": 3115, "patch_content": "    return differ.isDiffInProgress();\n", "patch_id": "airbnb/epoxy/36ePU4Gb", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyControllerAdapter.java", "_extra": {"func_start": 3077, "func_end": 3115}}
{"file_content": "", "char_start": 3270, "char_end": 3367, "patch_content": "    notifyBlocker.allowChanges();\n    result.dispatchTo(this);\n    notifyBlocker.blockChanges();\n", "patch_id": "airbnb/epoxy/75jYYxjj", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyControllerAdapter.java", "_extra": {"func_start": 3229, "func_end": 3501}}
{"file_content": "", "char_start": 3583, "char_end": 3622, "patch_content": "    modelBuildListeners.add(listener);\n", "patch_id": "airbnb/epoxy/6mphg4zW", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyControllerAdapter.java", "_extra": {"func_start": 3583, "func_end": 3622}}
{"file_content": "", "char_start": 3707, "char_end": 3749, "patch_content": "    modelBuildListeners.remove(listener);\n", "patch_id": "airbnb/epoxy/bUqgcNJt", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyControllerAdapter.java", "_extra": {"func_start": 3707, "func_end": 3749}}
{"file_content": "", "char_start": 3800, "char_end": 3817, "patch_content": "    return true;\n", "patch_id": "airbnb/epoxy/ncgaV3QC", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyControllerAdapter.java", "_extra": {"func_start": 3800, "func_end": 3817}}
{"file_content": "", "char_start": 5222, "char_end": 5303, "patch_content": "    //noinspection unchecked\n    return (List<EpoxyModel<?>>) getCurrentModels();", "patch_id": "airbnb/epoxy/AHZZgfnD", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyControllerAdapter.java", "_extra": {"func_start": 5222, "func_end": 5304}}
{"file_content": "", "char_start": 6157, "char_end": 6232, "patch_content": "      if (model.id() == targetModel.id()) {\n        return i;\n      }\n    }", "patch_id": "airbnb/epoxy/kFmvhWCZ", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyControllerAdapter.java", "_extra": {"func_start": 6023, "func_end": 6249}}
{"file_content": "", "char_start": 6619, "char_end": 6699, "patch_content": "    notifyItemMoved(fromPosition, toPosition);\n    notifyBlocker.blockChanges();", "patch_id": "airbnb/epoxy/RdLSM9N2", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyControllerAdapter.java", "_extra": {"func_start": 6437, "func_end": 6994}}
{"file_content": "", "char_start": 886, "char_end": 913, "patch_content": "    VIEW_TYPE_MAP.clear();\n", "patch_id": "airbnb/epoxy/k2JuSkrU", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ViewTypeManager.java", "_extra": {"func_start": 886, "func_end": 913}}
{"file_content": "", "char_start": 975, "char_end": 1045, "patch_content": "    lastModelForViewTypeLookup = model;\n    return getViewType(model);", "patch_id": "airbnb/epoxy/8CeqU3bh", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ViewTypeManager.java", "_extra": {"func_start": 975, "func_end": 1046}}
{"file_content": "", "char_start": 1327, "char_end": 1423, "patch_content": "    Class modelClass = model.getClass();\n\n    Integer viewType = VIEW_TYPE_MAP.get(modelClass);\n", "patch_id": "airbnb/epoxy/3eJH53vw", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ViewTypeManager.java", "_extra": {"func_start": 1099, "func_end": 1571}}
{"file_content": "", "char_start": 1208, "char_end": 1214, "patch_content": "    }\n", "patch_id": "airbnb/epoxy/Dgv22ynZ", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ViewTypeManager.java", "_extra": {"func_start": 2635, "func_end": 3462}}
{"file_content": "", "char_start": 2396, "char_end": 2408, "patch_content": "    id(id);\n", "patch_id": "airbnb/epoxy/8MwyRejL", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 2396, "func_end": 2408}}
{"file_content": "", "char_start": 2437, "char_end": 2484, "patch_content": "    this(idCounter--);\n    hasDefaultId = true;", "patch_id": "airbnb/epoxy/XTYrzjpc", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 2437, "func_end": 2485}}
{"file_content": "", "char_start": 2517, "char_end": 2542, "patch_content": "    return hasDefaultId;\n", "patch_id": "airbnb/epoxy/6XSkDEN7", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 2517, "func_end": 2542}}
{"file_content": "", "char_start": 2953, "char_end": 2977, "patch_content": "    return getLayout();\n", "patch_id": "airbnb/epoxy/GGY4sUD2", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 2953, "func_end": 2977}}
{"file_content": "", "char_start": 3179, "char_end": 3268, "patch_content": "    return LayoutInflater.from(parent.getContext()).inflate(getLayout(), parent, false);\n", "patch_id": "airbnb/epoxy/BRjjnBaL", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 3179, "func_end": 3268}}
{"file_content": "", "char_start": 6655, "char_end": 6671, "patch_content": "    bind(view);\n", "patch_id": "airbnb/epoxy/45YYr5h8", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 6655, "func_end": 6671}}
{"file_content": "", "char_start": 6655, "char_end": 6671, "patch_content": "    bind(view);\n", "patch_id": "airbnb/epoxy/eYcMhDBZ", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 9098, "func_end": 9114}}
{"file_content": "", "char_start": 10244, "char_end": 10259, "patch_content": "    return id;\n", "patch_id": "airbnb/epoxy/iAqsARLC", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 10244, "func_end": 10259}}
{"file_content": "", "char_start": 10742, "char_end": 10792, "patch_content": "    }\n\n    hasDefaultId = false;\n    this.id = id;", "patch_id": "airbnb/epoxy/BjUZuqNn", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 10547, "func_end": 10810}}
{"file_content": "", "char_start": 11120, "char_end": 11260, "patch_content": "    if (ids != null) {\n      for (@Nullable Number id : ids) {\n        result = 31 * result + hashLong64Bit(id == null ? 0 : id.hashCode());", "patch_id": "airbnb/epoxy/BEySoQAy", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 11099, "func_end": 11298}}
{"file_content": "", "char_start": 12474, "char_end": 12520, "patch_content": "    id(hashString64Bit(key));\n    return this;", "patch_id": "airbnb/epoxy/nBdCKr3F", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 12474, "func_end": 12521}}
{"file_content": "", "char_start": 10793, "char_end": 10810, "patch_content": "    return this;\n", "patch_id": "airbnb/epoxy/D5bBYpbW", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 13698, "func_end": 13817}}
{"file_content": "", "char_start": 14443, "char_end": 14484, "patch_content": "    onMutation();\n    layout = layoutRes;", "patch_id": "airbnb/epoxy/Hd9TBmhe", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 14443, "func_end": 14502}}
{"file_content": "", "char_start": 14576, "char_end": 14614, "patch_content": "      return getDefaultLayout();\n    }", "patch_id": "airbnb/epoxy/9BUkYUp2", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 14553, "func_end": 14635}}
{"file_content": "", "char_start": 10793, "char_end": 10810, "patch_content": "    return this;\n", "patch_id": "airbnb/epoxy/qXd69Pei", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 14743, "func_end": 14814}}
{"file_content": "", "char_start": 15010, "char_end": 15044, "patch_content": "    controller.addInternal(this);\n", "patch_id": "airbnb/epoxy/aJgaE6ZU", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 15010, "func_end": 15044}}
{"file_content": "", "char_start": 15565, "char_end": 15653, "patch_content": "      // previously staged model.\n      controllerToStageTo.clearModelFromStaging(this);", "patch_id": "airbnb/epoxy/pJvCFPTp", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 15284, "func_end": 15694}}
{"file_content": "", "char_start": 15963, "char_end": 16005, "patch_content": "    addIf(predicate.addIf(), controller);\n", "patch_id": "airbnb/epoxy/JhAwki3B", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 15963, "func_end": 16005}}
{"file_content": "", "char_start": 16797, "char_end": 16939, "patch_content": "      firstControllerAddedTo = controller;\n\n      // We save the current hashCode so we can compare it to the hashCode at later points in time", "patch_id": "airbnb/epoxy/U49JWPk9", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 16409, "func_end": 17852}}
{"file_content": "", "char_start": 17896, "char_end": 17939, "patch_content": "    return firstControllerAddedTo != null;\n", "patch_id": "airbnb/epoxy/avYBmDyT", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 17896, "func_end": 17939}}
{"file_content": "", "char_start": 18928, "char_end": 19021, "patch_content": "\n    if (controllerToStageTo != null) {\n      controllerToStageTo.setStagedModel(this);\n    }", "patch_id": "airbnb/epoxy/3AYR2htS", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 18515, "func_end": 19022}}
{"file_content": "", "char_start": 20335, "char_end": 20470, "patch_content": "        && hashCodeWhenAdded != hashCode()) {\n      throw new ImmutableModelException(this, descriptionOfChange, modelPosition);\n    }\n", "patch_id": "airbnb/epoxy/JeXbuQJY", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 20264, "func_end": 20470}}
{"file_content": "", "char_start": 20627, "char_end": 20677, "patch_content": "    }\n\n    EpoxyModel<?> that = (EpoxyModel<?>) o;", "patch_id": "airbnb/epoxy/cuogPLbU", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 20523, "func_end": 20835}}
{"file_content": "", "char_start": 20963, "char_end": 21025, "patch_content": "    result = 31 * result + (shown ? 1 : 0);\n    return result;", "patch_id": "airbnb/epoxy/UkFB6b4q", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 20878, "func_end": 21026}}
{"file_content": "", "char_start": 21529, "char_end": 21592, "patch_content": "    this.spanSizeOverride = spanSizeCallback;\n    return this;\n", "patch_id": "airbnb/epoxy/MxiMojht", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 21529, "func_end": 21592}}
{"file_content": "", "char_start": 22105, "char_end": 22172, "patch_content": "    }\n\n    return getSpanSize(totalSpanCount, position, itemCount);", "patch_id": "airbnb/epoxy/rBCbbAPN", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 21989, "func_end": 22173}}
{"file_content": "", "char_start": 22742, "char_end": 22795, "patch_content": "    onMutation();\n    shown = show;\n    return this;\n", "patch_id": "airbnb/epoxy/BXYoeUhv", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 22742, "func_end": 22795}}
{"file_content": "", "char_start": 23073, "char_end": 23097, "patch_content": "    return show(false);\n", "patch_id": "airbnb/epoxy/4wPLBbFf", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 23073, "func_end": 23097}}
{"file_content": "", "char_start": 23304, "char_end": 23322, "patch_content": "    return shown;\n", "patch_id": "airbnb/epoxy/9mHBMLMp", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModel.java", "_extra": {"func_start": 23304, "func_end": 23322}}
{"file_content": "", "char_start": 491, "char_end": 518, "patch_content": "    changesAllowed = true;\n", "patch_id": "airbnb/epoxy/fiypEzRK", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/NotifyBlocker.java", "_extra": {"func_start": 491, "func_end": 518}}
{"file_content": "", "char_start": 686, "char_end": 780, "patch_content": "          \"You cannot notify item changes directly. Call `requestModelBuild` instead.\");\n    }", "patch_id": "airbnb/epoxy/GkDbNZ6X", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/NotifyBlocker.java", "_extra": {"func_start": 620, "func_end": 781}}
{"file_content": "", "char_start": 1291, "char_end": 1330, "patch_content": "    }\n    notificationsPaused = false;\n", "patch_id": "airbnb/epoxy/p4xcRznr", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 1187, "func_end": 1330}}
{"file_content": "", "char_start": 1384, "char_end": 1414, "patch_content": "    this.observer = observer;\n", "patch_id": "airbnb/epoxy/cB48tqbx", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 1384, "func_end": 1414}}
{"file_content": "", "char_start": 1486, "char_end": 1606, "patch_content": "    if (!notificationsPaused && observer != null) {\n      observer.onItemRangeInserted(positionStart, itemCount);\n    }\n", "patch_id": "airbnb/epoxy/i9APHZux", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 1486, "func_end": 1606}}
{"file_content": "", "char_start": 1728, "char_end": 1795, "patch_content": "      observer.onItemRangeRemoved(positionStart, itemCount);\n    }\n", "patch_id": "airbnb/epoxy/dhdAxduK", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 1676, "func_end": 1795}}
{"file_content": "", "char_start": 2054, "char_end": 2080, "patch_content": "    return previousModel;\n", "patch_id": "airbnb/epoxy/6dKveSUv", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 1875, "func_end": 2080}}
{"file_content": "", "char_start": 2146, "char_end": 2212, "patch_content": "    notifyInsertion(size(), 1);\n    return super.add(epoxyModel);\n", "patch_id": "airbnb/epoxy/eAZwY9Yn", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 2146, "func_end": 2212}}
{"file_content": "", "char_start": 2314, "char_end": 2345, "patch_content": "    super.add(index, element);\n", "patch_id": "airbnb/epoxy/KfgUF6kJ", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 2283, "func_end": 2345}}
{"file_content": "", "char_start": 2466, "char_end": 2494, "patch_content": "    return super.addAll(c);\n", "patch_id": "airbnb/epoxy/FogWnEwb", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 2427, "func_end": 2494}}
{"file_content": "", "char_start": 2587, "char_end": 2659, "patch_content": "    notifyInsertion(index, c.size());\n    return super.addAll(index, c);", "patch_id": "airbnb/epoxy/D3CcSdXP", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 2587, "func_end": 2660}}
{"file_content": "", "char_start": 2749, "char_end": 2781, "patch_content": "    return super.remove(index);\n", "patch_id": "airbnb/epoxy/rBMiYpw9", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 2720, "func_end": 2781}}
{"file_content": "", "char_start": 2912, "char_end": 2966, "patch_content": "\n    notifyRemoval(index, 1);\n    super.remove(index);", "patch_id": "airbnb/epoxy/nRijy9GQ", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 2834, "func_end": 2984}}
{"file_content": "", "char_start": 3047, "char_end": 3106, "patch_content": "      notifyRemoval(0, size());\n      super.clear();\n    }\n", "patch_id": "airbnb/epoxy/RwNGvdqT", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 3025, "func_end": 3106}}
{"file_content": "", "char_start": 3228, "char_end": 3328, "patch_content": "    }\n\n    notifyRemoval(fromIndex, toIndex - fromIndex);\n    super.removeRange(fromIndex, toIndex);", "patch_id": "airbnb/epoxy/4TdSyJkN", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 3182, "func_end": 3329}}
{"file_content": "", "char_start": 3607, "char_end": 3694, "patch_content": "    boolean result = false;\n    Iterator<?> it = iterator();\n    while (it.hasNext()) {", "patch_id": "airbnb/epoxy/ZNsQpYk2", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 3401, "func_end": 3816}}
{"file_content": "", "char_start": 3490, "char_end": 3667, "patch_content": "    // doesn't call through to remove. Calling through to remove lets us leverage the notification\n    // done there\n    boolean result = false;\n    Iterator<?> it = iterator();", "patch_id": "airbnb/epoxy/8sVevb9h", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 3888, "func_end": 4304}}
{"file_content": "", "char_start": 4378, "char_end": 4400, "patch_content": "    return new Itr();\n", "patch_id": "airbnb/epoxy/XFKJxdHQ", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 4378, "func_end": 4400}}
{"file_content": "", "char_start": 5036, "char_end": 5067, "patch_content": "      return cursor != size();\n", "patch_id": "airbnb/epoxy/aehS8y9c", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 5036, "func_end": 5067}}
{"file_content": "", "char_start": 5143, "char_end": 5237, "patch_content": "      checkForComodification();\n      int i = cursor;\n      cursor = i + 1;\n      lastRet = i;", "patch_id": "airbnb/epoxy/LqpTKHLk", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 5143, "func_end": 5274}}
{"file_content": "", "char_start": 5429, "char_end": 5553, "patch_content": "        ModelList.this.remove(lastRet);\n        cursor = lastRet;\n        lastRet = -1;\n        expectedModCount = modCount;", "patch_id": "airbnb/epoxy/mEVpyHXp", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 5308, "func_end": 5662}}
{"file_content": "", "char_start": 5902, "char_end": 5929, "patch_content": "    return new ListItr(0);\n", "patch_id": "airbnb/epoxy/5KjXAhtA", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 5902, "func_end": 5929}}
{"file_content": "", "char_start": 6020, "char_end": 6051, "patch_content": "    return new ListItr(index);\n", "patch_id": "airbnb/epoxy/SMyBRMyp", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 6020, "func_end": 6051}}
{"file_content": "", "char_start": 6489, "char_end": 6511, "patch_content": "      cursor = index;\n", "patch_id": "airbnb/epoxy/RJyYkRy5", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 6489, "func_end": 6511}}
{"file_content": "", "char_start": 6615, "char_end": 6636, "patch_content": "      return cursor;\n", "patch_id": "airbnb/epoxy/MRTgxPeu", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 6615, "func_end": 6636}}
{"file_content": "", "char_start": 6676, "char_end": 6701, "patch_content": "      return cursor - 1;\n", "patch_id": "airbnb/epoxy/nDxbZHbD", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 6676, "func_end": 6701}}
{"file_content": "", "char_start": 5517, "char_end": 5653, "patch_content": "        expectedModCount = modCount;\n      } catch (IndexOutOfBoundsException ex) {\n        throw new ConcurrentModificationException();", "patch_id": "airbnb/epoxy/LoACheLn", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 7345, "func_end": 7639}}
{"file_content": "", "char_start": 7895, "char_end": 7944, "patch_content": "    }\n    throw new IndexOutOfBoundsException();\n", "patch_id": "airbnb/epoxy/chUMZzZf", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 7732, "func_end": 7944}}
{"file_content": "", "char_start": 8906, "char_end": 8936, "patch_content": "        end = start + length;\n", "patch_id": "airbnb/epoxy/mWSkqtJ6", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 8835, "func_end": 8936}}
{"file_content": "", "char_start": 9113, "char_end": 9156, "patch_content": "        return iterator.nextIndex() < end;\n", "patch_id": "airbnb/epoxy/sDCuxqXh", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 9113, "func_end": 9156}}
{"file_content": "", "char_start": 9339, "char_end": 9382, "patch_content": "          return iterator.next();\n        }", "patch_id": "airbnb/epoxy/ck7ptnzy", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 9297, "func_end": 9427}}
{"file_content": "", "char_start": 9863, "char_end": 9892, "patch_content": "        }\n        return -1;\n", "patch_id": "airbnb/epoxy/VoKRX6f9", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 9746, "func_end": 9892}}
{"file_content": "", "char_start": 10158, "char_end": 10264, "patch_content": "      fullList = list;\n      modCount = fullList.modCount;\n      offset = start;\n      size = end - start;", "patch_id": "airbnb/epoxy/SMjAVHq4", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 10158, "func_end": 10265}}
{"file_content": "", "char_start": 11174, "char_end": 11232, "patch_content": "      }\n      throw new ConcurrentModificationException();", "patch_id": "airbnb/epoxy/mFbemacg", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 11708, "func_end": 11965}}
{"file_content": "", "char_start": 12047, "char_end": 12077, "patch_content": "      return listIterator(0);\n", "patch_id": "airbnb/epoxy/BbJdcHRH", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 12047, "func_end": 12077}}
{"file_content": "", "char_start": 11117, "char_end": 11232, "patch_content": "        }\n        throw new IndexOutOfBoundsException();\n      }\n      throw new ConcurrentModificationException();", "patch_id": "airbnb/epoxy/GAczURMv", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 12179, "func_end": 12487}}
{"file_content": "", "char_start": 13193, "char_end": 13282, "patch_content": "        } else {\n          throw new ConcurrentModificationException();\n        }\n      }", "patch_id": "airbnb/epoxy/a96GrJ9w", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 12989, "func_end": 13283}}
{"file_content": "", "char_start": 11117, "char_end": 11181, "patch_content": "        }\n        throw new IndexOutOfBoundsException();\n      }", "patch_id": "airbnb/epoxy/mcWgysXQ", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 13371, "func_end": 13636}}
{"file_content": "", "char_start": 13724, "char_end": 13804, "patch_content": "        return size;\n      }\n      throw new ConcurrentModificationException();\n", "patch_id": "airbnb/epoxy/LFrjwVcm", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelList.java", "_extra": {"func_start": 13681, "func_end": 13804}}
{"file_content": "", "char_start": 1056, "char_end": 1113, "patch_content": "    super(expectedModelCount);\n    pauseNotifications();\n", "patch_id": "airbnb/epoxy/a3fNgTZr", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ControllerModelList.java", "_extra": {"func_start": 1056, "func_end": 1113}}
{"file_content": "", "char_start": 1136, "char_end": 1190, "patch_content": "    setObserver(OBSERVER);\n    resumeNotifications();\n", "patch_id": "airbnb/epoxy/BhsUJgoM", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ControllerModelList.java", "_extra": {"func_start": 1136, "func_end": 1190}}
{"file_content": "", "char_start": 1134, "char_end": 1214, "patch_content": "    this.resultCallback = resultCallback;\n    this.diffCallback = diffCallback;\n", "patch_id": "airbnb/epoxy/8h7WyxfK", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 1084, "func_end": 1214}}
{"file_content": "", "char_start": 2044, "char_end": 2069, "patch_content": "    return readOnlyList;\n", "patch_id": "airbnb/epoxy/PTXvNyt3", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 2044, "func_end": 2069}}
{"file_content": "", "char_start": 2299, "char_end": 2351, "patch_content": "    return generationTracker.finishMaxGeneration();\n", "patch_id": "airbnb/epoxy/hswmJDsz", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 2299, "func_end": 2351}}
{"file_content": "", "char_start": 2509, "char_end": 2565, "patch_content": "    return generationTracker.hasUnfinishedGeneration();\n", "patch_id": "airbnb/epoxy/jFBdwP7b", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 2509, "func_end": 2565}}
{"file_content": "", "char_start": 3026, "char_end": 3163, "patch_content": "    int generation = generationTracker.incrementAndGetNextScheduled();\n    tryLatchList(newList, generation);\n    return interruptedDiff;", "patch_id": "airbnb/epoxy/BX8zcKgU", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 2890, "func_end": 3164}}
{"file_content": "", "char_start": 4913, "char_end": 5070, "patch_content": "    executor.execute(new Runnable() {\n      @Override\n      public void run() {\n        DiffUtil.DiffResult result = DiffUtil.calculateDiff(wrappedCallback);", "patch_id": "airbnb/epoxy/ZHgpQk6t", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 3763, "func_end": 5183}}
{"file_content": "", "char_start": 5707, "char_end": 5797, "patch_content": "        if (result != null && dispatchResult) {\n          resultCallback.onResult(result);", "patch_id": "airbnb/epoxy/cWrzhPaV", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 5357, "func_end": 5824}}
{"file_content": "", "char_start": 6235, "char_end": 6317, "patch_content": "    if (generationTracker.finishGeneration(runGeneration)) {\n      list = newList;", "patch_id": "airbnb/epoxy/FrUHpiQL", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 6235, "func_end": 6526}}
{"file_content": "", "char_start": 7435, "char_end": 7474, "patch_content": "      return ++maxScheduledGeneration;\n", "patch_id": "airbnb/epoxy/iM5iNPNr", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 7435, "func_end": 7474}}
{"file_content": "", "char_start": 7530, "char_end": 7671, "patch_content": "      boolean isInterrupting = hasUnfinishedGeneration();\n      maxFinishedGeneration = maxScheduledGeneration;\n      return isInterrupting;\n", "patch_id": "airbnb/epoxy/Lc3DGNFH", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 7530, "func_end": 7671}}
{"file_content": "", "char_start": 7731, "char_end": 7792, "patch_content": "      return maxScheduledGeneration > maxFinishedGeneration;\n", "patch_id": "airbnb/epoxy/sD2PYBmD", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 7731, "func_end": 7792}}
{"file_content": "", "char_start": 8523, "char_end": 8592, "patch_content": "      this.newList = newList;\n      this.diffCallback = diffCallback;", "patch_id": "airbnb/epoxy/AUVCTyGo", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 8493, "func_end": 8593}}
{"file_content": "", "char_start": 8648, "char_end": 8677, "patch_content": "      return oldList.size();\n", "patch_id": "airbnb/epoxy/qcqfKfne", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 8648, "func_end": 8677}}
{"file_content": "", "char_start": 8732, "char_end": 8761, "patch_content": "      return newList.size();\n", "patch_id": "airbnb/epoxy/tuBidyY5", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 8732, "func_end": 8761}}
{"file_content": "", "char_start": 8904, "char_end": 8982, "patch_content": "          oldList.get(oldItemPosition),\n          newList.get(newItemPosition)", "patch_id": "airbnb/epoxy/36qZ5Epf", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 8861, "func_end": 8992}}
{"file_content": "", "char_start": 9095, "char_end": 9219, "patch_content": "      return diffCallback.areContentsTheSame(\n          oldList.get(oldItemPosition),\n          newList.get(newItemPosition)", "patch_id": "airbnb/epoxy/V3qZYaUw", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 9095, "func_end": 9229}}
{"file_content": "", "char_start": 8904, "char_end": 8982, "patch_content": "          oldList.get(oldItemPosition),\n          newList.get(newItemPosition)", "patch_id": "airbnb/epoxy/WLW6xf8f", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/AsyncEpoxyDiffer.java", "_extra": {"func_start": 9343, "func_end": 9475}}
{"file_content": "", "char_start": 4168, "char_end": 4225, "patch_content": "      super(size);\n      for (int i = 0; i < size; ++i) {", "patch_id": "airbnb/epoxy/PWTVTCko", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ViewHolderState.java", "_extra": {"func_start": 4168, "func_end": 4267}}
{"file_content": "", "char_start": 3566, "char_end": 3660, "patch_content": "          !isInFirstRow && isInLastRow(position, itemCount, spanSizeLookup, spanCount);\n    }\n", "patch_id": "airbnb/epoxy/QXsofYfK", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyItemSpacingDecorator.java", "_extra": {"func_start": 2763, "func_end": 3660}}
{"file_content": "", "char_start": 5347, "char_end": 5378, "patch_content": "      }\n    }\n\n    return true;", "patch_id": "airbnb/epoxy/nUhLgerC", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyItemSpacingDecorator.java", "_extra": {"func_start": 5504, "func_end": 5720}}
{"file_content": "", "char_start": 575, "char_end": 605, "patch_content": "    this(id, quantity, null);\n", "patch_id": "airbnb/epoxy/YyjA3U9j", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/QuantityStringResAttribute.java", "_extra": {"func_start": 575, "func_end": 605}}
{"file_content": "", "char_start": 973, "char_end": 1072, "patch_content": "    } else {\n      return context.getResources().getQuantityString(id, quantity, formatArgs);\n    }", "patch_id": "airbnb/epoxy/ECfDVzhK", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/QuantityStringResAttribute.java", "_extra": {"func_start": 848, "func_end": 1073}}
{"file_content": "", "char_start": 1509, "char_end": 1523, "patch_content": "    return 0;\n", "patch_id": "airbnb/epoxy/NJgHU9i8", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelWithView.java", "_extra": {"func_start": 1509, "func_end": 1523}}
{"file_content": "", "char_start": 1956, "char_end": 2041, "patch_content": "        \"Layout resources are unsupported. Views must be created with `buildView`\");\n", "patch_id": "airbnb/epoxy/HRPP4qN6", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelWithView.java", "_extra": {"func_start": 1911, "func_end": 2041}}
{"file_content": "", "char_start": 2231, "char_end": 2281, "patch_content": "    }\n    super.requestDelayedModelBuild(delayMs);", "patch_id": "airbnb/epoxy/5Z4YLCeQ", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/Typed4EpoxyController.java", "_extra": {"func_start": 2012, "func_end": 2282}}
{"file_content": "", "char_start": 202, "char_end": 235, "patch_content": "    this.tag = tag;\n    reset();\n", "patch_id": "airbnb/epoxy/UMULZKBG", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DebugTimer.java", "_extra": {"func_start": 202, "func_end": 235}}
{"file_content": "", "char_start": 1278, "char_end": 1362, "patch_content": "    state.position = position;\n\n    if (immutableModel) {\n      state.model = model;", "patch_id": "airbnb/epoxy/gTciEFHe", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelState.java", "_extra": {"func_start": 1160, "func_end": 1442}}
{"file_content": "", "char_start": 1701, "char_end": 1802, "patch_content": "    pair = new ModelState();\n    pair.lastMoveOp = 0;\n    pair.id = id;\n    pair.position = position;", "patch_id": "airbnb/epoxy/SPvdKgMJ", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ModelState.java", "_extra": {"func_start": 1612, "func_end": 1879}}
{"file_content": "", "char_start": 1058, "char_end": 1155, "patch_content": "    allowModelBuildRequests = true;\n    requestModelBuild();\n    allowModelBuildRequests = false;", "patch_id": "airbnb/epoxy/TNdCG3PA", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/TypedEpoxyController.java", "_extra": {"func_start": 1034, "func_end": 1156}}
{"file_content": "", "char_start": 1290, "char_end": 1470, "patch_content": "          \"You cannot call `requestModelBuild` directly. Call `setData` instead to trigger a \"\n              + \"model refresh with new data.\");\n    }\n    super.requestModelBuild();", "patch_id": "airbnb/epoxy/aaguTwBR", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/TypedEpoxyController.java", "_extra": {"func_start": 1215, "func_end": 1471}}
{"file_content": "", "char_start": 1814, "char_end": 2008, "patch_content": "          \"You cannot call `requestModelBuild` directly. Call `setData` instead to trigger a \"\n              + \"model refresh with new data.\");\n    }\n    super.requestDelayedModelBuild(delayMs);", "patch_id": "airbnb/epoxy/REscWdF9", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/TypedEpoxyController.java", "_extra": {"func_start": 1739, "func_end": 2009}}
{"file_content": "", "char_start": 2212, "char_end": 2349, "patch_content": "          \"You cannot call `buildModels` directly. Call `setData` instead to trigger a model \"\n              + \"refresh with new data.\");", "patch_id": "airbnb/epoxy/NHhNsHGG", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/TypedEpoxyController.java", "_extra": {"func_start": 2142, "func_end": 2386}}
{"file_content": "", "char_start": 1856, "char_end": 2001, "patch_content": "    String clsName = controllerClass.getName();\n    if (clsName.startsWith(\"android.\") || clsName.startsWith(\"java.\")) {\n      return null;\n    }", "patch_id": "airbnb/epoxy/RwVg2Pbo", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ControllerHelperLookup.java", "_extra": {"func_start": 1690, "func_end": 2524}}
{"file_content": "", "char_start": 130, "char_end": 150, "patch_content": "    super(message);\n", "patch_id": "airbnb/epoxy/a65mnZ4o", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/IllegalEpoxyUsage.java", "_extra": {"func_start": 130, "func_end": 150}}
{"file_content": "", "char_start": 613, "char_end": 664, "patch_content": "    defaultString = null;\n    defaultStringRes = 0;", "patch_id": "airbnb/epoxy/rse9JsNp", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/StringAttributeData.java", "_extra": {"func_start": 589, "func_end": 665}}
{"file_content": "", "char_start": 739, "char_end": 829, "patch_content": "    hasDefault = true;\n    this.defaultString = defaultString;\n    string = defaultString;", "patch_id": "airbnb/epoxy/NXTUwoxe", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/StringAttributeData.java", "_extra": {"func_start": 739, "func_end": 856}}
{"file_content": "", "char_start": 1115, "char_end": 1159, "patch_content": "    this.string = string;\n    stringRes = 0;", "patch_id": "airbnb/epoxy/btVa8E9d", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/StringAttributeData.java", "_extra": {"func_start": 1115, "func_end": 1179}}
{"file_content": "", "char_start": 1235, "char_end": 1266, "patch_content": "    setValue(stringRes, null);\n", "patch_id": "airbnb/epoxy/LNNdnWM3", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/StringAttributeData.java", "_extra": {"func_start": 1235, "func_end": 1266}}
{"file_content": "", "char_start": 1449, "char_end": 1490, "patch_content": "      string = null;\n      pluralRes = 0;", "patch_id": "airbnb/epoxy/9nG3rpJH", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/StringAttributeData.java", "_extra": {"func_start": 1353, "func_end": 1542}}
{"file_content": "", "char_start": 1751, "char_end": 1845, "patch_content": "      throw new IllegalArgumentException(\"0 is an invalid value for required strings.\");\n    }", "patch_id": "airbnb/epoxy/Y8CZ4oJu", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/StringAttributeData.java", "_extra": {"func_start": 1589, "func_end": 1846}}
{"file_content": "", "char_start": 2040, "char_end": 2130, "patch_content": "      this.formatArgs = formatArgs;\n      string = null;\n      stringRes = 0;\n    } else {", "patch_id": "airbnb/epoxy/tgR475Lo", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/StringAttributeData.java", "_extra": {"func_start": 1948, "func_end": 2169}}
{"file_content": "", "char_start": 2372, "char_end": 2505, "patch_content": "      } else {\n        return context.getResources().getQuantityString(pluralRes, quantity);\n      }\n    } else if (stringRes != 0) {", "patch_id": "airbnb/epoxy/LjW7eX5X", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/StringAttributeData.java", "_extra": {"func_start": 2224, "func_end": 2731}}
{"file_content": "", "char_start": 3583, "char_end": 3602, "patch_content": "    return result;\n", "patch_id": "airbnb/epoxy/Ypxahbku", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/StringAttributeData.java", "_extra": {"func_start": 3357, "func_end": 3602}}
{"file_content": "", "char_start": 2659, "char_end": 2679, "patch_content": "    super(context);\n", "patch_id": "airbnb/epoxy/knP4KmbN", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/Carousel.java", "_extra": {"func_start": 2659, "func_end": 2679}}
{"file_content": "", "char_start": 2751, "char_end": 2778, "patch_content": "    super(context, attrs);\n", "patch_id": "airbnb/epoxy/SHgztvAv", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/Carousel.java", "_extra": {"func_start": 2751, "func_end": 2778}}
{"file_content": "", "char_start": 2864, "char_end": 2901, "patch_content": "    super(context, attrs, defStyle);\n", "patch_id": "airbnb/epoxy/dKFikN8b", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/Carousel.java", "_extra": {"func_start": 2864, "func_end": 2901}}
{"file_content": "", "char_start": 3543, "char_end": 3661, "patch_content": "    if (snapHelperFactory != null) {\n      snapHelperFactory.buildSnapHelper(getContext()).attachToRecyclerView(this);", "patch_id": "airbnb/epoxy/MwzUgRZB", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/Carousel.java", "_extra": {"func_start": 2944, "func_end": 3788}}
{"file_content": "", "char_start": 4117, "char_end": 4160, "patch_content": "    return defaultGlobalSnapHelperFactory;\n", "patch_id": "airbnb/epoxy/fAQQn6Pz", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/Carousel.java", "_extra": {"func_start": 4117, "func_end": 4160}}
{"file_content": "", "char_start": 6622, "char_end": 6830, "patch_content": "    // Use the linearlayoutmanager default of 2 if the user did not specify one\n    int prefetchCount = numItemsToPrefetch == 0 ? 2 : numItemsToPrefetch;\n\n    LayoutManager layoutManager = getLayoutManager();", "patch_id": "airbnb/epoxy/gTJZnokw", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/Carousel.java", "_extra": {"func_start": 6497, "func_end": 6981}}
{"file_content": "", "char_start": 9736, "char_end": 9882, "patch_content": "    if (initialWidth instanceof Integer) {\n      ViewGroup.LayoutParams params = child.getLayoutParams();\n      params.width = (int) initialWidth;", "patch_id": "airbnb/epoxy/Xq7hgiby", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/Carousel.java", "_extra": {"func_start": 9582, "func_end": 10051}}
{"file_content": "", "char_start": 10644, "char_end": 10685, "patch_content": "    return defaultSpacingBetweenItemsDp;\n", "patch_id": "airbnb/epoxy/kKfN3sJq", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/Carousel.java", "_extra": {"func_start": 10644, "func_end": 10685}}
{"file_content": "", "char_start": 11369, "char_end": 11523, "patch_content": "    int px = dpToPx(paddingDp != NO_VALUE_SET ? paddingDp : getDefaultSpacingBetweenItemsDp());\n    setPadding(px, px, px, px);\n    setItemSpacingPx(px);\n", "patch_id": "airbnb/epoxy/cnZuwRvu", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/Carousel.java", "_extra": {"func_start": 11369, "func_end": 11523}}
{"file_content": "", "char_start": 15594, "char_end": 15681, "patch_content": "      this(paddingPx, paddingPx, paddingPx, paddingPx, itemSpacingPx, PaddingType.PX);\n", "patch_id": "airbnb/epoxy/nXodqCt6", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/Carousel.java", "_extra": {"func_start": 15594, "func_end": 15681}}
{"file_content": "", "char_start": 16141, "char_end": 16218, "patch_content": "      this(leftPx, topPx, rightPx, bottomPx, itemSpacingPx, PaddingType.PX);\n", "patch_id": "airbnb/epoxy/QHb2WeQZ", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/Carousel.java", "_extra": {"func_start": 16141, "func_end": 16218}}
{"file_content": "", "char_start": 17748, "char_end": 17777, "patch_content": "    super.setModels(models);\n", "patch_id": "airbnb/epoxy/FMFBjF92", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/Carousel.java", "_extra": {"func_start": 17748, "func_end": 17777}}
{"file_content": "", "char_start": 3744, "char_end": 3790, "patch_content": "    this(layoutRes, new ArrayList<>(models));\n", "patch_id": "airbnb/epoxy/Hsj9cxU2", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 3744, "func_end": 3790}}
{"file_content": "", "char_start": 4034, "char_end": 4095, "patch_content": "    this(layoutRes, new ArrayList<>(Arrays.asList(models)));\n", "patch_id": "airbnb/epoxy/hU2SrADz", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 4034, "func_end": 4095}}
{"file_content": "", "char_start": 4892, "char_end": 4963, "patch_content": "    models = new ArrayList<>();\n    shouldSaveViewStateDefault = false;", "patch_id": "airbnb/epoxy/DaSisUQf", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 4892, "func_end": 4964}}
{"file_content": "", "char_start": 5164, "char_end": 5328, "patch_content": "    // By default we save view state if any of the models need to save state.\n    shouldSaveViewStateDefault |= model.shouldSaveViewState();\n    models.add(model);\n", "patch_id": "airbnb/epoxy/JH4oeYS2", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 5164, "func_end": 5328}}
{"file_content": "", "char_start": 5413, "char_end": 5574, "patch_content": "    iterateModels(holder, new IterateModelsCallback() {\n      @Override\n      public void onModel(EpoxyModel model, EpoxyViewHolder viewHolder, int modelIndex) {", "patch_id": "airbnb/epoxy/aXwZMThw", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 5413, "func_end": 5712}}
{"file_content": "", "char_start": 5575, "char_end": 5711, "patch_content": "        setViewVisibility(model, viewHolder);\n        viewHolder.bind(model, null, Collections.emptyList(), modelIndex);\n      }\n    });", "patch_id": "airbnb/epoxy/QqCj6DxC", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 5835, "func_end": 6134}}
{"file_content": "", "char_start": 6353, "char_end": 6436, "patch_content": "\n    final EpoxyModelGroup previousGroup = (EpoxyModelGroup) previouslyBoundModel;\n", "patch_id": "airbnb/epoxy/jGKA6hV8", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 6251, "func_end": 7052}}
{"file_content": "", "char_start": 7145, "char_end": 7226, "patch_content": "    if (model.isShown()) {\n      viewHolder.itemView.setVisibility(View.VISIBLE);", "patch_id": "airbnb/epoxy/pQ8BEQNq", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 7145, "func_end": 7298}}
{"file_content": "", "char_start": 7385, "char_end": 7411, "patch_content": "    holder.unbindGroup();\n", "patch_id": "airbnb/epoxy/bh8JZCmY", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 7385, "func_end": 7411}}
{"file_content": "", "char_start": 5469, "char_end": 5574, "patch_content": "      @Override\n      public void onModel(EpoxyModel model, EpoxyViewHolder viewHolder, int modelIndex) {", "patch_id": "airbnb/epoxy/caA6sbzv", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 7505, "func_end": 7781}}
{"file_content": "", "char_start": 5696, "char_end": 5712, "patch_content": "      }\n    });\n", "patch_id": "airbnb/epoxy/3t5K7gMM", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 7877, "func_end": 8155}}
{"file_content": "", "char_start": 8248, "char_end": 8319, "patch_content": "    holder.bindGroupIfNeeded(this);\n    int modelCount = models.size();", "patch_id": "airbnb/epoxy/aSrE2hKh", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 8248, "func_end": 8444}}
{"file_content": "", "char_start": 8944, "char_end": 9020, "patch_content": "        \"You should set a layout with layout(...) instead of using this.\");\n", "patch_id": "airbnb/epoxy/kuzX3PPC", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 8899, "func_end": 9020}}
{"file_content": "", "char_start": 10005, "char_end": 10022, "patch_content": "    return true;\n", "patch_id": "airbnb/epoxy/c2TgoVAy", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 10005, "func_end": 10022}}
{"file_content": "", "char_start": 10120, "char_end": 10161, "patch_content": "    return new ModelGroupHolder(parent);\n", "patch_id": "airbnb/epoxy/LaZs3RV7", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 10120, "func_end": 10161}}
{"file_content": "", "char_start": 10260, "char_end": 10356, "patch_content": "    if (!(o instanceof EpoxyModelGroup)) {\n      return false;\n    }\n    if (!super.equals(o)) {", "patch_id": "airbnb/epoxy/SWwv7nDR", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 10214, "func_end": 10472}}
{"file_content": "", "char_start": 10550, "char_end": 10615, "patch_content": "    result = 31 * result + models.hashCode();\n    return result;\n", "patch_id": "airbnb/epoxy/s3vHDBwG", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelGroup.java", "_extra": {"func_start": 10515, "func_end": 10615}}
{"file_content": "", "char_start": 1215, "char_end": 1234, "patch_content": "    return models;\n", "patch_id": "airbnb/epoxy/6U6xfN5v", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 1215, "func_end": 1234}}
{"file_content": "", "char_start": 1610, "char_end": 1616, "patch_content": "    }\n", "patch_id": "airbnb/epoxy/4FJHwiK4", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 1510, "func_end": 1905}}
{"file_content": "", "char_start": 1974, "char_end": 2071, "patch_content": "    EpoxyModel<?> model = models.get(position);\n    return model.isShown() ? model : hiddenModel;", "patch_id": "airbnb/epoxy/Qq9TaJp7", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 1974, "func_end": 2072}}
{"file_content": "", "char_start": 3260, "char_end": 3297, "patch_content": "    notifyModelChanged(model, null);\n", "patch_id": "airbnb/epoxy/5iY5Cxf3", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 3260, "func_end": 3297}}
{"file_content": "", "char_start": 3529, "char_end": 3592, "patch_content": "    int index = getModelPosition(model);\n    if (index != -1) {", "patch_id": "airbnb/epoxy/gdJMrv2a", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 3529, "func_end": 3640}}
{"file_content": "", "char_start": 3912, "char_end": 3994, "patch_content": "    resumeModelListNotifications();\n\n    notifyItemRangeInserted(initialSize, 1);\n", "patch_id": "airbnb/epoxy/4YPxVWr3", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 3811, "func_end": 3994}}
{"file_content": "", "char_start": 4178, "char_end": 4259, "patch_content": "    int initialSize = models.size();\n    int numModelsToAdd = modelsToAdd.length;", "patch_id": "airbnb/epoxy/tcxRserM", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 4178, "func_end": 4508}}
{"file_content": "", "char_start": 4749, "char_end": 4852, "patch_content": "    pauseModelListNotifications();\n    models.addAll(modelsToAdd);\n    resumeModelListNotifications();\n", "patch_id": "airbnb/epoxy/6ZqjSr5R", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 4711, "func_end": 4915}}
{"file_content": "", "char_start": 5335, "char_end": 5450, "patch_content": "\n    pauseModelListNotifications();\n    models.add(targetIndex, modelToInsert);\n    resumeModelListNotifications();", "patch_id": "airbnb/epoxy/sABdP9oq", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 5154, "func_end": 5489}}
{"file_content": "", "char_start": 5452, "char_end": 5489, "patch_content": "    notifyItemInserted(targetIndex);\n", "patch_id": "airbnb/epoxy/Ri8EiBDT", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 5725, "func_end": 6094}}
{"file_content": "", "char_start": 6339, "char_end": 6442, "patch_content": "      pauseModelListNotifications();\n      models.remove(index);\n      resumeModelListNotifications();\n", "patch_id": "airbnb/epoxy/aDYti99f", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 6275, "func_end": 6481}}
{"file_content": "", "char_start": 6601, "char_end": 6692, "patch_content": "\n    pauseModelListNotifications();\n    models.clear();\n    resumeModelListNotifications();", "patch_id": "airbnb/epoxy/8gYNeCsm", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 6559, "func_end": 6743}}
{"file_content": "", "char_start": 7187, "char_end": 7333, "patch_content": "    // This is a sublist, so clearing it will clear the models in the original list\n    pauseModelListNotifications();\n    modelsToRemove.clear();", "patch_id": "airbnb/epoxy/eHGxkfTD", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 7026, "func_end": 7455}}
{"file_content": "", "char_start": 7868, "char_end": 7910, "patch_content": "      return;\n    }\n\n    model.show(show);", "patch_id": "airbnb/epoxy/Df5QnueV", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 7833, "func_end": 7942}}
{"file_content": "", "char_start": 8204, "char_end": 8232, "patch_content": "    showModel(model, true);\n", "patch_id": "airbnb/epoxy/Qpyygq4F", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 8204, "func_end": 8232}}
{"file_content": "", "char_start": 8505, "char_end": 8544, "patch_content": "    showModels(Arrays.asList(models));\n", "patch_id": "airbnb/epoxy/UVu5Uky2", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 8505, "func_end": 8544}}
{"file_content": "", "char_start": 8937, "char_end": 8982, "patch_content": "    showModels(Arrays.asList(models), show);\n", "patch_id": "airbnb/epoxy/Gn59Ejna", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 8937, "func_end": 8982}}
{"file_content": "", "char_start": 9262, "char_end": 9292, "patch_content": "    showModels(models, true);\n", "patch_id": "airbnb/epoxy/XQ7WG3Ly", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 9262, "func_end": 9292}}
{"file_content": "", "char_start": 9692, "char_end": 9762, "patch_content": "    for (EpoxyModel<?> model : models) {\n      showModel(model, show);", "patch_id": "airbnb/epoxy/Ue9fvQJ2", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 9692, "func_end": 9769}}
{"file_content": "", "char_start": 10034, "char_end": 10063, "patch_content": "    showModel(model, false);\n", "patch_id": "airbnb/epoxy/C9PpYYTR", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 10034, "func_end": 10063}}
{"file_content": "", "char_start": 10344, "char_end": 10375, "patch_content": "    showModels(models, false);\n", "patch_id": "airbnb/epoxy/dxwRCLMU", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 10344, "func_end": 10375}}
{"file_content": "", "char_start": 10649, "char_end": 10688, "patch_content": "    hideModels(Arrays.asList(models));\n", "patch_id": "airbnb/epoxy/r9tEVmoE", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 10649, "func_end": 10688}}
{"file_content": "", "char_start": 10951, "char_end": 10993, "patch_content": "    hideModels(getAllModelsAfter(model));\n", "patch_id": "airbnb/epoxy/BPhjJEYb", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 10951, "func_end": 10993}}
{"file_content": "", "char_start": 11885, "char_end": 11932, "patch_content": "    ((ModelList) models).pauseNotifications();\n", "patch_id": "airbnb/epoxy/8np2SNLX", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 11885, "func_end": 11932}}
{"file_content": "", "char_start": 11985, "char_end": 12033, "patch_content": "    ((ModelList) models).resumeNotifications();\n", "patch_id": "airbnb/epoxy/b2vhwXdD", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyAdapter.java", "_extra": {"func_start": 11985, "func_end": 12033}}
{"file_content": "", "char_start": 1563, "char_end": 1638, "patch_content": "    }\n    if (!(o instanceof SimpleEpoxyModel)) {\n      return false;\n    }", "patch_id": "airbnb/epoxy/CHU6KjZ3", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/SimpleEpoxyModel.java", "_extra": {"func_start": 1523, "func_end": 1997}}
{"file_content": "", "char_start": 6797, "char_end": 6917, "patch_content": "      public void run() {\n        clearRecyclerViewSelectionMarker(recyclerView);\n      }\n    }, TOUCH_DEBOUNCE_MILLIS);", "patch_id": "airbnb/epoxy/Tg5na9ww", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyModelTouchCallback.java", "_extra": {"func_start": 6191, "func_end": 6918}}
{"file_content": "", "char_start": 738, "char_end": 861, "patch_content": "          \"You cannot call `requestModelBuild` directly. Call `setModels` instead.\");\n    }\n    super.requestModelBuild();\n", "patch_id": "airbnb/epoxy/5p3fjaZ2", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/SimpleEpoxyController.java", "_extra": {"func_start": 675, "func_end": 861}}
{"file_content": "", "char_start": 2281, "char_end": 2418, "patch_content": "          \"You cannot call `buildModels` directly. Call `setData` instead to trigger a model \"\n              + \"refresh with new data.\");", "patch_id": "airbnb/epoxy/8qCiG5d3", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/Typed2EpoxyController.java", "_extra": {"func_start": 2211, "func_end": 2456}}
{"file_content": "", "char_start": 4714, "char_end": 4776, "patch_content": "    this(defaultModelBuildingHandler, defaultDiffingHandler);\n", "patch_id": "airbnb/epoxy/T6HyChoL", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 4714, "func_end": 4776}}
{"file_content": "", "char_start": 4926, "char_end": 5027, "patch_content": "    modelBuildHandler = modelBuildingHandler;\n    setDebugLoggingEnabled(globalDebugLoggingEnabled);\n", "patch_id": "airbnb/epoxy/NVS9RspV", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 4862, "func_end": 5027}}
{"file_content": "", "char_start": 7827, "char_end": 7955, "patch_content": "        || threadBuildingModels != null // model build is in progress\n        || adapter.isDiffInProgress(); // Diff in progress", "patch_id": "airbnb/epoxy/G5aMGwZ3", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 7735, "func_end": 7956}}
{"file_content": "", "char_start": 8532, "char_end": 8577, "patch_content": "    adapter.addModelBuildListener(listener);\n", "patch_id": "airbnb/epoxy/PxR6gREA", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 8532, "func_end": 8577}}
{"file_content": "", "char_start": 8896, "char_end": 8944, "patch_content": "    adapter.removeModelBuildListener(listener);\n", "patch_id": "airbnb/epoxy/Ekojifpo", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 8896, "func_end": 8944}}
{"file_content": "", "char_start": 10312, "char_end": 10388, "patch_content": "    }\n\n    if (requestedModelBuildType == RequestedModelBuildType.DELAYED) {", "patch_id": "airbnb/epoxy/CrT9jHxq", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 10168, "func_end": 10712}}
{"file_content": "", "char_start": 13626, "char_end": 13686, "patch_content": "    return currentModelCount != 0 ? currentModelCount : 25;\n", "patch_id": "airbnb/epoxy/84ui7p7Y", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 13574, "func_end": 13686}}
{"file_content": "", "char_start": 14759, "char_end": 14773, "patch_content": "      }\n    }\n", "patch_id": "airbnb/epoxy/gCDcRde4", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 14587, "func_end": 14789}}
{"file_content": "", "char_start": 14618, "char_end": 14740, "patch_content": "    int size = modelsBeingBuilt.size();\n    for (int i = 0; i < size; i++) {\n      if (modelsBeingBuilt.get(i) == model) {", "patch_id": "airbnb/epoxy/4viUGYnA", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 14853, "func_end": 15095}}
{"file_content": "", "char_start": 6919, "char_end": 6925, "patch_content": "    }\n", "patch_id": "airbnb/epoxy/SQhgHMKD", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 15172, "func_end": 15353}}
{"file_content": "", "char_start": 15964, "char_end": 16017, "patch_content": "\n      for (Interceptor interceptor : interceptors) {", "patch_id": "airbnb/epoxy/s4AVmvso", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 15693, "func_end": 16540}}
{"file_content": "", "char_start": 17671, "char_end": 17706, "patch_content": "    interceptors.add(interceptor);\n", "patch_id": "airbnb/epoxy/AUDAms6U", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 17671, "func_end": 17706}}
{"file_content": "", "char_start": 6919, "char_end": 6925, "patch_content": "    }\n", "patch_id": "airbnb/epoxy/ADMy6RsL", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 18516, "func_end": 18647}}
{"file_content": "", "char_start": 6919, "char_end": 6925, "patch_content": "    }\n", "patch_id": "airbnb/epoxy/sEQxDwmU", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 18695, "func_end": 18812}}
{"file_content": "", "char_start": 18993, "char_end": 19016, "patch_content": "    model.addTo(this);\n", "patch_id": "airbnb/epoxy/4hk3kNix", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 18993, "func_end": 19016}}
{"file_content": "", "char_start": 20485, "char_end": 20654, "patch_content": "    // In that case we may have a previously staged model that still needs to be added.\n    clearModelFromStaging(modelToAdd);\n    modelToAdd.controllerToStageTo = null;", "patch_id": "airbnb/epoxy/kNytFBKj", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 19906, "func_end": 20693}}
{"file_content": "", "char_start": 6919, "char_end": 6925, "patch_content": "    }\n", "patch_id": "airbnb/epoxy/5pmsZDcb", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 21305, "func_end": 21410}}
{"file_content": "", "char_start": 21489, "char_end": 21550, "patch_content": "      stagedModel.addTo(this);\n    }\n    stagedModel = null;\n", "patch_id": "airbnb/epoxy/4buz2Fee", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 21458, "func_end": 21550}}
{"file_content": "", "char_start": 21639, "char_end": 21710, "patch_content": "      addCurrentlyStagedModelIfExists();\n    }\n    stagedModel = null;\n", "patch_id": "airbnb/epoxy/5iBqPpMx", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 21607, "func_end": 21710}}
{"file_content": "", "char_start": 21853, "char_end": 21912, "patch_content": "    return threadBuildingModels == Thread.currentThread();\n", "patch_id": "airbnb/epoxy/hWkKiZvH", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 21853, "func_end": 21912}}
{"file_content": "", "char_start": 23355, "char_end": 23434, "patch_content": "      }\n    }\n\n    throw new IllegalArgumentException(\"No duplicates in list\");", "patch_id": "airbnb/epoxy/rGJy738K", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 23180, "func_end": 23435}}
{"file_content": "", "char_start": 24107, "char_end": 24153, "patch_content": "    this.filterDuplicates = filterDuplicates;\n", "patch_id": "airbnb/epoxy/FHgoKmdF", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 24107, "func_end": 24153}}
{"file_content": "", "char_start": 24207, "char_end": 24236, "patch_content": "    return filterDuplicates;\n", "patch_id": "airbnb/epoxy/iJAi3acR", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 24207, "func_end": 24236}}
{"file_content": "", "char_start": 24586, "char_end": 24659, "patch_content": "    EpoxyController.filterDuplicatesDefault = filterDuplicatesByDefault;\n", "patch_id": "airbnb/epoxy/jcSMABji", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 24586, "func_end": 24659}}
{"file_content": "", "char_start": 25679, "char_end": 25802, "patch_content": "      timer = NO_OP_TIMER;\n      if (debugObserver != null) {\n        adapter.unregisterAdapterDataObserver(debugObserver);", "patch_id": "airbnb/epoxy/VHsrbYRQ", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 25383, "func_end": 25817}}
{"file_content": "", "char_start": 25865, "char_end": 25898, "patch_content": "    return timer != NO_OP_TIMER;\n", "patch_id": "airbnb/epoxy/iFcVRxua", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 25865, "func_end": 25898}}
{"file_content": "", "char_start": 26163, "char_end": 26238, "patch_content": "    EpoxyController.globalDebugLoggingEnabled = globalDebugLoggingEnabled;\n", "patch_id": "airbnb/epoxy/UVEfcLQz", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 26163, "func_end": 26238}}
{"file_content": "", "char_start": 27837, "char_end": 27857, "patch_content": "    return adapter;\n", "patch_id": "airbnb/epoxy/5AGTyxgb", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 27837, "func_end": 27857}}
{"file_content": "", "char_start": 32065, "char_end": 32357, "patch_content": "                \"This EpoxyController had its adapter added to more than one ReyclerView. Epoxy \"\n                    + \"does not support attaching an adapter to multiple RecyclerViews because \"\n                    + \"saved state will not work properly. If you did not intend to attach your \"", "patch_id": "airbnb/epoxy/oRCkKwjR", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyController.java", "_extra": {"func_start": 31647, "func_end": 32988}}
{"file_content": "", "char_start": 1596, "char_end": 1650, "patch_content": "              + \"model refresh with new data.\");\n    }", "patch_id": "airbnb/epoxy/MnM4Lgs5", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/Typed3EpoxyController.java", "_extra": {"func_start": 1950, "func_end": 2220}}
{"file_content": "", "char_start": 2484, "char_end": 2528, "patch_content": "    }\n    buildModels(data1, data2, data3);\n", "patch_id": "airbnb/epoxy/GhMT2Sm2", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/Typed3EpoxyController.java", "_extra": {"func_start": 2276, "func_end": 2528}}
{"file_content": "", "char_start": 5369, "char_end": 5415, "patch_content": "          Arrays.asList(targetModelClasses));\n", "patch_id": "airbnb/epoxy/72hpQ9Br", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyTouchHelper.java", "_extra": {"func_start": 5278, "func_end": 5415}}
{"file_content": "", "char_start": 5369, "char_end": 5415, "patch_content": "          Arrays.asList(targetModelClasses));\n", "patch_id": "airbnb/epoxy/Chq9UCSe", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/EpoxyTouchHelper.java", "_extra": {"func_start": 12375, "func_end": 12501}}
{"file_content": "", "char_start": 627, "char_end": 701, "patch_content": "    value ^= (value >>> 35);\n    value ^= (value << 4);\n    return value;\n", "patch_id": "airbnb/epoxy/tBPvh2Ax", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/IdUtils.java", "_extra": {"func_start": 599, "func_end": 701}}
{"file_content": "", "char_start": 582, "char_end": 634, "patch_content": "\n    if (!(viewHolder instanceof EpoxyViewHolder)) {", "patch_id": "airbnb/epoxy/DXnWCvSt", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/ListenersUtils.java", "_extra": {"func_start": 340, "func_end": 702}}
{"file_content": "", "char_start": 1367, "char_end": 1435, "patch_content": "    return new DiffResult(Collections.EMPTY_LIST, newModels, null);\n", "patch_id": "airbnb/epoxy/dbthqGh4", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffResult.java", "_extra": {"func_start": 1338, "func_end": 1435}}
{"file_content": "", "char_start": 2038, "char_end": 2106, "patch_content": "    return new DiffResult(previousModels, newModels, differResult);\n", "patch_id": "airbnb/epoxy/5du4gKEq", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffResult.java", "_extra": {"func_start": 2038, "func_end": 2106}}
{"file_content": "", "char_start": 2347, "char_end": 2417, "patch_content": "    this.newModels = newModels;\n    this.differResult = differResult;\n", "patch_id": "airbnb/epoxy/qjF9sYP3", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffResult.java", "_extra": {"func_start": 2305, "func_end": 2417}}
{"file_content": "", "char_start": 2466, "char_end": 2522, "patch_content": "    dispatchTo(new AdapterListUpdateCallback(adapter));\n", "patch_id": "airbnb/epoxy/WhCSDL8R", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffResult.java", "_extra": {"func_start": 2466, "func_end": 2522}}
{"file_content": "", "char_start": 2583, "char_end": 2781, "patch_content": "    if (differResult != null) {\n      differResult.dispatchUpdatesTo(callback);\n    } else if (newModels.isEmpty() && !previousModels.isEmpty()) {\n      callback.onRemoved(0, previousModels.size());", "patch_id": "airbnb/epoxy/j2iCCwB5", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/DiffResult.java", "_extra": {"func_start": 2583, "func_end": 2933}}
{"file_content": "", "char_start": 1137, "char_end": 1164, "patch_content": "    add(indexToInsert, 1);\n", "patch_id": "airbnb/epoxy/Qq2Qfdft", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOpHelper.java", "_extra": {"func_start": 1137, "func_end": 1164}}
{"file_content": "", "char_start": 1534, "char_end": 1623, "patch_content": "      addItemsToLastOperation(itemCount, null);\n    } else {\n      numInsertionBatches++;", "patch_id": "airbnb/epoxy/VCBW7y5r", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOpHelper.java", "_extra": {"func_start": 1216, "func_end": 1684}}
{"file_content": "", "char_start": 1724, "char_end": 1757, "patch_content": "    update(indexToChange, null);\n", "patch_id": "airbnb/epoxy/4kUNif98", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOpHelper.java", "_extra": {"func_start": 1724, "func_end": 1757}}
{"file_content": "", "char_start": 2017, "char_end": 2178, "patch_content": "        lastOp.positionStart = indexToChange;\n      } else if (lastOp.positionEnd() == indexToChange) {\n        // Add another item at the end of the batch range", "patch_id": "airbnb/epoxy/TNZHSewv", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOpHelper.java", "_extra": {"func_start": 1826, "func_end": 2720}}
{"file_content": "", "char_start": 2760, "char_end": 2790, "patch_content": "    remove(indexToRemove, 1);\n", "patch_id": "airbnb/epoxy/TLqDjqu9", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOpHelper.java", "_extra": {"func_start": 2760, "func_end": 2790}}
{"file_content": "", "char_start": 3443, "char_end": 3539, "patch_content": "    } else {\n      numRemovalBatches++;\n      addNewOperation(REMOVE, startPosition, itemCount);", "patch_id": "airbnb/epoxy/EnqvbrPz", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOpHelper.java", "_extra": {"func_start": 2845, "func_end": 3546}}
{"file_content": "", "char_start": 3611, "char_end": 3667, "patch_content": "    return lastOp != null && lastOp.type == updateType;\n", "patch_id": "airbnb/epoxy/R2To3Ujm", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOpHelper.java", "_extra": {"func_start": 3611, "func_end": 3667}}
{"file_content": "", "char_start": 3750, "char_end": 3804, "patch_content": "    addNewOperation(type, position, itemCount, null);\n", "patch_id": "airbnb/epoxy/jtVpwbv3", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOpHelper.java", "_extra": {"func_start": 3750, "func_end": 3804}}
{"file_content": "", "char_start": 3926, "char_end": 4018, "patch_content": "    lastOp = UpdateOp.instance(type, position, itemCount, payload);\n    opList.add(lastOp);\n", "patch_id": "airbnb/epoxy/3Hb9eSwV", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOpHelper.java", "_extra": {"func_start": 3926, "func_end": 4018}}
{"file_content": "", "char_start": 4106, "char_end": 4176, "patch_content": "    lastOp.itemCount += numItemsToAdd;\n    lastOp.addPayload(payload);", "patch_id": "airbnb/epoxy/QZfA5Z55", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOpHelper.java", "_extra": {"func_start": 4106, "func_end": 4177}}
{"file_content": "", "char_start": 4261, "char_end": 4359, "patch_content": "    UpdateOp op = UpdateOp.instance(MOVE, from, to, null);\n    opList.add(op);\n    moves.add(op);\n", "patch_id": "airbnb/epoxy/C4zrMmWk", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOpHelper.java", "_extra": {"func_start": 4214, "func_end": 4359}}
{"file_content": "", "char_start": 4389, "char_end": 4413, "patch_content": "    return numRemovals;\n", "patch_id": "airbnb/epoxy/MoYSHULj", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOpHelper.java", "_extra": {"func_start": 4389, "func_end": 4413}}
{"file_content": "", "char_start": 4504, "char_end": 4530, "patch_content": "    return numInsertions;\n", "patch_id": "airbnb/epoxy/3rGetj5D", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOpHelper.java", "_extra": {"func_start": 4504, "func_end": 4530}}
{"file_content": "", "char_start": 4620, "char_end": 4645, "patch_content": "    return moves.size();\n", "patch_id": "airbnb/epoxy/hEAYJHPm", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOpHelper.java", "_extra": {"func_start": 4620, "func_end": 4645}}
{"file_content": "", "char_start": 4683, "char_end": 4715, "patch_content": "    return numInsertionBatches;\n", "patch_id": "airbnb/epoxy/EvVagM55", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOpHelper.java", "_extra": {"func_start": 4683, "func_end": 4715}}
{"file_content": "", "char_start": 4751, "char_end": 4781, "patch_content": "    return numRemovalBatches;\n", "patch_id": "airbnb/epoxy/bdq3W6zx", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-adapter/src/main/java/com/airbnb/epoxy/UpdateOpHelper.java", "_extra": {"func_start": 4751, "func_end": 4781}}
{"file_content": "", "char_start": 306, "char_end": 326, "patch_content": "    super(context);\n", "patch_id": "airbnb/epoxy/7Tzg5aiF", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-sample/src/main/java/com/airbnb/epoxy/sample/models/BaseView.java", "_extra": {"func_start": 306, "func_end": 326}}
{"file_content": "", "char_start": 538, "char_end": 558, "patch_content": "    super(context);\n", "patch_id": "airbnb/epoxy/tuNsTT2K", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-sample/src/main/java/com/airbnb/epoxy/sample/views/GridCarousel.java", "_extra": {"func_start": 538, "func_end": 558}}
{"file_content": "", "char_start": 648, "char_end": 679, "patch_content": "    super(context);\n    init();", "patch_id": "airbnb/epoxy/7xNo3Eqo", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-sample/src/main/java/com/airbnb/epoxy/sample/views/HeaderView.java", "_extra": {"func_start": 648, "func_end": 680}}
{"file_content": "", "char_start": 1931, "char_end": 2109, "patch_content": "    ViewDataBinding binding = DataBindingUtil.inflate(layoutInflater, getViewType(), parent, false);\n    View view = binding.getRoot();\n    view.setTag(binding);\n    return view;", "patch_id": "airbnb/epoxy/VaJut7Ax", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-databinding/src/main/java/com/airbnb/epoxy/DataBindingEpoxyModel.java", "_extra": {"func_start": 1853, "func_end": 2110}}
{"file_content": "", "char_start": 2232, "char_end": 2281, "patch_content": "    holder.dataBinding.executePendingBindings();\n", "patch_id": "airbnb/epoxy/3KmBmmup", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-databinding/src/main/java/com/airbnb/epoxy/DataBindingEpoxyModel.java", "_extra": {"func_start": 2183, "func_end": 2281}}
{"file_content": "", "char_start": 2399, "char_end": 2518, "patch_content": "    setDataBindingVariables(holder.dataBinding, previouslyBoundModel);\n    holder.dataBinding.executePendingBindings();", "patch_id": "airbnb/epoxy/mAWWwf7R", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-databinding/src/main/java/com/airbnb/epoxy/DataBindingEpoxyModel.java", "_extra": {"func_start": 2399, "func_end": 2519}}
{"file_content": "", "char_start": 2232, "char_end": 2281, "patch_content": "    holder.dataBinding.executePendingBindings();\n", "patch_id": "airbnb/epoxy/ZgLAop4k", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-databinding/src/main/java/com/airbnb/epoxy/DataBindingEpoxyModel.java", "_extra": {"func_start": 2624, "func_end": 2732}}
{"file_content": "", "char_start": 4253, "char_end": 4289, "patch_content": "    return new DataBindingHolder();\n", "patch_id": "airbnb/epoxy/jbivJBE5", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-databinding/src/main/java/com/airbnb/epoxy/DataBindingEpoxyModel.java", "_extra": {"func_start": 4253, "func_end": 4289}}
{"file_content": "", "char_start": 4444, "char_end": 4470, "patch_content": "      return dataBinding;\n", "patch_id": "airbnb/epoxy/LyppVTV7", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-databinding/src/main/java/com/airbnb/epoxy/DataBindingEpoxyModel.java", "_extra": {"func_start": 4444, "func_end": 4470}}
{"file_content": "", "char_start": 4545, "char_end": 4602, "patch_content": "      dataBinding = (ViewDataBinding) itemView.getTag();\n", "patch_id": "airbnb/epoxy/ZXVJEaqX", "repository": "airbnb/epoxy", "commit_sha": "9a43af4d043e71f29afe4cbeadf8c008740a5184", "file_name": "epoxy-databinding/src/main/java/com/airbnb/epoxy/DataBindingEpoxyModel.java", "_extra": {"func_start": 4545, "func_end": 4602}}

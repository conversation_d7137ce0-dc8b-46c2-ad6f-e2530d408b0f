{"file_content": "", "char_start": 1866, "char_end": 1897, "patch_content": "", "patch_id": "seata/baaa8ba6e64d6657", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-druid/src/main/java/io/seata/sqlparser/druid/sqlserver/SqlServerDeleteRecognizer.java", "_extra": {"func_start": 1866, "func_end": 1897, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15814, "char_end": 15846, "patch_content": "", "patch_id": "seata/5f466c02d62e12f7", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/session/GlobalSession.java", "_extra": {"func_start": 15814, "func_end": 15846, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2038, "char_end": 2233, "patch_content": "", "patch_id": "seata/3d989ff6585d7ca4", "repository": "seata/seata", "commit_sha": "", "file_name": "integration-tx-api/src/main/java/io/seata/integration/tx/api/remoting/parser/DefaultRemotingParser.java", "_extra": {"func_start": 2038, "func_end": 2300, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3958, "char_end": 4153, "patch_content": "", "patch_id": "seata/2ef93129c6e00dac", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/SqlGenerateUtils.java", "_extra": {"func_start": 3816, "func_end": 4208, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23639, "char_end": 23681, "patch_content": "", "patch_id": "seata/860a313c0f22f6b7", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/storage/db/store/LogStoreDataBaseDAO.java", "_extra": {"func_start": 23639, "func_end": 23681, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1036, "char_end": 1057, "patch_content": "", "patch_id": "seata/6ea72c7831df8318", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/auth/DefaultCheckAuthHandler.java", "_extra": {"func_start": 1036, "func_end": 1057, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3115, "char_end": 3175, "patch_content": "", "patch_id": "seata/07a92daeb51227b3", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/util/LowerCaseLinkHashMap.java", "_extra": {"func_start": 3115, "func_end": 3175, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11955, "char_end": 12086, "patch_content": "", "patch_id": "seata/b93554ee0cc9a6de", "repository": "seata/seata", "commit_sha": "", "file_name": "config/seata-config-nacos/src/main/java/io/seata/config/nacos/NacosConfiguration.java", "_extra": {"func_start": 11955, "func_end": 12086, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4526, "char_end": 4623, "patch_content": "", "patch_id": "seata/d0e65524d8592048", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/AbstractPreparedStatementProxy.java", "_extra": {"func_start": 4526, "func_end": 4623, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13469, "char_end": 13607, "patch_content": "", "patch_id": "seata/3c58df860c5fe2c1", "repository": "seata/seata", "commit_sha": "", "file_name": "spring/src/main/java/io/seata/spring/annotation/GlobalTransactionScanner.java", "_extra": {"func_start": 12966, "func_end": 13750, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3612, "char_end": 3635, "patch_content": "", "patch_id": "seata/16df84a27ce85b98", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/sql/struct/TableRecords.java", "_extra": {"func_start": 3612, "func_end": 3635, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4120, "char_end": 4223, "patch_content": "", "patch_id": "seata/e80359545ee66ab1", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/lock/AbstractLockManager.java", "_extra": {"func_start": 3924, "func_end": 4424, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1534, "char_end": 1588, "patch_content": "", "patch_id": "seata/5441fcab9e3910e6", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine/src/main/java/io/seata/saga/engine/expression/ExpressionFactoryManager.java", "_extra": {"func_start": 1534, "func_end": 1588, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1888, "char_end": 1947, "patch_content": "", "patch_id": "seata/33dfce4356c2af78", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-druid/src/main/java/io/seata/sqlparser/druid/DruidDelegatingDbTypeParser.java", "_extra": {"func_start": 1888, "func_end": 1947, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2561, "char_end": 2620, "patch_content": "", "patch_id": "seata/92a6905e9a9c2494", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/exception/FrameworkException.java", "_extra": {"func_start": 2561, "func_end": 2620, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3910, "char_end": 3931, "patch_content": "", "patch_id": "seata/f65e7bedc7747089", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-druid/src/main/java/io/seata/sqlparser/druid/sqlserver/SqlServerDeleteRecognizer.java", "_extra": {"func_start": 4068, "func_end": 4089, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6987, "char_end": 7042, "patch_content": "", "patch_id": "seata/aeedc0e228bebed5", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/DataCompareUtils.java", "_extra": {"func_start": 6924, "func_end": 7104, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5090, "char_end": 5195, "patch_content": "", "patch_id": "seata/2359d3778df8ccdb", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/store/db/sql/log/MysqlLogStoreSqls.java", "_extra": {"func_start": 5090, "func_end": 5195, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2167, "char_end": 2219, "patch_content": "", "patch_id": "seata/8e5285f511abc8b7", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-statelang/src/main/java/io/seata/saga/statelang/domain/impl/StateInstanceImpl.java", "_extra": {"func_start": 2167, "func_end": 2219, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 932, "char_end": 975, "patch_content": "", "patch_id": "seata/9b1623721eba413f", "repository": "seata/seata", "commit_sha": "", "file_name": "compressor/seata-compressor-7z/src/main/java/io/seata/compressor/sevenz/SevenZCompressor.java", "_extra": {"func_start": 932, "func_end": 975, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4126, "char_end": 4204, "patch_content": "", "patch_id": "seata/35615c33b33f79da", "repository": "seata/seata", "commit_sha": "", "file_name": "spring/src/main/java/io/seata/spring/util/OrderUtil.java", "_extra": {"func_start": 4126, "func_end": 4204, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4513, "char_end": 4698, "patch_content": "", "patch_id": "seata/5a5ca9b60213cae0", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine-store/src/main/java/io/seata/saga/engine/store/db/DbStateLangStore.java", "_extra": {"func_start": 4106, "func_end": 4893, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10703, "char_end": 10745, "patch_content": "", "patch_id": "seata/738c9d8665092c8f", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/xa/ConnectionProxyXA.java", "_extra": {"func_start": 10589, "func_end": 10745, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1944, "char_end": 1971, "patch_content": "", "patch_id": "seata/bfc6823878d8b0a9", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/protocol/RpcMessage.java", "_extra": {"func_start": 1944, "func_end": 1971, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11428, "char_end": 11496, "patch_content": "", "patch_id": "seata/c9267c763c86b1c8", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/loader/EnhancedServiceLoader.java", "_extra": {"func_start": 11428, "func_end": 11496, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5870, "char_end": 5924, "patch_content": "", "patch_id": "seata/2cf9e8357165cd35", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/DataCompareUtils.java", "_extra": {"func_start": 5776, "func_end": 6596, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5314, "char_end": 5340, "patch_content": "", "patch_id": "seata/289fbcb642c98f96", "repository": "seata/seata", "commit_sha": "", "file_name": "integration-tx-api/src/main/java/io/seata/rm/tcc/api/BusinessActionContext.java", "_extra": {"func_start": 5314, "func_end": 5340, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1458, "char_end": 1522, "patch_content": "", "patch_id": "seata/e0e96f693acdb818", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/serializer/SerializerServiceLoader.java", "_extra": {"func_start": 1458, "func_end": 1951, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1667, "char_end": 1738, "patch_content": "", "patch_id": "seata/072dbfda3d29b88d", "repository": "seata/seata", "commit_sha": "", "file_name": "config/seata-config-core/src/main/java/io/seata/config/AbstractConfiguration.java", "_extra": {"func_start": 1667, "func_end": 1738, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12830, "char_end": 13019, "patch_content": "", "patch_id": "seata/7506466ffe1f18fb", "repository": "seata/seata", "commit_sha": "", "file_name": "discovery/seata-discovery-redis/src/main/java/io/seata/discovery/registry/redis/RedisRegistryServiceImpl.java", "_extra": {"func_start": 12324, "func_end": 13409, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2266, "char_end": 2287, "patch_content": "", "patch_id": "seata/f65e7bedc7747089", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-antlr/src/main/java/io/seata/sqlparser/antlr/mysql/AntlrMySQLSelectRecognizer.java", "_extra": {"func_start": 2508, "func_end": 2529, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2978, "char_end": 3027, "patch_content": "", "patch_id": "seata/13ab8cbeba2c185d", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-antlr/src/main/java/io/seata/sqlparser/antlr/mysql/listener/SelectSpecificationSqlListener.java", "_extra": {"func_start": 2710, "func_end": 3028, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4123, "char_end": 4258, "patch_content": "", "patch_id": "seata/5610eeaf04d30121", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-druid/src/main/java/io/seata/sqlparser/druid/oracle/OracleInsertRecognizer.java", "_extra": {"func_start": 3768, "func_end": 5361, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5766, "char_end": 5929, "patch_content": "", "patch_id": "seata/7bca8892556cf786", "repository": "seata/seata", "commit_sha": "", "file_name": "config/seata-config-core/src/main/java/io/seata/config/ConfigurationCache.java", "_extra": {"func_start": 4306, "func_end": 6074, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3862, "char_end": 3952, "patch_content": "", "patch_id": "seata/2450a1d2c7c62801", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/protocol/AbstractIdentifyRequest.java", "_extra": {"func_start": 3522, "func_end": 3983, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14001, "char_end": 14129, "patch_content": "", "patch_id": "seata/e578857b47b0bf79", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/undo/parser/JacksonUndoLogParser.java", "_extra": {"func_start": 13758, "func_end": 14279, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1873, "char_end": 1894, "patch_content": "", "patch_id": "seata/9471844ccb394ca5", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-druid/src/main/java/io/seata/sqlparser/druid/oracle/OracleOperateRecognizerHolder.java", "_extra": {"func_start": 1708, "func_end": 1894, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1201, "char_end": 1227, "patch_content": "", "patch_id": "seata/6d1bb61725f084f2", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/protocol/HeartbeatMessage.java", "_extra": {"func_start": 1201, "func_end": 1227, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3190, "char_end": 3232, "patch_content": "", "patch_id": "seata/15814c438197168c", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/ServerRunner.java", "_extra": {"func_start": 3190, "func_end": 3232, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2417, "char_end": 2518, "patch_content": "", "patch_id": "seata/6e99ed4ff88a820e", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/protocol/RegisterTMRequest.java", "_extra": {"func_start": 1883, "func_end": 2705, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2441, "char_end": 2506, "patch_content": "", "patch_id": "seata/b0fe88dae0026239", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/storage/SessionConverter.java", "_extra": {"func_start": 2441, "func_end": 2506, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2495, "char_end": 2528, "patch_content": "", "patch_id": "seata/409fa37c64ebb1fa", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-statelang/src/main/java/io/seata/saga/statelang/domain/impl/StateMachineImpl.java", "_extra": {"func_start": 2495, "func_end": 2528, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5966, "char_end": 6031, "patch_content": "", "patch_id": "seata/ccee9f4c3c1916e6", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/loader/EnhancedServiceLoader.java", "_extra": {"func_start": 5966, "func_end": 6031, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4591, "char_end": 4651, "patch_content": "", "patch_id": "seata/2a85bef3d730b4e6", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/DataSourceProxy.java", "_extra": {"func_start": 4519, "func_end": 4651, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16987, "char_end": 17075, "patch_content": "", "patch_id": "seata/12389982abb4c15f", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/storage/redis/store/RedisTransactionStoreManager.java", "_extra": {"func_start": 13185, "func_end": 17086, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1132, "char_end": 1157, "patch_content": "", "patch_id": "seata/5ffa3ef8d96f9a40", "repository": "seata/seata", "commit_sha": "", "file_name": "console/src/main/java/io/seata/console/param/BaseParam.java", "_extra": {"func_start": 1132, "func_end": 1157, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1724, "char_end": 1904, "patch_content": "", "patch_id": "seata/cdda2b4beb64e857", "repository": "seata/seata", "commit_sha": "", "file_name": "serializer/seata-serializer-protobuf/src/main/java/io/seata/serializer/protobuf/convertor/RegisterRMResponseConvertor.java", "_extra": {"func_start": 1419, "func_end": 2911, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2918, "char_end": 2977, "patch_content": "", "patch_id": "seata/61331533fbb67790", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/util/ArrayUtils.java", "_extra": {"func_start": 2526, "func_end": 2977, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5985, "char_end": 6014, "patch_content": "", "patch_id": "seata/49a33dc1b8c8c694", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/BaseDataSourceResource.java", "_extra": {"func_start": 5985, "func_end": 6014, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1246, "char_end": 1299, "patch_content": "", "patch_id": "seata/b5054915480443dc", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-druid/src/main/java/io/seata/sqlparser/druid/oracle/OracleOperateRecognizerHolder.java", "_extra": {"func_start": 1246, "func_end": 1299, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2370, "char_end": 2572, "patch_content": "", "patch_id": "seata/701b25c673046bbb", "repository": "seata/seata", "commit_sha": "", "file_name": "tcc/src/main/java/io/seata/rm/tcc/remoting/parser/LocalTCCRemotingParser.java", "_extra": {"func_start": 1533, "func_end": 2889, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1560, "char_end": 1604, "patch_content": "", "patch_id": "seata/5044b3993a15c8b3", "repository": "seata/seata", "commit_sha": "", "file_name": "tm/src/main/java/io/seata/tm/api/transaction/TransactionInfo.java", "_extra": {"func_start": 1560, "func_end": 1604, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1624, "char_end": 1754, "patch_content": "", "patch_id": "seata/245d4ebd60cfebb7", "repository": "seata/seata", "commit_sha": "", "file_name": "discovery/seata-discovery-core/src/main/java/io/seata/discovery/registry/FileRegistryServiceImpl.java", "_extra": {"func_start": 1624, "func_end": 1884, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3943, "char_end": 3965, "patch_content": "", "patch_id": "seata/ed140e3af721250b", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine/src/main/java/io/seata/saga/engine/pcext/utils/CompensationHolder.java", "_extra": {"func_start": 3051, "func_end": 5246, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2313, "char_end": 2439, "patch_content": "", "patch_id": "seata/a2950047db8bb25d", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-druid/src/main/java/io/seata/sqlparser/druid/mysql/MySQLInsertRecognizer.java", "_extra": {"func_start": 2313, "func_end": 2439, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1971, "char_end": 2002, "patch_content": "", "patch_id": "seata/baaa8ba6e64d6657", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-druid/src/main/java/io/seata/sqlparser/druid/mysql/MySQLDeleteRecognizer.java", "_extra": {"func_start": 1971, "func_end": 2002, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1145, "char_end": 1180, "patch_content": "", "patch_id": "seata/d9b6bfca91c04455", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/util/CycleDependencyHandler.java", "_extra": {"func_start": 1145, "func_end": 1180, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9332, "char_end": 9369, "patch_content": "", "patch_id": "seata/1f0506c4739a60a1", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/rpc/netty/NettyClientConfig.java", "_extra": {"func_start": 9332, "func_end": 9369, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23149, "char_end": 23367, "patch_content": "", "patch_id": "seata/f2e547ab0958507d", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/storage/redis/store/RedisTransactionStoreManager.java", "_extra": {"func_start": 22688, "func_end": 23940, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3320, "char_end": 3446, "patch_content": "", "patch_id": "seata/bb57e45640257936", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/rpc/netty/NettyRemotingServer.java", "_extra": {"func_start": 3282, "func_end": 3472, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1591, "char_end": 1641, "patch_content": "", "patch_id": "seata/4057d05209ecdb8a", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine/src/main/java/io/seata/saga/engine/pcext/StateInstruction.java", "_extra": {"func_start": 3755, "func_end": 3805, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1661, "char_end": 1714, "patch_content": "", "patch_id": "seata/012870cf0876cb1b", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/rpc/ShutdownHook.java", "_extra": {"func_start": 1661, "func_end": 1714, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1640, "char_end": 1683, "patch_content": "", "patch_id": "seata/be57535ee93f2704", "repository": "seata/seata", "commit_sha": "", "file_name": "config/seata-config-core/src/main/java/io/seata/config/file/SimpleFileConfig.java", "_extra": {"func_start": 1640, "func_end": 1683, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15527, "char_end": 15613, "patch_content": "", "patch_id": "seata/414b64eb552aefc5", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/exec/mysql/MySQLUpdateJoinExecutor.java", "_extra": {"func_start": 15527, "func_end": 15613, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20275, "char_end": 20315, "patch_content": "", "patch_id": "seata/d18530cdbb62aef2", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/storage/file/store/FileTransactionStoreManager.java", "_extra": {"func_start": 20275, "func_end": 20315, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2550, "char_end": 2561, "patch_content": "", "patch_id": "seata/bc77e9c89fe42c7c", "repository": "seata/seata", "commit_sha": "", "file_name": "tm/src/main/java/io/seata/tm/api/GlobalTransactionContext.java", "_extra": {"func_start": 2225, "func_end": 2561, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1786, "char_end": 1841, "patch_content": "", "patch_id": "seata/2288ca944291b9db", "repository": "seata/seata", "commit_sha": "", "file_name": "seata-spring-autoconfigure/seata-spring-autoconfigure-core/src/main/java/io/seata/spring/boot/autoconfigure/SeataCoreAutoConfiguration.java", "_extra": {"func_start": 1786, "func_end": 1841, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7130, "char_end": 7151, "patch_content": "", "patch_id": "seata/6ea72c7831df8318", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/coordinator/AbstractCore.java", "_extra": {"func_start": 10426, "func_end": 10447, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2658, "char_end": 2746, "patch_content": "", "patch_id": "seata/a20dcd9442597c27", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/undo/mysql/MySQLUndoLogManager.java", "_extra": {"func_start": 2341, "func_end": 2959, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15369, "char_end": 15392, "patch_content": "", "patch_id": "seata/e0e7289de2db84f1", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/util/ReflectionUtil.java", "_extra": {"func_start": 14385, "func_end": 15392, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2213, "char_end": 2259, "patch_content": "", "patch_id": "seata/b382b2778bd5a5ed", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-core/src/main/java/io/seata/sqlparser/struct/IndexMeta.java", "_extra": {"func_start": 2213, "func_end": 2259, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 35763, "char_end": 35900, "patch_content": "", "patch_id": "seata/20f8aab522e5fea5", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine-store/src/main/java/io/seata/saga/engine/store/db/DbAndReportTcStateLogStore.java", "_extra": {"func_start": 35726, "func_end": 36521, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3143, "char_end": 3166, "patch_content": "", "patch_id": "seata/e4be4065d9c3d981", "repository": "seata/seata", "commit_sha": "", "file_name": "integration/http/src/main/java/io/seata/integration/http/DefaultHttpExecutor.java", "_extra": {"func_start": 3143, "func_end": 3166, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3375, "char_end": 3468, "patch_content": "", "patch_id": "seata/43a61a78eaa460ef", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine/src/main/java/io/seata/saga/engine/impl/ProcessCtrlStateMachineEngine.java", "_extra": {"func_start": 3375, "func_end": 3469, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 25051, "char_end": 25184, "patch_content": "", "patch_id": "seata/fcf7f2758631fb66", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine-store/src/main/java/io/seata/saga/engine/store/db/DbAndReportTcStateLogStore.java", "_extra": {"func_start": 24669, "func_end": 25666, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1936, "char_end": 1980, "patch_content": "", "patch_id": "seata/82badc96f22cb967", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-druid/src/main/java/io/seata/sqlparser/druid/mysql/MySQLSelectForUpdateRecognizer.java", "_extra": {"func_start": 1908, "func_end": 1980, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7173, "char_end": 7220, "patch_content": "", "patch_id": "seata/a12ead931dbe5f7a", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine-store/src/main/java/io/seata/saga/engine/store/db/StateLogStoreSqls.java", "_extra": {"func_start": 7173, "func_end": 7220, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9774, "char_end": 9946, "patch_content": "", "patch_id": "seata/73f34a7a9a74c6af", "repository": "seata/seata", "commit_sha": "", "file_name": "config/seata-config-etcd3/src/main/java/io/seata/config/etcd3/EtcdConfiguration.java", "_extra": {"func_start": 9653, "func_end": 9994, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4370, "char_end": 4472, "patch_content": "", "patch_id": "seata/6fbdbf3d2f02f427", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/AbstractConnectionProxy.java", "_extra": {"func_start": 4370, "func_end": 4472, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7943, "char_end": 7989, "patch_content": "", "patch_id": "seata/849ff38c1dd24aaf", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/xa/StatementProxyXA.java", "_extra": {"func_start": 7943, "func_end": 7989, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14351, "char_end": 14401, "patch_content": "", "patch_id": "seata/26a7ea2f92b7ed93", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/loader/EnhancedServiceLoader.java", "_extra": {"func_start": 14351, "func_end": 14401, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2271, "char_end": 2304, "patch_content": "", "patch_id": "seata/51183060d9a35255", "repository": "seata/seata", "commit_sha": "", "file_name": "integration-tx-api/src/main/java/io/seata/integration/tx/api/remoting/RemotingDesc.java", "_extra": {"func_start": 2271, "func_end": 2304, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2088, "char_end": 2130, "patch_content": "", "patch_id": "seata/0068ba2e062082a0", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-statelang/src/main/java/io/seata/saga/statelang/domain/impl/ChoiceStateImpl.java", "_extra": {"func_start": 2088, "func_end": 2130, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2810, "char_end": 2867, "patch_content": "", "patch_id": "seata/9ce3a659fdc47e73", "repository": "seata/seata", "commit_sha": "", "file_name": "seata-spring-autoconfigure/seata-spring-autoconfigure-core/src/main/java/io/seata/spring/boot/autoconfigure/properties/config/ConfigNacosProperties.java", "_extra": {"func_start": 2810, "func_end": 2867, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15766, "char_end": 15933, "patch_content": "", "patch_id": "seata/450e613a6d2616ab", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/loader/EnhancedServiceLoader.java", "_extra": {"func_start": 15301, "func_end": 16229, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1067, "char_end": 1106, "patch_content": "", "patch_id": "seata/a0f6e94b3993f223", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-antlr/src/main/java/io/seata/sqlparser/antlr/mysql/stream/ANTLRNoCaseStringStream.java", "_extra": {"func_start": 991, "func_end": 1163, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2983, "char_end": 3102, "patch_content": "", "patch_id": "seata/f80da188dfebb60f", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine-store/src/main/java/io/seata/saga/engine/pcext/interceptors/InSagaBranchHandlerInterceptor.java", "_extra": {"func_start": 2983, "func_end": 3243, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20572, "char_end": 20747, "patch_content": "", "patch_id": "seata/fea3e5c986c39b23", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/loader/EnhancedServiceLoader.java", "_extra": {"func_start": 20013, "func_end": 23122, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2923, "char_end": 3102, "patch_content": "", "patch_id": "seata/7775952fee13bcb8", "repository": "seata/seata", "commit_sha": "", "file_name": "discovery/seata-discovery-core/src/main/java/io/seata/discovery/loadbalance/ConsistentHashLoadBalance.java", "_extra": {"func_start": 2905, "func_end": 3117, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2788, "char_end": 2964, "patch_content": "", "patch_id": "seata/498fb1c2fc2da0cc", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/StatementProxy.java", "_extra": {"func_start": 2788, "func_end": 2964, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3178, "char_end": 3241, "patch_content": "", "patch_id": "seata/09d41eee7ffbe54d", "repository": "seata/seata", "commit_sha": "", "file_name": "tm/src/main/java/io/seata/tm/api/DefaultFailureHandlerImpl.java", "_extra": {"func_start": 3178, "func_end": 3242, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10849, "char_end": 10951, "patch_content": "", "patch_id": "seata/0eb1b6d48d1e9c73", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/ConnectionContext.java", "_extra": {"func_start": 10757, "func_end": 10951, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3089, "char_end": 3241, "patch_content": "", "patch_id": "seata/b4631bd101d244fa", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/console/impl/redis/GlobalLockRedisServiceImpl.java", "_extra": {"func_start": 2153, "func_end": 3242, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9680, "char_end": 9774, "patch_content": "", "patch_id": "seata/4cbaaa01bbac0e6e", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/storage/file/store/FileTransactionStoreManager.java", "_extra": {"func_start": 8528, "func_end": 9848, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5249, "char_end": 5355, "patch_content": "", "patch_id": "seata/7cc2b6fe96641630", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine/src/main/java/io/seata/saga/engine/pcext/handlers/ServiceTaskStateHandler.java", "_extra": {"func_start": 2444, "func_end": 5613, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2037, "char_end": 2154, "patch_content": "", "patch_id": "seata/966124dba77d83a2", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine-store/src/main/java/io/seata/saga/engine/store/db/DbStateLangStore.java", "_extra": {"func_start": 1924, "func_end": 2218, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4030, "char_end": 4071, "patch_content": "", "patch_id": "seata/142211951b2d994f", "repository": "seata/seata", "commit_sha": "", "file_name": "seata-spring-autoconfigure/seata-spring-autoconfigure-core/src/main/java/io/seata/spring/boot/autoconfigure/properties/ThreadFactoryProperties.java", "_extra": {"func_start": 4030, "func_end": 4071, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 35161, "char_end": 35211, "patch_content": "", "patch_id": "seata/33c0455847cbda24", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/storage/redis/store/RedisTransactionStoreManager.java", "_extra": {"func_start": 35161, "func_end": 35211, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2821, "char_end": 2875, "patch_content": "", "patch_id": "seata/92c195c8f39273df", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/storage/db/store/LogStoreDataBaseDAO.java", "_extra": {"func_start": 22989, "func_end": 23043, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2715, "char_end": 2844, "patch_content": "", "patch_id": "seata/1ca9874bbc82a9bd", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-druid/src/main/java/io/seata/sqlparser/druid/mysql/MySQLInsertRecognizer.java", "_extra": {"func_start": 2600, "func_end": 2975, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8644, "char_end": 8692, "patch_content": "", "patch_id": "seata/77b95f403a07283c", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/util/StringUtils.java", "_extra": {"func_start": 8644, "func_end": 8692, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2846, "char_end": 2856, "patch_content": "", "patch_id": "seata/1f096ecbbde38e24", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine/src/main/java/io/seata/saga/engine/impl/ProcessCtrlStateMachineEngine.java", "_extra": {"func_start": 22203, "func_end": 25566, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2266, "char_end": 2287, "patch_content": "", "patch_id": "seata/f65e7bedc7747089", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-antlr/src/main/java/io/seata/sqlparser/antlr/mysql/AntlrMySQLSelectRecognizer.java", "_extra": {"func_start": 2266, "func_end": 2287, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18103, "char_end": 18137, "patch_content": "", "patch_id": "seata/39de4d491cb671d0", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/storage/file/store/FileTransactionStoreManager.java", "_extra": {"func_start": 18103, "func_end": 18137, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2879, "char_end": 2923, "patch_content": "", "patch_id": "seata/48c3f26163c8cb18", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/undo/postgresql/PostgresqlUndoDeleteExecutor.java", "_extra": {"func_start": 2879, "func_end": 2923, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2047, "char_end": 2081, "patch_content": "", "patch_id": "seata/b6bf6fade0f6676b", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-statelang/src/main/java/io/seata/saga/statelang/domain/impl/StateInstanceImpl.java", "_extra": {"func_start": 2047, "func_end": 2081, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9229, "char_end": 9250, "patch_content": "", "patch_id": "seata/346da341dcff301e", "repository": "seata/seata", "commit_sha": "", "file_name": "tm/src/main/java/io/seata/tm/api/DefaultGlobalTransaction.java", "_extra": {"func_start": 9229, "func_end": 9250, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1213, "char_end": 1260, "patch_content": "", "patch_id": "seata/049c417cb626d531", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/protocol/RegisterRMResponse.java", "_extra": {"func_start": 1213, "func_end": 1260, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1752, "char_end": 1827, "patch_content": "", "patch_id": "seata/f1be46863bac37a9", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-druid/src/main/java/io/seata/sqlparser/druid/oracle/OracleDeleteRecognizer.java", "_extra": {"func_start": 1752, "func_end": 1827, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6323, "char_end": 6405, "patch_content": "", "patch_id": "seata/92aa253f19b4e8f9", "repository": "seata/seata", "commit_sha": "", "file_name": "spring/src/main/java/io/seata/rm/fence/SpringFenceHandler.java", "_extra": {"func_start": 5858, "func_end": 7492, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1061, "char_end": 1110, "patch_content": "", "patch_id": "seata/555489d87682bec9", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/protocol/transaction/GlobalCommitRequest.java", "_extra": {"func_start": 1061, "func_end": 1110, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3509, "char_end": 3545, "patch_content": "", "patch_id": "seata/1a994e602f68e0ab", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/store/GlobalTransactionDO.java", "_extra": {"func_start": 3509, "func_end": 3545, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5917, "char_end": 5940, "patch_content": "", "patch_id": "seata/80d24efd308d9556", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/BaseDataSourceResource.java", "_extra": {"func_start": 5917, "func_end": 5940, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7790, "char_end": 7820, "patch_content": "", "patch_id": "seata/f7425a15fb11cfb5", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/rpc/netty/NettyClientConfig.java", "_extra": {"func_start": 7790, "func_end": 7820, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1775, "char_end": 1799, "patch_content": "", "patch_id": "seata/392d297b1987af8f", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-statelang/src/main/java/io/seata/saga/statelang/domain/impl/StateMachineImpl.java", "_extra": {"func_start": 1775, "func_end": 1799, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1271, "char_end": 1359, "patch_content": "", "patch_id": "seata/153269b89a4df179", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-antlr/src/main/java/io/seata/sqlparser/antlr/mysql/listener/UpdateSpecificationSqlListener.java", "_extra": {"func_start": 1270, "func_end": 1359, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1598, "char_end": 1642, "patch_content": "", "patch_id": "seata/7164690a6e5bbce0", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-statelang/src/main/java/io/seata/saga/statelang/parser/utils/ResourceUtil.java", "_extra": {"func_start": 1436, "func_end": 1642, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6095, "char_end": 6150, "patch_content": "", "patch_id": "seata/cca8e2e92fef6d1c", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/session/BranchSession.java", "_extra": {"func_start": 6095, "func_end": 6150, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1597, "char_end": 1637, "patch_content": "", "patch_id": "seata/765a12d5e5b63a8a", "repository": "seata/seata", "commit_sha": "", "file_name": "seata-spring-autoconfigure/seata-spring-autoconfigure-server/src/main/java/io/seata/spring/boot/autoconfigure/properties/server/session/SessionProperties.java", "_extra": {"func_start": 1597, "func_end": 1637, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2931, "char_end": 2968, "patch_content": "", "patch_id": "seata/90b125f331266100", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-antlr/src/main/java/io/seata/sqlparser/antlr/mysql/AntlrMySQLDeleteRecognizer.java", "_extra": {"func_start": 2931, "func_end": 2968, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5802, "char_end": 6024, "patch_content": "", "patch_id": "seata/0933c360c83ea789", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/transaction/saga/SagaCore.java", "_extra": {"func_start": 4536, "func_end": 7393, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8810, "char_end": 8867, "patch_content": "", "patch_id": "seata/bf59868838acc25a", "repository": "seata/seata", "commit_sha": "", "file_name": "common/src/main/java/io/seata/common/util/NetUtil.java", "_extra": {"func_start": 8762, "func_end": 9053, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2579, "char_end": 2600, "patch_content": "", "patch_id": "seata/1846886e95b2b34c", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/sql/struct/TableRecords.java", "_extra": {"func_start": 2579, "func_end": 2600, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3219, "char_end": 3435, "patch_content": "", "patch_id": "seata/a45455bdd3a5df2a", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/util/JdbcUtils.java", "_extra": {"func_start": 3071, "func_end": 4111, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3808, "char_end": 3842, "patch_content": "", "patch_id": "seata/3bbf118dabb9bc42", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/store/BranchTransactionDO.java", "_extra": {"func_start": 3808, "func_end": 3842, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1085, "char_end": 1132, "patch_content": "", "patch_id": "seata/3bcae3189b4e7cb6", "repository": "seata/seata", "commit_sha": "", "file_name": "tm/src/main/java/io/seata/tm/api/GlobalTransactionContext.java", "_extra": {"func_start": 1085, "func_end": 1132, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2196, "char_end": 2220, "patch_content": "", "patch_id": "seata/8f9c5c4bf9c1ff37", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/rpc/netty/NettyPoolKey.java", "_extra": {"func_start": 2196, "func_end": 2220, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1320, "char_end": 1347, "patch_content": "", "patch_id": "seata/68b3264c7d771427", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/protocol/transaction/UndoLogDeleteRequest.java", "_extra": {"func_start": 1320, "func_end": 1347, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1500, "char_end": 1528, "patch_content": "", "patch_id": "seata/b1a903d73d3f5a41", "repository": "seata/seata", "commit_sha": "", "file_name": "seata-spring-autoconfigure/seata-spring-autoconfigure-client/src/main/java/io/seata/spring/boot/autoconfigure/properties/SagaAsyncThreadPoolProperties.java", "_extra": {"func_start": 1500, "func_end": 1528, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1440, "char_end": 1494, "patch_content": "", "patch_id": "seata/d24ca595a695c169", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/transaction/xa/XACore.java", "_extra": {"func_start": 1360, "func_end": 1505, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 998, "char_end": 1019, "patch_content": "", "patch_id": "seata/e2f07305bfd7c9a4", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/exception/TransactionException.java", "_extra": {"func_start": 998, "func_end": 1019, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3101, "char_end": 3237, "patch_content": "", "patch_id": "seata/205322a0be929d97", "repository": "seata/seata", "commit_sha": "", "file_name": "serializer/seata-serializer-protobuf/src/main/java/io/seata/serializer/protobuf/convertor/RegisterRMResponseConvertor.java", "_extra": {"func_start": 3027, "func_end": 3781, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6282, "char_end": 6307, "patch_content": "", "patch_id": "seata/ba64ceb144e11ae2", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-core/src/main/java/io/seata/sqlparser/struct/ColumnMeta.java", "_extra": {"func_start": 6282, "func_end": 6307, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8318, "char_end": 8559, "patch_content": "", "patch_id": "seata/9d94700477067472", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/coordinator/AbstractCore.java", "_extra": {"func_start": 8317, "func_end": 8560, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7615, "char_end": 7656, "patch_content": "", "patch_id": "seata/2fe810561c4af5a9", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/undo/parser/ProtostuffUndoLogParser.java", "_extra": {"func_start": 7615, "func_end": 7656, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3413, "char_end": 3469, "patch_content": "", "patch_id": "seata/a3d529ab66a66da2", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/AbstractTCInboundHandler.java", "_extra": {"func_start": 10872, "func_end": 11680, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5013, "char_end": 5069, "patch_content": "", "patch_id": "seata/9ce3a659fdc47e73", "repository": "seata/seata", "commit_sha": "", "file_name": "seata-spring-autoconfigure/seata-spring-autoconfigure-client/src/main/java/io/seata/spring/boot/autoconfigure/properties/SeataProperties.java", "_extra": {"func_start": 5013, "func_end": 5070, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12791, "char_end": 12892, "patch_content": "", "patch_id": "seata/090dfe98ee9f3f33", "repository": "seata/seata", "commit_sha": "", "file_name": "discovery/seata-discovery-zk/src/main/java/io/seata/discovery/registry/zk/ZookeeperRegisterServiceImpl.java", "_extra": {"func_start": 12791, "func_end": 12892, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1033, "char_end": 1106, "patch_content": "", "patch_id": "seata/2df761fb48f6e373", "repository": "seata/seata", "commit_sha": "", "file_name": "integration-tx-api/src/main/java/io/seata/integration/tx/api/remoting/parser/AbstractedRemotingParser.java", "_extra": {"func_start": 1033, "func_end": 1106, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8890, "char_end": 8928, "patch_content": "", "patch_id": "seata/28eaa9a70f57aa40", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-core/src/main/java/io/seata/sqlparser/struct/ColumnMeta.java", "_extra": {"func_start": 8890, "func_end": 8928, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2113, "char_end": 2137, "patch_content": "", "patch_id": "seata/b679ed54d5c9c03c", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/protocol/AbstractIdentifyRequest.java", "_extra": {"func_start": 2113, "func_end": 2137, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6996, "char_end": 7109, "patch_content": "", "patch_id": "seata/541a6ad16fa38e80", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/AbstractPreparedStatementProxy.java", "_extra": {"func_start": 6996, "func_end": 7109, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1170, "char_end": 1206, "patch_content": "", "patch_id": "seata/ef39ab031f5ab8f3", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-statelang/src/main/java/io/seata/saga/statelang/domain/impl/FailEndStateImpl.java", "_extra": {"func_start": 1170, "func_end": 1206, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1437, "char_end": 1630, "patch_content": "", "patch_id": "seata/26c9efe37a1dcd48", "repository": "seata/seata", "commit_sha": "", "file_name": "console/src/main/java/io/seata/console/config/JacksonConfig.java", "_extra": {"func_start": 1307, "func_end": 1816, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2080, "char_end": 2172, "patch_content": "", "patch_id": "seata/ab52754203f1186e", "repository": "seata/seata", "commit_sha": "", "file_name": "core/src/main/java/io/seata/core/rpc/processor/client/RmBranchCommitProcessor.java", "_extra": {"func_start": 1838, "func_end": 2172, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2761, "char_end": 2827, "patch_content": "", "patch_id": "seata/9cc2af0c8d4c0b65", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/exec/AbstractDMLBaseExecutor.java", "_extra": {"func_start": 2761, "func_end": 2827, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6285, "char_end": 6324, "patch_content": "", "patch_id": "seata/b28fa62dc395792a", "repository": "seata/seata", "commit_sha": "", "file_name": "saga/seata-saga-engine/src/main/java/io/seata/saga/engine/pcext/utils/CompensationHolder.java", "_extra": {"func_start": 6285, "func_end": 6324, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3064, "char_end": 3099, "patch_content": "", "patch_id": "seata/69bcc9a5f117fb67", "repository": "seata/seata", "commit_sha": "", "file_name": "seata-spring-autoconfigure/seata-spring-autoconfigure-client/src/main/java/io/seata/spring/boot/autoconfigure/properties/client/TmProperties.java", "_extra": {"func_start": 3064, "func_end": 3099, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3178, "char_end": 3253, "patch_content": "", "patch_id": "seata/dff772c85989d50e", "repository": "seata/seata", "commit_sha": "", "file_name": "seata-spring-autoconfigure/seata-spring-autoconfigure-client/src/main/java/io/seata/spring/boot/autoconfigure/properties/client/TmProperties.java", "_extra": {"func_start": 3178, "func_end": 3253, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7802, "char_end": 7866, "patch_content": "", "patch_id": "seata/9ee2a38f3373c1cb", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/session/AbstractSessionManager.java", "_extra": {"func_start": 7802, "func_end": 7866, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5223, "char_end": 5412, "patch_content": "", "patch_id": "seata/98d40f1b593f6f6d", "repository": "seata/seata", "commit_sha": "", "file_name": "discovery/seata-discovery-sofa/src/main/java/io/seata/discovery/registry/sofa/SofaRegistryServiceImpl.java", "_extra": {"func_start": 5185, "func_end": 6247, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5942, "char_end": 5971, "patch_content": "", "patch_id": "seata/56d9f004f6f92146", "repository": "seata/seata", "commit_sha": "", "file_name": "sqlparser/seata-sqlparser-core/src/main/java/io/seata/sqlparser/struct/ColumnMeta.java", "_extra": {"func_start": 5942, "func_end": 5971, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1746, "char_end": 1809, "patch_content": "", "patch_id": "seata/8fd65d9d9c1f9cff", "repository": "seata/seata", "commit_sha": "", "file_name": "integration-tx-api/src/main/java/io/seata/integration/tx/api/remoting/parser/DefaultRemotingParser.java", "_extra": {"func_start": 1746, "func_end": 1809, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1852, "char_end": 1900, "patch_content": "", "patch_id": "seata/59abbe751f8e39ec", "repository": "seata/seata", "commit_sha": "", "file_name": "tm/src/main/java/io/seata/tm/api/transaction/TransactionHookManager.java", "_extra": {"func_start": 1557, "func_end": 1900, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4222, "char_end": 4313, "patch_content": "", "patch_id": "seata/97f264990faf3665", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/metrics/MetricsSubscriber.java", "_extra": {"func_start": 4399, "func_end": 5154, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10276, "char_end": 10306, "patch_content": "", "patch_id": "seata/59b7fdacfd0fca09", "repository": "seata/seata", "commit_sha": "", "file_name": "server/src/main/java/io/seata/server/storage/db/store/DataBaseTransactionStoreManager.java", "_extra": {"func_start": 9860, "func_end": 10306, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10994, "char_end": 11118, "patch_content": "", "patch_id": "seata/d22d384fb140f1ee", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/AbstractPreparedStatementProxy.java", "_extra": {"func_start": 10994, "func_end": 11118, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2028, "char_end": 2099, "patch_content": "", "patch_id": "seata/3560262b62f61d7e", "repository": "seata/seata", "commit_sha": "", "file_name": "seata-spring-autoconfigure/seata-spring-autoconfigure-client/src/main/java/io/seata/spring/boot/autoconfigure/properties/client/UndoProperties.java", "_extra": {"func_start": 2028, "func_end": 2099, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9078, "char_end": 9105, "patch_content": "", "patch_id": "seata/72274d6fca1a1237", "repository": "seata/seata", "commit_sha": "", "file_name": "spring/src/main/java/io/seata/spring/annotation/GlobalTransactionScanner.java", "_extra": {"func_start": 7980, "func_end": 9498, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1218, "char_end": 1270, "patch_content": "", "patch_id": "seata/12f54bf608ef08e1", "repository": "seata/seata", "commit_sha": "", "file_name": "rm-datasource/src/main/java/io/seata/rm/datasource/xa/XABranchXid.java", "_extra": {"func_start": 1194, "func_end": 1270, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2962, "char_end": 2992, "patch_content": "", "patch_id": "seata/49bfdb4289702350", "repository": "seata/seata", "commit_sha": "", "file_name": "tcc/src/main/java/io/seata/rm/tcc/TCCResource.java", "_extra": {"func_start": 2962, "func_end": 2992, "mode": "2-3-lines"}}

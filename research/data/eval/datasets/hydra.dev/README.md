# Hydra dataset

To recreate the majority of the original raw dataset, run
`prepare_raw.sh`. This will clone the repo, checkout a
 pre-determined head, then archive the data into a package
 for the share drive.

To recreate the processed dataset, run `run_pipeline.sh`.
This will take the raw data and create jsonl records from
it. This data will be the input for the evaluation task.

To recreate the prompts is not fully automated. These
prompt files are stored in
   /mnt/efs/augment/raw/hydra.dev/prompts.
For each repository, how the prompts are created may
differ. For the Repocoder dataset, the prompts are stored
in the Repocoder repository, which we clone.

If not fully automated by this pipeline, please record
the steps for recreating the prompts when adding a new
repository:

# <PERSON><PERSON><PERSON> and Syncthing
See this Notion page: https://www.notion.so/Repo-Eval-Go-1f0410393a4547e7a617e7fb57ff1f0f?pvs=4

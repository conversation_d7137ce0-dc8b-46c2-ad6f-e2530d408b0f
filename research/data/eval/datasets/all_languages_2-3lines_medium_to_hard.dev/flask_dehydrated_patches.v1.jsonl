{"file_content": "", "char_start": 923, "char_end": 1014, "patch_content": "", "patch_id": "pallets/flask/59cbnxhd", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/templating.py", "_extra": {"func_start": 746, "func_end": 1015, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1365, "char_end": 1502, "patch_content": "", "patch_id": "pallets/flask/U5XTZXe5", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/templating.py", "_extra": {"func_start": 1329, "func_end": 1503, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1915, "char_end": 2043, "patch_content": "", "patch_id": "pallets/flask/jWB96swy", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/templating.py", "_extra": {"func_start": 1859, "func_end": 2044, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3217, "char_end": 3283, "patch_content": "", "patch_id": "pallets/flask/XTL7rcvK", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/templating.py", "_extra": {"func_start": 3038, "func_end": 3283, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3504, "char_end": 3600, "patch_content": "", "patch_id": "pallets/flask/erQ3XFvm", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/templating.py", "_extra": {"func_start": 3720, "func_end": 4124, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5405, "char_end": 5570, "patch_content": "", "patch_id": "pallets/flask/K7MfepUW", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/templating.py", "_extra": {"func_start": 5405, "func_end": 5571, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2210, "char_end": 2291, "patch_content": "", "patch_id": "pallets/flask/eHavRmga", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/testing.py", "_extra": {"func_start": 1836, "func_end": 2826, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3099, "char_end": 3149, "patch_content": "", "patch_id": "pallets/flask/F7fFDPBq", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/testing.py", "_extra": {"func_start": 3099, "func_end": 3149, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3212, "char_end": 3241, "patch_content": "", "patch_id": "pallets/flask/WFiZrWQS", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/testing.py", "_extra": {"func_start": 3212, "func_end": 3369, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4176, "char_end": 4247, "patch_content": "", "patch_id": "pallets/flask/NfrVw3Tb", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/testing.py", "_extra": {"func_start": 4033, "func_end": 4368, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5890, "char_end": 5934, "patch_content": "", "patch_id": "pallets/flask/PMEE5sxb", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/testing.py", "_extra": {"func_start": 5183, "func_end": 6071, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6154, "char_end": 6267, "patch_content": "", "patch_id": "pallets/flask/MYuip2TU", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/testing.py", "_extra": {"func_start": 6108, "func_end": 6287, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7220, "char_end": 7338, "patch_content": "", "patch_id": "pallets/flask/ovmDkiPd", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/testing.py", "_extra": {"func_start": 6767, "func_end": 8284, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8424, "char_end": 8481, "patch_content": "", "patch_id": "pallets/flask/boDD6b4b", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/testing.py", "_extra": {"func_start": 8325, "func_end": 8481, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8991, "char_end": 9049, "patch_content": "", "patch_id": "pallets/flask/EqjaSPFY", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/testing.py", "_extra": {"func_start": 8991, "func_end": 9049, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9793, "char_end": 9864, "patch_content": "", "patch_id": "pallets/flask/YmBGpQDz", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/testing.py", "_extra": {"func_start": 9793, "func_end": 10017, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3956, "char_end": 4073, "patch_content": "", "patch_id": "pallets/flask/3Rm3qxV5", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/views.py", "_extra": {"func_start": 3624, "func_end": 5002, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6212, "char_end": 6281, "patch_content": "", "patch_id": "pallets/flask/3pZ9Wngy", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/views.py", "_extra": {"func_start": 5846, "func_end": 6320, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6396, "char_end": 6455, "patch_content": "", "patch_id": "pallets/flask/pAjG7V6f", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/views.py", "_extra": {"func_start": 6396, "func_end": 6789, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 845, "char_end": 950, "patch_content": "", "patch_id": "pallets/flask/k58s53fM", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/helpers.py", "_extra": {"func_start": 845, "func_end": 950, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2968, "char_end": 3049, "patch_content": "", "patch_id": "pallets/flask/BFTMrxwi", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/helpers.py", "_extra": {"func_start": 2698, "func_end": 4241, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6040, "char_end": 6105, "patch_content": "", "patch_id": "pallets/flask/HNH4wivo", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/helpers.py", "_extra": {"func_start": 5956, "func_end": 6105, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7847, "char_end": 7896, "patch_content": "", "patch_id": "pallets/flask/6nD8hCX2", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/helpers.py", "_extra": {"func_start": 7772, "func_end": 7950, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8730, "char_end": 8795, "patch_content": "", "patch_id": "pallets/flask/DTfweiQh", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/helpers.py", "_extra": {"func_start": 8653, "func_end": 8795, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9476, "char_end": 9547, "patch_content": "", "patch_id": "pallets/flask/fCHEvSP4", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/helpers.py", "_extra": {"func_start": 9476, "func_end": 9585, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10271, "char_end": 10359, "patch_content": "", "patch_id": "pallets/flask/Sx7tuoEG", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/helpers.py", "_extra": {"func_start": 10271, "func_end": 10359, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13238, "char_end": 13374, "patch_content": "", "patch_id": "pallets/flask/eQW4mdYZ", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/helpers.py", "_extra": {"func_start": 13180, "func_end": 13536, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13726, "char_end": 13819, "patch_content": "", "patch_id": "pallets/flask/4a23eD5x", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/helpers.py", "_extra": {"func_start": 13606, "func_end": 13953, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20562, "char_end": 20701, "patch_content": "", "patch_id": "pallets/flask/owXgL2Te", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/helpers.py", "_extra": {"func_start": 20562, "func_end": 20708, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2355, "char_end": 2411, "patch_content": "", "patch_id": "pallets/flask/rxRY6P3x", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/ctx.py", "_extra": {"func_start": 2322, "func_end": 2464, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2858, "char_end": 2913, "patch_content": "", "patch_id": "pallets/flask/gTQZ4Xnm", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/ctx.py", "_extra": {"func_start": 2858, "func_end": 2913, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2961, "char_end": 2998, "patch_content": "", "patch_id": "pallets/flask/7XXGVq47", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/ctx.py", "_extra": {"func_start": 2961, "func_end": 2998, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4261, "char_end": 4304, "patch_content": "", "patch_id": "pallets/flask/sXbRAF3d", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/ctx.py", "_extra": {"func_start": 4041, "func_end": 4318, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7319, "char_end": 7397, "patch_content": "", "patch_id": "pallets/flask/cmrTQQme", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/ctx.py", "_extra": {"func_start": 7319, "func_end": 7513, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7602, "char_end": 7730, "patch_content": "", "patch_id": "pallets/flask/R4X34ujV", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/ctx.py", "_extra": {"func_start": 7602, "func_end": 7730, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8289, "char_end": 8367, "patch_content": "", "patch_id": "pallets/flask/AzUkbUXL", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/ctx.py", "_extra": {"func_start": 7849, "func_end": 8367, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9970, "char_end": 10054, "patch_content": "", "patch_id": "pallets/flask/sV2Qsqor", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/ctx.py", "_extra": {"func_start": 9827, "func_end": 10644, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12586, "char_end": 12726, "patch_content": "", "patch_id": "pallets/flask/ApVV2XNP", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/ctx.py", "_extra": {"func_start": 11975, "func_end": 13045, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13585, "char_end": 13635, "patch_content": "", "patch_id": "pallets/flask/obXMERb2", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/ctx.py", "_extra": {"func_start": 13410, "func_end": 14397, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5183, "char_end": 5276, "patch_content": "", "patch_id": "pallets/flask/R3qNZZcw", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/config.py", "_extra": {"func_start": 4920, "func_end": 5963, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6661, "char_end": 6757, "patch_content": "", "patch_id": "pallets/flask/TEinrBKN", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/config.py", "_extra": {"func_start": 6661, "func_end": 7223, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8846, "char_end": 8892, "patch_content": "", "patch_id": "pallets/flask/Q7d89ce2", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/config.py", "_extra": {"func_start": 8717, "func_end": 8892, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11019, "char_end": 11126, "patch_content": "", "patch_id": "pallets/flask/i2ZCXeWS", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/config.py", "_extra": {"func_start": 10878, "func_end": 11147, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12634, "char_end": 12717, "patch_content": "", "patch_id": "pallets/flask/cm9p29JN", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/config.py", "_extra": {"func_start": 12424, "func_end": 12760, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 679, "char_end": 724, "patch_content": "", "patch_id": "pallets/flask/BsyoZquf", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sessions.py", "_extra": {"func_start": 679, "func_end": 724, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3316, "char_end": 3438, "patch_content": "", "patch_id": "pallets/flask/R7QQR5or", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sessions.py", "_extra": {"func_start": 3228, "func_end": 3439, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6247, "char_end": 6288, "patch_content": "", "patch_id": "pallets/flask/cUs4ej2P", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sessions.py", "_extra": {"func_start": 6247, "func_end": 6288, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7688, "char_end": 7771, "patch_content": "", "patch_id": "pallets/flask/rRnnjjrq", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sessions.py", "_extra": {"func_start": 7688, "func_end": 7771, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9037, "char_end": 9136, "patch_content": "", "patch_id": "pallets/flask/EGncJM9Y", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sessions.py", "_extra": {"func_start": 9007, "func_end": 9136, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9691, "char_end": 9778, "patch_content": "", "patch_id": "pallets/flask/hr8VtfcB", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sessions.py", "_extra": {"func_start": 9653, "func_end": 9778, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12371, "char_end": 12502, "patch_content": "", "patch_id": "pallets/flask/MZi7pDJF", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sessions.py", "_extra": {"func_start": 12159, "func_end": 12616, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 691, "char_end": 760, "patch_content": "", "patch_id": "pallets/flask/GtnrS9je", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/logging.py", "_extra": {"func_start": 691, "func_end": 760, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2099, "char_end": 2182, "patch_content": "", "patch_id": "pallets/flask/RqZiJYqg", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/wrappers.py", "_extra": {"func_start": 2099, "func_end": 2221, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3167, "char_end": 3234, "patch_content": "", "patch_id": "pallets/flask/NU89sm7R", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/wrappers.py", "_extra": {"func_start": 3080, "func_end": 3235, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3701, "char_end": 3810, "patch_content": "", "patch_id": "pallets/flask/i6HLrKc7", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/wrappers.py", "_extra": {"func_start": 3701, "func_end": 4163, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4299, "char_end": 4402, "patch_content": "", "patch_id": "pallets/flask/iy3Kcju5", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/wrappers.py", "_extra": {"func_start": 4233, "func_end": 4442, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 991, "char_end": 1059, "patch_content": "", "patch_id": "pallets/flask/q5JYKG78", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/debughelpers.py", "_extra": {"func_start": 583, "func_end": 1335, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2992, "char_end": 3126, "patch_content": "", "patch_id": "pallets/flask/iyky3jiR", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/debughelpers.py", "_extra": {"func_start": 2722, "func_end": 3245, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10647, "char_end": 10774, "patch_content": "", "patch_id": "pallets/flask/Sb6ToQr5", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/app.py", "_extra": {"func_start": 9433, "func_end": 10892, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12477, "char_end": 12567, "patch_content": "", "patch_id": "pallets/flask/4UmN8RGp", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/app.py", "_extra": {"func_start": 12224, "func_end": 12649, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16676, "char_end": 16726, "patch_content": "", "patch_id": "pallets/flask/kVmB3v2n", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/app.py", "_extra": {"func_start": 16318, "func_end": 17357, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 17955, "char_end": 18148, "patch_content": "", "patch_id": "pallets/flask/JF43R2sW", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/app.py", "_extra": {"func_start": 17915, "func_end": 18327, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 30393, "char_end": 30455, "patch_content": "", "patch_id": "pallets/flask/XK9sc7CB", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/app.py", "_extra": {"func_start": 30007, "func_end": 30456, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 33092, "char_end": 33183, "patch_content": "", "patch_id": "pallets/flask/sYdfcLKa", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/app.py", "_extra": {"func_start": 33065, "func_end": 33183, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 34141, "char_end": 34338, "patch_content": "", "patch_id": "pallets/flask/5xrCWQ7A", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/app.py", "_extra": {"func_start": 33696, "func_end": 34420, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 34716, "char_end": 34801, "patch_content": "", "patch_id": "pallets/flask/GmFSzHYu", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/app.py", "_extra": {"func_start": 34677, "func_end": 35036, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 36016, "char_end": 36076, "patch_content": "", "patch_id": "pallets/flask/EcAaZ9Pp", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/app.py", "_extra": {"func_start": 35758, "func_end": 36232, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 36523, "char_end": 36671, "patch_content": "", "patch_id": "pallets/flask/rh9xrViF", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/app.py", "_extra": {"func_start": 36523, "func_end": 36723, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 41986, "char_end": 42121, "patch_content": "", "patch_id": "pallets/flask/nqECcPi9", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/app.py", "_extra": {"func_start": 40516, "func_end": 43102, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 46090, "char_end": 46243, "patch_content": "", "patch_id": "pallets/flask/YSWK2aHv", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/app.py", "_extra": {"func_start": 45364, "func_end": 48903, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 49518, "char_end": 49704, "patch_content": "", "patch_id": "pallets/flask/ZMfLLGZG", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/app.py", "_extra": {"func_start": 49436, "func_end": 50003, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 24545, "char_end": 24558, "patch_content": "", "patch_id": "pallets/flask/Z6yZxtKw", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/app.py", "_extra": {"func_start": 56831, "func_end": 57054, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 58497, "char_end": 58571, "patch_content": "", "patch_id": "pallets/flask/Xdnv4ZyD", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/app.py", "_extra": {"func_start": 58108, "func_end": 58930, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2785, "char_end": 2896, "patch_content": "", "patch_id": "pallets/flask/EbNXERbP", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/cli.py", "_extra": {"func_start": 2718, "func_end": 3158, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4755, "char_end": 4774, "patch_content": "", "patch_id": "pallets/flask/JjZxBvgt", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/cli.py", "_extra": {"func_start": 3355, "func_end": 5694, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5914, "char_end": 5975, "patch_content": "", "patch_id": "pallets/flask/qPCHX4yk", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/cli.py", "_extra": {"func_start": 5880, "func_end": 6443, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6946, "char_end": 7059, "patch_content": "", "patch_id": "pallets/flask/sgFo8Zyw", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/cli.py", "_extra": {"func_start": 6509, "func_end": 7257, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7572, "char_end": 7642, "patch_content": "", "patch_id": "pallets/flask/6bcow4KG", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/cli.py", "_extra": {"func_start": 7295, "func_end": 7658, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9315, "char_end": 9396, "patch_content": "", "patch_id": "pallets/flask/7oVMVWJQ", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/cli.py", "_extra": {"func_start": 9161, "func_end": 10423, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11293, "char_end": 11334, "patch_content": "", "patch_id": "pallets/flask/9UG5jybt", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/cli.py", "_extra": {"func_start": 11036, "func_end": 11334, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12127, "char_end": 12152, "patch_content": "", "patch_id": "pallets/flask/hpzCwYj2", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/cli.py", "_extra": {"func_start": 11907, "func_end": 12152, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12370, "char_end": 12469, "patch_content": "", "patch_id": "pallets/flask/biM9xr3o", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/cli.py", "_extra": {"func_start": 12370, "func_end": 12469, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13628, "char_end": 13746, "patch_content": "", "patch_id": "pallets/flask/PuUpVuso", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/cli.py", "_extra": {"func_start": 13628, "func_end": 14032, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 17563, "char_end": 17596, "patch_content": "", "patch_id": "pallets/flask/tXHUahsG", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/cli.py", "_extra": {"func_start": 16765, "func_end": 17774, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 17872, "char_end": 17912, "patch_content": "", "patch_id": "pallets/flask/RpLRGXry", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/cli.py", "_extra": {"func_start": 17812, "func_end": 18369, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18822, "char_end": 18904, "patch_content": "", "patch_id": "pallets/flask/4SV3rj9U", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/cli.py", "_extra": {"func_start": 18408, "func_end": 19388, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 24407, "char_end": 24491, "patch_content": "", "patch_id": "pallets/flask/9k39XzrK", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/cli.py", "_extra": {"func_start": 24407, "func_end": 24491, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 25766, "char_end": 25808, "patch_content": "", "patch_id": "pallets/flask/cr4QAA6V", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/cli.py", "_extra": {"func_start": 25691, "func_end": 26552, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 32676, "char_end": 32709, "patch_content": "", "patch_id": "pallets/flask/aXgmbB7Z", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/cli.py", "_extra": {"func_start": 32177, "func_end": 33476, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2754, "char_end": 2801, "patch_content": "", "patch_id": "pallets/flask/Qe5TomCd", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/tag.py", "_extra": {"func_start": 2754, "func_end": 2801, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3104, "char_end": 3171, "patch_content": "", "patch_id": "pallets/flask/AhUHDUoq", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/tag.py", "_extra": {"func_start": 3087, "func_end": 3240, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3319, "char_end": 3380, "patch_content": "", "patch_id": "pallets/flask/9VzXqAHT", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/tag.py", "_extra": {"func_start": 3287, "func_end": 3380, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3429, "char_end": 3499, "patch_content": "", "patch_id": "pallets/flask/SUJ6dV5n", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/tag.py", "_extra": {"func_start": 3429, "func_end": 3499, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3773, "char_end": 3842, "patch_content": "", "patch_id": "pallets/flask/rzpHeGzb", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/tag.py", "_extra": {"func_start": 3773, "func_end": 3842, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4163, "char_end": 4191, "patch_content": "", "patch_id": "pallets/flask/sKgRJJn8", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/tag.py", "_extra": {"func_start": 4163, "func_end": 4191, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4639, "char_end": 4687, "patch_content": "", "patch_id": "pallets/flask/XnuJhhN5", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/tag.py", "_extra": {"func_start": 4639, "func_end": 4687, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5410, "char_end": 5449, "patch_content": "", "patch_id": "pallets/flask/tLSHKFdT", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/tag.py", "_extra": {"func_start": 5410, "func_end": 5449, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5705, "char_end": 5748, "patch_content": "", "patch_id": "pallets/flask/F9zWR7f2", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/tag.py", "_extra": {"func_start": 5705, "func_end": 5748, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5795, "char_end": 5827, "patch_content": "", "patch_id": "pallets/flask/Ewt49w72", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/tag.py", "_extra": {"func_start": 5795, "func_end": 5827, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5876, "char_end": 5909, "patch_content": "", "patch_id": "pallets/flask/pEVc3C8M", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/tag.py", "_extra": {"func_start": 5876, "func_end": 5909, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8075, "char_end": 8176, "patch_content": "", "patch_id": "pallets/flask/JWNbbmLE", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/tag.py", "_extra": {"func_start": 8075, "func_end": 8199, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8448, "char_end": 8525, "patch_content": "", "patch_id": "pallets/flask/rN3iykbE", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/tag.py", "_extra": {"func_start": 8328, "func_end": 8526, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8819, "char_end": 8871, "patch_content": "", "patch_id": "pallets/flask/o7cF7WQp", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/tag.py", "_extra": {"func_start": 8819, "func_end": 8871, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1745, "char_end": 1789, "patch_content": "", "patch_id": "pallets/flask/FrhGbHdv", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/provider.py", "_extra": {"func_start": 1745, "func_end": 1789, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2501, "char_end": 2626, "patch_content": "", "patch_id": "pallets/flask/4BoFtyun", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/provider.py", "_extra": {"func_start": 2473, "func_end": 2737, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3576, "char_end": 3604, "patch_content": "", "patch_id": "pallets/flask/q7y5oSJM", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/provider.py", "_extra": {"func_start": 3548, "func_end": 3918, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6457, "char_end": 6496, "patch_content": "", "patch_id": "pallets/flask/Y9ErVC8S", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/provider.py", "_extra": {"func_start": 6457, "func_end": 6496, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7316, "char_end": 7442, "patch_content": "", "patch_id": "pallets/flask/SMkvM9JP", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/json/provider.py", "_extra": {"func_start": 7220, "func_end": 7640, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1867, "char_end": 1924, "patch_content": "", "patch_id": "pallets/flask/ZgpXutHp", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/app.py", "_extra": {"func_start": 1813, "func_end": 1925, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13458, "char_end": 13595, "patch_content": "", "patch_id": "pallets/flask/HR3BpWiS", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/app.py", "_extra": {"func_start": 12161, "func_end": 17284, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 19561, "char_end": 19596, "patch_content": "", "patch_id": "pallets/flask/myG7aTmd", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/app.py", "_extra": {"func_start": 19561, "func_end": 19596, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 19884, "char_end": 19931, "patch_content": "", "patch_id": "pallets/flask/dJ43kD4q", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/app.py", "_extra": {"func_start": 19884, "func_end": 19931, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 20618, "char_end": 20717, "patch_content": "", "patch_id": "pallets/flask/drscgDMM", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/app.py", "_extra": {"func_start": 20465, "func_end": 20717, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 21485, "char_end": 21573, "patch_content": "", "patch_id": "pallets/flask/awY9FH6H", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/app.py", "_extra": {"func_start": 21485, "func_end": 21700, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 22200, "char_end": 22244, "patch_content": "", "patch_id": "pallets/flask/My5fYfuW", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/app.py", "_extra": {"func_start": 22200, "func_end": 22244, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 22627, "char_end": 22729, "patch_content": "", "patch_id": "pallets/flask/Sj9cxBWD", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/app.py", "_extra": {"func_start": 22598, "func_end": 22729, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23303, "char_end": 23397, "patch_content": "", "patch_id": "pallets/flask/ZMrorueS", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/app.py", "_extra": {"func_start": 23303, "func_end": 23445, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 24704, "char_end": 24746, "patch_content": "", "patch_id": "pallets/flask/odJTnxnw", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/app.py", "_extra": {"func_start": 24704, "func_end": 24746, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 25741, "char_end": 25834, "patch_content": "", "patch_id": "pallets/flask/aJbKZMqz", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/app.py", "_extra": {"func_start": 25221, "func_end": 27287, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 27945, "char_end": 27991, "patch_content": "", "patch_id": "pallets/flask/UPwmJiir", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/app.py", "_extra": {"func_start": 29166, "func_end": 29325, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 32308, "char_end": 32374, "patch_content": "", "patch_id": "pallets/flask/EvVScyea", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/app.py", "_extra": {"func_start": 32308, "func_end": 32374, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 35746, "char_end": 35843, "patch_content": "", "patch_id": "pallets/flask/E4FA3YsE", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/app.py", "_extra": {"func_start": 35717, "func_end": 35844, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 36480, "char_end": 36558, "patch_content": "", "patch_id": "pallets/flask/5FJqvqNt", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/app.py", "_extra": {"func_start": 36146, "func_end": 36664, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 37709, "char_end": 37761, "patch_content": "", "patch_id": "pallets/flask/JkewvKZy", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/app.py", "_extra": {"func_start": 37462, "func_end": 37977, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4181, "char_end": 4230, "patch_content": "", "patch_id": "pallets/flask/4dNwGqGe", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 3686, "func_end": 4388, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8097, "char_end": 8145, "patch_content": "", "patch_id": "pallets/flask/YfYM6UtU", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 7452, "func_end": 8241, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9079, "char_end": 9124, "patch_content": "", "patch_id": "pallets/flask/jbtr6kZ6", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 9079, "func_end": 9124, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9521, "char_end": 9590, "patch_content": "", "patch_id": "pallets/flask/8UPC4dh3", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 9463, "func_end": 9642, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10013, "char_end": 10088, "patch_content": "", "patch_id": "pallets/flask/Zppo8N85", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 10013, "func_end": 10088, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10712, "char_end": 10835, "patch_content": "", "patch_id": "pallets/flask/okFWFCMt", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 10682, "func_end": 10836, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13075, "char_end": 13202, "patch_content": "", "patch_id": "pallets/flask/LLyRZNxD", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 12078, "func_end": 15161, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15849, "char_end": 15956, "patch_content": "", "patch_id": "pallets/flask/UMg6rwPS", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 15229, "func_end": 16425, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9590, "char_end": 9611, "patch_content": "", "patch_id": "pallets/flask/jMirrF8R", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 16969, "func_end": 17519, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18065, "char_end": 18111, "patch_content": "", "patch_id": "pallets/flask/QKhxbEJi", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 17943, "func_end": 18112, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18722, "char_end": 18767, "patch_content": "", "patch_id": "pallets/flask/dhYoX93u", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 19872, "func_end": 20047, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 21250, "char_end": 21358, "patch_content": "", "patch_id": "pallets/flask/ihyB7ZED", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 21182, "func_end": 21359, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 21650, "char_end": 21756, "patch_content": "", "patch_id": "pallets/flask/8nnZWAwc", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 21624, "func_end": 21757, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 22042, "char_end": 22147, "patch_content": "", "patch_id": "pallets/flask/NGoohWwK", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 22016, "func_end": 22148, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23303, "char_end": 23433, "patch_content": "", "patch_id": "pallets/flask/TK657Vhz", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 23303, "func_end": 23481, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23823, "char_end": 23932, "patch_content": "", "patch_id": "pallets/flask/YVncpijB", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 23797, "func_end": 23933, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 24185, "char_end": 24291, "patch_content": "", "patch_id": "pallets/flask/8UJ769u9", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/blueprints.py", "_extra": {"func_start": 24185, "func_end": 24319, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1352, "char_end": 1420, "patch_content": "", "patch_id": "pallets/flask/AMZDsZLW", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/scaffold.py", "_extra": {"func_start": 1328, "func_end": 1559, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9619, "char_end": 9700, "patch_content": "", "patch_id": "pallets/flask/JVPUBfWh", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/scaffold.py", "_extra": {"func_start": 9619, "func_end": 9737, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10264, "char_end": 10307, "patch_content": "", "patch_id": "pallets/flask/TYx5926C", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/scaffold.py", "_extra": {"func_start": 10177, "func_end": 10435, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10931, "char_end": 11077, "patch_content": "", "patch_id": "pallets/flask/rcMurnMd", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/scaffold.py", "_extra": {"func_start": 10931, "func_end": 11102, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16804, "char_end": 16871, "patch_content": "", "patch_id": "pallets/flask/Naazn8Ax", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/scaffold.py", "_extra": {"func_start": 16769, "func_end": 16897, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23033, "char_end": 23101, "patch_content": "", "patch_id": "pallets/flask/KwtPouoo", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/scaffold.py", "_extra": {"func_start": 23033, "func_end": 23101, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13785, "char_end": 13831, "patch_content": "", "patch_id": "pallets/flask/DxCaLsrv", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/scaffold.py", "_extra": {"func_start": 24684, "func_end": 24856, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 25224, "char_end": 25357, "patch_content": "", "patch_id": "pallets/flask/CjH2k3Ao", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/scaffold.py", "_extra": {"func_start": 25224, "func_end": 25357, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 26597, "char_end": 26700, "patch_content": "", "patch_id": "pallets/flask/i2BgUmj9", "repository": "pallets/flask", "commit_sha": "c56786888a1a50adaff5f9d76534861a8ebdb6e3", "file_name": "src/flask/sansio/scaffold.py", "_extra": {"func_start": 25786, "func_end": 26972, "mode": "2-3-lines"}}

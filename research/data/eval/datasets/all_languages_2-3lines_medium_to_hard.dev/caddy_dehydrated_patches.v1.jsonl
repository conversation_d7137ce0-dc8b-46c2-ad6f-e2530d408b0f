{"file_content": "", "char_start": 12865, "char_end": 12946, "patch_content": "", "patch_id": "caddyserver/caddy/DpiP242K", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "caddyconfig/httpcaddyfile/addresses.go", "_extra": {"func_start": 12776, "func_end": 13307, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6083, "char_end": 6113, "patch_content": "", "patch_id": "caddyserver/caddy/3QTKyxdA", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/reverseproxy/hosts.go", "_extra": {"func_start": 6032, "func_end": 6183, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5290, "char_end": 5317, "patch_content": "", "patch_id": "caddyserver/caddy/baopc3Dm", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "caddyconfig/httpcaddyfile/directives.go", "_extra": {"func_start": 5271, "func_end": 5401, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5022, "char_end": 5152, "patch_content": "", "patch_id": "caddyserver/caddy/AwZFQgyL", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/reverseproxy/selectionpolicies.go", "_extra": {"func_start": 4996, "func_end": 5153, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 22328, "char_end": 22362, "patch_content": "", "patch_id": "caddyserver/caddy/6j95J8mw", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "caddyconfig/httpcaddyfile/builtins.go", "_extra": {"func_start": 22328, "func_end": 22362, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14314, "char_end": 14365, "patch_content": "", "patch_id": "caddyserver/caddy/T82vF6mA", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "caddyconfig/httpcaddyfile/addresses.go", "_extra": {"func_start": 14017, "func_end": 14448, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1794, "char_end": 1803, "patch_content": "", "patch_id": "caddyserver/caddy/9ri8oiuW", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/push/link.go", "_extra": {"func_start": 1137, "func_end": 1861, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3463, "char_end": 3569, "patch_content": "", "patch_id": "caddyserver/caddy/3bt2UhYf", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/tracing/tracer.go", "_extra": {"func_start": 3463, "func_end": 3620, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1229, "char_end": 1320, "patch_content": "", "patch_id": "caddyserver/caddy/8QRUvVka", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "caddyconfig/httpcaddyfile/builtins.go", "_extra": {"func_start": 1110, "func_end": 1754, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1685, "char_end": 1771, "patch_content": "", "patch_id": "caddyserver/caddy/Z39ksBWf", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/metrics/metrics.go", "_extra": {"func_start": 1659, "func_end": 1772, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1353, "char_end": 1453, "patch_content": "", "patch_id": "caddyserver/caddy/Mqe25wt9", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddytls/storageloader.go", "_extra": {"func_start": 1327, "func_end": 1454, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12964, "char_end": 13066, "patch_content": "", "patch_id": "caddyserver/caddy/j6CyTQci", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/rewrite/rewrite.go", "_extra": {"func_start": 12797, "func_end": 13237, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8485, "char_end": 8576, "patch_content": "", "patch_id": "caddyserver/caddy/VZKXu5YK", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/templates/tplcontext.go", "_extra": {"func_start": 8333, "func_end": 8838, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2713, "char_end": 2746, "patch_content": "", "patch_id": "caddyserver/caddy/G7BxYkTS", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/reverseproxy/hosts.go", "_extra": {"func_start": 2713, "func_end": 2746, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1617, "char_end": 1686, "patch_content": "", "patch_id": "caddyserver/caddy/rs8T25Qu", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "caddyconfig/caddyfile/lexer.go", "_extra": {"func_start": 1617, "func_end": 1912, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1841, "char_end": 1873, "patch_content": "", "patch_id": "caddyserver/caddy/rBz3YpPh", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/tracing/tracer.go", "_extra": {"func_start": 1286, "func_end": 2123, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1705, "char_end": 1793, "patch_content": "", "patch_id": "caddyserver/caddy/tvHatVGr", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/subroute.go", "_extra": {"func_start": 1679, "func_end": 1794, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1141, "char_end": 1225, "patch_content": "", "patch_id": "caddyserver/caddy/HNbPsrUD", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/invoke.go", "_extra": {"func_start": 1115, "func_end": 1226, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1219, "char_end": 1316, "patch_content": "", "patch_id": "caddyserver/caddy/78AuAT2E", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/replacer.go", "_extra": {"func_start": 1219, "func_end": 1399, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4743, "char_end": 4823, "patch_content": "", "patch_id": "caddyserver/caddy/i2EvQ2PP", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/ip_matchers.go", "_extra": {"func_start": 4718, "func_end": 5252, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4338, "char_end": 4441, "patch_content": "", "patch_id": "caddyserver/caddy/L4vTnJqm", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/ip_matchers.go", "_extra": {"func_start": 7027, "func_end": 7193, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9302, "char_end": 9434, "patch_content": "", "patch_id": "caddyserver/caddy/e3zSRT3i", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/ip_matchers.go", "_extra": {"func_start": 9282, "func_end": 9563, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13594, "char_end": 13679, "patch_content": "", "patch_id": "caddyserver/caddy/KHp2nULL", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/matchers.go", "_extra": {"func_start": 13568, "func_end": 13680, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 21797, "char_end": 21921, "patch_content": "", "patch_id": "caddyserver/caddy/9P2eSJ7B", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/matchers.go", "_extra": {"func_start": 21797, "func_end": 22489, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18280, "char_end": 18364, "patch_content": "", "patch_id": "caddyserver/caddy/rBZUs2mT", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/fileserver/matcher.go", "_extra": {"func_start": 18280, "func_end": 18364, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 24929, "char_end": 24987, "patch_content": "", "patch_id": "caddyserver/caddy/DKGeLYat", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/matchers.go", "_extra": {"func_start": 24872, "func_end": 24988, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 28462, "char_end": 28531, "patch_content": "", "patch_id": "caddyserver/caddy/DQcte8KP", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/matchers.go", "_extra": {"func_start": 28438, "func_end": 28736, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 28870, "char_end": 28959, "patch_content": "", "patch_id": "caddyserver/caddy/TXAyQyVa", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/matchers.go", "_extra": {"func_start": 28844, "func_end": 28960, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 30150, "char_end": 30277, "patch_content": "", "patch_id": "caddyserver/caddy/wKhw3vYx", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/matchers.go", "_extra": {"func_start": 30150, "func_end": 30278, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 33098, "char_end": 33196, "patch_content": "", "patch_id": "caddyserver/caddy/LDCrrwY8", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/matchers.go", "_extra": {"func_start": 33072, "func_end": 33197, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 35545, "char_end": 35679, "patch_content": "", "patch_id": "caddyserver/caddy/g3aJDNCD", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/matchers.go", "_extra": {"func_start": 35172, "func_end": 36581, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 36691, "char_end": 36749, "patch_content": "", "patch_id": "caddyserver/caddy/CweKAkwF", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/matchers.go", "_extra": {"func_start": 36691, "func_end": 36811, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 38473, "char_end": 38526, "patch_content": "", "patch_id": "caddyserver/caddy/XpiQTffp", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/matchers.go", "_extra": {"func_start": 38473, "func_end": 38583, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3238, "char_end": 3305, "patch_content": "", "patch_id": "caddyserver/caddy/XcSVcJsF", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/reverseproxy/hosts.go", "_extra": {"func_start": 3238, "func_end": 3305, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14076, "char_end": 14123, "patch_content": "", "patch_id": "caddyserver/caddy/nrfDCTCo", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/rewrite/rewrite.go", "_extra": {"func_start": 13935, "func_end": 14268, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 768, "char_end": 775, "patch_content": "", "patch_id": "caddyserver/caddy/q635LYdb", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/tracing/tracerprovider.go", "_extra": {"func_start": 613, "func_end": 801, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3260, "char_end": 3343, "patch_content": "", "patch_id": "caddyserver/caddy/YcZv57fL", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/encode/encode.go", "_extra": {"func_start": 3228, "func_end": 3554, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2134, "char_end": 2172, "patch_content": "", "patch_id": "caddyserver/caddy/FeFWhCgq", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/tracing/module.go", "_extra": {"func_start": 2134, "func_end": 2172, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8423, "char_end": 8534, "patch_content": "", "patch_id": "caddyserver/caddy/hAHJrdHz", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/celmatcher.go", "_extra": {"func_start": 8381, "func_end": 8535, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12890, "char_end": 12956, "patch_content": "", "patch_id": "caddyserver/caddy/PRK9bGqg", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/celmatcher.go", "_extra": {"func_start": 12863, "func_end": 12957, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11328, "char_end": 11393, "patch_content": "", "patch_id": "caddyserver/caddy/WRsC6ueK", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/rewrite/rewrite.go", "_extra": {"func_start": 11225, "func_end": 12102, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1608, "char_end": 1669, "patch_content": "", "patch_id": "caddyserver/caddy/Je9ibZi6", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/logging/netwriter.go", "_extra": {"func_start": 1608, "func_end": 1727, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8448, "char_end": 8514, "patch_content": "", "patch_id": "caddyserver/caddy/kgP98QC3", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/reverseproxy/selectionpolicies.go", "_extra": {"func_start": 8362, "func_end": 8515, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5064, "char_end": 5165, "patch_content": "", "patch_id": "caddyserver/caddy/sXEFPB6d", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/staticresp.go", "_extra": {"func_start": 5038, "func_end": 5166, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6542, "char_end": 6601, "patch_content": "", "patch_id": "caddyserver/caddy/DzLBVG8q", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/staticresp.go", "_extra": {"func_start": 6401, "func_end": 8620, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1985, "char_end": 2087, "patch_content": "", "patch_id": "caddyserver/caddy/GyGWrRFc", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "modules/caddyhttp/reverseproxy/upstreams.go", "_extra": {"func_start": 1959, "func_end": 2088, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2944, "char_end": 3021, "patch_content": "", "patch_id": "caddyserver/caddy/Xhx2dARt", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "caddyconfig/caddyfile/dispenser.go", "_extra": {"func_start": 2815, "func_end": 3053, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7227, "char_end": 7325, "patch_content": "", "patch_id": "caddyserver/caddy/4QQ9qz5E", "repository": "caddyserver/caddy", "commit_sha": "7103ea096fffd851a5efd67fd6d30cf65bc38873", "file_name": "caddyconfig/httpcaddyfile/addresses.go", "_extra": {"func_start": 6787, "func_end": 7822, "mode": "2-3-lines"}}

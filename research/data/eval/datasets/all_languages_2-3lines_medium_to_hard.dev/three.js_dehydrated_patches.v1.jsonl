{"file_content": "", "char_start": 3831, "char_end": 3896, "patch_content": "", "patch_id": "mrdoob/three.js/HFs5TgyD", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/animation/PropertyBinding.js", "_extra": {"func_start": 3830, "func_end": 3896, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5232, "char_end": 5274, "patch_content": "", "patch_id": "mrdoob/three.js/c32Lj742", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix4.js", "_extra": {"func_start": 5231, "func_end": 5274, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8491, "char_end": 8535, "patch_content": "", "patch_id": "mrdoob/three.js/BtWjZiFy", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Quaternion.js", "_extra": {"func_start": 8490, "func_end": 8784, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1603, "char_end": 1654, "patch_content": "", "patch_id": "mrdoob/three.js/DYdAHZLU", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Ray.js", "_extra": {"func_start": 1449, "func_end": 1791, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4099, "char_end": 4217, "patch_content": "", "patch_id": "mrdoob/three.js/ehxLGfpp", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/geometries/TorusKnotGeometry.js", "_extra": {"func_start": 4099, "func_end": 4218, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2599, "char_end": 2721, "patch_content": "", "patch_id": "mrdoob/three.js/sHd5h47a", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix3.js", "_extra": {"func_start": 2480, "func_end": 2723, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 17674, "char_end": 17679, "patch_content": "", "patch_id": "mrdoob/three.js/8ucLcoHW", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/PMREMGenerator.js", "_extra": {"func_start": 17352, "func_end": 19454, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 788, "char_end": 792, "patch_content": "", "patch_id": "mrdoob/three.js/V3cvGKTk", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/animation/AnimationAction.js", "_extra": {"func_start": 11504, "func_end": 12077, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1476, "char_end": 1563, "patch_content": "", "patch_id": "mrdoob/three.js/cPuk2p5E", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/ColorManagement.js", "_extra": {"func_start": 1427, "func_end": 1563, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3892, "char_end": 3958, "patch_content": "", "patch_id": "mrdoob/three.js/3f68XxJr", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Triangle.js", "_extra": {"func_start": 3891, "func_end": 3958, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9097, "char_end": 9173, "patch_content": "", "patch_id": "mrdoob/three.js/XvsbBtsz", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Color.js", "_extra": {"func_start": 10395, "func_end": 10561, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2466, "char_end": 2574, "patch_content": "", "patch_id": "mrdoob/three.js/46UsUybv", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/geometries/TorusGeometry.js", "_extra": {"func_start": 2465, "func_end": 2574, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2143, "char_end": 2225, "patch_content": "", "patch_id": "mrdoob/three.js/gJrVdk6t", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Sphere.js", "_extra": {"func_start": 2022, "func_end": 2245, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 147, "char_end": 186, "patch_content": "", "patch_id": "mrdoob/three.js/5H96sKCC", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector3.js", "_extra": {"func_start": 147, "func_end": 230, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7530, "char_end": 7615, "patch_content": "", "patch_id": "mrdoob/three.js/cZwPjTre", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector3.js", "_extra": {"func_start": 7529, "func_end": 7617, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3928, "char_end": 3965, "patch_content": "", "patch_id": "mrdoob/three.js/DdN6FNXE", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix3.js", "_extra": {"func_start": 3880, "func_end": 4097, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4653, "char_end": 4723, "patch_content": "", "patch_id": "mrdoob/three.js/DnjE6hyE", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/core/Curve.js", "_extra": {"func_start": 4503, "func_end": 4883, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3809, "char_end": 3899, "patch_content": "", "patch_id": "mrdoob/three.js/p9e3Ntka", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/geometries/LatheGeometry.js", "_extra": {"func_start": 3809, "func_end": 3900, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 904, "char_end": 959, "patch_content": "", "patch_id": "mrdoob/three.js/eLfkoQj3", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Spherical.js", "_extra": {"func_start": 903, "func_end": 960, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6528, "char_end": 6579, "patch_content": "", "patch_id": "mrdoob/three.js/mf3zyUSN", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector3.js", "_extra": {"func_start": 6528, "func_end": 6580, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 366, "char_end": 459, "patch_content": "", "patch_id": "mrdoob/three.js/fZZ9Hktw", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix3.js", "_extra": {"func_start": 336, "func_end": 524, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 17827, "char_end": 17908, "patch_content": "", "patch_id": "mrdoob/three.js/LZP4Buj8", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix4.js", "_extra": {"func_start": 17606, "func_end": 18423, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8046, "char_end": 8073, "patch_content": "", "patch_id": "mrdoob/three.js/tqFqQAeU", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/animation/AnimationObjectGroup.js", "_extra": {"func_start": 7530, "func_end": 8412, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3839, "char_end": 3937, "patch_content": "", "patch_id": "mrdoob/three.js/fa3kUBKq", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Color.js", "_extra": {"func_start": 3746, "func_end": 3939, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2447, "char_end": 2477, "patch_content": "", "patch_id": "mrdoob/three.js/eFkcKHtn", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/curves/CatmullRomCurve3.js", "_extra": {"func_start": 2414, "func_end": 4446, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3801, "char_end": 3865, "patch_content": "", "patch_id": "mrdoob/three.js/LF9grDdt", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Triangle.js", "_extra": {"func_start": 3800, "func_end": 3865, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 792, "char_end": 859, "patch_content": "", "patch_id": "mrdoob/three.js/nfwaweF4", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Spherical.js", "_extra": {"func_start": 767, "func_end": 876, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1439, "char_end": 1489, "patch_content": "", "patch_id": "mrdoob/three.js/dcVJtJoG", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/objects/Mesh.js", "_extra": {"func_start": 1403, "func_end": 1549, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 111, "char_end": 140, "patch_content": "", "patch_id": "mrdoob/three.js/LWYBozx7", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Quaternion.js", "_extra": {"func_start": 111, "func_end": 202, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7768, "char_end": 7843, "patch_content": "", "patch_id": "mrdoob/three.js/FoRnnETr", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Quaternion.js", "_extra": {"func_start": 7731, "func_end": 7884, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 911, "char_end": 947, "patch_content": "", "patch_id": "mrdoob/three.js/XGjrK4eJ", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/core/Interpolations.js", "_extra": {"func_start": 911, "func_end": 948, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11362, "char_end": 11417, "patch_content": "", "patch_id": "mrdoob/three.js/qMLXewZU", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Quaternion.js", "_extra": {"func_start": 10983, "func_end": 11453, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6608, "char_end": 6661, "patch_content": "", "patch_id": "mrdoob/three.js/afQqYB5G", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector3.js", "_extra": {"func_start": 6607, "func_end": 6661, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3766, "char_end": 3880, "patch_content": "", "patch_id": "mrdoob/three.js/ggQ3Yftr", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Box3.js", "_extra": {"func_start": 3708, "func_end": 3882, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1769, "char_end": 1773, "patch_content": "", "patch_id": "mrdoob/three.js/MoMqCWXA", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/objects/Sprite.js", "_extra": {"func_start": 1152, "func_end": 1926, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1911, "char_end": 1956, "patch_content": "", "patch_id": "mrdoob/three.js/Nez9C9sn", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Vector2.js", "_extra": {"func_start": 1910, "func_end": 1956, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 350, "char_end": 378, "patch_content": "", "patch_id": "mrdoob/three.js/9PgdwrZT", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/cameras/CubeCamera.js", "_extra": {"func_start": 338, "func_end": 1205, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 354, "char_end": 397, "patch_content": "", "patch_id": "mrdoob/three.js/tgKoVepn", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/cameras/PerspectiveCamera.js", "_extra": {"func_start": 195, "func_end": 593, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3216, "char_end": 3267, "patch_content": "", "patch_id": "mrdoob/three.js/SZJXfyDR", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/Object3D.js", "_extra": {"func_start": 3183, "func_end": 3269, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5447, "char_end": 5493, "patch_content": "", "patch_id": "mrdoob/three.js/Vxy5FEcx", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/Object3D.js", "_extra": {"func_start": 5102, "func_end": 5763, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6885, "char_end": 6911, "patch_content": "", "patch_id": "mrdoob/three.js/qxVsEdPq", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/Object3D.js", "_extra": {"func_start": 6826, "func_end": 6934, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5275, "char_end": 5306, "patch_content": "", "patch_id": "mrdoob/three.js/Da5GubZ3", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/Object3D.js", "_extra": {"func_start": 9706, "func_end": 9837, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4191, "char_end": 4246, "patch_content": "", "patch_id": "mrdoob/three.js/FCgKpymj", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Color.js", "_extra": {"func_start": 4104, "func_end": 4488, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3010, "char_end": 3054, "patch_content": "", "patch_id": "mrdoob/three.js/aeSLKH27", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Matrix3.js", "_extra": {"func_start": 2739, "func_end": 3534, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 342, "char_end": 410, "patch_content": "", "patch_id": "mrdoob/three.js/5BzvXYop", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/InterleavedBuffer.js", "_extra": {"func_start": 162, "func_end": 453, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 658, "char_end": 751, "patch_content": "", "patch_id": "mrdoob/three.js/MrXaFde5", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/BufferAttribute.js", "_extra": {"func_start": 458, "func_end": 915, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 265, "char_end": 306, "patch_content": "", "patch_id": "mrdoob/three.js/LodePSYt", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/extras/curves/EllipseCurve.js", "_extra": {"func_start": 264, "func_end": 554, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5615, "char_end": 5688, "patch_content": "", "patch_id": "mrdoob/three.js/YhQkPnqw", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/core/BufferAttribute.js", "_extra": {"func_start": 5615, "func_end": 5690, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5652, "char_end": 5827, "patch_content": "", "patch_id": "mrdoob/three.js/8g96JM28", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/geometries/CylinderGeometry.js", "_extra": {"func_start": 5652, "func_end": 5828, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3687, "char_end": 3772, "patch_content": "", "patch_id": "mrdoob/three.js/byJixFPR", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/math/Triangle.js", "_extra": {"func_start": 3687, "func_end": 3773, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11359, "char_end": 11424, "patch_content": "", "patch_id": "mrdoob/three.js/LM7o7Qj4", "repository": "mrdoob/three.js", "commit_sha": "a645ee18", "file_name": "src/animation/AnimationMixer.js", "_extra": {"func_start": 11071, "func_end": 12486, "mode": "2-3-lines"}}

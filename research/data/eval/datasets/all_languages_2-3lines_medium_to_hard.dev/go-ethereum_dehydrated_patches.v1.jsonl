{"file_content": "", "char_start": 11865, "char_end": 11956, "patch_content": "", "patch_id": "ethereum/go-ethereum/SwLW3j6Z", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "cmd/devp2p/dns_route53.go", "_extra": {"func_start": 11777, "func_end": 12139, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18865, "char_end": 18940, "patch_content": "", "patch_id": "ethereum/go-ethereum/4tCkQVCs", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "accounts/abi/bind/backends/simulated.go", "_extra": {"func_start": 16809, "func_end": 19750, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7646, "char_end": 7678, "patch_content": "", "patch_id": "ethereum/go-ethereum/kKnKbcFo", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "p2p/rlpx/rlpx.go", "_extra": {"func_start": 7608, "func_end": 7678, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1649, "char_end": 1710, "patch_content": "", "patch_id": "ethereum/go-ethereum/ccfNpruh", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/state/journal.go", "_extra": {"func_start": 1649, "func_end": 1711, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11776, "char_end": 11920, "patch_content": "", "patch_id": "ethereum/go-ethereum/SSL2wDFy", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/types/transaction_signing.go", "_extra": {"func_start": 11573, "func_end": 12176, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 546, "char_end": 584, "patch_content": "", "patch_id": "ethereum/go-ethereum/txBWm6Si", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/bn256/google/gfp2.go", "_extra": {"func_start": 546, "func_end": 584, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4943, "char_end": 5054, "patch_content": "", "patch_id": "ethereum/go-ethereum/3iRtdRH3", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "common/bitutil/compress.go", "_extra": {"func_start": 4699, "func_end": 5750, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2744, "char_end": 2799, "patch_content": "", "patch_id": "ethereum/go-ethereum/fxnLsgBo", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "common/bitutil/bitutil.go", "_extra": {"func_start": 2744, "func_end": 2834, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23463, "char_end": 23569, "patch_content": "", "patch_id": "ethereum/go-ethereum/GzNkUss5", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "accounts/abi/bind/backends/simulated.go", "_extra": {"func_start": 26477, "func_end": 27117, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5521, "char_end": 5572, "patch_content": "", "patch_id": "ethereum/go-ethereum/JfKz9svy", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "rlp/encode.go", "_extra": {"func_start": 5521, "func_end": 5572, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 940, "char_end": 968, "patch_content": "", "patch_id": "ethereum/go-ethereum/VHwU3jGc", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/bn256/google/gfp12.go", "_extra": {"func_start": 940, "func_end": 975, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8624, "char_end": 8669, "patch_content": "", "patch_id": "ethereum/go-ethereum/FvCpiioS", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "p2p/enode/localnode.go", "_extra": {"func_start": 8624, "func_end": 8670, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3924, "char_end": 4086, "patch_content": "", "patch_id": "ethereum/go-ethereum/UT42iL8m", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "ethclient/gethclient/gethclient.go", "_extra": {"func_start": 3171, "func_end": 4441, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5910, "char_end": 5957, "patch_content": "", "patch_id": "ethereum/go-ethereum/GPcp5kpm", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/bls12381/fp6.go", "_extra": {"func_start": 5910, "func_end": 6046, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5521, "char_end": 5605, "patch_content": "", "patch_id": "ethereum/go-ethereum/DwX9AeDX", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/vm/runtime/runtime.go", "_extra": {"func_start": 5502, "func_end": 6163, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3174, "char_end": 3291, "patch_content": "", "patch_id": "ethereum/go-ethereum/9SKUxqoX", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/rawdb/freezer_meta.go", "_extra": {"func_start": 2473, "func_end": 3405, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8888, "char_end": 8986, "patch_content": "", "patch_id": "ethereum/go-ethereum/c2s9AXaW", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "p2p/simulations/http.go", "_extra": {"func_start": 8202, "func_end": 8998, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2967, "char_end": 2989, "patch_content": "", "patch_id": "ethereum/go-ethereum/tZ9urqNu", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/bn256/google/gfp2.go", "_extra": {"func_start": 3048, "func_end": 3339, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2082, "char_end": 2121, "patch_content": "", "patch_id": "ethereum/go-ethereum/74ZVqND9", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/bn256/cloudflare/gfp2.go", "_extra": {"func_start": 1887, "func_end": 2202, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2228, "char_end": 2299, "patch_content": "", "patch_id": "ethereum/go-ethereum/c8YwqcUV", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/rawdb/chain_freezer.go", "_extra": {"func_start": 2086, "func_end": 2371, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18513, "char_end": 18550, "patch_content": "", "patch_id": "ethereum/go-ethereum/md9Afak9", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/rawdb/freezer_table.go", "_extra": {"func_start": 18339, "func_end": 19237, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1938, "char_end": 1990, "patch_content": "", "patch_id": "ethereum/go-ethereum/6dPixYh3", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/vm/memory_table.go", "_extra": {"func_start": 1938, "func_end": 1990, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3177, "char_end": 3248, "patch_content": "", "patch_id": "ethereum/go-ethereum/YJXmdLJ3", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "common/prque/lazyqueue.go", "_extra": {"func_start": 3155, "func_end": 3309, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1322, "char_end": 1407, "patch_content": "", "patch_id": "ethereum/go-ethereum/5qv2Qa44", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/bn256/cloudflare/gfp6.go", "_extra": {"func_start": 1278, "func_end": 1446, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1670, "char_end": 1686, "patch_content": "", "patch_id": "ethereum/go-ethereum/koWZzsVR", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "p2p/nat/natupnp.go", "_extra": {"func_start": 1567, "func_end": 1712, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3545, "char_end": 3592, "patch_content": "", "patch_id": "ethereum/go-ethereum/4LPA2tex", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/bls12381/fp2.go", "_extra": {"func_start": 3453, "func_end": 3593, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5859, "char_end": 5924, "patch_content": "", "patch_id": "ethereum/go-ethereum/habDdPFe", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "light/odr_util.go", "_extra": {"func_start": 5462, "func_end": 6709, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9815, "char_end": 9907, "patch_content": "", "patch_id": "ethereum/go-ethereum/8Sxo6hTA", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "cmd/devp2p/dns_route53.go", "_extra": {"func_start": 9815, "func_end": 9954, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 19475, "char_end": 19518, "patch_content": "", "patch_id": "ethereum/go-ethereum/8c7ETgZs", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "signer/core/api.go", "_extra": {"func_start": 19475, "func_end": 19518, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 19466, "char_end": 19510, "patch_content": "", "patch_id": "ethereum/go-ethereum/sUQ49GJM", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "node/node.go", "_extra": {"func_start": 19466, "func_end": 19510, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8118, "char_end": 8207, "patch_content": "", "patch_id": "ethereum/go-ethereum/9Bth3HUx", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "signer/core/signed_data.go", "_extra": {"func_start": 8118, "func_end": 8208, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1789, "char_end": 1804, "patch_content": "", "patch_id": "ethereum/go-ethereum/S7B9TKzV", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/bls12381/fp.go", "_extra": {"func_start": 1789, "func_end": 1804, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 25381, "char_end": 25436, "patch_content": "", "patch_id": "ethereum/go-ethereum/5P7phpKu", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "accounts/abi/bind/backends/simulated.go", "_extra": {"func_start": 25031, "func_end": 25661, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6307, "char_end": 6388, "patch_content": "", "patch_id": "ethereum/go-ethereum/NqwU99hn", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/state/snapshot/iterator.go", "_extra": {"func_start": 6237, "func_end": 6389, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3470, "char_end": 3553, "patch_content": "", "patch_id": "ethereum/go-ethereum/JxpAPsab", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "rlp/encbuffer.go", "_extra": {"func_start": 3436, "func_end": 3624, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 13599, "char_end": 13662, "patch_content": "", "patch_id": "ethereum/go-ethereum/TwyCWuhF", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "accounts/abi/bind/backends/simulated.go", "_extra": {"func_start": 13543, "func_end": 13802, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3795, "char_end": 3831, "patch_content": "", "patch_id": "ethereum/go-ethereum/bGhaUpbb", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/rawdb/accessors_indexes.go", "_extra": {"func_start": 4657, "func_end": 5510, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8775, "char_end": 8824, "patch_content": "", "patch_id": "ethereum/go-ethereum/CNt9ukQa", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "node/rpcstack.go", "_extra": {"func_start": 8665, "func_end": 8825, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3105, "char_end": 3122, "patch_content": "", "patch_id": "ethereum/go-ethereum/MmsnGLFd", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/rawdb/freezer_batch.go", "_extra": {"func_start": 3016, "func_end": 3137, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2108, "char_end": 2131, "patch_content": "", "patch_id": "ethereum/go-ethereum/GGwaqXSM", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/bn256/cloudflare/lattice.go", "_extra": {"func_start": 1913, "func_end": 2520, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2467, "char_end": 2520, "patch_content": "", "patch_id": "ethereum/go-ethereum/9BgJQsaU", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/bls12381/fp12.go", "_extra": {"func_start": 2467, "func_end": 2521, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16462, "char_end": 16519, "patch_content": "", "patch_id": "ethereum/go-ethereum/divMs3vY", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/vm/evm.go", "_extra": {"func_start": 16430, "func_end": 16520, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1104, "char_end": 1142, "patch_content": "", "patch_id": "ethereum/go-ethereum/sx3KK5ZH", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/bn256/google/gfp6.go", "_extra": {"func_start": 1104, "func_end": 1142, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6908, "char_end": 6947, "patch_content": "", "patch_id": "ethereum/go-ethereum/inHEgzgq", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/vm/instructions.go", "_extra": {"func_start": 6662, "func_end": 6947, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9227, "char_end": 9303, "patch_content": "", "patch_id": "ethereum/go-ethereum/m4GtzJHH", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "eth/fetcher/tx_fetcher.go", "_extra": {"func_start": 9227, "func_end": 9303, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11882, "char_end": 11932, "patch_content": "", "patch_id": "ethereum/go-ethereum/DqTbuqjZ", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/state/snapshot/conversion.go", "_extra": {"func_start": 11882, "func_end": 12305, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8094, "char_end": 8121, "patch_content": "", "patch_id": "ethereum/go-ethereum/fT4D58g5", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "p2p/dnsdisc/client.go", "_extra": {"func_start": 7791, "func_end": 8129, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3120, "char_end": 3168, "patch_content": "", "patch_id": "ethereum/go-ethereum/m6nFnRfe", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "common/lru/basiclru.go", "_extra": {"func_start": 3120, "func_end": 3169, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15694, "char_end": 15771, "patch_content": "", "patch_id": "ethereum/go-ethereum/BJoethSA", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "rlp/rlpgen/gen.go", "_extra": {"func_start": 15546, "func_end": 16230, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6588, "char_end": 6626, "patch_content": "", "patch_id": "ethereum/go-ethereum/ETfZ989z", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "rlp/encode.go", "_extra": {"func_start": 6588, "func_end": 6627, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 12193, "char_end": 12249, "patch_content": "", "patch_id": "ethereum/go-ethereum/qMFfD8pE", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "node/rpcstack.go", "_extra": {"func_start": 12116, "func_end": 12294, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2272, "char_end": 2348, "patch_content": "", "patch_id": "ethereum/go-ethereum/SgAKwSbT", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/types/tx_legacy.go", "_extra": {"func_start": 2166, "func_end": 2696, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7632, "char_end": 7648, "patch_content": "", "patch_id": "ethereum/go-ethereum/bjcb4MSY", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/state/snapshot/iterator_fast.go", "_extra": {"func_start": 7213, "func_end": 9088, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10972, "char_end": 11029, "patch_content": "", "patch_id": "ethereum/go-ethereum/edVeqZxn", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "p2p/simulations/http.go", "_extra": {"func_start": 10972, "func_end": 11029, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3789, "char_end": 3838, "patch_content": "", "patch_id": "ethereum/go-ethereum/eDVE34Fx", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/types/tx_dynamic_fee.go", "_extra": {"func_start": 3789, "func_end": 3838, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15163, "char_end": 15204, "patch_content": "", "patch_id": "ethereum/go-ethereum/A5ParB8q", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "ethdb/leveldb/leveldb.go", "_extra": {"func_start": 15163, "func_end": 15204, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 21012, "char_end": 21055, "patch_content": "", "patch_id": "ethereum/go-ethereum/m6dGTMkt", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/state/statedb.go", "_extra": {"func_start": 21012, "func_end": 21055, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 49364, "char_end": 49455, "patch_content": "", "patch_id": "ethereum/go-ethereum/9EeNdNiU", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/state/statedb.go", "_extra": {"func_start": 49308, "func_end": 49556, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 11490, "char_end": 11580, "patch_content": "", "patch_id": "ethereum/go-ethereum/d57vBXko", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/state/snapshot/iterator.go", "_extra": {"func_start": 11316, "func_end": 11787, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 16122, "char_end": 16185, "patch_content": "", "patch_id": "ethereum/go-ethereum/jwqEyyGi", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "node/node.go", "_extra": {"func_start": 15985, "func_end": 16185, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5113, "char_end": 5150, "patch_content": "", "patch_id": "ethereum/go-ethereum/aqQZyrTb", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/ecies/ecies.go", "_extra": {"func_start": 4864, "func_end": 5169, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18841, "char_end": 18865, "patch_content": "", "patch_id": "ethereum/go-ethereum/EW8r7Jce", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "ethdb/pebble/pebble.go", "_extra": {"func_start": 18841, "func_end": 18866, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9898, "char_end": 10059, "patch_content": "", "patch_id": "ethereum/go-ethereum/3PK4AyzA", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "rlp/rlpgen/gen.go", "_extra": {"func_start": 9832, "func_end": 10152, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 2772, "char_end": 2931, "patch_content": "", "patch_id": "ethereum/go-ethereum/gDE3toaC", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/state/trie_prefetcher.go", "_extra": {"func_start": 2035, "func_end": 2942, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 15057, "char_end": 15135, "patch_content": "", "patch_id": "ethereum/go-ethereum/KwSYW8aH", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "metrics/sample.go", "_extra": {"func_start": 15050, "func_end": 15351, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 9034, "char_end": 9098, "patch_content": "", "patch_id": "ethereum/go-ethereum/LFvfUzHb", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "rlp/encbuffer.go", "_extra": {"func_start": 9000, "func_end": 9181, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1862, "char_end": 1976, "patch_content": "", "patch_id": "ethereum/go-ethereum/ks8vuLun", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "cmd/utils/prompt.go", "_extra": {"func_start": 1862, "func_end": 2159, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4338, "char_end": 4369, "patch_content": "", "patch_id": "ethereum/go-ethereum/GZMosNbb", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/ecies/params.go", "_extra": {"func_start": 4338, "func_end": 4369, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5568, "char_end": 5606, "patch_content": "", "patch_id": "ethereum/go-ethereum/FNsjDPi8", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/ecies/ecies.go", "_extra": {"func_start": 5465, "func_end": 5606, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7763, "char_end": 7784, "patch_content": "", "patch_id": "ethereum/go-ethereum/WNXXgBK9", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "metrics/meter.go", "_extra": {"func_start": 7738, "func_end": 7784, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1148, "char_end": 1196, "patch_content": "", "patch_id": "ethereum/go-ethereum/b4aSwiZP", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "common/prque/prque.go", "_extra": {"func_start": 1148, "func_end": 1196, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6461, "char_end": 6499, "patch_content": "", "patch_id": "ethereum/go-ethereum/6biMT5c5", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/bls12381/fp6.go", "_extra": {"func_start": 6390, "func_end": 6517, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 14624, "char_end": 14696, "patch_content": "", "patch_id": "ethereum/go-ethereum/QvNaHK3K", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "p2p/rlpx/rlpx.go", "_extra": {"func_start": 14624, "func_end": 14696, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1247, "char_end": 1347, "patch_content": "", "patch_id": "ethereum/go-ethereum/rcSBWo2B", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "p2p/netutil/net.go", "_extra": {"func_start": 991, "func_end": 2485, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 5395, "char_end": 5450, "patch_content": "", "patch_id": "ethereum/go-ethereum/6Cvz4b9X", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "p2p/enode/nodedb.go", "_extra": {"func_start": 5165, "func_end": 5494, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1607, "char_end": 1694, "patch_content": "", "patch_id": "ethereum/go-ethereum/KP5uiSRv", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "event/feedof.go", "_extra": {"func_start": 1509, "func_end": 1694, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 29367, "char_end": 29456, "patch_content": "", "patch_id": "ethereum/go-ethereum/sQNh8nS8", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/state/statedb.go", "_extra": {"func_start": 29323, "func_end": 29457, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 620, "char_end": 681, "patch_content": "", "patch_id": "ethereum/go-ethereum/US54zX7y", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/bn256/google/gfp2.go", "_extra": {"func_start": 620, "func_end": 732, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 7315, "char_end": 7333, "patch_content": "", "patch_id": "ethereum/go-ethereum/E5nZjPn2", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/state/statedb.go", "_extra": {"func_start": 7294, "func_end": 7334, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1901, "char_end": 1957, "patch_content": "", "patch_id": "ethereum/go-ethereum/VMdUzfxE", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/blake2b/blake2b.go", "_extra": {"func_start": 1881, "func_end": 2005, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 23041, "char_end": 23085, "patch_content": "", "patch_id": "ethereum/go-ethereum/jrjyTj8c", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/rawdb/freezer_table.go", "_extra": {"func_start": 22745, "func_end": 23633, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 6573, "char_end": 6617, "patch_content": "", "patch_id": "ethereum/go-ethereum/gDc6QVjP", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "p2p/enode/nodedb.go", "_extra": {"func_start": 6507, "func_end": 6617, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 717, "char_end": 758, "patch_content": "", "patch_id": "ethereum/go-ethereum/aBip9NWp", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/bn256/cloudflare/gfp2.go", "_extra": {"func_start": 717, "func_end": 759, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4924, "char_end": 5030, "patch_content": "", "patch_id": "ethereum/go-ethereum/K2rmG9AJ", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "accounts/abi/bind/backends/simulated.go", "_extra": {"func_start": 4911, "func_end": 5302, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4532, "char_end": 4563, "patch_content": "", "patch_id": "ethereum/go-ethereum/bDuQwiYj", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/state/access_list.go", "_extra": {"func_start": 4532, "func_end": 4563, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 10328, "char_end": 10407, "patch_content": "", "patch_id": "ethereum/go-ethereum/SSLDnA4X", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "eth/filters/filter.go", "_extra": {"func_start": 10047, "func_end": 10408, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 8014, "char_end": 8038, "patch_content": "", "patch_id": "ethereum/go-ethereum/CitT3giB", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/bn256/google/bn256.go", "_extra": {"func_start": 7975, "func_end": 8038, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3536, "char_end": 3610, "patch_content": "", "patch_id": "ethereum/go-ethereum/Tx5K3CmT", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "p2p/dnsdisc/sync.go", "_extra": {"func_start": 3467, "func_end": 3610, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4101, "char_end": 4208, "patch_content": "", "patch_id": "ethereum/go-ethereum/SN6s8S8T", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/rawdb/accessors_chain.go", "_extra": {"func_start": 3928, "func_end": 4693, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 4183, "char_end": 4258, "patch_content": "", "patch_id": "ethereum/go-ethereum/HdBYUnhg", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "internal/utesting/utesting.go", "_extra": {"func_start": 4183, "func_end": 4302, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 3404, "char_end": 3407, "patch_content": "", "patch_id": "ethereum/go-ethereum/pA8uNQhB", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "cmd/utils/flags.go", "_extra": {"func_start": 37788, "func_end": 39121, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 21306, "char_end": 21369, "patch_content": "", "patch_id": "ethereum/go-ethereum/ea3UHNff", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "accounts/abi/bind/backends/simulated.go", "_extra": {"func_start": 20072, "func_end": 22381, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 18282, "char_end": 18371, "patch_content": "", "patch_id": "ethereum/go-ethereum/5PE75Rzm", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "core/types/transaction_signing.go", "_extra": {"func_start": 18188, "func_end": 18404, "mode": "2-3-lines"}}
{"file_content": "", "char_start": 1974, "char_end": 1998, "patch_content": "", "patch_id": "ethereum/go-ethereum/46EZgd3Q", "repository": "ethereum/go-ethereum", "commit_sha": "67979022aa8ea6a96b618c086f60b49af7d2a568", "file_name": "crypto/bls12381/fp12.go", "_extra": {"func_start": 1974, "func_end": 1998, "mode": "2-3-lines"}}

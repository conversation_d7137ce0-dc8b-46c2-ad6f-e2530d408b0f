# Polycoder pipeline

## Polycoder V2
This pipeline uses the Polycoder project's list of repos as a source
for per-language evaluation. The pipeline works as follows:
1. Using the index.zip list of files, collect the names of all repos and their representative language.
2. For each language, filter out any repos with less than 64 files.
3. Select 30 random repos for each language from the remaining sets.
4. <PERSON>lone each of those repos and filter out the files for the chosen language.
5. Archive the repos using lm_dataformat
6. Compress the archive into single file for processing by the evaluation harness.

To run the pipeline, check the top level paths in run_pipeline.sh, then execute:
`./run_pipeline`

## Polycoder V3
Polycoder V3 takes the output of V2 and splits it into two different archive files:
one contains source files for memories, the second contains source files for test.
Originally, we split the files at the beginning of evaluation, but we found that to
be inflexible when we wanted to use the source data for other purposes. Polycoder v3
captures the runtime split from the evaluation harness, and splits the v2 archive based
on those splits.

Furthermore, the pipeline is split into a "preparation" stage and the processing pipeline.
The preparation stage will generate the raw data (git repos) by cloning from github. This
was used to create the raw data for v2. Because we do not need to update the repos in this
version, just split them differently, we don't need to create a new raw set. The
preparation stage is left in the pipleline in case we want to regenerate the raw data
at a future time.

The script repo_split.py can be used to split a unified archive into per-repo test/memorize sets.
We used this to provide a dataset for fine-tuning, for example.

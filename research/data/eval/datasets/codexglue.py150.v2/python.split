fabric-engine/Kraken Python/kraken/plugins/si_plugin/utils/curves.py
fabric-engine/Kraken Python/kraken/core/objects/components/component_input.py
fabric-engine/Kraken Python/kraken/core/objects/rig.py
fabric-engine/Kraken Python/kraken/core/maths/mat44.py
fabric-engine/Kraken Python/kraken/core/objects/constraints/orientation_constraint.py
fabric-engine/Kraken tests/MathTests/vec4.py
fabric-engine/Kraken Python/kraken/plugins/maya_plugin/synchronizer.py
fabric-engine/Kraken tests/objects/control_offsets.py
fabric-engine/Kraken tests/ScriptTests/guideSync.py
fabric-engine/Kraken Python/kraken/core/io/kraken_saver.py
fabric-engine/Kraken Python/kraken/core/profiler.py
fabric-engine/Kraken Python/kraken/core/objects/container.py
fabric-engine/Kraken Python/kraken/ui/GraphView/graph_commands.py
fabric-engine/Kraken Python/kraken/ui/GraphView/pyflowgraph/node.py
fabric-engine/Kraken tests/RigTests/armGuideSync.py
fabric-engine/Kraken Python/kraken/core/objects/transform.py
fabric-engine/Kraken tests/MathTests/color.py
fabric-engine/Kraken Python/kraken/ui/HAppkit_Editors/editor_widgets/array_editor.py
fabric-engine/Kraken tests/customConfig/arm_build_with_custom_config.py
fabric-engine/Kraken Python/kraken/core/objects/constraints/constraint.py
fabric-engine/Kraken Python/kraken/ui/HAppkit_Editors/editor_widgets/nested_editor.py
fabric-engine/Kraken tests/ComponentTests/spine_build.py
fabric-engine/Kraken tests/RigTests/bobGuideSync.py
fabric-engine/Kraken Python/kraken/core/objects/operators/operator.py
fabric-engine/Kraken Python/kraken/core/maths/rotation_order.py
fabric-engine/Kraken Python/kraken/ui/HAppkit_Editors/editor_widgets/list_view_editor.py
fabric-engine/Kraken Python/kraken/plugins/maya_plugin/__init__.py
spulec/moto tests/test_swf/models/test_timeout.py
spulec/moto moto/awslambda/urls.py
spulec/moto tests/test_s3/test_server.py
spulec/moto tests/test_swf/models/test_history_event.py
spulec/moto moto/ec2/models.py
spulec/moto moto/ec2/responses/route_tables.py
spulec/moto moto/s3bucket_path/__init__.py
spulec/moto moto/ec2/responses/customer_gateways.py
spulec/moto moto/redshift/urls.py
spulec/moto moto/swf/exceptions.py
spulec/moto moto/rds/exceptions.py
spulec/moto moto/opsworks/responses.py
spulec/moto tests/test_iam/test_iam_groups.py
spulec/moto tests/test_sqs/test_sqs.py
spulec/moto tests/helpers.py
spulec/moto moto/ec2/responses/vm_export.py
spulec/moto moto/swf/models/generic_type.py
spulec/moto moto/awslambda/responses.py
spulec/moto moto/ec2/responses/internet_gateways.py
spulec/moto moto/s3/urls.py
spulec/moto tests/test_ec2/test_availability_zones_and_regions.py
spulec/moto tests/test_cloudformation/test_cloudformation_stack_integration.py
spulec/moto moto/ec2/responses/tags.py
spulec/moto moto/iam/models.py
spulec/moto moto/glacier/models.py
spulec/moto tests/test_ec2/test_virtual_private_gateways.py
spulec/moto moto/dynamodb/comparisons.py
spulec/moto moto/sqs/__init__.py
spulec/moto moto/opsworks/urls.py
spulec/moto moto/kinesis/models.py
spulec/moto moto/ec2/responses/elastic_block_store.py
spulec/moto moto/s3bucket_path/utils.py
spulec/moto tests/test_glacier/test_glacier_vaults.py
spulec/moto moto/ec2/responses/__init__.py
spulec/moto tests/test_core/test_utils.py
spulec/moto tests/test_cloudformation/test_server.py
spulec/moto moto/sqs/models.py
spulec/moto tests/test_elb/test_elb.py
spulec/moto tests/test_ses/test_ses.py
mozilla/moztrap tests/model/tags/models/test_tag.py
mozilla/moztrap tests/view/markup/templatetags/test_markup.py
mozilla/moztrap moztrap/view/results/runcaseversions/views.py
mozilla/moztrap moztrap/view/manage/runs/forms.py
mozilla/moztrap moztrap/view/manage/products/forms.py
mozilla/moztrap moztrap/view/manage/tags/views.py
mozilla/moztrap moztrap/model/environments/admin.py
mozilla/moztrap tests/model/core/admin/test_product.py
mozilla/moztrap moztrap/model/execution/migrations/0003_auto.py
mozilla/moztrap moztrap/model/core/migrations/0003_auto__add_field_productversion_cc_version__add_field_product.py
mozilla/moztrap moztrap/view/users/context_processors.py
mozilla/moztrap moztrap/model/library/api.py
mozilla/moztrap tests/view/results/results/test_views.py
mozilla/moztrap moztrap/model/execution/migrations/0004_auto__chg_field_result_started.py
mozilla/moztrap tests/model/library/admin/test_case.py
mozilla/moztrap tests/view/manage/test_filters.py
mozilla/moztrap moztrap/view/lists/cases.py
mozilla/moztrap tests/view/templatetags/test_urls.py
mozilla/moztrap moztrap/model/core/management/commands/generate_fixture.py
mozilla/moztrap moztrap/view/lists/finder.py
mozilla/moztrap moztrap/view/api/speedy.py
mozilla/moztrap tests/model/execution/admin/test_result.py
mozilla/moztrap moztrap/view/filters.py
mozilla/moztrap moztrap/model/library/importer.py
mozilla/moztrap tests/model/library/api/test_caseversion_resource.py
mozilla/moztrap tests/model/execution/api/test_runcaseversion.py
mozilla/moztrap moztrap/view/urls.py
mozilla/moztrap moztrap/view/users/browserid_urls.py
mozilla/moztrap moztrap/model/environments/models.py
mozilla/moztrap tests/view/lists/templatetags/test_filters.py
mozilla/moztrap moztrap/model/library/migrations/0008_auto__add_field_case_priority.py
mozilla/moztrap moztrap/model/execution/api.py
mozilla/moztrap tests/view/manage/tags/test_forms.py
yasoob/youtube-dl-GUI youtube_dl/extractor/archiveorg.py
yasoob/youtube-dl-GUI youtube_dl/extractor/pyvideo.py
yasoob/youtube-dl-GUI youtube_dl/extractor/dreisat.py
yasoob/youtube-dl-GUI youtube_dl/extractor/testurl.py
yasoob/youtube-dl-GUI youtube_dl/extractor/tmz.py
yasoob/youtube-dl-GUI youtube_dl/extractor/tenplay.py
yasoob/youtube-dl-GUI youtube_dl/extractor/empflix.py
yasoob/youtube-dl-GUI youtube_dl/extractor/tube8.py
yasoob/youtube-dl-GUI youtube_dl/extractor/yesjapan.py
yasoob/youtube-dl-GUI youtube_dl/extractor/ro220.py
yasoob/youtube-dl-GUI youtube_dl/extractor/videodetective.py
yasoob/youtube-dl-GUI youtube_dl/extractor/ubu.py
yasoob/youtube-dl-GUI youtube_dl/extractor/toypics.py
yasoob/youtube-dl-GUI youtube_dl/extractor/fc2.py
yasoob/youtube-dl-GUI youtube_dl/extractor/vuclip.py
yasoob/youtube-dl-GUI youtube_dl/extractor/discovery.py
yasoob/youtube-dl-GUI youtube_dl/extractor/golem.py
yasoob/youtube-dl-GUI youtube_dl/postprocessor/atomicparsley.py
yasoob/youtube-dl-GUI youtube_dl/extractor/bloomberg.py
yasoob/youtube-dl-GUI youtube_dl/extractor/chilloutzone.py
yasoob/youtube-dl-GUI youtube_dl/extractor/worldstarhiphop.py
yasoob/youtube-dl-GUI youtube_dl/extractor/played.py
yasoob/youtube-dl-GUI youtube_dl/extractor/vporn.py
yasoob/youtube-dl-GUI youtube_dl/extractor/grooveshark.py
yasoob/youtube-dl-GUI youtube_dl/postprocessor/ffmpeg.py
yasoob/youtube-dl-GUI youtube_dl/extractor/hypem.py
yasoob/youtube-dl-GUI build/bdist.win-amd64/winexe/temp/win32pipe.py
yasoob/youtube-dl-GUI youtube_dl/extractor/eporner.py
yasoob/youtube-dl-GUI youtube_dl/extractor/cnet.py
yasoob/youtube-dl-GUI youtube_dl/postprocessor/xattrpp.py
yasoob/youtube-dl-GUI youtube_dl/extractor/nhl.py
yasoob/youtube-dl-GUI youtube_dl/extractor/fktv.py
yasoob/youtube-dl-GUI youtube_dl/extractor/atttechchannel.py
yasoob/youtube-dl-GUI youtube_dl/extractor/ministrygrid.py
yasoob/youtube-dl-GUI youtube_dl/downloader/mplayer.py
yasoob/youtube-dl-GUI youtube_dl/extractor/huffpost.py
yasoob/youtube-dl-GUI youtube_dl/extractor/mooshare.py
yasoob/youtube-dl-GUI youtube_dl/extractor/comedycentral.py
yasoob/youtube-dl-GUI youtube_dl/extractor/glide.py
EricssonResearch/calvin-base calvin/actorstore/systemactors/net/UDPListener.py
EricssonResearch/calvin-base calvin/utilities/dynops.py
EricssonResearch/calvin-base calvin/examples/display_sensors/devactors/sensor/Environmental.py
EricssonResearch/calvin-base calvin/actorstore/systemactors/net/HTTPPost.py
EricssonResearch/calvin-base calvin/tests/test_security.py
EricssonResearch/calvin-base calvin/actorstore/systemactors/exception/IsEOS.py
EricssonResearch/calvin-base calvin/runtime/north/storage.py
EricssonResearch/calvin-base calvin/actorstore/systemactors/media/MediaPlayer.py
EricssonResearch/calvin-base calvin/actorstore/systemactors/json/List.py
EricssonResearch/calvin-base calvin/calvinsys/io/gpiohandler.py
EricssonResearch/calvin-base calvin/tests/test_endpoint.py
EricssonResearch/calvin-base calvin/runtime/south/endpoint.py
EricssonResearch/calvin-base calvin/utilities/calvinlogger.py
EricssonResearch/calvin-base calvin/calvinsys/media/camerahandler.py
EricssonResearch/calvin-base calvin/runtime/south/plugins/transports/calvinip/__init__.py
EricssonResearch/calvin-base calvin/actorstore/systemactors/std/Stringify.py
EricssonResearch/calvin-base calvin/actorstore/systemactors/json/GetValue.py
EricssonResearch/calvin-base calvin/runtime/south/plugins/io/display/platform/raspberry_pi/sensehat_impl/display.py
EricssonResearch/calvin-base calvin/actor/actorport.py
EricssonResearch/calvin-base calvin/actorstore/systemactors/std/RecTimer.py
EricssonResearch/calvin-base calvin/tutorial/erct/Mult.py
EricssonResearch/calvin-base calvin/actorstore/systemactors/media/ImageRenderer.py
EricssonResearch/calvin-base calvin/runtime/north/plugins/requirements/shadow_actor_reqs_match.py
EricssonResearch/calvin-base calvin/runtime/south/plugins/storage/twistedimpl/securedht/append_server.py
EricssonResearch/calvin-base calvin/runtime/north/plugins/requirements/all_nodes.py
EricssonResearch/calvin-base calvin/actorstore/systemactors/json/FromString.py
EricssonResearch/calvin-base calvin/tests/test_actorport.py
EricssonResearch/calvin-base calvin/examples/display_sensors/devactors/io/Display.py
EricssonResearch/calvin-base calvin/utilities/utils.py
EricssonResearch/calvin-base calvin/runtime/south/plugins/media/defaultimpl/mediaplayer.py
EricssonResearch/calvin-base calvin/actorstore/systemactors/io/FileReader.py
EricssonResearch/calvin-base calvin/runtime/south/plugins/storage/twistedimpl/securedht/tests/test_dht_server_nice4.py
EricssonResearch/calvin-base calvin/examples/webserver/actors/io/FileReader2.py
EricssonResearch/calvin-base calvin/actorstore/systemactors/std/Constant.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/future/backports/test/support.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/future/moves/tkinter/messagebox.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/future/moves/tkinter/constants.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/future/backports/email/encoders.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/future/moves/http/cookies.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/pyqode/core/styles/qt.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/pyqode/core/_forms/search_panel_ui.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/pyqode/core/backend/server.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/pyqode/core/modes/line_highlighter.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/future/moves/copyreg.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/future/moves/sys.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/future/moves/dbm/ndbm.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/pyqode/core/widgets/filesystem_treeview.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/pyqode/core/modes/caret_line_highlight.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/pyqode/core/modes/occurences.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/future/moves/tkinter/font.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/future/utils/__init__.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/future/backports/http/client.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/pyqode/core/modes/autoindent.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/future/moves/urllib/response.py
OpenCobolIDE/OpenCobolIDE scripts/install-qt.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/pyqode/core/panels/encodings.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/pyqode/core/managers/__init__.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/future/moves/tkinter/colorchooser.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/future/backports/email/feedparser.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/pyqode/core/managers/modes.py
OpenCobolIDE/OpenCobolIDE open_cobol_ide/extlibs/pyqode/core/_forms/dlg_unsaved_files_ui.py
openstack/solum solum/tests/common/test_service.py
openstack/solum solum/api/handlers/camp/attribute_definition_handler.py
openstack/solum solum/deployer/api.py
openstack/solum solum/tests/api/controllers/v1/test_sensor.py
openstack/solum solum/common/solum_barbicanclient.py
openstack/solum solum/common/solum_keystoneclient.py
openstack/solum solum/api/controllers/camp/v1_1/datamodel/type_definitions.py
openstack/solum solum/objects/service.py
openstack/solum solum/objects/plan.py
openstack/solum solum/api/release.py
openstack/solum solum/conductor/handlers/default.py
openstack/solum solum/tests/api/camp/v1_1/test_services.py
openstack/solum solum/common/rpc/service.py
openstack/solum solum/api/controllers/camp/v1_1/type_definitions.py
openstack/solum solum/objects/sqlalchemy/extension.py
openstack/solum solum/api/handlers/camp/parameter_definitions_handler.py
openstack/solum solum/common/exception.py
openstack/solum solum/common/config.py
openstack/solum solum/openstack/common/importutils.py
openstack/solum solum/api/handlers/sensor_handler.py
openstack/solum solum/tests/api/handlers/test_extension.py
openstack/solum solum/worker/handlers/default.py
openstack/solum solum/common/repo_utils.py
openstack/solum solum/openstack/common/fileutils.py
openstack/solum solum/api/handlers/assembly_handler.py
openstack/solum solum/uploaders/local.py
openstack/solum solum/api/controllers/v1/language_pack.py
openstack/solum solum/tests/common/test_clients.py
openstack/solum solum/objects/sqlalchemy/migration/alembic_migrations/versions/5583c6e70156_move_triggers_from_assembly_to_plan.py
openstack/solum solum/api/controllers/v1/datamodel/extension.py
openstack/solum solum/worker/app_handlers/utils.py
openstack/solum solum/tests/api/controllers/v1/test_userlog.py
openstack/solum solum/api/controllers/v1/datamodel/app.py
openstack/solum solum/tests/common/test_yamlutils.py
openstack/solum solum/api/controllers/v1/datamodel/plan.py
openstack/solum solum/api/controllers/camp/v1_1/datamodel/parameter_definitions.py
django-oscar/django-oscar tests/integration/partner/selector_mixin_tests.py
django-oscar/django-oscar src/oscar/apps/search/facets.py
django-oscar/django-oscar src/oscar/templatetags/string_filters.py
django-oscar/django-oscar src/oscar/apps/order/migrations/0002_auto_20141007_2032.py
django-oscar/django-oscar src/oscar/apps/customer/models.py
django-oscar/django-oscar tests/integration/basket/form_tests.py
django-oscar/django-oscar tests/integration/offer/combination_tests.py
django-oscar/django-oscar tests/integration/offer/post_order_action_tests.py
django-oscar/django-oscar tests/unit/offer/benefit_tests.py
django-oscar/django-oscar src/oscar/apps/search/app.py
django-oscar/django-oscar src/oscar/apps/dashboard/orders/views.py
django-oscar/django-oscar src/oscar/apps/catalogue/receivers.py
django-oscar/django-oscar src/oscar/apps/catalogue/categories.py
django-oscar/django-oscar src/oscar/apps/partner/migrations/0004_auto_20160107_1755.py
django-oscar/django-oscar src/oscar/apps/dashboard/reports/utils.py
django-oscar/django-oscar src/oscar/apps/voucher/admin.py
django-oscar/django-oscar src/oscar/apps/search/config.py
django-oscar/django-oscar tests/functional/customer/wishlists_tests.py
django-oscar/django-oscar src/oscar/apps/search/views.py
django-oscar/django-oscar src/oscar/test/contextmanagers.py
django-oscar/django-oscar tests/functional/dashboard/order_tests.py
django-oscar/django-oscar src/oscar/apps/catalogue/reviews/managers.py
django-oscar/django-oscar src/oscar/apps/dashboard/offers/config.py
django-oscar/django-oscar tests/unit/shipping/model_tests.py
django-oscar/django-oscar src/oscar/apps/promotions/layout.py
django-oscar/django-oscar src/oscar/apps/basket/config.py
django-oscar/django-oscar src/oscar/apps/dashboard/nav.py
django-oscar/django-oscar src/oscar/apps/wishlists/__init__.py
django-oscar/django-oscar src/oscar/apps/partner/abstract_models.py
django-oscar/django-oscar src/oscar/apps/dashboard/catalogue/widgets.py
django-oscar/django-oscar src/oscar/templatetags/dashboard_tags.py
django-oscar/django-oscar src/oscar/apps/dashboard/offers/views.py
django-oscar/django-oscar src/oscar/apps/voucher/receivers.py
django-oscar/django-oscar src/oscar/apps/payment/config.py
django-oscar/django-oscar src/oscar/test/factories/basket.py
django-oscar/django-oscar src/oscar/apps/customer/app.py
django-oscar/django-oscar src/oscar/apps/order/admin.py
django-oscar/django-oscar src/oscar/apps/catalogue/reviews/app.py
django-oscar/django-oscar tests/integration/offer/percentage_benefit_tests.py
django-oscar/django-oscar tests/functional/dashboard/communication_tests.py
django-oscar/django-oscar src/oscar/apps/analytics/config.py
django-oscar/django-oscar src/oscar/apps/catalogue/exceptions.py
django-oscar/django-oscar src/oscar/apps/dashboard/users/__init__.py
django-oscar/django-oscar tests/unit/wishlist_tests.py
django-oscar/django-oscar src/oscar/apps/dashboard/pages/views.py
django-oscar/django-oscar src/oscar/test/factories/utils.py
django-oscar/django-oscar src/oscar/apps/wishlists/migrations/0001_initial.py
django-oscar/django-oscar tests/unit/dashboard/catalogue_form_tests.py
django-oscar/django-oscar src/oscar/apps/offer/migrations/0002_auto_20151210_1053.py
django-oscar/django-oscar src/oscar/apps/basket/signals.py
django-oscar/django-oscar src/oscar/apps/order/abstract_models.py
django-oscar/django-oscar src/oscar/apps/dashboard/shipping/app.py
django-oscar/django-oscar src/oscar/management/commands/oscar_update_product_ratings.py
django-oscar/django-oscar tests/unit/catalogue/test_models.py
django-oscar/django-oscar src/oscar/apps/checkout/forms.py
django-oscar/django-oscar src/oscar/apps/address/config.py
django-oscar/django-oscar src/oscar/apps/dashboard/reports/config.py
django-oscar/django-oscar tests/functional/customer/auth_tests.py
django-oscar/django-oscar src/oscar/apps/catalogue/reviews/migrations/0001_initial.py
django-oscar/django-oscar src/oscar/apps/customer/notifications/services.py
django-oscar/django-oscar src/oscar/test/factories/order.py
django-oscar/django-oscar tests/integration/partner/strategy_tests.py
django-oscar/django-oscar src/oscar/management/commands/oscar_calculate_scores.py
robotframework/robotframework src/robot/writer/datafilewriter.py
robotframework/robotframework utest/resources/Listener.py
robotframework/robotframework atest/testresources/testlibs/BinaryDataLibrary.py
robotframework/robotframework src/robot/utils/text.py
robotframework/robotframework src/robot/variables/assigner.py
robotframework/robotframework utest/running/test_imports.py
robotframework/robotframework atest/interpreter.py
robotframework/robotframework atest/testdata/standard_libraries/telnet/telnet_variables.py
robotframework/robotframework src/robot/conf/settings.py
robotframework/robotframework atest/testresources/testlibs/pythonmodule/__init__.py
robotframework/robotframework atest/testdata/test_libraries/ThreadLoggingLib.py
robotframework/robotframework src/robot/libraries/XML.py
robotframework/robotframework atest/testdata/variables/InvalidClass.py
robotframework/robotframework utest/output/test_logger.py
robotframework/robotframework src/robot/parsing/restsupport.py
robotframework/robotframework src/robot/model/keyword.py
robotframework/robotframework atest/testdata/test_libraries/PrintLib.py
robotframework/robotframework atest/testdata/standard_libraries/builtin/import_resource_vars.py
robotframework/robotframework src/robot/running/usererrorhandler.py
robotframework/robotframework atest/testdata/keywords/library_with_keywords_with_dots_in_name.py
robotframework/robotframework utest/resources/__init__.py
robotframework/robotframework atest/testdata/test_libraries/as_listener/module_v1_listenerlibrary.py
robotframework/robotframework atest/testdata/standard_libraries/builtin/get_user_dict.py
robotframework/robotframework atest/testdata/keywords/library/with/dots/__init__.py
robotframework/robotframework src/robot/libdocpkg/output.py
robotframework/robotframework src/robot/libraries/Screenshot.py
robotframework/robotframework src/robot/output/console/highlighting.py
robotframework/robotframework utest/reporting/test_jswriter.py
robotframework/robotframework atest/testdata/keywords/resources/RecLibrary1.py
robotframework/robotframework src/robot/errors.py
robotframework/robotframework src/robot/running/arguments/__init__.py
robotframework/robotframework atest/testresources/testlibs/ExtendPythonLib.py
robotframework/robotframework atest/testresources/testlibs/Exceptions.py
robotframework/robotframework atest/testdata/variables/DynamicPythonClass.py
robotframework/robotframework atest/testdata/keywords/library/with/dots/in/name/__init__.py
robotframework/robotframework utest/utils/test_robottime.py
robotframework/robotframework src/robot/utils/__init__.py
robotframework/robotframework atest/testdata/variables/automatic_variables/HelperLib.py
robotframework/robotframework src/robot/output/filelogger.py
robotframework/robotframework src/robot/output/pyloggingconf.py
robotframework/robotframework utest/utils/test_asserts.py
robotframework/robotframework atest/testresources/listeners/attributeverifyinglistener.py
robotframework/robotframework src/robot/htmldata/testdata/create_libdoc_data.py
robotframework/robotframework atest/testdata/standard_libraries/builtin/reload_library/module_library.py
robotframework/robotframework atest/testresources/testlibs/newstyleclasses.py
robotframework/robotframework src/robot/output/stdoutlogsplitter.py
robotframework/robotframework atest/testdata/test_libraries/InitImportingAndIniting.py
robotframework/robotframework atest/testdata/standard_libraries/builtin/variables_to_import_2.py
robotframework/robotframework atest/testdata/variables/list_and_dict_variable_file.py
robotframework/robotframework src/robot/result/merger.py
robotframework/robotframework src/robot/model/visitor.py
robotframework/robotframework atest/testdata/standard_libraries/remote/dictresult.py
robotframework/robotframework utest/api/test_using_libraries.py
robotframework/robotframework atest/testdata/standard_libraries/builtin/vars_for_get_variables.py
robotframework/robotframework atest/testdata/standard_libraries/builtin/times.py
robotframework/robotframework atest/robot/cli/model_modifiers/ModelModifier.py
iskandr/parakeet parakeet/transforms/subst.py
iskandr/parakeet parakeet/builder/adverb_builder.py
iskandr/parakeet test/arrays/test_empty.py
iskandr/parakeet parakeet/c_backend/run_function.py
iskandr/parakeet test/algorithms/test_histogram_intersection.py
iskandr/parakeet test/algorithms/test_harris_corner.py
iskandr/parakeet parakeet/value_specialization/abstract_value.py
iskandr/parakeet parakeet/openmp_backend/multicore_compiler.py
iskandr/parakeet parakeet/ndtypes/array_type.py
iskandr/parakeet parakeet/type_inference/var_map.py
iskandr/parakeet parakeet/transforms/licm.py
iskandr/parakeet parakeet/builder/builder.py
iskandr/parakeet parakeet/value_specialization/find_constant_values.py
iskandr/parakeet parakeet/transforms/vectorize.py
iskandr/parakeet test/compiler/test_shape_inference.py
iskandr/parakeet benchmarks/julia.py
iskandr/parakeet parakeet/type_inference/linearize_args.py
iskandr/parakeet parakeet/value_specialization/value_specialization.py
iskandr/parakeet parakeet/lib/linalg.py
iskandr/parakeet parakeet/llvm_backend/llvm_config.py
iskandr/parakeet parakeet/ndtypes/__init__.py
iskandr/parakeet parakeet/type_inference/__init__.py
iskandr/parakeet parakeet/c_backend/system_info.py
iskandr/parakeet test/algorithms/test_matmult_allpairs.py
iskandr/parakeet benchmarks/matmult.py
iskandr/parakeet parakeet/c_backend/__init__.py
iskandr/parakeet parakeet/lib/array_constructors.py
iskandr/parakeet parakeet/ndtypes/ptr_type.py
iskandr/parakeet parakeet/syntax/adverbs.py
iskandr/parakeet parakeet/analysis/index_elim_analysis.py
iskandr/parakeet parakeet/frontend/run_function.py
iskandr/parakeet parakeet/syntax/stmt.py
iskandr/parakeet parakeet/ndtypes/fn_type.py
iskandr/parakeet parakeet/builder/loop_builder.py
iskandr/parakeet test/numpy_lib/test_linspace.py
iskandr/parakeet parakeet/c_backend/fn_compiler.py
iskandr/parakeet test/compiler/test_escape_analysis.py
iskandr/parakeet benchmarks/morphology.py
iskandr/parakeet parakeet/frontend/decorators.py
iskandr/parakeet test/algorithms/test_nn.py
torchbox/wagtail wagtail/tests/snippets/migrations/0002_searchablesnippet.py
torchbox/wagtail wagtail/wagtaildocs/__init__.py
torchbox/wagtail wagtail/wagtailcore/migrations/0018_pagerevision_submitted_for_moderation_index.py
torchbox/wagtail wagtail/wagtailforms/views.py
torchbox/wagtail wagtail/wagtailembeds/migrations/0003_capitalizeverbose.py
torchbox/wagtail wagtail/wagtailcore/__init__.py
torchbox/wagtail wagtail/contrib/settings/forms.py
torchbox/wagtail wagtail/wagtailembeds/views/chooser.py
torchbox/wagtail wagtail/wagtailusers/wagtail_hooks.py
torchbox/wagtail wagtail/wagtailembeds/finders/embedly.py
torchbox/wagtail wagtail/wagtailadmin/tests/test_collections_views.py
torchbox/wagtail wagtail/wagtailsearch/management/commands/search_garbage_collect.py
torchbox/wagtail wagtail/wagtailcore/views.py
torchbox/wagtail wagtail/wagtailcore/migrations/0022_add_site_name.py
torchbox/wagtail wagtail/utils/sendfile_streaming_backend.py
torchbox/wagtail wagtail/tests/testapp/wagtail_hooks.py
torchbox/wagtail wagtail/wagtaildocs/migrations/0004_capitalizeverbose.py
torchbox/wagtail wagtail/wagtaildocs/views/documents.py
torchbox/wagtail wagtail/contrib/wagtailsearchpromotions/models.py
torchbox/wagtail wagtail/wagtailcore/migrations/0006_add_lock_page_permission.py
torchbox/wagtail wagtail/tests/testapp/migrations/0003_onetoonepage.py
torchbox/wagtail wagtail/contrib/wagtailsearchpromotions/__init__.py
torchbox/wagtail wagtail/wagtailadmin/templatetags/gravatar.py
torchbox/wagtail wagtail/contrib/modeladmin/menus.py
torchbox/wagtail wagtail/contrib/wagtailsitemaps/__init__.py
torchbox/wagtail wagtail/wagtailredirects/migrations/0003_make_site_field_editable.py
torchbox/wagtail wagtail/wagtailembeds/models.py
torchbox/wagtail wagtail/api/apps.py
torchbox/wagtail wagtail/wagtailredirects/apps.py
torchbox/wagtail wagtail/wagtailcore/migrations/0027_fix_collection_path_collation.py
torchbox/wagtail wagtail/wagtailimages/templatetags/wagtailimages_tags.py
torchbox/wagtail wagtail/wagtailsnippets/views/chooser.py
torchbox/wagtail wagtail/wagtailsnippets/permissions.py
torchbox/wagtail wagtail/wagtailimages/migrations/0001_initial.py
torchbox/wagtail wagtail/contrib/settings/tests/test_templates.py
torchbox/wagtail wagtail/wagtaildocs/wagtail_hooks.py
torchbox/wagtail wagtail/contrib/wagtailsearchpromotions/migrations/0002_capitalizeverbose.py
torchbox/wagtail wagtail/tests/search/models.py
torchbox/wagtail wagtail/wagtailimages/formats.py
torchbox/wagtail wagtail/api/v2/endpoints.py
torchbox/wagtail wagtail/wagtailimages/rect.py
torchbox/wagtail wagtail/wagtailcore/fields.py
torchbox/wagtail wagtail/wagtaildocs/blocks.py
torchbox/wagtail wagtail/wagtailadmin/migrations/0001_create_admin_access_permissions.py
torchbox/wagtail wagtail/wagtailsearch/index.py
torchbox/wagtail wagtail/wagtailsearch/views/frontend.py
torchbox/wagtail wagtail/wagtailadmin/templatetags/wagtailuserbar.py
torchbox/wagtail wagtail/wagtailusers/__init__.py
torchbox/wagtail wagtail/wagtailusers/urls/users.py
torchbox/wagtail wagtail/tests/routablepage/models.py
torchbox/wagtail wagtail/project_template/home/<USER>
torchbox/wagtail wagtail/wagtailcore/permission_policies/collections.py
torchbox/wagtail wagtail/wagtailcore/migrations/0021_capitalizeverbose.py
torchbox/wagtail wagtail/contrib/wagtailfrontendcache/tests.py
glue-viz/glue glue/core/roi.py
glue-viz/glue glue/utils/qt/tests/test_dialogs.py
glue-viz/glue glue/app/qt/versions.py
glue-viz/glue glue/app/qt/mdi_area.py
glue-viz/glue glue/core/tests/test_hub.py
glue-viz/glue glue/viewers/image/__init__.py
glue-viz/glue glue/viewers/common/qt/data_viewer.py
glue-viz/glue glue/external/pvextractor/pvextractor.py
glue-viz/glue glue/external/wcsaxes/axislabels.py
glue-viz/glue glue/core/edit_subset_mode.py
glue-viz/glue glue/core/qt/tests/test_data_combo_helper.py
glue-viz/glue glue/core/data_factories/npy.py
glue-viz/glue glue/core/data_factories/fits.py
glue-viz/glue glue/core/parse.py
glue-viz/glue glue/viewers/table/__init__.py
glue-viz/glue glue/viewers/scatter/layer_artist.py
glue-viz/glue glue/viewers/common/qt/tests/test_toolbar.py
glue-viz/glue glue/core/tests/test_component_link.py
glue-viz/glue glue/tests/helpers.py
glue-viz/glue glue/core/util.py
glue-viz/glue glue/plugins/dendro_viewer/data_factory.py
glue-viz/glue glue/main.py
glue-viz/glue glue/viewers/custom/qt/tests/test_custom_viewer.py
glue-viz/glue glue/core/qt/tests/test_data_collection_model.py
glue-viz/glue glue/external/wcsaxes/core.py
glue-viz/glue glue/viewers/scatter/tests/test_layer_artist.py
glue-viz/glue glue/core/qt/style_dialog.py
glue-viz/glue glue/viewers/common/qt/tests/test_mouse_mode.py
glue-viz/glue glue/external/pvextractor/gui.py
glue-viz/glue glue/viewers/histogram/client.py
glue-viz/glue glue/default_config.py
glue-viz/glue glue/core/aggregate.py
glue-viz/glue glue/plugins/export_plotly.py
glue-viz/glue glue/viewers/image/qt/viewer_widget.py
glue-viz/glue glue/utils/tests/test_geometry.py
glue-viz/glue glue/app/qt/tests/test_terminal.py
glue-viz/glue glue/viewers/image/qt/rgb_edit.py
openhatch/oh-mainline vendor/packages/twisted/doc/core/howto/tutorial/listings/finger/finger02.py
openhatch/oh-mainline vendor/packages/scrapy/scrapy/contrib/downloadermiddleware/chunked.py
openhatch/oh-mainline vendor/packages/Django/django/contrib/gis/tests/geoadmin/tests.py
openhatch/oh-mainline vendor/packages/whoosh/src/whoosh/qparser/taggers.py
openhatch/oh-mainline vendor/packages/whoosh/src/whoosh/qparser/syntax.py
openhatch/oh-mainline vendor/packages/twisted/doc/core/examples/courier.py
openhatch/oh-mainline vendor/packages/Django/django/contrib/auth/admin.py
openhatch/oh-mainline vendor/packages/twisted/doc/core/examples/pbsimple.py
openhatch/oh-mainline vendor/packages/twisted/doc/core/benchmarks/netstringreceiver.py
openhatch/oh-mainline vendor/packages/twisted/doc/web/howto/listings/wait_for_it.py
openhatch/oh-mainline vendor/packages/twisted/doc/core/howto/tutorial/listings/finger/finger/tap.py
openhatch/oh-mainline vendor/packages/twisted/doc/core/howto/listings/pb/copy2_receiver.py
openhatch/oh-mainline vendor/packages/whoosh/src/whoosh/support/levenshtein.py
openhatch/oh-mainline vendor/packages/twisted/doc/core/howto/listings/pb/pb2server.py
openhatch/oh-mainline vendor/packages/whoosh/src/whoosh/lang/porter2.py
openhatch/oh-mainline vendor/packages/twisted/doc/core/howto/listings/pb/pb4client.py
openhatch/oh-mainline vendor/packages/Django/django/middleware/csrf.py
openhatch/oh-mainline vendor/packages/twisted/doc/web/howto/listings/render_1.py
openhatch/oh-mainline vendor/packages/twisted/doc/core/howto/listings/TwistedQuotes/quoters.py
openhatch/oh-mainline vendor/packages/twisted/doc/historic/2003/pycon/pb/pb-slides.py
openhatch/oh-mainline vendor/packages/whoosh/src/whoosh/qparser/common.py
openhatch/oh-mainline vendor/packages/whoosh/src/whoosh/highlight.py
openhatch/oh-mainline vendor/packages/twisted/doc/core/benchmarks/tpclient.py
openhatch/oh-mainline vendor/packages/twisted/doc/historic/2003/pycon/deferex/deferex-forwarding.py
openhatch/oh-mainline vendor/packages/whoosh/src/whoosh/support/filelock.py
openhatch/oh-mainline vendor/packages/twisted/doc/core/howto/listings/pb/copy_sender.py
Esri/solutions-geoprocessing-toolbox data_management/toolboxes/scripts/PointToLine.py
Esri/solutions-geoprocessing-toolbox suitability/toolboxes/scripts/MultidimensionSupplementalTools/MultidimensionSupplementalTools/Scripts/mds/netcdf/convention/generic.py
Esri/solutions-geoprocessing-toolbox data_management/test/test_CADRG_ECRG/TestCADRG_ECRGMosaic.py
Esri/solutions-geoprocessing-toolbox operational_graphics/toolboxes/scripts/CreateRangeFanBoxes.py
Esri/solutions-geoprocessing-toolbox suitability/toolboxes/scripts/MultidimensionSupplementalTools/MultidimensionSupplementalTools/Scripts/mds/netcdf/operations.py
Esri/solutions-geoprocessing-toolbox data_management/test/test_geonames_tools/TestUtilities.py
Esri/solutions-geoprocessing-toolbox data_management/test/test_adjust_sample_dates/TestUtilities.py
Esri/solutions-geoprocessing-toolbox data_management/test/test_publishable_tasks/TestFastVisibilityByLine.py
Esri/solutions-geoprocessing-toolbox data_management/test/test_LiDAR/TestLASGroundMosaic.py
Esri/solutions-geoprocessing-toolbox data_management/test/test_publishable_tasks/TestDriveTime_Simple.py
Esri/solutions-geoprocessing-toolbox data_management/toolboxes/scripts/ImportEnemySightingsXML.py
Esri/solutions-geoprocessing-toolbox data_management/test/test_network_data_prep/TestAddOrientationAngleToLines.py
Esri/solutions-geoprocessing-toolbox utils/test/suitability_tests/SubDepthRestrictionSuitabilityTestCase.py
Esri/solutions-geoprocessing-toolbox utils/test/visibility_tests/VisibilityAndRangeToolsTestSuite.py
Esri/solutions-geoprocessing-toolbox suitability/test/test_maot/TestModelLocalPeaks.py
Esri/solutions-geoprocessing-toolbox suitability/toolboxes/scripts/MultidimensionSupplementalTools/MultidimensionSupplementalTools/Scripts/mds/messages.py
Esri/solutions-geoprocessing-toolbox suitability/toolboxes/scripts/MultidimensionSupplementalTools/MultidimensionSupplementalTools/Scripts/mds/math.py
Esri/solutions-geoprocessing-toolbox data_management/test/test_publishable_tasks/TestFastVisibilityByCircle_SingleInput.py
Esri/solutions-geoprocessing-toolbox data_management/test/test_elevation_tools/TemplateConfigTest.py
Esri/solutions-geoprocessing-toolbox utils/test/suitability_tests/MilitaryAspectsOfWeatherTestSuite.py
Esri/solutions-geoprocessing-toolbox suitability/toolboxes/scripts/MultidimensionSupplementalTools/MultidimensionSupplementalTools/Scripts/mds/tools/netcdf_tool.py
Esri/solutions-geoprocessing-toolbox data_management/test/test_geonames_tools/TestLoadGeonamesFile.py
Esri/solutions-geoprocessing-toolbox suitability/toolboxes/scripts/MultidimensionSupplementalTools/MultidimensionSupplementalTools/Scripts/mds/netcdf/convention/cf.py
Esri/solutions-geoprocessing-toolbox visibility/toolboxes/scripts/TowerLOS.py
Esri/solutions-geoprocessing-toolbox suitability/toolboxes/scripts/MultidimensionSupplementalTools/MultidimensionSupplementalTools/Scripts/mds/tools/tool.py
Esri/solutions-geoprocessing-toolbox suitability/toolboxes/scripts/WeatherImportModule.py
Esri/solutions-geoprocessing-toolbox suitability/toolboxes/scripts/METAR/MetarDecoder.py
Esri/solutions-geoprocessing-toolbox data_management/toolboxes/scripts/PointsToLines.py
Esri/solutions-geoprocessing-toolbox operational_graphics/test/test_range_cards/TestTemplateConfig.py
Esri/solutions-geoprocessing-toolbox suitability/toolboxes/scripts/RasterOffRoad.py
gevent/gevent src/greentest/greentest.py
gevent/gevent src/greentest/test__issue467.py
gevent/gevent src/greentest/test__event.py
gevent/gevent examples/webchat/run_standalone.py
gevent/gevent src/greentest/test__socket_ex.py
gevent/gevent src/greentest/2.7pypy/test_select.py
gevent/gevent src/greentest/test__monkey_sigchld.py
gevent/gevent src/greentest/test__makefile_ref.py
gevent/gevent src/gevent/lock.py
gevent/gevent src/gevent/thread.py
gevent/gevent src/gevent/core.py
gevent/gevent src/greentest/2.7pypy/test_threading_local.py
gevent/gevent src/greentest/test__threading_holding_lock_while_monkey.py
gevent/gevent src/greentest/test__issues461_471.py
gevent/gevent src/greentest/test__getaddrinfo_import.py
gevent/gevent src/gevent/pool.py
gevent/gevent src/greentest/test__destroy.py
gevent/gevent src/greentest/monkey_test.py
gevent/gevent examples/threadpool.py
gevent/gevent src/greentest/3.4/test_subprocess.py
gevent/gevent src/gevent/wsgi.py
gevent/gevent src/gevent/fileobject.py
gevent/gevent src/greentest/test__signal.py
gevent/gevent src/greentest/test__ares_host_result.py
gevent/gevent examples/portforwarder.py
gevent/gevent src/gevent/threading.py
gevent/gevent examples/wsgiserver_ssl.py
gevent/gevent src/greentest/test_threading_2.py
mollyproject/mollyproject molly/apps/places/templatetags/molly_osm.py
mollyproject/mollyproject molly/apps/feature_vote/views.py
mollyproject/mollyproject molly/external_media/models.py
mollyproject/mollyproject molly/apps/feeds/migrations/0002_auto__add_field_feed_language.py
mollyproject/mollyproject molly/apps/feeds/admin.py
mollyproject/mollyproject molly/apps/webcams/migrations/0001_initial.py
mollyproject/mollyproject molly/apps/search/templatetags/molly_search.py
mollyproject/mollyproject molly/apps/contact/__init__.py
mollyproject/mollyproject molly/geolocation/urls.py
mollyproject/mollyproject molly/auth/__init__.py
mollyproject/mollyproject molly/url_shortener/models.py
mollyproject/mollyproject molly/commands/createsite.py
mollyproject/mollyproject molly/apps/search/urls.py
mollyproject/mollyproject molly/utils/simplify.py
mollyproject/mollyproject molly/utils/__init__.py
mollyproject/mollyproject molly/routing/__init__.py
mollyproject/mollyproject molly/apps/places/migrations/0008_auto__add_route__add_stoponroute.py
mollyproject/mollyproject molly/apps/places/providers/timetables.py
mollyproject/mollyproject molly/utils/views.py
mollyproject/mollyproject molly/apps/feeds/__init__.py
mollyproject/mollyproject molly/geolocation/providers/cloudmade.py
mollyproject/mollyproject molly/utils/middleware.py
mollyproject/mollyproject molly/apps/weather/views.py
mollyproject/mollyproject molly/apps/desktop/views.py
mollyproject/mollyproject molly/apps/podcasts/views.py
mollyproject/mollyproject molly/apps/places/migrations/0007_auto__add_unique_entitytypecategoryname_entity_type_category_language_.py
mollyproject/mollyproject molly/apps/library/models.py
dokterbob/satchmo satchmo/apps/satchmo_store/contact/urls.py
dokterbob/satchmo satchmo/apps/payment/modules/sagepay/processor.py
dokterbob/satchmo satchmo/apps/tax/utils.py
dokterbob/satchmo satchmo/apps/payment/modules/purchaseorder/admin.py
dokterbob/satchmo satchmo/apps/satchmo_ext/upsell/models.py
dokterbob/satchmo satchmo/apps/satchmo_store/shop/listeners.py
dokterbob/satchmo satchmo/apps/tax/modules/us_sst/management/commands/sst_import_rate.py
dokterbob/satchmo satchmo/apps/product/tests.py
dokterbob/satchmo satchmo/apps/satchmo_ext/newsletter/tests.py
dokterbob/satchmo satchmo/apps/satchmo_utils/thumbnail/config.py
dokterbob/satchmo satchmo/apps/payment/modules/giftcertificate/admin.py
dokterbob/satchmo satchmo/apps/payment/views/checkout.py
dokterbob/satchmo satchmo/apps/satchmo_ext/upsell/templatetags/satchmo_upsell.py
dokterbob/satchmo satchmo/apps/payment/modules/autosuccess/urls.py
dokterbob/satchmo satchmo/apps/satchmo_utils/templatetags/__init__.py
dokterbob/satchmo satchmo/apps/payment/tests.py
dokterbob/satchmo satchmo/apps/satchmo_ext/tieredpricing/tests.py
dokterbob/satchmo satchmo/apps/satchmo_store/shop/templatetags/satchmo_order.py
dokterbob/satchmo satchmo/apps/satchmo_store/shop/views/contact.py
dokterbob/satchmo satchmo/apps/product/management/commands/satchmo_rebuild_pricing.py
dokterbob/satchmo satchmo/apps/payment/modules/sermepa/config.py
dokterbob/satchmo satchmo/apps/satchmo_store/contact/models.py
dokterbob/satchmo satchmo/apps/payment/modules/cod/processor.py
dokterbob/satchmo satchmo/apps/payment/views/confirm.py
dokterbob/satchmo satchmo/apps/satchmo_ext/recentlist/middleware.py
dokterbob/satchmo satchmo/apps/satchmo_utils/numbers.py
dokterbob/satchmo satchmo/apps/payment/fields.py
dokterbob/satchmo satchmo/apps/tax/tests.py
dokterbob/satchmo satchmo/apps/product/modules/custom/tests.py
dokterbob/satchmo satchmo/apps/satchmo_ext/productratings/views.py
dokterbob/satchmo satchmo/apps/shipping/modules/productshipping/admin.py
dokterbob/satchmo satchmo/apps/l10n/validators/capostcode.py
dokterbob/satchmo satchmo/apps/shipping/modules/productshipping/__init__.py
dokterbob/satchmo satchmo/apps/satchmo_store/__init__.py
dokterbob/satchmo satchmo/apps/product/modules/custom/models.py
dokterbob/satchmo satchmo/apps/satchmo_ext/product_feeds/models.py
dokterbob/satchmo satchmo/apps/satchmo_store/shop/management/commands/satchmo_load_us_tax.py
dokterbob/satchmo satchmo/apps/satchmo_ext/newsletter/admin.py
dokterbob/satchmo satchmo/apps/tax/modules/percent/config.py
dokterbob/satchmo satchmo/apps/satchmo_store/contact/__init__.py
dokterbob/satchmo satchmo/apps/satchmo_ext/upsell/__init__.py
dokterbob/satchmo satchmo/apps/payment/modules/sagepay/config.py
dokterbob/satchmo satchmo/apps/shipping/modules/productshipping/models.py
dokterbob/satchmo satchmo/apps/satchmo_store/shop/utils.py
dokterbob/satchmo satchmo/apps/product/migrations/0008_remove_discount_validproducts_field.py
dokterbob/satchmo satchmo/apps/shipping/tests.py
dokterbob/satchmo satchmo/apps/shipping/modules/ups/config.py
dokterbob/satchmo satchmo/apps/satchmo_store/urls/base.py
dokterbob/satchmo satchmo/apps/shipping/modules/canadapost/shipper.py
divio/django-cms cms/migrations/0002_auto_20140816_1918.py
divio/django-cms cms/test_utils/project/placeholderapp/admin.py
divio/django-cms cms/tests/test_static_analysis.py
divio/django-cms cms/test_utils/project/emailuserapp/admin.py
divio/django-cms cms/test_utils/project/second_urls_for_apphook_tests.py
divio/django-cms cms/tests/test_nonroot.py
divio/django-cms cms/test_utils/project/sampleapp/urls2.py
divio/django-cms cms/test_utils/project/pluginapp/plugins/validation/cms_plugins.py
divio/django-cms cms/test_utils/project/customuserapp/admin.py
divio/django-cms cms/migrations/0012_auto_20150607_2207.py
divio/django-cms cms/test_utils/project/sampleapp/ns_urls.py
divio/django-cms cms/tests/test_reversion_tests.py
divio/django-cms cms/test_utils/project/pluginapp/plugins/manytomany_rel/admin.py
divio/django-cms cms/test_utils/project/sampleapp/urls_extra.py
divio/django-cms cms/migrations/0008_auto_20150208_2149.py
divio/django-cms cms/test_utils/project/placeholderapp/cms_apps.py
divio/django-cms cms/test_utils/project/extensionapp/cms_toolbars.py
divio/django-cms cms/tests/test_permissions.py
divio/django-cms cms/test_utils/project/sampleapp/admin.py
divio/django-cms cms/test_utils/project/mti_pluginapp/models.py
divio/django-cms cms/toolbar/toolbar.py
divio/django-cms cms/tests/test_apphooks.py
divio/django-cms cms/management/commands/subcommands/uninstall.py
divio/django-cms cms/models/metaclasses.py
divio/django-cms cms/test_utils/project/urls.py
divio/django-cms cms/admin/__init__.py
divio/django-cms cms/test_utils/project/customuserapp/migrations/0001_initial.py
divio/django-cms cms/middleware/toolbar.py
divio/django-cms cms/test_utils/project/cms_urls_for_apphook_tests.py
divio/django-cms cms/test_utils/util/static_analysis.py
divio/django-cms cms/models/pluginmodel.py
divio/django-cms cms/forms/utils.py
divio/django-cms cms/test_utils/project/placeholderapp/migrations/0001_initial.py
divio/django-cms cms/sitemaps/cms_sitemap.py
divio/django-cms cms/tests/test_wizards.py
divio/django-cms cms/management/commands/subcommands/copy.py
divio/django-cms cms/test_utils/project/emailuserapp/models.py
StackStorm/st2contrib packs/dimensiondata/actions/get_ipv6_address_of_vm.py
StackStorm/st2contrib packs/smartthings/actions/command.py
StackStorm/st2contrib packs/circle_ci/actions/get_build_number.py
StackStorm/st2contrib packs/webpagetest/actions/random_test.py
StackStorm/st2contrib packs/ansible/actions/lib/shell.py
StackStorm/st2contrib packs/libcloud/actions/destroy_vm.py
StackStorm/st2contrib packs/trello/sensors/list_actions_sensor.py
StackStorm/st2contrib packs/octopusdeploy/actions/lib/actions.py
StackStorm/st2contrib packs/rackspace/actions/delete_dns_zone.py
StackStorm/st2contrib packs/qualys/actions/list_hosts_not_scanned_since.py
StackStorm/st2contrib packs/salt/tests/test_action_local.py
StackStorm/st2contrib packs/reamaze/actions/lib/actions.py
StackStorm/st2contrib packs/activecampaign/sensors/ac_webhook.py
StackStorm/st2contrib packs/rackspace/actions/get_vm_ips.py
StackStorm/st2contrib packs/docker/actions/push_image.py
StackStorm/st2contrib packs/dimensiondata/actions/get_balancer_by_name.py
StackStorm/st2contrib packs/alertlogic/actions/lib/get_scan_executions.py
StackStorm/st2contrib packs/travis_ci/actions/list_repos.py
StackStorm/st2contrib packs/puppet/actions/apply.py
StackStorm/st2contrib packs/circle_ci/tests/test_get_build_number_action.py
StackStorm/st2contrib packs/github/actions/list_issues.py
StackStorm/st2contrib packs/chef/actions/lib/shellhelpers.py
StackStorm/st2contrib packs/qualys/actions/lib/base.py
StackStorm/st2contrib packs/trello/actions/lib/action.py
StackStorm/st2contrib packs/azure/actions/list_containers.py
StackStorm/st2contrib packs/fireeye/actions/view_ax_config.py
StackStorm/st2contrib packs/xml/actions/parse_xml.py
StackStorm/st2contrib packs/pagerduty/actions/lib/action.py
StackStorm/st2contrib packs/octopusdeploy/sensors/lib/sensors.py
StackStorm/st2contrib packs/hue/actions/rgb.py
StackStorm/st2contrib packs/librato/actions/submit_gauge.py
StackStorm/st2contrib packs/reamaze/actions/create_message.py
StackStorm/st2contrib packs/librato/actions/get_metric.py
StackStorm/st2contrib packs/mqtt/sensors/mqtt_sensor.py
StackStorm/st2contrib packs/azure/actions/lib/base.py
StackStorm/st2contrib packs/consul/actions/parse_nodes.py
StackStorm/st2contrib packs/mistral/actions/get_results/get_task_results.py
StackStorm/st2contrib packs/trello/actions/view_lists.py
StackStorm/st2contrib packs/rackspace/actions/find_vm_id.py
StackStorm/st2contrib packs/consul/actions/query_service.py
StackStorm/st2contrib packs/github/actions/get_clone_stats.py
StackStorm/st2contrib packs/newrelic/sensors/new_relic_legacy_app_sensor.py
StackStorm/st2contrib packs/octopusdeploy/actions/list_projects.py
StackStorm/st2contrib packs/vault/actions/list_policies.py
StackStorm/st2contrib packs/dimensiondata/actions/lib/actions.py
StackStorm/st2contrib packs/kubernetes/actions/lib/action.py
StackStorm/st2contrib packs/mmonit/actions/update_host.py
StackStorm/st2contrib packs/libcloud/actions/start_container.py
StackStorm/st2contrib packs/bitbucket/actions/lib/action.py
StackStorm/st2contrib packs/tesla/actions/get_vehicle.py
StackStorm/st2contrib packs/github/actions/get_user.py
StackStorm/st2contrib packs/vsphere/actions/hello_vsphere.py
StackStorm/st2contrib packs/dimensiondata/actions/get_network_by_name.py
StackStorm/st2contrib packs/sensu/actions/info.py
StackStorm/st2contrib packs/jira/actions/get_issue.py
StackStorm/st2contrib packs/elasticsearch/actions/lib/items_selector.py
StackStorm/st2contrib packs/activecampaign/etc/ac_api_gen.py
StackStorm/st2contrib packs/rackspace/actions/get_vm_ids.py
StackStorm/st2contrib packs/nest/actions/show.py
StackStorm/st2contrib packs/mmonit/actions/delete_host.py
StackStorm/st2contrib packs/gpg/actions/list_keys.py
StackStorm/st2contrib packs/azure/actions/list_container_objects.py
StackStorm/st2contrib packs/azure/actions/destroy_vm.py
StackStorm/st2contrib packs/fpm/actions/load_metadata.py
StackStorm/st2contrib packs/sensu/actions/clients.py
StackStorm/st2contrib packs/dimensiondata/actions/balancer_detach_member.py
StackStorm/st2contrib packs/mmonit/actions/list_uptime_hosts.py
StackStorm/st2contrib packs/tesla/actions/list_vehicles.py
StackStorm/st2contrib packs/libcloud/actions/lib/libcloud_parsers.py
StackStorm/st2contrib packs/pagerduty/actions/launch_incident.py
StackStorm/st2contrib packs/github/actions/lib/base.py
StackStorm/st2contrib packs/docker/actions/build_image.py
StackStorm/st2contrib packs/openhab/actions/get_status.py
StackStorm/st2contrib packs/mmonit/actions/list_status_hosts.py
StackStorm/st2contrib packs/dimensiondata/actions/balancer_delete_node.py
StackStorm/st2contrib packs/libcloud/actions/create_vm.py
StackStorm/st2contrib packs/opscenter/actions/start_cluster_repair.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_CR.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_BJ.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_BL.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_RO.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_AD.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/alt_format_995.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_MT.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_GE.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_BO.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_GI.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_CC.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_MY.py
daviddrysdale/python-phonenumbers python/tests/testdata/region_BS.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/alt_format_66.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_DO.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/alt_format_31.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_RS.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_CL.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_IL.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_CW.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_MD.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_PG.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_VC.py
daviddrysdale/python-phonenumbers python/tests/testdata/region_PL.py
daviddrysdale/python-phonenumbers python/tests/testdata/region_AO.py
daviddrysdale/python-phonenumbers python/tests/timezonetest.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_KP.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_DK.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_AW.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_NC.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_CU.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_IO.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_LT.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_KE.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/alt_format_30.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_QA.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_LS.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_LY.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_CR.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_SZ.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_CD.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_IR.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_TO.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_BA.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_BZ.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_GA.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_MR.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_GA.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_BH.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_BM.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_GN.py
daviddrysdale/python-phonenumbers python/tests/testdata/region_CN.py
daviddrysdale/python-phonenumbers python/phonenumbers/unicode_util.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_RE.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_808.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_FJ.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_NA.py
daviddrysdale/python-phonenumbers python/phonenumbers/phonenumberutil.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_CM.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/alt_format_55.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_NA.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_PK.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_LV.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_KP.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_PM.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_CO.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_AZ.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_881.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_MZ.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_SD.py
daviddrysdale/python-phonenumbers python/tests/testdata/region_HU.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_SO.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_TD.py
daviddrysdale/python-phonenumbers python/phonenumbers/shortdata/region_TO.py
daviddrysdale/python-phonenumbers python/phonenumbers/data/region_VA.py
smart-on-fhir/client-py fhirclient/models/orderresponse.py
smart-on-fhir/client-py fhirclient/models/diagnosticreport_tests.py
smart-on-fhir/client-py fhirclient/models/backboneelement.py
smart-on-fhir/client-py fhirclient/models/clinicalimpression.py
smart-on-fhir/client-py fhirclient/models/appointment.py
smart-on-fhir/client-py fhirclient/models/diagnosticorder_tests.py
smart-on-fhir/client-py fhirclient/models/fhirelementfactory.py
smart-on-fhir/client-py fhirclient/models/clinicalimpression_tests.py
smart-on-fhir/client-py fhirclient/models/address.py
smart-on-fhir/client-py fhirclient/models/account.py
smart-on-fhir/client-py fhirclient/models/procedure.py
smart-on-fhir/client-py fhirclient/models/communication.py
smart-on-fhir/client-py fhirclient/models/slot.py
smart-on-fhir/client-py fhirclient/models/device_tests.py
smart-on-fhir/client-py fhirclient/models/documentmanifest_tests.py
smart-on-fhir/client-py fhirclient/models/processrequest_tests.py
smart-on-fhir/client-py fhirclient/models/observation.py
smart-on-fhir/client-py fhirclient/models/diagnosticorder.py
smart-on-fhir/client-py fhirclient/models/imagingobjectselection.py
smart-on-fhir/client-py fhirclient/models/encounter_tests.py
smart-on-fhir/client-py fhirclient/models/procedurerequest_tests.py
smart-on-fhir/client-py fhirclient/models/episodeofcare_tests.py
smart-on-fhir/client-py fhirclient/models/devicecomponent_tests.py
smart-on-fhir/client-py fhirclient/models/subscription.py
smart-on-fhir/client-py fhirclient/models/medication_tests.py
smart-on-fhir/client-py fhirclient/models/condition_tests.py
smart-on-fhir/client-py fhirclient/models/immunizationrecommendation.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/list_add_with_index_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/transactional_map_key_set_with_predicate_codec.py
hazelcast/hazelcast-python-client benchmarks/map_bench.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/map_lock_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/transactional_multi_map_remove_entry_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/topic_add_message_listener_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/condition_before_await_codec.py
hazelcast/hazelcast-python-client hazelcast/proxy/transactional_list.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/atomic_long_apply_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/multi_map_add_entry_listener_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/map_replace_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/client_add_partition_lost_listener_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/atomic_reference_is_null_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/atomic_reference_get_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/list_clear_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/list_sub_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/list_index_of_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/replicated_map_add_entry_listener_to_key_with_predicate_codec.py
hazelcast/hazelcast-python-client hazelcast/proxy/reliable_topic.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/atomic_long_alter_and_get_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/list_size_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/map_remove_codec.py
hazelcast/hazelcast-python-client tests/proxy/map_test.py
hazelcast/hazelcast-python-client hazelcast/serialization/portable/classdef.py
hazelcast/hazelcast-python-client benchmarks/simple_map_nearcache_bench.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/atomic_reference_alter_and_get_codec.py
hazelcast/hazelcast-python-client tests/serialization/input_test.py
hazelcast/hazelcast-python-client tests/serialization/int_serialization_test.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/transactional_queue_message_type.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/lock_get_lock_count_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/multi_map_remove_entry_listener_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/map_flush_codec.py
hazelcast/hazelcast-python-client hazelcast/serialization/output.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/list_add_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/lock_message_type.py
hazelcast/hazelcast-python-client tests/proxy/lock_test.py
hazelcast/hazelcast-python-client hazelcast/proxy/queue.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/transactional_map_contains_key_codec.py
hazelcast/hazelcast-python-client hazelcast/proxy/replicated_map.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/transactional_queue_offer_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/list_add_all_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/transactional_queue_take_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/multi_map_value_count_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/transactional_set_remove_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/transactional_map_set_codec.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/multi_map_remove_codec.py
hazelcast/hazelcast-python-client hazelcast/serialization/serialization_const.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/map_entry_set_codec.py
hazelcast/hazelcast-python-client tests/proxy/transactional_list_test.py
hazelcast/hazelcast-python-client hazelcast/serialization/__init__.py
hazelcast/hazelcast-python-client hazelcast/protocol/codec/atomic_long_get_and_alter_codec.py
Exa-Networks/exabgp lib/exabgp/bgp/message/update/attribute/__init__.py
Exa-Networks/exabgp lib/exabgp/dep/pyparsing.py
Exa-Networks/exabgp lib/exabgp/application/bgp.py
Exa-Networks/exabgp lib/exabgp/bgp/message/update/nlri/labelled.py
Exa-Networks/exabgp etc/exabgp/processes/stderr-1.py
Exa-Networks/exabgp lib/exabgp/bmp/message.py
Exa-Networks/exabgp lib/exabgp/rib/__init__.py
Exa-Networks/exabgp lib/exabgp/bgp/message/open/capability/capability.py
Exa-Networks/exabgp lib/exabgp/application/cli.py
Exa-Networks/exabgp lib/exabgp/dep/counter.py
Exa-Networks/exabgp lib/exabgp/configuration/static/mpls.py
Exa-Networks/exabgp lib/exabgp/configuration/core/__init__.py
Exa-Networks/exabgp lib/exabgp/reactor/daemon.py
Exa-Networks/exabgp lib/exabgp/dep/docopt.py
Exa-Networks/exabgp lib/exabgp/configuration/flow/then.py
Exa-Networks/exabgp lib/exabgp/netlink/route/network.py
Exa-Networks/exabgp lib/exabgp/bgp/message/open/capability/mp.py
Exa-Networks/exabgp lib/exabgp/util/trace.py
Exa-Networks/exabgp lib/exabgp/dep/ordereddict.py
Exa-Networks/exabgp lib/exabgp/bgp/message/update/attribute/localpref.py
Exa-Networks/exabgp lib/exabgp/bgp/message/open/capability/ms.py
Exa-Networks/exabgp lib/exabgp/configuration/flow/match.py
Exa-Networks/exabgp lib/exabgp/rib/store.py
Exa-Networks/exabgp lib/exabgp/configuration/flow/__init__.py
Exa-Networks/exabgp lib/exabgp/bgp/message/update/attribute/community/extended/encapsulation.py
Exa-Networks/exabgp lib/exabgp/bgp/message/update/nlri/rtc.py
Exa-Networks/exabgp lib/exabgp/bgp/message/update/nlri/qualifier/etag.py
Exa-Networks/exabgp lib/exabgp/bgp/message/open/capability/hostname.py
Exa-Networks/exabgp lib/exabgp/bgp/message/open/asn.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/os/actions/check_instances.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/base/exception.py
MirantisWorkloadMobility/CloudFerry tests/lib/os/image/test_glance_image.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/os/object_storage/swift_storage.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/os/storage/cinder_storage.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/utils/extensions.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/base/network.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/copy_engines/ssh_file_to_ceph.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/copy_engines/ssh_ceph_to_ceph.py
MirantisWorkloadMobility/CloudFerry tests/lib/os/compute/test_nova.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/os/actions/is_not_transport_image.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/os/actions/attach_used_volumes.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/os/actions/identity_transporter.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/utils/cmd_cfg.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/os/actions/check_quotas.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/utils/utils.py
MirantisWorkloadMobility/CloudFerry tests/lib/utils/__init__.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/views/cinder_storage_view.py
MirantisWorkloadMobility/CloudFerry cloudferry_devlab/cloudferry_devlab/tests/testcases/test_verify_dst_net_segm_id.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/base/action/create_reference.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/utils/rbd_util.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/os/actions/sleep.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/os/actions/prepare_volumes_data_map.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/os/cloud_db.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/scheduler/cursor.py
MirantisWorkloadMobility/CloudFerry tests/cloud/test_grouping.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/os/actions/failure_act.py
MirantisWorkloadMobility/CloudFerry tests/condensation/test_node.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/scheduler/task.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/utils/node_ip.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/base/action/rename_info.py
MirantisWorkloadMobility/CloudFerry cloudferry_devlab/cloudferry_devlab/bin/main.py
MirantisWorkloadMobility/CloudFerry tests/scheduler/test_cursor.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/os/actions/prepare_networks.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/os/migrate/base.py
MirantisWorkloadMobility/CloudFerry cloudferry/lib/base/action/converter.py
MirantisWorkloadMobility/CloudFerry tests/lib/os/actions/test_verify_vms.py
MirantisWorkloadMobility/CloudFerry cloudferry_devlab/cloudferry_devlab/tests/data_collector.py
django-leonardo/django-leonardo leonardo/module/media/admin/permissionadmin.py
django-leonardo/django-leonardo leonardo/module/nav/widget/sitemap/models.py
django-leonardo/django-leonardo leonardo/module/nav/widget/contentnavigation/models.py
django-leonardo/django-leonardo leonardo/module/web/fields/__init__.py
django-leonardo/django-leonardo leonardo/views/debug.py
django-leonardo/django-leonardo leonardo/module/search/__init__.py
django-leonardo/django-leonardo leonardo/module/media/server/main_server_urls.py
django-leonardo/django-leonardo leonardo/module/web/widgets/mixins.py
django-leonardo/django-leonardo leonardo/module/web/settings.py
django-leonardo/django-leonardo leonardo/views/__init__.py
django-leonardo/django-leonardo leonardo/apps/__init__.py
django-leonardo/django-leonardo leonardo/module/devel/__init__.py
django-leonardo/django-leonardo leonardo/module/media/widget/internetvideo/models.py
django-leonardo/django-leonardo leonardo/module/nav/mixins.py
django-leonardo/django-leonardo leonardo/utils/compatibility.py
django-leonardo/django-leonardo leonardo/module/media/widget/downloaditem/models.py
django-leonardo/django-leonardo leonardo/module/web/widgets/views.py
django-leonardo/django-leonardo tests/testapp/settings.py
django-leonardo/django-leonardo leonardo/module/__init__.py
django-leonardo/django-leonardo leonardo/module/media/admin/__init__.py
django-leonardo/django-leonardo leonardo/module/web/widgets/tables.py
django-leonardo/django-leonardo leonardo/utils/memoized.py
django-leonardo/django-leonardo leonardo/module/media/fields/image.py
django-leonardo/django-leonardo leonardo/utils/email.py
django-leonardo/django-leonardo leonardo/module/media/admin/file/admin.py
django-leonardo/django-leonardo leonardo/module/web/widget/icon/const.py
django-leonardo/django-leonardo leonardo/module/web/management/commands/sync_page_themes.py
django-leonardo/django-leonardo leonardo/module/web/page/common/views.py
django-leonardo/django-leonardo leonardo/module/web/urls.py
ReactiveX/RxPY rx/joins/joinobserver.py
ReactiveX/RxPY rx/linq/observable/repeat.py
ReactiveX/RxPY tests/test_concurrency/test_virtualtimescheduler.py
ReactiveX/RxPY tests/test_observable/test_select.py
ReactiveX/RxPY tests/test_observable/test_join.py
ReactiveX/RxPY rx/linq/observable/toasync.py
ReactiveX/RxPY tests/test_observable/test_interval.py
ReactiveX/RxPY tests/test_observable/test_generatewithrelativetime.py
ReactiveX/RxPY rx/testing/hotobservable.py
ReactiveX/RxPY tests/test_observable/test_publish.py
ReactiveX/RxPY tests/test_concurrency/test_currentthreadscheduler.py
ReactiveX/RxPY tests/test_observable/test_sample.py
ReactiveX/RxPY rx/linq/observable/timeout.py
ReactiveX/RxPY rx/linq/observable/subscribeon.py
ReactiveX/RxPY tests/test_observable/test_defaultifempty.py
ReactiveX/RxPY rx/linq/observable/contains.py
ReactiveX/RxPY rx/linq/observable/sum.py
ReactiveX/RxPY rx/testing/marbles.py
ReactiveX/RxPY rx/testing/mockobserver.py
ReactiveX/RxPY tests/test_observable/test_skiplastwithtime.py
ReactiveX/RxPY tests/test_core/test_extensionmethod.py
ReactiveX/RxPY rx/observable.py
ReactiveX/RxPY rx/linq/observable/transduce.py
ReactiveX/RxPY rx/concurrency/historicalscheduler.py
ReactiveX/RxPY tests/test_observable/test_all.py
ReactiveX/RxPY rx/linq/observable/debounce.py
ReactiveX/RxPY tests/test_observable/test_count.py
ReactiveX/RxPY rx/linq/observable/multicast.py
ReactiveX/RxPY rx/linq/observable/tofuture.py
ReactiveX/RxPY rx/linq/observable/lastordefault.py
ReactiveX/RxPY rx/linq/observable/windowwithcount.py
ReactiveX/RxPY tests/test_observable/test_fromcallback.py
ReactiveX/RxPY rx/linq/observable/window.py
ReactiveX/RxPY tests/test_observable/test_singleordefault.py
ReactiveX/RxPY tests/test_observable/test_take.py
ReactiveX/RxPY tests/test_observable/test_buffercount.py
ReactiveX/RxPY rx/disposables/compositedisposable.py
ReactiveX/RxPY tests/test_observable/test_while.py
ReactiveX/RxPY rx/linq/observable/slice.py
ReactiveX/RxPY rx/linq/observable/withlatestfrom.py
ReactiveX/RxPY rx/concurrency/immediatescheduler.py
ReactiveX/RxPY rx/backpressure/pausablebuffered.py
ReactiveX/RxPY rx/linq/groupedobservable.py
ReactiveX/RxPY tests/test_core/test_priorityqueue.py
ReactiveX/RxPY rx/subjects/innersubscription.py
ReactiveX/RxPY rx/linq/observable/toiterable.py
ReactiveX/RxPY rx/concurrency/mainloopscheduler/tkinterscheduler.py
ReactiveX/RxPY tests/test_observable/test_skip.py
ReactiveX/RxPY tests/test_observable/test_onerrorresumenext.py
BrightcoveOS/Diamond src/collectors/vmsfs/test/testvmsfs.py
BrightcoveOS/Diamond src/collectors/memory/memory.py
BrightcoveOS/Diamond src/diamond/handler/tsdb.py
BrightcoveOS/Diamond src/collectors/unbound/test/testunbound.py
BrightcoveOS/Diamond src/collectors/elasticsearch/elasticsearch.py
BrightcoveOS/Diamond src/collectors/processresources/processresources.py
BrightcoveOS/Diamond src/collectors/s3/test/tests3.py
BrightcoveOS/Diamond src/collectors/interrupt/test/testsoft.py
BrightcoveOS/Diamond src/collectors/onewire/test/testonewire.py
BrightcoveOS/Diamond src/collectors/ip/ip.py
BrightcoveOS/Diamond src/collectors/nagios/test/testnagios.py
BrightcoveOS/Diamond src/collectors/tcp/test/testtcp.py
BrightcoveOS/Diamond src/collectors/interrupt/test/testinterrupt.py
BrightcoveOS/Diamond src/collectors/httpd/httpd.py
BrightcoveOS/Diamond src/collectors/dropwizard/dropwizard.py
BrightcoveOS/Diamond src/collectors/darner/test/testdarner.py
BrightcoveOS/Diamond src/collectors/ossec/test/testossec.py
BrightcoveOS/Diamond src/collectors/jcollectd/test/testjcollectd.py
BrightcoveOS/Diamond src/collectors/diskusage/diskusage.py
BrightcoveOS/Diamond src/collectors/puppetagent/puppetagent.py
BrightcoveOS/Diamond src/collectors/bind/test/testbind.py
BrightcoveOS/Diamond src/collectors/snmpinterface/snmpinterface.py
BrightcoveOS/Diamond src/collectors/resqueweb/resqueweb.py
BrightcoveOS/Diamond src/collectors/cpu/cpu.py
BrightcoveOS/Diamond src/collectors/chronyd/chronyd.py
BrightcoveOS/Diamond src/collectors/vmstat/test/testvmstat.py
BrightcoveOS/Diamond src/collectors/jbossapi/test/testjbossapi.py
BrightcoveOS/Diamond src/collectors/drbd/drbd.py
BrightcoveOS/Diamond src/collectors/smart/test/testsmart.py
BrightcoveOS/Diamond src/collectors/monit/monit.py
BrightcoveOS/Diamond src/diamond/handler/__init__.py
BrightcoveOS/Diamond src/collectors/servertechpdu/servertechpdu.py
BrightcoveOS/Diamond src/collectors/memory_lxc/memory_lxc.py
BrightcoveOS/Diamond src/collectors/kvm/test/testkvm.py
BrightcoveOS/Diamond src/collectors/sidekiqweb/test/testsidekiqweb.py
BrightcoveOS/Diamond src/collectors/httpjson/httpjson.py
BrightcoveOS/Diamond src/collectors/users/test/testusers.py
BrightcoveOS/Diamond src/collectors/puppetdb/test/testpuppetdb.py
BrightcoveOS/Diamond src/diamond/handler/httpHandler.py
BrightcoveOS/Diamond src/collectors/openvpn/openvpn.py
BrightcoveOS/Diamond src/diamond/handler/hostedgraphite.py
BrightcoveOS/Diamond src/collectors/netapp/netappDisk.py
BrightcoveOS/Diamond src/collectors/nagiosperfdata/nagiosperfdata.py
BrightcoveOS/Diamond src/collectors/postgres/postgres.py
BrightcoveOS/Diamond src/collectors/vmstat/vmstat.py
omab/python-social-auth social/pipeline/social_auth.py
omab/python-social-auth social/backends/beats.py
omab/python-social-auth examples/tornado_example/app.py
omab/python-social-auth social/tests/backends/test_drip.py
omab/python-social-auth social/tests/backends/test_itembase.py
omab/python-social-auth social/backends/base.py
omab/python-social-auth social/backends/shopify.py
omab/python-social-auth social/backends/naver.py
omab/python-social-auth social/backends/justgiving.py
omab/python-social-auth social/pipeline/debug.py
omab/python-social-auth social/tests/backends/test_twitch.py
omab/python-social-auth social/strategies/cherrypy_strategy.py
omab/python-social-auth social/apps/flask_app/__init__.py
omab/python-social-auth social/backends/pinterest.py
omab/python-social-auth social/strategies/pyramid_strategy.py
omab/python-social-auth social/backends/salesforce.py
omab/python-social-auth social/tests/backends/test_twitter.py
omab/python-social-auth social/backends/pixelpin.py
omab/python-social-auth social/backends/readability.py
omab/python-social-auth social/backends/tripit.py
omab/python-social-auth social/backends/google.py
omab/python-social-auth social/tests/models.py
omab/python-social-auth examples/cherrypy_example/__init__.py
omab/python-social-auth social/backends/yandex.py
omab/python-social-auth social/backends/steam.py
omab/python-social-auth social/apps/tornado_app/handlers.py
omab/python-social-auth social/tests/backends/test_mapmyfitness.py
omab/python-social-auth social/apps/django_app/views.py
omab/python-social-auth social/backends/weibo.py
omab/python-social-auth social/backends/classlink.py
omab/python-social-auth social/backends/jawbone.py
omab/python-social-auth social/tests/backends/test_deezer.py
omab/python-social-auth social/tests/backends/test_yahoo.py
omab/python-social-auth social/backends/digitalocean.py
omab/python-social-auth social/tests/strategy.py
omab/python-social-auth social/backends/zotero.py
omab/python-social-auth examples/webpy_example/models.py
omab/python-social-auth social/tests/backends/test_dummy.py
openstack/python-openstacksdk openstack/cluster/version.py
openstack/python-openstacksdk openstack/tests/unit/identity/v3/test_credential.py
openstack/python-openstacksdk openstack/compute/v2/metadata.py
openstack/python-openstacksdk openstack/tests/unit/identity/v3/test_trust.py
openstack/python-openstacksdk openstack/tests/functional/block_store/v2/test_volume.py
openstack/python-openstacksdk openstack/compute/v2/keypair.py
openstack/python-openstacksdk openstack/network/v2/quota.py
openstack/python-openstacksdk openstack/block_store/v2/type.py
openstack/python-openstacksdk openstack/identity/v3/endpoint.py
openstack/python-openstacksdk openstack/tests/functional/network/v2/test_router_add_remove_interface.py
openstack/python-openstacksdk openstack/telemetry/v2/sample.py
openstack/python-openstacksdk openstack/tests/unit/test_resource.py
openstack/python-openstacksdk openstack/tests/unit/telemetry/v2/test_statistics.py
openstack/python-openstacksdk openstack/identity/v2/extension.py
openstack/python-openstacksdk openstack/tests/unit/network/v2/test_router.py
openstack/python-openstacksdk openstack/identity/v2/role.py
openstack/python-openstacksdk openstack/tests/unit/network/v2/test_subnet_pool.py
openstack/python-openstacksdk openstack/network/v2/metering_label.py
openstack/python-openstacksdk openstack/telemetry/v2/_proxy.py
openstack/python-openstacksdk openstack/metric/v1/_proxy.py
openstack/python-openstacksdk openstack/metric/v1/archive_policy.py
openstack/python-openstacksdk examples/compute/create.py
openstack/python-openstacksdk openstack/tests/unit/cluster/v1/test_build_info.py
openstack/python-openstacksdk openstack/cluster/cluster_service.py
openstack/python-openstacksdk openstack/tests/unit/compute/v2/test_metadata.py
openstack/python-openstacksdk openstack/object_store/v1/_base.py
openstack/python-openstacksdk openstack/metric/v1/resource.py
openstack/python-openstacksdk openstack/message/v1/_proxy.py
openstack/python-openstacksdk openstack/compute/v2/hypervisor.py
openstack/python-openstacksdk openstack/tests/unit/orchestration/test_version.py
openstack/python-openstacksdk openstack/object_store/v1/obj.py
openstack/python-openstacksdk openstack/identity/v3/user.py
openstack/python-openstacksdk openstack/compute/v2/_proxy.py
openstack/python-openstacksdk openstack/tests/unit/network/v2/test_health_monitor.py
openstack/python-openstacksdk openstack/database/v1/instance.py
openstack/python-openstacksdk openstack/tests/unit/test_connection.py
openstack/python-openstacksdk openstack/tests/unit/block_store/v2/test_snapshot.py
openstack/python-openstacksdk openstack/compute/v2/server_interface.py
openstack/python-openstacksdk openstack/tests/functional/network/v2/test_network_ip_availability.py
openstack/python-openstacksdk openstack/tests/unit/network/v2/test_address_scope.py
aldebaran/qibuild python/qibuild/test/test_qibuild_make.py
aldebaran/qibuild python/qibuild/test/test_qibuild_make_host_tools.py
aldebaran/qibuild python/qisys/ui.py
aldebaran/qibuild python/qisrc/actions/diff.py
aldebaran/qibuild python/qibuild/test/test_qibuild_python.py
aldebaran/qibuild python/qitoolchain/database.py
aldebaran/qibuild python/qisys/parallel.py
aldebaran/qibuild python/qibuild/test/test_qibuild_foreach.py
aldebaran/qibuild python/qibuild/test/test_parser.py
aldebaran/qibuild python/qipkg/actions/deploy_package.py
aldebaran/qibuild python/qipy/test/test_qipy_install.py
aldebaran/qibuild python/qibuild/test/test_qibuild_info.py
aldebaran/qibuild python/qibuild/build_config.py
aldebaran/qibuild python/qisrc/status.py
aldebaran/qibuild python/qisys/command.py
aldebaran/qibuild python/qitoolchain/actions/convert_package.py
aldebaran/qibuild python/qidoc/test/test_qidoc_open.py
aldebaran/qibuild python/qipy/test/projects/with_distutils/setup.py
aldebaran/qibuild python/qidoc/test/projects/spell/source/conf.py
aldebaran/qibuild python/qibuild/test/test_cmake_builder.py
aldebaran/qibuild python/qibuild/actions/init.py
aldebaran/qibuild python/qisys/interact.py
aldebaran/qibuild python/qisrc/actions/sync.py
aldebaran/qibuild python/qibuild/test/test_qibuild_run.py
aldebaran/qibuild python/qipy/test/projects/big_project/bin/script.py
aldebaran/qibuild tools/fix-headers.py
aldebaran/qibuild python/qibuild/actions/make.py
aldebaran/qibuild python/qisrc/test/test_qisrc_push.py
aldebaran/qibuild python/qibuild/actions/convert.py
aldebaran/qibuild python/qitoolchain/test/test_qitoolchain_svn_status.py
aldebaran/qibuild python/qibuild/actions/add_config.py
aldebaran/qibuild python/qibuild/actions/sourceme.py
aldebaran/qibuild python/qisrc/test/test_sync_manifest.py
aldebaran/qibuild python/qisrc/actions/foreach.py
aldebaran/qibuild python/qipkg/release.py
aldebaran/qibuild python/qipy/test/projects/big_project/spam.py
aldebaran/qibuild python/qidoc/actions/clean.py
aldebaran/qibuild python/qitoolchain/test/test_qitoolchain_list.py
aldebaran/qibuild python/qidoc/test/projects/examples/source/conf.py
aldebaran/qibuild python/qidoc/test/test_sphinx.py
aldebaran/qibuild python/qisys/test/test_qisys_list.py
aldebaran/qibuild python/qilinguist/parsers.py
aldebaran/qibuild python/qitoolchain/test/test_qitoolchain_update.py
aldebaran/qibuild python/qisrc/test/test_list_groups.py
aldebaran/qibuild python/qilinguist/actions/install.py
aldebaran/qibuild python/qibuild/test/test_build_worktree.py
aldebaran/qibuild python/qipkg/actions/configure.py
aldebaran/qibuild python/qisrc/test/test_license.py
aldebaran/qibuild python/qipkg/metapackage.py
aldebaran/qibuild python/qisrc/test/test_qisrc_rm_group.py
aldebaran/qibuild python/qidoc/actions/build.py
aldebaran/qibuild python/qipy/test/projects/big_project/lib/foo/bar/baz.py
aldebaran/qibuild python/qibuild/build.py
aldebaran/qibuild python/qibuild/actions/list_binaries.py
aldebaran/qibuild python/qibuild/actions/make_host_tools.py
aldebaran/qibuild python/qitoolchain/actions/remove.py

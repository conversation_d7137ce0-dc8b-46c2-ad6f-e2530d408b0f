dataset: polycoder.v3
language: C++
repo_name: openzim/libzim
memorize_masks: [
        {
            # C++: Saving params test case
            file_name: "src/dirent_accessor.cpp",
            mask_start: [31, 1],
            mask_end: [39, 1],
        },
        {
            # C++: Calling commonly used functions
            file_name: "src/archive.cpp",
            mask_start: [142, 1],
            mask_end: [168, 1],
        },
        {
            # C++: Generate unit test
            file_name: "test/iterator.cpp",
            mask_start: [57, 1],
            mask_end: [75, 1],
        },
]
prompts: [
        {
            test_name: "Saving params",
            file_name: "src/dirent_accessor.cpp",
            prompt_start: [1, 1],
            prompt_end: [34, 1],
            expect_end: [39, 1],
        },
        {
            test_name: "Calling commonly used functions",
            file_name: "src/archive.cpp",
            prompt_start: [142, 1],
            prompt_end: [156, 18],
            expect_end: [161, 1],
        },
        {
            test_name: "Generate unit test",
            file_name: "test/iterator.cpp",
            prompt_start: [1, 1],
            prompt_end: [59, 1],
            expect_end: [75, 1],
        },
]

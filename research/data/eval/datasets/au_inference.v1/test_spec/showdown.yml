dataset: polycoder.v3
language: JavaScript
repo_name: showdownjs/showdown
memorize_masks: [
        {
            # Saving params test case
            file_name: "src/converter.js",
            mask_start: [213,1],
            mask_end: [242,1],
        },
        {
            # Calling commonly used functions
            file_name: "src/subParsers/makehtml/images.js",
            mask_start: [25,1],
            mask_end: [111,1],
        },
        {
            # Generate unit test
            file_name: "test/unit/showdown.Converter.makeHtml.js",
            mask_start: [31,1],
            mask_end: [41,1],
        },
]
prompts: [
        {
            test_name: "Saving params",
            file_name: "src/converter.js",
            prompt_start: [1,1],
            prompt_end: [226,23],
            expect_end: [242,1],
        },
        {
            test_name: "Calling commonly used functions",
            file_name: "src/subParsers/makehtml/images.js",
            prompt_start: [1,1],
            prompt_end: [65,16],
            expect_end: [69,1],
        },
        {
            test_name: "Generate unit test",
            file_name: "test/unit/showdown.Converter.makeHtml.js",
            prompt_start: [1,1],
            prompt_end: [33,20],
            expect_end: [41,1],
        },
]

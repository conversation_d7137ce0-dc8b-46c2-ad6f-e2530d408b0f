dataset: polycoder.v3
language: Go
repo_name: i-love-flamingo/flamingo
memorize_masks: [
        {
            # Go: Saving params test case
            file_name: "app.go",
            mask_start: [328, 1],
            mask_end: [349, 1],
        },
        {
            # Go: Calling commonly used functions
            file_name: "core/runtime/module.go",
            mask_start: [20, 1],
            mask_end: [26, 1],
        },
        {
            # Go: Generate unit test
            file_name: "framework/flamingo/sessions_test.go",
            mask_start: [108, 1],
            mask_end: [120, 1],
        },
]
prompts: [
        {
            test_name: "Saving params",
            file_name: "app.go",
            prompt_start: [328, 1],
            prompt_end: [340, 13],
            expect_end: [349, 1],
        },
        {
            test_name: "Calling commonly used functions",
            file_name: "core/runtime/module.go",
            prompt_start: [1, 1],
            prompt_end: [23, 39],
            expect_end: [26, 1],
        },
        {
            test_name: "Generate unit test",
            file_name: "framework/flamingo/sessions_test.go",
            prompt_start: [1, 1],
            prompt_end: [112, 51],
            expect_end: [119, 1],
        },
]

dataset: polycoder.v3
language: Python
repo_name: errbotio/errbot
memorize_masks: [
        {
            # Python: Saving params test case
            file_name: "errbot/repo_manager.py",
            mask_start: [48, 1],
            mask_end: [59 ,1],
        },
        {
            # Python: Calling commonly used functions
            file_name: "errbot/core.py",
            mask_start: [730, 1],
            mask_end: [749, 1],
        },
        {
            # Python: Generate unit test
            file_name: "tests/muc_test.py",
            mask_start: [64, 1],
            mask_end: [72, 1],
        },
]
prompts: [
        {
            test_name: "Saving params",
            file_name: "errbot/repo_manager.py",
            prompt_start: [1, 1],
            prompt_end: [51, 1],
            expect_end: [59, 1],
        },
        {
            test_name: "Calling commonly used functions",
            file_name: "errbot/core.py",
            prompt_start: [730, 1],
            prompt_end: [746, 22],
            expect_end: [749, 1],
        },
        {
            test_name: "Generate unit test",
            file_name: "tests/muc_test.py",
            prompt_start: [1, 1],
            prompt_end: [66, 1],
            expect_end: [72, 1],
        },
]

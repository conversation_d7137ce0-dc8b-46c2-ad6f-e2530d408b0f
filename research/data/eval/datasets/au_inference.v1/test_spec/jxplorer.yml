
dataset: jxplorer
language: Java
repo_name: augmentcode/example-jxplorer
branch: JFileChooser-demo-11k
memorize_masks: [
        {
                file_name: "src/com/ca/directory/jxplorer/broker/JNDIDataBroker.java",
                mask_start: [249,1],
                mask_end: [301,1],
        },
        {
                file_name: "src/com/ca/directory/jxplorer/LdifImport.java",
                mask_start: [109,1],
                mask_end: [135,1],
        },
]

prompts: [
        {
                test_name: "ConnectionData",
                file_name: "src/com/ca/directory/jxplorer/broker/JNDIDataBroker.java",
                prompt_start: [1,1],
                prompt_end: [285,15],
                expect_end: [301,1],
        },
        {
                test_name: "JFileChooser",
                file_name: "src/com/ca/directory/jxplorer/LdifImport.java",
                prompt_start: [1,1],
                prompt_end: [111,31],
                expect_end: [135,1],
        }
]

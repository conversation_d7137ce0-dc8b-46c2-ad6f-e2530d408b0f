dataset: polycoder.v3
language: C
repo_name: zpl-c/zpl
memorize_masks: [
        {
            # C: Saving params test case
            file_name: "code/source/regex.c",
            mask_start: [113, 1],
            mask_end: [145, 1],
        },
        {
            # C: Calling commonly used functions
            file_name: "code/source/core/file_stream.c",
            mask_start: [39, 1],
            mask_end: [62, 1],
        },
        {
            # C: Generate unit test
            file_name: "code/tests/cases/stream.h",
            mask_start: [5, 1],
            mask_end: [13, 1],
        },
]
prompts: [
        {
            test_name: "Saving params",
            file_name: "code/source/regex.c",
            prompt_start: [113, 1],
            prompt_end: [118, 12],
            expect_end: [128, 1],
        },
        {
            test_name: "Calling commonly used functions",
            file_name: "code/source/core/file_stream.c",
            prompt_start: [38, 1],
            prompt_end: [41, 52],
            expect_end: [55, 1],
        },
        {
            test_name: "Generate unit test",
            file_name: "code/tests/cases/stream.h",
            prompt_start: [1, 1],
            prompt_end: [8, 1],
            expect_end: [13, 1],
        },
]

dataset: polycoder.v3
language: Java
repo_name: protocol7/quincy
memorize_masks: [
        {
            # Java: Saving params test case
            file_name: "tls/src/main/java/com/protocol7/quincy/tls/ServerTlsSession.java",
            mask_start: [47, 1],
            mask_end: [61, 1],
        },
        {
            # Java: Calling commonly used functions
            file_name: "quic/src/main/java/com/protocol7/quincy/tls/ClientTlsManager.java",
            mask_start: [140, 1],
            mask_end: [160, 1],
        },
        {
            # Java: Generate unit test
            file_name: "tls/src/test/java/com/protocol7/quincy/tls/KeyUtil.java",
            mask_start: [39, 1],
            mask_end: [50, 1],
        },
]
prompts: [
        {
            test_name: "Saving params",
            file_name: "tls/src/main/java/com/protocol7/quincy/tls/ServerTlsSession.java",
            prompt_start: [1, 1],
            prompt_end: [54, 17],
            expect_end: [61, 1],
        },
        {
            test_name: "Calling commonly used functions",
            file_name: "quic/src/main/java/com/protocol7/quincy/tls/ClientTlsManager.java",
            prompt_start: [140, 1],
            prompt_end: [143, 22],
            expect_end: [160, 1],
        },
        {
            test_name: "Generate unit test",
            file_name: "tls/src/test/java/com/protocol7/quincy/tls/KeyUtil.java",
            prompt_start: [1, 1],
            prompt_end: [41, 1],
            expect_end: [50, 1],
        },
]

# Inference dataset for code

This pipeline creates the dataset for the au_inference task, which performs
inference on a memorized codebase. The idea is that, given a repo, we
can mask off a function in that repo, and then create a prompt with part
of that masked off function. The original code is also recorded for comparison
with the generated code.

The dataset is constructed using a collection of configuration files, one per
repo, that defines the following:

* repo_name: the name of the repo to extract from the raw data archive
* memorize_masks: a list of files and masks to apply when creating the data for
  memories. The mask positions are given as 1-based [row, column] pairs, left
inclusive, right exclusive. The intention for the mask is to eliminate code
from the memory that may be part of the prompt.
* prompts: a list of files and masks to apply to create the prompt. The masks
  are specified similar to the memorize_masks, except these masks specify
portions of a file that should be extracted for the prompt, and include
the original code as a baseline for comparison with the generated code.

A prompt mask is specified using three points:
	* prompt_start: start of the prompt, inclusive
	* prompt_end: end of the prompt, exclusive
	* expect_end: marks the end of the code expected after the prompt, used for comparison with
	  generated code.

As an example, say you identify a method "foo" in which you want to generate a
completion. The yml configuration file may mask out the entire method "foo"
from the memorized file. The prompt may then include the starting point in the
file all the way to the middle of method "foo", at the point you want the completion
to start. Finally, the expected_end should point to the end of the method, since
this was the original "correct" code.

"""Contains test data for the retrieval and prompt formatting stages."""

import dataclasses
import pickle
from collections.abc import Iterable
from dataclasses import dataclass
from pathlib import Path

import pytest
from megatron.tokenizer.tokenizer import StarCoderTokenizer

from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from base.static_analysis.parsing import Sr<PERSON><PERSON><PERSON>
from research.core.types import Chunk, Document
from research.data.rag.common import GenerateRetrievedChunksFromFim
from research.data.rag.constants import CONTENT_COLUMN, FILE_LANG_COLUMN, PATH_COLUMN
from research.data.rag.ender import EnderDataPromptFormattingConfig
from research.data.rag.retrieval_utils import serialize_retrieved_chunks
from research.data.rag.rogue import RogueSLDataPromptFormattingConfig
from research.fim.fim_sampling import FimProblem, SkipOrOutput
from research.retrieval.chunking_functions import <PERSON>LevelChunker, SignatureChunker
from research.retrieval.retrieval_database import RetrievalDatabase
from research.retrieval.types import Chunker


@dataclass
class RepoProblems:
    """Problems for a single repo."""

    file_list: list[dict[str, str]]
    """List of files as maps from columns to values."""

    problems: list[FimProblem]
    """List of FIM problems."""


@dataclass
class FileProblems:
    """Problems for a single file. Used to help construct RepoProblems."""

    file: dict[str, str]
    """Map from columns (content, langpart, max_stars_repo_path) to values."""

    problems: list[FimProblem]
    """FIM problems for this file."""


@dataclass
class FileOutputs:
    """Outputs for a single file. Used to help construct chunks."""

    file_chunks: list[Chunk]
    """List of chunks for this file."""

    problem_chunks: list[list[Chunk]]
    """List of chunks for each problem (the before state of the file)."""


def _file_outputs_from_chunker(chunker: Chunker, problems: FileProblems) -> FileOutputs:
    content = problems.file[CONTENT_COLUMN]
    path = problems.file[PATH_COLUMN]
    file_doc = Document.new(text=content, path=path)
    file_chunks = chunker.split_into_chunks(file_doc)
    assert file_chunks is not None
    for chunk in file_chunks:
        chunk.parent_doc = dataclasses.replace(chunk.parent_doc, text="")
    problem_chunks = []
    for problem in problems.problems:
        problem_doc = Document.new(
            text=problem.prefix.code + problem.suffix.code, path=path
        )
        chunks = chunker.split_into_chunks(problem_doc)
        assert chunks is not None
        for chunk in chunks:
            chunk.parent_doc = dataclasses.replace(chunk.parent_doc, text="")
        problem_chunks.append(chunks)

    return FileOutputs(
        file_chunks=file_chunks,
        problem_chunks=problem_chunks,
    )


def make_retrieval_output(fim_problem: FimProblem, chunks: dict[str, list[Chunk]]):
    serialized_chunks = {
        f"{key}_chunks": serialize_retrieved_chunks(chunks)
        for key, chunks in chunks.items()
    }
    return {
        "prefix": fim_problem.prefix.code,
        "suffix": fim_problem.suffix.code,
        "middle_char_start": fim_problem.middle_range.start,
        "middle_char_end": fim_problem.middle_range.stop,
        "file_path": str(fim_problem.file_path),
        "suffix_offset": len(fim_problem.suffix.range) - len(fim_problem.suffix.code),
        "middle_spans": pickle.dumps(fim_problem.middle_spans),
        **serialized_chunks,
    }


def make_prompt_tokens_rogue(
    tok: StarCoderTokenizer,
    problem: FimProblem,
    prefix_trunc: int,
    suffix_trunc: int,
    middle_trunc: int,
    chunks: Iterable[Chunk],
):
    path = str(problem.file_path)
    prefix = problem.prefix.code
    suffix = problem.suffix.code
    middle_spans = problem.middle_spans

    file_toks = [tok.filename_id, *tok.tokenize_safe(path)]
    prompt_tokens = [tok.retrieval_section_id]
    for chunk in chunks:
        prompt_tokens += [tok.retrieval_start_id]
        prompt_tokens += [tok.filename_id, *tok.tokenize_safe(chunk.parent_doc.path)]
        prompt_tokens += [tok.retrieval_body_id, *tok.tokenize_safe(chunk.text)]
    prompt_tokens += [tok.fim_prefix_id, *file_toks, tok.prefix_body_id]
    prompt_tokens += tok.tokenize_safe(prefix)[-(prefix_trunc - 1 - len(file_toks)) :]
    prompt_tokens += [tok.fim_suffix_id, *tok.tokenize_safe(suffix)[:suffix_trunc]]
    prompt_tokens += [tok.fim_middle_id]

    middle_tokens = []
    for middle in middle_spans:
        middle_tokens += (
            [tok.skip_id] if middle.skipped else tok.tokenize_safe(middle.content.code)
        )
    middle_tokens += [tok.eod_id]
    prompt_tokens += middle_tokens[:middle_trunc]
    return prompt_tokens


def make_prompt_tokens_rogue_sl(
    tok: StarCoderTokenizer,
    problem: FimProblem,
    far_prefix_start: int,
    far_prefix_end: int,
    suffix_trunc: int,
    middle_trunc: int,
    chunks: Iterable[Chunk],
) -> list[int]:
    path = str(problem.file_path)
    prefix = problem.prefix.code
    suffix = problem.suffix.code
    middle_spans = problem.middle_spans

    file_toks = [tok.filename_id, *tok.tokenize_safe(path)]
    prompt_tokens = [*file_toks, *tok.tokenize_safe("\n")]
    prompt_tokens += [tok.far_prefix_id]
    prompt_tokens += tok.tokenize_safe(prefix)[far_prefix_start:far_prefix_end]
    prompt_tokens += [tok.fim_suffix_id, *tok.tokenize_safe(suffix)[:suffix_trunc]]
    prompt_tokens += [tok.retrieval_section_id]
    for chunk in chunks:
        prompt_tokens += [tok.retrieval_start_id]
        prompt_tokens += [tok.filename_id, *tok.tokenize_safe(chunk.parent_doc.path)]
        prompt_tokens += [tok.retrieval_body_id, *tok.tokenize_safe(chunk.text)]
    prompt_tokens += [tok.fim_prefix_id, *tok.tokenize_safe(prefix)[far_prefix_end:]]
    prompt_tokens += [tok.fim_middle_id]

    middle_tokens = []
    for middle in middle_spans:
        middle_tokens += (
            [tok.skip_id] if middle.skipped else tok.tokenize_safe(middle.content.code)
        )
    middle_tokens += [tok.eod_id]
    prompt_tokens += middle_tokens[:middle_trunc]
    return prompt_tokens


def make_prompt_tokens_ender(
    tok: StarCoderTokenizer,
    problem: FimProblem,
    far_prefix_start: int,
    far_prefix_end: int,
    suffix_trunc: int,
    middle_trunc: int,
    chunks: Iterable[Chunk],
    signature_chunks: Iterable[Chunk],
):
    path = str(problem.file_path)
    prefix = problem.prefix.code
    suffix = problem.suffix.code
    middle_spans = problem.middle_spans

    file_toks = [tok.filename_id, *tok.tokenize_safe(path)]
    prompt_tokens = [*file_toks, *tok.tokenize_safe("\n")]
    prompt_tokens += [tok.far_prefix_id]
    prompt_tokens += tok.tokenize_safe(prefix)[far_prefix_start:far_prefix_end]
    prompt_tokens += [tok.retrieval_section_id]
    for chunk in chunks:
        prompt_tokens += [tok.retrieval_start_id]
        prompt_tokens += [tok.filename_id, *tok.tokenize_safe(chunk.parent_doc.path)]
        prompt_tokens += [tok.retrieval_body_id, *tok.tokenize_safe(chunk.text)]
    prompt_tokens += [tok.sig_begin_id]
    prompt_tokens += tok.tokenize_safe("\n")
    signature_chunks = list(signature_chunks)
    for chunk in signature_chunks:
        prompt_tokens += tok.tokenize_safe(chunk.text)
        prompt_tokens += tok.tokenize_safe("\n\n")
    if len(signature_chunks) > 0:
        prompt_tokens.pop()
    prompt_tokens += tok.tokenize_safe("\n")
    prompt_tokens += [tok.sig_end_id]
    prompt_tokens += [tok.fim_prefix_id, *tok.tokenize_safe(prefix)[far_prefix_end:]]
    prompt_tokens += [tok.fim_suffix_id, *tok.tokenize_safe(suffix)[:suffix_trunc]]
    prompt_tokens += [tok.fim_middle_id]

    middle_tokens = []
    for middle in middle_spans:
        middle_tokens += (
            [tok.skip_id] if middle.skipped else tok.tokenize_safe(middle.content.code)
        )
    middle_tokens += [tok.eod_id]
    prompt_tokens += middle_tokens[:middle_trunc]
    return prompt_tokens


LINE_RETRIEVER_CONFIG = dict(
    chunker={
        "max_lines_per_chunk": 40,
        "name": "line_level",
    },
    query_formatter={
        "name": "simple_query",
        "max_tokens": 50,
    },
    document_formatter={
        "name": "simple_document",
    },
    scorer={
        "name": "bm25",
    },
)


SIGNATURE_CONFIG = dict(
    chunker={
        "name": "signature",
    },
    query_formatter={
        "add_path": True,
        "add_suffix": True,
        "max_lines": -1,
        "max_tokens": 1023,
        "name": "ethanol6_query",
        "tokenizer_name": "StarCoderTokenizer",
    },
    document_formatter={
        "add_path": False,
        "name": "simple_document",
        "tokenizer_name": "StarCoderTokenizer",
    },
    scorer={
        "name": "bm25",
    },
)


ROGUE_CONFIG_DICT = {
    "input": "",
    "output": "",
    "random_seed": 74912,
    "seq_len": 4097,
    "max_prefix_tokens": 1280,
    "max_suffix_tokens": 768,
    "max_retrieved_chunk_tokens": -1,
    "max_prompt_tokens": 3838,
    "max_target_tokens": 256,
    "retrieval_dropout": 0.0,
    "max_filename_tokens": 50,
    "preamble": "",
    "prepend_path_to_retrieved": True,
    "prefix_after_suffix": False,
    "add_retrieval_after_context": False,
    "only_truncate_true_prefix": True,
    "always_use_suffix_token": True,
    "nearby_prefix_char_len": 0,
    "prefix_char_offset": 0,
    "samples_column": "prompt_tokens",
    "num_validation_samples": 10000,
}


ROGUE_SL_CONFIG = RogueSLDataPromptFormattingConfig(
    input="",
    output="",
    tokenizer_name="starcoder",
    num_validation_samples=50000,
    seq_len=4097,
    component_order=["prefix", "suffix", "retrieval", "nearby_prefix"],
    max_prefix_tokens=1030,
    max_suffix_tokens=768,
    max_retrieved_chunk_tokens=-1,
    max_filename_tokens=50,
    max_target_tokens=256,
    max_prompt_tokens=3838,
    random_seed=74912,
    data_augmentation_rate=0.5,
    dense_retrieval_dropout_rate=0.0,
    max_prompt_token_range=None,
    context_quant_token_len=50,
    nearby_prefix_token_len=250,
    nearby_prefix_token_overlap=0,
    nearby_suffix_token_len=0,
    nearby_suffix_token_overlap=0,
    use_far_prefix_token=True,
)


ENDER_CONFIG = EnderDataPromptFormattingConfig(
    random_seed=74912,
    shuffle_retrieved_chunks=False,
    component_order=["prefix", "retrieval", "signature", "nearby_prefix", "suffix"],
    max_prefix_tokens=1024,
    max_suffix_tokens=512,
    max_signature_tokens=1024,
    max_retrieved_chunk_tokens=-1,
    max_filename_tokens=50,
    max_header_tokens=0,
    max_target_tokens=256,
    max_prompt_tokens=6142,
    seq_len=7937,
    # data_augmentation_rate=0.5,
    data_augmentation_rate=0.0,
    dense_retrieval_dropout_rate=0.3,
    signature_retrieval_dropout_rate=0.3,
    max_prompt_token_range=(3124, 7678),
    context_quant_token_len=64,
    nearby_prefix_token_len=512,
    nearby_prefix_token_overlap=0,
    nearby_suffix_token_len=0,
    nearby_suffix_token_overlap=0,
    num_validation_samples=50000,
)


@pytest.fixture(scope="session")
def generate_retrieved_chunks_rogue():
    gen = GenerateRetrievedChunksFromFim(
        retrieval_database_factories={
            "line": lambda: RetrievalDatabase.from_yaml_config(LINE_RETRIEVER_CONFIG),
        },
        retrieval_languages=["python", "javascript"],
        num_retrieved_chunks=4,
    )
    return gen


@pytest.fixture(scope="session")
def generate_retrieved_chunks_ender():
    gen = GenerateRetrievedChunksFromFim(
        retrieval_database_factories={
            "line": lambda: RetrievalDatabase.from_yaml_config(LINE_RETRIEVER_CONFIG),
            "signature": lambda: RetrievalDatabase.from_yaml_config(SIGNATURE_CONFIG),
        },
        retrieval_languages=["python", "javascript"],
        num_retrieved_chunks=4,
    )
    return gen


def _foo_problems() -> FileProblems:
    foo_content = "def foo(a):\n    return a + 1\n"
    return FileProblems(
        file={
            CONTENT_COLUMN: foo_content,
            FILE_LANG_COLUMN: "python",
            PATH_COLUMN: "foo.py",
        },
        problems=[
            FimProblem(
                prefix=SrcSpan(CharRange(0, 4), "def "),
                suffix=SrcSpan(CharRange(11, len(foo_content)), "\n    return a + 1\n"),
                middle_spans=[
                    SkipOrOutput(SrcSpan(CharRange(4, 11), "foo(a):"), False)
                ],
                file_path=Path("foo.py"),
                middle_node_type="identifier",
                original_middle_code="foo(a):",
            ),
            # Empty target, checks the case where new_doc == old_doc
            FimProblem(
                prefix=SrcSpan(CharRange(0, 4), foo_content[:4]),
                suffix=SrcSpan(CharRange(4, len(foo_content)), foo_content[4:]),
                middle_spans=[SkipOrOutput(SrcSpan(CharRange(4, 4), ""), False)],
                file_path=Path("foo.py"),
                middle_node_type="identifier",
                original_middle_code="foo",
            ),
        ],
    )


def _bar_problems() -> FileProblems:
    bar_content = "function bar(b) {\n    return b + 2;\n}\n"
    return FileProblems(
        file={
            CONTENT_COLUMN: bar_content,
            FILE_LANG_COLUMN: "javascript",
            PATH_COLUMN: "bar.js",
        },
        problems=[
            FimProblem(
                prefix=SrcSpan(CharRange(0, 13), "function bar("),
                suffix=SrcSpan(CharRange(30, len(bar_content)), ") + 2;\n}\n"),
                middle_spans=[
                    SkipOrOutput(SrcSpan(CharRange(13, 14), "b"), False),
                    SkipOrOutput(SrcSpan(CharRange(14, 15), ")"), True),
                    SkipOrOutput(SrcSpan(CharRange(15, 30), " {\n    return b"), False),
                ],
                file_path=Path("bar.js"),
                middle_node_type="identifier",
                original_middle_code="b",
            ),
        ],
    )


FOO_PROBLEMS = _foo_problems()

FOO_LINE_OUTPUTS = _file_outputs_from_chunker(
    LineLevelChunker(max_lines_per_chunk=40), FOO_PROBLEMS
)

FOO_SIGNATURE_OUTPUTS = _file_outputs_from_chunker(SignatureChunker(), FOO_PROBLEMS)

BAR_PROBLEMS = _bar_problems()

BAR_LINE_OUTPUTS = _file_outputs_from_chunker(
    LineLevelChunker(max_lines_per_chunk=40), BAR_PROBLEMS
)

BAR_SIGNATURE_OUTPUTS = _file_outputs_from_chunker(SignatureChunker(), BAR_PROBLEMS)

FOOBAR_REPO = RepoProblems(
    file_list=[FOO_PROBLEMS.file, BAR_PROBLEMS.file],
    problems=FOO_PROBLEMS.problems + BAR_PROBLEMS.problems,
)

FOOBAR_LINE_CHUNKS = [
    [FOO_LINE_OUTPUTS.problem_chunks[0][0], BAR_LINE_OUTPUTS.file_chunks[0]],
    [FOO_LINE_OUTPUTS.problem_chunks[1][0], BAR_LINE_OUTPUTS.file_chunks[0]],
    [BAR_LINE_OUTPUTS.problem_chunks[0][0], FOO_LINE_OUTPUTS.file_chunks[0]],
]

FOOBAR_ROGUE_RETRIEVAL = [
    make_retrieval_output(problem, {"line": chunks})
    for problem, chunks in zip(FOOBAR_REPO.problems, FOOBAR_LINE_CHUNKS, strict=True)
]


def _foobar_rogue_prompts(
    repo: RepoProblems,
    chunks: list[list[Chunk]],
):
    tok = StarCoderTokenizer()
    return [
        make_prompt_tokens_rogue(
            tok, repo.problems[0], 1280, 768, 256, reversed(chunks[0][1:])
        ),
        make_prompt_tokens_rogue(
            tok, repo.problems[1], 1280, 768, 256, reversed(chunks[1][1:])
        ),
        make_prompt_tokens_rogue(
            tok, repo.problems[2], 1280, 768, 256, reversed(chunks[2][1:])
        ),
    ]


FOOBAR_ROGUE_PROMPTS = _foobar_rogue_prompts(FOOBAR_REPO, FOOBAR_LINE_CHUNKS)


def _foobar_roguesl_prompts(repo: RepoProblems, chunks: list[list[Chunk]]):
    tok = StarCoderTokenizer()
    return [
        make_prompt_tokens_rogue_sl(
            tok, repo.problems[0], 0, 0, 768, 256, reversed(chunks[0][1:])
        ),
        make_prompt_tokens_rogue_sl(
            tok, repo.problems[1], 0, 0, 768, 256, reversed(chunks[1][1:])
        ),
        make_prompt_tokens_rogue_sl(
            tok, repo.problems[2], 0, 0, 768, 256, reversed(chunks[2][1:])
        ),
    ]


FOOBAR_ROGUESL_PROMPTS = _foobar_roguesl_prompts(FOOBAR_REPO, FOOBAR_LINE_CHUNKS)

FOOBAR_SIGNATURE_CHUNKS = [
    [
        BAR_SIGNATURE_OUTPUTS.file_chunks[1],
        FOO_SIGNATURE_OUTPUTS.problem_chunks[0][0],
        BAR_SIGNATURE_OUTPUTS.file_chunks[0],
    ],
    [
        FOO_SIGNATURE_OUTPUTS.problem_chunks[1][1],
        BAR_SIGNATURE_OUTPUTS.file_chunks[1],
        FOO_SIGNATURE_OUTPUTS.problem_chunks[1][0],
        BAR_SIGNATURE_OUTPUTS.file_chunks[0],
    ],
    [
        FOO_SIGNATURE_OUTPUTS.file_chunks[1],
        BAR_SIGNATURE_OUTPUTS.problem_chunks[0][0],
        FOO_SIGNATURE_OUTPUTS.file_chunks[0],
    ],
]

FOOBAR_ENDER_RETRIEVAL = [
    make_retrieval_output(problem, {"line": chunks, "signature": signature})
    for problem, chunks, signature in zip(
        FOOBAR_REPO.problems, FOOBAR_LINE_CHUNKS, FOOBAR_SIGNATURE_CHUNKS, strict=True
    )
]


def _foobar_ender_prompts(
    repo: RepoProblems, chunks: list[list[Chunk]], sigs: list[list[Chunk]]
):
    tok = StarCoderTokenizer()
    problems = repo.problems
    return [
        make_prompt_tokens_ender(
            tok, problems[0], 0, 0, 768, 256, reversed(chunks[0][1:]), reversed(sigs[0])
        ),
        make_prompt_tokens_ender(
            tok, problems[1], 0, 0, 768, 256, reversed(chunks[1][1:]), reversed(sigs[1])
        ),
        make_prompt_tokens_ender(
            tok, problems[2], 0, 0, 768, 256, reversed(chunks[2][1:]), reversed(sigs[2])
        ),
    ]


FOOBAR_ENDER_PROMPTS = _foobar_ender_prompts(
    FOOBAR_REPO, FOOBAR_LINE_CHUNKS, FOOBAR_SIGNATURE_CHUNKS
)


def _long_problems():
    # With a three-digit number, each snippet is 4 lines, 40 chars, 20 tokens long,
    # including the delimiters.
    x_fmt = "def funct_x{num}(x):\n    return x + {num}\n"
    y_fmt = "def funct_y{num}(y):\n    return y - {num}\n"
    z_fmt = "def funct_z{num}(z):\n    return z * {num}\n"
    long_content = "\n\n".join(
        [y_fmt.format(num=i) for i in range(100, 195)]
        + [x_fmt.format(num=i) for i in range(195, 220)]
        + [z_fmt.format(num=i) for i in range(220, 305)]
        + [x_fmt.format(num=i) for i in range(305, 306)]
        + [y_fmt.format(num=i) for i in range(306, 355)]
        + [x_fmt.format(num=i) for i in range(355, 356)]
        + [z_fmt.format(num=i) for i in range(356, 405)]
        + [x_fmt.format(num=i) for i in range(405, 406)]
        + [y_fmt.format(num=i) for i in range(406, 450)]
    )
    start = 99 * 40
    end = 149 * 40 + 39
    # Make the middle ~50 functions long (200 lines, 2000 chars, 1000 tokens)
    # since that makes it easier to test overlap behavior.
    return FileProblems(
        file={
            CONTENT_COLUMN: long_content,
            FILE_LANG_COLUMN: "python",
            PATH_COLUMN: "long.py",
        },
        problems=[
            FimProblem(
                prefix=SrcSpan(CharRange(0, start + 15), long_content[: start + 15]),
                suffix=SrcSpan(
                    CharRange(end, len(long_content)), ")" + long_content[end:]
                ),
                middle_spans=[
                    SkipOrOutput(
                        SrcSpan(CharRange(start + 16, start + 17), "x"), False
                    ),
                    SkipOrOutput(SrcSpan(CharRange(start + 17, start + 17), ")"), True),
                    SkipOrOutput(
                        SrcSpan(
                            CharRange(start + 18, end), long_content[start + 18 : end]
                        ),
                        False,
                    ),
                ],
                file_path=Path("long.py"),
                middle_node_type="identifier",
                original_middle_code="x",
            )
        ],
    )


LONG_PROBLEMS = _long_problems()
LONG_LINE_OUTPUTS = _file_outputs_from_chunker(
    LineLevelChunker(max_lines_per_chunk=40), LONG_PROBLEMS
)
LONG_SIGNATURE_OUTPUTS = _file_outputs_from_chunker(SignatureChunker(), LONG_PROBLEMS)
LONG_REPO = RepoProblems(
    file_list=[LONG_PROBLEMS.file, FOO_PROBLEMS.file, BAR_PROBLEMS.file],
    problems=LONG_PROBLEMS.problems,
)


LONG_LINE_CHUNKS = [
    [chunk for chunk in LONG_LINE_OUTPUTS.problem_chunks[0] if "x" in chunk.text],
]

LONG_ROGUE_RETRIEVAL = [
    make_retrieval_output(problem, {"line": chunks})
    for problem, chunks in zip(LONG_REPO.problems, LONG_LINE_CHUNKS, strict=True)
]


def _long_rogue_prompts(repo, chunks):
    tok = StarCoderTokenizer()
    return [
        make_prompt_tokens_rogue(
            tok, repo.problems[0], 1280, 768, 256, reversed(chunks[0][1:])
        )
    ]


LONG_ROGUE_PROMPTS = _long_rogue_prompts(LONG_REPO, LONG_LINE_CHUNKS)


def _long_roguesl_prompts(repo, chunks):
    tok = StarCoderTokenizer()
    # Total prefix len is 1988, and suffix len is 4001
    return [
        make_prompt_tokens_rogue_sl(
            tok, repo.problems[0], 750, 1750, 751, 256, reversed(chunks[0][1:])
        )
    ]


LONG_ROGUESL_PROMPTS = _long_roguesl_prompts(LONG_REPO, LONG_LINE_CHUNKS)

LONG_SIGNATURE_CHUNKS = [
    [LONG_SIGNATURE_OUTPUTS.problem_chunks[0][i] for i in [0, 59, 79, 60]]
]

LONG_ENDER_RETRIEVAL = [
    make_retrieval_output(problem, {"line": chunks, "signature": signature})
    for problem, chunks, signature in zip(
        LONG_REPO.problems, LONG_LINE_CHUNKS, LONG_SIGNATURE_CHUNKS, strict=True
    )
]


def _long_ender_prompts(
    repo: RepoProblems, chunks: list[list[Chunk]], sigs: list[list[Chunk]]
):
    tok = StarCoderTokenizer()
    problems = repo.problems
    # Total prefix len is 1988, and suffix len is 4001
    return [
        make_prompt_tokens_ender(
            tok,
            problems[0],
            512,
            1536,
            481,
            256,
            reversed(chunks[0][1:]),
            reversed(sigs[0][1:]),
        )
    ]


LONG_ENDER_PROMPTS = _long_ender_prompts(
    LONG_REPO, LONG_LINE_CHUNKS, LONG_SIGNATURE_CHUNKS
)

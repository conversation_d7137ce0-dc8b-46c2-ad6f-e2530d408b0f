import dataclasses
import json
import logging
import pickle
from dataclasses import dataclass
from functools import partial
from typing import Any, Dict, Generic, Sequence, TypeVar

from base.blob_names.python.blob_names import <PERSON><PERSON><PERSON><PERSON><PERSON>, FilePath
from base.datasets.completion import (
    CompletionRequest,
    GranularEditEvent as completion_GranularEditEvent,
    RecencyInfo,
)
from base.datasets.hindsight_completion import Hindsight<PERSON>ompletionDatum
from base.datasets.recency_info import ReplacementText
from base.diff_utils.diff_utils import DiffHunk, File
from base.diff_utils.edit_events import GranularEditEvent, SingleEdit
from base.diff_utils.retriever_util_completion import FileContentProvider
from base.prompt_format.chunk_origin import Chunk<PERSON>rigin, ChunkOriginValues
from base.prompt_format.common import PromptChunk
from base.prompt_format_completion.ender_prompt_formatter import (
    EnderPromptFormatter,
    EnderPromptFormatterConfig,
    StatelessCachingConfig,
    TokenApportionmentConfig,
)
from base.prompt_format_completion.overlap import modified_chunks_filter
from base.prompt_format_completion.prompt_formatter import PromptInput
from base.ranges.range_types import CharRange
from base.static_analysis.parsing import SrcSpan
from base.tokenizers import tokenizer as prod_tokenizer
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer

from research.core.recency_info import convert_from_datasets_recency_info
from research.data.rag.retrieval_utils import deserialize_retrieved_prompt_chunks
from research.data.spark.pipelines.utils import map_parquet
from research.retrieval.types import Chunk

logger = logging.getLogger(__name__)


AT = TypeVar("AT")


@dataclass
class OutputWithBoundary(Generic[AT]):
    """Records whether content should have a pause or stop token at the end of the output."""

    content: AT
    """The content being output."""

    pause_at_end: bool = False
    """Whether the content should be followed by a pause."""

    stop_at_end: bool = False
    """Whether the content should be followed by a stop token."""

    def __post_init__(self):
        assert not (self.pause_at_end and self.stop_at_end)


class ResearchFileContentProvider(FileContentProvider):
    """A file retriever that uses the cache. Cache must return files in the same order as the input keys."""

    def __init__(self, cache):
        self.cache = cache

    def retrieve_files(
        self,
        paths: Dict[BlobName, FilePath],
        expected: bool,
    ) -> Dict[BlobName, File | None]:
        blob_names = paths.keys()
        blob_contents = self.cache.get(blob_names)
        results = {}
        # We assume that the cache returns files in the same order as the input keys.
        for blob_name, blob_content in zip(blob_names, blob_contents):
            results[blob_name] = (
                File(path=str(blob_content.path), contents=blob_content.content)
                if blob_content
                else None
            )
        return results


class FunctionWrapper:
    def __init__(self, make_fn):
        self.make_fn = make_fn
        self.fn = None

    def __call__(self, *args, **kwargs):
        if self.fn is None:
            self.fn = self.make_fn()
        return self.fn(*args, **kwargs)


@dataclass
class EnderDataPromptFormattingConfig:
    """The configuration for the Ender prompt formatter."""

    max_content_len: int
    input_fraction: float
    prefix_fraction: float
    max_path_tokens: int

    max_dense_signature_tokens: int
    max_recency_retriever_tokens: int
    max_diff_retriever_tokens: int
    include_diff_retriever: bool

    max_target_tokens: int

    always_filter_chunks_by_recency: bool = True

    def as_dict(self) -> dict:
        """Return configuration as a dictionary."""
        return dataclasses.asdict(self)


#######################
# Conversion Utilities #
#######################


def deserialize_hindsight_problem(
    hindsight_problem: dict,
) -> dict:
    """Deserialize the hindsight problems."""
    return {"hindsight_problem": HindsightCompletionDatum.from_dict(hindsight_problem)}


def chunk_to_prompt_chunk(chunk: Chunk, origin: str) -> PromptChunk:
    """Converts a chunk to a prompt chunk."""
    return PromptChunk(
        text=chunk.text,
        path=chunk.parent_doc.path,
        unique_id=chunk.id,
        origin=origin,
        char_start=chunk.char_offset,
        char_end=chunk.char_offset + chunk.length,
        blob_name=chunk.parent_doc.id,
        header=chunk.header,
    )


def diff_hunk_to_prompt_chunk(
    diff_hunk: DiffHunk,
    filter_duplicated_file_paths: bool = True,
) -> PromptChunk:
    """Converts a recent chunk to a prompt chunk."""
    return PromptChunk(
        text=diff_hunk.text,
        path=diff_hunk.path_header(filter_duplicated_file_paths),
        char_start=0,
        char_end=len(diff_hunk.text),
        origin=ChunkOrigin.DIFF_RETRIEVER.value,
    )


def recent_chunk_to_prompt_chunk(
    recent_change: ReplacementText,
) -> PromptChunk:
    """Converts a recent chunk to a prompt chunk."""
    return PromptChunk(
        blob_name=recent_change.blob_name,
        path=recent_change.path,
        text=recent_change.replacement_text,
        char_start=recent_change.crange.start,
        char_end=recent_change.crange.stop,
        origin=ChunkOrigin.RECENCY_RETRIEVER.value,
    )


def convert_edit_events(
    edit_events: list[completion_GranularEditEvent],
) -> list[GranularEditEvent]:
    return [
        GranularEditEvent(
            path=event.path,
            before_blob_name=event.before_blob_name,
            after_blob_name=event.after_blob_name,
            edits=[
                SingleEdit(
                    before_start=edit.before_start,
                    after_start=edit.after_start,
                    before_text=edit.before_text,
                    after_text=edit.after_text,
                )
                for edit in event.edits
            ],
        )
        for event in edit_events
    ]


def ground_truth_to_middle_spans(stop_at_end: bool):
    def ground_truth_to_middle_spans_fn(
        ground_truth: str,
    ) -> dict:
        """Convert ground truth to middle spans."""
        middle_spans: Sequence[OutputWithBoundary[SrcSpan]] = [
            OutputWithBoundary(
                content=SrcSpan(CharRange(0, len(ground_truth)), ground_truth),
                stop_at_end=stop_at_end,
            )
        ]
        return {"middle_spans": pickle.dumps(middle_spans)}

    return ground_truth_to_middle_spans_fn


def keep_essential_features_only(
    prefix: str,
    prefix_begin: int,
    suffix: str,
    middle_spans: bytes,
    file_path: str,
    retrieved_chunks: str,
    recency_info: str | None,
    # These are not used for prompt generation, but are useful for debugging and analysis.
    # inference_response_token_ids is required to interface with the prompt stage.
    original_ground_truth: str,
    inference_response_token_ids: str,
    request_id: str,
):
    return {
        "prefix": prefix,
        "prefix_begin": prefix_begin,
        "suffix": suffix,
        "middle_spans": middle_spans,
        "file_path": file_path,
        "retrieved_chunks": retrieved_chunks,
        "recency_info": recency_info,
        "original_ground_truth": original_ground_truth,
        "inference_response_token_ids": inference_response_token_ids,
        "request_id": request_id,
    }


######################
# Prompt Formatting  #
######################


def generate_ender_prompt_fn(
    tokenizer: prod_tokenizer.Tokenizer,
    config: EnderDataPromptFormattingConfig,
) -> map_parquet.FlatMapFn:
    """Return a row-wise function to generate Ender prompts. Does not include target tokens."""

    def _generate_ender_promptprefix(
        prefix: str,
        prefix_begin: int,
        suffix: str,
        file_path: str,
        retrieved_chunks: str,
        recency_info: str | None,
    ) -> dict[str, list[int] | str]:
        """Generate Ender prompts."""

        assert (
            prefix is not None
            and suffix is not None
            and file_path is not None
            and retrieved_chunks is not None
        ), "These fields must be non-None when generating prompts."

        deserialized_retrieved_chunks = deserialize_retrieved_prompt_chunks(
            retrieved_chunks
        )
        # Filter retrieved chunks based on recency info if we are using recency retriever in the prompt
        if (
            config.max_recency_retriever_tokens > 0
            or config.always_filter_chunks_by_recency
        ):
            recency_info_obj = (
                RecencyInfo.from_json(recency_info) if recency_info else None
            )
            if recency_info_obj is not None:
                deserialized_retrieved_chunks = list(
                    modified_chunks_filter(
                        deserialized_retrieved_chunks,
                        convert_from_datasets_recency_info(recency_info_obj),
                        origins=ChunkOriginValues,
                        skip_origins=[
                            ChunkOrigin.RECENCY_RETRIEVER.value,
                            ChunkOrigin.DIFF_RETRIEVER.value,
                        ],
                    )
                )

        apportionment_config = TokenApportionmentConfig(
            max_content_len=config.max_content_len,
            input_fraction=config.input_fraction,
            prefix_fraction=config.prefix_fraction,
            max_path_tokens=config.max_path_tokens,
            per_retriever_max_tokens={
                "dense_signature": config.max_dense_signature_tokens,
                "recency_retriever": config.max_recency_retriever_tokens,
                "diff_retriever": config.max_diff_retriever_tokens,
            },
        )

        if config.include_diff_retriever:
            component_order = (
                "path",
                "prefix",
                "retrieval",
                "signature",
                "diff",
                "nearby_prefix",
                "suffix",
            )
        else:
            component_order = (
                "path",
                "prefix",
                "retrieval",
                "signature",
                "nearby_prefix",
                "suffix",
            )

        ender_prompt_formatter_config = EnderPromptFormatterConfig(
            stateless_caching_config=StatelessCachingConfig(
                nearby_prefix_token_len=512,
                quantize_token_len=64,
                quantize_char_len=250,
            ),
            component_order=component_order,
            filter_visible_chunks_by_content=True,
            signature_chunk_origin="dense_signature",
        )

        prompt_formatter = EnderPromptFormatter(
            apportionment_config=apportionment_config,
            prompt_formatter_config=ender_prompt_formatter_config,
            tokenizer=tokenizer,
        )

        prompt_input = PromptInput(
            prefix=prefix,
            suffix=suffix,
            prefix_begin=prefix_begin,
            path=file_path,
            retrieved_chunks=deserialized_retrieved_chunks,
            lang=None,
        )

        formatter_output = prompt_formatter.format_prompt(
            prompt_input, config.max_target_tokens
        )
        prompt_tokens = formatter_output.tokens()
        return {"prompt_tokens": prompt_tokens}

    return _generate_ender_promptprefix


def add_middle_spans_to_prompt_tokens(
    tokenizer: prod_tokenizer.Tokenizer,
    config: EnderDataPromptFormattingConfig,
    prompt_tokens: list[int],
    middle_spans: bytes,
    request_id: str,
) -> dict[str, list[int] | str]:
    """Add middle spans to prompt tokens."""
    assert prompt_tokens is not None, "prompt_tokens must be non-None."
    assert middle_spans is not None, "middle_spans must be non-None."

    deserialized_middle_spans: list[OutputWithBoundary[SrcSpan]] = pickle.loads(
        middle_spans
    )
    special_tokens = tokenizer.special_tokens
    assert (
        len(deserialized_middle_spans) == 1
    ), "Not implemented for multiple middle spans."
    assert isinstance(
        special_tokens,
        (prod_tokenizer.RagSpecialTokens),
    )
    deserialized_middle_span = deserialized_middle_spans[0]
    target_tokens = tokenizer.tokenize_safe(deserialized_middle_span.content.code)
    if deserialized_middle_span.pause_at_end:
        target_tokens.append(special_tokens.pause)
    if deserialized_middle_span.stop_at_end:
        target_tokens.append(special_tokens.eos)
    target_tokens = target_tokens[: config.max_target_tokens]
    prompt_tokens = prompt_tokens + target_tokens
    # Prompt consists of both prompt and target tokens.
    return {
        "prompt_tokens": prompt_tokens,
        "target_tokens": target_tokens,
        "request_id": request_id,
    }


def add_token_ids_to_prompt_tokens(
    config: EnderDataPromptFormattingConfig,
    prompt_tokens: list[int],
    inference_response_token_ids: str,
    request_id: str,
) -> dict[str, list[int] | str]:
    """Add token ids to prompt tokens."""
    assert prompt_tokens is not None, "prompt_tokens must be non-None."
    assert (
        inference_response_token_ids is not None
    ), "inference_response_token_ids must be non-None."
    target_tokens = json.loads(inference_response_token_ids)
    target_tokens = target_tokens[: config.max_target_tokens]
    prompt_tokens = prompt_tokens + target_tokens
    return {
        "prompt_tokens": prompt_tokens,
        "target_tokens": target_tokens,
        "request_id": request_id,
    }


def add_target_to_prompt_tokens_fn(tokenizer, config) -> map_parquet.FlatMapFn:
    """Add target tokens to prompt tokens."""

    def add_target_to_prompt_tokens(
        prompt_tokens: list[int],
        middle_spans: bytes | None,
        inference_response_token_ids: str,
        use_hindsight_ground_truth: bool,
        request_id: str,
    ) -> dict[str, list[int] | str]:
        """
        prompt_tokens: the prompt tokens
        middle_spans: the middle spans (ground truth from hindsight)
        inference_response_token_ids: the inference response token ids
        use_hindsight_ground_truth: whether to use the hindsight ground truth or the inference response
        request_id: the request id
        """

        if use_hindsight_ground_truth:
            assert middle_spans is not None, "middle_spans must be non-None."
            return add_middle_spans_to_prompt_tokens(
                tokenizer=tokenizer,
                config=config,
                prompt_tokens=prompt_tokens,
                middle_spans=middle_spans,
                request_id=request_id,
            )
        else:
            return add_token_ids_to_prompt_tokens(
                config=config,
                prompt_tokens=prompt_tokens,
                inference_response_token_ids=inference_response_token_ids,
                request_id=request_id,
            )

    return add_target_to_prompt_tokens


#######################
# Processing Classes  #
#######################
class TruncateTargetTokens:
    """Truncate target tokens by inserting pause when model predicts pause."""

    def __init__(
        self, tokenizer: Qwen25CoderTokenizer, max_target_tokens: int, config: dict
    ):
        self.tokenizer = tokenizer
        self.max_target_tokens = max_target_tokens
        self.config = config
        self._model = None

    @property
    def pause_token_id(self) -> int:
        return self.tokenizer.special_tokens.pause

    @property
    def eos_token_id(self) -> int:
        return self.tokenizer.special_tokens.eos

    @property
    def model(self):
        self.__init_model()
        assert self._model is not None
        return self._model

    def __init_model(self):
        if self._model is not None:
            return self._model

        from research.eval.harness.factories import create_model
        from research.models.fastforward_llama_models import LLAMA_FastForwardModel

        self._model = create_model(self.config)
        assert isinstance(self._model, LLAMA_FastForwardModel)
        self._model.load()
        return self._model

    def __call__(self, prompt_tokens: list[int], target_tokens: list[int]) -> dict:
        """Truncate target tokens by inserting pause or eos token correctly.

        args:
            prompt_tokens: the prompt tokens, which includes the target tokens
            target_tokens: the target tokens

        returns:
            a dict containing the middle spans and the original ground truth.
        """
        import torch
        from research.models.fastforward_llama_models import LLAMA_FastForwardModel

        assert len(target_tokens) != 0, "target_tokens must not be empty"
        assert (
            len(target_tokens) <= self.max_target_tokens
        ), f"{len(target_tokens)=} > {self.max_target_tokens=}"

        original_target_str = self.tokenizer.detokenize(target_tokens)

        assert isinstance(self.model, LLAMA_FastForwardModel)
        outputs = self.model.forward_pass_single_logits(torch.tensor(prompt_tokens))
        # Predict the probability for the current target tokens, not the next token
        target_logits = outputs[:-1][-(len(target_tokens)) :]
        predicted_token_ids = torch.argmax(target_logits, dim=-1).tolist()

        assert len(predicted_token_ids) == len(
            target_tokens
        ), f"{len(predicted_token_ids)=} != {len(target_tokens)=}"

        final_target_token_ids = []
        middle_spans: list[OutputWithBoundary[SrcSpan]] = []
        for target_token, predicted_token_id in zip(target_tokens, predicted_token_ids):
            if predicted_token_id == self.pause_token_id:
                target_str = self.tokenizer.detokenize(final_target_token_ids)
                middle_spans.append(
                    OutputWithBoundary(
                        content=SrcSpan(CharRange(0, len(target_str)), target_str),
                        pause_at_end=True,
                    )
                )
                return {
                    "middle_spans": pickle.dumps(middle_spans),
                    "original_ground_truth": original_target_str,
                }
            final_target_token_ids.append(target_token)

        stop_at_end = False
        if final_target_token_ids[-1] == self.eos_token_id:
            final_target_token_ids.pop()
            stop_at_end = True

        target_str = self.tokenizer.detokenize(final_target_token_ids)
        middle_spans.append(
            OutputWithBoundary(
                content=SrcSpan(CharRange(0, len(target_str)), target_str),
                stop_at_end=stop_at_end,
            )
        )
        return {
            "middle_spans": pickle.dumps(middle_spans),
            "original_ground_truth": original_target_str,
        }


def make_generate_retrieved_chunks_from_hindsight_problem():
    from research.retrieval.retrieval_database import RetrievalDatabase
    from typing import Iterator, Mapping, Callable
    from base.datasets.gcs_blob_cache import GCSBlobCache
    from google.cloud import storage
    from base.datasets.gcp_creds import get_gcp_creds
    from base.datasets.tenants import get_tenant
    from base.logging.secret_logging import get_safe_logger
    from base.diff_utils.retriever_util_completion import (
        EditEventConstructionInput,
        filter_replacement_text,
        get_files_before_replacement,
        apply_replacements,
        get_squashable_edits,
    )
    from research.data.rag.retrieval_utils import (
        serialize_retrieved_prompt_chunks,
    )
    from base.diff_utils.diff_formatter import format_file_changes_with_ranges
    from research.retrieval.types import Document
    from research.core.model_input import ModelInput

    @dataclass
    class GenerateRetrievedChunksFromHindsightProblem:
        """A class to generate retrieved chunks from hindsight problems."""

        def __init__(
            self,
            retrieval_database_factories: Mapping[str, Callable[[], RetrievalDatabase]],
            num_retrieved_chunks: int,
            tenant_name: str,
            diff_context_lines: int = 3,
            big_event_lines: int = 8,
            use_smart_header: bool = True,
            filter_duplicated_file_paths: bool = True,
            max_total_changed_chars: int = 5000,
        ):
            self.retrieval_database_factories = retrieval_database_factories
            self.num_retrieved_chunks = num_retrieved_chunks
            self.tenant_name = tenant_name
            self._diff_context_lines = diff_context_lines
            self._big_event_lines = big_event_lines
            self._use_smart_header = use_smart_header
            self._filter_duplicated_file_paths = filter_duplicated_file_paths
            self._max_total_changed_chars = max_total_changed_chars
            self._safe_logger = get_safe_logger(
                logger=logger, secret_logs_enabled=False
            )
            self._ret_dbs = dict[str, RetrievalDatabase]()
            self._cache: GCSBlobCache | None = None
            self.counter = 0

        @property
        def cache(self) -> GCSBlobCache:
            """The cache."""
            self.__init_cache()
            assert self._cache is not None
            return self._cache

        def __init_cache(self):
            """Initialize the cache."""
            if self._cache is not None:
                return
            gcp_creds, _ = get_gcp_creds()
            tenant = get_tenant(self.tenant_name)
            client = storage.Client(project=tenant.project_id, credentials=gcp_creds)
            bucket = client.bucket(tenant.blob_bucket_name)
            self._cache = GCSBlobCache(
                bucket=bucket,
                bucket_prefix=tenant.blob_bucket_prefix,
                max_size_bytes=1_000_000_000,  # 1GB cache size
                num_threads=10,
            )

        def __init_retrieval_dbs(self):
            """Initialize the retrieval databases."""
            for key, factory in self.retrieval_database_factories.items():
                if key not in self._ret_dbs:
                    self._ret_dbs[key] = factory()
                    self._ret_dbs[key].load()

        def _get_current_version_of_outdated_files(
            self,
            replacements: Sequence[ReplacementText],
            file_content_provider: FileContentProvider,
        ):
            replacements = filter_replacement_text(replacements)
            files_before_replacements = get_files_before_replacement(
                replacements, file_content_provider, expected=True
            )

            reconstructed_files, replacement_error_files = apply_replacements(
                replacements, files_before_replacements, self._safe_logger
            )

            for error in replacement_error_files:
                self._safe_logger.warn(f"Ignoring error {error.error_type} for file: ")
                self._safe_logger.secret_warn(
                    f"{error.file_path=}, {error.file_blob_name=}"
                )

            return reconstructed_files

        def process_edit_event(
            self,
            request: CompletionRequest,
        ) -> list[PromptChunk]:
            edit_event_construction_input = EditEventConstructionInput(
                active_file_path=request.path,
                active_file_prefix=request.prefix,
                active_file_suffix=request.suffix,
                recent_changes=request.recency_info.recent_changes
                if request.recency_info
                else [],
                edit_events=convert_edit_events(
                    request.edit_events if request.edit_events else []
                ),
            )

            research_file_content_provider = ResearchFileContentProvider(
                cache=self.cache,
            )

            # Step 1. Get the current version of all files mentioned in recent changes
            reconstructed_files = self._get_current_version_of_outdated_files(
                edit_event_construction_input.recent_changes,
                research_file_content_provider,
            )

            # This active file may not be correct, but we add it to the reconstructed files in case it is and edit events rely on it.
            # If it is incorrect, the blob name will not match and it will be ignored.
            reconstructed_files.append(edit_event_construction_input.active_file)

            reconstructed_blob_names_to_files = {
                file.blob_name: file for file in reconstructed_files
            }

            # Step 2. Get the current version of all files mentioned in edit events
            squashable_edits = get_squashable_edits(
                edit_events=edit_event_construction_input.edit_events,
                reconstructed_files=reconstructed_blob_names_to_files,
                file_content_provider=research_file_content_provider,
                safe_logger=self._safe_logger,
            )

            # Step 3. Convert edit events to file changes
            recency_changes = squashable_edits.convert_edit_events_to_modified_files(
                safe_logger=self._safe_logger,
                is_source_file=lambda _: True,
                max_total_changed_chars=self._max_total_changed_chars,
                big_event_lines=self._big_event_lines,
            )

            all_diff_hunks = format_file_changes_with_ranges(
                recency_changes,
                diff_context_lines=self._diff_context_lines,
                diff_filter=lambda path: path.suffix != ".ipynb",
                use_smart_header=self._use_smart_header,
            )

            all_edit_prompt_chunks = [
                diff_hunk_to_prompt_chunk(diff_hunk, self._filter_duplicated_file_paths)
                for diff_hunk in all_diff_hunks
            ]

            # Edit events are ordered from oldest to newest, so let's reverse the order to get the most recent changes first
            all_edit_prompt_chunks.reverse()
            edit_prompt_chunks = all_edit_prompt_chunks[: self.num_retrieved_chunks]

            return edit_prompt_chunks

        def process_recency_chunks(
            self,
            request: CompletionRequest,
        ) -> list[PromptChunk]:
            recent_changes = (
                request.recency_info.recent_changes if request.recency_info else []
            )

            # Unsure if we want to limit to [:10] here, but service code in `recency_retriever.py` is, so we will for now.
            recent_changes_chunks = [
                recent_chunk_to_prompt_chunk(recent_change=recent_change)
                for recent_change in recent_changes[:10]
            ]

            recent_changes_chunks = recent_changes_chunks[: self.num_retrieved_chunks]
            return recent_changes_chunks

        def process_single_hindsight_problem(
            self, hindsight_problem: HindsightCompletionDatum
        ) -> dict | None:
            request = hindsight_problem.completion.request
            output: dict[str, Any] = dict(
                request_id=hindsight_problem.completion.request_id,
                prefix=request.prefix,
                prefix_begin=request.position.prefix_begin if request.position else 0,
                suffix=request.suffix,
                ground_truth=hindsight_problem.ground_truth,
                file_path=request.path,
            )

            assert (
                hindsight_problem.completion.inference_response is not None
            ), "Inference response is required"
            output["inference_response_token_ids"] = json.dumps(
                hindsight_problem.completion.inference_response.token_ids
            )

            blob_names = request.blob_names
            blob_contents = self.cache.get(blob_names)

            # Step 1: Construct the list of documents in the workspace.
            documents: list[Document] = []
            for blob_name, blob_content in zip(blob_names, blob_contents):
                # GCS failed to find or download the blob.
                if blob_content is None:
                    self._safe_logger.error(
                        f"Failed to find blob name {blob_name} in request {hindsight_problem.completion.request_id}"
                    )
                    return None

                document = Document.new(
                    text=blob_content.content,
                    path=str(blob_content.path),
                )
                documents.append(document)

            # Validate that all documents have unique paths.
            # This could happen if the user has multiple workspaces open, and it is easier to filter out. This does not impact many samples.
            try:
                num_unique_paths = len(set(doc.path for doc in documents))
                assert (
                    num_unique_paths == len(documents)
                ), f"Expected unique paths, found {num_unique_paths} unique paths out of {len(documents)} documents. request_id={hindsight_problem.completion.request_id}"
            except Exception as e:
                self._safe_logger.error(f"Failed to validate documents: {e}")
                return None

            # Step 2: Index and embed all files for each retrieval database.
            for db in self._ret_dbs.values():
                db.add_docs(documents)

            doc_ids = {doc.id for doc in documents}
            model_input = ModelInput(
                prefix=request.prefix,
                suffix=request.suffix,
                path=request.path,
                doc_ids=doc_ids,
                extra={
                    "ground_truth": hindsight_problem.ground_truth,
                },
            )

            # Step 3: Retrieve chunks from each retrieval database.
            retrieved_chunks: list[PromptChunk] = []
            retrieved_chunk_to_score: dict[PromptChunk, float | None] = {}
            for origin, db in self._ret_dbs.items():
                origin_chunks, scores = db.query(
                    model_input=model_input,
                    top_k=self.num_retrieved_chunks,
                    doc_ids=doc_ids,
                )

                prompt_chunks = [
                    chunk_to_prompt_chunk(chunk, origin) for chunk in origin_chunks
                ]
                retrieved_chunks.extend(prompt_chunks)
                for chunk, score in zip(prompt_chunks, scores):
                    retrieved_chunk_to_score[chunk] = score

            # Step 4: Process edit event
            try:
                edit_chunks = self.process_edit_event(request)
            except Exception as e:
                self._safe_logger.warn(f"Failed to process edit event: {e}")
                edit_chunks = []
            retrieved_chunks.extend(edit_chunks)

            # Step 5: Process recency chunks
            recent_changes_chunks = self.process_recency_chunks(request)
            retrieved_chunks.extend(recent_changes_chunks)

            output["retrieved_chunks"] = serialize_retrieved_prompt_chunks(
                retrieved_chunks
            )
            output["retrieved_scores"] = json.dumps(
                [
                    retrieved_chunk_to_score.get(chunk, None)
                    for chunk in retrieved_chunks
                ]
            )
            output["recency_info"] = (
                request.recency_info.to_json() if request.recency_info else None
            )

            return output

        def __call__(
            self, hindsight_problem: HindsightCompletionDatum
        ) -> Iterator[dict]:
            """Convert each FIM problem in the repo to be retrieval-augmented data samples.

            Args:
                repo: The repository, which is a list of files, to process.
                fim_problems: The FIM problems to augment.
                config: The configuration object.
                tokenizer: The tokenizer to use.
                retrieval_database: The retrieval database to use.

            Returns:
                A generator of processed rows.
            """
            self.__init_retrieval_dbs()
            if self.counter % 100 == 0:
                self._safe_logger.info(f"Processed {self.counter} hindsight problems.")
            result = self.process_single_hindsight_problem(hindsight_problem)
            self.counter += 1
            if result is not None:
                yield result

    return GenerateRetrievedChunksFromHindsightProblem

"""Helpful utilities for filtering logic using spark."""

import typing

from pyspark.sql import dataframe as sparkDF
from pyspark.sql import functions as sparkF
from pyspark.sql.types import IntegerType

from base.languages.unit_test_guesser import is_unit_test


def filter_repos_by_config(
    df_repos: sparkDF.DataFrame,
    config,
    repo_min_size: int | None = None,
    repo_max_size: int | None = None,
    size_column: str = "size",
    path_column: str = "path",
    file_lang_column: str = "langpart",
) -> sparkDF.DataFrame:
    """Filter the repos via a RogueSampleConfig."""

    def _count_filtered_files(file_list: list[dict[str, typing.Any]]) -> int:
        """Count the number of files that pass the filtering."""
        filtered_files = []
        for file in file_list:
            if (
                config.small_filter_char_threshold is not None
                and file[size_column] < config.small_filter_char_threshold
            ):
                continue
            if file[file_lang_column] not in config.sample_languages:
                continue
            if config.only_keep_unit_test_file and not is_unit_test(file[path_column]):
                continue
            filtered_files.append(file)
        return len(filtered_files)

    filter_udf = sparkF.udf(_count_filtered_files, returnType=IntegerType())
    df_repos_filtered = df_repos.withColumn(
        "num_filtered_file_list", filter_udf(sparkF.col("file_list"))
    )

    df_repos_final = df_repos_filtered.filter(
        sparkF.col("num_filtered_file_list") > 0
    ).drop("num_filtered_file_list")
    return filter_by_repo_size(df_repos_final, repo_min_size, repo_max_size)


def filter_by_repo_size(
    df_repos: sparkDF.DataFrame,
    min_size: int | None = None,
    max_size: int | None = None,
) -> sparkDF.DataFrame:
    """Filter the repos by the repo size."""
    # Build the filter condition
    if min_size is not None and max_size is not None:
        condition = (sparkF.col("total_size") >= min_size) & (
            sparkF.col("total_size") <= max_size
        )
    elif min_size is not None:
        condition = sparkF.col("total_size") >= min_size
    elif max_size is not None:
        condition = sparkF.col("total_size") <= max_size
    else:
        condition = None

    # If a condition is specified, apply the filter
    if condition is not None:
        df_repos = df_repos.filter(condition)
    return df_repos

"""Tests for conftest.py."""

import dataclasses
import pytest

from research.core.types import Chunk, Document
from research.data.rag.conftest import (
    FOO_PROBLEMS,
    BAR_PROBLEMS,
    FOO_LINE_OUTPUTS,
    BAR_LINE_OUTPUTS,
    FOO_SIGNATURE_OUTPUTS,
    BAR_SIGNATURE_OUTPUTS,
)
from research.data.rag.constants import CONTENT_COLUMN, PATH_COLUMN
from research.retrieval.chunking_functions import get_chunk_id


def make_single_line_chunk(content: str, path: str):
    parent_doc = Document.new(text=content, path=path)
    chunk_id = get_chunk_id(parent_doc.id, content)
    return Chunk(
        id=chunk_id,
        text=content,
        parent_doc=dataclasses.replace(parent_doc, text=""),
        char_offset=0,
        length=len(content),
        line_offset=0,
        length_in_lines=len(content.splitlines()),
        meta={},
    )


def make_module_signature_chunk(content: str, path: str, function_name: str):
    chunk_text = f"In file: {path}"
    if function_name:
        chunk_text += f"\nFUNCTION: {function_name}"
    parent_doc = Document.new(content, path)
    chunk_id = get_chunk_id(parent_doc.id, chunk_text)
    return Chunk(
        id=chunk_id,
        text=chunk_text,
        parent_doc=dataclasses.replace(parent_doc, text=""),
        char_offset=0,
        length=len(chunk_text),
        line_offset=0,
        length_in_lines=len(content.splitlines()),
        meta={"kind": "signature"},
    )


def make_function_signature_chunk(content: str, path: str):
    chunk_text = f"from: {path}\n{content.rstrip()}"
    parent_doc = Document.new(content, path)
    chunk_id = get_chunk_id(parent_doc.id, chunk_text)
    return Chunk(
        id=chunk_id,
        text=chunk_text,
        parent_doc=dataclasses.replace(parent_doc, text=""),
        char_offset=0,
        length=len(chunk_text),
        line_offset=0,
        length_in_lines=len(content.splitlines()),
        meta={"kind": "signature"},
    )


@pytest.mark.parametrize(
    "file,chunks",
    [
        (FOO_PROBLEMS.file, FOO_LINE_OUTPUTS.file_chunks),
        (BAR_PROBLEMS.file, BAR_LINE_OUTPUTS.file_chunks),
    ],
)
def test_file_single_line_chunk(file, chunks):
    expected = [make_single_line_chunk(file[CONTENT_COLUMN], file[PATH_COLUMN])]
    assert chunks == expected


@pytest.mark.parametrize(
    "problems,problem_chunks",
    [
        (FOO_PROBLEMS.problems, FOO_LINE_OUTPUTS.problem_chunks),
        (BAR_PROBLEMS.problems, BAR_LINE_OUTPUTS.problem_chunks),
    ],
)
def test_problems_single_line_chunk(problems, problem_chunks):
    for problem, chunks in zip(problems, problem_chunks, strict=True):
        expected = [
            make_single_line_chunk(
                problem.prefix.code + problem.suffix.code, problem.file_path
            )
        ]
        assert chunks == expected


@pytest.mark.parametrize(
    "file,chunks,function_name",
    [
        (FOO_PROBLEMS.file, FOO_SIGNATURE_OUTPUTS.file_chunks, "foo"),
        (BAR_PROBLEMS.file, BAR_SIGNATURE_OUTPUTS.file_chunks, "bar"),
    ],
)
def test_file_function_signature_chunk(file, chunks, function_name):
    expected = [
        make_module_signature_chunk(
            file[CONTENT_COLUMN], file[PATH_COLUMN], function_name
        ),
        make_function_signature_chunk(file[CONTENT_COLUMN], file[PATH_COLUMN]),
    ]
    assert chunks == expected


@pytest.mark.parametrize(
    "problems,problem_chunks",
    [
        (FOO_PROBLEMS.problems[:1], FOO_SIGNATURE_OUTPUTS.problem_chunks[:1]),
        # FOO_PROBLEMS.problems[1:2] has multiple signature chunks.
        (FOO_PROBLEMS.problems[2:], FOO_SIGNATURE_OUTPUTS.problem_chunks[2:]),
        (BAR_PROBLEMS.problems, BAR_SIGNATURE_OUTPUTS.problem_chunks),
    ],
)
def test_problems_single_signature_chunk(problems, problem_chunks):
    for problem, chunks in zip(problems, problem_chunks, strict=True):
        expected = [
            make_module_signature_chunk(
                problem.prefix.code + problem.suffix.code, problem.file_path, ""
            )
        ]
        assert chunks == expected


@pytest.mark.parametrize(
    "problems,problem_chunks, function_name",
    [
        (FOO_PROBLEMS.problems[1:2], FOO_SIGNATURE_OUTPUTS.problem_chunks[1:2], "foo"),
    ],
)
def test_problems_function_signature_chunk(problems, problem_chunks, function_name):
    for problem, chunks in zip(problems, problem_chunks, strict=True):
        content = problem.prefix.code + problem.suffix.code
        expected = [
            make_module_signature_chunk(content, problem.file_path, function_name),
            make_function_signature_chunk(content, problem.file_path),
        ]
        assert chunks == expected

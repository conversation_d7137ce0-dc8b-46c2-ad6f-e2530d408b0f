"""Functions for Rogue data processing."""

import pickle
import random
from dataclasses import dataclass
from types import SimpleNamespace
from typing import Any, Optional, Sequence, Tuple

from megatron.tokenizer.tokenizer import (
    DeepSeekCoderBaseTokenizer,
    LLama3BaseTokenizer,
    StarCoderBaseTokenizer,
)

from research.core.model_input import ModelInput
from research.core.prompt_formatters import (
    PromptFormatterRogue,
    PromptFormatterRogueSLCache,
)
from research.data.rag import common
from research.data.rag.retrieval_utils import deserialize_retrieved_chunks
from research.data.spark.pipelines.utils import map_parquet
from research.fim import fim_prompt, fim_sampling
from research.retrieval import utils as rutils
from research.retrieval.retrieval_database import RetrievalDatabase


@dataclass
class RogueSampleConfig:
    """Configuration for sampling the Rogue dataset."""

    input: str
    output: str
    fim_version: str

    # Sampling languages
    repo_languages: Sequence[str]
    sample_languages: Sequence[str]
    retrieval_languages: Sequence[str]

    # Sampling parameters
    limit_repos: int
    repo_min_size: int
    repo_max_size: int
    every_n_lines: int
    max_problems_per_file: int

    # File filtering
    small_filter_char_threshold: Optional[int] = None
    small_downsample_char_threshold: Optional[int] = None
    small_downsampled_probability: Optional[float] = None
    only_keep_unit_test_file: bool = False

    random_seed: int = 74912

    # Retrieval
    num_retrieved_chunks: int = 40
    scorer_config: Optional[dict[str, Any]] = None
    chunker_config: Optional[dict[str, Any]] = None
    query_config: Optional[dict[str, Any]] = None
    document_config: Optional[dict[str, Any]] = None
    multi_retriever_configs: Optional[dict[str, Any]] = None
    multi_retriever_weights: Optional[dict[str, int]] = None

    def __post_init__(self):
        if self.multi_retriever_configs is None and self.scorer_config is None:
            raise ValueError("Must provide either scorer or multi_retriever_configs")


def make_process_partition_fn(
    config: RogueSampleConfig,
    get_node_weight: fim_sampling.GET_NODE_WEIGHT_TYPE | None = None,
) -> map_parquet.PandasFuncType:
    """Returns a function that can be used to process a partition of the dataset."""
    # pylint: disable=unused-import,import-outside-toplevel
    from research.eval.harness.factories import create_retriever  # noqa: F401

    # pylint: enable=unused-import,import-outside-toplevel
    # Creates retrieval config
    if config.multi_retriever_configs is None:
        retriever_config = {
            "scorer": config.scorer_config,
            "chunker": config.chunker_config,
            "query_formatter": config.query_config,
            "document_formatter": config.document_config,
        }
    else:
        retriever_config = {
            "name": "multi_retriever",
            "retrievers": config.multi_retriever_configs,
        }
        if config.multi_retriever_weights is not None:
            retriever_config["database_weights"] = config.multi_retriever_weights

    file_filter = common.FileFilter(
        small_filter_char_threshold=config.small_filter_char_threshold,
        small_downsampled_probability=config.small_downsampled_probability,
        small_downsample_char_threshold=config.small_downsample_char_threshold,
        sample_languages=config.sample_languages,
        only_keep_unit_test_file=False,
    )

    def _create_retriever(config) -> RetrievalDatabase:
        retrieval_database = create_retriever(config)
        assert isinstance(retrieval_database, RetrievalDatabase)
        return retrieval_database

    return map_parquet.chain_processors(
        [
            map_parquet.allow_unused_args()(
                common.create_fim_samples_from_repo_generator(
                    file_filter,
                    max_problems_per_file=config.max_problems_per_file,
                    every_n_lines=config.every_n_lines,
                    random_seed=config.random_seed,
                    get_node_weight=get_node_weight,
                )
            ),
            map_parquet.passthrough_feature()(
                map_parquet.allow_unused_args()(common.remove_fim_with_empty_prefix)
            ),
            common.GenerateRetrievedChunksFromFim(
                retrieval_database_factories={
                    "line": lambda: _create_retriever(retriever_config),
                },
                retrieval_languages=config.retrieval_languages,
                num_retrieved_chunks=config.num_retrieved_chunks,
            ),
        ]
    )


# TODO(michiel) add correctness test
def generate_prompt(
    prefix: str,
    middle_spans: bytes,
    suffix: str,
    suffix_offset: int,
    middle_char_start: int,
    middle_char_end: int,
    file_path: str,
    line_chunks: str,
    tokenizer: DeepSeekCoderBaseTokenizer | StarCoderBaseTokenizer,
    config: SimpleNamespace,
) -> list[int]:
    """Construct a token prompt."""

    seed = (
        config.random_seed
        + int.from_bytes((file_path).encode(), "little")
        + middle_char_start
    )
    random.seed(seed)

    # retrieval dropout
    if config.retrieval_dropout > 0 and random.random() < config.retrieval_dropout:
        max_retrieved_chunk_tokens = 0
    else:
        max_retrieved_chunk_tokens = config.max_retrieved_chunk_tokens

    # TODO(jeff): We may want to use a base prompt formatter.
    prompt_formatter = PromptFormatterRogue(
        max_prefix_tokens=config.max_prefix_tokens,
        max_suffix_tokens=config.max_suffix_tokens,
        max_retrieved_chunk_tokens=max_retrieved_chunk_tokens,
        max_filename_tokens=config.max_filename_tokens,
        preamble=config.preamble,
        # TODO(michiel) Use seperator token and make formatter use at beginning and end
        prepend_path_to_retrieved=config.prepend_path_to_retrieved,
        prefix_after_suffix=config.prefix_after_suffix,
        add_retrieval_after_context=config.add_retrieval_after_context,
        only_truncate_true_prefix=config.only_truncate_true_prefix,
        always_use_suffix_token=config.always_use_suffix_token,
        nearby_prefix_char_len=config.nearby_prefix_char_len,
        prefix_char_offset=config.prefix_char_offset,
    )
    # Make it so we don't load new tokenizer for every row even though we build prompt formatter
    prompt_formatter.tokenizer = tokenizer
    prompt_formatter.max_prompt_tokens = config.max_prompt_tokens

    # Randomly sample prompt formatter settings

    # 50% of the time use standard settings

    # Else
    # 20%, use empty suffix
    # 20%, use no retrievals (independent of empty suffix)

    # 25%, sample max prompt size uniformly over [500, max]
    # 25%, sample size of prefix from [1, max prompt size]
    # 25%, sample size of suffix from [1, max prompt size]

    retrieved_chunks = deserialize_retrieved_chunks(line_chunks)
    model_input = ModelInput(
        prefix=prefix,
        suffix=suffix,
        retrieved_chunks=retrieved_chunks,
        path=file_path,
        cursor_position=len(prefix),
    )

    # TODO(michiel) Add option for sampling different prompt styles
    prompt_tokens, _ = prompt_formatter.prepare_prompt(model_input)

    deserialized_middle_spans: Sequence[
        fim_sampling.SkipOrOutput[fim_sampling.SrcSpan]
    ] = pickle.loads(middle_spans)
    target_tokens = fim_prompt._format_middle(
        middle_spans=deserialized_middle_spans,
        tokenize_span=lambda span: tokenizer.tokenize_safe(span.code),
        skip_id=tokenizer.skip_id,
        pause_id=tokenizer.pause_id,
        fim_stop_id=tokenizer.eod_id,
    )

    target_tokens = target_tokens[: config.max_target_tokens]
    prompt_tokens += target_tokens

    return prompt_tokens


@dataclass
class RogueSLDataPromptFormattingConfig:
    input: str | Sequence[str]
    output: str

    seq_len: int
    num_validation_samples: int
    tokenizer_name: str

    component_order: Sequence[str]
    max_prefix_tokens: int
    max_suffix_tokens: int
    max_retrieved_chunk_tokens: int
    max_filename_tokens: int
    max_prompt_tokens: int
    max_target_tokens: int

    # Data augmentation
    random_seed: int = 74912
    data_augmentation_rate: float = 0.0
    dense_retrieval_dropout_rate: float = 0.0
    max_prompt_token_range: Optional[Tuple[int, int]] = None

    # Stateless caching
    context_quant_token_len: int = 0
    nearby_prefix_token_len: int = 0
    nearby_prefix_token_overlap: int = 0
    nearby_suffix_token_len: int = 0
    nearby_suffix_token_overlap: int = 0
    use_far_prefix_token: bool = False
    use_far_suffix_token: bool = False

    # Misc
    max_scope_path_tokens: int = 0


def generate_prompt_sl(
    prefix: str,
    middle_spans: bytes,
    suffix: str,
    suffix_offset: int,
    middle_char_start: int,
    middle_char_end: int,
    file_path: str,
    line_chunks: str,
    tokenizer: DeepSeekCoderBaseTokenizer
    | StarCoderBaseTokenizer
    | LLama3BaseTokenizer,
    config: RogueSLDataPromptFormattingConfig,
) -> list[int]:
    """Construct a token prompt."""

    seed = (
        config.random_seed
        + int.from_bytes((file_path).encode(), "little")
        + middle_char_start
    )
    random.seed(seed)

    max_retrieved_chunk_tokens = config.max_retrieved_chunk_tokens
    max_prompt_tokens = config.max_prompt_tokens

    # Sample whether to perform data augmentation at all
    if random.random() < config.data_augmentation_rate:
        # Randomly set dense retrieval tokens to 0 so model can operate without.
        if random.random() < config.dense_retrieval_dropout_rate:
            max_retrieved_chunk_tokens = 0

        if config.max_prompt_token_range is not None:
            max_prompt_tokens = random.randint(*config.max_prompt_token_range)

    # TODO (jeff): We may want to use a base prompt formatter.
    prompt_formatter = PromptFormatterRogueSLCache(
        max_prefix_tokens=config.max_prefix_tokens,
        max_suffix_tokens=config.max_suffix_tokens,
        max_retrieved_chunk_tokens=max_retrieved_chunk_tokens,
        max_filename_tokens=config.max_filename_tokens,
        max_scope_path_tokens=config.max_scope_path_tokens,
        component_order=config.component_order,
        context_quant_token_len=config.context_quant_token_len,
        nearby_prefix_token_len=config.nearby_prefix_token_len,
        nearby_prefix_token_overlap=config.nearby_prefix_token_overlap,
        nearby_suffix_token_len=config.nearby_suffix_token_len,
        nearby_suffix_token_overlap=config.nearby_suffix_token_overlap,
        use_far_prefix_token=config.use_far_prefix_token,
        use_far_suffix_token=config.use_far_suffix_token,
    )
    # Make it so we don't load new tokenizer for every row even though we build prompt formatter
    prompt_formatter.tokenizer = tokenizer
    prompt_formatter.max_prompt_tokens = max_prompt_tokens

    # Randomly sample prompt formatter settings
    retrieved_chunks = deserialize_retrieved_chunks(line_chunks)

    model_input = ModelInput(
        prefix=prefix,
        suffix=suffix,
        retrieved_chunks=retrieved_chunks,
        path=file_path,
        cursor_position=len(prefix),
    )

    # TODO(michiel) Add option for sampling different prompt styles
    prompt_tokens, _ = prompt_formatter.prepare_prompt(model_input)

    deserialized_middle_spans: Sequence[
        fim_sampling.SkipOrOutput[fim_sampling.SrcSpan]
    ] = pickle.loads(middle_spans)
    target_tokens = fim_prompt._format_middle(
        middle_spans=deserialized_middle_spans,
        tokenize_span=lambda span: tokenizer.tokenize_safe(span.code),
        skip_id=tokenizer.skip_id,
        pause_id=tokenizer.pause_id,
        fim_stop_id=tokenizer.eod_id,
    )

    target_tokens = target_tokens[: config.max_target_tokens]
    prompt_tokens += target_tokens

    return prompt_tokens

"""Data pipeline to generate training data for simple elden (2024-08-14).

This is a reference implementation of the Elden pipeline.
DO NOT RUN it directly without reading the code, as it takes a long time to finish with many resources.
Feel free to copy and modify it for your own purpose.

NOTE(<PERSON>anyi): the retrieval stage will have 800+/6000 parquet files failed! I haven't figured out the reason.
But I moved those data into "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814-rerunfail", and will investigate later.

The pipeline is consist of 4 stages:
1. Generate FIM samples for normal distribution, and 2 customized unit test distribution.
2. Retrieved line and signature chunks.
3. Mixup different subsets based on a ratio.
4. Generate prompts and export to indexed dataset.

These stages can be run separately or all together. Use `main` function to decide which stage to run.

# Usage

## Generate FIM samples for different distribution, which takes one hour to process 120K repos.
## normal:           119932 repos with 36,294,719 FIM samples
## unittest-default:  54844 repos with  2,618,849 FIM samples
## unittest-literal:  50438 repos with  1,549,784 FIM samples
python research/data/rag/elden/20240814_elden.py --stage fim --category normal
python research/data/rag/elden/20240814_elden.py --stage fim --category unittest-default
python research/data/rag/elden/20240814_elden.py --stage fim --category unittest-literal

## Generate Retrieval-Augmented FIM samples (most expensive stage).
## normal:           29,954,056 samples
## unittest-default:  2,536,305 samples
## unittest-literal:  1,510,174 samples
python research/data/rag/elden/20240814_elden.py --stage retrieval --category normal
python research/data/rag/elden/20240814_elden.py --stage retrieval --category unittest-default
python research/data/rag/elden/20240814_elden.py --stage retrieval --category unittest-literal

## Mixup different subsets and then generate the final indexed dataset.
python research/data/rag/elden/20240814_elden.py --stage mix    # 33,261,631 data in total
python research/data/rag/elden/20240814_elden.py --stage mix2final --formatter elden
python research/data/rag/elden/20240814_elden.py --stage mix2final --formatter simple_elden_v2.0_6k
python research/data/rag/elden/20240814_elden.py --stage mix2final --formatter simple_elden_v2.0
python research/data/rag/elden/20240814_elden.py --stage mix2final --formatter simple_elden_v2.0_12k
python research/data/rag/elden/20240814_elden.py --stage mix2final --formatter simple_elden_16k

`map_parquet.chain_processors` is used to chain multiple data transformation functions in a stage.
You can comment out some of the functions and run a stage to see the intermediate outputs.
"""

import argparse
import dataclasses
import functools
import json
import logging
import os
import pathlib
import pickle
import random
import time
import typing
from dataclasses import dataclass
from typing import Literal, TypedDict

import megatron.data.indexed_dataset as indexed_dataset
from megatron.tokenizer import tokenizer as research_tokenizer
from pyspark.sql import dataframe as sparkDF
from pyspark.sql import functions as sparkF
from pyspark.sql.types import IntegerType

import base.tokenizers as prod_tokenizers
from base.prompt_format.common import PromptChunk
from base.prompt_format_completion.prompt_formatter import PromptInput
from base.prompt_format_completion.simple_elden_prompt_formatter import (
    SimpleEldenPromptFormatter,
    SimpleEldenPromptFormatterConfig,
    TokenApportionment,
)
from base.tokenizers import tokenizer as prod_tokenizer
from research.core.utils_for_log import time_string
from research.data.rag import common, constants, ender
from research.data.rag.retrieval_utils import deserialize_retrieved_chunks
from research.data.spark import k8s_session
from research.data.spark.pipelines.stages.common import export_indexed_dataset_helper
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.pipelines.utils.map_parquet import (
    allow_unused_args,
    passthrough_feature,
)
from research.fim import fim_prompt, fim_sampling
from research.fim.fim_augment import FileAugmenter, ImportShufflingAugmenter
from research.fim.fim_sampling_for_tests import (
    DefaultUnitTestCorruptionNodesPicker,
    LiteralUnitTestCorruptionNodesPicker,
)
from research.retrieval.retrieval_database import RetrievalDatabase


def main(args: argparse.Namespace):
    """The main functionality."""
    if args.category is None and args.stage not in ("mix", "mix2final"):
        raise ValueError("Please specify the category.")
    if args.stage == "fim":
        fim_stage(args.category)
    elif args.stage == "augment":
        augment_stage(args.category)
    elif args.stage == "retrieval":
        retrieval_stage(args.category)
    elif args.stage == "mix":
        mixdata_stage(args.mix_config_name)
    elif args.stage == "mix2final":
        assert args.formatter is not None
        mix2prompt2dataset_stage(args.mix_config_name, args.formatter)
    else:
        raise ValueError(f"Unknown stage {args.stage}")


# Paths for intermediate and final data.
INPUT_URI = "/mnt/efs/spark-data/shared/repo/2024-0814_120k_repartition5"
OUTPUT_ROOT_URI = "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/"
GLOBAL_TASK_INFO_URI = "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/task_info"
# OUTPUT_ROOT_URI = "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/"
# GLOBAL_TASK_INFO_URI = "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/task_info"

SAMPLE_LANGUAGES = constants.SAMPLE_LANGUAGES
RETRIEVAL_LANGUAGES = constants.RETRIEVAL_LANGUAGES


# Uses `TypedDict` to satisfy type checker. However, it complicates the code as `CONFIG`
# could simply be a normal `dict`. I don't find a way to satisfy the type checker
# without using `TypedDict`.
class ConfigType(TypedDict):
    max_problems_per_file: int
    small_downsampled_probability: float
    small_downsample_char_threshold: int
    small_filter_char_threshold: int
    random_seed: int
    num_retrieved_chunks: int


CONFIG = ConfigType(
    max_problems_per_file=25,
    small_downsampled_probability=0.1,
    small_downsample_char_threshold=1500,
    small_filter_char_threshold=500,
    random_seed=74912,
    num_retrieved_chunks=128,
)


def fim_stage(category: str):
    """Generate FIM samples."""
    start_time = time.time()
    # Define the sampler for each distribution.

    file_augmenter = None
    if category.endswith("-import-dropout"):
        file_augmenter = FileAugmenter(import_dropout_prob=1.0)
    fim_category = category.removesuffix("-import-dropout")
    max_problems_per_file = CONFIG["max_problems_per_file"]
    if fim_category == "normal":
        every_n_lines = 100
        file_filter = common.FileFilter(
            small_filter_char_threshold=CONFIG["small_filter_char_threshold"],
            small_downsampled_probability=CONFIG["small_downsampled_probability"],
            small_downsample_char_threshold=CONFIG["small_downsample_char_threshold"],
            sample_languages=SAMPLE_LANGUAGES,
            only_keep_unit_test_file=False,
        )
        sampler = fim_sampling.CSTFimSampler()
        get_node_weight = None
    elif fim_category == "unittest-default":
        every_n_lines = 80
        file_filter = common.FileFilter(
            small_filter_char_threshold=CONFIG["small_filter_char_threshold"],
            small_downsampled_probability=CONFIG["small_downsampled_probability"],
            small_downsample_char_threshold=CONFIG["small_downsample_char_threshold"],
            sample_languages=SAMPLE_LANGUAGES,
            only_keep_unit_test_file=True,
        )
        node_picker = DefaultUnitTestCorruptionNodesPicker(
            no_corruption_expansion_rate=0.2,
            random_corrupt_available_siblings_rate=0.4,
            corrupt_all_available_siblings_rate=0.2,
            possibly_corrupt_ancestor_rate=0.3,
        )
        sampler = fim_sampling.CSTFimSampler(
            pick_whole_node_rate=1.0,
            pick_extra_spaces_when_whole_node=0.1,
            empty_completion_rate=0.01,
            corruption_nodes_picker=node_picker,
        )
        get_node_weight = node_picker.get_node_weight
    elif fim_category == "unittest-literal":
        every_n_lines = 80
        file_filter = common.FileFilter(
            small_filter_char_threshold=CONFIG["small_filter_char_threshold"],
            small_downsampled_probability=CONFIG["small_downsampled_probability"],
            small_downsample_char_threshold=CONFIG["small_downsample_char_threshold"],
            sample_languages=SAMPLE_LANGUAGES,
            only_keep_unit_test_file=True,
        )
        node_picker = LiteralUnitTestCorruptionNodesPicker(
            no_corruption_expansion_rate=0.2,
            random_corrupt_available_siblings_rate=0.5,
            corrupt_all_available_siblings_rate=0.2,
            possibly_corrupt_ancestor_rate=0.0,
            edit_similarity_threshold=0.3,
            max_num_lines_per_node=10,
            max_num_char_per_node=400,
        )
        sampler = fim_sampling.CSTFimSampler(
            pick_whole_node_rate=1.0,
            pick_extra_spaces_when_whole_node=0.1,
            empty_completion_rate=0.01,
            corruption_nodes_picker=node_picker,
        )
        get_node_weight = node_picker.get_node_weight
    else:
        raise ValueError(f"Unknown category {fim_category}")

    # HACK(jeff): override import-dropout only to preserve backwards compatibility.
    if category.endswith("-import-dropout"):
        max_problems_per_file = 5
        every_n_lines = 200

    # A spark that does not need GPU.
    spark = k8s_session(
        name="elden-fim-stage",
        max_workers=128,
        conf={
            "spark.executor.pyspark.memory": "128G",
            "spark.executor.memory": "128G",
            "spark.sql.parquet.columnarReaderBatchSize": "32",
            "spark.executor.cores": "1",
            "spark.task.cpus": "1",
        },
        ephemeral_storage_gb=128,
    )
    spark.sparkContext.setJobDescription("FIM Sampling Stage")
    data = spark.read.parquet(os.path.join(INPUT_URI, "part-*zstd.parquet"))
    print(f"{time_string()} Input {INPUT_URI} has {data.count()} rows.")
    # NOTE: some fields are not json-serializable and we thus use str(...).
    config_to_save = {
        "category": category,
        "sampler": str(dataclasses.asdict(sampler)),
        "file_filter": str(vars(file_filter)),
        "every_n_lines": every_n_lines,
        "config": dict(CONFIG),
    }
    print(f"{time_string()} Config:\n{json.dumps(config_to_save, indent=2)}")

    cur_output_path = os.path.join(OUTPUT_ROOT_URI, f"fim-{category}")
    result = map_parquet.apply_pandas(
        spark,
        map_parquet.chain_processors(
            [
                # Allows unused args to be passed to the row-wise function.
                allow_unused_args(bound=False)(
                    common.RobustFIMSampler(
                        file_filter,
                        max_problems_per_file=max_problems_per_file,
                        every_n_lines=every_n_lines,
                        random_seed=CONFIG["random_seed"],
                        sampler=sampler,
                        file_augmenter=file_augmenter,
                        get_node_weight=get_node_weight,
                    ).__call__
                ),
                common.RepoFilter(max_repo_size_files=3_000).__call__,
                passthrough_feature()(
                    allow_unused_args()(common.remove_fim_with_empty_prefix)
                ),
                passthrough_feature()(
                    allow_unused_args()(common.remove_trouble_keys_from_file_list)
                ),
                passthrough_feature()(
                    allow_unused_args()(common.serialize_fim_problems)
                ),
            ]
        ),
        input_path=INPUT_URI,
        output_path=cur_output_path,
        timeout=1200,  # 20 mins timeout
        batch_size=16,
        task_info_location=os.path.join(GLOBAL_TASK_INFO_URI, f"fim-{category}"),
        ignore_error=True,
        allow_resume=True,
        # profile=True,  # -> this is for debug
        # ignore_error=False,
        # timing_run=True,
    )
    data = spark.read.parquet(os.path.join(cur_output_path, "*zstd.parquet"))
    print(
        f"{time_string()} [FIM: {category}]: generated {data.count()} rows in {cur_output_path}."
    )
    data.printSchema()

    # Count the number of FIM samples.
    def count_fim_samples(value):
        value = common.deserialize_fim_problems(value)
        fim_problems = value["fim_problems"]
        return len(fim_problems)

    count_fim_samples_udf = sparkF.udf(count_fim_samples, IntegerType())
    data_count = data.withColumn(
        "num_fim_problems", count_fim_samples_udf(sparkF.col("fim_problems"))
    ).agg(sparkF.sum("num_fim_problems").alias("sum_column"))
    data_count = data_count.toPandas()
    data_count = data_count["sum_column"].iloc[0]
    print(f"{time_string()} [FIM: {category}]: {data_count} FIM samples.")

    # Stop the spark session.
    spark.stop()
    print(result["status_count"])
    print(result["task_info"]["stderr"][0])
    config_to_save["result"] = dict(status_count=result["status_count"])
    # Save the config into the output folder.
    config_file_path = os.path.join(cur_output_path, "config.json")
    with open(config_file_path, "w") as f:
        f.write(json.dumps(config_to_save, indent=2))
    print(f"{time_string()} Config saved to {config_file_path}.")
    total_time = time.time() - start_time
    print(f"{time_string()} Total time: {total_time / 60:.1f} mins.")


def augment_stage(category: str):
    """Run post-fim data augmentation"""
    start_time = time.time()

    fim_augmenter = None
    if category.endswith("-import-shuffling"):
        fim_augmenter = ImportShufflingAugmenter()
    fim_category = category.removesuffix("-import-shuffling")

    if fim_augmenter is None:
        print("fim_augmenter was None, nothing to do!")
        return

    # A spark that does not need GPU.
    spark = k8s_session(
        name="elden-fim-stage",
        max_workers=128,
        conf={
            "spark.executor.pyspark.memory": "128G",
            "spark.executor.memory": "128G",
            "spark.sql.parquet.columnarReaderBatchSize": "32",
            "spark.executor.cores": "1",
            "spark.task.cpus": "1",
        },
        ephemeral_storage_gb=128,
    )
    spark.sparkContext.setJobDescription("FIM Augmenting Stage")
    input_url = os.path.join(OUTPUT_ROOT_URI, f"fim-{fim_category}")
    output_url = os.path.join(OUTPUT_ROOT_URI, f"fim-{category}")
    data = spark.read.parquet(os.path.join(input_url, "part-*zstd.parquet"))
    print(f"{time_string()} Input {input_url} has {data.count()} rows.")
    # NOTE: some fields are not json-serializable and we thus use str(...).
    config_to_save = {
        "category": category,
        "augmenter": str(dataclasses.asdict(fim_augmenter)),
        "config": dict(CONFIG),
    }
    print(f"{time_string()} Config:\n{json.dumps(config_to_save, indent=2)}")

    result = map_parquet.apply_pandas(
        spark,
        map_parquet.chain_processors(
            [
                # Allows unused args to be passed to the row-wise function.
                passthrough_feature()(
                    allow_unused_args()(common.deserialize_fim_problems)
                ),
                allow_unused_args(bound=False)(
                    common.RobustFimAugmenter(
                        fim_augmenter, random_seed=CONFIG["random_seed"]
                    ).__call__
                ),
                passthrough_feature()(
                    allow_unused_args()(common.remove_fim_with_empty_prefix)
                ),
                passthrough_feature()(
                    allow_unused_args()(common.remove_trouble_keys_from_file_list)
                ),
                passthrough_feature()(
                    allow_unused_args()(common.serialize_fim_problems)
                ),
            ]
        ),
        input_path=input_url,
        output_path=output_url,
        timeout=1200,  # 20 mins timeout
        batch_size=16,
        task_info_location=os.path.join(GLOBAL_TASK_INFO_URI, f"fim-{category}"),
        ignore_error=True,
        allow_resume=True,
        # profile=True,  # -> this is for debug
        # ignore_error=False,
        # timing_run=True,
    )
    data = spark.read.parquet(os.path.join(output_url, "*zstd.parquet"))
    print(
        f"{time_string()} [FIM: {category}]: generated {data.count()} rows in {output_url}."
    )
    data.printSchema()

    # Count the number of FIM samples.
    def count_fim_samples(value):
        value = common.deserialize_fim_problems(value)
        fim_problems = value["fim_problems"]
        return len(fim_problems)

    count_fim_samples_udf = sparkF.udf(count_fim_samples, IntegerType())
    data_count = data.withColumn(
        "num_fim_problems", count_fim_samples_udf(sparkF.col("fim_problems"))
    ).agg(sparkF.sum("num_fim_problems").alias("sum_column"))
    data_count = data_count.toPandas()
    data_count = data_count["sum_column"].iloc[0]
    print(f"{time_string()} [FIM: {category}]: {data_count} FIM samples.")

    # Stop the spark session.
    spark.stop()
    print(result["status_count"])
    print(result["task_info"]["stderr"][0])
    config_to_save["result"] = dict(status_count=result["status_count"])
    # Save the config into the output folder.
    config_file_path = os.path.join(output_url, "config.json")
    with open(config_file_path, "w") as f:
        f.write(json.dumps(config_to_save, indent=2))
    print(f"{time_string()} Config saved to {config_file_path}.")
    total_time = time.time() - start_time
    print(f"{time_string()} Total time: {total_time / 60:.1f} mins.")


# Config for StarEthanol. StarEthanol is used for line chunk retrieval.
SETHANOL_CONFIG = dict(
    chunker={
        "max_lines_per_chunk": 40,
        "name": "line_level",
    },
    document_formatter={
        "add_path": True,
        "add_prefix": False,
        "add_suffix": False,
        "max_tokens": 999,
        "name": "ethanol6_document",
        "tokenizer_name": "StarCoderTokenizer",
    },
    query_formatter={
        "add_path": True,
        "add_suffix": True,
        "max_tokens": 1023,
        "name": "ethanol6_query",
        "prefix_ratio": 0.9,
        "tokenizer_name": "StarCoderTokenizer",
    },
    scorer={
        "name": "dense_scorer_v2_fbwd_neox",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/star_ethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/",
    },
    max_num_chunks_per_file=1_000_000,
)

# Config for Methanol. Methanol is used for signature chunk retrieval.
METHANOL_CONFIG = dict(
    scorer={
        "checkpoint_path": "/mnt/efs/augment/checkpoints/menthol/methanol_0416.4_1250/global_step1250/",
        "name": "dense_scorer_v2_fbwd_neox",
    },
    chunker={
        "name": "signature",
    },
    document_formatter={
        "add_path": False,
        "name": "simple_document",
        "max_tokens": 999,
        "tokenizer_name": "StarCoderTokenizer",
    },
    query_formatter={
        "add_path": True,
        "add_suffix": True,
        "max_lines": -1,
        "max_tokens": 1023,
        "name": "ethanol6_query",
        "tokenizer_name": "StarCoderTokenizer",
    },
    max_num_chunks_per_file=1_000_000,
)


def retrieval_stage(category: str):
    """Generate retrieval-augmented samples."""
    root_url = OUTPUT_ROOT_URI
    start_time = time.time()
    batch = 5
    spark_gpu = k8s_session(
        name=f"elden-retrieval-stage-{category}",
        max_workers=32,
        # max_workers=96,
        conf={
            "spark.executor.pyspark.memory": "256G",
            "spark.executor.memory": "110G",
            "spark.executor.cores": "1",
            "spark.task.cpus": "1",
            "spark.sql.parquet.columnarReaderBatchSize": str(batch),
        },
        gpu_type=["RTX_A5000"],
        # gpu_type=["A100_NVLINK_80GB"],
        # gpu_type=["H100_NVLINK_80GB"],
        ephemeral_storage_gb=110,
    )
    input_url = os.path.join(root_url, f"fim-{category}")
    data = spark_gpu.read.parquet(os.path.join(input_url, "*zstd.parquet"))
    print(f"{time_string()} Input {input_url} has {data.count()} rows.")
    data.printSchema()
    # NOTE: some fields are not json-serializable and we thus use str(...).
    config_to_save = {
        "category": category,
        "line_retrieval_config": SETHANOL_CONFIG,
        "signature_retrieval_config": METHANOL_CONFIG,
        "config": dict(CONFIG),
    }
    print(f"{time_string()} Config:\n{json.dumps(config_to_save, indent=2)}")

    # A helper function to make type checking work.
    def _create_retriever(config) -> RetrievalDatabase:
        from research.eval.harness.factories import create_retriever

        retrieval_database = create_retriever(config)
        assert isinstance(retrieval_database, RetrievalDatabase)
        return retrieval_database

    output_url = os.path.join(root_url, f"retrieval-{category}")
    result = map_parquet.apply_pandas(
        spark_gpu,
        map_parquet.chain_processors(
            [
                passthrough_feature()(
                    allow_unused_args()(common.deserialize_fim_problems)
                ),
                common.only_preserve_keys(["file_list", "fim_problems"]),
                common.GenerateRetrievedChunksFromFim(
                    retrieval_database_factories={
                        "line": lambda: _create_retriever(SETHANOL_CONFIG),
                        "signature": lambda: _create_retriever(METHANOL_CONFIG),
                    },
                    retrieval_languages=list(RETRIEVAL_LANGUAGES),
                    num_retrieved_chunks=CONFIG["num_retrieved_chunks"],
                ),
            ]
        ),
        input_path=input_url,
        output_path=output_url,
        timeout=3600 * 2,  # 2-hours timeout
        batch_size=batch,
        task_info_location=os.path.join(GLOBAL_TASK_INFO_URI, f"retrieval-{category}"),
        ignore_error=False,
        allow_resume=True,
        # profile=True,  # -> this is for debug
        # ignore_error=False,
        # timing_run=True,
    )
    data = spark_gpu.read.parquet(os.path.join(output_url, "*zstd.parquet"))
    print(f"{time_string()} [Retrieval]: {output_url} has {data.count()} rows.")
    data.printSchema()
    spark_gpu.stop()
    print(result["status_count"])
    print(result["task_info"]["stderr"][0])
    config_to_save["result"] = dict(status_count=result["status_count"])
    # Save the config into the output folder.
    config_file_path = os.path.join(output_url, "config.json")
    with open(config_file_path, "w") as f:
        f.write(json.dumps(config_to_save, indent=2))
    print(f"{time_string()} Config saved to {config_file_path}.")
    print(f"{time_string()} Elapsed time: {(time.time() - start_time)/60:.1f} mins.")


Category = Literal[
    "normal",
    "unittest-default",
    "unittest-literal",
    "normal-import-dropout",
    "unittest-default-import-dropout",
    "unittest-literal-import-dropout",
    "normal-import-shuffling",
    "unittest-default-import-shuffling",
    "unittest-literal-import-shuffling",
]


@dataclass
class MixConfig:
    """A configuration for mixing data."""

    limit_samples: dict[Category, int] | None = None
    """Select the given number of samples for each category. Limits may be non-exact,
    since we will use `pyspark.sql.DataFrame.sample` with a computed fraction.
    Categories not in the dict are not selected."""

    fraction_of_max: dict[Category, float] | None = None
    """Take the category with the max samples, and scale each category as a fraction
    of the max."""

    random_seed: int = 94081
    """The random seed."""


MIX_CONFIGS = {
    "mix": MixConfig(
        fraction_of_max={
            "normal": 1.0,
            "unittest-default": 0.06,
            "unittest-literal": 0.06,
        },
    ),
    "import-mix": MixConfig(
        limit_samples={
            "normal-import-dropout": 1_000_000,
            # Actual is already less than the limit, around 450k.
            "normal-import-shuffling": 500_000,
        }
    ),
    "import-mix-v2": MixConfig(
        # Reduce import shuffling ratio, and add `mix` as 50%.
        limit_samples={
            "normal": 2_200_000,
            "unittest-default": 150_000,
            "unittest-literal": 150_000,
            "normal-import-dropout": 2_000_000,
            # Actual is already less than the limit, around 450k.
            "normal-import-shuffling": 500_000,
        }
    ),
}


def mixdata_stage(mix_config_name: str):
    """Mixup different subsets and then generate the indexed dataset."""
    source_url = OUTPUT_ROOT_URI
    start_time = time.time()
    spark = k8s_session(
        name="elden-mixdata",
        max_workers=48,
        conf={
            "spark.executor.pyspark.memory": "512G",
            "spark.executor.memory": "256G",
            "spark.executor.cores": "1",
            "spark.task.cpus": "1",
            "spark.sql.parquet.columnarReaderBatchSize": "20",
        },
        ephemeral_storage_gb=512,
    )

    if mix_config_name not in MIX_CONFIGS:
        raise ValueError(f"{mix_config_name} is not in {list(MIX_CONFIGS)}")

    mix_config = MIX_CONFIGS[mix_config_name]
    if mix_config.limit_samples is not None == mix_config.fraction_of_max is not None:
        raise ValueError(
            "Exactly one of limit_samples and fraction_of_max must be set."
        )

    # Get dataframes and count samples
    if mix_config.limit_samples is not None:
        existing_sets = tuple(mix_config.limit_samples.keys())
    else:
        assert mix_config.fraction_of_max is not None
        existing_sets = tuple(mix_config.fraction_of_max.keys())
    spark.sparkContext.setJobDescription(f"Mix {existing_sets} datasets")
    dataframe_by_set: dict[str, sparkDF.DataFrame] = dict()
    num_samples_by_set: dict[str, int] = dict()
    random_seed = mix_config.random_seed
    for category in existing_sets:
        input_url = os.path.join(source_url, f"retrieval-{category}")
        assert os.path.exists(input_url), f"{input_url} does not exist."
        dataframe_by_set[category] = spark.read.parquet(
            os.path.join(input_url, "*zstd.parquet")
        )
        num_samples_by_set[category] = dataframe_by_set[category].count()
        print(
            f"{time_string()} Input {input_url} has {num_samples_by_set[category]} rows."
        )
    max_num_samples = max(num_samples_by_set.values())

    # Compute fraction to sample of each category
    fraction_by_set = {}
    if mix_config.limit_samples is not None:
        for category, limit in mix_config.limit_samples.items():
            fraction_by_set[category] = min(1.0, limit / num_samples_by_set[category])
    else:
        assert mix_config.fraction_of_max is not None
        for category, fraction in mix_config.fraction_of_max.items():
            fraction_by_set[category] = min(
                1.0, fraction * max_num_samples / num_samples_by_set[category]
            )

    # Print approximate ratio of final dataset
    new_num_samples_by_set = {
        category: int(num_samples_by_set[category] * fraction_by_set[category])
        for category in existing_sets
    }
    total_samples = sum(new_num_samples_by_set.values())
    for category, new_num_samples in new_num_samples_by_set.items():
        ratio = new_num_samples / total_samples * 100
        print(f"{category}: {new_num_samples:9d} / {total_samples:9d} = {ratio:.3g}")

    union_df = None
    for category, fraction in fraction_by_set.items():
        sampled_dataframe = dataframe_by_set[category].sample(
            withReplacement=False, fraction=fraction, seed=random_seed
        )
        if union_df is None:
            union_df = sampled_dataframe
        else:
            union_df = union_df.union(sampled_dataframe)
        print(
            f"{time_string()} Union has {union_df.count()} rows as added {fraction=:g} of {category}."
        )

    # DO NOT USE repartition_and_shuffle, it can not handle TB level data......!
    # union_df = repartition_and_shuffle(random_seed=random_seed, df=union_df)
    mix_output_url = os.path.join(source_url, f"{mix_config_name}-samples")
    assert union_df is not None
    union_df.write.mode("overwrite").parquet(mix_output_url)
    union_df = spark.read.parquet(mix_output_url)
    print(f"{time_string()} Union has {union_df.count()} rows in {mix_output_url}.")
    spark.stop()
    print(f"{time_string()} Time elapsed: {(time.time() - start_time)/60:.1f} mins.")


@dataclass
class SimpleEldenPromptFormattingConfig:
    """The configuration for the simple Elden prompt formatter."""

    max_prefix_tokens: int
    max_suffix_tokens: int
    max_retrieved_chunk_tokens: int
    max_filename_tokens: int
    max_prompt_tokens: int
    max_signature_tokens: int
    max_target_tokens: int

    formatter_version: str

    # FIM Target Format
    max_pause_spans: int | None

    # Dataset
    num_validation_samples: int = 50000

    # Data augmentation
    random_seed: int = 74912
    data_augmentation_rate: float = 0.0
    dense_retrieval_dropout_rate: float = 0.0
    signature_retrieval_dropout_rate: float = 0.0
    max_prompt_token_range: tuple[int, int] | None = None
    max_retrieved_chunk_tokens_range: tuple[int, int] | None = None
    shuffle_retrieved_chunk_rate: float | None = None

    def as_dict(self) -> dict:
        """Return configuration as a dictionary."""
        return dataclasses.asdict(self)


def generate_simple_elden_prompt_fn(
    tokenizer: prod_tokenizer.Tokenizer, config: SimpleEldenPromptFormattingConfig
) -> map_parquet.FlatMapFn:
    """Return a row-wise function to generate Ender prompts."""

    def _generate_ender_promptprefix(
        prefix: str,
        middle_spans: bytes,
        suffix: str,
        file_path: str,
        line_chunks: str,
        signature_chunks: str,
    ) -> dict[str, list[int]]:
        # Construct seed for data augmentation
        seed = (
            config.random_seed
            + int.from_bytes((file_path).encode(), "little")
            + len(prefix)
        )
        random.seed(seed)

        line_chunks_in_prod: list[PromptChunk] = []
        for chunk in deserialize_retrieved_chunks(line_chunks):
            line_chunks_in_prod.append(
                PromptChunk(
                    text=chunk.text,
                    path=chunk.parent_doc.path,
                    unique_id=chunk.id,
                    origin="dense_retriever",
                    char_start=chunk.char_offset,
                    char_end=chunk.char_offset + chunk.length,
                    blob_name=chunk.parent_doc.id,
                    header=chunk.header,
                )
            )
        sig_chunks_in_prod: list[PromptChunk] = []
        for chunk in deserialize_retrieved_chunks(signature_chunks):
            sig_chunks_in_prod.append(
                PromptChunk(
                    text=chunk.text,
                    path=chunk.parent_doc.path,
                    unique_id=chunk.id,
                    origin="signature_retriever",
                    char_start=chunk.char_offset,
                    char_end=chunk.char_offset + chunk.length,
                    blob_name=chunk.parent_doc.id,
                    header=chunk.header,
                )
            )
        # Start the data augmentation
        max_retrieved_chunk_tokens = config.max_retrieved_chunk_tokens
        max_signature_tokens = config.max_signature_tokens
        max_prompt_tokens = config.max_prompt_tokens

        # Sample whether to perform data augmentation at all
        if random.random() < config.data_augmentation_rate:
            # Randomly set signature tokens to 0 so model can operate without.
            if random.random() < config.signature_retrieval_dropout_rate:
                max_signature_tokens = 0
            # Randomly set prompt tokens to a random value.
            if config.max_prompt_token_range is not None:
                max_prompt_tokens = random.randint(*config.max_prompt_token_range)
            # Randomly set dense retrieval tokens to 0 so model can operate without.
            if random.random() < config.dense_retrieval_dropout_rate:
                max_retrieved_chunk_tokens = 0
            elif config.max_retrieved_chunk_tokens_range is not None:
                max_retrieved_chunk_tokens = random.randint(
                    *config.max_retrieved_chunk_tokens_range
                )
            # Randomly set the shuffle the retrieved chunks.
            if (
                config.shuffle_retrieved_chunk_rate is not None
                and random.random() < config.shuffle_retrieved_chunk_rate
            ):
                random.shuffle(line_chunks_in_prod)
                random.shuffle(sig_chunks_in_prod)

        prompt_formatter = SimpleEldenPromptFormatter(
            config=SimpleEldenPromptFormatterConfig(
                max_prompt_length=max_prompt_tokens,
                token_config=TokenApportionment(
                    path_len=config.max_filename_tokens,
                    prefix_len=config.max_prefix_tokens,
                    suffix_len=config.max_suffix_tokens,
                    retrieval_len=max_retrieved_chunk_tokens,
                ),
                per_retriever_max_tokens={
                    "signature_retriever": max_signature_tokens,
                    "dense_retriever": max_retrieved_chunk_tokens,
                },
                version=config.formatter_version,
            ),
            tokenizer=tokenizer,
        )

        prompt_input = PromptInput(
            prefix=prefix,
            suffix=suffix,
            # Contains the full prefix, so prefix_begin should be 0.
            prefix_begin=0,
            path=file_path,
            retrieved_chunks=line_chunks_in_prod + sig_chunks_in_prod,
            lang=None,
        )

        formatter_output = prompt_formatter.format_prompt(prompt_input, -1)

        deserialized_middle_spans: list[
            fim_sampling.SkipOrOutput[fim_sampling.SrcSpan]
        ] = pickle.loads(middle_spans)
        special_tokens = tokenizer.special_tokens
        assert isinstance(
            special_tokens,
            (prod_tokenizer.RagSpecialTokens, research_tokenizer.ResearchSpecialTokens),
        )
        assert special_tokens.skip is not None
        assert special_tokens.pause is not None
        target_tokens = fim_prompt._format_middle(
            middle_spans=deserialized_middle_spans,
            tokenize_span=lambda span: tokenizer.tokenize_safe(span.code),
            skip_id=special_tokens.skip,
            pause_id=special_tokens.pause,
            fim_stop_id=special_tokens.eos,
            max_pause_spans=config.max_pause_spans,
        )

        target_tokens = target_tokens[: config.max_target_tokens]
        prompt_tokens = formatter_output.tokens() + target_tokens

        return {"prompt_tokens": prompt_tokens}

    return _generate_ender_promptprefix


def mix2prompt2dataset_stage(mix_config_name: str, formatter: str):
    """Mixup different subsets and then generate the prompt tokens and target tokens."""
    source_url = OUTPUT_ROOT_URI
    root_output_url = OUTPUT_ROOT_URI

    start_time = time.time()
    spark = k8s_session(
        max_workers=128,
        conf={
            "spark.executor.memory": "128G",
            "spark.executor.cores": "1",
            "spark.task.cpus": "1",
            "spark.executor.pyspark.memory": "256G",
            "spark.sql.parquet.columnarReaderBatchSize": "20",
        },
        ephemeral_storage_gb=128,
    )

    if formatter == "elden":
        prompt_config = ender.EnderDataPromptFormattingConfig(
            random_seed=74912,
            component_order=[
                "prefix",
                "retrieval",
                "signature",
                "nearby_prefix",
                "suffix",
            ],
            max_prefix_tokens=1024,
            max_suffix_tokens=512,
            max_signature_tokens=1024,
            max_retrieved_chunk_tokens=-1,
            max_filename_tokens=50,
            max_header_tokens=0,
            max_target_tokens=256,
            max_prompt_tokens=6142,
            seq_len=7937,
            data_augmentation_rate=0.5,
            dense_retrieval_dropout_rate=0.3,
            signature_retrieval_dropout_rate=0.3,
            # max_prompt_token_range=(3072, 7678), # -> this is a debug that the smallest range is too small
            max_prompt_token_range=(3124, 7678),
            context_quant_token_len=64,
            nearby_prefix_token_len=512,
            nearby_prefix_token_overlap=0,
            nearby_suffix_token_len=0,
            nearby_suffix_token_overlap=0,
            # Validation
            num_validation_samples=50000,
        )
        seq_length = prompt_config.seq_len
        generate_prompt_fn = functools.partial(
            ender.generate_ender_prompt, config=prompt_config
        )
    elif formatter.startswith("simple_elden_v2.0_6k"):
        # max_retrieved_chunk_tokens_range has a similar effect as max_prompt_token_range
        prompt_config = SimpleEldenPromptFormattingConfig(
            random_seed=74912,
            max_prefix_tokens=1024,
            max_suffix_tokens=512,
            max_signature_tokens=1024,
            max_retrieved_chunk_tokens=6048,
            max_filename_tokens=50,
            max_target_tokens=256,
            max_prompt_tokens=6048,
            formatter_version="v2.0",
            max_pause_spans=4,
            data_augmentation_rate=0.5,
            dense_retrieval_dropout_rate=0.3,
            signature_retrieval_dropout_rate=0.3,
            max_prompt_token_range=(3300, 7680),
            max_retrieved_chunk_tokens_range=None,
            shuffle_retrieved_chunk_rate=None,
            # Validation
            num_validation_samples=50000,
        )
        assert prompt_config.max_prompt_token_range is not None
        max_prompt_tokens = max(
            prompt_config.max_prompt_tokens,
            prompt_config.max_prompt_token_range[1],
        )
        seq_length = max_prompt_tokens + prompt_config.max_target_tokens + 1
        generate_prompt_fn = functools.partial(
            generate_simple_elden_prompt_fn, config=prompt_config
        )
    elif formatter.startswith("simple_elden_v2.0_12k"):
        # max_retrieved_chunk_tokens_range has a similar effect as max_prompt_token_range
        prompt_config = SimpleEldenPromptFormattingConfig(
            random_seed=74912,
            max_prefix_tokens=1024,
            max_suffix_tokens=1024,
            max_signature_tokens=3072,
            max_retrieved_chunk_tokens=12288,
            max_filename_tokens=50,
            max_target_tokens=256,
            max_prompt_tokens=12288,
            formatter_version="v2.0",
            max_pause_spans=4,
            data_augmentation_rate=0.5,
            dense_retrieval_dropout_rate=0.3,
            signature_retrieval_dropout_rate=0.3,
            max_prompt_token_range=(5376, 12288),
            max_retrieved_chunk_tokens_range=None,
            shuffle_retrieved_chunk_rate=0.05,
            # Validation
            num_validation_samples=50000,
        )
        seq_length = (
            prompt_config.max_prompt_tokens + prompt_config.max_target_tokens + 1
        )
        generate_prompt_fn = functools.partial(
            generate_simple_elden_prompt_fn, config=prompt_config
        )
    elif formatter.startswith("simple_elden_v"):
        version = formatter.split("_elden_")[1]
        if version == "v1.0":
            max_pause_spans = 3
            shuffle_retrieved_chunk_rate = None
        elif version == "v2.0":
            max_pause_spans = 4
            shuffle_retrieved_chunk_rate = 0.1
        else:
            raise ValueError(f"Unknown version {version}.")
        # max_retrieved_chunk_tokens_range has a similar effect as max_prompt_token_range
        prompt_config = SimpleEldenPromptFormattingConfig(
            random_seed=74912,
            max_prefix_tokens=1024,
            max_suffix_tokens=512,
            max_signature_tokens=1024,
            max_retrieved_chunk_tokens=7680,  # this can be higher
            max_filename_tokens=50,
            max_target_tokens=256,
            max_prompt_tokens=7680,
            formatter_version=version,
            max_pause_spans=max_pause_spans,
            data_augmentation_rate=0.5,
            dense_retrieval_dropout_rate=0.3,
            signature_retrieval_dropout_rate=0.3,
            max_prompt_token_range=(3300, 7680),
            max_retrieved_chunk_tokens_range=None,
            shuffle_retrieved_chunk_rate=shuffle_retrieved_chunk_rate,
            # Validation
            num_validation_samples=50000,
        )
        seq_length = (
            prompt_config.max_prompt_tokens + prompt_config.max_target_tokens + 1
        )
        generate_prompt_fn = functools.partial(
            generate_simple_elden_prompt_fn, config=prompt_config
        )
    elif formatter == "simple_elden_16k":  # -> use 16K tokens
        prompt_config = SimpleEldenPromptFormattingConfig(
            random_seed=74912,
            max_prefix_tokens=2048,
            max_suffix_tokens=2048,
            max_signature_tokens=2048,
            max_retrieved_chunk_tokens=10000,  # this can be higher
            max_filename_tokens=100,
            max_target_tokens=512,
            max_prompt_tokens=16384 - 512,
            formatter_version="v1.0",
            max_pause_spans=5,
            data_augmentation_rate=0.5,
            dense_retrieval_dropout_rate=0.3,
            signature_retrieval_dropout_rate=0.3,
            max_prompt_token_range=(9000, 16384 - 512),
            # Validation
            num_validation_samples=50000,
        )
        seq_length = (
            prompt_config.max_prompt_tokens + prompt_config.max_target_tokens + 1
        )
        generate_prompt_fn = functools.partial(
            generate_simple_elden_prompt_fn, config=prompt_config
        )
    else:
        raise ValueError(f"Unknown formatter {formatter}")

    input_url = os.path.join(source_url, f"{mix_config_name}-samples")
    tokenizers: dict[str, prod_tokenizer.Tokenizer] = dict()
    # tokenizers["dscv2"] = prod_tokenizers.DeepSeekCoderV2Tokenizer()
    # tokenizers["sc2"] = prod_tokenizers.StarCoder2Tokenizer()
    # tokenizers["llama3"] = prod_tokenizers.Llama3BaseTokenizer()
    # tokenizers["sc1"] = prod_tokenizers.TiktokenStarCoderTokenizer()
    tokenizers["qwen25"] = prod_tokenizers.Qwen25CoderTokenizer()

    for name, tokenizer in tokenizers.items():
        cur_out_url = os.path.join(
            root_output_url, f"{mix_config_name}-prompts-{formatter}-{name}"
        )
        if os.path.exists(cur_out_url):
            print(f"{time_string()} {cur_out_url} already exists, skipping.")
        else:
            print(
                f"{time_string()} Generating prompts for {formatter} with {name} into {cur_out_url}"
            )
            spark.sparkContext.setJobDescription(
                f"Creating prompts for {formatter} with tokenizer {name}"
            )
            result = map_parquet.apply_pandas(
                spark,
                map_parquet.chain_processors(
                    [
                        allow_unused_args()(generate_prompt_fn(tokenizer=tokenizer)),
                        common.create_pad_pack_tokens_fn(
                            seq_len=seq_length,
                            tokenizer=tokenizer,
                        ),
                    ]
                ),
                input_path=input_url,
                output_path=cur_out_url,
                task_info_location=os.path.join(
                    GLOBAL_TASK_INFO_URI,
                    f"{mix_config_name}-prompts-{formatter}-{name}",
                ),
                # prompt formatting stage shouldn't fail, otherwise something wrong.
                ignore_error=False,
                timeout=1800,
                batch_size=128,
                # timing_run=True,
            )
            print(result["status_count"])
            print(result["task_info"]["stderr"][0])
        df = spark.read.parquet(
            *map_parquet.list_files(
                spark, cur_out_url, suffix="parquet", include_path=True
            )
        ).select("prompt_tokens")
        total_data = df.count()
        assert total_data > 1_000_000, f"{total_data=} shouldn't be less than 1M."
        print(
            f"{time_string()} [Prompt: {mix_config_name}-prompts {name}]: generated {total_data} rows in {cur_out_url}."
        )
        spark.sparkContext.setJobDescription(f"Creating mixed indexed dataset : {name}")
        output_dataset_url = os.path.join(
            root_output_url, f"{mix_config_name}-dataset-{formatter}-{name}"
        )
        export_indexed_dataset_helper(
            df,
            vocab_size=tokenizer.vocab_size,
            samples_column="prompt_tokens",
            num_validation_samples=50000,
            indexed_dataset_path=pathlib.Path(output_dataset_url),
            filter_by_langs=None,
        )
        dataset = indexed_dataset.MMapIndexedDataset(
            os.path.join(output_dataset_url, "dataset"), skip_warmup=True
        )
        print(
            f"{time_string()} [Dataset: {mix_config_name}-dataset {name}]: generated in {output_dataset_url} with {len(dataset)} samples."
        )
    spark.stop()
    total_time = time.time() - start_time
    print(f"{time_string()} Total time: {total_time / 60:.1f} mins.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--category",
        type=str,
        choices=list(typing.get_args(Category)),
        default=None,
        required=False,
    )
    parser.add_argument(
        "--mix_config_name",
        type=str,
        choices=list(MIX_CONFIGS.keys()),
        default=None,
        required=False,
    )
    parser.add_argument(
        "--stage",
        type=str,
        choices=["fim", "augment", "retrieval", "mix", "mix2final"],
        required=True,
    )
    parser.add_argument(
        "--formatter",
        type=str,
        choices=[
            "elden",
            "simple_elden_v1.0",
            "simple_elden_v2.0",
            "simple_elden_v2.0_6k",
            "simple_elden_v2.0_12k",
            "simple_elden_16k",
        ],
        default=None,
        required=False,
    )
    args = parser.parse_args()
    logging.basicConfig(level=logging.INFO)
    main(args)

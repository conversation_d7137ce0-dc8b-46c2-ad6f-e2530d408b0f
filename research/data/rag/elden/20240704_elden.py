"""Data pipeline to generate training data for Elden (2024-07-03).

This is a reference implementation of the Elden pipeline. Do not run it directly without
reading the code. Feel free to copy and modify it.

The pipeline is consist of 4 stages:
1. Generate FIM samples for normal distribution, and 2 customized unit test distribution.
2. Retrieved line and signature chunks.
3. Generate prompts.
4. Export to indexed dataset.

These stages can be run separately or all together. Use `main` function to decide which
stage to run.

# Usage

## Generate FIM samples for different distribution, which takes 30~50 mins to process 90K repos.
## normal: 82885 repos, unittest-default: 41066 repos, unittest-literal: 37658 repos.
python research/data/rag/elden/20240704_elden.py --stage fim --category normal
python research/data/rag/elden/20240704_elden.py --stage fim --category unittest-default
python research/data/rag/elden/20240704_elden.py --stage fim --category unittest-literal

## Generate Retrieval-Augmented FIM samples.
## normal: 9324412 samples, unittest-default: 1414405 samples, unittest-literal: 852832 samples.
python research/data/rag/elden/20240704_elden.py --stage retrieval --category normal
python research/data/rag/elden/20240704_elden.py --stage retrieval --category unittest-default
python research/data/rag/elden/20240704_elden.py --stage retrieval --category unittest-literal

## Generate Prompts (take about 30 mins to process 9M samples over 3 tokenizers)
## this stage does not have any failure so that the number of samples keeps the same.
python research/data/rag/elden/20240704_elden.py --stage prompt --category normal
python research/data/rag/elden/20240704_elden.py --stage prompt --category unittest-default
python research/data/rag/elden/20240704_elden.py --stage prompt --category unittest-literal

## Generate Indexed Dataset.
python research/data/rag/elden/20240704_elden.py --stage dataset --category normal
python research/data/rag/elden/20240704_elden.py --stage dataset --category unittest-default
python research/data/rag/elden/20240704_elden.py --stage dataset --category unittest-literal

`map_parquet.chain_processors` is used to chain multiple data transformation functions in a stage.
You can comment out some of the functions and run a stage to see the intermediate outputs.
"""

import argparse
import dataclasses
import json
import logging
import os
import pathlib
from typing import TypedDict

import base.tokenizers as prod_tokenizer
from base.tokenizers.tokenizer import Tokenizer
from research.core.utils_for_log import time_string
from research.data.rag import common, constants, ender
from research.data.rag.ender import EnderDataPromptFormattingConfig
from research.data.rag.utils import repartition_and_shuffle
from research.data.spark import k8s_session
from research.data.spark.pipelines.stages.common import export_indexed_dataset_helper
from research.data.spark.pipelines.utils import map_parquet
from research.fim import fim_sampling
from research.fim.fim_augment import FileAugmenter
from research.fim.fim_sampling_for_tests import (
    DefaultUnitTestCorruptionNodesPicker,
    LiteralUnitTestCorruptionNodesPicker,
)
from research.retrieval.retrieval_database import RetrievalDatabase

passthrough_feature = map_parquet.passthrough_feature
allow_unused_args = map_parquet.allow_unused_args


def main(args: argparse.Namespace):
    """Main."""
    if args.stage == "fim":
        fim_stage(args.category)
    elif args.stage == "retrieval":
        retrieval_stage(args.category)
    elif args.stage == "prompt":
        prompt_formatting_stage(args.category)
    elif args.stage == "dataset":
        build_dataset_stage(args.category)
    else:
        raise ValueError(f"Unknown stage {args.stage}")


# Paths for intermediate and final data.
# INPUT_URI = "/mnt/efs/spark-data/shared/repo/2024-0619_90k_repartitioned/"
# OUTPUT_ROOT_URI = "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/"
# GLOBAL_TASK_INFO_URI = "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/task_info"
# NOTE: These are smaller datasets for testing only. Edit the above for real runs.
INPUT_URI = "/mnt/efs/spark-data/user/jeff/repo/2024-0619_90k_repartitioned-900/"
OUTPUT_ROOT_URI = "/mnt/efs/spark-data/user/jeff/elden/0619_90k_0704-900/"
GLOBAL_TASK_INFO_URI = "/mnt/efs/spark-data/user/jeff/elden/0619_90k_0704-900/task_info"


SAMPLE_LANGUAGES = constants.SAMPLE_LANGUAGES
RETRIEVAL_LANGUAGES = constants.RETRIEVAL_LANGUAGES


# Uses `TypedDict` to satisfy type checker. However, it complicates the code as `CONFIG`
# could simply be a normal `dict`. I don't find a way to satisfy the type checker
# without using `TypedDict`.
class ConfigType(TypedDict):
    max_problems_per_file: int
    small_downsampled_probability: float
    small_downsample_char_threshold: int
    small_filter_char_threshold: int
    random_seed: int
    num_retrieved_chunks: int


CONFIG = ConfigType(
    max_problems_per_file=5,
    small_downsampled_probability=0.1,
    small_downsample_char_threshold=1500,
    small_filter_char_threshold=500,
    random_seed=74912,
    num_retrieved_chunks=128,
)


def fim_stage(category: str):
    """Generate FIM samples."""
    # Define the sampler for each distribution.
    file_augmenter = None
    if category == "normal":
        every_n_lines = 200
        file_filter = common.FileFilter(
            small_filter_char_threshold=CONFIG["small_filter_char_threshold"],
            small_downsampled_probability=CONFIG["small_downsampled_probability"],
            small_downsample_char_threshold=CONFIG["small_downsample_char_threshold"],
            sample_languages=SAMPLE_LANGUAGES,
            only_keep_unit_test_file=False,
        )
        sampler = fim_sampling.CSTFimSampler()
        get_node_weight = None
    elif category == "normal-augmented":
        every_n_lines = 200
        file_filter = common.FileFilter(
            small_filter_char_threshold=CONFIG["small_filter_char_threshold"],
            small_downsampled_probability=CONFIG["small_downsampled_probability"],
            small_downsample_char_threshold=CONFIG["small_downsample_char_threshold"],
            sample_languages=SAMPLE_LANGUAGES,
            only_keep_unit_test_file=False,
        )
        sampler = fim_sampling.CSTFimSampler()
        get_node_weight = None
        file_augmenter = FileAugmenter(import_dropout_prob=1.0)
    elif category == "unittest-default":
        every_n_lines = 80
        file_filter = common.FileFilter(
            small_filter_char_threshold=CONFIG["small_filter_char_threshold"],
            small_downsampled_probability=CONFIG["small_downsampled_probability"],
            small_downsample_char_threshold=CONFIG["small_downsample_char_threshold"],
            sample_languages=SAMPLE_LANGUAGES,
            only_keep_unit_test_file=True,
        )
        node_picker = DefaultUnitTestCorruptionNodesPicker(
            no_corruption_expansion_rate=0.2,
            random_corrupt_available_siblings_rate=0.4,
            corrupt_all_available_siblings_rate=0.2,
            possibly_corrupt_ancestor_rate=0.3,
        )
        sampler = fim_sampling.CSTFimSampler(
            pick_whole_node_rate=1.0,
            pick_extra_spaces_when_whole_node=0.1,
            empty_completion_rate=0.01,
            corruption_nodes_picker=node_picker,
        )
        get_node_weight = node_picker.get_node_weight
    elif category == "unittest-literal":
        every_n_lines = 80
        file_filter = common.FileFilter(
            small_filter_char_threshold=CONFIG["small_filter_char_threshold"],
            small_downsampled_probability=CONFIG["small_downsampled_probability"],
            small_downsample_char_threshold=CONFIG["small_downsample_char_threshold"],
            sample_languages=SAMPLE_LANGUAGES,
            only_keep_unit_test_file=True,
        )
        node_picker = LiteralUnitTestCorruptionNodesPicker(
            no_corruption_expansion_rate=0.2,
            random_corrupt_available_siblings_rate=0.5,
            corrupt_all_available_siblings_rate=0.2,
            possibly_corrupt_ancestor_rate=0.0,
            edit_similarity_threshold=0.3,
            max_num_lines_per_node=10,
            max_num_char_per_node=400,
        )
        sampler = fim_sampling.CSTFimSampler(
            pick_whole_node_rate=1.0,
            pick_extra_spaces_when_whole_node=0.1,
            empty_completion_rate=0.01,
            corruption_nodes_picker=node_picker,
        )
        get_node_weight = node_picker.get_node_weight
    else:
        raise ValueError(f"Unknown category {category}")

    # A spark that does not need GPU.
    spark = k8s_session(
        max_workers=512,
        conf={
            "spark.executor.pyspark.memory": "64G",
            "spark.executor.memory": "32G",
            "spark.sql.parquet.columnarReaderBatchSize": "32",
            "spark.task.cpus": "2",
        },
    )
    data = spark.read.parquet(INPUT_URI)
    print(f"{time_string()} Input {INPUT_URI} has {data.count()} rows.")
    # NOTE: some fields are not json-serializable and we thus use str(...).
    config_to_save = {
        "category": category,
        "sampler": str(dataclasses.asdict(sampler)),
        "file_augmenter": (
            str(dataclasses.asdict(file_augmenter)) if file_augmenter else None
        ),
        "file_filter": str(vars(file_filter)),
        "every_n_lines": every_n_lines,
        "config": dict(CONFIG),
    }
    print(f"{time_string()} Config:\n{json.dumps(config_to_save, indent=2)}")

    cur_output_path = os.path.join(OUTPUT_ROOT_URI, f"fim-{category}")
    result = map_parquet.apply_pandas(
        spark,
        map_parquet.chain_processors(
            [
                # Allows unused args to be passed to the row-wise function.
                allow_unused_args()(
                    common.RobustFIMSampler(
                        file_filter,
                        max_problems_per_file=CONFIG["max_problems_per_file"],
                        every_n_lines=every_n_lines,
                        random_seed=CONFIG["random_seed"],
                        file_augmenter=file_augmenter,
                        sampler=sampler,
                        get_node_weight=get_node_weight,
                    ).__call__
                ),
                passthrough_feature()(
                    allow_unused_args()(common.remove_fim_with_empty_prefix)
                ),
                passthrough_feature()(
                    allow_unused_args()(common.remove_trouble_keys_from_file_list)
                ),
                passthrough_feature()(
                    allow_unused_args()(common.serialize_fim_problems)
                ),
            ]
        ),
        input_path=INPUT_URI,
        output_path=cur_output_path,
        timeout=1200,  # 20 mins timeout
        batch_size=16,
        task_info_location=os.path.join(GLOBAL_TASK_INFO_URI, f"fim-{category}"),
        ignore_error=True,
        # profile=True,  # -> this is for debug
        # ignore_error=False,
        # timing_run=True,
    )
    data = spark.read.parquet(os.path.join(cur_output_path, "*zstd.parquet"))
    print(
        f"{time_string()} [FIM: {category}]: generated {data.count()} rows in {cur_output_path}."
    )
    data.printSchema()
    spark.stop()
    print(result["status_count"])
    print(result["task_info"]["stderr"][0])
    config_to_save["result"] = dict(status_count=result["status_count"])
    # Save the config into the output folder.
    config_file_path = os.path.join(cur_output_path, "config.json")
    with open(config_file_path, "w") as f:
        f.write(json.dumps(config_to_save, indent=2))
    print(f"{time_string()} Config saved to {config_file_path}.")


# Config for StarEthanol. StarEthanol is used for line chunk retrieval.
SETHANOL_CONFIG = dict(
    chunker={
        "max_lines_per_chunk": 40,
        "name": "line_level",
    },
    document_formatter={
        "add_path": True,
        "add_prefix": False,
        "add_suffix": False,
        "max_tokens": 999,
        "name": "ethanol6_document",
        "tokenizer_name": "StarCoderTokenizer",
    },
    query_formatter={
        "add_path": True,
        "add_suffix": True,
        "max_tokens": 1023,
        "name": "ethanol6_query",
        "prefix_ratio": 0.9,
        "tokenizer_name": "StarCoderTokenizer",
    },
    scorer={
        "name": "dense_scorer_v2_fbwd_neox",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/star_ethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/",
    },
)

# Config for Methanol. Methanol is used for signature chunk retrieval.
METHANOL_CONFIG = dict(
    scorer={
        "checkpoint_path": "/mnt/efs/augment/checkpoints/menthol/methanol_0416.4_1250/global_step1250/",
        "name": "dense_scorer_v2_fbwd_neox",
    },
    chunker={
        "name": "signature",
    },
    document_formatter={
        "add_path": False,
        "name": "simple_document",
        "max_tokens": 999,
        "tokenizer_name": "StarCoderTokenizer",
    },
    query_formatter={
        "add_path": True,
        "add_suffix": True,
        "max_lines": -1,
        "max_tokens": 1023,
        "name": "ethanol6_query",
        "tokenizer_name": "StarCoderTokenizer",
    },
)


def retrieval_stage(category: str):
    """Generate retrieval-augmented samples."""
    spark_gpu = k8s_session(
        max_workers=128,
        conf={
            "spark.executor.pyspark.memory": "200G",
            "spark.executor.memory": "80G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
        },
        gpu_type="RTX_A5000",
    )
    spark_cpu = k8s_session(
        max_workers=256,
        conf={
            "spark.executor.pyspark.memory": "64G",
            "spark.executor.memory": "32G",
            "spark.sql.parquet.columnarReaderBatchSize": "32",
            "spark.task.cpus": "2",
        },
    )
    input_url = os.path.join(OUTPUT_ROOT_URI, f"fim-{category}")
    data = spark_cpu.read.parquet(os.path.join(input_url, "*zstd.parquet"))
    print(f"{time_string()} Input {input_url} has {data.count()} rows.")
    # NOTE: some fields are not json-serializable and we thus use str(...).
    config_to_save = {
        "category": category,
        "line_retrieval_config": SETHANOL_CONFIG,
        "signature_retrieval_config": METHANOL_CONFIG,
        "config": dict(CONFIG),
    }
    print(f"{time_string()} Config:\n{json.dumps(config_to_save, indent=2)}")

    # A helper function to make type checking work.
    def _create_retriever(config) -> RetrievalDatabase:
        from research.eval.harness.factories import create_retriever

        retrieval_database = create_retriever(config)
        assert isinstance(retrieval_database, RetrievalDatabase)
        return retrieval_database

    output_url = os.path.join(OUTPUT_ROOT_URI, f"retrieval-{category}")
    result = map_parquet.apply_pandas(
        spark_gpu,
        map_parquet.chain_processors(
            [
                passthrough_feature()(
                    allow_unused_args()(common.deserialize_fim_problems)
                ),
                common.GenerateRetrievedChunksFromFim(
                    retrieval_database_factories={
                        "line": lambda: _create_retriever(SETHANOL_CONFIG),
                        "signature": lambda: _create_retriever(METHANOL_CONFIG),
                    },
                    retrieval_languages=list(RETRIEVAL_LANGUAGES),
                    num_retrieved_chunks=CONFIG["num_retrieved_chunks"],
                ),
            ]
        ),
        input_path=input_url,
        output_path=output_url,
        timeout=1800,  # 30 mins timeout
        batch_size=128,
        task_info_location=os.path.join(GLOBAL_TASK_INFO_URI, f"retrieval-{category}"),
        ignore_error=True,
        # profile=True,  # -> this is for debug
        # ignore_error=False,
        # timing_run=True,
    )
    data = spark_cpu.read.parquet(os.path.join(output_url, "*zstd.parquet"))
    print(
        f"{time_string()} [Retrieval: {category}]: generated {data.count()} rows in {output_url}."
    )
    data.printSchema()
    spark_cpu.stop()
    spark_gpu.stop()
    print(result["status_count"])
    print(result["task_info"]["stderr"][0])
    config_to_save["result"] = dict(status_count=result["status_count"])
    # Save the config into the output folder.
    config_file_path = os.path.join(output_url, "config.json")
    with open(config_file_path, "w") as f:
        f.write(json.dumps(config_to_save, indent=2))
    print(f"{time_string()} Config saved to {config_file_path}.")


PF_CONFIG = EnderDataPromptFormattingConfig(
    random_seed=74912,
    #
    component_order=["prefix", "retrieval", "signature", "nearby_prefix", "suffix"],
    max_prefix_tokens=1024,
    max_suffix_tokens=512,
    max_signature_tokens=1024,
    max_retrieved_chunk_tokens=-1,
    max_filename_tokens=50,
    max_header_tokens=0,
    max_target_tokens=256,
    max_prompt_tokens=6142,
    seq_len=7937,
    data_augmentation_rate=0.5,
    dense_retrieval_dropout_rate=0.3,
    signature_retrieval_dropout_rate=0.3,
    # max_prompt_token_range=(3072, 7678), # -> this is a debug that the smallest range is too small
    max_prompt_token_range=(3124, 7678),
    context_quant_token_len=64,
    nearby_prefix_token_len=512,
    nearby_prefix_token_overlap=0,
    nearby_suffix_token_len=0,
    nearby_suffix_token_overlap=0,
    # Validation
    num_validation_samples=50000,
)
tokenizers: dict[str, Tokenizer] = dict()
tokenizers["sc2"] = prod_tokenizer.StarCoder2Tokenizer()
tokenizers["llama3"] = prod_tokenizer.Llama3BaseTokenizer()
tokenizers["dsc"] = prod_tokenizer.DeepSeekCoderBaseTokenizer()
tokenizers["dscv2"] = prod_tokenizer.DeepSeekCoderV2Tokenizer()


def prompt_formatting_stage(category: str):
    """Generate the prompt tokens and target tokens."""
    spark = k8s_session(max_workers=128)

    input_url = os.path.join(OUTPUT_ROOT_URI, f"retrieval-{category}")
    data = spark.read.parquet(os.path.join(input_url, "*zstd.parquet"))
    print(f"{time_string()} Input {input_url} has {data.count()} rows.")

    for name, tokenizer in tokenizers.items():
        output_url = os.path.join(OUTPUT_ROOT_URI, f"prompt-{category}", name)
        result = map_parquet.apply_pandas(
            spark,
            map_parquet.chain_processors(
                [
                    ender.generate_ender_prompt(
                        tokenizer=tokenizer,
                        config=PF_CONFIG,
                    ),
                    common.create_pad_pack_tokens_fn(
                        seq_len=PF_CONFIG.seq_len,
                        tokenizer=tokenizer,
                    ),
                ]
            ),
            input_path=input_url,
            output_path=output_url,
            task_info_location=os.path.join(
                GLOBAL_TASK_INFO_URI, f"prompt-{category}-{name}"
            ),
            ignore_error=True,
            timeout=1600,
            batch_size=128,
        )
        data = spark.read.parquet(os.path.join(output_url, "*zstd.parquet"))
        print(
            f"{time_string()} [Prompt: {category} {name}]: generated {data.count()} rows in {output_url}."
        )
        print(result["status_count"])
        print(result["task_info"]["stderr"][0])
    spark.stop()


def build_dataset_stage(category: str):
    """Export to indexed dataset."""
    spark = k8s_session()
    for name, tokenizer in tokenizers.items():
        input_url = os.path.join(OUTPUT_ROOT_URI, f"prompt-{category}", name)
        PROMPT_COLUMN = "prompt_tokens"
        df = spark.read.parquet(
            *map_parquet.list_files(
                spark, input_url, suffix="parquet", include_path=True
            )
        ).select(PROMPT_COLUMN)
        print(f"{time_string()} Input {input_url} has {df.count()} rows.")
        spark.sparkContext.setJobDescription(f"Shuffling dataset {category} {name}")
        df = repartition_and_shuffle(PF_CONFIG.random_seed, df)

        output_url = os.path.join(OUTPUT_ROOT_URI, f"dataset-{category}", name)
        spark.sparkContext.setJobDescription(
            f"Creating indexed dataset {category} {name}"
        )
        export_indexed_dataset_helper(
            df,
            vocab_size=tokenizer.vocab_size,
            samples_column=PROMPT_COLUMN,
            num_validation_samples=50000,
            indexed_dataset_path=pathlib.Path(output_url),
            filter_by_langs=None,
        )
        print(
            f"{time_string()} [Dataset: {category} {name}]: generated in {output_url}."
        )
    spark.stop()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--category",
        type=str,
        choices=["normal", "normal-augmented", "unittest-default", "unittest-literal"],
        required=True,
    )
    parser.add_argument(
        "--stage",
        type=str,
        choices=["fim", "retrieval", "prompt", "dataset"],
        required=True,
    )
    args = parser.parse_args()
    logging.basicConfig(level=logging.INFO)
    main(args)

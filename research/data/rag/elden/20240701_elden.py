"""Data pipeline to generate training data for Elden.

This is a reference implementation of the Elden pipeline. Do not run it directly without
reading the code. Feel free to copy and modify it.

The pipeline is consist of 3 stages:
1. Generate FIM samples and retrieved line and signature chunks.
2. Generate prompts.
3. Export to indexed dataset.

These stages can be run separately or all together. Use `main` function to decide which
stage to run.

Usage:
python research/data/rag/elden/20240701_elden.py

Debug/Experiment:
To experiment the pipeline, use
```
# Run Spark executor locally on all cores to reduce the overhead of scheduling remote
# workers.
spark = get_session(control_plane="local[*]")

map_parquet.apply_pandas(
    spark,
    ...
    batch_size=10,
    timing_run=True,
)
```

`map_parquet.chain_processors` is used to chain multiple data transformation functions in a stage.
You can comment out some of the functions and run a stage to see the intermediate
outputs.
"""

import pathlib
from typing import TypedDict

from megatron.tokenizer.tokenizer import StarCoder2Tokenizer

from research.data.rag import common, constants, ender
from research.data.rag.ender import EnderDataPromptFormattingConfig
from research.data.rag.utils import repartition_and_shuffle

# pylint: disable=unused-import
from research.data.spark import (
    get_session,  # noqa
    k8s_session,
)

# pylint: enable=unused-import
from research.data.spark.pipelines.stages.common import export_indexed_dataset_helper
from research.data.spark.pipelines.utils import map_parquet
from research.eval.harness.factories import create_retriever
from research.retrieval.retrieval_database import RetrievalDatabase

passthrough_feature = map_parquet.passthrough_feature
allow_unused_args = map_parquet.allow_unused_args


def main():
    """Main."""
    # Please comment/uncomment the following to select which stages to run.
    stage1()
    # stage2()
    # stage3()


# Paths for intermediate and final data.
STAGE1_URI = "/mnt/efs/spark-data/shared/repo/2024-0619_90k_repartitioned/"
STAGE2_URI = "/mnt/efs/spark-data/user/vzhao/elden/0619_90k_0619/02_fim_samples"
STAGE3_URI = "/mnt/efs/spark-data/user/vzhao/elden/0619_90k_0619/03_prompt_sc2_PfRetSigNpfSuf_rdrop030"
OUTPUT_PATH = pathlib.Path(
    "/mnt/efs/augment/data/processed/rag/dataset/elden_0619_90k_0619_sc2_pfretsignpfsuf_rdrop030/"
)


SAMPLE_LANGUAGES = constants.SAMPLE_LANGUAGES
RETRIEVAL_LANGUAGES = constants.RETRIEVAL_LANGUAGES


# Uses `TypedDict` to satisfy type checker. However, it complicates the code as `CONFIG`
# could simply be a normal `dict`. I don't find a way to satisfy the type checker
# without using `TypedDict`.
class ConfigType(TypedDict):
    every_n_lines: int
    max_problems_per_file: int
    small_downsampled_probability: float
    small_downsample_char_threshold: int
    small_filter_char_threshold: int
    random_seed: int
    num_retrieved_chunks: int


CONFIG = ConfigType(
    every_n_lines=200,
    max_problems_per_file=5,
    small_downsampled_probability=0.1,
    small_downsample_char_threshold=1500,
    small_filter_char_threshold=500,
    random_seed=74912,
    num_retrieved_chunks=128,
)

# Config for StarEthanol. StarEthanol is used for line chunk retrieval.
SETHANOL_CONFIG = dict(
    chunker={
        "max_lines_per_chunk": 40,
        "name": "line_level",
    },
    document_formatter={
        "add_path": True,
        "add_prefix": False,
        "add_suffix": False,
        "max_tokens": 999,
        "name": "ethanol6_document",
    },
    query_formatter={
        "add_path": True,
        "add_suffix": True,
        "max_tokens": 1023,
        "name": "ethanol6_query",
        "prefix_ratio": 0.9,
    },
    scorer={
        "name": "generic_neox",
        "checkpoint_path": "star_ethanol/starethanol6_16.1_mean_proj_512_2000",
    },
)

# Config for Methanol. Methanol is used for signature chunk retrieval.
METHANOL_CONFIG = dict(
    scorer={
        "checkpoint_path": "methanol/methanol_0416.4_1250",
        "name": "generic_neox",
    },
    chunker={
        "name": "signature",
    },
    document_formatter={
        "add_path": False,
        "name": "simple_document",
        "tokenizer_name": "StarCoderTokenizer",
    },
    query_formatter={
        "add_path": True,
        "add_suffix": True,
        "max_lines": -1,
        "max_tokens": 1023,
        "name": "ethanol6_query",
        "tokenizer_name": "StarCoderTokenizer",
    },
)


def stage1():
    """Generate FIM samples and retrieved line and signature chunks."""
    file_filter = common.FileFilter(
        small_filter_char_threshold=CONFIG["small_filter_char_threshold"],
        small_downsampled_probability=CONFIG["small_downsampled_probability"],
        small_downsample_char_threshold=CONFIG["small_downsample_char_threshold"],
        sample_languages=SAMPLE_LANGUAGES,
        only_keep_unit_test_file=False,
    )

    spark = k8s_session(
        max_workers=64,
        conf={
            "spark.executor.pyspark.memory": "50G",
            "spark.executor.memory": "30G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
        },
        gpu_type="RTX_A5000",
    )

    # A helper function to make type checking work.
    def _create_retriever(config) -> RetrievalDatabase:
        retrieval_database = create_retriever(config)
        assert isinstance(retrieval_database, RetrievalDatabase)
        return retrieval_database

    # Uncomment this to run on local.
    # spark = get_session()

    result = map_parquet.apply_pandas(
        spark,
        map_parquet.chain_processors(
            [
                # Allows unused args to be passed to the row-wise function.
                allow_unused_args()(
                    common.RobustFIMSampler(
                        file_filter,
                        max_problems_per_file=CONFIG["max_problems_per_file"],
                        every_n_lines=CONFIG["every_n_lines"],
                        random_seed=CONFIG["random_seed"],
                    ).__call__
                ),
                passthrough_feature()(
                    allow_unused_args()(common.remove_fim_with_empty_prefix)
                ),
                common.GenerateRetrievedChunksFromFim(
                    retrieval_database_factories={
                        "line": lambda: _create_retriever(SETHANOL_CONFIG),
                        "signature": lambda: _create_retriever(METHANOL_CONFIG),
                    },
                    retrieval_languages=list(RETRIEVAL_LANGUAGES),
                    num_retrieved_chunks=CONFIG["num_retrieved_chunks"],
                ),
            ]
        ),
        input_path=STAGE1_URI,
        output_path=STAGE2_URI,
        timeout=3600,  # one hour timeout
        batch_size=8,
        # profile=True,
        # ignore_error=True,
        # debug run
        timing_run=True,
    )
    spark.stop()
    print(result["status_count"])
    print(result["task_info"]["stderr"][0])


PF_CONFIG = EnderDataPromptFormattingConfig(
    random_seed=74912,
    #
    component_order=["prefix", "retrieval", "signature", "nearby_prefix", "suffix"],
    max_prefix_tokens=1024,
    max_suffix_tokens=512,
    max_signature_tokens=1024,
    max_retrieved_chunk_tokens=-1,
    max_filename_tokens=50,
    max_header_tokens=0,
    max_target_tokens=256,
    # max_prompt_tokens=3838,
    max_prompt_tokens=6142,
    data_augmentation_rate=0.5,
    dense_retrieval_dropout_rate=0.3,
    signature_retrieval_dropout_rate=0.3,
    max_prompt_token_range=(3072, 7678),
    # max_prompt_token_range=None,
    context_quant_token_len=64,
    # context_quant_token_len=0,
    nearby_prefix_token_len=512,
    # nearby_prefix_token_len=0,
    nearby_prefix_token_overlap=0,
    nearby_suffix_token_len=0,
    nearby_suffix_token_overlap=0,
    # Validation
    num_validation_samples=50000,
    # seq_len=4097,
    seq_len=7937,
)
TOKENIZER = StarCoder2Tokenizer()


def stage2():
    """Generate prompts."""
    spark = k8s_session(max_workers=256)
    # spark = get_session(control_plane="local[*]")

    result = map_parquet.apply_pandas(
        spark,
        map_parquet.chain_processors(
            [
                ender.generate_ender_prompt(
                    tokenizer=TOKENIZER,
                    config=PF_CONFIG,
                ),
                common.create_pad_pack_tokens_fn(
                    seq_len=PF_CONFIG.seq_len,
                    tokenizer=TOKENIZER,
                ),
            ]
        ),
        input_path=STAGE2_URI,
        output_path=STAGE3_URI,
        ignore_error=True,
        timeout=7200,
        # Debug
        # batch_size=10,
        # timing_run=True,
    )

    spark.stop()

    print(result["status_count"])
    print(result["task_info"]["stderr"][0])


def stage3():
    """Export to indexed dataset."""
    spark = k8s_session()
    # spark = get_session(control_plane="local[*]")
    PROMPT_COLUMN = "prompt_tokens"
    df = spark.read.parquet(
        *map_parquet.list_files(spark, STAGE3_URI, suffix="parquet", include_path=True)
    ).select(PROMPT_COLUMN)
    spark.sparkContext.setJobDescription("Shuffling dataset")
    df = repartition_and_shuffle(PF_CONFIG.random_seed, df)

    spark.sparkContext.setJobDescription("Creating indexed dataset")
    export_indexed_dataset_helper(
        df,
        vocab_size=TOKENIZER.vocab_size,
        samples_column=PROMPT_COLUMN,
        num_validation_samples=50000,
        indexed_dataset_path=OUTPUT_PATH,
        filter_by_langs=None,
    )
    spark.stop()


if __name__ == "__main__":
    main()

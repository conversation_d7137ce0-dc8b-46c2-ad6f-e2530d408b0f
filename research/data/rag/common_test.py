"""Unit tests for common.py.

pytest research/data/rag/common_test.py
"""

from typing import Any

import pytest

from research.data.rag.common import GenerateRetrievedChunksFromFim
from research.data.rag.conftest import (
    FOOBAR_ENDER_RETRIEVAL,
    FOOBAR_REPO,
    FOOBAR_ROGUE_RETRIEVAL,
    LONG_ENDER_RETRIEVAL,
    LONG_REPO,
    LONG_ROGUE_RETRIEVAL,
    RepoProblems,
)
from research.data.rag.retrieval_utils import deserialize_retrieved_chunks


@pytest.mark.parametrize(
    "repo,expected_outputs",
    [
        (FOOBAR_REPO, FOOBAR_ROGUE_RETRIEVAL),
        (LONG_REPO, LONG_ROGUE_RETRIEVAL),
    ],
)
def test_generate_retrieved_chunks_rogue(
    repo: RepoProblems,
    expected_outputs: list[dict[str, Any]],
    generate_retrieved_chunks_rogue: GenerateRetrievedChunksFromFim,
):
    gen = generate_retrieved_chunks_rogue
    outputs = list(gen(repo.file_list, repo.problems))

    assert len(outputs) == len(repo.problems)
    # Do an initial check on the deserialized versions for better printing
    for output, expected in zip(outputs, expected_outputs):
        actual_line_chunks = deserialize_retrieved_chunks(output["line_chunks"])
        expected_line_chunks = deserialize_retrieved_chunks(expected["line_chunks"])
        assert actual_line_chunks == expected_line_chunks

    assert outputs == expected_outputs


@pytest.mark.parametrize(
    "repo,expected_outputs",
    [
        (FOOBAR_REPO, FOOBAR_ENDER_RETRIEVAL),
        (LONG_REPO, LONG_ENDER_RETRIEVAL),
    ],
)
def test_generate_retrieved_chunks_ender(
    repo: RepoProblems,
    expected_outputs: list[dict[str, Any]],
    generate_retrieved_chunks_ender: GenerateRetrievedChunksFromFim,
):
    gen = generate_retrieved_chunks_ender
    outputs = list(gen(repo.file_list, repo.problems))

    assert len(outputs) == len(repo.problems)
    # Do an initial check on the deserialized versions for better printing
    for output, expected in zip(outputs, expected_outputs):
        actual_line_chunks = deserialize_retrieved_chunks(output["line_chunks"])
        expected_line_chunks = deserialize_retrieved_chunks(expected["line_chunks"])
        assert actual_line_chunks == expected_line_chunks
        actual_sig_chunks = deserialize_retrieved_chunks(output["signature_chunks"])
        expected_sig_chunks = deserialize_retrieved_chunks(expected["signature_chunks"])
        assert actual_sig_chunks == expected_sig_chunks

    assert outputs == expected_outputs

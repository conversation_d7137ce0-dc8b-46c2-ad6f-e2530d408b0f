"""Library of global constants."""

PATH_COLUMN = "max_stars_repo_path"
ID_COLUMN = "hexsha"
CONTENT_COLUMN = "content"
SIZE_COLUMN = "size"
FILE_LANG_COLUMN = "langpart"

# Samples Repo whose main language is in this set.
REPO_LANGUAGES = {
    "c",
    "c++",
    "go",
    "java",
    "javascript",
    "python",
    "rust",
    "typescript",
    "c-sharp",
    "ruby",
    "php",
    "tsx",
    "jsx",
    "css",
    "shell",
    "scala",
    "lua",
    "kotlin",
}

# Samples FIM problems from files of these languages.
SAMPLE_LANGUAGES = REPO_LANGUAGES | {
    "sql",
    "markdown",
}

# Retrieves chunks from files of these languages.
RETRIEVAL_LANGUAGES = SAMPLE_LANGUAGES | {
    "cuda",
    "svelte",
    "protocol-buffer",
    "dart",
    "html",
    "makefile",
    "dockerfile",
    "text",
    "yaml",
    "json",
    "xml",
    "jsonnet",
}

# These keys are identified to cause schema trouble, as sometimes they are null
# and thus one shard may interrupt it as null but other shards not, and thus can
# not be merged together.
TROUBLE_KEYS = {
    "max_forks_count",
    "max_forks_repo_forks_event_max_datetime",
    "max_forks_repo_forks_event_min_datetime",
    "max_issues_count",
    "max_stars_count",
    "max_stars_repo_licenses",
    "max_stars_repo_stars_event_min_datetime",
    "max_stars_repo_stars_event_max_datetime",
    "max_issues_repo_issues_event_max_datetime",
    "max_issues_repo_issues_event_min_datetime",
}

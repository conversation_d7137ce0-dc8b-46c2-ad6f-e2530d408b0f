# RAG Data Pipeline

This folder contains all the necessary components and scripts to run the RAG data pipeline.

For all the RAG data pipeline, we usually will reuse the shared repositories located at `/mnt/efs/spark-data/shared/repo`.
The shared repositories are generated by this script `research/data/rag/run_sample_repo.py`. In rare cases,
we will need to regenerate the shared repositories. Otherwise, please reuse the shared repositories to increase the reproducibility.

## Shared Functionality

- We define the shared variables in `research/data/rag/constants.py`, such as repo languages, sample languages, and retrieval languages.
- We define the shared functions in `research/data/rag/common.py`.

## Elden

The reference script to run the Elden pipeline is `research/data/rag/elden`, and we use the date as the file name to track the version and easily find the latest version.

"""Collect the failed spark tasks (the missing parquet files between a input folder and a output folder).

Usage:
python research/data/rag/run_collect_fail_files.py \
    --raw_input /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/fim-normal/ \
    --raw_output /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/retrieval-normal \
    --output_dir /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/fim-normal-rerun

"""

import argparse
import os
import pathlib

import tqdm


def main(args):
    input_dir = pathlib.Path(args.raw_input)
    output_dir = pathlib.Path(args.raw_output)
    assert input_dir.exists()
    assert output_dir.exists()
    input_parquet_files = [x.name for x in input_dir.glob("*.zstd.parquet")]
    output_parquet_files = [x.name for x in output_dir.glob("*.zstd.parquet")]
    print(f"There are {len(input_parquet_files)} input parquet files in total.")
    print(f"There are {len(output_parquet_files)} output parquet files in total.")
    missing_files = set(input_parquet_files) - set(output_parquet_files)
    print(f"There are {len(missing_files)} missing parquet files in total.")
    files_in_output_not_in_input = set(output_parquet_files) - set(input_parquet_files)
    assert not len(files_in_output_not_in_input)
    missing_destination = pathlib.Path(args.output_dir)
    print(f"Moving {len(missing_files)} missing parquet files to {missing_destination}")
    missing_destination.mkdir(exist_ok=True, parents=False)
    assert len(list(missing_destination.glob("*.zstd.parquet"))) == 0
    # Copy the files into the destination
    for file in tqdm.tqdm(missing_files, "Copying files"):
        input_file = input_dir / file
        output_file = missing_destination / file
        os.system(f"cp {input_file} {output_file}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--raw_input",
        type=str,
        required=True,
        help="Input path of your original spark job.",
    )
    parser.add_argument(
        "--raw_output",
        type=str,
        required=True,
        help="Output path of your original spark job.",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help="Output directory for failed files.",
    )
    args = parser.parse_args()
    main(args)

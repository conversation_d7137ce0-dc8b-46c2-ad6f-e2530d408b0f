"""Contains retrieval-related utility functions for data processing."""

import json
from typing import Any, Sequence

from base.prompt_format_completion import Prompt<PERSON>hunk
from research.retrieval.types import Chunk, Document


def serialize_retrieved_chunks(
    retrieved_chunks: Sequence[Chunk], add_parent_doc_text: bool = False
) -> str:
    """Convert retrieved chunks to string for use in dataframe."""

    def to_dict(chunk: Chunk) -> dict[str, Any]:
        chunk_dict = {
            "id": chunk.id,
            "text": chunk.text,
            "parent_doc": {
                "id": chunk.parent_doc.id,
                "path": chunk.parent_doc.path,
                # WARNING: just storing empty string, we don't want to store file
                "text": chunk.parent_doc.text if add_parent_doc_text else "",
                # Not supporting meta field
            },
            "char_offset": chunk.char_offset,
            "length": chunk.length,
            "line_offset": chunk.line_offset,
            "length_in_lines": chunk.length_in_lines,
            "header": chunk.header,
            "meta": {},
            # Not supporting meta field
        }
        if chunk.meta is not None and "line_annotations" in chunk.meta:
            chunk_dict["meta"] = {"line_annotations": chunk.meta["line_annotations"]}
        return chunk_dict

    return json.dumps([to_dict(chunk) for chunk in retrieved_chunks])


def serialize_retrieved_prompt_chunks(retrieved_chunks: list[PromptChunk]) -> str:
    """Convert retrieved chunks to string for use in dataframe."""
    return PromptChunk.schema().dumps(retrieved_chunks, many=True)


def convert_line_annotations(line_annotations: dict[int, dict[str, Any]]) -> dict:
    """Convert line annotations from deserialized form to original form."""

    converted_line_annotations = {}
    for line_idx, line_annotation in line_annotations.items():
        converted_line_annotations[int(line_idx)] = {
            "scope_path": tuple(line_annotation["scope_path"]),
            "line_number_in_scope": line_annotation["line_number_in_scope"],
            "line_number_in_file": line_annotation["line_number_in_file"],
        }

    return converted_line_annotations


def deserialize_retrieved_chunks(retrieved_chunks: str) -> list[Chunk]:
    """Constructs retrieved chunks from string."""

    def to_chunk(dict_: dict[str, Any]) -> Chunk:
        chunk = Chunk(
            id=dict_["id"],
            text=dict_["text"],
            parent_doc=Document(
                id=dict_["parent_doc"]["id"],
                text=dict_["parent_doc"]["text"],
                path=dict_["parent_doc"]["path"],
            ),
            char_offset=dict_["char_offset"],
            length=dict_["length"],
            line_offset=dict_["line_offset"],
            length_in_lines=dict_["length_in_lines"],
            header=dict_.get("header", ""),
        )
        if "meta" in dict_ and "line_annotations" in dict_["meta"]:
            chunk.meta = {
                "line_annotations": convert_line_annotations(
                    dict_["meta"]["line_annotations"]
                )
            }
        return chunk

    dicts = json.loads(retrieved_chunks)
    return [to_chunk(dict_) for dict_ in dicts]


def deserialize_retrieved_prompt_chunks(retrieved_chunks: str) -> list[PromptChunk]:
    """Deserialize prompt chunks from string."""
    return PromptChunk.schema().loads(retrieved_chunks, many=True)

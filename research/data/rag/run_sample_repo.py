"""Data pipeline to sample repos from the stack dataset.

Usage:
python research/data/rag/run_sample_repo.py --limit_repos=90000 --repo_min_size=500000 --repo_max_size=100000000

"""

import json
import os
import pathlib
from datetime import datetime
from types import SimpleNamespace

from absl import app, flags
from pyspark.sql import functions as F

from research.data.rag import constants
from research.data.rag.spark_utils import filter_by_repo_size
from research.data.spark import k8s_session

_INPUT = flags.DEFINE_string(
    "input", "s3a://the-stack-processed/by-repo-3", "Input path to the stack dataset."
)

_OUTPUT_DIR = flags.DEFINE_string(
    "output_dir",
    "/mnt/efs/spark-data/shared/repo",
    "Output directory for the sampled repos.",
)

_LIMIT_REPOS = flags.DEFINE_integer(
    "limit_repos", None, "Limit number of repos.", required=True
)

_REPO_MIN_SIZE = flags.DEFINE_integer("repo_min_size", 500_000, "Minimum repo size.")

_REPO_MAX_SIZE = flags.DEFINE_integer(
    "repo_max_size", 100_000_000, "Maximum repo size."
)
_RANDOM_SEED = flags.DEFINE_integer("random_seed", 74912, "Random seed.")


REPO_LANG_COLUMN = "max_size_lang"

REPO_LANG_SUBCOL = "langpart"


def main(unused_argv):
    formatted_time = datetime.now().strftime("%Y-%m%d")
    output_uri = os.path.join(
        _OUTPUT_DIR.value, f"{formatted_time}_{_LIMIT_REPOS.value/1000:.0f}k"
    )
    config = SimpleNamespace(
        **{
            "input": _INPUT.value,
            "repo_languages": list(constants.REPO_LANGUAGES),
            "limit_repos": _LIMIT_REPOS.value,
            "repo_min_size": _REPO_MIN_SIZE.value,
            "repo_max_size": _REPO_MAX_SIZE.value,
            "random_seed": _RANDOM_SEED.value,
        }
    )
    # Convert to config_str so that we can know if it is json-serializable.
    config_str = json.dumps(vars(config), indent=2)
    print(f"Config:\n{config_str}")

    spark = k8s_session(
        max_workers=128,
        conf={
            "spark.executor.pyspark.memory": "128G",
            "spark.executor.memory": "128G",
            "spark.sql.parquet.columnarReaderBatchSize": "32",
            "spark.task.cpus": "2",
        },
    )
    print("Processing retrieval samples")
    df = spark.read.parquet(config.input)

    # Report statistics on repo languages
    top_languages = (
        df.groupBy(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL])
        .count()
        .orderBy(F.desc("count"))
        .limit(100)
    )

    # Show the result
    top_languages.show(100)

    # Filter for language of main repo being language we want to train on
    if hasattr(config, "repo_languages"):
        config.languages = [lang.lower() for lang in config.repo_languages]
        df = df.filter(
            df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL].isin(config.repo_languages)
        )

    df = filter_by_repo_size(
        df,
        min_size=getattr(config, "repo_min_size", None),
        max_size=getattr(config, "repo_max_size", None),
    )

    total = df.count()
    print(f"Processing {total} repos", flush=True)

    limit_repos = config.limit_repos
    # Spark seems not have a sample function with `n`, and thus we pre-compute the
    # fraction and then do the limit.
    fraction = float(limit_repos) / total
    fraction = min(fraction + 0.1, 1.0)
    df = df.sample(
        withReplacement=False, fraction=fraction, seed=config.random_seed
    ).limit(limit_repos)
    df = df.limit(limit_repos)

    num_partitions = max(limit_repos // 20, 20)
    print(f"{limit_repos=} {num_partitions=}")
    df = df.repartition(num_partitions)
    df.write.parquet(output_uri, mode="overwrite")
    df = spark.read.parquet(output_uri)
    print(f"Randomly sampled {df.count()}/{total} repos", flush=True)
    spark.stop()
    with (pathlib.Path(output_uri) / "config.json").open("w") as f:
        f.write(config_str)


if __name__ == "__main__":
    app.run(main)

from types import SimpleNamespace

import pytest
from megatron.tokenizer.tokenizer import StarCoderTokenizer

from research.data.rag.conftest import (
    FOOBAR_ROGUE_PROMPTS,
    FOOBAR_ROGUE_RETRIEVAL,
    FOOBAR_ROGUESL_PROMPTS,
    LONG_ROGUE_PROMPTS,
    LONG_ROGUE_RETRIEVAL,
    LONG_ROGUESL_PROMPTS,
    ROGUE_CONFIG_DICT,
    ROGUE_SL_CONFIG,
)
from research.data.rag.rogue import (
    generate_prompt,
    generate_prompt_sl,
)


@pytest.mark.parametrize(
    "retrieval_outputs,expected_prompt_tokens",
    [
        (FOOBAR_ROGUE_RETRIEVAL, FOOBAR_ROGUE_PROMPTS),
        (LONG_ROGUE_RETRIEVAL, LONG_ROGUE_PROMPTS),
    ],
)
def test_generate_prompt_foobar(retrieval_outputs, expected_prompt_tokens):
    tokenizer = StarCoderTokenizer()
    config = SimpleNamespace(**ROGUE_CONFIG_DICT)
    for retrieval_output, expected in zip(
        retrieval_outputs, expected_prompt_tokens, strict=True
    ):
        prompt_tokens = generate_prompt(
            **retrieval_output,
            tokenizer=tokenizer,
            config=config,
        )
        assert tokenizer.detokenize(prompt_tokens) == tokenizer.detokenize(expected)
        assert prompt_tokens == expected


@pytest.mark.parametrize(
    "retrieval_outputs,expected_prompt_tokens",
    [
        (FOOBAR_ROGUE_RETRIEVAL, FOOBAR_ROGUESL_PROMPTS),
        (LONG_ROGUE_RETRIEVAL, LONG_ROGUESL_PROMPTS),
    ],
)
def test_generate_prompt_sl_foobar(retrieval_outputs, expected_prompt_tokens):
    tokenizer = StarCoderTokenizer()
    config = ROGUE_SL_CONFIG
    for retrieval_output, expected in zip(
        retrieval_outputs, expected_prompt_tokens, strict=True
    ):
        prompt_tokens = generate_prompt_sl(
            **retrieval_output,
            tokenizer=tokenizer,
            config=config,
        )
        print(prompt_tokens, expected)

        assert tokenizer.detokenize(prompt_tokens) == tokenizer.detokenize(expected)
        assert prompt_tokens == expected

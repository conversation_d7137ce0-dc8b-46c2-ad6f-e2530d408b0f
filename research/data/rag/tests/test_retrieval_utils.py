"""Tests for retrieval_utils."""

import copy

from research.data.rag.retrieval_utils import (
    deserialize_retrieved_chunks,
    serialize_retrieved_chunks,
)
from research.retrieval.types import Chunk, Document


class TestChunkSerialization:
    """Tests for serialization and deserialization of retrieved chunks in pipeline."""

    def test_round_trip(self):
        example_chunk = Chunk(
            id="foo",
            text="hello world",
            parent_doc=Document(id="foo", path="foo.py", text="hello world"),
            char_offset=3,
            length=11,
            line_offset=2,
            length_in_lines=1,
            meta=None,
        )
        expected_chunk = copy.deepcopy(example_chunk)
        expected_chunk.parent_doc.text = ""
        serialized_chunk = serialize_retrieved_chunks([example_chunk])
        deserialized_chunks = deserialize_retrieved_chunks(serialized_chunk)
        assert deserialized_chunks == [expected_chunk]

    def test_round_trip_with_line_annotations(self):
        line_annotations = {
            3: {
                "scope_path": ("scope1", "scope2"),
                "line_number_in_scope": 1,
                "line_number_in_file": 3,
            },
        }
        example_chunk = Chunk(
            id="foo",
            text="hello world",
            parent_doc=Document(id="foo", path="foo.py", text="hello world"),
            char_offset=3,
            length=11,
            line_offset=2,
            length_in_lines=1,
            meta={"line_annotations": line_annotations},
        )
        expected_chunk = copy.deepcopy(example_chunk)
        expected_chunk.parent_doc.text = ""
        serialized_chunk = serialize_retrieved_chunks([example_chunk])
        deserialized_chunks = deserialize_retrieved_chunks(serialized_chunk)
        assert deserialized_chunks == [expected_chunk]

"""Core functions for RAG data processing.

This module implements a few data transformation functions (`FlatMapFn`) shared by RAG
pipelines. Stateless callables are implemented as high-order functions, while stateful
callables are implemented as classes.
"""

import logging
import pathlib
import pickle
import random
import time
from collections import defaultdict
from dataclasses import dataclass
from typing import Any, Callable, Collection, Iterator, Mapping, Sequence

import numpy as np
import torch

from base.executor.robust_executor import RobustExecutor
from base.languages.unit_test_guesser import is_unit_test
from base.static_analysis.common import guess_lang_from_fp
from base.static_analysis.parsing import ParsingFailedError
from base.tokenizers.tokenizer import Tokenizer
from research.core.model_input import ModelInput
from research.core.types import compute_file_id
from research.data.rag import constants, utils
from research.data.rag.retrieval_utils import serialize_retrieved_chunks
from research.data.spark.pipelines.utils import map_parquet
from research.fim import fim_sampling
from research.fim.fim_augment import AbstractF<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ug<PERSON>er, FimRepr
from research.retrieval.retrieval_database import RetrievalDatabase
from research.retrieval.types import Document
from research.static_analysis.parsing import TsParsedFile

FimProblem = fim_sampling.FimProblem
logger = logging.getLogger(__name__)


class FileFilter:
    """A class to filter files."""

    SIZE_KEY: str = "size"
    LANGPART_KEY: str = "langpart"
    MAX_STARS_REPO_PATH_KEY: str = "max_stars_repo_path"

    def __init__(
        self,
        small_filter_char_threshold: int | None,
        small_downsampled_probability: float | None,
        small_downsample_char_threshold: int | None,
        sample_languages: Collection[str],
        only_keep_unit_test_file: bool,
    ):
        self.small_filter_char_threshold = small_filter_char_threshold
        self.small_downsampled_probability = small_downsampled_probability
        self.small_downsample_char_threshold = small_downsample_char_threshold
        self.sample_languages = sample_languages
        self.only_keep_unit_test_file = only_keep_unit_test_file

    def filter(self, file: Mapping[str, Any]) -> bool:
        """Returns True to remove the file."""
        file_size = file[self.SIZE_KEY]
        if (  # Filter tiny files
            self.small_filter_char_threshold is not None
            and file_size < self.small_filter_char_threshold
        ):
            return True

        if (  # Subsample small files
            self.small_downsampled_probability is not None
            and self.small_downsample_char_threshold is not None
            and file_size < self.small_downsample_char_threshold
            and random.random() > self.small_downsampled_probability
        ):
            return True

        if file[self.LANGPART_KEY] not in self.sample_languages:
            return True
        if self.only_keep_unit_test_file and not is_unit_test(
            file[self.MAX_STARS_REPO_PATH_KEY]
        ):
            return True
        return False


class RobustFIMSampler:
    """A class to sample FIM problems from a repo."""

    def __init__(
        self,
        file_filter: FileFilter,
        max_problems_per_file: int,
        every_n_lines: int,
        random_seed: int,
        sampler: fim_sampling.CSTFimSampler | None = None,
        file_augmenter: FileAugmenter | None = None,
        get_node_weight: fim_sampling.GET_NODE_WEIGHT_TYPE | None = None,
        timeout_s: float = 10.0,
    ):
        """Args:
        file_filter: The file filter to use.
        max_problems_per_file: The maximum number of problems to sample from each file.
        every_n_lines: The spacing between sampled problems.
        random_seed: The random seed to use.
        get_node_weight: A function that returns a up-sampling weight for each candidate tree-sitter node. e.g., UsageBasedNodeWeights.
        timeout_s: The timeout (in seconds) for processing each file.
        """

        if sampler is None:
            self.sampler = fim_sampling.CSTFimSampler()
        else:
            self.sampler = sampler
        self.file_filter = file_filter
        self.file_augmenter = file_augmenter
        self.max_problems_per_file = max_problems_per_file
        self.every_n_lines = every_n_lines
        self.random_seed = random_seed
        self.get_node_weight = get_node_weight
        # Keys to read data for a file.
        self.CONTENT_KEY = "content"
        self.MAX_STARS_REPO_PATH_KEY = "max_stars_repo_path"
        self.batch_size = 500
        self.timeout_s = timeout_s

    def __call__(self, file_list: Sequence[Mapping]) -> Iterator[dict]:
        """Returns FIM samples.

        Args:
            repo: The repository, which is a list of files, to process.
        """
        all_samples: list[FimProblem] = []
        executor = RobustExecutor(timeout_s=self.timeout_s)
        for file in file_list:
            if self.file_filter.filter(file):
                continue

            file_content = file[self.CONTENT_KEY]
            file_path = file[self.MAX_STARS_REPO_PATH_KEY]
            file_id = compute_file_id(path=file_path, file_contents=file_content)
            cur_seed = int.from_bytes(file_id.encode(), "little") + self.random_seed
            self.sampler.rng.seed(cur_seed)

            try:
                samples = executor.run(self._sample_from_file, file_path, file_content)
                all_samples.extend(samples)
            except ParsingFailedError:
                logger.error(
                    f"File {file_path} with {len(file_content)} chars failed to parse."
                )
            except TimeoutError:
                logger.error(f"File {file_path} timed out.", exc_info=True)
            except RuntimeError:
                logger.error(f"File {file_path} failed.", exc_info=True)
        # In case a single row contains too many FIM problems.
        for i in range(0, len(all_samples), self.batch_size):
            yield {
                "fim_problems": all_samples[i : i + self.batch_size],
                "file_list": file_list,
            }

    def _sample_from_file(self, file_path: str, file_content: str) -> list[FimProblem]:
        """Returns FIM samples from a file."""
        lang = guess_lang_from_fp(file_path)
        if lang is None:
            return []
        pfile = TsParsedFile.parse(
            path=pathlib.Path(file_path), lang=lang, code=file_content
        )
        if self.file_augmenter is not None:
            pfile = self.file_augmenter.transform(pfile, self.sampler.rng)
        samples, _ = self.sampler.sample_every_n_lines(
            pfile,
            every_n_lines=self.every_n_lines,
            get_node_weight=self.get_node_weight,
            max_problems_per_file=self.max_problems_per_file,
        )
        return samples


def _find_matching_files(files: list[str], fim_problem: FimProblem) -> list[str]:
    """Filters out file contents that don't match the fim problem."""
    correct_files = []
    for content in files:
        try:
            # HACK(jeff): this throws if there is a discrepancy while parsing.
            FimRepr.from_cst_fim_problem(fim_problem, content)
        except ValueError:
            continue
        correct_files.append(content)

    return correct_files


@dataclass
class RobustFimAugmenter:
    """A class to augment FIM problems."""

    fim_augmenter: AbstractFimAugmenter
    random_seed: int
    timeout_s: float = 10.0

    CONTENT_KEY: str = constants.CONTENT_COLUMN
    MAX_STARS_REPO_PATH_KEY: str = constants.PATH_COLUMN

    def __call__(
        self, file_list: Sequence[Mapping], fim_problems: Sequence[FimProblem]
    ) -> Iterator[dict]:
        """Transforms FIM problems. Returns at most one augmented problem per input
        problem (or zero if the transformation is not applicable or unsuccessful).

        Args:
            file_list: The list of files.
            fim_problems: The FIM problems to augment.
        """
        all_samples: list[FimProblem] = []
        executor = RobustExecutor(timeout_s=self.timeout_s)
        file_map = defaultdict[str, list[str]](list[str])
        for file in file_list:
            file_map[file[self.MAX_STARS_REPO_PATH_KEY]].append(file[self.CONTENT_KEY])
        for fim_problem in fim_problems:
            file_path = str(fim_problem.file_path)
            if file_path not in file_map:
                logger.error(f"File {file_path} was not found.")
                continue
            files = file_map[file_path]
            matching_files = _find_matching_files(files, fim_problem)
            if len(matching_files) != 1:
                logger.error(
                    f"None of the {len(files)} files at {file_path}"
                    f"match the FIM problem."
                )
                continue

            file_content = matching_files[0]
            try:
                sample = executor.run(self._augment_fim, file_content, fim_problem)
                if sample is not None:
                    all_samples.append(sample)
            except ParsingFailedError:
                logger.error(
                    f"File {file_path} with {len(file_content)} chars failed to parse."
                )
            except TimeoutError:
                logger.error(f"File {file_path} timed out.", exc_info=True)
            except RuntimeError:
                logger.error(f"File {file_path} failed.", exc_info=True)

        yield {"fim_problems": all_samples, "file_list": file_list}

    def _augment_fim(self, code: str, fim_problem: FimProblem) -> FimProblem | None:
        """Returns an augmented FIM problem if successful."""
        file_path = fim_problem.file_path
        file_id = compute_file_id(path=file_path, file_contents=code)
        cur_seed = int.from_bytes(file_id.encode(), "little") + self.random_seed
        rng = random.Random(cur_seed)
        lang = guess_lang_from_fp(file_path)
        if lang is None:
            return None
        pfile = TsParsedFile.parse(path=file_path, lang=lang, code=code)
        return self.fim_augmenter.transform(fim_problem, pfile, rng)


class RepoFilter:
    """A class to filter repos."""

    def __init__(self, max_repo_size_files: int):
        self.max_repo_size_files = max_repo_size_files

    def __call__(
        self, file_list: Sequence[Mapping[str, Any]], fim_problems
    ) -> Iterator[dict]:
        """Returns True to remove the repo."""
        num = 0
        for file in file_list:
            if file[constants.FILE_LANG_COLUMN] in constants.RETRIEVAL_LANGUAGES:
                num += 1
        if num <= self.max_repo_size_files:
            yield {"file_list": file_list, "fim_problems": fim_problems}


class GenerateRetrievedChunksFromFim:
    """A class to generate retrieved chunks from FIM problems."""

    def __init__(
        self,
        retrieval_database_factories: Mapping[str, Callable[[], RetrievalDatabase]],
        retrieval_languages: Sequence[str],
        num_retrieved_chunks: int,
        max_failures: int = 100,
    ):
        self.retrieval_database_factories = retrieval_database_factories
        self._ret_dbs = dict[str, RetrievalDatabase]()
        self.retrieval_languages = retrieval_languages
        self.num_retrieved_chunks = num_retrieved_chunks
        self.max_failures = max_failures

    def _lazy_init_ret_dbs(self):
        """Loads the retrieval databases."""
        for key, factory in self.retrieval_database_factories.items():
            if key not in self._ret_dbs:
                self._ret_dbs[key] = factory()
                self._ret_dbs[key].load()

    def _clear_index(self):
        """Clears the retrieval databases."""
        for db in self._ret_dbs.values():
            db.remove_all_docs()

    @staticmethod
    def process_single_fim_problem(
        sample: FimProblem,
        documents: Sequence[Document],
        original_doc_ids: set[str],
        ret_dbs: dict[str, RetrievalDatabase],
        num_retrieved_chunks: int,
    ) -> dict:
        """Returns a retrieval-augmented sample."""
        path = str(sample.file_path)
        prefix = sample.prefix.code
        suffix = sample.suffix.code
        middle_char_start, middle_char_end = sample.middle_range.to_tuple()
        suffix_offset = len(sample.suffix.range) - len(suffix)

        # We keep the serialized middle spans.
        output = dict(
            prefix=prefix,
            middle_spans=pickle.dumps(sample.middle_spans),
            suffix=suffix,
            suffix_offset=suffix_offset,
            middle_char_start=middle_char_start,
            middle_char_end=middle_char_end,
            file_path=path,
        )

        # Index the current document's before state.
        new_doc = Document.new(text=prefix + suffix, path=path)
        for db in ret_dbs.values():
            db.add_doc(new_doc)
        logger.info(f"Indexed {path} with {len(prefix + suffix)} chars.")

        # Retrieve chunks
        doc_ids = {doc.id for doc in documents if doc.path != path}
        doc_ids.add(new_doc.id)
        model_input = ModelInput(
            prefix=prefix, suffix=suffix, path=path, doc_ids=doc_ids
        )
        for key, ret_db in ret_dbs.items():
            # TODO(Xuanyi): the signature dense retriever will stuck at some data.
            # Around 15% of FIM problems are dropped because of this problem.
            # As if a repo has a FIM problem that being stucked by the dense retriever' query func,
            # the entire parquet file will be dropped.
            # I have moved some problemtic data at /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/fim-normal-stuck,
            # and we need to spend some time to figure out why this is happening.
            output[f"{key}_chunks"] = serialize_retrieved_chunks(
                ret_db.query(
                    model_input=model_input,
                    top_k=num_retrieved_chunks,
                    doc_ids=doc_ids,
                )[0]
            )
            logger.info(f"Retrieved {num_retrieved_chunks} chunks from {key}.")

        if new_doc.id not in original_doc_ids:
            for db in ret_dbs.values():
                db.remove_doc(new_doc.id)
            logger.info(f"Removed {path} with {len(prefix + suffix)} chars.")
        return output

    def __call__(
        self,
        file_list: Sequence[Mapping[str, Any]],
        fim_problems: Sequence[FimProblem],
    ) -> Iterator[dict]:
        """Convert each FIM problem in the repo to be retrieval-augmented data samples.

        Args:
            repo: The repository, which is a list of files, to process.
            fim_problems: The FIM problems to augment.
            config: The configuration object.
            tokenizer: The tokenizer to use.
            retrieval_database: The retrieval database to use.

        Returns:
            A generator of processed rows.
        """
        self._lazy_init_ret_dbs()
        self._clear_index()
        # Step 1: Index all files.
        start_time = time.time()
        documents: list[Document] = []
        for file in file_list:
            # Only add files of desired languages to be retrieved
            if (
                self.retrieval_languages
                and file[constants.FILE_LANG_COLUMN] not in self.retrieval_languages
            ):
                continue

            document = Document.new(
                text=file[constants.CONTENT_COLUMN],
                path=file[constants.PATH_COLUMN],
            )
            documents.append(document)

        original_doc_ids = {doc.id for doc in documents}
        for db in self._ret_dbs.values():
            db.add_docs(documents)
        logger.info(f"Indexed {len(documents)} files in {time.time() - start_time:.2f} sec.")  # fmt: off
        logger.info(f"GPU Memory Usage: {torch.cuda.memory_allocated() / 1e9:.2f} GB.")
        num_failures = 0
        # Step 2: Retrieve chunks for each FIM problem.
        for index, sample in enumerate(fim_problems):
            try:
                output = self.process_single_fim_problem(
                    sample,
                    documents,
                    original_doc_ids,
                    self._ret_dbs,
                    self.num_retrieved_chunks,
                )
                logger.info(
                    f"Finished processing {index}/{len(fim_problems)} FIM problems"
                    f" (GPU Memory Usage: {torch.cuda.memory_allocated() / 1e9:.2f} GB)."
                )
                yield output
            except Exception as e:
                logger.error(
                    f"Failed to process {index}/{len(fim_problems)} FIM problem: {e}"
                )
                num_failures += 1
            if num_failures >= self.max_failures:
                self._clear_index()
                raise RuntimeError(
                    f"Failed to process {num_failures} FIM problems ({self.max_failures} max)."
                )

        logger.info(f"GPU Memory Usage: {torch.cuda.memory_allocated() / 1e9:.2f} GB.")
        # Keeps database and reset the index.
        self._clear_index()
        logger.info(f"Finish processing the repo in {time.time() - start_time:.2f} secs.")  # fmt: off


def serialize_fim_problems(fim_problems: Sequence[FimProblem]) -> dict[str, bytes]:
    return {"fim_problems": pickle.dumps(fim_problems)}


def deserialize_fim_problems(
    fim_problems: bytes,
) -> dict[str, list[FimProblem]]:
    return {"fim_problems": pickle.loads(fim_problems)}


def remove_fim_with_empty_prefix(
    fim_problems: Sequence[FimProblem],
) -> dict[str, list[FimProblem]]:
    """Filter FIM problems."""
    # TODO(michiel) fix insanity.
    # Sampler can return empty prefix. Prompt formatters don't like this, so we filter them out for now.
    return {"fim_problems": [sample for sample in fim_problems if sample.prefix]}


def remove_trouble_keys_from_file_list(file_list: Sequence[dict]):
    """Drop some keys that are useless but cause schema trouble."""
    new_file_lists = []
    for file in file_list:
        for key in constants.TROUBLE_KEYS:
            file.pop(key, None)
        new_file_lists.append(file)
    return {"file_list": new_file_lists}


def only_preserve_keys(keys: Sequence[str]) -> map_parquet.FlatMapFn:
    """This function is used to remove unnecessary keys from your data.

    For example, your input data has 3 keys, A/B/C, you can use this function to only keep A/B."""

    def flat_map_fn(**input_row) -> Iterator[dict]:
        for k in keys:
            if k not in input_row:
                raise ValueError(f"Key {k} not found in {input_row.keys()=}.")
        yield {k: input_row[k] for k in keys}

    return flat_map_fn


def create_pad_pack_tokens_fn(
    seq_len: int, tokenizer: Tokenizer
) -> map_parquet.FlatMapFn:
    """Returns a row-wise function to pad and pack tokens."""

    if tokenizer.vocab_size < 65500:
        dtype = np.uint16
    else:
        dtype = np.int32

    def flat_map_fn(prompt_tokens) -> Iterator[dict]:
        yield {
            "prompt_tokens": utils.pad_pack_tokens(
                tokens=prompt_tokens,
                seq_len=seq_len,
                tokenizer=tokenizer,
                dtype=dtype,
            )
        }

    return flat_map_fn


def create_unpack_unpad_tokens_fn(tokenizer: Tokenizer) -> map_parquet.FlatMapFn:
    """Returns a row-wise function to unpack and unpad tokens."""

    if tokenizer.vocab_size < 65500:
        dtype = np.uint16
    else:
        dtype = np.int32

    def flat_map_fn(prompt_tokens: bytearray) -> Iterator[dict]:
        unpacked_tokens = np.frombuffer(
            prompt_tokens, dtype=np.dtype(dtype).newbyteorder("<")
        ).tolist()
        padding = tokenizer.special_tokens.padding
        if padding in unpacked_tokens:
            index = unpacked_tokens.index(padding)
            assert unpacked_tokens[index:] == [padding] * (len(unpacked_tokens) - index)
            prompt_tokens = unpacked_tokens[:index]
        else:
            prompt_tokens = unpacked_tokens
        yield {"prompt_tokens": prompt_tokens}

    return flat_map_fn

"""Unit tests for ender.py.

pytest research/data/rag/ender_test.py
"""

import pytest
from megatron.tokenizer.tokenizer import StarCoderTokenizer

from research.data.rag.conftest import (
    ENDER_CONFIG,
    FOOBAR_ENDER_PROMPTS,
    FOOBAR_ENDER_RETRIEVAL,
    LONG_ENDER_PROMPTS,
    LONG_ENDER_RETRIEVAL,
)
from research.data.rag.ender import generate_ender_prompt


@pytest.mark.parametrize(
    "retrieval_outputs,expected_prompt_tokens",
    [
        (FOOBAR_ENDER_RETRIEVAL, FOOBAR_ENDER_PROMPTS),
        (LONG_ENDER_RETRIEVAL, LONG_ENDER_PROMPTS),
    ],
)
def test_generate_ender_prompt_foobar(retrieval_outputs, expected_prompt_tokens):
    tokenizer = StarCoderTokenizer()
    config = ENDER_CONFIG
    gen = generate_ender_prompt(tokenizer=tokenizer, config=config)
    for retrieval_output, expected in zip(
        retrieval_outputs, expected_prompt_tokens, strict=True
    ):
        prompt_outputs = gen(**retrieval_output)
        assert isinstance(prompt_outputs, dict)
        prompt_tokens = prompt_outputs["prompt_tokens"]
        assert tokenizer.detokenize(prompt_tokens) == tokenizer.detokenize(expected)
        assert prompt_tokens == expected

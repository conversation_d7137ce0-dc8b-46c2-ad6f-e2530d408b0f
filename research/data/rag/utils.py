"""Helper functions for data pipelines."""

from typing import Any

import numpy as np

from base.tokenizers.tokenizer import Tokenizer
from research.data.spark import get_session
from research.data.spark.pipelines.utils import map_parquet
from research.data.utils.config_utils import save_config_s3  # noqa
from research.data.utils.spark_utils import repartition_and_shuffle


def inspect_samples(sample_dir: str, max_files: int = 1):
    """Load samples from S3 for inspection."""
    spark = get_session()
    files = map_parquet.list_files(
        spark,
        sample_dir,
        suffix="parquet",
        include_path=False,
    )
    read_paths = [sample_dir + filepath for filepath in files]
    read_paths = read_paths[:max_files]
    df = spark.read.parquet(*read_paths)
    df = df.toPandas()
    spark.stop()

    # Check the number of rows in the DataFrame
    num_rows = len(df)

    # Store the first row in a variable
    first_row = df.iloc[0]

    # Now you can work with the number of rows and the first row
    print(f"Number of Rows: {num_rows}")
    print(f"Keys: {first_row.keys()}")
    print(f"Middle: {first_row['middle']}")
    print(f"Prefix: {first_row['prefix']}")

    return df


def pad_pack_tokens(
    tokens: np.ndarray, seq_len, tokenizer: Tokenizer, dtype
) -> bytearray:
    # Spark automatically converts arrays to lists.
    # our thing doesn't
    if len(tokens) > seq_len:
        raise ValueError(f"token length exceeds seq_len: {len(tokens)} > {seq_len}")
    pad_id: int = tokenizer.special_tokens.padding
    num_padding_tokens = seq_len - len(tokens)
    all_tokens = np.pad(tokens, (0, num_padding_tokens), constant_values=pad_id)
    # No need to use pack_tokens as numpy already pack everything into byte arrays.
    # just need to ensure byteorder is little endian.
    # Also, affirmatively ensure that the array is unsigned 16 bit.
    packed_tokens = all_tokens.astype(dtype).newbyteorder("<").tobytes()
    return bytearray(packed_tokens)

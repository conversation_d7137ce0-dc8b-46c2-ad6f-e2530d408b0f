"""Helper functions for Ender data processing."""

import dataclasses
import pickle
import random
from dataclasses import dataclass

from megatron.tokenizer import tokenizer as research_tokenizer

import base.tokenizers as prod_tokenizer
from base.tokenizers.tokenizer import RagSpecialTokens
from research.core.model_input import ModelInput
from research.core.prompt_formatters import PromptFormatterEnder
from research.data.rag.retrieval_utils import deserialize_retrieved_chunks
from research.data.spark.pipelines.utils import map_parquet
from research.fim import fim_prompt, fim_sampling
from research.retrieval import utils as rutils


@dataclass
class EnderDataPromptFormattingConfig:
    """Configuration for formatting the Ender prompt."""

    component_order: list[str]
    shuffle_retrieved_chunks: bool
    max_prefix_tokens: int
    max_suffix_tokens: int
    max_retrieved_chunk_tokens: int
    max_filename_tokens: int
    max_prompt_tokens: int
    max_signature_tokens: int
    max_header_tokens: int
    max_target_tokens: int
    num_validation_samples: int
    seq_len: int

    # FIM Target Format
    max_pause_spans: int | None = None

    # Stateless caching
    context_quant_token_len: int = 0
    nearby_prefix_token_len: int = 0
    nearby_prefix_token_overlap: int = 0
    nearby_suffix_token_len: int = 0
    nearby_suffix_token_overlap: int = 0

    # Data augmentation
    random_seed: int = 74912
    data_augmentation_rate: float = 0.0
    dense_retrieval_dropout_rate: float = 0.0
    signature_retrieval_dropout_rate: float = 0.0
    max_prompt_token_range: tuple[int, int] | None = None

    def as_dict(self) -> dict:
        """Return configuration as a dictionary."""
        return dataclasses.asdict(self)


SUPPORTED_TOKENIZERS = (
    research_tokenizer.StarCoderTokenizer
    | research_tokenizer.StarCoder2Tokenizer
    | research_tokenizer.DeepSeekCoderBaseTokenizer
    | research_tokenizer.CodestralTokenizer
    | prod_tokenizer.DeepSeekCoderBaseTokenizer
    | prod_tokenizer.Llama3BaseTokenizer
    | prod_tokenizer.StarCoder2Tokenizer
    | prod_tokenizer.TiktokenStarCoderTokenizer
)


# TODO(michiel) add correctness test
def generate_ender_prompt(
    tokenizer: prod_tokenizer.Tokenizer, config: EnderDataPromptFormattingConfig
) -> map_parquet.FlatMapFn:
    """Return a row-wise function to generate Ender prompts."""

    def _generate_ender_promptprefix(
        prefix: str,
        middle_spans: bytes,
        suffix: str,
        suffix_offset: int,
        middle_char_start: int,
        middle_char_end: int,
        file_path: str,
        line_chunks: str,
        signature_chunks: str,
    ) -> dict[str, list[int]]:
        # Construct seed for data augmentation
        seed = (
            config.random_seed
            + int.from_bytes((file_path).encode(), "little")
            + middle_char_start
        )
        random.seed(seed)

        max_retrieved_chunk_tokens = config.max_retrieved_chunk_tokens
        max_signature_tokens = config.max_signature_tokens
        max_prompt_tokens = config.max_prompt_tokens

        # Sample whether to perform data augmentation at all
        if random.random() < config.data_augmentation_rate:
            # Randomly set dense retrieval tokens to 0 so model can operate without.
            if random.random() < config.dense_retrieval_dropout_rate:
                max_retrieved_chunk_tokens = 0

            # Randomly set signature tokens to 0 so model can operate without.
            if random.random() < config.signature_retrieval_dropout_rate:
                max_signature_tokens = 0

            if config.max_prompt_token_range is not None:
                max_prompt_tokens = random.randint(*config.max_prompt_token_range)

        prompt_formatter = PromptFormatterEnder(
            max_prefix_tokens=config.max_prefix_tokens,
            max_suffix_tokens=config.max_suffix_tokens,
            max_signature_tokens=max_signature_tokens,
            max_retrieved_chunk_tokens=max_retrieved_chunk_tokens,
            max_filename_tokens=config.max_filename_tokens,
            max_header_tokens=config.max_header_tokens,
            component_order=config.component_order,
            shuffle_retrieved_chunks=config.shuffle_retrieved_chunks,
            context_quant_token_len=config.context_quant_token_len,
            nearby_prefix_token_len=config.nearby_prefix_token_len,
            nearby_prefix_token_overlap=config.nearby_prefix_token_overlap,
            nearby_suffix_token_len=config.nearby_suffix_token_len,
            nearby_suffix_token_overlap=config.nearby_suffix_token_overlap,
        )
        # We don't load new tokenizer for every row even though we build prompt formatter
        prompt_formatter.tokenizer = tokenizer  # type: ignore
        prompt_formatter.max_prompt_tokens = max_prompt_tokens

        deserialized_line_chunks = deserialize_retrieved_chunks(line_chunks)
        deserialized_signature_chunks = deserialize_retrieved_chunks(signature_chunks)

        model_input = ModelInput(
            prefix=prefix,
            suffix=suffix,
            retrieved_chunks=deserialized_line_chunks,
            path=file_path,
            cursor_position=len(prefix),
            extra={
                "signature_chunks": deserialized_signature_chunks,
            },
        )

        # TODO(michiel) Add option for sampling different prompt styles
        prompt_tokens, _ = prompt_formatter.prepare_prompt(model_input)

        deserialized_middle_spans: list[
            fim_sampling.SkipOrOutput[fim_sampling.SrcSpan]
        ] = pickle.loads(middle_spans)
        special_tokens = tokenizer.special_tokens
        assert isinstance(
            special_tokens,
            (RagSpecialTokens, research_tokenizer.ResearchSpecialTokens),
        )
        assert special_tokens.skip is not None
        assert special_tokens.pause is not None
        target_tokens = fim_prompt._format_middle(
            middle_spans=deserialized_middle_spans,
            tokenize_span=lambda span: tokenizer.tokenize_safe(span.code),
            skip_id=special_tokens.skip,
            pause_id=special_tokens.pause,
            fim_stop_id=special_tokens.eos,
            max_pause_spans=config.max_pause_spans,
        )

        target_tokens = target_tokens[: config.max_target_tokens]
        prompt_tokens += target_tokens

        return {"prompt_tokens": prompt_tokens}

    return _generate_ender_promptprefix

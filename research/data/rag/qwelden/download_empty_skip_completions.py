"""
Utility to download empty and skip completions from GCS and save them to disk.

ONLY WORKS FOR COMPLETIONS THAT USE QWEN25CODER TOKENIZER.

Usage:
python research/data/rag/qwelden/download_empty_skip_completions.py \
    --tokenizer-name qwen25coder \
    --gcs \
    --tenant i0-vanguard0 \
    --model-name qweldenv1-1-14b \
    --start-time 2024-11-01T00:00:00Z \
    --end-time 2025-01-14T23:59:59Z \
    --limit 50000 \
    --offset 0
"""

import argparse
import json
import logging
import sys
import zstandard as zstd
from datetime import datetime
from pathlib import Path
from typing import cast, List, Optional

from base.datasets.completion import CompletionDatum
from base.datasets.completion_dataset_gcs import CompletionDataset, Filters
from base.datasets.hindsight_completion import HindsightCompletionDatum
from base.datasets.tenants import get_tenant, DatasetTenant
from base.tokenizers import create_tokenizer_by_name
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from research.data.rag.qwelden.get_requests import (
    get_request_ids,
)
from research.core.data_paths import canonicalize_path
from research.core.utils_for_file import read_jsonl_zst

logger = logging.getLogger(__name__)

BASE_DIR = Path(canonicalize_path("user/pranay/hindsight/"))


def get_gcs_completions(
    tenant: DatasetTenant,
    model_name: str = "qweldenv1-1-14b",
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    not_filtered_only: bool = False,
    limit: int = 100,
    offset: int = 0,
) -> List[CompletionDatum]:
    """Get completions from GCS using request IDs from BigQuery."""
    # Get request IDs.
    request_ids = get_request_ids(
        tenant_name=tenant.name,
        model_name=model_name,
        start_time=start_time,
        end_time=end_time,
        not_filtered_only=not_filtered_only,
        limit=limit,
        offset=offset,
    )

    # Query completions.
    print(f"Querying for {len(request_ids)} completion events.")
    completions = CompletionDataset.create_data_from_gcs(
        tenant=tenant, filters=Filters(request_ids=request_ids)
    )
    completions = list(completions)
    print(f"Found {len(completions)} completions.")
    return completions


def load_completions_from_file(input_file: str):
    """Load completions from a local .jsonl.zst file."""
    print(f"Loading completions from {input_file}")
    data = read_jsonl_zst(input_file)
    print(f"Found {len(data)} completions.")
    return data


def write_hindsight_data(completions: List[CompletionDatum], output_file: Path):
    """Write hindsight data to a zstd compressed JSON lines file."""

    def _convert_datetime_nested(obj):
        """Convert datetime objects to timestamps, handling nested structures."""
        if isinstance(obj, dict):
            return {k: _convert_datetime_nested(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [_convert_datetime_nested(item) for item in obj]
        elif isinstance(obj, tuple):
            return tuple(_convert_datetime_nested(item) for item in obj)
        elif isinstance(obj, datetime):
            return int(obj.timestamp())
        return obj

    hindsight_datums = [
        HindsightCompletionDatum(completion=datum, ground_truth="")
        for datum in completions
    ]
    fake_hindsight_dicts = [datum.to_dict() for datum in hindsight_datums]
    with zstd.open(output_file, "w", encoding="utf-8") as f:
        for datum in fake_hindsight_dicts:
            json.dump(_convert_datetime_nested(datum), f)
            f.write("\n")


def categorize_and_save_completions(
    completions: List[CompletionDatum],
    start_time: str,
    end_time: str,
    tenant_name: str,
    limit: int,
    offset: int,
    tokenizer_name: str,
):
    """Categorize completions and save empty/skip data to specified directory.

    ONLY WORKS FOR COMPLETIONS THAT USE QWEN25CODER TOKENIZER.
    """
    # Initialize tokenizer
    assert (
        tokenizer_name == "qwen25coder"
    ), "Ensure that completions and tokenizer match. Currently only supports Qwen25CoderTokenizer as recent completion models are Qwen based."
    tokenizer: Qwen25CoderTokenizer = cast(
        Qwen25CoderTokenizer, create_tokenizer_by_name(tokenizer_name)
    )
    eos_id = tokenizer.special_tokens.eos
    skip_id = tokenizer.special_tokens.skip

    # Categorize completions
    empty_completion = []
    skip_completion = []
    neither_empty_nor_skip = []

    # Keep only the completions with at least 50 blob names
    completions = [
        completion
        for completion in completions
        if len(completion.request.blob_names) >= 50
    ]

    for completion in completions:
        # Check for empty completions
        if len(completion.response.token_ids) == 0:
            assert completion.inference_response is not None
            if (
                len(completion.inference_response.token_ids) != 1
                or completion.inference_response.token_ids[0] != eos_id
            ):
                logger.error(
                    f"Empty completion with non-eos token_ids: {completion.request_id}"
                )
                continue
            empty_completion.append(completion)
        # Check for skip completions
        elif skip_id in completion.response.token_ids:
            if (
                len(completion.response.skipped_suffix) > 0
                or len(completion.response.suffix_replacement_text) > 0
            ):
                skip_completion.append(completion)
        else:
            neither_empty_nor_skip.append(completion)

    all_completion = empty_completion + skip_completion + neither_empty_nor_skip
    print(
        f"{len(empty_completion)=}, {len(skip_completion)=}, {len(neither_empty_nor_skip)=}, {len(all_completion)=}"
    )

    start_date = datetime.fromisoformat(start_time).strftime("%Y-%m-%d")
    end_date = datetime.fromisoformat(end_time).strftime("%Y-%m-%d")

    # Write data
    empty_directory = (
        BASE_DIR
        / f"empty_{start_date}_{end_date}"
        / f"{offset}_{offset+limit}"
        / tenant_name
    )
    skip_directory = (
        BASE_DIR
        / f"skip_{start_date}_{end_date}"
        / f"{offset}_{offset+limit}"
        / tenant_name
    )

    save_data(empty_completion, empty_directory, len(all_completion))
    save_data(skip_completion, skip_directory, len(all_completion))


def save_data(completions: List[CompletionDatum], directory: Path, total: int):
    """Save hindsight data and metadata to a directory."""
    print(f"Saving data to {directory}")
    directory.mkdir(parents=True, exist_ok=True)

    request_ids = [completion.request_id for completion in completions]
    with open(directory / "request_ids.json", "w") as f:
        json.dump(request_ids, f)

    metadata = {
        "count": len(request_ids),
        "total": total,
        "percentage": len(request_ids) / total * 100,
        "command": " ".join(sys.argv),
    }
    print(json.dumps(metadata, indent=2))
    with open(directory / "metadata.json", "w") as f:
        json.dump(metadata, f, indent=2)

    write_hindsight_data(completions, directory / "data.jsonl.zst")


def main():
    """Main function to process completions and save results.

    ONLY WORKS FOR COMPLETIONS GENERATED USING QWEN25CODER TOKENIZER.
    """
    parser = argparse.ArgumentParser(
        description="Process completions from GCS or local file"
    )

    # Input source group (mutually exclusive)
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument("--gcs", action="store_true", help="Fetch data from GCS")
    input_group.add_argument(
        "--input-file", type=str, help="Path to local .jsonl.zst file with completions"
    )

    parser.add_argument(
        "--tokenizer-name", type=str, help="Tokenizer name. Must be qwen25coder."
    )

    # GCS parameters
    parser.add_argument(
        "--tenant",
        type=str,
        choices=["dogfood-shard", "i0-vanguard0"],
        help="Tenant name (required if --gcs is used)",
    )
    parser.add_argument(
        "--model-name",
        type=str,
        default="qweldenv1-1-14b",
        help="Model name to filter completions",
    )
    parser.add_argument(
        "--start-time",
        type=str,
        help="Start time in ISO format (e.g., 2024-11-01T00:00:00Z)",
    )
    parser.add_argument(
        "--end-time",
        type=str,
        help="End time in ISO format (e.g., 2025-01-14T23:59:59Z)",
    )
    parser.add_argument(
        "--limit", type=int, default=100, help="Maximum number of completions to fetch"
    )
    parser.add_argument(
        "--offset",
        type=int,
        default=0,
        help="Offset to fetch completions. Will fetch completions from offset to offset + limit.",
    )
    args = parser.parse_args()

    # Validate arguments
    if args.gcs and not args.tenant:
        parser.error("--tenant is required when using --gcs")

    # Get completions from either GCS or local file
    if args.gcs:
        tenant = get_tenant(args.tenant)
        completions = get_gcs_completions(
            tenant=tenant,
            model_name=args.model_name,
            start_time=args.start_time,
            end_time=args.end_time,
            not_filtered_only=True,
            limit=args.limit,
            offset=args.offset,
        )
    else:
        completions = load_completions_from_file(args.input_file)

    # Categorize completions and save results
    categorize_and_save_completions(
        completions=completions,
        start_time=args.start_time,
        end_time=args.end_time,
        tenant_name=args.tenant,
        limit=args.limit,
        offset=args.offset,
        tokenizer_name=args.tokenizer_name,
    )

    print("\nProcessing complete!")


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    main()

#!/bin/bash

# Simple script to run multiple stages of the qwelden pipeline in sequence

# No default tenant - it's required
TENANT=""
# Flag to print commands without executing
DRY_RUN=false
# Array to store stages
STAGES=()

# Help message
function show_help {
    echo "Usage: $0 [options]"
    echo "Run multiple stages of the qwelden pipeline in sequence"
    echo ""
    echo "Options:"
    echo "  -t, --tenant_name NAME     Tenant name (REQUIRED)"
    echo "  -d, --inner_dir DIR        Inner directory path"
    echo "  -m, --mix_config_name NAME Mix configuration name"
    echo "  -s, --stages STAGE [STAGE...]  Stages to run (retrieval, target, mixup, prompt)"
    echo "  -p, --print-only           Print commands without executing them"
    echo "  -h, --help                 Show this help message"
    echo ""
    echo "Example:"
    echo "  $0 -t i0-vanguard0 -m my_mix -s mixup prompt"
    echo "  $0 -t i0-vanguard0 -d my_dir -s retrieval target"
}

# Parse options
while [[ $# -gt 0 ]]; do
    case "$1" in
        -t|--tenant_name)
            TENANT="$2"
            shift 2
            ;;
        -d|--inner_dir)
            INNER_DIR="$2"
            shift 2
            ;;
        -m|--mix_config_name)
            MIX_CONFIG_NAME="$2"
            shift 2
            ;;
        -s|--stages)
            shift
            # Collect all stages until the next option or end of arguments
            while [[ $# -gt 0 && ! "$1" == -* ]]; do
                STAGES+=("$1")
                shift
            done
            ;;
        -p|--print-only)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Check if tenant name was provided
if [ -z "$TENANT" ]; then
    echo "Error: Tenant name is required"
    show_help
    exit 1
fi

# Check if any stages were provided
if [ ${#STAGES[@]} -eq 0 ]; then
    echo "Error: No stages specified"
    show_help
    exit 1
fi

# Build all commands first
COMMANDS=()
for STAGE in "${STAGES[@]}"; do
    # Build command with appropriate arguments
    CMD="python research/data/rag/qwelden/20241229_qwelden_hindsight.py --stage $STAGE --tenant_name $TENANT"

    # Add optional arguments if provided
    if [ ! -z "$INNER_DIR" ]; then
        CMD="$CMD --inner_dir $INNER_DIR"
    fi

    if [ ! -z "$MIX_CONFIG_NAME" ]; then
        CMD="$CMD --mix_config_name $MIX_CONFIG_NAME"
    fi

    COMMANDS+=("$CMD")
done

# Print all commands
echo "Commands to be executed:"
for CMD in "${COMMANDS[@]}"; do
    echo "  $CMD"
done
echo ""

# If dry run, exit here
if [ "$DRY_RUN" = true ]; then
    echo "Dry run completed. Exiting without executing commands."
    exit 0
fi

# Run each command in sequence
for CMD in "${COMMANDS[@]}"; do
    echo "========================================================"
    echo "Executing: $CMD"
    echo "Started at: $(date)"
    echo "========================================================"

    # Run the command
    eval $CMD

    # Check if the command completed successfully
    if [ $? -ne 0 ]; then
        echo "Error: Command failed"
        exit 1
    fi

    echo "Completed at: $(date)"
    echo "========================================================"
done

echo "All commands completed successfully!"

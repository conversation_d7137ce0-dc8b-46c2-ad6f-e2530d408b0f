import argparse
import logging
from typing import Optional, List

from google.cloud import bigquery
from base.datasets.tenants import get_tenant
from base.datasets.gcp_creds import get_gcp_creds

logger = logging.getLogger(__name__)


def get_request_ids(
    tenant_name: str,
    model_name: str,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    not_filtered_only: bool = False,
    limit: Optional[int] = None,
    offset: Optional[int] = None,
    page_size: Optional[int] = 128,
) -> List[str]:
    """Run a BigQuery query to get completion request IDs.

    Args:
        tenant_name: Name of the tenant to query
        model_name: Model name to filter by
        start_time: Start timestamp in ISO format (e.g., "2023-01-01T00:00:00Z")
        end_time: End timestamp in ISO format (e.g., "2023-01-31T23:59:59Z")
        not_filtered_only: Whether to only return request IDs that were not filtered
        limit: Maximum number of results to return
        offset: Offset for the query
        page_size: Page size for the query

    Returns:
        List of request IDs
    """
    tenant = get_tenant(tenant_name)

    query = f"""
SELECT DISTINCT chr.request_id
FROM `{tenant.project_id}.{tenant.search_dataset_name}.model` m
JOIN `{tenant.project_id}.{tenant.analytics_dataset_name}.completion_host_response` chr
    ON m.request_id = chr.request_id
JOIN `{tenant.project_id}.{tenant.analytics_dataset_name}.human_request_metadata` rm
    ON m.request_id = rm.request_id
JOIN `{tenant.project_id}.{tenant.analytics_dataset_name}.api_http_response` http
    ON m.request_id = http.request_id
LEFT JOIN `{tenant.project_id}.{tenant.analytics_dataset_name}.completion_post_process` cpp
    ON m.request_id = cpp.request_id
WHERE JSON_EXTRACT_SCALAR(http.sanitized_json, '$.code') = "200"
AND m.model_name = "{model_name}"
AND chr.tenant = "{tenant_name}"
"""

    # Add time range filters if provided
    if start_time:
        query += f'\nAND chr.time >= TIMESTAMP("{start_time}")'
    if end_time:
        query += f'\nAND chr.time <= TIMESTAMP("{end_time}")'

    if not_filtered_only:
        query += "\nAND (cpp.request_id IS NULL OR JSON_EXTRACT_SCALAR(cpp.sanitized_json, '$.filter_reason') = \"NOT_FILTERED\")"

    query += "\nORDER BY chr.request_id"

    if limit:
        query += f"\nLIMIT {limit}"
    if offset:
        query += f"\nOFFSET {offset}"

    # Get credentials and create client
    gcp_creds, _ = get_gcp_creds()
    bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)

    logger.info("Querying BigQuery: %s", query)

    # Execute the query
    rows = bigquery_client.query_and_wait(query, page_size=page_size)

    # Extract request IDs
    request_ids = list(set([row.request_id for row in rows]))
    request_ids = sorted(request_ids)
    logger.info(f"Found {len(request_ids)} request IDs")

    return request_ids


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Query BigQuery for completion request IDs"
    )

    parser.add_argument(
        "--tenant-name",
        type=str,
        default="dogfood-shard",
        help="Name of the tenant to query (default: dogfood-shard)",
    )

    parser.add_argument(
        "--model-name",
        type=str,
        default="qweldenv1-1-14b",
        help="Model name to filter by (default: qweldenv1-1-14b)",
    )

    parser.add_argument(
        "--start-time",
        type=str,
        default=None,
        help="Start timestamp in ISO format (e.g., '2025-01-01T00:00:00Z')",
    )

    parser.add_argument(
        "--end-time",
        type=str,
        default=None,
        help="End timestamp in ISO format (e.g., '2025-01-31T23:59:59Z')",
    )

    parser.add_argument(
        "--not-filtered-only",
        action="store_true",
        default=False,
        help="Whether to only return request IDs that were not filtered",
    )

    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="Maximum number of results to return",
    )

    return parser.parse_args()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    args = parse_args()

    request_ids = get_request_ids(
        tenant_name=args.tenant_name,
        model_name=args.model_name,
        start_time=args.start_time,
        end_time=args.end_time,
        not_filtered_only=args.not_filtered_only,
        limit=args.limit,
    )

    print(f"Retrieved {len(request_ids)} request IDs")

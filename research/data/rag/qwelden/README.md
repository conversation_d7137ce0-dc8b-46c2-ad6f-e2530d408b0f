# Qwelden Data Pipeline

This directory contains scripts for processing and preparing data for the Qwelden model training pipeline. The pipeline processes hindsight data (user interactions with the model) to create datasets for training and evaluation. Note that this pipeline should be ran for train and eval datasets separately.

## Pipeline Overview

The Qwelden data pipeline consists of several stages:

1. **Data Filtering**: Filter raw hindsight data based on model, license, and edit events
2. **Data Conversion**: Convert raw data from JSONL.ZST format to Parquet with ZSTD compression and partition by user_id
3. **Retrieval**: Retrieve line and signature chunks, recency chunks, and diff chunks
4. **Target Processing**: Truncate ground truth text based on model prediction patterns
5. **Mixup**: Mixup different subsets based on a ratio
6. **Prompt Generation**: Generate prompts with different configurations for training

## Key Scripts (in order)

- `filter_jsonl_data.py`: Filters hindsight data based on model, license, and edit events and combines them into one jsonl file
- `jsonl_to_partitioned_hindsight_parquet.py`: Converts filtered jsonl file to parquet with ZSTD compression and partitions by user_id, splitting each partition to 250 records
- `20241229_qwelden_hindsight.py`: Main pipeline script for processing hindsight data (retrieval->target->prompt). `run_stages.sh` can be used to run multiple stages in sequence.

## Usage Examples

### Filtering Data
python research/data/rag/qwelden/filter_jsonl_data.py \
    --dates 2024-11-01_2024-11-14 2024-11-15_2024-11-30 2024-12-01_2024-12-14 2024-12-15_2024-12-31 2025-01-01_2025-01-14 \
    --tenant_name i0-vanguard0 \
    --inner_dir vanguard_2024-11-01_2025-01-14 \
    --model qweldenv1_14b \
    --license-filter \
    --edit-filter

### Converting JSONL to Parquet

python research/data/rag/qwelden/jsonl_to_partitioned_hindsight_parquet.py \
    --input_file /mnt/efs/augment/user/pranay/hindsight/vanguard_test_2024-11-01_2025-01-14/data.jsonl.zst

### Running the Hindsight Pipeline
python research/data/rag/qwelden/20241229_qwelden_hindsight.py --stage retrieval --tenant_name <tenant_name> --inner_dir vanguard_2024-11-01_2025-01-14
python research/data/rag/qwelden/20241229_qwelden_hindsight.py --stage target --tenant_name <tenant_name> --inner_dir vanguard_2024-11-01_2025-01-14
python research/data/rag/qwelden/20241229_qwelden_hindsight.py --stage prompt --tenant_name <tenant_name> --inner_dir vanguard_2024-11-01_2025-01-14

python research/data/rag/qwelden/run_stages.sh -t <tenant_name> -d <inner_dir> -m <mix_config_name> -s retrieval target prompt

## Data Paths
The pipeline uses the following data paths:
Base URI:  gs://gcp-us1-user/pranay/hindsight
Base File Path: /mnt/efs/augment/user/pranay/hindsight

## Pipeline Configuration
The pipeline uses various configurations for prompt formatting:
* Different content lengths
* Various token allocations for signatures, recency, and diff retrievers
* Configurable input and prefix fractions

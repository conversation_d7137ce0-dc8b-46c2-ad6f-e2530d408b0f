"""
Convert JSONL.ZST files to Parquet files with ZSTD compression and partition by user_id.

Usage:
python research/data/rag/qwelden/jsonl_to_partitioned_hindsight_parquet.py \
    --input_file /mnt/efs/augment/user/pranay/hindsight/datasets/vanguard_permissive_skip_2024-11-01_2025-01-14/i0-vanguard0/data.jsonl.zst
"""

import argparse
import os
from pathlib import Path
from collections import defaultdict
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from research.core.utils_for_file import read_jsonl_zst

# Configuration constants
TARGET_PARTITION_SIZE = 250


def save_to_parquet(results: list[dict], parquet_filepath: Path):
    """Save a list of dictionaries to a Parquet file with ZSTD compression."""
    df = pd.DataFrame({"hindsight_problem": results})
    table = pa.Table.from_pandas(df)

    parquet_filepath.parent.mkdir(parents=True, exist_ok=True)
    pq.write_table(table, parquet_filepath, compression="zstd")


def partition_data(results: list[dict], output_base_path: str):
    """Partition data by user_id with fixed-size partitions."""
    # Dictionary to track count per user and partition data
    user_counts = defaultdict(int)
    partitioned_data = defaultdict(list)

    def filepath(partition_key):
        path = os.path.join(
            output_base_path, "partition", f"{partition_key}.zstd.parquet"
        )
        return Path(path)

    # Partition the data into fixed-size partitions based on user_id
    for record in results:
        user_id = record["completion"]["user_id"]
        partition_idx = user_counts[user_id] // TARGET_PARTITION_SIZE
        user_counts[user_id] += 1

        partition_key = f"{user_id}_{partition_idx}"
        partitioned_data[partition_key].append(record)

    # Write each partition to a Parquet file
    print(f"Writing {len(partitioned_data)} partition files")
    for partition_key, partition_records in partitioned_data.items():
        partition_path = filepath(partition_key)
        save_to_parquet(partition_records, partition_path)

    return len(partitioned_data)


def main(input_file: str):
    input_path = Path(input_file)
    if not input_path.exists():
        raise FileNotFoundError(f"Input file not found: {input_path}")

    # Create output path with the same directory structure and name, but with .zstd.parquet extension
    output_base_path = os.path.dirname(input_path)

    print(f"Reading {input_path}")
    results = read_jsonl_zst(input_path)

    print(f"Partitioning and writing data with {len(results)} records")
    num_partitions = partition_data(results, output_base_path)
    print(f"Created {num_partitions} partition files")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Convert JSONL.ZST files to Parquet files"
    )
    parser.add_argument(
        "--input_file", type=str, required=True, help="Path to the input JSONL.ZST file"
    )
    args = parser.parse_args()

    main(args.input_file)

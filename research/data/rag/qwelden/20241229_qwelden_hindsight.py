"""Data pipeline to process hindsight parquet files.

The pipeline is consist of 3 stages:
1. Retrieve line and signature chunks, recency chunks, and diff chunks.
2. Truncate the ground truth text by where the model would have predicted a stop token.
3. Generate 4 datasets depending on if recency or edit events are included in the prompt.

Usage:
python research/data/rag/qwelden/20241229_qwelden_hindsight.py --stage <stage> --tenant_name <tenant_name> --inner_dir <inner_dir>
"""

import argparse
import dataclasses
import os
import pathlib
import time
import functools
from typing import TypedDict

import pyspark.sql.dataframe as sparkDF
from pyspark.sql import functions as F

from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from research.data.spark import get_session, k8s_session
from research.core.utils_for_log import time_string
from research.data.spark.pipelines.utils import map_parquet
from research.data.rag import hindsight_common
from research.data.rag.hindsight_common import EnderDataPromptFormattingConfig
from research.data.rag import common
from research.data.spark.pipelines.stages.common import export_indexed_dataset_helper
import megatron.data.indexed_dataset as indexed_dataset
from research.data.spark.pipelines.utils.map_parquet import (
    allow_unused_args,
    passthrough_feature,
)
from research.data.utils.spark_utils import repartition_and_shuffle


class ConfigType(TypedDict):
    num_retrieved_chunks: int
    max_workers: int
    random_seed: int


# Global configuration
CONFIG = ConfigType(
    num_retrieved_chunks=32,
    max_workers=96,
    random_seed=42,
)

BASE_URI = "gs://gcp-us1-user/pranay/hindsight"
BASE_FP_URI = "/mnt/efs/augment/user/pranay/hindsight"

PARTITION_URI = "partition"  # partitioned by user_id and number of rows
RETRIEVAL_URI = "retrieval"  # retrieved chunks from line and signature retriever, edit events and recency chunks
TARGET_URI = "target"  # ground truth truncated by where the model would have predicted a stop or pause token
MIXDATA_URI = "mixdata"

TASK_INFO = "task_info"

# Config for StarEthanol. StarEthanol is used for line chunk retrieval.
SETHANOL_CONFIG = dict(
    chunker={
        "name": "transform_smart_line_level",
        "max_chunk_chars": 768,
        "max_headers": 3,
    },
    document_formatter={
        "name": "base:ethanol6-embedding-with-path-key",
        "max_tokens": 999,
        "tokenizer": "starcoder",
    },
    query_formatter={
        "name": "base:ethanol6.16.1-query-embedding",
        "max_tokens": 1023,
        "tokenizer": "starcoder",
    },
    scorer={
        "name": "dense_scorer_v2_fbwd",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/michiel/retriever/ethanol/stareth_2000s_128docactual_smartnoheader",
    },
    max_num_chunks_per_file=512,
)

# Config for Methanol. Methanol is used for signature chunk retrieval.
METHANOL_CONFIG = dict(
    scorer={
        "checkpoint_path": "/mnt/efs/augment/checkpoints/menthol/methanol_0416.4_1250/global_step1250/",
        "name": "dense_scorer_v2_fbwd_neox",
    },
    chunker={
        "name": "signature",
    },
    document_formatter={
        "add_path": False,
        "name": "simple_document",
        "max_tokens": 999,
        "tokenizer_name": "StarCoderTokenizer",
    },
    query_formatter={
        "add_path": True,
        "add_suffix": True,
        "max_tokens": 1023,
        "name": "ethanol6_query",
        "tokenizer_name": "StarCoderTokenizer",
    },
    max_num_chunks_per_file=512,
)


def retrieval_stage(data_dir: str, tenant_name: str):
    """Process the partitioned data."""
    start_time = time.time()
    input_url = os.path.join(BASE_URI, data_dir, PARTITION_URI)
    output_url = os.path.join(BASE_URI, data_dir, RETRIEVAL_URI)

    spark = k8s_session(
        name=f"retrieval-parquet-{tenant_name}",
        max_workers=CONFIG["max_workers"],
        conf={
            "spark.executor.pyspark.memory": "256G",
            "spark.executor.memory": "110G",
            "spark.executor.cores": "1",
            "spark.task.cpus": "1",
            "spark.sql.parquet.columnarReaderBatchSize": "16",
        },
        gpu_type=["h100"],
        ephemeral_storage_gb=110,
        image="us-central1-docker.pkg.dev/augment-research-gsc/docker-us-central1/augment_devpod_gpu:ubuntu22.04-cuda-12.1-py-3.11.7-pytorch-2.3.0-ngc-det-0.36.0-26-spark-3.4.3-s33-devpod12",
    )

    # Uncomment this to run on local.
    # spark = get_session()

    data = spark.read.parquet(
        *map_parquet.list_files(spark, input_url, suffix="parquet", include_path=True)
    )
    input_count = data.count()
    print(f"{time_string()} Input {input_url} has {input_count} rows.")
    data.printSchema()

    def make_retrieval_fn():
        def _create_retriever(config):
            from research.eval.harness.factories import create_retriever
            from research.retrieval.retrieval_database import RetrievalDatabase

            retrieval_database = create_retriever(config)
            assert isinstance(retrieval_database, RetrievalDatabase)
            return retrieval_database

        return hindsight_common.make_generate_retrieved_chunks_from_hindsight_problem()(
            retrieval_database_factories={
                "dense_retriever": lambda: _create_retriever(SETHANOL_CONFIG),
                "dense_signature": lambda: _create_retriever(METHANOL_CONFIG),
            },
            num_retrieved_chunks=CONFIG["num_retrieved_chunks"],
            tenant_name=tenant_name,
        )

    result = map_parquet.apply_pandas(
        spark,
        map_parquet.chain_processors(
            [
                hindsight_common.deserialize_hindsight_problem,
                hindsight_common.FunctionWrapper(make_fn=make_retrieval_fn),
            ]
        ),
        input_path=input_url,
        output_path=output_url,
        timeout=3600 * 5,
        batch_size=128,
        task_info_location=os.path.join(
            BASE_URI, data_dir, TASK_INFO, "hindsight-retrieval"
        ),
        ignore_error=False,
        allow_resume=True,
    )

    print(result["status_count"])
    print(result["task_info"]["stderr"][0])

    data = spark.read.parquet(
        *map_parquet.list_files(spark, output_url, suffix="parquet", include_path=True)
    )
    data.printSchema()

    output_count = data.count()
    print(
        f"{time_string()} Output {output_url} has {output_count} rows. Retained {output_count}/{input_count} of the input."
    )

    spark.stop()

    total_time = time.time() - start_time
    print(f"{time_string()} Total time: {total_time / 60:.1f} mins.")


# Do not use diff retriever but use recency retriever.
prompt_recency_only_config = EnderDataPromptFormattingConfig(
    max_content_len=6304,  # 6144 (max content len for services with 96 max target tokens) + (256 - 96)
    input_fraction=4 / 12,
    prefix_fraction=3 / 4,
    max_path_tokens=50,
    max_dense_signature_tokens=1024,
    max_recency_retriever_tokens=1024,
    max_diff_retriever_tokens=0,
    include_diff_retriever=False,
    max_target_tokens=256,
)


def target_stage(data_dir: str):
    """Generate the prompt that will be used to find pause or eos token in the target and return the target string as ground truth."""
    input_url = os.path.join(BASE_URI, data_dir, RETRIEVAL_URI)
    output_url = os.path.join(BASE_URI, data_dir, TARGET_URI)
    formatter_name = "elder"
    tokenizer_name = "qwen25"

    start_time = time.time()
    spark = k8s_session(
        name=f"target-parquet-{formatter_name}-{tokenizer_name}",
        max_workers=CONFIG["max_workers"],
        conf={
            "spark.executor.memory": "128G",
            "spark.executor.cores": "1",
            "spark.task.cpus": "1",
            "spark.executor.pyspark.memory": "256G",
            "spark.sql.parquet.columnarReaderBatchSize": "20",
        },
        gpu_type=["h100"],
        ephemeral_storage_gb=128,
        # image="us-central1-docker.pkg.dev/augment-research-gsc/docker-us-central1/augment_devpod_gpu:ubuntu22.04-cuda-12.1-py-3.11.7-pytorch-2.3.0-ngc-det-0.36.0-26-spark-3.4.3-s33-devpod12",
    )

    # Uncomment this to run on local.
    # spark = get_session()

    data = spark.read.parquet(
        *map_parquet.list_files(spark, input_url, suffix="parquet", include_path=True)
    )
    input_count = data.count()
    print(f"{time_string()} Input {input_url} has {input_count} rows.")

    tokenizer = Qwen25CoderTokenizer()
    if os.path.exists(output_url):
        print(f"{time_string()} {output_url} already exists, skipping.")
    else:
        print(f"{time_string()} Generating prompts for {output_url}")
        config = {
            "name": "fastforward_qwen25coder_14b",
            "checkpoint_path": "/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_smart_ffwd",
            "checkpoint_sha256": "7d3f96dcc780ffdcf0c344c5edf54ef039fe0db58a1b30fddc7753ba639a76e9",
            "sequence_length": 6600,
        }
        spark.sparkContext.setJobDescription(
            f"Creating prompts for {formatter_name} with tokenizer {tokenizer_name}"
        )
        result = map_parquet.apply_pandas(
            spark,
            map_parquet.chain_processors(
                [
                    passthrough_feature()(
                        allow_unused_args()(
                            hindsight_common.ground_truth_to_middle_spans(
                                stop_at_end=True
                            )
                        )
                    ),
                    passthrough_feature()(
                        allow_unused_args()(
                            hindsight_common.generate_ender_prompt_fn(
                                tokenizer=tokenizer,
                                config=prompt_recency_only_config,
                            )
                        )
                    ),
                    passthrough_feature()(
                        lambda **kwargs: hindsight_common.add_middle_spans_to_prompt_tokens(
                            tokenizer=tokenizer,
                            config=prompt_recency_only_config,
                            prompt_tokens=kwargs.get("prompt_tokens", []),
                            middle_spans=kwargs.get("middle_spans", b""),
                            request_id=kwargs.get("request_id", ""),
                        )
                    ),
                    passthrough_feature()(
                        allow_unused_args()(
                            hindsight_common.TruncateTargetTokens(
                                tokenizer=tokenizer,
                                max_target_tokens=prompt_recency_only_config.max_target_tokens,
                                config=config,
                            ).__call__
                        )
                    ),
                    allow_unused_args()(hindsight_common.keep_essential_features_only),
                ]
            ),
            input_path=input_url,
            output_path=output_url,
            task_info_location=os.path.join(
                BASE_URI,
                data_dir,
                TASK_INFO,
                f"target_{formatter_name}_{tokenizer_name}",
            ),
            ignore_error=False,
            allow_resume=True,
            timeout=3600 * 3,
            batch_size=128,
        )
        print(result["status_count"])
        print(result["task_info"]["stderr"][0])

    data = spark.read.parquet(
        *map_parquet.list_files(spark, output_url, suffix="parquet", include_path=True)
    )
    data.printSchema()

    output_count = data.count()
    print(
        f"{time_string()} Output {output_url} has {output_count} rows. Retained {output_count}/{input_count} of the input."
    )

    spark.stop()

    total_time = time.time() - start_time
    print(f"{time_string()} Total time: {total_time / 60:.1f} mins.")


@dataclasses.dataclass
class DatasetConfig:
    """Configuration for a dataset to include in the mixup."""

    name: str
    """Name of the dataset."""

    directories: list[str]
    """List of directory paths containing parts of the same dataset."""

    stage: str
    """Stage to read from. Usually either retrieval or target."""

    use_hindsight_ground_truth: bool
    """Whether to use ground truth (True) or model output (False) as target."""

    percentage: float | None = None
    """
    If set, include this percentage of the total dataset.
    If None, this dataset will fill the remaining percentage.
    """


@dataclasses.dataclass
class MixConfig:
    """Configuration for a dataset mix with percentage-based datasets."""

    datasets: list[DatasetConfig]
    """List of datasets to include in the mix."""

    @property
    def main_datasets(self) -> list[DatasetConfig]:
        """Returns the main datasets (those with percentage=None)."""
        return [d for d in self.datasets if d.percentage is None]

    @property
    def total_specified_percentage(self) -> float:
        """Returns the sum of all specified percentages."""
        return sum(d.percentage or 0 for d in self.datasets)


# Main dataset with no percentage limit
vanguard_hindsight_dataset = DatasetConfig(
    name="hindsight",
    directories=["datasets/vanguard_hindsight_2024-11-01_2025-01-14/i0-vanguard0"],
    stage="target",
    use_hindsight_ground_truth=True,
    percentage=None,
)

vanguard_permissive_hindsight_dataset = DatasetConfig(
    name="permissive_hindsight",
    directories=[
        "datasets/vanguard_permissive_hindsight_2024-11-01_2025-01-14/i0-vanguard0"
    ],
    stage="target",
    use_hindsight_ground_truth=True,
    percentage=None,
)

# Percentage-based datasets
vanguard_skip_dataset = DatasetConfig(
    name="skip",
    directories=["datasets/vanguard_skip_2024-11-01_2025-01-14/i0-vanguard0"],
    stage="retrieval",
    use_hindsight_ground_truth=False,
    percentage=0.034,
)

vanguard_permissive_skip_dataset = DatasetConfig(
    name="permissive_skip",
    directories=[
        "datasets/vanguard_permissive_skip_2024-11-01_2025-01-14/i0-vanguard0"
    ],
    stage="retrieval",
    use_hindsight_ground_truth=False,
    percentage=0.034,
)

# Dictionary of mix configurations
MIX_CONFIGS = {
    "datasets/vanguard_mix_2024-11-01_2025-01-14/i0-vanguard0": MixConfig(
        datasets=[
            vanguard_hindsight_dataset,
            DatasetConfig(
                name="empty",
                directories=[
                    "datasets/vanguard_empty_2024-11-01_2025-01-14/i0-vanguard0"
                ],
                stage="retrieval",
                use_hindsight_ground_truth=False,
                percentage=0.34,
            ),
            vanguard_skip_dataset,
        ],
    ),
    "datasets/vanguard_permissive_mix_empty10_2024-11-01_2025-01-14/i0-vanguard0": MixConfig(
        datasets=[
            vanguard_permissive_hindsight_dataset,
            DatasetConfig(
                name="empty",
                directories=[
                    "datasets/vanguard_permissive_empty_2024-11-01_2025-01-14/i0-vanguard0"
                ],
                stage="retrieval",
                use_hindsight_ground_truth=False,
                percentage=0.1,
            ),
            vanguard_permissive_skip_dataset,
        ],
    ),
    "datasets/vanguard_permissive_mix_empty15_2024-11-01_2025-01-14/i0-vanguard0": MixConfig(
        datasets=[
            vanguard_permissive_hindsight_dataset,
            DatasetConfig(
                name="empty",
                directories=[
                    "datasets/vanguard_permissive_empty_2024-11-01_2025-01-14/i0-vanguard0"
                ],
                stage="retrieval",
                use_hindsight_ground_truth=False,
                percentage=0.15,
            ),
            vanguard_permissive_skip_dataset,
        ],
    ),
    "datasets/vanguard_permissive_mix_empty20_2024-11-01_2025-01-14/i0-vanguard0": MixConfig(
        datasets=[
            vanguard_permissive_hindsight_dataset,
            DatasetConfig(
                name="empty",
                directories=[
                    "datasets/vanguard_permissive_empty_2024-11-01_2025-01-14/i0-vanguard0"
                ],
                stage="retrieval",
                use_hindsight_ground_truth=False,
                percentage=0.2,
            ),
            vanguard_permissive_skip_dataset,
        ],
    ),
    "datasets/vanguard_permissive_mix_empty25_2024-11-01_2025-01-14/i0-vanguard0": MixConfig(
        datasets=[
            vanguard_permissive_hindsight_dataset,
            DatasetConfig(
                name="empty",
                directories=[
                    "datasets/vanguard_permissive_empty_2024-11-01_2025-01-14/i0-vanguard0"
                ],
                stage="retrieval",
                use_hindsight_ground_truth=False,
                percentage=0.25,
            ),
            vanguard_permissive_skip_dataset,
        ],
    ),
    "datasets/vanguard_permissive_mix_empty30_2024-11-01_2025-01-14/i0-vanguard0": MixConfig(
        datasets=[
            vanguard_permissive_hindsight_dataset,
            DatasetConfig(
                name="empty",
                directories=[
                    "datasets/vanguard_permissive_empty_2024-11-01_2025-01-14/i0-vanguard0"
                ],
                stage="retrieval",
                use_hindsight_ground_truth=False,
                percentage=0.3,
            ),
            vanguard_permissive_skip_dataset,
        ],
    ),
}


def mixdata_stage(mix_config_name: str):
    """Mixup different subsets of data into a single dataset.

    1. Read all datasets and count samples
    2. Calculate target samples for each dataset. Main datasets get all their samples, while percentage-based datasets get a fraction of their samples.
    3. Calculate sampling fraction for each dataset.
    4. Perform sampling from each dataset and add use_hindsight_ground_truth column
    5. Assert that we got the right number of samples

    Adds new column use_hindsight_ground_truth that indicates whether to use hindsight ground truth or inference response as final target."""
    start_time = time.time()
    spark = k8s_session(
        name=f"mixdata-parquet-{mix_config_name}",
        max_workers=16,
        conf={
            "spark.executor.memory": "128G",  # actual memory
            "spark.executor.cores": "8",  # num cores requested per worker
            "spark.task.cpus": "1",
            # "spark.executor.pyspark.memory": "256G", # virtual memory
            "spark.sql.parquet.columnarReaderBatchSize": "20",
        },
        ephemeral_storage_gb=128,
    )

    if mix_config_name not in MIX_CONFIGS:
        raise ValueError(f"{mix_config_name} is not in {list(MIX_CONFIGS)}")
    mix_config = MIX_CONFIGS[mix_config_name]

    # 1. Load all dataframes and count samples
    dataframes_by_dataset: dict[str, sparkDF.DataFrame] = dict()
    num_samples_by_dataset: dict[str, int] = {}
    for dataset in mix_config.datasets:
        combined_df = None
        total_samples = 0

        for directory in dataset.directories:
            input_url = os.path.join(BASE_URI, directory, dataset.stage)
            df = spark.read.parquet(os.path.join(input_url, "*zstd.parquet"))
            samples = df.count()
            total_samples += samples
            print(f"{time_string()} Input {input_url} has {samples} rows.")

            if combined_df is None:
                combined_df = df
            else:
                combined_df = combined_df.union(df)

        assert combined_df is not None, f"Failed to load data for {dataset.name}"
        dataframes_by_dataset[dataset.name] = combined_df
        num_samples_by_dataset[dataset.name] = total_samples

    # 2. Calculate target samples for each dataset. Main datasets get all their samples, while percentage-based datasets get a fraction of their samples.
    remaining_percentage = 1.0 - mix_config.total_specified_percentage
    assert (
        remaining_percentage > 0
    ), f"Insufficient remaining percentage for main dataset. Got {remaining_percentage:.2f}"
    print(
        f"{time_string()} Remaining percentage for main dataset: {remaining_percentage:.2f}"
    )

    total_main_samples = sum(
        num_samples_by_dataset[d.name] for d in mix_config.main_datasets
    )
    assert (
        total_main_samples > 0
    ), f"Insufficient samples for main datasets. Found {total_main_samples}"
    target_final_dataset_size = int(total_main_samples / remaining_percentage)

    # For datasets with specified percentage, calculate target samples. For main datasets, use all available samples.
    target_samples_by_dataset: dict[str, int] = {}
    for dataset in mix_config.datasets:
        if dataset.percentage is not None:
            # Calculate target samples based on final dataset size
            target_samples = int(target_final_dataset_size * dataset.percentage)
            target_samples_by_dataset[dataset.name] = target_samples
            print(
                f"{time_string()} Target samples for {dataset.name}: {target_samples}/{num_samples_by_dataset[dataset.name]}, which is {target_samples/target_final_dataset_size} of final dataset ({dataset.percentage:.3f})"
            )
            assert (
                num_samples_by_dataset[dataset.name] >= target_samples
            ), f"Insufficient samples for {dataset.directories}. Found {num_samples_by_dataset[dataset.name]} but need {target_samples}"
        else:
            target_samples_by_dataset[dataset.name] = num_samples_by_dataset[
                dataset.name
            ]
            print(
                f"{time_string()} Target samples for {dataset.name}: {num_samples_by_dataset[dataset.name]} (main dataset)"
            )

    # 3. Calculate sampling fraction for each dataset. Sampling will require a percentage.
    sampling_fraction_by_dataset = {}
    for dataset_name, target_samples in target_samples_by_dataset.items():
        available_samples = num_samples_by_dataset[dataset_name]
        sampling_fraction = min(1.0, target_samples / available_samples)
        sampling_fraction_by_dataset[dataset_name] = sampling_fraction

    # 4. Sample from each dataset and add new column
    union_dfs: dict[str, sparkDF.DataFrame] = {}
    for dataset in mix_config.datasets:
        sampling_fraction = sampling_fraction_by_dataset[dataset.name]
        sampled_dataframe = dataframes_by_dataset[dataset.name].sample(
            withReplacement=False,
            fraction=sampling_fraction,
            seed=CONFIG["random_seed"],
        )
        sampled_dataframe = sampled_dataframe.withColumn(
            "use_hindsight_ground_truth",
            F.lit(dataset.use_hindsight_ground_truth),
        )
        union_dfs[dataset.name] = sampled_dataframe

    union_df = functools.reduce(
        lambda df1, df2: df1.unionByName(df2, allowMissingColumns=True),
        union_dfs.values(),
    )
    total_samples = union_df.count()

    # 5. Assert that we got the right number of samples
    for dataset in mix_config.datasets:
        inital_samples = num_samples_by_dataset[dataset.name]
        target_samples = target_samples_by_dataset[dataset.name]
        actual_samples = union_dfs[dataset.name].count()

        # If this is a main dataset, we should have gotten all the samples. Otherwise, we should be within 1% of the expected ratio.
        if dataset.percentage is None:
            print(
                f"{time_string()} Samples for main dataset {dataset.name}: {actual_samples}/{target_samples} (expected 1.00)"
            )
            assert (
                actual_samples == target_samples
            ), f"{time_string()} Failed to get all samples for main dataset {dataset.name}. Got {actual_samples}/{target_samples} samples."
        else:
            actual_ratio = actual_samples / total_samples
            expected_ratio = dataset.percentage
            print(
                f"{time_string()} Ratio of final dataset for {dataset.name}: {actual_ratio:.3f} (expected {expected_ratio:.3f}). Requested {actual_samples} samples from initial sample count {inital_samples}, got {actual_samples} after sampling. Final dataset has {total_samples} samples."
            )
            assert (
                abs(actual_ratio - expected_ratio) < 0.01
            ), f"{time_string()} Actual ratio {actual_ratio:.3f} is too far from expected ratio {expected_ratio:.3f} for {dataset.name}. Got {actual_samples=}/{target_samples=} samples out of target. Final dataset has {total_samples=} samples."

    # DO NOT USE repartition_and_shuffle, it can not handle TB level data......!
    # union_df = repartition_and_shuffle(random_seed=random_seed, df=union_df)
    mix_output_url = os.path.join(BASE_URI, mix_config_name, MIXDATA_URI)
    assert union_df is not None
    union_df.write.mode("overwrite").parquet(mix_output_url)
    union_df = spark.read.parquet(mix_output_url)
    print(f"{time_string()} Union has {union_df.count()} rows in {mix_output_url}.")
    spark.stop()
    print(f"{time_string()} Time elapsed: {(time.time() - start_time)/60:.1f} mins.")


def prompt_stage(mix_config_name: str):
    """Generate the prompts and target tokens."""

    if mix_config_name not in MIX_CONFIGS:
        raise ValueError(f"{mix_config_name} is not in {list(MIX_CONFIGS)}")

    start_time = time.time()
    spark = k8s_session(
        name=f"prompts-parquet-{mix_config_name}",
        max_workers=16,
        conf={
            "spark.executor.memory": "128G",  # actual memory
            "spark.executor.cores": "8",  # num cores requested per worker
            "spark.task.cpus": "1",
            # "spark.executor.pyspark.memory": "256G", # virtual memory
            "spark.sql.parquet.columnarReaderBatchSize": "20",
        },
        ephemeral_storage_gb=128,
        # image="us-central1-docker.pkg.dev/augment-research-gsc/docker-us-central1/augment_devpod_gpu:ubuntu22.04-cuda-12.1-py-3.11.7-pytorch-2.3.0-ngc-det-0.36.0-26-spark-3.4.3-s33-devpod12",
    )

    input_url = os.path.join(BASE_URI, mix_config_name, MIXDATA_URI)

    formatter = "elder"
    tokenizer_name = "qwen25"
    tokenizer = Qwen25CoderTokenizer()

    # Uncomment this to run on local.
    # spark = get_session()

    def generate_and_save_prompts(
        prompt_dir_name: str,
        dataset_dir_name: str,
        config: EnderDataPromptFormattingConfig,
    ):
        output_url = os.path.join(BASE_URI, mix_config_name, prompt_dir_name)
        if os.path.exists(os.path.join(BASE_FP_URI, mix_config_name, prompt_dir_name)):
            print(f"{time_string()} {output_url} already exists, skipping.")
        else:
            print(f"{time_string()} Generating prompts for {output_url}")
            spark.sparkContext.setJobDescription(
                f"Creating prompts for {formatter} with tokenizer {tokenizer_name}"
            )
            result = map_parquet.apply_pandas(
                spark,
                map_parquet.chain_processors(
                    [
                        passthrough_feature()(
                            allow_unused_args()(
                                hindsight_common.generate_ender_prompt_fn(
                                    tokenizer=tokenizer,
                                    config=config,
                                )
                            )
                        ),
                        # Do not pass through features as they are not needed.
                        allow_unused_args()(
                            hindsight_common.add_target_to_prompt_tokens_fn(
                                tokenizer=tokenizer,
                                config=config,
                            )
                        ),
                        passthrough_feature()(
                            allow_unused_args()(
                                common.create_pad_pack_tokens_fn(
                                    seq_len=config.max_content_len,
                                    tokenizer=tokenizer,
                                )
                            )
                        ),
                    ]
                ),
                input_path=input_url,
                output_path=output_url,
                task_info_location=os.path.join(
                    BASE_URI,
                    mix_config_name,
                    TASK_INFO,
                    prompt_dir_name,
                    f"prompts_{formatter}_{tokenizer_name}",
                ),
                ignore_error=False,
                timeout=3600,
                batch_size=128,
            )

            print(result["status_count"])
            print(result["task_info"]["stderr"][0])
            df = spark.read.parquet(
                *map_parquet.list_files(
                    spark, output_url, suffix="parquet", include_path=True
                )
            ).select("prompt_tokens")
            df = repartition_and_shuffle(CONFIG["random_seed"], df)
            total_data = df.count()
            print(
                f"{time_string()} [Prompt: {prompt_dir_name}]: generated {total_data} rows in {output_url}."
            )
            spark.sparkContext.setJobDescription(
                f"Creating indexed dataset: {dataset_dir_name}"
            )
            output_dataset_url = os.path.join(
                BASE_FP_URI, mix_config_name, dataset_dir_name
            )
            export_indexed_dataset_helper(
                df,
                vocab_size=tokenizer.vocab_size,
                samples_column="prompt_tokens",
                num_validation_samples=0,
                indexed_dataset_path=pathlib.Path(output_dataset_url),
                filter_by_langs=None,
            )
            dataset = indexed_dataset.MMapIndexedDataset(
                os.path.join(output_dataset_url, "dataset"), skip_warmup=True
            )
            print(
                f"{time_string()} [Dataset: {dataset_dir_name}]: generated in {output_dataset_url} with {len(dataset)} samples."
            )

    def generate_configs():
        input_fractions = {6304: 4 / 12, 7328: 4 / 14, 8352: 4 / 16}
        configs = {}

        specific_configs = [
            {"max_content_len": 8352, "signature": 1024, "recency": 1024, "diff": 2048},
            {"max_content_len": 6304, "signature": 1024, "recency": 1024, "diff": 0},
            {"max_content_len": 7328, "signature": 1024, "recency": 0, "diff": 2048},
        ]
        for config in specific_configs:
            max_content_len = config["max_content_len"]
            signature_tokens = config["signature"]
            recency_tokens = config["recency"]
            diff_tokens = config["diff"]
            # Estimate the minimum number of line tokens as it depends on the other components.
            line_tokens = round(
                (max_content_len - 256) * (1 - input_fractions[max_content_len])
                - (signature_tokens + recency_tokens + diff_tokens)
            )

            name = f"{max_content_len}b_{signature_tokens}s_{recency_tokens}r_{diff_tokens}d_{line_tokens}l"

            configs[name] = EnderDataPromptFormattingConfig(
                max_content_len=max_content_len,
                input_fraction=input_fractions[config["max_content_len"]],
                prefix_fraction=3 / 4,
                max_path_tokens=50,
                max_dense_signature_tokens=signature_tokens,
                max_recency_retriever_tokens=recency_tokens,
                max_diff_retriever_tokens=diff_tokens,
                include_diff_retriever=diff_tokens > 0,
                max_target_tokens=256,
            )

        print(f"Generated {len(configs)} configs")

        return configs

    configs = generate_configs()
    prompts_uri = {key: f"prompts/{key}" for key in configs.keys()}
    dataset_uri = {key: f"datasets/{key}" for key in configs.keys()}

    for key, config in configs.items():
        generate_and_save_prompts(
            prompts_uri[key],
            dataset_uri[key],
            config,
        )

    spark.stop()

    total_time = time.time() - start_time
    print(f"{time_string()} Total time: {total_time / 60:.1f} mins.")


def main(
    stage: str, tenant_name: str, inner_dir: str | None, mix_config_name: str | None
):
    """Main function to run the specified stage.

    # NOTE: This pipeline is not generalized due to tokenizer hardcoding.
    # The tokenizer is specifically set to Qwen25CoderTokenizer and cannot be easily changed.

    Args:
        stage: Stage to run (retrieval, target, mixup, prompt)
        tenant_name: Name of the tenant
        inner_dir: Inner directory name
        mix_config_name: Name of the mix configuration to use for mixup stage
    """
    if stage == "retrieval":
        assert inner_dir is not None
        retrieval_stage(data_dir=inner_dir, tenant_name=tenant_name)
    elif stage == "target":
        assert inner_dir is not None
        target_stage(data_dir=inner_dir)
    elif stage == "mixup":
        assert mix_config_name is not None
        mixdata_stage(mix_config_name=mix_config_name)
    elif stage == "prompt":
        assert mix_config_name is not None
        prompt_stage(mix_config_name=mix_config_name)
    else:
        raise ValueError(f"Unknown stage: {stage}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Partition and process parquet files")
    parser.add_argument(
        "--stage",
        type=str,
        choices=["retrieval", "target", "mixup", "prompt"],
        required=True,
        help="Stage to run",
    )

    parser.add_argument(
        "--tenant_name",
        type=str,
        choices=["dogfood-shard", "i0-vanguard0"],
        required=True,
        help="Tenant name",
    )

    parser.add_argument(
        "--inner_dir",
        type=str,
        default=None,
        required=False,
        help="Inner directory name after <base_path>/user/pranay/hindsight/<inner_dir>",
    )

    parser.add_argument(
        "--mix_config_name",
        type=str,
        choices=list(MIX_CONFIGS.keys()),
        default=None,
        required=False,
        help="Name of the mix configuration to use for mixup and prompt stage",
    )

    args = parser.parse_args()

    main(args.stage, args.tenant_name, args.inner_dir, args.mix_config_name)

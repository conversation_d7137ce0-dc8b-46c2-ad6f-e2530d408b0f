metadata:
  name: # Set by gen_ray_cluster_spec.py to {cluster_name}-worker-pod
  namespace: # Managed by <PERSON><PERSON><PERSON>ay operator based on RayCluster deployment namespace
  labels: # KubeRay will add additional labels for Ray cluster management
    app: ray
    function: ray-worker
spec:
  enableServiceLinks: false
  serviceAccountName: # Set by gen_ray_cluster_spec.py based on cluster (ray or cw-east4-ray)
  tolerations: [] # Set by gen_ray_cluster_spec.py, can be extended for GPU node tolerations
  initContainers:
    - name: prepare-deps
      image: # Set by create_template() based on --image argument
      command: # Set by create_template() based on cluster configuration
      resources:
        requests:
          cpu: "8"
          memory: "32Gi"
      volumeMounts: # Filtered by allowed_volume_mount() to include only permitted volumes
      - mountPath: /mnt/ephemeral/ram
        name: local-ramdisk
  containers:
    - name: ray-worker
      image: # Set to match initContainer image by gen_ray_cluster_spec.py
      command:
        - echo "Hello Ray!"  # Placeholder - KubeRay will override with ray start command
      imagePullPolicy: Always
      env: # CUDA/NCCL env vars added when --gpu-count > 0
      resources: # Modified by create_template() to add GPU resources and ephemeral storage
        limits:
          ephemeral-storage:  # Set by create_template() based on ephemeral_storage_gb
        requests:
          ephemeral-storage:  # Set by create_template() based on ephemeral_storage_gb
      volumeMounts: # Filtered by allowed_volume_mount() to include only permitted volumes
      - mountPath: /mnt/ephemeral/ram
        name: local-ramdisk
      securityContext:
        runAsUser: 0
  volumes: # Filtered by allowed_volume() to include only permitted buckets
  - name: local-ramdisk
    emptyDir:
      medium: Memory
      sizeLimit: 500Mi
  affinity: # Set by create_template() based on cluster type and GPU requirements

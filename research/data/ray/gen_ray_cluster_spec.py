import argparse
import os
from pathlib import Path
import secrets
import sys
from typing import Any

import yaml
import ray

from research.data.spark.infra.template_utils import create_template
from research.infra.cfg.clusters import Clusters


# Buckets granted permission in the Google/Kubernetes service account `ray`/`cw-east4-ray`
ALLOWED_BUCKETS = ["gcp-us1-checkpoints", "gcp-us1-user"]


CW_RAY_KEY_VOLUME_NAME = "ray-secret"  # pragma: allowlist secret
CW_RAY_KEY_SECRET_NAME = "cw-east4-ray-key"  # pragma: allowlist secret
DEFAULT_GCS_PROJECT = "augment-research-gsc"


def allowed_volume(volume: dict[str, Any]) -> bool:
    if "csi" not in volume:
        return True
    if volume["csi"]["volumeAttributes"]["bucketName"] in ALLOWED_BUCKETS:
        return True
    return False


def allowed_volume_mount(volume_mount: dict[str, Any]) -> bool:
    return volume_mount["name"] in ["local-ramdisk", "aug-checkpoints", "aug-user"]


def create_pod_template_spec(
    cluster: str,  # either "gcp-us1" or "cw-east4"
    cluster_name: str,
    image: str,
    gpu_type: str,
    gpu_count: int,
    is_head: bool = False,
) -> dict[str, Any]:
    # Create a pod template for Ray workers
    template_file = create_template(
        source=Path(__file__).parent / "worker-template.yaml",
        image=image,
        shared_folder=None,
        region="",
        gpu_type=gpu_type,
        gpu_count=gpu_count if not is_head else 0,  # Head node does not need GPUs
        ephemeral_storage_gb=32,
    )
    pod_template_spec = yaml.safe_load(template_file.read_text())
    pod_template_spec["metadata"]["name"] = (
        f"{cluster_name}-worker-pod" if not is_head else f"{cluster_name}-head-pod"
    )
    pod_template_spec["spec"]["containers"][0]["image"] = pod_template_spec["spec"][
        "initContainers"
    ][0]["image"]
    pod_template_spec["spec"]["tolerations"] = []

    if cluster == "gcp-us1":
        pod_template_spec["spec"]["serviceAccountName"] = "ray"

    pod_template_spec["spec"]["volumes"] = [
        volume
        for volume in pod_template_spec["spec"]["volumes"]
        if allowed_volume(volume)
    ]
    pod_template_spec["spec"]["containers"][0]["volumeMounts"] = [
        volume_mount
        for volume_mount in pod_template_spec["spec"]["containers"][0]["volumeMounts"]
        if allowed_volume_mount(volume_mount)
    ]
    pod_template_spec["spec"]["initContainers"][0]["volumeMounts"] = [
        volume_mount
        for volume_mount in pod_template_spec["spec"]["initContainers"][0][
            "volumeMounts"
        ]
        if allowed_volume_mount(volume_mount)
    ]

    if cluster == "cw-east4":
        match_expressions = pod_template_spec["spec"]["affinity"]["nodeAffinity"][
            "requiredDuringSchedulingIgnoredDuringExecution"
        ]["nodeSelectorTerms"][0]["matchExpressions"]
        for m in match_expressions:
            if m["key"] == "node.coreweave.cloud/reserved":
                m["values"] = [
                    "augment",
                    "a64a15",
                ]  # The latter is the name found in cw-east4

        # Inject secret key for Ray
        pod_template_spec["spec"]["volumes"].append(  # pragma: allowlist secret
            {
                "name": CW_RAY_KEY_VOLUME_NAME,  # pragma: allowlist secret
                "secret": {  # pragma: allowlist secret
                    "secretName": CW_RAY_KEY_SECRET_NAME,  # pragma: allowlist secret
                },
            }
        )
        ray_secret_volume_mount = {
            "name": CW_RAY_KEY_VOLUME_NAME,
            "mountPath": "/etc/ray-secret",
            "readOnly": True,
        }
        pod_template_spec["spec"]["containers"][0]["volumeMounts"].append(
            ray_secret_volume_mount
        )
        pod_template_spec["spec"]["initContainers"][0]["volumeMounts"].append(
            ray_secret_volume_mount
        )

    if args.gpu_count > 0:
        # Pass some CUDA/NCCL environment variables to the container
        pod_template_spec["spec"]["containers"][0]["env"] = [
            {"name": "LD_LIBRARY_PATH", "value": "/usr/local/nvidia/lib64"},
            *[
                {"name": key, "value": value}
                for key, value in os.environ.items()
                if key.startswith("NCCL_")
            ],
        ]

    if cluster == "cw-east4":
        pod_template_spec["spec"]["containers"][0]["env"].append(
            {
                "name": "GOOGLE_APPLICATION_CREDENTIALS",
                "value": "/etc/ray-secret/key.json",
            }
        )  # Mount the Google Cloud auth secret key for Ray
        pod_template_spec["spec"]["containers"][0]["env"].append(
            {
                "name": "GOOGLE_CLOUD_PROJECT",
                "value": DEFAULT_GCS_PROJECT,
            }
        )  # Set the default project for the Google Cloud CLI

    return pod_template_spec


def create_ray_cluster_spec(
    name: str,
    head_pod_template_spec: dict[str, Any],
    worker_pod_template_spec: dict[str, Any],
    replicas: int = 1,
) -> dict:
    ray_cluster_spec = {
        "rayVersion": ray.__version__,
        "headGroupSpec": {
            "serviceType": "ClusterIP",
            "rayStartParams": {
                "dashboard-host": "0.0.0.0",
                "num-cpus": "0",  # Head node does not run tasks
            },
            "template": head_pod_template_spec,
        },
        "workerGroupSpecs": [
            {
                "groupName": f"{name}-worker",
                "replicas": replicas,
                "template": worker_pod_template_spec,
                "rayStartParams": {},
            },
        ],
    }
    ray_cluster = {
        "apiVersion": "ray.io/v1",
        "kind": "RayCluster",
        "metadata": {
            "name": name,
        },
        "spec": ray_cluster_spec,
    }
    return ray_cluster


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--name",
        type=str,
        default=f"ray-cluster-{secrets.token_urlsafe(6).lower().replace('_', '')}",
    )
    parser.add_argument("--replicas", type=int, default=8)
    parser.add_argument(
        "--image",
        type=str,
        default=None,
    )
    parser.add_argument(
        "--gpu-type", type=str, default="nvidia-h100-mega-80gb"
    )  # H100_NVLINK_80GB
    parser.add_argument("--gpu-count", type=int, default=1)
    args = parser.parse_args()

    cluster = Clusters.detect_cluster()
    print(f"Detected cluster: {cluster}", file=sys.stderr)

    if args.image is None:
        args.image = Clusters.load_current().images["devpod_gpu"]

    head_pod_template_spec = create_pod_template_spec(
        cluster, args.name, args.image, args.gpu_type, args.gpu_count, is_head=True
    )
    worker_pod_template_spec = create_pod_template_spec(
        cluster, args.name, args.image, args.gpu_type, args.gpu_count, is_head=False
    )
    ray_cluster_spec = create_ray_cluster_spec(
        args.name,
        head_pod_template_spec,
        worker_pod_template_spec,
        replicas=args.replicas,
    )
    print(yaml.dump(ray_cluster_spec))

### Ray in Augment

This directory contains scripts for launching Ray clusters in Augment.

First, we need to generate the Ray cluster spec. The script `gen_ray_cluster_spec.py` does this.
```sh
python gen_ray_cluster_spec.py \
  --name $cluster_name \
  --replicas 8 \
  --gpu-type nvidia-h100-mega-80gb \
  --gpu-count 1 \
  > ray-cluster-spec.yaml
```

Then, we can launch the cluster with `kubectl`.
```sh
kubectl apply -f ray-cluster-spec.yaml
```

To get the Ray head IP and port, we can use `kubectl` again.
```sh
export RAY_ADDRESS=$( \
  kubectl get raycluster $cluster_name -o json \
  | jq -r '"http://\(.status.head.podIP):\(.status.endpoints.dashboard)"' \
)
```

Now we can submit Ray jobs to the cluster. The script `submit_ray.py` does this.
The script will zip the entire `augment` directory and upload it to GCS.
Then it will submit a Ray job to the cluster with the zipped directory as the working directory.
This is because our repo is too large to be directly uploadable to <PERSON>.

```sh
python submit_ray.py \
  --ray-address $RAY_ADDRESS \
  -- \
  python my_script.py \
    --arg1 value1 \
    --arg2 value2
```

When you are done with the cluster, you can delete it with `kubectl`.
```sh
kubectl delete raycluster $cluster_name
```

### Viewing the Ray dashboard

The easiest way I found is log into a devpod with Docker access,
and view it with [Carbonyl](https://github.com/fathyb/carbonyl). Carbonyl is a headless browser in terminals.
```sh
docker run --rm -ti fathyb/carbonyl $RAY_ADDRESS
```

import argparse
import asyncio
import os
import subprocess
import time
import uuid
from termcolor import colored

from ray.dashboard.modules.job.common import JobStatus
from ray.job_submission import JobSubmissionClient

from research.core.constants import AUGMENT_ROOT


# Configuration constants
DEFAULT_RAY_GCS_BUCKET = "gcp-us1-user"


def run_command(cmd: list[str], cwd: str = AUGMENT_ROOT):
    print(f"Running command: {colored(' '.join(cmd), 'yellow')}")
    subprocess.run(
        cmd,
        check=True,
        text=True,
        cwd=cwd,
        stdout=subprocess.DEVNULL,
        stderr=subprocess.DEVNULL,
    )


async def stream_logs(submission_id: str):
    try:
        async for lines in client.tail_job_logs(submission_id):
            print(lines)
    except Exception as e:
        print(f"Error streaming logs: {e}")
        raise e
    finally:
        print("Log streaming stopped")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--ray-address", type=str, required=True)
    parser.add_argument("--job-id", type=str, required=False)
    parser.add_argument(
        "--gcs-bucket",
        type=str,
        default=DEFAULT_RAY_GCS_BUCKET,
        help=f"GCS bucket for storing Ray job artifacts (default: {DEFAULT_RAY_GCS_BUCKET})",
    )
    args, remaining_args = parser.parse_known_args()

    # After "--", all arguments are passed to the entrypoint
    if "--" in remaining_args:
        idx = remaining_args.index("--")
        remaining_args = remaining_args[idx + 1 :]
        remaining_args = [r if r.startswith("--") else f'"{r}"' for r in remaining_args]
        args.entrypoint = " ".join(remaining_args)
    else:
        raise ValueError("Entrypoint not specified")
    print(f"Entrypoint: {args.entrypoint}")

    os.chdir(AUGMENT_ROOT)  # Zip has to be run from the root directory
    # Zip all files in AUGMENT_ROOT to gs://{bucket}/ray/augment-$src_zip_id.zip
    # This is because our repo is too large to be directly uploadable to Ray

    src_zip_id = uuid.uuid4()
    zip_gcs_path = f"gs://{args.gcs_bucket}/ray/augment-{src_zip_id}.zip"

    excluded_patterns = [
        ".git/*",
        "*.ipynb",
        ".idea/*",
        "bazel-augment/*",
        "bazel-bin/*",
        "bazel-out/*",
        "*.npy",
    ]
    excluded_pattern_cli_options = [
        t for pattern in excluded_patterns for t in ["-x", pattern]
    ]
    temp_zip_path = f"/tmp/augment-{src_zip_id}.zip"
    try:
        run_command(
            [
                "zip",
                "-r",
                temp_zip_path,
                ".",
            ]
            + excluded_pattern_cli_options
        )
        run_command(
            [
                "gcloud",
                "storage",
                "cp",
                temp_zip_path,
                zip_gcs_path,
            ]
        )
    finally:
        # Clean up the temporary zip file
        if os.path.exists(temp_zip_path):
            try:
                os.remove(temp_zip_path)
                print(f"Cleaned up temporary file: {temp_zip_path}")
            except Exception as e:
                print(
                    f"Warning: Failed to clean up temporary file {temp_zip_path}: {e}"
                )

    # Get all environment variables starting with NCCL_
    nccl_env_vars = {k: v for k, v in os.environ.items() if k.startswith("NCCL_")}

    client = JobSubmissionClient(args.ray_address)
    submission_id = client.submit_job(
        entrypoint=args.entrypoint,
        runtime_env={
            "working_dir": zip_gcs_path,
            "entrypoint": args.entrypoint,
            "pip": f"{AUGMENT_ROOT}/research/requirements.txt",  # this path is local, not relative to working_dir
            "env_vars": {
                "LD_LIBRARY_PATH": "/usr/local/nvidia/lib64",  # Very important for NCCL to work
                "PYTHONPATH": ".:./research/gpt-neox",  # Make sure that gpt-neox is in the PYTHONPATH
                **nccl_env_vars,
            },
        },
    )
    print(f"Submitted job {submission_id}")
    asyncio.run(stream_logs(submission_id))

import gcsfs
import tempfile
from abc import ABC, abstractmethod
from typing import Any, <PERSON>ric, TypeVar, BinaryIO
from collections.abc import Iterable

from termcolor import colored

import ray
import os
from pathlib import Path

from dataclasses_json import DataClassJsonMixin
from google.cloud import storage
from ray.data import Datasink
from ray.types import ObjectRef
from ray.data._internal.execution.interfaces import TaskContext
from ray.data.block import Block
from ray.data.datasource import WriteReturnType
from tqdm import tqdm

from research.determined_utils import augment_fs_path_for_bucket


# Configuration constants
DEFAULT_GCS_PROJECT = "augment-research-gsc"
PARTITIONS_PER_WORKER = (
    4  # Number of partitions per worker to allow for better load balancing
)

Input = TypeVar(
    "Input", bound=DataClassJsonMixin
)  # ensure the input is json-serializable
Output = TypeVar(
    "Output", bound=DataClassJsonMixin
)  # ensure the output is json-serializable


class AbstractRayActor(ABC, Generic[Input, Output]):
    """
    An actor that processes a single row of data and returns multiple rows.
    Each input row is specified as a dataclass instance of type Input,
    and each output row is specified as a dataclass instance of type Output.

    Ray processors should inherit from this class.
    """

    def __init__(self, input_cls: type[Input], output_cls: type[Output]):
        self.input_cls = input_cls
        self.output_cls = output_cls

    @abstractmethod
    def process(self, row: Input) -> list[Output]:
        """
        Process a single instance of data. May return multiple instances (think of it as flatmapping).
        """
        raise NotImplementedError()

    def process_json_str(self, json_str: str) -> list[str]:
        """
        Process a single row of data in the form of a json string.
        This is useful when the input data is in the form of a jsonl file.
        """
        row = self.input_cls.from_json(json_str)
        outputs = self.process(row)
        return [x.to_json(ensure_ascii=False, indent=None) for x in outputs]

    def process_ray_data_row(self, row: dict[str, Any]) -> list[dict[str, Any]]:
        """
        Process a single row of data in the form of a Ray dataset.
        When Ray reads a jsonl file, it will read each line as a dictionary with a single key "text".
        This key "text" is used to represent the original json string.
        """
        obj = self.input_cls.from_json(row["text"])
        outputs = self.process(obj)
        return [{"text": x.to_json(ensure_ascii=False, indent=None)} for x in outputs]


def _get_files_local(input_path: str) -> list[str]:
    """
    Get all files in the input path. If the input path is a file, return a list containing that file.
    """
    input_path = Path(input_path).resolve().absolute()
    if input_path.is_file():
        return [str(input_path)]
    filenames = [x.name for x in input_path.iterdir() if x.is_file()]
    return [str(input_path / x) for x in filenames]


def _get_files_gcs(
    input_path: str, gsc_project: str = DEFAULT_GCS_PROJECT
) -> list[str]:
    """
    Get all files in the input path that is in a GCS bucket. If the input path is a file, return a list containing that file.
    """
    assert input_path.startswith("gs://")
    blob = storage.Blob.from_string(input_path)
    client = storage.Client(project=gsc_project)
    bucket = client.bucket(blob.bucket.name)
    blobs = bucket.list_blobs(prefix=blob.name)
    return [f"gs://{blob.bucket.name}/{x.name}" for x in blobs]


def _get_files(input_path: str) -> list[str]:
    """
    Get all files in the input path, regardless of whether it is in a local directory or a GCS bucket.
    If the input path is a file, return a list containing that file.
    """
    if input_path.startswith("gs://"):
        return _get_files_gcs(input_path)
    else:
        return _get_files_local(input_path)


def _write_blocks(blocks: Iterable[Block], stream: BinaryIO):
    """
    Write all blocks to the given output stream.
    """
    try:
        for block in blocks:
            for batch in block.to_batches():
                for row in batch.to_pylist():
                    keys = list(row.keys())
                    if len(keys) != 1:
                        raise ValueError(
                            f"Expected 1 key per row, got {len(keys)} keys: {keys}"
                        )
                    key = list(keys)[0]
                    line = row[key]
                    if not isinstance(line, str):
                        raise TypeError(
                            f"Expected string value for key '{key}', got {type(line).__name__}"
                        )
                    try:
                        stream.write(line.encode("utf-8"))
                        stream.write(b"\n")
                    except UnicodeEncodeError as e:
                        raise ValueError(f"Failed to encode line to UTF-8: {e}") from e
    except Exception as e:
        raise RuntimeError(f"Error writing blocks to stream: {e}") from e


def sync_file_locally(file_path: str):
    """
    Sync the file to the local filesystem if it's in a GCS bucket.
    If it's already mounted, do nothing;
    if it's not mounted, download it to the local filesystem and return the local path.
    The path mimics the mounted path structure, like /mnt/efs/augment/...

    This is useful when we are operating on CoreWeave, where we don't have the mounted access to GCS buckets.
    """
    if file_path.startswith("gs://"):
        client = storage.Client()
        bucket_name, path = file_path[5:].split("/", 1)
        path_prefix = augment_fs_path_for_bucket(bucket_name)

        bucket = client.bucket(bucket_name)
        os.makedirs(path_prefix, exist_ok=True)

        # Download all blobs in path_prefix
        for blob in bucket.list_blobs(prefix=path):
            print(
                f"Downloading gs://{bucket_name}/{blob.name} to {path_prefix / blob.name}"
            )
            local_path = path_prefix / blob.name
            os.makedirs(local_path.parent, exist_ok=True)
            blob.download_to_filename(local_path)

        return str(path_prefix / path)
    else:
        return file_path


class RawJsonlDatasink(Datasink[None]):
    """
    A datasink that writes the output to jsonl files. Each partition will be written to a separate file.
    """

    def __init__(self, path: str):
        super().__init__()
        self.path = path

    def on_write_start(self) -> None:
        os.makedirs(os.path.dirname(self.path), exist_ok=True)

    def write(self, blocks: Iterable[Block], ctx: TaskContext):
        with open(f"{self.path}/{ctx.task_idx}.jsonl", "wb") as f:
            _write_blocks(blocks, f)


class GcsJsonlDatasink(Datasink[None]):
    """
    A Ray Datasink for writing Ray Datasets to Google Cloud Storage.
    """

    def __init__(self, path: str):
        super().__init__()
        blob = storage.Blob.from_string(path)
        self.bucket_name = blob.bucket.name
        self.path = blob.name.rstrip("/")
        self.client: storage.Client | None = None

    def _lazy_init_client(self):
        if self.client is None:
            self.client = storage.Client(project=DEFAULT_GCS_PROJECT)

    def write(self, blocks: Iterable[Block], ctx: TaskContext):
        self._lazy_init_client()
        bucket = self.client.bucket(self.bucket_name)
        filename = f"{self.path}/{ctx.task_idx}.jsonl"
        with tempfile.NamedTemporaryFile() as temp_file:
            _write_blocks(blocks, temp_file)
            temp_file.flush()
            blob = bucket.blob(filename)
            blob.upload_from_filename(temp_file.name)


Actor = TypeVar("Actor", bound=AbstractRayActor)


class RayRunner(Generic[Actor]):
    """
    A runner that manages a pool of Ray actors.
    """

    def __init__(
        self,
        actor_cls: type[Actor],
        actor_args: dict[str, Any],
        num_workers: int,
        num_cpu_per_worker: int,
        num_gpu_per_worker: int,
        local: bool = False,
        max_allowed_errored_blocks: int = 0,
    ):
        self.actor_cls = actor_cls
        self.actor_args = actor_args
        self.num_workers = num_workers
        self.num_cpu_per_worker = num_cpu_per_worker
        self.num_gpu_per_worker = num_gpu_per_worker
        self.num_partitions = (
            num_workers * PARTITIONS_PER_WORKER
        )  # If some partition failed, we can still process the data.
        self.local = local
        self.max_allowed_errored_blocks = max_allowed_errored_blocks
        self.actors: list[Actor] | list[ObjectRef[Actor]] = []

    def __enter__(self):
        print("Actor arguments:")
        for k, v in self.actor_args.items():
            print(f"  {colored(k, 'light_green')} = {colored(v, 'light_blue')}")
        print()
        if self.local:
            print("Initializing 1 actor in local debugging mode.")
            assert (
                self.num_workers == 1
            ), "Local mode is for debugging and only supports 1 worker."
            self.actors = [self.actor_cls(**self.actor_args)]
        else:
            ray_address = os.environ.get("RAY_ADDRESS")
            assert ray_address is not None, "RAY_ADDRESS environment variable not set."
            print(f"Connecting to Ray cluster at {ray_address}")
            ray.init()
            ctx = ray.data.DataContext.get_current()
            ctx.verbose_stats_logs = True
            ctx.max_errored_blocks = self.max_allowed_errored_blocks
            ctx.log_internal_stack_trace_to_stdout = True

            print(f"Initializing {self.num_workers} actors remotely.")
            self.actors = [
                ray.remote(
                    num_cpus=self.num_cpu_per_worker, num_gpus=self.num_gpu_per_worker
                )(self.actor_cls).remote(**self.actor_args)
                for _ in range(self.num_workers)
            ]
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        if not self.local:
            ray.shutdown()

    def process_jsonl(self, input_path: str, output_path: str):
        """
        Process all jsonl files in input_path and write the output to output_path.

        Args:
            input_path: Path to the input directory.
            output_path: Path to the output directory.
        """
        filenames = _get_files(input_path)
        print(f"Found {len(filenames)} files to process.")
        if self.local:
            if input_path.startswith("gs://") and output_path.startswith("gs://"):
                fs = gcsfs.GCSFileSystem(project=DEFAULT_GCS_PROJECT)
                f_open = fs.open
            else:
                f_open = open
            assert len(self.actors) == 1, "Local actor not initialized."
            actor = self.actors[0]
            # get all partitions in input_path
            for file in filenames:
                print(f"Processing {file}")
                file_name = Path(file).name
                with f_open(file, "r") as f_in, f_open(
                    output_path + "/" + file_name, "w"
                ) as f_out:
                    for line in tqdm(f_in, desc=f"Processing {file}"):
                        results = actor.process_json_str(line)
                        for result in results:
                            print(result, file=f_out)

        else:  # distributed on Ray
            assert len(self.actors) == self.num_workers, "Actors not initialized."
            data = ray.data.read_text(
                filenames,
                override_num_blocks=self.num_workers,
            )
            indices = ray.data.range(data.count())  # {id: int64}
            zipped_data = indices.zip(data)  # {id: int64, text: str}
            gen_data = zipped_data.repartition(
                self.num_partitions
            ).flat_map(  # round-robin sending to actors
                lambda x: ray.get(
                    self.actors[x["id"] % self.num_workers].process_ray_data_row.remote(
                        x
                    )
                )
            )
            gen_data.repartition(self.num_workers)
            if output_path.startswith("gs://"):
                gen_data.write_datasink(GcsJsonlDatasink(output_path))
            else:
                gen_data.write_datasink(RawJsonlDatasink(output_path))

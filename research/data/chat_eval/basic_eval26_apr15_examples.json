[{"question": "Where does filtering retrieved chunks vs prefix/suffix happen in services?", "expected_reply": "services/completion_host/single_model_server/overlap.py\nservices/completion_host/single_model_server/single_round_handler.py, in Completion._retrieve", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "What retrieval chunks are filtered out by prefix/suffix filtering in services?", "expected_reply": "Chunks that are fully contained within prefix/suffix range", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "Where does the actual search over document embeddings happen for dense retrieval?", "expected_reply": "services/embeddings_search_host/cpu_server/src/index_search.rs\nresearch/retrieval/retrieval_database.py", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "Is loading the document embeddings in the searcher sequential with computing scores?", "expected_reply": "Loading is overlapped with computing scores to save time", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "Where is the Rogue data pipeline located?", "expected_reply": "augment/research/data/rag/rogue.py\naugment/experimental/michiel/notebooks/rogue", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "In the Rogue data pipeline, where does the retrieval happen?", "expected_reply": "In process repo, after calling _file_to_samples", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "In the Rogue data pipeline, how do I change what languages we retrieve during training", "expected_reply": "change config.retrieval_languages", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "Where in the repo do we implement non-neural speculative decoding model?", "expected_reply": "LongestOverlapLM, NullModel (dummy implementation)", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "Where in context manager service do we delete old blobs?", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "What does VSCode extension sends to the chat backend as context?", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "Which batch size do we use for ethanol retrieval during evaluation?", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "How do you create a new dictionary from two python dictionaries?", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "How can you use an unpickleable object in a Pyspark udf?", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "How do you run a specific test in pytest", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "How can you call arguments by name in typescript?", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "I get this error when running a bazel command. What is the most likely problem?\nServer terminated abruptly (error code: 14, error message: 'Socket closed')", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "Write an rsync to copy a directory, except for any 1st or 2nd level subdirectory that ends in _test", "expected_reply": "rsync -av --exclude='/_test/' --exclude='//*_test/' /path/to/source/ /path/to/destination/", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "Write my Python function that parses this string into data format: 2024-03-08 18:56:09.595356 UTC", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "I have data in JSONL format. How can I sort it by one of the keys with jq in bash", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "I have a column that contains JSON, specifically, a list of objects. I want to return this column, but only keep some fields in each list element. How can I do that?", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "I'm working on VSCode extension. I want to paste some code at the cursor position, but I want VSCode to do that in a smart way -- perhaps, adjust indentations and format. I know that VSCode has API that covers this use case. How can I use this API?", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "Implement a Future class that mimics the Promises API for handling the result of an asynchronous operation. The Future should have:\n\n- A constructor that takes an executor function with resolve and reject functions\n- A then() method that takes onFulfilled and onRejected callback functions\n- A catch() method to handle rejection\n- The ability to chain multiple thens and catches\n\nThe Future should handle both synchronous and asynchronous operations. Demonstrate the usage of the Future class by:\n\n- Creating a Future with a synchronous executor that resolves or rejects randomly\n- Creating a Future with an asynchronous executor that resolves or rejects after 1 second\n- Chaining multiple thens and a catch to handle the results of the Futures", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "Do Python dataclasses automatically have hash function implemented?", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "I want to inherit multiprocessing.Pool class in Python to provide additional caching. Essentially, before sending a input in a queue I want to first check whether we have results in cache. and if yes, then return the result. Note that cache access should happen in a single process since I'm going to use file-based cache and it's not thread-safe.", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "How to do COUNT DISTINCT in SQL for two columns?", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}, {"question": "In Python I have nested dataclasses structures. I want to parse a json string into this dataclass object, but when I do then internal dataclasses remain just dictionary. what shall I do?", "expected_reply": "", "extra": {"repo_url": "augment_apr_10_2024"}}]
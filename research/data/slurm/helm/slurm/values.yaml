# Default values for slurm.

region: LGA1

mounts:
  shared:
    - mountPath: /mnt/efs/aug-cw-lga1
      claimName: aug-cw-lga1
    - mountPath: /mnt/efs/aug-cw-lga1-nvme
      claimName: aug-cw-lga1-nvme
    - mountPath: /mnt/efs/aug-cw-las1
      claimName: aug-cw-las1
  slurm:
    - mountPath: /var/log/slurm
      claimName: var-log-slurm
    - mountPath: /var/lib/slurm
      claimName: var-lib-slurm

common:
  image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/augment_slurm:slurm-22-05-9-1-2

slurmctld:
  replicaCount: 1
  imagePullPolicy: Always

slurmd:
  replicaCount: 3
  subdomainSuffix: tenant-augment-eng.svc.tenant.chi.local
  imagePullPolicy: Always
  maxNodeCount: 100
  cpuCount: 16
  ramGi: 128

slurmdbd:
  imagePullPolicy: Always

mysql:
  image: mariadb:10.10
  imagePullPolicy: Always

munge:
  secretName: augment-slurm-munge  # pragma: allowlist secret

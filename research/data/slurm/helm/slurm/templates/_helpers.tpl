{{/*
Expand the name of the chart.
*/}}
{{- define "slurm.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Define the subdomain for the slurmd worker containers
slurmd-my-cluster.tenant-augment-eng.svc.tenant.chi.local
*/}}
{{- define "slurmd.subdomain" -}}
{{- if .Values.slurmd.subdomainOverride }}
{{- .Values.slurmd.subdomainOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "slurmd-%s.%s" .Values.clusterName .Values.slurmd.subdomainSuffix }}
{{- end }}
{{- end }}

{{/*
Define the names of the config map
*/}}
{{- define "slurm.configmap.name" -}}
{{- printf "slurm-%s-config-map" .Values.clusterName }}
{{- end }}

{{/*
Volume mounts and volumes
*/}}
{{- define "slurm.volumeMounts" -}}
{{- range .Values.mounts.shared }}
{{ printf "- mountPath: %s" .mountPath }}
{{ printf "  name: %s" .claimName }}
{{- end }}
{{- range .Values.mounts.slurm }}
{{ printf "- mountPath: %s" .mountPath }}
{{ printf "  name: %s" .claimName }}
{{- end }}
{{ printf "- name: munge-key" }}
{{ printf "  mountPath: /etc/munge"}}
{{ printf "  readOnly: true" }}
{{- end }}

{{- define "slurm.volumes" -}}
{{- range .Values.mounts.shared }}
{{ printf "- name: %s" .claimName }}
{{ printf "  persistentVolumeClaim: " }}
{{ printf "    claimName: %s" .claimName }}
{{- end }}
{{- range .Values.mounts.slurm }}
{{ printf "- name: %s" .claimName }}
{{ printf "  emptyDir: {}" }}
{{- end }}
{{ printf "- name: munge-key" }}
{{ printf "  secret:"}}
{{ printf "     secretName: %s" .Values.munge.secretName }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "slurm.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "slurm.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "slurm.labels" -}}
helm.sh/chart: {{ include "slurm.chart" . }}
slurm-cluster: {{ .Values.clusterName }}
{{ include "slurm.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "slurm.selectorLabels" -}}
app.kubernetes.io/name: {{ include "slurm.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "slurm.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "slurm.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

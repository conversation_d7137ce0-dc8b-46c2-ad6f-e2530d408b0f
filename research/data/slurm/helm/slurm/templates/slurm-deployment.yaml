apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "slurm.configmap.name" . }}
  labels:
    {{- include "slurm.labels" . | nindent 4 }}
data:
  slurmd_worker_domain: {{ include "slurmd.subdomain" . }}
  slurm_cluster_name: {{ .Values.clusterName }}
{{ tpl (.Files.Glob "config/*.conf").AsConfig .  | indent 2 }}
---
apiVersion: v1
kind: Service
metadata:
  creationTimestamp: null
  labels:
    app: slurmdbd
    {{- include "slurm.labels" . | nindent 4 }}
  name: {{ printf "slurmdbd-%s" .Values.clusterName }}
spec:
  ports:
    - name: "6819"
      port: 6819
      targetPort: 6819
  selector:
    app: slurmctld  # slurmctld not slurmdbd since it's on the ctld pod
    slurm-cluster: {{ .Values.clusterName }}
status:
  loadBalancer: {}
---
apiVersion: v1
kind: Service
metadata:
  creationTimestamp: null
  labels:
    app: slurmctld
    {{- include "slurm.labels" . | nindent 4 }}
  name: {{ printf "slurmctld-%s" .Values.clusterName }}
spec:
  ports:
    - name: "6817"
      port: 6817
      targetPort: 6817
  selector:
    app: slurmctld
    slurm-cluster: {{ .Values.clusterName }}
status:
  loadBalancer: {}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: slurmctld
    {{- include "slurm.labels" . | nindent 4 }}
  name: {{ printf "slurmctld-%s " .Values.clusterName }}
spec:
  replicas: {{ .Values.slurmctld.replicaCount }}
  selector:
    matchLabels:
      app: slurmctld
      slurm-cluster: {{ .Values.clusterName }}
  strategy:
    type: Recreate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: slurmctld
        slurm-cluster: {{ .Values.clusterName }}
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: topology.kubernetes.io/region
                operator: In
                values:
                - {{ .Values.region }}
      containers:
        - name: slurmctld
          args:
            - slurmctld
          image: {{ .Values.common.image }}
          imagePullPolicy: {{ .Values.slurmctld.imagePullPolicy }}
          ports:
            - containerPort: 6817
          resources: {}
          volumeMounts:
            {{- include "slurm.volumeMounts" . | indent 12 }}
            - name: config
              mountPath: "/etc/slurm.data"
              readOnly: true
          env:
            - name: SLURMD_WORKER_DOMAIN
              valueFrom:
                configMapKeyRef:
                  name: {{ include "slurm.configmap.name" . }}
                  key: slurmd_worker_domain
            - name: SLURM_CLUSTER_NAME
              valueFrom:
                configMapKeyRef:
                  name: {{ include "slurm.configmap.name" . }}
                  key: slurm_cluster_name
        - name: slurmdbd
          args:
            - slurmdbd
          image: {{ .Values.common.image }}
          imagePullPolicy: {{ .Values.slurmdbd.imagePullPolicy }}
          ports:
            - containerPort: 6819
          resources: {}
          volumeMounts:
            - mountPath: /var/run/mysqld
              name: var-run-mysqld
            - name: config
              mountPath: "/etc/slurm.data"
              readOnly: true
            - name: munge-key
              mountPath: /etc/munge
              readOnly: true
          env:
            - name: SLURMD_WORKER_DOMAIN
              valueFrom:
                configMapKeyRef:
                  name: {{ include "slurm.configmap.name" . }}
                  key: slurmd_worker_domain
            - name: SLURM_CLUSTER_NAME
              valueFrom:
                configMapKeyRef:
                  name: {{ include "slurm.configmap.name" . }}
                  key: slurm_cluster_name
        - name: mysql
          env:
            - name: MYSQL_DATABASE
              value: slurm_acct_db
            - name: MYSQL_PASSWORD
              value: password
            - name: MYSQL_RANDOM_ROOT_PASSWORD
              value: "yes"
            - name: MYSQL_USER
              value: slurm
          image: {{ .Values.mysql.image }}
          imagePullPolicy: {{ .Values.mysql.imagePullPolicy }}
          resources: {}
          volumeMounts:
            - mountPath: /var/run/mysqld
              name: var-run-mysqld
      hostname: {{ printf "slurmctld-%s " .Values.clusterName }}  # Needs to match the service name or dbd fails to start
      restartPolicy: Always
      volumes:
        - name: var-run-mysqld
          emptyDir: {}
        {{- include "slurm.volumes" . | indent 8 }}
        - name: config
          configMap:
            name: {{ include "slurm.configmap.name" . }}
            items:
            - key: "slurm.conf"
              path: "slurm.conf"
            - key: "slurmdbd.conf"
              path: "slurmdbd.conf"
---
apiVersion: v1
kind: Service
metadata:
  creationTimestamp: null
  labels:
    app: slurmd
    {{- include "slurm.labels" . | nindent 4 }}
  name: {{ printf "slurmd-%s" .Values.clusterName }}
spec:
  ports:
    - name: "6818"
      port: 6818
      targetPort: 6818
  clusterIP: None
  selector:
    app: slurmd
    slurm-cluster: {{ .Values.clusterName }}
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ printf "slurmd-%s" .Values.clusterName }}
  labels:
    {{- include "slurm.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      app: {{ printf "slurmd-%s" .Values.clusterName }} # has to match .spec.template.metadata.labels
      slurm-cluster: {{ .Values.clusterName }}
  serviceName: "slurmd"
  replicas: {{ .Values.slurmd.replicaCount }}
  podManagementPolicy: Parallel
  template:
    metadata:
      labels:
        app: {{ printf "slurmd-%s" .Values.clusterName }} # has to match .spec.selector.matchLabels
        {{- include "slurm.labels" . | nindent 8 }}
    spec:
      terminationGracePeriodSeconds: 10
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: topology.kubernetes.io/region
                operator: In
                values:
                - {{ .Values.region }}
              - key: node.coreweave.cloud/cpu
                operator: In
                values:
                  - intel-xeon-icelake
                  - intel-xeon-scalable
                  - intel-xeon-v4
      containers:
        - name: c1
          resources:
            limits:
              cpu: {{ .Values.slurmd.cpuCount }}
              memory: {{ .Values.slurmd.ramGi }}Gi
            requests:
              cpu: {{ .Values.slurmd.cpuCount }}
              memory: {{ .Values.slurmd.ramGi }}Gi
          args:
            - slurmd
          image: {{ .Values.common.image }}
          imagePullPolicy: {{ .Values.slurmd.imagePullPolicy }}
          ports:
            - containerPort: 6818
          volumeMounts:
            {{- include "slurm.volumeMounts" . | indent 12 }}
            - name: config
              mountPath: "/etc/slurm.data"
              readOnly: false
          env:
            - name: SLURMD_WORKER_DOMAIN
              valueFrom:
                configMapKeyRef:
                  name: {{ include "slurm.configmap.name" . }}
                  key: slurmd_worker_domain
            - name: SLURM_CLUSTER_NAME
              valueFrom:
                configMapKeyRef:
                  name: {{ include "slurm.configmap.name" . }}
                  key: slurm_cluster_name
      volumes:
        {{- include "slurm.volumes" . | indent 8 }}
        - name: config
          configMap:
            name: {{ include "slurm.configmap.name" . }}
            items:
            - key: "slurm.conf"
              path: "slurm.conf"
            - key: "slurmdbd.conf"
              path: "slurmdbd.conf"

"""A simple counter that is used to track number of events in a given timespan.

For example, event count in a rolling 1 minute window.
"""
import asyncio
import time
import uuid
from collections import deque


class TimedCounter:
    """A counter that is used to track number of events in a given timespan.

    Here we simply track the timestamps of all events in a queue.
    Before every query and insertion we drop all the items that are expired.
    """

    def __init__(self, window_size_seconds: float):
        self.window_size_seconds = window_size_seconds
        self.events: deque[float] = deque()
        self.uncommitted_ticks: deque[str] = deque()
        self.lock = asyncio.Lock()

    def prune(self):
        """Prune all expired events."""
        ts = time.time()
        cutoff = ts - self.window_size_seconds
        while self.events and self.events[0] < cutoff:
            self.events.popleft()
        return ts

    async def tick(self, max_length: int, max_wait: float = 1.0):
        """Try to tick the counter but block until length satisfies the limit."""
        start_time = time.time()
        if max_length < 1:
            raise ValueError("Length limit must be at least 1")
        my_id = str(uuid.uuid4())
        self.uncommitted_ticks.append(my_id)
        # Only register if I am i front of the queue
        while self.uncommitted_ticks and (
            self.uncommitted_ticks[0] != my_id or self.get_count() >= max_length
        ):
            await asyncio.sleep(min(max_wait, self.seconds_till_size(max_length - 1)))
        async with self.lock:
            if self.uncommitted_ticks and self.uncommitted_ticks[0] == my_id:
                next_up = self.uncommitted_ticks.popleft()
                if next_up == my_id:
                    ts = time.time()
                    self.events.append(ts)
                    return ts - start_time
            raise RuntimeError("Interference detected. Unable to correct register tick")

    def get_count(self) -> int:
        """Query the number of events in the counter."""
        self.prune()
        return len(self.events)

    def seconds_till_size(self, target: int) -> float:
        """Number of seconds till the counter reaches the target size."""
        ts = self.prune()
        event_count = len(self.events)
        while event_count <= target:
            return 0
        # figure out which event has to expire to reach target size
        watch_id = event_count - target - 1
        return self.events[watch_id] + self.window_size_seconds - ts

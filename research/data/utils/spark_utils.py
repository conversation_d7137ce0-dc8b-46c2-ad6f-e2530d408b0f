"""Utilities for working with spark."""

from typing import Optional

from pyspark.sql.dataframe import DataFrame
from pyspark.sql.functions import rand


def repartition_and_shuffle(
    random_seed: Optional[int],
    df: DataFrame,
    rows_per_indexed_dataset: int = 4096,
) -> DataFrame:
    """Deterministically shuffle entire dataframe."""
    df = df.withColumn("random", rand(random_seed))
    df = df.persist()
    num_partitions = max(1, df.count() // rows_per_indexed_dataset)
    df = df.repartition(num_partitions, "random")
    sorted_df = df.sortWithinPartitions("random")
    df.unpersist()
    sorted_df = sorted_df.drop("random")
    return sorted_df

"""Manages async tasks that are requests to many different sites.

For each site, this is used to handle a concurrency limit and rate limit.

Some of our tasks involves extracting data from a large number of sites.
This module leverages this structure to achieve a high level of parallelization,
while also controlling rate limiting for individual sites.
"""

import asyncio
import logging
import math
import random
import uuid
from collections import defaultdict
from dataclasses import dataclass, field
from typing import Callable, Iterable, Optional, Union

import aiohttp

from research.data.utils.timed_counter import TimedCounter

HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"
}
ROBOT_MARK = '<meta name="robots" content="noindex,'


logger = logging.getLogger(__name__)


def valid_status(status_code: int):
    """Returns if a status code is valid."""
    return 200 <= status_code < 300


@dataclass
class TaskRequest:
    """A outgoing request that need to be made."""

    group: str
    """Concurrency within requests of the same group are limited."""

    url: str
    """Request URL."""

    params: Optional[dict] = None
    """Params for the request.  If not present, will use the default one for the manager."""

    headers: Optional[dict] = None
    """Request headers.  Will use the default for the manager if not present."""

    json_data: Optional[dict] = None
    """JSON data to send with the request.  Will use manager default if not present."""

    allow_redirects: bool = True
    """Whether redirection is allowed."""

    accepted_code: Callable[[int], bool] = valid_status
    """Function that is used to check response code to verify it is normal.  If it returns False, retry will happen."""

    method: str = "GET"
    """The HTTP method of the request."""

    decode_json: bool = True
    """Whether to decode the json results to objects."""

    verify: bool = True
    """Whether to verify identity of the SSL certificate."""

    task_id: uuid.UUID = field(default_factory=uuid.uuid4)
    """Used to identify the response and correlate to the request."""


@dataclass
class TaskResponse:
    """Represents a response from a downlaod event."""

    task_id: uuid.UUID
    """Same as the ID in the request."""

    request: TaskRequest
    """A copy of the request."""

    success: bool
    response_code: int
    result: Union[dict, str, None]
    errors: list[Exception]


class RequestTaskManager:
    """An async request manager for conditional concurrency control.

    Very often we need to submit large number of requests to many different URLs.
    All requests to different sites can proceed concurrently, whereas only one
    is allowed for the same site at any time, with a cooldown between requests
    on the same site.
    """

    def __init__(
        self,
        max_per_site: int = 1,
        cooldown_sec: float = 0.1,
        max_retries: int = 3,
        retry_cooldown: float = 10.0,
        method: str = "GET",
        params: Optional[dict] = None,
        headers: Optional[dict] = None,
        json_data: Optional[dict] = None,
        timeout: float = 30,
        rpm: Union[float, None, dict[str, float]] = None,
        concurrency_limit: int = 100,
        jitter: float = 0.1,
    ):
        """Initialize the manager."""
        # A semaphore for each site to do per-site rate control
        self.semaphores = defaultdict(lambda: asyncio.Semaphore(max_per_site))
        self.max_per_site = max_per_site
        self.cooldown_sec = cooldown_sec
        self.max_retries = max_retries
        self.retry_cooldown = retry_cooldown
        self.backoff_factor = 2.0
        self.method = method
        self.headers = dict(headers or HEADERS)
        self.params = dict(params or {})
        self.json_data = dict(json_data or {})
        self.timeout = timeout
        self.concurrency_limit = concurrency_limit
        self.jitter = jitter
        self.global_semaphore: asyncio.Semaphore

        self.blocked_sites: set[str] = set()
        """Do not send requests to these sites.  Can be updated on the fly."""

        # Get the rate limit for each site.
        self.rpm: dict[str, Optional[float]]
        if rpm is None:
            self.rpm = defaultdict(lambda: None)
        elif isinstance(rpm, dict):
            self.rpm = defaultdict(lambda: None)
            self.rpm.update(rpm)
        else:
            self.rpm = defaultdict(lambda: rpm)

        # Create the per group rate counters for 1min window
        self.req_counters = defaultdict(lambda: TimedCounter(60.0))

    async def tick_rpm_counter(self, group: str):
        """Register a new request in the counter under the rpm constraint.

        If the count is already reaching the limit then have to wait.

        returns the number of seconds waited.
        """
        # Make sure RPM limit is not exceeded
        rpm = self.rpm[group]
        if not rpm:
            return 0.0
        rpm = int(math.floor(rpm))
        counter = self.req_counters[group]
        return await counter.tick(rpm)

    async def make_request(self, req: TaskRequest):
        """Extract data through request asynchronously using aiohttp."""
        params = req.params or self.params
        headers = req.headers or self.headers
        json_data = req.json_data or self.json_data
        cd = self.retry_cooldown
        errors = []
        # add random wait
        await asyncio.sleep(random.uniform(0, max(self.jitter, 0)))
        # Use a collection of semaphores to controller concurrency per site.
        async with (
            aiohttp.ClientSession() as session,
            self.global_semaphore,
            self.semaphores[req.group],
        ):
            if req.group in self.blocked_sites:
                return TaskResponse(req.task_id, req, False, -2, None, [])

            last_response = -1
            for _ in range(self.max_retries):
                try:
                    logger.debug(
                        "Sending request to %s in group %s", req.url, req.group
                    )
                    waited = await self.tick_rpm_counter(req.group)
                    if waited > 0.1:
                        logger.info("Waited %.1f sec due to RPM limit", waited)
                    # Apply cooldown before each independent request.
                    if waited < self.cooldown_sec:
                        await asyncio.sleep(self.cooldown_sec - waited)
                    req_kwargs = {
                        "timeout": self.timeout,
                        "allow_redirects": req.allow_redirects,
                        "verify_ssl": req.verify,
                    }
                    if params:
                        req_kwargs["params"] = params
                    if json_data:
                        req_kwargs["json"] = json_data
                    # Asynchronously send the request and wait for the response
                    async with session.request(
                        req.method, req.url, headers=headers, **req_kwargs
                    ) as response:
                        if ROBOT_MARK in str(response.text).lower():
                            self.blocked_sites.add(req.url)
                            logger.info(
                                "Encoutered robot blocker: %s. Will no longer process this site.",
                                req.url,
                            )
                            return TaskResponse(req.task_id, req, False, -2, None, [])
                        last_response = response.status
                        # Check if the request was successful
                        if req.accepted_code(response.status):
                            if req.decode_json:
                                result = await response.json()
                            else:
                                result = await response.text()
                            return TaskResponse(
                                req.task_id,
                                req,
                                True,
                                response.status,
                                result,
                                errors,
                            )
                        else:
                            logger.warning(
                                "Unacceptable response code %d for url %s",
                                response.status,
                                req.url,
                            )
                            errors.append(
                                aiohttp.ClientResponseError(
                                    request_info=response.request_info,
                                    history=response.history,
                                    status=response.status,
                                    message=f"Inadmissible response code {response.status}",
                                )
                            )
                except Exception as exc:  # pylint: disable=broad-except
                    logger.warning(
                        "Exception occuring handling request for %s: %s",
                        req.url,
                        exc,
                        exc_info=True,
                    )
                    errors.append(exc)
                logger.info("Waiting %.2f seconds to retry request to %s", cd, req.url)
                await asyncio.sleep(cd)
                cd *= self.backoff_factor
            # All retries failed.  We have to return now
            return TaskResponse(req.task_id, req, False, last_response, None, errors)

    async def make_all_requests(self, reqs: Iterable[TaskRequest]):
        """Collect a batch of requests, run all of them and return."""
        self.semaphores.clear()
        self.global_semaphore = asyncio.Semaphore(self.concurrency_limit)
        tasks = [asyncio.create_task(self.make_request(req)) for req in reqs]
        return await asyncio.gather(*tasks)

    def make_all_requests_sync(self, reqs: Iterable[TaskRequest]):
        return asyncio.run(self.make_all_requests(reqs))

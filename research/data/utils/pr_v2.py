"""Utilities for working with the PR v2 dataset."""

import hashlib
import logging
import tempfile
from collections.abc import Iterable
from pathlib import Path
from typing import TypedDict

from tqdm import tqdm

from research.data.utils.bare_repo_utils import unpack_repo_tar
from research.next_edits.next_edits_dataset import PRMeta, RepoChangeWithMeta
from research.utils.repo_change_utils import get_commit_history, iterate_repo_history

# GCS bucket for spark data
SPARK_DATA_BUCKET = "gcp-us1-spark-data"

# Base directory for all PR v2 data
PR_V2_BASE = "/mnt/efs/spark-data/shared/pr_v2"
PR_V2_BASE_GCS = f"gs://{SPARK_DATA_BUCKET}/shared/pr_v2"

# Table containing raw PR data generated from PR events
# PR diffs have been joined with the PR events
PR_DATA_CW = f"{PR_V2_BASE}/pr_data"
# TODO(zhewei): migrate this to new project
PR_DATA_GCP = "gs://bigquery-data-staging/gh_pr_candidates_filtered"

# Table containing PRs grouped by their base repo name
# Path suffix indicates different number of partitions, e.g. pr_grouped_10k (default=1000)
PR_GROUPED = f"{PR_V2_BASE}/pr_grouped"

# Root directory containing downloaded repos
# Subdirectories are created based on the repo name hash to avoid too many files in one directory
DOWNLOADED_REPOS = f"{PR_V2_BASE}/downloaded_repos"
# Hashing size for repo names
# It's used to control the number of subdirectories for downloaded repos
REPO_NAME_HASH_SIZE = 1000
# Extension for repo tar files
REPO_TAR_EXT = ".tar.gz"
# Extension for log files of downloaded repos
DOWNLOAD_LOG_EXT = ".log"
# Keys in log files of downloaded repos
# - List of saved intermediate commit shas in selected PRs
SAVED_SHAS = "saved_shas"


# === Datasets for AutoFix
# Table containing metadata of intermediate commits for PRs
PR_INTER_COMMITS = f"{PR_V2_BASE}/inter_commits"
# Table containing metadata of check runs for commits
PR_CHECK_RUNS = f"{PR_V2_BASE}/check_runs"
# Table containing logs of failed check runs for commits
PR_FAILED_LOGS = f"{PR_V2_BASE}/failed_logs"
# === End of AutoFix datasets


# === Intermediate tables
# Table containing repo names and their branches to be downloaded
REPO_BRANCHES = f"{PR_V2_BASE}/repo_branches"
# Table containing repo names and their PRs to be processed
REPO_PRS = f"{PR_V2_BASE}/repo_prs"
REPO_PRS_GCS = f"{PR_V2_BASE_GCS}/repo_prs"
# === End of intermediate tables


class GitUserInfo(TypedDict):
    login: str
    id: int


class PrCommitInfo(TypedDict):
    ref: str
    sha: str


class PrInfo(TypedDict):
    id: int
    title: str
    body: str | None
    user: GitUserInfo
    created_at: str
    updated_at: str
    merged_at: str
    merged: bool
    merge_commit_sha: str
    commits: int
    base: PrCommitInfo
    head: PrCommitInfo


class RepoPrInfo(TypedDict):
    """Meta info about the PRs coming from a given repo.

    This is used for the PR dataset v2.
    """

    repo_name: str
    path_to_repo_tar: str
    pr_list: list[PrInfo]


def pr_data_to_repo_changes(
    repo_pr_info: RepoPrInfo,
    max_repo_commits: int | None,
    max_repo_bytes: int = 1_000_000_000,
    max_repo_files: int = 10_000,
    max_workers: int = 1,
) -> Iterable[RepoChangeWithMeta]:
    """Convert the given repo_pr_info into repo changes."""
    path = Path(repo_pr_info["path_to_repo_tar"])
    repo_size = path.stat().st_size
    repo_name = repo_pr_info["repo_name"]
    if repo_size > max_repo_bytes:
        size_gb = repo_size / 1e9
        logging.warning(f"Repo {repo_name} too large ({size_gb} GB)")
        return
    all_prs = sorted(repo_pr_info["pr_list"], key=lambda pr: pr["merged_at"])
    if not all_prs:
        logging.warning(f"Repo {repo_name} has no PRs")
        return
    sha_to_pr = {pr["merge_commit_sha"]: pr for pr in all_prs}
    last_sha = all_prs[-1]["merge_commit_sha"]
    first_sha = all_prs[0]["merge_commit_sha"]
    with tempfile.TemporaryDirectory() as tmp_dir:
        tmp_dir = Path(tmp_dir)
        unpack_repo_tar(path, tmp_dir)
        commit_history = get_commit_history(
            tmp_dir, max_history=max_repo_commits, commit_id=last_sha
        )
        first_commit_i = next(
            (i for i, commit in enumerate(commit_history) if commit.sha == first_sha),
            0,
        )
        # drop commits that are not in the PR history
        commit_history = commit_history[first_commit_i:]
        repo_changes = iterate_repo_history(
            tmp_dir, commit_history, max_workers=max_workers
        )
        for repo_change, commit in zip(repo_changes, commit_history[1:]):
            if len(repo_change.after_files) > max_repo_files:
                logging.warning(
                    f"Repo {repo_name} has too many files ({len(repo_change.after_files)})"
                )
                break
            pr_meta: PRMeta | None = None
            if pr := sha_to_pr.get(commit.sha):
                pr_meta = PRMeta(
                    repo_name=repo_name,
                    pr_number=pr["id"],
                    title=pr["title"],
                    body=pr["body"] or "",
                )
            yield RepoChangeWithMeta(repo_change, commit, pr_meta)


def repartition_pr_data(
    data_dir: Path,
    output_dir: Path,
):
    """Repartition the PR data by putting each row into a separate parquet file.

    This function also drops the diffs stored in each PR.
    """
    import pandas as pd

    all_files = list(data_dir.glob("*.parquet"))
    n_files = 0
    output_dir.mkdir(exist_ok=False, parents=True)
    for file in tqdm(all_files, desc="Repartitioning", smoothing=0):
        df = pd.read_parquet(file)
        for row in df.to_dict(orient="records"):
            row["pr_list"] = [
                PrInfo(
                    {
                        "id": pr["id"],
                        "title": pr["title"],
                        "body": pr["body"],
                        "user": pr["user"],
                        "created_at": pr["created_at"],
                        "updated_at": pr["updated_at"],
                        "merged_at": pr["merged_at"],
                        "merged": pr["merged"],
                        "merge_commit_sha": pr["merge_commit_sha"],
                        "commits": pr["commits"],
                        "base": pr["base"],
                        "head": pr["head"],
                    }
                )
                for pr in row["pr_list"]
            ]
            out_file = output_dir / f"part-{n_files}.parquet"
            # convert the row back into a row and save it
            pd.DataFrame([row]).to_parquet(out_file)
            n_files += 1


def hash_repo_name(repo_name: str) -> int:
    """Hash the repo name to a number between 0 and REPO_NAME_HASH_SIZE - 1."""
    return int(hashlib.sha256(repo_name.encode()).hexdigest(), 16) % REPO_NAME_HASH_SIZE


def convert_repo_to_filename(repo_name: str) -> str:
    """Convert the full repo name to a valid filename without extension."""
    return repo_name.replace("/", "__")  # double underscores


def get_tar_dir_path(
    repo_name: str,
    root: str = DOWNLOADED_REPOS,
) -> Path:
    """Get the directory path that contains the tar file for the given repo name."""
    return Path(root) / str(hash_repo_name(repo_name))


def get_tar_filename(repo_name: str) -> str:
    """Get the filename of the saved tar file for the given repo name."""
    return f"{convert_repo_to_filename(repo_name)}{REPO_TAR_EXT}"


def get_tar_full_path(
    repo_name: str,
    root: str = DOWNLOADED_REPOS,
) -> Path:
    """Get the full path to the saved tar file for the given repo name."""
    return get_tar_dir_path(repo_name, root) / get_tar_filename(repo_name)


def get_download_log_filename(repo_name: str) -> str:
    """Get the filename of the log file for the given repo name."""
    return f"{convert_repo_to_filename(repo_name)}{DOWNLOAD_LOG_EXT}"


def get_download_log_full_path(
    repo_name: str,
    root: str = DOWNLOADED_REPOS,
) -> Path:
    """Get the full path to the log file for the given repo name."""
    return get_tar_dir_path(repo_name, root) / get_download_log_filename(repo_name)

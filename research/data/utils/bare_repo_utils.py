"""Utilities for working with our bare Git repo dataset."""

from __future__ import annotations

import datetime
from math import ceil
import re
import subprocess
import tempfile
from pathlib import Path
from random import Random
from typing import Iterable, Sequence, TypedDict

import boto3
import pandas as pd

from research.core import augment_secrets

BareRepoPath = "augment-github/bare-repos"
EndpointUrl = "https://object.las1.coreweave.com"


class _RepoInfo(TypedDict):
    Key: str
    LastModified: datetime.datetime
    ETag: str
    Size: int
    StorageClass: str


def get_bare_repo_list(max_repos: int = 100_000) -> Sequence[_RepoInfo]:
    """Get the list of repos from `s3://augment-github/bare-repos`"""
    s3_client = get_s3_client()
    bucket, prefix = BareRepoPath.split("/", 1)

    repos = list[_RepoInfo]()
    continue_token: str | None = None

    while len(repos) < max_repos:
        response: dict = s3_client.list_objects_v2(
            Bucket=bucket,
            Prefix=prefix + "/",
            MaxKeys=max_repos,
            ContinuationToken=continue_token or "",
        )
        new_repos = response["Contents"]
        repos.extend(new_repos)
        continue_token = response.get("NextContinuationToken")
        if not new_repos or continue_token is None:
            break
    return repos[:max_repos]


def make_bare_repo_list(
    max_repos: int = 100_000, max_num_files: int = 10_000
) -> Iterable[list[str]]:
    """Get the list of bar repo list and yield as batches."""
    print("Getting full repo list...")
    repos = get_bare_repo_list(max_repos)
    print(f"Got a total of {len(repos)} repos.")
    bucket, _ = BareRepoPath.split("/", 1)
    all_s3_paths = [f"s3://{bucket}/{repo['Key']}" for repo in repos]
    rng = Random(42)
    rng.shuffle(all_s3_paths)
    batch_size = max(5, ceil(len(all_s3_paths) / max_num_files))
    for start in range(0, len(all_s3_paths), batch_size):
        yield all_s3_paths[start : start + batch_size]


def save_bare_repo_list_disk(
    save_path: Path, max_repos: int = 100_000, max_num_files: int = 10_000
):
    """Write a list of bare repo links to the disk path as parquet files.

    Each file contains a dataframe with one column, "repo_path".
    """
    save_path.mkdir(parents=True)
    for i, batch in enumerate(make_bare_repo_list(max_repos, max_num_files)):
        df = pd.DataFrame(batch, columns=["repo_path"])
        parquet_path = save_path / f"part{i}.parquet"
        df.to_parquet(parquet_path)


def save_bare_repo_list_s3(
    save_path: str, max_repos: int = 100_000, max_num_files: int = 10_000
):
    """Write a list of bare repo links to the s3 path as parquet files.

    Each file contains a dataframe with one column, "repo_path".
    """
    if not save_path.startswith("s3://") or not save_path.endswith("/"):
        raise ValueError(f"save_path must be a valid s3 path: {save_path}")

    with tempfile.TemporaryDirectory() as tmp_dir:
        for i, batch in enumerate(make_bare_repo_list(max_repos, max_num_files)):
            df = pd.DataFrame(batch, columns=["repo_path"])
            parquet_path = Path(tmp_dir) / f"part{i}.parquet"
            df.to_parquet(parquet_path)
        subprocess.run(
            f"s3cmd rm {save_path} --recursive",
            shell=True,
            stdout=subprocess.DEVNULL,
            check=False,
        )
        subprocess.run(
            f"s3cmd put part*.parquet {save_path} --recursive",
            cwd=tmp_dir,
            shell=True,
            stdout=subprocess.DEVNULL,
            check=True,
        )
    print(f"Repo list saved to {save_path}")


def s3_url_exits(s3_url_prefix: str) -> bool:
    """Check if any file exists under a given prefix."""
    if s3_url_prefix.startswith("s3a://"):
        s3_url_prefix = f"s3{s3_url_prefix[3:]}"
    result = subprocess.check_output(["s3cmd", "ls", s3_url_prefix], text=True)
    return bool(result.strip())


def get_s3_client(endpoint_url: str = EndpointUrl):
    """Get a boto3 client for S3."""
    access_key, secret_key = augment_secrets.get_coreweave_keys()
    return boto3.client(
        "s3",
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_key,
        endpoint_url=endpoint_url,
    )


def download_repo_from_s3(s3_url: str, dst_dir: Path) -> None:
    """Download the repo tarball from S3 and extract it as .git under dst_dir.

    dst_dir needs to exist and be empty.
    """

    if not s3_url.startswith("s3://"):
        raise ValueError(f"s3_url must start with s3://: {s3_url}")
    if not s3_url.endswith(".tar.gz"):
        raise ValueError(f"s3_url must end with .tar.gz: {s3_url}")
    if not dst_dir.exists():
        raise ValueError(f"dst_dir not exist: {dst_dir}")
    if (dst_dir / ".git").exists():
        raise ValueError(f"dst_dir already contains a git repo: {dst_dir}")

    pattern = r"s3://([^/]+)/(.+)"
    match = re.match(pattern, s3_url)
    if match is None:
        raise ValueError(f"bad s3_url: {s3_url}")
    bucket_name = match.group(1)
    object_key = match.group(2)
    print(f"downloading: {bucket_name=}, {object_key=}")

    s3_client = get_s3_client()
    target_path = dst_dir / "repo.tar.gz"
    with target_path.open("wb") as f:
        s3_client.download_fileobj(bucket_name, object_key, f)
    # subprocess.run(f"s3cmd get {s3_url}", shell=True, check=True, cwd=dst_dir)
    # target_path = dst_dir / Path(s3_url[5:]).name
    unpack_repo_tar(target_path, dst_dir)
    target_path.unlink()


def unpack_repo_tar(repo_tar: Path, dst_dir: Path) -> None:
    assert repo_tar.exists()
    assert dst_dir.exists()
    subprocess.run(f"tar -xzf {repo_tar}", shell=True, check=True, cwd=dst_dir)
    [git_file_path] = list(dst_dir.glob("*.git"))
    (git_file_path).rename(dst_dir / ".git")
    # convert this to a non-bare git repo
    subprocess.run(
        "git config --local --bool core.bare false && git reset HEAD -- .",
        shell=True,
        check=True,
        cwd=dst_dir,
    )

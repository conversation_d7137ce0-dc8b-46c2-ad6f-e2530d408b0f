"""Unit tests for sort_files_within_repo."""

from itertools import permutations

from pyspark.sql import Row

from research.data.train.common.sort_files_within_repo import (
    _maximize_filename_match_sort,
    _random_sort,
)


class TestSortFilesWithinRepo:
    """Unit tests for sort_files_within_repo."""

    CONTENT_COL = "content"
    FILE_PATH_COL = "file_path"

    def test_random_sort(self):
        file_list = self._generate_simple_file_list()
        expected = [file_list[idx] for idx in [1, 4, 2, 0, 3]]

        # the random seed will be set to a fixed value when calling the function
        generated = _random_sort(file_list)
        assert generated == expected

    def test_maximize_filename_match_sort(self):
        file_list = self._generate_simple_file_list()
        expected = [file_list[idx] for idx in [3, 4, 2, 1, 0]]

        # permutating the input won't change the output of the given graph
        # note that this is NOT always true for different kinds of graphs
        all_permutations = list(permutations(file_list))
        for new_file_list in all_permutations:
            generated = _maximize_filename_match_sort(
                new_file_list, self.CONTENT_COL, self.FILE_PATH_COL
            )
            assert generated == expected

    def test_maximize_filename_match_sort_with_cyclic_dependency(self):
        file_list = self._generate_simple_file_list(add_cycle=True)
        expected = [file_list[idx] for idx in [3, 4, 2, 1, 5, 0]]

        generated = _maximize_filename_match_sort(
            file_list, self.CONTENT_COL, self.FILE_PATH_COL
        )
        assert generated == expected

    def _generate_simple_file_list(self, add_cycle=False):
        file0 = Row(
            file_path="src/file0.py",
            content="import utils.common.file_2",
        )
        file1 = Row(
            file_path="src/mock/a/long/path/to/file1.py",
            content="import utils.common.file_2 file_2.foo()",
        )
        file2 = Row(
            file_path="src/utils/common/file_2.py",
            content="import cpp_module.file4 file5.bar()",
        )
        file3 = Row(
            file_path="src/cpp_module/header__file_3.h",  # double underscores
            content="test unmatched names: file file_0 file.1 file__2 file-4",
        )
        file4 = Row(
            file_path="src/cpp_module/file4.cpp",
            content="#include <src/cpp_module/header__file_3.h>",
        )
        if add_cycle:
            # cycle: file2 => file1 => file5 => file2
            file5 = Row(
                file_path="file5.py",
                content="import mock.a.long.path.to.file1 file1()",
            )
            return [file0, file1, file2, file3, file4, file5]
        return [file0, file1, file2, file3, file4]

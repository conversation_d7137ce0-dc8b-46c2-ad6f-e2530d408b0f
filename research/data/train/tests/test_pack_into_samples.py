"""Unit tests for pack_into_samples."""

import numpy as np
import pytest

from research.data.train.common.pack_into_samples import pack_into_samples


class TestPackIntoSamples:
    """Unit tests for pack_into_samples."""

    @pytest.mark.parametrize(
        "data,token_length,pad_token_id,expected",
        [
            # common case
            (
                [
                    [1, 2, 3],
                    [4, 5, 6, 7, 8, 9],
                    [10, 11],
                    [12, 13, 14],
                ],
                4,
                0,
                [
                    [1, 2, 3, 4],
                    [5, 6, 7, 8],
                    [9, 10, 11, 12],
                    [13, 14, 0, 0],
                ],
            ),
            # no padding tokens
            (
                [
                    [1, 2, 3, 4, 5],
                    [6, 7, 8, 9],
                    [10, 11],
                    [12],
                ],
                4,
                0,
                [
                    [1, 2, 3, 4],
                    [5, 6, 7, 8],
                    [9, 10, 11, 12],
                ],
            ),
            # very long first example
            (
                [
                    [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
                    [17, 18, 19],
                ],
                5,
                0,
                [
                    [1, 2, 3, 4, 5],
                    [6, 7, 8, 9, 10],
                    [11, 12, 13, 14, 15],
                    [16, 17, 18, 19, 0],
                ],
            ),
            # no data
            (
                [],
                5,
                0,
                [],
            ),
        ],
    )
    def test_pack_to_samples(self, data, token_length, pad_token_id, expected):
        generated = pack_into_samples(data, token_length, pad_token_id)
        assert np.all(np.array(generated) == np.array(expected))

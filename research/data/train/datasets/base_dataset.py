"""Base classes for defining datasets."""

from dataclasses import dataclass
from enum import Enum
from typing import Any, Iterator, Optional


class DataAccessFormat(str, Enum):
    """Underlying file or table format for data access."""

    DELTA = "delta"
    JSON = "json"
    PARQUET = "parquet"

    BIGQUERY = "bigquery"
    """BigQuery table."""


@dataclass
class Dataset:
    """Basic information of a dataset."""

    name: str
    """Name of this Dataset."""

    sources: dict[str, Any]
    """Upstream data for producing this Dataset; can be url, another Dataset, etc."""

    generator: Optional[str]
    """Path to the script, pipeline, or notebook code that generates this Dataset."""

    location: str
    """Location that saves this Dataset."""

    format: DataAccessFormat
    """File or table format of this Dataset."""

    description: Optional[str] = None
    """Description of this Dataset."""

    def __post_init__(self):
        assert self.name is not None and self.name.strip()
        assert self.location is not None and self.location.strip()
        assert isinstance(self.format, DataAccessFormat)
        DatasetRegistry.add(self)


class DatasetRegistry:
    """Registry of all datasets."""

    _registry: dict[str, Dataset] = {}
    _instance: Optional["DatasetRegistry"] = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._registry = {}
        return cls._instance

    @classmethod
    def add(cls, dataset: Dataset):
        """Add a dataset to the registry."""
        if dataset.name in cls._registry:
            raise ValueError(f"Dataset {dataset.name} already registered.")
        cls._registry[dataset.name] = dataset

    @classmethod
    def get(cls, name: str) -> Dataset:
        """Get a dataset by name."""
        return cls._registry[name]

    @classmethod
    def __iter__(cls) -> Iterator[Dataset]:
        """Iterate over the registered datasets."""
        return iter(list(cls._registry.values()))

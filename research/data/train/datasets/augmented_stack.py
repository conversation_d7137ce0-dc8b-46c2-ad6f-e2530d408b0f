"""Definitions of Augmented-Stack datasets."""

from research.data.train.datasets.base_dataset import DataAccessFormat, Dataset
from research.data.train.datasets.github import github_snapshot
from research.data.train.datasets.starcoder import starcoder_raw
from research.data.train.datasets.the_stack import the_stack_dedup

# The initial Augmented-Stack dataset, created by combining The-Stack and our
# collected GitHub data. It filters out unfavorable languages/extensions and
# low-quality files (similar to approaches used by StarCoder). Additional
# advanced data filtering, grouping, and sorting will all be based on this
# source dataset.
augmented_stack = Dataset(
    name="augmented_stack",
    sources={
        "the_stack": the_stack_dedup,
        "github": github_snapshot,
    },
    generator="research/data/train/jobs/gen_augmented_stack.py",
    location="/mnt/efs/spark-data/shared/aug-stack/v1/filtered/",
    format=DataAccessFormat.PARQUET,
    description="Initial Augmented-Stack dataset for further processing.",
)

augstack_deduped = Dataset(
    name="augstack_deduped",
    sources={
        "original": augmented_stack,
    },
    generator="research/data/train/jobs/dedupe/run_dedupe.sh",
    location="/mnt/efs/spark-data/shared/aug-stack/deduped_0_7/",
    format=DataAccessFormat.PARQUET,
    description="Soft-deduplicated Augmented-Stack dataset.",
)

augstack_by_repo = Dataset(
    name="augstack_by_repo",
    sources={
        "augstack_deduped": augstack_deduped,
        "starcoder_extra": starcoder_raw,
    },
    generator="research/data/train/jobs/gen_augstack_by_repo.py",
    location="/mnt/efs/spark-data/shared/aug-stack/by-repo/",
    format=DataAccessFormat.PARQUET,
    description="Group the deduplicated Augmented-Stack dataset by repos.",
)

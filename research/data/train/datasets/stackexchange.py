"""Definitions of various raw and and derived StackExchange datasets.

Natural language datasets all exist in bucket `nl-datasets`
"""

from research.data.train.datasets.base_dataset import DataAccessFormat, Dataset

stackexchange_qa = Dataset(
    name="stackexchange_qa",
    sources={"url": "https://archive.org/download/stackexchange/"},
    generator="research/data/collection/stackexchange/download_from_archive_org.sh",
    location="/mnt/efs/augment-nvme/data/stackexchange/stackexchange_qa/",
    format=DataAccessFormat.PARQUET,
    description="Multi-site StackExchange joined into question-answer-list pairs.",
)


stackexchange_tokenized = Dataset(
    name="stackexchange_tokenized",
    sources={"unformatted": stackexchange_qa},
    generator="research/data/train/jobs/gen_stackexchange_dataset.py",
    location="/mnt/efs/spark-data/shared/nl-datasets/stackexchange/tokenized_and_packed/",
    format=DataAccessFormat.PARQUET,
    description="StackExchange formatted into text, tokenized, and split equal sized chunks.",
)

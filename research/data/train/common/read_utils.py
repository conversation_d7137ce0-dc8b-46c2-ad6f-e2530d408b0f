"""Utilities for reading data in Spark."""

import logging

from pyspark.sql import DataFrame, SparkSession

from research.data.train.datasets.base_dataset import Dataset


def get_single_source(dataset: Dataset) -> Dataset:
    """Get the single source of a dataset."""
    if len(dataset.sources) != 1:
        raise ValueError(f"Expected a single source dataset, but got {dataset.sources}")

    return list(dataset.sources.values())[0]


def show_info(df: DataFrame):
    """Print out some basic information about the dataframe."""
    df.printSchema()

    print(f"Number of partitions: {df.rdd.getNumPartitions()}")
    print(f"Partitioner: {df.rdd.partitioner}")


def read_dataset(
    spark: SparkSession, dataset: Dataset, verbose: bool = True
) -> DataFrame:
    """Read a dataset.

    Args:
        spark: Spark session.
        dataset: Dataset object.
        verbose: Whether to print out some basic information about the dataframe.

    Returns:
        Spark DataFrame.
    """
    if verbose:
        logging.info(f"Reading dataset {dataset}")
    return read_path(spark, dataset.location, dataset.format, verbose)


def read_path(
    spark: SparkSession, path: str, src_format: str, verbose: bool = True
) -> DataFrame:
    """Read a path.

    Args:
        spark: Spark session.
        path: Path to read.
        src_format: Format of source.
        verbose: Whether to print out some basic information about the dataframe.

    Returns:
        Spark DataFrame.
    """
    if verbose:
        logging.info(f"Reading path {path} in format {src_format}")

    dataframe = spark.read.format(src_format).load(path)

    if verbose:
        show_info(dataframe)
    return dataframe

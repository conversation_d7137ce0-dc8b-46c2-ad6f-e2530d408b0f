"""Sort files within each repo to improve data quality.

See more details in:
- notion page [https://www.notion.so/Dependency-based-long-context-pretraining-for-code-dd8eff6417fa4e138224ba29734c34de]
- paper link [https://arxiv.org/abs/2310.10638]
"""

import re
from collections import Counter
from enum import Enum
from pathlib import Path
from typing import Optional, Sequence, Tuple

import numpy as np
from pyspark.sql.types import StructType

from research.data.train.common.constants import RANDOM_SEED


class FileSortingStrategy(Enum):
    """Supported strategies to sort files within each repo."""

    RANDOM = 1
    """Randomly sort the files within each repo.

    Note that this random sorting is done within the repo boundaries, so files
    in the same repo will still be placed together.
    """

    MAXIMIZE_FILENAME_MATCH = 2
    """Select the next file based on the exact filename match score.

    We count occurrences of the current filename in each file content to
    generate the scores. A file with the highest score will be selected
    as the next file in the sorted output.
    """


def sort_files_within_repo(
    file_list: Sequence[StructType],
    strategy: FileSortingStrategy,
    content_col: Optional[str] = None,
    file_path_col: Optional[str] = None,
) -> Sequence[StructType]:
    """Sort files within each repo based on the given strategy.

    Files on the right side of the output list will have dependencies on
    files on the left, e.g. if we have the output list [file1, file2, ...],
    it's likely that in file2 it imports file1 and calls some methods defined
    in file1.

    Args:
        file_list: a list of files to be sorted.
        strategy: the sorting strategy to use.
        content_col: the column name of the file content in the struct.
        file_path_col: the column name of the file path in the struct.

    Returns:
        A list of sorted files.
    """
    if strategy == FileSortingStrategy.RANDOM:
        return _random_sort(file_list)
    if strategy == FileSortingStrategy.MAXIMIZE_FILENAME_MATCH:
        if content_col is None or file_path_col is None:
            raise ValueError(
                "content_col and file_path_col must be provided when using MAXIMIZE_FILENAME_MATCH"
            )
        return _maximize_filename_match_sort(file_list, content_col, file_path_col)
    raise ValueError(f"Unknown strategy: {strategy}")


def _random_sort(file_list: Sequence[StructType]) -> Sequence[StructType]:
    """Randomly sort the files within each repo.

    See sort_files_within_repo for details about input parameters and the output.
    """
    np.random.seed(RANDOM_SEED)
    list_size = len(file_list)
    return [file_list[idx] for idx in np.random.permutation(list_size)]


def _maximize_filename_match_sort(
    file_list: Sequence[StructType],
    content_col: str,
    file_path_col: str,
) -> Sequence[StructType]:
    """Select the next file based on the exact filename match score.

    See sort_files_within_repo for details about input parameters and the output.
    """
    if len(file_list) < 2:
        return file_list

    out_edges, in_degrees = _construct_file_dependency_graph(
        file_list, content_col, file_path_col
    )

    return _traverse_graph(file_list, out_edges, in_degrees)


def _construct_file_dependency_graph(
    file_list: Sequence[StructType],
    content_col: str,
    file_path_col: str,
) -> Tuple[Sequence[Sequence[Tuple[int, int]]], Sequence[int]]:
    """Construct a file dependency graph based on the given file list.

    Returns:
        A tuple of (out_edges, in_degrees) that represents the constructed graph.

    Assuming N files in the input list, we can index them as 0, 1, ... N-1.

    out_edges[node_i] is a list of tuple (node_j, weight) where:
        - 0 <= node_i, node_j < N
        - node_j is the index of the file depending on file node_i
        - weight is the value of the edge (node_i, node_j)
        - we can apply different approaches to calculate the weight

    Values in the in_degrees list represent the in-degree of each node.
    It can in theory be calculated from out_edges. We precompute and return it
    here for better efficiency.
    """
    list_size = len(file_list)
    out_edges = [[] for _ in range(list_size)]
    in_degrees = [0 for _ in range(list_size)]

    # map from filename (without extension) to the index of the file in the input list
    filename_to_idx = {
        Path(file_list[idx][file_path_col]).stem: idx for idx in range(list_size)
    }

    token_match_regex = re.compile(r"\b[a-zA-Z0-9_]+\b")
    for to_node in range(list_size):
        tokens = token_match_regex.findall(file_list[to_node][content_col])
        token_counts = Counter(tokens)

        for token, count in token_counts.items():
            if token in filename_to_idx:
                from_node = filename_to_idx[token]
                if from_node != to_node:
                    out_edges[from_node].append((to_node, count))
                    in_degrees[to_node] += count

    return out_edges, in_degrees


def _traverse_graph(
    file_list: Sequence[StructType],
    out_edges: Sequence[Sequence[Tuple[int, int]]],
    in_degrees: Sequence[int],
) -> Sequence[StructType]:
    """Traverse a file dependency graph and return the sorted files.

    Args:
        file_list: a list of files to be sorted.
        out_edges: the file dependency graph.
        in_degrees: the in-degrees of each node in the graph.

    Returns:
        A list of sorted files based on the traversal path.
    """
    # in-degree of a visited file will be assigned the largest in-degree value + 1
    # so they won't be the next root or inserted again until all files are visited
    if len(file_list) < 2:
        return file_list
    visited = max(in_degrees) + 1

    # invalid index to indicate there is no unvisited file left
    invalid_idx = -1

    def _get_new_root():
        min_idx, min_val = invalid_idx, visited
        for idx, val in enumerate(in_degrees):
            if val < min_val:
                min_idx, min_val = idx, val
        if min_idx == invalid_idx:
            raise ValueError("No unvisited file found")
        return min_idx

    def _append_file_to_output(file_idx):
        in_degrees[file_idx] = visited
        output.append(file_list[file_idx])

    def _get_unvisited_children(file_idx):
        return [pair for pair in out_edges[file_idx] if in_degrees[pair[0]] < visited]

    output = []
    target_len = len(file_list)

    while len(output) < target_len:
        current_node = _get_new_root()

        while current_node != invalid_idx:
            _append_file_to_output(current_node)

            unvisited_children = _get_unvisited_children(current_node)
            if unvisited_children:
                # update in-degrees of files that depend on the current file
                for idx, val in unvisited_children:
                    in_degrees[idx] -= val

                current_node = max(unvisited_children, key=lambda x: x[1])[0]
            else:
                current_node = invalid_idx

    return output

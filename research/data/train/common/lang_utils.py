"""Utils for consolidating languages in The-Stack, StarCoder, and GitHub datasets.

Note that The-Stack and GitHub datasets already share the same set of languages
as listed in research/data/collection/github/controllers/languages.py
"""

from typing import List, Mapping

# List of languages in the StarCoder dataset.
# They are exactly the same as values provided in the 'lang' column in the dataset.
# There should be 86 languages + 2 formats (json & yaml) in total.
STARCODER_LANGUAGES: List[str] = [
    "ada",
    "agda",
    "alloy",
    "antlr",
    "applescript",
    "assembly",
    "augeas",
    "awk",
    "batchfile",
    "bluespec",
    "c",
    "c-sharp",
    "clojure",
    "cmake",
    "coffeescript",
    "common-lisp",
    "cpp",
    "css",
    "cuda",
    "dart",
    "dockerfile",
    "elixir",
    "elm",
    "emacs-lisp",
    "erlang",
    "f-sharp",
    "fortran",
    "glsl",
    "go",
    "groovy",
    "haskell",
    "html",
    "idris",
    "isabelle",
    "java",
    "java-server-pages",
    "javascript",
    "json",
    "julia",
    "kotlin",
    "lean",
    "literate-agda",
    "literate-coffeescript",
    "literate-haskell",
    "lua",
    "makefile",
    "maple",
    "markdown",
    "mathematica",
    "matlab",
    "ocaml",
    "pascal",
    "perl",
    "php",
    "powershell",
    "prolog",
    "protocol-buffer",
    "python",
    "r",
    "racket",
    "restructuredtext",
    "rmarkdown",
    "ruby",
    "rust",
    "sas",
    "scala",
    "scheme",
    "shell",
    "smalltalk",
    "solidity",
    "sparql",
    "sql",
    "stan",
    "standard-ml",
    "stata",
    "systemverilog",
    "tcl",
    "tcsh",
    "tex",
    "thrift",
    "typescript",
    "verilog",
    "vhdl",
    "visual-basic",
    "xslt",
    "yacc",
    "yaml",
    "zig",
]

# Extra types in the 'lang' column in the StarCoder dataset.
# GitHub issues, commits and Jupyter notebooks subsets have different columns from the rest.
STARCODER_EXTRA: List[str] = [
    "git-commits-cleaned",
    "github-issues-filtered-structured",
    "jupyter-scripts-dedup-filtered",
    "jupyter-structured-clean-dedup",
]

# Special cases for language conversion that are not covered by common rules.
SPECIAL_CONVERSIONS: Mapping[str, str] = {
    "c++": "cpp",
    "objective-c++": "objective-cpp",
}


def get_standard_language(language: str) -> str:
    """Standardize the given language name and return the result.

    Common rules:
        * All leading and trailing whitespaces are removed.
        * All characters are lowercased.
        * All spaces are replaced with dashes.
        * All # characters are replaced with '-sharp'.
    """
    std_lang = language.strip().lower().replace(" ", "-").replace("#", "-sharp")

    if std_lang in SPECIAL_CONVERSIONS:
        return SPECIAL_CONVERSIONS[std_lang]
    else:
        return std_lang

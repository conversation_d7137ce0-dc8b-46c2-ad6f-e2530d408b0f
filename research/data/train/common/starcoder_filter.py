"""Common utilities to apply StarCoder filters to a dataframe.

For details, see
https://arxiv.org/pdf/2305.06161.pdf
and
https://docs.google.com/spreadsheets/d/1Lk-pTk_rXI__fCgixr7ZWSi8wR09Zzd2j_G90J80r00/edit#gid=965720876
and
https://github.com/bigcode-project/bigcode-dataset/blob/main/preprocessing/visual_inspection.csv
"""

import re
from pathlib import Path

import pyspark.sql.functions as F
from bs4 import BeautifulSoup
from pyspark.sql import DataFrame
from pyspark.sql.types import StringType

from research.data.train.common.lang_utils import (
    STARCODER_LANGUAGES,
    get_standard_language,
)

# Additional language patterns that we failed to capture during initial etl
# Either we missed from starcoder, or it was not in starcoder but we decided
# to add it to the dataset.
ADDITIONAL_LANGUAGE_PATTERNS = {
    # Languages that are not captured or included by starcoder
    # An empty list means extensions are captured but not included in starcoder
    "asciidoc": [],
    "astro": [r".*\.astro"],
    "bazel": ["BUILD", "WORKSPACE"],
    "changelog": ["changelog"],
    "cobol": [],
    "cython": [],
    "desktop": [r".*\.desktop\.in"],
    "eda": [
        r".*\.qsf",
        r".*\.qpf",
        r".*\.qip",
        r".*\.ucf",
        r".*\.xdc",
        r".*\.sdc",
        r".*\.blif",
        r".*\.kiss2",
        r".*\.slif",
        r".*\.pla",
    ],
    "heroku": ["procfile"],
    "jsonnet": [r".*\.jsonnet", r".*\.libsonnet"],
    "jsx": [],
    "license": [
        "LICENSE",
        "COPYRIGHT",
        "COPYING",
        "NOTICE",
        r"license[_.-].*",
        r"MODULE_LICENSE_*.*",
    ],
    "llvm": [],
    "lookml": [],
    "nextjs": [r".*\.next"],
    "nuxtjs": [r".*\.nuxt"],
    "objective-c": [r".*\.m"],
    "objective-cpp": [],
    "project_files": ["manifest", "description", "namespace"],
    "readme": ["README"],
    "svelte": [r".*\.svelte"],
    "swift": [],
    "vagrantfile": ["vagrantfile"],
    "vue": [],
    "xpages": [r".*\.xsp\.metadata"],
    # Existing languages in starcoder but new patterns
    # Extensions with >1 parts are not handled correctly during initial etl
    "assembly": [r".*\.s\.in", r".*\.s"],
    "clojure": [r".*\.cljs\.hl"],
    "cmake": [r".*\.cmake\.in"],
    "dockerfile": ["dockerfile", r"dockerfile\..*"],
    "emacs-lisp": [r".*\.emacs\.desktop"],
    "html": [r".*\.html\.hl"],
    "makefile": ["makefile", r"makefile\..*"],
    "python": ["pipfile", r"requirements\.txt"],
    "restructuredtext": [r".*\.rest\.txt", r".*\.rst\.txt"],
    "ruby": ["Gemfile", "Gemspec", "rakefile"],
    "rust": [r".*\.rs\.in"],
    "shell": [r".*\.sh\.in"],
}


def update_lang(
    input_df: DataFrame,
    lang_col: str,
    path_col: str,
) -> DataFrame:
    """Update the language column of the input dataframe based on the provided path.

    This function will:
    1. capture some missing cases
    2. standardize the language names
    """

    @F.udf(StringType())
    def update_lang_udf(lang: str, path: str) -> str:
        output_lang = lang

        filename = Path(path).name
        for new_lang, patterns in ADDITIONAL_LANGUAGE_PATTERNS.items():
            if any(
                # append $ to each pattern for exact matching
                re.match(pattern + "$", filename, re.IGNORECASE)
                for pattern in patterns
            ):
                output_lang = new_lang
                break

        return get_standard_language(output_lang)

    return input_df.withColumn(
        lang_col,
        update_lang_udf(F.col(lang_col), F.col(path_col)),
    )


# Final set of selected languages
# So far it's expected to have 88 + 25 = 113 languages in total
SELECTED_LANGUAGES = [
    get_standard_language(lang)
    for lang in set(STARCODER_LANGUAGES).union(ADDITIONAL_LANGUAGE_PATTERNS.keys())
]


def filter_lang(
    input_df: DataFrame,
    lang_col: str,
) -> DataFrame:
    """Filter out rows that are not in the selected languages."""
    return input_df.filter(F.col(lang_col).isin(SELECTED_LANGUAGES))


# Extensions excluded in the StarCoder dataset
# 36 extensions are removed after manual inspection
# Check the "Include" column in the google sheet for more details
EXCLUDED_EXTENSIONS: list[str] = [
    ".al",
    ".aux",
    ".bison",
    ".cats",
    ".cdf",
    ".cl2",
    ".csx",
    ".dpr",
    ".f77",
    ".frg",
    ".frx",
    ".geo",
    ".grt",
    ".ihlp",
    ".ltx",
    ".nbp",
    ".pck",
    ".ph",
    ".pls",
    ".plx",
    ".rest",
    ".rkt",
    ".rktd",
    ".rktl",
    ".sexp",
    ".sig",
    ".sthlp",
    ".tab",
    ".toc",
    ".vhf",
    ".vht",
    ".vrx",
    ".w",
    ".wlt",
    ".xrl",
    ".yy",
]


def filter_ext(
    input_df: DataFrame,
    path_col: str,
) -> DataFrame:
    """Filter out paths that contain the excluded extensions."""
    # Escape the preceding dot in each extension for regex matching
    excluded_ext_regex = ["\\" + ext for ext in EXCLUDED_EXTENSIONS]
    return input_df.filter(~F.col(path_col).rlike(f"({'|'.join(excluded_ext_regex)})$"))


# Minimum alphanumeric fraction for a file to be considered
# Filtering will be applied to all files unless specified below
# Check the "Alphanum_threshold" column in the google sheet
MIN_ALPHANUM = 0.25
NO_MIN_ALPHANUM_EXT = [
    ".nb",
    ".ma",
    ".frm",
    ".sas",
]


def filter_alphanum_udf(path: str, alphanum: float) -> bool:
    """Filter out rows that do not meet the minimum alphanumeric fraction."""
    ext = Path(path).suffix
    if ext in NO_MIN_ALPHANUM_EXT:
        return True
    return alphanum >= MIN_ALPHANUM


# Minimum alphabetic fraction for a file to be considered
# Filtering will be applied to selected extensions only
# Check the "Alpha filter" column in the google sheet
MIN_ALPHA = 0.25
MIN_ALPHA_EXT = [
    ".ads",
    ".aug",
    ".bas",
    ".fp",
    ".ma",
    ".matlab",
    ".mpl",
    ".nb",
    ".prc",
    ".scm",
    ".sld",
    ".sps",
    ".sv",
    ".vhd",
    ".wl",
    ".y",
]

CUSTOM_MIN_ALPHA = {
    ".json": 0.5,
    ".yaml": 0.5,
    ".yml": 0.5,
}


def filter_alpha_udf(path: str, content: str, size: int) -> bool:
    """Filter out rows that do not meet the minimum alphabetic fraction."""
    ext = Path(path).suffix
    if ext in MIN_ALPHA_EXT or ext in CUSTOM_MIN_ALPHA:
        alphabets = re.findall(r"[a-zA-Z]", content)
        min_alpha = MIN_ALPHA if ext in MIN_ALPHA_EXT else CUSTOM_MIN_ALPHA[ext]
        return len(alphabets) >= min_alpha * size
    return True


# XML pattern for filtering files
# Only match the pattern within the first 100 characters of each file
# Check the "XML filter" column in the google sheet
XML_PATTERN = "<?xml version="
# Do not apply XML filter to below languages
NO_XML_FILTER_LANG = [
    "xslt",
]


def filter_xml_udf(lang: str, content: str) -> bool:
    """Filter out rows that contain XML."""
    if lang in NO_XML_FILTER_LANG:
        return True
    return not (XML_PATTERN in content[:100])


def filter_html_udf(lang: str, content: str, size: int, source: str) -> bool:
    """Filter out rows that contain low-quality HTML."""
    # HTML files from the github source have already been converted using html2text
    if source == "github" or lang != "html":
        return True

    try:
        soup = BeautifulSoup(content, features="html.parser")
    except:  # noqa: E722; pylint: disable=W0702
        return False

    # kill all script and style elements
    for script in soup(["script", "style"]):
        script.extract()

    text_len = len(soup.get_text())
    return (text_len > 100) and (text_len / size > 0.2)


# Line length limits for filtering files
# Check the "Long_line_threshold" column in the google sheet
MAX_LINE_LEN_LIMIT = 1000
AVG_LINE_LEN_LIMIT = 100
# Do not apply line length limit to below extensions
NO_LINE_LEN_LIMIT_EXT = [
    ".ado",
    ".asm",
    ".bbx",
    ".bib",
    ".bsv",
    ".cbx",
    ".cmake",
    ".do",
    ".dtx",
    ".frm",
    ".ins",
    ".json",
    ".lbx",
    ".ma",
    ".markdown",
    ".matlab",
    ".md",
    ".mkd",
    ".mkdn",
    ".mkii",
    ".mkiv",
    ".mkvi",
    ".nb",
    ".r",
    ".rmd",
    ".ron",
    ".rst",
    ".sql",
    ".sty",
    ".tex",
]


def filter_line_len_udf(path: str, max_line_len: int, avg_line_len: float) -> bool:
    """Filter out rows that contain long lines."""
    ext = Path(path).suffix
    if ext in NO_LINE_LEN_LIMIT_EXT:
        return True
    return (max_line_len <= MAX_LINE_LEN_LIMIT) and (avg_line_len <= AVG_LINE_LEN_LIMIT)


def filter_json_yaml_udf(lang: str, size: int) -> bool:
    """Filter out rows that contain low-quality JSON or YAML."""
    if lang != "json" and lang != "yaml":
        return True
    return 100 <= size and size <= 5000

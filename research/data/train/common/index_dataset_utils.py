"""Utilities to generate indexed dataset from a dataframe."""

import logging
import shutil
from pathlib import Path
from tempfile import TemporaryDirectory
from typing import Sequence

import megatron.data.indexed_dataset as indexed_dataset
from pyspark import TaskContext
from pyspark.sql import SparkSession
from pyspark.sql.types import ArrayType, BinaryType

from research.core.utils_for_s3 import cleanup_dir, get_s3_client
from research.data.train.common.constants import RANDOM_SEED
from research.data.train.common.pack_utils import unpack_tokens

INDEXED_DATASET_IMPL = "mmap"

NUM_TRAINING_OUTPUTS = 300
NUM_VALIDATION_OUTPUTS = 10


def index_and_save_partition(
    partition: list,
    dir_path: str,
    vocab_size: int,
    column_name: str,
    is_train: bool,
    unpack: bool = True,
):
    """Index and save a Spark partition.

    Args:
        partition: Spark partition.
        dir_path: Directory path to save the indexed dataset.
        vocab_size: Vocabulary size.
        column_name: Column name of the content in the dataframe.
        is_train: Whether this is a training dataset.

    Returns:
        Number of samples in the indexed dataset.
        Path to the bin file.
        Path to the idx file.
    """
    logging.info(f"Building indexed dataset to {dir_path}")

    task_context = TaskContext.get()
    if task_context is None:
        raise ValueError("TaskContext is None")
    partition_id = task_context.partitionId()

    if is_train:
        file_prefix = f"training_{partition_id:0>5}"
        logging.info(f"Building training dataset {file_prefix}")
    else:
        file_prefix = f"validation_{partition_id:0>5}"
        logging.info(f"Building validation dataset {file_prefix}")

    bin_path = Path(dir_path) / f"{file_prefix}.bin"
    idx_path = Path(dir_path) / f"{file_prefix}.idx"

    builder = indexed_dataset.make_builder(
        out_file=str(bin_path),
        impl=INDEXED_DATASET_IMPL,
        vocab_size=vocab_size,
    )

    count = 0
    for row in partition:
        count += 1
        if unpack:
            tokens = unpack_tokens(row[column_name])
        else:
            tokens = row[column_name]
        builder.add_item(tokens)
        builder.end_document()
    builder.finalize(str(idx_path))

    return count, bin_path, idx_path


def get_fs_writer(
    output_dir: str,
    vocab_size: int,
    column_name: str,
    is_train: bool,
    unpack: bool = True,
):
    """This returns a function that creates bin and idx files for a Spark partition.

    Results will be directly written to a shared filesystem.
    """

    def writer_impl(partition):
        count, bin_path, idx_path = index_and_save_partition(
            partition, output_dir, vocab_size, column_name, is_train, unpack=unpack
        )
        logging.info(f"Saved {count} samples in {bin_path} and {idx_path}")

    return writer_impl


def get_s3_writer(
    bucket: str,
    subdir: str,
    vocab_size: int,
    column_name: str,
    is_train: bool,
    access_key: str,
    secret_key: str,
):
    """This returns a function that creates bin and idx files for a Spark partition.

    Results will first be written to a temporary directory and then uploaded to S3.
    """
    if subdir.endswith("/"):
        subdir = subdir[:-1]

    def writer_impl(partition):
        s3 = get_s3_client(access_key, secret_key)

        with TemporaryDirectory() as tmpdir:
            count, bin_path, idx_path = index_and_save_partition(
                partition, tmpdir, vocab_size, column_name, is_train
            )

            if count > 0:
                logging.info(f"Uploading {count} samples to S3")
                s3.upload_file(str(bin_path), bucket, f"{subdir}/{bin_path.name}")
                s3.upload_file(str(idx_path), bucket, f"{subdir}/{idx_path.name}")

    return writer_impl


def get_train_valid_dfs(
    spark: SparkSession,
    input_path: str | Sequence[str],
    validation_count: int,
):
    """Get training and validation dataframes.

    Args:
        spark: Spark session.
        input_path: Path to the input dataframe.
        validation_count: Number of validation samples.
    """
    if isinstance(input_path, str):
        input_path = [input_path]
    df = spark.read.parquet(*input_path)

    if len(df.columns) != 1:
        raise ValueError(
            f"Expected a single column dataframe, but got {len(df.columns)}"
        )

    num_samples = df.count()
    if num_samples < validation_count:
        raise ValueError(
            f"Number of total samples {num_samples} is smaller than validation count {validation_count}"
        )

    validation_pct = 1.0 * validation_count / num_samples
    train_df, valid_df = df.randomSplit(
        [1.0 - validation_pct, validation_pct], RANDOM_SEED
    )

    return train_df, valid_df


def generate_indexed_dataset_fs(
    spark: SparkSession,
    input_path: str | Sequence[str],
    output_dir: str,
    vocab_size: int,
    validation_count: int,
):
    """Generate training/validation indexed dataset from a dataframe to shared filesystem.

    Args:
        spark: Spark session.
        input_path: Path to the input dataframe.
        output_dir: Directory where the output indexed dataset will be stored.
        vocab_size: Vocabulary size.
        validation_count: Number of validation samples.
    """
    logging.info(f"Generating indexed datasets from {input_path}")
    logging.info(f"Outputs will be saved to {output_dir}")
    logging.info(f"{validation_count} samples will be the validation set")

    train_df, valid_df = get_train_valid_dfs(spark, input_path, validation_count)
    column_name = train_df.columns[0]

    # Check column schema to see if it is an array of integers, or a bytearray.
    if isinstance(train_df.schema[column_name].dataType, ArrayType):
        unpack = False
        logging.info(f"Column {column_name} is an array; will not unpack")
    elif isinstance(train_df.schema[column_name].dataType, BinaryType):
        unpack = True
        logging.info(f"Column {column_name} is a bytearray; will unpack")
    else:
        raise ValueError(
            f"Expected a column of type array of integers or bytearray, but got {train_df.schema[column_name].dataType}"
        )
    output_path = Path(output_dir)
    if output_path.exists():
        shutil.rmtree(output_dir, ignore_errors=True)
    output_path.mkdir(parents=True, exist_ok=True)

    train_df.coalesce(NUM_TRAINING_OUTPUTS).foreachPartition(
        get_fs_writer(output_dir, vocab_size, column_name, is_train=True, unpack=unpack)
    )
    if validation_count > 0:
        valid_df.coalesce(NUM_VALIDATION_OUTPUTS).foreachPartition(
            get_fs_writer(
                output_dir, vocab_size, column_name, is_train=False, unpack=unpack
            )
        )


def generate_indexed_dataset_s3(
    spark: SparkSession,
    input_path: str,
    output_bucket: str,
    output_subdir: str,
    vocab_size: int,
    validation_count: int,
):
    """Generate training/validation indexed dataset from a dataframe to s3.

    Args:
        spark: Spark session.
        input_path: Path to the input dataframe.
        output_bucket: Bucket where the output indexed dataset will be stored.
        output_subdir: Subdirectory where the output will be stored.
        vocab_size: Vocabulary size.
        validation_count: Number of validation samples.
    """
    logging.info(f"Generating indexed datasets from {input_path}")
    logging.info(f"Outputs will be saved in {output_bucket}/{output_subdir}")
    logging.info(f"{validation_count} samples will be the validation set")

    train_df, valid_df = get_train_valid_dfs(spark, input_path, validation_count)
    column_name = train_df.columns[0]

    access_key = str(spark.conf.get("spark.hadoop.fs.s3a.access.key") or "")
    secret_key = str(spark.conf.get("spark.hadoop.fs.s3a.secret.key") or "")

    cleanup_dir(output_bucket, output_subdir, access_key, secret_key)

    train_df.coalesce(NUM_TRAINING_OUTPUTS).foreachPartition(
        get_s3_writer(
            output_bucket,
            output_subdir,
            vocab_size,
            column_name,
            is_train=True,
            access_key=access_key,
            secret_key=secret_key,
        )
    )
    valid_df.coalesce(NUM_VALIDATION_OUTPUTS).foreachPartition(
        get_s3_writer(
            output_bucket,
            output_subdir,
            vocab_size,
            column_name,
            is_train=False,
            access_key=access_key,
            secret_key=secret_key,
        )
    )

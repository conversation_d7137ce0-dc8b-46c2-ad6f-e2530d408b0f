"""List of users and repos to exclude from training datasets."""

USER_DENYLIST = [
    "scalablytyped",
    "openscriptures",
    "codexstanford",
    "0x736e",
    "cirosantilli",
    "sillsdev",
    "jar<PERSON><PERSON><PERSON><PERSON><PERSON>",
    "api-evangelist",
    "trellixvulnteam",
]

REPO_DENYLIST = [
    "andrewdefries/andrewdefries.github.io",
    "arpecop/big-content",
    "cryptoanarchywiki/mailing-list-archive-generator",
    "data-sources-exemples/macrodatabase",
    "dli-invest/fdrtt-stream",
    "edipermadi/music",
    "hugmouse/tdc-exported",
    "jaalte/dvcf22-digital-garden",
    "jonnyg23/mahabrain",
    "klishgroup/prose-pogs",
    "linuxhw/testdays",
    "mackorone/spotify-playlist-archive",
    "marinade-finance/staking-status",
    "netwrck/awesome-ai-chatbots",
    "nodebe4/opinion",
    "oeuvres/oeuvres.github.io",
    "richgel999/ufo_data",
    "rugk/gesetze",
    "samysberg/marklisma",
    "seed-info/apt-info-sub",
    "thaichau91/qewq",
    "tytydraco/yessleep",
    "wikipathways/pfocr-database",
    "mathiasbynens/unicode-data",
    "2947721120/cdnjs",
    "davidedc/js-lpc-speech-synthesizer-demo",
    "jgallen23/jquery-builder",
    "stdlib-js/www",
    "as-com/jsdelivr-archive",
    "pderakhshanfar/emse-bbc-experiment",
    "garghub/cerebro",
    "leusonmario/2022phdthesis",
    "thielelab/metaboreport",
    "hughbe/windows-sdk-headers",
    "ibm/fdr-generator",
    "eaaskt/nlu",
    "fahad1121/mmij",
    "fjmper/phd-outliers",
    "zxiaofan/jdk",
    "zacpereira/ciscoyang",
    "claudeyj/patch_correctness",
    "jarocho105/pre2",
    "chixiaye/valextractor",
    "4thel00z/microsoft-crap-that-doesnt-work",
    "cf-testorg/aws-sdk-java-test",
    "andreas237/androidpolicyautomation",
    "sealuzh/termite-replication",
    "ouyang-w-19/decogo",
    "sceccode/cfm",
    "sceccode/cfm_web",
    "shahinhonarvar/turbulence-benchmark",
    "iantal/androidpermissions",
    "maddoudou22/test-aws-sdk-java",
    "carpedmt/deadeye",
    "lfspkg/lfs2",
    "burdeyniyb/releaseglowofspeed",
    "mali5820k/uninterrupted_traffic_flow",
    "dopcn/just_a_dumper",
    "conholdate/products.conholdate.com",
    "thadafinser/useragentparsercomparison",
    "inpluslab/reentrancystudy-data",
    "w1ndness/swde-dataset",
    "goc-spending/data-corporations",
    "anatolii331/anatolii331.github.io",
    "jumpstartgeorgia/job-announcement-scraper",
    "groupdocs-conversion/groupdocs.conversion-products",
    "sccoco/smartcoco",
    "volt72/dockerfiles",
    "wildfly-swarm/wildfly-swarm-javadocs",
    "kiang/foundations_data",
    "jhipster/documentation-archive-v1-to-v5",
    "cryptodefect/artifact",
    "gugod/vote-watch-2018",
    "whosonfirst-data/whosonfirst-data-admin-in",
    "goc-spending/goc-spending-data",
    "conda-forge/repodata-shards",
    "sony/nnabla-doc",
    "trinodb/docs.trino.io",
    "npocmaka/windows-server-2003",  # This is a leak! don't use it!
    "selfrender/windows-server-2003",  # same
    "33kk/uso-archive",
    "nodejs/i18n",
    "ishiura-compiler/cf3",
    "ezhil-language-foundation/uliveeran",
    "imaiguo/imaiguo.github.io",
    "daodao10/chart",
    "eliranwong/openHebrewbible",
    "alexander-berg/2022-tests-examples",
    "alexander-berg/2022-tests-examples-2",
    "changeworld/azure-docs.it-",
    "soumyadipdas37/finescoop.github.io",
    "kaixy1/sabbath-school-lessons",
    "zhzhzhy/weibohot_history",
    "christopheredwards/vxvista",
    "schapplm/robsynth-serroblib",
    "worldvista/vista-vehu-m",
    "moltenstar/ue4.26-customrendering",
    "darkxraven/kernel_realme_titan",  # This is GPL!
    "sirepi/azure-docs.it-it",
    "sizeofvoid/ifconfigd",
    "toak/sabbath-school-lessons",
    "charitha22/hybf-cc23-artifact",
    "mitchellolsthoorn/icsme-research-2022-syntest-security-conditions-replication",
    "rcvd/interconnected-markdown",
    "feenkcom/gtoolkit-crasher",
    "luoxuhai/chinese-novel",
    "beardedtim/bible-study",
    "fracz/refactor-extractor",
    "rosoareslv/sed99",
    "unicode-org/cldr-staging",
    "ashutosh0gupta/llvm_bmc",
    "groupdocs-signature/groupdocs.signature-products",
    "tarikmanoar/html",
    "mingruji6388012/sp2023-pygress",
    "libertysoft4/conspiracy-text-post-archive",
    "spear-se/loginbugreportsempirical_data",
    "zhiyuanjia/wobench",
    "freelawproject/opinionated",
    "lambdamusic/dbpedialinks",
    "jumpstartgeorgia/real-estate-scraper",
    "jiwanger/note",
    "smarttests/smarttest",
    "agroce/fuzzgoattriage",
    "hazardsec/vulnerability-management",
    "pypi-data/pypi-json-data",
    "allenyep/baidu_hor_rank_crawler",
    "renovate-bot/googleapis-gen",
    "learncheme/mathematica_simulations",
    "bgoonz/usefulresourcerepo2.0",
    "mestiti/project_scrum_symfony",
    "nagaikenshin/schemaorg",
    "changmg/vacsem",
    "sajjadzaidi/autorepair",
    "nguyenquangthang1997/gas_saver_visualization",
    "unpackdev/downloader",
    "feriamir88/zettomellygioe",
    "cch1999/protein-stability",
    "abgoyal-archive/ot_w969",
    "sgtest/monorepo-test-3",
    "jenarvaezg/eth-scrapper",
    "lambdamusic/ontospy-examples",
    "ios-xr/model-driven-telemetry",
    "tdurieux/smartbugs-wild",
    "smartbugs/smartbugs-wild",
    "giulianobelinassi/lto-timing-analysis",
    "execunix/vinos",  # this contains gpl
    "dendaxd/qaoa-maxcut-amplitudes",
    "ori-community/wotw-rando-client",
    "dtcxzyw/llvm-codegen-benchmark",
    "ysak-y/flutter_audio_capture",
    "wilseypa/odroidnetworking",
    "typophile/typophile.github.io",
    "thegithubjoshua/headers.joshuawhe.online",
    "twitter/metrics",
    "tongshitong/mengdigua_xiangmu",
    "tomvansteijn/mf6shell",
    "sslab-gatech/janus",
    "vmexit/trio-sosp23-ae",
    "zippenk/zippenk.github.io",
    "whosonfirst-data/whosonfirst-data-admin-pk",
    "erikfig/chatbot",
    "arfc/2022-yardas-ms",
    "sjfandrews/mr_adphenome",
    "beatzevo/repo-info-master",
    "dplbsd/soc2013",
    "blackoutjack/jamtests",
    "jiyushe/jiyu-example-sentence-source",
    "joliebig/featurehouse_fstmerge_examples",
    "mechatronicbeing/resources-geographic-data",
    "unittestbot/juliet-java-test-suite",
    "raincross7/code-similarity",
    "snu-sf/crellvm-tests",
    "saveourtool/juliet-benchmark-java",
    "rokn/count_words_2015",
    "hyperledger-gerrit-archive/fabric-gerrit",
    "dheeraj-nalapat/seasoc",
    "power-grid-lib/pglib-opf",
    "gwak2837/linuxassignment3",
    "yasirroni/pypglib",
    "korenmiklos/106",
    "shan-huang-1993/plc-pyramid",
    "akh1lesh/object_detection_using_intel_realsense",
    "msiedlarek/minimal-linux",  # This is GPL!
    "nwchemgit/archivedforum",
    "jakkunight/gts6lwifi-kernel",  # this is not open source!
    "memade/chromium",
    "zd938780519/srctwo",
    "nwjs/chromium.src",
    "xueqiya/chromium_src",
    "mxobs/deb-pkg_trusty_chromium-browser",
    "jasonhan-vassar/chromium",
    "revyos/chromium-109.0.5414.119",  # chrome clones
    "dingjingmaster/kernel",
    "dingjingmaster/kernel-2.6",
    "zeyvier/taraid",
    "msiedlarek/minimal-linux",
    "junke365/rockchip-rk3588-legacy",  # this is or has linux
    "travismadill/canadianlaws",
    "zharzinhoo/kernel-oriente-guamp",
    "bbti-tech/darkromkernel",
    "velicharlagokulkumar/freedom",
    "azure/azure-sdk-for-python",
    "hyperion4ik/bsd_kernel",
    "tombibsd/netbsd-src",  # bsd clones
    "tianyuzhou95/proj121-page-table-using-hashtable",
    "ric2b/vivaldi-browser",
    "jakkunight/gts6lwifi-kernel",
    "serpcompany/serp.media",
    "publicdocs/us-scotus-flite",
    "huaweicloud/huaweicloud-sdk-python-v3",
    "groupdocs-viewer/groupdocs.viewer-products",
    "lizetteo/tracking.virtually.unlimited.number.tokens.and.addresses",
]

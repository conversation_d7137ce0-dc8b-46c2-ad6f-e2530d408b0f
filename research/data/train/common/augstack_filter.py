"""Common filters for augstack dataset."""

import math
import re
from collections import Counter, defaultdict
from typing import Sequence

import pyspark.sql.functions as F
import zstandard as zstd
from pyspark.sql import DataFrame

from research.data.train.common.sort_files_within_repo import (
    FileSortingStrategy,
    sort_files_within_repo,
)
from research.data.train.common.user_repo_denylists import REPO_DENYLIST, USER_DENYLIST

# Constants for filtering rules
WORD_PATTERN = re.compile(r"\b\w+\b")
GENERATED_PATTERN = r"(?i)(autogenerated|auto-generated|do not edit|do not modify|generated with|generated by|generated automatically|automatically generated|file is generated)"
GENERATED_PATH_PATTERN = r"(?i)(?:^|.*/)(?:generated|autogen|gen)/.*"
VERSIONED_PATTERN = r"\d+\.\d+.*\/.*\/.*\."
GENERATED_CHECK_LEN = 200
COMPRESSION_LEVEL = 16
REQUIRE_STARS = ["javascript", "css", "html", "json", "yaml"]

# Mostly languages that we have AST filtering.
# Removing C and Javascript because we found large amount of files duplicated
# between repos for these two languages.
REPO_DEDUPE_LANGS = [
    "python",
    "java",
    "c-sharp",
    "cpp",
    "go",
    "rust",
    "typescript",
    "swift",
    "ruby",
    "kotlin",
    "haskell",
    "erlang",
    "scala",
]


def filter_denylist(
    df: DataFrame, repo_name_col: str = "repo_name", lang_col: str = "lang"
) -> DataFrame:
    """Apply deny list filers.

    - Exclude repositories in the deny list.
    - Exclude user repositories in the deny list.
    - Exclude generated code from static website repos, but keep the source markdown.
    """
    return df.filter(
        ~F.col(repo_name_col).isin(REPO_DENYLIST)
        & ~(
            (
                F.col(repo_name_col).rlike(r"^.*\.github\.io$")
                | F.col(repo_name_col).rlike(r"^.*\.com$")
                | F.col(repo_name_col).rlike(r"^.*\.org$")
            )
            & ~F.col(lang_col).isin(["markdown"])
        )
        & ~F.split(F.col(repo_name_col), "/")[0].isin(USER_DENYLIST)
    )


def filter_autogen(
    df: DataFrame, content_col: str = "content", file_path_col: str = "repo_path"
) -> DataFrame:
    """Remove automatically generated code."""
    return (
        df.withColumn(
            "is_autogenerated",
            F.when(
                (
                    F.substring(F.col(content_col), 1, GENERATED_CHECK_LEN).rlike(
                        GENERATED_PATTERN
                    )
                    | F.col(file_path_col).rlike(GENERATED_PATH_PATTERN)
                ),
                True,
            ).otherwise(False),
        )
        .withColumn("is_versioned", F.col(file_path_col).rlike(VERSIONED_PATTERN))
        .filter(~F.col("is_autogenerated") & ~F.col("is_versioned"))
        .drop("is_autogenerated", "is_versioned")
    )


def filter_content_quality(
    df: DataFrame,
    content_col: str = "content",
    max_entropy: float = 0.90,
    min_entropy: float = 0.40,
    min_entropy_large: float = 0.56,  # min entropy for large files
    max_compress_ratio: float = 10,
    min_compress_ratio: float = 2.5,
    size_threshold: int = 20000,
    size_limit: float = 5e6,
    soft_size_limit: float = 9e5,
    compression_level: int = COMPRESSION_LEVEL,
) -> DataFrame:
    """Filter out rows that do not meet the content quality conditions."""

    @F.udf(
        returnType="struct<word_entropy double, compression_ratio double, raw_size long, compressed_size long>"
    )
    def content_quality_stats(content: str):
        """Filter out rows that do not meet the content quality conditions.

        This is a rowwise function that is applied to each files.
        """
        ctx = zstd.ZstdCompressor(level=compression_level)
        words = WORD_PATTERN.findall(content)
        total_word_count = len(words)
        frequencies = Counter(words)
        # Convert frequencies to probabilities
        probabilities = [f / total_word_count for f in frequencies.values()]
        # Calculate entropy using probabilities
        word_entropy = -sum(
            p * math.log2(p) for p in probabilities if p > 0
        )  # Ensure p > 0 to avoid log2(0)
        # Normalize the entropy by dividing by log2(total_word_count)
        word_entropy = (
            word_entropy / math.log2(total_word_count) if total_word_count > 1 else 0.0
        )  # Check total_word_count > 1 to avoid division by zero

        btext = content.encode("utf-8")
        original_size = len(btext)
        compressed_size = len(ctx.compress(btext))
        compression_ratio = original_size / compressed_size

        return (
            word_entropy,
            compression_ratio,
            original_size,
            compressed_size,
        )

    return df.withColumn(
        "content_quality", content_quality_stats(F.col(content_col))
    ).filter(
        (F.col("size") < size_limit)
        & (
            (F.col("content_quality.word_entropy") > min_entropy)
            & (F.col("size") < soft_size_limit)
            | (F.col("content_quality.word_entropy") > min_entropy_large)
        )
        & (F.col("content_quality.word_entropy") < max_entropy)
        & (
            (F.col("content_quality.compression_ratio") > min_compress_ratio)
            | (F.col("size") < size_threshold)
        )
        & (F.col("content_quality.compression_ratio") < max_compress_ratio)
    )


def filter_stars(
    df: DataFrame, stars_col: str = "stars_count", lang_col: str = "lang"
) -> DataFrame:
    """Filter out rows that do not have stars if the language is in REQUIRE_STARS."""
    return df.filter(
        (
            F.col(stars_col).isNull()
            | ~F.col(lang_col).isin(REQUIRE_STARS)
            | (F.col(stars_col) > 0)
        )
        & ((F.col("size") < 1e6) | (F.col("stars_count") > 1))
    )


def filter_data(
    df: DataFrame,
    lang_col: str = "lang",
    path_col: str = "repo_path",
    size_col: str = "size",
    size_limit: int = 100000,
    filter_lang: Sequence[str] = ("sql"),
    data_dir_names: Sequence[str] = (
        "data",
        "data-raw",
        "output",
        "result",
        "results",
        "build",
        "out",
        "dist",
        "compiled",
        "transpiled",
        "es5",
        "es6",
    ),
) -> DataFrame:
    """Filter out rows that do not meet the content quality conditions."""
    data_cond = F.lower(path_col).startswith("dataset/")
    for data_dir_name in data_dir_names:
        data_cond = (
            data_cond
            | F.lower(path_col).contains(f"/{data_dir_name}/")
            | F.lower(path_col).startswith(f"{data_dir_name}/")
        )
    return df.filter(
        (~F.lower(lang_col).isin(filter_lang) | (F.col(size_col) < size_limit))
        & ~data_cond
    )


def get_prune_file_udf(schema, file_path_col="repo_path", mult_threshold=500):
    @F.udf(schema)
    def prune_file_tree(files):
        """Prune directories with extremely high multiplicity.

        We count the number of files and subdirectories in each directory.
        If the number is higher than this value, the directory is removed.

        In general, this indicates autogenerated content.
        """
        mult = defaultdict(int)  # folder multiplicity
        for file_item in files:
            path = file_item[file_path_col]
            if "/" not in path:
                continue
            parent = path.rsplit("/", 1)[0]
            mult[parent] += 1
        kept = []
        for file_item in files:
            path = file_item[file_path_col]
            if "/" not in path:
                kept.append(file_item)
                continue
            parent = path.rsplit("/", 1)[0]
            if mult[parent] > mult_threshold:
                continue
            kept.append(file_item)
        return kept

    return prune_file_tree


def get_repo_analysis_udf(content_col="content", compression_level=16):
    @F.udf("struct<raw_size long, compressed_size long, compression_ratio double>")
    def repo_analysis_udf(files):
        cctx = zstd.ZstdCompressor(level=compression_level)
        repo_str = ""
        for file_item in files:
            repo_str += file_item[content_col] + "\n"
        btext = repo_str.encode("utf-8")
        original_size = len(btext)
        compressed_size = len(cctx.compress(btext))
        return original_size, compressed_size, original_size / compressed_size

    return repo_analysis_udf


def get_organize_repo_udf(file_path_col, content_column, size_limit):
    """Create an UDF that sorts repo files, and then chunk them into sets of related files.

    Create a new chunk whenever the total size goes beyond SIZE_LIMIT."""

    @F.udf(f"array< array< struct<{file_path_col} string, {content_column} string> > >")
    def organize_repo(files):
        """Sort repo files, and then chunk them into sets of related files.

        Create a new chunk whenever the total size goes beyond SIZE_LIMIT."""
        sorted_files = sort_files_within_repo(
            files,
            FileSortingStrategy.MAXIMIZE_FILENAME_MATCH,
            content_column,
            file_path_col,
        )
        current_chunk = []
        chunks = []
        total_size = 0
        for file_item in sorted_files:
            current_chunk.append(file_item)
            total_size += len(file_item[content_column])
            if total_size >= size_limit:
                total_size = 0
                chunks.append(current_chunk)
                current_chunk = []
        if current_chunk:
            chunks.append(current_chunk)
        return chunks

    return organize_repo

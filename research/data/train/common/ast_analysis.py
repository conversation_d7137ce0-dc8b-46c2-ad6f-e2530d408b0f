"""AST level filters for various languages."""

from dataclasses import dataclass
from pathlib import Path

import pyspark.sql.functions as F
import tree_sitter as ts
from pyspark.sql import DataFrame
from pyspark.sql.types import StringType

from base.static_analysis.common import (
    AllSupportedLanguages,
    LanguageID,
    guess_lang_from_fp,
    iter_ts_nodes_via_dfs,
)


@dataclass
class ASTStats:
    """Basic stats of a code block."""

    size: int
    """Size of the code block in characters."""

    root_error: bool
    """Whether the root node is an error."""

    errors: int
    """Number of error nodes."""

    max_mult: int
    """The largest number of direct children a node may have across the tree."""

    max_leaf: int
    """The largest size of a leaf or string node, not counting comments."""

    max_depth: int
    """The largest depth of the tree."""

    total_comment_size: int
    """Total number of characters from comments."""

    n_func: int
    """Number of functions and methods."""

    n_class: int
    """Number of classes, templates, structs etc."""


AST_LANG: tuple[LanguageID, ...] = (
    "python",
    "java",
    "cpp",
    "javascript",
    "typescript",
    "go",
    "rust",
    "c_sharp",
    "php",
    "scala",
    "ruby",
    "lua",
    "kotlin",
)
"""All the languages that we would do static analysis on"""


def get_lang(path: str) -> LanguageID | None:
    """Get the language of the file.

    We leverage static_analysis to guess the language, but
    in addition disable all languages that are not in `AST_LANG`.
    """
    lang = guess_lang_from_fp(path)
    if lang is None or lang not in AST_LANG:
        return None
    return lang


FUNCTION_TAGS = ("constructor_declaration",)
"""possible node types for functions and methods that don't
contain the words "method" or "function" """


CLASS_TAGS = (
    "template_definition",
    "interface_declaration",
    "namespace_definition",
    "internal_module",
    "type_declaration",
    "impl_item",
    "trait_item",
    "mod_item",
)
"""Possible node types for classes, types and interfaces that
don't contain the words "class", "struct" or "enum" """


SUPRESS_ERROR = ("cpp", "c_sharp", "lua")
"""Languages that we would like to suppress errors.

For C/C++/C# this is because the prevailent use of namespaces and header files;
since we do not preprocess, symbols defined in header files would become ERROR
because in treesitter due to them being undefined identifiers.
"""

DOCSTRING_PARENTS = ("class_definition", "function_definition", "module")
"""Possible parent node types for docstrings.

For now we only consider class, function or module dostrings.
"""


def is_docstring(node: ts.Node, lang: LanguageID):
    """Check if the given node is a docstring based on its parent node type and position.

    This is Python specific.   A docstring is a literal string expression that is
    directly nested in a function, class or module.
    """
    if lang != "python" or node.type != "string":
        return False
    parent = node.parent
    if not parent or parent.type != "expression_statement":
        return False
    block = parent.parent
    if not block or block.type not in DOCSTRING_PARENTS:
        return False
    return True


def get_code_stats(code: str, path: str, lang: str):
    """Get basic stats of a code block.

    Stats include:
        errors: Number of syntax errors
        max_mult: The largest number of children a node may have across the tree.  Large is bad.
        max_leaf: The largest size of a leaf node, not counting comments.  Large is bad.
        max_depth: The largest depth of the tree.  Large is bad.
        total_comment_size:  Total number of characters from comments.
        n_func: Number of functions and methods
        n_class: Number of classes, templates, structs etc
    """
    from base.static_analysis.parsing import GlobalParser

    if not lang:
        return None
    # C is a special case
    if lang == "c":
        lang = "cpp"
    if lang not in AllSupportedLanguages:
        return None
    tree = GlobalParser.parse_ts_tree(code, lang, Path(path))
    errors = 0
    comment_size = 0
    function_count = 0
    class_count = 0
    for node in iter_ts_nodes_via_dfs(tree.root_node):
        if "comment" in node.type:
            comment_size += len(node.text)
        if node.type == "ERROR" and lang not in SUPRESS_ERROR:
            errors += 1

        # Python is special in that much of the comments are docstrings, which
        # are technically string expressions, not comments.
        # The strings are nested directly in a `expression_statement` node with no
        # additional parent, which indicates a string literal.
        # This string literal is directly nested in a scope node that is a
        # function (also includes methods), class or module.
        if lang == "python" and is_docstring(node, lang):
            comment_size += len(node.text)

        if (
            "function" in node.type
            or "method" in node.type
            or node.type in FUNCTION_TAGS
        ):
            function_count += 1
        if (
            "class" in node.type
            or "struct" in node.type
            or "enum" in node.type
            or node.type in CLASS_TAGS
        ):
            class_count += 1

    depth_limit = 200

    def rec_stats(n: ts.Node, level: int):
        """Recursively collect whole tree statistics."""
        if n.children:
            subtree_max_mult = len(n.children)
            subtree_max_leaf = 0
        else:
            subtree_max_mult = 0
            # Comments and docstrings are excluded from max node size
            # analysis
            if "comment" in n.type or is_docstring(n, lang):
                subtree_max_leaf = 0
            else:
                subtree_max_leaf = len(n.text)
        max_depth = level
        if level >= depth_limit:
            return subtree_max_mult, subtree_max_leaf, level
        for c in n.children:
            c_max_mult, c_max_leaf, c_depth = rec_stats(c, level + 1)
            subtree_max_mult = max(subtree_max_mult, c_max_mult)
            subtree_max_leaf = max(subtree_max_leaf, c_max_leaf)
            max_depth = max(max_depth, c_depth + 1)
        return subtree_max_mult, subtree_max_leaf, max_depth

    # Actually do the recursion and collect the statistics
    max_mult, max_leaf, max_depth = rec_stats(tree.root_node, 0)

    # Check if any direct children of root is error node
    root_error = any(c.type == "ERROR" for c in tree.root_node.children)

    return ASTStats(
        size=len(code),
        root_error=root_error,
        errors=errors,
        max_mult=max_mult,
        max_leaf=max_leaf,
        max_depth=max_depth,
        total_comment_size=comment_size,
        n_func=function_count,
        n_class=class_count,
    )


def label_lang(df: DataFrame) -> DataFrame:
    """Add a column to the dataframe that indicates the language of the code.

    This is specifically for AST analysis purposes and can be different from the language
    specifier that we use for dedupe etc.
    """
    get_lang_udf = F.udf(get_lang, returnType=StringType())
    return df.withColumn("ast_lang", get_lang_udf("repo_path"))

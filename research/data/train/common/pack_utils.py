"""Utilities for packing/unpacking tokens."""

import struct
from typing import Sequence

from research.data.train.common.constants import TOKEN_SIZE


def pack_format(length: int) -> str:
    """Returns the format string for packing/unpacking tokens."""
    # 'H' means unsigned short and it should be aligned with TOKEN_SIZE
    return f"<{length}H"


def token_len(packed_tokens: bytearray) -> int:
    """Returns the number of tokens in the given binary."""
    return len(packed_tokens) // TOKEN_SIZE


def pack_tokens(token_ids: Sequence[int]) -> bytearray:
    """Packs a sequence of tokens into binary format."""
    length = len(token_ids)
    return bytearray(struct.pack(pack_format(length), *token_ids))


def unpack_tokens(packed_tokens: bytearray) -> Sequence[int]:
    """Unpacks tokens to a list containing token ids."""
    length = token_len(packed_tokens)
    return list(struct.unpack(pack_format(length), packed_tokens))

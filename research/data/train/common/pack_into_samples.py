"""Helper functions for packing tokens into equal-size samples."""

import math
from typing import Sequence

import numpy as np


def pack_into_samples(
    data: Sequence[np.ndarray],
    token_length: int,
    pad_token_id: int,
) -> Sequence[np.ndarray]:
    """Pack tokenized documents into equal-size samples.

    Args:
        data: A sequence of 1D numpy array containing tokens
        token_length: Token length of the packed samples
        pad_token_id: Token to use for padding to seq_length

    Returns:
        A list of packed samples, where each sample is a 1D numpy array
    """
    if len(data) == 0:
        return []

    concatenated = np.concatenate(data)
    concatenated_len = len(concatenated)

    # the last sample will be padded on the right
    pad_len = (
        math.ceil(concatenated_len / token_length) * token_length - concatenated_len
    )
    output = np.pad(
        concatenated, (0, pad_len), mode="constant", constant_values=pad_token_id
    )

    output_len = len(output)
    assert output_len % token_length == 0
    return np.split(output, output_len // token_length)

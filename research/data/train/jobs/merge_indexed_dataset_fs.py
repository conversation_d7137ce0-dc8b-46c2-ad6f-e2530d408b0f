"""Job to merge indexed datasets from shared fs and save the combined one to disk."""

import argparse
import logging
import os

import numpy as np
from megatron.data import indexed_dataset

from research.data.train.common.index_dataset_utils import INDEXED_DATASET_IMPL

# Prefixes of the indexed datasets to merge
PREFIXES = ["training", "validation"]


def main(args):
    logging.basicConfig(level=logging.INFO)
    logging.info("Args %s", args)

    input_path = args.input_path.rstrip("/")
    output_path = args.output_path.rstrip("/")
    vocab_size_set = False
    vocab_size = None

    for prefix in PREFIXES:
        datasets = [
            file[:-4]
            for file in os.listdir(input_path)
            if file.startswith(prefix) and file.endswith(".bin")
        ]

        if len(datasets) == 0:
            logging.info(f"No datasets found for prefix {prefix}")
            continue

        # Use the type of the child dataset to infer what dtype
        # we should use here.
        # dtype is controlled by vocab_size parameter, which is
        # not used directly; it is uint16 if vocab_size < 65500,
        # and is not None, otherwise it is int32.
        if not vocab_size_set:
            target = f"{input_path}/{datasets[0]}"
            dataset = indexed_dataset.make_dataset(
                target, INDEXED_DATASET_IMPL, skip_warmup=True
            )
            index = (
                dataset._index
                if isinstance(dataset, indexed_dataset.MMapIndexedDataset)
                else None
            )
            if index is not None and index.dtype == np.uint16:
                vocab_size = 30000
            else:
                vocab_size = None
            vocab_size_set = True

        output_prefix = f"{output_path}/{prefix}"
        combined_builder = indexed_dataset.make_builder(
            out_file=f"{output_prefix}.bin",
            impl=INDEXED_DATASET_IMPL,
            vocab_size=vocab_size,
        )
        logging.info(f"Combining into {output_prefix}")

        for ds in datasets:
            logging.info(f"Combining {ds}")
            combined_builder.merge_file_(f"{input_path}/{ds}")

        combined_builder.finalize(f"{output_prefix}.idx")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Combine multiple indexed datasets to one"
    )
    parser.add_argument(
        "--input-path",
        type=str,
        required=True,
        help="path to the input indexed datasets",
    )
    parser.add_argument(
        "--output-path",
        type=str,
        required=True,
        help="path to save the combined dataset",
    )

    main(parser.parse_args())

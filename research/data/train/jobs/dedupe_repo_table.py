"""Refresh the repo table.

Because we get repo info from calling user APIs, this contains some
duplicates, so we need to deduplicate them.
"""

import logging

import pyspark.sql.functions as F
from pyspark.sql import SparkSession

from research.data.spark import k8s_session
from research.data.train.common.read_utils import get_single_source, read_dataset
from research.data.train.datasets.github import github_repo_deduped


def dedupe_repos(
    spark: SparkSession,
):
    """Refresh the deduplicated snapshot of repo table.

    Returns a dataframe of the deduplicated repo table.
    """
    df = read_dataset(spark, get_single_source(github_repo_deduped))
    df = (
        df.withColumn("canonical_name", F.lower(F.col("full_name")))
        .repartition(1000, "canonical_name")
        .dropDuplicates(["canonical_name"])
        .write.mode("overwrite")
        .format(github_repo_deduped.format)
        .save(github_repo_deduped.location)
    )


def main():
    logging.basicConfig(level=logging.INFO)

    logging.info("Starting spark")
    spark = k8s_session(
        max_workers=120,
        conf={
            "spark.executor.cores": "4",
            "spark.executor.memory": "250G",
            "spark.sql.parquet.enableVectorizedReader": "false",
        },
    )

    logging.info("Refreshing repo table")
    dedupe_repos(spark)
    logging.info("Repos dedupe and saved.")


if __name__ == "__main__":
    main()

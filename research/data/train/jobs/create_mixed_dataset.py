"""Job to generate a mixture of datasets and pack them into indexed datasets.

Reads the already packed fixed length samples in binary array parquet format.

You can also use it to create an indexed dataset from one source.

For a given ratio of each source, create a fixed mixture dataframe.

- All sources are parquets of fixed length packed binary arrays
- All sources should already have the same schema
- If ratio is `None`, the dataset is kept to its entirety, all the others are down sampled.
- If any ratio requires up sampling, an exception is raised.
- The resulting dataframe is randomly shuffled
"""

import argparse
import json
from dataclasses import dataclass
from pathlib import Path
from typing import Mapping, Optional

import pyspark.sql.functions as F
import yaml
from pyspark.sql import DataFrame, SparkSession

from research.data.spark import k8s_session
from research.data.train.common.constants import RANDOM_SEED
from research.data.train.common.index_dataset_utils import (
    generate_indexed_dataset_fs,
    generate_indexed_dataset_s3,
)


@dataclass
class DataSource:
    """Information about a data source."""

    path: str
    """Path to the source."""

    ratio: Optional[float]
    """Target ratio of the source in the final dataset.

    None value means keep the entire source.
    """

    items: int
    """Number of items in the source."""

    df: DataFrame
    """Source dataframe."""


def read_source(
    spark_session: SparkSession, path: str, ratio: Optional[float]
) -> DataSource:
    """Read a source.

    Args:
        spark_session: Spark session
        path: Path to the source
        ratio: Target ratio of the source in the final dataset
    """
    # replace s3:// with s3a:// if applicable
    if path.startswith("s3://"):
        path = "s3a://" + path[5:]
    df = spark_session.read.parquet(path)
    items = df.count()
    return DataSource(path, ratio, items, df)


def lcg(seed: int) -> int:
    """Linear congruential generator."""
    a = 1664525
    c = 1013904223
    m = 2**32
    return (seed * a + c) % m


def create_mixture_df(
    spark_session: SparkSession,
    source_ratio: Mapping[str, Optional[float]],
    partitions: int = 500,
    seed=RANDOM_SEED,
) -> DataFrame:
    """Create a mixture of datasets with a given sampling ratio in each set.

    While you can supply a seed to the sampling, shuffling is very difficult to be consistently random,
    and will behave differently due to the dynamic nature of compute environments, i.e. connect speed, node count, etc.
    Therefore the random seed will ensure the same entries, but not the same order.  If desired you can
    sort them based on a given field, but this is unnecessarily expensive and is not recommended.

    Args:
        spark_session: Spark session
        source_ratio: Dictionary of sources to sample from; key is the path and value is the sampling ratio.
                `None` denotes no down sampling.
        partitions: Number of partitions to use for the output dataframe
        seed: Random seed.  Seed is rotated deterministically with LCG so each source will have effectively
            independent sampling. (otherwise source will equal length will have the same rows sampled.)

    Returns:
        A dataframe with the mixture of the sources.
    """
    sources = [
        read_source(spark_session, path, ratio) for path, ratio in source_ratio.items()
    ]

    # First we need to get the total number of rows, and how many rows cannot be sampled.
    # also, what is the total ratio that is already allocated.
    fixed_rows = sum(source.items for source in sources if source.ratio is None)
    total_ratio = sum(source.ratio for source in sources if source.ratio is not None)
    if total_ratio > 1:
        raise ValueError(f"Total ratio of {total_ratio} is too high.  Maximum is 1.")

    # The remainder of the ratio allocation go to the fixed rows, so we know the total
    # number of rows of the final df
    if fixed_rows:
        target_rows = fixed_rows / (1 - total_ratio)
    elif not total_ratio:
        raise ValueError("No samples requested.")
    else:
        # renomalize ratios
        for source in sources:
            if source.ratio is not None:
                source.ratio /= total_ratio
        # One of the sources will be fully sampled.
        # Compare the presumptive number of rows should each source be fully sampled.
        target_rows = min(
            source.items / source.ratio
            for source in sources
            if source.ratio is not None
        )
    print(f"Target dataset will comtain approximate {int(target_rows)} items")

    df = None
    # Compute the number of rows of each source to sample.
    for source in sources:
        if source.ratio is not None:
            sampling_ratio = target_rows * source.ratio / source.items
            if sampling_ratio > 1:
                raise ValueError(
                    f"Requested ratio of {source.ratio} is too high.  Only {source.items} rows available."
                    f" Maximum ratio is {source.items / target_rows:.2f}"
                )
            print(f"Sampling {source.path} with ratio {sampling_ratio:.2f}")
            sample_df = source.df.sample(False, sampling_ratio, seed=seed)
            seed = lcg(seed)
        else:
            print(f"Data source {source.path} is kept to its entirety.")
            sample_df = source.df
        if df is None:
            df = sample_df
        else:
            df = df.union(sample_df)

    if df is None:
        raise ValueError("No sources provided.")

    return df.repartition(partitions).sortWithinPartitions(F.rand(seed=seed))


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--seed", type=int, default=RANDOM_SEED)
    parser.add_argument(
        "--sources",
        type=str,
        required=True,
        help="either a JSON string specifying sources, or filename to a JSON file.",
    )
    parser.add_argument(
        "--output-path",
        type=str,
        required=True,
        help="Object store location to store the output dataset.",
    )
    parser.add_argument(
        "--validation-count",
        type=int,
        default=10000,
        help="number of validation examples to generate",
    )
    parser.add_argument(
        "--workers", type=float, default=20, help="number of spark workers to use"
    )
    parser.add_argument(
        "--vocab-size",
        type=int,
        default=None,
        help="number of tokens to use in the vocabulary",
    )
    return parser.parse_args()


def main():
    args = parse_args()
    # parse the sources
    source_path = Path(args.sources)
    if source_path.exists():
        with source_path.open("r") as f:
            sources = json.load(f)
    else:
        try:
            sources = json.loads(args.sources)
        except json.JSONDecodeError:
            pass
        try:
            sources = yaml.safe_load(args.sources)
        except yaml.YAMLError as exc:
            raise ValueError(
                f"Could not parse sources string: {args.sources}. "
                "Must be JSON, YAML or a .json file"
            ) from exc

    if isinstance(sources, str):
        sources = {sources: None}
    print(f"Loaded sources: {sources}")

    # verify sources
    assert isinstance(
        sources, dict
    ), f"Sources must be a dictionary, but got {type(sources)}"

    for path, ratio in sources.items():
        assert isinstance(path, str), f"Path must be a string, but got {type(path)}"
        if ratio is not None:
            assert isinstance(
                ratio, (int, float)
            ), f"Ratio must be a number, but got {type(ratio)}"
            ratio = float(ratio)
            if ratio < 0 or ratio > 1:
                raise ValueError(f"Ratio {ratio} must be between 0 and 1.")

    # create spark session
    print(f"Creating spark session with {args.workers} workers")
    spark = k8s_session(max_workers=args.workers)

    df = create_mixture_df(spark, sources, seed=args.seed)
    mix_path = "/mnt/efs/spark-data/shared/aug-stack/_intermediate/mixed/"
    print(f"Saving mixed dataset to {mix_path}")
    df.write.mode("overwrite").parquet(mix_path)

    print(f"Generating indexed dataset at {args.output_path}")
    if args.output_path.startswith("s3://") or args.output_path.startswith("s3a://"):
        # parse output bucket and path
        bucket, object_path = args.output_path.split("://", 1)[-1].split("/", 1)
        print(f"Saving output to bucket: {bucket}, path: {object_path}")

        generate_indexed_dataset_s3(
            spark,
            mix_path,
            bucket,
            object_path,
            args.vocab_size,
            args.validation_count,
        )
    else:
        generate_indexed_dataset_fs(
            spark,
            mix_path,
            args.output_path,
            args.vocab_size,
            args.validation_count,
        )

    spark.stop()


if __name__ == "__main__":
    main()

"""Job to generate the indexed dataset for starcoder data.

It uses the `starcoder_by_repo` dataset as input and follows the below steps:
1. Sort files within each repo based on certain strategies.
2. Tokenize file content.
3. Pack sorted and tokenized files into equal-length samples.
4. Apply FIM to samples.
5. Pack samples into binary type for efficient storage.
6. Write the results as parquet files to s3.
7. Generate batch indexed datasets to s3.
"""

import argparse
import logging
import typing
from typing import Iterable

import numpy as np
import pandas
from pyspark.sql.types import BinaryType, StructField, StructType

from base.tokenizers.tiktoken_starcoder_tokenizer import (
    StarCoderSpecialTokens,
    TiktokenStarCoderTokenizer,
)
from research.data.spark import k8s_session
from research.data.train.common.index_dataset_utils import generate_indexed_dataset_s3
from research.data.train.common.pack_into_samples import pack_into_samples
from research.data.train.common.pack_utils import pack_tokens
from research.data.train.common.read_utils import read_dataset
from research.data.train.common.sort_files_within_repo import (
    FileSortingStrategy,
    sort_files_within_repo,
)
from research.data.train.datasets.starcoder import starcoder_by_repo
from research.data.train.jobs.gen_starcoder_by_repo import (
    CONTENT_COL,
    FILE_LIST_COL,
    FILE_PATH_COL,
)
from research.fim.pretraining_fim import PreTrainingFIM

SORTING_STRATEGY = FileSortingStrategy.MAXIMIZE_FILENAME_MATCH

SEQ_LENGTH = 4096
ADD_ONE_TOKEN = True

FIM_RATE = 0.5
FIM_SPM_RATE = 0.0

OUTPUT_COL = "packed_samples"
PARQUET_OUTPUT_PATH = "s3a://starcoder/by_repo_starcoder_tokenizer_4k_fim_sorted"

INDEXED_DATASET_OUTPUT_BUCKET = "starcoder"
INDEXED_DATASET_OUTPUT_SUBDIR = "indexed/batches"

VALIDATION_COUNT = 10000


def main(args):
    logging.basicConfig(level=logging.INFO)
    logging.info("Args %s", args)

    logging.info("Starting spark")
    spark = k8s_session(
        min_workers=128,
        max_workers=128,
        conf={
            "spark.executor.cores": "4",
            "spark.executor.memory": "64G",
            "spark.sql.parquet.enableVectorizedReader": "false",
        },
    )

    # Set up the data processing pipeline in this pandas UDF.
    def _pandas_udf(iterable: Iterable[pandas.DataFrame]) -> Iterable[pandas.DataFrame]:
        tokenizer = TiktokenStarCoderTokenizer()
        special_tokens = typing.cast(StarCoderSpecialTokens, tokenizer.special_tokens)

        for pdf in iterable:
            # step 1: sort files to a global list
            global_file_list = []
            for file_list in pdf[FILE_LIST_COL]:
                global_file_list.extend(
                    sort_files_within_repo(
                        file_list, SORTING_STRATEGY, CONTENT_COL, FILE_PATH_COL
                    )
                )

            # step 2: tokenize text content
            tokens = [
                np.array(
                    tokenizer.tokenize_unsafe(file[CONTENT_COL]) + [special_tokens.eos]
                )
                for file in global_file_list
            ]

            # step 3: pack file tokens into equal-length samples
            token_length = SEQ_LENGTH + (1 if ADD_ONE_TOKEN else 0)
            packed_samples = pack_into_samples(
                tokens, token_length, pad_token_id=special_tokens.fim_pad
            )

            # step 4: apply FIM to samples
            fim_processor = PreTrainingFIM(
                fim_rate=FIM_RATE, fim_spm_rate=FIM_SPM_RATE, tokenizer=tokenizer
            )
            packed_samples = fim_processor.apply_on_samples(packed_samples)

            # step 5: pack tokens into binary for efficient storage
            packed_samples = [pack_tokens(sample.tolist()) for sample in packed_samples]

            yield pandas.DataFrame({OUTPUT_COL: packed_samples})

    if args.reuse_parquet:
        logging.info(f"Reusing parquet files under {PARQUET_OUTPUT_PATH}")
    else:
        df = read_dataset(spark, starcoder_by_repo)

        schema = StructType([StructField(OUTPUT_COL, BinaryType())])
        df = df.mapInPandas(_pandas_udf, schema=schema)

        # step 6: write the results as parquet files to s3
        df.repartition(500).write.mode("overwrite").parquet(PARQUET_OUTPUT_PATH)

    # step 7: generate the indexed dataset from the parquet output
    generate_indexed_dataset_s3(
        spark,
        PARQUET_OUTPUT_PATH,
        INDEXED_DATASET_OUTPUT_BUCKET,
        INDEXED_DATASET_OUTPUT_SUBDIR,
        TiktokenStarCoderTokenizer().vocab_size,
        VALIDATION_COUNT,
    )

    spark.stop()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--reuse_parquet", action="store_true", default=False)

    main(parser.parse_args())

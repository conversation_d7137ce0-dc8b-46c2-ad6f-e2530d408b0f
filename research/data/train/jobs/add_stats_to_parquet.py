"""Add a number of experimental statistics for Python code.

This includes
- AST based tree stats
- word based stats and per word entropy
- compression ratio

Word entropy are computed using the Shannon entropy formula, and normalized by the number of words:
H(X) = -∑(x∈X)p(x)log2(p(x))

Compression ratio is computed using the zlib compression algorithm.

Usage:
python add_stats_to_parquet.py --input gs://bucket/infile.parquet --output gs://bucket/outfile.parquet [--content_column content]
"""
import argparse
import ast
import re
import zlib
from collections import Counter
from dataclasses import dataclass

import numpy as np
import pandas as pd
import pyspark.sql.functions as F
from pyspark.sql.types import BooleanType, IntegerType, StructField, StructType

from research.data.spark.utils import k8s_session


@dataclass
class SubtreeStat:
    """Statistics for a subtree."""

    n_statement: int
    """Total number of statements in subtree."""

    max_mult: int
    """Maximum multiplicity in the subtree."""

    max_leaf: int
    """Maximum size of leaf node."""

    n_class: int
    """Number of class definitions."""

    n_func: int
    """Number of function definitions."""


def analyze_subtree(node):
    """Returns the number of statements, largest leaf node, and largest multiplicity in the subtree."""
    stats = SubtreeStat(
        int(isinstance(node, ast.stmt)),
        0,
        0,
        n_class=int(isinstance(node, ast.ClassDef)),
        n_func=int(
            isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.Lambda))
        ),
    )
    n_child = 0
    for child in ast.iter_child_nodes(node):
        n_child += 1
        child_stats = analyze_subtree(child)
        stats.n_statement += child_stats.n_statement
        stats.max_mult = max(stats.max_mult, child_stats.max_mult)
        stats.max_leaf = max(stats.max_leaf, child_stats.max_leaf)
        stats.n_class += child_stats.n_class
        stats.n_func += child_stats.n_func

    stats.max_mult = max(stats.max_mult, n_child)

    # get node size if it is a leaf
    if not n_child:
        stats.max_leaf = len(ast.dump(node))
    return stats


def main(input_location: str, output_location: str, content_column: str = "content"):
    spark = k8s_session(
        max_workers=100,
        conf={
            "spark.executor.memory": "120G",
            "spark.executor.pyspark.memory": "100G",
        },
    )

    df = spark.read.parquet(input_location)

    pattern = r"(?i)^(//|#|/\*|<!--)\s*(autogenerated|auto-generated|do not edit|do not modify)"
    check_length = 200

    # Flagging rows where the file content starts with the specified phrases
    df = df.withColumn(
        "is_autogenerated",
        F.when(
            F.substring(F.col(content_column), 1, check_length).rlike(pattern), True
        ).otherwise(False),
    )

    schema = StructType(
        [
            StructField("syntax_error", BooleanType(), nullable=True),
            StructField("parser_failure", BooleanType(), nullable=True),
            StructField("n_statement", IntegerType(), nullable=True),
            StructField("max_mult", IntegerType(), nullable=True),
            StructField("max_leaf", IntegerType(), nullable=True),
            StructField("n_class", IntegerType(), nullable=True),
            StructField("n_func", IntegerType(), nullable=True),
        ]
    )

    @F.pandas_udf(schema)
    def stmt_analysis_udf(codes: pd.Series) -> pd.DataFrame:
        results = []
        for code in codes:
            try:
                tree = ast.parse(code)
                result = analyze_subtree(tree)  # This function needs to be defined
                results.append(
                    (
                        False,
                        False,
                        result.n_statement,
                        result.max_mult,
                        result.max_leaf,
                        result.n_class,
                        result.n_func,
                    )
                )
            except (ValueError, SyntaxError):
                results.append((True, False, 0, 0, 0, 0, 0))
            except (MemoryError, RecursionError):
                results.append((False, True, 0, 0, 0, 0, 0))

        return pd.DataFrame(
            results,
            columns=[
                "syntax_error",
                "parser_failure",
                "n_statement",
                "max_mult",
                "max_leaf",
                "n_class",
                "n_func",
            ],
        )

    # Collect statistics
    # df = df.withColumn("stats", stmt_analysis_udf(content_column))

    pattern = re.compile(r"\b\w+\b")

    @F.udf(
        "struct<total_word_count long, unique_word_count long, top_word string, top_word_frequency long, word_entropy double>"
    )
    def word_analysis_udf(text):
        words = pattern.findall(text)
        total_word_count = len(words)
        frequencies = Counter(words)
        unique_word_count = len(frequencies)
        if not unique_word_count:
            top_word, top_word_frequency = "", 0
        else:
            top_word, top_word_frequency = frequencies.most_common(1)[0]
        # Convert frequencies to probabilities
        probabilities = [f / total_word_count for f in frequencies.values()]
        # Calculate entropy using probabilities
        word_entropy = -sum(
            p * np.log2(p) for p in probabilities if p > 0
        )  # Ensure p > 0 to avoid log2(0)
        # Normalize the entropy by dividing by log2(total_word_count)
        word_entropy = (
            word_entropy / np.log2(total_word_count) if total_word_count > 0 else 0
        )  # Check total_word_count > 0 to avoid division by zero
        return (
            total_word_count,
            unique_word_count,
            top_word,
            top_word_frequency,
            float(word_entropy),
        )

    df = df.withColumn("word_analysis", word_analysis_udf(content_column))

    @F.udf("struct<raw_size long, compressed_size long, compression_ratio double>")
    def compress_analysis_udf(text):
        # Get the compression ratio of the doc
        btext = text.encode("utf-8")
        original_size = len(btext)
        compressed_size = len(zlib.compress(btext))
        return original_size, compressed_size, original_size / compressed_size

    df = df.withColumn("compress_analysis", compress_analysis_udf(content_column))

    df.write.mode("overwrite").parquet(output_location)
    spark.stop()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input",
        type=str,
        default="s3a://python-training-experiments/augstack/parquet/",
        help="input dataframe location",
    )
    parser.add_argument(
        "--output",
        type=str,
        required=True,
        help="output dataframe location",
    )
    parser.add_argument(
        "--content_column",
        type=str,
        default="content",
        help="content column name",
    )
    args = parser.parse_args()

    main(args.input, args.output, args.content_column)

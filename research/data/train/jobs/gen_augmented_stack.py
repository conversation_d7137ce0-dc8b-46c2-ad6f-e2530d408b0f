"""Job to generate the augmented_stack dataset."""

import logging

import pyspark.sql.functions as F
from pyspark.sql.types import BooleanType

from research.data.spark import k8s_session
from research.data.train.common.ast_analysis import label_lang
from research.data.train.common.augstack_filter import (
    filter_autogen,
    filter_content_quality,
    filter_data,
    filter_denylist,
    filter_stars,
)
from research.data.train.common.starcoder_filter import (
    filter_alpha_udf,
    filter_alphanum_udf,
    filter_ext,
    filter_html_udf,
    filter_json_yaml_udf,
    filter_lang,
    filter_line_len_udf,
    filter_xml_udf,
    update_lang,
)
from research.data.train.datasets.augmented_stack import augmented_stack
from research.data.train.datasets.github import github_snapshot
from research.data.train.datasets.the_stack import the_stack_dedup

# Columns to union on
UNION_COLUMNS = [
    "repo_name",
    "repo_path",
    "stars_count",
    "lang",
    "content",
    "avg_line_length",
    "max_line_length",
    "alphanum_fraction",
]


def main():
    logging.basicConfig(level=logging.INFO)

    logging.info("Starting spark")
    spark = k8s_session(
        min_workers=200,
        max_workers=200,
        conf={
            "spark.executor.cores": "5",
            "spark.executor.memory": "350G",
            "spark.sql.parquet.enableVectorizedReader": "false",
        },
        packages=["bs4"],
    )

    # Load and preprocess the stack dataset before the union operation
    stack_df = spark.read.format(the_stack_dedup.format).load(the_stack_dedup.location)
    stack_df.printSchema()

    stack_df = stack_df.withColumnsRenamed(
        {
            "max_stars_repo_name": "repo_name",
            "max_stars_repo_path": "repo_path",
            "max_stars_count": "stars_count",
        }
    )

    stack_df = filter_ext(stack_df, "repo_path")
    stack_df = update_lang(stack_df, "lang", "repo_path")
    stack_df = filter_lang(stack_df, "lang")

    # Load the github snapshot dataset
    github_df = spark.read.format(github_snapshot.format).load(github_snapshot.location)
    github_df.printSchema()

    # Union the two datasets as the starting point of the augmented dataset
    stack_df = stack_df.select(UNION_COLUMNS).withColumn("source", F.lit("stack"))
    github_df = github_df.select(UNION_COLUMNS).withColumn("source", F.lit("github"))

    augmented_stack_df = stack_df.union(github_df)
    augmented_stack_df.printSchema()

    # Standardize some columns
    augmented_stack_df = augmented_stack_df.withColumn(
        "repo_name",
        F.lower(F.col("repo_name")),
    ).withColumn(
        "size",
        F.length(F.col("content")),
    )

    # Filter denied and autogenerated files
    augmented_stack_df = filter_denylist(augmented_stack_df)
    augmented_stack_df = filter_autogen(augmented_stack_df)
    augmented_stack_df = filter_stars(augmented_stack_df)
    augmented_stack_df = filter_data(augmented_stack_df)

    # Filter the augmented dataset
    @F.udf(BooleanType())
    def filter_udf(
        repo_path: str,
        lang: str,
        content: str,
        size: int,
        avg_line_length: float,
        max_line_length: int,
        alphanum_fraction: float,
        source: str,
    ) -> bool:
        return (
            filter_line_len_udf(repo_path, max_line_length, avg_line_length)
            and filter_json_yaml_udf(lang, size)
            and filter_alphanum_udf(repo_path, alphanum_fraction)
            and filter_alpha_udf(repo_path, content, size)
            and filter_xml_udf(lang, content)
            and filter_html_udf(lang, content, size, source)
        )

    augmented_stack_df = augmented_stack_df.filter(
        filter_udf(
            "repo_path",
            "lang",
            "content",
            "size",
            "avg_line_length",
            "max_line_length",
            "alphanum_fraction",
            "source",
        )
    )

    # Filter out rows that do not meet the content quality conditions
    augmented_stack_df = filter_content_quality(augmented_stack_df)
    augmented_stack_df = label_lang(augmented_stack_df).filter(
        "ast_lang is not null or size < 1e6"
    )
    (
        augmented_stack_df.repartition(5000, "lang", "repo_name")
        .write.mode("overwrite")
        .format(augmented_stack.format)
        .save(augmented_stack.location)
    )
    spark.stop()


if __name__ == "__main__":
    main()

"""Group by repo and apply filter on deduped files."""

import pyspark.sql.functions as F

from research.data.spark import k8s_session
from research.data.train.common.augstack_filter import (
    REPO_DEDUPE_LANGS,
    get_organize_repo_udf,
    get_prune_file_udf,
    get_repo_analysis_udf,
)

CONTENT_COLUMN = "content"
FILELIST_COLUMN = "file_list"
FILE_PATH_COLUMN = "repo_path"
COMPRESSION_LEVEL = 16
SIZE_LIMIT = 3e5
MULT_THRESHOLD = 500
INPUT_PATH = "/mnt/efs/spark-data/shared/aug-stack/v1/file_dedupe_0_6/"
GROUPED_PATH = "/mnt/efs/spark-data/temp_weekly/augstack_v1/file_lang_grouped/"
OUTPUT_PATH = "/mnt/efs/spark-data/shared/aug-stack/v1/file_dedupe_grouped_0_6/"


def process_lang(spark, lang: str):
    input_path = f"{INPUT_PATH}/lang={lang}/"
    grouped_path = f"{GROUPED_PATH}/lang={lang}/"
    output_path = f"{OUTPUT_PATH}/lang={lang}/"
    df = spark.read.parquet(input_path)
    rows = df.count()
    partitions = (rows // 10000) + 1
    print(f"Processing language {lang}")
    print(f"There are {rows} rows with {partitions} partitions")
    df = (
        df.groupBy("repo_name")
        .agg(
            F.max("stars_count").alias("stars_count"),
            F.sum(F.length(CONTENT_COLUMN)).alias("total_size"),
            F.count("*").alias("file_count"),
            F.collect_list(
                F.struct(
                    "repo_path",
                    "content",
                )
            ).alias(FILELIST_COLUMN),
        )
        .repartition(partitions, "repo_name")
    )
    df.write.mode("overwrite").parquet(grouped_path)

    # Repo level filters
    # First prune files based on high multiplicity
    df = spark.read.parquet(grouped_path)
    prune_file_udf = get_prune_file_udf(
        schema=df.schema[FILELIST_COLUMN].dataType, mult_threshold=MULT_THRESHOLD
    )
    df = df.withColumn(FILELIST_COLUMN, prune_file_udf(FILELIST_COLUMN))

    # Then apply repo level compression ratio filters
    repo_analysis_udf = get_repo_analysis_udf(
        content_col=CONTENT_COLUMN, compression_level=COMPRESSION_LEVEL
    )
    df = df.withColumn("repo_analysis", repo_analysis_udf(FILELIST_COLUMN)).filter(
        "repo_analysis.compression_ratio<=11"
    )
    organize_repo = get_organize_repo_udf(
        file_path_col=FILE_PATH_COLUMN,
        content_column=CONTENT_COLUMN,
        size_limit=SIZE_LIMIT,
    )

    df = df.select(
        "repo_name",
        "stars_count",
        F.col("file_count").alias("repo_file_count"),
        F.col("total_size").alias("repo_total_size"),
        F.posexplode(organize_repo(FILELIST_COLUMN)).alias("chunk_id", FILELIST_COLUMN),
        "repo_analysis",
    )
    df = df.withColumn("chunk_file_count", F.size(F.col(FILELIST_COLUMN)))
    df.repartition(partitions, "repo_name").write.mode("overwrite").parquet(output_path)


def main():
    spark = k8s_session(
        max_workers=120,
        conf={
            "spark.executor.cpus": "4",
            "spark.sql.parquet.columnarReaderBatchSize": "64",
            "spark.sql.execution.arrow.maxRecordsPerBatch": "128",
            "spark.executor.memory": "300G",
        },
    )
    langs = [
        entry[0]
        for entry in spark.read.parquet(INPUT_PATH).select("lang").distinct().collect()
    ]

    for lang in langs:
        if lang in REPO_DEDUPE_LANGS:
            continue
        process_lang(spark, lang)
    spark.stop()


if __name__ == "__main__":
    main()

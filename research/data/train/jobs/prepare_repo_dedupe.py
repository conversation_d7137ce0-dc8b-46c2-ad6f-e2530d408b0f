"""Process languages that need to be repo-dedupled."""

import pyspark.sql.functions as F

from research.data.spark import k8s_session
from research.data.train.common.augstack_filter import (
    REPO_DEDUPE_LANGS,
    get_organize_repo_udf,
    get_prune_file_udf,
    get_repo_analysis_udf,
)

CONTENT_COLUMN = "content"
FILE_PATH_COL = "repo_path"
MULT_THRESHOLD = 500
FILES_COLUMN = "file_list"
COMPRESSION_LEVEL = 16
SIZE_LIMIT = 3e5
INPUT_PATH = "/mnt/efs/spark-data/shared/aug-stack/v1/ast_filtered/"
GROUPED_PATH = "/mnt/efs/spark-data/temp_weekly/augstack_v1/repo_lang_grouped/"
OUTPUT_PATH = "/mnt/efs/spark-data/shared/aug-stack/v1/to_repo_dedupe/"
PARTITIONS = 2000


def process_lang(spark, lang: str):
    df = spark.read.parquet(INPUT_PATH).filter(F.col("lang") == lang)

    grouped_path = f"{GROUPED_PATH}/lang={lang}/"
    output_path = f"{OUTPUT_PATH}/lang={lang}/"

    df = (
        df.groupBy("repo_name")
        .agg(
            F.max("stars_count").alias("stars_count"),
            F.sum(F.length(CONTENT_COLUMN)).alias("total_size"),
            F.count("*").alias("file_count"),
            F.collect_list(
                F.struct(
                    FILE_PATH_COL,
                    CONTENT_COLUMN,
                )
            ).alias(FILES_COLUMN),
        )
        .repartition(PARTITIONS, "repo_name")
    )
    df.write.mode("overwrite").parquet(grouped_path)

    df = spark.read.parquet(grouped_path)
    prune_file_udf = get_prune_file_udf(
        schema=df.schema[FILES_COLUMN].dataType,
        file_path_col=FILE_PATH_COL,
        mult_threshold=MULT_THRESHOLD,
    )
    df = df.withColumn(FILES_COLUMN, prune_file_udf(FILES_COLUMN))

    repo_analysis_udf = get_repo_analysis_udf(
        content_col=CONTENT_COLUMN, compression_level=COMPRESSION_LEVEL
    )
    df = df.withColumn("repo_analysis", repo_analysis_udf(FILES_COLUMN)).filter(
        "repo_analysis.compression_ratio<=11 and repo_analysis.compression_ratio>=2"
    )

    organize_repo = get_organize_repo_udf(
        file_path_col=FILE_PATH_COL,
        content_column=CONTENT_COLUMN,
        size_limit=SIZE_LIMIT,
    )

    df = df.select(
        "repo_name",
        "stars_count",
        F.col("file_count").alias("repo_file_count"),
        F.col("total_size").alias("repo_total_size"),
        F.posexplode(organize_repo(FILES_COLUMN)).alias("chunk_id", FILES_COLUMN),
        "repo_analysis",
    )
    df = df.withColumn("chunk_file_count", F.size(F.col(FILES_COLUMN)))

    df.repartition(PARTITIONS, "repo_name").write.mode("overwrite").parquet(output_path)


def main():
    spark = k8s_session(
        max_workers=100,
        conf={
            "spark.sql.parquet.columnarReaderBatchSize": "64",
            "spark.sql.execution.arrow.maxRecordsPerBatch": "128",
            "spark.executor.memory": "250G",
        },
    )
    for lang in REPO_DEDUPE_LANGS:
        print(f"Processing language {lang}")
        process_lang(spark, lang)
    spark.stop()


if __name__ == "__main__":
    main()

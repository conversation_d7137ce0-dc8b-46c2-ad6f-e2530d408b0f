"""Job to merge indexed datasets from s3 and save the combined one to disk."""

import argparse
import logging
from pathlib import Path
from tempfile import TemporaryDirectory

from megatron.data import indexed_dataset
from retry import retry

from base.tokenizers.tiktoken_starcoder_tokenizer import TiktokenStarCoderTokenizer
from research.core.utils_for_s3 import get_s3_client
from research.data.train.common.index_dataset_utils import INDEXED_DATASET_IMPL

# Prefixes of the indexed datasets to merge
PREFIXES = ["training", "validation"]


def main(args):
    logging.basicConfig(level=logging.INFO)
    logging.info("Args %s", args)

    input_bucket = args.input_bucket
    input_subdirs = [subdir.rstrip("/") for subdir in args.input_subdir]
    output_path = args.output_path.rstrip("/")

    s3 = get_s3_client()
    vocab_size = TiktokenStarCoderTokenizer().vocab_size

    for prefix in PREFIXES:
        output_prefix = f"{output_path}/{prefix}"
        path_prefixes = set()
        for input_subdir in input_subdirs:
            # If the input is an s3 path, extract the bucket name
            if input_subdir.startswith("s3://"):
                bucket_name, input_subdir = input_subdir[len("s3://") :].split("/", 1)
            elif input_bucket:
                bucket_name = input_bucket
            else:
                raise ValueError(
                    "Please specify either --input-bucket or "
                    "include bucket name in --input-subdir"
                )
            objects = s3.list_objects_v2(
                Bucket=bucket_name,
                Prefix=f"{input_subdir}/{prefix}",
            )
            path_prefixes.update(
                [
                    Path(content["Key"]).with_suffix("")
                    for content in objects["Contents"]
                ]
            )
            logging.info(f"Found {len(path_prefixes)} indexed datasets for {prefix}")

        combined_builder = indexed_dataset.make_builder(
            out_file=f"{output_prefix}.bin",
            impl=INDEXED_DATASET_IMPL,
            vocab_size=vocab_size,
        )
        logging.info(f"Combining into {output_prefix}")

        @retry(tries=20, delay=5)
        def add_to_combined_builder(builder, path_prefix: Path):
            with TemporaryDirectory() as tmpdir:
                logging.info(f"Downloading from {path_prefix} to {tmpdir}")

                file_prefix = path_prefix.name
                local_path_prefix = f"{tmpdir}/{file_prefix}"

                s3.download_file(
                    Bucket=bucket_name,
                    Key=f"{path_prefix}.bin",
                    Filename=f"{local_path_prefix}.bin",
                )
                s3.download_file(
                    Bucket=bucket_name,
                    Key=f"{path_prefix}.idx",
                    Filename=f"{local_path_prefix}.idx",
                )

                builder.merge_file_(local_path_prefix)

        for path_prefix in path_prefixes:
            add_to_combined_builder(combined_builder, path_prefix)

        combined_builder.finalize(f"{output_prefix}.idx")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Combine multiple indexed datasets to one"
    )
    parser.add_argument(
        "--input-bucket",
        type=str,
        required=False,
        help="bucket name where the input is located.",
    )
    parser.add_argument(
        "--input-subdir",
        type=str,
        required=True,
        nargs="+",
        help="directories to search for objects. must be full s3 paths if --input-bucket is not specified",
    )
    parser.add_argument(
        "--output-path",
        type=str,
        required=True,
        help="path to save the combined dataset",
    )

    main(parser.parse_args())

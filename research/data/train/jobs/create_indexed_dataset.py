"""Job to generate the indexed dataset from tokenized parquet dataset.

The input is one or more parquet dataset with a single column containing tokenized samples.
If there are multiple input locations, the contents will be concatenated and merged.

The input may be packed byte array or array of integer format.  If it is a byte array
it is assumed as already packed and will be unpacked.

Use `uint16` flag to create indexed dataset that are uint16; otherwise it will be int32.

The output is a single indexed dataset.

"""

import argparse
import logging

from research.data.spark import k8s_session
from research.data.train.common.index_dataset_utils import generate_indexed_dataset_fs


def parse_args():
    parser = argparse.ArgumentParser()
    # a list of data locations as input
    parser.add_argument(
        "--input_path",
        type=str,
        required=True,
        help="One or more input paths containing parquet files.  They must contain a single column of tokenized samples as arrays of ints.",
        nargs="+",
    )
    parser.add_argument("--output_path", type=str, required=True)
    parser.add_argument("--validation_count", type=int, default=10000)
    parser.add_argument("--workers", type=int, default=100)
    parser.add_argument(
        "--uint16",
        help="Create uint16 dataset instead of int32",
        action="store_true",
        default=False,
    )
    return parser.parse_args()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    logging.info("Starting spark")
    args = parse_args()

    spark = k8s_session(
        min_workers=args.workers,
        max_workers=args.workers,
        conf={
            "spark.executor.cores": "4",
            "spark.executor.memory": "200G",
            "spark.sql.parquet.enableVectorizedReader": "false",
        },
    )

    generate_indexed_dataset_fs(
        spark=spark,
        input_path=args.input_path,
        output_dir=args.output_path,
        # Exact numbers don't matter.  Anything below 65000 will result in a uint16 dataset
        vocab_size=30000 if args.uint16 else 100000,
        validation_count=args.validation_count,
    )

    spark.stop()

"""Job to generate the indexed dataset for AugStack data.

It uses the `augstack_by_repo` dataset as input and follows the below steps:
1. Sort files within each repo based on certain strategies.
2. Tokenize file content.
3. Pack sorted and tokenized files into equal-length samples.
    => save results as parquet files (pre-fim)
    => this can be reused for applying different FIM processings
4. Apply FIM to samples.
    => save results as parquet files (pre-indexing)
5. Generate corresponding batch indexed datasets.
"""

import argparse
import logging
import typing
from typing import Iterable

import numpy as np
import pandas
from pyspark.sql.types import BinaryType, StructField, StructType

from base.tokenizers.tiktoken_starcoder_tokenizer import (
    StarCoderSpecialTokens,
    TiktokenStarCoderTokenizer,
)
from research.data.spark import k8s_session
from research.data.train.common.index_dataset_utils import generate_indexed_dataset_fs
from research.data.train.common.pack_into_samples import pack_into_samples
from research.data.train.common.pack_utils import pack_tokens, unpack_tokens
from research.data.train.common.read_utils import read_dataset
from research.data.train.common.sort_files_within_repo import (
    FileSortingStrategy,
    sort_files_within_repo,
)
from research.data.train.datasets.augmented_stack import augstack_by_repo
from research.data.train.jobs.gen_augstack_by_repo import (
    CONTENT_COL,
    FILE_LIST_COL,
    FILE_PATH_COL,
)
from research.fim.pretraining_fim import PreTrainingFIM

SORTING_STRATEGY = FileSortingStrategy.MAXIMIZE_FILENAME_MATCH

SEQ_LENGTH = 4096
ADD_ONE_TOKEN = True

FIM_RATE = 0.5
FIM_SPM_RATE = 0.0

OUTPUT_COL = "packed_samples"
PRE_FIM_PATH = "/mnt/efs/spark-data/shared/aug-stack/_intermediate/pre-fim"
PRE_INDEXING_PATH = "/mnt/efs/spark-data/shared/aug-stack/_intermediate/pre-indexing"
INDEXED_OUTPUT_PATH = "/mnt/efs/spark-data/shared/aug-stack/indexed-batches"

VALIDATION_COUNT = 10000


def main(args):
    logging.basicConfig(level=logging.INFO)
    logging.info("Args %s", args)

    logging.info("Starting spark")
    spark = k8s_session(
        min_workers=128,
        max_workers=128,
        conf={
            "spark.executor.cores": "4",
            "spark.executor.memory": "64G",
            "spark.sql.parquet.enableVectorizedReader": "false",
            "spark.sql.execution.arrow.maxRecordsPerBatch": "300",
        },
    )

    output_schema = StructType([StructField(OUTPUT_COL, BinaryType())])

    # Set up the data processing pipeline in this pandas UDF.
    def _pandas_udf(iterable: Iterable[pandas.DataFrame]) -> Iterable[pandas.DataFrame]:
        for pdf in iterable:
            tokenizer = TiktokenStarCoderTokenizer()
            special_tokens = typing.cast(
                StarCoderSpecialTokens, tokenizer.special_tokens
            )

            # step 1: sort files to a global list
            global_file_list = []
            for file_list in pdf[FILE_LIST_COL]:
                global_file_list.extend(
                    sort_files_within_repo(
                        file_list, SORTING_STRATEGY, CONTENT_COL, FILE_PATH_COL
                    )
                )

            # step 2: tokenize text content
            tokens = [
                np.array(
                    tokenizer.tokenize_unsafe(file[CONTENT_COL]) + [special_tokens.eos]
                )
                for file in global_file_list
            ]

            # step 3: pack file tokens into equal-length samples
            token_length = SEQ_LENGTH + (1 if ADD_ONE_TOKEN else 0)
            packed_samples = pack_into_samples(
                tokens, token_length, pad_token_id=special_tokens.fim_pad
            )

            packed_samples = [pack_tokens(sample.tolist()) for sample in packed_samples]
            yield pandas.DataFrame({OUTPUT_COL: packed_samples})

    if args.reuse_pre_fim:
        logging.info(f"Reusing pre-fim data under {PRE_FIM_PATH}")
    else:
        df = read_dataset(spark, augstack_by_repo)
        df = df.mapInPandas(_pandas_udf, schema=output_schema)
        df.repartition(500).write.mode("overwrite").parquet(PRE_FIM_PATH)

    def _fim_udf(iterable: Iterable[pandas.DataFrame]) -> Iterable[pandas.DataFrame]:
        for pdf in iterable:
            tokenizer = TiktokenStarCoderTokenizer()

            samples = [np.array(unpack_tokens(sample)) for sample in pdf[OUTPUT_COL]]

            # step 4: apply FIM to samples
            fim_processor = PreTrainingFIM(
                fim_rate=FIM_RATE, fim_spm_rate=FIM_SPM_RATE, tokenizer=tokenizer
            )
            samples = fim_processor.apply_on_samples(samples, seed=args.seed)

            packed_samples = [pack_tokens(sample.tolist()) for sample in samples]
            yield pandas.DataFrame({OUTPUT_COL: packed_samples})

    df = spark.read.parquet(PRE_FIM_PATH)
    df = df.mapInPandas(_fim_udf, schema=output_schema)
    df.repartition(500).write.mode("overwrite").parquet(PRE_INDEXING_PATH)

    # step 5: generate the indexed dataset from the parquet output
    if not args.skip_index:
        generate_indexed_dataset_fs(
            spark,
            PRE_INDEXING_PATH,
            INDEXED_OUTPUT_PATH,
            TiktokenStarCoderTokenizer().vocab_size,
            VALIDATION_COUNT,
        )

    spark.stop()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--reuse_pre_fim", action="store_true", default=False)
    parser.add_argument("--skip_index", action="store_true", default=False)
    parser.add_argument("--seed", type=int, required=False, default=None)

    main(parser.parse_args())

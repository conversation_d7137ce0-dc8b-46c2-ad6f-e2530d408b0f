"""Job to generate the indexed dataset for AugStack data.

We added a few additional filters here, because by the time we developed these
filters we have already performed deduplication and filtering.

This includes:
1. Remove license header from the file content.
2. Filter out samples with too long words.
3. Filter out samples with too long word lengths.
4. Filter out samples with problematic KL-divergence.

The next time we regenerate the dataset, we should move these filters upstream to
`research/data/train/common/augstack_filter.py`.
"""

import argparse
import logging
import re
from collections import Counter
from functools import partial
from typing import Iterable

import numpy as np
import pandas
import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from base.tokenizers import create_tokenizer_by_name
from research.data.spark import k8s_session
from research.data.train.common.index_dataset_utils import generate_indexed_dataset_fs
from research.data.train.jobs.gen_augstack_by_repo import (
    CONTENT_COL,
)
from research.fim.pretraining_fim import PreTrainingFIM

# Default arguments
SEQ_LENGTH = 4096
ADD_ONE_TOKEN = True
FIM_RATE = 0.36
FIM_SPM_RATE = 0.0
MAX_TAIL = 410
TOKENIZER_NAME = "deepseek_coder_base"
BATCH_SIZE = 1000
BASE_PATH = "/mnt/efs/spark-data/shared/aug-stack/v1"
PRE_INDEXING_PATH = BASE_PATH + "/tokenized_and_packed/"
INDEXED_OUTPUT_PATH = BASE_PATH + "/indexed-batches/"

FILE_LIST_COL = "file_list"
OUTPUT_COL = "packed_samples"

VALIDATION_COUNT = 10000

KL_WORD_LEN_LIMIT = 100


def remove_license_header(text: str, headspace: int = 100):
    """Remove license header from the file content.

    Args:
        text (str): The file content.
        headspace (int): The number of characters to check for license header.

    Returns:
        str: The file content without license header.
    """
    head = text[:headspace].lower()
    # First check and see if license and copyright are in the head
    if "copyright" not in head and "license" not in head:
        return text
    # If so, remove leading comment blocks
    if text.startswith("/*"):
        return text[text.find("*/") + 2 :].lstrip()
    if text.startswith("<!--"):
        return text[text.find("-->") + 3 :].lstrip()
    if text.startswith("# "):
        # remove all comment lines at the head of file
        lines = text.split("\n")
        for i, line in enumerate(lines):
            if line and not line.startswith("#"):
                return "\n".join(lines[i:])
        return ""
    return text


def filter_word_length(
    content: str, pattern: re.Pattern, max_len: int, max_avg_len: int
):
    """Filter out samples with too long words."""
    lengths = [len(w) for w in pattern.split(content)]
    if max(lengths) < max_len and sum(lengths) / len(lengths) < max_avg_len:
        return True
    else:
        return False


def filter_kl_divergence(
    lang: str,
    content: str,
    base_distr: dict,
    pattern: re.Pattern,
    threshold: float = 1.0,
    size_limit: int = 10000,
    epsilon=0.01,
):
    """Filter out samples with problematic KL-divergence.

    For each language, the word length distribution is calculated and stored across the
    entire dataset first.

    When processing each files, the KL-divergence of the word length distrubtions of the
    current file with respect to the base distribution for the corresponding language is
    calculated for files larger than `size_limit`.

    Those that diverge significantly from the base distribution are filtered out.

    Args:
        lang (str): The language of the file.
        content (str): The file content.
        base_distr (dict): The base word length distribution for each language.
        pattern (re.Pattern): The pattern to split the file content into words.
        threshold (float, optional): The threshold for the KL-divergence. Defaults to 0.8.
        size_limit (int, optional): The minimum size of the file to calculate the KL-divergence. Defaults to 10000.

    Returns:
        bool: Whether the file should be retained.
    """
    # unable to calculation divergence if language is too small or file is too small
    if lang not in base_distr or len(content) < size_limit:
        return True
    q = base_distr[lang]
    lengths = [
        min(len(w), KL_WORD_LEN_LIMIT) for w in pattern.split(content) if len(w) > 0
    ]
    min_q = epsilon / KL_WORD_LEN_LIMIT
    counts = Counter(lengths)
    p = {length: count / len(lengths) for length, count in counts.items()}
    kl = float(sum(p[i] * np.log2(p[i] / max(q.get(i, 0), min_q)) for i in p))
    return kl < threshold


# Set up the data processing pipeline in this pandas UDF.
def tokenize(
    pdfs: Iterable[pandas.DataFrame], base_distr: dict, args: argparse.Namespace
):
    seq_length = args.seq_length + (1 if args.add_one_token else 0)
    tokenizer = create_tokenizer_by_name(args.tokenizer)
    fim_processor = PreTrainingFIM(
        fim_rate=args.fim_rate, fim_spm_rate=args.fim_spm_rate, tokenizer=tokenizer
    )
    special_tokens = tokenizer.special_tokens
    assert isinstance(special_tokens.eos, int), "eos is not int"
    assert isinstance(special_tokens.padding, int), "padding is not int"
    try:
        if isinstance(special_tokens.begin_sequence, int):
            bos_sequence = [special_tokens.begin_sequence]
        elif isinstance(special_tokens.begin_sequence, (list, tuple)):
            bos_sequence = list(special_tokens.begin_sequence)
        elif special_tokens.begin_sequence is None:
            bos_sequence = []
        else:
            raise ValueError(
                "begin_sequence is not int, list, tuple, or None: {}".format(
                    special_tokens.begin_sequence
                )
            )
    except (AttributeError, ValueError):
        bos_sequence = []
    pattern1 = re.compile(r"[\s,.;]+")
    pattern2 = re.compile(r"\W+")

    buffer = []
    batch = []
    for pdf in pdfs:
        for _, row in pdf.iterrows():
            file_content = row[CONTENT_COL]
            lang = row["lang"]
            file_content = remove_license_header(file_content)
            if not file_content:
                continue
            if not filter_word_length(file_content, pattern2, 64, 16):
                continue
            if not filter_word_length(file_content, pattern1, 130, 20):
                continue
            if not filter_kl_divergence(
                lang, file_content, base_distr, pattern1, threshold=args.kl_threshold
            ):
                continue

            new_tokens = (
                bos_sequence
                + tokenizer.tokenize_safe(file_content)
                + [special_tokens.eos]
            )
            if (
                len(buffer) + len(new_tokens) > seq_length
                and len(buffer) > seq_length - args.max_tail
            ):
                buffer.extend([special_tokens.padding] * (seq_length - len(buffer)))
                batch.append(np.array(buffer))
                buffer = []
            buffer.extend(new_tokens)
            while len(buffer) >= seq_length:
                batch.append(np.array(buffer[:seq_length]))
                buffer = buffer[seq_length:]
            if len(batch) >= args.batch_size:
                fim_samples = fim_processor.apply_on_samples(batch)
                yield pandas.DataFrame({OUTPUT_COL: fim_samples})
                batch = []
    if buffer:
        buffer.extend([special_tokens.padding] * (seq_length - len(buffer)))
        batch.append(np.array(buffer))
    if batch:
        fim_samples = fim_processor.apply_on_samples(batch)
        yield pandas.DataFrame({OUTPUT_COL: fim_samples})


def get_base_distr(df: DataFrame, min_size: float = 1e7):
    """Calculate the base length distribution for each language.

    The language has to have at least `min_size` total size to be included in the base distribution.

    This would be later used to calculate the KL-divergence of the current sample with respect to the base distribution.
    """
    pdf = (
        df.withColumn("words", F.split(F.col("content"), "[\\s,.;]+"))
        .drop("content")
        .withColumn("word_lengths", F.transform(F.col("words"), F.length))
        .drop("words")
        .select("lang", F.explode("word_lengths").alias("length"))
        .withColumn("length", F.least(F.col("length"), F.lit(KL_WORD_LEN_LIMIT)))
        .filter(F.col("length") > 0)
        .groupBy("lang", "length")
        .agg(
            F.count("*").alias("count"),
        )
        .toPandas()
    )
    base_distr = {}
    for lang in pdf["lang"].unique():
        lang_pdf = pdf[pdf["lang"] == lang]
        total = lang_pdf["count"].sum()
        if total < min_size:
            print(f"Language {lang} has only {total} words. Skipping.")
            continue
        base_distr[lang] = {
            int(length): float(count / total)
            for length, count in zip(lang_pdf["length"], lang_pdf["count"])
        }
        print(f"Language {lang} has {total} words.")
    return base_distr


def main(args):
    logging.basicConfig(level=logging.INFO)
    logging.info("Args %s", args)

    logging.info("Starting spark")
    spark = k8s_session(
        min_workers=200,
        max_workers=200,
        conf={
            "spark.executor.cores": "4",
            "spark.executor.memory": "280G",
            "spark.sql.parquet.enableVectorizedReader": "false",
            "spark.sql.execution.arrow.maxRecordsPerBatch": "32",
        },
    )

    vocab_size = create_tokenizer_by_name(args.tokenizer).vocab_size
    if not args.skip_tokenize:
        print("Computing base distribution for each language...")
        df = (
            spark.read.parquet(args.input_path)
            .select(
                "lang",
                F.explode("file_list").alias("file"),
            )
            .select("lang", F.col("file.content").alias(CONTENT_COL))
        )
        if args.min_stars:
            df = df.filter(F.col("stars_count") >= args.min_stars)
        base_distr = get_base_distr(df, args.distr_min_size)

        print("Tokenizing and packing...")
        df = spark.read.parquet(args.input_path).sortWithinPartitions(
            F.xxhash64("repo_name"), "chunk_id"
        )
        if args.min_stars:
            print(f"Filtering out repos with less than {args.min_stars} stars")
            df = df.filter(F.col("stars_count") >= args.min_stars)
        df = (
            df.select("lang", F.explode("file_list").alias("file"))
            .select(
                "lang",
                F.col("file.content").alias(CONTENT_COL),
            )
            .mapInPandas(
                partial(tokenize, base_distr=base_distr, args=args),
                schema="struct<packed_samples array<int>>",
            )
            .write.mode("overwrite")
            .parquet(args.pre_indexing_path)
        )

    # step 5: generate the indexed dataset from the parquet output
    if not args.skip_index:
        if vocab_size < 30000:
            print("Using uint16")
        else:
            print("Using int32")
        generate_indexed_dataset_fs(
            spark,
            args.pre_indexing_path,
            args.indexed_output_path,
            vocab_size,
            args.validation_count,
        )

    spark.stop()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--skip_tokenize", action="store_true", default=False)
    parser.add_argument("--skip_index", action="store_true", default=False)
    parser.add_argument(
        "--batch_size",
        type=int,
        required=False,
        default=BATCH_SIZE,
        help="Batch size for mapInPandas.",
    )
    parser.add_argument("--seed", type=int, required=False, default=42)
    parser.add_argument("--seq_length", type=int, required=False, default=SEQ_LENGTH)
    parser.add_argument("--add_one_token", action="store_true", default=ADD_ONE_TOKEN)
    parser.add_argument("--fim_rate", type=float, required=False, default=FIM_RATE)
    parser.add_argument(
        "--fim_spm_rate", type=float, required=False, default=FIM_SPM_RATE
    )
    parser.add_argument(
        "--max_tail",
        type=int,
        required=False,
        default=MAX_TAIL,
        help="Sample will be padded if we have less than this amount of free space.",
    )
    parser.add_argument("--tokenizer", type=str, required=False, default=TOKENIZER_NAME)
    parser.add_argument(
        "--min_stars",
        type=int,
        default=None,
        help="Minimum number of stars to include in the dataset.",
    )
    parser.add_argument(
        "--input_path",
        type=str,
        required=False,
        default=f"{BASE_PATH}/by_repo",
        help="Path to the input dataset, lists of files grouped by repo.",
    )
    parser.add_argument(
        "--pre_indexing_path",
        type=str,
        required=False,
        default=PRE_INDEXING_PATH,
        help="Path to save the pre-indexing dataset.",
    )
    parser.add_argument(
        "--indexed_output_path",
        type=str,
        required=False,
        default=INDEXED_OUTPUT_PATH,
        help="Path to save the indexed dataset.",
    )
    parser.add_argument(
        "--validation_count",
        type=int,
        required=False,
        default=VALIDATION_COUNT,
        help="Number of samples to use for validation.",
    )
    parser.add_argument(
        "--kl_threshold",
        type=float,
        required=False,
        default=1.0,
        help="KL-divergence threshold to filter out out-of-distribution samples.  Default: 1.0",
    )
    parser.add_argument(
        "--distr_min_size",
        type=float,
        required=False,
        default=1e7,
        help="Minimum size of the language to be included in the base distribution. Default: 1e7",
    )
    main(parser.parse_args())

"""Partition original filtered dataset by AST languages.

This is to help a following step that does treesitter static analysis based on language.
"""

import logging

import pandas as pd
import pyspark.sql.functions as F

from research.data.spark import k8s_session
from research.data.spark.pipelines.utils import map_parquet
from research.data.train.common.ast_analysis import get_code_stats
from research.data.train.datasets.augmented_stack import augmented_stack

logging.basicConfig(level=logging.INFO)

NO_AST_PATH = "/mnt/efs/spark-data/shared/aug-stack/_intermediate/augstack_no_ast"
AST_PATH = "/mnt/efs/spark-data/shared/aug-stack/_intermediate/augstack_ast"
STAT_PATH = "/mnt/efs/spark-data/shared/aug-stack/_intermediate/augstack_ast_stats"
FINAL_PATH = "/mnt/efs/spark-data/shared/aug-stack/v1/ast_filtered"

# Fields to collect from statistics
FIELDS = [
    "root_error",
    "errors",
    "max_mult",
    "max_leaf",
    "max_depth",
    "total_comment_size",
    "n_func",
    "n_class",
]

# Which columns to keep in the final AST filtered dataset
OUTPUT_COLUMNS = [
    "repo_name",
    "repo_path",
    "stars_count",
    "lang",
    "content",
    "avg_line_length",
    "max_line_length",
    "alphanum_fraction",
    "source",
    "size",
    "content_quality.word_entropy",
    "content_quality.compression_ratio",
]

spark = k8s_session(
    min_workers=200,
    max_workers=200,
    conf={
        "spark.executor.cores": "4",
        "spark.executor.memory": "300G",
        "spark.sql.parquet.enableVectorizedReader": "false",
    },
)

# First set aside the non-AST languages
print("Writing non-ast languages.")
df = spark.read.format(augmented_stack.format).load(augmented_stack.location)
df.filter("ast_lang is null and size < 8e5").write.mode("overwrite").parquet(
    NO_AST_PATH
)

# Now partition by AST languages
langs = [
    lang for (lang,) in df.select("ast_lang").distinct().collect() if lang is not None
]
for lang in langs:
    ast_path = f"{AST_PATH}/ast_lang={lang}"
    stat_path = f"{STAT_PATH}/ast_lang={lang}"

    lang_df = df.filter(f"ast_lang = '{lang}'").drop("ast_lang")
    rows = lang_df.count()
    partitions = (rows // 2000) + 1
    print(f"Writing {lang}. {rows} rows, {partitions} partitions.", flush=True)
    lang_df.repartition(partitions).write.mode("overwrite").parquet(ast_path)

    # Create the work function, and apply it to the dataset
    def process(df: pd.DataFrame, ast_lang: str = lang) -> pd.DataFrame:
        """Collect AST based statistics for a list of files."""
        new_columns = {field: [] for field in FIELDS}
        for code, path in zip(df.content, df.repo_path):
            stats = get_code_stats(code, path, ast_lang)
            for field in FIELDS:
                if stats is None:
                    new_columns[field].append(0)
                else:
                    new_columns[field].append(getattr(stats, field))
        for column, values in new_columns.items():
            df[column] = values
        return df

    map_parquet.apply_pandas(
        spark,
        process,
        ast_path,
        stat_path,
        ignore_error=True,
        batch_size=3,
        timeout=60 * 45,
        task_info_location=f"/mnt/efs/spark-data/temp_weekly/map_parquet_task_logs/apply_ast_filter/{lang}/",
    )

# Filter and combine the datasets from each language
df_ast = (
    spark.read.parquet(STAT_PATH)
    .withColumn(
        "invalid",
        F.expr("""
                root_error and stars_count < 2 and ast_lang not in ('c', 'cpp', 'lua', 'c_sharp')
                OR max_depth > 190
            """),
    )
    .withColumn(
        "big_node",
        F.col("max_leaf") > 5000,
    )
    .withColumn(
        "high_mult",
        F.col("max_mult") > 3000,
    )
    .withColumn(
        "low_comment",
        F.expr("""
                size > 20000 and (
                    total_comment_size / size < 0.07 and COALESCE(stars_count, 0) < 10
                    OR COALESCE(stars_count, 0) < 100 and total_comment_size / size < 0.03
                    OR COALESCE(stars_count, 0) < 1000 and total_comment_size / size < 0.01
                )
                OR total_comment_size / size > 0.95
        """),
    )
    .withColumn(
        "structureless",
        F.expr("size / (n_func + n_class + 1) > 20000"),
    )
    .filter(
        "NOT low_comment AND not structureless AND NOT big_node AND NOT high_mult AND (NOT invalid OR stars_count > 1)"
    )
)

df_no_ast = spark.read.parquet(NO_AST_PATH)

df_ast.union(df_no_ast).repartition(5000).write.mode("overwrite").parquet(FINAL_PATH)
spark.stop()

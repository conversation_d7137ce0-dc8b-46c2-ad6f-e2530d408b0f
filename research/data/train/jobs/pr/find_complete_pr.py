"""Find PRs that have all edited base files downloaded.
There should be no missing file while applying the PR diff in the output dataset.
"""

import re

import pyspark.sql.functions as F
from pyspark.sql.types import ArrayType, StringType

from research.data.spark import k8s_session

# Number of Spark workers
NUM_WORKERS = 64

# Regex to find file names in the base repo
PATTERN = re.compile(r"diff --git a/(.+?) b/")

# Input and output paths
INPUT_PR = "/mnt/efs/spark-data/shared/gh_pr_candidates"
REPO_FILE_MAPPING = "/mnt/efs/spark-data/shared/gh_pr_repo_files_2m/repo_file_mapping"
PR_COMPLETE = "/mnt/efs/spark-data/shared/gh_pr_complete"

# Column name of a temporary column to join on
JOIN_COL = "join_col"


@F.udf(ArrayType(StringType()))
def get_base_files(diff: str):
    """Find all file paths in the base repo from the provided diff"""
    return list(set(PATTERN.findall(str(diff))))


def main():
    print(f"Starting spark with {NUM_WORKERS} workers")
    spark = k8s_session(
        min_workers=NUM_WORKERS,
        max_workers=NUM_WORKERS,
        conf={
            "spark.executor.cores": "4",
            "spark.executor.memory": "64G",
        },
    )

    candidates = spark.read.parquet(INPUT_PR).filter(F.col("pr_diff").isNotNull())
    print(f"There are {candidates.count()} PRs as candidates")

    required_files = candidates.select(
        "id",
        F.col("base.repo_full_name").alias("repo_name"),
        F.col("base.sha").alias("commit_sha"),
        F.explode(get_base_files(F.col("pr_diff"))).alias("path"),
    ).select(
        "id",
        F.concat_ws("/", "repo_name", "commit_sha", "path").alias(JOIN_COL),
    )
    required_files.printSchema()

    saved_files = spark.read.parquet(REPO_FILE_MAPPING).select(
        F.concat_ws("/", "repo_name", "commit_sha", "path").alias(JOIN_COL)
    )
    saved_files.printSchema()

    incomplete = (
        required_files.join(saved_files, on=JOIN_COL, how="left_anti")
        .select("id")
        .distinct()
        .persist()
    )
    print(f"There are {incomplete.count()} PRs with missing edited files")

    print(f"Saving complete PRs to {PR_COMPLETE}")
    (
        candidates.join(incomplete, on="id", how="left_anti")
        .write.mode("overwrite")
        .parquet(PR_COMPLETE)
    )

    spark.stop()


if __name__ == "__main__":
    main()

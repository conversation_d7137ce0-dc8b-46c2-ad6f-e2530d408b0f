"""Join PR data with repos.
The output contains file sha only without real file content.
"""

import pyspark.sql.functions as F

from research.data.spark import k8s_session

# Spark settings
NUM_WORKERS = 64
NUM_PARTITIONS = 5000

# Input and output paths
PR_COMPLETE = "/mnt/efs/spark-data/shared/gh_pr_complete"
REPO_FILE_MAPPING = "/mnt/efs/spark-data/shared/gh_pr_repo_files_2m/repo_file_mapping"
OUTPUT = "/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha"

# Repos that are too large (>2GB) or have too many PRs
# Downsampling them might still cause OOM in middle or downstream steps
TOO_LARGE_REPOS = [
    "cfpb/cfgov-lighthouse",
    "cloudsecurityalliance/gsd-database",
    "google/material-design-icons",
    "stdlib-js/www",
    "NixOS/nixpkgs",
]


def main():
    print(f"Starting spark with {NUM_WORKERS} workers")
    spark = k8s_session(
        min_workers=NUM_WORKERS,
        max_workers=NUM_WORKERS,
        conf={
            "spark.executor.cores": "4",
            "spark.executor.memory": "64G",
            "spark.sql.shuffle.partitions": f"{NUM_PARTITIONS}",
            "spark.kryo.referenceTracking": "false",
            "spark.sql.parquet.columnarReaderBatchSize": "32",
        },
    )

    pr_complete = spark.read.parquet(PR_COMPLETE)

    # Step 1: Find all required files for PRs in the input data
    required_repo_shas = (
        pr_complete.select(
            F.col("base.repo_full_name").alias("repo_name"),
            F.col("base.sha").alias("commit_sha"),
        )
        .distinct()
        .filter(~F.col("repo_name").isin(TOO_LARGE_REPOS))
    )
    required_files = spark.read.parquet(REPO_FILE_MAPPING).join(
        required_repo_shas, on=["repo_name", "commit_sha"], how="inner"
    )

    # Step 2: Group required files by repo and commit hash
    grouped_files = required_files.groupBy("repo_name", "commit_sha").agg(
        F.collect_list(F.struct("path", "file_sha")).alias("files"),
    )

    # Step 3: Join PR data with repo files
    joined = pr_complete.withColumns(
        {
            "repo_name": F.col("base.repo_full_name"),
            "commit_sha": F.col("base.sha"),
        }
    ).join(grouped_files, on=["repo_name", "commit_sha"], how="inner")

    # Step 4: Save joined data
    print(f"Saving joined PRs to {OUTPUT}")
    (
        joined.repartition(NUM_PARTITIONS, "repo_name")
        .write.mode("overwrite")
        .parquet(OUTPUT)
    )

    spark.stop()


if __name__ == "__main__":
    main()

"""Spark job to download repo branches."""

import argparse
import logging
import subprocess
from tempfile import TemporaryDirectory
from typing import Sequence

import pyspark.sql.functions as F

from research.data.spark import k8s_session
from research.data.utils.pr_v2 import (
    PR_DATA_CW,
    PR_DATA_GCP,
    REPO_BRANCHES,
    get_tar_dir_path,
    get_tar_full_path,
)

# Spark settings
NUM_WORKERS = 64
CORES_PER_WORKER = 4
RAM_PER_WORKER = "64G"
NUM_PARTITIONS = NUM_WORKERS * CORES_PER_WORKER * 4

# Whether to load PR data from GCP
# Note that we should avoid excessive data loading from GCP to CW
LOAD_FROM_GCP = False

# PR count threshold for a repo to be downloaded
# This is the default value and can be changed by the --pr-count-threshold argument
PR_COUNT_THRESHOLD = 100

# Max number of branches to download for each repo
MAX_BRANCHES_PER_REPO = 100

# Git command configs
MAX_DEPTH = 1000
TIMEOUT = 1800

# Repos to be excluded from downloading
# It's usually due to the repo being too large or having too many branches
BLOCK_LIST = [
    "cdnjs/cdnjs",
    "covid19tracking/covid-tracking-data",
    "mackorone/spotify-playlist-archive",
]

# Repos to be downloaded with only the branch with the most PRs
SINGLE_BRANCH_LIST = [
    "clickhouse/clickhouse",
]

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def git_wrapper(
    cmd_list: Sequence[str],
    path_to_repo: str | None = None,
):
    """Wrapper for executing git commands."""
    path_args = ["-C", path_to_repo] if path_to_repo else []
    subprocess.run(
        ["git"] + path_args + list(cmd_list),
        timeout=TIMEOUT,
        stdout=subprocess.DEVNULL,
        stderr=subprocess.DEVNULL,
    )


def download_repo_branches(iterator):
    """Download the repo and its listed branches."""
    for repo_name, repo_pr_count, branch_list in iterator:
        if repo_pr_count == 0 or len(branch_list) == 0:
            logger.warning(f"Skipping {repo_name} with no pr or branch")
            continue
        if repo_name in BLOCK_LIST:
            logger.warning(f"Skipping {repo_name} in block list")
            continue

        dir_path = get_tar_dir_path(repo_name)
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)

        full_tar_path = get_tar_full_path(repo_name)
        if full_tar_path.exists():
            logger.warning(f"Skipping already downloaded {repo_name}")
            continue

        branches = [branch_info for branch_info in branch_list]
        branches.sort(key=lambda x: x[1], reverse=True)
        if len(branches) > MAX_BRANCHES_PER_REPO:
            branches = branches[:MAX_BRANCHES_PER_REPO]
        if repo_name in SINGLE_BRANCH_LIST:
            branches = [branches[0]]

        logger.info(f"Start processing {repo_name} with {len(branches)} branches")
        with TemporaryDirectory() as repo_dir:
            try:
                logger.info("Cloning default branch")
                git_wrapper(
                    cmd_list=[
                        "clone",
                        "--single-branch",
                        f"--depth={MAX_DEPTH}",
                        f"https://github.com/{repo_name}.git",
                        repo_dir,
                    ]
                )
            except Exception:  # pylint: disable=broad-except
                logger.warning(f"Failed to clone default branch for {repo_name}")
                continue

            # Fetch branches in the list to the same repo directory
            # It's safe to add and fetch the same remote branch multiple times
            for branch_info in branches:
                branch = branch_info[0]
                try:
                    logger.info(f"Fetching branch {branch}")
                    git_wrapper(
                        cmd_list=["remote", "set-branches", "--add", "origin", branch],
                        path_to_repo=repo_dir,
                    )
                    git_wrapper(
                        cmd_list=["fetch", f"--depth={MAX_DEPTH}", "origin", branch],
                        path_to_repo=repo_dir,
                    )
                except Exception:  # pylint: disable=broad-except
                    logger.warning(f"Failed to fetch branch {branch} for {repo_name}")
                    continue

            try:
                logger.info(f"Archiving repo to {full_tar_path}")
                subprocess.run(
                    ["tar", "-C", repo_dir, "-czf", str(full_tar_path), "."],
                )
            except Exception:  # pylint: disable=broad-except
                logger.warning(f"Failed to archive {repo_name}")
                continue

            logger.info(f"Finish processing {repo_name}")


def main(
    recompute_branches: bool = False,
    pr_count_threshold: int = PR_COUNT_THRESHOLD,
):
    spark = k8s_session(
        max_workers=NUM_WORKERS,
        conf={
            "spark.executor.cores": str(CORES_PER_WORKER),
            "spark.executor.memory": RAM_PER_WORKER,
        },
        access_gcp=LOAD_FROM_GCP,
    )

    if recompute_branches:
        input_path = PR_DATA_GCP if LOAD_FROM_GCP else PR_DATA_CW
        (
            spark.read.parquet(input_path)
            .select(
                F.lower(F.col("base.repo_full_name")).alias("repo_name"),
                F.col("base.ref").alias("branch_name"),
            )
            .groupBy("repo_name", "branch_name")
            .agg(
                F.count("*").alias("branch_pr_count"),
            )
            .groupBy("repo_name")
            .agg(
                F.sum("branch_pr_count").alias("repo_pr_count"),
                F.collect_list(
                    F.struct(
                        "branch_name",
                        "branch_pr_count",
                    )
                ).alias("branch_list"),
            )
            .repartition(10)
            .write.mode("overwrite")
            .parquet(REPO_BRANCHES)
        )
    else:
        logger.info(f"Using existing repo branches results at {REPO_BRANCHES}")

    repo_branches = spark.read.parquet(REPO_BRANCHES).filter(
        F.col("repo_pr_count") >= pr_count_threshold
    )

    repo_count = repo_branches.count()
    branch_count = repo_branches.select(F.explode("branch_list")).count()
    logger.info(f"Working on {repo_count} repos and {branch_count} branches")

    repo_branches.repartition(NUM_PARTITIONS).foreachPartition(download_repo_branches)

    spark.stop()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()

    parser.set_defaults(recompute_branches=False)
    parser.add_argument(
        "--recompute-branches",
        action="store_true",
        help="Whether to recompute the repo branches to download",
    )

    parser.add_argument(
        "--pr-count-threshold",
        type=int,
        default=PR_COUNT_THRESHOLD,
        help="PR count threshold for a repo to be downloaded",
    )

    args = parser.parse_args()
    main(args.recompute_branches, args.pr_count_threshold)

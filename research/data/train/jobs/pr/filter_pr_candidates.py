"""Filter PRs for downloading repos."""

from research.data.spark import k8s_session
import pyspark.sql.functions as F


# Spark settings
NUM_WORKERS = 40
NUM_PARTITIONS = 800

# Input and output paths
INPUT_TABLE = "augment-387916.github_generated.pr_candidates"
OUTPUT_PATH = "gs://bigquery-data-staging/gh_pr_candidates_filtered"

# Size limit for PR diff
PR_DIFF_LIMIT = 5e5
ADDITIONS_LIMIT = 5e3


def main():
    print(f"Starting spark with {NUM_WORKERS} workers")
    spark = k8s_session(
        min_workers=NUM_WORKERS,
        max_workers=NUM_WORKERS,
        conf={
            "spark.executor.memory": "32G",
            "spark.executor.cores": "8",
        },
    )

    print(f"Filtering PRs and saving to {OUTPUT_PATH}")
    (
        spark.read.format("bigquery")
        .option("table", INPUT_TABLE)
        .load()
        .filter(F.col("base.stargazers_count").isNotNull())
        .filter(F.col("base.stargazers_count") > 0)
        .filter(F.col("pr_diff").isNotNull())
        .filter(F.length(F.col("pr_diff")) < PR_DIFF_LIMIT)
        .filter(F.col("additions").isNotNull())
        .filter(F.col("additions") < ADDITIONS_LIMIT)
        .repartition(NUM_PARTITIONS)
        .write.mode("overwrite")
        .parquet(OUTPUT_PATH)
    )

    spark.stop()


if __name__ == "__main__":
    main()

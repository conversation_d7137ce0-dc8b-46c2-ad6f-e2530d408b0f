"""Spark job to fetch PR diffs from GitHub.

A common workflow will be:
1. Run query `gen_pr_diff_missing` on BigQuery to generate PRs without diffs at
    `github_generated.pr_diff_missing`
2. Run this script to fetch the diffs and save them to GCS under
    `gs://bigquery-data-staging/gh_pr_diff/yyyy-mm-dd`
3. Run query `dedupe_pr_diffs` on BigQuery to update table at
    `github_generated.pr_diff_deduped`
4. Export table `github_generated.pr_diff_deduped` to GCS under subdir
    `yyyy-00-00` and clean up other dates
"""

import datetime
import os
import time

import pyspark.sql.functions as F
import requests
from pyspark.sql.types import StringType

from research.data.spark import k8s_session

# GitHub token and headers
GITHUB_TOKEN_ENV = "GITHUB_TOKEN"
if GITHUB_TOKEN_ENV not in os.environ:
    raise ValueError(f"Please set {GITHUB_TOKEN_ENV} env to access GitHub API")

GITHUB_TOKEN = os.environ.get(GITHUB_TOKEN_ENV)
HEADERS = {
    "Authorization": f"Bearer {GITHUB_TOKEN}",
}

# Input BigQuery table that contains PR id and url for missing diffs
INPUT_TABLE = "augment-387916.github_generated.pr_diff_missing"

# Root directory for storing the output
# It's partitioned by job execution date
# E.g. gs://bigquery-data-staging/gh_pr_diff/yyyy-mm-dd/*.parquet
OUTPUT_ROOT = "gs://bigquery-data-staging/gh_pr_diff"

# Local directory for dumping the input table
LOCAL_DUMP_DIR = "/mnt/efs/spark-data/temporary/missing_diffs"

# Number of workers and partitions (also number of output files)
NUM_WORKERS = 20
NUM_PARTITIONS = 4096

# Current diff size limit
DIFF_SIZE_LIMIT = 1e6
# A special value to indicate that the diff is longer than the limit
DIFF_SIZE_OVER_LIMIT = "DIFF_SIZE_OVER_LIMIT"


@F.udf(returnType=StringType())
def add_pr_diff(url):
    try:
        response = requests.get(
            f"{url}.diff",
            headers=HEADERS,
            timeout=10,
        )
    except Exception:  # pylint: disable=broad-except
        return None

    if response.ok:
        diff = response.text
        if len(diff) > DIFF_SIZE_LIMIT:
            return DIFF_SIZE_OVER_LIMIT
        return diff
    else:
        if response.status_code == 429:
            # GitHub request rate limit exceeded
            time.sleep(1)
        return None


def main():
    spark = k8s_session(
        min_workers=NUM_WORKERS,
        max_workers=NUM_WORKERS,
        conf={
            "spark.app.name": "fetch-pr-diffs",
            "spark.executor.memory": "64G",
            "spark.executor.cores": "8",
            "spark.default.parallelism": str(NUM_PARTITIONS),
            "spark.debug.maxToStringFields": "100",
            "spark.sql.adaptive.enabled": "false",
            "spark.sql.adaptive.coalescePartitions.enabled": "false",
            "spark.sql.adaptive.advisoryPartitionSizeInBytes": "1KB",
        },
    )

    # Determine the output directory by the current date
    date = datetime.datetime.now().strftime("%Y-%m-%d")
    output_dir = f"{OUTPUT_ROOT}/{date}"
    print(f"Output will be written to {output_dir}")

    # Load the input table to get metadata about missing diffs
    missing_diffs = spark.read.format("bigquery").option("table", INPUT_TABLE).load()
    count = missing_diffs.count()
    print(f"There are {count} PRs to process and fetch diffs from github")

    # Dump the input table to a local directory after repartitioning it
    print(f"Dump the input table to a local directory with {NUM_PARTITIONS} partitions")
    (
        missing_diffs.repartition(NUM_PARTITIONS)
        .write.format("parquet")
        .mode("overwrite")
        .save(LOCAL_DUMP_DIR)
    )

    # Process the missing diffs and save them to the output directory
    print("Start to process the missing diffs")
    (
        spark.read.parquet(LOCAL_DUMP_DIR)
        .withColumn("pr_diff", add_pr_diff("url"))
        .select("id", "pr_diff")
        .write.format("parquet")
        .option("intermediateFormat", "orc")
        .save(output_dir)
    )

    spark.stop()


if __name__ == "__main__":
    main()

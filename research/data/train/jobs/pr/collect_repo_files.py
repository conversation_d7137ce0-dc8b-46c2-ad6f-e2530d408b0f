# ruff: noqa: F821
"""Spark job to collect repository files for PRs."""

import shutil
import subprocess
import time
from pathlib import Path
from tempfile import TemporaryDirectory

import pandas as pd
import pygit2
import pyspark.sql.functions as F

from research.data.spark import k8s_session

# Spark settings
WORKERS = 64
CORES = 2
NUM_PARTITIONS = WORKERS * CORES * 2

# Input and output paths
PR_DATA_PATH = "/mnt/efs/spark-data/shared/gh_pr_candidates/"
REPO_SHA_PATH = "/mnt/efs/spark-data/shared/_temporary/gh_working_repo_sha/"
REPO_FILES_OUTPUT = "/mnt/efs/spark-data/shared/_temporary/repo_files"
FILE_CONTENT_OUTPUT = "/mnt/efs/spark-data/shared/_temporary/file_content"

# Size limit in bytes for files to be saved
FILE_SIZE_LIMIT = 1e6

# Flush thresholds
REPO_FILES_FLUSH_THRESHOLD = 1e7
FILE_CONTENT_FLUSH_THRESHOLD = 5e8

# Output dataset column names
REPO_FILES_COLUMNS = ["repo_name", "commit_sha", "path", "file_sha"]
FILE_CONTENT_COLUMNS = ["file_sha", "content"]


def save_to_parquet(data, columns, output_dir):
    df = pd.DataFrame(data, columns=columns)

    retry = 0
    while retry < 3:
        output_path = f"{output_dir}/{int(time.time())}.parquet"
        df.to_parquet(output_path)

        try:
            saved_size = len(pd.read_parquet(output_path))
            print(f"Saved {saved_size} records to {output_path}")
            break
        except Exception:  # pylint: disable=broad-except
            print(f"Failed to read the saved data (retry={retry})")

        retry += 1
        time.sleep(10)
        try:
            Path(output_path).unlink(missing_ok=True)
        except Exception:  # pylint: disable=broad-except
            pass


def save_repo_files(iterator):
    saved_file_shas = set()  # Files are deduplicated within each partition
    repo_files = []
    file_content = []
    file_content_sizes = []

    for repo_name, sha_list in iterator:
        print(f"Start processing {repo_name}")

        with TemporaryDirectory() as repo_dir:
            try:
                repo_clone_url = f"https://github.com/{repo_name}.git"
                subprocess.run(
                    [
                        "git",
                        "clone",
                        repo_clone_url,
                        "--single-branch",
                        "--bare",
                        "--depth=1000",
                        repo_dir,
                    ],
                    timeout=1800,
                )
                repo = pygit2.Repository(repo_dir)
            except Exception:  # pylint: disable=broad-except
                print(f"Failed to clone and load {repo_name}")
                continue

            for sha in sha_list:
                try:
                    commit = repo[sha]
                    if not isinstance(commit, pygit2.Commit):
                        continue
                    root_tree = commit.tree
                except Exception:  # pylint: disable=broad-except
                    print(f"Failed to get tree for {repo_name} {sha}")
                    continue

                def get_files(
                    repo: pygit2.Repository,
                    tree: pygit2.Tree,
                    repo_name: str,
                    sha: str,
                    prev_path: str = "",
                ):
                    for entry in tree:
                        entry_path = prev_path + str(entry.name)

                        if entry.type == pygit2.GIT_OBJ_BLOB:
                            file_sha = entry.hex
                            try:
                                obj = repo[file_sha]
                            except Exception:  # pylint: disable=broad-except
                                print(f"Failed to get blob for {file_sha}")
                                continue
                            if not isinstance(obj, pygit2.Blob):
                                continue

                            try:
                                data = obj.data
                                ext = Path(entry_path).suffix.lower()
                                if (
                                    not data
                                    or obj.is_binary
                                    or obj.size > FILE_SIZE_LIMIT
                                    or ext in (".pdf", ".html")
                                ):
                                    continue

                                content = ""
                                if isinstance(data, str):
                                    content = str(data)
                                elif isinstance(data, memoryview):
                                    content = data.tobytes().decode("utf-8")
                                else:
                                    content = data.decode("utf-8")
                            except Exception:  # pylint: disable=broad-except
                                print(f"Failed to load and decode {file_sha}")
                                continue

                            repo_files.append([repo_name, sha, entry_path, file_sha])
                            if file_sha not in saved_file_shas:
                                saved_file_shas.add(file_sha)
                                file_content.append([file_sha, content])
                                file_content_sizes.append(len(content))

                            # Flush to disk
                            if len(repo_files) > REPO_FILES_FLUSH_THRESHOLD:
                                save_to_parquet(
                                    repo_files,
                                    REPO_FILES_COLUMNS,
                                    REPO_FILES_OUTPUT,
                                )
                                repo_files.clear()
                            if sum(file_content_sizes) > FILE_CONTENT_FLUSH_THRESHOLD:
                                save_to_parquet(
                                    file_content,
                                    FILE_CONTENT_COLUMNS,
                                    FILE_CONTENT_OUTPUT,
                                )
                                file_content.clear()
                                file_content_sizes.clear()
                        elif entry.type == pygit2.GIT_OBJ_TREE:
                            if isinstance(entry, pygit2.Tree):
                                get_files(repo, entry, repo_name, sha, entry_path + "/")
                        else:
                            print(f"Unknown entry type {entry.type}")

                get_files(repo, root_tree, repo_name, sha)

        print(f"Finish processing {repo_name}")

    if len(repo_files) > 0:
        save_to_parquet(repo_files, REPO_FILES_COLUMNS, REPO_FILES_OUTPUT)
    if len(file_content) > 0:
        save_to_parquet(file_content, FILE_CONTENT_COLUMNS, FILE_CONTENT_OUTPUT)

    # Manually free memory to avoid potential OOM
    del saved_file_shas
    del repo_files
    del file_content
    del file_content_sizes


def prepare_folder(folder_path: str):
    print(f"Cleaning up and preparing output folder {folder_path}")
    if Path(folder_path).exists():
        shutil.rmtree(folder_path, ignore_errors=True)
    Path(folder_path).mkdir(parents=True, exist_ok=True)


def main():
    print(f"Starting spark with {WORKERS} workers")
    spark = k8s_session(
        min_workers=WORKERS,
        max_workers=WORKERS,
        conf={
            "spark.executor.cores": f"{CORES}",
            "spark.executor.memory": "64G",
        },
    )

    print(f"Getting repo shas to work on and saving to {REPO_SHA_PATH}")
    (
        spark.read.parquet(PR_DATA_PATH)
        .select("base.repo_full_name", "base.sha")
        .distinct()
        .groupBy("repo_full_name")
        .agg(F.collect_set("sha").alias("sha_list"))
        .repartition(10)
        .write.mode("overwrite")
        .parquet(REPO_SHA_PATH)
    )

    repo_shas = spark.read.parquet(REPO_SHA_PATH)

    repo_count = repo_shas.count()
    sha_count = repo_shas.select(F.explode("sha_list")).count()
    print(f"Working on {repo_count} repos and {sha_count} shas")

    prepare_folder(REPO_FILES_OUTPUT)
    prepare_folder(FILE_CONTENT_OUTPUT)

    print("Starting to collect files")
    repo_shas.repartition(NUM_PARTITIONS).foreachPartition(save_repo_files)

    spark.stop()


if __name__ == "__main__":
    main()

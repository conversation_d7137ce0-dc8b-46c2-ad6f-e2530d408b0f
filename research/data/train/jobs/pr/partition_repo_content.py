"""Partition file content by repo."""

import pyspark.sql.functions as F
from pyspark.sql import DataFrame, SparkSession

from research.data.spark import k8s_session

# Spark settings
NUM_WORKERS = 64
NUM_PARTITIONS = 5000

# Input and output paths
PR = "/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha"
REPO_FILE_MAPPING = "/mnt/efs/spark-data/shared/gh_pr_repo_files_2m/repo_file_mapping"
FILE_CONTENT = "/mnt/efs/spark-data/shared/gh_pr_repo_files_2m/file_content"
OUTPUT = "/mnt/efs/spark-data/shared/gh_pr_repo_content"
REFERENCE = "/mnt/efs/spark-data/shared/gh_pr_repo_ref.parquet"


def get_repo_to_file_mapping(
    spark: SparkSession, data_path: str, col_name: str
) -> DataFrame:
    """Get a mapping of repo names to parquet file paths."""
    return (
        spark.read.parquet(data_path)
        .select(
            "repo_name",
            F.substring(F.input_file_name(), 8, 100000).alias(
                col_name
            ),  # Drop prefix 'file://'
        )
        .distinct()
    )


def main():
    print(f"Starting spark with {NUM_WORKERS} workers")
    spark = k8s_session(
        min_workers=NUM_WORKERS,
        max_workers=NUM_WORKERS,
        conf={
            "spark.executor.cores": "4",
            "spark.executor.memory": "64G",
            "spark.sql.shuffle.partitions": f"{NUM_PARTITIONS}",
            "spark.kryo.referenceTracking": "false",
            "spark.sql.parquet.columnarReaderBatchSize": "32",
        },
    )

    required_repo_shas = (
        spark.read.parquet(PR).select("repo_name", "commit_sha").distinct()
    )
    required_repo_files = (
        spark.read.parquet(REPO_FILE_MAPPING)
        .join(required_repo_shas, on=["repo_name", "commit_sha"], how="inner")
        .select("repo_name", "file_sha")
        .distinct()
    )

    file_content = spark.read.parquet(FILE_CONTENT)
    joined = required_repo_files.join(file_content, on="file_sha", how="inner")

    print(f"Saving output to {OUTPUT}")
    (
        joined.repartition(NUM_PARTITIONS, "repo_name")
        .write.mode("overwrite")
        .parquet(OUTPUT)
    )

    # Create a reference file for mapping repo names to parquet files
    joined_sha_mapping = get_repo_to_file_mapping(spark, PR, "joined_sha_parquet")
    content_mapping = get_repo_to_file_mapping(spark, OUTPUT, "content_parquet")

    count = joined_sha_mapping.count()
    assert content_mapping.count() == count
    print(f"Collected {count} repo mappings")

    print(f"Saving reference file to {REFERENCE}")
    (
        joined_sha_mapping.join(content_mapping, on="repo_name", how="inner")
        .coalesce(1)
        .write.mode("overwrite")
        .parquet(REFERENCE)
    )

    spark.stop()


if __name__ == "__main__":
    main()

"""Group PRs by repo after filtering."""

import argparse
import os

import pyspark.sql.functions as F
from pyspark.sql.types import StringType

from research.data.spark import k8s_session
from research.data.utils.pr_v2 import (
    DOWNLOADED_REPOS,
    PR_DATA_CW,
    PR_GROUPED,
    REPO_TAR_EXT,
    get_tar_full_path,
)

# Spark settings
NUM_WORKERS = 64
CORES_PER_WORKER = 4
RAM_PER_WORKER = "64G"
NUM_PARTITIONS = NUM_WORKERS * CORES_PER_WORKER * 4
DEFAULT_OUTPUT_FILES = 1000

# Filter settings
TAR_SIZE_LIMIT = 2e9  # 2GB

# Repos having a large number of PRs but are not suitable for training
BLOCK_LIST = [
    "firstcontributions/first-contributions",
    "homebrew/homebrew-cask",
    "homebrew/homebrew-core",
    "juliaregistries/general",
    "microsoft/winget-pkgs",
    "nixos/nixpkgs",
    "openshift/openshift-docs",
    "wolseybankwitness/rediffusion",
]


def count_downloaded_repos(root: str) -> int:
    """Count the number of downloaded repos."""
    repo_count = 0
    for _, _, files in os.walk(root):
        for file in files:
            if file.endswith(REPO_TAR_EXT):
                repo_count += 1
    return repo_count


@F.udf(StringType())
def path_to_repo_tar(repo_name: str) -> str | None:
    """Get the path to the tar file for the given repo name.

    If the tar file does not exist, or is empty, or is too large, return None.
    """
    full_tar_path = get_tar_full_path(repo_name)

    if (
        not full_tar_path.exists()
        or full_tar_path.stat().st_size == 0
        or full_tar_path.stat().st_size > TAR_SIZE_LIMIT
        or repo_name in BLOCK_LIST
    ):
        return None

    return str(full_tar_path)


def main(drop_diff: bool, output_files: int):
    repo_count = count_downloaded_repos(DOWNLOADED_REPOS)
    print(f"There are {repo_count} repos downloaded")

    spark = k8s_session(
        max_workers=NUM_WORKERS,
        conf={
            "spark.executor.cores": str(CORES_PER_WORKER),
            "spark.executor.memory": RAM_PER_WORKER,
            "spark.default.parallelism": str(NUM_PARTITIONS),
            "spark.sql.shuffle.partitions": str(NUM_PARTITIONS),
        },
    )

    input_df = spark.read.parquet(PR_DATA_CW)
    struct_cols = [col for col in input_df.columns if not drop_diff or col != "pr_diff"]

    (
        input_df.withColumn(
            "repo_name",
            F.lower(F.col("base.repo_full_name")),
        )
        .withColumn(
            "path_to_repo_tar",
            path_to_repo_tar(F.col("repo_name")),
        )
        .filter(F.col("path_to_repo_tar").isNotNull())
        .groupBy("repo_name")
        .agg(
            F.first("path_to_repo_tar").alias("path_to_repo_tar"),
            F.collect_list(F.struct(struct_cols)).alias("pr_list"),
        )
        .repartition(output_files)
        .write.mode("overwrite")
        .parquet(f"{PR_GROUPED}_{output_files//1000}k")
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser()

    parser.set_defaults(drop_diff=False)
    parser.add_argument(
        "--drop-diff",
        action="store_true",
        help="Whether to drop the PR diff column in the output",
    )

    parser.add_argument(
        "--output-files",
        type=int,
        default=DEFAULT_OUTPUT_FILES,
        help="Number of output files",
    )

    args = parser.parse_args()
    main(args.drop_diff, args.output_files)

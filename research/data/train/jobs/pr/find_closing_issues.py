"""A job to fetch closing issues for PRs by querying GitHub GraphQL API."""

import json
import time
from pathlib import Path

import pandas as pd
import requests

TOKEN = "ADD_YOUR_TOKEN_HERE"
HEADERS = {"Content-Type": "application/json", "Authorization": f"Bearer {TOKEN}"}
API_URL = "https://api.github.com/graphql"

INPUT = "/mnt/efs/spark-data/shared/gh_pr_issues/query_input"
OUTPUT = "/mnt/efs/spark-data/shared/gh_pr_issues/query_output"

ISSUE_SIZE = 10


def main():
    min_remain_rate = 5000  # for monitoring

    print(f"Start processing PRs under {INPUT}")
    for path in Path(INPUT).glob("*.parquet"):
        output_path = f"{Path(OUTPUT) / path.name[:10]}.json"
        if Path(output_path).exists():
            print(f"Output file {output_path} already exists, skipping")
            continue

        print(f"Start processing file {path}")
        data = pd.read_parquet(path)
        output = []

        for _, row in data.iterrows():
            repo_name = row["repo_name"]
            owner, name = repo_name.split("/", 1)
            pr_number = row["number"]

            try:
                query = """
                    query {
                        repository(owner: "%s", name: "%s") {
                            pullRequest(number: %d) {
                                closingIssuesReferences(first: %d) {
                                    nodes {
                                        repository {
                                            nameWithOwner
                                        }
                                        number
                                        url
                                    }
                                }
                            }
                        }
                    }
                    """ % (
                    owner,
                    name,
                    pr_number,
                    ISSUE_SIZE,
                )

                response = requests.post(
                    API_URL, json={"query": query}, headers=HEADERS
                )

                sleep_time = 0

                remain_rate_limit = int(response.headers["x-ratelimit-remaining"])
                min_remain_rate = min(min_remain_rate, remain_rate_limit)
                print(
                    f"Remaining rate limit: {remain_rate_limit} (min={min_remain_rate})"
                )

                if remain_rate_limit <= 0:
                    if "x-ratelimit-reset" in response.headers:
                        rate_limit_reset = float(response.headers["x-ratelimit-reset"])
                        print(
                            f"Found rate-limit-reset time in headers: {rate_limit_reset}"
                        )
                        sleep_time = rate_limit_reset - time.time()
                    else:
                        sleep_time = 60

                if "retry-after" in response.headers:
                    retry_after = float(response.headers["retry-after"])
                    print(f"Found retry-after time in headers: {retry_after}")
                    sleep_time = max(sleep_time, retry_after)

                if sleep_time > 0:
                    print(f"Rate limit reached, sleeping for {sleep_time} seconds")
                    time.sleep(sleep_time)
                    continue

                if response.status_code != 200:
                    print(
                        f"Failed to fetch issues for {repo_name} {pr_number}: {response.status_code}"
                    )
                    time.sleep(1)
                    continue

                response_json = response.json()
                issues = [
                    [node["repository"]["nameWithOwner"], node["number"]]
                    for node in response_json["data"]["repository"]["pullRequest"][
                        "closingIssuesReferences"
                    ]["nodes"]
                ]

                new_record = {
                    "repo_name": repo_name,
                    "pr_number": pr_number,
                    "issues": issues,
                }
                if len(issues) > 0:
                    print(new_record)

                output.append(new_record)

                # Sleep to avoid rate limit (5000 per hour)
                time.sleep(0.5)
            except Exception:  # pylint: disable=broad-except
                print(f"Failed to fetch issues for {repo_name} {pr_number}")
                time.sleep(1)

        with open(output_path, "w") as json_file:
            json.dump(output, json_file)
        output.clear()


if __name__ == "__main__":
    main()

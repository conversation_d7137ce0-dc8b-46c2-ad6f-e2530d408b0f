"""Job to generate the augstack_by_repo dataset.

The output dataset will contain either:
* one grouped repo per row, or
* one ungrouped file per row.
File content will be saved in a single column as an array.

Please note that in the output dataset, it will group files that:
1. have a valid repo name (isNotNull)
2. have content length <= MAX_FILE_SIZE_TO_GROUP_BY_REPO
3. are not in the denylist of repos

We set the file size limit here because for very large files they
are unlikely to be concatenated with other files in the same repo
to form a training sample (except the first and last chunks). These
files will be processed independently and separately, while other
smaller files will be organized and processed at the repo level.
"""

import logging
import math
import re
import zlib
from collections import Counter

import pyspark.sql.functions as F
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.types import BooleanType

from research.data.spark import k8s_session
from research.data.train.common.constants import (
    MAX_FILE_SIZE_TO_GROUP_BY_REPO,
    RANDOM_SEED,
)
from research.data.train.common.lang_utils import STARCODER_EXTRA
from research.data.train.common.user_repo_denylists import REPO_DENYLIST
from research.data.train.datasets.augmented_stack import (
    augstack_by_repo,
    augstack_deduped,
)
from research.data.train.datasets.starcoder import starcoder_raw

# Existing columns in the input AugStack dataset.
REPO_NAME_COL = "repo_name"
FILE_PATH_COL = "repo_path"
STARS_COL = "stars_count"
CONTENT_COL = "content"
SIZE_COL = "size"
SOURCE_COL = "source"
LANG_COL = "lang"

# New columns/fields to be added to the final output dataset.
SUM_SIZE_COL = "sum_size"
FILE_COUNT_COL = "file_count"
FILE_LIST_COL = "file_list"
IS_GROUPED_COL = "is_grouped"

# Path to the intermediate dataset before grouping.
INTERMEDIATE_PATH = "/mnt/efs/spark-data/shared/aug-stack/_intermediate/pre-grouping/"

# Downsample rates for certain languages.
DOWNSAMPLE_RATES = {
    "css": 0.25,
    "json": 0.25,
    "yaml": 0.25,
    "html": 0.5,
}

WORD_PATTERN = re.compile(r"\b\w+\b")

# We exclude some repos from the grouping process due to their sizes.
REPO_GROUPING_DENYLIST = [
    "npocmaka/windows-server-2003",
]


def get_starcoder_extra_df(spark: SparkSession) -> DataFrame:
    """Get StarCoder exta languages using the same schema as AugStack."""
    return (
        spark.read.format(starcoder_raw.format)
        .load(starcoder_raw.location)
        .filter(F.col(LANG_COL).isin(STARCODER_EXTRA))
        .select(
            F.col("max_stars_repo_name").alias(REPO_NAME_COL),
            F.col("max_stars_repo_path").alias(FILE_PATH_COL),
            F.col("max_stars_count").alias(STARS_COL),
            F.col(CONTENT_COL),
            F.length(F.col(CONTENT_COL)).alias(SIZE_COL),
            F.lit(None).alias("avg_line_length"),
            F.lit(None).alias("max_line_length"),
            F.lit(None).alias("alphanum_fraction"),
            F.lit("starcoder_extra").alias(SOURCE_COL),
            F.lit(None).alias("lang_id"),
            F.col(LANG_COL),
        )
    )


def downsample_languages(df: DataFrame) -> DataFrame:
    """Downsample size of certain languages."""
    for lang, rate in DOWNSAMPLE_RATES.items():
        df = df.filter(
            (F.col(LANG_COL) != lang)
            | ((F.col(LANG_COL) == lang) & (F.rand(seed=RANDOM_SEED) < rate))
        )
    return df


@F.udf(returnType=BooleanType())
def content_quality_filter(content: str) -> bool:
    """Filter out rows that do not meet the content quality conditions."""

    words = WORD_PATTERN.findall(content)
    total_word_count = len(words)
    frequencies = Counter(words)
    # Convert frequencies to probabilities
    probabilities = [f / total_word_count for f in frequencies.values()]
    # Calculate entropy using probabilities
    word_entropy = -sum(
        p * math.log2(p) for p in probabilities if p > 0
    )  # Ensure p > 0 to avoid log2(0)
    # Normalize the entropy by dividing by log2(total_word_count)
    word_entropy = (
        word_entropy / math.log2(total_word_count) if total_word_count > 1 else 0.0
    )  # Check total_word_count > 1 to avoid division by zero

    btext = content.encode("utf-8")
    original_size = len(btext)
    compressed_size = len(zlib.compress(btext))
    compression_ratio = original_size / compressed_size

    return (
        word_entropy > 0.20
        and word_entropy < 0.95
        and (original_size < 20000 or compression_ratio >= 2)
        and compression_ratio <= 15
    )


def group_by_repo(input_df: DataFrame) -> DataFrame:
    """Group the input dataset by repo."""
    struct_cols = [col for col in input_df.columns]
    output_df = (
        input_df.groupBy(REPO_NAME_COL)
        .agg(
            F.sum(F.col(SIZE_COL)).alias(SUM_SIZE_COL),
            F.count("*").alias(FILE_COUNT_COL),
            F.collect_list(F.struct(struct_cols)).alias(FILE_LIST_COL),
        )
        .withColumn(IS_GROUPED_COL, F.lit(True))
    )

    return output_df


def group_by_file(input_df: DataFrame) -> DataFrame:
    """Group the input dataset by file to match the output schema."""
    struct_cols = [col for col in input_df.columns]
    output_df = input_df.select(
        F.col(REPO_NAME_COL),
        F.col(SIZE_COL).alias(SUM_SIZE_COL),
        F.lit(1).alias(FILE_COUNT_COL),
        F.array(F.struct(struct_cols)).alias(FILE_LIST_COL),
        F.lit(False).alias(IS_GROUPED_COL),
    )

    return output_df


def main():
    logging.basicConfig(level=logging.INFO)

    logging.info("Starting spark")
    spark = k8s_session(
        min_workers=128,
        max_workers=128,
        conf={
            "spark.executor.cores": "4",
            "spark.executor.memory": "128G",
            "spark.sql.shuffle.partitions": "1000",
        },
    )

    # 1st stage
    df = spark.read.format(augstack_deduped.format).load(augstack_deduped.location)
    df = df.union(get_starcoder_extra_df(spark))

    df = (
        downsample_languages(df)
        .filter(
            F.col(REPO_NAME_COL).isNull() | (~F.col(REPO_NAME_COL).isin(REPO_DENYLIST))
        )
        .filter(content_quality_filter(F.col(CONTENT_COL)))
    )

    df.repartition(500).write.mode("overwrite").parquet(INTERMEDIATE_PATH)

    # 2nd stage
    df = spark.read.parquet(INTERMEDIATE_PATH)

    condition_null = F.col(REPO_NAME_COL).isNull()
    condition_large = F.col(SIZE_COL) > MAX_FILE_SIZE_TO_GROUP_BY_REPO
    condition_deny = F.col(REPO_NAME_COL).isin(REPO_GROUPING_DENYLIST)

    grouping_df = df.filter(~condition_null & ~condition_large & ~condition_deny)
    ungrouping_df = df.filter(condition_null | condition_large | condition_deny)

    df = group_by_repo(grouping_df).union(group_by_file(ungrouping_df))

    df.repartition(500).write.mode("overwrite").format(augstack_by_repo.format).save(
        augstack_by_repo.location
    )

    spark.stop()


if __name__ == "__main__":
    main()

"""Take a parquet dataset, tokenize and pack into samples."""

import argparse
import typing
from dataclasses import dataclass

import pandas as pd
from pyspark.sql import SparkSession

from base.tokenizers import create_tokenizer_by_name
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.utils import k8s_session
from research.data.train.common.pack_into_samples import pack_into_samples

# Default argument values
SEQ_LENGTH = 4096
ADD_ONE_TOKEN = True
CONTENT_FIELD = "content"
OUTPUT_COLUMN = "packed_samples"


@dataclass
class TokenizeAndPackOptions:
    """Options for tokenizing and packing."""

    input_path: str
    """Input dataframe location."""

    output_path: str
    """Output packed dataset location."""

    workers: int
    """number of spark workers to use."""

    context_length: int
    """context length to use for packing."""

    add_one_token: bool
    """whether to add one token to the end of each sample."""

    content_column: str
    """content column name."""

    output_column: str
    """output column name."""

    batch_size: int
    """Batch size for mapInPandas."""

    timeout_seconds: int
    """Timeout in seconds for processing each file."""

    task_info_location: str
    """Location to store task info."""

    tokenizer: str
    """Which tokenizer to use."""


# This takes a list of repos and creates packed samples
def create_packed_docs(
    spark_session: SparkSession,
    options: TokenizeAndPackOptions,
) -> dict[str, typing.Any]:
    """Convert parquet of text to parquet of packed samples.

    Here we define a function that takes a pandas dataframe containg
    text to be tokenized and packed binary arrays.

    The output is another pandas dataframe with the packed samples in the
    specific column defined in the options.

    Then we just run map_parquet.apply_pandas to apply the function to the
    input dataframe.
    """
    token_length = options.context_length + (1 if options.add_one_token else 0)

    def tokenize_and_pack(batch: pd.DataFrame) -> pd.DataFrame:
        # first tokenize each doc
        tokenizer = create_tokenizer_by_name(options.tokenizer)
        special_tokens = tokenizer.special_tokens
        tokens = []
        for content in batch[options.content_column]:
            doc = tokenizer.tokenize_safe(content) + [special_tokens.eos]
            tokens.append(doc)
        # pack samples
        samples = pack_into_samples(
            tokens, token_length, pad_token_id=-special_tokens.padding
        )
        return pd.DataFrame({options.output_column: samples})

    result = map_parquet.apply_pandas(
        spark_session,
        tokenize_and_pack,
        options.input_path,
        options.output_path,
        batch_size=options.batch_size,
        timeout=options.timeout_seconds,
        task_info_location=options.task_info_location,
        ignore_error=True,
    )

    print("Task stats:", result["status_count"])
    return result


def get_options() -> TokenizeAndPackOptions:
    """Get arguments from command line."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input-path",
        type=str,
        required=True,
        help="Input text dataframe location",
    )
    parser.add_argument(
        "--output-path",
        type=str,
        required=True,
        help="Output packed dataset location",
    )
    parser.add_argument(
        "--workers", type=int, default=50, help="number of spark workers to use"
    )
    parser.add_argument(
        "--context-length",
        type=int,
        default=SEQ_LENGTH,
        help="context length to use for packing",
    )
    parser.add_argument(
        "--add-one-token",
        type=bool,
        default=ADD_ONE_TOKEN,
        help="whether to add one token to the end of each sample",
    )
    parser.add_argument(
        "--content-column",
        type=str,
        default=CONTENT_FIELD,
        help="content column name",
    )
    parser.add_argument(
        "--output-column",
        type=str,
        default=OUTPUT_COLUMN,
        help="output column name",
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=10,
        help="Batch size for mapInPandas.",
    )
    parser.add_argument(
        "--timeout-seconds",
        type=int,
        default=2 * 60 * 60,
        help="Timeout in seconds for processing each file.",
    )
    parser.add_argument(
        "--task-info-location",
        type=str,
        default="/mnt/efs/spark-data/temp_weekly/tokenize_and_pack_task_logs/",
        help="Location to store task info.",
    )
    parser.add_argument(
        "--tokenizer",
        type=str,
        default="deepseek_coder_base",
        help="Which tokenizer to use.",
    )

    args = parser.parse_args()
    return TokenizeAndPackOptions(
        input_path=args.input_path,
        output_path=args.output_path,
        workers=args.workers,
        context_length=args.context_length,
        add_one_token=args.add_one_token,
        content_column=args.content_column,
        output_column=args.output_column,
        batch_size=args.batch_size,
        timeout_seconds=args.timeout_seconds,
        task_info_location=args.task_info_location,
        tokenizer=args.tokenizer,
    )


def main():
    options = get_options()
    spark = k8s_session(
        max_workers=options.workers,
        conf={
            "spark.executor.memory": "120G",
            "spark.executor.pyspark.memory": "100G",
        },
    )
    create_packed_docs(
        spark,
        options,
    )
    spark.stop()


if __name__ == "__main__":
    main()

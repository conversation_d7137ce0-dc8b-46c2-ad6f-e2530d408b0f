export CLUSTER_NAME=augstack-dedupe
export PROJECT_ID=augment-387916
export REGION=us-central1
export ZONE=us-central1-a

# gcloud dataproc clusters create $CLUSTER_NAME \
#     --enable-component-gateway \
#     --region $REGION \
#     --zone $ZONE \
#     --master-machine-type c2d-standard-16 \
#     --master-boot-disk-size 500 \
#     --num-workers 32 \
#     --worker-machine-type c2d-standard-16 \
#     --worker-boot-disk-size 500 \
#     --image-version 2.0-debian10 \
#     --project $PROJECT_ID
#
# sleep 60

gcloud dataproc jobs submit pyspark --cluster $CLUSTER_NAME \
    --region $REGION \
    --jars gs://spark-lib/bigquery/spark-bigquery-latest_2.12.jar \
    --driver-log-levels root=WARN \
    --properties="spark.executor.memory"="50g","spark.driver.memory"="16g","spark.executor.cores"="16","spark.task.cpus"="2" \
    minhash_deduplication_spark_by_repo.py \
    -- \
    --input "gs://aug-stack/v1/to_repo_dedupe/" \
    --output "gs://aug-stack/v1/repo_deduped_0_5/" \
    --min_ngram_size 5 \
    --ngram_size 5 \
    --column file_list \
    --threshold 0.5

gcloud dataproc clusters stop $CLUSTER_NAME --region $REGION

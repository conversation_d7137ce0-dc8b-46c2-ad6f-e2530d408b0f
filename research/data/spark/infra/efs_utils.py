"""Utilities for managing EFS mount points in Spark."""

import logging
from pathlib import Path
from typing import Optional, Sequence, Union

from research.core.constants import AUGMENT_DRIVES
from research.core.utils import find_mounted_drive
from research.data.spark.infra import default_configs

logger = logging.getLogger(__name__)


def get_cw_efs_path(
    region: Union[str, Sequence[str]] = "las1",
) -> tuple[str, Optional[Path]]:
    """Get the path to the EFS mount point on Coreweave.

    Uses the AUGMENT_DRIVE mapping to lookup drives in each region
    and the find_mounted_drive function to find the mounted path.

    Args:
        region:  One or a list of regions to look for drives.

    Returns:
        The EFS drive name and Path object pointing to its mount point.
    """
    if isinstance(region, str):
        region = [region]
    for region_name in region:
        for drive in AUGMENT_DRIVES.get(region_name, []):
            try:
                return drive, find_mounted_drive(drive)
            except ValueError:
                pass
    # Not found, sadly
    return "", None


def find_efs_path(
    region: Union[str, Sequence[str]] = "las1",
) -> Optional[Path]:
    """Find an available EFS mount point."""
    efs_drive, efs_path = "", None

    if default_configs.ON_COREWEAVE:
        efs_drive, efs_path = get_cw_efs_path(region)
        if efs_path is None:
            logger.info(
                f'Cannot find EFS mount point for region "{region}" using known drives;'
                "Will try to use other regions"
            )
            remainder = [r for r in ["las1", "lga1"] if r not in region]
            efs_drive, efs_path = get_cw_efs_path(remainder)

    if efs_path is None:
        logger.info("Trying all known mount locations")
        # Look through all the mount points in EFS_MOUNT.
        # These are all the shared drives mounted on the worker pods.
        # If they are also available on the driver we can use them to transfer packages
        volumes = default_configs.VOLUMES[default_configs._cluster.name]
        for drive, path_str in volumes.items():
            path = Path(path_str)
            if path.exists() and path.is_dir():
                logger.info(f"Found EFS mount at {path}")
                efs_drive = drive
                efs_path = path
                break

    if efs_path is None:
        logger.warning(
            "Cannot find any known augment shared drive mounts; "
            "Package sharing with workers is disabled"
        )
    else:
        logger.info(f"Using EFS drive {efs_drive} at {efs_path} for package sharing")

    return efs_path

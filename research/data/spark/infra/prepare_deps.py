"""<PERSON><PERSON><PERSON> to prepare dependencies for Spark workers.

This script is expected to be run as part of the Spark init container.
"""

import argparse
import tarfile
from pathlib import Path


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--base_path",
        type=str,
        required=True,
        help="path to the base directory where dependencies are stored locally",
    )

    args = parser.parse_args()
    base_path = Path(args.base_path)

    # Traverse the base path and extract all tar files
    # Folder structure will be unchanged
    tar_files = list(base_path.rglob("*.tar"))
    for file_path in tar_files:
        with tarfile.open(file_path, "r") as tar:
            extract_path = file_path.parent
            if extract_path.name == "user_base":
                # Directly extract the user_base tar file to the dependency root
                extract_path = base_path
            tar.extractall(path=extract_path)

    # Create symlinks into the site packages directory in the shared drive location
    # PySpark is configured to use this site-packages folder as user site
    # i.e this will be the site directory with highest priority
    site_path = base_path / "lib/python3.11/site-packages"
    site_path.mkdir(parents=True, exist_ok=True)

    # Maps all symlinks to be created in site package
    links = {
        "research": "research",
        "base": "base",
        "experimental": "experimental",
        "megatron": "research/gpt-neox/megatron",
        "services": "services",
    }
    augment_relative = Path("../../../augment")
    for link, source in links.items():
        link_path = site_path / link
        source_path = augment_relative / source
        link_path.symlink_to(source_path)

    # TODO(xiaolei): services protos are now relative to services
    # not in base/datasets/stubs.
    # Create a .pth file to add base/datasets/stubs to the Python path
    pth_file = site_path / "augment_stubs.pth"
    stubs_path = site_path / "base/datasets/stubs"
    pth_file.write_text(str(stubs_path.resolve()) + "\n")


if __name__ == "__main__":
    main()

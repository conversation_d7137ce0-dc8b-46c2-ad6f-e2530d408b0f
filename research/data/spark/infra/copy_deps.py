"""Utilities for copying dependencies to shared drive."""

import fnmatch
import logging
import shutil
import site
import socket
import tarfile
import tempfile
import uuid
from datetime import date
from pathlib import Path

from research.core.constants import AUGMENT_ROOT
from research.core.utils import time_block

logger = logging.getLogger(__name__)

# The script to prepare dependencies for Spark executors.
PREPARE_DEPS_SCRIPT = "research/data/spark/infra/prepare_deps.py"

# Patterns to ignore when copying dependencies
IGNORED_PATTERNS = [
    "*.pyc",
    "*.log",
    ".*",
    "__pycache__",
    "*.ipynb",
    "lm-evaluation-harness",
    "tests",
]


def archive_copy(source: str, destination: str):
    """Archive and copy a directory from source to destination.

    Both source and destination should be directory paths.
    """
    Path(destination).mkdir(parents=True, exist_ok=True)

    with tempfile.TemporaryDirectory() as tmpdir:
        tar_file = Path(tmpdir) / "archived.tar"
        with tarfile.open(tar_file, "w") as tar:
            tar.add(
                source,
                arcname=".",
                filter=lambda x: (
                    None
                    if any(
                        fnmatch.fnmatch(Path(x.name).name, pattern)
                        for pattern in IGNORED_PATTERNS
                    )
                    else x
                ),
            )
        shutil.copy(tar_file, destination)


def copy_deps(efs_root: Path) -> Path:
    """Copy dependencies to shared drive.

    This is needed for code/file dependency sharing between driver and executors.

    Args:
        efs_root: Path to the EFS mount point shared with executors.

    Returns:
        Absolute path pointing to the shared user base.
    """
    # if efs_root is not already .../python_env, create a subdirectory `python_env`
    # inside and use that as the starting path.  otherwise use the efs_root directly.
    # The shared base is in {efs_path}/{date}/{hostname}/{session_id}/

    date_str = date.today().strftime("%Y-%m-%d")
    hostname = socket.gethostname()
    session_id = str(uuid.uuid4())
    if efs_root.parts[-1] != "python_env":
        efs_path = efs_root / "python_env"
    else:
        efs_path = efs_root
    base_path = efs_path / date_str / hostname / session_id

    # Ensure the base_path is in the /mnt folder.  Just in case something messed up
    assert base_path.parts[:2] == (
        "/",
        "mnt",
    ), f"Shared drive should be in /mnt: {base_path}"
    logger.info(f"Start to copy dependencies to {base_path}")

    # Copy local user base folder over
    # Due to uuid this is guaranteed to be a new directory
    with time_block("Copy user_base", logger.info):
        user_base = site.getuserbase()
        archive_copy(user_base, str(base_path / "user_base"))

    # Copy packages in the augment repo
    aug_path = base_path / "augment"
    aug_path.mkdir(parents=True, exist_ok=True)
    aug_packages = ["base", "experimental", "research", "services"]
    for package in aug_packages:
        with time_block(f"Copy {package}", logger.info):
            archive_copy(str(AUGMENT_ROOT / package), str(aug_path / package))

    # Copy the preparation script
    shutil.copy(str(AUGMENT_ROOT / PREPARE_DEPS_SCRIPT), str(base_path))

    return base_path

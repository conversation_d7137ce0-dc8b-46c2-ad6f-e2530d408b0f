"""Utilities for managing K8s config."""

import logging
import os
import yaml

from kubernetes import client, config

logger = logging.getLogger(__name__)

# K8s config path environment variable
K8S_CONFIG_ENV = "KUBECONFIG"

# Default K8s config path
DEFAULT_K8S_CONFIG_PATH = os.path.expanduser("~/.kube/config")

# Default K8s API server port
DEFAULT_PORT = 443


def get_k8s_config_path() -> str | None:
    """Get the path to the K8s config that will be used by Spark applications.

    If KUBECONFIG is set in the environment, return the first valid path in the list.
    Otherwise, return the default path if it exists.
    Return None if no valid path is found.
    """
    if K8S_CONFIG_ENV in os.environ:
        env_paths = os.environ.get(K8S_CONFIG_ENV, "").split(":")
        valid_paths = []

        for path in env_paths:
            if path and path not in valid_paths and os.path.exists(path):
                valid_paths.append(path)
        logger.info(f"Found {len(valid_paths)} valid k8s config paths: {valid_paths}.")

        if valid_paths:
            logger.info(f"k8s config {valid_paths[0]} will be used.")
            return valid_paths[0]
        else:
            logger.warning(f"No valid k8s config path detected in {K8S_CONFIG_ENV}.")
    else:
        logger.info(f"{K8S_CONFIG_ENV} is not found in the environment.")

    if os.path.exists(DEFAULT_K8S_CONFIG_PATH):
        logger.info(f"Default k8s config {DEFAULT_K8S_CONFIG_PATH} will be used.")
        return DEFAULT_K8S_CONFIG_PATH
    else:
        logger.warning(f"Default k8s config {DEFAULT_K8S_CONFIG_PATH} does not exist.")

    return None


def set_k8s_config_path() -> str | None:
    """Verify and set the KUBECONFIG environment variable for Spark applications.

    Spark:
     - will only look at the first path in KUBECONFIG,
     - and will log a WARN every time,
     - and will error if the path doesn't exist.

    Therefore, we replace the KUBECONFIG env var with only a single, present file, and
    fallback to `~/.kube/config`.
    """
    k8s_config_path = get_k8s_config_path()
    if k8s_config_path:
        logger.warning(f"Setting {K8S_CONFIG_ENV} to {k8s_config_path}")
        os.environ[K8S_CONFIG_ENV] = k8s_config_path
    return k8s_config_path


def validate_k8s_config_path():
    """Validate the environment variable for the K8s config path."""
    if K8S_CONFIG_ENV in os.environ:
        env_paths = os.environ.get(K8S_CONFIG_ENV, "").split(":")
        if len(env_paths) == 1 and os.path.exists(env_paths[0]):
            return
    raise RuntimeError(f"Expected one single valid path in {K8S_CONFIG_ENV}")


def get_k8s_api_server(config_path: str, context: str) -> str:
    """Get the corresponding K8s API server for the given context."""
    client_configuration = client.Configuration()
    config.load_kube_config(
        config_file=config_path,
        context=context,
        client_configuration=client_configuration,
        persist_config=False,
    )
    api_server = client_configuration.host
    if ":" not in api_server.lstrip("https://"):
        api_server = f"{api_server}:{DEFAULT_PORT}"
    return api_server


def get_k8s_namespace(config_path: str, context: str) -> str:
    """Get the corresponding K8s namespace for the given context."""
    with open(config_path, "r") as f:
        config_data = yaml.safe_load(f)
    for ctx in config_data["contexts"]:
        if ctx["name"] == context:
            return ctx["context"]["namespace"]
    return "default"

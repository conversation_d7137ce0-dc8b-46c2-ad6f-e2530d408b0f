# It is important to note that Spark is opinionated about certain pod configurations
# so there are values in the pod template that will always be overwritten by Spark.
apiVersion: v1
kind: Pod
metadata:
  name: spark-executor-pod # controlled by spark.kubernetes.executor.podNamePrefix
  namespace: # overwritten by spark.kubernetes.namespace
  labels: # add additional labels from spark.kubernetes.executor.label.*
    app: spark
    function: spark-executor
spec:
  enableServiceLinks: false
  initContainers:
    - name: prepare-deps
      image: # filled in at runtime
      command: # filled in at runtime
      resources:
        requests:
          cpu: "2"
          memory: "4Gi"
      volumeMounts: # updated at runtime
      - mountPath: /mnt/ephemeral/ram
        name: local-ramdisk
  containers:
    - name: spark-executor
      image: # overwritten by spark.kubernetes.executor.container.image
      command:
        - /usr/local/bin/init.sh
        - --spark
        - --
      imagePullPolicy: Always # overwritten by spark.kubernetes.container.image.pullPolicy
      env: # add additional env from spark.executorEnv.[EnvironmentVariableName]
      resources: # mainly controlled by spark configs
        limits:
          ephemeral-storage:
        requests:
          ephemeral-storage:
      volumeMounts: # updated at runtime
      - mountPath: /mnt/ephemeral/ram
        name: local-ramdisk
      securityContext:
        runAsUser: 0
  volumes: # updated at runtime
  - name: local-ramdisk
    emptyDir:
      medium: Memory
      sizeLimit: 500Mi
  affinity: # overwritten at runtime

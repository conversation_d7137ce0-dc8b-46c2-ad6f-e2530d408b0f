# It is important to note that Spark is opinionated about certain pod configurations
# so there are values in the pod template that will always be overwritten by Spark.
apiVersion: v1
kind: Pod
metadata:
  name: spark-driver-pod # overwritten by spark.kubernetes.driver.pod.name
  namespace: # overwritten by spark.kubernetes.namespace
  labels: # add additional labels from spark.kubernetes.driver.label.*
    app: spark
    function: spark-driver
spec:
  containers:
    - name: spark-driver
      image: # overwritten by spark.kubernetes.driver.container.image
      imagePullPolicy: Always # overwritten by spark.kubernetes.container.image.pullPolicy
      env: # add additional env from spark.kubernetes.driverEnv.[EnvironmentVariableName]
      resources: # mainly controlled by spark configs
        limits:
          ephemeral-storage:
        requests:
          ephemeral-storage:
      volumeMounts: # updated at runtime
      - mountPath: /mnt/ephemeral/ram
        name: local-ramdisk
  volumes: # updated at runtime
  - name: local-ramdisk
    emptyDir:
      medium: Memory
      sizeLimit: 500Mi
  affinity: # overwritten at runtime

"""Utilities for creating pod templates."""

import logging
import tempfile
import uuid
from pathlib import Path
from subprocess import check_output
from typing import Any, Mapping, Optional, Sequence, Union

import yaml

from research.data.spark.infra import default_configs
from research.data.spark.infra.copy_deps import PREPARE_DEPS_SCRIPT
from research.environments import get_provider

logger = logging.getLogger(__name__)


# Path to the local dependency folder in the pod
LOCAL_DEP_BASE_PATH = "/mnt/ephemeral/disk/deps"

# Name of the init container that prepares dependencies
# This should match the name in the pod templates
PREPARE_DEPS_CONTAINER = "prepare-deps"

# Buffer size (in GB) for ephemeral storage
EPHEMERAL_STORAGE_BUFFER = 2


def get_volume_defs(volumes: Mapping[str, str]) -> Sequence[Mapping[str, Any]]:
    """Get the volume definitions for the pod template."""
    volume_defs = []
    for name in volumes.keys():
        volume_defs.append(
            {
                "name": name,
                "persistentVolumeClaim": {
                    "claimName": name,
                },
            }
        )
    return volume_defs


def get_volume_mnts(volumes: Mapping[str, str]) -> Sequence[Mapping[str, str]]:
    """Get the volume mounts for the pod template."""
    volume_mnts = []
    for name, path in volumes.items():
        volume_mnts.append(
            {
                "name": name,
                "mountPath": path,
            }
        )
    return volume_mnts


def get_username() -> str | None:
    """Get the username of the current user."""
    try:
        return check_output(["augi", "whoami"]).decode("utf-8").strip()
    except:  # noqa: E722
        return None


def get_labels(gpu_type: str | Sequence[str] = "") -> Mapping[str, str]:
    """Get the labels for the pod template."""
    user = get_username()
    if not isinstance(gpu_type, str):
        gpu_type = ",".join(gpu_type)
    labels = {
        "aug.user": user or "unknown",
        "aug.gpu_type": gpu_type,
        "aug.type": "spark-executor",
    }
    return labels


def create_template(
    source: Path,
    image: str,
    shared_folder: Optional[Path] = None,
    region: Union[str, Sequence[str]] = "",
    gpu_type: Union[str, Sequence[str]] = "",
    gpu_count: int = 1,
    ephemeral_storage_gb: int = 32,
    pod_anti_affinity: str | None = None,
    path: Optional[Path] = None,
    use_highmem_pool: bool = True,
) -> Path:
    """Create a pod template based on supplied specs.

    The node affinity part will be filled out by this function and gpus will be added to pod
    resource requirements if they are requested.
    All other parts of the original YAML file will be kept

    Args:
        source:  The source YAML config
        image:  Image to use for the pod
        shared_folder:  Path to the shared folder that can be accessed by the pod
        region:  Region of the pod
        gpu_type:  Type of GPU to use.  `A40`, `RTX_A5000`, `A100_PCIE_40GB` etc
        gpu_count:  Number of GPUs per pod
        ephemeral_storage_gb:  Size of the ephemeral storage in GB.  This is the local storage
            that is mounted to each worker.  It is not shared with other workers.
        pod_anti_affinity:  Whether to add pod anti-affinity to the pod template.
            Allowed values are "required" and "preferred".
            If "required", it will prevent multiple pods from being scheduled on the same node.
            If "preferred", it will try to schedule pods on different nodes.
            If None, no pod anti-affinity will be added.
        path:  Path where new YAML will be stored.  Default is in the temp folder.
        use_highmem_pool:  Whether to use the highmem pool.  This is only valid for cpu only
            jobs on GCP.

    Returns:
        Path object pointing to the new template YAML file.
    """
    if not path:
        path = Path(tempfile.mkdtemp())
    with source.open() as f:
        doc = yaml.safe_load(f)

    doc["metadata"]["labels"].update(get_labels(gpu_type))

    for init_container in doc["spec"]["initContainers"]:
        init_container["image"] = image

    if shared_folder:
        # Update command to prepare dependencies from the shared folder
        script = Path(PREPARE_DEPS_SCRIPT).name
        prepare_deps_command = [
            "/bin/bash",
            "-c",
            f"mkdir {LOCAL_DEP_BASE_PATH} && "
            + f"cp -r {shared_folder}/* {LOCAL_DEP_BASE_PATH} && "
            + f"python3 {LOCAL_DEP_BASE_PATH}/{script} --base_path {LOCAL_DEP_BASE_PATH}",
        ]

        for init_container in doc["spec"]["initContainers"]:
            if init_container["name"] == PREPARE_DEPS_CONTAINER:
                init_container["command"] = prepare_deps_command
                break

    # The node affinity part of the spec will be filled out below
    node_affinity_exps = []

    need_gpu = gpu_type and gpu_count
    if isinstance(gpu_type, str):
        gpu_type = [gpu_type]

    if default_configs.ON_COREWEAVE:
        if region:
            if isinstance(region, str):
                region = [region]
            region_clause = {
                "key": "topology.kubernetes.io/region",
                "operator": "In",
                "values": [r.upper() for r in region],
            }
            node_affinity_exps.append(region_clause)

        if need_gpu and any("H100" in g or "A100" in g for g in gpu_type):
            reserved_clause = {
                "key": "node.coreweave.cloud/reserved",
                "operator": "In",
                "values": ["augment"],
            }
            node_affinity_exps.append(reserved_clause)

    if (default_configs.ON_COREWEAVE or need_gpu) and use_highmem_pool:
        use_highmem_pool = False
        logger.warning("use_highmem_pool is only valid for GCP cpu jobs.  Ignoring.")

    if use_highmem_pool:
        highmem_affinity = {
            "key": "r.augmentcode.com/pool-type",
            "operator": "In",
            "values": ["spark-highmem"],
        }
        node_affinity_exps.append(highmem_affinity)
        doc["spec"].setdefault("tolerations", []).append(
            {
                "effect": "NoSchedule",
                "key": "r.augmentcode.com/pool-type",
                "value": "spark-highmem",
                "operator": "Equal",
            }
        )

    if need_gpu:
        gpu_selector_label = (
            "gpu.nvidia.com/class"
            if default_configs.ON_COREWEAVE
            else "cloud.google.com/gke-accelerator"
        )
        processor_clause = {
            "key": gpu_selector_label,
            "operator": "In",
            "values": gpu_type,
        }
    else:
        if default_configs.ON_COREWEAVE:
            processor_clause = {
                "key": "node.coreweave.cloud/cpu",
                "operator": "In",
                "values": default_configs.PREFERRED_CW_CPU_TYPES,
            }
        else:
            processor_clause = {}
            logger.info("No CPU selector will be added for GCP.")
    if processor_clause:
        node_affinity_exps.append(processor_clause)

    if node_affinity_exps:
        doc["spec"]["affinity"] = {
            "nodeAffinity": {
                "requiredDuringSchedulingIgnoredDuringExecution": {
                    "nodeSelectorTerms": [{"matchExpressions": node_affinity_exps}]
                }
            }
        }
    else:
        doc["spec"]["affinity"] = {}

    if pod_anti_affinity:
        label_selector = {"matchLabels": {"spark-role": "executor"}}

        required = []
        preferred = [
            # always prefer executors to be scheduled on different regions/zones
            {
                "weight": 40,
                "podAffinityTerm": {
                    "topologyKey": "topology.kubernetes.io/region",
                    "labelSelector": label_selector,
                },
            },
            {
                "weight": 20,
                "podAffinityTerm": {
                    "topologyKey": "topology.kubernetes.io/zone",
                    "labelSelector": label_selector,
                },
            },
        ]

        if pod_anti_affinity == "required":
            required.append(
                {
                    "topologyKey": "kubernetes.io/hostname",
                    "labelSelector": label_selector,
                }
            )
        elif pod_anti_affinity == "preferred":
            preferred.append(
                {
                    "weight": 100,
                    "podAffinityTerm": {
                        "topologyKey": "kubernetes.io/hostname",
                        "labelSelector": label_selector,
                    },
                }
            )
        else:
            raise ValueError(
                f"pod_anti_affinity must be 'required' or 'preferred', got {pod_anti_affinity}"
            )

        doc["spec"]["affinity"]["podAntiAffinity"] = {
            "preferredDuringSchedulingIgnoredDuringExecution": preferred
        }
        if required:
            # avoid adding empty list to the template
            doc["spec"]["affinity"]["podAntiAffinity"][
                "requiredDuringSchedulingIgnoredDuringExecution"
            ] = required

    if len(doc["spec"]["containers"]) != 1:
        logger.warning("Expected one executor container in the pod template.")
    executor = doc["spec"]["containers"][0]
    container_resources = executor["resources"]

    # Add gpu resource requirements for pods if specified
    if need_gpu:
        container_resources["limits"]["nvidia.com/gpu"] = gpu_count
        container_resources["requests"]["nvidia.com/gpu"] = gpu_count

    # Update ephemeral storage
    container_resources["limits"]["ephemeral-storage"] = f"{ephemeral_storage_gb}Gi"
    container_resources["requests"]["ephemeral-storage"] = f"{ephemeral_storage_gb}Gi"

    ephemeral_storage_mnt = {
        "name": "local-drive",
        "mountPath": "/mnt/ephemeral/disk",
    }

    disk_size = ephemeral_storage_gb - EPHEMERAL_STORAGE_BUFFER
    if disk_size <= 0:
        logger.error("Requested ephemeral storage is smaller than the buffer size 2GB.")
    ephemeral_storage_def = {
        "name": "local-drive",
        "emptyDir": {
            "sizeLimit": f"{disk_size}Gi",
        },
    }

    if default_configs.ON_COREWEAVE:
        # Update volume definitions and mounts
        volumes = default_configs.VOLUMES[default_configs._cluster.name]

        mounts = list(get_volume_mnts(volumes)) + [ephemeral_storage_mnt]
        definitions = list(get_volume_defs(volumes)) + [ephemeral_storage_def]
    else:
        annotations = doc["metadata"].setdefault("annotations", {})
        annotations.update(
            {
                "gke-gcsfuse/volumes": "true",
                "gke-gcsfuse/cpu-limit": "1",
                "gke-gcsfuse/memory-limit": "2Gi",
                "gke-gcsfuse/ephemeral-storage-limit": "32Gi",
                "gke-gcsfuse/cpu-request": "1",
                "gke-gcsfuse/memory-request": "2Gi",
                "gke-gcsfuse/ephemeral-storage-request": "32Gi",
            }
        )

        # On GCP we use the definitions provided in the provider configs
        definitions, mounts = get_provider().get_volumes_and_mounts(
            include_tags=["spark"]
        )
        mounts += [ephemeral_storage_mnt]
        definitions += [ephemeral_storage_def]

    for init_container in doc["spec"]["initContainers"]:
        init_container["volumeMounts"] += mounts

    executor["volumeMounts"] += mounts
    doc["spec"]["volumes"] += definitions

    output = path / f"spark-template-{uuid.uuid4()}.yaml"
    with output.open("w") as f:
        yaml.safe_dump(doc, f)
    return output

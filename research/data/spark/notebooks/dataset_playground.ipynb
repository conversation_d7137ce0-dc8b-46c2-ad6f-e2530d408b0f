{"cells": [{"cell_type": "markdown", "id": "0455e0bb-6ce4-4eee-ad6b-704b661707cf", "metadata": {}, "source": ["## Pyspark dataset loading examples\n", "This notebook provides some examples on how to load datasets in pyspark and also example schemas and sample data. \n", "\n", "Note that contents of this file is generated on clusters that preconfigured access for S3 with the following `spark-defaults.conf` default configurations (skipping the memory and size settings): \n", "```conf\n", "spark.master                             spark://<master_node_name>:7077\n", "spark.hadoop.fs.s3a.endpoint             https://object.lga1.coreweave.com\n", "spark.hadoop.fs.s3a.access.key           <CW S3 access key>\n", "spark.hadoop.fs.s3a.secret.key           <CW S3 secret key>\n", "spark.hadoop.fs.s3a.path.style.access    true\n", "spark.hadoop.fs.s3a.impl                 org.apache.hadoop.fs.s3a.S3AFileSystem\n", "spark.jars                               /mnt/efs/augment-lga1-nvme/lib/jars/aws-java-sdk-bundle-1.12.471.jar,/mnt/efs/augment-lga1-nvme/lib/jars/hadoop-aws-3.3.4.jar\n", "spark.driver.extraLibraryPath            /mnt/efs/augment-lga1-nvme/lib/hadoop-3.3.4/lib/native/\n", "spark.executor.extraLibraryPath          /mnt/efs/augment-lga1-nvme/lib/hadoop-3.3.4/lib/native/\n", "```\n", "\n", "If you are running on a cluster without these defaults, you may need to change the creation of your Spark session to incorporate or adjust them, or set them in config argument when doing spark-submit.\n", "\n", "In the future, we plan to create a Hive Metastore to make them behave more like a warehouse. That would enable schema and data locations can be stored and directly queried in Spark and to enable directly accessing the data through SQL.  However, this is relatively lower priority since the number of data sources and people that use them are both relatively small for the moment.\n", "\n", "### Note\n", "\n", "**For CoreWeave S3 to work on pyspark the protocol name in the URI has to be `s3a://`**.  Spark use the protocol name to determine the underlying connector, and `s3://` correspond to the legacy s3 protocol which is no longer supported in recent versions of Hadoop.  Even though CoreWeave uses `s3://` in the `s3cmd` URIs, and no documentation is available about protocol, we found that it in fact implements the newer extended s3 (s3a) under the hood.  Generally we find that configurations that works for MinIO also works for CoreWeave."]}, {"cell_type": "code", "execution_count": 4, "id": "6789ea61-a025-4ec2-b31e-5f533db0121a", "metadata": {"execution": {"iopub.execute_input": "2023-05-24T23:03:18.901953Z", "iopub.status.busy": "2023-05-24T23:03:18.900105Z", "iopub.status.idle": "2023-05-24T23:03:19.322606Z", "shell.execute_reply": "2023-05-24T23:03:19.320157Z", "shell.execute_reply.started": "2023-05-24T23:03:18.901853Z"}, "tags": []}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "import pyspark.sql.functions as sp\n", "\n", "# Settings here are just for nicer displays\n", "spark = SparkSession.builder.appName(\"schema_investigation\").getOrCreate()"]}, {"cell_type": "markdown", "id": "c909e6d5-53fa-4b4d-9001-ecbea312fc26", "metadata": {}, "source": ["### Red Pajama dataset\n", "The Red Pajama dataset contains the following components (directly lifted from their website):\n", "\n", "\n", "| Dataset       | Token Count |\n", "|---------------|-------------|\n", "| Commoncrawl   | 878 Billion        |\n", "| C4            | 175 Billion        |\n", "| GitHub        | 59 Billion         |\n", "| Books         | 26 Billion         |\n", "| ArXiv         | 28 Billion         |\n", "| Wikipedia     | 24 Billion         |\n", "| StackExchange | 20 Billion         |\n", "| Total         | 1.2 Trillion      |\n", "\n", "The original data is in plain line delimited json.  Common crawl dataset is compressed with bst; several components (books, stachexchange, wikipedia) are one single file.  The raw data is located in **`s3a://redpajama/raw/`**.\n", "\n", "This is somewhat difficult for query, so we have converted each dataset to partitioned parquet files and those are located in **`s3a://redpajama/warehouse/`** folder."]}, {"cell_type": "code", "execution_count": 2, "id": "a32c0b17-541c-4483-86de-0c8b1d51ec21", "metadata": {}, "outputs": [], "source": ["from pyspark.sql.types import StructType\n", "\n", "# Many data files have this `meta` field which is a nested object.  This function will unnest them for better views of the schema\n", "def flatten_struct(schema, prefix=\"\"):\n", "    \"\"\"Unnest the schema of a nested struct column\"\"\"\n", "    result = []\n", "    for elem in schema:\n", "        if isinstance(elem.dataType, StructType):\n", "            result += flatten_struct(elem.dataType, prefix + elem.name + \".\")\n", "        else:\n", "            result.append(sp.col(prefix + elem.name).alias(prefix + elem.name))\n", "    return result\n", "\n", "def unnest_struct(df):\n", "    \"\"\"Unnest struct columns of a dataframe\"\"\"\n", "    new_schema = flatten_struct(df.schema)\n", "    return df.select(new_schema)"]}, {"cell_type": "code", "execution_count": 3, "id": "6c2d3dfc-8e26-4938-804e-d0cfe9d14c6c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/05/22 19:13:34 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Component  github\n", "Schema:  struct<meta:struct<alnum_prop:double,avg_line_length:double,binary:boolean,content_hash:string,copies:string,id:string,language:array<struct<bytes:string,name:string>>,license:string,line_count:bigint,max_line_length:bigint,mode:string,path:string,ref:string,repo_name:string,size:string,source:string,symlink_target:string,timestamp:string>,text:string>\n", "Sample: \n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>meta.alnum_prop</th><th>meta.avg_line_length</th><th>meta.binary</th><th>meta.content_hash</th><th>meta.copies</th><th>meta.id</th><th>meta.language</th><th>meta.license</th><th>meta.line_count</th><th>meta.max_line_length</th><th>meta.mode</th><th>meta.path</th><th>meta.ref</th><th>meta.repo_name</th><th>meta.size</th><th>meta.source</th><th>meta.symlink_target</th><th>meta.timestamp</th><th>text</th></tr>\n", "<tr><td>0.5575096728807598</td><td>23.39917695473251</td><td>false</td><td>81e3cddf43a381727a6059ace43c436e</td><td>6</td><td>5b499ec63a6213ab22f358ae3f38f40944721f09</td><td>[{38946, CSS}, {794, HTML}, {124711, JavaScript}, {6486, TypeScript}]</td><td>mit</td><td>243</td><td>140</td><td>33188</td><td>webpack.config.js</td><td>refs/heads/master</td><td>galtalmor/ng2-calculator</td><td>5686</td><td>github</td><td>null</td><td></td><td>// @AngularClass\\n\\n// Helper\\nvar sliceArgs = Function.prototype.call.bind(Array.prototype.slice...</td></tr>\n", "<tr><td>0.7620673599255006</td><td>47.72592592592593</td><td>false</td><td>e9e989607a7797e25ea33d1a671b5de3</td><td>28</td><td>dad12f62adc67d77d2c7d46dc703e57270842135</td><td>[{978, C}, {28461919, Go}, {1193990, HTML}, {63843, <PERSON>file}, {1013, Nginx}, {267014, Protocol B...</td><td>apache-2.0</td><td>135</td><td>164</td><td>33188</td><td>docs/proposals/custom-metrics.md</td><td>refs/heads/master</td><td>anguslees/kubernetes</td><td>6447</td><td>github</td><td>null</td><td></td><td>&lt;!-- BEGIN MUNGE: UNVERSIONED_WARNING --&gt;\\n\\n&lt;!-- BEGIN STRIP_FOR_RELEASE --&gt;\\n\\n&lt;img src=&quot;http:/...</td></tr>\n", "<tr><td>0.7004538577912254</td><td>36.054545454545455</td><td>false</td><td>0e2d0002759678b401fd85fdd7a2d7c5</td><td>1</td><td>e20b698a9d847010694567cae2b5a3e931ddf1cb</td><td>[{23183, Java}]</td><td>apache-2.0</td><td>55</td><td>122</td><td>33188</td><td>addressbook-web-tests/src/test/java/ru/arkuz/addressbook/tests/ContactModificationTests.java</td><td>refs/heads/master</td><td>arkuz/java_for_testers</td><td>1983</td><td>github</td><td>null</td><td></td><td>package ru.arkuz.addressbook.tests;\\n\\nimport org.testng.Assert;\\nimport org.testng.annotations.B...</td></tr>\n", "<tr><td>0.6114427860696517</td><td>30.0</td><td>false</td><td>27e74b6ca12f05723398249b0a405429</td><td>682</td><td>8536862e3d5c848fab78cb88be6cfd725aff6377</td><td>[]</td><td>mit</td><td>1407</td><td>148</td><td>33188</td><td>ajax/libs/fullPage.js/1.7/jquery.fullPage.js</td><td>refs/heads/master</td><td>kiwi89/cdnjs</td><td>42372</td><td>github</td><td>null</td><td></td><td>\\r\\n\\r\\n(function($) {\\r\\n\\t$.fn.fullpage = function(options) {\\r\\n\\t\\t// Create some defaults, e...</td></tr>\n", "<tr><td>0.6210526315789474</td><td>31.666666666666668</td><td>false</td><td>31dc6ad0bb1fc372046a9ead1fc78274</td><td>5</td><td>f6d59a88f5572800cfb3efc20909de865046c8dd</td><td>[{3630, Dockerfile}, {175, JavaScript}, {2855, PowerShell}, {47, Shell}, {90205277, TypeScript}]</td><td>apache-2.0</td><td>6</td><td>84</td><td>33188</td><td>tests/cases/fourslash/codeFixChangeJSDocSyntax15.ts</td><td>refs/heads/master</td><td>minestarks/TypeScript</td><td>348</td><td>github</td><td>null</td><td></td><td>verify.codeFix({\\n    description: &quot;Change &#x27;function(number?): number&#x27; to &#x27;(arg0: number) =&gt; numb...</td></tr>\n", "<tr><td>0.6880841121495327</td><td>29.517241379310345</td><td>false</td><td>0954c51bd12a277be6149856e67c05b7</td><td>62</td><td>056e84b4cb999df0d9f2f7fbf60d543957158a4b</td><td>[{6973, AppleScript}, {41371, Assembly}, {14261, Batchfile}, {4207390, C}, {*********, C++}, {777...</td><td>bsd-3-clause</td><td>116</td><td>78</td><td>33188</td><td>win8/metro_driver/direct3d_helper.cc</td><td>refs/heads/android-4.4</td><td>Perferom/android_external_chromium_org</td><td>3767</td><td>github</td><td>null</td><td></td><td>namespace {\\n\\nvoid CheckIfFailed(HRESULT hr) {\\n  DCHECK(!FAILED(hr));\\n  if (FAILED(hr))\\n    D...</td></tr>\n", "<tr><td>0.7282656663724625</td><td>38.244725738396625</td><td>false</td><td>38088f570d69bf58632ebfd65bd9e217</td><td>1</td><td>d1a7b6d704dcc595e1fb55a4351d36c41608d5f0</td><td>[{579, Assembly}, {2093, Batchfile}, {255521, C}, {237, C#}, {10992, C++}, {54863, CSS}, {1017, D...</td><td>apache-2.0</td><td>237</td><td>100</td><td>33188</td><td>src/com/facebook/buck/cxx/toolchain/linker/Linker.java</td><td>refs/heads/dev</td><td><PERSON><PERSON><PERSON><PERSON>/buck</td><td>9680</td><td>github</td><td>null</td><td></td><td>\\n\\npackage com.facebook.buck.cxx.toolchain.linker;\\n\\nimport com.facebook.buck.core.filesystems....</td></tr>\n", "<tr><td>0.6820809248554913</td><td>23.066666666666666</td><td>false</td><td>4d5edcfafc349d16255487f2922e2273</td><td>110</td><td>e07204f1dc10ae6066596cd21b74ff9eef3e8001</td><td>[{529, <PERSON>ch<PERSON><PERSON>}, {2120, CSS}, {60923, CoffeeScript}, {13436, HTML}, {234304, JavaScript}, {2081...</td><td>mit</td><td>15</td><td>67</td><td>33188</td><td>example/mocha-zombie/node_modules/zombie/node_modules/jsdom/test/level2/html/files/menu.html</td><td>refs/heads/master</td><td>dmohl/expectThat</td><td>346</td><td>github</td><td>null</td><td></td><td>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.01 Transitional//EN&quot;&gt;\\n&lt;HTML&gt;\\n&lt;HEAD&gt;\\n&lt;META HTTP-EQUIV...</td></tr>\n", "<tr><td>0.6296296296296297</td><td>33.13636363636363</td><td>false</td><td>6911ac64fc4b04dfae920d0c17e91d7f</td><td>1</td><td>c49d8a65d9e240a0428e71df2746bf34b6f8322f</td><td>[{81112, Java}]</td><td>apache-2.0</td><td>22</td><td>64</td><td>33188</td><td>Demo_toolbar/Ch5_ToolbarSample/app/src/main/res/menu/menu.xml</td><td>refs/heads/master</td><td>pierreduchemin/poec-android-03-2017</td><td>729</td><td>github</td><td>null</td><td></td><td>&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-8&quot;?&gt;\\n&lt;menu xmlns:android=&quot;http://schemas.android.com/apk/res/a...</td></tr>\n", "<tr><td>0.6691616766467066</td><td>24.29090909090909</td><td>false</td><td>e6c99d2508edc732cc21d22936a50d73</td><td>1</td><td>7f3318eaa63c9e89a20671dc0e09d63116a66a53</td><td>[{1238141, Scala}]</td><td>apache-2.0</td><td>55</td><td>101</td><td>33188</td><td>src/main/scala/inox/solvers/smtlib/SMTLIBDebugger.scala</td><td>refs/heads/master</td><td>romac/inox</td><td>1376</td><td>github</td><td>null</td><td></td><td>\\n\\npackage inox\\npackage solvers\\npackage smtlib\\n\\nimport utils._\\nimport _root_.smtlib.trees.T...</td></tr>\n", "</table>\n"], "text/plain": ["+------------------+--------------------+-----------+--------------------------------+-----------+----------------------------------------+----------------------------------------------------------------------------------------------------+------------+---------------+--------------------+---------+--------------------------------------------------------------------------------------------+----------------------+--------------------------------------+---------+-----------+-------------------+--------------+----------------------------------------------------------------------------------------------------+\n", "|   meta.alnum_prop|meta.avg_line_length|meta.binary|               meta.content_hash|meta.copies|                                 meta.id|                                                                                       meta.language|meta.license|meta.line_count|meta.max_line_length|meta.mode|                                                                                   meta.path|              meta.ref|                        meta.repo_name|meta.size|meta.source|meta.symlink_target|meta.timestamp|                                                                                                text|\n", "+------------------+--------------------+-----------+--------------------------------+-----------+----------------------------------------+----------------------------------------------------------------------------------------------------+------------+---------------+--------------------+---------+--------------------------------------------------------------------------------------------+----------------------+--------------------------------------+---------+-----------+-------------------+--------------+----------------------------------------------------------------------------------------------------+\n", "|0.5575096728807598|   23.39917695473251|      false|81e3cddf43a381727a6059ace43c436e|          6|5b499ec63a6213ab22f358ae3f38f40944721f09|                               [{38946, CSS}, {794, HTML}, {124711, JavaScript}, {6486, TypeScript}]|         mit|            243|                 140|    33188|                                                                           webpack.config.js|     refs/heads/master|              galtalmor/ng2-calculator|     5686|     github|               null|              |// @AngularClass\\n\\n// Helper\\nvar sliceArgs = Function.prototype.call.bind(Array.prototype.slice...|\n", "|0.7620673599255006|   47.72592592592593|      false|e9e989607a7797e25ea33d1a671b5de3|         28|dad12f62adc67d77d2c7d46dc703e57270842135|[{978, C}, {28461919, Go}, {1193990, HTML}, {63843, <PERSON><PERSON><PERSON>}, {1013, Nginx}, {267014, Protocol B...|  apache-2.0|            135|                 164|    33188|                                                            docs/proposals/custom-metrics.md|     refs/heads/master|                  anguslees/kubernetes|     6447|     github|               null|              |<!-- BEGIN MUNGE: UNVERSIONED_WARNING -->\\n\\n<!-- BEGIN STRIP_FOR_RELEASE -->\\n\\n<img src=\"http:/...|\n", "|0.7004538577912254|  36.054545454545455|      false|0e2d0002759678b401fd85fdd7a2d7c5|          1|e20b698a9d847010694567cae2b5a3e931ddf1cb|                                                                                     [{23183, Java}]|  apache-2.0|             55|                 122|    33188|addressbook-web-tests/src/test/java/ru/arkuz/addressbook/tests/ContactModificationTests.java|     refs/heads/master|                arkuz/java_for_testers|     1983|     github|               null|              |package ru.arkuz.addressbook.tests;\\n\\nimport org.testng.Assert;\\nimport org.testng.annotations.B...|\n", "|0.6114427860696517|                30.0|      false|27e74b6ca12f05723398249b0a405429|        682|8536862e3d5c848fab78cb88be6cfd725aff6377|                                                                                                  []|         mit|           1407|                 148|    33188|                                                ajax/libs/fullPage.js/1.7/jquery.fullPage.js|     refs/heads/master|                          kiwi89/cdnjs|    42372|     github|               null|              |\\r\\n\\r\\n(function($) {\\r\\n\\t$.fn.fullpage = function(options) {\\r\\n\\t\\t// Create some defaults, e...|\n", "|0.6210526315789474|  31.666666666666668|      false|31dc6ad0bb1fc372046a9ead1fc78274|          5|f6d59a88f5572800cfb3efc20909de865046c8dd|    [{3630, Dockerfile}, {175, JavaScript}, {2855, PowerShell}, {47, Shell}, {90205277, TypeScript}]|  apache-2.0|              6|                  84|    33188|                                         tests/cases/fourslash/codeFixChangeJSDocSyntax15.ts|     refs/heads/master|                 minestarks/TypeScript|      348|     github|               null|              |verify.codeFix({\\n    description: \"Change 'function(number?): number' to '(arg0: number) => numb...|\n", "|0.6880841121495327|  29.517241379310345|      false|0954c51bd12a277be6149856e67c05b7|         62|056e84b4cb999df0d9f2f7fbf60d543957158a4b|[{6973, Apple<PERSON>}, {41371, Assembly}, {14261, <PERSON><PERSON><PERSON><PERSON>}, {4207390, C}, {*********, C++}, {777...|bsd-3-clause|            116|                  78|    33188|                                                        win8/metro_driver/direct3d_helper.cc|refs/heads/android-4.4|Perferom/android_external_chromium_org|     3767|     github|               null|              |namespace {\\n\\nvoid CheckIfFailed(HRESULT hr) {\\n  DCHECK(!FAILED(hr));\\n  if (FAILED(hr))\\n    D...|\n", "|0.7282656663724625|  38.244725738396625|      false|38088f570d69bf58632ebfd65bd9e217|          1|d1a7b6d704dcc595e1fb55a4351d36c41608d5f0|[{579, Assembly}, {2093, <PERSON><PERSON><PERSON><PERSON>}, {255521, C}, {237, C#}, {10992, C++}, {54863, CSS}, {1017, D...|  apache-2.0|            237|                 100|    33188|                                      src/com/facebook/buck/cxx/toolchain/linker/Linker.java|        refs/heads/dev|                       <PERSON><PERSON><PERSON><PERSON>/buck|     9680|     github|               null|              |\\n\\npackage com.facebook.buck.cxx.toolchain.linker;\\n\\nimport com.facebook.buck.core.filesystems....|\n", "|0.6820809248554913|  23.066666666666666|      false|4d5edcfafc349d16255487f2922e2273|        110|e07204f1dc10ae6066596cd21b74ff9eef3e8001|[{529, <PERSON>ch<PERSON><PERSON>}, {2120, CSS}, {60923, CoffeeScript}, {13436, HTML}, {234304, JavaScript}, {2081...|         mit|             15|                  67|    33188|example/mocha-zombie/node_modules/zombie/node_modules/jsdom/test/level2/html/files/menu.html|     refs/heads/master|                      dmohl/expectThat|      346|     github|               null|              |<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\">\\n<HTML>\\n<HEAD>\\n<META HTTP-EQUIV...|\n", "|0.6296296296296297|   33.13636363636363|      false|6911ac64fc4b04dfae920d0c17e91d7f|          1|c49d8a65d9e240a0428e71df2746bf34b6f8322f|                                                                                     [{81112, Java}]|  apache-2.0|             22|                  64|    33188|                               Demo_toolbar/Ch5_ToolbarSample/app/src/main/res/menu/menu.xml|     refs/heads/master|   pierreduchemin/poec-android-03-2017|      729|     github|               null|              |<?xml version=\"1.0\" encoding=\"utf-8\"?>\\n<menu xmlns:android=\"http://schemas.android.com/apk/res/a...|\n", "|0.6691616766467066|   24.29090909090909|      false|e6c99d2508edc732cc21d22936a50d73|          1|7f3318eaa63c9e89a20671dc0e09d63116a66a53|                                                                                  [{1238141, <PERSON><PERSON>}]|  apache-2.0|             55|                 101|    33188|                                     src/main/scala/inox/solvers/smtlib/SMTLIBDebugger.scala|     refs/heads/master|                            romac/inox|     1376|     github|               null|              |\\n\\npackage inox\\npackage solvers\\npackage smtlib\\n\\nimport utils._\\nimport _root_.smtlib.trees.T...|\n", "+------------------+--------------------+-----------+--------------------------------+-----------+----------------------------------------+----------------------------------------------------------------------------------------------------+------------+---------------+--------------------+---------+--------------------------------------------------------------------------------------------+----------------------+--------------------------------------+---------+-----------+-------------------+--------------+----------------------------------------------------------------------------------------------------+"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Component  c4\n", "Schema:  struct<meta:struct<language:string,source:string,timestamp:string,url:string>,text:string>\n", "Sample: \n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>meta.language</th><th>meta.source</th><th>meta.timestamp</th><th>meta.url</th><th>text</th></tr>\n", "<tr><td>en</td><td>c4</td><td>2019-04-20T04:12:31Z</td><td>https://chelseafancast.com/2019/01/the-majority/</td><td>In his latest article for the Fancast, @<PERSON><PERSON><PERSON><PERSON> discusses the Chelsea Together initiative.\\...</td></tr>\n", "<tr><td>en</td><td>c4</td><td>2019-04-21T11:16:30Z</td><td>https://www.elpasochiropractorblog.com/2018/05/chiropractor-cerebral-palsy-specialists.html</td><td>The Greek word &quot;chiropractic&quot; means &quot;hand practice&quot; or therapy done by hand. Chiropractic care is...</td></tr>\n", "<tr><td>en</td><td>c4</td><td>2019-04-20T23:09:53Z</td><td>http://www.unclebeanz.com/product/KAAPB.html</td><td>We are proud to offer my personal favorite SL28 varietal in a powerful peaberry format. This an e...</td></tr>\n", "<tr><td>en</td><td>c4</td><td>2019-04-25T20:46:17Z</td><td>https://www.boulderdowntown.com/do/holiday-cookies2</td><td>Come join us for this family class! Food Lab will have stations for you to decorate you cookies, ...</td></tr>\n", "<tr><td>en</td><td>c4</td><td>2019-04-21T15:17:45Z</td><td>https://mightymarksblog.com/tag/teas-reduced-fee/</td><td>When applying for a use-based federal trademark online with the United States Patent and Trademar...</td></tr>\n", "<tr><td>en</td><td>c4</td><td>2019-04-26T03:47:52Z</td><td>http://985thejewel.com/backpacks-homeless-2018/</td><td>The annual Backpacks For The Homeless has returned for another year, filling 2000 backpacks with ...</td></tr>\n", "<tr><td>en</td><td>c4</td><td>2019-04-23T00:13:27Z</td><td>https://www.solutiontree.com/blog/student-and-staff-empowerment/</td><td>Student empowerment is a popular topic. And it should be.\\nStudent empowerment is often represent...</td></tr>\n", "<tr><td>en</td><td>c4</td><td>2019-04-20T13:13:23Z</td><td>http://www.michigancatchandcook.com/</td><td>Catch &amp; Cook some salmon!\\nWould you like to have your services as a charter boat or restaurant p...</td></tr>\n", "<tr><td>en</td><td>c4</td><td>2019-04-24T16:21:46Z</td><td>https://www.lovecrochet.com/us/lion-brand-vanna-s-choice?country=US</td><td>Vanna&#x27;s Choice comes in a huge color selection which has been specifically designed to be the sam...</td></tr>\n", "<tr><td>en</td><td>c4</td><td>2019-04-25T07:59:41Z</td><td>http://www.worldof3dgames.com/game/the-murdered-butler</td><td><PERSON> called me today. Apparently, her butler was murdered. I better go there and investigate.</td></tr>\n", "</table>\n"], "text/plain": ["+-------------+-----------+--------------------+-------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------+\n", "|meta.language|meta.source|      meta.timestamp|                                                                                   meta.url|                                                                                                text|\n", "+-------------+-----------+--------------------+-------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------+\n", "|           en|         c4|2019-04-20T04:12:31Z|                                           https://chelseafancast.com/2019/01/the-majority/|In his latest article for the Fancast, @NickStroudley discusses the Chelsea Together initiative.\\...|\n", "|           en|         c4|2019-04-21T11:16:30Z|https://www.elpasochiropractorblog.com/2018/05/chiropractor-cerebral-palsy-specialists.html|The Greek word \"chiropractic\" means \"hand practice\" or therapy done by hand. Chiropractic care is...|\n", "|           en|         c4|2019-04-20T23:09:53Z|                                               http://www.unclebeanz.com/product/KAAPB.html|We are proud to offer my personal favorite SL28 varietal in a powerful peaberry format. This an e...|\n", "|           en|         c4|2019-04-25T20:46:17Z|                                        https://www.boulderdowntown.com/do/holiday-cookies2|Come join us for this family class! Food Lab will have stations for you to decorate you cookies, ...|\n", "|           en|         c4|2019-04-21T15:17:45Z|                                          https://mightymarksblog.com/tag/teas-reduced-fee/|When applying for a use-based federal trademark online with the United States Patent and Trademar...|\n", "|           en|         c4|2019-04-26T03:47:52Z|                                            http://985thejewel.com/backpacks-homeless-2018/|The annual Backpacks For The Homeless has returned for another year, filling 2000 backpacks with ...|\n", "|           en|         c4|2019-04-23T00:13:27Z|                           https://www.solutiontree.com/blog/student-and-staff-empowerment/|Student empowerment is a popular topic. And it should be.\\nStudent empowerment is often represent...|\n", "|           en|         c4|2019-04-20T13:13:23Z|                                                       http://www.michigancatchandcook.com/|Catch & Cook some salmon!\\nWould you like to have your services as a charter boat or restaurant p...|\n", "|           en|         c4|2019-04-24T16:21:46Z|                        https://www.lovecrochet.com/us/lion-brand-vanna-s-choice?country=US|<PERSON><PERSON>'s Choice comes in a huge color selection which has been specifically designed to be the sam...|\n", "|           en|         c4|2019-04-25T07:59:41Z|                                     http://www.worldof3dgames.com/game/the-murdered-butler|<PERSON> called me today. Apparently, her butler was murdered. I better go there and investigate.|\n", "+-------------+-----------+--------------------+-------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------+"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Component  arxiv\n", "Schema:  struct<meta:struct<arxiv_id:string,language:string,timestamp:string,url:string,yymm:string>,text:string>\n", "Sample: \n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>meta.arxiv_id</th><th>meta.language</th><th>meta.timestamp</th><th>meta.url</th><th>meta.yymm</th><th>text</th></tr>\n", "<tr><td>1703.09675</td><td>en</td><td>2017-03-29T02:10:36</td><td>https://arxiv.org/abs/1703.09675</td><td>1703</td><td>\\section{Introduction}\\n\\nThe strongest motivation for new physics beyond the Standard Model (SM)...</td></tr>\n", "<tr><td>1703.09617</td><td>en</td><td>2017-05-10T02:06:18</td><td>https://arxiv.org/abs/1703.09617</td><td>1703</td><td>\\section{Introduction}\\nThe \\nHo\\v{r}ava-Lifshitz gravity is motivated by a deformation of the us...</td></tr>\n", "<tr><td>1703.09572</td><td>en</td><td>2017-03-29T02:08:29</td><td>https://arxiv.org/abs/1703.09572</td><td>1703</td><td>\\section{Introduction}\\n\\n\\par X-ray binaries are systems in which a black hole or neutron star a...</td></tr>\n", "<tr><td>1703.09518</td><td>en</td><td>2017-03-29T02:07:29</td><td>https://arxiv.org/abs/1703.09518</td><td>1703</td><td>\\section{Introduction}\\n\\IEEEPARstart{T}{he} differential entropy of a given probability distribu...</td></tr>\n", "<tr><td>1703.09907</td><td>en</td><td>2017-03-30T02:04:07</td><td>https://arxiv.org/abs/1703.09907</td><td>1703</td><td>\\subsection{#1}\\vskip5pt}\\n\\def\\Case#1{\\vskip4pt\\par\\noindent{\\it Case: #1}}\\n\\def\\Cases#1{\\vskip...</td></tr>\n", "<tr><td>1703.09636</td><td>en</td><td>2017-09-25T02:05:58</td><td>https://arxiv.org/abs/1703.09636</td><td>1703</td><td>\\section{Introduction and Statement of Results}\\r\\n\\r\\nLet $L/K$ be a finite Galois extension of ...</td></tr>\n", "<tr><td>1703.09652</td><td>en</td><td>2017-07-19T02:07:36</td><td>https://arxiv.org/abs/1703.09652</td><td>1703</td><td>\\section{Introduction}\\label{sec:Intro}\\nLet $G$ be a finite group. We say that $G$ is \\emph{$d$-...</td></tr>\n", "<tr><td>1703.09698</td><td>en</td><td>2017-10-10T02:04:09</td><td>https://arxiv.org/abs/1703.09698</td><td>1703</td><td>\\section{Introduction} \\label{S:Introduction}\\r\\nFluid flows in a thin domain appear in many prob...</td></tr>\n", "<tr><td>1703.09894</td><td>en</td><td>2018-05-29T02:09:38</td><td>https://arxiv.org/abs/1703.09894</td><td>1703</td><td>\\section{Introduction}\\nThe phenomenon of neutrino oscillations, which is experimentally well est...</td></tr>\n", "<tr><td>1703.09829</td><td>en</td><td>2017-05-19T02:07:06</td><td>https://arxiv.org/abs/1703.09829</td><td>1703</td><td>\\section{Introduction}\\r\\n\\label{secintro}\\r\\n\\r\\nStar formation occurs within dense ($\\rm n(H_{2...</td></tr>\n", "</table>\n"], "text/plain": ["+-------------+-------------+-------------------+--------------------------------+---------+----------------------------------------------------------------------------------------------------+\n", "|meta.arxiv_id|meta.language|     meta.timestamp|                        meta.url|meta.yymm|                                                                                                text|\n", "+-------------+-------------+-------------------+--------------------------------+---------+----------------------------------------------------------------------------------------------------+\n", "|   1703.09675|           en|2017-03-29T02:10:36|https://arxiv.org/abs/1703.09675|     1703|\\section{Introduction}\\n\\nThe strongest motivation for new physics beyond the Standard Model (SM)...|\n", "|   1703.09617|           en|2017-05-10T02:06:18|https://arxiv.org/abs/1703.09617|     1703|\\section{Introduction}\\nThe \\nHo\\v{r}ava-Lifshitz gravity is motivated by a deformation of the us...|\n", "|   1703.09572|           en|2017-03-29T02:08:29|https://arxiv.org/abs/1703.09572|     1703|\\section{Introduction}\\n\\n\\par X-ray binaries are systems in which a black hole or neutron star a...|\n", "|   1703.09518|           en|2017-03-29T02:07:29|https://arxiv.org/abs/1703.09518|     1703|\\section{Introduction}\\n\\IEEEPARstart{T}{he} differential entropy of a given probability distribu...|\n", "|   1703.09907|           en|2017-03-30T02:04:07|https://arxiv.org/abs/1703.09907|     1703|\\subsection{#1}\\vskip5pt}\\n\\def\\Case#1{\\vskip4pt\\par\\noindent{\\it Case: #1}}\\n\\def\\Cases#1{\\vskip...|\n", "|   1703.09636|           en|2017-09-25T02:05:58|https://arxiv.org/abs/1703.09636|     1703|\\section{Introduction and Statement of Results}\\r\\n\\r\\nLet $L/K$ be a finite Galois extension of ...|\n", "|   1703.09652|           en|2017-07-19T02:07:36|https://arxiv.org/abs/1703.09652|     1703|\\section{Introduction}\\label{sec:Intro}\\nLet $G$ be a finite group. We say that $G$ is \\emph{$d$-...|\n", "|   1703.09698|           en|2017-10-10T02:04:09|https://arxiv.org/abs/1703.09698|     1703|\\section{Introduction} \\label{S:Introduction}\\r\\nFluid flows in a thin domain appear in many prob...|\n", "|   1703.09894|           en|2018-05-29T02:09:38|https://arxiv.org/abs/1703.09894|     1703|\\section{Introduction}\\nThe phenomenon of neutrino oscillations, which is experimentally well est...|\n", "|   1703.09829|           en|2017-05-19T02:07:06|https://arxiv.org/abs/1703.09829|     1703|\\section{Introduction}\\r\\n\\label{secintro}\\r\\n\\r\\nStar formation occurs within dense ($\\rm n(H_{2...|\n", "+-------------+-------------+-------------------+--------------------------------+---------+----------------------------------------------------------------------------------------------------+"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Component  book\n", "Schema:  struct<meta:struct<publication_date:bigint,short_book_title:string,title:string,url:string>,text:string>\n", "Sample: \n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>meta.publication_date</th><th>meta.short_book_title</th><th>meta.title</th><th>meta.url</th><th>text</th></tr>\n", "<tr><td>null</td><td>null</td><td><PERSON> Roberts - Chesapeake Bay - The Quinn Legacy_ Inner Harbor, Chesapeake Blue [retail]</td><td>null</td><td> \\nNora Roberts\\n\\nHOT ICE\\n\\nSACRED SINS\\n\\nBRAZEN VIRTUE\\n\\nSWEET REVENGE\\n\\nPUBLIC SECRETS\\n\\n...</td></tr>\n", "<tr><td>null</td><td>null</td><td>Nature&#x27;s Top 40_ Britain&#x27;s Best - <PERSON></td><td>null</td><td>\\n\\n## Dedication\\n\\n_Dedicated to my dad... you would have been chuffed to bits_\\n\\n## Contents\\...</td></tr>\n", "<tr><td>null</td><td>null</td><td><PERSON> and the Art of Diploma</td><td>null</td><td>\\n\\nNapoleon le Grand\\n\\n_Library of Congress_\\n\\n© 2012 by <PERSON>\\n\\nAll rights reser...</td></tr>\n", "<tr><td>null</td><td>null</td><td><PERSON>&#x27;s <PERSON><PERSON> <PERSON> <PERSON> R</td><td>null</td><td> \\n## PRAISE FOR THE NOVELS OF SIMON R. GREEN\\n\\nAgents of Light and Darkness\\n\\n&quot;If you like you...</td></tr>\n", "<tr><td>null</td><td>null</td><td>Newhall - <PERSON><PERSON><PERSON></td><td>null</td><td>\\n\\nMAP OF NEWHALL. This 1889 map is the earliest known one of Newhall and was probably drawn up ...</td></tr>\n", "<tr><td>null</td><td>null</td><td>Night of the Avenging Blowfish</td><td>null</td><td>\\nJOHN WELTER\\n\\n# **NIGHT  \\nOF THE  \\nAVENGING  \\nBLOWFISH**\\n\\nA NOVEL OF COVERT OPERATIONS, L...</td></tr>\n", "<tr><td>null</td><td>null</td><td>Nous et les Autres</td><td>null</td><td> \\nLa première édition de cet ouvrage a paru  \\ndans la collection « La couleur des idées » en 19...</td></tr>\n", "<tr><td>null</td><td>null</td><td><PERSON> <PERSON> <PERSON></td><td>null</td><td> \\nTable of Contents\\n\\nTitle Page\\n\\nCopyright Page\\n\\nDedication\\n\\n**BY THE SAME AUTHOR**\\n\\n_...</td></tr>\n", "<tr><td>null</td><td>null</td><td>Nothing to Fear - <PERSON></td><td>null</td><td>\\n\\nAs problems confront us, how many times will someone say &quot;that&#x27;s life&quot;? From Scripture and a ...</td></tr>\n", "<tr><td>null</td><td>null</td><td>No</td><td>null</td><td>\\n\\n**No.452 (RAAF) Squadron 1941-1945\\n\\neISBN: 978-2918590-22-4**\\n\\n_Contributors &amp; Acknowledg...</td></tr>\n", "</table>\n"], "text/plain": ["+---------------------+---------------------+----------------------------------------------------------------------------------------+--------+----------------------------------------------------------------------------------------------------+\n", "|meta.publication_date|meta.short_book_title|                                                                              meta.title|meta.url|                                                                                                text|\n", "+---------------------+---------------------+----------------------------------------------------------------------------------------+--------+----------------------------------------------------------------------------------------------------+\n", "|                 null|                 null|Nora Roberts - Chesapeake Bay - The Quinn Legacy_ Inner Harbor, Chesapeake Blue [retail]|    null| \\nNora Roberts\\n\\nHOT ICE\\n\\nSACRED SINS\\n\\nBRAZEN VIRTUE\\n\\nSWEET REVENGE\\n\\nPUBLIC SECRETS\\n\\n...|\n", "|                 null|                 null|                                           Nature's Top 40_ Britain's Best - <PERSON>|    null|\\n\\n## Dedication\\n\\n_Dedicated to my dad... you would have been chuffed to bits_\\n\\n## Contents\\...|\n", "|                 null|                 null|                                                         <PERSON> and the Art of Diploma|    null|\\n\\nNapoleon le Grand\\n\\n_Library of Congress_\\n\\n© 2012 by <PERSON>\\n\\nAll rights reser...|\n", "|                 null|                 null|                                                          <PERSON>'s <PERSON><PERSON> <PERSON> <PERSON> R|    null| \\n## PRAISE FOR THE NOVELS OF SIMON R. GREEN\\n\\nAgents of Light and Darkness\\n\\n\"If you like you...|\n", "|                 null|                 null|                                                                 <PERSON><PERSON> - <PERSON><PERSON><PERSON>|    null|\\n\\nMAP OF NEWHALL. This 1889 map is the earliest known one of Newhall and was probably drawn up ...|\n", "|                 null|                 null|                                                          Night of the Avenging Blowfish|    null|\\nJOHN WELTER\\n\\n# **NIGHT  \\nOF THE  \\nAVENGING  \\nBLOWFISH**\\n\\nA NOVEL OF COVERT OPERATIONS, L...|\n", "|                 null|                 null|                                                                      Nous et les Autres|    null| \\nLa première édition de cet ouvrage a paru  \\ndans la collection « La couleur des idées » en 19...|\n", "|                 null|                 null|                                                                <PERSON> <PERSON> <PERSON>|    null| \\nTable of Contents\\n\\nTitle Page\\n\\nCopyright Page\\n\\nDedication\\n\\n**BY THE SAME AUTHOR**\\n\\n_...|\n", "|                 null|                 null|                                                               Nothing to Fear - <PERSON> C|    null|\\n\\nAs problems confront us, how many times will someone say \"that's life\"? From Scripture and a ...|\n", "|                 null|                 null|                                                                                      No|    null|\\n\\n**No.452 (RAAF) Squadron 1941-1945\\n\\neISBN: 978-2918590-22-4**\\n\\n_Contributors & Acknowledg...|\n", "+---------------------+---------------------+----------------------------------------------------------------------------------------+--------+----------------------------------------------------------------------------------------------------+"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Component  stackexchange\n", "Schema:  struct<meta:struct<language:string,question_score:string,source:string,timestamp:string,url:string>,text:string>\n", "Sample: \n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>meta.language</th><th>meta.question_score</th><th>meta.source</th><th>meta.timestamp</th><th>meta.url</th><th>text</th></tr>\n", "<tr><td>en</td><td>5</td><td>stackexchange</td><td>2023-03-29</td><td>https://scifi.stackexchange.com/questions/6924</td><td>Q: How could they possibly not know about Observers? At the end of Fringe&#x27;s third season, there w...</td></tr>\n", "<tr><td>en</td><td>17</td><td>stackexchange</td><td>2023-03-29</td><td>https://scifi.stackexchange.com/questions/6927</td><td>Q: Why did the writers of Blake&#x27;s 7 destroy the Liberator? The Liberator was a beautiful ship des...</td></tr>\n", "<tr><td>en</td><td>46</td><td>stackexchange</td><td>2023-03-29</td><td>https://scifi.stackexchange.com/questions/6935</td><td>Q: Did <PERSON><PERSON> know that this character was his daughter from the start? When <PERSON><PERSON> and...</td></tr>\n", "<tr><td>en</td><td>19</td><td>stackexchange</td><td>2023-03-29</td><td>https://scifi.stackexchange.com/questions/6937</td><td>Q: In Star Trek (2009) why was the Romulan mining ship medical bay full of water In the film Star...</td></tr>\n", "<tr><td>en</td><td>39</td><td>stackexchange</td><td>2023-03-29</td><td>https://scifi.stackexchange.com/questions/6938</td><td>Q: In Star Trek (2009), what did the Romulans do for 25 years while waiting? In the film Star Tre...</td></tr>\n", "<tr><td>en</td><td>17</td><td>stackexchange</td><td>2023-03-29</td><td>https://scifi.stackexchange.com/questions/6943</td><td>Q: Short story of a lone person at a military outpost/station that may have the quote &quot;Hello, Bro...</td></tr>\n", "<tr><td>en</td><td>12</td><td>stackexchange</td><td>2023-03-29</td><td>https://scifi.stackexchange.com/questions/6944</td><td>Q: Do I need to check on wiki for unknown terms in Dune? I just started to read Dune, the terms a...</td></tr>\n", "<tr><td>en</td><td>20</td><td>stackexchange</td><td>2023-03-29</td><td>https://scifi.stackexchange.com/questions/6946</td><td>Q: What is a quad? Quads are mentioned often in the Star Trek universe, in varying orders of magn...</td></tr>\n", "<tr><td>en</td><td>32</td><td>stackexchange</td><td>2023-03-29</td><td>https://scifi.stackexchange.com/questions/6954</td><td>Q: Man sells his soul in exchange for gold, can&#x27;t bathe for years I know the title question for t...</td></tr>\n", "<tr><td>en</td><td>5</td><td>stackexchange</td><td>2023-03-29</td><td>https://scifi.stackexchange.com/questions/6956</td><td>Q: Did <PERSON> alter Walter? At the end of Fringe&#x27;s third season, there was a significant ch...</td></tr>\n", "</table>\n"], "text/plain": ["+-------------+-------------------+-------------+--------------+----------------------------------------------+----------------------------------------------------------------------------------------------------+\n", "|meta.language|meta.question_score|  meta.source|meta.timestamp|                                      meta.url|                                                                                                text|\n", "+-------------+-------------------+-------------+--------------+----------------------------------------------+----------------------------------------------------------------------------------------------------+\n", "|           en|                  5|stackexchange|    2023-03-29|https://scifi.stackexchange.com/questions/6924|Q: How could they possibly not know about Observers? At the end of Fringe's third season, there w...|\n", "|           en|                 17|stackexchange|    2023-03-29|https://scifi.stackexchange.com/questions/6927|Q: Why did the writers of <PERSON>'s 7 destroy the Liberator? The Liberator was a beautiful ship des...|\n", "|           en|                 46|stackexchange|    2023-03-29|https://scifi.stackexchange.com/questions/6935|Q: Did <PERSON><PERSON> know that this character was his daughter from the start? When Dar<PERSON>ader and...|\n", "|           en|                 19|stackexchange|    2023-03-29|https://scifi.stackexchange.com/questions/6937|Q: In Star Trek (2009) why was the Romulan mining ship medical bay full of water In the film Star...|\n", "|           en|                 39|stackexchange|    2023-03-29|https://scifi.stackexchange.com/questions/6938|Q: In Star Trek (2009), what did the Romulans do for 25 years while waiting? In the film Star Tre...|\n", "|           en|                 17|stackexchange|    2023-03-29|https://scifi.stackexchange.com/questions/6943|Q: Short story of a lone person at a military outpost/station that may have the quote \"Hello, Bro...|\n", "|           en|                 12|stackexchange|    2023-03-29|https://scifi.stackexchange.com/questions/6944|Q: Do I need to check on wiki for unknown terms in Dune? I just started to read Dune, the terms a...|\n", "|           en|                 20|stackexchange|    2023-03-29|https://scifi.stackexchange.com/questions/6946|Q: What is a quad? Quads are mentioned often in the Star Trek universe, in varying orders of magn...|\n", "|           en|                 32|stackexchange|    2023-03-29|https://scifi.stackexchange.com/questions/6954|Q: Man sells his soul in exchange for gold, can't bathe for years I know the title question for t...|\n", "|           en|                  5|stackexchange|    2023-03-29|https://scifi.stackexchange.com/questions/6956|Q: Did <PERSON> alter Walter? At the end of Fringe's third season, there was a significant ch...|\n", "+-------------+-------------------+-------------+--------------+----------------------------------------------+----------------------------------------------------------------------------------------------------+"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Component  wikipedia\n", "Schema:  struct<meta:struct<language:string,timestamp:string,title:string,url:string>,text:string>\n", "Sample: \n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>meta.language</th><th>meta.timestamp</th><th>meta.title</th><th>meta.url</th><th>text</th></tr>\n", "<tr><td>it</td><td>20230320</td><td><PERSON></td><td>https://it.wikipedia.org/wiki/<PERSON>%20F<PERSON>berg</td><td>\\n\\nCarriera\\n\\nClub \\nHa cominciato la sua carriera da professionista nel 1990 nella squadra del...</td></tr>\n", "<tr><td>it</td><td>20230320</td><td>Pallacanestro ai Giochi della XX Olimpiade</td><td>https://it.wikipedia.org/wiki/Pallacanestro%20ai%20Giochi%20della%20XX%20Olimpiade</td><td>Il torneo di pallacanestro ai Giochi della XX Olimpiade si disputò a Monaco di Baviera (all&#x27;epoca...</td></tr>\n", "<tr><td>it</td><td>20230320</td><td>Perossidi</td><td>https://it.wikipedia.org/wiki/Perossidi</td><td>I perossidi sono composti chimici contenenti il gruppo caratteristico formato da due atomi di oss...</td></tr>\n", "<tr><td>it</td><td>20230320</td><td>Uncertain</td><td>https://it.wikipedia.org/wiki/Uncertain</td><td>Uncertain è il primo EP della band irlandese The Cranberries. È stato pubblicato dalla Xeric Reco...</td></tr>\n", "<tr><td>it</td><td>20230320</td><td>Miracolo a Milano</td><td>https://it.wikipedia.org/wiki/Miracolo%20a%20Milano</td><td>Miracolo a Milano è un film fantastico del 1951 co-scritto, prodotto e diretto da Vittorio De Sic...</td></tr>\n", "<tr><td>it</td><td>20230320</td><td>Cratere Abul Wáfa</td><td>https://it.wikipedia.org/wiki/Cratere%20Abul%20W%C3%A1fa</td><td>Abul Wáfa è un cratere lunare intitolato al matematico ed astronomo persiano del X secolo Abul Wá...</td></tr>\n", "<tr><td>it</td><td>20230320</td><td>Andromeda (serie televisiva)</td><td>https://it.wikipedia.org/wiki/Andromeda%20%28serie%20televisiva%29</td><td>Andromeda è una serie televisiva fantascientifica creata da <PERSON>, già ideatore della ...</td></tr>\n", "<tr><td>it</td><td>20230320</td><td><PERSON> l-<PERSON><PERSON><PERSON> al<PERSON></td><td>https://it.wikipedia.org/wiki/Abu%20l-Wafa%20Muh<PERSON>mad%20al-Buzjani</td><td>Realizzò un quadrante a muro per accurate misure astronomiche della declinazione delle stelle; in...</td></tr>\n", "<tr><td>it</td><td>20230320</td><td>Laguna di Venezia</td><td>https://it.wikipedia.org/wiki/Laguna%20di%20Venezia</td><td>La Laguna di Venezia, o Laguna veneta (in dialetto veneziano Ƚaguna de Venesia o Ƚaguna vèneta), ...</td></tr>\n", "<tr><td>it</td><td>20230320</td><td>RevConnect</td><td>https://it.wikipedia.org/wiki/RevConnect</td><td>RevConnect è un software di peer-to-peer (P2P) che utilizza la rete Direct Connect ed è basato su...</td></tr>\n", "</table>\n"], "text/plain": ["+-------------+--------------+------------------------------------------+----------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------+\n", "|meta.language|meta.timestamp|                                meta.title|                                                                          meta.url|                                                                                                text|\n", "+-------------+--------------+------------------------------------------+----------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------+\n", "|           it|      20230320|                            <PERSON>|                                    https://it.wikipedia.org/wiki/<PERSON>%20F<PERSON>berg|\\n\\nCarriera\\n\\nClub \\nHa cominciato la sua carriera da professionista nel 1990 nella squadra del...|\n", "|           it|      20230320|Pallacanestro ai Giochi della XX Olimpiade|https://it.wikipedia.org/wiki/Pallacanestro%20ai%20Giochi%20della%20XX%20Olimpiade|Il torneo di pallacanestro ai Giochi della XX Olimpiade si disputò a Monaco di Baviera (all'epoca...|\n", "|           it|      20230320|                                 Perossidi|                                           https://it.wikipedia.org/wiki/Perossidi|I perossidi sono composti chimici contenenti il gruppo caratteristico formato da due atomi di oss...|\n", "|           it|      20230320|                                 Uncertain|                                           https://it.wikipedia.org/wiki/Uncertain|Uncertain è il primo EP della band irlandese The Cranberries. È stato pubblicato dalla Xeric Reco...|\n", "|           it|      20230320|                         Miracolo a Milano|                               https://it.wikipedia.org/wiki/Miracolo%20a%20Milano|Miracolo a Milano è un film fantastico del 1951 co-scritto, prodotto e diretto da Vittorio De Sic...|\n", "|           it|      20230320|                         Cratere Abul Wáfa|                          https://it.wikipedia.org/wiki/Cratere%20Abul%20W%C3%A1fa|Abul Wáfa è un cratere lunare intitolato al matematico ed astronomo persiano del X secolo Abul Wá...|\n", "|           it|      20230320|              Andromeda (serie televisiva)|                https://it.wikipedia.org/wiki/Andromeda%20%28serie%20televisiva%29|Andromeda è una serie televisiva fantascientifica creata da <PERSON>, già ideatore della ...|\n", "|           it|      20230320|            <PERSON> l<PERSON><PERSON><PERSON><PERSON>|                https://it.wikipedia.org/wiki/Abu%20l-Wafa%20Muhammad%20al-<PERSON>uzjani|Realizzò un quadrante a muro per accurate misure astronomiche della declinazione delle stelle; in...|\n", "|           it|      20230320|                         Laguna di Venezia|                               https://it.wikipedia.org/wiki/Laguna%20di%20Venezia|La Laguna di Venezia, o Laguna veneta (in dialetto veneziano Ƚaguna de Venesia o Ƚaguna vèneta), ...|\n", "|           it|      20230320|                                RevConnect|                                          https://it.wikipedia.org/wiki/RevConnect|RevConnect è un software di peer-to-peer (P2P) che utilizza la rete Direct Connect ed è basato su...|\n", "+-------------+--------------+------------------------------------------+----------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------+"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Component  common_crawl/2023-06\n", "Schema:  struct<pred_label:string,pred_label_prob:double,source:string,text:string,wiki_prob:double>\n", "Sample: \n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>pred_label</th><th>pred_label_prob</th><th>source</th><th>text</th><th>wiki_prob</th></tr>\n", "<tr><td>__label__wiki</td><td>0.9097615480422974</td><td>cc/2023-06/en_head_0038.json.gz/line4</td><td><PERSON>: &quot;At certain points, the loyalty to the original 25-year-old animation is staggering&quot;\\...</td><td>0.9097615480422974</td></tr>\n", "<tr><td>__label__cc</td><td>0.6098833084106445</td><td>cc/2023-06/en_head_0038.json.gz/line9</td><td>D&amp;C Section 124\\nRevelation given through <PERSON> at Lamoni, Iowa, April 1897. The Twelv...</td><td>0.39011669158935547</td></tr>\n", "<tr><td>__label__wiki</td><td>0.8318547010421753</td><td>cc/2023-06/en_head_0038.json.gz/line11</td><td>This editable Main Article is under development and subject to a disclaimer.\\n[edit intro]\\nAn id...</td><td>0.8318547010421753</td></tr>\n", "<tr><td>__label__cc</td><td>0.7375264763832092</td><td>cc/2023-06/en_head_0038.json.gz/line13</td><td>Home » Financial Intelligence Authority (FIA)\\nFinancial Intelligence Authority (FIA)\\nThe Financ...</td><td>0.26247352361679077</td></tr>\n", "<tr><td>__label__wiki</td><td>0.5057622194290161</td><td>cc/2023-06/en_head_0038.json.gz/line16</td><td>weeping chinese elm\\nIt has small glossy dark serrated green leaves, which turn bronze to yellow ...</td><td>0.5057622194290161</td></tr>\n", "<tr><td>__label__wiki</td><td>0.7300208806991577</td><td>cc/2023-06/en_head_0038.json.gz/line18</td><td>References for Pinaceae\\nAnonymous. 1977. Abies grandis (Douglas ex D. Don) <PERSON>dley, Grand fir. D...</td><td>0.7300208806991577</td></tr>\n", "<tr><td>__label__cc</td><td>0.732601523399353</td><td>cc/2023-06/en_head_0038.json.gz/line22</td><td><PERSON>, <PERSON> b. between 1810 and 1815, d. Aug 6, 1890\\n<PERSON><PERSON><PERSON>, David W b. Dec 12, 1806, d. Apr 10...</td><td>0.267398476600647</td></tr>\n", "<tr><td>__label__wiki</td><td>0.8676305413246155</td><td>cc/2023-06/en_head_0038.json.gz/line24</td><td>Difference between revisions of &quot;Clerks Guide&quot;\\nLktesar (talk | contribs)\\n{{NoBookInfoBox\\n|shor...</td><td>0.8676305413246155</td></tr>\n", "<tr><td>__label__wiki</td><td>0.8079292178153992</td><td>cc/2023-06/en_head_0038.json.gz/line25</td><td>Countering global terror attacks\\nFollowing a series of high-profile terror attacks in 2018, the ...</td><td>0.8079292178153992</td></tr>\n", "<tr><td>__label__cc</td><td>0.5316174626350403</td><td>cc/2023-06/en_head_0038.json.gz/line29</td><td>16 things as old as our 2016 Freshers\\nPost written by <PERSON> | September 5, 2016\\nMost mem...</td><td>0.4683825373649597</td></tr>\n", "</table>\n"], "text/plain": ["+-------------+------------------+--------------------------------------+----------------------------------------------------------------------------------------------------+-------------------+\n", "|   pred_label|   pred_label_prob|                                source|                                                                                                text|          wiki_prob|\n", "+-------------+------------------+--------------------------------------+----------------------------------------------------------------------------------------------------+-------------------+\n", "|__label__wiki|0.9097615480422974| cc/2023-06/en_head_0038.json.gz/line4|<PERSON>: \"At certain points, the loyalty to the original 25-year-old animation is staggering\"\\...| 0.9097615480422974|\n", "|  __label__cc|0.6098833084106445| cc/2023-06/en_head_0038.json.gz/line9|D&C Section 124\\nRevelation given through <PERSON> at Lamoni, Iowa, April 1897. The Twelv...|0.39011669158935547|\n", "|__label__wiki|0.8318547010421753|cc/2023-06/en_head_0038.json.gz/line11|This editable Main Article is under development and subject to a disclaimer.\\n[edit intro]\\nAn id...| 0.8318547010421753|\n", "|  __label__cc|0.7375264763832092|cc/2023-06/en_head_0038.json.gz/line13|Home » Financial Intelligence Authority (FIA)\\nFinancial Intelligence Authority (FIA)\\nThe Financ...|0.26247352361679077|\n", "|__label__wiki|0.5057622194290161|cc/2023-06/en_head_0038.json.gz/line16|weeping chinese elm\\nIt has small glossy dark serrated green leaves, which turn bronze to yellow ...| 0.5057622194290161|\n", "|__label__wiki|0.7300208806991577|cc/2023-06/en_head_0038.json.gz/line18|References for Pinaceae\\nAnonymous. 1977. Abies grandis (Douglas ex D. Don) Lindley, Grand fir. D...| 0.7300208806991577|\n", "|  __label__cc| 0.732601523399353|cc/2023-06/en_head_0038.json.gz/line22|<PERSON>, <PERSON> b. between 1810 and 1815, d. Aug 6, 1890\\n<PERSON><PERSON><PERSON>, David <PERSON> b. Dec 12, 1806, d. Apr 10...|  0.267398476600647|\n", "|__label__wiki|0.8676305413246155|cc/2023-06/en_head_0038.json.gz/line24|Difference between revisions of \"Clerks Guide\"\\nLktesar (talk | contribs)\\n{{NoBookInfoBox\\n|shor...| 0.8676305413246155|\n", "|__label__wiki|0.8079292178153992|cc/2023-06/en_head_0038.json.gz/line25|Countering global terror attacks\\nFollowing a series of high-profile terror attacks in 2018, the ...| 0.8079292178153992|\n", "|  __label__cc|0.5316174626350403|cc/2023-06/en_head_0038.json.gz/line29|16 things as old as our 2016 Freshers\\nPost written by <PERSON> | September 5, 2016\\nMost mem...| 0.4683825373649597|\n", "+-------------+------------------+--------------------------------------+----------------------------------------------------------------------------------------------------+-------------------+"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# I have only converted the latest common crawl dumps for now.\n", "\n", "redpajama_base = 's3a://redpajama/warehouse'\n", "for path in ['github', 'c4', 'arxiv', 'book', 'stackexchange', 'wikipedia', 'common_crawl/2023-06']:\n", "    df = spark.read.parquet(f'{redpajama_base}/{path}')\n", "    print('Component ', path)\n", "    print('Schema: ', df.schema.simpleString())\n", "    print('Sample: ')\n", "    display(unnest_struct(df.limit(10)))"]}, {"cell_type": "markdown", "id": "e8b3d332-ef6e-4595-8632-600ea4b119f6", "metadata": {}, "source": ["### Starcoder dataset\n", "\n", "The Starcoder dataset is found in `s3a://starcoder`.  It is already in partitioned parquets and divided into different languages.  The only changes we made is that we named the langugage folders in Hive partition format so that `lang` will show up as a column "]}, {"cell_type": "code", "execution_count": 4, "id": "405e34e6-040a-44f6-a01a-df00ca73638d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Schema:  struct<max_stars_repo_path:string,max_stars_repo_name:string,max_stars_count:bigint,id:string,content:string,lang:string>\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>max_stars_repo_path</th><th>max_stars_repo_name</th><th>max_stars_count</th><th>id</th><th>content</th><th>lang</th></tr>\n", "<tr><td>app/.externalNativeBuild/cmake/release/armeabi-v7a/CMakeFiles/3.6.0-rc2/CMakeCCompiler.cmake</td><td>nerviantone/opencv-documentscanner-android-master</td><td>0</td><td>0</td><td>set(CMAKE_C_COMPILER &quot;/home/<USER>/Android/Sdk/ndk-bundle/toolchains/llvm/prebuilt/linux-x86_64/...</td><td>cmake</td></tr>\n", "<tr><td>ports/indicators/portfile.cmake</td><td>TheScarfix/vcpkg</td><td>24</td><td>1</td><td># header-only library\\r\\n\\r\\nvcpkg_from_github(\\r\\n    OUT_SOURCE_PATH SOURCE_PATH\\r\\n    REPO p-...</td><td>cmake</td></tr>\n", "<tr><td>build/cmake/version.cmake</td><td>mojing1999/aom</td><td>0</td><td>2</td><td>#\\n# Copyright (c) 2017, Alliance for Open Media. All rights reserved\\n#\\n# This source code is s...</td><td>cmake</td></tr>\n", "<tr><td>Onboard-SDK-ROS/dji_sdk_demo/cmake-modules/FindOpenCVViz.cmake</td><td>NMMI/aerial-alter-ego</td><td>6</td><td>3</td><td>&lt;reponame&gt;NMMI/aerial-alter-ego&lt;gh_stars&gt;1-10\\n# Once done this will define\\n#\\n#  FOUND_OPENCV_V...</td><td>cmake</td></tr>\n", "<tr><td>Software/CPU/myscrypt/build/cmake-3.12.3/Tests/RunCMake/string/ConcatNoArgs.cmake</td><td>duonglvtnaist/Multi-ROMix-Scrypt-Accelerator</td><td>107</td><td>4</td><td>&lt;gh_stars&gt;100-1000\\nstring(CONCAT)\\n</td><td>cmake</td></tr>\n", "<tr><td>cmake/02-opts.cmake</td><td>ronsabag/map-reduce</td><td>0</td><td>5</td><td>set(SETTING_ALSA_SOUNDCARD &quot;default&quot;\\n  CACHE STRING &quot;Name of the ALSA soundcard driver&quot;)\\nset(SE...</td><td>cmake</td></tr>\n", "<tr><td>src/optim/Sources.cmake</td><td>kikislater/micmac</td><td>451</td><td>6</td><td>set(Optim_Src_Files\\n\\t${OPTIM_DIR}/cox_roy.cpp\\n\\t${OPTIM_DIR}/func_mean_square.cpp\\n\\t${OPTIM_D...</td><td>cmake</td></tr>\n", "<tr><td>cmake/SPLAStaticConfig.cmake</td><td>AdhocMan/spla</td><td>0</td><td>7</td><td>include(CMakeFindDependencyMacro)\\n\\n# Only look for modules we installed and save value\\nset(_CM...</td><td>cmake</td></tr>\n", "<tr><td>cmake/FindTBB.cmake</td><td>DHY-2020/DSKCF-notbb</td><td>0</td><td>8</td><td>&lt;gh_stars&gt;0\\n# Locate Intel Threading Building Blocks include paths and libraries\\r\\n# TBB can be...</td><td>cmake</td></tr>\n", "<tr><td>build/CMakeFiles/traffic_simulation.dir/cmake_clean.cmake</td><td>jbarneyVBFD/Concurrent-Traffic-Simulation</td><td>0</td><td>9</td><td>&lt;reponame&gt;jbarneyVBFD/Concurrent-Traffic-Simulation\\nfile(REMOVE_RECURSE\\n  &quot;CMakeFiles/traffic_s...</td><td>cmake</td></tr>\n", "<tr><td>cmake/third_party/mlperf.cmake</td><td>IsolatedMy/plaidml</td><td>0</td><td>10</td><td>message(&quot;Fetching mlperf&quot;)\\nFetchContent_Declare(\\n  mlperf\\n  URL      https://github.com/mlcomm...</td><td>cmake</td></tr>\n", "<tr><td>ports/protobuf/portfile.cmake</td><td>Innervate/vcpkg</td><td>0</td><td>11</td><td>include(vcpkg_common_functions)\\n\\nset(PROTOBUF_VERSION 3.4.1)\\nset(PROTOC_VERSION 3.4.0)\\n\\nvcpk...</td><td>cmake</td></tr>\n", "<tr><td>cmake/share/cmake-3.12/Modules/FindJasper.cmake</td><td>ConnectionMaster/hge</td><td>26</td><td>12</td><td>&lt;reponame&gt;ConnectionMaster/hge&lt;filename&gt;cmake/share/cmake-3.12/Modules/FindJasper.cmake\\n# Distri...</td><td>cmake</td></tr>\n", "<tr><td>cmake/z3_add_component.cmake</td><td>delcypher/z3</td><td>1</td><td>13</td><td>include(CMakeParseArguments)\\ndefine_property(GLOBAL PROPERTY Z3_LIBZ3_COMPONENTS\\n              ...</td><td>cmake</td></tr>\n", "<tr><td>cmake/rpath_fixer.cmake</td><td>lparth/homeenc-HElib</td><td>1</td><td>14</td><td># Function to change external libraries rpath on mac and linux\\nfunction(change_rpath lib_name_no...</td><td>cmake</td></tr>\n", "<tr><td>cmake/find/s3.cmake</td><td>lizhic<PERSON>/ClickHouse</td><td>18</td><td>15</td><td>&lt;reponame&gt;lizhichao/ClickHouse\\nif(NOT OS_FREEBSD AND NOT APPLE AND NOT ARCH_ARM)\\n    option(ENA...</td><td>cmake</td></tr>\n", "<tr><td>deps/src/cmake-3.9.3/Modules/MatlabTestsRedirect.cmake</td><td>shreyasvj25/turicreate</td><td>26</td><td>16</td><td># Distributed under the OSI-approved BSD 3-Clause License.  See accompanying\\n# file Copyright.tx...</td><td>cmake</td></tr>\n", "<tr><td>ports/box2d/portfile.cmake</td><td>athre0z/vcpkg</td><td>14</td><td>17</td><td>&lt;gh_stars&gt;10-100\\ninclude(vcpkg_common_functions)\\n\\nvcpkg_check_linkage(ONLY_STATIC_LIBRARY)\\n\\n...</td><td>cmake</td></tr>\n", "<tr><td>ports/espressif/boards/adafruit_camera_esp32s2/board.cmake</td><td>fabaff/tinyuf2</td><td>109</td><td>18</td><td>&lt;reponame&gt;fabaff/tinyuf2\\n# Apply board specific content here\\nset(IDF_TARGET &quot;esp32s2&quot;)\\n</td><td>cmake</td></tr>\n", "<tr><td>3rdparty/github/cmake-utils/cmake/FindStrtoll.cmake</td><td>jddurand/c-genericLogger</td><td>6</td><td>19</td><td>MACRO (FINDSTRTOLL)\\n  GET_PROPERTY(source_dir_set GLOBAL PROPERTY MYPACKAGE_SOURCE_DIR SET)\\n  I...</td><td>cmake</td></tr>\n", "</table>\n", "only showing top 20 rows\n"], "text/plain": ["+--------------------------------------------------------------------------------------------+-------------------------------------------------+---------------+---+----------------------------------------------------------------------------------------------------+-----+\n", "|                                                                         max_stars_repo_path|                              max_stars_repo_name|max_stars_count| id|                                                                                             content| lang|\n", "+--------------------------------------------------------------------------------------------+-------------------------------------------------+---------------+---+----------------------------------------------------------------------------------------------------+-----+\n", "|app/.externalNativeBuild/cmake/release/armeabi-v7a/CMakeFiles/3.6.0-rc2/CMakeCCompiler.cmake|nerviantone/opencv-documentscanner-android-master|              0|  0|set(CMAKE_C_COMPILER \"/home/<USER>/Android/Sdk/ndk-bundle/toolchains/llvm/prebuilt/linux-x86_64/...|cmake|\n", "|                                                             ports/indicators/portfile.cmake|                                 TheScarfix/vcpkg|             24|  1|# header-only library\\r\\n\\r\\nvcpkg_from_github(\\r\\n    OUT_SOURCE_PATH SOURCE_PATH\\r\\n    REPO p-...|cmake|\n", "|                                                                   build/cmake/version.cmake|                                   mojing1999/aom|              0|  2|#\\n# Copyright (c) 2017, Alliance for Open Media. All rights reserved\\n#\\n# This source code is s...|cmake|\n", "|                              Onboard-SDK-ROS/dji_sdk_demo/cmake-modules/FindOpenCVViz.cmake|                            NMMI/aerial-alter-ego|              6|  3|<reponame>NMMI/aerial-alter-ego<gh_stars>1-10\\n# Once done this will define\\n#\\n#  FOUND_OPENCV_V...|cmake|\n", "|           Software/CPU/myscrypt/build/cmake-3.12.3/Tests/RunCMake/string/ConcatNoArgs.cmake|     duonglvtnaist/Multi-ROMix-Scrypt-Accelerator|            107|  4|                                                                <gh_stars>100-1000\\nstring(CONCAT)\\n|cmake|\n", "|                                                                         cmake/02-opts.cmake|                              ronsabag/map-reduce|              0|  5|set(SETTING_ALSA_SOUNDCARD \"default\"\\n  CACHE STRING \"Name of the ALSA soundcard driver\")\\nset(SE...|cmake|\n", "|                                                                     src/optim/Sources.cmake|                                kikislater/micmac|            451|  6|set(Optim_Src_Files\\n\\t${OPTIM_DIR}/cox_roy.cpp\\n\\t${OPTIM_DIR}/func_mean_square.cpp\\n\\t${OPTIM_D...|cmake|\n", "|                                                                cmake/SPLAStaticConfig.cmake|                                    AdhocMan/spla|              0|  7|include(CMakeFindDependencyMacro)\\n\\n# Only look for modules we installed and save value\\nset(_CM...|cmake|\n", "|                                                                         cmake/FindTBB.cmake|                             DHY-2020/DSKCF-notbb|              0|  8|<gh_stars>0\\n# Locate Intel Threading Building Blocks include paths and libraries\\r\\n# TBB can be...|cmake|\n", "|                                   build/CMakeFiles/traffic_simulation.dir/cmake_clean.cmake|        jbarneyVBFD/Concurrent-Traffic-Simulation|              0|  9|<reponame>jbarneyVBFD/Concurrent-Traffic-Simulation\\nfile(REMOVE_RECURSE\\n  \"CMakeFiles/traffic_s...|cmake|\n", "|                                                              cmake/third_party/mlperf.cmake|                               IsolatedMy/plaidml|              0| 10|message(\"Fetching mlperf\")\\nFetchContent_Declare(\\n  mlperf\\n  URL      https://github.com/mlcomm...|cmake|\n", "|                                                               ports/protobuf/portfile.cmake|                                  Innervate/vcpkg|              0| 11|include(vcpkg_common_functions)\\n\\nset(PROTOBUF_VERSION 3.4.1)\\nset(PROTOC_VERSION 3.4.0)\\n\\nvcpk...|cmake|\n", "|                                             cmake/share/cmake-3.12/Modules/FindJasper.cmake|                             ConnectionMaster/hge|             26| 12|<reponame>ConnectionMaster/hge<filename>cmake/share/cmake-3.12/Modules/FindJasper.cmake\\n# Distri...|cmake|\n", "|                                                                cmake/z3_add_component.cmake|                                     delcypher/z3|              1| 13|include(CMakeParseArguments)\\ndefine_property(GLOBAL PROPERTY Z3_LIBZ3_COMPONENTS\\n              ...|cmake|\n", "|                                                                     cmake/rpath_fixer.cmake|                             lparth/homeenc-HElib|              1| 14|# Function to change external libraries rpath on mac and linux\\nfunction(change_rpath lib_name_no...|cmake|\n", "|                                                                         cmake/find/s3.cmake|                             lizhic<PERSON>/ClickHouse|             18| 15|<reponame>lizhichao/ClickHouse\\nif(NOT OS_FREEBSD AND NOT APPLE AND NOT ARCH_ARM)\\n    option(ENA...|cmake|\n", "|                                      deps/src/cmake-3.9.3/Modules/MatlabTestsRedirect.cmake|                           shreyasvj25/turicreate|             26| 16|# Distributed under the OSI-approved BSD 3-Clause License.  See accompanying\\n# file Copyright.tx...|cmake|\n", "|                                                                  ports/box2d/portfile.cmake|                                    athre0z/vcpkg|             14| 17|<gh_stars>10-100\\ninclude(vcpkg_common_functions)\\n\\nvcpkg_check_linkage(ONLY_STATIC_LIBRARY)\\n\\n...|cmake|\n", "|                                  ports/espressif/boards/adafruit_camera_esp32s2/board.cmake|                                   fabaff/tinyuf2|            109| 18|          <reponame>fabaff/tinyuf2\\n# Apply board specific content here\\nset(IDF_TARGET \"esp32s2\")\\n|cmake|\n", "|                                         3rdparty/github/cmake-utils/cmake/FindStrtoll.cmake|                         jddurand/c-genericLogger|              6| 19|MACRO (FINDSTRTOLL)\\n  GET_PROPERTY(source_dir_set GLOBAL PROPERTY MYPACKAGE_SOURCE_DIR SET)\\n  I...|cmake|\n", "+--------------------------------------------------------------------------------------------+-------------------------------------------------+---------------+---+----------------------------------------------------------------------------------------------------+-----+\n", "only showing top 20 rows"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["starcoder_base = 's3a://starcoder/raw'\n", "# Note: basePath is necessary for the parquet reader to understand the files are structured in a partitioned format\n", "all_lang = spark.read.option(\"basePath\", starcoder_base).parquet(f'{starcoder_base}/lang=*/*.parquet')\n", "# have a quick view of the overall dataframe\n", "print('Schema: ', all_lang.schema.simpleString())\n", "all_lang"]}, {"cell_type": "markdown", "id": "320da1cb-0564-4db9-ac4d-2ca56bca9c3c", "metadata": {}, "source": ["## Fix dataset star count types\n", "\n", "In Starcoder dataset the number of stars is float in the C, C++, C# and a few other language sets.  This is problematic.  We will cast them to int here."]}, {"cell_type": "code", "execution_count": 5, "id": "2c7cc257-1a86-435b-aa7b-892cf530c5e5", "metadata": {"execution": {"iopub.execute_input": "2023-05-24T23:03:27.748586Z", "iopub.status.busy": "2023-05-24T23:03:27.747708Z", "iopub.status.idle": "2023-05-24T23:04:18.332926Z", "shell.execute_reply": "2023-05-24T23:04:18.330973Z", "shell.execute_reply.started": "2023-05-24T23:03:27.748524Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fixing dataset xslt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Fixing dataset verilog\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["starcoder_backup = 's3a://starcoder/backup'\n", "starcoder_base = 's3a://starcoder/raw'\n", "\n", "fix_langs = ['xslt', 'verilog', 'cpp', 'c', 'c-sharp', 'elixir']\n", "\n", "for lang in fix_langs:\n", "    try:\n", "        probe = spark.read.parquet(f'{starcoder_base}/lang={lang}/*.parquet')\n", "        need_fix = False\n", "        for field in probe.schema.jsonValue()['fields']:\n", "            if field['name'] == 'max_stars_count' and field['type'] == 'double':\n", "                need_fix = True\n", "    except:\n", "        need_fix = True\n", "    if not need_fix:\n", "        continue\n", "    print(f'Fixing dataset {lang}')\n", "    df = spark.read.parquet(f'{starcoder_backup}/lang={lang}/*.parquet')\n", "    df.withColumn('max_stars_count', sp.col('max_stars_count').cast('bigint')).write.mode('overwrite').parquet(f'{starcoder_base}/lang={lang}/')\n"]}, {"cell_type": "markdown", "id": "780509e5-9839-4c9e-ae0a-49a1333f7322", "metadata": {}, "source": ["#### High level statistics\n", "Here we also run some statistics across the entire dataset to make sure it works.  It is also in a way a performance test.\n", "\n", "(Expected runtime is about 20~30min to analyze through the entire dataset with 16CPUs.)"]}, {"cell_type": "code", "execution_count": 5, "id": "028d3007-315b-4f52-bc75-2cbae2064e8b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>lang</th><th>average_bytes</th><th>stddev_bytes</th><th>count_millions</th><th>total_GB</th></tr>\n", "<tr><td>java</td><td>4354.762022119321</td><td>15681.821041160238</td><td>20.071773</td><td>87.407794777</td></tr>\n", "<tr><td>markdown</td><td>3557.1996060541665</td><td>12865.33596139883</td><td>21.029287</td><td>74.805371432</td></tr>\n", "<tr><td>javascript</td><td>3324.827492179939</td><td>14521.167194257167</td><td>19.544285</td><td>64.981376083</td></tr>\n", "<tr><td>php</td><td>3893.640825295286</td><td>12280.620997741613</td><td>15.683017</td><td>61.064035255</td></tr>\n", "<tr><td>python</td><td>4705.554148558805</td><td>13148.63058022484</td><td>12.866649</td><td>60.54471358</td></tr>\n", "<tr><td>github-issues-filtered-structured</td><td>1943.3637113374111</td><td>6737.177976212181</td><td>30.982955</td><td>60.211150417</td></tr>\n", "<tr><td>git-commits-cleaned</td><td>7443.37591133032</td><td>9851.870219857306</td><td>7.634718</td><td>56.828076051</td></tr>\n", "<tr><td>c</td><td>6324.761873167564</td><td>26319.98562765786</td><td>8.536791</td><td>53.993170236</td></tr>\n", "<tr><td>cpp</td><td>7714.12644551601</td><td>27149.561736662446</td><td>6.353527</td><td>49.011910653</td></tr>\n", "<tr><td>c-sharp</td><td>4154.812750797706</td><td>14572.13824315</td><td>10.801285</td><td>44.877316643</td></tr>\n", "<tr><td>html</td><td>8893.189252007218</td><td>29254.93685210389</td><td>3.299965</td><td>29.34721327</td></tr>\n", "<tr><td>typescript</td><td>2531.6714436097627</td><td>10341.15950549195</td><td>10.547331</td><td>26.702376699</td></tr>\n", "<tr><td>go</td><td>5066.854461181579</td><td>21563.347326635256</td><td>4.700526</td><td>23.816881133</td></tr>\n", "<tr><td>css</td><td>4397.917352043786</td><td>20913.488522714353</td><td>2.721616</td><td>11.969442232</td></tr>\n", "<tr><td>sql</td><td>11203.588520842304</td><td>59792.565095724225</td><td>0.97542</td><td>10.928204315</td></tr>\n", "<tr><td>rust</td><td>6610.028850360892</td><td>23297.058551555478</td><td>1.380468</td><td>9.124933307</td></tr>\n", "<tr><td>jupyter-scripts-dedup-filtered</td><td>7747.441042744202</td><td>10055.41156121818</td><td>0.91451</td><td>7.085112308</td></tr>\n", "<tr><td>ruby</td><td>2023.3573630217797</td><td>7694.014852474908</td><td>3.39032</td><td>6.859828935</td></tr>\n", "<tr><td>jupyter-structured-clean-dedup</td><td>8818.619070405222</td><td>14539.112982134142</td><td>0.668743</td><td>5.897389773</td></tr>\n", "<tr><td>kotlin</td><td>2561.8626130571583</td><td>5907.660617690251</td><td>2.239354</td><td>5.73691729</td></tr>\n", "</table>\n", "only showing top 20 rows\n"], "text/plain": ["+---------------------------------+------------------+------------------+--------------+------------+\n", "|                             lang|     average_bytes|      stddev_bytes|count_millions|    total_GB|\n", "+---------------------------------+------------------+------------------+--------------+------------+\n", "|                             java| 4354.762022119321|15681.821041160245|     20.071773|87.407794777|\n", "|                         markdown|3557.1996060541665|12865.335961398825|     21.029287|74.805371432|\n", "|                       javascript| 3324.827492179939|14521.167194257174|     19.544285|64.981376083|\n", "|                              php| 3893.640825295286|12280.620997741606|     15.683017|61.064035255|\n", "|                           python| 4705.554148558805|13148.630580224843|     12.866649| 60.54471358|\n", "|github-issues-filtered-structured|1943.3637113374111|  6737.17797621218|     30.982955|60.211150417|\n", "|              git-commits-cleaned|  7443.37591133032| 9851.870219857301|      7.634718|56.828076051|\n", "|                                c| 6324.761873167564| 26319.98562765788|      8.536791|53.993170236|\n", "|                              cpp|  7714.12644551601|27149.561736662454|      6.353527|49.011910653|\n", "|                          c-sharp| 4154.812750797706|14572.138243150004|     10.801285|44.877316643|\n", "|                             html| 8893.189252007218|29254.936852103892|      3.299965| 29.34721327|\n", "|                       typescript|2531.6714436097627|10341.159505491947|     10.547331|26.702376699|\n", "|                               go| 5066.854461181579| 21563.34732663525|      4.700526|23.816881133|\n", "|                              css| 4397.917352043786|20913.488522714357|      2.721616|11.969442232|\n", "|                              sql|11203.588520842304| 59792.56509572421|       0.97542|10.928204315|\n", "|                             rust| 6610.028850360892|23297.058551555474|      1.380468| 9.124933307|\n", "|   jupyter-scripts-dedup-filtered| 7747.441042744202| 10055.41156121818|       0.91451| 7.085112308|\n", "|                             ruby|2023.3573630217797| 7694.014852474908|       3.39032| 6.859828935|\n", "|   jupyter-structured-clean-dedup| 8818.619070405222| 14539.11298213414|      0.668743| 5.897389773|\n", "|                           kotlin|2561.8626130571583| 5907.660617690251|      2.239354|  5.73691729|\n", "+---------------------------------+------------------+------------------+--------------+------------+\n", "only showing top 20 rows"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["stats = all_lang.withColumn('length', sp.length('content')).groupBy('lang').agg(\n", "    sp.avg('length').alias('average_bytes'),\n", "    sp.stddev('length').alias('stddev_bytes'),\n", "    (sp.count('length')/1e6).alias('count_millions'),\n", "    (sp.sum('length')/1e9).alias('total_GB'),\n", ").orderBy(sp.desc('total_GB'))\n", "stats"]}, {"cell_type": "markdown", "id": "382e7bb9-a075-4121-95e3-a7e58e4a65bc", "metadata": {}, "source": ["**Important note**: the schema of some of the subsets, namely the four below, are different and have additional columns.  These additional columns won't be in this \n", "dataframe because other language datasets don't have them.  To fully utilize these you can load them separately.\n", "\n", " - jupyter-scripts-dedup-filtered\n", " - jupyter-structured-clean-dedup\n", " - github-issues-filtered-structured\n", " - git-commits-cleaned"]}, {"cell_type": "code", "execution_count": 6, "id": "0cb2f4b8-15b4-46ec-9bb6-3140bf6ac332", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Schema: StructType([Struct<PERSON>ield('id', StringType(), True), StructField('content', StringType(), True)])\n"]}, {"data": {"text/html": ["<table border='1'>\n", "<tr><th>id</th><th>content</th></tr>\n", "<tr><td>0</td><td>&lt;issue_start&gt;&lt;issue_comment&gt;Title: Simple File Silo: Errors aren&#x27;t submitted to message system\\nu...</td></tr>\n", "<tr><td>1</td><td>&lt;issue_start&gt;&lt;issue_comment&gt;Title: Asset Pipeline ignores assets.compress config option\\nusername...</td></tr>\n", "<tr><td>2</td><td>&lt;issue_start&gt;&lt;issue_comment&gt;Title: Initializing Schema Migrations with &quot;dbo.&quot; in table prefix bre...</td></tr>\n", "<tr><td>3</td><td>&lt;issue_start&gt;&lt;issue_comment&gt;Title: Support truthy-value shorthand for filters, like [foo]\\nuserna...</td></tr>\n", "<tr><td>4</td><td>&lt;issue_start&gt;&lt;issue_comment&gt;Title: `create` fails on Ruby 2.0.0-preview2\\nusername_0: ~ github cr...</td></tr>\n", "<tr><td>5</td><td>&lt;issue_start&gt;&lt;issue_comment&gt;Title: Android 2.3 support\\nusername_0: The HTML 5 File API is not su...</td></tr>\n", "<tr><td>6</td><td>&lt;issue_start&gt;&lt;issue_comment&gt;Title: Downloading assets in development mode takes a long time\\nuser...</td></tr>\n", "<tr><td>7</td><td>&lt;issue_start&gt;&lt;issue_comment&gt;Title: Bo<PERSON> error, &quot;Forwarded port &#x27;xxxx&#x27; (host port) is declared mu...</td></tr>\n", "<tr><td>8</td><td>&lt;issue_start&gt;&lt;issue_comment&gt;Title: Gallery Style Notebook Links\\nusername_0: Two things\\n1. We sh...</td></tr>\n", "<tr><td>9</td><td>&lt;issue_start&gt;&lt;issue_comment&gt;Title: ScreenShot Android without screenshooter\\nusername_0: I am fac...</td></tr>\n", "</table>\n"], "text/plain": ["+---+----------------------------------------------------------------------------------------------------+\n", "| id|                                                                                             content|\n", "+---+----------------------------------------------------------------------------------------------------+\n", "|  0|<issue_start><issue_comment>Title: Simple File Silo: Errors aren't submitted to message system\\nu...|\n", "|  1|<issue_start><issue_comment>Title: Asset Pipeline ignores assets.compress config option\\nusername...|\n", "|  2|<issue_start><issue_comment>Title: Initializing Schema Migrations with \"dbo.\" in table prefix bre...|\n", "|  3|<issue_start><issue_comment>Title: Support truthy-value shorthand for filters, like [foo]\\nuserna...|\n", "|  4|<issue_start><issue_comment>Title: `create` fails on Ruby 2.0.0-preview2\\nusername_0: ~ github cr...|\n", "|  5|<issue_start><issue_comment>Title: Android 2.3 support\\nusername_0: The HTML 5 File API is not su...|\n", "|  6|<issue_start><issue_comment>Title: Downloading assets in development mode takes a long time\\nuser...|\n", "|  7|<issue_start><issue_comment>Title: <PERSON><PERSON> error, \"Forwarded port 'xxxx' (host port) is declared mu...|\n", "|  8|<issue_start><issue_comment>Title: Gallery Style Notebook Links\\nusername_0: Two things\\n1. We sh...|\n", "|  9|<issue_start><issue_comment>Title: ScreenShot Android without screenshooter\\nusername_0: I am fac...|\n", "+---+----------------------------------------------------------------------------------------------------+"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# For example, github issues\n", "issues = spark.read.parquet(f'{starcoder_base}/lang=github-issues-filtered-structured/*.parquet')\n", "print('Schema:', issues.schema)\n", "unnest_struct(issues.limit(10))"]}, {"cell_type": "code", "execution_count": 6, "id": "ca1aa733-3f2e-4552-a36f-56412f7f4d9a", "metadata": {"execution": {"iopub.execute_input": "2023-05-24T23:05:11.479843Z", "iopub.status.busy": "2023-05-24T23:05:11.478194Z", "iopub.status.idle": "2023-05-24T23:05:11.964904Z", "shell.execute_reply": "2023-05-24T23:05:11.963076Z", "shell.execute_reply.started": "2023-05-24T23:05:11.479774Z"}, "tags": []}, "outputs": [], "source": ["spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "f9d3fdc3-82c9-4ba8-b9ee-7055403dfcec", "metadata": {}, "source": ["### Step 1. Configure Spark and load data\n", "\n", "Get the Starcoder raw files, which is hive partitioned by language.  Here we map the Hive partitions \n", "automatically to column names. \n"]}, {"cell_type": "code", "execution_count": null, "id": "6b9e9b64-a2e1-4851-9fc0-c5e0c7c184bf", "metadata": {"tags": []}, "outputs": [], "source": ["import os\n", "import sys\n", "home = os.environ['HOME']\n", "sys.path.append(home)\n", "sys.path.append(f'{home}/augment/research')\n", "\n", "from pyspark.sql import SparkSession\n", "import pyspark.sql.functions as F\n", "\n", "from util import get_splitter_udf, get_dataset_writer, merge_indexes, tokenize_udf\n", "\n", "# The cluster already have things preconfigured so just load defaults here\n", "spark = SparkSession.builder.appName(\"starcoder_pipeline\").getOrCreate()\n", "spark.sparkContext.setJobDescription('Loading Starcoder data')\n", "\n", "starcoder_base = 's3a://starcoder/raw'\n", "# Note: basePath is necessary for the parquet reader to understand the files are structured in a partitioned format\n", "raw = spark.read.option(\"basePath\", starcoder_base).parquet(starcoder_base)"]}, {"cell_type": "markdown", "id": "0c75a14a-f43b-4865-9bea-13fa8a0d964d", "metadata": {}, "source": ["### Step 2. Chunking large files\n", "\n", "Some source files are very very large.  However they actualy do correspond to decent amount of code (files 1MB or larger consist of 369MB of java, 280MB of c++, 336MB of C, 40MB of Python etc), so \n", "we do not want to just throw them away even at fairly long tails.\n", "\n", "Here we divide all files into large and small ones, with a threshold of 50k.  Files larger than 50k are sliced into ~50k chunks."]}, {"cell_type": "code", "execution_count": null, "id": "294f8e37-c2bb-4590-ab09-fdf84f3a3bc5", "metadata": {"execution": {"iopub.execute_input": "2023-06-05T03:40:03.038593Z", "iopub.status.busy": "2023-06-05T03:40:03.037982Z", "iopub.status.idle": "2023-06-05T05:09:44.680698Z", "shell.execute_reply": "2023-06-05T05:09:44.671909Z", "shell.execute_reply.started": "2023-06-05T03:40:03.038544Z"}, "tags": []}, "outputs": [], "source": ["SIZE_LIMIT = 50000\n", "spark.sparkContext.setJobDescription('Create chunked dataset')\n", "\n", "large_files = raw.filter(F.length(F.col('content')) > SIZE_LIMIT).withColumnRenamed('id', 'file_id')\n", "small_files = raw.filter(F.length(F.col('content')) <= SIZE_LIMIT).withColumnRenamed('id', 'file_id')\n", "\n", "splitter_udf = get_splitter_udf(chunk_size=SIZE_LIMIT)\n", "\n", "# Large files need to be splitted to size of about the size of SIZE_LIMIT\n", "splitted = large_files.repartition(2000).withColumn(\n", "    'chunks', splitter_udf('content')\n", ").select(\n", "    'lang', 'file_id', 'max_stars_repo_name', 'max_stars_repo_path', 'max_stars_count',\n", "    <PERSON>.posexplode('chunks').alias('chunk_pos', 'chunk'),\n", "    F.size('chunks').alias('num_chunks'),\n", ").filter(\n", "    F.length('chunk') < 2 * SIZE_LIMIT\n", ")\n", "\n", "# Small files don't need anything.  just tag the chunk index as 0\n", "small_files = small_files.select(\n", "    'lang', 'file_id', 'max_stars_repo_name', 'max_stars_repo_path', 'max_stars_count',\n", "    F.lit(0).alias('chunk_pos'),\n", "    <PERSON>.col('content').alias('chunk'),\n", "    F.lit(1).alias('num_chunks'),\n", ")\n", "splitted.union(small_files).repartition(1500).write.mode('overwrite').parquet('s3a://starcoder/chunked/')"]}, {"cell_type": "markdown", "id": "fc9a0b00-b1cc-42c8-a909-0d297da9708a", "metadata": {}, "source": ["### Step 3. Tokenization\n", "\n", "Tokenize each chunk and make sure to put the `<|endoftext|>` token at the end of each chunk.  \n", "Here we create a Pandas UDF to perform tokenization.  This batches a few hundred rows per Python \n", "wrapper call to minimize the communication overhead associated with UDFs.\n", "\n", "By default Pandas UDFs batch 10,000 rows which can be too much for memory consumption, because it increase \n", "the chance that many rows in the batch might be large.  Here we tune it down to 500, and also \n", "re-configures spark to use 2 CPUs per task because we are bounded by memory to CPU ratios."]}, {"cell_type": "code", "execution_count": null, "id": "64761b59-688a-4fbf-8398-cdefe339697e", "metadata": {"execution": {"iopub.execute_input": "2023-06-05T05:09:44.697146Z", "iopub.status.busy": "2023-06-05T05:09:44.696530Z", "iopub.status.idle": "2023-06-05T05:09:46.042099Z", "shell.execute_reply": "2023-06-05T05:09:46.040339Z", "shell.execute_reply.started": "2023-06-05T05:09:44.697081Z"}, "tags": []}, "outputs": [], "source": ["spark.stop()\n", "spark = SparkSession.builder.appName(\"starcoder_pipeline\").config(\n", "    'spark.sql.execution.arrow.maxRecordsPerBatch', 500\n", ").config(\n", "    'spark.task.cpus', 2\n", ").getOrCreate()"]}, {"cell_type": "code", "execution_count": null, "id": "611a196b-ba57-4c74-bc0a-bf275eaade96", "metadata": {"execution": {"iopub.execute_input": "2023-06-05T06:18:41.372150Z", "iopub.status.busy": "2023-06-05T06:18:41.371081Z"}, "tags": []}, "outputs": [], "source": ["spark.sparkContext.setJobDescription(f'Tokenize all languages')\n", "spark.read.parquet(\n", "    's3a://starcoder/chunked/'\n", ").withColumn(\n", "    'tokenized', tokenize_udf('chunk')\n", ").write.mode(\n", "    'overwrite'\n", ").parquet('s3a://starcoder/tokenized/')"]}, {"cell_type": "markdown", "id": "c810ac64-ac96-435a-89b0-c86c836f8c3f", "metadata": {}, "source": ["Count total number of tokens out there, just to be sure\n", "\n", "don't need the extra memory per core any more, so switch it back"]}, {"cell_type": "code", "execution_count": null, "id": "808eccb5-6acf-4595-ae36-becaaafe9bce", "metadata": {"execution": {"iopub.execute_input": "2023-06-05T18:33:35.243847Z", "iopub.status.busy": "2023-06-05T18:33:35.242419Z", "iopub.status.idle": "2023-06-05T18:43:15.455640Z", "shell.execute_reply": "2023-06-05T18:43:15.453581Z", "shell.execute_reply.started": "2023-06-05T18:33:35.243750Z"}, "tags": []}, "outputs": [], "source": ["spark.stop()\n", "spark = SparkSession.builder.appName(\"starcoder_pipeline\").config(\n", "    'spark.task.cpus', 1\n", ").getOrCreate()\n", "\n", "stats = spark.read.parquet(\n", "    's3a://starcoder/tokenized/'\n", ").agg(F.sum(F.size('tokenized')).alias('total_tokens'), F.count('*').alias('total_documents')).first()\n", "\n", "print('Stats for tokenized dataset: ', stats)  # expect 278G tokens just over 207M docs (starcoder raw has 206642239 files)"]}, {"cell_type": "markdown", "id": "4608f994-f115-47e4-a248-2f056407c5d3", "metadata": {}, "source": ["### Step 4. Save each partition to index files"]}, {"cell_type": "code", "execution_count": null, "id": "9f693312-6708-44bc-81be-2a8c49515b48", "metadata": {"tags": []}, "outputs": [], "source": ["spark.sparkContext.setJobDescription(f'Create index files for all languages')\n", "writer = get_dataset_writer(\n", "    output_path='indexed-train',\n", "    output_bucket='starcoder',\n", "    filename_pattern='starcoder-{partition_id:05d}',\n", ")\n", "bin_files = spark.read.parquet(\n", "    's3a://starcoder/tokenized/'\n", ").select('tokenized', <PERSON><PERSON>hash(<PERSON><PERSON>col('chunk')).alias('chunk_hash')).repartition(\n", "    3000, 'chunk_hash'\n", ").sortWithinPartitions(\n", "    'chunk_hash'\n", ").select('tokenized').rdd.mapPartitionsWithIndex(writer).collect()"]}, {"cell_type": "markdown", "id": "42724248-5e22-4d62-b949-20e444c099af", "metadata": {}, "source": ["### Step 5. Combine indexes\n", "\n", "Combine the large number of bin and idx files into one"]}, {"cell_type": "code", "execution_count": null, "id": "90e4dd26-ec43-41cd-b0a5-e3c34f5d1500", "metadata": {"execution": {"iopub.execute_input": "2023-06-06T00:14:42.507767Z", "iopub.status.busy": "2023-06-06T00:14:42.507043Z", "iopub.status.idle": "2023-06-06T02:15:24.656114Z", "shell.execute_reply": "2023-06-06T02:15:24.654337Z", "shell.execute_reply.started": "2023-06-06T00:14:42.507712Z"}, "tags": []}, "outputs": [], "source": ["%%capture\n", "merge_indexes(\n", "    bin_files,\n", "    bucket='starcoder',\n", "    output_path='indexed-train-combined',\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "37246160-2842-4717-a796-ec873c567c6d", "metadata": {"execution": {"iopub.execute_input": "2023-06-06T07:14:47.691809Z", "iopub.status.busy": "2023-06-06T07:14:47.691077Z", "iopub.status.idle": "2023-06-06T07:14:47.715203Z", "shell.execute_reply": "2023-06-06T07:14:47.713475Z", "shell.execute_reply.started": "2023-06-06T07:14:47.691752Z"}, "tags": []}, "outputs": [], "source": ["spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 5}
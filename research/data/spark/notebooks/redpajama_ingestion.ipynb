{"cells": [{"cell_type": "markdown", "id": "e752ae3c-2b06-4568-a934-792d70bfc1a9", "metadata": {}, "source": ["### Ingestion of Red Pajama data\n", "\n", "Mainly, we convert Red Pajama data from very big JSON files into partitioned parquet for easier processing later"]}, {"cell_type": "code", "execution_count": 1, "id": "7b870d84-8777-4d63-8971-af37c072beb4", "metadata": {"execution": {"iopub.execute_input": "2023-05-23T15:10:23.399805Z", "iopub.status.busy": "2023-05-23T15:10:23.398576Z", "iopub.status.idle": "2023-05-23T15:10:34.558528Z", "shell.execute_reply": "2023-05-23T15:10:34.556491Z", "shell.execute_reply.started": "2023-05-23T15:10:23.399721Z"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/05/23 15:10:28 WARN Utils: Your hostname, xiaolei-dev-cpu-playground resolves to a loopback address: *********; using ************* instead (on interface enp3s0)\n", "23/05/23 15:10:28 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n"]}], "source": ["from pyspark.sql import SparkSession\n", "import pyspark.sql.functions as sp\n", "\n", "# get spark monitoring if available\n", "try:\n", "    import sparkmonitor\n", "    monitor_path = sparkmonitor.__path__[0]\n", "except:\n", "    monitor_path = None\n", "\n", "spark = SparkSession.builder.appName(\"redpajama_ingestion\").config(\n", "        'spark.sql.shuffle.partitions', 1000\n", "    )\n", "\n", "if monitor_path:\n", "    spark = spark.config(\n", "        'spark.extraListeners', 'sparkmonitor.listener.JupyterSparkMonitorListener'\n", "    ).config(\n", "        'spark.driver.extraClassPath', f'{monitor_path}/listener.jar'\n", "    )\n", "\n", "spark = spark.getOrCreate()"]}, {"cell_type": "code", "execution_count": 2, "id": "f3f7012e-e67a-4773-ae55-1412fd430fd0", "metadata": {"execution": {"iopub.execute_input": "2023-05-23T15:10:34.588006Z", "iopub.status.busy": "2023-05-23T15:10:34.587377Z", "iopub.status.idle": "2023-05-23T15:10:37.074581Z", "shell.execute_reply": "2023-05-23T15:10:37.071898Z", "shell.execute_reply.started": "2023-05-23T15:10:34.587934Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current settings for max partition size:  200m\n"]}], "source": ["print('Current settings for max partition size: ', spark.conf.get(\"spark.sql.files.maxPartitionBytes\"))"]}, {"cell_type": "markdown", "id": "bbf31391-83e3-4e09-897f-ec6a8543f337", "metadata": {}, "source": ["#### Basic settings\n", "\n", "Here configure the raw Red Pajama dump file location (they should be jsonl or compressed jsonl files) \n", "and the subsets contained within."]}, {"cell_type": "code", "execution_count": 3, "id": "d709b13c-5b8f-4dba-92aa-e17770280294", "metadata": {"execution": {"iopub.execute_input": "2023-05-23T15:10:37.088688Z", "iopub.status.busy": "2023-05-23T15:10:37.087662Z", "iopub.status.idle": "2023-05-23T15:10:37.099217Z", "shell.execute_reply": "2023-05-23T15:10:37.094710Z", "shell.execute_reply.started": "2023-05-23T15:10:37.088621Z"}, "tags": []}, "outputs": [], "source": ["redpajama_base = 's3a://redpajama/raw'\n", "folders = ['arxiv', 'book', 'c4', 'github', 'stackexchange', 'wikipedia']\n", "output_path = 's3a://redpajama/warehouse'"]}, {"cell_type": "code", "execution_count": null, "id": "4ddce92b-7aa0-4bfc-b354-b192928f2b32", "metadata": {"editable": true, "execution": {"iopub.execute_input": "2023-05-22T22:36:29.175906Z", "iopub.status.busy": "2023-05-22T22:36:29.173054Z"}, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Working on book\n"]}, {"name": "stderr", "output_type": "stream", "text": ["23/05/22 22:36:29 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["  Number of partitions: 515\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Working on github\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["  Number of partitions: 1102\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 4:================>                                    (344 + 30) / 1102]\r"]}], "source": ["\n", "for item in folders:\n", "    print(f'Working on {item}')\n", "    df = spark.read.json(f'{redpajama_base}/{item}/')\n", "    print(f'  Number of partitions: {df.rdd.getNumPartitions()}')\n", "    df.write.mode('overwrite').parquet(f'{output_path}/{item}/')"]}, {"cell_type": "markdown", "id": "0b2e8427-a292-4b19-99a4-3eb87f500d68", "metadata": {}, "source": ["### Now work on common crawl"]}, {"cell_type": "code", "execution_count": null, "id": "484afff0-4eff-44bd-8b24-5a8815fe8c4c", "metadata": {"editable": true, "execution": {"iopub.execute_input": "2023-05-23T15:10:37.110463Z", "iopub.status.busy": "2023-05-23T15:10:37.108520Z"}, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Working on dump 2023-06\n", "   part 0 kind head\n"]}, {"name": "stderr", "output_type": "stream", "text": ["23/05/23 15:10:37 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 0 kind middle\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 1 kind head\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 1 kind middle\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 2 kind head\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 17:>                                                         (0 + 7) / 7]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 3 kind head\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 3 kind middle\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 4 kind head\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 33:>                                                         (0 + 7) / 7]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 5 kind head\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 5 kind middle\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 6 kind middle\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 7 kind head\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 7 kind middle\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 8 kind head\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 8 kind middle\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 9 kind head\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 9 kind middle\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Working on dump 2022-05\n", "   part 0 kind head\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 0 kind middle\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 1 kind middle\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 2 kind head\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 2 kind middle\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 3 kind head\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 3 kind middle\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 4 kind head\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 4 kind middle\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 5 kind head\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 5 kind middle\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 6 kind head\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 6 kind middle\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 7 kind head\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["   part 7 kind middle\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 143:>                                                      (0 + 30) / 90]\r"]}], "source": ["# Spark cannot auto partition input json file if it is compressed.  We have to do its job for it\n", "dumps = ['2023-06', '2022-05', '2021-04', '2020-05', '2019-30']\n", "# Make sure to increase the number of partitions\n", "for dump in dumps:\n", "    print(f'Working on dump {dump}')\n", "    # divide each file into 10 partitions\n", "    for part in range(10):\n", "        for kind in ('head', 'middle'):\n", "            print(f'   part {part} kind {kind}')\n", "            df = spark.read.json(f'{redpajama_base}/common_crawl/{dump}/en_{kind}_*{part}.json.gz.dedup.classifier.jsonl.zst')\n", "            df = df.repartition(df.rdd.getNumPartitions() * 10)\n", "            df.write.mode('overwrite').parquet(f'{output_path}/common_crawl/{dump}/{kind}-group-{part:02d}/')"]}, {"cell_type": "markdown", "id": "f48c571c-316a-4637-9b2e-4e2e82cdc4d1", "metadata": {}, "source": ["### Some basic stats and verifications\n", "\n", "Just check the scale to verify that conversion is complete.  Also a quick comparison for the performance of the JSONL and Parquet formats. (Spoiler: JSONL is bad)"]}, {"cell_type": "code", "execution_count": 10, "id": "a4cef879-6bf8-4128-80b4-03c72cf735e6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 35:=====================================================>(173 + 2) / 175]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["JSON row count:  111402716\n", "CPU times: user 1.21 s, sys: 258 ms, total: 1.46 s\n", "Wall time: 20min 47s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["%%time\n", "df_json = spark.read.json(f'{redpajama_base}/common_crawl/{dump}/')\n", "print('JSON row count: ', df_json.count())"]}, {"cell_type": "code", "execution_count": 6, "id": "49e5c523-fd10-4949-adb0-bb2197e49839", "metadata": {"execution": {"iopub.execute_input": "2023-05-23T02:23:58.771527Z", "iopub.status.busy": "2023-05-23T02:23:58.770673Z", "iopub.status.idle": "2023-05-23T02:24:13.594439Z", "shell.execute_reply": "2023-05-23T02:24:13.592066Z", "shell.execute_reply.started": "2023-05-23T02:23:58.771452Z"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 12:===================================================>(1493 + 1) / 1494]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["parquet row count:  111402716\n", "CPU times: user 3.63 s, sys: 739 ms, total: 4.37 s\n", "Wall time: 14.8 s\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["%%time\n", "df_pq = spark.read.parquet(f'{output_path}/common_crawl/{dump}/')\n", "print('parquet row count: ', df_pq.count())"]}, {"cell_type": "code", "execution_count": 14, "id": "0c3645ab-e28d-4ba2-82ec-290c1eda392a", "metadata": {"execution": {"iopub.execute_input": "2023-05-23T15:08:08.802690Z", "iopub.status.busy": "2023-05-23T15:08:08.801842Z", "iopub.status.idle": "2023-05-23T15:08:09.265775Z", "shell.execute_reply": "2023-05-23T15:08:09.263704Z", "shell.execute_reply.started": "2023-05-23T15:08:08.802614Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "SPARKMONITOR_LISTENER: Exception sending socket message:java.io.IOException: Stream closed\n", "\n", "\n", "SPARKMONITOR_LISTENER: Exception sending socket message:java.io.IOException: Stream closed\n", "\n"]}], "source": ["spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 5}
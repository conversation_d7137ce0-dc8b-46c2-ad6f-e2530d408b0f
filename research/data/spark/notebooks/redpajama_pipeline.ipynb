{"cells": [{"cell_type": "markdown", "id": "f9d3fdc3-82c9-4ba8-b9ee-7055403dfcec", "metadata": {}, "source": ["### Step 1. Configure Spark and load data\n", "\n", "Process subsets of Red Pajama: `arxiv`, `wikipedia`, `book` and `stackexchange`"]}, {"cell_type": "code", "execution_count": 2, "id": "6b9e9b64-a2e1-4851-9fc0-c5e0c7c184bf", "metadata": {"execution": {"iopub.execute_input": "2023-06-08T23:05:16.125874Z", "iopub.status.busy": "2023-06-08T23:05:16.124753Z", "iopub.status.idle": "2023-06-08T23:05:17.565465Z", "shell.execute_reply": "2023-06-08T23:05:17.563489Z", "shell.execute_reply.started": "2023-06-08T23:05:16.125819Z"}, "tags": []}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "import pyspark.sql.functions as F\n", "\n", "from util import get_splitter_udf, get_dataset_writer, merge_indexes, tokenize_udf\n", "\n", "# The cluster already have things preconfigured so just load defaults here\n", "spark = SparkSession.builder.appName(\"redpajama_pipeline\").config(\n", "    'spark.sql.execution.arrow.maxRecordsPerBatch',   500\n", ").config(\n", "    'spark.task.cpus', 2\n", ").getOrCreate()"]}, {"cell_type": "markdown", "id": "0c75a14a-f43b-4865-9bea-13fa8a0d964d", "metadata": {}, "source": ["### Step 2. Define workflow\n", "\n", "For each subset, the workflow is to chunk, repartition, then tokenize"]}, {"cell_type": "code", "execution_count": 2, "id": "294f8e37-c2bb-4590-ab09-fdf84f3a3bc5", "metadata": {"execution": {"iopub.execute_input": "2023-06-08T04:56:01.405390Z", "iopub.status.busy": "2023-06-08T04:56:01.405139Z", "iopub.status.idle": "2023-06-08T04:56:01.415037Z", "shell.execute_reply": "2023-06-08T04:56:01.414139Z", "shell.execute_reply.started": "2023-06-08T04:56:01.405360Z"}, "tags": []}, "outputs": [], "source": ["def tokenize_subset(subset, size_limit=50000, partitions=1000, content_column='text', bucket='redpajama'):\n", "    \"\"\"Workflow to tokenize a subset of redpajama\"\"\"\n", "    spark.sparkContext.setJobDescription(f'Loading redpajama subset {subset}')\n", "    df = spark.read.parquet(f's3a://redpajama/warehouse/{subset}/')\n", "\n", "    spark.sparkContext.setJobDescription(f'Tokenizing redpajama subset {subset}')\n", "    outpath = f'tokenized/subset={subset}/'\n", "    tokenize_df(df, outpath, size_limit=size_limit, partitions=partitions, content_column=content_column, bucket=bucket)\n", "\n", "\n", "def tokenize_df(df, output_path, size_limit=50000, partitions=1000, content_column='text', bucket='redpajama'):\n", "    \"\"\"Workflow to tokenize a subset of redpajama\"\"\"\n", "    splitter_udf = get_splitter_udf(chunk_size=size_limit)\n", "\n", "\n", "    # Large files need to be splitted to size of about the size_limit\n", "    splitted = df.select(content_column).repartition(partitions).withColumn(\n", "        'chunks', splitter_udf(content_column)\n", "    ).select(\n", "        <PERSON><PERSON>explode('chunks').alias('chunk'),\n", "    ).filter(\n", "        F.length('chunk') < 2 * size_limit\n", "    ).repartition(partitions).withColumn(\n", "        'chunk_hash',\n", "        F.md5(<PERSON><PERSON>col('chunk')),\n", "    )\n", "    \n", "    # Then tokenize it\n", "    tokenized = splitted.withColumn(\n", "        'tokenized', tokenize_udf('chunk')\n", "    ).write.mode(\n", "        'overwrite'\n", "    ).parquet(f's3a://{bucket}/{output_path}')"]}, {"cell_type": "markdown", "id": "fc9a0b00-b1cc-42c8-a909-0d297da9708a", "metadata": {}, "source": ["### Step 3. Tokenize each set\n", "\n", "Tokenize each subset one by one"]}, {"cell_type": "code", "execution_count": null, "id": "64761b59-688a-4fbf-8398-cdefe339697e", "metadata": {"execution": {"iopub.execute_input": "2023-06-07T23:55:04.175183Z", "iopub.status.busy": "2023-06-07T23:55:04.174463Z"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/06/07 23:55:05 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "[Stage 6:>                                                      (0 + 45) / 1000]\r"]}], "source": ["for subset in ['stackexchange']:\n", "    tokenize_subset(subset)\n", "    "]}, {"cell_type": "markdown", "id": "bb085785-a918-47c8-9d1c-47879b88b9e8", "metadata": {}, "source": ["### Step 4. Count tokens of each set"]}, {"cell_type": "code", "execution_count": 5, "id": "947115a2-bd9b-48cf-a1d8-8abb88316197", "metadata": {"execution": {"iopub.execute_input": "2023-06-08T02:37:13.782149Z", "iopub.status.busy": "2023-06-08T02:37:13.779529Z", "iopub.status.idle": "2023-06-08T02:41:46.456804Z", "shell.execute_reply": "2023-06-08T02:41:46.455452Z", "shell.execute_reply.started": "2023-06-08T02:37:13.782064Z"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["path = 's3a://redpajama/tokenized/'\n", "df = spark.read.option('basePath', path).parquet(path)\n", "stats = df.groupBy('subset').agg(F.count('*').alias('items'), F.sum(F.length('chunk')).alias('total_size')).toPandas()"]}, {"cell_type": "code", "execution_count": 7, "id": "e3be8973-1f90-45cd-98cd-8dd89edd971d", "metadata": {"execution": {"iopub.execute_input": "2023-06-08T02:53:46.905721Z", "iopub.status.busy": "2023-06-08T02:53:46.905049Z", "iopub.status.idle": "2023-06-08T02:53:46.923832Z", "shell.execute_reply": "2023-06-08T02:53:46.921939Z", "shell.execute_reply.started": "2023-06-08T02:53:46.905672Z"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>subset</th>\n", "      <th>items</th>\n", "      <th>total_size</th>\n", "      <th>tokens_GT</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>book</td>\n", "      <td>2181321</td>\n", "      <td>104453595129</td>\n", "      <td>26.113399</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>wikipedia</td>\n", "      <td>29900042</td>\n", "      <td>72079648546</td>\n", "      <td>18.019912</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>arxiv</td>\n", "      <td>2521776</td>\n", "      <td>88118980394</td>\n", "      <td>22.029745</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>stackexchange</td>\n", "      <td>21652507</td>\n", "      <td>51644435086</td>\n", "      <td>12.911109</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          subset     items    total_size  tokens_GT\n", "0           book   2181321  104453595129  26.113399\n", "1      wikipedia  29900042   72079648546  18.019912\n", "2          arxiv   2521776   88118980394  22.029745\n", "3  stackexchange  21652507   51644435086  12.911109"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["stats['tokens_GT'] = stats['total_size']/4e9\n", "stats"]}, {"cell_type": "markdown", "id": "66ede584-31fe-4132-bdf9-365c5ac0782a", "metadata": {"execution": {"iopub.execute_input": "2023-06-08T04:56:01.422238Z", "iopub.status.busy": "2023-06-08T04:56:01.422033Z", "iopub.status.idle": "2023-06-08T04:56:18.487182Z", "shell.execute_reply": "2023-06-08T04:56:18.485335Z", "shell.execute_reply.started": "2023-06-08T04:56:01.422218Z"}, "tags": []}, "source": ["### Step 5. Tokenize code langauges in Stack that are not in Starcoder"]}, {"cell_type": "code", "execution_count": 8, "id": "dfaeb0b9-b137-4b67-af6b-873a2b761c16", "metadata": {"execution": {"iopub.execute_input": "2023-06-08T04:59:30.094290Z", "iopub.status.busy": "2023-06-08T04:59:30.093620Z", "iopub.status.idle": "2023-06-08T06:07:36.260288Z", "shell.execute_reply": "2023-06-08T06:07:36.256508Z", "shell.execute_reply.started": "2023-06-08T04:59:30.094238Z"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["stack_path = 's3a://the-stack-dedupe/'\n", "stack = spark.read.option('basePath', stack_path).parquet(stack_path)\n", "\n", "noncode = [\n", "    'json', 'html', 'csv', 'text',\n", "    'xml', 'markdown', 'svg',\n", "    'yaml', 'css', 'gettext-catalog',\n", "    'unity3d-asset', 'restructuredtext',\n", "    'ini',\n", "    'asciidoc',\n", "]\n", "extra = ['jupyter-notebook', 'c++']\n", "\n", "starcoder_base = 's3a://starcoder/raw'\n", "starcoder_lang = spark.read.option(\"basePath\", starcoder_base).parquet(starcoder_base).select('lang').distinct().collect()\n", "starcoder_lang = [row.lang for row in starcoder_lang]\n", "\n", "stack = stack.select('langpart', 'content').filter(~F.col('langpart').isin(noncode+extra+starcoder_lang))\n", "\n", "# print(stack.select('langpart').distinct().collect())\n", "tokenize_df(stack, 'stack-lang-tokenized', size_limit=50000, partitions=1000, content_column='content', bucket='starcoder')\n"]}, {"cell_type": "markdown", "id": "85e55757-14ce-4a9f-98ba-bdc614e80039", "metadata": {}, "source": ["### Step 6. Combine source to get StarCoder+\n", "\n", "Get these different sources and mix with starcoder to get starcoder+.  389G Tokens (about 100G more than StarCoder)"]}, {"cell_type": "code", "execution_count": 2, "id": "8d3b42cf-35db-4a77-9a47-49491c0f6169", "metadata": {"execution": {"iopub.execute_input": "2023-06-08T08:07:47.800020Z", "iopub.status.busy": "2023-06-08T08:07:47.799182Z", "iopub.status.idle": "2023-06-08T08:08:05.150603Z", "shell.execute_reply": "2023-06-08T08:08:05.148674Z", "shell.execute_reply.started": "2023-06-08T08:07:47.799943Z"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/06/08 08:07:49 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "                                                                                \r"]}], "source": ["from_stack = spark.read.parquet('s3a://starcoder/stack-lang-tokenized/').select('tokenized', 'chunk_hash')\n", "from_rpj = spark.read.parquet(\n", "    's3a://redpajama/tokenized/subset=arxiv/',\n", "    's3a://redpajama/tokenized/subset=stackexchange/',\n", "    's3a://redpajama/tokenized/subset=wikipedia/'\n", ").select('tokenized', 'chunk_hash')\n", "df = spark.read.parquet(\n", "    's3a://starcoder/tokenized/'\n", ").select(\n", "    'tokenized',\n", "    F.hash(F.col('chunk')).alias('chunk_hash')\n", ").union(from_rpj).union(from_stack)"]}, {"cell_type": "code", "execution_count": 3, "id": "bfe4bee8-56e1-49eb-bcf5-dc288c90161f", "metadata": {"execution": {"iopub.execute_input": "2023-06-08T08:08:18.236623Z", "iopub.status.busy": "2023-06-08T08:08:18.235892Z", "iopub.status.idle": "2023-06-08T09:38:09.798168Z", "shell.execute_reply": "2023-06-08T09:38:09.795879Z", "shell.execute_reply.started": "2023-06-08T08:08:18.236569Z"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["spark.sparkContext.setJobDescription(f'Create index files for augmented StarCoder+')\n", "writer = get_dataset_writer(\n", "    output_path='indexed',\n", "    output_bucket='starcoder-plus',\n", "    filename_pattern='starcoder-p-{partition_id:05d}',\n", ")\n", "bin_files = df.repartition(\n", "    5000, 'chunk_hash'\n", ").sortWithinPartitions(\n", "    'chunk_hash'\n", ").select('tokenized').rdd.mapPartitionsWithIndex(writer).collect()"]}, {"cell_type": "code", "execution_count": 1, "id": "2b20f4b7-2212-4b5b-873d-81a96391196d", "metadata": {"execution": {"iopub.execute_input": "2023-06-08T21:23:32.920311Z", "iopub.status.busy": "2023-06-08T21:23:32.918953Z", "iopub.status.idle": "2023-06-08T21:23:33.229652Z", "shell.execute_reply": "2023-06-08T21:23:33.227512Z", "shell.execute_reply.started": "2023-06-08T21:23:32.920224Z"}, "tags": []}, "outputs": [{"ename": "NameError", "evalue": "name 'merge_indexes' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mmerge_indexes\u001b[49m(\n\u001b[1;32m      2\u001b[0m     bin_files,\n\u001b[1;32m      3\u001b[0m     bucket\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mstarcoder-plus\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[1;32m      4\u001b[0m     output_path\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mindexed-train-combined\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[1;32m      5\u001b[0m )\n", "\u001b[0;31mNameError\u001b[0m: name 'merge_indexes' is not defined"]}], "source": ["merge_indexes(\n", "    bin_files,\n", "    bucket='starcoder-plus',\n", "    output_path='indexed-train',\n", "    wordir='/mnt/efs/augment-lga1/data/processed/starcoder+stack+rpj/',\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 5}
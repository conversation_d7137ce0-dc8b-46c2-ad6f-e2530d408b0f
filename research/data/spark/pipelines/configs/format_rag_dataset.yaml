global:
  data_processing_root: /tmp/

stages:
  # Apply prompt formatter and convert rag samples to indexed dataset
  - name: format_rag_samples_into_dataset

    # input: sparktest/retrievalintermediate
    # output: sparktest/retrievaldataset
    input: /mnt/efs/augment-lga1/data/processed/rag/samples/small_python/
    output: /mnt/efs/augment/data/processed/rag/dataset/small_python_addpath/

    # Desired number of tokens in each prompt
    seq_len: 4097

    # How to divide tokens between different components of the prompt
    # Leaving 256 tokens room for middle
    max_prefix_tokens: 1536
    max_suffix_tokens: 256
    max_retrieved_chunk_tokens: 2048
    preamble: ""
    prepend_path_to_retrieved: true

    # Convert this column to indexed dataset
    samples_column: "prompt_tokens"
    num_validation_samples: 10000
    random_seed: 74912


spark:
  app_name: "FormatRagToDataset"
  log_level: WARN
  master_url: "spark://michiel-dataprocess:7077"
  # TODO we're giving every executor lots of memory, see if we can reduce it
  # total-mem = executor_instances * (executor_mem + executor_mem_overhead)
  #   + driver_mem + driver_mem_overhead

  spark.executor.memory: "10g"
  spark.executor.memoryOverhead: "6g"
  spark.executor.instances: "12"

  spark.executor.cores: "5"
  spark.driver.memory: "20g"
  spark.driver.memoryOverhead: "6g"
  spark.driver.maxResultSize: "2g"
  spark.sql.debug.maxToStringFields: "4096"
  spark.serializer: "org.apache.spark.serializer.KryoSerializer"
  spark.sql.files.maxPartitionBytes: "1g"
  # This is trying to solve a memory problem with tokenizing the whole dataset
  spark.sql.parquet.enableVectorizedReader: false
  spark.sql.parquet.columnarReaderBatchSize: "2048" # default 4096

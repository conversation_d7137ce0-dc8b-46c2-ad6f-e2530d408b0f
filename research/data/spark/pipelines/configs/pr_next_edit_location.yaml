# Version History
# V5: Uses the wip-sampler on a pre-split train / validation set. This allows us to
#     use a stable validation dataset for evaluation.
# V6: Uses a distance-based negative sampler.
# V7: Uses the "keep_most" strategy
# V8: increases chunk size to 60

global:
  data_processing_root: /mnt/efs/spark-data/user/arun/data-pipeline/pr-next-edit-location-v8-wip-sampler
  tokenizer: starcoder
  is_production_tokenizer: true
  efs_path: /mnt/efs/spark-data
  # tmpdir: /mnt/ephemeral/disk

stages:
  - name: create_next_edit_location_dataset_from_prs
    input: /mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_train
    output: next-edit-location-train

    pr_repo_reference_file: /mnt/efs/spark-data/shared/gh_pr_repo_ref.parquet

    # These are very large repos that cause the pipeline to choke
    skip_repo_names: ["NixOS/nixpkgs", "microsoft/AzureTRE"]

    negative_sampler: distance

    wip_sampler: wip_repo_sampler
    # wip_sampler: diff_subset
    max_prs_per_repo: 10

    max_wip_states_per_pr: 10

    max_samples_per_repo: 1000

    num_context_lines_in_diff: 5
    max_lines_per_chunk: 60

    max_negative_documents_per_sample: 256
    max_positive_documents_per_sample: 32

    add_path_to_prompt: true
    max_query_token_length: 4096
    max_document_token_length: 1000

    # For debugging purposes
    # limit_num_input_parquet_files: 5

  - name: export_indexed_dataset
    input: next-edit-location-train
    output: next-edit-location-train-indexed-dataset
    samples_column: sample_tokens


  - name: create_next_edit_location_dataset_from_prs
    input: /mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_validation
    output: next-edit-location-validation

    pr_repo_reference_file: /mnt/efs/spark-data/shared/gh_pr_repo_ref.parquet

    # These are very large repos that cause the pipeline to choke
    skip_repo_names: ["NixOS/nixpkgs", "microsoft/AzureTRE"]

    negative_sampler: distance

    wip_sampler: wip_repo_sampler
    # wip_sampler: diff_subset
    max_prs_per_repo: 10

    max_wip_states_per_pr: 10

    max_samples_per_repo: 1000

    num_context_lines_in_diff: 5
    max_lines_per_chunk: 30

    max_negative_documents_per_sample: 512
    max_positive_documents_per_sample: 64

    add_path_to_prompt: true
    max_query_token_length: 4096
    max_document_token_length: 1000

    # Save the WIP files just for the validation data.
    save_wip_files: true

    # For debugging purposes
    # limit_num_input_parquet_files: 5

  - name: export_indexed_dataset
    input: next-edit-location-validation
    output: next-edit-location-validation-indexed-dataset
    samples_column: sample_tokens

global:
  data_processing_root: /tmp/

stages:
  # Filter by language and other criteria, and rebalance languages
  - name: make_retrieval_augmented_samples
    languages:
      # - C
      # - C++
      # - Go
      # - Java
      # - JavaScript
      - Python

    input: /mnt/efs/augment-lga1/data/raw/the-stack-dedup.2023-02-04/data/
    # output: sparktest/retrievalintermediate
    # output: /mnt/efs/augment/data/processed/rag/samples/test/
    output: /mnt/efs/augment/data/processed/rag/samples/small_python_lmiddles/

    # Number of repositories to process
    limit_repos: 100000

    # Sets FiM samples per file
    every_n_lines: 150
    max_problems_per_file: null

    # Upweighted range for FiM middle character length
    len_middle_lower: 100
    len_middle_upper: 304
    # Max range for FiM middle char length
    len_middle_max: 304  # Set so that we are guaranteed < 304 tokens
    # Sample middles that end in middle of syntactic leaf for leaves above this length
    min_leaf_len_for_insert: 100

    # Char length of prefix/suffix saved for possible use in prompt
    len_prefix_max: 8000  # 2000 tokens * 4 to be on the safe side
    len_suffix_max: 4000  # 1000 tokens * 4 to be on the safe side

    repo_min_size: null
    repo_max_size: null

    # Retrieval settings
    chunker: scope_aware
    retriever_name: bm25
    num_retrieved_chunks: 25
    random_seed: 74912


spark:
  app_name: "MakeRagSamples"
  log_level: WARN
  # master_url: "k8s://https://k8s.ord1.coreweave.com:443"
  master_url: "spark://michiel-dataprocess:7077"
  # TODO we're giving every executor lots of memory, see if we can reduce it
  # total-mem = executor_instances * (executor_mem + executor_mem_overhead)
  #   + driver_mem + driver_mem_overhead

  spark.executor.memory: "10g"
  spark.executor.memoryOverhead: "6g"
  spark.executor.instances: "12"

  spark.executor.cores: "5"

  spark.driver.memory: "20g"
  spark.driver.memoryOverhead: "6g"
  spark.driver.maxResultSize: "2g"
  spark.sql.debug.maxToStringFields: "4096"
  spark.serializer: "org.apache.spark.serializer.KryoSerializer"
  spark.sql.files.maxPartitionBytes: "1g"
  # This is trying to solve a memory problem with tokenizing the whole dataset
  spark.sql.parquet.enableVectorizedReader: false
  spark.sql.parquet.columnarReaderBatchSize: "2048" # default 4096

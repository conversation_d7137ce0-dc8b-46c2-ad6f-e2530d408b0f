# The Spark configuration part of the pipeline config, for use in scripts
# that use Spark without the full pipeline
spark:
  app_name: "ProcessTheStack"
  log_level: WARN
  master_url: "spark://guy-dev-data-processing-lga1:7077"

  spark.executor.memory: "10g"
  spark.executor.memoryOverhead: "6g"
  spark.executor.instances: "12"

  spark.executor.cores: "5"

  spark.driver.memory: "20g"
  spark.driver.memoryOverhead: "6g"
  spark.driver.maxResultSize: "2g"
  spark.sql.debug.maxToStringFields: "4096"
  spark.serializer: "org.apache.spark.serializer.KryoSerializer"
  spark.sql.files.maxPartitionBytes: "128m"

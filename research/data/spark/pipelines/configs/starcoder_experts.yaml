# Prepare samples for language-specific MoE training

global:
  data_processing_root: /mnt/efs/augment-lga1-nvme/user/guy/data-pipeline/starcoder
  tokenizer: StarCoderTokenizer

stages:
  # Filter by language and other criteria
  - name: filter_and_tokenize

    partition_by: lang

    input: /mnt/efs/augment-lga1-nvme/data/raw/starcoder/raw
    output: tokenized

  # python 4k
  - name: pack_samples
    langs:
      - python

    seq_length: 4096

    # We make each sample one token bigger, so the total size is seq_length + 1.
    # This is because neox uses the first seq_length tokens for the inputs,
    # and the last seq_length tokens for the labels.
    add_one_token: True

    fim_rate: 0.5
    fim_spm_rate: 0
    fim_prefix_token: <fim_prefix>
    fim_suffix_token: <fim_suffix>
    fim_middle_token: <fim_middle>

    input: tokenized
    output: packed_samples_4096_python

  - name: export_indexed_dataset
    samples_column: packed_samples
    num_validation_samples: 10000
    input: packed_samples_4096_python
    output: exported_dataset_4096_python

  # python 8k
  - name: pack_samples
    langs:
      - python

    seq_length: 8192

    # We make each sample one token bigger, so the total size is seq_length + 1.
    # This is because neox uses the first seq_length tokens for the inputs,
    # and the last seq_length tokens for the labels.
    add_one_token: True

    fim_rate: 0.5
    fim_spm_rate: 0
    fim_prefix_token: <fim_prefix>
    fim_suffix_token: <fim_suffix>
    fim_middle_token: <fim_middle>

    input: tokenized
    output: packed_samples_8192_python

  - name: export_indexed_dataset
    samples_column: packed_samples
    num_validation_samples: 10000
    input: packed_samples_8192_python
    output: exported_dataset_8192_python

  # python 2k no-fim
  - name: pack_samples
    langs:
      - python

    seq_length: 2048

    # We make each sample one token bigger, so the total size is seq_length + 1.
    # This is because neox uses the first seq_length tokens for the inputs,
    # and the last seq_length tokens for the labels.
    add_one_token: True

    fim_rate: 0.0
    fim_spm_rate: 0
    fim_prefix_token: <fim_prefix>
    fim_suffix_token: <fim_suffix>
    fim_middle_token: <fim_middle>

    input: tokenized
    output: packed_samples_2048_python_nofim

  - name: export_indexed_dataset
    samples_column: packed_samples
    num_validation_samples: 10000
    input: packed_samples_2048_python_nofim
    output: exported_dataset_2048_python_nofim

  # python 2k fim
  - name: pack_samples
    langs:
      - python

    seq_length: 2048

    # We make each sample one token bigger, so the total size is seq_length + 1.
    # This is because neox uses the first seq_length tokens for the inputs,
    # and the last seq_length tokens for the labels.
    add_one_token: True

    fim_rate: 0.5
    fim_spm_rate: 0
    fim_prefix_token: <fim_prefix>
    fim_suffix_token: <fim_suffix>
    fim_middle_token: <fim_middle>

    input: tokenized
    output: packed_samples_2048_python_fim

  - name: export_indexed_dataset
    samples_column: packed_samples
    num_validation_samples: 10000
    input: packed_samples_2048_python_fim
    output: exported_dataset_2048_python_fim

  # python 4k no-fim
  - name: pack_samples
    langs:
      - python

    seq_length: 4096

    # We make each sample one token bigger, so the total size is seq_length + 1.
    # This is because neox uses the first seq_length tokens for the inputs,
    # and the last seq_length tokens for the labels.
    add_one_token: True

    fim_rate: 0.0
    fim_spm_rate: 0
    fim_prefix_token: <fim_prefix>
    fim_suffix_token: <fim_suffix>
    fim_middle_token: <fim_middle>

    input: tokenized
    output: packed_samples_4096_python_nofim

  - name: export_indexed_dataset
    samples_column: packed_samples
    num_validation_samples: 10000
    input: packed_samples_4096_python_nofim
    output: exported_dataset_4096_python_nofim

  # python 4k no-fim neox-dataset
  # TODO filter by language
  - name: export_indexed_dataset
    langs:
      - python

    samples_column: tokenized_content
    num_validation_samples: 10000
    input: tokenized
    output: exported_dataset_4096_python_nofim_neoxdataset

  # python 8k no-fim
  - name: pack_samples
    langs:
      - python

    seq_length: 8192

    # We make each sample one token bigger, so the total size is seq_length + 1.
    # This is because neox uses the first seq_length tokens for the inputs,
    # and the last seq_length tokens for the labels.
    add_one_token: True

    fim_rate: 0.0
    fim_spm_rate: 0
    fim_prefix_token: <fim_prefix>
    fim_suffix_token: <fim_suffix>
    fim_middle_token: <fim_middle>

    input: tokenized
    output: packed_samples_8192_python_nofim

  - name: export_indexed_dataset
    samples_column: packed_samples
    num_validation_samples: 10000
    input: packed_samples_8192_python_nofim
    output: exported_dataset_8192_python_nofim


spark:
  app_name: "ProcessTheStack"
  log_level: WARN
  master_url: "spark://guy-dev-data-processing-lga1:7077"

  # total-mem = executor_instances * (executor_mem + executor_mem_overhead)
  #   + driver_mem + driver_mem_overhead

  # spark.executor.memory: "30g"
  # spark.executor.memoryOverhead: "18g"
  # spark.executor.instances: "4"

  spark.executor.memory: "20g"
  spark.executor.memoryOverhead: "12g"
  spark.executor.instances: "6"

  # spark.executor.memory: "10g"
  # spark.executor.memoryOverhead: "6g"
  # spark.executor.instances: "12"

  spark.executor.cores: "5"

  # spark.driver.memory: "22g"
  spark.driver.memory: "20g"
  spark.driver.memoryOverhead: "6g"
  spark.driver.maxResultSize: "2g"
  spark.sql.debug.maxToStringFields: "4096"
  spark.serializer: "org.apache.spark.serializer.KryoSerializer"
  spark.sql.files.maxPartitionBytes: "128m"
  # This is trying to solve a memory problem with tokenizing the whole dataset
  spark.sql.parquet.enableVectorizedReader: false
  spark.sql.parquet.columnarReaderBatchSize: "2048" # default 4096

global:
  data_processing_root: /mnt/efs/aug-cw-lga1-nvme/data-pipeline/the-stack/k8s

stages:
  # Filter by language and other criteria, and rebalance languages
  - name: filter_and_rebalance_languages
    input: /mnt/efs/aug-cw-lga1/data/raw/the-stack-dedup.2023-02-04/data
    output: filtered
    # Note: Language names are case sensitive
    languages:
      - C
      - C++
      - Go
      - Java
      - JavaScript
      - Python
    # weights from codegen (total size used in training)
    language_weights:
      C: 48.9
      C++: 69.9
      Go: 21.4
      Java: 120.3
      JavaScript: 24.7
      Python: 55.9

  # Tokenize the content
  - name: tokenize
    input: filtered
    output: tokenized

  # Create positive pairs for retrieval training
  - name: make_retrieval_pairs
    # min, max are both inclusive
    min_retrieved: 64
    max_retrieved: 256
    min_prompt: 64
    max_prompt: 2046  # leave room for start/end tokens in a 2048 seq length
    input: tokenized
    output: retrieval_pairs

  # Create a dense retrieval dataset
  - name: make_retrieval_dataset
    label: make_retrieval_dataset_bs8
    retrieval_batch_size: 8
    input: retrieval_pairs
    output: retrieval

  # Pad samples and save the dataset as an indexed dataset, ready for training
  - name: make_dense_retrieval_indexed_dataset
    label: make_dense_retrieval_indexed_dataset_bs8
    seq_len: 2048
    input: retrieval
    output: dense_retrieval_indexed_dataset

  # Create dense retrieval dataset with larger batch size
  - name: make_retrieval_dataset
    label: make_retrieval_dataset_bs1024
    retrieval_batch_size: 1024
    input: retrieval_pairs
    output: retrieval_bs1024

  - name: make_dense_retrieval_indexed_dataset
    label: make_dense_retrieval_indexed_dataset_bs1024
    seq_len: 2048
    input: retrieval_bs1024
    output: dense_retrieval_indexed_dataset_bs1024

  # ... and another with an even larger batch size
  - name: make_retrieval_dataset
    label: make_retrieval_dataset_bs16384
    retrieval_batch_size: 16384
    input: retrieval_pairs
    output: retrieval_bs16384

  - name: make_dense_retrieval_indexed_dataset
    label: make_dense_retrieval_indexed_dataset_bs16384
    seq_len: 2048
    input: retrieval_bs16384
    output: dense_retrieval_indexed_dataset_bs16384

spark:
  app_name: "ProcessTheStack"
  log_level: WARN
  master_url: "k8s://https://k8s.ord1.coreweave.com:443"
  # Executor OOM will kill the run, so we give it lots of overhead.
  # The eventual container spec in the pod will request
  # spark.executor.memory + s.e.pyspark.memory + s.e.memoryOverhead
  spark.executor.memory: "48g"
  spark.executor.pyspark.memory: "48g"
  spark.executor.memoryOverhead: "12g"
  # Scaling executor.instances up too high (limit TBD) seems to lead to failures from disk contention,
  # materialized as "Connection reset" failures
  spark.executor.instances: "64"
  spark.executor.cores: "5"
  spark.driver.maxResultSize: "2g"
  spark.sql.debug.maxToStringFields: "4096"
  spark.serializer: "org.apache.spark.serializer.KryoSerializer"
  spark.sql.files.maxPartitionBytes: "128m"

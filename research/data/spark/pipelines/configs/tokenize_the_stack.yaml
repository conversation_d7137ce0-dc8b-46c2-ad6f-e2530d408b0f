global:
  #data_processing_root: /mnt/efs/augment-lga1-nvme/data-pipeline/the-stack
  data_processing_root: /mnt/efs/augment-lga1-nvme/user/guy/data-pipeline/the-stack

stages:
  # Filter by language and other criteria, and rebalance languages
  - name: filter_and_tokenize
    max_size: 1_000_000

    skip_langparts:
      # Got OOM from files in these partitions, so skip them
      - csv
      - jupyter-notebook
      - markdown
      - yaml
      # These showed up in the big files list and perhaps can be relaxed, but
      # let's skip them for now
      - json
      - xml
      - html
      - text

    partition_by: langpart

    input: /mnt/efs/augment-lga1-nvme/data/raw/the-stack-dedup.2023-02-04/data
    # input: /mnt/efs/augment-lga1-nvme/data/raw/the-stack-dedup.2023-02-04/data/langpart=java/data_0000.parquet
    #input: /mnt/efs/augment-lga1/data/raw/the-stack-dedup.2023-02-04/data
    # input: /mnt/efs/augment-lga1/data/raw/mini-stack-dedup.2023-02-04
    # input: /mnt/efs/augment-lga1-nvme/data/raw/the-stack-debugging
    output: full_dataset_tokenized


spark:
  app_name: "ProcessTheStack"
  log_level: WARN
  master_url: "spark://guy-dev-data-processing-lga1:7077"

  # total-mem = executor_instances * (executor_mem + executor_mem_overhead)
  #   + driver_mem + driver_mem_overhead

  spark.executor.memory: "30g"
  spark.executor.memoryOverhead: "18g"
  spark.executor.instances: "4"

  # spark.executor.memory: "20g"
  # spark.executor.memoryOverhead: "12g"
  # spark.executor.instances: "6"

  # spark.executor.memory: "10g"
  # spark.executor.memoryOverhead: "6g"
  # spark.executor.instances: "12"

  spark.executor.cores: "5"

  # spark.driver.memory: "22g"
  spark.driver.memory: "20g"
  spark.driver.memoryOverhead: "6g"
  spark.driver.maxResultSize: "2g"
  spark.sql.debug.maxToStringFields: "4096"
  spark.serializer: "org.apache.spark.serializer.KryoSerializer"
  spark.sql.files.maxPartitionBytes: "128m"
  # This is trying to solve a memory problem with tokenizing the whole dataset
  spark.sql.parquet.enableVectorizedReader: false
  spark.sql.parquet.columnarReaderBatchSize: "2048" # default 4096

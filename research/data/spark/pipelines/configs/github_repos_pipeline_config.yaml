global:
  data_processing_root: /mnt/efs/augment-lga1-nvme/data-pipeline/github-repos

stages:
  # Filter by language and other criteria, and rebalance languages
  - name: compute_github_repo_statistics
    limit: 10
    input: initial-repo-list/shuffled_repos.txt
    output: statistics

spark:
  app_name: "ProcessTheStack"
  log_level: WARN
  master_url: "spark://guy-dev-data-processing-lga1:7077"
  # TODO we're giving every executor lots of memory, see if we can reduce it
  # total-mem = executor_instances * (executor_mem + executor_mem_overhead)
  #   + driver_mem + driver_mem_overhead
  spark.executor.memory: "10g"
  spark.executor.memoryOverhead: "6g"
  spark.executor.instances: "12"
  spark.executor.cores: "5"
  spark.driver.memory: "20g"
  spark.driver.memoryOverhead: "6g"
  spark.driver.maxResultSize: "2g"
  spark.sql.debug.maxToStringFields: "4096"
  spark.serializer: "org.apache.spark.serializer.KryoSerializer"
  spark.sql.files.maxPartitionBytes: "128m"

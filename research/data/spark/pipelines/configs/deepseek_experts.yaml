# Prepare samples for language-specific MoE training

global:
  # TO<PERSON><PERSON>(guy) move to LAS1 to make it faster
  data_processing_root: /mnt/efs/augment-lga1-nvme/user/guy/data-pipeline/deepseek
  tokenizer: DeepSeekCoderBaseTokenizer

stages:
  # Filter by language and other criteria
  - name: filter_and_tokenize

    partition_by: lang

    # TODO(guy) move this to LAS1 to make it faster
    input: /mnt/efs/augment-lga1-nvme/data/raw/starcoder/raw
    output: tokenized

  # python 4k
  - name: pack_samples
    langs:
      - python

    seq_length: 4096

    # We make each sample one token bigger, so the total size is seq_length + 1.
    # This is because neox uses the first seq_length tokens for the inputs,
    # and the last seq_length tokens for the labels.
    add_one_token: True

    fim_rate: 0.5
    fim_spm_rate: 0
    fim_prefix_token: <｜fim▁begin｜>
    fim_suffix_token: <｜fim▁hole｜>
    fim_middle_token: <｜fim▁end｜>

    input: tokenized
    output: packed_samples_4096_python

  - name: export_indexed_dataset
    samples_column: packed_samples
    num_validation_samples: 10000
    input: packed_samples_4096_python
    output: exported_dataset_4096_python

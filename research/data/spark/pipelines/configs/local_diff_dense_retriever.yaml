global:
  data_processing_root: /mnt/efs/augment-lga1-nvme/data-pipeline/diff_dense_retriever/diff-dense-retriever_40milpairs_fixtokenization

stages:
  # Create a dense retrieval dataset
  - name: format_query_key_chunks
    input: /mnt/efs/augment-lga1-nvme/raw_diff_datasets/raw_diff_dense_retrieval_v1_40mil_pairs
    seq_len: 640
    output: formatted_chunks

  # Pad samples and save the dataset as an indexed dataset, ready for training
  - name: add_batching
    retrieval_batch_size: 1024
    input: formatted_chunks
    output: batched_and_formatted_chunks

  - name: add_padding_and_index
    seq_len: 640
    input: batched_and_formatted_chunks
    output: diff_dense_retriever_indexed_dataset



spark:
  app_name: "ProcessDiffDenseRetriever"
  log_level: WARN
  master_url: "spark://colin-dev-lga1:7078"
  # TODO we're giving every executor lots of memory, see if we can reduce it
  # total-mem = executor_instances * (executor_mem + executor_mem_overhead)
  #   + driver_mem + driver_mem_overhead
  spark.executor.memory: "10g"
  spark.executor.memoryOverhead: "6g"
  spark.executor.instances: "12"
  spark.executor.cores: "20"
  spark.driver.memory: "20g"
  spark.driver.memoryOverhead: "6g"
  spark.driver.maxResultSize: "2g"
  spark.sql.debug.maxToStringFields: "4096"
  spark.serializer: "org.apache.spark.serializer.KryoSerializer"
  spark.sql.files.maxPartitionBytes: "128m"
  # These modules will be available to UDFs

global:
  data_processing_root: /mnt/efs/augment-lga1-nvme/data-pipeline/the-stack

stages:
  # Filter by language and other criteria, and rebalance languages
  - name: filter_and_rebalance_languages
    input: /mnt/efs/augment-lga1/data/raw/the-stack-dedup.2023-02-04/data
    output: filtered
    # Note: Language names are case sensitive
    languages:
      - C
      - C++
      - Go
      - Java
      - JavaScript
      - Python
    # weights from codegen (total size used in training)
    # language_weights:
    #   C: 48.9
    #   C++: 69.9
    #   Go: 21.4
    #   Java: 120.3
    #   JavaScript: 24.7
    #   Python: 55.9

  # Tokenize the content
  - name: tokenize
    input: filtered
    output: tokenized

  # Create positive pairs for retrieval training
  - name: make_random_samefile_retrieval_pairs
    # min, max are both inclusive
    min_retrieved: 64
    max_retrieved: 256
    min_prompt: 64
    max_prompt: 2046  # leave room for start/end tokens in a 2048 seq length
    input: tokenized
    output: retrieval_pairs

  # Create a dense retrieval dataset, not grouped by language
  # Batch size is not specified because it is meaningless: all rows get shuffled
  - name: batch_dense_retrieval_dataset
    group_batch_samples_by_lang: false
    input: retrieval_pairs
    output: retrieval_ungrouped

  # Pad samples and save the dataset as an indexed dataset, ready for training
  - name: export_dense_retrieval_indexed_dataset
    seq_len: 2048
    input: retrieval_ungrouped
    output: dense_retrieval_indexed_dataset_ungrouped

  # # Create dense retreival dataset with larger batch size, grouped by language
  # - name: make_retrieval_dataset
  #   retrieval_batch_size: 1024
  #   group_batch_samples_by_lang: true
  #   input: retrieval_pairs
  #   output: retrieval_bs1024

  # - name: make_dense_retrieval_indexed_dataset, grouped by language
  #   seq_len: 2048
  #   input: retrieval_bs1024
  #   output: dense_retrieval_indexed_dataset_bs1024

  # # ... and another with an even larger batch size
  # - name: make_retrieval_dataset
  #   retrieval_batch_size: 16384
  #   group_batch_samples_by_lang: true
  #   input: retrieval_pairs
  #   output: retrieval_bs16384

  # - name: make_dense_retrieval_indexed_dataset
  #   seq_len: 2048
  #   input: retrieval_bs16384
  #   output: dense_retrieval_indexed_dataset_bs16384

spark:
  app_name: "ProcessTheStack"
  log_level: WARN
  master_url: "spark://guy-dev-data-processing-lga1:7077"
  # total-mem = executor_instances * (executor_mem + executor_mem_overhead)
  #   + driver_mem + driver_mem_overhead
  spark.executor.memory: "10g"
  spark.executor.memoryOverhead: "6g"
  spark.executor.instances: "12"
  spark.executor.cores: "5"
  spark.driver.memory: "20g"
  spark.driver.memoryOverhead: "6g"
  spark.driver.maxResultSize: "2g"
  spark.sql.debug.maxToStringFields: "4096"
  spark.serializer: "org.apache.spark.serializer.KryoSerializer"
  spark.sql.files.maxPartitionBytes: "128m"

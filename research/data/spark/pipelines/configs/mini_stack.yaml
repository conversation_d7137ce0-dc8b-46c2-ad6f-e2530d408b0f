global:
  data_processing_root: /mnt/efs/augment-lga1-nvme/data-pipeline/mini-stack

stages:
  # Filter by language and other criteria, and rebalance languages
  - name: filter_and_tokenize
    # max_size: 1000000 # 1mB

    skip_langparts:
      # Got OOM from files in these two partitions, so skip them
      - csv
      - jupyter-notebook
      # TODO can these be relaxed?
      # These showed up in the big files list
      - json
      - xml
      - html
      - text
    token_bytearray_packing: False
    input: /mnt/efs/augment-lga1/data/raw/mini-stack-dedup.2023-02-04/data
    output: full_dataset_tokenized

  - name: show_content_statistics
    input: full_dataset_tokenized
    output: full_dataset_content_statistics

  # - name: verify_tokenization
  #   input: full_dataset_tokenized
  #   output: full_dataset_tokenization_verification

  - name: pack
    input: full_dataset_tokenized
    seq_length: 2048
    output: packed


spark:
  app_name: "ProcessTheStack"
  log_level: WARN
  master_url: "spark://guy-dev-data-processing-lga1:7077"
  # TODO we're giving every executor lots of memory, see if we can reduce it
  # total-mem = executor_instances * (executor_mem + executor_mem_overhead)
  #   + driver_mem + driver_mem_overhead

  # spark.executor.memory: "30g"
  # spark.executor.memoryOverhead: "8g"
  # spark.executor.instances: "4"

  spark.executor.memory: "10g"
  spark.executor.memoryOverhead: "6g"
  spark.executor.instances: "12"

  spark.executor.cores: "5"

  # spark.driver.memory: "22g"
  spark.driver.memory: "20g"
  spark.driver.memoryOverhead: "6g"
  spark.driver.maxResultSize: "2g"
  spark.sql.debug.maxToStringFields: "4096"
  spark.serializer: "org.apache.spark.serializer.KryoSerializer"
  spark.sql.files.maxPartitionBytes: "128m"
  # This is trying to solve a memory problem with tokenizing the whole dataset
  spark.sql.parquet.enableVectorizedReader: false
  spark.sql.parquet.columnarReaderBatchSize: "2048" # default 4096

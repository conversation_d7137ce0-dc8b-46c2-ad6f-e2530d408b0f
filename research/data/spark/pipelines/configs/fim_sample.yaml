global:
  data_processing_root: /mnt/efs/aug-cw-lga1-nvme/data-pipeline/the-stack/
  tokenizer: HFGPT2Tokenizer
  tokenizer_args: ["/mnt/efs/aug-cw-lga1/checkpoints/tokenizers/starcoderbase"]

stages:
  # Subsample the data significantly
  - name: limit
    input: filtered
    output: filtered_1M_py
    size: 1_000_000
    numPartitions: 64
    ext: py

  # Sample FIM problems
  - name: sample_fim_and_tokenize
    input: filtered_1M_py
    output: fim_tokenized_1M_py
    force: true

  - name: add_global_batch_id
    input: fim_tokenized_1M_py
    output: fim_tokenized_batched_1M_py
    batch_size: 128
    force: true

  - name: export_indexed_dataset
    input: fim_tokenized_batched_1M_py
    output: fim_indexed_dataset_1M_py
    force: true

#!/bin/bash
export PYTHONPATH=${PYTHONPATH}:${HOME}/augment:${HOME}/augment/research/gpt-neox/
export TOKENIZERS_PARALLELISM=false
export PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python

CONFIG=configs/pr_next_edit_location.yaml;

# chdir to the main pipeline directory
cd "$(dirname "$0")"
cd ..

# make sure we have the optimized tokenizer installed
bazel run -c opt //base:install

time python3 pipeline.py \
    --k8s \
    --config "$CONFIG" \
    |& tee log.txt

## Spark in k8s

## Launching the pipeline
./run_k8s.sh

The main difference from the single-node invocation is that JVM is created before the python process, so the memory arguments have to be moved to the spark-submit call.

Once launched, the launching shell will continue to poll the driver pod; this is not very informative, and can be interrupted without problems.

## Monitoring a run

Find the driver pod for your run:
    `kubectl pods | grep dense-retrieval`
Look for the one that just launched

To follow the logs:
    `kubectl logs -f $pod_name`
This can be interrupted without problems.

To view the spark driver console, forward the port from the driver pod to your local host
    `kubectl port-forward $pod_name 4040:4040`
Then access the console at http://localhost:4040 _not https!_

## Monitoring in grafana
https://augment-grafana.tenant-augment-eng.ord1.ingress.coreweave.cloud/d/UVdD3RUVz/spark-status

Two variables are required for this dashboard - the driver pod name (see above) and the ID from the executor pods:
    `kubectl get pods | grep kubectl get pods | grep processthestack`
The ID is the hex value in the middle of the name eg "processthestack-033f1a8801b02a01-exec-46"

### Creating the image
Probably don't need to do this much.  The image we use is the default augment image, with a layer of spark on top.

The basic build steps are:
- build a spark distro
- build the docker images from it
- push the images up to the registry

ref: https://spark.apache.org/docs/latest/building-spark.html

The augment spark changes are at https://github.com/augmentcode/spark/tree/au-140-augment-spark-container

- sudo apt install r-base
- sudo Rscript -e "install.packages(c('knitr', 'rmarkdown', 'devtools', 'testthat', 'e1071', 'survival'), repos='https://cloud.r-project.org/')"
- Clone https://github.com/augmentcode/spark
- git checkout au-140-augment-spark-container
- Build the distribution without R enabled (see below)
  - ./dev/make-distribution.sh --name custom-spark --pip  --tgz -DskipTests  -Phive -Phive-thriftserver -Pmesos -Pyarn -Pkubernetes
- cd dist
- docker build -t augment_spark:latest -f kubernetes/dockerfiles/spark/Dockerfile .
- docker build -t augment_spark_python:latest --build_arg base_img=augment_spark:latest -f kubernetes/dockerfiles/spark/bindings/python/Dockerfile .

Push the augment_spark_python docker image up to whatever you're referencing in the run_k8s.sh script


The default distribution build failed with R enabled, it's unclear why.  Disabling it in our environment is probably ok?
./dev/make-distribution.sh --name custom-spark --pip --r --tgz -Psparkr -Phive -Phive-thriftserver -Pmesos -Pyarn -Pkubernetes

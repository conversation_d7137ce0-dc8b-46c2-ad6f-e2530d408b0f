"""Utility functions for the data pipeline."""

import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Callable, Optional

import yaml
from megatron.tokenizer import get_tokenizer

from base.tokenizers import Tokenizer, create_tokenizer_by_name
from research.data.spark.pipelines.stages.common import TOKEN_SIZE


@dataclass
class GlobalPipelineConfig:
    """Global pipeline configuration."""

    data_processing_root: str
    """Root for reading/writing stage outputs."""

    tokenizer: Optional[str] = None
    """Name of tokenizer to use, or None if no tokenizer is used in the pipeline."""

    tokenizer_args: Optional[list[str]] = None
    """Optional arguments to pass to the tokenizer factory."""

    is_production_tokenizer: bool = False
    """True if this is a production tokenizer, False if it's a reasearch tokenizer."""

    no_tokenizer: bool = False
    """Set this explicitly to True if you don't want to use a tokenizer."""

    tmpdir: Optional[str] = None
    """Specify a fast temporary directory.

    If set, stage functions need to actively use it.
    """

    efs_path: Optional[Path] = None
    """Path to the EFS mount point."""


class ObjectDict(dict):
    """Provides both namespace-like and dict-like access to fields.

    Allows access to fields using both obj.name notation and obj["name"]
    notation. The latter is useful when "name" contains periods, for example.
    """

    def __getattr__(self, name: str):
        if name in self:
            return self[name]
        else:
            raise AttributeError("No such attribute: " + name)

    def __setattr__(self, name: str, value):
        self[name] = value

    def __delattr__(self, name: str):
        if name in self:
            del self[name]
        else:
            raise AttributeError("No such attribute: " + name)


def make_dot_accessible(data):
    """Makes a nested data structure dot accessible.

    Takes a nested data structure (dicts, list, and leaves), and converts
    it to a structure that also allows dot access to the dict keys.

    Dot, dict, and list notations can be used to access fields:

        ns = nested_dict_to_namespace({"a": "b"})
        assert ns.a == "b"
        assert ns["a"] == "b"

    Args:
        data: The nested data structure to convert.

    Returns:
        An object that supports dot access.
    """
    if isinstance(data, dict):
        result = ObjectDict()
        for k, v in data.items():
            result.__setattr__(k, make_dot_accessible(v))
        return result
    elif isinstance(data, list):
        return [make_dot_accessible(elem) for elem in data]
    return data


def test_make_dot_accessible():
    d = {
        "leaf": "b",
        "list": [{"c": "d"}, {"e": "f"}],
        "dict": {"foo": {"bar": "xyz"}, "abc": "def"},
    }

    da = make_dot_accessible(d)
    assert isinstance(da, ObjectDict)
    assert da.leaf == "b"
    assert da["leaf"] == "b"
    assert da.list[0].c == "d"
    assert da["list"][1]["e"] == "f"
    assert da.dict.foo.bar == "xyz"
    assert da["dict"].abc == "def"


def load_config(path: Path) -> ObjectDict:
    """Load a config and return it as a structured namespace."""
    with path.open("r", encoding="utf8") as config_file:
        config_dict = yaml.safe_load(config_file)
    return make_dot_accessible(config_dict)  # type: ignore


TokenizerFactory = Callable[[], Tokenizer]


def setup_tokenizer(
    global_config: GlobalPipelineConfig,
) -> Optional[TokenizerFactory]:
    """Setup and sanity check the tokenizer.

    Returns a tokenizer_factory which can be called to return a tokenizer. The
    tokenizer itself can be a production tokenizer or a research tokenizer.
    Returns Nones if no tokenizer is configured.
    """
    if global_config.no_tokenizer:
        return None
    elif not global_config.tokenizer:
        raise ValueError("Tokenizer not specified in config")

    logging.info(
        "Loading tokenizer %s with args %s (production tokenizer? %s)",
        global_config.tokenizer,
        global_config.tokenizer_args,
        global_config.is_production_tokenizer,
    )

    def tokenizer_factory():
        assert global_config.tokenizer is not None
        if global_config.is_production_tokenizer:
            if global_config.tokenizer_args:
                raise ValueError("Production tokenizers do not support tokenizer_args")
            tokenizer = create_tokenizer_by_name(global_config.tokenizer)
        else:
            tokenizer = get_tokenizer(
                global_config.tokenizer, *global_config.tokenizer_args
            )

            # NOTE(arun): We store tokens as "short" integers to save on space.
            if len(tokenizer.vocab) > 2 ** (TOKEN_SIZE * 8) - 1:
                raise ValueError(
                    f"Vocabulary size is too large ({len(tokenizer.vocab)})."
                )
        return tokenizer

    return tokenizer_factory

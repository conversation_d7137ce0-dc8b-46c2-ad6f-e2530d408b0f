"""Spark pipeline functions for next edit location.

This file implements Spark wrapper functions for the stages defined in
`research.next_edits.edit_localization_stages`. See the `quick_test_run` function for an
example run config.

Note that we use a simple versioning system to separately version each pipeline stage,
and `run_stages` then saves each stage's result using a directory name based on the
version numbers + configs of *all* previous stages. This way, any unchanged stages'
result will automatically be reused by `run_stages`, hence only the stages that
have changed since last run will be run.
"""

import hashlib
import json
import logging
import pickle
from dataclasses import asdict, dataclass, field
from pathlib import Path
from typing import Any, Callable, Literal, cast

import pandas as pd
import pyarrow
from dataclasses_json import DataClassJsonMixin
from pyspark.sql import SparkSession
from unidiff import PatchedFile, UnidiffParseError

from base.caching.lru_cache import lru_cache
from base.languages.language_guesser import guess_language
from base.prompt_format_next_edit.gen_prompt_formatter import EditGenFormatterConfig
from research.core.diff_utils import (
    CommandFailedError,
    diff_subset,
    parse_git_diff_output,
)
from research.data.spark.pipelines.stages import prs
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.pipelines.utils.next_edit_common import (
    PRToRepoChangeConfig,
    pr_to_repo_change,
    run_create_indexed_dataset,
    smart_session,
    stage_block,
)
from research.data.spark.pipelines.utils.prs_dataset import (
    PRData,
    RepositoryFileManager,
)
from research.next_edits.edit_localization_stages import (
    CreateRetrievalProblemsConfig,
    EditLocalizationProblem,
    EditLocalizationRetrievalProblem,
    EditModelScorerConfig,
    FormatAsTokensConfig,
    RepoChangeToProblemConfig,
    RetrieverConfigDict,
    ScoreChunksConfig,
    create_retrieval_problems,
    format_as_tokens,
    repo_change_to_problems,
    score_chunks,
)
from research.next_edits.next_edits_dataset import CommitMeta, PRMeta
from research.utils import repo_change_utils
from research.utils.inspect_indexed_dataset import print_green
from research.utils.repo_change_utils import RepoChange
from research.utils.token_array_utils import TokenArray

logger = logging.getLogger(__name__)


RunMode = Literal["", "timing", "local"]


# Cache this resource across multiple batches in the same process.
@lru_cache()
def create_repo_file_manager(
    pr_repo_reference_file: Path | str,
) -> RepositoryFileManager:
    """Create a repository file manager."""
    return RepositoryFileManager(Path(pr_repo_reference_file))


def pr_to_problems_wrapper(
    input_path: Path | str,
    output_path: Path | str,
    task_info_location: Path | str,
    data_config: PRToRepoChangeConfig,
    config: RepoChangeToProblemConfig,
    spark: SparkSession,
    timing_run: bool = False,
    global_seed: int = 0,
) -> dict:
    """Sample problems from the PR dataset.

    This is a spark wrapper around `repo_to_problems` and generates a row
    for each input repo, with the following 3 columns:
        - id: a unique identifier for the batch of problems.
        - repo_name: the name of the repo.
        - num_problems: the number of problems sampled from the repo.
        - pickled_results: the pickled list of `EditLocalizationProblem`s

    Args:
        input_path: The path to the input parquet file.
        output_path: The path to the output parquet file.
        task_info_location: The path to the task info file.
        data_config: The PR to RepoChange conversion config.
        config: The RepoChange to problems conversion config.
        spark: The spark session.
        timing_run: Whether this is a timing run.
        global_seed: The global seed to use.

    Returns:
        The task info dict.
    """

    def process_batch(df: pd.DataFrame):
        repo_file_manager = RepositoryFileManager(
            Path(data_config.pr_repo_reference_file)
        )

        problems_per_repo = dict[str, list[EditLocalizationProblem]]()

        for pr in df.to_dict(orient="records"):
            tup = pr_to_repo_change(
                cast(PRData, pr),
                repo_file_manager,
                data_config,
                max_repo_size_files=config.max_repo_size_files,
                file_filter=lambda path: bool(guess_language(path)),
            )
            if tup is None:
                continue
            (past_to_future_repo_change, pr_meta, commit_meta) = tup
            problems_per_repo.setdefault(pr_meta.repo_name, []).extend(
                repo_change_to_problems(
                    past_to_future_repo_change,
                    pr_meta,
                    commit_meta,
                    config,
                    seed=global_seed + hash(f"{pr_meta.repo_name}/{pr_meta.pr_number}"),
                )
            )

        # Get a unique id for a single batch of problems as there might be multiple
        # batches with the same repo name.
        def get_id(problems: list[EditLocalizationProblem]) -> str:
            return hashlib.sha256(
                (b",".join(bytes(problem.pr_meta.pr_number) for problem in problems))
            ).hexdigest()

        return pd.DataFrame(
            [
                {
                    "id": f"{repo_name}/{get_id(problems)}",
                    "repo_name": repo_name,
                    "num_problems": len(problems),
                    "pickled_results": pickle.dumps(problems),
                }
                for repo_name, problems in problems_per_repo.items()
            ]
        )

    return map_parquet.apply_pandas(
        spark_session=spark,
        pandas_func=process_batch,
        input_path=str(input_path),
        output_path=str(output_path),
        ignore_error=True,
        # Keep batch sizes small as storing the pickled results takes a lot of memory.
        batch_size=1 if timing_run else 10,
        task_info_location=str(task_info_location),
        # 1 hour for this stage is more than sufficient.
        timeout=3600,
        timing_run=timing_run,
    )


def run_pr_to_problems(
    pr_source_path: Path | str,
    output_root: Path | str,
    short_name: str,
    pr_config: PRToRepoChangeConfig,
    config: RepoChangeToProblemConfig,
    max_workers: int = 32,
    mode: RunMode = "",
    overwrite: bool = False,
    global_seed: int = 0,
    spark: SparkSession | None = None,
) -> tuple[Path, SparkSession | None]:
    """Run the PR to problems stage.

    Args:
        pr_source_path: The path to the input parquet file of PRs grouped by repo.
        output_root: Output for parquet files with pickled `EditLocalizationProblem`s.
        short_name: The short name of the stage.
        pr_config: The PR to RepoChange conversion config.
        config: The RepoChange to problems conversion config.
        max_workers: The maximum number of workers to use.
        mode: The run mode.
        overwrite: Whether to overwrite existing files.
        global_seed: The global seed to use.
        spark: The spark session.

    Returns:
        The path to the output parquet file, and the spark session.
    """
    # Local runs are for debugging purposes, so we set the timing_run option.
    name = f"S{config.VERSION}_{short_name}"
    output_data_path = Path(output_root) / name

    with stage_block(output_data_path, mode=mode, overwrite=overwrite) as (
        output_data_path,
        output_log_path,
        completed,
    ):
        if completed:
            return output_data_path, spark

        if not spark:
            spark = smart_session(name, max_workers=max_workers, mode=mode)

        # Save the full stage configuration to the log directory.
        output_log_path.mkdir(parents=True, exist_ok=True)
        (Path(output_log_path) / "config.json").write_text(
            json.dumps(
                {
                    "pr_config": asdict(pr_config),
                    "config": asdict(config),
                },
                indent=2,
            )
        )
        result = pr_to_problems_wrapper(
            pr_source_path,
            output_data_path,
            output_log_path,
            pr_config,
            config,
            spark,
            timing_run=bool(mode),
            global_seed=global_seed,
        )
        logger.info(result)

    return output_data_path, spark


def create_retrieval_problems_wrapper(
    input_path: Path | str,
    output_path: Path | str,
    task_info_location: Path | str,
    config: CreateRetrievalProblemsConfig,
    spark: SparkSession,
    timing_run: bool = False,
    global_seed: int = 0,
) -> dict:
    """Spark wrapper around `create_retrieval_problems`

    Generates a row for each input repo, with the following columns:
        - id: a unique identifier for the batch of problems.
        - repo_name: the name of the repo.
        - num_problems: the number of problems sampled from the repo.
        - pickled_results: the pickled list of tuples of
            `EditLocalizationProblem`, `EditLocalizationRetrievalProblem`.

    Args:
        input_path: The path to the input parquet file of problems grouped by repo.
        output_path: The path to the output parquet file.
        task_info_location: The path to the task info file.
        config: The config for the stage.
        spark: The spark session.
        timing_run: Whether to run in timing mode.
        global_seed: The global seed to use.

    Returns:
        The result dict.
    """

    def process_batch(df: pd.DataFrame) -> pd.DataFrame:
        output_per_repo = dict[
            str, list[tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]]
        ]()

        for row in df.to_dict(orient="records"):
            problems: list[EditLocalizationProblem] = pickle.loads(
                row["pickled_results"]
            )
            assert len(problems) == row["num_problems"]
            repo_name = row["repo_name"]

            output_per_repo.setdefault(repo_name, []).extend(
                create_retrieval_problems(
                    problems, config, seed=global_seed + hash(row["id"])
                )
            )

        # Get a unique id for a single batch of problems as there might be multiple
        # batches with the same repo name.
        def get_id(
            output: list[
                tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]
            ],
        ) -> str:
            return hashlib.sha256(
                (b",".join(bytes(problem.pr_meta.pr_number) for problem, _ in output))
            ).hexdigest()

        return pd.DataFrame(
            [
                {
                    "id": f"{repo_name}/{get_id(output)}",
                    "repo_name": repo_name,
                    "num_problems": len(output),
                    "pickled_results": pickle.dumps(output),
                }
                for repo_name, output in output_per_repo.items()
            ]
        )

    return map_parquet.apply_pandas(
        spark_session=spark,
        pandas_func=process_batch,
        input_path=str(input_path),
        output_path=str(output_path),
        batch_size=1,
        task_info_location=str(task_info_location),
        timeout=(
            3 * 3600  # 3 hours for dense retrieval
            if config.retrieval_strategy == "retrieved"
            else 3600  # 1 hour for other strategies retrieval
        ),
        input_columns=["id", "repo_name", "num_problems", "pickled_results"],
        ignore_error=True,
        timing_run=timing_run,
    )


def run_create_retrieval_problems(
    problems_source_path: Path | str,
    output_root: Path | str,
    short_name: str,
    config: CreateRetrievalProblemsConfig,
    max_workers: int = 32,
    mode: RunMode = "",
    overwrite: bool = False,
    global_seed: int = 0,
    spark: SparkSession | None = None,
) -> tuple[Path, SparkSession | None]:
    """Run the create retrieval problems stage.

    Args:
        problems_source_path: The path to the input parquet file of problems grouped by repo.
        output_root: The path to the output root directory.
        short_name: The short name of the config.
        config: The config for the stage.
        max_workers: The max number of workers to use.
        mode: The run mode.
        overwrite: Whether to overwrite existing output.
        global_seed: The global seed to use.
        spark: The spark session.

    Returns:
        The output path and spark session.
    """
    name = f"{Path(problems_source_path).name},R{config.VERSION}_{short_name}"

    output_data_path = Path(output_root) / name
    with stage_block(output_data_path, mode=mode, overwrite=overwrite) as (
        output_data_path,
        output_log_path,
        completed,
    ):
        if completed:
            return output_data_path, spark

        if not spark:
            spark = smart_session(
                name,
                max_workers=max_workers,
                mode=mode,
                gpu_type=(
                    ["RTX_A4000", "RTX_A5000", "RTX_A6000", "A40"]
                    if config.retrieval_strategy == "retrieved"
                    else ""
                ),
            )

        # Save the full stage configuration to the log directory.
        output_log_path.mkdir(parents=True, exist_ok=True)
        with (Path(output_log_path) / "config.json").open("w") as f:
            json.dump(asdict(config), f, indent=2)
        result = create_retrieval_problems_wrapper(
            problems_source_path,
            output_data_path,
            output_log_path,
            config,
            spark,
            timing_run=bool(mode),
            global_seed=global_seed,
        )
        logger.info(result)

    return output_data_path, spark


def score_chunks_wrapper(
    input_path: Path | str,
    output_path: Path | str,
    task_info_location: Path | str,
    config: ScoreChunksConfig,
    spark: SparkSession,
    timing_run: bool = False,
    global_seed: int = 0,
) -> dict:
    """Spark wrapper around `score_chunks`.

    Generates a table where each row has the following columns:
        - id: a unique identifier for the batch of problems.
        - repo_name: the name of the repo.
        - num_problems: the number of problems sampled from the repo.
        - pickled_results: the pickled list of (EditLocalizationProblem, EditLocalizationRetrievalProblem, score)

    Args:
        input_path: The path to the input parquet file.
        output_path: The path to the output parquet file.
        task_info_location: The path to the task info location.
        config: The config for the stage.
        spark: The spark session.
        timing_run: Whether to run in timing mode.
        global_seed: The global seed to use.

    Returns:
        Task info for the stage.
    """

    def process_batch(df: pd.DataFrame) -> pd.DataFrame:
        output_per_repo = dict[
            str,
            list[tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]],
        ]()

        for row in df.to_dict(orient="records"):
            problems: list[
                tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]
            ] = pickle.loads(row["pickled_results"])
            assert len(problems) == row["num_problems"]
            repo_name = row["repo_name"]

            output_per_repo.setdefault(repo_name, []).extend(
                score_chunks(
                    config,
                    problems,
                    seed=global_seed + hash(row["id"]),
                )
            )

        def get_id(
            output: list[
                tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]
            ],
        ) -> str:
            return hashlib.sha256(
                (b",".join(bytes(problem.pr_meta.pr_number) for problem, _ in output))
            ).hexdigest()

        return pd.DataFrame(
            [
                {
                    "id": get_id(output),
                    "repo_name": repo_name,
                    "num_problems": len(output),
                    "pickled_results": pickle.dumps(output),
                }
                for repo_name, output in output_per_repo.items()
            ]
        )

    return map_parquet.apply_pandas(
        spark,
        process_batch,
        str(input_path),
        str(output_path),
        task_info_location=str(task_info_location),
        batch_size=1,
        input_columns=["id", "repo_name", "num_problems", "pickled_results"],
        timeout=(
            8 * 3600  # 8 hours for model based scoring
            if "edit_model_score" in config.scoring_strategies
            else 3600  # 1 hour for other strategies retrieval
        ),
        ignore_error=True,
        timing_run=timing_run,
    )


def run_score_chunks(
    input_path: Path | str,
    output_root: Path | str,
    short_name: str,
    config: ScoreChunksConfig,
    max_workers: int = 32,
    mode: RunMode = "",
    overwrite: bool = False,
    global_seed: int = 0,
    spark: SparkSession | None = None,
) -> tuple[Path, SparkSession | None]:
    """Run stage: Score chunks.

    Args:
        input_path: The path to the input parquet file.
        output_root: The root path to the output directory.
        short_name: The short name of the stage.
        config: The config for the stage.
        max_workers: The maximum number of workers to use.
        mode: The run mode.
        overwrite: Whether to overwrite existing output.
        global_seed: The global seed to use
        spark: The spark session.

    Returns:
        The output path and spark session.
    """
    name = f"{Path(input_path).name},Sc{config.VERSION}_{short_name}"

    output_data_path = Path(output_root) / name
    with stage_block(output_data_path, mode=mode, overwrite=overwrite) as (
        output_data_path,
        output_log_path,
        completed,
    ):
        if completed:
            return output_data_path, spark

        if not spark:
            spark = smart_session(
                name,
                max_workers=max_workers,
                mode=mode,
                gpu_type=(
                    ["RTX_A4000", "RTX_A5000", "RTX_A6000", "A40"]
                    if "edit_model_score" in config.scoring_strategies
                    else ""
                ),
            )

        # Save the full stage configuration to the log directory.
        output_log_path.mkdir(parents=True, exist_ok=True)
        with (Path(output_log_path) / "config.json").open("w") as f:
            json.dump(asdict(config), f, indent=2)
        result = score_chunks_wrapper(
            input_path,
            output_data_path,
            output_log_path,
            config,
            spark,
            timing_run=bool(mode),
            global_seed=global_seed,
        )
        logger.info(result)

    return output_data_path, spark


def format_as_tokens_wrapper(
    input_path: Path | str,
    output_path: Path | str,
    task_info_location: Path | str,
    config: FormatAsTokensConfig,
    spark: SparkSession,
    timing_run: bool = False,
    global_seed: int = 0,
) -> dict:
    """Stage Format as tokens.

    This is a spark wrapper around `format_as_tokens` and generates a table
    where each row has the following columns:
        - id: a unique identifier for the batch of problems.
        - repo_name: the name of the repo.
        - num_problems: the number of problems sampled from the repo.
        - pickled_results: the pickled list of token arrays.

    The output of this stage is intended to be used to directly create an
    IndexedDataset.

    Args:
        input_path: The path to the input parquet file.
        output_path: The path to the output parquet file.
        task_info_location: The path to the task info location.
        config: The config for the stage.
        spark: The spark session.
        timing_run: Whether to run in timing mode.
        global_seed: The global seed to use.

    Returns:
        Task info for the stage.
    """

    def process_batch(df: pd.DataFrame):
        ids_per_repo = dict[str, Any]()
        output_per_repo = dict[
            str,
            tuple[
                TokenArray, EditLocalizationProblem, EditLocalizationRetrievalProblem
            ],
        ]()
        for row in df.to_dict(orient="records"):
            problems: list[
                tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]
            ] = pickle.loads(row["pickled_results"])
            assert len(problems) == row["num_problems"]
            repo_name = row["repo_name"]
            output_per_repo.setdefault(repo_name, []).extend(
                format_as_tokens(config, problems, seed=global_seed + hash(row["id"]))
            )
            ids_per_repo.setdefault(repo_name, hashlib.sha256()).update(
                row["id"].encode()
            )
        return pd.DataFrame(
            [
                {
                    "id": f"{repo_name}/{ids_per_repo[repo_name].hexdigest()}",
                    "repo_name": repo_name,
                    "num_problems": len(output),
                    "pickled_results": pickle.dumps(output),
                }
                for repo_name, output in output_per_repo.items()
            ]
        )

    return map_parquet.apply_pandas(
        spark,
        process_batch,
        str(input_path),
        str(output_path),
        task_info_location=str(task_info_location),
        batch_size=1,
        input_columns=["id", "repo_name", "num_problems", "pickled_results"],
        timeout=3600,  # 1 hour is more than enough for this stage.
        ignore_error=True,
        timing_run=timing_run,
    )


def run_format_as_tokens(
    input_path: Path | str,
    output_root: Path | str,
    short_name: str,
    config: FormatAsTokensConfig,
    max_workers: int = 32,
    mode: RunMode = "",
    overwrite: bool = False,
    global_seed: int = 0,
    spark: SparkSession | None = None,
) -> tuple[Path, SparkSession | None]:
    """Run stage: Format as tokens.

    Args:
        input_path: The path to the input parquet file.
        output_root: The root path to the output directory.
        short_name: The short name of the stage.
        config: The config for the stage.
        max_workers: The maximum number of workers to use.
        mode: The run mode.
        overwrite: Whether to overwrite existing output.
        global_seed: The global seed to use.
        spark: The spark session.

    Returns:
        The path to the output parquet file.
    """
    name = f"{Path(input_path).name},T{config.VERSION}_{short_name}"
    output_data_path = Path(output_root) / name

    with stage_block(output_data_path, mode=mode, overwrite=overwrite) as (
        output_data_path,
        output_log_path,
        completed,
    ):
        if completed:
            return output_data_path, spark

        if not spark:
            spark = smart_session(name, max_workers=max_workers, mode=mode)

        # Save the full stage configuration to the log directory.
        output_log_path.mkdir(parents=True, exist_ok=True)
        with (Path(output_log_path) / "config.json").open("w") as f:
            json.dump(asdict(config), f, indent=2)

        result = format_as_tokens_wrapper(
            input_path,
            output_data_path,
            output_log_path,
            config,
            spark,
            timing_run=bool(mode),
            global_seed=global_seed,
        )
        logger.info(result)

    return output_data_path, spark


CHECKPOINT_ROOT = Path("/mnt/efs/augment/checkpoints")
RAVEN_ROOT = CHECKPOINT_ROOT / "next-edit-location"

PR_TRAIN_DATA_PATH = "/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_sorted_train"
PR_VALIDATION_DATA_PATH = (
    "/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_sorted_validation"
)
DEFAULT_OUTPUT_ROOT = "/mnt/efs/spark-data/user/arun/next-edit-location"
TMP_OUTPUT_ROOT = "/mnt/efs/spark-data/temp-weekly/arun/next-edit-location"


def quick_test_run():
    """Start a quick test run of the data generation pipelines."""
    logging.basicConfig(level=logging.INFO, force=True)

    raven_config: RetrieverConfigDict = {
        "scorer": {
            "name": "dense_scorer_v2_fbwd",
            "checkpoint_path": str(
                RAVEN_ROOT / "raven1b.v13-query-1pos-bs1x8x16-lr2e-05-iters2000-K128"
            ),
            "tokenizer_name": "starcoder",
        },
        "chunker": {
            "name": "smart_line_level",
            "max_chunk_chars": 2_000,
        },
        "query_formatter": {
            "name": "next_edit_location_query",
            "tokenizer": "starcoder",
            "max_instruction_tokens": 0,
            "filter_duplicated_file_paths": True,
            "use_smart_header": True,
        },
        "document_formatter": {
            "name": "ethanol6_document",
            "max_tokens": 999,
            "add_path": True,
            "tokenizer_name": "StarCoderTokenizer",
        },
    }

    problems_path, spark = run_pr_to_problems(
        pr_source_path=PR_TRAIN_DATA_PATH,
        output_root=TMP_OUTPUT_ROOT,
        short_name="basic.filter",
        pr_config=PRToRepoChangeConfig(),
        config=RepoChangeToProblemConfig(
            remove_unsupported_languages=True,
        ),
        mode="local",
        overwrite=False,
    )
    retrieval_problems_path, spark = run_create_retrieval_problems(
        problems_source_path=problems_path,
        output_root=TMP_OUTPUT_ROOT,
        short_name="retrieved",
        config=CreateRetrievalProblemsConfig(
            retriever_config=raven_config,
            retrieval_strategy="retrieved",
            num_retrieved_chunks=128,
            ignore_whitespace_changes=True,
        ),
        mode="local",
        spark=spark,
        overwrite=False,
    )
    scored_problems_path, spark = run_score_chunks(
        input_path=retrieval_problems_path,
        output_root=TMP_OUTPUT_ROOT,
        short_name="raven_edit_S24-R4-P18",
        config=ScoreChunksConfig(
            scoring_strategies=("path_distance", "edit_model_score"),
            edit_model_scorer_config=EditModelScorerConfig(
                checkpoint_path=str(
                    CHECKPOINT_ROOT
                    / "next-edit-gen/S24-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k-starcoder2_3b-ffwd"
                ),
                tokenizer_name="starcoder2",
                formatter_config=EditGenFormatterConfig(
                    # Prefer a very short context for retrieval scoring.
                    diff_context_lines=9,
                    max_prompt_tokens=1750,
                    section_budgets={
                        "prefix_tks": 100,
                        "suffix_tks": 100,
                        "filename_tks": 50,
                        "instruction_tks": 100,
                        "diff_tks": 300,
                        "retrieval_tks": 0,
                    },
                ),
            ),
        ),
        mode="local",
        spark=spark,
        overwrite=False,
    )
    formatted_tokens_path, spark = run_format_as_tokens(
        input_path=scored_problems_path,
        output_root=TMP_OUTPUT_ROOT,
        short_name="basic",
        config=FormatAsTokensConfig(
            query_formatter_config=raven_config["query_formatter"],
            document_formatter_config=raven_config["document_formatter"],
            downsample_commits_hunk_threshold=10,
        ),
        mode="local",
        spark=spark,
        overwrite=True,
    )

    def to_token_array(
        obj: tuple[
            TokenArray, EditLocalizationProblem, EditLocalizationRetrievalProblem
        ],
    ) -> TokenArray:
        return obj[0]

    indexed_dataset_output_path = run_create_indexed_dataset(
        input_path=formatted_tokens_path,
        output_root=TMP_OUTPUT_ROOT,
        tokenizer_name="starcoder",
        to_token_array=to_token_array,
        overwrite=True,
    )
    print_green(f"Indexed dataset saved to: {indexed_dataset_output_path}")


if __name__ == "__main__":
    # see also: experimental/jiayi/next_edit/start_data_pipelines.py
    quick_test_run()

"""Spark pipeline functions for next edit generation.

This file implements 3 Spark wrapper functions for the 3 stages defined in
`research.next_edits.edit_gen_stages`. The 3 wrapper functions are then chained
together in the `run_stages` function, which is the main entry point for starting
the Spark job. See the `quick_test_run` function for an example run config.

Note that we use a simple versioning system to separately version each pipeline stage,
and `run_stages` then saves each stage's result using a directory name based on the
version numbers + configs of *all* previous stages. This way, any unchanged stages'
result will automatically be reused by `run_stages`, hence only the stages that
have changed since last run will be run.
"""

import gzip
import gc
import json
import logging
import math
import pickle
import re
import shutil
import subprocess
import tempfile
import time
from collections.abc import Iterable
from pathlib import Path
from random import Random
from typing import Callable, Literal, NamedTuple, Sequence, assert_never, cast

import numpy as np
import pandas as pd
from research.data.dataset import indexed_dataset
from tqdm import tqdm

from research.data.spark.pipelines.stages.next_edit_grouping_pipelines import (
    grouped_to_ordered_repo_changes_wrapper,
)
import research.data.spark.utils as spark_utils
from base.languages.language_guesser import guess_language
from base.prompt_format_next_edit.gen_prompt_formatter import EditGenFormatterConfig
from base.static_analysis.common import assert_eq, groupby
from base.tokenizers import create_tokenizer_by_name
from base.tokenizers.tokenizer import NextEditGenSpecialTokens, Tokenizer
from research.data.spark.pipelines.stages.next_edit_location_pipelines import (
    PRToRepoChangeConfig,
    pr_to_repo_change,
)
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.pipelines.utils.cached_stage_utils import cached_stage
from research.data.spark.pipelines.utils.prs_dataset import (
    PRData,
    RepositoryFileManager,
)
from research.data.utils import pr_v2
from research.data.utils.bare_repo_utils import (
    download_repo_from_s3,
    save_bare_repo_list_disk,
)
from research.fastbackward.loss_masking import unmask_token_np
from research.model_server.server_logging import configure_server_logging
from research.next_edits.edit_gen_sampler import EditGenProblem, EditGenSampler
from research.next_edits.edit_gen_stages import (
    FormattedEditGenProblem,
    PromptConfig,
    RagEditGenProblem,
    RetrievalConfig,
    RetrieverConfigDict,
    SamplingConfig,
    format_as_tokens,
    ordered_repo_changes_to_problems,
    perform_dense_retrieval,
    repo_changes_to_problems,
)
from research.next_edits.next_edits_dataset import (
    HindsightEditGenProblem,
    OrderedRepoChange,
    RepoChangeWithMeta,
    repo_changes_from_commits,
)
from research.utils.data_utils import downsample_to, pretty_counts
from research.utils.inspect_indexed_dataset import print_green, print_yellow
from research.utils.token_array_utils import (
    TokenArray,
    pad_sequences,
    split_by_ratios,
)

MAX_COMMITS_PER_REPO = 5000
MAX_FILES_PER_REPO = 10_000
ROW_SIZE_LIMIT_MB = 100


def s3_repo_to_repo_changes_stage(
    input_path: str,
    output_path: str,
    task_info_location: Path | str,
    quicktest: bool,
    spark,
) -> dict:
    """Stage 0: Turn raw git repos into `RepoChangeWithMeta`s.

    This stage expects the input dataframe to contain an `repo_s3_path` column
    and will generate a row for each input repo, with the following columns:
        - repo_path: the path to the repo tar file
        - pickled_results: the pickled list of `RepoChangeWithMeta`s
        - num_commits: the number of commits in `picked_results`
        - num_prs: the number of commits that are PRs in `picked_results`
    """

    def process_repo(repo_s3_path: str):
        max_commits = MAX_COMMITS_PER_REPO if not quicktest else 100
        with tempfile.TemporaryDirectory() as tmp_dir:
            tmp_dir = Path(tmp_dir)
            # download the repo from s3 and extract it as .git file in tmp_dir
            download_repo_from_s3(repo_s3_path, tmp_dir)
            repo_changes_with_meta = repo_changes_from_commits(
                tmp_dir, max_commits, last_commit="HEAD", max_workers=1
            )
            columns = {
                "repo_path": repo_s3_path,
                "pickled_results": pickle.dumps(repo_changes_with_meta),
                "num_commits": len(repo_changes_with_meta),
                "num_prs": 0,
            }
            return pd.Series(columns)

    return map_parquet.apply(
        spark,
        process_repo,
        input_path,
        output_path,
        batch_size=1,
        task_info_location=task_info_location,
        input_columns=["repo_path"],
        ignore_error=True,
        timeout=30 * 60,  # spend at most 30 minutes per repo
        row_size_limit_mb=ROW_SIZE_LIMIT_MB,
    )


def pr_data_v2_to_repo_changes_stage(
    input_path: str,
    output_path: str,
    task_info_location: Path | str,
    quicktest: bool,
    spark,
) -> dict:
    """Stage 0: Turn PR v2 data into `RepoChangeWithMeta`s.

    This stage expects the input dataframe to contain the following columns:
        - repo_name: the name of the repo
        - path_to_repo_tar: the path to the repo tar file
        - pr_list: the list of PRs in the repo

    The resulting dataframe contains the following columns:
        - repo_path: the disk path to the repo tar file
        - pickled_results: the pickled list of `RepoChangeWithMeta`s
        - num_commits: the number of commits in `picked_results`
        - num_prs: the number of commits that are PRs in `picked_results`
    """

    def process_row(repo_name: str, path_to_repo_tar: str, pr_list: list[pr_v2.PrInfo]):
        with tempfile.TemporaryDirectory() as tmp_dir:
            tmp_dir = Path(tmp_dir)
            repo_pr_info = pr_v2.RepoPrInfo(
                repo_name=repo_name, path_to_repo_tar=path_to_repo_tar, pr_list=pr_list
            )
            repo_changes = pr_v2.pr_data_to_repo_changes(
                repo_pr_info,
                max_workers=1,
                max_repo_files=MAX_FILES_PER_REPO,
                max_repo_commits=MAX_COMMITS_PER_REPO if not quicktest else 50,
            )
            repo_changes = list(repo_changes)
            columns = {
                "repo_path": path_to_repo_tar,
                "pickled_results": pickle.dumps(repo_changes),
                "num_commits": len(repo_changes),
                "num_prs": sum(1 for rc in repo_changes if rc.pr_meta is not None),
            }
            return pd.Series(columns)

    return map_parquet.apply(
        spark,
        process_row,
        input_path,
        output_path,
        batch_size=1,
        task_info_location=task_info_location,
        input_columns=["repo_name", "path_to_repo_tar", "pr_list"],
        ignore_error=True,
        timeout=30 * 60,  # spend at most 30 minutes per repo
        row_size_limit_mb=ROW_SIZE_LIMIT_MB,
    )


def repo_changes_to_problems_stage(
    input_path: str,
    output_path: str,
    task_info_location: Path | str,
    config: SamplingConfig,
    timing_run: bool,
    spark,
) -> dict:
    """Stage 1: Sample edit problems from repo changes.

    This is a spark wrapper around `repo_to_problems` and generates a row
    for each input repo, with the following 3 columns:
        - repo_path: the path to the repo tar file
        - num_repo_changes: the number of repo changes in the input
        - num_problems: the number of problems sampled from the repo
        - pickled_results: the pickled list of EditGenProblem

    It expects input to follow the format generated by `pr_data_v2_to_repo_changes`.
    """

    def process_repo(repo_path: str, pickled_results: bytes):
        start_time = time.time()
        repo_changes: list[RepoChangeWithMeta] = pickle.loads(pickled_results)
        seed = hash(repo_path)
        edit_gen_probs = repo_changes_to_problems(
            config, repo_changes, seed, start_time=start_time
        )
        edit_gen_probs = list(edit_gen_probs)
        columns = {
            "repo_path": repo_path,
            "num_repo_changes": len(repo_changes),
            "num_problems": len(edit_gen_probs),
            "pickled_results": pickle.dumps(edit_gen_probs),
        }
        return pd.Series(columns)

    return map_parquet.apply(
        spark,
        process_repo,
        input_path,
        output_path,
        batch_size=1,
        task_info_location=task_info_location,
        input_columns=["repo_path", "pickled_results"],
        ignore_error=True,
        timing_run=timing_run,
        # use a slightly higher timeout to make the graceful timeout logic hit first
        timeout=config.timeout_per_repo * 1.2,
        row_size_limit_mb=ROW_SIZE_LIMIT_MB,
    )


def ordered_repo_changes_to_problems_stage(
    input_path: str,
    output_path: str,
    task_info_location: Path | str,
    config: SamplingConfig,
    timing_run: bool,
    spark,
) -> dict:
    """Alternative stage 1: Sample edit problems from ordered repo changes.

    This is a spark wrapper around `repo_to_problems` and generates a row
    for each input repo, with the following 3 columns:
        - repo_path: the path to the repo tar file
        - num_repo_changes: the number of repo changes in the input
        - num_problems: the number of problems sampled from the repo
        - pickled_results: the pickled list of EditGenProblem

    It expects input to follow the format generated by `pr_data_v2_to_repo_changes`.
    """

    def process_repo(repo_path: str, pickled_results: bytes):
        start_time = time.time()
        repo_changes: list[OrderedRepoChange] = pickle.loads(pickled_results)
        seed = hash(repo_path)
        edit_gen_probs = ordered_repo_changes_to_problems(
            config, repo_changes, seed, start_time=start_time
        )
        edit_gen_probs = list(edit_gen_probs)
        columns = {
            "repo_path": repo_path,
            "num_repo_changes": len(repo_changes),
            "num_problems": len(edit_gen_probs),
            "pickled_results": pickle.dumps(edit_gen_probs),
        }
        return pd.Series(columns)

    return map_parquet.apply(
        spark,
        process_repo,
        input_path,
        output_path,
        batch_size=1,
        task_info_location=task_info_location,
        input_columns=["repo_path", "pickled_results"],
        ignore_error=True,
        timing_run=timing_run,
        # use a slightly higher timeout to make the graceful timeout logic hit first
        timeout=config.timeout_per_repo * 1.2,
        row_size_limit_mb=ROW_SIZE_LIMIT_MB,
    )


def pr_data_v1_to_repo_changes_stage(
    input_path: str,
    output_path: str,
    task_info_location: str | Path,
    pr_config: PRToRepoChangeConfig,
    quicktest: bool,
    spark,
) -> dict:
    """Stage 0: Convert PRs v1 data to `RepoChangeWithMeta`s.

    This is a Spark wrapper around `repo_changes_to_problems` and generates a row
    for all problems coming from each repo, with the following 3 columns:
        - repo_path: stores a string formatted as "pr3://{repo_name}"
        - num_problems: the number of problems sampled from the repo
        - pickled_results: the pickled list of EditGenProblem

    Returns: the task info dict.
    """

    def process_batch(df: pd.DataFrame) -> pd.DataFrame:
        """Turn a batch of PRs into edit problems."""

        def to_repo_changes(pr_data: list[PRData]):
            repo_file_manager = RepositoryFileManager(
                Path(pr_config.pr_repo_reference_file)
            )
            for pr in pr_data:
                tup = pr_to_repo_change(
                    pr,
                    repo_file_manager,
                    pr_config,
                    max_repo_size_files=MAX_FILES_PER_REPO,
                    # filter out files that are not in the base langauge list
                    file_filter=lambda path: bool(guess_language(path)),
                )
                if tup is None:
                    continue
                (past_to_future_repo_change, pr_meta, commit_meta) = tup
                yield RepoChangeWithMeta(
                    past_to_future_repo_change, commit_meta, pr_meta
                )

        repo_to_data = groupby(
            df.to_dict(orient="records"), lambda x: cast(PRData, x)["repo_name"]
        )
        repo_to_data = cast(dict[str, list[PRData]], repo_to_data)

        rows: list[dict] = []
        for repo_name, pr_data in repo_to_data.items():
            pr_data.sort(key=lambda x: cast(PRData, x)["number"])
            n_to_use = len(pr_data) if not quicktest else min(50, len(pr_data))
            changes = list(to_repo_changes(pr_data[-n_to_use:]))
            row = {
                "repo_path": f"prs://{repo_name}",
                "pickled_results": pickle.dumps(changes),
                "num_commits": len(changes),
                "num_prs": sum(1 for rc in changes if rc.pr_meta is not None),
            }
            rows.append(row)

        return pd.DataFrame(rows)

    return map_parquet.apply_pandas(
        spark_session=spark,
        pandas_func=process_batch,
        input_path=input_path,
        output_path=output_path,
        ignore_error=True,
        batch_size=100,  # each row is a PR, so we need larger batches
        task_info_location=task_info_location,
        timing_run=False,
        timeout=30 * 60,  # spend at most 30 minutes per repo
        row_size_limit_mb=ROW_SIZE_LIMIT_MB,
    )


def dense_retrieval_stage(
    input_path: str,
    output_path: str,
    config: RetrievalConfig,
    spark,
    task_info_location: str | Path | None = None,
    use_compressed_pickles: bool = True,
) -> dict:
    """Stage 2: Perform dense retrieval.

    This is a spark wrapper around `perform_dense_retrieval` and generates a row
    for each input repo, with the following 3 columns:
        - repo_path: the s3 path of the input repo
        - num_results: the number of results.
        - pickled_results: the pickled list of RagEditGenProblem
    """

    def load_problems(pickled_results: bytes):
        problems: list[EditGenProblem] | list[HindsightEditGenProblem]
        if use_compressed_pickles:
            from compress_pickle import loads as compressed_loads

            try:
                problems = compressed_loads(pickled_results, compression="gzip")
            except gzip.BadGzipFile:
                problems = pickle.loads(pickled_results)
        else:
            problems = pickle.loads(pickled_results)
        return problems

    def process_problems(repo_path: str, num_problems: int, pickled_results: bytes):
        problems = load_problems(pickled_results)
        assert_eq(len(problems), num_problems)
        results = list[RagEditGenProblem]()
        start_time = time.time()
        for result in perform_dense_retrieval(config, problems, seed=hash(repo_path)):
            results.append(result)
            if time.time() - start_time > config.timeout_per_repo:
                msg = (
                    f"Repo time out hit: {repo_path}\n"
                    f"Finished {len(results)} of {len(problems)} problems."
                )
                logging.info(msg)
                break
        columns = {
            "repo_path": repo_path,
            "num_problems": len(results),
            "pickled_results": pickle.dumps(results),
        }
        return pd.Series(columns)

    return map_parquet.apply(
        spark,
        process_problems,
        input_path,
        output_path,
        batch_size=1,
        # use a slightly higher timeout to make the graceful timeout logic hit first
        timeout=config.timeout_per_repo * 1.2,
        input_columns=["repo_path", "num_problems", "pickled_results"],
        # drop_original_columns=True,
        ignore_error=False,
        task_info_location=task_info_location,
        row_size_limit_mb=ROW_SIZE_LIMIT_MB,
    )


def format_as_tokens_stage(
    input_path: str,
    output_path: str,
    config: PromptConfig,
    spark,
    task_info_location: str | Path | None = None,
) -> dict:
    """Stage 3: Format as tokens.

    This is a spark wrapper around `format_as_tokens` and generates a table
    where each row has the following columns:
        - repo_path: the s3 path of the input repo
        - num_problems: the number of problems sampled from the repo
        - pickled_results: the picked list of FormattedEditGenProblem
    """

    def format_problems(repo_path: str, num_problems: int, pickled_results: bytes):
        results: list[RagEditGenProblem]
        results = pickle.loads(pickled_results)
        assert_eq(len(results), num_problems)
        list_of_prompts = list(format_as_tokens(config, results, seed=hash(repo_path)))
        return pd.Series(
            {
                "repo_path": repo_path,
                "num_problems": len(list_of_prompts),
                "pickled_results": pickle.dumps(list_of_prompts),
            }
        )

    return map_parquet.apply(
        spark,
        format_problems,
        input_path,
        output_path,
        input_columns=["repo_path", "num_problems", "pickled_results"],
        # drop_original_columns=True,
        task_info_location=task_info_location,
        batch_size=1,
        ignore_error=True,
        row_size_limit_mb=ROW_SIZE_LIMIT_MB,
    )


class Stage3Result(NamedTuple):
    """The result of processing a repo through stage 3."""

    repo_path: str
    num_problems: int
    tokenized_problems: list[FormattedEditGenProblem]


def download_stage3_result(result_s3_path: str) -> list[Stage3Result]:
    """Download the stage3 result as a list of `Stage3Result`."""
    if not result_s3_path.endswith("/"):
        raise ValueError(f"output_path must end with /: {result_s3_path}")
    result_s3_path = result_s3_path.replace("s3a://", "s3://")

    with tempfile.TemporaryDirectory() as tmp_dir:
        tmp_dir = Path(tmp_dir)
        subprocess.run(
            ["s3cmd", "get", result_s3_path, tmp_dir, "--recursive"],
            cwd=tmp_dir,
            check=True,
            stdout=subprocess.DEVNULL,
        )
        return load_stage3_result_from_disk(tmp_dir)


def load_stage3_result_from_disk(result_path: Path) -> list[Stage3Result]:
    """Load the stage3 result from disk."""
    # first remove any .parquet.tmp files
    bad_files = list(result_path.glob("*.parquet.tmp"))
    if bad_files:
        logging.warning(f"Removing {len(bad_files)} .parquet.tmp files.")
    for file in bad_files:
        file.unlink()

    return sum(
        load_stage3_result_from_files(list(result_path.glob("*.parquet")), 1000),
        [],
    )


def load_stage3_result_from_files(
    parquet_files: list[Path],
    repos_per_batch: int,
) -> Iterable[list[Stage3Result]]:
    """Load the stage3 result from disk."""
    results = list[Stage3Result]()
    for shard in tqdm(parquet_files, unit="parquet", smoothing=0):
        df = pd.read_parquet(shard)
        for repo_dict in df.to_dict(orient="records"):
            repo_path = repo_dict["repo_path"]
            num_problems = repo_dict["num_problems"]
            tokenized_problems = pickle.loads(repo_dict["pickled_results"])
            results.append(Stage3Result(repo_path, num_problems, tokenized_problems))
        if len(results) > repos_per_batch:
            yield results
            results = []
            # Explicitly GC-ing to clear up the old results.
            gc.collect()
    if results:
        yield results


def compute_token_stats(
    problems: Sequence[FormattedEditGenProblem],
    tokenizer: Tokenizer,
    skip_expensive: bool,
) -> dict[str, list[int]]:
    """Compute basic statistics on token sequences."""
    import numpy as np

    if not problems:
        logging.warning("No token sequences to compute stats on.")
        return {}

    def get_num_squashes(debug_info: dict):
        if "n_squashes" in debug_info:
            return debug_info["n_squashes"]
        if "simulate_wip_repo_states_with_order" in debug_info:
            return debug_info["simulate_wip_repo_states_with_order"].get(
                "num_squashes", 0
            )
        return 0

    num_tokens = [len(prob.tokens) for prob in problems]
    # treat non-negative tokens as loss tokens
    num_loss_tokens = [np.count_nonzero(prob.tokens >= 0) for prob in problems]
    is_pr_rate = [int(prob.pr_meta is not None) for prob in problems]
    num_squashes = [get_num_squashes(prob.debug_info) for prob in problems]
    num_added_lines = list[int]()
    num_removed_lines = list[int]()
    num_modified_lines = list[int]()
    is_single_line_change_rate = list[int]()
    has_change_rate = list[int]()

    special = tokenizer.special_tokens

    if isinstance(special, NextEditGenSpecialTokens):
        has_change_rate.extend(
            np.count_nonzero(np.abs(prob.tokens) == special.has_change)
            for prob in problems
        )
    else:
        logging.warning(f"Not a supported tokenizer: {type(tokenizer)=}")

    if (
        not skip_expensive
        and sum(has_change_rate) > 0
        and isinstance(special, NextEditGenSpecialTokens)
    ):
        for prob in tqdm(problems, desc="Counting changed lines"):
            seq = np.abs(prob.tokens)
            added, deleted, modified = _find_changed_lines(seq, tokenizer, special)
            num_added_lines.append(added)
            num_removed_lines.append(deleted)
            num_modified_lines.append(modified)
            is_single_line = added <= 1 and deleted <= 1 and (added or deleted)
            is_single_line_change_rate.append(int(is_single_line))

    return {
        "num_tokens": num_tokens,
        "num_loss_tokens": num_loss_tokens,
        "num_added_lines": num_added_lines,
        "num_removed_lines": num_removed_lines,
        "num_modified_lines": num_modified_lines,
        "has_change_ratio": has_change_rate,
        "is_pr_ratio": is_pr_rate,
        "num_squashes": num_squashes,
        "single_line_change_ratio": is_single_line_change_rate,
    }


def _find_changed_lines(
    token_seq: TokenArray,
    tokenizer: Tokenizer,
    special_tokens: NextEditGenSpecialTokens,
) -> tuple[int, int, int]:
    """Return the number of added and deleted lines in the given sequence.

    This assumes the diff format is used (i.e., `use_diff_based_output` is True).
    """
    from base.prompt_format_next_edit.gen_prompt_formatter import (
        MODEL_DIFF_OUTPUT_PATTERN,
    )

    if np.count_nonzero(token_seq == special_tokens.has_change) == 0:
        return 0, 0, 0
    mid_id = special_tokens.fim_middle
    # find the location of `mid_id` in the sequence
    mid_idx = int(np.where(token_seq == mid_id)[0][0])
    # skip mid_id and has_change_id
    output_tokens = cast(Sequence[int], token_seq[mid_idx + 2 :])
    output_lines = tokenizer.detokenize(output_tokens).splitlines(keepends=True)
    added_lines = 0
    deleted_lines = 0
    modified_lines = 0
    for line in output_lines:
        if match := re.match(MODEL_DIFF_OUTPUT_PATTERN, line):
            if match.group(2).startswith("+"):
                added_lines += 1
            elif match.group(2).startswith("-"):
                deleted_lines += 1
            elif match.group(2).startswith("|"):
                modified_lines += 1
    return added_lines, deleted_lines, modified_lines


def save_stage3_results_as_datasets(
    rng: Random,
    result_path: Path,
    save_path: Path,
    dataset_splits: dict[str, float],
    pad_to_length: int,
    tokenizer: Tokenizer,
    special: NextEditGenSpecialTokens,
    positive_ratio: float,
    unmask_tokens: bool,
    repos_per_batch: int = 1000,
):
    """Save the stage3 results as indexed datasets.

    Args:
        rng: the random number generator.
        result_path: A disk path to the stage3 results.
        save_path: the path under which to save the indexed datasets.
        dataset_splits: the ratios of the dataset splits.
        pad_to_length: the length to pad the sequences to.
        tokenizer: the tokenizer to use.
        positive_ratio: the fraction of positive examples to keep.
        unmask_tokens: whether to unmask the negative tokens.
        repos_per_batch: the number of repos to load per batch. This is used to
            control the memory usage. Examples are only rebalanced within each batch.
    """
    result_shards = list(result_path.glob("*.parquet"))
    print_green(f"Got results for {len(result_shards)} shards.")

    save_path.mkdir(parents=True, exist_ok=True)
    assert save_path.is_absolute(), f"save_path must be absolute: {save_path=}"

    if unmask_tokens:
        dtype = np.uint16 if tokenizer.vocab_size < 2**16 else np.uint32
        padding_id = special.padding
    else:
        dtype = np.int32
        padding_id = -special.padding

    def _create_indexed_dataset(
        save_dir: Path,
        data_name: str,
        shards: list[Path],
    ):
        # Create a dataset builder for this split.
        dataset_builder = indexed_dataset.MMapIndexedDatasetBuilder(
            str(save_dir / f"{data_name}.bin"),
            dtype=dtype,
        )
        split_stats = dict[str, list[int]]()

        for part_id, results_part in enumerate(
            load_stage3_result_from_files(shards, repos_per_batch=repos_per_batch)
        ):
            problems = [
                prob for part in results_part for prob in part.tokenized_problems
            ]
            problems = _rebalance_positive_negative_examples(
                rng,
                problems,
                has_change_id=special.has_change,
                no_change_id=special.no_change,
                positive_ratio=positive_ratio,
            )

            problem_tokens = (
                unmask_token_np(prob.tokens) if unmask_tokens else prob.tokens
                for prob in problems
            )
            for tokens in pad_sequences(
                problem_tokens,
                seq_len=pad_to_length,
                pad_id=padding_id,
            ):
                dataset_builder.add_item(tokens)

            # only compute the expensive stats for the first part
            skip_expensive = part_id > 0
            stats = compute_token_stats(
                problems, tokenizer, skip_expensive=skip_expensive
            )
            if not split_stats:
                split_stats.update(stats)
            else:
                for k, v in stats.items():
                    split_stats[k].extend(v)
            # Force GC to make sure we don't run out of memory.
            del problems, results_part
            gc.collect()
        dataset_builder.finalize(str(save_dir / f"{data_name}.idx"))
        return split_stats

    # We'll split the shards into the dataset splits.
    result_splits = split_by_ratios(result_shards, dataset_splits, rng)
    for split_name, split_result_shards in result_splits.items():
        Path("/home/<USER>/tmp").mkdir(exist_ok=True, parents=True)
        with tempfile.TemporaryDirectory(
            dir="/home/<USER>/tmp", prefix="save_stage3_results_as_datasets"
        ) as tmp_dir:
            tmp_dir = Path(tmp_dir)
            logging.info(f"Building a local indexed dataset at {tmp_dir}.")
            split_stats = _create_indexed_dataset(
                tmp_dir, split_name, split_result_shards
            )
            # upload all files under tmp_dir to gcloud
            logging.info(f"Uploading {tmp_dir} to gcloud.")
            for file in tmp_dir.glob("*"):
                save_path_gcp = str(save_path / file.name).replace(
                    "/mnt/efs/augment/data", "gs://gcp-us1-data"
                )
                subprocess.run(
                    f"gcloud storage cp {file} {save_path_gcp}",
                    shell=True,
                    check=True,
                )

        if not split_stats:
            print(f"No data found for {split_name}.")
            continue
        print(f"Dataset statistics for {split_name}:")
        num_problems = len(split_stats["num_tokens"])
        has_change_ratio = (
            sum(split_stats["has_change_ratio"]) / num_problems
            if num_problems
            else float("nan")
        )
        tokens_per_problem_average = np.mean(split_stats["num_tokens"])
        tokens_per_problem_median = np.median(split_stats["num_tokens"])
        tokens_per_problem_max = max(split_stats["num_tokens"])
        loss_tokens_per_problem = np.mean(split_stats["num_loss_tokens"])
        added_lines_per_problem = np.mean(split_stats["num_added_lines"])
        removed_lines_per_problem = np.mean(split_stats["num_removed_lines"])
        modified_lines_per_problem = np.mean(split_stats["num_modified_lines"])
        single_line_change_ratio = np.mean(split_stats["single_line_change_ratio"])
        is_pr_ratio = np.mean(split_stats["is_pr_ratio"])
        num_squashes_per_problem = np.mean(split_stats["num_squashes"])

        reduced_stats = {
            "num_problems": pretty_counts(num_problems),
            "total_num_tokens": pretty_counts(sum(split_stats["num_tokens"])),
            "tokens_per_problem_average": f"{tokens_per_problem_average:.2f}",
            "tokens_per_problem_median": f"{tokens_per_problem_median:.2f}",
            "tokens_per_problem_max": str(tokens_per_problem_max),
            "loss_tokens_per_problem": f"{loss_tokens_per_problem:.2f}",
            "added_lines_per_problem": f"{added_lines_per_problem:.2f}",
            "removed_lines_per_problem": f"{removed_lines_per_problem:.2f}",
            "modified_lines_per_problem": f"{modified_lines_per_problem:.2f}",
            "has_change_ratio": f"{has_change_ratio:.2%}",
            "single_line_change_ratio": f"{single_line_change_ratio:.2%}",
            "is_pr_ratio": f"{is_pr_ratio:.2%}",
            "num_squashes_per_problem": f"{num_squashes_per_problem:.2f}",
            "pad_to_length": str(pad_to_length),
        }
        for k, v in reduced_stats.items():
            print(f"\t{k}: {v}")
        (save_path / f"{split_name}_token_stats.json").write_text(
            json.dumps(reduced_stats, indent=2)
        )


def _rebalance_positive_negative_examples(
    rng: Random,
    problems: Sequence[FormattedEditGenProblem],
    has_change_id: int,
    no_change_id: int,
    positive_ratio: float,
) -> list[FormattedEditGenProblem]:
    """Downsample to have the desired positive example ratio."""

    positive_examples = list[FormattedEditGenProblem]()
    negative_examples = list[FormattedEditGenProblem]()
    for prob in problems:
        if has_change_id in prob.tokens:
            positive_examples.append(prob)
        else:
            assert no_change_id in prob.tokens
            negative_examples.append(prob)

    n_positive = len(positive_examples)
    n_negative = len(negative_examples)
    negative_ratio = 1.0 - positive_ratio
    n_positive = min(n_positive, round(n_negative * positive_ratio / negative_ratio))
    if n_positive != len(positive_examples):
        ratio = (len(positive_examples) - n_positive) / len(positive_examples)
        logging.warning(f"Threw away {ratio:.1%} positive examples to rebalance.")
    positive_examples = downsample_to(rng, positive_examples, n_positive)

    n_negative = min(n_negative, round(n_positive * negative_ratio / positive_ratio))
    if n_negative != len(negative_examples):
        ratio = (len(negative_examples) - n_negative) / len(negative_examples)
        logging.warning(f"Threw away {ratio:.1%} negative examples to rebalance.")
    negative_examples = downsample_to(rng, negative_examples, n_negative)
    return positive_examples + negative_examples


cpu_spark_conf = {
    "spark.executor.pyspark.memory": "200G",
    "spark.executor.memory": "80G",
    "spark.sql.parquet.columnarReaderBatchSize": "16",
}
gpu_spark_conf = {
    "spark.executor.pyspark.memory": "200G",
    "spark.executor.memory": "60G",
    "spark.sql.parquet.columnarReaderBatchSize": "16",
    "spark.task.cpus": "5",
}


def get_session(
    use_gpu: bool,
    max_workers: int = 32,
    local_run: bool = False,
    name: str | None = None,
):
    if local_run:
        config = (gpu_spark_conf if use_gpu else cpu_spark_conf).copy()
        config["spark.task.cpus"] = "1"
        return spark_utils.get_local_session(
            workers=1, conf=config, memory_per_worker_mb=16384
        )
    if use_gpu:
        return spark_utils.k8s_session(
            max_workers=max_workers,
            conf=gpu_spark_conf,
            gpu_type=["h100"],
            gpu_count=1,
            name=name,
            skip_bazel_build=False,
            # TODO: Remove this kwarg once the pod image issue has been resolved.
            image="us-central1-docker.pkg.dev/augment-research-gsc/docker-us-central1/augment_devpod_gpu:ubuntu22.04-cuda-12.1-py-3.11.7-pytorch-2.3.0-ngc-det-0.36.0-26-spark-3.4.3-s33-devpod12",
        )
    else:
        return spark_utils.k8s_session(
            max_workers=max_workers, conf=cpu_spark_conf, gpu_count=0, name=name
        )


class FromGitRepos(NamedTuple):
    """Indicates that we are using the bare repo dataset."""

    repo_list_path: Path
    """The path pointing to the bare repo list.

    e.g., `/mnt/efs/spark-data/shared/next-edit/300K_repos`
    """


class FromPRsV1(NamedTuple):
    """Indicates that we are using the PR v1 dataset."""

    pr_data_path: Path
    """The path pointing to the PR v1 dataset.

    e.g., `/mnt/efs/spark-data/shared/gh_pr_train_repartitioned`
    """

    pr_config: PRToRepoChangeConfig


class FromPRsV2(NamedTuple):
    """Indicates that we are using the PR v2 dataset."""

    pr_data_path: Path
    """The path pointing to the PR v2 dataset.

    e.g., `/mnt/efs/spark-data/shared/pr_v2/pr_grouped_10k`
    """


class FromOrderedChanges(NamedTuple):
    """Indicates that we are using the OrderedRepoChanges dataset."""

    pr_data_path: Path
    """The path pointing to the repo changes sampled from PRs.

    e.g., `/mnt/efs/spark-data/shared/next-edit/stage0/gh_pr_train_repartitioned /repo_changes`
    """

    grouping_data_path: Path
    """The path pointing to the PR grouping data.

    e.g., `/mnt/efs/spark-data/shared/next-edit-grouping/G1.0_hunks.lt30.v2`
    """


def get_quicktest_prs_v2_data():
    test_data_path = Path("/mnt/efs/spark-data/shared/pr_v2/pr_grouped_100")
    if not test_data_path.exists():
        test_data_path.mkdir(parents=True, exist_ok=False)
        all_parquets = list(
            Path("/mnt/efs/spark-data/shared/pr_v2/pr_grouped_10k").glob("*.parquet")
        )
        for parquet in all_parquets[:100]:
            shutil.copy(parquet, test_data_path)
    return test_data_path


def download_raw_repo_data(repo_list_path: Path, num_repos: int) -> Path:
    if not repo_list_path.exists():
        save_bare_repo_list_disk(repo_list_path, max_repos=num_repos)
    return repo_list_path


DataSource = FromGitRepos | FromPRsV1 | FromPRsV2 | FromOrderedChanges

NEXT_EDIT_SPARK_ROOT = Path("/mnt/efs/spark-data/shared/next-edit")
"""Disk path in which the next-edit pipeline data is stored."""

NEXT_EDIT_IDATA_ROOT = Path("/mnt/efs/augment/data/processed/next-edit/")
"""Disk path in which the next-edit indexed dataset is stored."""


def get_quicktest_data(
    data_name: Literal["pr_v1", "pr_v2", "raw_repos", "ordered_pr_v1"],
):
    # Note that we use different sampler parameters for different datasets
    # to ensure that we reach a desired positive:negative example ratio.
    # Otherwise, we may lose too much data during the rebalancing step.
    match data_name:
        case "pr_v2":
            data_source = FromPRsV2(pr_data_path=get_quicktest_prs_v2_data())
            sampler = EditGenSampler(
                random_edit_region_rate=0.4, random_target_file_rate=0.125
            )
        case "pr_v1":
            data_source = FromPRsV1(
                pr_data_path=Path("/mnt/efs/spark-data/shared/gh_pr_tiny"),
                pr_config=PRToRepoChangeConfig(),
            )
            sampler = EditGenSampler()
        case "raw_repos":
            data_source = FromGitRepos(
                repo_list_path=download_raw_repo_data(
                    NEXT_EDIT_SPARK_ROOT / "1K_repos", 1_000
                )
            )
            sampler = EditGenSampler()
        case "ordered_pr_v1":
            data_source = FromOrderedChanges(
                pr_data_path=Path(
                    "/mnt/efs/spark-data/shared/next-edit/stage0/quicktest-gh_pr_tiny/repo_changes"
                ),
                grouping_data_path=Path(
                    "/mnt/efs/spark-data/shared/next-edit-grouping/G1.0_hunks.lt30.v2"
                ),
            )
            sampler = EditGenSampler(
                random_edit_region_rate=0.3, random_target_file_rate=0.15
            )
        case _:
            assert_never(data_name)
    return data_source, sampler


def get_stage_output_path(
    data_source_name: str, stage_id: int, combined_config_str: str
) -> Path:
    """Returns the path to a stage's output."""
    return (
        NEXT_EDIT_SPARK_ROOT
        / f"stage{stage_id}/{data_source_name}/{combined_config_str}"
    )


def get_all_stage_output_paths(
    data_source: DataSource,
    sampling_config_str: str,
    retrieval_config_str: str,
    prompt_config_str: str,
    idata_config_str: str,
    is_quicktest: bool,
) -> Sequence[Path]:
    """Returns the paths to all stages' outputs."""
    data_source_name = get_data_source_name(data_source)
    if is_quicktest:
        data_source_name = "quicktest-" + data_source_name

    sampling_config_str = f"S{SamplingConfig.VERSION}_{sampling_config_str}"
    retrieval_config_str = (
        f"{sampling_config_str},R{RetrievalConfig.VERSION}_{retrieval_config_str}"
    )
    prompt_config_str = (
        f"{retrieval_config_str},P{PromptConfig.VERSION}_{prompt_config_str}"
    )
    idata_config_str = f"{prompt_config_str}-{idata_config_str}"

    stage0_output_path = get_stage_output_path(data_source_name, 0, "repo_changes")
    stage1_output_path = get_stage_output_path(data_source_name, 1, sampling_config_str)
    stage2_output_path = get_stage_output_path(
        data_source_name, 2, retrieval_config_str
    )
    stage3_output_path = get_stage_output_path(data_source_name, 3, prompt_config_str)
    idata_save_path = NEXT_EDIT_IDATA_ROOT / data_source_name / idata_config_str

    output_paths = [
        stage0_output_path,
        stage1_output_path,
        stage2_output_path,
        stage3_output_path,
        idata_save_path,
    ]
    for id, path in enumerate(output_paths[:-1]):
        print_green(f"Stage {id} output path: {path}")
    print_green(f"Indexed dataset will be saved to: {idata_save_path}")
    return output_paths


# TODO: Create a systematic way to get data source name.
def get_data_source_name(data_source: DataSource) -> str:
    """Returns a name for the data source."""
    if isinstance(data_source, FromGitRepos):
        data_source_name = data_source.repo_list_path.name
    elif isinstance(data_source, FromPRsV1):
        data_source_name = data_source.pr_data_path.name
    elif isinstance(data_source, FromPRsV2):
        data_source_name = "prv2-" + data_source.pr_data_path.name
    elif isinstance(data_source, FromOrderedChanges):
        assert data_source.pr_data_path.name == "repo_changes"
        data_source_name = "ordered-" + data_source.pr_data_path.parent.name
    else:
        assert_never(data_source)
    return data_source_name


def get_initial_stage_input_path(data_source: DataSource) -> Path:
    """Returns the path to the data source."""
    if isinstance(data_source, FromGitRepos):
        data_source_path = data_source.repo_list_path
    elif isinstance(data_source, (FromPRsV1 | FromPRsV2 | FromOrderedChanges)):
        data_source_path = data_source.pr_data_path
    else:
        assert_never(data_source)
    return data_source_path


def create_stage0(max_workers: int, data_source: DataSource, is_quicktest: bool):
    def _stage0(stage0_input_path: Path, stage0_output_path: Path):
        with get_session(
            use_gpu=False, max_workers=max_workers, name=stage0_output_path.name
        ) as spark:
            if isinstance(data_source, FromGitRepos):
                stage0_result = s3_repo_to_repo_changes_stage(
                    str(stage0_input_path),
                    str(stage0_output_path),
                    task_info_location=stage0_output_path.with_name(
                        f"{stage0_output_path.name}.logs"
                    ),
                    quicktest=is_quicktest,
                    spark=spark,
                )
            elif isinstance(data_source, FromPRsV1):
                stage0_result = pr_data_v1_to_repo_changes_stage(
                    input_path=str(stage0_input_path),
                    output_path=str(stage0_output_path),
                    pr_config=data_source.pr_config,
                    task_info_location=stage0_output_path.with_name(
                        f"{stage0_output_path.name}.logs"
                    ),
                    quicktest=is_quicktest,
                    spark=spark,
                )
            elif isinstance(data_source, FromPRsV2):
                stage0_result = pr_data_v2_to_repo_changes_stage(
                    input_path=str(stage0_input_path),
                    output_path=str(stage0_output_path),
                    task_info_location=stage0_output_path.with_name(
                        f"{stage0_output_path.name}.logs"
                    ),
                    quicktest=is_quicktest,
                    spark=spark,
                )
            elif isinstance(data_source, FromOrderedChanges):
                repos_per_file = 5 if not is_quicktest else 1
                stage0_result = grouped_to_ordered_repo_changes_wrapper(
                    repo_change_dir=str(stage0_input_path),
                    grouping_data_dir=str(data_source.grouping_data_path),
                    output_path=str(stage0_output_path),
                    task_info_location=stage0_output_path.with_name(
                        f"{stage0_output_path.name}.logs"
                    ),
                    spark=spark,
                    repos_per_file=repos_per_file,
                )
            else:
                assert_never(data_source)
            print("Stage0 result:", stage0_result)

    return _stage0


def create_stage1(
    sampling_config: SamplingConfig, max_workers: int, data_source: DataSource
):
    def _stage1(stage1_input_path: Path, stage1_output_path: Path):
        with get_session(
            use_gpu=False, max_workers=max_workers, name=stage1_output_path.name
        ) as spark:
            task_info_location = stage1_output_path.with_name(
                f"{stage1_output_path.name}.logs"
            )
            if isinstance(data_source, FromOrderedChanges):
                stage1_result = ordered_repo_changes_to_problems_stage(
                    str(stage1_input_path),
                    str(stage1_output_path),
                    task_info_location=task_info_location,
                    config=sampling_config,
                    timing_run=False,
                    spark=spark,
                )
            else:
                stage1_result = repo_changes_to_problems_stage(
                    str(stage1_input_path),
                    str(stage1_output_path),
                    task_info_location=task_info_location,
                    config=sampling_config,
                    timing_run=False,
                    spark=spark,
                )
            print("stage1 result:", dict(stage1_result))

    return _stage1


def create_stage2(retrieval_config: RetrievalConfig, max_workers: int):
    def _stage2(stage2_input_path: Path, stage2_output_path: Path):
        use_gpu = not retrieval_config.skip_dense_retrieval
        with get_session(
            use_gpu, max_workers=max_workers, name=stage2_output_path.name
        ) as spark:
            stage2_result = dense_retrieval_stage(
                str(stage2_input_path),
                str(stage2_output_path),
                retrieval_config,
                task_info_location=stage2_output_path.with_name(
                    f"{stage2_output_path.name}.logs"
                ),
                spark=spark,
                use_compressed_pickles=False,
            )
            print("stage2 result:", dict(stage2_result))

    return _stage2


def create_stage3(prompt_config: PromptConfig, max_workers: int):
    def _stage3(stage3_input_path: Path, stage3_output_path: Path):
        with get_session(
            use_gpu=False, max_workers=max_workers, name=stage3_output_path.name
        ) as spark:
            stage3_result = format_as_tokens_stage(
                str(stage3_input_path),
                str(stage3_output_path),
                prompt_config,
                task_info_location=stage3_output_path.with_name(
                    f"{stage3_output_path.name}.logs"
                ),
                spark=spark,
            )
            print("stage3 result:", dict(stage3_result))

    return _stage3


def create_final_stage(
    pad_to_length: int,
    positive_ratio: float,
    tokenizer_name: str,
    unmask_tokens: bool,
):
    def _final_stage(final_stage_input_path: Path, final_stage_output_path: Path):
        tokenizer = create_tokenizer_by_name(tokenizer_name)
        special = tokenizer.special_tokens
        assert isinstance(special, NextEditGenSpecialTokens)
        save_stage3_results_as_datasets(
            Random(42),
            final_stage_input_path,
            final_stage_output_path,
            {"train": 0.9, "valid": 0.1},
            pad_to_length=pad_to_length,
            tokenizer=tokenizer,
            special=special,
            positive_ratio=positive_ratio,
            unmask_tokens=unmask_tokens,
        )

    return _final_stage


def run_cached_stages(
    stages: Sequence[Callable[[Path, Path], None]],
    result_paths: Sequence[Path],
    first_stage_id: int = 0,
):
    """Run all stages in order.

    Each stage is a functions that takes in the input and output paths.
    The first entry in `result_paths` is the input path for the first stage, and the
    last entry in `result_paths` is the output path for the last stage.
    """

    if len(result_paths) != len(stages) + 1:
        raise ValueError(f"{len(stages)=}, {len(result_paths)=}.")

    # temporary fix for kubeconfig
    import os

    os.environ["KUBECONFIG"] = "/home/<USER>/.kube/config"
    # surpress spark noise to keep logs clean
    configure_server_logging(log_level="WARNING")

    for idx, stage in enumerate(stages):
        input_path = result_paths[idx]
        output_path = result_paths[idx + 1]
        cached_stage(name=f"stage{idx + first_stage_id}", output_path=output_path)(
            lambda: stage(input_path, output_path)
        )


def round_up_seq_length(seq_len: int, factor: int = 128) -> int:
    """Round up sequence length to be a multiple of factor."""
    return math.ceil(seq_len / factor) * factor


ethanol_config: RetrieverConfigDict = {
    "scorer": {
        "name": "dense_scorer_v2_fbwd_neox",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/star_ethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000",
    },
    "chunker": {
        "name": "line_level",
        "max_lines_per_chunk": 40,
    },
    "query_formatter": {
        "name": "ethanol6_query",
        "max_tokens": 1023,
        "add_path": True,
        "add_suffix": True,
        "prefix_ratio": 0.9,
        "tokenizer_name": "StarCoderTokenizer",
    },
    "document_formatter": {
        "name": "ethanol6_document",
        "max_tokens": 999,
        "add_path": True,
        "add_prefix": False,
        "add_suffix": False,
        "tokenizer_name": "StarCoderTokenizer",
    },
}

edit_ethanol_config: RetrieverConfigDict = {
    "scorer": {
        "name": "dense_scorer_v2_fbwd_neox",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/star_ethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000",
        "tokenizer_name": "starcoder",
    },
    "chunker": {
        "name": "smart_line_level",
        "max_chunk_chars": 768,
    },
    "query_formatter": {
        "name": "base:ethanol6.16.1-query-embedding-add-selected-code-and-instructions",
        "tokenizer": "starcoder",
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "tokenizer": "starcoder",
    },
}


raven_retriever_config: RetrieverConfigDict = {
    "scorer": {
        "name": "dense_scorer_v2_fbwd",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/next-edit-gen-retrieval/ravenr1b.tied.S1.13.1,R1.3_edit_ethanol,Sc1.0_smart_chunks,T1.0",
        "tokenizer_name": "starcoder",
    },
    "chunker": {
        "name": "smart_line_level",
        "max_chunk_chars": 768,
    },
    "query_formatter": {
        "name": "next_edit_gen_query",
        "tokenizer": "starcoder",
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "tokenizer": "starcoder",
    },
}


raven_retriever_fp8_config: RetrieverConfigDict = {
    "scorer": {
        "name": "dense_scorer_ffwd_starcoder_fp8",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/next-edit-gen-retrieval/raven-retriever-v1-fp8",
        "checkpoint_sha256": "8552b328c825f5e7bb305c481f44c594182d2b2fe7b715f281b5cc7f62ffaf4e",
        "output_projection_dim": 512,
        "tokenizer_name": "starcoder",
    },
    "chunker": {
        "name": "smart_line_level",
        "max_chunk_chars": 768,
    },
    "query_formatter": {
        "name": "next_edit_gen_query",
        "tokenizer": "starcoder",
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "tokenizer": "starcoder",
    },
}


def quick_test_run(
    data_name: Literal["pr_v2", "pr_v1", "raw_repos", "ordered_pr_v1"],
    skip_retrieval: bool,
    clear_existing: bool = False,
    print_paths_only: bool = False,
):
    """Start a quick test run of the data generation pipelines."""
    data_source, sampler = get_quicktest_data(data_name)

    # --------------------------------------------------------
    # Begin specifying the stage parameters.
    # Params for stage 1.
    sampling_config_name = "quicktest_400p"
    sampling_config = SamplingConfig(
        sampler=sampler,
        max_problems_per_repo=400,
        timeout_per_repo=200,
    )

    # Params for stage 2.
    retrieval_config_name = "edit_ethanol-K24" if not skip_retrieval else "skip"
    retrieval_config = RetrievalConfig(
        retriever_config=edit_ethanol_config,
        num_retrieved_chunks=24,
        timeout_per_repo=300,
        skip_dense_retrieval=skip_retrieval,
    )

    # Params for stage 3.
    prompt_config_name = "star2_context9"
    prompt_config = PromptConfig(
        tokenizer_name="starcoder2",
        formatter_config=EditGenFormatterConfig(diff_context_lines=9),
    )

    # Params for final stage.
    positive_ratio = 0.70
    pad_to_length = round_up_seq_length(prompt_config.max_sequence_length()) + 1

    # --------------------------------------------------------
    # End specifying the stage parameters.

    stage0_input_path = get_initial_stage_input_path(data_source)
    stage_output_paths = get_all_stage_output_paths(
        data_source=data_source,
        sampling_config_str=sampling_config_name,
        retrieval_config_str=retrieval_config_name,
        prompt_config_str=prompt_config_name,
        idata_config_str=f"pos_{positive_ratio:.2f}-pad_{pad_to_length}",
        is_quicktest=True,
    )
    if print_paths_only:
        print("Exiting early since print_paths_only=True.")
        return

    if clear_existing:
        # Note that we do not clear stage 0 results since they rarely require a change
        for path in stage_output_paths[1:]:
            if path.exists():
                print_yellow(f"Removing existing data at: {path}")
                shutil.rmtree(path, ignore_errors=True)

    run_cached_stages(
        stages=[
            # TODO(vzhao): refactor `quicktest` to move the logic into the main
            # function. This helps reader understand what happens when `quicktest=True`.
            create_stage0(
                max_workers=32,
                data_source=data_source,
                is_quicktest=True,
            ),
            create_stage1(
                sampling_config=sampling_config,
                max_workers=32,
                data_source=data_source,
            ),
            create_stage2(
                retrieval_config=retrieval_config,
                max_workers=32,
            ),
            create_stage3(
                prompt_config=prompt_config,
                max_workers=32,
            ),
            create_final_stage(
                pad_to_length=pad_to_length,
                positive_ratio=positive_ratio,
                tokenizer_name=prompt_config.tokenizer_name,
                unmask_tokens=True,  # We will use FIM loss masking.
            ),
        ],
        result_paths=[stage0_input_path, *stage_output_paths],
    )


if __name__ == "__main__":
    # see also: experimental/jiayi/finetuning/make_edit_gen_dataset.py
    quick_test_run(
        data_name="pr_v2",
        skip_retrieval=False,
    )

"""Spark pipeline functions for retrieval for next edit generation (not localization).

This file implements Spark wrapper functions for the stages defined in
`research.next_edits.edit_gen_retrieval_stages`. See the `quick_test_run` function for an
example run config.
"""

import json
import logging
import pickle
from dataclasses import asdict
from pathlib import Path

import pandas as pd
from pyspark.sql import SparkSession

from base.prompt_format_next_edit.gen_prompt_formatter import EditGenFormatterConfig
from base.prompt_format_next_edit.retrieval_prompt_formatter import (
    EditGenRetrievalQueryFormatterConfig,
)
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.pipelines.utils.next_edit_common import (
    RunMode,
    run_create_indexed_dataset,
    smart_session,
    stage_block,
    to_token_array_identity,
)
from research.next_edits.edit_gen_retrieval_stages import (
    EditGenRetrievalProblem,
    FormatAsTokensConfig,
    ScoreChunksConfig,
    format_as_tokens,
    score_chunks,
)
from research.next_edits.edit_gen_stages import RagEditGenProblem
from research.utils.inspect_indexed_dataset import print_green
from research.utils.token_array_utils import TokenArray

logger = logging.getLogger(__name__)


def score_chunks_wrapper(
    input_path: Path | str,
    output_path: Path | str,
    task_info_location: Path | str,
    config: ScoreChunksConfig,
    spark: SparkSession,
    timing_run: bool = False,
    global_seed: int = 0,
) -> dict:
    """Spark wrapper around `score_chunks`.

    Generates a table where each row has the following columns:
        - repo_path: the name of the repo.
        - num_problems: the number of problems sampled from the repo.
        - pickled_results: the pickled list of (EditLocalizationProblem, EditLocalizationRetrievalProblem, score)

    Args:
        input_path: The path to the input parquet file.
        output_path: The path to the output parquet file.
        task_info_location: The path to the task info location.
        config: The config for the stage.
        spark: The spark session.
        timing_run: Whether to run in timing mode.
        global_seed: The global seed to use.

    Returns:
        Task info for the stage.
    """

    def process_batch(df: pd.DataFrame) -> pd.DataFrame:
        output_per_repo = dict[
            str,
            list[EditGenRetrievalProblem],
        ]()

        for row in df.to_dict(orient="records"):
            problems: list[RagEditGenProblem] = pickle.loads(row["pickled_results"])
            assert len(problems) == row["num_problems"]
            repo_path = row["repo_path"]

            output_per_repo.setdefault(repo_path, []).extend(
                score_chunks(
                    config,
                    problems,
                    seed=global_seed + hash(row["repo_path"]),
                )
            )

        return pd.DataFrame(
            [
                {
                    "repo_path": repo_path,
                    "num_problems": len(output),
                    "pickled_results": pickle.dumps(output),
                }
                for repo_path, output in output_per_repo.items()
                if output
            ]
        )

    return map_parquet.apply_pandas(
        spark,
        process_batch,
        str(input_path),
        str(output_path),
        task_info_location=str(task_info_location),
        batch_size=1,
        input_columns=["repo_path", "num_problems", "pickled_results"],
        timeout=3600,  # 1 hour is more than enough for this stage.
        ignore_error=True,
        timing_run=timing_run,
    )


def run_score_chunks(
    input_path: Path | str,
    output_root: Path | str,
    short_name: str,
    config: ScoreChunksConfig,
    max_workers: int = 32,
    mode: RunMode = "",
    overwrite: bool = False,
    global_seed: int = 0,
    spark: SparkSession | None = None,
) -> tuple[Path, SparkSession | None]:
    """Run stage: Score chunks.

    Args:
        input_path: The path to the input parquet file.
        output_root: The root path to the output directory.
        short_name: The short name of the stage.
        config: The config for the stage.
        max_workers: The maximum number of workers to use.
        mode: The run mode.
        overwrite: Whether to overwrite existing output.
        global_seed: The global seed to use
        spark: The spark session.

    Returns:
        The output path and spark session.
    """
    name = f"{Path(input_path).name},Sc{config.VERSION}_{short_name}"

    output_data_path = Path(output_root) / name
    with stage_block(output_data_path, mode=mode, overwrite=overwrite) as (
        output_data_path,
        output_log_path,
        completed,
    ):
        if completed:
            return output_data_path, spark

        if not spark:
            spark = smart_session(
                name,
                max_workers=max_workers,
                mode=mode,
                gpu_type=(["RTX_A4000", "RTX_A5000", "RTX_A6000", "A40"]),
            )

        # Save the full stage configuration to the log directory.
        output_log_path.mkdir(parents=True, exist_ok=True)
        with (Path(output_log_path) / "config.json").open("w") as f:
            json.dump(asdict(config), f, indent=2)
        result = score_chunks_wrapper(
            input_path,
            output_data_path,
            output_log_path,
            config,
            spark,
            timing_run=bool(mode),
            global_seed=global_seed,
        )
        logger.info(result)

    return output_data_path, spark


def format_as_tokens_wrapper(
    input_path: Path | str,
    output_path: Path | str,
    task_info_location: Path | str,
    config: FormatAsTokensConfig,
    spark: SparkSession,
    timing_run: bool = False,
    global_seed: int = 0,
) -> dict:
    """Stage Format as tokens.

    This is a spark wrapper around `format_as_tokens` and generates a table
    where each row has the following columns:
        - id: a unique identifier for the batch of problems.
        - repo_path: the name of the repo.
        - num_problems: the number of problems sampled from the repo.
        - pickled_results: the pickled list of token arrays.

    The output of this stage is intended to be used to directly create an
    IndexedDataset.

    Args:
        input_path: The path to the input parquet file.
        output_path: The path to the output parquet file.
        task_info_location: The path to the task info location.
        config: The config for the stage.
        spark: The spark session.
        timing_run: Whether to run in timing mode.
        global_seed: The global seed to use.

    Returns:
        Task info for the stage.
    """

    def process_batch(df: pd.DataFrame):
        output_per_repo = dict[str, list[TokenArray]]()
        for row in df.to_dict(orient="records"):
            problems: list[EditGenRetrievalProblem] = pickle.loads(
                row["pickled_results"]
            )
            assert len(problems) == row["num_problems"]
            repo_path = row["repo_path"]
            output_per_repo.setdefault(repo_path, []).extend(
                format_as_tokens(
                    config, problems, seed=global_seed + hash(row["repo_path"])
                )
            )
        return pd.DataFrame(
            [
                {
                    "repo_path": repo_path,
                    "num_problems": len(output),
                    "pickled_results": pickle.dumps(output),
                }
                for repo_path, output in output_per_repo.items()
            ]
        )

    return map_parquet.apply_pandas(
        spark,
        process_batch,
        str(input_path),
        str(output_path),
        task_info_location=str(task_info_location),
        batch_size=1,
        input_columns=["repo_path", "num_problems", "pickled_results"],
        timeout=3600,  # 1 hour is more than enough for this stage.
        ignore_error=True,
        timing_run=timing_run,
    )


def run_format_as_tokens(
    input_path: Path | str,
    output_root: Path | str,
    short_name: str,
    config: FormatAsTokensConfig,
    max_workers: int = 32,
    mode: RunMode = "",
    overwrite: bool = False,
    global_seed: int = 0,
    spark: SparkSession | None = None,
) -> tuple[Path, SparkSession | None]:
    """Run stage: Format as tokens.

    Args:
        input_path: The path to the input parquet file.
        output_root: The root path to the output directory.
        short_name: The short name of the stage.
        config: The config for the stage.
        max_workers: The maximum number of workers to use.
        mode: The run mode.
        overwrite: Whether to overwrite existing output.
        global_seed: The global seed to use.
        spark: The spark session.

    Returns:
        The path to the output parquet file.
    """
    name = f"{Path(input_path).name},T{config.VERSION}_{short_name}"
    output_data_path = Path(output_root) / name

    with stage_block(output_data_path, mode=mode, overwrite=overwrite) as (
        output_data_path,
        output_log_path,
        completed,
    ):
        if completed:
            return output_data_path, spark

        if not spark:
            spark = smart_session(name, max_workers=max_workers, mode=mode)

        # Save the full stage configuration to the log directory.
        output_log_path.mkdir(parents=True, exist_ok=True)
        with (Path(output_log_path) / "config.json").open("w") as f:
            json.dump(asdict(config), f, indent=2)

        result = format_as_tokens_wrapper(
            input_path,
            output_data_path,
            output_log_path,
            config,
            spark,
            timing_run=bool(mode),
            global_seed=global_seed,
        )
        logger.info(result)

    return output_data_path, spark


PR_TRAIN_DATA_PATH = "/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_sorted_train"
PR_VALIDATION_DATA_PATH = (
    "/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_sorted_validation"
)
DEFAULT_OUTPUT_ROOT = "/mnt/efs/spark-data/shared/next-edit-gen-retrieval"
TMP_OUTPUT_ROOT = "/mnt/efs/spark-data/temp-weekly/arun/next-edit-location"


def quick_test_run():
    """Start a quick test run of the data generation pipelines."""
    logging.basicConfig(level=logging.INFO, force=True)

    # TODO(arun): Connect the edit gen rag stage here.
    rag_edit_problems_path = Path(
        "/mnt/efs/spark-data/shared/next-edit/"
        "stage2/gh_pr_train_repartitioned/S1.13.1_6000p_2000f,R1.3_edit_ethanol_synth_instruct_k128"
    )
    mode = "local"
    spark = None
    scored_problems_path, spark = run_score_chunks(
        input_path=rag_edit_problems_path,
        output_root=TMP_OUTPUT_ROOT,
        short_name="smart_chunks_1280",
        config=ScoreChunksConfig(
            checkpoint_path=(
                "/mnt/efs/augment/checkpoints/"
                "next-edit-gen/S1.13.1-R1.3_edit_ethanol_synth_instruct-P1.10.1_context12-gh_pr_train_repartitioned-starcoder2_7b-ffwd"
            ),
            formatter_config=EditGenFormatterConfig(
                diff_context_lines=12,
                max_prompt_tokens=1750,
                section_budgets={
                    "prefix_tks": 100,
                    "suffix_tks": 100,
                    "filename_tks": 50,
                    "instruction_tks": 100,
                    "diff_tks": 300,
                    "retrieval_tks": 1000,
                },
            ),
            downsample_retrieval_rate=0.0,
            drop_instruction_rate=0.5,
            downsample_no_change_rate=0.0,
        ),
        mode=mode,
        spark=spark,
        overwrite=False,
    )
    token_arrays_path, spark = run_format_as_tokens(
        input_path=scored_problems_path,
        output_root=TMP_OUTPUT_ROOT,
        short_name="format_as_tokens",
        config=FormatAsTokensConfig(
            query_formatter_config=EditGenRetrievalQueryFormatterConfig(
                diff_context_lines=12,
            ),
            document_formatter_config={
                "max_content_length": 1024,
                "add_path": True,
            },
        ),
        mode=mode,
        spark=spark,
        overwrite=True,
    )
    indexed_dataset_output_path = run_create_indexed_dataset(
        input_path=token_arrays_path,
        output_root=DEFAULT_OUTPUT_ROOT,
        tokenizer_name="starcoder",
        overwrite=True,
    )

    print_green(f"Token arrays saved to: {indexed_dataset_output_path}")


if __name__ == "__main__":
    # see also: experimental/next_edits/data_configs/run_edit_gen_retrieval_pipeline.py
    quick_test_run()

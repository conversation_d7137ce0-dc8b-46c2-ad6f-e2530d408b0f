"""Spark pipeline functions for next edit location.

This file implements Spark wrapper functions for the stages defined in
`research.next_edits.edit_localization_stages`. See the `quick_test_run` function for an
example run config.

Note that we use a simple versioning system to separately version each pipeline stage,
and `run_stages` then saves each stage's result using a directory name based on the
version numbers + configs of *all* previous stages. This way, any unchanged stages'
result will automatically be reused by `run_stages`, hence only the stages that
have changed since last run will be run.
"""

import hashlib
import json
import logging
import pickle
import re
from collections.abc import Sequence
from dataclasses import asdict, dataclass
from pathlib import Path
import shutil
from typing import Literal, Mapping, cast

import jinja2
import pandas as pd
from dataclasses_json import DataClassJsonMixin
from pyspark.sql import SparkSession
from tqdm.auto import tqdm

from base.caching.lru_cache import lru_cache
from base.diff_utils.diff_formatter import DiffHunk, format_file_changes_with_ranges
from base.diff_utils.edit_events import SingleFileEdit
from base.languages.language_guesser import guess_language
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.pipelines.utils.next_edit_common import (
    PRToRepoChangeConfig,
    pr_to_repo_change,
    smart_session,
    stage_block,
)
from research.data.spark.pipelines.utils.prs_dataset import (
    PRData,
    RepositoryFileManager,
)
from research.llm_apis.chat_utils import (
    GeminiVertexAIChatClient,
)
from research.next_edits.next_edits_dataset import (
    GroupedEdits,
    OrderedRepoChange,
    PRMeta,
    RepoChangeWithMeta,
)
from research.utils.inspect_indexed_dataset import print_green
from research.utils.repo_change_utils import (
    CommitMeta,
    RepoChange,
)
from research.utils import unpickler

logger = logging.getLogger(__name__)


RunMode = Literal["", "timing", "local"]


# Cache this resource across multiple batches in the same process.
@lru_cache()
def create_repo_file_manager(
    pr_repo_reference_file: Path | str,
) -> RepositoryFileManager:
    """Create a repository file manager."""
    return RepositoryFileManager(Path(pr_repo_reference_file))


@dataclass
class GroupedChange:
    description: str
    changes: Sequence[DiffHunk]

    def pprint(self):
        print(f"Description: {self.description}")
        for i, hunk in enumerate(self.changes):
            print(f"Hunk {i+1}:\n{hunk.text_with_header()}")


def create_prompt_template():
    return jinja2.Template("""
    You are a helpful coding assistant assisting a software engineer to order the \
    changes they have made to their codebase.

    Here are the changes the engineer made:
    {% for hunk in hunks %}
    Hunk #{{loop.index}}:
    ```
    {{hunk}}
    ```
    {% endfor %}

    Here is the commit message they want to use:

    {{title}}
    {{body}}

    What are the high-level changes made in the above diff?

    Group the diff hunks according to these high-level changes and arrange them in the \
    order in which they would have been implemented. The output should use the following \
    format:

    1. (description of Group 1): (first hunk# of group 1), (second hunk# of group 1) ...
    2. (description of Group 2): (first hunk# of group 2), (second hunk# of group 2) ...
    ...

    Guidelines:
    * Every hunk should be part of at least one group.
    * Order definitions before their uses, and uses before their imports.
    * Implementations and tests should be ordered next to each other.
    * Order each semantically related change together, e.g. definitions, uses and tests. A\
    single file may need to be split into multiple groups.
    * Avoid generic groups like "refactoring and cleanup" or "improve readability".
    * Only output the list of groups and hunks.
    """)


@dataclass
class RepoChangeToGroupsConfig(DataClassJsonMixin):
    """Configuration for RepoChange to Groups."""

    max_repo_size_files: int = 2_000
    """Repos with more than this many after_files will be filtered out."""

    min_diff_hunks: int = 1
    """Ignore PRs with less than this many hunks."""

    max_diff_hunks: int = 30
    """Ignore PRs with more than this many hunks."""

    VERSION = "1.0"
    """The version of the grouping config.

    Should be updated after each incompatible change to the grouping stage.

    Changelog:
        - 1.0: First version -- first version of the prompt.
    """


def get_llm_client() -> GeminiVertexAIChatClient:
    return GeminiVertexAIChatClient()


@dataclass
class GroupedRepoChanges:
    pr_meta: PRMeta
    commit_meta: CommitMeta
    diff_hunks: Sequence[DiffHunk]
    groups: Sequence[GroupedChange]
    prompt: str
    response: str

    def to_ordered_repo_change(
        self, repo_change: RepoChange
    ) -> "OrderedRepoChange | None":
        def hunk_to_edit(hunk: DiffHunk) -> SingleFileEdit | None:
            if hunk.before_path == "/dev/null":
                before_text = ""
                before_path = None
            else:
                before_path = Path(hunk.before_path)
                if before_path not in repo_change.before_files:
                    logger.warning("File %s not found in before_files", before_path)
                    return None
                before_text = repo_change.before_files[before_path]
                before_path = str(before_path)
            if hunk.after_path == "/dev/null":
                after_text = ""
                after_path = None
            else:
                after_path = Path(hunk.after_path)
                if after_path not in repo_change.after_files:
                    logger.warning("File %s not found in after_files", after_path)
                    return None
                after_text = repo_change.after_files[after_path]
                after_path = str(after_path)
            if before_text is None or after_text is None:
                return None
            return SingleFileEdit(
                before_start=hunk.before_crange.start,
                before_text=before_text[hunk.before_crange.to_slice()],
                after_start=hunk.after_crange.start,
                after_text=after_text[hunk.after_crange.to_slice()],
                before_path=before_path,
                after_path=after_path,
            )

        # we deduplicate the hunks in case there are duplicates
        seen_hunks = set[DiffHunk]()

        edit_groups = list[GroupedEdits]()
        for group in self.groups:
            edits = list[SingleFileEdit]()
            for hunk in group.changes:
                if hunk in seen_hunks:
                    continue
                edit = hunk_to_edit(hunk)
                if edit is None:
                    return None
                edits.append(edit)
                seen_hunks.add(hunk)
            if edits:
                edit_groups.append(GroupedEdits(group.description, tuple(edits)))

        return OrderedRepoChange(
            repo_change,
            commit_meta=self.commit_meta,
            pr_meta=self.pr_meta,
            edit_groups=tuple(edit_groups),
        )

    def pprint(self):
        print(f"PR: {self.pr_meta}")
        print(f"Commit: {self.commit_meta}")
        for group in self.groups:
            group.pprint()


def repo_change_to_groups(
    past_to_future_rc: RepoChange,
    pr_meta: PRMeta,
    commit_meta: CommitMeta,
    config: RepoChangeToGroupsConfig,
) -> list[GroupedRepoChanges]:
    diff_hunks = format_file_changes_with_ranges(
        [f.map(lambda x: x.to_file()) for f in past_to_future_rc.changed_files],
        diff_context_lines=5,
    )
    if (
        len(diff_hunks) < config.min_diff_hunks
        or len(diff_hunks) > config.max_diff_hunks
    ):
        logger.info(
            "Skipping %s: %d hunks not in [%d, %d]",
            pr_meta,
            len(diff_hunks),
            config.min_diff_hunks,
            config.max_diff_hunks,
        )
        return []

    def quote_text(text: str) -> str:
        return "\n".join("> " + line for line in text.splitlines())

    prompt = (
        create_prompt_template()
        .render(
            hunks=[diff.text_with_header() for diff in diff_hunks],
            title=quote_text(pr_meta.title),
            description=quote_text(pr_meta.body),
        )
        .strip()
    )

    response = get_llm_client().generate([prompt], max_tokens=1000)
    groups = parse_output(response, diff_hunks)

    return [
        GroupedRepoChanges(
            pr_meta=pr_meta,
            commit_meta=commit_meta,
            diff_hunks=diff_hunks,
            groups=groups,
            prompt=prompt,
            response=response,
        )
    ]


@lru_cache()
def load_grouped_changes(
    grouping_data_dir: Path | str,
) -> Mapping[PRMeta, GroupedRepoChanges]:
    """Load the grouped changes from disk."""

    def module_remap(module_name: str) -> str:
        # for backward compatibility with older data
        if module_name == "research.next_edits.edit_grouping_stages":
            return "research.data.spark.pipelines.stages.next_edit_grouping_pipelines"
        return module_name

    grouping_data_dir = Path(grouping_data_dir)
    parquet_files = list(grouping_data_dir.glob("*.parquet"))
    pr_to_grouped_changes = dict[PRMeta, GroupedRepoChanges]()
    for file in parquet_files:
        df = pd.read_parquet(file)
        for row in df.to_dict(orient="records"):
            groups: list[GroupedRepoChanges] = unpickler.loads(
                row["pickled_results"], module_remap
            )
            for grc in groups:
                pr_to_grouped_changes[grc.pr_meta] = grc
    return pr_to_grouped_changes


def grouped_to_ordered_repo_changes_wrapper(
    repo_change_dir: Path | str,
    grouping_data_dir: Path | str,
    output_path: Path | str,
    task_info_location: Path | str,
    spark: SparkSession,
    timing_run: bool = False,
    timeout: float = 1800,
    repos_per_file: int = 10,
) -> dict:
    """Convert repo changes into OrderedRepoChanges.

    The input path should contain a dataframe with the following columns:
        - repo_path: the path to the repo tar file
        - pickled_results: the pickled list of `RepoChangeWithMeta`s

    And the output will contain a dataframe with the following columns:
        - repo_path: the path to the repo tar file
        - pickled_results: the pickled list of `OrderedRepoChange`s
        - num_prs: the number of PRs in `picked_results`
    """

    def process_repo(repo_path: str, pickled_results: bytes):
        repo_changes: list[RepoChangeWithMeta] = pickle.loads(pickled_results)
        groupped_changes = load_grouped_changes(grouping_data_dir)
        ordered_repo_changes = [
            order_rc
            for rc in repo_changes
            if rc.pr_meta
            and rc.pr_meta in groupped_changes
            and (
                order_rc := groupped_changes[rc.pr_meta].to_ordered_repo_change(
                    rc.repo_change
                )
            )
            is not None
        ]
        columns = {
            "repo_path": repo_path,
            "pickled_results": pickle.dumps(ordered_repo_changes),
            "num_prs": len(ordered_repo_changes),
        }
        return pd.Series(columns)

    tmp_output_path = Path(output_path) / "tmp"

    spark_result = map_parquet.apply(
        spark,
        process_repo,
        str(repo_change_dir),
        str(tmp_output_path),
        task_info_location=str(task_info_location),
        batch_size=1,
        input_columns=["repo_path", "pickled_results"],
        timeout=timeout,
        ignore_error=True,
        timing_run=timing_run,
    )
    # repartition result to be one parquet file per repo
    parquets = list(tmp_output_path.glob("*.parquet"))
    new_parquet_id = 0
    n_repos = 0
    current_df_rows = list[dict]()
    for parquet in tqdm(parquets, desc="repartitioning", unit="parquet", smoothing=0):
        df = pd.read_parquet(parquet)
        for row in df.to_dict(orient="records"):
            num_prs = row["num_prs"]
            if num_prs == 0:
                continue
            current_df_rows.append(row)
            n_repos += 1
            if len(current_df_rows) >= repos_per_file:
                new_df = pd.DataFrame(current_df_rows)
                new_parquet_path = Path(output_path) / f"part-{new_parquet_id}.parquet"
                new_df.to_parquet(new_parquet_path)
                new_parquet_id += 1
                current_df_rows = list[dict]()
    # save the last batch of repos
    if current_df_rows:
        new_df = pd.DataFrame(current_df_rows)
        new_parquet_path = Path(output_path) / f"part-{new_parquet_id}.parquet"
        new_df.to_parquet(new_parquet_path)
        new_parquet_id += 1
    shutil.rmtree(tmp_output_path)
    logging.info(f"Finished with data from {n_repos} repos.")
    logging.info(f"Data saved to: {output_path}")
    return spark_result


def pr_to_groups_wrapper(
    input_path: Path | str,
    output_path: Path | str,
    task_info_location: Path | str,
    data_config: PRToRepoChangeConfig,
    config: RepoChangeToGroupsConfig,
    spark: SparkSession,
    timing_run: bool = False,
) -> dict:
    """Sample problems from the PR dataset.

    This is a spark wrapper around `repo_to_problems` and generates a row
    for each input repo, with the following 3 columns:
        - id: a unique identifier for the batch of problems.
        - repo_name: the name of the repo.
        - num_problems: the number of problems sampled from the repo.
        - pickled_results: the pickled list of `EditLocalizationProblem`s

    Args:
        input_path: The path to the input parquet file.
        output_path: The path to the output parquet file.
        task_info_location: The path to the task info file.
        data_config: The PR to RepoChange conversion config.
        config: The RepoChange to problems conversion config.
        spark: The spark session.
        timing_run: Whether this is a timing run.
        global_seed: The global seed to use.

    Returns:
        The task info dict.
    """

    def process_batch(df: pd.DataFrame):
        repo_file_manager = RepositoryFileManager(
            Path(data_config.pr_repo_reference_file)
        )

        problems_per_repo = dict[str, list[GroupedRepoChanges]]()

        for pr in df.to_dict(orient="records"):
            tup = pr_to_repo_change(
                cast(PRData, pr),
                repo_file_manager,
                data_config,
                max_repo_size_files=config.max_repo_size_files,
                file_filter=lambda path: bool(guess_language(path)),
            )
            if tup is None:
                continue
            (past_to_future_repo_change, pr_meta, commit_meta) = tup
            problems_per_repo.setdefault(pr_meta.repo_name, []).extend(
                repo_change_to_groups(
                    past_to_future_repo_change,
                    pr_meta,
                    commit_meta,
                    config,
                )
            )

        # Get a unique id for a single batch of problems as there might be multiple
        # batches with the same repo name.
        def get_id(problems: list[GroupedRepoChanges]) -> str:
            return hashlib.sha256(
                (b",".join(bytes(problem.pr_meta.pr_number) for problem in problems))
            ).hexdigest()

        return pd.DataFrame(
            [
                {
                    "id": f"{repo_name}/{get_id(problems)}",
                    "repo_name": repo_name,
                    "num_problems": len(problems),
                    "pickled_results": pickle.dumps(problems),
                }
                for repo_name, problems in problems_per_repo.items()
                if problems
            ]
        )

    return map_parquet.apply_pandas(
        spark_session=spark,
        pandas_func=process_batch,
        input_path=str(input_path),
        output_path=str(output_path),
        ignore_error=True,
        # Keep batch sizes small as storing the pickled results takes a lot of memory.
        batch_size=1 if timing_run else 10,
        task_info_location=str(task_info_location),
        # 1 hour for this stage is more than sufficient.
        timeout=3600,
        timing_run=timing_run,
    )


def run_pr_to_groups(
    pr_source_path: Path | str,
    output_root: Path | str,
    short_name: str,
    pr_config: PRToRepoChangeConfig,
    config: RepoChangeToGroupsConfig,
    max_workers: int = 32,
    mode: RunMode = "",
    overwrite: bool = False,
    spark: SparkSession | None = None,
) -> tuple[Path, SparkSession | None]:
    """Run the PR to problems stage.

    Args:
        pr_source_path: The path to the input parquet file of PRs grouped by repo.
        output_root: Output for parquet files with pickled `EditLocalizationProblem`s.
        short_name: The short name of the stage.
        pr_config: The PR to RepoChange conversion config.
        config: The RepoChange to problems conversion config.
        max_workers: The maximum number of workers to use.
        mode: The run mode.
        overwrite: Whether to overwrite existing files.
        global_seed: The global seed to use.
        spark: The spark session.

    Returns:
        The path to the output parquet file, and the spark session.
    """
    # Local runs are for debugging purposes, so we set the timing_run option.
    name = f"G{config.VERSION}_{short_name}"
    output_data_path = Path(output_root) / name

    with stage_block(output_data_path, mode=mode, overwrite=overwrite) as (
        output_data_path,
        output_log_path,
        completed,
    ):
        if completed:
            return output_data_path, spark

        if not spark:
            spark = smart_session(name, max_workers=max_workers, mode=mode)

        # Save the full stage configuration to the log directory.
        output_log_path.mkdir(parents=True, exist_ok=True)
        (Path(output_log_path) / "config.json").write_text(
            json.dumps(
                {
                    "pr_config": asdict(pr_config),
                    "config": asdict(config),
                },
                indent=2,
            )
        )
        result = pr_to_groups_wrapper(
            pr_source_path,
            output_data_path,
            output_log_path,
            pr_config,
            config,
            spark,
            timing_run=bool(mode),
        )
        logger.info(result)

    return output_data_path, spark


def parse_output(response: str, diff_hunks: Sequence[DiffHunk]) -> list[GroupedChange]:
    pattern = r"^(?P<num>\d+)\. \**(?P<group>[^:]+):?\**:? (?P<hunks>.*)$"

    seen_hunks = set()
    groups = []
    # Example usage:
    lines = response.splitlines()
    for line in lines:
        if match := re.match(pattern, line):
            description = match.group("group")
            hunks = [int(h) for h in re.findall(r"\d+", match.group("hunks"))]
            groups.append(
                GroupedChange(
                    description=description,
                    changes=[
                        diff_hunks[h - 1]
                        for h in hunks
                        if 1 <= h <= len(diff_hunks) and h not in seen_hunks
                    ],
                )
            )
            seen_hunks.update(hunks)
        else:
            logger.warning(f"Couldn't parse line: {line}")
    return groups


CHECKPOINT_ROOT = Path("/mnt/efs/augment/checkpoints")
RAVEN_ROOT = CHECKPOINT_ROOT / "next-edit-location"

PR_TRAIN_DATA_PATH = "/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_sorted_train"
PR_VALIDATION_DATA_PATH = (
    "/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_sorted_validation"
)
DEFAULT_OUTPUT_ROOT = "/mnt/efs/spark-data/user/arun/next-edit-location"
TMP_OUTPUT_ROOT = "/mnt/efs/spark-data/temp-weekly/arun/next-edit-location"


def quick_test_run():
    """Start a quick test run of the data generation pipelines."""
    logging.basicConfig(level=logging.INFO, force=True)

    problems_path, spark = run_pr_to_groups(
        pr_source_path=PR_TRAIN_DATA_PATH,
        output_root=TMP_OUTPUT_ROOT,
        short_name="hunks.lt30",
        pr_config=PRToRepoChangeConfig(),
        config=RepoChangeToGroupsConfig(),
        mode="local",
        overwrite=False,
    )
    print_green(f"Problems saved to: {problems_path}")


if __name__ == "__main__":
    quick_test_run()

"""Spark pipeline functions for processing GitHub PRs.

The schema of the input joined PR-files dataset:

root
 |-- repo_name: string (nullable = true)
 |-- sha: string (nullable = true)
 |-- id: string (nullable = true)
 |-- url: string (nullable = true)
 |-- number: long (nullable = true)
 |-- state: string (nullable = true)
 |-- title: string (nullable = true)
 |-- body: string (nullable = true)
 |-- user: struct (nullable = true)
 |    |-- login: string (nullable = true)
 |    |-- id: string (nullable = true)
 |    |-- type: string (nullable = true)
 |    |-- site_admin: string (nullable = true)
 |-- created_at: string (nullable = true)
 |-- merged: string (nullable = true)
 |-- merge_commit_sha: string (nullable = true)
 |-- requested_reviewers: string (nullable = true)
 |-- comments: long (nullable = true)
 |-- review_comments: long (nullable = true)
 |-- commits: long (nullable = true)
 |-- additions: long (nullable = true)
 |-- deletions: long (nullable = true)
 |-- changed_files: long (nullable = true)
 |-- reviews_array: array (nullable = true)
 |    |-- element: struct (containsNull = true)
 |    |    |-- state: string (nullable = true)
 |    |    |-- body: string (nullable = true)
 |    |    |-- commit_id: string (nullable = true)
 |    |    |-- submitted_at: string (nullable = true)
 |-- comments_array: array (nullable = true)
 |    |-- element: struct (containsNull = true)
 |    |    |-- id: string (nullable = true)
 |    |    |-- in_reply_to_id: string (nullable = true)
 |    |    |-- path: string (nullable = true)
 |    |    |-- diff_hunk: string (nullable = true)
 |    |    |-- body: string (nullable = true)
 |    |    |-- commit_id: string (nullable = true)
 |    |    |-- original_commit_id: string (nullable = true)
 |    |    |-- base_sha: string (nullable = true)
 |    |    |-- head_sha: string (nullable = true)
 |    |    |-- user: struct (nullable = true)
 |    |    |    |-- login: string (nullable = true)
 |    |    |    |-- id: string (nullable = true)
 |    |    |    |-- type: string (nullable = true)
 |    |    |    |-- site_admin: string (nullable = true)
 |    |    |-- updated_at: string (nullable = true)
 |-- base: struct (nullable = true)
 |    |-- label: string (nullable = true)
 |    |-- ref: string (nullable = true)
 |    |-- sha: string (nullable = true)
 |    |-- repo_id: string (nullable = true)
 |    |-- repo_owner: string (nullable = true)
 |    |-- repo_name: string (nullable = true)
 |    |-- repo_full_name: string (nullable = true)
 |    |-- description: string (nullable = true)
 |    |-- fork: string (nullable = true)
 |    |-- created_at: string (nullable = true)
 |    |-- updated_at: string (nullable = true)
 |    |-- pushed_at: string (nullable = true)
 |    |-- homepage: string (nullable = true)
 |    |-- size: long (nullable = true)
 |    |-- stargazers_count: long (nullable = true)
 |    |-- watchers_count: long (nullable = true)
 |    |-- forks_count: long (nullable = true)
 |    |-- open_issues_count: long (nullable = true)
 |    |-- repo_language: string (nullable = true)
 |    |-- has_issues: string (nullable = true)
 |    |-- has_projects: string (nullable = true)
 |    |-- has_downloads: string (nullable = true)
 |    |-- has_wiki: string (nullable = true)
 |    |-- has_pages: string (nullable = true)
 |    |-- has_discussions: string (nullable = true)
 |    |-- allow_forking: string (nullable = true)
 |    |-- is_template: string (nullable = true)
 |    |-- archived: string (nullable = true)
 |    |-- disabled: string (nullable = true)
 |    |-- license: string (nullable = true)
 |    |-- default_branch: string (nullable = true)
 |    |-- topics: string (nullable = true)
 |-- head: struct (nullable = true)
 |    |-- label: string (nullable = true)
 |    |-- ref: string (nullable = true)
 |    |-- sha: string (nullable = true)
 |    |-- repo_id: string (nullable = true)
 |    |-- repo_owner: string (nullable = true)
 |    |-- repo_name: string (nullable = true)
 |    |-- repo_full_name: string (nullable = true)
 |    |-- description: string (nullable = true)
 |    |-- fork: string (nullable = true)
 |    |-- created_at: string (nullable = true)
 |    |-- updated_at: string (nullable = true)
 |    |-- pushed_at: string (nullable = true)
 |    |-- homepage: string (nullable = true)
 |    |-- size: long (nullable = true)
 |    |-- stargazers_count: long (nullable = true)
 |    |-- watchers_count: long (nullable = true)
 |    |-- forks_count: long (nullable = true)
 |    |-- open_issues_count: long (nullable = true)
 |    |-- repo_language: string (nullable = true)
 |    |-- has_issues: string (nullable = true)
 |    |-- has_projects: string (nullable = true)
 |    |-- has_downloads: string (nullable = true)
 |    |-- has_wiki: string (nullable = true)
 |    |-- has_pages: string (nullable = true)
 |    |-- has_discussions: string (nullable = true)
 |    |-- allow_forking: string (nullable = true)
 |    |-- is_template: string (nullable = true)
 |    |-- archived: string (nullable = true)
 |    |-- disabled: string (nullable = true)
 |    |-- license: string (nullable = true)
 |    |-- default_branch: string (nullable = true)
 |    |-- topics: string (nullable = true)
 |-- pr_diff: string (nullable = true)
 |-- files: array (nullable = true)
 |    |-- element: struct (containsNull = true)
 |    |    |-- hash: string (nullable = true)
 |    |    |-- path: string (nullable = true)
 |    |    |-- content: string (nullable = true)
"""

import argparse
import os
import random
from collections import defaultdict
from dataclasses import asdict, dataclass, field
from functools import partial
from pathlib import Path
from random import Random
from typing import Callable, Iterable, Literal, Optional, Sequence, Union, cast

import numpy as np
import pandas
from dataclasses_json import DataClassJsonMixin
from pyspark.sql import SparkSession
from unidiff import PatchSet
from unidiff.errors import UnidiffParseError

from base.prompt_format_completion.prompt_formatter import (
    TokenList,
)
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.prompt_format_retrieve.ethanol_embedding_prompt_formatter import (
    Ethanol6DocumentFormatter,
    RetrieverPromptFormatter,
)
from base.prompt_format_retrieve.prompt_formatter import DocumentRetrieverPromptInput
from base.tokenizers.tiktoken_starcoder_tokenizer import TiktokenStarCoderTokenizer
from base.tokenizers.tokenizer import Tokenizer
from research.core.diff_utils import (
    CommandFailedError,
    File,
    Repository,
    apply_diff,
    compute_repo_diff,
    diff_subset,
    parse_git_diff_output,
)
from research.core.types import Chunk, ChunkId, Document, DocumentId
from research.data.spark.pipelines.pipeline_utils import (
    GlobalPipelineConfig,
    TokenizerFactory,
    load_config,
)
from research.data.spark.pipelines.stage_function_registry import (
    register_stage_function,
)
from research.data.spark.pipelines.stages.common import (
    declare_processing_success,
    get_limited_map_parquet_input,
)
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.pipelines.utils.prs_dataset import (
    PRData,
    RepositoryFileManager,
)
from research.next_edits.wip_repo_sampler import WipRepoSampler
from research.retrieval.chunking_functions import LineLevelChunker
from research.retrieval.scorers.recency_scorer import (
    get_diff_line_ranges,
    label_overlapping_chunks,
)
from research.retrieval.types import Chunker
from research.static_analysis import usage_analysis
from research.utils.repo_change_utils import (
    repo_change_from_patchset,
)


@dataclass
class CreatePrDatasetForNextEditLocationConfig:
    """Configuration for add_simulated_wip_state."""

    name: str
    input: Path
    output: Path

    num_context_lines_in_diff: int
    """Number of context lines to use in the diff shown to the model."""

    max_lines_per_chunk: int
    """Number of lines in every chunk."""

    # Batching
    max_negative_documents_per_sample: int
    max_positive_documents_per_sample: int

    # Prompt formatting for query and documents
    add_path_to_prompt: bool
    max_query_token_length: int
    max_document_token_length: int

    negative_sampler: Literal["uniform", "distance"] = "uniform"
    """Controls how negatives are sampled.

    - uniform: Sample negatives uniformly at random.
    - distance: Sample negatives based on the distance between the positives and the
      document.
    """

    wip_sampler: Literal["diff_subset", "wip_repo_sampler"] = "diff_subset"
    """Which WIP repository state sampler to use."""

    max_wip_states_per_pr: Optional[int] = None
    """Limit the number of WIP states to generate per PR."""

    max_prs_per_repo: Optional[int] = None
    """Limit the number of PRs to process per repository."""

    max_samples_per_repo: Optional[int] = None
    """Limit the number of samples to generate per repository."""

    max_prs_per_dataframe: Optional[int] = None
    """If specified, limit the number of PRs to process per dataframe."""

    skip_repo_names: Sequence[str] = field(default_factory=list)
    """Skip these repositories when processing."""

    limit_num_input_parquet_files: Optional[int] = None
    """If specified, limit the number of input parquet files to process."""

    pr_repo_reference_file: str = "/mnt/efs/spark-data/shared/gh_pr_repo_ref.parquet"
    """Path to the parquet file containing the join info for PRs and repository files."""

    save_wip_files: bool = False
    """If true, save the WIP files in the output parquet file.

    Setting this option to true can really explode the size of the output, so use with
    care, e.g. only for a small validation dataset.
    """


class ChunkCache:
    """Cache document chunking results."""

    def __init__(self, chunker: Chunker):
        self.chunker = chunker
        self._doc_to_chunks_cache: dict[DocumentId, list[Chunk]] = {}

    def get_chunks(self, doc: Document) -> list[Chunk]:
        if doc.id not in self._doc_to_chunks_cache:
            self._doc_to_chunks_cache[doc.id] = (
                self.chunker.split_into_chunks(doc) or []
            )
        return self._doc_to_chunks_cache[doc.id]


ChunkTokensCache = dict[ChunkId, TokenList]
"""Cache chunk formatting and tokanization results."""


def _format_next_edit_location_query(
    past_to_wip_diff: PatchSet, tokenizer: Tokenizer, sample_token_length: int
) -> Optional[TokenList]:
    """Format the query for next-edit location prediction.

    If the query is empty, returns None.
    """
    diff_tokens = tokenizer.tokenize_unsafe(str(past_to_wip_diff))
    diff_tokens = diff_tokens[: sample_token_length - 1]
    assert isinstance(tokenizer, TiktokenStarCoderTokenizer)
    if not diff_tokens:
        return None
    query_tokens = diff_tokens + [tokenizer.special_tokens.end_of_query]
    assert len(query_tokens) <= sample_token_length  # TODO(guy) truncate, don't assert
    return query_tokens


def _format_document(
    chunk: Chunk,
    label: bool,
    doc_formatter: RetrieverPromptFormatter[DocumentRetrieverPromptInput],
    tokenizer: Tokenizer,
    chunk_tokens_cache: ChunkTokensCache,
) -> TokenList:
    """Format a retrieval document and return its tokens."""
    # Document tokens
    if chunk.id not in chunk_tokens_cache:
        chunk_tokens_cache[chunk.id] = doc_formatter.format_prompt(
            DocumentRetrieverPromptInput(
                text=chunk.text,
                path=chunk.path if chunk.path else "",
            ),
        ).tokens()
    doc_tokens: TokenList = chunk_tokens_cache[chunk.id]

    assert (
        doc_tokens[-1] == tokenizer.special_tokens.end_of_key  # type: ignore
    ), "Expected end-of-key token to be generated by the prompt formatter"

    # Label tokens
    if label:
        label_value = 0
    else:
        label_value = -1
    assert isinstance(tokenizer, TiktokenStarCoderTokenizer)
    end_of_key_token: int = tokenizer.special_tokens.end_of_key
    label_tokens = tokenizer.tokenize_unsafe(str(label_value)) + [end_of_key_token]

    return doc_tokens + label_tokens


@dataclass
class NextEditLocationSample(DataClassJsonMixin):
    """A sample for next-edit location prediction."""

    repo_name: str
    """The repository this sample came from."""

    pr_number: int
    """The PR number within the repository."""

    title: str
    """The title of the PR."""

    wip_to_future_diff: str
    """Diff between WIP state and future state (the repo state after the PR is applied)"""

    past_to_wip_diff: str
    """Diff between the past and WIP states"""

    pr_diff: str
    """Diff between past and future (past is the state before the PR)"""

    sample_tokens: TokenList
    """Sample tokens"""

    query_tokens: TokenList
    """Query tokens."""

    document_tokens: list[TokenList]
    """Tokenized documents."""

    labels: list[bool]
    """Positive/negative label for each document."""

    body: Optional[str] = None
    """The body of the PR (if it exists)."""

    wip_files: list[File] = field(default_factory=list)
    """All the files in the WIP state.

    This field may be empty if we chose not to export these files.
    """


def get_next_edit_location_sample(
    tokenizer: Tokenizer,
    pr: dict,
    past_to_wip_diff: PatchSet,
    wip_to_future_diff: PatchSet,
    chunks: list[Chunk],
    overlapping_chunk_labels: list[bool],
    config: CreatePrDatasetForNextEditLocationConfig,
    _chunk_tokens_cache: Optional[ChunkTokensCache] = None,
) -> Optional[NextEditLocationSample]:
    """Returns the tokens for a sample for next-edit location predictions.

    The sample format is:
        {query_tokens}<|ret-endofquery|>
        {document_tokens}<|ret-endofkey|>{0 if doc.label is True else -1}<|ret-endofkey|>
        {document_tokens}<|ret-endofkey|>{0 if doc.label is True else -1}<|ret-endofkey|>
        {document_tokens}<|ret-endofkey|>{0 if doc.label is True else -1}<|ret-endofkey|>

    Args:
        tokenizer: The tokenizer to use.
        pr: The PR to process (expects a dict from the PRs dataset).
        past_to_wip_diff: The diff between the past and WIP repository states.
        chunks: The chunks in the WIP repository.
        overlapping_chunk_labels: Whether each chunk overlaps with future changes.
        config: The configuration for the dataset.
        _chunk_tokens_cache: Optional cache of tokens for chunks.

    Returns None if no valid sample can be generated.
    """
    if _chunk_tokens_cache is None:
        chunk_tokens_cache: ChunkTokensCache = {}
    else:
        chunk_tokens_cache = _chunk_tokens_cache

    # Query tokens
    query_tokens: Optional[TokenList] = _format_next_edit_location_query(
        past_to_wip_diff, tokenizer, config.max_query_token_length
    )

    if not query_tokens:
        # Only generate a sample if it has a non-empty query
        return None

    # Document tokens
    apportionment_config = TokenApportionmentConfig(
        max_content_len=config.max_document_token_length,
        input_fraction=0,
        prefix_fraction=0,
        max_path_tokens=0,
    )
    doc_formatter = Ethanol6DocumentFormatter(
        apportionment_config=apportionment_config,
        tokenizer=tokenizer,
        add_path=config.add_path_to_prompt,
    )

    negative_chunks: list[Chunk] = []
    positive_chunks: list[Chunk] = []

    for chunk, is_overlapping_with_future_changes in zip(
        chunks, overlapping_chunk_labels
    ):
        if is_overlapping_with_future_changes:
            positive_chunks.append(chunk)
        else:
            negative_chunks.append(chunk)

    if not positive_chunks:
        # Only generate a sample if it has positive prediction targets
        return None

    random.shuffle(positive_chunks)

    positive_chunks = positive_chunks[: config.max_positive_documents_per_sample]

    if config.negative_sampler == "uniform":
        negative_chunks = _sample_negative_chunks_uniform(
            negative_chunks, config.max_negative_documents_per_sample
        )
    elif config.negative_sampler == "distance":
        negative_chunks = _sample_negative_chunks_distance(
            negative_chunks, positive_chunks, config.max_negative_documents_per_sample
        )
    else:
        raise ValueError(f"Unknown negative sampler: {config.negative_sampler}")

    positive_documents: list[TokenList] = [
        _format_document(
            chunk,
            label=True,
            doc_formatter=doc_formatter,
            tokenizer=tokenizer,
            chunk_tokens_cache=chunk_tokens_cache,
        )
        for chunk in positive_chunks
    ]

    negative_documents: list[TokenList] = [
        _format_document(
            chunk,
            label=False,
            doc_formatter=doc_formatter,
            tokenizer=tokenizer,
            chunk_tokens_cache=chunk_tokens_cache,
        )
        for chunk in negative_chunks
    ]

    all_documents: list[TokenList] = positive_documents + negative_documents
    labels = [True for _ in positive_chunks] + [False for _ in negative_chunks]

    sample_tokens: TokenList = []
    sample_tokens.extend(query_tokens)
    for tokens in all_documents:
        sample_tokens.extend(tokens)

    return NextEditLocationSample(
        repo_name=pr["repo_name"],
        pr_number=pr["number"],
        title=pr["title"],
        body=pr["body"],
        past_to_wip_diff=str(past_to_wip_diff),
        wip_to_future_diff=str(wip_to_future_diff),
        pr_diff=pr["pr_diff"],
        sample_tokens=sample_tokens,
        query_tokens=query_tokens,
        document_tokens=all_documents,
        labels=labels,
    )


def _distance(
    chunk1: Chunk,
    chunk2: Chunk,
    same_file_weight: int = 400,
    max_path_distance: int = 4,
):
    if chunk1.path is None or chunk2.path is None:
        # If we don't know the path distance, then give them the maximum distance.
        # This should be very rare / impossible.
        return same_file_weight * (max_path_distance + 1)
    if chunk1.path != chunk2.path:
        # If two chunks are in different files, we give them a weighting up to
        # `max_path_distance` and then treat them uniformly.
        return same_file_weight * min(
            max_path_distance,
            usage_analysis.path_distance(Path(chunk1.path), Path(chunk2.path)) / 2,
        )
    else:
        # If two chunks are more than same_file_weight lines apart, they have a
        # uniform score equal to files in the same directory.
        # files in the same directory.
        return min(same_file_weight, chunk1.line_range.distance(chunk2.line_range))


def _softmax(logits: np.ndarray) -> np.ndarray:
    """Computes the softmax of logits."""
    return np.exp(logits - np.logaddexp.reduce(logits))


def _sample_negative_chunks_uniform(
    negative_chunks: list[Chunk],
    max_negative_documents_per_sample: int,
) -> list[Chunk]:
    """Samples negative chunks uniformly."""

    random.shuffle(negative_chunks)
    return negative_chunks[:max_negative_documents_per_sample]


def _sample_negative_chunks_distance(
    negative_chunks: list[Chunk],
    positive_chunks: list[Chunk],
    max_negative_documents_per_sample: int,
    temperature: float = 0.1,
    epsilon: float = 0.1,
) -> list[Chunk]:
    """Samples negative chunks based on distance from positive chunks.

    Args:
        negative_chunks: All the chunks in the repository - positive_chunks.
        positive_chunks: The chunks that overlap with future changes.
        max_negative_documents_per_sample: The maximum number of negative chunks to
            sample.
        temperature: The temperature to use for the softmax. Larger temperatures
            will make the sampling more uniform.
        epsilon: The epsilon to use to mix in a uniform distribution. Larger epsilons
            will make the sampling more uniform.
    """

    negative_dists = np.array(
        [
            # Use a combination of path distance and line distance:
            min(_distance(chunk, positive_chunk) for positive_chunk in positive_chunks)
            for chunk in negative_chunks
        ]
    )
    logits = (-negative_dists / np.mean(negative_dists)) / temperature
    weights = _softmax(logits)
    weights = epsilon * 1 / len(negative_chunks) + (1 - epsilon) * weights

    idxs = np.random.choice(
        len(negative_chunks),
        size=min(len(negative_chunks), max_negative_documents_per_sample),
        replace=False,
        p=weights,
    )
    return [negative_chunks[idx] for idx in idxs]


def _create_wip_states_with_diff_subset(
    past_repo: Repository, past_to_future_diff: PatchSet
) -> Iterable[Repository]:
    """Simulate a single WIP state of the repository by partially dropping some changes.

    Uses diff_subset to generate a subset of the diff.
    """

    def should_keep_in_wip(file, hunk) -> bool:
        del file
        # Keep all file-level changes like renames, because we are not trying
        # to predict those
        if hunk is None:
            return True

        # sample a sampling probability from U(0, 1)
        keep_hunk_prob = random.uniform(0, 1)
        return random.uniform(0, 1) < keep_hunk_prob

    past_to_wip_diff = diff_subset(past_to_future_diff, should_keep_in_wip)
    wip_repo = apply_diff(past_repo, past_to_wip_diff)
    yield wip_repo


def _create_wip_states_with_wip_repo_sampler(
    past_repo: Repository,
    past_to_future_diff: PatchSet,
) -> Iterable[Repository]:
    """Simulate WIP states of the repository by partially dropping some changes.

    Uses WipRepoSampler() to generate a series of WIP states.
    """
    repo_sampler = WipRepoSampler()
    repo_change = repo_change_from_patchset(past_repo, past_to_future_diff)
    rng = Random()
    for wip_repo_change in repo_sampler.simulate_wip_repo_states(repo_change, rng):
        wip_repo = Repository(
            files=[
                File(str(path), code)
                for path, code in wip_repo_change.after_files.items()
            ]
        )
        yield wip_repo


def _create_wip_states(
    past_repo: Repository,
    past_to_future_diff: PatchSet,
    config: CreatePrDatasetForNextEditLocationConfig,
) -> list[Repository]:
    if config.wip_sampler == "diff_subset":
        wip_states = list(
            _create_wip_states_with_diff_subset(past_repo, past_to_future_diff)
        )
    elif config.wip_sampler == "wip_repo_sampler":
        wip_states = list(
            _create_wip_states_with_wip_repo_sampler(past_repo, past_to_future_diff)
        )
    else:
        raise ValueError(f"Unknown WIP sampler: {config.wip_sampler}")

    random.shuffle(wip_states)

    if config.max_wip_states_per_pr is not None:
        wip_states = wip_states[: config.max_wip_states_per_pr]

    return wip_states


def _create_next_edit_location_samples_from_pr(
    pr: dict,
    past_repo: Repository,
    tokenizer: Tokenizer,
    chunk_cache: ChunkCache,
    chunk_tokens_cache: ChunkTokensCache,
    config: CreatePrDatasetForNextEditLocationConfig,
) -> Iterable[NextEditLocationSample]:
    """Process a PR for next-edit location predictions.

    Returns:
        Iterator over training samples generated from the PR.
    """
    past_to_future_diff: PatchSet = parse_git_diff_output(pr["pr_diff"])
    future_repo = apply_diff(past_repo, past_to_future_diff)

    wip_states: list[Repository] = _create_wip_states(
        past_repo, past_to_future_diff, config
    )

    for wip_repo in wip_states:
        past_to_wip_diff = compute_repo_diff(
            past_repo,
            wip_repo,
            num_context_lines=config.num_context_lines_in_diff,
        )

        # Find chunks that overlap with future changes
        chunks = []
        for file in wip_repo.files:
            doc = Document.new(file.contents, file.path)
            file_chunks = chunk_cache.get_chunks(doc)
            chunks.extend(file_chunks)

        # Overlap calculations require the diff to have no context lines, so we
        # re-compute the diff to ensure this is the case (i.e. we don't rely
        # on having no context lines in the supplied dataset)
        wip_to_future_diff_with_no_context = compute_repo_diff(
            wip_repo, future_repo, num_context_lines=0
        )
        overlapping_chunk_labels: list[bool] = label_overlapping_chunks(
            chunks,
            get_diff_line_ranges(wip_to_future_diff_with_no_context, source_range=True),
        )

        wip_to_future_diff = compute_repo_diff(
            wip_repo, future_repo, num_context_lines=config.num_context_lines_in_diff
        )

        # Compute the next-edit location sample (a batch of query, positives, and negatives)
        sample = get_next_edit_location_sample(
            tokenizer=tokenizer,
            pr=pr,
            past_to_wip_diff=past_to_wip_diff,
            wip_to_future_diff=wip_to_future_diff,
            chunks=chunks,
            overlapping_chunk_labels=overlapping_chunk_labels,
            config=config,
            _chunk_tokens_cache=chunk_tokens_cache,
        )

        if sample is None:
            continue

        if config.save_wip_files:
            sample.wip_files = list(wip_repo.files)

        yield sample


def _process_pr_into_next_edit_location_samples(
    tokenizer,
    repo_file_manager: RepositoryFileManager,
    pr: dict,
    chunk_cache: ChunkCache,
    chunk_tokens_cache: ChunkTokensCache,
    config: CreatePrDatasetForNextEditLocationConfig,
) -> Iterable[NextEditLocationSample]:
    repo_file_manager.add_repository_files(pr["repo_name"])
    past_repo: Repository = repo_file_manager.get_pr_files(cast(PRData, pr))
    try:
        yield from _create_next_edit_location_samples_from_pr(
            pr, past_repo, tokenizer, chunk_cache, chunk_tokens_cache, config
        )
    except CommandFailedError:
        print(
            f"PR processing failed with CommandFailedError: id={pr['id']} number={pr['number']} repo_name={pr['repo_name']} url={pr['url']}"
        )
    except UnidiffParseError:
        print(
            f"PR processing failed with UnidiffParseError (could not parse the diff): id={pr['id']} number={pr['number']} repo_name={pr['repo_name']} url={pr['url']}"
        )


def _filter_next_edit_location_samples(
    samples: Iterable[NextEditLocationSample],
    config: CreatePrDatasetForNextEditLocationConfig,
) -> Iterable[NextEditLocationSample]:
    if config.max_samples_per_repo is None:
        return samples

    samples_by_repo = defaultdict(list)
    for sample in samples:
        samples_by_repo[sample.repo_name].append(sample)

    for repo_name, repo_samples in samples_by_repo.items():
        del repo_name
        if len(repo_samples) > config.max_samples_per_repo:
            random.shuffle(repo_samples)
            repo_samples = repo_samples[: config.max_samples_per_repo]
        for sample in repo_samples:
            yield sample


# PandasFuncType = Callable[[pd.DataFrame], Union[pd.DataFrame, pd.Series, Iterable[Any]]]
def _next_edit_location_pandas_func(
    df: pandas.DataFrame,
    tokenizer_factory: TokenizerFactory,
    tmpdir: Optional[str],
    config: CreatePrDatasetForNextEditLocationConfig,
) -> pandas.DataFrame:
    training_samples: list[dict] = []
    num_prs_per_repo: dict[str, int] = defaultdict(int)

    # Set temporary directory as an environment variable. This causes
    # tempfile to use it, leading to faster diff operations in diff_utils.
    if tmpdir:
        if not os.path.exists(tmpdir):
            raise FileNotFoundError(f"Temporary directory {tmpdir} does not exist")
        os.environ["TMPDIR"] = tmpdir
        print(f"Using temporary directory: {tmpdir}")

    tokenizer = tokenizer_factory()
    repo_file_manager = RepositoryFileManager(
        pr_repo_reference_parquet_file=Path(config.pr_repo_reference_file)
    )

    def num_processed_prs():
        return sum(num_prs_per_repo.values())

    chunker = LineLevelChunker(max_lines_per_chunk=config.max_lines_per_chunk)
    chunk_cache = ChunkCache(chunker=chunker)
    chunk_tokens_cache: ChunkTokensCache = {}

    for pr in df.to_dict(orient="records"):
        print(f"Processing PR {pr['id']} ({pr['number']})")

        # Filtering
        if (
            config.max_prs_per_dataframe
            and num_processed_prs() >= config.max_prs_per_dataframe
        ):
            break

        repo_name = pr["repo_name"]

        if repo_name in config.skip_repo_names:
            continue

        if (
            config.max_prs_per_repo is not None
            and num_prs_per_repo[repo_name] >= config.max_prs_per_repo
        ):
            continue

        # Process the samples
        samples = _process_pr_into_next_edit_location_samples(
            tokenizer, repo_file_manager, pr, chunk_cache, chunk_tokens_cache, config
        )
        samples = _filter_next_edit_location_samples(samples, config)
        training_samples.extend([asdict(sample) for sample in samples])
        num_prs_per_repo[repo_name] += 1

    print(f"Returning dataset with {len(training_samples)} training samples")
    return pandas.DataFrame(training_samples)


@register_stage_function("create_next_edit_location_dataset_from_prs")
def create_next_edit_location_dataset_from_prs(
    config: Union[dict, CreatePrDatasetForNextEditLocationConfig],
    spark: SparkSession,
    tokenizer_factory: TokenizerFactory,
    tmpdir: Optional[str],
):
    """Stage function, creates a dataset of next edit locations."""
    if not isinstance(config, CreatePrDatasetForNextEditLocationConfig):
        config = CreatePrDatasetForNextEditLocationConfig(**config)

    map_parquet_input = get_limited_map_parquet_input(
        config.input, config.limit_num_input_parquet_files
    )

    print(f"Running map_parquet on this input: {map_parquet_input}")

    pandas_func = partial(
        _next_edit_location_pandas_func,
        tokenizer_factory=tokenizer_factory,
        tmpdir=tmpdir,
        config=config,
    )

    map_parquet.apply_pandas(
        spark_session=spark,
        pandas_func=pandas_func,
        input_path=map_parquet_input,
        output_path=str(config.output),
        ignore_error=True,
        timeout=3600,
        task_info_location="/mnt/efs/spark-data/user/guy/next-edit-location-task-info",
    )

    declare_processing_success(config.output)


RowVerifier = Callable[[dict], None]
"""Verifies a row of a dataset. Accepts the row as a dict, raises an exception if the row is invalid."""


def _verify_parquet_dataset(dataset_path: Path, verify_row_fn: RowVerifier):
    for parquet_file in sorted(list(dataset_path.glob("*.parquet"))):
        df = pandas.read_parquet(parquet_file)
        print(f"Verifying {parquet_file} ({len(df)} rows)")
        for row in df.to_dict(orient="records"):
            verify_row_fn(row)

    print("\nDataset verified.")


def _verify_next_edit_location_row(row: dict):
    assert len(row["query_tokens"]) > 0, "No query tokens"
    assert sum(row["labels"]) > 0, "No positive samples"


def main():
    """Verify generated data."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config", type=Path, required=True, help="the pipeline config yaml file"
    )
    args = parser.parse_args()

    config = load_config(args.config)
    global_config = GlobalPipelineConfig(**config["global"])

    next_edit_location_dataset_path = (
        Path(global_config.data_processing_root) / "next-edit-location"
    )
    _verify_parquet_dataset(
        dataset_path=next_edit_location_dataset_path,
        verify_row_fn=_verify_next_edit_location_row,
    )


if __name__ == "__main__":
    main()

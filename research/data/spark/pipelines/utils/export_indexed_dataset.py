"""Export parquet files to an indexed dataset."""

import argparse
import cProfile
import random
import shutil
from dataclasses import dataclass
from functools import partial
from multiprocessing import Pool
from pathlib import Path
from tempfile import TemporaryDirectory

import megatron.data.indexed_dataset as indexed_dataset
import numpy as np
import pandas
import torch
from megatron.tokenizer.tokenizer import get_tokenizer

from research.data.spark.pipelines.stages.common import (
    unpack_tokens,
)

TOKEN_SIZE = 2
INDEXED_DATASET_IMPL = "mmap"


class IndexedDatasetBuilderWrapper:
    """Convenience wrapper around an indexed dataset builder."""

    def __init__(self, output_dataset: Path, vocab_size: int):
        self.output_dataset = output_dataset
        self.builder = indexed_dataset.make_builder(
            str(output_dataset.with_suffix(".bin")),
            impl=INDEXED_DATASET_IMPL,
            vocab_size=vocab_size,
        )

    def add_item(self, item: indexed_dataset.TokenSeqLike):
        """Add an item to the dataset."""
        self.builder.add_item(item)
        self.builder.end_document()

    def append_indexed_dataset(self, other_dataset: Path):
        """Append another indexed dataset to this one."""
        self.builder.merge_file_(str(other_dataset.with_suffix("")))

    def finalize(self):
        """Must be called after all rows have been added."""
        self.builder.finalize(str(self.output_dataset.with_suffix(".idx")))

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, exc_traceback):
        self.finalize()


def append_parquet_file_to_indexed_dataset(
    parquet_path: Path,
    column: str,
    indexed_dataset_builder: IndexedDatasetBuilderWrapper,
) -> int:
    """Exports the rows in a parquet file to an indexed dataset.

    Returns the number of exported rows.
    """
    df = pandas.read_parquet(parquet_path, columns=[column])
    for row in df.to_dict(orient="records"):
        if column not in row:
            raise ValueError(f"Column {column} not found in row")
        if isinstance(row[column], list):
            tokens = row[column]
        elif isinstance(row[column], np.ndarray):
            # Copy the array to avoid pytorch warnings
            tokens = np.array(row[column])
        else:
            tokens = list(unpack_tokens(row[column]))
        tensor = torch.Tensor(tokens)
        indexed_dataset_builder.add_item(tensor)
    return len(df)


def export_parquet_file_to_indexed_dataset(
    parquet_path: Path, column: str, indexed_dataset_path: Path, vocab_size: int
) -> int:
    """Returns the number of rows."""
    with IndexedDatasetBuilderWrapper(indexed_dataset_path, vocab_size) as builder:
        return append_parquet_file_to_indexed_dataset(parquet_path, column, builder)


@dataclass
class ExportResult:
    """Result of exporting a parquet file."""

    parquet_path: Path
    indexed_dataset_path: Path
    num_rows: int


def process_file(
    parquet_path: Path, column: str, tmp_dir: Path, vocab_size: int
) -> ExportResult:
    # call with_suffix("") twice to remove .zstd.parquet suffixes
    indexed_dataset_path = tmp_dir / parquet_path.with_suffix("").with_suffix("").name
    num_rows = export_parquet_file_to_indexed_dataset(
        parquet_path, column, indexed_dataset_path, vocab_size
    )

    print(f"Exported {num_rows} rows from {parquet_path} to {indexed_dataset_path}")
    return ExportResult(parquet_path, indexed_dataset_path, num_rows)


def export_indexed_dataset(
    input_path: Path,
    output_path: Path,
    column: str,
    tokenizer,
    num_validation_samples: int,
    save_validation_parquet_files: bool,
    num_workers: int,
    _tmp_dir: Path,
):
    parquet_file_list = list(input_path.glob("*.parquet"))
    random.shuffle(parquet_file_list)

    output_path.mkdir(parents=True, exist_ok=True)
    output_dataset_path = output_path / "dataset"
    validation_dataset_output_path = output_path / "validation_dataset"
    validation_parquet_files_path = output_path / "validation"

    if output_dataset_path.exists():
        raise FileExistsError(f"Output path {output_dataset_path} already exists")
    if validation_dataset_output_path.exists():
        raise RuntimeError(
            f"Output path {validation_dataset_output_path} already exists"
        )

    num_exported_rows = 0
    num_exported_validation_rows = 0

    builder = IndexedDatasetBuilderWrapper(output_dataset_path, tokenizer.vocab_size)
    validation_builder = None
    if num_validation_samples > 0:
        validation_builder = IndexedDatasetBuilderWrapper(
            validation_dataset_output_path, tokenizer.vocab_size
        )

    with TemporaryDirectory(dir=_tmp_dir) as tmp_dir:
        tmp_dir = Path(tmp_dir)
        process_file_fn = partial(
            process_file,
            column=column,
            tmp_dir=tmp_dir,
            vocab_size=tokenizer.vocab_size,
        )

        def process_exported_parquet_file(i, export_result):
            nonlocal num_exported_rows
            nonlocal num_exported_validation_rows

            if (
                validation_builder
                and num_exported_validation_rows < num_validation_samples
            ):
                num_exported_validation_rows += export_result.num_rows
                validation_builder.append_indexed_dataset(
                    export_result.indexed_dataset_path
                )
                print(
                    f"Exported {export_result.indexed_dataset_path} to validation split"
                )

                if save_validation_parquet_files:
                    validation_parquet_files_path.mkdir(parents=True, exist_ok=True)
                    shutil.copy(
                        str(export_result.parquet_path),
                        str(validation_parquet_files_path),
                    )
            else:
                num_exported_rows += export_result.num_rows
                builder.append_indexed_dataset(export_result.indexed_dataset_path)
                print(f"Exported {export_result.indexed_dataset_path} to main split")

            export_result.indexed_dataset_path.with_suffix(".bin").unlink()
            export_result.indexed_dataset_path.with_suffix(".idx").unlink()

            queue_size = len(list(tmp_dir.glob("*.bin")))
            print(
                f"Completed {i+1} out of {len(parquet_file_list)} files. Merge queue size: {queue_size}"
            )
            print()

        if num_workers > 0:
            with Pool(num_workers) as pool:
                for i, export_result in enumerate(
                    pool.imap_unordered(process_file_fn, parquet_file_list)
                ):
                    process_exported_parquet_file(i, export_result)
        else:
            with cProfile.Profile() as pr:
                for i, export_result in enumerate(
                    map(process_file_fn, parquet_file_list)
                ):
                    process_exported_parquet_file(i, export_result)

            pr.dump_stats("output.prof")

    builder.finalize()
    if validation_builder:
        validation_builder.finalize()

    print(f"Exported {num_exported_rows} into: {output_dataset_path}")
    print(
        f"Exported {num_exported_validation_rows} into: {validation_dataset_output_path}"
    )


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input",
        type=Path,
        required=True,
        help="path to a directory with parquet files",
    )
    parser.add_argument(
        "--output",
        type=Path,
        required=True,
        help="path to an output directory",
    )
    parser.add_argument(
        "--column",
        type=str,
        required=True,
        help="the column to export",
    )
    parser.add_argument(
        "--tokenizer",
        type=str,
        required=True,
        help="the tokenizer to use when exporting",
    )
    parser.add_argument(
        "--num_validation_samples",
        type=int,
        default=0,
        help="number of validation samples, aligned to whole parquet files",
    )
    parser.add_argument(
        "--num_workers",
        type=int,
        default=4,
        help="number of data worker processes, or 0 to avoid multiprocessing",
    )
    parser.add_argument(
        "--tmp_dir",
        type=Path,
        default=Path("/tmp"),
        help="a temporary directory",
    )
    parser.add_argument(
        "--save_validation_parquet_files",
        action="store_true",
        help="save the validation parquet files (useful for evaluation)",
    )
    args = parser.parse_args()

    args.tmp_dir.mkdir(parents=True, exist_ok=True)
    tokenizer = get_tokenizer(args.tokenizer)

    export_indexed_dataset(
        args.input,
        args.output,
        args.column,
        tokenizer=tokenizer,
        num_validation_samples=args.num_validation_samples,
        save_validation_parquet_files=args.save_validation_parquet_files,
        num_workers=args.num_workers,
        _tmp_dir=args.tmp_dir,
    )


if __name__ == "__main__":
    main()

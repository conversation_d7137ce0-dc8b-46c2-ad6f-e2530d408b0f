"""Utilities for working with the PRs dataset.

The PRs dataset contains PR and file data across several tables. It is used
to generate downstream training and evaluation datasets.

See: https://www.notion.so/GitHub-PR-Datasets-cbffe472c71e4c7a9476de70d983b218?pvs=4
"""

from pathlib import Path
from typing import Sequence, TypedDict

import pandas

from research.core.diff_utils import (
    File,
    Repository,
)


class RepositoryFileManager:
    """Manages files from a repository across multiple PRs."""

    def __init__(self, pr_repo_reference_parquet_file: Path):
        self.pr_repo_join_data = pandas.read_parquet(
            str(pr_repo_reference_parquet_file)
        )

        # repo_name -> file_sha -> content
        self.repo_files: dict[str, dict[str, str]] = {}

    def add_repository_files(self, repo_name: str):
        """Add the files from the given repository to the file manager.

        Does nothing if the files were already added.
        """
        if repo_name in self.repo_files:
            return
        self.repo_files[repo_name] = {}

        # find the content parquet file for the given repo
        _, content_parquet_file = self.get_pr_repo_join_data(repo_name)

        content_df = pandas.read_parquet(content_parquet_file)
        for row in content_df[content_df["repo_name"] == repo_name].to_dict(
            orient="records"
        ):
            self.repo_files[repo_name][row["file_sha"]] = row["content"]

    def get_pr_files_impl(
        self, repo_name: str, pr_files: Sequence["PRFileDict"]
    ) -> Repository:
        """Get a repository containing the given PR files."""
        if repo_name not in self.repo_files:
            raise ValueError(f"No files added for repo_name={repo_name}")

        all_files = self.repo_files[repo_name]
        files = [
            File(path=file["path"], contents=all_files[file["file_sha"]])
            for file in pr_files
        ]
        return Repository(files)

    def get_pr_files(self, pr: "PRData") -> Repository:
        """Get the files representing the base repository state at the PR.

        Base state means it is the repository state before the PR was applied.

        Args:
            pr: The PR dict as obtained from the PRs dataset.
        """
        repo_name = pr["repo_name"]
        pr_files = pr["files"]
        return self.get_pr_files_impl(repo_name, pr_files)

    def get_pr_repo_join_data(self, repo_name: str) -> tuple[str, str]:
        """Returns the PR parquet file and content parquet file for the given repo."""
        matches = self.pr_repo_join_data.loc[
            self.pr_repo_join_data["repo_name"] == repo_name
        ]
        if len(matches) != 1:
            raise ValueError(
                f"Expected 1 row for repo_name={repo_name}, found {len(matches)}"
            )
        return matches.iloc[0].joined_sha_parquet, matches.iloc[0].content_parquet


class PRFileDict(TypedDict):
    """Schema for the file data in the PR dataset."""

    path: str
    """File path."""

    file_sha: str
    """SHA of the file."""


class PRData(TypedDict):
    """Schema for the PR dataset.

    There are actually many more fields in the dataset, but these are the minimal set
    that we need.
    """

    repo_name: str
    """Repo name."""

    # Commit information
    commit_sha: str
    """The commit SHA."""

    # PR information
    number: int
    """PR Number."""

    title: str
    """PR Title."""

    body: str
    """PR Body."""

    pr_diff: str
    """The diff of the PR."""

    files: list[PRFileDict]
    """List of files in the PR."""

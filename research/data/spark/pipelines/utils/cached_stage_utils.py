import time
from pathlib import Path
from typing import Any, Callable

from research.utils.inspect_indexed_dataset import print_green, print_yellow


def cached_stage(name: str, output_path: Path | str):
    """Optionally run a spark stage by checking if the result already exists.

    To use this function, wrap the work in a stage into a local function that accepts
    no arguments and decorate it with `@cached_stage(stage_name, output_path)`.
    That local function will then only be run if `output_path/_SUCCESS` does not
    already exist.

    Args:
        name: The name of the stage, used for logging only.
        output_path: The directory in which the output will be saved.

    Returns:
        A decorator to be applied to a function that accepts no arguments.

    Usage:
        @cached_stage("stage_name", "output_path")
        def stage_work():
            ... the work to be cached ...
    """
    output_path = Path(output_path)

    def decorator(stage_work: Callable[[], Any]) -> None:
        if output_path.exists():
            if not (output_path / "_SUCCESS").exists():
                raise RuntimeError(
                    f"Stage {name} has incomplete output at: '{output_path}'. "
                    "Either delete the path or set overwrite=True to overwrite it."
                )
            else:
                print_yellow(
                    f"Skipping completed stage {name}, output path exists: {output_path}"
                )
                return None

        print_green(f"Starting {name}.\n    output_path: {output_path}")
        t0 = time.time()
        stage_work()
        (output_path / "_SUCCESS").touch()

        print_green(f"Result saved to: {output_path}")
        print_green(f"Time taken: {time_string(time.time() - t0)}")

    return decorator


def time_string(seconds: float) -> str:
    """Get a string showing the time in seconds, minutes, or hours."""
    if seconds < 600:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        return f"{seconds / 60:.1f}m"
    else:
        return f"{seconds / 3600:.1f}h"

"""Common utilities used by the next edit pipelines.

NOTE(arun): there isn't anything particularly special about these functions and next
edits, but they relate to a different approach to creating spark pipelines. If this new
way catches on, it makes sense to refactor these into a more common library.
"""

import logging
import pickle
import shutil
import time
from collections.abc import Sequence
from contextlib import contextmanager
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Callable, Literal

import pandas as pd
import pyarrow
from dataclasses_json import DataClassJsonMixin
from pyspark.sql import SparkSession
from tqdm import tqdm
from unidiff import PatchedFile, UnidiffParseError

from research.core.diff_utils import (
    CommandFailedError,
    diff_subset,
    parse_git_diff_output,
)
from research.data.spark.pipelines.stages import prs
from research.data.spark.pipelines.utils.cached_stage_utils import time_string
from research.data.spark.utils import get_local_session, k8s_session
from research.next_edits.next_edits_dataset import (
    CommitMeta,
    PRMeta,
)
from research.utils import repo_change_utils
from research.utils.inspect_indexed_dataset import print_green, print_yellow
from research.utils.token_array_utils import TokenArray

logger = logging.getLogger(__name__)


RunMode = Literal["", "timing", "local"]


def to_token_array_identity(obj: TokenArray) -> TokenArray:
    return obj


def run_create_indexed_dataset(
    input_path: Path | str,
    output_root: Path | str,
    tokenizer_name: str,
    to_token_array: Callable[[Any], TokenArray] = to_token_array_identity,
    overwrite: bool = False,
    versioned_name: str | None = None,
) -> Path:
    """Run stage 4: Create indexed dataset.

    Args:
        input_path: The path to the input parquet file.
        tokenizer_name: The name of the tokenizer.
        output_root: The root path to the output directory. If set, the output path should be None.
        to_token_array: A function that takes a row and returns a token array.
        overwrite: Whether to overwrite existing output.

    Returns:
        The path to the output parquet file.
    """
    from megatron.data.indexed_dataset import make_builder

    from base.tokenizers import create_tokenizer_by_name

    tokenizer = create_tokenizer_by_name(tokenizer_name)

    if versioned_name is None:
        versioned_name = f"{Path(input_path).name},indexed_dataset"
    output_data_path = Path(output_root) / versioned_name

    if output_data_path.exists() and not overwrite:
        print_yellow(
            f"Skipping create_indexed_dataset, already processed: {output_data_path}."
        )
        return output_data_path
    elif output_data_path.exists():
        print_yellow(f"Deleting existing output: {output_data_path}.")
        shutil.rmtree(output_data_path)

    print_green(f"Starting create_indexed_dataset: {output_data_path=}.")

    t0 = time.time()
    output_data_path.mkdir(parents=True, exist_ok=True)
    builder = make_builder(
        str(output_data_path / "dataset.bin"),
        "mmap",
        vocab_size=tokenizer.vocab_size,
    )

    for shard in tqdm(Path(input_path).glob("*.parquet")):
        df = pd.read_parquet(shard, columns=["pickled_results"])
        for row in df.to_dict(orient="records"):
            for obj in pickle.loads(row["pickled_results"]):
                token_seq = to_token_array(obj)
                builder.add_item(token_seq)
    builder.finalize(str(output_data_path / "dataset.idx"))

    print_green(f"Result saved to: {output_data_path}")
    print_green(f"Time taken: {time_string(time.time() - t0)}")

    return output_data_path


@contextmanager
def stage_block(
    output_data_path: Path,
    mode: RunMode = "",
    overwrite: bool = False,
):
    """Wraps a data pipeline stage to provides metadata about completion status.

    This is a useful way to skip running a particular stage. It works by checking if
    there's output in the output data path along with the `_SUCCESS` file. The context
    manager returns the output path, the log path and a completion flag -- the user
    decides to skip running the stage if the completion flag is set.

    Args:
        output_data_path: The path to the output directory.
        mode: The run mode. We use the mode to add a suffix to the output path to
            differentiate between different full runs and timing / local runs.
        overwrite: Whether to overwrite existing output.

    Yields:
        The output path and log path and a "completed" boolean flag.
    """
    name = output_data_path.name
    if mode:
        output_data_path = output_data_path.with_name(f"{output_data_path.name}_{mode}")

    output_log_path = output_data_path.with_name(f"{output_data_path.name}.logs")

    if output_data_path.exists() and overwrite:
        print_yellow(f"Deleting existing output: {output_data_path}.")
        shutil.rmtree(output_data_path)
        shutil.rmtree(output_log_path)
    elif output_data_path.exists() and (output_data_path / "_SUCCESS").exists():
        print_yellow(
            f"Skipping completed stage {name}, output path exists: {output_data_path}"
        )
        yield output_data_path, output_log_path, True
        return
    elif output_data_path.exists() and list(output_data_path.glob("*")):
        print_yellow(
            f"Stage {name} has incomplete output at: {output_data_path}. "
            "Either delete the path or set overwrite=True to overwrite it."
        )
        raise RuntimeError(
            f"Stage {name} has incomplete output at: {output_data_path}."
        )

    print_green(f"Starting {name}: {output_data_path=}, {output_log_path=}.")
    t0 = time.time()
    yield output_data_path, output_log_path, False
    (output_data_path / "_SUCCESS").touch()

    print_green(f"Result saved to: {output_data_path}")
    print_green(f"Time taken: {time_string(time.time() - t0)}")


cpu_spark_conf = {
    "spark.executor.pyspark.memory": "100G",
    "spark.executor.memory": "60G",
    "spark.sql.parquet.columnarReaderBatchSize": "256",
    "spark.rpc.lookupTimeout": "240",
}
gpu_spark_conf = {
    "spark.executor.pyspark.memory": "100G",
    "spark.executor.memory": "60G",
    "spark.sql.parquet.columnarReaderBatchSize": "256",
    "spark.task.cpus": "5",
    "spark.rpc.lookupTimeout": "240",
}


def smart_session(
    name: str,
    gpu_type: str | Sequence[str] = "",
    max_workers: int = 32,
    mode: RunMode = "",
    conf: dict[str, str] | None = None,
) -> SparkSession:
    """A convenience wrapper to create a Spark session with the right configuration."""
    # Get fewer workers for debugging purposes.
    if conf is None:
        conf = gpu_spark_conf if gpu_type else cpu_spark_conf

    if mode == "local":
        return get_local_session(workers=1, conf=conf)

    if mode == "timing":
        max_workers = min(5, max_workers)

    return k8s_session(
        name=name,
        max_workers=max_workers,
        conf=conf,
        gpu_count=1 if gpu_type else 0,
        gpu_type=gpu_type,
    )


@dataclass
class PRToRepoChangeConfig(DataClassJsonMixin):
    """Configuration for PR to RepoChange conversion."""

    pr_repo_reference_file: str = "/mnt/efs/spark-data/shared/gh_pr_repo_ref.parquet"
    """Path to the parquet file containing the join info for PRs and repository files."""

    skip_repo_names: list[str] = field(default_factory=list)
    """Skip these repositories when processing."""


def pr_to_repo_change(
    pr: prs.PRData,
    repo_file_manager: prs.RepositoryFileManager,
    data_config: PRToRepoChangeConfig,
    max_repo_size_files: int,
    file_filter: Callable[[Path], bool],
) -> tuple[repo_change_utils.RepoChange, PRMeta, CommitMeta] | None:
    """Convert a PR to a RepoChange.

    Args:
        pr: The PR to convert.
        repo_file_manager: The repository file manager. Used to actually get the files
            from the PR.
        data_config: The PR to RepoChange conversion config.
        max_repo_size_files: Repos with more than this many files will be filtered out.
        file_filter: Used to decide which files to keep (if return True).
    """

    def should_keep_pfile(pfile: PatchedFile) -> bool:
        if pfile.is_modified_file:
            return file_filter(Path(pfile.source_file)) and file_filter(
                Path(pfile.target_file)
            )
        elif pfile.is_added_file:
            return file_filter(Path(pfile.target_file))
        elif pfile.is_removed_file:
            return file_filter(Path(pfile.source_file))
        raise ValueError(f"Invalid pfile type: {pfile}")

    pr_meta = PRMeta(
        repo_name=pr["repo_name"],
        pr_number=pr["number"],
        title=pr["title"],
        body=pr["body"],
    )
    commit_meta = CommitMeta(
        repo_name=pr["repo_name"],
        sha=pr["commit_sha"],
        message=f"{pr['title']}\n\\{pr['body']}",
    )

    # Pre-filter some PRs.
    if pr["repo_name"] in data_config.skip_repo_names:
        logger.info("Skipping %s: in skip list.", pr_meta)
        return None

    files = [x for x in pr["files"] if file_filter(Path(x["path"]))]

    if len(files) > max_repo_size_files:
        # TODO(arun): more info
        logger.info("Skipping %s: too many files.", pr_meta)
        return None

    try:
        past_to_future_diff = parse_git_diff_output(pr["pr_diff"])
        past_to_future_diff = diff_subset(
            past_to_future_diff, lambda pfile, _: should_keep_pfile(pfile)
        )

        repo_file_manager.add_repository_files(pr["repo_name"])
        past_repo = repo_file_manager.get_pr_files_impl(pr["repo_name"], files)
        past_to_future_repo_change = repo_change_utils.repo_change_from_patchset(
            past_repo,
            past_to_future_diff,
        )
    except pyarrow.ArrowException:
        logger.warning("Failed %s: couldn't read PR files.", pr_meta)
        return None
    except FileNotFoundError:
        logger.warning("Failed %s: files mentioned by diff are missing.", pr_meta)
        return None
    except (UnidiffParseError, CommandFailedError):
        logger.warning("Failed %s: couldn't parse PR diff.", pr_meta)
        return None
    except ValueError:
        logger.warning("Failed %s: couldn't create repo change from PR.")
        return None

    return (past_to_future_repo_change, pr_meta, commit_meta)

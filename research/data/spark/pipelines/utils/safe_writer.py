"""A utility class for safely writing to parquet files."""

import time
from typing import Optional

import pyarrow
import pyarrow.fs as pyarrow_fs
import pyarrow.parquet as pyarrow_parquet

# additional file suffix of the intermediate file in 2-stage writing
TMP_SUFFIX = ".tmp"
# compression to use for the parquet output
OUTPUT_COMPRESSION = "zstd"
# time to wait (in seconds) between closing a file and committing it
COMMIT_WAIT_TIME = 10


class SafeWriter:
    """A utility class for safely writing to parquet files.

    This writer supports writing to local file system (or shared drive) and S3.

    While writing to a local file system, it uses a 2-stage writing scheme to avoid
    partially written parquet files. The writer will first write to an intermediate
    temporary file with a special suffix. After the writing is done, it will then
    commit the intermediate file to the final output location. This can ensure that
    the final file is always complete and valid.

    Args:
        path: Path to the file to write content to.
        s3_access_key: (optional) S3 access key.
        s3_secret_key: (optional) S3 secret key.
        s3_endpoint: (optional) S3 endpoint.
    """

    def __init__(
        self,
        path: str,
        s3_access_key: Optional[str] = None,
        s3_secret_key: Optional[str] = None,
        s3_endpoint: Optional[str] = None,
    ):
        """Initialize the writer based on the provided path."""
        self.raw_path = path

        # default settings to local fs
        self.file_system = pyarrow_fs.LocalFileSystem()
        # path where the batch stream is written to
        self.stream_path: str = self.raw_path + TMP_SUFFIX
        # optional path for the file commit stage
        # skip committing if this path is None
        self.commit_path: Optional[str] = self.raw_path

        # update settings if it is s3 path
        self.is_s3_path = self._contains_s3_prefix(self.raw_path)
        if self.is_s3_path:
            self.file_system = pyarrow_fs.S3FileSystem(
                access_key=s3_access_key,
                secret_key=s3_secret_key,
                endpoint_override=s3_endpoint,
            )
            # pyarrow expects bucket/path format, so we need to remove the protocol
            # writing to s3 doesn't require an intermediate tmp file
            self.stream_path = self.raw_path.split("://", 1)[-1]
            self.commit_path = None

        # handles gcs paths
        self.is_gcs_path = self._contains_gcs_prefix(self.raw_path)
        if self.is_gcs_path:
            self.file_system = pyarrow_fs.GcsFileSystem()
            # pyarrow expects bucket/path format, so we need to remove the protocol
            # writing to gcs doesn't require an intermediate tmp file
            self.stream_path = self.raw_path.split("://", 1)[-1]
            self.commit_path = None

        # having the same path for stream and commit can cause unexpected behavior
        assert self.stream_path != self.commit_path

        # the file stream and the internal writer will be initialized lazily
        self.stream = None
        self.writer = None

    def write(self, batch: pyarrow.RecordBatch):
        """Write a batch to the file stream."""
        if self.stream is None:
            # a file will be immediately created when we open the stream
            # this can result in an empty file if no data is written
            self.stream = self.file_system.open_output_stream(self.stream_path)
        if self.writer is None:
            self.writer = pyarrow_parquet.ParquetWriter(
                self.stream, batch.schema, compression=OUTPUT_COMPRESSION
            )
        self.writer.write(batch)

    def close(self):
        """Close the stream and the internal writer.

        Note that closing the stream will affect the writer. It's suggested to
        always close the writer first before closing the stream.
        """
        if self.writer:
            self.writer.close()
            self.writer = None
        if self.stream:
            self.stream.close()
            self.stream = None

    def finalize(self):
        """Close and commit the intermediate file if needed."""
        self.close()
        if self.commit_path and self._validate_file(self.stream_path):
            # wait for the file to be closed as there can be race conditions
            time.sleep(COMMIT_WAIT_TIME)
            self.file_system.move(self.stream_path, self.commit_path)

    def cleanup(self):
        """Close and delete all files created by this writer."""
        self.close()
        if self._path_exists(self.stream_path):
            self.file_system.delete_file(self.stream_path)
        if self.commit_path and self._path_exists(self.commit_path):
            self.file_system.delete_file(self.commit_path)

    @staticmethod
    def _contains_s3_prefix(path: str) -> bool:
        """Check if the path contains s3 prefix.

        The prefix can be either s3:// or s3a://
        """
        return path.startswith("s3://") or path.startswith("s3a://")

    @staticmethod
    def _contains_gcs_prefix(path: str) -> bool:
        """Check if the path contains gcs prefix.

        The prefix can be either gs://
        """
        return path.startswith("gs://")

    def _path_exists(self, path: str) -> bool:
        """Check if a file path exists."""
        file_info = self.file_system.get_file_info(path)
        return file_info.type != pyarrow_fs.FileType.NotFound

    def _validate_file(self, path: str) -> bool:
        """Check if a file is valid."""
        file_info = self.file_system.get_file_info(path)
        return file_info.type != pyarrow_fs.FileType.NotFound and file_info.size > 0

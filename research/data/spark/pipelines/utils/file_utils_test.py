import pytest
from research.data.spark.pipelines.utils import file_utils


@pytest.mark.parametrize(
    "input_path, expected_output",
    [
        ("gs://my-bucket/path/to/file.txt", "my-bucket"),
        ("gs://another-bucket", "another-bucket"),
        ("gs:/my-bucket/file.txt", "my-bucket"),
        ("gs://", ""),
    ],
)
def test_get_bucket_name_valid_paths(input_path, expected_output):
    assert file_utils.get_bucket_name(input_path) == expected_output


@pytest.mark.parametrize(
    "invalid_input",
    [
        "https://example.com/file.txt",
        "/local/path/to/file.txt",
        "",
    ],
)
def test_get_bucket_name_invalid_paths(invalid_input):
    with pytest.raises((IndexError, ValueError)):
        file_utils.get_bucket_name(invalid_input)


@pytest.mark.parametrize(
    "input_path, expected_output",
    [
        ("gs:/my-bucket/file.txt", "gs://my-bucket/file.txt"),
        ("gs://my-bucket/file.txt", "gs://my-bucket/file.txt"),
        ("gs:/my-bucket", "gs://my-bucket"),
        ("gs://my-bucket", "gs://my-bucket"),
        ("https://example.com", "https://example.com"),
        ("/local/path", "/local/path"),
        ("", ""),
    ],
)
def test_maybe_fix_gs_path(input_path, expected_output):
    assert file_utils.maybe_fix_gs_path(input_path) == expected_output

"""Utilities for working with files."""

from pathlib import Path

from google.cloud import storage


def maybe_fix_gs_path(path: str) -> str:
    """Fix the gs:// path if it's missing the double slash."""
    if path.startswith("gs:/") and not path.startswith("gs://"):
        return path.replace("gs:/", "gs://")
    return path


def get_bucket_name(gs_path: str) -> str:
    """Get the bucket name from a gs:// path."""
    gs_path = maybe_fix_gs_path(gs_path)
    if not gs_path.startswith("gs://"):
        raise ValueError(f"{gs_path} is not a valid gs:// path")
    return gs_path[5:].split("/")[0]


def gs_exists(gs_path: str) -> bool:
    """Check if a file exists in GCS."""
    gs_path = maybe_fix_gs_path(gs_path)
    if not gs_path.startswith("gs://"):
        raise ValueError(f"{gs_path} is not a valid gs:// path")

    bucket_name = get_bucket_name(gs_path)
    # Removes "gs://bucket_name/"
    prefix = gs_path.lstrip(f"gs://{bucket_name}/")
    bucket = storage.Client("system-services-dev").bucket(bucket_name)
    return any(bucket.list_blobs(prefix=prefix))


def path_exists(path: str) -> bool:
    """Check if a file exists in GCS or locally."""
    if path.startswith("gs://"):
        return gs_exists(path)
    return Path(path).exists()

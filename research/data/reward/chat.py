import base64
import pickle
from textwrap import dedent

import numpy as np
from google.cloud import bigquery, storage

from base.datasets.gcs_blob_cache import GCSBlobCache, GCSCheckpointCache
from base.datasets.tenants import DatasetTenant
from base.prompt_format.common import PromptChunk
from base.prompt_format_chat import get_chat_prompt_formatter_by_name
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
    Exchange,
)
from base.prompt_format_retrieve.prompt_formatter import (
    ChatRetrieverPromptInput,
)
from base.tokenizers import Tokenizer
from base.tokenizers.tokenizer import RetrievalSpecialTokens
from research.core.types import Document
from research.data.spark.pipelines.utils import map_parquet
from research.retrieval.retrieval_database import RetrievalDatabase
from research.retrieval.types import Chunk


def get_preferences_samples(
    project_id: str,
    dataset_name: str,
    tenant_name: str,
    start_time: str,
    history_limit: int = 1000000,
):
    """Get preference samples from BQ."""
    query = dedent(f"""
        SELECT *
        FROM {project_id}.{dataset_name}.preference_sample
        WHERE tenant="{tenant_name}"
        AND time > TIMESTAMP("{start_time}")
        ORDER BY time DESC
        LIMIT {history_limit}
    """)

    client = bigquery.Client(project=project_id)
    rows = [*client.query_and_wait(query)]

    clean_rows = []
    for row in rows:
        clean_rows.append(
            {"preference_request_id": row.request_id, **dict(row)["raw_json"]}
        )

    return clean_rows


def get_chat_samples(
    request_ids: list[str],
    project_id: str,
    dataset_name: str,
    tenant_name: str,
):
    """Get chat samples from BQ."""
    request_ids_str = ", ".join(f"'{request_id}'" for request_id in request_ids)
    query = dedent(f"""
        SELECT request.request_id, request.raw_json AS request_json, response.raw_json AS response_json
        FROM {project_id}.{dataset_name}.chat_host_request AS request
        JOIN {project_id}.{dataset_name}.chat_host_response AS response
            ON request.request_id = response.request_id
        WHERE request.request_id IN ({request_ids_str}) AND request.tenant="{tenant_name}"
        ORDER BY request.time DESC
        LIMIT 100000
    """)

    client = bigquery.Client(project=project_id)
    all_rows = list(client.query(query).result())
    assert len(all_rows) == len(request_ids)

    raw_requests = {
        row.request_id: {
            "request": row["request_json"]["request"],
            "response": row["response_json"]["response"],
        }
        for row in all_rows
    }

    return raw_requests


def add_chat_sample_info(
    preference_samples: list[dict],
    project_id: str,
    dataset_name: str,
    tenant_name: str,
):
    """Get chat samples with responses corresponding to preference samples from BQ."""
    all_chat_request_ids = []
    for sample in preference_samples:
        all_chat_request_ids.extend(sample["request_ids"])
    id_to_chat_request_dict = get_chat_samples(
        request_ids=all_chat_request_ids,
        project_id=project_id,
        dataset_name=dataset_name,
        tenant_name=tenant_name,
    )

    def add_info(input_sample: dict) -> dict:
        first_chat_sample = id_to_chat_request_dict[input_sample["request_ids"][0]]
        second_chat_sample = id_to_chat_request_dict[input_sample["request_ids"][1]]
        request = first_chat_sample["request"]

        chat_history = request.get("chat_history", [])
        chat_history = [
            Exchange(
                request_message=exchange["request_message"],
                response_text=exchange["response_text"],
                request_id=exchange.get("request_id"),
            )
            for exchange in chat_history
        ]

        output_sample = {
            # request info
            "prefix": request.get("prefix", ""),
            "suffix": request.get("suffix", ""),
            "path": request.get("path", ""),
            "message": request.get("message", ""),
            "selected_code": request.get("selected_code", ""),
            "chat_history": chat_history,
            "requested_blob_data": request.get("blobs", {}),
            "user_guided_blob_names": request.get("user_guided_blobs", []),
            "lang": request.get("lang", ""),
            "model_name": request.get("model_name", ""),
            "context_code_exchange_request_id": request.get(
                "context_code_exchange_request_id", ""
            ),
            "position": request.get("position", 0),
            # model outputs and feedback
            "model_outputs": [
                first_chat_sample["response"]["text"],
                second_chat_sample["response"]["text"],
            ],
            "scores": input_sample["scores"],
        }
        return output_sample

    samples_with_chat_info = [add_info(sample) for sample in preference_samples]
    return samples_with_chat_info


class DocumentFetcher:
    """Fetch documents corresponding to chat sample blobs from GCS."""

    def __init__(
        self,
        tenant: DatasetTenant,
        gcp_creds,
    ):
        storage_client = storage.Client(
            project=tenant.project_id, credentials=gcp_creds
        )
        blob_bucket = storage_client.bucket(tenant.blob_bucket_name)
        blob_cache_size_bytes = 2**30
        blob_cache_num_threads = 32

        # Create blob cache
        self.blob_cache = GCSBlobCache(
            bucket=blob_bucket,
            bucket_prefix=tenant.blob_bucket_prefix,
            max_size_bytes=blob_cache_size_bytes,
            num_threads=blob_cache_num_threads,
        )

        # Create checkpoint cache
        checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)
        self.checkpoint_cache = GCSCheckpointCache(
            checkpoint_bucket,
            tenant.checkpoint_bucket_prefix,
            blob_cache_size_bytes,
            num_threads=blob_cache_num_threads,
        )

    def get_sample_documents(
        self,
        requested_blob_data: dict,
        user_guided_blob_names: list[str],
    ) -> dict:
        checkpoint_id = requested_blob_data.pop("baseline_checkpoint_id", None)
        checkpoint_blob_names = set()
        if checkpoint_id is not None:
            checkpoint_return = next(iter(self.checkpoint_cache.get([checkpoint_id])))
            if checkpoint_return is not None:
                checkpoint_blob_names = set(checkpoint_return.blob_names)

        added_blob_names = set(
            [base64.b64decode(blob).hex() for blob in requested_blob_data["added"]]
            if "added" in requested_blob_data
            else []
        )
        deleted_blob_names = set(
            [base64.b64decode(blob).hex() for blob in requested_blob_data["deleted"]]
            if "deleted" in requested_blob_data
            else []
        )

        blob_names = sorted(
            list((added_blob_names | checkpoint_blob_names) - deleted_blob_names)
        )
        blobs = [blob for blob in self.blob_cache.get(blob_names) if blob is not None]
        assert len(blobs) == len(blob_names)
        documents = [
            Document(id=blob_name, text=blob.content, path=str(blob.path))
            for blob_name, blob in zip(blob_names, blobs)
            if blob is not None
        ]

        user_guided_blobs = [
            blob
            for blob in self.blob_cache.get(user_guided_blob_names)
            if blob is not None
        ]
        assert len(user_guided_blobs) == len(user_guided_blob_names)
        user_guided_documents = [
            Document(id=blob_name, text=blob.content, path=str(blob.path))
            for blob_name, blob in zip(user_guided_blob_names, user_guided_blobs)
            if blob is not None
        ]

        return {
            "documents": documents,
            "user_guided_documents": user_guided_documents,
        }


class GenerateChatRewardRetrieval:
    """Generate retrieved chunks for chat reward samples."""

    def __init__(
        self,
        retrieval_database_configs: dict[str, dict],
        num_retrieved_chunks: int,
    ):
        self.retrieval_database_configs = retrieval_database_configs
        self._ret_dbs = dict[str, RetrievalDatabase]()
        self.num_retrieved_chunks = num_retrieved_chunks

    def _lazy_init_ret_dbs(self):
        """Loads the retrieval databases."""
        from research.eval.harness.factories import create_retriever

        for key, config in self.retrieval_database_configs.items():
            if key not in self._ret_dbs:
                retrieval_database = create_retriever(config)
                assert isinstance(retrieval_database, RetrievalDatabase)
                self._ret_dbs[key] = retrieval_database
                self._ret_dbs[key].load()

    def _clear_indices(self):
        """Clears the retrieval databases."""
        for db in self._ret_dbs.values():
            db.remove_all_docs()

    def __call__(
        self,
        documents: list[Document],
        prefix: str,
        suffix: str,
        path: str,
        message: str,
        selected_code: str,
        chat_history: list[Exchange],
        **kwargs,
    ) -> dict:
        """Add retrieval to input sample."""

        self._lazy_init_ret_dbs()
        self._clear_indices()

        for retrieval_database in self._ret_dbs.values():
            retrieval_database.add_docs(documents)

        retriever_prompt_input = ChatRetrieverPromptInput(
            prefix=prefix,
            suffix=suffix,
            path=path,
            message=message,
            selected_code=selected_code,
            chat_history=chat_history,
        )

        def convert_to_prompt_chunk(chunk: Chunk, origin: str):
            return PromptChunk(
                text=chunk.text,
                path=chunk.parent_doc.path,
                unique_id=chunk.id,
                char_start=chunk.char_offset,
                char_end=chunk.char_offset + chunk.length,
                origin=origin,
                # We don't have a blob name for the retrieved chunks
            )

        retrieved_chunks_converted = []
        for key, ret_db in self._ret_dbs.items():
            retrieved_chunks, _ = ret_db.query(
                model_input=retriever_prompt_input,
                top_k=self.num_retrieved_chunks,
            )
            retrieved_chunks_converted.extend(
                [
                    convert_to_prompt_chunk(chunk, origin=key)
                    for chunk in retrieved_chunks
                ]
            )

        self._clear_indices()

        return {
            "retrieved_chunks": retrieved_chunks_converted,
            "prefix": prefix,
            "suffix": suffix,
            "path": path,
            "message": message,
            "selected_code": selected_code,
            "chat_history": chat_history,
            **kwargs,
        }


def create_convert_to_prompt_fn(
    tokenizer: Tokenizer,
    prompt_formatter_name: str,
    apportionment_config: ChatTokenApportionment,
) -> map_parquet.FlatMapFn:
    """Create function to convert input sample to prompt."""
    prompt_formatter = get_chat_prompt_formatter_by_name(
        name=prompt_formatter_name,
        tokenizer=tokenizer,
        token_apportionment=apportionment_config,
    )

    def convert_to_prompt(
        path: str,
        selected_code: str,
        prefix: str,
        suffix: str,
        message: str,
        chat_history: list[Exchange],
        retrieved_chunks: list[PromptChunk],
        context_code_exchange_request_id: str,
        model_outputs: list[str],
        scores: dict,
    ) -> dict:
        """Convert input sample to prompt."""

        prefix_len = len(prefix)
        suffix_len = len(suffix)
        selected_code_len = len(selected_code)
        suffix_end = prefix_len + selected_code_len + suffix_len

        chat_prompt_input = ChatPromptInput(
            message=message,
            path=path,
            prefix=prefix,
            selected_code=selected_code,
            suffix=suffix,
            chat_history=chat_history,
            prefix_begin=0,
            suffix_end=suffix_end,
            retrieved_chunks=retrieved_chunks,
            context_code_exchange_request_id=context_code_exchange_request_id,
        )

        model_outputs = model_outputs
        overall_scores = [-scores["overallRating"], scores["overallRating"]]

        chat_prompt_tokens = prompt_formatter.format_prompt(chat_prompt_input).tokens
        assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
        chat_prompt_tokens += [tokenizer.special_tokens.end_of_query]

        for model_output, score in zip(model_outputs, overall_scores):
            output_tokens = tokenizer.tokenize_safe(model_output) + [
                tokenizer.special_tokens.end_of_key
            ]
            score_tokens = tokenizer.tokenize_safe(str(score)) + [
                tokenizer.special_tokens.end_of_key
            ]
            chat_prompt_tokens += output_tokens + score_tokens

        return {"prompt_tokens": chat_prompt_tokens}

    return convert_to_prompt


def create_pad_pack_tokens_fn(seq_len, tokenizer) -> map_parquet.FlatMapFn:
    """Returns a row-wise function to pad and pack tokens."""

    def pad_pack_tokens_fn(prompt_tokens) -> dict:
        if len(prompt_tokens) > seq_len:
            raise ValueError(
                f"token length exceeds seq_len: {len(prompt_tokens)} > {seq_len}"
            )
        num_padding_tokens = seq_len - len(prompt_tokens)
        all_tokens = np.pad(
            prompt_tokens,
            (0, num_padding_tokens),
            constant_values=tokenizer.special_tokens.padding,
        )
        packed_prompt_tokens = all_tokens.astype(np.int32).newbyteorder("<").tobytes()
        return {"prompt_tokens": packed_prompt_tokens}

    return pad_pack_tokens_fn


def serialize_sample(**kwargs) -> dict[str, bytes]:
    return {"sample": pickle.dumps({**kwargs})}


def deserialize_sample(sample: bytes) -> dict:
    return pickle.loads(sample)

"""Spark pipeline for generating reward data.

The pipeline consists of 4 stages:
1. Sample: sample preferences from the dataset.
2. Retrieval: perform dense retrieval on the samples.
3. Prompt: format the samples into prompts.
4. Export: export the prompts to indexed dataset.
"""

import argparse
import json
import pathlib
from dataclasses import asdict, dataclass
from typing import Optional

from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.tenants import AITUTOR_MERCOR, AITUTOR_TURING
from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment
from base.tokenizers import create_tokenizer_by_name
from research.core.utils_for_log import time_string
from research.data.reward.chat import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    GenerateChatRewardRetrieval,
    add_chat_sample_info,
    create_convert_to_prompt_fn,
    create_pad_pack_tokens_fn,
    deserialize_sample,
    get_preferences_samples,
    serialize_sample,
)
from research.data.spark.pipelines.stages.common import export_indexed_dataset_helper
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.utils import k8s_session
from research.data.utils.spark_utils import repartition_and_shuffle


@dataclass
class RewardConfig:
    # General
    run_name: str
    output_root: str
    random_seed: int

    # Sampling
    project_id: str
    dataset_name: str
    tenant_name: str
    start_time: str
    history_limit: int

    # Retrieval
    retriever_config: dict
    num_retrieved_chunks: int

    # Prompt format
    tokenizer_name: str
    prompt_formatter_name: str
    path_len: int
    chat_history_len: int
    prefix_len: int
    suffix_len: int
    max_prompt_len: int
    seq_length: int

    # Export
    num_validation_samples: int

    # Optional arguments
    input_root: Optional[str] = None


def get_spark_cpu(name: str, test: bool = False):
    return k8s_session(
        name=name,
        max_workers=8 if test else 128,
        conf={
            "spark.executor.pyspark.memory": "1000G",
            "spark.executor.memory": "48G",
            "spark.sql.parquet.columnarReaderBatchSize": "32",
            "spark.task.cpus": "2",
        },
    )


def get_spark_gpu(name: str, test: bool = False):
    return k8s_session(
        name=name,
        max_workers=8 if test else 128,
        conf={
            "spark.executor.pyspark.memory": "1000G",
            "spark.executor.memory": "48G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
        },
        gpu_type="RTX_A5000",
    )


TENANTS = {
    "aitutor-turing": AITUTOR_TURING,
    "aitutor-mercor": AITUTOR_MERCOR,
}


def sample_stage(config: RewardConfig, test: bool = False, first_stage: bool = False):
    preference_samples = get_preferences_samples(
        project_id=config.project_id,
        dataset_name=config.dataset_name,
        tenant_name=config.tenant_name,
        start_time=config.start_time,
        history_limit=config.history_limit,
    )
    samples_with_chat_info = add_chat_sample_info(
        preference_samples=preference_samples,
        project_id=config.project_id,
        dataset_name=config.dataset_name,
        tenant_name=config.tenant_name,
    )
    gcp_creds, _ = get_gcp_creds(None)
    document_fetcher = DocumentFetcher(
        tenant=TENANTS[config.tenant_name],
        gcp_creds=gcp_creds,
    )
    samples_with_documents = []
    for sample in samples_with_chat_info:
        sample_documents = document_fetcher.get_sample_documents(
            requested_blob_data=sample.pop("requested_blob_data"),
            user_guided_blob_names=sample.pop("user_guided_blob_names"),
        )
        sample["documents"] = sample_documents["documents"]
        sample["user_guided_documents"] = sample_documents["user_guided_documents"]
        samples_with_documents.append(sample)

    serialized_samples = [
        serialize_sample(**sample) for sample in samples_with_documents
    ]
    spark = get_spark_cpu("samples-with-documents", test=test)
    df = spark.createDataFrame(serialized_samples)
    num_partitions = min(len(serialized_samples) // 50, 50)
    num_partitions = max(num_partitions, 1)
    df = df.repartition(num_partitions)
    write_path = f"{config.output_root}/{config.run_name}/samples_with_documents/"
    df.write.parquet(write_path)
    spark.stop()
    config_dict = asdict(config)
    config_dict["timestamp"] = time_string()
    with open(f"{write_path}config.json", "w") as f:
        json.dump(config_dict, f, indent=2)


def retrieval_stage(
    config: RewardConfig, test: bool = False, first_stage: bool = False
):
    dense_retrieval = GenerateChatRewardRetrieval(
        retrieval_database_configs={"dense_retriever": config.retriever_config},
        num_retrieved_chunks=128,
    )

    user_guided_retrieval = GenerateChatRewardRetrieval(
        retrieval_database_configs={"user_guided_retriever": config.retriever_config},
        num_retrieved_chunks=128,
    )

    def user_guided_retrieval_wrapped(
        user_guided_documents, retrieved_chunks, **kwargs
    ):
        sample_with_user_guided_retrieval = user_guided_retrieval(
            documents=user_guided_documents, **kwargs
        )
        sample_with_user_guided_retrieval["retrieved_chunks"] = (
            retrieved_chunks + sample_with_user_guided_retrieval["retrieved_chunks"]
        )
        return sample_with_user_guided_retrieval

    spark_gpu = get_spark_gpu("samples-with-retrieval", test=test)
    if first_stage and config.input_root is not None:
        input_path = f"{config.input_root}/{config.run_name}/samples_with_documents/"
    else:
        input_path = f"{config.output_root}/{config.run_name}/samples_with_documents/"
    input_path = f"{config.output_root}/{config.run_name}/samples_with_documents/"
    output_path = f"{config.output_root}/{config.run_name}/samples_with_retrieval/"

    result = map_parquet.apply_pandas(
        spark_gpu,
        map_parquet.chain_processors(
            [
                deserialize_sample,
                dense_retrieval,
                user_guided_retrieval_wrapped,
                serialize_sample,
            ]
        ),
        input_path=input_path,
        output_path=output_path,
        timeout=1800,  # 30 mins timeout
        batch_size=16,
    )
    spark_gpu.stop()

    print(result["status_count"])
    print(result["task_info"]["stderr"][0])

    config_dict = asdict(config)
    config_dict["timestamp"] = time_string()
    config_dict["input_path"] = input_path
    config_dict["result"] = dict(status_count=result["status_count"])
    with open(f"{output_path}config.json", "w") as f:
        json.dump(config_dict, f, indent=2)


def prompt_stage(config: RewardConfig, test: bool = False, first_stage: bool = False):
    tokenizer = create_tokenizer_by_name(config.tokenizer_name)

    apportionment_config = ChatTokenApportionment(
        path_len=config.path_len,
        message_len=-1,
        chat_history_len=config.chat_history_len,
        prefix_len=config.prefix_len,
        selected_code_len=-1,
        suffix_len=config.suffix_len,
        max_prompt_len=config.max_prompt_len,  # 2048 represents the max output tokens
    )

    convert_to_prompt = create_convert_to_prompt_fn(
        tokenizer=tokenizer,
        prompt_formatter_name=config.prompt_formatter_name,
        apportionment_config=apportionment_config,
    )
    pad_pack_tokens_fn = create_pad_pack_tokens_fn(
        seq_len=config.seq_length,  # 8192,
        tokenizer=tokenizer,
    )

    spark_cpu = get_spark_cpu("samples-with-prompts", test=test)
    if first_stage and config.input_root is not None:
        input_path = f"{config.input_root}/{config.run_name}/samples_with_retrieval/"
    else:
        input_path = f"{config.output_root}/{config.run_name}/samples_with_retrieval/"
    output_path = f"{config.output_root}/{config.run_name}/samples_with_prompts/"

    result = map_parquet.apply_pandas(
        spark_cpu,
        map_parquet.chain_processors(
            [
                deserialize_sample,
                map_parquet.allow_unused_args()(convert_to_prompt),
                pad_pack_tokens_fn,
            ]
        ),
        input_path=input_path,
        output_path=output_path,
        timeout=1800,  # 30 mins timeout
        batch_size=16,
    )
    spark_cpu.stop()

    print(result["status_count"])
    print(result["task_info"]["stderr"][0])

    config_dict = asdict(config)
    config_dict["timestamp"] = time_string()
    config_dict["input_path"] = input_path
    config_dict["result"] = dict(status_count=result["status_count"])

    with open(f"{output_path}config.json", "w") as f:
        json.dump(config_dict, f, indent=2)


def export_stage(config: RewardConfig, test: bool = False, first_stage: bool = False):
    PROMPT_COLUMN = "prompt_tokens"
    spark_cpu = get_spark_cpu("export-indexed-dataset", test=test)

    tokenizer = create_tokenizer_by_name(config.tokenizer_name)
    if first_stage and config.input_root is not None:
        input_path = f"{config.input_root}/{config.run_name}/samples_with_prompts/"
    else:
        input_path = f"{config.output_root}/{config.run_name}/samples_with_prompts/"
    output_path = f"{config.output_root}/{config.run_name}/indexed_dataset/"

    df = spark_cpu.read.parquet(
        *map_parquet.list_files(
            spark_cpu, input_path, suffix="parquet", include_path=True
        )
    ).select(PROMPT_COLUMN)
    print(f"Input {input_path} has {df.count()} rows.")
    spark_cpu.sparkContext.setJobDescription("Shuffling dataset")
    df = repartition_and_shuffle(random_seed=config.random_seed, df=df)

    spark_cpu.sparkContext.setJobDescription("Creating indexed dataset")
    export_indexed_dataset_helper(
        df,
        vocab_size=tokenizer.vocab_size,
        samples_column=PROMPT_COLUMN,
        num_validation_samples=config.num_validation_samples,
        indexed_dataset_path=pathlib.Path(output_path),
        filter_by_langs=None,
    )
    print(f"[Dataset generated in {output_path}.")
    spark_cpu.stop()
    config_dict = asdict(config)
    config_dict["timestamp"] = time_string()
    config_dict["input_path"] = input_path

    with open(f"{output_path}/config.json", "w") as f:
        json.dump(config_dict, f, indent=2)


STAGE_DICT = {
    "sample": sample_stage,
    "retrieval": retrieval_stage,
    "prompt": prompt_stage,
    "export": export_stage,
}

retriever_config = {
    "scorer": {
        "name": "dense_scorer_v2_fbwd",
        "checkpoint_path": "/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-3",
    },
    "chunker": {"name": "line_level", "max_lines_per_chunk": 30},
    "query_formatter": {
        "name": "base:chatanol6",
        "tokenizer_name": "rogue",
        "max_tokens": 1024,
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "tokenizer_name": "rogue",
        "add_path": True,
        "max_tokens": 1024,
    },
}

config = RewardConfig(
    project_id="system-services-prod",
    dataset_name="prod_request_insight_full_export_dataset",
    tenant_name="aitutor-turing",
    start_time="2024-06-23",
    history_limit=3,
    output_root="/mnt/efs/spark-data/shared/reward/",
    run_name="test",
    retriever_config=retriever_config,
    num_retrieved_chunks=256,
    tokenizer_name="llama3_instruct",
    prompt_formatter_name="binks_llama3_tokenized",
    path_len=256,
    chat_history_len=2048,
    prefix_len=1024,
    suffix_len=1024,
    max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
    seq_length=8192,
    random_seed=0,
    num_validation_samples=0,
)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()

    stage_defaults = [
        "sample",
        "retrieval",
        "prompt",
        "export",
    ]

    parser.add_argument(
        "--stages",
        "-s",
        type=str,
        help="stage to run",
        nargs="+",
        default=stage_defaults,
    )

    parser.add_argument("--test", "-t", action="store_true", help="test")
    parser.add_argument("--run_name", "-r", type=str, help="run name")
    args = parser.parse_args()

    if args.run_name is not None:
        config.run_name = args.run_name

    first_stage = True
    for stage_name in args.stages:
        STAGE_DICT[stage_name](config, test=args.test, first_stage=first_stage)
        first_stage = False

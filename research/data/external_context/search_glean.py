"""Client for searching Glean.

Eventually we need to move this to base.
The only current research dependency is how we provision an API token.
In research, we use a service account token stored in K8s secrets.
In base this needs to be more flexible and afford an auth flow.
"""

import concurrent.futures
from dataclasses import dataclass, field
import requests
import logging
import time
import uuid

from research.core.augment_secrets import get_k8s_secret


@dataclass
class GleanSearchChunk:
    """Represents a single chunk from a Glean search result."""

    text: str
    """The text of the snippet."""

    document: "GleanSearchResult"
    """The result that this snippet belongs to."""

    chunk_index: int
    """The index of the chunk in the document."""

    chunk_id: str = field(init=False)
    """A unique 4-character identifier for the chunk."""

    def __post_init__(self):
        self.chunk_id = str(uuid.uuid4())[:4]


@dataclass
class GleanSearchResult:
    """Represents a single search result from Glean."""

    document_id: str
    """The document ID of the result from Glean."""

    data_source: str
    """The data source of the result from Glean. `slack`, `notion`, etc."""

    document_type: str
    """The document type of the result from Glean. `page`, `message`, `channel`, etc."""

    title: str
    """The title of the document."""

    url: str
    """The original URL of the document, directly from the source not through Glean.  Auth will be required."""

    snippets: list[str]
    """The snippets of the document that match the search query.
    A best effort is made to order the snippets by natural order, but this is not guaranteed.
    Some snippets might be fragmented or incomplete.
    """

    raw_response: dict
    """The raw response from Glean, for debugging purposes."""

    content: list[str]
    """The full content of the document, if requested."""

    def to_markdown(self, use_content: bool = True) -> str:
        """Convert the search result to markdown string."""
        content = [f"# [{self.title}]({self.url})\n"]
        if use_content and self.content:
            content.append("\n\n".join(self.content) + "\n")
        else:
            for snippet in self.snippets:
                content.append(f"{snippet}\n\n")
        return "\n".join(content)

    def chunk_content(self, chunk_size: int = 2000) -> list[GleanSearchChunk]:
        """Break the content into chunks.
        Always leverage the pre-chunking in Glean results.
        """
        chunks = []
        current_chunk = GleanSearchChunk(text="", document=self, chunk_index=0)
        for snippet in self.content:
            if (
                current_chunk.text
                and len(current_chunk.text) + len(snippet) > chunk_size
            ):
                chunks.append(current_chunk)
                current_chunk = GleanSearchChunk(
                    text="", document=self, chunk_index=len(chunks)
                )
            current_chunk.text += snippet
        if current_chunk.text:
            chunks.append(current_chunk)
        return chunks


class GleanSearchClient:
    """Client for searching Glean documents."""

    def __init__(self, server_url: str, token: str | None = None, oauth: bool = False):
        """Initialize the Glean search client.

        Args:
            token: Optional API token. If not provided, will be fetched from K8s secrets.
            oauth: Whether this is an OAuth token.  Set it to True if you are using an OAuth
                token instead of a Glean issued API token.
        """
        self.server_url = server_url
        if token is None:
            token = get_k8s_secret(
                name="research-glean-search-token", field="token", namespace="gcp-us1"
            )
        self.token = token
        self.api_base = f"{self.server_url}/rest/api/v1"
        self.headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json",
        }
        self.content_cache: dict[str, list[str]] = {}
        if oauth:
            self.headers["X-Glean-Auth-Type"] = "OAUTH"

    def search(
        self,
        query: str,
        max_results: int = 10,
        max_snippet_size: int = 4000,
        additional_params: dict | None = None,
        content: bool = False,
    ) -> list[GleanSearchResult]:
        """Search Glean documents.

        Args:
            query: Search query string
            max_results: Maximum number of results to return
            max_snippet_size: Maximum size of each snippet in characters
            additional_params: Additional parameters to pass to the Glean API
                See https://developers.glean.com/client/operation/search/ for details.
            content: Whether to return the full content of the document.
                This is not recommended for large documents.

        Returns:
            List of GleanSearchResult objects
        """
        url = f"{self.api_base}/search"

        payload = {
            "query": query,
            "pageSize": max_results,
            "maxSnippetSize": max_snippet_size,
            "requestOptions": {"returnLlmContentOverSnippets": True},
        }
        if additional_params:
            payload.update(additional_params)

        response = requests.post(url, headers=self.headers, json=payload)
        response.raise_for_status()

        results = []
        for item in response.json().get("results", []):
            # Admittedly, the Glean API is not very consistent.
            # For Slack, the result entries do not have the snippets field, only fullTextList.
            # However, `fullTextList` can contain single text blocks that expands beyond the
            # relevant conversation and captures large portions of irrelevant context.
            # Instead, there's an undocumented `relatedResults` field that contains a list
            # of conversations.  From this list, you can use the document ID of the result itself
            # to identify an identical copy of the current result itself in the list.
            # This copy would have a `snippets` field that contains the relevant snippets.
            # The results maybe contain either a `snippets` field or a `fullTextList` field.
            # So the process is this:
            # 1. we use snippets if possible
            # 2. if not, try to see if there are snippets in any entries in relatedResults
            # 3. if not, use fullTextList if available
            document_id = item.get("document", {}).get("id", "")

            snippets = []
            # Use the snippets field if possible, which is not always there.
            if item.get("snippets", []):
                snippets = item["snippets"]
            # If not, try to find the copy of this result in relatedResults
            elif document_id:
                # relatedResults is a list of objects, each have a results list.
                # So far I only see one; it is not clear what it is for or if it
                # might contain multiple items in this list.
                # This field is not mentioned in the documentation.
                # Example of a result entry from Slack
                # {
                #    "document": {...},
                #    "title": "...",
                #    "url": "...",
                #    "fullTextList": [...],"
                #    "relatedResults": [
                #        {
                #            "results": [
                #                {
                #                    "document": {
                #                        "id": "..."
                #                    },
                #                    "snippets": [...]
                #                }
                #            ]
                #        }
                #    ],
                #    ...
                # }
                for group in item.get("relatedResults", []):
                    for related_item in group.get("results", []):
                        if (
                            related_item.get("document", {}).get("id", "")
                            == document_id
                        ):
                            snippets = related_item.get("snippets", [])
                            logging.debug(
                                "Found snippets in related results for document ID: %s",
                                document_id,
                            )
                    # It is not clear if there might be multiple groups or items in the list,
                    # or if they may overlap.  But if we already found a copy, we can probably stop.
                    if snippets:
                        break
                else:
                    logging.debug(
                        "No snippets found in related results for document ID: %s",
                        document_id,
                    )
            else:
                logging.debug("No snippets but also no document ID for matching.")

            snippets = [
                snippet["text"]
                for snippet in sorted(
                    snippets, key=lambda x: x.get("snippetTextOrdering", 0)
                )
                if "text" in snippet
            ]

            doc = item.get("document", {})
            doc_id = doc.get("id", "")
            data_source = doc.get("datasource", "")
            document_type = doc.get("docType", "")
            # Otherwise use fullTextList if available.  It's not clean, but better than nothing.
            # Note that this is just a list of strings, unlike snippets which are objects.
            if not snippets and "fullTextList" in item:
                snippets = item["fullTextList"]
                logging.debug("Using fullTextList for document ID: %s", doc_id)

            result = GleanSearchResult(
                document_id=doc_id,
                data_source=data_source,
                document_type=document_type,
                title=item.get("title", ""),
                url=item.get("url", ""),
                snippets=snippets,
                raw_response=item,
                content=[],  # Initialize with empty list
            )
            results.append(result)

        # Use thread pool to submit get_document() calls in parallel

        if content:
            with concurrent.futures.ThreadPoolExecutor() as executor:
                futures = []
                for result in results:
                    future = executor.submit(self.get_document, result.document_id)
                    futures.append((result, future))

                # Collect results
                for result, future in futures:
                    try:
                        result.content = future.result()
                    except Exception as e:
                        logging.warning(
                            "Failed to get document content for document ID: %s. Error: %s",
                            result.document_id,
                            e,
                        )

        return results

    def get_document(self, document_id: str) -> list[str]:
        """Get a document by ID."""
        if document_id in self.content_cache:
            return self.content_cache[document_id]
        url = f"{self.api_base}/getdocuments"
        payload = {
            "documentSpecs": [{"id": document_id}],
            "includeFields": ["DOCUMENT_CONTENT"],
        }
        max_retries = 3
        retry_delay = 0.2
        for attempt in range(max_retries):
            try:
                response = requests.post(url, headers=self.headers, json=payload)
                response.raise_for_status()
                resp_doc = response.json()
                break
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 429 and attempt < max_retries - 1:
                    logging.warning("Response headers: ", e.response.headers)
                    sleep_time = retry_delay * (2**attempt)
                    logging.warning(f"Rate limited. Retrying in {sleep_time} seconds.")
                    time.sleep(sleep_time)
                else:
                    raise
        else:
            raise RuntimeError("Max retries reached. Failed to get document.")
        if "documents" not in resp_doc or document_id not in resp_doc["documents"]:
            raise ValueError("Specified document not found in response.")
        doc = resp_doc["documents"][document_id]
        if "content" not in doc:
            raise RuntimeError(
                "Get document response does not have content. Check Glean deployment configs!"
            )
        content = doc["content"]
        if "fullTextList" in content and content["fullTextList"]:
            result = content["fullTextList"]
        elif "fullText" in content:
            result = [content["fullText"]]
        else:
            raise RuntimeError("Failed to full text from document content.")
        self.content_cache[document_id] = result
        return result

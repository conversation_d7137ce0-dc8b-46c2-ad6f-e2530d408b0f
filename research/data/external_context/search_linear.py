from dataclasses import dataclass
from datetime import datetime
import requests
from research.core.augment_secrets import get_k8s_secret


@dataclass
class LinearComment:
    """Represents a comment on a Linear issue."""

    body: str
    author: str
    created_at: datetime
    updated_at: datetime | None = None


@dataclass
class LinearIssue:
    """Represents a Linear issue with its comments."""

    id: str
    title: str
    description: str
    comments: list[LinearComment]


class LinearSearchClient:
    def __init__(self):
        linear_api_key = get_k8s_secret(
            name="research-linear-search-token", field="token", namespace="gcp-us1"
        )
        self.api_url = "https://api.linear.app/graphql"
        self.headers = {
            "Authorization": linear_api_key,
            "Content-Type": "application/json",
        }

    def search_issues(
        self, query: str, max_results: int = 10, include_comments: bool = True
    ) -> list[LinearIssue]:
        """Search Linear issues using the GraphQL API.

        Args:
            query: Search term
            max_results: Maximum number of results to return
            include_comments: Whether to fetch comments (can make query significantly slower)
        """
        if max_results <= 0:
            raise ValueError("max_results must be a positive integer")

        # Define the comments fragment only if needed
        comments_fragment = (
            """
            comments {
                nodes {
                    body
                    user {
                        name
                        email
                    }
                    createdAt
                    updatedAt
                }
            }
        """
            if include_comments
            else ""
        )

        graphql_query = f"""
        query($term: String!, $first: Int!) {{
            searchIssues(term: $term, first: $first) {{
                nodes {{
                    id
                    title
                    description
                    {comments_fragment}
                }}
            }}
        }}
        """

        variables = {"term": query, "first": max_results}

        try:
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json={"query": graphql_query, "variables": variables},
            )
            response.raise_for_status()
            data = response.json()

            if "errors" in data:
                print(f"GraphQL Errors: {data['errors']}")
                return []

            nodes = data.get("data", {}).get("searchIssues", {}).get("nodes", [])

            return [
                LinearIssue(
                    id=node["id"],
                    title=node["title"],
                    description=node.get("description", ""),
                    comments=[
                        LinearComment(
                            body=comment["body"],
                            author=self._get_author_from_comment(comment),
                            created_at=datetime.fromisoformat(
                                comment["createdAt"].replace("Z", "+00:00")
                            ),
                            updated_at=datetime.fromisoformat(
                                comment["updatedAt"].replace("Z", "+00:00")
                            )
                            if comment.get("updatedAt")
                            else None,
                        )
                        for comment in node.get("comments", {}).get("nodes", [])
                    ]
                    if include_comments
                    else [],
                )
                for node in nodes
            ]
        except requests.exceptions.RequestException as e:
            print(f"Request failed: {str(e)}")
            print(
                f"Response content: {e.response.text if hasattr(e, 'response') else 'No response'}"
            )
            raise

    def _get_author_from_comment(self, comment: dict) -> str:
        """Extract author information from a comment safely."""
        user_data = comment.get("user", {})
        if not user_data:
            return "Unknown"

        return user_data.get("name") or user_data.get("email") or "Unknown"


def main():
    client = LinearSearchClient()

    # Example: Search for issues without comments (faster)
    print("Searching for issues containing 'bug'...")
    issues = client.search_issues("bug", max_results=5, include_comments=False)
    for issue in issues:
        print(f"Found issue: {issue.id} - {issue.title}")

    # Example: Search with comments (slower but includes full context)
    print("\nSearching for issues with comments...")
    issues_with_comments = client.search_issues(
        "bug", max_results=2, include_comments=True
    )
    for issue in issues_with_comments:
        print(f"\nIssue: {issue.title}")
        print(f"Number of comments: {len(issue.comments)}")


if __name__ == "__main__":
    main()

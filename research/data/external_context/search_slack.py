"""Research hook to search Slack.
This is for eval only, and only works for Augment Slack.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Any

from research.core.augment_secrets import get_k8s_secret
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError


@dataclass
class SlackMessage:
    """Represents a Slack message search result."""

    text: str
    user: str
    ts: str
    permalink: str
    channel_id: str
    channel_name: str
    team: str
    thread_ts: Optional[str] = None
    raw_json: dict[str, Any] = None

    @property
    def timestamp(self) -> datetime:
        """Convert Slack timestamp to datetime."""
        return datetime.fromtimestamp(float(self.ts))


class SlackSearchClient:
    """Client for searching Slack messages."""

    def __init__(self, token: Optional[str] = None):
        """Initialize the Slack search client."""
        if token is None:
            token = get_k8s_secret(
                name="research-slack-search-token", field="token", namespace="gcp-us1"
            )
        self.client = WebClient(token=token)

    def _extract_text_from_blocks(self, blocks: list[dict]) -> str:
        """Extract text content from Slack blocks.

        Args:
            blocks: List of Slack block objects

        Returns:
            Concatenated text from all blocks
        """
        text = []
        for block in blocks:
            if block["type"] == "section" and "text" in block:
                text.append(block["text"].get("text", ""))
        return "\n".join(text)

    def search_messages(
        self,
        query: str,
        count: int = 100,
        sort: str = "relevant",
        sort_dir: str = "desc",
    ) -> list[SlackMessage]:
        """Search for messages in Slack."""
        try:
            response = self.client.search_messages(
                query=query, count=count, sort=sort, sort_dir=sort_dir
            )

            messages: list[SlackMessage] = []
            for match in response.data["messages"]["matches"]:
                # Get text from either direct text field or blocks
                text = match["text"]
                if not text and "blocks" in match:
                    text = self._extract_text_from_blocks(match["blocks"])

                messages.append(
                    SlackMessage(
                        text=text,
                        user=match["user"],
                        ts=match["ts"],
                        permalink=match["permalink"],
                        channel_id=match["channel"]["id"],
                        channel_name=match["channel"]["name"],
                        team=match["team"],
                        thread_ts=match.get("thread_ts"),
                        raw_json=match,
                    )
                )
            return messages

        except SlackApiError as e:
            print(f"Error searching Slack: {e.response['error']}")
            return []

"""Create prechunked version of the docsets."""

import uuid

import pandas as pd
import logging

from research.data.external_context.html_chunker import HTMLChunker
from research.retrieval.types import Document
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark import k8s_session


logging.basicConfig(level="INFO")

PARQUET = "gs://gcp-us1-spark-data/shared/nl-datasets/devdocs/parquet/"
CHUNKED_DOCS = "gs://gcp-us1-spark-data/shared/nl-datasets/devdocs/chunked/"

MAX_LEAF_SIZE = 2000
"""Max size of each chunk."""

MAX_HEADER_RATIO = 0.5
"""Max ratio of header budget relative to the total size of the chunk."""


def get_chunks(pdf: pd.DataFrame):
    """Take HTML pages and generate markdown chunks.

    Chunks also contain associated metadata, the offset of the chunk, and
    the length of the chunk.
    """
    chunker = HTMLChunker(max_leaf_size=MAX_LEAF_SIZE)
    chunks = []
    for _, row in pdf.iterrows():
        project = row["project"]
        version = row["version"] or ""
        path = row["path"]
        title = row["page_name"]
        # If the page is index.html, use the parent directory as the title.
        if title == "index":
            if "/" in path:
                title = path.split("/")[-2]
            else:
                title = project
        if version:
            docset = f"{project}~{version}"
        else:
            docset = project
        page_id = str(uuid.uuid4())
        doc = Document(
            id=page_id,
            text=row["content"],
            path=path,
            meta={
                "page_id": page_id,
                "docset_name": docset,
                "project": project,
                "version": version,
                "title": title,
                "page_url": path,
            },
        )
        doc_chunks = chunker.split_into_chunks(doc)
        if not doc_chunks:
            continue

        for chunk in doc_chunks:
            if not chunk.text:
                continue
            chunks.append(
                {
                    "text": chunk.text,
                    "path": chunk.parent_doc.path,
                    "char_offset": chunk.char_offset,
                    "length": chunk.length,
                    "line_offset": chunk.line_offset,
                    "length_in_lines": chunk.length_in_lines,
                    "meta": chunk.meta,
                }
            )

    if not chunks:
        return
    pdf = pd.DataFrame(chunks)
    # Remove rows where there are not valid chunks.
    pdf = pdf[pdf["text"].astype(bool)]
    return pdf.fillna("")


def main():
    spark = k8s_session(
        max_workers=100,
        conf={
            "spark.executor.memory": "120G",
            "spark.executor.pyspark.memory": "200G",
        },
    )

    map_parquet.apply_pandas(
        spark,
        get_chunks,
        PARQUET,
        CHUNKED_DOCS,
        batch_size=100,
        timeout=2 * 60 * 60,
        task_info_location="gs://gcp-us1-temporary/devdocs/chunking/",
        ignore_error=True,
    )
    spark.stop()


if __name__ == "__main__":
    main()

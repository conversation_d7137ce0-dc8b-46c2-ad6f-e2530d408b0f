"""A chunker that chunks based on HTML structure."""

from research.retrieval.chunking_functions import register_chunker
from research.retrieval.types import <PERSON><PERSON>, Document, Chunk
from research.data.external_context.html_chunker.chunk_html_by_scope import (
    get_articles,
    populate_node,
    iter_premerged_chunks,
)


@register_chunker("html_chunker")
class HTMLChunker(Chunker):
    """Split a document into chunks based on HTML structure.

    Expected metatdata field from documents (in `meta`):
    - title: title or header of the document

    Meta field will be passed on to chunks, and these additional fields will be added:
    - page_id:  A unique identifier for the page, which can be used to deduplicate chunks.
    - headers:  A list of headers for the chunk, in order.
    """

    def __init__(self, max_leaf_size: int = 1000, max_header_ratio: float = 0.5):
        self.max_leaf_size = max_leaf_size
        self.max_header_ratio = max_header_ratio

    def split_into_chunks(self, doc: Document) -> list[Chunk]:
        if doc.meta:
            title = doc.meta.get("title", "")
        else:
            title = ""
        # metadata for the page
        page_meta = doc.meta or {}
        chunks: list[Chunk] = []
        total_lines = 0
        char_offset = 0

        for article in get_articles(doc.text):
            root = populate_node(article, title, self.max_leaf_size)
            if not root:
                continue
            for headers, content in iter_premerged_chunks(root, self.max_leaf_size):
                lines = len(content.splitlines())
                chunk = Chunk(
                    id=f"{doc.id}-{len(chunks)}",
                    text=content,
                    parent_doc=doc,
                    char_offset=char_offset,
                    length=len(content),
                    line_offset=total_lines,
                    length_in_lines=lines,
                    header="\n\n".join(headers),
                    meta={**page_meta, "headers": headers},
                )
                chunks.append(chunk)
                char_offset += len(content)
                total_lines += lines
        return chunks

"""Parse an HTML documentation into chunks based on the DOM tree.

Leverage HTML tags to construct hierarchical structure and annotate
each chunk with context headers based on it..
"""

import logging
from dataclasses import dataclass
from typing import Generator, Iterable

from bs4 import BeautifulSoup, Tag

from research.data.collection.utils.markdown_conversion import (
    cleanup,
    robust_conversion,
)


logger = logging.getLogger(__name__)


@dataclass
class DocNode:
    """A node in the documentation graph."""

    soup: BeautifulSoup

    full_md: str

    children: list["DocNode"]

    header: str
    """Header in markdown. These can be title tags, table headings etc."""

    content: str
    """Content of this current node, not including children, in markdown."""

    def iter_nodes(self):
        """Iterate all nodes in the tree."""
        yield self
        for child in self.children:
            yield from child.iter_nodes()

    def iter_paths(self, parent_path: list["DocNode"] | None = None):
        """Iterate all paths from root to nodes in the tree"""
        parent_path = parent_path or []
        my_path = parent_path + [self]
        yield my_path
        for child in self.children:
            if child is None:
                continue
            yield from child.iter_paths(my_path)


MAX_DEPTH = 12
HEADER_TAGS = [
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6",
    "dt",
    "title",
    "caption",
    "figcaption",
]
ARTICLE_TAGS = ["article", "main", "body"]


def populate_node(soup, header: str, max_leaf_size: int, depth: int = 0):
    """Recursively parse a soup object into trees based on heuristics.

    Nodes are whole entities that preferably have a title.  The basic heuristics
    is as follows:

    - do some clean up and strip out all tags that wraps the entire current block
    to reveal the list of content inside.

    - If the entire block is small enough after converted to markdown, or it is a
    single code block, or depth is at the limit, then no further divison.

    - Split the direct children into different blocks.

    Splitting follows the following procedure:

    - If header-like tags exist in the top level of the current DOM subtree, for example
      tags like <h1>, <h2>... and <dt>, <title>, <caption>, <figcaption>, then take
      all the highest level title tags and make each the header of a new subblock.
      For instance, this soup block:

      content1
      <h1> Title 1 </h1>
      content2
      content3
      <h2> title 1.2 </h2>
      content4
      <h1> Title 2 </h1>
      content5

      Will have two child blocks:
      my_content: content1
      child1:
        header: Title 1
        recursively sent to populate_node: |
          content2
          content3
          <h2> title 1.2 </h2>
          content4
      child2:
        header: Title 2
        recursively sent to populate_node: content 5

    - If there are no headers, we look for <section> and <dl> tags;
      these indicate natural sections of the documentation and are frequently used.
      Each element will be a separate child, and if any other elements exist
      between them, consecutive ones will be grouped into separate children.

    - Otherwise, cut elements into children based on size limit, and in this
      mode there's no headings.  However, it will at least respect the DOM structure
      and keep the same subtree together.   We will group 1 to N subtrees until the
      estimated chunk size is over the limit, and send the content recursively to
      another populate_node call
    """
    depth += 1
    logger.debug("Entering populate_node with depth %d", depth)

    # make a copy of the soup
    soup = BeautifulSoup(str(soup), "html5lib")
    # remove all empty tags
    for tag in soup.find_all(recursive=True):
        if not tag.get_text(strip=True):
            tag.unwrap()
    # Unwrap all divs that holds a single item
    for tag in soup.find_all("div"):
        # don't do this inside pre
        if tag.find_parent("pre"):
            continue
        if len(tag.contents) < 2:
            tag.unwrap()

    while len(soup.contents) == 1:
        tag = soup.find(recursive=False)
        if not tag or tag.name in ("pre", "code", "p"):
            # This cannot be further broken up
            logger.debug("Stopping recursive because tag is %s", tag and tag.name)
            full_md = robust_conversion(str(soup), cleanup_html=False)
            return DocNode(
                soup=soup,
                full_md=full_md,
                children=[],
                header=header,
                content=full_md,
            )
        tag.unwrap()

    if not soup.contents:
        if header:
            logger.debug("Stopping recursive because there is no content")
            return DocNode(
                soup=soup,
                full_md="",
                children=[],
                header=header,
                content="",
            )
        else:
            return None

    # Include entire doc as content if it is small enough
    full_md = robust_conversion(str(soup), cleanup_html=False)
    if len(full_md) < max_leaf_size or depth >= MAX_DEPTH:
        logger.debug(
            "Stopping recursive call at size=%d, depth=%d", len(full_md), depth
        )
        first = soup.find()
        if isinstance(first, Tag) and first.name in HEADER_TAGS:
            header = robust_conversion(str(first), cleanup_html=False).strip()
            first.decompose()
            return DocNode(
                soup=soup,
                full_md=full_md,
                children=[],
                header=header,
                content=robust_conversion(str(soup), cleanup_html=False),
            )
        else:
            return DocNode(
                soup=soup,
                full_md=full_md,
                children=[],
                header=header,
                content=full_md,
            )

    # Check if there are headers.  If so split by headers.
    for header_tag in HEADER_TAGS:
        if not soup.find(header_tag, recursive=False):
            continue
        logger.info("Splitting based on header tag `%s` for %s", header_tag, header)
        current_header = None
        content = ""
        children = []
        current_child_elements = []
        for item in soup.contents:
            if not item.get_text(strip=True):
                continue
            if isinstance(item, Tag) and item.name == header_tag:
                if current_header or current_child_elements:
                    html = "".join([str(child) for child in current_child_elements])
                    if current_header is None:
                        content = robust_conversion(html, cleanup_html=False)
                        # Check if contents before first header is too big
                        if len(content) >= max_leaf_size:
                            children.append(
                                populate_node(html, "", max_leaf_size, depth)
                            )
                            content = ""
                    else:
                        children.append(
                            populate_node(html, current_header, max_leaf_size, depth)
                        )
                current_child_elements = []
                current_header = robust_conversion(
                    str(item), cleanup_html=False
                ).strip()
                logger.debug("Found header %s for %s", current_header, item.name)
            else:
                current_child_elements.append(item)
        if current_child_elements:
            html = "".join([str(child) for child in current_child_elements])
            if current_header is None:
                content = robust_conversion(html, cleanup_html=False)
            else:
                children.append(
                    populate_node(html, current_header, max_leaf_size, depth)
                )
        if len(children) == 1 and not header and not content:
            return children[0]
        return DocNode(
            soup=soup,
            full_md=full_md,
            children=children,
            header=header,
            content=content,
        )

    # Check if there are sections.  If so make each sections a child.
    if soup.find("section", "dl"):
        logger.info("Splitting based on sections for %s", header)
        content_html = ""
        in_sections = False
        sections = []
        for item in soup.contents:
            if not item.get_text(strip=True):
                continue
            if isinstance(item, Tag) and item.name in ("section", "dl"):
                in_sections = True
            if in_sections:
                sections.append(item)
            else:
                content_html += str(item)
        content = robust_conversion(content_html, cleanup_html=False)
        return DocNode(
            soup=soup,
            full_md=full_md,
            children=[
                populate_node(section, "", max_leaf_size, depth) for section in sections
            ],
            header=header,
            content=content,
        )

    # Check if there are dynamic toggles.
    # If so the toggle element is the title and toggled element is the content.
    top_level_toggled_elements = {}
    if soup.find(attrs={"data-toggle": True}):
        # The toggle has to be on the top level.  First collect top level ids.
        toggled_ids = set()

        # Look for elements with data-target or href attributes
        for tag in soup.find_all(attrs={"data-target": True}):
            target_id = tag["data-target"].strip("#")
            if target_id:
                toggled_ids.add(target_id)

        # check if any of these are top level
        for element in soup.contents:
            if not element or not isinstance(element, Tag) or not element.attrs:
                continue
            if element.get("id") in toggled_ids:
                top_level_toggled_elements[element.get("id")] = str(element)

    if top_level_toggled_elements:
        logger.info("Splitting based on toggles for %s", header)
        top_level_toggled_ids = set(top_level_toggled_elements.keys())
        children = []
        current_html = ""
        for item in soup.contents:
            if not item.get_text(strip=True):
                continue
            if not isinstance(item, Tag):
                current_html += str(item)
                continue
            if item.get("id") in top_level_toggled_ids:
                continue
            # find if i toggle any top level elements
            matched = set()
            for tag in item.find_all(attrs={"data-target": True}):
                target_id = tag["data-target"].strip("#")
                if target_id in top_level_toggled_elements:
                    matched.add(target_id)
            if matched:
                if current_html:
                    children.append(
                        populate_node(current_html, "", max_leaf_size, depth)
                    )
                    current_html = ""
                # the current element is the title, toggled element is the content
                title = robust_conversion(str(item), cleanup_html=False).strip()
                for target_id in matched:
                    item = top_level_toggled_elements.pop(target_id)
                    children.append(populate_node(item, title, max_leaf_size, depth))
                continue
            current_html += str(item)
        if current_html:
            children.append(populate_node(current_html, "", max_leaf_size, depth))
        if len(children) == 1 and not header:
            return children[0]
        return DocNode(
            soup=soup,
            full_md=full_md,
            children=children,
            header=header,
            content="",
        )

    # No sections.   Will divide by size.
    logger.info("Splitting based on size for %s, %d items", header, len(soup.contents))
    children = []
    current_html = ""
    current_text_len = 0
    starter = False
    items = 0
    for item in soup.contents:
        text = item.get_text(strip=True)
        text_len = len(text)
        # a starter element is a single line element ending with `:`
        last_item_starter = starter
        starter = len(text.splitlines()) == 1 and text.endswith(":")
        if (
            not last_item_starter
            and (
                current_text_len + text_len >= max_leaf_size * 0.9
                or starter
                and current_text_len + text_len >= max_leaf_size * 0.4
            )
            or items >= len(soup.contents) - 1
        ):
            if current_html:
                children.append(populate_node(current_html, "", max_leaf_size, depth))
            current_html = ""
            items = 0
            current_text_len = 0
        if text_len > max_leaf_size * 0.9 and not last_item_starter:
            children.append(populate_node(item, "", max_leaf_size, depth))
        else:
            current_html += str(item)
            current_text_len += text_len
            items += 1
    if current_html:
        children.append(populate_node(current_html, "", max_leaf_size, depth))
    if len(children) == 1 and not header:
        return children[0]
    return DocNode(
        soup=soup,
        full_md=full_md,
        children=children,
        header=header,
        content="",
    )


def get_articles(content: str):
    """Get all articles in the content HTML.

    This uses conventional HTML tags to extract core contents of a page that
    are not unrelated spoiler plates.
    """
    cleaned = cleanup(content, keep_links=False)
    soup = BeautifulSoup(cleaned, "html5lib")
    for tag_name in ARTICLE_TAGS:
        if soup.find(tag_name):
            return [
                article
                for article in soup.find_all(tag_name)
                if not article.find_parent(tag_name)
            ]
    return [soup]


def format_chunk(
    path: list["DocNode"],
    max_leaf_size: int = 1000,
    max_header_ratio: float = 0.5,
    min_content_size: int = 20,
) -> Generator[list[str], None, None]:
    """Takes the node lists and return lists of headers and content.

    Takes into consideration all the parent nodes up to the root.

    All headers from parent nodes are included here, unless they exceed the
    max_header_ratio.

    In the generated output, each iteration is a list of strings.  The last
    element is the content.  The rest are headers.
    """
    headers = []
    content = ""
    header_size = 0
    for node in path:
        if not node.header:
            continue
        if header_size + len(node.header) + 2 > max_leaf_size * max_header_ratio:
            content += node.header + "\n\n"
        else:
            headers.append(node.header)
            header_size += len(node.header) + 2

    budget = max(max_leaf_size - header_size, int(1 - max_header_ratio) * max_leaf_size)
    content = content + path[-1].content.strip() + "\n"
    while content:
        if len(content) < budget * 1.2:
            yield headers + [content]
            break
        pos = content[:budget].rfind("\n")
        if pos < min_content_size:
            yield headers + [content[:budget]]
            content = content[budget:]
        else:
            yield headers + [content[:pos]]
            content = content[pos:].lstrip()


def premerge_paths(buffer: list[list[str]]):
    """Merge adjacent paths and return the merged one.

    The new header path is the common shared prefix of all paths.
    all other non-shared paths go into the content.
    """

    shared_path = buffer[0]
    for item in buffer[1:]:
        # get the common prefix of lists shared_path and item
        for i, path_node in enumerate(shared_path):
            if path_node != item[i]:
                shared_path = shared_path[:i]
                break
    # Construct the new content
    content = ""
    prev = shared_path
    for item in buffer:
        for node in item:
            if node not in prev:
                content += node + "\n\n"
        prev = item

    # Just in case all items are shared.
    if not content and shared_path:
        content = shared_path[-1]
        shared_path = shared_path[:-1]
    return shared_path, content


def iter_premerged_chunks(
    root: DocNode, max_leaf_size: int = 1000
) -> Iterable[tuple[list[str], str]]:
    """Iterate the tree in pre-order and pre-merge chunks that are too small."""
    buffer = []
    current_size = 0
    # Each iteration returns a root-to-leaf path on the document tree, as a list of nodes
    for node_path in root.iter_paths():
        if not node_path or not node_path[-1].content.strip():
            continue
        # Converts the path from list of nodes to list of strings
        # Perform pre-merging of small chunks
        for str_path in format_chunk(node_path, max_leaf_size):
            item_size = sum(len(s) + 2 for s in str_path)
            if current_size + item_size > max_leaf_size and buffer:
                yield premerge_paths(buffer)
                buffer = []
                current_size = 0
            buffer.append(str_path)
            current_size += item_size
    if buffer:
        yield premerge_paths(buffer)

"""Infer the human readable name and title for a docset."""

import json
import time

from research.llm_apis.chat_utils import Chat<PERSON>lient
from research.llm_apis.chat_utils import AnthropicClient
from research.core.augment_secrets import get_k8s_secret


SYSTEM_PROMPT = """Please turn the ID of a particular documentation in a documentation collection into a human readable name, title, and aliases.
The IDs are in the form of `project~version`, where `project` is the name of the project, and `version` is the version of the project.
Both have been cleaned up to be used as IDs, or just `project` if version info is not available.

Your response should consist of three lines:
- the first line is the human readable name
- the second line is the title, with no other description or formatting of any kind.
- the third line is a list of aliases for the project in JSON list format.

NEVER add any description or notes.  Never make modifications to the output.
Do not include aliases that are too general for the particular documentation;
for example, do not include `sql` or `database` for `postgresql` and do not include `machine learning` or `python` for `pytorch`.
Do not include any version info in aliases.   Please always include the project name from the ID along with other aliases.
The description should be one concise sentence.  Please do not include any version info in the description.

For example, `node~18_lts` should result in answer:

NodeJS 18 LTS
Documentation for NodeJS v18 (LTS)
["node", "nodejs", "node.js"]


Another example, `gcp_python_client_libraries` should result in answer:

GCP Python Client Libraries
API Reference for Google Cloud Python Client Libraries
["gcp python", "google cloud python"]


And another one, `kubernetes~1.28` should result in answer:

Kubernetes 1.28
Documentation for Kubernetes 1.28
["kubernetes", "k8s", "kubectl", "gke", "eks"]
"""


def get_human_readable_name(
    docset: str,
    backoff_factor: float = 2.0,
    backoff_min_seconds: float = 10.0,
    max_retries: int = 3,
    client: ChatClient | None = None,
) -> tuple[str, str, list[str]]:
    """Get the human readable name and title for a docset.

    Args:
        docset (str): The name of the docset.
        backoff_factor (float): The backoff factor for retrying.
        backoff_min_seconds (float): The minimum backoff time in seconds.
        max_retries (int): The maximum number of retries.
        client (ChatClient): The client to use for generating the response.

    Returns:
        name (str): The human readable name of the docset.
        description (str): The description of the docset.
        keywords (list[str]): A list of keywords for the docset.
    """
    if not client:
        # Get the Anthropic API key from Kubernetes secrets
        try:
            # Using the standard secret for Anthropic API access
            api_key = get_k8s_secret(
                name="seal-research-anthropic-key",
                field="secret",
                namespace="eng-secrets",
            )
        except Exception as e:
            # Fallback to low QoS key if available
            try:
                api_key = get_k8s_secret(
                    name="seal-research-anthropic-low-qos-key",
                    field="secret",
                    namespace="eng-secrets",
                )
            except Exception as fallback_e:
                raise ValueError(
                    f"Failed to get Anthropic API key from Kubernetes secrets: {e}. Fallback also failed: {fallback_e}"
                )

        # Initialize the Anthropic client with Sonnet 3.7 model
        client = AnthropicClient(api_key=api_key, model="claude-3-7-sonnet-20250219")
    prompt = (
        f"Please convert this docset ID into a human readable name and title:\n{docset}"
    )

    for _ in range(max_retries):
        try:
            response = client.generate(
                messages=[prompt], system_prompt=SYSTEM_PROMPT, max_tokens=256
            )
        except Exception as e:
            print(
                f"Error generating response: {e}. Retrying in {backoff_min_seconds} seconds."
            )
            time.sleep(backoff_min_seconds)
            backoff_min_seconds *= backoff_factor
        else:
            response = response.replace("\\n", "\n")
            lines = [
                line.strip() for line in response.strip().splitlines() if line.strip()
            ]
            if len(lines) == 3:
                title, description, keywords_json = lines
            else:
                print(f"Invalid response: {docset} => '''{response}'''")
                title = lines[0]
                description = " ".join(lines[1:2]) or "Documentation for " + lines[0]
                keywords_json = json.dumps(
                    list(set([title.lower(), docset.split("~")[0].lower()]))
                )
            try:
                keywords = json.loads(keywords_json)
            except Exception as e:
                print(f"Invalid keywords: {keywords_json}: {e}")
                keywords = list(set([title.lower(), docset.split("~")[0].lower()]))
            return title, description, keywords
    raise RuntimeError(
        f"Failed to generate response for {docset} after {max_retries} retries."
    )

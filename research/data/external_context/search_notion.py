"""Research hook to search Notion.
This is for eval only, and it only searches the Augment-Research-Eval workspace.
Note that currently we can only search TITLES, not content.
"""

from dataclasses import dataclass
from typing import Any
import requests
from datetime import datetime
import logging

from research.core.augment_secrets import get_k8s_secret

logger = logging.getLogger(__name__)


@dataclass
class NotionBlock:
    """Represents a block of content in Notion."""

    block_type: str
    text_content: str
    has_children: bool  # We keep this for information only, but won't fetch children

    def to_markdown(self) -> str:
        """Convert the block to markdown format."""
        if not self.text_content:
            return ""

        match self.block_type:
            case "heading_1":
                return f"# {self.text_content}\n"
            case "heading_2":
                return f"## {self.text_content}\n"
            case "heading_3":
                return f"### {self.text_content}\n"
            case "code":
                return f"```\n{self.text_content}\n```\n"
            case "quote":
                return f"> {self.text_content}\n"
            case "bulleted_list_item":
                return f"* {self.text_content}\n"
            case "numbered_list_item":
                return f"1. {self.text_content}\n"
            case _:
                return f"{self.text_content}\n"


@dataclass
class NotionSearchResult:
    """Represents a single search result from Notion with matched content blocks."""

    page_id: str
    title: str
    url: str
    last_edited_time: datetime
    blocks: list[NotionBlock]

    def to_markdown(self) -> str:
        """Convert the search result to markdown format, including title and matched blocks."""
        content = [f"# {self.title}\n"]
        content.extend(block.to_markdown() for block in self.blocks)
        return "".join(content)


class NotionSearchClient:
    """Client for searching Notion pages and retrieving their content."""

    def __init__(self):
        self.token = get_k8s_secret(
            name="research-notion-search-token", field="token", namespace="gcp-us1"
        )
        self.api_base = "https://api.notion.com/v1"
        self.headers = {
            "Authorization": f"Bearer {self.token}",
            "Notion-Version": "2022-06-28",
            "Content-Type": "application/json",
        }

    def _extract_text_from_rich_text(
        self, rich_text: list[dict[str, Any]] | str
    ) -> str:
        """Extract plain text from Notion's rich text format."""
        if isinstance(rich_text, str):
            return rich_text
        return " ".join(text.get("plain_text", "") for text in rich_text)

    def _get_block_content(self, block: dict[str, Any]) -> NotionBlock:
        """Extract content from a Notion block based on its type."""
        logger.debug(f"Processing block: {block}")
        block_type = block.get("type", "")
        content = ""

        if block_type in block:
            block_data = block[block_type]
            logger.debug(f"Block data for type {block_type}: {block_data}")

            if isinstance(block_data, str):
                content = block_data
            elif "rich_text" in block_data:
                content = self._extract_text_from_rich_text(block_data["rich_text"])
            elif "text" in block_data:
                content = self._extract_text_from_rich_text(block_data["text"])
            elif "title" in block_data:
                content = self._extract_text_from_rich_text(block_data["title"])
            elif block_type == "image":
                content = f"[Image: {block_data.get('caption', '')}]"
            elif block_type == "child_page":
                content = f"[Subpage: {block_data.get('title', '')}]"

            logger.debug(f"Extracted content: {content}")

        return NotionBlock(
            block_type=block_type,
            text_content=content.strip(),
            has_children=block.get("has_children", False),
        )

    def _get_page_blocks(self, page_id: str) -> list[NotionBlock]:
        """Fetch blocks for a specific page."""
        logger.debug(f"Fetching blocks for page {page_id}")
        logger.info(f"Fetching blocks from Notion API: GET /blocks/{page_id}/children")
        response = requests.get(
            f"{self.api_base}/blocks/{page_id}/children", headers=self.headers
        )
        response.raise_for_status()
        data = response.json()
        logger.debug(f"Raw blocks response: {data}")

        blocks = []
        for block in data.get("results", []):
            block_content = self._get_block_content(block)
            logger.debug(f"Processed block: {block_content}")
            blocks.append(block_content)
        return blocks

    def search_title(
        self, query: str, max_results: int = 5
    ) -> list[NotionSearchResult]:
        """Search Notion pages by title and return result pages."""
        api_call_count = 1
        logger.info(
            f"[API Call #{api_call_count}] Searching Notion API: POST /search with query '{query}'"
        )
        response = requests.post(
            f"{self.api_base}/search",
            headers=self.headers,
            json={
                "query": query,
                "page_size": max_results
                * 2,  # Fetch more results to filter out empty ones
            },
        )
        response.raise_for_status()
        data = response.json()
        logger.debug(f"Search response: {data}")

        results = []
        for result in data.get("results", []):
            logger.debug(f"Processing result: {result['id']}")

            # Extract title
            title = ""
            if "properties" in result:
                if "title" in result["properties"]:
                    title_items = result["properties"]["title"].get("title", [])
                    if title_items:
                        title = title_items[0].get("plain_text", "")
                elif "Name" in result["properties"]:
                    name_items = result["properties"]["Name"].get("title", [])
                    if name_items:
                        title = name_items[0].get("plain_text", "")

            api_call_count += 1
            logger.info(
                f"[API Call #{api_call_count}] Fetching blocks for page {result['id']}"
            )
            blocks = self._get_page_blocks(result["id"])
            # Filter out empty blocks
            blocks = [b for b in blocks if b.text_content.strip()]

            if blocks:
                # Convert last_edited_time string to datetime
                last_edited = datetime.fromisoformat(
                    result.get("last_edited_time", "").rstrip("Z")
                )

                search_result = NotionSearchResult(
                    page_id=result["id"],
                    title=title,
                    url=result.get("url", ""),
                    last_edited_time=last_edited,
                    blocks=blocks,
                )
                results.append(search_result)
                if len(results) >= max_results:  # Stop once we have enough results
                    break
            else:
                logger.info(f"Skipping result {result['id']} as it has no content")

        return results

    def get_page_content(self, result: NotionSearchResult) -> str:
        """Convert a NotionSearchResult into a readable text format."""
        content = [f"# {result.title}\n"]

        for block in result.blocks:
            if block.text_content:
                if block.block_type == "heading_1":
                    content.append(f"\n## {block.text_content}\n")
                elif block.block_type == "heading_2":
                    content.append(f"\n### {block.text_content}\n")
                elif block.block_type == "heading_3":
                    content.append(f"\n#### {block.text_content}\n")
                elif block.block_type in ["code", "quote"]:
                    content.append(f"\n```\n{block.text_content}\n```\n")
                else:
                    content.append(f"{block.text_content}\n")

        return "".join(content)

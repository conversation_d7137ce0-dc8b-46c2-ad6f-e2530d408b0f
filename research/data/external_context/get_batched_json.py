"""Created formatted JSON file that we would copy to GCS in the desired hierarcy.

We are doing this to be consistent with the eval environment, where
documents are processed by the same chunker.
"""

import json
import logging
import uuid
from pathlib import Path
import argparse
import subprocess as sp
import yaml
from functools import partial
from dataclasses import dataclass, field
from dataclasses_json import DataClassJsonMixin

from google.cloud import storage
import pyspark.sql.functions as F
from pyspark.sql.types import Row

from base.blob_names.python.blob_names import get_blob_name
from research.data.spark import k8s_session
from research.core.constants import AUGMENT_ROOT
from research.data.external_context.infer_names import get_human_readable_name

logging.basicConfig(level=logging.INFO)

AUGMENT_DATA_BUCKET = "augment-data-dev"

BASE_PATH = "gs://gcp-us1-spark-data/shared/nl-datasets/devdocs"


@dataclass
class CreateDocsetDumpConfig(DataClassJsonMixin):
    additions: dict = field(default_factory=dict)
    removals: list = field(default_factory=list)
    categories: dict = field(default_factory=dict)
    input_path: str = f"{BASE_PATH}/chunked/"
    output_path: str = f"{BASE_PATH}/json_docs/"
    docset_collection_path: str = f"{BASE_PATH}/docset_collection_index.json"
    size_limit: float = 1e6
    max_chunks: int = 500


def get_docset_info(env="STAGING"):
    """Use bazel to render docset.jsonnet file and extract info"""
    cmd = [
        "bazel",
        "run",
        "//tools:jsonnet",
        "--",
        f"{AUGMENT_ROOT}/services/integrations/docset/server/docset.jsonnet",
        "-J",
        f"{AUGMENT_ROOT}",
        "--tla-str",
        f"env={env}",
    ]
    r = sp.run(cmd, capture_output=True, encoding="utf-8", check=True, cwd=AUGMENT_ROOT)
    if r.returncode != 0:
        raise RuntimeError(f"Failed to run {cmd}: {r.stderr}")
    return json.loads(r.stdout)


def get_docset_collection(env="STAGING"):
    """Extract docset collection file content."""
    docset_info = get_docset_info(env=env)
    collection_info = docset_info["version_collection"]
    if "checksum_sha256" not in collection_info:
        raise RuntimeError(f"Missing checksum_sha256 in {collection_info}")
    if "object_store_path" not in collection_info:
        raise RuntimeError(f"Missing object_store_path in {collection_info}")

    object_path = collection_info["object_store_path"]
    checksum = collection_info["checksum_sha256"]

    # Download the content
    storage_client = storage.Client()
    bucket = storage_client.bucket(AUGMENT_DATA_BUCKET)
    blob = bucket.blob(object_path)
    content = blob.download_as_bytes()
    downloaded_blob_checksum = get_blob_name("", content)
    if downloaded_blob_checksum != checksum:
        raise RuntimeError(f"Checksum mismatch for {object_path}")
    # parse json
    return json.loads(content.decode())


def write_jsonl(
    content: str, bucket: storage.Bucket, object_path: str, uri_base: str, files: list
):
    """Write a JSONL file to the specified path.

    Args:
        content (str): The content to be written to the file.
        bucket (storage.Bucket): The bucket where the file will be saved.
        object_path (str): The path where the file will be saved.
        uri_base (str): The base URI for the file.
        files (list): The list of known batched blob files; a new entry will be appended to it.
    """

    # For now we hard code the directory structure.  index.json is found at root of
    # docset version folder, and all batched blob files are in `files`

    relative_path = f"files/doc-{len(files)}.jsonl"
    output_path = f"{object_path}/{relative_path}"

    # files is the current list of batched blob files, so len(files)
    # is the index of the new file to be appended.
    blob_uri = ""  # f"{uri_base}/{len(files)}"

    # Use binary to avoid hashing inconsistencies.
    content_bytes = content.encode()
    output_object = bucket.blob(output_path)
    output_object.upload_from_string(content_bytes)
    # compute sha 256 checksum of the buffer
    blob_name = get_blob_name(blob_uri, content_bytes)

    files.append(
        {
            "name": relative_path,
            "blob_name": blob_name,
            "checksum": blob_name,
            "path": blob_uri,
        }
    )


def normalize(name: str):
    return name.lower().replace(" ", "_").replace("-", "_")


def parse_bucket_path(path: str) -> tuple[str, str]:
    """Parse a GCS path into bucket name and object path."""
    if not path.startswith("gs://"):
        raise ValueError(f"{path} is not a GCS path")
    path = path[len("gs://") :]
    if "/" not in path:
        raise ValueError(f"{path} does not contain an object path after bucket name")
    # extract bucket name and object path
    bucket_name, object_path = path.split("/", 1)
    return bucket_name, object_path


def save_docsets(
    docset_id: str, chunks: list[dict | Row], config: CreateDocsetDumpConfig
):
    """Save a list of chunks to a docset.

    Args:
        docset_id (str): The name of the docset.
        chunks (list[dict | Row]): A list of chunks to be saved.
        categories (dict[str, str]): A mapping of docset or project names to categories.

    Returns:
        tuple: A tuple containing the docset name, version, and the checksum of the index file.
    """

    docset = docset_id.split("/")[-1]
    docset_version_id = f"{docset}-{uuid.uuid4()}"
    output_uri = f"{config.output_path.rstrip('/')}/{docset_version_id}"

    bucket_name, object_path = parse_bucket_path(output_uri)
    object_path = object_path.rstrip("/")
    storage_client = storage.Client()
    bucket = storage_client.bucket(bucket_name)

    # This is the synthetic path that we use to identify a batched blob file.
    # Will be used as the path field of the blob to ensure the checksum changes
    # when the docset version changes.
    uri_base = f"{docset_id}/{docset_version_id}"

    # collect chunks into size within SIZE_LIMIT
    buffer = ""
    chunk_count = 0
    files = []
    for chunk in chunks:
        # Convert chunk to a dictionary if it's a Row object.
        if isinstance(chunk, Row):
            chunk = chunk.asDict()
        # Convert the chunk to a JSON string and add a newline.
        row = json.dumps(chunk) + "\n"
        # Check if the buffer is full or if we've reached the maximum number of chunks.
        if (
            len(buffer) + len(row) > config.size_limit
            or chunk_count >= config.max_chunks
        ) and buffer:
            # Write the current buffer to a JSONL file.
            write_jsonl(buffer, bucket, object_path, uri_base, files)
            # Reset the buffer and chunk count.
            buffer = ""
            chunk_count = 0
        # Append the current row to the buffer.
        buffer += row
        # Increment the chunk count.
        chunk_count += 1

    if buffer:
        write_jsonl(buffer, bucket, object_path, uri_base, files)
    index_path = f"{object_path}/index.json"
    index_object = bucket.blob(index_path)
    index_content = json.dumps(
        {
            "name": docset,
            "docset_id": docset_id,
            "docset_version_id": docset_version_id,
            "files": files,
        },
        indent=4,
    )
    index_content_bytes = index_content.encode()
    index_object.upload_from_string(index_content_bytes)
    index_file_blob_name = get_blob_name("", index_content_bytes)

    project = docset.split("~")[0]
    if docset in config.categories:
        category = config.categories[docset]
    elif project in config.categories:
        category = config.categories[project]
    else:
        category = "Others"
    return category, docset, docset_version_id, index_file_blob_name


def get_parser():
    parser = argparse.ArgumentParser(add_help=False)
    parser.add_argument(
        "--config",
        "-c",
        type=str,
        default=None,
        help="The path to the config file.  YAML format containing configurations.  To see all configs and their defaults, check --help.",
    )
    parser.add_argument(
        "--skip-generation",
        action="store_true",
    )
    parser.add_argument(
        "--help",
        "-h",
        action="store_true",
        help="Show help message about config file format and exit.",
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=10,
        help="Number of spark workers.",
    )
    return parser


def read_config(filename: str):
    """Read job config file."""
    config_file = Path(filename)
    with config_file.open() as f:
        config = yaml.safe_load(f)
    return CreateDocsetDumpConfig(**config)


def print_help(parser: argparse.ArgumentParser):
    default = CreateDocsetDumpConfig()
    original_help = parser.format_help()
    print(original_help)
    print("Config file fields and default values:")
    # convert default dataclass to yaml then print it
    print(yaml.dump(default.to_dict()))


def main(parser: argparse.ArgumentParser):
    args = parser.parse_args()
    if args.help:
        print_help(parser)
        return

    config = read_config(args.config)

    original_collection = get_docset_collection()

    print("Generating JSONL files")
    spark = k8s_session(
        max_workers=args.workers,
        conf={
            "spark.executor.memory": "180G",
            "spark.executor.pyspark.memory": "200G",
        },
    )

    normalized_additions = {
        normalize(k): normalize(v) for k, v in config.additions.items()
    }

    @F.udf("string")
    def get_docset_id(docset_name):
        return normalized_additions[docset_name]

    # Convert the vetted subset into JSONL and following a specific directory structure
    # Also compute checksums for each document and index.json files
    df = (
        spark.read.parquet(config.input_path)
        .select(
            "text",
            "path",
            "char_offset",
            "length",
            "line_offset",
            "length_in_lines",
            "meta.*",
        )
        .filter(F.col("docset_name").isin(list(normalized_additions.keys())))
        .withColumn("docset_id", get_docset_id("docset_name"))
    )

    # save docsets
    save_docsets_udf = F.udf(
        partial(save_docsets, config=config),
        "category string, docset_name string, docset_version_id string, checksum string",
    )

    version_index = (
        df.groupBy("docset_name", "docset_id")
        .agg(
            F.collect_list(
                F.struct(
                    "*",
                )
            ).alias("chunks")
        )
        .select(
            save_docsets_udf("docset_id", "chunks").alias("info"),
        )
        .collect()
    )
    print(f"Saved {len(version_index)} docsets.")
    spark.stop()

    # Get human readable names
    items = []
    for item in version_index:
        info = item["info"].asDict()
        display_name, title, keywords = get_human_readable_name(info["docset_name"])
        info["docset_id"] = "docset://" + info["docset_name"]
        info["name"] = display_name.strip()
        info["title"] = title.strip()
        info["keywords"] = keywords
        print(
            f"{info['docset_id']} {info['name']} {info['title']} keywords: {info['keywords']}"
        )
        items.append(info)
    new_collection = [
        item
        for item in original_collection + items
        if item["docset_id"].split("/")[-1] not in config.removals
    ]

    # Create version index file.
    index_bucket_name, index_object_path = parse_bucket_path(
        config.docset_collection_path
    )
    index_bucket = storage.Client().bucket(index_bucket_name)
    index_object = index_bucket.blob(index_object_path)
    index_object.upload_from_string(json.dumps(new_collection, indent=4))


if __name__ == "__main__":
    parser = get_parser()
    main(parser)

import concurrent.futures
import json
import argparse

from anthropic.types import TextBlock

# get a vertex aianthropic chat client
from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from research.core.constants import GCP_PROJECT_ID, GCP_VERTEX_REGION
from research.data.external_context.search_glean import (
    GleanSearchClient,
    GleanSearchResult,
)


def parse_args():
    parser = argparse.ArgumentParser(description="Glean Retrieval Evaluation")
    parser.add_argument(
        "--input", type=str, default="glean_examples.json", help="Input JSON file"
    )
    parser.add_argument(
        "--output", type=str, default="output.json", help="Output JSON file"
    )
    parser.add_argument(
        "--url", type=str, default="https://augment-be.glean.com", help="Augment URL"
    )
    parser.add_argument(
        "--max_results", type=int, default=12, help="Maximum number of search results"
    )
    parser.add_argument(
        "--max_snippet_size", type=int, default=4000, help="Maximum snippet size"
    )
    return parser.parse_args()


class GleanTestCase:
    """Handles the data and processing of a single example."""

    REGION = GCP_VERTEX_REGION
    PROJECT_ID = GCP_PROJECT_ID
    TEMPERATURE = 0
    MAX_OUTPUT_TOKENS = 1024 * 8
    MODEL = "claude-3-5-sonnet-v2@20241022"
    CLIENT = AnthropicVertexAiClient(
        project_id=PROJECT_ID,
        region=REGION,
        model_name=MODEL,
        temperature=TEMPERATURE,
        max_output_tokens=MAX_OUTPUT_TOKENS,
    ).client

    def __init__(
        self, question, reference_answer, reference, url, max_results, max_snippet_size
    ):
        self.question = question
        self.reference_answer = reference_answer
        self.reference = reference
        self.max_results = max_results
        self.max_snippet_size = max_snippet_size

        self.glean_client = GleanSearchClient(url)

    def _chat(
        self, prompt: str, system_prompt: str = "You are a helpful assistant."
    ) -> str:
        response = self.CLIENT.messages.create(
            model=self.MODEL,
            max_tokens=self.MAX_OUTPUT_TOKENS,
            messages=[
                {
                    "role": "user",
                    "content": prompt,
                }
            ],
            system=system_prompt,
            temperature=self.TEMPERATURE,
        )
        assert len(response.content) == 1
        content = response.content[0]
        assert isinstance(content, TextBlock)
        return content.text

    def get_search_queries(self) -> list[str]:
        """Use the chat model to determine the search query."""
        prompt = (
            """Please determine the search queries for the following question.
The search is based on keyword / phrase match, not semantic search or question answering.
You will be searching a knowledge base of company-wise documentation and message platforms.
If multiple searches are needed, you can return multiple queries separated by a newline.
There should be 1 to 3 queries, and each query should be at different levels of specificity or area.
Please return the search queries only, without any other text or explanations.  Question: """
            + self.question
        )

        response = self._chat(prompt, system_prompt="You are a helpful assistant.")
        return response.split("\n")

    def get_search_results(self, search_queries: list[str]) -> list[GleanSearchResult]:
        """Use the glean client to get search results."""
        with concurrent.futures.ThreadPoolExecutor() as executor:
            futures = []
            for i, query in enumerate(search_queries):
                # Distribute the max_results evenly among the queries
                max_query_results = self.max_results // len(search_queries)
                if i < self.max_results % len(search_queries):
                    max_query_results += 1
                futures.append(
                    executor.submit(
                        self.glean_client.search,
                        query,
                        max_results=max_query_results,
                        max_snippet_size=self.max_snippet_size,
                        content=True,
                    )
                )

            results = sum(
                (
                    future.result()
                    for future in concurrent.futures.as_completed(futures)
                ),
                start=[],
            )

        return results

    def get_answer(self, search_results):
        """Use the chat model to get the answer from the search results."""
        context = "\n".join(
            [result.to_markdown(use_content=True) for result in search_results]
        )
        prompt = self.question
        system_prompt = (
            "Please answer the user's question, considering the documents below. Note that not all documents are relevant. DOCUMENTS: "
            + context
        )
        return self._chat(prompt, system_prompt)

    def run(self):
        search_query = self.get_search_queries()
        search_results = self.get_search_results(search_query)
        answer = self.get_answer(search_results)
        return search_query, answer


def main():
    args = parse_args()

    with open(args.input) as examples_file:
        reference = json.load(examples_file)

    results = []
    for example in reference:
        gle = GleanTestCase(
            example["question"],
            example["answer"],
            example["reference"],
            args.url,
            args.max_results,
            args.max_snippet_size,
        )
        print("Question: ", example["question"])
        print("----------------")
        query, answer = gle.run()
        print("Query: ", query)
        print("----------------")
        print("Answer: ", answer)
        print("----------------")
        print("Reference URL: ", example["reference"])
        print("Reference Answer: ", example["answer"])
        print("================")

        results.append(
            {
                "question": example["question"],
                "query": query,
                "answer": answer,
                "reference_url": example["reference"],
                "reference_answer": example["answer"],
            }
        )

    with open(args.output, "w") as f:
        json.dump(results, f, indent=2)


if __name__ == "__main__":
    main()

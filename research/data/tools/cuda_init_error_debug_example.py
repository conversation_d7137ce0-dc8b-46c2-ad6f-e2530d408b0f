"""<PERSON><PERSON>t to debug the cuda init error in spark.

When developping GPU spark jobs, we often encounter the following cuda init error.
```
RuntimeError: CUDA error: initialization error
```

One solution is to move some import statement to the function body (lazy import).
However, the challenge is that we don't know which import statement is causing the
error.

This script is to help you quickly test a suspicious import statement and find the
culprit.

"""

from multiprocessing import Process

# Step 1: Adds a suspicious import statement here.
from base.fastforward.fwd_utils import get_checkpoint_sha

# Step 2: Run the script to see if it triggers the error.


def worker():
    import torch
    # Step 3: Move the import statement added in Step 1 here. And rerun the script to
    # see if it solves the error.

    torch.cuda.init()


def main():
    p = Process(target=worker)
    p.start()
    p.join()
    print("Done")


if __name__ == "__main__":
    main()

"""Methods for scoring retrieval candidates."""

import json
import logging
import pickle
import time
from typing import Any, Callable, Iterator, Mapping, Sequence

import torch

from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from base.tokenizers.tokenizer import RagSpecialTokens
from research.core.model_input import ModelInput
from research.core.tokenizers import get_tokenizer
from research.core.utils_for_log import time_string
from research.data.rag import constants
from research.data.rag.retrieval_utils import (
    deserialize_retrieved_chunks,
    serialize_retrieved_chunks,
)
from research.data.spark.pipelines.utils import map_parquet
from research.eval.harness.metrics import compute_mean_log_likelihood
from research.fim import fim_prompt, fim_sampling
from research.retrieval import utils as rutils
from research.retrieval.retrieval_database import RetrievalDatabase
from research.retrieval.types import Document

# pylint: disable=protected-access

logger = logging.getLogger(__name__)


class GenerateRetrievedChunksFromFim:
    """A class to generate retrieved chunks from FIM problems.
    Originally forked from rag_common.GenerateRetrievedChunksFromFim."""

    def __init__(
        self,
        retrieval_database_factories: Mapping[str, Callable[[], RetrievalDatabase]],
        retrieval_languages: Sequence[str],
        num_retrieved_chunks: int,
        max_failures: int = 100,
        use_ground_truth: bool = False,
    ):
        self.retrieval_database_factories = retrieval_database_factories
        self._ret_dbs = dict[str, RetrievalDatabase]()
        self.retrieval_languages = retrieval_languages
        self.num_retrieved_chunks = num_retrieved_chunks
        self.max_failures = max_failures
        self.use_ground_truth = use_ground_truth

    def _lazy_init_ret_dbs(self):
        """Loads the retrieval databases."""
        for key, factory in self.retrieval_database_factories.items():
            if key not in self._ret_dbs:
                self._ret_dbs[key] = factory()
                self._ret_dbs[key].load()

    def _clear_index(self):
        """Clears the retrieval databases."""
        for db in self._ret_dbs.values():
            db.remove_all_docs()

    def process_single_fim_problem(
        self,
        sample: fim_sampling.FimProblem,
        documents: Sequence[Document],
        original_doc_ids: set[str],
        ret_dbs: dict[str, RetrievalDatabase],
        num_retrieved_chunks: int,
    ) -> dict:
        """Returns a retrieval-augmented sample."""
        path = str(sample.file_path)
        prefix = sample.prefix.code
        suffix = sample.suffix.code
        middle_char_start, middle_char_end = sample.middle_range.to_tuple()
        suffix_offset = len(sample.suffix.range) - len(suffix)

        # We keep the serialized middle spans.
        output = dict(
            prefix=prefix,
            middle_spans=pickle.dumps(sample.middle_spans),
            suffix=suffix,
            suffix_offset=suffix_offset,
            middle_char_start=middle_char_start,
            middle_char_end=middle_char_end,
            file_path=path,
        )

        # Index the current document's before state.
        new_doc = Document.new(text=prefix + suffix, path=path)
        for db in ret_dbs.values():
            db.add_doc(new_doc)
        logger.info(f"Indexed {path} with {len(prefix + suffix)} chars.")

        # Retrieve chunks
        doc_ids = {doc.id for doc in documents if doc.path != path}
        doc_ids.add(new_doc.id)

        input_prefix = prefix
        input_suffix = suffix
        if self.use_ground_truth:
            middle_code = sample.show_middle()
            # divide middle in half and add halves to prefix/suffix
            input_prefix = prefix + middle_code[: len(middle_code) // 2]
            input_suffix = middle_code[len(middle_code) // 2 :] + suffix

        model_input = ModelInput(
            prefix=input_prefix, suffix=input_suffix, path=path, doc_ids=doc_ids
        )
        for key, ret_db in ret_dbs.items():
            # TODO(Xuanyi): the signature dense retriever will stuck at some data.
            # Around 15% of FIM problems are dropped because of this problem.
            # As if a repo has a FIM problem that being stucked by the dense retriever' query func,
            # the entire parquet file will be dropped.
            # I have moved some problemtic data at /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/fim-normal-stuck,
            # and we need to spend some time to figure out why this is happening.
            output[f"{key}_chunks"] = serialize_retrieved_chunks(
                ret_db.query(
                    model_input=model_input,
                    top_k=num_retrieved_chunks,
                    doc_ids=doc_ids,
                )[0]
            )
            logger.info(f"Retrieved {num_retrieved_chunks} chunks from {key}.")

        if new_doc.id not in original_doc_ids:
            for db in ret_dbs.values():
                db.remove_doc(new_doc.id)
            logger.info(f"Removed {path} with {len(prefix + suffix)} chars.")
        return output

    def __call__(
        self,
        file_list: Sequence[Mapping[str, Any]],
        fim_problems: Sequence[fim_sampling.FimProblem],
    ) -> Iterator[dict]:
        """Convert each FIM problem in the repo to be retrieval-augmented data samples.

        Args:
            repo: The repository, which is a list of files, to process.
            fim_problems: The FIM problems to augment.
            config: The configuration object.
            tokenizer: The tokenizer to use.
            retrieval_database: The retrieval database to use.

        Returns:
            A generator of processed rows.
        """
        self._lazy_init_ret_dbs()
        self._clear_index()
        # Step 1: Index all files.
        start_time = time.time()
        documents: list[Document] = []
        for file in file_list:
            # Only add files of desired languages to be retrieved
            if (
                self.retrieval_languages
                and file[constants.FILE_LANG_COLUMN] not in self.retrieval_languages
            ):
                continue

            document = Document.new(
                text=file[constants.CONTENT_COLUMN],
                path=file[constants.PATH_COLUMN],
            )
            documents.append(document)

        original_doc_ids = {doc.id for doc in documents}
        for db in self._ret_dbs.values():
            db.add_docs(documents)
        logger.info(f"Indexed {len(documents)} files in {time.time() - start_time:.2f} sec.")  # fmt: off
        logger.info(f"GPU Memory Usage: {torch.cuda.memory_allocated() / 1e9:.2f} GB.")
        num_failures = 0
        # Step 2: Retrieve chunks for each FIM problem.
        for index, sample in enumerate(fim_problems):
            try:
                output = self.process_single_fim_problem(
                    sample,
                    documents,
                    original_doc_ids,
                    self._ret_dbs,
                    self.num_retrieved_chunks,
                )
                logger.info(
                    f"Finished processing {index}/{len(fim_problems)} FIM problems"
                    f" (GPU Memory Usage: {torch.cuda.memory_allocated() / 1e9:.2f} GB)."
                )
                yield output
            except Exception as e:
                logger.error(
                    f"Failed to process {index}/{len(fim_problems)} FIM problem: {e}"
                )
                num_failures += 1
            if num_failures >= self.max_failures:
                self._clear_index()
                raise RuntimeError(
                    f"Failed to process {num_failures} FIM problems ({self.max_failures} max)."
                )

        logger.info(f"GPU Memory Usage: {torch.cuda.memory_allocated() / 1e9:.2f} GB.")
        # Keeps database and reset the index.
        self._clear_index()
        logger.info(f"Finish processing the repo in {time.time() - start_time:.2f} secs.")  # fmt: off


def create_filter_insufficient_retrieval_chunks_map(
    min_num_chunks: int,
) -> map_parquet.FlatMapFn:
    """A row-wise function to filter out samples with insufficient retrieval chunks."""

    def filter_insufficient_retrieval_chunks_map(
        retrieved_chunks: str,
    ) -> Iterator[dict]:
        deserialized_retrieved_chunks = deserialize_retrieved_chunks(retrieved_chunks)

        if len(deserialized_retrieved_chunks) >= min_num_chunks:
            yield {"retrieved_chunks": retrieved_chunks}
        else:
            return

    return filter_insufficient_retrieval_chunks_map


def filter_overlap_chunks_map(
    file_path: str,
    middle_char_start: int,
    middle_char_end: int,
    retrieved_chunks: str,
):
    deserialized_retrieved_chunks = deserialize_retrieved_chunks(retrieved_chunks)
    filtered_chunks = rutils.filter_overlap_chunks(
        query_file_name=file_path,
        query_span=rutils.Span(middle_char_start, middle_char_end),
        chunks=deserialized_retrieved_chunks,
    )

    return {"retrieved_chunks": serialize_retrieved_chunks(filtered_chunks)}


class ComputePPL:
    """A stateful row-wise function to compute perplexity."""

    def __init__(
        self,
        model_config: dict,
        tokenizer_name: str,
        score_batch_size: int = 2,
        max_target_tokens: int = 256,
        score_signature: bool = False,
    ):
        self._model_config = model_config
        self._score_batch_size = score_batch_size
        self._model = None
        self._tokenizer = get_tokenizer(tokenizer_name)
        self._max_target_tokens = max_target_tokens
        self._score_signature = score_signature

    @map_parquet.passthrough_feature(bound=True)
    @map_parquet.allow_unused_args(bound=True)
    def __call__(
        self,
        prefix: str,
        suffix: str,
        middle_spans: bytes,
        file_path: str,
        retrieved_chunks: str,
    ) -> dict:
        # Imports are here to avoid CUDA re-initialization RuntimeError.
        from research.eval.harness.factories import create_model

        if self._model is None:
            self._model = create_model(self._model_config)
            self._model.load()

        model_input = ModelInput(
            prefix=prefix,
            suffix=suffix,
            retrieved_chunks=deserialize_retrieved_chunks(retrieved_chunks),
            path=file_path,
        )
        print(f"{time_string()} Reranking...")
        special_tokens = self._tokenizer.special_tokens
        assert isinstance(special_tokens, RagSpecialTokens)
        deserialized_middle_spans: list[
            fim_sampling.SkipOrOutput[fim_sampling.SrcSpan]
        ] = pickle.loads(middle_spans)
        middle_tokens = fim_prompt._format_middle(
            middle_spans=deserialized_middle_spans,
            tokenize_span=lambda span: self._tokenizer.tokenize_safe(span.code),
            skip_id=special_tokens.skip,
            pause_id=special_tokens.pause,
            fim_stop_id=special_tokens.eos,
        )
        middle_tokens = middle_tokens[: self._max_target_tokens]
        scores, prompt_token_list = self._score_all_chunks(
            model_input=model_input,
            middle_tokens=middle_tokens,
            batchsize=self._score_batch_size,
        )
        assert len(scores) == len(
            deserialize_retrieved_chunks(retrieved_chunks)
        ), f"{len(scores)} {len(deserialize_retrieved_chunks(retrieved_chunks))}"
        print(f"{time_string()} Done reranking.")
        print(f"Peak GPU Memory: {torch.cuda.max_memory_allocated()}.")

        ppl_scores = {"scores": scores}
        return {
            "ppl_scores": json.dumps(ppl_scores),
            "ppl_prompt_len": json.dumps([len(p[0]) for p in prompt_token_list]),
        }

    def _score_all_chunks(
        self,
        model_input: ModelInput,
        middle_tokens: list[int],
        batchsize: int,
    ):
        """Returns a score for each retrieval candidate based on resulting perplexity."""
        prompt_token_list = []
        scores = []
        chunks = model_input.retrieved_chunks

        # List of chunks -> a model input
        def model_input_builder(_chunks):
            return ModelInput(
                prefix=model_input.prefix,
                suffix=model_input.suffix,
                path=model_input.path,
                retrieved_chunks=_chunks if not self._score_signature else [],
                cursor_position=-999999,
                extra={
                    "target_tokens": middle_tokens,
                    "signature_chunks": _chunks if self._score_signature else [],
                },
            )

        assert self._model is not None
        for i in range(0, len(chunks), batchsize):
            batch_model_inputs = []
            for chunk in chunks[i : (i + batchsize)]:
                iter_input = model_input_builder([chunk])
                batch_model_inputs.append(iter_input)
                prompt_token_list.append(
                    self._model.prompt_formatter.prepare_prompt(iter_input)
                )

            batch_outputs = self._model.forward_pass(batch_model_inputs)
            for output in batch_outputs:
                mean_ll = compute_mean_log_likelihood(
                    output.logits, output.label_tokens, output.target_mask
                )
                scores.append(mean_ll)

        assert len(scores) == len(prompt_token_list)
        return scores, prompt_token_list

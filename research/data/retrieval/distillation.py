"""Core functions for retrieval training data processing.

This module implements data transformation functions (`FlatMapFn`) shared by retrieval
pipelines. Stateless callables are implemented as high-order functions, while stateful
callables are implemented as classes.
"""

import json
from typing import Sequence

import numpy as np

import research.retrieval.chunk_formatters  # noqa: F401
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.prompt_format_retrieve import (
    CompletionRetrieverPromptInput,
    DocumentRetrieverPromptInput,
    get_retrieval_prompt_formatter_by_name,
)
from base.tokenizers.tokenizer import RetrievalSpecialTokens, Tokenizer
from research.data.rag.retrieval_utils import deserialize_retrieved_chunks
from research.data.spark.pipelines.utils import map_parquet


def create_prompt_format_fn(
    query_prompt_formatter_name: str,
    max_query_len: int,
    key_prompt_formatter_name: str,
    max_key_len: int,
    tokenizer: Tokenizer,
    num_retrieved_chunks: int,
) -> map_parquet.FlatMapFn:
    """Prompt formatting function for retriever training data."""

    def _prompt_from_retriever_sample(
        prefix: str,
        suffix: str,
        file_path: str,
        retrieved_chunks: str,
        ppl_scores: str,
    ) -> dict:
        """Flat map function to format a retriever sample into a prompt.
        Args:
            prefix: The prefix of the query.
            suffix: The suffix of the query.
            file_path: The file path of the query.
            retrieved_chunks: Serialized retrieved chunks.
            ppl_scores: Serialized dictionary of different perplexity related scores and metadata.
        Returns:
            A dict containing the formatted prompt.
        """

        special_tokens = tokenizer.special_tokens
        assert isinstance(special_tokens, RetrievalSpecialTokens)
        end_of_key_token = special_tokens.end_of_key
        query_apportionment_config = TokenApportionmentConfig(
            max_content_len=max_query_len,
            input_fraction=0.0,  # not used
            prefix_fraction=0.0,  # not used
            max_path_tokens=0,  # not used
        )
        query_prompt_formatter = get_retrieval_prompt_formatter_by_name(
            name=query_prompt_formatter_name,
            tokenizer=tokenizer,
            apportionment_config=query_apportionment_config,
        )
        key_apportionment_config = TokenApportionmentConfig(
            max_content_len=max_key_len,
            input_fraction=0.0,  # not used
            prefix_fraction=0.0,  # not used
            max_path_tokens=0,  # not used
        )
        key_prompt_formatter = get_retrieval_prompt_formatter_by_name(
            name=key_prompt_formatter_name,
            tokenizer=tokenizer,
            apportionment_config=key_apportionment_config,
        )

        query_prompt = query_prompt_formatter.format_prompt(
            CompletionRetrieverPromptInput(
                prefix=prefix,
                suffix=suffix,
                path=file_path,
            )
        ).tokens()

        retrieved_chunk_list = deserialize_retrieved_chunks(retrieved_chunks)
        ppl_score_dict = json.loads(ppl_scores)
        scores = ppl_score_dict["scores"]
        assert len(retrieved_chunk_list) == len(scores)

        chunk_tuples = [
            (chunk, scores[i]) for i, chunk in enumerate(retrieved_chunk_list)
        ][:num_retrieved_chunks]

        key_prompts = []
        for chunk, ppl_score in chunk_tuples:
            # Format the prompt
            key_prompt = key_prompt_formatter.format_prompt(
                DocumentRetrieverPromptInput(
                    text=chunk.text,
                    path=chunk.parent_doc.path,
                ),
            ).tokens()

            # Encode the perplexity score into tokens.
            ppl_info_tokens = tokenizer.tokenize_safe(f"{ppl_score}")

            # Format the footer of the prompt
            assert key_prompt[-1] == end_of_key_token
            ppl_info_tokens += [end_of_key_token]

            key_prompt.extend(ppl_info_tokens)
            key_prompts.append(key_prompt)

        # group the documents into prompts
        all_tokens = query_prompt
        all_tokens.extend(sum(key_prompts, []))
        return {"prompt_tokens": all_tokens}

    return _prompt_from_retriever_sample


def create_pack_tokens_fn(
    tokenizer: Tokenizer,
):
    """Pack tokens function for retriever training data."""

    if tokenizer.vocab_size < 65500:
        dtype = np.uint16
    else:
        dtype = np.int32

    def _pack_tokens(prompt_tokens: Sequence[int]) -> dict:
        byte_tokens = np.array(prompt_tokens).astype(dtype).newbyteorder("<").tobytes()
        return {"prompt_tokens": byte_tokens}

    return _pack_tokens

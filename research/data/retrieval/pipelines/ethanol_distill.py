"""Pipeline for generating retrieval training prompts from score samples.

The pipeline is split into three stages:
1. Shuffle the score samples.
2. Generate prompts.
3. Export the prompts to indexed dataset.

Sample command for running the pipeline:
python research/data/retrieval/pipelines/ethanol_distill.py -r test -s shuffle prompt export
"""

import argparse
import json
import os
import pathlib
from dataclasses import asdict, dataclass
from typing import Optional

import pyspark.sql.functions as F

from base.tokenizers import create_tokenizer_by_name
from research.core.utils_for_log import time_string
from research.data.retrieval.distillation import (
    create_pack_tokens_fn,
    create_prompt_format_fn,
)
from research.data.spark import k8s_session
from research.data.spark.pipelines.stages.common import export_indexed_dataset_helper
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.pipelines.utils.map_parquet import allow_unused_args


def get_spark_cpu(name: str, test: bool = False):
    spark = k8s_session(
        name=name,
        max_workers=8 if test else 128,
        conf={
            "spark.executor.pyspark.memory": "1050G",
            "spark.executor.memory": "32G",
            "spark.sql.parquet.columnarReaderBatchSize": "32",
            "spark.task.cpus": "2",
        },
    )
    return spark


@dataclass
class DistillConfig:
    """Config for ethanol distillation pipeline."""

    # General
    run_name: str
    output_root: str

    # Prompt
    num_retrieved_chunks: int
    query_prompt_formatter_name: str
    max_query_len: int
    key_prompt_formatter_name: str
    max_key_len: int
    tokenizer_name: str

    # Export
    num_validation_samples: int

    # Optional
    input_path: Optional[str] = None


def shuffle_stage(config: DistillConfig, test=False, first_stage=False):
    """Shuffle the score samples.

    Args:
        config: The config for the pipeline.
        test: Whether to use a smaller cluster.
        first_stage: Whether this is the first stage called in this run of the pipeline.
          Used to determine whether to read the input from different folder.
    """

    if first_stage and config.input_path is not None:
        input_path = config.input_path
    else:
        input_path = f"{config.output_root}/{config.run_name}/score/"
    output_path = f"{config.output_root}/{config.run_name}/shuffled/"

    print(f"{time_string()} Shuffling...")
    print(f"Input: {input_path}")
    print(f"Output: {output_path}")

    spark = get_spark_cpu("shuffle-stage", test=test)
    parquet_files = map_parquet.list_files(
        spark, input_path, suffix="parquet", include_path=True
    )
    read_df = spark.read.parquet(*parquet_files)
    if test:
        desired_partitions = 10
        max_samples = 1000
        read_df = read_df.limit(max_samples)
    else:
        desired_partitions = 500
    read_df.orderBy(F.rand()).repartition(desired_partitions).write.parquet(output_path)
    spark.stop()


def prompt_stage(config: DistillConfig, test=False, first_stage=False):
    """Generate prompts.

    Args:
        config: The config for the pipeline.
        test: Whether to use a smaller cluster.
        first_stage: Whether this is the first stage called in this run of the pipeline.
          Used to determine whether to read the input from different folder.
    """

    if first_stage and config.input_path is not None:
        input_path = config.input_path
    else:
        input_path = f"{config.output_root}/{config.run_name}/shuffled/"
    output_path = f"{config.output_root}/{config.run_name}/prompt/"

    print(f"{time_string()} Generating prompts...")
    print(f"Input: {input_path}")
    print(f"Output: {output_path}")

    spark = get_spark_cpu("prompt-stage", test=test)

    tokenizer = create_tokenizer_by_name(config.tokenizer_name)
    prompt_format_fn = create_prompt_format_fn(
        query_prompt_formatter_name=config.query_prompt_formatter_name,
        max_query_len=config.max_query_len,
        key_prompt_formatter_name=config.key_prompt_formatter_name,
        max_key_len=config.max_key_len,
        tokenizer=tokenizer,
        num_retrieved_chunks=config.num_retrieved_chunks,
    )
    pack_tokens_fn = create_pack_tokens_fn(
        tokenizer=tokenizer,
    )

    result = map_parquet.apply_pandas(
        spark,
        map_parquet.chain_processors(
            [
                allow_unused_args()(prompt_format_fn),
                pack_tokens_fn,
            ]
        ),
        input_path=input_path,
        output_path=output_path,
        timeout=7200,
        batch_size=16,
        ignore_error=False,
        task_info_location=os.path.join(output_path, "task_info"),
    )
    spark.stop()

    print(result["status_count"])
    print(result["task_info"]["stderr"])

    config_dict = asdict(config)
    config_dict["result"] = dict(status_count=result["status_count"])
    config_dict["timestamp"] = time_string()
    config_file_path = os.path.join(output_path, "config.json")
    with open(config_file_path, "w") as f:
        f.write(json.dumps(config_dict, indent=2))
    print(f"{time_string()} Config saved to {config_file_path}.")


def export_stage(config: DistillConfig, test: bool = False, first_stage: bool = False):
    """Export the prompts to indexed dataset.

    Args:
        config: The config for the pipeline.
        test: Whether to use a smaller cluster.
        first_stage: Whether this is the first stage called in this run of the pipeline.
          Used to determine whether to read the input from different folder.
    """

    PROMPT_COLUMN = "prompt_tokens"
    spark_cpu = get_spark_cpu("export-stage", test=test)

    tokenizer = create_tokenizer_by_name(config.tokenizer_name)
    if first_stage and config.input_path is not None:
        input_path = config.input_path
    else:
        input_path = f"{config.output_root}/{config.run_name}/prompt/"
    output_path = f"{config.output_root}/{config.run_name}/indexed_dataset/"

    df = spark_cpu.read.parquet(
        *map_parquet.list_files(
            spark_cpu, input_path, suffix="parquet", include_path=True
        )
    ).select(PROMPT_COLUMN)
    print(f"Input {input_path} has {df.count()} rows.")
    spark_cpu.sparkContext.setJobDescription("Creating indexed dataset")
    export_indexed_dataset_helper(
        df,
        vocab_size=tokenizer.vocab_size,
        samples_column=PROMPT_COLUMN,
        num_validation_samples=config.num_validation_samples,
        indexed_dataset_path=pathlib.Path(output_path),
        filter_by_langs=None,
    )
    print(f"[Dataset generated in {output_path}.")
    spark_cpu.stop()
    config_dict = asdict(config)
    config_dict["timestamp"] = time_string()
    config_dict["input_path"] = input_path

    with open(f"{output_path}/config.json", "w") as f:
        json.dump(config_dict, f, indent=2)


STAGES = {
    "shuffle": shuffle_stage,
    "prompt": prompt_stage,
    "export": export_stage,
}


CONFIG = DistillConfig(
    # General
    run_name="test",
    output_root="/mnt/efs/spark-data/shared/ethanol/",
    # Prompt
    tokenizer_name="starcoder",
    query_prompt_formatter_name="ethanol6.16.1-query-embedding",
    max_query_len=1024,
    key_prompt_formatter_name="ethanol6-embedding-with-path-key",
    max_key_len=1024,
    num_retrieved_chunks=128,
    # Export
    num_validation_samples=49152,
    # Optional
    input_path=None,
)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--stages",
        "-s",
        type=str,
        help="stage to run",
        nargs="+",
        default=["shuffle", "prompt", "export"],
    )
    parser.add_argument(
        "--test",
        "-t",
        action="store_true",
        help="test",
    )
    parser.add_argument(
        "--run-name",
        "-r",
        type=str,
        help="run name",
        default="test",
    )
    parser.add_argument(
        "--input-path",
        "-i",
        type=str,
        help="input path",
    )

    args = parser.parse_args()

    if args.run_name is not None:
        CONFIG.run_name = args.run_name

    if args.input_path is not None:
        CONFIG.input_path = args.input_path

    first_stage = True
    for stage in args.stages:
        STAGES[stage](CONFIG, test=args.test, first_stage=first_stage)
        first_stage = False

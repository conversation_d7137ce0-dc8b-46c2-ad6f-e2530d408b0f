"""Pipeline for generating perplexity scored fim samples.

The pipeline is split into three stages:
1. Generate FIM samples.
2. Generate retrieval samples.
3. Generate perplexity scores.

Sample command for running the pipeline:
python research/data/retrieval/pipelines/ethanol_score.py -r test -s fim retrieval score
"""

import argparse
import json
import os
from dataclasses import asdict, dataclass
from typing import Optional

from research.core.utils_for_log import time_string
from research.data.rag import common as rag_common
from research.data.rag import constants as rag_constants
from research.data.retrieval.scoring import (
    ComputePPL,
    GenerateRetrievedChunksFromFim,
    create_filter_insufficient_retrieval_chunks_map,
    filter_overlap_chunks_map,
)
from research.data.spark import k8s_session
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.pipelines.utils.map_parquet import (
    allow_unused_args,
    passthrough_feature,
)
from research.fim import fim_sampling
from research.retrieval.retrieval_database import RetrievalDatabase


@dataclass
class ScoreConfig:
    """Config for ethanol score pipeline."""

    # General
    run_name: str
    output_root: str
    random_seed: int

    # File filter
    small_filter_char_threshold: int
    small_downsampled_probability: float
    small_downsample_char_threshold: int
    sample_languages: list[str]

    # Fim sampling
    every_n_lines: int
    max_problems_per_file: int

    # Retrieval
    retrieval_languages: list[str]
    num_retrieved_chunks: int
    min_num_retrieved_chunks: int
    dense_retriever_config: dict
    signature_retriever_config: dict

    # If set to true, the ground truth is given to the 'seed retriever' that determines
    # the set of chunks to be reranked, divided between prefix and suffix.
    use_ground_truth: bool

    # Model scoring
    score_model_config: dict
    score_batch_size: int
    score_tokenizer_name: str
    max_target_tokens: int
    score_signature: bool

    # Optional
    # use when you want to use the output of a run from a different folder as input.
    input_path: Optional[str] = None


def get_spark_cpu(name: str, test: bool = False):
    spark = k8s_session(
        name=name,
        max_workers=8 if test else 128,
        conf={
            "spark.executor.pyspark.memory": "64G",
            "spark.executor.memory": "32G",
            "spark.sql.parquet.columnarReaderBatchSize": "32",
            "spark.task.cpus": "2",
        },
    )
    return spark


def get_spark_gpu(name: str, test: bool = False):
    spark = k8s_session(
        name=name,
        max_workers=8 if test else 128,
        conf={
            "spark.executor.pyspark.memory": "1000G",
            "spark.executor.memory": "80G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
        },
        gpu_type="RTX_A6000",
    )
    return spark


def get_spark_gpu_large(name: str, test: bool = False):
    spark = k8s_session(
        name=name,
        max_workers=8 if test else 128,
        conf={
            "spark.executor.pyspark.memory": "1000G",
            "spark.executor.memory": "80G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
        },
        gpu_type="H100_NVLINK_80GB",
        ephemeral_storage_gb=32,
    )
    return spark


def fim_stage(config: ScoreConfig, test: bool = False, first_stage: bool = False):
    """Generate FIM samples.

    Args:
        config: The config for the pipeline.
        test: Whether to use a smaller cluster.
        first_stage: Whether this is the first stage called in this run of the pipeline.
          Used to determine whether to read the input from different folder.
    """

    file_filter = rag_common.FileFilter(
        small_filter_char_threshold=config.small_filter_char_threshold,
        small_downsampled_probability=config.small_downsampled_probability,
        small_downsample_char_threshold=config.small_downsample_char_threshold,
        sample_languages=config.sample_languages,
        only_keep_unit_test_file=False,
    )

    sampler = fim_sampling.CSTFimSampler()
    get_node_weight = None

    if first_stage and config.input_path is not None:
        input_path = config.input_path
    else:
        input_path = f"{config.output_root}/{config.run_name}/repos"
    print(f"Input: {input_path}")
    output_path = f"{config.output_root}{config.run_name}/fim"
    spark = get_spark_cpu("fim-stage", test=test)
    parquet_files = map_parquet.list_files(
        spark, input_path, suffix="parquet", include_path=True
    )
    data = spark.read.parquet(*parquet_files)
    print(f"{time_string()} Generating FIM samples...")
    print(f"Input {input_path} has {data.count()} rows.")
    print(f"Output: {output_path}")

    result = map_parquet.apply_pandas(
        spark,
        map_parquet.chain_processors(
            [
                # Allows unused args to be passed to the row-wise function.
                allow_unused_args()(
                    rag_common.RobustFIMSampler(
                        file_filter,
                        max_problems_per_file=config.max_problems_per_file,
                        every_n_lines=config.every_n_lines,
                        random_seed=config.random_seed,
                        sampler=sampler,
                        get_node_weight=get_node_weight,
                    ).__call__
                ),
                passthrough_feature()(
                    allow_unused_args()(rag_common.remove_fim_with_empty_prefix)
                ),
                passthrough_feature()(
                    allow_unused_args()(rag_common.remove_trouble_keys_from_file_list)
                ),
                passthrough_feature()(
                    allow_unused_args()(rag_common.serialize_fim_problems)
                ),
            ]
        ),
        input_path=input_path,
        output_path=output_path,
        timeout=1200,  # 20 mins timeout
        batch_size=16,
        task_info_location=os.path.join(output_path, "task_info"),
        ignore_error=False,
    )
    print(result["status_count"])
    print(result["task_info"]["stderr"][0])

    data = spark.read.parquet(os.path.join(output_path, "*zstd.parquet"))
    count = data.count()
    print(f"{time_string()} Generated {count} rows in {output_path}.")

    spark.stop()

    config_to_save = asdict(config)
    config_to_save["result"] = dict(status_count=result["status_count"])
    config_to_save["timestamp"] = time_string()
    # Save the config into the output folder.
    config_file_path = os.path.join(output_path, "config.json")
    with open(config_file_path, "w") as f:
        f.write(json.dumps(config_to_save, indent=2))
    print(f"{time_string()} Config saved to {config_file_path}.")


def retrieval_stage(config: ScoreConfig, test: bool = False, first_stage: bool = False):
    """Generate retrieval samples.

    Args:
        config: The config for the pipeline.
        test: Whether to use a smaller cluster.
        first_stage: Whether this is the first stage called in this run of the pipeline.
          Used to determine whether to read the input from different folder.
    """

    def _create_retriever(config) -> RetrievalDatabase:
        from research.eval.harness.factories import create_retriever

        retrieval_database = create_retriever(config)
        assert isinstance(retrieval_database, RetrievalDatabase)
        return retrieval_database

    retriever_config = (
        config.signature_retriever_config
        if config.score_signature
        else config.dense_retriever_config
    )
    retriever_factories = {
        "retrieved": lambda: _create_retriever(retriever_config),
    }
    retriever_instance = GenerateRetrievedChunksFromFim(
        retrieval_database_factories=retriever_factories,
        retrieval_languages=config.retrieval_languages,
        num_retrieved_chunks=config.num_retrieved_chunks,
        use_ground_truth=config.use_ground_truth,
    )

    if first_stage and config.input_path is not None:
        input_path = config.input_path
    else:
        input_path = f"{config.output_root}/{config.run_name}/fim"
    output_path = f"{config.output_root}/{config.run_name}/retrieval"

    print(f"{time_string()} Generating retrieval samples...")
    print(f"Input: {input_path}")
    print(f"Output: {output_path}")

    spark_gpu = get_spark_gpu(name="retrieval-stage", test=test)
    result = map_parquet.apply_pandas(
        spark_gpu,
        map_parquet.chain_processors(
            [
                passthrough_feature()(
                    allow_unused_args()(rag_common.deserialize_fim_problems)
                ),
                retriever_instance,
            ]
        ),
        input_path=input_path,
        output_path=output_path,
        timeout=1800,  # 30 mins timeout
        batch_size=128,
        task_info_location=os.path.join(output_path, "task_info"),
        ignore_error=True if not test else False,
    )
    spark_gpu.stop()
    print(result["status_count"])
    print(result["task_info"]["stderr"][0])

    config_dict = asdict(config)
    config_dict["result"] = dict(status_count=result["status_count"])
    config_dict["timestamp"] = time_string()
    config_file_path = os.path.join(output_path, "config.json")
    with open(config_file_path, "w") as f:
        f.write(json.dumps(config_dict, indent=2))
    print(f"{time_string()} Config saved to {config_file_path}.")


def score_stage(config: ScoreConfig, test: bool = False, first_stage: bool = False):
    """Generate retrieval samples.

    Args:
        config: The config for the pipeline.
        test: Whether to use a smaller cluster.
        first_stage: Whether this is the first stage called in this run of the pipeline.
          Used to determine whether to read the input from different folder.
    """

    if first_stage and config.input_path is not None:
        input_path = config.input_path
    else:
        input_path = f"{config.output_root}/{config.run_name}/retrieval"
    output_path = f"{config.output_root}/{config.run_name}/score"

    print(f"{time_string()} Generating score samples...")
    print(f"Input: {input_path}")
    print(f"Output: {output_path}")

    spark_gpu = get_spark_gpu_large(name="score-stage", test=test)
    compute_instance = ComputePPL(
        model_config=config.score_model_config,
        score_batch_size=config.score_batch_size,
        tokenizer_name=config.score_tokenizer_name,
        max_target_tokens=config.max_target_tokens,
        score_signature=config.score_signature,
    )

    result = map_parquet.apply_pandas(
        spark_gpu,
        map_parquet.chain_processors(
            [
                passthrough_feature()(allow_unused_args()(filter_overlap_chunks_map)),
                passthrough_feature()(
                    allow_unused_args()(
                        create_filter_insufficient_retrieval_chunks_map(
                            min_num_chunks=config.min_num_retrieved_chunks,
                        )
                    )
                ),
                compute_instance,
            ]
        ),
        input_path=input_path,
        output_path=output_path,
        batch_size=8,
        timeout=24 * 3600,  # 24 hours timeout
        task_info_location=os.path.join(output_path, "task_info"),
        ignore_error=True if not test else False,
        allow_resume=True if not test else False,
    )
    spark_gpu.stop()

    print(result["status_count"])
    print(result["task_info"]["stderr"][0])

    config_dict = asdict(config)
    config_dict["result"] = dict(status_count=result["status_count"])
    config_dict["timestamp"] = time_string()
    config_file_path = os.path.join(output_path, "config.json")
    with open(config_file_path, "w") as f:
        f.write(json.dumps(config_dict, indent=2))
    print(f"{time_string()} Config saved to {config_file_path}.")


DENSE_RETRIEVER_CONFIG = {
    "scorer": {
        "name": "ethanol",
        "checkpoint_path": "ethanol/ethanol6-04.1",
    },
    "chunker": {
        "name": "smart_line_level",
        "max_chunk_chars": 768,
        "max_headers": 3,
    },
    "query_formatter": {
        "name": "ethanol6_query",
        "max_tokens": 1023,
        "add_path": True,
    },
    "document_formatter": {
        "name": "ethanol6_document",
        "max_tokens": 999,
        "add_path": True,
    },
}

SIG_RETRIEVER_CONFIG = {
    "chunker": {"name": "signature"},
    "document_formatter": {
        "add_path": False,
        "name": "simple_document",
        "tokenizer_name": "StarCoderTokenizer",
        "max_tokens": 999,
    },
    "query_formatter": {
        "add_path": True,
        "add_suffix": True,
        "max_lines": -1,
        "max_tokens": 1023,
        "name": "ethanol6_query",
        "tokenizer_name": "StarCoderTokenizer",
    },
    "scorer": {
        "checkpoint_path": "/mnt/efs/augment/checkpoints/menthol/methanol_0416.4_1250/global_step1250/",
        "name": "dense_scorer_v2_fbwd_neox",
    },
}

# SCORE_MODEL_CONFIG = {
#     "checkpoint_path": "rogue/diffb1m_7b_alphal_fixtoken",
#     "name": "rogue",
#     "prompt": {
#         "max_prefix_tokens": 250,
#         "max_suffix_tokens": 250,
#         "max_retrieved_chunk_tokens": -1,
#         "max_prompt_tokens": 3072,
#     },
# }

SCORE_MODEL_CONFIG = {
    "name": "elden_fb",
    "checkpoint_path": "star2/elden_7b_fprefretsignpfsuf_rdrop030_1k5b6kstep",
    "model_parallel_size": 1,
    "seq_length": 6600,
    "prompt": {
        "max_prefix_tokens": 0,
        "max_suffix_tokens": 250,
        "max_signature_tokens": 2048,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 6144,
        "component_order": [
            "prefix",
            "retrieval",
            "signature",
            "nearby_prefix",
            "suffix",
        ],
        "context_quant_token_len": 0,
        "nearby_prefix_token_len": 250,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 0,
        "nearby_suffix_token_overlap": 0,
    },
}

CONFIG = ScoreConfig(
    run_name="test",
    input_path=None,
    output_root="/mnt/efs/spark-data/shared/ethanol/",
    #
    # File filter
    small_filter_char_threshold=500,
    small_downsampled_probability=0.1,
    small_downsample_char_threshold=1500,
    sample_languages=list(rag_constants.SAMPLE_LANGUAGES),
    #
    # Fim sampling
    every_n_lines=200,
    max_problems_per_file=5,
    random_seed=74912,
    #
    # Retriever
    retrieval_languages=list(rag_constants.RETRIEVAL_LANGUAGES),
    num_retrieved_chunks=128,
    min_num_retrieved_chunks=20,
    dense_retriever_config=DENSE_RETRIEVER_CONFIG,
    signature_retriever_config=SIG_RETRIEVER_CONFIG,
    use_ground_truth=False,
    #
    # Model scoring
    score_model_config=SCORE_MODEL_CONFIG,
    score_batch_size=8,
    # score_tokenizer_name="starcoder",
    score_tokenizer_name="starcoder2",
    max_target_tokens=256,
    score_signature=False,
)


STAGES = {
    "fim": fim_stage,
    "retrieval": retrieval_stage,
    "score": score_stage,
}


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--stages",
        "-s",
        type=str,
        help="stage to run",
        nargs="+",
        default=["fim", "retrieval", "score"],
    )
    parser.add_argument(
        "--test",
        "-t",
        action="store_true",
        help="test",
    )
    parser.add_argument(
        "--run-name",
        "-r",
        type=str,
        help="run name",
    )
    parser.add_argument(
        "--input-path",
        "-i",
        type=str,
        help="input path",
    )

    args = parser.parse_args()

    if args.run_name is not None:
        CONFIG.run_name = args.run_name

    if args.input_path is not None:
        CONFIG.input_path = args.input_path

    first_stage = True
    for stage in args.stages:
        STAGES[stage](CONFIG, test=args.test, first_stage=first_stage)
        first_stage = False

[pytest]
# log_cli = 1
norecursedirs =
    collection
    eval
    pyarrow
    slurm
markers =
    cpu: marks tests that can be run on cpu
    large_gpu: marks tests that must be run on gpu systems larger than A10 with 24GB DRAM
    multi_gpu: marks tests that must be run on multi gpu systems
    slow: marks tests as slow to run
    parallel: marks tests that can be run in parallel via xdist
    skip_in_ci: run tests locally but not in CI

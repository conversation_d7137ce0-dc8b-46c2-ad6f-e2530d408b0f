#!/usr/bin/env python
"""CLI to inspect parquet data."""

import click
from pyspark.sql.utils import ParseException

from research.data.spark import get_session
from research.data.spark.pipelines.utils import map_parquet

TABLE_NAME = "data"

COUNT_CMD = f"SELECT COUNT(*) AS COUNT FROM {TABLE_NAME}"

HEAD_CMD = f"SELECT * FROM {TABLE_NAME} LIMIT 5"

SCHEMA_CMD = f"DESCRIBE {TABLE_NAME}"


@click.command()
@click.argument("path", required=True, type=click.Path())
@click.argument("command", type=str, default=None, required=False)
def main(path: str, command: str):
    """CLI for inspecting parquet data.

    COMMAND: "size" to count total number of entries; "head" to show first 5 entries; Leave it empty to entry interactive mode.
    """
    print("Initalizing spark...")
    spark = get_session(control_plane="local[*]")
    df = spark.read.parquet(
        *map_parquet.list_files(spark, path, suffix="parquet", include_path=True)
    )
    print(f"Parquet data in path {path} can be accessed through table `{TABLE_NAME}`.")

    df.createOrReplaceTempView(TABLE_NAME)
    if command is None:
        print("Input SELECT queries to inspect data.  Write `exit` to exit.")
        while True:
            try:
                # Input command from the user
                command = input("SQL> ")
                if not command.strip():
                    continue
                command_name = command.strip().split(maxsplit=1)[0].lower()
                if command_name == "exit":
                    break
                if command_name != "select":
                    print("\nWe only support SELECT queries for now")
                    continue
                result = spark.sql(command)
                if result:
                    result.show(truncate=50)
            except ParseException as e:
                print("\nError parsing SQL query: ", e.description)
            except KeyboardInterrupt:
                print("\nExiting due to Ctrl+C")
                break
            except EOFError:
                print("\nExiting due to EOF")
                break
    else:
        if command == "size":
            command = COUNT_CMD
        elif command == "head":
            command = HEAD_CMD
        elif command == "schema":
            command = SCHEMA_CMD
        else:
            raise ValueError(f"Unknown command: {command}")
        result = spark.sql(command)
        if result:
            result.show(truncate=50)
    spark.stop()


if __name__ == "__main__":
    main()

#!/usr/bin/env python

"""This script checks for invalid imports.

Currently, it looks for imports from experimental.

"""

import argparse
from typing import List, Tuple
from pathlib import Path


def test_check_file_contents():
    assert check_file_contents("from experimental.foo import bar") == [
        (0, "from experimental.foo import bar")
    ]
    assert check_file_contents("import experimental.foo") == [
        (0, "import experimental.foo")
    ]
    assert check_file_contents("from foo import bar") == []
    assert check_file_contents("import foo") == []
    assert check_file_contents("from foo import experimental.bar") == []
    assert check_file_contents("import foo.experimental.bar") == []
    assert check_file_contents("from foo.experimental import bar") == []
    assert check_file_contents("from foo.experimental.bar import baz") == []
    assert check_file_contents("import foo.experimental.bar") == []
    assert check_file_contents(
        "from foo.experimental.bar import baz\nimport experimental.qux"
    ) == [(1, "import experimental.qux")]
    assert check_file_contents(
        "def foo():\n    from experimental.foo import bar\n    return bar()"
    ) == [(1, "    from experimental.foo import bar")]


def check_file_contents(content: str) -> List[Tuple[int, str]]:
    matches = []
    if "from experimental." in content or "import experimental." in content:
        lines = content.splitlines()
        for i, line in enumerate(lines):
            if line.strip().startswith("from experimental.") or line.strip().startswith(
                "import experimental."
            ):
                matches.append((i, line))
    return matches


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("filenames", nargs="*")
    return parser.parse_args()


def main():
    args = parse_args()
    rc = 0
    for filename in args.filenames:
        try:
            contents = Path(filename).read_text()
        except UnicodeDecodeError:
            continue
        matches = check_file_contents(contents)
        if matches:
            print(f"Invalid experimental import in {filename}")
            for i, line in matches:
                print(f"{filename}:{i+1}: {line}")
            rc = 1
    return rc


if __name__ == "__main__":
    exit(main())

"""Utility functions for fastbackward.

Before adding a function here, consider if there is a more specific module it could go in.
"""

from __future__ import annotations

import ast
import functools
import gc
import json
import logging
from collections.abc import Iterator, Sequence
from pathlib import Path
from typing import Any, Mapping, Optional, TypeVar

import torch
import yaml

logger = logging.getLogger(__name__)


def clear_cuda_memory_cache():
    gc.collect()
    torch.cuda.empty_cache()


# From https://github.com/facebookresearch/llama/blob/ef351e9cd9496c579bf9f2bb036ef11bdc5ca3d2/llama/generation.py#L398
def sample_top_p(probs: torch.Tensor, p: float):
    probs_sort, probs_idx = torch.sort(probs, dim=-1, descending=True)
    probs_sum = torch.cumsum(probs_sort, dim=-1)
    mask = probs_sum - probs_sort > p
    probs_sort[mask] = 0.0
    probs_sort.div_(probs_sort.sum(dim=-1, keepdim=True))
    next_token = torch.multinomial(probs_sort, num_samples=1)
    next_token = torch.gather(probs_idx, -1, next_token)
    return next_token


def flatten_dict(
    nested_dict: Mapping[str, Any], delimiter: str = "."
) -> dict[str, Any]:
    """Flatten a nested dict.

    For example, if nested_dict is:
        {
            "a": { "b": 1, "c": 2, },
            "d": 3,
            "e": [{ "f": 4 }, { "f": 5 },]
        }
    then this function will return:
        {
            "a.b": 1,
            "a.c": 2,
            "d": 3,
            "e.0.f": 4,
            "e.1.f": 5,
        }

    Args:
        nested_dict: The nested dict to flatten.
        delimiter: The delimiter to use when flattening keys.

    Returns:
        A flattened dict where none of its values contains dicts.
    """
    flattened = {}
    for key, value in nested_dict.items():
        if isinstance(value, dict):
            assert (
                delimiter not in key
            ), f"Nested keys cannot contain '{delimiter=}'s. Found {key=}."
            for sub_key, sub_value in flatten_dict(value, delimiter=delimiter).items():
                flattened[f"{key}{delimiter}{sub_key}"] = sub_value
        elif isinstance(value, list):
            # Handle the recursive case.
            for sub_key, sub_value in flatten_dict(
                {str(i): v for i, v in enumerate(value)}, delimiter=delimiter
            ).items():
                flattened[f"{key}{delimiter}{sub_key}"] = sub_value
        else:
            flattened[key] = value

    return flattened


def unflatten_dict(
    flattened_dict: Mapping[str, Any], delimiter: str = ".", max_level: int = -1
) -> dict[str, Any]:
    """Unflatten a dict with "." delimited nested keys.

    For example, if flattened_dict is:
        {
            "a.b": 1,
            "a.c": 2,
            "d": 3,
            "e.0.f": 4,
            "e.1.f": 5,
        }
    then this function will return:
        {
            "a": { "b": 1, "c": 2, },
            "d": 3,
            "e": [{ "f": 4 }, { "f": 5 },]
        }

    We assume any nested dict with integer-only keys is actually a list and will convert
    it to be so (`e.0`, `e.1` above), with the exception of the top-level, which is
    always a dict.

    Args:
        flattened_dict: The flattened dict to unflatten.
        delimiter: The delimiter to use when unflattening keys.
        max_level: The maximum level of nesting to unflatten. Keys deeper than this
            will be left as dicts.

    Returns:
        An unflattened dict.
    """
    nested = {}
    for k, v in flattened_dict.items():
        *keys, last_key = k.split(delimiter, max_level)
        nested_dict = nested
        for i, key in enumerate(keys):
            assert isinstance(
                nested_dict, dict
            ), f"Value at {delimiter.join(keys[:i])} should be a dict, not {nested_dict=}."
            nested_dict = nested_dict.setdefault(key, dict())
        assert isinstance(
            nested_dict, dict
        ), f"Value at {delimiter.join(keys)} should be a dict, not {nested_dict=}."
        nested_dict[last_key] = v

    # Replace any nested dicts with integer-only indices with a list:
    def _listify_rec(nested_dict: dict[str, Any]) -> dict[str, Any] | list[Any]:
        # Listify recursively.
        for key, value in nested_dict.items():
            if isinstance(value, dict):
                nested_dict[key] = _listify_rec(value)

        if all(k.isdigit() for k in nested_dict):
            # Make sure the indices == a range from 0 to k.
            indices = sorted(nested_dict.keys(), key=int)
            if all(i == int(j) for i, j in enumerate(indices)):
                return [nested_dict[i] for i in indices]

        return nested_dict

    for key, value in nested.items():
        if isinstance(value, dict):
            nested[key] = _listify_rec(value)

    return nested


def parse_key_value_args(args: list[str]) -> dict[str, Any]:
    """Parse a list of key-value arguments into a dict.

    Args:
        args: A list of arguments, e.g. ["--key1=value", "--key2.subkey=value2"].
            Any list elements that aren't of the form `--key=value` will be ignored.

    Returns:
        The arguments converted into a dictionary. For example,
            {"key1": "value", "key2": {"subkey": "value2"}}
    """
    ret = {}
    for arg in args:
        # assume it's a --key=value argument
        if not (arg.startswith("--") and "=" in arg):
            continue

        key, val = arg.split("=", 1)
        key = key[len("--") :]

        # Try to parse the value into a Python literal.
        try:
            val = ast.literal_eval(val)
        except (SyntaxError, ValueError):
            logger.debug(
                f"Failed to parse {arg=} as a Python literal. Keeping it as a string."
            )

        ret[key] = val

    return unflatten_dict(ret)


def combine_dict(base: dict[str, Any], update: dict[str, Any]) -> dict[str, Any]:
    """Merge `update` into `base` by overwriting any existing keys.

    By default (and unlike dict.update) this function will merge into nested values
    rather than replace them. If you want to replace nested dict field, append "~" to
    its key (~ doesn't conflict with bash's expectations).

    For example, if base is:
        {
            "a": { "b": 1, "c": 2, },
            "d": 3,
            "e": { "f": 3 },
        }
    and update is:
        {
            "a": { "b": 4, "d": 5, },
            "e~": 6,
        }
    then this function will return:
        {
            "a": { "b": 4, "c": 2, "d": 5, },
            "d": 3,
            "e": 6,
        }


    Args:
        base: The base dict. Modified in place.
        update: The update dict.

    Returns:
        A new dict with the update merged into the base.
    """
    FORCE_UPDATE_SUFFIX = "~"
    ret = base
    for key, value in update.items():
        if force_update := key.endswith(FORCE_UPDATE_SUFFIX):
            key = key[: -len(FORCE_UPDATE_SUFFIX)]

        if (
            not force_update
            and isinstance(value, dict)
            and isinstance(ret.get(key), dict)
        ):
            ret[key] = combine_dict(ret[key], value)
        else:
            ret[key] = value

    return ret


def avg_list_of_dict(dicts: list[dict[str, Any]]) -> dict[str, Any]:
    """Average a list of dicts.

    Args:
        dicts: A list of dicts.

    Returns:
        A dict with the average of each key.
    """
    result: dict[str, Any] = {}
    for k in dicts[0].keys():
        result[k] = sum(d[k] for d in dicts) / len(dicts)
    return result


def parse_config_files(args: list[str]) -> dict:
    """Parse any configuration YAML files in from the command line.

    As a special case to support loading our training configs, if the file contains
    a top-level key "fastbackward_args", we will load that instead of the entire file.

    Args:
        args: A list of command line arguments.

    Returns:
        A dict of the parsed configuration.
    """

    ret = {}

    for arg in args:
        # Skip key-value arguments.
        if arg.startswith("--"):
            continue
        arg = Path(arg)
        if not arg.exists():
            raise ValueError(f"Config file {arg} doesn't exist.")

        with arg.open("r") as f:
            if arg.suffix == ".json":
                config = json.load(f)
            elif arg.suffix == ".yaml" or arg.suffix == ".yml":
                config = yaml.safe_load(f)
            else:
                raise ValueError(f"Unsupported config file type: {arg}")
        if "fastbackward_args" in config:
            # Special case for Determined configurations which use "fastbackward_args"
            # as their top-level key for training arguments.
            config = config["fastbackward_args"]
        if arg.name == "last_saved_info.json" and "config" in config:
            # Special case for configs stored in checkpoints which use "config" as their
            # top-level key for training arguments.
            config = config["config"]

        ret = combine_dict(ret, config)

    return ret


# Because this takes no arguments, this is a simple way of only running this function
# once.
@functools.lru_cache()
def setup_dataclasses_json():
    """Setup dataclasses_json to handle commonly used types.

    This function should be run before any dataclass is converted to JSON.
    """
    import dataclasses_json
    from marshmallow import fields as mm_fields

    dataclasses_json.global_config.encoders[Path] = str
    dataclasses_json.global_config.decoders[Path] = Path
    dataclasses_json.global_config.mm_fields[Path] = mm_fields.String()
    # `Optional[Path]` needs to be separately registered, but this conflicts with
    # dataclasses_json's type-hints.
    dataclasses_json.global_config.encoders[Optional[Path]] = str  # type: ignore
    dataclasses_json.global_config.decoders[Optional[Path]] = Path  # type: ignore
    dataclasses_json.global_config.mm_fields[Optional[Path]] = mm_fields.String()  # type: ignore


T = TypeVar("T")


def batched(values: Sequence[T], size: int) -> Iterator[Sequence[T]]:
    """Yields batches of size `size` from `values`."""
    for i in range(0, len(values), size):
        yield values[i : i + size]

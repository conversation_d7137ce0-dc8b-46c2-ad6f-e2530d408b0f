"""Utilities for managing PyTorch models that keep their weights and grads in a single flat buffer."""

from __future__ import annotations

from collections.abc import Collection, Iterable

import torch
from torch import Tensor
from torch.nn import Parameter

from research.fastbackward.utils import clear_cuda_memory_cache


class FlatModelState:
    """State associated with a flattened model."""

    def __init__(
        self,
        params_buf: torch.Tensor,
        grads_buf: torch.Tensor,
        param_addrs: dict[Parameter, tuple[int, int]],
        group_addrs: dict[str, tuple[int, int]] | None = None,
        dist_rank: int = 0,
        dist_world_size: int = 1,
        grad_acc_refs: dict[Parameter, object] | None = None,
    ):
        assert params_buf.numel() == grads_buf.numel()
        assert params_buf.numel() % dist_world_size == 0
        self.params_buf = params_buf  # Flat buffer of parameters
        self.grads_buf = grads_buf  # Flat buffer of grads
        self.param_addrs = param_addrs  # map param -> (start, end)
        self.group_addrs = group_addrs  # map group_key -> (start, end)
        self.dist_rank = dist_rank
        self.dist_world_size = dist_world_size
        self.grad_acc_refs = grad_acc_refs
        self.synthetic_params = {}

    def _range_of(self, group_key=None):
        if not group_key:
            return 0, self.params_buf.numel()
        else:
            assert self.group_addrs
            return self.group_addrs[group_key]

    def _dist_range_of(self, group_key=None):
        slice_numel = self.params_buf.numel() // self.dist_world_size
        start = slice_numel * self.dist_rank
        end = start + slice_numel
        if group_key:
            group_start, group_end = self._range_of(group_key)
            start = max(start, group_start)
            end = min(end, group_end)
        return start, end

    def _get_synthetic_param(self, start, end):
        key = (start, end)
        if key in self.synthetic_params:
            return self.synthetic_params[key]
        param = self.params_buf[start:end]
        param.grad = self.grads_buf[start:end]
        self.synthetic_params[key] = param
        return param

    def params(self, group_key=None):
        start, end = self._range_of(group_key)
        return self.params_buf[start:end]

    def grads(self, group_key=None):
        start, end = self._range_of(group_key)
        return self.grads_buf[start:end]

    # This function returns a fake "Parameter" object that is a slice of the params buf with the
    # `.grad` attribute set correctly. You can use this to interact with normal PyTorch optimizers.
    # TODO: needs a clearer name
    def dist_param(self, group_key=None):
        start, end = self._dist_range_of(group_key)
        return self._get_synthetic_param(start, end)

    # In contrast, this function simply returns the right slice of the params buf.
    def dist_params(self, group_key=None):
        start, end = self._dist_range_of(group_key)
        return self.params_buf[start:end]

    def dist_grads(self, group_key=None):
        start, end = self._dist_range_of(group_key)
        return self.grads_buf[start:end]

    def zero_grads(self):
        self.grads_buf.zero_()

    @property
    def dtype(self):
        return self.params_buf.dtype

    @property
    def device(self):
        return self.params_buf.device


def flatten_model(
    params: dict[str, Iterable[Parameter]] | Collection[Parameter],
    dist_rank: int = 0,
    dist_world_size: int = 1,
) -> FlatModelState:
    assert len(params) > 0
    if isinstance(params, dict):
        all_params = [p for some_params in params.values() for p in some_params]
    else:
        all_params = params
    params_requiring_grads = [p for p in all_params if p.requires_grad]
    params_buf, param_addrs = flatten_params(params_requiring_grads, dist_world_size)
    # Defensively clear the cache after flattening, since the per-param weights are now free
    clear_cuda_memory_cache()
    grads_buf = torch.zeros_like(params_buf)
    has_param_groups = isinstance(params, dict)

    group_addrs: dict[str, tuple[int, int]] | None = {} if has_param_groups else None
    if has_param_groups:
        assert group_addrs is not None
        for group_name, group_params in params.items():
            assert isinstance(group_name, str)
            group_start = min(param_addrs[p][0] for p in group_params)
            group_end = max(param_addrs[p][1] for p in group_params)
            group_addrs[group_name] = (group_start, group_end)

    # Update the `.grad` attribute for each param to point into the flat grad buf
    for param, addr in param_addrs.items():
        start, end = addr
        param.grad = grads_buf[start:end].view_as(param)

    # This commented-out code lets autograd allocate `.grad`s as normal and separately accumulates
    # the result into the flat grad buf. Disabled for now to save a little memory, but it might be
    # necessary for CUDA graph execution.
    grad_acc_refs = {}
    # for param in params_requiring_grads:
    #     start, end = param_addrs[param]
    #     hook = _make_grad_acc_hook(param, grads_buf, start, end)
    #     # Hack to access gradient accumlate function
    #     param_tmp = param.expand_as(param)
    #     grad_acc = param_tmp.grad_fn.next_functions[0][0]
    #     grad_acc.register_hook(hook)
    #     grad_acc_refs[param] = grad_acc

    return FlatModelState(
        params_buf,
        grads_buf,
        param_addrs,
        group_addrs,
        dist_rank,
        dist_world_size,
        grad_acc_refs,
    )


def _make_grad_acc_hook(param: Parameter, grads_buf: Tensor, start: int, end: int):
    def hook(*args):  # pylint: disable=unused-argument
        if param.grad is not None:
            with torch.no_grad():
                grads_buf[start:end].add_(param.grad.view(end - start))
            param.grad = None

    return hook


def round_up(x: int, alignment: int) -> int:
    residual = x % alignment
    if residual == 0:
        return x
    else:
        return x + alignment - residual


def flatten_params(
    params: Iterable[Parameter], dist_world_size: int = 1
) -> tuple[Tensor, dict[Parameter, tuple[int, int]]]:
    """Flatten a list of parameters into a single buffer."""
    aparam = next(iter(params))

    # Align everything to 128 byte boundaries
    def _align(x):
        return round_up(x, 128 // aparam.element_size())

    param_addrs = {}
    start, end = 0, 0
    for p in params:
        end = start + p.numel()
        param_addrs[p] = (start, end)
        start = _align(end)

    # Ensure buf is divisible by world size for uniform all_gather
    buf_numel = round_up(end, dist_world_size)
    params_buf = torch.zeros(buf_numel, dtype=aparam.dtype, device=aparam.device)
    for p in params:
        start, end = param_addrs[p]
        with torch.no_grad():
            params_buf[start:end].view_as(p).copy_(p)
        assert p._base is None  # pylint: disable=protected-access
        p.data = params_buf[start:end].view_as(p.data)

    return params_buf, param_addrs

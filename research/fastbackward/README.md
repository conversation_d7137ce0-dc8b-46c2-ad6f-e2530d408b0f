`fastbackward` is our library to train and finetune decoder-only transformers free from gpt-neox
and deepspeed.

# Running locally / single-node
The top-level script `train.py` is the entrypoint to training. Its arguments are any number of
config files (eg, `configs/llama_7b.py`) along with `--key=value` pairs to set individual config
values. You can see the full list of arguments in `args.py`.

To run locally, you can directly use Python to run `train.py`. E.g.:

```sh
python train.py \
    configs/llama_1b.py \
    --out_dir=$HOME/fastbackward_outputs \
    --n_layers=2 \
    --n_kv_heads=2 \
    --n_embd=128 \
    --ffn_dim_multiple_of=128 \
    --max_iters=10 \
    --lr_decay_iters=10 \
    --eval_interval=3 \
    --eval_items=10 \
    --train_data_path=/mnt/efs/augment/user/zhewei/data/starcoder/filename_match/training \
    --eval_data_path=/mnt/efs/augment/user/zhewei/data/starcoder/filename_match/validation \
    --model_vocab_size=51200 \
    --batch_size=2 \
    --gradient_accumulation_steps=2 \
    --use_activation_checkpointing=False \
    --run_name=test
```

You may use `torchrun` to launch multi-GPU training locally, e.g. (assuming 8 GPUs):
```sh
torchrun --nproc_per_node=8 train.py \
    configs/llama_1b.py \
    --out_dir=$HOME/fastbackward_outputs \
    --n_layers=2 \
    --n_kv_heads=2 \
    --n_embd=128 \
    --ffn_dim_multiple_of=128 \
    --max_iters=10 \
    --lr_decay_iters=10 \
    --eval_interval=3 \
    --eval_items=10 \
    --train_data_path=/mnt/efs/augment/user/zhewei/data/starcoder/filename_match/training \
    --eval_data_path=/mnt/efs/augment/user/zhewei/data/starcoder/filename_match/validation \
    --model_vocab_size=51200 \
    --batch_size=2 \
    --gradient_accumulation_steps=2 \
    --use_activation_checkpointing=False \
    --run_name=test
```

NOTE:

1. This assumes that you are running from the `fastbackward` directory and have already installed the
`augment` package in your environment. This should work either in a dev container or in a node
created with `launch_pod.py`.
2. The config is minimal while touching every aspect of the code, including batching, gradient
accumulation, evaluation, and checkpointing. It also uses `configs/llama_1b.py` as the base config and
overrides some of the defaults so that the user can easily delete certain parameters to revert to the
default 1B config.
3. `64` is the smallest `--n_embd` and `--ffn_dim_multiple_of` that works with the current RoPE
implementation. Zhuoran discovered this empirically and has not investigated the reason.
4. `--model_vocab_size=51200` is necessary since the datasets are pre-tokenized and the example
dataset uses the StarCoder tokenizer with 51200 tokens.

# Running on Determined (including multi-node)

To run on determined, you can use the `determined/launch.py` script that consumes a yaml config
file with a few determined-specific settings, along with a list of config files and individual args
to pass through to the fastbackward job. A sample config file to match the same job above on two
nodes and adding wandb logging looks like:
```
determined:
  description: null
  workspace: Dev
  project: Default

augment:
  podspec_path: "2xH100.yaml"
  gpu_count: 2
  project_group: pretraining

fastbackward_configs:
 - configs/llama_1b.py

fastbackward_args:
  out_dir: null
  n_kv_heads: 2
  n_layers: 2
  n_embd: 128
  ffn_dim_multiple_of: 128
  max_iters: 10
  lr_decay_iters: 10
  eval_interval: 3
  train_data_path: /mnt/efs/augment/user/zhewei/data/starcoder/filename_match/training
  eval_data_path: /mnt/efs/augment/user/zhewei/data/starcoder/filename_match/validation
  batch_size: 2
  gradient_accumulation_steps: 2
  use_activation_checkpointing: False
  wandb_project: <my-project>
  run_name: test
```
Assuming that file is `1b_test.yml`, you can launch the job with:
```
python determined/launch.py 1b_test.yml
```

A more realistic sample config is at `determined/sample_config.yml`.

# Notes and gotchas:
- If you do not set an `--out_dir` or set it empty, checkpointing and logging will both be on Determined.
- Iteration count: you basically always want `max_iters` and `lr_decay_iters` to match.
- Batch sizing: the given `batch_size` is _per GPU_. For the config above, that gives us:
```
16 GPUs * batch_size=4 * gradient_accumulation_steps=2 -> 128 sequences / iteration
block_size=4096 (sequence length) -> ~512K tokens per iteration
```
- For smaller models, you can get a good speedup with `--use_activation_checkpointing=False`
- This job is training from scratch, so there is no checkpoint to load from. Relevant args are:
  - `--checkpoint_optimizer_state=True`: Forces persistence of optimizer states at each checkpointing step. If `False`, FastBackward will only keep the latest optimizer states and remove old ones after saving a new one.
  - `--checkpoint`: Initializes the weights from the checkpoint in the given location (either a directory or a Determined storage ID), for finetuning or restarting from a checkpoint.
  - `--restore_training_state_from_checkpoint`: Loads optimizer state from the checkpoint (in addition to weights).

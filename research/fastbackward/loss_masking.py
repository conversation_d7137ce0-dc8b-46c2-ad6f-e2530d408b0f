"""Loss masking functions."""

from abc import ABC, abstractmethod
from typing import Literal, Sequence
import typing

import numpy as np
import torch
from torch import Tensor


MASKED_ZERO_TOKEN = -(2**31)


def unmask_token(masked_tokens: torch.Tensor) -> torch.Tensor:
    """Convert all loss-masked tokens back to their non-negative token ids.

    This function assumes all masked 0-tokens are encoded as MASKED_ZERO_TOKEN and
    that all other masked tokens are simply negated.
    """
    unmasked = masked_tokens.abs()
    unmasked[masked_tokens == MASKED_ZERO_TOKEN] = 0
    return unmasked


def unmask_token_np(masked_tokens: np.ndarray) -> np.ndarray:
    """Convert all loss-masked tokens back to their non-negative token ids.

    This function assumes all masked 0-tokens are encoded as MASKED_ZERO_TOKEN and
    that all other masked tokens are simply negated.
    """
    unmasked = np.abs(masked_tokens)
    unmasked[masked_tokens == MASKED_ZERO_TOKEN] = 0
    return unmasked


class LossMaskFn(ABC):
    """Base class for loss masking functions."""

    @abstractmethod
    def __call__(self, tokens: Tensor) -> Tensor:
        pass


class NoOpLossMask(LossMaskFn):
    """No-op loss mask."""

    def __call__(self, tokens: Tensor) -> Tensor:
        return torch.ones_like(tokens, dtype=torch.bool)


class NegativeLossMask(LossMaskFn):
    """Loss mask that masks out negative tokens."""

    def __call__(self, tokens: Tensor) -> Tensor:
        return tokens >= 0


class NonPositiveLossMask(LossMaskFn):
    """Loss mask that masks out non-positive tokens."""

    def __call__(self, tokens: Tensor) -> Tensor:
        return tokens > 0


class PadLossMask(LossMaskFn):
    """Loss mask that only masks out padding tokens.

    Negative tokens are first converted to nonnegative tokens.
    """

    def __init__(self, pad_token_id: int, eos_token_id: int):
        self.pad_token_id = pad_token_id
        self.eos_token_id = eos_token_id

    def __call__(self, tokens: Tensor) -> Tensor:
        tokens = unmask_token(tokens)
        if self.eos_token_id == self.pad_token_id:
            # Special case when we use the same token for both EOT and PAD.
            # An EOT is padding if the token on the left is also EOT
            # all indices of EOS tokens
            is_eos = tokens == self.eos_token_id

            # shift is the eos token masks shifted by 1 spot to the right.
            # last element is rolled back to first.
            # We need to be doing this on the second dimension (i.e. dim 1).
            shifted = torch.roll(is_eos, 1, 1)

            # pad mask indicates that a token is EOS and the token on its left is also EOS
            pad_mask = is_eos & shifted

            # first token is never padding.  This might be true because torch.roll
            # effectively treats the last element as on the left of the first token.
            pad_mask[:, 0] = False
        else:
            pad_mask = tokens == self.pad_token_id
        return ~pad_mask


class IgnoreListLossMask(LossMaskFn):
    """Loss mask that masks out tokens in the ignore list.

    Padding tokens and tokens with negative values are also masked out.
    """

    def __init__(
        self, ignore_list: Sequence[int], pad_token_id: int, eos_token_id: int
    ):
        """Initialize the ignore list."""
        self.ignore_list = torch.tensor(ignore_list)
        self.pad_loss_mask = PadLossMask(pad_token_id, eos_token_id)

    def __call__(self, tokens: Tensor) -> Tensor:
        """Return a mask tensor that masks out tokens in the ignore list."""
        # The shape of the tensors is (batch, seq_len)
        not_ignored = ~torch.isin(unmask_token(tokens), self.ignore_list)
        negative = tokens < 0
        not_pad = self.pad_loss_mask(tokens)
        return not_ignored & not_pad & ~negative


class FimLossMask(LossMaskFn):
    """Loss mask that masks out tokens after `fim_middle_token_id` and before `eot_token_id`.

    It also masks out padding tokens. Negative tokens are converted into nonnegative
    tokens first before deciding whether to mask them out.
    """

    def __init__(
        self,
        fim_middle_token_id: int,
        eot_token_id: int,
        pad_token_id: int,
        include_tokens_before_fim_middle: bool = False,
    ):
        self.fim_middle_token_id = fim_middle_token_id
        self.eot_token_id = eot_token_id
        self.pad_token_id = pad_token_id
        self.include_tokens_before_fim_middle = include_tokens_before_fim_middle

    def __call__(self, tokens: Tensor) -> Tensor:
        tokens = unmask_token(tokens)
        is_after_or_equal_fim_middle = (
            torch.cumsum(tokens == self.fim_middle_token_id, -1) > 0
        )
        is_after_fim_middle = torch.logical_and(
            is_after_or_equal_fim_middle, tokens != self.fim_middle_token_id
        )

        is_eot = tokens == self.eot_token_id
        is_pad = tokens == self.pad_token_id

        if self.eot_token_id == self.pad_token_id:
            # Special case when we use the same token for both EOT and PAD.
            # This is the case, for example, for DeepSeek tokenizer.
            # In this case, we treat the first EOT token as actual EOT,
            # and the rest as PADs.
            is_first_eot_token = torch.cumsum(is_eot, -1) == 1
            is_eot = torch.logical_and(is_eot, is_first_eot_token)
            is_pad = torch.logical_and(is_pad, torch.logical_not(is_first_eot_token))

        is_after_or_equal_eot = torch.cumsum(is_eot, -1) > 0
        is_before_eot = torch.logical_not(is_after_or_equal_eot)
        is_before_or_equal_eot = torch.logical_or(is_before_eot, is_eot)
        is_not_pad = torch.logical_not(is_pad)
        if self.include_tokens_before_fim_middle:
            return torch.logical_and(is_not_pad, is_before_or_equal_eot)
        else:
            return torch.logical_and(
                is_after_fim_middle,
                torch.logical_and(is_not_pad, is_before_or_equal_eot),
            )


LossMaskName = Literal[
    "none",
    "negative_tokens",
    "nonpositive_tokens",
    "fim",
    "pad",
    "fim_all_tokens",
    "ignore_list",
]
"""A string name for the loss masking function."""


def is_valid_loss_mask_name(name: str) -> typing.TypeGuard[LossMaskName]:
    return name in typing.get_args(LossMaskName)


# TODO: improve configuration for this
def get_loss_mask_fn(
    loss_mask_policy: LossMaskName,
    fim_middle_token_id: int,
    eot_token_id: int,
    pad_token_id: int,
    ignore_list: Sequence[int] = (),
):
    if loss_mask_policy == "none":
        return NoOpLossMask()
    elif loss_mask_policy == "negative_tokens":
        return NegativeLossMask()
    elif loss_mask_policy == "nonpositive_tokens":
        return NonPositiveLossMask()
    elif loss_mask_policy == "fim":
        return FimLossMask(fim_middle_token_id, eot_token_id, pad_token_id)
    elif loss_mask_policy == "pad":
        return PadLossMask(pad_token_id, eot_token_id)
    elif loss_mask_policy == "fim_all_tokens":
        return FimLossMask(
            fim_middle_token_id,
            eot_token_id,
            pad_token_id,
            include_tokens_before_fim_middle=True,
        )
    elif loss_mask_policy == "ignore_list":
        return IgnoreListLossMask(ignore_list, pad_token_id, eot_token_id)
    else:
        typing.assert_never(loss_mask_policy)

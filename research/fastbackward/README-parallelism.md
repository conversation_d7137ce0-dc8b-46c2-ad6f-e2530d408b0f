# Parallelism in fastbackward

This document serves as a high-level overview of the support for various sorts of parallelism in fastbackward. Broadly speaking, we support three kinds:
1. Data parallelism
2. Model (or Tensor) Parallelism
3. Sequence parallelism

## Terminology

To support parallel execution, all of the GPUs are organized into overlapping subsets called _groups_:
- _World_ or _world group_: all of the GPUs in the run.
- _Data parallel group_: a group of GPUs that each have the _same weights and different data batches_. At each iteration, they compute gradients wrt their distinct batches and then average those gradients together.
- _Model parallel group_: a group of GPUs that each have _different weights and the same input data batches_. The full set of model weights are divided up across the group's GPUs, and they cooperate to compute the gradient wrt the same input batch. Note that they do not exchange gradients, since they each have different weights.

Each GPU has a _rank_ (index) associated with each of these groups. Its _rank_ or _world rank_ is its global index among all GPUs (0 to N-1). Its _data parallel rank_ and _model parallel_rank_ are its position (0-indexed) within its data or model parallel group, respectively.

Every GPU is a member of one data parallel group and one model parallel group. Either or both groups can have a single member, in which case communication within that group is a no-op.

This terminology isn't used in the code, but it is possible to talk about the "0th" data parallel group (and so on). You can convince yourself that the 0th data parallel group consists of every GPU with _model parallel rank_ 0 (and vice versa).

Note that there is no mention here of sequence parallelism, since it has no distinct group. Instead, this is an additional form of parallelism within the model parallel group.

## Data parallelism

There is not much to say about data parallelism: this is a well-known technique at this point. For fastbackward in particular, the only notes are:
- We make no effort to overlap gradient communication with backprop. The speed gains are close-to-none and the extra complexity is substantial.
- We use a distributed optimizer that shards the optimizer state and performs updates as:
```
reduce_scatter(grads)
update(my_optimizer_slice)
all_gather(new_weights)
```

## Model parallelism

### Tensor parallelism

We use a specific sort of tensor parallelism introduced in Nvidia's Megatron implementation. Both the attention and MLP blocks are a pair of big matmuls. Each is split into K parts (for K-way model parallel):
- QKV: column-parallel
- Proj: row-parallel
- MLP1: column-parallel
- MLP2: row-parallel

Aside: I find the "column/row-parallel" terminology confusing, though it is standard. Here is my decoder ring:
- Column parallel: split the _output_ dimension. So if the full weight matrix maps from dimension `h1` to `h2`, then each parallel shard maps from `h1` to `h2 // K`.
- Row parallel: split the _input_ dimension.

If you draw a picture, you'll see that you can pair together a column-parallel matmul with a row-parallel matmul to get an output of the right _shape_ but sharded across devices in the "reduction" dimension. So we perform an all_reduce after the row-parallel matmul to assemble the full activation tensor.

### Sequence parallelism

Each matmul pair forms a tensor-parallel "region". That is, from QKV to proj and MLP1 to MLP2, the activations are parallelized due to being multiplied with sharded weights. But there is still work that happens outside those regions: mostly norms and residual adds.

Rather than doing this work redundantly, we will shard the activations _in the sequence dimension_ for those parts of execution that are _outside_ of the tensor-parallel regions. This is a small time savings (norm and residual add aren't expensive) but a big _memory_ savings, since your activation tensors are 1/Kth the size -- _and_ those are the tensors we save in activation checkpointing.

I expect this diagram needs more explanation, but here is ascii art of a state diagram showing how to transition between any two kinds of execution regions. "None" here means "no kind of parallelism" (normal execution).

```
       +------------+         all_reduce         +-------------+
       |            |<------------<--------------.             |
       |    None    |                            |    Tensor   |
       |            .------------->-------------->             |
       +.-----------+         No-op              +---.---------+
        |        ^                                   |        ^
        v        |                                   |        |
        |        |                                   v        |
        |        |                                   |        |
   split|        |                     reduce_scatter|        |
        |        |                                   |        |
        |        |all_gather                         |        |all_gather
        |        |                                   v        |
        |        ^                                   |        ^
        v        |                                   |        |
        |        |          +--------------+         |        |
        |        +------<---.              |<----<---+        |
        |                   |   Sequence   |                  |
        +------>----------->|              .-------->---------+
                            +--------------+
```

This diagram also makes clear why the "backward" implementations for various tensor- and sequence-parallel ops are what they are -- they are really state transitions. If sequence -> tensor (forward) is all_gather, then the backward of that op is simply tensor -> sequence (reduce_scatter).

Another point to aid in the understanding of the state diagram is its "transitivity". E.g. for none -> sequence -> tensor, split + all_gather is equivalent to a no-op, so none -> tensor is a no-op. For tensor -> none -> sequence, all_reduce + split is equivalent to a reduce_scatter, so tensor -> sequence is a reduce_scatter.


### Partially-sharded GQA

#### Definition

Partially-sharded grouped-query attention (GQA) means GQA where `1 < n_kv_heads < gpu_count`, so that

1. There are more than 1 KV heads, requiring sharding for efficiency;
2. There are insufficient KV heads for 1 per GPU, necessitating replication.

Consequently, fastbackward needs to partially shard and partially replicate these KV heads.

#### Notations

Since KV heads need partial sharding and partial replication within a model-parallel (MP) group, we define:
- A **KV (MP) subgroup** to be the process group that replicates the same KV head; and
- A **conjugate KV subgroup** to be the process group that are of the same rank in their respective KV subgroups, which holds a full set of KV heads.

#### Forward pass

In partially-sharded GQA forward:
- All processes in a KV subgroup must have the same weight;
- They will receive the same, replicated input;
- They will produce the same keys and values;
- The keys will interact with different queries and generate different attention scores;
- The different attention probabilities will aggregate the same values into different outputs;
- The outputs go through an all-reduce to produce the final output.

#### Backward pass

In partially-sharded GQA backward:
- Each process receives the same gradients because the output all-reduce;
- Due to different queries, each process's values will receive different gradients, necessitating a **post-accumulation gradient all-reduce on the value layers**;
- Due to different attention probabilities, the softmax nonlinearity, and different queries, each process's keys will receive different gradients, necessitating a **post-accumulation gradient all-reduce on the key layers**;
- Due to all the differences above, the input in each process in the **full model-parallel group**, not just the KV subgroup, will receive different gradients, but they were the same, replicated input, necessitating an **gradient all-reduce on the inputs**.

### A note on model-parallel weight replication

Above, I said that the GPUs in a model-parallel group have different weights and the same input data. It is not _quite_ true that the weights are fully divided among the model parallel group. A few weights are still replicated because they live outside the model parallel region. In practice, those are:
- Weights in the norm layers (LayerNorm or FusedRMSNorm)
- Biases in Proj / MLP2 (the 2nd matmul in each block)
Because all GPUs in the model parallel group have the same input data, they will each compute the same gradient for these weights, meaning you need no extra communication. However, in the sequence parallel case, the GPUs no longer have the same data, since the data is split on the sequence dimension. As a consequence, the gradients for these weights need to be all_reduced within the model parallel group _as well as_ across the data parallel group. You can see how this is handled in `_register_sequence_parallel_grad_allreduce`.

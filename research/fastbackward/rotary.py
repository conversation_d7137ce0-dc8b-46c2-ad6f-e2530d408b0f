"""Rotary embeddings for fastbackward."""

from dataclasses import dataclass

import torch
from torch import Tensor

from base.fastforward.positional_embeddings import RotaryConfig, calculate_rotary_freqs


@torch.compile
def rotary_embed(x: Tensor, freqs_cos: Tensor, freqs_sin: Tensor):
    """Yet another implementation of RoPE.

    This is based on the complex number-based implementation from the upstream LLAMA repo,
    with a particular focus on being `torch.compile` compatibility. As of PyTorch 2.1, this
    function (and its gradient) is compiled to a single fused kernel.

    NOTE: This is the LLAMA-style implementation, also called "interleaved" in (eg) Megatron.
    Args:
        x: Input activations of shape (batch, seqlen, nheads, headdim).
        freqs_{cos, sin}: Pre-computed cos/sin tables of shape (1, seqlen, 1, headdim // 2).
          See `precompute_rotary_freqs`.

    Returns:
        Result tensor of shape (batch, seqlen, nheads, headdim).
    """
    # Interleave pairwise in the hidden dimension and interpret each pair as a complex number.
    x_ = x.float().reshape(*x.shape[:-1], -1, 2)
    x_real = x_[..., 0]
    x_imag = x_[..., 1]
    # Perform the complex product (cos + i sin) * (x_real + i x_imag).
    cos_real = freqs_cos * x_real
    sin_imag = freqs_sin * x_imag
    cos_imag = freqs_cos * x_imag
    sin_real = freqs_sin * x_real
    real_part = (cos_real - sin_imag).type_as(x)
    imag_part = (cos_imag + sin_real).type_as(x)
    # Stack the parts and de-interleave along the last dimension
    return torch.stack((real_part, imag_part), dim=-1).flatten(-2)


@dataclass
class RotaryFreqs:
    """Helper class to hold the cos/sin tables for RoPE."""

    cos: Tensor
    sin: Tensor

    @torch.no_grad()
    def to(self, *args, **kwargs):
        return RotaryFreqs(self.cos.to(*args, **kwargs), self.sin.to(*args, **kwargs))

    @torch.no_grad()
    def slice(self, start: int, end: int):
        return RotaryFreqs(self.cos[:, start:end, ...], self.sin[:, start:end, ...])

    def apply(self, fn):
        return RotaryFreqs(fn(self.cos), fn(self.sin))


def precompute_rotary_freqs(
    headdim: int,
    seqlen: int,
    config: RotaryConfig,
) -> RotaryFreqs:
    """Helper function to compute the cos/sin tables for RoPE.

    Args:
        headdim: Hidden Dimension Per-head.
        seqlen: Maximum Sequence Length.
        config: The Rotary Config.

    Returns:
        Class with cos/sin tensors of shape (1, seqlen, 1, headdim // 2).
    """
    freqs_cos, freqs_sin = calculate_rotary_freqs(
        rope_dim=int(config.rotary_ratio * headdim),
        max_seq_len=seqlen,
        config=config,
        device="cpu",
    )
    freqs_cos = freqs_cos.unsqueeze(0).unsqueeze(2)
    freqs_sin = freqs_sin.unsqueeze(0).unsqueeze(2)
    return RotaryFreqs(freqs_cos, freqs_sin)

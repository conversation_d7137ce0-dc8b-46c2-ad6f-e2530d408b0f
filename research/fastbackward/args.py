"""All arguments to fastbackward training jobs.

This file is a stop-gap to bridge us to "proper" configuration with dataclasses.
Any code that needs the command line args can simply import the `args` module. On first
import, `configurator.py` will read `sys.argv` to update the (global) arg values.
"""

import os

import torch

# pylint: disable=invalid-name; This file defines configuration variables which Pylint
# recognizes as constants.

# === Start of arguments ===

# I/O
out_dir: str = "out"
"""Directory to save output to. Ignored when running on Determined."""
run_name: str = ""
"""REQUIRED. Used to name the output directory and Determined experiment."""
eval_interval = 500
"""If <= 0, then don't evaluate."""
log_interval = 1
"The logging interval for the training iterations."
eval_log_interval = 200
"The logging interval for the evaluation job."
wandb_log = True
"""DEPRECATED and no effect. Set wandb_project to None to disable logging."""
wandb_project = "llama2-7b-finetune"
"""If None, no wandb logging."""
wandb_group = ""  # the group name

# Data and evaluation
train_data_path = ""
"""REQUIRED. Path to the training dataset."""
cross_shuffle: bool = True
"""Whether to shuffle across dataset boundaries for ConcatDataset input."""
data_sampler_rand_seed: int = 0
"""The random seed for the data sampler."""
eval_data_path = ""
"""Path to the evaluation dataset. If empty, there will be no evaluation."""
eval_items = 0
"""Number of items to use for evaluation. If 0, then use all items."""
continuous_harness_evals = ""
"""Semi-colon-separated list of config files or glob patterns for continuous evaluation.

Fastbackward will parse the patterns to a list of config files and pass them to the
Augment evaluation harness for continuous evaluation at the same frequency as the
PyTorch-based evaluation (i.e. every `eval_interval` steps).

If empty, then no continuous harness evaluation will be performed.
"""
model_name = "fastbackward_starcoder"
"""Registered model name for continuous harness evaluation.

This paramter is only for continuous harness evaluation. Contrary to the name's
implication, this paramter mainly sets the tokenizer type.
"""
visualize_logits_samples = 0
"""Number of samples to visualize logits for.

Visualization html will be created if this is > 0. We only log this for evaluation
datasets. This also specifies the maximum number of samples to log for each evaluation
dataset.
"""
tokenizer_name = ""
"""Name of tokenizer to use for visualization.

REQUIRED if `visualize_logits_samples` > 0. Valid names may change over time. You can
obtain it through
```python
from base.tokenizers import list_tokenizers
list_tokenizers()
```
The above example currently returns
`['deepseek_coder_base', 'deepseek_coder_instruct', 'starcoder', 'rogue', 'fim']`
where `"fim"` is codegen tokenizer.
"""

use_research_tokenizer = False
"""Whether to use a research tokenizer instead of a base tokenizer."""

# Model
n_layers = 12
n_heads = 12
n_kv_heads = 0
"""Number of KV heads.

0 for multi-head attention. 1 for multi-query attention. Else for grouped query
attention.
"""
n_embd = 768
"""Hidden dimension."""
ffn_dim_multiple_of = 256
ffn_dim_multiplier = 1.0
norm_eps = 1e-5
rope_theta = 10000.0
rope_scaling_factor = 1.0
model_vocab_size = 32000
llama = None
"""DEPRECATED. Setting this flag leads to an error."""
starcoder = None
"""DEPRECATED. Setting this flag leads to an error."""
ffn_type = "glu"
"""One of "glu" or "mlp"."""
bias = "none"
"""One of "none" or "attn_mlp"."""
norm_type = "rmsnorm"
"""One of "rmsnorm" or "layernorm"."""
pos_embed_type = "rope"
"""One of "rope" or "absolute"."""
rotary_config = None
attn_config = None
ffn_config = None
first_layer_ffn_config = None
"""See an example of how to set up them in deepseek_coder_v2_lite.py."""

# Training
batch_size = 12
"""Per-device batch size (in number of sequences)."""
block_size = 1024
"""Training sequence length."""
gradient_accumulation_steps = 1
"""Number of forward-backward passes per step of gradient update.

This parameter increases the effective batch size and does not affect the number of
gradient update steps.
"""
learning_rate = 6e-4
"""Max learning rate."""
max_iters: int = 0
"""Total number of training iterations."""
max_epochs: int = 0
"""Alternative way to specify `max_iters`. Cannot be used with `max_iters > 0`."""
weight_decay = 1e-1
beta1 = 0.9
beta2 = 0.95
grad_clip = 1.0
"""Clip gradients at this value, or disable if == 0.0."""
decay_lr = True
"""Whether to decay the learning rate."""
warmup_iters = 2000
"""Number of learning rate warmup iterations."""
lr_decay_iters = 0
"""Defaults to `max_iters`."""
min_lr = 6e-5
"""Minimum learning rate, should be ~= learning_rate/10 per Chinchilla."""
loss_function = "cross_entropy"
"""
One of "cross_entropy" or "sequence_chunked_cross_entropy".
"""
loss_mask_policy = "negative_tokens"
"""Type of loss mask.

One of "negative_tokens", "fim", "ignore_list", where:
- if "negative_tokens", then we mask loss on negative token IDs;
- if "fim", then we only compute loss for tokens after `fim_middle_token_id` token and
  before `eot_token_id`;
- if "ignore_list", then we ignore any token ids in the provided list.
"""
# The defaults below are from StarCoder
fim_middle_token_id = 2
eot_token_id = 0
pad_token_id = 49152
ignore_list = ()

# Checkpointing
checkpoint = ""
"""Local directory or Determined storage ID."""
checkpoint_dir = ""
"""DEPRECATED. Use `checkpoint` instead."""
hf_checkpoint_dir = ""
checkpoint_optimizer_state = False

restore_optimizer_state_from_checkpoint = False
"""Restore optimizer state from checkpoint.

Iteration count will be restored when either this
or restore_training_metadata_from_checkpoint is True.
"""

restore_training_metadata_from_checkpoint = False
"""Restore training metadata such as total token count and masked token count.

Restoring metadata without restoring optimizer state is useful when changing
hyperparameters mid run, for example increasing batch sizes, where we do not
want to reuse optimizers but do want to keep the tracking statistics consistent
with the previous run.

Iteration count will also be restored.
"""

# Lifecycle hooks
determined_master_url = ""
"""External URL of Determined master; automatically set in launch.py."""
trial_entry_notify: bool = False
"""Notify on trial entry."""
trial_exit_notify: bool = False
"""Notify on trial exit."""
post_iter_notify: bool = False
"""Notify after each iteration."""
slack_user = ""
"""Slack user to notify."""
slack_token_path = "/run/determined/secrets/cw-bot-token/token"
"""Path to slack token."""

# data syncing, comma separated list
additional_sync_items = ""

## Note(Xuanyi): The following parameters are all used for RLHF experiments (either the reward model training or policy model training)
sft_collate_strategy = "none"
"""The indicator of how to prepare the data for SFT training (usually used for reward model training)"""
sft_dropout_attn: float = 0.0
rl_ref_checkpoint = ""
"""Local directory or Determined storage ID for the reference model."""
rl_reward_checkpoint = ""
"""Local directory or Determined storage ID for the reference model."""
rl_llm_eval_data_path = ""
"""Path to the data for LLM's downstream evaluation."""
rl_eval_interval: int = 0
"""Interval to evaluate the policy model w.r.t. the RL metrics."""
rl_dpo_ce_loss_coeff = 0.0
rl_dpo_beta = 0.1
rl_dpop_lambda = 0.0
rl_label_smoothing = 0.0
rl_num_pairs_per_prompt = 1
rl_max_generation_steps = 0
rl_online_rl = True
rl_use_ipo = False
rl_sync_ref_weights_per_iter = 0
rl_ckp_save_interval = 0
"""Interval to save checkpoints."""

# System
dtype = (
    "bfloat16"
    if torch.cuda.is_available() and torch.cuda.is_bf16_supported()
    else "float32"
)
model_parallel_size = 1
use_sequence_parallel = False
multi_node_service_name = ""
use_activation_checkpointing = True
determined_enabled = False

# === End of arguments ===

# Save current set of config keys to later use for `full_config()`
config_keys = [
    k
    for k, v in globals().items()
    if not k.startswith("_") and isinstance(v, (int, float, bool, str))
]

# Load config files and --k=v values from cmd line
exec(open("configurator.py", encoding="utf-8").read())  # pylint: disable=exec-used

# Hack to handle old-style multi-node launch
if not multi_node_service_name and "FASTBACKWARD_SERVICE_NAME" in os.environ:
    multi_node_service_name = os.environ["FASTBACKWARD_SERVICE_NAME"]


def full_config():
    """Returns a dictionary representing the full config."""
    return {k: globals()[k] for k in config_keys}


# If you need to manually add something to args not listed above
def add_arg(key: str, value):
    """Adds a new arg to the args namespace."""
    if key in globals():
        raise ValueError(
            f"Tried to add {key} to fastbackward args, but it is already present."
        )
    globals()[key] = value
    config_keys.append(key)

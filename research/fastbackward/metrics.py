"""Metrics for fastbackward."""

import logging
import math
from abc import ABC, abstractmethod
from collections import defaultdict
from typing import Callable

import torch
import torch.nn.functional as F
from torch import distributed as dist

import research.fastbackward.fs_model_parallel as mpu
from research.fastbackward import losses
from research.fastbackward.data import IGNORE_LABEL, unmask_token
from research.fastbackward.distributed import dist_argmax

MetricFn = Callable[[torch.Tensor, torch.Tensor, torch.Tensor], "BaseAggregator"]
"""A function that computes a metric from model output and target.

Args:
    X: The input tokens. Shape (batch, seq_len)
    Y: The target tokens to predict. Shape (batch, seq_len)
    Y^: The output of the model. Shape (batch, seq_len)

Returns:
    A metric aggregator that can be used to aggregate metrics over batches.
"""


class BaseAggregator(ABC):
    """Base class for aggregating values with different statistics."""

    @abstractmethod
    def observe(self, value: float) -> None:
        """Add a new value to the aggregator."""
        pass

    @abstractmethod
    def join(self, other: "BaseAggregator") -> None:
        """Combine this aggregator with another one."""
        pass

    @abstractmethod
    def get(self) -> float | None:
        """Get the aggregated value."""
        pass


class Mean(BaseAggregator):
    """Aggregates values by computing mean."""

    def __init__(self):
        super().__init__()
        self.sum = 0.0
        self.count = 0

    def observe(self, value: float) -> None:
        self.sum += value
        self.count += 1

    def join(self, other: "BaseAggregator") -> None:
        assert isinstance(other, Mean)
        self.sum += other.sum
        self.count += other.count

    def get(self) -> float | None:
        """Get the mean of all observed values."""
        return self.sum / self.count if self.count > 0 else None


class Max(BaseAggregator):
    """Aggregates values by computing maximum."""

    def __init__(self):
        super().__init__()
        self.current_max = None

    def observe(self, value: float) -> None:
        if self.current_max is None or value > self.current_max:
            self.current_max = value

    def join(self, other: "BaseAggregator") -> None:
        assert isinstance(other, Max)
        if other.current_max is not None:
            if self.current_max is None or other.current_max > self.current_max:
                self.current_max = other.current_max

    def get(self) -> float | None:
        """Get the maximum of all observed values."""
        return self.current_max


class Percentiles(BaseAggregator):
    """Aggregates values by computing percentiles.

    Warning: records all values, so may be memory intensive.
    """

    def __init__(self):
        super().__init__()
        self.values = []

    def observe(self, value: float) -> None:
        self.values.append(value)

    def join(self, other: "BaseAggregator") -> None:
        assert isinstance(other, Percentiles)
        self.values.extend(other.values)

    def get(self, percentile: float = 0.5) -> float | None:
        """Get the percentile of all observed values.

        Returns median by default.
        """
        if not self.values:
            return None
        sorted_values = sorted(self.values)
        idx = int(len(sorted_values) * percentile)
        return sorted_values[idx]


class Variance(BaseAggregator):
    """Aggregates values by computing variance.

    Warning: records all values, so may be memory intensive.
    """

    def __init__(self):
        super().__init__()
        self.values = []

    def observe(self, value: float) -> None:
        self.values.append(value)

    def join(self, other: "BaseAggregator") -> None:
        assert isinstance(other, Variance)
        self.values.extend(other.values)

    def get(self) -> float | None:
        """Get the variance of all observed values."""
        if not self.values:
            return None
        mean = sum(self.values) / len(self.values)
        return sum((x - mean) ** 2 for x in self.values) / len(self.values)


def _get_bucket(value: float) -> float:
    """Cast a value to a bucket, so that all values within a certain range map to the same bucket."""
    if value in [float("inf"), float("-inf"), 0.0]:
        return value
    return 10 ** round(math.log10(abs(value)), ndigits=3) * math.copysign(1, value)


class ApproximatePercentiles(BaseAggregator):
    """Aggregates values by computing approximate percentiles using bucket counting.

    Memory efficient: uses log(num_observations) memory, but with a relatively large constant.
    Accuracy: within 1% of true percentile value.
    """

    def __init__(self, max_num_buckets: int = 100_000):
        """Initialize buckets for tracking percentiles.

        Args:
            num_buckets: Number of buckets to use for approximation
        """
        super().__init__()
        self.max_num_buckets = max_num_buckets
        self.count = 0
        self.buckets = defaultdict(int)

    def observe(self, value: float) -> None:
        """Add a new observation to appropriate bucket."""
        quantized_value = _get_bucket(value)
        self.buckets[quantized_value] += 1
        self.count += 1
        if len(self.buckets) > self.max_num_buckets:
            raise ValueError(
                "Too many buckets required for metrics; potential for memory issues."
            )

    def join(self, other: "BaseAggregator") -> None:
        """Merge two approximate percentile trackers."""
        assert isinstance(other, ApproximatePercentiles)
        for bucket, count in other.buckets.items():
            self.buckets[bucket] += count
        self.count += other.count

    def get(self, percentile: float = 0.5) -> float | None:
        """Get the approximate percentile value.

        Args:
            percentile: The desired percentile (between 0 and 1)

        Returns:
            Approximate value at the specified percentile
        """
        if self.count == 0:
            return None
        target_count = self.count * percentile
        current_count = 0
        for bucket in sorted(self.buckets.keys()):
            current_count += self.buckets[bucket]
            if current_count >= target_count:
                return bucket
        assert False, "Inconsistency between self.count and self.buckets"


class GatherDataParallelLossFn(losses.LossFn):
    """Wraps a loss function to gather the loss across all processes."""

    def __init__(self, loss_fn: losses.LossFn):
        self.loss_fn = loss_fn

    def __call__(
        self,
        input_tokens: torch.Tensor,
        target_tokens: torch.Tensor,
        model_output: torch.Tensor,
    ) -> torch.Tensor:
        values = self.loss_fn(input_tokens, target_tokens, model_output).detach()
        if values.ndim == 0:
            values = values.unsqueeze(0)
        assert values.ndim == 1
        if mpu.get_data_parallel_world_size() > 1:
            assert values.is_cuda, f"{values.device=} {mpu.get_data_parallel_group()=}"
            gathered_values = [
                torch.zeros_like(values)
                for _ in range(mpu.get_data_parallel_world_size())
            ]
            dist.all_gather(
                gathered_values, values, group=mpu.get_data_parallel_group()
            )
            values = torch.cat(gathered_values, dim=0)
        assert values.ndim == 1
        return values


@torch.no_grad()
def loss_fn_as_metric_fn(
    loss_fn: losses.LossFn,
    aggregator_cls: type[BaseAggregator] = Mean,
) -> MetricFn:
    """Converts a loss function to a metric function."""

    def metric_fn(
        x: torch.Tensor, y: torch.Tensor, y_hat: torch.Tensor
    ) -> BaseAggregator:
        values = loss_fn(x, y, y_hat).detach()
        if values.ndim == 0:
            values = values.unsqueeze(0)
        assert values.ndim == 1
        aggregator = aggregator_cls()
        for value in values:
            aggregator.observe(value.item())
        return aggregator

    return metric_fn


def get_cross_entropy_mean() -> MetricFn:
    """Returns cross entropy loss as a metric."""
    return loss_fn_as_metric_fn(GatherDataParallelLossFn(losses.cross_entropy_loss))


class ExactMatchRate(losses.LossFn):
    @torch.no_grad()
    def __call__(
        self,
        input_tokens: torch.Tensor,
        target_tokens: torch.Tensor,
        logits: torch.Tensor,
    ) -> torch.Tensor:
        """Computes the fraction of examples that are exactly correct.

        This assumes greedy decoding and will ignore any negatively valued tokens.

        Args:
            input_tokens: The input tokens (will be ignored, but required for compatibility with other losses.)
            y: The target tokens as a 2-D tensor (batch, seq_len)
            y_hat: The predicted tokens as a 3-D tensor (batch, seq_len, vocab_size)

        Returns:
            A tensor with shape (batch,) indicating for each example whether it was exactly correct (1.0) or not (0.0).
        """
        del input_tokens  # unused

        if mpu.get_model_parallel_world_size() > 1:
            greedy_pred = dist_argmax(
                logits, dim=-1, group=mpu.get_model_parallel_group()
            )
        else:
            greedy_pred = logits.argmax(dim=-1)

        is_ignored = target_tokens < 0
        is_correct = greedy_pred == target_tokens
        # consider all ignored tokens as correct
        is_correct[is_ignored] = True
        # an example is correct iff all tokens are correct
        example_correct = is_correct.all(dim=-1)
        assert example_correct.shape == (target_tokens.size(0),)
        return example_correct.float()


class FimExactMatchRate(losses.LossFn):
    """Computes the exact match rate of the tokens after fim_middle.

    The returned loss function additionally masks out all tokens before fim_middle
    before computing the exact-match rate. As a result, it will always return 1 for
    sequences that do not contain a fim_middle token.

    Not backpropagatable.
    """

    def __init__(self, fim_middle_id: int):
        self.fim_middle_id = fim_middle_id

    @torch.no_grad()
    def __call__(
        self,
        input_tokens: torch.Tensor,
        target_tokens: torch.Tensor,
        model_output: torch.Tensor,
    ) -> torch.Tensor:
        is_fim_middle = unmask_token(input_tokens) == self.fim_middle_id
        is_before_middle = torch.cumsum(is_fim_middle, -1) == 0
        target_tokens = target_tokens.clone()
        # mask out all target tokens before the fim middle
        target_tokens[is_before_middle] = IGNORE_LABEL
        return ExactMatchRate()(input_tokens, target_tokens, model_output)


class TokenAccuracy(losses.LossFn):
    @torch.no_grad()
    def __call__(
        self,
        input_tokens: torch.Tensor,
        target_tokens: torch.Tensor,
        logits: torch.Tensor,
    ) -> torch.Tensor:
        """Computes the token accuracy for the predictions and the targets.

        - Ignores tokens with negative values.
        - Computes the accuracy for each example independently before averaging.
        - Removes examples where all tokens are ignored. This is to avoid
        the final batch of the dataset pulling the average down or up, depending
        on the batch size.

        Args:
            input_tokens: Will be ignored, but required for compatibility with other losses.
            target_tokens: A 2-D tensor (batch, seq_len) of type torch.int32.
            logits: The predicted tokens as a 3-D tensor (batch, seq_len, vocab_size)

        Returns:
            A tensor with shape (<=batch,) containing the token accuracy for each example.
            Examples where all tokens are ignored are removed.
        """
        del input_tokens  # unused
        if mpu.get_model_parallel_world_size() > 1:
            greedy_pred = dist_argmax(
                logits, dim=-1, group=mpu.get_model_parallel_group()
            )
        else:
            greedy_pred = logits.argmax(dim=-1)

        is_correct = (greedy_pred == target_tokens).float()
        is_ignored = target_tokens < 0
        is_correct[is_ignored] = 0
        num_is_valid = float(is_ignored.logical_not().sum())

        if num_is_valid == 0:
            num_is_valid += 1e-6
            logging.warning("All tokens in this batch were ignored.")
        reweight_factor = 1.0
        if mpu.get_data_parallel_world_size() > 1:
            # Reweight the result of batch using the number of valid tokens
            # across all tokens across all data parallel processes. This
            # prevents unequal batch sizes from pulling the average down or up.
            num_is_valid_global = torch.tensor(
                [num_is_valid], device=logits.device, dtype=torch.float32
            )
            dist.all_reduce(
                num_is_valid_global,
                op=dist.ReduceOp.SUM,
                group=mpu.get_data_parallel_group(),
            )
            num_is_valid_global = num_is_valid_global.item()
            assert num_is_valid_global > 0, f"Invalid {num_is_valid_global=}"
            reweight_factor = (
                num_is_valid / num_is_valid_global * mpu.get_data_parallel_world_size()
            )
            logging.debug(
                "Reweighting loss by %s (num_is_valid=%s, num_is_valid_global=%s)",
                reweight_factor,
                num_is_valid,
                num_is_valid_global,
            )
        return is_correct.sum() / num_is_valid * reweight_factor


def get_token_accuracy_mean() -> MetricFn:
    """Returns a metric function that computes token accuracy."""
    return loss_fn_as_metric_fn(GatherDataParallelLossFn(TokenAccuracy()))


def get_exact_match_mean() -> MetricFn:
    """Returns a metric function that computes exact match rate."""
    return loss_fn_as_metric_fn(GatherDataParallelLossFn(ExactMatchRate()))


def get_fim_exact_match_mean(fim_middle_id: int) -> MetricFn:
    """Returns a metric function that computes FIM exact match rate."""
    return loss_fn_as_metric_fn(
        GatherDataParallelLossFn(FimExactMatchRate(fim_middle_id))
    )

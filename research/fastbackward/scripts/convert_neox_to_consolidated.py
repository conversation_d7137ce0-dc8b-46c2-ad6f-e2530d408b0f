"""Simple script to convert neox StarCoder checkpoints to the consolidated format.

Usage:
    $ python3 convert_neox_to_consolidated.py <checkpoint_dir> <output_dir>
"""

import argparse
import dataclasses
import json
from pathlib import Path

import torch

from research.fastbackward.checkpointing.neox import (
    get_starcoder_neox_args,
    load_starcoder_neox_state_dict,
)
from research.fastbackward.model import ModelArgs


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "checkpoint_path", type=Path, help="Path to the checkpoint dir."
    )
    parser.add_argument(
        "output_path", type=Path, help="Where to write the consolidated checkpoint to."
    )

    args = parser.parse_args()

    model_args = get_starcoder_neox_args(args.checkpoint_path)

    args.output_path.mkdir(parents=True, exist_ok=True)
    (args.output_path / "params.json").write_text(
        json.dumps(ModelArgs.schema().dump(model_args))
    )
    print(model_args)

    mp_size = len(list(args.checkpoint_path.glob("layer_00-model_*-model_states.pt")))
    print(f"Model is {mp_size=}.")

    for mp_rank in range(mp_size):
        print(f"Loading model parallel rank {mp_rank:02d}...")
        state_dict = load_starcoder_neox_state_dict(
            args.checkpoint_path, mp_rank=mp_rank, mp_size=mp_size
        )
        torch.save(
            state_dict,
            args.output_path / f"consolidated.{mp_rank:02d}.pth",
        )
        del state_dict

    print("Done.")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
This script consolidates model-parallel **retriever** checkpoints a non-model-parallel checkpoint.
This is different from `resize_model_parallel_checkpoint.py` because retriever checkpoints
are not in the same format as model checkpoints.
For resizing an LM checkpoint, use `resize_model_parallel_checkpoint.py`.
"""

import argparse
import json
import shutil
from pathlib import Path

import torch

from research.fastbackward.retrieval_models import (
    load_embedder_from_checkpoint,
    TransformerEmbedder,
)
from research.fastbackward.checkpointing.utils import (
    merge_model_parallel_consolidated_checkpoints,
    get_model_weights_mp_split,
    stack_weights,
)
from research.fastbackward.model import ModelArgs, correct_model_args


def load_model_parallel_checkpoints(checkpoint_dir: Path) -> list[dict]:
    """Load model parallel checkpoints and model args from a directory.

    Args:
        checkpoint_dir: Directory containing the checkpoints and params.json

    Returns:
        Tuple of (model_args, list of state dicts)
    """
    # Load all model parallel checkpoints
    checkpoints = sorted(checkpoint_dir.glob("consolidated.*.pth"))
    if not checkpoints:
        raise ValueError(f"No checkpoints found in {checkpoint_dir}")

    print(f"Loading {len(checkpoints)} model parallel checkpoints...")
    return [
        torch.load(
            checkpoint_dir / f"consolidated.{mp_rank:02d}.pth",
            map_location=torch.device("cpu"),
        )
        for mp_rank in range(len(checkpoints))
    ]


def main():
    parser = argparse.ArgumentParser(
        description="Consolidate model parallel checkpoints"
    )
    parser.add_argument(
        "--input-dir",
        type=Path,
        required=True,
        help="Directory containing model parallel checkpoints",
    )
    parser.add_argument(
        "--output-dir",
        type=Path,
        required=True,
        help="Output directory for consolidated checkpoint",
    )

    args = parser.parse_args()

    model_config = json.loads((args.input_dir / "params.json").read_text())
    lm_args = ModelArgs.load_from_dict(
        model_config["model/query_model/language_model"]["params"]
    )

    # Create output directory
    args.output_dir.mkdir(parents=True, exist_ok=True)

    # Load checkpoints and model args
    state_dicts = load_model_parallel_checkpoints(args.input_dir)
    print("Loaded all checkpoints, merging...")
    keys = set(state_dicts[0].keys())

    # Merge the checkpoints
    model_args = correct_model_args(lm_args)
    mp_world_size = len(state_dicts)
    consolidated_weights = {}

    for prefix in ["model.query_model.lm.", "model.doc_model.lm."]:
        for key, dim, replication_size in get_model_weights_mp_split(
            model_args, mp_world_size
        ):
            if key.startswith("output"):
                continue  # skip, not in a retriever
            key = prefix + key
            consolidated_weights[key] = stack_weights(
                state_dicts, key, dim, replication_size
            )
            print(f"Merged {key}")

    for key in keys.difference(consolidated_weights):
        consolidated_weights[key] = state_dicts[0][key]
        print(f"Merged {key}")

    print("Saving consolidated weights")
    torch.save(consolidated_weights, args.output_dir / "consolidated.00.pth")

    # Copy params.json and other metadata files
    for json_file in args.input_dir.glob("*.json"):
        shutil.copy(json_file, args.output_dir)

    print(f"Successfully consolidated checkpoint saved to {args.output_dir}")


if __name__ == "__main__":
    main()

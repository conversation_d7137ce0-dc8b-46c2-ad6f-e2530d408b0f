"""
Simple script to resize a model parallel world.

This script is designed to resize a model parallel world by stacking and splitting
weights from a directory of model parallel checkpoints. It is used to adjust the
number of model parallel ranks in a distributed training setup.

Usage:
    $ python3 resize_model_parallel_world.py <checkpoint_dir> <output_dir>
"""

import argparse
import json
import shutil
from pathlib import Path

import torch

from research.fastbackward.checkpointing.utils import (
    merge_model_parallel_consolidated_checkpoints,
    split_consolidated_checkpoint,
)
from research.fastbackward.model import ModelArgs


def load_consolidated_checkpoint(model_args: ModelArgs, path: Path):
    """
    Loads a consolidated checkpoint from a directory of model parallel checkpoints.

    Args:
        model_args: The model arguments containing the number of layers.
        path: The directory containing the model parallel checkpoints.

    Returns:
        A dictionary containing the consolidated weights.
    """
    checkpoints = sorted(path.glob("consolidated.*.pth"))
    if not checkpoints:
        raise ValueError(f"No checkpoints found in {path}")

    weights = [torch.load(checkpoint, map_location="cpu") for checkpoint in checkpoints]
    return merge_model_parallel_consolidated_checkpoints(model_args, weights)


def split_and_save_checkpoint(
    model_args: ModelArgs,
    consolidated_weights: dict[str, torch.Tensor],
    output_path: Path,
    mp_world_size: int,
    mp_rank: int,
):
    """
    Splits a consolidated checkpoint and saves it to a specified output path.

    Args:
        model_args: The model arguments containing the number of layers.
        consolidated_weights: A dictionary containing the consolidated weights.
        output_path: The directory to save the split checkpoints to.
        mp_world_size: The total number of model parallel ranks.
        mp_rank: The current model parallel rank.
    """
    dst_weights = split_consolidated_checkpoint(
        model_args, consolidated_weights, mp_world_size, mp_rank
    )
    torch.save(dst_weights, output_path / f"consolidated.{mp_rank:02d}.pth")


def main():
    """Main function to parse arguments and resize the model parallel world."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "checkpoint_path", type=Path, help="Path to the checkpoint dir."
    )
    parser.add_argument(
        "output_path",
        type=Path,
        help="Where to write the resized checkpoint to.",
    )
    parser.add_argument(
        "--model_parallel_size",
        type=int,
        required=True,
        help="The desired model parallel size of the checkpoint.",
    )

    args = parser.parse_args()

    with (args.checkpoint_path / "params.json").open() as fh:
        model_args = ModelArgs.load_from_dict(json.load(fh))

    # Create directory for target checkpoint.
    args.output_path.mkdir(parents=True, exist_ok=True)

    for json_file in args.checkpoint_path.glob("*.json"):
        shutil.copy(json_file, args.output_path)

    consolidated_checkpoint = load_consolidated_checkpoint(
        model_args, args.checkpoint_path
    )
    print("Loaded and consolidated checkpoint.")

    for mp_rank in range(args.model_parallel_size):
        split_and_save_checkpoint(
            model_args,
            consolidated_checkpoint,
            args.output_path,
            args.model_parallel_size,
            mp_rank,
        )
    print("Done.")


if __name__ == "__main__":
    main()

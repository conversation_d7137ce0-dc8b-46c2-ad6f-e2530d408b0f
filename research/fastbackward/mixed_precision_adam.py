"""Custom Adam implementation for efficient distributed updates with mixed precision training."""

import logging
from typing import Any, Iterable

import torch
import torch.distributed

import research.fastbackward.fs_model_parallel as mpu

MAX_PARAM_NUMEL = 100_000_000


class MixedPrecisionAdamW:
    """Adam optimizer class.

    Note that this does _not_ implement the torch.nn.Optimizer interface.
    """

    def __init__(
        self,
        params_grads_options: Iterable[
            tuple[torch.Tensor, torch.Tensor, dict[str, Any]]
        ],
        lr=0.001,
        betas=(0.9, 0.999),
        eps=1e-08,
        weight_decay=0.01,
    ):
        try:
            from amp_C import multi_tensor_adam_capturable_master
            from apex.multi_tensor_apply import multi_tensor_applier

            self.use_apex = True
            self.multi_tensor_adam_capturable_master = (
                multi_tensor_adam_capturable_master
            )
            self.multi_tensor_applier = multi_tensor_applier
        except ImportError:
            self.use_apex = False
            self.multi_tensor_adam_capturable_master = None
            self.multi_tensor_applier = None

        if not self.use_apex:
            logging.warning("Could not import apex features for MixedPrecisionAdamW.")
        self.dummy_no_overflow = torch.zeros((1,), device="cuda", dtype=torch.int32)
        self.float_one = torch.ones((1,), device="cuda", dtype=torch.float32)
        self.int_one = torch.ones((1,), device="cuda", dtype=torch.int32)
        self.param_groups = []

        for param, grad, options in params_grads_options:
            m, v, w32 = [torch.zeros_like(param, dtype=torch.float32) for _ in range(3)]
            step = torch.zeros(1, dtype=torch.int, device=param.device)
            with torch.no_grad():
                w32.copy_(param)
            # Don't let any param we pass to the adam kernel get bigger than MAX_PARAM_NUMEL. This
            # has mild speed benefits, but it's most to avoid issues around 32-bit integer indexing
            # in that kernel overflowing when a param has more than 2^31 elements.
            if param.numel() > MAX_PARAM_NUMEL:
                param_list = param.split(MAX_PARAM_NUMEL)
                grad_list = grad.split(MAX_PARAM_NUMEL)
                m_list = m.split(MAX_PARAM_NUMEL)
                v_list = v.split(MAX_PARAM_NUMEL)
                w32_list = w32.split(MAX_PARAM_NUMEL)
            else:
                param_list = [param]
                grad_list = [grad]
                m_list = [m]
                v_list = [v]
                w32_list = [w32]
            group = {
                "params": param_list,
                "grads": grad_list,
                "m": m_list,
                "v": v_list,
                "step": step,
                "step_cpu": 0,
                "w32": w32_list,
            } | options

            # Make extra sure that the learning rate is a tensor
            if "lr" not in group:
                group["lr"] = torch.tensor(lr, dtype=torch.float32, device=param.device)
            else:
                old_lr = group["lr"]
                group["lr"] = torch.tensor(0, dtype=torch.float32, device=param.device)
                group["lr"].copy_(old_lr)
            self.param_groups.append(group)
        self.default_betas = betas
        self.default_eps = eps
        self.default_weight_decay = weight_decay

    def step(self, overflow_flag=None, inverse_scale=None, lr_scheduler=None):
        if overflow_flag is None:
            overflow_flag = self.dummy_no_overflow
        if inverse_scale is None:
            inverse_scale = self.float_one

        for group in self.param_groups:
            assert isinstance(group["lr"], torch.Tensor)
            step = group["step"]
            step += self.int_one
            group["step_cpu"] += 1

            if lr_scheduler is not None:
                group["lr"].copy_(lr_scheduler.get_lr(group["step_cpu"]))

            beta1, beta2 = group.get("betas", self.default_betas)
            if self.use_apex:
                assert self.multi_tensor_adam_capturable_master
                assert self.multi_tensor_applier
                self.multi_tensor_applier(
                    self.multi_tensor_adam_capturable_master,
                    overflow_flag,  # if == 1, then do a skip
                    [
                        group["grads"],
                        group["params"],
                        group["m"],
                        group["v"],
                        group["w32"],
                    ],
                    group["lr"],
                    beta1,
                    beta2,
                    group.get("eps", self.default_eps),
                    step,
                    1,  # adam_w_mode = True
                    1,  # bias_correction = True
                    group.get("weight_decay", self.default_weight_decay),
                    inverse_scale,  # inverse_scale -- ie, thing to multiply grads by to get the correct answer
                )
            else:
                # By-hand implementation of the AdamW weight update. This is a _slow_ fallback path.
                if overflow_flag.item() != 0:
                    continue
                grads = group["grads"]
                if inverse_scale is not None:
                    # TODO: why does this need to be out-of-place?
                    grads = tuple(g * inverse_scale for g in grads)
                params = group["params"]
                m = group["m"]
                v = group["v"]
                w32 = group["w32"]
                lr = group["lr"].item()
                decay = group.get("weight_decay", self.default_weight_decay)
                eps = group.get("eps", self.default_eps)
                stepval = group["step_cpu"]
                # All the groups are broken into groups of MAX_PARAM_NUMEL elements.
                # Iterate through them.
                for i, params_i in enumerate(params):
                    grads_i, w32_i = grads[i], w32[i]
                    m_i, v_i = m[i], v[i]
                    w32_i -= (lr * decay) * w32_i
                    m_i = beta1 * m_i + (1 - beta1) * grads_i
                    v_i = beta2 * v_i + (1 - beta2) * (grads_i * grads_i)
                    mhat = m_i / (1 - beta1**stepval)
                    vhat = v_i / (1 - beta2**stepval)
                    w32_i -= lr * mhat / (torch.sqrt(vhat) + eps)
                    params_i.copy_(w32_i)

    def parallel_state_dict(self):
        groups = []
        for pg in self.param_groups:
            state = dict(
                m=pg["m"],
                v=pg["v"],
                w32=pg["w32"],
                step=pg["step"],
            )
            groups.append(state)
        return dict[str, Any](
            param_groups=groups,
            world_size=torch.distributed.get_world_size(),
            model_parallel_size=mpu.get_model_parallel_world_size(),
            rank=torch.distributed.get_rank(),
        )

    def load_parallel_state_dict(self, state_dict):
        assert torch.distributed.get_world_size() == state_dict["world_size"]
        assert mpu.get_model_parallel_world_size() == state_dict["model_parallel_size"]
        assert torch.distributed.get_rank() == state_dict["rank"]
        groups = state_dict["param_groups"]
        assert len(groups) == len(self.param_groups)
        for src_pg, dst_pg in zip(groups, self.param_groups):
            for k in ["m", "v", "w32"]:
                src_lst = src_pg[k]
                dst_lst = dst_pg[k]
                assert len(src_lst) == len(dst_lst)
                for src_tensor, dst_tensor in zip(src_lst, dst_lst):
                    dst_tensor.copy_(src_tensor)
            dst_pg["step"].copy_(src_pg["step"])
            dst_pg["step_cpu"] = src_pg["step"].item()

    def zero_grad(self, set_to_none=True):  # pylint: disable=W0613
        for group in self.param_groups:
            group["grads"].zero_()

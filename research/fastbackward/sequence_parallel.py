# pylint: disable=abstract-method,arguments-differ
"""Utility functions to support sequence parallelism.

See README-parallelism.md for a general description of sequence parallelism.

A further wrinkle beyond what is described in the doc is that "sequence parallel" really means
"token parallel", since there is nothing in the "sequence parallel region" that cares about
sequence position or batching; you are simply splitting all the tokens (batch * seqlen) into K
chunks (for model_parallel_size=K).

Splitting purely on the sequence dimension is bad, since it's not the leading dimension, so you
spend a lot of time copying and transposing activations to get contiguous slices when batch_size>1.

The solution is to combine the batch and seqlen dimensions into a single dimension. When you enter
a sequence parallel region, you treat the activations as having size (batch*seqlen, ...) rather
than (batch, seqlen, ...). At which point you can freely split them into
(batch*seqlen // mp_size, ...)-sized chunks.

The difficulty with doing so is that _leaving_ the sequence parallel region, you all_gather a
tensor of size (batch*seqlen, ...), but you have lost the information to reshape back to
(batch, seqlen, ...). The solution is the context manager `sequence_parallel_context` below.
"""

from contextlib import contextmanager
from dataclasses import dataclass

import torch
import torch.distributed as dist

import research.fastbackward.fs_model_parallel as mpu


@dataclass
class _SPContext:
    batch_size: int
    seqlen: int
    sp_group: dist.ProcessGroup
    enabled: bool


_cur_context: _SPContext | None = None


@contextmanager
def sequence_parallel_context(
    batch_size: int,
    seqlen: int,
    sp_group: dist.ProcessGroup | None = None,
    enabled=True,
):
    """Context manager to set up sequence parallel metadata for any portion of model execution."""
    global _cur_context
    if _cur_context is not None:
        raise RuntimeError(
            "sequence_parallel_context is already active. Consider using `reenter_context`."
        )
    if sp_group is None:
        sp_group = mpu.get_model_parallel_group()
    _cur_context = _SPContext(batch_size, seqlen, sp_group, enabled)
    try:
        yield _cur_context
    finally:
        _cur_context = None


@contextmanager
def reenter_context(sp_ctx: _SPContext):
    """Variant of `sequence_parallel_context` that lets you re-use an existing context."""
    global _cur_context
    old_context = _cur_context
    _cur_context = sp_ctx
    try:
        yield sp_ctx
    finally:
        _cur_context = old_context


def all_gather_dim0(x: torch.Tensor, sp_group: dist.ProcessGroup):
    """Helper function to all_gather `x` along its first dimension."""
    group_size = dist.get_world_size(group=sp_group)
    if dist.get_backend(group=sp_group) == dist.Backend.NCCL:
        result_size = list(x.size())
        result_size[0] *= group_size
        result = torch.empty(result_size, dtype=x.dtype, device=x.device)
        dist.all_gather_into_tensor(result, x, group=sp_group)
    else:
        # Only NCCL supports `all_gather_into_tensor`, so fall back to a vanilla `all_gather`.
        gather_list = [torch.empty_like(x) for _ in range(group_size)]
        dist.all_gather(gather_list, x, group=sp_group)
        result = torch.cat(gather_list, dim=0)
    return result


def reduce_scatter_dim0(x: torch.Tensor, sp_group: dist.ProcessGroup):
    """Helper function to reduce_scatter `x` along its first dimension."""
    group_size = dist.get_world_size(group=sp_group)
    if dist.get_backend(group=sp_group) == dist.Backend.NCCL:
        result_size = list(x.size())
        result_size[0] //= group_size
        result = torch.empty(result_size, dtype=x.dtype, device=x.device)
        dist.reduce_scatter_tensor(result, x, group=sp_group)
    else:
        # Only NCCL supports `reduce_scatter_tensor`, so fall back to `all_reduce` + split.
        # (Note that gloo doesn't support even the vanilla `reduce_scatter`.)
        x_copy = x.clone().detach()
        dist.all_reduce(x_copy, group=sp_group)
        parts = torch.tensor_split(x_copy, group_size, dim=0)
        result = parts[dist.get_rank(group=sp_group)]
    return result


class _Seq2TensorFn(torch.autograd.Function):
    @staticmethod
    def forward(ctx, x, sp_ctx):
        ctx.sp_ctx = sp_ctx
        result = all_gather_dim0(x, sp_ctx.sp_group)
        return result.view(sp_ctx.batch_size, sp_ctx.seqlen, *result.shape[1:])

    @staticmethod
    def backward(ctx, grad_output):
        assert grad_output.size(0) == ctx.sp_ctx.batch_size
        assert grad_output.size(1) == ctx.sp_ctx.seqlen
        grad_output = grad_output.view(
            ctx.sp_ctx.batch_size * ctx.sp_ctx.seqlen, *grad_output.shape[2:]
        )
        grad_input = reduce_scatter_dim0(grad_output, ctx.sp_ctx.sp_group)
        return grad_input, None


class _Tensor2SeqFn(torch.autograd.Function):
    @staticmethod
    def forward(ctx, x, sp_ctx):
        ctx.sp_ctx = sp_ctx
        assert x.size(0) == sp_ctx.batch_size
        assert x.size(1) == sp_ctx.seqlen
        x = x.view(sp_ctx.batch_size * sp_ctx.seqlen, *x.shape[2:])
        return reduce_scatter_dim0(x, sp_ctx.sp_group)

    @staticmethod
    def backward(ctx, grad_output):
        grad_input = all_gather_dim0(grad_output, ctx.sp_ctx.sp_group)
        return grad_input.view(
            ctx.sp_ctx.batch_size, ctx.sp_ctx.seqlen, *grad_input.shape[1:]
        ), None


class _Seq2WholeFn(torch.autograd.Function):
    @staticmethod
    def forward(ctx, x, sp_ctx):
        ctx.sp_ctx = sp_ctx
        result = all_gather_dim0(x, sp_ctx.sp_group)
        return result.view(sp_ctx.batch_size, sp_ctx.seqlen, *result.shape[1:])

    @staticmethod
    def backward(ctx, grad_output):
        grad_output = grad_output.view(
            ctx.sp_ctx.batch_size * ctx.sp_ctx.seqlen, *grad_output.shape[2:]
        )
        sp_size = dist.get_world_size(group=ctx.sp_ctx.sp_group)
        assert grad_output.size(0) % sp_size == 0
        parts = torch.tensor_split(grad_output, sp_size, dim=0)
        my_part = parts[dist.get_rank(group=ctx.sp_ctx.sp_group)]
        return my_part, None


class _Whole2SeqFn(torch.autograd.Function):
    @staticmethod
    def forward(ctx, x, sp_ctx):
        ctx.sp_ctx = sp_ctx
        assert x.size(0) == ctx.sp_ctx.batch_size
        assert x.size(1) == ctx.sp_ctx.seqlen
        sp_size = dist.get_world_size(group=sp_ctx.sp_group)
        x = x.view(ctx.sp_ctx.batch_size * ctx.sp_ctx.seqlen, *x.shape[2:])
        assert x.size()[0] % sp_size == 0, (x.size(), sp_size)
        parts = torch.tensor_split(x, sp_size, dim=0)
        my_part = parts[dist.get_rank(group=sp_ctx.sp_group)]
        return my_part

    @staticmethod
    def backward(ctx, grad_output):
        grad_input = all_gather_dim0(grad_output, ctx.sp_ctx.sp_group)
        grad_input = grad_input.view(
            ctx.sp_ctx.batch_size, ctx.sp_ctx.seqlen, *grad_input.shape[1:]
        )
        return grad_input, None


def _wrap_function(apply_fn):
    def wrapper(x: torch.Tensor) -> torch.Tensor:
        if _cur_context is None:
            raise RuntimeError("No sequence parallel context enabled.")
        if not _cur_context.enabled:
            return x
        if dist.get_world_size(group=_cur_context.sp_group) == 1:
            return x
        return apply_fn(x, _cur_context)

    return wrapper


# These four functions implement state transitions into and out of sequence parallel regions.
# Each has the following form:
#   def x2y(x: torch.Tensor) -> torch.Tensor:
#
# With arguments:
#   x: The tensor of activations.
# These functions need to be called in a sequence parallel context set up as:
#   with sequence_parallel_context(batch_size, seqlen, sp_group):
#      ... # Run your model.
# This context ensures that the sequence parallel transition functions handle the batch and seqlen
# dimensions correctly. All activation tensors must have shape (batch, seqlen, ...).
seq2tensor = _wrap_function(_Seq2TensorFn.apply)
tensor2seq = _wrap_function(_Tensor2SeqFn.apply)
seq2none = _wrap_function(_Seq2WholeFn.apply)
none2seq = _wrap_function(_Whole2SeqFn.apply)

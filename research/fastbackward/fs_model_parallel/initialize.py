# coding=utf-8

# Copyright (c) Facebook, Inc. and its affiliates. All rights reserved.
#
# This source code is licensed under the BSD license found in the
# LICENSE file in the root directory of this source tree.

# Copyright (c) 2020, NVIDIA CORPORATION.  All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


"""Model and data parallel groups."""

import torch
import torch.distributed

from research.core.types import check_not_none

from .utils import ensure_divisibility

# Model parallel group that the current rank belongs to.
_MODEL_PARALLEL_GROUP: torch.distributed.ProcessGroup | None = None
_MODEL_PARALLEL_RANKS: list[int] | None = None
_MODEL_PARALLEL_BACKEND: str | None = None
# Data parallel group that the current rank belongs to.
_DATA_PARALLEL_GROUP: torch.distributed.ProcessGroup | None = None
_DATA_PARALLEL_RANKS: list[int] | None = None
_DATA_PARALLEL_BACKEND: str | None = None

# Model parallel sub-groups
_MODEL_PARALLEL_SUBGROUPS: dict[str, torch.distributed.ProcessGroup] | None = None


def initialize_model_parallel(
    model_parallel_size_: int,
    *,
    model_parallel_backend: str | None = None,
    ddp_backend: str | None = None,
) -> None:
    """
    Initialize model data parallel groups.

    Arguments:
        model_parallel_size: Number of GPUs used to parallelize model.
        model_parallel_backend: Backend for model parallel groups.
        ddp_backend: Backend for data parallel groups.

    Let's say we have a total of 8 GPUs denoted by g0 ... g7 and we
    use 2 GPUs to parallelize the model. The present function will
    create 4 model parallel groups and 2 data parallel groups as:
        4 model parallel groups:
            [g0, g1], [g2, g3], [g4, g5], [g6, g7]
        2 data parallel groups:
            [g0, g2, g4, g6], [g1, g3, g5, g7]
    Note that for efficiency, the caller should make sure adjacent ranks
    are on the same DGX box. For example if we are using 2 DGX-1 boxes
    with a total of 16 GPUs, rank 0 to 7 belong to the first box and
    ranks 8 to 15 belong to the second box.
    """
    # Get world size and rank. Ensure some consistencies.
    assert torch.distributed.is_initialized()
    world_size = torch.distributed.get_world_size()
    model_parallel_size = int(min(model_parallel_size_, world_size))
    ensure_divisibility(world_size, model_parallel_size)
    rank = torch.distributed.get_rank()

    data_parallel_size = world_size // model_parallel_size

    if torch.distributed.get_rank() == 0:
        print(f"> Initializing model parallel with size {model_parallel_size_}")
        print(f"> Initializing DDP with size {data_parallel_size}")

    global_ranks = torch.LongTensor(range(world_size)).reshape(
        data_parallel_size, model_parallel_size
    )

    current_ranks = torch.where(global_ranks == rank)
    assert all(len(r) == 1 for r in current_ranks)
    current_dp_rank, current_mp_rank = [r[0] for r in current_ranks]

    # Build the data parallel groups.
    global _DATA_PARALLEL_GROUP
    assert _DATA_PARALLEL_GROUP is None, "data parallel group is already initialized"
    global _DATA_PARALLEL_RANKS
    assert _DATA_PARALLEL_RANKS is None, "data parallel group is already initialized"
    global _DATA_PARALLEL_BACKEND
    assert (
        _DATA_PARALLEL_BACKEND is None
    ), "data parallel backend is already initialized"
    for mp_rank in range(model_parallel_size):
        ranks = global_ranks[:, mp_rank].tolist()
        group = torch.distributed.new_group(ranks, backend=ddp_backend)
        if mp_rank == current_mp_rank:
            _DATA_PARALLEL_GROUP = group
            _DATA_PARALLEL_RANKS = ranks
            _DATA_PARALLEL_BACKEND = ddp_backend

    # Build the model parallel groups.
    global _MODEL_PARALLEL_GROUP
    assert _MODEL_PARALLEL_GROUP is None, "model parallel group is already initialized"
    global _MODEL_PARALLEL_RANKS
    assert _MODEL_PARALLEL_RANKS is None, "model parallel group is already initialized"
    global _MODEL_PARALLEL_BACKEND
    assert (
        _MODEL_PARALLEL_BACKEND is None
    ), "model parallel backend is already initialized"
    for dp_rank in range(data_parallel_size):
        ranks = global_ranks[dp_rank, :].tolist()
        group = torch.distributed.new_group(ranks, backend=model_parallel_backend)
        if dp_rank == current_dp_rank:
            _MODEL_PARALLEL_GROUP = group
            _MODEL_PARALLEL_RANKS = ranks
            _MODEL_PARALLEL_BACKEND = model_parallel_backend

    global _MODEL_PARALLEL_SUBGROUPS
    _MODEL_PARALLEL_SUBGROUPS = {}


def model_parallel_is_initialized() -> bool:
    """Check if model and data parallel groups are initialized."""
    if _MODEL_PARALLEL_GROUP is None or _DATA_PARALLEL_GROUP is None:
        return False
    return True


def get_model_parallel_group() -> torch.distributed.ProcessGroup:
    """Get the model parallel group the caller rank belongs to."""
    assert _MODEL_PARALLEL_GROUP is not None, "model parallel group is not initialized"
    return _MODEL_PARALLEL_GROUP


def get_data_parallel_group() -> torch.distributed.ProcessGroup:
    """Get the data parallel group the caller rank belongs to."""
    assert _DATA_PARALLEL_GROUP is not None, "data parallel group is not initialized"
    return _DATA_PARALLEL_GROUP


def get_model_parallel_world_size() -> int:
    """Return world size for the model parallel group."""
    return torch.distributed.get_world_size(group=get_model_parallel_group())


def get_model_parallel_rank() -> int:
    """Return my rank for the model parallel group."""
    return torch.distributed.get_rank(group=get_model_parallel_group())


def get_model_parallel_src_rank() -> int:
    """Calculate the global rank corresponding to a local rank zero
    in the model parallel group."""
    global_rank = torch.distributed.get_rank()
    local_world_size = get_model_parallel_world_size()
    return (global_rank // local_world_size) * local_world_size


def get_data_parallel_world_size() -> int:
    """Return world size for the data parallel group."""
    return torch.distributed.get_world_size(group=get_data_parallel_group())


def get_data_parallel_rank() -> int:
    """Return my rank for the data parallel group."""
    return torch.distributed.get_rank(group=get_data_parallel_group())


def initialize_model_parallel_subgroup(name: str, parallel_size: int) -> None:
    """Initialize and return a new model parallel subgroup."""
    assert _MODEL_PARALLEL_RANKS, "model parallel group is not initialized"
    global _MODEL_PARALLEL_SUBGROUPS
    assert (
        _MODEL_PARALLEL_SUBGROUPS is not None
    ), "model parallel subgroups are not initialized"

    world_size = torch.distributed.get_world_size()
    data_parallel_size = get_data_parallel_world_size()
    model_parallel_size = get_model_parallel_world_size()
    ensure_divisibility(model_parallel_size, parallel_size)
    model_parallel_conjugate_size = model_parallel_size // parallel_size
    current_rank = torch.distributed.get_rank()

    if torch.distributed.get_rank() == 0:
        print(
            f"> Initializing model parallel subgroup {name} with size {parallel_size}."
        )

    groups = torch.LongTensor(range(world_size)).reshape(
        data_parallel_size, model_parallel_conjugate_size, parallel_size
    )

    current_ranks = torch.where(groups == current_rank)
    assert all(len(r) == 1 for r in current_ranks)
    current_dp_rank, current_conjugate_rank, _ = [r[0] for r in current_ranks]

    # Build the model parallel sub-groups.
    for dp_rank in range(data_parallel_size):
        for mp_conjugate_rank in range(model_parallel_conjugate_size):
            ranks = groups[dp_rank, mp_conjugate_rank, :].tolist()
            group = torch.distributed.new_group(ranks, backend=_MODEL_PARALLEL_BACKEND)
            if (
                dp_rank == current_dp_rank
                and mp_conjugate_rank == current_conjugate_rank
            ):
                _MODEL_PARALLEL_SUBGROUPS[name] = check_not_none(group)
                for member_rank in ranks:
                    assert member_rank in _MODEL_PARALLEL_RANKS


def get_model_parallel_subgroup(name: str) -> torch.distributed.ProcessGroup:
    """Get a model parallel subgroup."""
    assert (
        _MODEL_PARALLEL_SUBGROUPS is not None
    ), "model parallel subgroups are not initialized"
    if name == "":
        return get_model_parallel_group()
    return _MODEL_PARALLEL_SUBGROUPS[name]


def get_model_parallel_subgroup_world_size(name: str) -> int:
    """Get world size for a model parallel subgroup."""
    return torch.distributed.get_world_size(group=get_model_parallel_subgroup(name))


def get_model_parallel_subgroup_rank(name: str) -> int:
    """Get my rank for a model parallel subgroup."""
    return torch.distributed.get_rank(group=get_model_parallel_subgroup(name))


def destroy_model_parallel() -> None:
    """Set the groups to none."""
    global _MODEL_PARALLEL_GROUP
    _MODEL_PARALLEL_GROUP = None
    global _MODEL_PARALLEL_RANKS
    _MODEL_PARALLEL_RANKS = None
    global _MODEL_PARALLEL_BACKEND
    _MODEL_PARALLEL_BACKEND = None

    global _DATA_PARALLEL_GROUP
    _DATA_PARALLEL_GROUP = None
    global _DATA_PARALLEL_RANKS
    _DATA_PARALLEL_RANKS = None
    global _DATA_PARALLEL_BACKEND
    _DATA_PARALLEL_BACKEND = None

    global _MODEL_PARALLEL_SUBGROUPS
    _MODEL_PARALLEL_SUBGROUPS = None

"""Utilities for both text logging and metrics reporting."""

from __future__ import annotations

import json
import logging
import re
import time
from collections import defaultdict
from dataclasses import asdict, dataclass
from pathlib import Path
from typing import Any, Optional

import determined as det
import torch
import wandb
from torch import distributed as dist

import research.fastbackward.fs_model_parallel as mpu
from base.tokenizers import Tokenizer, create_tokenizer_by_name, list_tokenizers
from research.environments import providers
from research.fastbackward.data import unmask_token
from research.fastbackward.distributed import is_main_process
from research.utils.visualize_logits import (
    ColorTuple,
    LogitsVisualizer,
    TopKProbabilities,
)


@dataclass
class WandbConfig:
    """Configuration for Wandb logging."""

    project: str
    name: str
    group: Optional[str] = None


class _MetricsState:
    """State for metrics tracking."""

    def __init__(
        self,
        use_wandb: bool = False,
        determined_context: Optional[det.core.Context] = None,  # type: ignore
    ):
        self.use_wandb = use_wandb
        self.determined_context = determined_context


# Global variable for holding objects to talk to metrics services
_metrics_state: Optional[_MetricsState] = None


def initialize_metrics_tracking(
    wandb_config: Optional[WandbConfig] = None,
    determined_context: Optional[det.core.Context] = None,  # type: ignore
    run_config: Optional[dict[str, Any]] = None,
    reinit: bool = False,
) -> None:
    global _metrics_state  # pylint: disable=global-statement
    if not reinit:
        assert _metrics_state is None, "Metrics tracking already initialized"

    use_wandb = False
    if run_config is None:
        run_config = {}

    if wandb_config:
        wandb.init(**asdict(wandb_config), reinit=reinit, config=run_config)
        use_wandb = True

    # Convenience: if we have both determined and wandb, set links between the two
    if determined_context and wandb_config:
        assert wandb.run is not None, "wandb.run is None"
        wandb_url = wandb.run.get_url()
        det_exp_id = determined_context.train._exp_id  # pylint: disable=protected-access
        data = {
            "id": det_exp_id,
            "notes": f"[Link to wandb run]({wandb_url})",
        }
        determined_context.train._session.patch(  # pylint: disable=protected-access
            f"/api/v1/experiments/{det_exp_id}", data=json.dumps(data)
        )
        try:
            provider_name = providers.detect_provider()
            det_base_url = providers.get_provider(provider_name).determined_url
            det_url = f"{det_base_url}/det/experiments/{det_exp_id}"
            wandb.log(
                {
                    "determined/link": wandb.Html(
                        f'<a href="{det_url}">Link to determined run {det_exp_id}</a>'
                    )
                },
                # Start at step 0 because we log eval metrics at step 0.
                0,
            )
        except RuntimeError as exc:
            print(f"Failed to set determined url {exc}")

    _metrics_state = _MetricsState(use_wandb, determined_context)


def report_metrics(metrics: dict[str, Any], step: int) -> None:
    assert _metrics_state is not None, "Metrics tracking not initialized"
    if _metrics_state.use_wandb:
        wandb.log(metrics, step=step)

    # NOTE: there is a bunch of weirdness with determined metrics reporting:
    # - If I try to report train and validation metrics with the same step, the job crashes
    # - The reporting is generally slow (tens of ms) and occurs at a CPU/GPU sync point
    # Putting it together, the current solution is _only report validation metrics_
    if _metrics_state.determined_context:
        # Group the metrics in case we want, in the future, to report all of them
        grouped_metrics = defaultdict(dict)
        for metric_name, metric_value in metrics.items():
            parts = metric_name.split("/")
            if len(parts) == 1:
                group_name = "training"  # sane default value
                metric_name = parts[0]
            else:
                group_name = parts[0]
                metric_name = "/".join(parts[1:])
            if group_name == "train":
                group_name = "training"  # determined calls it "training"
            metric_name = metric_name.replace(".", "_")
            grouped_metrics[group_name][metric_name] = metric_value
        validation_metrics = grouped_metrics["validation"]
        if len(validation_metrics) > 0:
            _metrics_state.determined_context.train.report_validation_metrics(
                steps_completed=step,
                metrics=validation_metrics,
            )
        # Here is the code to log everything:
        # for group_name, group_metrics in grouped_metrics.items():
        #     _metrics_state.determined_context.train.report_metrics(
        #         group_name,
        #         step,
        #         group_metrics,
        #     )


def shutdown_metrics_tracking() -> None:
    assert _metrics_state is not None, "Metrics tracking not initialized"
    if _metrics_state.use_wandb:
        wandb.finish()


def setup_logging(
    log_file_prefix: Path | None = None,
    log_level: int = logging.INFO,
    append_timestamp: bool = True,
):
    """Set up logging to handle distributed logging in a uniform format.

    Args:
        log_file_prefix: If provided, prefix of a file path to save logs.
            Each distributed process (rank) will have its own log file with this prefix.
        log_level: Minimum level to log.
        append_timestamp: If true, we will append a timestamp to the log output files.
            Note that because time is a finicky concept in distributed systems, the
            different ranks will have different timestamps and so their file names
            won't exactly match.
    """
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # 1. Clear any existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 2. Create handlers
    if log_file_prefix is None:
        handlers = (logging.StreamHandler(),)
    else:
        # If we're setting up logging across multiple processes, we need to make sure
        # only one of them creates the directory, and then everyone waits for it to
        # be created.
        if is_main_process():
            log_file_prefix.parent.mkdir(parents=True, exist_ok=True)
        dist.barrier()

        # We'll save to a different log file for each process
        rank = dist.get_rank()
        if append_timestamp:
            log_file_prefix = log_file_prefix.with_name(
                f"{log_file_prefix.name}-{rank}-{time_string()}.log"
            )
        else:
            log_file_prefix = log_file_prefix.with_name(
                f"{log_file_prefix.name}-{rank}.log"
            )

        handlers = (
            logging.FileHandler(f"{log_file_prefix}"),
            logging.StreamHandler(),
        )

    # The rank is automatically included in Determined logs, but we are including it
    # here for convenience during local testing.
    rank = dist.get_rank()
    formatter = logging.Formatter(
        f"%(asctime)s - %(name)s.%(funcName)s@L%(lineno)d - rank@{rank}:%(levelname)s: %(message)s"
    )
    # 3. Attach handlers to the root logger.
    for handler in handlers:
        handler.setLevel(log_level)
        handler.setFormatter(formatter)
        root_logger.addHandler(handler)
    return root_logger


def time_string():
    """Get a string showing the current time."""
    DATETIMEFORMAT = "%Y-%m-%d_%H-%M-%S"
    string = "{:}".format(time.strftime(DATETIMEFORMAT, time.gmtime(time.time())))
    return string


def seconds2str(total_seconds: float) -> str:
    hours, remainder = divmod(total_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{int(hours)} hours {int(minutes)} mins {int(seconds)} seconds"


class Meter:
    """Simple class to track the average of a value over time."""

    def __init__(self):
        self.sum = 0.0
        self.count = 0

    def update(self, value):
        """Update the running sum and count with a new value."""
        self.sum += value
        self.count += 1

    def average(self):
        """Compute the average of the values seen so far."""
        if self.count == 0:
            return 0
        else:
            return self.sum / self.count


class Timer:
    """Simple class to track the time something takes."""

    def __init__(self):
        self.split_start_time = time.time()
        self.split_times = Meter()

    def reset(self):
        """Reset the counters."""
        self.split_start_time = time.time()
        self.split_times = Meter()

    def measure_split(self):
        """Measure the split time.

        Returns:
            The time taken for this split.
        """
        elapsed_time = time.time() - self.split_start_time
        self.split_times.update(elapsed_time)
        self.split_start_time = time.time()
        return elapsed_time


# Mapping from regular expressions for GPU devices to peak FP16/BF16 (tensor core) FLOPS
_DEVICE_FLOPS: list[tuple[str, float]] = [
    # https://developer.nvidia.com/blog/nvidia-hopper-architecture-in-depth/ (see SXM part)
    (r".*H100.*", 1000e12),
    # https://developer.nvidia.com/blog/nvidia-ampere-architecture-in-depth/
    (r".*A100.*", 312e12),
    # https://www.nvidia.com/content/dam/en-zz/Solutions/design-visualization/quadro-product-literature/quadro-rtx-5000-data-sheet-us-nvidia-704120-r4-web.pdf
    (r".*Quadro RTX 5000.*", 89.2e12),
    # https://www.nvidia.com/en-us/design-visualization/rtx-a5000/
    (r".*NVIDIA RTX A5000.*", 222.2e12),
    # https://www.nvidia.com/en-us/design-visualization/rtx-a6000/
    (r".*NVIDIA RTX A6000.*", 309.7e12),
]


def get_device_peak_flops(device_name: str) -> Optional[float]:
    """Match the given device_name to peak flops in the table."""
    for device_re, flops in _DEVICE_FLOPS:
        if re.match(device_re, device_name):
            return flops
    return None


class LogitsVisualizationReporter:
    """A class to log logits visualizations."""

    def __init__(
        self,
        max_samples: int,
        tokenizer: Tokenizer,
        special_token_color: ColorTuple = (30, 60, 190),
        show_top_k: int = 6,
    ):
        self.max_samples = max_samples
        self.tokenizer = tokenizer
        self.logits_visualizer = LogitsVisualizer(
            special_token_color=special_token_color,
            show_top_k=show_top_k,
            tokenizer=tokenizer,
        )
        self.html_dict: dict[str, list[wandb.Html]] = defaultdict(list)

    @torch.no_grad()
    def __call__(
        self,
        name: str,
        x: torch.Tensor,
        y: torch.Tensor,
        y_hat: torch.Tensor,
        chunk_size: int = 4096,
    ):
        """Log logits visualizations."""
        if mpu.get_data_parallel_rank() != 0:
            return
        assert y_hat.dim() == 3, f"{y_hat.dim()=}"
        # y_hat should have the shape: (batch, time, vocab//mp_size)
        full_y_hat = mpu.gather_from_model_parallel_region(y_hat)
        if not is_main_process():
            # Below we calculate the probs from the logits. Restricting the rest
            # of this function to just one process avoids that we move the probs
            # to the CPU 8 times per node, which is very slow.
            #
            # Above we have already aggregated the logits from all model parallel ranks,
            # so we can just use the full_y_hat tensor on the main process.
            return

        y_masked = y < 0  # negative labels are assumed to be masked out
        for j in range(x.size(0)):
            # check if we already collected enough samples for this eval
            if name in self.html_dict and len(self.html_dict[name]) >= self.max_samples:
                return

            # the visualizer expects non-negative token ids
            tokens = unmask_token(x[j, :]).tolist()
            tokens.append(unmask_token(y[j, -1]).item())
            # we don't ever train on the first token, so mark it as masked
            tokens_masked = [True] + y_masked[j, :].tolist()
            # len(tokens) should now equal to len(tokens_masked)

            # We compute the probs for all tokens except the first one
            # we should have probs.size(0) == len(tokens) - 1
            top_probs: list[TopKProbabilities] = []
            # Below is more memory efficient way to compute the probs,
            # which is equivalent to the following:
            # probs = torch.softmax(full_y_hat[j, :, :], dim=-1, dtype=torch.float)
            for start in range(0, full_y_hat.size(1), chunk_size):
                end = min(start + chunk_size, full_y_hat.size(1))
                current_probs = torch.softmax(
                    full_y_hat[j, start:end, :], dim=-1, dtype=torch.float
                )
                assert current_probs.shape == (end - start, full_y_hat.size(2))
                topk_probs, topk_ids = torch.topk(
                    current_probs, k=self.logits_visualizer.show_top_k, dim=-1
                )
                for i in range(end - start):
                    top_probs.append(
                        TopKProbabilities(topk_ids[i].tolist(), topk_probs[i].tolist())
                    )

            html_str = self.logits_visualizer.seq_distribution_as_html(
                tokens,
                top_probs,
                is_masked=tokens_masked,
                highlight_middle=False,
            )
            self.html_dict[name].append(wandb.Html(html_str))

    def report_metrics(self, iter_num: int):
        """Report the collected logits visualizations."""
        if not is_main_process():
            return
        metrics = {}
        for name, htmls in self.html_dict.items():
            for i, html in enumerate(htmls):
                if name == "*":
                    label_name = f"logits/sample-{i}"
                else:
                    label_name = f"logits/{name}.sample-{i}"
                metrics[label_name] = html
        report_metrics(metrics, iter_num)


def get_base_tokenizer(tokenizer_name: str) -> Tokenizer:
    """Get a base tokenizer by the *registered name*."""
    valid_names = list_tokenizers()
    if tokenizer_name not in valid_names:
        raise ValueError(
            f"Invalid tokenizer: {tokenizer_name}. Valid names: {valid_names}"
        )
    return create_tokenizer_by_name(tokenizer_name)


def get_research_tokenizer(cls_name: str) -> Tokenizer:
    """Get a research tokenizer by its *class name*."""
    # we guard the import here so that it's only imported when actually used
    from megatron.tokenizer.tokenizer import get_tokenizer

    return get_tokenizer(cls_name)

"""Attention implementations for fastbackward."""

import flash_attn
import torch
import torch.nn.functional as F
from torch import Tensor


class AttentionFn(torch.nn.Module):
    """Base class for attention function implementations."""

    def forward(
        self,
        xq: Tensor,
        xk: Tensor,
        xv: Tensor,
        start_pos: int | None = None,
        mask: Tensor | None = None,
    ) -> Tensor:
        raise NotImplementedError("Not implemented.")


class FusedAttn(AttentionFn):
    """Fused attention for training."""

    def __init__(self, dropout_p: float = 0.0, causal: bool = True):
        super(FusedAttn, self).__init__()
        self.dropout_p = dropout_p
        self.causal = causal

    def forward(
        self,
        xq: Tensor,
        xk: Tensor,
        xv: Tensor,
        start_pos: int | None = None,
        mask: Tensor | None = None,
    ):
        assert start_pos is None
        dropout_p = self.dropout_p if self.training else 0.0
        if xq.dtype in [torch.float16, torch.bfloat16] and mask is None:
            output = flash_attn.flash_attn_func(
                xq,
                xk,
                xv,
                dropout_p=dropout_p,
                causal=True,
                deterministic=True,
            )
            assert isinstance(output, torch.Tensor)
        else:
            # We fallback here if attention mask is provided or FP32 is used.
            # Flash attention does not support FP32, so we need PyTorch-native scaled
            # dot product attention (SDPA) as a fallback.
            # However, we need some extra work to use SDPA since:
            # 1. Flash attention expects [batch, seq, heads, dim], while SDPA expects
            #   [batch, heads, seq, dim];
            # 2. SDPA does not support grouped-query attention (GQA).
            n_heads = xq.shape[2]
            n_kv_heads = xk.shape[2]
            repetition_count = n_heads // n_kv_heads

            xq = xq.transpose(1, 2)
            xk = repeat_kv(xk, repetition_count).transpose(1, 2)
            xv = repeat_kv(xv, repetition_count).transpose(1, 2)

            output = F.scaled_dot_product_attention(
                xq,
                xk,
                xv,
                dropout_p=dropout_p,
                is_causal=mask is None,
                attn_mask=mask,
            )
            output = output.transpose(1, 2).contiguous()
        return output


def repeat_kv(x: torch.Tensor, n_rep: int) -> torch.Tensor:
    """torch.repeat_interleave(x, dim=2, repeats=n_rep)."""
    bs, slen, n_kv_heads, head_dim = x.shape
    if n_rep == 1:
        return x
    return (
        x[:, :, :, None, :]
        .expand(bs, slen, n_kv_heads, n_rep, head_dim)
        .reshape(bs, slen, n_kv_heads * n_rep, head_dim)
    )


class SimpleCachedAttn(AttentionFn):
    """Straight-line attention implementation with k/v-cache for generation."""

    def __init__(
        self,
        n_local_heads: int,
        n_local_kv_heads: int,
        head_dim: int,
        max_batch_size: int,
        max_seq_len: int,
    ):
        super(SimpleCachedAttn, self).__init__()
        self.register_buffer(
            "cache_k",
            torch.zeros((max_batch_size, max_seq_len, n_local_kv_heads, head_dim)),
            persistent=False,
        )
        self.register_buffer(
            "cache_v",
            torch.zeros((max_batch_size, max_seq_len, n_local_kv_heads, head_dim)),
            persistent=False,
        )
        self.n_kv_reps = n_local_heads // n_local_kv_heads
        self.head_dim = head_dim

    def reset_max_batch_size(self, max_batch_size: int):
        self.cache_k.resize_(max_batch_size, *self.cache_k.shape[1:])
        self.cache_v.resize_(max_batch_size, *self.cache_v.shape[1:])
        self.cache_k.zero_()
        self.cache_v.zero_()

    def forward(
        self,
        xq: Tensor,
        xk: Tensor,
        xv: Tensor,
        start_pos: int | None = None,
        mask: Tensor | None = None,
    ):
        assert start_pos is not None
        assert mask is None, "SimpleCachedAttn does not need the mask!"
        this_batch_size = xq.size(0)
        assert xk.size(0) == this_batch_size
        assert xv.size(0) == this_batch_size
        # NOTE: if `this_batch_size` is less than `max_batch_size`, we use the first
        # `this_batch_size` rows of the cache (starting from 0).
        return flash_attn.flash_attn_with_kvcache(
            xq,
            self.cache_k[0:this_batch_size, ...],
            self.cache_v[0:this_batch_size, ...],
            k=xk,
            v=xv,
            causal=True,
            cache_seqlens=start_pos,
        )

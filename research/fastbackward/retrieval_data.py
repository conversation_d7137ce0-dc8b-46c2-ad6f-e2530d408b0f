"""Utilities for loading retrieval data.

See `convert_tokens_to_retrieval_data` for a schema.
"""

from __future__ import annotations

import logging
from collections.abc import Callable
from typing import Literal

import numpy as np
import torch
import torch.utils.data._utils
from tensordict import Tensor<PERSON>ict

from base.tokenizers import Tokenizer
from base.tokenizers.tokenizer import RetrievalSpecialTokens
from research.fastbackward.data import pad_or_truncate_tokens

logger = logging.getLogger(__name__)


def patch_pin_memory():
    """Patch DataLoader's pin memory feature to play nicely with TensorDict.

    For reasons(TM), when using pin_memory=True with a "Mapping" type, DataLoader will
    try to create a new map object, which fails because it lacks TensorDict's batch_size
    argument. Annoyingly, TensorDict has a `pin_memory` feature that does the right
    thing, but the ordering of `if` cases in DataLoader's code is such that it will
    always try to create a new map object.
    """
    _pin_memory = torch.utils.data._utils.pin_memory.pin_memory

    # Patch torch's `pin_memory` function to work with TensorDict.
    def patched_pin_memory(data, device=None):
        """<PERSON> torch's pin_memory function to work with TensorDict."""
        if isinstance(data, TensorDict):
            return data.pin_memory()
        else:
            return _pin_memory(data, device)

    torch.utils.data._utils.pin_memory.pin_memory = patched_pin_memory


def resample_documents_keeping_positives(
    document_tokens: list[list[int]],
    document_scores_: list[float],
    max_positive_fraction: float,
    documents_per_batch: int,
    max_positive_count: int = -1,
    positive_threshold: float = 0.0,
):
    """Resample documents keeping positives.

    Args:
        document_tokens: A list of document tokens.
        document_scores_: A list of document scores.
        max_positive_fraction: The maximum fraction of documents to include in the
          batch that are "positive" in that they have a score equal to the max.
          We pick a random subset of documents if needed.
        documents_per_batch: The number of documents to include in each batch.
        max_positive_count: If > 0, the maximum number of positive documents to include
          in the batch.
        positive_threshold: The minimum score to consider a document as positive.

    Returns:
        A tuple of (document_tokens, document_scores) where document_tokens is a list
        of document tokens and document_scores is a list of document scores.
    """
    document_idxs = np.arange(len(document_scores_))
    # Figure out how many "positive" documents we have.
    document_scores = np.array(document_scores_)
    positive_idxs = document_idxs[document_scores >= positive_threshold]
    negative_idxs = document_idxs[document_scores < positive_threshold]

    # We try to keep up to `min(max_positive_count, max_positive_fraction` positive
    # documents in the batch, but we'll sample a random subset if there are more than
    # that.
    target_positive_number = int(max_positive_fraction * documents_per_batch)
    if max_positive_count > 0:
        target_positive_number = min(target_positive_number, max_positive_count)

    if len(positive_idxs) > target_positive_number:
        np.random.shuffle(positive_idxs)
        positive_idxs = positive_idxs[:target_positive_number]

    # Fill the rest of our quota with negative documents, sampling if necessary.
    if len(positive_idxs) + len(negative_idxs) > documents_per_batch:
        np.random.shuffle(negative_idxs)
        negative_idxs = negative_idxs[: documents_per_batch - len(positive_idxs)]

    document_idxs = np.concatenate([positive_idxs, negative_idxs])

    document_tokens = [document_tokens[i] for i in document_idxs]
    document_scores_ = document_scores[document_idxs].tolist()
    return document_tokens, document_scores_


def create_tokens_to_retrieval_data_fn(
    tokenizer: Tokenizer,
    documents_per_batch: int = 64,
    max_query_tokens: int = 4096,
    max_document_tokens: int = 512,
    max_positive_fraction: float = 0.25,
    make_token_lengths_divisible_by: int = 1,
    selection_method: Literal["random", "first"] = "first",
    max_positive_count: int = -1,
    positive_threshold: float = 0.0,
) -> Callable[[np.ndarray | torch.Tensor], TensorDict]:
    """Convert data from a sequence of tokens to a retrieval TensorDict.

    TODO(arun): This function is really composed of a number of simpler operations.
    We should refactor those into separate composable functions, and use the new
    component registry to create the data transformation pipeline in
    `train_retriever.py`.

    Args:
        tokenizer: The tokenizer; used to detokenize the score tokens. The tokenizer
            must have the following special tokens:
                end_of_query: The token ID for the query token.
                end_of_key: The token ID for the document token.
                pad_id: The token ID for padding.
        documents_per_batch: The number of documents to include in each batch.
        max_query_tokens: The maximum number of tokens in the query.
        max_document_tokens: The maximum number of tokens in a document.
        max_positive_fraction: The maximum fraction of documents to include in the
          batch that are "positive" in that they have a score equal to the max.
          We pick a random subset of documents if needed.
        make_token_lengths_divisible_by: Make sequence lengths divisible by this value.
          This can be used to make the sequence length divisible by power of 2 for
          efficient training. With a large value (i.e. max_*_tokens), you can force
          queries and documents to be the same length.
        selection_method: The method to use to select documents from the batch.
        max_positive_count: If > 0, the maximum number of positive documents to include
          in the batch.
        positive_threshold: The minimum score to consider a document as positive.

    Dimensions:
        B: Batch size.
        K: Number of documents per batch element.
        Lq: Sequence length for queries (typically longer than Ld).
        Ld: Sequence length for documents.

    Returns:
        a function that converts a sequence of tokens to a retrieval TensorDict.
        The tokens look like this (without the new lines):
            {query_tokens}<end-of-query>
            {document_tokens}<end-of-key>{score}<end-of-key>
            {document_tokens}<end-of-key>{score}<end-of-key>
            ...
            where {score} is the tokenized version of a floating-point score. There are
            no padding tokens.

        The returned TensorDict has the following fields:
        - query_tokens_BLq: The query tokens.
        - document_tokens_BKLd: The document tokens.
        - labels_BK: Scores for each document in the batch.

    Raises:
        IndexError: If the example is not formatted correctly and one of the delimiter
          tokens is missing.
    """
    assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)

    query_token_id = tokenizer.special_tokens.end_of_query
    document_token_id = tokenizer.special_tokens.end_of_key
    pad_id = tokenizer.special_tokens.padding

    def impl(example: np.ndarray | torch.Tensor) -> TensorDict:
        tokens: list[int] = example.tolist()
        # 1. Split out the query tokens.
        query_index = tokens.index(query_token_id)
        query_tokens_ = tokens[: min(query_index, max_query_tokens - 1)] + [
            query_token_id
        ]
        query_tokens_BLq = torch.tensor(
            pad_or_truncate_tokens(
                query_tokens_,
                round_up_divisible_by(
                    len(query_tokens_), make_token_lengths_divisible_by
                ),
                pad_id,
            ),
            dtype=torch.int32,
        ).reshape(1, -1)

        tokens = tokens[query_index + 1 :]

        document_tokens = []
        document_scores = []
        while tokens:
            document_index = tokens.index(document_token_id)
            document_tokens_ = tokens[
                : min(document_index, max_document_tokens - 1)
            ] + [document_token_id]
            document_tokens.append(
                pad_or_truncate_tokens(
                    document_tokens_,
                    round_up_divisible_by(
                        len(document_tokens_), make_token_lengths_divisible_by
                    ),
                    pad_id,
                )
            )

            score_index = tokens.index(document_token_id, document_index + 1)
            score_tokens = tokens[document_index + 1 : score_index]
            document_scores.append(float(tokenizer.detokenize(score_tokens)))
            tokens = tokens[score_index + 1 :]

        if selection_method == "random":
            document_tokens, document_scores = resample_documents_keeping_positives(
                document_tokens,
                document_scores,
                max_positive_fraction,
                documents_per_batch,
                max_positive_count=max_positive_count,
                positive_threshold=positive_threshold,
            )
        else:
            if max_positive_count > 0:
                logger.warning(
                    "max_positive_count is set, but selection_method is not random. "
                    "Ignoring max_positive_count."
                )
            document_tokens = document_tokens[:documents_per_batch]
            document_scores = document_scores[:documents_per_batch]

        # Set the padding length based on the length of the longest document in our batch.
        pad_length = max(len(tokens) for tokens in document_tokens)
        document_tokens_BKLd = torch.tensor(
            [
                pad_or_truncate_tokens(tokens, pad_length, pad_id)
                for tokens in document_tokens
            ],
            dtype=torch.int32,
        ).reshape(1, -1, pad_length)
        labels_BK = torch.tensor(document_scores, dtype=torch.float32).reshape(1, -1)
        labels_mask_BK = torch.ones_like(labels_BK, dtype=torch.bool)

        return TensorDict(
            {
                "query_tokens_BLq": query_tokens_BLq,
                "doc_tokens_BKLd": document_tokens_BKLd,
                "labels_BK": labels_BK,
                "labels_mask_BK": labels_mask_BK,
            },
            batch_size=[1],
        )

    return impl


def create_collate_retrieval_data_fn(
    document_token_id: int,
    pad_id: int,
) -> Callable[[list[TensorDict]], TensorDict]:
    """Collate a list of retrieval tensor dicts into a single batch.

    Args:
        document_token_id: The token ID for the document token.
        pad_id: The token ID for padding.

    Returns:
        A function that collates a list of TensorDicts to a single TensorDict.
        Each TensorDict should havewith the following fields:
        - query_tokens_BLq: The query tokens.
        - document_tokens_BKLd: The document tokens.
        - labels_BK: Scores for each document in the batch.
        - mask_BK: Mask for which documents in each batch should be considered. This is
            required when collating data as some examples may have too few docs.
    """

    def impl(examples: list[TensorDict]) -> TensorDict:
        device = examples[0]["query_tokens_BLq"].device

        B = len(examples)
        # 1. Pad the query tokens.
        query_tokens_BLq = torch.nn.utils.rnn.pad_sequence(
            [example["query_tokens_BLq"].squeeze(0) for example in examples],
            batch_first=True,
            padding_value=pad_id,
        )

        # 2. Pad the document tokens.
        # Because different examples may have different numbers of documents (K), we need to
        # first extend K to the max number of documents.
        K = max(example["doc_tokens_BKLd"].shape[1] for example in examples)

        def extend_docs(docs_KLd: torch.Tensor):
            """Get filler docs for a batch of documents that is Ld long and has K_ docs.

            Returns:
                A tensor of shape (target_K - K_, Ld) with filler tokens.
            """
            K_ = docs_KLd.shape[-2]
            Ld = docs_KLd.shape[-1]

            return torch.concat(
                (
                    docs_KLd,
                    torch.tensor(
                        [document_token_id] + [pad_id] * (Ld - 1),
                        dtype=torch.int32,
                        device=device,
                    ).expand(K - K_, -1),
                ),
                dim=0,
            )

        document_tokens_BKLd = torch.nn.utils.rnn.pad_sequence(
            # Flatten the document tokens from each example into a (B*K, Ld') sequence
            # for padding -- we'll reshape at the end.
            sum(
                (
                    list(extend_docs(example["doc_tokens_BKLd"].squeeze(0)))
                    for example in examples
                ),
                [],
            ),
            batch_first=True,
            padding_value=pad_id,
        ).reshape(B, K, -1)

        def extend_sequence_1d(
            seq: torch.Tensor, length: int, fill_value: float, dim: int = -1
        ) -> torch.Tensor:
            """Extend a 1-d sequence to ensure its length is `length`."""
            return torch.concat(
                (
                    seq,
                    torch.full(
                        (length - seq.shape[0],),
                        fill_value,
                        dtype=seq.dtype,
                        device=seq.device,
                    ),
                ),
                dim=dim,
            )

        # 3. Stack the labels.
        labels_BK = torch.stack(
            [
                extend_sequence_1d(example["labels_BK"].squeeze(0), K, -torch.inf)
                for example in examples
            ]
        )

        # 4. Create a mask for which documents in each batch should be considered.
        labels_mask_BK = torch.stack(
            [
                extend_sequence_1d(example["labels_mask_BK"].squeeze(0), K, False)
                for example in examples
            ]
        )

        return TensorDict(
            {
                "query_tokens_BLq": query_tokens_BLq,
                "doc_tokens_BKLd": document_tokens_BKLd,
                "labels_BK": labels_BK,
                "labels_mask_BK": labels_mask_BK,
            },
            batch_size=[len(examples)],
        )

    return impl


def round_up_divisible_by(x: int, divisor: int) -> int:
    """Round `x` up to make it divisible by `divisor`.

    Args:
        x: The value to make divisible.
        divisor: The divisor to make `x` divisible by.
        max_value: The maximum value to return.

    Returns:
        The rounded value.
    """
    quotient, remainder = divmod(x, divisor)
    if remainder == 0:
        return x
    else:
        return (quotient + 1) * divisor

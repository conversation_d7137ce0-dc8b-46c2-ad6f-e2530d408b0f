# DPO

## Data preparation


Training code expects data in a single `DatasetDict` (from <PERSON><PERSON>'s `datasets`) with 2 keys: `train` and `eval`.
Every sample should contain:
- `prompt_tokens: list[int]`: Tokens of input prompt.
- `chosen_continuation_tokens: list[int]`: Tokens of "good" continuation.
- `chosen_continuation_logprobs: list[float]`: Logprobs of "good" continuation. Same length with `chosen_continuation_tokens`.
- `rejected_continuation_tokens:  list[int]`: Tokens of "bad" continuation.
- `rejected_continuation_logprobs: list[float]`: Logprobs of "bad" continuation. Same length with `rejected_continuation_tokens`.

To save it in `DatasetDict` format:
```
train_samples = {
  "prompt_tokens": [[A1, A2, ...], [B1, B2, ...], ...]
  "chosen_continuation_tokens: [[X1, X2, ...], [Y1, Y2, ...], ...]
  ...
}
eval_samples = {...}

combined_dataset = DatasetDict(
    {
        "train": Dataset.from_dict(train_samples),
        "eval": Dataset.from_dict(eval_samples),
    }
)
combined_dataset.save_to_disk(OUTPUT_DIR / "preference_dataset")
```

## Training

- Example config file: `experimental/yuri/continuations/train_config_continuations_alignment.yaml`
- `python determined/launch.py -s train_rlhf.py $CONFIG_PATH`
- To validate that you computed offline logprobs correctly, check that `chosen_reward` and `rejected_rewards` metrics (in wandb) start approximately in 0.

"""Functions and utilities for training."""

import logging
import math
from collections.abc import Iterable, Iterator, Sequence
from dataclasses import dataclass, field
from typing import Callable, NamedTuple, Optional

import torch
import torch.distributed as dist
from dataclasses_json import DataClassJsonMixin
from torch.utils.data import DataLoader

import research.fastbackward.fs_model_parallel as mpu
from research.fastbackward import distributed, logutils
from research.fastbackward.flat_model_state import FlatModelState
from research.fastbackward.losses import LossFn
from research.fastbackward.mixed_precision_adam import MixedPrecisionAdamW
from research.fastbackward.model import ModelArgs, Transformer, num_params

logger = logging.getLogger(__name__)


class InputTargetPair(NamedTuple):
    """Data loaded for a single forward pass.

    These are returned from the training loop and can be used for metrics and logging.
    """

    X: torch.Tensor
    """Input tensor (or tensor dict) from the data loader."""

    Y: torch.Tensor
    """Target tensor (or tensor dict) from the data loader."""


@dataclass
class _MicroBatch:
    input_tokens: torch.Tensor
    """The input tensor for this micro batch."""

    target_tokens: torch.Tensor
    """The target tensor for this micro batch."""

    weight: float
    """The weight to apply to this micro batch when computing the loss.

    Typically around 1/gradient_accumulation_steps.

    By default, this is the number of valid tokens in this micro batch
    (across all data parallel processes) divided by the total number of
    valid tokens across all micro batches. Additional reweighting of the
    microbatch on this device versus other devices is happening in the loss
    function.
    """


def gather_micro_batches(
    train_loader: Iterator,
    gradient_accumulation_steps: int,
) -> list[_MicroBatch]:
    """Gather micro batches from all data parallel processes."""
    micro_batches = []
    num_valid_tokens_global = 0
    data_parallel_valid_tokens_per_micro_batch = []
    device = distributed.get_local_device()
    for micro_step in range(gradient_accumulation_steps):
        logger.debug("micro-step %d/%d", micro_step, gradient_accumulation_steps)
        input_tokens, target_tokens, _ = next(train_loader)
        # Move the data to the GPU
        input_tokens = input_tokens.to(device, non_blocking=True)
        target_tokens = target_tokens.to(device, non_blocking=True)
        # count the number of valid tokens across all microbatches in the current gradient accumulation step
        # this is used to reweight the loss across microbatches
        num_valid_tokens = (target_tokens >= 0).sum()
        num_valid_tokens_dp = torch.tensor(
            [num_valid_tokens], device=num_valid_tokens.device, dtype=torch.float32
        )
        dist.all_reduce(
            num_valid_tokens_dp,
            op=dist.ReduceOp.SUM,
            group=mpu.get_data_parallel_group(),
        )
        num_valid_tokens_dataparallel = num_valid_tokens_dp.item()
        num_valid_tokens_global += num_valid_tokens_dataparallel
        data_parallel_valid_tokens_per_micro_batch.append(num_valid_tokens_dataparallel)
        micro_batches.append(
            _MicroBatch(
                input_tokens,
                target_tokens,
                1.0 / gradient_accumulation_steps,  # placeholder, to be filled in below
            )
        )

    if num_valid_tokens_global == 0:
        num_valid_tokens_global += 1e-6
        logger.warning("All tokens in this batch were masked out.")
    for micro_batch, dp_valid_tokens in zip(
        micro_batches, data_parallel_valid_tokens_per_micro_batch
    ):
        micro_batch.weight = dp_valid_tokens / num_valid_tokens_global

    return micro_batches


def forward_backward(
    model: torch.nn.Module,
    loss_fn: LossFn,
    train_loader: Iterator,
    gradient_accumulation_steps: int,
    extra_model_train_args: dict | None = None,
    disable_microbatch_weighting: bool = False,
) -> tuple[float, list[InputTargetPair]]:
    if extra_model_train_args is None:
        extra_model_train_args = {}  # No empty containers in defaults!

    micro_batches = gather_micro_batches(train_loader, gradient_accumulation_steps)

    # use 1-dim densor with single element instead of 0-dim to simplify
    # all-reduce operation.
    total_loss = torch.tensor([0.0], device=distributed.get_local_device())
    inputs_and_targets = []
    for mb in micro_batches:
        logits = model(mb.input_tokens, **extra_model_train_args)
        loss = loss_fn(mb.input_tokens, mb.target_tokens, logits)
        if disable_microbatch_weighting:
            loss *= 1.0 / gradient_accumulation_steps
        else:
            # The loss we get from the loss function is already
            # reweighted across data parallel processes.
            # The following scales the loss to average over gradient accumulation steps.
            # micro_batch_weight should be approximately 1/gradient_accumulation_steps
            loss *= mb.weight
        loss.backward()
        total_loss += loss.detach()
        inputs_and_targets.append([mb.input_tokens, mb.target_tokens])

    if mpu.get_data_parallel_world_size() > 1:
        # If data parallel, average loss over all participants
        dist.all_reduce(
            total_loss,
            op=dist.ReduceOp.SUM,
            group=mpu.get_data_parallel_group(),
        )
        total_loss /= mpu.get_data_parallel_world_size()

    return total_loss.item(), inputs_and_targets


# TODO: break this into component testable functions
def flat_model_optimizer_step(
    flat_model: FlatModelState,
    optimizer: MixedPrecisionAdamW,
    grad_clip: float = 0.0,
) -> torch.Tensor:
    # 1) reduce_scatter low-precision grads
    if mpu.get_data_parallel_world_size() > 1:
        dist.reduce_scatter_tensor(
            flat_model.dist_grads(),
            flat_model.grads(),
            op=dist.ReduceOp.AVG,
            group=mpu.get_data_parallel_group(),
        )

    # 2) clip the grads
    if grad_clip > 0.0:
        # First, compute the norm of my owned slice of the grads
        dist_norm = flat_model.dist_grads().norm(dtype=torch.float32)
        if dist.get_world_size() > 1:
            # If grads are distributed, allreduce the partial-norm to get the global norm
            dist_norm_squared = dist_norm * dist_norm
            # Allreduce the square of my norm to get a global sum-of-squares of grads
            dist.all_reduce(dist_norm_squared)
            grad_norm = dist_norm_squared.sqrt()
        else:
            grad_norm = dist_norm

        clip_scaling = (grad_clip / (grad_norm + 1e-6)).clamp_(max=1.0)
    else:
        clip_scaling = None
        grad_norm = None

    # 3) step the optimizer
    optimizer.step(inverse_scale=clip_scaling)

    # 4) all_gather the new low-precision weights
    if mpu.get_data_parallel_world_size() > 1:
        dist.all_gather_into_tensor(
            flat_model.params(),
            flat_model.dist_params(),
            group=mpu.get_data_parallel_group(),
        )

    # 5) clear the grads for the next iteration
    flat_model.zero_grads()
    return grad_norm if grad_norm is not None else torch.tensor(-1)


def get_cosine_lr(
    iter_num: int,
    warmup_iters: int,
    learning_rate: float,
    decay_iters: int,
    min_lr: float,
) -> float:
    """Get the current cosine learning rate."""

    if iter_num < warmup_iters:
        return learning_rate * iter_num / warmup_iters
    if iter_num > decay_iters:
        return min_lr
    decay_ratio = (iter_num - warmup_iters) / (decay_iters - warmup_iters)
    assert 0 <= decay_ratio <= 1
    coeff = 0.5 * (1.0 + math.cos(math.pi * decay_ratio))  # coeff ranges 0..1
    return min_lr + coeff * (learning_rate - min_lr)


@dataclass
class IterationMetrics:
    """A container for training metrics that are logged during a single iteration.

    An iteration is a single forward-backward pass and so may involve multiple
    gradient-accumulation steps.

    The values in the struct are aggregated across model and data parallel groups.
    """

    iter_num: int
    """The iteration number, starting at 0."""

    loss: float
    """The loss measured in this iteration."""

    learning_rate: float
    """Learning rate used in this iteration."""

    iteration_time_s: float
    """Iteration time for this iteration."""

    mfu_estimate_percent: float
    """Estimated MFU for this step (0-100). Will be 0 if MFU cannot be computed."""

    iteration_time_average_s: float
    """The average iteration time up till this iteration."""

    remaining_time_s: float
    """An estimate of the remaining time."""

    total_samples_seen: int
    """The total number of samples seen so far."""

    grad_norm: Optional[float] = None
    """The gradient norm for this iteration, if it was computed."""


LRSchedule = Callable[[int], float]
"""A function that determines the learning rate at a given iteration.

Args:
    iteration: The iteration number.

Returns:
    learning rate at that iteration
"""


@dataclass
class OptimizerConfig(DataClassJsonMixin):
    """Configuration for the optimizer."""

    weight_decay: float = 1e-1
    """The weight decay to use."""
    learning_rate: float = 3e-5
    """The learning rate to use."""
    betas: tuple[float, float] = (0.9, 0.95)
    """The betas to use."""
    eps: float = 1e-8
    """The eps to use with Adam."""
    warmup_iters: int = 0
    """The number of iterations to warmup the learning rate over."""
    decay_iters: int = -1
    """The number of iterations to decay the learning rate over (-1 defaults to train_iters)."""
    min_lr: float = -1
    """The minimum learning rate to use. Defaults to learning_rate (so constant)."""
    load_from_checkpoint: bool = False
    """Load the optimizer state from a checkpoint if set."""

    def __post_init__(self):
        if self.min_lr < 0:
            # Set a constant learning rate if min_lr is not set.
            self.min_lr = self.learning_rate


@dataclass
class TrainOptions(DataClassJsonMixin):
    """Training options."""

    batch_size: int = 1
    """Micro batch size used for training per-GPU."""
    block_size: int = 4096
    """Block size used for training."""
    gradient_accumulation_steps: int = 1
    """Number of micro-batches to accumulate gradients before stepping the optimizer."""
    log_interval: int = 1
    """Number of iterations between logging."""
    start_iteration: int = 0
    """The iteration number to start training from."""
    grad_clip: float = 0.0
    """The maximum norm of the gradient to clip to."""
    max_iters: int = -1
    """Maximum number of training iterations."""
    optimizer: OptimizerConfig = field(default_factory=OptimizerConfig)
    """Configuration for the optimizer."""

    def __post_init__(self):
        self._optimizer_decay_iters_was_unset = self.optimizer.decay_iters
        if self._optimizer_decay_iters_was_unset < 0:
            # If decay iters wasn't set, use the max iters.
            self.optimizer.decay_iters = self.max_iters

    @property
    def per_iteration_batch_size(self) -> int:
        """The per-GPU batch size per iteration."""
        return self.batch_size * self.gradient_accumulation_steps

    @property
    def total_per_iteration_batch_size(self) -> int:
        """The total batch size per iteration over the data parallel group."""
        return (
            self.batch_size
            * self.gradient_accumulation_steps
            * mpu.get_data_parallel_world_size()
        )

    def set_iters_for_epochs(self, max_epochs: int, dataset_size: int):
        """Set the number of iterations for the given number of epochs."""
        assert max_epochs > 0, f"max_epochs must be > 0, got {max_epochs=}"

        iters_per_epoch = dataset_size // self.total_per_iteration_batch_size
        max_iters = max_epochs * iters_per_epoch
        if self.max_iters > 0 and max_iters != self.max_iters:
            logger.warning(f"Overriding {self.max_iters=} with {max_iters=}.")

        self.max_iters = max_iters
        if self._optimizer_decay_iters_was_unset < 0:
            # If decay iters wasn't originally set, use the max iters.
            self.optimizer.decay_iters = max_iters


def flops_bound_iteration_time_s(
    model: torch.nn.Module,
    train_options: TrainOptions,
) -> float:
    """Compute the minimum possible iteration time based on FLOP counting.

    Notes:
    - See the PaLM paper (https://arxiv.org/pdf/2204.02311.pdf) appendix B for the derivation
    - We require that `model` is a Transformer model, otherwise returning 0
    """
    if not isinstance(model, Transformer):
        return 0.0
    matmul_flops = 6 * num_params(model, matmul_only=True)
    args: ModelArgs = model.params
    attention_flops = (
        12
        * train_options.block_size
        * args.n_layers
        * args.dim
        / mpu.get_model_parallel_world_size()
    )
    total_flops = (
        (matmul_flops + attention_flops)
        * train_options.batch_size
        * train_options.gradient_accumulation_steps
        * train_options.block_size
    )
    device_peak_flops = logutils.get_device_peak_flops(torch.cuda.get_device_name())
    if device_peak_flops is None:
        return 0.0
    else:
        return total_flops / device_peak_flops


class IterationResult(NamedTuple):
    """The results of a single training iteration."""

    iter_num: int
    """Iteration number."""

    iter_metrics: IterationMetrics
    """Metrics for this iteration.

    This will be None before the first train iteration.
    """

    predictions: Sequence[InputTargetPair]
    """All inputs and targets for this iteration.

    This is a sequence with one element per micro-batch in the iteration.
    """


def train_loop(
    model: torch.nn.Module,
    loss_fn: LossFn,
    flat_model_state: FlatModelState,
    optimizer: MixedPrecisionAdamW,
    lr_schedule: LRSchedule,
    train_options: TrainOptions,
    train_loader: DataLoader,
    extra_model_train_args: dict | None = None,
    disable_microbatch_weighting: bool = False,
) -> Iterable[IterationResult]:
    """Model-agnostic training loop.

    This loop is responsible for handling the distributed training loop. Assumptions
    going into this loop:

    1. Distributed training has been initialized, the model and optimization state have
       have been moved to the right device and has been cast to the right data type
       (e.g. bfloat16).
    2. There is a single loss function for this model `loss_fn` that we can use for a
       backward pass.
    3. The train loader and the learning rate (`lr_schedule`) have been set up for the
       intended number of max iterations.

    Training loops have a tendency to get messy as custom logic is introduced. This
    implementation assumes that any model specific logic belongs in the model, and any
    task specific logic belongs in the loss function. The training loop yields train
    predictions and metrics after each iteration, and evaluation should be run in the
    calling function: it is up to the user to decide the eval interval.

    If you are looking to do something beyond these assumptions (e.g. RLHF), please
    consider implementing another training loop instead of complecting this one.

    Args:
        model: The torch model you want to train. Please initialize or load its
          checkpoint before providing it as an argument to this function. It is assumed
          that the model is already on the correct device.
        loss_fn: A function used to compute a training loss.
        flat_model_state: The FlatModelState object associated with this model.
        optimizer: The optimizer to use when training this model. It is your
            responsiblity to initialize the optimizer state for this model.
        lr_schedule: A function to determine the learning rate at a given iteration.
        train_options: A dataclass that containing training options.
        train_loader: A torch dataloader that will provide the training data. Consider
            using `DistributedStepCountSampler` as your data sampler to avoid
            introducing non-determinism into distributed training.
        extra_model_train_args: Optional kwargs to pass to the model during the _training_
            forward pass.

    Yields:
        An `IterationResults` object for each iteration.
    """
    # NOTE(arun, 2024/07/02): We changed the training loop to iterate over micro batches
    # from the train data loader. This change requires max_iters to be set.
    assert train_options.max_iters > 0, (
        "max_iters must be set to a positive value. If you want to train for 1 epoch, "
        "use train_options.set_iters_for_epochs(1, len(train_loader))"
    )

    # Calculate how many samples we'll process in each iteration.
    samples_per_iter = (
        train_options.gradient_accumulation_steps
        * mpu.get_data_parallel_world_size()
        * train_options.batch_size
    )
    logger.info("Training for %d iterations.", train_options.max_iters)

    # Note: "SOL" is "speed of light"
    sol_iteration_time_s = flops_bound_iteration_time_s(model, train_options)

    sec_per_iter = logutils.Timer()
    train_loader_it = iter(train_loader)
    for iter_num in range(train_options.start_iteration, train_options.max_iters):
        lr = lr_schedule(iter_num)
        for param_group in optimizer.param_groups:
            param_group["lr"].copy_(lr)

        # Core train loop: forward+backward on the batch and update the weights
        try:
            loss, inputs_and_targets = forward_backward(
                model,
                loss_fn,
                train_loader_it,
                train_options.gradient_accumulation_steps,
                extra_model_train_args=extra_model_train_args,
                disable_microbatch_weighting=disable_microbatch_weighting,
            )
        except StopIteration:
            logger.error(
                "Ran out of data after %d iterations. Training will stop.", iter_num
            )
            break

        grad_norm = flat_model_optimizer_step(
            flat_model_state, optimizer, train_options.grad_clip
        )

        # timing and logging
        dt = sec_per_iter.measure_split()

        iteration_metrics = IterationMetrics(
            # NOTE: this is a CPU-GPU sync point.
            iter_num=iter_num,
            loss=loss,
            learning_rate=lr,
            grad_norm=grad_norm.item(),
            iteration_time_s=dt,
            mfu_estimate_percent=sol_iteration_time_s / dt * 100,
            iteration_time_average_s=sec_per_iter.split_times.average(),
            remaining_time_s=(
                sec_per_iter.split_times.average()
                * (train_options.max_iters - iter_num)
            ),
            total_samples_seen=samples_per_iter * iter_num,
        )
        # Iteration count should correspond to the number of weight updates, so we
        # increment it by one here for the logging steps below.
        iter_num += 1
        yield IterationResult(iter_num, iteration_metrics, inputs_and_targets)

"""Utility functions for distributed communication and coordination in fastbackward."""

import logging
import os
import socket

import torch
import torch.distributed as dist

import research.fastbackward.fs_model_parallel as mpu
from base.caching.lru_cache import lru_cache


def set_distributed_envvars_for_single_gpu():
    os.environ["RANK"] = "0"
    os.environ["LOCAL_RANK"] = "0"
    os.environ["MASTER_ADDR"] = "127.0.0.1"
    os.environ["MASTER_PORT"] = str(find_available_local_port())
    os.environ["WORLD_SIZE"] = "1"
    os.environ["LOCAL_WORLD_SIZE"] = "1"
    os.environ["GROUP_RANK"] = "0"
    os.environ["GROUP_WORLD_SIZE"] = "1"


# "Caching" to allow multiple functions to call this function without trying to
# re-initialize as long as the arguments are the same. This pattern is needed when
# loading stuff within research.
@lru_cache(maxsize=1)
def init_distributed_for_training(
    model_parallel_size: int,
    distributed_backend: str = "nccl",
) -> tuple[int, int]:
    if int(os.environ.get("RANK", -1)) < 0:
        logging.warning(
            "No distributed envvars detected. Running as single-GPU. Consider running with `torchrun` instead."
        )
        set_distributed_envvars_for_single_gpu()

    # This envvar represents a lot of blood, sweat, and tears.
    # The links below provide the techincal context, but the effect of enabling this envvar is to
    # *significantly* reduce peak memory usage during model-parallel runs at no cost.
    # Some relevant links:
    # - My original investigation of memory fragmentation in model-parallel runs:
    #   https://discuss.pytorch.org/t/cuda-allocation-lifetime-for-inputs-to-distributed-all-reduce/191573
    # - The PR that added this envvar, along with a description of how it solves the issue:
    #   https://github.com/pytorch/pytorch/pull/76861
    # - A more general discussion of CUDA streams and the pytorch caching allocator, good background
    #   https://dev-discuss.pytorch.org/t/fsdp-cudacachingallocator-an-outsider-newb-perspective/1486
    os.environ["TORCH_NCCL_AVOID_RECORD_STREAMS"] = "1"

    # Core initialization
    dist.init_process_group(backend=distributed_backend)
    ddp_rank = int(os.environ["RANK"])
    ddp_local_rank = int(os.environ["LOCAL_RANK"])
    my_device = f"cuda:{ddp_local_rank}"
    torch.cuda.set_device(my_device)
    mpu.initialize_model_parallel(model_parallel_size)
    dist.barrier()
    return ddp_rank, ddp_local_rank


def get_local_device() -> torch.device:
    """Get the current device on the current node."""
    # We've set the right local device in `init_distributed_for_training`, so this is
    # all we need to do.
    return torch.device("cuda", index=torch.cuda.current_device())


def is_main_process() -> bool:
    """Check if the current process is the main process."""
    return dist.get_rank() == 0


def find_available_local_port() -> int:
    """Find available local port for torch.distributed."""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(("", 0))
        return s.getsockname()[1]


def dist_argmax(
    x: torch.Tensor, dim: int, group: dist.ProcessGroup, keepdim=False
) -> torch.Tensor:
    """
    Compute the argmax across all processes within a specified process group.

    Args:
    - x (torch.Tensor): The input tensor.
    - dim (int): The dimension over which to perform the argmax.
    - group (torch.distributed.ProcessGroup): The group of processes among which\
        to perform the gathering and argmax.

    Returns:
    - torch.Tensor: The indices of the max values across the given dimension, \
        considering only the shards of the tensor in the specified group.
    """
    world_size = dist.get_world_size(group)

    # Step 1: Compute local argmax and max values
    local_max_values, local_max_indices = x.max(dim=dim, keepdim=True)

    # Prepare tensors for gathering max values and indices across processes in the group
    gathered_max_values = [
        torch.zeros_like(local_max_values) for _ in range(world_size)
    ]
    gathered_max_indices = [
        torch.zeros_like(local_max_indices) for _ in range(world_size)
    ]

    # Step 2: Gather local max values and indices from all processes in the group
    dist.all_gather(gathered_max_values, local_max_values, group=group)
    dist.all_gather(gathered_max_indices, local_max_indices, group=group)

    # Fix indices to be relative to the original tensor
    id_offset = 0
    for indices in gathered_max_indices:
        indices.add_(id_offset)
        id_offset += x.size(dim)

    # Convert list of tensors to a single tensor for manipulation
    gathered_max_values = torch.cat(gathered_max_values, dim=dim)
    gathered_max_indices = torch.cat(gathered_max_indices, dim=dim)

    # Step 3: Find global argmax within the group from the gathered values
    _, global_max_indices = gathered_max_values.max(dim=dim, keepdim=True)
    global_argmax = torch.gather(gathered_max_indices, dim, global_max_indices)

    if not keepdim:
        global_argmax = global_argmax.squeeze(dim)

    return global_argmax

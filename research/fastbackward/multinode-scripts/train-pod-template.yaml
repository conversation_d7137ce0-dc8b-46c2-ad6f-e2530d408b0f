apiVersion: v1
kind: Pod
metadata:
  name: <NAME>
  labels:
    app: <APP>
spec:
  containers:
  - name: llama-nanogpt
    image: <IMAGE>
    command: [ "sleep", "inf" ]

    ports:
    - containerPort: 1234

    resources:
      limits:
        cpu: 96
        memory: 768Gi
        nvidia.com/gpu: 8
        rdma/ib: 1
      requests:
        cpu: 96
        memory: 768Gi
        nvidia.com/gpu: 8
        rdma/ib: 1

    volumeMounts:
    - mountPath: /mnt/efs/augment
      name: augment-shared-mount
    - mountPath: /mnt/tmpfs
      name: temp-ramfs
    - mountPath: /dev/shm
      name: shmem

  volumes:
  - name: augment-shared-mount
    persistentVolumeClaim:
      claimName: aug-cw-las1
  - name: temp-ramfs
    emptyDir:
      sizeLimit: 50Gi
      medium: "Memory"
  - name: shmem
    emptyDir:
      sizeLimit: 1Gi
      medium: "Memory"


  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: topology.kubernetes.io/region
                operator: In
                values:
                  - LAS1

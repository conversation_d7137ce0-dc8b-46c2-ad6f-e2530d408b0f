#!/bin/bash

set -x

K="kubectl --context coreweave --namespace tenant-augment-eng"

if [ $# -ne 4 ]; then
    echo "Usage: $0 <pod-name> <num-nodes> <augment-path> <command>"
    exit 1
fi

pod_name=$1
num_nodes=$2
augment_path=$3
command=$4

# If we have a wandb creds file, upload to pods
if [ -e ~/.netrc ] ; then
  for ((i = 1; i <= $num_nodes; i++)); do
    $K cp ~/.netrc $pod_name-$i:/root/.netrc
  done
fi

service_ip=`$K get service $pod_name-service -o jsonpath='{.spec.clusterIP}'`

pythonpath="$augment_path:$augment_path/research/gpt-neox"
# If pod name is "foo-bar", then the service is called FOO_BAR_SERVICE
service_name="${pod_name^^}_SERVICE"
fastbackward_service_name="${service_name//-/_}"

# Launch jobs
for ((i = 1; i <= $num_nodes; i++)); do
  if [ $i -eq 1 ]; then
    master_addr="127.0.0.1"
  else
    master_addr=$service_ip
  fi

  node_rank=$((i - 1))
  $K exec $pod_name-$i -- bash -c "export PYTHONPATH=$pythonpath \
    && export FASTBACKWARD_SERVICE_NAME=$fastbackward_service_name \
    && cd $augment_path/research/fastbackward \
    && nohup /opt/conda/bin/torchrun --nnodes=$num_nodes --nproc_per_node=8 --node_rank=$node_rank --master_addr=$master_addr --master_port=1234 \
    $command > log-$node_rank.out 2> log-$node_rank.err &" &
done

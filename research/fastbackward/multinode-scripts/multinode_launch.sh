#!/bin/bash
set -e

KAPPLY="kubectl --context coreweave --namespace tenant-augment-eng apply -f"
if [ $# -ne 3 ]; then
    echo "Usage: $0 <GPU-kind> <pod-name> <num-nodes>"
    exit 1
fi

if [ $1 == "A100" ]; then
    pod_type="A100_NVLINK_80GB"
elif [ $1 == "H100" ]; then
    pod_type="H100_NVLINK_80GB"
else
    echo "Unknown GPU: $1"
    exit 1
fi
add_pod_type=".spec.affinity.nodeAffinity.requiredDuringSchedulingIgnoredDuringExecution.nodeSelectorTerms[0].matchExpressions += {\"key\": \"gpu.nvidia.com/class\", \"operator\": \"In\", \"values\": [\"$pod_type\"]}"

git_root=`git rev-parse --show-toplevel`
gpu_image=au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/`cat $git_root/research/environments/gpu_tag.txt`

yq e "
  .metadata.name = \"$2-service\" |
  .spec.selector.app = \"$2-master\"
" train-service-template.yaml > tmp_service.yaml

echo "Launching service $2-service"
$KAPPLY tmp_service.yaml

yq e "
  .spec.containers[0].image = \"$gpu_image\" |
  .metadata.name = \"$2-1\" |
  .metadata.labels.app = \"$2-master\" |
  $add_pod_type
" train-pod-template.yaml > tmp_pod.yaml

echo "Launching pod $2-1"
$KAPPLY tmp_pod.yaml

for ((i = 2; i <= $3; i++)); do
  yq e "
   .spec.containers[0].image = \"$gpu_image\" |
   .metadata.name = \"$2-$i\" |
   del(.metadata.labels) |
   $add_pod_type
  " train-pod-template.yaml > tmp_pod.yaml
  echo "Launching pod $2-$i"
  $KAPPLY tmp_pod.yaml
done
echo "Done."
echo "You can check pod status with \`kubectl get pods | grep $2\`"

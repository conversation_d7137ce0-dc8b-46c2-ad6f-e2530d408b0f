"""Utilities for loading, saving, and manipulating fastbackward checkpoints."""

import contextlib
import glob
import json
import logging
import shutil
from collections.abc import Mapping
from pathlib import Path
from typing import Any, Callable, Optional

import determined as det
import torch
import torch.distributed as dist
from determined.core import (  # Pyright cannot find these classes.
    CheckpointContext,  # type: ignore
    Context,  # type: ignore
    DownloadMode,  # type: ignore
)
from determined.experimental import checkpoint as det_checkpoint
from determined.experimental import client as det_client

import research.fastbackward.fs_model_parallel as mpu
from research.fastbackward.checkpointing.utils import resize_weight
from research.fastbackward.distributed import is_main_process
from research.fastbackward.model import ModelArgs, Transformer

logger = logging.getLogger(__name__)

StateDict = dict[str, torch.Tensor]
"""Type alias for a pytorch state dict."""


DETERMINED_LOCATION: str = "//determined"

"""Describes how to remap a parameter from a checkpoint format to fastbackward.

Elements:
    - checkpoint_name: The name of the parameter in the checkpoint.
    - fastbackward_name: The name of the equivalent parameter in FastBackward.
    - split_dim: If set, the dimension to split along.
"""


def get_checkpoint_location(
    iteration: int | None,
    output_dir: Path | None,
    file_pattern="checkpoint_llama_iteration_{iteration}",
):
    """Get the checkpoint location.

    Args:
        iteration: The current iteration number.
        output_dir: The output directory.
        file_pattern: A file pattern to use when creating the checkpoint filename.
            Use "{iteration}" to replace the iteration number.
    """
    if output_dir:
        if iteration is None:
            return str(output_dir / file_pattern)
        else:
            return str(output_dir / file_pattern.format(iteration=iteration))
    else:
        return DETERMINED_LOCATION


def get_determined_restart_checkpoint(
    det_context: Context | None,
) -> det_checkpoint.Checkpoint | None:
    if det_context is None:
        return None
    cluster_info = det.get_cluster_info()
    assert cluster_info is not None, "cluster_info is None"
    trial_config = cluster_info.trial._config  # pylint: disable=protected-access
    searcher_config = trial_config.get("searcher")
    if searcher_config is None:
        return None
    source_checkpoint_uuid = searcher_config.get("source_checkpoint_uuid")
    if source_checkpoint_uuid is not None:
        return source_checkpoint_uuid
    source_trial_id = searcher_config.get("source_trial_id")
    if source_trial_id is not None:
        trial = det_client.get_trial(source_trial_id)
        checkpoints = [
            c
            for c in trial.list_checkpoints()
            if c.state == det_checkpoint.CheckpointState.COMPLETED
        ]
        sorted_checkpoints = sorted(
            checkpoints, key=lambda c: c.metadata["steps_completed"]
        )
        if len(sorted_checkpoints) > 0:
            return sorted_checkpoints[-1]
    return None


def determined_checkpoint_has_optimizer_state(
    checkpoint: det_checkpoint.Checkpoint,
) -> bool:
    """Check whether a determined checkpoint contains the files with optimizer state.

    In practice, all we do here is look for the optimizer state file of global rank 0.
    If there is a mismatch in terms of number of state files, that will be caught during optimizer
    state loading.
    """
    return "optimizer.00.pth" in checkpoint.resources.keys()


class CheckpointManager:
    """A checkpoint manager for fastbackward."""

    def __init__(
        self,
        distributed_context: Context | None = None,
        mp_world_size: int | None = None,
        mp_rank: int | None = None,
    ) -> None:
        if mp_world_size is None and mp_rank is None:
            self.mp_world_size: int = mpu.get_model_parallel_world_size()
            self.mp_rank: int = mpu.get_model_parallel_rank()
        else:
            assert (
                mp_world_size is not None and mp_rank is not None
            ), "Must set both or neither of mp_{world_size,rank}"
            self.mp_world_size: int = mp_world_size
            self.mp_rank: int = mp_rank

        self.context: CheckpointContext | None = (
            distributed_context.checkpoint if distributed_context else None
        )

        self.last_optimizer_location: str | None = None

    def _make_load_context(
        self,
        checkpoint_location: str,
        download_mode: DownloadMode = DownloadMode.LocalWorkersShareDownload,
    ):
        checkpoint_dir = Path(checkpoint_location)
        if not checkpoint_dir.exists():  # Assume it is a Determined storage ID
            assert self.context is not None
            load_context = self.context.restore_path(checkpoint_location, download_mode)
        else:
            load_context = contextlib.nullcontext(checkpoint_dir)
        return load_context

    def load_state_dict_checkpoint(
        self,
        checkpoint_location: str,
        model_vocab_size: Optional[int] = None,
    ) -> StateDict:
        with self._make_load_context(checkpoint_location) as checkpoint_dir:
            num_checkpoint_files = len(list(checkpoint_dir.glob("consolidated.*.pth")))
            assert num_checkpoint_files == self.mp_world_size, (
                f"Expected {self.mp_world_size} files in {checkpoint_dir}, "
                f"but found {num_checkpoint_files}"
            )
            checkpoint_path = checkpoint_dir / f"consolidated.{self.mp_rank:02d}.pth"
            state_dict = torch.load(checkpoint_path, map_location="cuda")

        if model_vocab_size is not None:
            state_dict["output.weight"] = resize_weight(
                weight=state_dict["output.weight"],
                new_size=model_vocab_size,
                resize_dim=0,
                split_dim=0,
                mp_world_size=self.mp_world_size,
                mp_rank=self.mp_rank,
            )
            state_dict["tok_embeddings.weight"] = resize_weight(
                weight=state_dict["tok_embeddings.weight"],
                new_size=model_vocab_size,
                resize_dim=0,
                split_dim=1,
                mp_world_size=self.mp_world_size,
                mp_rank=self.mp_rank,
            )

        # The upstream llama2 checkpoints have these extra buffers that aren't defined
        # on the model. We remove them (if present) so that strict module loading works.
        state_dict.pop("rope.freqs", None)
        return state_dict

    def _make_save_context(
        self,
        checkpoint_location: str,
        iteration: int,
        shard: bool = True,
        local_dir_must_exist: bool = False,
    ):
        if checkpoint_location == DETERMINED_LOCATION:
            assert self.context is not None
            metadata = {"steps_completed": iteration}
            save_context = self.context.store_path(metadata, shard=shard)
        else:
            checkpoint_dir = Path(checkpoint_location)
            if local_dir_must_exist:
                assert checkpoint_dir.exists()
            else:
                checkpoint_dir.mkdir(exist_ok=True, parents=True)
            save_context = contextlib.nullcontext((checkpoint_dir, None))
        return save_context

    def save_state_dict_checkpoint(
        self,
        checkpoint_dir: Path,
        state_dict: StateDict,
        model_config: dict,
    ):
        # Only data-parallel rank 0 saves the state dict
        if mpu.get_data_parallel_rank() > 0:
            return

        torch.save(
            state_dict,
            checkpoint_dir / f"consolidated.{mpu.get_model_parallel_rank():02d}.pth",
        )

        # Only master process saves model metadata
        if not is_main_process():
            return

        with (checkpoint_dir / "params.json").open("w") as f:
            json.dump(model_config, f, indent=2, default=str)

    def save_files(self, checkpoint_dir: Path, files: Mapping[str, Path]):
        """Save a set of files to a checkpoint directory."""

        for name, local_path in files.items():
            shutil.copy(
                src=str(local_path),
                dst=str(checkpoint_dir / name),
            )

    def save_checkpoint_saved_info(self, checkpoint_dir: Path, metadata: dict):
        if not is_main_process():
            return

        with (checkpoint_dir / "last_saved_info.json").open("w") as f:
            json.dump(metadata, f, indent=2, default=str)

    def save_parallel_optimizer_state(
        self,
        checkpoint_dir: Path,
        optimizer_state_dict: StateDict,
    ):
        rank = dist.get_rank()
        torch.save(
            optimizer_state_dict,
            str(checkpoint_dir / f"optimizer.{rank:02d}.pth"),
        )

    def checkpoint(
        self,
        checkpoint_location: str,
        *,
        iter_num: int,
        model_state_dict: StateDict,
        model_config: dict[str, Any],
        optimizer_state_dict: StateDict | None = None,
        extra_files: Mapping[str, Path] | None = None,
        metadata: dict[str, Any] | None = None,
    ):
        """Save a checkpoint.

        Args:
            checkpoint_location: The location to save the checkpoint.
            iter_num: The current iteration number.
            model_state_dict: The model state dict.
            model_config: The model configuration. Should be sufficient to re-instantiate
                a model from the state dict.
            extra_files: Any extra files to save in the checkpoint. This is typically
                used for tokenizers.
            metadata: Any additional metadata to save.

        Returns:
            The storage location of the checkpoint. If run on Determined, this is a
            Determined storage ID; otherwise, it is a path.
        """
        previous_optimizer_checkpoint = None

        with self._make_save_context(checkpoint_location, iter_num) as (
            checkpoint_dir,
            storage_id,
        ):
            # Identify location by determined ID if available or path
            storage_location = storage_id or str(checkpoint_dir)
            self.save_state_dict_checkpoint(
                checkpoint_dir, model_state_dict, model_config
            )
            self.save_checkpoint_saved_info(
                checkpoint_dir,
                {
                    "iter_num": iter_num,
                }
                | (metadata or {}),
            )
            self.save_files(checkpoint_dir, files=extra_files or {})
            # Block on saving the weights so there isn't a race creating the
            # checkpoint directory
            dist.barrier()

            # Save optimizer state if provided
            if optimizer_state_dict:
                previous_optimizer_checkpoint = self.last_optimizer_location
                self.save_parallel_optimizer_state(checkpoint_dir, optimizer_state_dict)
                self.last_optimizer_location = storage_location

        # Main process deletes all not-most-recent optimizer states
        if previous_optimizer_checkpoint:
            self.delete_optimizer_state(previous_optimizer_checkpoint)
        return storage_location

    def delete_optimizer_state(self, checkpoint_location: str):
        if is_main_process():
            if self.context:
                # pylint: disable=protected-access
                self.context._storage_manager.delete(
                    checkpoint_location, ["**/optimizer.*.pth"]
                )
                # pylint: enable=protected-access
            else:
                optimizer_paths = glob.glob(
                    str(Path(checkpoint_location) / "optimizer.*.pth")
                )
                for path in optimizer_paths:
                    Path(path).unlink()

    def load_parallel_optimizer_state(self, checkpoint_location: str):
        with self._make_load_context(checkpoint_location) as checkpoint_dir:
            rank = dist.get_rank()
            fname = checkpoint_dir / f"optimizer.{rank:02d}.pth"
            if not fname.exists():
                raise RuntimeError(
                    f"No optimizer state found in checkpoint {checkpoint_location}"
                )
            return torch.load(str(fname), map_location="cpu")

    def load_checkpoint_saved_info(self, checkpoint_location: str):
        with self._make_load_context(checkpoint_location) as checkpoint_dir:
            with (checkpoint_dir / "last_saved_info.json").open() as fh:
                return json.load(fh)

    def load_iteration_from_checkpoint(self, checkpoint_location: str) -> int:
        return self.load_checkpoint_saved_info(checkpoint_location)["iter_num"]


def safe_parallel_load(load_fn: Callable[[], StateDict]) -> StateDict:
    # HF checkpoints aren't model parallel-split, so each rank loads the entire model.
    # If every GPU on a system does this at the same time, then it's possible to OOM CPU
    # memory. The fix is to round-robin in model-parallel groups to reduce peak memory
    # usage.
    for i in range(mpu.get_model_parallel_world_size()):
        if mpu.get_model_parallel_rank() == i:
            state_dict = load_fn()
        dist.barrier(group=mpu.get_model_parallel_group())
    return state_dict  # type: ignore


def load_transformer_checkpoint(
    checkpoint_path: Path,
    load_weights: bool = True,
    use_activation_checkpointing: bool = True,
    skip_output: bool = False,
    mp_rank: int | None = None,
    mp_world_size: int | None = None,
) -> Transformer:
    """Load a FastBackward Transformer checkpoint.

    This is a checkpoint saved by the `CheckpointManager` above and in our LM train
    script.

    Args:
        checkpoint_path: The path to the checkpoint.
        load_weights: If set, load the weights from the checkpoint. Otherwise, we'll
            just use the checkpoint to configure the model, but load random weights.
        use_activation_checkpointing: If set, use activation checkpointing.
        skip_output: If set, skip the output layer (typically for embedding models).
        mp_rank: The rank for model parallelism. If None, the rank is inferred from
            the current process.
        mp_world_size: The world size for model parallelism. If None, the rank is
            inferred from the current process.

    Returns:
        A Transformer.
    """
    if mp_world_size is None and mp_rank is None:
        mp_world_size = mpu.get_model_parallel_world_size()
        mp_rank = mpu.get_model_parallel_rank()
    assert (
        mp_world_size is not None and mp_rank is not None
    ), "Must set both or neither of mp_{world_size,rank}"

    assert checkpoint_path is not None, "checkpoint_path must be set for now."
    assert checkpoint_path.exists(), f"{checkpoint_path=} does not exist."
    logger.info(f"[INIT] Loading config from {checkpoint_path}")
    params = json.loads((checkpoint_path / "params.json").read_text())
    model_args = ModelArgs.load_from_dict(params)
    model_args.use_activation_checkpointing = use_activation_checkpointing

    if skip_output:
        model_args.skip_output = True

    # Embedder model args.
    model = Transformer(model_args)
    if load_weights:
        logger.info(f"[INIT] Loading checkpoint from {checkpoint_path}")
        num_checkpoint_files = len(list(checkpoint_path.glob("consolidated.*.pth")))
        assert num_checkpoint_files == mp_world_size, (
            f"Expected {mp_world_size} files in {checkpoint_path}, "
            f"but found {num_checkpoint_files}"
        )
        state_dict = torch.load(checkpoint_path / f"consolidated.{mp_rank:02d}.pth")
        if skip_output:
            del state_dict["output.weight"]
        model.load_state_dict(state_dict)

    return model

"""Utilities for loading NeoX checkpoints."""

import logging
from pathlib import Path
from typing import Optional

import torch
import yaml

from research.fastbackward.checkpointing.checkpointing import StateDict
from research.fastbackward.model import ModelArgs, Transformer
from research.fastbackward.retrieval_models import TransformerEmbedder

logger = logging.getLogger(__name__)

_NEOX_STARCODER_LAYER_REMAPS: list[tuple[str, str, Optional[int]]] = [
    ("input_layernorm.weight", "layers.{}.attention_norm.weight", None),
    ("input_layernorm.bias", "layers.{}.attention_norm.bias", None),
    ("attention.query.weight", "layers.{}.attention.wq.weight", None),
    ("attention.query.bias", "layers.{}.attention.wq.bias", None),
    # The checkpoint actually stores "attention.key_value.weight", but we split it up
    # as attention.key and attention.value
    ("attention.key.weight", "layers.{}.attention.wk.weight", None),
    ("attention.key.bias", "layers.{}.attention.wk.bias", None),
    ("attention.value.weight", "layers.{}.attention.wv.weight", None),
    ("attention.value.bias", "layers.{}.attention.wv.bias", None),
    ("attention.dense.weight", "layers.{}.attention.wo.weight", None),
    ("attention.dense.bias", "layers.{}.attention.wo.bias", None),
    ("post_attention_layernorm.weight", "layers.{}.ffn_norm.weight", None),
    ("post_attention_layernorm.bias", "layers.{}.ffn_norm.bias", None),
    ("mlp.dense_h_to_4h.weight", "layers.{}.feed_forward.w1.weight", None),
    ("mlp.dense_h_to_4h.bias", "layers.{}.feed_forward.w1.bias", None),
    ("mlp.dense_4h_to_h.weight", "layers.{}.feed_forward.w2.weight", None),
    ("mlp.dense_4h_to_h.bias", "layers.{}.feed_forward.w2.bias", None),
]


def load_starcoder_neox_state_dict(
    checkpoint_path: Path,
    mp_rank: int = 0,
    mp_size: int = 1,
    is_embedding_model: bool = False,
) -> StateDict:
    """Load and transform the weights for NeoX Starcoder models.

    Args:
        checkpoint_path: Where the checkpoint is stored.
        mp_rank: The model parallel rank we want to load.
        mp_size: The model parallel world size. Must be the same as the world size of
            the checkpoint.
        is_embedding_model: If set, load weights for TransformerEmbedder instead.

    Returns:
        A state dict with the weights for a Tranformer or TransformerEmbedder.
    """
    assert checkpoint_path.exists() and checkpoint_path.is_dir()
    # Figure out model parallelism
    actual_mp_size = sum(
        1 for _ in checkpoint_path.glob("layer_00-model_*-model_states.pt")
    )
    assert (
        actual_mp_size == mp_size
    ), f"Expected {mp_size=} model parallel files, but found {actual_mp_size=}"

    # NeoX stores everything in separate layer files.
    # The layer numbering is *weird*; if $n$ is the "max" layer number:
    # - layer_00 is the embedding layer,
    # - layer_01 doesn't exist,
    # - layer_02 -- layer_{n-2} are the actual Tranformer layers.
    # - layer_{n-1} is the final norm layer,
    # - layer_{n} is the final output layer.
    # Processing layers in order is an easy way to do the right thing.
    layer_files = sorted(
        checkpoint_path.glob(f"layer_*-model_{mp_rank:02d}-model_states.pt")
    )

    sd = {}
    layer_dict = torch.load(layer_files[0])
    # This dummy scalar is a hack we introduced to support training of otherwise
    # frozen models in NeoX; it's not part of the checkpoint, but we need to add it .
    if "dummy" in layer_dict:
        scale = layer_dict["dummy"]
    else:
        scale = 1
    sd["tok_embeddings.weight"] = scale * layer_dict["word_embeddings.weight"]
    sd["pos_embeddings.weight"] = scale * layer_dict["position_embeddings.weight"]

    for i, layer_file in enumerate(layer_files[1:-2]):
        print("Loading layer", i)
        layer_dict = torch.load(layer_file)

        # Split the key_value weight into separate key and value weights.
        (
            layer_dict["attention.key.weight"],
            layer_dict["attention.value.weight"],
        ) = layer_dict["attention.key_value.weight"].chunk(2, dim=0)
        (
            layer_dict["attention.key.bias"],
            layer_dict["attention.value.bias"],
        ) = layer_dict["attention.key_value.bias"].chunk(2, dim=0)

        for key, remap, _ in _NEOX_STARCODER_LAYER_REMAPS:
            assert key in layer_dict, (key, layer_dict.keys())
            sd[remap.format(i)] = layer_dict[key]

    layer_dict = torch.load(layer_files[-2])
    sd["norm.weight"] = layer_dict["norm.weight"]
    sd["norm.bias"] = layer_dict["norm.bias"]

    # For embedding models like StarEthanol, we store an embedding projection in the
    # final layer instead of a vocabulary projection.
    # The language models do not have a bias in the final layer, but the embedding
    # models do.
    layer_dict = torch.load(layer_files[-1])

    if not is_embedding_model:
        sd["output.weight"] = layer_dict["final_linear.weight"]
        return sd
    else:
        # TransformerEmbedders nest the language model.
        sd = {
            **{f"lm.{key}": value for key, value in sd.items()},
            "output_projection.weight": layer_dict["projection.weight"],
            "output_projection.bias": layer_dict["projection.bias"],
        }
        return sd


def get_starcoder_neox_args(checkpoint_path: Path) -> ModelArgs:
    """Get the model args from a StarCoder checkpoint."""
    assert (checkpoint_path.parent / "config.yml").exists()
    config = yaml.safe_load((checkpoint_path.parent / "config.yml").open())
    # Normalize keys of the config
    config = {k.replace("-", "_"): v for k, v in config.items()}

    return ModelArgs(
        dim=config["hidden_size"],
        n_layers=config["num_layers"],
        n_heads=config["num_attention_heads"],
        # NB: This looks wrong and feels wrong, but "make_vocab_size_divisible_by" was
        # the only way we could set custom vocab sizes with NeoX...
        vocab_size=config["make_vocab_size_divisible_by"],
        max_seq_len=config["max_position_embeddings"],
        skip_output=config.get("contrastive_proj_dim") is not None,
        ffn_type="mlp",
        bias="attn_mlp",
        norm_type="layernorm",
        pos_embed_type="absolute",
        n_kv_heads=1,
    )


def get_starethanol_neox_args(checkpoint_path: Path) -> TransformerEmbedder.Config:
    """Get the NeoX-style model args from a StarEthanol checkpoint."""
    assert (checkpoint_path.parent / "config.yml").exists()
    config = yaml.safe_load((checkpoint_path.parent / "config.yml").open())
    # Normalize keys of the config
    config = {k.replace("-", "_"): v for k, v in config.items()}

    return TransformerEmbedder.Config(
        input_dim=config["hidden_size"],
        output_projection_dim=config["contrastive_proj_dim"],
        with_output_bias=True,
    )


def load_starethanol_checkpoint(
    checkpoint_path: Path,
    load_weights: bool = True,
    use_activation_checkpointing: bool = True,
) -> TransformerEmbedder:
    """Load a StarEthanol checkpoint as a TransformerEmbedder.

    Args:
        checkpoint_path: The path to the checkpoint.
        load_weights: If set, load the weights from the checkpoint. Otherwise, we'll
            just use the checkpoint to configure the model, but load random weights.
        use_activation_checkpointing: If set, use activation checkpointing.

    Returns:
        A TransformerEmbedder.
    """
    assert checkpoint_path is not None, "checkpoint_path must be set for now."
    assert checkpoint_path.exists(), f"{checkpoint_path=} does not exist."
    logger.info(f"[INIT] Loading config from {checkpoint_path}")
    model_args = get_starcoder_neox_args(checkpoint_path)
    embedder_cfg = get_starethanol_neox_args(checkpoint_path)
    assert model_args.skip_output, "Embedder checkpoints must skip-output."

    model_args.use_activation_checkpointing = use_activation_checkpointing

    # Embedder model args.
    embedder_model = TransformerEmbedder(Transformer(model_args), embedder_cfg)
    if load_weights:
        logger.info(f"[INIT] Loading checkpoint from {checkpoint_path}")
        state_dict = load_starcoder_neox_state_dict(
            checkpoint_path,
            is_embedding_model=True,
        )
        embedder_model.load_state_dict(state_dict)

    return embedder_model


def load_starcoder_checkpoint(
    checkpoint_path: Path,
    load_weights: bool = True,
    use_activation_checkpointing: bool = True,
    skip_output: bool = False,
) -> Transformer:
    """Load a StarCoder checkpoint as a Transformer.

    Args:
        checkpoint_path: The path to the checkpoint.
        load_weights: If set, load the weights from the checkpoint. Otherwise, we'll
            just use the checkpoint to configure the model, but load random weights.
        use_activation_checkpointing: If set, use activation checkpointing.
        skip_output: If set, skip the output layer (typically for embedding models).

    Returns:
        A Transformer.
    """
    assert checkpoint_path is not None, "checkpoint_path must be set for now."
    assert checkpoint_path.exists(), f"{checkpoint_path=} does not exist."
    logger.info(f"[INIT] Loading config from {checkpoint_path}")
    model_args = get_starcoder_neox_args(checkpoint_path)
    model_args.use_activation_checkpointing = use_activation_checkpointing

    if skip_output:
        model_args.skip_output = True

    # Embedder model args.
    model = Transformer(model_args)
    if load_weights:
        logger.info(f"[INIT] Loading checkpoint from {checkpoint_path}")
        state_dict = load_starcoder_neox_state_dict(
            checkpoint_path,
            is_embedding_model=False,
        )
        if skip_output:
            del state_dict["output.weight"]
        model.load_state_dict(state_dict)

    return model

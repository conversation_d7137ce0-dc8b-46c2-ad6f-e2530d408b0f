"""Utilities for checkpointing."""

from __future__ import annotations

import torch
import torch.distributed as dist

import research.fastbackward.fs_model_parallel as mpu
from research.fastbackward import model as fbw_model


def split_weight_for_model_parallel(
    weight: torch.Tensor, split_dim: int | None, world_size: int, rank: int
):
    """
    Splits a weight tensor across ranks in a model-parallel group or subgroup.

    Args:
        weight: The weight tensor to split.
        split_dim: The dimension to split along. If None, the weight is not split.
        world_size: The number of unique per-process weights.
        rank: The rank of the current process in the group or subgroup.

    Returns:
        A tensor containing the split weight for the current rank.
    """
    if split_dim is None or world_size == 1:
        return weight
    assert weight.size(split_dim) % world_size == 0
    return weight.chunk(world_size, dim=split_dim)[rank].detach().clone()


def stack_weights(
    state_dicts: list[dict[str, torch.Tensor]],
    key: str,
    dim: int | None,
    replication_size: int,
):
    """Stacks weights from a list of state dictionaries.

    Args:
        state_dicts: A list of state dictionaries.
        key: The key to stack weights for.
        dim: The dimension to stack along. If None, the weights are not stacked.
        replication_size: The number of times the weights are replicated.

    Returns:
        A tensor containing the stacked weights.
    """
    if dim is not None:
        consolidated_weight = torch.cat(
            [sd[key] for sd in state_dicts[::replication_size]], dim=dim
        )
    else:
        consolidated_weight = torch.stack(
            [sd[key] for sd in state_dicts[::replication_size]], dim=0
        ).mean(dim=0)
    # Clean up memory. Even if we only stacked a subset of the weights, we can still
    # clean up the rest of the weights, since they would be duplicates.
    for sd in state_dicts:
        del sd[key]
    return consolidated_weight.detach().clone()


def resize_weight(
    weight: torch.Tensor,
    new_size: int,
    resize_dim: int,
    split_dim: int,
    mp_world_size: int | None = None,
    mp_rank: int | None = None,
) -> torch.Tensor:
    """Resize a weight tensor to the new size for a model parallel model.

    The function assumes that new size is larger or equal than the original size.
    New weights are initialized with zeros.

    Args:
        weight: The weight tensor to resize.
        new_size: The new size of the dimension to resize.
        resize_dim: The dimension to resize.
        split_dim: The dimension we split the weight for model parallelism.
        mp_world_size: The number of model parallel processes.
        mp_rank: The rank of the current model parallel process.

    Returns:
        The resized weight tensor.

    Raises:
        ValueError: If the new size is smaller than the original size.
    """
    mp_world_size = mp_world_size or mpu.get_model_parallel_world_size()
    mp_rank = mp_rank or mpu.get_model_parallel_rank()
    weight_device = weight.device
    weight_gpu = weight.to("cuda")
    global_weights = [torch.zeros_like(weight_gpu) for _ in range(mp_world_size)]
    dist.all_gather(global_weights, weight_gpu, group=mpu.get_model_parallel_group())
    global_weight = torch.cat(global_weights, dim=split_dim).to(weight_device)
    assert global_weight.shape[split_dim] == weight.shape[split_dim] * mp_world_size

    old_size = global_weight.shape[resize_dim]
    if old_size == new_size:
        return weight
    elif old_size > new_size:
        raise ValueError(
            f"Cannot resize {old_size} to {new_size} "
            f"because it is larger than the original size."
        )

    # Make a copy of the weights shape so we can set the shape of padding tensor.
    padding_shape = list(global_weight.shape)
    padding_shape[resize_dim] = new_size - old_size
    padding = torch.zeros(padding_shape, dtype=weight.dtype, device=weight.device)

    # Add padding and split
    new_global_weight = torch.cat([global_weight, padding], dim=resize_dim)

    assert new_global_weight.shape[resize_dim] == new_size
    assert new_global_weight.shape[split_dim] % mp_world_size == 0

    new_global_weight_split = torch.tensor_split(
        new_global_weight, mp_world_size, dim=split_dim
    )
    assert len(new_global_weight_split) == mp_world_size
    new_weight = new_global_weight_split[mp_rank]
    return new_weight


def _get_trunk_weights_mp_split(
    model_args: fbw_model.ModelArgs,
    mp_world_size: int,
) -> list[tuple[str, int | None, int]]:
    """Get trunk weights that need to be split for model parallelism.

    Args:
        model_args: The model arguments. Needed to determine the normalization type.
        mp_world_size: The number of model parallel processes.

    Returns:
        A list of tuples (name, axis, replication_size)), where:
            name: The name of the weight.
            axis: The axis to split along. If None, the weight is not split.
            replication_size: The number of times the weight is replicated.
    """
    weights_mp_split = [
        ("tok_embeddings.weight", 1, 1),
        ("norm.weight", None, mp_world_size),
        ("output.weight", 0, 1),
    ]
    if model_args.pos_embed_type == "absolute":
        weights_mp_split.append(("pos_embeddings.weight", 1, 1))
    if model_args.norm_type == "layernorm":
        weights_mp_split.append(("norm.bias", None, mp_world_size))
    return weights_mp_split


def _get_attn_weights_mp_split(
    attn_config: fbw_model.GenericAttnSpec | fbw_model.DeepSeekV2MLASpec,
    mp_world_size: int,
) -> list[tuple[str, int | None, int]]:
    """Get the attention weights that need to be split for model parallelism."""
    if isinstance(attn_config, fbw_model.GenericAttnSpec):
        n_kv_heads = attn_config.n_kv_heads or attn_config.n_heads
        kv_group_size = max(mp_world_size // n_kv_heads, 1)
        weights_per_layer_mp_split: list[tuple[str, int | None, int]] = [
            ("layers.{layer_num}.attention.wq.weight", 0, 1),
            ("layers.{layer_num}.attention.wk.weight", 0, kv_group_size),
            ("layers.{layer_num}.attention.wv.weight", 0, kv_group_size),
            ("layers.{layer_num}.attention.wo.weight", 1, 1),
        ]
        if attn_config.bias:
            weights_per_layer_mp_split.extend(
                [
                    ("layers.{layer_num}.attention.wq.bias", 0, 1),
                    ("layers.{layer_num}.attention.wk.bias", 0, kv_group_size),
                    ("layers.{layer_num}.attention.wv.bias", 0, kv_group_size),
                    ("layers.{layer_num}.attention.wo.bias", None, mp_world_size),
                ]
            )
        elif attn_config.qkv_bias:
            weights_per_layer_mp_split.extend(
                [
                    ("layers.{layer_num}.attention.wq.bias", 0, 1),
                    ("layers.{layer_num}.attention.wk.bias", 0, kv_group_size),
                    ("layers.{layer_num}.attention.wv.bias", 0, kv_group_size),
                ]
            )
    elif isinstance(attn_config, fbw_model.DeepSeekV2MLASpec):
        weights_per_layer_mp_split: list[tuple[str, int | None, int]] = [
            ("layers.{layer_num}.attention.q_proj.weight", 0, 1),
            (
                "layers.{layer_num}.attention.kv_a_proj_with_mqa.weight",
                None,
                mp_world_size,
            ),
            (
                "layers.{layer_num}.attention.kv_a_layernorm.weight",
                None,
                mp_world_size,
            ),
            ("layers.{layer_num}.attention.kv_b_proj.weight", 0, 1),
            ("layers.{layer_num}.attention.o_proj.weight", 1, 1),
        ]
        if attn_config.bias:
            weights_per_layer_mp_split.extend(
                [
                    (
                        "layers.{layer_num}.attention.kv_a_proj_with_mqa.bias",
                        None,
                        mp_world_size,
                    ),
                    ("layers.{layer_num}.attention.o_proj.bias", None, mp_world_size),
                ]
            )
    else:
        raise NotImplementedError(f"Unsupported attention config: {attn_config}")
    return weights_per_layer_mp_split


def _get_ffn_weights_mp_split(
    ffn_config: fbw_model.MlpSpec | fbw_model.SwiGLUSpec | fbw_model.DeepSeekV2MoESpec,
    mp_world_size: int,
) -> list[tuple[str, int | None, int]]:
    """Get the FFN weights that need to be split for model parallelism."""
    if isinstance(ffn_config, fbw_model.MlpSpec):
        weights_per_layer_mp_split: list[tuple[str, int | None, int]] = [
            ("layers.{layer_num}.feed_forward.w1.weight", 0, 1),
            ("layers.{layer_num}.feed_forward.w2.weight", 1, 1),
        ]
        if ffn_config.bias:
            weights_per_layer_mp_split.extend(
                [
                    ("layers.{layer_num}.feed_forward.w1.bias", 0, 1),
                    ("layers.{layer_num}.feed_forward.w2.bias", None, mp_world_size),
                ]
            )
    elif isinstance(ffn_config, fbw_model.SwiGLUSpec):
        weights_per_layer_mp_split: list[tuple[str, int | None, int]] = [
            ("layers.{layer_num}.feed_forward.w1.weight", 0, 1),
            ("layers.{layer_num}.feed_forward.w2.weight", 1, 1),
            ("layers.{layer_num}.feed_forward.w3.weight", 0, 1),
        ]
        if ffn_config.bias:
            weights_per_layer_mp_split.extend(
                [
                    ("layers.{layer_num}.feed_forward.w1.bias", 0, 1),
                    ("layers.{layer_num}.feed_forward.w2.bias", None, mp_world_size),
                    ("layers.{layer_num}.feed_forward.w3.bias", 0, 1),
                ]
            )
    elif isinstance(ffn_config, fbw_model.DeepSeekV2MoESpec):
        weights_per_layer_mp_split: list[tuple[str, int | None, int]] = [
            ("layers.{layer_num}.feed_forward.shared_experts.w1.weight", 0, 1),
            ("layers.{layer_num}.feed_forward.shared_experts.w2.weight", 1, 1),
            ("layers.{layer_num}.feed_forward.shared_experts.w3.weight", 0, 1),
        ]
        weights_per_layer_mp_split.extend(
            [("layers.{layer_num}.feed_forward.gate_weight", None, mp_world_size)]
        )
        if ffn_config.use_dense_moe:
            weights_per_layer_mp_split.extend(
                [
                    ("layers.{layer_num}.feed_forward.w1", 0, 1),
                    ("layers.{layer_num}.feed_forward.w2", 0, 1),
                    ("layers.{layer_num}.feed_forward.w3", 0, 1),
                ]
            )
        else:
            weights_per_layer_mp_split.extend(
                [
                    ("layers.{layer_num}.feed_forward.w1", 0, 1),
                    ("layers.{layer_num}.feed_forward.w2", 0, 1),
                    ("layers.{layer_num}.feed_forward.w3", 0, 1),
                ]
            )
            # NOTE(Xuanyi): Please keep this comment here as it is for the "correct" sparse implementation
            # for inference, however, it will raise some nccl issues during the training.
            #
            # for iexpert in range(ffn_config.n_routed_experts):
            #     cur_key = "layers.{layer_num}.feed_forward.experts." + str(iexpert)
            #     weights_per_layer_mp_split.extend(
            #         [
            #             (cur_key + ".w1.weight", 0, 1),
            #             (cur_key + ".w2.weight", 1, 1),
            #             (cur_key + ".w3.weight", 0, 1),
            #         ]
            #     )
    else:
        raise ValueError(f"Unsupported FFN config: {ffn_config}")
    return weights_per_layer_mp_split


def get_model_weights_mp_split(
    model_args: fbw_model.ModelArgs,
    mp_world_size: int,
) -> list[tuple[str, int | None, int]]:
    """Get the weights that need to be split for model parallelism.

    Args:
        model_args: The model arguments containing the number of layers.
        mp_world_size: The number of model parallel processes.

    Returns:
        A list of tuples containing the weight name, the dimension to split, and the
        replication size.
    """
    model_args = fbw_model.correct_model_args(model_args)
    weights_mp_split = _get_trunk_weights_mp_split(model_args, mp_world_size)
    # For each transformer block, we figure out the ffn and attention layer split spec.
    for layer_num in range(model_args.n_layers):
        # Convert 2 norm layers.
        weights_per_layer_mp_split: list[tuple[str, int | None, int]] = [
            ("layers.{layer_num}.attention_norm.weight", None, mp_world_size),
            ("layers.{layer_num}.ffn_norm.weight", None, mp_world_size),
        ]
        if model_args.norm_type == "layernorm":
            weights_per_layer_mp_split.extend(
                [
                    ("layers.{layer_num}.attention_norm.bias", None, mp_world_size),
                    ("layers.{layer_num}.ffn_norm.bias", None, mp_world_size),
                ]
            )
        # Convert the attention layer.
        assert model_args.attn_config is not None
        weights_per_layer_mp_split.extend(
            _get_attn_weights_mp_split(model_args.attn_config, mp_world_size)
        )
        # Convert the feed-forward layer.
        if layer_num == 0:
            ffn_config = model_args.first_layer_ffn_config
        else:
            ffn_config = model_args.ffn_config
        assert ffn_config is not None
        weights_per_layer_mp_split.extend(
            _get_ffn_weights_mp_split(ffn_config, mp_world_size)
        )
        weights_mp_split.extend(
            [
                (name.format(layer_num=layer_num), dim, replication_size)
                for (name, dim, replication_size) in weights_per_layer_mp_split
            ]
        )
    return weights_mp_split


def split_consolidated_checkpoint(
    model_args: fbw_model.ModelArgs,
    consolidated_weights: dict[str, torch.Tensor],
    mp_world_size: int,
    mp_rank: int,
) -> dict[str, torch.Tensor]:
    """
    Splits a consolidated checkpoint and returns the state_dict for `mp_rank`.

    Args:
        model_args: The model arguments for the model itself.
        consolidated_weights: A dictionary containing the consolidated weights.
        mp_world_size: The total number of model parallel ranks.
        mp_rank: The current model parallel rank.

    Returns:
        State dict for the current model-parallel rank.
    """
    model_args = fbw_model.correct_model_args(model_args)
    dst_weights = {}
    for key, dim, replication_size in get_model_weights_mp_split(
        model_args, mp_world_size
    ):
        assert mp_world_size % replication_size == 0
        world_size = mp_world_size // replication_size
        rank = mp_rank // replication_size
        dst_weights[key] = split_weight_for_model_parallel(
            consolidated_weights[key], dim, world_size, rank
        )
    return dst_weights


def merge_model_parallel_consolidated_checkpoints(
    model_args: fbw_model.ModelArgs,
    weights: list[dict[str, torch.Tensor]],
):
    """Merges multiple model parallel state_dicts into a single consolidated state dict.

    Args:
        model_args: The model arguments containing the number of layers.
        weights: A list of state_dicts, one for each model parallel rank.

    Returns:
        A dictionary containing the consolidated weights.
    """
    model_args = fbw_model.correct_model_args(model_args)
    mp_world_size = len(weights)
    consolidated_weights = {}
    for key, dim, replication_size in get_model_weights_mp_split(
        model_args, mp_world_size
    ):
        consolidated_weights[key] = stack_weights(weights, key, dim, replication_size)
    return consolidated_weights

"""Utilities specifically for inference / generation with fastbackward models."""

import os
from pathlib import Path

import torch
import torch.distributed as dist
import torch.multiprocessing as mp

import research.fastbackward.fs_model_parallel as mpu
from research.fastbackward.distributed import find_available_local_port
from research.fastbackward.model import ModelArgs, Transformer


def _parallel_init(rank: int, world_size: int, master_ip: str, master_port: int):
    # Trick to save on Pytorch memory.
    # See https://discuss.pytorch.org/t/cuda-allocation-lifetime-for-inputs-to-distributed-all-reduce/191573
    # and https://github.com/augmentcode/augment/blob/main/research/fastbackward/distributed.py#L31
    os.environ["TORCH_NCCL_AVOID_RECORD_STREAMS"] = "1"
    dist.init_process_group(
        backend="nccl",
        init_method=f"tcp://{master_ip}:{master_port}",
        rank=rank,
        world_size=world_size,
    )
    mpu.initialize_model_parallel(world_size)


def _parallel_destroy():
    mpu.destroy_model_parallel()
    dist.destroy_process_group()


# TODO(carl): extend this to support generic checkpoint loading (ie, include HF)
def _load_model(
    model_args: ModelArgs,
    checkpoint_file: str,
):
    # NOTE(xuanyi): we skip the init here to speed up the model instantiation,
    # and it is also unnecessary since we are loading the weights from the checkpoint.
    model = Transformer(
        model_args,
        skip_init=True,
    )
    model.to(device=torch.device("cpu"), dtype=torch.bfloat16)
    state_dict = torch.load(checkpoint_file, map_location="cpu")
    state_dict.pop("rope.freqs", None)
    model.load_state_dict(state_dict, strict=True)
    model.to(device=torch.device("cuda"), dtype=torch.bfloat16)
    return model


def model_parallel_generate_func(
    rank: int,
    world_size: int,
    master_port: int,
    recv_q: mp.SimpleQueue,
    send_q: mp.SimpleQueue,
    model_args: ModelArgs,
    checkpoint_file: str,
):
    """Function for child processes in the parallel runner."""
    my_device = f"cuda:{rank}"
    torch.cuda.set_device(my_device)
    _parallel_init(rank, world_size, "127.0.0.1", master_port)
    model = _load_model(model_args, checkpoint_file)
    # Send a "ready" message after the model is loaded
    send_q.put(0)

    while True:
        x_recv, start_pos = recv_q.get()
        assert isinstance(x_recv, torch.Tensor)
        assert isinstance(start_pos, int)
        x = torch.empty_like(x_recv, device=my_device)
        x.copy_(x_recv)
        # Empty tensor is a signal to exit
        if x.numel() == 0:
            done_signal = torch.empty(0)
            send_q.put(done_signal)
            break
        try:
            result = model.generate(x, start_pos)
            send_q.put(result)
        except Exception as e:
            send_q.put(e)
            raise e

    _parallel_destroy()


class ParallelTransformerRunner:
    """Wrapper class for creating subprocesses to run a model-parallel Transformer object."""

    def __init__(self, model_parallel_size: int = 1):
        assert model_parallel_size >= 1
        assert model_parallel_size <= torch.cuda.device_count()
        self.model_parallel_size = model_parallel_size
        self.model = None
        self.procs = []
        self.send_qs = []
        self.recv_qs = []

    def load_model(
        self,
        model_args: ModelArgs,
        checkpoint_dir: Path,
    ):
        # If we are using FastBackward embedding model, it current initialize model parallel groups
        # with one in the master process. If we are not using FastBackward embedding model, the parallelsim
        # shouldn't be initialized yet.
        # For the FastBackward Transformer model, it will initialize the distributed groups in child processes.
        # TODO: if we also use a "runner" for FastBackward embedding model, we can make a stronger assertion
        # of assert not dist.is_initialized.
        if dist.is_initialized():
            assert dist.get_world_size() == 1
            assert mpu.initialize.model_parallel_is_initialized()
            assert mpu.get_model_parallel_world_size() == 1
        else:
            assert not mpu.initialize.model_parallel_is_initialized()
        master_port = find_available_local_port()
        # First, create processes (one per rank) and I/O queues for each
        ctx = mp.get_context("spawn")
        for rank in range(self.model_parallel_size):
            ckpt_file = (checkpoint_dir / f"consolidated.{rank:02d}.pth").as_posix()
            send_q = ctx.SimpleQueue()
            recv_q = ctx.SimpleQueue()
            proc = ctx.Process(
                target=model_parallel_generate_func,
                args=(
                    rank,
                    self.model_parallel_size,
                    master_port,
                    send_q,
                    recv_q,
                    model_args,
                    ckpt_file,
                ),
                daemon=True,  # Make sure children exit if parent terminates
            )
            proc.start()
            self.send_qs.append(send_q)
            self.recv_qs.append(recv_q)
            self.procs.append(proc)

        # Block until each child sends a "ready" message (model is loaded)
        for q in self.recv_qs:
            proc_ready = q.get()
            assert proc_ready == 0

    def generate(self, x: torch.Tensor, start_pos: int):
        assert len(self.procs) == self.model_parallel_size
        for q in self.send_qs:
            q.put((x, start_pos))
        results = []
        for q in self.recv_qs:
            this_result = q.get()
            if isinstance(this_result, Exception):
                raise this_result
            results.append(this_result)
        # Each results is the same, so copy the 0th one
        to_return = torch.empty_like(results[0], device=x.device)
        return to_return.copy_(results[0])

    def unload_model(self):
        # An empty tensor signals child process to exit
        exit_signal = torch.empty(0)
        for q in self.send_qs:
            q.put((exit_signal, 0))
        # See NOTE below for why we wait on a return signal from processes before exiting.
        for q in self.recv_qs:
            q.get()
        # NOTE: because of https://github.com/pytorch/pytorch/issues/115366, we cannot `join()`
        # on the child processes, since they might hang if they called into a compile'd function.
        # TODO: once we update to pytorch 2.2, we should revist this and remove the done signal
        # and revert back to a join().
        for p in self.procs:
            p.kill()
        self.procs = []
        self.send_qs = []
        self.recv_qs = []

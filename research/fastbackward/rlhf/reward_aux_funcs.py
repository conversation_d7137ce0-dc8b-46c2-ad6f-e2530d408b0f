"""The auxiliary reward functions."""

import random

import torch

from base.tokenizers.tokenizer import Tokenizer

PROMPT_TARGET_TOKEN_TYPE = tuple[list[int], list[int]]


def build_xy_tensor(
    prompt_tokens: list[int],
    target_tokens: list[int],
    eos: int | None,
    padding: int,
    seqlen: int,
):
    """Builds the x and y input tensors for Transformer."""
    if eos is not None:
        target_tokens.append(eos)
    x = prompt_tokens + target_tokens
    y = [-1] * (len(prompt_tokens) - 1) + target_tokens + [-1]
    assert len(x) == len(y), f"{len(x)=}, {len(y)=}"
    assert len(x) <= seqlen, f"{len(x)=}, {seqlen=}"
    assert len(y) <= seqlen, f"{len(y)=}, {seqlen=}"
    x += (seqlen - len(x)) * [padding]
    y += (seqlen - len(y)) * [-1]
    return torch.tensor(x, dtype=torch.int64), torch.tensor(y, dtype=torch.int64)


def build_pairwise_reward_tokens(
    sample: dict,
    tokenizer: Tokenizer,
    max_response_tokens: int = 64,
    shuffle: bool = True,
) -> PROMPT_TARGET_TOKEN_TYPE:
    assert "prompt_tokens" in sample, f"{sample.keys()=}"
    assert "pos_tokens" in sample, f"{sample.keys()=}"
    assert "neg_tokens" in sample, f"{sample.keys()=}"
    special_tokens = tokenizer.special_tokens
    pos_tokens = sample["pos_tokens"][:max_response_tokens].copy()
    neg_tokens = sample["neg_tokens"][:max_response_tokens].copy()
    if shuffle and random.random() < 0.5:
        prompt_tokens = (
            sample["prompt_tokens"]
            + [special_tokens.reward_signal]
            + tokenizer.tokenize_safe("Option 1")
            + [special_tokens.reward_signal]
            + neg_tokens
            + [special_tokens.reward_signal]
            + tokenizer.tokenize_safe("Option 2")
            + [special_tokens.reward_signal]
            + pos_tokens
            + [special_tokens.reward_signal]
        )
        response_tokens = tokenizer.tokenize_safe("score 2, score 1")
    else:
        prompt_tokens = (
            sample["prompt_tokens"]
            + [special_tokens.reward_signal]
            + tokenizer.tokenize_safe("Option 1")
            + [special_tokens.reward_signal]
            + pos_tokens
            + [special_tokens.reward_signal]
            + tokenizer.tokenize_safe("Option 2")
            + [special_tokens.reward_signal]
            + neg_tokens
            + [special_tokens.reward_signal]
        )
        response_tokens = tokenizer.tokenize_safe("score 1, score 2")
    return prompt_tokens, response_tokens


def build_singular_reward_tokens(
    sample: dict,
    tokenizer: Tokenizer,
    seqlen: int,
) -> tuple[PROMPT_TARGET_TOKEN_TYPE | None, PROMPT_TARGET_TOKEN_TYPE]:
    special_tokens = tokenizer.special_tokens
    prompt_tokens: list[int] = sample["prompt_tokens"]
    neg_tokens: list[int] = sample["neg_tokens"]
    if len(prompt_tokens) + len(neg_tokens) + 2 > seqlen:
        neg_tokens = neg_tokens[: seqlen - len(prompt_tokens) - 2]

    pos_tokens: list[int] = sample["pos_tokens"]
    if pos_tokens == [-1]:  # does not have positive tokens
        return None, (
            prompt_tokens + neg_tokens,
            [special_tokens.bad],
        )
    else:
        # We need 2 tokens for good/bad and eod
        if len(prompt_tokens) + len(pos_tokens) + 2 > seqlen:
            pos_tokens = pos_tokens[: seqlen - len(prompt_tokens) - 2]
        return (prompt_tokens + pos_tokens, [special_tokens.good]), (
            prompt_tokens + neg_tokens,
            [special_tokens.bad],
        )


def build_is_positive_reward_tokens(
    sample: dict,
    tokenizer: Tokenizer,
    seqlen: int,
) -> PROMPT_TARGET_TOKEN_TYPE:
    special_tokens = tokenizer.special_tokens
    prompt_tokens: list[int] = sample["prompt_tokens"]
    target_tokens: list[int] = sample["target_tokens"]
    is_positive: list[int] = sample["is_positive"]
    input_tokens = prompt_tokens + target_tokens
    if len(input_tokens) > seqlen:
        input_tokens = input_tokens[-seqlen:]
    # Return the answers
    if is_positive == [1]:
        return input_tokens, [special_tokens.good]
    elif is_positive == [0]:
        return input_tokens, [special_tokens.bad]
    else:
        raise ValueError(f"{is_positive=}")


def build_user_behavior_reward_tokens(
    sample: dict,
    tokenizer: Tokenizer,
    seqlen: int,
    user_behavior_key: str = "labels",
    constrain_user_behavior_token_as_singleton: bool = True,
) -> PROMPT_TARGET_TOKEN_TYPE:
    """Builds the input tokens and user behavior tokens for user behavior reward."""
    special_tokens = tokenizer.special_tokens
    prompt_tokens: list[int] = sample["prompt_tokens"]
    target_tokens: list[int] = sample["target_tokens"]
    user_behavior: list[int] = sample[user_behavior_key]
    input_tokens = prompt_tokens + target_tokens
    if len(input_tokens) > seqlen:
        input_tokens = input_tokens[-seqlen:]
    # Use this reward-signal as a sentinel to separate the user behavior.
    input_tokens = input_tokens + [special_tokens.reward_signal]
    # Essentially, we are using the user_behavior as a multi-class label.
    assert len(user_behavior) == 1, f"{user_behavior=}"
    user_behavior_token: list[int] = tokenizer.tokenize_safe(str(user_behavior[0]))
    if constrain_user_behavior_token_as_singleton:
        assert len(user_behavior_token) == 1, f"{user_behavior_token=}"
    # Return the answers
    return input_tokens, user_behavior_token

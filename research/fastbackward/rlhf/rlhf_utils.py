"""Some shared utilities for RLHF."""

import logging

import torch
import torch.distributed as dist
from datasets import Dataset as HFDataset
from tensordict.tensordict import TensorDict
from torch.utils.data import DataLoader, DistributedSampler

import research.fastbackward.fs_model_parallel as mpu
from base.tokenizers.tokenizer import Tokenizer
from research.fastbackward import distributed, logutils
from research.fastbackward.losses import cross_entropy_loss
from research.fastbackward.model import Transformer
from research.fastbackward.utils import avg_list_of_dict


def get_llm_collate_fn(
    tokenizer: Tokenizer,
    seqlen: int,
):
    def collate_fn(samples: list[dict]) -> TensorDict:
        """Collates a list of preference samples into a batch.
        Args:
            samples: list of preference samples
                Every sample is a dict with the following keys:
                    - prompt_tokens: list[int]
                    - target_tokens: list[int] (which should contain the EOS token by itself.)

        Returns:
            A dict with the following Tensors:
                - x: int, [batch, seqlen]
                - y: int, [batch, seqlen]
                - masks: bool, [batch, seqlen]
        """
        special_tokens = tokenizer.special_tokens

        tokens: list[list[int]] = []
        masks: list[list[bool]] = []
        for sample in samples:
            cur_tokens = sample["prompt_tokens"] + sample["target_tokens"]
            if len(cur_tokens) > seqlen + 1:
                cur_tokens = cur_tokens[: seqlen + 1]
                cur_mask = [True] * len(sample["prompt_tokens"]) + [False] * (
                    seqlen + 1 - len(sample["prompt_tokens"])
                )
            else:
                cur_mask = (
                    [True] * len(sample["prompt_tokens"])
                    + [False] * len(sample["target_tokens"])
                    + [True] * (seqlen + 1 - len(cur_tokens))
                )
                cur_tokens += [special_tokens.padding] * (seqlen + 1 - len(cur_tokens))
            tokens.append(cur_tokens)
            masks.append(cur_mask)
        torch_tokens = torch.tensor(tokens, dtype=torch.int64)
        torch_masks = torch.tensor(masks, dtype=torch.bool)
        return TensorDict(
            {
                "x": torch_tokens[:, :seqlen],
                "y": torch_tokens[:, 1 : seqlen + 1],
                "masks": torch_masks[:, 1 : seqlen + 1],
            },
            batch_size=len(samples),
        )

    return collate_fn


def build_llm_eval_loaders_by_name(
    rl_llm_eval_data_path: str,
    tokenizer: Tokenizer,
    batch_size: int,
    sequence_length: int,
    logger: logging.Logger,
) -> dict[str, DataLoader]:
    if not rl_llm_eval_data_path:
        return {}
    llm_eval_datasets = {}
    for idx, llm_eval_data_path in enumerate(rl_llm_eval_data_path.split(";")):
        if "@" in llm_eval_data_path:
            key = llm_eval_data_path.split("@")[0]
            llm_eval_data_path = llm_eval_data_path.split("@")[1]
        else:
            key = f"llm_eval_{idx}"
        llm_eval_datasets[key] = HFDataset.load_from_disk(llm_eval_data_path)

    data_parallel_world_size = mpu.get_data_parallel_world_size()
    llm_eval_loaders_by_name: dict[str, DataLoader] = {}
    for eval_name, eval_dataset in llm_eval_datasets.items():
        eval_loader = DataLoader(
            eval_dataset,  # type: ignore
            batch_size=batch_size,
            sampler=DistributedSampler(
                eval_dataset,  # type: ignore
                num_replicas=data_parallel_world_size,
                rank=mpu.get_data_parallel_rank(),
                shuffle=False,
                drop_last=True,
            ),
            collate_fn=get_llm_collate_fn(tokenizer, sequence_length),
            pin_memory=True,
            drop_last=True,
        )
        llm_eval_loaders_by_name[eval_name] = eval_loader
        if distributed.is_main_process():
            items_per_eval_step = batch_size * data_parallel_world_size
            logger.info(
                f"The default LLM [{eval_name}] dataset size: {len(eval_dataset)}"
            )
            logger.info(
                f"The default LLM [{eval_name}] eval dataset will be evaluated"
                f" for {len(eval_loader)} iterations of {items_per_eval_step} items"
                f" each."
            )
    return llm_eval_loaders_by_name


def compute_llm_eval_metrics(
    eval_data: TensorDict, model: Transformer
) -> dict[str, float]:
    x, y, masks = eval_data["x"], eval_data["y"], eval_data["masks"]
    y_masked = torch.where(~masks, y, -1)
    local_logits_policy: torch.Tensor = model(x)
    # Compute the cross-entropy loss
    ce_loss = cross_entropy_loss(
        None,  # type: ignore
        y_masked,
        local_logits_policy,
    )
    if mpu.get_data_parallel_world_size() > 1:
        dist.all_reduce(
            ce_loss,
            op=dist.ReduceOp.AVG,
            group=mpu.get_data_parallel_group(),
        )
    # Compute the token accuracy
    if mpu.get_model_parallel_world_size() > 1:
        greedy_pred = distributed.dist_argmax(
            local_logits_policy,
            dim=-1,
            group=mpu.get_model_parallel_group(),
        )
    else:
        greedy_pred = local_logits_policy.argmax(dim=-1)
    correct_pred = (greedy_pred == y) & (~masks)
    token_accuracy = correct_pred.sum(dim=-1).float() / (
        (~masks).sum(dim=-1).float() + 1e-6
    )
    token_accuracy = token_accuracy.mean()
    if mpu.get_data_parallel_world_size() > 1:
        dist.all_reduce(
            token_accuracy,
            op=dist.ReduceOp.AVG,
            group=mpu.get_data_parallel_group(),
        )
    token_acc_avg = token_accuracy.item()
    return {
        "token_accuracy": token_acc_avg,
        "ce_loss": ce_loss.item(),
    }


def evaluate_report_all_loaders(
    model: Transformer,
    llm_eval_loaders_by_name,
    iter_num: int,
    log_interval: int,
    log_prefix: str,
    logger: logging.Logger,
):
    with torch.no_grad():
        my_device = distributed.get_local_device()
        # Let's also evaluate the LLM metrics
        for eval_name, eval_loader in llm_eval_loaders_by_name.items():
            temp_eval_metrics = []
            for i, eval_data in enumerate(eval_loader):
                if distributed.is_main_process() and (i % log_interval == 0):
                    logger.info(
                        f"Eval [{log_prefix}] {eval_name} iter {i}/{len(eval_loader)}"
                    )
                eval_data = eval_data.to(my_device, non_blocking=True)
                ## LLM metrics for policy and reference models
                temp_llm_metric = compute_llm_eval_metrics(eval_data, model)  # type: ignore
                temp_eval_metrics.append(temp_llm_metric)
            temp_eval_metrics = avg_list_of_dict(temp_eval_metrics)

            if distributed.is_main_process():
                logger.info(
                    f"Device {my_device}: LLM-Eval {eval_name} metrics: {temp_eval_metrics}"
                )
                logutils.report_metrics(
                    {
                        f"{log_prefix}/{eval_name}/{k}": v
                        for k, v in temp_eval_metrics.items()
                    },
                    iter_num,
                )


def copy_weights_from_model(source_model: Transformer, target_model: Transformer):
    with torch.no_grad():
        target_state_dict = target_model.state_dict()
        for name, param in source_model.named_parameters():
            target_state_dict[name].copy_(param)

"""The online reinforcement learning (RL) training script."""

import contextlib
import copy
import dataclasses
import logging
import os
import pathlib
from functools import lru_cache, partial

import determined as det
import torch
import torch._dynamo
import torch.distributed as dist
import torch.nn.functional as F
from datasets import Dataset as HFDataset
from datasets import concatenate_datasets
from tensordict.tensordict import TensorDict
from torch.utils.data import DataLoader
from torch.utils.data.distributed import DistributedSampler

import research.fastbackward.fs_model_parallel as mpu
from base.tokenizers.tokenizer import RagSpecialTokens, Tokenizer
from research.determined_utils import remove_sync_flag_file, sync_all_to_local
from research.fastbackward import distributed, logutils, training, utils
from research.fastbackward.checkpointing import checkpointing
from research.fastbackward.data import DistributedStepCountSampler
from research.fastbackward.losses import cross_entropy_loss
from research.fastbackward.model import (
    GenericAttnSpec,
    ModelArgs,
    Transformer,
    configure_fsdp_optimizer,
    correct_model_args,
)
from research.fastbackward.rlhf import rlhf_utils
from research.fastbackward.training import flat_model_optimizer_step

# Try to avoid this warning:
# torch._dynamo hit config.cache_size_limit (8)
#       function: 'rotary_embed' (/run/determined/workdir/research/fastbackward/rotary.py:11)
#       last reason: tensor 'L['x']' stride mismatch at index 0. expected 1602048, actual 9096192

torch._dynamo.config.cache_size_limit = 32  # type: ignore

logger = logging.getLogger(__name__)


def get_dpo_collate_fn(
    tokenizer: Tokenizer,
    seqlen: int,
):
    def collate_fn(samples: list[dict]) -> TensorDict:
        """Collates a list of preference samples into a batch.
        Args:
            samples: list of preference samples
                Every sample is a dict with the following keys:
                    - prompt_tokens: list[int]

        Returns:
            A dict with the following Tensors:
                - prompt_tokens: int, [batch, seqlen]
                - prompt_length: int, [batch]
        """
        special_tokens = tokenizer.special_tokens
        tokens_list: list[torch.Tensor] = []
        length_list: list[int] = []
        for sample in samples:
            prompt_tokens = sample["prompt_tokens"]
            if len(prompt_tokens) > seqlen:
                prompt_tokens = prompt_tokens[-seqlen:]
                logger.info(f"Truncating prompt to {seqlen} tokens.")
            padded_tokens = prompt_tokens + [special_tokens.padding] * (
                seqlen - len(prompt_tokens)
            )
            tokens_list.append(torch.tensor(padded_tokens, dtype=torch.int64))
            length_list.append(len(prompt_tokens))

        return TensorDict(
            {
                "prompt_tokens": torch.stack(tokens_list),
                "prompt_length": torch.tensor(length_list, dtype=torch.int64),
            },
            batch_size=len(samples),
        )

    return collate_fn


def get_llm_collate_fn(
    tokenizer: Tokenizer,
    seqlen: int,
):
    def collate_fn(samples: list[dict]) -> TensorDict:
        """Collates a list of preference samples into a batch.
        Args:
            samples: list of preference samples
                Every sample is a dict with the following keys:
                    - prompt_tokens: list[int]
                    - target_tokens: list[int] (which should contain the EOS token by itself.)

        Returns:
            A dict with the following Tensors:
                - x: int, [batch, seqlen]
                - y: int, [batch, seqlen]
                - masks: bool, [batch, seqlen]
        """
        special_tokens = tokenizer.special_tokens

        tokens: list[list[int]] = []
        masks: list[list[bool]] = []
        for sample in samples:
            cur_tokens = sample["prompt_tokens"] + sample["target_tokens"]
            if len(cur_tokens) > seqlen + 1:
                cur_tokens = cur_tokens[: seqlen + 1]
                cur_mask = [True] * len(sample["prompt_tokens"]) + [False] * (
                    seqlen + 1 - len(sample["prompt_tokens"])
                )
            else:
                cur_mask = (
                    [True] * len(sample["prompt_tokens"])
                    + [False] * len(sample["target_tokens"])
                    + [True] * (seqlen + 1 - len(cur_tokens))
                )
                cur_tokens += [special_tokens.padding] * (seqlen + 1 - len(cur_tokens))
            tokens.append(cur_tokens)
            masks.append(cur_mask)
        torch_tokens = torch.tensor(tokens, dtype=torch.int64)
        torch_masks = torch.tensor(masks, dtype=torch.bool)
        return TensorDict(
            {
                "x": torch_tokens[:, :seqlen],
                "y": torch_tokens[:, 1 : seqlen + 1],
                "masks": torch_masks[:, 1 : seqlen + 1],
            },
            batch_size=len(samples),
        )

    return collate_fn


def load_model_fn(
    model_args: ModelArgs,
    model_vocab_size: int,
    dtype: str,
    det_context: det.core.Context | None,  # type: ignore
    checkpoint_location: str,
) -> Transformer:
    str2dtype = {"float32": torch.float32, "bfloat16": torch.bfloat16}
    if dtype not in str2dtype:
        raise ValueError(f"dtype {dtype} not supported")
    ptdtype = str2dtype[dtype]

    model = Transformer(model_args, skip_init=True)
    model.to(distributed.get_local_device(), ptdtype)

    checkpoint_manager = checkpointing.CheckpointManager(det_context)
    state_dict = checkpoint_manager.load_state_dict_checkpoint(
        checkpoint_location,
        model_vocab_size=model_vocab_size,
    )
    model.load_state_dict(state_dict)

    return model


@dataclasses.dataclass
class OnlineRLOptions:
    """Options for the online reinforcement learning algorithms."""

    ce_loss_coeff: float
    rl_dpop_lambda: float
    use_ipo: bool
    """Whether to use IPO instead of DPO."""
    beta: float
    label_smoothing: float
    sync_ref_weights_per_iter: int
    num_pairs_per_prompt: int
    max_generation_steps: int
    online_rl: bool
    batched_generation_batch_size: int = 32

    def __post_init__(self):
        assert self.max_generation_steps > 0
        assert self.num_pairs_per_prompt > 0


@dataclasses.dataclass
class PostIterHookParams:
    """Parameters for the post-iter hook."""

    eval_interval: int
    eval_log_interval: int
    ckp_save_interval: int | None
    max_iters: int
    start_iteration: int
    full_config: dict

    def __post_init__(self):
        assert self.eval_interval > 0, f"{self.eval_interval=}"
        if self.ckp_save_interval is not None:
            if self.ckp_save_interval == 0:
                self.ckp_save_interval = self.eval_interval
            assert (
                self.ckp_save_interval % self.eval_interval == 0
            ), f"{self.ckp_save_interval=} {self.eval_interval=}"


def batched_sample_continuation_batch(
    model: Transformer,
    prompt_tokens: torch.Tensor,
    num_samples: int,
    max_generation_steps: int,
    stop_tokens: torch.Tensor,
    generation_batch_size: int,
    temperature: float = 1.0,
    top_p: float = 0.999,
) -> list[torch.Tensor]:
    batch_size, prompt_length = prompt_tokens.shape
    assert batch_size == 1, "Only support batch size 1 for now"
    # First: pre-fill the prompt into the kv-cache and replicate to every cache line.
    prompt_tokens = prompt_tokens.to(distributed.get_local_device())
    prompt_logits = model.generate(prompt_tokens, 0)
    model.replicate_kv_cache_prefix(prefix_len=prompt_length)
    # This is ceildiv(num_samples, generation_batch_size)
    num_rounds = -(-num_samples // generation_batch_size)
    all_results: list[torch.Tensor] = []

    for round_idx in range(num_rounds):
        round_start = round_idx * generation_batch_size
        round_end = min(round_start + generation_batch_size, num_samples)
        round_batch_size = round_end - round_start
        round_eod_reached = torch.zeros(
            round_batch_size,
            dtype=torch.bool,
            device=distributed.get_local_device(),
        )
        round_batch = torch.full(
            (round_batch_size, prompt_length + max_generation_steps),
            -1,
            dtype=torch.long,
            device=distributed.get_local_device(),
        )
        round_batch[:, :prompt_length].copy_(
            prompt_tokens.repeat(round_batch_size, 1),
        )
        for num_generated in range(0, max_generation_steps):
            if num_generated == 0:
                next_token_logits = prompt_logits[:, -1, :].repeat(
                    round_batch_size,
                    1,
                )
            else:
                next_token_logits = model.generate(
                    round_batch[:, prompt_length + num_generated - 1].unsqueeze(1),
                    prompt_length + num_generated - 1,
                ).squeeze(1)
            next_token_probs = torch.softmax(next_token_logits / temperature, dim=-1)
            round_sampled_tokens = utils.sample_top_p(next_token_probs, top_p).squeeze()
            round_batch[:, prompt_length + num_generated].copy_(round_sampled_tokens)
            round_eod_reached |= torch.isin(round_sampled_tokens, stop_tokens)
            if round_eod_reached.all():
                break

        for batch_idx in range(round_batch_size):
            generation = round_batch[batch_idx, prompt_length:]
            eod_idxs = torch.isin(generation, stop_tokens).nonzero()
            if len(eod_idxs) > 0:
                generation = generation[: eod_idxs[0].item() + 1]
            all_results.append(generation.clone().unsqueeze(0))

    return all_results


def sample_continuation_batch(
    model: Transformer,
    prompt_tokens: torch.Tensor,
    num_samples: int,
    max_generation_steps: int,
    stop_tokens: torch.Tensor,
    temperature: float = 1.0,
    top_p: float = 0.999,
) -> list[torch.Tensor]:
    """Samples a continuation from the model."""
    bs, length = prompt_tokens.shape
    assert bs == 1, "Only support batch size 1 for now"
    meta_tokens = torch.full(
        (1, length + max_generation_steps),
        -1,
        dtype=torch.long,
        device=distributed.get_local_device(),
        requires_grad=False,
    )
    meta_tokens[0, :length] = prompt_tokens.clone().detach()
    input_mask = meta_tokens != -1
    all_target_tokens: list[torch.Tensor] = []
    # fmt: off
    reused_probs = None
    for index in range(num_samples):
        prev_pos = 0
        eod_reached = torch.tensor([False], device=distributed.get_local_device())
        tokens = meta_tokens.clone()
        for cur_pos in range(length, length + max_generation_steps):
            if index > 0 and cur_pos == length:
                # reuse the KV cache and we also need to reuse the last probs for the first token generation
                assert prev_pos == 0
                assert reused_probs is not None, f"{index=}/{num_samples=} {cur_pos=} {prev_pos=} {type(reused_probs)=}"
                probs = reused_probs
            else:
                logits = model.generate(tokens[:, prev_pos:cur_pos].contiguous(), prev_pos)
                probs = torch.softmax(logits[:, -1] / temperature, dim=-1)
                # logger.info(f"{index=}/{num_samples=} {cur_pos=} {prev_pos=} {length=} {type(probs)=}")
                if index == 0 and cur_pos == length:
                    # logger.info(f"Reuse probs for reused_pros {index=}/{num_samples=} {cur_pos=} {length=}")
                    reused_probs = probs
            assert isinstance(probs, torch.Tensor), f"{index=}/{num_samples=} {cur_pos=} {prev_pos=} {type(probs)=}"
            next_token = utils.sample_top_p(probs, top_p)
            next_token = next_token.reshape(-1)
            next_token = torch.where(input_mask[:, cur_pos], tokens[:, cur_pos], next_token)
            tokens[:, cur_pos] = next_token
            eod_reached |= (~input_mask[:, cur_pos]) & (torch.isin(next_token, stop_tokens))
            prev_pos = cur_pos
            if eod_reached.item():
                break
        target_tokens = tokens[:, length : prev_pos + 1].clone()
        all_target_tokens.append(target_tokens)
    # fmt: on
    return all_target_tokens


def token_probs_to_reward_score(
    user_behavior_tokens_list: tuple[float, ...],
    version: str = "default",
) -> float:
    """Computes the reward score from the token probabilities."""
    if version == "default":
        if len(user_behavior_tokens_list) == 6:
            # Check the logic at experimental/dxy/rag/rlhf/data/create-reward-data-qwen.py
            # raw@accept-then-other : 0
            # hindsight : 1
            # raw@accept-then-edit : 2
            # raw@accept : 3
            # raw@reject, raw@reject-partial, and raw@reject-all : 4
            # augmented rejection: 5
            reward_score = (
                user_behavior_tokens_list[0] * 1.5
                + user_behavior_tokens_list[1] * 2.0
                + user_behavior_tokens_list[2] * 0.5
                + user_behavior_tokens_list[3] * 1.0
                + user_behavior_tokens_list[4] * -1.0
                + user_behavior_tokens_list[5] * -2
            )
        elif len(user_behavior_tokens_list) == 7:
            # The new class of 6 is to see if the data comes from sft data.
            reward_score = (
                user_behavior_tokens_list[0] * 1.5
                + user_behavior_tokens_list[1] * 2.0
                + user_behavior_tokens_list[2] * 0.5
                + user_behavior_tokens_list[3] * 1.0
                + user_behavior_tokens_list[4] * -1.0
                + user_behavior_tokens_list[5] * -2
                + user_behavior_tokens_list[6] * 0.2
            )
        else:
            raise ValueError(f"Unknown length {len(user_behavior_tokens_list)=}")
    else:
        raise ValueError(f"Unknown version {version=}")
    return reward_score


def compute_reward_score_batch(
    model: Transformer,
    prompt_tokens: torch.Tensor,
    target_tokens_list: list[torch.Tensor],
    stop_tokens: torch.Tensor,
    replace_stop_token: int | None,
    reward_signal_id: int,
    user_behavior_tokens: tuple[int, ...],
) -> list[float]:
    """Computes the reward score of the target tokens.

    The prompt_tokens and target_tokens should contain no padding tokens and have batch size of 1.
    """
    assert prompt_tokens.shape[0] == 1, "Only support batch size 1 for now"
    assert len(user_behavior_tokens) == 6, f"{user_behavior_tokens=}"
    prompt_tokens = prompt_tokens.to(distributed.get_local_device())
    reward_signal_id_as_tensor = torch.tensor(
        [reward_signal_id], device=prompt_tokens.device
    ).view(1, 1)
    prompt_length = prompt_tokens.shape[1]
    _ = model.generate(prompt_tokens, 0)
    scores: list[float] = []
    for _, target_tokens in enumerate(target_tokens_list):
        assert target_tokens.shape[0] == 1, "Only support batch size 1 for now"
        # NOTE: Because our data prepartion stage did not distinguish between pause and eos,
        # we always append eos after the target tokens when we train the reward model.
        # Thus, here to compute the correct reward score, we need to replace the pause token with eos.
        if replace_stop_token is None:
            correct_target_tokens = target_tokens
        else:
            correct_target_tokens = torch.where(
                torch.isin(target_tokens, stop_tokens),
                torch.full_like(target_tokens, replace_stop_token),
                target_tokens,
            )
        # Add one more token of reward_signal_id to the end.
        correct_target_tokens = torch.cat(
            [correct_target_tokens, reward_signal_id_as_tensor],
            dim=1,
        )
        global_logits = model.generate(correct_target_tokens, prompt_length)
        global_logits_last_token = global_logits[:, -1]
        prob_last_token = torch.softmax(global_logits_last_token, dim=-1)
        # Reward Score is weighted by the probability of the all user behavior tokens.
        user_behavior_tokens_prob: torch.Tensor = (
            prob_last_token[:, user_behavior_tokens].squeeze(dim=0).cpu()
        )
        user_behavior_tokens_list: list[float] = user_behavior_tokens_prob.tolist()
        reward_score = token_probs_to_reward_score(
            tuple(user_behavior_tokens_list), "default"
        )
        # reward_score = (
        #     prob_last_token[:, good_token_id] - prob_last_token[:, bad_token_id]
        # ).item()
        scores.append(reward_score)
    return scores


def get_logp_of_target_tokens(
    model: Transformer,
    prompt_tokens: torch.Tensor,
    target_tokens: torch.Tensor,
    mask_tokens: torch.Tensor,
    return_ce_loss: bool = False,
):
    """Computes the log probability of the target tokens.

    According to LLAMA 3 paper (https://arxiv.org/pdf/2407.21783), there are two tricks to stabilize the training:
     - Masking out formatting tokens in DPO/IPO loss.
     - Regularization with NLL loss."""
    assert prompt_tokens.shape[0] == 1, "Only support batch size 1 for now"
    # target_tokens's shape should be [1, target_length]
    assert target_tokens.shape[0] == 1, "Only support batch size 1 for now"
    _, length = prompt_tokens.shape
    local_logits = model(
        torch.cat([prompt_tokens, target_tokens], dim=1), attention_mask=None
    )
    local_cont_logits = local_logits[:, length - 1 : -1]
    # cont_logits's shape should be [1, target_length, vocab_size]
    cont_logits = mpu.gather_from_model_parallel_region(local_cont_logits)
    # cont_logp's shape should be [1, target_length]
    cont_logps = (
        F.log_softmax(cont_logits, dim=-1)
        .gather(2, target_tokens[:, :, None])
        .squeeze(2)
    )
    masked_targets = (~torch.isin(target_tokens, mask_tokens)).float()
    if return_ce_loss:
        # We need to compute the cross-entropy loss.
        ce_loss = cross_entropy_loss(None, target_tokens, local_cont_logits)  # type: ignore
        return (
            (cont_logps * masked_targets).sum(-1),
            masked_targets.sum(),
            ce_loss,
        )
    else:
        return (cont_logps * masked_targets).sum(-1), masked_targets.sum(), None


def get_per_sample_dpo_loss(
    prompt_tokens: torch.Tensor,
    chosen_tokens: torch.Tensor,
    rejected_tokens: torch.Tensor,
    mask_tokens_in_dpo: list[int],
    options: OnlineRLOptions,
    policy_model: Transformer,
    ref_model: Transformer,
) -> tuple[torch.Tensor, dict[str, torch.Tensor]]:
    # Compute the chosen_logps and rejected_logps
    my_device = distributed.get_local_device()
    mask_tokens = torch.tensor(mask_tokens_in_dpo, device=my_device)
    policy_chosen_logps, num_seen_chosen_tokens, policy_chosen_ce_loss = (
        get_logp_of_target_tokens(
            policy_model,
            prompt_tokens,
            chosen_tokens,
            mask_tokens,
            return_ce_loss=options.ce_loss_coeff > 0,
        )
    )
    policy_rejected_logps, num_seen_rejected_tokens, _ = get_logp_of_target_tokens(
        policy_model, prompt_tokens, rejected_tokens, mask_tokens
    )
    with torch.no_grad():
        ref_chosen_logps, _, _ = get_logp_of_target_tokens(
            ref_model, prompt_tokens, chosen_tokens, mask_tokens
        )
        ref_rejected_logps, _, _ = get_logp_of_target_tokens(
            ref_model, prompt_tokens, rejected_tokens, mask_tokens
        )
    # if distributed.is_main_process():
    #     logger.info(
    #         f"{prompt_tokens=}\n"
    #         f"{chosen_tokens=}\n"
    #         f"{rejected_tokens=}\n"
    #         f"{policy_chosen_logps=}\n"
    #         f"{policy_rejected_logps=}\n"
    #         f"{ref_chosen_logps=}\n"
    #         f"{ref_rejected_logps=}\n"
    #         f"{policy_chosen_ce_loss=}\n"
    #         f"{policy_model.training=}"
    #     )
    assert isinstance(policy_chosen_logps, torch.Tensor)
    assert isinstance(policy_rejected_logps, torch.Tensor)
    assert isinstance(ref_chosen_logps, torch.Tensor)
    assert isinstance(ref_rejected_logps, torch.Tensor)
    # assert policy_chosen_ce_loss is not None
    log_ratios = (policy_chosen_logps - policy_rejected_logps) - (
        ref_chosen_logps - ref_rejected_logps
    )
    # This implements the DPO-Positive paper https://arxiv.org/pdf/2402.13228
    if options.rl_dpop_lambda > 0:
        dpo_logits = log_ratios - options.rl_dpop_lambda * torch.max(
            torch.zeros(1, device=log_ratios.device),
            ref_chosen_logps - policy_chosen_logps,
        )
    else:
        dpo_logits = log_ratios
    if options.use_ipo:  # Compute the IPO loss
        assert options.label_smoothing <= 0, "IPO does not support label smoothing"
        assert options.rl_dpop_lambda <= 0, "IPO does not support DPOP"
        base_dpo_loss = (dpo_logits - 1 / (2 * options.beta)) ** 2
    elif options.label_smoothing > 0:
        # As mentioned in https://ericmitchell.ai/cdpo.pdf:
        # conservative DPO (this label smoothing) can train the model until a desired improvement
        # in the implicit probability assigned by the model to the observed preferences is met.
        # But the normal DPO keeps increase the reward.
        base_dpo_loss = (
            -F.logsigmoid(options.beta * dpo_logits) * (1 - options.label_smoothing)
            - F.logsigmoid(-options.beta * dpo_logits) * options.label_smoothing
        )
    else:
        base_dpo_loss = -F.logsigmoid(options.beta * dpo_logits)

    if options.ce_loss_coeff > 0:
        assert policy_chosen_ce_loss is not None
        loss = base_dpo_loss + options.ce_loss_coeff * policy_chosen_ce_loss
    else:
        loss = base_dpo_loss
    # Collect the metrics
    with torch.no_grad():
        chosen_rewards = (
            options.beta * (policy_chosen_logps - ref_chosen_logps).detach()
        )
        rejected_rewards = (
            options.beta * (policy_rejected_logps - ref_rejected_logps).detach()
        )
        margin_policy = (policy_chosen_logps - policy_rejected_logps).detach()
        metrics = {
            "chosen_rewards": chosen_rewards,
            "rejected_rewards": rejected_rewards,
            "num_seen_chosen_tokens": num_seen_chosen_tokens,
            "num_seen_rejected_tokens": num_seen_rejected_tokens,
            "margin_policy": margin_policy,
            "log_ratios": log_ratios.detach().clone(),
            "dpo_logits": dpo_logits.detach().clone(),
            "base_dpo_loss": base_dpo_loss.detach().clone(),
        }
        if policy_chosen_ce_loss is not None:
            metrics["policy_chosen_ce_loss"] = policy_chosen_ce_loss.detach().clone()
    return loss, metrics


@lru_cache(maxsize=8)
def get_user_behavior_tokens(tokenizer: Tokenizer) -> tuple[int, ...]:
    """Get the user behavior tokens."""
    [token_0] = tokenizer.tokenize_safe("0")
    [token_1] = tokenizer.tokenize_safe("1")
    [token_2] = tokenizer.tokenize_safe("2")
    [token_3] = tokenizer.tokenize_safe("3")
    [token_4] = tokenizer.tokenize_safe("4")
    [token_5] = tokenizer.tokenize_safe("5")
    return token_0, token_1, token_2, token_3, token_4, token_5


def get_online_rl_data_pairs(
    data: TensorDict,
    tokenizer: Tokenizer,
    options: OnlineRLOptions,
    model: Transformer,
    reward_model: Transformer,
) -> tuple[list[TensorDict], dict[str, torch.Tensor]]:
    """Computes dpo loss. + adds weighted cross-entropy loss using `args.rl_dpo_ce_loss_coeff`.
        This function does not run reference model online, so log probabilities should
        be passed as arguments.
    Args:
        data: dict[str, Tensor]
            - prompt_tokens: int, [batch, prompt_length]
            - prompt_length: int, [batch]
        options: OnlineRLOptions
        model: Transformer (can be policy or ref model)
        reward_model: Transformer

    Returns:
        samples: list[dict[str, Tensor]]
        metrics: dict[str, float]
            all_reduced single number metrics
    """
    prompt_tokens = data["prompt_tokens"]
    prompt_lengths = data["prompt_length"].cpu().tolist()

    samples: list[TensorDict] = []
    score_diff, score_pos, score_neg = [], [], []
    special_tokens = tokenizer.special_tokens
    # TODO(Xuanyi): this should be abstracted in a better way
    user_behavior_tokens = get_user_behavior_tokens(tokenizer)
    # assert isinstance(special_tokens, RagSpecialTokens)
    stop_tokens_list = [special_tokens.eos, special_tokens.pause]
    stop_tokens = torch.tensor(stop_tokens_list, device=distributed.get_local_device())
    # replace_stop_token = special_tokens.eos
    replace_stop_token = None
    # For each data, we sample two continuations:
    with torch.no_grad(), model.expand_num_kv_caches(
        options.batched_generation_batch_size
    ):
        model.eval()
        reward_model.eval()
        local_batch = prompt_tokens.shape[0]
        for ibatch in range(local_batch):
            prompt_length = prompt_lengths[ibatch]
            prompt_tokens_i = prompt_tokens[ibatch : ibatch + 1, :prompt_length]
            target_tokens_list = batched_sample_continuation_batch(
                model,
                prompt_tokens_i,
                num_samples=2 * options.num_pairs_per_prompt,
                max_generation_steps=options.max_generation_steps,
                stop_tokens=stop_tokens,
                generation_batch_size=options.batched_generation_batch_size,
            )
            score_list = compute_reward_score_batch(
                reward_model,
                prompt_tokens_i,
                target_tokens_list,
                stop_tokens=stop_tokens,
                replace_stop_token=replace_stop_token,
                reward_signal_id=special_tokens.reward_signal,
                user_behavior_tokens=user_behavior_tokens,
            )
            for idx in range(options.num_pairs_per_prompt):
                target_tokens_i_1 = target_tokens_list[2 * idx]
                target_tokens_i_2 = target_tokens_list[2 * idx + 1]
                score_i_1 = score_list[2 * idx]
                score_i_2 = score_list[2 * idx + 1]
                if score_i_1 > score_i_2:
                    pos_target_tokens = target_tokens_i_1
                    neg_target_tokens = target_tokens_i_2
                    score_pos.append(score_i_1)
                    score_neg.append(score_i_2)
                else:
                    pos_target_tokens = target_tokens_i_2
                    neg_target_tokens = target_tokens_i_1
                    score_pos.append(score_i_2)
                    score_neg.append(score_i_1)
                score_diff.append(abs(score_i_1 - score_i_2))
                samples.append(
                    TensorDict(
                        {
                            "prompt_tokens": prompt_tokens_i,
                            "pos_tokens": pos_target_tokens.detach(),
                            "neg_tokens": neg_target_tokens.detach(),
                        }
                    )
                )
        score_diff_tensor = torch.tensor(score_diff, device=prompt_tokens.device)
        score_pos_tensor = torch.tensor(score_pos, device=prompt_tokens.device)
        score_neg_tensor = torch.tensor(score_neg, device=prompt_tokens.device)
    metrics = {
        "reward_score_diff": score_diff_tensor.mean(),
        "reward_score_pos": score_pos_tensor.mean(),
        "reward_score_neg": score_neg_tensor.mean(),
    }
    return samples, metrics


def get_single_dpo_loss(
    data: TensorDict,
    options: OnlineRLOptions,
    policy_model: Transformer,
    ref_model: Transformer,
) -> tuple[torch.Tensor, dict[str, torch.Tensor]]:
    """Computes dpo loss. + adds weighted cross-entropy loss using `args.rl_dpo_ce_loss_coeff`.
        This function does not run reference model online, so log probabilities should
        be passed as arguments.

    Args:
        data: dict[str, Tensor]
            - prompt_tokens: int, [batch, prompt_length]
            - pos_tokens: int, [batch, pos_target_length]
            - neg_tokens: int, [batch, neg_target_length]
        options: OnlineRLOptions
        policy_model: Transformer
        ref_model: Transformer

    Returns:
        dpo_loss: [1]
            Local DPO loss (i.e. before data parallel reduce)
        metrics: dict[str, torch.Tensor]
            all_reduced single number metrics
    """
    # NOTE: temporarily disable the masking
    mask_tokens_in_dpo = [-1]
    # mask_tokens_in_dpo = [special_tokens.pause, special_tokens.eos]
    # Compute the actual loss
    prompt_tokens: torch.Tensor = data["prompt_tokens"]
    pos_tokens: torch.Tensor = data["pos_tokens"]
    neg_tokens: torch.Tensor = data["neg_tokens"]
    dpo_loss, metrics = get_per_sample_dpo_loss(
        prompt_tokens,
        pos_tokens,
        neg_tokens,
        mask_tokens_in_dpo=mask_tokens_in_dpo,
        options=options,
        policy_model=policy_model,
        ref_model=ref_model,
    )
    metrics["pos_target_length"] = torch.tensor(
        pos_tokens.numel(), device=prompt_tokens.device
    ).float()
    metrics["neg_target_length"] = torch.tensor(
        neg_tokens.numel(), device=prompt_tokens.device
    ).float()
    return dpo_loss, metrics


def post_iter_hook_rl(
    iter_num: int,
    params: PostIterHookParams,
    tokenizer: Tokenizer,
    rl_options: OnlineRLOptions,
    policy_model: Transformer,
    ref_model: Transformer,
    reward_model: Transformer,
    det_context: det.core.Context | None,  # type: ignore
    rl_eval_loaders_by_name: dict[str, DataLoader],
) -> bool:
    assert params.ckp_save_interval is None, "RL does not support checkpointing yet."
    is_last_iter = iter_num == params.max_iters
    preempted = det_context.preempt.should_preempt() if det_context else False
    if preempted:
        logger.info("Received Determined preemption -- performing final checkpointing.")

    should_run_eval = (
        # It's the starting step.
        iter_num == params.start_iteration
        # It's at an eval interval.
        or (params.eval_interval > 0 and (iter_num % params.eval_interval == 0))
        # It's at the last iteration.
        or is_last_iter
        # We are being pre-empted.
        or preempted
    )
    if not should_run_eval:
        return False

    my_device = distributed.get_local_device()
    with torch.no_grad():
        policy_model.eval()
        ref_model.eval()
        reward_model.eval()
        # Let's also evaluate the RL metrics
        for eval_name, eval_loader in rl_eval_loaders_by_name.items():
            eval_metrics = []
            for i, eval_data in enumerate(eval_loader):
                if distributed.is_main_process() and (
                    i % params.eval_log_interval == 0
                ):
                    logger.info(f"Eval {eval_name} iter {i}/{len(eval_loader)} starts")
                eval_data = eval_data.to(my_device, non_blocking=True)

                online_rl_data, data_metrics = get_online_rl_data_pairs(
                    eval_data,
                    tokenizer,
                    options=rl_options,
                    model=policy_model if rl_options.online_rl else ref_model,
                    reward_model=reward_model,
                )
                dpo_metrics = []
                for sample in online_rl_data:
                    loss, cur_dpo_metrics = get_single_dpo_loss(
                        sample.to(my_device),
                        rl_options,
                        policy_model=policy_model,
                        ref_model=ref_model,
                    )
                    cur_dpo_metrics["loss"] = loss
                    dpo_metrics.append(cur_dpo_metrics)
                with torch.no_grad():
                    dpo_metrics = utils.avg_list_of_dict(dpo_metrics)
                    metrics = {**data_metrics, **dpo_metrics}
                    metrics_numbers = {}
                    keys = sorted(list(metrics.keys()))
                    for k in keys:
                        value = metrics[k].float().detach()
                        if mpu.get_data_parallel_world_size() > 1:
                            dist.all_reduce(
                                value,
                                op=dist.ReduceOp.AVG,
                                group=mpu.get_data_parallel_group(),
                            )
                        metrics_numbers[k] = float(value.item())
                eval_metrics.append(metrics_numbers)
            eval_metrics = utils.avg_list_of_dict(eval_metrics)
            if distributed.is_main_process():
                logger.info(
                    f"Device {my_device}: RL-Eval {eval_name} metrics: {eval_metrics}"
                )
                logutils.report_metrics(
                    {f"rl_eval/{eval_name}/{k}": v for k, v in eval_metrics.items()},
                    iter_num,
                )
    return preempted


def post_iter_hook(
    iter_num: int,
    params: PostIterHookParams,
    policy_model: Transformer,
    ref_model: Transformer,
    reward_model: Transformer,
    model_config: dict,
    det_context: det.core.Context | None,  # type: ignore
    checkpoint_manager: checkpointing.CheckpointManager,
    llm_eval_loaders_by_name: dict[str, DataLoader],
) -> bool:
    assert (
        params.ckp_save_interval is not None
    ), "post_iter_hook must have ckp_save_interval"
    is_last_iter = iter_num == params.max_iters
    preempted = det_context.preempt.should_preempt() if det_context else False
    if preempted:
        logger.info("Received Determined preemption -- performing final checkpointing.")

    should_run_eval = (
        # It's the starting step.
        iter_num == params.start_iteration
        # It's at an eval interval.
        or (params.eval_interval > 0 and (iter_num % params.eval_interval == 0))
        # It's at the last iteration.
        or is_last_iter
        # We are being pre-empted.
        or preempted
    )
    if not should_run_eval:
        return False

    with torch.no_grad():
        policy_model.eval()
        ref_model.eval()
        reward_model.eval()
        rlhf_utils.evaluate_report_all_loaders(
            policy_model,
            llm_eval_loaders_by_name,
            iter_num,
            log_interval=params.eval_log_interval,
            log_prefix="llm_eval",
            logger=logger,
        )
        rlhf_utils.evaluate_report_all_loaders(
            ref_model,
            llm_eval_loaders_by_name,
            iter_num,
            log_interval=params.eval_log_interval,
            log_prefix="llm_ref_eval",
            logger=logger,
        )

    if iter_num > params.start_iteration and (
        is_last_iter or (iter_num % params.ckp_save_interval == 0)
    ):
        checkpoint_location = checkpointing.get_checkpoint_location(iter_num, None)
        checkpoint_location = checkpoint_manager.checkpoint(
            checkpoint_location,
            model_state_dict=policy_model.state_dict(),
            model_config=model_config,
            iter_num=iter_num,
            metadata={
                "iter_num": iter_num,
                "config": params.full_config,
            },
        )
    else:
        logger.info("Skip init-eval checkpointing")
    return preempted


@dataclasses.dataclass
class LRParams:
    """Configuration for the optimizer."""

    decay_lr: bool
    """Whether to decay the learning rate."""

    learning_rate: float
    """The learning rate to use."""

    min_lr: float
    """The minimum learning rate to use."""

    warmup_iters: int
    """The number of iterations to warmup the learning rate over."""

    lr_decay_iters: int
    """The number of iterations to decay the learning rate over."""

    max_iters: int
    """The total number of training iterations."""


def create_lr_schedule(parameters: LRParams) -> training.LRSchedule:
    """Get learning rate given args."""

    if parameters.decay_lr:
        # If `lr_decay_iters` is 0 (default), decay to the target iteration count
        decay_iters = parameters.lr_decay_iters or parameters.max_iters
        return partial(
            training.get_cosine_lr,
            warmup_iters=parameters.warmup_iters,
            learning_rate=parameters.learning_rate,
            decay_iters=decay_iters,
            min_lr=parameters.min_lr,
        )
    else:
        return lambda _: parameters.learning_rate  # noqa


def train(det_context: det.core.Context | None, full_config, args):  # type: ignore
    # Build train dataset
    assert args.train_data_path, "Must provide a train_data_path"

    logger.info(f"The full config: {full_config}")
    train_datasets = []
    for idx_path, path in enumerate(args.train_data_path.split(";")):
        assert pathlib.Path(
            path
        ).exists(), f"Requested training dataset at {path} does not exist."
        cur_dataset = HFDataset.load_from_disk(path)
        train_datasets.append(cur_dataset)
        logger.info(f"{idx_path=}: {len(cur_dataset)=}")
    if len(train_datasets) == 1:
        train_dataset = train_datasets[0]
    else:
        train_dataset = concatenate_datasets(train_datasets)
    logger.info(f"The training data path: {args.train_data_path}")
    logger.info(f"Total training dataset size: {len(train_dataset)}")
    if args.max_iters == 0:
        assert args.max_epochs > 0, "If max_iters==0, max_epochs should be >0"
        global_batch_size: int = (
            args.batch_size
            * args.gradient_accumulation_steps
            * mpu.get_data_parallel_world_size()
        )
        args.max_iters = args.max_epochs * len(train_dataset) // global_batch_size
        logger.info(
            f"Running {args.max_epochs} epoch(s), "
            f"each epoch has {len(train_dataset)} items, "
            f"with a global batch size of {global_batch_size} we get max_iters={args.max_iters}"
        )
    else:
        assert args.max_epochs == 0, "If max_iters>0, max_epochs should be 0"
    assert not args.use_research_tokenizer
    assert args.tokenizer_name
    tokenizer = logutils.get_base_tokenizer(args.tokenizer_name)

    # Build eval datasets
    assert args.eval_data_path, "Must provide a eval_data_path"
    dpo_eval_datasets = {}
    for idx, eval_data_path in enumerate(args.eval_data_path.split(";")):
        if "@" in eval_data_path:
            key = eval_data_path.split("@")[0]
            eval_data_path = eval_data_path.split("@")[1]
        else:
            key = f"eval_{idx}"
        dpo_eval_datasets[key] = HFDataset.load_from_disk(eval_data_path)
        if distributed.is_main_process():
            logger.info(f"DPO-Eval {key} dataset size: {len(dpo_eval_datasets[key])}")
    dpo_eval_loaders_by_name = {}
    for eval_name, eval_dataset in dpo_eval_datasets.items():
        eval_loader = DataLoader(
            eval_dataset,  # type: ignore
            batch_size=args.batch_size,
            sampler=DistributedSampler(
                eval_dataset,  # type: ignore
                num_replicas=mpu.get_data_parallel_world_size(),
                rank=mpu.get_data_parallel_rank(),
                shuffle=False,
                drop_last=True,
            ),
            collate_fn=get_dpo_collate_fn(
                tokenizer, args.block_size - args.rl_max_generation_steps - 1
            ),
            pin_memory=True,
            drop_last=True,
        )
        dpo_eval_loaders_by_name[eval_name] = eval_loader
    assert args.rl_llm_eval_data_path, "Must provide a rl_llm_eval_data_path"
    llm_eval_loaders_by_name = rlhf_utils.build_llm_eval_loaders_by_name(
        args.rl_llm_eval_data_path,
        tokenizer,
        args.batch_size,
        args.block_size,
        logger,
    )

    # Set up the training data loader
    start_iteration = 0
    train_sampler = DistributedStepCountSampler(
        train_dataset,  # type: ignore
        # NOTE(arun): We now draw samples from the dataset in micro-batches, which
        # means we have to account for the number gradient accumulation steps in
        # each iteration.
        batch_size=args.batch_size,
        step_count=args.max_iters * args.gradient_accumulation_steps,
        start_step=start_iteration * args.gradient_accumulation_steps,
        dp_rank=mpu.get_data_parallel_rank(),
        dp_world_size=mpu.get_data_parallel_world_size(),
        seed=args.data_sampler_rand_seed,
        cross_shuffle=args.cross_shuffle,
    )
    if distributed.is_main_process():
        logger.info(f"Total steps per epoch: {train_sampler.steps_per_epoch}")
    train_loader = DataLoader(
        train_dataset,  # type: ignore
        batch_sampler=train_sampler,
        num_workers=4,  # TODO: try different values
        collate_fn=get_dpo_collate_fn(
            tokenizer, args.block_size - args.rl_max_generation_steps - 1
        ),
        pin_memory=True,
    )

    model_args = ModelArgs(
        dim=args.n_embd,
        n_layers=args.n_layers,
        n_heads=args.n_heads,
        n_kv_heads=args.n_kv_heads,
        multiple_of=args.ffn_dim_multiple_of,
        ffn_dim_multiplier=args.ffn_dim_multiplier,
        norm_eps=args.norm_eps,
        max_seq_len=args.block_size,
        vocab_size=args.model_vocab_size,
        rope_theta=args.rope_theta,
        rope_scaling_factor=args.rope_scaling_factor,
        ffn_type=args.ffn_type,
        bias=args.bias,
        norm_type=args.norm_type,
        pos_embed_type=args.pos_embed_type,
        rotary_config=args.rotary_config,
        attn_config=args.attn_config,
        ffn_config=args.ffn_config,
        first_layer_ffn_config=args.first_layer_ffn_config,
        use_activation_checkpointing=args.use_activation_checkpointing,
        use_sequence_parallel=args.use_sequence_parallel,
    )
    model_args = correct_model_args(model_args)
    if distributed.is_main_process():
        logger.info(f"The model config: {model_args}")

    # Let's be careful about the initialize_model_parallel_subgroup.
    attn_config = model_args.attn_config
    if (
        isinstance(attn_config, GenericAttnSpec)
        and attn_config.n_kv_heads < mpu.get_model_parallel_world_size()
    ):
        assert mpu.get_model_parallel_world_size() % attn_config.n_kv_heads == 0, (
            f"n_kv_heads={attn_config.n_kv_heads} must be a multiple of"
            f" model_parallel_size={mpu.get_model_parallel_world_size()}"
        )
        mpu.initialize_model_parallel_subgroup(
            "kv", mpu.get_model_parallel_world_size() // attn_config.n_kv_heads
        )
        if distributed.is_main_process():
            logger.info("Initialized kv subgroup.")

    # Create the online RL options
    online_rl_options = OnlineRLOptions(
        ce_loss_coeff=args.rl_dpo_ce_loss_coeff,
        rl_dpop_lambda=args.rl_dpop_lambda,
        use_ipo=args.rl_use_ipo,
        beta=args.rl_dpo_beta,
        label_smoothing=args.rl_label_smoothing,
        sync_ref_weights_per_iter=args.rl_sync_ref_weights_per_iter,
        num_pairs_per_prompt=args.rl_num_pairs_per_prompt,
        max_generation_steps=args.rl_max_generation_steps,
        online_rl=args.rl_online_rl,
    )
    online_rl_eval_options = OnlineRLOptions(
        ce_loss_coeff=args.rl_dpo_ce_loss_coeff,
        rl_dpop_lambda=args.rl_dpop_lambda,
        use_ipo=args.rl_use_ipo,
        beta=args.rl_dpo_beta,
        label_smoothing=args.rl_label_smoothing,
        num_pairs_per_prompt=1,  # only sample a single pair for eval
        sync_ref_weights_per_iter=-1,  # not used for eval
        max_generation_steps=args.rl_max_generation_steps,
        online_rl=args.rl_online_rl,
    )

    # We assume all the three models share the same architecture for simplicity.
    if distributed.is_main_process():
        logger.info(f"Load the policy model from {args.checkpoint}")
    policy_model_args = copy.deepcopy(model_args)
    if online_rl_options.online_rl:
        # Need both forward and decoding for online RL, thus can not use sequence parallel.
        policy_model_args.use_sequence_parallel = False
    else:
        policy_model_args.use_sequence_parallel = True and args.use_sequence_parallel
    policy_model = load_model_fn(
        policy_model_args,
        args.model_vocab_size,
        args.dtype,
        det_context,
        args.checkpoint,
    )
    policy_model.train()
    with torch.no_grad():
        ref_model_args = copy.deepcopy(model_args)
        if online_rl_options.online_rl:
            ref_model_args.use_sequence_parallel = True and args.use_sequence_parallel
        else:
            # Need both forward and decoding for online RL, thus can not use sequence parallel.
            ref_model_args.use_sequence_parallel = False
        ref_model = load_model_fn(
            ref_model_args,
            args.model_vocab_size,
            args.dtype,
            det_context,
            args.rl_ref_checkpoint,
        )
        ref_model.eval()
        if distributed.is_main_process():
            logger.info(f"Load the reference model from {args.rl_ref_checkpoint}")
        reward_model_args = copy.deepcopy(model_args)
        reward_model_args.use_sequence_parallel = False
        reward_model = load_model_fn(
            reward_model_args,
            args.model_vocab_size,
            args.dtype,
            det_context,
            args.rl_reward_checkpoint,
        )
        reward_model.eval()
        if distributed.is_main_process():
            logger.info(f"Load the reward model from {args.rl_reward_checkpoint}")

    optimizer, flat_model_state = configure_fsdp_optimizer(
        policy_model,
        weight_decay=args.weight_decay,
        learning_rate=args.learning_rate,
        betas=(args.beta1, args.beta2),
    )
    dist.broadcast(
        flat_model_state.params(),
        dist.get_global_rank(mpu.get_data_parallel_group(), 0),
        group=mpu.get_data_parallel_group(),
    )
    checkpoint_manager = checkpointing.CheckpointManager(det_context)

    # Create the PostIterHookParams and LRParams.
    post_iter_hook_params = PostIterHookParams(
        eval_interval=args.eval_interval,
        eval_log_interval=args.eval_log_interval,
        ckp_save_interval=args.rl_ckp_save_interval,
        max_iters=args.max_iters,
        start_iteration=start_iteration,
        full_config=full_config,
    )
    post_iter_hook_rl_params = PostIterHookParams(
        eval_interval=args.rl_eval_interval,
        eval_log_interval=args.eval_log_interval,
        ckp_save_interval=None,
        max_iters=args.max_iters,
        start_iteration=start_iteration,
        full_config=full_config,
    )
    lr_params = LRParams(
        decay_lr=args.decay_lr,
        learning_rate=args.learning_rate,
        min_lr=args.min_lr,
        warmup_iters=args.warmup_iters,
        lr_decay_iters=args.lr_decay_iters,
        max_iters=args.max_iters,
    )
    model_config = ModelArgs.schema().dump(correct_model_args(model_args))
    # TODO: we want to run evaluation for the initial checkpoint.
    # However, for unknown reason, the post_iter_hook call before the first training iteration will crash the job.
    # post_iter_hook(
    #     start_iteration,
    #     tokenizer=tokenizer,
    #     params=post_iter_hook_params,
    #     rl_options=online_rl_eval_options,
    #     policy_model=policy_model,
    #     ref_model=ref_model,
    #     reward_model=reward_model,
    #     model_config=model_config,
    #     det_context=det_context,
    #     checkpoint_manager=checkpoint_manager,
    #     dpo_eval_loaders_by_name=dpo_eval_loaders_by_name,
    #     llm_eval_loaders_by_name=llm_eval_loaders_by_name,
    # )

    lr_schedule = create_lr_schedule(lr_params)

    my_device = distributed.get_local_device()
    iter_train_loader = iter(train_loader)
    for iter_num in range(1, args.max_iters + 1):
        torch.cuda.synchronize()
        if distributed.is_main_process():
            logger.info(f"iter {iter_num}/{args.max_iters} starts")
        lr = lr_schedule(iter_num)
        for param_group in optimizer.param_groups:
            param_group["lr"].copy_(lr)

        cur_iter_loss = torch.zeros(
            1, dtype=torch.float32, device=distributed.get_local_device()
        )
        cur_iter_metrics: list[dict[str, float]] = []
        for index_in_batch in range(args.gradient_accumulation_steps):
            data: TensorDict = next(iter_train_loader)
            data = data.to(my_device, non_blocking=True)
            online_rl_data, data_metrics = get_online_rl_data_pairs(
                data,
                tokenizer,
                options=online_rl_options,
                model=policy_model if args.rl_online_rl else ref_model,
                reward_model=reward_model,
            )
            torch.cuda.synchronize()
            if distributed.is_main_process():
                logger.info(
                    f"iter {iter_num}/{args.max_iters} {index_in_batch}/{args.gradient_accumulation_steps}: finish sample {len(online_rl_data)} pairs"
                )
            dpo_metrics = []
            policy_model.train()
            for sample in online_rl_data:
                loss, cur_dpo_metrics = get_single_dpo_loss(
                    sample.to(my_device),
                    online_rl_options,
                    policy_model=policy_model,
                    ref_model=ref_model,
                )
                dpo_metrics.append(cur_dpo_metrics)
                # Each micro batch is enforced to have only one pair of positive and negative samples.
                loss /= args.rl_num_pairs_per_prompt * args.gradient_accumulation_steps
                loss.backward()
                cur_iter_loss += loss.detach()
            torch.cuda.synchronize()
            if distributed.is_main_process():
                logger.info(
                    f"iter {iter_num}/{args.max_iters} {index_in_batch}/{args.gradient_accumulation_steps}: finish compute the loss and backward"
                )
            # Compute metrics
            with torch.no_grad():
                dpo_metrics = utils.avg_list_of_dict(dpo_metrics)
                metrics = {**data_metrics, **dpo_metrics}
                metrics_numbers = {}
                keys = sorted(list(metrics.keys()))
                for k in keys:
                    v = metrics[k].float().detach()
                    if mpu.get_data_parallel_world_size() > 1:
                        dist.all_reduce(
                            v,
                            op=dist.ReduceOp.AVG,
                            group=mpu.get_data_parallel_group(),
                        )
                    metrics_numbers[k] = float(v.item())
                cur_iter_metrics.append(metrics_numbers)
        torch.cuda.synchronize()
        if distributed.is_main_process():
            logger.info(
                f"iter {iter_num}/{args.max_iters} finish the BP and compute metrics"
            )
        grad_norm = flat_model_optimizer_step(
            flat_model_state, optimizer, args.grad_clip
        )
        if mpu.get_data_parallel_world_size() > 1:
            dist.all_reduce(
                cur_iter_loss, op=dist.ReduceOp.AVG, group=mpu.get_data_parallel_group()
            )

        if distributed.is_main_process():
            logger.info(
                f"iter {iter_num}/{args.max_iters}: loss {cur_iter_loss.item():0.4f}, grad_norm: {float(grad_norm.item()):0.4f}"
            )

        if (
            iter_num % args.log_interval == 0 or iter_num < 10
        ) and distributed.is_main_process():
            iter_metrics_dict = utils.avg_list_of_dict(cur_iter_metrics)
            iter_metrics_dict["learning_rate"] = lr
            iter_metrics_dict["loss"] = float(cur_iter_loss.item())
            iter_metrics_dict["grad_norm"] = float(grad_norm.item())
            logutils.report_metrics(
                {f"train/{k}": v for k, v in iter_metrics_dict.items()},
                iter_num,
            )
        should_stop = post_iter_hook(
            iter_num,
            params=post_iter_hook_params,
            policy_model=policy_model,
            ref_model=ref_model,
            reward_model=reward_model,
            model_config=model_config,
            det_context=det_context,
            checkpoint_manager=checkpoint_manager,
            llm_eval_loaders_by_name=llm_eval_loaders_by_name,
        )
        should_stop_rl = post_iter_hook_rl(
            iter_num,
            params=post_iter_hook_rl_params,
            tokenizer=tokenizer,
            rl_options=online_rl_eval_options,
            policy_model=policy_model,
            ref_model=ref_model,
            reward_model=reward_model,
            det_context=det_context,
            rl_eval_loaders_by_name=dpo_eval_loaders_by_name,
        )
        if should_stop or should_stop_rl:
            logger.info("Asked to stop -- exiting early.")
            break
        # Update the reference model weights if needed.
        if (
            args.rl_sync_ref_weights_per_iter > 0
            and iter_num % args.rl_sync_ref_weights_per_iter == 0
        ):
            logger.info(f"Syncing the reference model weights at iter {iter_num}.")
            rlhf_utils.copy_weights_from_model(
                source_model=policy_model, target_model=ref_model
            )

    if distributed.is_main_process():
        logutils.shutdown_metrics_tracking()
    # On successful completion, clean up the last optimizer state checkpoint
    if checkpoint_manager.last_optimizer_location is not None:
        logger.info("Deleting optimizer state from final checkpoint.")
        checkpoint_manager.delete_optimizer_state(
            checkpoint_manager.last_optimizer_location
        )
    dist.destroy_process_group()
    logger.info("Finished the training!")


def main():
    from research.fastbackward import args

    if args.determined_enabled:
        rank = int(os.environ.get("RANK", -1))
        logging.debug(f"Found rank {rank}")
        sync_items = [
            args.checkpoint_dir,
            args.checkpoint,
            args.hf_checkpoint_dir,
            args.rl_ref_checkpoint,
            args.rl_reward_checkpoint,
            args.train_data_path,
            args.eval_data_path,
            args.rl_llm_eval_data_path,
        ]
        if args.additional_sync_items:
            if isinstance(args.additional_sync_items, str):
                args.additional_sync_items = args.additional_sync_items.split(",")
            logging.info(f"Syncing additional items: {args.additional_sync_items}")
            sync_items.extend(args.additional_sync_items)
        # sync_all_to_local() syncs the different hosts so we call it on every rank.
        sync_all_to_local(sync_items, rank)

    # Init distributed and set relevant variables
    logging.info("Initializing distributed...")
    distributed.init_distributed_for_training(
        args.model_parallel_size, distributed_backend="nccl"
    )
    if args.determined_enabled:
        rank = dist.get_rank()
        dist.barrier()
        if rank == 0:
            remove_sync_flag_file()
    logging.info("Done initializing distributed.")

    seed_offset = mpu.get_data_parallel_rank()
    args.add_arg("data_parallel_world_size", mpu.get_data_parallel_world_size())

    if args.determined_enabled:
        det_context_factory = partial(
            det.core.init,  # type: ignore
            distributed=(
                det.core.DistributedContext.from_torch_distributed()  # type: ignore
            ),
            tensorboard_mode=det.core.TensorboardMode.MANUAL,  # type: ignore
        )
    else:
        det_context_factory = contextlib.nullcontext

    with det_context_factory() as det_context:
        logutils.setup_logging(None, append_timestamp=True)

        torch.manual_seed(1337 + seed_offset)
        torch.cuda.manual_seed(1337 + seed_offset)
        torch.backends.cuda.matmul.allow_tf32 = True  # allow tf32 on matmul
        torch.backends.cudnn.allow_tf32 = True  # allow tf32 on cudnn
        assert args.dtype in ["float32", "bfloat16"], f"{args.dtype} unsupported"

        full_config = args.full_config()
        if distributed.is_main_process():
            assert args.run_name
            logutils.initialize_metrics_tracking(
                None
                if not args.wandb_project
                else logutils.WandbConfig(
                    project=args.wandb_project, name=args.run_name
                ),
                det_context,
                full_config,
                reinit=False,
            )
        train(det_context, full_config, args)


if __name__ == "__main__":
    main()

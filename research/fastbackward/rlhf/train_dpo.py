"""The offline reinforcement learning (RL) training script. We currently only implemented the DPO algorithm, and this script is kind of deprecated, please use `train_online_rl.py` instead."""

import contextlib
import logging
import pathlib
from functools import partial

import determined as det
import plotly
import plotly.express as px
import torch
import torch.distributed as dist
import torch.nn.functional as F
from datasets import Dataset as HFDataset
from datasets import concatenate_datasets
from tensordict.tensordict import TensorDict
from torch.utils.data import DataLoader
from torch.utils.data.distributed import DistributedSampler

import research.fastbackward.fs_model_parallel as mpu
from base.tokenizers import Tokenizer
from research.fastbackward import args, distributed, logutils
from research.fastbackward.checkpointing import checkpointing
from research.fastbackward.data import DistributedStepCountSampler
from research.fastbackward.losses import cross_entropy_loss
from research.fastbackward.model import (
    GenericAttnSpec,
    ModelArgs,
    Transformer,
    configure_fsdp_optimizer,
    correct_model_args,
)
from research.fastbackward.training import flat_model_optimizer_step
from research.fastbackward.utils import avg_list_of_dict

logger = logging.getLogger(__name__)


def pad(
    tensors: list[torch.Tensor],
    pad_value: int,
    capped_length: int | None = None,
    padding_side: str = "right",
) -> tuple[torch.Tensor, torch.Tensor, int]:
    """
    Returns stacked padded tensors and attention maps.
    """
    assert len(tensors[0].shape) == 1, "tensors must be 1-dimensional"
    assert padding_side in [
        "right",
        "left",
    ], f"padding_side must be 'right' or 'left', got {padding_side}"

    max_len = max(t.shape[0] for t in tensors)
    if capped_length is None:
        pass
    else:
        if max_len > capped_length:
            logger.info(
                f"Some tensors are longer ({max_len=}) than {capped_length=}, truncating them."
            )
            tensors = [t[:capped_length] for t in tensors]
        max_len = capped_length

    result = []
    attention_maps = []
    for t in tensors:
        add = torch.full(
            (max_len - t.shape[0],), pad_value, dtype=t.dtype, device=t.device
        )
        cur_attention_map = torch.zeros([max_len], dtype=torch.bool, device=t.device)
        if padding_side == "right":
            t_padded = torch.cat([t, add], dim=0)
            cur_attention_map[: t.shape[0]] = True
        else:
            t_padded = torch.cat([add, t], dim=0)
            cur_attention_map[-t.shape[0] :] = True
        result.append(t_padded)
        attention_maps.append(cur_attention_map)
    return torch.stack(result), torch.stack(attention_maps), max_len


def get_dpo_collate_fn(
    tokenizer: Tokenizer,
    seqlen: int,
):
    def collate_fn(samples: list[dict]) -> TensorDict:
        """Collates a list of preference samples into a batch.
        Args:
            samples: list of preference samples
                Every sample is a dict with the following keys:
                    - prompt_tokens: list[int]
                    - pos_tokens: list[int]
                    - neg_tokens: list[int]

        Returns:
            A dict with the following Tensors:
                - prompt_tokens: int, [batch, prompt_length]
                - prompt_attention_maps: bool, [batch, prompt_length]
                - pos_tokens: int, [batch, positive_target_length]
                - pos_att_maps: bool, [batch, positive_target_length]
                - neg_tokens: int, [batch, negative_target_length]
                - neg_att_maps: bool, [batch, negative_target_length]
        """
        special_tokens = tokenizer.special_tokens
        prompt_tokens, prompt_attention_maps, max_prompt_length = pad(
            [torch.tensor(s["prompt_tokens"], dtype=torch.int64) for s in samples],
            pad_value=special_tokens.padding,
            capped_length=None,
            padding_side="left",
        )
        assert max_prompt_length < seqlen, f"{max_prompt_length=} {seqlen=}"
        # Note(yuri): Technically, memory-wise, it would be optimal to pad (and process) chosen and rejected continuations separately.
        # But to make the training code simpler (e.g. to be able to concat them together), we just pad them together.
        pos_tokens, pos_att_maps, _ = pad(
            [
                torch.tensor(s["pos_tokens"] + [special_tokens.eos], dtype=torch.int64)
                for s in samples
            ],
            pad_value=special_tokens.padding,
            capped_length=seqlen - max_prompt_length,
            padding_side="right",
        )
        neg_tokens, neg_att_maps, _ = pad(
            [
                torch.tensor(s["neg_tokens"] + [special_tokens.eos], dtype=torch.int64)
                for s in samples
            ],
            pad_value=special_tokens.padding,
            capped_length=seqlen - max_prompt_length,
            padding_side="right",
        )
        assert (
            prompt_tokens.shape[1] + pos_tokens.shape[1] == seqlen
        ), f"{prompt_tokens.shape} + {pos_tokens.shape} != {seqlen}"
        assert (
            prompt_tokens.shape[1] + neg_tokens.shape[1] == seqlen
        ), f"{prompt_tokens.shape} + {neg_tokens.shape} != {seqlen}"

        return TensorDict(
            {
                "prompt_tokens": prompt_tokens,
                "prompt_attention_maps": prompt_attention_maps,
                "pos_tokens": pos_tokens,
                "pos_att_maps": pos_att_maps,
                "neg_tokens": neg_tokens,
                "neg_att_maps": neg_att_maps,
            },
            batch_size=len(samples),
        )

    return collate_fn


def get_llm_collate_fn(
    tokenizer: Tokenizer,
    seqlen: int,
):
    def collate_fn(samples: list[dict]) -> TensorDict:
        """Collates a list of preference samples into a batch.
        Args:
            samples: list of preference samples
                Every sample is a dict with the following keys:
                    - prompt_tokens: list[int]
                    - target_tokens: list[int] (which should contain the EOS token by itself.)

        Returns:
            A dict with the following Tensors:
                - x: int, [batch, seqlen]
                - y: int, [batch, seqlen]
                - masks: bool, [batch, seqlen]
        """
        special_tokens = tokenizer.special_tokens

        tokens: list[list[int]] = []
        masks: list[list[bool]] = []
        for sample in samples:
            cur_tokens = sample["prompt_tokens"] + sample["target_tokens"]
            if len(cur_tokens) > seqlen + 1:
                cur_tokens = cur_tokens[: seqlen + 1]
                cur_mask = [True] * len(sample["prompt_tokens"]) + [False] * (
                    seqlen + 1 - len(sample["prompt_tokens"])
                )
            else:
                cur_mask = (
                    [True] * len(sample["prompt_tokens"])
                    + [False] * len(sample["target_tokens"])
                    + [True] * (seqlen + 1 - len(cur_tokens))
                )
                cur_tokens += [special_tokens.padding] * (seqlen + 1 - len(cur_tokens))
            tokens.append(cur_tokens)
            masks.append(cur_mask)
        torch_tokens = torch.tensor(tokens, dtype=torch.int64)
        torch_masks = torch.tensor(masks, dtype=torch.bool)
        return TensorDict(
            {
                "x": torch_tokens[:, :seqlen],
                "y": torch_tokens[:, 1 : seqlen + 1],
                "masks": torch_masks[:, 1 : seqlen + 1],
            },
            batch_size=len(samples),
        )

    return collate_fn


def load_model_fn(
    model_args: ModelArgs,
    det_context: det.core.Context | None,  # type: ignore
    checkpoint_location: str,
) -> Transformer:
    assert args.dtype in ["float32", "bfloat16"], f"{args.dtype} unsupported"
    ptdtype = {"float32": torch.float32, "bfloat16": torch.bfloat16}[args.dtype]

    model = Transformer(model_args, skip_init=True)
    model.to(distributed.get_local_device(), ptdtype)

    checkpoint_manager = checkpointing.CheckpointManager(det_context)
    state_dict = checkpoint_manager.load_state_dict_checkpoint(
        checkpoint_location,
        model_vocab_size=args.model_vocab_size,
    )
    model.load_state_dict(state_dict)

    return model


def forward_pass(
    model: Transformer,
    prompt_tokens: torch.Tensor,
    prompt_attention_maps: torch.Tensor,
    continuation_tokens: torch.Tensor,
    continuation_attention_maps: torch.Tensor,
) -> TensorDict:
    """Performs Transformer forward pass and returns values that are needed for
    RLHF algorithms supported in this script.

    Args:
        model: Transformer model.
        prompt_tokens: int, [batch, prompt_length]
        prompt_attention_maps: bool, [batch, prompt_length]
        continuation_tokens: int, [batch, continuation_length]
        continuation_attention_maps: bool, [batch, continuation_length]

    Returns:
        dict:
            - "local_continuation_logits": [batch, continuation_length, vocab_size // model_parallel_size]
            - "continuation_logprobs": [batch, continuation_length]
    """
    batch, continuation_length = continuation_tokens.shape

    # Stack prompt and continuation together
    full_tokens = torch.cat([prompt_tokens, continuation_tokens], dim=1)
    full_attention_maps = torch.cat(
        [prompt_attention_maps, continuation_attention_maps], dim=1
    )[:, None, None, :]

    # Broadcast attention maps
    full_attention_maps = full_attention_maps.repeat(
        1, 1, full_attention_maps.shape[-1], 1
    )
    full_attention_maps = torch.tril(full_attention_maps)
    # To prevent some padded values from having empty attention
    diag = torch.eye(
        full_attention_maps.shape[-1],
        dtype=torch.bool,
        device=distributed.get_local_device(),
    )[None, None]
    full_attention_maps |= diag

    local_logits: torch.Tensor = model(full_tokens, attention_mask=full_attention_maps)
    local_continuation_logits = local_logits[:, -continuation_length - 1 : -1]

    continuation_logits = mpu.gather_from_model_parallel_region(
        local_continuation_logits
    )
    continuation_logprobs = (
        F.log_softmax(continuation_logits, dim=-1)
        .gather(2, continuation_tokens[:, :, None])
        .squeeze(2)
    )

    return TensorDict(
        {
            "local_continuation_logits": local_continuation_logits,
            "continuation_logprobs": continuation_logprobs,
        },
        batch_size=batch,
    )


def get_dpo_loss(
    data: TensorDict,
    policy_model: Transformer,
    ref_model: Transformer,
) -> tuple[torch.Tensor, dict[str, float], dict[str, list]]:
    """Computes dpo loss. + adds weighted cross-entropy loss using `args.rl_dpo_ce_loss_coeff`.
        This function does not run reference model online, so log probabilities should
        be passed as arguments.
    Args:
        prompt_tokens: int, [batch, prompt_length]
        prompt_attention_maps: bool, [batch, prompt_length]
        chosen_continuation_tokens: int, [batch, chosen_continuation_length]
        chosen_continuation_attention_maps: bool, [batch, chosen_continuation_length]
        chosen_continuation_ref_logprobs: float [batch, chosen_continuation_length]
        rejected_continuation_tokens: int, [batch, rejected_continuation_length]
        rejected_continuation_attention_maps: bool, [batch, rejected_continuation_length]
        rejected_continuation_ref_logprobs: float [batch, rejected_continuation_length]
        policy_model: Transformer

    Returns:
        dpo_loss: [1]
            Local DPO loss (i.e. before data parallel reduce)
        metrics: dict[str, float]
            all_reduced single number metrics
        distributions: dict[str, list]
            all_reduced distribution metrics.
            Used to draw histograms in visualization.
    """
    prompt_tokens = data["prompt_tokens"]
    prompt_attention_maps = data["prompt_attention_maps"]
    pos_tokens = data["pos_tokens"]
    pos_attention_maps = data["pos_att_maps"]
    neg_tokens = data["neg_tokens"]
    neg_attention_maps = data["neg_att_maps"]

    policy_model_pos_outputs = forward_pass(
        policy_model,
        prompt_tokens,
        prompt_attention_maps,
        pos_tokens,
        pos_attention_maps,
    )
    policy_model_neg_outputs = forward_pass(
        policy_model,
        prompt_tokens,
        prompt_attention_maps,
        neg_tokens,
        neg_attention_maps,
    )
    with torch.no_grad():
        ref_model_pos_outputs = forward_pass(
            ref_model,
            prompt_tokens,
            prompt_attention_maps,
            pos_tokens,
            pos_attention_maps,
        )
        ref_model_neg_outputs = forward_pass(
            ref_model,
            prompt_tokens,
            prompt_attention_maps,
            neg_tokens,
            neg_attention_maps,
        )

    # Compute cross-entropy loss on positive target
    pos_tokens_masked = torch.where(pos_attention_maps, pos_tokens, -1)
    ce_loss = cross_entropy_loss(
        None,  # type: ignore
        pos_tokens_masked,
        policy_model_pos_outputs["local_continuation_logits"],
    )

    # Compute DPO loss
    pos_logprobs_sum = (
        policy_model_pos_outputs["continuation_logprobs"] * pos_attention_maps
    ).sum(dim=-1)
    neg_logprobs_sum = (
        policy_model_neg_outputs["continuation_logprobs"] * neg_attention_maps
    ).sum(dim=-1)
    pos_ref_logprobs_sum = (
        ref_model_pos_outputs["continuation_logprobs"] * pos_attention_maps
    ).sum(dim=-1)
    neg_ref_logprobs_sum = (
        ref_model_neg_outputs["continuation_logprobs"] * neg_attention_maps
    ).sum(dim=-1)

    pi_logratios = pos_logprobs_sum - neg_logprobs_sum
    ref_logratios = pos_ref_logprobs_sum - neg_ref_logprobs_sum

    dpo_loss = -F.logsigmoid(args.rl_dpo_beta * (pi_logratios - ref_logratios))
    dpo_loss = dpo_loss.mean()

    # Combine losses
    loss = dpo_loss + args.rl_dpo_ce_loss_coeff * ce_loss

    # Compute metrics
    with torch.no_grad():
        pos_rewards = pos_logprobs_sum.detach() - pos_ref_logprobs_sum.detach()
        neg_rewards = neg_logprobs_sum.detach() - neg_ref_logprobs_sum.detach()
        accuracy = (pos_rewards > neg_rewards).float()
        margins = pos_rewards - neg_rewards

        metrics = {
            "rewards_for_positive_target": pos_rewards.mean(),
            "rewards_for_negative_target": neg_rewards.mean(),
            "accuracy": accuracy.mean(),
            "margins": margins.mean(),
            "dpo_loss": dpo_loss,
            "ce_loss": ce_loss,
            "pi_logratios": pi_logratios.mean(),
            "ref_logratios": ref_logratios.mean(),
            "positive_logprobs_sum": pos_logprobs_sum.mean(),
            "positive_logprobs_avg": (
                pos_logprobs_sum / pos_attention_maps.sum(dim=1)
            ).mean(),
            "positive_continuation_length_avg": pos_attention_maps.sum(dim=1)
            .float()
            .mean(),
            "negative_logprobs_sum": neg_logprobs_sum.mean(),
            "negative_logprobs_avg": (
                neg_logprobs_sum / neg_attention_maps.sum(dim=1)
            ).mean(),
            "negative_continuation_length_avg": neg_attention_maps.sum(dim=1)
            .float()
            .mean(),
        }
    metrics_numbers: dict[str, float] = {}
    for k, v in metrics.items():
        v = v.detach()
        dist.all_reduce(v, op=dist.ReduceOp.AVG, group=mpu.get_data_parallel_group())
        metrics_numbers[k] = float(v.item())

    distributions = {
        "pi_logratios": pi_logratios,
    }
    distributions_lists: dict[str, list] = {}
    for k, v in distributions.items():
        v = v.detach()
        gathered_tensors = [
            torch.zeros_like(v) for _ in range(mpu.get_data_parallel_world_size())
        ]
        dist.all_gather(gathered_tensors, v, group=mpu.get_data_parallel_group())

        gathered_values = []
        for t in gathered_tensors:
            assert len(t.shape) == 1
            gathered_values.extend(t.tolist())
        distributions_lists[k] = gathered_values

    return loss, metrics_numbers, distributions_lists


def prepare_wandb_distribution(
    distrs: list[dict[str, list]],
) -> dict[str, plotly.graph_objs.Figure]:
    """
    Example input structure:
        0 - {"pi_logratios": [1, 2, 3, 4]}
        1 - {"pi_logratios": [1, 2, 3, 4]}
        ...
    """
    assert distributed.is_main_process()

    result = {}
    for k in distrs[0].keys():
        assert isinstance(distrs[0][k], list)
        values = sum([d[k] for d in distrs], [])

        # For each distribution, we plot (emprical) CDF
        result[k] = px.ecdf(values, labels={"x": k})

    return result


def compute_llm_eval_metrics(
    eval_data: TensorDict, model: Transformer
) -> dict[str, float]:
    x, y, masks = eval_data["x"], eval_data["y"], eval_data["masks"]
    y_masked = torch.where(~masks, y, -1)
    local_logits_policy: torch.Tensor = model(x)
    # Compute the cross-entropy loss
    ce_loss = cross_entropy_loss(
        None,  # type: ignore
        y_masked,
        local_logits_policy,
    )
    if mpu.get_data_parallel_world_size() > 1:
        dist.all_reduce(
            ce_loss,
            op=dist.ReduceOp.AVG,
            group=mpu.get_data_parallel_group(),
        )
    # Compute the token accuracy
    if mpu.get_model_parallel_world_size() > 1:
        greedy_pred = distributed.dist_argmax(
            local_logits_policy,
            dim=-1,
            group=mpu.get_model_parallel_group(),
        )
    else:
        greedy_pred = local_logits_policy.argmax(dim=-1)
    correct_pred = (greedy_pred == y) & (~masks)
    token_accuracy = correct_pred.sum(dim=-1).float() / (
        (~masks).sum(dim=-1).float() + 1e-6
    )
    token_accuracy = token_accuracy.mean()
    if mpu.get_data_parallel_world_size() > 1:
        dist.all_reduce(
            token_accuracy,
            op=dist.ReduceOp.AVG,
            group=mpu.get_data_parallel_group(),
        )
    token_acc_avg = token_accuracy.item()
    return {
        "token_accuracy": token_acc_avg,
        "ce_loss": ce_loss.item(),
    }


def post_iter_hook(
    iter_num: int,
    policy_model: Transformer,
    ref_model: Transformer,
    det_context: det.core.Context | None,  # type: ignore
    dpo_eval_loaders_by_name: dict[str, DataLoader],
    llm_eval_loaders_by_name: dict[str, DataLoader],
):
    is_last_iter = iter_num == args.max_iters
    should_run_eval = (
        # It's the starting step.
        iter_num == 0
        # It's at an eval interval.
        or (args.eval_interval > 0 and (iter_num % args.eval_interval == 0))
        # It's at the last iteration.
        or is_last_iter
    )
    if not should_run_eval:
        return

    my_device = distributed.get_local_device()
    with torch.no_grad():
        policy_model.eval()
        ref_model.eval()
        # Let's also evaluate the LLM metrics
        for eval_name, eval_loader in llm_eval_loaders_by_name.items():
            eval_metrics = []
            for i, eval_data in enumerate(eval_loader):
                if distributed.is_main_process() and (i % args.eval_log_interval == 0):
                    logger.info(f"Eval {eval_name} iter {i}/{len(eval_loader)}")
                eval_data = eval_data.to(my_device, non_blocking=True)
                ## LLM metrics for policy and reference models
                llm_metric_policy = compute_llm_eval_metrics(eval_data, policy_model)
                llm_metric_ref = compute_llm_eval_metrics(eval_data, ref_model)
                merged_metrics = {}
                for key, value in llm_metric_policy.items():
                    merged_metrics[f"policy/{key}"] = value
                for key, value in llm_metric_ref.items():
                    merged_metrics[f"reference/{key}"] = value
                eval_metrics.append(merged_metrics)
            eval_metrics = avg_list_of_dict(eval_metrics)
            logger.info(
                f"Device {my_device}: LLM-Eval {eval_name} metrics: {eval_metrics}"
            )
            if distributed.is_main_process():
                logutils.report_metrics(
                    {f"llm_eval/{eval_name}/{k}": v for k, v in eval_metrics.items()},
                    iter_num,
                )
        # Let's also evaluate the DPO metrics
        for eval_name, eval_loader in dpo_eval_loaders_by_name.items():
            eval_metrics = []
            eval_distributions = []
            for i, eval_data in enumerate(eval_loader):
                if distributed.is_main_process() and (i % args.eval_log_interval == 0):
                    logger.info(f"Eval {eval_name} iter {i}/{len(eval_loader)}")
                eval_data = eval_data.to(my_device, non_blocking=True)
                cur_batch_loss, cur_batch_metrics, cur_batch_distrs = get_dpo_loss(
                    eval_data,
                    policy_model,
                    ref_model,
                )
                if mpu.get_data_parallel_world_size() > 1:
                    dist.all_reduce(
                        cur_batch_loss,
                        op=dist.ReduceOp.AVG,
                        group=mpu.get_data_parallel_group(),
                    )
                cur_batch_metrics["loss"] = cur_batch_loss.item()
                eval_metrics.append(cur_batch_metrics)
                eval_distributions.append(cur_batch_distrs)
            eval_metrics = avg_list_of_dict(eval_metrics)
            logger.info(
                f"Device {my_device}: DPO-Eval {eval_name} metrics: {eval_metrics}"
            )
            if distributed.is_main_process():
                eval_distributions = prepare_wandb_distribution(eval_distributions)
                logutils.report_metrics(
                    {f"dpo_eval/{eval_name}/{k}": v for k, v in eval_metrics.items()}
                    | {
                        f"dpo_eval/{eval_name}/{k}_distribution": v
                        for k, v in eval_distributions.items()
                    },
                    iter_num,
                )

    policy_model.train()

    if args.rl_ckp_save_interval == -1:
        return
    if is_last_iter or (iter_num % args.rl_ckp_save_interval == 0 and iter_num > 0):
        checkpoint_manager = checkpointing.CheckpointManager(det_context)
        checkpoint_location = checkpointing.get_checkpoint_location(iter_num, None)
        checkpoint_location = checkpoint_manager.checkpoint(
            checkpoint_location,
            model_state_dict=policy_model.state_dict(),
            model_config=args.full_config(),
            iter_num=iter_num,
            metadata={
                "iter_num": iter_num,
            },
        )


def train(det_context: det.core.Context | None):  # type: ignore
    # Build train dataset
    assert args.train_data_path, "Must provide a train_data_path"
    train_datasets = []
    for idx_path, path in enumerate(args.train_data_path.split(";")):
        assert pathlib.Path(path).exists()
        cur_dataset = HFDataset.load_from_disk(path)
        train_datasets.append(cur_dataset)
        logger.info(f"{idx_path=}: {len(cur_dataset)=}")
    if len(train_datasets) == 1:
        train_dataset = train_datasets[0]
    else:
        train_dataset = concatenate_datasets(train_datasets)
    logger.info(f"The training data path: {args.train_data_path}")
    logger.info(f"Total training dataset size: {len(train_dataset)}")
    if args.max_iters == 0:
        assert args.max_epochs > 0, "If max_iters==0, max_epochs should be >0"
        args.max_iters = (
            args.max_epochs
            * len(train_dataset)
            // (
                args.batch_size
                * args.gradient_accumulation_steps
                * mpu.get_data_parallel_world_size()
            )
        )

    assert not args.use_research_tokenizer
    assert args.tokenizer_name
    tokenizer = logutils.get_base_tokenizer(args.tokenizer_name)

    # Build eval datasets
    assert args.eval_data_path, "Must provide a eval_data_path"
    dpo_eval_datasets = {}
    for idx, eval_data_path in enumerate(args.eval_data_path.split(";")):
        if "@" in eval_data_path:
            key = eval_data_path.split("@")[0]
            eval_data_path = eval_data_path.split("@")[1]
        else:
            key = f"eval_{idx}"
        dpo_eval_datasets[key] = HFDataset.load_from_disk(eval_data_path)
        if distributed.is_main_process():
            logger.info(f"DPO-Eval {key} dataset size: {len(dpo_eval_datasets[key])}")
    assert args.rl_llm_eval_data_path, "Must provide a rl_llm_eval_data_path"
    llm_eval_datasets = {}
    for idx, llm_eval_data_path in enumerate(args.rl_llm_eval_data_path.split(";")):
        if "@" in llm_eval_data_path:
            key = llm_eval_data_path.split("@")[0]
            llm_eval_data_path = llm_eval_data_path.split("@")[1]
        else:
            key = f"llm_eval_{idx}"
        llm_eval_datasets[key] = HFDataset.load_from_disk(llm_eval_data_path)
        if distributed.is_main_process():
            logger.info(f"LLM-Eval {key} dataset size: {len(llm_eval_datasets[key])}")

    dpo_eval_loaders_by_name = {}
    for eval_name, eval_dataset in dpo_eval_datasets.items():
        eval_loader = DataLoader(
            eval_dataset,  # type: ignore
            batch_size=args.batch_size,
            sampler=DistributedSampler(
                eval_dataset,  # type: ignore
                num_replicas=mpu.get_data_parallel_world_size(),
                rank=mpu.get_data_parallel_rank(),
                shuffle=False,
                drop_last=True,
            ),
            collate_fn=get_dpo_collate_fn(tokenizer, args.block_size),
            pin_memory=True,
            drop_last=True,
        )
        dpo_eval_loaders_by_name[eval_name] = eval_loader
    llm_eval_loaders_by_name = {}
    for eval_name, eval_dataset in llm_eval_datasets.items():
        eval_loader = DataLoader(
            eval_dataset,  # type: ignore
            batch_size=args.batch_size,
            sampler=DistributedSampler(
                eval_dataset,  # type: ignore
                num_replicas=mpu.get_data_parallel_world_size(),
                rank=mpu.get_data_parallel_rank(),
                shuffle=False,
                drop_last=True,
            ),
            collate_fn=get_llm_collate_fn(tokenizer, args.block_size),
            pin_memory=True,
            drop_last=True,
        )
        llm_eval_loaders_by_name[eval_name] = eval_loader

    # Set up the training data loader
    start_iteration = 0
    train_sampler = DistributedStepCountSampler(
        train_dataset,  # type: ignore
        # NOTE(arun): We now draw samples from the dataset in micro-batches, which
        # means we have to account for the number gradient accumulation steps in
        # each iteration.
        batch_size=args.batch_size,
        step_count=args.max_iters * args.gradient_accumulation_steps,
        start_step=start_iteration * args.gradient_accumulation_steps,
        dp_rank=mpu.get_data_parallel_rank(),
        dp_world_size=mpu.get_data_parallel_world_size(),
        seed=args.data_sampler_rand_seed,
        cross_shuffle=args.cross_shuffle,
    )
    if distributed.is_main_process():
        logger.info(f"Total steps per epoch: {train_sampler.steps_per_epoch}")
    train_loader = DataLoader(
        train_dataset,  # type: ignore
        batch_sampler=train_sampler,
        num_workers=4,  # TODO: try different values
        collate_fn=get_dpo_collate_fn(tokenizer, args.block_size),
        pin_memory=True,
    )

    model_args = ModelArgs(
        dim=args.n_embd,
        n_layers=args.n_layers,
        n_heads=args.n_heads,
        n_kv_heads=args.n_kv_heads,
        multiple_of=args.ffn_dim_multiple_of,
        ffn_dim_multiplier=args.ffn_dim_multiplier,
        norm_eps=args.norm_eps,
        max_seq_len=args.block_size,
        vocab_size=args.model_vocab_size,
        rope_theta=args.rope_theta,
        rope_scaling_factor=args.rope_scaling_factor,
        ffn_type=args.ffn_type,
        bias=args.bias,
        norm_type=args.norm_type,
        pos_embed_type=args.pos_embed_type,
        rotary_config=args.rotary_config,
        attn_config=args.attn_config,
        ffn_config=args.ffn_config,
        first_layer_ffn_config=args.first_layer_ffn_config,
        use_activation_checkpointing=args.use_activation_checkpointing,
        use_sequence_parallel=args.use_sequence_parallel,
    )
    model_args = correct_model_args(model_args)
    logger.info(f"The model config: {model_args}")

    # Let's be careful about the initialize_model_parallel_subgroup.
    attn_config = model_args.attn_config
    if (
        isinstance(attn_config, GenericAttnSpec)
        and attn_config.n_kv_heads < mpu.get_model_parallel_world_size()
    ):
        assert mpu.get_model_parallel_world_size() % attn_config.n_kv_heads == 0, (
            f"n_kv_heads={attn_config.n_kv_heads} must be a multiple of"
            f" model_parallel_size={mpu.get_model_parallel_world_size()}"
        )
        mpu.initialize_model_parallel_subgroup(
            "kv", mpu.get_model_parallel_world_size() // attn_config.n_kv_heads
        )

    logger.info(f"Load the policy model from {args.checkpoint}")
    policy_model = load_model_fn(model_args, det_context, args.checkpoint)
    with torch.no_grad():
        ref_model = load_model_fn(model_args, det_context, args.rl_ref_checkpoint)
        ref_model.eval()
    optimizer, flat_model_state = configure_fsdp_optimizer(
        policy_model,
        weight_decay=args.weight_decay,
        learning_rate=args.learning_rate,
        betas=(args.beta1, args.beta2),
    )
    dist.broadcast(
        flat_model_state.params(),
        dist.get_global_rank(mpu.get_data_parallel_group(), 0),
        group=mpu.get_data_parallel_group(),
    )

    post_iter_hook(
        0,
        policy_model,
        ref_model,
        det_context,
        dpo_eval_loaders_by_name,
        llm_eval_loaders_by_name,
    )

    my_device = distributed.get_local_device()
    iter_train_loader = iter(train_loader)
    for iter_num in range(1, args.max_iters + 1):
        cur_iter_loss = torch.zeros(
            [], dtype=torch.float32, device=distributed.get_local_device()
        )
        cur_iter_metrics = []
        cur_iter_distrs = []
        for _ in range(args.gradient_accumulation_steps):
            data: TensorDict = next(iter_train_loader)
            data = data.to(my_device, non_blocking=True)
            loss, metrics, distributions = get_dpo_loss(
                data,
                policy_model,
                ref_model,
            )
            loss /= args.gradient_accumulation_steps
            loss.backward()
            cur_iter_loss += loss.detach()
            cur_iter_metrics.append(metrics)
            cur_iter_distrs.append(distributions)

        grad_norm = flat_model_optimizer_step(
            flat_model_state, optimizer, args.grad_clip
        )
        if mpu.get_data_parallel_world_size() > 1:
            dist.all_reduce(
                cur_iter_loss, op=dist.ReduceOp.AVG, group=mpu.get_data_parallel_group()
            )

        if distributed.is_main_process():
            logger.info(
                f"iter {iter_num}/{args.max_iters}: loss {cur_iter_loss.item():0.4f}, grad_norm: {float(grad_norm.item()):0.4f}"
            )

        if (
            iter_num % args.log_interval == 0 or iter_num < 10
        ) and distributed.is_main_process():
            cur_iter_metrics = avg_list_of_dict(cur_iter_metrics)
            cur_iter_metrics["loss"] = float(cur_iter_loss.item())
            cur_iter_metrics["grad_norm"] = float(grad_norm.item())
            cur_iter_distrs = prepare_wandb_distribution(cur_iter_distrs)
            logutils.report_metrics(
                {f"train/{k}": v for k, v in cur_iter_metrics.items()}
                | {f"train/{k}_distribution": v for k, v in cur_iter_distrs.items()},
                iter_num,
            )
        post_iter_hook(
            iter_num,
            policy_model,
            ref_model,
            det_context,
            dpo_eval_loaders_by_name,
            llm_eval_loaders_by_name,
        )


def main():
    distributed.init_distributed_for_training(
        args.model_parallel_size, distributed_backend="nccl"
    )
    args.n_kv_heads = args.n_kv_heads or args.n_heads
    if args.n_kv_heads < mpu.get_model_parallel_world_size():
        assert mpu.get_model_parallel_world_size() % args.n_kv_heads == 0
        mpu.initialize_model_parallel_subgroup(
            "kv", mpu.get_model_parallel_world_size() // args.n_kv_heads
        )
    seed_offset = mpu.get_data_parallel_rank()
    args.add_arg("data_parallel_world_size", mpu.get_data_parallel_world_size())

    if args.determined_enabled:
        det_context_factory = partial(
            det.core.init,  # type: ignore
            distributed=(
                det.core.DistributedContext.from_torch_distributed()  # type: ignore
            ),
            tensorboard_mode=det.core.TensorboardMode.MANUAL,  # type: ignore
        )
    else:
        det_context_factory = contextlib.nullcontext

    with det_context_factory() as det_context:
        logutils.setup_logging(None, append_timestamp=True)

        torch.manual_seed(1337 + seed_offset)
        torch.cuda.manual_seed(1337 + seed_offset)
        torch.backends.cuda.matmul.allow_tf32 = True  # allow tf32 on matmul
        torch.backends.cudnn.allow_tf32 = True  # allow tf32 on cudnn
        assert args.dtype in ["float32", "bfloat16"], f"{args.dtype} unsupported"

        if distributed.is_main_process():
            assert args.wandb_project
            assert args.run_name
            logutils.initialize_metrics_tracking(
                logutils.WandbConfig(project=args.wandb_project, name=args.run_name),
                det_context,
                args.full_config(),
                reinit=False,
            )

        assert args.rl_ckp_save_interval % args.eval_interval == 0
        train(det_context)


if __name__ == "__main__":
    main()

"""The SFT training script (primarily for the reward model training in RLHF).

We used the huggingface's datasets library for the data loading, where each data sample is a dictionary with various keys and values as list of integers.
Use the `sft_collate_strategy` FLAG defined in `args.py` to specify the data collating strategy that converts the above dictionary into a batch of tensors.
"""

# pylint: disable=logging-fstring-interpolation
from __future__ import annotations

import atexit
import contextlib
import dataclasses
import json
import logging
import os
import random
from collections.abc import Sequence
from functools import partial
from pathlib import Path

import determined as det
import requests
import torch
import torch.distributed as dist
from dataclasses_json import DataClassJsonMixin
from datasets import Dataset as HFDataset
from datasets import concatenate_datasets
from torch.utils.data import DataLoader
from torch.utils.data.distributed import DistributedSampler

import research.fastbackward.fs_model_parallel as mpu
from base.tokenizers.tokenizer import Tokenizer
from research.core.tokenizers import get_tokenizer
from research.determined_utils import remove_sync_flag_file, sync_all_to_local
from research.fastbackward import (
    args,
    distributed,
    evaluation,
    logutils,
    losses,
    metrics,
    training,
)
from research.fastbackward.checkpointing import checkpointing
from research.fastbackward.checkpointing import huggingface as huggingface_checkpointing
from research.fastbackward.data import DistributedStepCountSampler
from research.fastbackward.mixed_precision_adam import MixedPrecisionAdamW
from research.fastbackward.model import (
    GenericAttnSpec,
    ModelArgs,
    Transformer,
    configure_fsdp_optimizer,
    correct_model_args,
)
from research.fastbackward.rlhf import rlhf_utils
from research.fastbackward.rlhf.reward_aux_funcs import (
    build_is_positive_reward_tokens,
    build_pairwise_reward_tokens,
    build_singular_reward_tokens,
    build_user_behavior_reward_tokens,
    build_xy_tensor,
)

logger = logging.getLogger(__name__)
_latest_iteration = 0


# Because we define everything in `__main__`, we need to allow redefinition of top-level
# variables and apparent uses before assignment. We also need to disable the naming
# convention check since Pylint will recognize variables with a constant initialization
# as constants.
# pylint:disable=redefined-outer-name,used-before-assignment,invalid-name


def get_sft_collate_fn(
    tokenizer: Tokenizer,
    seqlen: int,
    strategy: str = "reward_by_is_positive",
    training: bool = True,
):
    """Creates a `collate_fn` to pass to a DataLoader."""
    special_tokens = tokenizer.special_tokens

    def collate_fn(samples: list[dict]) -> tuple[torch.Tensor, torch.Tensor, int]:
        assert len(samples) > 0, f"{len(samples)=}"
        all_xy: list[tuple[torch.Tensor, torch.Tensor]] = []
        for sample in samples:
            if strategy == "pairwise":
                prompt_tokens, target_tokens = build_pairwise_reward_tokens(
                    sample, tokenizer, max_response_tokens=64, shuffle=True
                )
                x, y = build_xy_tensor(
                    prompt_tokens,
                    target_tokens,
                    eos=special_tokens.eos,
                    padding=special_tokens.padding,
                    seqlen=seqlen,
                )
                all_xy.append((x, y))
            elif strategy.startswith("absolute"):
                pos_data, neg_data = build_singular_reward_tokens(
                    sample, tokenizer, seqlen=seqlen
                )
                if pos_data is not None:
                    x, y = build_xy_tensor(
                        pos_data[0],
                        pos_data[1],
                        eos=special_tokens.eos,
                        padding=special_tokens.padding,
                        seqlen=seqlen,
                    )
                    all_xy.append((x, y))
                x, y = build_xy_tensor(
                    neg_data[0],
                    neg_data[1],
                    eos=special_tokens.eos,
                    padding=special_tokens.padding,
                    seqlen=seqlen,
                )
                all_xy.append((x, y))
            elif strategy == "reward_by_is_positive":
                input_tokens, reward = build_is_positive_reward_tokens(
                    sample, tokenizer, seqlen=seqlen
                )
                x, y = build_xy_tensor(
                    input_tokens,
                    reward,
                    eos=None,  # no need to learn this eos stop.
                    padding=special_tokens.padding,
                    seqlen=seqlen,
                )
                all_xy.append((x, y))
            elif strategy == "user_behavior":
                input_tokens, user_behavior_token = build_user_behavior_reward_tokens(
                    sample, tokenizer, seqlen=seqlen
                )
                x, y = build_xy_tensor(
                    input_tokens,
                    user_behavior_token,
                    eos=None,  # no need to learn this eos stop.
                    padding=special_tokens.padding,
                    seqlen=seqlen,
                )
                all_xy.append((x, y))
            else:
                raise ValueError(f"Unknown strategy: {strategy}")
        if strategy == "absolute-random" and training:
            all_xy = random.sample(all_xy, len(samples))
        all_x = [x for x, _ in all_xy]
        all_y = [y for _, y in all_xy]
        return torch.stack(all_x), torch.stack(all_y), 0

    return collate_fn


def _initialize_metrics_tracking(
    det_context: det.core.Context | None = None,  # type: ignore
    name: str = args.run_name,
    group: str | None = args.wandb_group,
    reinit: bool = False,
):
    wandb_config = None
    if args.wandb_project:
        wandb_config = logutils.WandbConfig(
            project=args.wandb_project,
            name=name,
            group=group or None,
        )
    logutils.initialize_metrics_tracking(
        wandb_config, det_context, args.full_config(), reinit
    )


def log_metrics(
    iter_num: int,
    metrics: training.IterationMetrics,
    iter_tracking_metrics: TrackingMetrics,
    tracking_metrics: TrackingMetrics,
) -> None:
    """Log training metrics."""
    assert distributed.is_main_process()

    remaining_time_s = metrics.iteration_time_average_s * (
        args.max_iters - iter_num - 1
    )
    logger.info(
        "iter %5d / %5d: loss %0.4f, lr %0.2e, grad_norm %0.2f, time %0.2fms, mfu %0.2f%%, needs %s to"
        " finish.",
        iter_num,
        args.max_iters,
        metrics.loss,
        metrics.learning_rate,
        metrics.grad_norm or -1.0,
        metrics.iteration_time_s * 1000,
        metrics.mfu_estimate_percent,
        logutils.seconds2str(remaining_time_s),
    )
    masked_token_fraction = (
        (tracking_metrics.total_masked_tokens / tracking_metrics.total_tokens)
        if tracking_metrics.total_tokens > 0
        else 0.0
    )

    logutils.report_metrics(
        {
            "train/lm_loss": metrics.loss,
            "train/learning_rate": metrics.learning_rate,
            "train/grad_norm": metrics.grad_norm or -1.0,
            "runtime/iteration_time": metrics.iteration_time_s,
            "runtime/mfu": metrics.mfu_estimate_percent,
            "tracking/tokens_seen": iter_tracking_metrics.total_tokens,
            "tracking/masked_tokens_seen": iter_tracking_metrics.total_masked_tokens,
            "tracking/actual_tokens_seen": iter_tracking_metrics.total_unmasked_tokens,
            "tracking/total_tokens_seen": tracking_metrics.total_tokens,
            "tracking/total_masked_tokens": tracking_metrics.total_masked_tokens,
            "tracking/total_actual_tokens_seen": tracking_metrics.total_unmasked_tokens,
            "tracking/total_samples_seen": metrics.total_samples_seen,
            "tracking/masked_token_fraction": masked_token_fraction,
            "tracking/remaining_time_s": remaining_time_s,
        },
        iter_num,
    )


def post_slack_update(message: str):
    cluster_info = det.get_cluster_info()  # type: ignore
    if not args.slack_user or cluster_info is None:
        return

    token = Path(args.slack_token_path).read_text()

    experiment_id = str(cluster_info.trial.experiment_id)
    experiment_url = f"{args.determined_master_url}/det/experiments/{experiment_id}"

    auth_headers = {"Authorization": f"Bearer {token}"}
    slack_url = "https://slack.com/api/chat.postMessage"
    data = {"channel": args.slack_user, "unfurl_links": False}

    blocks = [
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"<{experiment_url}|Experiment #{experiment_id}> for user <@{args.slack_user}>",
            },
        },
        {"type": "section", "text": {"type": "mrkdwn", "text": f"{message}"}},
    ]
    data["blocks"] = json.dumps(blocks)
    r = requests.post(slack_url, data=data, headers=auth_headers, timeout=120)
    if not r.ok:
        logger.warning(f"Slack notification returned {r}")
        logger.warning(r.text)


def trial_entry_hook():
    if args.trial_entry_notify and distributed.is_main_process():
        post_slack_update(
            f"Trial Entry with {args.max_iters} total steps",
        )


def trial_exit_hook():
    """We check for trial_exit_notify and is_main_process when we register this.

    When this runs, distributed.is_main_process() won't be available.
    """
    post_slack_update(
        f"Trial Exit at iteration {_latest_iteration} with {args.max_iters} total steps",
    )


def post_iter_hook(
    iter_num: int,
    model: Transformer,
    optimizer: MixedPrecisionAdamW,
    tracking_metrics: TrackingMetrics,
    start_iteration: int,
    eval_loaders_by_name: dict[str, DataLoader],
    llm_eval_loaders_by_name: dict[str, DataLoader],
    loss_fn,
    checkpoint_manager: checkpointing.CheckpointManager,
    out_dir,
    model_args,
    extra_files,
    *,
    # Pyright cannot find Determined's `det.core.Context`.
    det_context: det.core.Context | None,  # type: ignore
):
    """Hook run after each train iteration. Typically for evaluation and checkpointing.

    Args:
        iter_num: The current iteration number.
        model: The model being trained.
        optimizer: The optimizer used to train the model.
        det_context: The determined context, if we are running on Determined.
        harness_evaluation_queue: The queue to which we should send evaluation requests.
    """
    is_last_iter = iter_num == args.max_iters

    preempted = det_context.preempt.should_preempt() if det_context else False
    if preempted:
        logger.info("Received Determined preemption -- performing final checkpointing.")

    # 1. Run evaluation if:
    should_run_eval = (
        # It's the starting step.
        iter_num == start_iteration
        # It's at an eval interval.
        or (args.eval_interval > 0 and (iter_num % args.eval_interval == 0))
        # It's at the last iteration.
        or is_last_iter
        # We are being pre-empted.
        or preempted
    )
    if not should_run_eval:
        return False

    # PyTorch evaluations
    total_eval_iters = sum(len(loader) for loader in eval_loaders_by_name.values())
    if distributed.is_main_process():
        logger.info(
            "Starting evaluation at iter %d of %d total batches",
            iter_num,
            total_eval_iters,
        )

    eval_metrics: dict[str, metrics.MetricFn] = {
        "lm_loss": metrics.get_cross_entropy_mean(),
        "exact_match": metrics.get_exact_match_mean(),
        "token_accuracy": metrics.get_token_accuracy_mean(),
    }
    eval_loss_by_name = evaluation.run_all_evaluation(
        model,
        eval_metrics,
        eval_loaders_by_name,
        None,
        eval_log_interval=args.eval_log_interval,
    )

    if distributed.is_main_process():
        for name, loss in eval_loss_by_name.items():
            logger.info(f"{iter_num:5d}/{args.max_iters:5d}: {name}={loss:0.5f}")
        to_report = dict(eval_loss_by_name)
        logutils.report_metrics(to_report, iter_num)

        if args.post_iter_notify:
            post_slack_update(
                f"Trial validation at iteration {iter_num} with {args.max_iters} total steps: {to_report}",
            )

    # LLM evaluations
    model.eval()
    rlhf_utils.evaluate_report_all_loaders(
        model,
        llm_eval_loaders_by_name,
        iter_num,
        log_interval=args.eval_log_interval,
        log_prefix="llm_eval",
        logger=logger,
    )
    model.train()

    # Checkpointing
    checkpoint_location = checkpointing.get_checkpoint_location(iter_num, out_dir)
    if iter_num > start_iteration:
        # 2. Save a new checkpoint if this is the best checkpoint so far.
        checkpoint_location = checkpoint_manager.checkpoint(
            checkpoint_location,
            model_state_dict=model.state_dict(),
            model_config=ModelArgs.schema().dump(correct_model_args(model_args)),
            iter_num=iter_num,
            optimizer_state_dict=(
                optimizer.parallel_state_dict()
                if args.checkpoint_optimizer_state
                else None
            ),
            extra_files=extra_files,
            metadata={
                "val_losses": eval_loss_by_name,
                "iter_num": iter_num,
                "model_args": ModelArgs.schema().dump(correct_model_args(model_args)),
                "config": args.full_config(),
                "tracking_metrics": tracking_metrics.to_dict(),
            },
        )
        logger.info(f"Checkpoint saved to {checkpoint_location}")
    else:
        logger.info("Skip init-eval checkpointing")
    return preempted


# TODO(arun): This should really live in training.py, and we should pass in a
# configuration here.
def create_lr_schedule(args) -> training.LRSchedule:
    """Get learning rate given args."""

    if args.decay_lr:
        # If `lr_decay_iters` is 0 (default), decay to the target iteration count
        decay_iters = args.lr_decay_iters or args.max_iters
        return partial(
            training.get_cosine_lr,
            warmup_iters=args.warmup_iters,
            learning_rate=args.learning_rate,
            decay_iters=decay_iters,
            min_lr=args.min_lr,
        )
    else:
        return lambda _: args.learning_rate  # noqa


@dataclasses.dataclass
class TrackingMetrics(DataClassJsonMixin):
    """For persisting tracking metrics such as total number of masked tokens
    seen in continued runs.

    This is especially needed when we change the dataset composition
    or batch structure during the run.
    """

    total_masked_tokens: int = 0
    """The total number of masked tokens seen so far."""

    total_tokens: int = 0
    """The total number of tokens seen, masked or unmasked."""

    @property
    def total_unmasked_tokens(self):
        """The total number of unmasked tokens seen."""
        return self.total_tokens - self.total_masked_tokens

    def update(self, other: TrackingMetrics):
        self.total_masked_tokens += other.total_masked_tokens
        self.total_tokens += other.total_tokens
        return self


def compute_train_metrics(
    inputs_and_targets: Sequence[training.InputTargetPair],
) -> TrackingMetrics:
    """Compute (additional) train time metrics."""
    tracking_metrics = torch.zeros(2, device="cuda")
    for _, Y in inputs_and_targets:
        tracking_metrics += torch.tensor([Y.numel(), (Y < 0).sum()], device="cuda")
    dist.all_reduce(
        tracking_metrics, op=dist.ReduceOp.SUM, group=mpu.get_data_parallel_group()
    )
    total_tokens, total_masked_tokens = tracking_metrics.cpu().numpy().tolist()
    return TrackingMetrics(
        total_tokens=int(total_tokens),
        total_masked_tokens=int(total_masked_tokens),
    )


def main(args):
    # We have to do the file sync before we initialize torch.distributed to avoid
    # NCCL timeouts on large syncs.
    if args.determined_enabled:
        rank = int(os.environ.get("RANK", -1))
        logging.debug(f"Found rank {rank}")
        sync_items = [
            args.checkpoint_dir,
            args.checkpoint,
            args.hf_checkpoint_dir,
            args.train_data_path,
            args.eval_data_path,
            args.rl_llm_eval_data_path,
        ]
        if args.additional_sync_items:
            if isinstance(args.additional_sync_items, str):
                args.additional_sync_items = args.additional_sync_items.split(",")
            logging.info(f"Syncing additional items: {args.additional_sync_items}")
            sync_items.extend(args.additional_sync_items)
        # sync_all_to_local() syncs the different hosts so we call it on every rank.
        sync_all_to_local(sync_items, rank)

    # Init distributed and set relevant variables
    logging.info("Initializing distributed...")
    distributed.init_distributed_for_training(
        args.model_parallel_size, distributed_backend="nccl"
    )
    if args.determined_enabled:
        rank = dist.get_rank()
        dist.barrier()
        if rank == 0:
            remove_sync_flag_file()
    logging.info("Done initializing distributed.")
    trial_entry_hook()
    if args.trial_exit_notify and distributed.is_main_process():
        atexit.register(trial_exit_hook)
    data_parallel_world_size = mpu.get_data_parallel_world_size()
    logging.info(f"The data parallel world size: {data_parallel_world_size}")
    data_parallel_rank = mpu.get_data_parallel_rank()
    logging.info(f"The data parallel rank: {data_parallel_rank}")

    # TODO(Xuanyi): use an elegant way to create these config.
    model_args = ModelArgs(
        dim=args.n_embd,
        n_layers=args.n_layers,
        n_heads=args.n_heads,
        n_kv_heads=args.n_kv_heads,
        multiple_of=args.ffn_dim_multiple_of,
        ffn_dim_multiplier=args.ffn_dim_multiplier,
        norm_eps=args.norm_eps,
        max_seq_len=args.block_size,
        vocab_size=args.model_vocab_size,
        rope_theta=args.rope_theta,
        rope_scaling_factor=args.rope_scaling_factor,
        ffn_type=args.ffn_type,
        bias=args.bias,
        norm_type=args.norm_type,
        pos_embed_type=args.pos_embed_type,
        rotary_config=args.rotary_config,
        attn_config=args.attn_config,
        ffn_config=args.ffn_config,
        first_layer_ffn_config=args.first_layer_ffn_config,
        use_activation_checkpointing=args.use_activation_checkpointing,
        use_sequence_parallel=args.use_sequence_parallel,
    )
    model_args = correct_model_args(model_args)
    assert model_args.attn_config is not None, "attn_config must be set before here"
    model_args.attn_config.dropout_attn = args.sft_dropout_attn
    logger.info(f"The model config: {model_args}")

    # Let's be careful about the initialize_model_parallel_subgroup.
    attn_config = model_args.attn_config
    if (
        isinstance(attn_config, GenericAttnSpec)
        and attn_config.n_kv_heads < mpu.get_model_parallel_world_size()
    ):
        assert mpu.get_model_parallel_world_size() % attn_config.n_kv_heads == 0, (
            f"n_kv_heads={attn_config.n_kv_heads} must be a multiple of"
            f" model_parallel_size={mpu.get_model_parallel_world_size()}"
        )
        mpu.initialize_model_parallel_subgroup(
            "kv", mpu.get_model_parallel_world_size() // attn_config.n_kv_heads
        )

    # NOTE(global-rng): we do NOT want to depend on the global pytorch RNG state as much
    # as possible. But dropout does depend on it, so to future-proof against dropout
    # support, we set the global RNG seed based on your model parallel group. That way,
    # all GPUs in the same group generate the same dropout mask (as they have the same
    # data items), while GPUs across groups have different masks.
    seed_offset = data_parallel_rank

    args.add_arg("data_parallel_world_size", data_parallel_world_size)
    tokens_per_iter = (
        args.gradient_accumulation_steps
        * data_parallel_world_size
        * args.batch_size
        * args.block_size
    )
    logging.info(f"Tokens per iteration: {tokens_per_iter}")

    if args.determined_enabled:
        # If this is a determined run, we'll create a determined context with
        # `det.core.init`
        det_context_factory = partial(
            det.core.init,  # type: ignore
            distributed=(
                det.core.DistributedContext.from_torch_distributed()  # type: ignore
            ),
            tensorboard_mode=det.core.TensorboardMode.MANUAL,  # type: ignore
        )
    else:
        # Otherwise, the determined context will be `None`
        det_context_factory = contextlib.nullcontext
    logging.info(f"Create determined context: {det_context_factory}")

    # Configurator should convert comma-separated string to tuple of ints automatically
    # Just in case it doesn't well double the work here...
    if isinstance(args.ignore_list, str):
        args.ignore_list = tuple(int(x) for x in args.ignore_list.split(","))

    with det_context_factory() as det_context:
        logging.info(f"Determined context: {det_context}")
        # We need to see if this run is actually a restart and fetch the source uuid
        restart_checkpoint = checkpointing.get_determined_restart_checkpoint(
            det_context
        )
        if restart_checkpoint is not None:
            args.checkpoint = restart_checkpoint.uuid
            # Always restore training metadata
            args.restore_training_metadata_from_checkpoint = True
            # Restore optimizer state only if the checkpoint has it
            args.restore_optimizer_state_from_checkpoint = (
                checkpointing.determined_checkpoint_has_optimizer_state(
                    restart_checkpoint
                )
            )
            logging.info(f"Restarting from checkpoint {args.checkpoint}")
            if not args.restore_optimizer_state_from_checkpoint:
                logging.warning(
                    f"Checkpoint at {args.checkpoint} does not have optimizer state."
                )
        # Setup output directory and logger
        out_dir = None
        if args.out_dir:
            out_dir = Path(args.out_dir) / args.run_name
            logutils.setup_logging(out_dir / "fastbackward", append_timestamp=True)
        else:
            out_dir = None
            logutils.setup_logging(None, append_timestamp=True)

        logger.info(
            f"The output location: {out_dir or checkpointing.DETERMINED_LOCATION}"
        )
        logger.info(f"Batch size per device: {args.batch_size}")
        logger.info(f"The sequence length: {args.block_size}")
        logger.info(f"The data parallel world size: {data_parallel_world_size}")
        logger.info(
            f"Steps to accumulate gradients: {args.gradient_accumulation_steps}"
        )
        logger.info(f"Thus, tokens per iteration will be: {tokens_per_iter}")
        logger.info(f"The original eval_interval: {args.eval_interval}")
        logger.info(f"The original eval_items: {args.eval_items}")
        checkpoint_location = checkpointing.get_checkpoint_location(None, out_dir)
        logger.info(f"The checkpoint will be saved into {checkpoint_location}")

        # See NOTE(global-rng)
        torch.manual_seed(1337 + seed_offset)
        torch.cuda.manual_seed(1337 + seed_offset)
        torch.backends.cuda.matmul.allow_tf32 = True  # allow tf32 on matmul
        torch.backends.cudnn.allow_tf32 = True  # allow tf32 on cudnn
        assert args.dtype in ["float32", "bfloat16"], f"{args.dtype} unsupported"
        ptdtype = {"float32": torch.float32, "bfloat16": torch.bfloat16}[args.dtype]

        tokenizer = get_tokenizer(args.tokenizer_name)
        # Build train dataset
        assert args.train_data_path, "Must provide a train_data_path"
        train_datasets = []
        for idx_path, path in enumerate(args.train_data_path.split(";")):
            assert Path(
                path
            ).exists(), f"Requested training dataset at {path} does not exist."
            cur_dataset = HFDataset.load_from_disk(path)
            train_datasets.append(cur_dataset)
            logger.info(f"{idx_path=}: {len(cur_dataset)=}")
        if len(train_datasets) == 1:
            train_dataset = train_datasets[0]
        else:
            train_dataset = concatenate_datasets(train_datasets)
        logger.info(f"The training data path: {args.train_data_path}")
        logger.info(f"Total training dataset size: {len(train_dataset)}")

        if args.max_iters == 0:
            assert args.max_epochs > 0, "If max_iters==0, max_epochs should be set"
            global_batch_size: int = (
                args.batch_size
                * args.gradient_accumulation_steps
                * data_parallel_world_size
            )
            args.max_iters = args.max_epochs * len(train_dataset) // global_batch_size
            logger.info(
                f"Running {args.max_epochs} epoch(s), "
                f"each epoch has {len(train_dataset)} items, "
                f"with a global batch size of {global_batch_size} we get max_iters={args.max_iters}"
            )
        else:
            assert args.max_epochs == 0, "If max_iters>0, max_epochs should be 0"

        # Build eval dataset
        assert args.eval_data_path, "Must provide a eval_data_path"
        sft_eval_dataset = {}
        for idx, eval_data_path in enumerate(args.eval_data_path.split(";")):
            if "@" in eval_data_path:
                key, eval_data_path = eval_data_path.split("@")
            else:
                key = f"eval_{idx}"
            sft_eval_dataset[key] = HFDataset.load_from_disk(eval_data_path)
        sft_eval_loaders_by_name = {}
        for eval_name, eval_dataset in sft_eval_dataset.items():
            eval_loader = DataLoader(
                eval_dataset,  # type: ignore
                batch_size=args.batch_size,
                sampler=DistributedSampler(
                    eval_dataset,  # type: ignore
                    num_replicas=mpu.get_data_parallel_world_size(),
                    rank=mpu.get_data_parallel_rank(),
                    shuffle=False,
                    drop_last=True,
                ),
                collate_fn=get_sft_collate_fn(
                    tokenizer, args.block_size, args.sft_collate_strategy, False
                ),
                pin_memory=True,
                drop_last=True,
            )
            sft_eval_loaders_by_name[eval_name] = eval_loader
            if distributed.is_main_process():
                items_per_eval_step = args.batch_size * data_parallel_world_size
                logger.info(f"The SFT [{eval_name}] dataset size: {len(eval_dataset)}")
                logger.info(
                    f"The SFT [{eval_name}] eval dataset will be evaluated"
                    f" for {len(eval_loader)} iterations of {items_per_eval_step} items"
                    f" each."
                )
        # Still keep track the original LLM evaluation
        # assert (
        #     args.rl_llm_eval_data_path
        # ), "Must provide a rl_llm_eval_data_path"
        llm_eval_loaders_by_name = rlhf_utils.build_llm_eval_loaders_by_name(
            args.rl_llm_eval_data_path,
            tokenizer,
            args.batch_size,
            args.block_size,
            logger,
        )

        # Set up the start iteration
        start_iteration = 0

        # We skip the init here to speed up the model instantiation.
        model = Transformer(model_args, skip_init=True)
        model.train()

        # Load a checkpoint if provided
        checkpoint_location = args.checkpoint or args.checkpoint_dir
        checkpoint_manager = checkpointing.CheckpointManager(det_context)
        if checkpoint_location:
            logger.info(f"[INIT] Loading FB checkpoint from {checkpoint_location}")
            state_dict = checkpoint_manager.load_state_dict_checkpoint(
                checkpoint_location,
                model_vocab_size=args.model_vocab_size,
            )
            model.load_state_dict(state_dict)
            tokenizer_model_path = Path(args.checkpoint_dir) / "tokenizer.model"
            del state_dict
        elif args.hf_checkpoint_dir:
            logger.info(f"[INIT] Loading HF checkpoint from {args.hf_checkpoint_dir}")
            state_dict = checkpointing.safe_parallel_load(
                lambda: huggingface_checkpointing.load_huggingface_checkpoint(
                    checkpoint_location=args.hf_checkpoint_dir,
                    model_vocab_size=args.model_vocab_size,
                    pos_embeddings_len=args.block_size,
                )
            )
            model.load_state_dict(state_dict)
            tokenizer_model_path = Path(args.hf_checkpoint_dir) / "tokenizer.model"
            del state_dict
        else:
            logger.info("[INIT] Random initialization from scratch.")
            model.init_weights()
            tokenizer_model_path = None

        # TODO: this logic around the tokenizer model file is horrible. We need to
        # clarify what is needed and where it should be stored.
        if tokenizer_model_path and tokenizer_model_path.exists():
            extra_files = {"tokenizer.model": tokenizer_model_path}
        else:
            if tokenizer_model_path:
                logger.warning(f"{tokenizer_model_path=} does not exist.")
            extra_files = {}

        # Make sure the model is in the right place and has the right dtype
        model.to(distributed.get_local_device(), ptdtype)

        # optimizer and extra state for FSDP (distributed optimizer)
        optimizer, flat_model_state = configure_fsdp_optimizer(
            model,
            weight_decay=args.weight_decay,
            learning_rate=args.learning_rate,
            betas=(args.beta1, args.beta2),
        )
        # Logging key information before training.
        if distributed.is_main_process():
            total_params = 0
            for name, params in model.named_parameters():
                total_params += params.numel()
                logger.info(f"{name}: {params.shape} {params.dtype} {params.device}")
            logger.info(
                f"Model on rank {mpu.get_model_parallel_rank()} has {total_params:,} parameters."
            )
            for idx, opt_group in enumerate(optimizer.param_groups):
                params_group = sum(p.numel() for p in opt_group["params"])
                logger.info(
                    f"The {idx}-th optimizer group has {params_group:,} parameters."
                )

        tracking_metrics = TrackingMetrics()
        # Load optimizer state from a checkpoint if provided
        if (
            args.restore_training_metadata_from_checkpoint
            or args.restore_optimizer_state_from_checkpoint
        ):
            assert (
                checkpoint_location
            ), "Must load from a llama checkpoint to restore training state"
            logger.info(f"[INIT] Loading training state from {checkpoint_location}")
        if args.restore_training_metadata_from_checkpoint:
            checkpoint_state = checkpoint_manager.load_checkpoint_saved_info(
                checkpoint_location
            )
            start_iteration = checkpoint_state["iter_num"]
            logger.info(f"[INIT] Setting start iteration to {start_iteration}")
            if "tracking_metrics" in checkpoint_state:
                logger.info(
                    f"Restoring tracking metrics from checkpoint: {checkpoint_state['tracking_metrics']}"
                )
                tracking_metrics = TrackingMetrics.from_dict(
                    checkpoint_state["tracking_metrics"]
                )
        if args.restore_optimizer_state_from_checkpoint:
            if distributed.is_main_process():
                logger.info("Restoring optimizer state")
            optimizer_state = checkpoint_manager.load_parallel_optimizer_state(
                checkpoint_location
            )
            optimizer.load_parallel_state_dict(optimizer_state)
            del optimizer_state
        else:
            if distributed.is_main_process():
                logger.info(
                    "Not restoring optimizer state.  Only loading checkpoint state."
                )

        # Set up the training data loader
        train_sampler = DistributedStepCountSampler(
            train_dataset,  # type: ignore
            # NOTE(arun): We now draw samples from the dataset in micro-batches, which
            # means we have to account for the number gradient accumulation steps in
            # each iteration.
            batch_size=args.batch_size,
            step_count=args.max_iters * args.gradient_accumulation_steps,
            start_step=start_iteration * args.gradient_accumulation_steps,
            dp_rank=mpu.get_data_parallel_rank(),
            dp_world_size=mpu.get_data_parallel_world_size(),
            seed=args.data_sampler_rand_seed,
            cross_shuffle=args.cross_shuffle,
        )
        if distributed.is_main_process():
            logger.info(f"Total steps per epoch: {train_sampler.steps_per_epoch}")
        train_loader = DataLoader(
            train_dataset,  # type: ignore
            batch_sampler=train_sampler,
            num_workers=4,  # TODO: try different values
            collate_fn=get_sft_collate_fn(
                tokenizer, args.block_size, args.sft_collate_strategy
            ),
            pin_memory=True,
        )

        # Set up metrics reporting via wandb
        if distributed.is_main_process():
            _initialize_metrics_tracking(det_context)

        # TODO(arun): Allow this to be configured.
        loss_fn = losses.cross_entropy_loss
        lr_schedule = create_lr_schedule(args)

        # Broadcast params across the data parallel group for the same model parallel
        # rank. This is needed (especially) during random initialization to ensure that
        # all model parallel ranks have the same random weights.
        dist.broadcast(
            flat_model_state.params(),
            # Get the 0-th
            dist.get_global_rank(mpu.get_data_parallel_group(), 0),
            group=mpu.get_data_parallel_group(),
        )

        train_options = training.TrainOptions(
            batch_size=args.batch_size,
            block_size=args.block_size,
            gradient_accumulation_steps=args.gradient_accumulation_steps,
            start_iteration=start_iteration,
            grad_clip=args.grad_clip,
            log_interval=args.log_interval,
            max_iters=args.max_iters,
        )
        logger.info(f"Training options: {train_options}")
        # Pre-training loop evaluation.
        post_iter_hook(
            start_iteration,
            model,
            optimizer,
            tracking_metrics,
            det_context=det_context,
            start_iteration=start_iteration,
            eval_loaders_by_name=sft_eval_loaders_by_name,
            llm_eval_loaders_by_name=llm_eval_loaders_by_name,
            loss_fn=loss_fn,
            checkpoint_manager=checkpoint_manager,
            out_dir=out_dir,
            model_args=model_args,
            extra_files=extra_files,
        )

        # training loop
        for iter_num, iter_metrics, inputs_and_targets in training.train_loop(
            model,
            loss_fn,
            flat_model_state=flat_model_state,
            optimizer=optimizer,
            lr_schedule=lr_schedule,
            train_options=train_options,
            train_loader=train_loader,
        ):
            _latest_iteration = iter_num
            iter_tracking_metrics = compute_train_metrics(inputs_and_targets)
            tracking_metrics.update(iter_tracking_metrics)

            if (
                iter_metrics.iter_num % train_options.log_interval == 0
                or iter_metrics.iter_num < 10
            ) and distributed.is_main_process():
                log_metrics(
                    iter_metrics.iter_num,
                    iter_metrics,
                    iter_tracking_metrics,
                    tracking_metrics,
                )

            should_stop = post_iter_hook(
                iter_num,
                model,
                optimizer,
                tracking_metrics,
                start_iteration=start_iteration,
                eval_loaders_by_name=sft_eval_loaders_by_name,
                llm_eval_loaders_by_name=llm_eval_loaders_by_name,
                loss_fn=loss_fn,
                checkpoint_manager=checkpoint_manager,
                out_dir=out_dir,
                model_args=model_args,
                extra_files=extra_files,
                det_context=det_context,
            )
            if should_stop:
                logger.info("Asked to stop -- exiting early.")
                break

        if distributed.is_main_process():
            logutils.shutdown_metrics_tracking()
        # On successful completion, clean up the last optimizer state checkpoint
        if checkpoint_manager.last_optimizer_location is not None:
            logger.info("Deleting optimizer state from final checkpoint.")
            checkpoint_manager.delete_optimizer_state(
                checkpoint_manager.last_optimizer_location
            )
        dist.destroy_process_group()
        logger.info("Finished the training!")


if __name__ == "__main__":
    if args.llama is not None or args.starcoder is not None:
        raise ValueError(
            "Fastbackward no longer supports `--llama` and `--starcoder`. You should"
            " prepend `configs/llama2.py` or `configs/starcoder.py` to your list of"
            " config files or set meta-architecture arguments (e.g. `ffn_type`,"
            " `norm_type`, `pos_embed_type`) directly in the config file or on the"
            " command line."
        )
    if not args.run_name:
        raise ValueError("You must specify a --run_name.")
    if not args.determined_enabled and not args.out_dir:
        raise ValueError("If you are running locally, you must specify an --out_dir.")
    if args.determined_enabled:
        if args.out_dir:
            logging.warning("--out_dir is ignored when running on Determined.")
            args.out_dir = None
    if args.checkpoint and args.checkpoint_dir:
        raise ValueError(
            "If you use the new --checkpoint argument, you must leave the deprecated"
            " --checkpoint_dir argument empty"
        )
    if not args.wandb_log:
        logging.warning(
            "--wandb_log is deprecated and no longer has any effect. Set"
            " --wandb_project to None to disable wandb logging."
        )
    main(args)

"""Implementation of retrieval models in fastbackward.

For now, all implementations are small and simple enough to keep in a single file.
When this is no longer true, we should replace this file with a module.
"""

import json
import logging
import typing
from collections.abc import Mapping
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Literal, Optional, Union

import torch
import torch.nn.functional as F
import torch.utils.checkpoint
from dataclasses_json import DataClassJsonMixin
from tensordict.tensordict import TensorDict
from torch import nn
from torchmetrics.functional.retrieval import (
    retrieval_average_precision,
    retrieval_reciprocal_rank,
)
from typing_extensions import Self

import research.fastbackward.fs_model_parallel as mpu
from base.component_registry.component_registry import (
    COMPONENT_NAME_KEY,
    REFERENCE_PREFIX,
)
from research.core.component_registry import (
    ComponentMap,
    Serializable,
    create_components,
    serialize_component_configs,
)
from research.fastbackward.model import fix_model_arg_params
from research.fastbackward.utils import flatten_dict, unflatten_dict

logger = logging.getLogger(__name__)


class TransformerEmbedder(torch.nn.Module):
    """Transformer model with a single token embedding."""

    @dataclass
    class Config(DataClassJsonMixin):
        input_dim: int
        """Dimension of the input embedding."""
        output_projection_dim: int
        """The dimension of the final output layer."""
        with_output_bias: bool
        """If true, include a bias in the final output layer."""

    def __init__(self, language_model: torch.nn.Module, config: Config):
        """Create an embedding model.

        Args:
            language_model: the underlying language model to use to extract embeddings.
            config: Configuration for this transformer embedding layer.
        """
        super().__init__()
        self.config = config
        self.lm = language_model
        self.output_projection = nn.Linear(
            config.input_dim,
            config.output_projection_dim,
            bias=config.with_output_bias,
        )

    def create_config(self) -> dict:
        return {
            "language_model": self.lm,
            "config": self.config,
        }

    def forward(self, X: TensorDict):
        """Compute the embeddings of the batch.

        Args:
            X: A tensor dict with the following keys:
                tokens_BL: The input tokens.
                emb_tokens_B: The embedding token ids for each element of the batch.
                    We will use this token id to extract embeddings from the logits.

        Dimensions:
            B: batch size
            L: sequence length
            D: language model embedding dimension
            P: projected embedding dimension

        Returns:
            Embedding for each k
        """
        tokens_BL = X["tokens_BL"]
        emb_tokens_B = X["emb_tokens_B"].reshape(-1, 1)
        indices_BL = tokens_BL == emb_tokens_B

        emb_token_present_B = indices_BL.sum(dim=-1)
        assert torch.equal(emb_token_present_B, torch.ones_like(emb_token_present_B)), (
            "Expected exactly 1 embedding token per batch element, but found ",
            emb_token_present_B,
        )

        logits_BLD = self.lm(tokens_BL)
        # Subselect the tokens for the embeddings. The reshape ensures that at most one
        # token will be selected per batch element.
        logits_BD = logits_BLD[indices_BL]
        assert logits_BD.shape[:1] == X.batch_size, logits_BD.shape
        emb_BP = self.output_projection(logits_BD)
        return emb_BP


class DualEncoderModel(nn.Module):
    """A dual encoder model."""

    @dataclass
    class Config(DataClassJsonMixin):
        query_token_id: int
        """The token ID for the query embedding."""
        document_token_id: int
        """The token ID for the document embedding."""
        freeze_document_model: bool = False
        """If True, freeze the document model."""

    def __init__(self, query_model: nn.Module, doc_model: nn.Module, config: Config):
        super().__init__()
        self.config = config
        self.query_model = query_model
        self.doc_model = doc_model

        if config.freeze_document_model:
            assert (
                self.query_model != self.doc_model
            ), "Cannot freeze the document model if it is the same as the query model."
            self.doc_model.requires_grad_(False)
        self.register_load_state_dict_post_hook(self._load_state_dict_post_hook)

    def create_config(self) -> dict:
        # NOTE(arun): `create_config` is used to save the model configuration in a
        # consistent way so that we can recursively save the configurations of the more
        # complex retrieval models that e.g. have separate query and document encoders.
        # It must return a dictionary of arguments to the constructor.
        return {
            "query_model": self.query_model,
            "doc_model": self.doc_model,
            "config": self.config,
        }

    def _load_state_dict_post_hook(self, module, incompatible_keys):
        """Hook to ensure that the doc model is equal to the query model when loading."""
        # NOTE(arun): On saving checkpoints when the query model == document model.
        # Torch save state dicts using pickle, which handles duplicate object
        # references. We previously had a hack to deleted the document model keys
        # from the state dict, but this is not necessary and problematic when there are
        # e.g., multiple DualEncoderModels in the checkpoint or other complicated
        # situations.
        # This code only exists here for backward compatibility, and will be removed
        # in the future.
        missing_doc_keys = {
            # As a heuristic, we identify DualEncoderModels with the key "doc_model".
            # This heuristic only runs on _missing keys_, so new checkpoints will not
            # trigger this logic.
            k
            for k in incompatible_keys.missing_keys
            if "doc_model" in k.split(".")
        }
        if missing_doc_keys:
            logger.warning(
                "Heuristically setting doc model to be equal to the query model. "
                "Please fix your checkpoints by 07/01/2024 using: "
                "experimental/arun/fixup_dual_encoder_checkpoints_20240616.py"
            )
            self.doc_model = self.query_model
            # Mark the doc keys as not missing any more.
            for key in missing_doc_keys:
                incompatible_keys.missing_keys.remove(key)

    def forward(self, X: TensorDict):
        """Computes the dual encoder embeddings.

        Args:
            X: A TensorDict containing the following entries:
                query_tokens_BLq: A token sequence, with shape [batch, length].
                doc_tokens_BKLd: A token sequence, with shape [batch, docs, length].

        Dimensions:
            B: Batch size.
            K: Number of documents per batch element.
            Lq: Sequence length for the queries.
            Ld: Sequence length for the documents.
            D: Embedding dimension.

        Query and document lengths do not need to match (typically query lengths are
        longer than document lengths).

        Returns:
            A tensor of shape [batch, dim] containing the corresponding query or doc
            embeddings.
        """
        query_tokens_BLq: torch.Tensor = X["query_tokens_BLq"]
        doc_tokens_BKLd: torch.Tensor = X["doc_tokens_BKLd"]

        B = query_tokens_BLq.shape[0]
        K = doc_tokens_BKLd.shape[1]
        Lq, Ld = query_tokens_BLq.shape[1], doc_tokens_BKLd.shape[2]

        if self.query_model == self.doc_model and Lq == Ld:
            # Stack the query and document tokens together for ~efficiency~.
            # Bj is joint batch = B + B*K.
            # L is the same as Lq or Ld.
            Bj, L = B + B * K, Ld
            tokens_BjL = torch.cat(
                [query_tokens_BLq, doc_tokens_BKLd.reshape(B * K, L)]
            ).reshape(Bj, L)
            emb_tokens_Bj = (
                torch.cat(
                    [
                        torch.ones(B) * self.config.query_token_id,
                        torch.ones(B * K) * self.config.document_token_id,
                    ],
                )
                .reshape(Bj)
                .to(tokens_BjL)
            )
            emb_BjD = self.query_model(
                TensorDict(
                    {
                        "tokens_BL": tokens_BjL,
                        "emb_tokens_B": emb_tokens_Bj,
                    },
                    batch_size=[B + B * K],
                )
            )
            query_emb_BD = emb_BjD[:B, :]
            doc_emb_BKD = emb_BjD[B:, :].reshape(B, K, -1)
        else:
            query_emb_BD = self.query_model(
                TensorDict(
                    {
                        "tokens_BL": query_tokens_BLq,
                        "emb_tokens_B": (torch.ones(B) * self.config.query_token_id).to(
                            query_tokens_BLq
                        ),
                    },
                    batch_size=[B],
                )
            )
            doc_emb_BKD = self.doc_model(
                TensorDict(
                    {
                        "tokens_BL": doc_tokens_BKLd.reshape(B * K, -1),
                        "emb_tokens_B": (
                            torch.ones(B * K) * self.config.document_token_id
                        ).to(doc_tokens_BKLd),
                    },
                    batch_size=[B * K],
                )
            ).reshape(B, K, -1)

        return TensorDict(
            {
                "query_emb_BD": query_emb_BD,
                "doc_emb_BKD": doc_emb_BKD,
            },
            batch_size=[B],
        )


class PerplexityLoss(nn.Module):
    """A perplexity loss with a learned logit scale parameter."""

    @dataclass
    class Config(DataClassJsonMixin):
        gold_temperature: float = 1.0
        """The temperature to use for the gold labels."""
        pred_temperature: float = 1.0
        """The temperature to use for the predicted labels."""
        logits_scale: float = 1.0
        """The scale to use for the logits."""
        learnable_logits_scale: bool = False
        """Whether to learn the logits scale.

        If true, `logits_scale` will be used as the initial value.
        """
        reduction: Literal["mean", "batchmean"] = "batchmean"
        """How to reduce the perplexity loss."""

    def __init__(
        self,
        config: Config,
    ):
        super().__init__()
        self.config = config
        self.gold_temperature = config.gold_temperature
        self.pred_temperature = config.pred_temperature
        self.logits_scale = nn.Parameter(
            torch.tensor(config.logits_scale),
            requires_grad=config.learnable_logits_scale,
        )

    def create_config(self) -> dict:
        return {"config": self.config}

    def extra_repr(self) -> str:
        return (
            f"gold_temperature={self.gold_temperature}, "
            f"pred_temperature={self.pred_temperature}, "
            f"logits_scale={self.logits_scale.data}, "
            f"requires_grad={self.logits_scale.requires_grad}"
        )

    def forward(
        self,
        X: TensorDict,
        labels_BK: torch.Tensor,
        prediction: TensorDict,
    ) -> torch.Tensor:
        """Calculate the perplexity loss, i.e. KL(p_gold || p_pred).

        Args:
        X: A TensorDict containing the following entries:
            query_mask_B: A mask indicating which entries in the batch are queries.
                Only exactly one query per batch is supported for now.
        labels_B: A tensor of shape [batch] containing the gold labels.
        prediction: A tensor dict with the following keys:
            query_emb_BD: A tensor of shape [batch, dim] containing the query embeddings.
            doc_emb_BKD: A tensor of shape [batch, docs, dim] containing the doc embeddings.

        Parallelism support:
            Model parallelism: is trivially supported as the embeddings are already
                identical across model parallel groups.
            Data parallelism: is not yet supported.
                TODO(arun): Here's a plan for data parallelism to support larger batches:
                Broadcast the query from rank 0, and gather the logits (and gold labels).
                We might want to only do this with a subgroup of the data parallel group.

        Returns:
            The perplexity loss averaged over the batch.
        """
        labels_mask_BK = X["labels_mask_BK"]
        query_emb_BD = prediction["query_emb_BD"]
        doc_emb_BKD = prediction["doc_emb_BKD"]

        logits_BK = torch.einsum("bd, bkd -> bk", query_emb_BD, doc_emb_BKD)
        # Mask out logits for masked positions.
        logits_BK = torch.where(
            labels_mask_BK, logits_BK * self.logits_scale, -torch.inf
        )
        labels_BK = torch.where(labels_mask_BK, labels_BK, -torch.inf)

        pred_lprobs_BK = torch.log_softmax(
            logits_BK.float() / self.pred_temperature, dim=-1, dtype=torch.float
        )
        gold_lprobs_BK = torch.log_softmax(
            labels_BK / self.gold_temperature, dim=-1, dtype=torch.float
        )

        loss_BK = F.kl_div(
            pred_lprobs_BK,
            gold_lprobs_BK,
            reduction="none",
            log_target=True,
        )
        # Mean over all non-masked entries.
        if self.config.reduction == "batchmean":
            loss = torch.where(
                labels_mask_BK, loss_BK, 0.0
            ).sum() / labels_mask_BK.size(0)
        else:
            loss = (
                torch.where(labels_mask_BK, loss_BK, 0.0).sum() / labels_mask_BK.sum()
            )

        return loss


def perplexity_loss(
    X: TensorDict,
    labels_BK: torch.Tensor,
    prediction: TensorDict,
    logits_scale: Optional[torch.Tensor] = None,
    gold_temperature: float = 1.0,
    pred_temperature: float = 1.0,
    reduction: Literal["mean", "batchmean"] = "batchmean",
) -> torch.Tensor:
    """See `PerplexityLoss` for details."""
    loss_fn = PerplexityLoss(
        PerplexityLoss.Config(
            gold_temperature=gold_temperature,
            pred_temperature=pred_temperature,
            logits_scale=logits_scale.item() if logits_scale is not None else 1.0,
            learnable_logits_scale=logits_scale is not None,
            reduction=reduction,
        )
    )
    loss_fn.to(X.device)
    return loss_fn.forward(X, labels_BK, prediction)


def _combine_avg(x: float, x_count: int, y: float, y_count: int) -> float:
    """Combine two averages."""
    return x + (y - x) * (y_count / (x_count + y_count)) if x_count + y_count else 0.0


@dataclass
class RankingMetrics(DataClassJsonMixin):
    """Ranking metrics."""

    mean_rr: float = 0.0
    """Mean reciprocal rank."""
    mean_ap: float = 0.0
    """Mean average precision."""
    mean_rank: float = 0.0
    """Mean rank (starting at 1) of all gold documents.

    This rank discounts the number of gold documents, so if all gold documents are in
    the top K documents, this is 1.
    """
    mean_top_rank: float = 0.0
    """Mean top rank (starting at 1)."""
    mean_bottom_rank: float = 0.0
    """Mean bottom rank excluding top ranks."""
    mean_p1: float = 0.0
    """Mean precision@1 (i.e. is the top ranked document correct?)."""
    mean_gold_prob: float = 0.0
    """Mean probability of the top ranked gold document."""
    mean_recall: float = 0.0
    """Mean recall."""
    mean_hard_recall: float = 0.0
    """Mean hard recall."""

    count: int = 1
    """Number of samples we are averaging over."""

    def __str__(self) -> str:
        return " ".join(
            [
                f"rr={self.mean_rr:.3f}",
                f"ap={self.mean_ap:.3f}",
                f"rank=[{self.mean_top_rank:.3f}, {self.mean_rank:.3f}, {self.mean_bottom_rank:.3f}]",
                f"p1={self.mean_p1:.3f}",
                f"gold_prob={self.mean_gold_prob:.3f}",
                f"recall={self.mean_recall:.3f}",
                f"hard_recall={self.mean_hard_recall:.3f}",
                f"count={self.count}",
            ]
        )

    def update(self, metrics: "RankingMetrics"):
        self.mean_rr = _combine_avg(
            self.mean_rr, self.count, metrics.mean_rr, metrics.count
        )
        self.mean_ap = _combine_avg(
            self.mean_ap, self.count, metrics.mean_ap, metrics.count
        )
        self.mean_rank = _combine_avg(
            self.mean_rank, self.count, metrics.mean_rank, metrics.count
        )
        self.mean_top_rank = _combine_avg(
            self.mean_top_rank, self.count, metrics.mean_top_rank, metrics.count
        )
        self.mean_bottom_rank = _combine_avg(
            self.mean_bottom_rank, self.count, metrics.mean_bottom_rank, metrics.count
        )
        self.mean_p1 = _combine_avg(
            self.mean_p1, self.count, metrics.mean_p1, metrics.count
        )
        self.mean_gold_prob = _combine_avg(
            self.mean_gold_prob, self.count, metrics.mean_gold_prob, metrics.count
        )
        # If not set yet (ie at default value of -1), then use the new value.
        # Assumes that metrics has these values set, so we assert this.
        # assert metrics.mean_recall > -0.1 and metrics.mean_hard_recall > -0.1, metrics
        self.mean_recall = (
            _combine_avg(
                self.mean_recall, self.count, metrics.mean_recall, metrics.count
            )
            if self.mean_recall > -0.1
            else metrics.mean_recall
        )
        self.mean_hard_recall = (
            _combine_avg(
                self.mean_hard_recall,
                self.count,
                metrics.mean_hard_recall,
                metrics.count,
            )
            if self.mean_hard_recall > -0.1
            else metrics.mean_hard_recall
        )
        self.count += metrics.count

    def to_tensor(self) -> torch.Tensor:
        """Returns the metrics as a tensor."""
        return torch.tensor(
            [
                # Each data parallel shard could in theory have processed a slightly
                # different number of examples; scale by count to be on the safe side.
                self.mean_rr * self.count,
                self.mean_ap * self.count,
                self.mean_rank * self.count,
                self.mean_top_rank * self.count,
                self.mean_bottom_rank * self.count,
                self.mean_p1 * self.count,
                self.mean_gold_prob * self.count,
                self.mean_recall * self.count,
                self.mean_hard_recall * self.count,
                self.count,
            ]
        )

    @classmethod
    def from_tensor(cls, metrics_tensor: torch.Tensor) -> Self:
        """Returns the metrics as a tensor."""
        count = metrics_tensor[-1].item()
        return cls(
            mean_rr=metrics_tensor[0].item() / count if count else 0.0,
            mean_ap=metrics_tensor[1].item() / count if count else 0.0,
            mean_rank=metrics_tensor[2].item() / count if count else 0.0,
            mean_top_rank=metrics_tensor[3].item() / count if count else 0.0,
            mean_bottom_rank=metrics_tensor[4].item() / count if count else 0.0,
            mean_p1=metrics_tensor[5].item() / count if count else 0.0,
            mean_gold_prob=metrics_tensor[6].item() / count if count else 0.0,
            mean_recall=metrics_tensor[7].item() / count if count else 0.0,
            mean_hard_recall=metrics_tensor[8].item() / count if count else 0.0,
            count=int(count),
        )


def compute_ranking_metrics(
    X: TensorDict,
    prediction: TensorDict,
) -> RankingMetrics:
    """Computes ranking metrics.

    Args:
        X: A TensorDict containing the following entries:
            labels_BK: A tensor of shape [batch] containing the gold labels.
            labels_mask_BK: A mask indicating which entries in the batch are queries.
                Only exactly one query per batch is supported for now.
        prediction: A TensorDict containing the following entries:
            query_emb_BD: A tensor of shape [batch, dim] containing the query embeddings.
            doc_emb_BKD: A tensor of shape [batch, docs, dim] containing the doc embeddings.

    Dimensions:
        B: Batch size.
        K: Number of documents per batch element.
        D: Embedding dimension.

    Returns:
        A RankingMetrics object with metrics for all entries in the batch.
    """
    labels_BK = X["labels_BK"]
    labels_mask_BK = X["labels_mask_BK"]
    query_emb_BD = prediction["query_emb_BD"]
    doc_emb_BKD = prediction["doc_emb_BKD"]

    B = labels_BK.shape[0]
    logits_BK = torch.einsum("bd, bkd -> bk", query_emb_BD, doc_emb_BKD)
    # Mask out logits for masked positions
    logits_BK = torch.where(labels_mask_BK, logits_BK, -torch.inf)
    pred_lprobs_BK = torch.log_softmax(logits_BK, dim=-1)
    pred_ranking_BK = pred_lprobs_BK.argsort(dim=-1, descending=True)

    metrics = RankingMetrics(count=0)
    # Compute the metrics in a for-loop because the metrics library doesn't support
    # batching. This is probably fine because the metrics logic here is trivial compared
    # to computing the logits.
    for i in range(B):
        metrics.update(
            compute_ranking_metrics_from_ranking(
                pred_lprobs_BK[i], pred_ranking_BK[i], labels_BK[i]
            )
        )
    return metrics


def compute_ranking_metrics_from_ranking(
    pred_lprobs_K: torch.Tensor,
    pred_ranking_K: torch.Tensor,
    labels_K: torch.Tensor,
    missing_correct_count: int = 0,
    total_gold_count: int | None = None,
) -> RankingMetrics:
    """Computes ranking metrics given the ranking of a batch element.

    This function is split out of `compute_ranking_metrics` so that it can be used in
    evaluation.

    Args:
        pred_lprobs_K: A tensor of shape [K] containing the log probabilities of the
            documents.
        pred_ranking_K: A tensor of shape [K] containing the predicted ranking.
        labels_K: A tensor of shape [K] containing the gold label scores.
        missing_correct_count: The number of correct labels that were missing.
            We typically only use this number during evaluation, because only the chunks
            in the batch are considered during training.

    Dimensions:
        K: Number of documents per batch element.

    Returns:
        A RankingMetrics object with metrics for this batch element.
    """
    K = labels_K.shape[0]

    # Get all ranks where the gold document is the highest scoring.
    # This allows for there to be multiple "gold" documents if they all have the same
    # value, as is the case for data with binary relevance labels.
    target_K = labels_K == labels_K.max()

    # Get the top rank.
    gold_count = target_K.sum()
    gold_ranking = target_K[pred_ranking_K].nonzero()

    mean_rr = retrieval_reciprocal_rank(pred_lprobs_K, target_K)
    mean_ap = (
        retrieval_average_precision(pred_lprobs_K, target_K)
        # Update the denominator to account for missing correct labels.
        * gold_count
        / (missing_correct_count + gold_count)
    )
    mean_top_rank = gold_ranking.min()
    # We discount the mean ranking of having all gold documents in the top K, which
    # is 1/N (N * (N-1) / 2).
    # And then we consider all missing correct labels as having a rank of K.
    mean_rank = (gold_ranking.float().sum() + missing_correct_count * K) / (
        gold_count + missing_correct_count
    ) - (gold_count + missing_correct_count - 1) / 2
    # For the bottom ranks, discount additional gold items.
    mean_bottom_rank = (
        (gold_ranking.max() - (gold_count - 1))
        if not missing_correct_count
        else torch.tensor(K)
    )
    mean_p1 = (gold_ranking.min() == 0).float()
    # We treat all missing correct labels as having a probability of 0.
    mean_gold_prob = torch.logsumexp(pred_lprobs_K[target_K], -1).exp() / (
        gold_count + missing_correct_count
    )

    if total_gold_count is not None:
        mean_recall = (gold_count / total_gold_count).item()
        mean_hard_recall = 1.0 if (gold_count == total_gold_count) else 0.0
    else:
        mean_recall = -1.0
        mean_hard_recall = -1.0

    return RankingMetrics(
        mean_rr=mean_rr.item(),
        mean_ap=mean_ap.item(),
        # Adjust ranks to start at 1.
        mean_top_rank=mean_top_rank.item() + 1.0,
        mean_rank=mean_rank.item() + 1.0,
        mean_bottom_rank=mean_bottom_rank.item() + 1.0,
        mean_p1=mean_p1.item(),
        mean_gold_prob=mean_gold_prob.float().item(),
        mean_recall=mean_recall,
        mean_hard_recall=mean_hard_recall,
    )


def reduce_retrieval_metrics_dp(metrics: RankingMetrics) -> RankingMetrics:
    """Reduce metrics across data parallel groups."""
    if mpu.get_data_parallel_world_size() == 1:
        return metrics

    import torch.distributed as dist

    # Put the metrics into a tensor so that we can use torch.distributed.all_reduce.
    metrics_tensor = metrics.to_tensor().cuda()

    dist.all_reduce(
        metrics_tensor, op=dist.ReduceOp.SUM, group=mpu.get_data_parallel_group()
    )

    return RankingMetrics.from_tensor(metrics_tensor)


def create_checkpoint(
    modules: Mapping[str, torch.nn.Module],
) -> tuple[Mapping[str, dict], Mapping[str, torch.Tensor]]:
    """Create a checkpoint for the fastbackward embedding model.

    NOTE(arun): This function currently lives in the retrieval_models which uses a
    different approach to checkpointing than the rest of fastbackward. We'll move this
    to a more central location if we agree on this approach.

    Args:
        modules: A mapping from component name to the corresponding model.

    Returns:
        A tuple of:
        - a dictionary describing the model components in the checkpoint.
        - the combined state dict for the models in the checkpoint.
    """
    if not all(isinstance(m, Serializable) for m in modules.values()):
        unserializable_modules = [
            type(m) for m in modules.values() if not isinstance(m, Serializable)
        ]
        raise ValueError(
            "Could not serialize the following components because they are missing the "
            f"required method `create_config()`: {unserializable_modules}."
        )

    # Test that we can save a checkpoint for the model
    configs = serialize_component_configs(typing.cast(dict[str, Serializable], modules))
    state_dict = flatten_dict(
        {key: module.state_dict() for key, module in modules.items()}
    )
    return configs, state_dict


def load_checkpoint(
    checkpoint_path: Union[Path, str],
    mp_rank: Optional[int] = None,
) -> ComponentMap:
    """Load a fastbackward embedding model from a checkpoint.

    NOTE(arun): This function currently lives in the retrieval_models which uses a
    different approach to checkpointing than the rest of fastbackward. We'll move this
    to a more central location if we agree on this approach.

    Args:
        checkpoint_path: The path to the checkpoint.
        mp_rank: The model parallel rank of the checkpoint. If unset, we will use the
            model parallel rank of the current process.

    Returns:
        A map from component name to the corresponding model. This will typically
        contain two keys, "model" and "loss".
    """
    checkpoint_path = Path(checkpoint_path)
    if mp_rank is None:
        mp_rank = mpu.get_model_parallel_rank()

    # load the model from its configuration
    component_configs = json.loads((checkpoint_path / "params.json").read_text())
    # load the checkpoint weights
    state_dict = torch.load(checkpoint_path / f"consolidated.{mp_rank:02d}.pth")

    return load_checkpoint_from_configs(component_configs, state_dict)


def load_checkpoint_from_configs(
    component_configs: Mapping[str, Any],
    state_dict: Mapping[str, torch.Tensor],
) -> ComponentMap:
    """Load a fastbackward embedding model from a checkpoint.

    NOTE(arun): This function currently lives in the retrieval_models which uses a
    different approach to checkpointing than the rest of fastbackward. We'll move this
    to a more central location if we agree on this approach.

    Args:
        checkpoint_path: The path to the checkpoint.

    Returns:
        A map from component name to the corresponding model. This will typically
        contain two keys, "model" and "loss".
    """
    component_configs = fix_component_configs(component_configs)
    components = create_components(component_configs)

    if "loss_fn" in components:
        # If we've also checkpointed the loss, then unflatten a single level to pull out
        # the model and loss.
        state_dict = unflatten_dict(state_dict, max_level=1)

        components["model"].load_state_dict(state_dict["model"])
        components["loss_fn"].load_state_dict(state_dict["loss_fn"])
    else:
        components["model"].load_state_dict(state_dict)

    return components


def fix_component_configs(component_configs: Mapping[str, Any]) -> Mapping[str, Any]:
    """Fix saved configurations to work with the latest version of fastbackward.

    This function is intended to be the central location to handle all the changes that
    made to the config files over time, as pertains to retrieval models.

    Args:
        component_configs: The component configurations to update.

    Returns:
        The fixed up component configurations.
    """
    ret = {}

    for name, config in component_configs.items():
        if f"{REFERENCE_PREFIX}{COMPONENT_NAME_KEY}" in config:
            component_name = config[f"{REFERENCE_PREFIX}{COMPONENT_NAME_KEY}"]
        elif f"{COMPONENT_NAME_KEY}" in config:
            component_name = config[f"{COMPONENT_NAME_KEY}"]
        else:
            raise ValueError(f"Could not find component name in config: {config}")

        match component_name:
            case "research.fastbackward.model.Transformer":
                # This function handles language model configuration changes.
                config = dict(config, params=fix_model_arg_params(config["params"]))
                ret[name] = config
            case _:
                ret[name] = config

    return ret


def load_embedder_from_checkpoint(
    checkpoint_path: Union[Path, str],
    mp_rank: Optional[int] = None,
    model_key: str = "model/query_model",
) -> TransformerEmbedder:
    """Load a fastbackward embedding model from a checkpoint.

    Args:
        checkpoint_path: The path to the checkpoint.
        mp_rank: The model parallel rank of the checkpoint. If unset, we will use the
            model parallel rank of the current process.
        model_key: The key of the model to load.

    Returns:
        The transformer embedder model.
    """
    component_map = load_checkpoint(checkpoint_path, mp_rank)
    return component_map.get_with_type(model_key, TransformerEmbedder)

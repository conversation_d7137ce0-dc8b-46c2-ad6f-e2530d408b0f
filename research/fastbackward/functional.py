"""Our own variants on functions from torch.nn.functional or related code."""

from typing import Optional

import torch
import torch.distributed as dist
import torch.nn.functional as F
from torch import Tensor

import research.fastbackward.fs_model_parallel as mpu


@torch.compile
def swiglu(gate: Tensor, x: Tensor) -> Tensor:
    """Fused SwiGLU: swish(gate) * x."""
    return F.silu(gate) * x


@torch.compile
def casted_cross_entropy(
    logits: Tensor, targets: Tensor, ignore_index: int = -100
) -> Tensor:
    """Wrapper around torch.nn.functional.cross_entropy that casts logits to float32.

    Doing this in one place makes it possible for torch.compile to fuse casting with the loss.

    Dimensions:
        logits: (*, vocab_size)
        targets: (*)
    where * is one or more batch dimensions (which are flattened).

    Returns:
        A scalar tensor containing the loss. The loss is averaged over all batch dimensions.
    """
    if logits.size()[:-1] != targets.size():
        raise ValueError(
            f"Logits and targets have different leading (batch) dimensions: {logits.size()} vs {targets.size()}"
        )
    logits_float = logits.view(-1, logits.size(-1)).float()
    loss = F.cross_entropy(logits_float, targets.view(-1), ignore_index=ignore_index)
    return loss


def vocab_parallel_cross_entropy(
    logits: Tensor, targets: Tensor, ignore_index: Optional[int] = None
) -> Tensor:
    """Extension of torch.nn.functional.cross_entropy for model-parallel logits.

    Args:
        logits (Tensor): A tensor of shape (*, parallel_vocab_size) where:
            - * is one or more batch dimensions
            - parallel_vocab_size is the per-model-parallel vocab size (ie, vocab_size // model_parallel_size)
        targets (Tensor): A tensor of shape (*) where * is matching batch dimensions.
        ignore_index (int, optional): A target value that is ignored and does not contribute to the loss.

    Returns:
        loss: A scalar tensor containing the loss. The loss is averaged over all batch dimensions.
    """
    if logits.size()[:-1] != targets.size():
        raise ValueError(
            f"Logits and targets have different leading (batch) dimensions: {logits.size()} vs {targets.size()}"
        )
    if logits.dtype != torch.float32:
        raise ValueError(
            f"Logits must have dtype torch.float32, but have dtype {logits.dtype}"
        )
    return VocabParallelCrossEntropyFn.apply(
        logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index
    )  # type: ignore


class VocabParallelCrossEntropyFn(torch.autograd.Function):
    # pylint: disable=abstract-method
    # pylint: disable=arguments-differ
    """Implementation of vocab_parallel_cross_entropy forward and backward.

    Based on the implementation in fairscale.nn.model_parallel.cross_entropy
    (https://github.com/facebookresearch/fairscale/blob/164cc0f3170b4a3951dd84dda29c3e1504ac4d6e/fairscale/nn/model_parallel/cross_entropy.py),
    which is itself based on the implementation from Megatron-LM.
    """

    @staticmethod
    def forward(ctx, parallel_logits, targets, ignore_index):
        assert parallel_logits.ndimension() == 2
        assert targets.ndimension() == 1

        # compute the max logit along the vocab dimension to re-scale the logits
        logits_max = torch.max(parallel_logits, dim=-1, keepdim=True)[0]
        dist.all_reduce(
            logits_max,
            op=dist.ReduceOp.MAX,
            group=mpu.get_model_parallel_group(),
        )
        parallel_logits.sub_(logits_max)

        # find the range of vocab indices I am responsible for
        batch_size, per_device_vocab_size = parallel_logits.size()
        vocab_start_index = mpu.get_model_parallel_rank() * per_device_vocab_size
        vocab_end_index = vocab_start_index + per_device_vocab_size

        # mask of targets for which I have the true logit (1 = masked, 0 = unmasked)
        target_mask = (targets < vocab_start_index) | (targets >= vocab_end_index)
        masked_targets = targets.clone() - vocab_start_index
        masked_targets[target_mask] = 0

        # pull out the predicted logits that I have and combine with the rest of the group
        batch_range = torch.arange(
            start=0, end=batch_size, device=parallel_logits.device
        )
        predicted_logits = (
            parallel_logits[batch_range, masked_targets].clone().contiguous()
        )
        predicted_logits[target_mask] = 0.0
        dist.all_reduce(
            predicted_logits,
            op=dist.ReduceOp.SUM,
            group=mpu.get_model_parallel_group(),
        )

        # sum(exp(logits)) across the vocab dimension with the rest of the group
        exp_logits = torch.exp_(parallel_logits)  # NOTE: modification is in-place
        sum_exp_logits = exp_logits.sum(dim=-1)
        dist.all_reduce(
            sum_exp_logits,
            op=dist.ReduceOp.SUM,
            group=mpu.get_model_parallel_group(),
        )

        # loss is log(sum(exp(logits))) - predicted_logit (average across batch)
        # need to zero out any ignore_index tokens
        per_token_losses = torch.log(sum_exp_logits) - predicted_logits
        if ignore_index is not None:
            ignore_index_mask = targets == ignore_index
            loss = per_token_losses[~ignore_index_mask].mean()
        else:
            ignore_index_mask = None
            loss = per_token_losses.mean()

        # compute actual probs with softmax for backward while we're here
        softmax = exp_logits.div_(sum_exp_logits.unsqueeze(dim=-1))

        # save for backward
        ctx.save_for_backward(softmax, target_mask, masked_targets, ignore_index_mask)

        return loss

    @staticmethod
    def backward(ctx, grad_output):
        softmax, target_mask, masked_targets, ignore_index_mask = ctx.saved_tensors

        batch_size, _ = softmax.size()
        batch_range = torch.arange(start=0, end=batch_size, device=softmax.device)

        softmax_update = 1.0 - target_mask.float()
        softmax[batch_range, masked_targets] -= softmax_update
        if ignore_index_mask is not None:
            # Since the loss is averaged over non-ignored tokens, the grad needs to be divided by the same count
            softmax.mul_(grad_output / (~ignore_index_mask).sum().float())
            softmax[ignore_index_mask, :] = 0.0
        else:
            softmax.mul_(grad_output / batch_size)

        return softmax, None, None

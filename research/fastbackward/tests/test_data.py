"""Tests for fastbackward.data."""

from research.fastbackward.data import (
    ListDataset,
    MapDataset,
    apply_chained,
    pad_or_truncate_tokens,
)


def test_mapped_dataset():
    """Test MappedDataset."""
    dataset = ListDataset(list(range(10)))
    mapped_dataset = MapDataset(
        dataset,
        apply_chained(
            [
                lambda x: x * 2,
                lambda x: x * 3,
            ]
        ),
    )

    assert len(mapped_dataset) == len(dataset)
    for i in range(len(dataset)):
        assert mapped_dataset[i] == dataset[i] * 6


def test_pad_or_truncate_tokens():
    """Test pad_tokens."""

    assert pad_or_truncate_tokens([1, 2, 3], 5, 0) == [1, 2, 3, 0, 0]
    assert pad_or_truncate_tokens([1, 2, 3, 4, 5], 5, 0) == [1, 2, 3, 4, 5]
    assert pad_or_truncate_tokens([1, 2, 3, 4, 5, 6], 5, 0) == [1, 2, 3, 4, 5]

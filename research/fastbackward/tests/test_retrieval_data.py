"""Tests for fastbackward.data."""

import numpy as np
import torch
from tensordict.tensordict import TensorDict

from base.tokenizers import (
    StarCoderSpecialTokens,
    create_tokenizer_by_name,
)
from research.fastbackward.data import (
    ListDataset,
    MapDataset,
)
from research.fastbackward.retrieval_data import (
    create_collate_retrieval_data_fn,
    create_tokens_to_retrieval_data_fn,
    resample_documents_keeping_positives,
)


def test_resample_documents_keeping_positives():
    """Test resample_documents_keeping_positives."""
    document_tokens = np.arange(100).reshape(100, -1)
    document_scores = (np.arange(100) % 3 == 0).astype(np.float32)
    document_tokens, document_scores = resample_documents_keeping_positives(
        document_tokens.tolist(),
        document_scores.tolist(),
        # About 1/3rd of the documents are positive.
        max_positive_fraction=0.25,
        documents_per_batch=20,
        positive_threshold=document_scores.max(),
    )
    assert len(document_tokens) == 20
    assert (np.array(document_scores) == 1.0).sum() == 5


def test_resample_documents_keeping_positives_with_float_scores():
    """Test resample_documents_keeping_positives with float scores."""
    document_tokens = np.arange(100).reshape(100, -1)
    document_scores = np.random.randn(100).astype(np.float32)
    # Get the top scoring document.
    top_idx = document_scores.argmax()

    for _ in range(10):
        document_tokens_, document_scores_ = resample_documents_keeping_positives(
            document_tokens.tolist(),
            document_scores.tolist(),
            max_positive_fraction=0.25,
            # Keep the number of documents per batch small: the probability of having
            # the top document by chance is now just 5%.
            documents_per_batch=5,
            positive_threshold=document_scores.max(),
        )
        assert len(document_tokens_) == 5
        # Test that we've sampled the top document.
        assert top_idx in document_tokens_


def test_resample_documents_keeping_positives_max_positives():
    """Test resample_documents_keeping_positives."""
    document_tokens = np.arange(100).reshape(100, -1)
    document_scores = (np.arange(100) % 3 == 0).astype(np.float32)
    document_tokens, document_scores = resample_documents_keeping_positives(
        document_tokens.tolist(),
        document_scores.tolist(),
        # About 1/3rd of the documents are positive.
        max_positive_fraction=0.25,
        documents_per_batch=20,
        max_positive_count=1,
        positive_threshold=document_scores.max(),
    )
    assert len(document_tokens) == 20
    assert (np.array(document_scores) == 1.0).sum() == 1


def test_resample_documents_keeping_positives_positive_threshold():
    """Test resample_documents_keeping_positives."""
    document_tokens = np.arange(100).reshape(100, -1)
    document_scores = (np.arange(100) % 3).astype(np.float32)
    document_tokens_, document_scores_ = resample_documents_keeping_positives(
        document_tokens.tolist(),
        document_scores.tolist(),
        # About 1/3rd of the documents are positive.
        max_positive_fraction=0.25,
        documents_per_batch=20,
        positive_threshold=1,
    )
    assert len(document_tokens_) == 20
    assert (np.array(document_scores_) >= 1.0).sum() == 5

    document_tokens_, document_scores_ = resample_documents_keeping_positives(
        document_tokens.tolist(),
        document_scores.tolist(),
        # About 1/3rd of the documents are positive.
        max_positive_fraction=0.25,
        documents_per_batch=20,
        positive_threshold=2,
    )
    assert len(document_tokens_) == 20
    assert (np.array(document_scores_) >= 1.0).sum() > 5
    assert (np.array(document_scores_) >= 2.0).sum() == 5


def test_create_tokens_to_retrieval_data_fn_random():
    """Test prepare_scored_retrieval_from_sequence."""
    tokenizer = create_tokenizer_by_name("starcoder")
    special_tokens = tokenizer.special_tokens
    assert isinstance(special_tokens, StarCoderSpecialTokens)

    sequence_dataset = ListDataset(
        [
            torch.tensor(
                tokenizer.tokenize_unsafe(
                    "query<|ret-endofquery|>"
                    "document a<|ret-endofkey|>0<|ret-endofkey|>"
                    "document b<|ret-endofkey|>-1<|ret-endofkey|>"
                    "document c<|ret-endofkey|>3.14<|ret-endofkey|>"
                )
            ),
            torch.tensor(
                tokenizer.tokenize_unsafe(
                    "very long query<|ret-endofquery|>"
                    "very long document a<|ret-endofkey|>-0.3<|ret-endofkey|>"
                    "long document b<|ret-endofkey|>1<|ret-endofkey|>"
                    "document c<|ret-endofkey|>2.71<|ret-endofkey|>"
                )
            ),
        ],
    )

    dataset = MapDataset(
        sequence_dataset,
        create_tokens_to_retrieval_data_fn(
            tokenizer=tokenizer,
            selection_method="random",
            # Something larger than the positive examples below.
            positive_threshold=2,
        ),
    )
    torch.testing.assert_close(
        dataset[0],
        TensorDict(
            {
                "query_tokens_BLq": torch.tensor(
                    [tokenizer.tokenize_unsafe("query<|ret-endofquery|>")]
                ).int(),
                "doc_tokens_BKLd": torch.tensor(
                    [
                        [
                            # Document c is reordered to be the first document because
                            # it is the most relevant (i.e. it is a "positive").
                            tokenizer.tokenize_unsafe("document c<|ret-endofkey|>"),
                            tokenizer.tokenize_unsafe("document a<|ret-endofkey|>"),
                            tokenizer.tokenize_unsafe("document b<|ret-endofkey|>"),
                        ]
                    ]
                ).int(),
                "labels_BK": torch.tensor(
                    [
                        [
                            3.14,
                            0,
                            -1,
                        ]
                    ]
                ),
                "labels_mask_BK": torch.tensor(
                    [
                        [
                            True,
                            True,
                            True,
                        ]
                    ]
                ),
            },
            batch_size=[],
        ),
    )
    torch.testing.assert_close(
        dataset[1],
        TensorDict(
            {
                "query_tokens_BLq": torch.tensor(
                    [tokenizer.tokenize_unsafe("very long query<|ret-endofquery|>")]
                ).int(),
                "doc_tokens_BKLd": torch.tensor(
                    [
                        [
                            tokenizer.tokenize_unsafe(
                                "document c<|ret-endofkey|><|padding|><|padding|>"
                            ),
                            tokenizer.tokenize_unsafe(
                                "very long document a<|ret-endofkey|>"
                            ),
                            tokenizer.tokenize_unsafe(
                                "long document b<|ret-endofkey|><|padding|>"
                            ),
                        ]
                    ]
                ).int(),
                "labels_BK": torch.tensor(
                    [
                        [
                            2.71,
                            -0.3,
                            1,
                        ]
                    ]
                ),
                "labels_mask_BK": torch.tensor(
                    [
                        [
                            True,
                            True,
                            True,
                        ]
                    ]
                ),
            },
            batch_size=[],
        ),
    )


def test_create_tokens_to_retrieval_data_fn_first():
    """Test prepare_scored_retrieval_from_sequence."""
    tokenizer = create_tokenizer_by_name("starcoder")
    special_tokens = tokenizer.special_tokens
    assert isinstance(special_tokens, StarCoderSpecialTokens)

    sequence_dataset = ListDataset(
        [
            torch.tensor(
                tokenizer.tokenize_unsafe(
                    "query<|ret-endofquery|>"
                    "document a<|ret-endofkey|>0<|ret-endofkey|>"
                    "document b<|ret-endofkey|>-1<|ret-endofkey|>"
                    "document c<|ret-endofkey|>3.14<|ret-endofkey|>"
                )
            ),
        ],
    )

    dataset = MapDataset(
        sequence_dataset,
        create_tokens_to_retrieval_data_fn(
            tokenizer=tokenizer, selection_method="first"
        ),
    )
    torch.testing.assert_close(
        dataset[0],
        TensorDict(
            {
                "query_tokens_BLq": torch.tensor(
                    [tokenizer.tokenize_unsafe("query<|ret-endofquery|>")]
                ).int(),
                "doc_tokens_BKLd": torch.tensor(
                    [
                        [
                            tokenizer.tokenize_unsafe("document a<|ret-endofkey|>"),
                            tokenizer.tokenize_unsafe("document b<|ret-endofkey|>"),
                            tokenizer.tokenize_unsafe("document c<|ret-endofkey|>"),
                        ]
                    ]
                ).int(),
                "labels_BK": torch.tensor(
                    [
                        [
                            0,
                            -1,
                            3.14,
                        ]
                    ]
                ),
                "labels_mask_BK": torch.tensor(
                    [
                        [
                            True,
                            True,
                            True,
                        ]
                    ]
                ),
            },
            batch_size=[],
        ),
    )


def test_create_tokens_to_retrieval_data_fn_divisible_by():
    """Test prepare_scored_retrieval_from_sequence."""
    tokenizer = create_tokenizer_by_name("starcoder")
    special_tokens = tokenizer.special_tokens
    assert isinstance(special_tokens, StarCoderSpecialTokens)

    sequence_dataset = ListDataset(
        [
            torch.tensor(
                tokenizer.tokenize_unsafe(
                    "query<|ret-endofquery|>"
                    "document a<|ret-endofkey|>0<|ret-endofkey|>"
                    "document b<|ret-endofkey|>-1<|ret-endofkey|>"
                    "document c<|ret-endofkey|>3.14<|ret-endofkey|>"
                )
            ),
        ],
    )

    dataset = MapDataset(
        sequence_dataset,
        create_tokens_to_retrieval_data_fn(
            tokenizer=tokenizer,
            selection_method="first",
            make_token_lengths_divisible_by=4,
        ),
    )
    torch.testing.assert_close(
        dataset[0]["query_tokens_BLq"],
        torch.tensor(
            [tokenizer.tokenize_unsafe("query<|ret-endofquery|><|padding|><|padding|>")]
        ).int(),
    )
    torch.testing.assert_close(
        dataset[0]["doc_tokens_BKLd"],
        torch.tensor(
            [
                [
                    tokenizer.tokenize_unsafe("document a<|ret-endofkey|><|padding|>"),
                    tokenizer.tokenize_unsafe("document b<|ret-endofkey|><|padding|>"),
                    tokenizer.tokenize_unsafe("document c<|ret-endofkey|><|padding|>"),
                ]
            ]
        ).int(),
    )


def test_collate_retrieval_data():
    """Test collate_retrieval_data."""
    tokenizer = create_tokenizer_by_name("starcoder")
    special_tokens = tokenizer.special_tokens
    assert isinstance(special_tokens, StarCoderSpecialTokens)

    sequence_dataset = ListDataset(
        [
            # This example only has 2 documents.
            torch.tensor(
                tokenizer.tokenize_unsafe(
                    "query<|ret-endofquery|>"
                    "document a<|ret-endofkey|>0<|ret-endofkey|>"
                    "document b<|ret-endofkey|>-1<|ret-endofkey|>"
                )
            ),
            # This example has 3 documents, and are longer than the first.
            torch.tensor(
                tokenizer.tokenize_unsafe(
                    "very long query<|ret-endofquery|>"
                    "document c<|ret-endofkey|>2.71<|ret-endofkey|>"
                    "very long document a<|ret-endofkey|>-0.3<|ret-endofkey|>"
                    "long document b<|ret-endofkey|>1<|ret-endofkey|>"
                )
            ),
        ],
    )

    dataset = MapDataset(
        sequence_dataset,
        create_tokens_to_retrieval_data_fn(tokenizer=tokenizer),
    )
    collate_fn = create_collate_retrieval_data_fn(
        document_token_id=special_tokens.end_of_key,
        pad_id=special_tokens.padding,
    )
    actual = collate_fn(dataset[:])
    expected = TensorDict(
        {
            "query_tokens_BLq": torch.tensor(
                [
                    tokenizer.tokenize_unsafe(
                        "query<|ret-endofquery|><|padding|><|padding|>"
                    ),
                    tokenizer.tokenize_unsafe("very long query<|ret-endofquery|>"),
                ]
            ).int(),
            "doc_tokens_BKLd": torch.tensor(
                [
                    [
                        tokenizer.tokenize_unsafe(
                            "document a<|ret-endofkey|><|padding|><|padding|>"
                        ),
                        tokenizer.tokenize_unsafe(
                            "document b<|ret-endofkey|><|padding|><|padding|>"
                        ),
                        tokenizer.tokenize_unsafe(
                            "<|ret-endofkey|><|padding|><|padding|><|padding|><|padding|>"
                        ),
                    ],
                    [
                        tokenizer.tokenize_unsafe(
                            "document c<|ret-endofkey|><|padding|><|padding|>"
                        ),
                        tokenizer.tokenize_unsafe(
                            "very long document a<|ret-endofkey|>"
                        ),
                        tokenizer.tokenize_unsafe(
                            "long document b<|ret-endofkey|><|padding|>"
                        ),
                    ],
                ]
            ).int(),
            "labels_BK": torch.tensor(
                [
                    [
                        0,
                        -1,
                        -torch.inf,
                    ],
                    [
                        2.71,
                        -0.3,
                        1,
                    ],
                ]
            ),
            "labels_mask_BK": torch.tensor(
                [
                    [
                        True,
                        True,
                        False,
                    ],
                    [
                        True,
                        True,
                        True,
                    ],
                ]
            ),
        },
        batch_size=[2],
    )

    torch.testing.assert_close(actual, expected)

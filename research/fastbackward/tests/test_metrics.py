import math
import random

import pytest
import torch

import research.fastbackward.fs_model_parallel as mpu
from research.fastbackward.metrics import (
    ApproximatePercentiles,
    Mean,
    Percentiles,
    TokenAccuracy,
    Variance,
    _get_bucket,
)
from research.fastbackward.tests.fake_distributed_runner import distributed_runner


def expand_predictions_to_logits(
    predictions: torch.Tensor,
    vocab_size: int,
) -> torch.Tensor:
    """
    Expand the predictions to logits.

    Args:
        predictions (torch.Tensor): A 2-D Integer tensor with shape (batch, seq_len).
        vocab_size (int): The size of the vocabulary.

    Returns:
        torch.Tensor: A 3-D tensor with shape (batch, seq_len, vocab_size) where its argmax(dim=-1) == predictions.
    """
    batch_size, seq_len = predictions.shape
    logits = torch.zeros((batch_size, seq_len, vocab_size), device=predictions.device)
    logits.scatter_(dim=-1, index=predictions.unsqueeze(-1), value=1)
    if mpu.get_model_parallel_world_size() > 1:
        assert logits.shape[-1] % mpu.get_model_parallel_world_size() == 0
        logits_per_device = torch.chunk(
            logits, mpu.get_model_parallel_world_size(), dim=-1
        )
        logits = logits_per_device[mpu.get_model_parallel_rank()]
    return logits


def test_quantize_random(num_tests=1000):
    count_mismatches_for_smaller_errors = 0
    for _ in range(num_tests):
        value = 10 ** (10 * random.random() - 5)

        if random.random() < 0.5:
            value *= -1

        assert _get_bucket(value) == pytest.approx(value, rel=0.01)
        if _get_bucket(value) == pytest.approx(value, rel=0.001):
            count_mismatches_for_smaller_errors += 1
    # In at least half the tests the error should be larger than 0.01%
    assert count_mismatches_for_smaller_errors > (num_tests / 2)


def test_mean():
    m = Mean()
    m.observe(1.0)
    m.observe(2.0)
    m.observe(3.0)
    assert m.get() == 2.0


def test_mean_join():
    m1 = Mean()
    m1.observe(1.0)
    m1.observe(2.0)
    m2 = Mean()
    m2.observe(3.0)
    m2.observe(4.0)
    m1.join(m2)
    assert m1.get() == 2.5
    assert m2.get() == 3.5


def test_mean_join_empty():
    m1 = Mean()
    m2 = Mean()
    m1.join(m2)
    assert m1.get() is None


def test_mean_join_one_empty():
    m1 = Mean()
    m1.observe(1.0)
    m2 = Mean()
    m1.join(m2)
    assert m1.get() == 1.0


def test_mean_negative():
    m = Mean()
    m.observe(1.0)
    m.observe(-2.0)
    assert m.get() == -0.5


def test_mean_zero():
    m = Mean()
    m.observe(0.0)
    m.observe(0.0)
    assert m.get() == 0.0


def test_mean_inf():
    m = Mean()
    m.observe(float("inf"))
    assert m.get() == float("inf")


def test_mean_neginf():
    m = Mean()
    m.observe(float("-inf"))
    assert m.get() == float("-inf")


def test_mean_nan():
    m = Mean()
    m.observe(float("nan"))
    value = m.get()
    assert value is not None
    assert math.isnan(value)


def test_percentile():
    p = Percentiles()
    p.observe(1.0)
    p.observe(2.0)
    p.observe(3.0)
    assert p.get(0.5) == 2.0
    assert p.get(0.25) == 1.0
    assert p.get(0.75) == 3.0


def test_percentile_join():
    p1 = Percentiles()
    p1.observe(1.0)
    p1.observe(2.0)
    p2 = Percentiles()
    p2.observe(3.0)
    p1.join(p2)
    assert p1.get(0.5) == 2.0
    assert p2.get(0.5) == 3.0


def test_approx_percentile():
    p = ApproximatePercentiles()
    p.observe(1.0)
    p.observe(2.0)
    p.observe(3.0)
    assert p.get(0.5) == pytest.approx(2.0, rel=0.01)
    assert p.get(0.25) == pytest.approx(1.0, rel=0.01)
    assert p.get(0.75) == pytest.approx(3.0, rel=0.01)


def test_approx_percentile_join():
    p1 = ApproximatePercentiles()
    p1.observe(1.0)
    p1.observe(2.0)
    p2 = ApproximatePercentiles()
    p2.observe(3.0)
    p1.join(p2)
    assert p1.get(0.5) == pytest.approx(2.0, rel=0.01)
    assert p2.get(0.5) == pytest.approx(3.0, rel=0.01)


def test_variance():
    v = Variance()
    v.observe(1.0)
    v.observe(2.0)
    v.observe(3.0)
    assert v.get() == 2 / 3


def test_variance_join():
    v1 = Variance()
    v1.observe(1.0)
    v1.observe(2.0)
    v2 = Variance()
    v2.observe(3.0)
    v1.join(v2)
    assert v1.get() == 2 / 3
    assert v2.get() == 0.0


def _token_accuracy_perfect_match():
    y = torch.tensor(
        [
            [1, 2, 3, 4, 5],
            [6, 7, 8, 9, 0],
        ],
        device=torch.device("cuda"),
    )
    y_hat = torch.tensor(
        [
            [1, 2, 3, 4, 5],
            [6, 7, 8, 9, 0],
        ],
        device=y.device,
    )
    results = TokenAccuracy()(y, y, expand_predictions_to_logits(y_hat, vocab_size=10))
    torch.testing.assert_close(results, torch.tensor(1.0, device=y.device))


def _token_accuracy_partial_match():
    y = torch.tensor(
        [
            [1, 2, 3, 4, 5],
            [6, 7, 8, 9, 0],
        ],
        device=torch.device("cuda"),
    )
    y_hat = torch.tensor(
        [
            [1, 2, 3, 0, 0],  # 3/5 correct
            [6, 7, 0, 0, 1],  # 2/5 correct
        ],
        device=y.device,
    )
    results = TokenAccuracy()(y, y, expand_predictions_to_logits(y_hat, vocab_size=10))
    torch.testing.assert_close(results, torch.tensor(0.5, device=y.device))


def _token_accuracy_masking():
    y = torch.tensor(
        [
            [1, 2, -1, -1, -1],  # Only first 2 tokens are valid
            [6, -1, -1, -1, -1],  # Only first token is valid
        ],
        device=torch.device("cuda"),
    )
    y_hat = torch.tensor(
        [
            [1, 2, 0, 0, 0],  # Both valid tokens correct
            [6, 0, 0, 0, 0],  # Valid token correct
        ],
        device=y.device,
    )
    results = TokenAccuracy()(y, y, expand_predictions_to_logits(y_hat, vocab_size=10))
    torch.testing.assert_close(results, torch.tensor(1.0, device=y.device))


def _token_accuracy_mismatch():
    y = torch.tensor(
        [
            [1, 1, 1, 1, 1],
            [5, 5, 5, 5, 5],
        ],
        device=torch.device("cuda"),
    )
    y_hat = torch.tensor(
        [
            [2, 2, 2, 2, 2],
            [6, 6, 6, 6, 6],
        ],
        device=y.device,
    )
    results = TokenAccuracy()(y, y, expand_predictions_to_logits(y_hat, vocab_size=10))
    torch.testing.assert_close(results, torch.tensor(0.0, device=y.device))


def _token_accuracy_all_ignored():
    y = torch.tensor(
        [
            [-1, -1, -1, -1, -1],
            [-1, -1, -1, -1, -1],
        ],
        device=torch.device("cuda"),
    )
    y_hat = torch.tensor(
        [
            [1, 2, 3, 4, 5],
            [6, 7, 8, 9, 0],
        ],
        device=y.device,
    )
    results = TokenAccuracy()(y, y, expand_predictions_to_logits(y_hat, vocab_size=10))
    assert results.numel() == 1, f"{results=}"
    assert results.ndim == 0
    assert results.item() == 0.0


def _token_accuracy_some_ignored():
    y = torch.tensor(
        [
            [1, 2, 3, 4, 5],
            [1, 1, 1, 1, 1],
            [-1, -1, -1, -1, -1],
        ],
        device=torch.device("cuda"),
    )
    y_hat = torch.tensor(
        [
            [1, 2, 3, 4, 0],  # 4/5 correct
            [1, 1, 1, 2, 2],  # 3/5 correct
            [6, 7, 8, 9, 0],
        ],
        device=y.device,
    )
    results = TokenAccuracy()(y, y, expand_predictions_to_logits(y_hat, vocab_size=10))
    torch.testing.assert_close(results, torch.tensor((0.8 + 0.6) / 2, device=y.device))


# @pytest.mark.parametrize("world_size", [1, 2])
@pytest.mark.parametrize("world_size", [1])
@pytest.mark.parametrize(
    "test_fn",
    [
        _token_accuracy_perfect_match,
        _token_accuracy_partial_match,
        _token_accuracy_masking,
        _token_accuracy_mismatch,
        _token_accuracy_all_ignored,
        _token_accuracy_some_ignored,
    ],
)
def test_token_accuracy(world_size, test_fn):
    """Test the token_accuracy function."""
    with distributed_runner(
        world_size=world_size,
        model_parallel_size=world_size,
        debug_mode=world_size == 1,
    ) as runner:
        runner.run(test_fn)

"""Tests for fastbackward.model."""

from typing import Callable

import pytest
import torch
from torch.utils.data import <PERSON><PERSON><PERSON><PERSON>, TensorDataset, default_collate

from research.fastbackward import distributed, losses
from research.fastbackward import fs_model_parallel as mpu
from research.fastbackward.model import (
    ModelArgs,
    Transformer,
    configure_fsdp_optimizer,
)
from research.fastbackward.tests.fake_distributed_runner import distributed_runner
from research.fastbackward.training import TrainOptions, train_loop

# Each test function below is actually run with the distributed runner below. They are
# marked private to prevent pytest from running them directly.


def smoke_test_model(model: torch.nn.Module, args: ModelArgs):
    """Smoke test for a model."""
    n_kv_heads = args.n_kv_heads or args.n_heads
    if n_kv_heads < mpu.get_model_parallel_world_size():
        assert mpu.get_model_parallel_world_size() % n_kv_heads == 0
        mpu.initialize_model_parallel_subgroup(
            "kv", mpu.get_model_parallel_world_size() // n_kv_heads
        )

    my_device = distributed.get_local_device()

    # NOTE(arun): We use FP16 here because it is the only data type supported by Gloo
    # AND flash attention. In practice, always use bfloat16.
    model.to(dtype=torch.float16, device=my_device)
    optimizer, fms = configure_fsdp_optimizer(
        model, weight_decay=0.1, learning_rate=0.1, betas=(0.9, 0.999)
    )

    batch_size = 4
    block_size = 16
    tokens_BL = torch.arange(batch_size * block_size, device=my_device).reshape(
        batch_size, block_size
    )
    targets_BL = torch.arange(1, batch_size * block_size + 1, device=my_device).reshape(
        batch_size, block_size
    )
    logits_BLV = model(tokens_BL)
    assert logits_BLV.shape == (
        batch_size,
        block_size,
        args.vocab_size // mpu.get_model_parallel_world_size(),
    ), logits_BLV.shape

    loss = losses.cross_entropy_loss(tokens_BL, targets_BL, logits_BLV)
    assert loss.shape == ()
    assert loss.item() > 0

    train_loader = DataLoader(
        TensorDataset(tokens_BL, targets_BL),
        batch_size=4,
        shuffle=False,
        # Add a "number of masked tokens" value to the end of each batch.
        # TODO(arun): We should make this less hard-coded in the train loop.
        collate_fn=lambda batch: (*default_collate(batch), block_size),
    )

    for _ in train_loop(
        model,
        loss_fn=losses.cross_entropy_loss,
        flat_model_state=fms,
        optimizer=optimizer,
        lr_schedule=lambda _: 1e-3,
        train_options=TrainOptions(
            batch_size=4,
            block_size=16,
            gradient_accumulation_steps=1,
            log_interval=1,
            max_iters=1,
        ),
        train_loader=train_loader,
    ):
        pass

    updated_loss = losses.cross_entropy_loss(tokens_BL, targets_BL, model(tokens_BL))

    assert (
        updated_loss < loss
    ), f"Loss increased from {loss.item()=} to {updated_loss.item()=}"


def _test_starcoder():
    """Smoke test for the StarCoder model."""
    args = ModelArgs(
        dim=64,
        n_layers=2,
        n_heads=4,
        n_kv_heads=1,
        max_seq_len=16,
        vocab_size=128,
        ffn_type="mlp",
        bias="attn_mlp",
        norm_type="layernorm",
        pos_embed_type="absolute",
    )
    smoke_test_model(Transformer(args), args)


def _test_llama():
    """Smoke test for the LLaMA model."""
    args = ModelArgs(
        dim=64,
        n_layers=2,
        n_heads=2,
        max_seq_len=16,
        vocab_size=128,
        multiple_of=32,
        ffn_dim_multiplier=2.0,
    )
    smoke_test_model(Transformer(args), args)


# NOTE(arun): We can only test with data parallel size 1 because the `gloo` backend
# does not support `reduce_scatter` or the `ReduceOp.AVG`.
@pytest.mark.parametrize("model_parallel_size", [1, 2])
@pytest.mark.parametrize("data_parallel_size", [1])
@pytest.mark.parametrize(
    "test_fn",
    [
        _test_starcoder,
        _test_llama,
    ],
)
def test_models(
    model_parallel_size: int,
    data_parallel_size: int,
    test_fn: Callable[[int, int], None],
):
    world_size = model_parallel_size * data_parallel_size
    # NB: need to init the MP subgroup if num_kv_heads < model_parallel_size, which occurs here
    # in the case of starcoder (kv_heads == 1) and model_parallel_size > 1.
    if model_parallel_size > 1 and test_fn == _test_starcoder:
        model_parallel_subgroups = {"kv": model_parallel_size}
    else:
        model_parallel_subgroups = None
    # Note the llama models can be slow to test due to `torch.compile` time on first iteration
    with distributed_runner(
        world_size,
        model_parallel_size,
        model_parallel_subgroups=model_parallel_subgroups,
        timeout_s=30.0,
    ) as runner:
        runner.run(test_fn)

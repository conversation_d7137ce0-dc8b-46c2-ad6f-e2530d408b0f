"""Tests for loss masking."""

import torch

from research.fastbackward.loss_masking import (
    FimLossMask,
    IgnoreListLossMask,
    NegativeLossMask,
    PadLossMask,
)


def test_negative_loss_mask():
    """Test the negative loss mask."""
    loss_mask_fn = NegativeLossMask()
    tokens = torch.tensor([[1, 2, 3, 4, -5, 6]])
    expected = torch.tensor([[1, 1, 1, 1, 0, 1]])
    assert (loss_mask_fn(tokens) == expected).all()


def test_pad_loss_mask():
    """Only pad tokens are masked out."""
    pad_token_id = 9
    eos_token_id = 10
    loss_mask_fn = PadLossMask(pad_token_id, eos_token_id)
    tokens = torch.tensor(
        [
            [1, 2, 3, 4, 5],
            [5, 5, -1, 5, 5],
            [1, 4, 4, 9, 9],
        ]
    )
    expected = torch.tensor(
        [
            # no padding
            [1, 1, 1, 1, 1],
            # negative tokens are not masked out
            [1, 1, 1, 1, 1],
            # padding tokens are masked out
            [1, 1, 1, 0, 0],
        ]
    )
    assert (loss_mask_fn(tokens) == expected).all()


def test_ignore_list_loss_mask():
    """Test the ignore list loss mask ignores tokens in the list."""
    ignore_list = [1, 2, 3]
    pad_token_id = 0
    eos_token_id = 10
    loss_mask_fn = IgnoreListLossMask(ignore_list, pad_token_id, eos_token_id)
    tokens = torch.tensor(
        [
            [1, 2, 3, 4, 5],
            [5, 5, -1, 5, 5],
            [1, 4, 4, 0, 0],
        ]
    )
    expected = torch.tensor(
        [
            # member of the list are masked out
            [0, 0, 0, 1, 1],
            # negative tokens are masked out
            [1, 1, 0, 1, 1],
            # padding tokens are masked out
            [0, 1, 1, 0, 0],
        ]
    )
    assert (loss_mask_fn(tokens) == expected).all()


def test_ignore_list_loss_mask_with_same_token_for_eot_and_pad():
    """Test the ignore list loss mask with the same token for EOT and PAD."""
    ignore_list = [5]
    pad_token_id = 0
    eos_token_id = 0
    loss_mask_fn = IgnoreListLossMask(ignore_list, pad_token_id, eos_token_id)
    tokens = torch.tensor(
        [
            [1, 2, 0, 3, 0],
            [1, 2, 3, 5, 0],
            [1, 1, 1, 0, 0],
            [0, 0, 0, 0, 0],
        ]
    )
    expected = torch.tensor(
        [
            # EOS are not masked out
            [1, 1, 1, 1, 1],
            # EOS not masked out even with ignored tokens next to them
            [1, 1, 1, 0, 1],
            # padding tokens are masked out
            [1, 1, 1, 1, 0],
            [1, 0, 0, 0, 0],
        ]
    )
    assert (loss_mask_fn(tokens) == expected).all()


def test_fim_loss_mask():
    """Test the FIM loss mask correctly masks middle tokens and handles padding/EOT."""
    loss_mask_fn = FimLossMask(fim_middle_token_id=7, eot_token_id=8, pad_token_id=9)
    tokens = torch.tensor(
        [
            # Regular sequence with FIM middle token
            [1, 2, 7, 4, 5, 1, 1, 1],
            # Sequence with padding
            [1, 7, 1, 2, 9, 9, 9, 9],
            # Sequence with EOT and padding
            [1, 7, 1, 2, 3, 8, 9, 9],
            # Above with all negative tokens
            [-1, -7, -1, -2, -3, -8, -9, -9],
        ]
    )
    expected = torch.tensor(
        [
            # Mask out the FIM middle token and the tokens before
            [0, 0, 0, 1, 1, 1, 1, 1],
            # Mask out FIM token and padding
            [0, 0, 1, 1, 0, 0, 0, 0],
            # Mask out FIM token but keep EOT
            [0, 0, 1, 1, 1, 1, 0, 0],
            # Mask out all FIM tokens
            [0, 0, 1, 1, 1, 1, 0, 0],
        ]
    )
    assert (loss_mask_fn(tokens) == expected).all()

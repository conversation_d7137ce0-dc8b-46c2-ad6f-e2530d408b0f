"""Tests to ensure the gradients are the same in all model-/data-parallel conditions.

pytest --slow research/fastbackward/tests/test_parallel_gradients.py
"""

import tempfile
from pathlib import Path

import pytest
import torch
import torch.distributed as dist

from base.fastforward import positional_embeddings
from research.fastbackward import fs_model_parallel as mpu
from research.fastbackward import losses
from research.fastbackward import model as fbw_model
from research.fastbackward.checkpointing import checkpointing
from research.fastbackward.checkpointing.utils import (
    merge_model_parallel_consolidated_checkpoints,
    split_consolidated_checkpoint,
)
from research.fastbackward.tests.fake_distributed_runner import distributed_runner

# These take a while to run, limit to a few cases for CI unless `--slow` is set
_CI_TEST_CASES = [
    ("glu", 0, True, True, 4),
    ("glu", 2, False, False, 1),
    ("mlp", 1, False, True, 1),
]


@pytest.mark.parametrize("activation_checkpointing", [True, False])
@pytest.mark.parametrize("sequence_parallel", [False])
@pytest.mark.parametrize("gradient_accumulation_steps", [1, 4])
@pytest.mark.parametrize(
    "rotary_config",
    [  # this is DeepSeek-Coder-V2-Lite's config
        positional_embeddings.RotaryConfig(
            rotary_ratio=1.0,
            rotary_theta=10000.0,
            max_position_embeddings=163840,
            ext_config=positional_embeddings.YaRNExtensionConfig(
                rotary_scaling_factor=40.0,
                unscaled_max_position_embeddings=4096,
                beta_fast=32,
                beta_slow=1,
                mscale=0.707,
            ),
        )
    ],
)
@pytest.mark.parametrize(
    "attn_config",
    [
        fbw_model.DeepSeekV2MLASpec(
            hidden_dim=8,
            num_heads=4,
            v_head_dim=8,
            q_lora_rank=None,
            kv_lora_rank=8,
            qk_rope_head_dim=8,
            qk_nope_head_dim=16,
            eps=1e-6,
            bias=True,
        ),
        fbw_model.GenericAttnSpec(
            hidden_dim=8,
            n_heads=4,
            n_kv_heads=2,
            norm_type="layernorm",
            pos_embed_type="rope",
            bias=True,
        ),
        fbw_model.GenericAttnSpec(
            hidden_dim=8,
            n_heads=4,
            n_kv_heads=2,
            norm_type="rmsnorm",
            pos_embed_type="rope",
            bias=False,
            qkv_bias=True,
        ),
    ],
)
@pytest.mark.parametrize(
    "ffn_config",
    [
        fbw_model.DeepSeekV2MoESpec(
            hidden_dim=8,
            n_routed_experts=4,
            routed_scaling_factor=1.0,
            num_experts_per_token=2,
            intermediate_size=12,
            n_shared_experts=1,
            topk_method="greedy",
            use_dense_moe=True,
        ),
        fbw_model.DeepSeekV2MoESpec(
            hidden_dim=8,
            n_routed_experts=4,
            routed_scaling_factor=1.0,
            num_experts_per_token=2,
            intermediate_size=12,
            n_shared_experts=1,
            topk_method="greedy",
            use_dense_moe=False,
        ),
    ],
)
def test_data_and_model_parallel_match_single_gpu_with_nested_config(
    slow_is_enabled: bool,  # Fixture from conftest indicating presence of --slow
    sequence_parallel: bool,
    activation_checkpointing: bool,
    gradient_accumulation_steps: int,
    rotary_config: positional_embeddings.RotaryConfig,
    attn_config: fbw_model.GenericAttnSpec | fbw_model.DeepSeekV2MLASpec,
    ffn_config: fbw_model.MlpSpec | fbw_model.SwiGLUSpec | fbw_model.DeepSeekV2MoESpec,
):
    """Tests for gradient consistency under all model-/data-parallel conditions."""
    if not (
        slow_is_enabled
        or (
            isinstance(attn_config, fbw_model.DeepSeekV2MLASpec)
            and isinstance(ffn_config, fbw_model.DeepSeekV2MoESpec)
            and activation_checkpointing
            and gradient_accumulation_steps == 4
        )
    ):
        pytest.skip("Run with --slow to enable")
    vocab_size = 4
    model_args = fbw_model.ModelArgs(
        dim=8,
        n_layers=2,
        n_heads=0,
        n_kv_heads=0,
        vocab_size=vocab_size,
        ffn_type="",
        bias="",
        norm_type="layernorm",
        rotary_config=rotary_config,
        attn_config=attn_config,
        first_layer_ffn_config=fbw_model.SwiGLUSpec(
            hidden_dim=8, intermediate_size=16, bias=True
        ),
        ffn_config=ffn_config,
        max_seq_len=128,
        use_sequence_parallel=sequence_parallel,
        use_activation_checkpointing=activation_checkpointing,
    )
    _test_with_model_args(
        model_args,
        gradient_accumulation_steps,
        vocab_size=vocab_size,
        run_all_cases=slow_is_enabled,
    )


@pytest.mark.parametrize("activation_checkpointing", [True, False])
@pytest.mark.parametrize("sequence_parallel", [True, False])
@pytest.mark.parametrize("n_kv_heads", [0, 1, 2])
@pytest.mark.parametrize("ffn_type", ["glu", "mlp"])
@pytest.mark.parametrize("gradient_accumulation_steps", [1, 4])
def test_data_and_model_parallel_match_single_gpu(
    slow_is_enabled: bool,  # Fixture from conftest indicating presence of --slow
    ffn_type: str,
    n_kv_heads: int,
    sequence_parallel: bool,
    activation_checkpointing: bool,
    gradient_accumulation_steps: int,
):
    """Tests for gradient consistency under all model-/data-parallel conditions."""
    if not (
        slow_is_enabled
        or (
            ffn_type,
            n_kv_heads,
            sequence_parallel,
            activation_checkpointing,
            gradient_accumulation_steps,
        )
        in _CI_TEST_CASES
    ):
        pytest.skip("Run with --slow to enable")
    vocab_size = 4
    model_args = fbw_model.ModelArgs(
        dim=8,
        n_layers=2,
        n_heads=4,
        n_kv_heads=n_kv_heads,
        vocab_size=vocab_size,
        ffn_type=ffn_type,
        bias="attn_mlp",
        norm_type="layernorm",
        use_sequence_parallel=sequence_parallel,
        use_activation_checkpointing=activation_checkpointing,
    )
    _test_with_model_args(
        model_args,
        gradient_accumulation_steps,
        vocab_size=vocab_size,
        run_all_cases=slow_is_enabled,
    )


def _test_with_model_args(
    model_args: fbw_model.ModelArgs,
    gradient_accumulation_steps: int,
    vocab_size: int,
    seqlen: int = 4,
    rtol: float = 1e-3,
    atol: float = 1e-3,
    run_all_cases: bool = True,
):
    batch = 8 * gradient_accumulation_steps
    # For KV parallel size calculation
    if model_args.attn_config is None:
        real_n_kv_heads = (
            model_args.n_kv_heads if model_args.n_kv_heads else model_args.n_heads
        )
    elif isinstance(model_args.attn_config, fbw_model.GenericAttnSpec):
        real_n_kv_heads = (
            model_args.attn_config.n_kv_heads
            if model_args.attn_config.n_kv_heads
            else model_args.attn_config.n_heads
        )
    elif isinstance(model_args.attn_config, fbw_model.DeepSeekV2MLASpec):
        real_n_kv_heads = None
    else:
        raise NotImplementedError(
            f"Does not support test with {model_args.attn_config}"
        )
    data = torch.randint(
        vocab_size, (batch, seqlen + 1), dtype=torch.int64, device="cpu"
    )
    x = data[:, :-1].contiguous()
    y = data[:, 1:].contiguous()
    with tempfile.TemporaryDirectory() as tmpdir_name:
        tmpdir = Path(tmpdir_name)
        # Step 1: create a model and save its weights to tmpdir
        with distributed_runner(world_size=1, timeout_s=120.0) as runner:
            runner.run(_create_model, tmpdir, model_args)
        # Step 2: run the model on a single GPU and save its grads to tmpdir
        runner_args = (
            _run_model,
            tmpdir,
            model_args,
            x,
            y,
            torch.float32,
            gradient_accumulation_steps,
        )
        with distributed_runner(world_size=1, timeout_s=120.0) as runner:
            # NOTE: we _always_ use gradacc=1 for the reference grads
            runner.run(_run_model, tmpdir, model_args, x, y, torch.float32, 1, "DP1MP1")
        ref_params = torch.load(tmpdir / "DP1MP1-00p.pth", map_location="cpu")
        ref_grads = torch.load(tmpdir / "DP1MP1-00g.pth", map_location="cpu")

        # Now start testing by comparing those grads to five different conditions.
        # Start with the "hardest" case
        # Condition 1: 2x data-parallel, 4x model-parallel
        # If `run_all_cases` is False, then this is the only case we run.
        with distributed_runner(
            world_size=8,
            model_parallel_size=4,
            model_parallel_subgroups={"kv": 4 // real_n_kv_heads}
            if real_n_kv_heads is not None and real_n_kv_heads < 4
            else None,
            timeout_s=240.0,
        ) as runner:
            runner.run(*runner_args, "DP2MP4")

        for dp_rank in range(2):
            param_sds = [
                torch.load(tmpdir / f"DP2MP4-{dp_rank}{i}p.pth", map_location="cpu")
                for i in range(4)
            ]
            params = merge_model_parallel_consolidated_checkpoints(
                model_args, param_sds
            )
            _assert_params_match(params, ref_params, label=f"DP2MP4-DR{dp_rank}")

            grad_sds = [
                torch.load(tmpdir / f"DP2MP4-{dp_rank}{i}g.pth", map_location="cpu")
                for i in range(4)
            ]
            grads = merge_model_parallel_consolidated_checkpoints(model_args, grad_sds)
            _assert_grads_match(
                grads, ref_grads, label=f"DP2MP4-DR{dp_rank}", rtol=rtol, atol=atol
            )
        if not run_all_cases:
            return

        # Condition 2: 2x data-parallel, 1x model-parallel
        with distributed_runner(world_size=2, timeout_s=120.0) as runner:
            runner.run(*runner_args, "DP2MP1")
        for dp_rank in range(2):
            params = torch.load(tmpdir / f"DP2MP1-{dp_rank}0p.pth", map_location="cpu")
            _assert_params_match(params, ref_params, label=f"DP2MP1-DR{dp_rank}")
            grads = torch.load(tmpdir / f"DP2MP1-{dp_rank}0g.pth", map_location="cpu")
            _assert_grads_match(
                grads,
                ref_grads,
                label=f"DP2MP1-DR{dp_rank}",
            )

        # Condition 3: 1x data-parallel, 2x model-parallel
        with distributed_runner(
            world_size=2,
            model_parallel_size=2,
            model_parallel_subgroups={"kv": 2} if real_n_kv_heads == 1 else None,
            timeout_s=180.0,
        ) as runner:
            runner.run(*runner_args, "DP1MP2")

        param_sds = [
            torch.load(tmpdir / f"DP1MP2-0{i}p.pth", map_location="cpu")
            for i in range(2)
        ]
        params = merge_model_parallel_consolidated_checkpoints(model_args, param_sds)
        _assert_params_match(params, ref_params, label="DP1MP2")

        grad_sds = [
            torch.load(tmpdir / f"DP1MP2-0{i}g.pth", map_location="cpu")
            for i in range(2)
        ]
        grads = merge_model_parallel_consolidated_checkpoints(model_args, grad_sds)
        _assert_grads_match(grads, ref_grads, label="DP1MP2", rtol=rtol, atol=atol)

        # Condition 4: 2x data-parallel, 2x model-parallel
        with distributed_runner(
            world_size=4,
            model_parallel_size=2,
            model_parallel_subgroups={"kv": 2} if real_n_kv_heads == 1 else None,
            timeout_s=180.0,
        ) as runner:
            runner.run(*runner_args, "DP2MP2")

        for dp_rank in range(2):
            param_sds = [
                torch.load(tmpdir / f"DP2MP2-{dp_rank}{i}p.pth", map_location="cpu")
                for i in range(2)
            ]
            params = merge_model_parallel_consolidated_checkpoints(
                model_args, param_sds
            )
            _assert_params_match(params, ref_params, label=f"DP2MP2-DR{dp_rank}")

            grad_sds = [
                torch.load(tmpdir / f"DP2MP2-{dp_rank}{i}g.pth", map_location="cpu")
                for i in range(2)
            ]
            grads = merge_model_parallel_consolidated_checkpoints(model_args, grad_sds)
            _assert_grads_match(
                grads, ref_grads, label=f"DP2MP2-DR{dp_rank}", rtol=rtol, atol=atol
            )

        # Condition 5: 1x data-parallel, 4x model-parallel
        with distributed_runner(
            world_size=4,
            model_parallel_size=4,
            model_parallel_subgroups={"kv": 4 // real_n_kv_heads}
            if real_n_kv_heads is not None and real_n_kv_heads < 4
            else None,
            timeout_s=180.0,
        ) as runner:
            runner.run(*runner_args, "DP1MP4")

        param_sds = [
            torch.load(tmpdir / f"DP1MP4-0{i}p.pth", map_location="cpu")
            for i in range(4)
        ]
        params = merge_model_parallel_consolidated_checkpoints(model_args, param_sds)
        _assert_params_match(params, ref_params, label="DP1MP4")

        grad_sds = [
            torch.load(tmpdir / f"DP1MP4-0{i}g.pth", map_location="cpu")
            for i in range(4)
        ]
        grads = merge_model_parallel_consolidated_checkpoints(model_args, grad_sds)
        _assert_grads_match(grads, ref_grads, label="DP1MP4", rtol=rtol, atol=atol)


def _assert_params_match(
    actual_params: dict[str, torch.Tensor],
    ref_params: dict[str, torch.Tensor],
    label: str = "",
):
    not_matching = []
    for name, ref_param in ref_params.items():
        actual_param = actual_params[name]
        if not (actual_param == ref_param).all():
            not_matching.append(name)
    if not_matching:
        print(f"{label} parameters not matching")
        for name in not_matching:
            print(name)
    assert not_matching == []


@torch.no_grad()
def _assert_grads_match(
    actual_grads: dict[str, torch.Tensor],
    ref_grads: dict[str, torch.Tensor],
    label: str,
    rtol: float = 1e-3,
    atol: float = 1e-3,
):
    not_matching = []
    for name, ref_grad in ref_grads.items():
        actual_grad = actual_grads[name]
        if not torch.allclose(
            actual_grad,
            ref_grad,
            rtol=rtol,
            atol=atol,
        ):
            max_diff = (actual_grad - ref_grad).abs().max().item()
            avg_diff = (actual_grad - ref_grad).abs().mean().item()
            not_matching.append(
                f"{name}: max diff={max_diff:.6f}: avg diff={avg_diff:.6f}"
            )

    if not_matching:
        print(f"{label} gradients not matching")
        for name in not_matching:
            print(name)
    assert (
        not_matching == []
    ), f"{label} gradients not matching: {', '.join(not_matching)}"


def _create_model(tmpdir, model_args: fbw_model.ModelArgs):
    """Creates a model with `model_args` and saves to `tmpdir`.

    Provides a way for multiple calls to `_run_model` to have the same weights.
    """
    model = fbw_model.Transformer(model_args, skip_init=False)
    checkpointer = checkpointing.CheckpointManager()
    checkpointer.save_state_dict_checkpoint(
        tmpdir,
        model.state_dict(),
        fbw_model.ModelArgs.schema().dump(model_args),
    )


def _run_model(
    ckptdir, model_args, x, y, model_dtype, gradient_accumulation_steps, result_prefix
):
    """Loads the model at `ckptdir`, runs fwd/bwd, and saves the grads to `ckptdir`."""
    model = fbw_model.Transformer(model_args, skip_init=False)
    checkpointer = checkpointing.CheckpointManager(mp_world_size=1, mp_rank=0)
    state_dict = checkpointer.load_state_dict_checkpoint(ckptdir)
    if mpu.get_model_parallel_world_size() > 1:
        # Need to split the checkpoint
        state_dict = split_consolidated_checkpoint(
            model_args,
            state_dict,
            mpu.get_model_parallel_world_size(),
            mpu.get_model_parallel_rank(),
        )
    model.load_state_dict(state_dict)
    model.to(device=torch.device("cuda"), dtype=model_dtype)
    model.train()

    _, flat_model_state = fbw_model.configure_fsdp_optimizer(
        model, weight_decay=1.0, learning_rate=0.1, betas=(0.9, 0.999)
    )

    total_batch = x.size(0)
    assert total_batch % mpu.get_data_parallel_world_size() == 0
    per_dp_batch = total_batch // mpu.get_data_parallel_world_size()
    assert per_dp_batch % gradient_accumulation_steps == 0
    per_dp_micro_batch = per_dp_batch // gradient_accumulation_steps
    batch_start = per_dp_batch * mpu.get_data_parallel_rank()
    batch_end = batch_start + per_dp_batch
    x = x[batch_start:batch_end, :].clone().cuda()
    y = y[batch_start:batch_end, :].clone().cuda()

    for micro_step in range(gradient_accumulation_steps):
        start = per_dp_micro_batch * micro_step
        end = start + per_dp_micro_batch
        x_micro = x[start:end, :]
        y_micro = y[start:end, :]
        loss = (
            losses.cross_entropy_loss(x_micro, y_micro, model(x_micro))
            / gradient_accumulation_steps
        )
        loss.backward()
    dist.all_reduce(
        flat_model_state.grads(),
        group=mpu.get_data_parallel_group(),
        op=dist.ReduceOp.SUM,
    )
    # We can't use an AVG all_reduce because of gloo, so manually divide instead
    flat_model_state.grads().div_(mpu.get_data_parallel_world_size())

    torch.save(
        model.state_dict(),
        ckptdir
        / (
            f"{result_prefix}"
            f"-{mpu.get_data_parallel_rank()}{mpu.get_model_parallel_rank()}p.pth"
        ),
    )

    grad_dict = {}
    for n, p in model.named_parameters():
        assert p.grad is not None
        grad_dict[n] = p.grad
    torch.save(
        grad_dict,
        ckptdir
        / (
            f"{result_prefix}"
            f"-{mpu.get_data_parallel_rank()}{mpu.get_model_parallel_rank()}g.pth"
        ),
    )

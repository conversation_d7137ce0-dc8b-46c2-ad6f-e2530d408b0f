"""Tests for fastbackward attention."""

from unittest.mock import patch
import pytest
import torch

from research.fastbackward import attention


@pytest.mark.parametrize("random_seed", list(range(3)))
@pytest.mark.parametrize("dim", [128, 256])
@pytest.mark.parametrize("kv_head_count", [1, 4, 8])
@pytest.mark.parametrize("head_count", [8, 16])
@pytest.mark.parametrize("seq_len", [1, 512, 2048, 8192])
@pytest.mark.parametrize("batch_size", [1, 3])
def test_bf16_fp32_flash_attention_close(
    batch_size: int,
    seq_len: int,
    head_count: int,
    kv_head_count: int,
    dim: int,
    random_seed: int,
):
    torch.manual_seed(random_seed)
    torch.cuda.manual_seed(random_seed)
    flash_attn = attention.FusedAttn()

    q = torch.randn(batch_size, seq_len, head_count, dim, device="cuda")
    k = torch.randn(batch_size, seq_len, kv_head_count, dim, device="cuda")
    v = torch.randn(batch_size, seq_len, kv_head_count, dim, device="cuda")
    out = flash_attn(q, k, v)

    q_bf16 = q.to(torch.bfloat16)
    k_bf16 = k.to(torch.bfloat16)
    v_bf16 = v.to(torch.bfloat16)
    out_bf16 = flash_attn(q_bf16, k_bf16, v_bf16)

    diff = (out - out_bf16).abs().max().item()
    assert diff < 3e-2  # It is actually this bad


@pytest.mark.parametrize("dim", [128, 256])
@pytest.mark.parametrize("kv_head_count", [1, 4, 8])
@pytest.mark.parametrize("head_count", [8, 16])
@pytest.mark.parametrize("seq_len", [1, 128])
@pytest.mark.parametrize("batch_size", [1, 3])
def test_attn_mask(
    batch_size: int,
    seq_len: int,
    head_count: int,
    kv_head_count: int,
    dim: int,
):
    flash_attn = attention.FusedAttn()

    q = torch.randn(batch_size, seq_len, head_count, dim, device="cuda")
    k = torch.randn(batch_size, seq_len, kv_head_count, dim, device="cuda")
    v = torch.randn(batch_size, seq_len, kv_head_count, dim, device="cuda")

    float_attn_mask = torch.rand(
        [batch_size, head_count, seq_len, seq_len], dtype=torch.float32, device="cuda"
    )
    bool_attn_mask = float_attn_mask > 0.5

    for mask in [None, bool_attn_mask, float_attn_mask]:
        with patch(
            "torch.nn.functional.scaled_dot_product_attention"
        ) as mock_scaled_dot_product_attention:
            flash_attn(q, k, v, mask=mask)
            assert mock_scaled_dot_product_attention.call_count == 1
            _, kwargs = mock_scaled_dot_product_attention.call_args

            if mask is None:
                assert kwargs["attn_mask"] is None
                assert kwargs["is_causal"] is True
            else:
                assert (kwargs["attn_mask"] == mask).all()
                assert kwargs["is_causal"] is False

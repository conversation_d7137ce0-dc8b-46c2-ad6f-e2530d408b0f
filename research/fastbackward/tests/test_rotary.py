"""Tests for fastbackward.rotary."""

import pytest
import torch

from base.fastforward.positional_embeddings import (
    DeepSeekV1ExtensionConfig,
    RotaryConfig,
)
from research.fastbackward import rotary


def precompute_freqs_cis(
    dim: int, end: int, theta: float = 10000.0, scaling_factor: float = 1.0
):
    freqs = 1.0 / (
        theta ** (torch.arange(0, dim, 2, dtype=torch.float32)[: (dim // 2)] / dim)
    )
    t = torch.arange(end, dtype=torch.float32, device=freqs.device)  # type: ignore
    t = t / scaling_factor
    freqs = torch.outer(t, freqs).float()  # type: ignore
    freqs_cis = torch.polar(torch.ones_like(freqs), freqs)  # complex64
    return freqs_cis


def reshape_for_broadcast(freqs_cis: torch.Tensor, x: torch.Tensor):
    ndim = x.ndim
    assert 0 <= 1 < ndim
    assert freqs_cis.shape == (x.shape[1], x.shape[-1])
    shape = [d if i == 1 or i == ndim - 1 else 1 for i, d in enumerate(x.shape)]
    return freqs_cis.view(*shape)


# This function (and the two above) are the reference implementation of RoPE from
# `facebookresearch/llama` based on complex arithmetic.
def llama_apply_rotary_emb(
    x: torch.Tensor,
    freqs_cis: torch.Tensor,
) -> torch.Tensor:
    x_ = torch.view_as_complex(x.float().reshape(*x.shape[:-1], -1, 2))
    freqs_cis = reshape_for_broadcast(freqs_cis, x_)
    xq_out = torch.view_as_real(x_ * freqs_cis).flatten(3)
    return xq_out.type_as(x)


@pytest.mark.parametrize("batch_size", [1, 4])
@pytest.mark.parametrize("nheads", [1, 16])
@pytest.mark.parametrize("headdim", [32, 128])
@pytest.mark.parametrize("scaling_factor", [1.0, 4.0])
@pytest.mark.parametrize("start_pos", [0, 100])
@pytest.mark.parametrize("seqlen", [128, 4096])
@pytest.mark.parametrize(
    "dtype",
    [torch.bfloat16, torch.float32],
)
def test_rotary_embed(
    batch_size: int,
    nheads: int,
    headdim: int,
    scaling_factor: float,
    start_pos: int,
    seqlen: int,
    dtype: torch.dtype,
):
    x = torch.randn(
        (batch_size, seqlen - start_pos, nheads, headdim), dtype=dtype, device="cuda"
    )
    rotary_freqs = (
        rotary.precompute_rotary_freqs(
            headdim,
            seqlen,
            RotaryConfig(
                1.0,
                10000.0,
                max_position_embeddings=seqlen,
                ext_config=DeepSeekV1ExtensionConfig(
                    rotary_scaling_factor=scaling_factor
                ),
            ),
        )
        .slice(start_pos, seqlen)
        .to("cuda")
    )
    ours = rotary.rotary_embed(x, rotary_freqs.cos, rotary_freqs.sin)

    freqs_cis = precompute_freqs_cis(headdim, seqlen, scaling_factor=scaling_factor).to(
        "cuda"
    )[start_pos:seqlen]
    reference = llama_apply_rotary_emb(x, freqs_cis)
    assert ours.dtype == reference.dtype
    if dtype == torch.bfloat16:
        assert torch.allclose(ours, reference, atol=1e-3, rtol=1e-2)
    else:
        assert torch.allclose(ours, reference, atol=1e-6, rtol=1e-4)

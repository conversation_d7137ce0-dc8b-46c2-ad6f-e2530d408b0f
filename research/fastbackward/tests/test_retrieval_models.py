"""Tests for fastbackward.retrieval_models.

pytest research/fastbackward/tests/test_retrieval_models.py
"""

import logging
from typing import Callable

import pytest
import torch
from tensordict.tensordict import TensorDict
from torch.utils.data import DataLoader

from research.fastbackward import distributed, losses, retrieval_data
from research.fastbackward import fs_model_parallel as mpu
from research.fastbackward.model import (
    ModelArgs,
    Transformer,
    configure_fsdp_optimizer,
)
from research.fastbackward.retrieval_models import (
    DualEncoderModel,
    PerplexityLoss,
    RankingMetrics,
    TransformerEmbedder,
    compute_ranking_metrics,
    compute_ranking_metrics_from_ranking,
    create_checkpoint,
    load_checkpoint_from_configs,
    perplexity_loss,
    reduce_retrieval_metrics_dp,
)
from research.fastbackward.tests.fake_distributed_runner import distributed_runner
from research.fastbackward.training import TrainOptions, train_loop

logger = logging.getLogger(__name__)

# Each test function below is actually run with the distributed runner below. They are
# marked private to prevent pytest from running them directly.

QUERY_TOKEN_ID = 0
DOCUMENT_TOKEN_ID = 15
PAD_TOKEN_ID = 127


def create_tied_starcoder(lm_dim: int, emb_dim: int):
    """Create a tied StarCoder dual encoder model."""
    mpu.initialize_model_parallel_subgroup("kv", mpu.get_model_parallel_world_size())
    lm = Transformer(
        ModelArgs(
            dim=lm_dim,
            n_layers=2,
            n_heads=4,
            n_kv_heads=1,
            max_seq_len=32,
            vocab_size=128,
            skip_output=True,
            ffn_type="mlp",
            bias="attn_mlp",
            norm_type="layernorm",
            pos_embed_type="absolute",
        )
    )
    query_model = TransformerEmbedder(
        lm,
        TransformerEmbedder.Config(
            input_dim=lm_dim,
            output_projection_dim=emb_dim,
            with_output_bias=True,
        ),
    )
    return DualEncoderModel(
        query_model,
        query_model,
        DualEncoderModel.Config(
            query_token_id=QUERY_TOKEN_ID,
            document_token_id=DOCUMENT_TOKEN_ID,
        ),
    )


def create_two_tower_starcoder(lm_dim: int, emb_dim: int, freeze_document_model: bool):
    """Create a StarCoder dual encoder model with separate query and document towers."""
    mpu.initialize_model_parallel_subgroup("kv", mpu.get_model_parallel_world_size())
    lm = Transformer(
        ModelArgs(
            dim=lm_dim,
            n_layers=2,
            n_heads=4,
            n_kv_heads=1,
            max_seq_len=32,
            vocab_size=128,
            skip_output=True,
            ffn_type="mlp",
            bias="attn_mlp",
            norm_type="layernorm",
            pos_embed_type="absolute",
        )
    )
    query_model = TransformerEmbedder(
        lm,
        TransformerEmbedder.Config(
            input_dim=lm_dim,
            output_projection_dim=emb_dim,
            with_output_bias=True,
        ),
    )
    doc_model = TransformerEmbedder(
        lm,
        TransformerEmbedder.Config(
            input_dim=lm_dim,
            output_projection_dim=emb_dim,
            with_output_bias=True,
        ),
    )
    return DualEncoderModel(
        query_model,
        doc_model,
        DualEncoderModel.Config(
            query_token_id=QUERY_TOKEN_ID,
            document_token_id=DOCUMENT_TOKEN_ID,
            freeze_document_model=freeze_document_model,
        ),
    )


def smoke_test_dual_encoder_model(model: torch.nn.Module, D: int, Lq: int, Ld: int):
    """Smoke test for a model."""
    logger.info("Training model: %s", model)
    my_device = distributed.get_local_device()

    # NOTE(arun): We use FP16 here because it is the only data type supported by Gloo
    # AND flash attention. In practice, always use bfloat16.
    model.to(dtype=torch.float16, device=my_device)
    B, K = 2, 3
    # To mock data for this test, we take ranges that ensure the query token and
    # document tokens are always in the token seqeunces. This is sneaky and here are
    # some assertions that will fail early if the assumptions are violated.
    assert K < Ld, f"The test data assumes that {K=} < {Ld=}."
    assert (
        Ld > DOCUMENT_TOKEN_ID
    ), f"The test data assumes that {Ld=} > {DOCUMENT_TOKEN_ID=}."
    # Identical query tokens in each batch element
    X = TensorDict(
        {
            "query_tokens_BLq": torch.arange(0, Lq).expand(B, -1),
            "doc_tokens_BKLd": torch.vstack(
                [torch.arange(0, Ld) + i for i in range(K)],
            ).expand(B, K, -1),
            # NOTE(arun): Adding this into the tensor batch here to simplify the collate
            # fn below.
            "labels_BK": torch.tensor([-100.0, 1.0, 1.0], device=my_device).expand(
                B, K
            ),
            "labels_mask_BK": torch.ones(B, K, dtype=torch.bool, device=my_device),
        },
        batch_size=[B],
        device=my_device,
    )
    # Mask out one document for this test.
    X["labels_mask_BK"][-1, -1] = False
    labels_BK = X["labels_BK"]

    prediction_batch = model(X)
    query_emb_BD = prediction_batch["query_emb_BD"]
    doc_emb_BKD = prediction_batch["doc_emb_BKD"]
    assert query_emb_BD.shape == (B, D)
    assert doc_emb_BKD.shape == (B, K, D)

    # To get loss to actually go down, it's important that pred_temperature is large
    # enough.
    loss_fn_module = PerplexityLoss(
        PerplexityLoss.Config(
            gold_temperature=1.0,
            pred_temperature=20.0,
            logits_scale=1.0,
            learnable_logits_scale=True,
        )
    ).to(device=my_device)
    loss_fn = losses.ModuleAsLossFn(loss_fn_module)

    loss = loss_fn_module(X, labels_BK, prediction_batch)
    assert loss.shape == ()
    assert loss.item() > 0

    collate_fn_ = retrieval_data.create_collate_retrieval_data_fn(
        DOCUMENT_TOKEN_ID, PAD_TOKEN_ID
    )

    # Wrap around our collate function above because the train loop expects a tuple.
    # TODO(arun): We should make this less hard-coded in the train loop.
    def collate_fn(batch):
        batch = collate_fn_(batch)
        # The 0 is the "number of masked tokens" expected in the train loop.
        return (batch, batch["labels_BK"], 0)

    train_loader = DataLoader(
        X,  # type: ignore
        batch_size=4,
        shuffle=False,
        collate_fn=collate_fn,
    )

    optimizer, fms = configure_fsdp_optimizer(
        model, loss_fn_module, weight_decay=0.1, learning_rate=0.1, betas=(0.9, 0.999)
    )
    for _ in train_loop(
        model,
        loss_fn=loss_fn,
        flat_model_state=fms,
        optimizer=optimizer,
        lr_schedule=lambda _: 1e-3,
        train_options=TrainOptions(
            batch_size=4,
            block_size=16,
            gradient_accumulation_steps=1,
            log_interval=1,
            max_iters=1,
        ),
        train_loader=train_loader,
    ):
        pass

    updated_loss = loss_fn_module(X, labels_BK, model(X))
    assert (
        updated_loss < loss
    ), f"Loss increased from {loss.item()=} to {updated_loss.item()=}"

    assert isinstance(model, DualEncoderModel), f"{model=} is not a DualEncoderModel."

    # Test that we can save a checkpoint for the model
    configs, state_dict = create_checkpoint(
        {
            "model": model,
            "loss_fn": loss_fn_module,
        }
    )
    logger.info(f"Finish creating the checkpoint: {configs}")
    # Test that we can load the checkpoint and everything is the same.
    components = load_checkpoint_from_configs(configs, state_dict)
    logger.info(f"Finish loading the checkpoint: {components}")
    new_model = components.get_with_type("model", torch.nn.Module)
    new_model.to(dtype=torch.float16, device=my_device)

    new_loss_fn = components.get_with_type("loss_fn", torch.nn.Module)
    new_loss_fn.to(device=my_device)

    torch.testing.assert_close(new_model(X), model(X))
    torch.testing.assert_close(
        loss_fn_module(X, labels_BK, model(X)),
        new_loss_fn(X, labels_BK, new_model(X)),
    )


def _test_starcoder_tied():
    """Smoke test for the StarCoder model."""
    lm_dim, emb_dim = 64, 32
    smoke_test_dual_encoder_model(
        create_tied_starcoder(lm_dim, emb_dim), emb_dim, Lq=16, Ld=16
    )


def _test_starcoder_tied_with_longer_queries():
    """Smoke test for the StarCoder model."""
    lm_dim, emb_dim = 64, 32
    smoke_test_dual_encoder_model(
        create_tied_starcoder(lm_dim, emb_dim), emb_dim, Lq=24, Ld=16
    )


def _test_starcoder_two_tower():
    """Smoke test for the StarCoder model."""
    lm_dim, emb_dim = 64, 32
    smoke_test_dual_encoder_model(
        create_two_tower_starcoder(lm_dim, emb_dim, freeze_document_model=False),
        emb_dim,
        Lq=24,
        Ld=16,
    )


def _test_starcoder_with_frozen_document_encoder():
    """Smoke test for the StarCoder model."""
    lm_dim, emb_dim = 64, 32
    smoke_test_dual_encoder_model(
        create_two_tower_starcoder(lm_dim, emb_dim, freeze_document_model=True),
        emb_dim,
        Lq=24,
        Ld=16,
    )


# NOTE(arun): We can only test with model parallel size 1 because the `gloo` backend
# does not support `all_gather` or `all_reduce`.
@pytest.mark.parametrize(
    "test_fn",
    [
        _test_starcoder_tied,
        _test_starcoder_tied_with_longer_queries,
        _test_starcoder_two_tower,
        _test_starcoder_with_frozen_document_encoder,
    ],
)
@pytest.mark.parametrize("data_parallel_size", [1])
@pytest.mark.parametrize("model_parallel_size", [1, 2])
def test_distributed(
    model_parallel_size: int,
    data_parallel_size: int,
    test_fn: Callable[[int, int], None],
):
    world_size = model_parallel_size * data_parallel_size
    with distributed_runner(
        world_size, model_parallel_size, timeout_s=30.0, debug_mode=False
    ) as runner:
        runner.run(test_fn)


def test_perplexity_loss():
    """Test perplexity loss.

    Here is the math to manually verify this test.
               Q           D
    emb_BD = [ 1 ] + [ -1  0  1 ]
             [ 1 ]   [  1  1  1 ]

    logits_BdD    = [    0       1       2 ]
    probs_hat_BdD = [ 0.09, 0.2447, 0.6652 ]
    labels_BdD    = [ -100       1       1 ]
    probs_BdD     = [ 0.00, 0.5000, 0.5000 ]
    loss          = 0.5 * (log (0.5) - log (0.2447)) + 0.5 * (log (0.5) - log (0.6652))
                  = 0.2145
    """
    my_device = distributed.get_local_device()
    B, K, D = 1, 3, 2
    labels_BK = torch.tensor([-100.0, 1.0, 1.0], device=my_device).reshape(B, K)
    prediction_batch = TensorDict(
        {
            "query_emb_BD": torch.tensor(
                [1, 1],
                dtype=torch.float32,
                device=my_device,
            ).reshape(B, D),
            "doc_emb_BKD": torch.tensor(
                [
                    # Doc vectors
                    [-1, 1],
                    [0, 1],
                    [1, 1],
                ],
                dtype=torch.float32,
                device=my_device,
            ).reshape(B, K, D),
        },
        batch_size=[B],
    )
    loss = perplexity_loss(
        TensorDict(
            {
                "labels_mask_BK": torch.ones(B, K, dtype=torch.bool, device=my_device),
            },
            batch_size=[B],
        ),
        labels_BK,
        prediction_batch,
        gold_temperature=1.0,
        pred_temperature=1.0,
    )
    assert loss.shape == ()
    torch.testing.assert_close(
        loss, torch.tensor(0.2145).to(loss), atol=1e-4, rtol=1e-3
    )


def test_perplexity_loss_with_masking():
    """Test perplexity loss with masking.

    Here is the math to manually verify this test.
    Batch entry 1 is equivalent to `test_perplexity_loss`, with loss = 0.2145.
    Batch entry 2 masks the 3rd document: and the loss is 0.

               Q           D
    emb_BD = [ 1 ] + [ -1  0  - ]
             [ 1 ]   [  1  1  - ]

    logits_BdD    = [    0       1       - ]
    probs_hat_BdD = [0.2689, 0.7311    -inf]
    labels_BdD    = [ -100       1       - ]
    probs_BdD     = [ 0.00, 1.0000         ]
    loss          = 1.0 * (log (1.0) - log (0.7311))
                  = 0.3132
    With batch mean: (0.2145 + 0.3132) / 2 = 0.26385
    """
    my_device = distributed.get_local_device()
    B, K, D = 2, 3, 2
    labels_BK = torch.tensor(
        [
            [-100.0, 1.0, 1.0],
            # The loss should respect the mask irrespective of what the label value is.
            [-100.0, 1.0, 1.0],
        ],
        device=my_device,
    ).reshape(B, K)
    prediction_batch = TensorDict(
        {
            "query_emb_BD": torch.tensor(
                [
                    [1, 1],
                    [1, 1],
                ],
                dtype=torch.float32,
                device=my_device,
            ).reshape(B, D),
            "doc_emb_BKD": torch.tensor(
                [
                    [
                        # Doc vectors
                        [-1, 1],
                        [0, 1],
                        [1, 1],
                    ],
                    [
                        # Doc vectors
                        [-1, 1],
                        [0, 1],
                        [0, 0],
                    ],
                ],
                dtype=torch.float32,
                device=my_device,
            ).reshape(B, K, D),
        },
        batch_size=[B],
    )
    loss = perplexity_loss(
        TensorDict(
            {
                "labels_mask_BK": torch.tensor(
                    [
                        [True, True, True],
                        [True, True, False],
                    ],
                    device=my_device,
                ),
            },
            batch_size=[B],
        ),
        labels_BK,
        prediction_batch,
        gold_temperature=1.0,
        pred_temperature=1.0,
    )
    assert loss.shape == ()
    torch.testing.assert_close(
        loss, torch.tensor((0.2145 + 0.3132) / 2).to(loss), atol=1e-4, rtol=1e-3
    )


def test_compute_retrieval_metrics():
    """Test standard retrieval metrics.

    Here is the math to manually verify this test.
    Batch entry 1:
               Q           D
    emb_BD    = [ 1 ] + [ -1  0  1 ]
                [ 1 ]   [  1  1  1 ]
    labels_BK     = [    1,   -100,      1 ]
    probs_hat_BK  = [ 0.09, 0.2447, 0.6652 ]
    ranking_BK    = [    2,      1,      0 ]

    Batch entry 2:

               Q           D
    emb_BD = [ 1 ] + [ -1  0  - ]
             [ 1 ]   [  1  1  - ]
    labels_BK     = [    1,   -100,    - ]
    probs_hat_BK  = [ 0.2689, 0.7311, -inf ]
    ranking_BK    = [    2,      0,    - ]

    Metrics:
        mean_rr = (1/1 + 1/2)/2 = 0.75
        mean_ap = ((1/1 + 2/3)/2 + 1/2)/2 = 0.6666
        mean_top_rank = (1 + 2)/2 = 1.5
        mean_bottom_rank = (2 + 2)/2 = 2
        mean_rank = ((1 + 2)/2 + 2)/2 = 1.75
        mean_p1 = (1 + 0)/2 = 0.5
        mean_gold_prob = ((0.6652 + 0.09)/2 + 0.2689)/2 = 0.3232
    """
    my_device = distributed.get_local_device()
    B, K, D = 2, 3, 2
    X = TensorDict(
        {
            "labels_mask_BK": torch.tensor(
                [
                    [True, True, True],
                    [True, True, False],
                ],
                device=my_device,
            ),
            "labels_BK": torch.tensor(
                [
                    [1.0, -100.0, 1.0],
                    [2.0, -100.0, -torch.inf],
                ],
                device=my_device,
            ),
        },
        batch_size=[B],
    )
    prediction_batch = TensorDict(
        {
            "query_emb_BD": torch.tensor(
                [
                    [1, 1],
                    [1, 1],
                ],
                dtype=torch.float32,
                device=my_device,
            ).reshape(B, D),
            "doc_emb_BKD": torch.tensor(
                [
                    [
                        # Doc vectors
                        [-1, 1],
                        [0, 1],
                        [1, 1],
                    ],
                    [
                        # Doc vectors
                        [-1, 1],
                        [0, 1],
                        [0, 0],
                    ],
                ],
                dtype=torch.float32,
                device=my_device,
            ).reshape(B, K, D),
        },
        batch_size=[B],
    )
    metrics = compute_ranking_metrics(X, prediction_batch)

    torch.testing.assert_close(metrics.mean_rr, 0.75)
    torch.testing.assert_close(metrics.mean_ap, 0.6666, atol=1e-4, rtol=1e-3)
    torch.testing.assert_close(metrics.mean_rank, 1.75)
    torch.testing.assert_close(metrics.mean_top_rank, 1.5)
    torch.testing.assert_close(metrics.mean_bottom_rank, 2.0)
    torch.testing.assert_close(metrics.mean_p1, 0.5)
    torch.testing.assert_close(metrics.mean_gold_prob, 0.3232, atol=1e-4, rtol=1e-3)
    torch.testing.assert_close(metrics.count, 2)


def _test_reduce_retrieval_metrics_dp():
    assert mpu.get_data_parallel_world_size() == 2

    if mpu.get_data_parallel_rank() == 0:
        metrics = RankingMetrics(
            mean_rr=0.5,
            mean_ap=0.5,
            mean_rank=2,
            mean_top_rank=3,
            mean_bottom_rank=8,
            mean_p1=0.5,
            mean_gold_prob=0.5,
            mean_recall=0.5,
            mean_hard_recall=0.5,
            count=2,
        )
    else:
        metrics = RankingMetrics(
            mean_rr=1.0,
            mean_ap=1.0,
            mean_rank=1,
            mean_top_rank=2,
            mean_bottom_rank=4,
            mean_p1=1.0,
            mean_gold_prob=1.0,
            mean_recall=1.0,
            mean_hard_recall=1.0,
            count=1,
        )
    logger.info("Unaggregated metrics: %s", metrics)
    reduced_metrics = reduce_retrieval_metrics_dp(metrics)
    logger.info("Reduced metrics: %s", reduced_metrics)

    torch.testing.assert_close(reduced_metrics.mean_rr, (0.5 * 2 + 1.0) / 3)
    torch.testing.assert_close(reduced_metrics.mean_ap, (0.5 * 2 + 1.0) / 3)
    torch.testing.assert_close(reduced_metrics.mean_rank, (2 * 2 + 1) / 3)
    torch.testing.assert_close(reduced_metrics.mean_top_rank, (2 * 3 + 2) / 3)
    torch.testing.assert_close(reduced_metrics.mean_bottom_rank, (2 * 8 + 4) / 3)
    torch.testing.assert_close(reduced_metrics.mean_p1, (0.5 * 2 + 1.0) / 3)
    torch.testing.assert_close(reduced_metrics.mean_gold_prob, (0.5 * 2 + 1.0) / 3)
    torch.testing.assert_close(reduced_metrics.mean_recall, (0.5 * 2 + 1.0) / 3)
    torch.testing.assert_close(reduced_metrics.mean_hard_recall, (0.5 * 2 + 1.0) / 3)
    torch.testing.assert_close(reduced_metrics.count, 3)


def test_reduce_retrieval_metrics_dp():
    """Test reducing retrieval metrics across the data parallel group."""
    with distributed_runner(2, 1, timeout_s=30.0) as runner:
        runner.run(_test_reduce_retrieval_metrics_dp)


def test_compute_retrieval_metrics_from_ranking_with_missing():
    """Test standard retrieval metrics.

    Here is the math to manually verify this test.
    probs_hat_K  = [ 0.09, 0.2447, 0.6652 ]
    labels_K     = [    1,   -100,      1 ]
    ranking_K    = [    2,      1,      0 ]
    missing_count = 1

    Metrics:
        mean_rr = 1/1
        mean_ap = (1/1 + 2/3 + 0)/3 = 5/9
        mean_top_rank = 1
        # Subtracting 1 because we discount the missing correct label.
        mean_rank = (1 + 3-1 + 4-2)/3 = 5/3
        mean_bottom_rank = 3 + 1 = 4
        mean_p1 = 1
        mean_gold_prob = (0.6652 + 0.09 + 0)/3 = 0.2517
    """
    metrics = compute_ranking_metrics_from_ranking(
        pred_lprobs_K=torch.log(torch.tensor([0.09, 0.2447, 0.6652])),
        pred_ranking_K=torch.tensor([2, 1, 0]),
        labels_K=torch.tensor([1.0, -100.0, 1.0]),
        missing_correct_count=1,
    )

    torch.testing.assert_close(metrics.mean_rr, 1.0)
    torch.testing.assert_close(metrics.mean_ap, 5 / 9, atol=1e-4, rtol=1e-3)
    torch.testing.assert_close(metrics.mean_top_rank, 1.0)
    torch.testing.assert_close(metrics.mean_rank, 5 / 3)
    torch.testing.assert_close(metrics.mean_bottom_rank, 4.0)
    torch.testing.assert_close(metrics.mean_p1, 1.0)
    torch.testing.assert_close(
        metrics.mean_gold_prob, (0.6652 + 0.09 + 0) / 3, atol=1e-4, rtol=1e-3
    )
    torch.testing.assert_close(metrics.count, 1)

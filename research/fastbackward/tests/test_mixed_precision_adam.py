"""Tests for fastbackward.mixed_precision_adam."""

import torch

from research.fastbackward.mixed_precision_adam import MixedPrecisionAdamW


def test_inverse_scale():
    sizes = [1000000, 1024, 37]
    params1 = [torch.randn(size, dtype=torch.bfloat16, device="cuda") for size in sizes]
    grads1 = [
        torch.randn(size, dtype=torch.bfloat16, device="cuda") * 0.1 for size in sizes
    ]
    params2 = [x.clone().detach() for x in params1]
    grads2 = [x.clone().detach() for x in grads1]
    options = [{} for _ in sizes]

    optimizer1 = MixedPrecisionAdamW(zip(params1, grads1, options))
    optimizer2 = MixedPrecisionAdamW(zip(params2, grads2, options))

    scale = torch.tensor(0.25, device="cuda")
    for grad in grads1:
        grad.mul_(scale)

    optimizer1.step()
    optimizer2.step(inverse_scale=scale)

    for p1, p2 in zip(params1, params2):
        assert torch.allclose(p1, p2)

    for pg1, pg2 in zip(optimizer1.param_groups, optimizer2.param_groups):
        for m1, m2 in zip(pg1["m"], pg2["m"]):
            assert torch.allclose(m1, m2)
        for v1, v2 in zip(pg1["v"], pg2["v"]):
            assert torch.allclose(v1, v2)
        for w321, w322 in zip(pg1["w32"], pg2["w32"]):
            assert torch.allclose(w321, w322)

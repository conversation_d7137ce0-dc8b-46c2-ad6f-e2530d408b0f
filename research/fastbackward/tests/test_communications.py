"""Tests to ensure the gradients are the same in all model-/data-parallel conditions."""

import tempfile
from pathlib import Path

import pytest
import torch
from torch import nn

from research.fastbackward import fs_model_parallel as mpu
from research.fastbackward.fs_model_parallel.layers import ColumnParallelLinear
from research.fastbackward.tests.fake_distributed_runner import distributed_runner


@pytest.mark.parametrize("repeat", range(3))
def test_column_parallel_linear_with_gather_output(repeat):
    """Tests for gradient consistency under all model-/data-parallel conditions."""
    del repeat  # Dummy variable for repeated random tests
    with tempfile.TemporaryDirectory() as tmpdir_name:
        tmpdir = Path(tmpdir_name)
        with distributed_runner(world_size=1, timeout_s=30.0) as runner:
            runner.run(_create_layer, tmpdir)

        x = torch.randn(5, 10)
        with distributed_runner(world_size=1, timeout_s=30.0) as runner:
            runner.run(_run_layer, tmpdir, x, 1, "DP1MP1")
        y_ref = torch.load(tmpdir / "DP1MP1_0.pth")

        with distributed_runner(
            world_size=2,
            model_parallel_size=2,
            timeout_s=30.0,
        ) as runner:
            runner.run(_run_layer, tmpdir, x, 2, "DP1MP2")

        y0 = torch.load(tmpdir / "DP1MP2_0.pth")
        y1 = torch.load(tmpdir / "DP1MP2_1.pth")
        torch.testing.assert_close(y0, y1)
        torch.testing.assert_close(y0, y_ref)
        torch.testing.assert_close(y1, y_ref)


def _create_layer(tmpdir):
    """Creates a model with `model_args` and saves to `tmpdir`.

    Provides a way for multiple calls to `_run_model` to have the same weights.
    """
    layer = nn.Linear(10, 10, bias=False)
    torch.save(layer.weight, tmpdir / "layer.pth")


def _run_layer(ckptdir, x, mp_size, label):
    """Loads the model at `ckptdir`, runs fwd/bwd, and saves the grads to `ckptdir`."""
    if mp_size > 1:
        weight = torch.load(ckptdir / "layer.pth")
        rank_weight = torch.chunk(weight, mp_size, dim=0)[mpu.get_model_parallel_rank()]
        layer = ColumnParallelLinear(
            10,
            10,
            bias=False,
            gather_output=True,
        )
        layer.weight = nn.Parameter(rank_weight)
    else:
        layer = nn.Linear(10, 10, bias=False)
        layer.weight = nn.Parameter(torch.load(ckptdir / "layer.pth"))
    y = layer(x)
    torch.save(
        y,
        ckptdir / f"{label}_{mpu.get_model_parallel_rank()}.pth",
    )

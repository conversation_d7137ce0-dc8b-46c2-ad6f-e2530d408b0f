"""Test utility to run with torch.distributed enabled even on one GPU."""

import contextlib
import traceback

import torch
import torch.distributed as dist
import torch.multiprocessing as mp

import research.fastbackward.fs_model_parallel as mpu
from research.fastbackward import logutils
from research.fastbackward.distributed import find_available_local_port
from research.fastbackward.tests.custom_triton_cache_manager import (
    maybe_set_triton_cache_manager,
)


@contextlib.contextmanager
def distributed_runner(
    world_size: int,
    model_parallel_size: int = 1,
    model_parallel_subgroups: dict[str, int] | None = None,
    timeout_s: float = 30.0,
    debug_mode: bool = False,
):
    """Context manager for running with torch.distributed and model parallel initialized.

    Args:
    - world_size: number of subprocesses to run (resulting distributed world size)
    - model_parallel_size: number of processes per model parallel group
    - timeout_s: timeout in seconds for each subprocess. NOTES:
      - This timeout is appleid both to each `run` call as well as context manager exit.
      - The point of this timeout is to prevent the parent process from hanging if one of
        the subprocesses crashes (quite common in testing!). Normally, the parent process
        blocks on each child completing, but if the child process dies, then it cannot send
        a "done" message. Without a timeout, the parent waits forever. Long-running test
        functions may require a longer timeout than the default of 10s.
    - debug_mode: If true, run the target run function in the same process. When
      set, we will not spawn multiple processes and so this will only work for a world
      size of 1. This mode is useful if you want to attach pdb to debug a failing test.

    Example:
    >>> # NOTE: the run fn must be defined on an actual module so it can be pickled. (Not a lambda, eg.)
    >>> def hello():
    >>>     print("hello from", dist.get_rank())
    >>> if __name__ == "__main__":
    >>>     with distributed_runner(world_size=4) as runner:
    >>>         runner.run(hello)

    Example (with debug mode):
    >>> def hello():
    >>>     import pdb; pdb.set_trace()
    >>>     print("hello from", dist.get_rank())
    >>> if __name__ == "__main__":
    >>>     with distributed_runner(world_size=1, debug_mode=True) as runner:
    >>>         runner.run(hello)
    """
    runner = FakeDistributedTestRunner(
        world_size,
        model_parallel_size,
        model_parallel_subgroups,
        timeout_s=timeout_s,
        debug_mode=debug_mode,
    )
    try:
        runner.start()
        yield runner
    finally:
        runner.stop()


class FakeDistributedTestRunner:
    """Wrapper class to manage subprocesses / queues for the distributed environment.

    See `distributed_runner` context manager for usage.
    """

    def __init__(
        self,
        world_size: int,
        model_parallel_size: int = 1,
        model_parallel_subgroups: dict[str, int] | None = None,
        master_port: int = -1,
        timeout_s: float = 30.0,
        debug_mode: bool = False,
    ):
        assert not debug_mode or (
            world_size == 1
        ), f"{debug_mode=} only works when {world_size=} is 1"
        assert (
            world_size % model_parallel_size == 0
        ), f"{world_size=} must be divisible by {model_parallel_size=}"
        if world_size > 1:
            maybe_set_triton_cache_manager()

        self.model_parallel_subgroups = model_parallel_subgroups or {}

        self.model_parallel_size = model_parallel_size
        self.world_size = world_size
        self.procs = []
        self.send_qs = []
        self.recv_qs = []
        self.child_had_exception = False
        self.master_port = (
            find_available_local_port() if master_port < 0 else master_port
        )
        self.timeout_s = timeout_s
        self.debug_mode = debug_mode

    def start(self):
        if self.debug_mode:
            _setup_distributed(
                0,
                self.world_size,
                self.model_parallel_size,
                self.model_parallel_subgroups,
                self.master_port,
            )
            return

        ctx = mp.get_context("spawn")
        for rank in range(self.world_size):
            send_q = ctx.Queue()
            recv_q = ctx.Queue()
            proc = ctx.Process(
                target=_subproc_run_fn,
                args=(
                    rank,
                    self.world_size,
                    self.model_parallel_size,
                    self.model_parallel_subgroups,
                    self.master_port,
                    send_q,
                    recv_q,
                ),
                daemon=False,  # `torch.compile` happens in a child process, daemon processes cannot have children
            )
            proc.start()
            self.procs.append(proc)
            self.send_qs.append(send_q)
            self.recv_qs.append(recv_q)

    def stop(self):
        if self.debug_mode:
            return

        # If no child had an exception, we synchronize so they can all exit cleanly.
        # (If any _did_ have an exception, we don't bother and skip straight to `kill`.)
        if not self.child_had_exception:
            for q in self.send_qs:
                q.put(None)
            for q in self.recv_qs:
                proc_done = q.get(timeout=self.timeout_s)
                assert proc_done is None
        # NOTE: we use a done message and kill instead of join because of https://github.com/pytorch/pytorch/issues/115366
        # (See also research.fastbackward.inference.ParallelTransformerRunner)
        for proc in self.procs:
            proc.kill()

    def run(self, fn, *args):
        if self.debug_mode:
            fn(*args)
            return

        for q in self.send_qs:
            q.put((fn, *args))
        for q in self.recv_qs:
            run_complete = q.get(timeout=self.timeout_s)
            if run_complete is not None:
                # This means we got an exception back, so re-raise it
                self.child_had_exception = True
                exc, trace = run_complete
                raise RuntimeError(f"Child process had exception: {trace}") from exc


def _subproc_run_fn(
    rank: int,
    world_size: int,
    model_parallel_size: int,
    model_parallel_subgroups: dict[str, int],
    master_port: int,
    recv_q: mp.Queue,
    send_q: mp.Queue,
):
    _setup_distributed(
        rank, world_size, model_parallel_size, model_parallel_subgroups, master_port
    )

    while True:
        msg = recv_q.get()
        if msg is None:
            send_q.put(None)
            break
        fn, *args = msg
        try:
            fn(*args)
            send_q.put(None)
        except Exception as e:
            trace = traceback.format_exc()
            # Return the exception to the parent process for better debugging
            send_q.put((e, trace))
            raise e


def _setup_distributed(
    rank: int,
    world_size: int,
    model_parallel_size: int,
    model_parallel_subgroups: dict[str, int],
    master_port: int,
):
    my_device = min(rank, torch.cuda.device_count() - 1)
    torch.cuda.set_device(my_device)
    # Only the gloo backend supports GPU over-subscription
    backend = "gloo" if world_size > torch.cuda.device_count() else "nccl"

    if dist.GroupMember.WORLD is not None:
        # Ensure that we only initialize the world once. This case should only be
        # triggered in debug_mode when we setup the distributed environment in the
        # current process.
        assert dist.get_world_size() == world_size
        assert mpu.get_model_parallel_world_size() == model_parallel_size
        return

    dist.init_process_group(
        backend=backend,
        init_method=f"tcp://localhost:{master_port}",
        rank=rank,
        world_size=world_size,
    )
    mpu.initialize_model_parallel(model_parallel_size)
    for name, parallel_size in model_parallel_subgroups.items():
        mpu.initialize_model_parallel_subgroup(name, parallel_size)
    logutils.setup_logging(None)

"""Tests for the checkpointing utilities.

pytest research/fastbackward/tests/test_checkpointing_utils.py
"""

import pytest
import torch

from research.fastbackward.checkpointing.utils import (
    get_model_weights_mp_split,
    merge_model_parallel_consolidated_checkpoints,
    split_consolidated_checkpoint,
)
from research.fastbackward.model import ModelArgs


def _get_random_state_dict(model_args: ModelArgs) -> dict[str, torch.Tensor]:
    """Get a state dict with random weights for the given model args."""
    state_dict = {
        "tok_embeddings.weight": torch.rand(model_args.vocab_size, model_args.dim),
        "norm.weight": torch.rand(model_args.dim),
        "output.weight": torch.rand(model_args.dim, model_args.vocab_size),
        "layers.0.attention_norm.weight": torch.rand(model_args.dim),
        "layers.0.attention.wq.weight": torch.rand(model_args.dim, model_args.dim),
        "layers.0.attention.wk.weight": torch.rand(model_args.dim, model_args.dim),
        "layers.0.attention.wv.weight": torch.rand(model_args.dim, model_args.dim),
        "layers.0.attention.wo.weight": torch.rand(model_args.dim, model_args.dim),
        "layers.0.ffn_norm.weight": torch.rand(model_args.dim),
        "layers.0.feed_forward.w1.weight": torch.rand(model_args.dim, model_args.dim),
        "layers.0.feed_forward.w2.weight": torch.rand(model_args.dim, model_args.dim),
    }
    if model_args.ffn_type == "glu":
        state_dict["layers.0.feed_forward.w3.weight"] = torch.rand(
            model_args.dim, model_args.dim
        )
    if model_args.bias in ["attn_mlp"]:
        state_dict.update(
            {
                "layers.0.attention.wq.bias": torch.rand(model_args.dim),
                "layers.0.attention.wk.bias": torch.rand(model_args.dim),
                "layers.0.attention.wv.bias": torch.rand(model_args.dim),
                "layers.0.attention.wo.bias": torch.rand(model_args.dim),
                "layers.0.feed_forward.w1.bias": torch.rand(model_args.dim),
                "layers.0.feed_forward.w2.bias": torch.rand(model_args.dim),
            }
        )
        if model_args.ffn_type == "glu":
            state_dict["layers.0.feed_forward.w3.bias"] = torch.rand(model_args.dim)
    if model_args.norm_type == "layernorm":
        state_dict.update(
            {
                "norm.bias": torch.rand(model_args.dim),
                "layers.0.attention_norm.bias": torch.rand(model_args.dim),
                "layers.0.ffn_norm.bias": torch.rand(model_args.dim),
            }
        )
    if model_args.pos_embed_type == "absolute":
        state_dict["pos_embeddings.weight"] = torch.rand(
            model_args.max_seq_len, model_args.dim
        )
    return state_dict


@pytest.mark.parametrize("n_kv_heads", [0, 1, 2])
@pytest.mark.parametrize("pos_embed_type", ["absolute", "rope"])
@pytest.mark.parametrize("norm_type", ["layernorm", "rmsnorm"])
@pytest.mark.parametrize("bias", ["attn_mlp", "none"])
@pytest.mark.parametrize("ffn_type", ["glu", "mlp"])
@pytest.mark.parametrize("world_size", [1, 2, 4])
def test_split_and_merge_consolidated_checkpoint(
    world_size: int,
    ffn_type: str,
    bias: str,
    norm_type: str,
    pos_embed_type: str,
    n_kv_heads: int,
):
    """Round-trip test for checkpoint splitting and merging."""
    model_args = ModelArgs(
        n_layers=1,
        vocab_size=100,
        max_seq_len=150,
        dim=64,
        n_heads=8,
        n_kv_heads=n_kv_heads,
        ffn_type=ffn_type,
        bias=bias,
        norm_type=norm_type,
        pos_embed_type=pos_embed_type,
    )
    state_dict = _get_random_state_dict(model_args)
    split_state_dicts = [
        split_consolidated_checkpoint(model_args, state_dict, world_size, i)
        for i in range(world_size)
    ]
    merged_state_dict = merge_model_parallel_consolidated_checkpoints(
        model_args, split_state_dicts
    )
    for name, value in state_dict.items():
        assert name in merged_state_dict
        torch.testing.assert_close(
            value,
            merged_state_dict[name],
            rtol=1e-9,
            atol=1e-9,
            msg=(
                f"Parameter {name} not matching after split-and-merge.\n"
                f"Original shape: {value.shape}\n"
                f"Merged shape: {merged_state_dict[name].shape}"
            ),
        )


@pytest.mark.parametrize("n_kv_heads", [0, 1, 2])
@pytest.mark.parametrize("pos_embed_type", ["absolute", "rope"])
@pytest.mark.parametrize("norm_type", ["layernorm", "rmsnorm"])
@pytest.mark.parametrize("bias", ["attn_mlp", "none"])
@pytest.mark.parametrize("ffn_type", ["glu", "mlp"])
@pytest.mark.parametrize("world_size", [1, 2, 4])
def test_merge_and_split_checkpoints(
    world_size: int,
    ffn_type: str,
    bias: str,
    norm_type: str,
    pos_embed_type: str,
    n_kv_heads: int,
):
    """Round-trip test for checkpoint merging and splitting."""
    model_args = ModelArgs(
        n_layers=1,
        vocab_size=100,
        max_seq_len=150,
        dim=64,
        n_heads=8,
        n_kv_heads=n_kv_heads,
        ffn_type=ffn_type,
        bias=bias,
        norm_type=norm_type,
        pos_embed_type=pos_embed_type,
    )
    state_dicts = [_get_random_state_dict(model_args) for _ in range(world_size)]

    # Non-parallel weights must be the same across ranks.
    for name, dim, _ in get_model_weights_mp_split(model_args, world_size):
        if dim is None:
            for state_dict in state_dicts[1:]:
                state_dict[name] = state_dicts[0][name]

    merged_state_dict = merge_model_parallel_consolidated_checkpoints(
        model_args, state_dicts
    )
    split_state_dicts = [
        split_consolidated_checkpoint(model_args, merged_state_dict, world_size, i)
        for i in range(world_size)
    ]
    for state_dict, split_state_dict in zip(state_dicts, split_state_dicts):
        unmatches = []
        for name, value in state_dict.items():
            assert name in split_state_dict

            if not torch.allclose(
                value,
                split_state_dict[name],
                rtol=1e-9,
                atol=1e-9,
            ):
                unmatches.append(name)

        if len(unmatches) > 0:
            print(unmatches)
        assert len(unmatches) == 0

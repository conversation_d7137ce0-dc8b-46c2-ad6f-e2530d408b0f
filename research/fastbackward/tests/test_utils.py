"""Unit tests for research/fastbackward/utils.py."""

from research.fastbackward import utils


def test_flatten_dict():
    """Test that flatten_dict() works as expected."""
    nested = {
        "a": {
            "b": 1,
            "c": 2,
        },
        "d": 3,
    }
    flattened = utils.flatten_dict(nested)
    assert flattened == {
        "a.b": 1,
        "a.c": 2,
        "d": 3,
    }


def test_flatten_dict_with_delimiter():
    """Test that flatten_dict() works as expected."""
    nested = {
        "a": {
            "b": 1,
            "c": 2,
        },
        "d": 3,
    }
    flattened = utils.flatten_dict(nested, delimiter="/")
    assert flattened == {
        "a/b": 1,
        "a/c": 2,
        "d": 3,
    }


def test_unflatten_dict():
    """Test that unflatten_dict() works as expected."""
    flattened = {
        "a.b": 1,
        "a.c.d": 2,
        "d": 3,
    }
    nested = utils.unflatten_dict(flattened)
    assert nested == {
        "a": {
            "b": 1,
            "c": {
                "d": 2,
            },
        },
        "d": 3,
    }


def test_unflatten_dict_max_level():
    """Test that unflatten_dict() works as expected."""
    flattened = {
        "a.b": 1,
        "a.c.d": 2,
        "d": 3,
    }
    nested = utils.unflatten_dict(flattened, max_level=1)
    assert nested == {
        "a": {
            "b": 1,
            "c.d": 2,
        },
        "d": 3,
    }


def test_unflatten_dict_with_delimiter():
    """Test that unflatten_dict() works as expected."""
    flattened = {
        "a/b": 1,
        "a/c/d": 2,
        "d": 3,
    }
    nested = utils.unflatten_dict(flattened, delimiter="/")
    assert nested == {
        "a": {
            "b": 1,
            "c": {
                "d": 2,
            },
        },
        "d": 3,
    }


def test_parse_key_value():
    """Test that parse_key_value() works as expected."""
    args = [
        "--key1='value'",
        "--key2.int=3",
        "--key2.bool=False",
        "--key2.tuple=('hi', 'there')",
        "extra_arg",
    ]
    parsed = utils.parse_key_value_args(args)
    assert parsed == {
        "key1": "value",
        "key2": {
            "int": 3,
            "bool": False,
            "tuple": ("hi", "there"),
        },
    }


def test_combine_dict():
    """Test that combine_dict() works as expected."""
    base = {
        "a": {
            "b": 1,
            "c": 2,
        },
        "d": 3,
        "e": {
            "f": 3,
        },
    }
    update = {
        "a": {
            "b": 4,
            "d": 5,
        },
        "e~": 6,
    }
    combined = utils.combine_dict(base, update)
    assert combined == {
        "a": {
            "b": 4,
            "c": 2,
            "d": 5,
        },
        "d": 3,
        "e": 6,
    }


def test_flatten_dict_with_lists():
    """Test that flatten_dict() works as expected."""
    nested = {
        "a": {
            "b": [1, 3],
            "c": [{"d": 2}, {"d": 4}],
        },
        "e": 5,
    }
    flattened = utils.flatten_dict(nested)
    assert flattened == {
        "a.b.0": 1,
        "a.b.1": 3,
        "a.c.0.d": 2,
        "a.c.1.d": 4,
        "e": 5,
    }


def test_unflatten_dict_with_lists():
    """Test that flatten_dict() works as expected."""
    flattened = {
        "a.b.0": 1,
        "a.b.1": 3,
        "a.c.0.d": 2,
        "a.c.1.d": 4,
        "e": 5,
    }
    nested = utils.unflatten_dict(flattened)
    assert nested == {
        "a": {
            "b": [1, 3],
            "c": [{"d": 2}, {"d": 4}],
        },
        "e": 5,
    }


def test_flatten_dict_with_long_lists():
    """Test that flatten_dict() works as expected."""
    nested = {
        "a": {
            "b": list(range(100)),
        },
    }
    assert nested == utils.unflatten_dict(utils.flatten_dict(nested))

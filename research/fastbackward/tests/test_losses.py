"""Tests for the losses module.

pytest research/fastbackward/tests/test_losses.py
"""

import pytest
import torch

import research.fastbackward.fs_model_parallel as mpu
from research.fastbackward import training
from research.fastbackward.data import IGNORE_LABEL
from research.fastbackward.losses import (
    ChunkedCrossEntropyLoss,
    CrossEntropyLoss,
)
from research.fastbackward.model import ModelArgs, Transformer
from research.fastbackward.tests.fake_distributed_runner import distributed_runner


def _test_chunked_cross_entropy_loss():
    # Match seeds in MP group so params and random data match
    torch.manual_seed(1234 + mpu.get_data_parallel_rank())
    torch.cuda.random.manual_seed(1234 + mpu.get_data_parallel_rank())
    seqlen = 32
    model_args = ModelArgs(
        dim=64,
        n_layers=2,
        n_heads=4,
        n_kv_heads=0,
        vocab_size=128,
        multiple_of=64,
    )
    # Construct two models and copy params from the first
    m1 = Transformer(model_args).to(device="cuda", dtype=torch.float32)
    m2 = Transformer(model_args).to(device="cuda", dtype=torch.float32)
    for p1, p2 in zip(m1.parameters(), m2.parameters()):
        p2.data.copy_(p1.data)

    microbatch = 2
    gradacc = 2
    all_data1 = []
    all_data2 = []
    for _ in range(gradacc):
        data = torch.randint(0, 128, (microbatch, seqlen + 1))
        X = data[:, :-1].clone()
        Y = data[:, 1:].clone()
        mask = torch.rand((microbatch, seqlen)) < 0.3
        nmasked = mask.sum().item()
        Y[mask] = IGNORE_LABEL
        all_data1.append((X, Y, nmasked))
        all_data2.append((X.clone(), Y.clone(), nmasked))

    chunked_ce_loss = ChunkedCrossEntropyLoss(
        m2,
        chunk_size=seqlen // 4,
    )
    cross_entropy_loss = CrossEntropyLoss()

    loss1, _ = training.forward_backward(
        m1,
        cross_entropy_loss,
        iter(all_data1),
        gradacc,
    )
    loss2, _ = training.forward_backward(
        m2,
        chunked_ce_loss,
        iter(all_data2),
        gradacc,
        extra_model_train_args={"skip_output": True},
    )
    torch.testing.assert_close(loss1, loss2)
    for (n1, p1), (n2, p2) in zip(m1.named_parameters(), m2.named_parameters()):
        assert n1 == n2
        assert p1.grad is not None
        assert p2.grad is not None
        torch.testing.assert_close(p1.grad, p2.grad, rtol=1e-4, atol=1e-4)


@pytest.mark.parametrize("world_size", [1, 2, 4])
def test_chunked_cross_entropy_loss(world_size):
    with distributed_runner(
        world_size=world_size,
        model_parallel_size=min(world_size, 2),
        debug_mode=world_size == 1,
    ) as runner:
        runner.run(_test_chunked_cross_entropy_loss)

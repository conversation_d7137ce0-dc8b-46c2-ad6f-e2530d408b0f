# Similar to the llama_small.py model with MQA and a few changes to be consistent with the DeepSeek models:
# 1. rope_theta = 100K
# 2. model_vocab_size = 32256 to match DeepSeek Coder and Instruct tokenizers.
ffn_type = "glu"
bias = "none"
norm_type = "rmsnorm"
pos_embed_type = "rope"

n_layers = 8
n_heads = 16
n_kv_heads = 1
rope_theta = 100000.0
rope_scaling_factor = 1.0
model_vocab_size = 32256
n_embd = 1024
ffn_dim_multiple_of = 128
ffn_dim_multiplier = 1.0
norm_eps = 1e-6
model_parallel_size = 1

# See /mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Base
from base.fastforward import positional_embeddings
from research.fastbackward import model

ffn_type = "mlp"
norm_type = "rmsnorm"
pos_embed_type = "rope"

n_layers = 27
n_embd = 2048
# This aims to explicitly disable the deprecated way to specify the FFN layer.
ffn_type = ""
rotary_config = positional_embeddings.RotaryConfig(
    rotary_ratio=1.0,
    rotary_theta=10000.0,
    max_position_embeddings=163840,
    ext_config=positional_embeddings.YaRNExtensionConfig(
        rotary_scaling_factor=40.0,
        unscaled_max_position_embeddings=4096,
        beta_fast=32,
        beta_slow=1,
        mscale=0.707,
    ),
)
attn_config = model.DeepSeekV2MLASpec(
    hidden_dim=2048,
    num_heads=16,
    v_head_dim=128,
    q_lora_rank=None,
    kv_lora_rank=512,
    qk_rope_head_dim=64,
    qk_nope_head_dim=128,
    eps=1e-6,
    bias=False,
)
first_layer_ffn_config = model.SwiGLUSpec(
    hidden_dim=2048, intermediate_size=10944, bias=False
)
ffn_config = model.DeepSeekV2MoESpec(
    hidden_dim=2048,
    n_routed_experts=64,
    routed_scaling_factor=1.0,
    num_experts_per_token=6,
    intermediate_size=1408,
    n_shared_experts=2,
    topk_method="greedy",
    use_dense_moe=False,
)
norm_eps = 1e-6
block_size = 128000
model_vocab_size = 102400
model_parallel_size = 2
use_sequence_parallel = False

from research.fastbackward.model import GenericAttnSpec

# Qwen2.5 Coder 32B spec.
ffn_type = "glu"
bias = "none"
norm_type = "rmsnorm"
pos_embed_type = "rope"

n_layers = 64
n_heads = 40
n_kv_heads = 8
n_embd = 5120
ffn_dim_multiple_of = 256
ffn_dim_multiplier = 2.02

norm_eps = 1e-06
rope_theta = 1000000.0

attn_config = GenericAttnSpec(
    hidden_dim=5120,
    n_heads=40,
    n_kv_heads=8,
    norm_type="rmsnorm",
    pos_embed_type="rope",
    bias=False,
    qkv_bias=True,
)

# Vocab stuff
model_vocab_size = 152064
fim_middle_token_id = 151660
eot_token_id = 151643
pad_token_id = 151665
model_parallel_size = 4
use_sequence_parallel = True

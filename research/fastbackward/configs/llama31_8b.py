# LLAMA 3.1 8B spec.
# Check /mnt/efs/augment/checkpoints/llama3.1/hf/Meta-Llama-3.1-8B/config.json
from base.fastforward import positional_embeddings
from research.fastbackward import model

ffn_type = ""
norm_type = "rmsnorm"
pos_embed_type = "rope"


n_layers = 32
n_embd = 4096
# This aims to explicitly disable the deprecated way to specify the FFN layer.
ffn_type = ""
rotary_config = positional_embeddings.RotaryConfig(
    rotary_ratio=1.0,
    rotary_theta=500000.0,
    max_position_embeddings=131072,
    ext_config=positional_embeddings.Llama31ExtensionConfig(
        rotary_scaling_factor=8.0,
    ),
)
attn_config = model.GenericAttnSpec(
    hidden_dim=4096,
    n_heads=32,
    n_kv_heads=8,
    norm_type="rmsnorm",
    pos_embed_type="rope",
    bias=False,
)
ffn_config = model.SwiGLUSpec(hidden_dim=4096, intermediate_size=14336, bias=False)
first_layer_ffn_config = ffn_config
norm_eps = 1e-5
block_size = 128000
model_vocab_size = 128256
model_parallel_size = 1
use_sequence_parallel = True

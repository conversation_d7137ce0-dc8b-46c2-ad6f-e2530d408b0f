from research.fastbackward.model import GenericAttnSpec

# Qwen2.5 Coder 0.5B spec.
ffn_type = "glu"
bias = "none"
norm_type = "rmsnorm"
pos_embed_type = "rope"

n_layers = 24
n_heads = 14
n_kv_heads = 2
n_embd = 896
ffn_dim_multiple_of = 256
ffn_dim_multiplier = 2.036

norm_eps = 1e-06
rope_theta = 1000000.0

attn_config = GenericAttnSpec(
    hidden_dim=896,
    n_heads=14,
    n_kv_heads=2,
    norm_type="rmsnorm",
    pos_embed_type="rope",
    bias=False,
    qkv_bias=True,
)

# Vocab stuff
model_vocab_size = 151936
fim_middle_token_id = 151660
eot_token_id = 151643
pad_token_id = 151665

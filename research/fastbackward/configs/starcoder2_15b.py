# See /mnt/efs/augment/checkpoints/starcoder2-15b-fb-mp2/params.json
ffn_type = "mlp"
bias = "attn_mlp"
norm_type = "layernorm"
pos_embed_type = "rope"

n_layers = 40
n_heads = 48
n_kv_heads = 4
n_embd = 6144
rope_theta = 100000
model_vocab_size = 49152
multiple_of = 256
ffn_dim_multiplier = 1
norm_eps = 1e-5
block_size = 4096
model_parallel_size = 2
use_sequence_parallel = True
# checkpoint_dir: /mnt/efs/augment/checkpoints/starcoder2-15b-fb-mp2

from research.fastbackward.model import GenericAttnSpec

# Qwen2.5 Coder 7B spec.
ffn_type = "glu"
bias = "none"
norm_type = "rmsnorm"
pos_embed_type = "rope"

n_layers = 28
n_heads = 28
n_kv_heads = 4
n_embd = 3584
ffn_dim_multiple_of = 512
ffn_dim_multiplier = 1.95

norm_eps = 1e-06
rope_theta = 1000000.0

attn_config = GenericAttnSpec(
    hidden_dim=3584,
    n_heads=28,
    n_kv_heads=4,
    norm_type="rmsnorm",
    pos_embed_type="rope",
    bias=False,
    qkv_bias=True,
)

# Vocab stuff
model_vocab_size = 152064
fim_middle_token_id = 151660
eot_token_id = 151643
pad_token_id = 151665

# See /mnt/efs/augment/checkpoints/starcoder2-3b-fb/params.json
ffn_type = "mlp"
bias = "attn_mlp"
norm_type = "layernorm"
pos_embed_type = "rope"

n_layers = 30
n_heads = 24
n_kv_heads = 2
n_embd = 3072
rope_theta = 1000000
model_vocab_size = 49152
multiple_of = 256
ffn_dim_multiplier = 1
norm_eps = 1e-5
block_size = 4096
model_parallel_size = 1
# checkpoint_dir: /mnt/efs/augment/checkpoints/starcoder2-3b-fb

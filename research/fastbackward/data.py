"""Utilities for data loading in fastbackward."""

import logging
import typing
from collections.abc import Callable
from typing import Any, Iterator, List, Optional, TypeVar

import numpy as np
import torch
import torch.utils.data

import research.fastbackward.fs_model_parallel as mpu
from research.fastbackward.loss_masking import LossMaskFn, unmask_token

logger = logging.getLogger(__name__)

IGNORE_LABEL = -1


def make_indexed_dataset_collate_fn(
    seqlen: int, loss_mask_fn: Optional[LossMaskFn] = None
):
    """Creates a `collate_fn` for an indexed_dataset to pass to a DataLoader."""

    def collate_fn(samples):
        assert len(samples) > 0, f"{len(samples)=}"
        assert len(samples[0]) >= (seqlen + 1), f"{len(samples[0])=}, {seqlen=}"
        x = torch.stack(
            [torch.from_numpy(sample[0:seqlen].astype(np.int64)) for sample in samples]
        )
        y = torch.stack(
            [
                torch.from_numpy(sample[1 : seqlen + 1].astype(np.int64))
                for sample in samples
            ]
        )
        if loss_mask_fn is not None:
            loss_mask = loss_mask_fn(y).logical_not()
            x, y = unmask_token(x), unmask_token(y)
            y[loss_mask] = IGNORE_LABEL
            num_masked = loss_mask.sum().item()
            return x, y, num_masked
        else:
            return unmask_token(x), unmask_token(y)

    return collate_fn


class DistributedStepCountSampler(torch.utils.data.Sampler[List[int]]):
    """A Sampler to run a fixed number of steps over a dataset.

    The class combines some logic from PyTorch's DistributedSampler and BatchSampler, but does so
    in a way that lets you specify a step count (possibily multiple epochs) rather than always
    running over the underlying dataset exactly once.

    Some key points:
    - `batch_size` is per-GPU or per-model parallel group if using model parallelism
    - The sampler will automatically re-shuffle at epoch boundaries
    - The sampler always drops the tail of an epoch to ensure whole batches
      - "whole" == multiple of batch_size * dp_world_size
    """

    def __init__(
        self,
        dataset: torch.utils.data.Dataset,
        batch_size: int,
        step_count: Optional[int] = None,  # If None, run one epoch
        start_step: int = 0,
        dp_rank: int | None = None,
        dp_world_size: int | None = None,
        seed: int = 0,
        cross_shuffle: bool = True,
    ):
        super().__init__()

        if dp_rank is None:
            dp_rank = mpu.get_data_parallel_rank()
        if dp_world_size is None:
            dp_world_size = mpu.get_data_parallel_world_size()

        self.dataset_len = len(dataset)  # type: ignore[arg-type]
        self.local_batch_size = batch_size
        self.global_batch_size = self.local_batch_size * dp_world_size
        self.steps_per_epoch = self.dataset_len // self.global_batch_size
        assert self.steps_per_epoch > 0, "dataset too small"
        if step_count is None:
            step_count = self.steps_per_epoch
        self.step_count = step_count
        assert start_step < step_count, f"{start_step=} must be < {self.step_count=}"
        self._start_step = start_step
        self.rank = dp_rank
        self.seed = seed
        self.cross_shuffle = cross_shuffle
        if isinstance(dataset, torch.utils.data.ConcatDataset):
            self.cumulative_sizes = dataset.cumulative_sizes
        else:
            self.cumulative_sizes = [self.dataset_len]

    @property
    def start_step(self):
        return self._start_step

    @start_step.setter
    def start_step(self, start_step: int):
        assert (
            start_step < self.step_count
        ), f"{start_step=} must be < {self.step_count=}"
        self._start_step = start_step

    def _data_order_for_epoch(self, epoch) -> List[int]:
        """Create shuffled index.

        If cross_shuffle=False, only shuffle within each block.
        Otherwise shuffle the entire dataset.

        The two modes would be identical if the dataset is not
        a ConcatDataset, or if it only contains a single subset.
        """
        if self.cross_shuffle:
            g = torch.Generator()
            g.manual_seed(self.seed + epoch)
            return torch.randperm(self.dataset_len, generator=g).tolist()
        large_prime = 104729
        # For each range, generate permutation and
        seed = self.seed + epoch
        results = []
        for start, end in zip([0] + self.cumulative_sizes, self.cumulative_sizes):
            # For each segment,
            g = torch.Generator()
            g.manual_seed(seed)
            seed += large_prime
            segment = (torch.randperm(end - start, generator=g) + start).tolist()
            results.extend(segment)
        return results

    def __iter__(self) -> Iterator[List[int]]:
        # Lazy init
        cur_epoch = -1
        data_order: List[int] = []
        for step in range(self.start_step, self.step_count):
            # At epoch boundaries (or init), get a new data order
            this_epoch = step // self.steps_per_epoch
            if this_epoch > cur_epoch:
                data_order = self._data_order_for_epoch(this_epoch)
                cur_epoch = this_epoch

            epoch_step = step - this_epoch * self.steps_per_epoch
            batch_start = (
                epoch_step * self.global_batch_size + self.rank * self.local_batch_size
            )
            batch_end = batch_start + self.local_batch_size
            assert 0 <= batch_start and batch_end <= len(data_order)
            yield data_order[batch_start:batch_end]

    def __len__(self) -> int:
        return self.step_count - self.start_step


class RandomTokenDataset(torch.utils.data.Dataset):
    """Helper class to generate random batches of tokens for speed tests.

    Notes:
    - `num_items` is the total size of the "dataset" -- it's kind of meaningless, but `Dataset`
      needs to implement __len__.
    - The `seed` should be the same for all ranks, since per-rank slicing is handled by the
      Sampler, not the Dataset.
    """

    def __init__(self, vocab_size: int, num_items: int, seqlen: int, seed: int = 0):
        if vocab_size <= np.iinfo(np.uint16).max:
            self.dtype = np.uint16
        else:
            self.dtype = np.uint32
        self.vocab_size = vocab_size
        self.num_items = num_items
        self.seqlen = seqlen
        self.seed = seed

    def __len__(self):
        return self.num_items

    def __getitem__(self, idx: int):
        gen = np.random.default_rng(self.seed + idx)
        return gen.integers(0, self.vocab_size, size=self.seqlen + 1, dtype=self.dtype)


def pad_or_truncate_tokens(
    tokens: list[int], desired_length: int, pad_token_id: int
) -> list[int]:
    """Pad or truncate a list of tokens to a desired length."""
    if len(tokens) > desired_length:
        return tokens[:desired_length]
    else:
        return tokens + [pad_token_id] * (desired_length - len(tokens))


U = TypeVar("U")
V = TypeVar("V")


def apply_chained(fns: list[Callable[[Any], Any]], **kwargs) -> Callable[[Any], Any]:
    """Apply a sequence of functions in a chain.

    Args:
        fns: A list of functions.
        kwargs: The arguments to pass to the first function.

    Returns:
        The result of applying the functions in the chain.
    """

    def _fn(x):
        for fn in fns:
            x = fn(x, **kwargs)
        return x

    return _fn


class MapDataset(torch.utils.data.Dataset[V]):
    """A dataset that is created by mapping the items of another dataset.

    Example:
    >>> dataset = ListDataset(list(range(10)))
    >>> mapped_dataset = MapDataset(
    ...     dataset, apply_chained([lambda x: x * 2, lambda x: x * 3])
    ... )
    >>> assert mapped_dataset[0] == 0
    >>> assert mapped_dataset[1] == 6
    >>> assert mapped_dataset[2] == 12
    >>> ...
    """

    def __init__(self, dataset: torch.utils.data.Dataset[U], map_fn: Callable[[U], V]):
        # Keep this information around to help with error messages.
        self._map_fn = map_fn
        self._dataset = dataset

    def __str__(self):
        return f"{self.__class__.__name__}({self._map_fn}, {self._dataset})"

    def __repr__(self):
        return f"<{self.__class__.__name__}({self._map_fn}, {self._dataset})>"

    @typing.overload
    def __getitem__(self, idx: int) -> V:
        pass

    @typing.overload
    def __getitem__(self, idx: slice) -> list[V]:
        pass

    def __getitem__(self, idx):
        if isinstance(idx, slice):
            return [
                self._map_fn(self._dataset[ix]) for ix in range(*idx.indices(len(self)))
            ]

        x = self._dataset[idx]
        return self._map_fn(x)

    def __len__(self):
        # Cannot verify that dataset is Sized.
        return len(self._dataset)  # type: ignore[arg-type]


class ListDataset(torch.utils.data.Dataset[U]):
    """Creates a dataset from a list of items.

    Weirdly, torch doesn't have a built-in way to do this; the built-in `TensorDataset`
    doesn't let you create a dataset with varying length items. In practice, we mostly
    use `IndexedDataset` to give us variable length tensors, but this is useful for
    testing.
    """

    def __init__(self, items: list[U]):
        self._items = items

    def __getitem__(self, idx):
        return self._items[idx]

    def __len__(self):
        return len(self._items)

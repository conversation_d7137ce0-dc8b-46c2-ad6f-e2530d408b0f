#!/bin/bash
# This script provides an entrypoint for determined jobs to launch fastbackward.
# Compare to research/gpt-neox/jobs/determined.sh.
#
# Command-line arguments:
#   $1: The first argument is the path to the training script.
#   $@: All remaining command line args are passed directly to the training script.
#
# The script is run with //research/fastbackward as the working directory.

set -e
set -x

AUGMENT_DIR=$(dirname $0)/../../../
JOBS_DIR=$AUGMENT_DIR/research/gpt-neox/jobs

# First, set up some state.
source $JOBS_DIR/entrypoint_utils.sh
entrypoint_setup

# Install the augment package.
cd $AUGMENT_DIR
pip install -e .

# Get the train script.
TRAIN_SCRIPT="$1";
shift

if [ ! -f "$TRAIN_SCRIPT" ]; then
    echo "Training script not found: $TRAIN_SCRIPT"
    exit 1
fi

# Launch training, passing through all args.
cd $AUGMENT_DIR/research/fastbackward

python -m determined.launch.torch_distributed python "${TRAIN_SCRIPT}" --determined_enabled=True "$@"

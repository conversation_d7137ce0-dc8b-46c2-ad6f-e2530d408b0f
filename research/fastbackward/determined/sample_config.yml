determined:
  description: null
  workspace: Dev
  project: Default

augment:
  podspec_path: "1xA100.yaml"
  gpu_count: 1
  project_group: pretraining
  # Only keep the last checkpoint in testing. You may want to change this field.
  keep_last_n_checkpoints: 1

fastbackward_configs:
 # Zero or more config files. Path relative to fastbackward base.
 - configs/llama_1b.py

fastbackward_args:
  out_dir: null  # Required if running locally.
  run_name: test
  n_kv_heads: 16
  learning_rate: 2e-3
  min_lr: 2e-4
  decay_lr: True
  max_iters: 4
  lr_decay_iters: 4
  eval_interval: 3
  eval_items: 8  # NOTE: Set to 0 to use the entire eval set.
  train_data_path: /mnt/efs/augment/user/zhewei/data/starcoder/filename_match/training
  eval_data_path: /mnt/efs/augment/user/zhewei/data/starcoder/filename_match/validation
  # continuous_harness_evals: configs/pretrain/*.yml
  model_vocab_size: 51200
  batch_size: 4
  gradient_accumulation_steps: 2
  use_activation_checkpointing: False
  checkpoint_optimizer_state: False
  wandb_project: null  # NOTE: If null, will not log to wandb
  # Example checkpoints:
  # checkpoint: /mnt/efs/augment/user/zhuoran/experiments/lr33_30b/checkpoint_llama_iteration_20000
  # checkpoint: ccc1ed90-fcaf-446b-bfe5-3de59dda44bd
  # restore_training_metadata_from_checkpoint: True
  # restore_optimizer_state_from_checkpoint: True

  # To visualize logits, you must also specify a tokenizer name that base.tokenizers recognize
  # visualize_logits_samples: 10
  # tokenizer_name: deepseek_coder_base

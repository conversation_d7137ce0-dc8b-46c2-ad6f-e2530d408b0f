"""Top-level script to launch fastbackward training in determined.

Modeled after research/gpt-neox/jobs/experiment.py.

Usage:
$ python3 launch.py <config_file> -s <script_path>
"""

from __future__ import annotations

import argparse
import logging
import shlex
import subprocess
import sys
import urllib.parse
from collections import UserDict
from pathlib import Path
from typing import Sequence

import yaml
from jobs.internal.augment_configs import DeterminedConfig

from research.environments import list_providers, sync, detect_provider
from research.environments.providers import ClusterName
from research.fastbackward.utils import (
    combine_dict,
    flatten_dict,
    parse_key_value_args,
    unflatten_dict,
)

MYDIR = Path(__file__).parent
AUGMENT_ROOT = MYDIR.parent.parent.parent
WORKDIR = Path("/run/determined/workdir")
ENTRYPOINT_SCRIPT = WORKDIR / "research/fastbackward/determined/fastbackward.sh"


def _get_workdir_script_path(script_path: Path):
    """Translate the script path to a path relative to the workdir."""
    # Get an absolute path for this
    script_path = script_path.resolve()
    try:
        return WORKDIR / script_path.relative_to(AUGMENT_ROOT)
    except ValueError as e:
        raise ValueError(
            f"script_path {script_path=} is not part of {AUGMENT_ROOT=}."
        ) from e


class AttrDict(UserDict):
    def __init__(self, *args, **kwargs):
        super(AttrDict, self).__init__(*args, **kwargs)

    def __getattr__(self, name):
        if name not in self:
            raise AttributeError(f"No such attribute: {name}")
        else:
            return self[name]

    def __getitem__(self, key):
        if key not in self:
            raise KeyError(f"No such key: {key}")
        elif isinstance(self.data[key], dict):
            return AttrDict(self.data[key])
        else:
            return self.data[key]


def _sync_gcp(path: str | Path, dest_cluster: ClusterName) -> None:
    """Sync the given path to GCP."""
    path = Path(path)
    print(f"Syncing {path} to {dest_cluster}")
    sync(path)


def _sync_directories(dirs: Sequence[str | Path | None], cluster: ClusterName) -> None:
    """Sync the directories to the given cluster."""
    for sync_dir in dirs:
        if sync_dir is None or sync_dir == "":
            continue
        _sync_gcp(sync_dir, cluster)


def launch_fb_job(
    config_data: dict,
    extra_args: dict | None = None,
    script_path: Path = AUGMENT_ROOT / "research/fastbackward/train.py",
    dump_config_only: bool = False,
    cluster: ClusterName = "CW-EAST4",
    sync_data_checkpoint: bool = True,  # Unused, left for compatibility
    skip_bazel: bool = True,
    include_files: Sequence[str | Path | None] = [],
) -> int | None:
    """Launch a FastBackward job in Determined.

    Returns the experiment ID if the job was successfully launched.

    Args:
        config_data: The config data to send to Determined.
        extra_args: A dictionary of extra arguments to be merged with config_data.
        script_path: The path to the script to run.
        dump_config_only: If true, only dump the config and exit.
        cluster: The cluster to launch the job on.
        sync_data_checkpoint: deprecated, left for compatibility
    """
    fastbackward_configs = config_data.pop("fastbackward_configs", [])
    fastbackward_args = config_data.pop("fastbackward_args", {})
    # Combine any override flags from the command line.
    fastbackward_args = combine_dict(fastbackward_args, extra_args or {})
    # We're going to pass this dict as a serires of key-value pairs, so we need to
    # flatten any nested dicts.
    fastbackward_args = flatten_dict(fastbackward_args)

    # Format the run name using the current arguments.
    fastbackward_args["run_name"] = fastbackward_args["run_name"].format_map(
        # We need to unflatten the dict to get format strings like {foo.bar} to work
        # with format_map().
        AttrDict(unflatten_dict(fastbackward_args))
    )

    fastbackward_args["determined_master_url"] = (
        DeterminedConfig.determined_url_for_cluster(cluster)
    )

    # Entrypoint is `fastbackward.sh <script> <config1> ... --arg1=val1 --arg2=val2...`
    fb_command = (
        [str(ENTRYPOINT_SCRIPT)]
        + [shlex.quote(str(_get_workdir_script_path(script_path)))]
        + fastbackward_configs
        + [
            "--{k}={v}".format(
                k=shlex.quote(k), v=shlex.quote(v) if isinstance(v, str) else v
            )
            for k, v in fastbackward_args.items()
        ]
    )

    config_data["augment"]["entrypoint"] = " ".join(fb_command)
    config_data["augment"]["cluster"] = cluster
    if "name" not in config_data["determined"]:
        config_data["determined"]["name"] = fastbackward_args["run_name"]
    else:
        logging.warning(
            "Setting determined.name separately is deprecated. "
            "Use fastbackward_args.run_name instead."
        )

    cfg = DeterminedConfig(config_data, skip_neox_args=True)
    # Save the command line to the config
    cfg.augment["command_line"] = " ".join(sys.argv)

    if dump_config_only:
        print(cfg)
        return None

    experiment_id = cfg.run(run_bazel=not skip_bazel, include_files=include_files)
    print(f"Created experiment {experiment_id}")
    print(urllib.parse.urljoin(cfg.determined_url, f"det/experiments/{experiment_id}"))
    return experiment_id


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "config_file",
        type=Path,
        help="Path to the config file.",
    )
    parser.add_argument(
        "-s",
        "--script",
        type=Path,
        default=AUGMENT_ROOT / "research/fastbackward/train.py",
        help="Path to the training script that will be run in Determined.",
    )
    parser.add_argument(
        "-d",
        "--dump_config",
        default=False,
        action="store_true",
        help="Dump the experiment config file only. Do not start the job.",
    )
    parser.add_argument(
        "-c",
        "--cluster",
        default="CW-EAST4",
        type=str,
        choices=list_providers(),
        help="Set the desired cluster",
    )
    parser.add_argument(
        "--skip_bazel_build",
        default=False,
        action="store_true",
        help="Skip the bazel build step before launching. May result in no tokenizer in the determined environment.",
    )
    parser.add_argument(
        "--include_files",
        default=[],
        nargs="+",
        help="Additional include files to use for the experiment",
    )

    args, extra_values = parser.parse_known_args()
    with args.config_file.open("r", encoding="utf-8") as f:
        config_dict = yaml.safe_load(f)
    extra_args = parse_key_value_args(extra_values)
    launch_fb_job(
        config_dict,
        extra_args=extra_args,
        script_path=args.script,
        dump_config_only=args.dump_config,
        cluster=args.cluster,
        skip_bazel=args.skip_bazel_build,
        include_files=args.include_files,
    )


if __name__ == "__main__":
    main()

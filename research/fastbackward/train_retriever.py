r"""Top-level training script for retrievers with fastbackward.

Train scripts can get messy as they grow in complexity, so here are the assumptions of
this training script (see Extensibility below to specialize its functionality):

1. It should support the training of "most" retrieval models with "most" loss functions.
   As a result the script requires a somewhat verbose configuration to handle the
   generality of use-cases. See Configuration and Extensibility below for options on
   specializing this script. This includes for example:
   - Model-parallel and data-parallel training.
   - Loading checkpoints from existing retrieval and language models.
   - Training a retriever from scratch.
   - Perplexity, MAP, binary cross-entropy losses.
2. It should save checkpoints that can be loaded elsewhere. See Checkpoint format below.
3. It should save a training configuration that should be sufficient to reproduce a
   training run (assuming the data or model code haven't been modified).
4. This script assumes training data is in IndexedDataset format with each example
   consisting of a query followed by a series of scored documents (other formats might
   be supported in the future):

    {query}<|end-of-query|> ↩
    {doc1}<|end-of-key|>{score}<|end-of-key|> ↩
    ... ↩
    {docK}<|end-of-key|>{score}<|end-of-key|> ↩

## Limitations:
* This script does not support contrastive training, especially in large-batch settings.

## Configuration:
The full configuration for this script is specified via the `Config` dataclass. See
`sample_retrieval_config.yml` for an example. The most interesting part of this config
is the `components` field, which specifies the model and loss function to use.
The class or function used is dynamically dispatched based on the `component_name`. Here
is a simple example:
```yaml
components:
model:
    # This says we want to use this function to create the model -- the remaining
    # fields describe the arguments to the function.
    $component_name: research.fastbackward.train_retriever.create_dual_encoder_with_tokenizer
    # Here, we reference the query (and document) model by name - because they share a
    # name, the same model is passed in for the query and document towers.
    query_model: query_model
    doc_model: query_model
    # This is a little strange, but the tokenizer is injected into the function using
    # the name "tokenizer".
    tokenizer: tokenizer
query_model:
    # Now to construct the query model, we'll call this checkpoint loading function.
    # We could also specify the full model config here, but that's too verbose.
    $component_name: research.fastbackward.checkpointing.neox.load_starethanol_checkpoint
    checkpoint_path: /mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000
loss_fn:
    # Finally, we'll create a loss function using this class.
    $component_name: research.fastbackward.retrieval_models.PerplexityLoss
    # The class uses a single config in its constructor with simple arguments below.
    config:
        gold_temperature: 0.01
        pred_temperature: 100.0
        logits_scale: 1.0
        learnable_logits_scale: True
```

## Extensibility:
If you would like to simplify the configuration of components or extend some parts of
its functionality, you can import this script and modify its behavior. Here is an
example:

```python
from research.fastbackward import train_retriever

def custom_load_dataset(
    config: train_retriever.RetrievalDataConfig,
    tokenizer: Tokenizer
):
    # My custom loading functions.

def main():
    # Code to set up the config with your custom logic.
    config = train_retriever.Config(...)
    # Patching the load dataset with your custom function, if you so want.
    train_retriever.load_dataset = custom_load_dataset
    train_retriever.run(config)

if __name__ == "__main__":
    main()
```

## Usage examples:

```bash
# Run the train script locally. You can attach a debugger to this if you'd like.
python3 train_retriever.py \
    # You can point to a yaml configuration.
    determined/sample_retrieval_config.yml \
    # You can override configuration parameters via command line.
    --run_name my-run
    --train_options.batch_size=8
# Run the train script via determined -- looks basically the same.
python3 determined/launch.py -s train_retriever.py \
    determined/sample_retrieval_config.yml \
    # You can override configuration parameters via command line.
    --run_name my-run
    --train_options.batch_size=8
```
"""

import contextlib
import logging
from dataclasses import dataclass, field
from functools import partial
from pathlib import Path
from typing import Literal, NamedTuple, Optional

import determined as det
import torch
import torch.distributed
import torch.utils.data
from dataclasses_json import DataClassJsonMixin
from megatron.data import indexed_dataset
from tensordict import TensorDict

import research.fastbackward.fs_model_parallel as mpu
from base.component_registry.component_registry import ComponentRegistry
from base.tokenizers import Tokenizer, create_tokenizer_by_name
from base.tokenizers.tokenizer import RetrievalSpecialTokens
from research.core.component_registry import autocreate_registry, create_components
from research.fastbackward import (
    data,
    distributed,
    logutils,
    losses,
    retrieval_data,
    retrieval_models,
    training,
    utils,
)
from research.fastbackward.checkpointing import checkpointing, neox
from research.fastbackward.data import (
    DistributedStepCountSampler,
    ListDataset,
)
from research.fastbackward.mixed_precision_adam import MixedPrecisionAdamW
from research.fastbackward.model import Transformer, configure_fsdp_optimizer

logger = logging.getLogger(__name__)


@dataclass
class RetrievalDataConfig(DataClassJsonMixin):
    """Configuration for loading retrieval data."""

    path: Path
    """Path to dataset in IndexedDataset format."""

    tokenizer_name: str
    """The name of the tokenizer to use to parse the data."""

    limit: Optional[int] = None
    """If set, the maximum number of entries to use."""

    max_query_tokens: int = 4096
    """The maximum number of tokens to use for a query."""

    max_document_tokens: int = 1024
    """The maximum number of tokens to use for a document."""

    documents_per_batch: int = 64
    """The number of documents to use per batch."""

    selection_method: Literal["first", "random"] = "first"
    """The method to use to select documents from the batch.

    One of:
        - first: Select the first n documents.
        - random: Select a random n documents balancing positive and negative.
    """

    max_positive_count: int = -1
    """The maximum number of positive documents to include in a batch.

    Only used if selection_method is "random".
    """

    positive_threshold: float = 0.0
    """The threshold to use to determine if a document is positive.

    Only used if selection_method is "random".
    """


@dataclass
class Config(DataClassJsonMixin):
    """A class to hold all the configuration parameters for this train script."""

    run_name: str
    """The name of the run to use for logging and wandb."""

    train_data: RetrievalDataConfig | list[RetrievalDataConfig]
    """Configuration for the training data."""

    components: dict
    """The components to use for the training.

    This dictionary must contain two fields:
    - model: A model to train.
    - loss_fn: A loss function to use.

    See `sample_retrieval_config.yml` for examples.
    """

    # ==== Training options ====
    dtype: str = "bfloat16"
    """The dtype to use for training."""

    train_options: training.TrainOptions = field(default_factory=training.TrainOptions)
    """The training configuration."""

    max_epochs: int = -1
    """If >0, the number of epochs to train for."""

    # ===== Evaluation / Checkpointing options =====

    eval_data: Optional[RetrievalDataConfig] = None
    """If set, configuration for the eval data.

    If left unset, evaluation will not be run.
    """

    eval_interval: int = 0
    """The number of iterations to run between evaluations."""

    eval_batch_size: int = -1
    """The batch size to use for evaluation.

    If -1, use the same batch size as training."""

    checkpoint_interval: int = -1
    """The number of iterations to run between checkpointing.

    If -1, use the same interval as eval_interval.
    If 0, never checkpoint.

    Must be a multiple of `eval_interval`.
    """
    # ===== Runtime options =====

    model_parallel_size: int = 1
    """The number of GPUs to use for model parallelism."""

    data_parallel_size: int = -1
    """The data parallel size. If -1, determine it from the distributed environment.

    This field is typically not set manually, but rather is set by the training script
    to reproduce the run in the future."""

    out_dir: Optional[Path] = None
    """The output directory to use for logging and checkpoints."""

    wandb_project: Optional[str] = None
    """If set, the wandb project to log to."""

    wandb_group: Optional[str] = None
    """If set, the wandb group to log to."""

    determined_enabled: bool = False
    """If true, use determined to run the training.

    NOTE: This flag is set by the launch script -- while we don't currently use it
    we need it to parse the input options.
    """

    determined_master_url: str = ""
    """External URL of Determined master; automatically set in launch.py."""

    def __post_init__(self):
        """Configuration consistency checks."""
        # Set default values.
        if self.eval_batch_size == -1:
            # Default to using the same batch size as training for eval.
            self.eval_batch_size = self.train_options.batch_size

        if self.checkpoint_interval == -1:
            # Default to checkpointing at the same time as evaluation.
            self.checkpoint_interval = self.eval_interval

        # Consistency checks.
        assert (self.max_epochs > 0) or (
            self.train_options.max_iters > 0
        ), f"Set one of {self.max_epochs=} or {self.train_options.max_iters=}.\n"

        if not isinstance(self.train_data, list):
            self.train_data = [self.train_data]

        if self.eval_data is not None:
            assert self.eval_data.tokenizer_name == self.tokenizer_name

        if self.checkpoint_interval > 0:
            # We require that the checkpoint interval is a multiple of the eval interval.
            assert (
                self.eval_interval > 0
                and self.checkpoint_interval % self.eval_interval == 0
            ), f"{self.checkpoint_interval=} must be a multiple of eval_interval {self.eval_interval=}."

    @property
    def tokenizer_name(self) -> str:
        """The name of the tokenizer to use."""
        if isinstance(self.train_data, list):
            return self.train_data[0].tokenizer_name
        else:
            return self.train_data.tokenizer_name


def log_metrics(
    iter_num: int,
    metrics: training.IterationMetrics,
    max_iters: int,
) -> None:
    """Log training metrics."""
    assert distributed.is_main_process()
    logger.info(
        "iter %5d / %5d: loss %0.4f, lr %0.2e, time %0.2fms, needs %s to finish.",
        iter_num,
        max_iters,
        metrics.loss,
        metrics.learning_rate,
        metrics.iteration_time_s * 1000,
        logutils.seconds2str(metrics.remaining_time_s),
    )

    logutils.report_metrics(
        {
            "train/kl_loss": metrics.loss,
            "train/grad_norm": metrics.grad_norm,
            "train/learning_rate": metrics.learning_rate,
            "runtime/iteration_time_s": metrics.iteration_time_s,
            "tracking/total_samples_seen": metrics.total_samples_seen,
            "tracking/remaining_time_s": metrics.remaining_time_s,
        },
        iter_num,
    )


@torch.no_grad()
def run_evaluation(model: torch.nn.Module, eval_loader: torch.utils.data.DataLoader):
    """Run evaluation for the whole of eval_loader."""
    model.eval()
    my_device = distributed.get_local_device()
    metrics = retrieval_models.RankingMetrics(count=0)
    for i, X in enumerate(eval_loader):
        logger.info(f"Eval iter {i}/{len(eval_loader)}: {metrics}")
        # Move the data to the model.
        X = X.to(my_device, non_blocking=True)
        Y_hat = model(X)
        metrics_ = retrieval_models.compute_ranking_metrics(X, Y_hat)
        metrics.update(metrics_)

    # Combine the metrics across the data parallel group.
    metrics = retrieval_models.reduce_retrieval_metrics_dp(metrics)
    model.train()

    return metrics


def post_iter_hook(
    iter_num: int,
    model: torch.nn.Module,
    config: Config,
    loss_fn: torch.nn.Module,
    eval_loader: torch.utils.data.DataLoader,
    checkpoint_manager: checkpointing.CheckpointManager,
    det_context: Optional[det.core.Context] = None,  # type: ignore
    start_iteration: int = 0,
):
    """Hook run after every training iteration.

    Args:
        iter_num: The current iteration number.
        model: The model being trained.
        optimizer: The optimizer used to train the model.
        config: The full experiment configuration.
        loss_fn: The loss function used to train the model.
        eval_loader: The evaluation data loader.
        checkpoint_manager: The checkpoint manager to use to save checkpoints.
        det_context: The determined context, if we are running on Determined.
        start_iteration: The iteration number we started training from (used to skip
            checkpointing at the start).
    """
    is_last_iter = iter_num == config.train_options.max_iters

    preempted = det_context.preempt.should_preempt() if det_context else False
    if preempted:
        logger.info("Received Determined preemption -- performing final checkpointing.")

    # 1. Run evaluation if:
    should_run_eval = (
        # It's the starting step.
        iter_num == start_iteration
        # It's at an eval interval.
        or (config.eval_interval > 0 and (iter_num % config.eval_interval == 0))
        # It's at the last iteration.
        or is_last_iter
        # We are being pre-empted.
        or preempted
    )
    if not should_run_eval:
        return False

    # Run eval.
    eval_metrics = run_evaluation(model, eval_loader)
    if distributed.is_main_process():
        logger.info(
            f"{iter_num:5d}/{config.train_options.max_iters:5d}: {eval_metrics}"
        )
        logutils.report_metrics(
            {
                f"validation/{name}": loss
                for name, loss in eval_metrics.to_dict().items()
            }
            | {
                "train/logits_scale": loss_fn.logits_scale.item(),
            },
            iter_num,
        )

    should_checkpoint = checkpoint_manager is not None and (
        # It's at a checkpoint interval.
        (
            config.checkpoint_interval > 0
            and (iter_num % config.checkpoint_interval == 0)
            # ...but not the starting step.
            and iter_num > start_iteration
        )
        # It's at the last iteration.
        or is_last_iter
        # We are being pre-empted.
        or preempted
    )

    if not should_checkpoint:
        logger.info("Skipping checkpointing.")
        return preempted
    assert checkpoint_manager is not None

    checkpoint_location = checkpointing.get_checkpoint_location(
        iter_num, config.out_dir, file_pattern="checkpoint.{iteration}"
    )

    # Linearize the model and loss into a single configuration.
    # The function handles recursive components like the query and doc models.
    model_config, model_state_dict = retrieval_models.create_checkpoint(
        {
            "model": model,
            "loss_fn": loss_fn,
        }
    )

    checkpoint_manager.checkpoint(
        checkpoint_location,
        model_state_dict=dict(model_state_dict),
        model_config=dict(model_config),
        iter_num=iter_num,
        metadata={
            "val_metrics": eval_metrics.to_dict(),
            "iter_num": iter_num,
            "config": config.to_dict(),
        },
    )
    logging.info("Saved checkpoint to %s", checkpoint_location)

    return preempted


class OptimizerAndSchedule(NamedTuple):
    """A named tuple for the output of `setup_optimizer`."""

    optimizer: MixedPrecisionAdamW
    flat_model_state: training.FlatModelState
    lr_schedule: training.LRSchedule
    start_iteration: int


def setup_optimizer(
    *modules: torch.nn.Module,
    optimizer_config: training.OptimizerConfig,
    checkpoint_manager: checkpointing.CheckpointManager,
    checkpoint_location: Optional[str] = None,
) -> OptimizerAndSchedule:
    """Setup an optimizer and learning rate schedule from the given configuration.

    Args:
        modules: One or more modules to be optimized, e.g. the model and loss function.
        optimizer_config: The optimizer configuration.
        checkpoint_manager: If provided, the checkpoint manager to load optimizer state
          from.
        checkpoint_location: If provided, the checkpoint location to load optimizer
          state from.

    Returns:
        A tuple containing the optimizer, the flattened model state, learning
        rate schedule and start iteration.
    """

    optimizer, flat_model_state = configure_fsdp_optimizer(
        *modules,
        weight_decay=optimizer_config.weight_decay,
        learning_rate=optimizer_config.learning_rate,
        betas=optimizer_config.betas,
        eps=optimizer_config.eps,
    )

    if (
        checkpoint_manager
        and checkpoint_location
        and optimizer_config.load_from_checkpoint
    ):
        start_iteration = checkpoint_manager.load_iteration_from_checkpoint(
            checkpoint_location
        )
        optimizer_state = checkpoint_manager.load_parallel_optimizer_state(
            checkpoint_location,
        )
        optimizer.load_parallel_state_dict(optimizer_state)
        del optimizer_state
    else:
        start_iteration = 0

    # NOTE(arun): You can create a constant learning rate from this by setting `min_lr`
    # to be `learning_rate` (this is the default behavior if `min_lr` is not set).
    lr_schedule = partial(
        training.get_cosine_lr,
        warmup_iters=optimizer_config.warmup_iters,
        learning_rate=optimizer_config.learning_rate,
        decay_iters=optimizer_config.decay_iters,
        min_lr=optimizer_config.min_lr,
    )

    return OptimizerAndSchedule(
        optimizer, flat_model_state, lr_schedule, start_iteration
    )


def load_dataset(
    data_config: RetrievalDataConfig,
    tokenizer: Tokenizer,
):
    """Loads a retrieval dataset from the given path."""
    assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
    assert indexed_dataset.MMapIndexedDataset.exists(
        str(data_config.path)
    ), f"Requested dataset at {data_config.path} does not exist."

    ds = indexed_dataset.MMapIndexedDataset(str(data_config.path), skip_warmup=True)
    ds = data.MapDataset(
        ds,
        # TODO(vzhao): try to split up this map function into smaller functions.
        retrieval_data.create_tokens_to_retrieval_data_fn(
            tokenizer=tokenizer,
            max_query_tokens=data_config.max_query_tokens,
            max_document_tokens=data_config.max_document_tokens,
            documents_per_batch=data_config.documents_per_batch,
            max_positive_count=data_config.max_positive_count,
            selection_method=data_config.selection_method,
            positive_threshold=data_config.positive_threshold,
        ),
    )
    if data_config.limit is not None:
        ds = torch.utils.data.Subset(ds, list(range(min(len(ds), data_config.limit))))

    return ds


def create_dual_encoder_with_tokenizer(
    query_model: torch.nn.Module,
    doc_model: torch.nn.Module,
    tokenizer: Tokenizer,
    freeze_document_model: bool = False,
) -> retrieval_models.DualEncoderModel:
    """Create a dual encoder model from the model and tokenizer.

    This is a helper function to correctly set the token ids using the tokenizer.

    Args:
        query_model: The query model to use.
        doc_model: The doc model to use.
        tokenizer: The tokenizer to use to pick the right token ids to extract
            query and document embeddings.
        freeze_document_model: If True, freeze the document model.
    """
    assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
    return retrieval_models.DualEncoderModel(
        query_model,
        doc_model,
        retrieval_models.DualEncoderModel.Config(
            query_token_id=tokenizer.special_tokens.end_of_query,
            document_token_id=tokenizer.special_tokens.end_of_key,
            freeze_document_model=freeze_document_model,
        ),
    )


def create_embedder_with_language_model(
    language_model: Transformer,
    output_projection_dim: int,
    with_output_bias: bool = True,
) -> retrieval_models.TransformerEmbedder:
    """Create an embedder model from a Transformer language model.

    This is a helper function to correctly set the input_dim based on the language
    model loaded from a checkpoint, e.g..

    Args:
        language_model: The language model to use.
        output_projection_dim: The output projection dimension.
        with_output_bias: If true, include a bias in the output projection.
    """
    return retrieval_models.TransformerEmbedder(
        language_model,
        retrieval_models.TransformerEmbedder.Config(
            input_dim=language_model.params.dim,
            output_projection_dim=output_projection_dim,
            with_output_bias=with_output_bias,
        ),
    )


def train(
    config: Config,
    existing_components: Optional[dict] = None,
    det_context: Optional[det.core.Context] = None,  # type: ignore
):
    """Run the training script.

    This method assumes the following pre-conditions are true:

    - The distributed environment is initialized with desired model parallelism.
    - The determined context has been created.

    Args:
        config: The configuration object.
        existing_components: If set, existing components to use.
        det_context: If set, the determined context used to load and save checkpoints.
    """
    existing_components = existing_components or {
        "tokenizer": create_tokenizer_by_name(config.tokenizer_name),
    }

    data_parallel_world_size = mpu.get_data_parallel_world_size()
    data_parallel_rank = mpu.get_data_parallel_rank()
    # NOTE(global-rng): we do NOT want to depend on the global pytorch RNG state as much
    # as possible. But dropout does depend on it, so to future-proof against dropout
    # support, we set the global RNG seed based on your model parallel group. That way,
    # all GPUs in the same group generate the same dropout mask (as they have the same
    # data items), while GPUs across groups have different masks.
    seed_offset = data_parallel_rank

    if config.out_dir:
        out_dir = Path(config.out_dir) / config.run_name
        logutils.setup_logging(out_dir / "fastbackward", append_timestamp=True)
    else:
        out_dir = None
        logutils.setup_logging(None, append_timestamp=True)

    train_options = config.train_options

    logger.info("Config: %s", config)
    logger.info("The output location: %s", out_dir or checkpointing.DETERMINED_LOCATION)
    logger.info("Batch size per device: %d", train_options.batch_size)
    logger.info("The data parallel world size: %d", data_parallel_world_size)

    if (
        config.data_parallel_size >= 0
        and config.data_parallel_size != data_parallel_world_size
    ):
        logger.warning(
            f"Current {data_parallel_world_size=} != {config.data_parallel_size=}. "
            "Adjusting gradient accumulation steps to match."
        )
        desired_accumulation_steps = (
            train_options.gradient_accumulation_steps * config.data_parallel_size
        )
        assert desired_accumulation_steps % data_parallel_world_size == 0, (
            f"Data-parallel {desired_accumulation_steps=}"
            f"must be divisible by {data_parallel_world_size=}."
        )
        train_options.gradient_accumulation_steps = (
            desired_accumulation_steps // data_parallel_world_size
        )
    config.data_parallel_size = data_parallel_world_size

    checkpoint_location = checkpointing.get_checkpoint_location(None, out_dir)
    logger.info("The checkpoint will be saved to %s", checkpoint_location)

    # See NOTE(global-rng)
    torch.manual_seed(1337 + seed_offset)
    torch.cuda.manual_seed(1337 + seed_offset)
    torch.backends.cuda.matmul.allow_tf32 = True  # allow tf32 on matmul
    torch.backends.cudnn.allow_tf32 = True  # allow tf32 on cudnn
    assert config.dtype in ["float32", "bfloat16"], "fp16 unsupported"
    ptdtype = {"float32": torch.float32, "bfloat16": torch.bfloat16}[config.dtype]

    if not isinstance(config.train_data, list):
        config.train_data = [config.train_data]

    tokenizer = create_tokenizer_by_name(config.tokenizer_name)
    assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)

    # Create datasets.
    train_dataset = torch.utils.data.ConcatDataset(
        [load_dataset(data_config, tokenizer) for data_config in config.train_data]
    )
    logger.info("Total training dataset size: %s", len(train_dataset))

    if config.max_epochs > 0:
        config.train_options.set_iters_for_epochs(config.max_epochs, len(train_dataset))

    logger.info(
        "Running %d iterations, or %.2f epochs.",
        config.train_options.max_iters,
        config.train_options.max_iters
        * train_options.total_per_iteration_batch_size
        / len(train_dataset),
    )

    # Set up the training data loader
    train_sampler = DistributedStepCountSampler(
        train_dataset,
        batch_size=config.train_options.batch_size,
        # NOTE(arun): We now draw samples from the dataset in micro-batches, which
        # means we have to account for the number gradient accumulation steps in
        # each iteration.
        step_count=(
            config.train_options.max_iters
            * config.train_options.gradient_accumulation_steps
        ),
        start_step=0,
        dp_rank=mpu.get_data_parallel_rank(),
        dp_world_size=mpu.get_data_parallel_world_size(),
        cross_shuffle=True,
    )
    logger.info("Total steps per epoch: %d", train_sampler.steps_per_epoch)

    _collate_fn = retrieval_data.create_collate_retrieval_data_fn(
        tokenizer.special_tokens.end_of_key, tokenizer.special_tokens.padding
    )

    # TODO(arun): We should make this less hard-coded in the train loop.
    # Wrap around the collation function because the train loop expects a tuple.
    def collate_fn(xs):
        batch = _collate_fn(xs)
        return (batch, batch["labels_BK"], 0)

    train_loader = torch.utils.data.DataLoader(
        train_dataset,
        batch_sampler=train_sampler,
        num_workers=4,
        collate_fn=collate_fn,
        pin_memory=True,
    )

    if config.eval_data:
        eval_dataset = load_dataset(config.eval_data, tokenizer)
    else:
        eval_dataset = ListDataset([])

    eval_loader = torch.utils.data.DataLoader[TensorDict](
        eval_dataset,
        sampler=torch.utils.data.DistributedSampler(
            eval_dataset,
            shuffle=False,
            num_replicas=mpu.get_data_parallel_world_size(),
            rank=mpu.get_data_parallel_rank(),
        ),
        batch_size=config.eval_batch_size,
        num_workers=4,
        collate_fn=_collate_fn,
        pin_memory=True,
    )
    logger.info("Total eval dataset size: %s", len(eval_dataset))

    checkpoint_manager = checkpointing.CheckpointManager(det_context)

    # Initialize the retrieval model and loss from the regsitry
    components = create_components(
        config.components,
        registries=[setup_registry(config.components)],
        existing_components=existing_components,
    )
    model = components.get_with_type("model", torch.nn.Module)
    loss_fn = components.get_with_type("loss_fn", torch.nn.Module)
    logger.info("Model: %s", model)
    logger.info("Loss: %s", loss_fn)
    model.train()

    # Make sure the model is in the right place and has the right dtype
    model.to(dtype=ptdtype, device=distributed.get_local_device())
    loss_fn.to(dtype=ptdtype, device=distributed.get_local_device())

    # optimizer and extra state for FSDP (distributed optimizer)
    optimizer, flat_model_state, lr_schedule, start_iteration = setup_optimizer(
        model,
        loss_fn,
        checkpoint_manager=checkpoint_manager,
        optimizer_config=config.train_options.optimizer,
    )
    # Update train sampler start step
    train_sampler.start_step = (
        start_iteration * config.train_options.gradient_accumulation_steps
    )

    # Set up metrics reporting via wandb
    if distributed.is_main_process():
        wandb_config = None
        if config.wandb_project:
            wandb_config = logutils.WandbConfig(
                project=config.wandb_project,
                name=config.run_name,
                group=config.wandb_group or None,
            )
        logutils.initialize_metrics_tracking(
            wandb_config,
            det_context,
            config.to_dict(),
        )

    # Compute eval metrics before training.
    should_stop = post_iter_hook(
        start_iteration,
        model,
        config=config,
        loss_fn=loss_fn,
        eval_loader=eval_loader,
        checkpoint_manager=checkpoint_manager,
        det_context=det_context,
        start_iteration=start_iteration,
    )

    # training loop
    for iter_num, iter_metrics, _ in training.train_loop(
        model,
        losses.ModuleAsLossFn(loss_fn),
        flat_model_state=flat_model_state,
        optimizer=optimizer,
        lr_schedule=lr_schedule,
        train_options=config.train_options,
        train_loader=train_loader,
        # Doesn't apply microbatch weighting for retrieval training. Due to the
        # implementation, the computed micro batch weights are all zeros for
        # retrieval training data.
        disable_microbatch_weighting=True,
    ):
        if (
            iter_metrics.iter_num % train_options.log_interval == 0
            and distributed.is_main_process()
        ):
            log_metrics(
                iter_num, iter_metrics, max_iters=config.train_options.max_iters
            )
        should_stop = post_iter_hook(
            iter_num,
            model,
            config=config,
            loss_fn=loss_fn,
            eval_loader=eval_loader,
            checkpoint_manager=checkpoint_manager,
            det_context=det_context,
            start_iteration=start_iteration,
        )
        if should_stop:
            logger.info("Asked to stop -- exiting early.")
            break

    if distributed.is_main_process():
        logutils.shutdown_metrics_tracking()
    torch.distributed.destroy_process_group()
    logger.info("Finished the training!")


def setup_registry(component_configs: dict[str, dict]) -> ComponentRegistry:
    # NOTE(arun): We now auto-import + register any components that are fully specified
    # (e.g. research.fastbackward.model.Transformer) in the component config, but we'll
    # still manually auto-register the most commonly used components as a shortcut and
    # for backwards compatibility.
    registry = ComponentRegistry()
    registry.autoregister(Transformer)
    registry.autoregister(retrieval_models.DualEncoderModel)
    registry.autoregister(retrieval_models.TransformerEmbedder)
    registry.autoregister(retrieval_models.PerplexityLoss)
    registry.autoregister(neox.load_starcoder_checkpoint)
    registry.autoregister(neox.load_starethanol_checkpoint)
    registry.autoregister(checkpointing.load_transformer_checkpoint)
    registry.autoregister(create_dual_encoder_with_tokenizer)
    registry.autoregister(create_embedder_with_language_model)
    return autocreate_registry(component_configs, registry)


def run(
    config: Config,
    existing_components: Optional[dict] = None,
):
    """Entry function to run the training script.

    This method takes care of setting up the distributed environment and determined
    before calling `train`.

    Args:
        config: The configuration object.
        existing_components: The existing components to use if any.
    """
    logger.info("Config: %s", config)

    # Setup registry and validate that we can create components ok.
    if "model" not in config.components or "loss_fn" not in config.components:
        raise ValueError(
            f"`model` and `loss_fn` must be in the components. Found: {config.components.keys()}"
        )
    existing_components = existing_components or {
        "tokenizer": create_tokenizer_by_name(config.tokenizer_name),
    }
    create_components(
        config.components,
        registries=[setup_registry(config.components)],
        existing_components=existing_components,
        validate_only=True,
    )

    # Init distributed and set relevant variables
    distributed.init_distributed_for_training(
        config.model_parallel_size, distributed_backend="nccl"
    )
    # `det.core.init` returns a dummy init that we can use
    experiment_context = (
        partial(
            det.core.init,  # type: ignore
            distributed=det.core.DistributedContext.from_torch_distributed(),  # type: ignore
            tensorboard_mode=det.core.TensorboardMode.MANUAL,  # type: ignore
        )
        if config.determined_enabled
        else contextlib.nullcontext
    )

    # Patch how we handle pin_memory.
    retrieval_data.patch_pin_memory()

    with experiment_context() as det_context:
        train(config, existing_components, det_context)


if __name__ == "__main__":
    import sys

    # Setup logging for the main process before we setup the distributed logging.
    logging.basicConfig(level=logging.INFO)

    utils.setup_dataclasses_json()
    config_dct = utils.parse_config_files(sys.argv[1:])
    utils.combine_dict(config_dct, utils.parse_key_value_args(sys.argv[1:]))

    config = Config.schema().load(config_dct)
    run(config)

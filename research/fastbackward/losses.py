"""Losses and metrics for fastbackward."""

from __future__ import annotations

import abc
import logging

import torch
import torch.distributed as dist

import research.fastbackward.fs_model_parallel as mpu
from research.fastbackward import functional
from research.fastbackward.data import IGNORE_LABEL
from research.fastbackward.model import Transformer


class _FakeLossForChunkedCrossEntropy:
    def __init__(self, total_loss, saved_partial_grad, saved_h, final_linear):
        self.total_loss = total_loss
        self.saved_partial_grad = saved_partial_grad
        self.saved_h = saved_h
        self.final_linear = final_linear
        # Track a divisor to support `loss = loss / grad_acc_steps` (or similar)
        self.divisor = 1.0

    def backward(self):
        # Backward on this loss means resuming backprop at the saved hidden activations.
        # NOTE: be sure to include the divisor here!
        self.saved_h.backward(self.saved_partial_grad / self.divisor)
        # Also: to make the divisor work, we previously stashed thus-far accumulated grads for
        # `final_linear`. Here we apply the divisor to the grads for this microbatch and then
        # re-accumulate the stashed grads. See NOTE(stashed-final-linear).
        for p in self.final_linear.parameters():
            assert hasattr(p, "_stashed_accumulated_grad")
            if p.grad is not None:
                p.grad.div_(self.divisor)
                p.grad.add_(p._stashed_accumulated_grad)

    def detach(self):
        # Returns the "actual" loss by applying the divisor.
        return self.total_loss.detach() / self.divisor

    def item(self):
        return self.detach().item()

    def __truediv__(self, denom):
        self.divisor *= denom
        return self

    def __mul__(self, factor):
        self.divisor /= factor
        return self


TensorLike = torch.Tensor | _FakeLossForChunkedCrossEntropy


@torch.no_grad()
def _data_parallel_reweight_factor(target_tokens: torch.Tensor) -> float:
    """Returns factor representing the relative number of valid tokens in this data parallel rank."""
    # TODO(markus): reweight between micro batches in gradient accumulation
    #   - training.py line 50ish
    #   - introduce nested for loop, first load microbatches, measure number of valid tokens globally
    #   - pass in total_batch_valid_tokens to this function
    if mpu.get_data_parallel_world_size() == 1:
        return 1.0

    # reweight the loss of this data-parallel batch using the number of valid tokens.
    valid_tokens = (target_tokens != IGNORE_LABEL).sum().unsqueeze(0)
    valid_tokens_t = valid_tokens.clone()
    dist.all_reduce(
        valid_tokens_t,
        op=dist.ReduceOp.SUM,
        group=mpu.get_data_parallel_group(),
    )
    valid_tokens_global = valid_tokens_t.item()
    if valid_tokens_global == 0:
        return 0.0  # avoid div-by-zero
    assert valid_tokens_global > 0, f"Invalid {valid_tokens_global=}"
    reweight_factor = (
        valid_tokens.item() / valid_tokens_global * mpu.get_data_parallel_world_size()
    )
    logging.debug(
        "Reweighting loss by %s (valid_tokens=%s, valid_tokens_global=%s)",
        reweight_factor,
        valid_tokens,
        valid_tokens_global,
    )
    return reweight_factor


class LossFn(abc.ABC):
    """Abstract base class for loss functions."""

    @abc.abstractmethod
    def __call__(
        self,
        input_tokens: torch.Tensor,
        target_tokens: torch.Tensor,
        logits: torch.Tensor,
    ) -> TensorLike:
        """Compute the loss for a given set of inputs and targets.

        Can be backpropagated through.
        """
        pass


class CrossEntropyLoss(LossFn):
    """Cross entropy loss."""

    def __init__(self, reweight_loss_across_dataparallel: bool = True):
        self.reweight_loss_across_dataparallel = reweight_loss_across_dataparallel

    def __call__(
        self,
        input_tokens: torch.Tensor,
        target_tokens: torch.Tensor,
        logits: torch.Tensor,
    ) -> torch.Tensor:
        """Compute the cross entropy loss for a given set of inputs and targets."""

        # We don't need the input tokens for this loss.
        del input_tokens

        # The non-parallel loss implementation is faster (because the compiler can fuse it),
        # so we use the parallel loss only when necessary.
        # TODO(carl): investigate compilation for parallel loss.
        if mpu.get_model_parallel_world_size() > 1:
            result = functional.vocab_parallel_cross_entropy(
                logits.float(), target_tokens, ignore_index=IGNORE_LABEL
            )
        else:
            result = functional.casted_cross_entropy(
                logits, target_tokens, ignore_index=IGNORE_LABEL
            )
        if self.reweight_loss_across_dataparallel:
            rw = _data_parallel_reweight_factor(target_tokens)
            result *= rw
        return result


cross_entropy_loss = CrossEntropyLoss()  # legacy / shorthand


class ChunkedCrossEntropyLoss(LossFn):
    """Cross entropy loss that chunks the sequence dimension to reduce memory usage."""

    def __init__(self, model: Transformer, chunk_size: int = 4096):
        """Initialize the loss function.

        Args:
            model: The model that will be used for the loss. This is needed to get the final
            linear layer.
            chunk_size: The chunk size to use.
        """
        assert isinstance(model, Transformer)
        self.final_linear = model.output
        self.chunk_size = chunk_size

    def __call__(
        self,
        input_tokens: torch.Tensor,
        target_tokens: torch.Tensor,
        logits: torch.Tensor,
    ) -> TensorLike:
        """Compute the cross entropy loss for a given set of inputs and targets.

        NOTES:
        - See https://github.com/augmentcode/augment/pull/14908 for a longer discussion of this feature.
        - In particular, the provided function doesn't _actually_ return a Tensor. Instead, it returns
          an instance of _FakeLossForChunkedCrossEntropy (see below) that acts like a Tensor just
          enough to work with `training.forward_backward`. If you make any substantial changes to the
          train loop, it is likely this will need to be updated.
        """
        result = _sequence_chunked_cross_entropy_loss(
            input_tokens, target_tokens, logits, self.final_linear, self.chunk_size
        )
        rw = _data_parallel_reweight_factor(target_tokens)
        return result * rw


def _sequence_chunked_cross_entropy_loss(
    _, true_labels, hidden, final_linear, chunk_size
) -> _FakeLossForChunkedCrossEntropy:
    """Main implementation of the sequence-chunked cross entropy loss.

    The main idea is:
    - Operate over the final `hidden` activations, _not_ the logits
    - Produce the logits in chunks and compute each loss chunk separately
    - Immediately backpropgate those losses -- but only back to the final hidden
    - Return an object that acts like a loss Tensor but stores the state needed to continue
    backprop.
    """
    # Sanity check the call, since this is a crazy overload.
    assert isinstance(true_labels, torch.Tensor)
    assert isinstance(hidden, torch.Tensor)
    assert isinstance(final_linear, torch.nn.Module)
    total_loss = torch.zeros([], device=hidden.device, dtype=torch.float32)
    seqlen = hidden.size(1)
    # Here is where we detach `hidden` from autograd so we can backprop loss only as far as
    # `hidden` and no further.
    h_detached = hidden.detach()
    h_detached.requires_grad_()

    # NOTE(stashed-final-linear): gradient accumulation for the final linear layer is tricky. When
    # we (later on) do `loss /= grad_acc_steps`, the grads for final_linear are already computed.
    # Naively, we could divide the grads out by the same divisor. But in the gradacc case, we would
    # _repeatedly_ divide over-and-over for multiple microbatches, which is wrong. To solve this,
    # we stash the current accumulated grads off to the side so that the grads for final linear are
    # only wrt the current microbatch. Later on, in backward, we can first fix up the divisor and
    # then re-accumulate these stashed grads.
    for p in final_linear.parameters():
        if p.grad is not None:
            p._stashed_accumulated_grad = p.grad.clone()  # type: ignore
            p.grad.zero_()
        else:
            p._stashed_accumulated_grad = torch.zeros_like(p)  # type: ignore

    # We need to average across chunks, but each chunk may have a different number of non-masked
    # tokens. So each chunk's loss is weighted by (chunk_valid_tokens / total_valid_tokens). In the
    # simple case, this reduces to weighting by (1 / num_chunks) -- ie, averaging across chunks.
    total_tokens = true_labels.numel()
    total_valid_tokens = total_tokens - (true_labels == IGNORE_LABEL).sum().item()

    # Loop over chunks and compute + backprop per-chunk loss.
    for start in range(0, seqlen, chunk_size):
        end = min(start + chunk_size, seqlen)
        this_labels = true_labels[:, start:end].contiguous()
        this_chunk_num_valid_tokens = (
            this_labels.numel() - (this_labels == IGNORE_LABEL).sum().item()
        )
        # If this chunks has no unmasked tokens, then skip it. Saves a small bit of work, and more
        # importantly, avoids adding a nan loss to total_loss. (F.cross_entropy returns nan for
        # all-masked inputs.)
        if this_chunk_num_valid_tokens == 0:
            continue

        this_h = h_detached[:, start:end, ...]
        this_logits = final_linear(this_h)
        this_loss = CrossEntropyLoss(reweight_loss_across_dataparallel=False)(
            None,  # type: ignore
            this_labels,
            this_logits,
        )
        # Weight this chunk's loss by its fraction of the non-masked tokens
        this_loss *= this_chunk_num_valid_tokens / total_valid_tokens
        total_loss += this_loss.detach()
        this_loss.backward()
    assert h_detached.grad is not None  # Can't have _all_ masked tokens
    return _FakeLossForChunkedCrossEntropy(
        total_loss, h_detached.grad.clone(), hidden, final_linear
    )


class ModuleAsLossFn(LossFn):
    """A loss function that wraps a torch.nn.Module.

    Handy for PerplexityLoss, which is a torch.nn.Module.
    """

    def __init__(self, module: torch.nn.Module):
        self.module = module

    def __call__(
        self,
        input_tokens: torch.Tensor,
        target_tokens: torch.Tensor,
        logits: torch.Tensor,
    ) -> torch.Tensor:
        return self.module(input_tokens, target_tokens, logits)

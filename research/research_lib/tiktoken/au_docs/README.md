# Tiktoken

[`tiktoken`](https://github.com/openai/tiktoken) is a fast tokenizer from
OpenAI. It implements the tokenization logics in Rust, and connect to a Python
interface.

This document presents the study that we learned from installing and using
`tiktoken` in our system, with an emphasis on comparing `tiktoken` with the
`CodeGen` that we currently use.

**TL;DR:**
* `tiktoken` is at least twice faster than `CodeGen`, and the speedup factor
grows linearly as the sequence length.

* `tiktoken`'s outputs are slightly different from `CodeGen`'s outputs, but the
difference empirically does not affect the behaviors of the downstream language
models that consume these outputs.

## Usage

## Local installation

To use `tiktoken`, you will need the Rust compiler, called `rustc`, and a PyO3
package that binds Rust code to Python interfaces, called `maturin`.

* We recommend the default [rustup.rc](https://rustup.rs/) to install `rustc`.
Note that doing so will install `rustc` *on your system* instead of on a dev
container or `venv`.  While this might sound dangerous, we haven't experienced
any failure with it.

* `maturin` can be installed via:
  ```bash
  user@remote $ pip install maturin==0.14.17
  ```
  and we recommend doing so in your `venv` or a dev container.

## Determined AI's Installation

Determined AI is Augment's current platform for managing jobs. Determined AI's
jobs are run containers, and so we need to install all dependencies into our
containers before the job can run.

In the case of `tiktoken`, this means that we need to build the `tiktoken`
library in Rust and then bind it to Python.

We can do this by adding the following lines into the `entrypoint` script of a
Determined configuration file, such as follows:

```bash
GPT_DIR=$(dirname $0)/..

echo "Install rustc"
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | bash -s -- -y
export PATH="${HOME}/.cargo/bin:$PATH"

TIKTOKEN_DIR="megatron/tokenizer/tiktoken_lib"

echo "Build Tiktoken"
cd ${TIKTOKEN_DIR}
pip install .

cd ${GPT_DIR}
echo "DONE"
```
Essentially, we are telling the Determined AI's setup job to `cd` into the
`tiktoken` code directory, then `pip install` the package as a Python
dependency. Note that this process requires the Rust compiler `rustc`, hence the
`rustup` command.

## Build the Rust code and bind it to Python

After installing, you need to build the Rust implementation of `tiktoken` and
bind it to your Python interface. You can do so by invoking:
```shell
user@remote $ cd ~/src/augment/research/research_lib/tiktoken
user@remote $ pip install .
```
After that, you should be able to `import tiktoken` from Python. One quick way
to test your installation is to try:
```shell
user@remote:~/src/augment/research/research_lib/tiktoken $ pytest tiktoken_test.py
```
If your installation is correct, you should see all tests passed:
```shell
tiktoken_test.py::test_codegen_vs_tiktoken_in_rust[v0] PASSED
tiktoken_test.py::test_codegen_vs_tiktoken_in_rust[v1] PASSED
tiktoken_test.py::test_codegen_vs_tiktoken_in_rust[v2] PASSED
tiktoken_test.py::test_tiktoken_in_megatron[v0] PASSED
tiktoken_test.py::test_tiktoken_in_megatron[v1] PASSED
tiktoken_test.py::test_tiktoken_in_megatron[v2] PASSED
```

A very important note here is that you need `maturin develop --release` in order
to match the speed of `tiktoken`, as reporeted by OpenAI. The caveat is that you
will not see any debugging messages from Rust's `dbg!(...)`.

On the other hand, `maturin develop` will also build your code, and when you run
it, you will see everything you log. However, the speed will be very slow.

## Analysis

## Datasets

We evalate two tokenizers on 6 languages; C, C++, Java, Javascript, Python, and
Go.  For each language, we take 1000 programs from TheStack.

## Speed comparison: `tiktoken` vs. `CodeGen`

![](images/speed_comparison.png)
The image above presents a comparison of tokenization speeds against the length
of the output sequences for `tiktoken` and `CodeGen`. The main
takeaways from the figure are:

* Both tokenizers have *approximately* linear latency in terms of their output
sequence lengths.

* `tiktoken` is indeed a lot faster than `CodeGenTokenier`. The speed up factor
grows as the sequences become longer, but generally `CodeGen` is rarely
3x slower than `tiktoken`.

## Output comparison: `tiktoken` vs. `CodeGen`

The outputs of `tiktoken` and `CodeGen` are slightly different from
each other, mostly because of whitespace handling.

In a nutshell, when tokenizing a very long string of whitespaces, e.g., 40
leading whitespaces due to indentation in Python:
* `tiktoken` would greedily match the longest spree of whitespaces from left to
  right. In our case, `tiktoken` would produce two tokens:
  ```
    [31 whitespaces] [9 whitespaces]
  ```
* `CodeGen`'s behavior is much less well-defined, because `CodeGen` uses
multiple rounds of normalization during the tokenization process. We do not have
a detailed understanding how `CodeGen` works.

Despite the different behaviors, the two tokenizers produce very similar
outputs. Below are some comparisons.

## Length comparison
The length of the tokenized outputs are quite similar between `tiktoken` and
`CodeGen`, and so we do not expect any difference in speed when these tokenized
sequences are passed into subsequent language models.
![](images/length_comparison.png)

## Per-line comparison
We compare the outputs of `tiktoken` and `CodeGen` at each line for multiple
programs from the aforementioned [6 languages](#datasets).

The following tables present the comparisons.

| **Language** | **Line counts** | **Line diffs** | **Diff Percentage** |
| ------------ | --------------- | -------------- | ------------------- |
| c            | 156991          | 1211           | 0.77 %              |
| c++          | 271400          | 3038           | 1.12 %              |
| go           | 130179          | 127            | 0.10 %              |
| java         | 138195          | 414            | 0.30 %              |
| javascript   | 131480          | 797            | 0.61 %              |
| python       | 128141          | 2702           | 2.11 %              |

**Table 1.** Line differences bewteen `tiktoken` and `CodeGen` for 6 languages.

| **Language** | **Line counts** | **Line diffs** | **Diff Percentage** |
| ------------ | --------------- | -------------- | ------------------- |
| c            | 156991          | 592            | 0.38 %              |
| c++          | 271400          | 425            | 0.16 %              |
| go           | 130179          | 25             | 0.02 %              |
| java         | 138195          | 111            | 0.08 %              |
| javascript   | 131480          | 15             | 0.01 %              |
| python       | 128141          | 87             | 0.07 %              |

**Table 2.** Line differences bewteen `tiktoken` and `CodeGen` for 6 languages.
Only lines *without* leading whitespaces are counted.

| **Language** | **Line counts** | **Line diffs** | **Diff Percentage** |
| ------------ | --------------- | -------------- | ------------------- |
| c            | 156330          | 0              | 0.00 % |
| c++          | 270947          | 3              | 0.00 % |
| go           | 130153          | 0              | 0.00 % |
| java         | 138080          | 0              | 0.00 % |
| javascript   | 131457          | 2              | 0.00 % |
| python       | 128047          | 2              | 0.00 % |

**Table 3.** Line differences bewteen `tiktoken` and `CodeGen` for 6 languages.
Lines with more than 32 whitespaces are skipped.

The comparisons indicate that:
* The number of different lines between `tiktoken` and `CodeGen` is very small.

* Most of the differences come from handling leading whitespaces.

* *All* differences come from handling series of more than 32 whitespaces, which
is very rare.

Thus, we conclude that `tiktoken` and `CodeGen` are very similar.

## Evaluation on real benchmarks

**Perplexity Evaluation.** To evaluate `tiktoken` and `CodeGen` on real
*benchmarks, we evaluate 3 CodeGen models, at sizes 350M, 2B, and 6B parameters,
*where the tokenizers could be `CodeGen` or `tiktoken`.

Using the two tokenizers, we compute the byte perplexity for the 3 models on the
aforementioned [6 languages](#datasets).

The following figure presents the result, from which we can observe that
`tiktoken` produces less than 0.02 point difference in byte perplexity.  This
small difference indicates that the two tokenizers indeed do not cause dramtic
changes in our model's performance.
![](images/polycoder_pplx.png)

**Generative Evaluation.** We also compare `tiktoken` and `CodeGen` on
[HumanEval](https://github.com/augmentcode/augment/blob/main/research/eval/configs/human-eval.yml),
across a wide range of models. The results are presented in the table below. The
results tell us that on this generative code completion task, `tiktoken`'s
quality is not that different from `CodeGen`.

| Models             | `tiktoken`<br>(pass@1, pass@10) | `CodeGen`<br>(pass@1, pass@10) |
| :----------------- | :-----------------------------: | :----------------------------: |
| codegen-350M-multi | (6.83, 9.58)                    | (6.83, 9.58)                   |
| conan-350M         | (6.04, 9.37)                    | (6.04, 9.37)                   |
| codegen-2B-multi   | (14.51, 20.73)                  | (14.51, 20.73)                 |
| conan-2B           | (13.26, 17.95)                  | (13.26, 17.95)                 |
| codegen-6B-multi   | (19.18, 25.69)                  | (18.99, 25.31)                 |

In conclusion, while the outputs of `tiktoken` and `CodeGen` are slightly
different, our experiments indicate that the difference do not affect the
outcome of multiple downstream language models.

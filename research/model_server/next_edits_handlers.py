"""Server code that implements the NextEdit APIs."""

import copy
import json
import logging
from dataclasses import asdict, dataclass
from typing import Iterable, Union
import uuid

from base.logging.secret_logging import UnsafeLogger
from flask import jsonify, stream_with_context

from base.diff_utils.diff_formatter import format_file_changes
from base.diff_utils.diff_utils import compute_file_diff
from base.diff_utils.edit_events import (
    group_edit_events,
    grouped_events_to_file_changes,
    reorder_merge_events,
    truncate_edit_events,
)
from base.diff_utils.str_diff import precise_char_diff, precise_line_diff
from base.ranges.line_map import LineMap
from base.ranges.range_types import LineRange
from base.retrieval.chunking.smart_chunking import expand_point_by_smart_chunks
from base.static_analysis.common import deduplicate, shorten_str
from research.core.changes import Changed
from research.core.diff_utils import File
from research.core.next_edit_location_prompt_input import (
    DiffSpan,
    FileLocation,
    NextEditLocationOutput,
    NextEditLocationSystemInput,
    ScoredFileHunk,
)
from research.core.types import Document, check_not_none
from research.eval.harness.systems.next_edit_combined_system import (
    NextEditCombinedSystem,
    NextEditCombinedSystemInput,
    NextEditStreamedEdit,
)
from research.eval.harness.systems.next_edit_gen_system import (
    EditGenSystemInput,
    NextEditGenSystem,
    SuggestInstructionInput,
)
from research.eval.harness.systems.next_edit_location_system import (
    BasicNextEditLocationSystem,
)
from research.eval.harness.systems.next_edit_reranker_system import (
    NextEditRerankerOutput,
    NextEditRerankerSystem,
)
from research.model_server.model_server_app import (
    ModelServerApp,
    _RecentChangeDebugInfo,
    apply_recent_changes,
)
from research.model_server.model_server_requests import (
    CharRange,
    NextEditCombinedRequest,
    NextEditCombinedResponse,
    NextEditGenRequest,
    NextEditGenResponse,
    NextEditInstructionRequest,
    NextEditInstructionResponse,
    NextEditLocationRequest,
    NextEditLocationResponse,
    NextEditResult,
    ScoredFileLocation,
)
from research.model_server.vcs_utils import convert_vcs_changes
from research.models.fastforward_models import GenerationCanceledError
from research.utils.repo_change_utils import is_training_source_file


background_filter_threshold = 0.7


def handle_next_edit_location_request(app: ModelServerApp):
    """Handle next edit request."""

    system = app.system
    if isinstance(system, NextEditCombinedSystem):
        system = system.edit_location_system
    assert isinstance(system, (BasicNextEditLocationSystem, NextEditRerankerSystem))

    request_content = app.get_request(NextEditLocationRequest)
    instruction = request_content.instruction or ""

    logging.info("~= " * 20 + "Next Edit Location Request" + " ~=" * 20)

    session_id = app.get_session_id()
    request_id = app.get_request_id()

    ctx_info = _lock_and_process_next_edit_context(app, request_content, system)
    if isinstance(ctx_info, _UnknownCheckpointError):
        unknown_checkpoint_id = ctx_info.unknown_checkpoint_id
        logging.warning(f"Canceled due to {unknown_checkpoint_id=}")
        short_response = NextEditLocationResponse(
            candidate_locations=[],
            unknown_blob_names=[],
            checkpoint_not_found=True,
            canceled=True,
            critical_errors=[f"Cancelled due to {unknown_checkpoint_id=}"],
        )
        return jsonify(short_response)

    restrict_to_file = None
    if request_content.is_single_file:
        restrict_to_file = request_content.path
    if restrict_to_file is not None:
        logging.info(f"Restricting to file: {restrict_to_file}")

    system_input = NextEditLocationSystemInput(
        current_file=File("", ""),
        edit_region=CharRange(0, 0),
        instruction=instruction,
        recent_changes=ctx_info.recent_changes,
        diagnostics=tuple(request_content.diagnostics),
        doc_ids=ctx_info.doc_ids,
        top_k=request_content.num_results,
        restrict_to_file=restrict_to_file,
    )

    # The inference host server is not thread-safe when we do caching, so we
    # avoid running replacement and completion in parallel
    with (
        app.system_lock,
        app._setup_logging("next_edit_location", session_id, request_id) as file_logger,
    ):
        if file_logger:
            ctx_info.rc_debug_info.log_to_file(file_logger)
            if ctx_info.vcs_errors:
                file_logger.log("VCS_errors.txt", "\n".join(ctx_info.vcs_errors))
        output = system.generate(system_input)
        if file_logger:
            output.log_to_file(file_logger)

    candidate_locations = []
    if isinstance(output, NextEditLocationOutput):
        candidate_locations = [
            ScoredFileLocation(
                item=loc.item,
                score=loc.score,
                debug_info="",
            )
            for loc in output.scored_candidates
        ]
    if isinstance(output, NextEditRerankerOutput):
        location_debug_info = {
            loc.item: f"original: score={loc.score:.4g}, rank={index}"
            for index, loc in enumerate(output.reranker_debug_info.before_reranking)
        }
        candidate_locations = [
            ScoredFileLocation(
                item=loc.item,
                score=loc.score,
                debug_info=location_debug_info.get(loc.item, "Unknown"),
            )
            for loc in output.scored_candidates
        ]
    # The type checker can't handle checking output, so we add this for safety.
    if len(candidate_locations) != len(output.scored_candidates):
        logging.error(
            f"Only found {len(candidate_locations)} changes instead of {len(output.scored_candidates)}."
        )

    response = NextEditLocationResponse(
        candidate_locations=candidate_locations,
        unknown_blob_names=ctx_info.unknown_blob_names,
        critical_errors=_get_critical_errors(ctx_info),
    )

    app.requests_handled += 1

    return jsonify(response)


def handle_next_edit_gen_request(app: ModelServerApp):
    """Handles a next edit generation request."""

    system = app.system
    if isinstance(system, NextEditCombinedSystem):
        system = system.edit_gen_system
    if not isinstance(system, NextEditGenSystem):
        app.abort(400, "Server does not support next edit generation.")

    # Prepare request
    request_content = app.get_request(NextEditGenRequest)
    request_content = _expand_selected_text(request_content)
    selected_range = CharRange(
        request_content.selection_begin_char, request_content.selection_end_char
    )

    session_id = app.get_session_id()
    request_id = app.get_request_id()

    foreground_key = ("foreground", session_id)
    background_key = ("background", session_id)
    if request_content.mode == "BACKGROUND":
        user_key = background_key
        to_cancel = {background_key}
    else:
        user_key = foreground_key
        to_cancel = {foreground_key, background_key}
    user_queue = app.user_request_queue
    num_canceled = user_queue.add_request(user_key, request_id, to_cancel)
    if num_canceled > 0:
        logging.info(f"Will cancel {num_canceled} existing requests.")

    ctx_info = _lock_and_process_next_edit_context(app, request_content, system)
    if not request_content.selected_text.endswith("\n"):
        logging.warning("Selected text does not end with a newline.")

    if isinstance(ctx_info, _UnknownCheckpointError):
        unknown_checkpoint_id = ctx_info.unknown_checkpoint_id
        logging.warning(f"Canceled due to {unknown_checkpoint_id=}")
        user_queue.finish_request(user_key, request_id)
        response = NextEditGenResponse(
            next_edit=NextEditResult(
                suggestion_id=uuid.uuid4().hex,
                path=request_content.path or "",
                blob_name=request_content.blob_name,
                char_start=selected_range.start,
                char_end=selected_range.stop,
                existing_code=request_content.selected_text,
                suggested_code=request_content.selected_text,
                diff_spans=[],
                change_description="",
                truncation_char=None,
            ),
            unknown_blob_names=[],
            checkpoint_not_found=True,
            canceled=True,
            critical_errors=[f"Cancelled due to {unknown_checkpoint_id=}"],
        )
        return jsonify(response)

    system_input = EditGenSystemInput(
        path=request_content.path or "",
        prefix=request_content.prefix,
        suffix=request_content.suffix,
        selected_code=request_content.selected_text,
        instruction=request_content.instruction,
        recent_changes=ctx_info.recent_changes,
        generate_description=request_content.mode == "BACKGROUND",
        doc_ids=ctx_info.doc_ids,
        must_change=request_content.mode == "FORCED",
    )

    if request_content.mode != "BACKGROUND":
        logging.info("~= " * 20 + "Next Edit Gen User Request" + " ~=" * 20)
        logging.debug(f"System input: {system_input.summary()}")
    else:
        logging.info(
            "~= " * 20
            + "Background Edit Gen Request"
            + " ~=" * 20
            + "\n(Request details omitted)"
        )

    if ctx_info.unknown_blob_names:
        logging.warning(f"Found {len(ctx_info.unknown_blob_names)} missing files.")

    # The inference host server is not thread-safe when we do caching, so we
    # guard the generation code using the completion lock
    with (
        app.system_lock,
        app._setup_logging("next_edit_gen", session_id, request_id) as logger,
    ):
        if logger:
            ctx_info.rc_debug_info.log_to_file(logger)
            if ctx_info.vcs_errors:
                logger.log("VCS_errors.txt", "\n".join(ctx_info.vcs_errors))
        try:
            system_output = system.generate(
                system_input,
                should_cancel=lambda: user_queue.should_cancel(user_key, request_id),
            )
            if logger:
                system_output.log_to_file(logger)
            if description_time_taken := system_output.debug_info.get(
                "change_description_time_taken"
            ):
                logging.info(f"Change description time taken: {description_time_taken}")
        except GenerationCanceledError:
            logging.warning(f"Generation canceled: {user_key}")
            return jsonify(
                NextEditGenResponse(
                    next_edit=NextEditResult(
                        suggestion_id=uuid.uuid4().hex,
                        path=request_content.path or "",
                        blob_name=request_content.blob_name,
                        char_start=selected_range.start,
                        char_end=selected_range.stop,
                        existing_code=request_content.selected_text,
                        suggested_code=request_content.selected_text,
                        diff_spans=[],
                        change_description="",
                        truncation_char=None,
                    ),
                    unknown_blob_names=ctx_info.unknown_blob_names,
                    canceled=True,
                )
            )
        finally:
            user_queue.finish_request(user_key, request_id)

    if (
        request_content.mode == "BACKGROUND"
        and check_not_none(system_output.prob_changed) < background_filter_threshold
    ):
        logging.warning("Filtering out background edit.")
        generated_text = system_input.selected_code
    else:
        generated_text = system_output.replacement

    change_prob_text = ""
    if system_output.prob_changed is not None:
        change_prob_text = f", prob_changed={system_output.prob_changed:.1%}"
    confidence_text = ""
    if system_output.changed:
        confidence_text = f", change_confidence={system_output.change_confidence:.1%}"
    logging.info(f"changed={system_output.changed}{change_prob_text}{confidence_text}")

    diff = precise_char_diff(system_input.selected_code, generated_text)
    spans = diff.span_ranges()

    if output_tokens := system_output.debug_info.get("output_tokens"):
        logging.info(f"Model raw output: ({request_id=}):\n{output_tokens}")
    if change_description := system_output.change_description:
        logging.info(f"Change description: {change_description}")

    diff_spans = [DiffSpan(original=span[0], updated=span[1]) for span in spans]
    response = NextEditGenResponse(
        next_edit=NextEditResult(
            suggestion_id=uuid.uuid4().hex,
            path=request_content.path or "",
            blob_name=request_content.blob_name,
            char_start=selected_range.start,
            char_end=selected_range.stop,
            existing_code=request_content.selected_text,
            suggested_code=generated_text,
            diff_spans=diff_spans,
            truncation_char=system_output.truncation_char,
            change_description=system_output.change_description,
        ),
        unknown_blob_names=ctx_info.unknown_blob_names,
        critical_errors=_get_critical_errors(ctx_info),
        truncation_char=system_output.truncation_char,
        change_description=system_output.change_description,
    )

    app.requests_handled += 1

    return jsonify(response)


def handle_next_edit_combined_request(app: ModelServerApp):
    """Handles a next edit request that combines the edit and localization model."""

    system = app.system
    if not isinstance(system, NextEditCombinedSystem):
        app.abort(400, "Server does not support next edit generation.")

    request_content: NextEditCombinedRequest = app.get_request(NextEditCombinedRequest)
    request_content.instruction = request_content.instruction or ""

    logging.info("~= " * 20 + "Next Edit Combined Request" + " ~=" * 20)

    session_id = app.get_session_id()
    request_id = app.get_request_id()
    foreground_key = ("foreground", session_id)
    background_key = ("background", session_id)
    user_key = foreground_key
    to_cancel = {foreground_key, background_key}

    # put a new request into the queue, potentially evict old requests
    user_queue = app.user_request_queue
    user_queue.add_request(user_key, request_id, to_cancel)

    ctx_info = _lock_and_process_next_edit_context(app, request_content, system)
    if isinstance(ctx_info, _UnknownCheckpointError):
        unknown_checkpoint_id = ctx_info.unknown_checkpoint_id
        logging.warning(f"Canceled due to {unknown_checkpoint_id=}")
        user_queue.finish_request(user_key, request_id)
        response = NextEditCombinedResponse(
            changed_file_regions=[],
            unknown_blob_names=[],
            checkpoint_not_found=True,
            canceled=True,
            critical_errors=[f"Cancelled due to {unknown_checkpoint_id=}"],
        )
        return jsonify(response)

    absolute_max_changes_to_attempt = 100

    max_changes_to_attempt = round(
        system.default_max_changes_to_attempt * request_content.search_factor
    ) + len(request_content.blocked_locations)
    if max_changes_to_attempt < 1:
        app.abort(400, "Invalid search factor.")
    if max_search_hit := max_changes_to_attempt > absolute_max_changes_to_attempt:
        logging.warning(
            f"{max_changes_to_attempt=} is greater than absolute_max_changes_to_attempt, "
            f"so setting max_changes_to_attempt to {absolute_max_changes_to_attempt}."
        )
        max_changes_to_attempt = absolute_max_changes_to_attempt

    restrict_to_file = None
    if request_content.is_single_file:
        restrict_to_file = request_content.path
    if restrict_to_file is not None:
        logging.info(f"Restricting to file: {restrict_to_file}")

    system_input = NextEditCombinedSystemInput(
        instruction=request_content.instruction,
        recent_changes=ctx_info.recent_changes,
        diagnostics=tuple(request_content.diagnostics),
        doc_ids=ctx_info.doc_ids,
        blocked_locations=frozenset(request_content.blocked_locations),
        max_changes_to_return=1,
        max_changes_to_attempt=max_changes_to_attempt,
        restrict_to_file=restrict_to_file,
        selection_begin_char=request_content.selection_begin_char,
        selection_end_char=request_content.selection_end_char,
    )
    logging.info(f"System input:\n{system_input.summary()}")

    # The inference host server is not thread-safe when we do caching, so we
    # guard the generation code using the completion lock
    with (
        app.system_lock,
        app._setup_logging("next_edit_combined", session_id, request_id) as file_logger,
    ):
        try:
            if file_logger:
                ctx_info.rc_debug_info.log_to_file(file_logger)
                if ctx_info.vcs_errors:
                    file_logger.log("VCS_errors.txt", "\n".join(ctx_info.vcs_errors))
            [system_output] = list(
                system.generate(
                    system_input,
                    should_cancel=lambda: user_queue.should_cancel(
                        user_key, request_id
                    ),
                ).generator
            )
            if file_logger:
                system_output.log_to_file(file_logger)
        finally:
            user_queue.finish_request(user_key, request_id)

    response = NextEditCombinedResponse(
        changed_file_regions=system_output.changed_file_regions,
        unknown_blob_names=ctx_info.unknown_blob_names,
        canceled=False,
        max_search_hit=max_search_hit,
        critical_errors=_get_critical_errors(ctx_info),
    )
    logging.info(f"Response ({request_id=}):\n{str(response)}")

    app.requests_handled += 1

    return jsonify(response)


def handle_next_edit_stream_request(app: ModelServerApp):
    request_content = app.get_request(NextEditGenRequest)
    request_content = _expand_selected_text(request_content)
    request_content.instruction = request_content.instruction or ""

    if request_content.scope == "CURSOR":
        # hand this to the edit gen request handler
        return handle_next_edit_gen_request(app)

    system = app.system
    if not isinstance(system, NextEditCombinedSystem):
        app.abort(400, "Server does not support next edit generation.")

    logging.info("~= " * 20 + "Next Edit Combined Streaming Request" + " ~=" * 20)

    session_id = app.get_session_id()
    request_id = app.get_request_id()
    foreground_key = ("foreground", session_id)
    background_key = ("background", session_id)
    if request_content.mode == "BACKGROUND":
        user_key = background_key
        to_cancel = {background_key}
    else:
        user_key = foreground_key
        to_cancel = {foreground_key, background_key}

    # put a new request into the queue, potentially evict old requests
    user_queue = app.user_request_queue
    user_queue.add_request(user_key, request_id, to_cancel)

    ctx_info = _lock_and_process_next_edit_context(app, request_content, system)
    if isinstance(ctx_info, _UnknownCheckpointError):
        unknown_checkpoint_id = ctx_info.unknown_checkpoint_id
        logging.warning(f"Canceled due to {unknown_checkpoint_id=}")
        response = NextEditCombinedResponse(
            changed_file_regions=[],
            unknown_blob_names=[],
            checkpoint_not_found=True,
            canceled=True,
            critical_errors=[f"Cancelled due to {unknown_checkpoint_id=}"],
        )
        user_queue.finish_request(user_key, request_id)
        return jsonify(response)
    vcs_critical_errors = _get_critical_errors(ctx_info)

    absolute_max_changes_to_attempt = 100

    max_changes_to_attempt = system.default_max_changes_to_attempt
    if max_changes_to_attempt < 1:
        app.abort(400, "Invalid search factor.")
    if max_changes_to_attempt > absolute_max_changes_to_attempt:
        logging.warning(
            f"{max_changes_to_attempt=} is greater than absolute_max_changes_to_attempt, "
            f"so setting max_changes_to_attempt to {absolute_max_changes_to_attempt}."
        )
        max_changes_to_attempt = absolute_max_changes_to_attempt

    restrict_to_file = None
    if request_content.scope == "FILE":
        restrict_to_file = request_content.path
    if restrict_to_file is not None:
        logging.warning(f"Restricting to file: {restrict_to_file}")
        if request_content.mode == "BACKGROUND":
            # at most try 10 edits in background single-file mode
            max_changes_to_attempt = min(max_changes_to_attempt, 10)

    # The inference host server is not thread-safe when we do caching, so we
    # guard the generation code using the completion lock
    with (
        app.system_lock,
        app._setup_logging("next_edit_combined", session_id, request_id) as file_logger,
    ):
        all_docs = system.get_documents(ctx_info.doc_ids)
        path_to_doc = {doc.path: doc for doc in all_docs}
        blocked_line_locations = list[FileLocation]()
        for loc in request_content.blocked_locations:
            doc = path_to_doc[loc.path]
            lmap = LineMap(doc.text)
            line_start = lmap.get_line_number(loc.char_start)
            line_end = lmap.get_line_number(loc.char_end)
            if line_end == line_start:
                # ensures that we at least block one line
                line_end += 1
            blocked_line_locations.append(
                FileLocation(
                    path=loc.path,
                    range=LineRange(line_start, line_end),
                )
            )
        system_input = NextEditCombinedSystemInput(
            instruction=request_content.instruction,
            recent_changes=ctx_info.recent_changes,
            diagnostics=request_content.diagnostics,
            doc_ids=ctx_info.doc_ids,
            blocked_locations=frozenset(blocked_line_locations),
            max_changes_to_return=system.max_changes_to_return,
            max_changes_to_attempt=max_changes_to_attempt,
            restrict_to_file=restrict_to_file,
            selection_begin_char=request_content.selection_begin_char,
            selection_end_char=request_content.selection_end_char,
        )
        logging.info(f"System input:\n{system_input.summary()}")
        try:
            if file_logger:
                ctx_info.rc_debug_info.log_to_file(file_logger)
                if ctx_info.vcs_errors:
                    file_logger.log("VCS_errors.txt", "\n".join(ctx_info.vcs_errors))
            system_output = system.generate(
                system_input,
                should_cancel=lambda: user_queue.should_cancel(user_key, request_id),
            )
        except GenerationCanceledError:
            logging.warning(f"Initial generation canceled: {user_key}")
            user_queue.finish_request(user_key, request_id)
            return jsonify(
                NextEditGenResponse(
                    next_edit=NextEditResult(
                        suggestion_id=uuid.uuid4().hex,
                        path=request_content.path or "",
                        blob_name=request_content.blob_name,
                        char_start=request_content.selection_begin_char,
                        char_end=request_content.selection_end_char,
                        existing_code=request_content.selected_text,
                        suggested_code=request_content.selected_text,
                        diff_spans=[],
                        change_description="",
                        truncation_char=None,
                    ),
                    unknown_blob_names=ctx_info.unknown_blob_names,
                    canceled=True,
                )
            )

    app.requests_handled += 1
    result_id = 0

    def log_changed_region(region: ScoredFileHunk):
        nonlocal result_id
        path = region.file_location.path
        diff_str = compute_file_diff(
            File(path, region.original_code),
            File(path, region.updated_code),
            use_smart_header=False,
        )
        logging.info(
            f"Changed region [{result_id}]: {path}:{region.file_location.range}\n"
            f"Editing score: {region.editing_score:.4g}, location score: {region.localization_score:.4g}\n"
            f"Diff shown below:\n{diff_str}"
        )
        result_id += 1

    def edit_to_responses(
        streamed_edit: NextEditStreamedEdit,
        path_to_doc: dict[str, Document],
        split_into_hunks: bool,
    ) -> Iterable[NextEditGenResponse]:
        for region in streamed_edit.changed_file_regions:
            # Convert from the combined system's line-based result
            # to the generation system's char-based result.
            cur_doc = path_to_doc[region.file_location.path]
            lmap = LineMap(cur_doc.text)
            char_start = lmap.get_char_index(region.file_location.range.start)
            char_end = lmap.get_char_index(region.file_location.range.stop)

            if region.original_code != region.updated_code:
                log_changed_region(region)

            if region.editing_score < background_filter_threshold:
                logging.warning(
                    f"Filtering out background edit: {region.file_location.path}:{region.file_location.range}"
                )
                continue

            span_ranges = precise_char_diff(
                region.original_code, region.updated_code
            ).span_ranges()
            spans = [DiffSpan(before, after) for before, after in span_ranges]
            result = NextEditResult(
                suggestion_id=uuid.uuid4().hex,
                path=region.file_location.path,
                blob_name=cur_doc.id,
                char_start=char_start,
                char_end=char_end,
                existing_code=region.original_code,
                suggested_code=region.updated_code,
                diff_spans=spans,
                truncation_char=region.truncation_char,
                change_description=region.change_description,
            )
            if split_into_hunks:
                hunks = _split_changes_into_hunks(result)
            else:
                hunks = [result]

            for result_hunk in hunks:
                response = NextEditGenResponse(
                    next_edit=result_hunk,
                    unknown_blob_names=ctx_info.unknown_blob_names,
                    critical_errors=vcs_critical_errors,
                    truncation_char=result_hunk.truncation_char,
                    change_description=result_hunk.change_description,
                )
                yield response

    def generator():
        try:
            while True:
                with (
                    app.system_lock,
                    app._setup_logging(
                        "next_edit_combined", session_id, request_id
                    ) as file_logger,
                ):
                    result = next(system_output.generator, None)
                    if result is None:
                        break

                    if file_logger:
                        # TODO: We currently clobber the combined results here
                        # (but not the generation subresults).
                        result.log_to_file(file_logger)

                for response in edit_to_responses(
                    result,
                    path_to_doc,
                    split_into_hunks=True,
                ):
                    yield json.dumps(asdict(response)) + "\n"
        except GenerationCanceledError:
            logging.warning(f"Generation canceled: {user_key}")
        user_queue.finish_request(user_key, request_id)
        logging.info("Generator finished")

    return stream_with_context(generator())


def _split_changes_into_hunks(
    change: NextEditResult,
    n_context_lines: int = 3,
) -> Iterable[NextEditResult]:
    """Split a change diff into multiple hunks."""
    assert n_context_lines > 0, "context_lines must be positive"
    line_level_hunks = precise_line_diff(
        change.existing_code, change.suggested_code
    ).group_into_hunks(n_context_lines - 1)

    truncation_char_abs = None
    if change.truncation_char is not None:
        truncation_char_abs = change.char_start + change.truncation_char

    for hunk, hunk_span_before in zip(
        line_level_hunks.spans, line_level_hunks.span_ranges_in_before
    ):
        truncation_char_relative = None
        if (
            truncation_char_abs is not None
            and hunk_span_before.start <= truncation_char_abs <= hunk_span_before.stop
        ):
            truncation_char_relative = truncation_char_abs - hunk_span_before.start
        span_ranges = precise_char_diff(hunk.before, hunk.after).span_ranges()
        spans = [DiffSpan(before, after) for before, after in span_ranges]
        yield NextEditResult(
            path=change.path,
            blob_name=change.blob_name,
            char_start=change.char_start + hunk_span_before.start,
            char_end=change.char_start + hunk_span_before.stop,
            existing_code=hunk.before,
            suggested_code=hunk.after,
            diff_spans=spans,
            truncation_char=truncation_char_relative,
            change_description=change.change_description,
            suggestion_id=uuid.uuid4().hex,
        )


def handle_next_edit_instruction_request(app: ModelServerApp):
    """Suggest an instruction based on the diffs in the request."""
    system = app.system
    if isinstance(system, NextEditCombinedSystem):
        system = system.edit_gen_system
    if not isinstance(system, NextEditGenSystem):
        app.abort(400, "Server does not support next edit generation.")

    request_content = app.get_request(NextEditInstructionRequest)

    ctx_info = _lock_and_process_next_edit_context(app, request_content, system)
    if isinstance(ctx_info, _UnknownCheckpointError):
        unknown_checkpoint_id = ctx_info.unknown_checkpoint_id
        logging.warning(f"Canceled due to {unknown_checkpoint_id=}")
        instruction = request_content.instruction + "\n(Canceled)"
        response = NextEditInstructionResponse(instruction=instruction)
        return jsonify(response)

    sys_input = SuggestInstructionInput(
        instruction_prefix=request_content.instruction,
        recent_changes=ctx_info.recent_changes,
    )
    session_id = app.get_session_id()
    request_id = app.get_request_id()
    with (
        app.system_lock,
        app._setup_logging(
            "next_edit_get_instruction", session_id, request_id
        ) as file_logger,
    ):
        output = system.suggest_instruction(sys_input)
        if file_logger:
            output.log_to_file(file_logger)
            ctx_info.rc_debug_info.log_to_file(file_logger)
            if ctx_info.vcs_errors:
                file_logger.log("VCS_errors.txt", "\n".join(ctx_info.vcs_errors))

    response = NextEditInstructionResponse(instruction=output.instruction)
    logging.info("Generated instruction:\n" + response.instruction)

    app.requests_handled += 1
    return jsonify(response)


def handle_resolve_next_edit_request(app: ModelServerApp):
    """Handle next edit resolution request."""
    # Silently ignore these, since they're only meaningful in services.
    return ""


def handle_record_next_edit_user_event_request(app: ModelServerApp):
    """Handle next edit user event request."""
    # Silently ignore these, since they're only meaningful in services.
    return ""


def handle_record_next_edit_session_event(app: ModelServerApp):
    """Handle next edit session event request."""
    # Silently ignore these, since they're only meaningful in services.
    return ""


NextEditRequestUnion = Union[
    NextEditLocationRequest,
    NextEditCombinedRequest,
    NextEditInstructionRequest,
    NextEditGenRequest,
]
NextEditSystemUnion = Union[
    NextEditCombinedSystem,
    NextEditGenSystem,
    BasicNextEditLocationSystem,
    NextEditRerankerSystem,
]


@dataclass
class _NextEditContext:
    """Context info needed by next edit handlers."""

    doc_ids: frozenset[str]
    recent_changes: tuple[Changed[File], ...]
    rc_debug_info: _RecentChangeDebugInfo
    vcs_errors: list[str]
    unknown_blob_names: list[str]


@dataclass
class _UnknownCheckpointError:
    unknown_checkpoint_id: str


USE_VCS_CHANGES: bool = True


def _lock_and_process_next_edit_context(
    app: ModelServerApp,
    request_content: NextEditRequestUnion,
    system: NextEditSystemUnion,
) -> _UnknownCheckpointError | _NextEditContext:
    """This function may lock the system_lock when performing some operations."""
    doc_ids, unknown_checkpoint_id = app._convert_blobs(request_content.blobs)
    if unknown_checkpoint_id is not None:
        return _UnknownCheckpointError(unknown_checkpoint_id)

    unknown_blob_names = app.find_missing(doc_ids)

    # TODO: remove this when we have properly fixed the recent chunk reconstruction bug.
    # The current file being edited by the user often fails to reconstruct due to
    # invalid recent chunks, so we always add the current doc (obtained by
    # concatenating prefix, selected code, and suffix) as an additional document
    # available for vcs reconstruction. And when processing each vcs entry, we first
    # check if the current_blob_name is already available in the backend, and if so,
    # directly use it and skip the reconstruction logic.
    if isinstance(request_content, NextEditGenRequest):
        current_doc = Document.new(
            text=(
                request_content.prefix
                + request_content.selected_text
                + request_content.suffix
            ),
            path=request_content.path or "",
        )
        with app.system_lock:
            system.add_docs([current_doc])

    with app.system_lock:
        doc_ids, new_docs, rc_info = apply_recent_changes(
            request_content.recent_changes,
            doc_ids,
            system,
            system.get_documents,
        )

    conversion_result = None
    if USE_VCS_CHANGES:
        if request_content.vcs_change is None:
            raise ValueError("No VCS change found in the request.")

        with app.system_lock:
            conversion_result = convert_vcs_changes(
                app.system_lock,
                system.get_documents,
                new_docs,
                request_content.vcs_change.working_directory_changes,
            )
        unknown_blob_names += conversion_result.unknown_blob_names
        unknown_blob_names = deduplicate(unknown_blob_names)

    def get_file(doc_id: str) -> File | None:
        # first check new_docs, then fall back to system.get_documents
        if doc_id in new_docs:
            return File(new_docs[doc_id].path, contents=new_docs[doc_id].text)
        match system.get_documents([doc_id]):
            case [doc]:
                return File(doc.path, contents=doc.text)
            case _:
                return None

    path_to_current_blob = {
        event.path: event.after_blob_name for event in request_content.edit_events
    }
    path_to_current_text = {
        path: doc.contents
        for path, blob_name in path_to_current_blob.items()
        if (doc := get_file(blob_name))
    }

    filtered_events = truncate_edit_events(
        request_content.edit_events,
        max_total_changed_chars=5000,
        is_source_file=is_training_source_file,
        logger=UnsafeLogger(),
    )
    groupped_events = group_edit_events(
        filtered_events,
        group_sizes=[1, 100],
        big_event_lines=8,
        logger=UnsafeLogger(),
    )
    recency_changes = grouped_events_to_file_changes(
        groupped_events, path_to_current_text, logger=UnsafeLogger()
    ).changes
    num_events = len(filtered_events)
    if recency_changes:
        recency_diffs = format_file_changes(recency_changes, diff_context_lines=3)
        recency_diffs = shorten_str(recency_diffs, max_len=4000)
        logging.info(
            f"Converted {num_events} edit events to {len(groupped_events)} groups ("
            f"{len(recency_changes)} file changes). Diffs shown below:\n{recency_diffs}"
        )
    else:
        logging.info(f"Converted {num_events} edit events to 0 file changes.")

    if conversion_result:
        # simply append recency changes to the vcs changes
        recent_changes = conversion_result.file_changes + recency_changes
    else:
        recent_changes = recency_changes

    return _NextEditContext(
        doc_ids=doc_ids,
        recent_changes=tuple(recent_changes),
        rc_debug_info=rc_info,
        vcs_errors=conversion_result.error_messages if conversion_result else [],
        unknown_blob_names=unknown_blob_names,
    )


def _expand_selected_text(request: NextEditGenRequest) -> NextEditGenRequest:
    """Expand the selected text by smart chunks."""
    if request.prefix_begin != 0:
        # the expansion may not work well if the prefix got truncated
        logging.warning(f"{request.prefix_begin=} is not 0.")

    original_range = CharRange(
        len(request.prefix),
        len(request.prefix) + len(request.selected_text),
    )
    selected_range = original_range
    all_text = request.prefix + request.selected_text + request.suffix
    if selected_range.is_point():
        # expand by smart chunks
        chunk_size = 2000
        selected_range = expand_point_by_smart_chunks(
            all_text,
            selected_range.start,
            chunk_size,
        )
    # always expand the selected range to line boundaries
    lmap = LineMap(all_text)
    lrange = lmap.crange_to_lrange(selected_range)
    selected_range = lmap.lrange_to_crange(lrange)

    # update the request using the new selection range
    result = copy.copy(request)
    result.prefix = all_text[: selected_range.start]
    result.suffix = all_text[selected_range.stop :]
    result.selected_text = all_text[selected_range.to_slice()]
    result.selection_begin_char = selected_range.start
    result.selection_end_char = selected_range.stop

    if selected_range != original_range:
        logging.info(
            f"Expanded selection from {str(original_range)} to {str(selected_range)}."
        )

    return result


def _get_critical_errors(ctx_info: _NextEditContext) -> list[str]:
    """Get critical errors that should be shown in the front end."""
    critical_errors = []
    critical_errors.extend(ctx_info.vcs_errors)
    critical_errors.extend(error for _, error in ctx_info.rc_debug_info.bad_changes)
    critical_errors.extend(error for _, error in ctx_info.rc_debug_info.bad_blobs)
    return critical_errors

"""VCS utils.

Utilities for working with VCS changes.
"""

from dataclasses import dataclass
import logging
from pathlib import Path
import threading
from typing import Callable, Iterable, Mapping

from base.blob_names.python.blob_names import BlobName
from research.core.changes import Added, Changed, Deleted, Modified
from research.core.diff_utils import File
from research.core.types import Document, DocumentId
from research.core.utils import assert_eq
from research.model_server.model_server_requests import (
    WorkingDirectoryChange,
)
from research.utils.repo_change_utils import is_training_source_file


@dataclass
class VcsConversionResult:
    file_changes: list[Changed[File]]
    unknown_blob_names: set[DocumentId]
    error_messages: list[str]


def convert_vcs_changes(
    system_lock: threading.RLock,
    get_documents: Callable[[Iterable[DocumentId]], list[Document]],
    new_docs: Mapping[BlobName, Document],
    working_changes: list[WorkingDirectoryChange],
    is_source_file: Callable[[Path], bool] = is_training_source_file,
) -> VcsConversionResult:
    """Convert the working directory changes to a list of Changed[File].

    Args:
        get_documents: a function that fetches documents by blob names.
        new_docs: a mapping from indexed blob name to updated document (with\
            recent changes applied). Although the front end sends the updated blob\
            names, we use this mapping to perform extra checking to ensure that our\
            reconstruction matches what the front end wanted to send.
        working_changes: the working directory changes from the VCS.
    """

    def get_document(doc_id: DocumentId) -> Document | None:
        match get_documents([doc_id]):
            case [doc]:
                return doc
            case _:
                return None

    result = VcsConversionResult(
        file_changes=[],
        unknown_blob_names=set(),
        error_messages=[],
    )
    # filter out non-source changes
    working_changes = [
        wdc
        for wdc in working_changes
        if (wdc.after_path is None or is_source_file(Path(wdc.after_path)))
        and (wdc.before_path is None or is_source_file(Path(wdc.before_path)))
    ]

    # lock the system since we are accessing the documents
    with system_lock:
        file_names = [wc.after_path or wc.before_path for wc in working_changes]
        logging.info(f"Converting {len(working_changes)} working changes: {file_names}")
        for wdc in working_changes:
            convert_single_vcs_change(result, get_document, new_docs, wdc)
        changed_file_names = [c.get_later().path for c in result.file_changes]
        logging.info(
            f"Converted {len(result.file_changes)}/{len(working_changes)} working changes: "
            f"{changed_file_names}"
        )
        if len(result.error_messages) > 0:
            logging.warning(
                f"{len(result.error_messages)} conversion errors:\n"
                + "\n".join(result.error_messages)
            )
        return result


def convert_single_vcs_change(
    result: VcsConversionResult,
    get_document: Callable[[DocumentId], Document | None],
    new_docs: Mapping[BlobName, Document],
    wdc: WorkingDirectoryChange,
) -> None:
    """Convert the working directory change to a Changed[File] and add to the result."""
    if not wdc.head_blob_name:
        before_file = None
    else:
        before_doc = get_document(wdc.head_blob_name)
        if before_doc is None:
            result.error_messages.append(
                "Could not find the before document: "
                f"path={wdc.before_path}, blob_name={wdc.head_blob_name}"
            )
            result.unknown_blob_names.add(wdc.head_blob_name)
            return
        assert_eq(before_doc.path, wdc.before_path)
        before_file = File(before_doc.path, before_doc.text)

    if wdc.current_blob_name and (after_doc := get_document(wdc.current_blob_name)):
        # as an optimization, we can skip reconstructing the after file if it's already
        # available.
        after_file = File(after_doc.path, after_doc.text)
    elif not wdc.indexed_blob_name:
        after_file = None
    else:
        if get_document(wdc.indexed_blob_name) is None:
            result.error_messages.append(
                "Could not find the indexed document: "
                f"path={wdc.after_path}, blob_name={wdc.indexed_blob_name}"
            )
            result.unknown_blob_names.add(wdc.indexed_blob_name)
            return

        # new_docs is responsible for mapping indexed blob names to the current
        # version of the file. If an indexed blob is not in new_docs, then we
        # assume it has not been recently modified and is the same as the indexed blob.
        after_doc = new_docs.get(wdc.indexed_blob_name)
        if after_doc is None:
            after_doc = get_document(wdc.indexed_blob_name)
        if after_doc is None:
            result.error_messages.append(
                f"Could not find the after document: {wdc.after_path}"
            )
            # do not add current_blob_name as missing blobs since the front end
            # may not have it and may be relying on back end reconstruction.
            return
        if not wdc.current_blob_name:
            result.error_messages.append(
                f"current_blob_name missing for file {wdc.after_path}, cannot verify."
            )
        elif after_doc.id != wdc.current_blob_name:
            result.error_messages.append(
                "Reconstructed blob name didn't match the front end: "
                f"{wdc.after_path=}, {after_doc.id=}, {wdc.current_blob_name=}"
            )
            return
        assert_eq(after_doc.path, wdc.after_path)
        after_file = File(after_doc.path, after_doc.text)
    match before_file, after_file:
        case File(), File():
            file_change = Modified(before_file, after_file)
        case None, File():
            file_change = Added(after_file)
        case File(), None:
            file_change = Deleted(before_file)
        case _:
            raise ValueError(f"{type(before_file)=}, {type(after_file)=}")
    result.file_changes.append(file_change)

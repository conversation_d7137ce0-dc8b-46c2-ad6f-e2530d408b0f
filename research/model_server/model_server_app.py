from __future__ import annotations

import argparse
import functools
import logging
import threading
import time
from contextlib import contextmanager
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import (
    Callable,
    Iterable,
    Mapping,
    NewType,
    NoReturn,
    Optional,
    Sequence,
    TypeVar,
)

import flask
import pytz
from flask import Flask, abort, request
from unidiff import PatchSet

from base.blob_names.python.blob_names import BlobName
from base.ranges.range_types import IntRange
from base.static_analysis.common import groupby, replace_str
from research.core.types import Document, DocumentId
from research.core.utils import FileLogger, assert_eq
from research.eval.harness.systems.abs_system import AbstractSystem
from research.model_server.auth_token_db import AuthTokenDatabase
from research.model_server.blob_list_manager import (
    BlobListManager,
)
from research.model_server.file_store import FileStore
from research.model_server.model_server_requests import (
    <PERSON><PERSON><PERSON>,
    ReplacementText,
)
from research.model_server.multiuser_queue import MultiuserQueue
from research.model_server.replacement_handlers import Replace<PERSON><PERSON><PERSON><PERSON><PERSON>andler
from research.model_server.server_logging import _print_obj
from research.model_server.web_server_interface import ServerWebInterface
from research.retrieval.scorers.recency_scorer import RecencyExtra
from research.retrieval.types import DocumentIndex
from research.utils.dataclass_utils import fromdict

SessionId = NewType("SessionId", str)
RequestId = NewType("RequestId", str)


REQUEST_TOKEN_PREFIX = "Bearer "


class ModelServerApp(Flask):
    """The Flask App running in this script."""

    args: argparse.Namespace
    web_interface: ServerWebInterface
    file_store_map: dict[SessionId, FileStore]
    """Maps each session id to a FileStore."""
    system: AbstractSystem
    document_index: DocumentIndex
    log_dir: Path | None
    """The directory to log system outputs."""
    replacement_handler: Optional[ReplacementRequestHandler]
    requests_handled: int = 0
    latest_request_time: float

    completion_queue_lock: threading.RLock = threading.RLock()
    """Lock this when accessing the completion request queue."""

    system_lock: threading.RLock = threading.RLock()
    """Lock this when using the system."""

    file_store_map_lock: threading.RLock = threading.RLock()
    """Lock this when using the file store map.

    During nested acquisitions, acquire this first before acquiring system_lock.
    """

    last_request_was_replacement: bool = False
    recency_extra: Optional[RecencyExtra] = None

    user_request_queue: MultiuserQueue[tuple[str, SessionId], RequestId]
    """A multiuser queue for handling request cancelations.

    The user key is a tuple of (request_type, session_id). A new request with
    the same user key will cancel any pending request with the same key.
    """

    # git-diff cache
    latest_git_diff_blob_names: Optional[list[str]] = None
    client_git_diff_patchset: Optional[PatchSet] = None

    use_extension_retrieval: bool
    blob_list_mgr: BlobListManager
    auth_token_db: Optional[AuthTokenDatabase] = None
    system_config_str: Optional[str] = None
    """The configuration str for the system, which is used for logging."""

    def find_missing(self, file_ids: Iterable[str]) -> list[str]:
        """Return which of the given file IDs are not indexed by the system."""
        with self.system_lock:
            current_ids = self.system.get_doc_ids()
        missing_ids = set(file_ids) - current_ids
        return list(missing_ids)

    def get_file_store(self) -> FileStore:
        """Get the file store for the user id in the request.

        If the file store already exists for the session ID, it will be
        loaded from disk. Otherwise, a new file store will be created and saved.
        All new files in the file store will be added to the system.
        """
        uid = self.get_session_id()
        if uid is None:
            logging.warning("No session id found, using default file store.")
            uid = SessionId("default")
        args = self.args
        with self.file_store_map_lock:
            if uid not in self.file_store_map:
                logging.info(f"Creating new file store for user: {uid}")
                file_store_dir = (
                    args.uploaded_files_store_path
                    / "model_server_file_store"
                    / f"port_{args.port}"
                )
                file_store = FileStore(file_store_dir / uid)
                self.file_store_map[uid] = file_store
                stored_files = file_store.get_documents()
                with self.system_lock:
                    try:
                        indexed_ids = self.system.get_doc_ids()
                    except NotImplementedError:
                        logging.error(
                            "System did not implement get_doc_ids(). Assuming nothing has been indexed."
                        )
                        indexed_ids = set()
                    to_add = [doc for doc in stored_files if doc.id not in indexed_ids]
                    logging.info(
                        f"Loaded {len(stored_files)} uploaded files, adding {len(to_add)} of them to system"
                    )
                    self.system.add_docs(to_add)
                return file_store
            else:
                return self.file_store_map[uid]

    @staticmethod
    def get_request(
        request_type: type[_RequestT], print_raw_request: bool = False
    ) -> _RequestT:
        """Get the current request and parse it as the given type."""
        if print_raw_request:
            _print_obj(request.get_json())

        content = _parse_json_request(request.get_json(), request_type)
        assert isinstance(content, request_type)
        return content

    def get_user_id(self) -> Optional[str]:
        """Get the user id from the request."""
        auth = request.headers.get("Authorization", None)
        if auth is None:
            return None
        if not auth.startswith(REQUEST_TOKEN_PREFIX):
            return None
        token = auth[len(REQUEST_TOKEN_PREFIX) :]
        if self.auth_token_db is not None:
            return self.auth_token_db.get_user_id(token)
        return None

    @staticmethod
    def abort(code: int, message: str = "") -> NoReturn:
        """Abort the request with the given code and message."""
        logging.error(f"Aborting with code {code}. {message=}")
        flask.abort(code, description=message)

    @staticmethod
    def get_session_id() -> SessionId:
        """Get the user id from the request."""
        sid = request.headers.get("x-request-session-id", None)
        if sid is None:
            raise ValueError("No session id found")
        return SessionId(sid)

    @staticmethod
    def get_request_id() -> RequestId:
        """Get the user id from the request."""
        rid = request.headers.get("x-request-id", None)
        if rid is None:
            raise ValueError("No request id found")
        return RequestId(rid)

    def _convert_blobs(self, blobs: Blobs | None) -> tuple[frozenset[str], str | None]:
        """Returns (doc_ids, unknown_checkpoint_id)"""
        if blobs is None:
            logging.warning("No blob list found in the request.")
            return frozenset(), None
        doc_ids = self.blob_list_mgr.return_flat_blob_list(blobs)
        if doc_ids is None:
            unknown_checkpoint_id = blobs.checkpoint_id
            logging.warning(f"Unknown checkpoint ID: {unknown_checkpoint_id}")
            return frozenset(), unknown_checkpoint_id
        else:
            return doc_ids, None

    def ensure_authenticated(self, fn):
        """Ensure request is "authenticated"."""

        @functools.wraps(fn)
        def wrapper(*args, **kwargs):
            if not (
                auth := request.headers.get("Authorization")
            ) or not auth.startswith(REQUEST_TOKEN_PREFIX):
                logging.error("Unauthorized request (no token), rejecting with 403")
                abort(403)
            token = auth[len(REQUEST_TOKEN_PREFIX) :]
            if (
                self.auth_token_db is not None
                and self.auth_token_db.get_user_id(token) is None
            ):
                logging.error(
                    "Unauthorized request (token is not allow listed), rejecting with 403"
                )
                abort(403)
            return fn(*args, **kwargs)

        return wrapper

    def _truncate_prefix_suffix(self, prefix: str, suffix: str) -> tuple[str, str]:
        max_prefix_len = self.args.suggested_prefix_char_count
        max_suffix_len = self.args.suggested_suffix_char_count
        assert max_prefix_len > 0
        assert max_suffix_len > 0

        if len(prefix) > max_prefix_len:
            logging.warning(
                (
                    "Client sent prefix longer than "
                    "suggested_prefix_char_count=%d, truncating"
                ),
                max_prefix_len,
            )
            prefix = prefix[len(prefix) - max_prefix_len :]
            assert_eq(len(prefix), max_prefix_len)

        if len(suffix) > max_suffix_len:
            logging.warning(
                (
                    "Client sent suffix longer than "
                    "suggested_suffix_char_count=%d, truncating"
                ),
                max_suffix_len,
            )
            suffix = suffix[:max_suffix_len]
            assert_eq(len(suffix), max_suffix_len)

        return prefix, suffix

    @contextmanager
    def _setup_logging(
        self,
        endpoint: str,
        session_id: SessionId,
        request_id: RequestId,
        skip_logging=False,
    ):
        logging.info("Generating...")
        start_time = time.time()

        logger = None
        if self.log_dir is not None and not skip_logging:
            all_logs_file = (
                self.log_dir / f"session-{session_id}" / f"{endpoint}-requests.txt"
            )
            all_logs_file.parent.mkdir(exist_ok=True, parents=True)
            california_time = datetime.now(_california_tz)
            with all_logs_file.open("a", encoding="utf-8") as f:
                f.write(f"{request_id}\t({california_time})\n")
            logger = FileLogger(
                self.log_dir / f"session-{session_id}" / endpoint / request_id
            )
            logger.log("request_time.txt", str(california_time))
        yield logger

        time_taken_str = (
            f"({endpoint}) Generation took {time.time() - start_time:.2f} seconds"
        )
        logging.info(time_taken_str)
        if logger is not None:
            logger.log("time_taken.txt", time_taken_str)
            # print the log dir to a newline to reduce warping
            logging.info(f"({endpoint}) Logs saved to:\n{logger.log_dir}")


_RequestT = TypeVar("_RequestT")

_california_tz = pytz.timezone("America/Los_Angeles")


def apply_recent_changes(
    recent_changes: list[ReplacementText],
    blobs: frozenset[str],
    system: AbstractSystem,
    get_docs: Callable[[list[str]], list[Document]],
) -> tuple[frozenset[str], Mapping[BlobName, Document], _RecentChangeDebugInfo]:
    """Apply recent changes to the blobs.

    Returns (new_blob_set, new_blobs, debug_info) where new_blobs maps each
    modified blob's indexed blob name to its current document.
    """

    def get_document(blob_id: str) -> Document | None:
        """Get the document from the system's retriever."""
        match get_docs([blob_id]):
            case [doc]:
                return doc
            case _:
                return None

    # Filter out recent changes that have already been applied.
    pending_recent_changes = tuple(
        filter(lambda x: not x.present_in_blob, recent_changes)
    )
    if not pending_recent_changes:
        empty_info = _RecentChangeDebugInfo(recent_changes, [], [], [])
        return blobs, dict(), empty_info
    blobs_set = set(blobs)
    # Group recent changes by blob name so we can apply them all at once.
    grouped_changes = groupby(pending_recent_changes, lambda x: x.blob_name)
    new_docs = list[Document]()
    new_blobs = dict[BlobName, Document]()
    debug_info = _RecentChangeDebugInfo(
        recent_changes=sorted(
            pending_recent_changes, key=lambda x: (x.blob_name, x.char_start)
        ),
        bad_blobs=[],
        bad_changes=[],
        changes=[],
    )
    for blob_id, changes in grouped_changes.items():
        changes.sort(key=lambda x: x.char_start)

        change_path = changes[0].path
        changes_str = _show_recent_changes(changes)
        logging.info(
            f"Applying {len(changes)} change(s) to {change_path} ({blob_id=}): {changes_str}"
        )
        if blob_id not in blobs_set:
            debug_info.bad_blobs.append((blob_id, f"{change_path=} not in blob list."))
            continue
        doc = get_document(blob_id)
        if doc is None:
            debug_info.bad_blobs.append((blob_id, f"{change_path=} not in retriever."))
            continue
        for change in changes:
            if change.path != doc.path:
                debug_info.bad_changes.append(
                    (change, f"{change_path=} != {doc.path=}")
                )
                continue
            if change.char_start > change.char_end:
                debug_info.bad_changes.append((change, f"Invalid range {change=}"))
                continue
        new_text = replace_str(
            doc.text,
            [(IntRange(x.char_start, x.char_end), x.replacement_text) for x in changes],
        )
        new_doc = Document.new(text=new_text, path=doc.path)
        new_blobs[blob_id] = new_doc
        if new_text == doc.text:
            debug_info.bad_blobs.append((blob_id, f"{change_path=} has same text."))
            continue
        if new_doc.id == doc.id:
            debug_info.bad_blobs.append((blob_id, f"{change_path=} has same doc id."))
            continue
        if (
            changes[0].expected_blob_name
            and new_doc.id != changes[0].expected_blob_name
        ):
            debug_info.bad_blobs.append(
                (
                    blob_id,
                    f"{change_path=} has unexpected blob name: "
                    f"{new_doc.id=} != {changes[0].expected_blob_name=}",
                )
            )
            continue
        result_str = f"Applied {len(changes)} recent changes to {doc.path}: {doc.id} -> {new_doc.id}"
        logging.info(result_str)
        debug_info.changes.append(result_str)
        new_docs.append(new_doc)
        blobs_set.remove(blob_id)
        blobs_set.add(new_doc.id)
        # Note that we do not add this new doc to the file store as that does GC
        # and we don't want to remove the original blob, as the extension may
        # keep sending it.
    system.add_docs(new_docs)
    return frozenset(blobs_set), new_blobs, debug_info


@dataclass
class _RecentChangeDebugInfo:
    recent_changes: Sequence[ReplacementText]
    bad_blobs: list[tuple[DocumentId, str]]
    bad_changes: list[tuple[ReplacementText, str]]
    changes: list[str]

    def log_to_file(self, file_logger: FileLogger):
        """Log the output via a FileLogger."""
        recent_changes_str = "\n".join(str(change) for change in self.recent_changes)
        changes_str = "\n".join(self.changes)
        all_text = (
            f"Recent changes:\n{recent_changes_str}\n\n" f"Changes:\n{changes_str}\n"
        )
        file_logger.log("recent_changes.txt", all_text)

        bad_blobs_str = "\n".join(
            f"({blob_name=}, {reason=})" for blob_name, reason in self.bad_blobs
        )
        bad_changes_str = "\n".join(
            f"({str(change)}, {reason=})" for change, reason in self.bad_changes
        )
        all_bad_text = (
            f"Bad blobs:\n{bad_blobs_str}\n\n" f"Bad changes:\n{bad_changes_str}\n\n"
        )
        file_logger.log("recent_changes_errors.txt", all_bad_text)


def _show_recent_changes(changes: list[ReplacementText]) -> str:
    change_strs = list[str]()
    for change in changes:
        location = change.char_start
        size = change.char_end - change.char_start
        new_size = len(change.replacement_text)
        change_strs.append(f"Replacement({location=}, {size=}, {new_size=})")
    return ", ".join(change_strs)


def _parse_json_request(json_request: dict, request_dataclass: type):
    """Parse an incoming JSON request, create a dataclass and return it.

    The request should contain all required fields. The request may contain
    additional fields that are not in the dataclasses: such fields will be
    ignored.

    Args:
        json_request: The JSON content of the request as returned by request.get_json().
        request_dataclass: The dataclass contained the structured request.

    Returns:
        An object of type request_dataclass.
    """
    return fromdict(request_dataclass, _ignore_extra=True, **json_request)

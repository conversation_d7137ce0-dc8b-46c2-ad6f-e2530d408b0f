import logging
import time
from dataclasses import asdict, dataclass
from datetime import datetime
from pathlib import Path

from flask import jsonify  # type: ignore

from base.prompt_format.common import Exchange

from research.core.chat_prompt_input import ResearchChatPromptInput
from research.core.model_input import ChatInput, DialogTurn, ModelInput
from research.model_server.code_instruct_handlers import save_code_edit_data_json
from research.model_server.model_server_app import ModelServerApp, apply_recent_changes
from research.model_server.model_server_requests import ChatRequest, ChatResponse
from research.model_server.server_logging import print_request
from research.model_server.vcs_utils import convert_vcs_changes

GLOBAL_CHAT_LOG_DIR = Path("/mnt/efs/hdc/chat-logs")


@dataclass
class _UnknownCheckpointError:
    unknown_checkpoint_id: str


def handle_chat_request(app: ModelServerApp):
    """Handle chat request."""

    start_time = time.time()
    system = app.system

    # Prepare request
    request_content = app.get_request(ChatRequest)

    logging.info("Chat Request:")
    print_request(request_content, skip_keys=("blobs",))

    file_changes = None

    # TODO(guy) handle missing blobs, see next_edit_handlers.py for reference
    doc_ids, unknown_checkpoint_id = app._convert_blobs(request_content.blobs)
    if unknown_checkpoint_id is not None:
        return _UnknownCheckpointError(unknown_checkpoint_id)

    with app.system_lock:
        doc_ids, blob_name_map, rc_info = apply_recent_changes(
            request_content.recency_info_recent_changes,
            doc_ids,
            system,
            system.get_docs,
        )

    if request_content.vcs_change:
        vcs_changes = convert_vcs_changes(
            app.system_lock,
            system.get_docs,
            blob_name_map,
            request_content.vcs_change.working_directory_changes,
        )
        file_changes = vcs_changes.file_changes

    system_input = ResearchChatPromptInput(
        prefix=request_content.prefix or "",
        suffix=request_content.suffix or "",
        path=request_content.path or "",
        message=request_content.message,
        selected_code=request_content.selected_code or "",
        prefix_begin=request_content.prefix_begin or 0,
        suffix_end=request_content.suffix_end or 0,
        retrieved_chunks=[],
        chat_history=request_content.chat_history,
        doc_ids=doc_ids,
        recent_changes=file_changes,
    )

    # The inference host server is not thread-safe when we do caching, so we
    # avoid running replacement and completion in parallel
    with app.system_lock:
        logging.info("generating...")
        start = time.time()
        system_output = system.generate(system_input)
        generated_text, misc_dict = (
            system_output.generated_text,
            system_output.extra_output.additional_info,
        )
        elapsed = time.time() - start

        logging.debug(f"Response generation took {elapsed:.2f} seconds")

        response = ChatResponse(
            text=generated_text,
        )

        json_data = asdict(request_content)
        json_data.update(**misc_dict)
        json_data.update(
            {
                "response": generated_text,
                "timestamp": datetime.now().isoformat(),
                "time_cost_s": time.time() - start_time,
                "user_id": app.get_user_id(),
                "system_spec": app.system_config_str,
            }
        )
        if request_content.blobs:
            blob_set = app.blob_list_mgr.return_flat_blob_list(request_content.blobs)
            json_data["blobs"] = tuple(blob_set) if blob_set is not None else None

        # Log the user id as well
        # Save all the necessary information into a json file
        save_code_edit_data_json(
            json_data,
            app.get_request_id(),
            "-Call",
            global_log_dir=GLOBAL_CHAT_LOG_DIR,
        )

        app.requests_handled += 1

        return jsonify(response)

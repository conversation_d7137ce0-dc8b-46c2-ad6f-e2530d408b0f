# Model Serving

Models supported by the research library can be served to the VSCode extension using `launch_model_server.py`. This is a Flask server that supports serving the same protocol we use in production using any of our models. Retrieval is currently supported by pointing the model server to a local directory tree to retrieve code from (using any of our retrievers).

Example: To serve the recommended configuration, start an A40 or A100 instance. This can be either a Virtual Server with a dev container, or a reserved node. Then run:
```
> ./serve_model.sh
```
By default, the server only listens on localhost, for security reasons. Once the server is
up (this takes a few minutes), it will print out its URL, e.g.,
`http://127.0.0.1:5000`.

## Securely connecting to the research server

To connect the extension, open the extension settings and paste the URL in the
completion URL field.

There are several ways to obtain the URL and securely access the server:

1. Run your extension on the same host as the server, and point it to
`http://127.0.0.1:5000`.

2. Publish the server securely by following [these instructions](https://www.notion.so/Securely-publishing-a-research-model-server-d18f1fe3d343426e898532e84eb61b99?pvs=4).
The script described there will print out an HTTPS URL that you can use to connect
to the server from anywhere.

3. Run the server within CoreWeave, and connect to it from an extension running
on another CoreWeave host using the inernal `10.x.x.x` address (which is not a
public IP address). For example, if the server has IP address `********` you
can run the server with `--host ********`. You can then connect to the server
from an extension running inside CoreWeave with the URL `http://********:5000`.

4. Setup an SSH tunnel from another machine (e.g. your laptop), by running `ssh -L 5000:localhost:5000 YOUR_SERVER`.
   If you want the tunnel to run in the background, instead run `ssh -f -N -L 5000:localhost:5000 YOUR_SERVER`.
   Here, `YOUR_SERVER` is the server name you use to SSH into the server.

Once the URL is configured, the extension will send completion requests to your
server. You should see the completions appear in the extension, and the
requests show up in the server terminal.

The model server has various capabilities, `python launch_model_server.py --help` for a complete list.

## Serving systems

The server uses the notion of an `AbstractSystem` that generates completions, which is the same
concept we use for evaluation. A server can instantiate a system using a yaml config,
or using a system factory (a function that creates a AbstractSystem object). You can
see examples of yaml configs under the `research/model_server/configs` directory.

To launch a server with a system config, run:
```
python3 launch_model_server.py --system_yaml_config MY_CONFIG.yaml
```

To launch a server with a system factory, make sure you have a registered system factory:
```
@register_system_factory("my_factory")
def make_a_system(config: dict) -> AbstractSystem:
    # ... create the system object ...
    return system
```
Then launch the server:
```
python3 launch_model_server.py --system_factory_name my_factory
```

## Dynamic module loading

Sometimes, necessary components are defined in modules that do not get automatically
imported. This happens for example for modules that are under `experimental/`. Such
modules can be loaded dynamically using `--import_modules experimental.user.my_module ...`.

## Web interface

The research server has a web interface at http://127.0.0.1:8080 . It shows details about the prompt, and allows one to control the server while it's running.
The server is only listening on localhost for security reasons. There are two options to connect to the web interface:

1. Via VSCode: Connect to the remote instance where your server is running, run the "Simple Browser: Show" command to open the VSCode browser, and enter
   the URL http://127.0.0.1:8080 .

2. Setup an SSH tunnel from your laptop as above, by running `ssh -L 8080:localhost:8080 YOUR_SERVER`. If you want the tunnel to run in the background,
   instead run `ssh -f -N -L 8080:localhost:8080 YOUR_SERVER`.

# Advanced

## Prompting

An initial prompt can be supplied to the server using `--completion_preamble prompt.txt`. This prompt will be prepended to every completion request. The prompt file can be updated while the server is up, and the server will use the current version in every request. Prompts that are checked in should be placed in the `prompts/` directory.

## Replacement prompts

The extension also supports ([in-progress PR](https://github.com/augmentcode/augment/pull/756)) replacing a selected piece of text with model-generated text. The extension sends the model server a request that includes the prefix (text before the selection), suffix (text after the selection), and the selected text. The model responds with generated text, that the extension then uses to replace the selected text with.

In the server, this capability is enabled by supplying a "replacement prompt" and a "replacement config". The prompt is expected to have the following few-shot format:

```
[general comments]

[start_example_section]
prefix
[start_selection]
selected_text
[end_selection]
suffix

[start_replacement_section]
possible context lines (e.g. from the prefix)
[start_replacement]
replacement_text
[end_replacement]
possible context lines (e.g. from the suffix)

(additional such examples)
```

The model needs to be aware of some of this structure so it can construct its own examples based on the text in the request:
* `--replacement_preamble prompt.txt` specifies a few-shot prompt that simply gets prepended to replacement requests.
* `--replacement_config prompt_config.yml` defines the prompt structure fields (`start_selection`, `end_selection`, ...), and should match the structure used in the few-shot prompt:
** `start_selection`, `end_selection`: The text that defines the beginning and end of the selected text.
** `start_replacement`, `end_replacement`: The beginning and end of the generated replacement text.
** `start_example_section`: Beginning of the input example.
** `start_replacement_section`: Beginning of the model's output.

When a request comes in, the server constructs the model input by taking the prompt and appending a new example, constructed using the prefix, suffix, selected text, and configuration fields. It then uses the model to generate text until the next example starts, extracts the text between `start_replacements` and `end_replacement`, and sends this result back to the extension.

For an example of a replacement prompt, see `prompts/pseudocode_*`.

## Extensions

The file `language_extensions.json` contains a list of programming languages
along with their VSCode names and file extensions. It came from
https://gist.github.com/ppisarczyk/43962d06686722d26d176fad46879d41 .

The mapping to VSCode language names is from
https://code.visualstudio.com/docs/languages/identifiers .

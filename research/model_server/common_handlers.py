import json
import logging
from dataclasses import asdict
from pathlib import Path
from typing import Sequence

from flask import jsonify

from base.ranges.string_utils import shorten_str
from research.core.types import Document, DocumentId, compute_file_id
from research.core.utils_for_file import read_json
from research.model_server.model_server_app import Model<PERSON><PERSON>rApp
from research.model_server.model_server_requests import (
    BatchUploadRequest,
    BatchUploadResponse,
    BlobPayload,
    BlobsCheckpointRequest,
    BlobsCheckpointResponse,
    FindMissingRequest,
    KeyValuePair,
    MemorizeRequest,
)
from research.model_server.server_logging import print_request


def handle_get_models_request(app: ModelServerApp):
    """Handles the get-models API.

    The api informes the vscode extension about all models available.
    """
    supported_languages_from_args = app.args.languages

    def is_language_supported(lang: dict):
        if supported_languages_from_args is None:
            return True
        return lang["vscode_name"] in supported_languages_from_args

    language_data = read_json(Path(__file__).parent / "language_extensions.json")
    supported_languages = [
        {
            "name": lang["name"],
            "vscode_name": lang["vscode_name"],
            "extensions": lang["extensions"],
        }
        for lang in language_data
        if "vscode_name" in lang and is_language_supported(lang)
    ]

    if langs := app.system.supported_languages():
        # we filter the supported languages if the system provides a list
        supported_languages = [
            v for v in supported_languages if v["vscode_name"] in langs
        ]

    # files larger than this size will not be uploaded by the extension
    max_bytes_per_file = 100_000_000  # our current default

    # TODO(guy) Report all the models so we don't have to keep switching
    # the name in the extension. This should go away once model handling improves
    # in the extension
    model_names = ["research-model", "rogue-1B-single-16B-multi-line"]
    models = [
        {
            "name": name,
            "suggested_prefix_char_count": app.args.suggested_prefix_char_count,
            "suggested_suffix_char_count": app.args.suggested_suffix_char_count,
            # max file size to memorize
            "max_memorize_size_bytes": max_bytes_per_file,
            # completion timeout
            "completion_timeout_ms": app.args.completion_timeout_ms,
        }
        for name in model_names
    ]

    feature_flags: dict = {feature: True for feature in app.args.feature_flags}

    # git-diff needs a frequency
    if "git_diff" in feature_flags:
        del feature_flags["git_diff"]
        feature_flags["git_diff_polling_freq_msec"] = 2_000

    response = {
        "default_model": "research-model",
        "models": models,
        "languages": supported_languages,
        "feature_flags": feature_flags,
    }

    logging.info("response to get-models:")
    logging.info(json.dumps(response, indent=2))
    return jsonify(response)


def handle_find_missing_request(app: ModelServerApp):
    """Handles the find-missing API.

    The client sends us names (hashes) of files it has locally, and we need to
    report which ones don't exist in our DB.
    """
    request_content = app.get_request(FindMissingRequest)
    doc_ids = request_content.mem_object_names
    unknown_names: list[DocumentId] = []

    if app.use_extension_retrieval:
        # Only check missing doc ids if using extension retrieval logic.
        # Only include paths we want to retrieve.
        unknown_names = app.find_missing(doc_ids)

    # We index blobs upon startup or upload, so we never have blobs that are present
    # but not indexed. Hence nonindexed_blob_names is always empty.
    response = {
        "unknown_memory_names": unknown_names,
        "nonindexed_blob_names": [],
    }
    if unknown_names:
        logging.warning(f"find_missing response: {len(unknown_names)} missing doc ids")
    return jsonify(response)


def handle_memorize_request(app: ModelServerApp):
    """Handle content memorization request."""

    req: MemorizeRequest = app.get_request(MemorizeRequest)  # type: ignore

    log_message = (
        f"memorize request:\n"
        f"  model: {req.model}\n"
        f"  path: {req.path}\n"
        f"  content: {shorten_str(req.t)}\n"
        f"  blob_name: {req.blob_name}\n"
        f"  metadata: {req.metadata}"
    )
    logging.info(log_message)

    blob = BlobPayload(
        blob_name=req.blob_name, path=req.path, content=req.t, metadata=req.metadata
    )
    # Only ingest blob if we are using extension retrieval logic.
    if app.use_extension_retrieval:
        [blob_name] = add_blobs_to_system(app, [blob])
    else:
        blob_name = blob.blob_name

    result = jsonify({"mem_object_name": blob_name})
    return result


def handle_batch_upload_request(app: ModelServerApp):
    """Handle the batch upload of blobs request."""

    req: BatchUploadRequest = app.get_request(BatchUploadRequest)  # type: ignore

    logging.info("batch upload request:")
    print_request(req, skip_keys=("blobs",))

    # Only ingest blob if we are using extension retrieval logic.
    if app.use_extension_retrieval:
        ids = add_blobs_to_system(app, req.blobs)
    else:
        ids = [x.blob_name for x in req.blobs]

    result = jsonify(asdict(BatchUploadResponse(ids)))
    return result


def handle_checkpoint_request(app: ModelServerApp):
    """Handle checkpoint request."""

    # Prepare request
    request_content = app.get_request(BlobsCheckpointRequest)
    logging.info("checkpoint request:")
    print_request(request_content.blobs, len_threshold=64)

    blobs = request_content.blobs

    checkpoint_id = app.blob_list_mgr.checkpoint(blobs)
    if checkpoint_id is None:
        app.abort(413, "Server does not have the starting checkpoint id.")

    assert checkpoint_id is not None
    response = BlobsCheckpointResponse(new_checkpoint_id=checkpoint_id)
    return jsonify(asdict(response))


def add_blobs_to_system(
    app: ModelServerApp, blobs: Sequence[BlobPayload]
) -> list[DocumentId]:
    # In case of a mismatch between the blob names computed by the client
    # and the server, we use the one from the server. The corrected blob
    # name will be returned to the client.
    correct_blob_names = list[str]()
    for blob in blobs:
        computed_blob_name = compute_file_id(blob.path, blob.content)
        correct_blob_names.append(computed_blob_name)
        if blob.blob_name != computed_blob_name:
            logging.error(
                f"IDs for path {blob.path} do not match:"
                f" {blob.blob_name} != {computed_blob_name}. Make sure you have "
                "the latest extension version installed."
            )

    def metadata_to_dict(metadata: list[KeyValuePair]):
        return {x["key"]: x["value"] for x in metadata}

    new_docs = [
        Document(
            id=blob_name,
            text=blob.content,
            path=blob.path,
            meta=metadata_to_dict(blob.metadata),
        )
        for blob_name, blob in zip(correct_blob_names, blobs)
    ]
    file_store = app.get_file_store()
    with app.system_lock:
        # Add docs to retrieval corpus
        app.system.add_docs(new_docs)
    # Save docs to file store
    for doc in new_docs:
        assert doc.path is not None  # since blob.path is not None
        succeed = file_store.add_file(
            file_id=doc.id, path=doc.path, file_contents=doc.text, metadata=doc.meta
        )
        if not succeed:
            logging.warning(f"file_store.add_file {succeed=}, doc={str(doc)}")

    return [doc.id for doc in new_docs]

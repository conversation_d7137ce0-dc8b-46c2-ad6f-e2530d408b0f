# Our "current" SOTA system on Hydra
name: basic_rag
model:
  name: rogue
  checkpoint_path: rogue/diffb1m_16b_alphal_fimv2
  prompt:
    max_prefix_tokens: 1280
    max_suffix_tokens: 768
    max_retrieved_chunk_tokens: -1
    max_prompt_tokens: 3816
generation_options:
  temperature: 0
  top_k: 0
  top_p: 0
  max_generated_tokens: 280
retriever:
  name: diff_boykin
  max_query_lines: 10
  chunker:
    name: line_level
    max_lines_per_chunk: 40

experimental:
  remove_suffix: False
  retriever_top_k: 25
  trim_on_dedent: True
  trim_on_max_lines: null

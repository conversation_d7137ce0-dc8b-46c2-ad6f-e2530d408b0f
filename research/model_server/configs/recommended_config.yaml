name: model_server
multi_line_model:
  name: starcoderbase_16b_fim_aligned_fastforward
  prompt:
    max_prefix_tokens: 1024
    max_prompt_tokens: 3816
    max_suffix_tokens: 1024
    always_fim_style: True
    retrieval_layout_style: comment2
    include_filename_in_prompt: true
    github_stars: null
single_line_model:
  name: starcoderbase_1b_fim_aligned_fastforward
  prompt:
    max_prefix_tokens: 1024
    max_prompt_tokens: 2048
    max_suffix_tokens: 512
    always_fim_style: True
    include_filename_in_prompt: true
    github_stars: null
multi_line_generation_options:
  temperature: 0
  top_k: 0
  top_p: 0
  max_generated_tokens: 300
single_line_generation_options:
  temperature: 0
  top_k: 0
  top_p: 0
  max_generated_tokens: 20
retriever:
  name: diff_boykin
  chunker:
    name: line_level
    max_lines_per_chunk: 40
  max_query_lines: 10
completion_preamble: /home/<USER>/augment/experimental/colin/prompting_experiments/2023_07_17_definition_usage_exp/few_shot/prompts/prompt1.py

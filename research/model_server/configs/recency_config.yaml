# A RAG system that uses the recency scorer
name: basic_rag
model:
  name: rogue_fastforward
  model_path: rogue/rogue16B_diffb1m_chunk30
  # model_path: rogue/diffb1m_16b_alphal_fimv2
  prompt:
    max_prefix_tokens: 1280
    max_suffix_tokens: 768
    max_retrieved_chunk_tokens: -1
    max_prompt_tokens: 3816
    always_use_suffix_token: True
    only_truncate_true_prefix: True
generation_options:
  temperature: 0
  top_k: 0
  top_p: 0
  max_generated_tokens: 280
retriever:
  name: recency_scorer
  scorer:
    inner_scorer_name: diff_boykin
  chunker:
    name: scope_aware
    max_lines_per_chunk: 30
    # name: line_level
    # max_lines_per_chunk: 40
    include_scope_path_in_chunk_text: true
    merge_empty_chunks: false
    keep_empty_chunks: false
experimental:
  remove_suffix: False
  retriever_top_k: 25
  trim_on_dedent: True
  trim_on_max_lines: null

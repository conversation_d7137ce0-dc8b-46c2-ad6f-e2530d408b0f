system:
  name: "chat_rag"
  model:
    name: "fastforward_llama3_instruct_8b"
    # name: "null"
    sequence_length: 8192
  prompt_formatter:
    tokenizer_name: llama3_instruct
    prompt_formatter_name: binks_llama3_legacy
    prefix_len: 1024
    suffix_len: 1024
    path_len: 256
    message_len: 0 # Not used by the binks_llama3 prompt formatter
    selected_code_len: 0 # Not used by the binks_llama3 prompt formatter
    chat_history_len: 2048
    recent_changes_len: 2000
    retrieval_len_per_each_user_guided_file: 2000
    retrieval_len_for_user_guided: 3000
    retrieval_len: -1 # Fill the rest of the input prompt with retrievals
    max_prompt_len: 6144 # 8192 - 2048, the last 2048 reserved for output tokens
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 1024
  retriever:
    # scorer:
    #   name: dense_scorer_v2_fbwd
    #   checkpoint_path: /mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid
    #   model_key: model.models.0
    scorer:
      name: dense_scorer_v2_fbwd
      checkpoint_path: /mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-3
    chunker:
      name: line_level
      max_lines_per_chunk: 30
    query_formatter:
      name: base:chatanol6
      tokenizer_name: rogue
      max_tokens: 1024
    document_formatter:
      name: base:ethanol6-embedding-with-path-key
      tokenizer_name: rogue
      add_path: true
      max_tokens: 1024
  experimental:
    retriever_top_k: 32
  verbose: False

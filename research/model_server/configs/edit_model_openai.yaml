name: basic_code_edit
model:
  name: openai
  openai_model_name: gpt-4-1106-preview
context_free_prompt_formatter:
  template_file: research/model_server/prompts/codedit/oai_context_free_template.txt
  tokenizer_name: deepseekcoderinstructtokenizer
context_dependent_prompt_formatter:
  template_file: research/model_server/prompts/codedit/oai_context_dependent_template.txt
  tokenizer_name: deepseekcoderinstructtokenizer
generation_options:
  temperature: 0
  top_k: 0
  top_p: 0
  max_generated_tokens: 1024
input_token_budget: 200

"""NOTE(jiayi): these functions are currently not in use."""

import logging
from pathlib import Path

from base.languages.language_guesser import default_language_guesser
from base.languages.languages import LanguageId
from research.core.types import Document, compute_file_id
from research.model_server.file_store import FileStore

lang_guesser = default_language_guesser()


def should_index_path_for_retrieval(
    path: Path,
    language_set: set[LanguageId] | None,
    include_dirs: list[str] | None,
    exclude_dirs: list[str] | None,
) -> bool:
    """Should the given path be included for retrieval.

    Args:
        path: path relative to the retrieval root
        language_set: set of languages to index. None means indexing any known language.
        include_extensions: e.g. [".py", ".c"]
        include_dirs: e.g. ["research", "experimental"]
    """

    lang = lang_guesser.get_language(str(path))
    if lang is None or (language_set and lang not in language_set):
        return False

    def should_include_by_dirs():
        if exclude_dirs is not None:
            for exclude_dir in exclude_dirs:
                if str(path).startswith(exclude_dir + "/"):
                    return False
        if include_dirs is not None:
            for include_dir in include_dirs:
                if str(path).startswith(include_dir + "/"):
                    return True
        return include_dirs is None

    return should_include_by_dirs()


def get_docs_from_files(
    root: Path,
    language_set: set[LanguageId] | None,
    include_dirs: list[str] | None = None,
    exclude_dirs: list[str] | None = None,
) -> list[Document]:
    """Add files from given dir to index.

    Args:
        root: root path to get docs from
        language_set: set of languages to index
        include_extensions: list of extensions to include
        include_dirs: optional list of top-level dirs to include. If not
            specified, all directories are included.
    """
    docs: list[Document] = []
    num_files = 0
    num_lines = 0

    logging.info(f"Processing corpus at {root}, {language_set=}")

    for path in Path(root).rglob("*"):
        if not path.is_file():
            continue
        if not should_index_path_for_retrieval(
            path.relative_to(root), language_set, include_dirs, exclude_dirs
        ):
            continue
        num_files += 1
        with path.open("r", encoding="utf8") as file:
            text = file.read()
            num_lines += len(text.split("\n"))
            doc_path = str(path.relative_to(root))
            doc = Document(
                text=text,
                id=compute_file_id(path=doc_path, file_contents=text),
                path=doc_path,
            )
            docs.append(doc)

    logging.info(
        f"Server-local corpus contains {num_files} files with {num_lines} lines"
    )
    return docs


def load_file_store(args):
    """Load the file store from disk."""
    file_store = FileStore(
        args.uploaded_files_store_path / "model_server_file_store" / f"port_{args.port}"
    )
    if args.disable_extension_retrieval or args.clear_uploaded_file_store:
        # When we're not using the extension for retrieval, clear the cache
        # so we don't retrieve from previously-uploaded files.
        file_store.clear()

    if args.server_local_retrieval_path:
        language_set = set(args.languages) if args.languages else None
        docs: list[Document] = get_docs_from_files(
            args.server_local_retrieval_path,
            language_set=language_set,
            include_dirs=args.include_retrieval_dirs,
            exclude_dirs=args.exclude_retrieval_dirs,
        )

        for doc in docs:
            assert doc.meta is None
            assert doc.path is not None
            file_store.add_file(
                file_id=doc.id, path=doc.path, file_contents=doc.text, metadata=doc.meta
            )

        logging.info(f"Loaded {len(docs)} server-local documents")
    return file_store

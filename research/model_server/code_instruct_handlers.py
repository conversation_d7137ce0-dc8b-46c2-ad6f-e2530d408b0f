import logging
import time
from dataclasses import asdict
from datetime import datetime
from pathlib import Path
from typing import Any

from flask import jsonify, request

from research.core.edit_prompt_input import ResearchEditPromptInput
from research.core.types import DocumentId
from research.core.utils_for_file import write_json
from research.eval.harness.systems.code_edit_system import BasicCodeEditSystem
from research.eval.harness.systems.droid_code_edit_system import DroidCodeEditSystem
from research.eval.harness.systems.next_edit_gen_system import NextEditGenSystem
from research.model_server.model_server_app import ModelServerApp
from research.model_server.model_server_requests import (
    CodeEditAnnotationRequest,
    CodeEditRequest,
    CodeEditResponse,
)
from research.model_server.server_logging import print_request

GLOBAL_EDIT_LOG_DIR = Path("/mnt/efs/hdc/code-edit-logs")


def handle_code_edit_request(app: ModelServerApp):
    """Handles a code edit request."""

    if app.args.disable_completions:
        app.abort(400, "Server does not support completions.")
    start_time = time.time()

    system = app.system
    assert isinstance(system, (BasicCodeEditSystem, DroidCodeEditSystem))

    request_content = app.get_request(CodeEditRequest)

    logging.info("Code Edit Request:")
    print_request(request_content)

    # Preprocess the inputs
    prefix, suffix = app._truncate_prefix_suffix(
        request_content.prefix, request_content.suffix
    )
    path = request_content.path or ""

    blob_set = None
    if request_content.blobs:
        blob_set = app.blob_list_mgr.return_flat_blob_list(request_content.blobs)
        doc_ids = list(blob_set) if blob_set is not None else []
    else:
        doc_ids = []
    if not doc_ids:
        logging.warning("No blob list found in the request.")
    extra = {
        "lang": request_content.lang,
        "lines_in_prefix_suffix": request_content.lines_in_prefix_suffix,
    }

    system_input = ResearchEditPromptInput(
        prefix=prefix,
        suffix=suffix,
        path=path,
        instruction=request_content.instruction,
        selected_code=request_content.selected_text,
        retrieved_chunks=[],
        doc_ids=doc_ids,
        extra=extra,
    )

    # The inference host server is not thread-safe when we do caching, so we
    # avoid running replacement and completion in parallel
    with app.system_lock:
        unknown_blob_names: list[DocumentId] = []
        if app.use_extension_retrieval:
            if blob_set is not None:
                unknown_blob_names = app.find_missing(blob_set)
            else:
                logging.warning("blob_set is None.")
        if unknown_blob_names:
            logging.warning(f"Found {len(unknown_blob_names)} missing files.")
            if isinstance(system, NextEditGenSystem):
                logging.warning(
                    "NextEditGenSystem currently cannot handle missing files."
                )
                short_response = CodeEditResponse(
                    text=request_content.selected_text,
                    unknown_blob_names=unknown_blob_names,
                )
                return jsonify(short_response)

        logging.info("Generating...")
        start = time.time()

        system_output = system.generate(system_input)

        logging.info(f"Generation took {time.time() - start:.2f} seconds")

        generated_text, misc_dict = (
            system_output.generated_text,
            system_output.extra_output.additional_info,
        )

        response = CodeEditResponse(
            text=generated_text,
            unknown_blob_names=unknown_blob_names,
        )

        json_data = asdict(request_content)

        json_data.update(**misc_dict)
        json_data.update(
            {
                "response": generated_text,
                "sender_ip": request.remote_addr,
                "timestamp": datetime.now().isoformat(),
                "time_cost_s": time.time() - start_time,
            }
        )

        json_data["blobs"] = doc_ids

        # Log the user id as well
        json_data["user_id"] = app.get_user_id()
        json_data["system_spec"] = app.system_config_str
        # Save all the necessary information into a json file
        save_code_edit_data_json(
            json_data,
            app.get_request_id(),
            "-Call",
            request_content.completion_url,
        )

        app.requests_handled += 1

        logging.info(f"Response: {response}")
        return jsonify(response)


def handle_log_edit_status(app: ModelServerApp):
    """Logging human annotated status."""
    try:
        request_content = app.get_request(CodeEditAnnotationRequest)
    except TypeError as e:
        logging.error(f"Fail to parse request due to {e}")
        return jsonify({"status": "incorrect inputs"})

    logging.info("request to log edit status:")
    print_request(request_content)

    json_data = asdict(request_content)
    json_data.update(
        {
            "sender_ip": request.remote_addr,
            "timestamp": datetime.now().isoformat(),
        }
    )
    save_code_edit_data_json(
        json_data,
        request_content.request_id,
        "-Feedback",
        request_content.completion_url,
    )
    return jsonify({"status": "success"})


def save_code_edit_data_json(
    json_data: dict[str, Any],
    request_id: str | None = None,
    suffix: str = "",
    completion_url: str | None = None,
    global_log_dir: Path = GLOBAL_EDIT_LOG_DIR,
):
    """Save the request json data to a file.

    We are currently saving code edit requests / feedback into individual json files.
    We likely would need to revisit the method when we scale up the number of requests.
    """
    filename = f"{request_id}{suffix}.json"
    # Save the logs into different sub-folders based on the completion url.
    if completion_url is None:
        sub_folder = "default"
    elif "dogfood" in completion_url:
        sub_folder = "dogfood"
    elif "aitutor-pareto" in completion_url:
        sub_folder = "aitutor-pareto"
    elif "aitutor-turing" in completion_url:
        sub_folder = "aitutor-turing"
    else:
        sub_folder = "other"
    log_save_dir = global_log_dir / sub_folder
    full_file_path = log_save_dir / filename
    try:
        log_save_dir.mkdir(exist_ok=True, parents=False)
        write_json(
            full_file_path,
            json_data,
            indent=2,
        )
        logging.info(f"File path with request info: {full_file_path}")
    except BaseException as e:  # pylint: disable=broad-exception-caught
        logging.error(
            f"Fail to write file with request info ({full_file_path}) due to {e}"
        )

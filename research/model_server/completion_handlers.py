import json
import logging
import time
from dataclasses import asdict

from flask import jsonify
from unidiff import PatchSet

from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from base.static_analysis.common import assert_eq
from research.core.model_input import ModelInput
from research.core.types import DocumentId
from research.eval.harness.systems.abs_system import (
    CodeCompleteSystem,
    CompletionResult,
)
from research.model_server.blob_list_manager import blobs_from_completion_request
from research.model_server.model_server_app import ModelS<PERSON>rApp
from research.model_server.model_server_requests import (
    CompletionItem,
    CompletionRequest,
    CompletionResponse,
    ReplacementRequest,
)
from research.model_server.server_logging import print_request
from research.models.meta_model import GenerationOptions, GenerativeLanguageModel
from research.retrieval.scorers.recency_scorer import RECENCY_EXTRA_KEY, RecencyExtra


def handle_completion_request(app: ModelServerApp):
    """Handle completion request."""
    if app.args.disable_completions:
        app.abort(400, "Server does not support completions.")

    system: CodeCompleteSystem
    system = app.system

    # Prepare request
    request_content = app.get_request(CompletionRequest)
    file_store = app.get_file_store()

    if (
        request_content.recency_info is not None
        and request_content.recency_info.git_diff_info is not None
    ):
        blob_names = [
            item.content_blob_name
            for item in request_content.recency_info.git_diff_info
        ]
        if app.latest_git_diff_blob_names != blob_names:
            file_diffs = [
                file_store.get_file_contents(file_id) for file_id in blob_names
            ]
            missing_blobs = [
                requested
                for requested, found in zip(blob_names, file_diffs)
                if found is None
            ]
            if missing_blobs:
                logging.warning(
                    f"git_diff_blob_names missing {len(missing_blobs)} blobs starting with: {missing_blobs[0]}"
                )

            file_diffs = [x for x in file_diffs if x is not None]
            diff_text = "".join([file_diff for file_diff in file_diffs if file_diff])
            app.client_git_diff_patchset = PatchSet(diff_text)
            app.latest_git_diff_blob_names = blob_names
            logging.info("new local git diff:")
            logging.info("\n%s", diff_text)

    logging.info("completion request:")
    print_request(request_content)

    prefix = request_content.prompt
    suffix = request_content.suffix
    path = request_content.path

    max_prefix_len = app.args.suggested_prefix_char_count
    max_suffix_len = app.args.suggested_suffix_char_count
    assert max_prefix_len > 0
    assert max_suffix_len > 0

    if len(prefix) > max_prefix_len:
        logging.warning(
            (
                "Client sent prefix longer than "
                "suggested_prefix_char_count=%d, truncating"
            ),
            max_prefix_len,
        )
        prefix = prefix[len(prefix) - max_prefix_len :]
        assert_eq(len(prefix), max_prefix_len)

    if len(suffix) > max_suffix_len:
        logging.warning(
            (
                "Client sent suffix longer than "
                "suggested_suffix_char_count=%d, truncating"
            ),
            max_suffix_len,
        )
        suffix = suffix[:max_suffix_len]
        assert len(suffix) == max_suffix_len

    recency_extra = RecencyExtra()

    if (
        request_content.recency_info is not None
        and request_content.recency_info.tab_switch_events is not None
    ):
        recency_extra.tab_switch_paths = [
            item.path for item in request_content.recency_info.tab_switch_events
        ]
        recency_extra.tab_switch_blob_names = [
            item.file_blob_name
            for item in request_content.recency_info.tab_switch_events
        ]

    if app.web_interface.use_git_diffs_for_retrieval:
        recency_extra.client_git_diff_patchset = app.client_git_diff_patchset

    unknown_checkpoint_id = None
    blobs = blobs_from_completion_request(request_content)

    blob_set = app.blob_list_mgr.return_flat_blob_list(blobs)
    if blob_set is None:
        unknown_checkpoint_id = blobs.checkpoint_id
        blob_set = frozenset()

    extra = {
        RECENCY_EXTRA_KEY: recency_extra,
    }

    model_input = ModelInput(
        prefix=prefix,
        suffix=suffix,
        path=path,
        doc_ids=list(blob_set),
        cursor_position=request_content.cursor_position,
        extra=extra,
    )

    # Record latest request time
    this_request_time = time.time()
    with app.completion_queue_lock:
        if this_request_time > app.latest_request_time:
            app.latest_request_time = this_request_time

    # Wait for the completion lock to fulfill the request
    with app.system_lock:
        # If the request isn't new, return an empty response and discard it
        with app.completion_queue_lock:
            if app.latest_request_time > this_request_time:
                return jsonify({})

        # VSCode likes the immediately send a completion request after a
        # replacement request, leading to ghost text. Here we return an
        # empty completion when that happens.
        if app.last_request_was_replacement:
            app.last_request_was_replacement = False
            logging.info("skipping completion request that followed replacement")
            response = CompletionResponse(
                text="",
                completions=[],
                completion_items=[],
                unknown_blob_names=[],
                suggested_prefix_char_count=max_prefix_len,
                suggested_suffix_char_count=max_suffix_len,
                completion_timeout_ms=app.args.completion_timeout_ms,
            )
            return jsonify(asdict(response))

        logging.info("generating...")
        start = time.time()
        completion = system.generate(model_input)
        assert isinstance(completion, CompletionResult)
        elapsed = time.time() - start

        # TODO(guy) Needs better typing. We need to detokenize the returned
        # prompt, and for that we need access to the tokenizer. So we reach
        # into the system -> model -> tokenizer, which only works if the system
        # has a "model" associated with it. This is all very brittle, and we
        # should either expose a proper model or tokenizer from the system,
        # or we should return the detokenized prompt.
        if model := getattr(system, "model", None):
            assert isinstance(model, GenerativeLanguageModel)
            prompt = model.tokenizer.detokenize(completion.prompt_tokens)
            app.web_interface.record_completion_request(
                request=request_content,
                prompt=prompt,
                generated_output=completion.generated_text,
                retrieved_chunks=completion.retrieved_chunks,
            )

        unknown_blob_names: list[DocumentId] = []
        if app.use_extension_retrieval:
            if blob_set is not None:
                unknown_blob_names = app.find_missing(blob_set)
            else:
                logging.warning("context is None.")
            logging.info(f"Found {len(unknown_blob_names)} missing files.")

        completion_item = CompletionItem(
            text=completion.generated_text,
            suffix_replacement_text=completion.extra_output.suffix_replacement_text,
            skipped_suffix=completion.extra_output.skipped_suffix,
        )
        response = CompletionResponse(
            text=completion.generated_text,
            completions=[completion.generated_text],
            completion_items=[completion_item],
            unknown_blob_names=unknown_blob_names,
            unknown_checkpoint_id=unknown_checkpoint_id,
            suggested_prefix_char_count=max_prefix_len,
            suggested_suffix_char_count=max_suffix_len,
            completion_timeout_ms=app.args.completion_timeout_ms,
        )

        response_for_logging = asdict(response)
        response_for_logging["unknown_blob_names"] = (
            f"... {len(response_for_logging['unknown_blob_names'])} unknown blob names ..."
        )
        logging.info(json.dumps(response_for_logging, indent=2))
        logging.debug(f"Response generation took {elapsed:.2f} seconds")

        app.requests_handled += 1
        logging.debug(f"Completion requests handled: {app.requests_handled}")

        return jsonify(response)


def handle_replace_selection_request(app: ModelServerApp):
    """Handle replacement request.

    This endpoint supports replacing a selected piece of text in the extension
    with a model-generated piece of text. See README.md for a description of
    the replacement process.
    """
    if not app.replacement_handler:
        raise ValueError("no replacement handler specified")

    request_content = app.get_request(ReplacementRequest)
    logging.info("replacement request:")
    print_request(request_content)

    # Potential optimization: use START_EXAMPLE_SECTION as StopCriterion
    options = _get_generation_options(
        app.args.max_replacement_generated_tokens, request_content
    )

    # The inference host server is not thread-safe when we do caching, so we
    # avoid running replacement and completion in parallel
    with app.system_lock:
        app.last_request_was_replacement = True
        response = app.replacement_handler.handle_request(request_content, options)
        response = asdict(response)
        logging.info("response:")
        logging.info(json.dumps(response, indent=2))
        return jsonify(response)


def handle_resolve_completions_request(app: ModelServerApp):
    del app
    return {}


def _get_generation_options(
    max_generated_tokens: int,
    request_content: CompletionRequest | ReplacementRequest,
):
    def get(value, default):
        if value is None:
            return default
        return value

    temperature = get(
        request_content.temperature, default=GenerationOptions.temperature
    )
    top_k = get(request_content.top_k, default=GenerationOptions.top_k)
    top_p = get(request_content.top_p, default=GenerationOptions.top_p)
    max_tokens = get(request_content.max_tokens, default=max_generated_tokens)

    return GenerationOptions(
        temperature=temperature,
        max_generated_tokens=max_tokens,
        # stop_criteria=stop_criteria,
        top_k=top_k,
        top_p=top_p,
    )

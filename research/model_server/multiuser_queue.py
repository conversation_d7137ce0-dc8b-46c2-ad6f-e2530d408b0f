from __future__ import annotations

import threading
from typing import Collection, Generic, TypeVar

KeyT = TypeVar("KeyT")
ReqT = TypeVar("ReqT")


class MultiuserQueue(Generic[KeyT, ReqT]):
    """A thread-safe queue for managing concurrent requests from multiple users.

    Each user is uniquely identified by a user key of type `KeyT`, and each request
    is uniquely identified by a request key of type `ReqT`.
    """

    def __init__(self):
        self._pending_requests = list[tuple[KeyT, ReqT]]()
        """A FIFO queue storing pending requests.

        There should be at most 1 request per user key at any given time.
        """
        self._lock = threading.RLock()

    def add_request(
        self, user: KeyT, request: ReqT, to_cancel: Collection[KeyT] | None = None
    ) -> int:
        """Add a request to the end of the queue under the given user key.

        Args:
            user: The user key associated with the request.
            request: The request to be put into the queue.
            to_cancel: A collection of user keys that if present, should be canceled\
                before the given request is added. If None, existing requests with the\
                    same user key will be canceled.

        Returns:
            The number of existing requests that were canceled.
        """
        if to_cancel is None:
            to_cancel = {user}
        num_canceled = 0
        with self._lock:
            new_queue = [
                req for req in self._pending_requests if req[0] not in to_cancel
            ]
            num_canceled = len(self._pending_requests) - len(new_queue)
            new_queue.append((user, request))
            self._pending_requests = new_queue
        return num_canceled

    def finish_request(self, user: KeyT, request: ReqT) -> bool:
        """Remove a request from the queue."""
        old_deleted = False
        with self._lock:
            new_queue = [
                req for req in self._pending_requests if req != (user, request)
            ]
            old_deleted = len(new_queue) < len(self._pending_requests)
            if old_deleted:
                self._pending_requests = new_queue
        return old_deleted

    def get_pending_for_user(self, user: KeyT) -> list[ReqT]:
        """Get all pending requests for a user."""
        with self._lock:
            return [req[1] for req in self._pending_requests if req[0] == user]

    def should_cancel(self, user: KeyT, request_id: ReqT) -> bool:
        """Returns true if the given request is no longer pending for the user."""
        pending_inputs = self.get_pending_for_user(user)
        return request_id not in pending_inputs

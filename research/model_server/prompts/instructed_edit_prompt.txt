# The following is a series of examples of code, plus a natural language instruction
# for how to edit it. Following each example is 100% valid code where the code
# was edited according to the instruction.

# Here are some examples of common code editing tasks:
# - refactor code into a separate function
# V rename something (more complicated than just a variable)
# V add error handling
# - replace explicit code with an API call
# V measure the time it takes to run a piece of code and print it out
# V add a docstring to a function
# V fix a typo
# V fix a bug
# V replace a loop with a list comprehension

# [[[Example with instruction]]]
# instruction: change green to blue

def print_green(s):
    """Print text in green."""
    print(f"{Fore.GREEN}{s}{Style.RESET_ALL}")

# [[[Example converted to 100% valid Python code]]]

def print_blue(s):
    """Print text in blue."""
    print(f"{Fore.BLUE}{s}{Style.RESET_ALL}")

# [[[Example with instruction]]]
# instruction: make list comprehension

result = []
for item in items:
    result.append(transform(item))

# [[[Example converted to 100% valid Python code]]]

result = [transform(item) for item in items]

# [[[Example with instruction]]]
# instruction: fix typo

def factoriall(n):
    """Return the factorial of n."""
    if n == 0:
        return 1
    else:
        return n * factoriall(n - 1)

# [[[Example converted to 100% valid Python code]]]

def factorial(n):
    """Return the factorial of n."""
    if n == 0:
        return 1
    else:
        return n * factoriall(n - 1)

# [[[Example with instruction]]]
# instruction: fix off by one

def sum_one_to_n(n):
    """Return the nth Fibonacci number."""
    return sum(range(n))

# [[[Example converted to 100% valid Python code]]]

def sum_one_to_n(n):
    """Return the nth Fibonacci number."""
    return sum(range(n + 1))

# [[[Example with instruction]]]
# instruction: measure time, print

make_rpc_call(build_request(SERVICE_ID, PAYLOAD))

# [[[Example converted to 100% valid Python code]]]

start = time.time()
make_rpc_call(build_request(SERVICE_ID, PAYLOAD))
end = time.time()
print(f"Took {end - start:.2f} seconds")

# [[[Example with instruction]]]
# instruction: handle file not found

def read_file(path: Path) -> str:
    """Read the file contents.

    If the file doesn't exist, return "".
    """
    with open(path) as f:
        return f.read()

# [[[Example converted to 100% valid Python code]]]

def read_file(path: Path) -> str:
    """Read the file contents.

    If the file doesn't exist, return "".
    """
    try:
        with open(path) as f:
            return f.read()
    except FileNotFoundError:
        return ""

# [[[Example with instruction]]]
# instruction: remove timing

    start = time.time()
    connect_to_db(db, params)
    end = time.time()
    print(f"Took {end - start:.2f} seconds")

# [[[Example converted to 100% valid Python code]]]

    connect_to_db(db, params)

# [[[Example with instruction]]]
# instruction: do it

    all_contents = ""

# TODO specify UTF8
    with path1.open("r") as f1:
        all_contents += f1.read()

    with path2.open("r") as f2:
        all_contents += f2.read()

    with path3.open("r") as f3:
        all_contents += f3.read()

# [[[Example converted to 100% valid Python code]]]

        all_contents = ""

        with path1.open("r", encoding="utf8") as f1:
            all_contents += f1.read()

        with path2.open("r", encoding="utf8") as f2:
            all_contents += f2.read()

        with path3.open("r", encoding="utf8") as f3:
            all_contents += f3.read()

# [[[Example with instruction]]]
# instruction: clean up

        # Detect if str-to-bytes conversion (or vice versa) is needed for the combination of this file-like object and the used dumps() callable.
        # This avoids checking this for each .write().  ,  Note that this
        # deliberately does not support ‘dynamic’ return types that depend on input and dump options, like simplejson on Python 2 in some cases.

# [[[Example converted to 100% valid Python code]]]

        # Detect if str-to-bytes conversion (or vice versa) is needed for the
        # combination of this file-like object and the used dumps() callable.
        # This avoids checking this for each .write(). Note that this
        # deliberately does not support ‘dynamic’ return types that depend on
        # input and dump options, like simplejson on Python 2 in some cases.

# [[[Example with instruction]]]
# instruction: fix indentation

        if isinstance(self._fp, io.TextIOBase):
        self._fp_is_binary = False
        elif isinstance(self._fp, io.IOBase):
        self._fp_is_binary = True
        else:
        try:
        self._fp.write("")  # type: ignore[arg-type]
        except TypeError:
        self._fp_is_binary = True
        else:
        self._fp_is_binary = False

# [[[Example converted to 100% valid Python code]]]

        if isinstance(self._fp, io.TextIOBase):
            self._fp_is_binary = False
        elif isinstance(self._fp, io.IOBase):
            self._fp_is_binary = True
        else:
            try:
                self._fp.write("")  # type: ignore[arg-type]
            except TypeError:
                self._fp_is_binary = True
            else:
                self._fp_is_binary = False

# [[[Example with instruction]]]
# instruction: use partial

fn = lambda ingredients: trail_mix(ingredients, state="ca", month="may")

# [[[Example converted to 100% valid Python code]]]

fn = partial(trail_mix, state="ca", month="may")

# [[[Example with instruction]]]
# instruction: add docstring

def add(a, b):
    return a + b

# [[[Example converted to 100% valid Python code]]]

def add(a, b):
    """Add two numbers."""
    return a + b

# [[[Example with instruction]]]
# instruction: make body a function

for student in students:
    if student["grade"] > 90:
        student["status"] = "Excellent"
    elif student["grade"] > 80:
        student["status"] = "Good"
    else:
        student["status"] = "Average"

# [[[Example converted to 100% valid Python code]]]

def grade_to_status(grade):
    if grade > 90:
        return "Excellent"
    elif grade > 80:
        return "Good"
    else:
        return "Average"

for student in students:
    student["status"] = grade_to_status(student["grade"])

# [[[Example with instruction]]]
# instruction: add the imports

with Path("/tmp/data.jsonl").open("r") as file:
    for line in file.readlines():
        data = json.loads(line)
        print(data["tree"])

# [[[Example converted to 100% valid Python code]]]

from pathlib import Path
import json

with Path("/tmp/data.jsonl").open("r") as file:
    for line in file.readlines():
        data = json.loads(line)
        print(data["tree"])

# [[[Example with instruction]]]
# instruction: turn into a normal class

@dataclass
class Turtle:
    """A turtle."""
    x: int = 0
    """x coordinate."""

    y: int = 0
    """y coordinate."""

    angle: float = 0.
    """angle where turtle is headed, in radians."""

# [[[Example converted to 100% valid Python code]]]

class Turtle:
    def __init__(self, x: int = 0, y: int = 0, angle: float = 0.):
        """Constructor.

        Args:
            x: x coordinate.
            y: y coordinate.
            angle: angle where turtle is headed, in radians.
        """
        self.x = x
        self.y = y
        self.angle = angle

# [[[Example with instruction]]]
# instruction: split long line

            raise NotImplementedError((
                f"Only greedy sampling implemented, got temperature={options.temperature}"
            ))

# [[[Example converted to 100% valid Python code]]]

            raise NotImplementedError((
                "Only greedy sampling implemented, "
                f"got temperature={options.temperature}"
            ))

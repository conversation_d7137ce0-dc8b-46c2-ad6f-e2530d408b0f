"""Tests for the ToolSupportWrapper class."""

import json
import pytest
from typing import Any, Dict, List, Optional, Tu<PERSON>
from unittest.mock import MagicMock, patch

from research.llm_apis.llm_client import (
    AssistantC<PERSON>nt<PERSON>lock,
    GeneralContentBlock,
    LLMClient,
    LLMMessages,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolParam,
    ToolSupportProvider,
)
from research.llm_apis.tool_support_wrapper import ToolSupportWrapper


class MockLLMClient(LLMClient):
    """Mock LLM client for testing."""

    def __init__(self, response_text: str = ""):
        """Initialize the mock client.

        Args:
            response_text: The text to return in the response
        """
        self.response_text = response_text
        self.generate_called = False
        self.last_system_prompt = None
        self.last_messages = None
        self.last_tools = None

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: Optional[str] = None,
        temperature: float = 0.0,
        tools: List[ToolParam] = [],
        tool_choice: Optional[Dict[str, str]] = None,
        thinking_tokens: Optional[int] = None,
    ) -> <PERSON><PERSON>[List[AssistantContentBlock], Dict[str, Any]]:
        """Generate responses.

        Args:
            messages: A list of messages.
            max_tokens: The maximum number of tokens to generate.
            system_prompt: A system prompt.
            temperature: The temperature.
            tools: A list of tools.
            tool_choice: A tool choice.
            thinking_tokens: Optional number of tokens to use for thinking.

        Returns:
            A tuple containing:
            - A list of assistant content blocks
            - A dictionary of metadata
        """
        self.generate_called = True
        self.last_system_prompt = system_prompt
        self.last_messages = messages
        self.last_tools = tools

        return [TextResult(text=self.response_text)], {"mock": "metadata"}


class TestToolSupportWrapper:
    """Tests for the ToolSupportWrapper class."""

    def test_init(self):
        """Test initialization of the wrapper."""
        client = MockLLMClient()
        wrapper = ToolSupportWrapper(client)

        assert wrapper.client == client

    def test_generate_without_tools(self):
        """Test generate method without tools."""
        client = MockLLMClient(response_text="Hello, world!")
        wrapper = ToolSupportWrapper(client)

        messages: LLMMessages = [[TextPrompt(text="Hi")]]
        response, metadata = wrapper.generate(
            messages=messages,
            max_tokens=100,
            system_prompt="You are a helpful assistant.",
        )

        assert client.generate_called
        assert client.last_system_prompt == "You are a helpful assistant."
        assert client.last_messages == messages
        assert client.last_tools == []

        assert len(response) == 1
        assert isinstance(response[0], TextResult)
        assert response[0].text == "Hello, world!"
        assert metadata == {"mock": "metadata"}

    def test_generate_with_tools(self):
        """Test generate method with tools."""
        # Mock response with a tool call
        tool_call_response = 'I need to check the weather. <function=get_weather>{"location": "San Francisco"}</function>'
        client = MockLLMClient(response_text=tool_call_response)
        wrapper = ToolSupportWrapper(client)

        # Create a tool
        weather_tool = ToolParam(
            name="get_weather",
            description="Get the weather for a location",
            input_schema={
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The location to get the weather for",
                    }
                },
                "required": ["location"],
            },
        )

        messages: LLMMessages = [[TextPrompt(text="What's the weather in San Francisco?")]]
        response, metadata = wrapper.generate(
            messages=messages,
            max_tokens=100,
            system_prompt="You are a helpful assistant.",
            tools=[weather_tool],
        )

        # Verify the client was called with the right parameters
        assert client.generate_called
        assert client.last_system_prompt is not None
        assert "Function Name: 'get_weather'" in client.last_system_prompt
        assert "You are a helpful assistant." in client.last_system_prompt
        assert client.last_messages == messages
        assert (
            client.last_tools == []
        )  # Tools should be empty since we're handling them in the wrapper

        # Verify the response was parsed correctly
        assert len(response) == 2
        assert isinstance(response[0], TextResult)
        assert response[0].text == "I need to check the weather."
        assert isinstance(response[1], ToolCall)
        assert response[1].tool_name == "get_weather"
        assert response[1].tool_input == {"location": "San Francisco"}

        # Verify metadata
        assert "tool_support_provider" in metadata
        assert metadata["original_system_prompt"] == "You are a helpful assistant."

    def test_generate_with_conversation_history(self):
        """Test generate method with conversation history."""
        # Mock response with a tool call
        tool_call_response = '<function=get_weather>{"location": "New York"}</function>'
        client = MockLLMClient(response_text=tool_call_response)
        wrapper = ToolSupportWrapper(client)

        # Create a tool
        weather_tool = ToolParam(
            name="get_weather",
            description="Get the weather for a location",
            input_schema={
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The location to get the weather for",
                    }
                },
                "required": ["location"],
            },
        )

        # Create a conversation history
        messages: LLMMessages = [
            [TextPrompt(text="What's the weather in San Francisco?")],
            [
                ToolCall(
                    tool_call_id="call_123",
                    tool_name="get_weather",
                    tool_input={"location": "San Francisco"},
                )
            ],
            [TextResult(text="The weather in San Francisco is 72°F and sunny.")],
            [TextPrompt(text="What about New York?")],
        ]

        response, metadata = wrapper.generate(
            messages=messages,
            max_tokens=100,
            system_prompt="You are a helpful assistant.",
            tools=[weather_tool],
        )

        # Verify the client was called with the right parameters
        assert client.generate_called
        assert client.last_system_prompt is not None
        assert "Function Name: 'get_weather'" in client.last_system_prompt

        # The last message should have been modified to include the conversation history
        assert client.last_messages is not None
        assert len(client.last_messages) > 0
        assert len(client.last_messages[-1]) > 0
        assert hasattr(client.last_messages[-1][0], "text")
        text_content = getattr(client.last_messages[-1][0], "text")
        assert "User: What's the weather in San Francisco?" in text_content
        assert "Assistant: [Tool Call: get_weather]" in text_content
        assert "The weather in San Francisco is 72°F and sunny." in text_content
        assert "What about New York?" in text_content

        # Verify the response was parsed correctly
        assert len(response) == 1
        assert isinstance(response[0], ToolCall)
        assert response[0].tool_name == "get_weather"
        assert response[0].tool_input == {"location": "New York"}

        # Verify metadata
        assert "tool_support_provider" in metadata
        assert metadata["original_system_prompt"] == "You are a helpful assistant."

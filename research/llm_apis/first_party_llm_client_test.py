"""Tests for the FirstPartyLLMClient."""

import unittest

from unittest import mock

from base.third_party_clients.third_party_model_client import (
    ThirdPartyModelResponse,
    ToolUseResponse,
)
from research.llm_apis.first_party_clients import LocalModelClient
from research.llm_apis.first_party_llm_client import FirstPartyLLMClient
from research.llm_apis.llm_client import (
    LLMMessages,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolParam,
)


class TestFirstPartyLLMClient(unittest.TestCase):
    """Tests for the FirstPartyLLMClient."""

    def setUp(self):
        """Set up the test."""
        # Create a mock first party client
        self.mock_first_party_client = mock.MagicMock(spec=LocalModelClient)

        # Create a FirstPartyLLMClient that wraps the mock client
        self.client = FirstPartyLLMClient(
            first_party_client=self.mock_first_party_client
        )

    def test_initialization(self):
        """Test initialization of FirstPartyLLMClient."""
        self.assertEqual(self.client.first_party_client, self.mock_first_party_client)

    def test_generate_text_response(self):
        """Test generating a text response."""
        # Set up the mock to return a text response
        self.mock_first_party_client.generate_response.return_value = [
            ThirdPartyModelResponse(text="Hello, world!")
        ]

        # Call generate
        messages = [[TextPrompt(text="Hi there")]]
        messages: LLMMessages = messages
        response, metadata = self.client.generate(
            messages=messages,
            max_tokens=100,
            system_prompt="You are a helpful assistant.",
            temperature=0.7,
        )

        # Check that the response is correct
        self.assertEqual(len(response), 1)
        self.assertIsInstance(response[0], TextResult)
        assert isinstance(response[0], TextResult)
        self.assertEqual(response[0].text, "Hello, world!")

        # Check that the metadata is correct
        self.assertEqual(metadata["first_party_client"], "MagicMock")
        self.assertEqual(metadata["response_count"], 1)

    def test_generate_tool_response(self):
        """Test generating a tool response."""
        # Set up the mock to return a tool response
        self.mock_first_party_client.generate_response.return_value = [
            ThirdPartyModelResponse(
                text="",
                tool_use=ToolUseResponse(
                    tool_name="calculator",
                    input={"expression": "1 + 1"},
                    tool_use_id="123",
                ),
            )
        ]

        # Call generate with a tool
        messages = [[TextPrompt(text="What is 1 + 1?")]]
        messages: LLMMessages = messages

        tools = [
            ToolParam(
                name="calculator",
                description="A calculator tool",
                input_schema={
                    "type": "object",
                    "properties": {
                        "expression": {
                            "type": "string",
                            "description": "The expression to evaluate",
                        }
                    },
                    "required": ["expression"],
                },
            )
        ]
        response, metadata = self.client.generate(
            messages=messages,
            max_tokens=100,
            system_prompt="You are a helpful assistant.",
            temperature=0.7,
            tools=tools,
        )

        # Check that the response is correct
        self.assertEqual(len(response), 1)
        self.assertIsInstance(response[0], ToolCall)
        assert isinstance(response[0], ToolCall)
        self.assertEqual(response[0].tool_name, "calculator")
        self.assertEqual(response[0].tool_input, {"expression": "1 + 1"})
        self.assertEqual(response[0].tool_call_id, "123")

        # Check that the metadata is correct
        self.assertEqual(metadata["first_party_client"], "MagicMock")
        self.assertEqual(metadata["response_count"], 1)


if __name__ == "__main__":
    unittest.main()

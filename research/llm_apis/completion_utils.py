"""Utilities for generating completions using LLM APIs."""

import json
import multiprocessing.pool
from abc import abstractmethod
from dataclasses import dataclass
from typing import Iterable, Optional

import requests
from termcolor import colored

from research.core.abstract_prompt_formatter import Abstract<PERSON>rompt<PERSON><PERSON>atter
from research.core.llama_prompt_formatters import Dialog
from research.core.model_input import ModelInput


@dataclass
class CompletionResponse:
    """Response to a completion request."""

    full_response: dict
    content_field: str = "content"

    @property
    def content(self) -> str:
        return self.full_response[self.content_field]

    @property
    def model(self) -> str:
        return self.full_response["model"]


class CompletionClient:
    """A simple completion client."""

    @abstractmethod
    def generate(
        self,
        prompt: str | list[int],
        max_generated_tokens: int,
        temperature: float = 0.0,
        stop: Optional[list[str]] = None,
    ) -> CompletionResponse:
        """Generate and return a completion.

        Args:
            prompt: prompt
            max_generated_tokens: max number of tokens to generate
            temperature: temperature
            stop: A list of stop strings. Will not be included in the response.
        """
        pass


class MistralClient(CompletionClient):
    """Mistral client."""

    def __init__(self, mistral_api_key: str):
        self.mistral_api_key = mistral_api_key

    def generate(
        self,
        prompt: str | list[int],
        max_generated_tokens: int,
        temperature: float = 0.0,
        stop: Optional[list[str]] = None,
    ) -> CompletionResponse:
        if stop is not None:
            raise NotImplementedError("Stop criteria not implemented for Mistral")
        if isinstance(prompt, list):
            raise NotImplementedError("Mistral client does not support token prompts")

        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Bearer {self.mistral_api_key}",
        }
        data = {
            "model": "mistral-medium",
            "messages": [
                {
                    "role": "user",
                    "content": prompt,
                }
            ],
            "temperature": temperature,
            "top_p": 1,
            "max_tokens": max_generated_tokens,
            "stream": False,
            "safe_prompt": False,
            "random_seed": None,
        }
        http_response = requests.post(
            "https://api.mistral.ai/v1/chat/completions",
            headers=headers,
            data=json.dumps(data),
            timeout=120,
        )
        http_response.raise_for_status()
        response_str: str = http_response.content.decode(encoding="utf-8")
        response = json.loads(response_str)
        full_response = {
            "content": response["choices"][0]["message"]["content"],
            "model": response["model"],
            "rest": response,
        }
        return CompletionResponse(full_response=full_response)


class MockClient(CompletionClient):
    """A mock client."""

    def generate(
        self,
        prompt: str | list[int],
        max_generated_tokens: int,
        temperature: float = 0.0,
        stop: Optional[list[str]] = None,
    ) -> CompletionResponse:
        """Generate and return a completion.

        Args:
            prompt: prompt
            max_generated_tokens: max number of tokens to generate
            temperature: temperature
            stop: A list of stop strings. Will not be included in the response.
        """
        return CompletionResponse(full_response={"model": "mock", "content": "mock"})


class TritonClient(CompletionClient):
    """NVIDIA Triton client with the TensorRT-LLM backend.

    Uses the Generate extension, which is subject to change.
    https://docs.nvidia.com/deeplearning/triton-inference-server/user-guide/docs/protocol/extension_generate.html

    Also see this for specific TRT-LLM options:
    https://github.com/triton-inference-server/tensorrtllm_backend/blob/main/docs/llama.md
    """

    def __init__(
        self,
        eod_id: int,
        prompt_template: str = "{prompt}",
        address: str = "127.0.0.1:8080",
        timeout: int = 20,
    ):
        """Construct.

        Args:
            eod_id: Token ID for end-of-document.
            prompt_template: A string where '{prompt}' will be replaced with the prompt,
                and the result will be fed to the model as the full prompt.
            address: Server address.
            timeout: Request timeout in seconds.
        """
        self.eod_id = eod_id
        self.prompt_template = prompt_template
        self.completion_url = f"http://{address}/v2/models/ensemble/generate"
        self.streaming_url = f"http://{address}/v2/models/ensemble/generate_stream"
        self.timeout = timeout
        self.headers = {
            "Content-Type": "application/json",
        }
        self.random_seed = 42

    def _get_post_request_payload(
        self,
        prompt: str,
        max_generated_tokens: int,
        stop: Optional[list[str]],
        temperature: float,
        stream: bool,
        extra_payload: Optional[dict] = None,
    ) -> dict:
        full_prompt = self.prompt_template.replace("{prompt}", prompt)

        # Supply a different seed for every request. For Triton, this is
        # necessary in order for temperature sampling to generate a different
        # in consecutive requests.
        seed = self.random_seed
        self.random_seed += 1

        payload = {
            "text_input": full_prompt,
            "max_tokens": max_generated_tokens,
            "end_id": self.eod_id,
            "stream": stream,
            "temperature": temperature,
            "top_k": 40,
            "top_p": 0.95,
            "random_seed": seed,
            # An attempt to turn off unnecessary data being returned, though
            # it doesn't seem to do much
            "return_context_logits": False,
            "return_log_probs": False,
            "return_generation_logits": False,
        }

        if extra_payload:
            payload.update(extra_payload)

        if stop:
            payload["stop_words"] = stop

        return payload

    def generate(
        self,
        prompt: str | list[int],
        max_generated_tokens: int,
        temperature: float = 0.0,
        stop: Optional[list[str]] = None,
        extra_payload: Optional[dict] = None,
    ) -> CompletionResponse:
        """Generate and return a completion.

        Args:
            prompt: prompt
            max_generated_tokens: max number of tokens to generate
            temperature: temperature
            stop: A list of stop strings. Will not be included in the response.
        """
        if isinstance(prompt, list):
            raise NotImplementedError("Triton does not support token prompts")
        payload = self._get_post_request_payload(
            prompt,
            max_generated_tokens,
            stop,
            temperature,
            stream=False,
            extra_payload=extra_payload,
        )
        http_response = requests.post(
            self.completion_url,
            headers=self.headers,
            data=json.dumps(payload),
            timeout=self.timeout,
        )
        http_response.raise_for_status()
        response_str: str = http_response.content.decode(encoding="utf-8")
        response = json.loads(response_str)
        return CompletionResponse(full_response=response, content_field="text_output")

    def generate_stream(
        self,
        prompt: str,
        max_generated_tokens: int,
        temperature: float = 0.0,
        stop: Optional[list[str]] = None,
    ) -> Iterable[str]:
        """Generate a completion and stream it.

        Args:
            prompt: prompt
            max_generated_tokens: max number of tokens to generate
            temperature: temperature
            stop: A list of stop strings. Will not be included in the response.
        """
        payload = self._get_post_request_payload(
            prompt, max_generated_tokens, stop, temperature, stream=True
        )

        with requests.post(
            self.streaming_url,
            headers=self.headers,
            data=json.dumps(payload),
            timeout=self.timeout,
            stream=True,
        ) as http_response:
            for line in http_response.iter_lines():
                # Example model response: data: {"text_output":"?", ...}
                serialized_json = (
                    line.decode(encoding="utf-8").replace("data: ", "").strip()
                )
                if not serialized_json:
                    continue
                response = json.loads(serialized_json)
                if "error" in response:
                    raise ValueError(f"Error: {response['error']}")
                yield response["text_output"]


class LlamaCppClient(CompletionClient):
    """llama.cpp client."""

    def __init__(
        self,
        prompt_template: str = "{prompt}",
        address: str = "127.0.0.1:8080",
        timeout: int = 20,
    ):
        """Construct.

        Args:
            prompt_template: A string where '{prompt}' will be replaced with the prompt,
                and the result will be fed to the model as the full prompt.
            address: Server address.
            timeout: Request timeout in seconds.
        """
        self.prompt_template = prompt_template
        self.url = f"http://{address}/completion"
        self.timeout = timeout
        self.headers = {
            "Content-Type": "application/json",
        }

    def _get_post_request_payload(
        self,
        prompt: str | list[int],
        max_generated_tokens: int,
        stop: Optional[list[str]],
        temperature: float,
    ) -> dict:
        if stop is None:
            stop = []

        if isinstance(prompt, str):
            full_prompt = self.prompt_template.replace("{prompt}", prompt)
        else:
            full_prompt = prompt

        return {
            "prompt": full_prompt,
            "n_predict": max_generated_tokens,
            "temperature": temperature,
            "stop": stop,
        }

    def generate(
        self,
        prompt: str | list[int],
        max_generated_tokens: int,
        temperature: float = 0.0,
        stop: Optional[list[str]] = None,
    ) -> CompletionResponse:
        """Generate and return a completion.

        Args:
            prompt: prompt
            max_generated_tokens: max number of tokens to generate
            temperature: temperature
            stop: A list of stop strings. Will not be included in the response.
        """
        payload = self._get_post_request_payload(
            prompt, max_generated_tokens, stop, temperature
        )
        http_response = requests.post(
            self.url,
            headers=self.headers,
            data=json.dumps(payload),
            timeout=self.timeout,
        )
        http_response.raise_for_status()
        response_str: str = http_response.content.decode(encoding="utf-8")
        response = json.loads(response_str)
        return CompletionResponse(full_response=response)

    def generate_stream(
        self,
        prompt: str,
        max_generated_tokens: int,
        temperature: float = 0.0,
        stop: Optional[list[str]] = None,
    ) -> Iterable[str]:
        """Generate a completion and stream it.

        Args:
            prompt: prompt
            max_generated_tokens: max number of tokens to generate
            temperature: temperature
            stop: A list of stop strings. Will not be included in the response.
        """
        payload = self._get_post_request_payload(
            prompt, max_generated_tokens, stop, temperature
        )
        payload["stream"] = True

        with requests.post(
            self.url,
            headers=self.headers,
            data=json.dumps(payload),
            timeout=self.timeout,
            stream=True,
        ) as http_response:
            for line in http_response.iter_lines():
                # Example model response: data: {"content":"?","stop":false,"id_slot":0,"multimodal":false}
                serialized_json = (
                    line.decode(encoding="utf-8").replace("data: ", "").strip()
                )
                if not serialized_json:
                    continue
                response = json.loads(serialized_json)
                yield response["content"]


def print_prompt(prompt: str, num_header_lines: int = 6, num_footer_lines: int = 6):
    print(colored("'''", "green"), end="")
    lines = prompt.splitlines(keepends=True)
    if len(lines) > num_header_lines + num_footer_lines:
        print("".join(lines[:num_header_lines]), end="")
        print("...")
        print("".join(lines[-num_footer_lines:]), end="")
    else:
        print(prompt, end="")
    print(colored("'''", "green"))


@dataclass
class ModelDetails:
    """Model details."""

    name: str
    client: CompletionClient
    prompt_formatter: Optional[AbstractPromptFormatter]
    stop: Optional[list[str]] = None


def generate_model_response(
    model: ModelDetails,
    instruction: str,
    max_generated_tokens: int,
    verbose: bool = False,
) -> str:
    if model.prompt_formatter is None:
        prompt = instruction
    else:
        dialog = Dialog(messages=[instruction])
        model_input = ModelInput(extra={"dialog": dialog})
        prompt: str = model.prompt_formatter.prepare_prompt_text(model_input)[0]
        if verbose:
            print(f"prompt for {model.name}:\n{prompt}\n")
    response = model.client.generate(
        prompt=prompt,
        max_generated_tokens=max_generated_tokens,
        temperature=0.0,
        stop=model.stop,
    )
    if verbose:
        print(f"response from {model.name}:\n{response}\n")
    return response.content


def generate_model_responses(
    models: list[ModelDetails], prompt: str, max_generated_tokens: int
) -> list[str]:
    """Generate responses in parallel."""
    with multiprocessing.pool.ThreadPool(processes=len(models)) as pool:
        generated_texts: list[str] = pool.map(
            lambda model: generate_model_response(
                model, prompt, max_generated_tokens=max_generated_tokens
            ),
            models,
        )
    return generated_texts

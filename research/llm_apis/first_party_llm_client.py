"""Contains LLM client implementation that wraps first party clients."""

import j<PERSON>
from typing import Any, <PERSON><PERSON>

from base.prompt_format.common import (
    ChatRequestN<PERSON>,
    ChatRequestNodeType,
    ChatRequestText,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    Exchange,
)
from base.prompt_format_chat.prompt_formatter import StructuredChatPromptOutput
from base.third_party_clients.third_party_model_client import (
    ThirdPartyModelResponse,
    ToolChoice,
    ToolChoiceType,
    ToolDefinition,
)
from research.llm_apis.first_party_clients import LocalModelClient
from research.llm_apis.llm_client import (
    AssistantContentBlock,
    LLMClient,
    LLMMessages,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolFormattedResult,
    ToolParam,
)


class FirstPartyLLMClient(LLMClient):
    """An LLM client that wraps a first party client.

    This client adapts the LocalModelClient interface to the LLMClient interface,
    allowing first party clients to be used with the LLMClient API.
    """

    def __init__(
        self,
        first_party_client: LocalModelClient,
    ):
        """Initialize the first party LLM client.

        Args:
            first_party_client: The first party client to wrap.
        """
        self.first_party_client = first_party_client

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: list[ToolParam] = [],
        tool_choice: dict[str, str] | None = None,
        thinking_tokens: int | None = None,
    ) -> Tuple[list[AssistantContentBlock], dict[str, Any]]:
        """Generate responses using the wrapped first party client.

        Args:
            messages: A list of messages.
            max_tokens: The maximum number of tokens to generate.
            system_prompt: A system prompt.
            temperature: The temperature.
            tools: A list of tools.
            tool_choice: A tool choice.
            thinking_tokens: Number of tokens to use for thinking (not supported).

        Returns:
            A tuple of (generated response, metadata).
        """
        if thinking_tokens is not None:
            raise NotImplementedError("Thinking tokens are not supported yet.")

        # Convert LLMMessages to StructuredChatPromptOutput
        prompt_output = self._convert_messages_to_prompt_output(
            messages, system_prompt, tools
        )

        # Convert tool_choice to ToolChoice
        tool_choice_param = self._convert_tool_choice(tool_choice)

        # Call the first party client
        responses = self.first_party_client.generate_response(
            prompt_output=prompt_output,
            temperature=temperature,
            max_output_tokens=max_tokens,
            tool_choice=tool_choice_param,
        )

        # Convert ThirdPartyModelResponse to AssistantContentBlock
        # We still need to return AssistantContentBlock for backward compatibility
        assistant_content_blocks = self._convert_responses_to_content_blocks(responses)

        # Create metadata
        metadata = {
            "first_party_client": type(self.first_party_client).__name__,
            "response_count": len(responses),
            "model": getattr(self.first_party_client, "model", None),
        }

        return assistant_content_blocks, metadata

    def _convert_messages_to_prompt_output(
        self,
        messages: LLMMessages,
        system_prompt: str | None,
        tools: list[ToolParam],
    ) -> StructuredChatPromptOutput:
        """Convert LLMMessages to StructuredChatPromptOutput.

        Args:
            messages: The messages to convert.
            system_prompt: The system prompt.
            tools: The tools to convert.

        Returns:
            A StructuredChatPromptOutput.
        """
        # Convert tools to ToolDefinition
        tool_definitions = [
            self._convert_tool_param_to_definition(tool) for tool in tools
        ]

        # Process the messages to extract chat history and current message
        # The LLMMessages format is a list of message lists, where each message list
        # alternates between user and assistant messages

        # The last message in the list should be a user message (odd index)
        if not messages:
            current_message = []
            chat_history = []
        else:
            # Extract the current message (last user message)
            last_user_messages = messages[-1] if len(messages) % 2 == 1 else []

            # Convert the last user message to ChatRequestNode objects
            current_message = self._convert_user_content_blocks_to_nodes(
                last_user_messages
            )

            # Build chat history from previous messages
            chat_history = []
            for i in range(len(messages) - (1 if len(messages) % 2 == 1 else 0)):
                if i % 2 == 0:  # User message
                    user_message = self._convert_user_content_blocks_to_nodes(
                        messages[i]
                    )
                    if i + 1 < len(
                        messages
                    ):  # If there's a corresponding assistant message
                        assistant_message = (
                            self._convert_assistant_content_blocks_to_nodes(
                                messages[i + 1]
                            )
                        )
                        chat_history.append(Exchange(user_message, assistant_message))

        # Create a StructuredChatPromptOutput
        return StructuredChatPromptOutput(
            system_prompt=system_prompt or "",
            chat_history=chat_history,
            message=current_message,
            retrieved_chunks_in_prompt=[],
            retrieval_as_tool=False,
            tools=[],  # This is different from tool_definitions
            tool_definitions=tool_definitions,
        )

    def _convert_user_content_blocks_to_nodes(
        self, content_blocks: list
    ) -> list[ChatRequestNode]:
        """Convert user content blocks to ChatRequestNode objects.

        Args:
            content_blocks: The content blocks to convert.

        Returns:
            A list of ChatRequestNode objects.
        """
        result = []
        for i, block in enumerate(content_blocks):
            if isinstance(block, TextPrompt):
                result.append(
                    ChatRequestNode(
                        id=i,
                        type=ChatRequestNodeType.TEXT,
                        text_node=ChatRequestText(content=block.text),
                        tool_result_node=None,
                    )
                )
            elif isinstance(block, ToolFormattedResult):
                result.append(
                    ChatRequestNode(
                        id=i,
                        type=ChatRequestNodeType.TOOL_RESULT,
                        text_node=None,
                        tool_result_node=ChatRequestToolResult(
                            tool_use_id=block.tool_call_id,
                            content=block.tool_output,
                            is_error=False,
                        ),
                    )
                )

        return result

    def _convert_assistant_content_blocks_to_nodes(
        self, content_blocks: list
    ) -> list[ChatResultNode]:
        """Convert assistant content blocks to ChatResultNode objects.

        Args:
            content_blocks: The content blocks to convert.

        Returns:
            A list of ChatResultNode objects.
        """
        result = []
        for i, block in enumerate(content_blocks):
            if isinstance(block, TextResult):
                result.append(
                    ChatResultNode(
                        id=i,
                        type=ChatResultNodeType.RAW_RESPONSE,
                        content=block.text,
                    )
                )
            elif isinstance(block, ToolCall):
                result.append(
                    ChatResultNode(
                        id=i,
                        type=ChatResultNodeType.TOOL_USE,
                        content="",
                        tool_use=ChatResultToolUse(
                            name=block.tool_name,
                            input=block.tool_input,
                            tool_use_id=block.tool_call_id,
                        ),
                    )
                )

        return result

    def _convert_user_content_blocks_to_string(self, content_blocks: list) -> str:
        """Convert user content blocks to a string.

        Args:
            content_blocks: The content blocks to convert.

        Returns:
            A string representation of the content blocks.
        """
        result = []
        for block in content_blocks:
            if isinstance(block, TextPrompt):
                result.append(block.text)
            elif isinstance(block, ToolFormattedResult):
                result.append(f"[Tool Result: {block.tool_name}]\n{block.tool_output}")

        return "\n".join(result)

    def _convert_assistant_content_blocks_to_string(self, content_blocks: list) -> str:
        """Convert assistant content blocks to a string.

        Args:
            content_blocks: The content blocks to convert.

        Returns:
            A string representation of the content blocks.
        """
        result = []
        for block in content_blocks:
            if isinstance(block, TextResult):
                result.append(block.text)
            elif isinstance(block, ToolCall):
                import json

                result.append(
                    f"[Tool Call: {block.tool_name}]\n"
                    f"Arguments: {json.dumps(block.tool_input, indent=2)}"
                )

        return "\n".join(result)

    def _convert_tool_param_to_definition(
        self, tool_param: ToolParam
    ) -> ToolDefinition:
        """Convert a ToolParam to a ToolDefinition.

        Args:
            tool_param: The tool parameter to convert.

        Returns:
            A ToolDefinition.
        """
        # Ensure the input schema is properly formatted
        input_schema = tool_param.input_schema

        # Make sure the schema has a type field
        if "type" not in input_schema:
            input_schema["type"] = "object"

        # Make sure required fields are specified if present in the schema
        if "required" not in input_schema and "properties" in input_schema:
            # Default to making all properties required
            input_schema["required"] = list(input_schema["properties"].keys())

        return ToolDefinition(
            name=tool_param.name,
            description=tool_param.description,
            input_schema_json=json.dumps(input_schema),
        )

    def _convert_tool_choice(
        self, tool_choice: dict[str, str] | None
    ) -> ToolChoice | None:
        """Convert a tool_choice dict to a ToolChoice.

        Args:
            tool_choice: The tool choice to convert.

        Returns:
            A ToolChoice or None.
        """
        if tool_choice is None:
            return None

        if tool_choice["type"] == "any":
            return ToolChoice(type=ToolChoiceType.ANY)
        elif tool_choice["type"] == "auto":
            return ToolChoice(type=ToolChoiceType.AUTO)
        elif tool_choice["type"] == "tool":
            # For specific tool choice, we need to provide the tool name
            return ToolChoice(type=ToolChoiceType.TOOL, name=tool_choice["name"])

        return None

    def _convert_responses_to_content_blocks(
        self, responses: list[ThirdPartyModelResponse]
    ) -> list[AssistantContentBlock]:
        """Convert ThirdPartyModelResponse objects to AssistantContentBlock objects.

        Args:
            responses: The responses to convert.

        Returns:
            A list of AssistantContentBlock objects.
        """
        content_blocks = []

        # Combine consecutive text responses
        combined_text = ""

        for response in responses:
            # If we have a tool use, flush any accumulated text first
            if response.tool_use and combined_text:
                content_blocks.append(TextResult(text=combined_text))
                combined_text = ""

            if response.tool_use:
                # Convert tool use to ToolCall
                content_blocks.append(
                    ToolCall(
                        tool_call_id=response.tool_use.tool_use_id or "",
                        tool_name=response.tool_use.tool_name,
                        tool_input=response.tool_use.input,
                    )
                )
            elif response.text:
                # Accumulate text
                if combined_text:
                    combined_text += response.text
                else:
                    combined_text = response.text

        # Add any remaining text
        if combined_text:
            content_blocks.append(TextResult(text=combined_text))

        return content_blocks

    def _convert_responses_to_result_nodes(
        self, responses: list[ThirdPartyModelResponse]
    ) -> list[ChatResultNode]:
        """Convert ThirdPartyModelResponse objects to ChatResultNode objects.

        Args:
            responses: The responses to convert.

        Returns:
            A list of ChatResultNode objects.
        """
        result_nodes = []

        # Combine consecutive text responses
        combined_text = ""
        node_id = 0

        for response in responses:
            # If we have a tool use, flush any accumulated text first
            if response.tool_use and combined_text:
                result_nodes.append(
                    ChatResultNode(
                        id=node_id,
                        type=ChatResultNodeType.RAW_RESPONSE,
                        content=combined_text,
                    )
                )
                node_id += 1
                combined_text = ""

            if response.tool_use:
                # Convert tool use to ChatResultNode with ChatResultToolUse
                result_nodes.append(
                    ChatResultNode(
                        id=node_id,
                        type=ChatResultNodeType.TOOL_USE,
                        content="",
                        tool_use=ChatResultToolUse(
                            name=response.tool_use.tool_name,
                            input=response.tool_use.input,
                            tool_use_id=response.tool_use.tool_use_id or "",
                        ),
                    )
                )
                node_id += 1
            elif response.text:
                # Accumulate text
                if combined_text:
                    combined_text += response.text
                else:
                    combined_text = response.text

        # Add any remaining text
        if combined_text:
            result_nodes.append(
                ChatResultNode(
                    id=node_id,
                    type=ChatResultNodeType.RAW_RESPONSE,
                    content=combined_text,
                )
            )

        return result_nodes

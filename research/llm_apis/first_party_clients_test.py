"""Tests for first party clients."""

import json
import unittest
from unittest import mock

import pytest

from base.prompt_format_chat.prompt_formatter import StructuredChatPromptOutput
from base.third_party_clients.third_party_model_client import (
    ThirdPartyModelResponse,
    ToolChoice,
    ToolDefinition,
)
from research.llm_apis.first_party_clients import (
    LocalModelClient,
    QwenLocalClient,
    ToolParsingError,
)

# Constants for testing
TOOL_START_TOKEN = 100
TOOL_END_TOKEN = 101
STOP_TOKEN = 102


class TestLocalModelClient(unittest.TestCase):
    """Tests for LocalModelClient."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mocks
        self.mock_model = mock.MagicMock()
        self.mock_formatter = mock.MagicMock()
        self.stop_tokens = [0]

        # Configure mock model
        mock_generation_output = mock.MagicMock()
        mock_generation_output.tokens = [1, 2, 3, 0]
        self.mock_model.raw_generate_tokens.return_value = mock_generation_output
        self.mock_model.tokenizer.detokenize.return_value = "123"

        # Configure mock formatter
        mock_formatted_output = mock.MagicMock()
        mock_formatted_output.tokens = [4, 5, 6]
        self.mock_formatter.format_prompt.return_value = mock_formatted_output

        # Create client
        self.client = LocalModelClient(
            self.mock_model, self.mock_formatter, self.stop_tokens
        )

        # Create test prompt
        self.prompt_output = StructuredChatPromptOutput(
            system_prompt="You are a helpful assistant.",
            chat_history=[],
            message="Hello",
            retrieved_chunks_in_prompt=[],
            retrieval_as_tool=False,
            tools=[],
            tool_definitions=[],
        )

    def test_initialization(self):
        """Test initialization of LocalModelClient."""
        self.assertEqual(self.client.model, self.mock_model)
        self.assertEqual(self.client.tokenize_formatter, self.mock_formatter)
        self.assertEqual(self.client.stop_tokens, self.stop_tokens)

    def test_generate_response_basic(self):
        """Test basic response generation."""
        responses = self.client.generate_response(self.prompt_output)

        # Check that the formatter was called with the prompt output
        self.mock_formatter.format_prompt.assert_called_once_with(self.prompt_output)

        # Check that the model was called with the formatted tokens
        self.mock_model.raw_generate_tokens.assert_called_once()

        # Check that the model was called with the right parameters
        # We need to inspect how the model was called
        # Since we can't directly access the GenerationOptions object that was created,
        # we'll verify the model was called correctly by checking the response

        # Check the response
        self.assertEqual(len(responses), 1)
        self.assertIsInstance(responses[0], ThirdPartyModelResponse)
        self.assertEqual(responses[0].text, "123")  # Detokenized [1, 2, 3]

    def test_generate_response_with_temperature(self):
        """Test response generation with temperature."""
        temperature = 0.7
        self.client.generate_response(self.prompt_output, temperature=temperature)

        # Check that the model was called
        self.mock_model.raw_generate_tokens.assert_called_once()

    def test_generate_response_with_max_output_tokens(self):
        """Test response generation with max_output_tokens."""
        max_output_tokens = 100
        self.client.generate_response(
            self.prompt_output, max_output_tokens=max_output_tokens
        )

        # Check that the model was called
        self.mock_model.raw_generate_tokens.assert_called_once()

    def test_unsupported_features(self):
        """Test that unsupported features raise NotImplementedError."""
        with self.assertRaises(NotImplementedError):
            self.client.generate_response(self.prompt_output, tool_choice=ToolChoice())

        with self.assertRaises(NotImplementedError):
            self.client.generate_response(self.prompt_output, prefill="prefill")

        with self.assertRaises(NotImplementedError):
            self.client.generate_response(self.prompt_output, use_caching=True)

    def test_parse_response(self):
        """Test parsing of response tokens."""
        response_tokens = [1, 2, 3, 0]
        responses = self.client.parse_response(response_tokens, self.prompt_output)

        # Check that the stop token was removed
        self.mock_model.tokenizer.detokenize.assert_called_once_with([1, 2, 3])

        # Check the response
        self.assertEqual(len(responses), 1)
        self.assertIsInstance(responses[0], ThirdPartyModelResponse)
        self.assertEqual(responses[0].text, "123")  # Detokenized [1, 2, 3]

    def test_handle_prefill(self):
        """Test handle_prefill method."""
        prompt_tokens = [1, 2, 3]
        result = self.client.handle_prefill(prompt_tokens, None, None)
        self.assertEqual(result, prompt_tokens)

        with self.assertRaises(NotImplementedError):
            self.client.handle_prefill(prompt_tokens, ToolChoice(), None)

        with self.assertRaises(NotImplementedError):
            self.client.handle_prefill(prompt_tokens, None, "prefill")


class TestQwenLocalClient(unittest.TestCase):
    """Tests for QwenLocalClient."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mocks
        self.mock_model = mock.MagicMock()
        self.mock_formatter = mock.MagicMock()
        self.stop_tokens = [STOP_TOKEN]

        # Configure mock model
        mock_generation_output = mock.MagicMock()
        mock_generation_output.tokens = [1, 2, 3, STOP_TOKEN]
        self.mock_model.raw_generate_tokens.return_value = mock_generation_output
        self.mock_model.tokenizer.detokenize.return_value = "123"

        # Configure mock formatter
        mock_formatted_output = mock.MagicMock()
        mock_formatted_output.tokens = [4, 5, 6]
        self.mock_formatter.format_prompt.return_value = mock_formatted_output

        # Create client
        self.client = QwenLocalClient(
            self.mock_model,
            self.mock_formatter,
            self.stop_tokens,
            tool_start_token=TOOL_START_TOKEN,
            tool_end_token=TOOL_END_TOKEN,
        )

        # Create test prompt
        self.prompt_output = StructuredChatPromptOutput(
            system_prompt="You are a helpful assistant.",
            chat_history=[],
            message="Hello",
            retrieved_chunks_in_prompt=[],
            retrieval_as_tool=False,
            tools=[],
            tool_definitions=[],
        )

    def test_initialization(self):
        """Test initialization of QwenLocalClient."""
        self.assertEqual(self.client.model, self.mock_model)
        self.assertEqual(self.client.tokenize_formatter, self.mock_formatter)
        self.assertEqual(self.client.stop_tokens, self.stop_tokens)
        self.assertEqual(self.client.tool_start_token, TOOL_START_TOKEN)
        self.assertEqual(self.client.tool_end_token, TOOL_END_TOKEN)

    def test_initialization_without_tool_tokens(self):
        """Test initialization without tool tokens."""
        client = QwenLocalClient(
            self.mock_model,
            self.mock_formatter,
            self.stop_tokens,
        )
        self.assertEqual(client.tool_start_token, None)
        self.assertEqual(client.tool_end_token, None)

    def test_validate_tool_call_valid(self):
        """Test validation of a valid tool call."""
        tool_name = "test_tool"
        tool_input = {"param": "test_value"}
        tool_definitions = [
            ToolDefinition(
                name="test_tool",
                description="Test tool",
                input_schema_json=json.dumps(
                    {
                        "type": "object",
                        "properties": {"param": {"type": "string"}},
                        "required": ["param"],
                    }
                ),
            )
        ]

        # Should not raise an exception
        result = self.client.validate_tool_call(tool_name, tool_input, tool_definitions)
        self.assertTrue(result)

    def test_validate_tool_call_missing_tool(self):
        """Test validation with a missing tool."""
        tool_name = "missing_tool"
        tool_input = {"param": "test_value"}
        tool_definitions = [
            ToolDefinition(
                name="test_tool",
                description="Test tool",
                input_schema_json=json.dumps(
                    {
                        "type": "object",
                        "properties": {"param": {"type": "string"}},
                        "required": ["param"],
                    }
                ),
            )
        ]

        with self.assertRaises(ToolParsingError):
            self.client.validate_tool_call(tool_name, tool_input, tool_definitions)

    def test_validate_tool_call_invalid_input(self):
        """Test validation with invalid tool input."""
        tool_name = "test_tool"
        tool_input = {"wrong_param": "test_value"}  # Missing required param
        tool_definitions = [
            ToolDefinition(
                name="test_tool",
                description="Test tool",
                input_schema_json=json.dumps(
                    {
                        "type": "object",
                        "properties": {"param": {"type": "string"}},
                        "required": ["param"],
                    }
                ),
            )
        ]

        with self.assertRaises(ToolParsingError):
            self.client.validate_tool_call(tool_name, tool_input, tool_definitions)

    def test_parse_response_no_tool_tokens(self):
        """Test parsing response when tool tokens are not defined."""
        # Create a client without tool tokens
        client = QwenLocalClient(
            self.mock_model,
            self.mock_formatter,
            self.stop_tokens,
        )

        response_tokens = [1, 2, 3, STOP_TOKEN]
        responses = client.parse_response(response_tokens, self.prompt_output)

        # Should just return the full response as text
        self.assertEqual(len(responses), 1)
        self.assertIsInstance(responses[0], ThirdPartyModelResponse)
        self.assertEqual(responses[0].text, "123")
        self.assertIsNone(responses[0].tool_use)

    def test_parse_response_with_tool_section(self):
        """Test parsing response with a tool section."""
        # Configure mock model to detokenize tool JSON correctly
        self.mock_model.tokenizer.detokenize.side_effect = lambda tokens: (
            "123"
            if tokens == [1, 2, 3]
            else "456"
            if tokens == [4, 5, 6]
            else '{"name":"test_tool","arguments":{"param":"test_value"}}'
            if tokens == [7, 8, 9]
            else "unknown"
        )

        # Create tool definitions
        tool_definitions = [
            ToolDefinition(
                name="test_tool",
                description="Test tool",
                input_schema_json=json.dumps(
                    {
                        "type": "object",
                        "properties": {"param": {"type": "string"}},
                        "required": ["param"],
                    }
                ),
            )
        ]

        # Create prompt with tool definitions
        prompt_output = StructuredChatPromptOutput(
            system_prompt="You are a helpful assistant.",
            chat_history=[],
            message="Hello",
            retrieved_chunks_in_prompt=[],
            retrieval_as_tool=False,
            tools=["test_tool"],
            tool_definitions=tool_definitions,
        )

        # Response with tool section
        response_tokens = [
            1,
            2,
            3,  # Text tokens
            TOOL_START_TOKEN,
            7,
            8,
            9,  # Tool JSON tokens
            TOOL_END_TOKEN,
            4,
            5,
            6,  # More text tokens
            STOP_TOKEN,
        ]

        responses = self.client.parse_response(response_tokens, prompt_output)

        # Should return 3 responses: text before tool, tool use, text after tool
        self.assertEqual(len(responses), 3)

        # First response should be text before tool
        self.assertIsInstance(responses[0], ThirdPartyModelResponse)
        self.assertEqual(responses[0].text, "123")
        self.assertIsNone(responses[0].tool_use)

        # Second response should be tool use
        self.assertIsInstance(responses[1], ThirdPartyModelResponse)
        self.assertEqual(responses[1].text, "")
        self.assertIsNotNone(responses[1].tool_use)
        if responses[1].tool_use:  # Type checking for IDE
            self.assertEqual(responses[1].tool_use.tool_name, "test_tool")
            self.assertEqual(responses[1].tool_use.input, {"param": "test_value"})

        # Third response should be text after tool
        self.assertIsInstance(responses[2], ThirdPartyModelResponse)
        self.assertEqual(responses[2].text, "456")
        self.assertIsNone(responses[2].tool_use)

    def test_parse_response_with_invalid_tool_json(self):
        """Test parsing response with invalid tool JSON."""
        # Configure mock model to detokenize tool JSON incorrectly
        self.mock_model.tokenizer.detokenize.side_effect = lambda tokens: (
            "123"
            if tokens == [1, 2, 3]
            else "invalid json"
            if tokens == [7, 8, 9]
            else "unknown"
        )

        # Response with tool section containing invalid JSON
        response_tokens = [
            1,
            2,
            3,  # Text tokens
            TOOL_START_TOKEN,
            7,
            8,
            9,  # Invalid tool JSON tokens
            TOOL_END_TOKEN,
            STOP_TOKEN,
        ]

        # Should raise ToolParsingError
        with self.assertRaises(ToolParsingError):
            self.client.parse_response(response_tokens, self.prompt_output)

    def test_parse_response_with_mismatched_tool_tokens(self):
        """Test parsing response with mismatched tool tokens."""
        # Response with mismatched tool tokens (start without end)
        response_tokens = [
            1,
            2,
            3,  # Text tokens
            TOOL_START_TOKEN,
            7,
            8,
            9,  # Tool JSON tokens
            STOP_TOKEN,  # Missing TOOL_END_TOKEN
        ]

        # Should raise ToolParsingError
        with self.assertRaises(ToolParsingError):
            self.client.parse_response(response_tokens, self.prompt_output)


if __name__ == "__main__":
    unittest.main()

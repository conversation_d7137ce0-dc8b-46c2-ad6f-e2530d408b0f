"""Wrapper for LLMClient that adds tool support to any model.

This module provides a wrapper class that implements the LLMClient interface
and adds tool support to any model, even those that don't natively support tools.
"""

from typing import Any, Dict, List, Optional, Tuple

from research.llm_apis.llm_client import (
    AssistantContentBlock,
    LLMClient,
    LLMMessages,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolFormattedResult,
    ToolParam,
    ToolSupportProvider,
)


class ToolSupportWrapper(LLMClient):
    """Wrapper for LLMClient that adds tool support to any model.

    This class wraps any LLMClient instance and adds tool support by:
    1. Creating a system prompt that includes tool details
    2. Calling the wrapped client with the modified system prompt
    3. Parsing the response to extract tool calls
    4. Converting the parsed tool calls to the format expected by the LLMClient interface

    Example usage:
    ```python
    # Create a client that doesn't natively support tools
    base_client = FireworksClient(model_name="accounts/fireworks/models/deepseek-r1")

    # Wrap it with the ToolSupportWrapper
    client = ToolSupportWrapper(base_client)

    # Now you can use tools with this client
    response, metadata = client.generate(
        messages=[[TextPrompt(text="What's the weather in San Francisco?")]],
        max_tokens=1024,
        tools=[get_weather_tool],
    )
    ```
    """

    def __init__(self, client: LLMClient):
        """Initialize the wrapper with an LLMClient instance.

        Args:
            client: The LLMClient instance to wrap
        """
        self.client = client

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: Optional[str] = None,
        temperature: float = 0.0,
        tools: List[ToolParam] = [],
        tool_choice: Optional[Dict[str, str]] = None,
        thinking_tokens: Optional[int] = None,
    ) -> Tuple[List[AssistantContentBlock], Dict[str, Any]]:
        """Generate responses with tool support.

        This method:
        1. If tools are provided, creates a system prompt that includes tool details
        2. Calls the wrapped client with the modified system prompt
        3. Parses the response to extract tool calls
        4. Returns the parsed response

        Args:
            messages: A list of messages.
            max_tokens: The maximum number of tokens to generate.
            system_prompt: A system prompt.
            temperature: The temperature.
            tools: A list of tools.
            tool_choice: A tool choice.
            thinking_tokens: Optional number of tokens to use for thinking.

        Returns:
            A tuple containing:
            - A list of assistant content blocks
            - A dictionary of metadata
        """
        original_system_prompt = system_prompt
        use_tool_support = False

        # If tools are provided, modify the system prompt to include tool details
        if tools:
            use_tool_support = True
            system_prompt = ToolSupportProvider.create_tool_system_prompt(
                tools=tools, base_system_prompt=system_prompt
            )

            # Convert the messages to a prompt format that includes previous tool calls and results
            if messages and len(messages) > 1:
                # Only convert previous messages if there are any
                messages_prompt = ToolSupportProvider.convert_messages_to_prompt(
                    messages
                )

                # Prepend the messages prompt to the first user message in the current exchange
                # Find the first user message in the last exchange
                if messages[-1]:
                    for i, block in enumerate(messages[-1]):
                        if isinstance(block, TextPrompt):  # It's a TextPrompt
                            # Prepend the messages history to the user's message
                            # TextPrompt objects have a text attribute
                            text_prompt = block  # Type hint for clarity
                            text_prompt.text = (
                                f"{messages_prompt}\n\n{text_prompt.text}"
                            )
                            break

        # Call the wrapped client with the modified system prompt
        # We don't pass the tools to the wrapped client since we're handling them ourselves
        wrapped_tools = [] if use_tool_support else tools
        response, metadata = self.client.generate(
            messages=messages,
            max_tokens=max_tokens,
            system_prompt=system_prompt,
            temperature=temperature,
            tools=wrapped_tools,
            tool_choice=tool_choice,
            thinking_tokens=thinking_tokens,
        )

        # If we're using tool support, parse the response to extract tool calls
        if use_tool_support:
            # Find the text response in the response blocks
            text_response = ""
            tool_calls = []

            # First, collect all text responses and tool calls
            for block in response:
                if isinstance(block, TextResult):
                    # TextResult objects have a text attribute
                    text_response += block.text
                elif isinstance(block, ToolCall):
                    # Save tool calls to return them along with any parsed tool calls
                    tool_calls.append(block)

            # Process the text response if we have one
            parsed_text_response = []
            provider_metadata = {}

            if text_response:
                # Parse the text response to extract tool calls
                parsed_text_response, provider_metadata = (
                    ToolSupportProvider.convert_to_assistant_content_blocks(
                        text=text_response
                    )
                )

            # Combine parsed tool calls from text with any direct tool calls
            combined_response = parsed_text_response + tool_calls

            if (
                combined_response
            ):  # If we have any response (either parsed or direct tool calls)
                # Update the metadata
                metadata.update(
                    {
                        "original_system_prompt": original_system_prompt,
                        "tool_support_provider": provider_metadata,
                    }
                )

                return combined_response, metadata

        # If we're not using tool support or there was no text response, return the original response
        return response, metadata

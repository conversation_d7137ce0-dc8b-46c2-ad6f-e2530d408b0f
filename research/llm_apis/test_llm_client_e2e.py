"""End-to-end tests for the LLM client."""

import json
import os
import pytest
from pathlib import Path
from typing import List, Dict, Any

from research.llm_apis.llm_client import (
    FireworksClient,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolFormattedResult,
    ToolParam,
    ToolSupportProvider,
)
from research.llm_apis.tool_support_wrapper import ToolSupportWrapper


@pytest.mark.manual
def test_fireworks_client_with_tool_support_wrapper():
    """Test FireworksClient with ToolSupportWrapper for a model that doesn't natively support tools.

    This test:
    1. Creates a FireworksClient with an API key from $HOME/.augment/fireworks_api_token
    2. Uses the deepseek-r1 model (which doesn't natively support tools)
    3. Wraps it with ToolSupportWrapper to enable tool support
    4. Uses the wrapper to call the model with a "get_temperature" tool
    5. Processes the tool response and sends it back to the model
    6. Verifies the final response is good

    This test is marked as manual because it requires:
    - A valid Fireworks API key in $HOME/.augment/fireworks_api_token
    - Internet access to call the Fireworks API
    """
    # Check if the API key file exists
    api_key_path = Path(os.path.expanduser("~/.augment/fireworks_api_token"))
    if not api_key_path.exists():
        pytest.skip(f"API key file not found at {api_key_path}")

    # Read the API key
    api_key = api_key_path.read_text().strip()
    if not api_key:
        pytest.skip("API key is empty")

    # Create the FireworksClient with the deepseek-r1 model
    base_client = FireworksClient(
        api_key=api_key,
        model_name=FireworksClient.MODEL_DEEPSEEK_R1,  # This model doesn't natively support tools
    )

    # Wrap it with the ToolSupportWrapper
    client = ToolSupportWrapper(base_client)

    # Define the get_temperature tool
    get_temperature_tool = ToolParam(
        name="get_temperature",
        description="Get the current temperature in a given location",
        input_schema={
            "type": "object",
            "properties": {
                "location": {
                    "type": "string",
                    "description": "The location to get the temperature for",
                }
            },
            "required": ["location"],
        },
    )

    # Initial user question
    user_question = "What's the temperature in San Francisco today?"

    # First call to the model to get the tool call
    print("\n=== First call to the model ===")
    response1, metadata1 = client.generate(
        messages=[[TextPrompt(text=user_question)]],
        max_tokens=1024,
        tools=[get_temperature_tool],
    )

    # Print the response
    print("Model response:")
    for block in response1:
        if isinstance(block, TextResult):
            print(f"Text: {block.text}")
        elif isinstance(block, ToolCall):
            print(f"Tool Call: {block.tool_name}")
            print(f"Arguments: {json.dumps(block.tool_input, indent=2)}")
            print(f"ID: {block.tool_call_id}")

    # Verify that the model called the get_temperature tool
    tool_calls = [block for block in response1 if isinstance(block, ToolCall)]
    assert len(tool_calls) > 0, "Model did not make any tool calls"

    tool_call = tool_calls[0]
    assert (
        tool_call.tool_name == "get_temperature"
    ), f"Expected tool_name 'get_temperature', got '{tool_call.tool_name}'"
    assert "location" in tool_call.tool_input, "Tool input missing 'location' parameter"
    assert (
        tool_call.tool_input["location"].lower() == "san francisco"
    ), f"Expected location 'San Francisco', got '{tool_call.tool_input['location']}'"

    # Simulate executing the tool and getting a result
    temperature = 72  # Canned temperature for San Francisco
    tool_result = f"The current temperature in {tool_call.tool_input['location']} is {temperature}°F."

    # Create a ToolFormattedResult
    tool_formatted_result = ToolFormattedResult(
        tool_call_id=tool_call.tool_call_id,
        tool_name=tool_call.tool_name,
        tool_output=tool_result,
    )

    # For Fireworks, we need to convert the conversation to a single prompt
    # because it only supports one entry per message
    print("\n=== Second call to the model with tool result ===")

    # Create a conversation history that includes the tool result
    conversation = [
        [TextPrompt(text=user_question)],
        response1,
        [tool_formatted_result],
    ]

    # Convert the conversation to a single prompt using ToolSupportProvider
    conversation_prompt = ToolSupportProvider.convert_messages_to_prompt(conversation)
    print(f"Conversation prompt:\n{conversation_prompt}")

    # Call the model with the conversation prompt
    response2, metadata2 = client.generate(
        messages=[[TextPrompt(text=conversation_prompt)]],
        max_tokens=1024,
        tools=[get_temperature_tool],
    )

    # Print the final response
    print("Final model response:")
    for block in response2:
        if isinstance(block, TextResult):
            print(f"Text: {block.text}")
        elif isinstance(block, ToolCall):
            print(f"Tool Call: {block.tool_name}")
            print(f"Arguments: {json.dumps(block.tool_input, indent=2)}")
            print(f"ID: {block.tool_call_id}")

    # Verify that the final response is a text response (not a tool call)
    text_results = [block for block in response2 if isinstance(block, TextResult)]
    assert len(text_results) > 0, "Model did not provide a text response"

    # Verify that the final response mentions the temperature
    final_response = text_results[0].text
    assert (
        str(temperature) in final_response
    ), f"Final response does not mention the temperature ({temperature}): {final_response}"
    assert (
        "San Francisco" in final_response
    ), f"Final response does not mention San Francisco: {final_response}"

    print("\n=== Test completed successfully ===")

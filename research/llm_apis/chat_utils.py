"""LLM API with support for multiple clients."""

import json
from concurrent.futures import ThreadPoolExecutor
from threading import <PERSON>
from time import time
from typing import Any, Iterable, Literal, Optional, Protocol

import anthropic
import openai
import vertexai
from anthropic.types import ContentBlockDeltaEvent, TextDelta
from google.auth import transport
from kubernetes.client.exceptions import ApiException
from vertexai.generative_models import (
    GenerationConfig,
    GenerativeModel,
)

from base.datasets.gcp_creds import get_gcp_creds
from research.core.augment_secrets import get_k8s_secret
from research.core.constants import GCP_PROJECT_ID, GCP_REGION, GCP_VERTEX_REGION
from research.data.synthetic_code_edit.api_lib import G<PERSON><PERSON>rapper, OpenAIChatModels
from research.llm_apis.completion_utils import LlamaCppClient, TritonClient
from research.llm_apis.llama3_tokenizer import (
    ChatFormat,
    Dialog,
    Message,
    Role,
    Tokenizer,
)


class ChatClient(Protocol):
    """A chat client."""

    def generate(
        self,
        messages: list[str],
        max_tokens: int,
        system_prompt: Optional[str] = None,
        temperature: float = 0.0,
    ) -> str:
        """Generate responses.

        Args:
            messages: A list of messages.
            system_prompt: A system prompt.
            max_tokens: The maximum number of tokens to generate.
            temperature: The temperature.

        Returns:
            A generated response.
        """
        ...

    # TODO(guy) clean it up.. can we use our own tokenizer interface?
    @property
    def tokenizer(self) -> Any:
        """The tokenizer associated with this model.

        The tokenizer should have a encode(text) method that returns a
        list of integer token IDs, and a decode(tokens) method that
        returns the text string.
        """
        ...

    def get_prompt_token_length(
        self,
        messages: list[str],
        system_prompt: Optional[str] = None,
    ) -> int:
        """Returns the number of tokens in the prompt."""
        ...

    def get_token_length(self, text: str) -> int:
        """Returns the number of tokens in the text."""
        ...

    @property
    def address(self) -> str:
        """The address of the server, if available, for logging purposes."""
        return "[address unavailable]"


class OpenAIClient:
    """API client for OpenAI chat API."""

    def __init__(self, gpt_wrapper: GptWrapper, model: OpenAIChatModels):
        self.gpt_wrapper = gpt_wrapper
        self.model: OpenAIChatModels = model

    def generate(
        self,
        messages: list[str],
        max_tokens: int,
        system_prompt: Optional[str] = None,
    ) -> str:
        result = self.gpt_wrapper(
            messages=messages,
            model=self.model,
            system_prompt=system_prompt,
            max_tokens=max_tokens,
        )
        assert isinstance(result, str)
        return result


class Llama3ChatClient:
    """API client for LLaMA 3 on llama.cpp or Triton."""

    def __init__(
        self,
        server_type: Literal["llama.cpp", "triton"],
        address: str,
        tokenizer_model_path: str = "/mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-8B-Instruct/tokenizer.model",
        timeout: int = 20,
    ):
        self.tokenizer = Tokenizer(tokenizer_model_path)
        self.prompt_formatter = ChatFormat(self.tokenizer)
        self.address = address
        self.server_type = server_type

        if server_type == "llama.cpp":
            self.client = LlamaCppClient(address=address, timeout=timeout)
        elif server_type == "triton":
            # Triton doesn't know when to stop unless we give it the end-of-document token.
            self.client = TritonClient(
                address=address,
                timeout=timeout,
                eod_id=self.tokenizer.special_tokens["<|eot_id|>"],
            )
        else:
            raise ValueError(f"Unknown server type: {server_type}")

    def _prepare_prompt_text(
        self,
        messages: list[str],
        system_prompt: Optional[str] = None,
    ) -> str:
        prompt_tokens = self._prepare_prompt_tokens(messages, system_prompt)
        return self.tokenizer.decode(prompt_tokens)

    def _prepare_prompt_tokens(
        self,
        messages: list[str],
        system_prompt: Optional[str] = None,
    ) -> list[int]:
        roles: list[Role] = ["user", "assistant"]
        dialog: Dialog = []

        if system_prompt:
            dialog.append(Message(role="system", content=system_prompt))

        for i, message in enumerate(messages):
            dialog.append(Message(role=roles[i % 2], content=message))

        return self.prompt_formatter.encode_dialog_prompt(dialog)

    def _clean_response(self, response: str) -> str:
        return response.split("<|eot_id|>")[0]

    def generate(
        self,
        messages: list[str],
        max_tokens: int,
        system_prompt: Optional[str] = None,
        temperature: float = 0.0,
    ) -> str:
        if self.server_type == "llama.cpp":
            prompt = self._prepare_prompt_tokens(messages, system_prompt)
        else:
            prompt = self._prepare_prompt_text(messages, system_prompt)

        response = self.client.generate(
            prompt=prompt, max_generated_tokens=max_tokens, temperature=temperature
        )
        # TODO(guy) restore
        # return response.content
        return self._clean_response(response.content)

    def generate_stream(
        self,
        messages: list[str],
        max_tokens: int,
        system_prompt: Optional[str] = None,
    ) -> Iterable[str]:
        prompt = self._prepare_prompt_text(messages, system_prompt)
        for response in self.client.generate_stream(
            prompt=prompt, max_generated_tokens=max_tokens
        ):
            response_text = self._clean_response(response)
            yield response_text

    def get_prompt_token_length(
        self,
        messages: list[str],
        system_prompt: Optional[str] = None,
    ) -> int:
        """Returns the number of tokens in the prompt."""
        return len(self._prepare_prompt_tokens(messages, system_prompt))

    def get_token_length(self, text: str) -> int:
        return len(self.tokenizer.encode(text, bos=True, eos=True))


class Llama3LlamaCppApiClient(Llama3ChatClient):
    """For backward compatibility with existing pipeline code."""

    def __init__(
        self,
        address: str,
        tokenizer_model_path: str = "/mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-8B-Instruct/tokenizer.model",
        timeout: int = 20,
    ):
        super().__init__(
            address=address,
            tokenizer_model_path=tokenizer_model_path,
            timeout=timeout,
            server_type="llama.cpp",
        )


def run_health_checks(addresses: list[str] | list[ChatClient]):
    """Run the health checks in parallel."""
    if not addresses:
        print("No addresses provided, skipping health checks.")
        return

    print_lock = Lock()

    def print_with_lock(message):
        with print_lock:
            print(message)

    def run_health_check(address_or_client: str | ChatClient) -> bool:
        try:
            if isinstance(address_or_client, str):
                client = Llama3ChatClient(
                    "triton", address=address_or_client, timeout=10
                )
            else:
                client = address_or_client
            response = client.generate(messages=["hello"], max_tokens=32)
            print_with_lock(f"Health check response for {client.address}: {response}")
            if not response:
                print_with_lock(
                    f"Health check failed for {address_or_client}, received empty response"
                )
                return False
            return True
        except Exception as e:  # pylint: disable=broad-except
            print_with_lock(f"Health check failed for {address_or_client}: {e}")
            return False

    print("Running health checks...")

    with ThreadPoolExecutor(max_workers=len(addresses)) as executor:
        futures = [executor.submit(run_health_check, address) for address in addresses]
        for future in futures:
            result = future.result()
            if not result:
                raise ValueError("Health check failed")

    print("Health check passed.")


def _get_openai_style_dialog(
    messages: list[str], system_prompt: Optional[str] = None
) -> list[dict[str, str]]:
    """Returns a dialog for OpenAI API.

    [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Who won the world series in 2020?"},
        {"role": "assistant", "content": "The Los Angeles Dodgers won the World Series in 2020."},
        {"role": "user", "content": "Who won the world series in 2021?"},
        ...
    ]
    """
    oai_dialog: list[dict[str, str]] = []

    if system_prompt:
        oai_dialog.append({"role": "system", "content": system_prompt})

    for i, message in enumerate(messages):
        if i % 2 == 0:
            oai_dialog.append({"role": "user", "content": message})
        else:
            oai_dialog.append({"role": "assistant", "content": message})
    return oai_dialog


class OpenAIAPIClient(ChatClient):
    """A client for OpenAI API based servers (that are not actually OpenAI)."""

    def __init__(
        self,
        base_url: str,
        api_key: str,
        model: Optional[str] = None,
        vllm_prompt_workaround: bool = False,
    ):
        """If a model is not specified, the only model on the server is used."""
        self.update_client(base_url=base_url, api_key=api_key)

        if model:
            self.model = model
        else:
            model_names = [model.id for model in self.openai_client.models.list().data]
            if len(model_names) != 1:
                raise ValueError(f"Expected 1 model, got {len(model_names)}")
            self.model = model_names[0]

        self.vllm_prompt_workaround = vllm_prompt_workaround

    def update_client(self, base_url: str, api_key: str):
        self.openai_client = openai.OpenAI(base_url=base_url, api_key=api_key)

    def _process_response(self, response: str) -> str:
        # DeepSeek Coder V2 Instruct responds in chinese if the prompt ends
        # with "Assistant: ". So the prompt ends with "Assistant:", and we
        # strip the first space character.
        if self.vllm_prompt_workaround:
            if response.startswith(" "):
                return response[1:]
        return response

    def generate(
        self,
        messages: list[str],
        max_tokens: int,
        system_prompt: Optional[str] = None,
        temperature: float = 0.0,
    ) -> str:
        completion = self.openai_client.chat.completions.create(
            model=self.model,
            messages=_get_openai_style_dialog(messages, system_prompt),  # type: ignore
            max_tokens=max_tokens,
            temperature=temperature,
        )  # type: ignore

        response = completion.choices[0].message.content
        assert response is not None
        response = self._process_response(response)
        return response

    def generate_stream(
        self,
        messages: list[str],
        max_tokens: int,
        system_prompt: Optional[str] = None,
        temperature: float = 0.0,
    ) -> Iterable[str]:
        is_first_not_none_response = True

        for response in self.openai_client.chat.completions.create(
            model=self.model,
            messages=_get_openai_style_dialog(messages, system_prompt),  # type: ignore
            max_tokens=max_tokens,
            temperature=temperature,
            stream=True,
        ):  # type: ignore
            content = response.choices[0].delta.content
            if content is not None:
                if is_first_not_none_response:
                    content = self._process_response(content)
                    is_first_not_none_response = False
                yield content

    @property
    def tokenizer(self) -> Any:
        return None

    def get_prompt_token_length(
        self,
        messages: list[str],
        system_prompt: Optional[str] = None,
    ) -> int:
        raise NotImplementedError()

    def get_token_length(self, text: str) -> int:
        raise NotImplementedError()


class AnthropicClient(ChatClient):
    """A client for Anthropic API servers."""

    def __init__(
        self,
        api_key: str,
        model: str = "claude-3-5-sonnet-20240620",
    ):
        """API key is taken from os.environ.get("ANTHROPIC_API_KEY") by default."""
        self.model = model
        self.client = anthropic.Anthropic(api_key=api_key)

    def generate(
        self,
        messages: list[str],
        max_tokens: int,
        system_prompt: Optional[str] = None,
        temperature: float = 0.0,
    ) -> str:
        kwargs = {
            "model": self.model,
            "messages": _get_openai_style_dialog(messages),
            "temperature": temperature,
            "max_tokens": max_tokens,
        }

        if system_prompt is not None:
            kwargs["system"] = system_prompt

        message = self.client.messages.create(**kwargs)
        return message.content[0].text

    def generate_stream(
        self,
        messages: list[str],
        max_tokens: int,
        system_prompt: Optional[str] = None,
        temperature: float = 0.0,
    ) -> Iterable[str]:
        if system_prompt is None:
            system_prompt = ""
        for response in self.client.messages.create(
            model=self.model,
            messages=_get_openai_style_dialog(messages),  # type: ignore
            system=system_prompt,
            max_tokens=max_tokens,
            temperature=temperature,
            stream=True,
        ):  # type: ignore
            # print(response)
            if isinstance(response, ContentBlockDeltaEvent):
                delta = response.delta
                if isinstance(delta, TextDelta):
                    yield delta.text
                else:
                    raise NotImplementedError(
                        f"Delta type {type(delta)} not implemented"
                    )
            # yield response
            # content = response.choices[0].delta.content
            # if content is not None:
            #     if is_first_not_none_response:
            #         is_first_not_none_response = False
            #     yield content

    @property
    def tokenizer(self) -> Any:
        return None

    def get_prompt_token_length(
        self,
        messages: list[str],
        system_prompt: Optional[str] = None,
    ) -> int:
        raise NotImplementedError()

    def get_token_length(self, text: str) -> int:
        raise NotImplementedError()


class DeepSeekCoderV2ChatClient(ChatClient):
    """API client for DeepSeek Coder V2 on llama.cpp.

    This is only for llama.cpp.
    When hosted on vllm, the OpenAIAPIClient should be used instead.
    """

    def __init__(
        self,
        address: str,
        timeout: int = 20,
    ):
        self._address = address
        self.client = LlamaCppClient(address=address, timeout=timeout)

    @property
    def address(self) -> str:
        return self._address

    def _prepare_prompt_text(
        self,
        messages: list[str],
        system_prompt: Optional[str] = None,
    ) -> str:
        """Here's a jinja chat template (tabs added for formatting):

        <｜begin▁of▁sentence｜>{% for message in messages %}{% if message['role'] == 'system' %}{{ message['content'] }}

        {% elif message['role'] == 'user' %}User: {{ message['content'] }}

        {% elif message['role'] == 'assistant' %}Assistant: {{ message['content'] }}<｜end▁of▁sentence｜>{% endif %}{% endfor %}Assistant:
        """
        prompt = "<｜begin▁of▁sentence｜>"
        if system_prompt:
            prompt += system_prompt + "\n\n"
        for i, message in enumerate(messages):
            if i % 2 == 0:
                prompt += "User: " + message + "\n\n"
            else:
                prompt += "Assistant: " + message + "<｜end▁of▁sentence｜>"
        prompt += "Assistant:"  # TODO(guy) maybe a space is needed
        return prompt

    def generate(
        self,
        messages: list[str],
        max_tokens: int,
        system_prompt: Optional[str] = None,
        temperature: float = 0.0,
    ) -> str:
        prompt = self._prepare_prompt_text(messages, system_prompt)

        response = self.client.generate(
            prompt=prompt, max_generated_tokens=max_tokens, temperature=temperature
        )
        return response.content

    def generate_stream(
        self,
        messages: list[str],
        max_tokens: int,
        system_prompt: Optional[str] = None,
    ) -> Iterable[str]:
        prompt = self._prepare_prompt_text(messages, system_prompt)
        for response in self.client.generate_stream(
            prompt=prompt, max_generated_tokens=max_tokens
        ):
            yield response


class GeminiVertexAIChatClient(OpenAIAPIClient):
    """Chat client for Gemini 1.5 Pro.


    Requires authentication with GCP:
    ```bash
    gcloud auth login
    gcloud auth application-default login
    ```
    """

    TOKEN_EXPIRATION_SECONDS = 3600

    def __init__(
        self,
        project_id: str = GCP_PROJECT_ID,
        location: str = GCP_REGION,
        k8s_secret_name: Optional[str] = "research-vertex-ai-sa",
        k8s_secret_key: Optional[str] = "key-json",
        model="google/gemini-1.5-pro-001",
    ):
        self.project_id = project_id
        self.location = location
        self.k8s_secret_name = k8s_secret_name
        self.k8s_secret_key = k8s_secret_key
        self.base_url = f"https://us-central1-aiplatform.googleapis.com/v1beta1/projects/{project_id}/locations/us-central1/endpoints/openapi"
        api_token = self._get_api_token()

        super().__init__(
            base_url=self.base_url,
            api_key=api_token,
            model=model,
        )

    def _get_api_token(self):
        if self.k8s_secret_name and self.k8s_secret_key:
            try:
                service_account_info = json.loads(
                    get_k8s_secret(self.k8s_secret_name, self.k8s_secret_key)
                )
            except ApiException:
                print(
                    f"Failed to get k8s secret {self.k8s_secret_name}. Using default credential."
                )
                gcp_creds, _ = get_gcp_creds(project_id=self.project_id)
            else:
                gcp_creds, _ = get_gcp_creds(service_account_info=service_account_info)
        else:
            gcp_creds, _ = get_gcp_creds(project_id=self.project_id)
        auth_request = transport.requests.Request()  # type: ignore
        gcp_creds.refresh(auth_request)
        if gcp_creds.token is None:
            raise ValueError("Authentication failed")
        self._token_timestamp = time()
        return gcp_creds.token

    def _refresh_client(self):
        if time() - self._token_timestamp > self.TOKEN_EXPIRATION_SECONDS - 60:
            self.update_client(base_url=self.base_url, api_key=self._get_api_token())

    def generate(self, *args, **kwargs) -> str:
        self._refresh_client()
        return super().generate(*args, **kwargs)

    def generate_stream(self, *args, **kwargs) -> Iterable[str]:
        self._refresh_client()
        return super().generate_stream(*args, **kwargs)


class AnthropicVertexAIChatClient(ChatClient):
    """Use Anthropic models via Vertex AI.

    Prerequisites:
    > pip3 install --upgrade --user google-cloud-aiplatform
    > gcloud auth application-default login
    > pip3 install -U 'anthropic[vertex]'
    """

    def __init__(
        self,
        region=GCP_VERTEX_REGION,
        project_id=GCP_PROJECT_ID,
        model="claude-3-5-sonnet@20240620",
    ):
        self.client = anthropic.AnthropicVertex(region=region, project_id=project_id)
        self.model = model

    def _get_create_messages_kwargs(
        self, messages, system_prompt, max_tokens, temperature, stream
    ):
        dialog = _get_openai_style_dialog(messages)

        kwargs = {
            "model": self.model,
            "messages": dialog,
            "temperature": temperature,
            "max_tokens": max_tokens,
        }

        if system_prompt is not None:
            kwargs["system"] = system_prompt

        return kwargs

    def generate(
        self,
        messages: list[str],
        max_tokens: int,
        system_prompt: Optional[str] = None,
        temperature: float = 0.0,
    ) -> str:
        """Generate responses.

        Args:
            messages: A list of messages.
            system_prompt: A system prompt.
            max_tokens: The maximum number of tokens to generate.
            temperature: The temperature.

        Returns:
            A generated response.
        """
        kwargs = self._get_create_messages_kwargs(
            messages, system_prompt, max_tokens, temperature, stream=False
        )
        message = self.client.messages.create(**kwargs)
        return message.content[0].text

    def generate_stream(
        self,
        messages: list[str],
        max_tokens: int,
        system_prompt: Optional[str] = None,
        temperature: float = 0.0,
    ) -> Iterable[str]:
        kwargs = self._get_create_messages_kwargs(
            messages, system_prompt, max_tokens, temperature, stream=False
        )
        with self.client.messages.stream(**kwargs) as stream:
            for text in stream.text_stream:
                yield text

    @property
    def tokenizer(self) -> Any:
        """The tokenizer associated with this model.

        The tokenizer should have a encode(text) method that returns a
        list of integer token IDs, and a decode(tokens) method that
        returns the text string.
        """
        raise NotImplementedError()

    def get_prompt_token_length(
        self,
        messages: list[str],
        system_prompt: Optional[str] = None,
    ) -> int:
        """Returns the number of tokens in the prompt."""
        # TODO(guy) best guess for now
        approx_prompt = "\n".join(messages)
        if system_prompt:
            approx_prompt += system_prompt
        return self.get_token_length(approx_prompt)

    def get_token_length(self, text: str) -> int:
        """Returns the number of tokens in the text."""
        # TODO(guy) best guess for now
        return int(len(text) / 3.5)


class OpenRouterClient(ChatClient):
    """API client for OpenRouter.

    Note: DO NOT SEND AUGMENT REPO TO THIS API. OPENROUTER IS NOT A TRUSTED API.
    """

    def __init__(self, api_key: str, provider_name: str, model_name: str):
        self.provider_name = provider_name
        self.model_name = model_name
        self.client = openai.OpenAI(
            base_url="https://openrouter.ai/api/v1", api_key=api_key
        )

    def generate(
        self,
        messages: list[str],
        max_tokens: int,
        system_prompt: Optional[str] = None,
        temperature: float = 0.0,
        contains_sensitive_information: bool = True,
    ) -> str:
        """Generate a response.

        Args:
            messages: A list of messages.
            system_prompt: A system prompt.
            max_tokens: The maximum number of tokens to generate.
            temperature: The temperature.
            contains_sensitive_information: Whether the response contains sensitive
                information. Must be explicitly set to False.

        Returns:
            A generated response.
        """
        if contains_sensitive_information:
            raise ValueError("OpenRouter does not support sensitive information")

        dialog = _get_openai_style_dialog(
            messages=messages, system_prompt=system_prompt
        )
        response = self.client.chat.completions.create(
            model=self.model_name,
            messages=dialog,  # type: ignore
            temperature=temperature,
            max_tokens=max_tokens,
            extra_body={
                "provider": {"order": [self.provider_name]},
            },
        )
        return response.choices[0].message.content or ""

    def get_token_length(self, text: str) -> int:
        """Returns the number of tokens in the text. Because OpenRouter has models
        with different tokenizers, this is a best guess."""
        return int(len(text) / 3.5)

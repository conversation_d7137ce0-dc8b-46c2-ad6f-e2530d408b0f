"""Tests for the ToolSupportProvider.convert_messages_to_prompt method."""

import json
import pytest
from typing import Dict, List, Any

from research.llm_apis.llm_client import (
    ToolSupportProvider,
    ToolParam,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolFormattedResult,
)


# Create a wrapper for ToolFormattedResult for testing
class MockToolFormattedResult(ToolFormattedResult):
    """A wrapper for ToolFormattedResult for testing."""

    def __init__(self, tool_name: str, formatted_result: str):
        self.tool_name = tool_name
        self.tool_call_id = f"call_{tool_name}_{hash(formatted_result)}"[:32]
        self.tool_output = formatted_result


class TestToolSupportProviderMessages:
    """Tests for the ToolSupportProvider.convert_messages_to_prompt method."""

    def test_convert_messages_to_prompt_simple(self):
        """Test converting simple messages to a prompt."""
        messages = [
            [TextPrompt(text="Hello, how are you?")],
            [TextResult(text="I'm doing well, thank you for asking!")],
            [TextPrompt(text="What's the weather like today?")],
        ]

        prompt = ToolSupportProvider.convert_messages_to_prompt(messages)

        assert "User: Hello, how are you?" in prompt
        assert "Assistant: I'm doing well, thank you for asking!" in prompt
        assert "User: What's the weather like today?" in prompt

    def test_convert_messages_to_prompt_with_tool_calls(self):
        """Test converting messages with tool calls to a prompt."""
        messages = [
            [TextPrompt(text="What's the weather like in San Francisco?")],
            [
                ToolCall(
                    tool_call_id="call_123",
                    tool_name="get_weather",
                    tool_input={"location": "San Francisco"},
                )
            ],
        ]

        prompt = ToolSupportProvider.convert_messages_to_prompt(messages)

        assert "User: What's the weather like in San Francisco?" in prompt
        assert "Assistant: [Tool Call: get_weather]" in prompt
        assert '"location": "San Francisco"' in prompt

    def test_convert_messages_to_prompt_with_tool_results(self):
        """Test converting messages with tool results to a prompt."""
        messages = [
            [TextPrompt(text="What's the weather like in San Francisco?")],
            [
                ToolCall(
                    tool_call_id="call_123",
                    tool_name="get_weather",
                    tool_input={"location": "San Francisco"},
                )
            ],
            [
                MockToolFormattedResult(
                    tool_name="get_weather",
                    formatted_result="The weather in San Francisco is 72°F and sunny.",
                )
            ],
        ]

        prompt = ToolSupportProvider.convert_messages_to_prompt(messages)

        assert "User: What's the weather like in San Francisco?" in prompt
        assert "Assistant: [Tool Call: get_weather]" in prompt
        assert "User: [Tool Result: get_weather]" in prompt
        assert "The weather in San Francisco is 72°F and sunny." in prompt

    def test_convert_messages_to_prompt_complex(self):
        """Test converting complex messages to a prompt."""
        messages = [
            [TextPrompt(text="I need weather information for multiple cities.")],
            [
                TextResult(
                    text="I can help with that. Which cities do you want to know about?"
                )
            ],
            [TextPrompt(text="San Francisco and New York, please.")],
            [
                ToolCall(
                    tool_call_id="call_123",
                    tool_name="get_weather",
                    tool_input={"location": "San Francisco"},
                ),
                TextResult(text="Let me check the weather for both cities."),
            ],
            [
                MockToolFormattedResult(
                    tool_name="get_weather",
                    formatted_result="The weather in San Francisco is 72°F and sunny.",
                )
            ],
            [
                ToolCall(
                    tool_call_id="call_456",
                    tool_name="get_weather",
                    tool_input={"location": "New York"},
                )
            ],
            [
                MockToolFormattedResult(
                    tool_name="get_weather",
                    formatted_result="The weather in New York is 65°F and cloudy.",
                )
            ],
            [
                TextResult(
                    text="Here's the weather information for both cities:\n- San Francisco: 72°F and sunny\n- New York: 65°F and cloudy"
                )
            ],
        ]

        prompt = ToolSupportProvider.convert_messages_to_prompt(messages)

        assert "User: I need weather information for multiple cities." in prompt
        assert (
            "Assistant: I can help with that. Which cities do you want to know about?"
            in prompt
        )
        assert "User: San Francisco and New York, please." in prompt
        assert "Assistant: [Tool Call: get_weather]" in prompt
        assert "Let me check the weather for both cities." in prompt
        assert "User: [Tool Result: get_weather]" in prompt
        assert "The weather in San Francisco is 72°F and sunny." in prompt
        assert "Assistant: [Tool Call: get_weather]" in prompt
        assert "User: [Tool Result: get_weather]" in prompt
        assert "The weather in New York is 65°F and cloudy." in prompt
        assert "Assistant: Here's the weather information for both cities:" in prompt

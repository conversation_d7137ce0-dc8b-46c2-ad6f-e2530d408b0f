"""Utilities for working with production chat with tools data structures."""

import json

from base.prompt_format.common import (
    ChatRequestN<PERSON>,
    ChatRequestNodeType,
    ChatResultNode,
    ChatResultNodeType,
    RequestMessage,
    ResponseMessage,
)
from base.prompt_format_chat.prompt_formatter import (
    StructuredChatPromptOutput,
)
from base.third_party_clients.anthropic_direct_client import get_tool_definition
from base.third_party_clients.third_party_model_client import ToolDefinition
from research.llm_apis.llm_client import (
    AssistantContentBlock,
    GeneralContentBlock,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolFormattedResult,
    ToolParam,
    UserContentBlock,
)


def convert_request_to_user_content_block(
    request: RequestMessage,
    tool_call_id_to_tool_name: dict[str, str],
) -> list[UserContentBlock]:
    """Converts RequestMessage to list[UserContentBlock]."""

    if isinstance(request, str):
        return [TextPrompt(text=request)]

    nodes = []
    for node in request:
        assert isinstance(node, ChatRequestNode)
        if node.type == ChatRequestNodeType.TEXT:
            assert node.text_node is not None
            nodes.append(TextPrompt(text=node.text_node.content))
        elif node.type == ChatRequestNodeType.TOOL_RESULT:
            assert node.tool_result_node is not None
            nodes.append(
                ToolFormattedResult(
                    tool_call_id=node.tool_result_node.tool_use_id,
                    tool_name=tool_call_id_to_tool_name[
                        node.tool_result_node.tool_use_id
                    ],
                    tool_output=node.tool_result_node.content,
                )
            )
        elif node.type == ChatRequestNodeType.IMAGE:
            # skip
            print("We are skipping an image node")
            continue
        elif node.type == ChatRequestNodeType.IDE_STATE:
            # skip
            print("We are skipping an ide state node")
            continue
        elif node.type == ChatRequestNodeType.EDIT_EVENTS:
            # skip
            print("We are skipping an edit events node")
            continue
        else:
            raise ValueError(f"Unsupported node type: {node.type}")
    return nodes


def convert_response_to_assistant_content_block(
    response: ResponseMessage,
) -> tuple[list[AssistantContentBlock], dict[str, str]]:
    """Converts ResponseMessage to list[AssistantContentBlock] and returns a mapping of tool call IDs to tool names."""
    tool_call_id_to_tool_name = {}
    if isinstance(response, str):
        return [TextResult(text=response)], tool_call_id_to_tool_name

    nodes = []
    for node in response:
        assert isinstance(node, ChatResultNode)
        if node.type == ChatResultNodeType.RAW_RESPONSE:
            nodes.append(TextResult(text=node.content))
        elif node.type == ChatResultNodeType.TOOL_USE:
            assert node.tool_use is not None
            nodes.append(
                ToolCall(
                    tool_call_id=node.tool_use.tool_use_id,
                    tool_name=node.tool_use.name,
                    tool_input=node.tool_use.input,
                )
            )
            tool_call_id_to_tool_name[node.tool_use.tool_use_id] = node.tool_use.name
        else:
            raise ValueError(f"Unsupported node type: {node.type}")
    return nodes, tool_call_id_to_tool_name


class ToolDefinitionNotFoundError(Exception):
    def __init__(self, tool_name: str):
        self.tool_name = tool_name
        super().__init__(f"Tool definition not found for {tool_name}")


def fetch_tool_params(tool_name: str, strict: bool = True) -> ToolParam | None:
    try:
        tool_def = get_tool_definition(tool_name)
    except NotImplementedError:
        if strict:
            raise ToolDefinitionNotFoundError(tool_name)
        else:
            return None
    assert "description" in tool_def
    assert "input_schema" in tool_def
    return ToolParam(
        name=tool_def["name"],
        description=tool_def["description"],
        input_schema=dict(tool_def["input_schema"]),
    )


def convert_chat_prompt_output_to_messages(
    prompt_output: StructuredChatPromptOutput, strict: bool = True
) -> tuple[list[list[GeneralContentBlock]], list[ToolParam]]:
    messages = []
    tool_call_id_to_tool_name = {}
    for exchange in prompt_output.chat_history:
        messages.append(
            convert_request_to_user_content_block(
                exchange.request_message, tool_call_id_to_tool_name
            )
        )
        assistant_content_blocks, current_tool_call_id_to_tool_name = (
            convert_response_to_assistant_content_block(exchange.response_text)
        )
        tool_call_id_to_tool_name.update(current_tool_call_id_to_tool_name)
        messages.append(assistant_content_blocks)
    messages.append(
        convert_request_to_user_content_block(
            prompt_output.message, tool_call_id_to_tool_name
        )
    )

    tool_params = []
    for tool_name in set(tool_call_id_to_tool_name.values()):
        tool_param = fetch_tool_params(tool_name, strict=strict)
        if tool_param is not None:
            tool_params.append(tool_param)
    return messages, tool_params


def convert_tool_definitions_to_tool_params(
    tool_definitions: list[ToolDefinition],
) -> list[ToolParam]:
    return [
        ToolParam(
            name=tool_def.name,
            description=tool_def.description,
            input_schema=json.loads(tool_def.input_schema_json),
        )
        for tool_def in tool_definitions
    ]

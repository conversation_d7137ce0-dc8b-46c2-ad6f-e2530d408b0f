"""Tests for prod_adapter_utils."""

import json
import pytest
from unittest.mock import patch, Mock

from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatResultNode,
    ChatResultNodeType,
    ChatRequestText,
    ChatRequestToolResult,
    ChatResultToolUse,
    Exchange,
)
from base.prompt_format_chat.prompt_formatter import (
    StructuredChatPromptOutput,
)
from base.third_party_clients.third_party_model_client import ToolDefinition

from research.llm_apis.llm_client import (
    TextPrompt,
    TextResult,
    ToolCall,
    ToolFormattedResult,
)
from research.llm_apis.prod_adapter_utils import (
    convert_request_to_user_content_block,
    convert_response_to_assistant_content_block,
    fetch_tool_params,
    convert_chat_prompt_output_to_messages,
    convert_tool_definitions_to_tool_params,
    ToolDefinitionNotFoundError,
)


# Test fixtures
@pytest.fixture
def simple_request_message() -> str:
    return "Hello, how can I help you?"


@pytest.fixture
def complex_request_message() -> list[ChatRequestNode]:
    return [
        ChatRequestNode(
            id=1,
            type=ChatRequestNodeType.TEXT,
            text_node=ChatRequestText(content="What is the weather?"),
            tool_result_node=None,
            image_node=None,
        ),
        ChatRequestNode(
            id=2,
            type=ChatRequestNodeType.TOOL_RESULT,
            text_node=None,
            tool_result_node=ChatRequestToolResult(
                tool_use_id="weather_tool_1",
                content="Sunny, 75°F",
                is_error=False,
            ),
            image_node=None,
        ),
    ]


@pytest.fixture
def simple_response_message() -> str:
    return "I can help you with that."


@pytest.fixture
def complex_response_message() -> list[ChatResultNode]:
    return [
        ChatResultNode(
            id=1,
            type=ChatResultNodeType.RAW_RESPONSE,
            content="Let me check the weather.",
            tool_use=None,
        ),
        ChatResultNode(
            id=2,
            type=ChatResultNodeType.TOOL_USE,
            content="",
            tool_use=ChatResultToolUse(
                name="weather",
                input={"location": "San Francisco"},
                tool_use_id="weather_tool_1",
            ),
        ),
    ]


@pytest.fixture
def tool_definition() -> ToolDefinition:
    return ToolDefinition(
        name="weather",
        description="Get the weather for a location",
        input_schema_json=json.dumps(
            {
                "type": "object",
                "properties": {
                    "location": {"type": "string"},
                },
                "required": ["location"],
            }
        ),
    )


# Tests for convert_request_to_user_content_block
def test_convert_simple_request():
    request = "Hello"
    tool_call_id_to_tool_name = {}
    result = convert_request_to_user_content_block(request, tool_call_id_to_tool_name)
    assert len(result) == 1
    assert isinstance(result[0], TextPrompt)
    assert result[0].text == "Hello"


def test_convert_empty_request():
    request = ""
    tool_call_id_to_tool_name = {}
    result = convert_request_to_user_content_block(request, tool_call_id_to_tool_name)
    assert len(result) == 1
    assert isinstance(result[0], TextPrompt)
    assert result[0].text == ""


def test_convert_request_with_error():
    request = [
        ChatRequestNode(
            id=1,
            type=ChatRequestNodeType.TOOL_RESULT,
            text_node=None,
            tool_result_node=ChatRequestToolResult(
                tool_use_id="weather_tool_1",
                content="API Error: Connection failed",
                is_error=True,
            ),
            image_node=None,
        )
    ]
    tool_call_id_to_tool_name = {"weather_tool_1": "weather"}
    result = convert_request_to_user_content_block(request, tool_call_id_to_tool_name)
    assert len(result) == 1
    assert isinstance(result[0], ToolFormattedResult)
    assert result[0].tool_call_id == "weather_tool_1"
    assert result[0].tool_name == "weather"
    assert result[0].tool_output == "API Error: Connection failed"


def test_convert_request_multiple_text_nodes():
    request = [
        ChatRequestNode(
            id=1,
            type=ChatRequestNodeType.TEXT,
            text_node=ChatRequestText(content="First message"),
            tool_result_node=None,
            image_node=None,
        ),
        ChatRequestNode(
            id=2,
            type=ChatRequestNodeType.TEXT,
            text_node=ChatRequestText(content="Second message"),
            tool_result_node=None,
            image_node=None,
        ),
    ]
    tool_call_id_to_tool_name = {}
    result = convert_request_to_user_content_block(request, tool_call_id_to_tool_name)
    assert len(result) == 2
    assert isinstance(result[0], TextPrompt)
    assert isinstance(result[1], TextPrompt)
    assert result[0].text == "First message"
    assert result[1].text == "Second message"


def test_convert_complex_request(complex_request_message):
    tool_call_id_to_tool_name = {"weather_tool_1": "weather"}
    result = convert_request_to_user_content_block(
        complex_request_message, tool_call_id_to_tool_name
    )
    assert len(result) == 2
    assert isinstance(result[0], TextPrompt)
    assert isinstance(result[1], ToolFormattedResult)
    assert result[0].text == "What is the weather?"
    assert result[1].tool_call_id == "weather_tool_1"
    assert result[1].tool_name == "weather"
    assert result[1].tool_output == "Sunny, 75°F"


# Tests for convert_response_to_assistant_content_block
def test_convert_simple_response():
    response = "I can help"
    result, tool_map = convert_response_to_assistant_content_block(response)
    assert len(result) == 1
    assert isinstance(result[0], TextResult)
    assert result[0].text == "I can help"
    assert not tool_map


def test_convert_empty_response():
    response = ""
    result, tool_map = convert_response_to_assistant_content_block(response)
    assert len(result) == 1
    assert isinstance(result[0], TextResult)
    assert result[0].text == ""
    assert not tool_map


def test_convert_response_with_error_tool():
    response = [
        ChatResultNode(
            id=1,
            type=ChatResultNodeType.TOOL_USE,
            content="",
            tool_use=ChatResultToolUse(
                name="weather",
                input={"location": "Invalid Location"},
                tool_use_id="weather_tool_1",
            ),
        ),
        ChatResultNode(
            id=2,
            type=ChatResultNodeType.RAW_RESPONSE,
            content="Sorry, I couldn't get the weather information.",
            tool_use=None,
        ),
    ]
    result, tool_map = convert_response_to_assistant_content_block(response)
    assert len(result) == 2
    assert isinstance(result[0], ToolCall)
    assert isinstance(result[1], TextResult)
    assert result[0].tool_call_id == "weather_tool_1"
    assert result[0].tool_name == "weather"
    assert result[1].text == "Sorry, I couldn't get the weather information."
    assert tool_map["weather_tool_1"] == "weather"


def test_convert_response_multiple_tool_uses():
    response = [
        ChatResultNode(
            id=1,
            type=ChatResultNodeType.TOOL_USE,
            content="",
            tool_use=ChatResultToolUse(
                name="weather",
                input={"location": "New York"},
                tool_use_id="weather_tool_1",
            ),
        ),
        ChatResultNode(
            id=2,
            type=ChatResultNodeType.TOOL_USE,
            content="",
            tool_use=ChatResultToolUse(
                name="time",
                input={"timezone": "EST"},
                tool_use_id="time_tool_1",
            ),
        ),
    ]
    result, tool_map = convert_response_to_assistant_content_block(response)
    assert len(result) == 2
    assert isinstance(result[0], ToolCall)
    assert isinstance(result[1], ToolCall)
    assert result[0].tool_call_id == "weather_tool_1"
    assert result[0].tool_name == "weather"
    assert result[1].tool_call_id == "time_tool_1"
    assert result[1].tool_name == "time"
    assert tool_map["weather_tool_1"] == "weather"
    assert tool_map["time_tool_1"] == "time"


def test_convert_complex_response(complex_response_message):
    result, tool_map = convert_response_to_assistant_content_block(
        complex_response_message
    )
    assert len(result) == 2
    assert isinstance(result[0], TextResult)
    assert isinstance(result[1], ToolCall)
    assert result[0].text == "Let me check the weather."
    assert result[1].tool_call_id == "weather_tool_1"
    assert result[1].tool_name == "weather"
    assert tool_map["weather_tool_1"] == "weather"


# Tests for fetch_tool_params
@patch("research.llm_apis.prod_adapter_utils.get_tool_definition")
def test_fetch_tool_params_success(mock_get_tool_def):
    mock_get_tool_def.return_value = {
        "name": "test_tool",
        "description": "A test tool",
        "input_schema": {"type": "object"},
    }
    result = fetch_tool_params("test_tool")
    assert result is not None
    assert result.name == "test_tool"
    assert result.description == "A test tool"
    assert result.input_schema == {"type": "object"}


@patch("research.llm_apis.prod_adapter_utils.get_tool_definition")
def test_fetch_tool_params_not_found(mock_get_tool_def):
    mock_get_tool_def.side_effect = NotImplementedError()
    with pytest.raises(ToolDefinitionNotFoundError):
        fetch_tool_params("nonexistent_tool")


# Tests for convert_chat_prompt_output_to_messages
@patch("research.llm_apis.prod_adapter_utils.get_tool_definition")
def test_convert_chat_prompt_output(
    mock_get_tool_def, simple_request_message, simple_response_message
):
    prompt_output = StructuredChatPromptOutput(
        system_prompt=None,  # No system prompt needed for this test
        chat_history=[
            Exchange(
                request_message=simple_request_message,
                response_text=simple_response_message,
                request_id=None,
            )
        ],
        message="What's next?",
        retrieved_chunks_in_prompt=[],  # No retrieved chunks needed for this test
    )

    messages, tool_params = convert_chat_prompt_output_to_messages(prompt_output)
    assert len(messages) == 3  # Request, response, final message
    assert isinstance(messages[0][0], TextPrompt)
    assert isinstance(messages[1][0], TextResult)
    assert isinstance(messages[2][0], TextPrompt)
    assert messages[0][0].text == simple_request_message
    assert messages[1][0].text == simple_response_message
    assert messages[2][0].text == "What's next?"
    assert not tool_params


@patch("research.llm_apis.prod_adapter_utils.get_tool_definition")
def test_convert_chat_prompt_output_empty_history(mock_get_tool_def):
    prompt_output = StructuredChatPromptOutput(
        system_prompt=None,
        chat_history=[],
        message="First message",
        retrieved_chunks_in_prompt=[],
    )

    messages, tool_params = convert_chat_prompt_output_to_messages(prompt_output)
    assert len(messages) == 1  # Just the final message
    assert isinstance(messages[0][0], TextPrompt)
    assert messages[0][0].text == "First message"
    assert not tool_params


@patch("research.llm_apis.prod_adapter_utils.get_tool_definition")
def test_convert_chat_prompt_output_with_system_prompt(mock_get_tool_def):
    prompt_output = StructuredChatPromptOutput(
        system_prompt="You are a helpful assistant.",
        chat_history=[],
        message="Hello",
        retrieved_chunks_in_prompt=[],
    )

    messages, tool_params = convert_chat_prompt_output_to_messages(prompt_output)
    assert len(messages) == 1  # Just the final message
    assert isinstance(messages[0][0], TextPrompt)
    assert messages[0][0].text == "Hello"
    assert not tool_params
    # Note: system prompt is handled separately in the API call


@patch("research.llm_apis.prod_adapter_utils.get_tool_definition")
def test_convert_chat_prompt_output_with_tools(mock_get_tool_def):
    mock_get_tool_def.return_value = {
        "name": "weather",
        "description": "Get weather info",
        "input_schema": {"type": "object"},
    }

    prompt_output = StructuredChatPromptOutput(
        system_prompt=None,
        chat_history=[
            Exchange(
                request_message=[
                    ChatRequestNode(
                        id=1,
                        type=ChatRequestNodeType.TEXT,
                        text_node=ChatRequestText(content="Check weather"),
                        tool_result_node=None,
                        image_node=None,
                    )
                ],
                response_text=[
                    ChatResultNode(
                        id=1,
                        type=ChatResultNodeType.TOOL_USE,
                        content="",
                        tool_use=ChatResultToolUse(
                            name="weather",
                            input={"location": "London"},
                            tool_use_id="weather_1",
                        ),
                    )
                ],
                request_id=None,
            )
        ],
        message="What's the weather like?",
        retrieved_chunks_in_prompt=[],
        tools=None,  # tools parameter only accepts "replace_text" or None
    )

    messages, tool_params = convert_chat_prompt_output_to_messages(prompt_output)
    assert len(messages) == 3  # Request, tool use, final message
    assert len(tool_params) == 1
    assert tool_params[0].name == "weather"
    assert tool_params[0].description == "Get weather info"
    assert tool_params[0].input_schema == {"type": "object"}


# Tests for convert_tool_definitions_to_tool_params
def test_convert_tool_definitions(tool_definition):
    tool_defs = [tool_definition]
    result = convert_tool_definitions_to_tool_params(tool_defs)
    assert len(result) == 1
    assert result[0].name == "weather"
    assert result[0].description == "Get the weather for a location"
    assert result[0].input_schema["type"] == "object"
    assert "location" in result[0].input_schema["properties"]

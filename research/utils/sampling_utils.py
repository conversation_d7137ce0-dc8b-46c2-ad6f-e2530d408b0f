import random
from typing import Sequence, TypeVar

AT = TypeVar("AT")


def downsample_to(rng: random.Random, xs: Sequence[AT], n: int) -> list[AT]:
    """Downsample a list to length n without changing the element order."""
    if n == len(xs):
        return list(xs)
    ids = list(range(len(xs)))
    rng.shuffle(ids)
    to_keep = set(ids[:n])
    return [x for i, x in enumerate(xs) if i in to_keep]

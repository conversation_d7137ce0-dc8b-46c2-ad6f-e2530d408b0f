"""
This is a utility file for calculating the diagnostic metrics for the next edit hindsight location eval task.
This is for offline analysis.
Some plots of these metrics can be found in the notebook can be found on Notion under `Evaluating Location Model Performance with Diagnostic-Enhanced Retrieval`
"""

from collections import defaultdict
from typing import Counter, Sequence

from base.retrieval.chunking.smart_chunking import <PERSON>Chunker
from research.core.next_edit_location_prompt_input import Diagnostic

from research.core.next_edit_location_prompt_input import FileLocation
from research.core.types import Document, NTuple, <PERSON>r<PERSON><PERSON><PERSON>
from research.eval.harness.tasks.next_edit_hindsight_location_eval_task import (
    NextEditHindsightLocationDiagnosticsOutput,
    NextEditHindsightTaskInput,
    any_touch_ground_truth,
)


# def calculate_diagnostic_metrics(output_path: Path):
def calculate_diagnostic_metrics(
    output: Sequence[NextEditHindsightLocationDiagnosticsOutput],
    top_ks: Sequence[int],
    num_swaps: Sequence[int],
):
    """Calculate the metrics for the output of the next edit hindsight location eval task."""

    current_file: dict[int, dict[int, list[int]]] = defaultdict(
        lambda: defaultdict(list)
    )
    non_current_file: dict[int, dict[int, list[int]]] = defaultdict(
        lambda: defaultdict(list)
    )
    helpful_diagnostic: list[Diagnostic] = []
    covered_diagnostic_messages: Counter[str] = Counter()
    covered_diagnostic_types: Counter[str] = Counter()
    total_diagnostic_types: Counter[str] = Counter()
    helpful_diagnostic_current_file: list[Diagnostic] = []
    helpful_diagnostic_non_current_file: list[Diagnostic] = []

    for datum in output:
        # most recent changes are first
        diagnostic_file_locations: Sequence[FileLocation] = [
            FileLocation(
                path=diag.location.path[1:],
                range=diag.location.lrange,
            )
            for diag in datum.diagnostics
        ]

        # sorted
        scored_locations = sorted(
            datum.scored_candidates, key=lambda x: x.score, reverse=True
        )

        # track what types
        for diagnostic in datum.diagnostics:
            total_diagnostic_types[diagnostic.severity] += 1
            loc = FileLocation(
                path=diagnostic.location.path[1:],
                range=diagnostic.location.lrange,
            )
            if any_touch_ground_truth([loc], datum.ground_truth):
                covered_diagnostic_messages[diagnostic.message] += 1
                covered_diagnostic_types[diagnostic.severity] += 1

        if type(scored_locations[0].item) == dict:
            file_locations = [FileLocation.from_dict(x.item) for x in scored_locations]  # type: ignore
        else:
            file_locations = [x.item for x in scored_locations]

        found_helpful_diagnostic: bool = False
        for k_val in top_ks:
            # get top k locations only ignore the score
            top_k_locations = file_locations[:k_val]
            last_covers = False
            for num_swap in num_swaps:
                if num_swap > len(top_k_locations) or num_swap > len(
                    diagnostic_file_locations
                ):
                    continue

                slice_index = k_val - num_swap
                swapped_locations = (
                    top_k_locations[:slice_index] + diagnostic_file_locations[:num_swap]
                )

                covers: bool = any_touch_ground_truth(
                    swapped_locations, datum.ground_truth
                )
                if datum.cursor_path == datum.ground_truth.path:
                    current_file[k_val][num_swap].append(covers)
                else:
                    non_current_file[k_val][num_swap].append(covers)

                if (
                    num_swap != 0
                    and covers
                    and not last_covers
                    and not found_helpful_diagnostic
                ):
                    found_helpful_diagnostic = True
                    helpful_diagnostic.append(datum.diagnostics[num_swap - 1])
                    if datum.cursor_path == datum.ground_truth.path:
                        helpful_diagnostic_current_file.append(
                            datum.diagnostics[num_swap - 1]
                        )
                    else:
                        helpful_diagnostic_non_current_file.append(
                            datum.diagnostics[num_swap - 1]
                        )
                last_covers = covers

    # count different helpful message types
    helpful_message_types: Counter[str] = Counter(
        [diag.message for diag in helpful_diagnostic]
    )

    helpful_messages: Counter[str] = Counter(
        [diag.message for diag in helpful_diagnostic]
    )

    current_file_recall_metrics = {
        k_val: {num_swap: sum(v) / len(v) for num_swap, v in k_val_dict.items()}
        for k_val, k_val_dict in current_file.items()
    }
    non_current_file_recall_metrics = {
        k_val: {num_swap: sum(v) / len(v) for num_swap, v in k_val_dict.items()}
        for k_val, k_val_dict in non_current_file.items()
    }
    # return the recall metrics
    data = {
        "current_file_recall": current_file_recall_metrics,
        "non_current_file_recall": non_current_file_recall_metrics,
        "num_swaps": num_swaps,
        "top_ks": top_ks,
        "num_samples": len(output),
        "num_samples_current_file": sum(
            1 for datum in output if datum.cursor_path == datum.ground_truth.path
        ),
        "num_samples_non_current_file": sum(
            1 for datum in output if datum.cursor_path != datum.ground_truth.path
        ),
        "num_samples_with_diagnostic": sum(
            1 for datum in output if len(datum.diagnostics) > 0
        ),
        "num_samples_with_diagnostic_current_file": sum(
            1
            for datum in output
            if len(datum.diagnostics) > 0
            and datum.cursor_path == datum.ground_truth.path
        ),
        "num_samples_with_diagnostic_non_current_file": sum(
            1
            for datum in output
            if len(datum.diagnostics) > 0
            and datum.cursor_path != datum.ground_truth.path
        ),
        "helpful_message_types": {
            message_type: count for message_type, count in helpful_message_types.items()
        },
        "helpful_messages": {
            message: count for message, count in helpful_messages.items()
        },
        "total_helpful_messages": len(helpful_diagnostic),
        "total_helpful_messages_current_file": len(helpful_diagnostic_current_file),
        "total_helpful_messages_non_current_file": len(
            helpful_diagnostic_non_current_file
        ),
        "covered_diagnostic_messages": {
            message: count for message, count in covered_diagnostic_messages.items()
        },
        "covered_diagnostic_types": {
            type_: count for type_, count in covered_diagnostic_types.items()
        },
        "total_diagnostics_types": {
            type_: count for type_, count in total_diagnostic_types.items()
        },
    }

    data["proportion_helpful_messages"] = (
        data["total_helpful_messages"] / data["num_samples_with_diagnostic"]
    )
    data["proportion_helpful_messages_current_file"] = (
        data["total_helpful_messages_current_file"]
        / data["num_samples_with_diagnostic_current_file"]
    )
    data["proportion_helpful_messages_non_current_file"] = (
        data["total_helpful_messages_non_current_file"]
        / data["num_samples_with_diagnostic_non_current_file"]
    )
    return data


# def calculate_diagnostic_metrics(output_path: Path):
def calculate_diagnostic_metrics_at_most_l_swaps(
    output: Sequence[NextEditHindsightLocationDiagnosticsOutput],
    task_input: list[NextEditHindsightTaskInput],
    top_ks: Sequence[int],
    num_swaps: Sequence[int],
    ignore_around_cursor: bool = False,
):
    """Calculate the metrics for the output of the next edit hindsight location eval task."""
    # The data was not sorted in the same way, so we need to zip them together.
    all_data = _return_zipped(task_input, output)

    current_file_at_most_l_swaps: dict[int, dict[int, list[int]]] = defaultdict(
        lambda: defaultdict(list)
    )
    non_current_file_at_most_l_swaps: dict[int, dict[int, list[int]]] = defaultdict(
        lambda: defaultdict(list)
    )

    covered_diagnostic_messages: Counter[str] = Counter()
    covered_diagnostic_types: Counter[str] = Counter()
    total_diagnostic_types: Counter[str] = Counter()

    chunker = SmartChunker(max_chunk_chars=2000, max_headers=0)
    # count of ground truth locations that are touched by the cursor
    ignored_count = 0

    for input, datum in all_data:
        if ignore_around_cursor and (
            datum.cursor_path == datum.ground_truth.path
            and _check_smart_chunk_overlap(
                chunker,
                next(
                    (doc for doc in input.documents if doc.path == datum.cursor_path), None
                ),
                datum.ground_truth.before_crange,
                input.prompt.edit_region,
            )
        ):
            ignored_count += 1
            continue

        # get all chunks for the diagnostics
        diagnostic_chunk_locations = _get_all_chunks_for_diagnostics(
            datum.diagnostics, input.documents, chunker
        )

        for chunk, diagnostic in diagnostic_chunk_locations:
            total_diagnostic_types[diagnostic.severity] += 1
            if any_touch_ground_truth([chunk], datum.ground_truth):
                covered_diagnostic_messages[diagnostic.message] += 1
                covered_diagnostic_types[diagnostic.severity] += 1

        # sorted
        scored_locations = sorted(
            datum.scored_candidates, key=lambda x: x.score, reverse=True
        )

        if scored_locations and type(scored_locations[0].item) == dict:
            file_locations = [FileLocation.from_dict(x.item) for x in scored_locations]  # type: ignore
        else:
            file_locations = [x.item for x in scored_locations]

        # keep only those chunks that do not overlap with the scored locations
        unique_diagnostic_chunk_locations = [
            loc
            for loc, _ in diagnostic_chunk_locations
            if not any(
                file_location.path == loc.path
                and file_location.range.touches(loc.range)
                for file_location in file_locations
            )
        ]

        if scored_locations and type(scored_locations[0].item) == dict:
            file_locations = [FileLocation.from_dict(x.item) for x in scored_locations]  # type: ignore
        else:
            file_locations = [x.item for x in scored_locations]

        for k_val in top_ks:
            # get top k locations only ignore the score
            top_k_locations = file_locations[:k_val]
            for l in num_swaps:  # noqa: E741
                if l > k_val:
                    # we can't swap more than the number of top-k locations
                    continue

                num_swap = min(l, len(unique_diagnostic_chunk_locations))
                slice_index = k_val - num_swap
                swapped_locations = (
                    top_k_locations[:slice_index]
                    + unique_diagnostic_chunk_locations[:num_swap]
                )

                covers: bool = any_touch_ground_truth(
                    swapped_locations, datum.ground_truth
                )
                if datum.cursor_path == datum.ground_truth.path:
                    current_file_at_most_l_swaps[k_val][l].append(covers)
                else:
                    non_current_file_at_most_l_swaps[k_val][l].append(covers)

    current_file_recall_metrics = {
        k_val: {num_swap: sum(v) / len(v) for num_swap, v in k_val_dict.items()}
        for k_val, k_val_dict in current_file_at_most_l_swaps.items()
    }
    non_current_file_recall_metrics = {
        k_val: {num_swap: sum(v) / len(v) for num_swap, v in k_val_dict.items()}
        for k_val, k_val_dict in non_current_file_at_most_l_swaps.items()
    }
    # return the recall metrics
    data = {
        "current_file_recall": current_file_recall_metrics,
        "non_current_file_recall": non_current_file_recall_metrics,
        "num_swaps": num_swaps,
        "top_ks": top_ks,
        "num_samples": len(output),
        "num_samples_current_file": sum(
            1 for datum in output if datum.cursor_path == datum.ground_truth.path
        ),
        "num_samples_non_current_file": sum(
            1 for datum in output if datum.cursor_path != datum.ground_truth.path
        ),
        "covered_diagnostic_messages": {
            message: count for message, count in covered_diagnostic_messages.items()
        },
        "covered_diagnostic_types": {
            type_: count for type_, count in covered_diagnostic_types.items()
        },
        "total_diagnostics_types": {
            type_: count for type_, count in total_diagnostic_types.items()
        },
        "ignore_around_cursor": ignored_count,
    }
    return data


def _get_all_chunks_for_diagnostics(
    diagnostics: NTuple[Diagnostic],
    documents: list[Document],
    chunker: SmartChunker,
) -> list[tuple[FileLocation, Diagnostic]]:
    """Get all chunks for the diagnostics, sorted by cursor distance."""
    all_chunks = []
    for diagnostic in diagnostics:
        all_chunks.extend(_get_chunks_for_diagnostic(diagnostic, documents, chunker))

    # remove overlapping chunks
    # return _remove_overlapping_chunks(all_chunks)
    return all_chunks


def _return_zipped(
    task_input: list[NextEditHindsightTaskInput],
    output: Sequence[NextEditHindsightLocationDiagnosticsOutput],
) -> list[
    tuple[NextEditHindsightTaskInput, NextEditHindsightLocationDiagnosticsOutput]
]:
    """Data was not sorted in the same way, so we need to zip them together. This is a hacky way to do it."""
    zipped_data = []
    for input in task_input:
        for datum in output:
            if (
                input.ground_truth.path == datum.ground_truth.path
                and input.ground_truth.before_lrange == datum.ground_truth.before_lrange
                and input.ground_truth.after_lrange == datum.ground_truth.after_lrange
                and input.ground_truth.before_text == datum.ground_truth.before_text
                and input.ground_truth.after_text == datum.ground_truth.after_text
                and len(input.prompt.diagnostics) == len(datum.diagnostics)
                and (
                    len(input.prompt.diagnostics) == 0
                    or all(
                        [
                            input.prompt.diagnostics[i] == datum.diagnostics[i]
                            for i in range(len(input.prompt.diagnostics))
                        ]
                    )
                )
            ):
                zipped_data.append((input, datum))
    return zipped_data


def _get_chunks_for_diagnostic(
    diagnostic: Diagnostic,
    documents: list[Document],
    chunker: SmartChunker,
) -> list[tuple[FileLocation, Diagnostic]]:
    """Get chunks for the diagnostic file, sorted by cursor distance."""

    # get the correct doc
    doc = next(
        (doc for doc in documents if doc.path == diagnostic.location.path[1:]), None
    )
    if doc is None:
        print(f"Diagnostic file not found: {diagnostic.location.path}")
        return []
    # get the chunks for the diagnostic file
    chunks = chunker.split_chunks(doc.text, None)

    relevant_chunks = []
    for chunk in chunks:
        if chunk.lrange().touches(diagnostic.location.lrange):
            relevant_chunks.append((FileLocation(doc.path, chunk.lrange()), diagnostic))
    return relevant_chunks

def _check_smart_chunk_overlap(
    chunker: SmartChunker,
    doc: Document | None,
    range1: CharRange,
    range2: CharRange,
) -> bool:
    if doc is None:
        return False
    chunks = chunker.split_chunks(doc.text, None)
    for chunk in chunks:
        if chunk.crange().touches(range1) and chunk.crange().touches(range2):
            return True
    return False
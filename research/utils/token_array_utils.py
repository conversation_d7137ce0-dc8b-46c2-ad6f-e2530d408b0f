"""Utils for working with token sequences using numpy arrays."""

import logging
from pathlib import Path
from random import Random
from typing import Iterable, Mapping, Sequence, TypeVar, cast

import numpy as np
from megatron.data import indexed_dataset
from tqdm import tqdm

from base.tokenizers.tokenizer import Tokenizer
from research.static_analysis.common import assert_eq

TokenArray = np.ndarray
"""A numpy array containing token ids."""


def to_token_array(
    tokens: Sequence[int], vocab_size: int, nonnegative: bool = True
) -> TokenArray:
    if nonnegative:
        # make sure that all tokens are actually non-negative
        assert (np.array(tokens, dtype=np.int32) >= 0).all(), tokens
    if not nonnegative or vocab_size >= 2**16:
        int_type = np.int32
    else:
        int_type = np.uint16
    return np.array(tokens, dtype=int_type)


def pad_sequences(
    examples: Iterable[TokenArray],
    seq_len: int,
    pad_id: int,
) -> Iterable[TokenArray]:
    """Pad sequences to the given sequence length."""
    for ex in examples:
        if len(ex) >= seq_len:
            raise ValueError(f"{len(ex)=} is larger than {seq_len}.")
        else:
            yield np.pad(ex, (0, seq_len - len(ex)), constant_values=pad_id)


def make_indexed_dataset(
    out_path: Path,
    token_seqs: Iterable[TokenArray],
) -> None:
    """Turn a sequence of token sequences into an mmap indexed dataset."""
    out_path.parent.mkdir(parents=True, exist_ok=True)
    dtype = np.int32
    # look at the first item to figure out the data type
    first_item = next(iter(token_seqs), None)
    if first_item is not None:
        dtype = first_item.dtype.type
    logging.info(f"Building the indexed dataset using dtype={dtype}")
    builder = indexed_dataset.MMapIndexedDatasetBuilder(
        str(out_path.with_suffix(".bin")), dtype=dtype
    )
    if first_item is not None:
        builder.add_item(first_item)
    for tk_array in token_seqs:
        builder.add_item(tk_array)
    builder.finalize(out_path.with_suffix(".idx"))


def shuffle_pad_dataset(
    data: Sequence[TokenArray],
    dataset_name: Path,
    seq_len: int,
    pad_id: int,
    rng: Random,
) -> None:
    """Shuffle and pad sequences into an indexed dataset and save it."""
    ids = list(range(len(data)))
    rng.shuffle(ids)
    shuffled = tqdm(
        (data[i] for i in ids),
        smoothing=0,
        total=len(data),
        desc="shuffle_pack_dataset",
    )
    padded = pad_sequences(shuffled, seq_len=seq_len, pad_id=pad_id)
    make_indexed_dataset(dataset_name, padded)


def pack_sequences(
    examples: Iterable[TokenArray],
    seq_len: int,
    pad_id: int,
) -> Iterable[TokenArray]:
    """Pack shorter examples into longer ones.

    This function tries to pack as many examples into a single sequence as possible.
    However, an example will never be split. When no more examples can be packed, the
    remaining space will be filled with `pad_id`. We also insert a `pad_id` between
    adjacent examples to be safe.
    """
    current_batch: list[TokenArray] = []
    remaining = seq_len
    for ex in examples:
        assert len(ex) <= seq_len, f"{len(ex)=} is larger than {seq_len}."
        if len(ex) + 1 > remaining:
            padding = np.array([pad_id] * remaining, dtype=ex.dtype)
            current_batch.append(padding)
            yield np.concatenate(current_batch)
            current_batch = []
            remaining = seq_len
        current_batch.append(ex)
        current_batch.append(np.array([pad_id], dtype=ex.dtype))
        remaining -= len(ex) + 1
    if current_batch:
        padding = np.array([pad_id] * remaining, dtype=current_batch[0].dtype)
        current_batch.append(padding)
        yield np.concatenate(current_batch)


def shuffle_pack_dataset(
    data: Sequence[TokenArray],
    dataset_name: Path,
    seq_len: int,
    pad_id: int,
    rng: Random,
) -> None:
    """Shuffle and pack sequences into an indexed dataset and save it."""
    ids = list(range(len(data)))
    rng.shuffle(ids)
    shuffled = tqdm(
        (data[i] for i in ids),
        smoothing=0,
        total=len(data),
        desc="shuffle_pack_dataset",
    )
    packed = pack_sequences(shuffled, seq_len=seq_len, pad_id=pad_id)
    make_indexed_dataset(dataset_name, packed)


def split_shuffle_pack_dataset(
    data: Sequence[Sequence],
    out_dir: Path,
    split_sizes: dict[str, float],
    seq_len: int,
    tokenizer: Tokenizer,
    rng: Random | int = 42,
):
    if isinstance(rng, int):
        rng = Random(rng)

    N = len(data)
    ex_ids = list(range(N))
    rng.shuffle(ex_ids)
    weight_sum = sum(split_sizes.values())
    split_sizes = {k: x / weight_sum for k, x in split_sizes.items()}
    split_start = 0

    for name, size_ratio in split_sizes.items():
        split_stop = split_start + int(N * size_ratio)
        split_ids = ex_ids[split_start:split_stop]
        assert split_ids, f"Empty split: {name}. {split_sizes=}, {N=}"
        print(f"Processing split: {name} (size={len(split_ids)})")
        split_examples = tqdm(
            (cast(TokenArray, data[i]) for i in split_ids),
            smoothing=0,
            total=len(split_ids),
            desc=f"Process: {name}",
        )
        packed = pack_sequences(
            split_examples, seq_len=seq_len, pad_id=tokenizer.special_tokens.padding
        )
        make_indexed_dataset(out_dir / name, packed)
        split_start = split_stop


AT = TypeVar("AT")


def split_by_ratios(
    data: Sequence[AT],
    split_ratios: Mapping[str, float],
    rng: Random,
) -> dict[str, list[AT]]:
    """Randomly split a sequence into multiple ones based on the ratios."""
    N = len(data)
    ex_ids = list(range(N))
    rng.shuffle(ex_ids)
    weight_sum = sum(split_ratios.values())
    if weight_sum <= 0:
        raise ValueError(f"{weight_sum=}")

    split_sizes = {k: int(N * x / weight_sum) for k, x in split_ratios.items()}
    leftover = N - sum(split_sizes.values())
    last_split = tuple(split_sizes.keys())[-1]
    split_sizes[last_split] += leftover

    split_start = 0
    result = dict[str, list[AT]]()

    for name, split_size in split_sizes.items():
        split_stop = split_start + split_size
        split_ids = ex_ids[split_start:split_stop]
        result[name] = [data[i] for i in split_ids]
        split_start = split_stop

    assert_eq(sum(len(x) for x in result.values()), N)
    return result


def load_indexed_dataset(data_path: "Path | str", impl: str = "mmap"):
    """Load an indexed dataset from disk."""
    dataset = indexed_dataset.make_dataset(str(data_path), impl, skip_warmup=True)
    assert dataset, f"Failed to load indexed dataset from {data_path}."
    return dataset

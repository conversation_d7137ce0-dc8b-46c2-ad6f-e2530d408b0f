"""Utilities to traverse Git repo histories.

The `RepoChange` dataclass is used to represent a repo change (such as a commit)
as two snapshots of the repo. The `iterate_repo_history` function iterates
through a specified span of commits and yields `RepoChange` objects.
"""

from __future__ import annotations

import logging
import subprocess
import time
from dataclasses import dataclass
from pathlib import Path
import tempfile
from typing import Callable, Iterable, Mapping, NamedTuple, Sequence, TypeVar

from dataclasses_json import DataClassJsonMixin
from pyrsistent import PMap as PyrMap
from pyrsistent import PVector as PyrVector
from pyrsistent import pmap as pyr_map
from pyrsistent import pvector as pyr_vector
from typing_extensions import assert_never
from unidiff import PatchedFile, PatchSet
from unidiff.constants import DEV_NULL

from base.languages.language_guesser import guess_language
from base.static_analysis.common import (
    assert_eq,
    assert_str_eq,
    shorten_str,
    show_str_diff,
)
from research.core.changes import (
    Added,
    Changed,
    Deleted,
    Modified,
    get_after,
    get_before,
)
from research.core.diff_utils import (
    File,
    Repository,
    _get_sub_repos_for_diff,
    apply_diff,
    compute_repo_diff,
)
from research.utils.process_map import pmap as process_map


class FileTuple(NamedTuple):
    """A file path and its content."""

    path: Path
    code: str

    @staticmethod
    def from_file(file: File) -> FileTuple:
        return FileTuple(Path(file.path), file.contents)

    def to_file(self) -> File:
        return File(str(self.path), self.code)


class GitOperationError(Exception):
    """Exception raised when a Git operation fails."""


@dataclass(frozen=True)
class RepoChange:
    """A repo level change (such as a commit) stored as two snapshots.

    Note that we use the immutable PyrMap and PyrSet (instead of dict and set) to
    enable efficient updates without making full copies of the snapshots.
    """

    before_files: PyrMap[Path, str]
    """Maps (relative) path of each file to its content before the change."""

    after_files: PyrMap[Path, str]
    """Maps (relative) path of each file to its content after the change."""

    changed_files: PyrVector[Changed[FileTuple]]
    """Records the list of changed files, sorted from oldest to newest.

    See also: `show_changed_files` to print the set of changed paths in a Git style.
    """

    @staticmethod
    def build(
        before_files: Mapping[Path, str],
        after_files: Mapping[Path, str],
        changed_files: Iterable[Changed[FileTuple]],
    ):
        """A convenience factory method to build a RepoChange."""
        return RepoChange(
            pyr_map(before_files), pyr_map(after_files), pyr_vector(changed_files)
        )

    @staticmethod
    def from_before_and_changes(
        before_files: Mapping[Path, str], changes: Iterable[Changed[FileTuple]]
    ) -> RepoChange:
        """Build a repo change by applying a series of file changes to before files."""
        if not isinstance(before_files, PyrMap):
            before_files = pyr_map(before_files)
        if not isinstance(changes, PyrVector):
            changes = pyr_vector(changes)
        after_files = before_files
        for change in changes:
            if before_file := get_before(change):
                after_files = after_files.discard(before_file.path)
            if after_file := get_after(change):
                after_files = after_files.set(after_file.path, after_file.code)
        return RepoChange(before_files, after_files, changes)

    @classmethod
    def from_file_changes(cls, file_changes: Iterable[Changed[File]]) -> RepoChange:
        """Build a repo change from a series of file changes.

        Only the files mentioned in the file changes will be included in the result.
        """
        file_tuple_changes = [
            change.map(FileTuple.from_file) for change in file_changes
        ]
        before_files = dict[Path, str]()
        after_files = dict[Path, str]()
        for change in file_tuple_changes:
            if before_file := get_before(change):
                before_files[before_file.path] = before_file.code
            if after_file := get_after(change):
                after_files[after_file.path] = after_file.code
        return RepoChange(
            pyr_map(before_files), pyr_map(after_files), pyr_vector(file_tuple_changes)
        )

    @staticmethod
    def no_change(files: Mapping[Path, str]) -> RepoChange:
        """A convenience factory method to build a RepoChange with no changes."""
        files = pyr_map(files)
        return RepoChange(files, files, pyr_vector())

    def verify(self) -> None:
        """Check if applying the changes to the before files yields the after files."""
        applied = RepoChange.from_before_and_changes(
            self.before_files, self.changed_files
        ).after_files
        if applied != self.after_files:
            extra_files = set(self.after_files) - set(applied)
            missing_files = set(applied) - set(self.after_files)
            common_files = set(self.after_files) & set(applied)
            different_files = {
                path for path in common_files if self.after_files[path] != applied[path]
            }
            raise AssertionError(
                "Verification failed:\n"
                f"Extra files: {extra_files}\n"
                f"Missing files: {missing_files}\n"
                f"Different files: {different_files}"
            )

    def changed_paths(self) -> set[Changed[Path]]:
        """Return the paths from the set of changed files."""
        return {change.map(lambda x: x.path) for change in self.changed_files}

    def unchanged_files(self) -> Iterable[FileTuple]:
        """Return the set of unchanged files."""
        changed_before_paths = {
            path
            for pchange in self.changed_paths()
            if (path := get_before(pchange)) is not None
        }
        for path, code in self.before_files.items():
            if path not in changed_before_paths:
                yield FileTuple(path, code)

    def check_changed_files(self) -> None:
        """Check if `change_files` is consistent with the recorded changes."""
        for change in self.changed_files:
            if before_file := get_before(change):
                before_path, before_code = before_file
                assert (
                    before_path in self.before_files
                ), f"{before_path=}, {self.before_files.keys()}"
                assert_str_eq(before_code, self.before_files[before_path])
            if after_file := get_after(change):
                after_path, after_code = after_file
                assert (
                    after_path in self.after_files
                ), f"{after_path=}, {self.after_files.keys()}"
                assert_str_eq(after_code, self.after_files[after_path])

    def show_changed_files(self) -> str:
        """Print the set of changed files in a Git-style."""
        lines = list[str]()
        for change in self.changed_files:
            if isinstance(change, Added):
                lines.append(f"A {change.after.path}")
            elif isinstance(change, Deleted):
                lines.append(f"D {change.before.path}")
            elif isinstance(change, Modified):
                if change.before.path == change.after.path:
                    lines.append(f"M {change.before.path}")
                else:
                    lines.append(f"R {change.before.path} -> {change.after.path}")
            else:
                assert_never(change)
        return "\n".join(lines)

    def add_changed_file(self, file_change: Changed[FileTuple]) -> RepoChange:
        """Build a new RepoChange with one more file change appeneding to the end."""
        before_file = get_before(file_change)
        after_file = get_after(file_change)
        if before_file == after_file:
            return self

        new_changed_files = self.changed_files.append(file_change)
        after_files = self.after_files
        if before_file:
            # this file change should start from the current file state
            before_path, before_code = before_file
            if before_code != after_files[before_path]:
                raise ValueError(
                    f"Inconsistent before file: {before_path}. Diff shown below:\n"
                    + show_str_diff(after_files[before_path], before_code),
                )
            after_files = after_files.discard(before_path)
        if after_file:
            after_path, after_code = after_file
            after_files = after_files.set(after_path, after_code)
        return RepoChange(self.before_files, after_files, new_changed_files)

    def drop_change(self, path_change: Changed[Path]) -> RepoChange:
        """Build a new RepoChange with one less file change."""
        [file_change] = [
            change
            for change in self.changed_files
            if change.map(lambda x: x.path) == path_change
        ]
        after_files = self.after_files
        if after_file := get_after(file_change):
            after_path, after_code = after_file
            if after_code != after_files[after_path]:
                raise ValueError(
                    f"Inconsistent after file: {after_path}. Diff shown below:\n"
                    + show_str_diff(after_code, after_files[after_path]),
                )
            after_files = after_files.discard(after_file.path)
        if before_file := get_before(file_change):
            before_path, before_code = before_file
            after_files = after_files.set(before_path, before_code)
        changed_files = self.changed_files.remove(file_change)
        return RepoChange(self.before_files, after_files, changed_files)

    def filter_by_path(self, filter_fn: Callable[[Path], bool]) -> RepoChange:
        """Build a new RepoChange by filtering _out_ changes that match `filter_fn`.

        Args:
            filter_fn: A function that returns true if a given path should be removed.
                We match paths in both the before and after state.
        Returns:
            A new RepoChange with the given files removed.
        """
        before_files = pyr_map(
            {
                path: code
                for path, code in self.before_files.items()
                if not filter_fn(path)
            }
        )
        after_files = pyr_map(
            {
                path: code
                for path, code in self.after_files.items()
                if not filter_fn(path)
            }
        )
        changed_files = pyr_vector(
            [
                change
                for change in self.changed_files
                if (
                    (before_file := get_before(change)) is None
                    or not filter_fn(before_file.path)
                )
                and (
                    (after_file := get_after(change)) is None
                    or not filter_fn(after_file.path)
                )
            ]
        )
        return RepoChange(before_files, after_files, changed_files)

    def squash(self, next_change: RepoChange | None = None) -> RepoChange:
        """Build a new RepoChange by squashing file changes.

        The modification order among file changes is preserved. If the same file is
        modified by multiple file changes, the time of the last modification will
        be used to determine the file change order.

        Raise:
            GitOperationError: when git fails to compute repo diff.
        """

        if next_change is None:
            next_change = RepoChange.no_change(self.after_files)

        # compute the new change order
        file_mod_time = dict[Path, int]()
        for i, change in enumerate(self.changed_files + next_change.changed_files):
            if (before_file := get_before(change)) is not None:
                file_mod_time[before_file.path] = i
            if (after_file := get_after(change)) is not None:
                file_mod_time[after_file.path] = i

        def get_mod_time(change: Changed[Path]) -> int:
            match change:
                case Modified(before_path, after_path):
                    return max(file_mod_time[before_path], file_mod_time[after_path])
                case Deleted(before_path):
                    return file_mod_time[before_path]
                case Added(after_path):
                    return file_mod_time[after_path]
                case _:
                    raise AssertionError(f"Shouldn't happen: {change=}")

        before_files = self.before_files
        after_files = next_change.after_files
        changed_file_paths = _find_changed_files_in_repos(
            self.before_repo(), next_change.after_repo(), is_source_file=lambda _: True
        )
        # sort the path changes by mod time
        changed_file_paths = sorted(changed_file_paths, key=get_mod_time)

        # map changed paths into changed files
        changed_files = list[Changed[FileTuple]]()
        for path_change in changed_file_paths:
            match path_change:
                case Modified(before_path, after_path):
                    file_change = Modified(
                        FileTuple(before_path, before_files[before_path]),
                        FileTuple(after_path, after_files[after_path]),
                    )
                case Deleted(before_path):
                    file_change = Deleted(
                        FileTuple(before_path, before_files[before_path])
                    )
                case Added(after_path):
                    file_change = Added(FileTuple(after_path, after_files[after_path]))
                case _:
                    raise AssertionError(f"Shouldn't happen: {path_change=}")
            changed_files.append(file_change)

        return RepoChange(
            before_files=before_files,
            after_files=after_files,
            changed_files=pyr_vector(changed_files),
        )

    def before_repo(self) -> Repository:
        """Return the repository before the change."""
        return Repository(
            files=[File(str(path), code) for path, code in self.before_files.items()]
        )

    def after_repo(self) -> Repository:
        """Return the repository before the change."""
        return Repository(
            files=[File(str(path), code) for path, code in self.after_files.items()]
        )

    def __repr__(self) -> str:
        return (
            f"RepoChange(len(before_files)={len(self.before_files)}, "
            f"len(after_files)={len(self.after_files)}, "
            f"changed_files_paths={[c.get_later().path for c in self.changed_files]})"
        )


T = TypeVar("T")


def squash_file_changes(
    changes: Sequence[Changed[FileTuple]],
) -> PyrVector[Changed[FileTuple]]:
    """Squash a series of contiguous file changes into a new sequence.

    The new sequence contains at most one change per file and is sorted
    by each file's last modified time in the original sequence.

    Note that unlike RepoChange.squash, this function does not call `git` to figure
    out the file changes, so it might not recognize some file renaming operations, but
    this is also considerable more efficient.
    """
    path_to_file_change = dict[Path, Changed[FileTuple]]()
    n_deletions = 0

    for change in changes:
        if isinstance(change, Added):
            path_to_file_change[change.after.path] = change
        elif isinstance(change, Deleted):
            change0 = path_to_file_change.pop(change.before.path, None)
            if isinstance(change0, (Added, Modified)):
                change = _squash_changed_file(change0, change)
            deletion_path = Path(f"/dev/null/{n_deletions}")
            if change is not None:
                path_to_file_change[deletion_path] = change
                n_deletions += 1
        elif isinstance(change, Modified):
            change0 = path_to_file_change.pop(change.before.path, None)
            if isinstance(change0, (Added, Modified)):
                change = _squash_changed_file(change0, change)
            if isinstance(change, (Added, Modified)):
                path_to_file_change[change.after.path] = change

    return pyr_vector(path_to_file_change.values())


def _squash_changed_file(
    change: Added[FileTuple] | Modified[FileTuple],
    next_change: Modified[FileTuple] | Deleted[FileTuple],
) -> Changed[FileTuple] | None:
    """Squash two changed values into a single one.

    Returns None if the two changes cancel each other.
    """
    match change, next_change:
        case Added(a), Deleted(b):
            assert_eq(a.path, b.path)
            assert_str_eq(a.code, b.code)
            return None
        case Added(a), Modified(b, c):
            assert_eq(a.path, b.path)
            assert_str_eq(a.code, b.code)
            return Added(c)
        case Modified(a, b), Modified(c, d):
            assert_eq(b.path, c.path)
            if a.code == d.code:
                return None
            return Modified(a, d)
        case Modified(a, b), Deleted(c):
            assert_eq(b.path, c.path)
            assert_str_eq(b.code, c.code)
            return Deleted(a)
        case _:
            raise ValueError(f"Bad types: {type(change)=}, {type(next_change)=}")


def _squash_changed(
    change: Added[T] | Modified[T], next_change: Modified[T] | Deleted[T]
) -> Changed[T] | None:
    """Squash two changed values into a single one.

    Returns None if the two changes cancel each other.
    """
    match change, next_change:
        case Added(a), Deleted(b):
            assert_eq(a, b)
            return None
        case Added(a), Modified(b, c):
            assert_eq(a, b)
            return Added(c)
        case Modified(a, b), Modified(c, d):
            assert_eq(b, c)
            if a == d:
                return None
            return Modified(a, d)
        case Modified(a, b), Deleted(c):
            assert_eq(b, c)
            return Deleted(a)
        case _:
            raise ValueError(f"Bad types: {type(change)=}, {type(next_change)=}")


def is_training_source_file(path: str | Path) -> bool:
    """Check if the given path is a lang defined in base.languages."""
    if isinstance(path, str):
        path = Path(path)
    return (
        bool(path.suffix)
        and guess_language(path) is not None
        and path.suffix != ".ipynb"
        and not str(path).endswith(".min.js")
    )


def iterate_repo_history(
    project_dir: Path,
    history: Sequence[CommitMeta],
    is_source_file: Callable[[Path], bool] = is_training_source_file,
    silent: bool = False,
    max_workers: int = 1,
) -> Iterable[RepoChange]:
    """Iterate through a repo history and yield repo changes between adjacent commits.

    Args:
        project_dir: the project direcotry containing the .git file.
        history: a sorted, contiguous list of commits to sample from.
        is_source_file: a function that returns whether a given path is a source file.\
            Only source files will be included in the results.
        silent: If false, will print out progress.
        max_workers: The maximum number of workers used to speed up git operations.

    Returns:
        A stream of `N-1` RepoChange objects, where `N` is the length of `history`.

    See also:
        `get_commit_history` to get a list of commits.
    """
    if len(history) < 2:
        return ()
    prev_commit = history[0]
    init_files = [
        path
        for path in files_from_commit(project_dir, prev_commit.sha)
        if is_source_file(path)
    ]

    init_contents = process_map(
        file_content_from_commit,
        init_files,
        key_args={"project_dir": project_dir, "commit": prev_commit.sha},
        desc="building repo",
        tqdm_args={
            "smoothing": 0,
            "unit": "file",
            "disable": silent,
        },
        max_workers=max_workers,
    )
    prev_files: PyrMap[Path, str] = pyr_map(
        {path: content for path, content in zip(init_files, init_contents)}
    )

    for current_commit in history[1:]:
        all_changes = _find_changed_files_in_commits(
            project_dir, prev_commit.sha, current_commit.sha, is_source_file
        )
        current_files = prev_files
        to_update = list[Path]()
        for path_change in all_changes:
            if before_path := get_before(path_change):
                current_files = current_files.discard(before_path)
            if after_path := get_after(path_change):
                to_update.append(after_path)

        new_contents = process_map(
            file_content_from_commit,
            to_update,
            key_args={"project_dir": project_dir, "commit": current_commit.sha},
            desc="building repo",
            tqdm_args={
                "disable": True,
            },
            max_workers=max_workers,
        )

        current_files = current_files.update(dict(zip(to_update, new_contents)))
        changed_files: list[Changed[FileTuple]] = []

        for path_change in all_changes:
            before_path = get_before(path_change)
            after_path = get_after(path_change)
            if before_path and after_path:
                changed_files.append(
                    Modified(
                        FileTuple(before_path, prev_files[before_path]),
                        FileTuple(after_path, current_files[after_path]),
                    )
                )
            elif before_path:
                changed_files.append(
                    Deleted(FileTuple(before_path, prev_files[before_path]))
                )
            elif after_path:
                changed_files.append(
                    Added(FileTuple(after_path, current_files[after_path]))
                )

        yield RepoChange(prev_files, current_files, pyr_vector(changed_files))
        prev_files = current_files
        prev_commit = current_commit


@dataclass(frozen=True)
class CommitMeta(DataClassJsonMixin):
    """Metadata about a commit."""

    sha: str
    """The SHA of the commit."""

    message: str
    """The commit message."""

    repo_name: str = ""
    """The name of the repo."""

    parents: tuple[str, ...] = ()
    """The parent commits' SHAs."""

    children: tuple[str, ...] = ()
    """The child commits' SHAs."""

    debugging_info: str | None = None
    """A string of debugging info from commit generation"""

    def summary(self) -> str:
        """A short summary of the commit."""
        return f"[{self.sha[:10]} {shorten_str(self.message)}]"

    def __str__(self) -> str:
        return self.summary()


def get_commit_history(
    project_dir: Path,
    max_history: int | None = None,
    commit_id: str = "HEAD",
) -> list[CommitMeta]:
    """Trace back from the given commit to build a linearized commit history.

    The returned history is sorted chronologically and ends at the given `commit_id`.
    When a merge commit is encountered, the second parent (the branch that's
    being merged in) is used as the history.
    """
    commit_id = _run_command(
        ["git", "rev-parse", commit_id],
        cwd=project_dir,
    ).strip()
    repo_name = project_dir.name
    rev_history = list[CommitMeta]()
    for _ in range(max_history if max_history else 100000):
        try:
            lines = _run_command(
                ["git", "cat-file", "-p", commit_id],
                cwd=project_dir,
            ).splitlines()
        except subprocess.CalledProcessError as e:
            logging.error(f"Failed to get commit {commit_id}: {e}")
            break
        parents = []
        for line in lines[1:]:
            if line.startswith("parent "):
                parents.append(line.split(" ")[1])
            else:
                break
        commit_msg = _run_command(
            ["git", "show", commit_id, "-s", "--format=%B"],
            cwd=project_dir,
        ).strip()
        rev_history.append(
            CommitMeta(
                commit_id,
                message=commit_msg,
                repo_name=repo_name,
                parents=tuple(parents),
            )
        )
        if not parents:
            break
        commit_id = parents[-1]

    return list(reversed(rev_history))


@dataclass
class PatchedFileMeta:
    """Stores the meta data of a unidiff.PatchedFile."""

    before_path: Path | None
    after_path: Path | None

    @classmethod
    def from_patched_file(cls, pfile: PatchedFile) -> "PatchedFileMeta":
        before_file = cls.parse_file_path(pfile.source_file, "a/")
        after_file = cls.parse_file_path(pfile.target_file, "b/")
        return cls(before_file, after_file)

    @classmethod
    def parse_file_path(cls, path_str: str | None, rm_prefix: str) -> Path | None:
        if path_str is None or path_str == DEV_NULL:
            return None
        if path_str.startswith(rm_prefix):
            path_str = path_str[len(rm_prefix) :]
        return Path(path_str)


def repo_change_from_patchset(
    repo: Repository, patch_set: PatchSet, reverse: bool = False
) -> RepoChange:
    """Build a repo change from the given repo and patch set.

    Args:
        repo: The repository to apply the patch to.
        patch_set: The patch set to apply.
        reverse: Whether to reverse the patch set.
            If false (default), `repo` is the "before" state of the repo.
            If true, `repo` is the "after" state of the repo.

    Returns:
        A RepoChange object from the "before" state to the "after" state.
    """

    repo_files = {Path(file.path): file.contents for file in repo.files}
    repo_files = pyr_map(repo_files)

    # find the subset of files modified by the patch set
    patch_targets = set[Path]()
    for pfile in patch_set:
        meta = PatchedFileMeta.from_patched_file(pfile)
        if not reverse and (target_path := meta.before_path):
            patch_targets.add(target_path)
        elif reverse and (target_path := meta.after_path):
            patch_targets.add(target_path)

    target_files = [
        File(str(path), code)
        for path, code in repo_files.items()
        if path in patch_targets
    ]
    # apply the patch set on the target files to form the new repo
    try:
        new_repo = apply_diff(Repository(target_files), patch_set, reverse=reverse)
    except ValueError as e:
        target_file_paths = [file.path for file in target_files]
        raise ValueError(
            f"Failed to apply diff. target files: {target_file_paths}\n"
            f"Repo files: {repo_files.keys()}\n"
            f"patch set:\n{str(patch_set)}"
        ) from e
    # now combine the new repo with before_files to form after_files.
    # note that there may be deleted or removed files that we'll remove
    # from after_files in the for loop below.
    new_repo_dict = {Path(file.path): file.contents for file in new_repo.files}
    new_repo_files: PyrMap[Path, str] = repo_files.update(new_repo_dict)

    if reverse:
        before_files, after_files = new_repo_files, repo_files
    else:
        before_files, after_files = repo_files, new_repo_files

    # record the changed files using the RepoChange format
    all_changes = list[Changed[FileTuple]]()
    for pfile in patch_set:
        meta = PatchedFileMeta.from_patched_file(pfile)
        before_path = meta.before_path
        after_path = meta.after_path
        if before_path is not None and before_path not in before_files:
            raise FileNotFoundError(f"Missing before file: {before_path}")
        if after_path is not None and after_path not in after_files:
            raise FileNotFoundError(f"Missing after file: {after_path}")

        if before_path and after_path:
            change = Modified(
                FileTuple(before_path, before_files[before_path]),
                FileTuple(after_path, after_files[after_path]),
            )
            if not reverse and after_path != before_path:
                after_files = after_files.discard(before_path)
            elif reverse and after_path != before_path:
                before_files = before_files.discard(after_path)
        elif before_path:
            change = Deleted(FileTuple(before_path, before_files[before_path]))
            if not reverse:
                after_files = after_files.discard(before_path)
        elif after_path:
            change = Added(FileTuple(after_path, after_files[after_path]))
            if reverse:
                before_files = before_files.discard(after_path)
        else:
            raise AssertionError(f"Shouldn't happen: {before_path=}, {after_path=}")
        all_changes.append(change)

    return RepoChange(before_files, after_files, pyr_vector(all_changes))


def patchset_from_repo_change(
    repo_change: RepoChange, num_context_lines: int, ignore_whitespace: bool = False
) -> PatchSet:
    """Build a patch set from the given repo change."""
    before_files = list[File]()
    after_files = list[File]()
    for change in repo_change.changed_files:
        if before_file := get_before(change):
            before_files.append(before_file.to_file())
        if after_file := get_after(change):
            after_files.append(after_file.to_file())
    repo_before = Repository(before_files)
    repo_after = Repository(after_files)
    return compute_repo_diff(
        repo_before,
        repo_after,
        num_context_lines=num_context_lines,
        ignore_whitespace=ignore_whitespace,
    )


def file_content_from_commit(
    path: str | Path,
    project_dir: Path,
    commit: str,
) -> str:
    """Get the content of a file at a given commit."""
    path = Path(path)
    if path.is_absolute():
        path = path.relative_to(project_dir)
    try:
        return _run_command(
            ["git", "show", f"{commit}:{path}"],
            cwd=project_dir,
        )
    except (UnicodeDecodeError, subprocess.CalledProcessError):
        msg = (
            "Failed to decode file from git. Returning an empty string."
            f"File: {path}\n"
            f"Commit: {commit}\n"
            f"Project: {project_dir}\n"
        )
        logging.error(msg)
        return ""


def fetch_commit(
    project_dir: Path,
    commit: str,
):
    _run_command(
        ["git", "fetch", "origin", commit],
        cwd=project_dir,
    )


def files_from_commit(
    project_dir: Path,
    commit: str,
) -> Iterable[Path]:
    """Get all files that are in a given commit."""
    try:
        return (
            Path(path)
            for path in _run_command(
                ["git", "ls-tree", "-r", "--name-only", commit],
                cwd=project_dir,
            ).splitlines()
        )
    except subprocess.CalledProcessError:
        # fetch the commit
        start_time = time.time()
        logging.warning(f"Failed to get files from commit {commit}. Fetching...")
        fetch_commit(project_dir, commit)
        logging.warning(
            f"Finished fetching commit {commit} after {time.time()-start_time:.1f} seconds."
        )
        output = (
            Path(path)
            for path in _run_command(
                ["git", "ls-tree", "-r", "--name-only", commit],
                cwd=project_dir,
            ).splitlines()
        )
        logging.warning(
            "Upon fetch+retry, we have successfully git ls-tree'd the commit."
        )
        return output


def _run_command(args: Sequence[str], cwd: str | Path) -> str:
    """Run a command and return the output."""
    return subprocess.check_output(
        args,
        cwd=cwd,
        text=True,
    )


def _find_changed_files_in_commits(
    project_dir: Path,
    prev_commit_hash: str,
    new_commit_hash: str,
    is_source_file: Callable[[Path], bool],
) -> set[Changed[Path]]:
    # get changed files
    try:
        git_output = _run_command(
            [
                "git",
                "diff",
                "--name-status",
                prev_commit_hash,
                new_commit_hash,
            ],
            cwd=project_dir,
        )
    except subprocess.CalledProcessError:
        # Try fetching the commits
        start_time = time.time()
        logging.warning(
            f"Failed to get files from commits {prev_commit_hash} and {new_commit_hash}. Fetching now..."
        )
        fetch_commit(project_dir, prev_commit_hash)
        fetch_commit(project_dir, new_commit_hash)
        logging.warning(
            f"Finished fetching commits {prev_commit_hash} and {new_commit_hash} after {time.time()-start_time:.1f} seconds."
        )
        git_output = _run_command(
            [
                "git",
                "diff",
                "--name-status",
                prev_commit_hash,
                new_commit_hash,
            ],
            cwd=project_dir,
        )
        logging.warning(
            "Upon fetch+retry, we have successfully git diffed the commits."
        )
    return _parse_name_status(git_output, is_source_file)


def _find_changed_files_in_repos(
    before_repo: Repository,
    after_repo: Repository,
    is_source_file: Callable[[Path], bool],
) -> set[Changed[Path]]:
    def fix_path(path: Path) -> Path:
        text = str(path)
        if text.startswith("a/"):
            return Path(text[2:])
        if text.startswith("b/"):
            return Path(text[2:])
        return path

    with tempfile.TemporaryDirectory() as project_dir:
        before_repo, after_repo = _get_sub_repos_for_diff(before_repo, after_repo)
        before_repo.save(Path(project_dir) / "a")
        after_repo.save(Path(project_dir) / "b")
        result = subprocess.run(
            [
                "git",
                "diff",
                "--name-status",
                "--no-index",
                "--",
                "a",
                "b",
            ],
            cwd=project_dir,
            capture_output=True,
            text=True,
        )
    git_output = result.stdout
    # somehow git diff don't always have a return code of 0
    if result.stderr.strip():
        raise GitOperationError(f"git diff failed: {result.stderr}")
    changed_paths = _parse_name_status(git_output, is_source_file)
    changed_paths = {change.map(fix_path) for change in changed_paths}
    return changed_paths


def _parse_name_status(git_output: str, is_source_file: Callable[[Path], bool]):
    git_lines = git_output.splitlines()
    path_changes: set[Changed[Path]] = set()

    for line in git_lines:
        segs = line.split("\t")
        if len(segs) == 2:
            # e.g., "A\tfile.py"
            tag, path = segs
            path = Path(path)
            if not is_source_file(path):
                continue
            if tag.endswith("A"):
                path_changes.add(Added(path))
            elif tag.endswith("D"):
                path_changes.add(Deleted(path))
            if tag.endswith("M"):
                path_changes.add(Modified(path, path))
        elif len(segs) == 3:
            # e.g., "R99\tfile1.py\tfile2.py"
            tag, path1, path2 = segs
            path1 = Path(path1)
            path2 = Path(path2)
            assert tag.startswith("R")
            if is_source_file(path1) and is_source_file(path2):
                path_changes.add(Modified(path1, path2))
            else:
                if is_source_file(path1):
                    path_changes.add(Deleted(path1))
                if is_source_file(path2):
                    path_changes.add(Added(path2))
    return path_changes


def repo_change_from_repositories(
    repo_before: Repository, repo_after: Repository, patch_set: PatchSet | None = None
) -> RepoChange:
    """Build a repo change from a before and after version of a repository.

    TODO(arun): This is pretty in-efficient as we compute a git diff to figure out
    renamed files, and then re-apply the diff to get the new repo. This function is
    currently only used in the unit tests, so it's not a big deal.

    Args:
        repo_before: The repository before the change.
        repo_after: The repository after the change.
        patch_set: If provided, use this patch set instead of computing a diff. This is
            strictly an efficiency improvement.

    Returns:
        A RepoChange object.
    """
    if patch_set is None:
        patch_set = compute_repo_diff(repo_before, repo_after, num_context_lines=0)

    return repo_change_from_patchset(repo_before, patch_set)

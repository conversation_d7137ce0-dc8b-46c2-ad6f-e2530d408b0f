"""Split an indexed dataset into smaller ones (typically for train/val splits)."""

import argparse
from collections.abc import MutableMapping
from pathlib import Path

import megatron.data.indexed_dataset as indexed_dataset
import numpy as np


def split_indexed_dataset(
    input_dataset_root: Path,
    split_root_to_length: MutableMapping[Path, int],
    shuffle: bool = True,
):
    """Split an indexed dataset into smaller ones (typically for train/val splits).

    Args:
        input_dataset_root: Path to an indexed dataset. The path should be to the "root"
            of the dataset (minus the ".bin" or ".idx" suffix).
        split_root_to_length: A mapping for the output dataset root (minus the ".bin" or
            ".idx" suffix) to the number of documents to include in the split. The value
            can include at most one -1 element which indicates that that count should be
            equal to the remainder.
        shuffle: If True, shuffle the documents before splitting.
    """
    assert indexed_dataset.MMapIndexedDataset.exists(str(input_dataset_root))
    assert (
        sum([1 for v in split_root_to_length.values() if v < 0]) <= 1
    ), f"Only one split in {split_root_to_length=} can be -1"
    split_total = sum([v for v in split_root_to_length.values() if v >= 0])

    # Load the dataset.
    dataset = indexed_dataset.MMapIndexedDataset(
        str(input_dataset_root), skip_warmup=True
    )
    assert dataset._index is not None, "Dataset must be loaded"
    assert (
        split_total <= len(dataset)
    ), f"Sum of splits {split_total=} in {split_root_to_length=} must be <= {len(dataset)}"

    # Resolve the split lengths.
    if -1 in split_root_to_length.values():
        (remainder_key,) = [k for k, v in split_root_to_length.items() if v == -1]
        split_root_to_length[remainder_key] = len(dataset) - split_total

    if shuffle:
        ixs = np.random.permutation(len(dataset))
    else:
        ixs = np.arange(len(dataset))

    start = 0
    for path, split in split_root_to_length.items():
        stop = start + split
        builder = indexed_dataset.MMapIndexedDatasetBuilder(
            str(path) + ".bin", dtype=dataset._index.dtype
        )
        for ix in ixs[start:stop]:
            builder.add_array_item(dataset[ix])
            builder.end_document()
        builder.finalize(str(path) + ".idx")
        start = stop


def main():
    """Main."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-i",
        "--input_dataset_root",
        type=Path,
        required=True,
        help="indexed dataset path prefix",
    )
    parser.add_argument(
        "-o",
        "--output_dir",
        type=Path,
        required=False,
        help="output directory. defaults to input_prefix.",
    )
    parser.add_argument(
        "-s",
        "--splits",
        type=str,
        required=True,
        help="comma-separated list of splits, e.g., train=-1,dev=4000,test=4000",
    )
    parser.add_argument(
        "-x",
        "--shuffle",
        type=bool,
        default=True,
        help="Should we shuffle the dataset before splitting?",
    )
    args = parser.parse_args()

    if args.output_dir is None:
        args.output_dir = args.input_dataset_root.parent

    # Parse the splits.
    splits = dict[Path, int]()
    for i, s in enumerate(args.splits.split(",")):
        if "=" in s:
            key, count = s.split("=", 1)
        else:
            key, count = i, s
        splits[args.output_dir / f"dataset.{key}"] = int(count)

    # Create output directory if it does not exist.
    args.output_dir.mkdir(exist_ok=True)
    # Split the dataset.
    split_indexed_dataset(
        args.input_dataset_root,
        splits,
        shuffle=True,
    )


if __name__ == "__main__":
    main()

"""Utils for deserializing pickled objects."""

import io
import pickle
from typing import Any, Callable


def loads(data: bytes, module_remap: Callable[[str], str] = lambda x: x) -> Any:
    """Loads a pickled object from a bytestring with optionally module remapping.

    Args:
        data: The pickled data.
        module_remap: A function to remap module names.

    Returns:
        The unpickled object.
    """

    class RenameUnpickler(pickle.Unpickler):
        def find_class(self, module, name):
            # Rename the module here
            module = module_remap(module)
            return super().find_class(module, name)

    file = io.BytesIO(data)
    unpickler = RenameUnpickler(file)
    return unpickler.load()

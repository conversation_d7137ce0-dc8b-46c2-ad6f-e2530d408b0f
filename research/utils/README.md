# Utils
The `utils` directory contains scripts that are useful for working with the model.

## Descriptions

### Working with Evaluation Datasets

`repo.py`: Creates a dataset archive from git repositories.
Given a reference to a set of git repos, this script will convert the repo into
an lm_dataformat archive suitable for evaluation. The script can accept repos in one
of two ways: (1) by directory, or (2) by csv file.
(Note: this script is largely the same as the one used for compiling the github portion
of The Pile. See [https://github.com/EleutherAI/github-downloader])
The lm_dataformat code can be found at [https://github.com/leogao2/lm_dataformat]

Examples:

To download a set of github repos and create a dataset suitable for evaluation,
do the following:
1. Create a csv file in the format of:
   repo-name, stars, language
   e.g. augmentcode/augment, 5, Python
2. Choose the --csv option and pass the csv file name:
   `python repo.py --csv [csv-file] --archive [archive-name]`

To archive a set of pre-downloaded repos, do the following:
1. Place the repos under a common directory
2. Choose the --dir option and pass the directory name:
   `python repo.py --dir [dirname] --archive [archive-name]`

`join_zst.py`: Combine lm_dataformat zst files into one.
It may be more convenient to deal with a single lm_dataformat file rather
than an archive directory. You can use `join_zst` to convert the archive
directory into a single file.
(Note: this is essentially the same as the join.py script found at [https://github.com/EleutherAI/the-pile])
Example:
`python join_zst.py dest-file.jsonl.zst archive/data_*.zst`

`repo_stats.py`: Calculate basic stats on repos in the lm_dataformat format.
To calculate file, word, and byte counts for repos contained within datasets,
use `zstdcat [repo-dataset.jsonl.zst] | python repo_stats.py`
The output is a set of newline-delimited records, like:
```
Repo files words bytes
augmentcode/augment 456 182039 1940008
```

`split.py`: Select records based on a set of desired repos.
Use `split.py` to select documents out of a larger set of docs, where
the selected docs belong to particular repos. The list of repos is given
in a file. For example:
1. Create a "split-file" that contains the names of repos you want, newline-delimited.
2. Run: `zstdcat [repo-dataset.jsonl.zst] | python split.py [split-file]`
Output goes to stdout.


### Processing Evaluation Reports

`eval_reports.py`: Post process evaluation results.
The evaluation process will output eval_result files into your local directory
that describe the scores for the eval tasks. The `eval_reports.py` script
will extract the results, display them for easy viewing, and optionally
upload them to wandb.

"""Unit tests for repo_change_utils.

We use a test repo's history stored in `research/fim/tests/testdata/test_repo.bundle`.
"""

import subprocess
from pathlib import Path

import pytest

from base.diff_utils.changes import Changed
from base.static_analysis.common import assert_eq
from research.core.changes import Added, Deleted, Modified
from research.core.constants import AUGMENT_ROOT
from research.core.diff_utils import File, Repository
from research.utils.repo_change_utils import (
    FileTuple,
    RepoChange,
    get_commit_history,
    iterate_repo_history,
    patchset_from_repo_change,
    repo_change_from_patchset,
    repo_change_from_repositories,
    squash_file_changes,
)


def is_source_file(path: Path) -> bool:
    # for tests in this file, we only consider python files as source code
    return path.suffix == ".py"


@pytest.fixture
def test_repo_fixture(tmp_path: Path):
    test_bundle_path = AUGMENT_ROOT / "research/utils/tests/testdata/test_repo.bundle"
    # git clone from the bundle
    subprocess.run(
        f"git clone -b master {test_bundle_path} test_repo",
        shell=True,
        check=True,
        cwd=tmp_path,
    )
    yield tmp_path / "test_repo"


def test_get_commit_history(test_repo_fixture: Path):
    """Test get_commit_history on the test repo."""
    history = get_commit_history(test_repo_fixture, commit_id="HEAD")
    assert len(history) == 5
    messages = [x.message for x in history]
    assert_eq(
        messages,
        [
            "Initial commit.",
            "Add file3.",
            "Update file1.",
            "Rename file1 and remove file3.",
            "Add not_code.txt.",
        ],
    )

    shorter_history = get_commit_history(test_repo_fixture, max_history=3)
    assert_eq(len(shorter_history), 3)


def test_iterate_repo_history(test_repo_fixture: Path):
    """Test iterate_repo_history on the test repo."""

    history = get_commit_history(test_repo_fixture, commit_id="HEAD")
    repo_changes = list(
        iterate_repo_history(test_repo_fixture, history, is_source_file, silent=True)
    )
    assert_eq(len(repo_changes), 4)

    change1, change2, change3, change4 = repo_changes
    assert_eq(change1.changed_paths(), {Added(Path("file3.py"))})
    assert_eq(change2.changed_paths(), {Modified(Path("file1.py"), Path("file1.py"))})
    assert_eq(
        change3.changed_paths(),
        {
            Deleted(Path("file3.py")),
            Modified(Path("file1.py"), Path("file1_renamed.py")),
        },
    )
    assert_eq(change4.changed_paths(), set())


def test_add_changed_file(test_repo_fixture: Path):
    """Test that repeatedly applying add_changed_file leads to after repo state."""

    history = get_commit_history(test_repo_fixture, commit_id="HEAD")
    repo_changes = list(
        iterate_repo_history(test_repo_fixture, history, is_source_file, silent=True)
    )
    for repo_change in repo_changes:
        reconstructed = RepoChange.build(
            before_files=repo_change.before_files,
            after_files=repo_change.before_files,
            changed_files=[],
        )
        for change in repo_change.changed_files:
            reconstructed = reconstructed.add_changed_file(change)
        assert reconstructed == repo_change


def test_drop_changed_file(test_repo_fixture: Path):
    """Test that repeatedly applying drop_changed_file leads to before repo state."""

    history = get_commit_history(test_repo_fixture, commit_id="HEAD")
    repo_changes = list(
        iterate_repo_history(test_repo_fixture, history, is_source_file, silent=True)
    )
    for repo_change in repo_changes:
        dropped = repo_change
        for change in repo_change.changed_files:
            dropped = dropped.drop_change(change.map(lambda x: x.path))
        assert dropped.before_files == repo_change.before_files
        assert dropped.after_files == repo_change.before_files
        assert list(dropped.changed_files) == []


def test_patchset_conversion(test_repo_fixture: Path):
    # Test that applying patchset_from_repo_change and then repo_change_from_patchset
    # gives the original repo_change.

    history = get_commit_history(test_repo_fixture, commit_id="HEAD")
    repo_changes = list(
        iterate_repo_history(test_repo_fixture, history, is_source_file, silent=True)
    )

    for repo_change in repo_changes:
        for num_context_lines in range(0, 5):
            patchset = patchset_from_repo_change(
                repo_change, num_context_lines=num_context_lines
            )
            before_files = [
                File(str(path), code) for path, code in repo_change.before_files.items()
            ]
            rebuilt = repo_change_from_patchset(Repository(before_files), patchset)
            assert dict(rebuilt.before_files) == dict(repo_change.before_files)
            assert dict(rebuilt.after_files) == dict(repo_change.after_files)
            assert set(rebuilt.changed_files) == set(repo_change.changed_files)


def test_patchset_conversion_reverse(test_repo_fixture: Path):
    # Test that applying patchset_from_repo_change and then repo_change_from_patchset
    # gives the original repo_change.

    history = get_commit_history(test_repo_fixture, commit_id="HEAD")
    repo_changes = list(
        iterate_repo_history(test_repo_fixture, history, is_source_file, silent=True)
    )

    for repo_change in repo_changes:
        for num_context_lines in range(0, 5):
            patchset = patchset_from_repo_change(
                repo_change, num_context_lines=num_context_lines
            )
            after_files = [
                File(str(path), code) for path, code in repo_change.after_files.items()
            ]
            rebuilt = repo_change_from_patchset(
                Repository(after_files), patchset, reverse=True
            )
            assert dict(rebuilt.before_files) == dict(repo_change.before_files)
            assert dict(rebuilt.after_files) == dict(repo_change.after_files)
            assert set(rebuilt.changed_files) == set(repo_change.changed_files)


def test_patchset_conversion_r_newline():
    # Test that patchset_from_repo_change handles \r characters correctly.
    change = RepoChange.from_file_changes(
        [
            Modified(
                before=File(path="a.txt", contents="\r."),
                after=File(path="a.txt", contents=""),
            )
        ]
    )
    patchset = patchset_from_repo_change(change, num_context_lines=3)
    rebuilt = repo_change_from_patchset(change.before_repo(), patchset)
    assert rebuilt == change


def test_repo_change_from_repositories(test_repo_fixture: Path):
    # Test that applying repo_change_from_repositories gives the original repo_change.

    history = get_commit_history(test_repo_fixture, commit_id="HEAD")
    repo_changes = list(
        iterate_repo_history(test_repo_fixture, history, is_source_file, silent=True)
    )

    for repo_change in repo_changes:
        before_repo = repo_change.before_repo()
        after_repo = repo_change.after_repo()
        rebuilt = repo_change_from_repositories(before_repo, after_repo)
        assert dict(rebuilt.before_files) == dict(repo_change.before_files)
        assert dict(rebuilt.after_files) == dict(repo_change.after_files)
        assert set(rebuilt.changed_files) == set(repo_change.changed_files)


def test_repo_change_squashing_cases():
    # scenario: add then modify
    change1 = RepoChange.build(
        before_files={Path("a.py"): "a"},
        after_files={Path("a.py"): "a", Path("b.py"): "b"},
        changed_files=[Added(FileTuple(Path("b.py"), "b"))],
    )
    change2 = RepoChange.build(
        before_files={Path("a.py"): "a", Path("b.py"): "b"},
        after_files={Path("a.py"): "a", Path("b.py"): "b_mod"},
        changed_files=[
            Modified(FileTuple(Path("b.py"), "b"), FileTuple(Path("b.py"), "b_mod"))
        ],
    )
    squashed = change1.squash(change2)
    assert_eq(squashed.before_files, change1.before_files)
    assert_eq(squashed.after_files, change2.after_files)
    assert_eq(list(squashed.changed_files), [Added(FileTuple(Path("b.py"), "b_mod"))])

    # scenario: modify then delete
    change3 = RepoChange.build(
        before_files={Path("a.py"): "a", Path("b.py"): "b_mod"},
        after_files={Path("a.py"): "a"},
        changed_files=[Deleted(FileTuple(Path("b.py"), "b_mod"))],
    )
    squashed = squashed.squash(change3)
    assert_eq(dict(squashed.before_files), {Path("a.py"): "a"})
    assert_eq(dict(squashed.after_files), {Path("a.py"): "a"})
    assert_eq(list(squashed.changed_files), [])

    # scenario: delete then add
    change4 = RepoChange.build(
        before_files={Path("a.py"): "a"},
        after_files={Path("a.py"): "a", Path("b.py"): "b"},
        changed_files=[Added(FileTuple(Path("b.py"), "b"))],
    )
    squashed = change3.squash(change4)
    assert_eq(squashed.before_files, change3.before_files)
    assert_eq(squashed.after_files, change4.after_files)
    assert_eq(
        list(squashed.changed_files),
        [Modified(FileTuple(Path("b.py"), "b_mod"), FileTuple(Path("b.py"), "b"))],
    )

    # scenario: delete then add with a different name
    change5 = RepoChange.build(
        before_files={Path("a.py"): "this is a long line"},
        after_files={},
        changed_files=[Deleted(FileTuple(Path("a.py"), "this is a long line"))],
    )
    change6 = RepoChange.build(
        before_files={},
        after_files={Path("a1.py"): "this is a long line"},
        changed_files=[Added(FileTuple(Path("a1.py"), "this is a long line"))],
    )
    squashed = change5.squash(change6)
    assert_eq(squashed.before_files, change5.before_files)
    assert_eq(squashed.after_files, change6.after_files)
    assert_eq(
        list(squashed.changed_files),
        [
            Modified(
                FileTuple(Path("a.py"), "this is a long line"),
                FileTuple(Path("a1.py"), "this is a long line"),
            )
        ],
    )


def test_repo_change_squashing_order_preserved():
    change1 = RepoChange.build(
        before_files={},
        after_files={Path("a.py"): "a", Path("b.py"): "b"},
        changed_files=[
            Added(FileTuple(Path("a.py"), "a")),
            Added(FileTuple(Path("b.py"), "b")),
        ],
    )
    change2 = RepoChange.build(
        before_files={Path("a.py"): "a", Path("b.py"): "b"},
        after_files={
            Path("a.py"): "a_mod",
            Path("b.py"): "b",
            Path("c.py"): "c",
            Path("d.py"): "d",
        },
        changed_files=[
            Added(FileTuple(Path("d.py"), "d")),
            Modified(FileTuple(Path("a.py"), "a"), FileTuple(Path("a.py"), "a_mod")),
            Added(FileTuple(Path("c.py"), "c")),
        ],
    )
    squashed = change1.squash(change2)
    squashed_order = [
        Added(FileTuple(Path("b.py"), "b")),
        Added(FileTuple(Path("d.py"), "d")),
        Added(FileTuple(Path("a.py"), "a_mod")),
        Added(FileTuple(Path("c.py"), "c")),
    ]
    assert_eq(list(squashed.changed_files), squashed_order)


def test_repo_change_squashing_on_repo(test_repo_fixture: Path):
    # Test that squashing repo changes gives the correct result.

    history = get_commit_history(test_repo_fixture, commit_id="HEAD")
    repo_changes = list(
        iterate_repo_history(test_repo_fixture, history, is_source_file, silent=True)
    )

    before_files = repo_changes[0].before_files
    squashed = repo_changes[0]

    for next_change in repo_changes[1:]:
        squashed = squashed.squash(next_change)
        assert_eq(squashed.before_files, before_files)
        assert_eq(squashed.after_files, next_change.after_files)

        applied = RepoChange.from_before_and_changes(
            before_files, squashed.changed_files
        ).after_files
        assert dict(applied) == dict(next_change.after_files)


def test_squash_file_changes_basic_example():
    file_changes = [
        Modified(
            before=FileTuple(path=Path("src/file1.py"), code="version1"),
            after=FileTuple(path=Path("src/file1.py"), code="version2"),
        ),
        Modified(
            before=FileTuple(path=Path("src/file2.py"), code="unrelated1"),
            after=FileTuple(path=Path("src/file2.py"), code="unrelated2"),
        ),
        Modified(
            before=FileTuple(path=Path("src/file1.py"), code="version2"),
            after=FileTuple(path=Path("src/file1.py"), code="version3"),
        ),
    ]
    squashed = squash_file_changes(file_changes)
    expected = [
        Modified(
            before=FileTuple(path=Path("src/file2.py"), code="unrelated1"),
            after=FileTuple(path=Path("src/file2.py"), code="unrelated2"),
        ),
        Modified(
            before=FileTuple(path=Path("src/file1.py"), code="version1"),
            after=FileTuple(path=Path("src/file1.py"), code="version3"),
        ),
    ]
    assert_eq(list(squashed), expected)


def test_squash_file_changes_addition_and_renaming_example():
    file_changes = [
        Added(
            FileTuple(path=Path("src/file1.py"), code="version1"),
        ),
        Modified(
            before=FileTuple(path=Path("src/file1.py"), code="version1"),
            after=FileTuple(path=Path("src/file1.py"), code="version2"),
        ),
        Deleted(
            FileTuple(path=Path("src/file2.py"), code="unrelated"),
        ),
        Modified(
            before=FileTuple(path=Path("src/file1.py"), code="version2"),
            after=FileTuple(path=Path("src/file1_renamed.py"), code="version3"),
        ),
    ]
    squashed = squash_file_changes(file_changes)
    expected = [
        Deleted(
            FileTuple(path=Path("src/file2.py"), code="unrelated"),
        ),
        Added(
            FileTuple(path=Path("src/file1_renamed.py"), code="version3"),
        ),
    ]
    assert_eq(list(squashed), expected)


def test_squash_file_changes_on_repo(test_repo_fixture: Path):
    history = get_commit_history(test_repo_fixture, commit_id="HEAD")
    repo_changes = list(
        iterate_repo_history(test_repo_fixture, history, is_source_file, silent=True)
    )

    file_changes = list[Changed[FileTuple]]()
    before_files = repo_changes[0].before_files

    for next_change in repo_changes:
        file_changes.extend(next_change.changed_files)
        files_no_squash = RepoChange.from_before_and_changes(
            before_files, file_changes
        ).after_files
        assert dict(files_no_squash) == dict(next_change.after_files)

        files_with_squash = RepoChange.from_before_and_changes(
            before_files, squash_file_changes(file_changes)
        ).after_files
        assert dict(files_with_squash) == dict(next_change.after_files)

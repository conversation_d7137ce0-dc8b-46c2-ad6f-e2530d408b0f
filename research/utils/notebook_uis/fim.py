"""Notebook UIs for visualizing FIM and next edit problems."""

from __future__ import annotations

import difflib

import ipywidgets as widgets
from IPython.display import display

from base.static_analysis.common import shorten_str
from research.fim.diff_fim_sampling import DiffFimProblem
from research.fim.repo_diff_fim_sampling import (
    DiffFimProblemWithContext,
    GitDiffFimSamplerResult,
)
from research.utils.notebook_uis.notebook_uis import (
    code_html,
    render_item_browser_with_params,
    render_repo_change,
    _UIParams,
)


def render_diff_fim_problem(
    problem_ctx: DiffFimProblem,
    params: _UIParams,
) -> widgets.Widget:
    """Render a DiffFimProblem as HTML."""
    context_before = problem_ctx.code_before
    fim_prob = problem_ctx.fim_problem

    middle = fim_prob.show_middle()

    context_after = "".join(
        [
            fim_prob.prefix.code,
            "<|middle|>",
            fim_prob.suffix.code,
        ]
    )
    context_diff = "".join(
        difflib.unified_diff(
            context_before.splitlines(keepends=True),
            context_after.splitlines(keepends=True),
            n=5,
        )
    )
    begin_id = context_diff.index("<|middle|>")
    end_id = context_diff.index("<|middle|>") + len("<|middle|>")

    prefix = shorten_str(context_diff[:begin_id], params.max_prefix_chars, "left")
    suffix = shorten_str(context_diff[end_id:], params.max_suffix_chars, "right")
    middle = shorten_str(middle, params.max_middle_chars, omit_mode="right")

    parts = [
        code_html(prefix),
        code_html("<|middle|>", "color: blue;"),
        code_html(suffix),
        "<hr class='solid'>",
        code_html(middle, "color: blue;"),
    ]
    prompt = "".join(parts)

    return widgets.VBox(
        children=[
            widgets.HTML(prompt),
            widgets.HTML(
                "<style type='text/css'>" + "span {line-height: 0;}" + "</style>"
            ),
        ],
    )


def render_ctx_diff_fim_problem(
    problem_ctx: DiffFimProblemWithContext,
    logs: list[str] | None,
    params: _UIParams,
):
    titles = ["FIM Problem"]
    children = [render_diff_fim_problem(problem_ctx.diff_fim_problem, params)]
    if logs:
        titles.append("Logs")
        children.append(widgets.HTML(code_html("\n".join(logs))))
    context = problem_ctx.retrieval_context
    titles.append(f"Repo Change ({len(context.changed_files)})")
    children.append(render_repo_change(context))
    tabs = widgets.Tab(children=children, titles=titles)

    path_before = problem_ctx.diff_fim_problem.path_before
    path_after = problem_ctx.diff_fim_problem.path_after
    if path_before != path_after:
        path_label = widgets.Label(f"File: {path_before} -> {path_after}")
    else:
        path_label = widgets.Label(f"File: {path_before}")
    commit_label = widgets.Label(f"Commit: {problem_ctx.commit.summary()}")
    return widgets.VBox(children=[commit_label, path_label, tabs])


def display_diff_fim_viewer(
    result: GitDiffFimSamplerResult,
):
    """Build an ipywidgets UI for viewing a sequence of DiffFimProblems."""
    N = len(result.ctx_problems)

    def display_item(i: int, params: _UIParams):
        prob = result.ctx_problems[i]
        logs = result.sampler_logs[i] if result.sampler_logs else None
        return render_ctx_diff_fim_problem(prob, logs, params)

    display(render_item_browser_with_params(N, display_item))

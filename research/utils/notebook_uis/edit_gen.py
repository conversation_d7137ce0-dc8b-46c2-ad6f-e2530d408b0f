"""Notebook UIs for visualizing FIM and next edit problems."""

from __future__ import annotations

import difflib
import html
from typing import Sequence

import ipywidgets as widgets
from IPython.display import display

from base.caching.lru_cache import lru_cache
from base.datasets.hindsight_next_edit import NextEditIntermediateType
from base.diff_utils.changes import Modified
from base.diff_utils.diff_formatter import format_file_changes
from base.logging.secret_logging import Ignore<PERSON><PERSON><PERSON><PERSON>ger
from research.next_edits.next_edits_dataset import HindsightEditGenProblem
from research.next_edits.next_hunk_heuristic import (
    HeuristicStateRecorder,
    NextHunkHeuristic,
)
from base.diff_utils.diff_utils import File, compute_file_diff
from base.diff_utils.str_diff import precise_line_diff
from base.ranges.line_map import LineMap
from base.ranges.range_types import CharRange
from base.static_analysis.common import replace_str
from research.next_edits.edit_gen_sampler import EditGenProblem
from research.utils.notebook_uis.notebook_uis import (
    code_html,
    diff_html,
    generate_dataclass_html,
    render_item_browser,
    render_item_browser_with_params,
    render_repo_change,
    pprint_dict,
    _UIParams,
    show_edge_whitespaces,
)
from research.utils.repo_change_utils import RepoChange


def render_edit_gen_problem(
    edit_prob: EditGenProblem,
    params: _UIParams,
    problem_prompt: str | None = None,
) -> widgets.Widget:
    """Render an EditGenProblem as HTML."""
    prev_path = edit_prob.prev_path

    if prev_path:
        prev_code = edit_prob.repo_change.before_files[prev_path]
    else:
        prev_code = ""

    current_code = edit_prob.current_code
    select_start, select_stop = edit_prob.edit_region.to_tuple()
    selected_code = current_code[select_start:select_stop]
    ctx_start = max(0, select_start - params.max_prefix_chars)
    ctx_stop = min(len(current_code), select_stop + params.max_suffix_chars)
    current_context = "\n".join(
        [
            current_code[ctx_start:select_start],
            "~=" * 30 + " selection begin " + "~=" * 30,
            selected_code,
            "~=" * 30 + " selection end " + "~=" * 30,
            current_code[select_stop:ctx_stop],
        ]
    )

    prev_to_current_diff = precise_line_diff(prev_code, current_code)
    prev_ctx_range = prev_to_current_diff.inverted.before_range_to_after(
        CharRange(ctx_start, ctx_stop)
    )
    prev_context = prev_code[prev_ctx_range.to_slice()]

    context_diff_html = diff_html(precise_line_diff(prev_context, current_context))
    replace_code = edit_prob.output.replacement
    if selected_code != replace_code:
        path = str(edit_prob.current_path)
        output = code_html(
            compute_file_diff(
                before_file=File(path=path, contents=selected_code),
                after_file=File(path=path, contents=replace_code),
                use_smart_header=True,
                num_context_lines=3,
            )
        )
    else:
        output = "<b style='color: orange;'>(No change)</b>"
    prompt = "\n".join(
        [
            "<b style='color: gray'>Instruction</b><br>",
            code_html(edit_prob.instruction or "(empty)"),
            "<hr class='solid'>",
            "<b style='color: gray'>Additional changes to selection</b><br>",
            output,
            "<hr class='solid'>",
            "<b style='color: gray'>Current changes and selection</b><br>",
            context_diff_html,
        ]
    )

    main_prompt_ui = widgets.VBox(
        children=[
            widgets.HTML(prompt),
            widgets.HTML(
                "<style type='text/css'>" + "span {line-height: 0;}" + "</style>"
            ),
        ],
    )

    titles = ["Main Input"]
    children: list[widgets.Widget] = [main_prompt_ui]
    repo_change = edit_prob.repo_change
    titles.append(f"Repo Change ({len(repo_change.changed_files)})")
    children.append(render_repo_change(repo_change))
    if edit_prob.changed_final_files:
        current_to_future = edit_prob.current_to_future()
        titles.append(f"Current to Future ({len(current_to_future.changed_files)})")
        children.append(render_repo_change(current_to_future))
    if problem_prompt:
        titles.append("Prompt")
        children.append(widgets.HTML(code_html(problem_prompt)))
    if edit_prob.debug_info:
        titles.append("Debug Info")
        debug_info_str = pprint_dict(edit_prob.debug_info)
        children.append(widgets.HTML(code_html(debug_info_str)))
    tabs = widgets.Tab(children=children, titles=titles)

    current_path = edit_prob.current_path
    if prev_path is None:
        path_diff_str = f"New file: {current_path}"
    elif prev_path != current_path:
        path_diff_str = f"Renamed file: {prev_path} -> {current_path}"
    else:
        path_diff_str = f"file: {current_path}"
    path_label = widgets.Label(path_diff_str)

    commit = edit_prob.commit_meta
    commit_label = widgets.Label(f"Commit: {commit.repo_name}/{commit.summary()}")
    return widgets.VBox(children=[commit_label, path_label, tabs])


def display_edit_gen_viewer(
    problems: Sequence[EditGenProblem],
    problem_prompts: Sequence[str] | None = None,
) -> None:
    """Build an ipywidgets UI for viewing a sequence of EditGenProblems."""
    N = len(problems)

    def display_item(i: int, params: _UIParams):
        prompt = problem_prompts[i] if problem_prompts else None
        return render_edit_gen_problem(problems[i], params, problem_prompt=prompt)

    display(render_item_browser_with_params(N, display_item))


def render_file_changes(file_changes: Sequence[Modified[File]]) -> widgets.Widget:
    """Render a sequence of Modified[File] changes as HTML."""
    hint = "<b style='color: gray'>(Newest changes are at the bottom.)</b><br>"
    diff_str = format_file_changes(
        file_changes,
        diff_context_lines=5,
        diff_filter=lambda _: True,
    )
    return widgets.HTML(hint + code_html(diff_str))


def render_hindsight_edit_gen_problem(
    edit_prob: HindsightEditGenProblem,
    params: _UIParams,
    problem_prompt: str | None = None,
    edit_group_sizes: Sequence[int] = (1, 2),
) -> widgets.Widget:
    """Render an EditGenProblem as HTML."""
    current_path = edit_prob.current_path

    current_code = edit_prob.current_files[current_path]
    select_start, select_stop = edit_prob.edit_region.to_tuple()
    selected_code = current_code[select_start:select_stop]
    ctx_start = max(0, select_start - params.max_prefix_chars)
    ctx_stop = min(len(current_code), select_stop + params.max_suffix_chars)
    current_context = "\n".join(
        [
            current_code[ctx_start:select_start],
            "~=" * 30 + " selection begin " + "~=" * 30,
            selected_code,
            "~=" * 30 + " selection end " + "~=" * 30,
            current_code[select_stop:ctx_stop],
        ]
    )

    recent_changes = edit_prob.squashable_edits.convert_edit_events_to_modified_files(
        safe_logger=IgnoreAllLogger(),
        group_sizes=edit_group_sizes,
    )
    prev_code = current_code
    for change in recent_changes:
        if change.after.path == current_path:
            prev_code = change.before.contents
            break

    prev_to_current_diff = precise_line_diff(prev_code, current_code)
    prev_ctx_range = prev_to_current_diff.inverted.before_range_to_after(
        CharRange(ctx_start, ctx_stop)
    )
    prev_context = prev_code[prev_ctx_range.to_slice()]

    context_diff_html = diff_html(precise_line_diff(prev_context, current_context))
    replace_code = edit_prob.ground_truth.replacement
    if selected_code != replace_code:
        output = code_html(
            compute_file_diff(
                before_file=File(path=current_path, contents=selected_code),
                after_file=File(path=current_path, contents=replace_code),
                use_smart_header=True,
                num_context_lines=3,
            )
        )
    else:
        output = "<b style='color: orange;'>(No change)</b>"
    prompt = "\n".join(
        [
            "<b style='color: gray'>Additional changes to selection (ground truth)</b><br>",
            output,
            "<hr class='solid'>",
            "<b style='color: gray'>Recent changes in the current file</b><br>",
            context_diff_html,
        ]
    )

    main_prompt_ui = widgets.VBox(
        children=[
            widgets.HTML(prompt),
            widgets.HTML(
                "<style type='text/css'>" + "span {line-height: 0;}" + "</style>"
            ),
        ],
    )

    titles = ["Current File"]
    children: list[widgets.Widget] = [main_prompt_ui]
    titles.append(f"Recent changes ({len(recent_changes)})")
    children.append(render_file_changes(recent_changes))
    if problem_prompt:
        titles.append("Prompt")
        children.append(widgets.HTML(code_html(problem_prompt)))
    tabs = widgets.Tab(children=children, titles=titles)

    path_label = widgets.Label(f"file: {current_path}")

    request_id = edit_prob.origin.request_id
    request_time = edit_prob.origin.request_time.strftime("%Y-%m-%d %H:%M:%S.%f")
    commit_label = widgets.HTML(
        f'Request ID: <a href="{dogfood_ri_lint(request_id)}" target="_blank">{request_id}</a>'
        f"<br> Request time: {request_time}",
    )
    return widgets.VBox(children=[commit_label, path_label, tabs])


def dogfood_ri_lint(request_id: str) -> str:
    return f"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{request_id}"


def display_hindsight_edit_gen_viewer(
    problems: Sequence[HindsightEditGenProblem],
    problem_prompts: Sequence[str] | None = None,
) -> None:
    """Build an ipywidgets UI for viewing a sequence of HindsightEditGenProblems."""
    N = len(problems)

    def display_item(i: int, params: _UIParams):
        prompt = problem_prompts[i] if problem_prompts else None
        return render_hindsight_edit_gen_problem(
            problems[i], params, problem_prompt=prompt
        )

    display(render_item_browser_with_params(N, display_item))


def render_intermediate_problem(
    problem: NextEditIntermediateType,
) -> widgets.Widget:
    """Render a NextEditIntermediateType as HTML."""
    titles: list[str] = []
    children: list[widgets.Widget] = []

    # To enable indentation of nested dataclasses.
    styling = (
        "<style type='text/css'>details > *:not(summary){  margin-left: 2em; }</style>"
    )

    titles.append("Summary")
    user_id = None
    if problem.future_events:
        user_id = problem.future_events[0].user_id
    children.append(
        widgets.HTML(
            f"<div>Request ID: {problem.request.request_id}</div>"
            f"<div>Timestamp: {problem.request.timestamp}</div>"
            f"<div>User ID: {user_id}</div>"
            f"<div>Session ID: {problem.session_id}</div>"
            f"<div>Path: {problem.request.path}</div>"
            f"<div>Mode/Scope: {problem.request.mode} / {problem.request.scope}</div>"
        )
    )

    titles.append("Next edit request")
    children.append(widgets.HTML(styling + generate_dataclass_html(problem.request)))

    titles.append("Events")
    edited_files = set([event.file_path for event in problem.future_events])
    children.append(
        widgets.HTML(
            styling
            + f"<div>Edited files: {edited_files}</div>"  # Added from testing
            + generate_dataclass_html(problem.future_events)
        )
    )

    titles.append("Saved files")
    children.append(
        widgets.HTML(styling + generate_dataclass_html(problem.files_for_events))
    )

    tabs = widgets.Tab(children=children, titles=titles)

    commit_label = widgets.Label(f"Request ID: {problem.request.request_id}")
    return widgets.VBox(children=[commit_label, tabs])


def display_next_edit_intermediate_problems(
    problems: list[NextEditIntermediateType],
) -> None:
    """Build an ipywidgets UI for viewing NextEditIntermediateTypes."""
    N = len(problems)

    def display_item(i: int):
        return render_intermediate_problem(problems[i])

    display(render_item_browser(N, display_item))


def render_next_hunk_heuristic_state(
    files_at_request_time: dict[str, str],
    state: NextHunkHeuristic.InternalState,
    prev_state: NextHunkHeuristic.InternalState | None,
    selected_index: int | None = None,
):
    """Render a GroundTruthHeuristic.InternalState as HTML."""
    titles: list[str] = []
    children: list[widgets.Widget] = []
    cached_line_map = lru_cache(maxsize=256)(LineMap)

    # Display last edit information
    titles.append("Previous Edit")
    if (prev_edit := state.last_edit) and prev_state:
        before_text = prev_state.modified_files.get(
            prev_edit.file_path, files_at_request_time[prev_edit.file_path]
        )
        fake_after_text = replace_str(
            before_text, [(x.crange, x.text) for x in prev_edit.content_changes]
        )
        edit_time = prev_edit.time.strftime("%Y-%m-%d %H:%M:%S.%f")
        last_edit_html = f"(Previous Edit at {edit_time}) <br>"
        if state.edit_apply_error:
            last_edit_html += f"<b style='color: red;'> (Edit failed to apply: {state.edit_apply_error}) </b><br>"
        if before_text == fake_after_text:
            last_edit_html += "<br> (Edit has no effect) <br>"
        last_edit_html += render_diff_html(
            prev_edit.file_path, before_text, fake_after_text
        )
        children.append(widgets.HTML(last_edit_html))
    else:
        children.append(widgets.HTML("No previous edit."))

    titles.append(f"Hunk Sequence ({len(state.hunk_sequence)})")
    hunk_seq_html = list[str]()
    for i, (path, before_range, after_range) in enumerate(state.hunk_sequence):
        before_text = files_at_request_time[path]
        after_text = state.modified_files[path]
        fake_after_text = replace_str(
            before_text, [(before_range, after_text[after_range.to_slice()])]
        )

        before_lrange = cached_line_map(before_text).crange_to_lrange(before_range)
        after_lrange = cached_line_map(after_text).crange_to_lrange(after_range)
        hunk_seq_html.append(
            f"<h4>Hunk {i}: before_lines={str(before_lrange)}, after_lines={str(after_lrange)}"
            f", before_range={str(before_range)}, after_range={str(after_range)}</h4>"
        )
        hunk_seq_html.append(render_diff_html(path, before_text, fake_after_text))

    children.append(widgets.HTML("".join(hunk_seq_html)))

    titles.append(f"Edit Ranges ({len(state.edit_ranges_at_request_time)})")
    edit_ranges_html = list[str]()
    edit_ranges_html.append("Edit ranges at request time:")
    last_path = None
    for i, edit_group in enumerate(state.edit_ranges_at_request_time):
        if not edit_group:
            continue
        edit_ranges_html.append(f"<h3>Group {i}</h3>")
        for path, range in edit_group:
            if path != last_path:
                edit_ranges_html.append(f"<h4>Switch to file: {path}</h4>")
                last_path = path
            file_text = files_at_request_time[path]
            lmap = cached_line_map(file_text)
            lrange = lmap.crange_to_lrange(range)
            edit_ranges_html.append(f"Edit: LineRange={lrange}, CharRange={range} <br>")
    children.append(widgets.HTML("".join(edit_ranges_html)))

    titles.append(f"All Hunks ({len(state.hunks_so_far)})")
    hunks_so_far_html = list[str]()
    for i, (path, before_range, after_range) in enumerate(sorted(state.hunks_so_far)):
        before_text = files_at_request_time[path]
        after_text = state.modified_files[path]
        fake_after_text = replace_str(
            before_text, [(before_range, after_text[after_range.to_slice()])]
        )

        before_lrange = cached_line_map(before_text).crange_to_lrange(before_range)
        after_lrange = cached_line_map(after_text).crange_to_lrange(after_range)
        hunks_so_far_html.append(
            f"<h4>Hunk {i}: before_lines={str(before_lrange)}, after_lines={str(after_lrange)}"
            f", before_range={str(before_range)}, after_range={str(after_range)}</h4>"
        )
        hunks_so_far_html.append(render_diff_html(path, before_text, fake_after_text))
    children.append(widgets.HTML("".join(hunks_so_far_html)))

    # Create tabs for different sections
    tabs = widgets.Tab(children=children, titles=titles)
    tabs.selected_index = selected_index

    return widgets.VBox(children=[tabs]), tabs


def render_diff_html(
    file_path: str, before_text: str, after_text: str, num_context_lines: int = 5
) -> str:
    """Render a diff between two versions of a file."""
    diff_str = compute_file_diff(
        File(file_path, before_text),
        File(file_path, after_text),
        num_context_lines=num_context_lines,
        deduplicate_identical_paths=True,
    )
    diff_lines = diff_str.splitlines(keepends=True)
    new_lines = list[str]()
    for line in diff_lines:
        # show leading and trailing whitespace characters for changed lines
        if line.startswith("+") or line.startswith("-"):
            new_line = line[0] + show_edge_whitespaces(line[1:])
        else:
            new_line = line
        new_lines.append(new_line)

    return code_html("".join(new_lines), extra_style="line-height: 0;")


def render_next_hunk_heuristic_analyzer(
    analyzer: HeuristicStateRecorder,
) -> widgets.Widget:
    """Build an ipywidgets UI for viewing NextEditIntermediateTypes."""
    files_at_request_time = {
        file.path: file.contents for file in analyzer.datum.files_for_events.values()
    }

    last_tab: widgets.Tab | None = None

    def display_item(i: int):
        nonlocal last_tab
        if i == 0:
            # special step used to display the ground truth
            if not analyzer.ground_truths:
                return widgets.HTML(
                    f"Algorithm failed at step={len(analyzer.snapshots)-1}."
                )

            gt_html = list[str]()
            for j, (gt, is_stable) in enumerate(
                zip(analyzer.ground_truths, analyzer.ground_truths_are_stable)
            ):
                gt_html.append(f"<h3>Ground Truth Change {j}</h3>")
                if not is_stable:
                    gt_html.append(
                        "<b style='color: red;'> (This ground truth is not stable.)</b><br>"
                    )
                before_text = files_at_request_time[gt.path]
                fake_after_text = replace_str(
                    before_text, [(gt.before_crange, gt.after_text)]
                )
                diff_str = render_diff_html(gt.path, before_text, fake_after_text)
                gt_html.append(diff_str)
            ui = widgets.HTML("".join(gt_html))

        else:
            ui, tabs = render_next_hunk_heuristic_state(
                files_at_request_time,
                analyzer.snapshots[i],
                analyzer.snapshots[i - 1],
                selected_index=last_tab.selected_index if last_tab else 0,
            )
            last_tab = tabs
        return ui

    request_id = analyzer.datum.request.request_id
    user_id = (
        analyzer.datum.future_events[0].user_id
        if analyzer.datum.future_events
        else "Unknown"
    )
    request_link = dogfood_ri_lint(request_id)
    request_time = analyzer.datum.request.timestamp.strftime("%Y-%m-%d %H:%M:%S.%f")
    step_browser = render_item_browser(
        len(analyzer.snapshots), display_item, selector_label="Step:"
    )
    return widgets.VBox(
        children=[
            widgets.HTML(
                f'Request ID: <a href="{request_link}" target="_blank">{request_id}</a>'
                f"<br> {request_time} by {user_id}",
                layout=widgets.Layout(margin="0 0 0 50px"),
            ),
            step_browser,
        ]
    )

"""Notebook UI utils."""

from __future__ import annotations

import difflib
import html
from dataclasses import dataclass, is_dataclass
from typing import Callable, Optional

import ipywidgets as widgets
from typing_extensions import assert_never, override

from base.prompt_format_next_edit.gen_prompt_formatter import equal_modulo_spaces
from research.core.changes import get_after, get_before
from research.core.str_diff import AddedSpan, DeletedSpan, ModSpan, NoopSpan, StrDiff
from research.utils.inspect_indexed_dataset import escape_control_chars
from research.utils.repo_change_utils import RepoChange


def show_header(title: str, width: int = 30) -> str:
    return "~=" * width + f" {title} " + "~=" * width


default_font_size = "9pt"

HtmlStr = str


@dataclass
class _UIParams:
    max_prefix_chars: int
    max_suffix_chars: int
    max_middle_chars: int


def code_html(code: str, extra_style: str = "", font_size=default_font_size) -> HtmlStr:
    """Return an HTML widget with the given code."""
    style = f"font-size: {font_size}; font-family: monospace; white-space: pre;"
    escaped = html.escape(escape_control_chars(code))
    return f"<span style='{style}{extra_style}'>{escaped}</span>"


def diff_html(
    diff: StrDiff, add_color: str = "blue", delete_color: str = "red"
) -> HtmlStr:
    """Pretty-print the diff as an HTML str."""

    def add_style(text: str):
        return code_html(text, f"color: {add_color};")

    def del_style(text: str):
        return code_html(text, f"color: {delete_color};")

    def normal_style(text: str):
        return code_html(text)

    parts = list[HtmlStr]()

    before_code = diff.get_before()
    after_code = diff.get_after()
    if before_code != after_code and equal_modulo_spaces(before_code, after_code):
        parts.append(code_html("(Whitespace-only change)\n", "color: orange;"))

    for op in diff.spans:
        if isinstance(op, AddedSpan):
            parts.append(add_style(op.inserted))
        elif isinstance(op, DeletedSpan):
            parts.append(del_style(op.deleted))
        elif isinstance(op, ModSpan):
            parts.append(del_style(op.before))
            parts.append(add_style(op.after))
        elif isinstance(op, NoopSpan):
            parts.append(normal_style(op.text))
        else:
            assert_never(op)

    return "".join(parts)


def render_repo_change(repo_change: RepoChange) -> widgets.Widget:
    """Render a RepoChange as an Accordion.

    Each file change is shown as an accordion item.
    """
    titles = list[str]()
    files = list[widgets.HTML]()
    for file_change in repo_change.changed_files:
        if before_file := get_before(file_change):
            before_path, before_code = before_file
        else:
            before_path, before_code = None, ""
        if after_file := get_after(file_change):
            after_path, after_code = after_file
        else:
            after_path, after_code = None, ""
        diff_lines = difflib.unified_diff(
            before_code.splitlines(keepends=True),
            after_code.splitlines(keepends=True),
            n=5,
        )
        if before_path != after_path:
            file_title = f"Changed: {before_path} -> {after_path}\n"
        else:
            file_title = f"Changed: {before_path}\n"
        titles.append(file_title)
        files.append(widgets.HTML(code_html(file_title + "".join(diff_lines))))

    return widgets.Accordion(children=files, titles=titles)


def pprint_dict(d: dict, indent_spaces: int = 4) -> str:
    """Pretty-print nested dicts by adding indentations."""
    tab = " " * indent_spaces
    lines = list[str]()

    def rec_print(x: dict, level: int) -> None:
        for k, v in x.items():
            if isinstance(v, dict):
                lines.append(f"{tab * level}{k}:")
                rec_print(v, level + 1)
            else:
                lines.append(f"{tab * level}{k}: {repr(v)}")

    rec_print(d, 0)
    return "\n".join(lines)


def render_item_browser(
    n_items: int,
    item_display_fn: Callable[[int], widgets.Widget],
    selector_label: str = "Example",
    initial_item: int = 0,
) -> widgets.Widget:
    """Build an ipywidgets UI for viewing a sequence of NextEditIntermediateTypes."""
    example_selector = widgets.IntText(
        value=initial_item,
        min=0,
        max=n_items - 1,
        description=selector_label,
    )

    def inc_example(delta: int):
        ex_id = example_selector.value + delta
        ex_id = ex_id % n_items
        example_selector.value = ex_id

    next_button = widgets.Button(description="Next")
    prev_button = widgets.Button(description="Prev")
    next_button.on_click(lambda _: inc_example(1))
    prev_button.on_click(lambda _: inc_example(-1))

    main_output = widgets.VBox()

    def display_example(_):
        ex_id = example_selector.value
        ex_id = min(max(ex_id, 0), n_items - 1)
        main_output.children = [item_display_fn(ex_id)]

    for selector in [
        example_selector,
    ]:
        selector.observe(display_example, names="value")

    display_example(None)

    return widgets.VBox(
        [
            widgets.HBox([example_selector, prev_button, next_button]),
            main_output,
            widgets.HBox([example_selector, prev_button, next_button]),
        ]
    )


def render_item_browser_with_params(
    n_items: int,
    item_display_fn: Callable[[int, _UIParams], widgets.Widget],
) -> widgets.Widget:
    """Build an ipywidgets UI for viewing a sequence of DiffFimProblems."""
    example_selector = widgets.IntText(
        value=0,
        min=0,
        max=n_items - 1,
        description="Example:",
    )

    def inc_example(delta: int):
        ex_id = example_selector.value + delta
        ex_id = ex_id % n_items
        example_selector.value = ex_id

    next_button = widgets.Button(description="Next")
    prev_button = widgets.Button(description="Prev")
    next_button.on_click(lambda _: inc_example(1))
    prev_button.on_click(lambda _: inc_example(-1))

    prefix_len_selector = widgets.IntText(value=2000, description="Prefix len:")
    suffix_len_selector = widgets.IntText(value=1000, description="Suffix len:")
    middle_len_selector = widgets.IntText(value=1000, description="Middle len:")

    main_output = widgets.VBox()

    def display_example(_):
        ex_id = example_selector.value
        ex_id = min(max(ex_id, 0), n_items - 1)
        prefix_len = prefix_len_selector.value
        suffix_len = suffix_len_selector.value
        middle_len = middle_len_selector.value
        params = _UIParams(
            max_prefix_chars=prefix_len,
            max_suffix_chars=suffix_len,
            max_middle_chars=middle_len,
        )
        main_output.children = [item_display_fn(ex_id, params)]

    for selector in [
        example_selector,
        prefix_len_selector,
        suffix_len_selector,
        middle_len_selector,
    ]:
        selector.observe(display_example, names="value")

    display_example(None)

    return widgets.VBox(
        [
            widgets.HBox(
                [prefix_len_selector, suffix_len_selector, middle_len_selector],
                layout=widgets.Layout(width="100%"),
            ),
            widgets.HBox([example_selector, prev_button, next_button]),
            main_output,
            widgets.HBox([example_selector, prev_button, next_button]),
        ]
    )


def generate_dataclass_html(
    data,
    header: Optional[str] = None,
    text_limit: int = 500,
    display_limit: int = 50,
    min_dataclass_length: int = 50,
) -> str:
    """Generates HTML for dataclasses and lists of dataclasses.

    `header`: Optional classifier to print before the contents.
    `text_limit`: Class fields above this length are truncated for ease of reading (the full contents can be viewed by expanding).
    `display_limit`: Maximum number of characters of class field shown if exceeds text limit.
    `min_dataclass_length`: Dataclasses below this size are shown inline.
    """
    # Displaying long dictionaries and dataclasses
    if (is_dataclass(data) or isinstance(data, dict)) and len(
        str(data)
    ) >= min_dataclass_length:
        if isinstance(data, dict):
            fields = data.keys()
            values = data.values()
        else:
            fields = data.__dataclass_fields__
            values = [getattr(data, field) for field in fields]

        attributes = list[str]()
        for field, value in zip(fields, values):
            html_safe_field = html.escape(field)
            if isinstance(value, list) or is_dataclass(value):
                attributes.append(generate_dataclass_html(value, field))
            elif len(str(value)) > text_limit:
                attributes.append(
                    f"<details><summary>{html_safe_field}: {html.escape(str(value)[:display_limit])}...</summary><pre>{html.escape(str(value))}</pre></details>"
                )
            else:
                attributes.append(
                    f"<div>{html_safe_field}: {html.escape(str(value))}</div>"
                )
        attributes_str = "".join(attributes)
        if header:
            return f"<details><summary>{html.escape(header)}: {html.escape(type(data).__name__)}</summary>{attributes_str}</details>"
        else:
            return f"<details><summary>{html.escape(type(data).__name__)}</summary>{attributes_str}</details>"
    # Displaying lists
    elif isinstance(data, list):
        items = list[str]()
        for i, item in enumerate(data):
            items.append(generate_dataclass_html(item, str(i)))
        items_str = "".join(items)
        if header:
            return f"<details><summary>{html.escape(header)}: {html.escape(type(data).__name__)}</summary>{items_str}</details>"
        else:
            return f"<details><summary>{html.escape(type(data).__name__)}</summary>{items_str}</details>"
    # Displaying strings, integers, short dictionaries/dataclasses, etc...
    else:
        if header:
            return f"<div>{html.escape(header)}: {html.escape(str(data))}</div>"
        else:
            return html.escape(str(data))


def show_edge_whitespaces(text: str) -> str:
    """Explicitly show leading and trailing whitespace characters using unicode symbols."""
    lines = text.splitlines(keepends=True)
    result = list[str]()
    for line in lines:
        # Handle leading whitespace
        leading_space = line[: len(line) - len(line.lstrip())]
        content = line.lstrip()
        # Handle trailing whitespace
        trailing_space = content[len(content.rstrip()) :]
        content = content.rstrip()

        # Replace whitespace characters with symbols only in leading/trailing spaces
        marked_leading = leading_space.replace(" ", "·").replace("\t", "   →")
        marked_trailing = trailing_space.replace(" ", "·").replace("\t", "   →")

        result.append(f"{marked_leading}{content}{marked_trailing}")

    return "".join(result).replace("\r", "\\r")

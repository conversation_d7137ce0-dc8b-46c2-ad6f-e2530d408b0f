"""Utils to randomly transform import statements for model training."""

from __future__ import annotations

from dataclasses import dataclass
from random import Random
from typing import Callable, Iterable, Sequence, TypeVar

import tree_sitter as ts

from base.ranges import ByteRange
from base.static_analysis.common import LanguageID, assert_eq, decode_bytes, replace_str
from base.static_analysis.parsing import (
    TreeSitterParser,
    TsParsedFile,
    WhitespaceHandler,
)


@dataclass
class ImportStatement:
    """A language-agnostic representation of an import statement."""

    stmt: ts.Node
    """The import statement node."""

    symbols: Sequence[ts.Node]
    """The list of symbols that can be dropped by import dropout.

    This should never be empty since no symbols leads to dropping the entire statement.
    """

    def __post_init__(self):
        assert self.symbols, "Symbols cannot be empty."

    def __repr__(self):
        symbols_code = ", ".join(decode_bytes(n.text) for n in self.symbols)
        return f"ImportStatement(stmt={repr(decode_bytes(self.stmt.text))}, symbols=[{symbols_code}])"


def is_import_noising_supported(lang: LanguageID) -> bool:
    """Check if import noising is supported for the given language."""
    return lang in _lang_to_get_imports_fn


def find_all_imports(
    node: ts.Node, lang: LanguageID, max_rec_depth: int = 200
) -> Iterable[ImportStatement]:
    """Find all the import statements in the tree."""
    get_imports = _lang_to_get_imports_fn.get(lang)

    if get_imports is None:
        raise NotImplementedError(f"Import noising not implemented for: {lang}")

    def rec_find(node: ts.Node, depth: int) -> Iterable[ImportStatement]:
        if imports := get_imports(node):
            yield from imports
            return
        if depth > max_rec_depth:
            return
        for child in node.children:
            yield from rec_find(child, depth + 1)

    return rec_find(node, 0)


@dataclass
class ImportDropout:
    """Used to transform code by randomly dropping imported symbols."""

    dropout_rate: float
    """The probability of dropping out an imported symbol."""

    def transform(self, pfile: TsParsedFile, rng: Random) -> bytes:
        """Transform the tree by randomly dropping some imported symbols.

        Return the transformed code (as bytes).
        """
        all_imports = list(find_all_imports(pfile.ts_tree.root_node, pfile.lang))
        all_imports.sort(key=lambda i: i.stmt.start_byte)
        code_bytes = pfile.code_as_bytes
        ws = WhitespaceHandler(code_bytes)

        edits = list[tuple[ByteRange, bytes]]()

        for istmt in all_imports:
            to_delete = set[int]()
            for i in range(len(istmt.symbols)):
                if rng.random() < self.dropout_rate:
                    to_delete.add(i)
            if len(to_delete) == len(istmt.symbols):
                # delete the entire statement
                node = istmt.stmt
                brange = ByteRange(node.start_byte, node.end_byte)
                edits.append((ws.extend_range(brange), b""))
                continue
            # delete some symbols but keep the statement
            commas_to_delete = _compute_comma_deletes(to_delete, len(istmt.symbols))
            for i in sorted(to_delete):
                node = istmt.symbols[i]
                # It's not safe to eat trailing newlines after individual symbols
                brange = ByteRange(ws.move_start_left(node.start_byte), node.end_byte)
                edits.append((brange, b""))
            for i in sorted(commas_to_delete):
                # check if this comma is still needed
                if (comma := istmt.symbols[i].next_sibling) and comma.type == ",":
                    brange = ByteRange(comma.start_byte, comma.end_byte)
                    edits.append((ws.extend_range(brange), b""))
        new_code = replace_str(code_bytes, edits, allow_overlap=True)
        return new_code


@dataclass
class ImportShuffling:
    """Used to transform code by randomly shuffling imported symbols."""

    parser: TreeSitterParser
    """The tree sitter parser used to parse the source code."""

    shuffle_rate: float = 0.1
    """Each import statement has this probability of being randomly swapped."""

    def transform(
        self,
        pfile: TsParsedFile,
        rng: Random,
    ) -> bytes:
        """Transform the file by randomly shuffling import statements and symbols.

        Return the transformed code (as bytes).

        Note that instead of uniformly random shuffling, we sample near-sorted orders
        to make it harder for the model to tell whether the order is shuffled or not.
        This is to prevent the model from only predicting sorted imports if the
        context is clearly sorted.
        """

        def random_stmt_order(stmts: Sequence[ImportStatement]) -> Sequence[int]:
            """Randomly shuffle the order of the import statements."""
            return random_shuffle_order(rng, range(len(stmts)), self.shuffle_rate)

        def random_symbol_order(istmt: ImportStatement) -> Sequence[int]:
            """Randomly shuffle the order of the symbols in the import statement."""
            return random_shuffle_order(
                rng, range(len(istmt.symbols)), self.shuffle_rate
            )

        new_code = reorder_import_statements(
            pfile,
            random_stmt_order,
        )
        pfile = TsParsedFile.parse(pfile.path, pfile.lang, new_code, parser=self.parser)
        new_code = reorder_import_symbols(
            pfile,
            random_symbol_order,
        )
        return new_code


_T = TypeVar("_T")


def random_shuffle_order(
    rng: Random, xs: Sequence[_T], shuffle_rate: float
) -> list[_T]:
    """Randomly shuffle the items of the given sequence.

    Each item has `shuffle_rate` probability of being swapped with another random item.

    Example:
    >>> rng = Random(42)
    >>> for rate in [0.0, 0.1, 0.2, 0.5]:
    >>>     print(f"{rate=}:", random_shuffle_order(rng, range(10), rate))
    rate=0.0: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    rate=0.1: [0, 1, 3, 2, 4, 5, 6, 7, 8, 9]
    rate=0.2: [6, 1, 5, 0, 2, 4, 3, 7, 8, 9]
    rate=0.5: [8, 9, 2, 5, 4, 3, 6, 7, 0, 1]
    """
    new_order = list(xs)
    for i in range(len(new_order)):
        if rng.random() < shuffle_rate:
            j = rng.randint(0, len(new_order) - 1)
            new_order[i], new_order[j] = new_order[j], new_order[i]
    return new_order


def reorder_import_statements(
    pfile: TsParsedFile,
    order_fn: Callable[[Sequence[ImportStatement]], Sequence[int]],
) -> bytes:
    """Reorder the import statements according to `order_fn`.

    Return the transformed code (as bytes).
    """
    all_imports = list(find_all_imports(pfile.ts_tree.root_node, pfile.lang))
    all_imports.sort(key=lambda i: i.stmt.start_byte)
    code_bytes = pfile.code_as_bytes

    new_order = order_fn(all_imports)
    assert_eq(len(new_order), len(all_imports), lambda: f"{all_imports=}")

    edits = list[tuple[ByteRange, bytes]]()
    for i, j in enumerate(new_order):
        if i == j:
            continue
        old_import = all_imports[i]
        new_import = all_imports[j]
        old_range = ByteRange(old_import.stmt.start_byte, old_import.stmt.end_byte)
        new_range = ByteRange(new_import.stmt.start_byte, new_import.stmt.end_byte)
        edits.append((old_range, code_bytes[new_range.start : new_range.stop]))
    new_code = replace_str(code_bytes, edits, allow_overlap=True)
    return new_code


def reorder_import_symbols(
    pfile: TsParsedFile,
    order_fn: Callable[[ImportStatement], Sequence[int]],
) -> bytes:
    """Reorder the import symbols according to `order_fn`.

    Return the transformed code (as bytes).
    """
    all_imports = list(find_all_imports(pfile.ts_tree.root_node, pfile.lang))
    all_imports.sort(key=lambda i: i.stmt.start_byte)
    code_bytes = pfile.code_as_bytes

    edits = list[tuple[ByteRange, bytes]]()
    for istmt in all_imports:
        new_order = order_fn(istmt)
        for i, j in enumerate(new_order):
            if i == j:
                continue
            old_symbol = istmt.symbols[i]
            new_symbol = istmt.symbols[j]
            old_range = ByteRange(old_symbol.start_byte, old_symbol.end_byte)
            new_range = ByteRange(new_symbol.start_byte, new_symbol.end_byte)
            edits.append((old_range, code_bytes[new_range.start : new_range.stop]))
    new_code = replace_str(code_bytes, edits, allow_overlap=True)
    return new_code


def _compute_comma_deletes(delete_symbols: set[int], num_symbols: int) -> set[int]:
    """Compute the indices of commas to delete.

    We need to delete commas that are no longer needed after deleting some symbols.
    For example, if we delete a and c below
    ```
    import {a}, b, {c}
    ```
    we need to also delete the comma after a and b.
    Hence, we have _compute_comma_deletes({0, 2}, 3) = {0, 1}.
    """
    commas_to_delete = set[int]()
    # always delete trailing commas associated with the deleted symbols
    for i in range(num_symbols - 1):
        if i in delete_symbols:
            commas_to_delete.add(i)
    symbs_left = set(range(num_symbols)) - delete_symbols
    commas_left = set(range(num_symbols - 1)) - commas_to_delete
    last_symb = max(symbs_left)
    for i in commas_left:
        # delete dangling commas
        if i >= last_symb:
            commas_to_delete.add(i)
    return commas_to_delete


def _get_python_imports(node: ts.Node) -> Sequence[ImportStatement]:
    """Get all the python imported symbols in the given node."""
    if node.type == "import_statement":
        symbols = node.children_by_field_name("name")
    elif node.type == "import_from_statement":
        symbols = node.children_by_field_name("name")
    else:
        return ()

    if symbols:
        return (ImportStatement(stmt=node, symbols=symbols),)
    else:
        # treat the entire statement as a droppable symbol
        return (ImportStatement(stmt=node, symbols=(node,)),)


def _get_java_imports(node: ts.Node) -> Sequence[ImportStatement]:
    """Get all the java imported symbols in the given node."""
    if node.type == "import_declaration":
        # treat the entire statement as a droppable symbol
        return (ImportStatement(stmt=node, symbols=(node,)),)
    else:
        return ()


def _get_cpp_imports(node: ts.Node) -> Sequence[ImportStatement]:
    """Get all the cpp imported symbols in the given node."""
    if node.type == "preproc_include":
        # e.g., `#include "myHeaderFile.h"`
        if paths := node.children_by_field_name("path"):
            return (ImportStatement(stmt=node, symbols=tuple(paths)),)
    elif node.type == "using_declaration":
        # e.g., `using namespace std;`
        return (ImportStatement(stmt=node, symbols=(node,)),)
    return ()


def _get_typescript_imports(node: ts.Node) -> Sequence[ImportStatement]:
    """Get all the typescript imported symbols in the given node."""
    if node.type == "import_statement":
        clauses = tuple(
            c
            for n in node.named_children
            if n.type == "import_clause"
            for c in n.named_children
        )
        if (
            not clauses
            and (source := node.child_by_field_name("source"))
            and source.named_children
        ):
            return (ImportStatement(stmt=node, symbols=[source.named_children[0]]),)
        symbols = list[ts.Node]()
        for clause in clauses:
            if clause.type == "identifier":
                symbols.append(clause)
            elif clause.type == "named_imports":
                symbols.extend(clause.named_children)
            elif clause.type == "namespace_import":
                symbols.append(clause)
        if symbols:
            return (ImportStatement(stmt=node, symbols=symbols),)
        else:
            # treat the entire statement as a droppable symbol
            return (ImportStatement(stmt=node, symbols=(node,)),)
    else:
        return ()


def _get_go_imports(node: ts.Node) -> Sequence[ImportStatement]:
    """Get all the go imported symbols in the given node."""
    if node.type == "import_declaration":
        clauses = list[ts.Node]()
        for n in node.named_children:
            if n.type == "import_spec":
                clauses.append(n)
            elif n.type == "import_spec_list":
                clauses.extend(n.named_children)
        if clauses:
            return (ImportStatement(stmt=node, symbols=tuple(clauses)),)
        else:
            # treat the entire statement as a droppable symbol
            return (ImportStatement(stmt=node, symbols=(node,)),)
    else:
        return ()


def _get_rust_imports(node: ts.Node) -> Sequence[ImportStatement]:
    """Get all the Rust imported symbols in the given node."""
    if node.type == "use_declaration":
        clauses = list[ts.Node]()
        for n in node.named_children:
            if n.type in ("scoped_identifier", "use_wildcard"):
                clauses.append(n)
            elif n.type == "scoped_use_list":
                if name_list := n.child_by_field_name("list"):
                    clauses.extend(name_list.named_children)
        if clauses:
            return (ImportStatement(stmt=node, symbols=tuple(clauses)),)
        else:
            # treat the entire statement as a droppable symbol
            return (ImportStatement(stmt=node, symbols=(node,)),)
    else:
        return ()


_lang_to_get_imports_fn: dict[
    LanguageID, Callable[[ts.Node], Sequence[ImportStatement]]
] = {
    "python": _get_python_imports,
    "java": _get_java_imports,
    "cpp": _get_cpp_imports,
    "typescript": _get_typescript_imports,
    "javascript": _get_typescript_imports,
    "go": _get_go_imports,
    "rust": _get_rust_imports,
}

from unittest.mock import MagicMock
import pytest

from random import Random
from pathlib import Path

import tree_sitter as ts

from base.ranges.range_types import <PERSON><PERSON>R<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from base.static_analysis.parsing import SrcSpan
from research.fim.fim_augment import (
    FileAugmenter,
    FimRepr,
    FimSpan,
    ImportShufflingAugmenter,
    IndexRange,
    NodeEndpoints,
    find_valid_nodes,
)
from research.fim.fim_sampling import FimProblem, SkipOrOutput
from research.fim.import_noising import find_all_imports
from research.static_analysis.parsing import TsParsedFile


@pytest.fixture
def python_file():
    return TsParsedFile.parse(
        path=Path("foobar.py"),
        lang="python",
        code="""
import os
import sys

import foo_a
import foo_b
import foo_c
from foo_d import bar_a, bar_b, bar_c

def foo(x: int):
    return foo_a.foo(foo_b.foo(foo_c.foo(x)))

def bar(x: int):
    return bar_a(bar_b(bar_c(x)))
""",
    )


@pytest.fixture
def python_file_lstrip(python_file):
    return TsParsedFile.parse(
        path=Path("foobar2.py"), lang="python", code=python_file.code.lstrip()
    )


@pytest.fixture
def python_file_inner_imports():
    return TsParsedFile.parse(
        path=Path("inner_imports.py"),
        lang="python",
        code="""
from random import Random

def foo(x: int):
    rng = Random(42)
    if rng.random() < 0.5:
        import foo_a
        import foo_b
        import foo_c
        return foo_a.foo(foo_b.foo(foo_c.foo(x)))

    if rng.random() < 0.5:
        from foo_x import foo_a
        if rng.random() < 0.5:
            from foo_y import foo_b
            if rng.random() < 0.5:
                from foo_z import foo_c
                return foo_a.foo(foo_b.foo(foo_c.foo(x)))
            return foo_a.foo(foo_b.foo(x))
        return foo_a.foo(x)
    return x

def bar(x: int):
    from foo_d import bar_a, bar_b, bar_c
    return bar_a(bar_b(bar_c(x)))
""",
    )


@pytest.fixture(params=["foobar.py"])
def input_file(
    request, python_file, python_file_lstrip, python_file_inner_imports
) -> TsParsedFile:
    if request.param == "foobar.py":
        return python_file
    if request.param == "foobar2.py":
        return python_file_lstrip
    if request.param == "inner_imports.py":
        return python_file_inner_imports
    raise ValueError(f"Unknown parameter: {request.param}")


def test_noop(input_file):
    file_augmenter = FileAugmenter(import_dropout_prob=0.0, import_shuffling_prob=0.0)
    assert file_augmenter.noop_prob == 1.0
    assert file_augmenter.import_dropout_prob == 0.0
    assert file_augmenter.import_shuffling_prob == 0.0
    rng = Random(42)
    actual = file_augmenter.transform(input_file, rng)
    expected = input_file
    assert actual.path == expected.path
    assert actual.lang == expected.lang
    assert actual.code == expected.code


@pytest.mark.parametrize(
    "input_file, expected_code",
    [
        (
            "foobar.py",
            """

import foo_b
import foo_c
from foo_d import bar_a

def foo(x: int):
    return foo_a.foo(foo_b.foo(foo_c.foo(x)))

def bar(x: int):
    return bar_a(bar_b(bar_c(x)))
""",
        )
    ],
    indirect=["input_file"],
)
def test_import_dropout(input_file, expected_code):
    file_augmenter = FileAugmenter(
        import_dropout_prob=1.0, import_shuffling_prob=0.0, import_dropout_rate=0.5
    )
    assert file_augmenter.noop_prob == 0.0
    assert file_augmenter.import_dropout_prob == 1.0
    assert file_augmenter.import_shuffling_prob == 0.0
    rng = Random(42)
    actual = file_augmenter.transform(input_file, rng)
    expected = TsParsedFile.parse(
        input_file.path,
        input_file.lang,
        expected_code,
    )
    assert actual.path == expected.path
    assert actual.lang == expected.lang
    assert actual.code == expected.code


@pytest.mark.parametrize(
    "input_file, expected_code",
    [
        (
            "foobar.py",
            """
import foo_a
import sys

import os
from foo_d import bar_a, bar_c, bar_b
import foo_c
import foo_b

def foo(x: int):
    return foo_a.foo(foo_b.foo(foo_c.foo(x)))

def bar(x: int):
    return bar_a(bar_b(bar_c(x)))
""",
        )
    ],
    indirect=["input_file"],
)
def test_import_shuffling(input_file, expected_code):
    file_augmenter = FileAugmenter(
        import_dropout_prob=0.0, import_shuffling_prob=1.0, import_shuffling_rate=0.5
    )
    assert file_augmenter.noop_prob == 0.0
    assert file_augmenter.import_dropout_prob == 0.0
    assert file_augmenter.import_shuffling_prob == 1.0
    rng = Random(42)
    actual = file_augmenter.transform(input_file, rng)
    expected = TsParsedFile.parse(
        input_file.path,
        input_file.lang,
        expected_code,
    )
    assert actual.path == expected.path
    assert actual.lang == expected.lang
    assert actual.code == expected.code


@pytest.mark.parametrize(
    "fim_problem, code",
    [
        pytest.param(
            FimProblem(
                prefix=SrcSpan(CharRange(0, 1), "a"),
                suffix=SrcSpan(CharRange(2, 3), "c"),
                middle_spans=tuple(
                    [SkipOrOutput(SrcSpan(CharRange(1, 2), "b"), False)]
                ),
                file_path=Path("foobar.py"),
                middle_node_type="identifier",
                original_middle_code="b",
            ),
            "abc",
            id="basic",
        ),
        pytest.param(
            FimProblem(
                prefix=SrcSpan(CharRange(0, 2), "ab"),
                suffix=SrcSpan(CharRange(4, 6), "cef"),
                middle_spans=tuple(),
                file_path=Path("foobar.py"),
                middle_node_type="identifier",
                original_middle_code="cd",
            ),
            "abcdef",
            id="empty_range_leftovers",
        ),
        pytest.param(
            FimProblem(
                prefix=SrcSpan(CharRange(0, 0), ""),
                suffix=SrcSpan(CharRange(4, 4), "c"),
                middle_spans=tuple(
                    [SkipOrOutput(SrcSpan(CharRange(0, 2), "ab"), False)]
                ),
                file_path=Path("foobar.py"),
                middle_node_type="identifier",
                original_middle_code="abcd",
            ),
            "abcd",
            id="empty_prefix_suffix",
        ),
        pytest.param(
            FimProblem(
                prefix=SrcSpan(CharRange(0, 0), ""),
                suffix=SrcSpan(CharRange(4, 4), "cd"),
                middle_spans=tuple(
                    [SkipOrOutput(SrcSpan(CharRange(0, 2), "ab"), False)]
                ),
                file_path=Path("foobar.py"),
                middle_node_type="identifier",
                original_middle_code="abcd",
            ),
            "abcd",
            id="empty_suffix_adjacent_leftovers",
        ),
        pytest.param(
            FimProblem(
                prefix=SrcSpan(CharRange(0, 0), ""),
                suffix=SrcSpan(CharRange(0, 0), ""),
                middle_spans=tuple(),
                file_path=Path("empty.py"),
                middle_node_type="identifier",
                original_middle_code="",
            ),
            "",
            id="empty_everything",
        ),
        pytest.param(
            FimProblem(
                prefix=SrcSpan(CharRange(0, 2), "a("),
                suffix=SrcSpan(CharRange(8, 10), ")degh"),
                middle_spans=tuple(
                    [
                        SkipOrOutput(SrcSpan(CharRange(2, 3), "b"), False, False),
                        SkipOrOutput(SrcSpan(CharRange(3, 4), ")"), True, True),
                        SkipOrOutput(SrcSpan(CharRange(4, 5), "c"), False, True),
                    ]
                ),
                file_path=Path("xyz.cpp"),
                middle_node_type="xyz",
                original_middle_code="b)cdef",
            ),
            "a(b)cdefgh",
            id="basic_skip_tokens",
        ),
        pytest.param(
            FimProblem(
                prefix=SrcSpan(CharRange(0, 2), "a("),
                suffix=SrcSpan(CharRange(8, 10), ")degh"),
                middle_spans=tuple(
                    [
                        SkipOrOutput(SrcSpan(CharRange(2, 4), "bc"), False, False),
                        SkipOrOutput(SrcSpan(CharRange(4, 5), ")"), True, True),
                    ]
                ),
                file_path=Path("abc.js"),
                middle_node_type="abc",
                original_middle_code="bc)def",
            ),
            "a(bc)defgh",
            id="middle_ends_with_skip_pause",
        ),
        pytest.param(
            FimProblem(
                prefix=SrcSpan(CharRange(0, 4), "a((b"),
                suffix=SrcSpan(CharRange(10, 12), "))degh"),
                middle_spans=tuple(
                    [
                        SkipOrOutput(SrcSpan(CharRange(4, 5), ")"), True, False),
                        SkipOrOutput(SrcSpan(CharRange(5, 6), "c"), False, True),
                        SkipOrOutput(SrcSpan(CharRange(6, 7), ")"), True, False),
                    ]
                ),
                file_path=Path("abc.js"),
                middle_node_type="abc",
                original_middle_code=")c)def",
            ),
            "a((b)c)defgh",
            id="middle_starts_and_ends_with_skip_no_pause",
        ),
    ],
)
def test_fim_repr_roundtrip(fim_problem: FimProblem, code: str):
    fim_repr = FimRepr.from_cst_fim_problem(fim_problem, code)
    assert fim_repr.to_fim_problem() == fim_problem


@pytest.mark.parametrize(
    "fim_repr, expected",
    [
        pytest.param(
            FimRepr(
                code="abcdef",
                spans=[
                    FimSpan(CharRange(0, 1), True, False),
                    FimSpan(CharRange(1, 1), True, True),
                    FimSpan(CharRange(2, 3), True, False),
                    FimSpan(CharRange(3, 4), False, True),
                    FimSpan(CharRange(4, 5), False, False),
                    FimSpan(CharRange(5, 6), True, False),
                ],
                middle_node_type="abc",
                file_path=Path("abc.py"),
            ),
            IndexRange(1, 5),
            id="basic",
        ),
        pytest.param(
            FimRepr(
                code="abcdef",
                spans=[
                    FimSpan(CharRange(0, 1), True, False),
                    FimSpan(CharRange(1, 1), True, True),
                    FimSpan(CharRange(2, 3), True, False),
                    FimSpan(CharRange(3, 4), False, True),
                    FimSpan(CharRange(4, 5), False, False),
                ],
                middle_node_type="abc",
                file_path=Path("abc.py"),
            ),
            IndexRange(1, 5),
            id="no_suffix",
        ),
    ],
)
def test_get_middle_index_range(fim_repr: FimRepr, expected: IndexRange):
    assert fim_repr.get_middle_index_range() == expected


@pytest.mark.parametrize(
    "fim_span_indices, a_tuple, b_tuple, expected_indices",
    [
        pytest.param([0, 1, 2, 3, 4, 5], (0, 3), (3, 6), [3, 4, 5, 0, 1, 2]),
        pytest.param([0, 1, 2, 3, 4, 5], (3, 6), (0, 3), [3, 4, 5, 0, 1, 2]),
        pytest.param([0, 1, 2, 3, 4, 5], (3, 6), (1, 2), [0, 3, 4, 5, 2, 1]),
        pytest.param([0, 1, 2, 3, 4, 5], (1, 2), (3, 5), [0, 3, 4, 2, 1, 5]),
        pytest.param([0, 1, 2, 3, 4, 5], (0, 0), (3, 6), [3, 4, 5, 0, 1, 2]),
        pytest.param([0, 1, 2, 3, 4, 5], (1, 1), (3, 6), [0, 3, 4, 5, 1, 2]),
        pytest.param([], (0, 0), (0, 0), []),
    ],
)
def test_swap_spans(
    fim_span_indices: list[int],
    a_tuple: tuple[int, int],
    b_tuple: tuple[int, int],
    expected_indices: list[int],
):
    code = "abcdef"
    middle_node_type = "abc"
    path = Path("abc.py")
    all_fim_spans = [
        FimSpan(CharRange(0, 1), True, False),
        FimSpan(CharRange(1, 1), True, True),
        FimSpan(CharRange(2, 3), True, False),
        FimSpan(CharRange(3, 4), False, True),
        FimSpan(CharRange(4, 5), False, False),
        FimSpan(CharRange(5, 6), True, False),
    ]
    fim_spans = [all_fim_spans[i] for i in fim_span_indices]
    expected_spans = [all_fim_spans[i] for i in expected_indices]
    fim_repr = FimRepr(code, fim_spans, middle_node_type, path)
    expected = FimRepr(code, expected_spans, middle_node_type, path)

    a = IndexRange(*a_tuple)
    b = IndexRange(*b_tuple)
    fim_repr.swap_spans(a, b)
    assert fim_repr == expected


@pytest.mark.parametrize(
    "fim_span_cranges, crange_tuple, expected_iranges",
    [
        pytest.param([(1, 3), (0, 0), (4, 6)], (0, 6), [(0, 3)]),
        pytest.param([(1, 3), (0, 0), (4, 6)], (0, 0), [(1, 2)]),
        pytest.param([(1, 3), (0, 0), (4, 6)], (1, 6), [(0, 1), (2, 3)]),
        pytest.param([(1, 3), (0, 0), (4, 6)], (3, 6), [(2, 3)]),
        pytest.param([(1, 3), (0, 0), (4, 6)], (3, 3), []),
    ],
)
def test_find_spans_in(
    fim_span_cranges: list[tuple[int, int]],
    crange_tuple: tuple[int, int],
    expected_iranges: list[tuple[int, int]],
):
    code = "abcdef"
    middle_node_type = "abc"
    path = Path("abc.py")
    fim_spans = [FimSpan(CharRange(a, b), True, False) for a, b in fim_span_cranges]
    crange = CharRange(*crange_tuple)
    expected = [IndexRange(a, b) for a, b in expected_iranges]
    fim_repr = FimRepr(code, fim_spans.copy(), middle_node_type, path)
    assert fim_repr.find_spans_in(crange) == expected
    assert fim_repr == FimRepr(code, fim_spans, middle_node_type, path)


@pytest.mark.parametrize(
    "fim_span_cranges, char_index, expected_iranges",
    [
        pytest.param([(1, 3), (0, 0), (4, 6)], 3, [(1, 1)]),
        pytest.param([(1, 3), (0, 0), (4, 6)], 1, [(0, 0)]),
        pytest.param([(1, 3), (0, 0), (4, 6)], 6, [(3, 3)]),
        pytest.param([(1, 3), (0, 0), (4, 6)], 0, [(1, 1), (2, 2)]),
        pytest.param([(1, 3), (3, 5), (5, 6)], 3, [(1, 1)]),
        pytest.param([(1, 3), (3, 5), (3, 4)], 3, [(1, 1), (2, 2)]),
    ],
)
def test_find_insertions(
    fim_span_cranges: list[tuple[int, int]],
    char_index: int,
    expected_iranges: list[tuple[int, int]],
):
    code = "abcdef"
    middle_node_type = "abc"
    path = Path("abc.py")
    fim_spans = [FimSpan(CharRange(a, b), True, False) for a, b in fim_span_cranges]
    expected = [IndexRange(a, b) for a, b in expected_iranges]
    fim_repr = FimRepr(code, fim_spans.copy(), middle_node_type, path)
    assert fim_repr.find_insertions(char_index) == expected
    assert fim_repr == FimRepr(code, fim_spans, middle_node_type, path)


@pytest.mark.parametrize(
    "fim_spans, cut_range, expected",
    [
        pytest.param(
            [
                FimSpan(CharRange(1, 6), True, True),
                FimSpan(CharRange(1, 6), False, False),
                FimSpan(CharRange(0, 5), False, True),
                FimSpan(CharRange(2, 6), False, True),
                FimSpan(CharRange(3, 4), True, False),
                FimSpan(CharRange(3, 3), False, False),
            ],
            CharRange(2, 5),
            [
                FimSpan(CharRange(1, 2), True, False),
                FimSpan(CharRange(2, 5), True, False),
                FimSpan(CharRange(5, 6), True, True),
                FimSpan(CharRange(1, 2), False, False),
                FimSpan(CharRange(2, 5), False, False),
                FimSpan(CharRange(5, 6), False, False),
                FimSpan(CharRange(0, 2), False, False),
                FimSpan(CharRange(2, 5), False, True),
                FimSpan(CharRange(2, 5), False, False),
                FimSpan(CharRange(5, 6), False, True),
                FimSpan(CharRange(3, 4), True, False),
                FimSpan(CharRange(3, 3), False, False),
            ],
        ),
        pytest.param(
            [
                FimSpan(CharRange(1, 6), True, True),
                FimSpan(CharRange(1, 6), False, False),
                FimSpan(CharRange(2, 4), False, True),
                FimSpan(CharRange(2, 3), False, True),
                FimSpan(CharRange(3, 4), True, False),
                FimSpan(CharRange(3, 3), False, False),
            ],
            CharRange(3, 3),
            [
                FimSpan(CharRange(1, 3), True, False),
                FimSpan(CharRange(3, 6), True, True),
                FimSpan(CharRange(1, 3), False, False),
                FimSpan(CharRange(3, 6), False, False),
                FimSpan(CharRange(2, 3), False, False),
                FimSpan(CharRange(3, 4), False, True),
                FimSpan(CharRange(2, 3), False, True),
                FimSpan(CharRange(3, 4), True, False),
                FimSpan(CharRange(3, 3), False, False),
            ],
        ),
    ],
)
def test_cut_spans(
    fim_spans: list[FimSpan], cut_range: CharRange, expected: list[FimSpan]
):
    code = "abcdef"
    middle_node_type = "abc"
    path = Path("abc.py")
    fim_repr = FimRepr(code, fim_spans, middle_node_type, path)
    fim_repr.cut_spans(cut_range)
    expected_repr = FimRepr(code, expected, middle_node_type, path)
    assert fim_repr == expected_repr


@pytest.mark.parametrize(
    "fim_span_cranges, crange_tuples, expected",
    [
        pytest.param(
            [(1, 3), (0, 0), (4, 5)],
            [(1, 3), (1, 2), (4, 4), (0, 0), (3, 4), (6, 6), (0, 1)],
            [True, True, True, True, False, False, False],
        ),
        pytest.param([(0, 4), (2, 6)], [(1, 5), (2, 4)], [False, True]),
    ],
)
def test_spans_contain(
    fim_span_cranges: list[tuple[int, int]],
    crange_tuples: list[tuple[int, int]],
    expected: list[bool],
):
    code = "abcdef"
    middle_node_type = "abc"
    path = Path("abc.py")
    fim_spans = [FimSpan(CharRange(a, b), True, False) for a, b in fim_span_cranges]
    cranges = [CharRange(a, b) for a, b in crange_tuples]
    fim_repr = FimRepr(code, fim_spans, middle_node_type, path)
    assert fim_repr.spans_contain(cranges) == expected


@pytest.mark.parametrize(
    "input_file, brange_tuple, is_valid",
    [
        pytest.param("foobar.py", (2, 3), True, id="inside_import"),
        pytest.param("foobar.py", (1, 10), True, id="full_import"),
        pytest.param("foobar.py", (0, 101), True, id="full_imports"),
        pytest.param("foobar.py", (0, 1), True, id="intra_import_whitespace"),
        pytest.param("foobar.py", (99, 101), True, id="top_level_whitespace"),
        pytest.param("foobar.py", (102, 102), True, id="point_range"),
        pytest.param("foobar.py", (101, 102), False, id="non_import"),
        pytest.param("foobar.py", (0, 102), False, id="import_and_non_import"),
        pytest.param("foobar.py", (104, 105), False, id="inner_whitespace"),
        pytest.param("inner_imports.py", (92, 164), True, id="full_inner_import"),
        pytest.param("inner_imports.py", (367, 424), True, id="full_innermost_import"),
        pytest.param("inner_imports.py", (345, 347), False, id="inner_non_import"),
    ],
    indirect=["input_file"],
)
def test_find_valid_nodes_imports(
    input_file: TsParsedFile, brange_tuple: tuple[int, int], is_valid: bool
):
    brange = ByteRange(*brange_tuple)
    imports = list(find_all_imports(input_file.ts_tree.root_node, input_file.lang))
    import_nodes = [import_.stmt for import_ in imports]
    valid_nodes = find_valid_nodes(brange, input_file, import_nodes)

    # TODO(jeff): Imports can't contain each other. This simplifies the test because
    # there is only one possible covering, but we don't currently test cases where
    # there are multiple coverings and we want the smallest.
    overlapping_imports = [
        import_node
        for import_node in import_nodes
        if ByteRange(import_node.start_byte, import_node.end_byte).overlaps(brange)
    ]

    expected = overlapping_imports if is_valid else None
    assert valid_nodes == expected


@pytest.mark.parametrize(
    "input_file, byte, expected",
    [
        pytest.param("foobar.py", 5, 8, id="inner"),
        pytest.param("foobar.py", 118, 122, id="whitespace"),
        pytest.param("foobar.py", 8, 8, id="start"),
        pytest.param("foobar.py", 0, 0, id="first"),
        pytest.param("foobar.py", 216, 216, id="last"),
        pytest.param("foobar.py", 215, 216, id="one_before_last"),
    ],
    indirect=["input_file"],
)
def test_next_start_byte(input_file: TsParsedFile, byte: int, expected: int):
    endpoints = NodeEndpoints.from_pfile(input_file)
    assert endpoints.next_start(byte) == expected


@pytest.mark.parametrize(
    "input_file, char, expected",
    [
        pytest.param("foobar.py", 14, 10, id="inner"),
        pytest.param("foobar.py", 118, 117, id="whitespace"),
        pytest.param("foobar.py", 10, 10, id="end"),
        pytest.param("foobar.py", 0, 0, id="first"),
        pytest.param("foobar.py", 216, 216, id="last"),
        pytest.param("foobar.py", 1, 0, id="one_after_first"),
    ],
    indirect=["input_file"],
)
def test_prev_end(input_file: TsParsedFile, char: int, expected: int):
    endpoints = NodeEndpoints.from_pfile(input_file)
    assert endpoints.prev_end(char) == expected


@pytest.mark.parametrize(
    "input_file, brange_tuple, expected_tuple",
    [
        pytest.param("foobar.py", (118, 119), (117, 122), id="whitespace"),
        pytest.param("foobar.py", (118, 118), (117, 122), id="whitespace_point_range"),
        pytest.param("foobar.py", (101, 117), (99, 122), id="node"),
        pytest.param("foobar.py", (96, 96), (93, 101), id="node_point_range"),
        pytest.param("foobar.py", (0, 216), (0, 216), id="whole_file"),
        pytest.param("foobar.py", (1, 215), (0, 216), id="almost_full_file"),
    ],
    indirect=["input_file"],
)
def test_expand_range(
    input_file: TsParsedFile,
    brange_tuple: tuple[int, int],
    expected_tuple: tuple[int, int],
):
    brange = ByteRange(*brange_tuple)
    expected = ByteRange(*expected_tuple)
    endpoints = NodeEndpoints.from_pfile(input_file)
    assert endpoints.expand_range(brange) == expected


FOOBAR_PROBLEM_SPANS = [
    [
        FimSpan(CharRange(0, 38), True, False),
        FimSpan(CharRange(38, 42), False, True),
        FimSpan(CharRange(42, 48), False, False),
        FimSpan(CharRange(48, 216), True, False),
    ],
]

FOOBAR2_PROBLEM_SPANS = [
    [
        FimSpan(
            CharRange(max(span.crange.start - 1, 0), span.crange.stop - 1),
            span.skipped,
            span.pause_at_end,
        )
        for span in problem_spans
    ]
    for problem_spans in FOOBAR_PROBLEM_SPANS
]


@pytest.mark.parametrize(
    "input_file, spans, choices, choice_index, prefix, suffix",
    [
        pytest.param(
            "foobar.py",
            FOOBAR_PROBLEM_SPANS[0],
            6,
            0,
            "\nim",
            """
import os
import sys

import foo_a
import foo_c
from foo_d import bar_a, bar_b, bar_c

def foo(x: int):
    return foo_a.foo(foo_b.foo(foo_c.foo(x)))

def bar(x: int):
    return bar_a(bar_b(bar_c(x)))
""",
            id="basic_first",
        ),
        pytest.param(
            "foobar.py",
            FOOBAR_PROBLEM_SPANS[0],
            6,
            5,
            """
import os
import sys

import foo_a
import foo_c
from foo_d import bar_a, bar_b, bar_c
im""",
            """

def foo(x: int):
    return foo_a.foo(foo_b.foo(foo_c.foo(x)))

def bar(x: int):
    return bar_a(bar_b(bar_c(x)))
""",
            id="basic_last",
        ),
        pytest.param(
            "foobar.py",
            FOOBAR_PROBLEM_SPANS[0],
            6,
            2,
            """
import os
import sys
im""",
            """

import foo_a
import foo_c
from foo_d import bar_a, bar_b, bar_c

def foo(x: int):
    return foo_a.foo(foo_b.foo(foo_c.foo(x)))

def bar(x: int):
    return bar_a(bar_b(bar_c(x)))
""",
            id="basic",
        ),
        pytest.param(
            "foobar2.py",
            FOOBAR2_PROBLEM_SPANS[0],
            5,
            1,
            """import os
import sys
im""",
            """

import foo_a
import foo_c
from foo_d import bar_a, bar_b, bar_c

def foo(x: int):
    return foo_a.foo(foo_b.foo(foo_c.foo(x)))

def bar(x: int):
    return bar_a(bar_b(bar_c(x)))
""",
            id="basic_lstrip",
        ),
    ],
    indirect=["input_file"],
)
def test_import_shuffling_augmenter(
    input_file: TsParsedFile,
    spans: list[FimSpan],
    choices: int,
    choice_index: int,
    prefix: str,
    suffix: str,
):
    middle_node_type = "import_statement"
    fim_repr = FimRepr(input_file.code, spans, middle_node_type, input_file.path)
    fim_prob = fim_repr.to_fim_problem()
    # Check that the problem is valid.
    assert (
        FimRepr.from_cst_fim_problem(fim_prob, input_file.code).to_fim_problem()
        == fim_prob
    )

    augmenter = ImportShufflingAugmenter()
    rng = MagicMock(Random)
    rng.choice.side_effect = lambda x: x[min(choice_index, len(x) - 1)]
    result = augmenter.transform(fim_prob, input_file, rng)
    rng.choice.assert_called_once()
    assert len(rng.choice.call_args.args[0]) == choices

    assert result is not None
    assert result.middle_spans == fim_prob.middle_spans
    assert result.prefix.code == prefix
    assert result.suffix.code == suffix
    assert result.middle_node_type == fim_prob.middle_node_type


@pytest.mark.parametrize(
    "input_file, spans, choices, success",
    [
        pytest.param("foobar.py", FOOBAR_PROBLEM_SPANS[0], 6, True),
        pytest.param(
            "foobar.py",
            [
                FimSpan(CharRange(0, 7), False, True),
                FimSpan(CharRange(7, 8), False, False),
                FimSpan(CharRange(8, 23), True, False),
                FimSpan(CharRange(36, 216), True, False),
            ],
            4,
            True,
        ),
        pytest.param(
            "foobar.py",
            [
                FimSpan(CharRange(0, 25), True, False),
                FimSpan(CharRange(25, 29), False, True),
                FimSpan(CharRange(29, 35), False, False),
                FimSpan(CharRange(35, 49), True, False),
                FimSpan(CharRange(49, 216), True, False),
            ],
            1,
            False,
        ),
        pytest.param(
            "foobar.py",
            [
                FimSpan(CharRange(0, 99), True, False),
                FimSpan(CharRange(99, 101), False, False),
                FimSpan(CharRange(101, 216), True, False),
            ],
            1,
            False,
        ),
        pytest.param(
            "inner_imports.py",
            [
                FimSpan(CharRange(0, 92), True, False),
                FimSpan(CharRange(92, 112), False, False),
                FimSpan(CharRange(112, 164), True, False),
                FimSpan(CharRange(211, 644), True, False),
            ],
            2,
            True,
        ),
        pytest.param(
            "inner_imports.py",
            [
                FimSpan(CharRange(0, 125), True, False),
                FimSpan(CharRange(125, 133), False, False),
                FimSpan(CharRange(133, 644), True, False),
            ],
            2,
            True,
        ),
        pytest.param(
            "inner_imports.py",
            [
                FimSpan(CharRange(0, 164), True, False),
                FimSpan(CharRange(164, 170), False, False),
                FimSpan(CharRange(170, 644), True, False),
            ],
            1,
            False,
        ),
    ],
    indirect=["input_file"],
)
def test_import_shuffling_augmenter_sanity_check(
    input_file: TsParsedFile, spans: list[FimSpan], choices: int, success: bool
):
    middle_node_type = "import_statement"
    fim_repr = FimRepr(input_file.code, spans, middle_node_type, input_file.path)
    fim_prob = fim_repr.to_fim_problem()
    # Check that the problem is valid.
    assert (
        FimRepr.from_cst_fim_problem(fim_prob, input_file.code).to_fim_problem()
        == fim_prob
    )
    augmenter = ImportShufflingAugmenter()

    assert choices > 0, "must have at least one choice"
    results = []
    for choice_index in range(choices):
        rng = MagicMock(Random)
        rng.choice.side_effect = lambda x: x[min(choice_index, len(x) - 1)]
        result = augmenter.transform(fim_prob, input_file, rng)
        if success:
            rng.choice.assert_called_once()
            assert len(rng.choice.call_args.args[0]) == choices

        results.append(result)

    if not success:
        for result in results:
            assert result is None
        return

    # Sanity check that result middles are the same, and the same characters.
    fim_chars = sorted(fim_prob.prefix.code + fim_prob.suffix.code)
    for result in results:
        assert result is not None
        assert result.middle_spans == fim_prob.middle_spans
        assert result.middle_node_type == fim_prob.middle_node_type
        result_chars = sorted(result.prefix.code + result.suffix.code)
        assert result_chars == fim_chars

    # Sanity check that prefixes/suffix lengths are strictly increasing/decreasing.
    # Also implicitly checks that the results are all different.
    for prev, next in zip(results[:-1], results[1:]):
        assert len(prev.prefix.code) < len(next.prefix.code)
        assert len(prev.suffix.code) > len(next.suffix.code)

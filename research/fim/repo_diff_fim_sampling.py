"""Utilities to sample diff-fim data from Git repos."""

from __future__ import annotations

import logging
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Callable, Sequence, Tuple

from pyrsistent import PVector as PyrVector
from pyrsistent import pvector as pyr_vector
from tqdm.auto import tqdm

from base.static_analysis.common import guess_lang_from_fp, LanguageID
from research.core.changes import Changed, Deleted, Modified
from research.fim.diff_fim_sampling import DiffFim<PERSON>roblem, DiffFimSampler
from research.fim.fim_sampling import FimSamplingFailed
from research.utils.repo_change_utils import (
    CommitMeta,
    FileTuple,
    RepoChange,
    get_commit_history,
    iterate_repo_history,
)


@dataclass
class DiffFimProblemWithContext:
    """A diff fim problem with retrieval context and commit info."""

    diff_fim_problem: DiffFimProblem
    """A diff FIM problem."""

    retrieval_context: RepoChange
    """A repo change from which the diff FIM problem is created."""

    commit: CommitMeta
    """The commit of the repo change."""


@dataclass
class GitDiffFimSamplerResult:
    """The result of sampling diff FIM problems from a Git repo.

    Note that `ctx_problems` follows the commit order, and adjacent problems will have
    very similar retrieval contexts, so performing incremental retrieval index update
    between the problems should be efficient.
    """

    ctx_problems: Sequence[DiffFimProblemWithContext]
    """The sampled diff FIM problems."""

    sampler_logs: Sequence[list[str]]
    """The sampler logs of each problem."""

    errors: Sequence[FimSamplingFailed]
    """The errors encountered during sampling."""


def _default_is_source_file(path: Path) -> bool:
    """Check if the given path is a source file."""
    return guess_lang_from_fp(path) is not None


@dataclass
class GitDiffFimSampler:
    """Utility class to sample training data from Git repos."""

    sampler: DiffFimSampler
    """The sampler to sample diff FIM problems."""

    max_probs_per_commit: int | None = None
    """The maximum number of diff FIM problems to sample from each commit."""

    is_source_file: Callable[[Path], bool] = _default_is_source_file
    """Checks if the given path should be considered as source file."""

    @property
    def rng(self):
        """The rng used for sampling."""
        return self.sampler.rng

    def sample_from_commits(
        self,
        project_dir: Path,
        commits: Sequence[CommitMeta] | int,
        record_logs: bool = False,
        silent: bool = False,
        time_limit_seconds: float | None = None,
        max_workers: int = 1,
    ) -> GitDiffFimSamplerResult:
        """Sample contextualized diff FIM problems from the commits.

        Args:
            project_dir: a directory containing only the .git file.
            commits: a list of continuous commits to sample from. If a number `n` is\
                given, will sample from the last `n` commits.
            record_logs: whether to record sampler logs.
            silent: If false, will print out progress.
            time_limit_seconds: time limit in seconds.
            max_workers: the max number of workers to speed up git operations.
        """
        start_time = time.time()
        sampled_problems = list[DiffFimProblemWithContext]()
        sampler_logs = list[list[str]]()
        sampling_errors = list[FimSamplingFailed]()
        repo_result = GitDiffFimSamplerResult(
            sampled_problems, sampler_logs, sampling_errors
        )
        if isinstance(commits, int):
            commits = get_commit_history(project_dir, commits)

        def has_timeouted(step):
            if time_limit_seconds and (time.time() - start_time > time_limit_seconds):
                logging.warning(
                    f"_edits_from_commit_history timed out for {project_dir} "
                    f"({time_limit_seconds=})."
                    f"Partial results ({step}/{len(commits)-1}) will be returned."
                )
                return True
            else:
                return False

        repo_changes = iterate_repo_history(
            project_dir, commits, self.is_source_file, silent, max_workers=max_workers
        )
        for step, (commit, repo_change) in tqdm(
            enumerate(zip(commits[1:], repo_changes)),
            total=len(commits) - 1,
            smoothing=0,
            desc="processing commits",
            disable=silent,
            unit="commit",
        ):
            if has_timeouted(step):
                return repo_result

            # pick a subset of changed files to sample FIM problems
            candidate_changes: Sequence[Tuple[Any, LanguageID]] = [
                (c, lang)
                for c in repo_change.changed_files
                if not isinstance(c, Deleted)  # can't sample from a deleted file
                and (lang := guess_lang_from_fp(c.after[0]))
            ]
            self.rng.shuffle(candidate_changes)
            candidate_changes = candidate_changes[: self.max_probs_per_commit]
            changed_files: PyrVector[Changed[FileTuple]] = pyr_vector()
            before_files = repo_change.before_files
            current_files = repo_change.before_files

            for file_change, lang in candidate_changes:
                if has_timeouted(step):
                    return repo_result

                path, content = file_change.after
                if isinstance(file_change, Modified):
                    old_path, old_content = file_change.before
                else:
                    old_path, old_content = path, ""

                # Update the retrieval context. Note that we don't include changes of
                # the current file into the context yet.
                current_files = current_files.discard(old_path)
                retrieval_context = RepoChange(
                    before_files, current_files, changed_files
                )

                # sample a diff FIM problem
                logs = list[str]() if record_logs else None
                sampled = self.sampler.sample_diff_fim(
                    (old_path, old_content),
                    (path, content),
                    lang,
                    logs=logs,
                )
                if isinstance(sampled, FimSamplingFailed):
                    sampled.details += f"\ncommit: {commit.summary()}"
                    sampling_errors.append(sampled)
                else:
                    ctx_prob = DiffFimProblemWithContext(
                        sampled, retrieval_context, commit
                    )
                    sampled_problems.append(ctx_prob)
                if logs is not None:
                    sampler_logs.append(logs)
                current_files = current_files.set(path, content)
                changed_files = changed_files.append(file_change)

        return repo_result

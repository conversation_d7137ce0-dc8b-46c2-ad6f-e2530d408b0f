"""Implements the `PreTrainingFIM` class to apply FIM transformations to samples for pretraining."""

from dataclasses import dataclass
from typing import Iterable, Optional, Sequence

import numpy as np

from base.tokenizers.tiktoken_starcoder_tokenizer import StarCoderSpecialTokens
from base.tokenizers.tokenizer import FimSpecialTokens, Tokenizer


@dataclass
class PreTrainingFIM:
    """Apply FIM transformation to samples for pretraining."""

    fim_rate: float
    """The rate of FIM transformation to apply to the samples."""

    fim_spm_rate: float
    """The rate of FIM SPM transformation to apply to the samples."""

    tokenizer: Tokenizer
    """The tokenizer to use for FIM transformation, which is used for production with better performance."""

    def __post_init__(self):
        if self.fim_rate < 0 or self.fim_rate > 1:
            raise ValueError("FIM rate must be a probability 0 <= rate <= 1")
        if self.fim_spm_rate < 0 or self.fim_spm_rate > 1:
            raise ValueError("FIM SPM rate must be a probability 0 <= rate <= 1")

        if isinstance(self.tokenizer, Tokenizer) and isinstance(
            self.tokenizer.special_tokens, FimSpecialTokens
        ):
            special_tokens = self.tokenizer.special_tokens
            self.prefix_token_id = special_tokens.fim_prefix
            self.middle_token_id = special_tokens.fim_middle
            self.suffix_token_id = special_tokens.fim_suffix
            if isinstance(special_tokens, StarCoderSpecialTokens):
                self.padding_token_id = special_tokens.fim_pad
            else:
                self.padding_token_id = special_tokens.padding
            self.eod_token_id = special_tokens.eos
            try:
                self.bos_token_id: list[int] = list(
                    getattr(special_tokens, "begin_sequence", list())
                )
            except ValueError:
                # Starcoder raises ValueError when accessing `begin_sequence`
                self.bos_token_id: list[int] = []
            print("self.bos_token_id = {}".format(self.bos_token_id))
            self.tokenize = self.tokenizer.tokenize_unsafe
            self.detokenize = self.tokenizer.detokenize
        else:
            raise ValueError(f"Unknown tokenizer type: {type(self.tokenizer)}")

        # Initialize the random number generator
        # It will be reset at each call to `apply_on_samples` based on the given seed
        self.np_rng = np.random.RandomState()

    def apply_on_doc_stream(
        self,
        stream: Iterable[np.ndarray],
        sequence_length: int,
        seed: Optional[int] = None,
        tail_length: int = 1000,  # pad if the space left is smaller than this and we cannot fit another document.
    ):
        """Document level FIM.

        See DeepSeek Coder paper section 3.1 (https://arxiv.org/pdf/2401.14196.pdf)
        "We implement the Fill-in-the-Middle (FIM) method at the document level before the
        packing process, as proposed in the original work by Bavarian et al. (2022)"
        (https://arxiv.org/pdf/2207.14255.pdf)

        Process:
        The input is a stream of tokenized documents.
        FIM transformation is independently applied to each document.
        They are then concatenated together and cut into samples of the given
        sequence length.

        The alternative implemented in `apply_on_samples` is to pack files into training
        samples first, then apply FIM.

        Args:
            stream: A stream of tokenized documents.
            sequence_length:  Sequence length of the packed samples.
            seed: The seed to use for the random number generator.
            tail_length: The length of the padding to add to the end of the sample.  If the space
                remaining is smaller than this and we cannot fit another document, we pad with
                `padding_token_id`.
        """
        self.np_rng = np.random.RandomState(seed=seed)
        # for `base.tokenizers.Tokenizer` instances, there is no
        # general interface.  Deepseek and Starcoder tokenizers use a
        # `begin_sequence` field or property.
        # See https://github.com/augmentcode/augment/blob/cf1a343cc72ff67db65e540e52a28be80d9ddec9/base/tokenizers/tiktoken_starcoder_tokenizer.py#L132
        bos_token_id = getattr(self.tokenizer.special_tokens, "begin_sequence", None)
        buffer = []
        # stream yields tokenized documents, without any special tokens such as BOS or EOS
        # We continuously add FIM transformed and tokenized documents to the right of the
        # buffer, and yield full samples from the left when we have enough tokens
        for document in stream:
            # Since we are applying FIM before packing, there is no need to truncate or pad
            # during FIM transformation, therefore `truncate_or_pad=False`
            sample = self._apply_fim_transformation(document, truncate_or_pad=False)

            # Pad if there is little space left, and we cannot fit another document.
            if (
                len(buffer) + len(sample) > sequence_length
                and len(buffer) + tail_length > sequence_length
            ):
                buffer.extend([self.padding_token_id] * (sequence_length - len(buffer)))
                yield np.array(buffer)
                buffer = []

            # Add the next tokenized document (possibly with FIM applied) to the sequence,
            # and add proper BOS and EOS tokens if applicable
            if bos_token_id is not None:
                buffer.extend(bos_token_id)
            buffer.extend(sample)
            buffer.append(self.eod_token_id)

            # Yield any full samples until we need to add the next document
            while len(buffer) > sequence_length:
                result = buffer[:sequence_length]
                buffer = buffer[sequence_length:]
                yield np.array(result)

        # Pad and yield any remaining tokens in the buffer
        if buffer:
            buffer.extend([self.padding_token_id] * (sequence_length - len(buffer)))
            yield np.array(buffer)

    def apply_on_samples(
        self,
        data: Iterable[np.ndarray],
        seed: Optional[int] = None,
        truncate_or_pad: bool = True,
    ) -> Sequence[np.ndarray]:
        """Apply FIM transformations to the given token samples."""
        self.np_rng = np.random.RandomState(seed=seed)
        return [
            self._apply_fim_transformation(sample, truncate_or_pad=truncate_or_pad)
            for sample in data
        ]

    def _apply_fim_transformation(
        self, sample: np.ndarray, truncate_or_pad: bool = False
    ) -> np.ndarray:
        """Apply a FIM transformation to a packed sample for pretraining.

        This was adapted from the StarCoder FIM code:
        https://github.com/EleutherAI/gpt-neox/blob/FIM-clean/megatron/data/gpt2_dataset.py#L115
        """
        sample_len = sample.shape[0]

        def call_permute(sample_slice):
            return self._permute(sample_slice, truncate_or_pad=truncate_or_pad)

        if self.fim_rate != 0:
            # split sample by document
            segment_breaks = np.argwhere(sample == self.eod_token_id)

            if segment_breaks.shape != (0, 1):
                # then there is an EOD token in this example
                curr_start_position = 0
                new_samples = []
                for loc in segment_breaks.flatten():
                    # Only permute non-empty segments.
                    if loc - curr_start_position > 0:
                        # permute {prefix, suffix, middle} or {suffix, prefix, middle}
                        permuted = call_permute(sample[curr_start_position:loc])
                        new_samples += [permuted, [self.eod_token_id]]

                    curr_start_position = loc + 1  # jump over the EOD token

                # Permute the segment after the last EOD
                permuted = call_permute(sample[curr_start_position:])
                new_samples.append(permuted)

                sample = np.concatenate(new_samples)
            else:
                sample = call_permute(sample)

        if truncate_or_pad:
            # Truncate or pad sequence to max-length
            diff = sample.shape[0] - sample_len
            if diff > 0:  # too long
                sample = sample[:sample_len]
            elif diff < 0:  # too short
                sample = np.concatenate([sample, np.full(-diff, self.padding_token_id)])

        if truncate_or_pad:
            assert sample.shape[0] == sample_len
        return sample

    def _permute(
        self,
        segment: np.ndarray,
        truncate_or_pad: bool = True,
    ):
        """Perform a FIM permutation on a given sample segment.

        This segment is the whole training sample if there are not EOD tokens inside,
        or the segment after the last EOD token otherwise.

        From https://github.com/EleutherAI/gpt-neox/blob/FIM-clean/megatron/data/gpt2_dataset.py#L341

        Take in a sample (np array w/ size (0,chunklength)) and perform a FIM
        transformation on it.  Maintain the same sample length (if transform creates
        a few extra tokens, drop them).
        """
        # Sometimes the tail can be very short, or the document might just have a BOS token
        if len(segment) < 2:
            return segment
        if self.np_rng.binomial(1, self.fim_rate):  # sample bernoulli dist
            # `bos_tokens` contain the tokens at the start of the segment that should
            # not be moved to the suffix
            # Currently, this happens when the tokenizer has a BOS token and the first
            # token of the segment is the BOS token.
            if len(self.bos_token_id) > 1:
                raise ValueError("Impossible to have more than one BOS token.")
            if len(self.bos_token_id) == 0 or segment[0] != self.bos_token_id[0]:
                bos_tokens = []
            else:
                segment = segment[1:]
                bos_tokens = [self.bos_token_id]
            contents = self.detokenize(segment.tolist())

            try:
                # A boundary can be =0 (prefix will be empty)
                # a boundary can be =len(contents) (suffix will be empty)
                # The two boundaries can be equal (middle will be empty)
                boundaries = list(
                    self.np_rng.randint(low=0, high=len(contents) + 1, size=2)
                )
                boundaries.sort()
            except ValueError as exc:
                print(len(contents), contents)
                print(exc)
                raise exc

            prefix = contents[: boundaries[0]]
            middle = contents[boundaries[0] : boundaries[1]]
            suffix = contents[boundaries[1] :]

            prefix = np.array([*self.tokenize(prefix)], dtype=np.int64)
            middle = np.array([*self.tokenize(middle)], dtype=np.int64)
            suffix = np.array([*self.tokenize(suffix)], dtype=np.int64)

            # here we truncate each given segment to fit the same length as it was before
            # A consequence is that we never reach the end of a file?
            # we should rather truncate at the context-level
            if truncate_or_pad:
                # need to make same length as the input. Take the 3 sentinel tokens
                # into account
                new_length = suffix.shape[0] + prefix.shape[0] + middle.shape[0] + 3
                diff = new_length - segment.shape[0]
                if diff > 0:  # too long
                    if (
                        suffix.shape[0] <= diff
                    ):  # if there's no space to truncate the suffix: stop and report it.
                        # atm i should have stopped this from happening
                        return segment
                    suffix = suffix[: suffix.shape[0] - diff]
                elif diff < 0:  # too short
                    suffix = np.concatenate(
                        [suffix, np.full(-diff, self.padding_token_id)]
                    )

            if self.np_rng.binomial(1, self.fim_spm_rate):
                # SPM (variant 2 from FIM paper)
                # This might need some adjustments in the future.
                # Different models might use different formats, and for
                # deepseek we don't really know how to do SPM
                new_segment = np.concatenate(
                    [
                        bos_tokens + [self.prefix_token_id, self.suffix_token_id],
                        suffix,
                        [self.middle_token_id],
                        prefix,
                        middle,
                    ]
                )
            else:
                # PSM
                # <BOS><FIM_PREFIX>prefix<FIM_SUFFIX>suffix<FIM_MIDDLE>middle<EOD>

                new_segment = np.concatenate(
                    [
                        bos_tokens + [self.prefix_token_id],
                        prefix,
                        [self.suffix_token_id],
                        suffix,
                        [self.middle_token_id],
                        middle,
                    ]
                )

        else:
            # don't do FIM preproc
            new_segment = segment

        return new_segment

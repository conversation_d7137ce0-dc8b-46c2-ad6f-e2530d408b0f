"""Unit tests for the research signature index."""

from pathlib import Path

from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from base.static_analysis.signature_index import FileSummary
from base.static_analysis.usage_analysis import SymbolNameUsage
from research.core.types import Document
from research.eval.harness.systems.research_signature_index import (
    ResearchSignatureIndex,
)


def test_repeated_usages():
    """Repeated usages of the same name should not be returned twice."""

    example = {
        "defs.py": "def a(): pass\ndef b(): pass\ndef c(): pass",
        "uses.py": "a();a();b();b();c();c()",
    }

    index, pfiles = build_signature_index(example)
    pfile = pfiles["uses.py"]

    middle_range = CharRange(8, 15)  # select the two b() calls.
    usages = index.get_training_signature_usages(
        pfile.path, pfile.lang, pfile.code, middle_range
    )
    ctx_usages = list(usages.ctx_signatures.keys())
    # should only report the `a` and `c` usages closest to the middle
    assert ctx_usages == [
        SymbolNameUsage("c", <PERSON><PERSON><PERSON><PERSON><PERSON>(16, 17)),
        SymbolNameUsage("a", CharRange(4, 5)),
    ]

    # should only report the first `b` usage in the middle
    middle_usages = list(usages.middle_signatures.keys())
    assert middle_usages == [SymbolNameUsage("b", CharRange(8, 9))]


def test_no_module_usages_in_middle():
    """There should not be any inline lookups of modules."""
    example = {
        "mod_a.py": "def a(): pass",
        "mod_b.py": "def b(): pass",
        "uses.py": "import mod_a; import mod_b",
    }

    index, pfiles = build_signature_index(example)
    pfile = pfiles["uses.py"]

    middle_range = CharRange(0, 12)  # select `import mod_a`.
    usages = index.get_training_signature_usages(
        pfile.path, pfile.lang, pfile.code, middle_range
    )
    ctx_names = [u.name for u in usages.ctx_signatures.keys()]
    assert ctx_names == ["@mod_b"]

    middle_names = [u.name for u in usages.middle_signatures.keys()]
    assert middle_names == []


class_def_example = """\
class BigClass:
    def m_a(): pass
    def m_b(): pass
    def m_c(): another_f()

def another_f(): pass
"""


def test_same_class_usage():
    example = {
        "class_def.py": class_def_example,
    }
    index, pfiles = build_signature_index(example)
    pfile = pfiles["class_def.py"]
    middle_range = CharRange(20, 35)  # selects `def m_a(): pass`.
    # use a short prefix/suffix size such that the prompt range does not
    # fully include the BigClass's.
    index.est_prefix_chars = 5
    index.est_suffix_chars = 5
    usages = index.get_training_signature_usages(
        pfile.path, pfile.lang, pfile.code, middle_range
    )
    ctx_names = [u.name for u in usages.ctx_signatures.keys()]
    # @class_def the module is included due to cursor usage
    assert ctx_names == ["BigClass", "@class_def", "another_f"]

    signature_text = [
        sig.text
        for u, sigs in usages.ctx_signatures.items()
        if u.name == "BigClass"
        for sig in sigs
    ][0]
    assert "m_b" in signature_text
    assert "m_c" in signature_text
    assert "m_a" not in signature_text

    # if we extend the prompt to cover `BigClass`, it should not show up in the context
    index.est_prefix_chars = 50
    index.est_suffix_chars = 50
    usages = index.get_training_signature_usages(
        pfile.path, pfile.lang, pfile.code, middle_range
    )
    ctx_names = [u.name for u in usages.ctx_signatures.keys()]
    assert ctx_names == ["@class_def", "another_f"]

    # if we extend the prompt to cover entire file, there will be no usages left
    index.est_prefix_chars = 50
    index.est_suffix_chars = 100
    usages = index.get_training_signature_usages(
        pfile.path, pfile.lang, pfile.code, middle_range
    )
    ctx_names = [u.name for u in usages.ctx_signatures.keys()]
    assert ctx_names == []


def test_same_file_usage_on_updated_doc():
    """Test calling `get_training_signature_usages` on an updated document.

    This is a regression test for a bug where `get_training_signature_usages`
    incorrectly updates the index (due to document ids), which leads to incorrect
    results for subsequent `get_context_signatures` calls. Since document ids are
    no longer tracked in signature index, this test should pass now.
    """
    example = {
        "class_def.py": class_def_example,
    }
    index, _ = build_signature_index(example)

    # now let's update the file content and call `get_training_signature_usages`
    updated_code1 = "\n\n" + class_def_example
    middle_range = CharRange(20, 35).shifted(2)  # selects `def m_a(): pass`.
    # use a short prefix/suffix size such that the prompt range does not
    # fully include the BigClass's.
    index.est_prefix_chars = 5
    index.est_suffix_chars = 5
    usages = index.get_training_signature_usages(
        Path("class_def.py"), "python", updated_code1, middle_range
    )
    ctx_names = [u.name for u in usages.ctx_signatures.keys()]
    assert ctx_names == ["BigClass", "@class_def", "another_f"]

    # now set the file content it its original value
    added = index.add_docs([Document.new(class_def_example, "class_def.py")])
    assert len(added) == 1
    # the context signature should contain parent scopes
    updated_summary2 = FileSummary.from_pfile(added[0])
    index.est_suffix_chars += len(middle_range)
    est_prompt_range = CharRange(
        max(0, 20 - index.est_prefix_chars),
        20 + index.est_suffix_chars,
    )
    ctx_signatures = index.get_context_signatures(
        updated_summary2, 20, est_prompt_range
    ).ctx_signatures
    ctx_names = [u.name for u in ctx_signatures.keys()]
    assert ctx_names == ["BigClass", "@class_def", "another_f"]


def test_set_for_token_budgets():
    """Test that the token budgets can affect signature index."""

    example = {
        "foo.py": "bar(foo)\ndef foo(): bar()",
        "bar.py": "foo(bar)\ndef bar(): foo()",
    }

    index, summary_map = build_signature_index(example)

    # First test normal budgets
    file = summary_map["foo.py"]
    ctx_signatures = index.get_training_signature_usages(
        file.path, file.lang, file.code, middle_range=CharRange(0, 0)
    ).ctx_signatures
    used_names = {u.name for u in ctx_signatures.keys()}
    assert "foo" not in used_names  # same file usage is already in the suffix
    assert "bar" in used_names  # cross file usage

    # if we don't have any suffix tokens, the index will not exclude
    # signatures that are already defined in the same file later
    index.set_for_token_budgets(
        prefix_tokens=1000, suffix_tokens=0, signature_tokens=1000
    )
    ctx_signatures = index.get_training_signature_usages(
        file.path, file.lang, file.code, middle_range=CharRange(0, 0)
    ).ctx_signatures
    used_names = {u.name for u in ctx_signatures.keys()}
    assert "foo" in used_names  # same file usage
    assert "bar" in used_names  # cross file usage

    # if we set the signature tokens to 0, there will be no signature usages
    index.set_for_token_budgets(
        prefix_tokens=1000, suffix_tokens=1000, signature_tokens=0
    )
    ctx_signatures = index.get_training_signature_usages(
        file.path, file.lang, file.code, middle_range=CharRange(0, 0)
    ).ctx_signatures
    assert not ctx_signatures


def build_signature_index(name2file: dict[str, str]):
    """Build a signature index from a set of files."""
    index = ResearchSignatureIndex(verbose=True)
    docs = [Document.new(code, path) for path, code in name2file.items()]
    pfiles = index.add_docs(docs)
    pfiles = {str(pfile.path): pfile for pfile in pfiles}
    return index, pfiles

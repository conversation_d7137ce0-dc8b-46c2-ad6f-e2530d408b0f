"""Unit tests for the FIM sampler for unit test, lol.

pytest research/fim/tests/test_fim_sampling_for_tests.py
"""

# pylint: disable=protected-access

import os
import pathlib

import pytest
import tree_sitter as ts

from base.languages import language_guesser
from base.static_analysis.common import guess_lang_from_fp
from research.core.artifacts import collect_artifacts
from research.core.data_paths import canonicalize_path
from research.core.types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from research.fim.fim_sampling import CSTFimSampler, _get_nodes_in_brange
from research.fim.fim_sampling_for_tests import (
    DefaultUnitTestCorruptionNodesPicker,
    LiteralUnitTestCorruptionNodesPicker,
)
from research.static_analysis.parsing import TsParsedFile


def _load_all_files(temp_dir: str):
    file_contents: dict[str, str] = {}
    # Walk through the cloned repository and collect file contents
    for root, _, files in os.walk(temp_dir):
        for file in files:
            file_path = os.path.join(root, file)
            # Ignore files in the experimental or third_party directories
            if "experimental" in file_path or "third_party" in file_path:
                continue
            if language_guesser.guess_language(file_path) is None:
                continue
            with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                file_contents[os.path.relpath(file_path, temp_dir)] = f.read()

    return file_contents


@pytest.fixture
def file_contents():
    root_folder = canonicalize_path("data/Research-CI/augment-5cda1ca")
    assert pathlib.Path(root_folder).exists()
    return _load_all_files(str(root_folder))


def _find_index_exact_match(target_text: bytes, nodes: list[bytes]) -> int | None:
    for i, node in enumerate(nodes):
        if target_text == node:
            return i
    return None


def _get_nodes_with_weight(
    file_path: str,
    file_contents: dict[str, str],
    sampler: CSTFimSampler,
    node_picker: DefaultUnitTestCorruptionNodesPicker,
) -> list[tuple[ts.Node, float]]:
    assert (
        file_path in file_contents
    ), f"File {file_path} not found from {len(file_contents)} files"
    file_content = file_contents[file_path]
    lang = guess_lang_from_fp(file_path)
    assert lang is not None
    pfile = TsParsedFile.parse(
        path=pathlib.Path(file_path), lang=lang, code=file_content
    )
    candidate_range = CharRange(0, len(pfile.code))
    brange = pfile.bmap.crange_to_brange(candidate_range)
    target_candidates = _get_nodes_in_brange(pfile.ts_tree.root_node, brange)
    get_node_weight = node_picker.get_node_weight
    candidate_weights = [
        get_node_weight(n, pfile, sampler.rng) for n in target_candidates
    ]
    # Remove the nodes that have zero weight
    node_with_weight = [
        (n, w) for n, w in zip(target_candidates, candidate_weights) if w > 0
    ]
    return node_with_weight


def test_literal(file_contents: dict[str, str]):
    node_picker_literal = LiteralUnitTestCorruptionNodesPicker(
        no_corruption_expansion_rate=0.2,
        random_corrupt_available_siblings_rate=0.5,
        corrupt_all_available_siblings_rate=0.2,
        possibly_corrupt_ancestor_rate=0.0,
        edit_similarity_threshold=0.3,
        max_num_lines_per_node=10,
        max_num_char_per_node=400,
    )
    sampler = CSTFimSampler(
        pick_whole_node_rate=1.0,
        pick_extra_spaces_when_whole_node=0.0,
        empty_completion_rate=0.01,
        corruption_nodes_picker=node_picker_literal,
    )

    # Test this file: https://github.com/augmentcode/augment/blob/5cda1ca65fb9c5cc262c30be53c621814f55e99c/research/fastbackward/tests/test_attention.py
    file_path = "research/fastbackward/tests/test_attention.py"
    node_with_weight = _get_nodes_with_weight(
        file_path, file_contents, sampler, node_picker_literal
    )
    node_with_weight = sorted(node_with_weight, key=lambda x: x[1], reverse=True)
    available_node_text = [n.text for n, _ in node_with_weight]
    # The 6 decorators about pytest should be captured and should not capture too many nodes
    assert 6 <= len(node_with_weight) <= 10
    # This must be top-4 out of 6
    target_text = b"""@pytest.mark.parametrize("dim", [128, 256])"""
    index = _find_index_exact_match(target_text, available_node_text)
    assert index is not None and index <= 3, f"index = {index}\n{target_text}"
    # This must be top-3 out of 6, as it contains more numbers
    target_text = (
        b"""@pytest.mark.parametrize("seq_len", [1, 128, 512, 2048, 4096, 8192])"""
    )
    index = _find_index_exact_match(target_text, available_node_text)
    assert index is not None and index <= 2, f"index = {index}\n{target_text}"

    # Test this file: https://github.com/augmentcode/augment/blob/5cda1ca65fb9c5cc262c30be53c621814f55e99c/research/retrieval/tests/test_prompt_formatters.py
    file_path = "research/retrieval/tests/test_prompt_formatters.py"
    node_with_weight = _get_nodes_with_weight(
        file_path, file_contents, sampler, node_picker_literal
    )
    node_with_weight = sorted(node_with_weight, key=lambda x: x[1], reverse=True)
    available_node_text = [n.text for n, _ in node_with_weight]
    assert len(node_with_weight) >= 6
    target_text = b"8: [101, 24, 25, 26, 100, 3, 4, 5]"
    index = _find_index_exact_match(target_text, available_node_text)
    # This must be top-half
    assert (
        index is not None and index < len(node_with_weight) // 2
    ), f"index = {index}\n{target_text}"


def test_default(file_contents: dict[str, str]):
    node_picker = DefaultUnitTestCorruptionNodesPicker(
        no_corruption_expansion_rate=0.2,
        random_corrupt_available_siblings_rate=0.4,
        corrupt_all_available_siblings_rate=0.2,
        possibly_corrupt_ancestor_rate=0.3,
    )
    sampler = CSTFimSampler(
        pick_whole_node_rate=1.0,
        pick_extra_spaces_when_whole_node=0.0,
        empty_completion_rate=0.01,
        corruption_nodes_picker=node_picker,
    )

    # Test this file: https://github.com/augmentcode/augment/blob/5cda1ca65fb9c5cc262c30be53c621814f55e99c/clients/vscode/src/__tests__/workspace/blob-watcher.test.ts
    file_path = "clients/vscode/src/__tests__/workspace/blob-watcher.test.ts"
    with collect_artifacts() as collector:
        node_with_weight = _get_nodes_with_weight(
            file_path, file_contents, sampler, node_picker
        )
        artifacts = collector.get_artifacts()
    node_with_weight = sorted(node_with_weight, key=lambda x: x[1], reverse=True)
    # The 4 test func call must be captured
    assert len(node_with_weight) >= 4
    assert node_with_weight[0][0].text.startswith(b"test(")
    assert len(artifacts) == len(node_with_weight)

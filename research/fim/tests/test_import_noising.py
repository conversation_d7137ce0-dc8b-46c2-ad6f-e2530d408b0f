"""Tests for import_noising utilities."""

import textwrap
from typing import Sequence

from base.static_analysis.common import assert_str_eq
from base.static_analysis.parsing import TsParsedFile
from research.fim.import_noising import (
    ImportStatement,
    find_all_imports,
    is_import_noising_supported,
    reorder_import_statements,
    reorder_import_symbols,
)
from research.static_analysis.common import assert_eq, decode_bytes
from research.static_analysis.usage_analysis import (
    LanguageID,
    ParsedFile,
    Path,
    all_usage_supports,
)


def check_find_imports(
    code: str, lang: LanguageID, expected_imports: Sequence[Sequence[str]]
):
    """Check the result of `find_all_imports`.

    `expected_imports` should contain the symbols in each import statement.
    """
    pfile = ParsedFile.parse(Path(""), lang, code)
    imports = find_all_imports(pfile.ts_tree.root_node, lang)
    actual = [[decode_bytes(n.text) for n in s.symbols] for s in imports]
    assert_eq(actual, list(expected_imports))


def test_find_all_imports():
    """Check the result of `find_all_imports`."""

    check_find_imports(
        """from foo import *""",
        "python",
        [["from foo import *"]],
    )

    python_import_example = textwrap.dedent(
        """
        from __future__ import annotations
        import a.b.c
        from e.f import g1, g2 as h2
        """
    )

    # Note that we don't consider future imports as import statements because they
    # have stricter requirements, so we don't randomly manipulate them.
    check_find_imports(
        python_import_example,
        "python",
        [["a.b.c"], ["g1", "g2 as h2"]],
    )

    typescript_import_example = textwrap.dedent(
        """
        import * as math from './math';
        import myFunction from './myFunction';
        import { add, sub as subtract } from './add_sub';
        import './myModule';
        import defaultExp, { Component } from '@angular/core';
        """
    )

    check_find_imports(
        typescript_import_example,
        "typescript",
        [
            ["* as math"],
            ["myFunction"],
            ["add", "sub as subtract"],
            ["./myModule"],
            ["defaultExp", "Component"],
        ],
    )

    java_import_example = textwrap.dedent(
        """
        import foo.*;

        import java.util.List;
        """
    )
    check_find_imports(
        java_import_example,
        "java",
        [
            ["import foo.*;"],
            ["import java.util.List;"],
        ],
    )

    go_import_example = textwrap.dedent(
        """
        import (
            "fmt"
            "math"
        )
        import os_alias "os"
        import "example.com/ex"
        """
    )

    check_find_imports(
        go_import_example,
        "go",
        [
            ['"fmt"', '"math"'],
            ['os_alias "os"'],
            ['"example.com/ex"'],
        ],
    )

    cpp_import_example = textwrap.dedent(
        """
        #include <iostream>
        #include "myHeaderFile.h"
        using namespace std;
        using std::cout;
        """
    )

    check_find_imports(
        cpp_import_example,
        "cpp",
        [
            ["<iostream>"],
            ['"myHeaderFile.h"'],
            ["using namespace std;"],
            ["using std::cout;"],
        ],
    )

    rust_import_example = textwrap.dedent(
        """
        use std::fs::File;
        use std::collections::{HashMap, HashSet};
        use super::*;
        """
    )

    check_find_imports(
        rust_import_example,
        "rust",
        [
            ["std::fs::File"],
            ["HashMap", "HashSet"],
            ["super::*"],
        ],
    )


def test_find_imports_recursion_depth():
    rec_depth = 10_000
    rec_example = "f(" * rec_depth + "x" + ")" * rec_depth

    for lang in ("python", "java", "cpp", "typescript", "javascript", "go", "rust"):
        check_find_imports(
            rec_example,
            lang,
            [],
        )


def test_import_noising_implemented():
    """We should implement dropout support for langauges with usage support."""
    for lang in all_usage_supports:
        if not is_import_noising_supported(lang):
            raise NotImplementedError(f"Import noising not implemented for: {lang}")


PYTHON_IMPORT_EXAMPLE = """\
from dataclasses import dataclass, field

import tree_sitter as ts  # some comments

from base.ranges import ByteRange as BRange, LineRange as LRange
from base.static_analysis.common import (
    LanguageID,
    replace_str,
    show_str_diff,
)

foo(1 + x)

from research.next_edits.wip_repo_sampler import sample_near_sorted_order
"""


def test_reorder_import_statements():
    """Test `reorder_import_statements` on a Python example."""
    new_order = [1, 0, 2, 4, 3]
    pfile = TsParsedFile.parse(Path("example.py"), "python", PYTHON_IMPORT_EXAMPLE)
    transformed = reorder_import_statements(pfile, lambda _: new_order)

    expected = """\
import tree_sitter as ts

from dataclasses import dataclass, field  # some comments

from base.ranges import ByteRange as BRange, LineRange as LRange
from research.next_edits.wip_repo_sampler import sample_near_sorted_order

foo(1 + x)

from base.static_analysis.common import (
    LanguageID,
    replace_str,
    show_str_diff,
)
"""
    assert_str_eq(transformed.decode(), expected)


def test_reorder_import_symbols():
    """Test `reorder_import_symbols` on a Python example."""

    def reorder_fn(istmt: ImportStatement) -> Sequence[int]:
        """Simply reverse the order of the symbols."""
        n_symbols = len(istmt.symbols)
        return list(reversed(range(n_symbols)))

    pfile = TsParsedFile.parse(Path("example.py"), "python", PYTHON_IMPORT_EXAMPLE)
    transformed = reorder_import_symbols(pfile, reorder_fn)
    expected = """\
from dataclasses import field, dataclass

import tree_sitter as ts  # some comments

from base.ranges import LineRange as LRange, ByteRange as BRange
from base.static_analysis.common import (
    show_str_diff,
    replace_str,
    LanguageID,
)

foo(1 + x)

from research.next_edits.wip_repo_sampler import sample_near_sorted_order
"""
    assert_str_eq(transformed.decode(), expected)

"""FIM sampler based on file diff information."""

from __future__ import annotations

from dataclasses import dataclass, field
from pathlib import Path
from random import Random
from typing import Iterable

import tree_sitter as ts

from base.diff_utils.str_diff import precise_char_diff
from base.ranges.byte_map import ByteMap
from base.ranges.line_map import LineMap
from base.ranges.range_types import ByteRange, CharRange
from base.static_analysis.common import (
    LanguageID,
    shorten_str,
    show_str_diff,
    tsnode_to_crange,
)
from base.static_analysis.parsing import Src<PERSON><PERSON>, TreeSitterParser, TsParsedFile
from research.core.str_diff import AddedSpan, ModSpan, NoopSpan, StrDiff
from research.fim.fim_sampling import (
    CSTFimSampler,
    DefaultCorruptionNodesPicker,
    FimProblem,
    FimSamplingFailed,
    SkipOrOutput,
    _divide_spans_by_skips,
    _get_subsequent_nodes,
    find_todo_like,
)


@dataclass
class DiffFimSampler:
    """FIM sampler based on file diff information."""

    fim_sampler: CSTFimSampler = field(
        # need a larger expansion rate since we only corrupt modified nodes
        default_factory=lambda: CSTFimSampler(
            corruption_nodes_picker=DefaultCorruptionNodesPicker(
                corruption_expansion_rate=0.4
            ),
            pick_whole_node_rate=0.5,
        )
    )
    """The underlying FIM sampler."""

    ts_parser: TreeSitterParser = field(default_factory=TreeSitterParser)
    """The tree-sitter parser."""

    min_node_modify_ratio: float = 0.5
    """The minimal modified ratio of a node to be considered as modified."""

    simulate_WIP_context: bool = True
    """Whether to simulate a WIP state of the prefix and suffix by partially dropping
    some changes."""

    @property
    def rng(self) -> Random:
        return self.fim_sampler.rng

    def sample_diff_fim(
        self,
        file_before: tuple[Path, str],
        file_after: tuple[Path, str],
        lang: LanguageID,
        trials: int = 3,
        logs: list[str] | None = None,
    ) -> DiffFimProblem | FimSamplingFailed:
        """Repeatedly calls `sample_diff_fim_once` for at most `trials` times.

        Returns the first one that succeeds.
        """
        if trials <= 0:
            raise ValueError(f"{trials=} must be positive.")

        result = None
        for t in range(trials):
            if logs is not None:
                logs.append(f"**Trial {t}**")
            result = self.sample_diff_fim_once(file_before, file_after, lang, logs=logs)
            if not isinstance(result, FimSamplingFailed):
                return result
        assert isinstance(result, FimSamplingFailed), f"{result=}"
        return result

    def sample_diff_fim_once(
        self,
        file_before: tuple[Path, str],
        file_after: tuple[Path, str],
        lang: LanguageID,
        logs: list[str] | None = None,
    ) -> DiffFimProblem | FimSamplingFailed:
        """Sample a FimProblem from two versions of the same file.

        Args:
            file_before: the path and content of the file before the diff.
            file_after: the path and content of the file after the diff.
            lang: the language of the file.
            logs: a list of strings to log debugging info in the sampling process.

        With some simplification, the sampling algorithm works as follows:
        1. Parse the after version of the file to obtain a parse tree.
        2. Take the character diff between the after version and the before version
        of the file. Based on this diff, mark each node in the parse tree as being
        “unchanged” or “modified”.
        3. Randomly pick one of the modified nodes as the target node. Sample a cursor
        position within the target node to be the start of the middle span. End the
        middle using the end of the target node.
        4. If there are modified nodes that are immediately after the target node,
        optionally include these nodes into the middle span as well.
            Occasionally, we convert the target range into an empty range to teach
            the model to not generate anything when the target is complete.
        5. Using the middle range and the parse tree, run the same pause and skip
        assignment logic from FIM Sampler V2 to construct the model output.
        6. Randomly drop some changes in the prefix and suffix to simulate a WIP
        version of the file.
        7. Combine the middle from step 5 and the WIP prefix and suffix from step 6
        to construct the FIM problem.
        """

        def is_small_leaf_node(n: _ChangedNode) -> bool:
            return n.node.child_count == 0 and len(n.crange) <= 10

        sampler = self.fim_sampler
        path, code_after = file_after
        path_before, code_before = file_before
        if file_before == file_after:
            return FimSamplingFailed("File before and after are the same.", f"{path=}")

        # step 1: parse the after version of the file to obtain a parse tree
        pfile = TsParsedFile.parse(path, lang, code_after, self.ts_parser)
        ts_tree = pfile.ts_tree

        # step 2: find all nodes that are modified in the diff whose modify
        # ratio is larger than the set threshold
        diff = precise_char_diff(code_before, code_after)
        modified_nodes = find_modified_nodes(ts_tree.root_node, diff, pfile.bmap)
        num_all_modified = len(modified_nodes)
        modified_nodes = [
            n
            for n in modified_nodes
            # very small leaf nodes will always be considered as modified
            # otherwise, we check the ratio of modified characters
            if is_small_leaf_node(n) or n.modified_ratio() >= self.min_node_modify_ratio
        ]
        if not modified_nodes:
            diff_span_types = [type(span).__name__ for span in diff.spans]
            return FimSamplingFailed(
                "No modified nodes found in the diff.",
                f"file_path={path}, {num_all_modified=}, {diff_span_types=}",
            )
        if logs is not None:
            logs.append(f"Found {len(modified_nodes)} modified nodes.")

        # step 3: randomly pick one of the modified nodes as the target node
        try:
            # pylint: disable=protected-access
            target_result = sampler._sample_target_range(
                pfile,
                [n.node for n in modified_nodes],
                weights=[n.modified_ratio() for n in modified_nodes],
                logs=logs,
            )
        except FimSamplingFailed as e:
            return e
        target_range, target_node, target_leftover = target_result

        if logs:
            for n in modified_nodes:
                if n.node == target_node:
                    logs.append(f"Target node modified: {n.modified_ratio():.2%}")

        # record the start position of the suffix, which can be different from
        # the stop of the final middle when target_leftover is not empty.
        prefix_stop, suffix_start = target_range.to_tuple()

        # step 4: sample additional nodes after the last middle as suffix corruption.
        corruption_nodes_picker = sampler.corruption_nodes_picker
        assert isinstance(corruption_nodes_picker, DefaultCorruptionNodesPicker)
        extra_nodes = self._pick_corruption_nodes(
            target_node,
            [n.node for n in modified_nodes],
            corruption_rate=corruption_nodes_picker.corruption_expansion_rate,
            pfile=pfile,
            logs=logs,
        )
        if extra_nodes:
            suffix_start = pfile.bmap.byte_to_char(extra_nodes[-1].end_byte)

        if not target_leftover:
            # expand the middle to include these extra nodes
            target_range = CharRange(target_range.start, suffix_start)
            # optionally move trailing newlines into the middle
            # pylint: disable=protected-access
            target_range = sampler._include_extra_suffix_newlines(
                target_range, pfile.code, sampler.include_trailing_newline_rate
            )
            suffix_start = target_range.stop
            # [logging]
            if logs is not None:
                target_text = shorten_str(pfile.code[target_range.to_slice()])
                logs.append(f"Expanding target range to {target_range}, {target_text=}")

        # step 4.1: occasionally convert this into an empty completion
        if target_range and self.rng.random() < sampler.empty_completion_rate:
            # move the middle into the prefix
            prefix_stop = target_range.stop
            target_range = CharRange.point(prefix_stop)
            # [logging]
            if logs is not None:
                logs.append(f"Turn into empty target, {prefix_stop=}")

        if find_todo_like(pfile.code[target_range.to_slice()]) is not None:
            return FimSamplingFailed(
                "TODO-like marker found in target.", f"{pfile.path=}"
            )

        # step 5: run the same pause and skip assignment logic from FIM Sampler V2
        # to construct the model output.
        try:
            # pylint: disable=protected-access
            pause_spans = sampler._compute_pause_spans(
                pfile,
                target_range,
                min_span_size=sampler.min_size_to_pause,
                max_span_size=sampler.max_first_span_size,
                always_recurse=sampler.recursive_pause_scheme,
            )
        except FimSamplingFailed as e:
            return e

        # pylint: disable=protected-access
        skipped = sampler._sample_skipped_ranges(pfile, target_range)
        # [logging]
        if skipped and logs is not None:
            skipped_strs = [pfile.code[s.to_slice()] for s in skipped]
            logs.append(f"{skipped_strs=}")

        # divide the pause spans by skips
        pause_spans = _divide_spans_by_skips(pause_spans, skipped)

        # step 6: simulate a WIP file by partially drop some changes in the prefix
        # and suffix.
        middle_range = CharRange(prefix_stop, suffix_start)
        if not self.simulate_WIP_context:
            wip_prefix = code_after[: middle_range.start]
            wip_suffix = code_after[middle_range.stop :]
        else:
            target_range = tsnode_to_crange(target_node, pfile.bmap)
            preserved_range = middle_range.merge(target_range)
            wip_prefix, wip_suffix = _simulate_wip_file(
                code_before,
                code_after,
                middle_range,
                preserved_range=preserved_range,
                rng=self.rng,
                logs=logs,
            )

        # leftover suffix is usually the last part of a string or comment that was
        # not picked as the middle; it's only nonempty in insertion type completion.
        leftover_suffix = pfile.code[target_leftover.to_slice()]

        # Skipped parts are closing characters that we add to the suffix to simulate
        # an IDE's auto-closing behavior.
        skipped_parts = [pfile.code[s.to_slice()] for s in skipped]

        wip_suffix = "".join((*skipped_parts, leftover_suffix, wip_suffix))

        # step 7: construct the FIM problem
        def range_to_src_span(crange: CharRange) -> SrcSpan:
            return SrcSpan(crange, pfile.code[crange.to_slice()])

        wip_prefix_span = SrcSpan(CharRange(0, prefix_stop), wip_prefix)
        wip_suffix_span = SrcSpan(CharRange(suffix_start, len(pfile.code)), wip_suffix)
        middle_spans = tuple(
            SkipOrOutput(range_to_src_span(m.content), m.skipped, m.pause_at_end)
            for m in pause_spans
        )
        assert (
            wip_prefix_span.range.stop <= wip_suffix_span.range.start
        ), f"{wip_prefix_span=}, {wip_suffix_span=}, {middle_spans=}"
        fim_prob = FimProblem(
            prefix=wip_prefix_span,
            middle_spans=middle_spans,
            suffix=wip_suffix_span,
            file_path=pfile.path,
            middle_node_type=target_node.type,
            original_middle_code=pfile.code[prefix_stop:suffix_start],
        )
        return DiffFimProblem(fim_prob, path_before, code_before)

    def _pick_corruption_nodes(
        self,
        target_node: ts.Node,
        modified_nodes: Iterable[ts.Node],
        corruption_rate: float,
        pfile: TsParsedFile,
        logs: list[str] | None,
    ) -> list[ts.Node]:
        """Grab some modified nodes after `target_node` as additional middle span.

        Note that this implementation is different from the one in CSTFimSampler
        since we are using the set of modified nodes to decide how much we can extend
        the middle (where the middle can't be extended to include unchanged nodes).
        """
        extra_nodes = list[ts.Node]()
        modified_node_ranges = {
            ByteRange(n.start_byte, n.end_byte) for n in modified_nodes
        }
        bmap = pfile.bmap
        for nodes in _get_subsequent_nodes(target_node):
            # we count how many sibling nodes have been modified. If they have all been
            # modified, we have some chance to include all of them into the middle.
            n_changed = 0
            for n in nodes:
                n_range = ByteRange(n.start_byte, n.end_byte)
                if n_range in modified_node_ranges:
                    n_changed += 1
                else:
                    break
            if n_changed == len(nodes) and self.rng.random() < corruption_rate:
                # take all sibling nodes
                extra_nodes.extend(nodes)
            else:
                # take some fraction of nodes, biasing toward a smaller number
                node_ranges = [
                    bmap.brange_to_crange(ByteRange(n.start_byte, n.end_byte))
                    for n in nodes
                ]
                # group the id of tiny pauses with the larger ones so that we don't
                # leave things like trailing commas in the suffix.
                # pylint: disable=protected-access
                pause_groups = CSTFimSampler._merge_small_pauses(
                    node_ranges, pfile, CSTFimSampler.min_size_to_pause
                )

                n_choices = [0] + [pause_ids[-1] + 1 for pause_ids in pause_groups]
                weights = [1 + len(nodes) - i for i in n_choices]
                n_extra = self.rng.choices(n_choices, weights)[0]
                extra_nodes.extend(nodes[:n_extra])
                if n_extra < len(nodes):
                    # if not all nodes are taken, stop corruption
                    break
        # [logging]
        if logs is not None:
            extra_nodes_str = "\n".join(f"\t{n}" for n in extra_nodes)
            logs.append(f"_pick_corruption_nodes:\n{extra_nodes_str}")

        return extra_nodes


def _simulate_wip_file(
    file_before: str,
    file_after: str,
    middle_range: CharRange,
    preserved_range: CharRange,
    rng: Random,
    logs: list[str] | None = None,
) -> tuple[str, str]:
    """Simulate a WIP state of the file by partially dropping some changes.

    Returns the simulated prefix and suffix before the completion.

    Args:
        file_before: the original file content before the change.
        file_after: the file content after the change.
        middle_range: the range of the middle span in `file_after`.
        preserved_range: the range in `file_after` within which changes should not\
            be dropped (e.g., to ensure FIM consistency.)
        rng: random number generator.
        logs: a list to which additional logging information can be appended.
    """

    diff = precise_char_diff(file_before, file_after)

    # find out which changes can be dropped
    # single-line changes on the same line should be dropped or kept together
    lmap_after = LineMap(file_after)
    candidate_groups = list[list[int]]()  # span ids that can be dropped together
    current_group = list[int]()
    current_group_line: int | None = None
    for span_id, (op, op_range) in enumerate(
        zip(diff.spans, diff.span_ranges_in_after)
    ):
        # changes next to the middle should be kept to ensure FIM consistency
        if isinstance(op, NoopSpan) or op_range.intersect(preserved_range) is not None:
            continue
        line_range = lmap_after.crange_to_lrange(op_range)
        if len(line_range) == 1 and current_group_line == line_range.start:
            current_group.append(span_id)
        else:
            current_group_line = line_range.start if len(line_range) > 0 else None
            candidate_groups.append(current_group)
            current_group = [span_id]
    if current_group:
        candidate_groups.append(current_group)

    # randomly sample which changes to drop
    n_kept = rng.randint(0, len(candidate_groups))
    rng.shuffle(candidate_groups)
    dropped_ids = {span_id for group in candidate_groups[:n_kept] for span_id in group}

    # construct the WIP prefix and suffix
    cursor_after = middle_range.start
    middle_offset = 0
    wip_file_parts = list[str]()
    for span_id, (op, op_range) in enumerate(
        zip(diff.spans, diff.span_ranges_in_after)
    ):
        if span_id in dropped_ids:
            wip_file_parts.append(op.before)
            if op_range.stop < cursor_after:
                middle_offset += len(op.before) - len(op.after)
        else:
            wip_file_parts.append(op.after)

    wip_file = "".join(wip_file_parts)
    new_middle_range = middle_range.shifted(middle_offset)
    wip_prefix = wip_file[: new_middle_range.start]
    wip_suffix = wip_file[new_middle_range.stop :]

    if logs is not None:
        prefix_after = file_after[: middle_range.start]
        suffix_after = file_after[middle_range.stop :]
        log_segs = [
            "_simulate_wip_file:",
            f"\tcandidate drops: {len(candidate_groups)}",
            f"\tdropped: {len(dropped_ids)}",
            f"\tprefix_after: {repr(shorten_str(prefix_after, omit_mode='left'))}",
            f"\tsuffix_after: {repr(shorten_str(suffix_after, omit_mode='right'))}",
            f"\tmiddle_offset: {middle_offset}",
            f"\twip_prefix:   {repr(shorten_str(wip_prefix, omit_mode='left'))}",
            f"\twip_suffix:   {repr(shorten_str(wip_suffix, omit_mode='right'))}",
        ]
        logs.append("\n".join(log_segs))

    return wip_prefix, wip_suffix


@dataclass
class DiffFimProblem:
    """A FimProblem with diff information."""

    fim_problem: FimProblem
    """A FIM problem in the simulated version of the file."""

    path_before: Path
    """The path to the original file."""

    code_before: str
    """The original code before the simulated version.

    The diff between the fim_problem and code_before can be shown to the model
    as additional diff information.
    """

    @property
    def path_after(self) -> Path:
        """The path to the file after the simulated version."""
        return self.fim_problem.file_path

    def show_problem(
        self,
        max_prefix_chars: int = 1000,
        max_suffix_chars: int = 1000,
        max_middle_chars: int = 1000,
    ) -> str:
        """Pretty print the FimProblem as a string."""
        context_before = self.code_before
        context_after = "".join(
            [
                self.fim_problem.prefix.code,
                "<|cursor|>",
                self.fim_problem.suffix.code,
            ]
        )
        prompt = show_str_diff(context_before, context_after)
        cursor_pos = prompt.index("<|cursor|>")
        prefix = shorten_str(prompt[:cursor_pos], max_prefix_chars, "left")
        suffix = shorten_str(prompt[cursor_pos:], max_suffix_chars, "right")
        prompt = prefix + suffix
        middle = self.fim_problem.show_middle()
        middle = shorten_str(middle, max_middle_chars, "middle")

        return "\n".join(
            [
                "vv" * 60,
                "~=" * 20 + "Prompt" + "~=" * 20,
                prompt,
                "~=" * 20 + "Middle" + "~=" * 20,
                middle,
                "^^" * 60,
            ]
        )


@dataclass
class _ChangedNode:
    """A node that has been added or modified."""

    node: ts.Node
    """The node that has been modified."""
    crange: CharRange
    """The character range of the entire node."""
    changed_parts: list[CharRange]
    """The character ranges of the parts that have been modified."""

    def modified_ratio(self) -> float:
        """Return the ratio of the node that has been modified."""
        changed_chars = sum(len(crange) for crange in self.changed_parts)
        return changed_chars / len(self.crange)


def find_modified_nodes(
    root: ts.Node, diff: StrDiff, bmap: ByteMap
) -> list[_ChangedNode]:
    """Find all nodes that are modified in the given diff."""

    span_and_ranges = [
        (span, span_range)
        for (span, span_range) in zip(diff.spans, diff.span_ranges_in_after)
        if isinstance(span, (AddedSpan, ModSpan))
    ]
    changed_nodes = list[_ChangedNode]()

    def rec_find(n: ts.Node) -> None:
        """Recursively find all nodes that are modified in the given diff."""
        start_char = bmap.byte_to_char(n.start_byte)
        end_char = bmap.byte_to_char(n.end_byte)
        node_range = CharRange(start_char, end_char)
        intersections = [
            intersection
            for (_, span_range) in span_and_ranges
            if (intersection := span_range.intersect(node_range))
        ]
        if not intersections:
            return
        cnode = _ChangedNode(n, node_range, intersections)
        changed_nodes.append(cnode)

        for c in n.children:
            rec_find(c)

    rec_find(root)
    return changed_nodes

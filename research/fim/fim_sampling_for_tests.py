"""This module implements some special CorruptionNodesPickers to sample unit-test related FIM problems.

It aims to be more language-agnostic.
"""

import math
import typing
from dataclasses import dataclass
from random import Random

import tree_sitter as ts

from base.ranges import ByteRange
from base.static_analysis.common import decode_bytes
from base.static_analysis.parsing import TsParsedFile
from research.core.artifacts import artifacts_enabled, post_artifact
from research.core.utils_for_str import compute_edit_similarity, get_first_n_lines
from research.fim.fim_sampling import CorruptionNodesPicker, _get_subsequent_nodes

# TODO(Xuanyi): these node types might be specific to a subset of languages like Python and TypeScript,
# we should refactor this (and the helper functions below) into per-language lists for extensibility.
EXACT_FUNC_CLASS_TYPES = (
    "function_definition",
    "function_declaration",
    "decorated_definition",
    "decorated_declaration",
    "method_definition",
    "method_declaration",
    "class_definition",
    "class_declaration",
)
# People may write "call_expression" at the top level in typescript as a unit test function :-)
VAGUE_FUNC_CLASS_TYPES = tuple(list(EXACT_FUNC_CLASS_TYPES) + ["call_expression"])
FUNC_CLASS_BOUNDARY_TYPES = tuple(["module", "program"] + list(EXACT_FUNC_CLASS_TYPES))


def get_num_literal(node: ts.Node | None) -> int:
    if node is None:
        return 0
    if node.type in ("string", "integer", "float", "true", "false", "none"):
        return 1
    sum_literal = 0
    for child in node.children:
        sum_literal += get_num_literal(child)
    return sum_literal


def get_depth_and_size(node: ts.Node | None) -> tuple[int, int]:
    if node is None:
        return 0, 0
    cur_dep, cur_size = 0, 1
    for child in node.children:
        depth, size = get_depth_and_size(child)
        cur_dep = max(cur_dep, depth + 1)
        cur_size += size
    return cur_dep, cur_size


def has_ancestor_in_types(
    node: ts.Node | None, types: typing.Collection[str], skip_self: bool = True
) -> bool:
    """Check that whether the ancestor has the given type."""
    if node is None:
        return False
    if not skip_self and node.type in types:
        return True
    else:
        return has_ancestor_in_types(node.parent, types, skip_self=False)


def has_children_in_types(node: ts.Node | None, types: tuple[str, ...]) -> bool:
    if node is None:
        return False
    for child in node.children:
        if child.type in types:
            return True
        elif has_children_in_types(child, types):
            return True
    return False


def _generic_type(node: ts.Node | None) -> str | None:
    if node is None:
        return None
    elif node.type in ("string", "integer", "float", "true", "false", "none"):
        return "literal"
    else:
        return node.type


def _check_same_type(node: ts.Node, target_node: ts.Node) -> bool:
    return _generic_type(node) == _generic_type(target_node)


def _check_is_similar_literal_node(node: ts.Node | None, target_node: ts.Node) -> bool:
    """Check whether the node is a generic literal node that similar to the target node.

    It follows the following heuristics:
    1. If the node has no children as literal or inconsistent type with the target node, then no
    2. If they have the same first 5 chars, then yes
    3. If they have the same first 3 children with the same type, then yes
    4. If they have the same first child type with the same typeand the first 3 grandchildren with the same type, then yes
    5. Otherwise, no
    """
    if node is None:
        return False
    if get_num_literal(node) == 0:
        return False
    if node.type != target_node.type:
        return False
    # If they have the same first 5 chars, then yes
    if node.text[:5] == target_node.text[:5]:
        return True
    # If they have the same first 3 children types, then yes
    if len(node.children) >= 3 and len(target_node.children) >= 3:
        if (
            _check_same_type(node.children[0], target_node.children[0])
            and _check_same_type(node.children[1], target_node.children[1])
            and _check_same_type(node.children[2], target_node.children[2])
        ):
            return True
    # If they have the same first child type and the first 3 grandchildren types, then yes
    if len(node.children) >= 1 and len(target_node.children) >= 1:
        son_of_node, son_of_target = node.children[0], target_node.children[0]
        if (
            son_of_node.type == son_of_target.type
            and len(son_of_node.children) >= 3
            and len(son_of_target.children) >= 3
            and _check_same_type(son_of_node.children[0], son_of_target.children[0])
            and _check_same_type(son_of_node.children[1], son_of_target.children[1])
            and _check_same_type(son_of_node.children[2], son_of_target.children[2])
        ):
            return True
    return False


def has_similar_siblings(node: ts.Node, edit_similarity_threshold: float) -> bool:
    """Check whether the node has similar siblings."""
    if node.parent is None:
        return False
    all_siblings = node.parent.children
    position = all_siblings.index(node)
    for idx, sibling in enumerate(all_siblings):
        if idx == position:
            continue
        if (
            sibling.type == node.type
            and compute_edit_similarity(
                decode_bytes(node.text), decode_bytes(sibling.text)
            )
            > edit_similarity_threshold
        ):
            return True
    return False


def counts_num_str_in_nodes(text: str, nodes: typing.Sequence[ts.Node]) -> int:
    """Counts the number of nodes that contains the given string."""
    encoded_text = text.encode("utf-8").lower()
    num = 0
    for node in nodes:
        if encoded_text in node.text.lower():
            num += 1
    return num


@dataclass
class DefaultUnitTestCorruptionNodesPicker(CorruptionNodesPicker):
    """The node picker that only expands to siblings."""

    no_corruption_expansion_rate: float = 0.25
    """The probability of not expanding suffix corruption to a larger scope."""

    random_corrupt_available_siblings_rate: float = 0.3
    """The probability of taking a few available siblings."""

    corrupt_all_available_siblings_rate: float = 0.3
    """The probability of taking all available siblings if possible.
    If the prob does not hit the above 3 cases, we will try to corrupt all siblings as well as the ancestor.
    """

    possibly_corrupt_ancestor_rate: float = 0.3
    """The probability of corrupt the ancestor if possible."""

    edit_similarity_threshold: float = 0.5
    """The threshold of edit similarity to consider two nodes as similar."""

    max_num_lines_per_node: int = 20
    """The max number of lines in a node to be considered for corruption."""

    max_num_char_per_node: int = 800
    """The max number of characters in a node to be considered for corruption."""

    min_node_depth_size: tuple[int, int] = (1, 3)
    """The depth and size threshold to filter out nodes."""

    def __post_init__(self):
        assert (
            self.no_corruption_expansion_rate
            + self.random_corrupt_available_siblings_rate
            + self.corrupt_all_available_siblings_rate
        ) <= 1.0
        self._cache_node2_extra_nodes = dict[ts.Node, list[ts.Node]]()

    def reset(self):
        self._cache_node2_extra_nodes.clear()

    def get_node_weight(self, node: ts.Node, pfile: TsParsedFile, rng: Random) -> float:
        """Customize the node weights and also attach its extra nodes to the node.

        Here is a high-level heuristic:
        1. Avoid samples that are obviously not useful, such as too small, too large, entire func, etc.
        2. A node must have similar siblings.
        3. A node must be in a function.
        4. Then we try to expand this node as the middle text by randomly expanding to include valid siblings or ancestors.
        5. For such a middle text, we compare its near prefix and near suffix under the same parent to compute the final weight.
            - the detailed logic for computing the final weight can be found in generate_signature_data.
        """
        text = decode_bytes(node.text).lower()
        num_c, num_l = node.start_byte - node.end_byte, text.count("\n")
        if node.parent is None:
            return 0.0
        # avoid sample too large or too small nodes.
        if num_c > self.max_num_char_per_node or num_l > self.max_num_lines_per_node:
            return 0.0
        depth, size = get_depth_and_size(node)
        if depth <= self.min_node_depth_size[0] or size <= self.min_node_depth_size[1]:
            return 0.0
        # avoid sample the entire function
        if node.type in FUNC_CLASS_BOUNDARY_TYPES:
            return 0.0
        # avoid sample not in the function
        if not has_ancestor_in_types(node, VAGUE_FUNC_CLASS_TYPES, skip_self=True):
            return 0.0
        # if it contains a function boundary, then we don't sample it.
        if has_children_in_types(node, FUNC_CLASS_BOUNDARY_TYPES):
            return 0.0
        if not has_similar_siblings(node, self.edit_similarity_threshold):
            return 0.0
        all_siblings = node.parent.children
        position = all_siblings.index(node)
        left_siblings = all_siblings[:position]
        right_siblings = all_siblings[position + 1 :]
        avaliable_index, restrict_not_corrupt_ancestor = [], False
        for idx in range(len(right_siblings)):
            if right_siblings[idx].type in FUNC_CLASS_BOUNDARY_TYPES:
                restrict_not_corrupt_ancestor = True
                break
            else:
                avaliable_index.append(idx)
        extra_nodes = list[ts.Node]()
        debug_info = {
            "mid_node": node,
            "avaliable_indexes_of_right_siblings": avaliable_index,
            "left_siblings": left_siblings,
            "right_siblings": right_siblings,
        }
        nodes_in_middle: list[ts.Node] = [node]
        siblings_after_middle: list[ts.Node] = []
        prob = rng.random()
        debug_info["prob"] = prob
        # ------------------------------------------------------------------------
        # The following codes will pick the corruption nodes.
        if not avaliable_index:
            debug_info["branch"] = "no_corruption_expansion@no_avaliable_index"
            siblings_after_middle.extend(right_siblings)
        elif prob < self.no_corruption_expansion_rate:
            debug_info["branch"] = (
                f"no_corruption_expansion@{prob}<{self.no_corruption_expansion_rate}"
            )
            siblings_after_middle.extend(right_siblings)
        elif (
            prob
            < self.no_corruption_expansion_rate
            + self.random_corrupt_available_siblings_rate
        ):
            choice_index = rng.choice(avaliable_index)
            debug_info["branch"] = (
                f"random_corrupt_available_siblings@{choice_index}/{len(right_siblings)}"
            )
            extra_nodes.extend(right_siblings[: choice_index + 1])
            # update nodes_in_middle and siblings_after_middle
            nodes_in_middle.extend(right_siblings[: choice_index + 1])
            siblings_after_middle.extend(right_siblings[choice_index + 1 :])
        elif (
            restrict_not_corrupt_ancestor
            or prob
            < self.no_corruption_expansion_rate
            + self.random_corrupt_available_siblings_rate
            + self.corrupt_all_available_siblings_rate
        ):
            debug_info["branch"] = "corrupt_all_available_siblings"
            extra_nodes.extend(right_siblings[: avaliable_index[-1] + 1])
            # update nodes_in_middle and siblings_after_middle
            nodes_in_middle.extend(right_siblings[: avaliable_index[-1] + 1])
            siblings_after_middle = []  # it should be empty now
        else:
            # try to corrupt the ancestor if possible
            extra_nodes.extend(right_siblings)
            nodes_in_middle.extend(right_siblings)
            debug_info["branch"] = "corrupt_ancestor"
            debug_info["corrupt_ancestor"] = []
            for idx, nodes in enumerate(_get_subsequent_nodes(node.parent)):
                debug_info["corrupt_ancestor"].append(f"{idx}:{len(nodes)}-nodes")
                if not nodes:
                    extra_nodes.extend(nodes)
                else:
                    corrupt_prob = rng.random()
                    if corrupt_prob < self.possibly_corrupt_ancestor_rate:
                        # 50% to expand partial subnodes and 50% to expand all subnodes
                        if rng.random() < 0.5:
                            expand_index = rng.randint(1, len(nodes))
                            extra_nodes.extend(nodes[:expand_index])
                            debug_info["corrupt_ancestor"].append(
                                f"expand the {idx}-th subnodes group with {expand_index}/{len(nodes)}."
                            )
                            break
                        else:
                            debug_info["corrupt_ancestor"].append(
                                f"expand the entire {idx}-th subnodes group."
                            )
                            extra_nodes.extend(nodes)
                    else:
                        debug_info["corrupt_ancestor"].append(
                            f"breaks at the {idx}-th subnode."
                        )
                        break
        # ------------------------------------------------------------------------
        # This customized logic can be override by subclasses.
        weights = self._calculate_weights(
            node,
            left_siblings=left_siblings,
            nodes_in_middle=nodes_in_middle,
            siblings_after_middle=siblings_after_middle,
            pfile=pfile,
            debug_info=debug_info,
        )
        final_weight = max(weights)
        if final_weight > 0:
            self._cache_node2_extra_nodes[node] = extra_nodes
        # logging
        if artifacts_enabled() and final_weight > 0:
            debug_info["nodes_in_middle"] = nodes_in_middle
            debug_info["siblings_after_middle"] = siblings_after_middle
            debug_info["weights"] = weights
            debug_info["middle_node_str"] = decode_bytes(node.text)
            post_artifact(debug_info)

        return final_weight

    def _calculate_weights(
        self,
        node: ts.Node,
        left_siblings: list[ts.Node],
        nodes_in_middle: list[ts.Node],
        siblings_after_middle: list[ts.Node],
        pfile: TsParsedFile,
        debug_info: dict[str, typing.Any],
    ) -> list[float]:
        """Calculate the weights for the given node and its relevant siblings."""
        weights = []
        # Check if "assert" / "expect" / "verify" appears in the prefix string and middle string.
        actual_middle_brange = ByteRange(
            nodes_in_middle[0].start_byte, nodes_in_middle[-1].end_byte
        )
        actual_middle_crange = pfile.bmap.brange_to_crange(actual_middle_brange)
        actual_middle_text = pfile.code[actual_middle_crange.to_slice()]
        actual_num_lines_in_middle = actual_middle_text.count("\n") + 1
        for identifier in ("assert", "expect", "verify", "verifies"):
            num_left = counts_num_str_in_nodes(identifier, left_siblings)
            num_middle = counts_num_str_in_nodes(identifier, nodes_in_middle)
            num_right = counts_num_str_in_nodes(identifier, siblings_after_middle)
            debug_info[f"cal-weight@{identifier}"] = (num_left, num_middle, num_right)
            if num_left >= 1 and num_middle >= 1:
                # Heuristic 1: we prefer the left part contains less but at least one
                # num_left: 1 -> 4, 2 -> 1.81, 3 -> 1.37, 4 -> 1.17
                weight_left = max(1 / (math.log10(num_left) + 0.25), 1.0)
                # Heuristic 2: we prefer the middle part contains a bit more
                # num_middle: 1 -> 0.58 * 2, 2 -> 1.32 * 2, 3 -> 1.80 * 2, 4 -> 2.16 * 2
                weight_middle = min(math.log2(num_middle + 0.5) * 2, 5.0)
                # Heuristic 3: we prefer the right part contains less
                # num_right: 0 -> 2.97, 1 -> 1.14, 2 -> 1.0
                weight_right = max(1 / math.log(num_right + 1.4), 1.0)
                weight = weight_left + weight_middle + weight_right
                # Heuristic 4: we prefer the middle part contains more lines of code
                # actual_num_lines_in_middle: 1 -> 0.0, 2 -> 1.0, 3 -> 1.58, 4 -> 2.0
                weight += min(math.log2(actual_num_lines_in_middle), 2.0)
                # Heuristic 5: we prefer the first line of middle contains the identifier
                if identifier in get_first_n_lines(decode_bytes(node.text), 1):
                    weight += 2.0
                # Heuristic 6: we prefer the entire sibling nodes contains more identifier
                # with the Heuristic 1-5, num_left=1, num_middle=1, num_right=0 would introduce a higher weight.
                weight *= min(math.log(num_left + num_middle + num_right), 1.2)
                weights.append(weight)
            else:
                weights.append(0)
        return weights

    def pick_corruption_nodes(
        self,
        mid_node: ts.Node,
        pfile: TsParsedFile,
        rng: Random,
        logs: list[str] | None,
    ) -> list[ts.Node]:
        """Randomly grab additional nodes after `mid_node` as suffix corrupiton."""
        del pfile, rng, logs
        if mid_node in self._cache_node2_extra_nodes:
            extra_nodes = self._cache_node2_extra_nodes[mid_node]
        else:
            raise ValueError(
                f"It is expected to have extra nodes for {mid_node}, but not find"
            )
        return extra_nodes


@dataclass
class LiteralUnitTestCorruptionNodesPicker(DefaultUnitTestCorruptionNodesPicker):
    """Customize a literal selection logic."""

    def __post_init__(self):
        super().__post_init__()
        assert self.possibly_corrupt_ancestor_rate == 0.0, "Do not set it for literal."

    def _calculate_weights(
        self,
        node: ts.Node,
        left_siblings: list[ts.Node],
        nodes_in_middle: list[ts.Node],
        siblings_after_middle: list[ts.Node],
        pfile: TsParsedFile,
        debug_info: dict[str, typing.Any],
    ) -> list[float]:
        """Customize the weights calculation for the literal-related nodes.

        The high-level idea is to:
        - avoid node that contains no literal.
        - prefer the node has a closer sibling node that contains literal and a similar structure as it.
        Other heuristics about how to upsample or downsample weight can be found the in the comments.
        """
        actual_middle_brange = ByteRange(
            nodes_in_middle[0].start_byte, nodes_in_middle[-1].end_byte
        )
        actual_middle_crange = pfile.bmap.brange_to_crange(actual_middle_brange)
        actual_middle_text = pfile.code[actual_middle_crange.to_slice()]
        actual_num_lines_in_middle = actual_middle_text.count("\n") + 1
        if get_num_literal(node) == 0:
            return [0.0]
        is_similar_left = [
            _check_is_similar_literal_node(sib, node) for sib in left_siblings
        ]
        is_similar_middle = [
            _check_is_similar_literal_node(sib, node) for sib in nodes_in_middle[1:]
        ]
        is_similar_right = [
            _check_is_similar_literal_node(sib, node) for sib in siblings_after_middle
        ]
        if sum(is_similar_left) + sum(is_similar_middle) + sum(is_similar_right) < 2:
            return [0.0]
        left_close_dis, right_close_dis = 100, 100
        for idx, is_similar in enumerate(is_similar_left):
            if is_similar:
                left_close_dis = min(left_close_dis, len(is_similar_left) - idx)
        for idx, is_similar in enumerate(is_similar_middle + is_similar_right):
            if is_similar:
                right_close_dis = min(right_close_dis, idx + 1)
        # Within 3 distance, the unsimilar node may be empty node or a punctuation.
        if left_close_dis > 3 and right_close_dis > 3:
            return [0.0]
        weight = 0.2
        # Heuristic 1: we prefer the middle part contains a bit more similar literals
        # sum_middle: 0 -> 0, 1 -> 1.0, 2 -> 1.6, 3 -> 2.0
        weight += min(math.log2(sum(is_similar_middle) + 1), 3)
        # Heuristic 2: we prefer the number of literals in the first node to be higher
        # num_literals: 0 -> 0, 1 -> 1.0, 2 -> 1.6, 3 -> 2.0, 4 -> 2.3
        weight += min(math.log2(get_num_literal(node) + 1), 3.0)
        # Heuristic 3: we prefer the number of lines to be a bit higher
        # actual_num_lines_in_middle: 1 -> 0.0, 2 -> 0.7, 3 -> 1.1, 4 -> 1.3
        weight += min(math.log(actual_num_lines_in_middle), 2.0)
        # Heuristic 4: we prefer the middle follows the same pattern as the left part
        if left_close_dis <= 2:
            weight *= 2.0
        else:
            weight *= 1.0
        debug_info["num_literals"] = get_num_literal(node)
        debug_info["left_close_distance"] = left_close_dis
        debug_info["right_close_distance"] = right_close_dis
        debug_info["is_similar_left"] = is_similar_left
        debug_info["is_similar_middle"] = is_similar_middle
        debug_info["is_similar_right"] = is_similar_right
        return [weight]

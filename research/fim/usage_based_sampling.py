from collections import Counter

import tree_sitter as ts
from intervaltree import IntervalTree

from base.static_analysis.usage_analysis import (
    GlobalUsageAnalysis,
    ParsedFile,
    SymbolNameUsage,
)


class UsageBasedNodeWeights:
    """Assigning node sampling weights based on usage analysis.

    Given the result of a GlobalUsageAnalysis, this class will assign weights to
    nodes based on whether they contain usages of a nonlocal defintion. Names used
    only once in the current file will be preferred over names used multiple times.

    See `research/notebooks/usage_analysis_tutorial.ipynb` for an introduction to usage
    analysis.
    """

    usage_weight: float = 100.0
    """The weight of a node that's only been used once in the current file.

    We discount this weight if the name is used multiple times.
    """

    node_size_limit: int = 100
    """The max size of a node to benefit from usages inside.

    Nodes larger than this size will not be up-weighted. This ensures that we prefer
    more direct parents of a usage over larger nodes like the root node, which always
    contains all usages.
    """

    min_chars_as_nonlocal: int = 10_000
    """The min distance between definition and usage to be considered as nonlocal."""

    def __init__(self, pfile: ParsedFile, analysis: GlobalUsageAnalysis):
        def is_non_local(u: SymbolNameUsage) -> bool:
            if u.kind == "declare":
                return False
            if u.def_range is None:
                return True
            def_site_dis = u.def_range.crange.distance(u.use_site)
            return def_site_dis >= self.min_chars_as_nonlocal

        nonlocal_usages = [u for u in analysis.site2defs.keys() if is_non_local(u)]
        name_counts = Counter(u.name for u in nonlocal_usages)

        def usage_weight(u: SymbolNameUsage) -> float:
            w = self.usage_weight / name_counts[u.name]
            # class member usages are generally more noisy than global usages,
            # so we slightly downweight them
            if u.name.startswith("."):
                w /= 2
            return max(1.0, w)

        bmap = pfile.bmap
        # an interval tree that records the weight of each usage site
        self.site_to_weights = IntervalTree.from_tuples(
            (
                bmap.char_to_byte(site.start),
                bmap.char_to_byte(site.stop),
                usage_weight(u),
            )
            for u in nonlocal_usages
            # filter out empty ranges since they are not supported by intervaltree
            if (site := u.use_site)
        )

    def get_node_weight(self, node: ts.Node) -> float:
        """Return the weight of the given node."""
        if node.end_byte - node.start_byte > self.node_size_limit:
            return 1.0
        return 1.0 + sum(
            x.data for x in self.site_to_weights.overlap(node.start_byte, node.end_byte)
        )

"""This module contains classes to augment FIM problems."""

import bisect
from abc import ABC, abstractmethod
from collections import defaultdict
from dataclasses import dataclass
from pathlib import Path
from random import Random

import tree_sitter as ts
from typing_extensions import override

from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Int<PERSON>ang<PERSON>
from base.static_analysis.common import guess_lang_from_fp
from base.static_analysis.parsing import (
    GlobalTsParser,
    SrcSpan,
    TsNodeType,
    TsParsedFile,
    WhitespaceHandler,
)
from research.fim.fim_sampling import FimProblem, SkipOrOutput
from research.fim.import_noising import (
    ImportDropout,
    ImportShuffling,
    ImportStatement,
    find_all_imports,
    is_import_noising_supported,
)


@dataclass
class FileAugmenter:
    """A class to randomly augment files. We randomly pick a single transformation
    to apply, (e.g. import dropout, import shuffling, or no transformation)."""

    import_dropout_prob: float = 0.0
    """The probability of applying import dropout."""

    import_shuffling_prob: float = 0.0
    """The probability of applying import shuffling."""

    import_dropout_rate: float = 0.1
    """During import dropout, the probability of dropping out an imported symbol."""

    import_shuffling_rate: float = 0.05
    """During import shuffling, each import statement has this probability of being
    randomly swapped."""

    def __post_init__(self):
        assert (
            self.import_dropout_prob + self.import_shuffling_prob
        ) <= 1.0, "import_dropout_prob + import_shuffling_prob > 1.0"
        self.noop_prob = 1.0 - self.import_dropout_prob - self.import_shuffling_prob

    def transform(self, pfile: TsParsedFile, rng: Random) -> TsParsedFile:
        """Apply a randomly selected transformation to the given file."""
        if not is_import_noising_supported(pfile.lang):
            return pfile

        import_dropout = ImportDropout(self.import_dropout_rate)
        import_shuffling = ImportShuffling(GlobalTsParser, self.import_shuffling_rate)

        weights = [self.noop_prob, self.import_dropout_prob, self.import_shuffling_prob]
        choice = rng.choices(range(len(weights)), weights=weights)[0]
        if choice == 0:
            return pfile
        elif choice == 1:
            return TsParsedFile.parse(
                pfile.path, pfile.lang, import_dropout.transform(pfile, rng)
            )
        else:
            return TsParsedFile.parse(
                pfile.path, pfile.lang, import_shuffling.transform(pfile, rng)
            )


class AbstractFimAugmenter(ABC):
    @abstractmethod
    def transform(
        self, fim_problem: FimProblem, pfile: TsParsedFile, rng: Random
    ) -> FimProblem | None:
        """Transform the given FIM problem.

        Precondition: the fim problem should be an output of CSTFimSampler.
        See FimRepresentation.from_cst_fim_problem specifically.

        Return None if the transformation could not be applied.
        """
        raise NotImplementedError()


@dataclass
class FimSpan:
    """A span in a FIM problem."""

    crange: CharRange
    """The source range of this span."""

    skipped: bool
    """Whether this span is skipped."""

    pause_at_end: bool
    """Whether this span is followed by a pause token."""

    @property
    def is_output(self) -> bool:
        """Whether this span has some output."""
        return not self.skipped or self.pause_at_end

    def to_src_span(self, code: str) -> SkipOrOutput[SrcSpan]:
        """Convert this FimSpan to SkipOrOutput[SrcSpan] via the given code."""
        return SkipOrOutput(
            content=SrcSpan(self.crange, code[self.crange.to_slice()]),
            skipped=self.skipped,
            pause_at_end=self.pause_at_end,
        )


# TODO(jeff): See AU-4026. All methods currently return IntRange and not IndexRange.
class IndexRange(IntRange):
    pass


@dataclass
class FimRepr:
    """Represents a FIM problem from a source file as a list of spans.

    In this format, spans in between the first and last output spans form the middle.
    The prefix and suffix are the spans before and after the middle.
    Thus, the prefix and suffix will have skipped=True and pause_at_end=False.
    """

    code: str
    """The full original source of the FIM problem."""

    spans: list[FimSpan]
    """The spans of the FIM problem."""

    middle_node_type: TsNodeType
    """The type of the first middle span."""

    file_path: Path
    """The path of the file from which this problem is constructed."""

    def show_spans(self, no_special_token: bool = False) -> str:
        """Pretty print the spans of this FIM problem."""

        def show_span(span: FimSpan, code: str):
            if no_special_token:
                return code[span.crange.to_slice()]
            skipped = "<|skip|>" if span.skipped else "<|output|>"
            pause = "<|pause|>" if span.pause_at_end else ""
            return f"{skipped}{code[span.crange.to_slice()]}{pause}"

        return "".join(show_span(span, self.code) for span in self.spans)

    def get_middle_index_range(self) -> IndexRange:
        """Return the middle spans of this FIM problem."""
        middle_start = -1
        for i, s in enumerate(self.spans):
            if s.is_output:
                middle_start = i
                break

        if middle_start == -1:
            raise ValueError("No middle spans in FimRepresentation")

        middle_end = -1
        for i, s in enumerate(reversed(self.spans)):
            if s.is_output:
                middle_end = len(self.spans) - i
                break

        return IndexRange(middle_start, middle_end)

    def swap_spans(self, span_id1: IndexRange, span_ids2: IndexRange):
        """Swap the spans in the given ranges."""
        a, b = span_id1, span_ids2
        if a > b:
            a, b = b, a
        if a.overlaps(b):
            raise ValueError(f"Ranges {a} and {b} overlap")

        s = self.spans
        spans = (
            s[: a.start]
            + s[b.start : b.stop]
            + s[a.stop : b.start]
            + s[a.start : a.stop]
            + s[b.stop :]
        )
        self.spans = spans

    def find_spans_in(self, crange: CharRange) -> list[IndexRange]:
        """Find all spans that are contained in the given ranges.

        Returns the result as the smallest possible list of index ranges.
        """
        result = [(0, 0)]
        for i, span in enumerate(self.spans):
            if crange.contains(span.crange):
                a, b = result[-1]
                if i == b:
                    result[-1] = (a, b + 1)
                else:
                    result.append((i, i + 1))
        result = [IndexRange(a, b) for a, b in result if a < b]
        return result

    def find_insertions(self, char_index: int) -> list[IndexRange]:
        """Find all insertion points for the given character index.

        An insertion point is an index into spans where if you inserted, the insertion
        would be adjacent to `char_index`, i.e. the prev span's stop or the next span's
        start is `char_index`.
        """
        result = set()
        for i, span in enumerate(self.spans):
            if span.crange.start == char_index:
                result.add(i)
            if span.crange.stop == char_index:
                result.add(i + 1)

        return [IndexRange(i, i) for i in sorted(result)]

    def cut_spans(self, cut_range: CharRange):
        """Cuts spans using the start and stop of the given non-point range.

        The FimRepr is transformed into an equivalent representation, where
        all spans either are contained in the range, entirely to the left, or entirely
        to the right of the range.

        This method will not produce *new* point ranges. Notably:
        1. If the range and span touch but don't overlap, we will not make any cuts.
        2. If the range and span overlap, there may be one to three post-cut spans,
        with cases being (middle,), (left, middle), (middle, right),
        or (left, middle, right), but they will always be non-point ranges, and only
        the last span can contain a pause.
        3. If the range is a point range, we only split spans that strictly contain it.
        """
        spans: list[FimSpan] = []
        for span in self.spans:
            if (
                cut_range.contains(span.crange)
                or not cut_range.touches(span.crange)
                or cut_range.adjoins(span.crange)
            ):
                spans.append(span)
                continue

            # By here, range and span overlap, or range is a strictly contained point.
            left, right = span.crange.split_by_range(cut_range)
            middle = span.crange.intersect(cut_range)
            end = CharRange(span.crange.stop, span.crange.stop)

            # Add each span to the appropriate list if a valid, non-point range.
            if left is not None and len(left) > 0:
                spans.append(
                    FimSpan(
                        left,
                        skipped=span.skipped,
                        pause_at_end=left.contains(end) and span.pause_at_end,
                    )
                )
            if middle is not None and len(middle) > 0:
                spans.append(
                    FimSpan(
                        middle,
                        skipped=span.skipped,
                        pause_at_end=middle.contains(end) and span.pause_at_end,
                    )
                )
            if right is not None and len(right) > 0:
                spans.append(
                    FimSpan(
                        right,
                        skipped=span.skipped,
                        pause_at_end=right.contains(end) and span.pause_at_end,
                    )
                )
        self.spans = spans

    def spans_contain(self, cranges: list[CharRange]) -> list[bool]:
        """Check for each range whether it is contained in any span."""
        # TODO(jeff): can be implemented more efficiently, but this is fine for now.
        # Left as a leetcode exercise for the reader.
        return [
            any(span.crange.contains(crange) for span in self.spans)
            for crange in cranges
        ]

    def spans_contain_filter(self, cranges: list[CharRange]) -> list[CharRange]:
        """Returns the ranges that are contained in some span."""
        spans_contain = self.spans_contain(cranges)
        return [crange for crange, c in zip(cranges, spans_contain) if c]

    def to_fim_problem(self) -> FimProblem:
        """Convert this FimRepr to FimProblem.

        Tries to conform to CSTFimSampler format, but only guaranteed if this is a
        roundtrip.
        """
        middle_range = self.get_middle_index_range()
        middle_spans = [
            s.to_src_span(self.code)
            for s in self.spans[middle_range.start : middle_range.stop]
        ]
        prefix_spans = [
            s.to_src_span(self.code) for s in self.spans[: middle_range.start]
        ]
        suffix_spans = [
            s.to_src_span(self.code) for s in self.spans[middle_range.stop :]
        ]

        prefix = "".join([s.content.code for s in prefix_spans])
        suffix = "".join([s.content.code for s in suffix_spans])
        prefix_span = (
            prefix_spans[0].content.range
            if prefix_spans and prefix_spans[0].content.range.start == 0
            else CharRange(0, 0)
        )
        suffix_span = (
            suffix_spans[-1].content.range
            if suffix_spans and suffix_spans[-1].content.range.stop == len(self.code)
            else CharRange(len(self.code), len(self.code))
        )
        skip_code = "".join([s.content.code for s in middle_spans if s.skipped])
        suffix = skip_code + suffix

        # Filter out empty spans from middle_spans to conform to CSTFimSampler format.
        middle_spans = [s for s in middle_spans if s.content.code or s.pause_at_end]

        # NOTE: prefix_span, suffix_span, original_middle_code may no longer match the
        # CSTFimSampler format if not at start/end of file, are repeated, have gaps,
        # etc.
        return FimProblem(
            prefix=SrcSpan(prefix_span, prefix),
            suffix=SrcSpan(suffix_span, suffix),
            middle_spans=tuple(middle_spans),
            file_path=self.file_path,
            middle_node_type=self.middle_node_type,
            original_middle_code=self.code[prefix_span.stop : suffix_span.start],
        )

    @staticmethod
    def from_cst_fim_problem(fim_problem: FimProblem, code: str) -> "FimRepr":
        """Constructs a FimRepr from a CSTFimSampler FimProblem."""
        # Compute the middle spans
        if any(
            code[s.content.range.to_slice()] != s.content.code
            for s in fim_problem.middle_spans
        ):
            raise ValueError("FimProblem middle spans do not match the given code.")
        middle_spans = [
            FimSpan(s.content.range, s.skipped, s.pause_at_end)
            for s in fim_problem.middle_spans
        ]

        # Assume the prefix is whole.
        if code[fim_problem.prefix.range.to_slice()] != fim_problem.prefix.code:
            raise ValueError("FimProblem prefix does not match the given code.")
        prefix_spans = [
            FimSpan(fim_problem.prefix.range, skipped=True, pause_at_end=False)
        ]

        # Compute the middle stop and add dummy spans if necessary.
        if not middle_spans:
            middle_spans.append(
                FimSpan(
                    CharRange(
                        fim_problem.prefix.range.stop, fim_problem.prefix.range.stop
                    ),
                    skipped=False,
                    pause_at_end=False,
                )
            )
        if not middle_spans[-1].is_output:
            index = middle_spans[-1].crange.stop
            middle_spans.append(
                FimSpan(CharRange(index, index), skipped=False, pause_at_end=False)
            )
        if not middle_spans[0].is_output:
            index = middle_spans[0].crange.start
            middle_spans.insert(
                0,
                FimSpan(CharRange(index, index), skipped=False, pause_at_end=False),
            )

        middle_stop = middle_spans[-1].crange.stop

        # The suffix is more complicated - it is the concatenation of skip code,
        # target leftover, and a real suffix. First check the real suffix.
        suffix_range = fim_problem.suffix.range
        non_suffix_len = len(fim_problem.suffix.code) - len(suffix_range)
        if code[suffix_range.to_slice()] != fim_problem.suffix.code[non_suffix_len:]:
            raise ValueError("FimProblem suffix does not match the given code.")

        # Then compute the skip from the middle_spans.
        skip_code = "".join(
            [s.content.code for s in fim_problem.middle_spans if s.skipped]
        )
        if fim_problem.suffix.code[: len(skip_code)] != skip_code:
            raise ValueError("FimProblem skips do not match the given code.")

        # Finally, compute the target leftover.
        leftover_len = non_suffix_len - len(skip_code)
        target_leftover = CharRange(middle_stop, middle_stop + leftover_len)
        if (
            fim_problem.suffix.code[len(skip_code) : non_suffix_len]
            != code[target_leftover.to_slice()]
        ):
            raise ValueError(
                "FimProblem target leftover does not match the given code."
            )

        suffix_spans = [
            FimSpan(target_leftover, skipped=True, pause_at_end=False),
            FimSpan(suffix_range, skipped=True, pause_at_end=False),
        ]

        spans = prefix_spans + middle_spans + suffix_spans
        return FimRepr(
            code=code,
            spans=spans,
            middle_node_type=fim_problem.middle_node_type,
            file_path=fim_problem.file_path,
        )


def find_valid_nodes(
    brange: ByteRange,
    pfile: TsParsedFile,
    valid_nodes: list[ts.Node],
    max_depth: int = 200,
) -> list[ts.Node] | None:
    """Checks if the range is valid (i.e. covered by valid nodes), and returns the
    smallest covering in tree order. Because of whitespace, formally, we check if
    the range overlaps only with valid nodes or ancestors of valid nodes.

    Returns None if the range overlaps a node that is not an ancestor of a valid node.
    """
    ancestor_ids = set()
    for node in valid_nodes:
        for _ in range(max_depth):
            if node.parent is None:
                break
            if node.parent.id in ancestor_ids:
                break
            ancestor_ids.add(node.parent.id)
            node = node.parent

    valid_map = {node.id: node for node in valid_nodes}

    def rec_find(node: ts.Node, result_ids: list[int]) -> bool:
        """Returns True if the result is valid."""
        node_brange = ByteRange(node.start_byte, node.end_byte)
        if not brange.overlaps(node_brange):
            return True

        if node.id in valid_map:
            result_ids.append(node.id)
            return True

        if node.id not in ancestor_ids:
            return False

        for child in node.children:
            if not rec_find(child, result_ids):
                return False
        return True

    result_ids: list[int] = []
    valid_result = rec_find(pfile.ts_tree.root_node, result_ids)
    if not valid_result:
        return None

    return [valid_map[id_] for id_ in result_ids]


@dataclass(frozen=True)
class NodeEndpoints:
    """A helper class to find the next start char and previous end char."""

    node_starts: list[int]
    """Sorted list of all node start chars."""

    node_ends: list[int]
    """Sorted list of all node end chars."""

    def __post__init__(self):
        assert len(self.node_starts) == len(self.node_ends)
        assert len(self.node_starts) > 0
        assert len(self.node_ends) > 0

    def next_start(self, char: int) -> int:
        """Find the first start char >= the given char.

        If none exist, return the largest end char.
        """
        index = bisect.bisect_left(self.node_starts, char)
        if index == len(self.node_starts):
            return self.node_ends[-1]
        return self.node_starts[index]

    def prev_end(self, char: int) -> int:
        """Find the last end char <= the given char.

        If none exist, return the smallest start char.
        """
        index = bisect.bisect_right(self.node_ends, char)
        if index == 0:
            return self.node_starts[0]
        return self.node_ends[index - 1]

    def expand_range(self, crange: CharRange) -> CharRange:
        """Expand the given range to the next start char and previous end char."""
        return CharRange(
            self.prev_end(crange.start),
            self.next_start(crange.stop),
        )

    @staticmethod
    def from_pfile(pfile: TsParsedFile, max_depth: int = 200) -> "NodeEndpoints":
        """Construct a NodeEndpoints from a parsed file."""
        node_starts = set()
        node_ends = set()
        node_starts.add(0)
        assert isinstance(pfile.code, str)
        node_ends.add(len(pfile.code))

        def rec(node: ts.Node, depth: int = 0) -> None:
            if depth >= max_depth:
                return
            if node.start_byte not in node_starts:
                node_starts.add(pfile.bmap.byte_to_char(node.start_byte))
            if node.end_byte not in node_ends:
                node_ends.add(pfile.bmap.byte_to_char(node.end_byte))
            for child in node.children:
                rec(child, depth + 1)

        rec(pfile.ts_tree.root_node)

        return NodeEndpoints(sorted(node_starts), sorted(node_ends))


@dataclass
class ImportShufflingAugmenter(AbstractFimAugmenter):
    """Class to move an import middle to a random import location."""

    @staticmethod
    def _get_middle_imports(
        middle_crange: ByteRange, pfile: TsParsedFile, imports: list[ts.Node]
    ) -> list[ts.Node] | None:
        """Finds all middle imports in the FimRepr.

        Returns None if the middle is not an import range (contains non-imports
        or contains no imports).
        """
        middle_brange = pfile.bmap.crange_to_brange(middle_crange)
        if len(middle_brange) == 0:
            return None
        middle_imports = find_valid_nodes(middle_brange, pfile, imports)
        if middle_imports is None or len(middle_imports) == 0:
            return None
        return middle_imports

    @staticmethod
    def _get_parent_node(middle_imports: list[ts.Node]) -> ts.Node | None:
        """Check that all imports have the same parent, and get that parent."""
        parent_nodes = [import_.parent for import_ in middle_imports]
        parent_node_ids = [
            parent_node.id if parent_node else None for parent_node in parent_nodes
        ]
        if len(set(parent_node_ids)) != 1:
            return None
        parent = parent_nodes[0]
        if parent is None:
            return None
        return parent

    @staticmethod
    def _find_swaps(
        middle_imports: list[ts.Node],
        other_imports: list[ts.Node],
        pfile: TsParsedFile,
        fim_repr: FimRepr,
    ) -> tuple[CharRange, list[CharRange]] | None:
        """Finds an import range and a list of candidate character ranges
        (possibly point-ranges) which can be swapped with the import range.

        Returns None if no such pair exists.
        """
        middle_imports_brange = ByteRange(
            middle_imports[0].start_byte, middle_imports[-1].end_byte
        )
        middle_imports_crange = pfile.bmap.brange_to_crange(middle_imports_brange)
        endpoints = NodeEndpoints.from_pfile(pfile)

        # The middle may contain additional whitespace, so we expand the cut range
        # to cover all possible whitespace. We then swap it with another whitespace
        # range. However, this gets very tricky, especially with blocks of imports
        # separated by two newlines, start of file, inner imports, etc. So, we try
        # to be conservative and make sure the swap range matches the cut range's
        # whitespace.
        cut_crange = endpoints.expand_range(middle_imports_crange)
        left_chars = pfile.code[cut_crange.start : middle_imports_crange.start]
        right_chars = pfile.code[middle_imports_crange.stop : cut_crange.stop]
        if not left_chars or not right_chars:
            return None

        # We want to match the larger whitespace range (or both if equal).
        # There are some edge cases around indentation, so we check that
        # the larger ends with the smaller.
        match_left = left_chars.endswith(right_chars)
        match_right = right_chars.endswith(left_chars)
        if not match_left and not match_right:
            return None

        other_import_cranges = [
            pfile.bmap.brange_to_crange(ByteRange(node.start_byte, node.end_byte))
            for node in other_imports
        ]
        other_import_cranges = fim_repr.spans_contain_filter(other_import_cranges)

        swap_cranges = set[CharRange]()
        for node in other_import_cranges:
            if match_left:
                swap_crange = endpoints.expand_range(CharRange.point(node.start))
                swap_chars = pfile.code[swap_crange.to_slice()]
                if swap_chars.endswith(left_chars):
                    swap_cranges.add(
                        CharRange(swap_crange.stop - len(left_chars), swap_crange.stop)
                    )
            if match_right:
                swap_crange = endpoints.expand_range(CharRange.point(node.stop))
                swap_chars = pfile.code[swap_crange.to_slice()]
                if swap_chars.startswith(right_chars):
                    swap_cranges.add(
                        CharRange(
                            swap_crange.start, swap_crange.start + len(right_chars)
                        )
                    )

        swap_cranges = [c for c in swap_cranges if not cut_crange.touches(c)]
        swap_cranges = fim_repr.spans_contain_filter(swap_cranges)
        swap_cranges = sorted(swap_cranges)

        if len(swap_cranges) == 0:
            return None
        return cut_crange, swap_cranges

    @override
    def transform(
        self, fim_problem: FimProblem, pfile: TsParsedFile, rng: Random
    ) -> FimProblem | None:
        if not is_import_noising_supported(pfile.lang):
            return None

        fim_repr = FimRepr.from_cst_fim_problem(fim_problem, pfile.code)
        imports = list(find_all_imports(pfile.ts_tree.root_node, pfile.lang))
        import_nodes = [import_.stmt for import_ in imports]

        # Get the middle import nodes, and check if parent is None
        middle_spans = fim_repr.spans[fim_repr.get_middle_index_range().to_slice()]
        # NOTE: assumes that middle spans are in order
        middle_crange = middle_spans[0].crange.merge(middle_spans[-1].crange)
        middle_imports = self._get_middle_imports(middle_crange, pfile, import_nodes)
        if middle_imports is None:
            return None

        parent = self._get_parent_node(middle_imports)
        if parent is None:
            return None

        # Get other imports in the parent
        import_node_set = set(import_node.id for import_node in import_nodes)
        middle_import_set = set(import_node.id for import_node in middle_imports)
        other_import_set = import_node_set - middle_import_set
        other_imports = [
            node for node in parent.children if node.id in other_import_set
        ]

        # Find the swaps
        swaps = self._find_swaps(middle_imports, other_imports, pfile, fim_repr)
        if swaps is None:
            return None

        cut_crange, swap_cranges = swaps

        # This should never happen, but check just in case.
        if not cut_crange.contains(middle_crange):
            return None

        # Pick a random swap location.
        swap_crange = rng.choice(swap_cranges)

        # Finally perform the cut and swap.
        fim_repr.cut_spans(cut_crange)
        fim_repr.cut_spans(swap_crange)

        cut_indices = fim_repr.find_spans_in(cut_crange)
        if len(cut_indices) != 1:
            return None

        if swap_crange.is_point():
            swap_indices = fim_repr.find_insertions(swap_crange.start)
        else:
            swap_indices = fim_repr.find_spans_in(swap_crange)
        if len(swap_indices) != 1:
            return None

        fim_repr.swap_spans(cut_indices[0], swap_indices[0])
        return fim_repr.to_fim_problem()

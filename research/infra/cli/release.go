package cli

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"

	"github.com/augmentcode/augment/research/infra/lib/augment/release"
)

func init() {
	bgrp := &cobra.Group{
		ID:    "root_build",
		Title: "Build and Release Commands",
	}
	addCommand(nil, bgrp, releaseRoot())
}

func releaseRoot() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "release",
		Aliases: []string{"rel"},
		Short:   "Commands for working with augi releases. (Updates are normally AUTOMATED).",
	}
	addCommand(cmd, nil, releasePush(), ADMIN)
	addCommand(cmd, nil, releaseList())
	addCommand(cmd, nil, releaseSync())
	addCommand(cmd, nil, releaseSelect())
	addCommand(cmd, nil, releaseUpdate())

	ver := versionCmd()
	ver.Short += " (alias for `augi version`)."
	addCommand(cmd, nil, ver)
	return cmd
}

func releaseClient(cmd *cobra.Command, ro bool) func() *release.Client {
	flags := struct {
		dryRun bool
		conf   string
		reg    string
		repo   string
	}{}
	defConfig := func() string {
		if !ro {
			return ""
		}
		roConfig := "/run/augment/secrets/augi-release-readers/.dockerconfigjson"
		if _, err := os.Stat(roConfig); err == nil {
			return roConfig
		}
		return ""
	}()
	cmd.Flags().StringVar(&flags.conf, "config-file", defConfig, "Path to Docker config.json, leave empty for default.")
	cmd.Flags().StringVar(&flags.reg, "registry", "harbor-master.r.augmentcode.com", "")
	cmd.Flags().StringVar(&flags.repo, "repo", "augi/augi", "")
	return func() *release.Client {
		return release.NewClient(flags.conf, flags.reg, flags.repo)
	}
}

func releasePush() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "push [--dry-run] [--verbose] [<version> [labels...]]",
		Short: "Package self into an OCI Artifact and Push. Version is [YYYY-MM-DD.]N. Defaults to auto-calculated version and 'live' label.",
		Args:  cobra.ArbitraryArgs,
	}

	flags := struct {
		dryRun  bool
		verbose bool
		force   bool
		native  bool
		update  bool
	}{}
	cmd.Flags().BoolVarP(&flags.dryRun, "dry-run", "n", false, "Dry-run.")
	cmd.Flags().BoolVarP(&flags.verbose, "verbose", "v", false, "Verbose output.")
	cmd.Flags().BoolVarP(&flags.force, "force", "f", false, "Force push attempt, even if version checks fail.")
	cmd.Flags().BoolVar(&flags.native, "native", false, "Use native 'go build' rather than bazel.")
	cmd.Flags().BoolVarP(&flags.update, "update", "U", false, "Update workspace with 'go get; bazel mod tidy; gazelle' prior to build.")
	clifunc := releaseClient(cmd, false)

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		cli := clifunc()

		version, labels := func() (string, []string) {
			if len(args) == 0 {
				return "", []string{"live"}
			} else {
				return args[0], args[1:]
			}
		}()
		if version == "" || version == "-" || version == "." {
			var err error
			if version, err = cli.NextVersion(cmd.Context()); err != nil {
				return err
			}
		}

		tmpPath := "/tmp/augi." + version
		if err := buildTmp(cmd.Context(), tmpPath, version, flags.native, flags.update); err != nil {
			return err
		}

		r, err := cli.MakeRelease(tmpPath, version, labels...)
		if err != nil {
			return err
		}
		if flags.verbose {
			r.Println()
		}
		return cli.PutRelease(cmd.Context(), r, flags.dryRun, flags.force)
	}
	return cmd
}

func releaseList() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "list",
		Short: "List releases and basic info from registry.",
		Args:  cobra.NoArgs,
	}
	clifunc := releaseClient(cmd, true)
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		cli := clifunc()
		repo, err := cli.GetRepo(cmd.Context())
		if err != nil {
			return err
		}
		fmt.Print(repo.Info())
		return nil
	}
	return cmd
}

func releaseSync() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "sync [--dry-run|-n] [--force] [--no-links|-L] [version...]",
		Aliases: []string{"upgrade"},
		Short:   "Download new versions to the filesystem and manage symlinks.",
		Args:    cobra.ArbitraryArgs,
	}

	flags := struct {
		dry     bool
		force   bool
		nolinks bool
		path    string
	}{}
	cmd.Flags().BoolVarP(&flags.dry, "dry-run", "n", false, "Dry-run, log but don't do anything.")
	cmd.Flags().BoolVarP(&flags.force, "force", "f", false, "Force.")
	cmd.Flags().BoolVarP(&flags.nolinks, "no-links", "L", false, "Only handle versioned binaries, don't setup symlinks.")
	cmd.Flags().StringVar(&flags.path, "path", "/usr/local/bin", "Path to install directory.")
	clifunc := releaseClient(cmd, true)

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		versions := args
		cli := clifunc()
		cli.SetBaseDir(flags.path)
		return cli.SyncFS(cmd.Context(), flags.dry, flags.force, !flags.nolinks, versions...)
	}
	return cmd
}

func releaseSelect() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "select [--dry-run] [--version] <label>",
		Aliases: []string{},
		Short:   "Select label in --path.",
		Args:    cobra.ExactArgs(1),
	}

	flags := struct {
		dry  bool
		path string
		ver  bool
	}{}
	cmd.Flags().BoolVarP(&flags.dry, "dry-run", "n", false, "Dry-run, log but don't do anything.")
	cmd.Flags().StringVar(&flags.path, "path", "/usr/local/bin", "Path to install directory.")
	cmd.Flags().BoolVar(&flags.ver, "version", false, "Treat <label> as a version in augi.release/augi.<version>.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		label := args[0]
		cli := release.NewClient("", "", "augi/augi")
		cli.SetBaseDir(flags.path)
		return cli.Select(label, flags.ver, flags.dry)
	}
	return cmd
}

func releaseUpdate() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "update [--dry-run]",
		Aliases: []string{"upgrade"},
		Short:   "Update augi symlinks to the current release.",
		Args:    cobra.NoArgs,
	}

	flags := struct {
		dry  bool
		path string
	}{}
	cmd.Flags().BoolVarP(&flags.dry, "dry-run", "n", false, "Dry-run, log but don't do anything.")
	cmd.Flags().StringVar(&flags.path, "path", "/usr/local/bin", "Path to install directory.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		cli := release.NewClient("", "", "augi/augi")
		cli.SetBaseDir(flags.path)
		return cli.Update(flags.dry)
	}
	return cmd
}

package cli

import (
	"fmt"
	"os"
	"strings"

	"github.com/spf13/cobra"

	"github.com/augmentcode/augment/infra/lib/github"
	"github.com/augmentcode/augment/research/infra/lib/augment"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod/ctrl"
	"github.com/augmentcode/augment/research/infra/lib/augment/tokens"
)

func init() {
	addCommand(nil, nil, userRoot())
}

func userRoot() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "user",
		Aliases: []string{"users"},
		Short:   "Commands for working with Augment users.",
	}
	cmd.PersistentFlags().StringP("user", "u", augment.WhoAmI(), "Username to operate on.")
	cmd.PersistentFlags().String("keys-configmap", augment.AuthorizedKeysConfigMap, "Name of k8s ConfigMap.")
	cmd.PersistentFlags().String("github-configmap", augment.GitHubUserNameConfigMap, "Name of GitHub UserName ConfigMap.")
	cmd.PersistentFlags().String("github-username", "", "GitHub username, defaults to lookup from K8s ConfigMap.")

	cmdssh := &cobra.Command{
		Use:   "ssh",
		Short: "Commands for working with SSH (Authorized Keys)",
	}
	addCommand(cmdssh, nil, userSSHGet())
	addCommand(cmdssh, nil, userSSHValidate())
	addCommand(cmdssh, nil, userSSHSet())
	addCommand(cmdssh, nil, userSSHAppend())

	cmdgh := &cobra.Command{
		Use:     "github",
		Aliases: []string{"gh"},
		Short:   "Commands for working with GitHub",
	}
	addCommand(cmdgh, nil, userGHUserName())
	addCommand(cmdgh, nil, userGHSSHKeys())

	addCommand(cmd, nil, cmdssh)
	addCommand(cmd, nil, cmdgh)
	addCommand(cmd, nil, userK8sSync(), ADMIN)
	addCommand(cmd, nil, userSASync(), ADMIN)
	addCommand(cmd, nil, userSAKubeconfigs())
	addCommand(cmd, nil, userTokensFromGH())
	addCommand(cmd, nil, userClusterApply(), ADMIN)
	addCommand(cmd, nil, userClusterDelete(), ADMIN)

	return cmd
}

func getClientsOrDie(cmd *cobra.Command) augment.Clients {
	context, err := cmd.Flags().GetString("context")
	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to build K8s client: %v.", err)
		os.Exit(1)
	}
	cli, err := augment.NewClients(context)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to build K8s client: %v.", err)
		os.Exit(1)
	}
	if cmd.Flags().Lookup("keys-configmap") != nil {
		if cm, err := cmd.Flags().GetString("keys-configmap"); err != nil {
			fmt.Fprintf(os.Stderr, "Unable to build K8s client: %v.", err)
			os.Exit(1)
		} else {
			cli.SetAuthorizedKeysConfigMap(cm)
		}
	}
	if cmd.Flags().Lookup("github-configmap") != nil {
		if cm, err := cmd.Flags().GetString("github-configmap"); err != nil {
			fmt.Fprintf(os.Stderr, "Unable to build K8s client: %v.", err)
			os.Exit(1)
		} else {
			cli.SetGitHubUserNameConfigMap(cm)
		}
	}
	return cli
}

func userSSHGet() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "get-keys",
		Short: "Get SSH Authorized Keys for a user.",
		Args:  cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			user, err := cmd.Flags().GetString("user")
			if err != nil {
				return err
			}
			cli := getClientsOrDie(cmd)
			keys, err := cli.AuthorizedKeys(cmd.Context(), user, augment.OptFromK8s())
			if err != nil {
				return err
			}
			fmt.Println(keys)
			return nil
		},
	}
	return cmd
}

func userSSHValidate() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "validate-keys [<file>]",
		Short: "Validate SSH Authorized Keys for a user (stored in k8s, or local file).",
		Args:  cobra.RangeArgs(0, 1),
		RunE: func(cmd *cobra.Command, args []string) error {
			user, err := cmd.Flags().GetString("user")
			if err != nil {
				return err
			}
			cli := getClientsOrDie(cmd)
			if count, err := cli.ValidateAuthorizedKeysFrom(cmd.Context(), user,
				augment.OptFromFile(func() string {
					if len(args) > 0 {
						return args[0]
					}
					return ""
				}()),
				augment.OptFromK8s(len(args) == 0),
			); err != nil {
				return err
			} else {
				fmt.Printf("All %d keys are valid.\n", count)
			}

			return nil
		},
	}
	return cmd
}

func userSSHSet() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "set-keys [<file> | --from-github]",
		Short: "Set SSH Authorized Keys from a local file, github",
		Args:  cobra.RangeArgs(0, 1),
		RunE: func(cmd *cobra.Command, args []string) error {
			user, err := cmd.Flags().GetString("user")
			if err != nil {
				return err
			}
			fromGH, err := cmd.Flags().GetBool("from-github")
			if err != nil {
				return err
			}
			fromFile := func() string {
				if len(args) > 0 {
					return args[0]
				}
				if !fromGH {
					return "/dev/stdin"
				}
				return ""
			}()
			ghUser, err := cmd.Flags().GetString("github-username")
			if err != nil {
				return err
			}

			cli := getClientsOrDie(cmd)
			_, err = cli.AuthorizedKeysSet(cmd.Context(), user,
				augment.OptFromFile(fromFile),
				augment.OptFromGitHub(fromGH),
				augment.OptGitHubUser(ghUser),
				augment.OptAppend(false),
			)
			return err
		},
	}
	cmd.Flags().Bool("from-github", false, "Get keys from GitHub.")
	return cmd
}

func userSSHAppend() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "append-keys [<file> | --from-github]",
		Short: "Append SSH Authorized Keys from a local file, github, or stdin",
		Args:  cobra.RangeArgs(0, 1),
		RunE: func(cmd *cobra.Command, args []string) error {
			user, err := cmd.Flags().GetString("user")
			if err != nil {
				return err
			}
			fromGH, err := cmd.Flags().GetBool("from-github")
			if err != nil {
				return err
			}
			fromFile := func() string {
				if len(args) > 0 {
					return args[0]
				}
				if !fromGH {
					return "/dev/stdin"
				}
				return ""
			}()
			ghUser, err := cmd.Flags().GetString("github-username")
			if err != nil {
				return err
			}

			cli := getClientsOrDie(cmd)
			_, err = cli.AuthorizedKeysSet(cmd.Context(), user,
				augment.OptFromFile(fromFile),
				augment.OptFromGitHub(fromGH),
				augment.OptGitHubUser(ghUser),
				augment.OptAppend(true),
			)
			return err
		},
	}
	cmd.Flags().Bool("from-github", false, "Get keys from GitHub.")
	return cmd
}

func userGHSSHKeys() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "ssh-keys [--github-username=<github-username>]",
		Short: "Get SSH Keys stored in GitHub.",
		Args:  cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			ghUser, err := cmd.Flags().GetString("github-username")
			if err != nil {
				return err
			}
			user, err := cmd.Flags().GetString("user")
			if err != nil {
				return err
			}

			cli := getClientsOrDie(cmd)
			if keys, err := cli.AuthorizedKeys(cmd.Context(), user,
				augment.OptFromGitHub(),
				augment.OptGitHubUser(ghUser),
			); err != nil {
				return err
			} else {
				fmt.Println(keys)
			}

			return nil
		},
	}
	return cmd
}

func userGHUserName() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "username [--set=<new-username>]",
		Short: "Manage the github username associated with the user.",
		Args:  cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			user, err := cmd.Flags().GetString("user")
			if err != nil {
				return err
			}
			setName, err := cmd.Flags().GetString("set")
			if err != nil {
				return err
			}

			cli := getClientsOrDie(cmd)

			if setName != "" {
				return cli.GitHubUserNameSet(cmd.Context(), user, setName)
			} else {
				name, err := cli.GitHubUserName(cmd.Context(), user)
				if err != nil {
					return err
				}
				fmt.Println(name)
			}

			return nil
		},
	}
	cmd.Flags().String("set", "", "New GitHub username to set.")
	return cmd
}

func userK8sSync() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "os-set-all [--daemon] [--no-create-home]",
		Short: "Create OS users from the AuthorizedKeys config map.",
		Args:  cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			daemon, err := cmd.Flags().GetBool("daemon")
			if err != nil {
				return err
			}
			noCreateHome, err := cmd.Flags().GetBool("no-create-home")
			if err != nil {
				return err
			}
			cli := getClientsOrDie(cmd)
			if daemon {
				return cli.OSUserSetRun(cmd.Context(), !noCreateHome)
			} else {
				return cli.OSUserSetAllFromConfigMap(cmd.Context(), nil, !noCreateHome)
			}
		},
	}
	cmd.Flags().BoolP("daemon", "d", false, "Run continuously.")
	cmd.Flags().BoolP("no-create-home", "M", false, "Don't Create home directories.")
	return cmd
}

func userSASync() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "sa-sync [--daemon] [user...]",
		Short: "Create per-user K8s ServiceAccounts and RBAC policies from the AuthorizedKeys config map.",
		Args:  cobra.ArbitraryArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			daemon, err := cmd.Flags().GetBool("daemon")
			if err != nil {
				return err
			}
			if daemon && len(args) > 0 {
				return fmt.Errorf("The --daemon flag can only be used without passing a [user].")
			}

			cli := getClientsOrDie(cmd)

			if len(args) > 0 {
				for _, user := range args {
					if err := cli.UserSASync(cmd.Context(), user); err != nil {
						return err
					}
				}
				return nil
			} else if daemon {
				return cli.UserSASyncRun(cmd.Context())
			} else {
				return cli.UserSASyncAllFromConfigMap(cmd.Context(), nil)
			}
		},
	}
	cmd.Flags().BoolP("daemon", "d", false, "Run continuously.")
	return cmd
}

func userSAKubeconfigs() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "sa-kubeconfig [--stdout] [--daemon] [user...]",
		Short: "Write per-user Kubeconfigs for ServiceAccoutns from the AuthorizedKeys config map.",
		Args:  cobra.ArbitraryArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			stdout, err := cmd.Flags().GetBool("stdout")
			if err != nil {
				return err
			}
			daemon, err := cmd.Flags().GetBool("daemon")
			if err != nil {
				return err
			}
			if daemon && len(args) > 0 {
				return fmt.Errorf("The --daemon flag can only be used without passing a [user].")
			}

			cli := getClientsOrDie(cmd)

			if len(args) > 0 {
				for _, user := range args {
					if err := cli.UserSAWriteXdgOrPrint(cmd.Context(), user, stdout); err != nil {
						return err
					}
				}
				return nil
			} else if daemon {
				return cli.UserSAWriteXdgOrPrintRun(cmd.Context(), stdout)
			} else {
				return cli.UserSAWriteXdgOrPrintFromCM(cmd.Context(), nil, stdout)
			}
		},
	}
	cmd.Flags().BoolP("daemon", "d", false, "Run continuously.")
	cmd.Flags().Bool("stdout", false, "Write to stdout instead of XDG_RUNTIME_DIRs.")
	return cmd
}

func userTokensFromGH() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "tokens [--from-gh=<ref>|--from-k8s=<sec>] [--to-k8s=<sec>|--serve=<port>]",
		Short: "Gets user tokens from GitHub (default) or K8s and writes to stdout (default) or K8s.",
		Args:  cobra.NoArgs,
	}

	flags := struct {
		fromGHRef  string
		owner      string
		repo       string
		ghtok      string
		fromK8sSec string
		toK8sSec   string
		port       int32
	}{}
	cmd.Flags().StringVar(&flags.fromGHRef, "from-gh", "main", "The git ref (commit, branch, ...) to use as the token source.")
	cmd.Flags().StringVarP(&flags.owner, "owner", "o", "augmentcode", "The owning organization.")
	cmd.Flags().StringVarP(&flags.repo, "repo", "r", "augment", "The owning repo.")
	cmd.Flags().StringVarP(&flags.ghtok, "github-token", "G", "", "A literal token, from a `dir://`, or from a K8s secret if prefixed with `sec://`.")
	cmd.Flags().StringVar(&flags.fromK8sSec, "from-k8s", "", "A K8s secret to use as the token source.")
	cmd.Flags().StringVar(&flags.toK8sSec, "to-k8s", "", "A K8s secret to use as the token output.")
	cmd.Flags().Int32Var(&flags.port, "serve", 0, "Run a token auth server.")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		ctx := cmd.Context()
		k8s := getK8sOrDie()

		if cmd.Flags().Changed("from-gh") && cmd.Flags().Changed("from-k8s") {
			return fmt.Errorf("Only one of --from-gh XOR --from-k8s may be used.")
		}
		if cmd.Flags().Changed("to-k8s") && cmd.Flags().Changed("serve") {
			return fmt.Errorf("Only one of --to-k8s XOR --serve may be used.")
		}
		if cmd.Flags().Changed("serve") && !cmd.Flags().Changed("from-k8s") {
			return fmt.Errorf("Only --from-k8s is valid with --serve.")
		}

		/// SERVE
		if cmd.Flags().Changed("serve") {
			return tokens.ServeFromK8s(ctx, k8s, flags.fromK8sSec, flags.port)
		}

		/// SOURCE
		db, err := func() (*tokens.DB, error) {
			if cmd.Flags().Changed("from-k8s") {
				return tokens.FromK8s(ctx, k8s, flags.fromK8sSec)
			}
			if gh, err := func() (*github.Client, error) {
				if secname, ok := strings.CutPrefix(flags.ghtok, "sec://"); ok {
					sec, err := k8s.GetSecret(cmd.Context(), secname)
					if err != nil {
						return nil, err
					}
					return github.NewAppInstClient(flags.owner, flags.repo, sec.Key("app_id"), sec.Key("installation_id"), sec.Key("private-key.pem"))
				} else if dname, ok := strings.CutPrefix(flags.ghtok, "dir://"); ok {
					if !strings.HasPrefix(dname, "/") && !strings.HasPrefix(dname, "./") {
						dname = "./" + dname
					}
					return github.NewAppInstClient(flags.owner, flags.repo, dname+"/app_id", dname+"/installation_id", dname+"/private-key.pem")
				} else {
					gh := github.NewClient(flags.owner, flags.repo, flags.ghtok)
					return &gh, nil
				}
			}(); err != nil {
				return nil, err
			} else {
				return tokens.FromGitHub(cmd.Context(), gh, flags.fromGHRef)
			}
		}()
		if err != nil {
			return err
		}

		/// OUTPUT
		if flags.toK8sSec != "" {
			return db.ToK8s(ctx, k8s, flags.toK8sSec)
		} else {
			jtxt, err := db.JSON()
			if err != nil {
				return err
			}
			fmt.Println(jtxt)
			return nil
		}
	}

	return cmd
}

func userClusterApply() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "cluster-apply [-c <cluster>] [--no-diff] [-w|--wet-run] [user...]",
		Short: "Apply user-specific cluster configuration. With no users, apply all users.",
		Args:  cobra.ArbitraryArgs,
	}

	flags := struct {
		cluster string
		noDiff  bool
		wetRun  bool
	}{}
	cmd.Flags().StringVarP(&flags.cluster, "cluster", "c", "", "The cluster to apply to. Defaults to the current cluster.")
	cmd.Flags().BoolVar(&flags.noDiff, "no-diff", false, "Don't log diffs or skip Apply() on no diff.")
	cmd.Flags().BoolVarP(&flags.wetRun, "wet-run", "w", false, "Apply the changes to the cluster.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		cluster, err := clustersGetCluster(cmd)
		if err != nil {
			return err
		}
		c, err := ctrl.NewController(cmd.Context(), cluster, ctrl.WetRun(flags.wetRun), ctrl.NoDiff(flags.noDiff))
		if err != nil {
			return err
		}
		return c.ApplyUsers(cmd.Context(), args...)
	}
	return cmd
}

func userClusterDelete() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "cluster-delete [-c <cluster>] [-w|--wet-run] [--purge] user...",
		Short: "Delete user-specific cluster configuration.",
		Args:  cobra.ArbitraryArgs,
	}

	flags := struct {
		cluster string
		wetRun  bool
		purge   bool
	}{}
	cmd.Flags().StringVarP(&flags.cluster, "cluster", "c", "", "The cluster to apply to. Defaults to the current cluster.")
	cmd.Flags().BoolVarP(&flags.wetRun, "wet-run", "w", false, "Apply the changes to the cluster.")
	cmd.Flags().BoolVarP(&flags.purge, "purge", "p", false, "Purge the user from the cluster, including all personal data.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		cluster, err := clustersGetCluster(cmd)
		if err != nil {
			return err
		}
		c, err := ctrl.NewController(cmd.Context(), cluster, ctrl.WetRun(flags.wetRun))
		if err != nil {
			return err
		}
		return c.DeleteUsers(cmd.Context(), flags.purge, args...)
	}
	return cmd
}

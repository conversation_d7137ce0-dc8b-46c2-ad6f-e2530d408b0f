package cli

import (
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/spf13/cobra"

	"github.com/augmentcode/augment/research/infra/lib/gcs"
)

func init() {
	addCommand(nil, nil, gcsRoot())
}

func gcsRoot() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "gcs",
		Short: "Utils for working with GCS.",
	}
	addCommand(cmd, nil, gcsListFolders())
	addCommand(cmd, nil, gcsPruneFolders())
	addCommand(cmd, nil, gcsDeleteFolder())
	return cmd
}

func gcsListFolders() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "list-folders [-q] [--empty|-e|--tree|-t] [-F] [-S] <bucket>[/<prefix>/]",
		Aliases: []string{"lsf", "ls-folders"},
		Short:   "List folders in the bucket (for buckets with hierarchical namespaces enabled).",
		Long: strings.Join([]string{
			"By default, this command uses the Storage Control ListFolders() API to list Hierarchical Namespace folders. GCS automatically ",
			"creates Folders as needed. Note that Folders can exist that don't appear in the normal Bucket/Object API; these can accumulate ",
			"over time, hurting GCS performance -- these are mainly an issue caused by tooling pre-HNS support.",
			"",
			"This command can also be used to list empty Folders with the --empty (-e) or --tree (-t) flag.",
			"",
		}, "\n"),
		Args: cobra.ExactArgs(1),
	}
	flags := struct {
		quiet          bool
		empty          bool
		tree           bool
		fullyQualified bool
		stripPrefix    bool
	}{}
	cmd.Flags().BoolVarP(&flags.quiet, "quiet", "q", false, "Be quieter, do not log timing information.")
	cmd.Flags().BoolVarP(&flags.empty, "empty", "e", false, "List empty Folders.")
	cmd.Flags().BoolVarP(&flags.tree, "tree", "t", false, "Print in a tree, indicating **empty** Folders in the output.")
	cmd.Flags().BoolVarP(&flags.fullyQualified, "fully-qualified", "F", false, "Include the fully-qualified 'projects/_/buckets/*/folders' folder path prefix used by the Storage Control API.")
	cmd.Flags().BoolVarP(&flags.stripPrefix, "strip-prefix", "S", false, "If a prefix is provided, strip it from the output. An empty-line will represent the root folder. (Cannot be used with -F).")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		ctx := cmd.Context()
		bucket, prefix, _ := strings.Cut(args[0], "/")

		bkt, err := gcs.NewBucketClient(ctx, bucket)
		if err != nil {
			return err
		}

		if flags.tree {
			start := time.Now()
			if err := bkt.PrintObjectTree(ctx, prefix, os.Stdout); err != nil {
				return err
			}
			if !flags.quiet {
				bkt.LogInfo("Listed objects and printed tree in %v.", time.Now().Sub(start))
			}
			return nil
		}

		start := time.Now()
		label := ""
		folders, err := func() ([]string, error) {
			if flags.empty {
				label = " empty"
				return bkt.EmptyFolders(ctx, prefix)
			} else {
				return bkt.ListFolders(ctx, prefix, flags.fullyQualified, flags.stripPrefix)
			}
		}()
		if err != nil {
			return err
		}
		if !flags.quiet {
			bkt.LogInfo("Listed %d%s Folders in %v.", len(folders), label, time.Now().Sub(start))
		}

		for _, folder := range folders {
			fmt.Println(folder)
		}

		return nil
	}
	return cmd
}

func gcsPruneFolders() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "prune-folders [-q] [--dry-run|-n] <bucket>[/<prefix>/]",
		Aliases: []string{"prune"},
		Short:   "Delete empty folders.",
		Args:    cobra.ExactArgs(1),
	}
	flags := struct {
		quiet  bool
		dryrun bool
	}{}
	cmd.Flags().BoolVarP(&flags.quiet, "quiet", "q", false, "Be quieter, do not log each folder.")
	cmd.Flags().BoolVarP(&flags.dryrun, "dry-run", "n", false, "Dry-run, don't actually delete anything.")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		ctx := cmd.Context()
		bucket, prefix, _ := strings.Cut(args[0], "/")

		bkt, err := gcs.NewBucketClient(ctx, bucket)
		if err != nil {
			return err
		}

		start := time.Now()
		folders, err := bkt.EmptyFolders(ctx, prefix)
		if err != nil {
			return err
		}
		bkt.LogInfo("Listed %d empty Folders in %v.", len(folders), time.Now().Sub(start))

		start = time.Now()
		if err := bkt.DeleteFolders(ctx, flags.dryrun, flags.quiet, folders...); err != nil {
			return err
		}

		pfx := ""
		if flags.dryrun {
			pfx = "[dry-run] "
		}
		bkt.LogInfo("%sDeleted %d empty folders in %v.", pfx, len(folders), time.Now().Sub(start))

		return nil
	}
	return cmd
}

func gcsDeleteFolder() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "delete-folder [--dry-run|-n] <bucket>[/<folder>/]",
		Aliases: []string{},
		Short:   "Call DeleteFolder() on a single folder.",
		Args:    cobra.ExactArgs(1),
	}
	flags := struct {
		dryrun bool
	}{}
	cmd.Flags().BoolVarP(&flags.dryrun, "dry-run", "n", false, "Dry-run, don't actually delete anything.")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		ctx := cmd.Context()
		bucket, folder, _ := strings.Cut(args[0], "/")

		bkt, err := gcs.NewBucketClient(ctx, bucket)
		if err != nil {
			return err
		}

		return bkt.DeleteFolder(ctx, flags.dryrun, false, folder)
	}
	return cmd
}

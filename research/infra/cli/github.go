package cli

import (
	"fmt"
	"strings"

	"github.com/spf13/cobra"

	"github.com/augmentcode/augment/infra/lib/github"
)

func init() {
	addCommand(nil, nil, githubRoot())
}

func githubRoot() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "github",
		Aliases: []string{"gh"},
		Short:   "Commands for working with GitHub.",
	}
	addCommand(cmd, nil, githubJWT())
	addCommand(cmd, nil, githubInstallationToken())
	addCommand(cmd, nil, githubSSHKeys())
	addCommand(cmd, nil, githubRunners())
	addCommand(cmd, nil, githubPurgeOfflineRunners(), ADMIN)
	return cmd
}

func githubJWT() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "jwt <k8s-secret> | <k8s-secret-mountpoint> | <app-id> <key>",
		Short: "Generate a JWT for a GitHub App.",
		RunE: func(cmd *cobra.Command, args []string) error {
			cli := getClientsOrDie(cmd)
			if gen, err := cli.GitHubTokenGeneratorFromArgs(cmd.Context(), args); err != nil {
				return err
			} else if jwt, err := gen.FormatJWT(); err != nil {
				return err
			} else {
				fmt.Println(jwt)
			}
			return nil
		},
	}
	return cmd
}

func githubInstallationToken() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "installation-token <k8s-secret> | <k8s-secret-mountpoint> | <app-id> <inst-id> <key>",
		Short:   "Generate an Installation Access Token for a GitHub App.",
		Aliases: []string{"it"},
		RunE: func(cmd *cobra.Command, args []string) error {
			cli := getClientsOrDie(cmd)
			if gen, err := cli.GitHubTokenGeneratorFromArgs(cmd.Context(), args); err != nil {
				return err
			} else if tok, _, err := gen.GetAccessToken(cmd.Context()); err != nil {
				return err
			} else {
				fmt.Println(tok)
			}
			return nil
		},
	}
	return cmd
}

func githubSSHKeys() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "ssh-keys <user> [--augment|-A]",
		Short:   "Retrieve SSH public keys registered with github. With --augment, <user> is the augment username and mapped to github if available.",
		Aliases: []string{"ssh"},
		Args:    cobra.ExactArgs(1),
	}

	flags := struct {
		augment bool
	}{}
	cmd.Flags().BoolVarP(&flags.augment, "augment", "A", false, "Treat <user> as an augment username, and lookup the github username if we have it.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		user := args[0]

		cli := getClientsOrDie(cmd)
		getter := cli.GitHubSSHKeysForGHUser
		if flags.augment {
			getter = cli.GitHubSSHKeys
		}

		if keys, err := getter(cmd.Context(), user); err != nil {
			return err
		} else {
			fmt.Println(keys)
			return nil
		}
	}
	return cmd
}

func githubClientGetter(cmd *cobra.Command) func() (github.Client, error) {
	flags := struct {
		owner string
		repo  string
		tok   string
	}{}
	cmd.Flags().StringVarP(&flags.owner, "owner", "o", "augmentcode", "The owning organization.")
	cmd.Flags().StringVarP(&flags.repo, "repo", "r", "augment", "The owning repo.")
	cmd.Flags().StringVarP(&flags.tok, "token", "t", "name://augment-runner-manage", "A literal GitHub token. A token file with prefix name:// or a k8s secret with prefix sec://.")

	return func() (github.Client, error) {
		tok, err := func() (string, error) {
			if secname, found := strings.CutPrefix(flags.tok, "sec://"); found {
				k8s := getK8sOrDie()
				if sec, err := k8s.GetSecret(cmd.Context(), secname); err != nil {
					return "", err
				} else {
					return sec.KeyErr("token")
				}
			}
			return flags.tok, nil
		}()
		if err != nil {
			return github.Client{}, err
		}
		return github.NewClient(flags.owner, flags.repo, tok), nil
	}
}

func githubRunners() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "runners",
		Short:   "List github runners and their status.",
		Aliases: []string{"r"},
		Args:    cobra.NoArgs,
	}
	clifunc := githubClientGetter(cmd)
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		gh, err := clifunc()
		if err != nil {
			return nil
		}
		runners, _, err := gh.Runners(cmd.Context())
		if err != nil {
			return err
		}
		for _, r := range runners {
			fmt.Println(github.RunnerStatusLine(r))
		}
		return nil
	}
	return cmd
}

func githubPurgeOfflineRunners() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "purge-offline-runners [--dry-run|-n]",
		Short:   "Purge offline 'cw-gh-runner' runners.",
		Aliases: []string{"por"},
		Args:    cobra.NoArgs,
	}

	clifunc := githubClientGetter(cmd)
	flags := struct {
		dry bool
		pfx string
	}{}
	cmd.Flags().BoolVarP(&flags.dry, "dry-run", "n", false, "Dry-run, just log.")
	cmd.Flags().StringVarP(&flags.pfx, "name-prefix", "p", "cw-gh-runner", "The prefix of names to act on.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		gh, err := clifunc()
		if err != nil {
			return nil
		}
		return gh.RunnersPurgeOffline(cmd.Context(), flags.dry, github.NamePrefixFilter(flags.pfx))
	}
	return cmd
}

package cli

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/augmentcode/augment/research/infra/lib/coreweave"
)

func init() {
	addCommand(nil, nil, cwRoot())
}

func cwRoot() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "cw",
		Aliases: []string{"coreweave"},
		Short:   "Commands specific to CoreWave",
	}
	addCommand(cmd, nil, cwInstances())
	return cmd
}

func cwInstances() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "instances",
		Aliases: []string{"i"},
		Short:   "Get and print the CoreWeave instances API.",
		RunE: func(cmd *cobra.Command, args []string) error {
			raw, err := cmd.Flags().GetBool("raw")
			if err != nil {
				return err
			}
			cli := coreweave.New()
			if raw {
				buf, err := cli.InstancesRaw(cmd.Context())
				if err != nil {
					return err
				}
				fmt.Println(string(buf))
			} else {
				instances, err := cli.Instances(cmd.Context())
				if err != nil {
					return err
				}
				for i, inst := range instances.GPU {
					if i == 0 {
						fmt.Println("#", inst.RowHeader())
					}
					fmt.Println(inst.Row())
				}
				fmt.Println()
				for i, inst := range instances.CPU {
					if i == 0 {
						fmt.Println("#", inst.RowHeader())
					}
					fmt.Println(inst.Row())
				}
			}

			return nil
		},
	}
	cmd.Flags().Bool("raw", false, "Print raw JSON data.")
	return cmd
}

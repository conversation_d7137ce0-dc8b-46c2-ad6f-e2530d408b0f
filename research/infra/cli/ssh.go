package cli

import (
	"os"
	"os/user"
	"strings"

	"github.com/spf13/cobra"

	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/infra/lib/ssh"
)

func init() {
	addCommand(nil, nil, sshKeyGen(), ADMIN)
}

func sshKeyGen() *cobra.Command {
	cmd := &cobra.Command{
		Use:    "ssh-keygen [-f] [<dir>|sec://<name>]",
		Short:  "Generate SSH HostKeys and write to stdout, the filesystem, or k8s.",
		Args:   cobra.MaximumNArgs(1),
		Hidden: true,
	}

	flags := struct {
		force   bool
		comment string
	}{}
	cmd.Flags().BoolVarP(&flags.force, "force", "f", false, "Force overwrite destination if it exists.")
	cmd.Flags().StringVarP(&flags.comment, "comment", "C", "", "Public/Private key comment. Defaults to <user>@<hostname>.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		if flags.comment == "" && !cmd.Flags().Changed("comment") {
			if u, err := user.Current(); err != nil {
				return err
			} else if hn, err := os.Hostname(); err != nil {
				return err
			} else {
				flags.comment = u.Username + "@" + hn
			}
		}
		if set, err := (ssh.KeyGen{}).GenHostKeyFiles(flags.comment); err != nil {
			return err
		} else if len(args) == 0 {
			set.PrintAll()
			return nil
		} else if secname, ok := strings.CutPrefix(args[0], "sec://"); ok {
			k := getK8sOrDie()
			_, err := k8s.CreateSecretSSHHostKeys(cmd.Context(), k, secname, flags.force, set)
			return err
		} else {
			dir := args[0]
			return set.WriteFS(dir, flags.force)
		}
	}
	return cmd
}

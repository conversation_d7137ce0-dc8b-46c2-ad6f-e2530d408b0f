package cli

import (
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cobra"

	"github.com/augmentcode/augment/infra/lib/docker"
	"github.com/augmentcode/augment/research/infra/lib/registry"
)

func init() {
	addCommand(nil, nil, registryRoot())
}

var registryFlags struct {
	authSpec      string
	deployment    string
	defaultSecret string
	registry      string
}

func registryRoot() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "registry",
		Aliases: []string{"reg"},
		Short:   "Commands for working with a Docker Registry",
	}
	cmd.PersistentFlags().StringVar(&registryFlags.authSpec, "auth", "secret://", "Spec for obtaining registry credentials.")
	cmd.PersistentFlags().StringVar(&registryFlags.deployment, "deployment", "au-docker-reg-docker-registry", "The K8s Deployment name of the registry.")
	cmd.PersistentFlags().StringVar(&registryFlags.defaultSecret, "default-secret", "registry-readers", "The K8s Secret holding the registry API creds.")
	cmd.PersistentFlags().StringVar(&registryFlags.registry, "registry", "au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud", "The registry hostname.")

	gapi := &cobra.Group{ID: "reg_api", Title: "Registry API Commands"}
	gexe := &cobra.Group{ID: "reg_exe", Title: "Registry Pod Exec Commands"}
	gk8s := &cobra.Group{ID: "reg_k8s", Title: "Registry K8s Object Commands"}
	gdck := &cobra.Group{ID: "reg_dck", Title: "Docker Auth Commands"}
	cmd.AddGroup(gapi, gexe, gk8s, gdck)

	addCommand(cmd, gapi, registryLS())
	addCommand(cmd, gapi, registryRM())
	addCommand(cmd, gapi, registryCruft(), ADMIN)

	addCommand(cmd, gexe, registryGC(), ADMIN)
	addCommand(cmd, gexe, registryShell(), ADMIN)
	addCommand(cmd, gexe, registryExec(), ADMIN)

	addCommand(cmd, gk8s, registryResolvePod())

	addCommand(cmd, gdck, dockerAuth())

	return cmd
}

func getRegistryAuth(cmd *cobra.Command) (string, string, error) {
	spec := registryFlags.authSpec
	srv := registryFlags.registry

	// Implement secret:// directly because it doesn't belong in the docker library.
	if secname, found := strings.CutPrefix(spec, "secret://"); found {
		if secname == "" {
			secname = registryFlags.defaultSecret
		}

		k := getK8sOrDie()
		sec, err := k.GetSecret(cmd.Context(), secname)
		if err != nil {
			return "", "", err
		}

		if u, p, err := sec.DockerAuth(cmd.Context(), srv); err != nil {
			return "", "", err
		} else {
			return u, p, nil
		}
	} else {
		if u, p, err := docker.AuthFromSpec(cmd.Context(), spec, srv); err != nil {
			return "", "", err
		} else {
			return u, p, nil
		}
	}
}

func getRegistry(cmd *cobra.Command) (*registry.Client, error) {
	user, pass, err := getRegistryAuth(cmd)
	if err != nil {
		return nil, err
	}

	reg := registry.New(registryFlags.registry, user, pass)
	return reg, nil
}

////////////////////////////////////////////////////////////////////////////////
//
// Registry API Commands

func registryLS() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "ls [<repo>...]",
		Short: "List image details for all or specific repos.",
		Args:  cobra.ArbitraryArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			reg, err := getRegistry(cmd)
			if err != nil {
				return err
			}

			total := registry.NewRepo("*")
			if err := reg.GetRepos(cmd.Context(), args, func(repo *registry.Repo) {
				for _, image := range repo.Images() {
					total.Extend(image)
					fmt.Printf("IMAGE\t%s\n", image.FormatRow())
				}
				fmt.Printf("REPO\t%s\n", repo.FormatRow())
			}); err != nil {
				return err
			}
			fmt.Printf("REGISTRY\t%s\n", total.FormatRow())
			return nil
		},
	}
	return cmd
}

func registryRM() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "rm <repo:tag> [<repo:tag>...]",
		Short: "Delete one or more images from the registry.",
		Args:  cobra.MinimumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			reg, err := getRegistry(cmd)
			if err != nil {
				return err
			}
			for _, repotag := range args {
				repo, tag, _ := strings.Cut(repotag, ":")
				if repo == "" || tag == "" {
					return fmt.Errorf("'%s': not in the <repo>:<tag> format", repotag)
				}
			}
			for _, repotag := range args {
				repo, tag, _ := strings.Cut(repotag, ":")
				if _, _, err := reg.DeleteImage(cmd.Context(), repo, tag); err != nil {
					return fmt.Errorf("'%s': %w", repotag, err)
				}
			}
			return nil
		},
	}
	return cmd
}

func registryCruft() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "cruft [<repo>...]",
		Short: "List (and delete) 'cruft' in the registry.",
		Args:  cobra.ArbitraryArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			keepn, err := cmd.Flags().GetInt("keepn")
			if err != nil {
				return err
			}
			days, err := cmd.Flags().GetInt("days")
			if err != nil {
				return err
			}
			del, err := cmd.Flags().GetBool("delete")
			if err != nil {
				return err
			}

			reg, err := getRegistry(cmd)
			if err != nil {
				return err
			}

			total := registry.NewRepo("*")
			cruft := registry.NewRepo("x")
			now := time.Now()

			if err := reg.GetRepos(cmd.Context(), args, func(repo *registry.Repo) {
				total.Extend(repo.Images()...)
				icr := repo.Cruft(keepn, days, now)
				cruft.Extend(icr.Images()...)
				if icr.Len() > 0 {
					for _, image := range icr.Images() {
						fmt.Printf("IMAGE\t%s\n", image.FormatRow())
					}
					fmt.Printf("REPO\t%s\n", icr.FormatRow())
				}
			}); err != nil {
				return err
			}

			fmt.Printf("REGISTRY\t%s\n", total.FormatRow())
			fmt.Printf("CRUFT\t%s\n", cruft.FormatRow())

			allPool := total.BlobPool()
			cruftPool := cruft.BlobPool()
			allPool.UnTallyPool(cruftPool)
			orphanPool := allPool.OrphanPool()

			fmt.Printf("# Original: %d blobs using %s storage.\n", allPool.Len(), allPool.SizeHuman())
			fmt.Printf("# Cruft:    %d blobs using %s storage.\n", cruftPool.Len(), cruftPool.SizeHuman())
			fmt.Printf("# Orphans:  %d blobs using %s storage.\n", orphanPool.Len(), orphanPool.SizeHuman())

			for _, image := range cruft.Images() {
				fmt.Printf("# DELETE %s %s\n", image.Repo(), image.Digest())
				if image.Digest() != "" && del {
					resp, buf, err := reg.DeleteManifest(cmd.Context(), image.Repo(), image.Digest())
					if err != nil {
						fmt.Printf("# DELETE %s %s: %v\n", image.Repo(), image.Digest(), err)
					} else {
						fmt.Printf("# DELETE %v: [%v] %s: %s\n", resp.Request.URL, resp.StatusCode, resp.Status, string(buf))
					}
				}
			}

			return nil
		},
	}
	cmd.Flags().IntP("keepn", "n", 5, "Minimum number of tags to keep, per image.")
	cmd.Flags().IntP("days", "d", 30, "Minimum age (days) to be considered cruft.")
	cmd.Flags().BoolP("delete", "D", false, "Delete cruft.")
	return cmd
}

////////////////////////////////////////////////////////////////////////////////
//
// Registry Exec Commands

func registryGC() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "gc",
		Short: "Run the garbage-collect subcommand in the registry Pod.",
		Args:  cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			deluntagged, err := cmd.Flags().GetBool("delete-untagged")
			if err != nil {
				return err
			}

			wet, err := cmd.Flags().GetBool("wet-run")
			if err != nil {
				return err
			}
			k := getK8sOrDie()
			pod, err := k.GetDeploymentPod(cmd.Context(), registryFlags.deployment)
			if err != nil {
				return err
			}
			cmdline := []string{
				"/bin/registry", "garbage-collect", "/etc/docker/registry/config.yml",
			}
			if deluntagged {
				cmdline = append(cmdline, "--delete-untagged")
			}
			if !wet {
				cmdline = append(cmdline, "--dry-run")
			}
			return k.ExecPod(cmd.Context(), pod.Name(), "", false, false, cmdline...)
		},
	}
	cmd.Flags().BoolP("delete-untagged", "m", true, "")
	cmd.Flags().BoolP("wet-run", "w", false, "")
	return cmd
}

func registryShell() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "shell",
		Short: "Runs an interactive shell in the registry Pod.",
		Args:  cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			shell, err := cmd.Flags().GetString("shell")
			if err != nil {
				return err
			}
			k := getK8sOrDie()
			pod, err := k.GetDeploymentPod(cmd.Context(), registryFlags.deployment)
			if err != nil {
				return err
			}
			return k.ExecPod(cmd.Context(), pod.Name(), "", true, true, shell)
		},
	}
	cmd.Flags().String("shell", "/bin/ash", "")
	return cmd
}

func registryExec() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "exec [-i] [-t] [--] <cmdline> [<cmdline>...]",
		Short: "Runs an arbitrary command in the registry Pod.",
		Args:  cobra.MinimumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			stdin, err := cmd.Flags().GetBool("stdin")
			if err != nil {
				return err
			}
			tty, err := cmd.Flags().GetBool("tty")
			if err != nil {
				return err
			}
			k := getK8sOrDie()
			pod, err := k.GetDeploymentPod(cmd.Context(), registryFlags.deployment)
			if err != nil {
				return err
			}
			return k.ExecPod(cmd.Context(), pod.Name(), "", stdin, tty, args...)
		},
	}
	cmd.Flags().BoolP("stdin", "i", false, "stdin")
	cmd.Flags().BoolP("tty", "t", false, "tty")
	return cmd
}

////////////////////////////////////////////////////////////////////////////////
//
// Registry K8s Commands

func registryResolvePod() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "resolve-pod",
		Aliases: []string{"rp"},
		Short:   "Resolves the current Docker Registry K8s Pod.",
		Args:    cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			k := getK8sOrDie()
			pod, err := k.GetDeploymentPod(cmd.Context(), registryFlags.deployment)
			if err != nil {
				return err
			}
			fmt.Print(pod.Name() + "\n")
			return nil
		},
	}
	return cmd
}

func dockerAuth() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "auth [--auth=<spec>] [--registry=<server>]",
		Short: "Print docker registry username:password. Note, some --auth spec methods need to know the server, others don't.",
		Args:  cobra.NoArgs,
	}
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		if u, p, err := getRegistryAuth(cmd); err != nil {
			return err
		} else {
			fmt.Printf("%s:%s\n", u, p)
			return nil
		}
	}
	return cmd
}

package cli

import (
	"fmt"
	"io"
	"os"

	"github.com/spf13/cobra"

	"github.com/augmentcode/augment/research/infra/cfg/clusters"
	"github.com/augmentcode/augment/research/infra/lib/augment"
	"github.com/augmentcode/augment/research/infra/lib/augment/secrets"
)

func init() {
	addCommand(nil, nil, secretsRoot())
}

func secretsRoot() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "secrets",
		Aliases: []string{"secret"},
		Short:   "Commands for working with Augment Secrets.",
	}
	addCommand(cmd, nil, secretsSeal())
	addCommand(cmd, nil, secretsList())
	return cmd
}

func secretsSeal() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "seal <secret-name> [<value> or stdin]",
		Short: "Seal a secret.",
		Args:  cobra.MaximumNArgs(2),
	}
	flags := struct {
		outfile string
		force   bool
	}{}

	cmd.Flags().StringVarP(&flags.outfile, "outfile", "o", "", "Output file, assumed to be in "+secrets.EngSecretsDir+" unless absolute.\nDefaults to <secret-name>.json.  '-' for stdout.")
	cmd.Flags().BoolVarP(&flags.force, "force", "f", false, "Force overwrite destination if it exists.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		if len(args) == 0 {
			return fmt.Errorf("must pass secret name")
		}
		name := args[0]
		val := ""
		if len(args) == 2 {
			val = args[1]
		} else {
			// Read from stdin
			rval, err := io.ReadAll(os.Stdin)
			if err != nil {
				return err
			}
			val = string(rval)
			if len(val) == 0 {
				return fmt.Errorf("no value provided")
			}
		}

		username, err := augment.WhoAmIErr()
		if err != nil {
			return err
		}

		db, err := clusters.New()
		if err != nil {
			return err
		}
		db = db.Prod()

		sec, err := secrets.Seal(cmd.Context(), db, username, name, val)
		if err != nil {
			return err
		}

		if flags.outfile == "-" {
			sec.Print()
			return nil
		} else if flags.outfile == "" {
			return sec.Write(flags.force)
		} else {
			return sec.WriteTo(flags.outfile, flags.force)
		}
	}
	return cmd
}

// Look at the secrets in the repo, not in k8s.
func secretsList() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "list",
		Short:   "List secrets.",
		Aliases: []string{"ls"},
		Args:    cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			secs, err := secrets.List()
			if err != nil {
				return err
			}
			for _, sec := range secs {
				fmt.Println(sec)
			}
			return nil
		},
	}
	return cmd
}

package cli

import (
	"fmt"
	"os"
	"os/exec"
	"sort"
	"strings"

	"github.com/spf13/cobra"

	"github.com/augmentcode/augment/research/infra/cfg/clusters"
	"github.com/augmentcode/augment/research/infra/lib/augment"
)

func init() {
	addCommand(nil, nil, clustersRoot())
}

func clustersGetCluster(cmd *cobra.Command) (clusters.Cluster, error) {
	name, err := cmd.Flags().GetString("cluster")
	if err != nil {
		return clusters.Cluster{}, err
	}

	c, err := clusters.New()
	if err != nil {
		return clusters.Cluster{}, err
	}

	if name != "" {
		return c.Cluster(name)
	} else {
		return c.Current(cmd.Context())
	}
}

func clustersRoot() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "clusters",
		Aliases: []string{"providers", "cluster", "provider"},
		Short:   "Go-equivalent commands similar to providers.py",
	}
	cmd.PersistentFlags().StringP("cluster", "c", "", "The cluster (aka provider) for commands that take one. Defaults to autodetect.")

	addCommand(cmd, nil, clustersDetect())
	addCommand(cmd, nil, clustersList())
	addCommand(cmd, nil, clustersRegistries())
	addCommand(cmd, nil, clustersDump())
	addCommand(cmd, nil, clustersNode())
	addCommand(cmd, nil, clustersProfile())
	addCommand(cmd, nil, clustersResources())
	addCommand(cmd, nil, determinedLogin())

	return cmd
}

func clustersDetect() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "detect",
		Aliases: []string{},
		Short:   "Detect current cluster.",
		Args:    cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			if db, err := clusters.New(); err != nil {
				return err
			} else if name, err := db.DetectCluster(cmd.Context()); err != nil {
				return err
			} else {
				fmt.Println(name)
				return nil
			}
		},
	}
	return cmd
}

func clustersList() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "list [--all|-a] [--available|-A]",
		Aliases: []string{"ls"},
		Short:   "List clusters.",
		Args:    cobra.NoArgs,
	}
	flags := struct {
		all   bool
		avail bool
	}{}
	cmd.Flags().BoolVarP(&flags.all, "all", "a", false, "By default, only \"prod\" clusters are included. Include everything (e.g., test clusters).")
	cmd.Flags().BoolVarP(&flags.avail, "available", "A", false, "Only return clusters which are available in your local Kubeconfig (by context).")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		c, err := clusters.New()
		if err != nil {
			return err
		}
		if !flags.all {
			c = c.Prod()
		}
		if flags.avail {
			c = c.Available()
		}
		for _, name := range c.Names() {
			fmt.Println(name)
		}
		return nil
	}
	return cmd
}

func clustersRegistries() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "registries [--all|-a]",
		Aliases: []string{},
		Short:   "List registries.",
		Args:    cobra.NoArgs,
	}
	flags := struct {
		all bool
	}{}
	cmd.Flags().BoolVarP(&flags.all, "all", "a", false, "By default, only \"prod\" clusters are included. Include everything (e.g., test clusters).")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		c, err := clusters.New()
		if err != nil {
			return err
		}
		if !flags.all {
			c = c.Prod()
		}
		for _, reg := range c.Registries() {
			fmt.Println(reg)
		}
		return nil
	}
	return cmd
}

func clustersDump() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "dump [--all|-a] [--nodes|-n] [--volmounts|-v] [cluster...]",
		Aliases: []string{"all", "properties", "props"},
		Short:   "Dump info for all or select cluster(s).",
		Args:    cobra.ArbitraryArgs,
	}

	flags := struct {
		all       bool
		nodes     bool
		volmounts bool
	}{}
	cmd.Flags().BoolVarP(&flags.all, "all", "a", false, "By default, only \"prod\" clusters are included. Include everything (e.g., test clusters).")
	cmd.Flags().BoolVarP(&flags.nodes, "nodes", "N", false, "Also include node info.")
	cmd.Flags().BoolVarP(&flags.volmounts, "volmounts", "V", false, "Also include volume/mount info.")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		db, err := clusters.New()
		if err != nil {
			return err
		}
		if len(args) == 0 {
			if !flags.all {
				db = db.Prod()
			}
			for _, name := range db.Names() {
				// skip _UNITTEST_ when not explicitly requested
				if name == "_UNITTEST_" {
					continue
				}
				args = append(args, name)
			}
		}
		for _, name := range args {
			cluster, err := db.Cluster(name)
			if err != nil {
				return err
			}
			cluster.Dump(flags.nodes, flags.volmounts)
			fmt.Println()
		}
		return nil
	}

	return cmd
}

func clustersNode() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "node [--cluster=<cluster>]",
		Aliases: []string{},
		Short:   "Build a node and print details.",
		Args:    cobra.NoArgs,
	}
	tla := clusters.NodeTLAFlags(cmd.Flags())
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		c, err := clustersGetCluster(cmd)
		if err != nil {
			return err
		}
		tlacode, err := tla.TLACode()
		if err != nil {
			return err
		}
		n, err := c.NodeFromTLA(tlacode)
		if err != nil {
			return err
		}
		fmt.Println(n)
		return nil
	}
	return cmd
}

func clustersProfile() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "profile [--cluster=<cluster>] [--no-export]",
		Aliases: []string{"shell", "bash", "env"},
		Short:   "Print out a shell-compatible list of vars for the cluster.",
		Args:    cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			noExport, err := cmd.Flags().GetBool("no-export")
			if err != nil {
				return err
			}

			c, err := clustersGetCluster(cmd)
			if err != nil {
				return err
			}

			fmt.Print(c.Profile(!noExport))
			return nil
		},
	}
	cmd.Flags().BoolP("no-export", "E", false, "Do not prepend each line with 'export'.")
	return cmd
}

func formatClusterGpu(
	gpuType string, usedGpus int, totalGpus int, totalPods int, activeNodes int,
	totalNodes int, pendingPods int, pendingGpus int,
	pNode *clusters.ResourceNode, gNodes []*clusters.ResourceNode, userGpus map[string]int,
) map[string]string {
	summary := []string{
		fmt.Sprintf("\t%s: Using %d/%d (%0.2f%%) in %d pods across %d/%d nodes", gpuType, usedGpus, totalGpus, 100.0*float64(usedGpus)/float64(totalGpus), totalPods, activeNodes, totalNodes),
		fmt.Sprintf("\t    %d nodes are fully available", totalNodes-activeNodes),
	}
	if pendingPods > 0 {
		summary = append(summary, fmt.Sprintf("\t    There are %d outstanding GPU requests across %d pending pods", pendingGpus, pendingPods))
	}
	userUsage := []string{}
	if len(userGpus) > 0 {
		userUsage = append(userUsage, "GPU usage by user:")
		for user, gpus := range userGpus {
			userUsage = append(userUsage, fmt.Sprintf("\t%-8.8s: %d", user, gpus))
		}
	}
	details := []string{}
	for _, node := range gNodes {
		if len(node.Pods) == 0 {
			continue
		}
		details = append(details, node.Table(""))
	}
	if pNode != nil && len(pNode.Pods) > 0 {
		details = append(details, pNode.Table(""))
	}
	return map[string]string{
		"summary":   strings.Join(summary, "\n"),
		"userUsage": strings.Join(userUsage, "\n"),
		"details":   strings.Join(details, "\n"),
	}
}

func clustersResources() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "resources [--context=<context>] [--all|-a] [--summary|-s]",
		Aliases: []string{"res"},
		Short:   "Print out a list of resources for the cluster.",
		Args:    cobra.NoArgs,
	}

	flags := struct {
		all bool
		sum bool
	}{}
	cmd.Flags().BoolVarP(&flags.all, "all", "a", false, "List resources for all clusters, not just the current")
	cmd.Flags().BoolVarP(&flags.sum, "summary", "s", false, "Summary only, no pod details, implies --all")

	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		cluster, err := clustersGetCluster(cmd)
		if err != nil {
			return err
		}
		db, err := clusters.New()
		if err != nil {
			return err
		}

		var cs *clusters.Clusters
		if flags.all || flags.sum {
			cs = db.Prod().Available()
		} else {
			cs = db.Prod().Available().Filter(func(c clusters.Cluster) bool {
				return c.Name == cluster.Name
			})
		}
		fmt.Println("Getting resources for clusters: " + strings.Join(cs.Names(), ", "))
		resources, errors := cs.Resources(cmd.Context())
		for clusterName, err := range errors {
			if err != nil {
				fmt.Printf("Error getting resources for cluster %s: %v\n", clusterName, err)
			}
		}
		var totalUsage map[string]map[string]int = map[string]map[string]int{}
		clusterFormat := map[string]map[string]map[string]string{}
		for clusterName, nodes := range resources {
			gpuTypes := map[string]bool{}
			for _, node := range nodes {
				gpuTypes[node.GpuType] = true
			}
			if len(nodes) == 0 {
				fmt.Printf("No resources for cluster %s\n", clusterName)
				continue
			}
			clusterFormat[clusterName] = map[string]map[string]string{}
			for gpuType := range gpuTypes {
				var gNodes []*clusters.ResourceNode
				var pNode *clusters.ResourceNode
				userGpus := map[string]int{}
				activeNodes := 0
				usedGpus := 0
				totalGpus := 0
				totalPods := 0
				pendingPods := 0
				pendingGpus := 0
				for _, node := range nodes {
					if node.NodeName == "" && node.Pods[0].GpuType == gpuType {
						pNode = node
						pendingPods += len(node.Pods)
						pendingGpus += node.UsedGpu()
						continue
					}
					if node.GpuType == gpuType {
						gNodes = append(gNodes, node)
						if len(node.Pods) > 0 {
							activeNodes++
							usedGpus += node.UsedGpu()
							totalPods += len(node.Pods)
							for _, pod := range node.Pods {
								userGpus[pod.User] += pod.GpuCount
							}
						}
						totalGpus += node.GpuCount
					}
				}
				totalNodes := len(gNodes)
				if tu, ok := totalUsage[gpuType]; ok {
					tu["used"] += usedGpus
					tu["total"] += totalGpus
					tu["total_pods"] += totalPods
					tu["active"] += activeNodes
					tu["total_n"] += totalNodes
					tu["pending"] += pendingPods
					tu["pending_g"] += pendingGpus
				} else {
					totalUsage[gpuType] = map[string]int{
						"used":       usedGpus,
						"total":      totalGpus,
						"total_pods": totalPods,
						"active":     activeNodes,
						"total_n":    totalNodes,
						"pending":    pendingPods,
						"pending_g":  pendingGpus,
					}
				}

				clusterFormat[clusterName][gpuType] = formatClusterGpu(gpuType, usedGpus, totalGpus, totalPods, activeNodes, totalNodes, pendingPods, pendingGpus, pNode, gNodes, userGpus)
			}
		}
		clusterFormat["ALL"] = map[string]map[string]string{}
		for gpuType, tu := range totalUsage {
			usedGpus := tu["used"]
			totalGpus := tu["total"]
			totalPods := tu["total_pods"]
			activeNodes := tu["active"]
			totalNodes := tu["total_n"]
			pendingPods := tu["pending"]
			pendingGpus := tu["pending_g"]
			clusterFormat["ALL"][gpuType] = formatClusterGpu(gpuType, usedGpus, totalGpus, totalPods, activeNodes, totalNodes, pendingPods, pendingGpus, nil, nil, nil)
		}

		var cn []string
		for clusterName := range clusterFormat {
			cn = append(cn, clusterName)
		}
		sort.Slice(cn, func(i, j int) bool {
			if cn[i] == "ALL" {
				return false
			}
			if cn[j] == "ALL" {
				return true
			}
			return cn[i] < cn[j]
		})
		for _, clusterName := range cn {
			cluster := clusterFormat[clusterName]
			if clusterName == "ALL" {
				if !flags.all && !flags.sum {
					continue
				}
				fmt.Println("All Clusters")
			} else {
				fmt.Printf("Cluster: %s\n", clusterName)
			}
			for _, gpu := range cluster {
				fmt.Println(gpu["summary"])
				if flags.sum || clusterName == "ALL" {
					continue
				}
				if gpu["userUsage"] != "" {
					fmt.Println(gpu["userUsage"])
					fmt.Println()
				}
				if gpu["details"] != "" {
					fmt.Println(gpu["details"])
				}
			}
			fmt.Println()
		}

		return nil
	}
	return cmd
}

func determinedLogin() *cobra.Command {
	cmd := &cobra.Command{
		Use:     "determined-login [--cluster=<cluster>] [--admin|-a] [--dry-run|-n]",
		Aliases: []string{"det-login"},
		Short:   "Log into a determined instance.",
		Args:    cobra.NoArgs,
	}
	Vars := struct {
		DryRun bool
		Admin  bool
	}{}
	cmd.Flags().BoolVarP(&Vars.DryRun, "dry-run", "n", false, "Dry-run, just print the command.")
	cmd.Flags().BoolVarP(&Vars.Admin, "admin", "a", false, "Log in as admin.")
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		c, err := clustersGetCluster(cmd)
		if err != nil {
			return err
		}
		ctx := cmd.Context()

		cmdname := "det"
		userName := augment.WhoAmI()
		if Vars.Admin {
			userName = "admin"
		}
		cmdargs := []string{
			"--master", c.DeterminedURL,
			"user", "login", userName,
		}

		fmt.Println(strings.Join(append([]string{cmdname}, cmdargs...), " "))
		if Vars.DryRun {
			return nil
		}

		logincmd := exec.CommandContext(ctx, cmdname, cmdargs...)
		logincmd.Stdin, logincmd.Stdout, logincmd.Stderr = os.Stdin, os.Stdout, os.Stderr
		return logincmd.Run()
	}
	return cmd
}

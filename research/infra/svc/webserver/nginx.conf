user nginx;
worker_processes auto;

events {
	worker_connections 1024;
}

http {
	keepalive_timeout 65;
	sendfile on;
	include /etc/nginx/mime.types;
	default_type application/octet-stream;
	types {
		text/plain            log;
		text/plain            sh;
		text/plain            yaml yml;
		application/ld+json   jsonl jsonld;
	}

	server {
		listen 8000 default_server;
		listen [::]:8000 default_server;
		server_name _;

		location /oauth2/ {
			proxy_pass http://127.0.0.1:4180;
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Auth-Request-Redirect $request_uri;
		}
		location /oauth2/auth {
			proxy_pass http://127.0.0.1:4180;
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Auth-Request-Redirect $request_uri;
			proxy_set_header Content-Length "";
			proxy_pass_request_body off;
		}

		location / {
			root /srv/www;
			index index.html;
			autoindex on;

			auth_request /oauth2/auth;
			error_page 401 =403 /oauth2/sign_in;

			auth_request_set $auth_cookie $upstream_http_set_cookie;
			add_header Set-Cookie $auth_cookie;
		}

		error_page 500 502 503 504 /50x.html;
		location = /50x.html {
			root /usr/share/nginx/html;
		}
	}
}

NAME    := research-webserver
VERSION := 2024-12-29.1
CLUSTER := gcp-us1

# NOTE(mattm): Consider being explicit with the upstream versions.
VERSION_NGINX  := latest
VERSION_OAUTH2 := latest

CONTEXT  := $(shell jsonnet -A name="$(CLUSTER)" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
K8S_CONF := $(NAME).jsonnet
KUBECTL  := kubectl --context="$(CONTEXT)"
KUBECFG  := kubecfg --context="$(CONTEXT)" \
	    --tla-code="cluster='$(CLUSTER)'" \
	    --tla-code="name='$(NAME)'" \
	    --tla-code="version='$(VERSION)'" \
	    --tla-code="version_nginx='$(VERSION_NGINX)'" \
	    --tla-code="version_oauth2='$(VERSION_OAUTH2)'"

show:
	$(KUBECFG) show $(K8S_CONF)

diff:
	-$(KUBECFG) diff --diff-strategy=subset $(K8S_CONF)

apply:
	$(KUBECFG) update $(K8S_CONF)

logs:
	$(KUBECTL) logs -l aug.service=$(NAME) $(LOGS_EXTRA)

list:
	$(KUBECTL) get -Al aug.service=$(NAME) deploy,pod,svc,ing,role,rolebinding,sa,pv,pvc,cm,sealedsecret,secret

deploy: diff apply

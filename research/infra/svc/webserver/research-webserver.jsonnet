local clusters = import '../../cfg/clusters/clusters.jsonnet';

local nginx_conf = importstr 'nginx.conf';

function(
  cluster='gcp-us1',
  name='research-webserver',
  namespace='research-webserver',
  version='latest',
  version_nginx='latest',
  version_oauth2='latest',
  image_nginx='nginx',
  image_oauth2='quay.io/oauth2-proxy/oauth2-proxy',
  replicas=2,
  cpu_nginx='4',
  ram_nginx='4Gi',
  cpu_oauth2='2',
  ram_oauth2='2Gi',
  port=8080,
) local C = clusters.cluster(cluster); C.k8s + {

  hostname:: 'webserver.' + std.asciiLower(C.name) + '.r.augmentcode.com',
  bucket:: std.asciiLower(C.name) + '-public-html',

  BaseLabels+:: std.prune({
    'aug.service': name,
    'aug.version': version,
  }),

  Object+:: {
    name:: name,
    namespace:: namespace,
  },

  ns: $.Namespace + {
    name:: namespace,
  },

  cfg: $.ConfigMap + {
    local s = self,
    conf_fname:: 'nginx.conf',
    data: {
      [s.conf_fname]: nginx_conf,
    },
  },

  deploy: $.Deployment + {
    spec+: {
      replicas: replicas,
      template+: {
        metadata+: {
          annotations+: {
            'gke-gcsfuse/volumes': 'true',
            'nginx-conf-md5': std.md5(nginx_conf),  // force updates on config changes
          },
        },
        spec+: {
          local pod = self,
          local conf_mount = '/run/augment/' + $.cfg.name,
          containers: [
            $.Container + {
              name: 'nginx',
              image: image_nginx + ':' + version_nginx,
              volumeMounts: pod.volmount_mounts,
              resources+: {
                limits+: {
                  cpu: cpu_nginx,
                  memory: ram_nginx,
                },
              },
              args: ['nginx', '-g', 'daemon off;', '-c', conf_mount + '/' + $.cfg.conf_fname],
            },
            $.Container + {
              name: 'oauth2',
              image: image_oauth2 + ':' + version_oauth2,
              resources+: {
                limits+: {
                  cpu: cpu_oauth2,
                  memory: ram_oauth2,
                },
              },
              env: [
                {
                  name: 'OAUTH2_PROXY_CLIENT_ID',
                  valueFrom: {
                    secretKeyRef: {
                      name: $.oauth2_sec.spec.template.metadata.name,
                      key: 'client_id',
                    },
                  },
                },
                {
                  name: 'OAUTH2_PROXY_CLIENT_SECRET',
                  valueFrom: {
                    secretKeyRef: {
                      name: $.oauth2_sec.spec.template.metadata.name,
                      key: 'client_sec',
                    },
                  },
                },
                {
                  name: 'OAUTH2_PROXY_COOKIE_SECRET',
                  valueFrom: {
                    secretKeyRef: {
                      name: $.oauth2_sec.spec.template.metadata.name,
                      key: 'cookie_sec',
                    },
                  },
                },
              ],
              args: [
                '--http-address=http://127.0.0.1:4180',
                '--reverse-proxy=true',
                '--provider=google',
                '--email-domain=augmentcode.com',
                '--cookie-secure=true',
                '--cookie-expire=168h',
              ],
            },
          ],
          tolerations+: [
            {
              effect: 'NoSchedule',
              key: 'r.augmentcode.com/pool-type',
              value: 'svc',
            },
            {
              effect: 'PreferNoSchedule',
              key: 'r.augmentcode.com/pool-type',
              value: 'svc',
            },
          ],
          volmounts:: [
            {
              local vm = self,
              name:: $.cfg.metadata.name,
              volume:: {
                configMap: {
                  name: vm.name,
                },
              },
              mount:: {
                mountPath: conf_mount,
              },
            },
            {
              local vm = self,
              name:: $.bucket,
              volume:: {
                csi+: {
                  driver: 'gcsfuse.csi.storage.gke.io',
                  readOnly: true,
                  volumeAttributes+: {
                    bucketName: $.bucket,
                    mountOptions: std.join(',', [
                      'uid=101',
                      'gid=101',
                      'file-mode=0444',
                      'dir-mode=0555',
                      'o=noexec',
                      'o=noatime',
                    ]),
                  },
                },
              },
              mount:: {
                mountPath: '/srv/www',
              },
            },
          ],
        },
      },
    },
  },

  acl: $.GCP.IAM.PolicyMember + {
    name:: C.name + '-' + namespace + '.' + $.bucket + '.' + 'storage-objectuser',
    namespace: 'cc-' + C.name,
    spec+: {
      resourceRef: {
        kind: 'StorageBucket',
        external: $.bucket,
      },
      member: 'principalSet://iam.googleapis.com/projects/%d/locations/global/workloadIdentityPools/%s.svc.id.goog/namespace/%s' % [
        C.gcp_project_number,
        C.gcp_project,
        namespace,
      ],
      role: 'roles/storage.objectUser',
    },
  },

  svc: $.Service + {
    spec+: {
      selector+: {
        'k8s.deployment': $.deploy.metadata.name,
      },
      type: $.SERVICE_TYPE.CLUSTER_IP,
      ports: [
        {
          name: 'http',
          port: port,
          targetPort: 8000,
          protocol: 'TCP',
        },
      ],
    },
  },

  ing: $.Ingress + {
    local ing = self,
    metadata+: {
      annotations+: {
        'cert-manager.io/cluster-issuer': 'letsencrypt-prod',
      },
    },
    spec+: {
      ingressClassName: 'nginx',
      rules: [
        {
          host: $.hostname,
          http: {
            paths: [{
              path: '/',
              pathType: 'Prefix',
              backend: {
                service: {
                  name: $.svc.metadata.name,
                  port: {
                    number: port,
                  },
                },
              },
            }],
          },
        },
      ],
      tls: [{
        hosts: [$.hostname],
        secretName: ing.metadata.name + '-tls',
      }],
    },
  },

  oauth2_sec: $.SealedSecret + {
    name+:: '-oauth2',
    encryptedData: {
      client_sec: 'AgAYtLpxUXy22B39PLJZpHMhNaUfiIdElV5/KSK7xIqHAWCH/F77H/3iyZWauzcybSU/y6AVjDrNgfr5oKrIPltBsZxzWq/SeGZ1EaEQNezwGSF5PPlV3O3QOywoKChKFFDYGZmwML8QHd5sLnVYSeSpgh4YwCJRSzB/rTwuFsuKv+bIvFJiOxUzcLwYzDvSPUNqyYIOu8TQCKmwX0TmhZdMHZA39J5+EhktCr/pJMhHxINgocagIhbM/nAmZ8OoA3Y3TW0+D7C0uKBYRt0pA8fH3si1x/manFRvqCaXc7lVl86WhweR3jN2w5ppFdSQ2frI0gssMBXt7mc/jWrE4/n0L9rVTiMEVBTG6Yo7twWX2JkwNhhjMlTk/1h8CbrT0zA6zHa8isg8LC4UBzbnLRBLuNNT/+ltgYWBP+fEDNy1DpYgtU5EOkAi8fssjCQC8GB5FahvxFYtWPoD6WOTEI6zqmDCtG6qCS9iRmcZ5q2Ncstgdd4HYLwABFGcmPXzst9gZJsFCL+R9ZRDFnCwYyineMfsTBGHzWKiqPjgP5LazGr2AXzF5m0FNyG86rSKVSF+sOwlKH0l51bPDMqpgy7y3l56HuN9AW2je9hoO/9a38hsHVUbELdYMIQRFCGu1aRWqAGmJg+JU8Nt+HFMnghYNX+7mTooDnt9ayIZAbmsCW5AynOt6E82iqqeWmy6N0NXYy/j3Jto1vlbMsEbrxA+iOtkLxylTG0T0KsoqhemLRzw1A==',
      cookie_sec: 'AgATOOobkb8fNIWtPAOPyA136u+5R934Y4bWY8MWBjxKDycaCYceXk4j+utBz8LIeltmAsStzTtjq6g/tksRKTI+Uyhz4KwlGonykan6V98LElerk0vfQh4blQNFsNoR5EeiIOCCZiOMu3ySb/eH8Eq7RqWTvZc9QdzEp0bdztVP8JmRxrK8LCmdjrxzga+YRlYNeM/ySJ5B5COKPpjTD3bUppPUrRFScNo5G4KRynmoQMxEXaa0+5pjOYK1vu3aFIM8oUFXEed/gO0Eg07cy/hEBafSxqEGWLDuTza5MuuFx+l9BmetfzuvU2TzkFtlMLPWyIuN5t+eO+hG5itiKzjsVUc/A2FURwSbVr+5YCzrb41Rf3ZhXN25TfEW4OayPSNiefvRHFgJt8iEaa1NgDOns6Gs3himj+W+yI/EXSvv/8tF7owyj8x5ZJ4F39TaveTMbUtGhVg4lztsYqH7zpqZu9LAT9xpVHJfObhNfhmjCpJCaYjwZx/D8GQIQfGE4pMob0XVEJmLcQFEt62fiiFefHvlJXIa/FCON/V+R7qPKOli/GyX7zGXuczhd4MDUc4HrbjWI4xhC3kbB1tT5atTZI0jfbWMMNVUy7T3n0WjVXuEseOOeIveKXF5C5au30+Chg3FICMLFnAad1xFO7o12BRFiDpxpAGPtLjYCAc66j/LOQMvJPEymdOj/df/V29VeF5+qr+JFtD2p8yemRAK3WNoYbtP/LkFZ4W/85qkqA==',
    },
    spec+: {
      template+: {
        data+: {
          client_id: '670297456363-8dm8aprmvflmao4g6vqhhssnlvstpoct.apps.googleusercontent.com',
        },
      },
    },
  },
}

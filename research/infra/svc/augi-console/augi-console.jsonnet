local clusters = import '../../cfg/clusters/clusters.jsonnet';
local aug = import '../../cfg/lib/augment-enums.jsonnet';

function(
  cluster='gcp-us1',
  name='augi-console',
  namespace=null,
  version='latest',
  image=null,
  ssh_port=22022,
  cpu='4',
  ram='32Gi',
  ssh_authorizedkeys_configmap=aug.CW_AUTHORIZED_KEYS,
) local C = clusters.cluster(cluster); C.k8s + {

  BaseLabels+:: std.prune({
    'aug.service': name,
    'aug.version': version,
  }),

  Object+:: {
    name:: name,
    namespace:: if namespace != null then namespace else super.namespace,
  },

  deployment: $.Deployment + {
    local s = self,
    spec+: {
      replicas: 1,
      template+: {
        spec+: {
          local pod = self,
          hostname: s.name,
          serviceAccountName: $.sa.name,
          containers: [
            $.Container + {
              name: name,
              image: image,
              resources+: {
                limits+: {
                  cpu: cpu,
                  memory: ram,
                },
              },
              volumeMounts: pod.volmount_mounts,
            },
          ],
          volmounts:: [
            {
              name: 'run-tmpfs',
              volume: {
                emptyDir: { medium: 'Memory' },
              },
              mount: {
                mountPath: '/run',
              },
            },
            {
              name: 'tmp-tmpfs',
              volume: {
                emptyDir: { medium: 'Memory' },
              },
              mount: {
                mountPath: '/tmp',
              },
            },
            {
              name: 'ssh-authorized-keys',
              volume: {
                configMap: {
                  name: ssh_authorizedkeys_configmap,
                  optional: true,
                  defaultMode: std.parseOctal('0444'),
                },
              },
              mount: {
                mountPath: '/etc/ssh/authorized_keys',
                readOnly: true,
              },
            },
            {
              name: 'sshd-hostkeys',
              volume: {
                secret: {
                  secretName: $.sshd_hostkeys.metadata.name,
                  optional: false,
                  defaultMode: std.parseOctal('0400'),
                },
              },
              mount: {
                mountPath: '/etc/ssh/keys',
                readOnly: true,
              },
            },
            {
              local s = self,
              name: C.augi_release_readers,
              volume: {
                secret: {
                  secretName: s.name,
                  optional: false,
                },
              },
              mount: {
                mountPath: '/run/augment/secrets/' + s.name,
                readOnly: true,
              },
            },
          ],
        },
      },
    },
  },

  service: $.Service + {
    externalDNS:: '%s.%s.r.augmentcode.com' % [name, std.asciiLower(C.name)],
    spec+: {
      selector+: {
        'k8s.deployment': $.deployment.name,
      },
      type: $.SERVICE_TYPE.LOAD_BALANCER,
      externalTrafficPolicy: 'Local',
      ports: [
        {
          name: 'ssh',
          port: ssh_port,
          targetPort: ssh_port,
        },
      ],
    },
  },

  sa: $.ServiceAccount + {
  },

  rolebinding: $.RoleBinding + {
    role_name:: $.role.name,
    sas:: [$.sa],
  },

  role: $.Role + {
    rules: [
      {
        apiGroups: [''],
        resources: ['configmaps'],
        resourceNames: [ssh_authorizedkeys_configmap],
        verbs: $.READ_VERBS,
      },
    ],
  },

  sshd_hostkeys: $.SealedSecret + {
    name:: name + '-sshd-hostkeys',
    encryptedData: {
      'gcp-us1': {
        ssh_host_ecdsa_key: 'AgBgGTUmyVvdy73kfttr8M72lCbGbkRJlYOkbLzUvIieZxsa4GT+Jn0RULaX8vyM+iAV1hAsWhpHw469yv6I2eCtvpVZaaGyS4KLWXnfinPggMwUkEr7C+bJJdMnrjE/RqIw/D1DbTUxgn62G9x8I63jwvqU2thXWVAsHJ5OMpWpy2NGAx/vIiV1MjKBAtBWHo6rowIu7CV/7bjKOxA9h91GW+KyUPDFWFi75/aQ/zhtr07Vb/EdaBZULCQEfkjjdpvSsGKZ70BorwdaDoOrDf2TQD/2n4F2ULgncCmQICazWYCe5qoDrkLFMzZqpDACECU1MG2OFrmDmUv4LLWOqDXX6WcEiU/JkIWKwWgBGdR1wF6ik6i5aVJ9d5lbs2yTcW7ti3/745jrDvxNDbK3KjTdMRzdypqxY/OMnpJa1Xvsc598hBoXby59ErV9SaRyN1jN987YlNQ0KKtU4hresNUTw0/19FrMeEdnWUyregWZDcw8nb8ZGXx+k6CyZky3aT4LaJSbtEIBezZ4jNGu0H9NAAOECtL2AC3SrmRjKjt2YGR3cpqDfWTtpcJyiBFThKI9FBAyiZQ1+6NgmBmfcloHcsTi1RX+U0o6nfJREVaou3u0/98A5Ejn2E+Oz57vVu4pJ4GIj5gRol57zMd6O85OmAjDzEBOwGAYawEtEg/Ju6IjhJML+ese0C/Q40+9bma13dif32e6bOOlbRsmlAcM6SLBu6cNYjs6C2yskZY956r8ymjhVuspWd5hOTMVa/jvF/CXcwe2ofLkvQlrARPWXIFz9k0LnzKZl4L47+1iGjJJEh8UozZmHYZDYp0mI42dg36uHpl9uEMHDQmJYZ411LGU1Ss8Sh+J8+qbhc2HJgI199wa1sGxwjanzSS78mdX/nNLZlOrF5DqplG0aLoZVzyDaGhs7j8l+15ULmhKH2TtE+89NgMZjRrVHKx6JRPLTxpVKnYOX9f1xpEqGxxI7oMWVq8zYns3OGjI5a7d7KdKuWSGiDKsel6RWwnZ32kv3HucH3IxgtFoD7jkuYuV8obAK3qD5Uj8ikNy151dc3vR4eiimK6Q1evD1i0mvSw9/jftJ/G+9edOfCZgQJBoA4vqRmN0TvcwhywxTQb1Jsqq3z6eednfuk/g3JsilTtak79S0FaojCyihYlgSbNJTF/X+469vZXGd/ZP/4CoekqocvWlEKV89HKhCW1EhQ0u37xNPZWDwBMhQUWkjRUrKdVjsShmYvZGvMI/7XOlIYDAnSvRRUl9VzqSR0f37/sjlyUdPalAjx0KB0q8HEXVc2uZKw0vl+b7cYMwXKo6Qfh9sFUXZiBm1jM9mtrKFvjaohFGjBx3Se2aAxMwPBoK6mYQnLckhp2JvIp9bJucnW3a3SoQzM0SVKONkDynGspn8GVWThmJP9UsOOlWoz4H6jQ=',
        ssh_host_ed25519_key: 'AgAVVbtL4j1Y0KlB5zRdBaQWpl8JkgalBGB6BrkF7d5He9ff9zK53rRbn9jC699n8pYJBRFM7ifhlrVxTtn0ObmWSd70zeyvnN1fkD5Vx1gVsqzuwpSc+/mlJBZZPq+jfRO0B/774Zfe3GhS47BmlOYH8a3++asUBIcRH9bEhm58Jh6329RiG16dvMlr9nBum1cez6mebe1zHMZFLhFfij6pl1V6/RSMCwbZxLM4srrFjrE/UN1eGfYyYmgXj9UU/GgtlYylSH5J+IN45prRHIBfSjXye6uoSxVx/2BGKW82OIHz9ao++Zfcbn/oXuKDvk97kigi+hYBLdx6rzjKR5anyg4pfcRsYCj2qNGCd7x18Zkb11+rR5X2/+/KAZjJiE6Y5hJ/QhmGN1fov28jAZ704XXVDQJZBHzT8FDeNtFPxJtkb12seTBfM1iXTNBUUUiiXoL4+mqGCKP3LWWCq1j2DcRHCs7OGw4iTZPKL4wD+BD6P0Df4vrxBP/lI3Rahba1kVnWnhnCPS5SEJzhHIXZtaDaOT8lPls5Pe+r+BusKRstYotZmMMi8lvoGjGXuU8EBzo3TsTDdZsnWINC6fbdTkkwQKEDYbiGa5rvR6VQMisXM4rGYwcRng6vW3Gh2x8B+KipTLo6sQbHS3uorUmAOWfNsA/jtdu7i11x0JY06PXBGrljlzoNx5r8kRN/aDD5eSRivED8ABrwfJUXoVvr8NGgFHfpnQLjfPcOes7jJWNs+TRj6CRdCVQctjN6DoZs/pLvtyHDm3C3Zm2AhiIorGZAo0m+QcK0yQDOGBuoDUOyUXXouxVi0QAWfRB5583u9Lv2sx6sjPN9U3IwGDy9ka1dRx5TzxhXiWmdx87aM8cZS4LZkFpJbRK13bTfsfBBeioL+bohLn9rBbTKlvb9S3Nti3HDEF7JG74V8Zs3FuJ1s+8/57HHktHFShrznrX06zOB+YzsoOUwZvK3V2gXJf9gldAhEfKaIqskbEhqIfavApdLj8PhkSVnjK/+bq4LpBR/JspOydfxeKtwg4tLERWmVAz4gqebg0LmvQ7TeINr1Qw7Wc9U+C4GrzzQB/kGThwFbrr7oBrSnhTBQ+Y9vjPcL/8DiTavrtpm6/0jH2mUSep00FCjaJzY+S6QuL/08vpZoNciL7Y2XBnamRPv708YQWkVpNRetMFMJR1TPkgJTMf6DsoNBLb6+uJiLLRsdDfzD6xWl+MXdfvJPAhEFEcjSClhwWZW2ebR0b0W2Lyv7TpaahCwFP/GmcMCA8yIVMfLksag6nHBGkk=',
        ssh_host_rsa_key: '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',
      },
    }[C.name],
    spec+: {
      template+: {
        data: {
          'gcp-us1': {
            'ssh_host_ecdsa_key.pub': 'ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBCRpYTmjBddb7D+2Q46Vpz3eIK3PmHVnP/yIaxzZ4lTH1MjPdeMGDFw7a8FepiK/mPAMdZ+SYU7pn8I/lCo1EFE= <EMAIL>',
            'ssh_host_ed25519_key.pub': 'ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIMghXxkSYzDHbK64hEXtRI8Bu8ShB20BTtlfZujavMMm <EMAIL>',
            'ssh_host_rsa_key.pub': 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDvJ+gjnn/Pwqbbvy6cRivlN5o/RBGzqfkBJCWf2m+XYiHdXKLxstLPu5mGPU4RdPxxy6CwyAQ+QF86v9hxMlEeIgfce4KSmFLDdyxhQgv8U2z+mweKNksGsvFUgMBXyUt4rir5jBqZ0+pfdl5z6hAKFy26ZdmvQQPx2HZ3x8JemClmUDLJPBqd403XGkLma3LPDrhL50UXdJhdWplxgqiJ1eepucNZ2gtAcWzTS19ejdzJ84fR96wLOnwTZeuea0SiOUVoEoHWws6AjkxC56tp+9/jFAjDgiCkNKUIVVwRIVrMc57/UUsikA3PRPBIaP5FhgoQ7uu6FFv/nXbq+4TKu+dckIFkwP+iR0cj6X6koDraSfh86W6oR0dyAAg7/2Gbs9+QW2gEZBLkqZkVDJr/7YUpVz/zcqYFGhGiICEDG4t3QKhCTRqRhuKERqvIUUY66A7AgvQ95+YfNLK8LhostGjAOryyr/FGf2Uu1nLpN+R2N9vskGt4WbtmD6bEaWM= <EMAIL>',
          },
        }[C.name],
      },
    },
  },
}

#!/bin/bash

info() {
	local fmt="$1"
	shift
	printf "[init.sh] $fmt\n" "$@"
}

info "Network Configuration:"
ip add

if [[ ! -d /etc/ssh/authorized_keys/ ]]; then
	info "ERROR no SSH authorized_keys mounted from a k8s configmap."
	exit 1
fi

if [[ ! -d /etc/ssh/keys/ ]]; then
	info "WARNING SSH host keys should be mounted from a persistent k8s secret."
	info "WARNING Generating !EMPHMERAL! SSH host keys..."
	/bin/mkdir -p /etc/ssh/keys
	/usr/bin/ssh-keygen -A
	/bin/mv /etc/ssh/ssh_host*key* /etc/ssh/keys/
fi

info "Installing profile.d snippets..."

cat > /etc/profile.d/00-xdg.sh <<'EOF'
XDG_RUNTIME_DIR=/run/user/$(id -u)
[[ -d "$XDG_RUNTIME_DIR" ]] && export XDG_RUNTIME_DIR
export XDG_CONFIG_DIR="$HOME/.config"
EOF

cat > /etc/profile.d/augment-kubeconfig.sh <<'EOF'
# Kubeconfigs use first match as priority. Setup the following order:
#
#   0. $XDG_CONFIG_DIR/kube/config
#   1. $XDG_CONFIG_DIR/kube/config.d/*.conf [00 -> ZZ]
#   2. $HOME/.kube/config
#   3. $HOME/.kube/config.d/*.conf [00 -> ZZ]
#   4. $XDG_RUNTIME_DIR/kubeconfig.d/*.conf [00 - ZZ]
#   5. /etc/kube/config

KUBECONFIG="$KUBECONFIG${KUBECONFIG:+":"}$XDG_CONFIG_DIR/kube/config"
for f in "$XDG_CONFIG_DIR"/kube/config.d/*.conf; do
    [[ -r "$f" ]] && KUBECONFIG="$KUBECONFIG:$f"
done

KUBECONFIG="$KUBECONFIG:$HOME/.kube/config"
for f in "$HOME"/.kube/config.d/*.conf; do
    [[ -r "$f" ]] && KUBECONFIG="$KUBECONFIG:$f"
done

for f in "$XDG_RUNTIME_DIR"/kubeconfig.d/*.conf; do
    [[ -r "$f" ]] && KUBECONFIG="$KUBECONFIG:$f"
done

if [[ -e /etc/kube/config ]]; then
    if [[ ! -e "$HOME/.kube/config" ]]; then
        mkdir -p "$HOME/.kube"
        cp /etc/kube/config "$HOME/.kube/config"
    fi
    KUBECONFIG="$KUBECONFIG:/etc/kube/config"
fi

# Revert back to a single file, since a number of k8s libraries don't handle multiple paths
for _p in $(tr ':' $'\n' <<<"$KUBECONFIG"); do
  if [[ -e "$_p" ]]; then
    KUBECONFIG="$_p"
    break
  fi
done
unset _p

export KUBECONFIG
EOF

{
	while true; do
		info "Starting augi user os-set-all --daemon..."
		augi user os-set-all --daemon
		info "augi user os-set-all exited with code: %d, restarting..." "$?"
		sleep 1 # avoid tight error loop
	done
} &
{
	# NOTE(mattm): Run this way (without a --daemon flag) so that we can pick up the latest each time.
	while true; do
		info "Running augi release sync..."
		augi release sync --config-file /run/augment/secrets/augi-release-readers/.dockerconfigjson
		info "augi release sync exited with code=%d" "$?"

		info "Running augi release update..."
		augi release update
		info "augi release update exited with code=%d" "$?"
		sleep 10m
	done
} &

info "SSH configs:"
/usr/bin/tree /etc/ssh

info "Execing SSH..."
mkdir -p /run/sshd
exec /usr/sbin/sshd -D -e

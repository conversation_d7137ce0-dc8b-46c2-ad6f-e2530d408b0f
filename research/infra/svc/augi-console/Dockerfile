ARG UBUNTU_VERSION
FROM ubuntu:$UBUNTU_VERSION

RUN apt-get update && apt-get install -y curl gpg

# https://cloud.google.com/sdk/docs/install#deb
# For google-cloud-* below
# NOTE(mattm): This should be a noop on GPU images as it's already done on the nvidia or determined base image. We also only
# want google-cloud-* packages from this repo, but it also at least is known to provide kubectl.
RUN curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | gpg --dearmor > /usr/share/keyrings/cloud.google.gpg \
 && printf "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main\n" > /etc/apt/sources.list.d/google-cloud-sdk.list \
 && printf "Package: google-cloud-*\n"  >  /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "Pin: release o=cloud-sdk\n" >> /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "Pin-Priority: 500\n"        >> /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "\n"                         >> /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "Package: kubectl\n"         >> /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "Pin: release o=cloud-sdk\n" >> /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "Pin-Priority: -1\n"         >> /etc/apt/preferences.d/99-augment-google-cloud

RUN apt-get update \
 && apt-get upgrade -y \
 && apt-get clean

RUN apt-get install -y \
    curl \
    dumb-init \
    git \
    golang \
    google-cloud-cli \
    google-cloud-cli-gke-gcloud-auth-plugin \
    iproute2 \
    rsync \
    ssh \
    tmux \
    tree \
    vim \
 && apt-get clean

ENV LANG=C.UTF-8
RUN groupadd -r augment

RUN curl -sL -o /usr/local/bin/kubectl "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl" \
 && chmod u=rwx,go=rx /usr/local/bin/kubectl

# gcloud-lgin helper/wrapper
RUN --mount=type=bind,from=devpod,target=/mnt/devpod : \
 && install -oroot -groot -m0755 /mnt/devpod/gcloud-login /usr/local/bin/ \
 && echo '# If interactive, not a system user, and available'                                  >  /etc/profile.d/90-augment-gcloud-login.sh \
 && echo 'if [ "$PS1" -a `id -u` -ge 1000 ] && command -v gcloud-login > /dev/null 2>&1; then' >> /etc/profile.d/90-augment-gcloud-login.sh \
 && echo '    gcloud-login --quiet'                                                            >> /etc/profile.d/90-augment-gcloud-login.sh \
 && echo 'fi'                                                                                  >> /etc/profile.d/90-augment-gcloud-login.sh \
 && chown root:root /etc/profile.d/90-augment-gcloud-login.sh \
 && chmod u=rw,go=r /etc/profile.d/90-augment-gcloud-login.sh

COPY --from=bin --chmod=555 augi /usr/local/bin/augi.live
RUN /usr/local/bin/augi.live release select live
COPY --chmod=754 init.sh /usr/local/sbin/init.sh
COPY --chmod=664 50-augi-console.sshd.conf /etc/ssh/sshd_config.d/

COPY --chmod=644 kubeconfig.yaml /etc/kube/config

ENTRYPOINT ["/usr/bin/dumb-init", "--verbose", "--", "/usr/local/sbin/init.sh"]

NAME    := augi-console
VERSION := 2024-12-29.1
CLUSTER := gcp-us1
UBUNTU_VERSION := 24.04

REGISTRY := $(shell jsonnet -A name="$(CLUSTER)" -A prop=registry -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
IMAGE    := $(REGISTRY)/$(NAME):$(VERSION)

CONTEXT  := $(shell jsonnet -A name="$(CLUSTER)" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
K8S_CONF := $(NAME).jsonnet
KUBECTL  := kubectl --context="$(CONTEXT)"
KUBECFG  := kubecfg --context="$(CONTEXT)" \
	   --tla-code="cluster='$(CLUSTER)'" \
	   --tla-code="name='$(NAME)'" \
	   --tla-code="version='$(VERSION)'" \
	   --tla-code="image='$(IMAGE)'"

build:
	bazel build //research/infra:augi
	docker build --pull -t $(IMAGE) --build-arg=UBUNTU_VERSION="$(UBUNTU_VERSION)" --build-context=bin="$$(bazel info bazel-bin)/research/infra/augi_/" --build-context=devpod=../../lib/augment/devpod/ .

push: build
	docker push $(IMAGE)

show:
	$(KUBECFG) show $(K8S_CONF)

diff:
	-$(KUBECFG) diff --diff-strategy=subset $(K8S_CONF)

apply:
	$(KUBECFG) update $(K8S_CONF)

logs:
	$(KUBECTL) logs -l aug.service=$(NAME) $(LOGS_EXTRA)

list:
	$(KUBECTL) get -l aug.service=$(NAME) deploy,pod,svc,ing,pvc,role,rolebinding,sa,cm,secret,sealedsecret

deploy: diff push apply

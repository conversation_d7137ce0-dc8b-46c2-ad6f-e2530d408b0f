local lib = import '../../cfg/clusters/base-resources/tmpl/lib.jsonnet';
local clusters = import '../../cfg/clusters/clusters.jsonnet';
local vendor = {
  '16.3.4': importbin 'postgresql-16.3.4.tgz',
};

function(
  cluster='gcp-us1',
  name='research-sql',
  namespace='research-sql',
  version='16.3.4',

  db_name='datasets',
  user_name='augment',
  user_pw='AgCUKdA7qGdI+TOlrvAdPxHtzw1eOia/C/as5ChmlGuIzPmn5TZHZ8NoyM1+7x0JX76W3GTegjvBN+Ixhi1J8yJvv/zPnFsooFpFSqgZ0cEDhVwWA8k3oTXwXYw/cn6CZ8Z7Gs0TNHkQXS5m0h2s8xrBIBcOyTzt47ZlLNFC2XGfZyYlpfGKrcwO8Qzvo95AJzAnoZ/RprEfMZTYkZ+IoIcMCAE0brlbJa71i6eIQAg2N0AD0D8t0z2vP2b2z9GyViqyoVPqoYKCAtLnbw+nxCxVDmqNXGH7nPOqIC/ojio90WvGwYWxZM6OYIf7wRsgdm+W5B/JZ2WwchrHoJAGQ+91guCh/W6LOtpVIOJJCiSp7mwdifetkMuCjSEb1pjRnMJCEMglMuBHp9kC2LPYu8y2hHbnQpOlzN9dOavmQEfBXGrVpaDjIlKrMAHX49PBhW6JPX9LFyM54+f6MvBFvDUINtszmlworKDT65/JrAJvxQNvlCXiNryrHmf4oOkmYIUT8tjCWlUh/7PFcXX81Hjm6OY4FVzMz0V4j9czGtZWJxl+z75XgM7E3CevgxVNq8iXzIT9NYj+Gbs8hKuvkgRhgdFSCnCiLmu5kwCbI7qYbw8nlt1kxicKFTet680OY+OLojihUqy/rlk8Jr5MY1Y8gEAAg9nUY6dOHsT5Bh3aM9Mfg7V7dGbv6p1bgmtVASzOC9HJsrmZADSoLnizITodrXu5Uq19qTiNxf3dhw==',

  resources_preset='medium',
  storage_class='premium-rwo',
  storage_size='8Gi',  // the default, we were at 2+Gi on CoreWeave

) local C = clusters.cluster(cluster); C.k8s + {

  ns: $.Namespace + {
    name:: namespace,
  },

  Object+:: {
    name:: name,
    namespace:: $.ns.metadata.name,
  },

  auth: $.SealedSecret + {
    name+:: '-auth',
    encryptedData: {
      user_pw: user_pw,
      postgres_pw: self.user_pw,  // Use the same pw, but this user is local-only and local is trusted by pg_hba.conf.
    },
  },

  release: lib.ParseHelmChart(vendor[version], name, namespace, values={
    commonLabels+: {
      'aug.service': name,
      'aug.cluster': C.name,
    },
    auth+: {
      enablePostgresUser: true,
      database: db_name,
      username: user_name,
      existingSecret: $.auth.name,
      secretKeys+: {
        adminPasswordKey: 'postgres_pw',  // pragma: allowlist secret
        userPasswordKey: 'user_pw',  // pragma: allowlist secret
      },
    },
    primary+: {
      service+: {
        type: 'ClusterIP',
      },
      resourcesPreset: resources_preset,
      persistence+: {
        enabled: true,
        storageClass: storage_class,
        size: storage_size,
      },
      pgHbaConfiguration: |||
        # Trust everything local (needed for initial user creation).
        local    all  all                trust
        host     all  all  ::1/128       trust
        hostssl  all  all  ::1/128       trust
        host     all  all  127.0.0.1/32  trust
        hostssl  all  all  127.0.0.1/32  trust

        # Remote auth.
        hostssl %s %s all md5
        host    %s %s 10.0.0.0/8 trust

        # Uncomment to enable remote administration.
        # hostssl all postgres all md5
      ||| % [db_name, user_name, db_name, user_name],
    },
    rbac+: {
      create: true,
    },
    volumePermissions+: {
      enabled: true,
    },
  }),
}

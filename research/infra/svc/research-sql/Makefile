NAME      := research-sql
NAMESPACE := $(NAME)
CLUSTER   := gcp-us1

CONTEXT  := $(shell jsonnet -A name="$(CLUSTER)" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
K8S_CONF := $(NAME).jsonnet
KUBECTL  := kubectl --context="$(CONTEXT)" --namespace="$(NAMESPACE)"
KUBECFG  := kubecfg --context="$(CONTEXT)" --namespace="$(NAMESPACE)" \
	   --tla-code="name='$(NAME)'" \
	   --tla-code="namespace='$(NAMESPACE)'" \
	   --tla-code="cluster='$(CLUSTER)'"

show:
	$(KUBECFG) show $(K8S_CONF)

diff:
	-$(KUBECFG) diff --diff-strategy=subset $(K8S_CONF)

apply:
	$(KUBECFG) update $(K8S_CONF)

list:
	$(KUBECTL) get -n "$(NAMESPACE)" certificate,sealedsecret,secret,cm,pvc,sa,networkpolicy,svc,ing,deploy,statefulset,job,pod,role,rolebinding

pull:
	helm pull bitnami/postgresql

connect:
	psql "postgresql://augment@$(NAME)-postgresql.$(NAMESPACE).svc.cluster.local/datasets"

deploy: diff apply

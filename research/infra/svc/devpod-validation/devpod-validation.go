package main

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"sort"
	"strings"

	flag "github.com/spf13/pflag"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/infra/lib/logger"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod/crd/devpodv1"
)

func main() {
	svc := Service{Logger: logger.New(nil)}

	flag.Int32VarP(&svc.port, "port", "p", 8888, "Listening port.")
	flag.StringVar(&svc.tlsCertFile, "tls-cert-file", "", "TLS Public Key File.")
	flag.StringVar(&svc.tlsKeyFile, "tls-key-file", "", "TLS Private Key File.")
	flag.Parse()

	ctx := context.Background()
	if err := svc.Run(ctx); err != nil {
		svc.LogErr("%v", err)
		os.Exit(1)
	}
}

type Service struct {
	logger.Logger
	port        int32
	tlsCertFile string
	tlsKeyFile  string
}

func (svc *Service) Run(ctx context.Context) error {
	addr := fmt.Sprintf(":%d", svc.port)
	mux := http.NewServeMux()
	srv := &http.Server{
		Addr:    addr,
		Handler: mux,
	}
	svc.register(mux)
	svc.LogInfo("Listening on %s...", addr)
	if crt, key := svc.tlsCertFile, svc.tlsKeyFile; crt != "" && key != "" {
		return srv.ListenAndServeTLS(crt, key)
	} else {
		return srv.ListenAndServe()
	}
}

func (svc *Service) register(mux *http.ServeMux) {
	mux.HandleFunc("/", svc.handle)
}

func (svc *Service) logRequest(r *http.Request) {
	hdrs := func() string {
		keys := []string{}
		for k := range r.Header {
			keys = append(keys, k)
		}
		sort.Strings(keys)

		hdrs := []string{}
		for _, k := range keys {
			hdrs = append(hdrs, fmt.Sprintf("%s: %v", k, strings.Join(r.Header.Values(k), ", ")))
		}

		return strings.Join(hdrs, "; ")
	}
	hum := func() string {
		n := float64(r.ContentLength)
		u := ""
		us := []string{"Ki", "Mi", "Gi", "Ti"}
		for n >= 1024 && len(us) > 0 {
			n /= 1024
			u, us = us[0], us[1:]
		}
		return fmt.Sprintf("%.2f%s", n, u)
	}
	svc.LogInfo("RECV FROM %s %s %s %sB. HEADERS %s.", r.RemoteAddr, r.Method, r.RequestURI, hum(), hdrs())
}

func (svc *Service) logResponse(w http.ResponseWriter, r *http.Request, code int, err error) {
	if err != nil {
		if code < 400 {
			code = http.StatusInternalServerError
		}
		svc.LogErr("REPLY [%d] TO %s %s %s: %v.", code, r.RemoteAddr, r.Method, r.RequestURI, err)
		http.Error(w, err.Error(), code)
	} else {
		if code == 0 {
			code = http.StatusOK
		}
		svc.LogInfo("REPLY [%d] TO %s %s %s.", code, r.RemoteAddr, r.Method, r.RequestURI)
		if code != http.StatusOK {
			w.WriteHeader(code)
		}
	}
}

func (svc *Service) handle(w http.ResponseWriter, r *http.Request) {
	svc.logRequest(r)
	if r.URL.Path != "/devpod-validation" {
		svc.logResponse(w, r, http.StatusNotFound, fmt.Errorf("not found"))
		return
	} else if r.Method != http.MethodPost {
		svc.logResponse(w, r, http.StatusMethodNotAllowed, fmt.Errorf("method not allowed"))
		return
	}

	rev := k8s.NewAdmissionReview(nil)

	if body, err := io.ReadAll(r.Body); err != nil {
		svc.logResponse(w, r, http.StatusInternalServerError, err)
		return
	} else if err := rev.FromJSON(string(body)); err != nil {
		svc.logResponse(w, r, http.StatusBadRequest, err)
		return
	} else if err := svc.handleReview(r.Context(), rev); err != nil {
		svc.logResponse(w, r, http.StatusBadRequest, err)
		return
	} else if rbuf, err := rev.JSON(); err != nil {
		svc.logResponse(w, r, http.StatusInternalServerError, err)
		return
	} else if _, err := w.Write([]byte(rbuf + "\n")); err != nil {
		svc.logResponse(w, r, http.StatusInternalServerError, err)
		return
	} else {
		w.Header().Add("Content-Type", "application/json; charset=utf-8")
		svc.logResponse(w, r, http.StatusOK, nil)
		return
	}
}

func (svc *Service) handleReview(ctx context.Context, rev *k8s.AdmissionReview) error {
	if str, _ := rev.JSON(); true {
		svc.LogInfo("Got Review:\n%s", str)
	}

	req := rev.Request()
	if req == nil {
		return fmt.Errorf("empty AdmissionReview.Request")
	}

	// Initialize response, default deny.
	resp := rev.InitResponse()
	resp.UID = req.UID
	resp.Allowed = false

	if !rev.IsGVK(devpodv1.APIGroup, devpodv1.APIVersion, devpodv1.DevPodKind) {
		svc.LogWarn("Received %+v: allowing...", rev.GVK())
		resp.Allowed = true
	} else if devpod, err := devpodv1.NewDevPodFromAdmissionReview(rev); err != nil {
		return err
	} else if err := svc.handleDevPodReview(rev, devpod); err != nil {
		rev.SetStatus(http.StatusBadRequest, metav1.StatusReasonBadRequest, err)
	} else {
		resp.Allowed = true
	}

	if str, _ := rev.JSON(); true {
		svc.LogInfo("Sending Response:\n%s", str)
	}
	return nil
}

func (svc *Service) handleDevPodReview(rev *k8s.AdmissionReview, devpod *devpodv1.DevPod) error {
	return devpod.Validate()
}

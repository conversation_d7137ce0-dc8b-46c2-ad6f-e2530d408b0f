local clusters = import '../../cfg/clusters/clusters.jsonnet';
local crd = import '../../lib/augment/devpod/crd/devpodv1/devpodv1.jsonnet';

function(
  cluster='gcp-us1',
  name='devpod-validation',
  version='latest',
  image=null,
  port=4430,
  replicas=1,
  cpu='1',
  ram='2G',
) local C = clusters.cluster(cluster); C.k8s + {

  BaseLabels+:: std.prune({
    'aug.service': name,
    'aug.version': version,
  }),

  BaseObject+:: {
    name:: name,
  },

  Object+:: {
    namespace:: C.sys_namespace,
  },

  // Somewhat arbitrary, but use this validation hook to deploy the CRD as well.
  // (Other options: as part of the cluster, standalone, with the devpod-controller).
  crd: crd(cluster),

  hookcfg: $.ValidatingWebhookConfiguration + {
    metadata+: {
      annotations+: {
        'cert-manager.io/inject-ca-from': $.cert.namespace + '/' + $.cert.name,
      },
    },
    webhooks: [
      $.ValidatingWebhook + {
        name: name + '.' + C.name + '.r.augmentcode.com',
        clientConfig+: {
          service: {
            name: $.service.name,
            namespace: $.service.namespace,
            port: 443,
            path: '/devpod-validation',
          },
        },
        failurePolicy: self.FAILURE_POLICY.FAIL,
        matchPolicy: self.MATCH_POLICY.EXACT,
        sideEffects: self.SIDE_EFFECTS.NONE,
        namespaceSelector+: {
          matchExpressions: [
            // Satisfy https://cloud.google.com/kubernetes-engine/docs/how-to/optimize-webhooks#unsafe-webhooks.
            {
              key: 'kubernetes.io/metadata.name',
              operator: 'NotIn',
              values: ['kube-system', 'kube-node-lease'],
            },
          ],
        },
        rules: [
          self.Rule + {
            operations: [self.OPERATION_TYPE.CREATE, self.OPERATION_TYPE.UPDATE],
            apiGroups: ['r.augmentcode.com'],
            apiVersions: ['v1'],
            resources: ['devpods'],
            scope: self.SCOPE_TYPE.NAMESPACED,
          },
        ],
      },
    ],
  },

  deployment: $.Deployment + {
    spec+: {
      replicas: replicas,
      template+: {
        spec+: {
          local pod = self,
          containers: [
            $.Container + {
              name: name,
              image: image,
              volumeMounts: pod.volmount_mounts,
              resources+: {
                limits+: {
                  cpu: cpu,
                  memory: ram,
                },
              },
              args: [
                '--port=' + port,
                '--tls-cert-file=/run/secrets/' + $.cert.spec.secretName + '/tls.crt',
                '--tls-key-file=/run/secrets/' + $.cert.spec.secretName + '/tls.key',
              ],
            },
          ],
          volmounts:: [
            {
              name:: $.cert.spec.secretName,
              volume:: {
                secret: {
                  secretName: $.cert.spec.secretName,
                  defaultMode: std.parseOctal('0444'),
                },
              },
              mount:: {
                mountPath: '/run/secrets/' + $.cert.spec.secretName,
                readOnly: true,
              },
            },
          ],
          tolerations+: [
            {
              effect: 'NoSchedule',
              key: 'r.augmentcode.com/pool-type',
              value: 'svc',
            },
            {
              effect: 'PreferNoSchedule',
              key: 'r.augmentcode.com/pool-type',
              value: 'svc',
            },
          ],
        },
      },
    },
  },

  service: $.Service + {
    spec+: {
      selector+: {
        'k8s.deployment': $.deployment.name,
      },
      type: $.SERVICE_TYPE.CLUSTER_IP,
      ports: [
        {
          name: 'https',
          port: 443,
          targetPort: port,
        },
      ],
    },
  },

  cert: $.Certificate + {
    spec+: {
      issuerRef+: {
        kind: self.CLUSTER_ISSUER,
        name: 'selfsigned',
      },
      dnsNames: [
        std.join('.', [$.service.name, $.service.namespace, 'svc']),
        std.join('.', [$.service.name, $.service.namespace, 'svc.cluster.local']),
      ],
      secretName+: '-tls',
    },
  },
}

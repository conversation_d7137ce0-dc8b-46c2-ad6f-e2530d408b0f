load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library", "go_test")

package(default_visibility = ["//visibility:private"])

go_library(
    name = "devpod-validation_lib",
    srcs = ["devpod-validation.go"],
    importpath = "github.com/augmentcode/augment/research/infra/svc/devpod-validation",
    deps = [
        "//infra/lib/k8s",
        "//infra/lib/logger",
        "//research/infra/lib/augment/devpod/crd/devpodv1",
        "@com_github_spf13_pflag//:pflag",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
    ],
)

go_binary(
    name = "devpod-validation",
    embed = [":devpod-validation_lib"],
)

go_test(
    name = "devpod-validation_test",
    srcs = ["devpod-validation_test.go"],
    embed = [":devpod-validation_lib"],
)

NAME    := ssh-keys-syncer
VERSION := 2024-12-29.1
CLUSTER := gcp-core0

REGISTRY := $(shell jsonnet -A name="$(CLUSTER)" -A prop=registry -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
IMAGE     := $(REGISTRY)/$(NAME):$(VERSION)

DEST_CONF     := $(NAME)-dests.jsonnet
DEST_CLUSTERS := $(shell jsonnet -Se "local d = import '$(DEST_CONF)'; std.join(' ', d.DestClusters)")

CONTEXT  := $(shell jsonnet -A name="$(CLUSTER)" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
K8S_CONF := $(NAME).$(CLUSTER).jsonnet
KUBECTL  := kubectl --context="$(CONTEXT)"
KUBECFG  := kubecfg --context="$(CONTEXT)" \
	   --tla-code="cluster='$(CLUSTER)'" \
	   --tla-code="name='$(NAME)'" \
	   --tla-code="version='$(VERSION)'" \
	   --tla-code="image='$(IMAGE)'"

build:
	bazel build //research/infra:augi
	docker build --pull -t $(IMAGE) --build-context=bin="$$(bazel info bazel-bin)/research/infra/augi_/" .

push: build
	docker push $(IMAGE)

run-local:
	docker run -ti --rm --name test --hostname test $(IMAGE)

show:
	$(KUBECFG) show $(K8S_CONF)

diff:
	-$(KUBECFG) diff --diff-strategy=subset $(K8S_CONF)

apply:
	$(KUBECFG) update $(K8S_CONF)

logs:
	$(KUBECTL) logs -l aug.service=$(NAME) $(LOGS_EXTRA)

list:
	$(KUBECTL) get -Al aug.service=$(NAME) deploy,pod,svc,ing,pvc,role,rolebinding,sa,cm,secret,sealedsecret

deploy: diff push apply diff-dests apply-dests

show-dests:
	for d in $(DEST_CLUSTERS); do \
		context=$$(jsonnet -A name="$$d" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop") ; \
		echo "== $$d -> $$context"; \
		kubecfg --context="$$context" show -e "local d = import '$(DEST_CONF)'; d.DestClusterToK8s['$$d']"; \
	done

diff-dests:
	for d in $(DEST_CLUSTERS); do \
		context=$$(jsonnet -A name="$$d" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop") ; \
		echo "== $$d -> $$context"; \
		kubecfg --context="$$context" diff --diff-strategy=subset -e "local d = import '$(DEST_CONF)'; d.DestClusterToK8s['$$d']"; \
	done

apply-dests:
	for d in $(DEST_CLUSTERS); do \
		context=$$(jsonnet -A name="$$d" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop") ; \
		echo "== $$d -> $$context"; \
		kubecfg --context="$$context" update -e "local d = import '$(DEST_CONF)'; d.DestClusterToK8s['$$d']"; \
	done

list-dests:
	for d in $(DEST_CLUSTERS); do \
		context=$$(jsonnet -A name="$$d" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop") ; \
		echo "== $$d -> $$context"; \
		kubectl --context="$$context" get -l aug.service=$(NAME) deployment,replicaset,pod,service,ingress,secret,sealedsecret,configmap,pvc,role,rolebinding,sa; \
	done

## NOTE(mattm): This file is cached locally; this is not added as a dependency for the above targets.
$(NAME).kubeconfig.sealed.json: $(NAME).kubeconfig.sealed.json-gen.sh $(DEST_CONF)
	./$< > $@

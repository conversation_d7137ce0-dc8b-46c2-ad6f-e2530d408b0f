#!/bin/bash

# This script rebuilds the EncryptedData payload for the sealed secret.
# NOTE(mattm): The output of this is cached locally, generate it with `make ssh-keys-syncer.kubeconfig.sealed.json`.

declare -r main_cfg="ssh-keys-syncer.gcp-core0.jsonnet"
declare -r dest_cfg="ssh-keys-syncer-dests.jsonnet"

declare -r secname="$(jsonnet -Se "local k = import '$main_cfg'; k().kubeconfig_sec.spec.template.metadata.name")"
declare -a cmd=(
	kubectl create secret generic --dry-run=client -o json "$secname"
)
declare -ra dests=(
	$(jsonnet -Se "local d = import '$dest_cfg'; std.join(' ', d.KubeConfigs)")
)

for k in "${dests[@]}"; do
	IFS=: read -r spec_cluster cluster namespace <<<"$k"
	ctx=$(jsonnet -Se "local c = import '../../cfg/clusters/clusters.jsonnet'; c.cluster('$cluster').context_admin");
	sa=$(jsonnet -Se "local d = import '$dest_cfg'; d.DestClusterToK8s['$cluster'].sa.metadata.name")
	kcfg="$(augi build -U -- k8s sa-to-config --context="$ctx" --context-name="$spec_cluster" --namespace="$namespace" "$sa")"
	cmd+=("--from-literal=$spec_cluster.yaml=$kcfg")
done

"${cmd[@]}" | kubeseal --context=gcp-core0 --namespace=aug-system | jq -r '.spec.encryptedData'

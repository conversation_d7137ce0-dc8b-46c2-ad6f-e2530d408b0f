local clusters = import '../../cfg/clusters/clusters.jsonnet';
local dests = import 'ssh-keys-syncer-dests.jsonnet';

// NOTE(mattm): This file is cached locally, generate it with `make ssh-keys-syncer.kubeconfig.sealed.json`.
local encdata = import 'ssh-keys-syncer.kubeconfig.sealed.json';

function(
  cluster='gcp-core0',
  name='ssh-keys-syncer',
  version='latest',
  image=null,
  cpu='1',
  ram='4Gi',
) local C = clusters.cluster(cluster); C.k8s + {

  BaseLabels+:: std.prune({
    'aug.service': name,
    'aug.version': version,
  }),
  Object+:: {
    name:: name,
    namespace:: C.sys_namespace,
  },

  deploy: $.Deployment + {
    spec+: {
      replicas: 1,
      template+: {
        spec+: {
          local pod = self,
          local kpath = '/run/secrets/kubeconfigs',
          serviceAccountName: $.sa.metadata.name,
          containers: [$.Container + {
            name: $.deploy.metadata.name,
            image: if image != null then image else C.registry + '/' + name + ':' + version,
            args: ['--daemon', '--kubeconfig-base=' + kpath] + dests.SyncArgs,
            resources: {
              limits: {
                cpu: cpu,
                memory: ram,
              },
            },
            volumeMounts: pod.volmount_mounts,
          }],
          volmounts:: [
            {
              name: 'kubeconfigs',
              volume: {
                secret: {
                  secretName: $.kubeconfig_sec.metadata.name,
                  optional: true,  // TODO: FALSE
                  defaultMode: std.parseOctal('0444'),
                },
              },
              mount: {
                mountPath: kpath,
              },
            },
          ],
        },
      },
    },
  },

  sa: $.ServiceAccount + {},

  role: $.Role + {
    rules: [
      {
        apiGroups: [''],
        resources: ['configmaps'],
        resourceNames: [dests.SourceName],
        verbs: $.READ_VERBS,
      },
    ],
  },

  rolebinding: $.RoleBinding + {
    name+:: 's',
    role_name: $.role.metadata.name,
    sas:: [$.sa],
  },

  kubeconfig_sec: $.SealedSecret + {
    name+:: '-kubeconfigs',
    // NOTE(mattm): This file is cached locally, generate it with `make ssh-keys-syncer.kubeconfig.sealed.json`.
    encryptedData: encdata,
  },
}

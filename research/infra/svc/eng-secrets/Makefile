NAME    := eng-secrets
VERSION := 2024-10-23.1
CLUSTERS := cw-east4 gcp-us1

K8S_CONF := $(NAME).jsonnet
KUBECFG  := kubecfg --context="$$context" \
	   --tla-code="cluster_name='$$cluster'" \
	   --tla-code="job_inst='$(JOB_INST)'" \
	   --tla-code="image='$$image'" \
	   --tla-code="version='$(VERSION)'"

show:
	make loop LOOP_COMMAND=show

diff:
	make loop LOOP_COMMAND="diff --diff-strategy=subset"

apply:
	make loop LOOP_COMMAND=update

loop:
	for cluster in $(CLUSTERS); do \
		echo ====== "$$cluster" ======; \
		context=$$(jsonnet -A name="$$cluster" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop") ; \
		REGISTRY=$$(jsonnet -A name="$$cluster" -A prop=registry -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop"); \
		image=$$REGISTRY/$(shell cat ../../../environments/devpod_cpu_tag.txt); \
		$(KUBECFG) $(LOOP_COMMAND) $(K8S_CONF); \
	done

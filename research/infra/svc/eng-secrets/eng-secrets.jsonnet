local clusters = import '../../cfg/clusters/clusters.jsonnet';

local gh_app_auth_py = importstr '../invalidate-stale-prs/gh_app_auth.py';
local eng_secrets_sh = importstr 'eng-secrets.sh';
local requirements = importstr 'requirements.txt';

local name = 'eng-secrets';
local cmd = [
  'pip install -r /run/augment/' + name + '/requirements.txt',
  // ' && sleep 75000',  // uncomment for debugging
  ' && bash /run/augment/eng-secrets/eng-secrets.sh',
];

local run_command = std.join(' ', cmd);

function(cluster_name, version, image, job_inst='') local C = clusters.cluster(cluster_name); C.k8s + {
  by_cluster(obj)::
    if std.objectHas(obj, cluster_name)
    then obj[cluster_name]
    else std.get(obj, 'default'),

  filter_by_cluster(obj):: {
    [kv.key]: kv.value
    for kv in std.objectKeysValues(obj)
    if !std.isObject(kv.value) || std.member([null, cluster_name], std.get(kv.value, 'cluster_name'))
  },

  local as_job = job_inst != '',
  name:: name,

  gh_app_pem: C.k8s.SealedSecret + {
    name: 'gh-app-pem',
    encryptedData: $.by_cluster({
      'cw-east4': {
        'gh-app.pem': '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',
      },
      'gcp-us1': {
        'gh-app.pem': '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',
      },
    }),
  },

  configmap: C.k8s.ConfigMap + {
    name:: $.name,
    data: {
      'gh_app_auth.py': gh_app_auth_py,
      'requirements.txt': requirements,
      'eng-secrets.sh': eng_secrets_sh,
    },
  },
  jobspec:: {
    completions: 1,
    template+: {
      metadata+: {
        annotations+: {
          'config.md5': std.md5(std.manifestJson($.configmap.data)),
        },
      },
      spec+: {
        local pod = self,
        restartPolicy: 'Never',
        serviceAccountName: 'eng-secrets-sa',
        containers+: [
          {
            name: $.name,
            image: image,
            imagePullPolicy: 'IfNotPresent',
            env: [
              {
                name: 'GH_APP_PEM_PATH',
                value: '/mnt/augment/secrets/gh-app.pem',
              },
            ],
            command: [
              '/bin/bash',
              '-c',
              run_command + ' ' + cluster_name,
            ],
            volumeMounts: [
              {
                name: $.name,
                mountPath: '/run/augment/' + $.name,
              },
              {
                mountPath: '/mnt/augment/secrets/',
                name: 'gh-app-pem',
                readOnly: true,
              },
            ],
          },
        ],
        volumes: [
          {
            name: $.name,
            configMap: {
              name: $.name,
            },
          },
          {
            name: 'gh-app-pem',
            secret: {
              secretName: 'gh-app-pem',  // pragma: allowlist secret
            },
          },
        ],
      },
    },
  },
  cronjob:: C.k8s.CronJob + {
    local cj = self,
    name:: $.name,
    schedule:: '*/5 * * * *',
    concurrencyPolicy:: $.CRONJOB_POLICY.REPLACE,
    metadata+: {
      labels+: {
        'app.kubernetes.io/name': $.name,
        'aug.version': version,
      },
    },
    spec+: {
      jobTemplate+: {
        metadata+: {
          labels+: {
            'app.kubernetes.io/name': $.name,
            'aug.version': version,
          },
        },
        spec+: $.jobspec,
      },
    },
    podspec:: {},
  },
  job:: C.k8s.Job + {
    name: $.name + '-adhoc-' + job_inst,
    spec: $.jobspec,
    metadata+: {
      labels+: {
        'app.kubernetes.io/name': $.name,
        'aug.version': version,
      },
    },
  },

  cronjob_or_job: if as_job then $.job else $.cronjob,
}

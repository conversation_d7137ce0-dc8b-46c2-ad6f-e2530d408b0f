NAME    := expose-proxy
VERSION := 2025-02-23.2
CLUSTER := gcp-us1

# NOTE(mattm): Consider being explicit with the upstream versions.
VERSION_NGINX  := latest
VERSION_OAUTH2 := latest

REGISTRY := $(shell jsonnet -A name="$(CLUSTER)" -A prop=registry -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
IMAGE    := $(REGISTRY)/$(NAME)-augi:$(VERSION)

CONTEXT  := $(shell jsonnet -A name="$(CLUSTER)" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
K8S_CONF := $(NAME).jsonnet
KUBECTL  := kubectl --context="$(CONTEXT)"
KUBECFG  := kubecfg --context="$(CONTEXT)" --qps-limit=-1 \
	    --tla-code="cluster='$(CLUSTER)'" \
	    --tla-code="name='$(NAME)'" \
	    --tla-code="version='$(VERSION)'" \
	    --tla-code="version_nginx='$(VERSION_NGINX)'" \
	    --tla-code="version_oauth2='$(VERSION_OAUTH2)'" \
	    --tla-code="augi_image='$(IMAGE)'"

build:
	bazel build //research/infra:augi
	docker build --pull -t $(IMAGE) --build-context=bin="$$(bazel info bazel-bin)/research/infra/augi_/" .

push: build
	docker push $(IMAGE)

show:
	$(KUBECFG) show $(K8S_CONF)

diff:
	-$(KUBECFG) diff --diff-strategy=subset $(K8S_CONF)

apply:
	$(KUBECFG) update $(K8S_CONF)

logs:
	$(KUBECTL) logs -l aug.service=$(NAME) $(LOGS_EXTRA)

list:
	$(KUBECTL) get -n "$(NAME)" -l aug.service=$(NAME) cm,deploy,cj,job,pod,svc,ing,pvc,role,rolebinding,sa,secret,sealedsecret

deploy: diff push apply

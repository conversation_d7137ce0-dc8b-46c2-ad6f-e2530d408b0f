local clusters = import '../../cfg/clusters/clusters.jsonnet';

local nginx_conf_tmpl = importstr 'nginx.conf';

function(
  cluster='gcp-us1',
  name='expose-proxy',
  namespace='expose-proxy',
  version='latest',
  version_nginx='latest',
  version_oauth2='latest',
  image_nginx='nginx',
  image_oauth2='quay.io/oauth2-proxy/oauth2-proxy',
  replicas=1,
  cpu_nginx='4',
  ram_nginx='4Gi',
  cpu_oauth2='2',
  ram_oauth2='2Gi',
  augi_image=null,
  schedule='15 * * * *',
) {

  local C = clusters.cluster(cluster),
  local base_domain = 'exp.' + std.asciiLower(C.name) + '.r.augmentcode.com',
  local nginx_conf = nginx_conf_tmpl % {
    cluster_name: C.name,
  },

  local k8s = C.k8s + {
    BaseLabels+:: std.prune({
      'aug.service': name,
      'aug.version': version,
    }),

    Object+:: {
      name:: name,
      namespace:: namespace,
    },
  },

  ns: k8s.Namespace + {
    name:: namespace,
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Proxy Deployment + Svc + Ingress
  //
  // This contains pods with all of:
  //  - nginx (redirects <port>-<devpod>.exp.<cluster>.r.augmentcode.com)
  //  - oauth2_proxy
  //  - augi user tokens --serve (checks auth against internal_users.jsonnet tokens)
  //
  // The ingress takes care of External DNS and Lets Encrypt for the wildcard domain.
  //
  // TODO(mattm): Consider breaking these into separate, more modular services.
  //

  proxy: {
    local proxy = self,

    cfg: k8s.ConfigMap + {
      local s = self,
      conf_fname:: 'nginx.conf',
      data: {
        [s.conf_fname]: nginx_conf,
      },
    },

    oauth2_sec: k8s.SealedSecret + {
      name+:: '-oauth2',
      encryptedData: {
        client_sec: 'AgAqwh1MtjndWFFciU8QYSxjU98B2Xrl76GsiZfJoZ5I8DzeCGEOXoEvJ45myoFXAQ3LYK8a1T/hGWHvar0ltXCJ5mBUvvLb9wWpAhr6vgmcl1atNMrxtut7z8bYYgCMdcXrdX86+uesQSNfC5Uw2PPhrMXHs2w+9WNqcDbqT4HDucZuQbFw91wd0q/yFch9eMGS9tCrLlJqcEpZDDg5Ab6VTpkuEGwOZiD2eIv1s3M8KUZM8gxQQTZNZrjt372gnRb9QcnHaU4vWnHmbeH/NsliTRsK/bf4j36AGQ4FO+dnE243ILgp0qFuUIL8stY6VTU0WchgnD6Bbk2nPFPeF2c7gHzzmfmklqNvpVcBcKlJMhuN4sgRsibZeLhFppMB2cbMXVRAwHoiAYj61WKo85InSzz0g7K0GTLktMLzOA3dfLvkrGKtfezm1jpuWOs/Vo+bGoRJLcDgOlbK1cOo6DPk8OihX24qmIhJgK/bm1BPCRh7sxYvwUr6dX7sdMJFMR5El8Phdz03OP8Q24zbmaSyJRb8JGKgI/tLcP3ieNAb6xWhAOCAAQ7BknU6sbEdU8FBrL6baI49FOIXDq9Qij6XlnGTZNdwd8BvGFGqezrRMfMDuj3W2X6tvzKkuvmAmCkDLC3HxNRCRKY9JYwiCRPf0o2qORPmtQ66pJKFVqWSG8QGcX2beTJjyKx2zmfwE60tBTzjSh2XRWH3Wi8gKIB+irs//R5SDx3QKuSDrAFTb14few==',
        cookie_sec: 'AgCWtqzAVEDNm36zpcSfGq9I9d/SfMUlKaSZfY9Ff60qmoBeeaMiPXC8c4xCJyMoD63V8NsLLoM2BMpkOwjbRX7qdDyTeofkdEiYh40lBYz1yY1JIKLUiKnDt9GDDAcyFLsFcrg+mqy5tJmoRTGVA2b7zvB/Ux/ZDrMLyGrUp43I3x24QLK2qI7/G0ebqkMEzFrp9psJUdDt4mwbspwj2+EVDcc7BujMW2i7pQmLrI98PeP+c/rlo7AK9TUUYvp6VG08RkS8awXkyCSA9RGVccDzRLO9tMqpWJN8Y36hLvbE1ML0p/CwhDurter5joOS8mazWx7OB8wgEw+5E7RXBh4N84QXDPux+c7EPtfqfQDmU6kvc0YCJbbsY/XIm52P736OhP3mQPSNXMPDl59rkc03HnVAKwq6oZmDbqjTahl+iw6Ks29gfWdsypFEyNf0ffU7DI8yjp1x5blrP5PweUpAtVJcP1n7DEzuwIyXNzsNUbosIHE4v1LhD2aqMsUiEDVWN2MPeYf8zYDDn/65shKYLGBwCVA62xn9/9npdoApzoaY+4PqkxnXsIGsdJXxUZgIXgpuCU0eUocdQWHS9yvWCaQMnxcoecedz7P/mucQWCHzL8KlkoF7wWoV5LpeSWKssPxTiaMcxX10rANbU6FvDPwuCvtr31geHFeLC9zWWcJLamO8ezIhXTuXh00Sxg/MTqYt/evzkZwuqm8ucqAQyRJwciTGhvxIwgCMYroewJI=',
      },
      spec+: {
        template+: {
          data+: {
            client_id: '670*********-jij6ujbihu91u40hia6a3hullfcjhb66.apps.googleusercontent.com',
          },
        },
      },
    },

    deploy: k8s.Deployment + {
      spec+: {
        replicas: replicas,
        template+: {
          metadata+: {
            annotations+: {
              'nginx-conf-md5': std.md5(nginx_conf),  // force updates on config changes
            },
          },
          spec+: {
            local pod = self,
            conf_mount:: '/run/augment/' + proxy.cfg.metadata.name,
            serviceAccountName: proxy.sa.metadata.name,
            containers: [
              k8s.Container + {
                name: 'nginx',
                image: image_nginx + ':' + version_nginx,
                volumeMounts: pod.volmount_mounts,
                resources+: {
                  limits+: {
                    cpu: cpu_nginx,
                    memory: ram_nginx,
                  },
                },
                command: ['nginx', '-g', 'daemon off;', '-c', pod.conf_mount + '/' + proxy.cfg.conf_fname],
              },
              k8s.Container + {
                name: 'oauth2',
                image: image_oauth2 + ':' + version_oauth2,
                resources+: {
                  limits+: {
                    cpu: cpu_oauth2,
                    memory: ram_oauth2,
                  },
                },
                env: [
                  {
                    name: 'OAUTH2_PROXY_CLIENT_ID',
                    valueFrom: {
                      secretKeyRef: {
                        name: proxy.oauth2_sec.metadata.name,
                        key: 'client_id',
                      },
                    },
                  },
                  {
                    name: 'OAUTH2_PROXY_CLIENT_SECRET',
                    valueFrom: {
                      secretKeyRef: {
                        name: proxy.oauth2_sec.metadata.name,
                        key: 'client_sec',
                      },
                    },
                  },
                  {
                    name: 'OAUTH2_PROXY_COOKIE_SECRET',
                    valueFrom: {
                      secretKeyRef: {
                        name: proxy.oauth2_sec.metadata.name,
                        key: 'cookie_sec',
                      },
                    },
                  },
                ],
                args: [
                  '--http-address=http://127.0.0.1:4180',
                  '--reverse-proxy=true',
                  '--redirect-url=https://' + base_domain + '/oauth2/callback',
                  '--whitelist-domain=*.augmentcode.com',
                  '--provider=google',
                  '--email-domain=augmentcode.com',
                  '--cookie-domain=.' + base_domain + ',' + base_domain,
                  '--cookie-secure=true',
                  '--cookie-expire=168h',
                  '--cookie-csrf-per-request=true',
                  '--cookie-csrf-expire=5m',
                ],
              },
              k8s.Container + {
                name: 'user-tokens',
                image: augi_image,
                args: ['user', 'tokens', '--from-k8s=' + $.tokens.name, '--serve=8081'],
              },
            ],
            volmounts:: [
              {
                local vm = self,
                name:: proxy.cfg.metadata.name,
                volume:: {
                  configMap: {
                    name: vm.name,
                  },
                },
                mount:: {
                  mountPath: pod.conf_mount,
                },
              },
            ],
          },
        },
      },
    },

    svc: k8s.Service + {
      hostname:: name,
      spec+: {
        selector+: {
          'k8s.deployment': proxy.deploy.metadata.name,
        },
        ports: [
          {
            name: 'http',
            port: 8080,
            targetPort: 8080,
            protocol: 'TCP',
          },
        ],
      },
    },

    ing: k8s.Ingress + {
      local ing = self,
      metadata+: {
        annotations+: {
          'cert-manager.io/cluster-issuer': 'letsencrypt-prod',
        },
      },
      spec+: {
        ingressClassName: 'nginx',
        rules: [
          {
            host: base_domain,
            http: {
              paths: [{
                path: '/',
                pathType: 'Prefix',
                backend: {
                  service: {
                    name: proxy.svc.metadata.name,
                    port: {
                      number: 8080,
                    },
                  },
                },
              }],
            },
          },
          {
            host: '*.' + base_domain,
            http: {
              paths: [{
                path: '/',
                pathType: 'Prefix',
                backend: {
                  service: {
                    name: proxy.svc.metadata.name,
                    port: {
                      number: 8080,
                    },
                  },
                },
              }],
            },
          },
        ],
        tls: [{
          hosts: [base_domain, '*.' + base_domain],
          secretName: ing.metadata.name + '-tls',
        }],
      },
    },

    sa: k8s.ServiceAccount + {},

    role: k8s.Role + {
      rules: [
        {
          apiGroups: [''],
          resources: ['secrets'],
          resourceNames: [$.tokens.name],
          verbs: k8s.READ_VERBS,
        },
      ],
    },

    rb: k8s.RoleBinding + {
      role_name: proxy.role.metadata.name,
      sas: [proxy.sa],
    },
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Augment Tokens Secret
  //
  // This is read by the `augi user tokens --serve` container in the above
  // deployment and is written to by the getter in the below CronJob.
  //

  tokens: k8s.Secret + {
    name+:: '-user-tokens',
    i_know_what_im_doing:: true,  // this just defines the empty sec, to be filled in dynamically.
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Augment Tokens Getter (CronJob)
  //
  // Use a GitHub Token to read and evaluate 'tenants/tokens/internal_users.jsonnet' from
  // github.com/augmentcode/augment.git@main and dump into the above Secret.
  //

  tokens_getter: {
    local tg = self,

    gh_app_installation: k8s.SealedSecret + {
      name: 'github-augment-eng-read',
      encryptedData: {
        app_id: 'AgCqIQKoOyVbDNBt4b+XOboXy7APyG5rVZQ5B+GS4+M2Avh6zWMkfzhm+jl9dAfRxydXI40QbbQV48BYLzL6iSYqS1yJzgHPjgb4EuKSVPxYR/1KelG5oRjLeCt1KsRQqK/RrJwjdxiiegOFOanLUe3LHG5VtqNB+jzen1XObni16LGFIOSaJTiMdX3a2RjjOUoGEjuPG+o/wUWvJ8qP00QDk9gCItMPT2i3PDtxJxxstPN/Wm4swXoCxFEA9QwUPubVn/+0uyHJWxCLceKEesP9bn61s5VFwIKSogd1zZhDzjZWxt5SaG+hvH5g1rK/caw9FPd4Pi8LKEwqzs9U4s4W1v/5lPAG16T7TIvoLnvgdHFACcLgqj+PBRfNTcH1EfnRC473dSoeUpAWQg7kAHo2Jq/yIlG8zdQQZ1xSwkevdwEN8Wu3oyUixWg1Dq667HUHY0R00u6YI1GyTqolJ+/oyLTIcsHSU/g3i75+xcGL4atShC540ncTJca/GtHOnVBv4FPAIbBjcs524bC1amppSkhodPkZD7AchR8vqDQiNkOrSlAxWBIUdzB2eU6ydeKl/qsr38dcT5S52u7nyvaTDSUKN6/fopBncVv9m13fac42Wf+o2MGFvQr3Vzsdadhm545JWtCYFJ9lTHC9qNgCWi7JzoLTNfG2FWR9d4poJ6b7NMUKw6naIhwEm7URE7+mVstaRJc=',
        installation_id: 'AgBNlSlFmtxE/a3G8+N8qAUp7jMxiC8gEW50RsO+YDrUVTqZDj9B3SWt/yZtcTcawVXOft1b6sCRlor63PCQbMwsG+dPcz8EHJDvYnxggY9rtjdtHgWIEof+XUozpgJH9fPIj7VE8iXLh8YMp5Xfy9E7mn/2ZxfOsfNWWRVDXvyQgaAUFd9YaqL+yow1tdf8ee5nEZtkN/xVvhPajVsdqPr2sYxfKKRV2jMpZoEISWUqSU+KIeRTrcJ3DxJi9vgsuIfzbQ5F5P4Och89Iq8zq1RQPxVmpsnuy70ApWVgqDgQ2luy42fzURCz/pI6w9zbiNWnnzF8hyxcodkUHplR3SiygFjv44prHfcL9IO8QfoluUMLyN2qQvlOhJyPNlbURb9mEo7VI7TrfGnUDeAe5BfCbJiRAA7fochi49vvL7J4tk0YIaED5S1Q3XIMRaO7clmVeMkBV8eC2KIsLN9Jqpgek0w6KCCT0bH8Fdqp63kPkN/3AOpuMlMY6taESitKYDqRVaVfiBHVTSmA/ugbz8xBEkLSKT9iOL4vmXqD7Dz7k4JqSordSYmwzlf12fuQXCTkIefI8rd6OojXs7XZ9qqNHmaCFL9DSYZCIOeRzHZU+XuKadSEKI9Pew3KzwJCy7RMMTk1Kt7Vlz0Ym6KVuSu7vk8nrvEmVxWHsxFBRTg7bzwFUekticOKTwPJPC7bopiBpPYjoleftg==',
        'private-key.pem': '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',
      },
    },

    sa: k8s.ServiceAccount + {
      name+:: '-tokens-getter',
    },

    role: k8s.Role + {
      name+:: '-tokens-getter',
      rules: [
        {
          apiGroups: [''],
          resources: ['secrets'],
          resourceNames: [$.tokens.name],
          verbs: k8s.READ_VERBS + k8s.WRITE_VERBS_NO_COLLECTION,
        },
      ],
    },

    rb: k8s.RoleBinding + {
      name+:: '-tokens-getter',
      role_name: tg.role.metadata.name,
      sas: [tg.sa],
    },

    cron: k8s.CronJob + {
      name+:: '-tokens-getter',
      schedule: schedule,
      podspec:: {
        local pod = self,
        restartPolicy: k8s.RESTART_POLICY.NEVER,
        serviceAccountName: tg.sa.metadata.name,
        volmounts:: [{
          local vm = self,
          name: tg.gh_app_installation.name,
          volume:: {
            secret: {
              secretName: vm.name,
            },
          },
          mount:: {
            mountPath: '/run/augment/secrets/' + vm.name,
          },
        }],
        containers: [
          k8s.Container + {
            name: tg.cron.name,
            image: augi_image,
            volumeMounts: pod.volmount_mounts,
            args: [
              'user',
              'tokens',
              '--github-token=dir:///run/augment/secrets/' + tg.gh_app_installation.name,
              '--from-gh=main',
              '--to-k8s=' + $.tokens.name,
            ],
          },
        ],
      },
    },
  },
}

user nginx;
worker_processes auto;

error_log stderr debug;

events {
	worker_connections 1024;
}

http {
	keepalive_timeout 65;
	sendfile on;
	include /etc/nginx/mime.types;
	default_type text/plain;

	map $http_host $devpod_addr {
		~^(?<m_port>[0-9]+)-(?<m_devpod>[^.]+)\.exp\.(?<m_cluster>[^.]+)\.r\.augmentcode\.com$ $m_devpod-ingress.$m_cluster.svc.cluster.local:$m_port;
		~^(?<m_port>[0-9]+)-(?<m_devpod>[^.]+)\.exp\.r\.augmentcode\.com$                      $m_devpod-ingress.%(cluster_name)s.svc.cluster.local:$m_port;
		default 0;
	}

	server {
		listen 8080 default_server;
		listen [::]:8080 default_server;
		server_name _;

		recursive_error_pages on;

		location / {
			auth_request /token-auth;
			auth_request_set $auth_status $upstream_status;
			auth_request_set $auth_cookie $upstream_http_set_cookie;

			error_page 401 =403 /oauth2/sign_in;

			if ($devpod_addr = 0) {
				return 303 "https://www.notion.so/Research-DevPods-e14fee75d32d46c686b52733375e665a#4ed7e4a749a748829900307275ab3085";
			}
			resolver kube-dns.kube-system.svc.cluster.local;
			if ($devpod_addr != 0) {
				proxy_pass http://$devpod_addr;
			}

			# This shouldn't be needed, we should match either an expose redirect, or 303 to the docs.
			root /usr/share/nginx/html;
		}

		location = /token-auth {
			internal;
			proxy_pass http://127.0.0.1:8081;
			proxy_pass_request_headers on;
			proxy_pass_request_body off;
			proxy_set_header Content-Length "";
			proxy_set_header X-Original-URL $scheme://$host$request_uri;

			proxy_intercept_errors on;
			error_page 401 403 = /oauth2/auth;
		}

		location /oauth2/ {
			proxy_pass http://127.0.0.1:4180;
			proxy_set_header Host                    $host;
			proxy_set_header X-Real-IP               $remote_addr;
			proxy_set_header X-Auth-Request-Redirect $scheme://$host$request_uri;
		}
		location /oauth2/auth {
			proxy_pass http://127.0.0.1:4180;
			proxy_set_header Host                    $host;
			proxy_set_header X-Real-IP               $remote_addr;
			proxy_set_header X-Auth-Request-Redirect $scheme://$host$request_uri;
			proxy_set_header Content-Length "";
			proxy_pass_request_body off;
		}

		error_page 500 502 503 504 /50x.html;
		location = /50x.html {
			root /usr/share/nginx/html;
		}
	}
}

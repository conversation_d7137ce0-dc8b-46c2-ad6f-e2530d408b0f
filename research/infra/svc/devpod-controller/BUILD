load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library", "go_test")

package(default_visibility = ["//visibility:private"])

go_library(
    name = "devpod-controller_lib",
    srcs = ["devpod-controller.go"],
    importpath = "github.com/augmentcode/augment/research/infra/svc/devpod-controller",
    deps = [
        "//infra/lib/logger",
        "//research/infra/cfg/clusters",
        "//research/infra/lib/augment/devpod/ctrl",
        "@com_github_spf13_pflag//:pflag",
    ],
)

go_test(
    name = "devpod-controller_test",
    srcs = ["devpod-controller_test.go"],
    embed = [":devpod-controller_lib"],
)

go_binary(
    name = "devpod-controller",
    embed = [":devpod-controller_lib"],
)

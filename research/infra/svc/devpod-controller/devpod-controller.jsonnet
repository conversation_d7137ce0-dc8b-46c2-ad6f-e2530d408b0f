local clusters = import '../../cfg/clusters/clusters.jsonnet';

function(
  cluster='gcp-us1',
  name='devpod-controller',
  version='latest',
  image=null,
  replicas=1,
  cpu='1',
  ram='2G',
) local C = clusters.cluster(cluster); C.k8s + {

  BaseLabels+:: std.prune({
    'aug.service': name,
    'aug.version': version,
  }),

  BaseObject+:: {
    name:: name,
  },

  Object+:: {
    namespace:: C.sys_namespace,
  },

  sa: $.ServiceAccount + {},

  deployment: $.Deployment + {
    spec+: {
      replicas: replicas,
      template+: {
        spec+: {
          local pod = self,
          serviceAccountName: $.sa.metadata.name,
          containers: [
            $.Container + {
              name: name,
              image: image,
              volumeMounts: pod.volmount_mounts,
              resources+: {
                limits+: {
                  cpu: cpu,
                  memory: ram,
                },
              },
              args: [
                '--cluster=' + C.name,
                '--wet-run=true',
              ],
            },
          ],
          tolerations+: [
            {
              effect: 'NoSchedule',
              key: 'r.augmentcode.com/pool-type',
              value: 'svc',
            },
            {
              effect: 'PreferNoSchedule',
              key: 'r.augmentcode.com/pool-type',
              value: 'svc',
            },
          ],
        },
      },
    },
  },

  rolebinding: $.ClusterRoleBinding + {
    role_name:: 'cluster-admin',
    sas:: [$.sa],
  },
}

#!/bin/bash

set -e

main() {
	declare -r token_file="$1"
	declare -r cmd="${2}"
	declare -r input_spec="$(</dev/stdin)"

	if [[ "$cmd" != "get" ]]; then
		# Silently ignore
		return 0
	fi

	declare -A spec
	while IFS='=' read -r key val; do
		spec["$key"]="$val"
	done <<<"$input_spec"

	if [[ "${spec[protocol]}" != "https" || "${spec[host]}" != "github.com" ]]; then
		printf "Ignoring %s://%s, only https://github.com supported.\n" "${spec[protocol]}" "${spec[host]}" >&2
		return 0
	fi

	if [[ -r "$token_file" ]]; then
		declare -r token="$(<"$token_file")"
		if [[ "$token" ]]; then
			printf "%s\nusername=%s\npassword=%s\n" "$input_spec" "token" "$token"
		fi
	fi
}

main "$@"

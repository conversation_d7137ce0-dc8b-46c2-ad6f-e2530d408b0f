NAME     := augment-remote-agent
VERSION  := latest
CLUSTER  := gcp-agent0
REGISTRY := $(shell jsonnet -A name="$(CLUSTER)" -A prop=registry -Se "(import '../../../cfg/clusters/clusters.jsonnet').cluster_prop")
IMAGE    := $(REGISTRY)/$(NAME):$(VERSION)

repo_tgz:
	(cd $$(git rev-parse --show-toplevel) && git archive HEAD -o $(PWD)/augment.tar.gz)

build: # repo_tgz
	docker build --progress=plain --pull --build-context=knowledge=/mnt/efs/augment/user/guy/bob/knowledge_remote_agent -t $(IMAGE) .
	#$(RM) augment.tar.gz

push: build
	docker push $(IMAGE)

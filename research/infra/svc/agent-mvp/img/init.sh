#!/bin/bash

set -eu -o pipefail

main() {
	# NOTE(mattm): --repo, --ref, and --instructions are all deprecated and ignored.
	eval set -- $(getopt -n "$0" -o 'p:' --long 'id:,repo:,ref:,instructions:,persist:,port:' -- "$@")

	declare id="" persist=""
	declare -i port=0
	while true; do
		case "$1" in
		--) shift; break ;;
		--id) id="$2"; shift 2; continue ;;
		--persist) persist="$2"; shift 2; continue ;;
		-p|--port) port="$2"; shift 2; continue ;;
		--repo|--ref|--instructions) shift 2; continue ;; # deprecated, ignored
		*)
			printf "Invalid Option: %s\n" "$1" >&2
			return 1
		;;
		esac
	done
	declare -r id persist port
	declare -r ws="${persist:-$HOME}/workspace"

	# AgentConfig proto (json.gz)
	declare -r cfgz="/run/secrets/workspace-config/agent-config.json.gz"
	declare repo="" ref="" token="" patch="" setup_script=""
	declare -a instructions=()
	if [[ -e "$cfgz" ]]; then
		repo="$(gunzip -c "$cfgz" | jq '.workspace_setup.github_ref.url // ""' -r)"
		ref="$(gunzip -c "$cfgz" | jq '.workspace_setup.github_ref.ref // ""' -r)"
		token="$(gunzip -c "$cfgz" | jq '.workspace_setup.token // ""' -r)"
		patch="$(gunzip -c "$cfgz" | jq '.workspace_setup.patch // ""' -r)"
		setup_script="$(gunzip -c "$cfgz" | jq '.workspace_setup.setup_script // ""' -r)"

		mapfile -d $'\0' -t instructions < <(gunzip -c "$cfgz" | jq '.starting_nodes[].text_node.content+"\u0000"' -rj)
	fi
	# default to bash if no existing shebang
	[[ "$setup_script" ]] && [[ "${setup_script::2}" != '#!' ]] && printf -v setup_script "#!/bin/bash\n%s" "$setup_script"
	declare -r repo ref instructions token patch setup_script

	# Set a credential helper that can read a token from the mounted secret.
	git config set --global 'credential.https://github.com.helper' '/usr/local/bin/git-credential-helper /run/secrets/workspace-config/github-app-installation-token'

	if [[ ! -d "$ws" ]]; then
		git clone "$repo" "$ws"
		(
			set -eu
			cd "$ws"
			git checkout "$ref"
			[[ "$patch" ]] && git apply - <<<"$patch" || true
		)
	fi

	if [[ "$setup_script" ]]; then
		install -m755 /dev/stdin /tmp/setup_script <<<"$setup_script"
		( cd "$ws" && /tmp/setup_script || true )
	fi

	cd /home/<USER>/augment-agent
	declare -a cmd=(
		uv run experimental/guy/agent_qa/interactive_agent.py
		--approve-command-execution
		--no-complete-tool
		--enable-remote-tools
		--use-agents-host
		--remove-tool=clarify
		--knowledge-path=/usr/local/share/augment/knowledge
		--workspace-root="$ws"
	)
	if (( port > 0 )); then
		cmd+=(--port="$port")
	fi
	if [[ "$persist" ]]; then
		declare -r pickle="$persist/agent-state.pickle"
		cmd+=(--state-file="$pickle")

		# Only pass instructions when there isn't already a resume file.
		if [[ ! -e "$pickle" ]]; then
			for i in "${instructions[@]}"; do
				cmd+=(--instruction="$i")
			done
		fi
	else
		for i in "${instructions[@]}"; do
			cmd+=(--instruction="$i")
		done
	fi

	printf "Running: %s\n" "${cmd[*]}" >&2
	AUGMENT_EFS_ROOT="$HOME" \
	AUGMENT_API_TOKEN="$token" \
	"${cmd[@]}"
}

main "$@"

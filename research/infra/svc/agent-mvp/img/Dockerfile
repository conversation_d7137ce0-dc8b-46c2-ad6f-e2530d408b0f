FROM ubuntu:22.04 AS layer0

USER root
WORKDIR /root

### APT: minimal packages to support adding repos.
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && export DEBIAN_FRONTEND=noninteractive \
 && apt-get update -y \
 && apt-get install -y \
        apt-transport-https \
        ca-certificates \
        curl \
        gnupg \
        software-properties-common \
 && apt-get clean

### APT: git
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && add-apt-repository ppa:git-core/ppa -y

### APT: base packages
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && export DEBIAN_FRONTEND=noninteractive \
 && apt-get update -y \
 && apt-get install -y \
        ack \
        build-essential \
        bzip2 \
        chrpath \
        cmake \
        curl \
        dumb-init \
        g++ \
        gcc \
        git \
        gosu \
        gnupg \
        htop \
        iftop \
        iputils-ping \
        iotop \
        jq \
        less \
        make \
        munge \
        nano \
        net-tools \
        openssh-client \
        patchelf \
        pciutils \
        pdsh \
        perftest \
        perl \
        postgresql-client \
        rsync \
        ssh \
        sudo \
        tmux \
        tree \
        unzip \
        vim \
        wget \
        zstd \
  && apt-get clean

### uv
RUN --mount=type=cache,target=/root/dl : \
  && UV_VERSION=0.6.1 \
  && UV_SHA256SUM=0dcad9831d3f10f3bc4dcd7678948dfc74c0b3ab3f07aa684eb9e5135b971a58 \
  && UV_URL="https://github.com/astral-sh/uv/releases/download/$UV_VERSION/uv-x86_64-unknown-linux-gnu.tar.gz" \
  && UV_TGZ="uv-x86_64-unknown-linux-gnu-$UV_VERSION.tar.gz" \
  && UV_DL="/root/dl/$UV_TGZ" \
  && test -e "$UV_DL" || curl -fsSL "$UV_URL" -o "$UV_DL" \
  && printf "%s %s\n" "$UV_SHA256SUM" "$UV_DL" | sha256sum -c \
  && tar -xz --no-same-owner -C /usr/local/bin --strip-components=1 -f "$UV_DL" \
  && /usr/local/bin/uv generate-shell-completion bash | install -oroot -groot -m0644 /dev/stdin -DT /usr/local/share/bash-completion/completions/uv \
  && /usr/local/bin/uv generate-shell-completion fish | install -oroot -groot -m0644 /dev/stdin -DT /usr/local/share/fish/vendor_completions.d/uv.fish \
  && /usr/local/bin/uv generate-shell-completion zsh  | install -oroot -groot -m0644 /dev/stdin -DT /usr/local/share/zsh/site-functions/_uv

### Bazel
RUN --mount=type=cache,target=/root/dl : \
 && BAZELISK_VERSION=v1.25.0 \
 && BAZELISK_256SUM=fd8fdff418a1758887520fa42da7e6ae39aefc788cf5e7f7bb8db6934d279fc4 \
 && BAZELISK_DL=/root/dl/bazelisk-"$BAZELISK_VERSION" \
 && test -e "$BAZELISK_DL" || curl -fsSL https://github.com/bazelbuild/bazelisk/releases/download/"$BAZELISK_VERSION"/bazelisk-linux-amd64 -o "$BAZELISK_DL" \
 && printf "%s %s\n" "$BAZELISK_256SUM" "$BAZELISK_DL" | sha256sum -c \
 && install -oroot -groot -m0755 "$BAZELISK_DL" /usr/local/bin/bazel

### Go
RUN --mount=type=cache,target=/root/dl : \
 && _GO_VER=1.23.5 \
 && _GO_256SUM=cbcad4a6482107c7c7926df1608106c189417163428200ce357695cc7e01d091 \
 && _GO_TGZ="go$_GO_VER.linux-amd64.tar.gz" \
 && _GO_DL="/root/dl/$_GO_TGZ" \
 && test -e "$_GO_DL" || curl -fsSL https://golang.org/dl/"$_GO_TGZ" -o "$_GO_DL" \
 && printf "%s %s\n" "$_GO_256SUM" "$_GO_DL" | sha256sum -c \
 && rm -fr /usr/local/go \
 && tar -C /usr/local -xzf "$_GO_DL" \
 && printf 'export PATH="/usr/local/go/bin:$PATH"\n' > /etc/profile.d/90-go.sh

### User account (augment-agent)
RUN useradd --create-home --uid 1000 --shell /bin/bash augment-agent \
 && printf '%s ALL=(ALL:ALL) NOPASSWD: ALL\n' augment-agent | install -o root -g root -m400 /dev/stdin /etc/sudoers.d/zzz-augment

USER augment-agent
WORKDIR /home/<USER>

### interactive_agent.py environment (hopefully replaced by a small go proxy).
FROM layer0 AS repo
COPY augment.tar.gz .
RUN mkdir -p augment-agent \
 && tar -C augment-agent -xzf augment.tar.gz
FROM layer0
COPY --from=repo --chown=1000:1000 /home/<USER>/augment-agent /home/<USER>/augment-agent
COPY --from=knowledge --chown=0:0 --chmod=0644 / /usr/local/share/augment/knowledge/

RUN --mount=type=cache,uid=1000,gid=1000,target=/home/<USER>/.cache/ \
    --mount=type=cache,uid=1000,gid=1000,target=/home/<USER>/.cargo/ \
    --mount=type=cache,uid=1000,gid=1000,target=/home/<USER>/go/ \
    : \
 && cd augment-agent \
 && bazel run //tools/generate_proto_typestubs \
 && bazel run //base:install

RUN --mount=type=cache,uid=1000,gid=1000,target=/home/<USER>/.cache/ \
    --mount=type=cache,uid=1000,gid=1000,target=/home/<USER>/.cargo/ \
    --mount=type=cache,uid=1000,gid=1000,target=/home/<USER>/go/ \
    : \
 && cd augment-agent \
 && sed -i research/requirements.txt \
    -e '/^flash-attn/d' \
    -e '/^flashattn-hopper/d' \
    -e '/^megablocks/d' \
    -e '/^mip4py/d' \
    -e '/^transformer-engine/d' \
    -e '/^codellama/d' \
    -e '/^deepspeed/d' \
    -e '/^tensordict/d' \
 && export PATH=/usr/local/go/bin:$PATH \
 && uv sync

### Entrypoint
COPY --chmod=755 --chown=root:root init.sh /usr/local/sbin/
COPY --chmod=755 --chown=root:root git-credential-helper /usr/local/bin/
ENTRYPOINT ["/usr/bin/dumb-init", "--verbose", "--", "/usr/local/sbin/init.sh"]

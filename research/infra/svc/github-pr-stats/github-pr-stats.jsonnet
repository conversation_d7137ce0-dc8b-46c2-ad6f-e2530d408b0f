local clusters = import '../../cfg/clusters/clusters.jsonnet';

local hd_py = importstr 'github-pr-stats.py';
local requirements = importstr 'requirements.txt';

local invocation_args = '--since db --update-db --write-html /mnt/efs/augment/public_html/github_pr_stats';

function(cluster_name, version, job_inst='') local C = clusters.cluster(cluster_name); C.k8s + {
  by_cluster(obj)::
    if std.objectHas(obj, cluster_name)
    then obj[cluster_name]
    else std.get(obj, 'default'),

  filter_by_cluster(obj):: {
    [kv.key]: kv.value
    for kv in std.objectKeysValues(obj)
    if !std.isObject(kv.value) || std.member([null, cluster_name], std.get(kv.value, 'cluster_name'))
  },

  local as_job = job_inst != '',
  name:: 'github-pr-stats',

  gh_token: C.k8s.SealedSecret + {
    name: 'gh-runner-monitor-token',
    namespace: 'devex-metrics',
    encryptedData: {
      token: 'AgCdCoVgV20TtGPQX74lJc/99ERT2FrXGZ9SG2dmwyKLP9gfG+++NPQUFMw8J3PVxKbbPsGUMDgdn0FMV09Vytthy2G9ZHdef2R7dDWclZz3JbtdCIneky9WQuQLJCj9daRAnTTnjUCBoIo31K7wjbxJmz8Gq64aXDCRNzt4/L0/Wdcny0eIJLTlHIvuc/iJJ1YMk9I8Eky9HxsXQnJFzhU4KOsMr+BKwSMOg4DcnPViS9dCl5he4Mxqo54DD2UBYcHHnf9V3xBw/rwPGzPaGtBuaB+VthA0CscVnMU8cLzRnYlmfcUDOJaBIgmvCiLivHrAE2ge8e9SQVVZ7W4af3aDVkUci+QBIB2ZxhiKtSOwb7Etx37HVPfjOBIEXpGEkA7+29QxppLIJOuVtjs4/3hpMCjGeioDEyx/8ItN3l7RcFEbLFywvsz4QqKXeiVxj7v1IK6nIeW9UMROMxbKGYsqPtNyNpmhVjkxv6dISprdLjBJOs6JiAkfCVLtX8leRVHGD03y5S3T30cmqtEptKuQ5dnTaUh1ETT7NxGbeyVUkyjLbdWvucu8C1nU/Ao8SAI84JFoVatGlaMKBSTlzCaWKSb4ul7tbTYgOC6AcHhxddjptPjlfgK7U8DkwOAVhdm3MFguxoVXWhqgfr6/WYTRgP1UjG7skfb58EtwUnH9RpOCy/T7cCRvc9qRMNTSFjBEGmkw9rDhmB4IMYLZVlUvfcSbtgdARCchBoB3DsDQOqh3P0GtDC0z',
    },
  },

  configmap: C.k8s.ConfigMap + {
    name:: $.name,
    namespace:: 'devex-metrics',
    data: {
      'github-pr-stats.py': hd_py,
      'requirements.txt': requirements,
    },
  },
  jobspec:: {
    completions: 1,
    template+: {
      metadata+: {
        annotations+: {
          'gke-gcsfuse/volumes': 'true',
          'config.md5': std.md5(std.manifestJson($.configmap.data)),
        },
      },
      spec+: {
        local pod = self,
        restartPolicy: 'Never',
        containers+: [
          {
            name: $.name,
            image: 'python:3.11-slim',
            imagePullPolicy: 'IfNotPresent',
            env: [
              {
                name: 'GH_AUTH_TOKEN',
                valueFrom: {
                  secretKeyRef: {
                    name: 'gh-runner-monitor-token',
                    key: 'token',
                  },
                },
              },
              {
                name: 'METRICS_DB_PASSWORD',
                valueFrom: {
                  secretKeyRef: {
                    name: 'devex-metrics-auth',
                    key: 'user_pw',
                  },
                },
              },
            ],
            args: [
              '/bin/bash',
              '-c',
              'pip install -r /run/augment/' + $.name + '/requirements.txt && python3 /run/augment/' + $.name + '/' + $.name + '.py ' + invocation_args,
            ],
            volumeMounts: [
              {
                name: $.name,
                mountPath: '/run/augment/' + $.name,
              },
              {
                name: 'gcs-gcp-us1-public-html',
                mountPath: '/mnt/efs/augment/public_html/github_pr_stats',
                subPath: 'github_pr_stats',
              },
            ],
          },
        ],
        volumes: [
          {
            name: $.name,
            configMap: {
              name: $.name,
            },
          },
          {
            name: 'gcs-gcp-us1-public-html',
            csi: {
              driver: 'gcsfuse.csi.storage.gke.io',
              volumeAttributes: {
                bucketName: 'gcp-us1-public-html',
                mountOptions: std.join(',', [
                  'implicit-dirs',
                  'uid=101',
                  'gid=101',
                  'file-mode=0444',
                  'dir-mode=0555',
                  'o=noexec',
                  'o=noatime',
                ]),
              },
            },
          },
        ],
      },
    },
  },
  cronjob:: C.k8s.CronJob + {
    local cj = self,
    name:: $.name,
    schedule:: '*/10 * * * *',
    namespace:: 'devex-metrics',
    concurrencyPolicy:: $.CRONJOB_POLICY.REPLACE,
    metadata+: {
      labels+: {
        'app.kubernetes.io/name': $.name,
        'aug.version': version,
      },
    },
    spec+: {
      jobTemplate+: {
        metadata+: {
          labels+: {
            'app.kubernetes.io/name': $.name,
            'aug.version': version,
          },
        },
        spec+: $.jobspec,
      },
    },
    podspec:: {},
  },
  job:: C.k8s.Job + {
    name: $.name + '-adhoc-' + job_inst,
    spec: $.jobspec,
    metadata+: {
      labels+: {
        'app.kubernetes.io/name': $.name,
        'aug.version': version,
      },
    },
  },

  cronjob_or_job: if as_job then $.job else $.cronjob,
}

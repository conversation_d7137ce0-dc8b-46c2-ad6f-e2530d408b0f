"""A script to monitor GitHub and dump metrics in a DB for grafana.

Monitors
- idle runners
- queued workflows
- check run metrics
"""

import os
import time
from datetime import datetime, timedelta, timezone

import psycopg2
from psycopg2.extras import execute_batch
import requests

GITHUB_TOKEN = os.environ.get("GH_AUTH_TOKEN")
assert GITHUB_TOKEN is not None, "Set the GH_AUTH_TOKEN environment variable"
METRICS_DB_PASSWORD = os.environ.get("METRICS_DB_PASSWORD")
assert (
    METRICS_DB_PASSWORD is not None
), "Set the METRICS_DB_PASSWORD environment variable"

_conn = None
metrics_start = datetime.now(timezone.utc) - timedelta(days=365 * 2)


def graphql_query(query: str) -> tuple[dict, dict]:
    """Execute a GraphQL query."""
    request_url = "https://api.github.com/graphql"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {GITHUB_TOKEN}",
    }

    response = requests.request(
        "POST", request_url, json={"query": query}, headers=headers
    )

    return (response.json(), dict(response.headers))


def get_connection() -> psycopg2.extensions.connection:
    global _conn
    if _conn is None:
        _conn = psycopg2.connect(
            dbname="metrics",
            user="augment",
            password=os.environ.get("METRICS_DB_PASSWORD"),
            host="devex-metrics-postgresql-hl",
        )
    return _conn


r"""
create table check_runs (
pr_num integer,
database_id bigint primary key,
name varchar,
started_at timestamp,
completed_at timestamp,
updated_at timestamp,
conclusion varchar,
hash varchar(40)
);

Grafana dashboard build from:
SELECT
  week
  , name
  , duration75_sec
FROM
  (
    SELECT
      name,
      percentile_cont(0.75)
        WITHIN GROUP (ORDER BY EXTRACT(EPOCH FROM (completed_at - started_at)))
        AS duration75_sec,
      date_trunc('week', started_at) AS week
    FROM
      check_runs
    WHERE
      started_at > now() - interval '6 month'
    GROUP BY
      name,
      week
  ) AS subquery
GROUP BY
  name,
  week,
  duration75_sec
ORDER BY
  week, name;
"""


def update_check_runs(updates: dict):
    """Update the check run table."""

    query = """
    insert into check_runs (
        pr_num,
        database_id,
        name,
        updated_at,
        started_at,
        completed_at,
        conclusion,
        hash
        )
    values (%s, %s, %s, %s, %s, %s, %s, %s)
    on conflict (database_id) do update
    set started_at = excluded.started_at,
        completed_at = excluded.completed_at,
        conclusion = excluded.conclusion,
        hash = excluded.hash,
        updated_at = excluded.updated_at,
        pr_num = excluded.pr_num,
        name = excluded.name
    """
    conn = get_connection()
    params = []
    for pr_num, pr_updates in updates.items():
        for updated_at, check_run_data in pr_updates:
            params.append(
                (
                    pr_num,
                    check_run_data["databaseId"],
                    check_run_data["name"],
                    updated_at,
                    check_run_data["startedAt"],
                    check_run_data["completedAt"],
                    check_run_data["conclusion"],
                    check_run_data["checkSuite"]["commit"]["oid"],
                )
            )
    print(f"Updating {len(params)} check runs")
    # print(params)
    with conn, conn.cursor() as cur:
        execute_batch(
            cur,
            query,
            params,
        )


def convert_sql_time_graphql_time(sql_time: datetime) -> str:
    """Convert a SQL timestamp to a GraphQL timestamp."""
    return sql_time.strftime("%Y-%m-%dT%H:%M:%SZ")


def get_last_update_time() -> str:
    """Get the last update time from the DB."""
    query = """
    select max(updated_at) from check_runs
    """
    conn = get_connection()
    with conn, conn.cursor() as cur:
        cur.execute(query)
        last = cur.fetchone()
        if last is None or last[0] is None:
            return convert_sql_time_graphql_time(metrics_start)
        return convert_sql_time_graphql_time(last[0])


def process_pr_metrics(pr_data: list):
    """Process the PR data."""
    print(f"Processing {len(pr_data)} PRs")
    updates = {}
    for node in [node["node"] for node in pr_data]:
        updated_at = datetime.strptime(
            node["updatedAt"].replace("Z", "+00:00"), "%Y-%m-%dT%H:%M:%S%z"
        )
        print(
            f"Updating {node['number']} from {updated_at} ({len(node['commits']['nodes'])} commits)"
        )
        updates[node["number"]] = []
        for commit in [c["commit"] for c in node["commits"]["nodes"]]:
            for check_run in commit["checkSuites"]["nodes"]:
                for check_run_data in check_run["checkRuns"]["nodes"]:
                    updates[node["number"]].append((updated_at, check_run_data))
    update_check_runs(updates)


def get_github_metrics() -> None:
    """Get the check runs from GitHub.

    GraphQL searching and sorting is an absolute war-crime.

    Query the DB for the last update time we have.
    Paging through PRs:
        - first query, grab the latest N to get the cursor.
        - subsequent queries, grab batches of N using the cursor.
        - stop when we reach the update time we got in the initial query.

    Within each updated PR, we page through commits in a similar fashion,
    and within commits, we page through check runs.

    For each commit, we replace any previous check run info with the new data, for simplicity

    """
    batchsize = 20
    afterclause = ""
    commit_selector = "last: 20"
    cs_selector = "last: 20"
    cr_selector = "last: 20"
    dateclause = ""
    pr_query = """
    {
    search(
        first: %(batchsize)d, %(afterclause)s
        query: "repo:augmentcode/augment is:pr %(dateclause)s sort:updated-asc",
        type: ISSUE) {
        issueCount
        edges {
            cursor
            node {
            ... on PullRequest {
                number
                updatedAt
                state
                commits(%(commit_selector)s) {
                    totalCount
                    pageInfo {
                        endCursor
                        hasNextPage
                    }
                    nodes {
                        commit {
                            oid
                            status {
                                state
                                contexts {
                                    state,
                                    context,
                                    description,
                                    createdAt,
                                    targetUrl
                                }
                            }
                            checkSuites(%(cs_selector)s) {
                                pageInfo {
                                    endCursor
                                    hasNextPage
                                }
                                nodes {
                                    checkRuns(%(cr_selector)s) {
                                        pageInfo {
                                            endCursor
                                            hasNextPage
                                        }
                                        nodes {
                                            databaseId
                                            name
                                            startedAt
                                            completedAt
                                            conclusion

                                            checkSuite {
                                                commit {
                                                    oid
                                                }
                                            }
                                        }
                                    }
                                    status
                                    conclusion
                                }
                            }
                        }
                    }
                }
            }
            }
        }
        pageInfo {
            endCursor
            hasNextPage
        }
        }
    }
    """

    last_update = get_last_update_time()
    dateclause = f"updated:>{last_update}"
    print(f"Last update: {last_update}")
    while True:
        response, headers = graphql_query(
            pr_query
            % {
                "commit_selector": commit_selector,
                "cs_selector": cs_selector,
                "cr_selector": cr_selector,
                "batchsize": batchsize,
                "afterclause": afterclause,
                "dateclause": dateclause,
            }
        )
        try:
            if len(errors := response.get("errors", [])):
                if [error for error in errors if error.get("type") == "RATE_LIMITED"]:
                    now = int(datetime.now(timezone.utc).strftime("%s"))
                    reset = int(headers["X-RateLimit-Reset"])
                    delta = reset - now
                    print(f"Rate limited, sleeping {delta} until {reset}")
                    time.sleep(delta)
                    continue

            pr_data = response["data"]["search"]["edges"]
            process_pr_metrics(pr_data)
            cursor_data = response["data"]["search"]["pageInfo"]
            if not cursor_data.get("hasNextPage"):
                break
            afterclause = f"after: \"{cursor_data['endCursor']}\", "
            time.sleep(5)
        except:
            # print(json.dumps(response, indent=2))
            raise


def get_github_runners() -> list:
    """Get the runners from GitHub."""
    request_url = "https://api.github.com/repos/augmentcode/augment/actions/runners"
    headers = {
        "Accept": "application/vnd.github.v3+json",
        "Authorization": f"token {GITHUB_TOKEN}",
    }
    response = requests.get(request_url, headers=headers)
    response.raise_for_status()
    return response.json()["runners"]


def get_idle_status(runners: list) -> dict:
    """Return the idle count of the online runners in a dict, keyed on the joined labels."""
    ignore_labels = ["Linux", "X64", "self-hosted", "DL"]
    idle_status = {}
    for runner in runners:
        if runner["status"] == "online":
            label = ",".join(
                [
                    label["name"]
                    for label in sorted(runner["labels"], key=lambda x: x["name"])
                    if label["name"] not in ignore_labels
                ]
            )
            idle_status.setdefault(label, []).append(runner["busy"])
    return idle_status


def get_github_workflows() -> list:
    """Get the workflow list from github."""
    request_url = "https://api.github.com/repos/augmentcode/augment/actions/workflows"
    headers = {
        "Accept": "application/vnd.github.v3+json",
        "Authorization": f"token {GITHUB_TOKEN}",
    }
    response = requests.get(request_url, headers=headers)
    response.raise_for_status()
    return response.json()["workflows"]


def get_queued_workflows() -> dict:
    """Get the queued workflows."""
    queued = {}
    workflows = get_github_workflows()
    for w in workflows:
        w_id = w["id"]
        request_url = f"https://api.github.com/repos/augmentcode/augment/actions/workflows/{w_id}/runs?status=queued"
        headers = {
            "Accept": "application/vnd.github.v3+json",
            "Authorization": f"token {GITHUB_TOKEN}",
        }
        response = requests.get(request_url, headers=headers)
        response.raise_for_status()
        queued[w["name"]] = response.json()["total_count"]
    return queued


def update_postgres_idle(idle_status: dict):
    """Update a postgres table with the idle status."""
    query = """
    insert into idle_github_runners (labels, idle_count, total_count)
    values (%s, %s, %s)
    """
    conn = get_connection()
    with conn, conn.cursor() as cur:
        for labels, idle_state in idle_status.items():
            idle = len([i for i in idle_state if not i])
            print(f"Updating {labels}, {idle}, {len(idle_state)}")
            cur.execute(query, (labels, idle, len(idle_state)))


def update_postgres_queued(queued_workflows: dict):
    """Update a postgres table with the queued workflows."""
    query = """
    insert into queued_github_workflows (name, queued_count)
    values (%s, %s)
    """
    conn = get_connection()
    with conn, conn.cursor() as cur:
        for name, queued in queued_workflows.items():
            print(f"Updating {name}, {queued}")
            cur.execute(query, (name, queued))


def main():
    """Main function."""
    get_github_metrics()
    update_postgres_idle(get_idle_status(get_github_runners()))
    update_postgres_queued(get_queued_workflows())


if __name__ == "__main__":
    main()

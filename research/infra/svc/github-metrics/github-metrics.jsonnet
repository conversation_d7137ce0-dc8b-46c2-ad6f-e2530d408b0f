local clusters = import '../../cfg/clusters/clusters.jsonnet';

local hd_py = importstr 'github-metrics.py';
local requirements = importstr 'requirements.txt';

function(cluster_name, version, job_inst='') local C = clusters.cluster(cluster_name); C.k8s + {
  by_cluster(obj)::
    if std.objectHas(obj, cluster_name)
    then obj[cluster_name]
    else std.get(obj, 'default'),

  filter_by_cluster(obj):: {
    [kv.key]: kv.value
    for kv in std.objectKeysValues(obj)
    if !std.isObject(kv.value) || std.member([null, cluster_name], std.get(kv.value, 'cluster_name'))
  },

  local as_job = job_inst != '',
  name:: 'github-metrics',

  configmap: C.k8s.ConfigMap + {
    name:: $.name,
    namespace:: 'devex-metrics',
    data: {
      'github-metrics.py': hd_py,
      'requirements.txt': requirements,
    },
  },
  jobspec:: {
    completions: 1,
    template+: {
      metadata+: {
        annotations+: {
          'config.md5': std.md5(std.manifestJson($.configmap.data)),
        },
      },
      spec+: {
        local pod = self,
        restartPolicy: 'Never',
        containers+: [
          {
            name: $.name,
            image: 'python:3.11-slim',
            imagePullPolicy: 'IfNotPresent',
            env: [
              {
                name: 'GH_AUTH_TOKEN',
                valueFrom: {
                  secretKeyRef: {
                    name: 'gh-runner-monitor-token',  // This is defined in github-pr-stats.jsonnet
                    key: 'token',
                  },
                },
              },
              {
                name: 'METRICS_DB_PASSWORD',
                valueFrom: {
                  secretKeyRef: {
                    name: 'devex-metrics-auth',
                    key: 'user_pw',
                  },
                },
              },
            ],
            args: [
              '/bin/bash',
              '-c',
              'pip install -r /run/augment/' + $.name + '/requirements.txt && python3 /run/augment/' + $.name + '/' + $.name + '.py',
            ],
            volumeMounts: [
              {
                name: $.name,
                mountPath: '/run/augment/' + $.name,
              },
            ],
          },
        ],
        volumes: [
          {
            name: $.name,
            configMap: {
              name: $.name,
            },
          },
        ],
      },
    },
  },
  cronjob:: C.k8s.CronJob + {
    local cj = self,
    name:: $.name,
    schedule:: '*/5 * * * *',
    namespace:: 'devex-metrics',
    concurrencyPolicy:: $.CRONJOB_POLICY.REPLACE,
    metadata+: {
      labels+: {
        'app.kubernetes.io/name': $.name,
        'aug.version': version,
      },
    },
    spec+: {
      jobTemplate+: {
        metadata+: {
          labels+: {
            'app.kubernetes.io/name': $.name,
            'aug.version': version,
          },
        },
        spec+: $.jobspec,
      },
    },
    podspec:: {},
  },
  job:: C.k8s.Job + {
    name: $.name + '-adhoc-' + job_inst,
    spec: $.jobspec,
    metadata+: {
      labels+: {
        'app.kubernetes.io/name': $.name,
        'aug.version': version,
      },
    },
  },

  cronjob_or_job: if as_job then $.job else $.cronjob,
}

load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library", "go_test")

package(default_visibility = ["//visibility:private"])

go_library(
    name = "augi-admission_lib",
    srcs = ["augi-admission.go"],
    importpath = "github.com/augmentcode/augment/research/infra/svc/augi-admission",
    deps = [
        "//infra/lib/k8s",
        "//infra/lib/logger",
        "@com_github_spf13_pflag//:pflag",
    ],
)

go_binary(
    name = "augi-admission",
    embed = [":augi-admission_lib"],
)

go_test(
    name = "augi-admission_test",
    srcs = ["augi-admission_test.go"],
    embed = [":augi-admission_lib"],
)

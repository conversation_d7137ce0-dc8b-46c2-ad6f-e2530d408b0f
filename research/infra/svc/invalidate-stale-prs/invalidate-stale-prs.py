#!/usr/bin/env python3

from datetime import datetime, timezone
from gh_app_auth import get_gh_app_token  # type: ignore
from pathlib import Path
import argparse
import json
import os
import requests
import sys

url = "https://api.github.com/graphql"


query_template = """
{
    search(
        first: %(batchsize)d, %(afterclause)s
        query: "repo:augmentcode/augment is:pr is:open sort:updated-asc",
    type: ISSUE) {
        issueCount
        edges {
            cursor
            node {
                ... on PullRequest {
                    number
                    createdAt
                    updatedAt
                    state

                    commits(last:1) {
                        totalCount
                        pageInfo {
                            endCursor
                            hasNextPage
                        }
                        nodes {
                            commit {
                                oid
                                status {
                                    context(name: "branch-age") {
                                        state,
                                        context,
                                    }
                                }
                                checkSuites(last:10) {
                                    pageInfo {
                                        endCursor
                                        hasNextPage
                                    }
                                    nodes {
                                        checkRuns(last:10) {
                                            pageInfo {
                                                endCursor
                                                hasNextPage
                                            }
                                            nodes {
                                                name
                                                databaseId
                                                startedAt
                                                completedAt
                                                conclusion
                                            }
                                        }
                                        status
                                        conclusion
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        pageInfo {
            endCursor
            hasNextPage
        }
    }
}
"""


def get_next_batch(batch_size=10, last_cursor=None, app_token=None):
    afterclause = ""

    if last_cursor is not None:
        afterclause = f'after: "{last_cursor}",'

    query = query_template % {
        "batchsize": batch_size,
        "afterclause": afterclause,
    }
    if app_token is None:
        app_token = os.environ.get("GH_AUTH_TOKEN")
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {app_token}",
    }

    response = requests.request(
        "POST",
        url,
        data=json.dumps({"query": query}),
        headers=headers,
    )
    response.raise_for_status()
    # print(json.dumps(response.json(), indent=2))

    data = response.json()["data"]["search"]
    page_info = data["pageInfo"]
    total_count = data["issueCount"]
    prs = data["edges"]

    last_cursor = prs[-1]["cursor"]
    has_next_page = page_info["hasNextPage"]

    return prs, has_next_page, last_cursor, total_count


def set_failure_check(head_sha, app_token):
    return set_check_status(
        head_sha,
        app_token,
        "failure",
        "This PR has diverged from main and must be rebased",
    )


def set_success_check(head_sha, app_token):
    return set_check_status(
        head_sha, app_token, "success", "This PR is close enough to main"
    )


def set_check_status(head_sha, app_token, status, description):
    url = f"https://api.github.com/repos/augmentcode/augment/statuses/{head_sha}"
    app_headers = {
        "Content-Type": "application/json",
        "Accept": "application/vnd.github+json",
        "Authorization": f"Bearer {app_token}",
    }

    check_data = {
        "state": status,
        "description": description,
        "context": "branch-age",
    }
    # We can't patch the existing check, because github,
    # so we have to create a new one.
    response = requests.request(
        "POST",
        url,
        data=json.dumps(check_data),
        headers=app_headers,
    )
    response.raise_for_status()
    return response.ok


def invalidate_stale_checks(open_prs, max_age_days, dry_run):
    pem_path = get_pem_path()
    app_token = get_gh_app_token(pem_path)

    for pr in open_prs:
        pr_num = pr["node"]["number"]
        # print(f"Checking {pr_num}")
        last_commit = pr["node"]["commits"]["nodes"][0]["commit"]["oid"]
        check_suites = pr["node"]["commits"]["nodes"][0]["commit"]["checkSuites"][
            "nodes"
        ]
        status = pr["node"]["commits"]["nodes"][0]["commit"]["status"]
        ci_checks = [
            check
            for suite in check_suites
            for check in suite["checkRuns"]["nodes"]
            if check["name"] == "Research CI"
        ]
        # we only need to invalidate if all the checks are successful
        successful_checks = [c for c in ci_checks if c["conclusion"] == "SUCCESS"]
        if len(successful_checks) > 0 and len(successful_checks) == len(ci_checks):
            ci_check = successful_checks[0]
        else:
            continue
        # Don't re-set if we already have it
        # {'context': {'state': 'FAILURE', 'context': 'branch-age'}}
        if status:
            try:
                state = status.get("context", {}).get("state", "")
                if state != "SUCCESS":
                    continue
            except AttributeError:
                pass

        start_time = datetime.fromisoformat(ci_check["startedAt"])
        now = datetime.now(timezone.utc)
        if (now - start_time).total_seconds() > (60 * 60 * 24 * max_age_days):
            dr = "(dry-run) " if dry_run else ""
            print(
                f"{dr}Invalidating {ci_check['name']} on PR #{pr_num} at {last_commit} ({start_time})"
            )
            if not dry_run:
                try:
                    set_failure_check(last_commit, app_token)
                except Exception as exc:  # pylint: disable=broad-except
                    print(f"Failed to invalidate check: {exc}")


def get_pem_path():
    return Path(os.environ.get("GH_APP_PEM_PATH", "/mnt/augment/secrets/gh-app.pem"))


def get_open_prs():
    batch_size = 50
    has_next_page = True
    fetch_count = 0
    max_fetches = 500
    last_cursor = None
    open_prs = []
    pem_path = get_pem_path()
    app_token = get_gh_app_token(pem_path)

    while has_next_page and fetch_count < max_fetches:
        prs, has_next_page, last_cursor, _ = get_next_batch(
            batch_size, last_cursor, app_token
        )
        open_prs += prs

    return open_prs


if __name__ == "__main__":
    p = argparse.ArgumentParser()
    subparser = p.add_subparsers(title="subcommands", dest="subcommand")
    p.add_argument("--dry-run", action="store_true")

    invalidate_parser = subparser.add_parser("invalidate-stale-checks")
    invalidate_parser.add_argument("--max-age-days", type=int, default=3)

    status_parser = subparser.add_parser("set-status")
    status_parser.add_argument("--head-sha", required=True)
    status_parser.add_argument(
        "--status", required=True, choices=["success", "failure"]
    )

    args = p.parse_args()

    if args.subcommand == "set-status":
        pem_path = get_pem_path()
        app_token = get_gh_app_token(pem_path)
        if args.dry_run:
            rsp = "(dry-run) "
        else:
            if args.status == "success":
                rsp = set_success_check(args.head_sha, app_token)
            else:
                rsp = set_failure_check(args.head_sha, app_token)
        print(f"Set status {args.status} for {args.head_sha}: success = {rsp}")
        sys.exit(0)
    elif args.subcommand == "invalidate-stale-checks":
        open_prs = get_open_prs()
        print(f"Found {len(open_prs)} open PRs")
        invalidate_stale_checks(open_prs, args.max_age_days, args.dry_run)
    else:
        p.print_help()

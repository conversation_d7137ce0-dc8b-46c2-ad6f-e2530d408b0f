from pathlib import Path
import time
from jwt import JWT, jwk_from_pem
import requests
import sys


def get_pem(pem_path: Path) -> bytes:
    """Get the PEM from the k8s secret."""
    return pem_path.read_bytes()


def create_gh_jwt(pem_path: Path) -> str:
    # Get PEM file path
    pem = get_pem(pem_path)
    app_id = 697167

    signing_key = jwk_from_pem(pem)

    payload = {
        # Issued at time
        "iat": int(time.time()),
        # JWT expiration time (10 minutes maximum)
        "exp": int(time.time()) + 600,
        # GitHub App's identifier
        "iss": app_id,
    }

    # Create JWT
    jwt_instance = JWT()
    encoded_jwt = jwt_instance.encode(payload, signing_key, alg="RS256")

    return encoded_jwt


def get_app_installation(jwt: str) -> str:
    """Get the GH app installation ID."""
    response = requests.get(
        "https://api.github.com/app/installations",
        headers={
            "Authorization": f"Bearer {jwt}",
            "Accept": "application/vnd.github+json",
            "X-GitHub-Api-Version": "2022-11-28",
        },
    )
    response.raise_for_status()
    return response.json()[0]["id"]


def get_gh_app_token(pem_path: Path) -> str:
    """Get the GH app token."""
    jwt = create_gh_jwt(pem_path)
    installation = get_app_installation(jwt)
    response = requests.post(
        f"https://api.github.com/app/installations/{installation}/access_tokens",
        headers={
            "Authorization": f"Bearer {jwt}",
            "Accept": "application/vnd.github+json",
            "X-GitHub-Api-Version": "2022-11-28",
        },
    )
    response.raise_for_status()
    return response.json()["token"]


if __name__ == "__main__":
    print(get_gh_app_token(Path(sys.argv[1])))

local clusters = import '../../cfg/clusters/clusters.jsonnet';

local hd_py = importstr 'invalidate-stale-prs.py';
local gh_app_auth_py = importstr 'gh_app_auth.py';
local requirements = importstr 'requirements.txt';

local name = 'invalidate-stale-prs';
local cmd = [
  // 'sleep inf && ',  // uncomment for debugging
  'pip install -r /run/augment/' + name + '/requirements.txt',
  ' && python3 /run/augment/' + name + '/' + name + '.py',
  ' invalidate-stale-checks --max-age-days 3',
];

local run_command = std.join(' ', cmd);

function(cluster_name, version, job_inst='') local C = clusters.cluster(cluster_name); C.k8s + {
  by_cluster(obj)::
    if std.objectHas(obj, cluster_name)
    then obj[cluster_name]
    else std.get(obj, 'default'),

  filter_by_cluster(obj):: {
    [kv.key]: kv.value
    for kv in std.objectKeysValues(obj)
    if !std.isObject(kv.value) || std.member([null, cluster_name], std.get(kv.value, 'cluster_name'))
  },

  local as_job = job_inst != '',
  name:: name,

  configmap: C.k8s.ConfigMap + {
    name:: $.name,
    namespace:: 'devex-metrics',
    data: {
      'invalidate-stale-prs.py': hd_py,
      'gh_app_auth.py': gh_app_auth_py,
      'requirements.txt': requirements,
    },
  },
  jobspec:: {
    completions: 1,
    template+: {
      metadata+: {
        annotations+: {
          'config.md5': std.md5(std.manifestJson($.configmap.data)),
        },
      },
      spec+: {
        local pod = self,
        restartPolicy: 'Never',
        containers+: [
          {
            name: $.name,
            image: 'python:3.11-slim',
            imagePullPolicy: 'IfNotPresent',
            env: [
              {
                name: 'GH_AUTH_TOKEN',
                valueFrom: {
                  secretKeyRef: {
                    name: 'gh-runner-monitor-token',  // This is defined in github-pr-stats.jsonnet
                    key: 'token',
                  },
                },
              },
              {
                name: 'GH_APP_PEM_PATH',
                value: '/mnt/augment/secrets/gh-app.pem',
              },
            ],
            args: [
              '/bin/bash',
              '-c',
              run_command,
            ],
            volumeMounts: [
              {
                name: $.name,
                mountPath: '/run/augment/' + $.name,
              },
              {
                mountPath: '/mnt/augment/secrets/',
                name: 'gh-app-pem',
                readOnly: true,
              },
            ],
          },
        ],
        volumes: [
          {
            name: $.name,
            configMap: {
              name: $.name,
            },
          },
          {
            name: 'gh-app-pem',
            secret: {
              secretName: 'gh-app-pem',  // pragma: allowlist secret
            },
          },
        ],
      },
    },
  },
  cronjob:: C.k8s.CronJob + {
    local cj = self,
    name:: $.name,
    schedule:: '@hourly',
    namespace:: 'devex-metrics',
    concurrencyPolicy:: $.CRONJOB_POLICY.REPLACE,
    metadata+: {
      labels+: {
        'app.kubernetes.io/name': $.name,
        'aug.version': version,
      },
    },
    spec+: {
      jobTemplate+: {
        metadata+: {
          labels+: {
            'app.kubernetes.io/name': $.name,
            'aug.version': version,
          },
        },
        spec+: $.jobspec,
      },
    },
    podspec:: {},
  },
  job:: C.k8s.Job + {
    name: $.name + '-adhoc-' + job_inst,
    spec: $.jobspec,
    metadata+: {
      labels+: {
        'app.kubernetes.io/name': $.name,
        'aug.version': version,
      },
    },
  },

  cronjob_or_job: if as_job then $.job else $.cronjob,
}

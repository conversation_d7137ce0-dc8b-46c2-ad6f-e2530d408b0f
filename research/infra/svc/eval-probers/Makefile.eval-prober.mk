BASENAME ?=
NAME     ?=
VERSION  ?= latest
CLUSTER  ?= gcp-us1

GIT_REF ?= main
LIMIT   ?= 100

CONTEXT  ?= $(shell jsonnet -A name="$(CLUSTER)" -A prop=context_admin -Se "(import '../../../cfg/clusters/clusters.jsonnet').cluster_prop")
K8S_CONF ?= $(NAME).jsonnet
KUBECTL  ?= kubectl --context="$(CONTEXT)"
KUBECFG  ?= kubecfg --context="$(CONTEXT)" \
	   --tla-code="cluster='$(CLUSTER)'" \
	   --tla-code="basename='$(BASENAME)'" \
	   --tla-code="name='$(NAME)'" \
	   --tla-code="version='$(VERSION)'" \
	   --tla-code="job_inst='$(JOB_INST)'" \
	   --tla-code="git_ref='$(GIT_REF)'" \
	   --tla-code="image='$(IMAGE)'" \
	   --tla-code="eval_limit=$(LIMIT)"

help:
	@echo usage:
	@echo "    make show"
	@echo "    make diff"
	@echo "    make apply"
	@echo "    make list"
	@echo
	@echo By default, manage the '$(NAME)' cronjob.
	@echo With JOB_INST set, run an adhoc job instead.
	@echo Use GIT_REF to choose a commit, branch, tag.
	@echo User LIMIT to pass to eval.py \(default is 1000\).
	@echo
	@echo Examples:
	@echo "    make list"
	@echo "    make apply"
	@echo "    make diff LIMIT=99 GIT_REF=my-branch"
	@echo "    make apply LIMIT=99 GIT_REF=my-branch"
	@echo "    make show LIMIT=100 JOB_INST=mattm"
	@echo "    make apply LIMIT=100 JOB_INST=mattm"
	@echo

show:
	$(KUBECFG) show $(K8S_CONF)

diff:
	-$(KUBECFG) diff --diff-strategy=subset $(K8S_CONF)

apply:
	$(KUBECFG) update $(K8S_CONF)

logs:
	$(KUBECTL) logs -l aug.service=$(NAME) $(LOGS_EXTRA)

list:
	$(KUBECTL) get -l aug.service=$(NAME) svc,ing,pvc,role,rolebinding,sa,cm,sealedsecret,secret,cronjob,job,deploy,pod

deploy: diff apply

local clusters = import '../../cfg/clusters/clusters.jsonnet';
local shared = import 'eval-prober-shared.jsonnet';

function(
  cluster='gcp-us1',
  basename=error 'eval-prober basename required',
  name=basename + '-prober',
  namespace=null,
  version=null,
  image=null,
  job_inst='',
  schedule=error 'eval-prober schedule (cron) required',
  timezone='US/Pacific',
  augment_data_subpath=error 'eval-prober augment_data_subpath required',
  eval_limit=100,
  eval_metrics_script=basename + '-metrics-kill-and-fill.sh',
  eval_metrics_sh='',

  job_backoff_limit=3,
  job_deadline=4 * 3600,

  github_secret='github-augment-eng-read',  // pragma: allowlist secret
  git_repo='augmentcode/augment',
  git_ref='main',

  augment_token_secret='eval-token',  // pragma: allowlist secret
  eval_job_root_public_html_dir='eval-probers/' + basename,
  eval_config=basename + '.yml',
  extra_eval_args='',
  additional_volmounts=null,

) local C = clusters.cluster(cluster); C.k8s + {

  local as_job = job_inst != '',
  local as_cron = !as_job,

  local eval_sh = importstr 'eval-remote.sh',

  BaseLabels+:: std.prune({
    'aug.service': name,
    'aug.version': version,
    'aug.eval-prober': 'true',
  }),

  Object+:: {
    name:: name,
    namespace:: if namespace != null then namespace else super.namespace,
  },

  // This provides defaults for:
  //  - annotations
  //  - container resources
  //  - node affinity selectors
  //  - tolerations
  node:: C.node('cpu') + {
    multiplier: 8,
  },

  jobspec:: {
    completions: 1,
    backoffLimit: job_backoff_limit,
    activeDeadlineSeconds: job_deadline,
    template+: {
      metadata+: {
        annotations+: $.node.k8s_annotations,
      },
      spec+: {
        local pod = self,
        tolerations: $.node.k8s_tolerations,
        affinity+: {
          nodeAffinity+: {
            requiredDuringSchedulingIgnoredDuringExecution+: {
              nodeSelectorTerms+: [{
                matchExpressions: $.node.k8s_node_selectors,
              }],
            },
          },
        },
        restartPolicy: $.RESTART_POLICY.NEVER,
        local eval_path = if as_job then '/run/augment/init/eval-remote.sh' else '/run/augment/' + $.configmap.name + '/eval-remote.sh',
        initContainers: if as_cron then [] else [
          $.Container + {
            name: 'init',
            image: 'busybox:latest',
            securityContext+: {
              runAsUser: 0,
            },
            command: [
              '/bin/sh',
              '-c',
              '/bin/install -oroot -groot -m755 -D <(echo -n "' + std.base64(eval_sh) + '" | base64 -d) ' + eval_path,
            ],
          },
        ],
        containers: [
          $.Container + {
            name: name,
            image: if image != null && image != '' then image else C.images_main.gpu,
            imagePullPolicy: $.IMAGE_PP.ALWAYS,
            resources: $.node.k8s_resources,
            volumeMounts: pod.volmount_mounts,

            local mp = '/run/augment/secrets/' + github_secret,
            workingDir: '/home/<USER>',
            securityContext+: {
              runAsUser: 1000,  // augment
            },
            command: [
              eval_path,
              mp + '/app_id',
              mp + '/installation_id',
              mp + '/private-key.pem',
              git_ref,
              '--git-repo=' + git_repo,
              '--eval-config=' + eval_config,
              '--aug-efs=/mnt/efs/augment',
              '--job-dir=/mnt/efs/augment/public_html/' + eval_job_root_public_html_dir,
              '--metrics-script=' + eval_metrics_script,
              '--limit=' + eval_limit,
            ] + (
              if (extra_eval_args != '') then std.split(extra_eval_args, ' ') else []
            ),
            env: [
              {
                name: 'AUGMENT_EFS_ROOT',
                value: '/mnt/efs/augment',
              },
            ],
          },
        ],
        volmounts:: [
          if as_cron then {
            local vm = self,
            name:: $.configmap.name,
            volume: {
              configMap: {
                name: vm.name,
                defaultMode: std.parseOctal('555'),
              },
            },
            mount: {
              mountPath: '/run/augment/' + vm.name,
            },
          } else {
            local vm = self,
            name:: 'init',
            volume: {
              emptyDir: {},
            },
            mount: {
              mountPath: '/run/augment/init',
            },
          },
          {
            local vm = self,
            name:: github_secret,
            volume: {
              secret: {
                secretName: vm.name,
                defaultMode: std.parseOctal('0444'),
              },
            },
            mount: {
              mountPath: '/run/augment/secrets/' + vm.name,
              readOnly: true,
            },
          },
          {
            local vm = self,
            name:: augment_token_secret,
            volume: {
              secret: {
                secretName: vm.name,
                defaultMode: std.parseOctal('0444'),
                items: [{
                  key: 'token',
                  path: 'api_token',
                }],
              },
            },
            mount: {
              mountPath: '/home/<USER>/.config/augment/',
              readOnly: true,
            },
          },
          {
            local vm = self,
            name:: shared(cluster).psql_secret.spec.template.metadata.name,
            volume: {
              secret: {
                secretName: vm.name,
                defaultMode: std.parseOctal('0444'),
              },
            },
            mount: {
              mountPath: '/run/augment/secrets/' + vm.name,
              readOnly: true,
            },
          },
        ] + [
          vm + {
            mount: self.mounts[0] + {
              local _path = std.lstripChars(augment_data_subpath, '/'),
              mountPath+: '/' + _path,
              subPath: _path,
              readOnly: true,
            },
          }
          for vm in C.volmounts
          if vm.path == '/mnt/efs/augment/data'
        ] + [
          vm + {
            mount: self.mounts[0] + {
              mountPath: '/mnt/efs/augment/public_html/' + eval_job_root_public_html_dir,
              subPath: eval_job_root_public_html_dir,
              readOnly: false,
            },
          }
          for vm in C.volmounts
          if vm.path == '/mnt/efs/augment/public_html'
        ] + (
          if additional_volmounts != null then additional_volmounts else []
        ),
      },
    },
  },
  cronjob:: $.CronJob + {
    schedule:: schedule,
    //timezone:: timezone,
    concurrencyPolicy:: $.CRONJOB_POLICY.ALLOW,
    spec+: {
      jobTemplate+: {
        spec+: $.jobspec,
      },
    },
    podspec:: {},
  },
  job:: $.Job + {
    name+: '-adhoc-' + job_inst,
    spec+: $.jobspec,
  },
  configmap: if as_job then null else $.ConfigMap + {
    name+:: if as_cron then '-init' else '-adhoc-shared-init',
    data: {
      'eval-remote.sh': eval_sh,
      [eval_metrics_script]: eval_metrics_sh,
    },
  },
  cronjob_or_job: if as_job then $.job else $.cronjob,
}

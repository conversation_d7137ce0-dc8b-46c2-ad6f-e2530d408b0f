local prober = import '../eval-prober.jsonnet';

function(
  // Makefile
  cluster,
  basename,
  name,
  version,
  job_inst,
  git_ref,
  image,
  eval_limit,

  // jsonnet
  eval_config=basename + '.yml',
  schedule='@daily',  // Every day, this hits an API that incurs a cost
  augment_data_subpath='processed/human_eval_instruct.v1',
  eval_metrics_sh=importstr 'humaneval-instruct-remote-metrics-kill-and-fill.sh',
)
  prober(
    cluster=cluster,
    basename=basename,
    name=name,
    version=version,
    job_inst=job_inst,
    git_ref=git_ref,
    image=image,
    eval_limit=eval_limit,

    eval_config=eval_config,
    schedule=schedule,
    augment_data_subpath=augment_data_subpath,
    eval_metrics_sh=eval_metrics_sh,
  )

#!/bin/bash

set -euo pipefail

resolve_flag() {
	declare -r val="$1"
	if [[ "$val" == /* ]] || [[ "$val" == ./* ]]; then
		if [[ -e "$val" ]]; then
			cat "$val"
			return
		fi
	fi
	printf "%s" "$val"
}

info() {
	declare -r fmt="$1"
	shift
	printf "== $fmt\n" "$@"
}

usage() {
	cat <<-EOF
	Usage:
	  $0 [flags] <app-id> <inst-id> <pem> [<git-ref>]

	The <app-id>, <inst-id> and <pem> args may each either be a literal value,
	or a filename if leading with '/' or './'.

	Options:
	  <app-id>                   The GitHub Application ID.
	  <inst-id>                  The GitHub App Installation ID.
	  <pem>                      The GitHub App Installation private key, for generating a JWT.
	  <git-ref>                  (default="main") The git repo reference to use.
	  --git-repo                 (default=augmentcode/augment) The git repo to use.
	  -b --bazel                 Use bazel to run, defaults to running directly.
	  -d --direct                (default) Run directly, not with bazel.
	  -c --eval-config <path>    Eval config file, relative to research/eval/config/
	  -A --aug-efs <path>        The path to the Augment EFS volume. (Library defaults to /mnt/efs/augment).
	  -j --job-dir <path>        The working/output directory for the eval. (Library defaults to AUGMENT_EFS_ROOT/eval/jobs/.
	  -M --metrics-script <path> Path to post-eval metrics script, relative to this wrapper script.
	  --break                    Break and sleep forever before running the eval. Useful for debugging.
	  --limit <num>              (default=1000) The "limit" argument to pass to eval.
	EOF
}

main() {
	declare -i bazel=0 break=0
	declare git_repo="augmentcode/augment"
	declare eval_config=""
	declare augment_efs_root="${AUGMENT_EFS_ROOT:-}"
	declare job_dir=""
	declare metrics_script=""
	declare -i limit=1000

	eval set -- $(getopt -n "$0" -o 'bdc:A:j:M:h' --long 'bazel,direct,break,git-repo:,eval-config:,aug-efs:,job-dir:,metrics-script:,limit:,help' -- "$@")
	while true; do
		case "$1" in
		--)
			shift
			break
		;;
		--git-repo)
			git_repo="$2"
			shift 2
			continue
		;;
		-b|--bazel)
			bazel=1
			shift
			continue
		;;
		-d|--direct)
			bazel=0
			shift
			continue
		;;
		--break)
			break=1
			shift
			continue
		;;
		-c|--eval-config)
			eval_config="$2"
			shift 2
			continue
		;;
		-A|--aug-efs)
			augment_efs_root="$2"
			shift 2
			continue
		;;
		-j|--job-dir)
			job_dir="$2"
			shift 2
			continue
		;;
		-M|--metrics-script)
			metrics_script="$2"
			shift 2
			continue
		;;
		--limit)
			limit="$2"
			shift 2
			continue
		;;
		-h|--help)
			usage
			return 0
		;;
		*)
			printf "Invalid Option: %s\n" "$1"
			usage
			return 1
		;;
		esac
	done
	declare -r git_repo bazel break eval_config augment_efs_root limit job_dir metrics_script

	if (( $# != 3 && $# != 4 )); then
		usage
		return 1
	fi

	declare -ri app_id="$(resolve_flag "$1")"
	declare -ri inst_id="$(resolve_flag "$2")"
	declare -r pem="$(resolve_flag "$3")"
	declare -r ref="${4:-main}"
	declare -r workdir="augment"

	if (( app_id == 0 || inst_id == 0 )); then
		usage
		return 1
	fi

	info "Formatting JWT for app_id=%d..." "$app_id"
	jwt="$(mkJWT "$app_id" "$pem")"

	info "Getting Installation Access Token for inst_id=%d..." "$inst_id"
	tok="$(getIAT "$inst_id" "$jwt")"

	info "Getting and extracting tar for '%s' into '%s'..." "$ref" "$workdir"
	time getTar "$tok" "$git_repo" "$ref" | extractTar augment

	if (( break )); then
		info "Sleeping forever, so you can 'kubectl exec'..."
		sleep infinity
		return
	fi

	cd augment

	if (( !bazel )); then
		info "Running research-init.sh..."
		time ./research/research-init.sh
	fi

	declare -a evalargs=(
		--skip_bazel
		--local
		"$PWD"/research/eval/configs/"$eval_config"
		--limit="$limit"
	)
	if [[ "$job_dir" ]]; then
		evalargs+=(--job_root="$job_dir/$(date +'%Y/%m-%b')/$(hostname -s)")
	fi

	info "Common Eval Args: %s" "${evalargs[*]}"

	if (( !bazel )); then
		info "Running eval.py directly (no bazel)..."
		time AUGMENT_EFS_ROOT="$augment_efs_root" ./research/eval/eval.py "${evalargs[@]}"
	else
		info "Running //research/eval (with bazel)..."
		time AUGMENT_EFS_ROOT="$augment_efs_root" bazel run //research/eval -- "${evalargs[@]}"
	fi

	if [[ "$job_dir" ]] && [[ "$metrics_script" ]]; then
		info "Running post-eval touch-ups..."
		cd "$job_dir"
		declare -r metrics_script_abs="$(dirname "$(readlink -f "$0")")"/"$metrics_script"
		if [[ -x "$metrics_script_abs" ]]; then
			info "Regenerating results DB and HTML..."
			"$metrics_script_abs"
			# Leave a copy with the data for convenience
			install -m775 --backup=none "$metrics_script_abs" .
		fi
	fi
}

api_call() {
	declare -r tok="$1"
	declare -r path="$2"
	shift 2

	declare -ra cmd=(
		curl -sL
		-H 'Accept: application/vnd.github+json'
		-H 'X-GitHub-API-Version: 2022-11-28'
		-H "Authorization: Bearer $tok"
		https://api.github.com/"$path"
		"$@"
	)
	"${cmd[@]}"
}

b64enc() {
	openssl base64 | tr -d '=' | tr '/+' '_-' | tr -d '\n'
}

mkJWT() {
	declare -ri app_id="$1"
	declare -r  pem="$2"

	declare -ri now=$(date +%s)
	declare -ri iat=$((now - 60)) # Issues 60 seconds in the past
	declare -ri exp=$((now + 120)) # Expires 2 minutes in the future

	declare -r header_json='{
		"typ": "JWT",
		"alg": "RS256"
	}'
	declare -r payload_json="$(printf '{
		"iat": %d,
		"exp": %d,
		"iss": %d
	}' "$iat" "$exp" "$app_id")"

	declare -r header="$(b64enc <<<"$header_json")"
	declare -r payload="$(b64enc <<<"$payload_json")"
	declare -r hp="$header.$payload"
	declare -r sig="$(openssl dgst -sha256 -sign <(printf "%s" "$pem") <(printf "%s" "$hp") | b64enc)"

	declare -r jwt="$hp.$sig"

	echo -n "$jwt"
}

getIAT() {
	declare -ri inst_id="$1"
	declare -r jwt="$2"
	api_call "$jwt" app/installations/"$inst_id"/access_tokens -X POST | jq -r '.token'
}

getTar() {
	declare -r tok="$1"
	declare -r owner_repo="$2"
	declare -r ref="$3"
	api_call "$tok" "repos/$owner_repo/tarball/$ref"
}

extractTar() {
	declare -r dir="$1"
	if [[ -e "$dir" ]]; then
		printf "Output Dir %s already exists.\n" "$dir" >&2
		return 1
	fi
	mkdir -p "$dir"
	tar --strip-components=1 -zx -C "$dir"
}

main "$@"

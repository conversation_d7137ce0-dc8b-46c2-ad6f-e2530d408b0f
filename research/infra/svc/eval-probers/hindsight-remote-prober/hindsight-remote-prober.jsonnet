local prober = import '../eval-prober.jsonnet';

function(
  // Makefile
  cluster,
  basename,
  name,
  version,
  job_inst,
  git_ref,
  image,
  eval_limit,

  // jsonnet
  eval_config=basename + '.yml',
  schedule='0 */4 * * *',  // Every 4 hours, on the hour, 24/7
  augment_data_subpath='eval/hindsight',
  eval_metrics_sh=importstr 'hindsight-remote-metrics-kill-and-fill.sh',
  additional_volmounts=[
    {
      local vm = self,
      name:: 'aug-prod-cw-ri-importer',
      volume: {
        secret: {
          secretName: vm.name,
          defaultMode: std.parseOctal('0444'),
        },
      },
      mount: {
        mountPath: '/mnt/augment/secrets/cw-ri-importer',
        readOnly: true,
      },
    },
  ],
)
  prober(
    cluster=cluster,
    basename=basename,
    name=name,
    version=version,
    job_inst=job_inst,
    git_ref=git_ref,
    image=image,
    eval_limit=eval_limit,

    eval_config=eval_config,
    schedule=schedule,
    augment_data_subpath=augment_data_subpath,
    eval_metrics_sh=eval_metrics_sh,
    additional_volmounts=additional_volmounts,
  )

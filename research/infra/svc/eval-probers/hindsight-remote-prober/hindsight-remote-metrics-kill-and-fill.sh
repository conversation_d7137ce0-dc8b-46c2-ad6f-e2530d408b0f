#!/bin/bash

# This script rebuilds a sqlite DB with all eval-prober results in the current directory.
# It then runs the query passed on the command line, or a default one if nothing is provided.
# It's a bit heavy weight since all columns need to be supported individually (as opposed to
# a giant json payload column) but the end result is the easiest to query.

set -eu

main() {
	declare -r BASENAME='hindsight-remote'

	declare -r tmp="$(mktemp "/tmp/$BASENAME-prober-metrics-tmp.XXXXXXXX")"
	declare -r sqlite_db="$BASENAME.sqlite.db"
	declare -r html="$BASENAME.results.html"

	#
	# Normalize various input formats into a single jsonl file.
	#
	shopt -s nullglob

	log "Aggregating jsonl results: %s..." "$tmp"
	jq -cr 'if type == "array" then .[] else . end | .+= {"filename": input_filename}' */*results.jsonl */*/*/*results.jsonl > "$tmp"

	#
	# Kill-and-fill `devex-metrix` postgre table
	#

	postgre_kill_and_fill "$tmp"

	#
	# Cleanup
	#

	rm -f "$tmp"
}

log() {
	declare -r fmt="$1"; shift
	printf "%(%Y-%m-%d %H:%M:%S %Z)T INFO $fmt\n" -1 "$@"
}

pgval() {
	declare -r envvar="$1"
	declare -r cfgfile="$2"
	declare -r default="$3"

	if [[ -v "$envvar" ]]; then
		echo -n "${!envvar}"
	elif [[ -r "$cfgfile" ]]; then
		echo -n "$(<"$cfgfile")"
	else
		echo -n "$default"
	fi
}

postgre_kill_and_fill() {
	declare -r metrics_file="$1"

	log "Reloading Postgre Data from %s" "$metrics_file"

	declare -r pgcfg="/run/augment/secrets/eval-prober-psql-creds"
	declare -r pghostname="$(pgval PGHOSTNAME "$pgcfg/hostname" "devex-metrics-postgresql")"
	declare -r pgusername="$(pgval PGUSERNAME "$pgcfg/username" "augment")"
	declare -r pgpassword="$(pgval PGPASSWORD "$pgcfg/password" "")"
	declare -r pgdatabase="$(pgval PGDATABASE "$pgcfg/database" "metrics")"

	PGPASSWORD="$pgpassword" psql --host="$pghostname" --user="$pgusername" "$pgdatabase" --file=/dev/stdin <<-EOF
	BEGIN;

	DROP TABLE IF EXISTS hindsight_prober_tmp;
	CREATE TABLE hindsight_prober_tmp (r jsonb);
	\\copy hindsight_prober_tmp (r) FROM '$metrics_file';

	COMMIT;

	BEGIN;

	DROP TABLE IF EXISTS hindsight_prober;
	CREATE TABLE hindsight_prober (
		start_time timestamp,
		duration REAL,
		"limit" INT,

		"metrics" JSONB,

		filename VARCHAR,
		dirname VARCHAR,
		url VARCHAR
	);

	INSERT INTO hindsight_prober SELECT
		to_timestamp( (r->'start_time')::int ),
		(r->'run_duration')::real / 60,
		(r->'limit')::int,

		(r->'metrics')::jsonb,

		r->>'filename',
		regexp_replace(r->>'filename', '/[^/]*$', ''),
		'https://webserver.gcp-us1.r.augmentcode.com/eval-probers/$BASENAME/' || regexp_replace(r->>'filename', '/[^/]*$', '/')

	FROM hindsight_prober_tmp;

	COMMIT;
	EOF
}

main "$@"

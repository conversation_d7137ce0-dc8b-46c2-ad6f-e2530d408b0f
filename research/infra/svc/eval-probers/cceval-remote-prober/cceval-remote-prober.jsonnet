local prober = import '../eval-prober.jsonnet';

function(
  // Makefile
  cluster,
  basename,
  name,
  version,
  job_inst,
  git_ref,
  image,
  eval_limit,

  // jsonnet
  eval_config=basename + '.yml',
  schedule='0 */2 * * *',  // Every 2 hours, on the hour, 24/7
  augment_data_subpath='eval/cceval',
  eval_metrics_sh=importstr 'cceval-remote-metrics-kill-and-fill.sh',
)
  prober(
    cluster=cluster,
    basename=basename,
    name=name,
    version=version,
    job_inst=job_inst,
    git_ref=git_ref,
    image=image,
    eval_limit=eval_limit,

    eval_config=eval_config,
    schedule=schedule,
    augment_data_subpath=augment_data_subpath,
    eval_metrics_sh=eval_metrics_sh,
  )

#!/bin/bash

# This script rebuilds a sqlite DB with all eval-prober results in the current directory.
# It then runs the query passed on the command line, or a default one if nothing is provided.
# It's a bit heavy weight since all columns need to be supported individually (as opposed to
# a giant json payload column) but the end result is the easiest to query.

set -eu

main() {
	declare -r BASENAME='cceval-remote'

	declare -r tmp="$(mktemp "/tmp/$BASENAME-prober-metrics-tmp.XXXXXXXX")"
	declare -r tmp_as_array="$tmp.array"
	declare -r sqlite_db="$BASENAME.sqlite.db"
	declare -r html="$BASENAME.results.html"

	#
	# Normalize various input formats into a single file.
	# Postgre wants a jsonl file, Sqlite wants single json array.
	#
	shopt -s nullglob

	log "Aggregating jsonl results: %s..." "$tmp"
	jq -cr 'if type == "array" then .[] else . end | .+= {"filename": input_filename}' */*results.jsonl */*/*/*results.jsonl > "$tmp"
	log "Aggregating json results: %s..." "$tmp_as_array"
	jq -sr '.' "$tmp" > "$tmp_as_array"

	#
	# Kill-and-fill `devex-metrix` postgre table
	#

	postgre_kill_and_fill "$tmp"

	#
	# Kill-and-fill local sqlite table
	#

	sqlite_kill_and_fill "$sqlite_db" "$tmp_as_array"

	#
	# Regenerate HTML report
	#

	regenerate_html_report "$html" "$sqlite_db"

	#
	# Cleanup
	#

	rm -f "$tmp" "$tmp_as_array"
}

log() {
	declare -r fmt="$1"; shift
	printf "%(%Y-%m-%d %H:%M:%S %Z)T INFO $fmt\n" -1 "$@"
}

pgval() {
	declare -r envvar="$1"
	declare -r cfgfile="$2"
	declare -r default="$3"

	if [[ -v "$envvar" ]]; then
		echo -n "${!envvar}"
	elif [[ -r "$cfgfile" ]]; then
		echo -n "$(<"$cfgfile")"
	else
		echo -n "$default"
	fi
}

postgre_kill_and_fill() {
	declare -r metrics_file="$1"

	log "Reloading Postgre Data from %s" "$metrics_file"

	declare -r pgcfg="/run/augment/secrets/eval-prober-psql-creds"
	declare -r pghostname="$(pgval PGHOSTNAME "$pgcfg/hostname" "devex-metrics-postgresql")"
	declare -r pgusername="$(pgval PGUSERNAME "$pgcfg/username" "augment")"
	declare -r pgpassword="$(pgval PGPASSWORD "$pgcfg/password" "")"
	declare -r pgdatabase="$(pgval PGDATABASE "$pgcfg/database" "metrics")"

	PGPASSWORD="$pgpassword" psql --host="$pghostname" --user="$pgusername" "$pgdatabase" --file=/dev/stdin <<-EOF
	BEGIN;

	DROP TABLE IF EXISTS cceval_prober_tmp;
	CREATE TABLE cceval_prober_tmp (r jsonb);
	\\copy cceval_prober_tmp (r) FROM '$metrics_file';

	COMMIT;

	BEGIN;

	DROP TABLE IF EXISTS cceval_prober;
	CREATE TABLE cceval_prober (
		start_time timestamp,
		duration REAL,
		"limit" INT,

		"total_exact_match" REAL,
		"total_exact_match_strict" REAL,
		"total_edit_similarity" REAL,
		"total_ground_truth_log_likelihood" REAL,
		"total_ground_truth_token_accuracy" REAL,
		"total_ast_match" REAL,
		"total_samples" INT,

		"java_exact_match" REAL,
		"java_exact_match_strict" REAL,
		"java_edit_similarity" REAL,
		"java_ground_truth_log_likelihood" REAL,
		"java_ground_truth_token_accuracy" REAL,
		"java_ast_match" REAL,
		"java_samples" INT,

		"python_exact_match" REAL,
		"python_exact_match_strict" REAL,
		"python_edit_similarity" REAL,
		"python_ground_truth_log_likelihood" REAL,
		"python_ground_truth_token_accuracy" REAL,
		"python_ast_match" REAL,
		"python_samples" INT,

		"csharp_exact_match" REAL,
		"csharp_exact_match_strict" REAL,
		"csharp_edit_similarity" REAL,
		"csharp_ground_truth_log_likelihood" REAL,
		"csharp_ground_truth_token_accuracy" REAL,
		"csharp_ast_match" REAL,
		"csharp_samples" INT,

		"typescript_exact_match" REAL,
		"typescript_exact_match_strict" REAL,
		"typescript_edit_similarity" REAL,
		"typescript_ground_truth_log_likelihood" REAL,
		"typescript_ground_truth_token_accuracy" REAL,
		"typescript_ast_match" REAL,
		"typescript_samples" INT,

		filename VARCHAR,
		dirname VARCHAR,
		url VARCHAR
	);

	INSERT INTO cceval_prober SELECT
		to_timestamp( (r->'start_time')::int ),
		(r->'run_duration')::real / 60,
		(r->'limit')::int,

		(r#>'{metrics,total,exact_match}')::real,
		(r#>'{metrics,total,exact_match_strict}')::real,
		(r#>'{metrics,total,edit_similarity}')::real,
		(r#>'{metrics,total,ground_truth_log_likelihood}')::real,
		(r#>'{metrics,total,ground_truth_token_accuracy}')::real,
		(r#>'{metrics,total,ast_match}')::real,
		(r#>'{metrics,total,samples}')::int,

		(r#>'{metrics,per_language,java,exact_match}')::real,
		(r#>'{metrics,per_language,java,exact_match_strict}')::real,
		(r#>'{metrics,per_language,java,edit_similarity}')::real,
		(r#>'{metrics,per_language,java,ground_truth_log_likelihood}')::real,
		(r#>'{metrics,per_language,java,ground_truth_token_accuracy}')::real,
		(r#>'{metrics,per_language,java,ast_match}')::real,
		(r#>'{metrics,per_language,java,samples}')::int,

		(r#>'{metrics,per_language,python,exact_match}')::real,
		(r#>'{metrics,per_language,python,exact_match_strict}')::real,
		(r#>'{metrics,per_language,python,edit_similarity}')::real,
		(r#>'{metrics,per_language,python,ground_truth_log_likelihood}')::real,
		(r#>'{metrics,per_language,python,ground_truth_token_accuracy}')::real,
		(r#>'{metrics,per_language,python,ast_match}')::real,
		(r#>'{metrics,per_language,python,samples}')::int,

		(r#>'{metrics,per_language,csharp,exact_match}')::real,
		(r#>'{metrics,per_language,csharp,exact_match_strict}')::real,
		(r#>'{metrics,per_language,csharp,edit_similarity}')::real,
		(r#>'{metrics,per_language,csharp,ground_truth_log_likelihood}')::real,
		(r#>'{metrics,per_language,csharp,ground_truth_token_accuracy}')::real,
		(r#>'{metrics,per_language,csharp,ast_match}')::real,
		(r#>'{metrics,per_language,csharp,samples}')::int,

		(r#>'{metrics,per_language,typescript,exact_match}')::real,
		(r#>'{metrics,per_language,typescript,exact_match_strict}')::real,
		(r#>'{metrics,per_language,typescript,edit_similarity}')::real,
		(r#>'{metrics,per_language,typescript,ground_truth_log_likelihood}')::real,
		(r#>'{metrics,per_language,typescript,ground_truth_token_accuracy}')::real,
		(r#>'{metrics,per_language,typescript,ast_match}')::real,
		(r#>'{metrics,per_language,typescript,samples}')::int,

		r->>'filename',
		regexp_replace(r->>'filename', '/[^/]*$', ''),
		'https://webserver.gcp-us1.r.augmentcode.com/eval-probers/$BASENAME/' || regexp_replace(r->>'filename', '/[^/]*$', '/')

	FROM cceval_prober_tmp;

	COMMIT;
	EOF
}

sqlite_kill_and_fill() {
	declare -r db="$1"
	declare -r metrics_file="$2"

	log "Recreating sqlite db %s" "$db"

	TZ=US/Pacific sqlite3 "$db" <<-'EOF'
	DROP TABLE IF EXISTS results;
	CREATE TABLE results (
		start_time STRING PRIMARY KEY,
		duration INT,
		`limit` INT,

		`total_exact_match` REAL,
		`total_exact_match_strict` REAL,
		`total_edit_similarity` REAL,
		`total_ground_truth_log_likelihood` REAL,
		`total_ground_truth_token_accuracy` REAL,
		`total_ast_match` REAL,
		`total_samples` INT,

		`java_exact_match` REAL,
		`java_exact_match_strict` REAL,
		`java_edit_similarity` REAL,
		`java_ground_truth_log_likelihood` REAL,
		`java_ground_truth_token_accuracy` REAL,
		`java_ast_match` REAL,
		`java_samples` INT,

		`python_exact_match` REAL,
		`python_exact_match_strict` REAL,
		`python_edit_similarity` REAL,
		`python_ground_truth_log_likelihood` REAL,
		`python_ground_truth_token_accuracy` REAL,
		`python_ast_match` REAL,
		`python_samples` INT,

		`csharp_exact_match` REAL,
		`csharp_exact_match_strict` REAL,
		`csharp_edit_similarity` REAL,
		`csharp_ground_truth_log_likelihood` REAL,
		`csharp_ground_truth_token_accuracy` REAL,
		`csharp_ast_match` REAL,
		`csharp_samples` INT,

		`typescript_exact_match` REAL,
		`typescript_exact_match_strict` REAL,
		`typescript_edit_similarity` REAL,
		`typescript_ground_truth_log_likelihood` REAL,
		`typescript_ground_truth_token_accuracy` REAL,
		`typescript_ast_match` REAL,
		`typescript_samples` INT,

		url TEXT
	);
	EOF

	log "Reloading sqlite db %s from %s" "$db" "$metrics_file"

	TZ=US/Pacific sqlite3 "$db" <<-EOF
	INSERT OR REPLACE INTO results SELECT
		DATETIME(value->'$.start_time', 'unixepoch', 'localtime'),
		CAST(value->'$.run_duration' / 60 AS INT),
		value->'$.limit',

		value->'$.metrics.total.exact_match',
		value->'$.metrics.total.exact_match_strict',
		value->'$.metrics.total.edit_similarity',
		value->'$.metrics.total.ground_truth_log_likelihood',
		value->'$.metrics.total.ground_truth_token_accuracy',
		value->'$.metrics.total.ast_match',
		value->'$.metrics.total.samples',

		value->'$.metrics.per_language.java.exact_match',
		value->'$.metrics.per_language.java.exact_match_strict',
		value->'$.metrics.per_language.java.edit_similarity',
		value->'$.metrics.per_language.java.ground_truth_log_likelihood',
		value->'$.metrics.per_language.java.ground_truth_token_accuracy',
		value->'$.metrics.per_language.java.ast_match',
		value->'$.metrics.per_language.java.samples',

		value->'$.metrics.per_language.python.exact_match',
		value->'$.metrics.per_language.python.exact_match_strict',
		value->'$.metrics.per_language.python.edit_similarity',
		value->'$.metrics.per_language.python.ground_truth_log_likelihood',
		value->'$.metrics.per_language.python.ground_truth_token_accuracy',
		value->'$.metrics.per_language.python.ast_match',
		value->'$.metrics.per_language.python.samples',

		value->'$.metrics.per_language.csharp.exact_match',
		value->'$.metrics.per_language.csharp.exact_match_strict',
		value->'$.metrics.per_language.csharp.edit_similarity',
		value->'$.metrics.per_language.csharp.ground_truth_log_likelihood',
		value->'$.metrics.per_language.csharp.ground_truth_token_accuracy',
		value->'$.metrics.per_language.csharp.ast_match',
		value->'$.metrics.per_language.csharp.samples',

		value->'$.metrics.per_language.typescript.exact_match',
		value->'$.metrics.per_language.typescript.exact_match_strict',
		value->'$.metrics.per_language.typescript.edit_similarity',
		value->'$.metrics.per_language.typescript.ground_truth_log_likelihood',
		value->'$.metrics.per_language.typescript.ground_truth_token_accuracy',
		value->'$.metrics.per_language.typescript.ast_match',
		value->'$.metrics.per_language.typescript.samples',

		format('https://webserver.gcp-us1.r.augmentcode.com/eval-probers/$BASENAME/%s', value->>'$.filename')

	FROM json_each(readfile('$metrics_file'));
	EOF
}

regenerate_html_report() {
	declare -r html="$1"
	declare -r db="$2"

	log "Rebuilding HTML report %s" "$html"

	cat > "$html" <<-EOF
	<!DOCTYPE html>
	<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
		<title>CCEval Results</title>

		<style type="text/css">
		body {
			background-color: Canvas;
			color: CanvasText;
			color-scheme: light dark;
		}
		</style>
	</head>
	<body>
		<pre>$(sqlite3 --header --column "$db" 'SELECT * FROM results ORDER BY start_time ASC')</pre>
	</body>
	</html>
	EOF
}

main "$@"

NAME    := eval-prober-shared
CLUSTER := gcp-us1

CONTEXT  ?= $(shell jsonnet -A name="$(CLUSTER)" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
K8S_CONF ?= $(NAME).jsonnet
KUBECTL  ?= kubectl --context="$(CONTEXT)"
KUBECFG  ?= kubecfg --context="$(CONTEXT)" \
	   --tla-code="cluster='$(CLUSTER)'" \
	   --tla-code="name='$(NAME)'"

show:
	$(KUBECFG) show $(K8S_CONF)

diff:
	-$(KUBECFG) diff --diff-strategy=subset $(K8S_CONF)

apply:
	$(KUBECFG) update $(K8S_CONF)

list:
	$(KUBECTL) get -l aug.service=$(NAME) sealedsecret,secret,svc,ing,pvc,role,rolebinding,sa,cm,cronjob,job,deploy,pod

list-all:
	$(KUBECTL) get -l aug.eval-prober sealedsecret,secret,svc,ing,pvc,role,rolebinding,sa,cm,cronjob,job,deploy,pod

deploy: diff apply

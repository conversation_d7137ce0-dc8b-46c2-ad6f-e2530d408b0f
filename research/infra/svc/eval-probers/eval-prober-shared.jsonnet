local clusters = import '../../cfg/clusters/clusters.jsonnet';

function(
  cluster='gcp-us1',
  name='eval-prober-shared',
) local C = clusters.cluster(cluster); C.k8s + {
  psql_secret: C.k8s.SealedSecret + {
    name:: 'eval-prober-psql-creds',
    metadata+: {
      labels+: {
        'aug.service': name,
        'aug.eval-prober': 'true',
      },
    },
    encryptedData:: {
      password: 'AgDFF33+WQ3i9+uACibgQuV0YWo4/hgSY2Yka8yzITLoe/3p04ca9wK4FjU6EMXho3IS7ijYEEA3vRyIrFyIKYW1o/N4oDDn9xmSrpYcgfdKHiOukZsuuvjRkW/ugcJIfeu+FgUeWRsEkBwVJ6XHJ1LSkDdITTjrwx/hdwVnpxOnpvPp1L5LYMFr+OqbdHnDMypb7e0WYDPN58SfkeBjFZT3I/dJBvLSKzA+VKEjxgAObv3k9WASK5syuSsKj2S5X1wPQBGd/qIqmXYLHP927J9ZFTMcuWmAK6jUWMuJmQ33jBaB1hc9db+MU5Sd+ZAXwN90RkOey162tI3niI+cFXb+e/pCaxOrt3luDn5EG7mKnvd1NiFLy31q1V3tRSLVbLJokBSFZ/idkOezE2/uq/B7/aGBf6wIN1kdXxoL+zTKcPjxi0bIvNqzr5aY+YBmzRl6RVmtg4ztOwWnO249adAv11UeTZDxNNYQLHObrhI4JaGNoP1bsj0hh2lFt1i73LZDjxsmJObiw0bmYJKmjmCHtuqjQYOr/TroN7dmDY6CTE++nFS4aA6HMu20hYFYGjIAYQQeyd//eyZND5Ipm6beW4pjJPndtary5YtK0zG/uoKZVNe6k8Xq8MozVRr/mbTG54lqhnih8el44FXqBiZhuKMA267yF9bFpn8LOjmTFmmFnHq1cbl8KYZqwaeGaJo6oApc87J+PVX2neKvJgSKgOSZhavzqUHZXcEN/Q==',  // pragma: allowlist secret
    },
    spec+: {
      template+: {
        data+: {
          hostname: 'devex-metrics.r.augmentcode.com',
          username: 'augment',
          database: 'metrics',
        },
      },
    },
  },
}

local clusters = import '../../cfg/clusters/clusters.jsonnet';

local gc_py = importstr 'ci-gc.py';
local requirements = importstr 'requirements.txt';

function(cluster_name, version, job_inst='') local C = clusters.cluster(cluster_name); C.k8s + {
  by_cluster(obj)::
    if std.objectHas(obj, cluster_name)
    then obj[cluster_name]
    else std.get(obj, 'default'),

  filter_by_cluster(obj):: {
    [kv.key]: kv.value
    for kv in std.objectKeysValues(obj)
    if !std.isObject(kv.value) || std.member([null, cluster_name], std.get(kv.value, 'cluster_name'))
  },

  local as_job = job_inst != '',

  configmap: C.k8s.ConfigMap + {
    name:: 'ci-worker-gc',
    data: {
      'ci-gc.py': gc_py,
      'requirements.txt': requirements,
    },
  },
  jobspec:: {
    completions: 1,
    template+: {
      metadata+: {
        annotations+: {
          'config.md5': std.md5(std.manifestJson($.configmap.data)),
        },
      },
      spec+: {
        local pod = self,
        restartPolicy: 'Never',
        serviceAccountName: 'determined-service-account',
        containers+: [
          {
            name: $.configmap.name,
            image: 'python:3.11-slim',
            imagePullPolicy: 'IfNotPresent',
            args: [
              '/bin/bash',
              '-c',
              'pip install -r /run/augment/ci-worker-gc/requirements.txt && python3 /run/augment/ci-worker-gc/ci-gc.py --namespace=' + C.name,
            ],
            volumeMounts: [
              {
                name: $.configmap.name,
                mountPath: '/run/augment/ci-worker-gc',
              },
            ],
          },
        ],
        volumes: [
          {
            name: $.configmap.name,
            configMap: {
              name: $.configmap.name,
            },
          },
        ],
      },
    },
  },
  cronjob:: C.k8s.CronJob + {
    local cj = self,
    name:: $.configmap.name,
    schedule:: '@hourly',
    namespace:: C.name,
    concurrencyPolicy:: $.CRONJOB_POLICY.REPLACE,
    metadata+: {
      labels+: {
        'app.kubernetes.io/name': $.configmap.name,
        'aug.version': version,
      },
    },
    spec+: {
      jobTemplate+: {
        metadata+: {
          labels+: {
            'app.kubernetes.io/name': $.configmap.name,
            'aug.version': version,
          },
        },
        spec+: $.jobspec,
      },
    },
    podspec:: {},
  },
  job:: C.k8s.Job + {
    name: $.configmap.name + '-adhoc-' + job_inst,
    spec: $.jobspec,
    metadata+: {
      labels+: {
        'app.kubernetes.io/name': $.configmap.name,
        'aug.version': version,
      },
    },
  },

  cronjob_or_job: if as_job then $.job else $.cronjob,
}

#!/usr/bin/env python3

import argparse
from kubernetes import client, config
from datetime import datetime, timedelta, timezone
from kubernetes.client import (
    V1ConfigMapList,
    V1PodList,
)
from kubernetes.client.rest import ApiException

config.load_config()
try:
    c = client.Configuration().get_default_copy()
except AttributeError:
    c = client.Configuration()
client.Configuration.set_default(c)
_v1_api = client.CoreV1Api()


def delete_pods_by_age(
    pods: V1PodList, max_age: timedelta, namespace: str, dry_run: bool
):
    now = datetime.now(timezone.utc)
    for pod in pods.items:  # type: ignore
        if pod.status.start_time is None:
            continue
        dr = "(dry-run) " if dry_run else ""
        if (now - pod.status.start_time) > max_age:
            print(
                f"{dr}deleting pod {pod.metadata.name} ({pod.status.phase}) from {pod.status.start_time}"
            )
            if not dry_run:
                _v1_api.delete_namespaced_pod(pod.metadata.name, namespace)


def delete_configmaps_by_age(
    configmaps: V1ConfigMapList, max_age: timedelta, namespace: str, dry_run: bool
):
    now = datetime.now(timezone.utc)
    for cm in configmaps.items:  # type: ignore
        if cm.metadata.creation_timestamp is None:
            continue
        dr = "(dry-run) " if dry_run else ""
        if (now - cm.metadata.creation_timestamp) > max_age:
            print(
                f"{dr}deleting configmap {cm.metadata.name} from {cm.metadata.creation_timestamp}"
            )
            if not dry_run:
                _v1_api.delete_namespaced_config_map(cm.metadata.name, namespace)


def main(namespace: str, dry_run: bool):
    # ci-workers, 12 hours
    delete_pods_by_age(
        _v1_api.list_namespaced_pod(namespace=namespace, label_selector="GITHUB_PR"),
        timedelta(hours=12),
        namespace=namespace,
        dry_run=dry_run,
    )

    # hydra, 1hr on success
    delete_pods_by_age(  # type: ignore
        _v1_api.list_namespaced_pod(
            namespace=namespace,
            label_selector="hydra-block-resource-internet-access",
            field_selector="status.phase=Succeeded",
        ),
        timedelta(hours=1),
        namespace=namespace,
        dry_run=dry_run,
    )

    # hydra, 1hr on failure
    delete_pods_by_age(  # type: ignore
        _v1_api.list_namespaced_pod(
            namespace=namespace,
            label_selector="hydra-block-resource-internet-access",
            field_selector="status.phase=Failed",
        ),
        timedelta(hours=1),
        namespace=namespace,
        dry_run=dry_run,
    )

    # code-exec-eval, 1hr on success
    delete_pods_by_age(  # type: ignore
        _v1_api.list_namespaced_pod(
            namespace=namespace,
            label_selector="aug.app=code-exec-eval",
            field_selector="status.phase=Succeeded",
        ),
        timedelta(hours=1),
        namespace=namespace,
        dry_run=dry_run,
    )

    # code-exec-eval, 1hr on failure
    delete_pods_by_age(  # type: ignore
        _v1_api.list_namespaced_pod(
            namespace=namespace,
            label_selector="aug.app=code-exec-eval",
            field_selector="status.phase=Failed",
        ),
        timedelta(hours=1),
        namespace=namespace,
        dry_run=dry_run,
    )

    # Other pods completed - 2 days
    delete_pods_by_age(  # type: ignore
        _v1_api.list_namespaced_pod(
            namespace=namespace, field_selector="status.phase=Succeeded"
        ),
        timedelta(days=2),
        namespace=namespace,
        dry_run=dry_run,
    )
    delete_pods_by_age(  # type: ignore
        _v1_api.list_namespaced_pod(
            namespace=namespace, field_selector="status.phase=Failed"
        ),
        timedelta(days=2),
        namespace=namespace,
        dry_run=dry_run,
    )

    # hydra configmaps - 1 day
    delete_configmaps_by_age(
        _v1_api.list_namespaced_config_map(
            namespace=namespace, label_selector="augment_app=hydra"
        ),
        timedelta(days=1),
        namespace=namespace,
        dry_run=dry_run,
    )

    # code-exec-eval configmaps - 12hr
    delete_configmaps_by_age(
        _v1_api.list_namespaced_config_map(
            namespace=namespace, label_selector="aug.app=code-exec-eval"
        ),
        timedelta(hours=12),
        namespace=namespace,
        dry_run=dry_run,
    )

    # Spark configmaps - 10 days
    delete_configmaps_by_age(
        _v1_api.list_namespaced_config_map(
            namespace=namespace, label_selector="spark-role=executor"
        ),
        timedelta(days=10),
        namespace=namespace,
        dry_run=dry_run,
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--namespace", required=True)
    parser.add_argument("--dry-run", action="store_true")
    args = parser.parse_args()
    namespace = args.namespace
    dry_run = args.dry_run
    main(namespace, dry_run)

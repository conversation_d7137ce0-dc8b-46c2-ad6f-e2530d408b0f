#!/usr/bin/env python3
"""Garbage collect experiments."""

# pylint: disable=protected-access

import sys
import os
from datetime import datetime, timezone
from pathlib import Path

import pandas as pd
from determined.experimental import client as det_client

det_url = os.getenv("DET_MASTER")
assert det_url, "DET_MASTER must be set in the environment"
det_user = Path("/run/augment/secrets/gh-determined-password/username").read_text()
det_pass = Path("/run/augment/secrets/gh-determined-password/password").read_text()
try:
    det_client.login(
        master=det_url,
        user=det_user,
        password=det_pass,
    )
except ValueError:
    ...

project_names = [
    "Dev/Default",
    "GH/eval",
    "GH/training",
    "GH/regressions",
]

td_max = pd.Timedelta("14D")
now = pd.to_datetime(datetime.now(timezone.utc), utc=True)

workspace_ids = {}

r = det_client._determined._session.get(  # type: ignore
    "/api/v1/workspaces",
)
for workspace in r.json()["workspaces"]:
    workspace_ids[workspace["name"]] = workspace["id"]

workspaces = set([pn.split("/")[0] for pn in project_names])
projects = {}
for w in workspaces:
    r = det_client._determined._session.get(  # type: ignore
        f"/api/v1/workspaces/{workspace_ids[w]}/projects",
    )
    for p in r.json()["projects"]:
        if f"{w}/{p['name']}" in project_names:
            projects[f"{w}/{p['name']}"] = p["id"]

max_deletions = 20
for project_name, project_id in projects.items():
    offset = 0
    limit = 100
    deletions = []
    print(f"Checking {project_name}")
    while True:
        num_deleted = 0
        r = det_client._determined._session.get(  # type: ignore
            "/api/v1/experiments",
            params={"projectId": project_id, "offset": offset, "limit": limit},
        )
        project = r.json()
        return_count = len(project["experiments"])
        for experiment in [
            e
            for e in project["experiments"]
            if e["state"] not in ("STATE_DELETING", "STATE_QUEUED", "STATE_RUNNING")
            and e["username"] == "github"
        ]:
            age = pd.to_datetime(experiment["endTime"])
            if now - age > td_max:
                num_deleted += 1
                deletions.append(experiment["id"])
        if return_count < limit:
            break
        offset += return_count
    print(f"{project_name} deleting {len(deletions)} experiments")
    for experiment_id in deletions:
        print(f"Deleting {experiment_id}")
        experiment = det_client.get_experiment(experiment_id)

        try:
            # experiment.delete()
            # time.sleep(120)
            max_deletions -= 1
        except Exception as exc:  # pylint: disable=broad-except
            print(f"Failed to delete {experiment_id}: {exc}")
        if max_deletions == 0:
            print("Throttling deletions")
            sys.exit(0)

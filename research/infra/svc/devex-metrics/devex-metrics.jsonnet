local lib = import '../../cfg/clusters/base-resources/tmpl/lib.jsonnet';
local clusters = import '../../cfg/clusters/clusters.jsonnet';
local vendor = {
  '16.3.4': importbin 'postgresql-16.3.4.tgz',
};

function(
  cluster='gcp-core0',
  name='devex-metrics',
  namespace='devex-metrics',
  version='16.3.4',

  db_name='metrics',
  user_name='augment',
  user_pw='AgBzoEWbjVAkoyCmG/KyGyVDejhimExsbzYanBRBid8J2pOautEj1Jiux/xmyG3rGcOAQVqbiclArh9XyEwL/s1Q/wrhws8cRhXgVSksIUnuvwvJWr703tpH/cary1/OEpaUIlXj68H85rFpqpfhZw+wQ0xgxKERtuV6Ep/u8mFfHgs6zfRlF3XmFczCS4sgYOMhA5kzcJEMTqZmWC/RdFlJmp9SvL06WBApr7QlxEqMPhtTwOv7pyDEgfE65v0NGtgKgQk7VmsBKqVaBVJ8aX+9wQPmNfaKui65ZyM9vyiKNGMgFnFRzPJVwpUoAParaZs8dRNXXJYfp8mDfuXda+Sjp7OQoe3aHKsgowVks1sGBk6WXOwOO7Emthd1OnEY5Z6OB8p0iXnpH6/aBQ+7p5mPqOuYGDyqwk3IlSvIQf2/2iiy3P2thsDuoj7Pdn22DFEW06Hn0IQqL5+g6w95FEPwFz236NxT/Q72qQ50exc/TUCFG0ovxUtBDCyWMcG8Jk6doyQvJ4w87LZGXUCchXkPPBHB9Fk32qkpoFjpuB2pmAwV41YMp/YK3Sl/lATlZ/sbcWuoSrSE7pyJs21KLXO34OJ+kA8QyxCUZdkh4Qbvas692NZ04GqtK29inkL9kSMZ1p4spfzcxrs5Ewd3vW8rokdsS1iGswNAFg1YBbD4AjkosvZTeREsCPCpIk0GkXIxY8Kd1GVkSwad/9il/kTOvRstP0vZSwFOj3LLLA==',

  resources_preset='small',
  storage_class='premium-rwo',
  storage_size='8Gi',  // the default, we were at 2+Gi on CoreWeave
  domain='r.augmentcode.com',
  cluster_issuer='letsencrypt-prod',

) local C = clusters.cluster(cluster); C.k8s + {

  hostname:: name + '.' + domain,

  ns: $.Namespace + {
    name:: namespace,
  },

  Object+:: {
    name:: name,
    namespace:: $.ns.metadata.name,
  },

  tls: $.Certificate + {
    spec+: {
      secretName+: '-tls',
      secretTemplate+: {
        labels+: {
          'aug.service': name,
          'aug.cluster': C.name,
        },
      },
      dnsNames: [$.hostname],
      issuerRef+: {
        kind: self.CLUSTER_ISSUER,
        name: cluster_issuer,
      },
      usages: [
        'digital signature',
        'key encipherment',
      ],
    },
  },

  auth: $.SealedSecret + {
    name+:: '-auth',
    encryptedData: {
      user_pw: user_pw,
      postgres_pw: self.user_pw,  // Use the same pw, but this user is local-only and local is trusted by pg_hba.conf.
    },
  },

  release: lib.ParseHelmChart(vendor[version], name, namespace, values={
    commonLabels+: {
      'aug.service': name,
      'aug.cluster': C.name,
    },
    tls+: {
      enabled: true,
      certificatesSecret: $.tls.spec.secretName,
      certFilename: 'tls.crt',
      certKeyFilename: 'tls.key',
    },
    auth+: {
      enablePostgresUser: true,
      database: db_name,
      username: user_name,
      existingSecret: $.auth.name,
      secretKeys+: {
        adminPasswordKey: 'postgres_pw',  // pragma: allowlist secret
        userPasswordKey: 'user_pw',  // pragma: allowlist secret
      },
    },
    primary+: {
      service+: {
        type: 'LoadBalancer',
        externalTrafficPolicy: 'Local',
        annotations+: {
          'external-dns.alpha.kubernetes.io/hostname': $.hostname,
        },
      },
      resourcesPreset: resources_preset,
      persistence+: {
        enabled: true,
        storageClass: storage_class,
        size: storage_size,
      },
      extendedConfiguration: |||
        log_statement = 'none'  # none, ddl, mod, all
      |||,
      pgHbaConfiguration: |||
        # Trust everything local (needed for initial user creation).
        local    all  all                trust
        host     all  all  ::1/128       trust
        hostssl  all  all  ::1/128       trust
        host     all  all  127.0.0.1/32  trust
        hostssl  all  all  127.0.0.1/32  trust

        # Remote auth.
        hostssl %s %s all md5

        # Uncomment to enable remote administration.
        # hostssl all postgres all md5
      ||| % [db_name, user_name],
    },
    rbac+: {
      create: true,
    },
    volumePermissions+: {
      enabled: true,
    },
  }),
}

package main

import (
	"encoding/gob"
	"fmt"
	"strings"
	"time"

	"github.com/markbates/goth"
)

func init() {
	gob.Register(User{})
}

type User struct {
	Provider    string
	Email       string
	Username    string
	Domain      string
	Name        string
	FirstName   string
	LastName    string
	NickName    string
	Description string
	ID          string
	AvatarURL   string
	Location    string
	ExpiresAt   time.Time
}

type ExtendedUser struct {
	User
	AuthorizedKeys string
	GitHubUserName string
}

func NewUser(gu goth.User) User {
	parts := strings.Split(gu.Email, "@")
	username, domain := parts[0], ""
	if len(parts) > 1 {
		username, domain = strings.Join(parts[:len(parts)-1], "@"), parts[len(parts)-1]
	}

	return User{
		Provider:    gu.Provider,
		Email:       gu.Email,
		Username:    username,
		Domain:      domain,
		Name:        gu.Name,
		FirstName:   gu.FirstName,
		LastName:    gu.LastName,
		NickName:    gu.NickName,
		Description: gu.Description,
		ID:          gu.UserID,
		AvatarURL:   gu.AvatarURL,
		Location:    gu.Location,
		ExpiresAt:   gu.ExpiresAt,
	}
}

func (u User) FriendlyName() string {
	return u.Username
}

func (u User) IsValid(domain string) error {
	if u.Email == "" {
		return fmt.Errorf("user invalid: empty email address")
	}
	if u.Expired() {
		return fmt.Errorf("user invalid: expired on %v", u.ExpiresAt)
	}
	if domain != "" && u.Domain != domain {
		return fmt.Errorf("user invalid: only the %s domain accepted, go %s", domain, u.Domain)
	}
	return nil
}

func (u User) ExpiresIn() time.Duration {
	return u.ExpiresAt.Sub(time.Now())
}

func (u User) Expired() bool {
	return time.Now().After(u.ExpiresAt)
}

func (u User) Format() string {
	items := []string{
		fmt.Sprintf("Provider: %s", u.Provider),
		fmt.Sprintf("Email: %s", u.Email),
		fmt.Sprintf("Username: %s", u.Username),
		fmt.Sprintf("Domain: %s", u.Domain),
		fmt.Sprintf("Name: %s", u.Name),
		fmt.Sprintf("FirstName: %s", u.FirstName),
		fmt.Sprintf("LastName: %s", u.LastName),
		fmt.Sprintf("NickName: %s", u.NickName),
		fmt.Sprintf("Description: %s", u.Description),
		fmt.Sprintf("Location: %s", u.Location),
		fmt.Sprintf("AvatarURL: %s", u.AvatarURL),
		fmt.Sprintf("Expires: %v (in %v)", u.ExpiresAt, u.ExpiresIn()),
		fmt.Sprintf("IsValid?: %t", u.IsValid("")),
	}
	return strings.Join(items, "\n") + "\n"
}

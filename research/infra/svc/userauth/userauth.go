package main

import (
	"context"
	"flag"
	"fmt"
	"os"

	"github.com/augmentcode/augment/infra/lib/logger"
	"github.com/augmentcode/augment/research/infra/lib/augment"
)

func main() {
	ctx := context.Background()
	svc := Service{Logger: logger.New(nil)}

	flag.IntVar(&svc.Port, "port", 8080, "Listening port.")
	flag.StringVar(&svc.Hostname, "hostname", "", "Callback hostname.")

	flag.StringVar(&svc.AuthorizedKeysConfigMap, "authorized-keys-configmap", augment.AuthorizedKeysConfigMap, "Name of ConfigMap holding SSH Authorized Keys.")
	flag.StringVar(&svc.GitHubUserNameConfigMap, "github-username-configmap", augment.GitHubUserNameConfigMap, "Name of ConfigMap holding GitHub User Names.")

	flag.StringVar(&svc.OauthLimitDomain, "oauth-limit-domain", os.Getenv("OAUTH_LIMIT_DOMAIN"), "Defaults to the OAUTH_LIMIT_DOMAIN env var, used to pin oauth to a single domain.")
	flag.StringVar(&svc.OauthGoogleClientID, "oauth-google-client-id", os.Getenv("OAUTH_GOOGLE_CLIENT_ID"), "Defaults to OAUTH_GOOGLE_CLIENT_ID env var, can be a literal string or path if starts with '/' or './'.")
	flag.StringVar(&svc.OauthGoogleClientSec, "oauth-google-client-secret", os.Getenv("OAUTH_GOOGLE_CLIENT_SECRET"), "Defaults to OAUTH_GOOGLE_CLIENT_SECRET env var, can be a literal string or path if starts with '/' or './'.")
	flag.StringVar(&svc.CookieStoreSessionSec, "cookie-store-session-secret", os.Getenv("COOKIE_STORE_SESSION_SECRET"), "Defaults to COOKIE_STORE_SESSION_SECRET env var, can be a literal string or path if starts with '/' or './'.")
	flag.StringVar(&svc.CookieStoreSessionName, "cookie-store-session-name", "_aug_userauth", "Name of cookie.")

	flag.Parse()

	if err := svc.Run(ctx); err != nil {
		fmt.Fprintln(os.Stderr, err)
		os.Exit(1)
	}
}

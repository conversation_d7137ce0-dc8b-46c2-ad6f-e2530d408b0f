NAME    := userauth
VERSION := $(shell date +"%Y-%m-%d_%H-%M-%S")
CLUSTER := gcp-core0

REGISTRY := $(shell jsonnet -A name="$(CLUSTER)" -A prop=registry -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
IMAGE    := $(REGISTRY)/$(NAME):$(VERSION)

CONTEXT  := $(shell jsonnet -A name="$(CLUSTER)" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
K8S_CONF := $(NAME).jsonnet
KUBECTL  := kubectl --context="$(CONTEXT)"
KUBECFG  := kubecfg --context="$(CONTEXT)" \
	   --tla-code="cluster='$(CLUSTER)'" \
	   --tla-code="name='$(NAME)'" \
	   --tla-code="version='$(VERSION)'" \
	   --tla-code="image='$(IMAGE)'"

build:
	bazel build :$(NAME)
	docker build --pull --build-context=bin=$$(bazel info bazel-bin)/research/infra/svc/$(NAME)/$(NAME)_/ -t $(IMAGE) .

push: build
	docker push $(IMAGE)

show:
	$(KUBECFG) show $(K8S_CONF)

diff:
	-$(KUBECFG) diff --diff-strategy=subset $(K8S_CONF)

apply:
	$(KUBECFG) update $(K8S_CONF)

logs:
	$(KUBECTL) logs -l aug.service=$(NAME) $(LOGS_EXTRA)

list:
	$(KUBECTL) get -Al aug.service=$(NAME) deploy,pod,svc,ing,pvc,role,rolebinding,sa,cm,secret,sealedsecret

deploy: diff push apply

local clusters = import '../../cfg/clusters/clusters.jsonnet';
local aug = import '../../cfg/lib/augment-enums.jsonnet';

function(
  cluster='gcp-core0',
  name='userauth',
  namespace=null,
  version='latest',
  image=null,
  port=8080,
  replicas=3,
  cpu='1',
  ram='2G',
  ssh_authorizedkeys_configmap=aug.CW_AUTHORIZED_KEYS,
  github_username_configmap='augment-user-github-usernames',
) local C = clusters.cluster(cluster); C.k8s + {

  hostname:: name + '.r.augmentcode.com',

  BaseLabels+:: std.prune({
    'aug.service': name,
    'aug.version': version,
  }),

  Object+:: {
    name:: name,
    namespace:: C.sys_namespace,
  },

  gh_cm: $.ConfigMap + {
    name:: github_username_configmap,
  },

  keys_cm: $.ConfigMap + {
    name:: ssh_authorizedkeys_configmap,
  },

  deployment: $.Deployment + {
    spec+: {
      replicas: replicas,
      template+: {
        spec+: {
          local pod = self,
          serviceAccountName: $.sa.name,
          containers: [
            $.Container + {
              name: name,
              image: image,
              resources+: {
                limits+: {
                  cpu: cpu,
                  memory: ram,
                },
              },
              args: [
                '--port=' + port,
                '--hostname=' + $.hostname,
                '--authorized-keys-configmap=' + ssh_authorizedkeys_configmap,
                '--github-username-configmap=' + github_username_configmap,
                '--oauth-limit-domain=augmentcode.com',
                '--oauth-google-client-id=/run/secrets/userauth/oauth-google-client-id',
                '--oauth-google-client-secret=/run/secrets/userauth/oauth-google-client-secret',
                '--cookie-store-session-secret=/run/secrets/userauth/cookie-store-session-secret',
              ],
              volumeMounts: pod.volmount_mounts,
            },
          ],
          tolerations+: [
            {
              effect: 'NoSchedule',
              key: 'r.augmentcode.com/pool-type',
              value: 'svc',
            },
            {
              effect: 'PreferNoSchedule',
              key: 'r.augmentcode.com/pool-type',
              value: 'svc',
            },
          ],
          volmounts:: [
            {
              name:: 'secrets',
              volume:: {
                secret: {
                  secretName: $.secret.spec.template.metadata.name,
                  defaultMode: std.parseOctal('0444'),
                },
              },
              mount:: {
                mountPath: '/run/secrets/userauth',
                readOnly: true,
              },
            },
          ],
        },
      },
    },
  },

  service: $.Service + {
    spec+: {
      selector+: {
        'k8s.deployment': $.deployment.name,
      },
      type: $.SERVICE_TYPE.CLUSTER_IP,
      ports: [
        {
          name: 'http',
          port: port,
          targetPort: port,
        },
      ],
    },
  },

  ingress: $.Ingress + {
    local ing = self,
    metadata+: {
      annotations+: {
        'cert-manager.io/cluster-issuer': 'letsencrypt-prod',
        'nginx.ingress.kubernetes.io/proxy-buffer-size': '16k',  // default is 4k, more needed for oauth2 callback headers
      },
    },
    spec+: {
      ingressClassName: 'nginx',
      rules: [
        {
          host: $.hostname,
          http: {
            paths: [{
              path: '/',
              pathType: 'Prefix',
              backend: {
                service: {
                  name: $.service.metadata.name,
                  port: {
                    number: port,
                  },
                },
              },
            }],
          },
        },
      ],
      tls: [{
        hosts: [$.hostname],
        secretName: ing.metadata.name + '-tls',
      }],
    },
  },

  sa: $.ServiceAccount + {},

  rolebinding: $.RoleBinding + {
    role_name:: $.role.name,
    sas:: [$.sa],
  },

  role: $.Role + {
    rules: [
      {
        apiGroups: [''],
        resources: ['configmaps'],
        resourceNames: [ssh_authorizedkeys_configmap, github_username_configmap],
        verbs: $.READ_VERBS + $.WRITE_VERBS_NO_COLLECTION,
      },
    ],
  },

  secret: $.SealedSecret + {
    encryptedData: {
      'oauth-google-client-secret': 'AgCQIengQgMy+Nt1qLA+B+jEQ/CtnO2vYNz/R8ksZzQ3mdGE5p5AEDG6srSNWrx80lOQFzwW5eF1T9HUrprC5ldI+4h7gQuHvoH5+BpiLZizT3GL172u/wMbVmGZWJFjy9C+FJ/gXpzgadFv73frw7q/Y/NnhbIs8JREoRCHRDx3hHoU/8bsj337TiK+BCpsA5iVchGeEjQ6k9C46pmylIeK5Ptt7f5bu5EsBmopvxSbiCcwDGMH5s0m25Rbwc9FZ6pGmDPJvuZ+AU+lcGjGLzz61DHirofWb+90kC4ZkMWwwxU7slhIf64s+m9nlK0GmPbywrGXLbkoUZpQebGwSDARmwi0Lzo4icLLOVwPlObjfrJ35mshMVRbUlfrBM4w1XI8xNyPFbdNjLe53jd49Y7IiMI8tm7hj9J5oEuGLL1f64gKRrMiBZlyfSyRmeCjO05fhkWD9iDuxA6/wtahh4L9FkZ41VWrqyJE5polfcAlh4yS2GwvOX5NLpoQRXPiHYL53JLb0EnFncq8It2sXzePoz7eUZm94p9zmpvcXIMkBJmjbpAomcuzMutKAce3uFCqprjZ3A3mgJd4agTCASVgyWbkfI11feqY3tbGa9tARB33xa1c48sHE/1GF+XlSecNQ55L9o5Qv2PamDcvbT3WNo8AOyF501r7HY6EMNVF3MEvbZ9UC8A7q2Iv4hb2sc3FUi7qUnw136pYEdf2cVAJlU5vS+BwUvK5FtqBnwrPRZl4rQ==',  // pragma: allowlist secret
      'cookie-store-session-secret': 'AgCT00LPoiSFXILGv6lHjSdtVlJdSpYWpOOhmu5+GDFpfIPrRxmXgFPtZR/Kf+5oelGcxthCL/+30KZKoWLedPjGJ+RzlhqV2akYO+00z1W3t4AodiEhSj5tQVQN0CNMPsR26k+38/FdJqgsZm9oYogpKa0jz0N5RLETLWd++Lt+nFUzvEbD8k5wX/l+MZPLsuz+4mal4QfNgBBwri6WsEX0Fifvb2HX9O0YVkmUV+2CCUsSSQ+EM2lAPpTZunNNboHZ13/wOx9wCj5QzdO3vZAqyIx7cKRs7G5KZ/XrMnMdAzgHNOn8yG556qMgXul7Wtqq9gnqJSWB+OeRlq4F3KGfj9sCLy0FUkV1s292NexSchWJxPn5qt6Q9zv/9mMXG4Dl0cDveDGrWm9DmsvUcQXm8cH52VBBphznZmvcHnA0K38z12G7fGy/VQ3XECX3pEOOTckoFgVlerHEpE6TKiOd11nRqmnnHP0nrKe94PIimJg64szyxtFFulb92jz1DO5B+BRGClQ/JqoD1JEtqtS5bns+C4ZlYefWmAUHP2WZkp+qBEF3LiVux2mK48p2ws4Q54dBBxfrbf0rYodOne48UfodE0u9Tl8iWsqYkmwG6VFY/ZTAeT4FXucAEU+mFKH09/+/+uU6PZa75FoB5qbUTFYeZpCjAlP1QCUgRFF44v8LIsHJh9C+XfUJ8+cJlyrTuF2gZnR5uWOO8lO0TjWFJfTBhdh9TZm5IRJuF4DMSg==',  // pragma: allowlist secret
    },
    spec+: {
      template+: {
        data+: {
          'oauth-google-client-id': '670297456363-80lpocjp81r9o8dfc8qr4076538dmqm8.apps.googleusercontent.com',
        },
      },
    },
  },
}

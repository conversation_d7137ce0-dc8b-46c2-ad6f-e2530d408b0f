<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="icon" type="image/x-icon" href="/favicon.ico" />
  <style type="text/css">
    body {
      background-color: Canvas;
      color: CanvasText;
      color-scheme: light dark;
      display: flex;
      flex-direction: column;
      max-width: 800px;
      margin: auto;
    }

      div#banner {
        flex: 50px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
      }
        div#banner div#banner-left {
          text-align: left;
        }
          div#banner div#banner-left #banner-title {
          }
        div#banner div#banner-right {
          text-align: right;
        }
          div#banner div#banner-right .avatar {
            max-height: 25px;
            width: auto;
            height: auto;
          }
          div#banner div#banner-right a.button {
            padding: 1px 6px;
            border: 1px outset buttonborder;
            border-radius: 3px;
            color: buttontext;
            background-color: buttonface;
            text-decoration: none;
          }

      div#error-bar:not(:empty) {
        padding: 1em;
        text-align: left;
        background-color: #f2476a;
        display: initial;
      };
      div#error-bar:empty {
        display: none;
      };

      div#main {
        display: flex;
        flex-direction: column;
      }
        div#main div.main-section {}
        div#main div.main-section div.main-section-content {
          padding-left: 2em;
          padding-bottom: 1em;
        }
        div#main div.main-section .autotextarea {
          width: 100%;
          display: block;
          overflow: hidden;
          resize: none;
        }
        div#main div.main-section div.button-warn {
          color: orange;
          display: none;
        }
    h1 {
      font-size: 1.5em;
      font-weight: bold;
    }
    h1 #favicon {
      max-height: 1.5em;
      width: auto;
      height: auto;
    }
    h2 {
      font-size: 1.1em;
      font-weight: bold;
    }
    .code {
      font-family: monospace;
    }
    .augi {
      font-family: monospace;
    }
    pre.ssh-config {
      background-color: gray;
    }
  </style>

  <title>Augment UserAuth Console</title>
</head>

<body>
  <div id="banner">
    <div id="banner-left">
      <h1>
        <img src="/favicon.ico" alt="Augment Favicon" id="favicon" />
        Augment UserAuth Console
      </h1>
    </div>
    <div id="banner-right">
      <span id="banner-email">{{.User.Email}}</span>
      {{if .User.AvatarURL}}
      <img src="{{.User.AvatarURL}}" alt="avatar image" class="avatar"/>
      {{end}}
      <!-- Hide logout button for now, the user is logged right back in -->
      <!-- <a class="button" href="/logout/{{.User.Provider}}">Logout</a> -->
    </div>
  </div>
  <div id="error-bar">{{if .Error}}<span><b>NOTE:</b> {{.Error}}</span>{{end}}</div>
  <div id="main">
    <form id="main-form" action="javascript:void(0);">
    <div class="main-section">
      <h2>Option 1: From GitHub</h2>
      <div class="main-section-content">
        <label for="github_username">GitHub Username</label>:
        <input type="textbox" id="github_username" value="{{.User.GitHubUserName}}" />
        <button onclick="submitAppendFromGitHub();" class="button-gh" disabled>Append From GitHub</button>
        <button onclick="submitReplaceFromGitHub();" class="button-gh" disabled>Replace From GitHub</button>
      </div>
      <div class="main-section-content button-warn" id="button-gh-disabled-warn">
        <span>The GitHub options are disabled because the keys have been changed manually.</span>
      </div>
    </div>

    <div class="main-section">
      <h2>Option 2: Direct Edit</h2>
      <div class="main-section-content">
        <button onclick="submitApply();" class="button-apply" disabled>Apply</button>
      </div>
      <div class="main-section-content button-warn" id="button-apply-disabled-warn">
        <span>There's nothing to apply yet.</span>
      </div>
      <div class="main-section-content">
        <textarea class="autotextarea" id="authorized_keys">{{.User.AuthorizedKeys}}</textarea>
      </div>
    </div>
    </form>

    <div class="main-section">
      <h2>README</h2>
      <div class="main-section-content">
        <p>
          <b>TL;DR;</b> Register and edit your <b>SSH Public Keys</b> for the
          <b>Augment Research</b> environment(s).
        </p>
        <p>
          If you have already done similar in GitHub, you can copy your keys from there. Otherwise,
          use the textbox above to copy+paste or edit your keys directly. Additional documentation
          is available in notion:
          <ul>
            <li>
              <a href="https://www.notion.so/Augment-Research-Infra-Quick-Start-f43f2c6062d84eb1a5e746c072a7b2f8">
                🚀 Augment Research Infra - Quick Start
              </a>
            </li>
            <li>
              <a href="https://www.notion.so/Research-DevPods-e14fee75d32d46c686b52733375e665a">
                💻 Augment Research DevPods
              </a>
            </li>
            <li>
              <a href="https://www.notion.so/Augment-Research-SSH-cbdbe3b171f44473a3aa995aab128145#a7bbab29b2774d78b3358f672d99231c">
                🔑 Augment Research - SSH (User Guide)
              </a>
            </li>
          </ul>
        </p>
        <h3>SSH DevPod Details</h3>
        <p>
          When you create a DevPod with <span class="code">augi devpod create</span>, or view its details with
          <span class="code">augi devpod status</span>, an example SSH command is provided with hostname or IP, username,
          port, etc. In general, they will be:
          <ul>
            <li><b>HostName:</b> {{.User.Username}}-NAME.DOMAIN where NAME is your DevPod name and
              DOMAIN is specific to the cluster, <i>e.g.,</i> {{.User.Username}}-homepod.gcp-us1.augmentcode.com</li>
            <li><b>Port:</b> 22022</li>
            <li><b>User:</b> augment</li>
          </ul>
        </p>
        <h3>Per-pod SSH Config Stanza</h3>
        <p>
          Following from the above, you can define an SSH Host stanza for your pod. <i>NOTE: See below for
          a dynamic configuration which won't require a separate stanza for each DevPod.</i>

<pre class="ssh-config">
Host {{.User.Username}}-NAME
  HostName {{.User.Username}}-NAME.gcp-us1.r.augmentcode.com
  Port     22022
  User     augment
</pre>
        </p>
        <h3>Dynamic Prefix-based SSH Config (recommended)</h3>
        <p>
          The downside to the above option is that you need to update your SSH Config on all of your homedirs and company laptop each
          time you create a new pod. Instead, consider adopting the setup below. First, a generic <span class="code">~/.ssh/config</span>
          sets up <span class="code">~/.ssh/config/config.d/</span> snippets; second, <span class="code">~/.ssh/config/config.d/30-augment.conf</span>
          sets up a snippet that applies to ALL hostnames matching <span class="code">{{.User.Username}}-*</span>.
<pre class="ssh-config">
# $HOME/.ssh/config

Include config.d/*.conf

# Put additional config here if you must, but prefer snippet files in config.d/*.conf.

Host *
  ForwardX11 no
  ForwardAgent yes
  StrictHostKeyChecking no
  NoHostAuthenticationForLocalhost yes
</pre>

<pre class="ssh-config">
# $HOME/.ssh/config.d/30-augment.conf

# The "augi-console" is a simple, ephemeral host with the `augi` CLI available.
Host augi-console*
  User {{.User.Username}}
Host augi-console
  HostName augi-console.gcp-us1.r.augmentcode.com

Host {{.User.Username}}-*
  CanonicalizeHostname yes
  CanonicalDomains gcp-us1.r.augmentcode.com cw-east4.r.augmentcode.com

Match host *.r.augmentcode.com
  Port 22022
  User augment
  TCPKeepAlive no
  ServerAliveInterval 60
</pre>
        </p>
        <p>
          You should now be able to <span class="code">ssh {{.User.Username}}-NAME</span>, use
          with VSCode, etc for any NAME without needing to update configs as
          <span class="code">augi devpod</span> environments are created / modified / deleted.
        </p>
        <p>
          The above configuration also works in your dotfiles / (persistent) home directories for
          hopping from environment to environment.
        </p>
      </div>
    </div>
  </div>

  <script type="text/javascript">

    var origKeys = "{{.User.AuthorizedKeys}}";

    function updateAutoHeights() {
      document.querySelectorAll(".autotextarea").forEach((el) => {
          el.style.height = 'auto';
          el.style.height = el.scrollHeight + 'px';
      });
    }
    updateAutoHeights();

    document.querySelectorAll(".autotextarea").forEach((el) => {
        el.addEventListener("input", updateAutoHeights);
        el.addEventListener("input", updateButtons);
    });

    function setError(text) {
          if (text == "") {
                document.querySelector("div#error-bar").innerHTML = null;
          } else {
                document.querySelector("div#error-bar").innerHTML = `<span><b>ERROR:</b> ${text}</span>`;
          }
    }

    function setKeys(text) {
          origKeys = text
          document.getElementById("authorized_keys").value = text;
          updateAutoHeights();
          updateButtons();
    }

    function updateButtons() {
          const cur = document.getElementById("authorized_keys").value;
          const changed = (cur != origKeys);
          document.querySelectorAll("button.button-gh").forEach((el) => {
                el.disabled = changed;
          })
          document.querySelectorAll("button.button-apply").forEach((el) => {
                el.disabled = !changed;
          })
    }
    updateButtons();

    document.querySelectorAll("button.button-gh").forEach((el) => {
          el.addEventListener("mouseover", (event) => {
                if (el.disabled) {
                      document.getElementById("button-gh-disabled-warn").style.display = "initial";
                }
          });
          el.addEventListener("mouseout", (event) => {
                document.getElementById("button-gh-disabled-warn").style.display = "none";
          });
    });
    document.querySelectorAll("button.button-apply").forEach((el) => {
          el.addEventListener("mouseover", (event) => {
                if (el.disabled) {
                      document.getElementById("button-apply-disabled-warn").style.display = "initial";
                }
          });
          el.addEventListener("mouseout", (event) => {
                document.getElementById("button-apply-disabled-warn").style.display = "none";
          });
    });

    async function submitApply() {
        const val = document.getElementById("authorized_keys").value;
        try {
          const resp = await fetch("/api/authorized_keys", {
                method: "PUT",
                body: val,
          });
          const text = await resp.text();
          if (!resp.ok) {
                throw new Error(text);
          }
          setKeys(text);
          setError("");
        } catch (error) {
          setError(error);
        }
    }

    async function submitAppendFromGitHub() {
        const val = document.getElementById("github_username").value;
        try {
          const resp = await fetch(`/api/authorized_keys/append_from_github?github_username=${val}`, {
                method: "POST",
                body: val,
          });
          const text = await resp.text();
          if (!resp.ok) {
                throw new Error(text);
          }
          setKeys(text);
          setError("");
        } catch (error) {
          setError(error);
        }
    }

    async function submitReplaceFromGitHub() {
        const val = document.getElementById("github_username").value;
        try {
          const resp = await fetch(`/api/authorized_keys/set_from_github?github_username=${val}`, {
                method: "POST",
                body: val,
          });
          const text = await resp.text();
          if (!resp.ok) {
                throw new Error(text);
          }
          setKeys(text);
          setError("");
        } catch (error) {
          setError(error);
        }
    }
  </script>

</body>
</html>

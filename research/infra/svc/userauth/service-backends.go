package main

import (
	"context"

	"golang.org/x/sync/errgroup"

	"github.com/augmentcode/augment/research/infra/lib/augment"
)

////////////////////////////////////////////////////////////////////////////////
//
// READ
//

func (svc Service) authorizedKeys(ctx context.Context, user string) (string, error) {
	return svc.cli.AuthorizedKeys(ctx, user, augment.OptFromK8s())
}

func (svc Service) githubUsername(ctx context.Context, user string) (string, error) {
	return svc.cli.GitHubUserName(ctx, user)
}

func (svc Service) extendedUser(ctx context.Context, gu User) (ExtendedUser, error) {
	ext := ExtendedUser{User: gu}

	grp, gctx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		var err error
		ext.AuthorizedKeys, err = svc.authorizedKeys(gctx, ext.Username)
		return err
	})
	grp.Go(func() error {
		var err error
		ext.GitHubUserName, err = svc.githubUsername(gctx, ext.Username)
		return err
	})
	if err := grp.Wait(); err != nil {
		return ExtendedUser{}, err
	}

	return ext, nil
}

////////////////////////////////////////////////////////////////////////////////
//
// WRITE
//

func (svc Service) setAuthorizedKeys(ctx context.Context, user, keys string) (string, error) {
	return svc.cli.AuthorizedKeysSet(ctx, user, augment.OptFromBuf(keys))
}

func (svc Service) setAuthorizedKeysFromGH(ctx context.Context, user, ghuser string) (string, error) {
	return svc.cli.AuthorizedKeysSet(ctx, user,
		augment.OptFromGitHub(true),
		augment.OptGitHubUser(ghuser),
		augment.OptAppend(false),
	)
}

func (svc Service) appendAuthorizedKeysFromGH(ctx context.Context, user, ghuser string) (string, error) {
	return svc.cli.AuthorizedKeysSet(ctx, user,
		augment.OptFromGitHub(true),
		augment.OptGitHubUser(ghuser),
		augment.OptAppend(true),
	)
}

func (svc Service) setGithubUsername(ctx context.Context, user, ghuser string) error {
	return svc.cli.GitHubUserNameSet(ctx, user, ghuser)
}

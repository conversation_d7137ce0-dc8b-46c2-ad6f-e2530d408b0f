package main

import (
	"fmt"
	"net/http"
	"os"

	"github.com/gorilla/sessions"
	"github.com/markbates/goth/gothic"
)

////////////////////////////////////////////////////////////////////////////////
//
// <PERSON>ie <PERSON>
//

func (svc Service) session(r *http.Request) (*sessions.Session, error) {
	return svc.store.Get(r, svc.CookieStoreSessionName)
}

func (svc Service) getUser(r *http.Request) (User, error) {
	sess, err := svc.session(r)
	if err != nil {
		return User{}, err
	}

	a, ok := sess.Values["user"]
	if !ok {
		return User{}, nil
	}

	user, ok := a.(User)
	if !ok {
		fmt.Fprintf(os.Stderr, "error asserting User: %+v", a)
		return User{}, nil
	}

	return user, nil
}

func (svc Service) saveUser(w http.ResponseWriter, r *http.Request, user User) error {
	sess, err := svc.session(r)
	if err != nil {
		return err
	}
	sess.Values["user"] = user
	return sess.Save(r, w)
}

func (svc Service) logoutUser(w http.ResponseWriter, r *http.Request) error {
	sess, err := svc.session(r)
	if err != nil {
		return err
	}
	sess.Options.MaxAge = -1
	sess.Values = map[any]any{}
	return sess.Save(r, w)
}

////////////////////////////////////////////////////////////////////////////////
//
// OAuth2 Handlers
//

func (svc Service) hAuthCallback(w http.ResponseWriter, r *http.Request) {
	gu, err := gothic.CompleteUserAuth(w, r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	user := NewUser(gu)
	if err := user.IsValid(svc.OauthLimitDomain); err != nil {
		http.Error(w, err.Error(), http.StatusUnauthorized)
		return
	}
	if err := svc.saveUser(w, r, user); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	http.Redirect(w, r, "/", http.StatusTemporaryRedirect)
}

func (svc Service) hAuth(w http.ResponseWriter, r *http.Request) {
	gothic.BeginAuthHandler(w, r)
}

func (svc Service) hAuthLogout(w http.ResponseWriter, r *http.Request) {
	if err := svc.logoutUser(w, r); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	if err := gothic.Logout(w, r); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	http.Redirect(w, r, "/", http.StatusTemporaryRedirect)
}

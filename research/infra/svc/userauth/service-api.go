package main

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
)

func (svc Service) apiCaller(w http.ResponseWriter, r *http.Request) (User, bool) {
	if user, err := svc.getUser(r); err != nil {
		http.Error(w, err.Error(), http.StatusUnauthorized)
		return User{}, false

	} else if err := user.IsValid(svc.OauthLimitDomain); err != nil {
		http.Error(w, err.Error(), http.StatusUnauthorized)
		return User{}, false

	} else {
		return user, true
	}
}

func (svc Service) apiPlainText(w http.ResponseWriter, r *http.Request, f func(context.Context, User) (string, error)) {
	if user, ok := svc.apiCaller(w, r); !ok {
		return
	} else if text, err := f(r.Context(), user); err != nil {
		http.Error(w, err.<PERSON>r(), http.StatusInternalServerError)
		return

	} else {
		if _, err := w.Write([]byte(text)); err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		w.Header().Add("Content-Type", "text/plain; charset=utf-8")
	}
}

func (svc Service) apiJson(w http.ResponseWriter, r *http.Request, f func(context.Context, User) (any, error)) {
	if user, ok := svc.apiCaller(w, r); !ok {
		return
	} else if data, err := f(r.Context(), user); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return

	} else {
		enc := json.NewEncoder(w)
		enc.SetIndent("", "\t")
		if err := enc.Encode(data); err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		w.Header().Set("Content-Type", "application/json; charset=utf-8")
	}
}

func (svc Service) apiWrite(w http.ResponseWriter, r *http.Request, f func(context.Context, *http.Request, User) (string, error)) {
	if user, ok := svc.apiCaller(w, r); !ok {
		return
	} else if resp, err := f(r.Context(), r, user); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	} else if len(resp) > 0 {
		if _, err := w.Write([]byte(resp)); err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		w.Header().Add("Content-Type", "text/plain; charset=utf-8")
	}
}

////////////////////////////////////////////////////////////////////////////////
//
// READ
//

func (svc Service) apiGetAuthorizedKeys(w http.ResponseWriter, r *http.Request) {
	svc.apiPlainText(w, r, func(ctx context.Context, user User) (string, error) {
		return svc.authorizedKeys(ctx, user.Username)
	})
}

func (svc Service) apiGetGitHubUsername(w http.ResponseWriter, r *http.Request) {
	svc.apiPlainText(w, r, func(ctx context.Context, user User) (string, error) {
		return svc.githubUsername(ctx, user.Username)
	})
}

func (svc Service) apiGetUserDetails(w http.ResponseWriter, r *http.Request) {
	svc.apiJson(w, r, func(ctx context.Context, user User) (any, error) {
		if euser, err := svc.extendedUser(ctx, user); err != nil {
			return nil, err
		} else {
			resp := struct {
				Keys   string `json:"authorized_keys"`
				GHUser string `json:"github_username"`
			}{
				Keys:   euser.AuthorizedKeys,
				GHUser: euser.GitHubUserName,
			}
			return resp, nil
		}
	})
}

////////////////////////////////////////////////////////////////////////////////
//
// WRITE
//

func (svc Service) apiSetAuthorizedKeys(w http.ResponseWriter, r *http.Request) {
	svc.apiWrite(w, r, func(ctx context.Context, r *http.Request, user User) (string, error) {
		if buf, err := io.ReadAll(r.Body); err != nil {
			return "", err
		} else {
			return svc.setAuthorizedKeys(ctx, user.Username, string(buf))
		}
	})
}

func (svc Service) apiSetAuthorizedKeysFromGH(w http.ResponseWriter, r *http.Request) {
	svc.apiWrite(w, r, func(ctx context.Context, r *http.Request, user User) (string, error) {
		ghuser := r.URL.Query().Get("github_username")
		if ghuser == "" {
			var err error
			if ghuser, err = svc.githubUsername(ctx, user.Username); err != nil {
				return "", err
			}
		} else {
			if err := svc.setGithubUsername(ctx, user.Username, ghuser); err != nil {
				return "", err
			}
		}
		return svc.setAuthorizedKeysFromGH(ctx, user.Username, ghuser)
	})
}

func (svc Service) apiAppendAuthorizedKeysFromGH(w http.ResponseWriter, r *http.Request) {
	svc.apiWrite(w, r, func(ctx context.Context, r *http.Request, user User) (string, error) {
		ghuser := r.URL.Query().Get("github_username")
		if ghuser == "" {
			var err error
			if ghuser, err = svc.githubUsername(ctx, user.Username); err != nil {
				return "", err
			}
		} else {
			if err := svc.setGithubUsername(ctx, user.Username, ghuser); err != nil {
				return "", err
			}
		}
		return svc.appendAuthorizedKeysFromGH(ctx, user.Username, ghuser)
	})
}

func (svc Service) apiSetGitHubUsername(w http.ResponseWriter, r *http.Request) {
	svc.apiWrite(w, r, func(ctx context.Context, r *http.Request, user User) (string, error) {
		if buf, err := io.ReadAll(r.Body); err != nil {
			return "", err
		} else {
			return "", svc.setGithubUsername(ctx, user.Username, string(buf))
		}
	})
}

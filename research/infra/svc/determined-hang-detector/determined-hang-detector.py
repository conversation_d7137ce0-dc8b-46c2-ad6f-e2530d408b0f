#!/usr/bin/env python3
"""Find and kill hung experiments.

Experiments owned by the "github" user will be killed after 4 days.
Experiments owned by other users will trigger a warnings after 14 days.
"""

# pylint: disable=protected-access

import argparse
import os
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, NamedTuple

import humanfriendly
import pandas as pd
import requests
from determined.experimental import client as det_client
from kubernetes import client, config
from pathlib import Path

from kubernetes.client import V1PodList

config.load_config()
try:
    c = client.Configuration().get_default_copy()
except AttributeError:
    c = client.Configuration()
client.Configuration.set_default(c)
_v1_api = client.CoreV1Api()

SLACK_CHANNEL = "C051BBASJF4"  # #infra-alerts
gh_warn = pd.Timedelta("4D")
td_warn = pd.Timedelta("14D")
pod_warn = pd.Timedelta("12h")
# This runs on a python3.9 pod
now: pd.Timestamp = pd.to_datetime(datetime.utcnow(), utc=True)

offset: int = 0
limit: int = 100
kills: List[Dict[str, Any]] = []
warns: List[Dict[str, Any]] = []

det_url = os.getenv("DET_MASTER")
assert det_url, "DET_MASTER must be set in the environment"
det_user = Path("/run/augment/secrets/gh-determined-password/username").read_text()
det_pass = Path("/run/augment/secrets/gh-determined-password/password").read_text()
try:
    det_client.login(
        master=det_url,
        user=det_user,
        password=det_pass,
    )
except ValueError:
    ...


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser()
    parser.add_argument("--namespace", type=str, required=True)
    parser.add_argument("--kill", action="store_true")
    args = parser.parse_args()
    return args


def get_determined_data() -> Dict[str, Dict[str, Any]]:
    data: Dict[str, Dict[str, Any]] = {}
    offset: int = 0
    limit: int = 50
    while True:
        r: requests.Response = det_client._determined._session.get(  # type: ignore
            "/api/v1/experiments",
            params={"offset": offset, "limit": limit},
        )
        project: Dict[str, Any] = r.json()
        return_count: int = len(project["experiments"])
        for experiment in [
            e
            for e in project["experiments"]
            if e["state"] in ("STATE_QUEUED", "STATE_RUNNING")
        ]:
            age: pd.Timedelta = now - pd.to_datetime(experiment["startTime"])
            item: Dict[str, Any] = {
                "id": experiment["id"],
                "state": experiment["state"],
                "startTime": experiment["startTime"],
                "username": experiment["username"],
                "name": experiment["name"],
                "age": age,
            }
            data[experiment["id"]] = item
        if return_count < limit:
            return data
        offset += return_count


def process_hung_experiments(kill: bool = True) -> None:
    print("Processing hung experiments")
    data: Dict[str, Dict[str, Any]] = get_determined_data()
    print(f"Found {len(data)} experiments")
    for experiment in data.values():
        if experiment["age"] > gh_warn and experiment["username"] == "github":
            kills.append(experiment)
        elif experiment["age"] > td_warn:
            if kill:
                kills.append(experiment)
            else:
                warns.append(experiment)

    print(f"Found {len(kills) + len(warns)} experiments")
    kill_experiments(kills)
    warn_experiments(warns)


def kill_experiments(kills: List[Dict[str, Any]]) -> None:
    print(f"Killing {len(kills)} experiments")
    for experiment in kills:
        experiment_id: int = int(experiment["id"])
        state: str = experiment["state"]
        start_time: str = experiment["startTime"]
        username: str = experiment["username"]
        name: str = experiment["name"]
        experiment_obj: Any = det_client.get_experiment(experiment_id)
        url: str = f"{experiment_obj._session.master}/det/experiments/{experiment_id}"

        try:
            print(f"Killing {url} ({name}) {state} {start_time} {username}")
            experiment_obj.kill()
        except Exception as exc:  # pylint: disable=broad-except
            print(f"Failed to kill {experiment_id}: {exc}")


def post_slack(data: Dict[str, str]) -> None:
    print(data["text"])
    webhook_url: Optional[str] = os.getenv("SLACK_WEBHOOK_URL")
    assert webhook_url is not None, "Set the SLACK_WEBHOOK_URL variable"
    requests.post(
        webhook_url,
        json=data,
        headers={"Content-type": "application/json"},
        timeout=30,
    )


def warn_experiments(warns: List[Dict[str, Any]]) -> None:
    print(f"Warning {len(warns)} experiments")
    digests: List[str] = []
    current: List[str] = []
    for experiment in warns:
        experiment_id: int = int(experiment["id"])
        state: str = experiment["state"]
        start_time: str = experiment["startTime"]
        username: str = experiment["username"]
        name: str = experiment["name"]
        experiment_obj: Any = det_client.get_experiment(experiment_id)
        url: str = f"{experiment_obj._session.master}/det/experiments/{experiment_id}"
        txt: str = f"<{url}|Experiment {experiment_id}> ({name}) in {state} since {start_time} {username}"
        current.append(txt)
        if len(current) > 50:
            digests.append("\n".join(current))
            current = []

    if len(current):
        digests.append("\n".join(current))

    for txt in digests:
        # Notify slack
        data: Dict[str, str] = {
            "text": txt,
            "unfurl_links": "false",
        }
        post_slack(data)


class Pod(NamedTuple):
    pod_name: str
    pod_status: Any
    scheduled_time: datetime
    td: pd.Timedelta


def get_pending_pods(namespace: str) -> List[Pod]:
    pods: List[Any] = []
    pending: List[Pod] = []
    ct: str = ""
    while True:
        ret: V1PodList = _v1_api.list_namespaced_pod(
            namespace=namespace,
            limit=50,
            _continue=ct,
        )
        pods.extend(ret.items)  # type: ignore
        if ret.metadata._continue:  # type: ignore
            ct = ret.metadata._continue  # type: ignore
        else:
            break
    for pod in [p for p in pods if p.status.phase in ("Pending",)]:
        pod_name: str = pod.metadata.name
        pod_status: Any = pod.status
        scheduled_times: List[datetime] = [
            c.last_transition_time
            for c in pod_status.conditions
            if c.type == "PodScheduled"
        ]
        if scheduled_times == []:
            continue
        scheduled_time: datetime = scheduled_times[0]
        td = pd.Timedelta(
            seconds=time.time() - scheduled_time.timestamp(),
        )
        pending.append(Pod(pod_name, pod_status, scheduled_time, td))  # type: ignore
    return pending


def process_k8s_pods(namespace: str) -> None:
    print("Processing k8s pods")
    pending: List[Pod] = get_pending_pods(namespace)
    # print(pending)
    digests: List[str] = []
    current: List[str] = []
    for pod in pending:
        if pod.td > pod_warn:
            txt: str = f"Pod {pod.pod_name} has been pending for {humanfriendly.format_timespan(pod.td)}"
            current.append(txt)
            if len(current) > 50:
                digests.append("\n".join(current))
                current = []

    if len(current):
        digests.append("\n".join(current))

    for txt in digests:
        # Notify slack
        data: Dict[str, str] = {
            "text": txt,
            "unfurl_links": "false",
        }
        post_slack(data)


if __name__ == "__main__":
    args: argparse.Namespace = parse_args()
    process_k8s_pods(args.namespace)
    process_hung_experiments(args.kill)

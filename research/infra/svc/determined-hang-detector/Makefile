NAME    := determined-hang-detector
VERSION := 2024-10-08.1
CLUSTER := gcp-us1

CONTEXT  := $(shell jsonnet -A name="$(CLUSTER)" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
K8S_CONF := $(NAME).jsonnet
KUBECTL  := kubectl --context="$(CONTEXT)"
KUBECFG  := kubecfg --context="$(CONTEXT)" \
	   --tla-code="cluster_name='$(CLUSTER)'" \
	   --tla-code="job_inst='$(JOB_INST)'" \
	   --tla-code="version='$(VERSION)'"


show:
	$(KUBECFG) show $(K8S_CONF)

diff:
	-$(KUBECFG) diff --diff-strategy=subset $(K8S_CONF)

apply:
	$(KUBECFG) update $(K8S_CONF)

logs:
	$(KUBECTL) logs -l aug.service=$(NAME) $(LOGS_EXTRA)

list:
	$(KUBECTL) get -l aug.service=$(NAME) cronjob,job,deploy,pod,svc,ing,pvc,role,rolebinding,sa,cm,secret,sealedsecret

deploy: diff apply

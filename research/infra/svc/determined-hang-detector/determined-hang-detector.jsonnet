local clusters = import '../../cfg/clusters/clusters.jsonnet';

local hd_py = importstr 'determined-hang-detector.py';
local requirements = importstr 'requirements.txt';

function(cluster_name, version, job_inst='') local C = clusters.cluster(cluster_name); C.k8s + {
  by_cluster(obj)::
    if std.objectHas(obj, cluster_name)
    then obj[cluster_name]
    else std.get(obj, 'default'),

  filter_by_cluster(obj):: {
    [kv.key]: kv.value
    for kv in std.objectKeysValues(obj)
    if !std.isObject(kv.value) || std.member([null, cluster_name], std.get(kv.value, 'cluster_name'))
  },

  local as_job = job_inst != '',
  name:: 'determined-hang-detector',

  configmap: C.k8s.ConfigMap + {
    name:: $.name,
    data: {
      'determined-hang-detector.py': hd_py,
      'requirements.txt': requirements,
    },
  },
  jobspec:: {
    completions: 1,
    template+: {
      metadata+: {
        annotations+: {
          'config.md5': std.md5(std.manifestJson($.configmap.data)),
        },
      },
      spec+: {
        local pod = self,
        restartPolicy: 'Never',
        serviceAccountName: 'determined-service-account',
        containers+: [
          {
            name: $.name,
            image: 'python:3.11-slim',
            imagePullPolicy: 'IfNotPresent',
            env: [
              {
                name: 'DET_MASTER',
                value: C.determined_url,
              },
              {
                name: 'SLACK_WEBHOOK_URL',
                valueFrom: {
                  secretKeyRef: {
                    name: 'infra-alerts-webhook-url',
                    key: 'url',
                  },
                },
              },
            ],
            args: [
              '/bin/bash',
              '-c',
              'pip install -r /run/augment/' + $.name + '/requirements.txt && python3 /run/augment/' + $.name + '/' + $.name + '.py --namespace=' + C.name,
            ],
            volumeMounts: [
              {
                name: $.name,
                mountPath: '/run/augment/' + $.name,
              },
              {
                name: 'gh-determined-password',
                mountPath: '/run/augment/secrets/gh-determined-password',
                readOnly: true,
              },
            ],
          },
        ],
        volumes: [
          {
            name: $.name,
            configMap: {
              name: $.name,
            },
          },
          {
            name: 'gh-determined-password',
            secret: {
              secretName: 'gh-determined-password',  // pragma: allowlist secret
            },
          },
        ],
      },
    },
  },
  cronjob:: C.k8s.CronJob + {
    local cj = self,
    name:: $.name,
    schedule:: '@hourly',
    namespace:: C.name,
    concurrencyPolicy:: $.CRONJOB_POLICY.REPLACE,
    metadata+: {
      labels+: {
        'app.kubernetes.io/name': $.name,
        'aug.version': version,
      },
    },
    spec+: {
      jobTemplate+: {
        metadata+: {
          labels+: {
            'app.kubernetes.io/name': $.name,
            'aug.version': version,
          },
        },
        spec+: $.jobspec,
      },
    },
    podspec:: {},
  },
  job:: C.k8s.Job + {
    name: $.name + '-adhoc-' + job_inst,
    spec: $.jobspec,
    metadata+: {
      labels+: {
        'app.kubernetes.io/name': $.name,
        'aug.version': version,
      },
    },
  },

  cronjob_or_job: if as_job then $.job else $.cronjob,
}

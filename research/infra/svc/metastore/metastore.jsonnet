local lib = import '../../cfg/clusters/base-resources/tmpl/lib.jsonnet';
local clusters = import '../../cfg/clusters/clusters.jsonnet';
local vendor = {
  '16.3.4': importbin 'postgresql-16.3.4.tgz',
};

function(
  cluster='gcp-core0',
  name='metastore',
  namespace='metastore',
  version='16.3.4',

  db_name='metastore',
  postgres_pw='AgCnubBB1wcVzpUMWJEZi8Owp4Wqqzkn+wB3kB3j2I2DhHloqkg4NlIqSWVMgzYsbOd4b3Qh1+/8QAV/aVm2aOX++vI5DXPPVAPt4/LXrGIbnhX+h+HCddJuDAz3xXXdSVQb8g06T2F9piapWy4u3O+67i6kqeOzAZ3gg+hs6Niy9kB7deAq2hEs2nKNlcZ6nMhtv55gXVfe+9Pcc601sZ6aijK1m+jXaXErHEU6aR2snHYxxnSUrK1VkeUpkBCQPdxJ49gkIVmaToNkko6BBWiQsawAberZ1EWYgeG1XSPxoxU9KnArdeFKfOSmZqr/cWaOT2KjKCSAnJu6uPfEYjT6vozthKultdMX/WLvz2ryh9gqtUtLFfEMTNcZdA4STqpmhV0W6rrpaSq+sdFfmZNWY55PHDPOsPwUvRavblANd6JOXHuRdtKNU7lhdS1trK5ElYwukVb5eunQbB0toINdzNyK2KURJrJNI8ph6MImdlN1jynvM7GHZG0NkxxMeYsyvpyYzPbkyJri6JQVubrhoZCF4dlqgZ60Wv2shAaAWi6NZ1P6s6Vkd1rwDWXGQqxDvEk2twi/2yuVWJLyzi2hwPGJ+qhlaNZfd4wq+B5KIYw9QYreEb8ZGS3vyqOMZAO4J+tQAmqKFwx0BnYlxGZy3ni/LxjAy9owgeS2G6f/jFKZ6eD+gKq1OfEHrpgHfdGE0oBcJHYcTcG1',
  user_name='augment',
  user_pw='AgB8lkif06JkpCLz6PHf9UUGzM979TDK/M3xDB9H9jVNKYKc9f43u4O2IhyyF95Ithrl80ZviropTXwKulnGMobAILixTN1MEEtdWjF1TeGvekoi7WKGwhQH4IB1gCmMkx+IORJfW5wia1LLgorO3rEAZFxvkiry2PP7by4dnT6eIVnLVjg8Pw3W13/MC+k9JG0OR1bIx7O5AIYsGubVpMTpxqC3nszp6cPeHkdSOE+8ulV/NPjzUosicyIOXanlIstDZb0OvQbOfgq/FHvgBLlutxlcSJua/BTxMwABqa6qtO6YRz4VyuWDBdfBhfayj6W6puNTTOSLvrMOP2GqTxJZlJKZtrATmgC1QP2CFWK2muV5SH1Lx1G406EBJ09z5ObV9t4mcpsFys9q04jpwuPTPylqDVXWG5XZvcPy7VJWDVGj04BgsDiXJd6fD2NjClFNt4gxwfa3Ye2ap+3+Tw3WRH6UCdLa49OjJ37DyaSzVrYH+eKKCFoPO+OBE0n7/UhPuXl6vsWwTD+zogkGnfJNy6GPFDY+F+Llnuj/Gy1wInrk19oBK9qgGNYF2pngsqNh+eM9t1G/RbJKqlGj7R0h4H0reFVxZbDj7HiIrU253KC0apVwiPzGWhx6PbWTogvqA50NlpGTYTp+eStCPspBJ6Eldd8oZtWYNKGSLyetxKhXMKFcO8+XGrCf7aOvlLkOJFVJmLfYZQOu',

  cpu='8',
  ram='32Gi',
  storage_class='premium-rwo',
  storage_size='4Ti',  // NOTE(mattm): In CW the PVC was defined as 2000Gi but the live PVC was 4100Gi and filesystem usage 3.3Ti
  domain='r.augmentcode.com',
  cluster_issuer='letsencrypt-prod',

) local C = clusters.cluster(cluster); C.k8s + {

  hostname:: name + '.' + std.asciiLower(C.name) + '.' + domain,

  ns: $.Namespace + {
    name:: namespace,
  },

  Object+:: {
    name:: name,
    namespace:: $.ns.metadata.name,
  },

  tls: $.Certificate + {
    spec+: {
      secretName+: '-tls',
      secretTemplate+: {
        labels+: {
          'aug.service': name,
          'aug.cluster': C.name,
        },
      },
      dnsNames: [$.hostname],
      issuerRef+: {
        kind: self.CLUSTER_ISSUER,
        name: cluster_issuer,
      },
      usages: [
        'digital signature',
        'key encipherment',
      ],
    },
  },

  auth: $.SealedSecret + {
    name+:: '-auth',
    encryptedData: {
      user_pw: user_pw,
      postgres_pw: postgres_pw,
    },
  },

  release: lib.ParseHelmChart(vendor[version], name, namespace, values={
    commonLabels+: {
      'aug.service': name,
      'aug.cluster': C.name,
    },
    tls+: {
      enabled: true,
      certificatesSecret: $.tls.spec.secretName,
      certFilename: 'tls.crt',
      certKeyFilename: 'tls.key',
    },
    auth+: {
      enablePostgresUser: true,
      database: db_name,
      username: user_name,
      existingSecret: $.auth.name,
      secretKeys+: {
        adminPasswordKey: 'postgres_pw',  // pragma: allowlist secret
        userPasswordKey: 'user_pw',  // pragma: allowlist secret
      },
    },
    primary+: {
      service+: {
        type: 'LoadBalancer',
        externalTrafficPolicy: 'Local',
        annotations+: {
          'external-dns.alpha.kubernetes.io/hostname': $.hostname,
        },
      },
      resources+: {
        requests+: {
          cpu: std.toString(cpu),
          memory: std.toString(ram),
        },
        limits+: self.requests,
      },
      persistence+: {
        enabled: true,
        storageClass: storage_class,
        size: storage_size,
      },
      pgHbaConfiguration: |||
        # Trust everything local (needed for initial user creation).
        local    all  all                trust
        host     all  all  ::1/128       trust
        hostssl  all  all  ::1/128       trust
        host     all  all  127.0.0.1/32  trust
        hostssl  all  all  127.0.0.1/32  trust

        # Remote auth.
        hostssl %s %s all md5

        # Uncomment to enable remote administration.
        # hostssl all postgres all md5
      ||| % [db_name, user_name],
    },
    rbac+: {
      create: true,
    },
    volumePermissions+: {
      enabled: true,
    },
  }),
}

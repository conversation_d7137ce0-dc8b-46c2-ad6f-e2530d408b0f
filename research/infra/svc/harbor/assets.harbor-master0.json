{"registries": [], "projects": [{"creation_time": "2024-07-10T01:54:14.947Z", "current_user_role_id": 1, "current_user_role_ids": [1], "cve_allowlist": {"creation_time": "0001-01-01T00:00:00.000Z", "id": 2, "items": [], "project_id": 2, "update_time": "0001-01-01T00:00:00.000Z"}, "metadata": {"public": "false"}, "name": "cw-legacy", "owner_id": 1, "owner_name": "admin", "project_id": 2, "repo_count": 6, "update_time": "2024-07-10T01:54:14.947Z"}, {"creation_time": "2024-07-10T04:18:00.888Z", "current_user_role_id": 1, "current_user_role_ids": [1], "cve_allowlist": {"creation_time": "0001-01-01T00:00:00.000Z", "id": 3, "items": [], "project_id": 3, "update_time": "0001-01-01T00:00:00.000Z"}, "metadata": {"public": "false"}, "name": "master", "owner_id": 1, "owner_name": "admin", "project_id": 3, "repo_count": 13, "update_time": "2024-07-10T04:18:00.888Z"}], "replication-policies": [], "robots": [{"creation_time": "2024-07-11T03:58:00.445Z", "disable": false, "duration": -1, "editable": true, "expires_at": -1, "id": 4, "level": "system", "name": "robot$image-sync-test", "permissions": [{"access": [{"action": "list", "resource": "accessory"}, {"action": "list", "resource": "artifact"}, {"action": "list", "resource": "immutable-tag"}, {"action": "list", "resource": "label"}, {"action": "list", "resource": "log"}, {"action": "list", "resource": "metadata"}, {"action": "list", "resource": "notification-policy"}, {"action": "list", "resource": "preheat-policy"}, {"action": "list", "resource": "repository"}, {"action": "list", "resource": "tag"}, {"action": "list", "resource": "tag-retention"}, {"action": "pull", "resource": "repository"}, {"action": "read", "resource": "artifact"}, {"action": "read", "resource": "artifact-addition"}, {"action": "read", "resource": "label"}, {"action": "read", "resource": "metadata"}, {"action": "read", "resource": "notification-policy"}, {"action": "read", "resource": "preheat-policy"}, {"action": "read", "resource": "project"}, {"action": "read", "resource": "quota"}, {"action": "read", "resource": "repository"}, {"action": "read", "resource": "sbom"}, {"action": "read", "resource": "scan"}, {"action": "read", "resource": "scanner"}, {"action": "read", "resource": "tag-retention"}], "kind": "project", "namespace": "master"}], "update_time": "2024-07-11T05:14:26.680Z"}, {"creation_time": "2024-07-11T01:22:44.378Z", "disable": false, "duration": -1, "editable": true, "expires_at": -1, "id": 3, "level": "system", "name": "robot$marcmac-builder", "permissions": [{"access": [{"action": "create", "resource": "artifact"}, {"action": "create", "resource": "artifact-label"}, {"action": "create", "resource": "tag"}, {"action": "delete", "resource": "repository"}, {"action": "delete", "resource": "tag"}, {"action": "list", "resource": "accessory"}, {"action": "list", "resource": "artifact"}, {"action": "list", "resource": "immutable-tag"}, {"action": "list", "resource": "label"}, {"action": "list", "resource": "log"}, {"action": "list", "resource": "metadata"}, {"action": "list", "resource": "notification-policy"}, {"action": "list", "resource": "preheat-policy"}, {"action": "list", "resource": "repository"}, {"action": "list", "resource": "tag"}, {"action": "list", "resource": "tag-retention"}, {"action": "pull", "resource": "repository"}, {"action": "push", "resource": "repository"}, {"action": "read", "resource": "repository"}, {"action": "update", "resource": "repository"}, {"action": "read", "resource": "artifact"}, {"action": "read", "resource": "artifact-addition"}, {"action": "read", "resource": "label"}, {"action": "read", "resource": "metadata"}, {"action": "read", "resource": "preheat-policy"}, {"action": "read", "resource": "notification-policy"}, {"action": "read", "resource": "project"}, {"action": "read", "resource": "quota"}, {"action": "read", "resource": "sbom"}, {"action": "read", "resource": "scan"}, {"action": "read", "resource": "scanner"}, {"action": "read", "resource": "tag-retention"}, {"action": "stop", "resource": "sbom"}, {"action": "stop", "resource": "scan"}], "kind": "project", "namespace": "master"}, {"access": [{"action": "read", "resource": "catalog"}], "kind": "system", "namespace": "/"}], "update_time": "2024-07-11T16:46:24.285Z"}, {"creation_time": "2024-07-10T04:30:40.480Z", "disable": false, "duration": -1, "editable": true, "expires_at": -1, "id": 1, "level": "system", "name": "robot$mattm-builder", "permissions": [{"access": [{"action": "read", "resource": "catalog"}], "kind": "system", "namespace": "/"}, {"access": [{"action": "create", "resource": "artifact"}, {"action": "create", "resource": "tag"}, {"action": "delete", "resource": "repository"}, {"action": "list", "resource": "accessory"}, {"action": "list", "resource": "artifact"}, {"action": "list", "resource": "immutable-tag"}, {"action": "list", "resource": "label"}, {"action": "list", "resource": "log"}, {"action": "list", "resource": "metadata"}, {"action": "list", "resource": "notification-policy"}, {"action": "list", "resource": "preheat-policy"}, {"action": "list", "resource": "repository"}, {"action": "list", "resource": "tag"}, {"action": "list", "resource": "tag-retention"}, {"action": "pull", "resource": "repository"}, {"action": "push", "resource": "repository"}, {"action": "read", "resource": "artifact"}, {"action": "read", "resource": "artifact-addition"}, {"action": "read", "resource": "label"}, {"action": "read", "resource": "metadata"}, {"action": "read", "resource": "notification-policy"}, {"action": "read", "resource": "preheat-policy"}, {"action": "read", "resource": "project"}, {"action": "read", "resource": "quota"}, {"action": "read", "resource": "repository"}, {"action": "read", "resource": "sbom"}, {"action": "read", "resource": "scan"}, {"action": "read", "resource": "scanner"}, {"action": "read", "resource": "tag-retention"}, {"action": "stop", "resource": "sbom"}, {"action": "stop", "resource": "scan"}, {"action": "update", "resource": "repository"}, {"action": "delete", "resource": "tag"}, {"action": "create", "resource": "artifact-label"}], "kind": "project", "namespace": "master"}], "update_time": "2024-07-10T04:49:56.826Z"}], "immutability": [{"action": "immutable", "id": 1, "scope_selectors": {"repository": [{"decoration": "repoM<PERSON><PERSON>", "kind": "doublestar", "pattern": "**"}]}, "tag_selectors": [{"decoration": "matches", "kind": "doublestar", "pattern": "**"}], "template": "immutable_template"}]}
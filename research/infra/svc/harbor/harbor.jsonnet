local lib = import '../../cfg/clusters/base-resources/tmpl/lib.jsonnet';
local clusters = import '../../cfg/clusters/clusters.jsonnet';
local vendor = {
  '1.15.1': importbin 'harbor-1.15.1.tgz',
};

function(
  cluster='gcp-core0',
  name='harbor-master0',
  namespace='harbor-master',
  hostname='harbor-master.r.augmentcode.com',
  version='1.15.1',
) local C = clusters.cluster(cluster); C.k8s + {

  is_cw:: std.startsWith(C.name, 'cw-'),
  is_gcp:: !self.is_cw,

  //////////////////////////////////////////////////////////////////////////////
  //
  // Namespace Setup
  //
  // Create the above namespace, set it as the default for normal K8s objects.
  // Set the ConfigConnector namespace as the default for GCP objects.
  //

  ns: $.Namespace + {
    name:: namespace,
  },

  Object+:: {
    namespace:: $.ns.metadata.name,
  },

  GCP+:: if $.is_gcp then {
    Object+:: {
      namespace:: 'cc-' + C.name,
      project_id:: C.gcp_project,
      metadata+: {
        labels: {},  // reset
      },
    },
  } else {},

  //////////////////////////////////////////////////////////////////////////////
  //
  // GCP Storage Bucket (for bulk registry storage).
  //

  bucket: if $.is_gcp then $.GCP.Storage.Bucket + {
    name:: C.name + '-harbor-master-images',
    spec+: {
      location: C.gcp_region,
      storageClass: 'STANDARD',
      publicAccessPrevention: 'enforced',
      uniformBucketLevelAccess: true,
    },
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // SAs and ACLs for the above bucket.
  //
  // We need a K8s ServiceAccount and GCP ServiceAccount, bound by Workforce Identity.
  // The only ACLs needed are roles/storage.ObjectUser on the above bucket.
  // Only the registry pod will need access to the K8s SA.
  //

  registry_gsa: if $.is_gcp then $.GCP.IAM.ServiceAccount + {
    name:: name + '-registry',
    description:: 'Harbor Registry (' + name + ')',
  },

  registry_gsa_bucket: if $.is_gcp then $.GCP.IAM.PolicyMember + {
    name:: $.registry_gsa.metadata.name + '.' + $.bucket.metadata.name + '.storage-objectuser',
    spec+: {
      resourceRef: $.bucket.localKindRef,
      memberFrom: self.memberFromSA($.registry_gsa),
      role: 'roles/storage.objectUser',
    },
  },

  registry_gsa_workload_identity: if $.is_gcp then $.GCP.IAM.PolicyMember + {
    name:: $.registry_gsa.metadata.name + '.workload-identity-user',
    spec+: {
      resourceRef: $.registry_gsa.localKindRef,
      member: 'serviceAccount:%s.svc.id.goog[%s/%s]' % [C.gcp_project, $.registry_sa.metadata.namespace, $.registry_sa.metadata.name],
      role: 'roles/iam.workloadIdentityUser',
    },
  },

  registry_sa: if $.is_gcp then $.ServiceAccount + {
    name:: name + '-registry',
    metadata+: {
      annotations+: {
        'iam.gke.io/gcp-service-account': $.registry_gsa.email,
      },
    },
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Harbor Core Settings (these will all be grayed out in the UI).
  //
  //  - OIDC (@augmentcode.com) auth
  //  - Override default robot prefix.
  //

  system_settings: $.SealedSecret + {
    name:: name + '-core-system-config',
    encryptedData: {
      oauth2_secret: {
        'harbor-master0': 'AgAzMHD886LT3uLcDhnBCdV1/t0WRAVkF+sCNBoLJOC37YQ1UXRVXkIvXkapE+k5CpDSzRCidUiSCU8XZ8vWdWiwB49Pz/7VeRkIkQHvp1dAuyxaxKgqmU7bWcvNRSNceUzBywVNuG8ZnE9JQKVN9g29QD1irQOiMklnuW3m8feNQvGVVktgv3lIO0j/LuPhX3swc27rokAt77OGNTvWgjdilD+NbrsVSdRcNUTYj5NzFJDGPydVcwE6jptLqD80W7Tu9vsWIYxOXQ5IZvpvSzP9EIw7P0GpV8U9esl5aJkT7mItbNCWeZ+0+IISoSHDz+Uv0Ic8Jh8t9RDME4jdg4ud0kb6+vA/GYRCnTmXmGRK1DYxPwi5uyzurdryCepYbni1Q/1ZY7Em/3mxjgkOZywkuI/WVGkXVp5H2bRQrrxBS0c40ri6m3u6XViFdUhnKeYNtuK1FXx2/B4+9Vo2jQtdBpsSc0cISNY8EiEV9qKQyjP76J/8loMYHQidEpDRA8g7e+e2NB09U8fIlbVjF72o+O29002nPun7dGFLdg7WMGIp6qjlszeyO/3rgeDIvJGaWPzsYXH+iry2GGQFbHvq7yk/75VNhrgsgxTifv/NDtSIKj3/rDk8IUzO+FUPWzghhdWE5cTcipuweibOVe7RVnxlwFyh5OhRJCzTnEqwQeeC/QreUnaX2rC1UzjEoQNNk8e4H52upjyiRML4pv5a8mV+V6ehkN9u9ldFpSOAA2sN3Q==',
        'harbor-cw-east4': 'AgAqrx7oKRc/WI5LUjTwKvuX6vMC+JLta2K8jQzb5Vuj6sFZtYb3/m1772QFLNeiv9jxGcota1AFPkgEJG+yozM859mEQmOc6Mka4j0L0RLAOt/zGdeUg2MQbBhiHIj8LDOc9RN26sW3GnaBRK50SRH6WQfpY2nfFYQatj/wgxdcz2xjJNGnmGd/6pXE12lIPEG905Gujf3LfGnfW+M9CPBdykeJECw7cdOftnqZFFZ858+P/+0Diwbt/jx2qdQBCzQkdn2QQk4hb3G53DUWJFkOmUFr2Xy3wgpXyFtaX8BtmWDc89qi9XLr+lkuc9leQnpj+NfVJT7weDFvfZ1YWHLk/cGQFV6h+G6Aw/Z0YMgy/QBuk3AJs1g2dM9BwrUsHdNMTNXvZnapNRQg97ZSiOq1mWP5+5EQLYjXJ4PMnyFDLjxLxxCkT9GOsQpvu3qWZ1ltZuDnqo6RQHgq7aHaIrgnx2MdE6fisTCMqJuQ5DRdllvlE510yIFhkXqXyZ3b8TPGee6yAr/0rGt1uV6bg4TrAqQ+Th0aHL/E1PGJ0qW0lVUrWap7V2t7x2I4XqQCgNcZz/ljJ93TdOSlbxvsnVXUkVQyMDi9RCPTqy/I4PRweR2X26D3AF36ZxN5835smKts7bbWFXyQKrtOGe1buc0whtNMDsmOChuT9ywdc6kDlpfr9idNgDpRBlXQQQjCl0odGlieZR1ww9FPwCn05kaced2MP7gwF8/GyPyMMVYA5XlyTA==',
      }[name],
    },
    spec+: {
      template+: {
        type: $.SECRET_TYPE.OPAQUE,
        data: {
          CONFIG_OVERWRITE_JSON: std.manifestJson(std.prune({
            project_creation_restriction: 'adminonly',
            robot_name_prefix: 'r@',
            scanner_skip_update_pulltime: true,  // why isn't this the default?

            auth_mode: 'oidc_auth',
            self_registration: false,
            oidc_name: 'Google',
            oidc_endpoint: 'https://accounts.google.com',
            oidc_client_id: '************-a70s25au23e7lbe1vb7ae3dlg88nju6h.apps.googleusercontent.com',
            oidc_client_secret: '{{.oauth2_secret}}',
            // NOTE(mattm): The google groups scope has a bug: https://github.com/goharbor/harbor/issues/13609.
            // So this isn't currently working.
            oidc_admin_group: '<EMAIL>',
            oidc_scope: 'openid,email,profile,https://www.googleapis.com/auth/cloud-identity.groups.readonly',
            oidc_verify_cert: true,
            oidc_auto_onboard: true,
            oidc_user_claim: 'email',
          })),
        },
      },
    },
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Harbor Helm Release
  //

  release_tol: lib.ApplyToleration(self.release, 'r.augmentcode.com/pool-type', 'svc'),
  release:: lib.ParseHelmChart(vendor[version], name, namespace, values={
    expose: {
      type: 'ingress',
      tls: {
        certSource: 'secret',
        secret: {
          secretName: name + '-ingress-tls',
        },
      },
      ingress: {
        annotations: {
          'cert-manager.io/cluster-issuer': 'letsencrypt-prod',
        },
        className: 'nginx',
        hosts: {
          core: hostname,
        },
      },
    },
    externalURL: 'https://' + hostname,
    persistence: {
      persistentVolumeClaim: {
        local s = self,
        _pvc:: { storageClass: if $.is_gcp then 'premium-rwo' else 'shared-vast' },

        registry: self._pvc + { size: '2048Gi' },  // Unused when imageChartStorage.type != 'filesystem', below
        jobservice: { jobLog: s._pvc + { size: '4Gi' } },
        database: self._pvc + { size: '4Gi' },
        redis: self._pvc + { size: '4Gi' },
        trivy: self._pvc + { size: '16Gi' },
      },
      imageChartStorage: if $.is_gcp then {
        type: 'gcs',
        gcs: {
          bucket: $.bucket.metadata.name,
          useWorkloadIdentity: true,
        },
      } else {
        type: 'filesystem',
      },
    },
    updateStrategy: {
      type: 'Recreate',
    },
    core: {
      configureUserSettings: null,
      extraEnvVars: [{
        name: 'CONFIG_OVERWRITE_JSON',
        valueFrom: {
          secretKeyRef: {
            name: $.system_settings.metadata.name,
            key: 'CONFIG_OVERWRITE_JSON',
          },
        },
      }],
      resources: { requests: { cpu: '4', memory: '16Gi' } },
    },
    registry: {
      serviceAccountName: if $.is_gcp then $.registry_sa.metadata.name,
      registry: {
        resources: { requests: { cpu: '4', memory: '16Gi' } },
      },
      controller: {
        resources: { requests: { cpu: '2', memory: '4Gi' } },
      },
    },
    portal: {
      resources: { requests: { cpu: '4', memory: '4Gi' } },
    },
    jobservice: {
      resources: { requests: { cpu: '4', memory: '16Gi' } },
    },
    database: {
      internal: {
        resources: { requests: { cpu: '4', memory: '8Gi' } },
      },
    },
    redis: {
      internal: {
        resources: { requests: { cpu: '4', memory: '8Gi' } },
      },
    },
    exporter: {
      resources: { requests: { cpu: '4', memory: '16Gi' } },
    },
    trivy: {
      resources: { limits: { cpu: '1' } },  // avoid int<>str diff
    },
  }),
}

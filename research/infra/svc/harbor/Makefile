NAME := harbor-master0

ifeq ($(NAME),harbor-master0)
NAMESPACE := harbor-master
CLUSTER   := gcp-core0
HOSTNAME  := harbor-master.r.augmentcode.com
else ifeq ($(NAME),harbor-cw-east4)
NAMESPACE := harbor-cw-east4
CLUSTER   := cw-east4
HOSTNAME  := registry.cw-east4.r.augmentcode.com
else
$(error Unsupported NAME: $(NAME))
endif

CONTEXT  := $(shell jsonnet -A name="$(CLUSTER)" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
K8S_CONF := harbor.jsonnet
KUBECTL  := kubectl --context="$(CONTEXT)"
KUBECFG  := kubecfg --context="$(CONTEXT)" \
	   --tla-code="name='$(NAME)'" \
	   --tla-code="namespace='$(NAMESPACE)'" \
	   --tla-code="cluster='$(CLUSTER)'" \
	   --tla-code="hostname='$(HOSTNAME)'"

show:
	$(KUBECFG) show $(K8S_CONF)

diff:
	-$(KUBECFG) diff --diff-strategy=subset $(K8S_CONF)

apply:
	$(KUBECFG) update $(K8S_CONF)

list:
	$(KUBECTL) get -n "$(NAMESPACE)" sealedsecret,secret,cm,pvc,sa,svc,ing,deploy,statefulset,job,pod,role,rolebinding

deploy: diff apply

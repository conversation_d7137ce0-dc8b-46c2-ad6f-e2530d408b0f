#!/bin/sh

set -eu -o pipefail

info() {
	local fmt="$1"
	shift
	printf "[init.sh] $fmt\n" "$@"
}

info "Network Configuration:"
ip add

if [[ ! -d /etc/ssh/authorized_keys/ ]]; then
	info "WARNING SSH authorized_keys should be mounted from a k8s configmap."
fi

if [[ ! -d /etc/ssh/keys/ ]]; then
	info "WARNING SSH host keys should be mounted from a persistent k8s secret."
	info "WARNING Generating !EMPHMERAL! SSH host keys..."
	apk add --update --no-cache openssh-keygen
	/bin/mkdir -p /etc/ssh/keys
	/usr/bin/ssh-keygen -A
	/bin/mv /etc/ssh/ssh_host*key* /etc/ssh/keys/
fi

info "SSH configs:"
/usr/bin/tree /etc/ssh

info "Execing SSH..."
mkdir -p /run/sshd
exec /usr/sbin/sshd -D -e

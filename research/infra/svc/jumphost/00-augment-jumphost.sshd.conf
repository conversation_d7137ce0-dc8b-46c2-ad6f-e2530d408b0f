Port 22022

HostKey /etc/ssh/keys/ssh_host_rsa_key
HostKey /etc/ssh/keys/ssh_host_ecdsa_key
HostKey /etc/ssh/keys/ssh_host_ed25519_key

PermitRootLogin no
PasswordAuthentication no
KbdInteractiveAuthentication no

AuthorizedKeysFile none
AuthorizedKeysCommand /usr/bin/find /etc/ssh/authorized_keys -type f -exec sed -e $a\\ {} +
AuthorizedKeysCommandUser jump

AllowTcpForwarding yes  # alpine disabled by default

# vim: set ft=sshdconfig

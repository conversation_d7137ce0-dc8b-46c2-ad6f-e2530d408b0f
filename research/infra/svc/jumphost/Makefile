NAME      := jumphost
NAMESPACE := $(NAME)
VERSION   := 2024-12-29.1
CLUSTER   := gcp-us1
ALPINE_VERSION := latest

REGISTRY := $(shell jsonnet -A name="$(CLUSTER)" -A prop=registry -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
IMAGE    := $(REGISTRY)/$(NAME):$(VERSION)

CONTEXT  := $(shell jsonnet -A name="$(CLUSTER)" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
K8S_CONF := $(NAME).jsonnet
KUBECTL  := kubectl --context="$(CONTEXT)" --namespace="$(NAMESPACE)"
KUBECFG  := kubecfg --context="$(CONTEXT)" --namespace="$(NAMESPACE)" \
	   --tla-code="cluster='$(CLUSTER)'" \
	   --tla-code="name='$(NAME)'" \
	   --tla-code="namespace='$(NAMESPACE)'" \
	   --tla-code="version='$(VERSION)'" \
	   --tla-code="image='$(IMAGE)'"

build:
	docker build --pull -t "$(IMAGE)" --build-arg=ALPINE_VERSION="$(ALPINE_VERSION)" .

push:
	docker push "$(IMAGE)"

show:
	$(KUBECFG) show "$(K8S_CONF)"

diff:
	-$(KUBECFG) diff --diff-strategy=subset "$(K8S_CONF)"

apply:
	$(KUBECFG) update "$(K8S_CONF)"

logs:
	$(KUBECTL) logs -l aug.service="$(NAME)" $(LOGS_EXTRA)

list:
	$(KUBECTL) get -o wide -l aug.service="$(NAME)" sa,role,rolebinding,sealedsecret,secret,cm,pvc,ingress,svc,deploy,pod

deploy: diff build push apply

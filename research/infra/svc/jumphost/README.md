# SSH Jump Host

WARNING: This is an experiment / WIP.

## The Image

 - Use a very small (9MiB) alpine-based image to run SSHD.
 - No one can log directly in.
 - SSH Host Keys were generated one time, and loaded into a K8s Secret.
   - This way, a pool of jump hosts won't create know host_keys warnings.
 - SSH Authorized Keys are stored in per-user data in a single ConfigMap.
   - They're concatenated together using an AuthorizedKeysCommand.
   - Only SSH Key authentication is supported (no passwords, use the shared `jump` user).

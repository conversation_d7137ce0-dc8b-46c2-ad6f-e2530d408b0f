local clusters = import '../../cfg/clusters/clusters.jsonnet';
local aug = import '../../cfg/lib/augment-enums.jsonnet';

function(
  image,
  cluster='gcp-us1',
  name='jumphost',
  namespace='jumphost',
  version='latest',
  replicas=1,  // TODO(mattm): bump to 3 if we start using this more
  cpu='1',
  ram='1Gi',
  ssh_port=22022,
  hostname='jump',
  ssh_authorizedkeys_configmap=aug.CW_AUTHORIZED_KEYS,
) local C = clusters.cluster(cluster); C.k8s + {

  BaseLabels+:: std.prune({
    'aug.service': name,
    'aug.version': version,
  }),

  Object+:: {
    name:: name,
    namespace:: namespace,
  },

  ns: $.Namespace + {
    name:: namespace,
  },

  deploy: $.Deployment + {
    local d = self,
    spec+: {
      replicas: replicas,
      template+: {
        spec+: {
          local pod = self,
          hostname: d.metadata.name,
          containers: [{
            name: d.metadata.name,
            image: image,
            resources: {
              limits: {
                cpu: cpu,
                memory: ram,
              },
            },
            volumeMounts: pod.volmount_mounts,
          }],
          volmounts:: [
            {
              name: 'run-tmpfs',
              volume: {
                emptyDir: { medium: 'Memory' },
              },
              mount: {
                mountPath: '/run',
              },
            },
            {
              name: 'tmp-tmpfs',
              volume: {
                emptyDir: { medium: 'Memory' },
              },
              mount: {
                mountPath: '/tmp',
              },
            },
            {
              name: 'ssh-authorized-keys',
              volume: {
                configMap: {
                  name: ssh_authorizedkeys_configmap,
                  optional: true,
                  defaultMode: std.parseOctal('0444'),
                },
              },
              mount: {
                mountPath: '/etc/ssh/authorized_keys',
                readOnly: true,
              },
            },
            {
              name: 'sshd-hostkeys',
              volume: {
                secret: {
                  secretName: $.sshd_hostkeys.metadata.name,
                  optional: false,
                  defaultMode: std.parseOctal('0400'),
                },
              },
              mount: {
                mountPath: '/etc/ssh/keys',
                readOnly: true,
              },
            },
          ],
          tolerations+: [
            {
              effect: 'NoSchedule',
              key: 'r.augmentcode.com/pool-type',
              value: 'svc',
            },
            {
              effect: 'PreferNoSchedule',
              key: 'r.augmentcode.com/pool-type',
              value: 'svc',
            },
          ],
        },
      },
    },
  },

  service: $.LoadBalancerService + {
    externalDNS:: std.join('.', [hostname, std.asciiLower(C.name), 'r.augmentcode.com']),
    spec+: {
      selector: {
        'k8s.deployment': name,
      },
      ports: [
        {
          name: 'ssh',
          port: ssh_port,
          targetPort: ssh_port,
          protocol: 'TCP',
        },
      ],
    },
  },

  // For simplicity, use the same hostkeys for all clusters. The private keys still need to be sealed per-cluster.
  sshd_hostkeys: $.SealedSecret + {
    name+:: '-sshd-hostkeys',
    // augi ssh-keygen -C root@jumphost tmp.ssh  # or ssh-keygen but our wrapper is more convenient
    // cat ssh_host_{type?}_key | kubeseal --raw --context=${cluster?} --namespace=jumphost --name=jumphost-sshd-hostkeys; echo
    // shred -u tmp.ssh/*; rmdir tmp.ssh
    encryptedData: {
      'gcp-us1': {
        ssh_host_ecdsa_key: 'AgAMPscZCgqPp0rfdk3jp31CZEDNJt1dakghDn0FABphFlh+/rcFRCDJ5mpQYmK8UXeqL2JXLnzyOOPTd1vIDOt+r2eSHtwIR2XwZ9Wt7a7i/Q/67JcSGmVbr8qQ3O9PxzJyHxVc/1Kn099Uzkw/poc/OGG4iXJLs16jYcBR05NO8ao/ZDA/4wqKEcAUUxoRyTQCyujvzcx6Ogs6P7uq5INAoAlhKQZGXJpcU7b+gaBCtBuxZCPr9jKUMetXTcMcfXmtRcEkRBvLg8rSQoqYqMc1DJPtRdpEzx90vGE3UJoYJzn4mwRnFe5bfd/cQpT+JkETfyYhpG0QSeKa/Msh4W2SsJ+HX5yC4xRKK4WZYqM0SNJ+2+Ko6abNxpWk8jYCBPVtn0Upfz7udxQIOl9WR2ukFEpLbHZ9F/5oufHao6C44qFznNr0A6ZbLFiZtKDI1f9Tyowtvp2mf2YDzmQDxBRPpeyD3ITEP/LVFD/UdXf5s7QxnsJvKoNJgw33ILPfwbTu0tsMq4699NfBze1VUkuw9L0AtFY3+gqs79kZFKblRidbWjyROXKsg++jHZKiLwfT09GmTkb+nXZ2TlJ4pMq5SFqGJ/YbdXNYR6hxyyTwIViXo0G77mvvxTofae+os/AICBf/UhNvwxK4CVOuSeuLlVCFggDfrj0bRJNx5CRdmYW2P2zqRJzFchYtqVypQUE4mWVgEDvILqDdZqmYxN+SDdU1Xb9QM+lq61f3awMhUMv4l7Wm47v1Or2WmNZ/Saqx5HFkWQo4cRIT/ffXmV0vbjYDr+8nNJVzhn0UztoByOb4ySn1tMF65kdhrPZO+JeePC5iv77MZjX9a7BMkhC4Z5a8hvG+11vHLP8IFogBU7NhF4VB5CtTHzPM2uRAes44OEQFvhc2mjZohp8fKx7sNzilolxtT0tNnlmRCL/Vj03YZWLTkYM12j0HoBo2sSBlXppfwTMhguHiyxWhA09vewwuExzL1KjusI937kPAQmENQcPkzcLrpanwvgHrdX807mGKjlq2jhV9S6+gG8fCf9gzfdOFD8tA77T4kNsF3c2+8ZOxOKRPehqsrDVjHdhWBXvroKRoRygK2u6LF4LrncOjQ5bTErZSAtIl1AbFDoUCH3d0lK28FVlNUKXAchmoie0VUoAaMbE4ThOsf+UFg+sk7RUXvE9eZ6wqHhRl82XU+aOjo4jAOB6BjNL4pN7iD1oFdKbeaPKszn2LIPPK6K5nkRZBlTfGmE/yLGK5t+/P47SN6Q0+rCWu+50ZxkZGu1gYnOealeW+RvqWV3fnDpXafYegL1YQ/fX8wFIBcbQPJDUKi+Gm36eSaQ3d5Qw2Yr9P5twgytAJTBU9mnmbgv3LwzWw/ZUJ',
        ssh_host_ed25519_key: 'AgBnakZuO++zVmr14o2DCsjKYD55BCIk0/UIwLZfjqdanfpDN0nkcpX8Up6ylm1sDpHO6KlJwiFP9oZINk8y2rwA2kJQ2PcZGdbTa2Uf/Uv96wICuE/++0/HBi3KuS2xv18F1xxPR1RFp+yOYpEF7VtbG1UynPX6NX9FtTRnHjcpQWJ+OpBJJwUZ+IFrRraDJDeVyYBUSN3bWJg4o8yhryqBp/OxEfWbhoLKQZftnXVXK6nTTOYjJXEZa0R0Nj738PopW1j1SFf+c2DteM4k/T1I7p0QVbA9QdraPvq4zWorhjPaGMNqlcIG+UfsJRk2PQmiJ/1Rkrqmd2CupXGJG/JQerCeUsrf7k40kICmk4w+4crEk/TqALGLHCgcfe1EW+HvdD9bHbcyvNO7MNMk3KSHa8fQ9WPe60gpV5iICtIJuVxUDuysubqNVyFj9T78nq3bp9b4MKaeYUUsX2ILqr2nzMYgiYxC9IOKnTKRQfBEeBpnidWSMAsM6YRtVe6+uY0eHsr1Uxx4X/srP2nFbR1hFcUAnFaTu3q/nowPSPt4wTug9+vY8oridZ7qTfJYDBVxddsehZwildfVD3xNHo5Wj7i1cNurBKV5B2LWSxOhQekvxMVyul7NRC9rMeRrPz6NJcHRzmAHKcXpaMOckUGke+bIy5WPDJvTz9FFUqQApiZ/1hS7v9fKu0tonX/wC7Y6j+zhuseYWWcqC7jbVrHc4hc6yil2eY02xflpmYqf9AKR+wzXdt7v+gaq9UlWb/mGG7eLgAJZEVeI2U+WkWukUf5xShB3soNdRn5PJij56wLttpEkP0dfdyYfG3Rl5+wN7EovqNuagAvR8aFLVjes7/sixZRIXUsva6RNJGeWxb6AdnrDLqXfrRonppHsqYDr2ewl+UacQwd2Yzzkp7/mk0dW7SVCV2cXxh+2mtFPyr/+6jLv/q92YLsFvXJDkDhKaHCflsHZjjrhVYJO7CkKMGKAJowCpHdlGbqGcrFVEEmxrybnrJ5/plO9qItvkiAY2S8o6S8pIeAja6MjElGv8u9HE5lXSZxULdbSD1pOJyRBwOManL+BhsCJ0LmNlmpk7RIyBMd6y3zKpkaLJbLOkoR4V5a79zGj+Z2RyUqws8EhNqIX0vlvdRSzjns1uWq7Rw5VTjYQMbVmUM3lJK7nZdbfpixoNLe4zAOHgfOwxDht6CqzxmDey5vLd/G807xCG3UtzKu6Dkq9w31QU17I',
        ssh_host_rsa_key: '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',
      },
      'cw-east4': {
        ssh_host_ecdsa_key: 'AgBdFlG6evXcb79xrFP+D4GAM+cKqaeEPU0xaAikr58rnEjtRAmAqRdTW4tSKjTdxh1emamuqs3urY8b7ZGwVlFW842LmIkW5Lgk6ILIHHBa6kqhJOUwAiS0zOZZPQQ02+RYx+lYNYwMxctn92HqH3J4KbHu69URrL0XgWUWhp1nF8ylMszgULQXhT4buXqovXttVrJPtSSWVtLeIyPHaioD+up6jp4dSgY8QA9HQ3Dby/bDIFTbxEdblv25hhXqFGDJMjkuTG4WZEHWxdyGK0n+Odj5YqombGkm7j/EfSm98AhHZNFmPfFGsHvr9t7R1chrKd9UtUz618Wk7PkKa6W4uU5jDDGag3sUGDbclRI7uLC0m6s1be1Z2rI6QWak4hFWs7EwzRTKMz7E7iUfJ7TeTcwOYgIxRrFpCj7Tp4J60OMNhXTxottCW9zlImNGHtknmHh3ABlseGMD6w2cNTgDyRvSdNgMz7D18yig1guKMg5mvrg4RFLxTA9x3J+W9G+krl3hgnQ2nKxSw9cpVz9vqTlR6zI6JwZLFu0eF+rXT7M/t5RV9TXGcf2O5E2KDvGq78VxNJPJbG0OGUDZJZzElimo7GRE+H9HdZ+RXuB1hcXVvO+w2y5o0HD/9sCb5dzTUOePH6hreMtLTbY+G/UYJpwxEtnNOeHzpP0l1hCx6gAZOL0EgRrJmZtBgm6uz21vxNb0wRQ26PqwM29kqLfvj0jdxgacNmOsI2Wn6emYHZVtVqhRwVC4lVERMIzlPloWzUjWam2e6jGLF1BbRO1EApFlDAZyL7GwAP51sykffzROxHZLCqwWHpWdR75lYvotG6E/fsWnOG6nQ4CMQQO1MTI6TBBE3QmNeO4jqG9UmLyuwBErTzXGIjf/ljLyvJKOls20s88osryJcB7bRDWQ5sW7uN8ghaoQFV3tMObz36aTRz2BuV1DK3S0rEdiUL5ODTZh9qlZ5TLJ54QMmzE08f6LwLdq/0RpuzdOjSlolQTmj5D25ql8favlYK+tWRAXgRckoCBMvZQ0dA6GOrRHD0h5Zue4yIlPdNY6YZ4Z7aC9RaFzvZvMg7Alcgb9oUNIiRWMbmlaRqY+JFXQcDQhofVsUJh/JuB4j1I5MZe7COQC5VybXT6xdYMk7elS+nvXif0uRVOx5tw7nrRnkEcvDI6E4cFrolSxJKT9oMmtZmdP9GKOFpJyVWAyh/sQjEkjLqkVPeW94SgVwrgwWCMbjBb0C0Aq1/ryv5gDMTHOaudvZiq3+/Hxvzt7ZdMA+4pN985NS/6+v3lJHVWiFPQR3L+9aTiym0Se+wGjM5ik6KOTtW9abGm5BSscz0pTf8tBJHDYTAXO9xeTs+M1aJtwAAqi5gXioFT5',
        ssh_host_ed25519_key: 'AgCdxmpYJCQj9SZAQ5/GncbK6JWEC9Zyn3wfnkyV+u7XR0ioGriwjuureX3ynd5WDNQ/DOJuhZtnRvhXdtQyWezul5c7H6wS0NagLte0xwu1LXDO4hbXy7wXxNVb+kMdPc8oMBti/i/wHvPwytFC+yNBVydt2G/9A+yOszQ1Ocxrc0GmA8yc7FGupyvyFOF+FKQa6OgrW2VF2BGg2WHydSKmr+ierHFcuiR5ILN2+DWjZS2M4sZTtMmYx9UOcUMM4STp5Pjmo/yJkwpkiuS/xKv1UIFnxs9HVQpj6JCdmegry8o91j47XcOW9TqeFsfBg+fYzfXsM+mcyEPNrfOpQQqwOAlHpy5YujT9STjiESaW2jnMnaIAD1iUeTUZNh301mcy5CWhfrRdZziDGsc8A5Y+zNfDEQTfws2A3YIRIir5ldelIdKoK9b1HW5OGzDOJo4mX69GEmBFtKQpRHSH4NKqD9+mmRDUZrstrtX+wWdV39mBCwaC2gVueA+thPXYf/q9LLFq09H5q8w3A9u/RkLSp9tRntgooL09ra4swCA4M1NqCLZAb0x8MthbJsT7POJj1WF1p/XnZxZP+eBW7HLa+EFZfn+OGjxNUrU7jze58G7G/gupU+/WVtxHFW2PMJUt7lipQOiq86LEwk0W1rcbbROutj/PWftkBCuYGBc2538Gsoy/SxRVPbat9Yp1HZ0joVD+bSzgSXrQ6OvjwV0UQFpesR72OpBOT/LktB5ysMobUTIhgx+rRrcNHdMWQfJNHH8jUKakj+/8DOG+rHqGaR8rpjNMMZka1lscj1GeM5Bx6LdbIqdXNa7mv3IUIsNfEdKj8fJRzK/b76p0ophcB2i/6X6Bv5JPyI9mn75wxx979wk/Rzsj7GWRS5LtHBsqEuX1EpSKfYUhG6+A37GgI6DL0FHW2uEN2ibRF1vu9qWJhenRK95Dgju1qOPTwFCXS5+rakukGkJW958LlA9uDsDC6NEOA5N0Is2VuWtAKWgH6gV2HvsHKz3/EAxppMSr+PFGgDB5hWQtEvDHepOMtOyhwpsE4WklRUE+jhVO+Iv13EQk9lAGP3ea2/PrO+mv7Q6txuhtcjZn21URI6DTYAlYuCwJo/eCsH/cKOs1UbhVafjaZN4LzlsP6rjzXtf3pBH5NZMm0+7Iyqmqt75QknFbKZOSrk0Uo8hJkj2yAxMKeoHew0PWQF7NPXLZIgFsujmRfaVnyTvzziCou6IH',
        ssh_host_rsa_key: '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',
      },
    }[std.asciiLower(C.name)],
    spec+: {
      template+: {
        data: {
          'ssh_host_ecdsa_key.pub': 'ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBB4ZEYqv7owLqYUUqKUQ8ePELz+/l7++LUKWV4t2LpEpwKVwQirpjD2kTA5LOIzUIB3MWMhu2xJg3nyYRbmpiFo= root@jumphost',
          'ssh_host_ed25519_key.pub': 'ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIAMporGNh+4C+8kAc7sKFexRUOZbVZ2+OdII5aeHB79j root@jumphost',
          'ssh_host_rsa_key.pub': 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCzI2vKpang8t18h6dOen8p/CylvzwVQ9KJP5/YXtud5chV6gPisRKRAg24j57eRW16mDxnCHHEWCcCLe+6zbp3TalZ1ZE5zugeIsRSGzKrBYhrpfojQwfO0mYDlzaM0FaUXpO4cD7SPYuYJXsWVWhE9+2hCRMTX4thHC7FWIMDdtQHzDXfMN30Ylled/aYxvtRZJ0pLUDHmOUh9t5R0zlFzyWKS2bxxPxe1m/BmmdIwQC4cLejpU3O7YhYc2MdZRZugD/bei2PCUNM4GfJSH10Gjsv2mnjOgMNQ6ZkUkJPfX5oalpF9xqHvT4SJtiDe8aOjvDeCXc6w0Q+Fr+a+q+8450qHiXqc8Rm3QsgSjzvyj2UG0w8wWnzDJyksitO5dhcI2sj9SDoxAij/oHFh8INnsclgJjPtZulhvnR7/hlxqg3hEddFIfyxuLPOHy8aSMXuKHTgyjQpZ4ADfhCpINBzvEyQwORChoKQa5c+JKx5gLPVQr1XrGfEmybtiOUhpc= root@jumphost',
        },
      },
    },
  },
}

#!/usr/bin/env python3
from google.cloud import monitoring_v3  # type: ignore
import pandas as pd
from pandas import DataFrame, Series
import matplotlib.pyplot as plt  # matplotlib imports needed only in case you want to plot the `points`
import matplotlib.dates as mdates
from matplotlib.axes import Axes
import requests
from argparse import ArgumentParser
from pathlib import Path
import json
import os

import time
from datetime import datetime


def google_usage_data(
    now: float,
    interval_sec: int,
    cluster_name: str,
    project: str = "projects/augment-research-gsc",
):
    client = monitoring_v3.MetricServiceClient()
    interval = monitoring_v3.TimeInterval()

    seconds = int(now)
    nanos = int((now - seconds) * 10**9)

    interval = monitoring_v3.TimeInterval(
        {
            "end_time": {"seconds": seconds, "nanos": nanos},
            "start_time": {"seconds": (seconds - interval_sec), "nanos": nanos},
        }
    )

    filter = f"""
        metric.type = "kubernetes.io/container/accelerator/request"
        AND metadata.user_labels."aug.gpu_type" = "nvidia-h100-mega-80gb"
        AND resource.labels.cluster_name = "{cluster_name}"
    """

    aggregation = monitoring_v3.Aggregation(
        {
            "alignment_period": {"seconds": 600},  # 10 minutes
            "per_series_aligner": monitoring_v3.Aggregation.Aligner.ALIGN_MEAN,
        }
    )

    # Calling API to send list time series data
    results = client.list_time_series(
        request={
            "name": project,
            "filter": filter,
            "interval": interval,
            "view": monitoring_v3.ListTimeSeriesRequest.TimeSeriesView.FULL,
            "aggregation": aggregation,
        }
    )

    data = []

    for result in results:
        for point in result.points:
            data.append(
                {
                    "timestamp": point.interval.start_time.timestamp(),  # type: ignore
                    "value": int(point.value.double_value),
                }
            )

    df = pd.DataFrame(data)
    df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s")
    df = df.set_index("timestamp").sort_index()
    df = df.sort_values("timestamp")

    sums = df.groupby("timestamp")["value"].sum()
    return sums


def prom_query(prom_url, query_str, start_time, end_time, auth_headers):
    """Query prometheus for a range of time."""
    step = "10m"

    query = {
        "query": query_str,
        "start": start_time,
        "end": end_time,
        "step": step,
    }

    r = requests.get(prom_url, params=query, headers=auth_headers)
    r.raise_for_status()
    # print(json.dumps(r.json(), indent=2))
    return r.json()["data"]["result"]


def prometheus_usage_data(now: float, interval_sec: int, cluster_name: str):
    prom_range_url = "https://prometheus.ord1.coreweave.com/api/v1/query_range"

    auth_headers = {"Authorization": f"bearer {os.environ.get('CW_PROMETHEUS_TOKEN')}"}

    # query = f'billing_gpu{{label_gpu_nvidia_com_model=~"A100.*|H100.*", namespace="{cluster_name}"}}'
    query = f'billing_gpu{{label_gpu_nvidia_com_model=~"H100.*", namespace="{cluster_name}"}}'
    # print(query)
    metrics = prom_query(
        prom_range_url,
        query,
        now - interval_sec,
        now,
        auth_headers,
    )
    metrics = metrics[0]
    # print(m["values"])

    data = []

    for value in metrics["values"]:
        data.append(
            {
                "timestamp": value[0],
                "value": int(value[1]),
            }
        )

    df = pd.DataFrame(data)
    df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s")
    df = df.set_index("timestamp").sort_index()
    df = df.sort_values("timestamp")
    return df
    # print(metrics)


def tabular_data(util_rolling):
    # Tabular data
    # last_date = util_7da.tail(1).index[0]
    # last_date_str = last_date.strftime("%Y-%m-%d")
    last_val = util_rolling.tail(1).values.mean()
    last_val_str = f"{last_val:.2f}%"

    color = "green" if last_val > 75 else "blue" if last_val > 50 else "red"
    last_span = f"<span style='color:{color}'>{last_val_str}</span>"
    return last_span


def plot_data(
    ax: Axes,
    ax2: Axes,
    data: DataFrame | Series,
    color: str,
    label: str,
    gpu_max: int,
    lw: int = 1,
):
    ax.plot(data, color=color, linewidth=lw, label=label)
    ax.axhline(
        y=gpu_max,
        color=color,
        linewidth=lw,
        linestyle=":",
        label=f"{label} Max",
    )
    util_pct = (data / gpu_max) * 100
    ax2.plot(
        util_pct, color=color, linewidth=lw, linestyle="--", label=f"{label} Util%"
    )
    # Add 7-day rolling average
    util_pct_7da = util_pct.rolling(window="7D", min_periods=1).mean()
    ax2.plot(
        util_pct_7da,
        color=color,
        linewidth=lw,
        linestyle="-",
        label=f"{label} Util% (7d avg)",
    )


def slack_report(
    slack_channel: str,
    gcp_util_pct: Series,
    cw_util_pct: DataFrame | Series,
    gcp_gpu_max: int,
    cw_gpu_max: int,
    image_path: Path,
):
    gcp_7da = (
        gcp_util_pct.rolling(window="7D", min_periods=1).mean().tail(1).values.mean()  # type: ignore
    )
    cw_7da = (
        cw_util_pct.rolling(window="7D", min_periods=1).mean().tail(1).values.mean()  # type: ignore
    )

    summary_message = f"GCP 7-day avg: {gcp_7da:.2f}% ({gcp_gpu_max} GPUs)\nCW 7-day avg: {cw_7da:.2f}% ({cw_gpu_max} GPUs)"
    post_slack_update(slack_channel, summary_message, image_path)


def post_slack_update(slack_channel: str, summary_message: str, image_path: Path):
    token = os.environ.get("SLACK_BOT_TOKEN")

    image_size = image_path.stat().st_size
    auth_headers = {"Authorization": f"Bearer {token}"}
    r = requests.get(
        f"https://slack.com/api/files.getUploadURLExternal?filename=gpu_usage_plot.png&length={image_size}",
        headers=auth_headers,
    )
    print(r.text)
    upload_url = r.json()["upload_url"]
    upload_id = r.json()["file_id"]
    r = requests.post(
        upload_url, data=open(image_path, "rb").read(), headers=auth_headers
    )
    print(r.text)

    r = requests.post(
        "https://slack.com/api/files.completeUploadExternal",
        json={"files": [{"id": upload_id}]},
        headers=auth_headers,
    )
    print(r.text)
    url_private = r.json()["files"][0]["url_private"]
    print(url_private)
    # Slack throws a very confusing failure if you don't give it a second
    # to process the image
    time.sleep(30)

    # url_private = "https://files.slack.com/files-pri/T03N30J9CRE-F083PKRQDAA/gpu_usage_plot.png"
    slack_url = "https://slack.com/api/chat.postMessage"
    data = {"channel": slack_channel, "unfurl_links": False, "unfurl_media": False}
    report_url = (
        "https://webserver.gcp-us1.r.augmentcode.com/gpu_usage_reports/index.html"
    )

    blocks = [
        {
            "type": "header",
            "text": {"type": "plain_text", "text": "Reserved GPU Usage", "emoji": True},
        },
        {
            "type": "divider",
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"{summary_message}",
            },
        },
        {
            "type": "divider",
        },
        {
            "type": "image",
            "title": {"type": "plain_text", "text": "Reserved GPU Usage"},
            "block_id": "image4",
            "slack_file": {"url": url_private},
            "alt_text": "Reserved GPU Usage.",
        },
        {
            "type": "divider",
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"<{report_url}|This report is updated regularly on the engineering webserver.>",
            },
        },
    ]
    data["blocks"] = json.dumps(blocks)
    r = requests.post(slack_url, data=data, headers=auth_headers, timeout=120)
    print(r.text)


def create_report(html_path: Path, slack_channel: str):
    interval_sec = 60 * 60 * 24 * 7 * 4
    now = time.time()

    fig = plt.figure()
    ax = fig.add_subplot(212)
    ax.set_ylabel("GPU Count", fontsize=4)
    ax.set_title("Reserved GPU Usage", fontsize=8, pad=10)

    ax2 = fig.add_subplot(211)
    ax2.set_ylabel("Utilization %", fontsize=4)
    ax2.set_ylim(0, 110)
    ax2.set_title("Reserved GPU Utilization %", fontsize=8, pad=10)

    lw = 1

    cw_usage_data = prometheus_usage_data(now, interval_sec, "cw-east4")
    gcp_us1_data = google_usage_data(now, interval_sec, "gcp-us1")

    colors = ["tomato", "lightblue", "orchid"]
    cw_gpu_max = int(os.environ.get("CW_GPU_MAX", 512))
    gcp_gpu_max = int(os.environ.get("GCP_GPU_MAX", 592))

    plot_data(ax, ax2, cw_usage_data, colors.pop(), "CW H100", cw_gpu_max)  # type: ignore
    plot_data(ax, ax2, gcp_us1_data, colors.pop(), "GCP-US1 H100", gcp_gpu_max)  # type: ignore

    y_max = max(gcp_us1_data.max(), cw_usage_data.max().value)
    ax.set_ylim(0, y_max * 1.1)

    ax2.axhline(
        y=75,
        color="green",
        linewidth=lw,
        linestyle=":",
    )
    ax2.axhline(
        y=50,
        color="blue",
        linewidth=lw,
        linestyle=":",
    )

    xfmt = mdates.DateFormatter("%y-%m-%d")  # %H:%M')
    ax.xaxis.set_major_formatter(xfmt)
    ax.xaxis.set_major_locator(mdates.HourLocator(interval=72))
    ax.tick_params(axis="x", rotation=60, labelsize=4, pad=2)
    ax.tick_params(axis="y", labelsize=4)
    ax2.xaxis.set_major_formatter(xfmt)
    ax2.xaxis.set_major_locator(mdates.HourLocator(interval=72))
    ax2.tick_params(axis="x", rotation=60, labelsize=4, pad=2)
    ax2.tick_params(axis="y", labelsize=4)
    ax.legend(loc="best", fontsize=4)
    ax2.legend(loc="best", fontsize=4)

    # Adjust bottom margin to ensure labels don't get cut off
    plt.subplots_adjust(
        bottom=0.2,  # bottom margin
        hspace=0.5,  # height space between subplots
        top=0.95,  # top margin
    )

    gcp_util_pct = (gcp_us1_data / gcp_gpu_max) * 100  # type: ignore
    cw_util_pct = (cw_usage_data / cw_gpu_max) * 100  # type: ignore

    gcp_7da_span = tabular_data(gcp_util_pct.rolling(window="7D", min_periods=1).mean())
    cw_7da_span = tabular_data(cw_util_pct.rolling(window="7D", min_periods=1).mean())
    cw_30da_span = tabular_data(cw_util_pct.rolling(window="30D", min_periods=1).mean())  # type: ignore
    gcp_30da_span = tabular_data(
        gcp_util_pct.rolling(window="30D", min_periods=1).mean()
    )

    table_7da = f"""
    <table style="border-collapse: collapse; margin: 10px 0;">
        <tr style="border-bottom: 1px solid #ddd;">
            <th style="padding: 8px; text-align: left;">Cluster</th>
            <th style="padding: 8px; text-align: left;">GPU Count</th>
            <th style="padding: 8px; text-align: left;">7-day Average Utilization</th>
            <th style="padding: 8px; text-align: left;">30-day Average Utilization</th>
        </tr>
        <tr style="border-bottom: 1px solid #ddd;">
            <td style="padding: 8px;">GCP-US1 H100</td>
            <td style="padding: 8px;">{gcp_gpu_max}</td>
            <td style="padding: 8px;">{gcp_7da_span}</td>
            <td style="padding: 8px;">{gcp_30da_span}</td>
        </tr>
        <tr style="border-bottom: 1px solid #ddd;">
            <td style="padding: 8px;">CW H100</td>
            <td style="padding: 8px;">{cw_gpu_max}</td>
            <td style="padding: 8px;">{cw_7da_span}</td>
            <td style="padding: 8px;">{cw_30da_span}</td>
        </tr>
    </table>
    """

    # plt.legend(loc="best", fontsize=8)
    html_path.mkdir(parents=True, exist_ok=True)
    print(f"Writing {html_path/'gpu_usage_plot.png'}")
    plt.savefig(html_path / "gpu_usage_plot.png", dpi=300, bbox_inches="tight")
    today = str(datetime.fromtimestamp(now))
    with (html_path / "index.html").open("w") as f:
        f.write(f"""
        <html>
        <body>
        <p>Page updated {today}</p>
        {table_7da}
        <img src="gpu_usage_plot.png" style="width: 80%; height: auto;">
        </body>
        </html>
        """)

    if slack_channel:
        slack_report(
            slack_channel,
            gcp_util_pct,
            cw_util_pct,
            gcp_gpu_max,
            cw_gpu_max,
            html_path / "gpu_usage_plot.png",
        )


if __name__ == "__main__":
    parser = ArgumentParser()
    parser.add_argument("--write-html", type=Path, required=True)
    parser.add_argument("--slack-channel", type=str, default="")
    args = parser.parse_args()
    create_report(args.write_html, args.slack_channel)

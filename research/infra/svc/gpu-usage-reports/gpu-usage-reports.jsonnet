local clusters = import '../../cfg/clusters/clusters.jsonnet';

local hd_py = importstr 'gpu-usage-reports.py';
local requirements = importstr 'requirements.txt';

local marcmac_slack_id = 'U04DW26G9U4';
local augment_eng_channel_id = 'C0439560PAR';
local invocation_args = '--write-html /mnt/efs/augment/public_html/gpu_usage_reports';
local full_args = invocation_args + ' --slack-channel ' + augment_eng_channel_id;
local job_args = invocation_args + ' --slack-channel ' + marcmac_slack_id;

function(cluster_name, version, job_inst='') local C = clusters.cluster(cluster_name); C.k8s + {
  by_cluster(obj)::
    if std.objectHas(obj, cluster_name)
    then obj[cluster_name]
    else std.get(obj, 'default'),

  filter_by_cluster(obj):: {
    [kv.key]: kv.value
    for kv in std.objectKeysValues(obj)
    if !std.isObject(kv.value) || std.member([null, cluster_name], std.get(kv.value, 'cluster_name'))
  },

  local as_job = job_inst != '',
  name:: 'gpu-usage-reports',

  monitoring_sa: {
    local sa = self,
    gsa: $.GCP.IAM.ServiceAccount + {
      name:: $.name + '-sa',
      description:: 'Monitoring reader',
      project_id:: C.gcp_project,
      namespace:: 'cc-' + cluster_name,
    },
    sacct: $.ServiceAccount + {
      name:: sa.gsa.name,
      namespace:: 'devex-metrics',
      metadata+: {
        annotations+: {
          'iam.gke.io/gcp-service-account': sa.gsa.email,
        },
      },
    },
    wi: $.GCP.IAM.PolicyMember + {
      name:: sa.gsa.name + '.workload-identity-user',
      namespace:: sa.gsa.namespace,
      spec+: {
        resourceRef: sa.gsa.localKindRef,
        member: 'serviceAccount:%s.svc.id.goog[%s/%s]' % [
          C.gcp_project,
          sa.sacct.metadata.namespace,
          sa.sacct.metadata.name,
        ],
        role: 'roles/iam.workloadIdentityUser',
      },
    },
    roles: [
      $.GCP.IAM.PolicyMember + {
        name:: sa.gsa.name + '.' + C.gcp_project + '.reader',
        namespace:: sa.gsa.namespace,
        spec+: {
          resourceRef: {
            kind: 'Project',
            external: 'project/' + C.gcp_project,
          },
          memberFrom: self.memberFromSA(sa.gsa),
          role: 'roles/monitoring.viewer',
        },
      },
      $.GCP.IAM.PolicyMember + {
        name:: sa.gsa.name + '.' + C.gcp_project + '.storage',
        namespace:: sa.gsa.namespace,
        spec+: {
          resourceRef: {
            kind: 'Project',
            external: 'project/' + C.gcp_project,
          },
          memberFrom: self.memberFromSA(sa.gsa),
          role: 'roles/storage.objectUser',
        },
      },
    ],
  },

  cw_token: C.k8s.SealedSecret + {
    name: 'cw-prometheus-token',
    namespace: 'devex-metrics',
    encryptedData: {
      token: 'AgAgPvK7AvA3OKvRBZDT535pXAqcwfNVirRh/CuTE0RGWvYPj70APR+GjVlASBLWQolzCiN5x1HaFIQ1Dt/K/AsFcu+arLtbgVWiXfZd93r7HC1lCcwxOHmtWfuUYuDsWxJg85f7ntGhhIpOg3T7GlwI6lzZZU/8tIxaKmoDY7QEgAydAIXq/iycNMlZRqIOszV3HHgrFUkV9PGl8WZKWBtYMEMIcRGcUdNtMzf8pNTPzjc9Dpfolp5MDwlaFizpvesQiYXrLy+sMnW0dKz64kGSbLVENdmeMHg7rEAHb+vI2ZBouN7jyozBCTkQSehWsqa2xUyIuRbZuoOiobrPPEE7MJiKINrFiA6kxxrtKjEbbUrxo+UQFM8frKpswc4KEmSuKET0f/BrJsmaA7Jc1na5k0yUbXqVgDZhvp0X40q/gyLf6Lq9R7/bANXnmN+u0/jrQrCALlsRZDeJTJuMrJtnlDrOfAmkoZAPtMvWOdjNlu5zfh6JCWZx+xJ/h3ZBeJNJu0DbR3iekP1qUK2BXj4N8QyQR9A5Maq892EFKmxF4CJM0sEV8Q+VmGvceb97SizSKV5lZlkIeDaFESqB6bkwpqAwZgerP57xn1/8qgC3lR0NY6+g0C2i4gjqhmbMffsJjVfGVcw4jDPt6TYhq1c/6+yfc7g4JHAmxjlWEdTAntChwKZ2EtfQFy1tfRUfU3xtla0y/qHfB9LJu2rXS3JbLJ6aITwmtL+Zy3IKn7S64anH66gbz1vp',
    },
  },
  slack_bot_token: C.k8s.SealedSecret + {
    name: 'slack-bot-token',
    namespace: 'devex-metrics',
    encryptedData: {
      token: 'AgCtz9K9rL8admvAYht68476xUI3Z1oKhOf5LtyxKeAWW1HKKdZ+BhMpmgejjW0SR+Src0tW/NtlMcT01xONz/b8V/uhQFauAeCQNsEyXSGRThhz1pPNLblj6JgNX80quWqKxd8Dv4Ye7Xx6k0RSNRENMqTippRzspONKMGEOviDx1hFBmVubnehCWRvz2tkcc/ZoVqB/oERPR99DtENGhZQCt8ZWaAbCvagVTca1/Lv9HBblWUVwez4WEq+V4dNDl/xRXhk5aiILZLdY4+W5tG6sqwByYQadbxfFTnkUCXlfzCSWFDaaxd9qNKW+1sIOWtl5ON7TURZ2Q4pIzXLa5Juz/lT26/tK7nhQrrZEJxVtDt5gnhl/bGyTiwLH2FSaqbRuu/CqXIQkXg7qSZNT9X7d9LJvUxJqTgb9F/S4BYCgCUcnVaObNhFN57YUoX0yzWTQsFYH/YEJBbMZ9ZYkIUeJutJgXsz09aj9qK/pXO8fpCxdih48Wd7+JeiBcK2ZnbSsEfrzKyQ1FgM/dFnd62hwnRw/L/ER6FaKoCtumfwPprwGNpzWDJF9Ur9pr0WAjtSoTw19snIpCcc5tdD/KcMuRRu1JVzxlNOUEbrizeOeq5LIXTbCR/d2RvF/FAFKn/QYzwAva/dFgP9tLdEoLkIW9LVG+zDvzoPLevWuc7FoP8lG2xAcoNMYDnmqYsFvo9wkXcGxbajTHbchzqCRKNqMIz2SZ/8aEIjd9poeTMkws50YsyTb97p99LndI6FsGXguW32RUUheTM=',
    },
  },

  configmap: C.k8s.ConfigMap + {
    name:: $.name,
    namespace:: 'devex-metrics',
    data: {
      'gpu-usage-reports.py': hd_py,
      'requirements.txt': requirements,
    },
  },
  pod_env:: [
    {
      name: 'SLACK_BOT_TOKEN',
      valueFrom: {
        secretKeyRef: {
          name: $.slack_bot_token.name,
          key: 'token',
        },
      },
    },
    {
      name: 'CW_PROMETHEUS_TOKEN',
      valueFrom: {
        secretKeyRef: {
          name: $.cw_token.name,
          key: 'token',
        },
      },
    },
    {
      name: 'CW_GPU_MAX',
      value: '512',
    },
    {
      name: 'GCP_GPU_MAX',
      value: '336',
    },
  ],
  job_env:: $.pod_env + [
    {
      name: 'INVOCATION_ARGS',
      value: job_args,
    },
  ],
  weekly_env:: $.pod_env + [
    {
      name: 'INVOCATION_ARGS',
      value: full_args,
    },
  ],
  daily_env:: $.pod_env + [
    {
      name: 'INVOCATION_ARGS',
      value: invocation_args,
    },
  ],
  basespec:: {
    completions: 1,
    template+: {
      metadata+: {
        annotations+: {
          'gke-gcsfuse/volumes': 'true',
          'config.md5': std.md5(std.manifestJson($.configmap.data)),
        },
      },
      spec+: {
        local pod = self,
        restartPolicy: 'Never',
        serviceAccountName: 'gpu-usage-reports-sa',
        containers+: [
          {
            name: $.name,
            image: 'python:3.11-slim',
            imagePullPolicy: 'IfNotPresent',
            env: $.pod_env,
            args: [
              '/bin/bash',
              '-c',
              'pip install -r /run/augment/' + $.name + '/requirements.txt && python3 /run/augment/' + $.name + '/' + $.name + '.py ' + '$INVOCATION_ARGS',
            ],
            volumeMounts: [
              {
                name: $.name,
                mountPath: '/run/augment/' + $.name,
              },
              {
                name: 'gcs-gcp-us1-public-html',
                mountPath: '/mnt/efs/augment/public_html/gpu_usage_reports',
                subPath: 'gpu_usage_reports',
              },
            ],
          },
        ],
        volumes: [
          {
            name: $.name,
            configMap: {
              name: $.name,
            },
          },
          {
            name: 'gcs-gcp-us1-public-html',
            csi: {
              driver: 'gcsfuse.csi.storage.gke.io',
              volumeAttributes: {
                bucketName: 'gcp-us1-public-html',
                mountOptions: std.join(',', [
                  'implicit-dirs',
                  'uid=101',
                  'gid=101',
                  'file-mode=0444',
                  'dir-mode=0555',
                  'o=noexec',
                  'o=noatime',
                ]),
              },
            },
          },
        ],
      },
    },
  },
  jobspec:: $.basespec + {
    template+: {
      spec+: {
        containers: [
          super.containers[0] +
          {
            env: $.job_env,
          },
        ],
      },
    },
  },
  cronspec:: $.basespec + {
    template+: {
      spec+: {
        containers: [
          super.containers[0] +
          {
            env: $.weekly_env,
          },
        ],
      },
    },
  },
  daily_cronspec:: $.basespec + {
    template+: {
      spec+: {
        containers: [
          super.containers[0] +
          {
            env: $.daily_env,
          },
        ],
      },
    },
  },
  cronjob:: C.k8s.CronJob + {
    local cj = self,
    name:: $.name,
    schedule:: '@weekly',
    namespace:: 'devex-metrics',
    concurrencyPolicy:: $.CRONJOB_POLICY.REPLACE,
    metadata+: {
      labels+: {
        'app.kubernetes.io/name': $.name,
        'aug.version': version,
      },
    },
    spec+: {
      jobTemplate+: {
        metadata+: {
          labels+: {
            'app.kubernetes.io/name': $.name,
            'aug.version': version,
          },
        },
        spec+: $.cronspec,
      },
    },
    podspec:: {},
  },
  daily_cronjob:: C.k8s.CronJob + {
    local cj = self,
    name:: $.name + '-daily',
    schedule:: '@daily',
    namespace:: 'devex-metrics',
    concurrencyPolicy:: $.CRONJOB_POLICY.REPLACE,
    metadata+: {
      labels+: {
        'app.kubernetes.io/name': $.name,
        'aug.version': version,
      },
    },
    spec+: {
      jobTemplate+: {
        metadata+: {
          labels+: {
            'app.kubernetes.io/name': $.name,
            'aug.version': version,
          },
        },
        spec+: $.daily_cronspec,
      },
    },
    podspec:: {},
  },
  crons:: [
    $.cronjob,
    $.daily_cronjob,
  ],
  job:: C.k8s.Job + {
    name: $.name + '-adhoc-' + job_inst,
    spec: $.jobspec,
    namespace:: 'devex-metrics',
    metadata+: {
      labels+: {
        'app.kubernetes.io/name': $.name,
        'aug.version': version,
      },
    },
  },

  cronjob_or_job: if as_job then $.job else $.crons,
}

local lib = import '../../cfg/clusters/base-resources/tmpl/lib.jsonnet';
local clusters = import '../../cfg/clusters/clusters.jsonnet';
local vendor = {
  '8.8.2': importbin 'grafana-8.8.2.tgz',
};

function(
  cluster='gcp-core0',
  name='grafana0',
  namespace='grafana',
  hostname='grafana.r.augmentcode.com',
  version='8.8.2',
) local C = clusters.cluster(cluster); C.k8s + {
  is_gcp:: std.startsWith(C.name, 'gcp-'),

  ns: $.Namespace + {
    name:: namespace,
  },

  BaseLabels+:: {
    'aug.service': name,
  },

  Object+:: {
    name:: name,
    namespace:: $.ns.metadata.name,
  },

  GCP+:: {
    Object+:: {
      namespace:: 'cc-' + C.name,
    },
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Helm Release
  //

  // Use "existing" adminUser secret to stabalize pw.
  admin: $.SealedSecret + {
    name+:: '-admin',
    spec+: {
      template+: {
        data: {
          'admin-user': 'admin',
        },
      },
    },
    encryptedData: {
      'admin-password': 'AgACDoyPOEiSGHtRzZu28iwNlTOp+2XyYYhHp3tQnVUc+3ubsVDhqMDy9QJlrXyfNZTxQ4MeI5D5a666Y+ECA6rqjCwY5iqJJ4HiVcv7UnD9Tnltq79VJzf6PAlF8H0nnY15jNDBbZjrxBJ9eTWzGK7QI/hCSPhpTvkuIhaRy9RR6P5ous4txrmqREu7yDkW9/Jf4VUucJDzQxx72A9qWIZ26fyTJdeSWOrowM/eIVINGOA/wkMDXfy077lrftUmGqcMX/HTsso7cy3BAih96XyxN+f+Hp5trnBbS+H34RCsofmZzxIggfEZ92it/hwUEKZ/XA7hwSgWhWhVabSR7CA26WY43kjzfxf/yGYNxPPwpbrDehBam4PWf6a32B/2BIkihVF21yEyEdQXfSW0zbNIKjIB9qx98yZykesOUZxWU5bc86na1MCXzzk1C9M/yfVNz2bo+dN9I1SEdxzu6kG03/pzxpdBpzHqXIL8rx4HMlJoyAC2JGOtiLGbO8qlBemCLQf/DTROKtajp+YThJLHnu2rT7OjKfEW6Z4xlIYd3MRtFznRL3WRhuaiexj4jxJVRiEByEt+udolytl0/5hIFh0VraBTxWAXNfBiUvbVUQWhD8NHcDGz6mu9XFnkhiSGwz7WOWn32Roob9Kd9PNNZvBbm7NasZHtVxUbb8iszuX6vlWStbtS3oFe1MmFXx++XQnQlxa46G+pPwIH6KHms/AsGtavrPAAEYFHMcLdr7L3mKgVNvJL',  // pragma: allowlist secret
    },
  },

  oauth: $.SealedSecret + {
    name+:: '-oauth',
    spec+: {
      template+: {
        data: {
          client_id: '670297456363-98edhoieeanqethha7dpl5hde68tm0g8.apps.googleusercontent.com',
        },
      },
    },
    encryptedData: {
      client_sec: 'AgAG2EajkfuVr1c69SCdutNAR4gnnPKBsuqco7dRAdmATO99XNRjA9kxE78cqldSOagm3fBCz/rcvgqT9etDopKsZwjYUDn2D+ZXDTE7hR6Y9Bb7fa4oV63mJqVJBU9VLMZJuB3AsT+UbUnP+ojrTZG8wcK4PJzmb7CjPUdT6beqAmOP/uR3FECzDqoAenhAhFmwswaGuaFl6Ajdl/yYLVCGSJYAAtHGM+A0I6ZIGPKgQJSchs0nevhZQazPpIbzkPZxm4+3sgRcpEmvIn7pIJ0lBA2zR5Jo6GnePzjl4ODXp6RE6Ab8fW+mL9WE9ImsWgeArf+WV45aXj5F/3M9kx16WBQpLK2OTOgdOQhN6SDJDBMHvQOt+veYuPyE0NBIuBW4J5w7WhOVdUVElJAo8RcyCmsCtnGDS+VDMRLv7PLYDi3rpf7lc0Kn5FBV6a9tNzKFQmqrdK18BfCdEJkofQCKoXVAeDgSggRBZmkLY1/8O75iNC6WQrCDXPBKCFygyBJKhJOMw1zK1NjrbAChrDJIdEXVGhYgDNll8dUUl0VwRS2xKZah5NgVFPqipavNxZviqLtH2iu1Dp7a2inyTSZNZ/DFPIDxN3rUuLmu2MedMuLIojQp7OXaXzh3KsNuA+0W89WnXK9J8/KUlmLaHwwFJfxKVlK81ELbc8LWou0Hhib/Yy83hsZkjv3WQuCWZxtx9a5f5UCoDFJGZIa8EnLt4gNtBoMAAg4YsMT6blUP3bx+PQ==',
    },
  },

  release: lib.ParseHelmChart(vendor[version], name, namespace, values={
    rbac+: { namespaced: true },
    deploymentStrategy+: { type: 'Recreate' },
    enableServiceLinks: false,
    service+: {
      type: 'ClusterIP',
    },
    ingress+: {
      enabled: true,
      annotations+: {
        'cert-manager.io/cluster-issuer': 'letsencrypt-prod',
      },
      ingressClassName: 'nginx',
      hosts: [hostname],
      tls: [{
        hosts: [hostname],
        secretName: name + '-tls',
      }],
    },
    serviceAccount+: {
      annotations+: {
        'iam.gke.io/gcp-service-account': $.gcp_datasource.gsa.email,
      },
    },
    persistence+: {
      type: 'pvc',
      enabled: true,
      storageClassName: if $.is_gcp then 'premium-rwo' else 'shared-vast',
      size: '10Gi',
    },
    initChownData+: { enabled: false },
    extraSecretMounts+: [
      {
        name: self.secretName,
        secretName: $.oauth.metadata.name,
        mountPath: '/run/augment/secrets/' + self.secretName,
        defaultMode: std.parseOctal('0440'),
        readOnly: true,
      },
    ],
    admin+: {
      existingSecret: $.admin.metadata.name,
    },
    'grafana.ini'+: {
      server+: {
        enable_gzip: true,
        domain: hostname,
        root_url: 'https://' + self.domain,
      },
      security+: {
        // Required for sidecar. Set GH_SECURITY_DISABLE_ADMIN_CREATION if enabling to disable K8s secret.
        disable_initial_admin_creation: false,
        cookie_secure: true,
        strict_transport_security: true,
      },
      auth+: {
        disable_login_form: true,
      },
      'auth.google'+: {
        enabled: true,
        hosted_domain: 'augmentcode.com',
        allowed_domains: 'augmentcode.com',
        auth_url: 'https://accounts.google.com/o/oauth2/v2/auth',
        token_url: 'https://oauth2.googleapis.com/token',
        api_url: 'https://openidconnect.googleapis.com/v1/userinfo',
        client_id: '$__file{/run/augment/secrets/' + $.oauth.metadata.name + '/client_id}',
        client_secret: '$__file{/run/augment/secrets/' + $.oauth.metadata.name + '/client_sec}',
        scopes: std.join(' ', [
          'openid',
          'email',
          'profile',
          'https://www.googleapis.com/auth/userinfo.profile',
          'https://www.googleapis.com/auth/userinfo.email',
          'https://www.googleapis.com/auth/cloud-identity.groups.readonly',
        ]),
        use_pkce: true,
        skip_org_role_sync: false,
        allow_assign_grafana_admin: true,
        role_attribute_path: "contains(groups[*], '<EMAIL>') && 'GrafanaAdmin' || 'Viewer'",
      },
    },
    sidecar+: {
      datasources+: {
        enabled: true,
        label: 'grafana.sidecar.datasources',
        labelValue: name,
        resource: 'secret',
      },
      dashboards+: {
        enabled: true,
        label: 'grafana.sidecar.dashboards',
        labelValue: name,
        resource: 'configmap',
        folderAnnotation: 'grafana.sidecar.folder',
        provider+: {
          foldersFromFilesStructure: true,
        },
      },
      alerts+: {  // alerts includes alerting rules, contact points, notification policies, mute timings, and templates
        enabled: true,
        label: 'grafana.sidecar.alerts',
        labelValue: name,
        resource: 'both',
      },
      notifiers+: { enabled: false },  // notifiers are *not* notification policies
      plugins+: { enabled: false },
    },
    testFramework: { enabled: false },
    imageRenderer+: {
      enabled: true,
    },
  }),

  //////////////////////////////////////////////////////////////////////////////
  //
  // Datasources
  //

  datasources: $.SealedSecret + {
    name+:: '-datasources',
    spec+: {
      template+: {
        metadata+: {
          labels+: {
            'grafana.sidecar.datasources': name,
          },
        },
        data+: {
          'sidecar-datasources.yaml': |||
            apiVersion: 1
            prune: true
            datasources:
            - uid: devex_metrics
              name: DevEx Metrics
              editable: false
              type: postgres
              url: devex-metrics.r.augmentcode.com
              access: proxy
              user: augment
              jsonData:
                database: metrics
                sslmode: require
                timescaledb: false
                postgresVersion: 1700
              secureJsonData:
                password: {{index . ".devexpw"}}
            - uid: metastore
              name: MetaStore
              editable: false
              type: postgres
              url: metastore.gcp-us1.r.augmentcode.com
              access: proxy
              user: augment
              jsonData:
                database: metastore
                sslmode: require
                timescaledb: false
                postgresVersion: 1700
              secureJsonData:
                password: {{index . ".metastorepw"}}
            - uid: coreweave
              name: CoreWeave
              editable: false
              type: prometheus
              url: https://prometheus.ord1.coreweave.com
              access: proxy
              basicAuth: false
              jsonData:
                httpMethod: POST
                httpHeaderName1: Authorization
              secureJsonData:
                httpHeaderValue1: Bearer {{index . ".cwpromtoken"}}
            - uid: cw-east4
              name: CW-EAST4-Prometheus
              editable: false
              type: prometheus
              url: https://prometheus.ord1.coreweave.com
              access: proxy
              basicAuth: false
              jsonData:
                httpMethod: POST
                httpHeaderName1: Authorization
              secureJsonData:
                httpHeaderValue1: Bearer {{index . ".cwe4promtoken"}}
            - uid: gcp
              name: GCP
              editable: false
              type: stackdriver
              access: proxy
              jsonData:
                authenticationType: gce
          |||,
        },
      },
    },
    encryptedData: {
      '.cwpromtoken': 'AgB6LltlTGPfG/jYwLiAl/ztuudWqOWBagM+W4h4+LJhkLSZW0d1EHjP9xhA4+l0QgAmJJF8ErwbKJAI0HSExb3DShCAI1Gq7817tbx3RCJBMrqOnJHub8IKTag74582103jSGTEf9dCrOVljEZoxS82MpGuw0mh7QxA8J3iMxpIWsHRn6RSkC0NOLbPUo1+I4rVHZBuO6Mtw6a91nz94TC3vPkrpVS8EHCjYwAfVTyN5lO5RGGrJR8FOGz/YVcaDiXLYpZkavaovOT3PvpuAGqQjVFhwAe362+T1QWUu6nPMVkRcmHO0a4XFc17R98z//DuzwaTlFlyBJ1XkL13HZp0Oxup5B9evI8fmTZ3rqT5+4HAbRhUi/FHO2M1xAREXWsm4LgyXomX7c//7wKjNKmd6vvibTPLBbk9niU1eGHYCEjrWpYGth0hceFrr2+er0lXDYBPLW/H/Ymx9thPAo/uy838ffrUAU4ZQ5eehugMvE2IHOl3bFE5gsenYe5bMfI2R/9oZTguDd6uabprnAP6U6RP3rWHIzOIwG5U3wzpGNues57D6brFKymqmjsYgKW4VGVF08wc1JBgFCKhsTl4YlhP5QkoytHA9nKPO0l6kdXXJwY3d5LB3OYHvKQCYwdRa1tEkhiAvR0vuwtovoyVZtGOiDIdY3OrYPfGvERNujjjEuCMmzRQurpBKqNmJyYUPE2TwN2eadHpBj2aOmzCMBfKENia9jOklMPgjLUMsKPTUCCiyvli',
      '.cwe4promtoken': 'AgAyynQ9Gu97+WoTiLVUIlTt+exrFcdnhWO6wH1uiVZepgibJ1a+xi1xVCgXecoPK0Oo4uHsYzhvEMNajuVOZXqLorw0nkbo2bldjI3wDMcz5mp2dsN6NgRjlUHB+dYBqQ0q3YwGACglFieRVAoR+wShc72dRM4olMBRM24/jUdGlBt4belgxurUpxSZOX6dgDn2g35980rxP2kgxxcxcuq8X3T3nbQwA0ypDIUXaeMn1vDY5hSEOkMu3ZN26vC9HbqZJUZ3c9IOZqJLnvqy9P7828Rf7e5y+CfsDu8GOZpi45bYK8sZh6GljWAOT+hIUha0BNlVQevR8uqIjuPG+Esw3ljSewxB3eBjx5mvw1YLuXHQVqvH6CY4pSx+yRxfraTlLX+RK/qldDzDSTg6tDwcW5uoaFHqkf4z+bibmw1GHxzW9LOTNy9kuTp/oZFWj8m3f3bbCtfP3nC9owPc+FdUC6GD4T8JwUJ1m+8FqFGpFzYm2aI8sv+LS3i7QZ/wl6Fr1DH/2FPYEsWSagj55J78ES/a2NVgJ/VpooKetajEXsQhZlWCbDtnU5nf4GU5jzNBgAhPEOFEkzTYZIHnXfb4+hQXpbc6ai+dyM8FNxsEaQ/RB1ngHGsITWRKejW3w2/vlFSK+5DSMGPUg1n5WsDGJ8hJrZPnevlvpdYC7ewjC6ERLSwWT6WGnQxVvGlARMP3lRH3qn4p6osxnQ2bMIfAZLWpv9gkJ2ibcEb+jglnIBKU9+5vCeFriQ==',
      '.metastorepw': 'AgB+rNxaPIj/fFXHB1tPksJdyPYLKYYhTLNWU2X0V7h1yCxCbQByoGpyon9AhCvpnMaWZeQmVlnFKJNxaI0EXhfjhMTPfyM6cyPgQocUzwRPiTE5dOlFVeoe97QJm6nMdpBAeO7j7brGEgbPdP5eiqqBulTTcCOvzRNIMnITcYY1owOSy4meKdJBwFeld85Lc8LfCWXKkng6+vcPntUBocbt0CXd4WBb2YUH3gPIUxrm9FmSZT9eDLijY1LtnH9IyvBGjYgS50muYaSTwY3c3Xp03nCzlzTWO0s8uorrPdxoOR4WR0OJilM//0Od7PShHAYqGxOQrB9K2cuZ7JstKN1VO5b3WdauKA11D3Zc3bIMu8XPOnQBszwXHVcwSvt3ipgdk5TdkfRPOQVFr+dzlmnpfxodG+ykPvA4kqRWP0lS0GRs3vVVd2Zwv4PIzx6awiMzTfZlgTy94TJPzPk8XSX8SmP0dP9OoZBUqyNcGDSIeck8hvgW/s1qKe0FB56X8gYVK/WLZbUQimcASKsKEcBXIcX2qvlcpZ7fZZzgYMnhrfuWgpaExLnVtvtC0wiOjW00U+Mvh09gJlJTBEPdCU3NOQeZeLn3Rp2VjUdbMewX4FI7m+vlY8wfvHcFMTZ29unfebotwp/lxLVNdZrHrJvNNMgFLKIq6W1chc3+VVKA3gMUHenLDwMOQ8hI3w8oIz4Z4k6bdb80Z1ja',
      '.devexpw': 'AgCOWvidYEMrtnmK3p0Cj95W/FAmeXEVWQZsTh7e+2ceg1Xq+GgItWPz0BsT+uIYvQKgbpKOfZVxzAC+VMq5sLfRjf20sPiBSVo+ZKh9qxTbMWVuiWOzYGdgxM+i9A33K4jJYAKGYpuV9/KsIQVH6v2GI/7iUktV4+l0aNIDY652yUG9fvd3ibYUzfsRTAmX2++UeOawR8H/95yzotX/JzZvFzGbuh4WvH92Xw1Qfgx9xHyDlPzhIJsMbeJsvg+85yDtdtPhBclBrpect8a69iQYmE8Wbh47hlaZ2EhjCz35tIbo+dIDrAt9P6peSMLVU4Rl29GXaSpZ4SYZB9IaFt1Y4r7oe3P9DyPodO8o/HzCbhESCp2PDqGUUZc9sg4luDiFtjgKydBjSxzQ7YjY96oZIbwm7l2gtrsKZvCGTAvBmM8uDLs7L6dLJd5lbueYbrzPAebRAtiJCuK/lf690OEcDA3NMAoB6I2FIco47XHID7+W4jhiz8GCMXQfIKVAM/YvyzZAiRtYA00b3rDUR8U3srDx/TA+8KVsNP/VtALNx9Al9Rj51DYXF4MX/nEkk8T55LqnDu47g4nqnOyrx+JK6P9ldf10UijApQOkhPZVwjYsN/0rHUt/I9EF2PhIF1Zuko93VTOzr5Cv+CuZKZ1wdHRvL3aYtGEgocl1WgxPI0q1TIXsyYIlhQHonY9tqx1rc2fMeU4PdQzHvIg4BuG/94tv4bLqPPCiXJyP1A==',
    },
  },

  gcp_datasource: {
    local ds = self,
    gsa: $.GCP.IAM.ServiceAccount + {
      name:: C.name + '-grafana',
      description:: 'Grafana GCP ("stackdriver") DataSource',
      project_id:: C.gcp_project,
    },
    wi: $.GCP.IAM.PolicyMember + {
      name:: ds.gsa.name + '.workload-identity-user',
      spec+: {
        resourceRef: ds.gsa.localKindRef,
        ksa:: {
          // TODO(mattm): Get this from $.release instead.
          metadata: {
            name: name,
            namespace: $.ns.name,
          },
        },
        member: 'serviceAccount:%s.svc.id.goog[%s/%s]' % [
          C.gcp_project,
          self.ksa.metadata.namespace,
          self.ksa.metadata.name,
        ],
        role: 'roles/iam.workloadIdentityUser',
      },
    },
    roles: [
      $.GCP.IAM.PolicyMember + {
        name:: ds.gsa.name + '.' + C.gcp_project + '.reader',
        spec+: {
          resourceRef: {
            kind: 'Project',
            external: 'project/' + C.gcp_project,
          },
          memberFrom: self.memberFromSA(ds.gsa),
          role: 'roles/monitoring.viewer',
        },
      },
    ],
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Dashboards
  //

  _dash_folder:: $.ConfigMap + {
    local _b = self,
    name+:: '-dashboards-' + std.strReplace(std.asciiLower(self.folder), ' ', '-'),
    folder:: null,
    metadata+: {
      labels+: {
        'grafana.sidecar.dashboards': name,
      },
      annotations+: std.prune({
        'grafana.sidecar.folder': _b.folder,
      }),
    },
    data: {},
  },

  dashboards: (import 'dashboards/dashboards.jsonnet')(cm_tmpl=self._dash_folder),

  //////////////////////////////////////////////////////////////////////////////
  //
  // Alert Rules
  //

  alerts: $.ConfigMap + {
    name+:: '-alert-rules',
    metadata+: {
      labels+: {
        'grafana.sidecar.alerts': name,
      },
    },
    data: (import 'alerts/alerts.jsonnet') + {
      'delete.json': std.manifestJson(import 'alerts/delete.jsonnet'),
    },
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Contact Points
  //

  contacts: $.SealedSecret + {
    name+:: '-contacts',
    spec+: {
      template+: {
        metadata+: {
          labels+: {
            'grafana.sidecar.alerts': name,
          },
        },
        data+: {
          'sidecar-contacts.yaml': |||
            apiVersion: 1
            contactPoints:
            - name: Augment Slack
              receivers:
              - uid: slack_infra_alerts
                type: slack
                disableResolveMessage: true
                settings:
                  recipient: C051BBASJF4
                  username: Grafana
                  icon_emoji: ":robot_face:"
                  token: {{index . ".slack-infra-alerts-token"}}  # NOTE(mattm): I was not able to get this to work with 'secure_settings'
            - name: Augment Slack (#team-chat)
              receivers:
              - uid: slack_team_chat
                type: slack
                disableResolveMessage: true
                settings:
                  recipient: C06R495KUSD
                  username: Grafana
                  icon_emoji: ":robot_face:"
                  token: {{index . ".slack-infra-alerts-token"}}
          |||,
        },
      },
    },
    encryptedData: {
      '.slack-infra-alerts-token': 'AgBc6ys1vGoovKTY+8F7M6Dua55LVskL9C35sVM3ZlSZSe8EXsiwRlJ2cLmgx5WizystCMzQNwuNjtFz/DnpvZgF1qWOUwCTuEiKlqwPkx4BJ5W0w6uD9FyMquvBavy9+gqNpYohOAWMRfkALW7pLxvBIGXRTKUcsmgNRPQZ27SClAmJVXGC00tI+W83sf5cvFXvc5EtYfjtnKUhtHAFXWc5IkMP4g1yehQy0iZkNbedd7uX+kjWqtu1cqgUZB6gLNoNYIRtb0pdq3m87KPJ0wckLyzhjaaJjja+SV41nZLGtyr9KybFKPfuwBY25mcxmreqi7gCmnwAPhsPGCzsgruODZgwvXxmk/8t4Ufxjw90Vple35+V4t+O1WVyzPhwp/Xv0QEPDmVDvbD81F4toeLtXDJ34wokTkHKmCQSg5HykaYSm+Qlc2Rhr4eWeOM+ZWxWBtGy6ZvIR9KT1BlFcwsj+Fq+MgIkOe3izRSB6oykYaykBms4iQx8+8rrnszW/RltLawRNUjgpoSgqD1pv7gpNyxnLeuv5JxRi7Cc8FqNNb0Qa3sr7XzLHOjSiN0wkzL3WT2MT7sMvYogjbPhFOBvGyNvSTqKf124sorK96vaWfJKD5UCm4Mm1SWm6vbzFabR8t/XFcpbgF0I8kpb8CYs84lZkb7d1/4ZXfj67R6x+M2TILySzHe8gIWEeaPKlcrd1jPx8eBmIsmiAW17LAPbPhQwV2TX3IPKjoJ430qyTD4X2Xgxn3WH9jR6T4ZNaETPjhu8VF5fL+c=',
    },
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Notification Policies
  //

  notification_policies: $.ConfigMap + {
    name+:: '-notification-policies',
    metadata+: {
      labels+: {
        'grafana.sidecar.alerts': name,
      },
    },
    data+: {
      'sidecar-notification-policies.yaml': |||
        apiVersion: 1
        policies:
         - receiver: Augment Slack
           # (default) group_by: [grafana_folder, alertname]
           # (default) group_wait: 30s
           # (default) group_interval: 5m
           # (default) repeat_interval: 4h
           routes:
            - object_matchers: [["routing", "=", "Hourly"]]
              repeat_interval: 1h

            - object_matchers: [["routing", "=", "Constant"]]
              group_wait: 5s
              repeat_interval: 10m

            - object_matchers: [["severity", "=", "testing"]]

      |||,
    },
  },

}

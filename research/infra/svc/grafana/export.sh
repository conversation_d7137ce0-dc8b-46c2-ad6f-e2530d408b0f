#!/bin/bash

# TODO(mattm): This script is currently designed to export from the old Grafana to git for use in the new Grafana. Update
# it to use as a way to export manually updated dashboards from the new Grafana back into git.

set -eu -o pipefail

_main() {
	declare -r HOSTNAME="grafana.r.augmentcode.com"
	declare -r PW="$(_get_sec "gcp-core0" "grafana" "grafana0-admin")"

	declare -A _DASHBOARDS
	while IFS=$'\t' read -r uid uri fuid ftitle; do
		_export_dashboard "$uid" "$uri" "$fuid" "$ftitle"
	done < <(_api_call "search?type=dash-db" | jq -r '.[] | [.uid, .uri, .folderUid, .folderTitle] | @tsv')
	declare -r _DASHBOARDS

	declare -A _FOLDERS
	while IFS=$'\t' read -r uid title; do
		printf "INDEXING FOLDER [%s] %s\n" "$uid" "$title"
		_FOLDERS["$uid"]="$title"
	done < <(_api_call "folders" | jq -r '.[] | [.uid, .title] | @tsv')
	declare -r _FOLDERS

	declare -A _ALERTS
	while IFS=$'\t' read -r folderuid group uid title; do
		_export_alert "$folderuid" "$group" "$uid" "$title"
	done < <(_api_call "v1/provisioning/alert-rules" | jq -r '.[] | [.folderUID, .ruleGroup, .uid, .title] | @tsv')
	declare -r _ALERTS

	printf "GENERATING dashboards/dashboards.jsonnet\n"
	_generate_dashboards_json | jsonnetfmt - > "dashboards/dashboards.jsonnet"

	printf "GENERATING alerts/alerts.jsonnet\n"
	_generate_alerts_json | jsonnetfmt - > "alerts/alerts.jsonnet"
}

_get_sec() {
	declare -r ctx="$1" ns="$2" sec="$3"
	kubectl --context="$ctx" --namespace="$ns" get secret/"$sec" --template='{{index .data "admin-password" | base64decode}}'
}

_api_call() {
	declare -r path="$1"
	shift 1
	curl -sL -u "admin:$PW" "https://$HOSTNAME/api/$path" "$@"
}

_export_dashboard() {
	declare -r uid="$1" uri="$2" fuid="$3" ftitle="$4"
	declare -r name="${uri##db/}"
	declare -r fname_l="$(echo -n "${ftitle,,}" | tr ' ' '-')"
	declare -r out="dashboards/$fname_l/$uid.jsonnet"

	printf "EXPORTING DASHBOARD [%s] %s to %s\n" "$uid" "$name" "$out"
	_DASHBOARDS["$ftitle"$'\t'"$uid.json"]="$fname_l/$uid.jsonnet"

	mkdir -p "$(dirname "$out")"
	_api_call dashboards/uid/"$uid" | jq '.dashboard | del(.id) | del(.version)' | jsonnetfmt - >  "$out"
}

_export_alert() {
	declare -r folderuid="$1"
	declare -r group="$2"
	declare -r uid="$3"
	declare -r title="$4"

	declare -r folder="${_FOLDERS[$folderuid]}"
	declare -r out="alerts/$folder/$uid.jsonnet"

	printf "EXPORTING ALERT [%s] %s %s\n" "$uid" "$title" "$out"
	_ALERTS["$uid.json"]="$folder/$uid.jsonnet"

	mkdir -p "$(dirname "$out")"
	_api_call v1/provisioning/alert-rules/"$uid"/export?format=json | jq 'del(.id) | del(.version)' | jsonnetfmt - > "$out"
}

_generate_dashboards_json() {
	declare t k kt kk v sf sd
	mapfile -d '' sf < <(printf '%s\0' "${_FOLDERS[@]}" | sort -fz)

	printf "// THIS FILE WAS AUTO-GENERATED BY export.sh.\n\n"
	printf "function(cm_tmpl) {\n"

	for t in "${sf[@]}"; do
		mapfile -d '' sd < <(printf '%s\0' "${!_DASHBOARDS[@]}" | sort -fz)

		printf "  '%s': cm_tmpl {\n" "$t"
		printf "    folder: '%s',\n" "$t"
		printf "    data: {\n"

		for k in "${sd[@]}"; do
			IFS=$'\t' read -r kt kk <<<"$k"
			[[ "$kt" == "$t" ]] || continue
			v="${_DASHBOARDS[$k]}"
			printf "  '%s': std.manifestJson(import '%s'),\n" "$kk" "$v"
		done

		printf "    },\n"
		printf "  },\n"
		printf "\n"
	done
	printf "}\n"
}

_generate_alerts_json() {
	declare sk k v
	mapfile -d '' sk < <(printf '%s\0' "${!_ALERTS[@]}" | sort -fz)

	printf "// THIS FILE WAS AUTO-GENERATED BY export.sh.\n\n"
	printf "{\n"
	for k in "${sk[@]}"; do
		v="${_ALERTS[$k]}"
		printf "  '%s': std.manifestJson(import '%s'),\n" "$k" "$v"
	done
	printf "}\n"
}

_main "$@"

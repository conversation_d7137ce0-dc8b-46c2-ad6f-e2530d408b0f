// THIS FILE WAS AUTO-GENERATED BY export.sh.

{
  'augment-qa-prober-data-age.json': std.manifestJson(import 'Alerts/augment-qa-prober-data-age.jsonnet'),
  'augmentqa_prober_answer_keyword_recall.json': std.manifestJson(import 'Alerts/augmentqa_prober_answer_keyword_recall.jsonnet'),
  'cceval-prober-data-age.json': std.manifestJson(import 'Alerts/cceval-prober-data-age.jsonnet'),
  'gh-runner-utilization0.json': std.manifestJson(import 'Alerts/gh-runner-utilization0.jsonnet'),
  'hindsight-prober-data-age.json': std.manifestJson(import 'Alerts/hindsight-prober-data-age.jsonnet'),
  'humaneval-instruct-prober-data-age.json': std.manifestJson(import 'Alerts/humaneval-instruct-prober-data-age.jsonnet'),
  'research_ci_runtime_too_high0.json': std.manifestJson(import 'Alerts/research_ci_runtime_too_high0.jsonnet'),
  'shared-volume-critical0.json': std.manifestJson(import 'Alerts/shared-volume-critical0.jsonnet'),
  'shared-volume-predicted0.json': std.manifestJson(import 'Alerts/shared-volume-predicted0.jsonnet'),
  'shared-volume-usage0.json': std.manifestJson(import 'Alerts/shared-volume-usage0.jsonnet'),
}

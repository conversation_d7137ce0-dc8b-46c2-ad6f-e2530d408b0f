{
  apiVersion: 1,
  groups: [
    {
      orgId: 1,
      name: 'eval-probers',
      folder: 'Alerts',
      interval: '5m',
      rules: [
        {
          uid: 'humaneval-instruct-prober-data-age',
          title: 'HumanEval Instruct Prober Data Age',
          condition: 'HumanEvalInstructRemoteProberDataAgeTooOld',
          data: [
            {
              refId: 'HumanEvalInstructRemoteProberDataAgeSignal',
              relativeTimeRange: {
                from: 604800,
                to: 0,
              },
              datasourceUid: 'devex_metrics',
              model: {
                datasource: {
                  type: 'grafana-postgresql-datasource',
                  uid: 'devex_metrics',
                },
                editorMode: 'code',
                format: 'time_series',
                intervalMs: 60000,
                maxDataPoints: 43200,
                rawQuery: true,
                rawSql: 'SELECT $__time(start_time), EXTRACT(EPOCH FROM (lag(start_time, -1, NOW()) OVER (ORDER BY start_time ASC)) - start_time)::real / 3600 AS "data_age" FROM humaneval_instruct_prober WHERE $__timeFilter(start_time) ORDER BY start_time ASC;',
                refId: 'HumanEvalInstructRemoteProberDataAgeSignal',
                sql: {},
              },
            },
            {
              refId: 'HumanEvalInstructRemoteProberDataAge',
              relativeTimeRange: {
                from: 0,
                to: 0,
              },
              datasourceUid: '__expr__',
              model: {
                conditions: [
                  {
                    evaluator: {
                      params: [],
                      type: 'gt',
                    },
                    operator: {
                      type: 'and',
                    },
                    query: {
                      params: [
                        'A',
                      ],
                    },
                    reducer: {
                      params: [],
                      type: 'last',
                    },
                    type: 'query',
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '__expr__',
                },
                expression: 'HumanEvalInstructRemoteProberDataAgeSignal',
                intervalMs: 1000,
                maxDataPoints: 43200,
                reducer: 'last',
                refId: 'HumanEvalInstructRemoteProberDataAge',
                type: 'reduce',
              },
            },
            {
              refId: 'HumanEvalInstructRemoteProberDataAgeTooOld',
              relativeTimeRange: {
                from: 0,
                to: 0,
              },
              datasourceUid: '__expr__',
              model: {
                conditions: [
                  {
                    evaluator: {
                      params: [
                        25,
                      ],
                      type: 'gt',
                    },
                    operator: {
                      type: 'and',
                    },
                    query: {
                      params: [
                        'B',
                      ],
                    },
                    reducer: {
                      params: [],
                      type: 'last',
                    },
                    type: 'query',
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '__expr__',
                },
                expression: 'HumanEvalInstructRemoteProberDataAge',
                intervalMs: 1000,
                maxDataPoints: 43200,
                refId: 'HumanEvalInstructRemoteProberDataAgeTooOld',
                type: 'threshold',
              },
            },
          ],
          dashboardUid: 'humaneval-instruct-remote-prober',
          panelId: 13,
          noDataState: 'NoData',
          execErrState: 'Error',
          'for': '1h',
          annotations: {
            __dashboardUid__: 'humaneval-instruct-remote-prober',
            __panelId__: '13',
            summary: 'HumanEval Instruct Remote Prober most recent result is too old. Check the health of the CronJob.',
          },
          isPaused: false,
          notification_settings: {
            receiver: 'Augment Slack',
          },
        },
      ],
    },
  ],
}

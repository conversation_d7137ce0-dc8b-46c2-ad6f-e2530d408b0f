{
  apiVersion: 1,
  groups: [
    {
      orgId: 1,
      name: 'C<PERSON>',
      folder: 'Alerts',
      interval: '5m',
      rules: [
        {
          uid: 'gh-runner-utilization0',
          title: 'GH Runner Utilization Too High',
          condition: 'GitHubRunnerUtilization60mAvgTooHigh',
          data: [
            {
              refId: 'GitHubRunnerUtilization60mAvgSignal',
              relativeTimeRange: {
                from: 604800,
                to: 0,
              },
              datasourceUid: 'devex_metrics',
              model: {
                datasource: {
                  type: 'grafana-postgresql-datasource',
                  uid: 'devex_metrics',
                },
                editorMode: 'code',
                format: 'time_series',
                intervalMs: 60000,
                maxDataPoints: 43200,
                rawQuery: true,
                rawSql: "SELECT\n  $__time(timestamp_column),\n  labels,\n  AVG((1 - idle_count::numeric/total_count)*100.0) OVER (PARTITION BY labels ORDER BY timestamp_column RANGE BETWEEN '60m' PRECEDING AND CURRENT ROW) as utilization_60m\nFROM idle_github_runners\nWHERE $__timeFilter(timestamp_column)\nORDER BY timestamp_column",
                refId: 'GitHubRunnerUtilization60mAvgSignal',
                sql: {},
                table: 'idle_github_runners',
              },
            },
            {
              refId: 'GitHubRunnerUtilization60mAvg',
              relativeTimeRange: {
                from: 0,
                to: 0,
              },
              datasourceUid: '__expr__',
              model: {
                conditions: [
                  {
                    evaluator: {
                      params: [],
                      type: 'gt',
                    },
                    operator: {
                      type: 'and',
                    },
                    query: {
                      params: [
                        'B',
                      ],
                    },
                    reducer: {
                      params: [],
                      type: 'last',
                    },
                    type: 'query',
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '__expr__',
                },
                expression: 'GitHubRunnerUtilization60mAvgSignal',
                intervalMs: 1000,
                maxDataPoints: 43200,
                reducer: 'last',
                refId: 'GitHubRunnerUtilization60mAvg',
                type: 'reduce',
              },
            },
            {
              refId: 'GitHubRunnerUtilization60mAvgTooHigh',
              relativeTimeRange: {
                from: 0,
                to: 0,
              },
              datasourceUid: '__expr__',
              model: {
                conditions: [
                  {
                    evaluator: {
                      params: [
                        95,
                      ],
                      type: 'gt',
                    },
                    operator: {
                      type: 'and',
                    },
                    query: {
                      params: [
                        'C',
                      ],
                    },
                    reducer: {
                      params: [],
                      type: 'last',
                    },
                    type: 'query',
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '__expr__',
                },
                expression: 'GitHubRunnerUtilization60mAvg',
                intervalMs: 1000,
                maxDataPoints: 43200,
                refId: 'GitHubRunnerUtilization60mAvgTooHigh',
                type: 'threshold',
              },
            },
          ],
          dashboardUid: 'github-runner-status0',
          panelId: 2,
          noDataState: 'NoData',
          execErrState: 'Error',
          'for': '10m',
          annotations: {
            __dashboardUid__: 'github-runner-status0',
            __panelId__: '2',
            description: 'GH Runner Utilization over a 60m sliding window. Metrics are written by `cronjob/github-metrics` every ~5m, so this is roughly over 12 datapoints.',
            summary: 'GH Runner Utilization is Too High. Consider adding more runners.',
          },
          isPaused: false,
          notification_settings: {
            receiver: 'Augment Slack',
            repeat_interval: '4h',
          },
        },
      ],
    },
  ],
}

{
  apiVersion: 1,
  groups: [
    {
      orgId: 1,
      name: 'eval-probers',
      folder: 'Alerts',
      interval: '5m',
      rules: [
        {
          uid: 'hindsight-prober-data-age',
          title: 'Hindsight Prober Data Age',
          condition: 'HindsightRemoteProberDataAgeTooOld',
          data: [
            {
              refId: 'HindsightRemoteProberDataAgeSignal',
              relativeTimeRange: {
                from: 604800,
                to: 0,
              },
              datasourceUid: 'devex_metrics',
              model: {
                datasource: {
                  type: 'grafana-postgresql-datasource',
                  uid: 'devex_metrics',
                },
                editorMode: 'code',
                format: 'time_series',
                intervalMs: 60000,
                maxDataPoints: 43200,
                rawQuery: true,
                rawSql: 'SELECT $__time(start_time), EXTRACT(EPOCH FROM (lag(start_time, -1, NOW()) OVER (ORDER BY start_time ASC)) - start_time)::real / 3600 AS "data_age" FROM hindsight_prober WHERE $__timeFilter(start_time) ORDER BY start_time ASC;',
                refId: 'HindsightRemoteProberDataAgeSignal',
                sql: {},
              },
            },
            {
              refId: 'HindsightRemoteProberDataAge',
              relativeTimeRange: {
                from: 0,
                to: 0,
              },
              datasourceUid: '__expr__',
              model: {
                conditions: [
                  {
                    evaluator: {
                      params: [],
                      type: 'gt',
                    },
                    operator: {
                      type: 'and',
                    },
                    query: {
                      params: [
                        'A',
                      ],
                    },
                    reducer: {
                      params: [],
                      type: 'last',
                    },
                    type: 'query',
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '__expr__',
                },
                expression: 'HindsightRemoteProberDataAgeSignal',
                intervalMs: 1000,
                maxDataPoints: 43200,
                reducer: 'last',
                refId: 'HindsightRemoteProberDataAge',
                type: 'reduce',
              },
            },
            {
              refId: 'HindsightRemoteProberDataAgeTooOld',
              relativeTimeRange: {
                from: 0,
                to: 0,
              },
              datasourceUid: '__expr__',
              model: {
                conditions: [
                  {
                    evaluator: {
                      params: [
                        13,
                      ],
                      type: 'gt',
                    },
                    operator: {
                      type: 'and',
                    },
                    query: {
                      params: [
                        'B',
                      ],
                    },
                    reducer: {
                      params: [],
                      type: 'last',
                    },
                    type: 'query',
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '__expr__',
                },
                expression: 'HindsightRemoteProberDataAge',
                intervalMs: 1000,
                maxDataPoints: 43200,
                refId: 'HindsightRemoteProberDataAgeTooOld',
                type: 'threshold',
              },
            },
          ],
          dashboardUid: 'hindsight-remote-prober',
          panelId: 14,
          noDataState: 'NoData',
          execErrState: 'Error',
          'for': '1h',
          annotations: {
            __dashboardUid__: 'hindsight-remote-prober',
            __panelId__: '14',
            summary: 'Hindsight Remote Prober most recent result is too old. Check the health of the CronJob.',
          },
          isPaused: false,
          notification_settings: {
            receiver: 'Augment Slack',
          },
        },
      ],
    },
  ],
}

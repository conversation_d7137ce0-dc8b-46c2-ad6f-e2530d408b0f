{
  apiVersion: 1,
  groups: [
    {
      orgId: 1,
      name: 'Disk',
      folder: 'Alerts',
      interval: '10m',
      rules: [
        {
          uid: 'shared-volume-critical0',
          title: 'Shared Volume CRITICAL',
          condition: 'C',
          data: [
            {
              refId: 'A',
              relativeTimeRange: {
                from: 3600,
                to: 0,
              },
              datasourceUid: 'cw-east4',
              model: {
                editorMode: 'code',
                expr: '100 * sum(kubelet_volume_stats_used_bytes{persistentvolumeclaim=~"aug-.*", namespace="cw-east4"}) by (persistentvolumeclaim) / sum(kubelet_volume_stats_capacity_bytes) by (persistentvolumeclaim)',
                hide: false,
                intervalMs: 1000,
                legendFormat: '__auto',
                maxDataPoints: 43200,
                range: true,
                refId: 'A',
              },
            },
            {
              refId: 'B',
              relativeTimeRange: {
                from: 3600,
                to: 0,
              },
              datasourceUid: '-100',
              model: {
                conditions: [
                  {
                    evaluator: {
                      params: [],
                      type: 'gt',
                    },
                    operator: {
                      type: 'and',
                    },
                    query: {
                      params: [
                        'B',
                      ],
                    },
                    reducer: {
                      params: [],
                      type: 'last',
                    },
                    type: 'query',
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '-100',
                },
                expression: 'A',
                hide: false,
                intervalMs: 1000,
                maxDataPoints: 43200,
                reducer: 'last',
                refId: 'B',
                type: 'reduce',
              },
            },
            {
              refId: 'C',
              relativeTimeRange: {
                from: 3600,
                to: 0,
              },
              datasourceUid: '-100',
              model: {
                conditions: [
                  {
                    evaluator: {
                      params: [
                        97,
                      ],
                      type: 'gt',
                    },
                    operator: {
                      type: 'and',
                    },
                    query: {
                      params: [
                        'C',
                      ],
                    },
                    reducer: {
                      params: [],
                      type: 'last',
                    },
                    type: 'query',
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '-100',
                },
                expression: 'B',
                hide: false,
                intervalMs: 1000,
                maxDataPoints: 43200,
                refId: 'C',
                type: 'threshold',
              },
            },
          ],
          dashboardUid: '',
          panelId: 0,
          noDataState: 'OK',
          execErrState: 'OK',
          'for': '5m',
          annotations: {
            description: 'CRITICAL DISK USAGE',
            summary: 'CRITICAL DISK USAGE',
          },
          labels: {
            routing: 'Constant',
          },
          isPaused: false,
        },
      ],
    },
  ],
}

{
  apiVersion: 1,
  groups: [
    {
      orgId: 1,
      name: 'eval-probers',
      folder: 'Alerts',
      interval: '5m',
      rules: [
        {
          uid: 'cceval-prober-data-age',
          title: 'CCEval Prober Data Age',
          condition: 'CCEvalRemoteProberDataAgeTooOld',
          data: [
            {
              refId: 'CCEvalRemoteProberDataAgeSignal',
              relativeTimeRange: {
                from: 604800,
                to: 0,
              },
              datasourceUid: 'devex_metrics',
              model: {
                datasource: {
                  type: 'grafana-postgresql-datasource',
                  uid: 'devex_metrics',
                },
                editorMode: 'code',
                format: 'time_series',
                intervalMs: 60000,
                maxDataPoints: 43200,
                rawQuery: true,
                rawSql: 'SELECT $__time(start_time), EXTRACT(EPOCH FROM (lag(start_time, -1, NOW()) OVER (ORDER BY start_time ASC)) - start_time)::real / 3600 AS "data_age" FROM cceval_prober WHERE $__timeFilter(start_time) ORDER BY start_time ASC;',
                refId: 'CCEvalRemoteProberDataAgeSignal',
                sql: {},
              },
            },
            {
              refId: 'CCEvalRemoteProberDataAge',
              relativeTimeRange: {
                from: 0,
                to: 0,
              },
              datasourceUid: '__expr__',
              model: {
                conditions: [
                  {
                    evaluator: {
                      params: [],
                      type: 'gt',
                    },
                    operator: {
                      type: 'and',
                    },
                    query: {
                      params: [
                        'A',
                      ],
                    },
                    reducer: {
                      params: [],
                      type: 'last',
                    },
                    type: 'query',
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '__expr__',
                },
                expression: 'CCEvalRemoteProberDataAgeSignal',
                intervalMs: 1000,
                maxDataPoints: 43200,
                reducer: 'last',
                refId: 'CCEvalRemoteProberDataAge',
                type: 'reduce',
              },
            },
            {
              refId: 'CCEvalRemoteProberDataAgeTooOld',
              relativeTimeRange: {
                from: 0,
                to: 0,
              },
              datasourceUid: '__expr__',
              model: {
                conditions: [
                  {
                    evaluator: {
                      params: [
                        7,
                      ],
                      type: 'gt',
                    },
                    operator: {
                      type: 'and',
                    },
                    query: {
                      params: [
                        'B',
                      ],
                    },
                    reducer: {
                      params: [],
                      type: 'last',
                    },
                    type: 'query',
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '__expr__',
                },
                expression: 'CCEvalRemoteProberDataAge',
                intervalMs: 1000,
                maxDataPoints: 43200,
                refId: 'CCEvalRemoteProberDataAgeTooOld',
                type: 'threshold',
              },
            },
          ],
          dashboardUid: 'cceval-remote-prober',
          panelId: 13,
          noDataState: 'NoData',
          execErrState: 'Error',
          'for': '1h',
          annotations: {
            __dashboardUid__: 'cceval-remote-prober',
            __panelId__: '13',
            summary: 'CCEval Remote Prober most recent result is too old. Check the health of the CronJob.',
          },
          isPaused: false,
          notification_settings: {
            receiver: 'Augment Slack',
          },
        },
      ],
    },
  ],
}

{
  apiVersion: 1,
  groups: [
    {
      orgId: 1,
      name: 'Disk',
      folder: 'Alerts',
      interval: '10m',
      rules: [
        {
          uid: 'shared-volume-usage0',
          title: 'Shared Volume Usage',
          condition: 'C',
          data: [
            {
              refId: 'A',
              relativeTimeRange: {
                from: 3600,
                to: 0,
              },
              datasourceUid: 'cw-east4',
              model: {
                datasource: {
                  type: 'prometheus',
                  uid: 'cw-east4',
                },
                editorMode: 'code',
                expr: '100 * sum(kubelet_volume_stats_used_bytes{persistentvolumeclaim=~"aug-.*", namespace="cw-east4"}) by (persistentvolumeclaim) / sum(kubelet_volume_stats_capacity_bytes) by (persistentvolumeclaim)',
                interval: '',
                intervalMs: 15000,
                legendFormat: '__auto',
                maxDataPoints: 43200,
                range: true,
                refId: 'A',
              },
            },
            {
              refId: 'B',
              relativeTimeRange: {
                from: 3600,
                to: 0,
              },
              datasourceUid: '-100',
              model: {
                conditions: [
                  {
                    evaluator: {
                      params: [],
                      type: 'gt',
                    },
                    operator: {
                      type: 'and',
                    },
                    query: {
                      params: [
                        'B',
                      ],
                    },
                    reducer: {
                      params: [],
                      type: 'last',
                    },
                    type: 'query',
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '-100',
                },
                expression: 'A',
                hide: false,
                intervalMs: 1000,
                maxDataPoints: 43200,
                reducer: 'last',
                refId: 'B',
                type: 'reduce',
              },
            },
            {
              refId: 'C',
              relativeTimeRange: {
                from: 3600,
                to: 0,
              },
              datasourceUid: '-100',
              model: {
                conditions: [
                  {
                    evaluator: {
                      params: [
                        90,
                      ],
                      type: 'gt',
                    },
                    operator: {
                      type: 'and',
                    },
                    query: {
                      params: [
                        'C',
                      ],
                    },
                    reducer: {
                      params: [],
                      type: 'last',
                    },
                    type: 'query',
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '-100',
                },
                expression: 'B',
                hide: false,
                intervalMs: 1000,
                maxDataPoints: 43200,
                refId: 'C',
                type: 'threshold',
              },
            },
          ],
          dashboardUid: 'disk-usage0',
          panelId: 4,
          noDataState: 'OK',
          execErrState: 'OK',
          'for': '30m',
          annotations: {
            __dashboardUid__: 'disk-usage0',
            __panelId__: '4',
          },
          labels: {
            routing: 'Hourly',
          },
          isPaused: false,
        },
      ],
    },
  ],
}

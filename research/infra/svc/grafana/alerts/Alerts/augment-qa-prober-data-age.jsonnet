{
  apiVersion: 1,
  groups: [
    {
      orgId: 1,
      name: 'eval-probers',
      folder: 'Alerts',
      interval: '5m',
      rules: [
        {
          uid: 'augment-qa-prober-data-age',
          title: 'Augment QA Prober Data Age',
          condition: 'AugmentQARemoteProberDataAgeTooOld',
          data: [
            {
              refId: 'AugmentQARemoteProberDataAgeSignal',
              relativeTimeRange: {
                from: 604800,
                to: 0,
              },
              datasourceUid: 'devex_metrics',
              model: {
                datasource: {
                  type: 'grafana-postgresql-datasource',
                  uid: 'devex_metrics',
                },
                editorMode: 'code',
                format: 'time_series',
                intervalMs: 60000,
                maxDataPoints: 43200,
                rawQuery: true,
                rawSql: 'SELECT $__time(start_time), EXTRACT(EPOCH FROM (lag(start_time, -1, NOW()) OVER (ORDER BY start_time ASC)) - start_time)::real / 3600 AS "data_age" FROM augment_qa_prober WHERE $__timeFilter(start_time) ORDER BY start_time ASC;',
                refId: 'AugmentQARemoteProberDataAgeSignal',
                sql: {},
              },
            },
            {
              refId: 'AugmentQARemoteProberDataAge',
              relativeTimeRange: {
                from: 0,
                to: 0,
              },
              datasourceUid: '__expr__',
              model: {
                conditions: [
                  {
                    evaluator: {
                      params: [],
                      type: 'gt',
                    },
                    operator: {
                      type: 'and',
                    },
                    query: {
                      params: [
                        'A',
                      ],
                    },
                    reducer: {
                      params: [],
                      type: 'last',
                    },
                    type: 'query',
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '__expr__',
                },
                expression: 'AugmentQARemoteProberDataAgeSignal',
                intervalMs: 1000,
                maxDataPoints: 43200,
                reducer: 'last',
                refId: 'AugmentQARemoteProberDataAge',
                type: 'reduce',
              },
            },
            {
              refId: 'AugmentQARemoteProberDataAgeTooOld',
              relativeTimeRange: {
                from: 0,
                to: 0,
              },
              datasourceUid: '__expr__',
              model: {
                conditions: [
                  {
                    evaluator: {
                      params: [
                        25,
                      ],
                      type: 'gt',
                    },
                    operator: {
                      type: 'and',
                    },
                    query: {
                      params: [
                        'B',
                      ],
                    },
                    reducer: {
                      params: [],
                      type: 'last',
                    },
                    type: 'query',
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '__expr__',
                },
                expression: 'AugmentQARemoteProberDataAge',
                intervalMs: 1000,
                maxDataPoints: 43200,
                refId: 'AugmentQARemoteProberDataAgeTooOld',
                type: 'threshold',
              },
            },
          ],
          dashboardUid: 'augment-qa-remote-prober',
          panelId: 13,
          noDataState: 'NoData',
          execErrState: 'Error',
          'for': '1h',
          annotations: {
            __dashboardUid__: 'augment-qa-remote-prober',
            __panelId__: '13',
            summary: 'Augment QA Remote Prober most recent result is too old. Check the health of the CronJob.',
          },
          isPaused: false,
          notification_settings: {
            receiver: 'Augment Slack',
          },
        },
      ],
    },
  ],
}

{
  apiVersion: 1,
  groups: [
    {
      orgId: 1,
      name: 'C<PERSON>',
      folder: 'Alerts',
      interval: '5m',
      rules: [
        {
          uid: 'research_ci_runtime_too_high0',
          title: 'Research CI RunTime Too High',
          condition: 'ResearchCIRunTimeMax1dTooHigh',
          data: [
            {
              refId: 'ResearchCIRunTimeMax1dSignal',
              relativeTimeRange: {
                from: 604800,
                to: 0,
              },
              datasourceUid: 'devex_metrics',
              model: {
                datasource: {
                  type: 'grafana-postgresql-datasource',
                  uid: 'devex_metrics',
                },
                editorMode: 'code',
                format: 'time_series',
                intervalMs: 60000,
                maxDataPoints: 43200,
                rawQuery: true,
                rawSql: "SELECT date_trunc('day', started_at) AS time, name, percentile_cont(100/100) WITHIN GROUP (ORDER BY duration_m ASC) AS duration_m_100p FROM (\n  SELECT started_at, name, EXTRACT(EPOCH FROM completed_at-started_at)/60 AS duration_m FROM check_runs WHERE name LIKE 'CI - %' AND completed_at IS NOT NULL AND $__timeFilter(started_at) ORDER BY started_at ASC\n) GROUP BY time, name ORDER BY time;",
                refId: 'ResearchCIRunTimeMax1dSignal',
                sql: {},
              },
            },
            {
              refId: 'ResearchCIRunTimeMax1d',
              relativeTimeRange: {
                from: 0,
                to: 0,
              },
              datasourceUid: '__expr__',
              model: {
                conditions: [
                  {
                    reducer: {
                      params: [],
                      type: 'last',
                    },
                    type: 'query',
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '__expr__',
                },
                expression: 'ResearchCIRunTimeMax1dSignal',
                intervalMs: 1000,
                maxDataPoints: 43200,
                reducer: 'last',
                refId: 'ResearchCIRunTimeMax1d',
                type: 'reduce',
              },
            },
            {
              refId: 'ResearchCIRunTimeMax1dTooHigh',
              relativeTimeRange: {
                from: 0,
                to: 0,
              },
              datasourceUid: '__expr__',
              model: {
                conditions: [
                  {
                    evaluator: {
                      params: [
                        20,
                      ],
                      type: 'gt',
                    },
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '__expr__',
                },
                expression: 'ResearchCIRunTimeMax1d',
                intervalMs: 1000,
                maxDataPoints: 43200,
                refId: 'ResearchCIRunTimeMax1dTooHigh',
                type: 'threshold',
              },
            },
          ],
          dashboardUid: 'research-ci0',
          panelId: 1,
          noDataState: 'KeepLast',
          execErrState: 'Error',
          'for': '30m',
          annotations: {
            __dashboardUid__: 'research-ci0',
            __panelId__: '1',
          },
          isPaused: false,
          notification_settings: {
            receiver: 'Augment Slack',
            repeat_interval: '1d',
          },
        },
      ],
    },
  ],
}

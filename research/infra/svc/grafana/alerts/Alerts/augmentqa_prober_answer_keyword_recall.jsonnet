{
  apiVersion: 1,
  groups: [
    {
      orgId: 1,
      name: 'eval-probers',
      folder: 'Alerts',
      interval: '5m',
      rules: [
        {
          uid: 'augmentqa_prober_answer_keyword_recall',
          title: 'AugmentQA Prober Answer Keyword Recall Low',
          condition: 'AnswerKeywordRecallTooLow',
          data: [
            {
              refId: 'AnswerKeywordRecallSignal',
              relativeTimeRange: {
                from: 604800,
                to: 0,
              },
              datasourceUid: 'devex_metrics',
              model: {
                datasource: {
                  type: 'grafana-postgres-datasource',
                  uid: 'devex_metrics',
                },
                editorMode: 'code',
                format: 'time_series',
                hide: false,
                intervalMs: 1000,
                legendFormat: '__auto',
                maxDataPoints: 43200,
                rawQuery: true,
                rawSql: 'SELECT $__time(start_time), answer_keyword_recall FROM augment_qa_prober WHERE $__timeFilter(start_time) ORDER BY start_time ASC',
                refId: 'AnswerKeywordRecallSignal',
                sql: {
                  columns: [
                    {
                      alias: 'time',
                      parameters: [
                        {
                          name: 'start_time',
                          type: 'functionParameter',
                        },
                      ],
                      type: 'function',
                    },
                    {
                      alias: 'value',
                      parameters: [
                        {
                          name: 'answer_keyword_recall',
                          type: 'functionParameter',
                        },
                      ],
                      type: 'function',
                    },
                  ],
                  groupBy: [],
                  limit: null,
                  orderBy: {
                    property: {
                      name: [
                        'start_time',
                      ],
                      type: 'string',
                    },
                    type: 'property',
                  },
                  orderByDirection: 'ASC',
                },
                table: 'augment_qa_prober',
              },
            },
            {
              refId: 'AnswerKeywordRecall',
              relativeTimeRange: {
                from: 172800,
                to: 0,
              },
              datasourceUid: '-100',
              model: {
                conditions: [
                  {
                    reducer: {
                      params: [],
                      type: 'last',
                    },
                    type: 'query',
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '-100',
                },
                expression: 'AnswerKeywordRecallSignal',
                hide: false,
                intervalMs: 1000,
                maxDataPoints: 43200,
                reducer: 'last',
                refId: 'AnswerKeywordRecall',
                type: 'reduce',
              },
            },
            {
              refId: 'AnswerKeywordRecallTooLow',
              relativeTimeRange: {
                from: 172800,
                to: 0,
              },
              datasourceUid: '-100',
              model: {
                conditions: [
                  {
                    evaluator: {
                      params: [
                        0.68,
                      ],
                      type: 'lt',
                    },
                  },
                ],
                datasource: {
                  type: '__expr__',
                  uid: '-100',
                },
                expression: 'AnswerKeywordRecall',
                hide: false,
                intervalMs: 1000,
                maxDataPoints: 43200,
                refId: 'AnswerKeywordRecallTooLow',
                type: 'threshold',
              },
            },
          ],
          dashboardUid: 'augment-qa-remote-prober',
          panelId: 2,
          noDataState: 'OK',
          execErrState: 'OK',
          'for': '1h',
          annotations: {
            __dashboardUid__: 'augment-qa-remote-prober',
            __panelId__: '2',
            summary: 'Answer Keyword Recall is Too Low',
          },
          isPaused: false,
          notification_settings: {
            receiver: 'Augment Slack (#team-chat)',
            repeat_interval: '1d',
          },
        },
      ],
    },
  ],
}

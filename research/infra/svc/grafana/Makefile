NAME      := grafana0
NAMESPACE := grafana
CLUSTER   := gcp-core0
HOSTNAME  := grafana.r.augmentcode.com
CHART     := grafana/grafana

CONTEXT  := $(shell jsonnet -A name="$(CLUSTER)" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
K8S_CONF := grafana.jsonnet
KUBECTL  := kubectl --context="$(CONTEXT)"
KUBECFG  := kubecfg --context="$(CONTEXT)" \
	   --tla-code="name='$(NAME)'" \
	   --tla-code="namespace='$(NAMESPACE)'" \
	   --tla-code="cluster='$(CLUSTER)'" \
	   --tla-code="hostname='$(HOSTNAME)'"

show:
	$(KUBECFG) show "$(K8S_CONF)"

diff:
	-$(KUBECFG) diff --diff-strategy=subset "$(K8S_CONF)"

apply:
	$(KUBECFG) update "$(K8S_CONF)"

list:
	$(KUBECTL) get -n "$(NAMESPACE)" sealedsecret,secret,cm,pvc,sa,svc,ing,deploy,statefulset,job,pod,role,rolebinding

pull:
	helm pull "$(CHART)"

deploy: diff apply

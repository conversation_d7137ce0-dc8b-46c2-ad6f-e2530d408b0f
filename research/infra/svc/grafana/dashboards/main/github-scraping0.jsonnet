{
  annotations: {
    list: [
      {
        builtIn: 1,
        datasource: {
          type: 'grafana',
          uid: '-- <PERSON>ana --',
        },
        enable: true,
        hide: true,
        iconColor: 'rgba(0, 211, 255, 1)',
        name: 'Annotations & Alerts',
        target: {
          limit: 100,
          matchAny: false,
          tags: [],
          type: 'dashboard',
        },
        type: 'dashboard',
      },
    ],
  },
  editable: true,
  fiscalYearStartMonth: 0,
  graphTooltip: 0,
  links: [],
  liveNow: false,
  panels: [
    {
      collapsed: false,
      gridPos: {
        h: 1,
        w: 24,
        x: 0,
        y: 0,
      },
      id: 53,
      panels: [],
      title: 'File Content (in HEAD only)',
      type: 'row',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'metastore',
      },
      description: 'New files collected each day',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'short',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 12,
        x: 0,
        y: 1,
      },
      id: 59,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(file_count) AS daily_file_count\nFROM\n  lang_size_metric\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'A',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
      ],
      title: 'Daily New Files',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'metastore',
      },
      description: '',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'short',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 12,
        x: 12,
        y: 1,
      },
      id: 57,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(SUM(file_count)) OVER (\n    ORDER BY\n      date\n  ) AS total_file_count\nFROM\n  lang_size_metric\nGROUP BY\n  date\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'A',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
      ],
      title: 'Total Files',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'metastore',
      },
      description: 'Total size of characters collected each day',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'short',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 12,
        x: 0,
        y: 9,
      },
      id: 55,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(sum_char_size) AS daily_char_size\nFROM\n  lang_size_metric\nGROUP BY\ntime\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'A',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
      ],
      title: 'Daily New Characters',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'metastore',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'short',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 12,
        x: 12,
        y: 9,
      },
      id: 61,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(SUM(sum_char_size)) OVER (\n    ORDER BY\n      date\n  ) AS total_char_size\nFROM\n  lang_size_metric\nGROUP BY\n  date\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'A',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
      ],
      title: 'Total Characters',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'metastore',
      },
      description: 'Total size of characters collected each day (per language)',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'short',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 12,
        x: 0,
        y: 17,
      },
      id: 62,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(sum_char_size) AS python\nFROM\n  lang_size_metric\nWHERE\n  lang = 'python'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'python',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(sum_char_size) AS c\nFROM\n  lang_size_metric\nWHERE\n  lang = 'c'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'c',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(sum_char_size) AS cpp\nFROM\n  lang_size_metric\nWHERE\n  lang = 'c++'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'cpp',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(sum_char_size) AS java\nFROM\n  lang_size_metric\nWHERE\n  lang = 'java'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'java',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(sum_char_size) AS go\nFROM\n  lang_size_metric\nWHERE\n  lang = 'go'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'go',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(sum_char_size) AS typescript\nFROM\n  lang_size_metric\nWHERE\n  lang = 'typescript'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'typescript',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(sum_char_size) AS javascript\nFROM\n  lang_size_metric\nWHERE\n  lang = 'javascript'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'javascript',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(sum_char_size) AS rust\nFROM\n  lang_size_metric\nWHERE\n  lang = 'rust'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'rust',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(sum_char_size) AS csharp\nFROM\n  lang_size_metric\nWHERE\n  lang = 'c#'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'csharp',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(sum_char_size) AS shell\nFROM\n  lang_size_metric\nWHERE\n  lang = 'shell'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'shell',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(sum_char_size) AS perl\nFROM\n  lang_size_metric\nWHERE\n  lang = 'perl'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'perl',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(sum_char_size) AS markdown\nFROM\n  lang_size_metric\nWHERE\n  lang = 'markdown'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'Markdown',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
      ],
      title: 'Daily New Characters (Language Breakdown)',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'metastore',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'short',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 12,
        x: 12,
        y: 17,
      },
      id: 63,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(SUM(sum_char_size)) OVER (\n    ORDER BY\n      date\n  ) AS python\nFROM\n  lang_size_metric\nWHERE\n  lang = 'python'\nGROUP BY\n  date\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'python',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(SUM(sum_char_size)) OVER (\n    ORDER BY\n      date\n  ) AS c\nFROM\n  lang_size_metric\nWHERE\n  lang = 'c'\nGROUP BY\n  date\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'c',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(SUM(sum_char_size)) OVER (\n    ORDER BY\n      date\n  ) AS cpp\nFROM\n  lang_size_metric\nWHERE\n  lang = 'c++'\nGROUP BY\n  date\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'cpp',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(SUM(sum_char_size)) OVER (\n    ORDER BY\n      date\n  ) AS java\nFROM\n  lang_size_metric\nWHERE\n  lang = 'java'\nGROUP BY\n  date\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'java',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(SUM(sum_char_size)) OVER (\n    ORDER BY\n      date\n  ) AS go\nFROM\n  lang_size_metric\nWHERE\n  lang = 'go'\nGROUP BY\n  date\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'go',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(SUM(sum_char_size)) OVER (\n    ORDER BY\n      date\n  ) AS typescript\nFROM\n  lang_size_metric\nWHERE\n  lang = 'typescript'\nGROUP BY\n  date\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'typescript',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(SUM(sum_char_size)) OVER (\n    ORDER BY\n      date\n  ) AS javascript\nFROM\n  lang_size_metric\nWHERE\n  lang = 'javascript'\nGROUP BY\n  date\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'javascript',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(SUM(sum_char_size)) OVER (\n    ORDER BY\n      date\n  ) AS rust\nFROM\n  lang_size_metric\nWHERE\n  lang = 'rust'\nGROUP BY\n  date\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'rust',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(SUM(sum_char_size)) OVER (\n    ORDER BY\n      date\n  ) AS csharp\nFROM\n  lang_size_metric\nWHERE\n  lang = 'c#'\nGROUP BY\n  date\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'csharp',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(SUM(sum_char_size)) OVER (\n    ORDER BY\n      date\n  ) AS shell\nFROM\n  lang_size_metric\nWHERE\n  lang = 'shell'\nGROUP BY\n  date\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'shell',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(SUM(sum_char_size)) OVER (\n    ORDER BY\n      date\n  ) AS perl\nFROM\n  lang_size_metric\nWHERE\n  lang = 'perl'\nGROUP BY\n  date\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'perl',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          editorMode: 'code',
          format: 'table',
          hide: false,
          rawQuery: true,
          rawSql: "SELECT\n  date :: date AT TIME ZONE 'UTC-8' AS time,\n  SUM(SUM(sum_char_size)) OVER (\n    ORDER BY\n      date\n  ) AS markdown\nFROM\n  lang_size_metric\nWHERE\n  lang = 'markdown'\nGROUP BY\n  date\nORDER BY\n  time DESC\nLIMIT\n  90",
          refId: 'Markdown',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
      ],
      title: 'Total Characters (Language Breakdown)',
      type: 'timeseries',
    },
    {
      collapsed: true,
      gridPos: {
        h: 1,
        w: 24,
        x: 0,
        y: 25,
      },
      id: 49,
      panels: [
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          description: 'Daily collected metrics on the repo table without any restriction',
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              custom: {
                axisCenteredZero: false,
                axisColorMode: 'text',
                axisLabel: '',
                axisPlacement: 'auto',
                barAlignment: 0,
                drawStyle: 'line',
                fillOpacity: 0,
                gradientMode: 'none',
                hideFrom: {
                  legend: false,
                  tooltip: false,
                  viz: false,
                },
                lineInterpolation: 'linear',
                lineWidth: 1,
                pointSize: 5,
                scaleDistribution: {
                  type: 'linear',
                },
                showPoints: 'auto',
                spanNulls: false,
                stacking: {
                  group: 'A',
                  mode: 'none',
                },
                thresholdsStyle: {
                  mode: 'off',
                },
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                ],
              },
              unit: 'short',
            },
            overrides: [],
          },
          gridPos: {
            h: 8,
            w: 12,
            x: 0,
            y: 26,
          },
          id: 35,
          interval: '1d',
          options: {
            legend: {
              calcs: [],
              displayMode: 'list',
              placement: 'bottom',
              showLegend: true,
            },
            tooltip: {
              mode: 'single',
              sort: 'none',
            },
          },
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "SELECT\n  metric_timestamp AT TIME ZONE 'UTC-8' AS time,\n  SUM(total_count) AS total_repos,\n  SUM(api_called_count) AS total_requested_repos,\n  SUM(root_repo_count) AS total_root_repos\nFROM\n  repo_metric\nWHERE\n  EXTRACT(\n    HOUR\n    FROM\n      metric_timestamp\n  ) = 0\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Metrics on repo Table (General)',
          type: 'timeseries',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          description: 'API requested / total repos & root / API requested repos',
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              custom: {
                axisCenteredZero: false,
                axisColorMode: 'text',
                axisLabel: '',
                axisPlacement: 'auto',
                barAlignment: 0,
                drawStyle: 'line',
                fillOpacity: 0,
                gradientMode: 'none',
                hideFrom: {
                  legend: false,
                  tooltip: false,
                  viz: false,
                },
                lineInterpolation: 'linear',
                lineWidth: 1,
                pointSize: 5,
                scaleDistribution: {
                  type: 'linear',
                },
                showPoints: 'auto',
                spanNulls: false,
                stacking: {
                  group: 'A',
                  mode: 'none',
                },
                thresholdsStyle: {
                  mode: 'off',
                },
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                  {
                    color: 'red',
                    value: 80,
                  },
                ],
              },
              unit: 'percentunit',
            },
            overrides: [],
          },
          gridPos: {
            h: 8,
            w: 12,
            x: 12,
            y: 26,
          },
          id: 37,
          options: {
            legend: {
              calcs: [],
              displayMode: 'list',
              placement: 'bottom',
              showLegend: true,
            },
            tooltip: {
              mode: 'single',
              sort: 'none',
            },
          },
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "SELECT\n  metric_timestamp AT TIME ZONE 'UTC-8' AS time,\n  SUM(api_called_count) * 1.0 / SUM(total_count) AS requested_over_total,\n  SUM(root_repo_count) * 1.0 / SUM(api_called_count) AS root_over_requested\nFROM\n  repo_metric\nWHERE\n  EXTRACT(\n    HOUR\n    FROM\n      metric_timestamp\n  ) = 0\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Percentages on repo Table (General)',
          type: 'timeseries',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          description: "Daily collected metrics on repos with permissive licenses (all counts exclude the 'the-stack' source)",
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              custom: {
                axisCenteredZero: false,
                axisColorMode: 'text',
                axisLabel: '',
                axisPlacement: 'auto',
                barAlignment: 0,
                drawStyle: 'line',
                fillOpacity: 0,
                gradientMode: 'none',
                hideFrom: {
                  legend: false,
                  tooltip: false,
                  viz: false,
                },
                lineInterpolation: 'linear',
                lineWidth: 1,
                pointSize: 5,
                scaleDistribution: {
                  type: 'linear',
                },
                showPoints: 'auto',
                spanNulls: false,
                stacking: {
                  group: 'A',
                  mode: 'none',
                },
                thresholdsStyle: {
                  mode: 'off',
                },
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                  {
                    color: 'red',
                    value: 80,
                  },
                ],
              },
              unit: 'short',
            },
            overrides: [],
          },
          gridPos: {
            h: 8,
            w: 12,
            x: 0,
            y: 34,
          },
          id: 39,
          options: {
            legend: {
              calcs: [],
              displayMode: 'list',
              placement: 'bottom',
              showLegend: true,
            },
            tooltip: {
              mode: 'single',
              sort: 'none',
            },
          },
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "SELECT\n  metric_timestamp AT TIME ZONE 'UTC-8' AS time,\n  SUM(root_repo_count) AS permissive_root_repos,\n  SUM(download_attempted_count) AS total_attempted_repos,\n  SUM(successful_download_count) AS total_downloaded_repos\nFROM\n  repo_metric\n  JOIN license_info ON license_info.key = repo_metric.license\n  AND license_info.permissive\nWHERE\n  EXTRACT(\n    HOUR\n    FROM\n      metric_timestamp\n  ) = 0\n  AND source != 'the-stack'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Metrics on repo Table (Permissive w/o the-stack)',
          type: 'timeseries',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          description: 'Successfully downloaded repos / permissive root repos',
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              custom: {
                axisCenteredZero: false,
                axisColorMode: 'text',
                axisLabel: '',
                axisPlacement: 'auto',
                barAlignment: 0,
                drawStyle: 'line',
                fillOpacity: 0,
                gradientMode: 'none',
                hideFrom: {
                  legend: false,
                  tooltip: false,
                  viz: false,
                },
                lineInterpolation: 'linear',
                lineWidth: 1,
                pointSize: 5,
                scaleDistribution: {
                  type: 'linear',
                },
                showPoints: 'auto',
                spanNulls: false,
                stacking: {
                  group: 'A',
                  mode: 'none',
                },
                thresholdsStyle: {
                  mode: 'dashed',
                },
              },
              mappings: [],
              max: 1,
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                  {
                    color: 'orange',
                    value: 0.9,
                  },
                ],
              },
              unit: 'percentunit',
            },
            overrides: [],
          },
          gridPos: {
            h: 8,
            w: 12,
            x: 12,
            y: 34,
          },
          id: 42,
          options: {
            legend: {
              calcs: [],
              displayMode: 'list',
              placement: 'bottom',
              showLegend: true,
            },
            tooltip: {
              mode: 'single',
              sort: 'none',
            },
          },
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "SELECT\n  metric_timestamp AT TIME ZONE 'UTC-8' AS time,\n  SUM(successful_download_count) * 1.0 / SUM(root_repo_count) AS downloaded_percentage\nFROM\n  repo_metric\n  JOIN license_info ON license_info.key = repo_metric.license\n  AND license_info.permissive\nWHERE\n  EXTRACT(\n    HOUR\n    FROM\n      metric_timestamp\n  ) = 0\n  AND source != 'the-stack'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Downloaded Percentage (Permissive w/o the-stack)',
          type: 'timeseries',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          description: "Daily collected metrics on repos in the 'the-stack' source",
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              custom: {
                axisCenteredZero: false,
                axisColorMode: 'text',
                axisLabel: '',
                axisPlacement: 'auto',
                barAlignment: 0,
                drawStyle: 'line',
                fillOpacity: 0,
                gradientMode: 'none',
                hideFrom: {
                  legend: false,
                  tooltip: false,
                  viz: false,
                },
                lineInterpolation: 'linear',
                lineWidth: 1,
                pointSize: 5,
                scaleDistribution: {
                  type: 'linear',
                },
                showPoints: 'auto',
                spanNulls: false,
                stacking: {
                  group: 'A',
                  mode: 'none',
                },
                thresholdsStyle: {
                  mode: 'off',
                },
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                  {
                    color: 'red',
                    value: 80,
                  },
                ],
              },
              unit: 'short',
            },
            overrides: [],
          },
          gridPos: {
            h: 8,
            w: 12,
            x: 0,
            y: 42,
          },
          id: 40,
          options: {
            legend: {
              calcs: [],
              displayMode: 'list',
              placement: 'bottom',
              showLegend: true,
            },
            tooltip: {
              mode: 'single',
              sort: 'none',
            },
          },
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              hide: false,
              rawQuery: true,
              rawSql: "SELECT\n  metric_timestamp AT TIME ZONE 'UTC-8' AS time,\n  SUM(total_count) AS total_repos,\n  SUM(api_called_count) AS total_requested_repos\nFROM\n  repo_metric\nWHERE\n  EXTRACT(\n    HOUR\n    FROM\n      metric_timestamp\n  ) = 0\n  AND source = 'the-stack'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
              refId: 'General',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "SELECT\n  metric_timestamp AT TIME ZONE 'UTC-8' AS time,\n  SUM(root_repo_count) AS permissive_root_repos,\n  SUM(download_attempted_count) AS total_attempted_repos,\n  SUM(successful_download_count) AS total_downloaded_repos\nFROM\n  repo_metric\n  JOIN license_info ON license_info.key = repo_metric.license\n  AND license_info.permissive\nWHERE\n  EXTRACT(\n    HOUR\n    FROM\n      metric_timestamp\n  ) = 0\n  AND source = 'the-stack'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
              refId: 'Permissive',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Metrics on repo Table (the-stack)',
          type: 'timeseries',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          description: 'API requested / total repos & downloaded / permissive root repos',
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              custom: {
                axisCenteredZero: false,
                axisColorMode: 'text',
                axisLabel: '',
                axisPlacement: 'auto',
                barAlignment: 0,
                drawStyle: 'line',
                fillOpacity: 0,
                gradientMode: 'none',
                hideFrom: {
                  legend: false,
                  tooltip: false,
                  viz: false,
                },
                lineInterpolation: 'linear',
                lineWidth: 1,
                pointSize: 5,
                scaleDistribution: {
                  type: 'linear',
                },
                showPoints: 'auto',
                spanNulls: false,
                stacking: {
                  group: 'A',
                  mode: 'none',
                },
                thresholdsStyle: {
                  mode: 'dashed',
                },
              },
              mappings: [],
              max: 1,
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                ],
              },
              unit: 'percentunit',
            },
            overrides: [],
          },
          gridPos: {
            h: 8,
            w: 12,
            x: 12,
            y: 42,
          },
          id: 43,
          options: {
            legend: {
              calcs: [],
              displayMode: 'list',
              placement: 'bottom',
              showLegend: true,
            },
            tooltip: {
              mode: 'single',
              sort: 'none',
            },
          },
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              hide: false,
              rawQuery: true,
              rawSql: "SELECT\n  metric_timestamp AT TIME ZONE 'UTC-8' AS time,\n  SUM(api_called_count) * 1.0 / SUM(total_count) AS requested_over_total\nFROM\n  repo_metric\nWHERE\n  EXTRACT(\n    HOUR\n    FROM\n      metric_timestamp\n  ) = 0\n  AND source = 'the-stack'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
              refId: 'General',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              hide: false,
              rawQuery: true,
              rawSql: "SELECT\n  metric_timestamp AT TIME ZONE 'UTC-8' AS time,\n  SUM(successful_download_count) * 1.0 / SUM(root_repo_count) AS downloaded_percentage\nFROM\n  repo_metric\n  JOIN license_info ON license_info.key = repo_metric.license\n  AND license_info.permissive\nWHERE\n  EXTRACT(\n    HOUR\n    FROM\n      metric_timestamp\n  ) = 0\n  AND source = 'the-stack'\nGROUP BY\n  time\nORDER BY\n  time DESC\nLIMIT\n  90",
              refId: 'Permissive',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Percentages on repo Table (the-stack)',
          type: 'timeseries',
        },
      ],
      title: 'Repos',
      type: 'row',
    },
    {
      collapsed: true,
      gridPos: {
        h: 1,
        w: 24,
        x: 0,
        y: 26,
      },
      id: 45,
      panels: [
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          description: 'Daily counts of newly identified repos from GH Archive events',
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              custom: {
                axisCenteredZero: false,
                axisColorMode: 'text',
                axisLabel: '',
                axisPlacement: 'auto',
                barAlignment: 0,
                drawStyle: 'line',
                fillOpacity: 0,
                gradientMode: 'none',
                hideFrom: {
                  legend: false,
                  tooltip: false,
                  viz: false,
                },
                lineInterpolation: 'linear',
                lineWidth: 1,
                pointSize: 5,
                scaleDistribution: {
                  type: 'linear',
                },
                showPoints: 'auto',
                spanNulls: false,
                stacking: {
                  group: 'A',
                  mode: 'none',
                },
                thresholdsStyle: {
                  mode: 'off',
                },
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                ],
              },
              unit: 'short',
            },
            overrides: [],
          },
          gridPos: {
            h: 8,
            w: 12,
            x: 0,
            y: 27,
          },
          id: 23,
          interval: '1d',
          options: {
            legend: {
              calcs: [
                'mean',
                'min',
                'max',
              ],
              displayMode: 'list',
              placement: 'bottom',
              showLegend: true,
            },
            tooltip: {
              mode: 'single',
              sort: 'none',
            },
          },
          pluginVersion: '9.3.6',
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              hide: false,
              rawQuery: true,
              rawSql: "SELECT\n  additional_info ['new_repos'] :: integer AS new_repos,\n  partition_name :: date AT TIME ZONE 'UTC-8' AS time\nFROM\n  batch_job\nWHERE\n  name = 'gh-archive-processing'\n  AND status = 'done'\nORDER BY\n  time DESC\nLIMIT\n  90",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [
                      {
                        name: 'additional_info',
                        type: 'functionParameter',
                      },
                    ],
                    type: 'function',
                  },
                  {
                    parameters: [
                      {
                        name: 'partition_name',
                        type: 'functionParameter',
                      },
                    ],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
                orderBy: {
                  property: {
                    name: [
                      'partition_name',
                    ],
                    type: 'string',
                  },
                  type: 'property',
                },
                orderByDirection: 'DESC',
              },
              table: 'batch_job',
            },
          ],
          title: 'New Repos From GH Archive',
          transformations: [],
          type: 'timeseries',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          description: 'Duration (in minutes) of the processing time to find and insert new repos from GH Archive events',
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              custom: {
                axisCenteredZero: false,
                axisColorMode: 'text',
                axisLabel: '',
                axisPlacement: 'auto',
                barAlignment: 0,
                drawStyle: 'line',
                fillOpacity: 0,
                gradientMode: 'none',
                hideFrom: {
                  legend: false,
                  tooltip: false,
                  viz: false,
                },
                lineInterpolation: 'linear',
                lineWidth: 1,
                pointSize: 5,
                scaleDistribution: {
                  type: 'linear',
                },
                showPoints: 'auto',
                spanNulls: false,
                stacking: {
                  group: 'A',
                  mode: 'none',
                },
                thresholdsStyle: {
                  mode: 'dashed',
                },
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                  {
                    color: 'orange',
                    value: 60,
                  },
                ],
              },
            },
            overrides: [],
          },
          gridPos: {
            h: 8,
            w: 12,
            x: 12,
            y: 27,
          },
          id: 25,
          interval: '1d',
          options: {
            legend: {
              calcs: [
                'mean',
                'min',
                'max',
              ],
              displayMode: 'list',
              placement: 'bottom',
              showLegend: true,
            },
            tooltip: {
              mode: 'single',
              sort: 'none',
            },
          },
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "SELECT\n  EXTRACT(\n    min\n    FROM\n      (end_time :: timestamp - start_time :: timestamp)\n  ) :: integer AS duration_minutes,\n  partition_name :: date AT TIME ZONE 'UTC-8' AS time\nFROM\n  batch_job\nWHERE\n  name = 'gh-archive-processing'\n  AND status = 'done'\nORDER BY\n  time DESC\nLIMIT\n  90",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Process Time on GH Archive (Minutes)',
          type: 'timeseries',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          description: 'Number of attempts to download and process GH Archive events',
          fieldConfig: {
            defaults: {
              color: {
                mode: 'continuous-GrYlRd',
              },
              custom: {
                axisCenteredZero: false,
                axisColorMode: 'text',
                axisLabel: '',
                axisPlacement: 'auto',
                fillOpacity: 80,
                gradientMode: 'none',
                hideFrom: {
                  legend: false,
                  tooltip: false,
                  viz: false,
                },
                lineWidth: 1,
                scaleDistribution: {
                  type: 'linear',
                },
                thresholdsStyle: {
                  mode: 'dashed',
                },
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                  {
                    color: 'orange',
                    value: 3,
                  },
                ],
              },
            },
            overrides: [],
          },
          gridPos: {
            h: 8,
            w: 12,
            x: 0,
            y: 35,
          },
          id: 27,
          interval: '1d',
          options: {
            barRadius: 0,
            barWidth: 0.75,
            colorByField: 'attempts',
            groupWidth: 0.7,
            legend: {
              calcs: [
                'max',
              ],
              displayMode: 'list',
              placement: 'bottom',
              showLegend: true,
            },
            orientation: 'auto',
            showValue: 'never',
            stacking: 'none',
            text: {},
            tooltip: {
              mode: 'multi',
              sort: 'none',
            },
            xTickLabelRotation: 0,
            xTickLabelSpacing: 200,
          },
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "SELECT\n  count(*) AS attempts,\n  partition_name :: date AT TIME ZONE 'UTC-8' AS time\nFROM\n  batch_job\nWHERE\n  name = 'gh-archive-processing'\nGROUP BY time\nORDER BY\n  time DESC\nLIMIT\n  90",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Attempts to Process GH Archive',
          type: 'barchart',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          fieldConfig: {
            defaults: {
              color: {
                mode: 'thresholds',
              },
              custom: {
                align: 'auto',
                displayMode: 'auto',
                inspect: false,
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                ],
              },
            },
            overrides: [
              {
                matcher: {
                  id: 'byName',
                  options: 'date',
                },
                properties: [
                  {
                    id: 'custom.width',
                    value: 107,
                  },
                ],
              },
              {
                matcher: {
                  id: 'byName',
                  options: 'name',
                },
                properties: [
                  {
                    id: 'custom.width',
                    value: 190,
                  },
                ],
              },
              {
                matcher: {
                  id: 'byName',
                  options: 'status',
                },
                properties: [
                  {
                    id: 'custom.width',
                    value: 74,
                  },
                ],
              },
            ],
          },
          gridPos: {
            h: 8,
            w: 12,
            x: 12,
            y: 35,
          },
          id: 31,
          options: {
            footer: {
              fields: '',
              reducer: [
                'sum',
              ],
              show: false,
            },
            showHeader: true,
            sortBy: [],
          },
          pluginVersion: '9.3.6',
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "SELECT\n  partition_name AS date,\n  name,\n  status,\n  error_info\nFROM\n  batch_job\nWHERE\n  status != 'done'\n  and name = 'gh-archive-processing'\nORDER BY\n  date DESC\nLIMIT\n  5",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Failed GH Archive Jobs',
          type: 'table',
        },
      ],
      title: 'GH Archive',
      type: 'row',
    },
    {
      collapsed: true,
      gridPos: {
        h: 1,
        w: 24,
        x: 0,
        y: 27,
      },
      id: 47,
      panels: [
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          description: 'Daily collected metrics on the vendor_user table',
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              custom: {
                axisCenteredZero: false,
                axisColorMode: 'text',
                axisLabel: '',
                axisPlacement: 'auto',
                barAlignment: 0,
                drawStyle: 'line',
                fillOpacity: 0,
                gradientMode: 'none',
                hideFrom: {
                  legend: false,
                  tooltip: false,
                  viz: false,
                },
                lineInterpolation: 'linear',
                lineWidth: 1,
                pointSize: 5,
                scaleDistribution: {
                  type: 'linear',
                },
                showPoints: 'auto',
                spanNulls: false,
                stacking: {
                  group: 'A',
                  mode: 'none',
                },
                thresholdsStyle: {
                  mode: 'off',
                },
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                ],
              },
              unit: 'short',
            },
            overrides: [],
          },
          gridPos: {
            h: 8,
            w: 12,
            x: 0,
            y: 28,
          },
          id: 29,
          options: {
            legend: {
              calcs: [],
              displayMode: 'list',
              placement: 'bottom',
              showLegend: true,
            },
            tooltip: {
              mode: 'single',
              sort: 'none',
            },
          },
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "SELECT\n  metric_timestamp AT TIME ZONE 'UTC-8' AS time,\n  total_vendor_users,\n  ok_vendor_users,\n  unknown_vendor_users,\n  total_new_repos\nFROM\n  vendor_user_metric\nORDER BY\n  time DESC\nLIMIT\n  90",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Metrics on vendor_user Table',
          type: 'timeseries',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          description: 'Successfully requested (ok) + Unrequested (unknown) + Failed',
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              custom: {
                axisCenteredZero: false,
                axisColorMode: 'text',
                axisLabel: '',
                axisPlacement: 'auto',
                barAlignment: 1,
                drawStyle: 'line',
                fillOpacity: 0,
                gradientMode: 'none',
                hideFrom: {
                  legend: false,
                  tooltip: false,
                  viz: false,
                },
                lineInterpolation: 'linear',
                lineWidth: 1,
                pointSize: 5,
                scaleDistribution: {
                  type: 'linear',
                },
                showPoints: 'auto',
                spanNulls: false,
                stacking: {
                  group: 'A',
                  mode: 'percent',
                },
                thresholdsStyle: {
                  mode: 'off',
                },
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                ],
              },
              unit: 'percentunit',
            },
            overrides: [],
          },
          gridPos: {
            h: 8,
            w: 12,
            x: 12,
            y: 28,
          },
          id: 33,
          options: {
            legend: {
              calcs: [],
              displayMode: 'list',
              placement: 'bottom',
              showLegend: true,
            },
            tooltip: {
              mode: 'single',
              sort: 'none',
            },
          },
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "SELECT\n  metric_timestamp AT TIME ZONE 'UTC-8' AS time,\n  ok_vendor_users :: float / total_vendor_users AS ok_percent,\n  unknown_vendor_users :: float / total_vendor_users AS unknown_percent,\n  1 - (ok_vendor_users :: float + unknown_vendor_users) / total_vendor_users AS failed_percent\nFROM\n  vendor_user_metric\nORDER BY\n  time DESC\nLIMIT\n  90",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Percentages of User Types',
          type: 'timeseries',
        },
      ],
      title: 'Users',
      type: 'row',
    },
    {
      collapsed: true,
      gridPos: {
        h: 1,
        w: 24,
        x: 0,
        y: 28,
      },
      id: 51,
      panels: [
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          description: 'Scraping progress by repository count',
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                  {
                    color: 'red',
                    value: 500000,
                  },
                ],
              },
            },
            overrides: [],
          },
          gridPos: {
            h: 5,
            w: 12,
            x: 0,
            y: 37,
          },
          id: 11,
          options: {
            displayMode: 'lcd',
            minVizHeight: 10,
            minVizWidth: 0,
            orientation: 'horizontal',
            reduceOptions: {
              calcs: [
                'sum',
              ],
              fields: '',
              values: true,
            },
            showUnfilled: true,
          },
          pluginVersion: '9.3.6',
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              hide: false,
              rawQuery: true,
              rawSql: "select 'completed repositories', count(repo_storage.repo_name) from repo_storage;",
              refId: 'B',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "select 'total repositories', count(repo.repo_name) from repo;\n",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Progress by repository',
          transformations: [
            {
              id: 'merge',
              options: {},
            },
            {
              id: 'rowsToFields',
              options: {},
            },
          ],
          type: 'bargauge',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          description: 'Scraping progress by data size',
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                  {
                    color: 'red',
                    value: 500000,
                  },
                ],
              },
            },
            overrides: [],
          },
          gridPos: {
            h: 5,
            w: 12,
            x: 12,
            y: 37,
          },
          id: 13,
          options: {
            displayMode: 'lcd',
            minVizHeight: 10,
            minVizWidth: 0,
            orientation: 'horizontal',
            reduceOptions: {
              calcs: [
                'sum',
              ],
              fields: '',
              values: true,
            },
            showUnfilled: true,
          },
          pluginVersion: '9.3.6',
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              hide: false,
              rawQuery: true,
              rawSql: "select 'xfered GB', sum(size_kb)/(1024*1024) as GB\nfrom repo join repo_storage on repo.id = repo_storage.repo_id \nwhere repo_storage.status='done';",
              refId: 'B',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "select 'total GB', sum(size_kb)/(1024*1024) as GB\nfrom repo;",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Progress by size',
          transformations: [
            {
              id: 'merge',
              options: {},
            },
            {
              id: 'rowsToFields',
              options: {},
            },
          ],
          type: 'bargauge',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              custom: {
                axisCenteredZero: false,
                axisColorMode: 'text',
                axisLabel: '',
                axisPlacement: 'auto',
                barAlignment: 0,
                drawStyle: 'line',
                fillOpacity: 0,
                gradientMode: 'none',
                hideFrom: {
                  legend: false,
                  tooltip: false,
                  viz: false,
                },
                lineInterpolation: 'linear',
                lineWidth: 1,
                pointSize: 5,
                scaleDistribution: {
                  type: 'linear',
                },
                showPoints: 'auto',
                spanNulls: false,
                stacking: {
                  group: 'A',
                  mode: 'none',
                },
                thresholdsStyle: {
                  mode: 'off',
                },
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                  {
                    color: 'red',
                    value: 80,
                  },
                ],
              },
              unit: 'decbytes',
            },
            overrides: [],
          },
          gridPos: {
            h: 5,
            w: 12,
            x: 0,
            y: 42,
          },
          id: 15,
          options: {
            legend: {
              calcs: [],
              displayMode: 'list',
              placement: 'bottom',
              showLegend: false,
            },
            tooltip: {
              mode: 'single',
              sort: 'none',
            },
          },
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "select \n  date_trunc('minute', finished_at) as \"completion time\", \n  size_kb as size\nfrom repo_storage join repo on repo.id = repo_storage.repo_id \nwhere repo_storage.status='done';",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Bytes Xfered',
          type: 'timeseries',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              custom: {
                axisCenteredZero: false,
                axisColorMode: 'text',
                axisLabel: '',
                axisPlacement: 'auto',
                barAlignment: 0,
                drawStyle: 'line',
                fillOpacity: 0,
                gradientMode: 'none',
                hideFrom: {
                  legend: false,
                  tooltip: false,
                  viz: false,
                },
                lineInterpolation: 'linear',
                lineWidth: 1,
                pointSize: 5,
                scaleDistribution: {
                  type: 'linear',
                },
                showPoints: 'auto',
                spanNulls: false,
                stacking: {
                  group: 'A',
                  mode: 'none',
                },
                thresholdsStyle: {
                  mode: 'off',
                },
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                  {
                    color: 'red',
                    value: 80,
                  },
                ],
              },
              unit: 'gbytes',
            },
            overrides: [],
          },
          gridPos: {
            h: 5,
            w: 12,
            x: 12,
            y: 42,
          },
          id: 19,
          options: {
            legend: {
              calcs: [],
              displayMode: 'list',
              placement: 'right',
              showLegend: true,
            },
            tooltip: {
              mode: 'single',
              sort: 'none',
            },
          },
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "select partition_name::timestamp,\n  d.key language, sum(d.value::bigint*1e-9) GB\nfrom batch_job join \njson_each_text((additional_info->'head_size')::json) d\nON true \nwhere name='file_content-parquet' \nand d.value::bigint > 1e9 and \nd.key != '' \ngroup by partition_name, d.key \norder by GB desc;",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Ingestion Throughput',
          transformations: [
            {
              id: 'partitionByValues',
              options: {
                fields: [
                  'language',
                ],
              },
            },
          ],
          type: 'timeseries',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              custom: {
                axisCenteredZero: false,
                axisColorMode: 'text',
                axisLabel: '',
                axisPlacement: 'auto',
                barAlignment: 0,
                drawStyle: 'line',
                fillOpacity: 0,
                gradientMode: 'none',
                hideFrom: {
                  legend: false,
                  tooltip: false,
                  viz: false,
                },
                lineInterpolation: 'linear',
                lineWidth: 1,
                pointSize: 5,
                scaleDistribution: {
                  type: 'linear',
                },
                showPoints: 'auto',
                spanNulls: false,
                stacking: {
                  group: 'A',
                  mode: 'none',
                },
                thresholdsStyle: {
                  mode: 'off',
                },
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                ],
              },
            },
            overrides: [
              {
                matcher: {
                  id: 'byName',
                  options: 'error count',
                },
                properties: [
                  {
                    id: 'color',
                    value: {
                      mode: 'continuous-reds',
                    },
                  },
                ],
              },
            ],
          },
          gridPos: {
            h: 5,
            w: 12,
            x: 0,
            y: 47,
          },
          id: 17,
          options: {
            legend: {
              calcs: [],
              displayMode: 'list',
              placement: 'bottom',
              showLegend: true,
            },
            tooltip: {
              mode: 'single',
              sort: 'none',
            },
          },
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'time_series',
              rawQuery: true,
              rawSql: "select date_trunc('hour', start_time) as dt\n, count(status)\n, status\nfrom batch_job\nwhere status!='started'\ngroup by\ndt, status\norder by dt desc\n;",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [
                      {
                        name: 'start_time',
                        type: 'functionParameter',
                      },
                    ],
                    type: 'function',
                  },
                  {
                    parameters: [
                      {
                        name: 'status',
                        type: 'functionParameter',
                      },
                    ],
                    type: 'function',
                  },
                  {
                    name: 'COUNT',
                    parameters: [
                      {
                        name: 'status',
                        type: 'functionParameter',
                      },
                    ],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
              table: 'batch_job',
            },
          ],
          title: 'Batch Failure Rate',
          transformations: [
            {
              id: 'partitionByValues',
              options: {
                fields: [
                  'status',
                ],
              },
            },
          ],
          type: 'timeseries',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          fieldConfig: {
            defaults: {
              color: {
                mode: 'thresholds',
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                  {
                    color: 'red',
                    value: 80,
                  },
                ],
              },
            },
            overrides: [],
          },
          gridPos: {
            h: 4,
            w: 8,
            x: 16,
            y: 47,
          },
          id: 3,
          options: {
            colorMode: 'value',
            graphMode: 'area',
            justifyMode: 'auto',
            orientation: 'auto',
            reduceOptions: {
              calcs: [
                'lastNotNull',
              ],
              fields: '',
              values: false,
            },
            textMode: 'auto',
          },
          pluginVersion: '9.3.6',
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "select count(status) from repo_storage where status='started';",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Active scraping processes',
          type: 'stat',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          fieldConfig: {
            defaults: {
              color: {
                mode: 'thresholds',
              },
              custom: {
                align: 'auto',
                displayMode: 'auto',
                inspect: false,
              },
              mappings: [],
              thresholds: {
                mode: 'absolute',
                steps: [
                  {
                    color: 'green',
                  },
                  {
                    color: 'red',
                    value: 80,
                  },
                ],
              },
            },
            overrides: [
              {
                matcher: {
                  id: 'byName',
                  options: 'completion time',
                },
                properties: [
                  {
                    id: 'custom.width',
                    value: 250,
                  },
                ],
              },
              {
                matcher: {
                  id: 'byName',
                  options: 'mb',
                },
                properties: [
                  {
                    id: 'custom.width',
                    value: 85,
                  },
                ],
              },
              {
                matcher: {
                  id: 'byName',
                  options: 'primary_language',
                },
                properties: [
                  {
                    id: 'custom.width',
                    value: 136,
                  },
                ],
              },
              {
                matcher: {
                  id: 'byName',
                  options: 'commits',
                },
                properties: [
                  {
                    id: 'custom.width',
                    value: 96,
                  },
                ],
              },
              {
                matcher: {
                  id: 'byName',
                  options: 'Size(MB)',
                },
                properties: [
                  {
                    id: 'custom.width',
                    value: 102,
                  },
                ],
              },
            ],
          },
          gridPos: {
            h: 4,
            w: 16,
            x: 0,
            y: 52,
          },
          id: 7,
          options: {
            footer: {
              fields: '',
              reducer: [
                'sum',
              ],
              show: false,
            },
            showHeader: true,
            sortBy: [],
          },
          pluginVersion: '9.3.6',
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "select \n  finished_at as \"completion time\", \n  repo.repo_name, \n  size_kb/1024.0 as \"Size(MB)\", \n  primary_language, \n  commits \nfrom repo_storage join repo on repo.id = repo_storage.repo_id \nwhere repo_storage.status='done'\nlimit 5000;\n",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Completed Repositories',
          type: 'table',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          description: 'Languages of completed repos',
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              custom: {
                hideFrom: {
                  legend: false,
                  tooltip: false,
                  viz: false,
                },
              },
              mappings: [],
            },
            overrides: [
              {
                __systemRef: 'hideSeriesFrom',
                matcher: {
                  id: 'byNames',
                  options: {
                    mode: 'exclude',
                    names: [
                      'ct',
                      'other',
                    ],
                    prefix: 'All except:',
                    readOnly: true,
                  },
                },
                properties: [
                  {
                    id: 'custom.hideFrom',
                    value: {
                      legend: false,
                      tooltip: false,
                      viz: true,
                    },
                  },
                ],
              },
            ],
          },
          gridPos: {
            h: 13,
            w: 12,
            x: 0,
            y: 56,
          },
          id: 9,
          options: {
            legend: {
              displayMode: 'table',
              placement: 'right',
              showLegend: true,
            },
            pieType: 'pie',
            reduceOptions: {
              calcs: [
                'lastNotNull',
              ],
              fields: '',
              limit: 12,
              values: true,
            },
            tooltip: {
              mode: 'single',
              sort: 'none',
            },
          },
          pluginVersion: '9.3.6',
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: "select primary_language, count(primary_language) as ct \nfrom repo join repo_storage \non repo.id = repo_storage.repo_id \nwhere repo_storage.status='done' \ngroup by primary_language \norder by ct desc;",
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'Languages',
          type: 'piechart',
        },
        {
          datasource: {
            type: 'postgres',
            uid: 'metastore',
          },
          description: 'Licence types of completed repos',
          fieldConfig: {
            defaults: {
              color: {
                mode: 'palette-classic',
              },
              custom: {
                hideFrom: {
                  legend: false,
                  tooltip: false,
                  viz: false,
                },
              },
              mappings: [],
            },
            overrides: [
              {
                __systemRef: 'hideSeriesFrom',
                matcher: {
                  id: 'byNames',
                  options: {
                    mode: 'exclude',
                    names: [
                      'ct',
                      'other',
                    ],
                    prefix: 'All except:',
                    readOnly: true,
                  },
                },
                properties: [
                  {
                    id: 'custom.hideFrom',
                    value: {
                      legend: false,
                      tooltip: false,
                      viz: true,
                    },
                  },
                ],
              },
            ],
          },
          gridPos: {
            h: 13,
            w: 12,
            x: 12,
            y: 56,
          },
          id: 12,
          options: {
            legend: {
              displayMode: 'table',
              placement: 'right',
              showLegend: true,
            },
            pieType: 'pie',
            reduceOptions: {
              calcs: [
                'lastNotNull',
              ],
              fields: '',
              limit: 12,
              values: true,
            },
            tooltip: {
              mode: 'single',
              sort: 'none',
            },
          },
          pluginVersion: '9.3.6',
          targets: [
            {
              datasource: {
                type: 'postgres',
                uid: 'metastore',
              },
              editorMode: 'code',
              format: 'table',
              rawQuery: true,
              rawSql: 'select license, count(license) as ct from repo_storage group by license order by ct desc',
              refId: 'A',
              sql: {
                columns: [
                  {
                    parameters: [],
                    type: 'function',
                  },
                ],
                groupBy: [
                  {
                    property: {
                      type: 'string',
                    },
                    type: 'groupBy',
                  },
                ],
                limit: 50,
              },
            },
          ],
          title: 'License types',
          type: 'piechart',
        },
      ],
      title: 'Misc',
      type: 'row',
    },
  ],
  refresh: false,
  schemaVersion: 37,
  style: 'dark',
  tags: [],
  templating: {
    list: [],
  },
  time: {
    from: 'now-90d',
    to: 'now',
  },
  timepicker: {},
  timezone: '',
  title: 'Github Scraping',
  uid: 'github-scraping0',
  weekStart: '',
}

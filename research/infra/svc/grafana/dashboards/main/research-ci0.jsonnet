{
  annotations: {
    list: [
      {
        builtIn: 1,
        datasource: {
          type: 'grafana',
          uid: '-- <PERSON><PERSON> --',
        },
        enable: true,
        hide: true,
        iconColor: 'rgba(0, 211, 255, 1)',
        name: 'Annotations & Alerts',
        target: {
          limit: 100,
          matchAny: false,
          tags: [],
          type: 'dashboard',
        },
        type: 'dashboard',
      },
    ],
  },
  description: 'Research CI GitHub check timings written by `cronjob/github-metrics`.',
  editable: true,
  fiscalYearStartMonth: 0,
  graphTooltip: 1,
  links: [
    {
      asDropdown: true,
      icon: 'external link',
      includeVars: false,
      keepTime: false,
      tags: [],
      targetBlank: true,
      title: 'GitHub Metrics Collector',
      tooltip: 'Deployment source for the GitHub metrics collector.',
      type: 'link',
      url: 'https://github.com/augmentcode/augment/tree/main/research/infra/svc/github-metrics',
    },
  ],
  panels: [
    {
      datasource: {
        type: 'grafana-postgresql-datasource',
        uid: 'devex_metrics',
      },
      description: '',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: -1,
            barWidthFactor: 0.3,
            drawStyle: 'bars',
            fillOpacity: 100,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 0,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'always',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'normal',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          fieldMinMax: false,
          mappings: [],
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
            ],
          },
        },
        overrides: [],
      },
      gridPos: {
        h: 10,
        w: 24,
        x: 0,
        y: 0,
      },
      id: 4,
      interval: '1h',
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'grafana-postgresql-datasource',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'time_series',
          rawQuery: true,
          rawSql: "SELECT DATE_TRUNC('hour', started_at) AS time, name, COUNT(*) AS \"count\" FROM check_runs WHERE name LIKE 'CI - %' AND completed_at IS NOT NULL AND $__timeFilter(started_at) GROUP BY time, name ORDER BY time ASC",
          refId: 'ResearchCIRunTime',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
      ],
      title: 'Research CI Run Counts (hourly)',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'grafana-postgresql-datasource',
        uid: 'devex_metrics',
      },
      description: 'Raw graph of Research CI runtimes.',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineWidth: 3,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: true,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'area',
            },
          },
          fieldMinMax: false,
          mappings: [],
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: '#EAB839',
                value: 5,
              },
              {
                color: 'red',
                value: 10,
              },
            ],
          },
          unit: 'm',
        },
        overrides: [],
      },
      gridPos: {
        h: 11,
        w: 24,
        x: 0,
        y: 10,
      },
      id: 1,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'grafana-postgresql-datasource',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'time_series',
          rawQuery: true,
          rawSql: "SELECT $__time(started_at), name, EXTRACT(EPOCH FROM completed_at-started_at)/60 AS duration_m FROM check_runs WHERE name LIKE 'CI - %' AND completed_at IS NOT NULL AND $__timeFilter(started_at) ORDER BY started_at ASC",
          refId: 'ResearchCIRunTime',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
      ],
      title: 'Research CI Run Times (raw)',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'grafana-postgresql-datasource',
        uid: 'devex_metrics',
      },
      description: '',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineWidth: 3,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: true,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'area',
            },
          },
          fieldMinMax: false,
          mappings: [],
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'orange',
                value: 5,
              },
              {
                color: 'red',
                value: 10,
              },
            ],
          },
          unit: 'm',
        },
        overrides: [],
      },
      gridPos: {
        h: 10,
        w: 24,
        x: 0,
        y: 21,
      },
      id: 2,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      repeat: 'percentiles',
      repeatDirection: 'v',
      targets: [
        {
          datasource: {
            type: 'grafana-postgresql-datasource',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'time_series',
          rawQuery: true,
          rawSql: "SELECT date_trunc('$pctile_window', started_at) AS time, name, percentile_cont($percentiles/100) WITHIN GROUP (ORDER BY duration_m ASC) AS duration_m_${percentiles}p FROM (\n  SELECT started_at, name, EXTRACT(EPOCH FROM completed_at-started_at)/60 AS duration_m FROM check_runs WHERE name LIKE 'CI - %' AND completed_at IS NOT NULL AND $__timeFilter(started_at) ORDER BY started_at ASC\n) GROUP BY time, name ORDER BY time;",
          refId: 'ResearchCIRunTime',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
      ],
      title: 'Research CI Run Times (${percentiles}%ile over one ${pctile_window} windows)',
      type: 'timeseries',
    },
  ],
  refresh: '',
  schemaVersion: 40,
  tags: [],
  templating: {
    list: [
      {
        allValue: '',
        current: {
          text: [
            '100',
            '90',
            '50',
          ],
          value: [
            '100',
            '90',
            '50',
          ],
        },
        description: 'Choose which percentiles to display, each as a separate panel.',
        includeAll: false,
        label: '%-iles',
        multi: true,
        name: 'percentiles',
        options: [
          {
            selected: true,
            text: '100',
            value: '100',
          },
          {
            selected: false,
            text: '99',
            value: '99',
          },
          {
            selected: false,
            text: '95',
            value: '95',
          },
          {
            selected: true,
            text: '90',
            value: '90',
          },
          {
            selected: false,
            text: '75',
            value: '75',
          },
          {
            selected: true,
            text: '50',
            value: '50',
          },
        ],
        query: '100, 99, 95, 90, 75, 50',
        type: 'custom',
      },
      {
        current: {
          text: 'day',
          value: 'day',
        },
        description: '',
        label: '%-ile Window',
        name: 'pctile_window',
        options: [
          {
            selected: false,
            text: 'hour',
            value: 'hour',
          },
          {
            selected: true,
            text: 'day',
            value: 'day',
          },
          {
            selected: false,
            text: 'week',
            value: 'week',
          },
          {
            selected: false,
            text: 'month',
            value: 'month',
          },
        ],
        query: 'hour, day, week, month',
        type: 'custom',
      },
    ],
  },
  time: {
    from: 'now-7d',
    to: 'now',
  },
  timepicker: {},
  timezone: 'browser',
  title: 'Research CI',
  uid: 'research-ci0',
  weekStart: '',
}

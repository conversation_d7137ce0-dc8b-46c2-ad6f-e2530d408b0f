{
  annotations: {
    list: [
      {
        builtIn: 1,
        datasource: {
          type: 'grafana',
          uid: '-- <PERSON>ana --',
        },
        enable: true,
        hide: true,
        iconColor: 'rgba(0, 211, 255, 1)',
        name: 'Annotations & Alerts',
        target: {
          limit: 100,
          matchAny: false,
          tags: [],
          type: 'dashboard',
        },
        type: 'dashboard',
      },
    ],
  },
  editable: true,
  fiscalYearStartMonth: 0,
  graphTooltip: 0,
  links: [],
  liveNow: false,
  panels: [
    {
      datasource: {
        type: 'prometheus',
        uid: 'coreweave',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 0,
      },
      id: 2,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '9.3.6',
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'coreweave',
          },
          editorMode: 'code',
          exemplar: false,
          expr: 'kube_pod_created{pod=~"hydra-.*"}',
          format: 'table',
          instant: false,
          legendFormat: '__auto',
          range: true,
          refId: 'A',
        },
      ],
      title: 'Hydra Pod Creation',
      transformations: [
        {
          id: 'calculateField',
          options: {
            alias: 'MS',
            binary: {
              left: 'Value',
              operator: '*',
              reducer: 'sum',
              right: '1000',
            },
            mode: 'binary',
            reduce: {
              reducer: 'sum',
            },
          },
        },
        {
          id: 'convertFieldType',
          options: {
            conversions: [
              {
                destinationType: 'time',
                targetField: 'MS',
              },
            ],
            fields: {},
          },
        },
        {
          id: 'organize',
          options: {
            excludeByName: {
              Time: true,
              __name__: true,
              cluster: true,
              cluster_org: true,
              container: true,
              endpoint: true,
              instance: true,
              job: true,
              namespace: true,
              prometheus: true,
              prometheus_replica: true,
              service: true,
              uid: true,
            },
            indexByName: {},
            renameByName: {},
          },
        },
        {
          id: 'groupBy',
          options: {
            fields: {
              MS: {
                aggregations: [],
                operation: 'groupby',
              },
              Value: {
                aggregations: [
                  'count',
                ],
                operation: 'aggregate',
              },
            },
          },
        },
      ],
      type: 'timeseries',
    },
  ],
  refresh: false,
  schemaVersion: 37,
  style: 'dark',
  tags: [],
  templating: {
    list: [],
  },
  time: {
    from: '2023-09-18T08:10:19.354Z',
    to: '2023-09-19T01:39:23.314Z',
  },
  timepicker: {},
  timezone: '',
  title: 'Hydra Pod Creation',
  uid: 'hydra-pod-creation',
  weekStart: '',
}

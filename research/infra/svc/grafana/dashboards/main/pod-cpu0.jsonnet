{
  annotations: {
    list: [
      {
        builtIn: 1,
        datasource: {
          type: 'grafana',
          uid: '-- <PERSON>ana --',
        },
        enable: true,
        hide: true,
        iconColor: 'rgba(0, 211, 255, 1)',
        name: 'Annotations & Alerts',
        target: {
          limit: 100,
          matchAny: false,
          tags: [],
          type: 'dashboard',
        },
        type: 'dashboard',
      },
    ],
  },
  editable: true,
  fiscalYearStartMonth: 0,
  graphTooltip: 0,
  links: [],
  liveNow: false,
  panels: [
    {
      datasource: {
        type: 'prometheus',
        uid: 'coreweave',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
        },
        overrides: [],
      },
      gridPos: {
        h: 9,
        w: 24,
        x: 0,
        y: 0,
      },
      id: 2,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'coreweave',
          },
          editorMode: 'code',
          expr: 'max_over_time(\n    (\n        rate(container_cpu_usage_seconds_total [$__rate_interval])\n        / on(namespace, pod, container, node)\n        kube_pod_container_resource_requests{resource="cpu"}\n    )\n    [$__range:2m]\n) < 0.05',
          legendFormat: '__auto',
          range: true,
          refId: 'A',
        },
      ],
      title: 'Pod CPU Utilization',
      type: 'timeseries',
    },
  ],
  schemaVersion: 37,
  style: 'dark',
  tags: [],
  templating: {
    list: [],
  },
  time: {
    from: 'now-15m',
    to: 'now',
  },
  timepicker: {},
  timezone: '',
  title: 'Pod CPU',
  uid: 'pod-cpu0',
  weekStart: '',
}

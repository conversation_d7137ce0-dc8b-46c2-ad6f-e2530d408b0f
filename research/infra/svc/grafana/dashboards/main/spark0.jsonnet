{
  annotations: {
    list: [
      {
        builtIn: 1,
        datasource: {
          type: 'grafana',
          uid: '-- <PERSON>ana --',
        },
        enable: true,
        hide: true,
        iconColor: 'rgba(0, 211, 255, 1)',
        name: 'Annotations & Alerts',
        target: {
          limit: 100,
          matchAny: false,
          tags: [],
          type: 'dashboard',
        },
        type: 'dashboard',
      },
    ],
  },
  description: 'Status of pods in a spark run',
  editable: true,
  fiscalYearStartMonth: 0,
  graphTooltip: 0,
  links: [],
  liveNow: false,
  panels: [
    {
      datasource: {
        type: 'prometheus',
        uid: 'coreweave',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'thresholds',
          },
          displayName: 'Executors',
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'string',
        },
        overrides: [],
      },
      gridPos: {
        h: 5,
        w: 8,
        x: 8,
        y: 0,
      },
      id: 9,
      options: {
        colorMode: 'value',
        graphMode: 'area',
        justifyMode: 'auto',
        orientation: 'auto',
        reduceOptions: {
          calcs: [
            'lastNotNull',
          ],
          fields: '',
          values: false,
        },
        textMode: 'value',
      },
      pluginVersion: '9.3.6',
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'coreweave',
          },
          editorMode: 'builder',
          exemplar: false,
          expr: 'count(node_namespace_pod:kube_pod_info:{pod=~".*$pod_id.*"})',
          format: 'table',
          instant: false,
          legendFormat: 'Count',
          range: true,
          refId: 'A',
        },
      ],
      title: 'Executor Count',
      transformations: [
        {
          id: 'reduce',
          options: {
            includeTimeField: false,
            mode: 'reduceFields',
            reducers: [
              'lastNotNull',
            ],
          },
        },
      ],
      type: 'stat',
    },
    {
      datasource: {
        type: 'prometheus',
        uid: 'coreweave',
      },
      fieldConfig: {
        defaults: {
          color: {
            fixedColor: 'blue',
            mode: 'fixed',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 50,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 12,
        x: 0,
        y: 5,
      },
      id: 6,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'coreweave',
          },
          editorMode: 'builder',
          exemplar: false,
          expr: 'sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{container!="POD", pod="$driver_pod_id"})',
          legendFormat: '{{pod}}',
          range: true,
          refId: 'A',
        },
      ],
      title: 'Driver CPU Usage',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'prometheus',
        uid: 'coreweave',
      },
      fieldConfig: {
        defaults: {
          color: {
            fixedColor: 'blue',
            mode: 'fixed',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 50,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'decbits',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 12,
        x: 12,
        y: 5,
      },
      id: 7,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'coreweave',
          },
          editorMode: 'builder',
          exemplar: false,
          expr: 'sum(container_memory_rss{container!="POD", pod=~"$driver_pod_id"})',
          legendFormat: '{{pod}}',
          range: true,
          refId: 'A',
        },
      ],
      title: 'Driver Memory Usage',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'prometheus',
        uid: 'coreweave',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 50,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 12,
        x: 0,
        y: 13,
      },
      id: 2,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'coreweave',
          },
          editorMode: 'builder',
          exemplar: false,
          expr: 'sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{container!="POD", pod=~".*$pod_id.*"})',
          legendFormat: '{{pod}}',
          range: true,
          refId: 'A',
        },
      ],
      title: 'Total Executor CPU Usage',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'prometheus',
        uid: 'coreweave',
      },
      fieldConfig: {
        defaults: {
          color: {
            fixedColor: 'yellow',
            mode: 'fixed',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 50,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 12,
        x: 12,
        y: 13,
      },
      id: 3,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'coreweave',
          },
          editorMode: 'builder',
          exemplar: false,
          expr: 'avg(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{container!="POD", pod=~".*$pod_id.*"})',
          legendFormat: '{{pod}}',
          range: true,
          refId: 'A',
        },
      ],
      title: 'Avg Executor CPU Usage',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'prometheus',
        uid: 'coreweave',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 50,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'decbits',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 12,
        x: 0,
        y: 21,
      },
      id: 4,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'coreweave',
          },
          editorMode: 'builder',
          exemplar: false,
          expr: 'sum(container_memory_rss{container!="POD", pod=~".*$pod_id.*"})',
          legendFormat: '{{pod}}',
          range: true,
          refId: 'A',
        },
      ],
      title: 'Total Executor Memory Usage',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'prometheus',
        uid: 'coreweave',
      },
      fieldConfig: {
        defaults: {
          color: {
            fixedColor: 'yellow',
            mode: 'fixed',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 50,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'blue',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'decbits',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 12,
        x: 12,
        y: 21,
      },
      id: 5,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'coreweave',
          },
          editorMode: 'builder',
          exemplar: false,
          expr: 'avg(container_memory_rss{container!="POD", pod=~".*$pod_id.*"})',
          legendFormat: '{{pod}}',
          range: true,
          refId: 'A',
        },
      ],
      title: 'Avg Executor Memory Usage',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'prometheus',
        uid: 'coreweave',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 71,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'normal',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'bps',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 12,
        x: 0,
        y: 29,
      },
      id: 11,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'coreweave',
          },
          editorMode: 'builder',
          expr: 'sum(irate(container_network_transmit_bytes_total{pod=~".*$pod_id.*"}[3m]))*8',
          hide: false,
          legendFormat: 'Receive',
          range: true,
          refId: 'A',
        },
        {
          datasource: {
            type: 'prometheus',
            uid: 'coreweave',
          },
          editorMode: 'builder',
          expr: 'sum(irate(container_network_receive_bytes_total{pod=~".*$pod_id.*"}[3m])) * 8',
          hide: false,
          legendFormat: 'Transmit',
          range: true,
          refId: 'B',
        },
      ],
      title: 'Executor Network Throughput',
      type: 'timeseries',
    },
  ],
  schemaVersion: 37,
  style: 'dark',
  tags: [],
  templating: {
    list: [
      {
        current: {
          selected: false,
          text: '',
          value: '',
        },
        hide: 0,
        name: 'pod_id',
        options: [
          {
            selected: false,
            text: '',
            value: '',
          },
        ],
        query: '',
        skipUrlSync: false,
        type: 'textbox',
      },
      {
        current: {
          selected: false,
          text: '',
          value: '',
        },
        hide: 0,
        name: 'driver_pod_id',
        options: [
          {
            selected: false,
            text: '',
            value: '',
          },
        ],
        query: '',
        skipUrlSync: false,
        type: 'textbox',
      },
    ],
  },
  time: {
    from: 'now-6h',
    to: 'now',
  },
  timepicker: {},
  timezone: '',
  title: 'Spark Status',
  uid: 'spark0',
  weekStart: '',
}

{
  annotations: {
    list: [
      {
        builtIn: 1,
        datasource: '-- <PERSON>ana --',
        enable: true,
        hide: true,
        iconColor: 'rgba(0, 211, 255, 1)',
        name: 'Annotations & Alerts',
        type: 'dashboard',
      },
    ],
  },
  description: 'Monitor a Kubernetes cluster using Prometheus TSDB.  Shows overall cluster CPU / Memory / Disk usage as well as individual pod statistics.\r\n\r\nModified version of dashboard #162 to work with Prometheus 2.1+. \r\n\r\nNote - Managed Kubernetes instances (such as Azure AKS) might not give you access to FS stats, as you can see in the screenshot. You should be able to see those stats otherwise.',
  editable: true,
  gnetId: 6663,
  graphTooltip: 1,
  links: [],
  panels: [
    {
      cacheTimeout: null,
      colorBackground: false,
      colorValue: false,
      colors: [
        'rgba(50, 172, 45, 0.97)',
        'rgba(237, 129, 40, 0.89)',
        'rgba(245, 54, 54, 0.9)',
      ],
      datasource: 'coreweave',
      editable: true,
      'error': false,
      format: 'percent',
      gauge: {
        maxValue: 100,
        minValue: 0,
        show: true,
        thresholdLabels: false,
        thresholdMarkers: true,
      },
      gridPos: {
        h: 7,
        w: 8,
        x: 0,
        y: 0,
      },
      id: 4,
      interval: null,
      isNew: true,
      links: [],
      mappingType: 1,
      mappingTypes: [
        {
          name: 'value to text',
          value: 1,
        },
        {
          name: 'range to text',
          value: 2,
        },
      ],
      maxDataPoints: 100,
      nullPointMode: 'connected',
      nullText: null,
      postfix: '',
      postfixFontSize: '50%',
      prefix: '',
      prefixFontSize: '50%',
      rangeMaps: [
        {
          from: 'null',
          text: 'N/A',
          to: 'null',
        },
      ],
      sparkline: {
        fillColor: 'rgba(31, 118, 189, 0.18)',
        full: false,
        lineColor: 'rgb(31, 120, 193)',
        show: false,
      },
      tableColumn: '',
      targets: [
        {
          expr: '(sum(node_memory_MemTotal) - sum(node_memory_MemFree+node_memory_Buffers+node_memory_Cached) ) / sum(node_memory_MemTotal) * 100',
          interval: '10s',
          intervalFactor: 1,
          refId: 'A',
          step: 10,
        },
      ],
      thresholds: '65, 90',
      title: 'Cluster memory usage',
      type: 'singlestat',
      valueFontSize: '80%',
      valueMaps: [
        {
          op: '=',
          text: 'N/A',
          value: 'null',
        },
      ],
      valueName: 'current',
    },
    {
      cacheTimeout: null,
      colorBackground: false,
      colorValue: false,
      colors: [
        'rgba(50, 172, 45, 0.97)',
        'rgba(237, 129, 40, 0.89)',
        'rgba(245, 54, 54, 0.9)',
      ],
      datasource: 'coreweave',
      decimals: 2,
      editable: true,
      'error': false,
      format: 'percent',
      gauge: {
        maxValue: 100,
        minValue: 0,
        show: true,
        thresholdLabels: false,
        thresholdMarkers: true,
      },
      gridPos: {
        h: 7,
        w: 8,
        x: 8,
        y: 0,
      },
      id: 6,
      interval: null,
      isNew: true,
      links: [],
      mappingType: 1,
      mappingTypes: [
        {
          name: 'value to text',
          value: 1,
        },
        {
          name: 'range to text',
          value: 2,
        },
      ],
      maxDataPoints: 100,
      nullPointMode: 'connected',
      nullText: null,
      postfix: '',
      postfixFontSize: '50%',
      prefix: '',
      prefixFontSize: '50%',
      rangeMaps: [
        {
          from: 'null',
          text: 'N/A',
          to: 'null',
        },
      ],
      sparkline: {
        fillColor: 'rgba(31, 118, 189, 0.18)',
        full: false,
        lineColor: 'rgb(31, 120, 193)',
        show: false,
      },
      tableColumn: '',
      targets: [
        {
          expr: 'sum(sum by (container_name)( rate(container_cpu_usage_seconds_total{image!=""}[5m] ) )) / count(node_cpu{mode="system"}) * 100',
          format: 'time_series',
          interval: '10s',
          intervalFactor: 1,
          refId: 'A',
          step: 10,
        },
      ],
      thresholds: '65, 90',
      title: 'Cluster CPU usage',
      type: 'singlestat',
      valueFontSize: '80%',
      valueMaps: [
        {
          op: '=',
          text: 'N/A',
          value: 'null',
        },
      ],
      valueName: 'current',
    },
    {
      cacheTimeout: null,
      colorBackground: false,
      colorValue: false,
      colors: [
        'rgba(50, 172, 45, 0.97)',
        'rgba(237, 129, 40, 0.89)',
        'rgba(245, 54, 54, 0.9)',
      ],
      datasource: 'coreweave',
      decimals: 2,
      editable: true,
      'error': false,
      format: 'percent',
      gauge: {
        maxValue: 100,
        minValue: 0,
        show: true,
        thresholdLabels: false,
        thresholdMarkers: true,
      },
      gridPos: {
        h: 7,
        w: 8,
        x: 16,
        y: 0,
      },
      id: 7,
      interval: null,
      isNew: true,
      links: [],
      mappingType: 1,
      mappingTypes: [
        {
          name: 'value to text',
          value: 1,
        },
        {
          name: 'range to text',
          value: 2,
        },
      ],
      maxDataPoints: 100,
      nullPointMode: 'connected',
      nullText: null,
      postfix: '',
      postfixFontSize: '50%',
      prefix: '',
      prefixFontSize: '50%',
      rangeMaps: [
        {
          from: 'null',
          text: 'N/A',
          to: 'null',
        },
      ],
      sparkline: {
        fillColor: 'rgba(31, 118, 189, 0.18)',
        full: false,
        lineColor: 'rgb(31, 120, 193)',
        show: false,
      },
      tableColumn: '',
      targets: [
        {
          expr: '(sum(node_filesystem_size{device="rootfs"}) - sum(node_filesystem_free{device="rootfs"}) ) / sum(node_filesystem_size{device="rootfs"}) * 100',
          format: 'time_series',
          interval: '10s',
          intervalFactor: 1,
          metric: '',
          refId: 'A',
          step: 10,
        },
      ],
      thresholds: '65, 90',
      title: 'Cluster Filesystem usage',
      type: 'singlestat',
      valueFontSize: '80%',
      valueMaps: [
        {
          op: '=',
          text: 'N/A',
          value: 'null',
        },
      ],
      valueName: 'current',
    },
    {
      aliasColors: {},
      bars: false,
      dashLength: 10,
      dashes: false,
      datasource: 'coreweave',
      decimals: 3,
      editable: true,
      'error': false,
      fill: 0,
      grid: {},
      gridPos: {
        h: 7,
        w: 24,
        x: 0,
        y: 7,
      },
      id: 3,
      isNew: true,
      legend: {
        alignAsTable: true,
        avg: true,
        current: true,
        max: false,
        min: false,
        rightSide: true,
        show: true,
        sort: 'current',
        sortDesc: true,
        total: false,
        values: true,
      },
      lines: true,
      linewidth: 2,
      links: [],
      nullPointMode: 'connected',
      percentage: false,
      pointradius: 5,
      points: false,
      renderer: 'flot',
      seriesOverrides: [],
      spaceLength: 10,
      stack: false,
      steppedLine: false,
      targets: [
        {
          expr: 'sum by (container_name)( rate(container_cpu_usage_seconds_total{image!=""}[5m] ) )',
          format: 'time_series',
          interval: '10s',
          intervalFactor: 1,
          legendFormat: '{{ container_name }}',
          metric: 'container_cpu',
          refId: 'A',
          step: 10,
        },
      ],
      thresholds: [],
      timeFrom: null,
      timeShift: null,
      title: 'Pod CPU usage',
      tooltip: {
        msResolution: true,
        shared: true,
        sort: 0,
        value_type: 'cumulative',
      },
      type: 'graph',
      xaxis: {
        buckets: null,
        mode: 'time',
        name: null,
        show: true,
        values: [],
      },
      yaxes: [
        {
          format: 'percent',
          label: null,
          logBase: 1,
          max: null,
          min: null,
          show: true,
        },
        {
          format: 'short',
          label: null,
          logBase: 1,
          max: null,
          min: null,
          show: true,
        },
      ],
    },
    {
      aliasColors: {},
      bars: false,
      dashLength: 10,
      dashes: false,
      datasource: 'coreweave',
      decimals: 2,
      editable: true,
      'error': false,
      fill: 0,
      grid: {},
      gridPos: {
        h: 7,
        w: 24,
        x: 0,
        y: 14,
      },
      id: 2,
      isNew: true,
      legend: {
        alignAsTable: true,
        avg: true,
        current: true,
        max: false,
        min: false,
        rightSide: true,
        show: true,
        sideWidth: 200,
        sort: 'current',
        sortDesc: true,
        total: false,
        values: true,
      },
      lines: true,
      linewidth: 2,
      links: [],
      nullPointMode: 'connected',
      percentage: false,
      pointradius: 5,
      points: false,
      renderer: 'flot',
      seriesOverrides: [],
      spaceLength: 10,
      stack: false,
      steppedLine: false,
      targets: [
        {
          expr: 'sort_desc(sum(container_memory_usage_bytes{image!=""}) by (container_name, image))',
          interval: '10s',
          intervalFactor: 1,
          legendFormat: '{{ container_name }}',
          metric: 'container_memory_usage:sort_desc',
          refId: 'A',
          step: 10,
        },
      ],
      thresholds: [],
      timeFrom: null,
      timeShift: null,
      title: 'Pod memory usage',
      tooltip: {
        msResolution: false,
        shared: true,
        sort: 0,
        value_type: 'cumulative',
      },
      type: 'graph',
      xaxis: {
        buckets: null,
        mode: 'time',
        name: null,
        show: true,
        values: [],
      },
      yaxes: [
        {
          format: 'bytes',
          label: null,
          logBase: 1,
          max: null,
          min: null,
          show: true,
        },
        {
          format: 'short',
          label: null,
          logBase: 1,
          max: null,
          min: null,
          show: true,
        },
      ],
    },
    {
      aliasColors: {},
      bars: false,
      dashLength: 10,
      dashes: false,
      datasource: 'coreweave',
      decimals: 2,
      editable: true,
      'error': false,
      fill: 0,
      grid: {},
      gridPos: {
        h: 7,
        w: 24,
        x: 0,
        y: 21,
      },
      id: 8,
      isNew: true,
      legend: {
        alignAsTable: true,
        avg: true,
        current: true,
        max: false,
        min: false,
        rightSide: true,
        show: true,
        sideWidth: 200,
        sort: 'current',
        sortDesc: true,
        total: false,
        values: true,
      },
      lines: true,
      linewidth: 2,
      links: [],
      nullPointMode: 'connected',
      percentage: false,
      pointradius: 5,
      points: false,
      renderer: 'flot',
      seriesOverrides: [],
      spaceLength: 10,
      stack: false,
      steppedLine: false,
      targets: [
        {
          expr: 'sort_desc(sum by (pod_name) (rate (container_network_receive_bytes_total{name!=""}[5m])))',
          format: 'time_series',
          interval: '10s',
          intervalFactor: 1,
          legendFormat: '{{ pod_name }}',
          metric: 'network',
          refId: 'A',
          step: 10,
        },
        {
          expr: 'sort_desc(sum by (pod_name) (rate (container_network_transmit_bytes_total{name!=""}[5m])))',
          format: 'time_series',
          interval: '10s',
          intervalFactor: 1,
          legendFormat: '{{ pod_name }}',
          metric: 'network',
          refId: 'B',
          step: 10,
        },
      ],
      thresholds: [],
      timeFrom: null,
      timeShift: null,
      title: 'Pod Network i/o',
      tooltip: {
        msResolution: false,
        shared: true,
        sort: 0,
        value_type: 'cumulative',
      },
      type: 'graph',
      xaxis: {
        buckets: null,
        mode: 'time',
        name: null,
        show: true,
        values: [],
      },
      yaxes: [
        {
          format: 'bytes',
          label: null,
          logBase: 1,
          max: null,
          min: null,
          show: true,
        },
        {
          format: 'short',
          label: null,
          logBase: 1,
          max: null,
          min: null,
          show: true,
        },
      ],
    },
  ],
  refresh: '10s',
  schemaVersion: 16,
  style: 'dark',
  tags: [
    'kubernetes',
  ],
  templating: {
    list: [],
  },
  time: {
    from: 'now-1h',
    to: 'now',
  },
  timepicker: {
    refresh_intervals: [
      '5s',
      '10s',
      '30s',
      '1m',
      '5m',
      '15m',
      '30m',
      '1h',
      '2h',
      '1d',
    ],
    time_options: [
      '5m',
      '15m',
      '1h',
      '6h',
      '12h',
      '24h',
      '2d',
      '7d',
      '30d',
    ],
  },
  timezone: 'browser',
  title: 'Kubernetes pod and cluster monitoring (via Prometheus)',
  uid: 'k8s-pod-and-cluster',
}

{
  annotations: {
    list: [
      {
        builtIn: 1,
        datasource: {
          type: 'grafana',
          uid: '-- <PERSON>ana --',
        },
        enable: true,
        hide: true,
        iconColor: 'rgba(0, 211, 255, 1)',
        name: 'Annotations & Alerts',
        target: {
          limit: 100,
          matchAny: false,
          tags: [],
          type: 'dashboard',
        },
        type: 'dashboard',
      },
    ],
  },
  editable: true,
  fiscalYearStartMonth: 0,
  graphTooltip: 0,
  links: [],
  liveNow: false,
  panels: [
    {
      datasource: {
        default: false,
        type: 'prometheus',
        uid: 'cw-east4',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            axisSoftMax: 100,
            axisSoftMin: 0,
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 1,
            pointSize: 3,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'never',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
        },
        overrides: [],
      },
      gridPos: {
        h: 6,
        w: 23,
        x: 0,
        y: 0,
      },
      id: 4,
      interval: '5m',
      maxDataPoints: 500,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'cw-east4',
          },
          editorMode: 'code',
          expr: '100 * sum(kubelet_volume_stats_used_bytes{persistentvolumeclaim=~"$persistent_volume_claim", namespace="cw-east4", persistentvolumeclaim=~"aug-.*"}) by (persistentvolumeclaim) / sum(kubelet_volume_stats_capacity_bytes) by (persistentvolumeclaim)',
          legendFormat: '__auto',
          range: true,
          refId: 'A',
        },
      ],
      title: 'Used Pct',
      type: 'timeseries',
    },
    {
      datasource: {
        default: false,
        type: 'prometheus',
        uid: 'cw-east4',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'series',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 1,
            pointSize: 3,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'never',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
            ],
          },
          unit: 'decbytes',
        },
        overrides: [],
      },
      gridPos: {
        h: 6,
        w: 23,
        x: 0,
        y: 6,
      },
      id: 5,
      interval: '5m',
      maxDataPoints: 500,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'cw-east4',
          },
          editorMode: 'code',
          expr: 'avg(kubelet_volume_stats_used_bytes{persistentvolumeclaim=~"$persistent_volume_claim", persistentvolumeclaim=~"aug-.*", namespace="cw-east4"}) by (persistentvolumeclaim)',
          legendFormat: '__auto',
          range: true,
          refId: 'A',
        },
      ],
      title: 'Used Capacity',
      type: 'timeseries',
    },
    {
      datasource: {
        default: false,
        type: 'prometheus',
        uid: 'cw-east4',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 1,
            pointSize: 3,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'never',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'decbytes',
        },
        overrides: [],
      },
      gridPos: {
        h: 6,
        w: 23,
        x: 0,
        y: 12,
      },
      id: 6,
      interval: '5m',
      maxDataPoints: 500,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'cw-east4',
          },
          editorMode: 'code',
          expr: 'sum(kubelet_volume_stats_used_bytes{persistentvolumeclaim=~"aug-.*", namespace="cw-east4"})',
          legendFormat: '__auto',
          range: true,
          refId: 'A',
        },
      ],
      title: 'Total Share Capacity (aug-cw-.*)',
      type: 'timeseries',
    },
  ],
  refresh: '10s',
  schemaVersion: 39,
  tags: [],
  templating: {
    list: [
      {
        allValue: '',
        current: {
          selected: false,
          text: 'All',
          value: '$__all',
        },
        datasource: {
          type: 'prometheus',
          uid: 'cw-east4',
        },
        definition: 'kubelet_volume_stats_available_bytes',
        hide: 0,
        includeAll: true,
        label: 'Volume',
        multi: false,
        name: 'persistent_volume_claim',
        options: [],
        query: {
          qryType: 4,
          query: 'kubelet_volume_stats_available_bytes',
          refId: 'PrometheusVariableQueryEditor-VariableQuery',
        },
        refresh: 1,
        regex: '/.*persistentvolumeclaim="([^"]*)",.*/',
        skipUrlSync: false,
        sort: 0,
        type: 'query',
      },
    ],
  },
  time: {
    from: 'now-7d',
    to: 'now',
  },
  timepicker: {},
  timezone: '',
  title: 'Disk Usage',
  uid: 'disk-usage0',
  weekStart: '',
}

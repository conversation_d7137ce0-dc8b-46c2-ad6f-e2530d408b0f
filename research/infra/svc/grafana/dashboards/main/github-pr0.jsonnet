{
  annotations: {
    list: [
      {
        builtIn: 1,
        datasource: {
          type: 'grafana',
          uid: '-- <PERSON>ana --',
        },
        enable: true,
        hide: true,
        iconColor: 'rgba(0, 211, 255, 1)',
        name: 'Annotations & Alerts',
        target: {
          limit: 100,
          matchAny: false,
          tags: [],
          type: 'dashboard',
        },
        type: 'dashboard',
      },
    ],
  },
  editable: true,
  fiscalYearStartMonth: 0,
  graphTooltip: 0,
  links: [],
  liveNow: false,
  panels: [
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'bars',
            fillOpacity: 100,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 6,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 's',
        },
        overrides: [],
      },
      gridPos: {
        h: 9,
        w: 23,
        x: 0,
        y: 0,
      },
      id: 2,
      interval: '1d',
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: "select to_timestamp($__timeGroup(first_review_ts, '$__interval')) as time_bucket,\n    percentile_cont(0.5) within group (\n        order by extract (\n            epoch from(greatest(interval '0s',first_review_ts - created_at)) \n        ) \n    ) as median_time_to_first_review\n    from pull_requests, (\n        select pr_num, min(ts) as first_review_ts\n        from pull_request_events\n        where event_type = 'PullRequestReview' group by pr_num\n    ) as first_reviews\n    where pull_requests.pr_num = first_reviews.pr_num\n    and first_review_ts is not null and\n    created_at > now() - interval '1 year'\n    group by time_bucket;",
          refId: 'A',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
      ],
      title: 'Median time to review',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
          },
          mappings: [],
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: '1-3h',
            },
            properties: [
              {
                id: 'color',
                value: {
                  fixedColor: 'red',
                  mode: 'fixed',
                },
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: '5-12h',
            },
            properties: [
              {
                id: 'color',
                value: {
                  fixedColor: 'semi-dark-green',
                  mode: 'fixed',
                },
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'All Day',
            },
            properties: [
              {
                id: 'color',
                value: {
                  fixedColor: 'dark-purple',
                  mode: 'fixed',
                },
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'Still waiting',
            },
            properties: [
              {
                id: 'color',
                value: {
                  fixedColor: 'super-light-green',
                  mode: 'fixed',
                },
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 8,
        w: 6,
        x: 0,
        y: 9,
      },
      id: 5,
      options: {
        displayLabels: [
          'percent',
        ],
        legend: {
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
          values: [],
        },
        pieType: 'pie',
        reduceOptions: {
          calcs: [
            'lastNotNull',
          ],
          fields: '',
          values: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: "select case \n    when extract (epoch from(greatest(interval '0s',first_review_ts - created_at))) < 3600 then '0-1h'\n    when extract (epoch from(greatest(interval '0s',first_review_ts - created_at))) < 10800 then '1-3h'\n    when extract (epoch from(greatest(interval '0s',first_review_ts - created_at))) < 18000 then '3-5h'\n    when extract (epoch from(greatest(interval '0s',first_review_ts - created_at))) < 43200 then '5-12h'\n    when extract (epoch from(greatest(interval '0s',first_review_ts - created_at))) < 86400 then 'All Day'\n    else 'Still waiting'\n    end as time_to_first_review, count(*)\nfrom pull_requests, (\n    select pr_num, min(ts) as first_review_ts\n        from pull_request_events\n        where event_type = 'PullRequestReview' group by pr_num\n    ) as first_reviews\n    where pull_requests.pr_num = first_reviews.pr_num\n    and first_review_ts is not null and\n    created_at > now() - interval '6 months' group by time_to_first_review;",
          refId: 'A',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
      ],
      title: 'Time to receive review',
      type: 'piechart',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
          },
          mappings: [],
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: '1-3h',
            },
            properties: [
              {
                id: 'color',
                value: {
                  fixedColor: 'red',
                  mode: 'fixed',
                },
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: '5-12h',
            },
            properties: [
              {
                id: 'color',
                value: {
                  fixedColor: 'semi-dark-green',
                  mode: 'fixed',
                },
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'All Day',
            },
            properties: [
              {
                id: 'color',
                value: {
                  fixedColor: 'dark-purple',
                  mode: 'fixed',
                },
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'Still waiting',
            },
            properties: [
              {
                id: 'color',
                value: {
                  fixedColor: 'super-light-green',
                  mode: 'fixed',
                },
              },
            ],
          },
          {
            __systemRef: 'hideSeriesFrom',
            matcher: {
              id: 'byNames',
              options: {
                mode: 'exclude',
                names: [
                  'count',
                  '0-1h',
                ],
                prefix: 'All except:',
                readOnly: true,
              },
            },
            properties: [
              {
                id: 'custom.hideFrom',
                value: {
                  legend: false,
                  tooltip: false,
                  viz: true,
                },
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: '0-1h',
            },
            properties: [
              {
                id: 'color',
                value: {
                  fixedColor: 'blue',
                  mode: 'fixed',
                },
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: '3-5h',
            },
            properties: [
              {
                id: 'color',
                value: {
                  fixedColor: 'semi-dark-orange',
                  mode: 'fixed',
                },
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 8,
        w: 6,
        x: 6,
        y: 9,
      },
      id: 6,
      options: {
        displayLabels: [
          'percent',
        ],
        legend: {
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
          values: [],
        },
        pieType: 'pie',
        reduceOptions: {
          calcs: [
            'lastNotNull',
          ],
          fields: '',
          values: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: "select time_to_first_approval\n    , count(time_to_first_approval) as count\nfrom (\n    select created_at\n        , case \n            when ttfr < 3600 then '0-1h'\n            when ttfr < 10800 then '1-3h'\n            when ttfr < 18000 then '3-5h'\n            when ttfr < 43200 then '5-12h'\n            when ttfr < 86400 then 'All Day'\n            else 'Still waiting'\n        end as time_to_first_approval\n        from (\n            select created_at\n                , greatest(extract (epoch from(first_approval_ts - first_requested_review_ts)), 0) as ttfr\n                from pull_requests, \n                (\n                    select pr_num\n                        , min(ts) as first_approval_ts\n                    from pull_request_events\n                    where event_type = 'PullRequestReview' and state = 'APPROVED' group by pr_num\n                ) as approvals, \n                (\n                    select pr_num, min(ts) as first_requested_review_ts\n                    from pull_request_events\n                    where event_type = 'ReviewRequestedEvent' group by pr_num\n                ) as first_requested_review\n                where pull_requests.pr_num = first_requested_review.pr_num\n                and pull_requests.pr_num = approvals.pr_num\n                and first_requested_review_ts is not null and\n                created_at > now() - interval '1 year'\n        ) as foo\n) as bar\ngroup by time_to_first_approval;",
          refId: 'A',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
      ],
      title: 'Time to first approval',
      type: 'piechart',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            drawStyle: 'bars',
            fillOpacity: 100,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineWidth: 6,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 's',
        },
        overrides: [],
      },
      gridPos: {
        h: 9,
        w: 23,
        x: 0,
        y: 17,
      },
      id: 3,
      interval: '1d',
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: "select to_timestamp($__timeGroup(first_approval_ts, '$__interval')) as time_bucket,\n    percentile_cont(0.5) within group (\n        order by extract (\n            epoch from(greatest(interval '0s',first_approval_ts - created_at)) \n        ) \n    ) as median_time_to_first_review\n    from pull_requests, (\n        select pr_num, min(ts) as first_approval_ts\n        from pull_request_events\n        where event_type = 'PullRequestReview' and state = 'APPROVED'\n        group by pr_num\n    ) as first_approvals\n    where pull_requests.pr_num = first_approvals.pr_num\n    and first_approval_ts is not null and\n    created_at > now() - interval '1 year'\n    group by time_bucket;",
          refId: 'A',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
      ],
      title: 'Median time to approve',
      type: 'timeseries',
    },
  ],
  refresh: false,
  schemaVersion: 37,
  style: 'dark',
  tags: [],
  templating: {
    list: [],
  },
  time: {
    from: 'now-90d',
    to: 'now',
  },
  timepicker: {},
  timezone: '',
  title: 'Github PR Status',
  uid: 'github-pr0',
  weekStart: '',
}

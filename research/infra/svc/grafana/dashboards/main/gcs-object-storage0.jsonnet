{
  annotations: {
    list: [
      {
        builtIn: 1,
        datasource: {
          type: 'grafana',
          uid: '-- <PERSON><PERSON> --',
        },
        enable: true,
        hide: true,
        iconColor: 'rgba(0, 211, 255, 1)',
        name: 'Annotations & Alerts',
        type: 'dashboard',
      },
    ],
  },
  description: '',
  editable: true,
  fiscalYearStartMonth: 0,
  graphTooltip: 0,
  links: [
    {
      asDropdown: false,
      icon: 'external link',
      includeVars: false,
      keepTime: false,
      tags: [],
      targetBlank: true,
      title: 'GCP Metrics Explorer',
      tooltip: '',
      type: 'link',
      url: 'https://console.cloud.google.com/monitoring/metrics-explorer',
    },
    {
      asDropdown: false,
      icon: 'external link',
      includeVars: false,
      keepTime: false,
      tags: [],
      targetBlank: true,
      title: 'GCS Buckets',
      tooltip: '',
      type: 'link',
      url: 'https://console.cloud.google.com/storage/browser',
    },
  ],
  panels: [
    {
      collapsed: false,
      gridPos: {
        h: 1,
        w: 24,
        x: 0,
        y: 0,
      },
      id: 8,
      panels: [],
      title: 'GCS Storage Usage',
      type: 'row',
    },
    {
      datasource: {
        type: 'stackdriver',
        uid: 'gcp',
      },
      description: 'These metrics are only updated by GCS daily.',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'smooth',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'never',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'sishort',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 1,
      },
      id: 2,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          aliasBy: '',
          datasource: {
            type: 'stackdriver',
            uid: 'gcp',
          },
          promQLQuery: {
            expr: 'sum without(__name__,monitored_resource,storage_class,type)(storage_googleapis_com:storage_v2_total_count{monitored_resource="gcs_bucket"})',
            projectName: 'augment-research-gsc',
            step: '1h',
          },
          queryType: 'promQL',
          refId: 'A',
          timeSeriesList: {
            alignmentPeriod: 'cloud-monitoring-auto',
            crossSeriesReducer: 'REDUCE_NONE',
            filters: [
              'metric.type',
              '=',
              'storage.googleapis.com/storage/object_count',
            ],
            groupBys: [
              'resource.label.bucket_name',
              'metric.label.storage_class',
              'resource.type',
              'metadata.system_labels.instance_group',
            ],
            perSeriesAligner: 'ALIGN_MEAN',
            preprocessor: 'none',
            projectName: 'augment-research-gsc',
            view: 'FULL',
          },
        },
      ],
      title: 'Objects By Bucket',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'stackdriver',
        uid: 'gcp',
      },
      description: 'These metrics are only updated by GCS daily.',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'smooth',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'never',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'bytes',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 9,
      },
      id: 1,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          aliasBy: '',
          datasource: {
            type: 'stackdriver',
            uid: 'gcp',
          },
          promQLQuery: {
            expr: 'sum without(__name__,monitored_resource,storage_class,type)(storage_googleapis_com:storage_v2_total_bytes{monitored_resource="gcs_bucket"})',
            projectName: 'augment-research-gsc',
            step: '1h',
          },
          queryType: 'promQL',
          refId: 'A',
          timeSeriesList: {
            alignmentPeriod: 'cloud-monitoring-auto',
            crossSeriesReducer: 'REDUCE_NONE',
            filters: [
              'metric.type',
              '=',
              'storage.googleapis.com/storage/object_count',
            ],
            groupBys: [
              'resource.label.bucket_name',
              'metric.label.storage_class',
              'resource.type',
              'metadata.system_labels.instance_group',
            ],
            perSeriesAligner: 'ALIGN_MEAN',
            preprocessor: 'none',
            projectName: 'augment-research-gsc',
            view: 'FULL',
          },
        },
      ],
      title: 'Bytes By Bucket',
      type: 'timeseries',
    },
    {
      collapsed: false,
      gridPos: {
        h: 1,
        w: 24,
        x: 0,
        y: 17,
      },
      id: 9,
      panels: [],
      title: 'GCS Network Usage',
      type: 'row',
    },
    {
      datasource: {
        type: 'stackdriver',
        uid: 'gcp',
      },
      description: '',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'smooth',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'never',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'binBps',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 18,
      },
      id: 3,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          aliasBy: '',
          datasource: {
            type: 'stackdriver',
            uid: 'gcp',
          },
          promQLQuery: {
            expr: 'sum without(__name__,monitored_resource,method,response_code)(rate(storage_googleapis_com:network_sent_bytes_count{monitored_resource="gcs_bucket"}[$__interval]))',
            projectName: 'augment-research-gsc',
            step: '$min_step',
          },
          queryType: 'promQL',
          refId: 'A',
          timeSeriesList: {
            alignmentPeriod: 'cloud-monitoring-auto',
            crossSeriesReducer: 'REDUCE_NONE',
            filters: [
              'metric.type',
              '=',
              'storage.googleapis.com/storage/object_count',
            ],
            groupBys: [
              'resource.label.bucket_name',
              'metric.label.storage_class',
              'resource.type',
              'metadata.system_labels.instance_group',
            ],
            perSeriesAligner: 'ALIGN_MEAN',
            preprocessor: 'none',
            projectName: 'augment-research-gsc',
            view: 'FULL',
          },
        },
      ],
      title: 'Tx Bytes/second',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'stackdriver',
        uid: 'gcp',
      },
      description: '',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'smooth',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'never',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'binBps',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 26,
      },
      id: 4,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          aliasBy: '',
          datasource: {
            type: 'stackdriver',
            uid: 'gcp',
          },
          promQLQuery: {
            expr: 'sum without(__name__,monitored_resource,method,response_code)(rate(storage_googleapis_com:network_received_bytes_count{monitored_resource="gcs_bucket"}[$__interval]))',
            projectName: 'augment-research-gsc',
            step: '$min_step',
          },
          queryType: 'promQL',
          refId: 'A',
          timeSeriesList: {
            alignmentPeriod: 'cloud-monitoring-auto',
            crossSeriesReducer: 'REDUCE_NONE',
            filters: [
              'metric.type',
              '=',
              'storage.googleapis.com/storage/object_count',
            ],
            groupBys: [
              'resource.label.bucket_name',
              'metric.label.storage_class',
              'resource.type',
              'metadata.system_labels.instance_group',
            ],
            perSeriesAligner: 'ALIGN_MEAN',
            preprocessor: 'none',
            projectName: 'augment-research-gsc',
            view: 'FULL',
          },
        },
      ],
      title: 'Rx Bytes/second',
      type: 'timeseries',
    },
    {
      collapsed: false,
      gridPos: {
        h: 1,
        w: 24,
        x: 0,
        y: 34,
      },
      id: 10,
      panels: [],
      title: 'GCS API',
      type: 'row',
    },
    {
      datasource: {
        type: 'stackdriver',
        uid: 'gcp',
      },
      description: '',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'smooth',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'never',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'reqps',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 35,
      },
      id: 5,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          aliasBy: '',
          datasource: {
            type: 'stackdriver',
            uid: 'gcp',
          },
          promQLQuery: {
            expr: 'sum without(monitored_resource,response_code,method)(rate(storage_googleapis_com:api_request_count{monitored_resource="gcs_bucket",response_code="OK"}[$__interval]))',
            projectName: 'augment-research-gsc',
            step: '$min_step',
          },
          queryType: 'promQL',
          refId: 'A',
          timeSeriesList: {
            alignmentPeriod: 'cloud-monitoring-auto',
            crossSeriesReducer: 'REDUCE_NONE',
            filters: [
              'metric.type',
              '=',
              'storage.googleapis.com/storage/object_count',
            ],
            groupBys: [
              'resource.label.bucket_name',
              'metric.label.storage_class',
              'resource.type',
              'metadata.system_labels.instance_group',
            ],
            perSeriesAligner: 'ALIGN_MEAN',
            preprocessor: 'none',
            projectName: 'augment-research-gsc',
            view: 'FULL',
          },
        },
      ],
      title: 'API (response_code="OK")',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'stackdriver',
        uid: 'gcp',
      },
      description: '',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'smooth',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'never',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'reqps',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 43,
      },
      id: 6,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          aliasBy: '',
          datasource: {
            type: 'stackdriver',
            uid: 'gcp',
          },
          promQLQuery: {
            expr: 'sum without(monitored_resource,method)(rate(storage_googleapis_com:api_request_count{monitored_resource="gcs_bucket",response_code!="OK"}[$__interval]))',
            projectName: 'augment-research-gsc',
            step: '$min_step',
          },
          queryType: 'promQL',
          refId: 'A',
          timeSeriesList: {
            alignmentPeriod: 'cloud-monitoring-auto',
            crossSeriesReducer: 'REDUCE_NONE',
            filters: [
              'metric.type',
              '=',
              'storage.googleapis.com/storage/object_count',
            ],
            groupBys: [
              'resource.label.bucket_name',
              'metric.label.storage_class',
              'resource.type',
              'metadata.system_labels.instance_group',
            ],
            perSeriesAligner: 'ALIGN_MEAN',
            preprocessor: 'none',
            projectName: 'augment-research-gsc',
            view: 'FULL',
          },
        },
      ],
      title: 'API (response_code!="OK" errors)',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'stackdriver',
        uid: 'gcp',
      },
      description: '',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'smooth',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'never',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'reqps',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 51,
      },
      id: 7,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          aliasBy: '',
          datasource: {
            type: 'stackdriver',
            uid: 'gcp',
          },
          promQLQuery: {
            expr: '(sum without(monitored_resource,method,response_code)(rate(storage_googleapis_com:api_request_count{monitored_resource="gcs_bucket",response_code!="OK"}[$__interval])))\n/\n(sum without(monitored_resource,method,response_code)(rate(storage_googleapis_com:api_request_count{monitored_resource="gcs_bucket"}[$__interval])))',
            projectName: 'augment-research-gsc',
            step: '$min_step',
          },
          queryType: 'promQL',
          refId: 'A',
          timeSeriesList: {
            alignmentPeriod: 'cloud-monitoring-auto',
            crossSeriesReducer: 'REDUCE_NONE',
            filters: [
              'metric.type',
              '=',
              'storage.googleapis.com/storage/object_count',
            ],
            groupBys: [
              'resource.label.bucket_name',
              'metric.label.storage_class',
              'resource.type',
              'metadata.system_labels.instance_group',
            ],
            perSeriesAligner: 'ALIGN_MEAN',
            preprocessor: 'none',
            projectName: 'augment-research-gsc',
            view: 'FULL',
          },
        },
      ],
      title: 'API Error Rate',
      type: 'timeseries',
    },
  ],
  preload: false,
  schemaVersion: 40,
  tags: [],
  templating: {
    list: [
      {
        current: {
          text: '1h',
          value: '1h',
        },
        description: 'Increase min_step to decrease resolution when looking at longer timespans (to not exceed total maximum points).',
        label: 'Min Step',
        name: 'min_step',
        options: [
          {
            selected: false,
            text: '1m',
            value: '1m',
          },
          {
            selected: false,
            text: '5m',
            value: '5m',
          },
          {
            selected: false,
            text: '10m',
            value: '10m',
          },
          {
            selected: true,
            text: '1h',
            value: '1h',
          },
          {
            selected: false,
            text: '4h',
            value: '4h',
          },
          {
            selected: false,
            text: '12h',
            value: '12h',
          },
          {
            selected: false,
            text: '1d',
            value: '1d',
          },
        ],
        query: '1m, 5m, 10m, 1h, 4h, 12h, 1d',
        type: 'custom',
      },
    ],
  },
  time: {
    from: 'now-7d',
    to: 'now',
  },
  timepicker: {},
  timezone: 'browser',
  title: 'GCS Object Storage',
  uid: 'gcs-object-storage0',
  weekStart: '',
}

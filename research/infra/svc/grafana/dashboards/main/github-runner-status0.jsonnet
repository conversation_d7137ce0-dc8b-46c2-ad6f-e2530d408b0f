{
  annotations: {
    list: [
      {
        builtIn: 1,
        datasource: {
          type: 'grafana',
          uid: '-- <PERSON><PERSON> --',
        },
        enable: true,
        hide: true,
        iconColor: 'rgba(0, 211, 255, 1)',
        name: 'Annotations & Alerts',
        target: {
          limit: 100,
          matchAny: false,
          tags: [],
          type: 'dashboard',
        },
        type: 'dashboard',
      },
    ],
  },
  editable: true,
  fiscalYearStartMonth: 0,
  graphTooltip: 0,
  links: [
    {
      asDropdown: false,
      icon: 'external link',
      includeVars: false,
      keepTime: false,
      tags: [],
      targetBlank: true,
      title: 'GitHub Runners',
      tooltip: '',
      type: 'link',
      url: 'https://github.com/augmentcode/augment/settings/actions/runners',
    },
    {
      asDropdown: false,
      icon: 'external link',
      includeVars: false,
      keepTime: false,
      tags: [],
      targetBlank: true,
      title: 'Config',
      tooltip: '',
      type: 'link',
      url: 'https://github.com/augmentcode/augment/tree/main/research/environments/containers/github',
    },
  ],
  panels: [
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 1,
            pointSize: 1,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
            ],
          },
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 0,
      },
      id: 5,
      interval: '10m',
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'time_series',
          rawQuery: true,
          rawSql: 'SELECT $__time(timestamp_column), total_count, labels FROM idle_github_runners WHERE $__timeFilter(timestamp_column) ORDER BY timestamp_column',
          refId: 'A',
          sql: {
            columns: [
              {
                parameters: [
                  {
                    name: 'labels',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'idle_count',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'total_count',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'timestamp_column',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
          table: 'idle_github_runners',
        },
      ],
      title: 'GH Runner Count',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      description: 'A rolling average of runner utilization. This panel is hardcoded to `30m` and is the basis of an alert. The panel below is similar but the rolling window is configurable.',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 1,
            pointSize: 1,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'area',
            },
          },
          mappings: [],
          max: 100,
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 90,
              },
            ],
          },
          unit: 'percent',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 8,
      },
      id: 2,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'time_series',
          rawQuery: true,
          rawSql: "SELECT\n  $__time(timestamp_column),\n  labels,\n  AVG((1 - idle_count::numeric/total_count)*100.0) OVER (PARTITION BY labels ORDER BY timestamp_column RANGE BETWEEN '30m' PRECEDING AND CURRENT ROW) as utilization_30m\nFROM idle_github_runners\nWHERE $__timeFilter(timestamp_column)\nORDER BY timestamp_column",
          refId: 'A',
          sql: {},
          table: 'idle_github_runners',
        },
      ],
      title: 'GH Runner Utilization (30m rolling average -- hardcoded)',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      description: 'A rolling average of runner utilization. A rolling_window of `0m` is equivalent to the instantaneous utilization at a single datapoint.',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 1,
            pointSize: 1,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'area',
            },
          },
          mappings: [],
          max: 100,
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 90,
              },
            ],
          },
          unit: 'percent',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 16,
      },
      id: 6,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'time_series',
          rawQuery: true,
          rawSql: "SELECT\n  $__time(timestamp_column),\n  labels,\n  AVG((1 - idle_count::numeric/total_count)*100.0) OVER (PARTITION BY labels ORDER BY timestamp_column RANGE BETWEEN '${rolling_window}' PRECEDING AND CURRENT ROW) as utilization_${rolling_window}\nFROM idle_github_runners\nWHERE $__timeFilter(timestamp_column)\nORDER BY timestamp_column",
          refId: 'A',
          sql: {},
          table: 'idle_github_runners',
        },
      ],
      title: 'GH Runner Utilization (${rolling_window} rolling average -- configurable)',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 1,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
        },
        overrides: [],
      },
      gridPos: {
        h: 6,
        w: 24,
        x: 0,
        y: 24,
      },
      id: 4,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: 'SELECT timestamp_column, name, queued_count FROM queued_github_workflows',
          refId: 'A',
          sql: {
            columns: [
              {
                parameters: [
                  {
                    name: 'timestamp_column',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'name',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'queued_count',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
          table: 'queued_github_workflows',
        },
      ],
      title: 'GH Workflow Queues',
      transformations: [
        {
          id: 'partitionByValues',
          options: {
            fields: [
              'name',
            ],
          },
        },
      ],
      type: 'timeseries',
    },
  ],
  preload: false,
  refresh: false,
  schemaVersion: 40,
  tags: [],
  templating: {
    list: [
      {
        current: {
          text: '0m',
          value: '0m',
        },
        description: 'The window size used to compute a rolling overage.',
        label: 'Average Rolling Window',
        name: 'rolling_window',
        options: [
          {
            selected: true,
            text: '0m',
            value: '0m',
          },
          {
            selected: false,
            text: '5m',
            value: '5m',
          },
          {
            selected: false,
            text: '30m',
            value: '30m',
          },
          {
            selected: false,
            text: '1h',
            value: '1h',
          },
          {
            selected: false,
            text: '6h',
            value: '6h',
          },
          {
            selected: false,
            text: '1d',
            value: '1d',
          },
        ],
        query: '0m, 5m, 30m, 1h, 6h, 1d',
        type: 'custom',
      },
    ],
  },
  time: {
    from: 'now-7d',
    to: 'now',
  },
  timepicker: {},
  timezone: '',
  title: 'GitHub Runner Status',
  uid: 'github-runner-status0',
  weekStart: '',
}

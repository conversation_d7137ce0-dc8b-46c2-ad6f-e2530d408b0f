// THIS FILE WAS AUTO-GENERATED BY export.sh.

function(cm_tmpl) {
  Alerts: cm_tmpl + {
    folder: 'Alerts',
    data: {
    },
  },

  'Eval Probers': cm_tmpl + {
    folder: 'Eval Probers',
    data: {
      'augment-qa-remote-prober.json': std.manifestJson(import 'eval-probers/augment-qa-remote-prober.jsonnet'),
      'cceval-remote-prober.json': std.manifest<PERSON>son(import 'eval-probers/cceval-remote-prober.jsonnet'),
      'hindsight-remote-prober.json': std.manifestJson(import 'eval-probers/hindsight-remote-prober.jsonnet'),
      'humaneval-instruct-remote-prober.json': std.manifestJson(import 'eval-probers/humaneval-instruct-remote-prober.jsonnet'),
    },
  },

  Historical: cm_tmpl + {
    folder: 'Historical',
    data: {
      'historical-gpu0.json': std.manifestJson(import 'historical/historical-gpu0.jsonnet'),
    },
  },

  Main: cm_tmpl + {
    folder: 'Main',
    data: {
      'disk-usage0.json': std.manifestJson(import 'main/disk-usage0.jsonnet'),
      'gcs-object-storage0.json': std.manifestJson(import 'main/gcs-object-storage0.jsonnet'),
      'github-pr0.json': std.manifestJson(import 'main/github-pr0.jsonnet'),
      'github-runner-status0.json': std.manifestJson(import 'main/github-runner-status0.jsonnet'),
      'github-scraping0.json': std.manifestJson(import 'main/github-scraping0.jsonnet'),
      'gpu-utilization0.json': std.manifestJson(import 'main/gpu-utilization0.jsonnet'),
      'hydra-pod-creation.json': std.manifestJson(import 'main/hydra-pod-creation.jsonnet'),
      'k8s-pod-and-cluster.json': std.manifestJson(import 'main/k8s-pod-and-cluster.jsonnet'),
      'pod-cpu0.json': std.manifestJson(import 'main/pod-cpu0.jsonnet'),
      'research-ci0.json': std.manifestJson(import 'main/research-ci0.jsonnet'),
      'spark0.json': std.manifestJson(import 'main/spark0.jsonnet'),
    },
  },

}

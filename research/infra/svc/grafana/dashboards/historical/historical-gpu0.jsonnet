{
  annotations: {
    list: [
      {
        builtIn: 1,
        datasource: {
          type: 'grafana',
          uid: '-- <PERSON>ana --',
        },
        enable: true,
        hide: true,
        iconColor: 'rgba(0, 211, 255, 1)',
        name: 'Annotations & Alerts',
        target: {
          limit: 100,
          matchAny: false,
          tags: [],
          type: 'dashboard',
        },
        type: 'dashboard',
      },
    ],
  },
  editable: true,
  fiscalYearStartMonth: 0,
  graphTooltip: 0,
  links: [],
  liveNow: false,
  panels: [
    {
      datasource: {
        type: 'prometheus',
        uid: 'coreweave',
      },
      description: 'Utilization of reserved GPUs',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: 'GPU Count',
            axisPlacement: 'auto',
            axisSoftMin: 0,
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 16,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'dashed',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'green',
                value: 128,
              },
            ],
          },
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 0,
      },
      id: 2,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'coreweave',
          },
          editorMode: 'builder',
          expr: 'sum by(label_gpu_nvidia_com_model) (billing_gpu{label_gpu_nvidia_com_model=~"A100.*|H100.*"})',
          legendFormat: '{{label_gpu_nvidia_com_model}}',
          range: true,
          refId: 'A',
        },
      ],
      title: 'A100/H100 GPU Usage',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'prometheus',
        uid: 'coreweave',
      },
      description: 'Utilization of reserved GPUs',
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: 'GPU Count',
            axisPlacement: 'auto',
            axisSoftMin: 0,
            barAlignment: 0,
            drawStyle: 'line',
            fillOpacity: 16,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            lineInterpolation: 'linear',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'dashed',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'green',
                value: 128,
              },
            ],
          },
        },
        overrides: [
          {
            __systemRef: 'hideSeriesFrom',
            matcher: {
              id: 'byNames',
              options: {
                mode: 'exclude',
                names: [
                  'RTX_A5000',
                ],
                prefix: 'All except:',
                readOnly: true,
              },
            },
            properties: [
              {
                id: 'custom.hideFrom',
                value: {
                  legend: false,
                  tooltip: false,
                  viz: true,
                },
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 8,
      },
      id: 3,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'right',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      targets: [
        {
          datasource: {
            type: 'prometheus',
            uid: 'coreweave',
          },
          editorMode: 'builder',
          expr: 'sum by(label_gpu_nvidia_com_model) (billing_gpu{label_gpu_nvidia_com_model=~"RTX_A.*"})',
          legendFormat: '{{label_gpu_nvidia_com_model}}',
          range: true,
          refId: 'A',
        },
      ],
      title: 'All GPU Usage',
      type: 'timeseries',
    },
  ],
  schemaVersion: 37,
  style: 'dark',
  tags: [],
  templating: {
    list: [],
  },
  time: {
    from: 'now-1y',
    to: 'now',
  },
  timepicker: {},
  timezone: '',
  title: 'Historical GPU',
  uid: 'historical-gpu0',
  weekStart: '',
}

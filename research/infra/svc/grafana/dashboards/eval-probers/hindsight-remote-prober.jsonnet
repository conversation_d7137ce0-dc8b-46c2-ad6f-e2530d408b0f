{
  annotations: {
    list: [
      {
        builtIn: 1,
        datasource: {
          type: 'grafana',
          uid: '-- <PERSON>ana --',
        },
        enable: true,
        hide: true,
        iconColor: 'rgba(0, 211, 255, 1)',
        name: 'Annotations & Alerts',
        type: 'dashboard',
      },
    ],
  },
  description: 'Hindsight Remote Prober Results',
  editable: true,
  fiscalYearStartMonth: 0,
  graphTooltip: 0,
  links: [
    {
      asDropdown: false,
      icon: 'external link',
      includeVars: false,
      keepTime: false,
      tags: [],
      targetBlank: true,
      title: 'Raw Results',
      tooltip: '',
      type: 'link',
      url: 'https://webserver.gcp-us1.r.augmentcode.com/eval-probers/hindsight-remote/',
    },
  ],
  panels: [
    {
      datasource: {
        type: 'grafana-postgresql-datasource',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Eval Results (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
          ],
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
            ],
          },
        },
        overrides: [],
      },
      gridPos: {
        h: 10,
        w: 24,
        x: 0,
        y: 0,
      },
      id: 13,
      maxPerRow: 2,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      repeat: 'metrics',
      repeatDirection: 'v',
      targets: [
        {
          datasource: {
            type: 'grafana-postgresql-datasource',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: "SELECT\n  $__time(start_time),\n  ((value::jsonb)['${metrics}']['avg'])::real AS \"${metrics}\",\n  \"key\",\n  url\nFROM hindsight_prober, json_each(metrics['${group_by_key}']::json)\nWHERE $__timeFilter(start_time) AND key IN (${group_by_vals})\nORDER BY start_time ASC;",
          refId: 'HindsightDuration',
          sql: {},
        },
      ],
      title: 'Hindsight :: ${metrics} (by ${group_by_key})',
      transformations: [
        {
          id: 'partitionByValues',
          options: {
            fields: [
              'key',
            ],
            keepFields: true,
          },
        },
      ],
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'grafana-postgresql-datasource',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Eval Results (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
          ],
          mappings: [],
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
            ],
          },
          unit: 'm',
        },
        overrides: [],
      },
      gridPos: {
        h: 9,
        w: 24,
        x: 0,
        y: 20,
      },
      id: 3,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'grafana-postgresql-datasource',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: 'SELECT $__time(start_time), "duration", "url" FROM hindsight_prober WHERE $__timeFilter(start_time) ORDER BY start_time ASC;',
          refId: 'HindsightDuration',
          sql: {},
        },
      ],
      title: 'Hindsight :: Duration (m)',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'grafana-postgresql-datasource',
        uid: 'devex_metrics',
      },
      description: "The data age at each point is forward looking. It's the age that the datapoint will eventually be just before the next probe. The most recent point is calculated based on NOW().\n\nThe threshold is set based on the frequency of the probe (the cron schedule).",
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'area',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Eval Results (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
          ],
          mappings: [],
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 12,
              },
            ],
          },
          unit: 'h',
        },
        overrides: [],
      },
      gridPos: {
        h: 9,
        w: 24,
        x: 0,
        y: 29,
      },
      id: 14,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'grafana-postgresql-datasource',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: 'SELECT $__time(start_time), EXTRACT(EPOCH FROM (lag(start_time, 1, NOW()) OVER (ORDER BY start_time DESC)) - start_time)::real / 3600 AS "data_age", url FROM hindsight_prober WHERE $__timeFilter(start_time) ORDER BY start_time DESC;',
          refId: 'HindsightRemoteProberDataAge',
          sql: {},
        },
      ],
      title: 'Hindsight :: Data Age',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'thresholds',
          },
          custom: {
            align: 'auto',
            cellOptions: {
              type: 'auto',
            },
            inspect: false,
          },
          links: [],
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
            ],
          },
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: 'duration',
            },
            properties: [
              {
                id: 'unit',
                value: 'm',
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'filename',
            },
            properties: [
              {
                id: 'custom.hidden',
                value: true,
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'dirname',
            },
            properties: [
              {
                id: 'custom.hidden',
                value: true,
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'url',
            },
            properties: [
              {
                id: 'links',
                value: [
                  {
                    targetBlank: true,
                    title: '',
                    url: '${__data.fields["url"]}',
                  },
                ],
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 22,
        w: 24,
        x: 0,
        y: 38,
      },
      id: 12,
      options: {
        cellHeight: 'sm',
        footer: {
          countRows: false,
          fields: '',
          reducer: [
            'sum',
          ],
          show: false,
        },
        showHeader: true,
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: 'SELECT * FROM hindsight_prober WHERE $__timeFilter(start_time) ORDER BY start_time DESC',
          refId: 'FullTable',
          sql: {},
          table: 'hindsight_prober',
        },
      ],
      title: 'Hindsight :: Raw Results Table',
      type: 'table',
    },
  ],
  preload: false,
  refresh: '',
  schemaVersion: 40,
  tags: [],
  templating: {
    list: [
      {
        current: {
          text: 'lang',
          value: 'lang',
        },
        datasource: {
          type: 'grafana-postgresql-datasource',
          uid: 'devex_metrics',
        },
        definition: 'SELECT jsonb_object_keys(metrics) AS "group_by_key" FROM hindsight_prober WHERE $__timeFilter(start_time) GROUP BY 1',
        description: '',
        label: 'Group By',
        name: 'group_by_key',
        options: [],
        query: 'SELECT jsonb_object_keys(metrics) AS "group_by_key" FROM hindsight_prober WHERE $__timeFilter(start_time) GROUP BY 1',
        refresh: 2,
        regex: '',
        type: 'query',
      },
      {
        current: {
          text: [
            'All',
          ],
          value: [
            '$__all',
          ],
        },
        datasource: {
          type: 'grafana-postgresql-datasource',
          uid: 'devex_metrics',
        },
        definition: "SELECT jsonb_object_keys(metrics['${group_by_key}']) AS \"group_by_val\" FROM hindsight_prober WHERE $__timeFilter(start_time) GROUP BY 1",
        includeAll: true,
        label: 'IN',
        multi: true,
        name: 'group_by_vals',
        options: [],
        query: "SELECT jsonb_object_keys(metrics['${group_by_key}']) AS \"group_by_val\" FROM hindsight_prober WHERE $__timeFilter(start_time) GROUP BY 1",
        refresh: 1,
        regex: '',
        type: 'query',
      },
      {
        current: {
          text: [
            'exact_match',
            'token_accuracy',
          ],
          value: [
            'exact_match',
            'token_accuracy',
          ],
        },
        datasource: {
          type: 'grafana-postgresql-datasource',
          uid: 'devex_metrics',
        },
        definition: "SELECT json_object_keys(value) AS \"metrics\" FROM hindsight_prober, json_each(metrics['${group_by_key}']::json) WHERE $__timeFilter(start_time) GROUP BY 1;",
        includeAll: true,
        multi: true,
        name: 'metrics',
        options: [],
        query: "SELECT json_object_keys(value) AS \"metrics\" FROM hindsight_prober, json_each(metrics['${group_by_key}']::json) WHERE $__timeFilter(start_time) GROUP BY 1;",
        refresh: 1,
        regex: '',
        type: 'query',
      },
    ],
  },
  time: {
    from: 'now-30d',
    to: 'now',
  },
  timepicker: {},
  timezone: 'browser',
  title: 'Hindsight Remote Prober',
  uid: 'hindsight-remote-prober',
  weekStart: '',
}

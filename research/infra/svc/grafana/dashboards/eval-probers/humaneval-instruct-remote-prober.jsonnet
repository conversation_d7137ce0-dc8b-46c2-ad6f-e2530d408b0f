{
  annotations: {
    list: [
      {
        builtIn: 1,
        datasource: {
          type: 'grafana',
          uid: '-- <PERSON>ana --',
        },
        enable: true,
        hide: true,
        iconColor: 'rgba(0, 211, 255, 1)',
        name: 'Annotations & Alerts',
        type: 'dashboard',
      },
    ],
  },
  description: 'HumanEval Instruct Remote Prober Results',
  editable: true,
  fiscalYearStartMonth: 0,
  graphTooltip: 0,
  links: [
    {
      asDropdown: false,
      icon: 'external link',
      includeVars: false,
      keepTime: false,
      tags: [],
      targetBlank: true,
      title: 'Raw Results',
      tooltip: '',
      type: 'link',
      url: 'https://webserver.gcp-us1.r.augmentcode.com/eval-probers/humaneval-instruct-remote/',
    },
  ],
  panels: [
    {
      datasource: {
        type: 'grafana-postgresql-datasource',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'area',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Eval Results (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
          ],
          mappings: [],
          max: 1,
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'red',
                value: null,
              },
              {
                color: 'green',
                value: 0.9,
              },
            ],
          },
          unit: 'percentunit',
        },
        overrides: [],
      },
      gridPos: {
        h: 9,
        w: 24,
        x: 0,
        y: 0,
      },
      id: 1,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'grafana-postgresql-datasource',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: 'SELECT $__time(start_time), "pass@1", url FROM humaneval_instruct_prober WHERE $__timeFilter(start_time) ORDER BY start_time ASC;',
          refId: 'HumanEvalInstructPass@1',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
      ],
      title: 'HumanEval Instruct :: Pass@1',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'grafana-postgresql-datasource',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Eval Results (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
          ],
          mappings: [],
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
            ],
          },
          unit: 'short',
        },
        overrides: [],
      },
      gridPos: {
        h: 9,
        w: 24,
        x: 0,
        y: 9,
      },
      id: 2,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'grafana-postgresql-datasource',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: 'SELECT $__time(start_time), "samples", url FROM humaneval_instruct_prober WHERE $__timeFilter(start_time) ORDER BY start_time ASC;',
          refId: 'HumanEvalInstructSamples',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
      ],
      title: 'HumanEval Instruct :: Samples',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'grafana-postgresql-datasource',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Eval Results (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
          ],
          mappings: [],
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
            ],
          },
          unit: 'm',
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: 'rate',
            },
            properties: [
              {
                id: 'custom.axisPlacement',
                value: 'right',
              },
              {
                id: 'unit',
                value: 'reqpm',
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 9,
        w: 24,
        x: 0,
        y: 18,
      },
      id: 3,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'grafana-postgresql-datasource',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: 'SELECT $__time(start_time), "duration", "samples"/"duration" AS "rate", "url" FROM humaneval_instruct_prober WHERE $__timeFilter(start_time) ORDER BY start_time ASC;',
          refId: 'HumanEvalInstructPass@1',
          sql: {
            columns: [
              {
                parameters: [],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
          },
        },
      ],
      title: 'HumanEval Instruct :: Duration (m) / Rate (ops/m)',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'grafana-postgresql-datasource',
        uid: 'devex_metrics',
      },
      description: "The data age at each point is forward looking. It's the age that the datapoint will eventually be just before the next probe. The most recent point is calculated based on NOW().\n\nThe threshold is set based on the frequency of the probe (the cron schedule).",
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'area',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Eval Results (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
          ],
          mappings: [],
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 24,
              },
            ],
          },
          unit: 'h',
        },
        overrides: [],
      },
      gridPos: {
        h: 9,
        w: 24,
        x: 0,
        y: 27,
      },
      id: 13,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'grafana-postgresql-datasource',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: 'SELECT $__time(start_time), EXTRACT(EPOCH FROM (lag(start_time, 1, NOW()) OVER (ORDER BY start_time DESC)) - start_time)::real / 3600 AS "data_age", url FROM humaneval_instruct_prober WHERE $__timeFilter(start_time) ORDER BY start_time DESC;',
          refId: 'HumanEvalInstructRemoteProberDataAge',
          sql: {},
        },
      ],
      title: 'HumanEval Instruct :: Data Age',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'thresholds',
          },
          custom: {
            align: 'auto',
            cellOptions: {
              type: 'auto',
            },
            inspect: false,
          },
          links: [],
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
            ],
          },
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: 'duration',
            },
            properties: [
              {
                id: 'unit',
                value: 'm',
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'filename',
            },
            properties: [
              {
                id: 'custom.hidden',
                value: true,
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'dirname',
            },
            properties: [
              {
                id: 'custom.hidden',
                value: true,
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'url',
            },
            properties: [
              {
                id: 'links',
                value: [
                  {
                    targetBlank: true,
                    title: '',
                    url: '${__data.fields["url"]}',
                  },
                ],
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 22,
        w: 24,
        x: 0,
        y: 36,
      },
      id: 12,
      options: {
        cellHeight: 'sm',
        footer: {
          countRows: false,
          fields: '',
          reducer: [
            'sum',
          ],
          show: false,
        },
        showHeader: true,
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: 'SELECT * FROM humaneval_instruct_prober WHERE $__timeFilter(start_time) ORDER BY start_time DESC',
          refId: 'FullTable',
          sql: {},
          table: 'humaneval_instruct_prober',
        },
      ],
      title: 'HumanEval Instruct :: Raw Results Table',
      type: 'table',
    },
  ],
  preload: false,
  refresh: '',
  schemaVersion: 40,
  tags: [],
  templating: {
    list: [],
  },
  time: {
    from: 'now-30d',
    to: 'now',
  },
  timepicker: {},
  timezone: 'browser',
  title: 'HumanEval Instruct Remote Prober',
  uid: 'humaneval-instruct-remote-prober',
  weekStart: '',
}

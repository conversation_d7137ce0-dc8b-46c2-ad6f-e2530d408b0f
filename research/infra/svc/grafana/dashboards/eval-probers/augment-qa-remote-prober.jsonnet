{
  annotations: {
    list: [
      {
        builtIn: 1,
        datasource: {
          type: 'grafana',
          uid: '-- <PERSON>ana --',
        },
        enable: true,
        hide: true,
        iconColor: 'rgba(0, 211, 255, 1)',
        name: 'Annotations & Alerts',
        target: {
          limit: 100,
          matchAny: false,
          tags: [],
          type: 'dashboard',
        },
        type: 'dashboard',
      },
    ],
  },
  description: 'Augment QA Remote Prober Results',
  editable: true,
  fiscalYearStartMonth: 0,
  graphTooltip: 0,
  links: [
    {
      asDropdown: false,
      icon: 'external link',
      includeVars: false,
      keepTime: false,
      tags: [],
      targetBlank: true,
      title: 'Raw Results',
      tooltip: '',
      type: 'link',
      url: 'https://webserver.gcp-us1.r.augmentcode.com/eval-probers/augment-qa-remote/',
    },
  ],
  panels: [
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'smooth',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 1,
            pointSize: 3,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'always',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Raw Eval Data (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
            {
              targetBlank: true,
              title: 'HTML Report',
              url: '${__data.fields["html_report_url"]}',
            },
          ],
          mappings: [],
          max: 1,
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'percentunit',
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: 'answer_keyword_recall',
            },
            properties: [
              {
                id: 'custom.lineWidth',
                value: 3,
              },
              {
                id: 'custom.showPoints',
                value: 'auto',
              },
              {
                id: 'custom.lineStyle',
                value: {
                  dash: [
                    10,
                    10,
                  ],
                  fill: 'dash',
                },
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 0,
      },
      id: 2,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'builder',
          format: 'table',
          rawSql: 'SELECT start_time, answer_keyword_recall, html_report_url, url FROM augment_qa_prober ORDER BY start_time DESC ',
          refId: 'Answer Keyword Recall',
          sql: {
            columns: [
              {
                parameters: [
                  {
                    name: 'start_time',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'answer_keyword_recall',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'url',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            orderBy: {
              property: {
                name: [
                  'start_time',
                ],
                type: 'string',
              },
              type: 'property',
            },
            orderByDirection: 'DESC',
            whereJsonTree: {
              children1: [
                {
                  id: '899a8a98-0123-4456-b89a-b18fb6057399',
                  properties: {
                    field: 'filename',
                    operator: 'not_like',
                    value: [
                      'adhoc',
                    ],
                    valueSrc: [
                      'value',
                    ],
                    valueType: [
                      'text',
                    ],
                  },
                  type: 'rule',
                },
              ],
              id: 'a8b999b9-89ab-4cde-b012-318fb1b40e3a',
              type: 'group',
            },
            whereString: "filename NOT LIKE '%adhoc%'",
          },
          table: 'augment_qa_prober',
        },
      ],
      title: 'Augment QA :: Answer Keyword Recall',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'smooth',
            lineWidth: 1,
            pointSize: 3,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Raw Eval Data (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
            {
              targetBlank: true,
              title: 'HTML Report',
              url: '${__data.fields["html_report_url"]}',
            },
          ],
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'percentunit',
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: 'retrievals_keyword_recall',
            },
            properties: [
              {
                id: 'custom.lineWidth',
                value: 3,
              },
              {
                id: 'custom.showPoints',
                value: 'auto',
              },
              {
                id: 'custom.lineStyle',
                value: {
                  dash: [
                    10,
                    10,
                  ],
                  fill: 'dash',
                },
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 8,
      },
      id: 6,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'builder',
          format: 'table',
          rawSql: "SELECT start_time, retrievals_keyword_recall, html_report_url, url FROM augment_qa_prober WHERE filename NOT LIKE '%adhoc%' ORDER BY start_time DESC ",
          refId: 'retrievals_keyword_recall',
          sql: {
            columns: [
              {
                parameters: [
                  {
                    name: 'start_time',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'retrievals_keyword_recall',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'url',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            orderBy: {
              property: {
                name: [
                  'start_time',
                ],
                type: 'string',
              },
              type: 'property',
            },
            orderByDirection: 'DESC',
            whereJsonTree: {
              children1: [
                {
                  id: '9ab8888a-89ab-4cde-b012-318fb604a48a',
                  properties: {
                    field: 'filename',
                    operator: 'not_like',
                    value: [
                      'adhoc',
                    ],
                    valueSrc: [
                      'value',
                    ],
                    valueType: [
                      'text',
                    ],
                  },
                  type: 'rule',
                },
              ],
              id: 'a8b999b9-89ab-4cde-b012-318fb1b40e3a',
              type: 'group',
            },
            whereString: "filename NOT LIKE '%adhoc%'",
          },
          table: 'augment_qa_prober',
        },
      ],
      title: 'Augment QA :: Retrievals Keyword Recall',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'smooth',
            lineWidth: 1,
            pointSize: 3,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Raw Eval Data (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
            {
              targetBlank: true,
              title: 'HTML Report',
              url: '${__data.fields["html_report_url"]}',
            },
          ],
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'percentunit',
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: 'gold_paths_recall',
            },
            properties: [
              {
                id: 'custom.lineWidth',
                value: 3,
              },
              {
                id: 'custom.showPoints',
                value: 'auto',
              },
              {
                id: 'custom.lineStyle',
                value: {
                  dash: [
                    10,
                    10,
                  ],
                  fill: 'dash',
                },
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 16,
      },
      id: 10,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'builder',
          format: 'table',
          rawSql: "SELECT start_time, gold_paths_recall, html_report_url, url FROM augment_qa_prober WHERE filename NOT LIKE '%adhoc%' ORDER BY start_time DESC ",
          refId: 'gold_paths_recall',
          sql: {
            columns: [
              {
                parameters: [
                  {
                    name: 'start_time',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'gold_paths_recall',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'url',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            orderBy: {
              property: {
                name: [
                  'start_time',
                ],
                type: 'string',
              },
              type: 'property',
            },
            orderByDirection: 'DESC',
            whereJsonTree: {
              children1: [
                {
                  id: '9ab8888a-89ab-4cde-b012-318fb604a48a',
                  properties: {
                    field: 'filename',
                    operator: 'not_like',
                    value: [
                      'adhoc',
                    ],
                    valueSrc: [
                      'value',
                    ],
                    valueType: [
                      'text',
                    ],
                  },
                  type: 'rule',
                },
              ],
              id: 'a8b999b9-89ab-4cde-b012-318fb1b40e3a',
              type: 'group',
            },
            whereString: "filename NOT LIKE '%adhoc%'",
          },
          table: 'augment_qa_prober',
        },
      ],
      title: 'Augment QA :: Gold Paths Recall',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'smooth',
            lineWidth: 1,
            pointSize: 3,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Raw Eval Data (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
            {
              targetBlank: true,
              title: 'HTML Report',
              url: '${__data.fields["html_report_url"]}',
            },
          ],
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'percentunit',
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: 'gold_paths_mrr',
            },
            properties: [
              {
                id: 'custom.lineWidth',
                value: 3,
              },
              {
                id: 'custom.showPoints',
                value: 'auto',
              },
              {
                id: 'custom.lineStyle',
                value: {
                  dash: [
                    10,
                    10,
                  ],
                  fill: 'dash',
                },
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 24,
      },
      id: 11,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'builder',
          format: 'table',
          rawSql: "SELECT start_time, gold_paths_mrr, html_report_url, url FROM augment_qa_prober WHERE filename NOT LIKE '%adhoc%' ORDER BY start_time DESC ",
          refId: 'gold_paths_mrr',
          sql: {
            columns: [
              {
                parameters: [
                  {
                    name: 'start_time',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'gold_paths_mrr',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'url',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            orderBy: {
              property: {
                name: [
                  'start_time',
                ],
                type: 'string',
              },
              type: 'property',
            },
            orderByDirection: 'DESC',
            whereJsonTree: {
              children1: [
                {
                  id: '9ab8888a-89ab-4cde-b012-318fb604a48a',
                  properties: {
                    field: 'filename',
                    operator: 'not_like',
                    value: [
                      'adhoc',
                    ],
                    valueSrc: [
                      'value',
                    ],
                    valueType: [
                      'text',
                    ],
                  },
                  type: 'rule',
                },
              ],
              id: 'a8b999b9-89ab-4cde-b012-318fb1b40e3a',
              type: 'group',
            },
            whereString: "filename NOT LIKE '%adhoc%'",
          },
          table: 'augment_qa_prober',
        },
      ],
      title: 'Augment QA :: Gold Paths MRR',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 25,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'normal',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Raw Eval Data (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
            {
              targetBlank: true,
              title: 'HTML Report',
              url: '${__data.fields["html_report_url"]}',
            },
          ],
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: 'samples',
            },
            properties: [
              {
                id: 'custom.stacking',
                value: {
                  group: 'A',
                  mode: 'none',
                },
              },
              {
                id: 'custom.lineStyle',
                value: {
                  dash: [
                    10,
                    10,
                  ],
                  fill: 'dash',
                },
              },
              {
                id: 'custom.lineWidth',
                value: 3,
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'limit',
            },
            properties: [
              {
                id: 'custom.stacking',
                value: {
                  group: 'A',
                  mode: 'none',
                },
              },
              {
                id: 'custom.lineStyle',
                value: {
                  dash: [
                    10,
                    20,
                  ],
                  fill: 'dash',
                },
              },
              {
                id: 'custom.lineWidth',
                value: 3,
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 32,
      },
      id: 7,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'builder',
          format: 'table',
          rawQuery: true,
          rawSql: "SELECT start_time, \"limit\", samples, html_report_url, url FROM augment_qa_prober WHERE filename NOT LIKE '%adhoc%' ",
          refId: 'samples',
          sql: {
            columns: [
              {
                parameters: [
                  {
                    name: 'start_time',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: '"limit"',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'samples',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'url',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            orderBy: {
              property: {
                type: 'string',
              },
              type: 'property',
            },
            whereJsonTree: {
              children1: [
                {
                  id: '89aabba9-cdef-4012-b456-718fb6042990',
                  properties: {
                    field: 'filename',
                    operator: 'not_like',
                    value: [
                      'adhoc',
                    ],
                    valueSrc: [
                      'value',
                    ],
                    valueType: [
                      'text',
                    ],
                  },
                  type: 'rule',
                },
              ],
              id: 'a8b999b9-89ab-4cde-b012-318fb1b40e3a',
              type: 'group',
            },
            whereString: "filename NOT LIKE '%adhoc%'",
          },
          table: 'augment_qa_prober',
        },
      ],
      title: 'Augment QA :: Samples',
      transformations: [
        {
          id: 'organize',
          options: {},
        },
      ],
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'continuous-GrYlRd',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            axisSoftMax: 3,
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 1,
            pointSize: 3,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'always',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Raw Eval Data (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
            {
              targetBlank: true,
              title: 'HTML Report',
              url: '${__data.fields["html_report_url"]}',
            },
          ],
          mappings: [],
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'm',
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: 'samples',
            },
            properties: [
              {
                id: 'custom.hideFrom',
                value: {
                  legend: true,
                  tooltip: true,
                  viz: true,
                },
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'rate',
            },
            properties: [
              {
                id: 'custom.axisPlacement',
                value: 'right',
              },
              {
                id: 'unit',
                value: 'opm',
              },
              {
                id: 'color',
                value: {
                  mode: 'continuous-RdYlGr',
                },
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 8,
        w: 24,
        x: 0,
        y: 40,
      },
      id: 9,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'builder',
          format: 'table',
          rawSql: "SELECT start_time, duration, url, samples FROM augment_qa_prober WHERE filename NOT LIKE '%adhoc%' ORDER BY start_time DESC ",
          refId: 'duration',
          sql: {
            columns: [
              {
                parameters: [
                  {
                    name: 'start_time',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'duration',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'url',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'samples',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            orderBy: {
              property: {
                name: [
                  'start_time',
                ],
                type: 'string',
              },
              type: 'property',
            },
            orderByDirection: 'DESC',
            whereJsonTree: {
              children1: [
                {
                  id: '899a8a98-0123-4456-b89a-b18fb6057399',
                  properties: {
                    field: 'filename',
                    operator: 'not_like',
                    value: [
                      'adhoc',
                    ],
                    valueSrc: [
                      'value',
                    ],
                    valueType: [
                      'text',
                    ],
                  },
                  type: 'rule',
                },
              ],
              id: 'a8b999b9-89ab-4cde-b012-318fb1b40e3a',
              type: 'group',
            },
            whereString: "filename NOT LIKE '%adhoc%'",
          },
          table: 'augment_qa_prober',
        },
      ],
      title: 'Augment QA :: Duration (m) / Rate (ops/m)',
      transformations: [
        {
          id: 'calculateField',
          options: {
            alias: 'rate',
            binary: {
              left: 'samples',
              operator: '/',
              reducer: 'sum',
              right: 'duration',
            },
            mode: 'binary',
            reduce: {
              include: [],
              reducer: 'sum',
            },
            replaceFields: false,
          },
        },
        {
          id: 'organize',
          options: {
            excludeByName: {
              total_samples: false,
            },
            indexByName: {},
            renameByName: {
              total_samples: '',
            },
          },
        },
      ],
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'grafana-postgresql-datasource',
        uid: 'devex_metrics',
      },
      description: "The data age at each point is forward looking. It's the age that the datapoint will eventually be just before the next probe. The most recent point is calculated based on NOW().\n\nThe threshold is set based on the frequency of the probe (the cron schedule).",
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'area',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Eval Results (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
          ],
          mappings: [],
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 24,
              },
            ],
          },
          unit: 'h',
        },
        overrides: [],
      },
      gridPos: {
        h: 9,
        w: 24,
        x: 0,
        y: 48,
      },
      id: 13,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'grafana-postgresql-datasource',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: 'SELECT $__time(start_time), EXTRACT(EPOCH FROM (lag(start_time, 1, NOW()) OVER (ORDER BY start_time DESC)) - start_time)::real / 3600 AS "data_age", url FROM augment_qa_prober WHERE $__timeFilter(start_time) ORDER BY start_time DESC;',
          refId: 'AugmentQARemoteProberDataAge',
          sql: {},
        },
      ],
      title: 'Augment QA :: Data Age',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'thresholds',
          },
          custom: {
            align: 'auto',
            cellOptions: {
              type: 'auto',
            },
            inspect: false,
          },
          links: [],
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
            ],
          },
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: 'duration',
            },
            properties: [
              {
                id: 'unit',
                value: 'm',
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'filename',
            },
            properties: [
              {
                id: 'custom.hidden',
                value: true,
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'dirname',
            },
            properties: [
              {
                id: 'custom.hidden',
                value: true,
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'url',
            },
            properties: [
              {
                id: 'links',
                value: [
                  {
                    targetBlank: true,
                    title: '',
                    url: '${__data.fields["url"]}',
                  },
                ],
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'html_report_url',
            },
            properties: [
              {
                id: 'links',
                value: [
                  {
                    targetBlank: true,
                    title: '',
                    url: '${__data.fields["html_report_url"]}',
                  },
                ],
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 22,
        w: 24,
        x: 0,
        y: 57,
      },
      id: 12,
      options: {
        cellHeight: 'sm',
        footer: {
          countRows: false,
          fields: '',
          reducer: [
            'sum',
          ],
          show: false,
        },
        showHeader: true,
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: 'SELECT * FROM augment_qa_prober WHERE $__timeFilter(start_time) ORDER BY start_time DESC',
          refId: 'FullTable',
          sql: {},
          table: 'augment_qa_prober',
        },
      ],
      title: 'Augment QA :: Raw Results Table',
      type: 'table',
    },
  ],
  preload: false,
  refresh: '30m',
  schemaVersion: 40,
  tags: [],
  templating: {
    list: [],
  },
  time: {
    from: 'now-21d',
    to: 'now',
  },
  timepicker: {},
  timezone: '',
  title: 'Augment QA Remote Prober',
  uid: 'augment-qa-remote-prober',
  weekStart: '',
}

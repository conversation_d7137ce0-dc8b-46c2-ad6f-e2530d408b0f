{
  annotations: {
    list: [
      {
        builtIn: 1,
        datasource: {
          type: 'grafana',
          uid: '-- <PERSON>ana --',
        },
        enable: true,
        hide: true,
        iconColor: 'rgba(0, 211, 255, 1)',
        name: 'Annotations & Alerts',
        target: {
          limit: 100,
          matchAny: false,
          tags: [],
          type: 'dashboard',
        },
        type: 'dashboard',
      },
    ],
  },
  description: 'CCEval Remote Prober Results',
  editable: true,
  fiscalYearStartMonth: 0,
  graphTooltip: 0,
  links: [
    {
      asDropdown: false,
      icon: 'external link',
      includeVars: false,
      keepTime: false,
      tags: [],
      targetBlank: true,
      title: 'Raw Results',
      tooltip: '',
      type: 'link',
      url: 'https://webserver.gcp-us1.r.augmentcode.com/eval-probers/cceval-remote/',
    },
  ],
  panels: [
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'smooth',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 1,
            pointSize: 3,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'always',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Raw Results (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
          ],
          mappings: [],
          max: 1,
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'percentunit',
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: 'total_exact_match',
            },
            properties: [
              {
                id: 'custom.lineWidth',
                value: 3,
              },
              {
                id: 'custom.showPoints',
                value: 'never',
              },
              {
                id: 'custom.lineStyle',
                value: {
                  dash: [
                    10,
                    10,
                  ],
                  fill: 'dash',
                },
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 8,
        w: 18,
        x: 0,
        y: 0,
      },
      id: 2,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'builder',
          format: 'table',
          rawSql: "SELECT start_time, total_exact_match, java_exact_match, python_exact_match, csharp_exact_match, typescript_exact_match, url FROM cceval_prober WHERE filename NOT LIKE '%adhoc%' ORDER BY start_time DESC ",
          refId: 'Exact Match',
          sql: {
            columns: [
              {
                parameters: [
                  {
                    name: 'start_time',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'total_exact_match',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'java_exact_match',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'python_exact_match',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'csharp_exact_match',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'typescript_exact_match',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'url',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            orderBy: {
              property: {
                name: [
                  'start_time',
                ],
                type: 'string',
              },
              type: 'property',
            },
            orderByDirection: 'DESC',
            whereJsonTree: {
              children1: [
                {
                  id: '899a8a98-0123-4456-b89a-b18fb6057399',
                  properties: {
                    field: 'filename',
                    operator: 'not_like',
                    value: [
                      'adhoc',
                    ],
                    valueSrc: [
                      'value',
                    ],
                    valueType: [
                      'text',
                    ],
                  },
                  type: 'rule',
                },
              ],
              id: 'a8b999b9-89ab-4cde-b012-318fb1b40e3a',
              type: 'group',
            },
            whereString: "filename NOT LIKE '%adhoc%'",
          },
          table: 'cceval_prober',
        },
      ],
      title: 'Exact Match',
      type: 'timeseries',
    },
    {
      fieldConfig: {
        defaults: {},
        overrides: [],
      },
      gridPos: {
        h: 24,
        w: 4,
        x: 18,
        y: 0,
      },
      id: 5,
      options: {
        code: {
          language: 'plaintext',
          showLineNumbers: false,
          showMiniMap: false,
        },
        content: '# README\n\nData points on the first panel are clickable with links to raw results / logs / etc on the [Research Webserver](https://webserver.gcp-us1.r.augmentcode.com/eval-probers/cceval-remote/).\n\nhttps://www.notion.so/CCEval-Dogfood-Prober-6b09a7da10344fb1b043c88cd16115e9\n\nWe can graph any field in the CCEvalResult message.\n\nTODO: Label results with the model/version.',
        mode: 'markdown',
      },
      pluginVersion: '11.4.0',
      title: 'README',
      type: 'text',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'smooth',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'percentunit',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 18,
        x: 0,
        y: 8,
      },
      id: 3,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: "SELECT start_time, total_exact_match - total_exact_match_strict AS \"total\", java_exact_match - java_exact_match_strict AS \"java\", python_exact_match - python_exact_match_strict AS \"python\", csharp_exact_match - csharp_exact_match_strict AS \"csharp\", typescript_exact_match - typescript_exact_match_strict AS \"typescript\", url FROM cceval_prober WHERE filename NOT LIKE '%adhoc%' ORDER BY start_time DESC ",
          refId: 'Exact Match (strict)',
          sql: {
            columns: [
              {
                parameters: [
                  {
                    name: 'start_time',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'total_exact_match_strict',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'java_exact_match_strict',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'python_exact_match_strict',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'csharp_exact_match_strict',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'typescript_exact_match_strict',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            orderBy: {
              property: {
                name: [
                  'start_time',
                ],
                type: 'string',
              },
              type: 'property',
            },
            orderByDirection: 'DESC',
            whereJsonTree: {
              children1: [
                {
                  id: '8b99ab88-4567-489a-bcde-f18fb605163b',
                  properties: {
                    field: 'filename',
                    operator: 'not_like',
                    value: [
                      'adhoc',
                    ],
                    valueSrc: [
                      'value',
                    ],
                    valueType: [
                      'text',
                    ],
                  },
                  type: 'rule',
                },
              ],
              id: 'a8b999b9-89ab-4cde-b012-318fb1b40e3a',
              type: 'group',
            },
            whereString: "filename NOT LIKE '%adhoc%'",
          },
          table: 'cceval_prober',
        },
      ],
      title: '"Exact Match" - "Exact Match Strict" = ...',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'smooth',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'percentunit',
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 18,
        x: 0,
        y: 16,
      },
      id: 6,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'builder',
          format: 'table',
          rawSql: "SELECT start_time, total_edit_similarity, java_edit_similarity, python_edit_similarity, csharp_edit_similarity, typescript_edit_similarity FROM cceval_prober WHERE filename NOT LIKE '%adhoc%' ORDER BY start_time DESC ",
          refId: 'edit_similarity',
          sql: {
            columns: [
              {
                parameters: [
                  {
                    name: 'start_time',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'total_edit_similarity',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'java_edit_similarity',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'python_edit_similarity',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'csharp_edit_similarity',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'typescript_edit_similarity',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            orderBy: {
              property: {
                name: [
                  'start_time',
                ],
                type: 'string',
              },
              type: 'property',
            },
            orderByDirection: 'DESC',
            whereJsonTree: {
              children1: [
                {
                  id: '9ab8888a-89ab-4cde-b012-318fb604a48a',
                  properties: {
                    field: 'filename',
                    operator: 'not_like',
                    value: [
                      'adhoc',
                    ],
                    valueSrc: [
                      'value',
                    ],
                    valueType: [
                      'text',
                    ],
                  },
                  type: 'rule',
                },
              ],
              id: 'a8b999b9-89ab-4cde-b012-318fb1b40e3a',
              type: 'group',
            },
            whereString: "filename NOT LIKE '%adhoc%'",
          },
          table: 'cceval_prober',
        },
      ],
      title: 'Edit Similarity',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 25,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'normal',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Raw Eval Data (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
          ],
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: 'total_samples',
            },
            properties: [
              {
                id: 'custom.stacking',
                value: {
                  group: 'A',
                  mode: 'none',
                },
              },
              {
                id: 'custom.lineStyle',
                value: {
                  dash: [
                    10,
                    10,
                  ],
                  fill: 'dash',
                },
              },
              {
                id: 'custom.lineWidth',
                value: 3,
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'limit',
            },
            properties: [
              {
                id: 'custom.stacking',
                value: {
                  group: 'A',
                  mode: 'none',
                },
              },
              {
                id: 'custom.lineStyle',
                value: {
                  dash: [
                    10,
                    20,
                  ],
                  fill: 'dash',
                },
              },
              {
                id: 'custom.lineWidth',
                value: 3,
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 8,
        w: 18,
        x: 0,
        y: 24,
      },
      id: 7,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'builder',
          format: 'table',
          rawQuery: true,
          rawSql: "SELECT start_time, \"limit\", total_samples, java_samples, python_samples, csharp_samples, typescript_samples, url FROM cceval_prober WHERE filename NOT LIKE '%adhoc%' ",
          refId: 'samples',
          sql: {
            columns: [
              {
                parameters: [
                  {
                    name: 'start_time',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: '"limit"',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'total_samples',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'java_samples',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'python_samples',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'csharp_samples',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'typescript_samples',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'url',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            orderBy: {
              property: {
                type: 'string',
              },
              type: 'property',
            },
            whereJsonTree: {
              children1: [
                {
                  id: '89aabba9-cdef-4012-b456-718fb6042990',
                  properties: {
                    field: 'filename',
                    operator: 'not_like',
                    value: [
                      'adhoc',
                    ],
                    valueSrc: [
                      'value',
                    ],
                    valueType: [
                      'text',
                    ],
                  },
                  type: 'rule',
                },
              ],
              id: 'a8b999b9-89ab-4cde-b012-318fb1b40e3a',
              type: 'group',
            },
            whereString: "filename NOT LIKE '%adhoc%'",
          },
          table: 'cceval_prober',
        },
      ],
      title: 'Samples',
      transformations: [
        {
          id: 'organize',
          options: {},
        },
      ],
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
          },
          links: [],
          mappings: [],
        },
        overrides: [],
      },
      gridPos: {
        h: 8,
        w: 4,
        x: 18,
        y: 24,
      },
      id: 11,
      options: {
        displayLabels: [
          'name',
          'percent',
          'value',
        ],
        legend: {
          displayMode: 'list',
          placement: 'bottom',
          showLegend: false,
          values: [],
        },
        pieType: 'pie',
        reduceOptions: {
          calcs: [
            'lastNotNull',
          ],
          fields: '',
          values: false,
        },
        tooltip: {
          mode: 'multi',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'builder',
          format: 'table',
          rawSql: "SELECT start_time, java_samples AS \"java\", python_samples AS \"python\", csharp_samples AS \"c#\", typescript_samples AS \"typescript\", url FROM cceval_prober WHERE filename NOT LIKE '%adhoc%' LIMIT 50 ",
          refId: 'A',
          sql: {
            columns: [
              {
                parameters: [
                  {
                    name: 'start_time',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                alias: '"java"',
                parameters: [
                  {
                    name: 'java_samples',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                alias: '"python"',
                parameters: [
                  {
                    name: 'python_samples',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                alias: '"c#"',
                parameters: [
                  {
                    name: 'csharp_samples',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                alias: '"typescript"',
                parameters: [
                  {
                    name: 'typescript_samples',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'url',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            limit: 50,
            whereJsonTree: {
              children1: [
                {
                  id: '9b9999ab-0123-4456-b89a-b18fc2e1c1cc',
                  properties: {
                    field: 'filename',
                    operator: 'not_like',
                    value: [
                      'adhoc',
                    ],
                    valueSrc: [
                      'value',
                    ],
                    valueType: [
                      'text',
                    ],
                  },
                  type: 'rule',
                },
              ],
              id: 'b9b8b8a8-89ab-4cde-b012-318fc2dfd6e2',
              type: 'group',
            },
            whereString: "filename NOT LIKE '%adhoc%'",
          },
          table: 'cceval_prober',
        },
      ],
      title: 'Samples',
      transparent: true,
      type: 'piechart',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'continuous-GrYlRd',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            axisSoftMax: 3,
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineStyle: {
              fill: 'solid',
            },
            lineWidth: 1,
            pointSize: 3,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'always',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'off',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Raw Eval Data (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
          ],
          mappings: [],
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 80,
              },
            ],
          },
          unit: 'm',
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: 'total_samples',
            },
            properties: [
              {
                id: 'custom.hideFrom',
                value: {
                  legend: true,
                  tooltip: true,
                  viz: true,
                },
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'rate',
            },
            properties: [
              {
                id: 'custom.axisPlacement',
                value: 'right',
              },
              {
                id: 'unit',
                value: 'opm',
              },
              {
                id: 'color',
                value: {
                  mode: 'continuous-RdYlGr',
                },
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 8,
        w: 18,
        x: 0,
        y: 32,
      },
      id: 9,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'builder',
          format: 'table',
          rawSql: "SELECT start_time, duration, url, total_samples FROM cceval_prober WHERE filename NOT LIKE '%adhoc%' ORDER BY start_time DESC ",
          refId: 'duration',
          sql: {
            columns: [
              {
                parameters: [
                  {
                    name: 'start_time',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'duration',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'url',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
              {
                parameters: [
                  {
                    name: 'total_samples',
                    type: 'functionParameter',
                  },
                ],
                type: 'function',
              },
            ],
            groupBy: [
              {
                property: {
                  type: 'string',
                },
                type: 'groupBy',
              },
            ],
            orderBy: {
              property: {
                name: [
                  'start_time',
                ],
                type: 'string',
              },
              type: 'property',
            },
            orderByDirection: 'DESC',
            whereJsonTree: {
              children1: [
                {
                  id: '899a8a98-0123-4456-b89a-b18fb6057399',
                  properties: {
                    field: 'filename',
                    operator: 'not_like',
                    value: [
                      'adhoc',
                    ],
                    valueSrc: [
                      'value',
                    ],
                    valueType: [
                      'text',
                    ],
                  },
                  type: 'rule',
                },
              ],
              id: 'a8b999b9-89ab-4cde-b012-318fb1b40e3a',
              type: 'group',
            },
            whereString: "filename NOT LIKE '%adhoc%'",
          },
          table: 'cceval_prober',
        },
      ],
      title: 'Duration (m) / Rate (ops/m)',
      transformations: [
        {
          id: 'calculateField',
          options: {
            alias: 'rate',
            binary: {
              left: 'total_samples',
              operator: '/',
              reducer: 'sum',
              right: 'duration',
            },
            mode: 'binary',
            reduce: {
              include: [],
              reducer: 'sum',
            },
            replaceFields: false,
          },
        },
        {
          id: 'organize',
          options: {
            excludeByName: {
              total_samples: false,
            },
            indexByName: {},
            renameByName: {
              total_samples: '',
            },
          },
        },
      ],
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'grafana-postgresql-datasource',
        uid: 'devex_metrics',
      },
      description: "The data age at each point is forward looking. It's the age that the datapoint will eventually be just before the next probe. The most recent point is calculated based on NOW().\n\nThe threshold is set based on the frequency of the probe (the cron schedule).",
      fieldConfig: {
        defaults: {
          color: {
            mode: 'palette-classic',
          },
          custom: {
            axisBorderShow: false,
            axisCenteredZero: false,
            axisColorMode: 'text',
            axisLabel: '',
            axisPlacement: 'auto',
            barAlignment: 0,
            barWidthFactor: 0.6,
            drawStyle: 'line',
            fillOpacity: 0,
            gradientMode: 'none',
            hideFrom: {
              legend: false,
              tooltip: false,
              viz: false,
            },
            insertNulls: false,
            lineInterpolation: 'linear',
            lineWidth: 1,
            pointSize: 5,
            scaleDistribution: {
              type: 'linear',
            },
            showPoints: 'auto',
            spanNulls: false,
            stacking: {
              group: 'A',
              mode: 'none',
            },
            thresholdsStyle: {
              mode: 'area',
            },
          },
          links: [
            {
              targetBlank: true,
              title: 'Eval Results (Research Webserver)',
              url: '${__data.fields["url"]}',
            },
          ],
          mappings: [],
          min: 0,
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
              {
                color: 'red',
                value: 6,
              },
            ],
          },
          unit: 'h',
        },
        overrides: [],
      },
      gridPos: {
        h: 9,
        w: 24,
        x: 0,
        y: 40,
      },
      id: 13,
      options: {
        legend: {
          calcs: [],
          displayMode: 'list',
          placement: 'bottom',
          showLegend: true,
        },
        tooltip: {
          mode: 'single',
          sort: 'none',
        },
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'grafana-postgresql-datasource',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: 'SELECT $__time(start_time), EXTRACT(EPOCH FROM (lag(start_time, 1, NOW()) OVER (ORDER BY start_time DESC)) - start_time)::real / 3600 AS "data_age", url FROM cceval_prober WHERE $__timeFilter(start_time) ORDER BY start_time DESC;',
          refId: 'CCEvalRemoteProberDataAge',
          sql: {},
        },
      ],
      title: 'CCEval :: Data Age',
      type: 'timeseries',
    },
    {
      datasource: {
        type: 'postgres',
        uid: 'devex_metrics',
      },
      fieldConfig: {
        defaults: {
          color: {
            mode: 'thresholds',
          },
          custom: {
            align: 'auto',
            cellOptions: {
              type: 'auto',
            },
            inspect: false,
          },
          links: [],
          mappings: [],
          thresholds: {
            mode: 'absolute',
            steps: [
              {
                color: 'green',
                value: null,
              },
            ],
          },
        },
        overrides: [
          {
            matcher: {
              id: 'byName',
              options: 'duration',
            },
            properties: [
              {
                id: 'unit',
                value: 'm',
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'filename',
            },
            properties: [
              {
                id: 'custom.hidden',
                value: true,
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'dirname',
            },
            properties: [
              {
                id: 'custom.hidden',
                value: true,
              },
            ],
          },
          {
            matcher: {
              id: 'byName',
              options: 'url',
            },
            properties: [
              {
                id: 'links',
                value: [
                  {
                    targetBlank: true,
                    title: '',
                    url: '${__data.fields["url"]}',
                  },
                ],
              },
            ],
          },
        ],
      },
      gridPos: {
        h: 22,
        w: 24,
        x: 0,
        y: 49,
      },
      id: 12,
      options: {
        cellHeight: 'sm',
        footer: {
          countRows: false,
          fields: '',
          reducer: [
            'sum',
          ],
          show: false,
        },
        showHeader: true,
      },
      pluginVersion: '11.4.0',
      targets: [
        {
          datasource: {
            type: 'postgres',
            uid: 'devex_metrics',
          },
          editorMode: 'code',
          format: 'table',
          rawQuery: true,
          rawSql: 'SELECT * FROM cceval_prober WHERE $__timeFilter(start_time) ORDER BY start_time DESC',
          refId: 'FullTable',
          sql: {},
          table: 'cceval_prober',
        },
      ],
      title: 'Raw Results Table',
      type: 'table',
    },
  ],
  preload: false,
  refresh: '5m',
  schemaVersion: 40,
  tags: [],
  templating: {
    list: [],
  },
  time: {
    from: 'now-21d',
    to: 'now',
  },
  timepicker: {},
  timezone: '',
  title: 'CCEval Remote Prober',
  uid: 'cceval-remote-prober',
  weekStart: '',
}

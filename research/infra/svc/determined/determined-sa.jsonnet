local clusters = import '../../../../research/infra/cfg/clusters/clusters.jsonnet';

function(cluster_name, name='determined-service-account') local C = clusters.cluster(cluster_name); C.k8s + {

  local is_gcp = clusters.is_gcp(cluster_name),

  GCP+:: {
    Object+: {
      project_id:: C.gcp_project,
      namespace:: 'cc-' + C.name,
    },
  },

  // Create a GCP SA which will correspond to the Determined K8s SA.
  gcp_sa: if is_gcp then $.GCP.IAM.ServiceAccount + {
    name:: C.name + '-determined-sa',
  },

  k8s_sa: $.ServiceAccount + {
    name:: name,
    metadata+: {
      annotations+: if is_gcp then {
        // This annotation is the k8s-side of the GCP<>K8s binding.
        'iam.gke.io/gcp-service-account': $.gcp_sa.email,
      } else {},
    },
    imagePullSecrets: if is_gcp then [] else [{
      name: 'registry-image-pull-secret',
    }],
  },

  k8s_role: $.Role + {
    name:: 'determined-role',
    rules: [
      {
        apiGroups: [''],
        resources: ['pods', 'configmaps', 'events'],
        verbs: $.READ_VERBS + $.WRITE_VERBS,
      },
      {
        apiGroups: [''],
        resources: ['pods/log'],
        verbs: $.READ_VERBS,
      },
    ],
  },

  k8s_rolebinding: $.RoleBinding + {
    name:: 'determined-rolebinding',
    role_name:: $.k8s_role.name,
    sas:: [$.k8s_sa],
  },

  // The GCP side of the GCP<>K8s binding.
  gcp_workload_binding: if is_gcp then $.GCP.IAM.PolicyMember + {
    name:: $.gcp_sa.metadata.name + '-workload-identity-aug-cc-research',
    spec+: {
      resourceRef: $.gcp_sa.localKindRef,
      member: 'serviceAccount:%s.svc.id.goog[%s/%s]' % [C.gcp_project, $.k8s_sa.metadata.namespace, $.k8s_sa.metadata.name],
      role: 'roles/iam.workloadIdentityUser',
    },
  },

  // Finally, add GCP permissions...

  // Determined needs Object Storage (we apply `storage.admin` on the whole project).
  // TODO(mattm): Look into reducing to specific bucket(s).
  object_store_acl: if is_gcp then $.GCP.IAM.PolicyMember + {
    name:: $.gcp_sa.metadata.name + '.' + C.gcp_project + '.storage-admin',
    spec+: {
      resourceRef+: {
        kind: 'Project',
        external: 'project/' + C.gcp_project,
      },
      memberFrom: self.memberFromSA($.gcp_sa),
      role: 'roles/storage.admin',
    },
  },
}

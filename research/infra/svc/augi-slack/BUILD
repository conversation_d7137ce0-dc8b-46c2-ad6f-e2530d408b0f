load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library", "go_test")

package(default_visibility = ["//visibility:private"])

go_library(
    name = "augi-slack_lib",
    srcs = ["augi-slack.go"],
    importpath = "github.com/augmentcode/augment/research/infra/svc/augi-slack",
    deps = [
        "//infra/lib/logger",
        "//research/infra/lib/slack/augislack",
        "@com_github_spf13_pflag//:pflag",
    ],
)

go_test(
    name = "augi-slack_test",
    srcs = ["augi-slack_test.go"],
    embed = [":augi-slack_lib"],
)

go_binary(
    name = "augi-slack",
    embed = [":augi-slack_lib"],
)

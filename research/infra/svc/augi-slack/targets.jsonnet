local clusters = import '../../cfg/clusters/clusters.jsonnet';

function(
  cluster,
  name='augi-slack',
  namespace=null,
  version='latest',
) local C = clusters.cluster(cluster); C.k8s + {

  BaseLabels+:: std.prune({
    'aug.service': name,
    'aug.version': version,
  }),

  BaseObject+:: {
    name:: name,
  },

  Object+:: {
    namespace:: $.ns.metadata.name,
  },

  ns: $.Namespace + {
    name:: if namespace != null && namespace != '' then namespace else name,
  },

  sa: $.ServiceAccount + {
    name+:: '-target-sa',
  },

  sa_token: $.Secret + {
    i_know_what_im_doing: true,
    name:: $.sa.metadata.name + '-tok',
    type: 'kubernetes.io/service-account-token',
    metadata+: {
      annotations+: {
        'kubernetes.io/service-account.name': $.sa.metadata.name,
      },
    },
  },

  edit_rolebinding: $.RoleBinding + {
    namespace:: C.main_namespace,
    role_ref_kind:: 'ClusterRole',
    name+:: '-edit',
    role_name:: 'edit',
    sas:: [$.sa],
  },

  view_rolebinding: $.ClusterRoleBinding + {
    name+:: '-view',
    role_name:: 'view',
    sas:: [$.sa],
  },
}

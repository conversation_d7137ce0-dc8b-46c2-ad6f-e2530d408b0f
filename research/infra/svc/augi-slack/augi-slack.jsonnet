local clusters = import '../../cfg/clusters/clusters.jsonnet';

function(
  cluster='gcp-core0',
  name='augi-slack',
  namespace=null,
  version='latest',
  image=null,
  replicas=1,  // TODO(mattm): Bump to 3 after initial dev; slack socketmode works correctly with replicas.
  cpu='1',
  ram='2G',
) local C = clusters.cluster(cluster); C.k8s + {

  BaseLabels+:: std.prune({
    'aug.service': name,
    'aug.version': version,
  }),

  BaseObject+:: {
    name:: name,
  },

  Object+:: {
    namespace:: $.ns.metadata.name,
  },

  ns: $.Namespace + {
    name:: if namespace != null && namespace != '' then namespace else name,
  },

  tokens: $.SealedSecret + {
    botToken:: 'bot-token',
    appToken:: 'app-token',
    signingSec:: 'signing-secret',
    kubeconfigYaml:: 'kubeconfig.yaml',
    local s = self,
    encryptedData: {
      [s.botToken]: 'AgCA91zLCIlDG03I4cwB1BQnOnnYkTplarTGKpy0RwVQp9XO31UmuB047xfkmoISuB+DYZfQ7bPMQYNqSKwfds6eutnbvR2BVM8vjDcxwJeU/nftYxxzCE511LXaZivQCgrQKlao6A04YmqKjV4tonFvdFuB3U7nnRskjL9AZ313NwH06M9jnOWmudXWYEquvLDNJGi+gfb+xZskgXH52BA4OnGNXccUQvE09UqK1T8Htm070fFuHvfuNXbPXfy5Xq1sYRfC1BCuDpL2Tpf2dpkXspsG/U7GjOBg3RM2oATz5GVQP4AxbpmTB2Qat/+Jgq3wUZy5QtSMPlmhmeF973QoLlDSWN2sTT8lRKQq2RinHHBFC6MlhUrxDhu4bKRGHRpPoobHXwZiAD/VZUgL8YYnFnqBl2EvB0JDyWZUoImRugwTswXX9OpcmFk5PL0x23aV6RQhMfHlsNSVhdVAf/Z6ESpvoAKxhKFrnbkce4bBY1kQWpXG56R6nT7gR7jw4/HOJE2Jfr/MAh+ae+tOfNLd2Yu3BMZKmrqt5JRpbRySuYUPD3Zabz+ZZdRCg7PJznYZhUWukr5S7OqwD5GB02LfIHeEXkb0vD+b8r1+eVSh0QKEb3SYARdNNUQrHc+C3XdgrYK8IvmdRXhjIrVT8TmHbRZRNb5sR2EUfhYLsvgEYHxIEVxcMfSkYilXkFVK0ErWlgMuzJnBkfuLQl6+XYvc1/GzL+hxkyUVE+qThGW+VmSYqLyL29+HalH2MM/OF8/tqI9O5xMSd80=',
      [s.appToken]: 'AgAlvy7DtZlKgKDlvoYot4bxQTb50byary+9KI+uN623XNrTtXoFWBLpB6QFJ+1gd0WlfSSEEK2oUTQ15Fp6YC8Bxowb5RKaGvzpeWYOyiwe5JOLMvtgbJ/wvJ7my9yLyOgn6fkbymhCqWaaXF6lng4gQNJ5mcJE9k9GjAw16dyIDIIzb0KQc2hvwLAWHh6Yr03mL1xoL/2ASo3+dYhCbCx89qwoH3LxrNNakRhjNfB0zByBBCv3vtRJDWUmdQb3vlYifKLOJ1VC8KVWbe5sbH0/f4y4CnmT96G4nDk4nfD3EcYnhYwlJVeOS8APgTbGXS2BQjqA1DJs81mrJBE8affNs1OiqUkiJhoWR+njmgF1vgi4Ui1ZfDhFEu+u91eXun0FenQd6eArFGlOaRrRwvfXEJZdCyoa7T/486rSkMw+jsabKv6aa1DZ54tH+bU9RtTOw+86Trqk/qH+2ZEnPmLegvcvuf6Pmk53LEQx/1CAdIjgzztyE+ZrZIw0AXBlx1akiEFhi7jRXWq7c49VIQIZgvJOtS7hc/q8eRIv9BzODaa96ZfFhXh7j7pjk3b7fOc49GDPmo5wrh3BRMew6LJOZhtWbTYQ+IOHPawMY5FVufIPbljYg4xozIyp3BRHreybtNYWeFQXq8zLW3JFwYvb12361cNtzAsSar5sEVxmomaKqFBVm4Q3x0EzqGUh/43h8Q+dme8KOPV8ie8HQeyX+MEWuNWneOzDzZY9i3s8hcyfggY6ymGKyyeimQac7hpqScYdzzuQm7bfjZ++wF9HA0bMwYQVhSMj9tfNPLwqtnkaiXae6E4RIP0u0+lZBmlI',
      [s.signingSec]: 'AgCm5oDi+ttuv2Obncn1yNVDNwMXJ0rpeFdvmlCYPF6m8p6Zt54vIUyC9IlhmSCrFMy2b9eiXoByxpqxnZE9aF3VmT5BKcWIkKh+g2e+6s4STSKxk8Ne4hcRWGZ2S92zQ2/XPpX4QcpqLRaNGZs7/lk5bjdX8JfFvPUufXAWNcyAsNN2ex37JkcRYqkoaSGmvivNTjcxHxefFGWXblgqr3WrrIsWO+rGIsCtvvUAQdlpkNpxkR+460VGqiaYVO3zJ6EwFfX3IiK6nu8PYJx9iZORsJOViwi1gDzfSXTHD9amQG3wNXkYr15AjU9spgsPq7P/Jr/LienlEcbQLuY4qXLngr+0ly+REmR0wUsDBfyWS7a5VE/YHO6HqsHOx4DE56+4pOohFT+kc7HlPXWIhgOTDo2O9nP39zV6Is6OPqpyQ2Ma4qZNn6+Wi6LaLsGlg61tID++n0pNcL1rfai7Nf2lGanWU1zIEym8atlW+lcU7Bpyv24JbdTEJOaZFon7HqxO42omcBIUgKdzlmjmE7E3qJKfg1fjqMSkBUH2QlqnkSp4RVhLzHlikhS9muulPSLI/8OEkaccfD7Ubhtp06chXiDgasbDiaWdoYicbXbmfJi43DVnIeZJ6zq2DxfWR5L6jd1JUX+AtNAfmsbMd4BEF3lxR9VsCj9iaTVrF2zDrxLOTi891lk138gtQ7gbcf8rhiDjZXCsCxqFU0FYmEgBaVLNCW7IoHldYC++0z+A9g==',
      '.augi-slack-target-sa.token': 'AgBXISbVsd/QXY1ZdRvkamwsao2+j1f0KsKd/Pzah7CAkohgkDg3GA+F266Tp5HPpe+brYkdsWhfqrY+dKGp1Yv7C1ox7HRW9iV4TNE74n+nYtHfX2/GEsPipZF/uCv9zYbWGCJfPhT37EQ65hQt2uId6HgSJBM/e5mOn/3fAnEPs+OEzs/r5HWP0iwLQIwHl/3wfJhQk5sk3y+KaaFiyH1/KneT+fWGVMQiTQRNRA8V60F2QvZOV8YQwG0SXfsEPD4iEXxGUeU+rIc9Q33/mNzvwLwSf9umxQRq/cjQlKn0GUtd8M3DG2PdASWa1fKIphWNkkuL+EooG6oIEhmKplH0+aRGHlu4JKI47E7s7Xt10KUTw5J2K3i/GjS0fOook/78Ha6SdpDhiEblVTlYIntfxcUgGuLId5zQIF2C8CP8Rh+0Imi2nnth67i2UTS9oXmU4pVv4vbXeAZrXVhWbxwdyxlMph5UzIorWM68yVp2q5P/JEBHeWfMzIa9OlXmZRH5FTCeM7dXB+Tlj165jTOxohaRubg1WLhxUg5WD+pSbyYDzOYx5qx1bzdPC/YSLQxLAQdMthM0Sq22ZXWxYsV/Il9KO0u1dqG6zww4z8pCgiIQrMZjrent+njx1+78r/U6nj/v0dJNiu9LE5yCHGnAIJjvjIO+Zt6neVjliOI3Lj02vO8eovXIWSBuT4NMCNAEtBBJNDqgTo/39pgQMTiU7W8VY9Hf/XkQEunfxp6gmhiBV4aEX7zsRDzRzE5/2+4yqQC+Sbaa70IEdfkpbWZVYZspjcAkSa/xKqr0/m7yBukakYv31PaD4FSL5XnH1bwa7QzB68suxN0NREQwDc2z/Dbn1tfp+LvNyr3NIm3OOwsE1ObDjoHXfd1rSxDq86V4oReIaWr7sxmwYPyYDK/A5gut+nAHVyGku61ZBSjGPnJDdY5pLy9ij9rtaxDI3S5JYT5PQkXwfjgJ6KIf0v8we8Vosuhmu7d2dQzI/gDP6gkqJr4LGLyZbse87Ntv0drruqEQ9gMIi0UQYN3vLuWsCLVwfuaHUqGknePy64T+JIaaDs3tKltt1ZwaE4cCJxohnhwaQre3GHfiWbFVYSA50JK9Ls5G3ODUf1kluyOTQDyzcfnpDgrWoNXAUdfMJnAnYsLKmHxFyKK+WbHde1aZhwvNorqcQa37iIr5R7g2/pf8gFZN/O7xAHCA0wFeWJHZI1SeKfEDi5AJ0+pRtGXamVJOZZv72ZcdI+8orW22HXCDqC8s7zJDeXgv/Wmz/9o0Ax3kCOKtjOVqQjnp6/PcEAjQ0aQUgLFGEm3jVYVDEdfG46VCNbMQGaXy4MrFUFdqA4/PD8a4gJD64ecVs0ytTnccdEtYR6JsgxoUi66CNvVRHtCOzfoU8J8ezdRvy8cNSIyHBT0zJs7fMVkT/zmzF1F6SR0OecpbBul7pocyq47fsVm99cFFp/L08G+ZMoHdvth7CG+KACL2r4BNqKF6sAV5wtuBPa6mUkFZ84s8NuXf/ng3VnfstJj+N2648s8Jr2uy8QzAnI6hgZiy/3VGspb5LqxQdesOEX7ggPAlD5zUp3ZzsQquvvgxRJVfHM2tyMCEh97tpLfxO3FHvpyOKw8Z5qfkFRvfs2/9HWDzIM4Op4fTaQDc+XWxTFNP0KqchOpkrsWMuR3flCf6p2rVIEO6mdZDGPmdTRqS4+40ZJbA3Hziaf9dj6IOdl/Oso0u3RQfWDotpeC4LAxYoLuWq2Y5u3cQngVzIknOhjiZAbjj5SEelG05aWsTsblThItFfnrGrw9I5oUx2CTQ7GTnVAv68aVusmX0lnRWY3ebLE5f7w7uX3iCgTKgl3NEzEHsOTUVV8dwlvXG8waVQsWcRuEeILG6Zgn4SslLAJN8Jj2B29NCdZ/0PKUBm1wij2M0PNyRuH9yyvU1Z8M+YO2Q3eUCLY3L/1PJKv4KBkkhbsW/EM6n',
    },
    spec+: {
      template+: {
        data+: {
          [s.kubeconfigYaml]: |||
            apiVersion: v1
            kind: Config

            contexts:
              - name: gcp-us1
                context:
                  cluster: gcp-us1
                  user: augi-slack-target-sa

            clusters:
              - name: gcp-us1
                cluster:
                  server: https://35.238.125.24
                  certificate-authority-data: 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

            users:
              - name: augi-slack-target-sa
                user:
                  token: {{index . ".augi-slack-target-sa.token"}}
          |||,
        },
      },
    },
  },

  sa: $.ServiceAccount + {},

  deploy: $.Deployment + {
    spec+: {
      replicas: replicas,
      template+: {
        spec+: {
          local pod = self,
          serviceAccountName: $.sa.metadata.name,
          containers: [
            $.Container + {
              name: name,
              image: image,
              volumeMounts: pod.volmount_mounts,
              resources+: {
                limits+: {
                  cpu: cpu,
                  memory: ram,
                },
              },
              args: [
                '--bot-token=/run/secrets/' + $.tokens.metadata.name + '/' + $.tokens.botToken,
                '--app-token=/run/secrets/' + $.tokens.metadata.name + '/' + $.tokens.appToken,
                '--kubeconfig=/run/secrets/' + $.tokens.metadata.name + '/' + $.tokens.kubeconfigYaml,
              ],
            },
          ],
          volmounts:: [
            {
              name:: $.tokens.metadata.name,
              volume:: {
                secret: {
                  secretName: $.tokens.metadata.name,
                },
              },
              mount:: {
                mountPath: '/run/secrets/' + $.tokens.metadata.name,
                readOnly: true,
              },
            },
          ],
          tolerations+: [
            {
              effect: 'NoSchedule',
              key: 'r.augmentcode.com/pool-type',
              value: 'svc',
            },
            {
              effect: 'PreferNoSchedule',
              key: 'r.augmentcode.com/pool-type',
              value: 'svc',
            },
          ],
        },
      },
    },
  },
}

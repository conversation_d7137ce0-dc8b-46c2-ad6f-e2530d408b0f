NAME    := augi-slack
VERSION := $(shell date +"%Y-%m-%d_%H-%M-%S")
CLUSTER := gcp-core0
TARGETS := gcp-us1

REGISTRY := $(shell jsonnet -A name="$(CLUSTER)" -A prop=registry -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
IMAGE    := $(REGISTRY)/$(NAME):$(VERSION)

CONTEXT  := $(shell jsonnet -A name="$(CLUSTER)" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop")
K8S_CONF := $(NAME).jsonnet
KUBECTL  := kubectl --context="$(CONTEXT)"
KUBECFG  := kubecfg --context="$(CONTEXT)" \
	   --tla-code="cluster='$(CLUSTER)'" \
	   --tla-code="name='$(NAME)'" \
	   --tla-code="version='$(VERSION)'" \
	   --tla-code="image='$(IMAGE)'"

TGT_CONF    := targets.jsonnet
TGT_KUBECTL := kubectl
TGT_KUBECFG := kubecfg --tla-code="name='$(NAME)'" --tla-code="version='$(VERSION)'"

build:
	bazel build :$(NAME)
	docker build --pull --build-context=bin=$$(bazel info bazel-bin)/research/infra/svc/$(NAME)/$(NAME)_/ -t $(IMAGE) .

push: build
	docker push $(IMAGE)

show:
	$(KUBECFG) show $(K8S_CONF)
	for t in $(TARGETS); do \
		$(TGT_KUBECFG) --context="$$t" --tla-code="cluster='$$t'" show $(TGT_CONF); \
	done

diff:
	-$(KUBECFG) diff --diff-strategy=subset $(K8S_CONF)
	-for t in $(TARGETS); do \
		$(TGT_KUBECFG) --context="$$t" --tla-code="cluster='$$t'" diff --diff-strategy=subset $(TGT_CONF); \
	done

apply:
	$(KUBECFG) update $(K8S_CONF)
	for t in $(TARGETS); do \
		$(TGT_KUBECFG) --context="$$t" --tla-code="cluster='$$t'" update $(TGT_CONF); \
	done

logs:
	$(KUBECTL) logs -l aug.service=$(NAME) $(LOGS_EXTRA)

list:
	$(KUBECTL) get -Al aug.service=$(NAME) deploy,pod,svc,ing,pvc,clusterrole,clusterrolebinding,role,rolebinding,sa,cm,certificate,secret,sealedsecret
	for t in $(TARGETS); do \
		$(TGT_KUBECTL) --context="$$t" get -Al aug.service=$(NAME) deploy,pod,svc,ing,pvc,clusterrole,clusterrolebinding,role,rolebinding,sa,cm,certificate,secret,sealedsecret; \
	done

purge:
	$(KUBECTL) delete -Al aug.service=$(NAME) deploy,pod,svc,ing,pvc,clusterrole,clusterrolebinding,role,rolebinding,sa,cm,certificate,secret,sealedsecret

deploy: diff push apply

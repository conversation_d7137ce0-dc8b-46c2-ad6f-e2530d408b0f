load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library", "go_test")

package(default_visibility = ["//research/infra:internal"])

# gazelle:build_file_name BUILD,BUILD.bazel,BUILD.bzl

package_group(
    name = "internal",
    packages = ["//research/infra/..."],
)

go_binary(
    name = "augi",
    embed = [":augi_lib"],
    visibility = ["//visibility:public"],
)

go_library(
    name = "augi_lib",
    srcs = ["augi.go"],
    importpath = "github.com/augmentcode/augment/research/infra",
    visibility = ["//visibility:private"],
    deps = ["//research/infra/cli"],
)

go_test(
    name = "augi_test",
    srcs = ["augi_test.go"],
    embed = [":augi_lib"],
)

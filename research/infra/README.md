# Augment Research Infra

This directory contains a number of libraries and a single CLI -- `augi` -- for
Augment research infrastructure operations.

## Bootstrapping

For most users, `augi` will already be installed as `/usr/local/bin/augi`.

If not, to bootstrap with bazel (preferred):
```
$ bazel run //research/infra:augi -- install
```

or with go directly (if bazel is not available):
```
$ go run augi.go install
```

## Quick Start

 - **Bootstrap:** `bazel run //research/infra:augi -- install`
 - **Use:** `augi ...`
 - **Use from source:** `augi build -- ...`
 - **Reinstall from source:** `augi build -- install`

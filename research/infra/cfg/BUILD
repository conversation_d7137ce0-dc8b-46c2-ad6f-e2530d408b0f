load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(default_visibility = ["//research/infra:internal"])

go_library(
    name = "cfg",
    srcs = ["cfg.go"],
    embedsrcs = [
        "//research/infra/cfg/clusters:jsonnet_files",  # keep
        "//research/infra/cfg/clusters/base-resources:jsonnet_files",  # keep
        "//research/infra/cfg/lib:jsonnet_files",  # keep
    ],
    importpath = "github.com/augmentcode/augment/research/infra/cfg",
    deps = [
        "//infra/lib/k8s",
        "//research/environments:tags_go",
        "//research/infra/lib/augment",
        "@google_jsonnet_go//:go_default_library",
    ],
)

go_test(
    name = "cfg_test",
    srcs = ["cfg_test.go"],
    embed = [":cfg"],
)

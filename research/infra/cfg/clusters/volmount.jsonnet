{
  VolMount:: {
    local vm = self,

    name: error 'name is required',
    path:: error 'path is required',
    extra_paths:: [],

    pvc:: null,
    secret:: null,
    csi:: null,

    storage_type:: null,
    readonly:: null,

    volume: (
      if vm.secret != null then {
        secret: {
          secretName: vm.secret,
        },
      } else if vm.csi != null then {
        csi: vm.csi,
      } else if vm.pvc != null then {
        persistentVolumeClaim: {
          claimName: vm.pvc,
        },
      } else {
        persistentVolumeClaim: {
          claimName: vm.name,
        },
      }
    ),

    mounts: [
      std.prune({
        name: vm.name,
        mountPath: p,
        readOnly: vm.readonly,
      })
      for p in [vm.path] + vm.extra_paths
    ],

  },
}

package clusters

import (
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"github.com/spf13/pflag"

	"k8s.io/apimachinery/pkg/api/resource"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"
)

// NodeTLAFlagVars manages flags in a FlagSet, which can then be used to evaluate the jsonnet `node_from_args` function.
type NodeTLAFlagVars struct {
	flagset *pflag.FlagSet

	// Meta flags
	IgnoreValidations bool

	// Resource flags
	GPUType          string
	GPUCount         int
	CPUType          string
	CPUCount         string
	RAM              string
	EphemeralStorage string
	IgnoreLimits     bool

	// Placement flags
	AugPoolType     string
	AugLocalStorage bool
	Tolerations     []string
}

// NodeTLAFlags creates flags in the given FlagSet and returns a struct of vars which will contain the flag values.
func NodeTLAFlags(flags *pflag.FlagSet) *NodeTLAFlagVars {
	f := &NodeTLAFlagVars{
		flagset: flags,
	}

	// Meta flags
	flags.BoolVar(&f.IgnoreValidations, "ignore-validations", false, "Ignore all validation errors.")

	// Resource Flags
	flags.StringVar(&f.GPUType, "gpu-type", "", "The GPU type (alias) for node selectors.")
	flags.IntVar(&f.GPUCount, "gpu-count", 0, "The number of GPUs.")
	flags.StringVar(&f.CPUType, "cpu-type", "", "The CPU type (alias) for node selectors.")
	flags.StringVar(&f.CPUCount, "cpu-count", "", "The number of CPUs.")
	flags.StringVar(&f.RAM, "ram", "", "The amount of RAM, defaults to GB.")
	flags.StringVar(&f.EphemeralStorage, "ephemeral-storage", "", "The amount of ephemeral storage, defaults to GiB.")
	flags.BoolVarP(&f.IgnoreLimits, "ignore-limits", "L", false, "Ignore limit validations.")

	// Placement Flags
	flags.StringVar(&f.AugPoolType, "pool-type", "", "Set or override r.augmentcode.com/pool-type toleration.")
	flags.BoolVar(&f.AugLocalStorage, "local-storage", false, "Set or override the r.augmentcode.com/local-storage toleration.")
	flags.MarkHidden("local-storage")
	flags.StringSliceVar(&f.Tolerations, "tolerations", nil, "Add additional tolerations in the form key[=val][:effect]; effect defaults to NoSchedule.")
	flags.MarkHidden("tolerations")

	return f
}

// TLACode builds a Jsonnet Top-Level-Args *Code* map from the flags defined by
// NodeTLAFlags(), which can then be used to evaluate the jsonnet
// `node_from_args` function.
func (f NodeTLAFlagVars) TLACode() (map[string]string, error) {
	tlaCode := map[string]string{}
	errs := []error{}

	s := func(flag, key string, val string) {
		if f.flagset == nil || !f.flagset.Changed(flag) {
			return
		} else {
			tlaCode[key] = "'" + val + "'"
		}
	}
	ss := func(flag, key string, vals []string) {
		if f.flagset == nil || !f.flagset.Changed(flag) {
			return
		} else {
			quoted := []string{}
			for _, v := range vals {
				quoted = append(quoted, "'"+v+"'")
			}
			tlaCode[key] = "[" + strings.Join(quoted, ",") + "]"
		}
	}
	i := func(flag, key string, val int) {
		if f.flagset == nil || !f.flagset.Changed(flag) {
			return
		} else {
			tlaCode[key] = strconv.Itoa(val)
		}
	}
	b := func(flag, key string, val bool) {
		if f.flagset == nil || !f.flagset.Changed(flag) {
			return
		} else if val {
			tlaCode[key] = "true"
		} else {
			tlaCode[key] = "false"
		}
	}
	q := func(flag, key string, val string, units string, div float64) {
		if f.flagset == nil || !f.flagset.Changed(flag) {
			return
		}

		if units != "" {
			if _, err := strconv.ParseFloat(val, 64); err == nil {
				val += units
			}
		}

		q, err := resource.ParseQuantity(val)
		if err != nil {
			errs = append(errs, fmt.Errorf("--%s=%s: %v", flag, val, err))
		}

		if div == 0 {
			div = 1
		}
		tlaCode[key] = strconv.FormatFloat(q.AsApproximateFloat64()/div, 'f', -1, 64)
	}

	// Meta Flags
	b("ignore-validations", "__ignore_validations__", f.IgnoreValidations)

	// Resource Flags
	s("gpu-type", "gpu_type", f.GPUType)
	i("gpu-count", "gpu_count", f.GPUCount)
	s("cpu-type", "cpu_type", f.CPUType)
	q("cpu-count", "cpu_count", f.CPUCount, "", 1)
	q("ram", "ram", f.RAM, "G", 1000*1000*1000)
	q("ephemeral-storage", "ephemeral_storage", f.EphemeralStorage, "Gi", 1024*1024*1024)
	b("ignore-limits", "ignore_limits", f.IgnoreLimits)

	// Placement Flags
	s("pool-type", "aug_pool_type", f.AugPoolType)
	b("local-storage", "aug_local_storage", f.AugLocalStorage)
	ss("tolerations", "tolerations", f.Tolerations)

	return tlaCode, errors.Join(errs...)
}

type Node struct {
	PrimaryAlias      string   `json:"primary_alias"`
	Aliases           []string `json:"aliases"`
	AdditionalAliases []string `json:"additional_aliases"`
	AllAliases        []string `json:"all_aliases"`

	ValidationErrors []string `json:"validation_errors"`

	GPUs             int    `json:"gpus"`
	CPUs             int    `json:"cpus"`
	RAM              string `json:"ram_str"`
	EphemeralStorage string `json:"ephemeral_storage_str"`

	GPULimit              int    `json:"gpu_limit"`
	CPULimit              int    `json:"cpu_limit"`
	RAMLimit              string `json:"ram_limit_str"`
	EphemeralStorageLimit string `json:"ephemeral_storage_limit_str"`

	K8sResources *corev1a.ResourceRequirementsApplyConfiguration `json:"k8s_resources"`

	K8sNodeSelectors   []*corev1a.NodeSelectorRequirementApplyConfiguration `json:"k8s_node_selectors"`
	K8sTolerations     []*corev1a.TolerationApplyConfiguration              `json:"k8s_tolerations"`
	K8sAnnotations     map[string]string                                    `json:"k8s_annotations"`
	SidecarAnnotations map[string]string                                    `json:"sidecar_annotations"`
	SidecarContainers  []*corev1a.ContainerApplyConfiguration               `json:"sidecar_containers"`
	SidecarVolumes     []*corev1a.VolumeApplyConfiguration                  `json:"sidecar_volumes"`
	ContainerMounts    []*corev1a.VolumeMountApplyConfiguration             `json:"container_mounts"`
	ContainerEnvs      []*corev1a.EnvVarApplyConfiguration                  `json:"container_envs"`
}

func (n Node) Errors() []error {
	errs := []error{}
	for _, s := range n.ValidationErrors {
		errs = append(errs, errors.New(s))
	}
	return errs
}

func (n Node) Error() error {
	return errors.Join(n.Errors()...)
}

func (n Node) String() string {
	return strings.Join(n.TableRow(), " | ")
}

func (n Node) IsGPU() bool {
	return n.GPUs > 0
}

func (n Node) TableHeader() []string {
	return []string{
		"# alias (additional...)",
		"gpu (base)",
		"gpu (max)",
		"cpu (base)",
		"cpu (max)",
		"ram (base)",
		"ram (max)",
		"disk (base)",
		"disk (max)",
		"selector labels",
	}
}

func (n Node) TableRow() []string {
	aliases := n.PrimaryAlias
	if len(n.AdditionalAliases) > 0 {
		aliases += " (" + strings.Join(n.AdditionalAliases, " ") + ")"
	}
	itoa := func(i int) string {
		if i == 0 {
			return "-"
		}
		return strconv.Itoa(i)
	}
	atoa := func(s string) string {
		if s == "" {
			return "-"
		}
		return s
	}
	selectors := []string{}
	for _, sel := range n.K8sNodeSelectors {
		str := ""
		if k := sel.Key; k != nil {
			str += *k
		}
		if op := sel.Operator; op == nil {
			str += "?"
		} else if strings.ToLower(string(*op)) == "in" {
			str += "="
		} else if strings.ToLower(string(*op)) == "notin" {
			str += "!="
		} else {
			str += string(*op)
		}
		str += strings.Join(sel.Values, "+")
		selectors = append(selectors, str)
	}
	sort.Strings(selectors)
	return []string{
		aliases,
		itoa(n.GPUs),
		itoa(n.GPULimit),
		itoa(n.CPUs),
		itoa(n.CPULimit),
		atoa(n.RAM),
		atoa(n.RAMLimit),
		atoa(n.EphemeralStorage),
		atoa(n.EphemeralStorageLimit),
		atoa(strings.Join(selectors, "; ")),
	}
}

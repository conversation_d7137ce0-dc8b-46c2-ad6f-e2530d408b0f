package clusters

import (
	"fmt"
	"strings"

	corev1 "k8s.io/api/core/v1"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"
)

// A VolMount is a K8s Volume (which is at the PodSpec level) and one or more VolumeMounts (which
// are at the Container level and refer back to a Volume). This struct is defined to be sparsely
// defined, for example names default to the top-level `Name` property where possible.
//
// Use `Filled()`, `Volume()`, and/or `VolumeMounts()` to get fully-populated copies of this data.
type VolMount struct {
	Name   string               `json:"name,omitempty"`
	Vol    corev1.Volume        `json:"volume,omitempty"`
	Mounts []corev1.VolumeMount `json:"mounts,omitempty"`
}

// VolMountFromString parses a string into a VolMount. The format is limited to PVC_NAME:MOUNTPOINT.
func VolMountFromString(str string) (VolMount, error) {
	name, path, _ := strings.Cut(str, ":")
	if name == "" || path == "" {
		return VolMount{}, fmt.Errorf("volmount %q must be in the format PVC_NAME:MOUNTPOINT", str)
	}
	return VolMount{
		Name: name,
		Mounts: []corev1.VolumeMount{{
			MountPath: path,
		}},
	}, nil
}

// Filled returns a copy of the `VolMount` with derivable fields filled out.
func (vm VolMount) Filled() VolMount {
	return VolMount{
		Name:   vm.Name,
		Vol:    vm.Volume(),
		Mounts: vm.VolumeMounts(),
	}
}

// Volume returns a filled-out `corev1.Volume` spec.
func (vm VolMount) Volume() corev1.Volume {
	vol := vm.Vol.DeepCopy()
	vol.Name = vm.Name

	// Default to PVC if not otherwise set.
	if vol.VolumeSource.Size() == 0 {
		vol.VolumeSource.PersistentVolumeClaim = &corev1.PersistentVolumeClaimVolumeSource{}
	}

	// Set default source names, where possible.
	if src := vol.VolumeSource.Secret; src != nil && src.SecretName == "" {
		src.SecretName = vm.Name // pragma: allowlist secret
	}
	if src := vol.VolumeSource.PersistentVolumeClaim; src != nil && src.ClaimName == "" {
		src.ClaimName = vm.Name
	}
	if src := vol.VolumeSource.ConfigMap; src != nil && src.Name == "" {
		src.Name = vm.Name
	}

	return *vol
}

// VolumeApplyConfig returns a filled-out `corev1a.VolumeApplyConfiguration` spec.
func (vm VolMount) VolumeApplyConfig() *corev1a.VolumeApplyConfiguration {
	obj := vm.Volume()
	cfg := corev1a.Volume().WithName(obj.Name)

	if s := obj.VolumeSource.HostPath; s != nil {
		sc := corev1a.HostPathVolumeSource()
		cfg.WithHostPath(sc)
		sc.WithPath(s.Path)
		if s.Type != nil {
			sc.WithType(*s.Type)
		}
	}
	if s := obj.VolumeSource.EmptyDir; s != nil {
		sc := corev1a.EmptyDirVolumeSource()
		cfg.WithEmptyDir(sc)
		sc.WithMedium(s.Medium)
		if s.SizeLimit != nil {
			sc.WithSizeLimit(*s.SizeLimit)
		}
	}
	ktp := func(src []corev1.KeyToPath) []*corev1a.KeyToPathApplyConfiguration {
		ret := []*corev1a.KeyToPathApplyConfiguration{}
		for _, s := range src {
			d := corev1a.KeyToPath()
			ret = append(ret, d)

			d.WithKey(s.Key)
			d.WithPath(s.Path)
			if s.Mode != nil {
				d.WithMode(*s.Mode)
			}

		}
		return ret
	}
	if s := obj.VolumeSource.Secret; s != nil {
		sc := corev1a.SecretVolumeSource()
		cfg.WithSecret(sc)
		sc.WithSecretName(s.SecretName)
		sc.WithItems(ktp(s.Items)...)
		if s.DefaultMode != nil {
			sc.WithDefaultMode(*s.DefaultMode)
		}
		if s.Optional != nil {
			sc.WithOptional(*s.Optional)
		}
	}
	if s := obj.VolumeSource.ConfigMap; s != nil {
		sc := corev1a.ConfigMapVolumeSource()
		cfg.WithConfigMap(sc)
		sc.WithName(s.LocalObjectReference.Name)
		sc.WithItems(ktp(s.Items)...)
		if s.DefaultMode != nil {
			sc.WithDefaultMode(*s.DefaultMode)
		}
		if s.Optional != nil {
			sc.WithOptional(*s.Optional)
		}
	}
	if s := obj.VolumeSource.PersistentVolumeClaim; s != nil {
		sc := corev1a.PersistentVolumeClaimVolumeSource()
		cfg.WithPersistentVolumeClaim(sc)
		sc.WithClaimName(s.ClaimName)
		sc.WithReadOnly(s.ReadOnly)
	}
	if s := obj.VolumeSource.CSI; s != nil {
		sc := corev1a.CSIVolumeSource()
		cfg.WithCSI(sc)
		sc.WithDriver(s.Driver)
		sc.WithVolumeAttributes(s.VolumeAttributes)
		if s.ReadOnly != nil {
			sc.WithReadOnly(*s.ReadOnly)
		}
		if s.FSType != nil {
			sc.WithFSType(*s.FSType)
		}
	}
	if s := obj.VolumeSource.Ephemeral; s != nil {
		sc := corev1a.EphemeralVolumeSource()
		pvc := corev1a.PersistentVolumeClaimTemplate()
		spec := corev1a.PersistentVolumeClaimSpec()
		res := corev1a.VolumeResourceRequirements()

		cfg.WithEphemeral(sc)
		sc.WithVolumeClaimTemplate(pvc)
		pvc.WithSpec(spec)
		spec.WithResources(res)

		pvc.WithLabels(s.VolumeClaimTemplate.Labels)
		pvc.WithAnnotations(s.VolumeClaimTemplate.Annotations)

		spec.WithAccessModes(s.VolumeClaimTemplate.Spec.AccessModes...)
		if v := s.VolumeClaimTemplate.Spec.StorageClassName; v != nil {
			spec.WithStorageClassName(*v)
		}
		if v := s.VolumeClaimTemplate.Spec.VolumeMode; v != nil {
			spec.WithVolumeMode(*v)
		}
		res.WithRequests(s.VolumeClaimTemplate.Spec.Resources.Requests)

		if ds := s.VolumeClaimTemplate.Spec.DataSource; ds != nil {
			ref := corev1a.TypedLocalObjectReference()
			spec.WithDataSource(ref)
			if v := ds.APIGroup; v != nil {
				ref.WithAPIGroup(*v)
			}
			ref.WithKind(ds.Kind)
			ref.WithName(ds.Name)
		}
		if ds := s.VolumeClaimTemplate.Spec.DataSourceRef; ds != nil {
			ref := corev1a.TypedObjectReference()
			spec.WithDataSourceRef(ref)
			if v := ds.APIGroup; v != nil {
				ref.WithAPIGroup(*v)
			}
			ref.WithKind(ds.Kind)
			ref.WithName(ds.Name)
			if v := ds.Namespace; v != nil {
				ref.WithNamespace(*v)
			}
		}
	}

	return cfg
}

// VolumeMounts returns filled-out `corev1.VolumeMount` specs.
func (vm VolMount) VolumeMounts() []corev1.VolumeMount {
	mounts := make([]corev1.VolumeMount, len(vm.Mounts))
	for i, mnt := range vm.Mounts {
		if mnt.Name == "" {
			mnt.Name = vm.Name
		}
		mounts[i] = mnt
	}
	return mounts
}

// VolumeMountsApplyConfig returns filled-out `corev1a.VolumeMountApplyConfiguration` specs.
func (vm VolMount) VolumeMountsApplyConfig() []*corev1a.VolumeMountApplyConfiguration {
	volumeMounts := vm.VolumeMounts()
	mounts := []*corev1a.VolumeMountApplyConfiguration{}
	for _, obj := range volumeMounts {
		cfg := corev1a.VolumeMount()
		mounts = append(mounts, cfg)

		cfg.WithName(obj.Name)
		cfg.WithMountPath(obj.MountPath)
		cfg.WithSubPath(obj.SubPath)
		cfg.WithSubPathExpr(obj.SubPathExpr)
		cfg.WithReadOnly(obj.ReadOnly)

		if obj.MountPropagation != nil {
			cfg.WithMountPropagation(*obj.MountPropagation)
		}
		if obj.RecursiveReadOnly != nil {
			cfg.WithRecursiveReadOnly(*obj.RecursiveReadOnly)
		}
	}
	return mounts
}

type VolMounts []VolMount

func (vms VolMounts) Filled() VolMounts {
	ret := make(VolMounts, len(vms))
	for i, vm := range vms {
		ret[i] = vm.Filled()
	}
	return ret
}

func (vms VolMounts) Volumes() []corev1.Volume {
	ret := []corev1.Volume{}
	for _, vm := range vms {
		ret = append(ret, vm.Volume())
	}
	return ret
}

func (vms VolMounts) VolumeApplyConfigs() []*corev1a.VolumeApplyConfiguration {
	ret := []*corev1a.VolumeApplyConfiguration{}
	for _, vm := range vms {
		ret = append(ret, vm.VolumeApplyConfig())
	}
	return ret
}

func (vms VolMounts) Mounts() []corev1.VolumeMount {
	ret := []corev1.VolumeMount{}
	for _, vm := range vms {
		ret = append(ret, vm.VolumeMounts()...)
	}
	return ret
}

func (vms VolMounts) VolumeMountsApplyConfigs() []*corev1a.VolumeMountApplyConfiguration {
	ret := []*corev1a.VolumeMountApplyConfiguration{}
	for _, vm := range vms {
		ret = append(ret, vm.VolumeMountsApplyConfig()...)
	}
	return ret
}

#!/usr/bin/env python3
"""Python bindings for Augment Research Clusters.

The datasource for this these bindings, and the go bindings are from jsonnet. The
python and go bindings are roughly equivalent.
"""

import logging
import sys
from collections.abc import Callable
from dataclasses import dataclass
from pathlib import Path
from typing import Sequence

import _gojsonnet as jsonnet
import requests
from dataclasses_json import Undefined, dataclass_json
from research.infra.cfg.clusters.cluster import Cluster


@dataclass_json(undefined=Undefined.EXCLUDE)
@dataclass()
class Clusters:
    """Clusters is a dataclass for loading from jsonnet data.

    The Clusters "DB" should be loaded using the Clusters.from_jsonnet() class
    method. There are also three helper static methods which load the DB and
    return the requested data:

     - Clusters.load_clusters(): Returns the "prod" clusters.
     - Clusters.load_cluster(name: str): Returns the requested cluster.
     - Clusters.load_current(): Returns the current cluster that the library is
       being called from.

    All three methods might raise an exception on jsonnet eval errors, detection
    errors, or not-found errors.
    """

    clusters: dict[str, Cluster]

    @staticmethod
    def load_clusters() -> "Clusters":
        """A quick way to get the non-test Cluster definitions."""
        try:
            return Clusters.from_jsonnet().prod()
        except Exception as e:
            logging.error("Clusters.load_clusters(): %s.", e)
            raise

    @staticmethod
    def load_cluster(name: str) -> Cluster:
        """A quick way to get the named Cluster definition."""
        try:
            return Clusters.load_clusters().cluster(name)
        except Exception as e:
            logging.error("Clusters.load_cluster(%s): %s.", name, e)
            raise

    @staticmethod
    def load_current() -> Cluster:
        """A quick way to get the current Cluster definition (if detected)."""
        try:
            return Clusters.load_clusters().current()
        except Exception as e:
            logging.error("Clusters.load_current(): %s.", e)
            raise

    @classmethod
    def from_jsonnet(cls) -> "Clusters":
        fname = str(Path(__file__).parent / "clusters.jsonnet")
        s: str = jsonnet.evaluate_file(fname)
        return cls.schema().loads(s)  # type: ignore

    def filter(self, f: Callable[[Cluster], bool]) -> "Clusters":
        clusters = {n: c for n, c in self.clusters.items() if f(c)}
        return Clusters(clusters=clusters)

    def prod(self) -> "Clusters":
        def _f(c: Cluster) -> bool:
            return c.prod

        return self.filter(f=_f)

    def non_prod(self) -> "Clusters":
        def _f(c: Cluster) -> bool:
            return not c.prod

        return self.filter(f=_f)

    def list(self) -> Sequence[Cluster]:
        return sorted(self.clusters.values(), key=lambda c: c.name)

    def names(self) -> Sequence[str]:
        return sorted([c.name for c in self.clusters.values()])

    def cluster(self, name: str) -> Cluster:
        return self.clusters[name.lower()]

    def current(self) -> Cluster:
        name: str = self.detect_cluster()
        return self.cluster(name)

    @staticmethod
    def detect_cluster() -> str:
        # TODO(mattm): Bring this up to par with the go equivalent.
        try:
            r = requests.get(
                "http://metadata.google.internal/computeMetadata/v1/instance/zone",
                headers={"Metadata-Flavor": "Google"},
                timeout=3,
            )
            r.raise_for_status()
            if "us-central1" in r.text:
                return "gcp-us1"
        except:  # noqa: E722 pylint: disable=bare-except
            return "cw-east4"

        raise RuntimeError("Can't detect current provider")


if __name__ == "__main__":
    """A quick main to exercise the python bindings of the clusters jsonnet library."""
    if len(sys.argv) == 1:
        cluster = Clusters.load_current()
        print(cluster.dumps())
    else:
        clusters = Clusters.from_jsonnet()
        for arg in sys.argv[1:]:
            cluster = clusters.cluster(arg)
            print(cluster.dumps())

package clusters

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
)

// TestClusters tests some of the aggregate properties of the library. If any of these
// become annoying due to churn, just remove them.
func TestClusters(t *testing.T) {
	clusters, err := New()
	if err != nil {
		t.Fatalf("Error loading clusters: %v.", err)
	}

	wantNames := []string{
		"_unittest_",
		"cw-east4",
		"gcp-agent0",
		"gcp-core0",
		"gcp-eu-w4-prod-agent0",
		"gcp-prod-agent0",
		"gcp-us1",
		"prod-gsc",
	}
	if diff := cmp.Diff(wantNames, clusters.Names()); diff != "" {
		t.Errorf("Names(): -want +got:\n%s", diff)
	}

	wantRegs := []string{
		"reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1",
		"registry.cw-east4.r.augmentcode.com/infra",
		"us-central1-docker.pkg.dev/augment-research-gsc/docker-us-central1",
	}
	if diff := cmp.Diff(wantRegs, clusters.Registries()); diff != "" {
		t.Errorf("Registries(): -want +got:\n%s", diff)
	}

	if _, err := clusters.Cluster("notfound0"); !strings.Contains(err.Error(), "notfound0: configuration not found") {
		t.Errorf("Cluster() [notfound]: got unexpected error: %v.", err)
	}

	wantInProd := map[string]bool{"cw-east4": true, "gcp-us1": true, "prod-gsc": false}
	wantNotProd := map[string]bool{"_unittest_": true, "gcp-core0": true, "prod-gsc": true, "gcp-agent0": true, "gcp-prod-agent0": true, "gcp-eu-w4-prod-agent0": true}
	for _, n := range clusters.Prod().Names() {
		if !wantInProd[n] {
			t.Errorf("Names(prod=true): %s not included.", n)
		}
		if wantNotProd[n] {
			t.Errorf("Names(prod=true): %s included.", n)
		}
	}
	for _, n := range clusters.NonProd().Names() {
		if wantInProd[n] {
			t.Errorf("Names(prod=false): %s included.", n)
		}
		if !wantNotProd[n] {
			t.Errorf("Names(prod=false): %s not included.", n)
		}
	}

	if got, want := len(clusters.List()), len(clusters.Clusters); got != want {
		t.Errorf("List()[len]: got %v, want %v.", got, want)
	}
}

type rtFunc func(*http.Request) (*http.Response, error)

func (rt rtFunc) RoundTrip(r *http.Request) (*http.Response, error) {
	return rt(r)
}

func TestDetectCluster(t *testing.T) {
	tests := map[string]struct {
		want      string
		wantErr   string
		mZone     string
		mZoneCode int
		mZoneErr  error
		mRFBuf    string
		mRFErr    error
	}{
		"cw-ns-error": {
			want:      "",
			wantErr:   "error reading namespace: __readfile_err__",
			mZone:     "",
			mZoneErr:  fmt.Errorf("http client error"),
			mZoneCode: 0,
			mRFBuf:    "",
			mRFErr:    fmt.Errorf("__readfile_err__"),
		},
		"non-gcp-unknown": {
			want:      "",
			wantErr:   "unknown namespace: namespace0",
			mZone:     "",
			mZoneErr:  fmt.Errorf("http client error"),
			mZoneCode: 0,
			mRFBuf:    "namespace0",
			mRFErr:    nil,
		},
		"gcp-non-200": {
			want:      "",
			wantErr:   "HTTP non-OK status from GCP: 500",
			mZone:     "",
			mZoneErr:  nil,
			mZoneCode: http.StatusInternalServerError,
			mRFBuf:    "",
			mRFErr:    fmt.Errorf("__unused_error__"),
		},
		"gcp-no-match": {
			want:      "",
			wantErr:   "No mapping defined for: /project/path/zones/asia-southwest2-c",
			mZone:     "/project/path/zones/asia-southwest2-c",
			mZoneErr:  nil,
			mZoneCode: http.StatusOK,
			mRFBuf:    "",
			mRFErr:    fmt.Errorf("__unused_error__"),
		},
		"gcp-us1": {
			want:      "gcp-us1",
			wantErr:   "",
			mZone:     "/project/path/zones/us-central1-b",
			mZoneErr:  nil,
			mZoneCode: http.StatusOK,
			mRFBuf:    "",
			mRFErr:    fmt.Errorf("__unused_error__"),
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			clusters, err := New()
			if err != nil {
				t.Fatalf("Error loading clusters: %v.", err)
			}
			clusters.http = &http.Client{
				Transport: rtFunc(func(r *http.Request) (*http.Response, error) {
					if tc.mZoneErr != nil {
						return nil, tc.mZoneErr
					}
					rec := httptest.NewRecorder()
					rec.Code = tc.mZoneCode
					rec.Body.Write([]byte(tc.mZone))
					return rec.Result(), nil
				}),
			}
			clusters.osReadFile = func(_ string) ([]byte, error) {
				return []byte(tc.mRFBuf), tc.mRFErr
			}

			got, gotErr := clusters.DetectCluster(context.Background())

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}

			if got != tc.want {
				t.Errorf("cluster: got %s, want %s.", got, tc.want)
			}
		})
	}
}

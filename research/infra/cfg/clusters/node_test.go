package clusters

import (
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/spf13/pflag"
	"k8s.io/apimachinery/pkg/api/resource"

	corev1 "k8s.io/api/core/v1"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"
)

func TestNodeFrom(t *testing.T) {
	tests := map[string]struct {
		inFlags    map[string]string
		wantName   string
		wantTLAErr string
		wantErr    string
	}{
		"no-tla": {
			wantName: "cpu",
			wantErr:  "",
		},
		"err-parse-quantity": {
			wantTLAErr: "--ram=64GB: quantities must match the regular expression",
			inFlags: map[string]string{
				"ram": "64GB",
			},
		},
		"err-gpu-and-cpu": {
			wantName: "",
			wantErr:  "only one of gpu_type or cpu_type supported",
			inFlags: map[string]string{
				"gpu-type": "gpu0",
				"cpu-type": "cpu0",
			},
		},
		"err-validations": {
			wantName: "",
			wantErr:  "cpu cpus 999 greater than cpu_limit 150",
			inFlags: map[string]string{
				"cpu-type":  "cpu",
				"cpu-count": "999",
			},
		},
		"err-validations-noignore": {
			wantName: "",
			wantErr:  "cpu cpus 999 greater than cpu_limit 150",
			inFlags: map[string]string{
				"cpu-type":      "cpu",
				"cpu-count":     "999",
				"ignore-limits": "false",
			},
		},
		"noerr-validations-ignore": {
			wantName: "cpu",
			wantErr:  "",
			inFlags: map[string]string{
				"cpu-type":      "cpu",
				"cpu-count":     "999",
				"ignore-limits": "true",
			},
		},
		"noerr-not-found": {
			wantName: "",
			wantErr:  "found 0 node types",
			inFlags: map[string]string{
				"gpu-type": "notfound0",
			},
		},
		"cpu": {
			wantName: "cpu",
			wantErr:  "",
			inFlags: map[string]string{
				"cpu-type": "cpu",
			},
		},
		"gpu": {
			wantName: "nvidia-l4",
			wantErr:  "",
			inFlags: map[string]string{
				"gpu-type": "l4",
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			c := clusterT(t)

			flags := pflag.NewFlagSet("_test_", pflag.ContinueOnError)
			tla := NodeTLAFlags(flags)
			for k, v := range tc.inFlags {
				flags.Set(k, v)
			}

			gotTLA, gotTLAErr := tla.TLACode()
			if tc.wantTLAErr == "" && gotTLAErr != nil {
				t.Errorf("tla: got error %v, want no error.", gotTLAErr)
			}
			if tc.wantTLAErr != "" && (gotTLAErr == nil || !strings.Contains(gotTLAErr.Error(), tc.wantTLAErr)) {
				t.Errorf("tla: got error %v, want error containing '%v'.", gotTLAErr, tc.wantTLAErr)
			}
			if gotTLAErr != nil {
				return
			}

			gotNode, gotErr := c.NodeFromTLA(gotTLA)
			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}

			if got, want := gotNode.PrimaryAlias, tc.wantName; got != want {
				t.Errorf("PrimaryAlias: got %v, want %v.", got, want)
			}
		})
	}
}

func TestNode(t *testing.T) {
	quantT := func(t *testing.T, s string) resource.Quantity {
		q, err := resource.ParseQuantity(s)
		if err != nil {
			t.Fatalf("resource.ParseQuantity(%s): %v.", s, err)
		}
		return q
	}
	tests := map[string]struct {
		inFlags    map[string]string
		wantTLAErr string
		wantErr    string
		wantStr    string
		wantGPU    bool
		wantReq    *corev1.ResourceList
		wantSel    []*corev1a.NodeSelectorRequirementApplyConfiguration
		wantTol    []*corev1a.TolerationApplyConfiguration
	}{
		"default-no-tla": {
			wantStr: "cpu | - | - | 1 | 150 | 4.48G | 674.09G | 20.26Gi | 3044Gi | cloud.google.com/gke-ephemeral-storage-local-ssd=true",
			wantGPU: false,
			wantReq: &corev1.ResourceList{
				"cpu":               quantT(t, "1"),
				"memory":            quantT(t, "4.48G"),
				"ephemeral-storage": quantT(t, "20.26Gi"),
			},
			wantSel: []*corev1a.NodeSelectorRequirementApplyConfiguration{
				corev1a.NodeSelectorRequirement().
					WithKey("cloud.google.com/gke-ephemeral-storage-local-ssd").
					WithOperator("In").
					WithValues("true"),
			},
			wantTol: []*corev1a.TolerationApplyConfiguration{
				corev1a.Toleration().
					WithEffect(corev1.TaintEffectNoSchedule).
					WithKey("r.augmentcode.com/pool-type").
					WithValue("cpu"),
				corev1a.Toleration().
					WithEffect(corev1.TaintEffectPreferNoSchedule).
					WithKey("r.augmentcode.com/pool-type").
					WithValue("cpu"),
			},
		},
		"cpu": {
			inFlags: map[string]string{
				"cpu-type": "cpu",
			},
			wantStr: "cpu | - | - | 1 | 150 | 4.48G | 674.09G | 20.26Gi | 3044Gi | cloud.google.com/gke-ephemeral-storage-local-ssd=true",
			wantGPU: false,
			wantReq: &corev1.ResourceList{
				"cpu":               quantT(t, "1"),
				"memory":            quantT(t, "4.48G"),
				"ephemeral-storage": quantT(t, "20.26Gi"),
			},
			wantSel: []*corev1a.NodeSelectorRequirementApplyConfiguration{
				corev1a.NodeSelectorRequirement().
					WithKey("cloud.google.com/gke-ephemeral-storage-local-ssd").
					WithOperator("In").
					WithValues("true"),
			},
			wantTol: []*corev1a.TolerationApplyConfiguration{
				corev1a.Toleration().
					WithEffect(corev1.TaintEffectNoSchedule).
					WithKey("r.augmentcode.com/pool-type").
					WithValue("cpu"),
				corev1a.Toleration().
					WithEffect(corev1.TaintEffectPreferNoSchedule).
					WithKey("r.augmentcode.com/pool-type").
					WithValue("cpu"),
			},
		},
		"cpu-multiple": {
			inFlags: map[string]string{
				"cpu-count": "3",
			},
			wantStr: "cpu | - | - | 3 | 150 | 13.46G | 674.09G | 60.79Gi | 3044Gi | cloud.google.com/gke-ephemeral-storage-local-ssd=true",
			wantGPU: false,
			wantReq: &corev1.ResourceList{
				"cpu":               quantT(t, "3"),
				"memory":            quantT(t, "13.46G"),
				"ephemeral-storage": quantT(t, "60.79Gi"),
			},
			wantSel: []*corev1a.NodeSelectorRequirementApplyConfiguration{
				corev1a.NodeSelectorRequirement().
					WithKey("cloud.google.com/gke-ephemeral-storage-local-ssd").
					WithOperator("In").
					WithValues("true"),
			},
			wantTol: []*corev1a.TolerationApplyConfiguration{
				corev1a.Toleration().
					WithEffect(corev1.TaintEffectNoSchedule).
					WithKey("r.augmentcode.com/pool-type").
					WithValue("cpu"),
				corev1a.Toleration().
					WithEffect(corev1.TaintEffectPreferNoSchedule).
					WithKey("r.augmentcode.com/pool-type").
					WithValue("cpu"),
			},
		},
		"cpu-tolerations": {
			inFlags: map[string]string{
				"cpu-type":      "cpu",
				"pool-type":     "POOL0",
				"local-storage": "true",
				"tolerations":   "key1,key2=val2,key3:NoExecute,key4=val4:NoExecute",
			},
			wantStr: "cpu | - | - | 1 | 150 | 4.48G | 674.09G | 20.26Gi | 3044Gi | cloud.google.com/gke-ephemeral-storage-local-ssd=true",
			wantGPU: false,
			wantReq: &corev1.ResourceList{
				"cpu":               quantT(t, "1"),
				"memory":            quantT(t, "4.48G"),
				"ephemeral-storage": quantT(t, "20.26Gi"),
			},
			wantSel: []*corev1a.NodeSelectorRequirementApplyConfiguration{
				corev1a.NodeSelectorRequirement().
					WithKey("cloud.google.com/gke-ephemeral-storage-local-ssd").
					WithOperator("In").
					WithValues("true"),
			},
			wantTol: []*corev1a.TolerationApplyConfiguration{
				corev1a.Toleration().
					WithEffect(corev1.TaintEffectNoSchedule).
					WithKey("r.augmentcode.com/pool-type").
					WithValue("POOL0"),
				corev1a.Toleration().
					WithEffect(corev1.TaintEffectPreferNoSchedule).
					WithKey("r.augmentcode.com/pool-type").
					WithValue("POOL0"),
				corev1a.Toleration().
					WithEffect(corev1.TaintEffectNoSchedule).
					WithKey("r.augmentcode.com/local-storage").
					WithValue("true"),
				corev1a.Toleration().
					WithEffect(corev1.TaintEffectNoSchedule).
					WithKey("key1").
					WithOperator(corev1.TolerationOpExists),
				corev1a.Toleration().
					WithEffect(corev1.TaintEffectNoSchedule).
					WithKey("key2").
					WithValue("val2"),
				corev1a.Toleration().
					WithEffect(corev1.TaintEffectNoExecute).
					WithKey("key3").
					WithOperator(corev1.TolerationOpExists),
				corev1a.Toleration().
					WithEffect(corev1.TaintEffectNoExecute).
					WithKey("key4").
					WithValue("val4"),
			},
		},
		"gpu": {
			inFlags: map[string]string{
				"gpu-type": "l4",
			},
			wantStr: "nvidia-l4 (l4) | 1 | 4 | 9 | 38 | 43.5G | 174.02G | 86.37Gi | 345.5Gi | cloud.google.com/gke-accelerator=nvidia-l4; cloud.google.com/gke-ephemeral-storage-local-ssd=true",
			wantGPU: true,
			wantReq: &corev1.ResourceList{
				"nvidia.com/gpu":    quantT(t, "1"),
				"cpu":               quantT(t, "9.592"),
				"memory":            quantT(t, "43.5G"),
				"ephemeral-storage": quantT(t, "86.37Gi"),
			},
			wantSel: []*corev1a.NodeSelectorRequirementApplyConfiguration{
				corev1a.NodeSelectorRequirement().
					WithKey("cloud.google.com/gke-accelerator").
					WithOperator("In").
					WithValues("nvidia-l4"),
				corev1a.NodeSelectorRequirement().
					WithKey("cloud.google.com/gke-ephemeral-storage-local-ssd").
					WithOperator("In").
					WithValues("true"),
			},
		},
		"gpu-multiplied": {
			inFlags: map[string]string{
				"gpu-type":  "l4",
				"gpu-count": "3",
			},
			wantStr: "nvidia-l4 (l4) | 3 | 4 | 28 | 38 | 130.52G | 174.02G | 259.12Gi | 345.5Gi | cloud.google.com/gke-accelerator=nvidia-l4; cloud.google.com/gke-ephemeral-storage-local-ssd=true",
			wantGPU: true,
			wantReq: &corev1.ResourceList{
				"nvidia.com/gpu":    quantT(t, "3"),
				"cpu":               quantT(t, "28.777"),
				"memory":            quantT(t, "130.52G"),
				"ephemeral-storage": quantT(t, "259.12Gi"),
			},
			wantSel: []*corev1a.NodeSelectorRequirementApplyConfiguration{
				corev1a.NodeSelectorRequirement().
					WithKey("cloud.google.com/gke-accelerator").
					WithOperator("In").
					WithValues("nvidia-l4"),
				corev1a.NodeSelectorRequirement().
					WithKey("cloud.google.com/gke-ephemeral-storage-local-ssd").
					WithOperator("In").
					WithValues("true"),
			},
		},
		"gpu-customized": {
			inFlags: map[string]string{
				"gpu-type":          "l4",
				"gpu-count":         "3",
				"cpu-count":         "1",
				"ram":               "150",
				"ephemeral-storage": "300",
			},
			wantStr: "nvidia-l4 (l4) | 3 | 4 | 1 | 38 | 150G | 174.02G | 300Gi | 345.5Gi | cloud.google.com/gke-accelerator=nvidia-l4; cloud.google.com/gke-ephemeral-storage-local-ssd=true",
			wantGPU: true,
			wantReq: &corev1.ResourceList{
				"nvidia.com/gpu":    quantT(t, "3"),
				"cpu":               quantT(t, "1"),
				"memory":            quantT(t, "150G"),
				"ephemeral-storage": quantT(t, "300Gi"),
			},
			wantSel: []*corev1a.NodeSelectorRequirementApplyConfiguration{
				corev1a.NodeSelectorRequirement().
					WithKey("cloud.google.com/gke-accelerator").
					WithOperator("In").
					WithValues("nvidia-l4"),
				corev1a.NodeSelectorRequirement().
					WithKey("cloud.google.com/gke-ephemeral-storage-local-ssd").
					WithOperator("In").
					WithValues("true"),
			},
		},
		"edge-case-cpu-count=0": {
			inFlags: map[string]string{
				"cpu-count": "0",
			},
			wantErr: "Warning, cpus==0",
		},
		"edge-case-gpus-with-gpu-count=0": {
			inFlags: map[string]string{
				"gpu-type":  "nvidia-l4",
				"gpu-count": "0",
			},
			wantErr: "Warning, gpu_type==nvidia-l4 but gpus==0",
		},
		"edge-case-no-gpu-type-with-count": {
			inFlags: map[string]string{
				"gpu-count": "3",
			},
			wantErr: "Warning, gpus==3 without a gpu_type",
		},
		"edge-case-no-gpu-type-with-count-ignored": {
			inFlags: map[string]string{
				"gpu-count":          "3",
				"ignore-validations": "true",
			},
			wantStr: "cpu | 3 | - | 1 | 150 | 4.48G | 674.09G | 20.26Gi | 3044Gi | cloud.google.com/gke-ephemeral-storage-local-ssd=true",
			wantGPU: true,
			wantReq: &corev1.ResourceList{
				"nvidia.com/gpu":    quantT(t, "3"),
				"cpu":               quantT(t, "1"),
				"memory":            quantT(t, "4.48G"),
				"ephemeral-storage": quantT(t, "20.26Gi"),
			},
			wantSel: []*corev1a.NodeSelectorRequirementApplyConfiguration{
				corev1a.NodeSelectorRequirement().
					WithKey("cloud.google.com/gke-ephemeral-storage-local-ssd").
					WithOperator("In").
					WithValues("true"),
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			c := clusterT(t)

			flags := pflag.NewFlagSet("_test_", pflag.ContinueOnError)
			tla := NodeTLAFlags(flags)
			for k, v := range tc.inFlags {
				flags.Set(k, v)
			}

			gotTLA, gotTLAErr := tla.TLACode()
			if tc.wantTLAErr == "" && gotTLAErr != nil {
				t.Errorf("tla: got error %v, want no error.", gotTLAErr)
			}
			if tc.wantTLAErr != "" && (gotTLAErr == nil || !strings.Contains(gotTLAErr.Error(), tc.wantTLAErr)) {
				t.Errorf("tla: got error %v, want error containing '%v'.", gotTLAErr, tc.wantTLAErr)
			}
			if gotTLAErr != nil {
				return
			}

			gotNode, gotErr := c.NodeFromTLA(gotTLA)

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}

			if gotErr != nil {
				return
			}

			if got, want := gotNode.String(), tc.wantStr; got != want {
				t.Errorf("String() -want +got:\n - %s\n + %s", want, got)
			}
			if got, want := gotNode.IsGPU(), tc.wantGPU; got != want {
				t.Errorf("IsGPU: got %v, want %v.", got, want)
			}
			if diff := cmp.Diff(gotNode.K8sResources.Requests, gotNode.K8sResources.Limits); diff != "" {
				t.Errorf("K8s Resources mismatch: -reqs +limits:\n%s", diff)
			}
			if diff := cmp.Diff(tc.wantReq, gotNode.K8sResources.Requests); diff != "" {
				t.Errorf("K8s Resources: -want +got:\n%s", diff)
			}
			if diff := cmp.Diff(tc.wantSel, gotNode.K8sNodeSelectors, cmpopts.EquateEmpty()); diff != "" {
				t.Errorf("K8s Node Selectors: -want +got:\n%s", diff)
			}
			if diff := cmp.Diff(tc.wantTol, gotNode.K8sTolerations, cmpopts.EquateEmpty()); diff != "" {
				t.Errorf("K8s Tolerations: -want +got:\n%s", diff)
			}
		})
	}
}

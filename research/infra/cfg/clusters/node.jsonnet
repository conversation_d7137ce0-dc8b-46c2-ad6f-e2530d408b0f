local B = 1;
local KB = 1000 * B;
local MB = 1000 * KB;
local GB = 1000 * MB;
local TB = 1000 * GB;
local KiB = 1024 * B;
local MiB = 1024 * KiB;
local GiB = 1024 * MiB;
local TiB = 1024 * GiB;

{
  Node:: {
    local n = self,

    ////////////////////////////////////////////////////////////////////////////
    // Aliases

    aliases: [],

    primary_alias: (
      if n.gpu_selector_value != null then n.gpu_selector_value else
        if n.cpu_selector_value != null then n.cpu_selector_value else
          if std.length(n.aliases) > 0 then n.aliases[0] else
            error 'gpu_selector_value, cpu_selector_value, or at least one alias required'
    ),

    additional_aliases: std.filter(function(a) a != n.primary_alias, n.aliases),
    all_aliases: [n.primary_alias] + n.additional_aliases,

    ////////////////////////////////////////////////////////////////////////////
    // Validation

    ignore_limits: false,

    validation_errors: std.prune([
      if n.gpu_selector_value != null && n.gpus == 0 then std.format('Warning, gpu_type==%s but gpus==%d.', [n.gpu_selector_value, n.gpus]),
      if n.gpu_selector_value == null && n.gpus != null && n.gpus > 0 then std.format('Warning, gpus==%d without a gpu_type.', [n.gpus]),
      if n.cpus == 0 then 'Warning, cpus==0',
      if !n.ignore_limits && n.gpus != null && n.gpu_limit != null && n.gpus > n.gpu_limit then std.format('%s gpus %d greater than gpu_limit %d', [n.primary_alias, n.gpus, n.gpu_limit]),
      if !n.ignore_limits && n.cpus != null && n.cpu_limit != null && n.cpus > n.cpu_limit then std.format('%s cpus %d greater than cpu_limit %d', [n.primary_alias, n.cpus, n.cpu_limit]),
      if !n.ignore_limits && n.ram != null && n.ram_limit != null && n.ram > n.ram_limit then std.format('%s ram %d greater than ram_limit %d', [n.primary_alias, n.ram, n.ram_limit]),
      if !n.ignore_limits && n.ephemeral_storage != null && n.ephemeral_storage_limit != null && n.ephemeral_storage > n.ephemeral_storage_limit then std.format('%s ephemeral_storage %d greater than ephemeral_storage_limit %d', [n.primary_alias, n.ephemeral_storage, n.ephemeral_storage_limit]),
    ]),

    ////////////////////////////////////////////////////////////////////////////
    // Annotations

    k8s_annotations: {},

    ////////////////////////////////////////////////////////////////////////////
    // Base Resources

    multiplier: 1,

    _lead_resource:: null,
    _num_slots: std.floor(self._lead_resource.usable / self._lead_resource.base),
    _Resource:: {
      local r = self,

      name: error '_Resource.name is required',
      units: '',
      unit_f:: function(x) x,
      capacity: null,
      flat_tax: 0,
      tax_brackets:: [],
      deductions: [],

      allocatable: if self.capacity != null then self.capacity - self.tax - self.flat_tax,
      deduction: std.sum(self.deductions),
      usable: if self.allocatable != null then self.allocatable - self.deduction,

      _bracket:: function(rate, range=null) {
        _i: 0,
        _prev:: if self._i != 0 then r.tax_brackets_calced[self._i - 1],
        rate: rate,
        range: range,

        start: if self._prev == null then 0 else self._prev.end,
        end: if self.range != null then self.start + self.range,
        remaining: std.max(0, r.capacity - self.start),
        amount: if self.range == null then self.remaining else std.min(self.remaining, self.range),

        tax: self.rate * self.amount,
      },
      tax_brackets_calced: std.mapWithIndex(function(i, b) b + { _i: i }, self.tax_brackets),
      tax: std.foldl(function(sum, b) sum + b.tax, self.tax_brackets_calced, 0),

      base: if self.usable != null then n._lead_resource.base / n._lead_resource.usable * r.usable,
      multiplier: n.multiplier,
      value: if self.base != null then self.unit_f(self.multiplier * self.base),
      request: self.value,
      limit: self.request,

      // NOTE(mattm): Workaround jsonnet crashing on %g with 0.
      usable_str: if self.usable == 0 then '0' else if self.usable != null then std.format('%g%s', [self.unit_f(self.usable), self.units]),
      value_str: if self.value == 0 then '0' else if self.value != null then std.format('%g%s', [self.value, self.units]),
      request_str: if self.request == 0 then '0' else if self.request != null then std.format('%g%s', [self.request, self.units]),
      limit_str: if self.limit == 0 then '0' else if self.limit != null then std.format('%g%s', [self.limit, self.units]),
    },

    is_gpu: self.GPU.base != null,

    GPU:: self._Resource + {
      name: 'nvidia.com/gpu',
    },

    CPU:: self._Resource + {
      name: 'cpu',
      deductions+: std.prune([
        if self.capacity != null then 0.03 * self.capacity,  // 3% for daemonsets
      ]),
      unit_f: function(x) std.floor(x * 1000) / 1000,
    },

    RAM:: self._Resource + {
      name: 'memory',
      units: 'G',
      unit_f: function(x) std.floor(x * 100 / GB) / 100,
      deductions+: std.prune([
        if self.capacity != null then 0.03 * self.capacity,  // 3% for daemonsets
      ]),
    },

    DSK:: self._Resource + {
      name: 'ephemeral-storage',
      units: 'Gi',
      unit_f: function(x) if x != null then std.floor(x * 100 / GiB) / 100,
      deductions+: std.prune([
        if self.capacity != null then 0.10 * self.capacity,  // 10% for daemonsets
      ]),
    },

    ////////////////////////////////////////////////////////////////////////////
    // Resources

    gpus: if self.GPU.request != null then std.floor(self.GPU.request),
    cpus: std.floor(self.CPU.request),
    ram: self.RAM.request,
    ram_str: self.RAM.request_str,
    ephemeral_storage: self.DSK.request,
    ephemeral_storage_str: self.DSK.request_str,

    gpu_limit: if self.GPU.usable != null then std.floor(self.GPU.usable),
    cpu_limit: std.floor(self.CPU.usable),
    ram_limit: self.RAM.unit_f(self.RAM.usable),
    ram_limit_str: self.RAM.usable_str,
    ephemeral_storage_limit: self.DSK.unit_f(self.DSK.usable),
    ephemeral_storage_limit_str: self.DSK.usable_str,

    k8s_resources: {
      requests: std.prune({
        [n.GPU.name]: n.GPU.request_str,
        [n.CPU.name]: n.CPU.request_str,
        [n.RAM.name]: n.RAM.request_str,
        [n.DSK.name]: n.DSK.request_str,
      }),
      limits: std.prune({
        [n.GPU.name]: n.GPU.limit_str,
        [n.CPU.name]: n.CPU.limit_str,
        [n.RAM.name]: n.RAM.limit_str,
        [n.DSK.name]: n.DSK.limit_str,
      }),
    },

    ////////////////////////////////////////////////////////////////////////////
    // Node Selectors and Tolerations

    gpu_selector_label: null,
    gpu_selector_value: null,
    cpu_selector_label: null,
    cpu_selector_value: null,
    node_version_label: null,
    node_version_exclude_values: null,
    aug_pool_type: if n.gpus == null || n.gpus == 0 then 'cpu',
    aug_local_storage: false,
    tolerations: [],  // list of key[=val][:effect] defaults to NoSchedule

    //////////////////////////////////////////////////////////////////////////////
    // Sidecar Containers

    sidecar_annotations: {},
    sidecar_containers: [],
    sidecar_volumes: [],
    container_mounts: [],
    container_envs: [],

    local _sel(k, v, op='In') = if v != null then {
      key: k,
      operator: op,
      values: if std.type(v) == 'array' then v else [v],
    },
    k8s_node_selectors: std.prune([
      _sel(n.gpu_selector_label, n.gpu_selector_value),
      _sel(n.cpu_selector_label, n.cpu_selector_value),
      _sel(n.node_version_label, n.node_version_exclude_values, 'NotIn'),
    ]),

    // Parse key[=val][:effect]. If no val, then op=Exists; effect defaults to NoSchedule
    local _parse_tol = function(str)
      local kv_e = std.splitLimitR(str, ':', 1);
      local effect = if std.length(kv_e) == 2 then kv_e[1] else 'NoSchedule';
      local k_v = std.splitLimit(kv_e[0], '=', 1);
      local key = k_v[0];
      local value = if std.length(k_v) == 2 then k_v[1];
      local op = if value == null then 'Exists';
      std.prune({
        effect: effect,
        key: key,
        operator: op,
        value: value,
      }),

    k8s_tolerations: std.prune([
      if n.aug_pool_type != null then {
        effect: 'NoSchedule',
        key: 'r.augmentcode.com/pool-type',
        value: n.aug_pool_type,
      },
      if n.aug_pool_type != null then {
        effect: 'PreferNoSchedule',
        key: 'r.augmentcode.com/pool-type',
        value: n.aug_pool_type,
      },
      if n.aug_local_storage then {
        effect: 'NoSchedule',
        key: 'r.augmentcode.com/local-storage',
        value: std.toString(n.aug_local_storage),
      },
    ] + [
      _parse_tol(s)
      for s in n.tolerations
    ]),
  },

  GPUNodeMixin:: {
    _lead_resource:: self.GPU,
    GPU+: {
      base: 1,
    },
  },

  CPUNodeMixin:: {
    _lead_resource:: self.CPU,
    CPU+: {
      base: 1,
    },
  },
}

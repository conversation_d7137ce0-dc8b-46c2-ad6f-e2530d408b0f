package clusters

import (
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"

	corev1 "k8s.io/api/core/v1"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"
)

func TestVolMounts(t *testing.T) {
	t.<PERSON>llel()

	c := clusterT(t)
	volmnts := c.VolMounts

	wantVols := []corev1.Volume{
		{
			Name: "gcs-_unittest_-checkpoints0",
			VolumeSource: corev1.VolumeSource{
				CSI: &corev1.CSIVolumeSource{
					Driver: "gcsfuse.csi.storage.gke.io",
					VolumeAttributes: map[string]string{
						"bucketName":            "_unittest_-checkpoints0",
						"fileCacheCapacity":     "-1",
						"fileCacheForRangeRead": "false",
						"mountOptions": strings.Join([]string{
							"implicit-dirs",
							"uid=1000",
							"gid=1000",
							"file-mode=0660",
							"dir-mode=0770",
							"o=noexec",
							"o=noatime",
							"file-cache:enable-parallel-downloads:true",
							"file-cache:parallel-downloads-per-file:8",
							"file-cache:max-parallel-downloads:-1",
							"file-cache:download-chunk-size-mb:8",
							"file-system:kernel-list-cache-ttl-secs:30",
							"file-system:ignore-interrupts:false",
						}, ","),
					},
				},
			},
		},
		{
			Name: "gcs-_unittest_-bucket1",
			VolumeSource: corev1.VolumeSource{
				CSI: &corev1.CSIVolumeSource{
					Driver: "gcsfuse.csi.storage.gke.io",
					VolumeAttributes: map[string]string{
						"bucketName":            "_unittest_-bucket1",
						"fileCacheCapacity":     "-1",
						"fileCacheForRangeRead": "false",
						"mountOptions": strings.Join([]string{
							"implicit-dirs",
							"uid=1000",
							"gid=1000",
							"file-mode=0660",
							"dir-mode=0770",
							"o=noexec",
							"o=noatime",
							"file-cache:enable-parallel-downloads:true",
							"file-cache:parallel-downloads-per-file:8",
							"file-cache:max-parallel-downloads:-1",
							"file-cache:download-chunk-size-mb:8",
							"file-system:kernel-list-cache-ttl-secs:30",
							"file-system:ignore-interrupts:false",
						}, ","),
					},
				},
			},
		},
		{
			Name: "sec0",
			VolumeSource: corev1.VolumeSource{
				Secret: &corev1.SecretVolumeSource{
					SecretName: "sec0", // pragma: allowlist secret
				},
			},
		},
	}

	wantVolsApply := []*corev1a.VolumeApplyConfiguration{
		corev1a.Volume().WithName("gcs-_unittest_-checkpoints0").
			WithCSI(corev1a.CSIVolumeSource().
				WithDriver("gcsfuse.csi.storage.gke.io").
				WithVolumeAttributes(map[string]string{
					"bucketName":            "_unittest_-checkpoints0",
					"fileCacheCapacity":     "-1",
					"fileCacheForRangeRead": "false",
					"mountOptions": strings.Join([]string{
						"implicit-dirs",
						"uid=1000",
						"gid=1000",
						"file-mode=0660",
						"dir-mode=0770",
						"o=noexec",
						"o=noatime",
						"file-cache:enable-parallel-downloads:true",
						"file-cache:parallel-downloads-per-file:8",
						"file-cache:max-parallel-downloads:-1",
						"file-cache:download-chunk-size-mb:8",
						"file-system:kernel-list-cache-ttl-secs:30",
						"file-system:ignore-interrupts:false",
					}, ","),
				})),
		corev1a.Volume().WithName("gcs-_unittest_-bucket1").
			WithCSI(corev1a.CSIVolumeSource().
				WithDriver("gcsfuse.csi.storage.gke.io").
				WithVolumeAttributes(map[string]string{
					"bucketName":            "_unittest_-bucket1",
					"fileCacheCapacity":     "-1",
					"fileCacheForRangeRead": "false",
					"mountOptions": strings.Join([]string{
						"implicit-dirs",
						"uid=1000",
						"gid=1000",
						"file-mode=0660",
						"dir-mode=0770",
						"o=noexec",
						"o=noatime",
						"file-cache:enable-parallel-downloads:true",
						"file-cache:parallel-downloads-per-file:8",
						"file-cache:max-parallel-downloads:-1",
						"file-cache:download-chunk-size-mb:8",
						"file-system:kernel-list-cache-ttl-secs:30",
						"file-system:ignore-interrupts:false",
					}, ","),
				})),
		corev1a.Volume().WithName("sec0").
			WithSecret(corev1a.SecretVolumeSource().WithSecretName("sec0")),
	}

	wantMnts := []corev1.VolumeMount{
		{
			Name:      "gcs-_unittest_-checkpoints0",
			MountPath: "/mnt/efs/checkpoints0",
		},
		{
			Name:      "gcs-_unittest_-bucket1",
			MountPath: "/mnt/bucket1",
		},
		{
			Name:      "sec0",
			MountPath: "/run/sec0",
		},
		{
			Name:      "sec0",
			MountPath: "/run/sec0-extra",
		},
	}

	wantMntsApply := []*corev1a.VolumeMountApplyConfiguration{
		corev1a.VolumeMount().WithName("gcs-_unittest_-checkpoints0").
			WithMountPath("/mnt/efs/checkpoints0").
			WithReadOnly(false).
			WithSubPath("").
			WithSubPathExpr(""),
		corev1a.VolumeMount().WithName("gcs-_unittest_-bucket1").
			WithMountPath("/mnt/bucket1").
			WithReadOnly(false).
			WithSubPath("").
			WithSubPathExpr(""),
		corev1a.VolumeMount().WithName("sec0").
			WithMountPath("/run/sec0").
			WithReadOnly(false).
			WithSubPath("").
			WithSubPathExpr(""),
		corev1a.VolumeMount().WithName("sec0").
			WithMountPath("/run/sec0-extra").
			WithReadOnly(false).
			WithSubPath("").
			WithSubPathExpr(""),
	}

	if diff := cmp.Diff(wantVols, volmnts.Volumes()); diff != "" {
		t.Errorf("Volumes(): -want +got:\n%s", diff)
	}

	if diff := cmp.Diff(wantVolsApply, volmnts.VolumeApplyConfigs()); diff != "" {
		t.Errorf("VolumeApplyConfigs(): -want +got:\n%s", diff)
	}

	if diff := cmp.Diff(wantMnts, volmnts.Mounts()); diff != "" {
		t.Errorf("Mounts(): -want +got:\n%s", diff)
	}

	if diff := cmp.Diff(wantMntsApply, volmnts.VolumeMountsApplyConfigs()); diff != "" {
		t.Errorf("VolumeMountsApplyConfigs(): -want +got:\n%s", diff)
	}
}

func TestVolMountFromString(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		in      string
		want    VolMount
		wantErr string
	}{
		"empty": {
			in:      "",
			wantErr: `"" must be in the format PVC_NAME:MOUNTPOINT`,
		},
		"colon": {
			in:      ":",
			wantErr: `":" must be in the format PVC_NAME:MOUNTPOINT`,
		},
		"no-colon": {
			in:      "foo",
			wantErr: `"foo" must be in the format PVC_NAME:MOUNTPOINT`,
		},
		"no-mount-path": {
			in:      "foo:",
			wantErr: `"foo:" must be in the format PVC_NAME:MOUNTPOINT`,
		},
		"no-volname": {
			in:      ":foo",
			wantErr: `":foo" must be in the format PVC_NAME:MOUNTPOINT`,
		},
		"success": {
			in: "foo:bar",
			want: VolMount{
				Name: "foo",
				Mounts: []corev1.VolumeMount{{
					MountPath: "bar",
				}},
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			got, gotErr := VolMountFromString(tc.in)
			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}
			if diff := cmp.Diff(tc.want, got); diff != "" {
				t.Errorf("VolMountFromString(): -want +got:\n%s", diff)
			}
		})
	}
}

// Package clusters is a go-wrapper around the clusters config library.
package clusters

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"sort"
	"strings"
	"sync"

	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/infra/lib/logger"
	"github.com/augmentcode/augment/research/infra/cfg"
)

type Clusters struct {
	vm *cfg.VM

	http       *http.Client
	osReadFile func(string) ([]byte, error)

	Clusters map[string]Cluster `json:"clusters"`
}

// New creates a new Clusters object.
func New() (*Clusters, error) {
	vm, err := cfg.NewVM()
	if err != nil {
		return nil, err
	}
	c := &Clusters{
		vm: vm,

		http:       http.DefaultClient,
		osReadFile: os.ReadFile,
	}
	if err := vm.EvalJSON("clusters/clusters.jsonnet", nil, "", c); err != nil {
		return nil, err
	}
	for name := range c.Clusters {
		cluster := c.Clusters[name]
		cluster.vm = c.vm
		c.Clusters[name] = cluster
	}
	return c, nil
}

// Filter returns a subset of Clusters.
func (c Clusters) Filter(f func(Cluster) bool) *Clusters {
	filtered := map[string]Cluster{}
	for id, cluster := range c.Clusters {
		if f(cluster) {
			filtered[id] = cluster
		}
	}
	return &Clusters{
		vm:         c.vm,
		http:       c.http,
		osReadFile: c.osReadFile,
		Clusters:   filtered,
	}
}

// Prod filters to just prod clusters.
func (c Clusters) Prod() *Clusters {
	return c.Filter(func(cluster Cluster) bool {
		return cluster.Prod
	})
}

// NonProd filters to just nonprod clusters.
func (c Clusters) NonProd() *Clusters {
	return c.Filter(func(cluster Cluster) bool {
		return !cluster.Prod
	})
}

// Available filters out clusters that the caller doesn't have an entry for in Kubeconfig.
func (c Clusters) Available() *Clusters {
	cfg, err := k8s.LoadConfig("")
	if err != nil {
		logger.New(nil).LogErr("Unable to load K8s config to check for available contexts: %v.", err)
		return &c
	}
	set := map[string]bool{}
	for _, c := range cfg.Contexts() {
		set[c] = true
	}
	return c.Filter(func(cluster Cluster) bool {
		return set[cluster.Context]
	})
}

func (c Clusters) List() []Cluster {
	ret := []Cluster{}
	for _, cluster := range c.Clusters {
		ret = append(ret, cluster)
	}
	sort.Slice(ret, func(i, j int) bool {
		return ret[i].Name < ret[j].Name
	})
	return ret
}

func (c Clusters) Names() []string {
	ret := []string{}
	for _, cluster := range c.Clusters {
		ret = append(ret, cluster.Name)
	}
	sort.Strings(ret)
	return ret
}

// Registries returns a sorted, deduped list of registries.
// NOTE: These are a "registry prefix" for images in the cluster. They are
// useful for referencing images, but not for accessing the Registry V2 API.
func (c Clusters) Registries() []string {
	set := map[string]bool{}
	for _, cluster := range c.Clusters {
		if cluster.Registry != "" {
			set[cluster.Registry] = true
		}
	}

	ret := []string{}
	for reg := range set {
		ret = append(ret, reg)
	}
	sort.Strings(ret)
	return ret
}

func (c Clusters) Cluster(name string) (Cluster, error) {
	cluster, ok := c.Clusters[strings.ToLower(name)]
	if !ok {
		return Cluster{}, fmt.Errorf("%s: configuration not found", name)
	}
	return cluster, nil
}

func (c Clusters) Current(ctx context.Context) (Cluster, error) {
	name, err := c.DetectCluster(ctx)
	if err != nil {
		return Cluster{}, err
	}
	return c.Cluster(name)
}

// DetectCluster detects the current cluster. It works by trying the (Google) metadata API at Link-Local
// 169.254.169.254. Any response (regardless of HTTP Code) we assume GCP. Otherwise we match on the in-cluster
// namespace to determine which CoreWeave cluster.
func (c Clusters) DetectCluster(ctx context.Context) (string, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, "http://metadata.google.internal/computeMetadata/v1/instance/zone", http.NoBody)
	if err != nil {
		return "", fmt.Errorf("DetectCluster(): error building new http request: %w", err)
	}
	req.Header.Add("Metadata-Flavor", "Google")

	// If we get an error, we assume CoreWeave. If we don't get an error, we assume Google
	resp, err := c.http.Do(req)

	if err != nil {
		/// CoreWeave

		curNS, err := k8s.InClusterNamespace(c.osReadFile)
		if err != nil {
			return "", fmt.Errorf("DetectCluster(): non-GCP, but error reading namespace: %v.", err)
		}

		matches := c.Filter(func(c Cluster) bool {
			// Try to avoid matching GCP clusters that might have the same namespace.
			if !strings.HasPrefix(strings.ToLower(c.Name), "cw") {
				return false
			}
			return c.MainNamespace == curNS
		})
		matchesLst := matches.List()

		if l := len(matchesLst); l == 0 {
			return "", fmt.Errorf("DetectCluster(): non-GCP, but unknown namespace: %s.", curNS)
		} else if l != 1 {
			return "", fmt.Errorf("DetectCluster(): matched multiple CW clusters: %s.", strings.Join(matches.Names(), " "))
		} else {
			return matchesLst[0].Name, nil
		}
	} else {
		/// GCP

		if resp.StatusCode != http.StatusOK {
			return "", fmt.Errorf("DetectCluster(): HTTP non-OK status from GCP: %s", resp.Status)
		}

		defer resp.Body.Close()
		buf, err := io.ReadAll(resp.Body)
		if err != nil {
			return "", fmt.Errorf("DetectCluster(): error reading GCP response body: %w", err)
		}

		// The response contains the project, region, and zone. Parse out the region.
		// https://cloud.google.com/compute/docs/regions-zones#identifying_a_region_or_zone

		parts := strings.Split(string(buf), "/")
		parts = strings.Split(parts[len(parts)-1], "-")
		region := strings.Join(parts[0:len(parts)-1], "-")

		switch region {
		case "us-central1": // NOTE(mattm): This isn't thorough enough yet
			return "gcp-us1", nil
		default:
			return "", fmt.Errorf("DetectCluster(): No mapping defined for: %s", string(buf))
		}
	}
}

func (cs Clusters) Resources(ctx context.Context) (map[string][]*ResourceNode, map[string]error) {
	wg := sync.WaitGroup{}
	wg.Add(len(cs.Clusters))
	multiCluster := make(map[string][]*ResourceNode, len(cs.Clusters))
	multiError := make(map[string]error, len(cs.Clusters))

	for _, c := range cs.Clusters {
		go func() {
			defer wg.Done()
			k, err := c.NewK8s()
			if err != nil {
				multiError[c.Name] = err
				return
			}
			multiCluster[c.Name], multiError[c.Name] = c.clusterResources(ctx, k)
		}()
	}
	wg.Wait()

	return multiCluster, multiError
}

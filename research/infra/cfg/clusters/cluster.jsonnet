local node = import 'node.jsonnet';
local volmount = import 'volmount.jsonnet';

{
  Cluster:: {
    local c = self,
    name: error 'Cluster.name is reqired',
    long_name: c.name,
    prod: false,  // affects list of clusters for some operations
    // NOTE(mattm): Consider defaulting context and main_namespace str.asciiLower(self.name)
    context: error 'Cluster.context is required',
    context_admin: self.context,
    main_namespace: error 'Cluster.main_namespace is required',
    sys_namespace: null,
    checkpoint_bucket: error 'Cluster.checkpoint_bucket is required',
    shared_mount: error 'Cluster.shared_mount is required',
    determined_url: error 'Cluster.determined_url is required',
    k8s_url_external: null,
    k8s_url_internal: null,
    augi_release_readers: 'augi-release-readers',
    nodes: [],
    node_names: [n.primary_alias for n in c.nodes],

    user_roles: [],
    user_cluster_roles: [],
    user_cluster_cluster_roles: [],

    devpod_default_priority_class: 'aug-devpod-default-priority',

    // NOTE: Historically, `registry` means the applied to images and includes
    // the hostname and an image path.
    registry_hostname: error 'Cluster.registry_hostname is required',
    registry_url: 'https://' + self.registry_hostname,
    registry_basepath: '',
    registry: self.registry_hostname + self.registry_basepath,
    registry_img_pull_sec: null,

    // "Mountpoint" for per-cluster k8s library.
    k8s:: {
      BaseLabels+:: {
        'aug.cluster': c.name,
      },
      Object+:: {
        namespace:: c.main_namespace,
      },
    },

    images: {
      // TODO(mattm): std.stripChars -> std.trim
      local fmt(image_version) = c.registry + '/' + std.stripChars(image_version, ' \n\t'),
      cpu: fmt(importstr '../../../environments/cpu_tag.txt'),
      gpu: fmt(importstr '../../../environments/gpu_tag.txt'),
      spark_cpu: fmt(importstr '../../../environments/devpod_cpu_tag.txt'),
      spark_gpu: fmt(importstr '../../../environments/devpod_gpu_tag.txt'),
      devpod_cpu: fmt(importstr '../../../environments/devpod_cpu_tag.txt'),
      devpod_gpu: fmt(importstr '../../../environments/devpod_gpu_tag.txt'),
    },

    images_main: {
      [i.key]: std.splitLimit(i.value, ':', 1)[0] + ':main'
      for i in std.objectKeysValues(c.images)
    },

    Node:: node.Node,
    GPUNode:: self.Node + node.GPUNodeMixin,
    CPUNode:: self.Node + node.CPUNodeMixin,

    node(a)::
      local _a = std.asciiLower(a);
      local matches = std.filter(function(n) std.any([std.asciiLower(x) == _a for x in n.all_aliases]), c.nodes);
      local len = std.length(matches);
      if len == 1 then matches[0] else error std.format('"%s": found %d node types', [a, len]),

    node_from_args(
      gpu_type=null,
      gpu_count=null,
      cpu_type=null,
      cpu_count=null,
      ram=null,
      ephemeral_storage=null,
      ignore_limits=null,
      aug_pool_type=null,
      aug_local_storage=null,
      tolerations=null,
    )::
      if gpu_type != null && cpu_type != null then error 'only one of gpu_type or cpu_type supported' else
        local alias = if gpu_type != null then gpu_type else if cpu_type != null then cpu_type else 'cpu';
        local tmpl = c.node(alias);
        local settings = [
          if tmpl.is_gpu then {
            multiplier: if gpu_count != null then gpu_count else 1,
          } + if cpu_count != null then { CPU+: { value: cpu_count } } else {},
          if !tmpl.is_gpu then {
            multiplier: if cpu_count != null then cpu_count else 1,
          } + if gpu_count != null then { GPU+: { value: gpu_count } } else {},
          if ram != null then { RAM+: { value: ram } },
          if ephemeral_storage != null then { DSK+: { value: ephemeral_storage } },
          if ignore_limits != null then { ignore_limits: ignore_limits },
          if aug_pool_type != null then { aug_pool_type: aug_pool_type },
          if aug_local_storage != null then { aug_local_storage: aug_local_storage },
          if tolerations != null then { tolerations: tolerations },
        ];
        local node = std.foldl(function(X, x) if x != null then X + x else X, settings, tmpl);
        node,

    volmount_defs:: [],
    volmounts: [volmount.VolMount + def for def in c.volmount_defs],
  },
}

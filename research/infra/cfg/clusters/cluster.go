package clusters

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"sync"

	"github.com/augmentcode/augment/infra/lib/distribution"
	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/research/infra/cfg"
)

type Cluster struct {
	vm *cfg.VM // for node_from_args

	Name               string            `json:"name"`
	LongName           string            `json:"long_name"`
	Prod               bool              `json:"prod"`
	RegistryHostname   string            `json:"registry_hostname"`
	RegistryURL        string            `json:"registry_url"`
	RegistryBasepath   string            `json:"registry_basepath"`
	Registry           string            `json:"registry"`
	Context            string            `json:"context"`
	ContextAdmin       string            `json:"context_admin"`
	MainNamespace      string            `json:"main_namespace"`
	CheckpointBucket   string            `json:"checkpoint_bucket"`
	SharedMount        string            `json:"shared_mount"`
	DeterminedURL      string            `json:"determined_url"`
	K8sURLExternal     string            `json:"k8s_url_external"`
	K8sURLInternal     string            `json:"k8s_url_internal"`
	AugiReleaseReaders string            `json:"augi_release_readers"`
	Images             map[string]string `json:"images"`
	ImagesMain         map[string]string `json:"images_main"`
	Nodes              []Node            `json:"nodes"`
	VolMounts          VolMounts         `json:"volmounts"`
	DevPodDefaultPC    string            `json:"devpod_default_priority_class"`

	// GCP Extensions
	GCPProject       string `json:"gcp_project"`
	GCPProjectNumber int    `json:"gcp_project_number"`
}

func (c Cluster) Equals(other Cluster) bool {
	return c.Name == other.Name
}

func (c Cluster) Profile(export bool) string {
	exp := ""
	if export {
		exp = "export "
	}
	lines := []string{
		exp + "AUGMENT_DEV_CLOUD_PROVIDER=" + c.Name,
		exp + "AUGMENT_DEV_CONTAINER_REPO=" + c.Registry,
		exp + "AUGMENT_DEV_GPU_TAG=" + c.Images["gpu"],
		exp + "AUGMENT_DEV_CPU_TAG=" + c.Images["cpu"],
		exp + "AUGMENT_DEVPOD_CPU_TAG=" + c.Images["devpod_cpu"],
		exp + "AUGMENT_DEVPOD_GPU_TAG=" + c.Images["devpod_gpu"],
		exp + "AUGMENT_DEV_DETERMINED_URL=" + c.DeterminedURL,
		exp + "AUGMENT_DEV_SHARED_MOUNT_PATH=" + c.SharedMount,
		"", // trailing newline after join
	}
	return strings.Join(lines, "\n")
}

// Dump prints out info in a format ~identical to providers.py --all.
func (c Cluster) Dump(nodes, volmounts bool) {
	fmt.Println("cluster:", c.Name)
	fmt.Println("prod:", c.Prod)
	fmt.Println("checkpoint_bucket:", c.CheckpointBucket)
	fmt.Println("context:", c.Context)
	fmt.Println("cpu_tag:", c.Images["cpu"])
	fmt.Println("determined_url:", c.DeterminedURL)
	fmt.Println("k8s_url_external:", c.K8sURLExternal)
	fmt.Println("k8s_url_internal:", c.K8sURLInternal)
	fmt.Println("augi_release_readers:", c.AugiReleaseReaders)
	fmt.Println("devpod_cpu_tag:", c.Images["devpod_cpu"])
	fmt.Println("devpod_gpu_tag:", c.Images["devpod_gpu"])
	fmt.Println("gpu_tag:", c.Images["gpu"])
	fmt.Println("main_namespace:", c.MainNamespace)
	fmt.Println("registry:", c.Registry)
	fmt.Println("shared_mount:", c.SharedMount)
	if nodes {
		fmt.Println("nodes:")
		c.DumpNodes(" - ", true, true)
	}
	if volmounts {
		fmt.Println("volmounts:")
		for _, vm := range c.VolMounts {
			fmt.Printf(" - %s\n", vm.Volume().Name)
			for _, mnt := range vm.Mounts {
				fmt.Printf("   - %s\n", mnt.MountPath)
			}
		}
	}
}

// DumpNodes prints out an aligned table of node spec details.
func (c Cluster) DumpNodes(pfx string, gpu, cpu bool) {
	// Accumulate rows: Always include header, and then conditionally GPU and CPU rows.
	rows := [][]string{
		Node{}.TableHeader(),
	}
	for _, n := range c.Nodes {
		if n.IsGPU() {
			if !gpu {
				continue
			}
		} else {
			if !cpu {
				continue
			}
		}
		rows = append(rows, n.TableRow())
	}

	// Get max width in each columns.
	widths := map[int]int{}
	for _, row := range rows {
		for c, col := range row {
			if w := len(col); w > widths[c] {
				widths[c] = w
			}
		}
	}

	// Format each row to a buffer.
	buf := &strings.Builder{}
	for _, row := range rows {
		fmt.Fprint(buf, pfx)
		for c, col := range row {
			fmtstr := " | %*s"
			// left-align first and last cols which are more string-y than the others
			if c == 0 || c == len(row)-1 {
				fmtstr = " | %-*s"
			}
			fmt.Fprintf(buf, fmtstr, widths[c], col)
		}
		fmt.Fprint(buf, "\n")
	}

	// Finally, print to stdout.
	fmt.Println(buf.String())
}

// NodeFromTLA builds a Node -- which contains K8s Resource Quantities and K8s
// Node Affinity Selectors -- from a TLA map. The values in the map are jsonnet
// "TLA Code" strings.
func (c Cluster) NodeFromTLA(tla map[string]string) (Node, error) {
	ignoreValidations := tla["__ignore_validations__"] == "true"
	delete(tla, "__ignore_validations__")

	snipLines := []string{
		"local clusters = import 'clusters/clusters.jsonnet';",
		"local cluster = clusters.cluster('" + c.Name + "');",
		"cluster.node_from_args",
	}
	snippet := strings.Join(snipLines, "\n")
	n := Node{}
	if err := c.vm.EvalJSON(c.Name+".node_from_args()", tla, snippet, &n); err != nil {
		return Node{}, err
	}
	if !ignoreValidations {
		if err := n.Error(); err != nil {
			return Node{}, err
		}
	}
	return n, nil
}

func (c Cluster) IsGCP() bool {
	return strings.HasPrefix(strings.ToLower(c.Name), "gcp") || c.Name == "_unittest_"
}

func (c Cluster) LatestImageFromCurrent(curImage string) (string, error) {
	candidates := []string{
		c.Images["devpod_cpu"],
		c.Images["devpod_gpu"],
	}

	curReg, curRepo, _ := distribution.ParseImage(curImage)

	for _, latestImage := range candidates {
		latestReg, latestRepo, _ := distribution.ParseImage(latestImage)
		if latestReg == curReg && latestRepo == curRepo {
			return latestImage, nil
		}
	}

	return "", fmt.Errorf("LatestImageFromCurrent(): only `augment_devpod_cpu` and `augment_devpod_gpu` supported, got %s", curImage)
}

// NewK8s returns a k8s.Client using the Cluster.Context.
func (c Cluster) NewK8s() (*k8s.Client, error) {
	return k8s.New(c.Context)
}

func (c Cluster) CCNamespace() string {
	return "cc-" + c.Name
}

// ////////////////////////////////////////////////////////////////////////////
// Resources related code

func (c Cluster) clusterResources(ctx context.Context, k *k8s.Client) ([]*ResourceNode, error) {
	nodeMap := map[string]*ResourceNode{}

	wg := sync.WaitGroup{}

	activePods := []*k8s.Pod{}
	var podErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		includeStatus := map[string]bool{"Pending": true, "Running": true, "Init": true}

		listPods, podErr := k.ListPods(ctx)
		if podErr != nil {
			return
		}
		for _, pod := range listPods {
			if _, ok := includeStatus[pod.Status().String()]; !ok {
				continue
			}
			activePods = append(activePods, pod)
		}
	}()

	allNodes := []*k8s.Node{}
	var nodeErr error
	wg.Add(1)
	go func() {
		defer wg.Done()
		allNodes, nodeErr = k.ListGPUNodes(ctx)
	}()
	wg.Wait()

	if podErr != nil {
		return nil, podErr
	}
	// If nodes aren't available (CW), do our best

	gpuNodes := map[string]*k8s.Node{}
	for _, n := range allNodes {
		t := n.CanonicalGPUType()
		// TODO fix GPU filtering
		if t == "H100" || t == "A100" {
			gpuNodes[n.Name()] = n
			rn := &ResourceNode{
				NodeName: n.Name(),
				GpuType:  t,
				GpuCount: n.GPUCapacity(),
			}
			nodeMap[n.Name()] = rn
		}
	}

	for _, pod := range activePods {

		gpu := pod.CanonicalGPUType()
		// TODO fix GPU filtering
		if gpu != "H100" && gpu != "A100" {
			continue
		}
		count := pod.GPUCount()
		if count == 0 {
			continue
		}
		pd := ResourcePod{
			PodName:  pod.Name(),
			GpuCount: count,
			GpuType:  gpu,
			Status:   pod.Status(),
			User:     pod.AugmentUser(),
		}

		// get pod node
		node := pod.NodeName()
		rn, ok := nodeMap[node]
		if !ok {
			gpc := 0
			nd, ok := gpuNodes[node]
			if ok {
				gpc = nd.GPUCapacity()
			}
			// We only hit this on the pending pods
			// TODO(marcmac) I think this fall apart with pending pods of different gpu types
			rn = &ResourceNode{
				NodeName: node,
				GpuType:  gpu,
				GpuCount: gpc,
				Pods:     make([]ResourcePod, 0),
			}
			nodeMap[node] = rn
		}
		rn.Add(pd)
	}

	ret := []*ResourceNode{}
	for _, rn := range nodeMap {
		ret = append(ret, rn)
	}

	sort.Slice(ret, func(i, j int) bool {
		if ret[j].NodeName == "" {
			return false
		}
		if ret[i].NodeName == ret[j].NodeName {
			return ret[i].GpuType < ret[j].GpuType
		}
		return ret[i].NodeName < ret[j].NodeName
	})

	return ret, nodeErr
}

// /////////////////////////////////////////////////////////////////////////////
// structs for pod and node resource information
func elide(s string, maxLen int) string {
	if len(s) > maxLen || len(s) < 5 {
		return s[:(maxLen/2)-1] + "..." + s[len(s)-((maxLen/2)-2):]
	}
	return s
}

type ResourcePod struct {
	PodName  string
	GpuCount int
	GpuType  string
	User     string
	Status   k8s.PodStatus
}

type ResourceNode struct {
	NodeName string
	GpuType  string
	GpuCount int
	Pods     []ResourcePod
}

func (rp ResourcePod) String() string {
	return fmt.Sprintf("%s(%s): %s %vx%d", rp.PodName, rp.User, rp.Status.String(), rp.GpuType, rp.GpuCount)
}

func (rp ResourcePod) TableRow(pfx string) string {
	return pfx + fmt.Sprintf("%-40.40s %-8.8s %-3d", elide(rp.PodName, 40), rp.User, rp.GpuCount)
}

func (rn *ResourceNode) Add(pod ResourcePod) {
	rn.Pods = append(rn.Pods, pod)
}

func (rn *ResourceNode) String() string {
	nn := rn.NodeName
	if rn.NodeName == "" {
		nn = "PENDING"
	}
	st := []string{fmt.Sprintf("%s %s: %d/%d", nn, rn.GpuType, rn.UsedGpu(), rn.GpuCount)}
	for _, pod := range rn.Pods {
		st = append(st, fmt.Sprintf("\t%s", pod.String()))
	}
	return strings.Join(st, "\n")
}

func (rn *ResourceNode) Table(pfx string) string {
	if rn == nil {
		return ""
	}
	nn := rn.NodeName
	if rn.NodeName == "" {
		nn = "PENDING"
	}
	st := []string{fmt.Sprintf("%s %dx%s", nn, rn.UsedGpu(), rn.GpuType)}
	sort.Slice(rn.Pods, func(i, j int) bool {
		if rn.Pods[i].User != rn.Pods[j].User {
			return rn.Pods[i].User < rn.Pods[j].User
		}
		if rn.Pods[i].GpuCount != rn.Pods[j].GpuCount {
			return rn.Pods[i].GpuCount < rn.Pods[j].GpuCount
		}
		return rn.Pods[i].PodName < rn.Pods[j].PodName
	})
	for _, pod := range rn.Pods {
		st = append(st, pod.TableRow(pfx+"\t"))
	}

	return strings.Join(st, "\n")
}

func (rn *ResourceNode) UsedGpu() int {
	used := 0
	for _, pod := range rn.Pods {
		st := pod.Status.String()
		if st == "Running" || st == "Pending" {
			used += pod.GpuCount
		}
	}
	return used
}

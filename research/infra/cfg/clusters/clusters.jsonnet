{
  imports:: [
    import 'clusters/unittest.jsonnet',
    import 'clusters/gcp.jsonnet',
    import 'clusters/coreweave.jsonnet',
    import 'clusters/gcp-core.jsonnet',
    import 'clusters/prod-gsc.jsonnet',
    import 'clusters/gcp-agent.jsonnet',
  ],

  clusters: std.foldl(function(X, x) X + x, $.imports, {}),
  cluster_names: [c.name for c in std.objectValues($.clusters)],

  cluster:: function(name) self.clusters[std.asciiLower(name)],
  cluster_prop:: function(name, prop) self.clusters[std.asciiLower(name)][prop],
  is_gcp:: function(name) std.startsWith(std.asciiLower(name), 'gcp'),
}

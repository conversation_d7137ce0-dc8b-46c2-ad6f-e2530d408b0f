CLUSTERS := gcp-core0
KUBECFG  := kubecfg --qps-limit=-1 --context="$$context"

show:
	for cluster in $(CLUSTERS); do \
		echo ====== "$$cluster" ======; \
		context=$$(jsonnet -A name="$$cluster" -A prop=context_admin -Se "(import '../clusters.jsonnet').cluster_prop") ; \
		$(KUBECFG) show "$$cluster.jsonnet"; \
	done

diff:
	for cluster in $(CLUSTERS); do \
		echo ====== "$$cluster" ======; \
		context=$$(jsonnet -A name="$$cluster" -A prop=context_admin -Se "(import '../clusters.jsonnet').cluster_prop") ; \
		$(KUBECFG) diff --diff-strategy=subset "$$cluster.jsonnet"; \
	done


cleandiff:
	for cluster in $(CLUSTERS); do \
		echo ====== "$$cluster" ======; \
		context=$$(jsonnet -A name="$$cluster" -A prop=context_admin -Se "(import '../clusters.jsonnet').cluster_prop") ; \
		$(KUBECFG) diff --diff-strategy=subset "$$cluster.jsonnet" | awk '{ lines[NR] = $$0; if (NR > 2) { if (lines[NR-2] !~ /unchanged$$/ && lines[NR-1] !~ /unchanged$$/ && lines[NR] !~ /unchanged$$/) { print lines[NR-2]; } } }'; \
	done

apply:
	for cluster in $(CLUSTERS); do \
		echo ====== "$$cluster" ======; \
		context=$$(jsonnet -A name="$$cluster" -A prop=context_admin -Se "(import '../clusters.jsonnet').cluster_prop") ; \
		$(KUBECFG) update --ignore-unknown "$$cluster.jsonnet"; \
	done


CLUSTER_G   := gcp-core0
CONTEXT_G   := $(shell jsonnet -Se "(import '../clusters.jsonnet').cluster_prop" -A name="$(CLUSTER_G)" -A prop=context_admin)
CFG_G       := gcp-global.jsonnet
KUBECFG_G   := kubecfg --qps-limit=-1 --context="$(CONTEXT_G)"

show-global:
	$(KUBECFG_G) show "$(CFG_G)"

diff-global:
	$(KUBECFG_G) diff --diff-strategy=subset "$(CFG_G)"

cleandiff-global:
	$(KUBECFG_G) diff --diff-strategy=subset "$(CFG_G)" | awk '{ lines[NR] = $$0; if (NR > 2) { if (lines[NR-2] !~ /unchanged$$/ && lines[NR-1] !~ /unchanged$$/ && lines[NR] !~ /unchanged$$/) { print lines[NR-2]; } } }'

apply-global:
	$(KUBECFG_G) update --ignore-unknown "$(CFG_G)"

local tmpl = import 'agent-workspaces.tmpl.jsonnet';

function(aH=null, stages=[2, 3, 4, 5])
  tmpl(
    aH=aH,
    cluster='gcp-prod-agent0',
    envs=['STAGING', 'PROD'],
    ws_dns='ws.augmentcode.com',
    ws_dns_parent='projects/system-services-prod/managedZones/augmentcode-com',
    // ws_dns_shard='d',
    glassbreakers=[
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    stages=stages,
  )

local clusters = import '../clusters.jsonnet';
local bitnami_ss = import 'tmpl/bitnami-ss.jsonnet';
local external_dns = import 'tmpl/external-dns.jsonnet';
local image_primer = import 'tmpl/image-primer.jsonnet';
local ingress_nginx = import 'tmpl/ingress-nginx.jsonnet';
local kuberay_operator = import 'tmpl/kuberay-operator.jsonnet';

local C = clusters.cluster('cw-east4');

C.k8s + {
  // Hardcoded users/admins.
  users: [$.user + t for t in [
    { name:: 'tongfei' },
  ]],
  admins: [$.admin + t for t in [
    { name:: 'marcmac' },
    { name:: 'mattm' },
  ]],

  // Default user namespace a la tenant-augment-eng.
  main_ns: $.Namespace + {
    name:: C.main_namespace,  // cw-east4
  },

  // Augment-specific namespace, like kube-system.
  aug_system_ns: $.Namespace + {
    name:: 'aug-system',
  },

  eng_secrets_ns: $.Namespace + {
    name:: 'eng-secrets',
  },

  // A user is a ServiceAccount+token, in the *aug-system* namespace.
  user:: {
    name:: error 'user.name required',
    user_sa: $._sa_and_tok(self.name, $.aug_system_ns.metadata.name),
  },

  // An admin is a user + "root" and "admin" SAs in the *aug-system* namespace.
  admin:: $.user + {
    root_sa: $._sa_and_tok(self.name + '-root', $.aug_system_ns.metadata.name),
    admin_sa: $._sa_and_tok(self.name + '-admin', $.aug_system_ns.metadata.name),
  },

  // User,Admin,Root ClusterRoleBindings
  // https://kubernetes.io/docs/reference/access-authn-authz/rbac/#user-facing-roles
  // - user: pick 'view' just to have something, we'll likely have a custom role for this.
  // - admin: pick 'cluster-admin' just to have something, we might want a custom role for this.
  // - root: 'cluster-admin' is everything, like the system:masters group, which our admin kubeconfig from cw uses.
  user_rb: $.ClusterRoleBinding + {
    name:: 'aug:cluster-users',
    role_name:: 'view',  // TODO(mattm): Something custom.
    sas:: [a.user_sa.sa for a in $.admins] + [u.user_sa.sa for u in $.users],
  },
  view_extra_role: $.ClusterRole + {
    name:: 'aug:aggregate-to-view',
    metadata+: {
      labels+: {
        'rbac.authorization.k8s.io/aggregate-to-view': 'true',
      },
    },
    rules+: [
      {
        apiGroups: [''],
        resources: ['nodes'],
        verbs: $.READ_VERBS,
      },
    ],
  },
  user_rb_edit: $.RoleBinding + {
    name:: 'aug:cluster-users',
    namespace:: $.main_ns.metadata.name,
    role_name:: 'edit',  // TODO(mattm): Something custom.
    role_ref_kind:: 'ClusterRole',
    sas:: [a.user_sa.sa for a in $.admins] + [u.user_sa.sa for u in $.users],
  },
  admin_rb: $.ClusterRoleBinding + {
    name:: 'aug:cluster-admins',
    role_name:: 'cluster-admin',  // TODO(mattm): Maybe something slightly less powerfull?
    sas:: [a.admin_sa.sa for a in $.admins],
  },
  root_rb: $.ClusterRoleBinding + {
    name:: 'aug:cluster-root',
    role_name:: 'cluster-admin',  // NOTE(mattm): Equivalent to "admin" crt kubeconfig from CoreWeave, but not shared.
    sas:: [a.root_sa.sa for a in $.admins],
  },

  // Role and binding for eng-secrets
  eng_secrets: {
    local es = self,
    namespace:: 'eng-secrets',
    eng_secrets_sa: $.ServiceAccount + {
      name:: 'eng-secrets-sa',
    },
    eng_secrets_reader_role: $.Role + {
      name:: 'aug:eng-secrets-reader-role',
      namespace:: es.namespace,
      rules: [
        {
          apiGroups: [''],
          resources: ['secrets'],
          verbs: $.READ_VERBS,
        },
        {
          apiGroups: ['bitnami.com'],
          resources: ['sealedsecrets'],
          verbs: $.READ_VERBS,
        },

      ],
    },
    eng_secrets_writer_role: $.Role + {
      name:: 'aug:eng-secrets-writer-role',
      namespace:: es.namespace,
      rules: [
        {
          apiGroups: [''],
          resources: ['secrets'],
          verbs: $.READ_VERBS,
        },
        {
          apiGroups: ['bitnami.com'],
          resources: ['sealedsecrets'],
          verbs: $.READ_VERBS + $.WRITE_VERBS_NO_COLLECTION,
        },

      ],
    },
    eng_secrets_reader_role_binding: $.RoleBinding + {
      name:: 'aug:eng-secrets-reader-role-binding',
      namespace:: es.namespace,
      role_name:: es.eng_secrets_reader_role.name,
      sas:: [{ metadata: { name: 'default', namespace: 'cw-east4' } }, { metadata: { name: 'determined-service-account', namespace: 'cw-east4' } }],
    },
    eng_secrets_writer_role_binding: $.RoleBinding + {
      name:: 'aug:eng-secrets-writer-role-binding',
      namespace:: es.namespace,
      role_name:: es.eng_secrets_writer_role.name,
      sas:: [{ metadata: { name: es.eng_secrets_sa.name, namespace: es.eng_secrets_sa.namespace } }],
    },
  },

  pc: $.PriorityClass + {
    name:: 'aug-devpod-default-priority',
    value: 1000000,
    preemptionPolicy: self.PREEMPTION_POLICY.LOWER,
    description: |||
      The default PriorityClass for DevPods. Designed to match "determined-system-priority" which
      was used historically for this case.
    |||,
  },

  bitnami_ss: {
    release: bitnami_ss(C.k8s, name='bitnami-ss0', values={
      tolerations+: [
        {
          key: 'is_cpu_compute',
          operator: 'Exists',
        },
        {
          key: 'node.coreweave.cloud/reserved',
          operator: 'Exists',
        },
      ],
    }),
  },

  local edns_sa_key = '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',
  external_dns: external_dns(C.k8s, 'augment-research-gsc', zones=[C.name + '.r.augmentcode.com'], namespace=$.aug_system_ns.metadata.name, owner_id=std.asciiLower(C.name), sa_key=edns_sa_key),

  ingress_nginx: {
    release: ingress_nginx(C.k8s, name='nginx0', values={
      controller+: {
        service+: {
          annotations+: {
            'service.beta.kubernetes.io/coreweave-load-balancer-ip-families': 'ipv4',
            'service.beta.kubernetes.io/coreweave-load-balancer-type': 'public',
          },
        },
      },
    }),
  },

  ////////////////////////////////////////////////////////////////////////////
  //
  // KubeRay Operator
  //
  // Manages Ray clusters in Kubernetes
  //

  kuberay_operator: {
    release: kuberay_operator(C.k8s, name='kuberay-operator', namespace='ray-system', values={
      tolerations+: [
        {
          key: 'is_cpu_compute',
          operator: 'Exists',
        },
        {
          key: 'node.coreweave.cloud/reserved',
          operator: 'Exists',
        },
      ],
    }),
  },

  issuer: $.ClusterIssuer + {
    name:: 'letsencrypt-prod',
    spec+: {
      acme: {
        privateKeySecretRef: {
          name: 'letsencrypt-account-key',
        },
        server: 'https://acme-v02.api.letsencrypt.org/directory',
        solvers: [
          {
            http01: {
              ingress: {
                ingressClassName: 'nginx',
                serviceType: $.SERVICE_TYPE.CLUSTER_IP,
              },
            },
          },
        ],
      },
    },
  },

  ss_issuer: $.ClusterIssuer + {
    name:: 'selfsigned',
    spec+: {
      selfSigned: {},
    },
  },

  primer: image_primer(C, cycle='5m'),

  img_pull: [
    $.SealedSecret + {
      name:: 'registry-image-pull-secret',
      namespace:: ns,
      encryptedData: {
        token: {
          // NOTE(mattm): --scope=cluster-wide didn't work
          'aug-system': 'AgCfg9Kp8iUwNFno3lw+7wwfn/22jyDwQYPhTBwDtydVqyKq8OS90aU4j2Zkwf+g5ZGg4Wfj3EBbWZT8g1x/gK8ELdXMC5dhIHVnlNf3HAMniGlW966gv6nwzzOEygwrFZQ5fng69ixjuIA2KjpPL78uPnIMJOKWeLXvll1Q9YwbL/LewKpw02wxpYRYlASeX/6gy/WU9KzWP460pLsbscgdOTNgKjM4Tv1Pw5NU4YZRNA7sgza/vi4iPXsONVJgVtgAV6riv2TGETCk347OrrqgcU79axZzT0qHkyV3aXXpk1ysZhp5hcyyVqcLTt7R3Xrtv8YDdOpnNBwSSQBmmwz8grt0PMzuHjciVuk4Xa4IeQV1lZK1oEJWYvQeGOQ3RPkFl35Bl5R6B1YGAx+9TlrgACzKNcPDIShCjiVEWo48jXeILobIO5pkj4e4ClAvupOrMpvfVl9FLUj/iSmR5Z4dzBYmRZEWlXaWUXMtUYw/eJ5OlG1vbCg+PDB+/yrbV9oQ56gNc8Ws4+9jPwOCQfHid5FLHXA1a3V1ngXuzID/2z/7B7NGVY+xlK3gs3b2ekgJvFfRwappSonMCX6o58DJy6jb8nA8XZh4IPostG8TIszoJHvCYONvcx9ITYcxvMcB2+yprk6DUS6Y73pLUsqujbpVcbfw8aM/M1RilUPczYdrq0t01YfzRl95pYzME8PPvJ78BEI+ctjLpM9lZ7F25o4D+xdTTu9+oxvCSoL66Q==',
          [C.main_namespace]: 'AgBBhbwz+o108mDV/mpM+szDgAgw59o4Vone0EFhHbZqomYnYi4WOjQo2vw7pdY7gTArcD22eSyIyveNo/M8YHtSgIOiW2ooC3o3zagbQfau5lT2Z8ZY/V2aV6ltYje7SDaADSpOD/7Ehd6XUGQJUIyAgzvKdp7mMTMx580ZoUWuvMbHoZ/jYUHsD6d2nI4UN5scNhUu3UN//XSprIxyNy1bc1BA8wuujHIUE+NNLxfJD/eMABv6aSPP+jmYCjPNZQgQzjw8dUajyx4bGyOuzyH6RkviOAuXh0whw8W1EIZ1iVaoCMEHUYM+piQr+65ktBumN7Uzb0GpXPUTK6C3yeNhj70fpeNYCv/7SGRo5kKlzRmDNYR7Ajq/vpMKaveGVdUSx8trzEZqX47xf2r1bwdBnEwZ7ukyXqPotBKfwLV7rLTn/0BuL80uauzShZ/9D9EdODcszkWXIQ3x8vB0c5TaBjKFWbUf9L06I/49NZw1x9I4y1Kw2sQwu+5Rzcg/E+V0CLvdNLZ5Q6SQUoMEc3kF1F9Lv+6atW2Hnl0wlhZ7BsgN9/cZk5nsZJc1ftmnBDd2RR6OOUNfdQx11xnVrkW77bE9qYk0yzlwJNkdV08as8EqgNrZGycFIhGubUKoVNgmE4thU/CoDJawfTZOn4/DbmA9hDODxH8HiXnse7lVucASvy+EE8+fySN30keAn93CiblCPzqXilMj8dYyrjPLuvnXaA6h+mVzIGsutfjEsQ==',
        }[ns],
      },
      spec+: {
        template+: {
          type: $.SECRET_TYPE.DOCKER,
          data: {
            '.dockerconfigjson': |||
              {
                "auths": {
                  "registry.cw-east4.r.augmentcode.com": {
                    "auth": "{{printf "r@image-pull-secret0:%s" .token | b64enc}}"
                  }
                }
              }
            |||,
          },
        },
      },
    }
    for ns in ['aug-system', C.main_namespace]
  ],
  def_sa: [
    $.ServiceAccount + {
      name:: 'default',
      namespace:: ns,
      imagePullSecrets+: [
        { name: 'registry-image-pull-secret' },
      ],
    }
    for ns in ['aug-system', C.main_namespace]
  ],

  // NOTE(mattm): Provide from base k8s lib.
  _sa_and_tok(name, namespace):: {
    sa: $.ServiceAccount + {
      name:: name,
      namespace:: namespace,
    },
    tok: $.Secret + {
      i_know_what_im_doing: true,
      name:: name + '-tok',
      namespace:: namespace,
      type: 'kubernetes.io/service-account-token',
      metadata+: {
        annotations+: {
          'kubernetes.io/service-account.name': name,
        },
      },
    },
  },

  ray: {
    gsa: $.SealedSecret + {
      name:: C.name + '-ray-key',
      namespace:: C.main_namespace,
      encryptedData: {
        // kubectl --context=gke_augment-research-gsc_us-central1_gcp-core0 -n cc-cw-east4 get iamserviceaccountkey.iam.cnrm.cloud.google.com/cw-east4-ray-sa --template='{{.status.privateKey|base64decode}}' | augi k8s -K cw-east4 seal 'cw-east4 'cw-east4-ray-key'
        'key.json': '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',
      },
    },
  },
}

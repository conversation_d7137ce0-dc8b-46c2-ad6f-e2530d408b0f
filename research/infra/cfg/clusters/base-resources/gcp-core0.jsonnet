local clusters = import '../clusters.jsonnet';

local bitnami_ss = import 'tmpl/bitnami-ss.jsonnet';
local cert_manager = import 'tmpl/cert-manager.jsonnet';
local config_connector = import 'tmpl/config-connector.jsonnet';
local external_dns = import 'tmpl/external-dns.jsonnet';
local gke_cluster = import 'tmpl/gke_cluster.jsonnet';
local ingress_nginx = import 'tmpl/ingress-nginx.jsonnet';
local lib = import 'tmpl/lib.jsonnet';

// We divide the configuration into multiple stages because we can't push
// all of this at once.
//
// Outer (self-hosted, so some of this needs to be done by hand initially):
//   Stage0: The GKE Cluster Outer (Cluster, Networks, Node SA, NodePools)
//   Stage1: ConfigConnector Outer (IAM ServiceAccount and Roles)
// Inner (internal k8s resources):
//   Stage2: ConfigConnector Install (configconnector-operator-system)
//   Stage3: ConfigConnector and ConfigConnectorContext CRD (namespace config)
//   Stage4: The GKE Cluster Inner (StorageClass, NetDev, ...)
//   stage5: Marine Services + supporting ConfigConnector CRDs, DNS Zone, etc
//

function(cluster='gcp-core0', stages=[0, 1, 2, 3, 4, 5]) {

  // Unlike other clusters, these are both the same but define two separate variables so
  // it's clear where the bootstrapping issues are.
  local H = clusters.cluster(cluster),
  local C = clusters.cluster(cluster),

  // Helper wrapper to apply the 'svc' NoSchedule toleration.
  local svcTolerate = function(x) lib.ApplyToleration(x, 'r.augmentcode.com/pool-type', 'svc'),

  //////////////////////////////////////////////////////////////////////////////
  //
  // Stage0: GKE Cluster Outer (Cluster, Networks, Node SA, NodePools)
  // Stage4: GKE Cluster Inner (StorageClass, NetDev, ...)
  //
  // There's a circular dependency here while bootstrapping, but we need to start
  // somewhere; a fix would be if config-connector had a CLI ad-hoc mode. We need
  // to get a basic GKE cluster running with ConfigConnector in order to bootstrap.
  //
  // gcloud --project=augment-research-gsc compute networks create gcp-core0-vpc0 \
  //   --description="gcp-core0 net 1of1 [primary]" \
  //   --subnet-mode=custom \
  //   --mtu=8244
  //
  // gcloud --project=augment-research-gsc compute networks subnets create gcp-core0-vpc0-subnet0 \
  //   --description="gcp-core0 net 1of1 (subnet) [primary]" \
  //   --network=gcp-core0-vpc0 \
  //   --region=us-central1 \
  //   --stack-type=IPV4_ONLY \
  //   --range=10.0.0.0/16 \
  //   --secondary-range=pod=**********/16,svc=**********/16 \
  //   --enable-private-ip-google-access
  //
  // gcloud --project=augment-research-gsc container clusters create gcp-core0 \
  //   --region=us-central1 \
  //   --no-enable-basic-auth \
  //   --release-channel=regular \
  //   --network "projects/augment-research-gsc/global/networks/gcp-core0-vpc0" \
  //   --subnetwork "projects/augment-research-gsc/regions/us-central1/subnetworks/gcp-core0-vpc0-subnet0" \
  //   --cluster-secondary-range-name="pod" \
  //   --services-secondary-range-name="svc" \
  //   --workload-pool "augment-research-gsc.svc.id.goog"
  //

  cluster: gke_cluster + {
    host_cluster:: H.name,
    host_namespace:: 'cc-' + C.name,

    project_id:: C.gcp_project,
    location:: C.gcp_region,
    name:: C.name,
    description:: 'Research Core/Central/Global Cluster',
    num_nets:: 1,

    core_only:: true,

    // NOTE(mattm): Workaround for hand-created immutable field.
    Outer+: {
      cluster+: {
        spec+: {
          initialNodeCount: 0,
        },
      },
    },
  },

  stage0:: self.cluster.Outer,
  stage4:: {
    cluster_inner: $.cluster.Inner,

    image_pull: C.k8s.GCP.IAM.PolicyMember + {
      local repo = (import 'gcp-global.jsonnet')().repos.us,

      name:: $.cluster.Outer.node_sa.name + '.' + repo.metadata.name + '.reader',
      namespace:: $.cluster.host_namespace,
      spec+: {
        resourceRef: {
          kind: repo.kind,
          external: repo.ref,
        },
        member: 'serviceAccount:' + $.cluster.Outer.node_sa.email,
        role: 'roles/artifactregistry.reader',
      },
    },
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Stage1: ConfigConnecter Outer (IAM ServiceAccount and Roles)
  // Stage2: ConfigConnecter Install
  // Stage3: ConfigConnector Config
  //

  config_connector:: config_connector.InstallGlobal(C.k8s, contexts=[
    {
      H: H,
      C: C,
      namespace: $.cluster.host_namespace,
      gcp_target_project: C.gcp_project,
    },
  ]),
  stage1:: self.config_connector.Outer,
  stage2:: svcTolerate(self.config_connector.Install),
  stage3:: self.config_connector.Config,

  //////////////////////////////////////////////////////////////////////////////
  //
  // Stage5: Marines Resources
  //

  stage5:: C.k8s + {
    local k = self,

    ////////////////////////////////////////////////////////////////////////////
    //
    // Standard Namespaces ('aug-system' and '{cluster_name}')
    //

    // Default namespace, may go unused or be useful to tmp stuff.
    main_ns: k.Namespace + {
      name:: C.main_namespace,
    },

    // Augment-specific namespace, like kube-system.
    aug_system_ns: k.Namespace + {
      name:: 'aug-system',
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // RBAC (admin)
    //

    admin_rb: k.ClusterRoleBinding + {
      name:: 'aug:cluster-admins',
      role_name:: 'cluster-admin',  // TODO(mattm): Maybe something slightly less powerfull, or scoped to a list of namespaces.
      groups:: ['<EMAIL>'],
    },

    root_rb: k.ClusterRoleBinding + {
      name:: 'aug:cluster-root',
      role_name:: 'cluster-admin',
      groups:: ['<EMAIL>'],
    },

    //////////////////////////////////////////////////////////////////////////////
    //
    // Bitnami Sealed Secrets
    //
    // This runs in the `kube-system` namespace to make it easy to run the `kubeseal` CLI.
    //

    bitnami_ss: {
      release: svcTolerate(bitnami_ss(C.k8s, name='bitnami-ss0', values={})),
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // GCP ConfigConnector
    //

    GCP+:: {
      Object+:: {
        project_id:: C.gcp_project,
        namespace:: $.cluster.host_namespace,
        metadata+: {
          labels: {},
        },
      },
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // DNS Zone
    //
    // This a zone scoped to this cluster. We need to setup the zone itself, plus
    // the subdomain records in the parent zone.
    //

    dns: k.GCP.DNS.ManagedZone + {
      spec+: {
        dnsName: C.name + '.r.augmentcode.com.',
        description: 'Cluster-scoped zone for ' + C.name + '.',
        visibility: self.VISIBILITY.PUBLIC,
        dnssecConfig+: {
          state: 'off',
        },
      },
    },

    dns_parent_ns: k.GCP.DNS.RecordSet + {
      spec+: {
        managedZoneRef+: { external: 'r-augmentcode-com' },
        name: k.dns.spec.dnsName,
        type: 'NS',
        rrdatas: [
          'ns-cloud-d1.googledomains.com.',
          'ns-cloud-d2.googledomains.com.',
          'ns-cloud-d3.googledomains.com.',
          'ns-cloud-d4.googledomains.com.',
        ],
      },
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // External DNS
    //
    // This monitors Services (LoadBalancer) and Ingresses and creates A-records
    // in DNS. It's configured with an IAM ServiceAccount which can read all zones
    // in the project (to find the right one) and then write to zones where we want
    // to manage records.
    //

    external_dns: svcTolerate(external_dns(C.k8s, C.gcp_project, zones=[k.dns.spec.dnsName, 'r.augmentcode.com.'], namespace=k.aug_system_ns.metadata.name, owner_id=std.asciiLower(C.name)) + {
      sa+: {
        metadata+: {
          annotations+: {
            'iam.gke.io/gcp-service-account': '%s@%s.iam.gserviceaccount.com' % [k.external_dns_sa.sa.metadata.name, C.gcp_project],
          },
        },
      },
    }),

    external_dns_sa: {
      local s = self,
      sa: k.GCP.IAM.ServiceAccount + {
        name:: C.name + '-external-dns',
        description:: 'K8s External DNS',
      },
      sa_workload_identity: k.GCP.IAM.PolicyMember + {
        name:: s.sa.metadata.name + '.workload-identity-user',
        spec+: {
          resourceRef: s.sa.localKindRef,
          member: 'serviceAccount:%s.svc.id.goog[%s/%s]' % [C.gcp_project, k.external_dns.sa.metadata.namespace, k.external_dns.sa.metadata.name],
          role: 'roles/iam.workloadIdentityUser',
        },
      },
      sa_role_project_reader: k.GCP.IAM.PolicyMember + {
        name:: s.sa.metadata.name + '.' + C.gcp_project + '.reader',
        spec+: {
          resourceRef: {
            kind: 'Project',
            external: 'project/' + C.gcp_project,
          },
          memberFrom: self.memberFromSA(s.sa),
          role: 'roles/dns.reader',
        },
      },
      sa_role_zone_admin: k.GCP.IAM.PolicyMember + {
        name:: s.sa.metadata.name + '.' + C.name + '.admin',
        spec+: {
          resourceRef: k.dns.localKindRef,
          memberFrom: self.memberFromSA(s.sa),
          role: 'roles/dns.admin',
        },
      },
      // Access to r.augmentcode.com
      sa_role_zone_admin_r: k.GCP.IAM.PolicyMember + {
        name:: s.sa.metadata.name + '.' + 'r.admin',
        spec+: {
          resourceRef: {
            kind: 'DNSManagedZone',
            external: 'projects/' + C.gcp_project + '/managedZones/r-augmentcode-com',
          },
          memberFrom: self.memberFromSA(s.sa),
          role: 'roles/dns.admin',
        },
      },
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // Cert Manager
    //
    // We configure a dns01 solver for the above zone(s). The dns01 solver is used
    // instead of http01 because 1) only dns01 supports wildcards and 2) it avoids a
    // dependency on the ingress controller.

    cert_manager: {
      local cm = self,
      release: svcTolerate(cert_manager(C.k8s, name='cert-manager0', namespace='cert-manager', values={
        serviceAccount+: {
          annotations+: {
            'iam.gke.io/gcp-service-account': '%s@%s.iam.gserviceaccount.com' % [cm.dns01_sa.metadata.name, C.gcp_project],
          },
        },
      })),
      // ServiceAccount for dns01 solver.
      dns01_sa: k.GCP.IAM.ServiceAccount + {
        name:: C.name + '-dns01-solver',
        description:: 'K8s CertManager (dns01 solver)',
      },
      // Workload Identity binding.
      dns01_workload_identity: k.GCP.IAM.PolicyMember + {
        name:: cm.dns01_sa.metadata.name + '.workload-identity-user',
        spec+: {
          resourceRef: cm.dns01_sa.localKindRef,
          member: 'serviceAccount:' + C.gcp_project + '.svc.id.goog[cert-manager/cert-manager0]',
          role: 'roles/iam.workloadIdentityUser',
        },
      },
      // Read all zones, so that the solver can find the correct zone.
      dns01_reader_role: k.GCP.IAM.PolicyMember + {
        name:: cm.dns01_sa.metadata.name + '.' + C.gcp_project + '.reader',
        spec+: {
          resourceRef: {
            kind: 'Project',
            external: 'project/' + C.gcp_project,
          },
          memberFrom: self.memberFromSA(cm.dns01_sa),
          role: 'roles/dns.reader',
        },
      },
      // Access to r.augmentcode.com
      dns01_role_main: k.GCP.IAM.PolicyMember + {
        name:: cm.dns01_sa.metadata.name + '.r.admin',
        spec+: {
          resourceRef: {
            kind: 'DNSManagedZone',
            external: 'projects/' + C.gcp_project + '/managedZones/r-augmentcode-com',
          },
          memberFrom: self.memberFromSA(cm.dns01_sa),
          role: 'roles/dns.admin',
        },
      },
      // Access to {cluster}.r.augmentcode.com
      dns01_role_cluster: k.GCP.IAM.PolicyMember + {
        name:: cm.dns01_sa.metadata.name + '.' + C.name + '.admin',
        spec+: {
          resourceRef: k.dns.localKindRef,
          memberFrom: self.memberFromSA(cm.dns01_sa),
          role: 'roles/dns.admin',
        },
      },
      dns01_issuer: k.ClusterIssuer + {
        name:: 'letsencrypt-prod',
        spec+: {
          acme: {
            privateKeySecretRef: {
              name: 'letsencrypt-account-key',
            },
            server: 'https://acme-v02.api.letsencrypt.org/directory',
            solvers: [
              {
                dns01: {
                  cloudDNS: {
                    project: C.gcp_project,
                  },
                },
              },
            ],
          },
        },
      },
    },

    ////////////////////////////////////////////////////////////////////////////
    //
    // nginx Ingress Controller
    //
    // We enforce http->https redirect at the controller level, so it's not possible
    // for users to configure a plain HTTP ingress. We do *not* enforce auth here, that
    // happens on a per-ingress basis.
    //

    ingress_nginx: {
      release: svcTolerate(ingress_nginx(C.k8s, name='nginx0')),
    },

  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // Selectively return stages
  //

  ret:: [
    $['stage%d' % [num]]
    for num in stages
  ],

}.ret

local clusters = import '../clusters.jsonnet';
local gke_cluster = import 'tmpl/gke_cluster.jsonnet';
local lib = import 'tmpl/lib.jsonnet';

// NOTE(marcmac,mattm): We're bring this up for Services, we don't bother with most of the
// config we normally have. There are just two stages:
//
// Outer (hosted on gcp-core0's ConfigConnector):
//   Stage0: The GKE Cluster Outer (Cluster, Networks, Node SA, NodePools)
// Inner (internal K8s resources):
//   Stage4: The GKE Cluster Inner (StorageClass, NetDev)

function(aH=null, cluster='prod-gsc', stages=[4]) {
  local H = if aH == null then clusters.cluster('gcp-core0') else aH,
  local C = clusters.cluster(cluster),

  // Helper wrapper to apply the 'svc' NoSchedule toleration.
  local svcTolerate = function(x) lib.ApplyToleration(x, 'r.augmentcode.com/pool-type', 'svc'),

  //////////////////////////////////////////////////////////////////////////////
  //
  // Stage0: GKE Cluster Outer (Cluster, Networks, Node SA, NodePools)
  // Stage4: GKE Cluster Inner (StorageClass, NetDev, ...)
  //

  cluster:: gke_cluster + {
    host_cluster: H.name,
    host_namespace: 'cc-' + C.name,

    name:: C.name,
    project_id:: C.gcp_project,
    location:: C.gcp_region,
    primary_zone:: 'us-central1-a',
    description:: 'Production %s cluster in %s.' % [C.name, C.gcp_region],
    num_nets:: 9,

    // The H100 pool is managed as part of the prod services ConfigConnector
    // h100_reservation:: 'projects/augment-parent-gsc/reservations/h100-gsc-0',
    // h100_shards:: 1,
    svc_pool:: false,
    cpu_pool:: false,
    l4_pool:: false,
    t4_pool:: false,
  },
  stage0:: {
    cluster_outer: $.cluster.Outer + {
      cluster+: {
        spec+: {
          monitoringConfig+: {
            enableComponents: ['SYSTEM_COMPONENTS'],
          },
        },
      },
    },

    vpc_peer: H.k8s.GCP.Compute.NetworkPeering + {
      local p = self,
      name:: p.src.name + '-' + p.dst.project_id + '-' + p.dst.name,
      namespace:: $.cluster.host_namespace,

      src:: $.cluster.Outer.vpc0.net,
      dst:: H.k8s.GCP.Compute.Network + {
        name:: 'default',
        project_id: 'system-services-prod',
      },
      spec+: {
        networkRef: { external: p.src.ref },
        peerNetworkRef: { external: p.dst.ref },
      },
    },
  },

  stage4:: {
    cluster_inner: $.cluster.Inner,
  },

  ret:: [
    $['stage%d' % [num]]
    for num in stages
  ],

}.ret

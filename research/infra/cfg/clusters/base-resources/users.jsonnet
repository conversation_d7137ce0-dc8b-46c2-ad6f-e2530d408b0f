local clusters = import '../clusters.jsonnet';

local _user = function(C, name) C.k8s + {
  BaseObject+:: {
    name:: 'aug-user-' + name,
    metadata+: {
      labels+: {
        'aug.user': name,
      },
    },
  },

  // A user is represented by a ServiceAccount in the "sys_namespace".
  //  - cw: tenant-augment-eng
  //  - cw-east4: aug-system
  // The SA will have an imagePullSecret for the local registry in the cluster, if defined.
  sa: $.ServiceAccount + std.prune({
    namespace:: C.sys_namespace,
    imagePullSecrets: [{
      name: C.registry_img_pull_sec,
    }],
  }),

  // A user may have RoleBindings to a Role in the "main_namespace".
  //  - cw: tenant-augment-eng (aug-eng-writers, aug-readers)
  //  - cw-east4: cw-east4 (none needed)
  rbs: [
    $.RoleBinding + {
      role_ref_kind:: 'Role',
      name+:: '-' + role,
      namespace:: C.main_namespace,
      role_name:: role,
      sas+:: [$.sa],
    }
    for role in C.user_roles
  ],

  // A user may have RoleBindings to a ClusterRole in the "main_namespace".
  //  - cw: tenant-augment-eng (none available)
  //  - cw-east4: cw-east4 (edit)
  crbs: [
    $.RoleBinding + {
      role_ref_kind:: 'ClusterRole',
      name+:: '-' + role,
      namespace:: C.main_namespace,
      role_name:: role,
      sas+:: [$.sa],
    }
    for role in C.user_cluster_roles
  ],

  // A user may have ClusterRoleBindings to a ClusterRole (no namespace).
  //  - cw: (none available)
  //  - cw-east4: (view)
  ccrbs: [
    $.ClusterRoleBinding + {
      role_ref_kind:: 'ClusterRole',
      name+:: '-' + role,
      role_name:: role,
      sas+:: [$.sa],
    }
    for role in C.user_cluster_cluster_roles
  ],
};

function(cluster, users=[]) {
  local C = clusters.cluster(cluster),
  users: [_user(C, u) for u in users],
}

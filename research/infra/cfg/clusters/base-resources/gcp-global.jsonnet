// GCP Resources not tied to a specific cluster, but are hosted by a "core" cluster.
local clusters = import '../clusters.jsonnet';
local config_connector = import 'tmpl/config-connector.jsonnet';

function(cluster='gcp-core0', namespace='cc-research') local C = clusters.cluster(cluster); C.k8s + {

  //////////////////////////////////////////////////////////////////////////////
  //
  // ConfigConnector
  //

  config_connector: config_connector.Context(C, C, namespace, C.gcp_project, host_namespace='cc-' + C.name),

  Object+:: {
    namespace:: namespace,
    metadata+: {
      labels: {},  // reset labels
    },
  },

  GCP+:: {
    Object+:: {
      project_id:: C.gcp_project,
    },
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // DNS
  //

  r_zone: $.GCP.DNS.ManagedZone + {
    local obj = self,
    name:: 'r-augmentcode-com',
    spec+: {
      dnsName: 'r.augmentcode.com.',
      description: 'Research zone for exposed services.',
      visibility: self.VISIBILITY.PUBLIC,
      dnssecConfig+: {
        state: 'off',
      },
    },
  },

  records: [
    $.GCP.DNS.RecordSet + {
      name:: 'r-augi-console',
      spec+: {
        managedZoneRef: self.zoneRefFrom($.r_zone),
        name: 'augi-console.r.augmentcode.com.',
        type: 'CNAME',
        rrdatas: ['augi-console.tenant-augment-eng.coreweave.cloud.'],
      },
    },
  ],

  //////////////////////////////////////////////////////////////////////////////
  //
  // Docker Registries
  //

  repos: {
    us: self._base + {
      name:: 'docker-' + self.spec.location,
      spec+: {
        location: 'us-central1',
      },
    },

    _base:: $.GCP.ArtifactRegistry.Repository + {
      spec+: {
        description: 'Augment Research Image Mirror',
        format: self.FORMAT.DOCKER,
        dockerConfig: {
          immutableTags: false,
        },
        cleanupPolicyDryRun: false,
      },
    },
  },

  registry_reader: {
    local s = self,

    sa: $.GCP.IAM.ServiceAccount + {
      name:: 'registry-read-account',
      description:: 'Image Registry Reader.',
    },

    sa_key: $.GCP.IAM.ServiceAccountKeyFor(self.sa),

    acls: [
      $.GCP.IAM.PolicyMember + {
        name:: s.sa.name + '.' + repo.name + '.reader',
        spec+: {
          resourceRef: repo.localKindRef,
          memberFrom: self.memberFromSA(s.sa),
          role: 'roles/artifactregistry.reader',
        },
      }
      for repo in std.objectValues($.repos)
    ],

    // resourcemanager.projects.get needed at the project level for the v2/_catalog API per:
    // https://stackoverflow.com/questions/********/what-iam-role-is-required-for-using-the-docker-registry-catalog-vs-api-with-gcp
    catalog_acl: $.GCP.IAM.PolicyMember + {
      name:: s.sa.name + '.' + C.gcp_project + '.' + 'browser',
      spec+: {
        resourceRef: {
          kind: 'Project',
          external: 'project/' + C.gcp_project,
        },
        memberFrom: self.memberFromSA(s.sa),
        role: 'roles/browser',
      },
    },
  },

  registry_syncer: {
    local s = self,

    sa: $.GCP.IAM.ServiceAccount + {
      name:: 'registry-sync-account',
      description:: 'Image Registry Syncer.',
    },

    sa_key: $.GCP.IAM.ServiceAccountKeyFor(self.sa),

    acls: [
      $.GCP.IAM.PolicyMember + {
        name:: s.sa.name + '.' + repo.name + '.writer',
        spec+: {
          resourceRef: repo.localKindRef,
          memberFrom: self.memberFromSA(s.sa),
          role: 'roles/artifactregistry.writer',
        },
      }
      for repo in std.objectValues($.repos)
    ],
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // <EMAIL>
  //

  r_user_sa_auto: {
    local grp = self,

    google_grp: $.GCP.CloudIdentity.Group + {
      email:: '<EMAIL>',
      group_id:: '01tuee74150hs7x',  // NOTE(mattm): Recorded after creation for convenience.
    },

    members: [
      $.GCP.CloudIdentity.Membership + {
        name:: 'mattm.r-user-sa-auto.owner',
        group_obj:: grp.google_grp,
        member_email:: '<EMAIL>',
        role:: self.ROLE.OWNER,
      },
      $.GCP.CloudIdentity.Membership + {
        name:: 'marcmac.r-user-sa-auto.owner',
        group_obj:: grp.google_grp,
        member_email:: '<EMAIL>',
        role:: self.ROLE.OWNER,
      },
      $.GCP.CloudIdentity.Membership + {
        name:: 'gcp-us1.r-user-sa-auto.manager',
        group_obj:: grp.google_grp,
        member_email:: '<EMAIL>',
        role:: self.ROLE.MANAGER,
      },
    ],
  },

  l4_res: $.GCP.Compute.Reservation + {
    name:: self.spec.zone + '-l4-x4-0',
    spec+: {
      zone: 'us-central1-a',
      specificReservationRequired: true,
      specificReservation+: {
        count: 8,
        instanceProperties+: {
          machineType: 'g2-standard-48',
          guestAccelerators: [{
            acceleratorCount: 4,
            acceleratorType: 'nvidia-l4',
          }],
          localSsds+: [{
            diskSizeGb: 375,
            interface: 'NVME',
          } for i in std.range(1, 4)],
        },
      },
    },
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // "Hosted" GCP ConfigConnector items for other clusters.
  //

  hosted: {
    'gcp-us1': {
      local HC = clusters.cluster('gcp-us1'),
      // For gcp-core0 acting on gcp-us1.
      config_connector: config_connector.Context(C, C, 'cc-' + HC.name, HC.gcp_project, host_namespace='cc-' + C.name),
      // The cluster + gcp-us1 acting on gcp-us1.
      stages: (import 'gcp-us1.jsonnet')(C, stages=[0, 1]),
    },

    /*
    // NOTE(mattm): Commented out to avoid production issues since we don't actively
    // manage this.
    'prod-gsc': {
      local HC = clusters.cluster('prod-gsc'),
      // For gcp-core0 acting on gcp-us1.
      config_connector: config_connector.Context(C, C, 'cc-' + HC.name, HC.gcp_project, host_namespace='cc-' + C.name),
      // The cluster.
      stages: (import 'prod-gsc.jsonnet')(C, stages=[0]),
    },
    */

    'gcp-agent0': {
      local HC = clusters.cluster('gcp-agent0'),
      // For gcp-core0 acting on this cluster.
      config_connector: config_connector.Context(C, C, 'cc-' + HC.name, HC.gcp_project, host_namespace='cc-' + C.name),
      // The cluster + gcp-agent0 acting on itself.
      stages: (import 'gcp-agent0.jsonnet')(C, stages=[0, 1]),
    },

    'gcp-prod-agent0': {
      local HC = clusters.cluster('gcp-prod-agent0'),
      // For gcp-core0 acting on this cluster.
      config_connector: config_connector.Context(C, C, 'cc-' + HC.name, HC.gcp_project, host_namespace='cc-' + C.name),
      // The cluster + gcp-prod-agent0 acting on itself.
      stages: (import 'gcp-prod-agent0.jsonnet')(C, stages=[0, 1]),
    },

    'gcp-eu-w4-prod-agent0': {
      local HC = clusters.cluster('gcp-eu-w4-prod-agent0'),
      // For gcp-core0 acting on this cluster.
      config_connector: config_connector.Context(C, C, 'cc-' + HC.name, HC.gcp_project, host_namespace='cc-' + C.name),
      // The cluster + gcp-eu-w4-prod-agent0 acting on itself.
      stages: (import 'gcp-eu-w4-prod-agent0.jsonnet')(C, stages=[0, 1]),
    },

    'cw-east4': {
      local HC = clusters.cluster('cw-east4'),
      // For gcp-core0 acting on cw-east4,
      config_connector: config_connector.Context(C, C, 'cc-' + HC.name, C.gcp_project, host_namespace='cc-' + C.name),

      external_dns: C.k8s + {
        local s = self,
        GCP+:: {
          Object+:: {
            namespace:: 'cc-' + HC.name,
            metadata+: {
              labels: {},  // reset
            },
          },
        },
        dns: s.GCP.DNS.ManagedZone + {
          spec+: {
            dnsName: HC.name + '.r.augmentcode.com.',
            description: 'Cluster-scoped zone for ' + HC.name + '.',
            visibility: self.VISIBILITY.PUBLIC,
            dnssecConfig+: {
              state: 'off',
            },
          },
        },

        dns_parent_ns: s.GCP.DNS.RecordSet + {
          spec+: {
            managedZoneRef+: { external: 'r-augmentcode-com' },
            name: s.dns.spec.dnsName,
            type: 'NS',
            rrdatas: [
              'ns-cloud-c1.googledomains.com.',
              'ns-cloud-c2.googledomains.com.',
              'ns-cloud-c3.googledomains.com.',
              'ns-cloud-c4.googledomains.com.',
            ],
          },
        },
        sa: s.GCP.IAM.ServiceAccount + {
          name:: HC.name + '-external-dns',
          description:: 'K8s External DNS',
        },
        sa_key: s.GCP.IAM.ServiceAccountKeyFor(s.sa),
        sa_role_project_reader: s.GCP.IAM.PolicyMember + {
          name:: s.sa.metadata.name + '.' + C.gcp_project + '.reader',
          spec+: {
            resourceRef: {
              kind: 'Project',
              external: 'project/' + C.gcp_project,
            },
            memberFrom: self.memberFromSA(s.sa),
            role: 'roles/dns.reader',
          },
        },
        sa_role_zone_admin: s.GCP.IAM.PolicyMember + {
          name:: s.sa.metadata.name + '.' + C.name + '.admin',
          spec+: {
            resourceRef: s.dns.localKindRef,
            memberFrom: self.memberFromSA(s.sa),
            role: 'roles/dns.admin',
          },
        },
        ray: {
          local ray = self,
          sa: s.GCP.IAM.ServiceAccount + {
            name:: HC.name + '-ray',
            description:: 'K8s Ray Service Account',
          },
          sa_key: s.GCP.IAM.ServiceAccountKeyFor(ray.sa),
          acls: [
            [
              s.GCP.IAM.PolicyMember + {
                name:: ray.sa.metadata.name + '.' + bkt + '.storage-bucketviewer',
                spec+: {
                  resourceRef: {
                    kind: 'StorageBucket',
                    external: bkt,
                  },
                  memberFrom: self.memberFromSA(ray.sa),
                  role: 'roles/storage.bucketViewer',
                },
              },
              s.GCP.IAM.PolicyMember + {
                name:: ray.sa.metadata.name + '.' + bkt + '.storage-objectuser',
                spec+: {
                  resourceRef: {
                    kind: 'StorageBucket',
                    external: bkt,
                  },
                  memberFrom: self.memberFromSA(ray.sa),
                  role: 'roles/storage.objectUser',
                },
              },
            ]
            for bkt in ['gcp-us1-user', 'gcp-us1-checkpoints']
          ],
        },
      },
    },
  },
}

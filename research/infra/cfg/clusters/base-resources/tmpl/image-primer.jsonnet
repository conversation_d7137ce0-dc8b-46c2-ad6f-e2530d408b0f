// Image Primer
//
// This takes advantage of :main tags and re-pulls every `cycle`. We target every
// node except the gke pool because we try not to run our own images on those.
// There is a small inefficiency because we don't have any use-cases for running CPU
// images on GPU nodes, but we have occasionally run GPU images on CPU nodes.
//
// A GKE-specific alternative could be to use the "Image Streaming" feature, which
// mounts container images from NFS. The advantage to NFS streaming is startup time
// for freshly built images.
//
function(C, name='image-primer', cycle='5m') C.k8s + {

  BaseLabels+:: {
    'aug.service': name,
  },
  Object+:: {
    name:: name,
    namespace:: C.sys_namespace,
  },

  daemon: $.DaemonSet + {
    spec+: {
      template+: {
        spec+: {
          enableServiceLinks: false,
          restartPolicy: 'Always',
          tolerations: [
            {
              key: 'r.augmentcode.com/pool-type',
              operator: 'Exists',
            },
            {
              key: 'nvidia.com/gpu',
              operator: 'Exists',
            },
          ],
          initContainers: [
            $.Container + {
              restartPolicy: 'Always',  // make this a sidecar container
              name: std.strReplace(i, '_', '-'),
              image: C.images_main[i],
              imagePullPolicy: 'Always',
              command: ['/bin/sleep', cycle],
            }
            for i in [
              'cpu',
              'gpu',
              'devpod_cpu',
              'devpod_gpu',
            ]
          ],
          containers: [
            $.Container + {
              name: 'sleep',
              image: 'busybox:latest',
              command: ['/bin/sleep', 'infinity'],
            },
          ],
        },
      },
    },
  },

}

local k8s_lib = import '../../../../../../infra/cfg/lib/k8s/k8s.jsonnet';
local lib = import 'lib.jsonnet';
local vendor = {
  '1.24.0': importbin '../helm/haproxy-1.24.0.tgz',
};

function(k8s=k8s_lib, name='haproxy0', namespace='haproxy', values={}, probe_port=80, version='1.24.0') k8s + {
  helmData:: vendor[version],

  release: lib.ParseHelmChart(self.helmData, name, namespace, values={
    kind: 'Deployment',
    replicaCount: 3,
    service+: {
      type: 'LoadBalancer',
      externalTrafficPolicy: 'Local',
    },
  } + (
    if probe_port != null then {
      _baseProbe:: {
        successThreshold: 1,
        initialDelaySeconds: 0,
        timeoutSeconds: 1,
        tcpSocket: {
          port: probe_port,
        },
      },
      livenessProbe: self._baseProbe + {
        failureThreshold: 3,
        periodSeconds: 10,
        initialDelaySeconds: 0,
      },
      readinessProbe: self._baseProbe + {
        failureThreshold: 3,
        initialDelaySeconds: 0,
        periodSeconds: 10,
      },
      startupProbe: self._baseProbe + {
        failureThreshold: 20,
        periodSeconds: 1,
        initialDelaySeconds: 0,
      },
    } else {}
  ) + values),

}

local kubecfg = import 'kubecfg.libsonnet';

{
  ApplyToleration:: function(obj, key, value, effect='NoSchedule')
    if std.isArray(obj) then [$.ApplyToleration(o, key, value, effect) for o in obj]
    else if !std.isObject(obj) then obj
    else if !std.objectHas(obj, 'kind') then std.mapWithKey(function(fname, fval) $.ApplyToleration(fval, key, value, effect), obj)
    else if std.member(['Deployment', 'StatefulSet', 'Job'], obj.kind) then obj + {
      spec+: {
        template+: {
          spec+: {
            tolerations+: [{
              effect: effect,
              key: key,
              value: value,
            }],
          },
        },
      },
    }
    else obj,

  ParseHelmChart:: function(data, name, namespace, values)
    local orig = kubecfg.parseHelmChart(data, name, namespace, values);
    local clusterkinds = [
      'ClusterRole',
      'ClusterRoleBinding',
      'CustomResourceDefinition',
      'ValidatingWebhookConfiguration',
      'MutatingWebhookConfiguration',
      'IngressClass',
    ];
    {
      [kv.key]: [
        obj + {
          apiVersion: if super.apiVersion == 'policy/v1beta1' then 'policy/v1' else super.apiVersion,
          metadata: std.prune(super.metadata + if !std.member(clusterkinds, obj.kind) then {} else {
            namespace: null,
          }),
        }
        for obj in kv.value
        if obj != null
      ]
      for kv in std.objectKeysValues(orig)
    },
}

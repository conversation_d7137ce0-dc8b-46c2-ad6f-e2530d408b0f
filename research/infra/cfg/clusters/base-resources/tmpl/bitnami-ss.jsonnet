local lib = import 'lib.jsonnet';
local vendor = {
  '2.4.5': importbin '../helm/sealed-secrets-2.4.5.tgz',
};

function(k8s, name='bitnami-ss0', namespace='kube-system', values={}, version='2.4.5') k8s + {
  helmData:: vendor[version],

  ns: if namespace != 'kube-system' then k8s.Namespace + {
    name:: namespace,
  },

  release: lib.<PERSON><PERSON>(self.helmData, name, namespace, values={
    fullnameOverride: 'sealed-secrets-controller',
    resources+: {
      requests+: {
        cpu: '2',
        memory: '2Gi',
      },
    },
  } + values),

}

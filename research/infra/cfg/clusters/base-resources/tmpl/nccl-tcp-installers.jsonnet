// https://cloud.google.com/kubernetes-engine/docs/how-to/gpu-bandwidth-gpudirect-tcpx

function(tcpx=true, tcpxo=true, nri=true) {
  tcpx: if tcpx then std.parseYaml(importstr 'nccl-tcpx-installer.yaml') + {
    // Version update required for llama 3.1
    spec+: {
      template+: {
        spec+: {
          initContainers: [
            ic + {
              image: if super.image != 'us-docker.pkg.dev/gce-ai-infra/gpudirect-tcpx/nccl-plugin-gpudirecttcpx-dev:v3.1.9' then super.image else 'us-docker.pkg.dev/gce-ai-infra/gpudirect-tcpx/nccl-plugin-gpudirecttcpx-dev:v3.1.10-2.19.4-12.0',
            }
            for ic in super.initContainers
          ],
        },
      },
    },
  },

  tcpxo: if tcpxo then std.parseYaml(importstr 'nccl-tcpxo-installer.yaml'),

  nri: if nri then std.parseYaml(importstr 'nri-device-injector.yaml'),
}

local yaml = std.parseYaml(importstr 'configconnector-operator.yaml');

// ConfigConnecter is broken into two Global and Per-Context to make it easy to insert
// Contexts into existing clusters. Additionally, each of the above is broken into three
// stages:
//  - Global:
//    - Outer Stage: <empty>
//    - Install Stage: The upstream released YAML file containg the initial CRDs and "operator" context.
//    - Config Stage: The global ConfigConnector resource which configures namespaced mode.
//  - Per-Context:
//    - Outer Stage: GCP Prereqs for ConfigConnectorContext permissions (SA, WorkloadIdentity, IAM Roles).
//    - Install Stage: <empty>
//    - Config Stage: The Namespace and ConfigConnectorContext which uses the SA from Outer.
//
{

  // InstallGlobal returns resources for the "Global" group above. For convenience, `contexts` are included,
  // resources from the "Per-Context" group are also included.
  InstallGlobal:: function(k8s, name='config-connector', contexts=[]) k8s + {
    local G = self,

    BaseLabels+:: {
      'aug.service': name,
    },

    Outer: [ctx.Outer for ctx in G._Contexts],

    // Raw configconnector-operator.yaml from Google, with some slight modifications.
    Install: [
      obj + {
        // Prune original metadata because yaml contains some null CreationTimestamps which result in eternal diffs.
        // Add in our own BaseLabels.
        metadata: std.prune(super.metadata) + {
          labels+: G.BaseLabels,
        },
      } + if obj.kind == 'StatefulSet' then {
        spec+: {
          template+: {
            metadata+: {
              labels+: G.BaseLabels,
            },
          },
        },
      } else {}
      for obj in yaml
    ],

    Config: {
      // Global Config -- put ConfigConnector in namespaced mode.
      cc: G.ConfigConnector + {
        mode:: G.CC_MODE.NAMESPACED,
      },
      ctxs: [ctx.Config for ctx in G._Contexts],
    },

    // For convenience.
    _Contexts:: [$.Context(ctx.H, ctx.C, ctx.namespace, ctx.gcp_target_project, std.get(ctx, 'host_namespace')) for ctx in contexts],
  },

  Context:: function(H, C, namespace, gcp_target_project, host_namespace=null) {
    local X = self,

    Outer: H.k8s + {
      local O = self,

      GCP+:: {
        Object+:: {
          project_id:: H.gcp_project,
          namespace:: if host_namespace != null then host_namespace else namespace,
        },
      },

      // The GCP ServiceAccount that the ConfigConnector runs as.
      sa: O.GCP.IAM.ServiceAccount + {
        name:: (C.name + '-' + namespace)[:30],
        description:: 'Config Connector',
        project_id:: if H.name == C.name then H.gcp_project else gcp_target_project,
      },

      // GCP Workload Identity binding between ConfigConnector GCP ServiceAccount <> K8s ServiceAccount.
      workload_binding: O.GCP.IAM.PolicyMember + {
        name:: O.sa.metadata.name + '.workload-identity-user',
        spec+: {
          resourceRef: O.sa.localKindRef,
          member: 'serviceAccount:' + C.gcp_project + '.svc.id.goog[cnrm-system/cnrm-controller-manager-' + namespace + ']',
          role: 'roles/iam.workloadIdentityUser',
        },
      },

      // GCP IAM Roles for for the ConfigConnector ServiceAccount:
      //  - Project Editor (for most access).
      //  - IAM Security Admin (for adding IAM Policy Members).
      iam_roles: [
        O.GCP.IAM.PolicyMember + {
          name:: O.sa.metadata.name + '.' + gcp_target_project + '.editor',
          spec+: {
            resourceRef+: {
              kind: 'Project',
              external: 'project/' + gcp_target_project,
            },
            memberFrom: self.memberFromSA(O.sa),
            role: 'roles/editor',
          },
        },
        O.GCP.IAM.PolicyMember + {
          name:: O.sa.metadata.name + '.' + gcp_target_project + '.security-admin',
          spec+: {
            resourceRef+: {
              kind: 'Project',
              external: 'project/' + gcp_target_project,
            },
            memberFrom: self.memberFromSA(O.sa),
            role: 'roles/iam.securityAdmin',
          },
        },
      ],
    },

    Config: C.k8s + {
      local C = self,

      ns: C.Namespace + {
        name:: namespace,
        metadata+: {
          annotations+: {
            'cnrm.cloud.google.com/project-id': gcp_target_project,
          },
        },
      },

      cc_ctx: C.ConfigConnectorContext + {
        namespace:: C.ns.metadata.name,
        googleServiceAccount:: X.Outer.sa.email,
      },
    },

  },

}

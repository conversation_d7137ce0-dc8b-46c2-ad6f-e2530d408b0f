local lib = import 'lib.jsonnet';
local vendor = {
  '1.15.3': importbin '../helm/cert-manager-v1.15.3.tgz',
};

function(k8s, name='cert-manager0', namespace='cert-manager', values={}, version='1.15.3') k8s + {
  helmData:: vendor[version],

  ns: k8s.Namespace + {
    name:: namespace,
  },

  release: lib.Parse<PERSON>elm<PERSON>(self.helmData, name, namespace, values={
    crds: {
      enabled: true,
      keep: false,
    },
    replicaCount: 3,
    podDisruptionBudget: {
      enabled: true,
    },
    enableCertificateOwnerRef: true,
    dns01RecursiveNameservers: '8.8.8.8:53,8.8.4.4:53',
  } + values),

}

function(C, name='aug-sysctl') C.k8s + {

  BaseLabels+:: {
    'aug.service': name,
  },
  Object+:: {
    name:: name,
    namespace:: C.sys_namespace,
  },

  daemon: $.DaemonSet + {
    spec+: {
      template+: {
        spec+: {
          enableServiceLinks: false,
          restartPolicy: 'Always',
          tolerations: [
            { operator: 'Exists' },  // empty toleration to run everywhere
          ],
          initContainers: [
            $.Container + {
              name: name,
              image: 'busybox:latest',
              command: ['/bin/sh', '-c'],
              args: [
                |||
                  set -xe
                  sysctl -w fs.inotify.max_user_watches=524288
                |||,
              ],
              securityContext: {
                privileged: true,
              },
            },
          ],
          containers: [
            $.Container + {
              name: 'sleep',
              image: 'busybox:latest',
              command: ['/bin/sleep', 'infinity'],
            },
          ],
        },
      },
    },
  },
}

local lib = import 'lib.jsonnet';
local vendor = {
  '4.12.1': importbin '../helm/ingress-nginx-4.12.1.tgz',
};

function(k8s, name='nginx0', namespace='ingress-nginx', values={}, version='4.12.1') k8s + {
  helmData:: vendor[version],

  ns: k8s.Namespace + {
    name:: namespace,
  },

  release: lib.<PERSON><PERSON>(self.helmData, name, namespace, values={
    controller+: {
      config+: {
        hsts: true,
        'ssl-redirect': true,
        'force-ssl-redirect': true,
      },
      replicaCount: 2,
      service+: {
        externalTrafficPolicy: 'Local',
      },
    },
  } + values),

}

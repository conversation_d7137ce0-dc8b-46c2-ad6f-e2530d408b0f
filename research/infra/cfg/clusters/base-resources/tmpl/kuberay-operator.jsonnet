local lib = import 'lib.jsonnet';
local vendor = {
  '1.3.2': importbin '../helm/kuberay-operator-1.3.2.tgz',
};

function(k8s, name='kuberay-operator', namespace='ray-system', values={}, version='1.3.2') k8s + {
  helmData:: vendor[version],

  ns: if namespace != 'kube-system' then k8s.Namespace + {
    name:: namespace,
  },

  release: lib.<PERSON>rse<PERSON>el<PERSON>(self.helmData, name, namespace, values={
    // Default values for the KubeRay Operator
    // These can be overridden by the values parameter
    resources+: {
      requests+: {
        cpu: '100m',
        memory: '128Mi',
      },
      limits+: {
        cpu: '500m',
        memory: '512Mi',
      },
    },
  } + values),

  view_cr: $.ClusterRole + {
    name:: 'kuberay-view',
    metadata+: {
      labels+: {
        'rbac.authorization.k8s.io/aggregate-to-view': 'true',
      },
    },
    rules+: [
      self.Rule + {
        apiGroups: ['ray.io'],
        resources: ['*'],
        verbs: self.READ_VERBS,
      },
    ],
  },

  edit_cr: $.ClusterRole + {
    name:: 'kuberay-edit',
    metadata+: {
      labels+: {
        'rbac.authorization.k8s.io/aggregate-to-edit': 'true',
      },
    },
    rules+: [
      self.Rule + {
        apiGroups: ['ray.io'],
        resources: ['*'],
        verbs: self.WRITE_VERBS_NO_COLLECTION,
      },
    ],
  },

  admin_cr: $.ClusterRole + {
    name:: 'kuberay-admin',
    metadata+: {
      labels+: {
        'rbac.authorization.k8s.io/aggregate-to-admin': 'true',
      },
    },
    rules+: [
      self.Rule + {
        apiGroups: ['ray.io'],
        resources: ['*'],
        verbs: self.WRITE_VERBS,
      },
    ],
  },
}

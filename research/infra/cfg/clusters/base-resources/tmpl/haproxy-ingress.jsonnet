local k8s_lib = import '../../../../../../infra/cfg/lib/k8s/k8s.jsonnet';
local lib = import 'lib.jsonnet';
local vendor = {
  '1.44.2': importbin '../helm/haproxy-ingress-1.44.2.tgz',
};

function(k8s=k8s_lib, name='haproxy-ingress0', namespace='haproxy-ingress', values={}, version='1.44.2') k8s + {
  helmData:: vendor[version],

  release: lib.ParseHelmChart(self.helmData, name, namespace, values={
    controller+: {
      enableServiceLinks: false,
      replicaCount: 3,  // values.yaml defaults to 2
      service+: {
        type: 'LoadBalancer',
        externalTrafficPolicy: 'Local',
      },
      defaultTLSSecret+: {
        enabled: false,
      },
    },
  } + values),

}

function(k8s, gcp_project, zones, sa_key=null, name='external-dns', namespace='aug-system', owner_id='default', version='v0.14.2') k8s + {
  BaseLabels+:: {
    'aug.service': name,
  },
  ClusterObject+:: {
    name:: name,
  },
  Object+:: {
    name:: name,
    namespace:: namespace,
  },

  sa: $.ServiceAccount + {
    // empty
  },

  role: $.ClusterRole + {
    rules: [
      {
        apiGroups: [''],
        resources: ['services', 'endpoints', 'pods', 'nodes'],
        verbs: $.READ_VERBS,
      },
      {
        apiGroups: ['networking.k8s.io'],
        resources: ['ingresses'],
        verbs: $.READ_VERBS,
      },
    ],
  },

  rb: $.ClusterRoleBinding + {
    role_name: $.role.metadata.name,
    sas: [$.sa],
  },

  creds: if sa_key != null then $.SealedSecret + {
    encryptedData: {
      'key.json': sa_key,
    },
  },

  deployment: self.Deployment + {
    spec+: {
      replicas: 1,
      template+: {
        spec+: {
          local pod = self,
          serviceAccountName: $.sa.metadata.name,
          containers: [
            $.Container + {
              name: name,
              image: 'registry.k8s.io/external-dns/external-dns:' + version,
              args: [
                '--registry=txt',
                '--txt-owner-id=' + owner_id,
                '--source=service',
                '--source=ingress',
              ] + [
                '--domain-filter=' + zone
                for zone in zones
              ] + [
                '--provider=google',
                '--google-project=' + gcp_project,
                '--google-zone-visibility=public',
                '--log-format=json',
              ],
              env: if $.creds != null then [
                {
                  name: 'GOOGLE_APPLICATION_CREDENTIALS',
                  value: '/run/secrets/external-dns/key.json',
                },
              ],
              volumeMounts: pod.volmount_mounts,
            },
          ],
          volmounts:: [
            if $.creds != null then {
              local vm = self,
              name:: $.creds.metadata.name,
              volume: {
                secret: {
                  secretName: vm.name,
                },
              },
              mount: {
                mountPath: '/run/secrets/external-dns',
              },
            },
          ],
        },
      },
    },
  },
}

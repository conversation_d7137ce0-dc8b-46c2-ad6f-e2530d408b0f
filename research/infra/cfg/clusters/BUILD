load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@python_pip//:requirements.bzl", "requirement")
load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library", "jsonnet_to_json")
load("//tools/bzl:python.bzl", "py_binary", "py_library")

package(default_visibility = ["//research/infra:internal"])

################################################################################
##
## Raw filegroup()s of jsonnet + *_tag.txt source files.
##
## These are needed because the jsonnet_library() targets unfortunately don't
## work as data deps for the python and go bindings.
##

filegroup(
    name = "_clusters_files",
    srcs = ["clusters.jsonnet"],
    visibility = ["//visibility:private"],
)

filegroup(
    name = "_lib_files",
    srcs = [
        "cluster.jsonnet",
        "node.jsonnet",
        "volmount.jsonnet",
    ],
    visibility = ["//visibility:private"],
)

filegroup(
    name = "jsonnet_files",
    srcs = [
        ":_clusters_files",
        ":_lib_files",
        "//research/environments:image_tags",
        "//research/infra/cfg/clusters/clusters:files",
    ],
    visibility = [
        "//clients/beachhead:__subpackages__",
        "//research/infra:internal",
        "//services/test:__subpackages__",
    ],
)

################################################################################
##
## jsonnet_library()
##
## There are three jsonnet_library()s -- two in this package, one in the clusters/
## subdir with "clusters_jsonnet" being the main entrypoint:
##
##  - "clusters_lib_jsonnet": The templates/models for a Cluster, Node, VolMount.
##  - "clusters:clusters": The actual cluster instances in the clusters/ subdir.
##  - "clusters_jsonnet": The central database of all available clusters.
##
## Running `bazel build :_debug_clusters_json` is a good e2e run of the jsonnet
## targets. See the go_test() below which provides unit test coverage.
##

jsonnet_library(
    name = "clusters_jsonnet",
    srcs = [":_clusters_files"],
    deps = [
        "//research/infra/cfg/clusters/clusters",
    ],
)

jsonnet_library(
    name = "cluster_lib_jsonnet",
    srcs = [":_lib_files"],
    data = [
        "//research/environments:image_tags",
    ],
)

jsonnet_to_json(
    name = "_debug_clusters_json",
    src = "clusters.jsonnet",
    outs = ["clusters.json"],
    visibility = ["//visibility:private"],
    deps = [":clusters_jsonnet"],
)

################################################################################
##
## py_library()
##
## Similar to the raw jsonnet_library()s, there is an interal "clusters_py_lib"
## which contains the templates/models for Cluster, Node, and VolMount. Then,
## there is a more user-facing "clusters_py" library which wraps __init__.py to
## provide the database of clusters to users.
##
## There is also a quick "clusters_py_bin" py_binary() to use as an e2e of the
## python targets.
##

py_library(
    name = "clusters_py",
    srcs = ["__init__.py"],
    visibility = ["//visibility:public"],
    deps = [":clusters_py_lib"],
)

py_library(
    name = "clusters_py_lib",
    srcs = [
        "cluster.py",
        "clusters.py",
        "node.py",
        "volmount.py",
    ],
    data = [":jsonnet_files"],
    deps = [
        requirement("dataclasses_json"),
        requirement("gojsonnet"),
        requirement("requests"),
    ],
)

py_binary(
    name = "clusters_py_bin",
    srcs = ["clusters.py"],
    deps = [":clusters_py_lib"],
)

################################################################################
##
## go_library() **AND go_test()**
##
## The unit tests are the primary coverage of the jsonnet library.
##

go_library(
    name = "clusters",
    srcs = [
        "cluster.go",
        "clusters.go",
        "node.go",
        "volmount.go",
        "volmount_deepcopy.go",
    ],
    importpath = "github.com/augmentcode/augment/research/infra/cfg/clusters",
    deps = [
        "//infra/lib/distribution",
        "//infra/lib/k8s",
        "//infra/lib/logger",
        "//research/infra/cfg",
        "@com_github_spf13_pflag//:pflag",
        "@io_k8s_api//core/v1:core",
        "@io_k8s_apimachinery//pkg/api/resource",
        "@io_k8s_client_go//applyconfigurations/core/v1:core",
    ],
)

go_test(
    name = "clusters_test",
    srcs = [
        "cluster_test.go",
        "clusters_test.go",
        "node_test.go",
        "volmount_test.go",
    ],
    embed = [":clusters"],
    deps = [
        "@com_github_google_go_cmp//cmp",
        "@com_github_google_go_cmp//cmp/cmpopts",
        "@com_github_spf13_pflag//:pflag",
        "@io_k8s_api//core/v1:core",
        "@io_k8s_apimachinery//pkg/api/resource",
        "@io_k8s_client_go//applyconfigurations/core/v1:core",
    ],
)

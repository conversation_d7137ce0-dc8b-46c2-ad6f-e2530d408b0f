#!/usr/bin/env python3

from dataclasses import dataclass, field

from dataclasses_json import Undefined, config, dataclass_json


@dataclass_json(undefined=Undefined.EXCLUDE)
@dataclass()
class Node:
    primary_alias: str
    aliases: list[str]
    additional_aliases: list[str]
    all_aliases: list[str]

    validation_errors: list[str]

    gpus: int | None
    cpus: int
    ram: str = field(metadata=config(field_name="ram_str"))
    ephemeral_storage: str | None = field(
        metadata=config(field_name="ephemeral_storage_str")
    )

    gpu_limit: int | None
    cpu_limit: int | None
    ram_limit: str | None = field(metadata=config(field_name="ram_limit_str"))
    ephemeral_storage_limit: str | None = field(
        metadata=config(field_name="ephemeral_storage_limit_str")
    )

    k8s_resources: dict
    k8s_node_selectors: list[dict]
    k8s_tolerations: list[dict]
    k8s_annotations: dict[str, str]

    def dumps(self) -> str:
        return self.schema().dumps(self, indent=4)  # type: ignore

    @classmethod
    def loads(cls, s: str) -> "Node":
        return cls.schema().loads(s)  # type: ignore

    def raise_if_errors(self) -> None:
        if len(self.validation_errors) > 0:
            raise Exception(str(self.validation_errors))

    def is_gpu(self) -> bool:
        return bool(self.gpus)

package clusters

func (in *VolMount) DeepCopyInto(out *VolMount) {
	*out = *in
	in.Vol.DeepCopyInto(&out.Vol)
	for i, m := range in.Mounts {
		m.DeepCopyInto(&out.Mounts[i])
	}
}

func (in *VolMount) DeepCopy() *VolMount {
	if in == nil {
		return nil
	}
	out := &VolMount{}
	in.DeepCopyInto(out)
	return out
}

func (in VolMounts) DeepCopyInto(out VolMounts) {
	for i, vm := range in {
		vm.DeepCopyInto(&out[i])
	}
}

func (in VolMounts) DeepCopy() VolMounts {
	out := make(VolMounts, len(in))
	in.DeepCopyInto(out)
	return out
}

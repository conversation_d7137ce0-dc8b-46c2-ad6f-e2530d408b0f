local provider = import '../../../../../infra/cfg/lib/k8s/providers/cw.jsonnet';
local cluster = import '../cluster.jsonnet';

local B = 1;
local KB = 1000 * B;
local MB = 1000 * KB;
local GB = 1000 * MB;
local TB = 1000 * GB;
local KiB = 1024 * B;
local MiB = 1024 * KiB;
local GiB = 1024 * MiB;
local TiB = 1024 * GiB;

{
  'cw-east4': cluster.Cluster + {
    local c = self,

    name: 'cw-east4',
    long_name: 'CoreWeave - us-east-04',
    prod: true,
    context: 'cw-east4',
    context_admin: 'cw-east4-admin',
    main_namespace: 'cw-east4',
    sys_namespace: 'aug-system',
    checkpoint_bucket: 's3://dev-training-dai',
    shared_mount: '/mnt/efs/augment',
    determined_url: 'https://determined.' + self.name + '.r.augmentcode.com',
    k8s_url_external: '',  // TODO, assuming an IP addr or coreweave.com
    k8s_url_internal: '',
    registry_hostname: 'registry.cw-east4.r.augmentcode.com',
    registry_basepath: '/infra',
    registry_img_pull_sec: 'registry-image-pull-secret',

    k8s:: provider + super.k8s + {
      ServiceAccount+:: {
        imagePullSecrets+: [
          { name: c.registry_img_pull_sec },
        ],
      },
    },

    user_roles+: [],
    user_cluster_roles+: ['edit'],
    user_cluster_cluster_roles+: ['view'],

    Node+:: {
      local n = self,
    },
    GPUNode+:: {
      gpu_selector_label: 'gpu.nvidia.com/class',
    },
    CPUNode+:: {
      cpu_selector_label: 'node.coreweave.cloud/type',
      k8s_node_selectors+: [{
        key: 'node.coreweave.cloud/class',
        operator: 'In',
        values: ['cpu'],
      }],
    },

    nodes: [
      // NOTE(mattm): Use the smaller CPU type as the default, but allow on any CPU node.
      self.CPUNode + {
        aliases: ['cpu'],
        cw_node_pool:: 'cpu',
        cw_node_type:: 'cpu.epyc.96',
        CPU+: { allocatable: 95 },
        RAM+: { allocatable: 755 * GiB },
        DSK+: { allocatable: 7290 * GB },
      },
      self.CPUNode + {
        cw_node_pool:: 'cpu',
        cw_node_type:: 'cpu.epyc.96',
        cpu_selector_value: self.cw_node_type,
        CPU+: { allocatable: 95 },
        RAM+: { allocatable: 755 * GiB },
        DSK+: { allocatable: 7290 * GB },
      },
      self.CPUNode + {
        cw_node_pool:: 'cpu-a384',
        cw_node_type:: 'cpu.epyc.384',
        cpu_selector_value: self.cw_node_type,
        CPU+: { allocatable: 383 },
        RAM+: { allocatable: 1510 * GiB },
        DSK+: { allocatable: 7290 * GB },
      },
      self.GPUNode + {
        aliases: ['a100'],
        cw_node_pool:: 'gpu-a100',
        gpu_selector_value: 'A100_NVLINK_80GB',
        GPU+: { capacity: 8 },
        CPU+: { allocatable: 127 },
        RAM+: { allocatable: 2014 * GiB },
        DSK+: { allocatable: 7290 * GB },
      },
      self.GPUNode + {
        aliases: ['h100'],
        cw_node_pool:: 'gpu-h100',
        gpu_selector_value: 'H100_NVLINK_80GB',
        GPU+: { capacity: 8 },
        CPU+: { allocatable: 127 },
        RAM+: { allocatable: 2014 * GiB },
        DSK+: { allocatable: 7290 * GB },
      },
    ],

    volmount_defs:: [
      // TODO
    ],
  },
}

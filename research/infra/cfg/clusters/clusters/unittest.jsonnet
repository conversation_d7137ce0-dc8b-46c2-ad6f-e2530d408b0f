// The cluster in this file is based off of CW, but with some values replaced / masked to avoid
// too much churn in downstream unit tests.
local gcp = import 'gcp.jsonnet';

local B = 1;
local KB = 1000 * B;
local MB = 1000 * KB;
local GB = 1000 * MB;
local TB = 1000 * GB;
local KiB = 1024 * B;
local MiB = 1024 * KiB;
local GiB = 1024 * MiB;
local TiB = 1024 * GiB;

{
  _unittest_: gcp.GCPBase + {
    local c = self,

    name: '_unittest_',
    long_name: '_Unit Test Cluster_',
    prod: false,
    k8s_url_external: 'https://k8s.TEST.coreweave.com',
    k8s_url_internal: 'https://**********',

    gcp_project:: 'augment-unittest0',
    gcp_project_number:: '12345000042',
    gcp_region:: 'moon-darkside1',

    context: self.name,
    main_namespace: self.name,
    checkpoint_bucket: 'gs://det-unittest0',
    determined_url: 'https://det-unittest0.' + self.name + '.r.augmentcode.test',
    registry_hostname: 'reg-unittest0.r.augmentcode.test',
    registry_basepath: '/' + self.gcp_project + '/docker-' + self.gcp_region,

    // Replace versions, which change frequently.
    images: {
      local parts = std.splitLimit(item.value, ':', 1),
      [item.key]: parts[0] + ':v1'
      for item in std.objectKeysValues(super.images)
    },

    // NOTE(matt): A copy of gcp-us1's nodes at some arbitrary point in time.
    nodes: [
      self.CPUNode + {
        machine_type: 'c3-standard-176-lssd',
        aliases: ['cpu'],
        CPU+: { capacity: 176 },
        RAM+: {
          capacity: 740 * GB,  // Documented: 704GB; Observed: 743+GB;
          capacity_free_b: 743946518528,  // exact for debugging
          allocatable_obsv: 704722624 * KiB,  // exact for debugging
        },
        DSK+: {
          disk_count: 32,  // Documented 32*375GiB = 12TiB
          capacity_fs_obsv: 12777882288128,  // exact for debugging
          capacity_obsv: 12478400672 * KiB,  // exact for debugging
          allocatable_obsv: 11392719857875,  // exact for debugging
        },
        GCS_FUSE+: {
          slots: 20,  // ?!?(mattm): Roughly-ish 8-CPU slots, but mostly arbitrary
        },
      },

      self.GPUNode + {
        machine_type: 'n1-standard-64',
        aliases: ['t4'],
        gpu_selector_value: 'nvidia-tesla-t4',
        GPU+: { capacity: 4 },  // Documented: 1-8, we choose 4 for now.
        CPU+: { capacity: 64 },
        RAM+: { capacity: 250 * GB },  // Document: 240GB; Observed: 253+GB
        DSK+: { disk_count: 8 },  // Documented: 1-8, 16, or 24
      },

      self.GPUNode + {
        machine_type:: 'g2-standard-48',
        aliases: ['l4'],
        gpu_selector_value: 'nvidia-l4',
        GPU+: { capacity: 4 },  // Documented: 4
        CPU+: { capacity: 48 },  // Documented: 48
        RAM+: { capacity: 200 * GB },  // Documented: 192GB, Observed: 202+GB
        DSK+: { disk_count: 4 },  // Documented: 4
      },

      self.GPUNode + {
        machine_type:: 'a3-megagpu-8g',
        aliases: ['h100'],
        gpu_selector_value: 'nvidia-h100-mega-80gb',
        GPU+: { capacity: 8 },  // Documented: 8
        CPU+: { capacity: 208 },
        RAM+: { capacity: 1970 * GB },  // Documented: 1872GN, Observed: 1978+GB
        DSK+: { disk_count: 16 },  // Documented: 16
      },
    ],

    volmount_defs+:: [
      self._csi_vol(self, 'checkpoints0', '/mnt/efs/checkpoints0'),
      self._csi_vol(self, 'bucket1', '/mnt/bucket1'),
      {
        name: 'sec0',
        secret: self.name,
        path: '/run/' + self.name,
        extra_paths: ['/run/sec0-extra'],
      },
    ],
  },
}

local provider = import '../../../../../infra/cfg/lib/k8s/providers/gcp.jsonnet';
local cluster = import '../cluster.jsonnet';

local B = 1;
local KB = 1000 * B;
local MB = 1000 * KB;
local GB = 1000 * MB;
local TB = 1000 * GB;
local KiB = 1024 * B;
local MiB = 1024 * KiB;
local GiB = 1024 * MiB;
local TiB = 1024 * GiB;

{
  GCPBase:: cluster.Cluster + {
    local c = self,

    shared_mount: '/mnt/efs/augment',
    sys_namespace: 'aug-system',

    k8s:: provider + super.k8s,

    Node+:: {
      local n = self,

      // GKE reservations are documented here:
      // https://cloud.google.com/kubernetes-engine/docs/concepts/plan-node-sizes

      GPU+: {
        name: 'nvidia.com/gpu',
      },

      CPU+: {
        tax_brackets: [
          // Rate, Range (cores)
          self._bracket(0.06, 1),
          self._bracket(0.01, 1),
          self._bracket(0.005, 2),
          self._bracket(0.0025),
        ],
        deductions+: [
          n.GCS_FUSE.cpu_total,
        ],
      },

      RAM+: {
        flat_tax: 100 * MiB,
        tax: if self.capacity < 1 * GiB then 255 * MiB else super.tax,
        tax_brackets: [
          // Rate, Range
          self._bracket(0.25, 4 * GiB),
          self._bracket(0.20, 4 * GiB),
          self._bracket(0.10, 8 * GiB),
          self._bracket(0.06, 112 * GiB),
          self._bracket(0.02),
        ],
        deductions+: [
          n.GCS_FUSE.ram_total,
        ],
        allocatable: (1 - 0.0001) * super.allocatable,  // The precise calc was a little high for c3-172-lssd so add small fudge.
      },

      DSK+: {
        disk_size: 375 * GiB,
        disk_count: 0,
        capacity: self.disk_size * self.disk_count * (1 - 0.01),  // We lose a little less than 1% for the filesystem.
        tax: self._eviction_threshold + self._system_reservation,
        _eviction_threshold: 0.10 * self.disk_count * self.disk_size,
        _system_reservation: (
          if self.disk_count == 0 then 0 else
            if self.disk_count == 1 then 50 * GiB else
              if self.disk_count == 2 then 75 * GiB else
                100 * GiB
        ),
        deductions+: [
          n.GCS_FUSE.dsk_total,
        ],
      },

      GCS_FUSE: {
        // https://cloud.google.com/kubernetes-engine/docs/how-to/persistent-volumes/cloud-storage-fuse-csi-driver
        // The defaults are 250m CPU / 256 MiB RAM / 5 GiB Ephemeral Storage as *requests* / no *limits*.
        //
        // NOTE(mattm): This would be *much* easier, and easier to evolve, if we took these out of the user's original
        // request so that all future adjustments happened within users' existing allocations. However, we'll try to avoid
        // surprising people so that they get all of their --cpu-count, --ram, etc. The downside is that if we adjust the numbers here
        // the "slot sizes" will be fractured for existing pods until everyone updates. To do this, we take a deduction from
        // each node's overall usable capacity -- each `*_total` is deducted from CPU, RAM, and DSK above.
        //
        // For CPU and RAM 8x the defaults (2 CPU / 2 GiB RAM) seem resonable with our typical method of multiplying
        // proportionally with the node. (So e.g., 16 CPU / 16 GiB RAM when taking all 8 GPUs on a GPU node). We have to be careful
        // here because there's no check to see what the total is compared to the overal capacity on the node. In practice even
        // with ~20 slots we're capped at 40 CPU / 40 GiB RAM which seems fine for a large node; for a smaller node we'd have fewer
        // slots.
        //
        // For DSK (ephemeral-storage) we do the inverse and take 60% of the original allocatable capacity and then
        // divide by the expected number of slots on the node. We want even small pods to have a cache size large enough to hold
        // some of the ~50GiB model/checkpoint files but at that size and large numbers of slots we'll likely exceed the node's
        // capacity. Taking 60% means we'll have a large cache available but still leave plenty of room for the other ephemeral-storage
        // useses (logs, writable overalay, images, emptyDirs) no matter the node's shape.
        //
        // For all 3 CPU, RAM, and DSK, do our usual **requests == limits**.
        //
        // On GPU nodes, the number of slots is equal to the number of GPUs so this is more straightforward. Our large CPU
        // node with ~170 CPU is less obvious, similary if we add a pool of small-CPU nodes. For now, the CPUNode (below)
        // sets a guestimate on `GCS_FUSE.slots`.
        //

        multiplier: n.multiplier,
        slots: n._num_slots,

        // Defaults are 250m CPU / 256 MiB RAM / 5 GiB Ephemeral Storage as requests with no limits.
        default_cpu: 0.25,
        default_ram: 256 * MiB,
        default_dsk: 5 * GiB,

        // This sets a min of 2;2G for GPU nodes, CPU nodes set this to x4 instead of x8.
        cpu_and_ram_default_multiplier: if n.is_gpu then 8 else 4,
        cpu_default_multiplier: self.cpu_and_ram_default_multiplier,
        ram_default_multiplier: self.cpu_and_ram_default_multiplier,

        cpu_per_slot: self.cpu_default_multiplier * self.default_cpu,
        ram_per_slot: self.ram_default_multiplier * self.default_ram,
        dsk_per_slot: self.dsk_total / self.slots,

        cpu_total: self.cpu_per_slot * self.slots,
        ram_total: self.ram_per_slot * self.slots,
        dsk_total: 0.60 * n.DSK.allocatable,  // 60% of the machine's SSD for read cache

        cpu_request: self.multiplier * self.cpu_per_slot,
        ram_request: self.multiplier * self.ram_per_slot,
        dsk_request: self.multiplier * self.dsk_per_slot,

        cpu_limit: self.cpu_request,
        ram_limit: self.ram_request,
        dsk_limit: self.dsk_request,

        // NOTE(mattm): Workaround jsonnet crashing on %g with 0.
        cpu_request_str: if self.cpu_request == 0 then '0' else if self.cpu_request != null then std.format('%g', [self.cpu_request]),
        ram_request_str: if self.ram_request == 0 then '0' else if self.ram_request != null then std.format('%gGi', [self.ram_request / GiB]),
        dsk_request_str: if self.dsk_request == 0 then '0' else if self.dsk_request != null then std.format('%gGi', [self.dsk_request / GiB]),

        cpu_limit_str: if self.cpu_limit == 0 then '0' else if self.cpu_limit != null then std.format('%g', [self.cpu_limit]),
        ram_limit_str: if self.ram_limit == 0 then '0' else if self.ram_limit != null then std.format('%gGi', [self.ram_limit / GiB]),
        dsk_limit_str: if self.dsk_limit == 0 then '0' else if self.dsk_limit != null then std.format('%gGi', [self.dsk_limit / GiB]),
      },

      k8s_node_selectors+: if self.DSK.base == null then [] else [{
        key: 'cloud.google.com/gke-ephemeral-storage-local-ssd',
        operator: 'In',
        values: ['true'],
      }],

      k8s_annotations+: std.prune({
        'gke-gcsfuse/volumes': 'true',  // Enable the GCS FUSE SideCar container.

        'gke-gcsfuse/cpu-request': n.GCS_FUSE.cpu_request_str,
        'gke-gcsfuse/memory-request': n.GCS_FUSE.ram_request_str,
        'gke-gcsfuse/ephemeral-storage-request': n.GCS_FUSE.dsk_request_str,

        'gke-gcsfuse/cpu-limit': n.GCS_FUSE.cpu_limit_str,
        'gke-gcsfuse/memory-limit': n.GCS_FUSE.ram_limit_str,
        'gke-gcsfuse/ephemeral-storage-limit': n.GCS_FUSE.dsk_limit_str,
      }),
    },

    GPUNode+:: {
      gpu_selector_label: 'cloud.google.com/gke-accelerator',
    },
    CPUNode+:: {},

    volmount_defs:: [],

    _csi_vol:: function(C, suffix, mountpoint) {
      name: 'gcs-' + C.name + '-' + suffix,
      path: mountpoint,
      csi: {
        driver: 'gcsfuse.csi.storage.gke.io',
        volumeAttributes: {
          bucketName: C.name + '-' + suffix,

          // https://cloud.google.com/kubernetes-engine/docs/how-to/persistent-volumes/cloud-storage-fuse-csi-driver
          // https://cloud.google.com/storage/docs/gcsfuse-config-file

          // "-1" means unlimited, which hopefuly is capped by the cache's emptyDir volume, which is backed by
          // slots of 60% of the node's overall local-ssd capacity (described above).
          // NOTE(mattm): pretty-please let "-1" work and have each volume gracefully share the overal cache. If
          // this doesn't work we'll have to size each volume individually which will be a PITA. We currently have
          // 12 volumes and dividing by 12 may leave us with 12 caches which are too small to be useful. Picking and
          // choosing sizes for each of the 12 sounds like a nightmare.
          fileCacheCapacity: '-1',

          fileCacheForRangeRead: 'false',  // default. This is for partial reads within a large file... do we do any of that?
          mountOptions: std.join(',', [
            'implicit-dirs',
            'uid=1000',
            'gid=1000',
            'file-mode=0660',
            'dir-mode=0770',
            'o=noexec',
            'o=noatime',

            // https://cloud.google.com/kubernetes-engine/docs/how-to/persistent-volumes/cloud-storage-fuse-csi-driver#parallel-download
            'file-cache:enable-parallel-downloads:true',
            'file-cache:parallel-downloads-per-file:8',
            'file-cache:max-parallel-downloads:-1',
            'file-cache:download-chunk-size-mb:8',  // NOTE(mattm): Somewhat arbitrary, examples used "3".

            // This has a noticeable improvement to successive `ls -l` calls on slow volumes. However, it's a static
            // consistency delay so we can't make this too long.
            'file-system:kernel-list-cache-ttl-secs:30',

            // This defaults to true and it means when we have IO (like `ls -l`) hanging for ~1m and
            // regret it, we get stuck. Disabling allows `ctrl-c` to work immediately to cancel.
            'file-system:ignore-interrupts:false',
          ]),
        },
      },
    },
  },

  'gcp-us1': $.GCPBase + {
    name: 'gcp-us1',
    long_name: 'GCP us-central1',
    gcp_project: 'augment-research-gsc',
    gcp_project_number: 670297456363,
    gcp_region: 'us-central1',
    prod: true,
    context: self.name,
    main_namespace: self.name,
    sys_namespace: 'aug-system',
    checkpoint_bucket: 'gs://determined-checkpoint-storage',
    determined_url: 'https://determined.' + self.name + '.r.augmentcode.com',
    registry_hostname: 'us-central1-docker.pkg.dev',
    registry_basepath: '/' + self.gcp_project + '/docker-' + self.gcp_region,

    nodes: [
      self.CPUNode + {
        machine_type: 'c3-standard-176-lssd',
        aliases: ['cpu'],
        CPU+: { capacity: 176 },
        RAM+: {
          capacity: 740 * GB,  // Documented: 704GB; Observed: 743+GB;
          capacity_free_b: 743946518528,  // exact for debugging
          allocatable_obsv: 704722624 * KiB,  // exact for debugging
        },
        DSK+: {
          disk_count: 32,  // Documented 32*375GiB = 12TiB
          capacity_fs_obsv: 12777882288128,  // exact for debugging
          capacity_obsv: 12478400672 * KiB,  // exact for debugging
          allocatable_obsv: 11392719857875,  // exact for debugging
        },
        GCS_FUSE+: {
          slots: 20,  // ?!?(mattm): Roughly-ish 8-CPU slots, but mostly arbitrary
        },
      },

      self.CPUNode + {
        machine_type: 'n2-standard-80',
        aliases: ['kvm'],
        cpu_selector_label: 'r.augmentcode.com/pool-type',
        cpu_selector_value: self.aug_pool_type,
        aug_pool_type: 'cpu-kvm0',
        CPU+: { capacity: 80 },
        RAM+: {
          capacity: 4 * 80 * GB,
        },
        DSK+: {
          capacity: null,
          disk_count: null,
        },
        GCS_FUSE+: {
          slots: 18,  // ?!?(mattm): Roughly, based on slots of 4 CPU.
          dsk_total: null,
          dsk_per_slot: null,
          dsk_request: null,
        },
      },

      self.GPUNode + {
        machine_type: 'n1-standard-64',
        aliases: ['t4'],
        gpu_selector_value: 'nvidia-tesla-t4',
        GPU+: { capacity: 4 },  // Documented: 1-8, we choose 4 for now.
        CPU+: { capacity: 64 },
        RAM+: { capacity: 250 * GB },  // Document: 240GB; Observed: 253+GB
        DSK+: { disk_count: 8 },  // Documented: 1-8, 16, or 24
      },

      self.GPUNode + {
        machine_type:: 'g2-standard-48',
        aliases: ['l4'],
        gpu_selector_value: 'nvidia-l4',
        GPU+: { capacity: 4 },  // Documented: 4
        CPU+: { capacity: 48 },  // Documented: 48
        RAM+: { capacity: 200 * GB },  // Documented: 192GB, Observed: 202+GB
        DSK+: { disk_count: 4 },  // Documented: 4
      },

      self.GPUNode + {
        machine_type:: 'a3-megagpu-8g',
        aliases: ['h100'],
        gpu_selector_value: 'nvidia-h100-mega-80gb',
        GPU+: { capacity: 8 },  // Documented: 8
        CPU+: { capacity: 208 },
        RAM+: { capacity: 1970 * GB },  // Documented: 1872GN, Observed: 1978+GB
        DSK+: { disk_count: 16 },  // Documented: 16
        container_mounts+: [  // mounted on the main container
          { name: 'aperture-devices', mountPath: '/dev/aperture_devices' },
          { name: 'libraries', mountPath: '/usr/local/nvidia/lib64' },
        ],
        container_envs+: [{ name: 'NCCL_FASTRAK_LLCM_DEVICE_DIRECTORY', value: '/dev/aperture_devices' }],
        sidecar_volumes+: [  // added to the volumes
          {
            name: 'libraries',
            hostPath: { path: '/home/<USER>/bin/nvidia/lib64' },
          },
          {
            name: 'sys',
            hostPath: { path: '/sys' },
          },
          {
            name: 'proc-sys',
            hostPath: { path: '/proc/sys' },
          },
          {
            name: 'aperture-devices',
            hostPath: { path: '/dev/aperture_devices' },
          },
        ],
      },
    ],

    volmount_defs:: [
      self._csi_vol(self, 'checkpoints', '/mnt/efs/augment/checkpoints'),
      self._csi_vol(self, 'configs', '/mnt/efs/augment/configs'),
      self._csi_vol(self, 'data', '/mnt/efs/augment/data'),
      self._csi_vol(self, 'eval', '/mnt/efs/augment/eval'),
      self._csi_vol(self, 'external-models', '/mnt/efs/augment/external_models'),
      self._csi_vol(self, 'ftm-checkpoints', '/mnt/efs/augment/ftm_checkpoints'),
      self._csi_vol(self, 'python-env', '/mnt/efs/augment/python_env'),
      self._csi_vol(self, 'user', '/mnt/efs/augment/user'),
      self._csi_vol(self, 'public-html', '/mnt/efs/augment/public_html'),
      self._csi_vol(self, 'augment-ci-testing', '/mnt/efs/augment-ci-testing'),

      self._csi_vol(self, 'spark-data', '/mnt/efs/spark-data'),
    ],
  },
}

local provider = import '../../../../../infra/cfg/lib/k8s/providers/gcp.jsonnet';
local cluster = import '../cluster.jsonnet';

// Special-purpose GKE clusters for hosting remote agents.
{
  _agent_cluster_base:: cluster.Cluster + {
    local C = self,

    // The services environments hosted by this cluster (PROD/STAGING/DEV/...).
    services_envs:: [],
    services_env:: {
      [std.asciiUpper(env)]: {
        // In `base-resources`, a registry is created for each environment in each cluster.
        agent_image_registry:: '%(region)s-docker.pkg.dev/%(project)s/agents-%(env)s-%(region)s' % {
          region: C.gcp_region,
          project: C.gcp_project,
          env: std.asciiLower(env),
        },
      }
      for env in self.services_envs
    },

    // The normal registry property of the cluster should be used if/when we
    // deploy our own services in it.
    registry: null,
    registry_hostname: null,
    registry_basepath: null,

    // Normal cluster.Cluster properties.
    prod: false,  // no devpods, batch jobs, etc
    context: self.name,
    main_namespace: self.name,
    sys_namespace: 'aug-system',
    k8s:: provider + super.k8s + {
      GCP+:: {
        CloudIdentity+:: {
          Group+:: {
            spec+: {
              parent: 'customers/C02kn0kha',
            },
          },
        },
      },
    },

    // Stuff that doesn't apply to agent clusters.
    checkpoint_bucket: null,
    shared_mount: null,
    determined_url: null,

    nodes: [],
    volmount_defs:: [],

    // GitHub runner access to agent registries
    github_runner_access:: {
      github_runner_artifact_access: self.k8s.GCP.IAM.PolicyMember + {
        name:: 'gcp-us1-github-runner.artifactregistry-writer',
        spec+: {
          resourceRef: {
            kind: 'Project',
            external: 'project/' + C.gcp_project,
          },
          member: 'serviceAccount:<EMAIL>',
          role: 'roles/artifactregistry.writer',
        },
      },
    },
  },

  'gcp-agent0': self._agent_cluster_base + {
    name: 'gcp-agent0',
    long_name: 'GCP us-central1 Research Agent MVP',
    gcp_project:: 'augment-research-gsc',
    gcp_project_number: ************,
    gcp_region: 'us-central1',
    services_envs: ['DEV'],
  },

  'gcp-prod-agent0': self._agent_cluster_base + {
    name: 'gcp-prod-agent0',
    long_name: 'GCP us-central1 Remote Agent Workspaces (prod)',
    gcp_project:: 'agent-sandbox-prod',
    gcp_project_number: ************,
    gcp_region: 'us-central1',
    services_envs: ['STAGING', 'PROD'],
  },

  'gcp-eu-w4-prod-agent0': self._agent_cluster_base + {
    name: 'gcp-eu-w4-prod-agent0',
    long_name: 'GCP eu-west4 Remote Agent Workspaces (prod)',
    gcp_project:: 'agent-sandbox-prod',
    gcp_project_number: ************,
    gcp_region: 'europe-west4',
    services_envs: ['PROD'],
  },
}

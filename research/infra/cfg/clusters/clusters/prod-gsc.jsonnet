local provider = import '../../../../../infra/cfg/lib/k8s/providers/gcp.jsonnet';
local cluster = import '../cluster.jsonnet';

{
  // This is a special-purpose GKE Cluster for Prod Services to float gSC H100 capacity.
  'prod-gsc': cluster.Cluster + {
    local C = self,
    name: 'prod-gsc',
    long_name: 'GCP us-central1 Production Services gSC',
    gcp_project:: 'system-services-prod-gsc',
    gcp_project_number:: 610909115406,
    gcp_region: 'us-central1',
    prod: false,
    context: self.name,

    k8s:: provider + super.k8s,

    // Stuff that doesn't apply to this cluster.
    main_namespace: null,
    sys_namespace: null,
    checkpoint_bucket: null,
    shared_mount: null,
    determined_url: null,
    registry: null,
    registry_hostname: null,
    registry_basepath: null,

    nodes: [],
    volmount_defs:: [],
  },
}

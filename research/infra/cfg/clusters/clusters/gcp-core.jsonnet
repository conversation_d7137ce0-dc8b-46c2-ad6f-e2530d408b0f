local provider = import '../../../../../infra/cfg/lib/k8s/providers/gcp.jsonnet';
local cluster = import '../cluster.jsonnet';

{
  'gcp-core0': cluster.Cluster + {
    name: 'gcp-core0',
    long_name: 'GCP Core Cluster',
    gcp_project:: 'augment-research-gsc',
    gcp_region:: 'us-central1',

    prod: false,  // no devpods, batch jobs, etc
    context: self.name,
    main_namespace: self.name,
    sys_namespace: 'aug-system',
    registry_hostname: self.gcp_region + '-docker.pkg.dev',
    registry_basepath: '/' + self.gcp_project + '/docker-' + self.gcp_region,

    k8s:: provider + super.k8s + {
      GCP+:: {
        CloudIdentity+:: {
          Group+:: {
            spec+: {
              parent: 'customers/C02kn0kha',
            },
          },
        },
      },
    },

    // Stuff that doesn't apply to this cluster.
    checkpoint_bucket: null,
    shared_mount: null,
    determined_url: null,
    images: {},
    Node:: null,
  },
}

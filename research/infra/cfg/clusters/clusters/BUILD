load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library", "jsonnet_to_json")

package(default_visibility = ["//research/infra:internal"])

filegroup(
    name = "files",
    srcs = glob(["*.jsonnet"]) + [
        "//infra/cfg/lib/k8s/providers:srcs",
    ],
)

jsonnet_library(
    name = "clusters",
    srcs = [":files"],
    deps = [
        "//research/infra/cfg/clusters:cluster_lib_jsonnet",
        "//research/infra/cfg/lib",
    ],
)

jsonnet_to_json(
    name = "_debug_gcp_json",
    src = "gcp.jsonnet",
    outs = ["gcp.json"],
    visibility = ["//visibility:private"],
    deps = [":clusters"],
)

jsonnet_to_json(
    name = "_debug_cw_json",
    src = "coreweave.jsonnet",
    outs = ["cw.json"],
    visibility = ["//visibility:private"],
    deps = [":clusters"],
)

jsonnet_to_json(
    name = "_debug_unittest_json",
    src = "unittest.jsonnet",
    outs = ["unittest.json"],
    visibility = ["//visibility:private"],
    deps = [":clusters"],
)

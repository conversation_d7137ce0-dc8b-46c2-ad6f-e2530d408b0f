package clusters

import (
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
)

func clusterT(t *testing.T) Cluster {
	clusters, err := New()
	if err != nil {
		t.Fatalf("Error loading clusters: %v.", err)
	}

	c, err := clusters.Cluster("_unittest_")
	if err != nil {
		t.Fatalf("Error loading _unittest_ cluster: %v.", err)
	}

	return c
}

func TestCluster(t *testing.T) {
	c := clusterT(t)

	c.Dump(true, true)

	if got, want := c.Name, "_unittest_"; got != want {
		t.Errorf("Name: got %v, want %v.", got, want)
	}
	if got, want := c.Long<PERSON>ame, "_Unit Test Cluster_"; got != want {
		t.Errorf("LongName: got %v, want %v.", got, want)
	}
	if got, want := c.Prod, false; got != want {
		t.Errorf("Prod: got %v, want %v.", got, want)
	}
	if got, want := c.Registry, "reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1"; got != want {
		t.Errorf("Registry: got %v, want %v.", got, want)
	}
	if got, want := c.Context, "_unittest_"; got != want {
		t.Errorf("Context: got %v, want %v.", got, want)
	}
	if got, want := c.MainNamespace, "_unittest_"; got != want {
		t.Errorf("MainNamespace: got %v, want %v.", got, want)
	}
	if got, want := c.CheckpointBucket, "gs://det-unittest0"; got != want {
		t.Errorf("CheckpointBucket: got %v, want %v.", got, want)
	}
	if got, want := c.SharedMount, "/mnt/efs/augment"; got != want {
		t.Errorf("SharedMount: got %v, want %v.", got, want)
	}
	if got, want := c.DeterminedURL, "https://det-unittest0._unittest_.r.augmentcode.test"; got != want {
		t.Errorf("DeterminedURL: got %v, want %v.", got, want)
	}
	if got, want := c.AugiReleaseReaders, "augi-release-readers"; got != want {
		t.Errorf("AugiReleaseReaders: got %v, want %v.", got, want)
	}

	wantProfile := []string{
		"export AUGMENT_DEV_CLOUD_PROVIDER=_unittest_",
		"export AUGMENT_DEV_CONTAINER_REPO=reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1",
		"export AUGMENT_DEV_GPU_TAG=reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_gpu:v1",
		"export AUGMENT_DEV_CPU_TAG=reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_cpu:v1",
		"export AUGMENT_DEVPOD_CPU_TAG=reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_cpu:v1",
		"export AUGMENT_DEVPOD_GPU_TAG=reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_gpu:v1",
		"export AUGMENT_DEV_DETERMINED_URL=https://det-unittest0._unittest_.r.augmentcode.test",
		"export AUGMENT_DEV_SHARED_MOUNT_PATH=/mnt/efs/augment",
		"",
	}
	if diff := cmp.Diff(strings.Join(wantProfile, "\n"), c.Profile(true)); diff != "" {
		t.Errorf("Profile() -want +got:\n%s", diff)
	}

	if got, want := c.IsGCP(), true; got != want {
		t.Errorf("IsGCP(): got %v, want %v.", got, want)
	}
}

func TestLatestImageFromCurrent(t *testing.T) {
	c := clusterT(t)
	tests := map[string]struct {
		in      string
		want    string
		wantErr string
	}{
		"cpu": {
			in:      c.Registry + "/augment_devpod_cpu:v0",
			want:    c.Registry + "/augment_devpod_cpu:v1",
			wantErr: "",
		},
		"gpu": {
			in:      c.Registry + "/augment_devpod_gpu:v0",
			want:    c.Registry + "/augment_devpod_gpu:v1",
			wantErr: "",
		},
		"error": {
			in:      c.Registry + "/augment_gpu:v0",
			want:    "",
			wantErr: "LatestImageFromCurrent():",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			got, gotErr := c.LatestImageFromCurrent(tc.in)

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}

			if got != tc.want {
				t.Errorf("image -want +got:\n- %s\n+%s", got, tc.want)
			}
		})
	}
}

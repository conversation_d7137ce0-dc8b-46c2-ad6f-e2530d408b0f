local clusters = import '../clusters/clusters.jsonnet';

function(cluster_name) {
  by_cluster(obj)::
    if std.objectHas(obj, cluster_name)
    then obj[cluster_name]
    else std.get(obj, 'default'),

  filter_by_cluster(obj):: {
    [kv.key]: kv.value
    for kv in std.objectKeysValues(obj)
    if !std.isObject(kv.value) || std.member([null, cluster_name], std.get(kv.value, 'cluster_name'))
  },

  C:: clusters.cluster(cluster_name),
  k8s:: $.C.k8s + {
    BaseObject+:: {
      cluster_name:: null,
    },
  },

  ret:: $.filter_by_cluster({

    // Probably not used, creating to get past testing.
    determined_obj_store_creds: $.k8s.SealedSecret + {
      name:: 'tenant-augment-eng-determined-app-obj-store-creds',
      encryptedData: $.by_cluster({
        'gcp-us1': {
          accessKey: 'AgAWV+BYIZeG8SjarAYrBLNmXWT25k/ZPJUO1qPnhtRGhLtTVk8YT0Xj82E2/RcC2ejzwiW5pwi+rKG3B+Is8JCZ4DsjVaQ+g4am2MWbdds/SN4wwqPEOhEk/0NMqmwKH//lfarZlj5LxjkKiMhJcMAzkz0afGVXN/FmWQYd5zAaa8uSXZ86BGhGSUf1JWgVxuLSd3sYrEk8de25D35yKXDHdoyvbPKjPtY+aRRt7hNiw90MQ1y8ZpKWxeKvcAMnOLLGMrvpIdGL1Ii3UTSaaMWO+P9bD7QwTl44JHw8nsjtDkjx5ci0GWrerWlk2sXXTLbw4tPOodVQwNfocDJyWT33Gt4MnP6yHiXfbXYaJquEWFjKdTDqCi1st3AzHJSXPAupcp3vh3SHg7+ZJG0hWkFRDz87jdpZ9NJOFfdv5503OpFdXTeQfJyOZafUIqDNu0s4D/jm5bhvDrxvBkMDAUARLgnXdllXrlHDNL75E96T+qYWJKfyqimGv38Ok23/E+D5MzeAjWuM/8Mu3z1ZKbNobVo+bOtfgyzk2U9KV+HWpSJiasNkXtMXGk7BIbjJBzxW64/M2lVf0pQNGU1KLctEmSPsTCkbCBRdpRFotUQwfMp2Lm8TupCeOdubbxy3z4O/g4zYCthPNXcEgn0/+JLcsn82GKi4iI0mrhRYKJheXHsd2faQG46iwKdzwQqs5cqtHeJChdJ3KhjUNdvsqvBV',
          secretKey: 'AgCnlv5Lkb8bjQnS4Dot0qgfw+oNoyu0UJ9v6dKnQsiyrMK9PF+R+Om1Z1vdAZL9UdN0c2Hk1tFQp4VlU03ss5SfcGOFD+4oaIjbLJTyYyFfkoFzdRmGNWDBYgUDleM3TsESz49r7O7bx+9BHp7wpHEpDEFMiKXtKpl5A2zwtvylpw+JdjmWclWZJAy5+jLVT54OoP9GGvoetMVKDUvW6yt1UmoS0767fRoXjfxW86mHchI2NAOilPXHjFAtBpBdUZYBD2AfghxxRtHetgzUu6nBpaoLbSxZ4YjGvsRzdk++iF7sUVaI4DCTJFHn1BTZEvXUOqpEaOdwm9OcpTHy0BVBDjBlmdYAaKo38h6iKMbjZaOpu3jhIeYBZNrPt/vrh5CmEH2EMFhE+5jj27NAS5DeZhuF//qPVPmSG8uc+nC0K5KBQ5q+kfE24c4BnJASEBfy9m96jsQPzOFYU36Y4DrZqtpMYYxuM0iCJ4a3rRt+TwuGP1t56+TCTIHd3P93QoJGZ64pS/r72vP3p0gLrk+yn7ceB/GUSp78p9hrTQFjiAhYnO5sFMXbPL3jBDzp/z7qV57OeMeaBUWlPf9KNPyjwV4RLZa6TA/QQX5/BCp0TYwKLYPlgdz6UUWwrUhZQyFYjNjxSAIMgxcpgWCQx2UgwK4kAUZ7hxymIoGE+y6cFRRkfu/Vc40wWW75IsYeowZoMfFDCFa7KNfZ5zWUPpY3BpGhpQaQXb/y9OLZlCBr2H56d7lWuiNPrB3o',  // pragma: allowlist secret
        },
        'cw-east4': {
          accessKey: '',
          secretKey: '',  // pragma: allowlist secret
        },
      }),
    },

    aws_credential_keys: $.k8s.SealedSecret + {
      name:: 'aws-credential-keys',
      encryptedData: $.by_cluster({
        'gcp-us1': {
          aws_access_key_id: 'AgBtX+2Y4xCQxN4aq3iyoUu7LiR85gAeriSyZpXiCLm+p10tDKpfA+sJxZAK83oaNLAFk6mb4c64CYEc3bYQ50isuHUWlf8zXhJUrsLhlx2QvWovQTw9F+/6akeWPZSFXRLIxFHwmN5AMyw+kSBxHglPK7UnNTyP8Kn3irmG+M/3US/XNwcqQjF2EwRcdGMfOyswln8U5rLqlf2dfc23AgfUUK5s5bMQp+9+R1wNitc9zpY10HXDbwh8BRyPlxBzdtwD+4PxCmSfNu1zTN2LOhsLB+AjSRPG0GWx+SjbOwR45MzsO6jmPojd7onasaljr2zDQIvloVw/A21yhBhtdAlWGz8hL/+Dxm6B1pF2DNOVj/DbTxrMMZBvnPoIY8S8vlLr82t4ul8QEuVPbGRjdK9VlePRtPmfvzSTCGUzHFLgajm1Jj32l86/bsoNlerZIxPvKju09Y4I1ChCUrQWnGtYkQfkSOcAEw0BtbdECKE7gz44wWhlRFlqURqcIic5FEsVrWQ5v4Vy8u2nSeG9eOjnElSg9gCy3QEcJey8W1BzARuAhmagvUsCFyN7eJbVXeDQRA8oIwZW5n9D8KkAeJqf9G1xMTS2MZato8yE8oH2fRJzl/4eNrOMtEc2vpjE/fL6icl2O+5ifhZRFe/9r263CTtWRFsDo5nIVRogGxGzGv8J9a2SGagfAOF9G/3G5nxyyJZCnwtSMhMkxyT6VGyBjAb4RQ==',
          aws_secret_access_key: 'AgAten65k05j+aQL8fPxc2qqpVDs8bwdSadiZU1pt/MQpPvtnbF0k4XK7FTEqWLeqSu6lNJ75Mhkmw4Lz8D3eNES2lNgxZHqonZ1SC7DtE1Rd06opMBPOCdDsVYTMNUfDrNt9qP5uwTQbQC4SjHtekV2jrU+5nUKPza5oeiKvsxbBuEGWuB81g+Wyd8KJtxYSGxajmLj84JspC+8QqTmD4dj57NPdiKuF/6FtgFNW2TpWit2z4R18aV3DkKAHlweEu4YXYAUAyNJ2tmkQmQlGfL6J0abIpefY9orV3r2KNLamQHuPR1PMNcb3mE9XAP1OItucmDCmYda5ljZHPQ2lqCpym0WZqiEadrBKVwuizezWIU1BlSrYw8HWvLlFQ4kH/21qkf04c48WTarmjGKnWcjZv1oNZSQmmb9SriIH+GhKYpgKF+ncZAG/N3Gf7d6QSs8mWPH4/SoySKrFyxzKNilGQs5P/Fq1m9v5t7b/e9FgyrR1XBm6+BMsMR+4GeFnnyAi/9F+54JM8TlOqLROQkfKno5GpAaPotprHiT31Pa2urpF9RXRPDBoDS4KLu7ckACTC56ijo4hdPWztpv5YvSASkkMDcoSpksoktmXJ5lpfmE/6+MJz6JQh9SE7tzFFGjDzlc/1UV2gScy2WLqNZ14SLMUztSWMKyrR+Mc90DJ6ly/MPkkvt5voaCWM71H6l2vMIOMaW/Bye1NUTVltsDzgnH1zuApHx+ZZO1jiH9asj6AvFrN/lk',  // pragma: allowlist secret
        },
        'cw-east4': {
          aws_access_key_id: '',
          aws_secret_access_key: '',  // pragma: allowlist secret
        },
      }),
    },

    cw_bot_token: $.k8s.SealedSecret + {
      name:: 'cw-bot-token',
      encryptedData: $.by_cluster({
        'cw-east4': {
          token: 'AgAjBF/r2UpRhC7F4Y7bTlzZt5CKEcxtF+AUsGW33fmaNaVa2ZoCO2xDjsUNveZfAivCSH4Bh9osAVkQWvNnoTmidoKiawloTGqpmNfVY8pXOp+4M5ARGoX6LbPgBN40/UROoQZ3awgsv+bm3A6HQveh39pT4n0X3l4hWsqLT0+rCivbUbbDCNjHRHhTkWwhLnxlyyXcX/JnLvfy+IE80zqeHQOVs/u+d5mUzytG3lUA6+wHx7J2TBCOuT5xWf69jMOReUlYTCGFLP+TirZgcyjyhM6th4yGThfsNGzUtr1BVvxljGISzqx7/MW2G8flbCu3ozziNmgtBCml19VBMBoQS8DrD9Bff5WSJF7LICuik6efC8LHdMh6Fb1QnqDKZLlPzfg7rUDHlFh6v4+IbH9nQRbfj0YxG+1Ixv5lDcTlK9SRLj/d3ef2OVMEha2zNkjrHBkQ4t9nd++O6rfIIzGsE8CQpUt8Rgbh5RjI2NFcoLxTJL6z7vE7qeswcV3GhE0QuhsqIOA6LShJhwfDTwbMBP4PLq1xke+rRWQtyhfNUlvV8RDMWYMx50toFybHp+hUIJIcdk3RyDgLkCY6i/APDXSNCwD4Fq5rUqTkS9ZZ3q87t25ovZzgoAOKBbVmSFtUoUM3BQW7vntxx/hgkHkPP8hAI5Cutvl2N5gDn4K3u73D4F0jBvVlHVJJ2JjLnDBnuXoeEN7gL20kduE/qmWDXUCtYdgCwublDAUKr385zmriet7i06xsBw8swwAizPvSdAHXa7ipWKk=',
        },
        'gcp-us1': {
          token: 'AgCmGSusIByiUTQIftuc0Jj4LjcpZI5UuAo7kIyJ1qhqU4T8htHdAB/EFIczm73DgSbq/x97hrS+MzJOUfju3VMdZlm7rMRR5gPZRxfpfeE5IbjULt2BZLmwQjX/IAIbwzH1IeAX6aB5VAz6WAIxti8p7nNny+P3XsvMcjC34PoNFLpJfs3xFx+jUPaMM3Xclv77NnAgRfKmgzxyBC2XG5x8TBJdRHi92WBU1FU64xunLKlyRAQK8tKOSJVTCoxcDSj/q+a/bQAe1aUvEsa3WGMNltgMhX65RxzDBD6BMLbKx3cpjXhFzn+xjl4m9M3fSliFiip5hatXAGSAcn+qf10YWmXR+AoTMEaRXkqekqe6LFe0YG0IZZohX0ctsgjvZt15uHF8CUoQ3am2aObTkM/gqxSvHwbSQBrAwHgfVnUSeMO+I7OX0Mr7GZaiRSP3LyUJYXmQEldHF47tMxLCWc833SaeR0/tvHv10SEmr6Alq4yjrI7FXHRg7Kk8ItI8WstFRuAwLg3BTj0wQQ+wjQwsQhQMAmo/xBUCRoWe0M+lTFf6Im19Ks6IOu88J8PHT9pxwj7nWHmf2dwXopwKtNvAe+0SC05EyyBllvesOhvWNIpnMp7MGeb6k/rwG88s0CRgSdvR0e0zdlTnPXCawDt0g9MbkjFWmKmrCRf47Lfb2KXX2EyExHWYojqgZB3LKwG8xghBrlhfm73N+qXGZdlsPWGYW47sm1PIfEwsEsIszTw9CUN+LWc4C4ksGN9G2fhwr7bvU8gXeIg=',
        },
      }),
    },

    gh_determined_pw: $.k8s.SealedSecret + {
      name:: 'gh-determined-password',
      encryptedData: $.by_cluster({
        'cw-east4': {
          password: 'AgAZhluy4tVIWrNHPggNIJaRxmg0tltYLiVrdlT+X3gp1WGyfEruaO2v57kHvqRxQ/S+7//hlRw1gtzZ0sjL6Hu5KIk3rQTYl9IbwIV3qAzRSEAu8NU33Xcl1yHqmK18JluW+VV+FQuop1NFnBFawHQ1PzrGwbedLYa/r0e3wroe5ysoufjZS52qHiPB0XPxGYjqwn3yE0LkKXZWHjHIbL8yMAzz1xXfpt/WeAadeMUU1YbWxPj+DoYB3Lp+3QYX7BtuQRRtByd49EDOZ8EaGZ85EKiCSSqBIjwY8IfFKBzAKVzq707xvcvBYZRLVfWRZf03suC/4MtTd0PRvJa/R4VRqMs4iep4ZtHZ5sq/vyJuTb9HyvhyW+GzooOF7UIqETMkrlAGaekx9uuV8Cq6zUGmbdFI3s9S6bezx8j9shtktyv8O5mradQHRHbYAz61n5Tzs5hqtx0hm7KjbCn/DI2elN5MEtH9hyPMu8PzKBxgqvmddBeqBKcy1CfSvNySNDS21FUHIwibjsTiP5nb3AvhZdDG0ys0cMResrLd8RoVo0z5Ocn9tC7+jYecLvlvtk2BINVI2euBYct7TofSL4bOxHO1X+NRrTcufD3QuWvyU/RCc+OEHZYDr9/cXwNviHGn+qIxPSA8Zhp57YCZl5GXCBWEoiC1LmXxaMBoZH5ZohnAb3PmXiDJi3iLGWOiv6InzVuFBOCP',  // pragma: allowlist secret
        },
        'gcp-us1': {
          password: 'AgBcNNWeAX5icfQ3nqqjASwgMrX2KGyUgRwpeoBm70D/AYO5/+K2w6yD62UuISNkZg0xQjtZjv2sANBYGf7NqN1Ir5jTwuxGdmZjua710c5B1qaiW7XTZW9gNW1QHZEEaas9+ePKxEjOTSeOgocGT2wwXnZuLYqN+tLFiO06G7vYQq4dVUqiOT/zp/P8LDcP6xEzKyD9Ud1oqDX4FN/1Ettpsy7hZvXcMuK7pOqbE4R7XvB7fgkk5csrjGAx7uEQQF97ttchr8wCPUK7fQUwCgJcZhgrX+j1qGN6ShQ1qhRcyG7ldtuWyJzckOBYWZ/V008/siYFbkbtCpjwEI4QxsWj6U+yIZWirOV1e4p8HVbFOPSnuBB+btoX4p7jeDR0m49V+UbQDKlZAzyCKNB6TeR6lzIf6lzOFsgY9YyLf+7IwRZ5/spxkwUG7hwdfWJAuE2n20IW4t652ktkd0BZ4jpfqDY2JA/xieuOJwfpifBci0VYyJXEd36OHz7ZUCxDKUkaR5mB8hQ36ZXsUMtH4Y4sTPdZ7nZCCE7NNKM+a1wKNU7hjt7OpBikpETktd0VP8sXB7KkGX0mfhJ7GtsZZ4dYHRTlIyqrHOaaSBDjp9S1uYwhjsBjiSt+/tldmE+gAR0SNYTrOD3SWqXR3gqfITupozoBCkkC+Lt5rQp5DiCk2Suy9MuLjMSsdDdkhsZ/Aq+L4EjCwD/Q',  // pragma: allowlist secret
        },
      }),
      spec+: {
        template+: {
          data+: {
            username: 'github',
          },
        },
      },
    },

    eval_token: $.k8s.SealedSecret + {
      name:: 'eval-token',
      encryptedData: $.by_cluster({
        'cw-east4': {
          token: 'AgAi1OjKRjAuoxzNvmkhFkcg6igDvNPqgMR0g6oPMpq5PONQXCXjmrDyw3T7iLQ0NjuVhkd0y/BwDLBmIQt6oHi3wCpuymHnxtOG3dRZqgxPttclnNbcLnPofOFTPPaw+v3ZyutjHm+LPg8zeny7eBgyXAoEb4z00VHpIdBDLkz3zcv7s7rLxt6qUjxpzJFTThK66TtwsOnNSV0ayQFLowMaT8GxuSKNAeNC9tMFVMOwO0vzDBj1FZ3XNjuRl4SIdAwG227HTvfSJhSNxvJfJlF6fdohTj+v85CAVvrd6xdVxf9FvfJusfHnkywfCUXpwkQuvqsgKfU6IrTe/mDgOQUfx3xh2caogoQ7/J6TImpgME46fb//+j+UcvA7Lo7YvG4pUwi3XRqjuxmwBF2eipUL5OfquYsZJKgIubALUzWmi2gjmZhzWcA5erdh6Bjp8ODbS3cBXY5Q63bIon1Zh9fyQdh3jxTM48A70eTHZTE9sWfPB0YahxTKP/14tj6HTOQ3eqvUkyhOeI02fmwv9M+FPEv8LFv29qXaJjRatfzaT5xcL+RkZAZkop9puuflnzRqFzn4htDsCp1XyycFWhCjUeq2pAN3Uc7wr8fPRpDu8LXapO+6mpZ0No/RJNpSk3Ghu3kp9YchAViqAPi/HJpYHpHAlMK3UdIUsigStLm1Px18ycuBOCTnm6a35ptHsge+D4b88v+3pIK8g3syQGhcwx5Y1qYJ/yagum5uCKbEEKs5igI=',
        },
        'gcp-us1': {
          token: 'AgC3l4G1+Eg4SDxs19j5p2l86Oa3mag4Ksk19DFrnQX2lsGhYDrapZgBmaf+CASrtbcsFhavkWPsGrNM4BM6MTsKsAbJGqlndsqFZ3UyNCrIbdmspSwPFSjHx389NZKnnAT5NIXwFr1b9+3JHsj1QAh/EKPHUrVdr2Ke7a7bL79g2DnnT8oi2xIWvO4feOD3WtA+zl3oWng6hbUPunF9MKr74E2dzY/7PAhpeONdOfZ6AAVv22Jsb+gNgTUEbo+yS+wcuJA/Tv3zDpoyRd354sHbVYl3XGwLl2Wxs9l1RGf+ecTFAwzhOA+GjXjXizCbcL8NT0wguVo1UcyR0gaPe/iAOQ8DruUSXSXP0f2lza0K3yeS9e3mieD+ZKMPNypBY4WenoSTMdoc9s3DfZkfMKA0oZcaz0BFuzm8LP3WD87oKlBmWAR6Z5s5MWNaDq6QYI79BIyrmbFzRFn/wK0FQw1E9CjQ6G0AmIn5kOuKXVVgp5zclpVZsAq1bii+A9bSr8SwoekAhXokB54OKlGXJZxaGWFloiHgGsrojOLkdKSzZ/iv+k+8gsEhoeogd+AKYZbaV6YpBPTjuL5HuxSAGKdcShnXPpTC/NRKC2+GAkbsyKy8tmGtLmknrG5l23071z/xAG6SSTvCCNZ3c6HeTdzrnT17rfSAru7oqFIclOaFJLbegHI1OiKypHY8WizDc/rK3Cgx0WHpXrI1O3vaV+kSh6mcu5HJ41vlEKi9zbkKJVszr2U=',
        },
      }),
    },

    augi_release_readers: $.k8s.SealedSecret + {
      name:: 'augi-release-readers',
      encryptedData: $.by_cluster({
        'cw-east4': {
          master: 'AgCcuRvMe+oXzSheMyOV1KVZgyIkOPHmHqawwkyeB41V41RyR7vUCRxLsoirr26wHUuKqvsnwB+vApDsEs019CRnnCLVozTO/RPhhAanaLKm2IZUoRUoZh8ItiDQuVk87MmS2w6jjLU8mJjVE37jwHGUmTAms0WGwteVofMU1JHcmYlU3jKv2vqotol/yLSKX9REyOs/apEARiSSJHW8Td6JA0daZu+twpg+P9wNbW2sad/aE9xvEJRxYCeKzxpNnxKPunXZ1TV8yziK13o5yGHd0vOwbn9wzj7TX8YzeFt7rcq+PHEXpZRSNC7BALa7pYF5ylRrMCxrAGQ/N1xtnQ70VlnK7wedohTGVTZehcYCwpnnfXgRhjVaevE2Z6V1BLf24kLwrfZ2MyoLtEQNWGexh1n3lp1L3ubGeSltJXeTfYkUHoGjck1zbg6xtFU3UzWPnug8kAAs0qv5kHBgxwV9+iJWPx+NSgWBP3mbfN0WGETwYYPYNZn1IOLYs/tJZAMEapPJogvu14q3xUjG/spPlw7S3ZJe9OEIx+1shoDESJPa1tDUcMJRLhuWMgezqJmuPPWWLU39UPETxdC1Q161JMKtX793LYn/d5iNrmZSHL3HXBRtQZcHNU0si4b2L3ZjtSHN0mMsET8BODKc+3YojNcY+1SoXuySZclKxl7fC7EcJz3hQagtWwwz+EuIGBqYgVT+bl7BBgJn1vJZZyJO8Cjf53Y7zaO58543iGK3GA==',
        },
        'gcp-us1': {
          master: 'AgB8yVSDznpx3wqdJtclDvYc8DmxrlvAtFbjPE9TsUHpEii57qS9RrHyzJW5bCpKwnbX609t3RTsW6xUoygO5wKHCvlkE2Mrbq2gscT7I7dAak7weEU9Gh3QIOTteX/UrpbSKXca+W7jJoRq9FuNMpuSi72MFg7Q5dB4Emthj+5dhgyUU5q1U4LpMpTYRQFHr/iXJcKHxSPrx/2uhDpnN59aOvASEBl6pUx+IvRI9AGzaxoBGPjtVNji5zv6pXQ0clC/tHEhf4XAvZbEZZOlcCSVvSoq+H3TrTaoc0mVR0IK3Yw2ky0hDobfRjQ56A0iXGOpjRVqGCkEBxS2bNLZ/UkQydalrOyRZ5y8/dCfIR1nVZFLPCAvbtTMcKHbp6573uSJqIoyqHvjEO/MhDPyyZZGr8VH1F8ZJPBZOk0ydAl3lEM6T59c+coiRKzsjSjzAxdR3vsUUQMnGl0qwpHmFlZssrmvMH49zycpqhkDs0MW+NLofe+AIJ6YyrgZ2Sw1S2hTtfM+mznG49AfW7iAcflv5PiAdDqhCfv36JFPZDVAetNiXDV3EZx5WbhXxGYbwpGG66I8ESdC9RCzofDj9QAZqs7BTeEMk1kV8CpfpCJV29Ui1u/eCxUbNX51foI6u34RZFPn37rfvZYqDYfpUEXPDmGmLpJKNlF+rIwGWKaBZs80EZn+JY32YiaqqarmTsBspICsdspB1RxZBSft7RRO4X0sa5zcSsc03iH3P/NhIA==',
        },
      }),
      spec+: {
        template+: {
          type: $.k8s.SECRET_TYPE.DOCKER,
          data: {
            '.dockerconfigjson': |||
              {
                "auths": {
                  "harbor-master.r.augmentcode.com": {
                    "auth": "{{printf "r@augi-release-readers:%s" .master | b64enc}}"
                  }
                }
              }
            |||,
          },
        },
      },
    },

    registry_taggers: $.k8s.SealedSecret + {
      name:: 'registry-taggers',
      cluster_name:: 'gcp-us1',
      encryptedData: {
        password: 'AgCEpgqsjPXJuOM46zmTY3b+Ars0eyrCd+fncXIVyuTx+gfodfynxABvHj4PbcvtSl/cJqUMRI9ROt43AMi2GiHQ3EGiJiVnb4YyLpH+xpMjaDgjTeVGm5h3vDmkDLnnllsiy+AL9eC8ndrufBW0RMQ/NgmoSQrBEk9EvkgUypcp/6wtkMJlz+1iMdBuYiyfJcGFNHAw+w33ONnJPP24/DM7IQjbomTDsJ+trzJdB+hs5k/R4sE6aOkvpNmH2M0d1M8zIEo2fml2qYPn/97vu/qZO6hFE7b6jn+BWtJJlIKbwPFOE9aIWnZ2c+O17Obzl9YjpdmdT9gkRVCqx4ey7QuAGjAW7hdhGwv1BkFx52yeV8QFxihcgW/vl2wgpEsspLJ+1S9367PLJ+6cny7M3azApLlCwUAIbe1T4mO+kf4VJPBd4uTbZ6yArSc8dizhreeWktH3EXmnFfKVOu1JolHRughG1pdwsbQYtD8KSGcKANX/1z7AT4rX4DhgIsIxEFz8Bxk/ZVs5x1grrazNHWwx3ph8MLlSleKWRVOpS+1kiXtaFSFfixY1TpLIzO3e+d/t3Y7L+g4qcC7g5ANo2IArB56jc+srsyutzi1yTgzjT3iTSLvlpJiJQ1q0dqtt3m43P0IhuAw/sl6YPzRjpdkqB9btzpELfUeFfeKsXesXNfSLrlyD4apAWcqRkxFzjVfU9VaFHQOYA8gzIbPIWsxiC0gT12tchw9iuRyq7dNNtA==',  // pragma: allowlist secret
      },
      spec+: {
        template+: {
          type: $.k8s.SECRET_TYPE.BASIC_AUTH,
          data: {
            username: 'r@image-tagger',
          },
        },
      },
    },

    registry_readers: $.k8s.SealedSecret + {
      name:: 'registry-readers',
      cluster_name:: 'gcp-us1',
      encryptedData: {
        // NOTE(mattm): These are raw kubeseals of the "password". The usernames and base64 handling are inlined below.
        master: 'AgA3xTHm5g1bxQIKUZt+rCh4ke35asx8OGn2rtnmUuVj8WdJhN95R8YQnsHscyPOdERf7Ao1Op0Wo5yk/wqNFmLS2Jdbuppo9VBpKiHx0FQUnrATdj5Hl/M/46LW+inYvMrMfkazJwyZKJAEI7B/wZhwH7ybPdno3SKGSZdK8vtNw4JCkPpYFfnokl1OxsbCO6u7pDC31FTKr6EYOlMCCiJTC7af1JxykkXGIgFjCFoY48jMbhyOvhUd5MdfQn4khbtsAaKav2GD8esc0qoo+2o/BAWQ4dxBGt8WAPJ1AE4aMFJM+NUe6pirMb8ougdTa4Daifhp8H4jovSL8Bp4FA7WRxoD3MlkrZ2DyayJwGb2lgmqK0r/322vcySCTGy/mzXr4fFSFS83P+R86RO873FwkXNEm9VA4l/9JLkHcATvs/6lLxT7Eu54Yh4icp+s4uUpeC6401ALYJjUqsS/NpHxrYBVn6OG8E4VbZUByZc3vUnE0MtDmZku72UycYwh1sTqrZYp9gOlOXIY0IRmy43iMOdy0b/jJO9SFvnwBdmXC6tYFr48cgdIGEgaZVxLXxs3moUzI6X6m2YJ64Hiug+ndS097zz5TWJbRLfLC4MS3hlo+RJ9Pt+dBK5sMb8kKnt3y6cmXKbp79mrdHYTgS5PujV+WEfq32W7hD8L1iQzdrWXc5LqV3WpF6nMUja8iSPDRe5Q//WtYguAwXRatx9QaN9BWmz207EPGlMG2p1fEA==',
        cw_legacy: 'AgCaQFWoGi4iPSP0uq9gk/Tnw+vUanvlhUdl/9kZMg7AOP3vDJqrIFTYR73aeIfYwC2ZTbnYxDKiH1Y/bNnSJX/+syE7AOwD/dFXooX5kaFn9nF1FHHWDiDce+ZoMVgaQ9K2na6qfFaNRFh9+7ZQ2Cb/YBGRqvaqe59Q/ZQluxqvLlHN5HzrC7+UvkvzlXzDqOabWLiawu/SSrMUzo64QuVwqlqQzQzemi7SnPam8WZTXJaiRuxsg8NC4HiciFrdRX6bkuuVcjklbAZJUjIxpri2PZEi0wN5j5fq7ks2J/1RvDCAbsKbgL0B3XCYQvZJUkJf5j0Tr2saTtGorCmweUFCeEAXDvKX1Wvskrs31uDgJrGOgzHwug5RES/nWe+ax/pfmlF07t0RhOeOGwsc1RIu74XfX0QqMxYNWyC5BDaHNSJVhersFyfsCpVNvZUzpFpL+vWS2qQRDvxb8bm6lK1oMcrt6V/h2BURN7/x1YJ45xz89yy0Qs+0r+BmGNM+zoz18M3Hqz4mk2BAnf+JkxepiMsHqpBEH7+uozRmVtCzWu5F90l0NkHwliMjCk5eECQxu8HsUXUxYkm0XgTgciyFWQqXwqskEvM8nFTPaDc1kFMGtM8m03H5EMZbAQ59pFulzv1Mb4+nK+Rnm2aiMk18UwEF0pGM0Y9HHg3IVMODIKystBj4v8ZkG3j3RnLAYd4LTfYvKiUEsWNDAHbLul+l',
        gcp_us1: 'AgBH5SqSjy7Z+AplBzAFYU8lHAdsfsYN2o+yQ4I6D/R3v79JqetDck6EXh6D2s9rAZ6KpautXlvkA5JNL6PsKDhptMCQMkUS1bUhmeVE/suNiDG046LQrYBd8vyeTgEqGNqyQ5eeX9eJ1wKZveb8BNEQx+8avflUoRAby1I4kSvkfTaRvPG4wYoKhB+n/J6V45v2rf0w8aaa+VSLEHVmqSz8/eGIMPka0y8sp/l4pxmAKIlcW7cIExfCUH/0b5D228zWMB3l3VL+sdchGwDK6aDumQMpiSmJ0oB0hN6Cj8kxYxlHBWq0h0QzLUWHV3VBqe45djJak4GNsvhUNLrxPUlVwU5yMvAp562D3FFvLbSFQobgQkXo4z/HoO/BxdPWNxBfZqbrxyQw1t9o8w2zE4ggPDu7CuLsKivjRwPtnQfnsCzHhj13a4+KpCTy54OdCxEvyn1jSXxyrEOCE5Qk1x9uXnLSgq95Xkqbbz6Msb39g65Eeuh+UaYa71XCFDFA1h6ti9PFnHoeQkhI/OtZh/Nq0spSFV56Ee3zElPekx3HhdPHlsjuJQ0s3CCylz4OzB8dq/yu1dOYlm7a2Vqqid04cC5VMB6NDE6dNIQEntBoLHtt1LUM9dXWYaARLXIaPc+r5k955mL5isrJZLcLYV0MxI0m0nMcFkdr65Vmmdirs05pIl4TRqZMvzXpnDJ+LxcvSrn3fVjLjc8pU/e7jQwDow+qPXyniAkz04XLkhxJfIdjTQdd2skP5ith+TLqTV06Y/cEkMB4H61PqPkMRxOo6V9A2dXPykUeV2pQ5dl903WtrLwpqCIiaN06mHAIAvripJSlsww/jhx6h4aFCOqQPiRHYre6yyMAWgQ02uPrlO2NaOa7/kRs9hozXTzVR4CZ6qcEtSqaI3l4C7yZ5XYaA+tOe5yMosvT4LAQPhm6knjYT+Z1XpiRD/PTZbO8v8AKul9jTahz50Kj3ImRPtq/QD30OfEurnh7nWMdQrM9BNCYAfNelt4EiKaNBQCzNof3loRFwXWXSyQhlDS9bCqCbVlQI5Lx7HpcWkR8xfUB17Jgft7Bk4l+CRDroRo/2GhVRBVMi0VRrmY9T28r6g8mhmXLIoRQZ+pXYnXe7OQhjWomoq40mIea1WCPrTD+ezxCXnb3sYJ50zShOgknvGkPU6KeS5b8PGcpCFS9DNvCi1Cf7TeoyzNgeQj+MypWrwvHN7HslsW0lfEAYNGMJLb/ZZ5UMMI5FY2NIvyl4qC2jXbHsNcKNrbCRZwuEhgkhYhYRvwmuQqNoKGcXWYvpvfj+iH/dNJcMjwQqcS8fh2zTedXBOyq6cRv7xem+m6GuSgMn8cwgOIYV9ATFDJER0fMrl93lUyDmmAT/FC5cAVzdQrQtvmtFDHVO1DoTsH+ml2Hke7TBf5hWz35+PHJ6/3Egkk06FXjLeqI47wHF42uuM1bOdQ3/QIvhp1fJm/zxG6yJVZ+OHwfJrQF8t/mZNxY9lligODctHgLu3afcdq2H8ZDDh0IXuSy6i9lwDosN9mCB8wECJN4yAnmnqHQMxgP6pNIawtXqXozc89xZATkCkmS/hk5uUz/jeTPHVQ12ZhxnsDAlg5xzstyKXIMKsqvP5Z87bzMDaesCkZy9XmgyMIsrnfSX6xpNilFceqRte5YO2nAmB+Kv9MV/PtJBgZEtSZECGOFGaeXFMZ7FoTix2/zhCee42UyHQ5Sx1j6gmET6WtDGaNPyjEiJRrsFiyTcwXbB5JnrrS4n7dJn/oPdLBQquhgmgHR5ods9m714UH7/EeVAGYVHSYJHOb03UydxboQUkek6ChetLCbM/xKth+oUDWaJ2hYFm/VuVEFGQZ8L1MKDNqbRmdndfaxyQzzIg8dQZBeZV+PoIQtDGHe/8ZQU9zJlBp+9AK61kMRx2n7vagyeDccdfQXNz/Axf3vIlD5oguuu8K6XnbipCnPyMTq8BvWeNZcBiB79nlhcKPlElH+AxroMRqfxthnD4os7UQNSFXeIzI5l3JcEflIVa7oZcSGNv0KuBma65uxXoKOOq/bxKbLYenD96grsuXKWjE0+VACkCs1GhrrndhtyHsnIRlfQW6ewcdv3q0iZDXiir9q3qwD0k3VQe5h5ZMTafXL9IaY8BoUW16E0/mTVhbH8TrQdVpiXVhjljmlFLzdAYikoR668+vxi4uVDJKI5B1f9tCLrDoyI3GqRhWta/LofoZnNCmoklvHbLp6Nt4iIrHV+/vqL/XXeU2aQxLu2eUD9W6OkgvyIXbctasLRC+WrqPAcevzfdGLx9uoHcN6Knvx5lQyrHuYked+uK7eqbOCOK8UB5BnP4QTA8XKSEH766ecloveplO/QABKMemj10R1y3iVvCD05DEMcDncVwS06wpqoyS7VT5zqnNoZQYJrVIIkkj3V7CGDaEsQZU44BiTyuxekawV1AHMYOhI5NuZFQ6zAjEjTALR0eJB3WAE34QDo9rHUa941LmEpQfN92ON8O1BK7N9jHVs2JZR3LbqUHLxkI/blUZ4Ur4+m6UmowgK3p+JdtVnvIIBGSrB5sTgn+U6w5iYeYCKGIzcU+6g8Jf8EW0Mc92hFYpk/iFp6hYPZWL6Yk36o4vs7MeH1Le/CI1/fszUvdtuyEgv7Wza0SI/oyanuFF6J6Dpxln+DT8FXitaymsWG+sysb7K8WV8NkJwcbKTFEE6zaTtSrXcUb8DIO5+0A0/2Jf15KX/qdqkWUCsNZiwwLAfur0A1ROP008v41svyXvWcrL+bFXTMaVHlAQZBuqvAQdl57qjMkb2wHKR2BcqlbIqHJP6GNQjEgDkkKdK/wJqkRg5k6zFOknoozm3r97cnMdhk7ZSu22bkg9BNA7ah/yuTLHyzcuf4pLb5LarTG9QOAlk6rXenft+gr8W517QoE5WgPflPbqf9Hpa1Gi6EVB0GK3K8b5534O/KZ9qAD0sbSF8p80nVnAXGzTPZsCfBXfzsDcsAx7VbhcBn9g8I1MbFYvl6qzcPQ3lpR+8r16tK5//6XYCIdGu4d7P3KRpZD8XNb69XlMXvvmU3rWA10JKfsvcOo5Yghv31EqYEi6MbW8HRdVMvgQbehryzT7NQ54t8NN/GshsbLN49NbkoRJfsKwt3XSsdFo/3ycUF9uMv0kwBSS6uBuZIGT7AhIhDSiFNvsYbB5CtbDFsA7H0YAI7zjfQcUNg6UZhGaZxfmP0HyF+rDrGqHoET1EhZDhArvBmeJXthU69g9E3OKN2/2EUfYfr5QOiYa22qRy3XDvfFAS2yKiW3BnOYmg6A7R39Yxy+5d70YYZaCN7TVmSBdIVHpukgFnDPC3WUlcZXA93XrCsErwVej5r67Jq1+a2MHdqN7Cn74OWZJ6NKNoayxpuDU6X3o1JaUMeGoVMQgU3FcfYsRSqgOtkGsF5mNCRAofm6xa2sgHpyeTrhrbTyRgNRsOdD2nmhRsuC+4zyZQrYQTligg0StK8uxpn5zJFhr7s6tNrHpEms9xGASqq/8D5NuUCCHdtjPaZFIGkw+zTzFlBjm2b+l+8fYXKzF079MXV6Gfam35/USEM9ttTJh+vDHRu+hYpQ2SdswWzb+G/ccSEdO/36jh5ujuC2PfPvnR8p1gdAGJENplkLavRdUjRh0RSgDEdTGfSryp5FHAL23Wd4T7nRlv0iJPtcDVP8FpJ6g+B1SN0m7dt1HTYnmcO9UkDpR+w9qVSacnZuqrAmwmRlx96JsswJ9D3H3xafRV8kZ6LDqYqhmg1A2lJQWuZvc8ZUX0Eq4FvNJ/xBqotqJzH/AHaQWxYlUKcrztcP4hyo/DXqU4UeUfClqXE4PEun6dmEBgKD3dB2U77eNkrMHGkQ/1ZayH5P6GdiaBSYdpuO4oB1NUIdacUaSivshYeeP8tvYbFB9BQDMpnbPt5VBcmcD7f7GlX9eow+MQdmjO4T96HFk8RMtxxIyUMmk2XefG8Xx3PckoZnrdXEXRRzvDuEqYxXMjLkQZwPlnLG2polFQa+KTUo5g6IERKLZFFabhor+OtSZZciClTIHUuN3RzJvuycTWiTr/T28n1fWggswDIZWjExDiv/o94dwphiqv2+eczZv4fet3IQ1RjnhTuyF5SvZFlUv4955eUahnqysQwtnWszc4GxQ04wvHB42vqeJcrkp0xMpbOxalxAJ+E5WOW9HQ/gdLSn8ExhLQqEcoLfj9g+1nxSU3Hh0Upj0oSr2IzVfZLG0Ho8pozBqolFBta7L2co5AB5J9AgxHBb1lEUBtXPGptHnFmFqvKF0u4p6QwSRep6WSlcxrPTEeJYPAGPtsLDozG9OhEQeWoR5XwjWe/rVTKSLyu/pwdlVz2QgpEaBIR5RNWN6iysXIxK3slBUkaT5rKxRszPPb25s1vuN4BsFWVIDAyv3rongyn/rEu/aw1d9ZnntknhCWzj/YjXjbilrtu5y3Ha60hwiWF8K1vdhnJiH7Pto3otMauC9U96xbrQDimC8CEkKSENuax3u60DfZX12W8GNbxpuktdAw6pn6+JvLrMHlbpjWd+ulU6GhHtvS1YSeQokxkYKTOHcyN80BlUofOnLXp39Yup1uSxxv+pxDM4RwsM6jLr3zwix41QNFgMBS/rzpVf/2/4IudYokEXNntpxoY4P11+EjBse9B9dNkkSNiqF0dvbv6xbq3EykR0o/qgf675nm3mhBEml95WJ4+PGYk/h9vcuIqGchwJETzvYB/WmghrY1aVuPYarosoACr4p/ZYfpYlL/Q+9n3X9KTX7OyikDUDqthyeySpv67FGM341j7JD5v9lNhWPaKNX2fQvi58RGwOI//V1u6MsWMASY/1EueenlJiX3mvBjPNksbPxIharPcrErlz3mInbNUc+vWGxjnYAXHqf266NXS9ECrfTvkp/a1J477CrynMAi1cHQ+66/31O8E6UqAhjP2YGtVB8Zg1YBTnx95W4u',
        cw_east4: 'AgBoKrF2c5WbGVhST/anTOgUhihuYQYuWrM2VimT5pEi60PorDv4iMLZUT0OF9NSQKvf/OdBNM8KRL73HSQNpSw8d+PiIyDyJ/ZpHbqXck/0gdStvDxhbawbaw280y+63AMQ6OZBDJA8rZDWwH37HcsV/V3QViKABgah7A1L9jjLvb5Q7U88X8aqHwXBn1WThI1GyOCv3xpvjMqqenq5ATazI+MfNTG0DZaU0eKLgpE1IJY8Ck+Pos+zed4AsGp7VeFArzl8H96ClRYQ85ke0O5zb50d4ft0d9jlsbzMfzE27WaiwbNGZOqMalUStGbsxgM1mpJ2R+Ewg959wLz3/l5C5FiUyAc83gOPCXT8UW15XW9GVFrwxtRt3xnuKtLu2He4xqWUWp2j98LExt72Nj9vE90HRf7doGbIVQsz0HPXZ0JY5NIK9Sr/5kG9KESY/AhCDLcJ9kop4Z7x42KlZFZ3t/R3N3RvGRtrplkGOOYFYjvaTeIM8OKwWjuuwN1li5NJzrKBSefV1ebUAooSVbOgS3Q6F8j1wmMUu7feiIv18gDiRRvp8KaFoJ0pxjwVrD2foVcWTBsAjSl1Fgqd0la6HBomSW47SJ1kR3lBoz7PgOqm3TeStR6T9BaqL89akKk3J3ncxnn9NsADlvM/Zb9qKXJ4yI04ZTybdmOIr5pYgJICO94eVQ2NBJqeYf7EOPpxvkK+BcI66ABIOHKI+Vnqh40yUQNEzOPtIoFiDSeLug==',
      },
      spec+: {
        template+: {
          type: $.k8s.SECRET_TYPE.DOCKER,
          data: {
            '.dockerconfigjson': |||
              {
                "auths": {
                  "harbor-master.r.augmentcode.com": {
                    "auth": "{{printf "r@registry-reader:%s" .master | b64enc}}"
                  },
                  "us-central1-docker.pkg.dev": {
                    "auth": "{{printf "_json_key_base64:%s" .gcp_us1 | b64enc}}"
                  },
                  "registry.cw-east4.r.augmentcode.com": {
                    "auth": "{{printf "r@image-pull-secret0:%s" .cw_east4 | b64enc}}"
                  }
                }
              }
            |||,
          },
        },
      },
    },

    infra_alerts_webhook_url: $.k8s.SealedSecret + {
      name:: 'infra-alerts-webhook-url',
      encryptedData: $.by_cluster({
        'gcp-us1': {
          url: 'AgA/1dUh9v6Uxs33hMVbcGUNq/L2Meff2dgpsliqbeZNzJRMwx92caaeVCvEWohKFA+fbHlJXv/TmGV5W7NhXnz10SpryOjm+BX3OGhWMXRdvFen2oGaTNRqLJf+Qx1qnSwIPIF0OKlSTgoI6n17bdCA1GMc7x4zT9UDuCOKL0U99c+t93GZdMxvYstEJkZJCbWIO5/uCpVZv0KHyzMADP1HGLvakHPdLOknxrFC/FrXPxdbu0aZ2pn+FB5ee8Mrte1aNVIAN4s2v8ky9h4a4GRyBEROeLVnmbcDr1YdbHhmU88crC9kmumMMjTopcnIiK43v7Gv12PC9GmdOFf5WNoT5BR1Ozz5Jf94OMfloOUzOqLmykYspesYf27OMUYEfCSIh8RDphp1fsWR8Kx/S0UStkCXV15eDmGFKboGKZDiEcIY9qMQY95pWF8xwq4qOIDIfc6FZO3Lah+IRvC2iD/1pi9SsBjBFzEzOZmGVOkvacTueTUrM4kE0RohPuxh+zP3xftdGhDZ98eeEIPKKHtN/mCZAomjmaTqO0rEy/6GKeZJWQLbXrAR+JusmxFId5Z7p0JUWs3E0MBfeDshsa1Ew1HDIZZRqozosbxiXtnDayff6X4q0j0n7PXLJgdeKcwXQDm98Smxlrz+ZuHEtSdgpu7MsiIn4FPO6mgoa9DfYLnhb4ZERSh3rV+OAxdFxGmLOmAvMCNFwdZQOFpJiwuiOy9hXZ0HtniUEYL8uubNjyzQmP2WwezKEI0PYSoVmGDQT9UUIBJWYbPy6c+2RvBvSzfApuGhmOV/RSeDRNps8IE=',
        },
        'cw-east4': {
          url: 'AgAj0fxaLszuuMj0g8HCZRLPhJR0JksbJsETc5X8DyBjmNk9Y1YCTFcUciJ4rgS68Vs6n6+jBwPiZXBlm9/BtQbAD9U7ykMLgXiPF9k34GwgwUXbSHAhd64zQZyZCH9p9+VPc0royfFnauvHRuPFqGWVmaPSbAN8RoJn+CHRerdNe0vHhHWWMADJE38K1KQ99yTWlbw2kY/ZQfudmuJr3Frjjfu5D3tWFigFuP/765FsEmABn4tUl8ZucObAbCxL3hFzwx8ns/q5xvsThvuX7oe7GwWRjtax3z5qEsj96yJGd+6JHvB/1dlQyCk9jQ0DN1skZ6yQcyUAr2Rfq0PVza4xROAZtJ1/GFv46sSsunmjQHFttyimymXuWlriNPOv2BHMCEG3Scm4XMe3fuM//BprDZjMTP7x110X4o48u36XcCLalETE88kbbyha2plgx1yUXdXFCc+dAXMfh1q32klmo/YbpO6wAciqrG4eETIDMnHSQxAzQkwu0M0MvuioBdlzMQcUteOJv+sICLGW4Sk4xVRr9Hs3Jbu7MM4rDQ+couu0NUSSsufC9CY+a5NRp3yYItP1gkSdzOXXmwEyk/KF88BfZI8g3LTcNVUHc33EGFDPehMr5d4U4vtsG/Q9GiYLHgSlH3tiqmhtB3EyjH4f+TKIGAHB1QKwkipwCQlCKzRIVx13snxmpvDsI9Ly62G/sh5irjnhogCGxqvcrc2RIAlZBKaxqH8uzCxbRQiPiD3/igGNfODwAXoGbsl3Ywa+hlqHsSYsVr/I+zpeHqHXD/YFoLUPfNeAQhHKhW/qZwA=',
        },
      }),
    },

    github_augment_eng_read: $.k8s.SealedSecret + {
      cluster_name:: 'gcp-us1',  // TODO(mattm): All clusters after we drop CW
      name:: 'github-augment-eng-read',
      encryptedData: $.by_cluster({
        'gcp-us1': {
          'private-key.pem': '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',
        },
      }),
      spec+: {
        template+: {
          data+: {
            app_id: '880196',
            installation_id: '49767043',
          },
        },
      },
    },
    determined_db_ssl_root_cert: $.k8s.SealedSecret + {
      name:: 'determined-db-ssl-root-cert',
      encryptedData: $.by_cluster({
        'cw-east4': {
          cert: '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',
        },
        'gcp-us1': {
          cert: '',
        },
      }),
    },

    determined_sa_key: $.k8s.SealedSecret + {
      name:: 'determined-sa-key',
      encryptedData: $.by_cluster({
        'cw-east4': {
          application_credentials: '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',
        },
        'gcp-us1': {
          cert: '',
        },
      }),
    },

    // TODO(mattm): Switch to Workflow Identity?, maybe add to cw-east4
    aug_prod_cw_ri_import: $.k8s.SealedSecret + {
      cluster_name:: 'gcp-us1',
      name:: 'aug-prod-cw-ri-importer',
      metadata+: {
        annotations+: {
          'service-account-email': '<EMAIL>',
        },
      },
      encryptedData: {
        'cw-ri-importer.json': '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',
      },
    },

    augment_api_token: $.k8s.SealedSecret + {
      name:: 'augment-api-key',  // pragma: allowlist secret
      encryptedData: $.by_cluster({
        'gcp-us1': {
          token: 'AgCmKuLqAf/kVw0+BWdl7WlqCCL15rhn+obfuQRoCGH/dsUinyLMAXeCr+6tBw47WdRs7Q1fKjCvZhC6xbWeazI7nIiljE0jzE3kYUGCCgY3wgCWYASDTzgCbd7x5obM2gPjFDMlWNhRuVJ5MEUutJFgx5PmaMTgMfGTmlr9Ca1y4+4oOkofc2PI/qr+aHpRFLAUnM8a+eMSdasZXLFS8onJ6mrJSQU5CBT1gOmgJod4g1tBftQuAux8t/7/0dpME5l3AwYRxQIA84ii8vQzRR1PxKieaa+mckUHTtzSQBRSyh3AkGhAPJPdxqFju4frNwNOPqs4tD1lt1WyCxwJANG92KSgCFR0P+dMaBN1IehW1c9D+QYSc31S5JpxBEej+Mj2l/vVrmeJWMPX1zHdq06ajue84y9A/ADYOVSRP8BcTKa/LteAyfHcccpzj+6Amu62EfZt17HMPOVtBm+0laxzJyCAwfjhY4/OrEoVC+tcMvo/eCFMA08qabmt5yS5D4cYeGCk/wRzPz5UkqcmxnjX7ZVNOk6HMJex88bH8bzUPctTrreGLuZGXinOgrVxs1nYcYpmYaySDGkLii9K3QpzARI/T4lGa8bnH2InNadWqp8NgkIf6sD5zT9KHIk1+hmyjATEBjhzlFWfoixeEopNlZuVfgHHKSROW5qzwJHXVkqBgyzYe4AF3JZgIoCmHh817lcAeBOADRw5rss5gX4VzdFsgmyPLq8b+yFSn5JcVlG1IX4=',
        },
        'cw-east4': {
          token: 'AgAuwEQYhayp+wFSsUqxz5P7CyyzRlS3Icb9g4AG/gciKsRV9LGgOlAG30hvDh4nWuPydQ6IAo2ms24TIG4dhHA0nMRdORskSofTKVigUSad2aQMgjE7UWoVjXmtopKXjlrmv+oz7beyMitB1/kuB/jsQhK/+POgBGmckeSM5IXxsS2V7PRPnPZF9t3H0GBXgpToClOKVy6dMmKOZubd1Bk+tgvo4YXX90pZtPfXQjb3BrjbZiFkIsJDSEqalYEtX37HOMKisvr+eDceqIOwnjiFPCqkmRJVvXPgwrkaSNcmwRq1dTquvDfA+fzosBABVgReHhdXN+X3JCxNZsmni6fbQiav60tkvXNZOlI8sP/KUCHKirB7HvekvhWsonRI4Kz4fRdejYRhgeXTyOMQO3EwmfyjSIztao55D5Eimn6x/0aQKJDxm/QMoKjQSifCahoc4NFFoQg0mluxC8Q6MMIiqtNPr28RgR9SnkHeLLhvBMD9qvInUr/7HtHJurxt8kWNdOsuvIE8hSDDnFbdqIfCnhrRc1hzQ3wSrxxVlxeg+9lECP0b2M+yKxTf9jXyImiy8KcrmgPV5yMyq3s9hIjDViZvl3ic2MNwlkCfvy94z8F9i0ZVi7AT+FbuGn5Ml3DjDE2U0ezlVfyhnFdIGEWIe8dD2HFF5OiBjz0zUhfeOqcu6AjEXjj/JyOObVmqAxBty+v/S5aWJojSWhhd3FFfLsSkeBUxvJeIkqbuw9xuAdX5d7c=',
        },
      }),
    },

  }),
}.ret

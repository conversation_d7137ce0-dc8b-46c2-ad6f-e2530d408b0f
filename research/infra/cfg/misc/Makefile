K8S_CONF := misc.k8s.jsonnet
CLUSTERS := cw-east4 gcp-us1
KUBECFG  := kubecfg --context="$$context" --tla-code="cluster_name='$$cluster'"

show:
	for cluster in $(CLUSTERS); do \
		echo ====== "$$cluster" ======; \
		context=$$(jsonnet -A name="$$cluster" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop") ; \
		$(KUBECFG) show $(K8S_CONF); \
	done

diff:
	for cluster in $(CLUSTERS); do \
		echo ====== "$$cluster" ======; \
		context=$$(jsonnet -A name="$$cluster" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop") ; \
		$(KUBECFG) diff --diff-strategy=subset $(K8S_CONF); \
	done

apply:
	for cluster in $(CLUSTERS); do \
		echo ====== "$$cluster" ======; \
		context=$$(jsonnet -A name="$$cluster" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop") ; \
		$(KUBECFG) update $(K8S_CONF); \
	done

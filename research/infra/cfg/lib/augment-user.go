package cfglib

import (
	"github.com/augmentcode/augment/infra/lib/k8s"
)

type User struct {
	name      string
	namespace string
}

func NewUser(name string) User {
	return User{
		name:      name,
		namespace: "tenant-augment-eng",
	}
}

func (u User) Name() string {
	return u.name
}

func (u User) Namespace() string {
	return u.namespace
}

func (u User) DefaultServiceAccountName() string {
	return "aug-user-" + u.Name()
}

func (u User) DefaultObjectStorageUserName() string {
	return "aug-user-" + u.Name()
}

func (u User) DefaultServiceAccount() *k8s.ServiceAccount {
	name := u.DefaultServiceAccountName()
	labels := map[string]string{
		"aug.type": "user-service-account",
		"aug.user": u.Name(),
	}
	return k8s.BuildSimpleServiceAccount(name, u.Namespace(), labels)
}

func (u User) DefaultRoleBindings() (ret []*k8s.RoleBinding) {
	rbForRole := func(role string) *k8s.RoleBinding {
		name := "aug-user-" + u.Name() + "-" + role
		labels := map[string]string{
			"aug.type": "user-role-binding",
			"aug.user": u.Name(),
		}
		return k8s.BuildRoleBindingForSA(name, u.Namespace(), u.DefaultServiceAccount().Name(), role, labels)
	}
	return append(ret,
		rbForRole("aug-reader"),
		rbForRole("aug-eng-writer"),
	)
}

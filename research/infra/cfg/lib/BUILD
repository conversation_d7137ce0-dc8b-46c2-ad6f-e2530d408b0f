load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library", "jsonnet_to_json")

package(default_visibility = ["//research/infra:internal"])

filegroup(
    name = "jsonnet_files",
    srcs = glob(["**/*.jsonnet"]) + [
        "//infra/cfg/lib/k8s:srcs",
    ],
)

jsonnet_library(
    name = "lib",
    srcs = [":jsonnet_files"],
)

# NOTE(mattm): This isn't important, it's just a PoC.
jsonnet_to_json(
    name = "augment-enums_json",
    src = "augment-enums.jsonnet",
    outs = ["augment-enums.json"],
    visibility = ["//visibility:private"],
)

go_library(
    name = "cfglib_go",
    srcs = ["augment-user.go"],
    importpath = "github.com/augmentcode/augment/research/infra/cfg/lib",
    deps = [
        "//infra/lib/k8s",
    ],
)

{"version": 1, "dependencies": [{"source": {"git": {"remote": "https://github.com/grafana/grafonnet.git", "subdir": "gen/grafonnet-latest"}}, "version": "1ce5aec95ce32336fe47c8881361847c475b5254", "sum": "64fMUPI3frXGj4X1FqFd1t7r04w3CUSmXaDcJ23EYbQ="}, {"source": {"git": {"remote": "https://github.com/grafana/grafonnet.git", "subdir": "gen/grafonnet-v11.1.0"}}, "version": "1ce5aec95ce32336fe47c8881361847c475b5254", "sum": "41w7p/rwrNsITqNHMXtGSJAfAyKmnflg6rFhKBduUxM="}, {"source": {"git": {"remote": "https://github.com/jsonnet-libs/docsonnet.git", "subdir": "doc-util"}}, "version": "6ac6c69685b8c29c54515448eaca583da2d88150", "sum": "BrAL/k23jq+xy9oA7TWIhUx07dsA/QLm3g7ktCwe//U="}, {"source": {"git": {"remote": "https://github.com/jsonnet-libs/xtd.git", "subdir": ""}}, "version": "63d430b69a95741061c2f7fc9d84b1a778511d9c", "sum": "qiZi3axUSXCVzKUF83zSAxklwrnitMmrDK4XAfjPMdE="}], "legacyImports": false}
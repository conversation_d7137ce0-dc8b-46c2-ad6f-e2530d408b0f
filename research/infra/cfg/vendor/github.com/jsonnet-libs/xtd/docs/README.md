---
permalink: /
---

# xtd

```jsonnet
local xtd = import "github.com/jsonnet-libs/xtd/main.libsonnet"
```

`xtd` aims to collect useful functions not included in the Jsonnet standard library (`std`).

This package serves as a test field for functions intended to be contributed to `std`
in the future, but also provides a place for less general, yet useful utilities.


* [aggregate](aggregate.md)
* [array](array.md)
* [ascii](ascii.md)
* [camelcase](camelcase.md)
* [date](date.md)
* [inspect](inspect.md)
* [jsonpath](jsonpath.md)
* [number](number.md)
* [string](string.md)
* [url](url.md)
name: tests
on:
  pull_request: {}
  push:
    branches:
      - main
      - master

jobs:
  test: 
    name: test
    runs-on: ubuntu-latest
    steps: 
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v4
      - name: make test
        run: |
          go install github.com/google/go-jsonnet/cmd/jsonnet@latest
          go install github.com/jsonnet-bundler/jsonnet-bundler/cmd/jb@latest
          make test
  docs:
    name: docs
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v4
      - name: make docs
        run: |
          go install github.com/jsonnet-libs/docsonnet@master
          make docs
          git diff --exit-code
  
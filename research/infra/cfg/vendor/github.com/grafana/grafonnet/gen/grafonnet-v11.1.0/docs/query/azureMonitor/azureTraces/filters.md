# filters



## Index

* [`fn withFilters(value)`](#fn-withfilters)
* [`fn withFiltersMixin(value)`](#fn-withfiltersmixin)
* [`fn withOperation(value)`](#fn-withoperation)
* [`fn withProperty(value)`](#fn-withproperty)

## Fields

### fn withFilters

```jsonnet
withFilters(value)
```

PARAMETERS:

* **value** (`array`)

Values to filter by.
### fn withFiltersMixin

```jsonnet
withFiltersMixin(value)
```

PARAMETERS:

* **value** (`array`)

Values to filter by.
### fn withOperation

```jsonnet
withOperation(value)
```

PARAMETERS:

* **value** (`string`)

Comparison operator to use. Either equals or not equals.
### fn withProperty

```jsonnet
withProperty(value)
```

PARAMETERS:

* **value** (`string`)

Property name, auto-populated based on available traces.
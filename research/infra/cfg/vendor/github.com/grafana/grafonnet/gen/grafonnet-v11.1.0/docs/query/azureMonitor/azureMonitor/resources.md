# resources



## Index

* [`fn withMetricNamespace(value)`](#fn-withmetricnamespace)
* [`fn withRegion(value)`](#fn-withregion)
* [`fn withResourceGroup(value)`](#fn-withresourcegroup)
* [`fn withResourceName(value)`](#fn-withresourcename)
* [`fn withSubscription(value)`](#fn-withsubscription)

## Fields

### fn withMetricNamespace

```jsonnet
withMetricNamespace(value)
```

PARAMETERS:

* **value** (`string`)


### fn withRegion

```jsonnet
withRegion(value)
```

PARAMETERS:

* **value** (`string`)


### fn withResourceGroup

```jsonnet
withResourceGroup(value)
```

PARAMETERS:

* **value** (`string`)


### fn withResourceName

```jsonnet
withResourceName(value)
```

PARAMETERS:

* **value** (`string`)


### fn withSubscription

```jsonnet
withSubscription(value)
```

PARAMETERS:

* **value** (`string`)


# bucketAggs



## Subpackages

* [Filters.settings.filters](Filters/settings/filters.md)

## Index

* [`obj DateHistogram`](#obj-datehistogram)
  * [`fn withField(value)`](#fn-datehistogramwithfield)
  * [`fn withId(value)`](#fn-datehistogramwithid)
  * [`fn withSettings(value)`](#fn-datehistogramwithsettings)
  * [`fn withSettingsMixin(value)`](#fn-datehistogramwithsettingsmixin)
  * [`fn withType()`](#fn-datehistogramwithtype)
  * [`obj settings`](#obj-datehistogramsettings)
    * [`fn withInterval(value)`](#fn-datehistogramsettingswithinterval)
    * [`fn withMinDocCount(value)`](#fn-datehistogramsettingswithmindoccount)
    * [`fn withOffset(value)`](#fn-datehistogramsettingswithoffset)
    * [`fn withTimeZone(value)`](#fn-datehistogramsettingswithtimezone)
    * [`fn withTrimEdges(value)`](#fn-datehistogramsettingswithtrimedges)
* [`obj Filters`](#obj-filters)
  * [`fn withId(value)`](#fn-filterswithid)
  * [`fn withSettings(value)`](#fn-filterswithsettings)
  * [`fn withSettingsMixin(value)`](#fn-filterswithsettingsmixin)
  * [`fn withType()`](#fn-filterswithtype)
  * [`obj settings`](#obj-filterssettings)
    * [`fn withFilters(value)`](#fn-filterssettingswithfilters)
    * [`fn withFiltersMixin(value)`](#fn-filterssettingswithfiltersmixin)
* [`obj GeoHashGrid`](#obj-geohashgrid)
  * [`fn withField(value)`](#fn-geohashgridwithfield)
  * [`fn withId(value)`](#fn-geohashgridwithid)
  * [`fn withSettings(value)`](#fn-geohashgridwithsettings)
  * [`fn withSettingsMixin(value)`](#fn-geohashgridwithsettingsmixin)
  * [`fn withType()`](#fn-geohashgridwithtype)
  * [`obj settings`](#obj-geohashgridsettings)
    * [`fn withPrecision(value)`](#fn-geohashgridsettingswithprecision)
* [`obj Histogram`](#obj-histogram)
  * [`fn withField(value)`](#fn-histogramwithfield)
  * [`fn withId(value)`](#fn-histogramwithid)
  * [`fn withSettings(value)`](#fn-histogramwithsettings)
  * [`fn withSettingsMixin(value)`](#fn-histogramwithsettingsmixin)
  * [`fn withType()`](#fn-histogramwithtype)
  * [`obj settings`](#obj-histogramsettings)
    * [`fn withInterval(value)`](#fn-histogramsettingswithinterval)
    * [`fn withMinDocCount(value)`](#fn-histogramsettingswithmindoccount)
* [`obj Nested`](#obj-nested)
  * [`fn withField(value)`](#fn-nestedwithfield)
  * [`fn withId(value)`](#fn-nestedwithid)
  * [`fn withSettings(value)`](#fn-nestedwithsettings)
  * [`fn withSettingsMixin(value)`](#fn-nestedwithsettingsmixin)
  * [`fn withType()`](#fn-nestedwithtype)
* [`obj Terms`](#obj-terms)
  * [`fn withField(value)`](#fn-termswithfield)
  * [`fn withId(value)`](#fn-termswithid)
  * [`fn withSettings(value)`](#fn-termswithsettings)
  * [`fn withSettingsMixin(value)`](#fn-termswithsettingsmixin)
  * [`fn withType()`](#fn-termswithtype)
  * [`obj settings`](#obj-termssettings)
    * [`fn withMinDocCount(value)`](#fn-termssettingswithmindoccount)
    * [`fn withMissing(value)`](#fn-termssettingswithmissing)
    * [`fn withOrder(value)`](#fn-termssettingswithorder)
    * [`fn withOrderBy(value)`](#fn-termssettingswithorderby)
    * [`fn withSize(value)`](#fn-termssettingswithsize)

## Fields

### obj DateHistogram


#### fn DateHistogram.withField

```jsonnet
DateHistogram.withField(value)
```

PARAMETERS:

* **value** (`string`)


#### fn DateHistogram.withId

```jsonnet
DateHistogram.withId(value)
```

PARAMETERS:

* **value** (`string`)


#### fn DateHistogram.withSettings

```jsonnet
DateHistogram.withSettings(value)
```

PARAMETERS:

* **value** (`object`)


#### fn DateHistogram.withSettingsMixin

```jsonnet
DateHistogram.withSettingsMixin(value)
```

PARAMETERS:

* **value** (`object`)


#### fn DateHistogram.withType

```jsonnet
DateHistogram.withType()
```



#### obj DateHistogram.settings


##### fn DateHistogram.settings.withInterval

```jsonnet
DateHistogram.settings.withInterval(value)
```

PARAMETERS:

* **value** (`string`)


##### fn DateHistogram.settings.withMinDocCount

```jsonnet
DateHistogram.settings.withMinDocCount(value)
```

PARAMETERS:

* **value** (`string`)


##### fn DateHistogram.settings.withOffset

```jsonnet
DateHistogram.settings.withOffset(value)
```

PARAMETERS:

* **value** (`string`)


##### fn DateHistogram.settings.withTimeZone

```jsonnet
DateHistogram.settings.withTimeZone(value)
```

PARAMETERS:

* **value** (`string`)


##### fn DateHistogram.settings.withTrimEdges

```jsonnet
DateHistogram.settings.withTrimEdges(value)
```

PARAMETERS:

* **value** (`string`)


### obj Filters


#### fn Filters.withId

```jsonnet
Filters.withId(value)
```

PARAMETERS:

* **value** (`string`)


#### fn Filters.withSettings

```jsonnet
Filters.withSettings(value)
```

PARAMETERS:

* **value** (`object`)


#### fn Filters.withSettingsMixin

```jsonnet
Filters.withSettingsMixin(value)
```

PARAMETERS:

* **value** (`object`)


#### fn Filters.withType

```jsonnet
Filters.withType()
```



#### obj Filters.settings


##### fn Filters.settings.withFilters

```jsonnet
Filters.settings.withFilters(value)
```

PARAMETERS:

* **value** (`array`)


##### fn Filters.settings.withFiltersMixin

```jsonnet
Filters.settings.withFiltersMixin(value)
```

PARAMETERS:

* **value** (`array`)


### obj GeoHashGrid


#### fn GeoHashGrid.withField

```jsonnet
GeoHashGrid.withField(value)
```

PARAMETERS:

* **value** (`string`)


#### fn GeoHashGrid.withId

```jsonnet
GeoHashGrid.withId(value)
```

PARAMETERS:

* **value** (`string`)


#### fn GeoHashGrid.withSettings

```jsonnet
GeoHashGrid.withSettings(value)
```

PARAMETERS:

* **value** (`object`)


#### fn GeoHashGrid.withSettingsMixin

```jsonnet
GeoHashGrid.withSettingsMixin(value)
```

PARAMETERS:

* **value** (`object`)


#### fn GeoHashGrid.withType

```jsonnet
GeoHashGrid.withType()
```



#### obj GeoHashGrid.settings


##### fn GeoHashGrid.settings.withPrecision

```jsonnet
GeoHashGrid.settings.withPrecision(value)
```

PARAMETERS:

* **value** (`string`)


### obj Histogram


#### fn Histogram.withField

```jsonnet
Histogram.withField(value)
```

PARAMETERS:

* **value** (`string`)


#### fn Histogram.withId

```jsonnet
Histogram.withId(value)
```

PARAMETERS:

* **value** (`string`)


#### fn Histogram.withSettings

```jsonnet
Histogram.withSettings(value)
```

PARAMETERS:

* **value** (`object`)


#### fn Histogram.withSettingsMixin

```jsonnet
Histogram.withSettingsMixin(value)
```

PARAMETERS:

* **value** (`object`)


#### fn Histogram.withType

```jsonnet
Histogram.withType()
```



#### obj Histogram.settings


##### fn Histogram.settings.withInterval

```jsonnet
Histogram.settings.withInterval(value)
```

PARAMETERS:

* **value** (`string`)


##### fn Histogram.settings.withMinDocCount

```jsonnet
Histogram.settings.withMinDocCount(value)
```

PARAMETERS:

* **value** (`string`)


### obj Nested


#### fn Nested.withField

```jsonnet
Nested.withField(value)
```

PARAMETERS:

* **value** (`string`)


#### fn Nested.withId

```jsonnet
Nested.withId(value)
```

PARAMETERS:

* **value** (`string`)


#### fn Nested.withSettings

```jsonnet
Nested.withSettings(value)
```

PARAMETERS:

* **value** (`object`)


#### fn Nested.withSettingsMixin

```jsonnet
Nested.withSettingsMixin(value)
```

PARAMETERS:

* **value** (`object`)


#### fn Nested.withType

```jsonnet
Nested.withType()
```



### obj Terms


#### fn Terms.withField

```jsonnet
Terms.withField(value)
```

PARAMETERS:

* **value** (`string`)


#### fn Terms.withId

```jsonnet
Terms.withId(value)
```

PARAMETERS:

* **value** (`string`)


#### fn Terms.withSettings

```jsonnet
Terms.withSettings(value)
```

PARAMETERS:

* **value** (`object`)


#### fn Terms.withSettingsMixin

```jsonnet
Terms.withSettingsMixin(value)
```

PARAMETERS:

* **value** (`object`)


#### fn Terms.withType

```jsonnet
Terms.withType()
```



#### obj Terms.settings


##### fn Terms.settings.withMinDocCount

```jsonnet
Terms.settings.withMinDocCount(value)
```

PARAMETERS:

* **value** (`string`)


##### fn Terms.settings.withMissing

```jsonnet
Terms.settings.withMissing(value)
```

PARAMETERS:

* **value** (`string`)


##### fn Terms.settings.withOrder

```jsonnet
Terms.settings.withOrder(value)
```

PARAMETERS:

* **value** (`string`)
   - valid values: `"desc"`, `"asc"`


##### fn Terms.settings.withOrderBy

```jsonnet
Terms.settings.withOrderBy(value)
```

PARAMETERS:

* **value** (`string`)


##### fn Terms.settings.withSize

```jsonnet
Terms.settings.withSize(value)
```

PARAMETERS:

* **value** (`string`)


// This file is generated, do not manually edit.
{
  '#': { help: 'grafonnet.alerting.notificationTemplate', name: 'notificationTemplate' },
  '#withName': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['string'] }], help: '' } },
  withName(value): {
    name: value,
  },
  '#withProvenance': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['string'] }], help: '' } },
  withProvenance(value): {
    provenance: value,
  },
  '#withTemplate': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['string'] }], help: '' } },
  withTemplate(value): {
    template: value,
  },
}

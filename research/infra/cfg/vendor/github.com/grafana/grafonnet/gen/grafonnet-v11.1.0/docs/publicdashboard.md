# publicdashboard

grafonnet.publicdashboard

## Index

* [`fn withAccessToken(value)`](#fn-withaccesstoken)
* [`fn withAnnotationsEnabled(value=true)`](#fn-withannotationsenabled)
* [`fn withDashboardUid(value)`](#fn-withdashboarduid)
* [`fn withIsEnabled(value=true)`](#fn-withisenabled)
* [`fn withTimeSelectionEnabled(value=true)`](#fn-withtimeselectionenabled)
* [`fn withUid(value)`](#fn-withuid)

## Fields

### fn withAccessToken

```jsonnet
withAccessToken(value)
```

PARAMETERS:

* **value** (`string`)

Unique public access token
### fn withAnnotationsEnabled

```jsonnet
withAnnotationsEnabled(value=true)
```

PARAMETERS:

* **value** (`boolean`)
   - default value: `true`

Flag that indicates if annotations are enabled
### fn withDashboardUid

```jsonnet
withDashboardUid(value)
```

PARAMETERS:

* **value** (`string`)

Dashboard unique identifier referenced by this public dashboard
### fn withIsEnabled

```jsonnet
withIsEnabled(value=true)
```

PARAMETERS:

* **value** (`boolean`)
   - default value: `true`

Flag that indicates if the public dashboard is enabled
### fn withTimeSelectionEnabled

```jsonnet
withTimeSelectionEnabled(value=true)
```

PARAMETERS:

* **value** (`boolean`)
   - default value: `true`

Flag that indicates if the time range picker is enabled
### fn withUid

```jsonnet
withUid(value)
```

PARAMETERS:

* **value** (`string`)

Unique public dashboard identifier
// This file is generated, do not manually edit.
{
  '#': { help: 'grafonnet.folder', name: 'folder' },
  '#withParentUid': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['string'] }], help: 'only used if nested folders are enabled' } },
  withParentUid(value): {
    parentUid: value,
  },
  '#withTitle': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['string'] }], help: 'Folder title' } },
  withTitle(value): {
    title: value,
  },
  '#withUid': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['string'] }], help: 'Unique folder id' } },
  withUid(value): {
    uid: value,
  },
}

# parca

grafonnet.query.parca

## Index

* [`fn withDatasource(value)`](#fn-withdatasource)
* [`fn withHide(value=true)`](#fn-withhide)
* [`fn withLabelSelector(value="{}")`](#fn-withlabelselector)
* [`fn withProfileTypeId(value)`](#fn-withprofiletypeid)
* [`fn withQueryType(value)`](#fn-withquerytype)
* [`fn withRefId(value)`](#fn-withrefid)

## Fields

### fn withDatasource

```jsonnet
withDatasource(value)
```

PARAMETERS:

* **value** (`string`)

Set the datasource for this query.
### fn withHide

```jsonnet
withHide(value=true)
```

PARAMETERS:

* **value** (`boolean`)
   - default value: `true`

If hide is set to true, <PERSON><PERSON> will filter out the response(s) associated with this query before returning it to the panel.
### fn withLabelSelector

```jsonnet
withLabelSelector(value="{}")
```

PARAMETERS:

* **value** (`string`)
   - default value: `"{}"`

Specifies the query label selectors.
### fn withProfileTypeId

```jsonnet
withProfileTypeId(value)
```

PARAMETERS:

* **value** (`string`)

Specifies the type of profile to query.
### fn withQueryType

```jsonnet
withQueryType(value)
```

PARAMETERS:

* **value** (`string`)

Specify the query flavor
TODO make this required and give it a default
### fn withRefId

```jsonnet
withRefId(value)
```

PARAMETERS:

* **value** (`string`)

A unique identifier for the query within the list of targets.
In server side expressions, the refId is used as a variable name to identify results.
By default, the UI will assign A->Z; however setting meaningful names may be useful.
# connections



## Subpackages

* [vertices](vertices.md)

## Index

* [`fn withColor(value)`](#fn-withcolor)
* [`fn withColorMixin(value)`](#fn-withcolormixin)
* [`fn withPath(value)`](#fn-withpath)
* [`fn withSize(value)`](#fn-withsize)
* [`fn withSizeMixin(value)`](#fn-withsizemixin)
* [`fn withSource(value)`](#fn-withsource)
* [`fn withSourceMixin(value)`](#fn-withsourcemixin)
* [`fn withSourceOriginal(value)`](#fn-withsourceoriginal)
* [`fn withSourceOriginalMixin(value)`](#fn-withsourceoriginalmixin)
* [`fn withTarget(value)`](#fn-withtarget)
* [`fn withTargetMixin(value)`](#fn-withtargetmixin)
* [`fn withTargetName(value)`](#fn-withtargetname)
* [`fn withTargetOriginal(value)`](#fn-withtargetoriginal)
* [`fn withTargetOriginalMixin(value)`](#fn-withtargetoriginalmixin)
* [`fn withVertices(value)`](#fn-withvertices)
* [`fn withVerticesMixin(value)`](#fn-withverticesmixin)
* [`obj color`](#obj-color)
  * [`fn withField(value)`](#fn-colorwithfield)
  * [`fn withFixed(value)`](#fn-colorwithfixed)
* [`obj size`](#obj-size)
  * [`fn withField(value)`](#fn-sizewithfield)
  * [`fn withFixed(value)`](#fn-sizewithfixed)
  * [`fn withMax(value)`](#fn-sizewithmax)
  * [`fn withMin(value)`](#fn-sizewithmin)
  * [`fn withMode(value)`](#fn-sizewithmode)
* [`obj source`](#obj-source)
  * [`fn withX(value)`](#fn-sourcewithx)
  * [`fn withY(value)`](#fn-sourcewithy)
* [`obj sourceOriginal`](#obj-sourceoriginal)
  * [`fn withX(value)`](#fn-sourceoriginalwithx)
  * [`fn withY(value)`](#fn-sourceoriginalwithy)
* [`obj target`](#obj-target)
  * [`fn withX(value)`](#fn-targetwithx)
  * [`fn withY(value)`](#fn-targetwithy)
* [`obj targetOriginal`](#obj-targetoriginal)
  * [`fn withX(value)`](#fn-targetoriginalwithx)
  * [`fn withY(value)`](#fn-targetoriginalwithy)

## Fields

### fn withColor

```jsonnet
withColor(value)
```

PARAMETERS:

* **value** (`object`)


### fn withColorMixin

```jsonnet
withColorMixin(value)
```

PARAMETERS:

* **value** (`object`)


### fn withPath

```jsonnet
withPath(value)
```

PARAMETERS:

* **value** (`string`)
   - valid values: `"straight"`


### fn withSize

```jsonnet
withSize(value)
```

PARAMETERS:

* **value** (`object`)


### fn withSizeMixin

```jsonnet
withSizeMixin(value)
```

PARAMETERS:

* **value** (`object`)


### fn withSource

```jsonnet
withSource(value)
```

PARAMETERS:

* **value** (`object`)


### fn withSourceMixin

```jsonnet
withSourceMixin(value)
```

PARAMETERS:

* **value** (`object`)


### fn withSourceOriginal

```jsonnet
withSourceOriginal(value)
```

PARAMETERS:

* **value** (`object`)


### fn withSourceOriginalMixin

```jsonnet
withSourceOriginalMixin(value)
```

PARAMETERS:

* **value** (`object`)


### fn withTarget

```jsonnet
withTarget(value)
```

PARAMETERS:

* **value** (`object`)


### fn withTargetMixin

```jsonnet
withTargetMixin(value)
```

PARAMETERS:

* **value** (`object`)


### fn withTargetName

```jsonnet
withTargetName(value)
```

PARAMETERS:

* **value** (`string`)


### fn withTargetOriginal

```jsonnet
withTargetOriginal(value)
```

PARAMETERS:

* **value** (`object`)


### fn withTargetOriginalMixin

```jsonnet
withTargetOriginalMixin(value)
```

PARAMETERS:

* **value** (`object`)


### fn withVertices

```jsonnet
withVertices(value)
```

PARAMETERS:

* **value** (`array`)


### fn withVerticesMixin

```jsonnet
withVerticesMixin(value)
```

PARAMETERS:

* **value** (`array`)


### obj color


#### fn color.withField

```jsonnet
color.withField(value)
```

PARAMETERS:

* **value** (`string`)

fixed: T -- will be added by each element
#### fn color.withFixed

```jsonnet
color.withFixed(value)
```

PARAMETERS:

* **value** (`string`)

color value
### obj size


#### fn size.withField

```jsonnet
size.withField(value)
```

PARAMETERS:

* **value** (`string`)

fixed: T -- will be added by each element
#### fn size.withFixed

```jsonnet
size.withFixed(value)
```

PARAMETERS:

* **value** (`number`)


#### fn size.withMax

```jsonnet
size.withMax(value)
```

PARAMETERS:

* **value** (`number`)


#### fn size.withMin

```jsonnet
size.withMin(value)
```

PARAMETERS:

* **value** (`number`)


#### fn size.withMode

```jsonnet
size.withMode(value)
```

PARAMETERS:

* **value** (`string`)
   - valid values: `"linear"`, `"quad"`

| *"linear"
### obj source


#### fn source.withX

```jsonnet
source.withX(value)
```

PARAMETERS:

* **value** (`number`)


#### fn source.withY

```jsonnet
source.withY(value)
```

PARAMETERS:

* **value** (`number`)


### obj sourceOriginal


#### fn sourceOriginal.withX

```jsonnet
sourceOriginal.withX(value)
```

PARAMETERS:

* **value** (`number`)


#### fn sourceOriginal.withY

```jsonnet
sourceOriginal.withY(value)
```

PARAMETERS:

* **value** (`number`)


### obj target


#### fn target.withX

```jsonnet
target.withX(value)
```

PARAMETERS:

* **value** (`number`)


#### fn target.withY

```jsonnet
target.withY(value)
```

PARAMETERS:

* **value** (`number`)


### obj targetOriginal


#### fn targetOriginal.withX

```jsonnet
targetOriginal.withX(value)
```

PARAMETERS:

* **value** (`number`)


#### fn targetOriginal.withY

```jsonnet
targetOriginal.withY(value)
```

PARAMETERS:

* **value** (`number`)


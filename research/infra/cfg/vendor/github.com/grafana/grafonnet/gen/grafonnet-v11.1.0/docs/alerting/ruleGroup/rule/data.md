# data



## Index

* [`fn withDatasourceUid(value)`](#fn-withdatasourceuid)
* [`fn withModel(value)`](#fn-withmodel)
* [`fn withModelMixin(value)`](#fn-withmodelmixin)
* [`fn withQueryType(value)`](#fn-withquerytype)
* [`fn withRefId(value)`](#fn-withrefid)
* [`fn withRelativeTimeRange(value)`](#fn-withrelativetimerange)
* [`fn withRelativeTimeRangeMixin(value)`](#fn-withrelativetimerangemixin)
* [`obj relativeTimeRange`](#obj-relativetimerange)
  * [`fn withFrom(value)`](#fn-relativetimerangewithfrom)
  * [`fn withTo(value)`](#fn-relativetimerangewithto)

## Fields

### fn withDatasourceUid

```jsonnet
withDatasourceUid(value)
```

PARAMETERS:

* **value** (`string`)


### fn withModel

```jsonnet
withModel(value)
```

PARAMETERS:

* **value** (`object`)


### fn withModelMixin

```jsonnet
withModelMixin(value)
```

PARAMETERS:

* **value** (`object`)


### fn withQueryType

```jsonnet
withQueryType(value)
```

PARAMETERS:

* **value** (`string`)


### fn withRefId

```jsonnet
withRefId(value)
```

PARAMETERS:

* **value** (`string`)


### fn withRelativeTimeRange

```jsonnet
withRelativeTimeRange(value)
```

PARAMETERS:

* **value** (`object`)

RelativeTimeRange is the per query start and end time
for requests.
### fn withRelativeTimeRangeMixin

```jsonnet
withRelativeTimeRangeMixin(value)
```

PARAMETERS:

* **value** (`object`)

RelativeTimeRange is the per query start and end time
for requests.
### obj relativeTimeRange


#### fn relativeTimeRange.withFrom

```jsonnet
relativeTimeRange.withFrom(value)
```

PARAMETERS:

* **value** (`integer`)

Duration in seconds.
#### fn relativeTimeRange.withTo

```jsonnet
relativeTimeRange.withTo(value)
```

PARAMETERS:

* **value** (`integer`)

Duration in seconds.
// This file is generated, do not manually edit.
(import '../panel.libsonnet')
+ {
  '#': { help: 'grafonnet.panel.dashboardList', name: 'dashboardList' },
  panelOptions+:
    {
      '#withType': { 'function': { args: [], help: '' } },
      withType(): {
        type: 'dashlist',
      },
    },
  options+:
    {
      '#withFolderId': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['integer'] }], help: 'folderId is deprecated, and migrated to folderUid on panel init' } },
      withFolderId(value): {
        options+: {
          folderId: value,
        },
      },
      '#withFolderUID': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['string'] }], help: '' } },
      withFolderUID(value): {
        options+: {
          folderUID: value,
        },
      },
      '#withIncludeVars': { 'function': { args: [{ default: true, enums: null, name: 'value', type: ['boolean'] }], help: '' } },
      withIncludeVars(value=true): {
        options+: {
          includeVars: value,
        },
      },
      '#withKeepTime': { 'function': { args: [{ default: true, enums: null, name: 'value', type: ['boolean'] }], help: '' } },
      withKeepTime(value=true): {
        options+: {
          keepTime: value,
        },
      },
      '#withMaxItems': { 'function': { args: [{ default: 10, enums: null, name: 'value', type: ['integer'] }], help: '' } },
      withMaxItems(value=10): {
        options+: {
          maxItems: value,
        },
      },
      '#withQuery': { 'function': { args: [{ default: '', enums: null, name: 'value', type: ['string'] }], help: '' } },
      withQuery(value=''): {
        options+: {
          query: value,
        },
      },
      '#withShowFolderNames': { 'function': { args: [{ default: true, enums: null, name: 'value', type: ['boolean'] }], help: '' } },
      withShowFolderNames(value=true): {
        options+: {
          showFolderNames: value,
        },
      },
      '#withShowHeadings': { 'function': { args: [{ default: true, enums: null, name: 'value', type: ['boolean'] }], help: '' } },
      withShowHeadings(value=true): {
        options+: {
          showHeadings: value,
        },
      },
      '#withShowRecentlyViewed': { 'function': { args: [{ default: true, enums: null, name: 'value', type: ['boolean'] }], help: '' } },
      withShowRecentlyViewed(value=true): {
        options+: {
          showRecentlyViewed: value,
        },
      },
      '#withShowSearch': { 'function': { args: [{ default: true, enums: null, name: 'value', type: ['boolean'] }], help: '' } },
      withShowSearch(value=true): {
        options+: {
          showSearch: value,
        },
      },
      '#withShowStarred': { 'function': { args: [{ default: true, enums: null, name: 'value', type: ['boolean'] }], help: '' } },
      withShowStarred(value=true): {
        options+: {
          showStarred: value,
        },
      },
      '#withTags': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['array'] }], help: '' } },
      withTags(value): {
        options+: {
          tags:
            (if std.isArray(value)
             then value
             else [value]),
        },
      },
      '#withTagsMixin': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['array'] }], help: '' } },
      withTagsMixin(value): {
        options+: {
          tags+:
            (if std.isArray(value)
             then value
             else [value]),
        },
      },
    },
}
+ {
  panelOptions+: {
    '#withType':: {
      ignore: true,
    },
  },
}

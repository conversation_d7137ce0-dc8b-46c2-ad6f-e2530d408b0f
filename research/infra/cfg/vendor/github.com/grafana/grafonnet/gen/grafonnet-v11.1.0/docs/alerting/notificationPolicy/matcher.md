# matcher



## Index

* [`fn withName(value)`](#fn-withname)
* [`fn withType(value)`](#fn-withtype)
* [`fn withValue(value)`](#fn-withvalue)

## Fields

### fn withName

```jsonnet
withName(value)
```

PARAMETERS:

* **value** (`string`)


### fn withType

```jsonnet
withType(value)
```

PARAMETERS:

* **value** (`string`)
   - valid values: `"="`, `"!="`, `"=~"`, `"!~"`


### fn withValue

```jsonnet
withValue(value)
```

PARAMETERS:

* **value** (`string`)


// This file is generated, do not manually edit.
{
  '#': { help: 'grafonnet.publicdashboard', name: 'publicdashboard' },
  '#withAccessToken': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['string'] }], help: 'Unique public access token' } },
  withAccessToken(value): {
    accessToken: value,
  },
  '#withAnnotationsEnabled': { 'function': { args: [{ default: true, enums: null, name: 'value', type: ['boolean'] }], help: 'Flag that indicates if annotations are enabled' } },
  withAnnotationsEnabled(value=true): {
    annotationsEnabled: value,
  },
  '#withDashboardUid': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['string'] }], help: 'Dashboard unique identifier referenced by this public dashboard' } },
  withDashboardUid(value): {
    dashboardUid: value,
  },
  '#withIsEnabled': { 'function': { args: [{ default: true, enums: null, name: 'value', type: ['boolean'] }], help: 'Flag that indicates if the public dashboard is enabled' } },
  withIsEnabled(value=true): {
    isEnabled: value,
  },
  '#withTimeSelectionEnabled': { 'function': { args: [{ default: true, enums: null, name: 'value', type: ['boolean'] }], help: 'Flag that indicates if the time range picker is enabled' } },
  withTimeSelectionEnabled(value=true): {
    timeSelectionEnabled: value,
  },
  '#withUid': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['string'] }], help: 'Unique public dashboard identifier' } },
  withUid(value): {
    uid: value,
  },
}

# arcs



## Index

* [`fn withColor(value)`](#fn-withcolor)
* [`fn withField(value)`](#fn-withfield)

## Fields

### fn withColor

```jsonnet
withColor(value)
```

PARAMETERS:

* **value** (`string`)

The color of the arc.
### fn withField

```jsonnet
withField(value)
```

PARAMETERS:

* **value** (`string`)

Field from which to get the value. Values should be less than 1, representing fraction of a circle.
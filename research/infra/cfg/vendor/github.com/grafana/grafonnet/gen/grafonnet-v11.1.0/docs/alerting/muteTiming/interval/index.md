# interval



## Subpackages

* [time_intervals](time_intervals/index.md)

## Index

* [`fn withName(value)`](#fn-withname)
* [`fn withTimeIntervals(value)`](#fn-withtimeintervals)
* [`fn withTimeIntervalsMixin(value)`](#fn-withtimeintervalsmixin)

## Fields

### fn withName

```jsonnet
withName(value)
```

PARAMETERS:

* **value** (`string`)


### fn withTimeIntervals

```jsonnet
withTimeIntervals(value)
```

PARAMETERS:

* **value** (`array`)


### fn withTimeIntervalsMixin

```jsonnet
withTimeIntervalsMixin(value)
```

PARAMETERS:

* **value** (`array`)


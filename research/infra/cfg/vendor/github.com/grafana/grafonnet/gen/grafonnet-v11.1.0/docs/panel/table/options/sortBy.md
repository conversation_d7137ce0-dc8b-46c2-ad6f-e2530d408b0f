# sortBy



## Index

* [`fn withDesc(value=true)`](#fn-withdesc)
* [`fn withDisplayName(value)`](#fn-withdisplayname)

## Fields

### fn withDesc

```jsonnet
withDesc(value=true)
```

PARAMETERS:

* **value** (`boolean`)
   - default value: `true`

Flag used to indicate descending sort order
### fn withDisplayName

```jsonnet
withDisplayName(value)
```

PARAMETERS:

* **value** (`string`)

Sets the display name of the field to sort by
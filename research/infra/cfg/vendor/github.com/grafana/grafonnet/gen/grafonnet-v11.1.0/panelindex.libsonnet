// This file is generated, do not manually edit.
{
  '#': { help: 'grafonnet.panel', name: 'panel' },
  alertList: import 'panel/alertList.libsonnet',
  annotationsList: import 'panel/annotationsList.libsonnet',
  barChart: import 'panel/barChart.libsonnet',
  barGauge: import 'panel/barGauge.libsonnet',
  candlestick: import 'panel/candlestick.libsonnet',
  canvas: import 'panel/canvas.libsonnet',
  dashboardList: import 'panel/dashboardList.libsonnet',
  datagrid: import 'panel/datagrid.libsonnet',
  debug: import 'panel/debug.libsonnet',
  gauge: import 'panel/gauge.libsonnet',
  geomap: import 'panel/geomap.libsonnet',
  heatmap: import 'panel/heatmap.libsonnet',
  histogram: import 'panel/histogram.libsonnet',
  logs: import 'panel/logs.libsonnet',
  news: import 'panel/news.libsonnet',
  nodeGraph: import 'panel/nodeGraph.libsonnet',
  pieChart: import 'panel/pieChart.libsonnet',
  stat: import 'panel/stat.libsonnet',
  stateTimeline: import 'panel/stateTimeline.libsonnet',
  statusHistory: import 'panel/statusHistory.libsonnet',
  table: import 'panel/table.libsonnet',
  text: import 'panel/text.libsonnet',
  timeSeries: import 'panel/timeSeries.libsonnet',
  trend: import 'panel/trend.libsonnet',
  xyChart: import 'panel/xyChart.libsonnet',
  row: import 'panel/row.libsonnet',
}

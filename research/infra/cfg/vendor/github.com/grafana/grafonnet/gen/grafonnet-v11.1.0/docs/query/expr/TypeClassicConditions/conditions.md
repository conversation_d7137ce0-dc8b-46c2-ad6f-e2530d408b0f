# conditions



## Index

* [`fn withEvaluator(value)`](#fn-withevaluator)
* [`fn withEvaluatorMixin(value)`](#fn-withevaluatormixin)
* [`fn withOperator(value)`](#fn-withoperator)
* [`fn withOperatorMixin(value)`](#fn-withoperatormixin)
* [`fn withQuery(value)`](#fn-withquery)
* [`fn withQueryMixin(value)`](#fn-withquerymixin)
* [`fn withReducer(value)`](#fn-withreducer)
* [`fn withReducerMixin(value)`](#fn-withreducermixin)
* [`obj evaluator`](#obj-evaluator)
  * [`fn withParams(value)`](#fn-evaluatorwithparams)
  * [`fn withParamsMixin(value)`](#fn-evaluatorwithparamsmixin)
  * [`fn withType(value)`](#fn-evaluatorwithtype)
* [`obj operator`](#obj-operator)
  * [`fn withType(value)`](#fn-operatorwithtype)
* [`obj query`](#obj-query)
  * [`fn withParams(value)`](#fn-querywithparams)
  * [`fn withParamsMixin(value)`](#fn-querywithparamsmixin)
* [`obj reducer`](#obj-reducer)
  * [`fn withType(value)`](#fn-reducerwithtype)

## Fields

### fn withEvaluator

```jsonnet
withEvaluator(value)
```

PARAMETERS:

* **value** (`object`)


### fn withEvaluatorMixin

```jsonnet
withEvaluatorMixin(value)
```

PARAMETERS:

* **value** (`object`)


### fn withOperator

```jsonnet
withOperator(value)
```

PARAMETERS:

* **value** (`object`)


### fn withOperatorMixin

```jsonnet
withOperatorMixin(value)
```

PARAMETERS:

* **value** (`object`)


### fn withQuery

```jsonnet
withQuery(value)
```

PARAMETERS:

* **value** (`object`)


### fn withQueryMixin

```jsonnet
withQueryMixin(value)
```

PARAMETERS:

* **value** (`object`)


### fn withReducer

```jsonnet
withReducer(value)
```

PARAMETERS:

* **value** (`object`)


### fn withReducerMixin

```jsonnet
withReducerMixin(value)
```

PARAMETERS:

* **value** (`object`)


### obj evaluator


#### fn evaluator.withParams

```jsonnet
evaluator.withParams(value)
```

PARAMETERS:

* **value** (`array`)


#### fn evaluator.withParamsMixin

```jsonnet
evaluator.withParamsMixin(value)
```

PARAMETERS:

* **value** (`array`)


#### fn evaluator.withType

```jsonnet
evaluator.withType(value)
```

PARAMETERS:

* **value** (`string`)

e.g. "gt"
### obj operator


#### fn operator.withType

```jsonnet
operator.withType(value)
```

PARAMETERS:

* **value** (`string`)
   - valid values: `"and"`, `"or"`, `"logic-or"`


### obj query


#### fn query.withParams

```jsonnet
query.withParams(value)
```

PARAMETERS:

* **value** (`array`)


#### fn query.withParamsMixin

```jsonnet
query.withParamsMixin(value)
```

PARAMETERS:

* **value** (`array`)


### obj reducer


#### fn reducer.withType

```jsonnet
reducer.withType(value)
```

PARAMETERS:

* **value** (`string`)


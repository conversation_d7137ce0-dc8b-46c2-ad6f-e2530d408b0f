# role

grafonnet.role

## Index

* [`fn withDescription(value)`](#fn-withdescription)
* [`fn withDisplayName(value)`](#fn-withdisplayname)
* [`fn withGroupName(value)`](#fn-withgroupname)
* [`fn withHidden(value=true)`](#fn-withhidden)
* [`fn withName(value)`](#fn-withname)

## Fields

### fn withDescription

```jsonnet
withDescription(value)
```

PARAMETERS:

* **value** (`string`)

Role description
### fn withDisplayName

```jsonnet
withDisplayName(value)
```

PARAMETERS:

* **value** (`string`)

Optional display
### fn withGroupName

```jsonnet
withGroupName(value)
```

PARAMETERS:

* **value** (`string`)

Name of the team.
### fn withHidden

```jsonnet
withHidden(value=true)
```

PARAMETERS:

* **value** (`boolean`)
   - default value: `true`

Do not show this role
### fn withName

```jsonnet
withName(value)
```

PARAMETERS:

* **value** (`string`)

The role identifier `managed:builtins:editor:permissions`
# conditions



## Index

* [`fn withEvaluator(value)`](#fn-withevaluator)
* [`fn withEvaluatorMixin(value)`](#fn-withevaluatormixin)
* [`fn withLoadedDimensions(value)`](#fn-withloadeddimensions)
* [`fn withLoadedDimensionsMixin(value)`](#fn-withloadeddimensionsmixin)
* [`fn withUnloadEvaluator(value)`](#fn-withunloadevaluator)
* [`fn withUnloadEvaluatorMixin(value)`](#fn-withunloadevaluatormixin)
* [`obj evaluator`](#obj-evaluator)
  * [`fn withParams(value)`](#fn-evaluatorwithparams)
  * [`fn withParamsMixin(value)`](#fn-evaluatorwithparamsmixin)
  * [`fn withType(value)`](#fn-evaluatorwithtype)
* [`obj unloadEvaluator`](#obj-unloadevaluator)
  * [`fn withParams(value)`](#fn-unloadevaluatorwithparams)
  * [`fn withParamsMixin(value)`](#fn-unloadevaluatorwithparamsmixin)
  * [`fn withType(value)`](#fn-unloadevaluatorwithtype)

## Fields

### fn withEvaluator

```jsonnet
withEvaluator(value)
```

PARAMETERS:

* **value** (`object`)


### fn withEvaluatorMixin

```jsonnet
withEvaluatorMixin(value)
```

PARAMETERS:

* **value** (`object`)


### fn withLoadedDimensions

```jsonnet
withLoadedDimensions(value)
```

PARAMETERS:

* **value** (`object`)


### fn withLoadedDimensionsMixin

```jsonnet
withLoadedDimensionsMixin(value)
```

PARAMETERS:

* **value** (`object`)


### fn withUnloadEvaluator

```jsonnet
withUnloadEvaluator(value)
```

PARAMETERS:

* **value** (`object`)


### fn withUnloadEvaluatorMixin

```jsonnet
withUnloadEvaluatorMixin(value)
```

PARAMETERS:

* **value** (`object`)


### obj evaluator


#### fn evaluator.withParams

```jsonnet
evaluator.withParams(value)
```

PARAMETERS:

* **value** (`array`)


#### fn evaluator.withParamsMixin

```jsonnet
evaluator.withParamsMixin(value)
```

PARAMETERS:

* **value** (`array`)


#### fn evaluator.withType

```jsonnet
evaluator.withType(value)
```

PARAMETERS:

* **value** (`string`)
   - valid values: `"gt"`, `"lt"`, `"within_range"`, `"outside_range"`

e.g. "gt"
### obj unloadEvaluator


#### fn unloadEvaluator.withParams

```jsonnet
unloadEvaluator.withParams(value)
```

PARAMETERS:

* **value** (`array`)


#### fn unloadEvaluator.withParamsMixin

```jsonnet
unloadEvaluator.withParamsMixin(value)
```

PARAMETERS:

* **value** (`array`)


#### fn unloadEvaluator.withType

```jsonnet
unloadEvaluator.withType(value)
```

PARAMETERS:

* **value** (`string`)
   - valid values: `"gt"`, `"lt"`, `"within_range"`, `"outside_range"`

e.g. "gt"
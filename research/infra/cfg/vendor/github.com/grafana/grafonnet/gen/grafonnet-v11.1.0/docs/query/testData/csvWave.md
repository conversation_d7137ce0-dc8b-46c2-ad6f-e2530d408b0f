# csvWave



## Index

* [`fn withLabels(value)`](#fn-withlabels)
* [`fn withName(value)`](#fn-withname)
* [`fn withTimeStep(value)`](#fn-withtimestep)
* [`fn withValuesCSV(value)`](#fn-withvaluescsv)

## Fields

### fn withLabels

```jsonnet
withLabels(value)
```

PARAMETERS:

* **value** (`string`)


### fn withName

```jsonnet
withName(value)
```

PARAMETERS:

* **value** (`string`)


### fn withTimeStep

```jsonnet
withTimeStep(value)
```

PARAMETERS:

* **value** (`integer`)


### fn withValuesCSV

```jsonnet
withValuesCSV(value)
```

PARAMETERS:

* **value** (`string`)


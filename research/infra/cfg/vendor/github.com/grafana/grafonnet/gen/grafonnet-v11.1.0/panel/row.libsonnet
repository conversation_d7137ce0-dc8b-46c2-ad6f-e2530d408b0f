// This file is generated, do not manually edit.
{
  '#': { help: 'grafonnet.panel.row', name: 'row' },
  '#withCollapsed': { 'function': { args: [{ default: true, enums: null, name: 'value', type: ['boolean'] }], help: 'Whether this row should be collapsed or not.' } },
  withCollapsed(value=true): {
    collapsed: value,
  },
  '#withDatasource': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['object'] }], help: 'Ref to a DataSource instance' } },
  withDatasource(value): {
    datasource: value,
  },
  '#withDatasourceMixin': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['object'] }], help: 'Ref to a DataSource instance' } },
  withDatasourceMixin(value): {
    datasource+: value,
  },
  datasource+:
    {
      '#withType': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['string'] }], help: 'The plugin type-id' } },
      withType(value): {
        datasource+: {
          type: value,
        },
      },
      '#withUid': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['string'] }], help: 'Specific datasource instance' } },
      withUid(value): {
        datasource+: {
          uid: value,
        },
      },
    },
  '#withGridPos': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['object'] }], help: 'Position and dimensions of a panel in the grid' } },
  withGridPos(value): {
    gridPos: value,
  },
  '#withGridPosMixin': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['object'] }], help: 'Position and dimensions of a panel in the grid' } },
  withGridPosMixin(value): {
    gridPos+: value,
  },
  gridPos+:
    {
      '#withH': { 'function': { args: [{ default: 9, enums: null, name: 'value', type: ['integer'] }], help: 'Panel height. The height is the number of rows from the top edge of the panel.' } },
      withH(value=9): {
        gridPos+: {
          h: value,
        },
      },
      '#withStatic': { 'function': { args: [{ default: true, enums: null, name: 'value', type: ['boolean'] }], help: "Whether the panel is fixed within the grid. If true, the panel will not be affected by other panels' interactions" } },
      withStatic(value=true): {
        gridPos+: {
          static: value,
        },
      },
      '#withW': { 'function': { args: [{ default: 12, enums: null, name: 'value', type: ['integer'] }], help: 'Panel width. The width is the number of columns from the left edge of the panel.' } },
      withW(value=12): {
        gridPos+: {
          w: value,
        },
      },
      '#withX': { 'function': { args: [{ default: 0, enums: null, name: 'value', type: ['integer'] }], help: 'Panel x. The x coordinate is the number of columns from the left edge of the grid' } },
      withX(value=0): {
        gridPos+: {
          x: value,
        },
      },
      '#withY': { 'function': { args: [{ default: 0, enums: null, name: 'value', type: ['integer'] }], help: 'Panel y. The y coordinate is the number of rows from the top edge of the grid' } },
      withY(value=0): {
        gridPos+: {
          y: value,
        },
      },
    },
  '#withId': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['integer'] }], help: 'Unique identifier of the panel. Generated by Grafana when creating a new panel. It must be unique within a dashboard, but not globally.' } },
  withId(value): {
    id: value,
  },
  '#withPanels': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['array'] }], help: '' } },
  withPanels(value): {
    panels:
      (if std.isArray(value)
       then value
       else [value]),
  },
  '#withPanelsMixin': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['array'] }], help: '' } },
  withPanelsMixin(value): {
    panels+:
      (if std.isArray(value)
       then value
       else [value]),
  },
  '#withRepeat': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['string'] }], help: 'Name of template variable to repeat for.' } },
  withRepeat(value): {
    repeat: value,
  },
  '#withTitle': { 'function': { args: [{ default: null, enums: null, name: 'value', type: ['string'] }], help: 'Row title' } },
  withTitle(value): {
    title: value,
  },
  '#withType': { 'function': { args: [], help: '' } },
  withType(): {
    type: 'row',
  },
}
+ (import '../custom/row.libsonnet')

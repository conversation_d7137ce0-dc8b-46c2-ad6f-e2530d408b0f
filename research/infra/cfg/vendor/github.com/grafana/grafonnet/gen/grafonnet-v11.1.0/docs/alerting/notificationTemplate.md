# notificationTemplate

grafonnet.alerting.notificationTemplate

## Index

* [`fn withName(value)`](#fn-withname)
* [`fn withProvenance(value)`](#fn-withprovenance)
* [`fn withTemplate(value)`](#fn-withtemplate)

## Fields

### fn withName

```jsonnet
withName(value)
```

PARAMETERS:

* **value** (`string`)


### fn withProvenance

```jsonnet
withProvenance(value)
```

PARAMETERS:

* **value** (`string`)


### fn withTemplate

```jsonnet
withTemplate(value)
```

PARAMETERS:

* **value** (`string`)


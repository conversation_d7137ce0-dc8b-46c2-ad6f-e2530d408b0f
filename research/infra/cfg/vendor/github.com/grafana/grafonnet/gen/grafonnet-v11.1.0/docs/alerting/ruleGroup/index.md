# ruleGroup

grafonnet.alerting.ruleGroup

## Subpackages

* [rule](rule/index.md)

## Index

* [`fn withFolderUid(value)`](#fn-withfolderuid)
* [`fn withInterval(value)`](#fn-withinterval)
* [`fn withName(value)`](#fn-withname)
* [`fn withRules(value)`](#fn-withrules)
* [`fn withRulesMixin(value)`](#fn-withrulesmixin)

## Fields

### fn withFolderUid

```jsonnet
withFolderUid(value)
```

PARAMETERS:

* **value** (`string`)


### fn withInterval

```jsonnet
withInterval(value)
```

PARAMETERS:

* **value** (`integer`)

Duration in seconds.
### fn withName

```jsonnet
withName(value)
```

PARAMETERS:

* **value** (`string`)


### fn withRules

```jsonnet
withRules(value)
```

PARAMETERS:

* **value** (`array`)


### fn withRulesMixin

```jsonnet
withRulesMixin(value)
```

PARAMETERS:

* **value** (`array`)


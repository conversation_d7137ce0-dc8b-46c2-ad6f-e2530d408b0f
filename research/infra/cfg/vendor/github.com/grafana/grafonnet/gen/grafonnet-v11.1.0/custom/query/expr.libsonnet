{
  '#': {
    help: 'Server Side Expression operations for grafonnet.alerting.ruleGroup.rule',
    name: 'expr',
  },
  TypeMath+: { '#': { help: 'grafonnet.query.expr.TypeMath', name: 'TypeMath' } },
  TypeReduce+: { '#': { help: 'grafonnet.query.expr.TypeReduce', name: 'TypeReduce' } },
  TypeResample+: { '#': { help: 'grafonnet.query.expr.TypeResample', name: 'TypeResample' } },
  TypeClassicConditions+: { '#': { help: 'grafonnet.query.expr.TypeClassicConditions', name: 'TypeClassicConditions' } },
  TypeThreshold+: { '#': { help: 'grafonnet.query.expr.TypeThreshold', name: 'TypeThreshold' } },
  TypeSql+: { '#': { help: 'grafonnet.query.expr.TypeSql', name: 'TypeSql' } },
}

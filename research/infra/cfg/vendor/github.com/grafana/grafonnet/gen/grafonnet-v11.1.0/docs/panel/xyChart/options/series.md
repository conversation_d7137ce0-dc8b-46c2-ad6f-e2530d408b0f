# series



## Index

* [`fn withAxisBorderShow(value=true)`](#fn-withaxisbordershow)
* [`fn withAxisCenteredZero(value=true)`](#fn-withaxiscenteredzero)
* [`fn withAxisColorMode(value)`](#fn-withaxiscolormode)
* [`fn withAxisGridShow(value=true)`](#fn-withaxisgridshow)
* [`fn withAxisLabel(value)`](#fn-withaxislabel)
* [`fn withAxisPlacement(value)`](#fn-withaxisplacement)
* [`fn withAxisSoftMax(value)`](#fn-withaxissoftmax)
* [`fn withAxisSoftMin(value)`](#fn-withaxissoftmin)
* [`fn withAxisWidth(value)`](#fn-withaxiswidth)
* [`fn withFrame(value)`](#fn-withframe)
* [`fn withHideFrom(value)`](#fn-withhidefrom)
* [`fn withHideFromMixin(value)`](#fn-withhidefrommixin)
* [`fn withLabel(value="auto")`](#fn-withlabel)
* [`fn withLabelValue(value)`](#fn-withlabelvalue)
* [`fn withLabelValueMixin(value)`](#fn-withlabelvaluemixin)
* [`fn withLineColor(value)`](#fn-withlinecolor)
* [`fn withLineColorMixin(value)`](#fn-withlinecolormixin)
* [`fn withLineStyle(value)`](#fn-withlinestyle)
* [`fn withLineStyleMixin(value)`](#fn-withlinestylemixin)
* [`fn withLineWidth(value)`](#fn-withlinewidth)
* [`fn withName(value)`](#fn-withname)
* [`fn withPointColor(value)`](#fn-withpointcolor)
* [`fn withPointColorMixin(value)`](#fn-withpointcolormixin)
* [`fn withPointSize(value)`](#fn-withpointsize)
* [`fn withPointSizeMixin(value)`](#fn-withpointsizemixin)
* [`fn withScaleDistribution(value)`](#fn-withscaledistribution)
* [`fn withScaleDistributionMixin(value)`](#fn-withscaledistributionmixin)
* [`fn withShow(value="points")`](#fn-withshow)
* [`fn withX(value)`](#fn-withx)
* [`fn withY(value)`](#fn-withy)
* [`obj hideFrom`](#obj-hidefrom)
  * [`fn withLegend(value=true)`](#fn-hidefromwithlegend)
  * [`fn withTooltip(value=true)`](#fn-hidefromwithtooltip)
  * [`fn withViz(value=true)`](#fn-hidefromwithviz)
* [`obj labelValue`](#obj-labelvalue)
  * [`fn withField(value)`](#fn-labelvaluewithfield)
  * [`fn withFixed(value)`](#fn-labelvaluewithfixed)
  * [`fn withMode(value)`](#fn-labelvaluewithmode)
* [`obj lineColor`](#obj-linecolor)
  * [`fn withField(value)`](#fn-linecolorwithfield)
  * [`fn withFixed(value)`](#fn-linecolorwithfixed)
* [`obj lineStyle`](#obj-linestyle)
  * [`fn withDash(value)`](#fn-linestylewithdash)
  * [`fn withDashMixin(value)`](#fn-linestylewithdashmixin)
  * [`fn withFill(value)`](#fn-linestylewithfill)
* [`obj pointColor`](#obj-pointcolor)
  * [`fn withField(value)`](#fn-pointcolorwithfield)
  * [`fn withFixed(value)`](#fn-pointcolorwithfixed)
* [`obj pointSize`](#obj-pointsize)
  * [`fn withField(value)`](#fn-pointsizewithfield)
  * [`fn withFixed(value)`](#fn-pointsizewithfixed)
  * [`fn withMax(value)`](#fn-pointsizewithmax)
  * [`fn withMin(value)`](#fn-pointsizewithmin)
  * [`fn withMode(value)`](#fn-pointsizewithmode)
* [`obj scaleDistribution`](#obj-scaledistribution)
  * [`fn withLinearThreshold(value)`](#fn-scaledistributionwithlinearthreshold)
  * [`fn withLog(value)`](#fn-scaledistributionwithlog)
  * [`fn withType(value)`](#fn-scaledistributionwithtype)

## Fields

### fn withAxisBorderShow

```jsonnet
withAxisBorderShow(value=true)
```

PARAMETERS:

* **value** (`boolean`)
   - default value: `true`


### fn withAxisCenteredZero

```jsonnet
withAxisCenteredZero(value=true)
```

PARAMETERS:

* **value** (`boolean`)
   - default value: `true`


### fn withAxisColorMode

```jsonnet
withAxisColorMode(value)
```

PARAMETERS:

* **value** (`string`)
   - valid values: `"text"`, `"series"`

TODO docs
### fn withAxisGridShow

```jsonnet
withAxisGridShow(value=true)
```

PARAMETERS:

* **value** (`boolean`)
   - default value: `true`


### fn withAxisLabel

```jsonnet
withAxisLabel(value)
```

PARAMETERS:

* **value** (`string`)


### fn withAxisPlacement

```jsonnet
withAxisPlacement(value)
```

PARAMETERS:

* **value** (`string`)
   - valid values: `"auto"`, `"top"`, `"right"`, `"bottom"`, `"left"`, `"hidden"`

TODO docs
### fn withAxisSoftMax

```jsonnet
withAxisSoftMax(value)
```

PARAMETERS:

* **value** (`number`)


### fn withAxisSoftMin

```jsonnet
withAxisSoftMin(value)
```

PARAMETERS:

* **value** (`number`)


### fn withAxisWidth

```jsonnet
withAxisWidth(value)
```

PARAMETERS:

* **value** (`number`)


### fn withFrame

```jsonnet
withFrame(value)
```

PARAMETERS:

* **value** (`number`)


### fn withHideFrom

```jsonnet
withHideFrom(value)
```

PARAMETERS:

* **value** (`object`)

TODO docs
### fn withHideFromMixin

```jsonnet
withHideFromMixin(value)
```

PARAMETERS:

* **value** (`object`)

TODO docs
### fn withLabel

```jsonnet
withLabel(value="auto")
```

PARAMETERS:

* **value** (`string`)
   - default value: `"auto"`
   - valid values: `"auto"`, `"never"`, `"always"`

TODO docs
### fn withLabelValue

```jsonnet
withLabelValue(value)
```

PARAMETERS:

* **value** (`object`)


### fn withLabelValueMixin

```jsonnet
withLabelValueMixin(value)
```

PARAMETERS:

* **value** (`object`)


### fn withLineColor

```jsonnet
withLineColor(value)
```

PARAMETERS:

* **value** (`object`)


### fn withLineColorMixin

```jsonnet
withLineColorMixin(value)
```

PARAMETERS:

* **value** (`object`)


### fn withLineStyle

```jsonnet
withLineStyle(value)
```

PARAMETERS:

* **value** (`object`)

TODO docs
### fn withLineStyleMixin

```jsonnet
withLineStyleMixin(value)
```

PARAMETERS:

* **value** (`object`)

TODO docs
### fn withLineWidth

```jsonnet
withLineWidth(value)
```

PARAMETERS:

* **value** (`integer`)


### fn withName

```jsonnet
withName(value)
```

PARAMETERS:

* **value** (`string`)


### fn withPointColor

```jsonnet
withPointColor(value)
```

PARAMETERS:

* **value** (`object`)


### fn withPointColorMixin

```jsonnet
withPointColorMixin(value)
```

PARAMETERS:

* **value** (`object`)


### fn withPointSize

```jsonnet
withPointSize(value)
```

PARAMETERS:

* **value** (`object`)


### fn withPointSizeMixin

```jsonnet
withPointSizeMixin(value)
```

PARAMETERS:

* **value** (`object`)


### fn withScaleDistribution

```jsonnet
withScaleDistribution(value)
```

PARAMETERS:

* **value** (`object`)

TODO docs
### fn withScaleDistributionMixin

```jsonnet
withScaleDistributionMixin(value)
```

PARAMETERS:

* **value** (`object`)

TODO docs
### fn withShow

```jsonnet
withShow(value="points")
```

PARAMETERS:

* **value** (`string`)
   - default value: `"points"`
   - valid values: `"points"`, `"lines"`, `"points+lines"`


### fn withX

```jsonnet
withX(value)
```

PARAMETERS:

* **value** (`string`)


### fn withY

```jsonnet
withY(value)
```

PARAMETERS:

* **value** (`string`)


### obj hideFrom


#### fn hideFrom.withLegend

```jsonnet
hideFrom.withLegend(value=true)
```

PARAMETERS:

* **value** (`boolean`)
   - default value: `true`


#### fn hideFrom.withTooltip

```jsonnet
hideFrom.withTooltip(value=true)
```

PARAMETERS:

* **value** (`boolean`)
   - default value: `true`


#### fn hideFrom.withViz

```jsonnet
hideFrom.withViz(value=true)
```

PARAMETERS:

* **value** (`boolean`)
   - default value: `true`


### obj labelValue


#### fn labelValue.withField

```jsonnet
labelValue.withField(value)
```

PARAMETERS:

* **value** (`string`)

fixed: T -- will be added by each element
#### fn labelValue.withFixed

```jsonnet
labelValue.withFixed(value)
```

PARAMETERS:

* **value** (`string`)


#### fn labelValue.withMode

```jsonnet
labelValue.withMode(value)
```

PARAMETERS:

* **value** (`string`)
   - valid values: `"fixed"`, `"field"`, `"template"`


### obj lineColor


#### fn lineColor.withField

```jsonnet
lineColor.withField(value)
```

PARAMETERS:

* **value** (`string`)

fixed: T -- will be added by each element
#### fn lineColor.withFixed

```jsonnet
lineColor.withFixed(value)
```

PARAMETERS:

* **value** (`string`)

color value
### obj lineStyle


#### fn lineStyle.withDash

```jsonnet
lineStyle.withDash(value)
```

PARAMETERS:

* **value** (`array`)


#### fn lineStyle.withDashMixin

```jsonnet
lineStyle.withDashMixin(value)
```

PARAMETERS:

* **value** (`array`)


#### fn lineStyle.withFill

```jsonnet
lineStyle.withFill(value)
```

PARAMETERS:

* **value** (`string`)
   - valid values: `"solid"`, `"dash"`, `"dot"`, `"square"`


### obj pointColor


#### fn pointColor.withField

```jsonnet
pointColor.withField(value)
```

PARAMETERS:

* **value** (`string`)

fixed: T -- will be added by each element
#### fn pointColor.withFixed

```jsonnet
pointColor.withFixed(value)
```

PARAMETERS:

* **value** (`string`)

color value
### obj pointSize


#### fn pointSize.withField

```jsonnet
pointSize.withField(value)
```

PARAMETERS:

* **value** (`string`)

fixed: T -- will be added by each element
#### fn pointSize.withFixed

```jsonnet
pointSize.withFixed(value)
```

PARAMETERS:

* **value** (`number`)


#### fn pointSize.withMax

```jsonnet
pointSize.withMax(value)
```

PARAMETERS:

* **value** (`number`)


#### fn pointSize.withMin

```jsonnet
pointSize.withMin(value)
```

PARAMETERS:

* **value** (`number`)


#### fn pointSize.withMode

```jsonnet
pointSize.withMode(value)
```

PARAMETERS:

* **value** (`string`)
   - valid values: `"linear"`, `"quad"`

| *"linear"
### obj scaleDistribution


#### fn scaleDistribution.withLinearThreshold

```jsonnet
scaleDistribution.withLinearThreshold(value)
```

PARAMETERS:

* **value** (`number`)


#### fn scaleDistribution.withLog

```jsonnet
scaleDistribution.withLog(value)
```

PARAMETERS:

* **value** (`number`)


#### fn scaleDistribution.withType

```jsonnet
scaleDistribution.withType(value)
```

PARAMETERS:

* **value** (`string`)
   - valid values: `"linear"`, `"log"`, `"ordinal"`, `"symlog"`

TODO docs
# folder

grafonnet.folder

## Index

* [`fn withParentUid(value)`](#fn-withparentuid)
* [`fn withTitle(value)`](#fn-withtitle)
* [`fn withUid(value)`](#fn-withuid)

## Fields

### fn withParentUid

```jsonnet
withParentUid(value)
```

PARAMETERS:

* **value** (`string`)

only used if nested folders are enabled
### fn withTitle

```jsonnet
withTitle(value)
```

PARAMETERS:

* **value** (`string`)

Folder title
### fn withUid

```jsonnet
withUid(value)
```

PARAMETERS:

* **value** (`string`)

Unique folder id
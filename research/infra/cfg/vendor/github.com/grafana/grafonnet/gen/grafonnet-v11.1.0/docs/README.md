# grafonnet

Jsonnet library for rendering Grafana resources
## Install

```
jb install github.com/grafana/grafonnet/gen/grafonnet-v11.1.0@main
```

## Usage

```jsonnet
local grafonnet = import "github.com/grafana/grafonnet/gen/grafonnet-v11.1.0/main.libsonnet"
```


## Subpackages

* [accesspolicy](accesspolicy/index.md)
* [alerting](alerting/index.md)
* [dashboard](dashboard/index.md)
* [folder](folder.md)
* [librarypanel](librarypanel/index.md)
* [panel](panel/index.md)
* [preferences](preferences.md)
* [publicdashboard](publicdashboard.md)
* [query](query/index.md)
* [role](role.md)
* [rolebinding](rolebinding.md)
* [team](team.md)
* [util](util.md)

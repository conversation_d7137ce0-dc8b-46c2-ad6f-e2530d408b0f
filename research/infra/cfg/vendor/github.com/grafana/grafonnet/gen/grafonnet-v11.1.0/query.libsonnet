// This file is generated, do not manually edit.
{
  '#': { help: 'grafonnet.query', name: 'query' },
  azureMonitor: import 'query/azureMonitor.libsonnet',
  cloudWatch: import 'query/cloudWatch.libsonnet',
  elasticsearch: import 'query/elasticsearch.libsonnet',
  expr: import 'query/expr.libsonnet',
  googleCloudMonitoring: import 'query/googleCloudMonitoring.libsonnet',
  grafanaPyroscope: import 'query/grafanaPyroscope.libsonnet',
  loki: import 'query/loki.libsonnet',
  parca: import 'query/parca.libsonnet',
  prometheus: import 'query/prometheus.libsonnet',
  tempo: import 'query/tempo.libsonnet',
  testData: import 'query/testData.libsonnet',
}

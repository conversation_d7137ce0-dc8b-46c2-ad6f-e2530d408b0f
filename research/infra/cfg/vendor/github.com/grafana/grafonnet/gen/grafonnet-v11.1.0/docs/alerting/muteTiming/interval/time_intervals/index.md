# time_intervals



## Subpackages

* [times](times.md)

## Index

* [`fn withDaysOfMonth(value)`](#fn-withdaysofmonth)
* [`fn withDaysOfMonthMixin(value)`](#fn-withdaysofmonthmixin)
* [`fn withLocation(value)`](#fn-withlocation)
* [`fn withMonths(value)`](#fn-withmonths)
* [`fn withMonthsMixin(value)`](#fn-withmonthsmixin)
* [`fn withTimes(value)`](#fn-withtimes)
* [`fn withTimesMixin(value)`](#fn-withtimesmixin)
* [`fn withWeekdays(value)`](#fn-withweekdays)
* [`fn withWeekdaysMixin(value)`](#fn-withweekdaysmixin)
* [`fn withYears(value)`](#fn-withyears)
* [`fn withYearsMixin(value)`](#fn-withyearsmixin)

## Fields

### fn withDaysOfMonth

```jsonnet
withDaysOfMonth(value)
```

PARAMETERS:

* **value** (`array`)


### fn withDaysOfMonthMixin

```jsonnet
withDaysOfMonthMixin(value)
```

PARAMETERS:

* **value** (`array`)


### fn withLocation

```jsonnet
withLocation(value)
```

PARAMETERS:

* **value** (`string`)


### fn withMonths

```jsonnet
withMonths(value)
```

PARAMETERS:

* **value** (`array`)


### fn withMonthsMixin

```jsonnet
withMonthsMixin(value)
```

PARAMETERS:

* **value** (`array`)


### fn withTimes

```jsonnet
withTimes(value)
```

PARAMETERS:

* **value** (`array`)


### fn withTimesMixin

```jsonnet
withTimesMixin(value)
```

PARAMETERS:

* **value** (`array`)


### fn withWeekdays

```jsonnet
withWeekdays(value)
```

PARAMETERS:

* **value** (`array`)


### fn withWeekdaysMixin

```jsonnet
withWeekdaysMixin(value)
```

PARAMETERS:

* **value** (`array`)


### fn withYears

```jsonnet
withYears(value)
```

PARAMETERS:

* **value** (`array`)


### fn withYearsMixin

```jsonnet
withYearsMixin(value)
```

PARAMETERS:

* **value** (`array`)


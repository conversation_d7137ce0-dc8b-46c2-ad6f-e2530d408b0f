# properties



## Index

* [`fn withId(value="")`](#fn-withid)
* [`fn withValue(value)`](#fn-withvalue)
* [`fn withValueMixin(value)`](#fn-withvaluemixin)

## Fields

### fn withId

```jsonnet
withId(value="")
```

PARAMETERS:

* **value** (`string`)
   - default value: `""`


### fn withValue

```jsonnet
withValue(value)
```

PARAMETERS:

* **value** (`object`)


### fn withValueMixin

```jsonnet
withValueMixin(value)
```

PARAMETERS:

* **value** (`object`)


# muteTiming

grafonnet.alerting.muteTiming

## Subpackages

* [interval](interval/index.md)

## Index

* [`fn withIntervals(value)`](#fn-withintervals)
* [`fn withIntervalsMixin(value)`](#fn-withintervalsmixin)
* [`fn withName(value)`](#fn-withname)

## Fields

### fn withIntervals

```jsonnet
withIntervals(value)
```

PARAMETERS:

* **value** (`array`)


### fn withIntervalsMixin

```jsonnet
withIntervalsMixin(value)
```

PARAMETERS:

* **value** (`array`)


### fn withName

```jsonnet
withName(value)
```

PARAMETERS:

* **value** (`string`)


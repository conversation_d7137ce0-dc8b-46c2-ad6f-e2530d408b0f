K8S_CONF := secret.jsonnet
CLUSTERS := cw-east4 gcp-us1

KUBECFG  := kubecfg $$context_flag --tla-code="cluster_name='$$cluster'" --tla-code="encrypted_data='$$enc_data'"

FILES   := $(wildcard eng_secrets/*.json)

show:
	make loop LOOP_COMMAND=show

diff:
	make loop LOOP_COMMAND="diff --diff-strategy=subset"

apply:
	make loop LOOP_COMMAND=update

# the get-contexts grep is to detect if we're using a service account on a pod that
# has no contexts
loop:
	for file in $(FILES); do \
		enc_data=$$(cat $$file); \
		for cluster in $(CLUSTERS); do \
			echo ====== "$$cluster" ======; \
			context=$$(jsonnet -A name="$$cluster" -A prop=context_admin -Se "(import '../../cfg/clusters/clusters.jsonnet').cluster_prop") ; \
			available_contexts=$$(kubectl config get-contexts -o name | grep $$context); \
			context_flag=$$(if [ -n "$$available_contexts" ]; then echo "--context=$$context"; fi); \
			$(KUBECFG) $(LOOP_COMMAND) $(K8S_CONF); \
		done \
	done

local clusters = import '../clusters/clusters.jsonnet';

function(cluster_name, encrypted_data) {
  local encrypted = std.parseJson(encrypted_data),

  by_cluster(obj)::
    if std.objectHas(obj, cluster_name)
    then obj[cluster_name]
    else std.get(obj, 'default'),

  filter_by_cluster(obj):: {
    [kv.key]: kv.value
    for kv in std.objectKeysValues(obj)
    if !std.isObject(kv.value) || std.member([null, cluster_name], std.get(kv.value, 'cluster_name'))
  },

  C:: clusters.cluster(cluster_name),
  k8s:: $.C.k8s + {
    BaseObject+:: {
      cluster_name:: null,
    },
  },

  ret:: $.filter_by_cluster({
    sealed_secret: $.k8s.SealedSecret + {
      name:: encrypted.secret_name,
      metadata+: {
        labels+: {
          'aug.app': 'research-secret',
          'aug.creator': encrypted.creator,
        },
      },
      namespace:: 'eng-secrets',
      encryptedData: $.by_cluster(encrypted.encrypted_data),
    },
  }),
}.ret

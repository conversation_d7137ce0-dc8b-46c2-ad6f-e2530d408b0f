package cfg

import (
	"testing"
)

func TestDropDots(t *testing.T) {
	tests := map[string]string{
		"":                       "",
		"/":                      "/",
		".":                      "",
		"..":                     "",
		"/abs/./path":            "/abs/path",
		"/abs/../path":           "/path",
		"/abs/./../path":         "/path",
		"/c1/c2/../../path":      "/path",
		"c1/c2/../../path":       "path",
		"c1/c2/../../../../path": "path",
	}
	for orig, want := range tests {
		t.Run(orig, func(t *testing.T) {
			if got, want := dropDots(orig), want; got != want {
				t.<PERSON>rf("got %v, want %v.", got, want)
			}
		})
	}
}

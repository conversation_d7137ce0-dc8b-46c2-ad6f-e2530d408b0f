load("@io_bazel_rules_go//go:def.bzl", "go_library")

package(default_visibility = ["//research/infra:internal"])

go_library(
    name = "tokens",
    srcs = [
        "db.go",
        "github.go",
        "http.go",
        "jsonnet.go",
        "k8s.go",
    ],
    importpath = "github.com/augmentcode/augment/research/infra/lib/augment/tokens",
    deps = [
        "//infra/lib/github",
        "//infra/lib/k8s",
        "//infra/lib/logger",
        "//research/infra/cfg",
        "@org_golang_x_sync//errgroup",
    ],
)

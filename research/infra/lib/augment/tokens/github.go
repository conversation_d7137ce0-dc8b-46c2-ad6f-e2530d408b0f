package tokens

import (
	"archive/tar"
	"compress/gzip"
	"context"
	"io"
	"strings"

	"github.com/augmentcode/augment/infra/lib/github"
	"github.com/augmentcode/augment/research/infra/cfg"
)

func FromGitHub(ctx context.Context, gh *github.Client, ref string) (*DB, error) {
	/// Start with an empty jsonnet VM.
	vm := cfg.NewEmptyVM()

	/// Stream GitHub TGZ files into vm.
	if err := gh.Download(ctx, ref, func(r io.Reader) error {
		gzr, err := gzip.NewReader(r)
		if err != nil {
			return err
		}
		defer gzr.Close()
		tr := tar.NewReader(gzr)
		for {
			hdr, err := tr.Next()
			if err == io.EOF {
				break
			}
			if err != nil {
				return err
			}
			if hdr.Typeflag != tar.TypeReg {
				continue
			}
			if hdr.Size > 1*1024*1024 {
				continue
			}
			buf, err := io.ReadAll(tr)
			if err != nil {
				return err
			}
			parts := strings.Split(hdr.Name, "/")
			parts = parts[1:]
			fname := strings.Join(parts, "/")
			vm.AddFile(fname, buf)
		}
		return nil
	}); err != nil {
		return nil, err
	}

	/// Now, wrap FromJsonnet.
	return FromJsonnet(vm)
}

package tokens

import (
	"context"

	"github.com/augmentcode/augment/infra/lib/k8s"
)

const (
	K8sSecretKey = "internal_users.json" // pragma: allowlist secret
)

func FromK8s(ctx context.Context, k *k8s.Client, secname string) (*DB, error) {
	sec, err := k.Get<PERSON>(ctx, secname)
	if err != nil {
		return nil, err
	}
	str, err := sec.KeyErr(K8sSecretKey)
	if err != nil {
		return nil, err
	}
	return FromJSON(str)
}

func FromK8sWatch(secname string) (*DB, func(context.Context, *k8s.Client) error) {
	db := FromNative(map[string]User{})
	watcher := func(ctx context.Context, k *k8s.Client) error {
		return k.WatchSecrets(ctx, func(ev string, sec *k8s.Secret) {
			db.LogInfo("%s: Received watch event %s.", sec.<PERSON>Name(), ev)
			str, err := sec.KeyErr(K8sSecretKey)
			if err != nil {
				db.LogErr("%s: %v.", sec.ShortName(), err)
				return
			}
			db2, err := FromJSON(str)
			if err != nil {
				db.LogErr("%s: %v.", sec.ShortName(), err)
				return
			}
			db.lk.Lock()
			defer db.lk.Unlock()
			oldl := len(db.users)
			db.users = db2.users
			db.LogInfo("%s: updated %d -> %d (%d) entries.", sec.ShortName(), oldl, len(db.users), len(db2.users))
		}, k8s.ListByName(secname))
	}
	return db, watcher
}

func (db *DB) ToK8s(ctx context.Context, k *k8s.Client, secname string) error {
	jtxt, err := db.JSON()
	if err != nil {
		return err
	}

	sec, err := k.GetSecret(ctx, secname)
	err = k8s.NotFoundOK(err)
	if err != nil {
		return err
	}

	if sec == nil {
		sec = k8s.NewSecret(nil)
		sec.Raw().ObjectMeta.Name = secname
	}
	sec.Raw().Data = map[string][]byte{
		K8sSecretKey: []byte(jtxt),
	}

	_, err = k.ReplaceSecret(ctx, sec)
	return err
}

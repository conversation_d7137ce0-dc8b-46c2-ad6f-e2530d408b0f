package tokens

import (
	"github.com/augmentcode/augment/research/infra/cfg"
)

const (
	InternalTokenPath = "deploy/tenants/tokens/internal_users.jsonnet"
)

func FromJsonnet(vm *cfg.VM) (*DB, error) {
	/// Evaluate jsonnet.
	raw := []struct {
		UserID string `json:"user_id"`
		Sha256 string `json:"token_sha256"`
	}{}
	tla := map[string]string{
		"tenant_name": "null",
	}
	if err := vm.EvalJSON(InternalTokenPath, tla, "", &raw); err != nil {
		return nil, err
	}

	/// Convert into internal DB format.
	users := map[string]User{}
	for _, ru := range raw {
		users[ru.Sha256] = User{
			Sha256: ru.Sha256,
			ID:     ru.UserID,
		}
	}

	return FromNative(users), nil
}

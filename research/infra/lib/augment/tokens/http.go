package tokens

import (
	"context"
	"net/http"
	"strconv"
	"strings"

	"golang.org/x/sync/errgroup"

	"github.com/augmentcode/augment/infra/lib/k8s"
)

func ServeFromK8s(ctx context.Context, k *k8s.Client, secname string, port int32) error {
	db, watcher := FromK8sWatch(secname)
	grp, gctx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		return watcher(gctx, k)
	})
	grp.Go(func() error {
		addr := ":" + strconv.Itoa(int(port))
		return http.ListenAndServe(addr, http.HandlerFunc(db.handle))
	})

	return grp.Wait()
}

func (db *DB) handle(w http.ResponseWriter, r *http.Request) {
	db.LogInfo("%s %v from %s", r.Method, r.URL, r.RemoteAddr)
	auth := strings.TrimSpace(r.Header.Get("Authorization"))
	tok, ok := strings.CutPrefix(auth, "Bearer")
	if !ok {
		db.LogInfo("%s %v from %s -> Not Found -> %v", r.<PERSON>, r.URL, r.RemoteAddr, http.StatusUnauthorized)
		http.Error(w, "Bearer token not found.", http.StatusUnauthorized)
		return
	}
	if tok = strings.TrimSpace(tok); tok == "" {
		db.LogInfo("%s %v from %s -> Empty -> %v", r.Method, r.URL, r.RemoteAddr, http.StatusUnauthorized)
		http.Error(w, "Empty Bearer token.", http.StatusUnauthorized)
		return
	}
	if u, err := db.GetErr(tok); err != nil {
		db.LogInfo("%s %v from %s -> GetErr [%v] -> %v", r.Method, r.URL, r.RemoteAddr, err, http.StatusUnauthorized)
		http.Error(w, err.Error(), http.StatusUnauthorized)
		return
	} else {
		db.LogInfo("%s %v from %s -> User %s -> %v", r.Method, r.URL, r.RemoteAddr, u.ID, http.StatusOK)
	}
	w.WriteHeader(http.StatusOK)
}

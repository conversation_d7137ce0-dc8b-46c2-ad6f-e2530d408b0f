package tokens

import (
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"sync"

	"github.com/augmentcode/augment/infra/lib/logger"
)

type User struct {
	Sha256 string `json:"sha256"`
	ID     string `json:"id"`
}

type DB struct {
	logger.Logger
	users map[string]User
	lk    sync.RWMutex
}

func FromNative(users map[string]User) *DB {
	return &DB{
		Logger: logger.New(nil),
		users:  users,
	}
}

func FromJSON(buf string) (*DB, error) {
	users := map[string]User{}
	if err := json.Unmarshal([]byte(buf), &users); err != nil {
		return nil, err
	}
	return FromNative(users), nil
}

func (db *DB) JSON() (string, error) {
	db.lk.RLock()
	defer db.lk.RUnlock()
	buf, err := json.MarshalIndent(db.users, "", "  ")
	if err != nil {
		return "", err
	}
	return string(buf), nil
}

func (db *DB) Get(sec string) (User, bool) {
	db.lk.RLock()
	defer db.lk.RUnlock()
	sum := sha256.Sum256([]byte(sec))
	str := fmt.Sprintf("%x", sum)
	u, ok := db.users[str]
	return u, ok
}

func (db *DB) GetErr(sec string) (User, error) {
	db.lk.RLock()
	defer db.lk.RUnlock()
	sum := sha256.Sum256([]byte(sec))
	str := fmt.Sprintf("%x", sum)
	if u, ok := db.users[str]; !ok {
		return User{}, fmt.Errorf("%s: NotFound", str)
	} else {
		return u, nil
	}
}

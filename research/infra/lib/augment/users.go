package augment

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"os/user"
	"strings"
	"time"

	"golang.org/x/sync/errgroup"

	"github.com/augmentcode/augment/infra/lib/k8s"
	cfglib "github.com/augmentcode/augment/research/infra/cfg/lib"
)

const (
	AuthorizedKeysConfigMap = "augment-user-ssh-authorized-keys"
	GitHubUserNameConfigMap = "augment-user-github-usernames"
)

var (
	// Swappable for testing.
	tHostname    = os.Hostname
	tUserCurrent = user.Current
	tUserLookup  = user.Lookup
	tCmdRun      = func(cmd *exec.Cmd) error { return cmd.Run() }
)

func filterInvalidUsers(u string) string {
	if map[string]bool{
		"root":    true,
		"augment": true,
	}[u] {
		return ""
	}
	return u
}

// WhoAmIErr wraps WhoAmI() and returns an error on an empty result.
func WhoAmIErr() (string, error) {
	if u := WhoAmI(); u == "" {
		return "", fmt.Errorf("augment.WhoAmI(): not found")
	} else {
		return u, nil
	}
}

// WhoAmI returns the username of running user based on:
//   - Reading the `/startup/user` file.
//   - The current username if it's not `augment`.
//   - The system hostname if it has a `-dev*` suffix.
//   - Empty string if nothing can be determined.
func WhoAmI() string {
	if u := WhoAmIMetadata(); u != "" {
		return u
	}
	if u := WhoAmISystem(); u != "" {
		return u
	}
	if u := WhoAmIHostname(); u != "" {
		return u
	}
	if u := WhoAmIAugmentConfig(); u != "" {
		return u
	}
	return ""
}

// WhoAmIMetadata returns the username read from DevPod `/startup/user` metadata.
func WhoAmIMetadata() string {
	if buf, err := tReadFile("/startup/user"); err == nil {
		return filterInvalidUsers(strings.TrimSpace(string(buf)))
	}
	return ""
}

// WhoAmIHostname returns a username parsed from a hostname that looks like <user>-*dev*.
func WhoAmIHostname() string {
	hostname, err := tHostname()
	if err != nil {
		return ""
	}
	before, after, found := strings.Cut(hostname, "-")
	if !found || !strings.Contains(after, "dev") {
		return ""
	}
	return filterInvalidUsers(before)
}

// WhoAmISystem returns the system username, including "augment".
func WhoAmISystem() string {
	u, err := tUserCurrent()
	if err != nil {
		return ""
	}
	return filterInvalidUsers(u.Username)
}

// WhoAmIAugmentConfig returns the username, if present, in ~/.augment/user.json. Like the other
// WhoAmI* methods, any errors will result in an empty response but are otherwise ignored.
func WhoAmIAugmentConfig() string {
	cfg := struct {
		Name string `json:"name"`
	}{}
	if u, err := tUserCurrent(); err != nil {
		return ""
	} else if buf, err := tReadFile(u.HomeDir + "/.augment/user.json"); err != nil {
		return ""
	} else if err := json.Unmarshal(buf, &cfg); err != nil {
		return ""
	} else {
		return filterInvalidUsers(cfg.Name)
	}
}

// OSUserAdd runs `useradd` for an Augment user. Creating home is optional. It
// is assumed that an "augment" groups is available and added as a
// supplementary group. (Errors are logged).
func (c Clients) OSUserAdd(ctx context.Context, username string, createHome bool) error {
	username = strings.TrimSpace(username)
	if username == "" {
		return fmt.Errorf("useradd: empty username")
	}
	if username == "root" {
		return fmt.Errorf("useradd: root? no funny business")
	}

	cmdline := []string{"useradd"}
	cmdline = append(cmdline, "--shell", "/bin/bash")
	if createHome {
		cmdline = append(cmdline, "-m")
	}
	cmdline = append(cmdline, "-U", "-G", "augment")
	cmdline = append(cmdline, username)

	cmd := exec.CommandContext(ctx, cmdline[0], cmdline[1:]...)
	cmd.Stdin = nil
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := tCmdRun(cmd); err != nil {
		c.LogErr("$(%s): %v.", strings.Join(cmdline, " "), err)
		return err
	}

	c.LogInfo("$(%s): success.", strings.Join(cmdline, " "))
	return nil
}

// OSUserSet runs OSUserAdd only if needed. (Errors are logged).
func (c Clients) OSUserSet(ctx context.Context, username string, createHome bool) error {
	username = strings.TrimSpace(username)
	if username == "" {
		return fmt.Errorf("userset: empty username")
	}
	if username == "root" {
		return fmt.Errorf("userset: root? no funny business")
	}
	if _, err := tUserLookup(username); err == nil {
		c.LogDebug("user.Lookup(%s): found. Skipping.", username)
		return nil
	} else if _, match := err.(user.UnknownUserError); !match {
		c.LogErr("user.Lookup(%s): %v. Skipping.", username, err)
		return err
	} else {
		c.LogNotice("user.Lookup(%s): %v. Creating...", username, err)
	}
	return c.OSUserAdd(ctx, username, createHome)
}

// OSUserSetAllFromConfigMap runs OSUserSet for all users (keys) in the given
// ConfigMap. If the ConfigMap is nil, it will be looked up from defaults.
// (Errors are logged).
func (c Clients) OSUserSetAllFromConfigMap(ctx context.Context, cm *k8s.ConfigMap, createHome bool) error {
	if cm == nil {
		var err error
		if cm, err = c.k8s.GetConfigMap(ctx, c.authKeysConfigMap); err != nil {
			return err
		}
	}
	errs := []error{}
	for _, username := range cm.Keys() {
		if err := c.OSUserSet(ctx, username, createHome); err != nil {
			errs = append(errs, err)
		}
	}
	return errors.Join(errs...)
}

// OSUserSetRun is a daemon that runs OSUserSetAllFromConfigMap continuously based
// on watch events from k8s. (Errors are logged).
func (c Clients) OSUserSetRun(ctx context.Context, createHome bool) error {
	cm, err := c.k8s.GetConfigMap(ctx, c.authKeysConfigMap)
	if err != nil {
		return err
	}
	return cm.Watch(ctx, c.k8s, func(eventType string, cm *k8s.ConfigMap) {
		_ = c.OSUserSetAllFromConfigMap(ctx, cm, createHome)
	})
}

// UserSASync assures the user has a personal K8s service account and accompanying RBAC policies.
func (c Clients) UserSASync(ctx context.Context, user string) error {
	u := cfglib.NewUser(user)

	grp, gctx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		_, err := c.k8s.UpdateOrCreateServiceAccount(gctx, u.DefaultServiceAccount())
		return err
	})
	for _, rb := range u.DefaultRoleBindings() {
		grp.Go(func() error {
			_, err := c.k8s.UpdateOrCreateRoleBinding(gctx, rb)
			return err
		})
	}
	return grp.Wait()
}

// UserSASyncAllFromConfigMap runs UserSASync for all users (keys) in the given
// ConfigMap. If he ConfigMap is nil, it will be looked up from defaults.
// (Errors are logged).
func (c Clients) UserSASyncAllFromConfigMap(ctx context.Context, cm *k8s.ConfigMap) error {
	if cm == nil {
		var err error
		if cm, err = c.k8s.GetConfigMap(ctx, c.authKeysConfigMap); err != nil {
			return err
		}
	}
	errs := []error{}
	for _, username := range cm.Keys() {
		if err := c.UserSASync(ctx, username); err != nil {
			errs = append(errs, err)
		}
	}
	return errors.Join(errs...)
}

// UserSASyncRun is a daemon that runs UserSASyncAllFromConfigMap continuously based
// on watch events from k8s. (Errors are logged).
func (c Clients) UserSASyncRun(ctx context.Context) error {
	cm, err := c.k8s.GetConfigMap(ctx, c.authKeysConfigMap)
	if err != nil {
		return err
	}
	return cm.Watch(ctx, c.k8s, func(eventType string, cm *k8s.ConfigMap) {
		if err := c.UserSASyncAllFromConfigMap(ctx, cm); err != nil {
			c.LogErr("UserSASyncRun(): %v", err)
		}
	})
}

func (c Clients) UserSAKubeConfig(ctx context.Context, name string) (*k8s.KubeConfig, error) {
	user := cfglib.NewUser(name)
	return c.k8s.KubeConfigFromServiceAccount(ctx, user.DefaultServiceAccount().Name(), "coreweave", "")
}

func (c Clients) UserSAWriteXdgOrPrint(ctx context.Context, user string, stdout bool) error {
	cfg, err := c.UserSAKubeConfig(ctx, user)
	if err != nil {
		return err
	}
	if stdout {
		fmt.Println(cfg.String())
		return nil
	} else {
		c.LogInfo("Writing KubeConfig snippet for %s", user)
		return cfg.WriteXdgSnippet(user, "")
	}
}

func (c Clients) UserSAWriteXdgOrPrintFromCM(ctx context.Context, cm *k8s.ConfigMap, stdout bool) error {
	if cm == nil {
		var err error
		if cm, err = c.k8s.GetConfigMap(ctx, c.authKeysConfigMap); err != nil {
			return err
		}
	}
	errs := []error{}
	for _, username := range cm.Keys() {
		if err := c.UserSAWriteXdgOrPrint(ctx, username, stdout); err != nil {
			errs = append(errs, err)
		}
	}
	return errors.Join(errs...)
}

func (c Clients) UserSAWriteXdgOrPrintRun(ctx context.Context, stdout bool) error {
	/// The main action that we're performing.

	action := func() {
		c.LogInfo("UserSAWriteXdgOrPrintRun(): running...")
		if err := c.UserSAWriteXdgOrPrintFromCM(ctx, nil, stdout); err != nil {
			c.LogErr("UserSAWriteXdgOrPrintRun(): %v", err)
		}
	}

	/// We'll nudge this action from 1) a k8s secret ADDED watcher and
	/// 2) a 5m timer. Only one nudge at a time is needed. This buffered
	/// channel and nudge() helper implement "one nudge at a time".

	ch := make(chan struct{}, 1)
	nudge := func() {
		select {
		case ch <- struct{}{}:
			return
		default:
			return
		}
	}

	/// Main action loop.

	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			case <-ch:
				action()
			}
		}
	}()

	/// Nudge: Timer (5m)

	go func() {
		t := time.NewTicker(5 * time.Minute)
		defer t.Stop()
		for {
			select {
			case <-ctx.Done():
				return
			case <-t.C:
				nudge()
			}
		}
	}()

	/// Nudge: K8s Secret ADDED watcher.
	/// (We let this be the main thread because we want to exit
	//  on watcher errors).

	return c.k8s.WatchSecrets(ctx, func(evType string, _ *k8s.Secret) {
		if evType != "ADDED" {
			return
		}
		nudge()
	})
}

package secrets

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"time"

	"github.com/augmentcode/augment/infra/lib/logger"
	"github.com/augmentcode/augment/research/infra/cfg/clusters"
	"github.com/augmentcode/augment/research/infra/lib/augment"
)

const (
	EngSecretsDir       = "research/infra/cfg/secrets/eng_secrets" // pragma: allowlist secret
	EngSecretsNamespace = "eng-secrets"                            // pragma: allowlist secret
	EngSecretsKey       = "secret"                                 // pragma: allowlist secret
)

// Secret is an Augment eng-secret.
type Secret struct {
	logger.Logger

	SecretName string `json:"secret_name"`
	Creator    string `json:"creator"`
	Modified   string `json:"modified"`

	EncryptedData map[string]map[string]string `json:"encrypted_data"`
}

// Seal creates a new `Secret` with data for each cluster in the `db`.
func Seal(ctx context.Context, db *clusters.Clusters, username, name, val string) (*Secret, error) {
	sec := &Secret{
		Logger:        logger.New(nil),
		SecretName:    name,
		Creator:       username,
		Modified:      time.Now().Format(time.RFC3339),
		EncryptedData: map[string]map[string]string{},
	}
	for _, cluster := range db.List() {
		sec.LogInfo("[%s]: Sealing for cluster %s.", sec.SecretName, cluster.Name)

		k, err := cluster.NewK8s()
		if err != nil {
			return nil, err
		}

		ss, err := k.Seal(ctx, EngSecretsNamespace, name, []byte(val))
		if err != nil {
			return nil, err
		}

		sec.EncryptedData[cluster.Name] = map[string]string{
			EngSecretsKey: string(ss),
		}
	}
	return sec, nil
}

// FromFile reads a `Secret` from a json file.
func FromFile(fname string) (*Secret, error) {
	data, err := os.ReadFile(fname)
	if err != nil {
		return nil, err
	}
	sec := &Secret{}
	if err := json.Unmarshal(data, sec); err != nil {
		return nil, err
	}
	return sec, nil
}

func List() ([]*Secret, error) {
	dir, err := augment.Dir(EngSecretsDir)
	if err != nil {
		return nil, err
	}

	files, err := filepath.Glob(filepath.Join(dir, "*.json"))
	if err != nil {
		return nil, err
	}

	secrets := []*Secret{}
	for _, fname := range files {
		if sec, err := FromFile(fname); err != nil {
			return nil, err
		} else {
			secrets = append(secrets, sec)
		}
	}
	return secrets, nil
}

// String returns a one-line summary of the secret.
func (sec Secret) String() string {
	return fmt.Sprintf("%s created by %s at %s", sec.SecretName, sec.Creator, sec.Modified)
}

// JSON returns a multi-line JSON string.
func (sec Secret) JSON() (string, error) {
	if txt, err := json.MarshalIndent(sec, "", "  "); err != nil {
		return "", err
	} else {
		return string(txt) + "\n", nil
	}
}

// Print prints the `JSON()` string to stdout.
func (sec Secret) Print() {
	if txt, err := sec.JSON(); err != nil {
		sec.LogErr("[%s]: Error printing: %v.", sec.SecretName, err)
	} else {
		fmt.Print(txt)
	}
}

// WriteTo writes the `JSON()` string to the given `fname`, creating parent dirs as needed.
func (sec Secret) WriteTo(fname string, force bool) error {
	txt, err := sec.JSON()
	if err != nil {
		return err
	}

	if st, err := os.Stat(fname); err == nil {
		if !st.Mode().IsRegular() {
			return fmt.Errorf("%s: not a regular file", fname)
		}
		if !force {
			return fmt.Errorf("%s: already exists", fname)
		}
	} else if !errors.Is(err, fs.ErrNotExist) {
		return err
	}

	sec.LogInfo("[%s]: Writing to %s.", sec.SecretName, fname)
	dir := filepath.Dir(fname)
	if err := os.MkdirAll(dir, 0o755); err != nil {
		return err
	}
	if err := os.WriteFile(fname, []byte(txt), 0o644); err != nil {
		return err
	}
	return nil
}

// Write writes the `JSON()` string to its canonical location in the Augment repo under `EngSecretsDir`.
func (sec Secret) Write(force bool) error {
	if fname, err := augment.Dir(EngSecretsDir, sec.SecretName+".json"); err != nil {
		return err
	} else {
		return sec.WriteTo(fname, force)
	}
}

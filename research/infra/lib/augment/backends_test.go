package augment

import (
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
)

func TestClientBasics(t *testing.T) {
	cli := &Clients{}

	if got := cli.K8s(); got != nil {
		t.<PERSON>rrorf("K8s: got %v from empty client.", got)
	}
	cli.GitHub() // no panic

	if got, want := cli.authKeysConfigMap, ""; got != want {
		t.<PERSON>rro<PERSON>("authKeysConfigMap [init]: got %v, want %v.", got, want)
	}
	if got, want := cli.ghUsersConfigMap, ""; got != want {
		t.<PERSON><PERSON><PERSON>("ghUsersConfigMap [init]: got %v, want %v.", got, want)
	}

	cli.SetAuthorizedKeysConfigMap("auth-cm-0")
	cli.SetGitHubUserNameConfigMap("gh-cm-0")

	if got, want := cli.authKeysConfigMap, "auth-cm-0"; got != want { // pragma: allowlist secret
		t.<PERSON><PERSON><PERSON>("authKeysConfigMap [after-set]: got %v, want %v.", got, want)
	}
	if got, want := cli.ghUsersConfigMap, "gh-cm-0"; got != want {
		t.Errorf("ghUsersConfigMap [after-set]: got %v, want %v.", got, want)
	}

	cli.SetAuthorizedKeysConfigMap("")
	cli.SetGitHubUserNameConfigMap("")

	if got, want := cli.authKeysConfigMap, AuthorizedKeysConfigMap; got != want {
		t.Errorf("authKeysConfigMap [defaults]: got %v, want %v.", got, want)
	}
	if got, want := cli.ghUsersConfigMap, GitHubUserNameConfigMap; got != want {
		t.Errorf("ghUsersConfigMap [defaults]: got %v, want %v.", got, want)
	}
}

func TestCommonOpts(t *testing.T) {
	tests := map[string]struct {
		in      []CommonOpt
		want    commonOpts
		wantErr string
	}{
		"defaults": {
			in:   nil,
			want: commonOpts{},
		},
		"all": {
			in: []CommonOpt{
				OptFromBuf("buf0"),
				OptFromFile("file0"),
				OptFromK8s(true),
				OptFromGitHub(true),
				OptGitHubUser("ghuser0"),
				OptAppend(true),
			},
			want: commonOpts{
				fromBuf:    "buf0",
				fromFile:   "file0",
				fromK8s:    true,
				fromGH:     true,
				ghUserName: "ghuser0",
				extend:     true,
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			got, gotErr := mkCommonOpts(tc.in...)
			if diff := cmp.Diff(tc.want, got, cmpopts.EquateComparable(commonOpts{})); diff != "" {
				t.Errorf("-want +got:\n%s", diff)
			}
			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got err %v, want error containing '%v'.", gotErr, tc.wantErr)
			}
		})
	}
}

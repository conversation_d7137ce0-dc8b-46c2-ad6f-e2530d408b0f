package augment

import (
	"errors"
	"fmt"
	"io/fs"
	"os"
	"path"
	"strings"
)

const (
	Augment    = "augment"
	AugmentEnv = "AUGMENT"

	augmentSentinalFile = ".augmentroot"
	augmentFallbackPath = "/home/<USER>/augment"
)

var (
	errRootNotFound = fmt.Errorf("Augment repo root: NOT_FOUND")

	// Swappable for testing.
	tStat  = os.Stat
	tGetwd = os.Getwd
)

// Root returns the path to the Augment repo.
//  1. If the AUGMENT environmental variable is set, use it.
//  2. Work backwards from the current directory looking for one named 'augment' that also contains '.git'.
//  3. Use $HOME/augment if it exists.
//  4. Use /home/<USER>/augment if it exists.
//
// Otherwise, return an error.
func Root() (string, error) {
	exists := func(ps ...string) (bool, error) {
		p := path.Join(ps...)
		if _, err := tStat(p); err == nil {
			return true, nil
		} else if errors.Is(err, fs.ErrNotExist) {
			return false, nil
		} else {
			return false, err
		}
	}

	/// 1. Try the AUGMENT env var.

	if env := os.Getenv(AugmentEnv); env != "" {
		return env, nil
	}

	/// 2. Look for "augment/.git" from the current dir.

	cwd, err := tGetwd()
	if err != nil {
		return "", err
	}
	cwd = strings.TrimSuffix(cwd, "/")
	for dir, file := cwd, ""; dir != "" || file != ""; dir, file = path.Split(dir) {
		dir = strings.TrimSuffix(dir, "/")
		if file != Augment {
			continue
		}
		if ok, err := exists(dir, file, augmentSentinalFile); err != nil {
			return "", err
		} else if ok {
			return path.Join(dir, file), nil
		}
	}

	/// 3. Try $HOME/augment.

	if h := os.Getenv("HOME"); h != "" {
		dir := path.Join(h, Augment)
		if ok, err := exists(dir); err != nil {
			return "", err
		} else if ok {
			return dir, nil
		}
	}

	/// 4. Try /home/<USER>/augment

	if ok, err := exists(augmentFallbackPath); err != nil {
		return "", err
	} else if ok {
		return augmentFallbackPath, nil
	}

	/// Otherwise, error
	return "", errRootNotFound
}

// Dir returns an absolute path to a subdir of the Augment Repo. See `Root()`.
func Dir(subdir ...string) (string, error) {
	root, err := Root()
	if err != nil {
		return "", err
	}
	return path.Join(append([]string{root}, subdir...)...), nil
}

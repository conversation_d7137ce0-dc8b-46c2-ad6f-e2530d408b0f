package augment

import (
	"errors"
	"fmt"
	"io/fs"
	"os"
	"testing"
)

func TestRootAndDir(t *testing.T) {
	testError := fmt.Errorf("test error")

	tests := map[string]struct {
		env   map[string]string
		wd    string
		wdErr error
		stat  map[string]error
		in    []string
		wDir  string
		wErr  error
	}{
		"err-not-found": {
			env: map[string]string{
				"AUGMENT": "",
				"HOME":    "",
			},
			wd:    "/some/dir",
			wdErr: nil,
			stat: map[string]error{
				augmentFallbackPath: fs.ErrNotExist,
			},
			in:   nil,
			wDir: "",
			wErr: errRootNotFound,
		},
		"fallback-found": {
			env: map[string]string{
				"AUGMENT": "",
				"HOME":    "",
			},
			wd:    "/some/dir",
			wdErr: nil,
			stat: map[string]error{
				augmentFallbackPath: nil,
			},
			in:   nil,
			wDir: augmentFallbackPath,
			wErr: nil,
		},
		"fallback-error": {
			env: map[string]string{
				"AUGMENT": "",
				"HOME":    "",
			},
			wd:    "/some/dir",
			wdErr: nil,
			stat: map[string]error{
				augmentFallbackPath: testError,
			},
			in:   nil,
			wDir: "",
			wErr: testError,
		},
		"home-augment-found": {
			env: map[string]string{
				"AUGMENT": "",
				"HOME":    "/home/<USER>",
			},
			wd:    "/some/dir",
			wdErr: nil,
			stat: map[string]error{
				"/home/<USER>/" + Augment: nil,
			},
			in:   nil,
			wDir: "/home/<USER>/" + Augment,
			wErr: nil,
		},
		"home-augment-error": {
			env: map[string]string{
				"AUGMENT": "",
				"HOME":    "/home/<USER>",
			},
			wd:    "/some/dir",
			wdErr: nil,
			stat: map[string]error{
				"/home/<USER>/" + Augment: testError,
			},
			in:   nil,
			wDir: "",
			wErr: testError,
		},
		"home-augment-notfound": {
			env: map[string]string{
				"AUGMENT": "",
				"HOME":    "/home/<USER>",
			},
			wd:    "/some/dir",
			wdErr: nil,
			stat: map[string]error{
				"/home/<USER>/" + Augment: fs.ErrNotExist,
				augmentFallbackPath:     fs.ErrNotExist,
			},
			in:   nil,
			wDir: "",
			wErr: errRootNotFound,
		},

		"getwd-error": {
			env: map[string]string{
				"AUGMENT": "",
				"HOME":    "",
			},
			wd:    "/some/dir",
			wdErr: testError,
			stat:  map[string]error{},
			in:    nil,
			wDir:  "",
			wErr:  testError,
		},
		"env-var": {
			env: map[string]string{
				"AUGMENT": "_env/var_",
				"HOME":    "",
			},
			wd:    "/some/dir",
			wdErr: nil,
			stat:  map[string]error{},
			in:    nil,
			wDir:  "_env/var_",
			wErr:  nil,
		},
		"env-var-case": {
			env: map[string]string{
				"augment": "_env/var_",
				"HOME":    "",
			},
			wd:    "/some/dir",
			wdErr: nil,
			stat: map[string]error{
				augmentFallbackPath: fs.ErrNotExist,
			},
			in:   nil,
			wDir: "",
			wErr: errRootNotFound,
		},

		"wd-exists-error": {
			wd: "/some/dir/augment",
			stat: map[string]error{
				"/some/dir/augment/.augmentroot": testError,
			},
			wDir: "",
			wErr: testError,
		},
		"wd-match": {
			wd: "/some/dir/augment",
			stat: map[string]error{
				"/some/dir/augment/.augmentroot": nil,
			},
			wDir: "/some/dir/augment",
		},
		"wd-match-trailing-slash": {
			wd: "/some/dir/augment/",
			stat: map[string]error{
				"/some/dir/augment/.augmentroot": nil,
			},
			wDir: "/some/dir/augment",
		},
		"wd-match-deep": {
			wd: "/some/dir/augment/more/dirs",
			stat: map[string]error{
				"/some/dir/augment/.augmentroot": nil,
			},
			wDir: "/some/dir/augment",
		},
		"wd-match-outer": {
			wd: "/some/dir/augment/another/augment",
			stat: map[string]error{
				"/some/dir/augment/another/augment/.augmentroot": nil,
			},
			wDir: "/some/dir/augment/another/augment",
		},
		"wd-match-inner": {
			wd: "/some/dir/augment/another/augment",
			stat: map[string]error{
				"/some/dir/augment/another/augment/.augmentroot": fs.ErrNotExist,
				"/some/dir/augment/.augmentroot":                 nil,
			},
			wDir: "/some/dir/augment",
		},
		"wd-match-just-augment": {
			wd: "augment",
			stat: map[string]error{
				"augment/.augmentroot": nil,
			},
			wDir: "augment",
		},

		"dir-empty-string": {
			env: map[string]string{
				"AUGMENT": "/augment",
			},
			in:   []string{""},
			wDir: "/augment",
		},
		"dir-multiple-empty-string": {
			env: map[string]string{
				"AUGMENT": "/augment",
			},
			in:   []string{"", "", ""},
			wDir: "/augment",
		},
		"dir-starts-slash": {
			env: map[string]string{
				"AUGMENT": "/augment",
			},
			in:   []string{"/foo"},
			wDir: "/augment/foo",
		},
		"dir-ends-slash": {
			env: map[string]string{
				"AUGMENT": "/augment",
			},
			in:   []string{"/foo/"},
			wDir: "/augment/foo",
		},
		"dir-just-slash": {
			env: map[string]string{
				"AUGMENT": "/augment",
			},
			in:   []string{"/"},
			wDir: "/augment",
		},
		"dir-mixedlash": {
			env: map[string]string{
				"AUGMENT": "/augment",
			},
			in:   []string{"/", "foo", "/bar/", "", "/", "", "baz"},
			wDir: "/augment/foo/bar/baz",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			// NOTE: Cannot be parallel because of tStat, tGetwd global vars.

			for k, v := range tc.env {
				t.Setenv(k, v)
			}

			oStat := tStat
			t.Cleanup(func() { tStat = oStat })
			tStat = func(name string) (os.FileInfo, error) {
				err, ok := tc.stat[name]
				if !ok {
					err = fmt.Errorf("unhandled test stat error: %v", name)
				}
				return nil, err
			}

			oGetwd := tGetwd
			t.Cleanup(func() { tGetwd = oGetwd })
			tGetwd = func() (string, error) {
				return tc.wd, tc.wdErr
			}

			gotDir, gotErr := Dir(tc.in...)

			if got, want := gotDir, tc.wDir; got != want {
				t.Errorf("got %v, want %v.", got, want)
			}
			if got, want := gotErr, tc.wErr; !errors.Is(got, want) {
				t.Errorf("error: got %v, want %v.", got, want)
			}
		})
	}
}

package devpod

import (
	"errors"
	"fmt"
	"net"
	"reflect"
	"slices"
	"sort"
	"strings"

	"github.com/thediveo/go-asciitree"
	corev1 "k8s.io/api/core/v1"

	"github.com/augmentcode/augment/infra/lib/distribution"
	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/infra/lib/ssh"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod/crd/devpodv1"
)

// A Set defines all of the k8s objects that make up a DevPod. A set can come
// from three sources: 1) the DevPod Builder, when creating a new DevPod;
// 2) the DevPod Manager, when retrieving a live DevPod; or 3) a List call with
// a reduced set of data.
type Set struct {
	bld *Builder

	// Spec (Owner)
	spec *devpodv1.DevPod

	// Everything Else
	cm         *k8s.ConfigMap
	ingressSvc *k8s.Service // may be nil
	service    *k8s.Service
	deployment *k8s.Deployment
	pod        *k8s.Pod // may be nil
	sshHKSec   *k8s.Secret
	homePVC    *k8s.PVC // may be nil

	// As ApplyConfig
	cmAC         *k8s.ConfigMapApplyConfig
	ingressSvcAC *k8s.ServiceApplyConfig
	serviceAC    *k8s.ServiceApplyConfig
	deploymentAC *k8s.DeploymentApplyConfig
	sshHKSecAC   *k8s.SecretApplyConfig
	homePVCAC    *k8s.PVCApplyConfig

	// Misc Info
	dnsIPs  []net.IP
	augi    string
	augiErr error
}

func (s Set) User() string {
	return s.bld.User()
}

func (s Set) Name() string {
	return s.bld.Name()
}

func (s Set) Namespace() string {
	if s.spec != nil {
		return s.spec.Namespace()
	}
	if s.deployment != nil {
		return s.deployment.Namespace()
	}
	return "<unknown>"
}

func (s Set) Spec() *devpodv1.DevPod {
	return s.spec
}

func (s Set) NameList() []string {
	names := []string{}
	if o := s.cm; o != nil {
		names = append(names, o.ShortName())
	}
	if o := s.service; o != nil {
		names = append(names, o.ShortName())
	}
	if o := s.deployment; o != nil {
		names = append(names, o.ShortName())
	}
	if o := s.pod; o != nil {
		names = append(names, o.ShortName())
	}
	if o := s.homePVC; o != nil {
		names = append(names, o.ShortName())
	}
	if o := s.sshHKSec; o != nil {
		names = append(names, o.ShortName())
	}
	if o := s.ingressSvc; o != nil {
		names = append(names, o.ShortName())
	}
	return names
}

func (s Set) SetOwners(spec *devpodv1.DevPod) {
	if ac := s.cmAC; ac != nil {
		ac.WithOwner(&spec.Object)
	}
	if ac := s.ingressSvcAC; ac != nil {
		ac.WithOwner(&spec.Object)
	}
	if ac := s.serviceAC; ac != nil {
		ac.WithOwner(&spec.Object)
	}
	if ac := s.deploymentAC; ac != nil {
		ac.WithOwner(&spec.Object)
	}
	if ac := s.sshHKSecAC; ac != nil {
		ac.WithOwner(&spec.Object)
	}
	if ac := s.homePVCAC; ac != nil {
		// NOTE(mattm): Do *NOT* set home pvc ownership. Home PVC has a separate lifecycle.
	}
}

func (s Set) PodSpec() *corev1.PodSpec {
	if d := s.deployment; d != nil {
		return &d.Raw().Spec.Template.Spec
	} else if p := s.pod; p != nil {
		return &p.Raw().Spec
	} else {
		return nil
	}
}

func (s Set) Container() *corev1.Container {
	if p := s.PodSpec(); p == nil {
		return nil
	} else if len(p.Containers) == 0 {
		return nil
	} else {
		// We can have sidecar-but-not-sidecar container. Try to find the "c1-dev" container
		// first and if not then fallback to the first container.
		for i := range p.Containers {
			if p.Containers[i].Name == containerName {
				return &p.Containers[i]
			}
		}
		return &p.Containers[0]
	}
}

func (s Set) Image() string {
	if c := s.Container(); c == nil {
		return ""
	} else {
		return c.Image
	}
}

func (s Set) ResourceList() corev1.ResourceList {
	if c := s.Container(); c == nil {
		return nil
	} else {
		return c.Resources.Limits
	}
}

func (s Set) Resource(r corev1.ResourceName) string {
	if q, ok := s.ResourceList()[r]; !ok {
		return ""
	} else {
		return q.String()
	}
}

func (s Set) GPUs() string {
	return s.Resource("nvidia.com/gpu")
}

func (s Set) CPUs() string {
	return s.Resource(corev1.ResourceCPU)
}

func (s Set) RAM() string {
	if q, ok := s.ResourceList()[corev1.ResourceMemory]; !ok {
		return ""
	} else {
		gb := float64(q.Value()) / (1000 * 1000 * 1000)
		return fmt.Sprintf("%.2fG", gb)
	}
}

func (s Set) HomePVCName() string {
	// First find a mount, in priority order:
	//  1. /home/<USER>
	//  2. /home/<USER>
	//  3. /home
	//
	// Then find matching volume.

	mnt := func() *corev1.VolumeMount {
		if c := s.Container(); c != nil {
			for _, vm := range c.VolumeMounts {
				if vm.MountPath == "/home/"+s.User() {
					return &vm
				}
			}
			for _, vm := range c.VolumeMounts {
				if vm.MountPath == "/home/<USER>" {
					return &vm
				}
			}
			for _, vm := range c.VolumeMounts {
				if vm.MountPath == "/home" {
					return &vm
				}
			}
		}
		return nil
	}()
	if mnt == nil {
		return ""
	}

	if pod := s.PodSpec(); pod != nil {
		for _, v := range pod.Volumes {
			if v.Name == mnt.Name {
				if pvc := v.VolumeSource.PersistentVolumeClaim; pvc != nil {
					return pvc.ClaimName
				}
				break
			}
		}
	}

	return ""
}

func (s Set) NodeSelectors() []corev1.NodeSelectorRequirement {
	if p := s.PodSpec(); p == nil {
		return nil
	} else if a := p.Affinity; a == nil {
		return nil
	} else if na := a.NodeAffinity; na == nil {
		return nil
	} else if ns := na.RequiredDuringSchedulingIgnoredDuringExecution; ns == nil {
		return nil
	} else {
		ret := []corev1.NodeSelectorRequirement{}
		for _, term := range ns.NodeSelectorTerms {
			ret = append(ret, term.MatchExpressions...)
		}
		return ret
	}
}

func (s Set) GPUType() string {
	if t := s.bld.GPUType(); t != "" {
		return t
	}
	for _, req := range s.NodeSelectors() {
		if req.Key == "gpu.nvidia.com/class" && len(req.Values) > 0 {
			return req.Values[0]
		}
		if req.Key == "cloud.google.com/gke-accelerator" && len(req.Values) > 0 {
			return req.Values[0]
		}
	}
	return ""
}

func (s Set) IPAddr() string {
	if svc := s.service; svc == nil {
		// NOTE(mattm): We could try to fall back to a Pod IP here, but
		// we probably don't want to encourage using those.
		return ""
	} else if ip := svc.PublicIP(); ip != "" {
		return ip
	} else {
		return svc.ClusterIP()
	}
}

func (s Set) ExposedURLs() []string {
	urls := []string{}
	if svc := s.ingressSvc; svc != nil {
		for _, port := range svc.Raw().Spec.Ports {
			urls = append(urls, s.bld.ExposedURL(port.Port))
		}
	}
	return urls
}

func (s Set) Dotfiles() string {
	return s.cm.Raw().Data["dotfiles.tgz.b64"]
}

type treeItem struct {
	Label    string      `asciitree:"label"`
	Children []*treeItem `asciitree:"children"`
}

func (ti *treeItem) addChild(fmtstr string, a ...any) *treeItem {
	child := &treeItem{
		Label: fmt.Sprintf(fmtstr, a...),
	}
	ti.Children = append(ti.Children, child)
	return child
}

func (ti *treeItem) addChildAlign(width int, label string, fmtstr string, a ...any) *treeItem {
	pfx := fmt.Sprintf("%-*s", width+1, label+":")
	return ti.addChild(pfx+fmtstr, a...)
}

func (ti *treeItem) addTree(child *treeItem) {
	ti.Children = append(ti.Children, child)
}

func (ti treeItem) String() string {
	return asciitree.RenderFancy(ti)
}

func (s Set) Status() string {
	return s.statusTree().String()
}

func (s Set) statusTree() *treeItem {
	/// Meta and DevPod-level info.

	tree := &treeItem{
		Label: fmt.Sprintf("⬤ DevPod/%s", s.Name()),
	}
	tree.addChild("cluster:   %s", s.bld.Cluster().Name)
	tree.addChild("namespace: %s", s.Namespace())
	tree.addChild("user:      %s", s.User())

	resources := fmt.Sprintf("%s CPU(s) / %s RAM", s.CPUs(), s.RAM())
	if g := s.GPUs(); g != "" {
		resources = fmt.Sprintf("%s %s GPU(s) / %s", g, s.GPUType(), resources)
	}
	tree.addChild("reqs:    %s", resources)

	if tree := tree.addChild("image"); true {
		reg, name, version := distribution.ParseImage(s.Image())
		if reg != "" {
			tree.addChild("registry: %s", reg)
		}
		if name != "" {
			tree.addChild("name:     %s", name)
		}
		if version != "" {
			tree.addChild("version:  %s", version)
		}
	}

	if s.augi != "" || s.augiErr != nil {
		if s.augiErr != nil {
			tree.addChild("augi: %s [%v]", s.augi, s.augiErr)
		} else {
			tree.addChild("augi: %s", s.augi)
		}
	}

	/// Deployment

	if d := s.deployment; d == nil {
		tree.addChild("deploy/%s [does not exist]", s.bld.DeploymentName())
	} else {
		tree := tree.addChild("deploy/%s", d.Name())
		tree.addChild("status:    %s", d.Status().String())
		tree.addChild("condition: %s", d.Status().LatestCondition().String())
	}

	/// Pod

	if p := s.pod; p == nil {
		tree.addChild("pod/%s [Powered Off (`augi devpod poweronn %s` to boot)]", s.bld.Name(), s.bld.Name())
	} else {
		tree := tree.addChild("pod/%s", p.Name())
		tree.addChild("status:    %s", p.Status().String())
		tree.addChild("condition: %s", p.Status().LatestCondition().String())
	}

	/// Service and SSH

	if svc := s.service; svc == nil {
		tree.addChild("service/%s [does not exist?!]", s.bld.ServiceName())
	} else {
		tree := tree.addChild("service/%s", svc.Name())

		width := 12
		sshName := "<unset>"

		tree.addChildAlign(width, "svc type", string(svc.Type()))

		if ip := svc.ClusterIP(); ip != "" {
			tree.addChildAlign(width, "cluster ip", ip)
			sshName = ip
		} else {
			tree.addChildAlign(width, "cluster ip", "<none yet>")
		}

		if ip := svc.PublicIP(); ip != "" {
			tree.addChildAlign(width, "public ip", ip)
			sshName = ip
		} else if svc.Type() == corev1.ServiceTypeLoadBalancer {
			tree.addChildAlign(width, "public ip", "[not yet allocated]")
		}

		tcpPorts := []string{}
		udpPorts := []string{}
		for _, port := range svc.Raw().Spec.Ports {
			str := ""
			name := port.Name
			if name != "" {
				name = "(" + name + ")"
			}
			if ti := port.TargetPort.IntVal; ti > 0 && port.Port != ti {
				str = fmt.Sprintf("%d:%d%s", port.Port, ti, name)
			} else if ts := port.TargetPort.StrVal; ts != "" {
				str = fmt.Sprintf("%d:%s%s", port.Port, ts, name)
			} else {
				str = fmt.Sprintf("%d%s", port.Port, name)
			}
			if port.Protocol == corev1.ProtocolUDP {
				udpPorts = append(udpPorts, str)
			} else {
				tcpPorts = append(tcpPorts, str)
			}
		}
		if len(tcpPorts) > 0 {
			tree.addChildAlign(width, "tcp port(s)", strings.Join(tcpPorts, " "))
		}
		if len(udpPorts) > 0 {
			tree.addChildAlign(width, "udp port(s)", strings.Join(udpPorts, " "))
		}

		if hn := svc.ExternalDNSHostname(); hn != "" {
			warn := ""
			if len(s.dnsIPs) == 0 {
				warn = fmt.Sprintf(" [No DNS Response, Hostname NotYetAvailable]")
			} else {
				sshName = hn
			}
			tree.addChildAlign(width, "hostname", "%s%s", hn, warn)
		}
		tree.addChild("SSH").addChild("ssh -p%d augment@%s", s.bld.SSHNonStdPort(), sshName)
	}

	/// Expose Service (and deprecated Ingress)
	if svc := s.ingressSvc; svc == nil {
		tree.addChild("svc/%s: [none, `augi devpod expose` to add]", s.bld.IngressServiceName())
	} else {
		tree := tree.addChild("%s", svc.ShortName())
		if ip := svc.ClusterIP(); ip != "" {
			tree.addChild("cluster ip: %s", ip)
		}
		if ip := svc.PublicIP(); ip != "" {
			tree.addChild("public ip:  %s [WARN this service should be internal only!]", ip)
		}

		// NOTE(mattm): Collect these first in case we decide that we need to show at most e.g., 5 and then
		// summarize the rest.
		urls := s.ExposedURLs()
		urltree := tree.addChild("urls")
		for _, url := range urls {
			urltree.addChild("%s", url)
		}
	}

	/// HomeDir PVC

	if homedir := s.homePVC; homedir == nil {
		tree.addChild("homedir: EPHEMERAL [changes will be lost on restart]")
	} else {
		tree := tree.addChild("homedir: pvc/%s", homedir.Name())
		tree.addChild("status:   %s", homedir.Phase())
		tree.addChild("class:    %s (%s)", homedir.Class(), homedir.AccessModesString())
		if actual, requested := homedir.StatusCapacity(), homedir.Capacity(); actual == requested {
			tree.addChild("capacity: %s", actual)
		} else {
			tree.addChild("capacity: %s [WARNING actual differs from %s requested]", actual, requested)
		}
	}

	return tree
}

func (s *Set) YAML() (string, error) {
	if s == nil {
		return "", nil
	}

	// Snip bulk data because it's too much noise.
	cm := k8s.NewConfigMap(s.cm.Raw().DeepCopy())
	cm.Raw().Data["dotfiles.tgz.b64"] = fmt.Sprintf("<snip (%d bytes)>", len(cm.Raw().Data["dotfiles.tgz.b64"]))
	cm.Raw().Data["init.sh"] = fmt.Sprintf("<snip (%d bytes)>", len(cm.Raw().Data["init.sh"]))

	// Snip SSH Host *Private* Keys because they're, you know, private.
	if sec := s.sshHKSec; sec != nil {
		for key := range sec.Raw().Data {
			if !ssh.IsPublicFile(key) {
				sec.Raw().Data[key] = []byte("<REDACTED>")
			}
		}
	}

	ymls := []string{}
	errs := []error{}
	type hasYAML interface {
		YAML() (string, error)
	}
	app := func(obj hasYAML) {
		if obj == nil || reflect.ValueOf(obj).IsNil() {
			return
		} else if yml, err := obj.YAML(); err != nil {
			errs = append(errs, err)
		} else {
			ymls = append(ymls, yml)
		}
	}

	app(s.spec)
	app(cm)
	app(s.service)
	app(s.sshHKSec)
	app(s.homePVC)
	app(s.ingressSvc)
	app(s.deployment)
	// app(s.pod)

	if err := errors.Join(errs...); err != nil {
		return "", err
	}

	return strings.Join(ymls, "---\n") + "\n", nil
}

func (s *Set) ApplyConfigYAML() (string, error) {
	if s == nil {
		return "", nil
	}

	// Snip bulk data because it's too much noise.
	if cm := s.cmAC; cm != nil {
		cm.WithData(map[string]string{
			"dotfiles.tgz.b64": fmt.Sprintf("<snip (%d bytes)>", len(cm.ConfigMapApplyConfiguration.Data["dotfiles.tgz.b64"])),
			"init.sh":          fmt.Sprintf("<snip (%d bytes)>", len(cm.ConfigMapApplyConfiguration.Data["init.sh"])),
		})
	}

	// Snip SSH Host *Private* Keys because they're, you know, private.
	if sec := s.sshHKSecAC; sec != nil {
		for key := range sec.SecretApplyConfiguration.Data {
			if !ssh.IsPublicFile(key) {
				sec.WithData(map[string][]byte{
					key: []byte("<REDACTED>"),
				})
			}
		}
	}

	ymls := []string{}
	errs := []error{}
	type hasYAML interface {
		YAML() (string, error)
	}
	app := func(obj hasYAML) {
		if obj == nil || reflect.ValueOf(obj).IsNil() {
			return
		} else if yml, err := obj.YAML(); err != nil {
			errs = append(errs, err)
		} else {
			ymls = append(ymls, yml)
		}
	}

	app(s.spec)
	app(s.cmAC)
	app(s.homePVCAC)
	app(s.sshHKSecAC)
	app(s.serviceAC)
	app(s.ingressSvcAC)
	app(s.deploymentAC)
	// app(s.pod)

	if err := errors.Join(errs...); err != nil {
		return "", err
	}

	return strings.Join(ymls, "---\n") + "\n", nil
}

func (s Set) ListHeader(augiPath bool) []string {
	ret := []string{
		"User",
		"Cluster",
		"Namespace",
		"DevPod (Deployment or Pod)",
		"Current Pod",
		"IP Address",
		"GPUs",
		"GPU Type",
		"CPUs",
		"RAM",
	}
	if augiPath {
		ret = append(ret, "Augi")
	}
	return ret
}

func (s Set) ListRow(augiPath bool) []string {
	name := s.Name()
	podname := ""

	if p := s.pod; p != nil {
		name = "pod/" + p.Name()
		podname = name
	}
	if d := s.deployment; d != nil {
		name = "deploy/" + d.Name()
	}

	row := []string{
		s.User(),
		strings.ToLower(s.bld.Cluster().Name),
		s.Namespace(),
		name,
		podname,
		s.IPAddr(),
		s.GPUs(),
		s.GPUType(),
		s.CPUs(),
		s.RAM(),
	}

	if augiPath {
		v := s.augi
		if s.augiErr != nil {
			if v != "" {
				v += " "
			}
			v += "[" + strings.TrimSpace(s.augiErr.Error()) + "]"
		}
		row = append(row, v)
	}

	// Replace blank columns with '-'.
	for c := range row {
		if row[c] == "" {
			row[c] = "-"
		}
	}
	return row
}

func (s Set) Table(pfx string, augiPath bool) string {
	return Sets{&s}.Table(pfx, augiPath)
}

////////////////////////////////////////////////////////////////////////////////
//
// Sets - helpers for list of sets (e.g., tableulate)
//

type Sets []*Set

func (ss Sets) Sort() {
	sort.Slice(ss, func(i, j int) bool {
		return ss[i].Name() < ss[j].Name()
	})
}

func (ss Sets) SortWithAugi() {
	sort.Slice(ss, func(i, j int) bool {
		if ss[i].augi != ss[j].augi {
			return ss[i].augi < ss[j].augi
		}
		return ss[i].Name() < ss[j].Name()
	})
}

func (ss Sets) Table(pfx string, augiPath bool) string {
	if augiPath {
		ss.SortWithAugi()
	}

	// Accumulate rows from Header + each DevPod
	rows := [][]string{
		Set{}.ListHeader(augiPath),
	}
	for _, set := range ss {
		rows = append(rows, set.ListRow(augiPath))
	}

	// Get max width of each column.
	widths := map[int]int{}
	for _, row := range rows {
		for c, col := range row {
			if w := len(col); w > widths[c] {
				widths[c] = w
			}
		}
	}

	// Add a line separate between header and data.
	line := make([]string, len(widths))
	for c, w := range widths {
		line[c] = strings.Repeat("-", w)
	}
	rows = slices.Insert(rows, 1, line)

	// Format each row into a buffer.
	buf := &strings.Builder{}
	for _, row := range rows {
		fmt.Fprint(buf, pfx)
		for c, col := range row {
			fmtstr := "%-*s"
			if c > 0 {
				fmtstr = " " + fmtstr
			}
			fmt.Fprintf(buf, fmtstr, widths[c], col)
		}
		fmt.Fprintf(buf, "\n")
	}

	// Finally, return a multi-line string.
	return buf.String()
}

func (ss Sets) Status() string {
	return ss.statusTree().String()
}

func (ss Sets) statusTree() *treeItem {
	users := []string{}
	byuser := map[string]Sets{}

	for _, set := range ss {
		u := set.User()
		if u == "" {
			u = "<unknown>"
		}
		if _, seen := byuser[u]; !seen {
			users = append(users, u)
		}
		byuser[u] = append(byuser[u], set)
	}
	sort.Strings(users)

	tree := &treeItem{
		Label: "DevPods by User",
	}
	for _, user := range users {
		tree := tree.addChild("users/%s", user)
		for _, set := range byuser[user] {
			tree.addTree(set.statusTree())
		}
	}

	return tree
}

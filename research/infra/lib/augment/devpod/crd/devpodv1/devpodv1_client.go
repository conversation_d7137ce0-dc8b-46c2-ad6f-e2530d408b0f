package devpodv1

import (
	"context"

	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/watch"
	"k8s.io/client-go/dynamic"

	"github.com/augmentcode/augment/infra/lib/k8s"
)

func WrappedClient(k *k8s.Client) *Client {
	return &Client{Client: k}
}

type Client struct {
	*k8s.Client
}

func (c Client) devpods() dynamic.ResourceInterface {
	return c.Dynamic(APIGroup, APIVersion, DevPodResource)
}

func (c Client) ListDevPods(ctx context.Context, opts ...k8s.ListOpt) ([]*DevPod, error) {
	ul, err := c.devpods().List(ctx, k8s.ListOptions(opts...))
	if err != nil {
		return nil, err
	}
	devpods := []*DevPod{}
	for _, u := range ul.Items {
		if devpod, err := NewDevPodFromUnstructured(&u); err != nil {
			return nil, err
		} else {
			devpods = append(devpods, devpod)
		}
	}
	return devpods, nil
}

func (c Client) GetDevPod(ctx context.Context, name string, opts ...k8s.GetOpt) (*DevPod, error) {
	u, err := c.devpods().Get(ctx, name, k8s.GetOptions(opts...))
	if err != nil {
		return nil, err
	}
	devpod, err := NewDevPodFromUnstructured(u)
	if err != nil {
		return nil, err
	}
	return devpod, nil
}

func (c Client) WatchDevPods(ctx context.Context, f func(string, *DevPod), opts ...k8s.ListOpt) error {
	return c.Watch(ctx, "DevPods", c.devpods().Watch, opts, func(ev watch.Event) (k8s.Object, bool) {
		if u, ok := ev.Object.(*unstructured.Unstructured); !ok {
			return k8s.Object{}, false
		} else if devpod, err := NewDevPodFromUnstructured(u); err != nil {
			c.LogWarn("Failed to convert DevPod from Unstructured: %v.", err)
			return k8s.Object{}, false
		} else {
			f(string(ev.Type), devpod)
			return devpod.Object, true
		}
	})
}

func (c Client) CreateDevPod(ctx context.Context, devpod *DevPod) (*DevPod, error) {
	u, err := devpod.Unstructured()
	if err != nil {
		return nil, err
	}
	u2, err := c.devpods().Create(ctx, u, k8s.CreateOptions())
	if err != nil {
		return nil, err
	}
	return NewDevPodFromUnstructured(u2)
}

func (c Client) UpdateDevPod(ctx context.Context, devpod *DevPod) (*DevPod, error) {
	u, err := devpod.Unstructured()
	if err != nil {
		return nil, err
	}
	u2, err := c.devpods().Update(ctx, u, k8s.UpdateOptions())
	if err != nil {
		return nil, err
	}
	return NewDevPodFromUnstructured(u2)
}

func (c Client) ReplaceDevPod(ctx context.Context, obj *DevPod) (*DevPod, error) {
	if obj.HasUID() {
		return c.UpdateDevPod(ctx, obj)
	} else {
		return c.CreateDevPod(ctx, obj)
	}
}

func (c Client) DeleteDevPod(ctx context.Context, name string) error {
	c.LogInfo("Deleting DevPod %s...", name)
	return c.devpods().Delete(ctx, name, k8s.DeleteOptions())
}

func (c Client) DeleteIfDevPod(ctx context.Context, name string) error {
	if _, err := c.GetDevPod(ctx, name); err == nil {
		return k8s.NotFoundOK(c.DeleteDevPod(ctx, name))
	} else if k8s.IsNotFound(err) {
		c.LogInfo("Not Deleting DevPod: %s: already NotFound.", name)
		return nil
	} else {
		return err
	}
}

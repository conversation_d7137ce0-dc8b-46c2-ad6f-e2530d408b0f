load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library", "jsonnet_to_json")

package(default_visibility = ["//research/infra:internal"])

jsonnet_library(
    name = "devpodv1_jsonnet",
    srcs = ["devpodv1.jsonnet"],
    deps = [
        "//research/infra/cfg/clusters:clusters_jsonnet",
    ],
)

jsonnet_to_json(
    name = "devpodv1_json",
    src = "devpodv1.jsonnet",
    outs = ["devpodv1.json"],
    visibility = ["//visibility:private"],
    deps = [":devpodv1_jsonnet"],
)

go_library(
    name = "devpodv1",
    srcs = [
        "devpodv1.go",
        "devpodv1_client.go",
        "devpodv1_deepcopy.go",
        "devpodv1_status.go",
    ],
    importpath = "github.com/augmentcode/augment/research/infra/lib/augment/devpod/crd/devpodv1",
    deps = [
        "//infra/lib/k8s",
        "//research/infra/lib/augment/devpod/spec",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_apimachinery//pkg/apis/meta/v1/unstructured",
        "@io_k8s_apimachinery//pkg/runtime",
        "@io_k8s_apimachinery//pkg/watch",
        "@io_k8s_client_go//dynamic",
    ],
)

go_test(
    name = "devpodv1_test",
    srcs = ["devpodv1_test.go"],
    embed = [":devpodv1"],
    deps = [
        "//research/infra/lib/augment/devpod/spec",
        "@com_github_google_go_cmp//cmp",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
    ],
)

package devpodv1

import (
	"errors"
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"

	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod/spec"
)

const (
	APIGroup        = "r.augmentcode.com"
	APIVersion      = "v1"
	APIGroupVersion = APIGroup + "/" + APIVersion
	DevPodResource  = "devpods"
	DevPodKind      = "DevPod"
)

var (
	ErrMetadataNameMismatch    = errors.New("metadata.name != spec.devpod_name")
	ErrDevPodNameLabelMismatch = errors.New("metadata.labels[aug.devpod.name] != spec.devpod_name")
	ErrUserNameLabelMismatch   = errors.New("metadata.labels[aug.user] != spec.user_name")
)

type devPodObject struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   spec.DevPod  `json:"spec,omitempty"`
	Status DevPodStatus `json:"status,omitempty"`
}

type DevPod struct {
	k8s.Object
	raw *devPodObject
}

func NewDevPod(raw *devPodObject) *DevPod {
	if raw == nil {
		raw = &devPodObject{}
	}
	if raw.TypeMeta.APIVersion == "" {
		raw.TypeMeta.APIVersion = APIGroupVersion
	}
	if raw.TypeMeta.Kind == "" {
		raw.TypeMeta.Kind = DevPodKind
	}
	return &DevPod{
		Object: k8s.NewObject(raw),
		raw:    raw,
	}
}

func NewDevPodFromUnstructured(u *unstructured.Unstructured) (*DevPod, error) {
	devpod := NewDevPod(nil)
	if err := devpod.FromUnstructured(u); err != nil {
		return nil, err
	}
	return devpod, nil
}

func NewDevPodFromAdmissionReview(ar *k8s.AdmissionReview) (*DevPod, error) {
	if !ar.IsGVK(APIGroup, APIVersion, DevPodKind) {
		return nil, fmt.Errorf("AdmissionReview not for DevPod: %+v", ar.GVK())
	}
	req := ar.Request()
	if req == nil {
		return nil, fmt.Errorf("AdmissionReview has no request")
	}

	devpod := NewDevPod(nil)
	if err := devpod.FromJSON(string(req.Object.Raw)); err != nil {
		return nil, err
	}
	return devpod, nil
}

func NewDevPodFromSpec(s spec.DevPod, namespace string) *DevPod {
	return NewDevPod(&devPodObject{
		ObjectMeta: metav1.ObjectMeta{
			Name:      s.DevPodName,
			Namespace: namespace,
			Labels: map[string]string{
				"aug.devpod.name": s.DevPodName,
				"aug.user":        s.UserName,
			},
		},
		Spec: *s.DeepCopy(),
	})
}

func NewDevPodFromNames(cluster, user, devpod, namespace string) *DevPod {
	s := spec.DevPod{
		ClusterName: cluster,
		UserName:    user,
		DevPodName:  devpod,
	}
	return NewDevPodFromSpec(s, namespace)
}

func (o *DevPod) Raw() *devPodObject {
	return o.raw
}

func (o DevPod) DevPod() *spec.DevPod {
	if o.raw == nil {
		return nil
	}
	return &o.raw.Spec
}

func (o DevPod) Status() *DevPodStatus {
	if o.raw == nil {
		return nil
	}
	return &o.raw.Status
}

// Validate directly validates at the k8s level, and also calls the DevPod
// spec's `Validations()` method. NOTE: Some of these validations are also
// defined in the raw CRD definition.
func (o DevPod) Validate() error {
	if o.DevPod() == nil {
		return fmt.Errorf("cannot validate a nil DevPod")
	}
	errs := []error{}
	err := func(f string, a ...any) {
		errs = append(errs, fmt.Errorf(f, a...))
	}

	if mn, sn := o.Name(), o.DevPod().DevPodName; mn != sn {
		err("%w (%s != %s)", ErrMetadataNameMismatch, mn, sn)
	}

	if got, want := o.Label("aug.devpod.name"), o.DevPod().DevPodName; got != want {
		err("%w (%s != %s)", ErrDevPodNameLabelMismatch, got, want)
	}

	if got, want := o.Label("aug.user"), o.DevPod().UserName; got != want {
		err("%w (%s != %s)", ErrUserNameLabelMismatch, got, want)
	}

	errs = append(errs, o.DevPod().Validations()...)
	return errors.Join(errs...)
}

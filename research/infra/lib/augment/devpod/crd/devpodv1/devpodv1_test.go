package devpodv1

import (
	"errors"
	"testing"

	"github.com/google/go-cmp/cmp"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/augmentcode/augment/research/infra/lib/augment/devpod/spec"
)

func TestDevPod(t *testing.T) {
	t.Parallel()

	dp := NewDevPod(nil)
	dp.raw.ObjectMeta.Name = "u1-dp1"
	dp.DevPod().ClusterName = "c1"
	dp.DevPod().UserName = "u1"
	dp.DevPod().DevPodName = "u1-dp1"

	got := dp
	got.raw = dp.raw.DeepCopy()

	wantTxt := `{
		"apiVersion": "r.augmentcode.com/v1",
		"kind": "DevPod",
		"metadata": {
	  	  "name": "u1-dp1"
		},
		"spec": {
	  	  "cluster_name": "c1",
	  	  "devpod_name": "u1-dp1",
	  	  "user_name": "u1"
		}
	}`
	want := NewDevPod(nil)
	if err := want.FromJSON(wantTxt); err != nil {
		t.Fatal(err)
	}

	if wanty, err := want.YAML(); err != nil {
		t.Fatal(err)
	} else if goty, err := got.YAML(); err != nil {
		t.Fatal(err)
	} else if diff := cmp.Diff(wanty, goty); diff != "" {
		t.Errorf("DevPod: -want +got:\n%s", diff)
	}
}

func TestValidate(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		spec *DevPod
		want []error
		not  []error
	}{
		"empty": {
			spec: NewDevPod(nil),
			want: []error{spec.ErrUserNameEmpty, spec.ErrNoUserNamePrefix, spec.ErrNoSuffix},
			not:  []error{ErrMetadataNameMismatch, ErrDevPodNameLabelMismatch, ErrUserNameLabelMismatch},
		},
		"okay": {
			spec: NewDevPod(&devPodObject{
				ObjectMeta: metav1.ObjectMeta{
					Name: "u1-dp1",
					Labels: map[string]string{
						"aug.devpod.name": "u1-dp1",
						"aug.user":        "u1",
					},
				},
				Spec: spec.DevPod{
					ClusterName: "c1",
					UserName:    "u1",
					DevPodName:  "u1-dp1",
				},
			}),
			want: []error{},
			not:  []error{spec.ErrUserNameEmpty, spec.ErrNoUserNamePrefix, spec.ErrNoSuffix, ErrMetadataNameMismatch, ErrDevPodNameLabelMismatch, ErrUserNameLabelMismatch},
		},
		"metadata-name-mismatch": {
			spec: NewDevPod(&devPodObject{
				ObjectMeta: metav1.ObjectMeta{
					Name: "u1-dp1-extra",
					Labels: map[string]string{
						"aug.devpod.name": "u1-dp1",
						"aug.user":        "u1",
					},
				},
				Spec: spec.DevPod{
					ClusterName: "c1",
					UserName:    "u1",
					DevPodName:  "u1-dp1",
				},
			}),
			want: []error{ErrMetadataNameMismatch},
			not:  []error{spec.ErrUserNameEmpty, spec.ErrNoUserNamePrefix, spec.ErrNoSuffix, ErrDevPodNameLabelMismatch, ErrUserNameLabelMismatch},
		},
		"labels-mismatch": {
			spec: NewDevPod(&devPodObject{
				ObjectMeta: metav1.ObjectMeta{
					Name: "u1-dp1",
					Labels: map[string]string{
						"aug.devpod.name": "u1-dp1-extra",
						"aug.user":        "u1-extra",
					},
				},
				Spec: spec.DevPod{
					ClusterName: "c1",
					UserName:    "u1",
					DevPodName:  "u1-dp1",
				},
			}),
			want: []error{ErrDevPodNameLabelMismatch, ErrUserNameLabelMismatch},
			not:  []error{spec.ErrUserNameEmpty, spec.ErrNoUserNamePrefix, spec.ErrNoSuffix, ErrMetadataNameMismatch},
		},
	}
	for tname, tc := range tests {
		t.Run(tname, func(t *testing.T) {
			t.Parallel()
			gotErr := tc.spec.Validate()
			if (gotErr == nil) != (len(tc.want) == 0) {
				t.Errorf("gotErr: wanted %d error(s) but got: %v.", len(tc.want), gotErr)
			}
			for _, wantErr := range tc.want {
				if !errors.Is(gotErr, wantErr) {
					t.Errorf("Error does not include: %v", wantErr)
				}
			}
			for _, wantNotErr := range tc.not {
				if errors.Is(gotErr, wantNotErr) {
					t.Errorf("Error includes: %v", wantNotErr)
				}
			}
		})
	}
}

package devpodv1

import (
	"k8s.io/apimachinery/pkg/runtime"
)

func (in *DevPod) DeepCopy() *DevPod {
	if in == nil {
		return nil
	}
	return NewDevPod(in.raw.DeepCopy())
}

func (in *devPodObject) DeepCopyInto(out *devPodObject) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
	out.Status = in.Status
}

func (in *devPodObject) DeepCopy() *devPodObject {
	if in == nil {
		return nil
	}
	out := &devPodObject{}
	in.DeepCopyInto(out)
	return out
}

func (in *devPodObject) DeepCopyObject() runtime.Object {
	return in.DeepCopy()
}

func (in *DevPodStatus) DeepCopyInto(out *DevPodStatus) {
	*out = *in
}

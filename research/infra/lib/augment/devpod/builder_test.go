package devpod

import (
	"encoding/json"
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"

	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/infra/lib/ssh"
	"github.com/augmentcode/augment/research/infra/cfg/clusters"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod/spec"
)

func newBuilder(t *testing.T, s spec.DevPod) *Builder {
	t.Helper()
	db, err := clusters.New()
	if err != nil {
		t.Fatalf("clusters.New(): %v.", err)
	}
	c, err := db.Cluster("_UNITTEST_")
	if err != nil {
		t.Fatalf("clusters.Cluster(_UNITTEST_): %v.", err)
	}
	if s.ClusterName != "" {
		c.Name = s.ClusterName
	} else {
		s.ClusterName = c.Name
	}
	return NewBuilder(&c, &s)
}

func TestBuilderNames(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inSpec spec.DevPod

		wConfigMap         string
		wIngressService    string
		wService           string
		wDeployment        string
		wSSHHostKeysSecret string
		wHomePVCDefault    string
		wHomePVC           string
	}{
		"basic": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
			},
			wConfigMap:         "user0-name0-config",
			wIngressService:    "user0-name0-ingress",
			wService:           "user0-name0",
			wDeployment:        "user0-name0",
			wSSHHostKeysSecret: "user0-name0-ssh-hostkeys",
			wHomePVCDefault:    "user0-name0-home",
			wHomePVC:           "user0-name0-home",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			bld := newBuilder(t, tc.inSpec)

			if got, want := bld.ConfigMapName(), tc.wConfigMap; got != want {
				t.Errorf("ConfigMapName(): got %v, want %v.", got, want)
			}
			if got, want := bld.IngressServiceName(), tc.wIngressService; got != want {
				t.Errorf("IngressServiceName(): got %v, want %v.", got, want)
			}
			if got, want := bld.ServiceName(), tc.wService; got != want {
				t.Errorf("ServiceName(): got %v, want %v.", got, want)
			}
			if got, want := bld.DeploymentName(), tc.wDeployment; got != want {
				t.Errorf("DeploymentName(): got %v, want %v.", got, want)
			}
			if got, want := bld.SSHHostKeysSecretName(), tc.wSSHHostKeysSecret; got != want {
				t.Errorf("SSHHostKeysSecret(): got %v, want %v.", got, want)
			}
			if got, want := bld.HomePVCNameDefault(), tc.wHomePVCDefault; got != want {
				t.Errorf("HomePVCNameDefault(): got %v, want %v.", got, want)
			}
			if got, want := bld.HomePVCName(), tc.wHomePVC; got != want {
				t.Errorf("HomePVCName(): got %v, want %v.", got, want)
			}
		})
	}
}

func TestBuilderLabels(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inSpec   spec.DevPod
		oGPUType string
		wantBase map[string]string
		wantMeta map[string]string
	}{
		"no-gpu": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
			},
			oGPUType: "",
			wantBase: map[string]string{
				"aug.devpod": "true",
				"aug.type":   "devpod",
				"aug.user":   "user0",
			},
			wantMeta: map[string]string{
				"aug.devpod":      "true",
				"aug.type":        "devpod",
				"aug.user":        "user0",
				"aug.devpod.name": "user0-name0",
				"aug.service":     "user0-name0",
				"aug.gpu_type":    "",
			},
		},
		"with-gpu": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
			},
			oGPUType: "gpu0",
			wantBase: map[string]string{
				"aug.devpod": "true",
				"aug.type":   "devpod",
				"aug.user":   "user0",
			},
			wantMeta: map[string]string{
				"aug.devpod":      "true",
				"aug.type":        "devpod",
				"aug.user":        "user0",
				"aug.devpod.name": "user0-name0",
				"aug.service":     "user0-name0",
				"aug.gpu_type":    "gpu0",
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			bld := newBuilder(t, tc.inSpec)
			if t := tc.oGPUType; t != "" {
				bld.n.PrimaryAlias = t
				bld.n.GPUs = 1
			}

			if diff := cmp.Diff(tc.wantBase, bld.BaseLabels()); diff != "" {
				t.Errorf("BaseLabels(): -want +got:\n%s", diff)
			}

			if diff := cmp.Diff(tc.wantMeta, bld.MetaLabels()); diff != "" {
				t.Errorf("MetaLabels(): -want +got:\n%s", diff)
			}
		})
	}
}

func TestBuilderBuildSet(t *testing.T) {
	t.Parallel()
	bld := newBuilder(t, spec.DevPod{
		UserName:   "user0",
		DevPodName: "user0-name0",
	})
	if set, err := bld.BuildSet(nil); err != nil {
		t.Errorf("BuildSet(): %v.", err)
	} else if _, err := set.ApplyConfigYAML(); err != nil {
		t.Errorf("set.ApplyConfigYAML(): %v.", err)
	}
}

// TODO(mattm): Do a better job testing w/ file IO.
func TestBuilderConfigMap(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inSpec    spec.DevPod
		oInitSpec string
		oDotExtra []string
		wantErr   string
		want      string
	}{
		"init-error": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
			},
			oInitSpec: "/tmp/does-not-exist.txt",
			wantErr:   "/tmp/does-not-exist.txt: no such file or directory",
		},
		"dot-error": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
			},
			oDotExtra: []string{"/tmp/does-not-exist.txt"},
			wantErr:   "/tmp/does-not-exist.txt: no such file or directory",
		},
		"basic": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
			},
			oInitSpec: "embed://",
			want: `{
				"kind": "ConfigMap",
				"metadata": {
					"name": "user0-name0-config",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"data": {
					"devpod.spec.yaml": "<devpod.spec.yaml>",
					"devpod.spec.json": "<devpod.spec.json>",
					"dotfiles.tgz.b64": "<dotfiles.tgz.b64>",
					"init.sh": "<init.sh>",
					"user": "user0"
				}
			}`,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			bld := newBuilder(t, tc.inSpec)
			bld.InitSpec = tc.oInitSpec
			bld.DotExtra = tc.oDotExtra

			gotCM, gotErr := bld.BuildConfigMap()

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}

			// Ensure we can diff
			if gotCM == nil {
				gotCM = k8s.NewConfigMapApplyConfig("", "")
			} else {
				// Mask bulk data
				gotCM.WithData(map[string]string{
					"devpod.spec.json": "<devpod.spec.json>",
					"devpod.spec.yaml": "<devpod.spec.yaml>",
					"dotfiles.tgz.b64": "<dotfiles.tgz.b64>",
					"init.sh":          "<init.sh>",
				})
			}

			wantCM := k8s.NewConfigMapApplyConfig("", "")
			if err := wantCM.FromJSON(tc.want); err != nil {
				t.Errorf("failed to unmarshal tc.want JSON: %v.", err)
			} else if diff := cmp.Diff(wantCM.Raw(), gotCM.Raw(), cmpopts.EquateEmpty()); diff != "" {
				t.Errorf("ConfigMap: -want +got:\n%s", diff)
			}
		})
	}
}

func TestBuilderIngressService(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inSpec  spec.DevPod
		wantErr string
		want    string
	}{
		"no-ports": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
			},
		},
		"bld-ports": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Cxn: spec.Cxn{
					ExposedPorts: []spec.ExposedPort{
						{Port: 1},
						{Port: 2},
					},
				},
			},
			want: `{
				"kind": "Service",
				"metadata": {
					"name": "user0-name0-ingress",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"type": "ClusterIP",
					"selector": {
						"k8s.deployment": "user0-name0"
					},
					"ports": [
						{
							"name": "1",
							"protocol": "TCP",
							"port": 1,
							"targetPort": 1
						},
						{
							"name": "2",
							"protocol": "TCP",
							"port": 2,
							"targetPort": 2
						}
					]
				}
			}`,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			bld := newBuilder(t, tc.inSpec)
			gotObj, gotErr := bld.BuildIngressService()

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}

			// Ensure we can diff
			if gotObj == nil {
				gotObj = k8s.NewServiceApplyConfig("", "")
			}

			wantObj := k8s.NewServiceApplyConfig("", "")
			if err := wantObj.FromJSON(tc.want); err != nil {
				t.Errorf("failed to unmarshal tc.want JSON: %v.", err)
			} else if diff := cmp.Diff(wantObj.Raw(), gotObj.Raw(), cmpopts.EquateEmpty()); diff != "" {
				t.Errorf("IngressService: -want +got:\n%s", diff)
			}
		})
	}
}

func TestBuilderService(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inSpec  spec.DevPod
		want    string
		wantErr string
	}{
		"private": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Cxn: spec.Cxn{
					SSHStdPort: false,
					PublicIP_p: func() *bool { b := false; return &b }(),
				},
			},
			want: `{
				"kind": "Service",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"type": "ClusterIP",
					"selector": {
						"k8s.deployment": "user0-name0"
					},
					"ports": [
						{
							"name": "ssh",
							"protocol": "TCP",
							"port": 22022,
							"targetPort": 22022
						}
					]
				}
			}`,
		},
		"private-with-std": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Cxn: spec.Cxn{
					SSHStdPort: true,
					PublicIP_p: func() *bool { b := false; return &b }(),
				},
			},
			want: `{
				"kind": "Service",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"type": "ClusterIP",
					"selector": {
						"k8s.deployment": "user0-name0"
					},
					"ports": [
						{
							"name": "ssh",
							"protocol": "TCP",
							"port": 22022,
							"targetPort": 22022
						},
						{
							"name": "ssh-std",
							"protocol": "TCP",
							"port": 22,
							"targetPort": 22022
						}
					]
				}
			}`,
		},
		"public": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Cxn: spec.Cxn{
					PublicIP_p: func() *bool { b := true; return &b }(),
				},
			},
			want: `{
				"kind": "Service",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					},
					"annotations": {
						"external-dns.alpha.kubernetes.io/hostname": "user0-name0._unittest_.r.augmentcode.com"
					}
				},
				"spec": {
					"type": "LoadBalancer",
					"externalTrafficPolicy": "Local",
					"selector": {
						"k8s.deployment": "user0-name0"
					},
					"ports": [
						{
							"name": "ssh",
							"protocol": "TCP",
							"port": 22022,
							"targetPort": 22022
						}
					]
				}
			}`,
		},
		"public-mosh": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Cxn: spec.Cxn{
					MoshStart: 60000,
					MoshCount: 2,
				},
			},
			want: `{
				"kind": "Service",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					},
					"annotations": {
						"external-dns.alpha.kubernetes.io/hostname": "user0-name0._unittest_.r.augmentcode.com"
					}
				},
				"spec": {
					"type": "LoadBalancer",
					"externalTrafficPolicy": "Local",
					"selector": {
						"k8s.deployment": "user0-name0"
					},
					"ports": [
						{
							"name": "ssh",
							"protocol": "TCP",
							"port": 22022,
							"targetPort": 22022
						},
						{
							"name": "mosh-60000",
							"protocol": "UDP",
							"port": 60000,
							"targetPort": 60000
						},
						{
							"name": "mosh-60001",
							"protocol": "UDP",
							"port": 60001,
							"targetPort": 60001
						}
					]
				}
			}`,
		},
		"public-mosh-too-many": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Cxn: spec.Cxn{
					MoshStart: 60000,
					MoshCount: 11,
				},
			},
			wantErr: "11: too many mosh ports, limit is currently 10",
		},
		"eternal-terminal": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Cxn: spec.Cxn{
					EternalTerminal: true,
				},
			},
			want: `{
				"kind": "Service",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					},
					"annotations": {
						"external-dns.alpha.kubernetes.io/hostname": "user0-name0._unittest_.r.augmentcode.com"
					}
				},
				"spec": {
					"type": "LoadBalancer",
					"externalTrafficPolicy": "Local",
					"selector": {
						"k8s.deployment": "user0-name0"
					},
					"ports": [
						{
							"name": "ssh",
							"protocol": "TCP",
							"port": 22022,
							"targetPort": 22022
						},
						{
							"name": "eternal-terminal",
							"protocol": "TCP",
							"port": 22023,
							"targetPort": 22023
						}
					]
				}
			}`,
		},
		"cw-private": {
			inSpec: spec.DevPod{
				ClusterName: "cw-test0",
				UserName:    "user0",
				DevPodName:  "user0-name0",
				Cxn: spec.Cxn{
					PublicIP_p: func() *bool { b := false; return &b }(),
				},
			},
			want: `{
				"kind": "Service",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"type": "ClusterIP",
					"selector": {
						"k8s.deployment": "user0-name0"
					},
					"ports": [
						{
							"name": "ssh",
							"protocol": "TCP",
							"port": 22022,
							"targetPort": 22022
						}
					]
				}
			}`,
		},
		"cw-public": {
			inSpec: spec.DevPod{
				ClusterName: "cw-test0",
				UserName:    "user0",
				DevPodName:  "user0-name0",
			},
			want: `{
				"kind": "Service",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					},
					"annotations": {
						"external-dns.alpha.kubernetes.io/hostname": "user0-name0.cw-test0.r.augmentcode.com",
						"service.beta.kubernetes.io/coreweave-load-balancer-ip-families": "ipv4",
						"service.beta.kubernetes.io/coreweave-load-balancer-type": "public"
					}
				},
				"spec": {
					"type": "LoadBalancer",
					"externalTrafficPolicy": "Local",
					"selector": {
						"k8s.deployment": "user0-name0"
					},
					"ports": [
						{
							"name": "ssh",
							"protocol": "TCP",
							"port": 22022,
							"targetPort": 22022
						}
					]
				}
			}`,
		},
		"gcp-public-docker": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				DockerMode: true,
				Cxn: spec.Cxn{
					PublicIP_p: func() *bool { b := true; return &b }(), // ignored
				},
			},
			want: `{
				"kind": "Service",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"type": "ClusterIP",
					"selector": {
						"k8s.deployment": "user0-name0"
					},
					"ports": [
						{
							"name": "ssh",
							"protocol": "TCP",
							"port": 22022,
							"targetPort": 22022
						}
					]
				}
			}`,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			bld := newBuilder(t, tc.inSpec)
			gotObj, gotErr := bld.BuildService()

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}

			// Ensure we can diff
			if gotObj == nil {
				gotObj = k8s.NewServiceApplyConfig("", "")
			}

			wantObj := k8s.NewServiceApplyConfig("", "")
			if err := wantObj.FromJSON(tc.want); err != nil {
				t.Errorf("failed to unmarshal tc.want JSON: %v.", err)
			} else if diff := cmp.Diff(wantObj.Raw(), gotObj.Raw(), cmpopts.EquateEmpty()); diff != "" {
				t.Errorf("Service: -want +got:\n%s", diff)
			}
		})
	}
}

func TestBuilderSSHHostKeysSecret(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inSpec  spec.DevPod
		want    string
		wantErr string
		wantCom string
	}{
		"success": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
			},
			wantCom: "root@user0-name0",
			want: `{
				"kind": "Secret",
				"metadata": {
					"name": "user0-name0-ssh-hostkeys",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"type": "augmentcode.com/ssh-hostkeys",
				"data": {
					"ssh_host_ecdsa_key": "PGRlZmVycmVkPg==",
					"ssh_host_ecdsa_key.pub": "PGRlZmVycmVkPg==",
					"ssh_host_ed25519_key": "PGRlZmVycmVkPg==",
					"ssh_host_ed25519_key.pub": "PGRlZmVycmVkPg==",
					"ssh_host_rsa_key": "PGRlZmVycmVkPg==",
					"ssh_host_rsa_key.pub": "PGRlZmVycmVkPg=="
				}
			}`,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			bld := newBuilder(t, tc.inSpec)

			gotObj, gotErr := bld.BuildSSHHostKeysSecret()

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}

			// Ensure we can diff
			if gotObj == nil {
				gotObj = k8s.NewSecretApplyConfig("", "")
			}

			set := ssh.HostKeyFileSet(gotObj.Raw().Data)
			set.TestMockedOut(t, tc.wantCom)
			for k := range gotObj.Raw().Data {
				gotObj.Raw().Data[k] = []byte("<deferred>")
			}

			wantObj := k8s.NewSecretApplyConfig("", "")
			if err := wantObj.FromJSON(tc.want); err != nil {
				t.Errorf("failed to unmarshal tc.want JSON: %v.", err)
			} else if diff := cmp.Diff(wantObj.Raw(), gotObj.Raw(), cmpopts.EquateEmpty()); diff != "" {
				t.Errorf("SSHHostKeysSecret: -want +got:\n%s", diff)
			}
		})
	}
}

func TestBuilderHomePVC(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inSpec  spec.DevPod
		want    string
		wantErr string
	}{
		"ephemeral": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Home: spec.Home{
					Class: "ephemeral",
				},
			},
			want: ``,
		},
		"gcp-defaults": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
			},
			want: `{
				"kind": "PersistentVolumeClaim",
				"metadata": {
					"name": "user0-name0-home",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"storageClassName": "premium-rwo",
					"accessModes": ["ReadWriteOnce"],
					"resources": {
						"requests": {
							"storage": "256Gi"
						}
					}
				}
			}`,
		},
		"gcp-rwx": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Home: spec.Home{
					Class: "class0",
					Mode:  "rwx",
				},
			},
			want: `{
				"kind": "PersistentVolumeClaim",
				"metadata": {
					"name": "user0-name0-home",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"storageClassName": "class0",
					"accessModes": ["ReadWriteMany"],
					"resources": {
						"requests": {
							"storage": "256Gi"
						}
					}
				}
			}`,
		},
		"gcp-docker": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				DockerMode: true,
			},
			want: `{
				"kind": "PersistentVolumeClaim",
				"metadata": {
					"name": "user0-name0-home",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"storageClassName": "premium-rwo",
					"accessModes": ["ReadWriteOnce"],
					"resources": {
						"requests": {
							"storage": "500Gi"
						}
					}
				}
			}`,
		},
		"gcp-docker-large": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Home: spec.Home{
					Size_p: resource.NewQuantity(1024*1024*1024*1024, resource.BinarySI),
				},
				DockerMode: true,
			},
			want: `{
				"kind": "PersistentVolumeClaim",
				"metadata": {
					"name": "user0-name0-home",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"storageClassName": "premium-rwo",
					"accessModes": ["ReadWriteOnce"],
					"resources": {
						"requests": {
							"storage": "1Ti"
						}
					}
				}
			}`,
		},
		"cw-defaults": {
			inSpec: spec.DevPod{
				ClusterName: "cw-test0",
				UserName:    "user0",
				DevPodName:  "user0-name0",
			},
			want: `{
				"kind": "PersistentVolumeClaim",
				"metadata": {
					"name": "user0-name0-home",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"storageClassName": "shared-vast",
					"accessModes": ["ReadWriteOnce"],
					"resources": {
						"requests": {
							"storage": "256Gi"
						}
					}
				}
			}`,
		},
		"custom-size": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Home: spec.Home{
					Size_p: resource.NewQuantity(42*1024*1024*1024, resource.BinarySI),
				},
			},
			want: `{
				"kind": "PersistentVolumeClaim",
				"metadata": {
					"name": "user0-name0-home",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"storageClassName": "premium-rwo",
					"accessModes": ["ReadWriteOnce"],
					"resources": {
						"requests": {
							"storage": "42Gi"
						}
					}
				}
			}`,
		},
		"custom-but-default-name": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Home: spec.Home{
					CustomName: "user0-name0-home",
					Size_p:     resource.NewQuantity(256*1024*1024*1024, resource.BinarySI),
				},
			},
			want: `{
				"kind": "PersistentVolumeClaim",
				"metadata": {
					"name": "user0-name0-home",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"storageClassName": "premium-rwo",
					"accessModes": ["ReadWriteOnce"],
					"resources": {
						"requests": {
							"storage": "256Gi"
						}
					}
				}
			}`,
		},
		"custom-name-and-size": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Home: spec.Home{
					CustomName: "user0-myhome",
					Size_p:     resource.NewQuantity(42*1024*1024*1024, resource.BinarySI),
				},
			},
			want: `{
				"kind": "PersistentVolumeClaim",
				"metadata": {
					"name": "user0-myhome",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"storageClassName": "premium-rwo",
					"accessModes": ["ReadWriteOnce"],
					"resources": {
						"requests": {
							"storage": "42Gi"
						}
					}
				}
			}`,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			bld := newBuilder(t, tc.inSpec)
			gotObj, gotErr := bld.BuildHomePVC()

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}

			if got, want := (gotObj == nil), (tc.want == ""); got != want {
				t.Errorf("HomePVC (nil): got %t, want %t", got, want)
			}

			// Ensure we can diff
			if gotObj == nil {
				gotObj = k8s.NewPVCApplyConfig("", "")
			}

			wantObj := k8s.NewPVCApplyConfig("", "")
			if err := wantObj.FromJSON(tc.want); err != nil {
				t.Errorf("failed to unmarshal tc.want JSON: %v.", err)
			} else if diff := cmp.Diff(wantObj.Raw(), gotObj.Raw(), cmpopts.EquateEmpty()); diff != "" {
				t.Errorf("HomePVC: -want +got:\n%s", diff)
			}
		})
	}
}

func TestBuilderDeployment(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inSpec  spec.DevPod
		oNS     bool
		want    string
		wantErr string
	}{
		"defaults": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
			},
			want: `{
				"kind": "Deployment",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"replicas": 1,
					"strategy": { "type": "Recreate" },
					"selector": { "matchLabels": { "k8s.deployment": "user0-name0" } },
					"template": {
						"metadata": {
							"labels": {
								"k8s.deployment": "user0-name0",
								"aug.devpod": "true",
								"aug.devpod.name": "user0-name0",
								"aug.gpu_type": "",
								"aug.service": "user0-name0",
								"aug.type": "devpod",
								"aug.user": "user0"
							},
							"annotations": {
								"kubectl.kubernetes.io/default-container": "c1-dev",
								"gke-gcsfuse/volumes": "true",
								"gke-gcsfuse/cpu-limit": "1",
								"gke-gcsfuse/cpu-request": "1",
								"gke-gcsfuse/memory-limit": "1Gi",
								"gke-gcsfuse/memory-request": "1Gi",
								"gke-gcsfuse/ephemeral-storage-limit": "317.4Gi",
								"gke-gcsfuse/ephemeral-storage-request": "317.4Gi"
							}
						},
						"spec": {
							"hostname": "user0-name0",
							"setHostnameAsFQDN": true,
							"enableServiceLinks": false,
							"restartPolicy": "Always",
							"priorityClassName": "aug-devpod-default-priority",
							"serviceAccountName": "",
							"affinity": {
								"podAffinity": {
									"preferredDuringSchedulingIgnoredDuringExecution": [
										{
											"weight": 100,
											"podAffinityTerm": {
												"topologyKey": "kubernetes.io/hostname",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										},
										{
											"weight": 99,
											"podAffinityTerm": {
												"topologyKey": "cloud.google.com/gke-nodepool",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										}
									]
								},
								"nodeAffinity": {
									"requiredDuringSchedulingIgnoredDuringExecution": {
										"nodeSelectorTerms": [{
											"matchExpressions": [
												{
													"key": "cloud.google.com/gke-ephemeral-storage-local-ssd",
													"operator": "In",
													"values": ["true"]
												}
											]
										}]
									}
								},
								"podAntiAffinity": {}
							},
							"tolerations": [
								{
									"effect": "NoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								},
								{
									"effect": "PreferNoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								}
							],
							"volumes": [],
							"containers": [{
								"name": "c1-dev",
								"image": "reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_cpu",
								"imagePullPolicy": "Always",
								"resources": {
									"limits": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									},
									"requests": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									}
								},
								"volumeMounts": [],
								"command": ["/bin/bash", "/startup/init.sh", "--ssh-port=22022", "--et-port=0"],
								"securityContext": { "runAsUser": 0, "privileged": false }
							}]
						}
					}
				}
			}`,
		},
		"gcp-l4": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Resources: spec.Resources{
					NodeType: "l4",
					GPU:      func() *int32 { i := int32(2); return &i }(),
					CPU:      resource.NewQuantity(7, resource.DecimalSI),
				},
			},
			want: `{
				"kind": "Deployment",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "nvidia-l4",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"replicas": 1,
					"strategy": { "type": "Recreate" },
					"selector": { "matchLabels": { "k8s.deployment": "user0-name0" } },
					"template": {
						"metadata": {
							"labels": {
								"k8s.deployment": "user0-name0",
								"aug.devpod": "true",
								"aug.devpod.name": "user0-name0",
								"aug.gpu_type": "nvidia-l4",
								"aug.service": "user0-name0",
								"aug.type": "devpod",
								"aug.user": "user0"
							},
							"annotations": {
								"kubectl.kubernetes.io/default-container": "c1-dev",
								"gke-gcsfuse/volumes": "true",
								"gke-gcsfuse/cpu-limit": "4",
								"gke-gcsfuse/cpu-request": "4",
								"gke-gcsfuse/memory-limit": "4Gi",
								"gke-gcsfuse/memory-request": "4Gi",
								"gke-gcsfuse/ephemeral-storage-limit": "370.5Gi",
								"gke-gcsfuse/ephemeral-storage-request": "370.5Gi"
							}
						},
						"spec": {
							"hostname": "user0-name0",
							"setHostnameAsFQDN": true,
							"enableServiceLinks": false,
							"restartPolicy": "Always",
							"priorityClassName": "aug-devpod-default-priority",
							"serviceAccountName": "",
							"affinity": {
								"podAffinity": {
									"preferredDuringSchedulingIgnoredDuringExecution": [
										{
											"weight": 100,
											"podAffinityTerm": {
												"topologyKey": "kubernetes.io/hostname",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										},
										{
											"weight": 99,
											"podAffinityTerm": {
												"topologyKey": "cloud.google.com/gke-nodepool",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										}
									]
								},
								"nodeAffinity": {
									"requiredDuringSchedulingIgnoredDuringExecution": {
										"nodeSelectorTerms": [{
											"matchExpressions": [
												{
													"key": "cloud.google.com/gke-accelerator",
													"operator": "In",
													"values": ["nvidia-l4"]
												},
												{
													"key": "cloud.google.com/gke-ephemeral-storage-local-ssd",
													"operator": "In",
													"values": ["true"]
												}
											]
										}]
									}
								},
								"podAntiAffinity": {}
							},
							"volumes": [],
							"containers": [{
								"name": "c1-dev",
								"image": "reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_gpu",
								"imagePullPolicy": "Always",
								"resources": {
									"limits": {
										"cpu": "7",
										"memory": "87.01G",
										"ephemeral-storage": "172.75Gi",
										"nvidia.com/gpu": "2"
									},
									"requests": {
										"cpu": "7",
										"memory": "87.01G",
										"ephemeral-storage": "172.75Gi",
										"nvidia.com/gpu": "2"
									}
								},
								"volumeMounts": [],
								"command": ["/bin/bash", "/startup/init.sh", "--ssh-port=22022", "--et-port=0"],
								"securityContext": { "runAsUser": 0, "privileged": false }
							}]
						}
					}
				}
			}`,
		},
		"specific-node": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Resources: spec.Resources{
					NodeName: "node0",
				},
			},
			want: `{
				"kind": "Deployment",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"replicas": 1,
					"strategy": { "type": "Recreate" },
					"selector": { "matchLabels": { "k8s.deployment": "user0-name0" } },
					"template": {
						"metadata": {
							"labels": {
								"k8s.deployment": "user0-name0",
								"aug.devpod": "true",
								"aug.devpod.name": "user0-name0",
								"aug.gpu_type": "",
								"aug.service": "user0-name0",
								"aug.type": "devpod",
								"aug.user": "user0"
							},
							"annotations": {
								"kubectl.kubernetes.io/default-container": "c1-dev",
								"gke-gcsfuse/volumes": "true",
								"gke-gcsfuse/cpu-limit": "1",
								"gke-gcsfuse/cpu-request": "1",
								"gke-gcsfuse/memory-limit": "1Gi",
								"gke-gcsfuse/memory-request": "1Gi",
								"gke-gcsfuse/ephemeral-storage-limit": "317.4Gi",
								"gke-gcsfuse/ephemeral-storage-request": "317.4Gi"
							}
						},
						"spec": {
							"hostname": "user0-name0",
							"setHostnameAsFQDN": true,
							"enableServiceLinks": false,
							"restartPolicy": "Always",
							"priorityClassName": "aug-devpod-default-priority",
							"serviceAccountName": "",
							"affinity": {
								"podAffinity": {
									"preferredDuringSchedulingIgnoredDuringExecution": [
										{
											"weight": 100,
											"podAffinityTerm": {
												"topologyKey": "kubernetes.io/hostname",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										},
										{
											"weight": 99,
											"podAffinityTerm": {
												"topologyKey": "cloud.google.com/gke-nodepool",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										}
									]
								},
								"nodeAffinity": {
									"requiredDuringSchedulingIgnoredDuringExecution": {
										"nodeSelectorTerms": [{
											"matchExpressions": [
												{
													"key": "cloud.google.com/gke-ephemeral-storage-local-ssd",
													"operator": "In",
													"values": ["true"]
												},
												{
													"key": "kubernetes.io/hostname",
													"operator": "In",
													"values": ["node0"]
												}
											]
										}]
									}
								},
								"podAntiAffinity": {}
							},
							"tolerations": [
								{
									"effect": "NoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								},
								{
									"effect": "PreferNoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								}
							],
							"volumes": [],
							"containers": [{
								"name": "c1-dev",
								"image": "reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_cpu",
								"imagePullPolicy": "Always",
								"resources": {
									"limits": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									},
									"requests": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									}
								},
								"volumeMounts": [],
								"command": ["/bin/bash", "/startup/init.sh", "--ssh-port=22022", "--et-port=0"],
								"securityContext": { "runAsUser": 0, "privileged": false }
							}]
						}
					}
				}
			}`,
		},
		"tolerations": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Resources: spec.Resources{
					PoolType: "pool0",
					Tolerations: []corev1.Toleration{
						{
							Key:      "key0",
							Operator: "Equal",
							Value:    "val0",
							Effect:   "NoSchedule",
						},
					},
				},
			},
			want: `{
				"kind": "Deployment",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"replicas": 1,
					"strategy": { "type": "Recreate" },
					"selector": { "matchLabels": { "k8s.deployment": "user0-name0" } },
					"template": {
						"metadata": {
							"labels": {
								"k8s.deployment": "user0-name0",
								"aug.devpod": "true",
								"aug.devpod.name": "user0-name0",
								"aug.gpu_type": "",
								"aug.service": "user0-name0",
								"aug.type": "devpod",
								"aug.user": "user0"
							},
							"annotations": {
								"kubectl.kubernetes.io/default-container": "c1-dev",
								"gke-gcsfuse/volumes": "true",
								"gke-gcsfuse/cpu-limit": "1",
								"gke-gcsfuse/cpu-request": "1",
								"gke-gcsfuse/memory-limit": "1Gi",
								"gke-gcsfuse/memory-request": "1Gi",
								"gke-gcsfuse/ephemeral-storage-limit": "317.4Gi",
								"gke-gcsfuse/ephemeral-storage-request": "317.4Gi"
							}
						},
						"spec": {
							"hostname": "user0-name0",
							"setHostnameAsFQDN": true,
							"enableServiceLinks": false,
							"restartPolicy": "Always",
							"priorityClassName": "aug-devpod-default-priority",
							"serviceAccountName": "",
							"affinity": {
								"podAffinity": {
									"preferredDuringSchedulingIgnoredDuringExecution": [
										{
											"weight": 100,
											"podAffinityTerm": {
												"topologyKey": "kubernetes.io/hostname",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										},
										{
											"weight": 99,
											"podAffinityTerm": {
												"topologyKey": "cloud.google.com/gke-nodepool",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										}
									]
								},
								"nodeAffinity": {
									"requiredDuringSchedulingIgnoredDuringExecution": {
										"nodeSelectorTerms": [{
											"matchExpressions": [
												{
													"key": "cloud.google.com/gke-ephemeral-storage-local-ssd",
													"operator": "In",
													"values": ["true"]
												}
											]
										}]
									}
								},
								"podAntiAffinity": {}
							},
							"tolerations": [
								{
									"effect": "NoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "pool0"
								},
								{
									"effect": "PreferNoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "pool0"
								},
								{
									"effect": "NoSchedule",
									"key": "key0",
									"value": "val0",
									"operator": "Equal"
								}
							],
							"volumes": [],
							"containers": [{
								"name": "c1-dev",
								"image": "reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_cpu",
								"imagePullPolicy": "Always",
								"resources": {
									"limits": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									},
									"requests": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									}
								},
								"volumeMounts": [],
								"command": ["/bin/bash", "/startup/init.sh", "--ssh-port=22022", "--et-port=0"],
								"securityContext": { "runAsUser": 0, "privileged": false }
							}]
						}
					}
				}
			}`,
		},
		"custom-image": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Image: spec.Image{
					Registry: "reg0",
					Path:     "path0/path00",
					Tag:      "tag0",
				},
			},
			want: `{
				"kind": "Deployment",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"replicas": 1,
					"strategy": { "type": "Recreate" },
					"selector": { "matchLabels": { "k8s.deployment": "user0-name0" } },
					"template": {
						"metadata": {
							"labels": {
								"k8s.deployment": "user0-name0",
								"aug.devpod": "true",
								"aug.devpod.name": "user0-name0",
								"aug.gpu_type": "",
								"aug.service": "user0-name0",
								"aug.type": "devpod",
								"aug.user": "user0"
							},
							"annotations": {
								"kubectl.kubernetes.io/default-container": "c1-dev",
								"gke-gcsfuse/volumes": "true",
								"gke-gcsfuse/cpu-limit": "1",
								"gke-gcsfuse/cpu-request": "1",
								"gke-gcsfuse/memory-limit": "1Gi",
								"gke-gcsfuse/memory-request": "1Gi",
								"gke-gcsfuse/ephemeral-storage-limit": "317.4Gi",
								"gke-gcsfuse/ephemeral-storage-request": "317.4Gi"
							}
						},
						"spec": {
							"hostname": "user0-name0",
							"setHostnameAsFQDN": true,
							"enableServiceLinks": false,
							"restartPolicy": "Always",
							"priorityClassName": "aug-devpod-default-priority",
							"serviceAccountName": "",
							"affinity": {
								"podAffinity": {
									"preferredDuringSchedulingIgnoredDuringExecution": [
										{
											"weight": 100,
											"podAffinityTerm": {
												"topologyKey": "kubernetes.io/hostname",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										},
										{
											"weight": 99,
											"podAffinityTerm": {
												"topologyKey": "cloud.google.com/gke-nodepool",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										}
									]
								},
								"nodeAffinity": {
									"requiredDuringSchedulingIgnoredDuringExecution": {
										"nodeSelectorTerms": [{
											"matchExpressions": [
												{
													"key": "cloud.google.com/gke-ephemeral-storage-local-ssd",
													"operator": "In",
													"values": ["true"]
												}
											]
										}]
									}
								},
								"podAntiAffinity": {}
							},
							"tolerations": [
								{
									"effect": "NoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								},
								{
									"effect": "PreferNoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								}
							],
							"volumes": [],
							"containers": [{
								"name": "c1-dev",
								"image": "reg0/path0/path00",
								"imagePullPolicy": "Always",
								"resources": {
									"limits": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									},
									"requests": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									}
								},
								"volumeMounts": [],
								"command": ["/bin/bash", "/startup/init.sh", "--ssh-port=22022", "--et-port=0"],
								"securityContext": { "runAsUser": 0, "privileged": false }
							}]
						}
					}
				}
			}`,
		},
		"eternal-terminal": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Cxn: spec.Cxn{
					EternalTerminal: true,
				},
			},
			want: `{
				"kind": "Deployment",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"replicas": 1,
					"strategy": { "type": "Recreate" },
					"selector": { "matchLabels": { "k8s.deployment": "user0-name0" } },
					"template": {
						"metadata": {
							"labels": {
								"k8s.deployment": "user0-name0",
								"aug.devpod": "true",
								"aug.devpod.name": "user0-name0",
								"aug.gpu_type": "",
								"aug.service": "user0-name0",
								"aug.type": "devpod",
								"aug.user": "user0"
							},
							"annotations": {
								"kubectl.kubernetes.io/default-container": "c1-dev",
								"gke-gcsfuse/volumes": "true",
								"gke-gcsfuse/cpu-limit": "1",
								"gke-gcsfuse/cpu-request": "1",
								"gke-gcsfuse/memory-limit": "1Gi",
								"gke-gcsfuse/memory-request": "1Gi",
								"gke-gcsfuse/ephemeral-storage-limit": "317.4Gi",
								"gke-gcsfuse/ephemeral-storage-request": "317.4Gi"
							}
						},
						"spec": {
							"hostname": "user0-name0",
							"setHostnameAsFQDN": true,
							"enableServiceLinks": false,
							"restartPolicy": "Always",
							"priorityClassName": "aug-devpod-default-priority",
							"serviceAccountName": "",
							"affinity": {
								"podAffinity": {
									"preferredDuringSchedulingIgnoredDuringExecution": [
										{
											"weight": 100,
											"podAffinityTerm": {
												"topologyKey": "kubernetes.io/hostname",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										},
										{
											"weight": 99,
											"podAffinityTerm": {
												"topologyKey": "cloud.google.com/gke-nodepool",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										}
									]
								},
								"nodeAffinity": {
									"requiredDuringSchedulingIgnoredDuringExecution": {
										"nodeSelectorTerms": [{
											"matchExpressions": [
												{
													"key": "cloud.google.com/gke-ephemeral-storage-local-ssd",
													"operator": "In",
													"values": ["true"]
												}
											]
										}]
									}
								},
								"podAntiAffinity": {}
							},
							"tolerations": [
								{
									"effect": "NoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								},
								{
									"effect": "PreferNoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								}
							],
							"volumes": [],
							"containers": [{
								"name": "c1-dev",
								"image": "reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_cpu",
								"imagePullPolicy": "Always",
								"resources": {
									"limits": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									},
									"requests": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									}
								},
								"volumeMounts": [],
								"command": ["/bin/bash", "/startup/init.sh", "--ssh-port=22022", "--et-port=22023"],
								"securityContext": { "runAsUser": 0, "privileged": false }
							}]
						}
					}
				}
			}`,
		},
		"power-on": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				PowerOff_p: func() *bool { b := false; return &b }(),
			},
			want: `{
				"kind": "Deployment",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"replicas": 1,
					"strategy": { "type": "Recreate" },
					"selector": { "matchLabels": { "k8s.deployment": "user0-name0" } },
					"template": {
						"metadata": {
							"labels": {
								"k8s.deployment": "user0-name0",
								"aug.devpod": "true",
								"aug.devpod.name": "user0-name0",
								"aug.gpu_type": "",
								"aug.service": "user0-name0",
								"aug.type": "devpod",
								"aug.user": "user0"
							},
							"annotations": {
								"kubectl.kubernetes.io/default-container": "c1-dev",
								"gke-gcsfuse/volumes": "true",
								"gke-gcsfuse/cpu-limit": "1",
								"gke-gcsfuse/cpu-request": "1",
								"gke-gcsfuse/memory-limit": "1Gi",
								"gke-gcsfuse/memory-request": "1Gi",
								"gke-gcsfuse/ephemeral-storage-limit": "317.4Gi",
								"gke-gcsfuse/ephemeral-storage-request": "317.4Gi"
							}
						},
						"spec": {
							"hostname": "user0-name0",
							"setHostnameAsFQDN": true,
							"enableServiceLinks": false,
							"restartPolicy": "Always",
							"priorityClassName": "aug-devpod-default-priority",
							"serviceAccountName": "",
							"affinity": {
								"podAffinity": {
									"preferredDuringSchedulingIgnoredDuringExecution": [
										{
											"weight": 100,
											"podAffinityTerm": {
												"topologyKey": "kubernetes.io/hostname",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										},
										{
											"weight": 99,
											"podAffinityTerm": {
												"topologyKey": "cloud.google.com/gke-nodepool",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										}
									]
								},
								"nodeAffinity": {
									"requiredDuringSchedulingIgnoredDuringExecution": {
										"nodeSelectorTerms": [{
											"matchExpressions": [
												{
													"key": "cloud.google.com/gke-ephemeral-storage-local-ssd",
													"operator": "In",
													"values": ["true"]
												}
											]
										}]
									}
								},
								"podAntiAffinity": {}
							},
							"tolerations": [
								{
									"effect": "NoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								},
								{
									"effect": "PreferNoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								}
							],
							"volumes": [],
							"containers": [{
								"name": "c1-dev",
								"image": "reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_cpu",
								"imagePullPolicy": "Always",
								"resources": {
									"limits": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									},
									"requests": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									}
								},
								"volumeMounts": [],
								"command": ["/bin/bash", "/startup/init.sh", "--ssh-port=22022", "--et-port=0"],
								"securityContext": { "runAsUser": 0, "privileged": false }
							}]
						}
					}
				}
			}`,
		},
		"power-off": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				PowerOff_p: func() *bool { b := true; return &b }(),
			},
			want: `{
				"kind": "Deployment",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"replicas": 0,
					"strategy": { "type": "Recreate" },
					"selector": { "matchLabels": { "k8s.deployment": "user0-name0" } },
					"template": {
						"metadata": {
							"labels": {
								"k8s.deployment": "user0-name0",
								"aug.devpod": "true",
								"aug.devpod.name": "user0-name0",
								"aug.gpu_type": "",
								"aug.service": "user0-name0",
								"aug.type": "devpod",
								"aug.user": "user0"
							},
							"annotations": {
								"kubectl.kubernetes.io/default-container": "c1-dev",
								"gke-gcsfuse/volumes": "true",
								"gke-gcsfuse/cpu-limit": "1",
								"gke-gcsfuse/cpu-request": "1",
								"gke-gcsfuse/memory-limit": "1Gi",
								"gke-gcsfuse/memory-request": "1Gi",
								"gke-gcsfuse/ephemeral-storage-limit": "317.4Gi",
								"gke-gcsfuse/ephemeral-storage-request": "317.4Gi"
							}
						},
						"spec": {
							"hostname": "user0-name0",
							"setHostnameAsFQDN": true,
							"enableServiceLinks": false,
							"restartPolicy": "Always",
							"priorityClassName": "aug-devpod-default-priority",
							"serviceAccountName": "",
							"affinity": {
								"podAffinity": {
									"preferredDuringSchedulingIgnoredDuringExecution": [
										{
											"weight": 100,
											"podAffinityTerm": {
												"topologyKey": "kubernetes.io/hostname",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										},
										{
											"weight": 99,
											"podAffinityTerm": {
												"topologyKey": "cloud.google.com/gke-nodepool",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										}
									]
								},
								"nodeAffinity": {
									"requiredDuringSchedulingIgnoredDuringExecution": {
										"nodeSelectorTerms": [{
											"matchExpressions": [
												{
													"key": "cloud.google.com/gke-ephemeral-storage-local-ssd",
													"operator": "In",
													"values": ["true"]
												}
											]
										}]
									}
								},
								"podAntiAffinity": {}
							},
							"tolerations": [
								{
									"effect": "NoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								},
								{
									"effect": "PreferNoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								}
							],
							"volumes": [],
							"containers": [{
								"name": "c1-dev",
								"image": "reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_cpu",
								"imagePullPolicy": "Always",
								"resources": {
									"limits": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									},
									"requests": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									}
								},
								"volumeMounts": [],
								"command": ["/bin/bash", "/startup/init.sh", "--ssh-port=22022", "--et-port=0"],
								"securityContext": { "runAsUser": 0, "privileged": false }
							}]
						}
					}
				}
			}`,
		},
		"docker": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				DockerMode: true,
			},
			want: `{
				"kind": "Deployment",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"replicas": 1,
					"strategy": { "type": "Recreate" },
					"selector": { "matchLabels": { "k8s.deployment": "user0-name0" } },
					"template": {
						"metadata": {
							"labels": {
								"k8s.deployment": "user0-name0",
								"aug.devpod": "true",
								"aug.devpod.name": "user0-name0",
								"aug.gpu_type": "",
								"aug.service": "user0-name0",
								"aug.type": "devpod",
								"aug.user": "user0"
							},
							"annotations": {
								"kubectl.kubernetes.io/default-container": "c1-dev"
							}
						},
						"spec": {
							"hostname": "user0-name0",
							"setHostnameAsFQDN": true,
							"enableServiceLinks": false,
							"restartPolicy": "Always",
							"priorityClassName": "aug-devpod-default-priority",
							"serviceAccountName": "",
							"affinity": {
								"podAffinity": {
									"preferredDuringSchedulingIgnoredDuringExecution": [
										{
											"weight": 100,
											"podAffinityTerm": {
												"topologyKey": "kubernetes.io/hostname",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										},
										{
											"weight": 99,
											"podAffinityTerm": {
												"topologyKey": "cloud.google.com/gke-nodepool",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										}
									]
								},
								"nodeAffinity": {
									"requiredDuringSchedulingIgnoredDuringExecution": {
										"nodeSelectorTerms": [{
											"matchExpressions": [
												{
													"key": "cloud.google.com/gke-ephemeral-storage-local-ssd",
													"operator": "In",
													"values": ["true"]
												}
											]
										}]
									}
								},
								"podAntiAffinity": {}
							},
							"tolerations": [
								{
									"effect": "NoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								},
								{
									"effect": "PreferNoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								}
							],
							"volumes": [],
							"containers": [{
								"name": "c1-dev",
								"image": "reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_cpu",
								"imagePullPolicy": "Always",
								"resources": {
									"limits": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									},
									"requests": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									}
								},
								"volumeMounts": [],
								"command": ["/bin/bash", "/startup/init.sh", "--ssh-port=22022", "--et-port=0", "--docker"],
								"securityContext": {
									"runAsUser": 0,
									"privileged": true
								}
							}]
						}
					}
				}
			}`,
		},
		"namespaced": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
			},
			oNS: true,
			want: `{
				"kind": "Deployment",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"replicas": 1,
					"strategy": { "type": "Recreate" },
					"selector": { "matchLabels": { "k8s.deployment": "user0-name0" } },
					"template": {
						"metadata": {
							"labels": {
								"k8s.deployment": "user0-name0",
								"aug.devpod": "true",
								"aug.devpod.name": "user0-name0",
								"aug.gpu_type": "",
								"aug.service": "user0-name0",
								"aug.type": "devpod",
								"aug.user": "user0"
							},
							"annotations": {
								"kubectl.kubernetes.io/default-container": "c1-dev",
								"gke-gcsfuse/volumes": "true",
								"gke-gcsfuse/cpu-limit": "1",
								"gke-gcsfuse/cpu-request": "1",
								"gke-gcsfuse/memory-limit": "1Gi",
								"gke-gcsfuse/memory-request": "1Gi",
								"gke-gcsfuse/ephemeral-storage-limit": "317.4Gi",
								"gke-gcsfuse/ephemeral-storage-request": "317.4Gi"
							}
						},
						"spec": {
							"hostname": "user0-name0",
							"setHostnameAsFQDN": true,
							"enableServiceLinks": false,
							"restartPolicy": "Always",
							"priorityClassName": "aug-devpod-default-priority",
							"serviceAccountName": "user0",
							"affinity": {
								"podAffinity": {
									"preferredDuringSchedulingIgnoredDuringExecution": [
										{
											"weight": 100,
											"podAffinityTerm": {
												"topologyKey": "kubernetes.io/hostname",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										},
										{
											"weight": 99,
											"podAffinityTerm": {
												"topologyKey": "cloud.google.com/gke-nodepool",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										}
									]
								},
								"nodeAffinity": {
									"requiredDuringSchedulingIgnoredDuringExecution": {
										"nodeSelectorTerms": [{
											"matchExpressions": [
												{
													"key": "cloud.google.com/gke-ephemeral-storage-local-ssd",
													"operator": "In",
													"values": ["true"]
												}
											]
										}]
									}
								},
								"podAntiAffinity": {}
							},
							"tolerations": [
								{
									"effect": "NoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								},
								{
									"effect": "PreferNoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								}
							],
							"volumes": [],
							"containers": [{
								"name": "c1-dev",
								"image": "reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_cpu",
								"imagePullPolicy": "Always",
								"resources": {
									"limits": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									},
									"requests": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									}
								},
								"volumeMounts": [],
								"command": ["/bin/bash", "/startup/init.sh", "--ssh-port=22022", "--et-port=0"],
								"securityContext": { "runAsUser": 0, "privileged": false }
							}]
						}
					}
				}
			}`,
		},
		"privileged": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Privileged: true,
			},
			want: `{
				"kind": "Deployment",
				"metadata": {
					"name": "user0-name0",
					"namespace": "_unittest_",
					"labels": {
						"aug.devpod": "true",
						"aug.devpod.name": "user0-name0",
						"aug.gpu_type": "",
						"aug.service": "user0-name0",
						"aug.type": "devpod",
						"aug.user": "user0"
					}
				},
				"spec": {
					"replicas": 1,
					"strategy": { "type": "Recreate" },
					"selector": { "matchLabels": { "k8s.deployment": "user0-name0" } },
					"template": {
						"metadata": {
							"labels": {
								"k8s.deployment": "user0-name0",
								"aug.devpod": "true",
								"aug.devpod.name": "user0-name0",
								"aug.gpu_type": "",
								"aug.service": "user0-name0",
								"aug.type": "devpod",
								"aug.user": "user0"
							},
							"annotations": {
								"kubectl.kubernetes.io/default-container": "c1-dev",
								"gke-gcsfuse/volumes": "true",
								"gke-gcsfuse/cpu-limit": "1",
								"gke-gcsfuse/cpu-request": "1",
								"gke-gcsfuse/memory-limit": "1Gi",
								"gke-gcsfuse/memory-request": "1Gi",
								"gke-gcsfuse/ephemeral-storage-limit": "317.4Gi",
								"gke-gcsfuse/ephemeral-storage-request": "317.4Gi"
							}
						},
						"spec": {
							"hostname": "user0-name0",
							"setHostnameAsFQDN": true,
							"enableServiceLinks": false,
							"restartPolicy": "Always",
							"priorityClassName": "aug-devpod-default-priority",
							"serviceAccountName": "",
							"affinity": {
								"podAffinity": {
									"preferredDuringSchedulingIgnoredDuringExecution": [
										{
											"weight": 100,
											"podAffinityTerm": {
												"topologyKey": "kubernetes.io/hostname",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										},
										{
											"weight": 99,
											"podAffinityTerm": {
												"topologyKey": "cloud.google.com/gke-nodepool",
												"labelSelector": {
													"matchExpressions": [{
														"key": "aug.devpod",
														"operator": "Exists"
													}]
												}
											}
										}
									]
								},
								"nodeAffinity": {
									"requiredDuringSchedulingIgnoredDuringExecution": {
										"nodeSelectorTerms": [{
											"matchExpressions": [
												{
													"key": "cloud.google.com/gke-ephemeral-storage-local-ssd",
													"operator": "In",
													"values": ["true"]
												}
											]
										}]
									}
								},
								"podAntiAffinity": {}
							},
							"tolerations": [
								{
									"effect": "NoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								},
								{
									"effect": "PreferNoSchedule",
									"key": "r.augmentcode.com/pool-type",
									"value": "cpu"
								}
							],
							"volumes": [],
							"containers": [{
								"name": "c1-dev",
								"image": "reg-unittest0.r.augmentcode.test/augment-unittest0/docker-moon-darkside1/augment_devpod_cpu",
								"imagePullPolicy": "Always",
								"resources": {
									"limits": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									},
									"requests": {
										"cpu": "1",
										"memory": "4.48G",
										"ephemeral-storage": "20.26Gi"
									}
								},
								"volumeMounts": [],
								"command": ["/bin/bash", "/startup/init.sh", "--ssh-port=22022", "--et-port=0"],
								"securityContext": { "runAsUser": 0, "privileged": true }
							}]
						}
					}
				}
			}`,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			bld := newBuilder(t, tc.inSpec)
			bld.Namespaced = tc.oNS

			node, err := tc.inSpec.Node(bld.Cluster())
			if err != nil {
				t.Fatalf("Failed to build Node: %v.", err)
			}
			bld.n = node

			cm, err := bld.BuildConfigMap()
			if err != nil {
				t.Fatalf("err building ConfigMap: %v.", err)
			}
			sshHKSec, err := bld.BuildSSHHostKeysSecret()
			if err != nil {
				t.Fatalf("err building SSHHostKeysSecret: %v.", err)
			}
			homePVC, err := bld.BuildHomePVC()
			if err != nil {
				t.Fatalf("err building HomePVC: %v.", err)
			}

			gotObj, gotErr := bld.BuildDeployment(cm, sshHKSec, homePVC)

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}

			// Ensure we can diff
			if gotObj == nil {
				gotObj = k8s.NewDeploymentApplyConfig("", "")
			}

			// Avoid change detectors
			for i := range gotObj.Raw().Spec.Template.Spec.Containers {
				c := &gotObj.Raw().Spec.Template.Spec.Containers[i]
				if *c.Name != containerName {
					continue
				}

				// Strip image version
				before, _, _ := strings.Cut(*c.Image, ":")
				*c.Image = before

				// Strip volumes
				// We test the pod-specific volmounts separately with TestBuilderVolMounts().
				gotObj.Raw().Spec.Template.Spec.Volumes = nil
				c.VolumeMounts = nil
			}

			wantObj := k8s.NewDeploymentApplyConfig("", "")
			if err := wantObj.FromJSON(tc.want); err != nil {
				t.Errorf("failed to unmarshal tc.want JSON: %v.", err)
			} else if diff := cmp.Diff(wantObj.Raw(), gotObj.Raw(), cmpopts.EquateEmpty()); diff != "" {
				t.Errorf("Deployment: -want +got:\n%s", diff)
			}
		})
	}
}

func TestBuilderVolMounts(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inSpec  spec.DevPod
		sasec   string
		isGPU   bool
		want    string
		wantErr string
	}{
		"defaults": {
			inSpec: spec.DevPod{
				UserName:   "user0",
				DevPodName: "user0-name0",
				Volumes: spec.Volumes{
					VolMounts: clusters.VolMounts{
						clusters.VolMount{
							Name: "custom0",
							Mounts: []corev1.VolumeMount{{
								MountPath: "/custom/path",
							}},
						},
						clusters.VolMount{
							Name: "custom1",
							Mounts: []corev1.VolumeMount{{
								MountPath: "/custom1/path1",
							}},
						},
					},
				},
			},
			sasec: "aug-user-user0-secret-XYZ",
			isGPU: true,
			want: `[
				{
					"name": "run",
					"volume": {
						"name": "run",
						"emptyDir": {
							"medium": "Memory"
						}
					},
					"mounts": [{
						"name": "run",
						"mountPath": "/run"
					}]
				},
				{
					"name": "tmp",
					"volume": {
						"name": "tmp",
						"emptyDir": {
							"medium": "Memory"
						}
					},
					"mounts": [{
						"name": "tmp",
						"mountPath": "/tmp"
					}]
				},
				{
					"name": "user0-name0-config",
					"volume": {
						"name": "user0-name0-config",
						"configMap": {
							"name": "user0-name0-config"
						}
					},
					"mounts": [{
						"name": "user0-name0-config",
						"mountPath": "/startup",
						"readOnly": true
					}]
				},
				{
					"name": "user0-name0-ssh-hostkeys",
					"volume": {
						"name": "user0-name0-ssh-hostkeys",
						"secret": {
							"secretName": "user0-name0-ssh-hostkeys",
							"items": [
								{
									"key": "ssh_host_ecdsa_key",
									"path": "etc/ssh/ssh_host_ecdsa_key",
									"mode": 384
								},
								{
									"key": "ssh_host_ecdsa_key.pub",
									"path": "etc/ssh/ssh_host_ecdsa_key.pub",
									"mode": 420
								},
								{
									"key": "ssh_host_ed25519_key",
									"path": "etc/ssh/ssh_host_ed25519_key",
									"mode": 384
								},
								{
									"key": "ssh_host_ed25519_key.pub",
									"path": "etc/ssh/ssh_host_ed25519_key.pub",
									"mode": 420
								},
								{
									"key": "ssh_host_rsa_key",
									"path": "etc/ssh/ssh_host_rsa_key",
									"mode": 384
								},
								{
									"key": "ssh_host_rsa_key.pub",
									"path": "etc/ssh/ssh_host_rsa_key.pub",
									"mode": 420
								}
							]
						}
					},
					"mounts": [{
						"name": "user0-name0-ssh-hostkeys",
						"mountPath": "/etc/ssh/hostkeys"
					}]
				},
				{
					"name": "user0-name0-home",
					"volume": {
						"name": "user0-name0-home",
						"persistentVolumeClaim": {
							"claimName": "user0-name0-home"
						}
					},
					"mounts": [{
						"name": "user0-name0-home",
						"mountPath": "/home/<USER>",
						"subPath": "augment"
					}]
				},
				{
					"name": "augment-user-ssh-authorized-keys",
					"volume": {
						"name": "augment-user-ssh-authorized-keys",
						"configMap": {
							"name": "augment-user-ssh-authorized-keys",
							"items": [{
								"key": "user0",
								"path": "user0",
								"mode": 292
							}]
						}
					},
					"mounts": [{
						"name": "augment-user-ssh-authorized-keys",
						"mountPath": "/etc/ssh/authorized_keys",
						"readOnly": true
					}]
				},
				{
					"name": "aug-user-user0-secret-XYZ",
					"volume": {
						"name": "aug-user-user0-secret-XYZ",
						"secret": {
							"secretName": "aug-user-user0-secret-XYZ"` + // pragma: allowlist secret
				`}
					},
					"mounts": [{
						"name": "aug-user-user0-secret-XYZ",
						"mountPath": "/run/augment/serviceaccount/aug-user-user0"
					}]
				},
				{
					"name": "augi-release-readers",
					"volume": {
						"name": "augi-release-readers",
						"secret": {
							"optional": true,
							"secretName": "augi-release-readers"` + // pragma: allowlist secret
				`}
					},
					"mounts": [{
						"name": "augi-release-readers",
						"mountPath": "/run/augment/secrets/augi-release-readers"
					}]
				},
				{
					"name": "shmem",
					"volume": {
						"name": "shmem",
						"emptyDir": {
							"medium": "Memory"
						}
					},
					"mounts": [{
						"name": "shmem",
						"mountPath": "/dev/shm"
					}]
				},
				{
					"name": "custom0",
					"volume": {
						"name": "custom0",
						"persistentVolumeClaim": {
							"claimName": "custom0"
						}
					},
					"mounts": [{
						"name": "custom0",
						"mountPath": "/custom/path"
					}]
				},
				{
					"name": "custom1",
					"volume": {
						"name": "custom1",
						"persistentVolumeClaim": {
							"claimName": "custom1"
						}
					},
					"mounts": [{
						"name": "custom1",
						"mountPath": "/custom1/path1"
					}]
				}
			]`,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			tc.inSpec.Volumes.ExcludeClusterDefaults = true
			bld := newBuilder(t, tc.inSpec)
			bld.sasec = tc.sasec

			cm, err := bld.BuildConfigMap()
			if err != nil {
				t.Fatalf("err building ConfigMap: %v.", err)
			}
			sshHKSec, err := bld.BuildSSHHostKeysSecret()
			if err != nil {
				t.Fatalf("err building SSHHostKeysSecret: %v.", err)
			}
			homePVC, err := bld.BuildHomePVC()
			if err != nil {
				t.Fatalf("err building HomePVC: %v.", err)
			}

			gotVolMounts, gotErr := bld.buildVolMounts(cm, sshHKSec, homePVC, tc.isGPU)
			gotVolMounts = gotVolMounts.Filled()

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}

			wantVolMounts := clusters.VolMounts{}
			if err := json.Unmarshal([]byte(tc.want), &wantVolMounts); err != nil {
				t.Errorf("failed to unmarshal tc.want JSON: %v.", err)
			} else if diff := cmp.Diff(wantVolMounts, gotVolMounts); diff != "" {
				t.Errorf("VolMounts: -want +got:\n%s", diff)
			}
		})
	}
}

package devpod

import (
	"fmt"
	"os"
	"strings"

	_ "embed"

	"github.com/augmentcode/augment/research/infra/lib/augment"
)

//go:embed init.sh
var initEmbed string

const (
	InitFname   = "init.sh"
	InitSrcPath = "research/infra/lib/augment/devpod/" + InitFname
	DefaultSpec = InitFname + ",embed://"
)

// Init is an `init.sh` reader. It 1) enables testing of external calls (ReadFile);
// 2) Organizes into one struct, which is nice for godoc.
type Init struct {
	tReadFile func(string) (string, error)
}

// readFile enables swapping out os.ReadFile for testing. It also handles
// []byte -> string since we only need strings for our usecase.
func (i Init) readFile(fname string) (string, error) {
	if i.tReadFile != nil {
		return i.tReadFile(fname)
	}
	buf, err := os.ReadFile(fname)
	return string(buf), err
}

// FromSpec returns init.sh from a flexible command-line-friendly string spec. The format
// is a comman-separted list of zero or more sources. Valid sources:
//
//   - "": an empty string expands to the `DefaultSpec`, defined at the package level.
//   - ".": init.sh, relative to the working directory.
//   - "-": stdin.
//   - "repo://": init.sh, relative to an Augment repo on the local filesystem.
//   - "embed://": embedded init.sh from source (compile-time).
//   - <path>: any other path is read as-as from the local filesystem.
func (i Init) FromSpec(spec string) (string, error) {
	var err error
	for _, source := range strings.Split(spec, ",") {
		var buf string
		if buf, err = i.fromSource(source); err == nil {
			return buf, nil
		} else if os.IsNotExist(err) {
			continue
		} else {
			return "", err
		}
	}
	return "", err
}

func (i Init) fromSource(source string) (string, error) {
	switch {
	case source == "":
		return i.FromSpec(DefaultSpec)
	case source == ".":
		return i.FromPWD()
	case source == "-":
		return i.FromFilesystem("/dev/stdin")
	case source == "repo://":
		return i.FromLocalRepo()
	case strings.HasPrefix(source, "repo://"):
		return "", fmt.Errorf("%s invalid: repo:// must not be followed by any path", source)
	case source == "embed://":
		return i.FromEmbed(), nil
	case strings.HasPrefix(source, "embed://"):
		return "", fmt.Errorf("%s invalid: embed:// must not be followed by any path", source)
	default:
		return i.FromFilesystem(source)
	}
}

// FromEmbed returns init.sh as embedded compile-time.
func (i Init) FromEmbed() string {
	return initEmbed
}

// FromFilesystem returns init.sh as read from a given filesystem path.
func (i Init) FromFilesystem(fname string) (string, error) {
	return i.readFile(fname)
}

// FromPWD returns init.sh as read from $PWD/init.sh.
func (i Init) FromPWD() (string, error) {
	return i.FromFilesystem(InitFname)
}

// FromLocalRepo returns init.sh as read from the Augment Repo if found in the local filesystem.
func (i Init) FromLocalRepo() (string, error) {
	fname, err := augment.Dir(InitSrcPath)
	if err != nil {
		return "", err
	}
	return i.FromFilesystem(fname)
}

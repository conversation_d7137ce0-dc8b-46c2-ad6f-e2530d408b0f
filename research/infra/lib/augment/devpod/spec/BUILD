load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(default_visibility = ["//research/infra:internal"])

go_library(
    name = "spec",
    srcs = [
        "spec.go",
        "spec_deepcopy.go",
        "spec_flags.go",
        "spec_node.go",
        "spec_updates.go",
        "spec_validations.go",
    ],
    importpath = "github.com/augmentcode/augment/research/infra/lib/augment/devpod/spec",
    deps = [
        "//infra/lib/distribution",
        "//research/infra/cfg/clusters",
        "@com_github_goccy_go_yaml//:go-yaml",
        "@com_github_spf13_pflag//:pflag",
        "@io_k8s_api//core/v1:core",
        "@io_k8s_apimachinery//pkg/api/resource",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_apimachinery//pkg/util/intstr",
        "@io_k8s_client_go//applyconfigurations/core/v1:core",
    ],
)

go_test(
    name = "spec_test",
    srcs = [
        "spec_flags_test.go",
        "spec_node_test.go",
        "spec_test.go",
        "spec_updates_test.go",
        "spec_validations_test.go",
    ],
    embed = [":spec"],
    deps = [
        "//research/infra/cfg/clusters",
        "@com_github_google_go_cmp//cmp",
        "@com_github_google_go_cmp//cmp/cmpopts",
        "@com_github_spf13_pflag//:pflag",
        "@io_k8s_api//core/v1:core",
        "@io_k8s_apimachinery//pkg/api/resource",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_apimachinery//pkg/util/intstr",
        "@io_k8s_client_go//applyconfigurations/core/v1:core",
    ],
)

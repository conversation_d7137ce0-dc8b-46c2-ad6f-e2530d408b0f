package spec

import (
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	corev1 "k8s.io/api/core/v1"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"

	"github.com/augmentcode/augment/research/infra/cfg/clusters"
)

func TestNode(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		spec     DevPod
		wantErr  string
		wantTLA  map[string]string
		wantNode clusters.Node
	}{
		"empty": {
			spec:    DevPod{ClusterName: "_unittest_"},
			wantErr: "",
			wantTLA: map[string]string{},
			wantNode: clusters.Node{
				PrimaryAlias:          "cpu",
				Aliases:               []string{"cpu"},
				AllAliases:            []string{"cpu"},
				GPUs:                  0,
				CPUs:                  1,
				RAM:                   "4.48G",
				EphemeralStorage:      "20.26Gi",
				GPULimit:              0,
				CPULimit:              150,
				RAMLimit:              "674.09G",
				EphemeralStorageLimit: "3044Gi",
				K8sResources: corev1a.ResourceRequirements().
					WithLimits(corev1.ResourceList{
						corev1.ResourceCPU:              *quanT(t, "1"),
						corev1.ResourceMemory:           *quanT(t, "4.48G"),
						corev1.ResourceEphemeralStorage: *quanT(t, "20.26Gi"),
					}).
					WithRequests(corev1.ResourceList{
						corev1.ResourceCPU:              *quanT(t, "1"),
						corev1.ResourceMemory:           *quanT(t, "4.48G"),
						corev1.ResourceEphemeralStorage: *quanT(t, "20.26Gi"),
					}),
				K8sNodeSelectors: []*corev1a.NodeSelectorRequirementApplyConfiguration{
					corev1a.NodeSelectorRequirement().
						WithKey("cloud.google.com/gke-ephemeral-storage-local-ssd").
						WithOperator("In").
						WithValues("true"),
				},
				K8sTolerations: []*corev1a.TolerationApplyConfiguration{
					corev1a.Toleration().
						WithEffect(corev1.TaintEffectNoSchedule).
						WithKey("r.augmentcode.com/pool-type").
						WithValue("cpu"),
					corev1a.Toleration().
						WithEffect(corev1.TaintEffectPreferNoSchedule).
						WithKey("r.augmentcode.com/pool-type").
						WithValue("cpu"),
				},
				K8sAnnotations: map[string]string{
					"gke-gcsfuse/cpu-limit":                 "1",
					"gke-gcsfuse/cpu-request":               "1",
					"gke-gcsfuse/ephemeral-storage-limit":   "317.4Gi",
					"gke-gcsfuse/ephemeral-storage-request": "317.4Gi",
					"gke-gcsfuse/memory-limit":              "1Gi",
					"gke-gcsfuse/memory-request":            "1Gi",
					"gke-gcsfuse/volumes":                   "true",
				},
			},
		},
		"gpu-customized": {
			spec: DevPod{
				ClusterName: "_unittest_",
				Resources: Resources{
					NodeType:         "l4",
					GPU:              func() *int32 { i := int32(2); return &i }(),
					CPU:              quanT(t, "3"),
					RAM:              quanT(t, "1000G"),
					Disk:             quanT(t, "10Ti"),
					IgnoreLimits:     true,
					IgnoreValidation: true,
					PoolType:         "pool0",
				},
			},
			wantErr: "",
			wantTLA: map[string]string{
				"gpu_type":               "'l4'",
				"gpu_count":              "2",
				"cpu_count":              "3",
				"ram":                    "1000",
				"ephemeral_storage":      "10240",
				"aug_pool_type":          "'pool0'",
				"ignore_limits":          "true",
				"__ignore_validations__": "true",
			},
			wantNode: clusters.Node{
				PrimaryAlias:          "nvidia-l4",
				Aliases:               []string{"l4"},
				AdditionalAliases:     []string{"l4"},
				AllAliases:            []string{"nvidia-l4", "l4"},
				GPUs:                  2,
				CPUs:                  3,
				RAM:                   "1000G",
				EphemeralStorage:      "10240Gi",
				GPULimit:              4,
				CPULimit:              38,
				RAMLimit:              "174.02G",
				EphemeralStorageLimit: "345.5Gi",
				K8sResources: corev1a.ResourceRequirements().
					WithLimits(corev1.ResourceList{
						"nvidia.com/gpu":                *quanT(t, "2"),
						corev1.ResourceCPU:              *quanT(t, "3"),
						corev1.ResourceMemory:           *quanT(t, "1T"),
						corev1.ResourceEphemeralStorage: *quanT(t, "10240Gi"),
					}).
					WithRequests(corev1.ResourceList{
						"nvidia.com/gpu":                *quanT(t, "2"),
						corev1.ResourceCPU:              *quanT(t, "3"),
						corev1.ResourceMemory:           *quanT(t, "1T"),
						corev1.ResourceEphemeralStorage: *quanT(t, "10240Gi"),
					}),
				K8sNodeSelectors: []*corev1a.NodeSelectorRequirementApplyConfiguration{
					corev1a.NodeSelectorRequirement().
						WithKey("cloud.google.com/gke-accelerator").
						WithOperator("In").
						WithValues("nvidia-l4"),
					corev1a.NodeSelectorRequirement().
						WithKey("cloud.google.com/gke-ephemeral-storage-local-ssd").
						WithOperator("In").
						WithValues("true"),
				},
				K8sTolerations: []*corev1a.TolerationApplyConfiguration{
					corev1a.Toleration().
						WithEffect(corev1.TaintEffectNoSchedule).
						WithKey("r.augmentcode.com/pool-type").
						WithValue("pool0"),
					corev1a.Toleration().
						WithEffect(corev1.TaintEffectPreferNoSchedule).
						WithKey("r.augmentcode.com/pool-type").
						WithValue("pool0"),
				},
				K8sAnnotations: map[string]string{
					"gke-gcsfuse/cpu-limit":                 "4",
					"gke-gcsfuse/cpu-request":               "4",
					"gke-gcsfuse/ephemeral-storage-limit":   "370.5Gi",
					"gke-gcsfuse/ephemeral-storage-request": "370.5Gi",
					"gke-gcsfuse/memory-limit":              "4Gi",
					"gke-gcsfuse/memory-request":            "4Gi",
					"gke-gcsfuse/volumes":                   "true",
				},
			},
		},
		"gpu-default-num-custom-cpu": {
			spec: DevPod{
				ClusterName: "_unittest_",
				Resources: Resources{
					NodeType: "l4",
					GPU:      nil,
					CPU:      quanT(t, "3500m"),
				},
			},
			wantErr: "",
			wantTLA: map[string]string{
				"cpu_type":  "'l4'", // misnomer
				"cpu_count": "3.5",
			},
			wantNode: clusters.Node{
				PrimaryAlias:          "nvidia-l4",
				Aliases:               []string{"l4"},
				AdditionalAliases:     []string{"l4"},
				AllAliases:            []string{"nvidia-l4", "l4"},
				GPUs:                  1,
				CPUs:                  3,
				RAM:                   "43.5G",
				EphemeralStorage:      "86.37Gi",
				GPULimit:              4,
				CPULimit:              38,
				RAMLimit:              "174.02G",
				EphemeralStorageLimit: "345.5Gi",
				K8sResources: corev1a.ResourceRequirements().
					WithLimits(corev1.ResourceList{
						"nvidia.com/gpu":                *quanT(t, "1"),
						corev1.ResourceCPU:              *quanT(t, "3.5"),
						corev1.ResourceMemory:           *quanT(t, "43.5G"),
						corev1.ResourceEphemeralStorage: *quanT(t, "86.37Gi"),
					}).
					WithRequests(corev1.ResourceList{
						"nvidia.com/gpu":                *quanT(t, "1"),
						corev1.ResourceCPU:              *quanT(t, "3.5"),
						corev1.ResourceMemory:           *quanT(t, "43.5G"),
						corev1.ResourceEphemeralStorage: *quanT(t, "86.37Gi"),
					}),
				K8sNodeSelectors: []*corev1a.NodeSelectorRequirementApplyConfiguration{
					corev1a.NodeSelectorRequirement().
						WithKey("cloud.google.com/gke-accelerator").
						WithOperator("In").
						WithValues("nvidia-l4"),
					corev1a.NodeSelectorRequirement().
						WithKey("cloud.google.com/gke-ephemeral-storage-local-ssd").
						WithOperator("In").
						WithValues("true"),
				},
				K8sTolerations: nil,
				K8sAnnotations: map[string]string{
					"gke-gcsfuse/cpu-limit":                 "2",
					"gke-gcsfuse/cpu-request":               "2",
					"gke-gcsfuse/ephemeral-storage-limit":   "185.25Gi",
					"gke-gcsfuse/ephemeral-storage-request": "185.25Gi",
					"gke-gcsfuse/memory-limit":              "2Gi",
					"gke-gcsfuse/memory-request":            "2Gi",
					"gke-gcsfuse/volumes":                   "true",
				},
			},
		},
		"error": {
			spec: DevPod{
				ClusterName: "_unittest_",
				Resources: Resources{
					CPU:          quanT(t, "9999"),
					IgnoreLimits: false,
				},
			},
			wantErr: "cpus 9999 greater than cpu_limit",
			wantTLA: map[string]string{
				"cpu_count": "9999",
			},
		},
	}

	db, err := clusters.New()
	if err != nil {
		t.Fatalf("clusters.New(): %v.", err)
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			c, err := tc.spec.Cluster(db)
			if err != nil {
				t.Fatalf("DevPod.Cluster(): %v.", err)
			}

			gotTLA := tc.spec.Resources.NodeTLACode()
			if diff := cmp.Diff(tc.wantTLA, gotTLA, cmpopts.EquateEmpty()); diff != "" {
				t.Errorf("NodeTLACode(): -want +got:\n%s", diff)
			}

			gotNode, gotErr := tc.spec.Node(c)
			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}
			if diff := cmp.Diff(tc.wantNode, gotNode, cmpopts.EquateEmpty()); diff != "" {
				t.Errorf("Node(): -want +got:\n%s", diff)
			}
		})
	}
}

package spec

import (
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
)

func TestUpdates(t *testing.T) {
	t.<PERSON>llel()
	tests := map[string]struct {
		upd     func(*DevPod) error
		in      DevPod
		want    DevPod
		wantErr string
	}{
		"poweroff-from-default": {
			upd:  func(s *DevPod) error { s.SetPowerOff(); return nil },
			in:   DevPod{},
			want: DevPod{PowerOff_p: func() *bool { b := true; return &b }()},
		},
		"poweroff-from-on": {
			upd:  func(s *DevPod) error { s.SetPowerOff(); return nil },
			in:   DevPod{PowerOff_p: func() *bool { b := false; return &b }()},
			want: DevPod{PowerOff_p: func() *bool { b := true; return &b }()},
		},
		"poweroff-from-off": {
			upd:  func(s *DevPod) error { s.<PERSON>er<PERSON>ff(); return nil },
			in:   DevPod{PowerOff_p: func() *bool { b := true; return &b }()},
			want: DevPod{PowerOff_p: func() *bool { b := true; return &b }()},
		},
		"poweron-from-default": {
			upd:  func(s *DevPod) error { s.SetPowerOn(); return nil },
			in:   DevPod{},
			want: DevPod{},
		},
		"poweron-from-on": {
			upd:  func(s *DevPod) error { s.SetPowerOn(); return nil },
			in:   DevPod{PowerOff_p: func() *bool { b := false; return &b }()},
			want: DevPod{},
		},
		"poweron-from-off": {
			upd:  func(s *DevPod) error { s.SetPowerOn(); return nil },
			in:   DevPod{PowerOff_p: func() *bool { b := true; return &b }()},
			want: DevPod{},
		},

		"expose-ports/append/empty/parse-error": {
			upd:     func(s *DevPod) error { return s.ExposePorts(false, "__bad__") },
			in:      DevPod{},
			want:    DevPod{},
			wantErr: "__bad__",
		},
		"expose-ports/append/empty/empty": {
			upd:     func(s *DevPod) error { return s.ExposePorts(false) },
			in:      DevPod{},
			want:    DevPod{},
			wantErr: "no ports given",
		},
		"expose-ports/append/empty/mixed": {
			upd: func(s *DevPod) error { return s.ExposePorts(false, int32(42), "port80:80:8080", "443") },
			in:  DevPod{},
			want: DevPod{Cxn: Cxn{ExposedPorts: ExposedPorts{
				{Name: "", Port: 42, Target: 0},
				{Name: "port80", Port: 80, Target: 8080},
				{Name: "", Port: 443, Target: 0},
			}}},
		},
		"expose-ports/append/new-sorted": {
			upd: func(s *DevPod) error { return s.ExposePorts(false, int32(100), int32(300)) },
			in: DevPod{Cxn: Cxn{ExposedPorts: ExposedPorts{
				{Port: 200},
			}}},
			want: DevPod{Cxn: Cxn{ExposedPorts: ExposedPorts{
				{Port: 100},
				{Port: 200},
				{Port: 300},
			}}},
		},
		"expose-ports/append/overwrite": {
			upd: func(s *DevPod) error { return s.ExposePorts(false, int32(200), int32(300)) },
			in: DevPod{Cxn: Cxn{ExposedPorts: ExposedPorts{
				{Port: 100, Target: 101},
				{Port: 200, Target: 202},
			}}},
			want: DevPod{Cxn: Cxn{ExposedPorts: ExposedPorts{
				{Port: 100, Target: 101},
				{Port: 200},
				{Port: 300},
			}}},
		},
		"expose-ports/append/duplicate-assertion": {
			upd: func(s *DevPod) error { return s.ExposePorts(false, int32(100), int32(300)) },
			in: DevPod{Cxn: Cxn{ExposedPorts: ExposedPorts{
				{Port: 100, Target: 101},
				{Port: 100, Target: 102},
			}}},
			want: DevPod{Cxn: Cxn{ExposedPorts: ExposedPorts{
				{Port: 100, Target: 101},
				{Port: 100, Target: 102},
			}}},
			wantErr: "assertion error: duplicate port 100",
		},
		"expose-ports/set": {
			upd: func(s *DevPod) error { return s.ExposePorts(true, int32(100), int32(300)) },
			in: DevPod{Cxn: Cxn{ExposedPorts: ExposedPorts{
				{Port: 100, Target: 101},
				{Port: 100, Target: 102},
			}}},
			want: DevPod{Cxn: Cxn{ExposedPorts: ExposedPorts{
				{Port: 100},
				{Port: 300},
			}}},
		},

		"hide-ports/all": {
			upd: func(s *DevPod) error { s.HidePorts(); return nil },
			in: DevPod{Cxn: Cxn{ExposedPorts: ExposedPorts{
				{Port: 100, Target: 101},
				{Port: 200, Target: 202},
			}}},
			want: DevPod{},
		},
		"hide-ports/each": {
			upd: func(s *DevPod) error { s.HidePorts(100, 200); return nil },
			in: DevPod{Cxn: Cxn{ExposedPorts: ExposedPorts{
				{Port: 100, Target: 101},
				{Port: 200, Target: 202},
			}}},
			want: DevPod{},
		},
		"hide-ports/subset": {
			upd: func(s *DevPod) error { s.HidePorts(100); return nil },
			in: DevPod{Cxn: Cxn{ExposedPorts: ExposedPorts{
				{Port: 100, Target: 101},
				{Port: 200, Target: 202},
			}}},
			want: DevPod{Cxn: Cxn{ExposedPorts: ExposedPorts{
				{Port: 200, Target: 202},
			}}},
		},
		"hide-ports/subset-extra-ok": {
			upd: func(s *DevPod) error { s.HidePorts(100, 300); return nil },
			in: DevPod{Cxn: Cxn{ExposedPorts: ExposedPorts{
				{Port: 100, Target: 101},
				{Port: 200, Target: 202},
			}}},
			want: DevPod{Cxn: Cxn{ExposedPorts: ExposedPorts{
				{Port: 200, Target: 202},
			}}},
		},

		"grow-home/default/empty-arg": {
			upd:     func(s *DevPod) error { return s.GrowHome("") },
			in:      DevPod{Home: Home{Size_p: nil}},
			want:    DevPod{Home: Home{Size_p: nil}},
			wantErr: "quantities must match the regular expression",
		},
		"grow-home/default/empty-add": {
			upd:     func(s *DevPod) error { return s.GrowHome("+") },
			in:      DevPod{Home: Home{Size_p: nil}},
			want:    DevPod{Home: Home{Size_p: nil}},
			wantErr: "quantities must match the regular expression",
		},
		"grow-home/default/parse-error": {
			upd:     func(s *DevPod) error { return s.GrowHome("+_bad_") },
			in:      DevPod{Home: Home{Size_p: nil}},
			want:    DevPod{Home: Home{Size_p: nil}},
			wantErr: "quantities must match the regular expression",
		},
		"grow-home/default/default": {
			upd:     func(s *DevPod) error { return s.GrowHome("256Gi") },
			in:      DevPod{Home: Home{Size_p: nil}},
			want:    DevPod{Home: Home{Size_p: nil}},
			wantErr: "new Home Size (256Gi) is not an increase",
		},
		"grow-home/default/lower": {
			upd:     func(s *DevPod) error { return s.GrowHome("32Gi") },
			in:      DevPod{Home: Home{Size_p: nil}},
			want:    DevPod{Home: Home{Size_p: nil}},
			wantErr: "new Home Size (32Gi) is not an increase",
		},
		"grow-home/default/no-increase": {
			upd:     func(s *DevPod) error { return s.GrowHome("+0") },
			in:      DevPod{Home: Home{Size_p: nil}},
			want:    DevPod{Home: Home{Size_p: nil}},
			wantErr: "new Home Size (256Gi) is not an increase",
		},
		"grow-home/default/abs-increase-default-units": {
			upd:  func(s *DevPod) error { return s.GrowHome("320") },
			in:   DevPod{Home: Home{Size_p: nil}},
			want: DevPod{Home: Home{Size_p: quanT(t, "320Gi")}},
		},
		"grow-home/default/abs-increase-given-units": {
			upd:  func(s *DevPod) error { return s.GrowHome("32Ti") },
			in:   DevPod{Home: Home{Size_p: nil}},
			want: DevPod{Home: Home{Size_p: quanT(t, "32Ti")}},
		},
		"grow-home/default/rel-increase-default-units": {
			upd:  func(s *DevPod) error { return s.GrowHome("+64") },
			in:   DevPod{Home: Home{Size_p: nil}},
			want: DevPod{Home: Home{Size_p: quanT(t, "320Gi")}},
		},
		"grow-home/default/rel-increase-given": {
			upd:  func(s *DevPod) error { return s.GrowHome("+64Gi") },
			in:   DevPod{Home: Home{Size_p: nil}},
			want: DevPod{Home: Home{Size_p: quanT(t, "320Gi")}},
		},
		"grow-home/non-default/abs-increase": {
			upd:  func(s *DevPod) error { return s.GrowHome("2Ti") },
			in:   DevPod{Home: Home{Size_p: quanT(t, "1Ti")}},
			want: DevPod{Home: Home{Size_p: quanT(t, "2Ti")}},
		},
		"grow-home/non-default/rel-increase": {
			upd:  func(s *DevPod) error { return s.GrowHome("+1") },
			in:   DevPod{Home: Home{Size_p: quanT(t, "1Ti")}},
			want: DevPod{Home: Home{Size_p: quanT(t, "1025Gi")}},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			gotDevPod := tc.in
			gotErr := tc.upd(&gotDevPod)
			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}
			if diff := cmp.Diff(tc.want, gotDevPod); diff != "" {
				t.Errorf("spec: -want +got:\n%s.", diff)
			}
		})
	}
}

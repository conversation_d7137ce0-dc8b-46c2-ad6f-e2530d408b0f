package spec

import (
	"fmt"
	"sort"
	"strconv"
	"strings"

	"k8s.io/apimachinery/pkg/api/resource"
)

func (s *DevPod) SetPowerOff() {
	poweroff := true
	s.PowerOff_p = &poweroff
}

func (s *DevPod) SetPowerOn() {
	s.PowerOff_p = nil
}

// ExposePorts sets or appends exposed ports. If a port is already exposed, its
// name and target port are updated based on the given args. The resulting
// ports are sorted by port number. With `set=true` the list is replaced with the
// given ports instead of appended to.
func (s *DevPod) ExposePorts(set bool, ports ...any) error {
	/// Guard against empty list, we want to assume the end result won't be empty.

	if len(ports) == 0 {
		return fmt.Errorf("ExposePorts(): no ports given")
	}

	/// Parse given ports (int32 or NAME:PORT:TARGET string spec).

	eps, err := ExposedPortsFrom(ports...)
	if err != nil {
		return err
	}

	/// Sort results for stability.

	defer func() {
		sort.Slice(s.Cxn.ExposedPorts, func(i, j int) bool {
			return s.Cxn.ExposedPorts[i].Port < s.Cxn.ExposedPorts[j].Port
		})
	}()

	/// If setting (not appending), simply update the list.

	if set {
		s.Cxn.ExposedPorts = eps
		return nil
	}

	/// Otherwise, index the existing list by port number.

	curByPort := map[int32]*ExposedPort{}
	for i := range s.Cxn.ExposedPorts {
		ep := &s.Cxn.ExposedPorts[i]
		if _, ok := curByPort[ep.Port]; ok {
			return fmt.Errorf("assertion error: duplicate port %d", ep.Port)
		}
		curByPort[ep.Port] = ep
	}

	/// Append new ports or update existing.

	for _, ep := range eps {
		if cur := curByPort[ep.Port]; cur == nil {
			s.Cxn.ExposedPorts = append(s.Cxn.ExposedPorts, ep)
			curByPort[ep.Port] = &s.Cxn.ExposedPorts[len(s.Cxn.ExposedPorts)-1]
		} else {
			*cur = ep
		}
	}

	return nil
}

// HidePorts removes ports from the ExposedPorts list. As a special case, if no
// ports are given then ALL ports are removed.
func (s *DevPod) HidePorts(ports ...int32) {
	/// With no args, hide all ports.
	if len(ports) == 0 {
		s.Cxn.ExposedPorts = nil
		return
	}
	/// Convert input to set.
	hidePorts := map[int32]bool{}
	for _, p := range ports {
		hidePorts[p] = true
	}
	/// Replace with filtered list.
	fltrd := ExposedPorts{}
	for _, ep := range s.Cxn.ExposedPorts {
		if !hidePorts[ep.Port] {
			fltrd = append(fltrd, ep)
		}
	}
	if len(fltrd) == 0 {
		fltrd = nil
	}
	s.Cxn.ExposedPorts = fltrd
}

// GrowHome increases the size of the Home PVC. It is an error if the DevPod does not use a Home PVC
// or if the new size would not be an increase. The size may begin with a "+" for a relative increase,
// otherwise it's an absolute new size. If the size is plain number, the units default to "Gi", otherwise
// any scale is supported.
func (s *DevPod) GrowHome(arg string) error {
	// Strip any leading `+` and remember it in `add`.
	arg, add := strings.CutPrefix(arg, "+")

	// Default scale to Gi if not otherwise specified.
	if _, err := strconv.ParseFloat(arg, 64); err == nil {
		arg += "Gi"
	}

	// Parse new quantity.
	q, err := resource.ParseQuantity(arg)
	if err != nil {
		return fmt.Errorf("%s: %w", arg, err)
	}

	// The current size may be explicit or the default (256 GiB).
	cur := s.Home.Size()

	// Handle relative increase.
	if add {
		q.Add(cur)
	}

	// Ensure increase.
	if q.Cmp(cur) <= 0 {
		return fmt.Errorf("new Home Size (%s) is not an increase over current Home Size (%s)", q.String(), cur.String())
	}

	// Update.
	s.Home.Size_p = &q
	return nil
}

package spec

import (
	"strconv"

	"github.com/augmentcode/augment/research/infra/cfg/clusters"
)

func (s DevPod) Cluster(db *clusters.Clusters) (clusters.Cluster, error) {
	if db == nil {
		var err error
		if db, err = clusters.New(); err != nil {
			return clusters.Cluster{}, err
		}
	}
	return db.Cluster(s.ClusterName)
}

func (s DevPod) Node(c clusters.Cluster) (clusters.Node, error) {
	tla := s.Resources.NodeTLACode()
	return c.NodeFromTLA(tla)
}

func (s Resources) NodeTLACode() map[string]string {
	tla := map[string]string{}

	if i := s.GPU; i != nil {
		tla["gpu_count"] = strconv.FormatInt(int64(*i), 10)
		if nt := s.NodeType; nt != "" {
			tla["gpu_type"] = "'" + nt + "'"
		}
	} else {
		if nt := s.NodeType; nt != "" {
			tla["cpu_type"] = "'" + nt + "'"
		}
	}
	if q := s.CPU; q != nil {
		// CPUs can be expressed down to millicpu
		tla["cpu_count"] = strconv.FormatFloat(q.AsApproximateFloat64(), 'f', -1, 64)
	}
	if q := s.RAM; q != nil {
		// RAM is expected by the jsonnet library in G
		tla["ram"] = strconv.FormatFloat(q.AsApproximateFloat64()/1000/1000/1000, 'f', -1, 64)
	}
	if q := s.Disk; q != nil {
		// Disk is expected by the jsonnet library in Gi
		tla["ephemeral_storage"] = strconv.FormatFloat(q.AsApproximateFloat64()/1024/1024/1024, 'f', -1, 64)
	}

	if s.IgnoreLimits {
		tla["ignore_limits"] = "true"
	}
	if s.IgnoreValidation {
		tla["__ignore_validations__"] = "true"
	}

	if pool := s.PoolType; pool != "" {
		tla["aug_pool_type"] = "'" + pool + "'"
	}

	return tla
}

package spec

import (
	"errors"
	"fmt"
	"strings"

	"k8s.io/apimachinery/pkg/api/resource"
)

func (s DevPod) Validate() error {
	return errors.Join(s.Validations()...)
}

var (
	ErrUserNameEmpty    = errors.New("UserName must be set")
	ErrNoUserNamePrefix = errors.New("DevPodName must start with ${UserName}-")
	ErrNoSuffix         = errors.New("DevPodName must have suffix following ${UserName}-")
	ErrHomeTooBig       = fmt.Errorf("HomeSize exceeds max of %s", HomeMax.String())
	ErrSnapshotNotOwned = errors.New("Snapshots must belong to the same user")

	HomeMax = resource.NewQuantity(512*1024*1024*1024, resource.BinarySI)
)

func (s DevPod) Validations() []error {
	errs := []error{}
	err := func(f string, a ...any) {
		errs = append(errs, fmt.E<PERSON><PERSON>(f, a...))
	}

	if s.UserName == "" {
		err("%w", ErrUserNameEmpty)
	}

	if !strings.HasPrefix(s.DevPodName, s.UserName+"-") {
		err("%s: %w (%s)", s.DevPodName, ErrNoUserNamePrefix, s.UserName)
	}

	if d := len(s.DevPodName) - len(s.UserName+"-"); d < 1 {
		err("%s: %w (%s)", s.DevPodName, ErrNoSuffix, s.UserName)
	}

	// Try to prevent home volumes greater than .5Ti. This is best-effort, we can't currently prevent
	// people from editing the PVC directly.
	if q := s.Home.Size(); q.Cmp(*HomeMax) > 0 {
		err("%s: %w", q.String(), ErrHomeTooBig)
	}

	for _, ss := range s.Volumes.Snapshots {
		if !strings.HasPrefix(ss, s.UserName+"-") {
			err("%s: %w (%s)", ss, ErrSnapshotNotOwned, s.UserName)
		}
	}

	return errs
}

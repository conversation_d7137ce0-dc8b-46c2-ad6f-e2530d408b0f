package spec

import (
	"errors"
	"testing"
)

func TestValidate(t *testing.T) {
	t.<PERSON>lle<PERSON>()
	tests := map[string]struct {
		spec DevPod
		want []error
		not  []error
	}{
		"empty": {
			spec: DevPod{},
			want: []error{ErrUserNameEmpty, ErrNoUserNamePrefix, ErrNoSuffix},
			not:  []error{},
		},
		"okay": {
			spec: DevPod{UserName: "user0", DevPodName: "user0-name0"},
			want: []error{},
			not:  []error{ErrUserNameEmpty, ErrNoUserNamePrefix, ErrNoSuffix, ErrSnapshotNotOwned},
		},
		"no-username": {
			spec: DevPod{DevPodName: "-name0"},
			want: []error{ErrUserNameEmpty},
			not:  []error{ErrNoUserNamePrefix, ErrNoSuffix},
		},
		"username-no-hyphen": {
			spec: DevPod{UserName: "user0", DevPodName: "user0name0"},
			want: []error{ErrNoUserNamePrefix},
			not:  []error{ErrUserNameEmpty, ErrNoSuffix},
		},
		"home-toobig": {
			spec: DevPod{UserName: "user0", DevPodName: "user0-name0", Home: Home{Size_p: quanT(t, "1Ti")}},
			want: []error{ErrHomeTooBig},
		},
		"foreign-snapshot": {
			spec: DevPod{UserName: "user0", DevPodName: "user0-name0", Volumes: Volumes{Snapshots: []string{"user1-snap"}}},
			want: []error{ErrSnapshotNotOwned},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			gotErr := tc.spec.Validate()
			if (gotErr == nil) != (len(tc.want) == 0) {
				t.Errorf("gotErr: wanted %d error(s) but got: %v.", len(tc.want), gotErr)
			}
			for _, wantErr := range tc.want {
				if !errors.Is(gotErr, wantErr) {
					t.Errorf("Error does not include: %v", wantErr)
				}
			}
			for _, wantNotErr := range tc.not {
				if errors.Is(gotErr, wantNotErr) {
					t.Errorf("Error includes: %v", wantNotErr)
				}
			}
		})
	}
}

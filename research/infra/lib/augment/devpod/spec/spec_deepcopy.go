package spec

func (in *DevPod) DeepCopyInto(out *DevPod) {
	*out = *in

	/// Misc Fields

	if p := in.PowerOff_p; p != nil {
		out.PowerOff_p = func() *bool { v := *p; return &v }()
	}

	/// Resources

	if p := in.Resources.GPU; p != nil {
		out.Resources.GPU = func() *int32 { v := *p; return &v }()
	}
	if p := in.Resources.CPU; p != nil {
		p.DeepCopyInto(out.Resources.CPU)
	}
	if p := in.Resources.RAM; p != nil {
		p.DeepCopyInto(out.Resources.RAM)
	}
	if p := in.Resources.Disk; p != nil {
		p.DeepCopyInto(out.Resources.Disk)
	}
	for i, t := range in.Resources.Tolerations {
		t.DeepCopyInto(&out.Resources.Tolerations[i])
	}

	/// Cxn

	if p := in.Cxn.PublicIP_p; p != nil {
		out.Cxn.PublicIP_p = func() *bool { v := *p; return &v }()
	}

	/// Home
	if p := in.Home.Size_p; p != nil {
		p.DeepCopyInto(out.Home.Size_p)
	}

	/// Volumes
	out.Volumes.VolMounts = in.Volumes.VolMounts.DeepCopy()
}

func (in *DevPod) DeepCopy() *DevPod {
	if in == nil {
		return nil
	}
	out := &DevPod{}
	in.DeepCopyInto(out)
	return out
}

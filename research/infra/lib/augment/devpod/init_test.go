package devpod

import (
	"fmt"
	"os"
	"strings"
	"testing"
)

func TestInit(t *testing.T) {
	// Shorten the embeded init for simpler testing output.
	const embed = "<embed>"
	initEmbed = embed

	type fReadFile struct {
		wName string
		fBuf  string
		fErr  error
	}

	tests := map[string]struct {
		spec     string
		wantInit string
		wantErr  string

		fReadFiles []fReadFile
	}{
		"default": {
			spec:     "",
			wantInit: embed,
			wantErr:  "",
			fReadFiles: []fReadFile{
				{InitFname, "", os.ErrNotExist},
			},
		},
		"default-with-local": {
			spec:     "",
			wantInit: "<init.sh>",
			wantErr:  "",
			fReadFiles: []fReadFile{
				{InitFname, "<init.sh>", nil},
			},
		},
		"default-with-err": {
			spec:     "",
			wantInit: "",
			wantErr:  "<err>",
			fReadFiles: []fReadFile{
				{InitFname, "", fmt.Errorf("<err>")},
			},
		},
		"pwd": {
			spec:     ".",
			wantInit: "<pwd>",
			wantErr:  "",
			fReadFiles: []fReadFile{
				{InitFname, "<pwd>", nil},
			},
		},
		"stdin": {
			spec:     "-",
			wantInit: "<stdin>",
			wantErr:  "",
			fReadFiles: []fReadFile{
				{"/dev/stdin", "<stdin>", nil},
			},
		},
		"repo-err": {
			spec:     "repo://extra",
			wantInit: "",
			wantErr:  "invalid: repo:// must not be followed",
		},
		"embed": {
			spec:     "embed://",
			wantInit: "<embed>",
			wantErr:  "",
		},
		"embed-err": {
			spec:     "embed://extra",
			wantInit: "",
			wantErr:  "invalid: embed:// must not be followed",
		},
		"file": {
			spec:     "/path/to/file",
			wantInit: "<file>",
			wantErr:  "",
			fReadFiles: []fReadFile{
				{"/path/to/file", "<file>", nil},
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			i := Init{
				tReadFile: func(fname string) (string, error) {
					if len(tc.fReadFiles) == 0 {
						t.Fatalf("No more fake readfile configs.")
					}
					cfg := tc.fReadFiles[0]
					tc.fReadFiles = tc.fReadFiles[1:]

					if got, want := fname, cfg.wName; got != want {
						t.Errorf("tReadFile: got %v, want %v.", got, want)
					}
					return cfg.fBuf, cfg.fErr
				},
			}

			gotInit, gotErr := i.FromSpec(tc.spec)

			if l := len(tc.fReadFiles); l > 0 {
				t.Errorf("%d extra fake readfile configs.", l)
			}

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}

			if got, want := gotInit, tc.wantInit; got != want {
				t.Errorf("init: got %v, want %v.", got, want)
			}
		})
	}
}

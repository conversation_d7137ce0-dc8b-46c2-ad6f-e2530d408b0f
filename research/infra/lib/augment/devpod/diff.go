package devpod

import (
	"context"
	"fmt"
	"os"
	"os/exec"
)

func DpDiff(ctx context.Context, left, right *Set) error {
	return DpDiffCmd(ctx, left, right, "diff", "-u")
}

func DpVimDiff(ctx context.Context, left, right *Set) error {
	return DpDiffCmd(ctx, left, right, "vimdiff", "-R")
}

func DpDiffCmd(ctx context.Context, left, right *Set, cmdname string, args ...string) error {
	lyaml, err := left.YAML()
	if err != nil {
		return err
	}
	ryaml, err := right.YAML()
	if err != nil {
		return err
	}
	name := func() string {
		if left != nil {
			return left.Name()
		} else if right != nil {
			return right.Name()
		} else {
			return "_noname_"
		}
	}()
	lname := fmt.Sprintf("augi-devpod-diff.*.%s.yaml", name)
	return DpDiffStrings(ctx, cmdname, lyaml, lname, ryaml, args...)
}

func DpDiffStrings(ctx context.Context, cmdname, left, lname, right string, args ...string) error {
	lf, err := os.CreateTemp("", lname)
	if err != nil {
		return err
	} else if _, err := lf.WriteString(left); err != nil {
		lf.Close()
		return err
	} else if err := lf.Close(); err != nil {
		return err
	}

	rname := fmt.Sprintf("%s.new", lf.Name())
	if rf, err := os.Create(rname); err != nil {
		return err
	} else if _, err := rf.WriteString(right); err != nil {
		rf.Close()
		return err
	} else if err := rf.Close(); err != nil {
		return err
	}

	lfname := lf.Name()

	cmd := exec.CommandContext(ctx, cmdname, append(args, lfname, rname)...)
	cmd.Stdin, cmd.Stdout, cmd.Stderr = os.Stdin, os.Stdout, os.Stderr
	return cmd.Run()
}

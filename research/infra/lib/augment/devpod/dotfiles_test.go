package devpod

import (
	"archive/tar"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"io"
	"io/fs"
	"os"
	"os/user"
	"strings"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
)

// TODO add tests for dot-ls, dot-update
func TestDotfiles(t *testing.T) {
	tests := map[string]struct {
		defaults []string
		inExtra  []string
		inDeref  bool
		wantErr  string
		wantTar  []string

		fUserHome string
		fUserErr  error
		fEnts     map[string]fi
	}{
		"user-error": {
			fUserErr: fmt.Errorf("<user-error>"),
			wantErr:  "<user-error>",
		},
		"default": {
			fUserHome: "/home/<USER>",
			fUserErr:  nil,
			defaults: []string{
				"a", // file
				"b", // not exists
				"c", // dir
			},
			fEnts: map[string]fi{
				"/home/<USER>/a":   {name: "a", buf: "AAA"},
				"/home/<USER>/c":   {name: "c", children: []string{"/home/<USER>/c/d", "/home/<USER>/c/e"}},
				"/home/<USER>/c/d": {name: "d", buf: "DDD"},
				"/home/<USER>/c/e": {name: "e", buf: "EEE"},
			},
			wantTar: []string{
				"R a AAA",
				"R c/d DDD",
				"R c/e EEE",
			},
		},
		"default-err": {
			fUserHome: "/home/<USER>",
			fUserErr:  nil,
			defaults: []string{
				"a", // file
				"b", // not exists
				"c", // dir
			},
			fEnts: map[string]fi{
				"/home/<USER>/a":   {name: "a", buf: "AAA", err: fmt.Errorf("<a err>")},
				"/home/<USER>/c":   {name: "c", children: []string{"/home/<USER>/c/d", "/home/<USER>/c/e"}},
				"/home/<USER>/c/d": {name: "d", buf: "DDD"},
				"/home/<USER>/c/e": {name: "e", buf: "EEE"},
			},
			wantErr: "<a err>",
		},
		"default-with-extra": {
			fUserHome: "/home/<USER>",
			fUserErr:  nil,
			defaults: []string{
				"a", // file
				"b", // not exists
				"c", // dir
			},
			inExtra: []string{".f/g"},
			fEnts: map[string]fi{
				"/home/<USER>/a":    {name: "a", buf: "AAA"},
				"/home/<USER>/c":    {name: "c", children: []string{"/home/<USER>/c/d", "/home/<USER>/c/e"}},
				"/home/<USER>/c/d":  {name: "d", buf: "DDD"},
				"/home/<USER>/c/e":  {name: "e", buf: "EEE"},
				"/home/<USER>/.f":   {name: ".f", children: []string{"/home/<USER>/.f/g"}},
				"/home/<USER>/.f/g": {name: "g", buf: "GGG"},
			},
			wantTar: []string{
				"R a AAA",
				"R c/d DDD",
				"R c/e EEE",
				"R .f/g GGG",
			},
		},
		"default-with-extra-no-exists": {
			fUserHome: "/home/<USER>",
			fUserErr:  nil,
			defaults: []string{
				"a", // file
				"b", // not exists
				"c", // dir
			},
			inExtra: []string{".f/g"},
			fEnts: map[string]fi{
				"/home/<USER>/a":   {name: "a", buf: "AAA"},
				"/home/<USER>/c":   {name: "c", children: []string{"/home/<USER>/c/d", "/home/<USER>/c/e"}},
				"/home/<USER>/c/d": {name: "d", buf: "DDD"},
				"/home/<USER>/c/e": {name: "e", buf: "EEE"},
				"/home/<USER>/.f":  {name: ".f", children: []string{}},
			},
			wantErr: "file does not exist",
		},
		"default-with-symlinks-noderef": {
			inDeref:   false,
			fUserHome: "/home/<USER>",
			fUserErr:  nil,
			defaults: []string{
				"a", // file
				"b", // not exists
				"c", // dir
				"f", // symlink (to nowhere)
				"g", // symlink (to file)
				"h", // symlink (to dir)
				"i", // symlink (to symlink)
			},
			fEnts: map[string]fi{
				"/home/<USER>/a":   {name: "a", buf: "AAA"},
				"/home/<USER>/c":   {name: "c", children: []string{"/home/<USER>/c/d", "/home/<USER>/c/e"}},
				"/home/<USER>/c/d": {name: "d", buf: "DDD"},
				"/home/<USER>/c/e": {name: "e", buf: "EEE"},
				"/home/<USER>/f":   {name: "f", buf: "../nonexist/path/ignored", link: true},
				"/home/<USER>/g":   {name: "g", buf: "/home/<USER>/a", link: true},
				"/home/<USER>/h":   {name: "h", buf: "/home/<USER>/c", link: true},
				"/home/<USER>/i":   {name: "i", buf: "/home/<USER>/g", link: true},
			},
			wantTar: []string{
				"R a AAA",
				"R c/d DDD",
				"R c/e EEE",
				"L f ../nonexist/path/ignored",
				"L g /home/<USER>/a",
				"L h /home/<USER>/c",
				"L i /home/<USER>/g",
			},
		},
		"default-with-symlinks-deref": {
			inDeref:   true,
			fUserHome: "/home/<USER>",
			fUserErr:  nil,
			defaults: []string{
				"a", // file
				"b", // not exists
				"c", // dir
				"f", // symlink (to nowhere)
				"g", // symlink (to file)
				"h", // symlink (to dir)
				"i", // symlink (to symlink)
			},
			fEnts: map[string]fi{
				"/home/<USER>/a":   {name: "a", buf: "AAA"},
				"/home/<USER>/c":   {name: "c", children: []string{"/home/<USER>/c/d", "/home/<USER>/c/e"}},
				"/home/<USER>/c/d": {name: "d", buf: "DDD"},
				"/home/<USER>/c/e": {name: "e", buf: "EEE"},
				"/home/<USER>/f":   {name: "f", buf: "../nonexist/path/ignored", link: true},
				"/home/<USER>/g":   {name: "g", buf: "/home/<USER>/a", link: true},
				"/home/<USER>/h":   {name: "h", buf: "/home/<USER>/c", link: true},
				"/home/<USER>/i":   {name: "i", buf: "/home/<USER>/g", link: true},

				// Needed to work around limited testing environment (or limited FS interface used).
				"/home/<USER>/h/d": {name: "d", buf: "/home/<USER>/c/d", link: true},
				"/home/<USER>/h/e": {name: "e", buf: "/home/<USER>/c/e", link: true},
			},
			wantTar: []string{
				"R a AAA",
				"R c/d DDD",
				"R c/e EEE",
				"R g AAA",
				"R h/d DDD",
				"R h/e EEE",
				"R i AAA",
			},
		},
	}

	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			df := NewDotfiles()
			df.deref = tc.inDeref
			df.tUser = func() (*user.User, error) {
				return &user.User{HomeDir: tc.fUserHome}, tc.fUserErr
			}
			if tc.defaults != nil {
				df.defaults = tc.defaults
			}
			df.tLstat = func(fname string) (os.FileInfo, error) {
				if fi, ok := tc.fEnts[fname]; !ok {
					return nil, os.ErrNotExist
				} else if fi.err != nil {
					return nil, fmt.Errorf("Lstat(%s): %w", fname, fi.err)
				} else {
					return fi, nil
				}
			}
			df.tStat = func(fname string) (os.FileInfo, error) {
				if fi, ok := tc.fEnts[fname]; !ok {
					return nil, os.ErrNotExist
				} else if fi.err != nil {
					return nil, fmt.Errorf("Stat(%s): %w", fname, fi.err)
				} else if fi.link {
					return df.tStat(fi.buf)
				} else {
					return fi, nil
				}
			}
			df.tReadFile = func(fname string) ([]byte, error) {
				if fi, ok := tc.fEnts[fname]; !ok {
					return nil, os.ErrNotExist
				} else if fi.err != nil {
					return nil, fmt.Errorf("ReadFile(%s): %w", fname, fi.err)
				} else if fi.link {
					return df.tReadFile(fi.buf)
				} else {
					return []byte(fi.buf), nil
				}
			}
			df.tReadDir = func(fname string) ([]os.DirEntry, error) {
				if fi, ok := tc.fEnts[fname]; !ok {
					return nil, os.ErrNotExist
				} else if fi.err != nil {
					return nil, fmt.Errorf("ReadDir(%s): %w", fname, fi.err)
				} else if fi.link {
					return df.tReadDir(fi.buf)
				} else {
					ret := []os.DirEntry{}
					for _, cname := range fi.children {
						if ci, err := df.tLstat(cname); err != nil {
							return nil, err
						} else {
							ret = append(ret, fs.FileInfoToDirEntry(ci))
						}
					}
					return ret, nil
				}
			}
			df.tReadlink = func(fname string) (string, error) {
				if fi, ok := tc.fEnts[fname]; !ok {
					return "", os.ErrNotExist
				} else if fi.err != nil {
					return "", fmt.Errorf("Readlink(%s): %w", fname, fi.err)
				} else if !fi.link {
					return "", fmt.Errorf("Readlink(%s): not a symlink", fname)
				} else {
					return fi.buf, nil
				}
			}

			gotStr, gotErr := df.BuildAndClose(tc.inExtra...)

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}

			// Now, build a summary of the contents, iff there weren't errors.
			if gotErr != nil {
				return
			}

			gotTar := []string{}
			b64r := base64.NewDecoder(base64.StdEncoding, strings.NewReader(gotStr))
			gzr, err := gzip.NewReader(b64r)
			if err != nil {
				t.Fatalf("gzip NewReader(): %v.", err)
			}
			tarr := tar.NewReader(gzr)
			for {
				hdr, err := tarr.Next()
				if err == io.EOF {
					break
				}
				if err != nil {
					t.Fatalf("tar.Next(): %v.", err)
				}
				switch hdr.Typeflag {
				case tar.TypeReg:
					buf, err := io.ReadAll(tarr)
					if err != nil {
						t.Fatalf("tar.Read(): %v.", err)
					}
					gotTar = append(gotTar, fmt.Sprintf("R %s %s", hdr.Name, string(buf)))
				case tar.TypeSymlink:
					gotTar = append(gotTar, fmt.Sprintf("L %s %s", hdr.Name, hdr.Linkname))
				case tar.TypeDir:
					gotTar = append(gotTar, fmt.Sprintf("D %s", hdr.Name))
				default:
					gotTar = append(gotTar, fmt.Sprintf("? %s", hdr.Name))
				}
			}
			if diff := cmp.Diff(tc.wantTar, gotTar); diff != "" {
				t.Errorf("tar: -want +got:\n%s", diff)
			}
		})
	}
}

////////////////////////////////////////////////////////////////////////////////
// FileInfo impl for testing
//

type fi struct {
	name     string
	children []string
	link     bool

	// IO Ops
	buf string
	err error
}

func (fi fi) Name() string       { return fi.name }
func (fi fi) IsDir() bool        { return len(fi.children) > 0 }
func (fi fi) Size() int64        { return int64(len(fi.buf)) }
func (fi fi) ModTime() time.Time { return time.Time{} }
func (fi fi) Sys() any           { return nil }
func (fi fi) Mode() fs.FileMode {
	if fi.link {
		return fs.ModeSymlink
	}
	if fi.IsDir() {
		return fs.ModeDir
	}
	return fs.FileMode(0)
}

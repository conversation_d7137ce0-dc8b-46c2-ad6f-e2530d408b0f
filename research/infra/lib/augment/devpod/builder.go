package devpod

import (
	"fmt"
	"strconv"
	"strings"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	appsv1a "k8s.io/client-go/applyconfigurations/apps/v1"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"
	metav1a "k8s.io/client-go/applyconfigurations/meta/v1"

	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/infra/lib/ssh"
	"github.com/augmentcode/augment/research/infra/cfg/clusters"
	cfglib "github.com/augmentcode/augment/research/infra/cfg/lib"
	"github.com/augmentcode/augment/research/infra/lib/augment"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod/crd/devpodv1"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod/spec"
)

const (
	containerName = "c1-dev"
)

// Builder is used for calculating properties of a DevPod and building a `Set`
// of objects.
type Builder struct {
	spec *spec.DevPod
	clst *clusters.Cluster
	n    clusters.Node

	// ServiceAccount Secret name, set from UserServiceAccountTokenSet().
	sasec      string
	keygen     ssh.KeyGen
	InitSpec   string
	DotExtra   []string
	SkipDot    bool
	Namespaced bool
}

func NewBuilder(c *clusters.Cluster, s *spec.DevPod) *Builder {
	return &Builder{
		spec: s,
		clst: c,
	}
}

func (b *Builder) SetNode(n clusters.Node) {
	b.n = n
}

func (b Builder) Spec() spec.DevPod {
	return *b.spec
}

func (b Builder) Cluster() clusters.Cluster {
	return *b.clst
}

func (b Builder) Node() clusters.Node {
	return b.n
}

func (b Builder) Namespace() string {
	return b.Cluster().MainNamespace
}

func (b Builder) User() string {
	return b.Spec().UserName
}

func (b Builder) Name() string {
	return b.Spec().DevPodName
}

func (b Builder) IsGCP() bool {
	return b.Cluster().IsGCP()
}

func (b Builder) IsCW() bool {
	return !b.IsGCP()
}

func (b Builder) GPUType() string {
	if b.Node().IsGPU() {
		return b.Node().PrimaryAlias
	}
	return ""
}

func (b Builder) ExposedURL(port int32) string {
	return fmt.Sprintf("https://%d-%s.exp.%s.r.augmentcode.com", port, b.Name(), strings.ToLower(b.Cluster().Name))
}

func (b Builder) SSHServiceHostname() string {
	parts := []string{
		b.Name(),
		strings.ToLower(b.Cluster().Name),
		"r.augmentcode.com",
	}
	return strings.Join(parts, ".")
}

func (b Builder) SSHNonStdPort() int32 {
	return 22022
}

func (b Builder) EternalTerminalPort() int32 {
	if b.Spec().Cxn.EternalTerminal {
		return 22023
	}
	return 0
}

func (b Builder) Image() string {
	return b.Spec().Image.Image(b.Cluster(), b.Node().IsGPU())
}

func (b Builder) UserServiceAccountName() string {
	return cfglib.NewUser(b.User()).DefaultServiceAccountName()
}

func (b *Builder) UserServiceAccountTokenSet(sec *k8s.Secret) error {
	if got, want := sec.ServiceAccountName(), b.UserServiceAccountName(); got != want {
		return fmt.Errorf("UserServiceAccountTokenSet(%s): ServiceAccount Name %s != %s", sec.Name(), got, want)
	}
	b.sasec = sec.Name()
	return nil
}

////////////////////////////////////////////////////////////////////////////////
//
// Object Names
//

func (b Builder) ConfigMapName() string {
	return b.Name() + "-config"
}

func (b Builder) IngressServiceName() string {
	// NOTE(mattm): The "-ignress" suffix is historical. "-public" might be a better name,
	// but we also might be able to switch back to an ingress some day.
	return b.Name() + "-ingress"
}

func (b Builder) ServiceName() string {
	return b.Name()
}

func (b Builder) DeploymentName() string {
	return b.Name()
}

func (b Builder) SSHHostKeysSecretName() string {
	return b.Name() + "-ssh-hostkeys"
}

func (b Builder) HomePVCNameDefault() string {
	return b.Name() + "-home"
}

func (b Builder) HomePVCName() string {
	return b.Spec().HomePVCName()
}

////////////////////////////////////////////////////////////////////////////////
//
// Object Metadata
//

// BaseLabels apply to all objects related to DevPods, e.g., including a homedir
// not paired 1:1 with a specific pod name.
func (b Builder) BaseLabels() map[string]string {
	labels := map[string]string{
		"aug.devpod": "true",
		"aug.type":   "devpod",
		"aug.user":   b.User(),
	}
	return labels
}

// MetaLabels extend BaseLabels and apply to individual DevPods.
func (b Builder) MetaLabels() map[string]string {
	labels := b.BaseLabels()
	labels["aug.devpod.name"] = b.Name()
	labels["aug.service"] = b.Name()
	labels["aug.gpu_type"] = b.GPUType()
	return labels
}

////////////////////////////////////////////////////////////////////////////////
//
// Object / Spec Builders
//

func (b Builder) BuildSet(s *devpodv1.DevPod) (*Set, error) {
	set := &Set{spec: s, bld: &b}

	if ac, err := b.BuildConfigMap(); err != nil {
		return nil, err
	} else {
		set.cmAC = ac
	}

	if ac, err := b.BuildIngressService(); err != nil {
		return nil, err
	} else {
		set.ingressSvcAC = ac
	}

	if ac, err := b.BuildService(); err != nil {
		return nil, err
	} else {
		set.serviceAC = ac
	}

	if ac, err := b.BuildSSHHostKeysSecret(); err != nil {
		return nil, err
	} else {
		set.sshHKSecAC = ac
	}

	if ac, err := b.BuildHomePVC(); err != nil {
		return nil, err
	} else {
		set.homePVCAC = ac
	}

	if ac, err := b.BuildDeployment(set.cmAC, set.sshHKSecAC, set.homePVCAC); err != nil {
		return nil, err
	} else {
		set.deploymentAC = ac
	}

	if s != nil {
		set.SetOwners(s)
	}
	return set, nil
}

func (b Builder) BuildConfigMap() (*k8s.ConfigMapApplyConfig, error) {
	init, err := Init{}.FromSpec(b.InitSpec)
	if err != nil {
		return nil, err
	}

	yaml, err := b.Spec().YAML()
	if err != nil {
		yaml = err.Error()
	}

	json, err := b.Spec().JSON()
	if err != nil {
		json = err.Error()
	}

	data := map[string]string{
		"user":             b.User(),
		"init.sh":          init,
		"devpod.spec.yaml": yaml,
		"devpod.spec.json": json,
	}
	if !b.SkipDot {
		dot, err := NewDotfiles().BuildAndClose(b.DotExtra...)
		if err != nil {
			return nil, err
		}
		data["dotfiles.tgz.b64"] = dot
	}

	cfg := k8s.NewConfigMapApplyConfig(b.ConfigMapName(), b.Namespace())
	cfg.WithLabels(b.MetaLabels())
	cfg.WithData(data)

	return cfg, nil
}

// BuildIngressService may return nil without error.
func (b Builder) BuildIngressService() (*k8s.ServiceApplyConfig, error) {
	ports := b.Spec().Cxn.ExposedPorts
	if len(ports) == 0 {
		return nil, nil
	}

	cfg := k8s.NewServiceApplyConfig(b.IngressServiceName(), b.Namespace())
	cfg.WithLabels(b.MetaLabels())
	cfg.WithSpec(corev1a.ServiceSpec().
		WithType(corev1.ServiceTypeClusterIP).
		WithSelector(map[string]string{"k8s.deployment": b.DeploymentName()}).
		WithPorts(ports.ServicePortsApplyConfig()...),
	)

	return cfg, nil
}

func (b Builder) BuildService() (*k8s.ServiceApplyConfig, error) {
	cfg := k8s.NewServiceApplyConfig(b.ServiceName(), b.Namespace())
	svccfg := corev1a.ServiceSpec()
	cfg.WithSpec(svccfg)

	cfg.WithLabels(b.MetaLabels())
	svccfg.WithSelector(map[string]string{"k8s.deployment": b.DeploymentName()})

	if b.Spec().DockerMode || !b.Spec().Cxn.PublicIP() {
		svccfg.WithType(corev1.ServiceTypeClusterIP)
	} else {
		svccfg.WithType(corev1.ServiceTypeLoadBalancer)
		svccfg.WithExternalTrafficPolicy(corev1.ServiceExternalTrafficPolicyLocal)
		cfg.WithAnnotations(map[string]string{"external-dns.alpha.kubernetes.io/hostname": b.SSHServiceHostname()})

		if b.IsCW() {
			cfg.WithAnnotations(map[string]string{
				"service.beta.kubernetes.io/coreweave-load-balancer-ip-families": "ipv4",
				"service.beta.kubernetes.io/coreweave-load-balancer-type":        "public",
			})
		}
	}

	cfg.WithPort("ssh", corev1.ProtocolTCP, b.SSHNonStdPort(), b.SSHNonStdPort())

	if b.Spec().Cxn.SSHStdPort {
		cfg.WithPort("ssh-std", corev1.ProtocolTCP, 22, b.SSHNonStdPort())
	}
	if et := b.EternalTerminalPort(); et > 0 {
		cfg.WithPort("eternal-terminal", corev1.ProtocolTCP, et, et)
	}
	if start, count := b.Spec().Cxn.MoshStart, b.Spec().Cxn.MoshCount; start > 0 && count > 0 {
		if c, l := count, int32(10); c > l {
			return nil, fmt.Errorf("%d: too many mosh ports, limit is currently %d", c, l)
		}
		for p := start; p < start+count; p++ {
			cfg.WithPort("mosh-"+strconv.Itoa(int(p)), corev1.ProtocolUDP, p, p)
		}
	}

	return cfg, nil
}

func (b Builder) BuildSSHHostKeysSecret() (*k8s.SecretApplyConfig, error) {
	keyset, err := b.keygen.GenHostKeyFiles("root@" + b.Name())
	if err != nil {
		return nil, err
	}

	cfg := k8s.NewSecretApplyConfig(b.SSHHostKeysSecretName(), b.Namespace())
	cfg.WithLabels(b.MetaLabels())
	cfg.WithType(k8s.SecretTypeSSHHostKeys)
	cfg.WithData(keyset)

	return cfg, nil
}

func (b Builder) BuildHomePVC() (*k8s.PVCApplyConfig, error) {
	class := func() string {
		if c := b.Spec().Home.Class; c != "" {
			return c
		} else if b.IsGCP() {
			return "premium-rwo"
		} else if b.IsCW() {
			return "shared-vast"
		}
		return "premium-rwo"
	}()
	if class == "ephemeral" || class == "local" {
		return nil, nil
	}

	mode := func() corev1.PersistentVolumeAccessMode {
		if m := b.Spec().Home.Mode; m != "" {
			return k8s.PVCAccessMode(m)
		}
		return corev1.ReadWriteOnce
	}()

	labels := func() map[string]string {
		if b.Spec().Home.CustomName == "" {
			return b.MetaLabels()
		} else {
			return b.BaseLabels()
		}
	}()

	size, err := func() (resource.Quantity, error) {
		q := b.Spec().Home.Size()
		if b.Spec().DockerMode {
			gi500 := *resource.NewQuantity(500*1024*1024*1024, resource.BinarySI)
			if q.Cmp(gi500) < 0 {
				return gi500, nil
			}
		}
		return q, nil
	}()
	if err != nil {
		return nil, err
	}

	cfg := k8s.NewPVCApplyConfig(b.Spec().HomePVCName(), b.Namespace())
	cfg.WithLabels(labels)
	cfg.WithSpec(corev1a.PersistentVolumeClaimSpec().
		WithStorageClassName(class).
		WithAccessModes(mode).
		WithResources(corev1a.VolumeResourceRequirements().
			WithRequests(corev1.ResourceList{
				corev1.ResourceStorage: size,
			}),
		))

	return cfg, nil
}

func (b Builder) BuildDeployment(cmAC *k8s.ConfigMapApplyConfig, sshAC *k8s.SecretApplyConfig, homeAC *k8s.PVCApplyConfig) (*k8s.DeploymentApplyConfig, error) {
	/// Top-Level Deployment

	d := k8s.NewDeploymentApplyConfig(b.DeploymentName(), b.Namespace())
	d.WithLabels(b.MetaLabels())

	/// DeploymentSpec

	spec := appsv1a.DeploymentSpec()
	d.WithSpec(spec)

	spec.WithReplicas(func() int32 {
		if b.Spec().PowerOff() {
			return 0
		}
		return 1
	}())
	spec.WithStrategy(appsv1a.DeploymentStrategy().WithType(appsv1.RecreateDeploymentStrategyType))
	spec.WithSelector(metav1a.LabelSelector().WithMatchLabels(map[string]string{
		"k8s.deployment": b.DeploymentName(),
	}))

	/// PodTemplateSpec

	tmpl := corev1a.PodTemplateSpec()
	spec.WithTemplate(tmpl)

	tmpl.WithLabels(b.MetaLabels())
	tmpl.WithLabels(map[string]string{"k8s.deployment": b.DeploymentName()})

	tmplAnnotations := map[string]string{
		"kubectl.kubernetes.io/default-container": containerName,
	}
	for k, v := range b.Node().K8sAnnotations {
		tmplAnnotations[k] = v
	}
	for k, v := range b.Node().SidecarAnnotations {
		tmplAnnotations[k] = v
	}

	/// PodSpec

	podspec := corev1a.PodSpec()
	tmpl.WithSpec(podspec)

	volmnts, err := b.buildVolMounts(cmAC, sshAC, homeAC, b.Node().IsGPU())
	if err != nil {
		return nil, err
	}

	podspec.WithServiceAccountName(func() string {
		if b.Namespaced {
			return b.User()
		}
		return ""
	}())
	podspec.WithHostname(b.Name())
	podspec.WithSetHostnameAsFQDN(true)
	podspec.WithEnableServiceLinks(false)
	podspec.WithRestartPolicy(corev1.RestartPolicyAlways)
	podspec.WithPriorityClassName(b.Cluster().DevPodDefaultPC)
	podspec.WithAffinity(b.buildAffinity(b.Node()))
	podspec.WithTolerations(b.Node().K8sTolerations...)
	podspec.WithTolerations(b.Spec().Resources.TolerationApplyConfigs()...)
	podspec.WithVolumes(volmnts.VolumeApplyConfigs()...)
	podspec.WithVolumes(b.Node().SidecarVolumes...)

	/// Container

	c := corev1a.Container()

	c.WithName(containerName)
	c.WithImage(b.Image())
	c.WithImagePullPolicy(corev1.PullAlways)
	c.WithResources(b.Node().K8sResources)
	c.WithVolumeMounts(volmnts.VolumeMountsApplyConfigs()...)
	c.WithVolumeMounts(b.Node().ContainerMounts...)
	c.WithEnv(b.Node().ContainerEnvs...)
	c.WithCommand(
		"/bin/bash", "/startup/init.sh",
		fmt.Sprintf("--ssh-port=%d", b.SSHNonStdPort()),
		fmt.Sprintf("--et-port=%d", b.EternalTerminalPort()),
	)
	if b.Spec().DockerMode {
		c.WithCommand("--docker")
	}

	sec := corev1a.SecurityContext()
	c.WithSecurityContext(sec)
	sec.WithRunAsUser(0)
	sec.WithPrivileged(b.Spec().Privileged || b.Spec().DockerMode)

	/// Container (append last, ContainerApplyConfigurations are stored by value)
	podspec.WithContainers(b.Node().SidecarContainers...)
	podspec.WithContainers(c)

	// Disable GCS FUSE sidecar if there aren't any volumes.
	hasGCS := false
	for _, v := range podspec.Volumes {
		if v.CSI != nil && v.CSI.Driver != nil && *v.CSI.Driver == "gcsfuse.csi.storage.gke.io" {
			hasGCS = true
			break
		}
	}
	if !hasGCS {
		for k := range tmplAnnotations {
			if strings.HasPrefix(k, "gke-gcsfuse/") {
				delete(tmplAnnotations, k)
			}
		}
	}

	tmpl.WithAnnotations(tmplAnnotations)
	return d, nil
}

func (b Builder) buildAffinity(node clusters.Node) *corev1a.AffinityApplyConfiguration {
	a := corev1a.Affinity()

	nodeAC := corev1a.NodeAffinity()
	a.WithNodeAffinity(nodeAC)

	podAC := corev1a.PodAffinity()
	a.WithPodAffinity(podAC)

	podAntiAC := corev1a.PodAntiAffinity()
	a.WithPodAntiAffinity(podAntiAC)

	// PodAffinity to try to group pods together. DevPods usually have only 1 GPU so
	// we want to pack them into fewer machines to allow batch jobs to graph all 8.
	podAC.WithPreferredDuringSchedulingIgnoredDuringExecution(
		corev1a.WeightedPodAffinityTerm().
			WithWeight(100).
			WithPodAffinityTerm(
				corev1a.PodAffinityTerm().
					WithTopologyKey("kubernetes.io/hostname").
					WithLabelSelector(
						metav1a.LabelSelector().
							WithMatchExpressions(
								metav1a.LabelSelectorRequirement().
									WithKey("aug.devpod").
									WithOperator(metav1.LabelSelectorOpExists),
							),
					),
			),
		corev1a.WeightedPodAffinityTerm().
			WithWeight(99).
			WithPodAffinityTerm(
				corev1a.PodAffinityTerm().
					WithTopologyKey("cloud.google.com/gke-nodepool").
					WithLabelSelector(
						metav1a.LabelSelector().
							WithMatchExpressions(
								metav1a.LabelSelectorRequirement().
									WithKey("aug.devpod").
									WithOperator(metav1.LabelSelectorOpExists),
							),
					),
			),
	)

	nreqs := node.K8sNodeSelectors
	if nn := b.Spec().Resources.NodeName; nn != "" {
		nreqs = append(nreqs, corev1a.NodeSelectorRequirement().
			WithKey("kubernetes.io/hostname").
			WithOperator(corev1.NodeSelectorOpIn).
			WithValues(nn),
		)
	}
	if len(nreqs) > 0 {
		nodeAC.WithRequiredDuringSchedulingIgnoredDuringExecution(
			corev1a.NodeSelector().
				WithNodeSelectorTerms(
					corev1a.NodeSelectorTerm().WithMatchExpressions(nreqs...),
				),
		)
	}

	return a
}

// buildVolMounts builds the pod-specific Volumes and VolumeMounts. The cluster-defined mounts
// (e.g., /home/<USER>/aug-cw-las1) are merged in separately.
func (b Builder) buildVolMounts(cmAC *k8s.ConfigMapApplyConfig, sshsecAC *k8s.SecretApplyConfig, homeAC *k8s.PVCApplyConfig, isGPU bool) (clusters.VolMounts, error) {
	vms := clusters.VolMounts{}

	/// /run and /tmp from tmpfs
	vms = append(vms, clusters.VolMount{
		Name: "run",
		Vol: corev1.Volume{
			VolumeSource: corev1.VolumeSource{
				EmptyDir: &corev1.EmptyDirVolumeSource{
					Medium: corev1.StorageMediumMemory,
				},
			},
		},
		Mounts: []corev1.VolumeMount{{
			MountPath: "/run",
		}},
	})
	vms = append(vms, clusters.VolMount{
		Name: "tmp",
		Vol: corev1.Volume{
			VolumeSource: corev1.VolumeSource{
				EmptyDir: &corev1.EmptyDirVolumeSource{
					Medium: corev1.StorageMediumMemory,
				},
			},
		},
		Mounts: []corev1.VolumeMount{{
			MountPath: "/tmp",
		}},
	})

	/// Add main ConfigMap
	if cmAC != nil {
		vms = append(vms, clusters.VolMount{
			Name: cmAC.Name(),
			Vol: corev1.Volume{
				VolumeSource: corev1.VolumeSource{
					ConfigMap: &corev1.ConfigMapVolumeSource{},
				},
			},
			Mounts: []corev1.VolumeMount{{
				MountPath: "/startup",
				ReadOnly:  true,
			}},
		})
	}

	/// Add SSH HostKeys Secret (or deprecated Sys PVC)
	if sshsecAC != nil {
		vms = append(vms, clusters.VolMount{
			Name: sshsecAC.Name(),
			Vol: corev1.Volume{
				VolumeSource: corev1.VolumeSource{
					Secret: &corev1.SecretVolumeSource{
						Items: func() (items []corev1.KeyToPath) {
							for _, ki := range ssh.HostKeyFileSet(sshsecAC.Data()).Info() {
								items = append(items, corev1.KeyToPath{
									Key:  ki.Name,
									Path: "etc/ssh/" + ki.Name,
									Mode: func() *int32 {
										m := int32(ki.Mode)
										return &m
									}(),
								})
							}
							return
						}(),
					},
				},
			},
			Mounts: []corev1.VolumeMount{{
				MountPath: "/etc/ssh/hostkeys",
			}},
		})
	}

	/// Add (optional) home PVC
	if homeAC != nil {
		vms = append(vms, clusters.VolMount{
			Name: homeAC.Name(),
			// Volume defaults to PVC.
			Mounts: []corev1.VolumeMount{{
				MountPath: "/home/<USER>",
				SubPath:   "augment",
			}},
		})
	}

	/// Add authorized_keys mount
	if !b.Spec().Cxn.SSHDisableCentralizedKeys {
		cm := augment.AuthorizedKeysConfigMap
		vms = append(vms, clusters.VolMount{
			Name: cm,
			Vol: corev1.Volume{
				VolumeSource: corev1.VolumeSource{
					ConfigMap: &corev1.ConfigMapVolumeSource{
						Items: []corev1.KeyToPath{{
							Key:  b.User(),
							Path: b.User(),
							Mode: func() *int32 { m := int32(0o444); return &m }(),
						}},
					},
				},
			},
			Mounts: []corev1.VolumeMount{{
				MountPath: "/etc/ssh/authorized_keys",
				ReadOnly:  true,
			}},
		})
	}

	/// Add personal ServiceAccount
	if b.sasec != "" {
		vms = append(vms, clusters.VolMount{
			Name: b.sasec,
			Vol: corev1.Volume{
				VolumeSource: corev1.VolumeSource{
					Secret: &corev1.SecretVolumeSource{},
				},
			},
			Mounts: []corev1.VolumeMount{{
				MountPath: "/run/augment/serviceaccount/" + b.UserServiceAccountName(),
			}},
		})
	}

	/// Add augi-release-readers docker secret
	if secname := b.Cluster().AugiReleaseReaders; secname != "" {
		vms = append(vms, clusters.VolMount{
			Name: secname,
			Vol: corev1.Volume{
				VolumeSource: corev1.VolumeSource{
					Secret: &corev1.SecretVolumeSource{
						Optional: func() *bool { b := true; return &b }(),
					},
				},
			},
			Mounts: []corev1.VolumeMount{{
				MountPath: "/run/augment/secrets/" + secname,
			}},
		})
	}

	/// Hardcode 1Gi shmem to keep pytorch happy.
	/// TODO(marcmac): Make this configurable.
	if isGPU {
		name := "shmem"
		vms = append(vms, clusters.VolMount{
			Name: name,
			Vol: corev1.Volume{
				VolumeSource: corev1.VolumeSource{
					EmptyDir: &corev1.EmptyDirVolumeSource{
						Medium: corev1.StorageMediumMemory,
						// SizeLimit = 1Gi  // NOTE(mattm): commented out from launch_pod.py
					},
				},
			},
			Mounts: []corev1.VolumeMount{{
				MountPath: "/dev/shm",
			}},
		})
	}

	/// Add cluster-defined and user-defined volmounts
	if volspec := b.Spec().Volumes; true {
		if b.Spec().DockerMode {
			volspec.ExcludeClusterDefaults = true
		}
		if vms2, err := volspec.CombinedAllVolMounts(b.Cluster(), b.User()); err != nil {
			return nil, err
		} else {
			vms = append(vms, vms2...)
		}
	}

	return vms, nil
}

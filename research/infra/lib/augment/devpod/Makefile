REPO_ROOT := $(shell git rev-parse --show-toplevel)
CONTAINERS := $(REPO_ROOT)/research/environments/containers

.PHONY: build cpu gpu augment_devpod_cpu augment_devpod_gpu
.PHONY: push push_cpu push_gpu push_devpod_cpu push_devpod_gpu

build: augment_devpod_cpu augment_devpod_gpu
push: push_devpod_cpu push_devpod_gpu

augment_devpod_cpu cpu:
	$(MAKE) -C "$(CONTAINERS)" augment_devpod_cpu

augment_devpod_gpu gpu:
	$(MAKE) -C "$(CONTAINERS)" augment_devpod_gpu

push_devpod_cpu push_cpu:
	$(MAKE) -C "$(CONTAINERS)" push_devpod_cpu

push_devpod_gpu push_gpu:
	$(MAKE) -C "$(CONTAINERS)" push_devpod_gpu

load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "ctrl",
    srcs = [
        "ctrl.go",
        "ctrl_user.go",
        "user.go",
    ],
    importpath = "github.com/augmentcode/augment/research/infra/lib/augment/devpod/ctrl",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/lib/k8s",
        "//infra/lib/logger",
        "//research/infra/cfg/clusters",
        "//research/infra/lib/augment/devpod/crd/devpodv1",
        "@com_github_google_go_cmp//cmp",
        "@io_k8s_api//rbac/v1:rbac",
        "@io_k8s_apimachinery//pkg/watch",
        "@io_k8s_client_go//applyconfigurations/rbac/v1:rbac",
        "@org_golang_x_sync//errgroup",
    ],
)

go_test(
    name = "ctrl_test",
    srcs = ["ctrl_test.go"],
    embed = [":ctrl"],
)

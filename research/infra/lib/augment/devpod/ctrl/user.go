package ctrl

import (
	"fmt"

	rbacv1 "k8s.io/api/rbac/v1"
	rbacv1a "k8s.io/client-go/applyconfigurations/rbac/v1"

	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod/crd/devpodv1"
)

type user struct {
	n string
	c *Controller
}

func (u *user) name() string {
	return u.n
}

func (u *user) email() string {
	return u.name() + "@augmentcode.com"
}

func (u *user) namespace() string {
	return userNamespacePrefix + u.name()
}

func (u *user) k8s() *devpodv1.Client {
	return u.c.k8s(u.namespace())
}

func (u *user) nsTmpl(deleted bool) *k8s.NamespaceApplyConfig {
	cfg := k8s.NewNamespaceApplyConfig(u.namespace())
	labels := map[string]string{
		"aug.user": u.name(),
	}
	if deleted {
		labels["aug.deleted"] = "true"
	}
	cfg.WithLabels(labels)
	cfg.WithFinalizers("r.augment.com/user-protection")
	return cfg
}

func (u *user) k8sSA() *k8s.ServiceAccountApplyConfig {
	cfg := k8s.NewServiceAccountApplyConfig(u.name(), u.namespace())
	cfg.WithAnnotations(map[string]string{
		"iam.gke.io/gcp-service-account": u.gcpSA().Email(u.c.cluster.GCPProject),
	})
	return cfg
}

func (u *user) editorsRoleBinding() *k8s.RoleBindingApplyConfig {
	cfg := k8s.NewRoleBindingApplyConfig("aug:namespace-editors", u.namespace())
	cfg.WithLabels(map[string]string{
		"aug.user": u.name(),
	})
	cfg.WithRoleRef(rbacv1a.RoleRef().WithAPIGroup(rbacv1.GroupName).WithKind("ClusterRole").WithName("edit"))
	cfg.WithSubjects(
		rbacv1a.Subject().WithKind(rbacv1.ServiceAccountKind).WithName(u.k8sSA().Name()).WithNamespace(u.namespace()),
		rbacv1a.Subject().WithKind(rbacv1.UserKind).WithAPIGroup(rbacv1.GroupName).WithName(u.gcpSA().Email(u.c.cluster.GCPProject)),
		rbacv1a.Subject().WithKind(rbacv1.UserKind).WithAPIGroup(rbacv1.GroupName).WithName(u.email()),
	)
	return cfg
}

func (u *user) gcpSA() *k8s.GCP_IAM_ServiceAccount {
	o := k8s.NewGCP_IAM_ServiceAccount(nil)
	o.SetName(u.c.cluster.Name + "-" + u.name())
	o.SetNamespace(u.c.cluster.CCNamespace())
	o.SetLabel("aug.user", u.name())
	return o
}

func (u *user) workloadIdentityACL() *k8s.GCP_IAM_PolicyMember {
	o := k8s.NewGCP_IAM_PolicyMember(nil)
	sa := u.gcpSA()
	o.SetName(fmt.Sprintf("%s.%s.workload-identity", u.namespace(), sa.Name()))
	o.SetNamespace(u.c.cluster.CCNamespace())
	o.SetLabel("aug.user", u.name())
	o.Raw.Spec.ResourceRef.APIVersion = "iam.cnrm.cloud.google.com/v1beta1"
	o.Raw.Spec.ResourceRef.Kind = "IAMServiceAccount"
	o.Raw.Spec.ResourceRef.External = sa.IAMRef(u.c.cluster.GCPProject)
	o.Raw.Spec.Role = "roles/iam.workloadIdentityUser"
	o.SetK8sServiceAccountMember(u.c.cluster.GCPProject, u.namespace(), u.k8sSA().Name())
	return o
}

func (u *user) groupMembership() *k8s.GCP_CloudIdentity_Membership {
	o := k8s.NewGCP_CloudIdentity_Membership(nil)
	sa := u.gcpSA()
	o.SetName(sa.Name() + ".r-user-sa-auto.member")
	o.SetNamespace(u.c.cluster.CCNamespace())
	o.SetLabel("aug.user", u.name())
	o.Raw.Spec.GroupRef.External = "groups/01tuee74150hs7x"
	o.Raw.Spec.PreferredMemberKey.ID = sa.Email(u.c.cluster.GCPProject)
	o.SetMemberRole()
	return o
}

package ctrl

import (
	"context"
	"fmt"
	"strings"

	"golang.org/x/sync/errgroup"

	"github.com/google/go-cmp/cmp"
	"k8s.io/apimachinery/pkg/watch"

	"github.com/augmentcode/augment/infra/lib/k8s"
)

// ApplyAllUsers is like `ApplyUsers()` but assumes the list of usernames given is comprehensive so that
// deletions can also be handled. If the list of usernames is empty, it is looked up from the canonical source.
func (c *Controller) ApplyAllUsers(ctx context.Context, usernames ...string) error {
	/// Lookup usernames if needed.

	if len(usernames) == 0 {
		var err error
		if usernames, err = c.Usernames(ctx); err != nil {
			return err
		}
	}

	/// Apply users.

	if err := c.ApplyUsers(ctx, usernames...); err != nil {
		return err
	}

	/// Archive deleted users.

	live := map[string]bool{}
	for _, u := range usernames {
		live[u] = true
	}

	deleted := []string{}

	if nss, err := c.k8s("").ListNamespaces(ctx); err != nil {
		return fmt.Errorf("ListNamspaces(): %w", err)
	} else {
		for _, ns := range nss {
			username, match := strings.CutPrefix(ns.Name(), userNamespacePrefix)
			if !match || live[username] {
				continue
			}
			deleted = append(deleted, username)
		}
	}

	if err := c.DeleteUsers(ctx, false, deleted...); err != nil {
		return err
	}

	return nil
}

func (c *Controller) ApplyUsers(ctx context.Context, usernames ...string) error {
	/// Lookup usernames if needed.

	if len(usernames) == 0 {
		var err error
		if usernames, err = c.Usernames(ctx); err != nil {
			return err
		}
	}

	/// Pass1: Namespace, Read Objects

	grp1, g1ctx := errgroup.WithContext(ctx)
	grp1.SetLimit(16)

	/// Pass1: Apply user Namespace(s).

	for _, username := range usernames {
		grp1.Go(func() error {
			_ = c.ApplyUserNamespace(g1ctx, username, false)
			return nil
		})
	}

	/// Pass1: Fetch objects to be mirrored.

	objs := make([]*k8s.Object, len(configmaps)+len(secrets))
	for i, name := range configmaps {
		grp1.Go(func() error {
			if o, err := c.srcK8s().GetConfigMap(g1ctx, name); err != nil {
				c.LogErr("cm/%s: %v.", name, err)
			} else {
				objs[i] = &o.Object
			}
			return nil
		})
	}
	for i, name := range secrets {
		i += len(configmaps)
		grp1.Go(func() error {
			if o, err := c.srcK8s().GetSecret(g1ctx, name); err != nil {
				c.LogErr("secret/%s: %v.", name, err)
			} else {
				objs[i] = &o.Object
			}
			return nil
		})
	}

	if err := grp1.Wait(); err != nil {
		return err
	}

	/// In a second stage, mirror objects, ksa, gsa, workload identity.

	grp2, g2ctx := errgroup.WithContext(ctx)
	grp2.SetLimit(64)

	for _, username := range usernames {
		for _, o := range objs {
			if o == nil {
				continue
			}
			grp2.Go(func() error {
				if ctxDone(g2ctx) {
					return nil
				} else if err := c.mirrorObjectForUser(g2ctx, watch.EventType(""), o, username); err != nil {
					c.LogErr("user/%s -> %s: %v.", username, o.ShortName(), err)
				}
				return nil
			})
		}
		grp2.Go(func() error {
			_ = c.ApplyK8sSA(g2ctx, username)
			return nil
		})
		grp2.Go(func() error {
			_ = c.ApplyEditorsRoleBinding(g2ctx, username)
			return nil
		})
		grp2.Go(func() error {
			_ = c.ApplyGCPSA(g2ctx, username)
			return nil
		})
		grp2.Go(func() error {
			_ = c.ApplyWorkloadIdentityACL(g2ctx, username)
			return nil
		})
		grp2.Go(func() error {
			_ = c.ApplyGroupMembership(g2ctx, username)
			return nil
		})
	}

	if err := grp2.Wait(); err != nil {
		return err
	}

	return nil
}

func (c *Controller) DeleteUsers(ctx context.Context, purge bool, usernames ...string) error {
	grp, ctx := errgroup.WithContext(ctx)
	grp.SetLimit(16)

	for _, username := range usernames {
		u := c.user(username)

		/// Always delete the ConfigConnector assets.

		grp.Go(func() error {
			name := u.gcpSA().Name()
			if !c.wetRun {
				c.LogInfo("user/%s: iamsserviceaccount/%s: Delete(dryrun=%t).", username, name, !c.wetRun)
			} else if err := c.ccK8s().DeleteIfGCP_IAM_ServiceAccount(ctx, name); err != nil {
				c.LogErr("user/%s: iamserviceaccount/%s: Delete(): %v.", username, name, err)
			}
			return nil
		})
		grp.Go(func() error {
			name := u.workloadIdentityACL().Name()
			if !c.wetRun {
				c.LogInfo("user/%s: iampolicymember/%s: Delete(dryrun=%t).", username, name, !c.wetRun)
			} else if err := c.ccK8s().DeleteIfGCP_IAM_PolicyMember(ctx, name); err != nil {
				c.LogErr("user/%s: iampolicymember/%s: Delete(): %v.", username, name, err)
			}
			return nil
		})
		grp.Go(func() error {
			name := u.groupMembership().Name()
			if !c.wetRun {
				c.LogInfo("user/%s: cloudidentitymembership/%s: Delete(dryrun=%t).", username, name, !c.wetRun)
			} else if err := c.ccK8s().DeleteIfGCP_CloudIdentity_Membership(ctx, name); err != nil {
				c.LogErr("user/%s: cloudidentitymembership/%s: Delete(): %v.", username, name, err)
			}
			return nil
		})

		/// If purging, delete the whole namespace.
		/// Otherwise, assure the other assets are deleted and label the namespace deleted.
		if purge {
			grp.Go(func() error {
				if !c.wetRun {
					c.LogInfo("user/%s: ns/%s: Delete(dryrun=%t).", username, u.namespace(), !c.wetRun)
				} else if err := c.k8s("").DeleteIfNamespace(ctx, u.namespace()); err != nil {
					c.LogErr("user/%s: ns/%s: Delete(): %v.", username, u.namespace(), err)
				}
				return nil
			})
		} else {
			grp.Go(func() error {
				name := u.k8sSA().Name()
				if !c.wetRun {
					c.LogInfo("user/%s: sa/%s: Delete(dryrun=%t).", username, name, !c.wetRun)
				} else if err := u.k8s().DeleteIfServiceAccount(ctx, name); err != nil {
					c.LogErr("user/%s: sa/%s: Delete(): %v.", username, name, err)
				}
				return nil
			})
			grp.Go(func() error {
				name := u.editorsRoleBinding().Name()
				if !c.wetRun {
					c.LogInfo("user/%s: rolebinding/%s: Delete(dryrun=%t).", username, name, !c.wetRun)
				} else if err := u.k8s().DeleteIfRoleBinding(ctx, name); err != nil {
					c.LogErr("user/%s: rolebinding/%s: Delete(): %v.", username, name, err)
				}
				return nil
			})
			grp.Go(func() error {
				_ = c.ApplyUserNamespace(ctx, username, true)
				return nil
			})
		}
	}

	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

func (c *Controller) ApplyUserNamespace(ctx context.Context, username string, deleted bool) error {
	u := c.user(username)
	k := c.k8s("")

	cfg := u.nsTmpl(deleted)

	nodiffHint := false
	if !c.noDiff {
		if cur, err := k.GetNamespace(ctx, cfg.Name()); k8s.NotFoundOK(err) != nil {
			c.LogWarn("user/%s: ns/%s: Get() error: %v.", u.name(), u.namespace(), err)
		} else if cur == nil {
			c.LogInfo("user/%s: ns/%s: Diff() -current +desired: <new>.", u.name(), cfg.Name())
		} else if curac, err := cur.ApplyConfig(FieldManagerName); err != nil {
			c.LogWarn("user/%s: ns/%s: error getting ApplyConfig: %v.", u.name(), cur.Name(), err)
		} else if diff := cmp.Diff(curac, cfg); diff == "" {
			c.LogInfo("user/%s: ns/%s: Diff() -current +desired: <no diff>.", u.name(), cfg.Name())
			nodiffHint = true
		} else {
			c.LogInfo("user/%s: ns/%s: Diff() -current +desired:\n%s", u.name(), cfg.Name(), diff)
		}
	}

	if nodiffHint {
		c.LogInfo("user/%s: ns/%s: Apply(dryrun=%t,skip=nodiff).", u.name(), cfg.Name(), !c.wetRun)
		return nil
	} else {
		c.LogInfo("user/%s: ns/%s: Apply(dryrun=%t)...", u.name(), cfg.Name(), !c.wetRun)
	}

	if c.wetRun {
		if _, err := k.ApplyNamespace(ctx, cfg, k8s.ApplyForce(true)); err != nil {
			c.LogErr("user/%s: ns/%s: Apply(dryrun=%t): %v.", u.name(), cfg.Name(), !c.wetRun, err)
			return err
		}
	}

	return nil
}

func (c *Controller) ApplyK8sSA(ctx context.Context, username string) error {
	u := c.user(username)
	k := u.k8s()

	cfg := u.k8sSA()

	nodiffHint := false
	if !c.noDiff {
		if cur, err := k.GetServiceAccount(ctx, cfg.Name()); k8s.NotFoundOK(err) != nil {
			c.LogWarn("user/%s: sa/%s: Get() error: %v.", u.name(), u.namespace(), err)
		} else if cur == nil {
			c.LogInfo("user/%s: sa/%s: Diff() -current +desired: <new>.", u.name(), cfg.Name())
		} else if curac, err := cur.ApplyConfig(FieldManagerName); err != nil {
			c.LogWarn("user/%s: sa/%s: error getting ApplyConfig: %v.", u.name(), cur.Name(), err)
		} else if diff := cmp.Diff(curac, cfg); diff == "" {
			c.LogInfo("user/%s: sa/%s: Diff() -current +desired: <no diff>.", u.name(), cfg.Name())
			nodiffHint = true
		} else {
			c.LogInfo("user/%s: sa/%s: Diff() -current +desired:\n%s", u.name(), cfg.Name(), diff)
		}
	}

	if nodiffHint {
		c.LogInfo("user/%s: sa/%s: Apply(dryrun=%t,skip=nodiff).", u.name(), cfg.Name(), !c.wetRun)
		return nil
	} else {
		c.LogInfo("user/%s: sa/%s: Apply(dryrun=%t)...", u.name(), cfg.Name(), !c.wetRun)
	}

	if c.wetRun {
		if _, err := k.ApplyServiceAccount(ctx, cfg, k8s.ApplyForce(true)); err != nil {
			c.LogErr("user/%s: sa/%s: Apply(dryrun=%t): %v.", u.name(), cfg.Name(), !c.wetRun, err)
			return err
		}
	}

	return nil
}

func (c *Controller) ApplyGCPSA(ctx context.Context, username string) error {
	u := c.user(username)
	k := c.ccK8s()

	cfg := u.gcpSA()

	nodiffHint := false
	if !c.noDiff {
		if un, err := cfg.Unstructured(); err != nil {
			c.LogWarn("user/%s: iamserviceaccount/%s: error parsing desired: %v.", u.name(), cfg.Name(), err)
		} else if cur, err := k.GetGCP_IAM_ServiceAccount(ctx, un.GetName()); k8s.NotFoundOK(err) != nil {
			c.LogWarn("user/%s: iamserviceaccount/%s: Get() error: %v.", u.name(), cfg.Name(), err)
		} else if cur == nil {
			c.LogInfo("user/%s: iamserviceaccount/%s: Diff() -current +desired: <new>.", u.name(), un.GetName())
		} else if curun, err := k.ExtractUnstructuredObjectApply(&cur.Object, ""); err != nil {
			c.LogWarn("user/%s: iamserviceaccount/%s: Diff() error parsing current: %v.", u.name(), un.GetName(), err)
		} else if diff := cmp.Diff(curun, un); diff == "" {
			c.LogInfo("user/%s: iamserviceaccount/%s: Diff() -current +desired: <no diff>.", u.name(), un.GetName())
			nodiffHint = true
		} else {
			c.LogInfo("user/%s: iamserviceaccount/%s: Diff() -current +desired:\n%s", u.name(), un.GetName(), diff)
		}
	}

	if nodiffHint {
		c.LogInfo("user/%s: iamserviceaccount/%s: Apply(dryrun=%t,skip=nodiff).", u.name(), cfg.Name(), !c.wetRun)
		return nil
	} else {
		c.LogInfo("user/%s: iamserviceaccount/%s: Apply(dryrun=%t)...", u.name(), cfg.Name(), !c.wetRun)
	}

	if c.wetRun {
		if _, err := k.ApplyGCP_IAM_ServiceAccount(ctx, cfg, k8s.ApplyForce(true)); err != nil {
			c.LogErr("user/%s: iamserviceaccount/%s: Apply(dryrun=%t): %v.", u.name(), cfg.Name(), !c.wetRun, err)
			return err
		}
	}

	return nil
}

func (c *Controller) ApplyWorkloadIdentityACL(ctx context.Context, username string) error {
	u := c.user(username)
	k := c.ccK8s()

	cfg := u.workloadIdentityACL()

	nodiffHint := false
	if !c.noDiff {
		if un, err := cfg.Unstructured(); err != nil {
			c.LogWarn("user/%s: iampolicymember/%s: error parsing desired: %v.", u.name(), cfg.Name(), err)
		} else if cur, err := k.GetGCP_IAM_PolicyMember(ctx, un.GetName()); k8s.NotFoundOK(err) != nil {
			c.LogWarn("user/%s: iampolicymember/%s: Get() error: %v.", u.name(), cfg.Name(), err)
		} else if cur == nil {
			c.LogInfo("user/%s: iampolicymember/%s: Diff() -current +desired: <new>.", u.name(), un.GetName())
		} else if curun, err := k.ExtractUnstructuredObjectApply(&cur.Object, ""); err != nil {
			c.LogWarn("user/%s: iampolicymember/%s: Diff() error parsing current: %v.", u.name(), un.GetName(), err)
		} else if diff := cmp.Diff(curun, un); diff == "" {
			c.LogInfo("user/%s: iampolicymember/%s: Diff() -current +desired: <no diff>.", u.name(), un.GetName())
			nodiffHint = true
		} else {
			c.LogInfo("user/%s: iampolicymember/%s: Diff() -current +desired:\n%s", u.name(), un.GetName(), diff)
		}
	}

	if nodiffHint {
		c.LogInfo("user/%s: iampolicymember/%s: Apply(dryrun=%t,skip=nodiff).", u.name(), cfg.Name(), !c.wetRun)
		return nil
	} else {
		c.LogInfo("user/%s: iampolicymember/%s: Apply(dryrun=%t)...", u.name(), cfg.Name(), !c.wetRun)
	}

	if c.wetRun {
		if _, err := k.ApplyGCP_IAM_PolicyMember(ctx, cfg, k8s.ApplyForce(true)); err != nil {
			c.LogErr("user/%s: iampolicymember/%s: Apply(dryrun=%t): %v.", u.name(), cfg.Name(), !c.wetRun, err)
			return err
		}
	}

	return nil
}

func (c *Controller) ApplyGroupMembership(ctx context.Context, username string) error {
	u := c.user(username)
	k := c.ccK8s()

	cfg := u.groupMembership()

	nodiffHint := false
	if !c.noDiff {
		if un, err := cfg.Unstructured(); err != nil {
			c.LogWarn("user/%s: cloudidentitymembership/%s: error parsing desired: %v.", u.name(), cfg.Name(), err)
		} else if cur, err := k.GetGCP_CloudIdentity_Membership(ctx, un.GetName()); k8s.NotFoundOK(err) != nil {
			c.LogWarn("user/%s: cloudidentitymembership/%s: Get() error: %v.", u.name(), cfg.Name(), err)
		} else if cur == nil {
			c.LogInfo("user/%s: cloudidentitymembership/%s: Diff() -current +desired: <new>.", u.name(), un.GetName())
		} else if curun, err := k.ExtractUnstructuredObjectApply(&cur.Object, ""); err != nil {
			c.LogWarn("user/%s: cloudidentitymembership/%s: Diff() error parsing current: %v.", u.name(), un.GetName(), err)
		} else if diff := cmp.Diff(curun, un); diff == "" {
			c.LogInfo("user/%s: cloudidentitymembership/%s: Diff() -current +desired: <no diff>.", u.name(), un.GetName())
			nodiffHint = true
		} else {
			c.LogInfo("user/%s: cloudidentitymembership/%s: Diff() -current +desired:\n%s", u.name(), un.GetName(), diff)
		}
	}

	if nodiffHint {
		c.LogInfo("user/%s: cloudidentitymembership/%s: Apply(dryrun=%t,skip=nodiff).", u.name(), cfg.Name(), !c.wetRun)
		return nil
	} else {
		c.LogInfo("user/%s: cloudidentitymembership/%s: Apply(dryrun=%t)...", u.name(), cfg.Name(), !c.wetRun)
	}

	if c.wetRun {
		if _, err := k.ApplyGCP_CloudIdentity_Membership(ctx, cfg, k8s.ApplyForce(true)); err != nil {
			c.LogErr("user/%s: cloudidentitymembership/%s: Apply(dryrun=%t): %v.", u.name(), cfg.Name(), !c.wetRun, err)
			return err
		}
	}

	return nil
}

func (c *Controller) ApplyEditorsRoleBinding(ctx context.Context, username string) error {
	u := c.user(username)
	k := u.k8s()

	cfg := u.editorsRoleBinding()

	nodiffHint := false
	if !c.noDiff {
		if cur, err := k.GetRoleBinding(ctx, cfg.Name()); k8s.NotFoundOK(err) != nil {
			c.LogWarn("user/%s: rolebinding/%s: Get() error: %v.", u.name(), u.namespace(), err)
		} else if cur == nil {
			c.LogInfo("user/%s: rolebinding/%s: Diff() -current +desired: <new>.", u.name(), cfg.Name())
		} else if curac, err := cur.ApplyConfig(FieldManagerName); err != nil {
			c.LogWarn("user/%s: rolebinding/%s: error getting ApplyConfig: %v.", u.name(), cur.Name(), err)
		} else if diff := cmp.Diff(curac, cfg); diff == "" {
			c.LogInfo("user/%s: rolebinding/%s: Diff() -current +desired: <no diff>.", u.name(), cfg.Name())
			nodiffHint = true
		} else {
			c.LogInfo("user/%s: rolebinding/%s: Diff() -current +desired:\n%s", u.name(), cfg.Name(), diff)
		}
	}

	if nodiffHint {
		c.LogInfo("user/%s: rolebinding/%s: Apply(dryrun=%t,skip=nodiff).", u.name(), cfg.Name(), !c.wetRun)
		return nil
	} else {
		c.LogInfo("user/%s: rolebinding/%s: Apply(dryrun=%t)...", u.name(), cfg.Name(), !c.wetRun)
	}

	if c.wetRun {
		if _, err := k.ApplyRoleBinding(ctx, cfg, k8s.ApplyForce(true)); err != nil {
			c.LogErr("user/%s: rolebinding/%s: Apply(dryrun=%t): %v.", u.name(), cfg.Name(), !c.wetRun, err)
			return err
		}
	}

	return nil
}

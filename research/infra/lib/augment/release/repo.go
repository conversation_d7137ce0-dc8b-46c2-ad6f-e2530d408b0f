package release

import (
	"context"
	"errors"
	"fmt"
	"io/fs"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
)

type Repo struct {
	// Release is a map of the 'version' annotation to deduplicated manifests.
	Releases map[string]*Release
	Name     string
}

func (r Repo) Versions() []string {
	vers := []string{}
	for v := range r.Releases {
		vers = append(vers, v)
	}
	sort.Sort(sort.StringSlice(vers))
	return vers
}

func (r Repo) NextVersion(now time.Time) string {
	today := now.Format("2006-01-02")
	prefix := today + "."

	max := 0
	for _, ver := range r.Versions() {
		if numstr, found := strings.CutPrefix(ver, prefix); !found {
			continue
		} else if num, err := strconv.Atoi(numstr); err != nil {
			continue
		} else if num > max {
			max = num
		}
	}

	return prefix + strconv.Itoa(max+1)
}

func (r Repo) GetVersionOrLabel(s string) (*Release, error) {
	if rel := r.Releases[s]; rel != nil {
		return rel, nil
	}
	for _, rel := range r.Releases {
		for _, t := range rel.LabelTags() {
			if t == s {
				return rel, nil
			}
		}
	}
	return nil, fmt.Errorf("%s: %s NotFound", r.Name, s)
}

func (r Repo) Info() string {
	lines := []string{}
	for _, v := range r.Versions() {
		rel := r.Releases[v]
		ltags := strings.Join(rel.LabelTags(), " ")
		if ltags != "" {
			ltags = " " + ltags
		}
		lines = append(lines, fmt.Sprintf("%s %s %s%s", r.Name, rel.VersionTag(), rel.SizeHuman(), ltags))
	}
	return strings.Join(lines, "\n") + "\n"
}

func (r Repo) Validate() error {
	errs := []error{}

	if rel, ok := r.Releases[""]; ok {
		errs = append(errs, fmt.Errorf("repo contains an unversioned release: %+v", rel))
	}

	seen := map[string]bool{}
	for ver, rel := range r.Releases {
		errs = append(errs, rel.Validate(ver))
		for _, label := range rel.LabelTags() {
			if seen[label] {
				errs = append(errs, fmt.Errorf("duplicate label: %s", label))
				seen[label] = true
			}
		}
	}

	return errors.Join(errs...)
}

func (r Repo) SyncFS(ctx context.Context, cli *Client, dry, force, links bool, versions ...string) error {
	if len(versions) == 0 {
		versions = append(versions, "live")
	}

	if err := r.Validate(); err != nil {
		return err
	}

	releaseDir := cli.FullPath(cli.ReleaseDir())
	if _, err := cli.osStat(releaseDir); errors.Is(err, fs.ErrNotExist) {
		if dry {
			cli.LogInfo("%s: does not exist, would mkdir.", releaseDir)
		} else {
			cli.LogInfo("%s: does not existing, mkdir'ing...", releaseDir)
			if err := cli.osMkdir(releaseDir, 0o775); err != nil {
				return err
			}
		}
	} else if err != nil {
		return err
	}

	wg := sync.WaitGroup{}
	wg.Add(len(versions))
	errs := make([]error, len(versions))
	for i, version := range versions {
		go func() {
			defer wg.Done()
			if rel, err := r.GetVersionOrLabel(version); err != nil {
				errs[i] = err
			} else {
				errs[i] = rel.SyncFS(ctx, cli, releaseDir, dry, force, links)
			}
		}()
	}
	wg.Wait()

	return errors.Join(errs...)
}

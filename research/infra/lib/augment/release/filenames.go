package release

// BaseName returns the BaseName of the client, based on the repo.
// Typically `augi`.
func (c Client) BaseName() string {
	return c.basename
}

////////////////////////////////////////////////////////////////////////////////
//
// BaseDir() and FullPath()
//

// BaseDir is typically `/usr/local/bin`.
func (c Client) BaseDir() string {
	return c.basedir
}

// FullPath returns the file and dir names formatted below, relative to BaseDir.
func (c Client) FullPath(name string) string {
	if dir := c.BaseDir(); dir != "" {
		return dir + "/" + name
	} else {
		return name
	}
}

////////////////////////////////////////////////////////////////////////////////
//
// File and Dir Name formatters
//

func (c Client) MainBinary() string {
	return c.BaseName()
}

func (c Client) LabeledName(label string) string {
	return c.BaseName() + "." + label
}

// ReleaseDir is typically `augi.release`.
func (c Client) ReleaseDir() string {
	return c.BaseName() + ".release"
}

func (c Client) ReleaseFile(name string) string {
	return c.ReleaseDir() + "/" + name
}

func (c Client) LabeledRelease(label string) string {
	return c.ReleaseFile(c.LabeledName(label))
}

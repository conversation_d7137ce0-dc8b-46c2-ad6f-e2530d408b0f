load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(default_visibility = ["//research/infra:internal"])

go_library(
    name = "release",
    srcs = [
        "client.go",
        "filenames.go",
        "release.go",
        "repo.go",
        "symlink.go",
        "update.go",
    ],
    importpath = "github.com/augmentcode/augment/research/infra/lib/augment/release",
    deps = [
        "//infra/lib/logger",
        "@com_github_opencontainers_go_digest//:go-digest",
        "@com_github_regclient_regclient//:regclient",
        "@com_github_regclient_regclient//pkg/archive",
        "@com_github_regclient_regclient//types/descriptor",
        "@com_github_regclient_regclient//types/manifest",
        "@com_github_regclient_regclient//types/oci/v1:oci",
        "@com_github_regclient_regclient//types/ref",
        "@org_golang_x_sync//errgroup",
    ],
)

go_test(
    name = "release_test",
    srcs = ["filenames_test.go"],
    embed = [":release"],
)

package release

import (
	"errors"
	"io/fs"
	"strings"
)

func (c Client) Select(label string, ver, dry bool) error {
	main := c.FullPath(c.MainBinary())
	target := c.LabeledName(label)
	if ver {
		// When ver=true, allow label to be <ver>, augi.<ver>, or even augi.release/augi.<ver>.
		label = strings.TrimPrefix(label, c.ReleaseDir()+"/")
		label = strings.TrimPrefix(label, c.BaseName()+".")
		// When ver=true, set the target to augi.release/augi.<ver>.
		target = c.LabeledRelease(label)
	}
	return c.symlink(target, main, dry)
}

func (c Client) Update(dry bool) error {
	/// First loop over augi.release/augi.* and create augi.* symlinks.

	dirents, err := c.osReadDir(c.FullPath(c.ReleaseDir()))
	if err != nil {
		return err
	}
	errs := []error{}
	for _, dirent := range dirents {
		fullpath := c.FullPath(c.ReleaseFile(dirent.Name()))

		// Only look at augi.release/augi.* symlinks.
		if !strings.HasPrefix(dirent.Name(), c.BaseName()+".") {
			continue
		} else if dirent.Type()&fs.ModeSymlink == 0 {
			continue
		}

		// Read the augi.* symlink.
		target, err := c.osReadlink(fullpath)
		if err != nil {
			errs = append(errs, err)
			continue
		}

		// Update relative path. For any `augi.release/augi.* -> foo`, we want to setup the matching
		// symlink `augi.* -> augi.release/foo`. (Unless the target is an absolute path).
		if !strings.HasPrefix(target, "/") {
			target = c.ReleaseFile(target)
		}
		// Ensure augi.* -> augi.release/augi.* symlink.
		errs = append(errs, c.symlink(target, c.FullPath(dirent.Name()), dry))
	}

	/// Second loop over augi.* and delete stale -> augi.release/* symlinks.

	dirents, err = c.osReadDir(c.BaseDir())
	if err != nil {
		return err
	}
	for _, dirent := range dirents {
		fullpath := c.FullPath(dirent.Name())

		// Only look at augi.* symlinks.
		if !strings.HasPrefix(dirent.Name(), c.BaseName()+".") {
			continue
		} else if dirent.Type()&fs.ModeSymlink == 0 {
			continue
		}

		// Only look at symlinks pointing into augi.release/* (absolute or relative paths)
		target, err := c.osReadlink(fullpath)
		if err != nil {
			errs = append(errs, err)
			continue
		} else if strings.HasPrefix(target, c.ReleaseDir()+"/") {
			// pass, matches relative path to augi.release/*
		} else if strings.HasPrefix(target, c.FullPath(c.ReleaseDir())+"/") {
			// pass, matches absolute path to <basedir>/augi.release/*
		} else {
			continue
		}

		if _, err := c.osStat(fullpath); err == nil {
			// pass
		} else if !errors.Is(err, fs.ErrNotExist) {
			errs = append(errs, err)
		} else if dry {
			c.LogInfo("%s: would remove stale symlink to %s.", fullpath, target)
		} else {
			c.LogInfo("%s: removing stale symlink to %s.", fullpath, target)
			if err := c.osRemove(fullpath); err != nil {
				errs = append(errs, err)
			}
		}

	}

	return errors.Join(errs...)
}

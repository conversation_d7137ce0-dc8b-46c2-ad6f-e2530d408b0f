package release

import (
	"testing"
)

func TestFileNames(t *testing.T) {
	tests := map[string]struct {
		inRepo     string
		inBaseName string
		inBaseDir  string

		wBaseName       string
		wBaseDir        string
		iFullPath       string
		wFullPath       string
		wMainBinary     string
		iLabeledName    string
		wLabeledName    string
		wReleaseDir     string
		iReleaseFile    string
		wReleaseFile    string
		iLabeledRelease string
		wLabeledRelease string
	}{
		"defaults": {
			inRepo:     "augi/augi",
			inBaseName: "",
			inBaseDir:  "",

			wBaseName:       "augi",
			wBaseDir:        "",
			iFullPath:       "name0",
			wFullPath:       "name0",
			wMainBinary:     "augi",
			iLabeledName:    "label0",
			wLabeledName:    "augi.label0",
			wReleaseDir:     "augi.release",
			iReleaseFile:    "name0",
			wReleaseFile:    "augi.release/name0",
			iLabeledRelease: "label0",
			wLabeledRelease: "augi.release/augi.label0",
		},
		"with-basedir": {
			inRepo:     "augi/augi",
			inBaseName: "",
			inBaseDir:  "/usr/local/bin",

			wBaseName:       "augi",
			wBaseDir:        "/usr/local/bin",
			iFullPath:       "name0",
			wFullPath:       "/usr/local/bin/name0",
			wMainBinary:     "augi",
			iLabeledName:    "label0",
			wLabeledName:    "augi.label0",
			wReleaseDir:     "augi.release",
			iReleaseFile:    "name0",
			wReleaseFile:    "augi.release/name0",
			iLabeledRelease: "label0",
			wLabeledRelease: "augi.release/augi.label0",
		},
		"with-rel-basedir": {
			inRepo:     "augi/augi",
			inBaseName: "",
			inBaseDir:  "dir0",

			wBaseName:       "augi",
			wBaseDir:        "dir0",
			iFullPath:       "name0",
			wFullPath:       "dir0/name0",
			wMainBinary:     "augi",
			iLabeledName:    "label0",
			wLabeledName:    "augi.label0",
			wReleaseDir:     "augi.release",
			iReleaseFile:    "name0",
			wReleaseFile:    "augi.release/name0",
			iLabeledRelease: "label0",
			wLabeledRelease: "augi.release/augi.label0",
		},
		"with-basename": {
			inRepo:     "augi/augi",
			inBaseName: "augi0",
			inBaseDir:  "/dir0",

			wBaseName:       "augi0",
			wBaseDir:        "/dir0",
			iFullPath:       "name0",
			wFullPath:       "/dir0/name0",
			wMainBinary:     "augi0",
			iLabeledName:    "label0",
			wLabeledName:    "augi0.label0",
			wReleaseDir:     "augi0.release",
			iReleaseFile:    "name0",
			wReleaseFile:    "augi0.release/name0",
			iLabeledRelease: "label0",
			wLabeledRelease: "augi0.release/augi0.label0",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			cli := NewClient("", "", tc.inRepo)
			if tc.inBaseName != "" {
				cli.SetBaseName(tc.inBaseName)
			}
			if tc.inBaseDir != "" {
				cli.SetBaseDir(tc.inBaseDir)
			}

			if got, want := cli.BaseName(), tc.wBaseName; got != want {
				t.Errorf("BaseName: got %v, want %v.", got, want)
			}
			if got, want := cli.BaseDir(), tc.wBaseDir; got != want {
				t.Errorf("BaseDir: got %v, want %v.", got, want)
			}
			if in, got, want := tc.iFullPath, cli.FullPath(tc.iFullPath), tc.wFullPath; got != want {
				t.Errorf("FullPath(%s): got %v, want %v.", in, got, want)
			}
			if got, want := cli.MainBinary(), tc.wMainBinary; got != want {
				t.Errorf("MainBinary: got %v, want %v.", got, want)
			}
			if in, got, want := tc.iLabeledName, cli.LabeledName(tc.iLabeledName), tc.wLabeledName; got != want {
				t.Errorf("LabeledName(%s): got %v, want %v.", in, got, want)
			}
			if got, want := cli.ReleaseDir(), tc.wReleaseDir; got != want {
				t.Errorf("ReleaseDir: got %v, want %v.", got, want)
			}
			if in, got, want := tc.iReleaseFile, cli.ReleaseFile(tc.iReleaseFile), tc.wReleaseFile; got != want {
				t.Errorf("ReleaseFile(%s): got %v, want %v.", in, got, want)
			}
			if in, got, want := tc.iLabeledRelease, cli.LabeledRelease(tc.iLabeledRelease), tc.wLabeledRelease; got != want {
				t.Errorf("LabeledRelease(%s): got %v, want %v.", in, got, want)
			}
		})
	}
}

package release

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	"golang.org/x/sync/errgroup"

	digest "github.com/opencontainers/go-digest"
	"github.com/regclient/regclient"
	"github.com/regclient/regclient/pkg/archive"
	"github.com/regclient/regclient/types/descriptor"
	"github.com/regclient/regclient/types/manifest"
	ociv1 "github.com/regclient/regclient/types/oci/v1"
	"github.com/regclient/regclient/types/ref"

	"github.com/augmentcode/augment/infra/lib/logger"
)

////////////////////////////////////////////////////////////////////////////////
//
// Client
//

// A Client is a Registry client focused on a single Registry + Repo.
type Client struct {
	logger.Logger

	rc   *regclient.RegClient
	hn   string
	repo string

	basedir  string
	basename string

	tNow       func() time.Time
	osLstat    func(string) (os.FileInfo, error)
	osMkdir    func(string, os.FileMode) error
	osOpenFile func(string, int, os.FileMode) (*os.File, error)
	osReadDir  func(string) ([]os.DirEntry, error)
	osReadlink func(string) (string, error)
	osRemove   func(string) error
	osStat     func(string) (os.FileInfo, error)
	osSymlink  func(string, string) error
}

// NewClient builds a new Client.
func NewClient(conffile, hostname, repo string) *Client {
	opt := func() regclient.Opt {
		if conffile != "" {
			return regclient.WithDockerCredsFile(conffile)
		}
		return regclient.WithDockerCreds()
	}()
	parts := strings.Split(repo, "/")
	basename := parts[len(parts)-1]
	return &Client{
		Logger: logger.New(nil),
		rc:     regclient.New(opt),
		hn:     hostname,
		repo:   repo,

		basename: basename,
		basedir:  "",

		tNow:       time.Now,
		osLstat:    os.Lstat,
		osMkdir:    os.Mkdir,
		osOpenFile: os.OpenFile,
		osReadDir:  os.ReadDir,
		osReadlink: os.Readlink,
		osRemove:   os.Remove,
		osStat:     os.Stat,
		osSymlink:  os.Symlink,
	}
}

func (c *Client) SetBaseName(name string) {
	c.basename = name
}

func (c *Client) SetBaseDir(dir string) {
	c.basedir = dir
}

////////////////////////////////////////////////////////////////////////////////
//
// Client Low-Level builders.
//

func (c Client) BaseRef() ref.Ref {
	return ref.Ref{
		Scheme:     "reg",
		Registry:   c.hn,
		Repository: c.repo,
	}
}

func (c Client) DigestRef(d digest.Digest) ref.Ref {
	r := c.BaseRef()
	r.Digest = d.String()
	return r
}

func (c Client) TagRef(tag string) ref.Ref {
	r := c.BaseRef()
	r.Tag = tag
	return r
}

func (c Client) MakeBlob(mediaType string, buf []byte) Blob {
	desc := descriptor.Descriptor{
		MediaType: mediaType,
		Digest:    digest.FromBytes(buf),
		Size:      int64(len(buf)),
	}
	return Blob{
		Ref:  c.DigestRef(desc.Digest),
		Desc: desc,
		buf:  buf,
	}
}

func (c Client) MakeBlobJSON(mediaType string, obj any) (Blob, error) {
	buf, err := json.Marshal(obj)
	if err != nil {
		return Blob{}, err
	}
	return c.MakeBlob(mediaType, buf), nil
}

func (c Client) MakeGZBlobFile(mediaType string, fname string) (Blob, error) {
	f, err := os.Open(fname)
	if err != nil {
		return Blob{}, err
	}
	defer f.Close()

	buf := bytes.Buffer{}
	gz := gzip.NewWriter(&buf)
	defer gz.Close()

	if _, err := io.Copy(gz, f); err != nil {
		return Blob{}, err
	}
	if err := gz.Close(); err != nil {
		return Blob{}, err
	}
	if err := f.Close(); err != nil {
		return Blob{}, err
	}

	return c.MakeBlob(mediaType, buf.Bytes()), nil
}

////////////////////////////////////////////////////////////////////////////////
//
// Client Higher-Level builders for custom types.
//

func (c Client) MakeAugFileCfgBlob(version string) (Blob, error) {
	cfg := AugmentFileConfig{
		Version: version,
	}
	return c.MakeBlobJSON(MediaTypeAugFileCfg, cfg)
}

func (c Client) MakeAugFileBlob(fname string) (Blob, error) {
	return c.MakeGZBlobFile(MediaTypeAugFile, fname)
}

func (c Client) MakeRelease(fname string, version string, labels ...string) (Release, error) {
	version, err := EnforceVersion(version)
	if err != nil {
		return Release{}, err
	}

	cfg, err := c.MakeAugFileCfgBlob(version)
	if err != nil {
		return Release{}, err
	}

	bin, err := c.MakeAugFileBlob(fname)
	if err != nil {
		return Release{}, err
	}

	man := ociv1.Manifest{
		Versioned: ociv1.ManifestSchemaVersion,
		MediaType: manifest.MediaTypeOCI1Manifest,
		Config:    cfg.Desc,
		Layers:    []descriptor.Descriptor{bin.Desc},
		Annotations: map[string]string{
			VersionAnnotation: version,
		},
	}

	ret := Release{
		BaseName:   c.BaseName(),
		VersionRef: c.TagRef(version),
		Manifest:   man,
		Config:     cfg,
		Binary:     bin,
	}
	for _, l := range labels {
		ret.LabelRefs = append(ret.LabelRefs, c.TagRef(l))
	}

	return ret, nil
}

////////////////////////////////////////////////////////////////////////////////
//
// Client Low-Level RPCs.
//

func (c Client) PutBlob(ctx context.Context, blob Blob) error {
	_, err := c.rc.BlobPut(ctx, blob.Ref, blob.Desc, bytes.NewBuffer(blob.buf))
	return err
}

func (c Client) PutManifest(ctx context.Context, r ref.Ref, m manifest.Manifest) error {
	return c.rc.ManifestPut(ctx, r, m)
}

func (c Client) GetTags(ctx context.Context) ([]string, error) {
	lst, err := c.rc.TagList(ctx, c.BaseRef())
	if err != nil {
		return nil, err
	}
	return lst.GetTags()
}

func (c Client) ReadBlob(ctx context.Context, blob Blob, w io.Writer) error {
	rdr, err := func() (io.ReadCloser, error) {
		// If we have in-band data, no need to download.
		if len(blob.Desc.Data) > 0 {
			return io.NopCloser(bytes.NewBuffer(blob.Desc.Data)), nil
		}

		blobReader, err := c.rc.BlobGet(ctx, blob.Ref, blob.Desc)
		if err != nil {
			return nil, err
		}

		zReader, err := archive.Decompress(blobReader)
		if err != nil {
			if err := blobReader.Close(); err != nil {
				c.LogWarn("blob %s: error closing reader: %v.", blob.Desc.Digest, err)
			}
			return nil, err
		}

		return io.NopCloser(zReader), nil
	}()
	if err != nil {
		return err
	}
	defer func() {
		if err := rdr.Close(); err != nil {
			c.LogWarn("blob %s: error closing reader: %v.", blob.Desc.Digest, err)
		}
	}()

	if _, err := io.Copy(w, rdr); err != nil {
		return err
	}
	return rdr.Close()
}

func (c Client) GetManifest(ctx context.Context, tag string) (manifest.Manifest, error) {
	return c.rc.ManifestGet(ctx, c.TagRef(tag))
}

func (c Client) GetOCIManifest(ctx context.Context, tag string) (ociv1.Manifest, error) {
	if m, err := c.GetManifest(ctx, tag); err != nil {
		return ociv1.Manifest{}, err
	} else if oci, ok := m.GetOrig().(ociv1.Manifest); !ok {
		return ociv1.Manifest{}, fmt.Errorf("%s (%s) is not an OCIv1 Manifest", tag, m.GetDescriptor().MediaType)
	} else {
		return oci, nil
	}
}

////////////////////////////////////////////////////////////////////////////////
//
// Client High-Level RPCs.
//

func (c Client) PutRelease(ctx context.Context, r Release, dry, force bool) error {
	man, err := manifest.New(manifest.WithOrig(r.Manifest))
	if err != nil {
		return err
	}

	labeledName := c.LabeledName(r.Version())

	/// Check that we're pushing the next version, which covers that we're trying trying to
	/// overwrite && that we didn't add an accidental gap in versioning.

	if nextver, err := c.NextVersion(ctx); err != nil {
		return err
	} else if nextver == r.Version() {
		c.LogInfo("%s: version matches NextVersion().", labeledName)
	} else if !force {
		return fmt.Errorf("%s: version does not match NextVersion(): %s.", labeledName, nextver)
	} else {
		c.LogWarn("%s: version does not match NextVersion(): %s, but forcing anyway.", labeledName, nextver)
	}

	/// Put blobs in parallel.

	grp, gctx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		if dry {
			c.LogInfo("%s: would put config blob: %s.", labeledName, r.Config.Desc.Digest)
			return nil
		} else {
			c.LogInfo("%s: putting config blob: %s...", labeledName, r.Config.Desc.Digest)
			return c.PutBlob(gctx, r.Config)
		}
	})
	grp.Go(func() error {
		if dry {
			c.LogInfo("%s: would put binary blob: %s.", labeledName, r.Binary.Desc.Digest)
			return nil
		} else {
			c.LogInfo("%s: putting binary blob: %s...", labeledName, r.Binary.Desc.Digest)
			return c.PutBlob(gctx, r.Binary)
		}
	})
	if err := grp.Wait(); err != nil {
		return err
	}

	/// Put "main" version manifest.

	if dry {
		c.LogInfo("%s: would put manifest with *version* tag: %s.", labeledName, r.VersionRef.Tag)
	} else {
		c.LogInfo("%s: putting manifest with *version* tag: %s...", labeledName, r.VersionRef.Tag)
		if err := c.PutManifest(ctx, r.VersionRef, man); err != nil {
			return err
		}
	}

	/// Put additional manifests in parallel.

	grp, gctx = errgroup.WithContext(ctx)
	for _, ref := range r.LabelRefs {
		grp.Go(func() error {
			if dry {
				c.LogInfo("%s: would put manifest with *label* tag: %s.", labeledName, ref.Tag)
				return nil
			} else {
				c.LogInfo("%s: putting manifest with *label* tag: %s...", labeledName, ref.Tag)
				return c.PutManifest(gctx, ref, man)
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

func (c Client) GetRepo(ctx context.Context) (Repo, error) {
	tags, err := c.GetTags(ctx)
	if err != nil {
		return Repo{}, err
	}

	// Start by getting all manifests in parallel.
	// There will be duplicates where multiple tags refer to the same manifest.

	type mantag struct {
		tag string
		man ociv1.Manifest
	}
	manifests := make(chan mantag, len(tags))
	grp, gctx := errgroup.WithContext(ctx)
	for _, tag := range tags {
		grp.Go(func() error {
			if m, err := c.GetOCIManifest(gctx, tag); err != nil {
				return err
			} else if l := len(m.Layers); l != 1 {
				return fmt.Errorf("%s: got %d layer(s), expecting only 1", tag, l)
			} else {
				manifests <- mantag{tag: tag, man: m}
				return nil
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return Repo{}, err
	}
	close(manifests)

	// Index Release by *version* annotation, dedup manifests.

	repo := Repo{
		Releases: map[string]*Release{},
		Name:     c.repo,
	}
	for mt := range manifests {
		tag, man := mt.tag, mt.man
		version := man.Annotations[VersionAnnotation]

		rel := repo.Releases[version]
		if rel == nil {
			rel = &Release{}
			repo.Releases[version] = rel

			rel.BaseName = c.BaseName()
			rel.Manifest = man
			rel.Config.Ref = c.DigestRef(man.Config.Digest)
			rel.Config.Desc = man.Config
			// len(layers) == 1 was validated during the GetManifest().
			rel.Binary.Ref = c.DigestRef(man.Layers[0].Digest)
			rel.Binary.Desc = man.Layers[0]
		}

		if tag == version {
			rel.VersionRef = c.TagRef(tag)
		} else {
			rel.LabelRefs = append(rel.LabelRefs, c.TagRef(tag))
		}
	}

	return repo, nil
}

func (c Client) SyncFS(ctx context.Context, dry, force, links bool, versions ...string) error {
	repo, err := c.GetRepo(ctx)
	if err != nil {
		return err
	}
	return repo.SyncFS(ctx, &c, dry, force, links, versions...)
}

func (c Client) NextVersion(ctx context.Context) (string, error) {
	c.LogInfo("NextVersion(): Getting repo (this can take a few seconds)...")
	repo, err := c.GetRepo(ctx)
	if err != nil {
		return "", err
	}
	c.LogInfo("NextVersion(): Got repo.")
	return repo.NextVersion(c.tNow()), nil
}

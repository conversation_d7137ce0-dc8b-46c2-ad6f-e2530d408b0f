package release

import (
	"errors"
	"io/fs"
)

// symlink ensures a symlink at `name`, pointing to `target`. If `name` exists and
// is not already a symlink to `target`, then it is first removed.
func (c Client) symlink(target, name string, dry bool) error {
	if fi, err := c.osLstat(name); errors.Is(err, fs.ErrNotExist) {
		// pass
	} else if err != nil {
		return err
	} else if fi.Mode().Type()&fs.ModeSymlink != 0 {
		if curtgt, err := c.osReadlink(name); err != nil {
			return err
		} else if curtgt == target {
			c.LogInfo("%s: already symlinks to %s.", name, curtgt)
			return nil
		} else if dry {
			c.LogInfo("%s: currently symlinks to %s, would remove.", name, curtgt)
		} else {
			c.LogInfo("%s: currently symlinks to %s, removing...", name, curtgt)
			if err := c.osRemove(name); err != nil {
				return err
			}
		}
	} else if fi.Mode().IsRegular() {
		if dry {
			c.LogInfo("%s: exists as a regular file, would remove.", name)
		} else {
			c.LogInfo("%s: exists as a regular file, removing...", name)
			if err := c.osRemove(name); err != nil {
				return err
			}
		}
	} else {
		if dry {
			c.LogInfo("%s: exists as a %v, would remove.", name, fi.Mode().Type())
		} else {
			c.LogInfo("%s: exists as a %v, removing...", name, fi.Mode().Type())
			if err := c.osRemove(name); err != nil {
				return err
			}
		}
	}

	if dry {
		c.LogInfo("%s: would symlink to %s.", name, target)
	} else {
		c.LogInfo("%s: symlinking to %s...", name, target)
		if err := c.osSymlink(target, name); err != nil {
			return err
		}
		c.LogInfo("%s: symlinked.", name)
	}

	return nil
}

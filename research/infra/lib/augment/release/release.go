package release

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/fs"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/regclient/regclient/types/descriptor"
	ociv1 "github.com/regclient/regclient/types/oci/v1"
	"github.com/regclient/regclient/types/ref"
)

const (
	MediaTypeAugFileCfg = "application/vnd.augment.file.config.v1+json"
	MediaTypeAugFile    = "application/vnd.augment.file.v1+gzip"
	VersionAnnotation   = "version"
)

type AugmentFileConfig struct {
	Version string `json:"version"`
}

type Blob struct {
	Ref  ref.Ref
	Desc descriptor.Descriptor
	buf  []byte
}

func EnforceVersion(version string) (string, error) {
	const layout = "2006-01-02"

	if i, err := strconv.Atoi(version); err == nil {
		version = fmt.Sprintf("%s.%d", time.Now().Format(layout), i)
	}

	date, num, _ := strings.Cut(version, ".")
	if n, err := strconv.Atoi(num); err != nil {
		return "", fmt.Errorf("point release %q: %w", num, err)
	} else if n < 0 {
		return "", fmt.Errorf("point release %d: not valid", n)
	}

	if _, err := time.Parse(layout, date); err != nil {
		return "", fmt.Errorf("date release %q: %w", date, err)
	}

	return version, nil
}

// A Release is made up of a Manifest (with a primary Version tag + additional label tags),
// a Config Blob (we don't really need this except for backwards compatability), and a single
// "Layer" Blob containing a gzip'd file.
type Release struct {
	BaseName   string
	VersionRef ref.Ref
	LabelRefs  []ref.Ref
	Manifest   ociv1.Manifest
	Config     Blob
	Binary     Blob
}

func (r Release) Validate(ver string) error {
	if got, want := r.Version(), ver; got != want {
		return fmt.Errorf("version mismatch: got %s, want %s", got, want)
	}
	if ever, err := EnforceVersion(ver); err != nil {
		return err
	} else if got, want := ver, ever; got != want {
		return fmt.Errorf("version not in canonical format: got %s, want %s", got, want)
	}
	return nil
}

func (r Release) Version() string {
	return r.Manifest.Annotations[VersionAnnotation]
}

func (r Release) VersionTag() string {
	return r.VersionRef.Tag
}

func (r Release) LabelTags() []string {
	tags := []string{}
	for _, ref := range r.LabelRefs {
		tags = append(tags, ref.Tag)
	}
	sort.Strings(tags)
	return tags
}

func (r Release) SizeHuman() string {
	size := r.Binary.Desc.Size
	if h := size / 1024 / 1024 / 1024; h != 0 {
		return fmt.Sprintf("%dGiB", h)
	}
	if h := size / 1024 / 1024; h != 0 {
		return fmt.Sprintf("%dMiB", h)
	}
	if h := size / 1024; h != 0 {
		return fmt.Sprintf("%dKiB", h)
	}
	return fmt.Sprintf("%dB", size)
}

func (r Release) Println() {
	mi := func(a any) string {
		buf, err := json.MarshalIndent(a, "", "  ")
		if err != nil {
			return err.Error()
		}
		return string(buf)
	}
	fmt.Println(mi(r))
}

func (r Release) BinaryFileName() string {
	return r.BaseName + "." + r.Version()
}

func (r Release) SyncFS(ctx context.Context, cli *Client, dirname string, dry, force, links bool) error {
	if err := r.SyncBinary(ctx, cli, dirname, dry, force); err != nil {
		return err
	}
	if !links {
		return nil
	}
	if err := r.SyncSymLinks(ctx, cli, dirname, dry); err != nil {
		return err
	}
	return nil
}

func (r Release) SyncBinary(ctx context.Context, cli *Client, dirname string, dry, force bool) error {
	fname := dirname + "/" + r.BinaryFileName()

	file, err := func() (*os.File, error) {
		if fi, err := cli.osLstat(fname); errors.Is(err, fs.ErrNotExist) {
			cli.LogInfo("%s: does not exist, needs download.", fname)
		} else if err != nil {
			return nil, err
		} else if fi.Mode().Type()&fs.ModeSymlink != 0 {
			if dry {
				cli.LogInfo("%s: is a symlink, would remove.", fname)
			} else {
				cli.LogInfo("%s: is a symlink, removing...", fname)
				if err := cli.osRemove(fname); err != nil {
					return nil, err
				}
			}
		} else if !fi.Mode().IsRegular() {
			return nil, fmt.Errorf("%s: is a %v.", fname, fi.Mode().Type())
		} else if !force {
			cli.LogInfo("%s: already exists.", fname)
			return nil, nil
		} else {
			cli.LogInfo("%s: already exists, but force=%t.", fname, force)
		}

		if dry {
			cli.LogInfo("%s: dry-run, not downloading.", fname)
			return nil, nil
		} else {
			return cli.osOpenFile(fname, os.O_CREATE|os.O_TRUNC|os.O_RDWR, 0o775)
		}
	}()

	if file == nil {
		// file will be nil whenever we don't have to do anything,
		// so return err whether its nil or not.
		return err
	}

	cli.LogInfo("%s: downloading...", fname)
	if err := cli.ReadBlob(ctx, r.Binary, file); err != nil {
		if err := file.Close(); err != nil {
			cli.LogInfo("%s: error closing file: %v", fname, err)
		}
		return err
	}
	cli.LogInfo("%s: download complete.", fname)

	return file.Close()
}

func (r Release) SyncSymLinks(ctx context.Context, cli *Client, dirname string, dry bool) error {
	errs := []error{}
	for _, label := range r.LabelTags() {
		lname := dirname + "/" + r.BaseName + "." + label
		errs = append(errs, cli.symlink(r.BinaryFileName(), lname, dry))
	}
	return errors.Join(errs...)
}

load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(default_visibility = ["//research/infra:internal"])

go_library(
    name = "augment",
    srcs = [
        "backends.go",
        "github.go",
        "paths.go",
        "ssh.go",
        "users.go",
    ],
    importpath = "github.com/augmentcode/augment/research/infra/lib/augment",
    deps = [
        "//infra/lib/github",
        "//infra/lib/k8s",
        "//infra/lib/logger",
        "//infra/lib/ssh",
        "//research/infra/cfg/lib:cfglib_go",
        "@org_golang_x_sync//errgroup",
    ],
)

go_test(
    name = "augment_test",
    srcs = [
        "backends_test.go",
        "paths_test.go",
        "ssh_test.go",
        "users_test.go",
    ],
    embed = [
        ":augment",
    ],
    gotags = ["testing"],
    deps = [
        "//infra/lib/k8s",
        "//infra/lib/logger",
        "@com_github_google_go_cmp//cmp",
        "@com_github_google_go_cmp//cmp/cmpopts",
        "@io_k8s_api//core/v1:core",
    ],
)

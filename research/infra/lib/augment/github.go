package augment

import (
	"context"
	"fmt"
	"strings"

	"github.com/augmentcode/augment/infra/lib/github"
)

// GitHubUserName gets the user's stored GitHub username.
func (c Clients) GitHubUserName(ctx context.Context, user string) (string, error) {
	cm, err := c.k8s.GetConfigMap(ctx, c.ghUsersConfigMap)
	if err != nil {
		return "", err
	}
	return cm.Key(user), nil
}

// GitHubUserNameSet sets the user's store GitHub username.
func (c Clients) GitHubUserNameSet(ctx context.Context, user, val string) error {
	cm, err := c.k8s.GetConfigMap(ctx, c.ghUsersConfigMap)
	if err != nil {
		return err
	}
	_, err = cm.SetKey(ctx, c.k8s, user, val)
	return err
}

// GitHubSSHKeys gets the user's SSH keys from GitHub.
func (c Clients) GitHubSSHKeys(ctx context.Context, user string) (string, error) {
	ghuser, err := c.GitHubUserName(ctx, user)
	if err != nil {
		return "", err
	}
	if ghuser == "" {
		return "", fmt.Errorf("no GitHub username found for user %s", user)
	}
	return c.GitHubSSHKeysForGHUser(ctx, ghuser)
}

// GitHubSSHKeysForGHUser gets the github user's SSH keys from GitHub.
func (c Clients) GitHubSSHKeysForGHUser(ctx context.Context, ghuser string) (string, error) {
	keys, _, err := c.GitHub().UserSSHAuthorizedKeys(ctx, ghuser)
	return keys, err
}

// GitHubTokenGeneratorFromSecret returns a GitHub TokenGenerator from a K8s secret.
func (c Clients) GitHubTokenGeneratorFromSecret(ctx context.Context, secname string, opts ...github.TokenGeneratorOpt) (*github.TokenGenerator, error) {
	sec, err := c.K8s().GetSecret(ctx, secname)
	if err != nil {
		return nil, err
	}

	appID, err := sec.KeyUint64Err("app_id")
	if err != nil {
		return nil, err
	}
	instID, err := sec.KeyInt64Err("installation_id")
	if err != nil {
		return nil, err
	}
	privKey, err := sec.KeyBytesErr("private-key.pem")
	if err != nil {
		return nil, err
	}

	return github.NewTokenGenerator(appID, instID, privKey, opts...)
}

// GitHubTokenGeneratorFromSecretMount returns a GitHub TokenGenerator from a K8s secret mounted in a pod.
func (c Clients) GitHubTokenGeneratorFromSecretMount(ctx context.Context, mountpoint string, opts ...github.TokenGeneratorOpt) (*github.TokenGenerator, error) {
	return github.NewTokenGenerator(mountpoint+"/app_id", mountpoint+"/installation_id", mountpoint+"/private-key.pem", opts...)
}

// GitHubTokenGeneratorFromArgs returns a GitHub TokenGenerator from a flexible set of args.
// Valid Combos:
//   - <k8s-secret>: One arg, not begining with '/', taken as the name of a k8s secret containing app_id, installation_id, and private-key.pem keys.
//   - <k8s-secret-mountpoint>: One arg, begining with '/', taken as the mountpoint of a k8s secret containing app_id, installation_id, and private-key.pem keys.
//   - <app-id> <key>: Two args, each may be a literal value or a path to a file containing the value. The installation ID is `-1`.
//   - <app-id> <installation-id> <private-key>: Three args, each may be a literal value or a path to a file containing the value.
//
// This is mostly useful for CLIs, but pulled into a library since it's used multiple times and easier to test here.
func (c Clients) GitHubTokenGeneratorFromArgs(ctx context.Context, args []string, opts ...github.TokenGeneratorOpt) (*github.TokenGenerator, error) {
	if len(args) == 1 {
		if strings.HasPrefix(args[0], "/") {
			mountpoint := args[0]
			return c.GitHubTokenGeneratorFromSecretMount(ctx, mountpoint, opts...)
		} else {
			secname := args[0]
			return c.GitHubTokenGeneratorFromSecret(ctx, secname, opts...)
		}
	} else if len(args) == 2 {
		appID, privKey := args[0], args[1]
		return github.NewTokenGenerator(appID, int64(-1), privKey)
	} else if len(args) == 3 {
		appID, instID, privKey := args[0], args[1], args[2]
		return github.NewTokenGenerator(appID, instID, privKey)
	} else {
		return nil, fmt.Errorf("GitHubTokenGeneratorFromArgs(): only 1, 2, or 3 args are supported, recieved %d", len(args))
	}
}

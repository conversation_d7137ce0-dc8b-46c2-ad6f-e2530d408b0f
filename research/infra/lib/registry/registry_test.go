package registry

import (
	"context"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
)

////////////////////////////////////////////////////////////////////////////////
//
// REST Calls
//

func TestReq(t *testing.T) {
	tests := map[string]struct {
		inUser   string
		inPass   string
		inMethod string
		inPath   string
		inOpts   []reqOpt

		mCalls []mcall

		wRespCode int
		wRespH    map[string]string
		wRespBody string
		wErr      string
	}{
		"fail-build-request": {
			inPath: "\n",
			wErr:   "invalid control character in URL",
		},
		"get-ok-with-auth": {
			inUser:   "user0",
			inPass:   "pass0",
			inMethod: http.MethodGet,
			inPath:   "foopath",
			inOpts:   nil,

			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/foopath",
					wReqH: map[string]string{
						"Accept":        "application/vnd.docker.distribution.manifest.v2+json",
						"Authorization": "Basic dXNlcjA6cGFzczA=",
					},
					mRespCode: http.StatusOK,
					mRespH:    nil,
					mRespBody: "_RESPONSE_BODY_",
				},
			},

			wRespCode: http.StatusOK,
			wRespBody: "_RESPONSE_BODY_",
			wErr:      "",
		},
		"get-ok-with-opts": {
			inMethod: http.MethodGet,
			inPath:   "foopath",
			inOpts: []reqOpt{
				rParam("p1", "v1"),
				rHeader("H1", "V2"),
			},

			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/foopath?p1=v1",
					wReqH: map[string]string{
						"h1": "V2",
					},
					mRespCode: http.StatusOK,
					mRespH:    nil,
					mRespBody: "_RESPONSE_BODY_",
				},
			},

			wRespCode: http.StatusOK,
			wRespBody: "_RESPONSE_BODY_",
			wErr:      "",
		},
		"post-ok-with-resp-headers": {
			inMethod: http.MethodPost,
			inPath:   "foopath",

			mCalls: []mcall{
				{
					wReqMethod: http.MethodPost,
					wReqURL:    "/v2/foopath",
					mRespCode:  http.StatusOK,
					mRespH:     map[string]string{"rh1": "val0"},
					mRespBody:  "_RESPONSE_BODY_",
				},
			},

			wRespCode: http.StatusOK,
			wRespBody: "_RESPONSE_BODY_",
			wRespH:    map[string]string{"rh1": "val0"},
			wErr:      "",
		},
		"post-4xx": {
			inMethod: http.MethodPost,
			inPath:   "foopath",

			mCalls: []mcall{
				{
					wReqMethod: http.MethodPost,
					wReqURL:    "/v2/foopath",
					mRespCode:  http.StatusBadRequest,
				},
			},

			wRespCode: http.StatusBadRequest,
			wErr:      "",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()
			addr := mkMock(t, tc.mCalls...)
			c := New(addr, tc.inUser, tc.inPass)

			gotResp, gotBuf, gotErr := c.req(ctx, tc.inMethod, tc.inPath, tc.inOpts...)
			gotCode := 0
			gotHeaders := http.Header{}
			if gotResp != nil {
				gotCode = gotResp.StatusCode
				gotHeaders = gotResp.Header
			}

			if got, want := gotCode, tc.wRespCode; got != want {
				t.Errorf("ResponseCode: got %v, want %v.", got, want)
			}
			for k, v := range tc.wRespH {
				if got, want := gotHeaders.Get(k), v; got != want {
					t.Errorf("ResponseHeader[%s]: got %v, want %v.", k, got, want)
				}
			}
			if diff := cmp.Diff(tc.wRespBody, string(gotBuf)); diff != "" {
				t.Errorf("ResponseBody: -want +got:%s", diff)
			}

			if gotErr == nil {
				if tc.wErr != "" {
					t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
				}
			} else if tc.wErr == "" {
				t.Errorf("Err: got %v, want no error.", gotErr)
			} else if !strings.Contains(gotErr.Error(), tc.wErr) {
				t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
			}
		})
	}
}

func TestGet(t *testing.T) {
	tests := map[string]struct {
		inUser string
		inPass string
		inPath string
		inOpts []reqOpt

		mCalls []mcall

		wRespCode int
		wRespH    map[string]string
		wRespBody string
		wErr      string
	}{
		"get-fail-build-request": {
			inPath: "\n",
			wErr:   "invalid control character in URL",
		},
		"get-fail-non200": {
			inPath: "foopath",

			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/foopath",
					mRespCode:  http.StatusAccepted,
					mRespBody:  "_RESPONSE_BODY_",
				},
			},

			wRespCode: http.StatusAccepted,
			wRespBody: "_RESPONSE_BODY_",
			wErr:      "202 Accepted",
		},
		"get-fail-link-header": {
			inPath: "foopath",

			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/foopath",
					mRespCode:  http.StatusOK,
					mRespH:     map[string]string{"link": "_link_"},
					mRespBody:  "_RESPONSE_BODY_",
				},
			},

			wRespCode: http.StatusOK,
			wRespBody: "_RESPONSE_BODY_",
			wErr:      "Unhandled Link header",
		},
		"get-ok-with-auth": {
			inUser: "user0",
			inPass: "pass0",
			inPath: "foopath",
			inOpts: nil,

			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/foopath",
					wReqH: map[string]string{
						"Accept":        "application/vnd.docker.distribution.manifest.v2+json",
						"Authorization": "Basic dXNlcjA6cGFzczA=",
					},
					mRespCode: http.StatusOK,
					mRespH:    nil,
					mRespBody: "_RESPONSE_BODY_",
				},
			},

			wRespCode: http.StatusOK,
			wRespBody: "_RESPONSE_BODY_",
			wErr:      "",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()
			addr := mkMock(t, tc.mCalls...)
			c := New(addr, tc.inUser, tc.inPass)

			gotResp, gotBuf, gotErr := c.get(ctx, tc.inPath, tc.inOpts...)
			gotCode := 0
			gotHeaders := http.Header{}
			if gotResp != nil {
				gotCode = gotResp.StatusCode
				gotHeaders = gotResp.Header
			}

			if got, want := gotCode, tc.wRespCode; got != want {
				t.Errorf("ResponseCode: got %v, want %v.", got, want)
			}
			for k, v := range tc.wRespH {
				if got, want := gotHeaders.Get(k), v; got != want {
					t.Errorf("ResponseHeader[%s]: got %v, want %v.", k, got, want)
				}
			}
			if diff := cmp.Diff(tc.wRespBody, string(gotBuf)); diff != "" {
				t.Errorf("ResponseBody: -want +got:%s", diff)
			}

			if gotErr == nil {
				if tc.wErr != "" {
					t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
				}
			} else if tc.wErr == "" {
				t.Errorf("Err: got %v, want no error.", gotErr)
			} else if !strings.Contains(gotErr.Error(), tc.wErr) {
				t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
			}
		})
	}
}

func TestDelete(t *testing.T) {
	tests := map[string]struct {
		inUser string
		inPass string
		inPath string
		inOpts []reqOpt

		mCalls []mcall

		wRespCode int
		wRespH    map[string]string
		wRespBody string
		wErr      string
	}{
		"delete-fail-build-request": {
			inPath: "\n",
			wErr:   "invalid control character in URL",
		},
		"delete-fail-non202": {
			inPath: "foopath",

			mCalls: []mcall{
				{
					wReqMethod: http.MethodDelete,
					wReqURL:    "/v2/foopath",
					mRespCode:  http.StatusOK,
					mRespBody:  "_RESPONSE_BODY_",
				},
			},

			wRespCode: http.StatusOK,
			wRespBody: "_RESPONSE_BODY_",
			wErr:      "200 OK",
		},
		"delete-ok-with-auth": {
			inUser: "user0",
			inPass: "pass0",
			inPath: "foopath",
			inOpts: nil,

			mCalls: []mcall{
				{
					wReqMethod: http.MethodDelete,
					wReqURL:    "/v2/foopath",
					wReqH: map[string]string{
						"Authorization": "Basic dXNlcjA6cGFzczA=",
					},
					mRespCode: http.StatusAccepted,
					mRespH:    nil,
					mRespBody: "_RESPONSE_BODY_",
				},
			},

			wRespCode: http.StatusAccepted,
			wRespBody: "_RESPONSE_BODY_",
			wErr:      "",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()
			addr := mkMock(t, tc.mCalls...)
			c := New(addr, tc.inUser, tc.inPass)

			gotResp, gotBuf, gotErr := c.delete(ctx, tc.inPath, tc.inOpts...)
			gotCode := 0
			gotHeaders := http.Header{}
			if gotResp != nil {
				gotCode = gotResp.StatusCode
				gotHeaders = gotResp.Header
			}

			if got, want := gotCode, tc.wRespCode; got != want {
				t.Errorf("ResponseCode: got %v, want %v.", got, want)
			}
			for k, v := range tc.wRespH {
				if got, want := gotHeaders.Get(k), v; got != want {
					t.Errorf("ResponseHeader[%s]: got %v, want %v.", k, got, want)
				}
			}
			if diff := cmp.Diff(tc.wRespBody, string(gotBuf)); diff != "" {
				t.Errorf("ResponseBody: -want +got:%s", diff)
			}

			if gotErr == nil {
				if tc.wErr != "" {
					t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
				}
			} else if tc.wErr == "" {
				t.Errorf("Err: got %v, want no error.", gotErr)
			} else if !strings.Contains(gotErr.Error(), tc.wErr) {
				t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
			}
		})
	}
}

////////////////////////////////////////////////////////////////////////////////
//
// API Calls
//

func TestGetCatalogRepos(t *testing.T) {
	tests := map[string]struct {
		mCalls []mcall
		wRepos []string
		wErr   string
	}{
		"fail-4xx": {
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/_catalog?n=1024",
					mRespCode:  http.StatusForbidden,
					mRespBody:  "__INVALID_JSON__",
				},
			},
			wErr: "403",
		},
		"fail-bad-json": {
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/_catalog?n=1024",
					mRespCode:  http.StatusOK,
					mRespBody:  "__INVALID_JSON__",
				},
			},
			wErr: "invalid character",
		},
		"ok": {
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/_catalog?n=1024",
					mRespCode:  http.StatusOK,
					mRespBody: `{
						"repositories": [
							"repoC",
							"repoA",
							"repoB"
						]
					}`,
				},
			},
			wRepos: []string{"repoA", "repoB", "repoC"},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()
			addr := mkMock(t, tc.mCalls...)
			c := New(addr, "user0", "pass0")

			gotRepos, gotErr := c.GetCatalogRepos(ctx)

			if gotErr == nil {
				if tc.wErr != "" {
					t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
				}
			} else if tc.wErr == "" {
				t.Errorf("Err: got %v, want no error.", gotErr)
			} else if !strings.Contains(gotErr.Error(), tc.wErr) {
				t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
			}

			if diff := cmp.Diff(tc.wRepos, gotRepos); diff != "" {
				t.Errorf("Repos: -want +got:%s", diff)
			}
		})
	}
}

func TestGetRepoTags(t *testing.T) {
	tests := map[string]struct {
		inRepo string
		mCalls []mcall
		wTags  []string
		wErr   string
	}{
		"fail-4xx": {
			inRepo: "r0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/tags/list",
					mRespCode:  http.StatusForbidden,
					mRespBody:  "__INVALID_JSON__",
				},
			},
			wErr: "403",
		},
		"notfound-empty-ok": {
			inRepo: "r0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/tags/list",
					mRespCode:  http.StatusNotFound,
					mRespBody:  "__INVALID_JSON__",
				},
			},
		},
		"fail-bad-json": {
			inRepo: "r0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/tags/list",
					mRespCode:  http.StatusOK,
					mRespBody:  "__INVALID_JSON__",
				},
			},
			wErr: "invalid character",
		},
		"ok": {
			inRepo: "r0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/tags/list",
					mRespCode:  http.StatusOK,
					mRespBody: `{
						"tags": [
							"tagC",
							"tagA",
							"tagB"
						]
					}`,
				},
			},
			wTags: []string{"tagA", "tagB", "tagC"},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()
			addr := mkMock(t, tc.mCalls...)
			c := New(addr, "user0", "pass0")

			gotTags, gotErr := c.GetRepoTags(ctx, tc.inRepo)

			if gotErr == nil {
				if tc.wErr != "" {
					t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
				}
			} else if tc.wErr == "" {
				t.Errorf("Err: got %v, want no error.", gotErr)
			} else if !strings.Contains(gotErr.Error(), tc.wErr) {
				t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
			}

			if diff := cmp.Diff(tc.wTags, gotTags); diff != "" {
				t.Errorf("Repos: -want +got:%s", diff)
			}
		})
	}
}

func TestGetManifest(t *testing.T) {
	tests := map[string]struct {
		inRepo        string
		inTag         string
		mCalls        []mcall
		wErr          string
		wDigest       string
		wConfigDigest string
		wSize         uint64
	}{
		"fail-4xx": {
			inRepo: "r0",
			inTag:  "t0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusForbidden,
					mRespBody:  "__INVALID_JSON__",
				},
			},
			wErr: "403",
		},
		"fail-bad-json": {
			inRepo: "r0",
			inTag:  "t0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusOK,
					mRespBody:  "__INVALID_JSON__",
				},
			},
			wErr: "invalid character",
		},
		"ok-no-headers-or-config": {
			inRepo: "r0",
			inTag:  "t0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusOK,
					mRespBody: `{
						"layers": [
							{
								"size": 2,
								"digest": "layer0"
							},
							{
								"size": 3,
								"digest": "layer1"
							}
						]
					}`,
				},
			},
			wDigest:       "",
			wConfigDigest: "",
			wSize:         uint64(5),
		},
		"ok": {
			inRepo: "r0",
			inTag:  "t0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusOK,
					mRespH: map[string]string{
						"docker-content-digest": "dig0:1111",
					},
					mRespBody: `{
						"config": {
							"digest": "dig0:2222"
						},
						"layers": [
							{
								"size": 2,
								"digest": "layer0"
							},
							{
								"size": 3,
								"digest": "layer1"
							}
						]
					}`,
				},
			},
			wDigest:       "dig0:1111",
			wConfigDigest: "dig0:2222",
			wSize:         uint64(5),
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()
			addr := mkMock(t, tc.mCalls...)
			c := New(addr, "user0", "pass0")

			m, gotErr := c.GetManifest(ctx, tc.inRepo, tc.inTag)

			if gotErr == nil {
				if tc.wErr != "" {
					t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
				}
			} else if tc.wErr == "" {
				t.Errorf("Err: got %v, want no error.", gotErr)
			} else if !strings.Contains(gotErr.Error(), tc.wErr) {
				t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
			}

			if gotErr != nil {
				return
			}
			if got, want := m.Digest(), tc.wDigest; got != want {
				t.Errorf("Digest: got %v, want %v.", got, want)
			}
			if got, want := m.ConfigDigest(), tc.wConfigDigest; got != want {
				t.Errorf("ConfigDigest: got %v, want %v.", got, want)
			}
			if got, want := m.TotalSize(), tc.wSize; got != want {
				t.Errorf("TotalSize: got %v, want %v.", got, want)
			}
		})
	}
}

func TestGetBlob(t *testing.T) {
	tests := map[string]struct {
		inRepo    string
		inDigest  string
		mCalls    []mcall
		wRespCode int
		wRespBody string
		wErr      string
	}{
		"fail-4xx": {
			inRepo:   "r0",
			inDigest: "d0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/blobs/d0",
					mRespCode:  http.StatusForbidden,
					mRespBody:  "__BODY__",
				},
			},
			wErr: "403",
		},
		"ok": {
			inRepo:   "r0",
			inDigest: "d0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/blobs/d0",
					mRespCode:  http.StatusOK,
					mRespBody:  "__BODY__",
				},
			},
			wRespCode: http.StatusOK,
			wRespBody: "__BODY__",
			wErr:      "",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()
			addr := mkMock(t, tc.mCalls...)
			c := New(addr, "user0", "pass0")

			gotResp, gotBuf, gotErr := c.GetBlob(ctx, tc.inRepo, tc.inDigest)
			gotCode := 0
			if gotResp != nil {
				gotCode = gotResp.StatusCode
			}

			if got, want := gotCode, tc.wRespCode; got != want {
				t.Errorf("ResponseCode: got %v, want %v.", got, want)
			}
			if diff := cmp.Diff(tc.wRespBody, string(gotBuf)); diff != "" {
				t.Errorf("ResponseBody: -want +got:%s", diff)
			}

			if gotErr == nil {
				if tc.wErr != "" {
					t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
				}
			} else if tc.wErr == "" {
				t.Errorf("Err: got %v, want no error.", gotErr)
			} else if !strings.Contains(gotErr.Error(), tc.wErr) {
				t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
			}
		})
	}
}

func TestDeleteManifest(t *testing.T) {
	tests := map[string]struct {
		inRepo    string
		inDigest  string
		mCalls    []mcall
		wRespCode int
		wRespBody string
		wErr      string
	}{
		"fail-4xx": {
			inRepo:   "r0",
			inDigest: "d0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodDelete,
					wReqURL:    "/v2/r0/manifests/d0",
					mRespCode:  http.StatusForbidden,
					mRespBody:  "__BODY__",
				},
			},
			wRespBody: "__BODY__",
			wRespCode: http.StatusForbidden,
			wErr:      "403",
		},
		"ok": {
			inRepo:   "r0",
			inDigest: "d0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodDelete,
					wReqURL:    "/v2/r0/manifests/d0",
					mRespCode:  http.StatusAccepted,
					mRespBody:  "__BODY__",
				},
			},
			wRespCode: http.StatusAccepted,
			wRespBody: "__BODY__",
			wErr:      "",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()
			addr := mkMock(t, tc.mCalls...)
			c := New(addr, "user0", "pass0")

			gotResp, gotBuf, gotErr := c.DeleteManifest(ctx, tc.inRepo, tc.inDigest)
			gotCode := 0
			if gotResp != nil {
				gotCode = gotResp.StatusCode
			}

			if got, want := gotCode, tc.wRespCode; got != want {
				t.Errorf("ResponseCode: got %v, want %v.", got, want)
			}
			if diff := cmp.Diff(tc.wRespBody, string(gotBuf)); diff != "" {
				t.Errorf("ResponseBody: -want +got:%s", diff)
			}

			if gotErr == nil {
				if tc.wErr != "" {
					t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
				}
			} else if tc.wErr == "" {
				t.Errorf("Err: got %v, want no error.", gotErr)
			} else if !strings.Contains(gotErr.Error(), tc.wErr) {
				t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
			}
		})
	}
}

////////////////////////////////////////////////////////////////////////////////
//
// Higher Level Calls / Wrappers
//

func TestGetImageConfig(t *testing.T) {
	tests := map[string]struct {
		inRepo   string
		inDigest string
		mCalls   []mcall
		wErr     string
		wCreated time.Time
	}{
		"fail-4xx": {
			inRepo:   "r0",
			inDigest: "d0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/blobs/d0",
					mRespCode:  http.StatusForbidden,
					mRespBody:  "__BAD_JSON__",
				},
			},
			wErr: "403",
		},
		"fail-bad-json": {
			inRepo:   "r0",
			inDigest: "d0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/blobs/d0",
					mRespCode:  http.StatusOK,
					mRespBody:  "__BAD_JSON__",
				},
			},
			wErr: "invalid character",
		},
		"ok": {
			inRepo:   "r0",
			inDigest: "d0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/blobs/d0",
					mRespCode:  http.StatusOK,
					mRespBody: `{
						"created": "1970-01-01T00:00:01.000000Z"
					}`,
				},
			},
			wCreated: time.Unix(1, 0),
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()
			addr := mkMock(t, tc.mCalls...)
			c := New(addr, "user0", "pass0")

			gotImgCfg, gotErr := c.GetImageConfig(ctx, tc.inRepo, tc.inDigest)
			if gotErr == nil {
				if tc.wErr != "" {
					t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
				}
			} else if tc.wErr == "" {
				t.Errorf("Err: got %v, want no error.", gotErr)
			} else if !strings.Contains(gotErr.Error(), tc.wErr) {
				t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
			}

			if gotErr != nil {
				return
			}
			if got, gotErr := gotImgCfg.Created(); gotErr != nil {
				t.Errorf("Created(): %v.", gotErr)
			} else if want := tc.wCreated; !got.Equal(want) {
				t.Errorf("Created(): got %v, want %v.", got, want)
			}
		})
	}
}

func TestGetImage(t *testing.T) {
	tests := map[string]struct {
		inRepo   string
		inTag    string
		mCalls   []mcall
		wErr     string
		wDigest  string
		wSize    uint64
		wCreated time.Time
	}{
		"fail-manifest-4xx": {
			inRepo: "r0",
			inTag:  "t0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusForbidden,
					mRespBody:  "__BAD_JSON__",
				},
			},
			wErr: "403",
		},
		"fail-imageconfig-4xx": {
			inRepo: "r0",
			inTag:  "t0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusOK,
					mRespBody: `{
						"config": {
							"digest": "cfg00"
						}
					}`,
				},
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/blobs/cfg00",
					mRespCode:  http.StatusForbidden,
					mRespBody:  "__BAD_JSON__",
				},
			},
			wErr: "403",
		},
		"ok-with-config": {
			inRepo: "r0",
			inTag:  "t0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusOK,
					mRespBody: `{
						"config": {
							"digest": "cfg00"
						},
						"layers": [
							{
								"size": 2,
								"digest": "layer0"
							},
							{
								"size": 3,
								"digest": "layer1"
							}
						]
					}`,
					mRespH: map[string]string{"docker-content-digest": "d0"},
				},
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/blobs/cfg00",
					mRespCode:  http.StatusOK,
					mRespBody: `{
						"created": "1970-01-01T00:00:01.000000Z"
					}`,
				},
			},
			wCreated: time.Unix(1, 0),
			wDigest:  "d0",
			wSize:    uint64(5),
		},
		"ok-no-config": {
			inRepo: "r0",
			inTag:  "t0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusOK,
					mRespBody: `{
						"layers": [
							{
								"size": 2,
								"digest": "layer0"
							},
							{
								"size": 3,
								"digest": "layer1"
							}
						]
					}`,
					mRespH: map[string]string{"docker-content-digest": "d0"},
				},
			},
			wCreated: time.Time{},
			wDigest:  "d0",
			wSize:    uint64(5),
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()
			addr := mkMock(t, tc.mCalls...)
			c := New(addr, "user0", "pass0")

			gotImg, gotErr := c.GetImage(ctx, tc.inRepo, tc.inTag)
			if gotErr == nil {
				if tc.wErr != "" {
					t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
				}
			} else if tc.wErr == "" {
				t.Errorf("Err: got %v, want no error.", gotErr)
			} else if !strings.Contains(gotErr.Error(), tc.wErr) {
				t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
			}

			if gotErr != nil {
				return
			}

			if got, want := gotImg.Digest(), tc.wDigest; got != want {
				t.Errorf("Digest(): got %v, want %v.", got, want)
			}
			if got, want := gotImg.Created(), tc.wCreated; !got.Equal(want) {
				t.Errorf("Created(): got %v, want %v.", got, want)
			}
			if got, want := gotImg.Size(), tc.wSize; got != want {
				t.Errorf("Size(): got %v, want %v.", got, want)
			}
		})
	}
}

func TestGetRepoImages(t *testing.T) {
	// NOTE(mattm): Since GetRepoImages() uses a worker pool, just test based on a single image.
	tests := map[string]struct {
		inRepo  string
		mCalls  []mcall
		wErr    string
		wImages []string
	}{
		"get-tags-error": {
			inRepo: "r0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/tags/list",
					mRespCode:  http.StatusForbidden,
					mRespBody:  "__BAD_JSON__",
				},
			},
			wErr:    "403 Forbidden",
			wImages: []string{},
		},
		"zero-tags": {
			inRepo: "r0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/tags/list",
					mRespCode:  http.StatusOK,
					mRespBody:  `{"tags": []}`,
				},
			},
			wImages: []string{},
		},
		"one-tag-get-image-error": {
			inRepo: "r0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/tags/list",
					mRespCode:  http.StatusOK,
					mRespBody:  `{"tags": ["t0"]}`,
				},
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusInternalServerError,
					mRespBody:  `__BAD_JSON__`,
				},
			},
			wErr:    "500",
			wImages: []string{},
		},
		"one-tag-ok": {
			inRepo: "r0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/tags/list",
					mRespCode:  http.StatusOK,
					mRespBody:  `{"tags": ["t0"]}`,
				},
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusOK,
					mRespBody:  `{}`,
				},
			},
			wImages: []string{"r0:t0"},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()
			addr := mkMock(t, tc.mCalls...)
			c := New(addr, "user0", "pass0")

			gotImages := []string{}
			gotErr := c.GetRepoImages(ctx, tc.inRepo, func(img *Image) {
				gotImages = append(gotImages, img.String())
			})

			if gotErr == nil {
				if tc.wErr != "" {
					t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
				}
			} else if tc.wErr == "" {
				t.Errorf("Err: got %v, want no error.", gotErr)
			} else if !strings.Contains(gotErr.Error(), tc.wErr) {
				t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
			}

			if diff := cmp.Diff(tc.wImages, gotImages); diff != "" {
				t.Errorf("Images: -want +got:\n%s", diff)
			}
		})
	}
}

func TestGetRepo(t *testing.T) {
	// NOTE(mattm): Since GetRepoImages() uses a worker pool, just test based on a single image.
	tests := map[string]struct {
		inRepo  string
		mCalls  []mcall
		wErr    string
		wImages []string
		wLen    int
	}{
		"get-repo-images-error": {
			inRepo: "r0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/tags/list",
					mRespCode:  http.StatusForbidden,
					mRespBody:  "__BAD_JSON__",
				},
			},
			wErr:    "403 Forbidden",
			wImages: []string{},
		},
		"one-tag-ok": {
			inRepo: "r0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/tags/list",
					mRespCode:  http.StatusOK,
					mRespBody:  `{"tags": ["t0"]}`,
				},
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusOK,
					mRespBody:  `{}`,
				},
			},
			wImages: []string{"r0:t0"},
			wLen:    1,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()
			addr := mkMock(t, tc.mCalls...)
			c := New(addr, "user0", "pass0")

			gotRepo, gotErr := c.GetRepo(ctx, tc.inRepo)

			if gotErr == nil {
				if tc.wErr != "" {
					t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
				}
			} else if tc.wErr == "" {
				t.Errorf("Err: got %v, want no error.", gotErr)
			} else if !strings.Contains(gotErr.Error(), tc.wErr) {
				t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
			}

			if gotErr != nil {
				return
			}
			if got, want := gotRepo.Len(), tc.wLen; got != want {
				t.Errorf("Err: got %v, want %v.", got, want)
			}

			gotImages := []string{}
			for _, img := range gotRepo.Images() {
				gotImages = append(gotImages, img.String())
			}
			if diff := cmp.Diff(tc.wImages, gotImages); diff != "" {
				t.Errorf("Images: -want +got:\n%s", diff)
			}
		})
	}
}

func TestGetRepos(t *testing.T) {
	// NOTE(mattm): Since GetRepoImages() uses a worker pool, just test based on a single image.
	tests := map[string]struct {
		inRepos []string
		mCalls  []mcall
		wErr    string
		wRepos  []string
		wImages []string
	}{
		"given-repo-get-err": {
			inRepos: []string{"r0"},
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/tags/list",
					mRespCode:  http.StatusForbidden,
					mRespBody:  "__BAD_JSON__",
				},
			},
			wErr:    "403 Forbidden",
			wRepos:  []string{},
			wImages: []string{},
		},
		"given-repo-ok": {
			inRepos: []string{"r0"},
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/tags/list",
					mRespCode:  http.StatusOK,
					mRespBody:  `{"tags": ["t0"]}`,
				},
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusOK,
					mRespBody:  `{}`,
				},
			},
			wRepos:  []string{"r0"},
			wImages: []string{"r0:t0"},
		},
		"all-repos-catalog-err": {
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/_catalog?n=1024",
					mRespCode:  http.StatusForbidden,
					mRespBody:  "__BAD_JSON__",
				},
			},
			wErr:    "403 Forbidden",
			wRepos:  []string{},
			wImages: []string{},
		},
		"all-repos-ok": {
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/_catalog?n=1024",
					mRespCode:  http.StatusOK,
					mRespBody: `{
						"repositories": ["r0"]
					}`,
				},
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/tags/list",
					mRespCode:  http.StatusOK,
					mRespBody:  `{"tags": ["t0"]}`,
				},
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusOK,
					mRespBody:  `{}`,
				},
			},
			wRepos:  []string{"r0"},
			wImages: []string{"r0:t0"},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()
			addr := mkMock(t, tc.mCalls...)
			c := New(addr, "user0", "pass0")

			gotRepos := []string{}
			gotImages := []string{}
			gotErr := c.GetRepos(ctx, tc.inRepos, func(repo *Repo) {
				gotRepos = append(gotRepos, repo.Name())
				for _, img := range repo.Images() {
					gotImages = append(gotImages, img.String())
				}
			})

			if gotErr == nil {
				if tc.wErr != "" {
					t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
				}
			} else if tc.wErr == "" {
				t.Errorf("Err: got %v, want no error.", gotErr)
			} else if !strings.Contains(gotErr.Error(), tc.wErr) {
				t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
			}

			if diff := cmp.Diff(tc.wRepos, gotRepos); diff != "" {
				t.Errorf("REpos: -want +got:\n%s", diff)
			}
			if diff := cmp.Diff(tc.wImages, gotImages); diff != "" {
				t.Errorf("Images: -want +got:\n%s", diff)
			}
		})
	}
}

func TestDeleteImage(t *testing.T) {
	tests := map[string]struct {
		inRepo    string
		inTag     string
		mCalls    []mcall
		wErr      string
		wRespCode int
		wRespBody string
	}{
		"fail-image-4xx": {
			inRepo: "r0",
			inTag:  "t0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusForbidden,
					mRespBody:  "__BAD_JSON__",
				},
			},
			wErr: "403",
		},
		"fail-nodigest": {
			inRepo: "r0",
			inTag:  "t0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusOK,
					mRespBody: `{
						"config": {},
						"layers": []
					}`,
				},
			},
			wErr: "no digest found for r0:t0",
		},
		"fail-delete": {
			inRepo: "r0",
			inTag:  "t0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusOK,
					mRespBody: `{
						"config": {},
						"layers": []
					}`,
					mRespH: map[string]string{"docker-content-digest": "d0"},
				},
				{
					wReqMethod: http.MethodDelete,
					wReqURL:    "/v2/r0/manifests/d0",
					mRespCode:  http.StatusInternalServerError,
				},
			},
			wErr:      "500 Internal Server Error",
			wRespCode: http.StatusInternalServerError,
		},
		"ok": {
			inRepo: "r0",
			inTag:  "t0",
			mCalls: []mcall{
				{
					wReqMethod: http.MethodGet,
					wReqURL:    "/v2/r0/manifests/t0",
					mRespCode:  http.StatusOK,
					mRespBody: `{
						"config": {},
						"layers": []
					}`,
					mRespH: map[string]string{"docker-content-digest": "d0"},
				},
				{
					wReqMethod: http.MethodDelete,
					wReqURL:    "/v2/r0/manifests/d0",
					mRespCode:  http.StatusAccepted,
				},
			},
			wRespCode: http.StatusAccepted,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ctx := context.Background()
			addr := mkMock(t, tc.mCalls...)
			c := New(addr, "user0", "pass0")

			gotResp, gotBuf, gotErr := c.DeleteImage(ctx, tc.inRepo, tc.inTag)
			gotCode := 0
			if gotResp != nil {
				gotCode = gotResp.StatusCode
			}

			if got, want := gotCode, tc.wRespCode; got != want {
				t.Errorf("ResponseCode: got %v, want %v.", got, want)
			}
			if diff := cmp.Diff(tc.wRespBody, string(gotBuf)); diff != "" {
				t.Errorf("ResponseBody: -want +got:%s", diff)
			}

			if gotErr == nil {
				if tc.wErr != "" {
					t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
				}
			} else if tc.wErr == "" {
				t.Errorf("Err: got %v, want no error.", gotErr)
			} else if !strings.Contains(gotErr.Error(), tc.wErr) {
				t.Errorf("Err: got %v, want error containing %v.", gotErr, tc.wErr)
			}
		})
	}
}

////////////////////////////////////////////////////////////////////////////////
//
// Mock HTTP Server
//

func mkMock(t *testing.T, calls ...mcall) string {
	m := mock{mcalls: calls}
	server := httptest.NewServer(http.HandlerFunc(
		func(rw http.ResponseWriter, req *http.Request) {
			m.handle(t, rw, req)
		},
	))
	t.Cleanup(server.Close)
	return server.URL
}

type mcall struct {
	wReqMethod string
	wReqURL    string
	wReqH      map[string]string
	wReqBody   string

	mRespCode int
	mRespH    map[string]string
	mRespBody string
}

type mock struct {
	mcalls []mcall
}

func (m *mock) pop(t *testing.T) mcall {
	t.Helper()
	if len(m.mcalls) == 0 {
		t.Fatalf("No more mock calls.")
	}
	mcall := m.mcalls[0]
	m.mcalls = m.mcalls[1:]
	return mcall
}

func (m *mock) handle(t *testing.T, rw http.ResponseWriter, req *http.Request) {
	t.Helper()
	defer req.Body.Close()

	mcall := m.pop(t)

	// Assert Request

	if got, want := req.Method, mcall.wReqMethod; got != want {
		t.Errorf("Request Method: got %v, want %v.", got, want)
	}
	if got, want := req.URL.String(), mcall.wReqURL; got != want {
		t.Errorf("Request URL: got %v, want %v.", got, want)
	}
	for k, v := range mcall.wReqH {
		if got, want := req.Header.Get(k), v; got != want {
			t.Errorf("Request Header [%s]: got %v, want %v.", k, got, want)
		}
	}
	if buf, err := io.ReadAll(req.Body); err != nil {
		t.Fatalf("Request Body: %v.", err)
	} else if diff := cmp.Diff(mcall.wReqBody, string(buf)); diff != "" {
		t.Errorf("Request Body: -want +got:\n%s", diff)
	}

	// Build Response

	for k, v := range mcall.mRespH {
		rw.Header().Add(k, v)
	}
	rw.WriteHeader(mcall.mRespCode)
	if _, err := rw.Write([]byte(mcall.mRespBody)); err != nil {
		t.Errorf("Response Body: %v.", err)
	}
}

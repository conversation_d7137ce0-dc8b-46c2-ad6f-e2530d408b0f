// Package registry provides a Docker Registry V2 client.
package registry

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"

	"golang.org/x/sync/errgroup"
)

// Client is a Registery V1 client.
type Client struct {
	host string
	user string
	pass string
	base string
	http *http.Client
}

// New builds a new Client. Currently, only basic User:Pass auth is supported (and required).
func New(host, user, pass string) *Client {
	if !(strings.HasPrefix(host, "http://") || strings.HasPrefix(host, "https://")) {
		host = "https://" + host
	}
	return &Client{
		host: host,
		user: user,
		pass: pass,
		base: fmt.Sprintf("%s/v2", host),
		http: http.DefaultClient,
	}
}

////////////////////////////////////////////////////////////////////////////////
//
// REST Calls
//

type reqOpt func(*http.Request)

func rParam(key, value string) reqOpt {
	return func(r *http.Request) {
		q := r.URL.Query()
		q.Add(key, value)
		r.URL.RawQuery = q.Encode()
	}
}

func rHeader(key, value string) reqOpt {
	return func(r *http.Request) {
		r.Header.Add(key, value)
	}
}

// req is a low-level HTTP request to the registry. It takes care of reading the
// response body into a local buffer.
func (c Client) req(ctx context.Context, method, path string, opts ...reqOpt) (*http.Response, []byte, error) {
	// Setup request, headers, auth, opts...

	url := c.base + "/" + path
	req, err := http.NewRequestWithContext(ctx, method, url, nil)
	if err != nil {
		return nil, nil, err
	}
	req.SetBasicAuth(c.user, c.pass)
	req.Header.Add("Accept", "application/vnd.docker.distribution.manifest.v2+json")
	for _, o := range opts {
		o(req)
	}

	// Make the request (network IO).

	resp, err := c.http.Do(req)
	if err != nil {
		return resp, nil, err
	}
	defer resp.Body.Close()

	// Stream the response body into local memory (network IO).

	buf, err := io.ReadAll(resp.Body)
	if err != nil {
		return resp, nil, err
	}

	return resp, buf, nil
}

// get wraps `req()` for HTTP GET calls. Responses other than HTTP 200 are returned as
// an error.
func (c Client) get(ctx context.Context, path string, opts ...reqOpt) (*http.Response, []byte, error) {
	resp, buf, err := c.req(ctx, http.MethodGet, path, opts...)
	if err != nil {
		return resp, buf, err
	}
	if resp.StatusCode != http.StatusOK {
		return resp, buf, fmt.Errorf("%s", resp.Status)
	}
	if resp.Header.Get("Link") != "" {
		return resp, buf, fmt.Errorf("Unhandled Link header")
	}
	return resp, buf, nil
}

// delete wraps `req()` for HTTP DELETE calls.
func (c Client) delete(ctx context.Context, path string, opts ...reqOpt) (*http.Response, []byte, error) {
	resp, buf, err := c.req(ctx, http.MethodDelete, path, opts...)
	if err != nil {
		return resp, buf, err
	}
	if resp.StatusCode != http.StatusAccepted {
		return resp, buf, fmt.Errorf("%s", resp.Status)
	}
	return resp, buf, nil
}

////////////////////////////////////////////////////////////////////////////////
//
// API Calls
//

// GetCatalogRepos returns the list of repos in the registry.
func (c Client) GetCatalogRepos(ctx context.Context) ([]string, error) {
	// NOTE(mattm): Set n=1024 to lazily avoid pagination.
	resp, buf, err := c.get(ctx, "_catalog", rParam("n", "1024"))
	if err != nil {
		return nil, fmt.Errorf("GetCatalogRepos(): %w", err)
	}

	cat, err := NewCatalog(resp, buf)
	if err != nil {
		return nil, fmt.Errorf("GetCatalogRepos(): %w", err)
	}

	return cat.Repos(), nil
}

// GetRepoTags returns the list of tags for a given repo. HTTP NotFound returns
// and empty list, and is not treated as an error.
func (c Client) GetRepoTags(ctx context.Context, repo string) ([]string, error) {
	resp, buf, err := c.get(ctx, repo+"/tags/list")
	// Ignore NotFound
	if resp != nil && resp.StatusCode == http.StatusNotFound {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("GetRepoTags(%s): %w", repo, err)
	}

	t, err := NewTags(resp, buf)
	if err != nil {
		return nil, fmt.Errorf("GetRepoTags(%s): %w", repo, err)
	}

	return t.Tags(), nil
}

// GetManifest returns a raw manifest for a repo. See `GetImage()` for the higher-level
// call which handles the Manifest and ImageConfig together.
func (c Client) GetManifest(ctx context.Context, repo, tag string) (*Manifest, error) {
	resp, buf, err := c.get(ctx, repo+"/manifests/"+tag)
	if err != nil {
		return nil, fmt.Errorf("GetManifest(%s, %s): %w", repo, tag, err)
	}
	m, err := NewManifest(resp, buf)
	if err != nil {
		return nil, fmt.Errorf("GetManifest(%s, %s): %w", repo, tag, err)
	}
	return m, nil
}

// GetBlob returns a raw blob []byte buffer for a repo.
func (c Client) GetBlob(ctx context.Context, repo, digest string) (*http.Response, []byte, error) {
	resp, buf, err := c.get(ctx, repo+"/blobs/"+digest)
	if err != nil {
		return nil, nil, fmt.Errorf("GetBlob(%s, %s): %w", repo, digest, err)
	}
	return resp, buf, nil
}

// DeleteManifest deletes a manifest for a repo.
func (c Client) DeleteManifest(ctx context.Context, repo, digest string) (*http.Response, []byte, error) {
	resp, buf, err := c.delete(ctx, repo+"/manifests/"+digest)
	if err != nil {
		return resp, buf, fmt.Errorf("DeleteManifest(%s, %s): %w", repo, digest, err)
	}
	return resp, buf, err
}

////////////////////////////////////////////////////////////////////////////////
//
// Higher Level Calls / Wrappers
//

// GetImageConfig gets a manifest's config blob and returns it as an unmarshalled struct.
func (c Client) GetImageConfig(ctx context.Context, repo, digest string) (*ImageConfig, error) {
	resp, buf, err := c.GetBlob(ctx, repo, digest)
	if err != nil {
		return nil, err
	}
	return NewImageConfig(resp, buf)
}

// GetImage returns a high-level `Image` struct combining an image `Manifest`
// and `ImageConfig` (makes multiple RPC calls).
func (c Client) GetImage(ctx context.Context, repo, tag string) (*Image, error) {
	m, err := c.GetManifest(ctx, repo, tag)
	if err != nil {
		return nil, err
		// return NewImage(repo, tag, nil, nil), nil
	}
	var ic *ImageConfig
	if d := m.ConfigDigest(); d != "" {
		ic, err = c.GetImageConfig(ctx, repo, d)
		if err != nil {
			return nil, err
		}
	}
	return NewImage(repo, tag, m, ic), nil
}

// GetRepoImages resolves the list of tags (`GetRepoTags()`) for a repo, gets
// each `Image`, and then calls `f()` on each `Image`. `GetImage()` calls are handled
// in parallel.
// NOTE(mattm): `f()` is not expected to be long-running or perform IO. If needed, rework
// this for `f()` to take a context.Context param.
func (c Client) GetRepoImages(ctx context.Context, repo string, f func(*Image)) error {
	tags, err := c.GetRepoTags(ctx, repo)
	if err != nil {
		return err
	}
	grp, gctx := errgroup.WithContext(ctx)
	for _, tag := range tags {
		grp.Go(func() error {
			img, err := c.GetImage(gctx, repo, tag)
			if err == nil {
				f(img)
			}
			return err
		})
	}
	return grp.Wait()
}

// GetRepo calls `GetRepoImages()` in order to return a `Repo` struct.
func (c Client) GetRepo(ctx context.Context, repo string) (*Repo, error) {
	r := NewRepo(repo)
	if err := c.GetRepoImages(ctx, repo, func(image *Image) {
		r.Extend(image)
	}); err != nil {
		return nil, err
	}
	return r, nil
}

// GetRepos is a high level convenience call to acquire multiple (or all) `Repo`s in
// parallel. If `repoNames` is empty, all repos are queried using `GetCatalogRepos()`.
// NOTE(mattm): `f()` is not expected to be long-running or perform IO. If needed, rework
// this for `f()` to take a context.Context param.
func (c Client) GetRepos(ctx context.Context, repoNames []string, f func(*Repo)) error {
	if len(repoNames) == 0 {
		var err error
		repoNames, err = c.GetCatalogRepos(ctx)
		if err != nil {
			return err
		}
	}
	grp, gctx := errgroup.WithContext(ctx)
	for _, repoName := range repoNames {
		grp.Go(func() error {
			repo, err := c.GetRepo(gctx, repoName)
			if err == nil {
				f(repo)
			}
			return err
		})
	}
	return grp.Wait()
}

// DeleteImage unreferences an image from the registry. Note that this resolves
// a digest from the provided `tag` and deletes the digest; the v2 API does not
// support deleting just a tag.
// Also note, this does not free storage from the registry. For that, the
// `garbage-collect` command is needed.
func (c Client) DeleteImage(ctx context.Context, repo, tag string) (*http.Response, []byte, error) {
	img, err := c.GetImage(ctx, repo, tag)
	if err != nil {
		return nil, nil, err
	}
	if img.Digest() == "" {
		return nil, nil, fmt.Errorf("FailedPrecondition: no digest found for %s:%s", repo, tag)
	}
	return c.DeleteManifest(ctx, img.Repo(), img.Digest())
}

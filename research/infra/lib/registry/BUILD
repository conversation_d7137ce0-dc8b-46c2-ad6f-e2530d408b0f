load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(default_visibility = ["//research/infra:internal"])

go_library(
    name = "registry",
    srcs = [
        "blobpool.go",
        "image.go",
        "imagelist.go",
        "json.go",
        "registry.go",
        "repo.go",
    ],
    importpath = "github.com/augmentcode/augment/research/infra/lib/registry",
    deps = [
        "@com_github_dustin_go_humanize//:go-humanize",
        "@org_golang_x_sync//errgroup",
    ],
)

go_test(
    name = "registry_test",
    srcs = [
        "blobpool_test.go",
        "image_test.go",
        "imagelist_test.go",
        "json_test.go",
        "registry_test.go",
        "repo_test.go",
    ],
    embed = [":registry"],
    deps = ["@com_github_google_go_cmp//cmp"],
)

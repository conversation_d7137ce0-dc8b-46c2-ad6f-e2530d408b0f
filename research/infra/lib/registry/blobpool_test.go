package registry

import (
	"testing"
)

func assertBP(t *testing.T, bp *BlobPool, l int, s uint64, refs map[string]int) {
	t.Helper()
	if got, want := bp.<PERSON>(), l; got != want {
		t.<PERSON>rrorf("Len(): got %v, want %v.", got, want)
	}
	if got, want := bp.Size(), s; got != want {
		t.<PERSON><PERSON><PERSON>("Size(): got %v, want %v.", got, want)
	}
	if got, want := len(bp.<PERSON><PERSON>()), 1; got < want {
		t.Errorf("Size(): got %v, want >= %v.", got, want)
	}
	for k, v := range refs {
		if got, want := bp.i[k].refs, v; got != want {
			t.<PERSON>rrorf("Digest[%s].refs: got %v, want %v.", k, got, want)
		}
	}
}

func TestBlobPool(t *testing.T) {
	bp1 := NewBlobPool()
	t.Run("initial pool", func(t *testing.T) {
		assertBP(t, bp1, 0, 0, nil)
	})
	t.Run("tally-d0-1", func(t *testing.T) {
		if err := bp1.Tally("d0", 3, 1); err != nil {
			t.Errorf("Tally() err: got %v, want nil.", err)
		}
		assertBP(t, bp1, 1, 3, map[string]int{"d0": 1})
	})
	t.Run("untally-d0-0", func(t *testing.T) {
		if err := bp1.UnTally("d0", 3, 0); err != nil {
			t.Errorf("UnTally() err: got %v, expected nil.", err)
		}
		assertBP(t, bp1, 1, 3, map[string]int{"d0": 1})
	})
	t.Run("untally-d0-5", func(t *testing.T) {
		if err := bp1.UnTally("d0", 3, 5); err == nil {
			t.Errorf("UnTally() err: got nil, expected an error.")
		} else if got, want := err.Error(), "dropping 5 of 1 refs: cannot untally d0"; got != want {
			t.Errorf("UnTally() err: got %v, want %v.", got, want)
		}
		assertBP(t, bp1, 1, 3, map[string]int{"d0": 1})
	})
	t.Run("tally-d0-5", func(t *testing.T) {
		if err := bp1.Tally("d0", 3, 5); err != nil {
			t.Errorf("Tally() err: got %v, want nil.", err)
		}
		assertBP(t, bp1, 1, 3, map[string]int{"d0": 6})
	})
	t.Run("untally-d0-3", func(t *testing.T) {
		if err := bp1.UnTally("d0", 3, 3); err != nil {
			t.Errorf("UnTally() err: got %v, want nil.", err)
		}
		assertBP(t, bp1, 1, 3, map[string]int{"d0": 3})
	})
	t.Run("tally-d0-1-wrongsize", func(t *testing.T) {
		if err := bp1.Tally("d0", 30, 1); err == nil {
			t.Errorf("Tally() err: got nil, want an error.")
		} else if got, want := err.Error(), "size mismatch 30 != 3: cannot tally d0"; got != want {
			t.Errorf("UnTally() err: got %v, want %v.", got, want)
		}
		assertBP(t, bp1, 1, 3, map[string]int{"d0": 3})
	})
	t.Run("untally-d0-1-wrongsize", func(t *testing.T) {
		if err := bp1.UnTally("d0", 30, 1); err == nil {
			t.Errorf("UnTally() err: got nil, want an error.")
		} else if got, want := err.Error(), "size mismatch 30 != 3: cannot untally d0"; got != want {
			t.Errorf("UnTally() err: got %v, want %v.", got, want)
		}
		assertBP(t, bp1, 1, 3, map[string]int{"d0": 3})
	})
	t.Run("tally-d1-10", func(t *testing.T) {
		if err := bp1.Tally("d1", 42, 10); err != nil {
			t.Errorf("Tally() err: got %v, want nil.", err)
		}
		assertBP(t, bp1, 2, 45, map[string]int{"d0": 3, "d1": 10})
	})

	t.Run("tally-another-pool", func(t *testing.T) {
		bp2 := NewBlobPool()
		bp2.Tally("d1", 42, 20)
		bp2.Tally("d2", 7, 11)
		if err := bp1.TallyPool(bp2); err != nil {
			t.Errorf("TallyPool() err: got %v, want nil.", err)
		}
		assertBP(t, bp1, 3, 52, map[string]int{"d0": 3, "d1": 30, "d2": 11})
	})
	t.Run("untally-another-pool", func(t *testing.T) {
		bp3 := NewBlobPool()
		bp3.Tally("d2", 7, 11)
		if err := bp1.UnTallyPool(bp3); err != nil {
			t.Errorf("UnTallyPool() err: got %v, want nil.", err)
		}
		assertBP(t, bp1, 3, 52, map[string]int{"d0": 3, "d1": 30, "d2": 0})
	})
	t.Run("orphans", func(t *testing.T) {
		o := bp1.OrphanPool()
		assertBP(t, bp1, 3, 52, map[string]int{"d0": 3, "d1": 30, "d2": 0})
		assertBP(t, o, 1, 7, map[string]int{"d0": 0, "d1": 0, "d2": 0})
	})
}

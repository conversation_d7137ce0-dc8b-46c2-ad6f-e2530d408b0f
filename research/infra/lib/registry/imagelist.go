package registry

import (
	"cmp"
	"slices"
	"sync"
	"time"
)

// ImageTags converts a list of images to a list of tags.
func ImageTags(images []*Image) []string {
	ret := []string{}
	for _, image := range images {
		ret = append(ret, image.Tag())
	}
	return ret
}

// ImageList is a container for a list of images. This struct *is* thread-safe.
type ImageList struct {
	images []*Image
	blobs  *BlobPool
	lock   *sync.RWMutex
}

func NewImageList(images ...*Image) *ImageList {
	il := &ImageList{
		blobs: NewBlobPool(),
		lock:  &sync.RWMutex{},
	}
	il.Extend(images...)
	return il
}

func (il *ImageList) Extend(images ...*Image) {
	il.lock.Lock()
	defer il.lock.Unlock()
	for _, image := range images {
		if image.m == nil {
			continue
		}
		for _, layer := range image.m.manifest.Layers {
			il.blobs.Tally(layer.Digest, layer.Size, 1)
		}
	}
	il.images = append(il.images, images...)
}

func (il ImageList) Images() []*Image {
	il.lock.RLock()
	defer il.lock.RUnlock()
	return il.images
}

func (il *ImageList) BlobPool() *BlobPool {
	return il.blobs
}

func (il ImageList) Len() int {
	return len(il.Images())
}

func (il ImageList) Size() uint64 {
	il.lock.RLock()
	defer il.lock.RUnlock()
	return il.blobs.Size()
}

func (il ImageList) SizeHuman() string {
	il.lock.RLock()
	defer il.lock.RUnlock()
	return il.blobs.SizeHuman()
}

func (il ImageList) TagsByOldest() []string {
	return ImageTags(il.ByOldest())
}

func (il ImageList) TagsByNewest() []string {
	return ImageTags(il.ByNewest())
}

func (il ImageList) Oldest() *Image {
	return slices.MinFunc(il.Images(), func(i, j *Image) int {
		return cmp.Compare(i.Created().Unix(), j.Created().Unix())
	})
}

func (il ImageList) Newest() *Image {
	return slices.MaxFunc(il.Images(), func(i, j *Image) int {
		return cmp.Compare(i.Created().Unix(), j.Created().Unix())
	})
}

func (il ImageList) ByOldest() []*Image {
	il.lock.Lock()
	defer il.lock.Unlock()
	slices.SortFunc(il.images, func(i, j *Image) int {
		return cmp.Compare(i.Created().Unix(), j.Created().Unix())
	})
	return il.images
}

func (il ImageList) ByNewest() []*Image {
	il.lock.Lock()
	defer il.lock.Unlock()
	slices.SortFunc(il.images, func(i, j *Image) int {
		return -cmp.Compare(i.Created().Unix(), j.Created().Unix())
	})
	return il.images
}

func (il ImageList) CutOldest(n int, inverse bool) *ImageList {
	images := il.ByOldest()
	if l := len(images); n > l {
		n = l
	}
	if !inverse {
		return NewImageList(images[:n]...)
	} else {
		return NewImageList(images[n:]...)
	}
}

func (il ImageList) CutNewest(n int, inverse bool) *ImageList {
	images := il.ByNewest()
	if l := len(images); n > l {
		n = l
	}
	if !inverse {
		return NewImageList(images[:n]...)
	} else {
		return NewImageList(images[n:]...)
	}
}

func (il ImageList) CutOlder(t time.Time, inverse bool) *ImageList {
	cut := []*Image{}
	if !inverse {
		for _, image := range il.Images() {
			if image.Created().Before(t) {
				cut = append(cut, image)
			}
		}
	} else {
		for _, image := range il.Images() {
			if !image.Created().Before(t) {
				cut = append(cut, image)
			}
		}
	}
	return NewImageList(cut...)
}

func (il ImageList) CutNewer(t time.Time, inverse bool) *ImageList {
	cut := []*Image{}
	if !inverse {
		for _, image := range il.Images() {
			if image.Created().After(t) {
				cut = append(cut, image)
			}
		}
	} else {
		for _, image := range il.Images() {
			if !image.Created().After(t) {
				cut = append(cut, image)
			}
		}
	}
	return NewImageList(cut...)
}

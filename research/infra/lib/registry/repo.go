package registry

import (
	"strconv"
	"strings"
	"time"
)

// Repo represents an image repository (an image base name, plus one image for
// each tag). This is a thin wrapper around an ImageList.
type Repo struct {
	*ImageList
	repo string
}

func NewRepo(repo string, images ...*Image) *Repo {
	il := NewImageList(images...)
	return &Repo{
		ImageList: il,
		repo:      repo,
	}
}

func (r Repo) Name() string {
	return r.repo
}

func (r Repo) Cruft(keepn int, afterdays int, now time.Time) *Repo {
	cutoff := now.Add(time.Duration(-afterdays*24) * time.Hour)
	lst := NewImageList()
	for _, img := range r.Images() {
		if img.Tag() != "latest" {
			lst.Extend(img)
		}
	}
	lst = lst.CutNewest(keepn, true)
	lst = lst.CutOlder(cutoff, false)
	return NewRepo(r.Name(), lst.Images()...)
}

func (r Repo) FormatRow() string {
	items := []string{
		r.Name(),
		strconv.It<PERSON>(r.<PERSON>()),
		<PERSON><PERSON>(),
		strconv.FormatUint(r.<PERSON>(), 10),
	}
	return strings.Join(items, "\t")
}

package registry

import (
	"fmt"
	"testing"

	"github.com/google/go-cmp/cmp"
)

func TestRepo(t *testing.T) {
	r := NewRepo("repo0")
	if got, want := r.Name(), "repo0"; got != want {
		t.Errorf("Name(): got %v, want %v.", got, want)
	}
	if got, want := r.Len(), 0; got != want {
		t.<PERSON><PERSON>("Len(): got %v, want %v.", got, want)
	}
	if got, want := r.<PERSON>(), uint64(0); got != want {
		t.<PERSON>rrorf("Size(): got %v, want %v.", got, want)
	}
	if got, want := len(r.FormatRow()), 10; got < want {
		t.Errorf("FormatRow().len(): got %v, want >= %v", got, want)
	}
}

func TestRepoCruft(t *testing.T) {
	tests := map[string]struct {
		in          map[string]int
		inNow       int
		inKeepN     int
		inAfterDays int
		wByOldest   []string
	}{
		"keep-all-by-n": {
			in: map[string]int{
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
				"t5": 5,
				"t6": 6,
			},
			inKeepN:     6,
			inAfterDays: 1,
			inNow:       10,
			wByOldest:   []string{},
		},
		"keep-half-by-n": {
			in: map[string]int{
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
				"t5": 5,
				"t6": 6,
			},
			inKeepN:     3,
			inAfterDays: 1,
			inNow:       10,
			wByOldest:   []string{"t1", "t2", "t3"},
		},
		"keep-latest": {
			in: map[string]int{
				"latest": 1,
				"t1":     1,
				"t2":     2,
				"t3":     3,
				"t4":     4,
				"t5":     5,
				"t6":     6,
			},
			inKeepN:     3,
			inAfterDays: 1,
			inNow:       10,
			wByOldest:   []string{"t1", "t2", "t3"},
		},
		"keep-all-by-age": {
			in: map[string]int{
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
				"t5": 5,
				"t6": 6,
			},
			inKeepN:     0,
			inAfterDays: 20,
			inNow:       10,
			wByOldest:   []string{},
		},
		"keep-half-by-age": {
			in: map[string]int{
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
				"t5": 5,
				"t6": 6,
			},
			inKeepN:     0,
			inAfterDays: 6,
			inNow:       10,
			wByOldest:   []string{"t1", "t2", "t3"},
		},
		"mixed": {
			in: map[string]int{
				"latest": 1,
				"t1":     1,
				"t2":     2,
				"t3":     3,
				"t4":     4,
				"t5":     5,
				"t6":     6,
			},
			inKeepN:     4,
			inAfterDays: 6,
			inNow:       10,
			wByOldest:   []string{"t1", "t2"},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			r := NewRepo("repo0")
			for tag, age := range tc.in {
				img := mkImage(t, "repo0", tag, nil, ``, fmt.Sprintf(`{
					"created": "1970-01-%02dT00:00:00.000000Z"
				}`, age))
				r.Extend(img)
			}
			now := mkImage(t, "_tmp_", "_tmp_", nil, ``, fmt.Sprintf(`{
				"created": "1970-01-%02dT00:00:00.000000Z"
			}`, tc.inNow)).Created()

			cruft := r.Cruft(tc.inKeepN, tc.inAfterDays, now)
			if diff := cmp.Diff(tc.wByOldest, cruft.TagsByOldest()); diff != "" {
				t.Errorf("ByOldest(): -got +want:\n%s", diff)
			}
		})
	}
}

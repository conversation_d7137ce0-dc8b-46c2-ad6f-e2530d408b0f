package registry

import (
	"testing"
	"time"
)

func mkImage(t *testing.T, repo, tag string, hs []string, mbuf, icbuf string) *Image {
	t.Helper()
	var m *Manifest
	var ic *ImageConfig
	if mbuf != "" {
		var err error
		if m, err = NewManifest(mkResp(t, hs...), []byte(mbuf)); err != nil {
			t.<PERSON>al(err)
		}
	}
	if icbuf != "" {
		var err error
		if ic, err = NewImageConfig(mkResp(t), []byte(icbuf)); err != nil {
			t.Fatal(err)
		}
	}
	return NewImage(repo, tag, m, ic)
}

func TestImage(t *testing.T) {
	tests := map[string]struct {
		inRepo        string
		inTag         string
		inHeaders     []string
		inManifest    string
		inImageConfig string

		wString  string
		wRepo    string
		wTag     string
		wDigest  string
		wSize    uint64
		wSizeH   string
		wCreated time.Time
		wRow     string
	}{
		"nil": {
			inRepo:        "r0",
			inTag:         "t0",
			inManifest:    ``,
			inImageConfig: ``,
			wString:       "r0:t0",
			wRepo:         "r0",
			wTag:          "t0",
			wDigest:       "",
			wSize:         0,
			wSizeH:        "0 B",
			wCreated:      time.Time{},
		},
		"empty": {
			inRepo:        "r0",
			inTag:         "t0",
			inManifest:    `{}`,
			inImageConfig: `{}`,
			wString:       "r0:t0",
			wRepo:         "r0",
			wTag:          "t0",
			wDigest:       "",
			wSize:         0,
			wSizeH:        "0 B",
			wCreated:      time.Time{},
		},
		"basic": {
			inRepo:    "r0",
			inTag:     "t0",
			inHeaders: []string{"docker-content-digest=sch0:dig000"},
			inManifest: `{
				"layers": [
					{
						"digest": "d0",
						"size": 1024
					},
					{
						"digest": "d1",
						"size": 3072
					},
					{
						"digest": "d2",
						"size": 5120
					}
				]
			} `,
			inImageConfig: `{
				"created": "1970-01-01T00:00:01.000000Z"
			}`,
			wString:  "r0:t0",
			wRepo:    "r0",
			wTag:     "t0",
			wDigest:  "sch0:dig000",
			wSize:    9216,
			wSizeH:   "9.0 KiB",
			wCreated: time.Unix(1, 0),
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			i := mkImage(t, tc.inRepo, tc.inTag, tc.inHeaders, tc.inManifest, tc.inImageConfig)

			if got, want := i.String(), tc.wString; got != want {
				t.Errorf("String(): got %v, want %v.", got, want)
			}
			if got, want := i.Repo(), tc.wRepo; got != want {
				t.Errorf("Repo(): got %v, want %v.", got, want)
			}
			if got, want := i.Tag(), tc.wTag; got != want {
				t.Errorf("Tag(): got %v, want %v.", got, want)
			}
			if got, want := i.Digest(), tc.wDigest; got != want {
				t.Errorf("Digest(): got %v, want %v.", got, want)
			}
			if got, want := i.Size(), tc.wSize; got != want {
				t.Errorf("Size(): got %v, want %v.", got, want)
			}
			if got, want := i.SizeHuman(), tc.wSizeH; got != want {
				t.Errorf("SizeHuman(): got %v, want %v.", got, want)
			}
			if got, want := i.Created(), tc.wCreated; !got.Equal(want) {
				t.Errorf("Created(): got %v, want %v.", got, want)
			}
			if got, want := len(i.FormatRow()), 10; got < want {
				t.Errorf("FormatRow().len(): got %v, want >= %v.", got, want)
			}
		})
	}
}

package registry

import (
	"fmt"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
)

func TestEmptyImageList(t *testing.T) {
	il := NewImageList()
	if got, want := il.Len(), 0; got != want {
		t.Errorf("Len(): got %v, want %v.", got, want)
	}
	if got, want := il.Size(), uint64(0); got != want {
		t.<PERSON><PERSON><PERSON>("Size(): got %v, want %v.", got, want)
	}
	if got, want := il.<PERSON><PERSON>(), "0 B"; got != want {
		t.<PERSON>rrorf("SizeHuman(): got %v, want %v.", got, want)
	}
}

func TestImageListStorage(t *testing.T) {
	il := NewImageList(
		mkImage(t, "repo", "tag1", nil, `{
			"layers": [
				{
					"digest": "d1",
					"size":   3
				},
				{
					"digest": "d2",
					"size":   5
				}
			]
		}`, ""),
		mkImage(t, "repo", "tag2", nil, `{
			"layers": [
				{
					"digest": "d2",
					"size":   5
				},
				{
					"digest": "d3",
					"size":   7
				}
			]
		}`, ""),
	)
	if got, want := il.Len(), 2; got != want {
		t.Errorf("Len(): got %v, want %v.", got, want)
	}
	if got, want := il.Size(), uint64(15); got != want {
		t.Errorf("Size(): got %v, want %v.", got, want)
	}
}

func TestImageOldestNewest(t *testing.T) {
	tests := map[string]struct {
		in        map[string]int
		wOldest   string
		wNewest   string
		wByOldest []string
		wByNewest []string
	}{
		"one": {
			in: map[string]int{
				"tag0": 1,
			},
			wOldest:   "tag0",
			wNewest:   "tag0",
			wByOldest: []string{"tag0"},
			wByNewest: []string{"tag0"},
		},
		"three": {
			in: map[string]int{
				"tag0": 30,
				"tag1": 1,
				"tag2": 59,
			},
			wOldest:   "tag1",
			wNewest:   "tag2",
			wByOldest: []string{"tag1", "tag0", "tag2"},
			wByNewest: []string{"tag2", "tag0", "tag1"},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			il := NewImageList()
			for tag, age := range tc.in {
				img := mkImage(t, "repo", tag, nil, ``, fmt.Sprintf(`{
					"created": "1970-01-01T00:00:%02d.000000Z"
				}`, age))
				il.Extend(img)
			}
			if got, want := il.Oldest().Tag(), tc.wOldest; got != want {
				t.Errorf("Oldest(): got %v, want %v.", got, want)
			}
			if got, want := il.Newest().Tag(), tc.wNewest; got != want {
				t.Errorf("Newest(): got %v, want %v.", got, want)
			}
			if diff := cmp.Diff(tc.wByOldest, il.TagsByOldest()); diff != "" {
				t.Errorf("ByOldest(): -got +want:\n%s", diff)
			}
			if diff := cmp.Diff(tc.wByNewest, il.TagsByNewest()); diff != "" {
				t.Errorf("ByNewest(): -got +want:\n%s", diff)
			}
		})
	}
}

func TestCutOldest(t *testing.T) {
	tests := map[string]struct {
		in        map[string]int
		inN       int
		wByOldest []string
		wInverse  []string
	}{
		"exact": {
			inN: 5,
			in: map[string]int{
				"t0": 0,
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
			},
			wByOldest: []string{"t0", "t1", "t2", "t3", "t4"},
			wInverse:  []string{},
		},
		"extra": {
			inN: 100,
			in: map[string]int{
				"t0": 0,
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
			},
			wByOldest: []string{"t0", "t1", "t2", "t3", "t4"},
			wInverse:  []string{},
		},
		"partial": {
			inN: 3,
			in: map[string]int{
				"t0": 0,
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
			},
			wByOldest: []string{"t0", "t1", "t2"},
			wInverse:  []string{"t3", "t4"},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			il := NewImageList()
			for tag, age := range tc.in {
				img := mkImage(t, "repo", tag, nil, ``, fmt.Sprintf(`{
					"created": "1970-01-01T00:00:%02d.000000Z"
				}`, age))
				il.Extend(img)
			}
			il2 := il.CutOldest(tc.inN, false)
			if diff := cmp.Diff(tc.wByOldest, il2.TagsByOldest()); diff != "" {
				t.Errorf("ByOldest(): -got +want:\n%s", diff)
			}
			il3 := il.CutOldest(tc.inN, true)
			if diff := cmp.Diff(tc.wInverse, il3.TagsByOldest()); diff != "" {
				t.Errorf("ByOldest()[inverse]: -got +want:\n%s", diff)
			}
		})
	}
}

func TestCutNewest(t *testing.T) {
	tests := map[string]struct {
		in        map[string]int
		inN       int
		wByOldest []string
		wInverse  []string
	}{
		"exact": {
			inN: 5,
			in: map[string]int{
				"t0": 0,
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
			},
			wByOldest: []string{"t0", "t1", "t2", "t3", "t4"},
			wInverse:  []string{},
		},
		"extra": {
			inN: 100,
			in: map[string]int{
				"t0": 0,
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
			},
			wByOldest: []string{"t0", "t1", "t2", "t3", "t4"},
			wInverse:  []string{},
		},
		"partial": {
			inN: 3,
			in: map[string]int{
				"t0": 0,
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
			},
			wByOldest: []string{"t2", "t3", "t4"},
			wInverse:  []string{"t0", "t1"},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			il := NewImageList()
			for tag, age := range tc.in {
				img := mkImage(t, "repo", tag, nil, ``, fmt.Sprintf(`{
					"created": "1970-01-01T00:00:%02d.000000Z"
				}`, age))
				il.Extend(img)
			}
			il2 := il.CutNewest(tc.inN, false)
			if diff := cmp.Diff(tc.wByOldest, il2.TagsByOldest()); diff != "" {
				t.Errorf("ByOldest(): -got +want:\n%s", diff)
			}
			il3 := il.CutNewest(tc.inN, true)
			if diff := cmp.Diff(tc.wInverse, il3.TagsByOldest()); diff != "" {
				t.Errorf("ByOldest()[inverse]: -got +want:\n%s", diff)
			}
		})
	}
}

func TestCutOlder(t *testing.T) {
	tests := map[string]struct {
		in        map[string]int
		inT       int64
		wByOldest []string
		wInverse  []string
	}{
		"before": {
			inT: 0,
			in: map[string]int{
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
			},
			wByOldest: []string{},
			wInverse:  []string{"t1", "t2", "t3", "t4"},
		},
		"start-exclusive": {
			inT: 1,
			in: map[string]int{
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
			},
			wByOldest: []string{},
			wInverse:  []string{"t1", "t2", "t3", "t4"},
		},
		"middle-exclusive": {
			inT: 3,
			in: map[string]int{
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
			},
			wByOldest: []string{"t1", "t2"},
			wInverse:  []string{"t3", "t4"},
		},
		"after": {
			inT: 59,
			in: map[string]int{
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
			},
			wByOldest: []string{"t1", "t2", "t3", "t4"},
			wInverse:  []string{},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			il := NewImageList()
			for tag, age := range tc.in {
				img := mkImage(t, "repo", tag, nil, ``, fmt.Sprintf(`{
					"created": "1970-01-01T00:00:%02d.000000Z"
				}`, age))
				il.Extend(img)
			}
			il2 := il.CutOlder(time.Unix(tc.inT, 0), false)
			if diff := cmp.Diff(tc.wByOldest, il2.TagsByOldest()); diff != "" {
				t.Errorf("ByOldest(): -got +want:\n%s", diff)
			}
			il3 := il.CutOlder(time.Unix(tc.inT, 0), true)
			if diff := cmp.Diff(tc.wInverse, il3.TagsByOldest()); diff != "" {
				t.Errorf("ByOldest()[inverse]: -got +want:\n%s", diff)
			}
		})
	}
}

func TestCutNewer(t *testing.T) {
	tests := map[string]struct {
		in        map[string]int
		inT       int64
		wByOldest []string
		wInverse  []string
	}{
		"before": {
			inT: 0,
			in: map[string]int{
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
			},
			wByOldest: []string{"t1", "t2", "t3", "t4"},
			wInverse:  []string{},
		},
		"start-exclusive": {
			inT: 1,
			in: map[string]int{
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
			},
			wByOldest: []string{"t2", "t3", "t4"},
			wInverse:  []string{"t1"},
		},
		"middle-exclusive": {
			inT: 3,
			in: map[string]int{
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
			},
			wByOldest: []string{"t4"},
			wInverse:  []string{"t1", "t2", "t3"},
		},
		"after": {
			inT: 59,
			in: map[string]int{
				"t1": 1,
				"t2": 2,
				"t3": 3,
				"t4": 4,
			},
			wByOldest: []string{},
			wInverse:  []string{"t1", "t2", "t3", "t4"},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			il := NewImageList()
			for tag, age := range tc.in {
				img := mkImage(t, "repo", tag, nil, ``, fmt.Sprintf(`{
					"created": "1970-01-01T00:00:%02d.000000Z"
				}`, age))
				il.Extend(img)
			}
			il2 := il.CutNewer(time.Unix(tc.inT, 0), false)
			if diff := cmp.Diff(tc.wByOldest, il2.TagsByOldest()); diff != "" {
				t.Errorf("ByOldest(): -got +want:\n%s", diff)
			}
			il3 := il.CutNewer(time.Unix(tc.inT, 0), true)
			if diff := cmp.Diff(tc.wInverse, il3.TagsByOldest()); diff != "" {
				t.Errorf("ByOldest()[inverse]: -got +want:\n%s", diff)
			}
		})
	}
}

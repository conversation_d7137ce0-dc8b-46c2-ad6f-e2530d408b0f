package registry

import (
	"strconv"
	"strings"
	"time"

	"github.com/dustin/go-humanize"
)

// Image is a docker image which consists primarily of a Manifest +
// ImageConfig.
type Image struct {
	repo string
	tag  string
	m    *Manifest
	ic   *ImageConfig
}

func NewImage(repo, tag string, m *Manifest, ic *ImageConfig) *Image {
	return &Image{
		repo: repo,
		tag:  tag,
		m:    m,
		ic:   ic,
	}
}

func (i Image) String() string {
	return i.Repo() + ":" + i.Tag()
}

func (i Image) Repo() string {
	return i.repo
}

func (i Image) Tag() string {
	return i.tag
}

func (i Image) Digest() string {
	if i.m == nil {
		return ""
	}
	return i.m.Digest()
}

// Size returns the total size indicated in the Manifest. Note that storage size
// may be smaller if layers are shared with other images.
func (i Image) Size() uint64 {
	if i.m == nil {
		return 0
	}
	return i.m.TotalSize()
}

// SizeHuman is a friendly wrapper around `Size()`.
func (i Image) SizeHuman() string {
	return humanize.IBytes(i.Size())
}

// Created returns the image creation time as noted in the ImageConfig. Note that if this is
// a "zero struct" or the config is otherwise missing, the go "zero time" is 0000-01-01.
func (i Image) Created() time.Time {
	if i.ic == nil {
		return time.Time{}
	}
	t, err := i.ic.Created()
	if err != nil {
		return time.Time{}
	}
	return t
}

func (i Image) FormatRow() string {
	items := []string{
		i.Repo(),
		i.Tag(),
		i.SizeHuman(),
		strconv.FormatUint(i.Size(), 10),
		i.Created().Local().Format("2006-01-02 15:04:05 MST"),
		strconv.FormatInt(i.Created().Unix(), 10),
	}
	return strings.Join(items, "\t")
}

package registry

import (
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
)

func mkResp(t *testing.T, hdrs ...string) *http.Response {
	t.Helper()
	resp := &http.Response{
		Header: http.Header{},
	}
	for _, hdr := range hdrs {
		key, val, _ := strings.Cut(hdr, "=")
		resp.Header.Add(key, val)
	}
	return resp
}

func mkJSON(t *testing.T, body string, hdrs ...string) *JSON {
	t.Helper()
	json, err := NewJSON(mkResp(t, hdrs...), []byte(body))
	if err != nil {
		t.Fatal(err)
	}
	return json
}

func TestJSON(t *testing.T) {
	empty := mkJSON(t, "{}")
	json := mkJSON(
		t,
		`{"hello": "world", "num": 42, "unhandled": true}`,
		"foo=bar",
		"docker-content-digest=sha999:abcd-efg",
	)

	if got, want := json.Header("foo"), "bar"; got != want {
		t.<PERSON><PERSON><PERSON>("Header(foo): got %v, want %v.", got, want)
	}
	if got, want := json.Header("docker-content-digest"), "sha999:abcd-efg"; got != want {
		t.Errorf("Header(docker-content-digest): got %v, want %v.", got, want)
	}
	if got, want := json.Header("Docker-Content-Digest"), "sha999:abcd-efg"; got != want {
		t.Errorf("Header(Docker-Content-Digest): got %v, want %v.", got, want)
	}
	if got, want := json.Digest(), "sha999:abcd-efg"; got != want {
		t.Errorf("Digest(): got %v, want %v.", got, want)
	}
	if got, want := empty.Digest(), ""; got != want {
		t.Errorf("Digest()[empty]: got %v, want %v.", got, want)
	}
	if got, want := json.Header("_unset_"), ""; got != want {
		t.Errorf("Header(_unset_): got %v, want %v.", got, want)
	}
	if got, want := len(json.Pretty()), 10; got < want {
		t.Errorf("Pretty().len(): got %v, want < %v.", got, want)
	}

	obj := &struct {
		Hello string
		Num   int
		Used  bool
	}{}
	if err := json.Unmarshal(&obj); err != nil {
		t.Errorf("Unmarshal(): %v.", err)
	}
	if got, want := obj.Hello, "world"; got != want {
		t.Errorf("obj.Hello: got %v, want %v.", got, want)
	}
	if got, want := obj.Num, 42; got != want {
		t.Errorf("obj.Num: got %v, want %v.", got, want)
	}
	if got, want := obj.Used, false; got != want {
		t.Errorf("obj.Used: got %v, want %v.", got, want)
	}

	if _, err := NewJSON(nil, []byte(`{"hello": "world", "errors": [{"code": "C0", "message": "M0"}]}`)); err == nil {
		t.Errorf("NewJSON(<errors>) err: got nil, wanted an error")
	} else if got, want := err.Error(), "C0: M0"; got != want {
		t.Errorf("NewJSON(<errors>) err: got %v, want %s.", got, want)
	}
}

func TestCatalog(t *testing.T) {
	tests := map[string]struct {
		inHeaders   []string
		inBody      string
		wantErr     string
		wantDigest  string
		wantHeaders map[string]string

		wantRepos []string
	}{
		"invalid-json": {
			inBody:  `__error__`,
			wantErr: `invalid character '_' looking for beginning of value`,
		},
		"has-errors": {
			inBody:  `{"errors": [{"code": "__CODE__", "message": "__MESG__"}]}`,
			wantErr: `__CODE__: __MESG__`,
		},
		"empty": {
			inBody:    `{}`,
			wantRepos: nil,
		},
		"success": {
			inBody:    `{"repositories": ["repo99", "repo00", "repo55"]}`,
			wantRepos: []string{"repo00", "repo55", "repo99"},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			c, gotErr := NewCatalog(mkResp(t, tc.inHeaders...), []byte(tc.inBody))
			if (tc.wantErr == "") != (gotErr == nil) || (gotErr != nil && tc.wantErr != gotErr.Error()) {
				t.Errorf("NewCatalog() err: got %v, want %v.", gotErr, tc.wantErr)
			}
			if c == nil {
				return
			}
			if got, want := c.Digest(), tc.wantDigest; got != want {
				t.Errorf("Digest(): got %v, want %v.", got, want)
			}
			for key, want := range tc.wantHeaders {
				if got := c.Header(key); got != want {
					t.Errorf("Header(%s): got %v, want %v.", key, got, want)
				}
			}
			if diff := cmp.Diff(tc.wantRepos, c.Repos()); diff != "" {
				t.Errorf("Repos(): -got +want:\n%s", diff)
			}
		})
	}
}

func TestTags(t *testing.T) {
	tests := map[string]struct {
		inHeaders   []string
		inBody      string
		wantErr     string
		wantDigest  string
		wantHeaders map[string]string

		wantTags []string
	}{
		"invalid-json": {
			inBody:  `__error__`,
			wantErr: `invalid character '_' looking for beginning of value`,
		},
		"has-errors": {
			inBody:  `{"errors": [{"code": "__CODE__", "message": "__MESG__"}]}`,
			wantErr: `__CODE__: __MESG__`,
		},
		"empty": {
			inBody:   `{}`,
			wantTags: nil,
		},
		"success": {
			inBody:   `{"tags": ["tag99", "tag00", "tag55"]}`,
			wantTags: []string{"tag00", "tag55", "tag99"},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			tags, gotErr := NewTags(mkResp(t, tc.inHeaders...), []byte(tc.inBody))
			if (tc.wantErr == "") != (gotErr == nil) || (gotErr != nil && tc.wantErr != gotErr.Error()) {
				t.Errorf("NewTags() err: got %v, want %v.", gotErr, tc.wantErr)
			}
			if tags == nil {
				return
			}
			if got, want := tags.Digest(), tc.wantDigest; got != want {
				t.Errorf("Digest(): got %v, want %v.", got, want)
			}
			for key, want := range tc.wantHeaders {
				if got := tags.Header(key); got != want {
					t.Errorf("Header(%s): got %v, want %v.", key, got, want)
				}
			}
			if diff := cmp.Diff(tc.wantTags, tags.Tags()); diff != "" {
				t.Errorf("Tags(): -got +want:\n%s", diff)
			}
		})
	}
}

func TestManifests(t *testing.T) {
	tests := map[string]struct {
		inHeaders   []string
		inBody      string
		wantErr     string
		wantDigest  string
		wantHeaders map[string]string

		wantConfigDigest string
		wantTotalSize    uint64
	}{
		"invalid-json": {
			inBody:  `__error__`,
			wantErr: `invalid character '_' looking for beginning of value`,
		},
		"has-errors": {
			inBody:  `{"errors": [{"code": "__CODE__", "message": "__MESG__"}]}`,
			wantErr: `__CODE__: __MESG__`,
		},
		"empty": {
			inBody:           `{}`,
			wantConfigDigest: "",
			wantTotalSize:    0,
		},
		"basic": {
			inBody: `
			{
				"config": {
					"size": 1,
					"digest": "abc64:XYZ123"
				},
				"layers": [
					{
						"size": 2,
						"digest": "layer0"
					},
					{
						"size": 3,
						"digest": "layer1"
					}
				]
			}
			`,
			wantConfigDigest: "abc64:XYZ123",
			wantTotalSize:    5,
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			m, gotErr := NewManifest(mkResp(t, tc.inHeaders...), []byte(tc.inBody))
			if (tc.wantErr == "") != (gotErr == nil) || (gotErr != nil && tc.wantErr != gotErr.Error()) {
				t.Errorf("NewManifest() err: got %v, want %v.", gotErr, tc.wantErr)
			}
			if m == nil {
				return
			}
			if got, want := m.Digest(), tc.wantDigest; got != want {
				t.Errorf("Digest(): got %v, want %v.", got, want)
			}
			for key, want := range tc.wantHeaders {
				if got := m.Header(key); got != want {
					t.Errorf("Header(%s): got %v, want %v.", key, got, want)
				}
			}

			if got, want := m.ConfigDigest(), tc.wantConfigDigest; got != want {
				t.Errorf("ConfigDigest(): got %v, want %v.", got, want)
			}
			if got, want := m.TotalSize(), tc.wantTotalSize; got != want {
				t.Errorf("TotalSize(): got %v, want %v.", got, want)
			}
		})
	}
}

func TestImageConfig(t *testing.T) {
	tests := map[string]struct {
		inHeaders   []string
		inBody      string
		wantErr     string
		wantDigest  string
		wantHeaders map[string]string

		wantTS    time.Time
		wantTSErr string
	}{
		"invalid-json": {
			inBody:  `__error__`,
			wantErr: `invalid character '_' looking for beginning of value`,
		},
		"has-errors": {
			inBody:  `{"errors": [{"code": "__CODE__", "message": "__MESG__"}]}`,
			wantErr: `__CODE__: __MESG__`,
		},
		"empty": {
			inBody:    `{}`,
			wantTSErr: `parsing time "" as "2006-01-02T15:04:05.999999999Z": cannot parse "" as "2006"`,
		},
		"basic": {
			inBody: `
			{
				"created": "2024-03-02T03:08:29.019468438Z"
			}
			`,
			wantTS: time.Date(2024, 3, 2, 3, 8, 29, 19468438, time.UTC),
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			ic, gotErr := NewImageConfig(mkResp(t, tc.inHeaders...), []byte(tc.inBody))
			if (tc.wantErr == "") != (gotErr == nil) || (gotErr != nil && tc.wantErr != gotErr.Error()) {
				t.Errorf("NewManifest() err: got %v, want %v.", gotErr, tc.wantErr)
			}
			if ic == nil {
				return
			}
			if got, want := ic.Digest(), tc.wantDigest; got != want {
				t.Errorf("Digest(): got %v, want %v.", got, want)
			}
			for key, want := range tc.wantHeaders {
				if got := ic.Header(key); got != want {
					t.Errorf("Header(%s): got %v, want %v.", key, got, want)
				}
			}

			gotTS, gotTSErr := ic.Created()
			if (tc.wantTSErr == "") != (gotTSErr == nil) || (gotTSErr != nil && tc.wantTSErr != gotTSErr.Error()) {
				t.Errorf("Created() err: got %v, want %v.", gotTSErr, tc.wantTSErr)
			}
			if !gotTS.Equal(tc.wantTS) {
				t.Errorf("Created(): got %v, want %v.", gotTS, tc.wantTS)
			}
		})
	}
}

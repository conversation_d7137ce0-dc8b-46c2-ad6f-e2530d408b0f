package registry

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"sort"
	"time"
)

// JSON is a common struct, meant to be embedded in objects defined by the Registry API.
type JSON struct {
	head http.Header
	body []byte
}

func NewJSON(r *http.Response, body []byte) (*JSON, error) {
	if err := HasErrors(body); err != nil {
		return nil, err
	}
	return &JSON{
		head: r.Header,
		body: body,
	}, nil
}

func (j JSON) Header(key string) string {
	return j.head.Get(key)
}

func (j JSON) Digest() string {
	return j.Header("Docker-Content-Digest")
}

func (j JSON) Pretty() string {
	buf := bytes.Buffer{}
	if err := json.Indent(&buf, j.body, "", "\t"); err != nil {
		return err.Error()
	}
	return buf.String()
}

func (j JSON) Unmarshal(v any) error {
	return json.Unmarshal(j.body, v)
}

////////////////////////////////////////////////////////////////////////////////
//
// Common Error Handling
//

func HasErrors(buf []byte) error {
	errs := Errors{}
	if err := json.Unmarshal(buf, &errs); err != nil {
		return err
	}
	if err := errs.Err(); err != nil {
		return err
	}
	return nil
}

type Errors struct {
	Errors []Error
}

func (es Errors) Errs() []error {
	errs := []error{}
	for _, e := range es.Errors {
		errs = append(errs, e.Err())
	}
	return errs
}

func (es Errors) Err() error {
	return errors.Join(es.Errs()...)
}

type Error struct {
	Code    string
	Message string
	Detail  string
}

func (e Error) Err() error {
	if e.Detail == "" {
		return fmt.Errorf("%s: %s", e.Code, e.Message)
	} else {
		return fmt.Errorf("%s: %s\n\tdetail: {%s}", e.Code, e.Message, e.Detail)
	}
}

////////////////////////////////////////////////////////////////////////////////
//
// Catalog
//

type Catalog struct {
	*JSON
	catalog struct {
		Repositories []string
	}
}

func NewCatalog(r *http.Response, body []byte) (*Catalog, error) {
	j, err := NewJSON(r, body)
	if err != nil {
		return nil, err
	}
	c := &Catalog{JSON: j}
	if err := j.Unmarshal(&c.catalog); err != nil {
		return nil, err
	}
	sort.Strings(c.catalog.Repositories)
	return c, nil
}

func (c Catalog) Repos() []string {
	return c.catalog.Repositories
}

////////////////////////////////////////////////////////////////////////////////
//
// Tags
//

type Tags struct {
	*JSON
	tags struct {
		Name string
		Tags []string
	}
}

func NewTags(r *http.Response, body []byte) (*Tags, error) {
	j, err := NewJSON(r, body)
	if err != nil {
		return nil, err
	}
	t := &Tags{JSON: j}
	if err := j.Unmarshal(&t.tags); err != nil {
		return nil, err
	}
	sort.Strings(t.tags.Tags)
	return t, nil
}

func (t Tags) Tags() []string {
	return t.tags.Tags
}

////////////////////////////////////////////////////////////////////////////////
//
// Manifest
//

type Manifest struct {
	*JSON
	manifest struct {
		SchemaVersion int
		MediaType     string
		Config        struct {
			MediaType string
			Size      int
			Digest    string
		}
		Layers []Layer
	}
}

type Layer struct {
	MediaType string
	Size      uint64
	Digest    string
	Urls      []string
}

func NewManifest(r *http.Response, body []byte) (*Manifest, error) {
	j, err := NewJSON(r, body)
	if err != nil {
		return nil, err
	}
	m := &Manifest{JSON: j}
	if err := j.Unmarshal(&m.manifest); err != nil {
		return nil, err
	}
	return m, nil
}

func (m Manifest) ConfigDigest() string {
	return m.manifest.Config.Digest
}

func (m Manifest) Layers() []Layer {
	return m.manifest.Layers
}

func (m Manifest) TotalSize() uint64 {
	tot := uint64(0)
	for _, layer := range m.Layers() {
		tot += layer.Size
	}
	return tot
}

////////////////////////////////////////////////////////////////////////////////
//
// ImageConfig
//

type ImageConfig struct {
	*JSON
	config struct {
		Created      string
		Author       string
		Architecture string
		Os           string
		Config       struct {
			User       string
			WorkingDir string
			Env        []string
			Entrypoint []string
			Cmd        []string
			Labels     map[string]string
		}
		History []struct {
			Created    string
			CreatedBy  string
			EmptyLayer bool
			Comment    string
		}
	}
}

func NewImageConfig(r *http.Response, body []byte) (*ImageConfig, error) {
	j, err := NewJSON(r, body)
	if err != nil {
		return nil, err
	}
	ic := &ImageConfig{JSON: j}
	if err := j.Unmarshal(&ic.config); err != nil {
		return nil, err
	}
	return ic, nil
}

func (ic ImageConfig) Created() (time.Time, error) {
	return time.Parse("2006-01-02T15:04:05.999999999Z", ic.config.Created)
}

package registry

import (
	"fmt"

	"github.com/dustin/go-humanize"
)

type index map[string]struct {
	size uint64
	refs int
}

// BlobPool tracks the size of blobs and counts references to them. It's main
// purpose is to track the storage used by multiple images in a repo.
// NOTE(mattm): A BlobPool is not currently thread-safe, safety is provided
// by containing ImageList.
type BlobPool struct {
	i index
}

func NewBlobPool() *BlobPool {
	return &BlobPool{
		i: index{},
	}
}

func (bp BlobPool) pool() index {
	return bp.i
}

func (bp *BlobPool) setdefault(digest string, size uint64, refs int) (uint64, int) {
	ent, ok := bp.pool()[digest]
	if !ok {
		ent.size = size
		ent.refs = refs
		bp.pool()[digest] = ent
	}
	return ent.size, ent.refs
}

func (bp *BlobPool) incr(digest string, refs int) {
	ent := bp.pool()[digest]
	ent.refs += refs
	bp.pool()[digest] = ent
}

func (bp *BlobPool) decr(digest string, refs int) {
	ent := bp.pool()[digest]
	ent.refs -= refs
	bp.pool()[digest] = ent
}

func (bp BlobPool) Len() int {
	return len(bp.pool())
}

func (bp BlobPool) Size() uint64 {
	ret := uint64(0)
	for _, entry := range bp.pool() {
		ret += entry.size
	}
	return ret
}

func (bp BlobPool) SizeHuman() string {
	return humanize.IBytes(bp.Size())
}

func (bp *BlobPool) Tally(digest string, size uint64, num int) error {
	curSize, _ := bp.setdefault(digest, size, 0)
	if size != curSize {
		return fmt.Errorf("size mismatch %d != %d: cannot tally %s", size, curSize, digest)
	}
	bp.incr(digest, num)
	return nil
}

func (bp *BlobPool) UnTally(digest string, size uint64, num int) error {
	curSize, curRefs := bp.setdefault(digest, size, 0)
	if size != curSize {
		return fmt.Errorf("size mismatch %d != %d: cannot untally %s", size, curSize, digest)
	}
	if num > curRefs {
		return fmt.Errorf("dropping %d of %d refs: cannot untally %s", num, curRefs, digest)
	}
	bp.decr(digest, num)
	return nil
}

func (bp *BlobPool) TallyPool(other *BlobPool) error {
	for digest, ent := range other.pool() {
		if err := bp.Tally(digest, ent.size, ent.refs); err != nil {
			return err
		}
	}
	return nil
}

func (bp *BlobPool) UnTallyPool(other *BlobPool) error {
	for digest, ent := range other.pool() {
		if err := bp.UnTally(digest, ent.size, ent.refs); err != nil {
			return err
		}
	}
	return nil
}

func (bp BlobPool) OrphanPool() *BlobPool {
	orphans := index{}
	for digest, ent := range bp.pool() {
		if ent.refs == 0 {
			orphans[digest] = struct {
				size uint64
				refs int
			}{size: ent.size, refs: 0}
		}
	}
	ret := NewBlobPool()
	ret.i = orphans
	return ret
}

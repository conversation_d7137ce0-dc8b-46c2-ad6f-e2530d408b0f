load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(default_visibility = ["//research/infra:internal"])

go_library(
    name = "slack",
    srcs = [
        "slack.go",
        "socket.go",
    ],
    importpath = "github.com/augmentcode/augment/research/infra/lib/slack",
    deps = [
        "//infra/lib/logger",
        "@com_github_slack_go_slack//:slack",
        "@com_github_slack_go_slack//socketmode",
    ],
)

go_test(
    name = "slack_test",
    srcs = ["slack_test.go"],
    embed = [":slack"],
)

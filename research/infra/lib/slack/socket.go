package slack

import (
	"context"

	"github.com/slack-go/slack"
	"github.com/slack-go/slack/socketmode"
)

// SocketClient is a generic Slack SocketMode client. It's designed around the newer handler
// interface and handles converting raw event data to typed structs.
type SocketClient struct {
	*Client

	sock *socketmode.SocketmodeHandler
}

func NewSocketClient(cli *Client) *SocketClient {
	scli := socketmode.New(cli.api, socketmode.OptionDebug(false))
	sh := socketmode.NewSocketmodeHandler(scli)
	return &SocketClient{
		Client: cli,
		sock:   sh,
	}
}

func (c *SocketClient) Run(ctx context.Context) error {
	c.LogInfo("Running SocketModeHandler EventLoop...")
	if err := c.sock.RunEventLoopContext(ctx); err != nil {
		c.LogErr("Exiting SocketModeHandler EventLoop with error: %v.", err)
		return err
	} else {
		c.LogInfo("Exiting SocketModeHandler EventLoop.")
		return nil
	}
}

func (c *SocketClient) Ack(ctx context.Context, ev *socketmode.Event, payload any) error {
	return c.sock.Client.AckCtx(ctx, ev.Request.EnvelopeID, payload)
}

func (c *SocketClient) HandleSlashCommand(command string, h func(*socketmode.Event, slack.SlashCommand)) {
	c.sock.HandleSlashCommand(command, func(ev *socketmode.Event, _ *socketmode.Client) {
		if ev == nil {
			c.LogWarn("Received nil HandleSlashCommand event.")
			return
		} else if cmd, ok := ev.Data.(slack.SlashCommand); !ok {
			c.LogWarn("Received non-SlashCommand HandleSlashCommand event: %+v.", ev)
			return
		} else {
			c.LogInfo("Received SlashCommand: %+v.", ev)
			h(ev, cmd)
		}
	})
}

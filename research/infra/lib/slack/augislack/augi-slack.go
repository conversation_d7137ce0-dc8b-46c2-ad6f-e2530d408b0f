package augislack

import (
	"context"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/mattn/go-shellwords"
	vslack "github.com/slack-go/slack"
	"github.com/slack-go/slack/socketmode"
	"github.com/spf13/cobra"

	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/infra/lib/logger"
	"github.com/augmentcode/augment/research/infra/lib/slack"
)

// Bot is a Slash slash-command bot interface to `augi`.
type Bot struct {
	logger.Logger

	slack *slack.SocketClient
	k8s   *k8s.Client
}

func NewBot(botTok, appTok, kubeconfigPath string) (*Bot, error) {
	k, err := k8s.NewFromKubeconfig(targetCluster, kubeconfigPath)
	if err != nil {
		return nil, err
	}
	b := &Bot{
		Logger: logger.WithTag(logger.New(nil), "augi-slack"),
		slack:  slack.NewSocketClient(slack.NewClient(botTok, appTok)),
		k8s:    k,
	}
	return b, nil
}

func (b *Bot) K8s(stderr io.Writer) *k8s.Client {
	k := b.k8s.InNamespace("")
	k.Logger = logger.WithTag(logger.New(stderr), "k8s")
	k.SetDefaultFieldManager("augi")
	return &k
}

func (b *Bot) Run(ctx context.Context) error {
	b.slack.HandleSlashCommand("/augi", func(ev *socketmode.Event, cmd vslack.SlashCommand) {
		b.HandleSlashCommand(ctx, ev, cmd)
	})

	b.LogInfo("Running Augi Bot EventLoop...")
	if err := b.slack.Run(ctx); err != nil {
		b.LogErr("Exiting Augi Bot EventLoop with error: %v.", err)
		return err
	} else {
		b.LogInfo("Exiting Augi Bot EventLoop.")
		return nil
	}
}

func (b *Bot) HandleSlashCommand(ctx context.Context, ev *socketmode.Event, scmd vslack.SlashCommand) {
	/// Ack helper, should be called exactly once within 3 seconds.

	ack := func(f string, a ...any) {
		if err := b.slack.Ack(ctx, ev, map[string]string{
			"response_type": "in_channel",
			"text":          fmt.Sprintf(f, a...),
		}); err != nil {
			b.LogErr("Failed to ack event %s: %v.", ev.Request.EnvelopeID, err)
		}
	}

	/// Response helper, updates running log of command.

	firstResp := true
	resp := func(f string, a ...any) {
		if firstResp {
			firstResp = false
			if _, err := b.slack.PostMessage(ctx, scmd.ChannelID,
				vslack.MsgOptionResponseURL(scmd.ResponseURL, "in_channel"),
				vslack.MsgOptionText(fmt.Sprintf(f, a...), false),
			); err != nil {
				b.LogErr("Failed initial response to %s: %v.", ev.Request.EnvelopeID, err)
			}
		} else {
			if _, err := b.slack.UpdateMessage(ctx, scmd.ChannelID, "",
				vslack.MsgOptionReplaceOriginal(scmd.ResponseURL),
				vslack.MsgOptionText(fmt.Sprintf(f, a...), false),
			); err != nil {
				b.LogErr("Failed to update response to %s: %v.", ev.Request.EnvelopeID, err)
			}
		}
	}

	/// Authorize user: @augmentcode.com, update scmd.UserName if needed

	if userProfile, err := b.slack.UserProfile(ctx, scmd.UserID); err != nil {
		ack("Failed to get user profile for %s (%s): %v.", scmd.UserName, scmd.UserID, err)
		return
	} else if userName, found := strings.CutSuffix(userProfile.Email, "@augmentcode.com"); !found {
		ack("Unauthorized: %s.", userProfile.Email)
		return
	} else if scmd.UserName != userName {
		b.LogWarn("Mapped username %s to %s (from email %s) [%s].", scmd.UserName, userName, userProfile.Email, scmd.UserID)
		scmd.UserName = userName
	} else {
		b.LogInfo("Mapped username %s to %s (from email %s) [%s].", scmd.UserName, userName, userProfile.Email, scmd.UserID)
	}

	/// Init Root Command, with stdout/stderr written to buffer.

	cmd := b.rootCmd(scmd)
	stdout := &strings.Builder{}
	cmd.SetIn(io.LimitReader(strings.NewReader(""), 0))
	cmd.SetOut(stdout)
	cmd.SetErr(stdout)

	/// Parse slash command into args.

	if args, err := shellwords.Parse(scmd.Text); err != nil {
		ack("Failed to parse commandline: %v.", err)
		return
	} else {
		cmd.SetArgs(args)
	}

	/// Ack (before we execute, this is the 3s boundary).

	ack("")

	/// Start message which will be updated as the command runs.

	resp("Running: `%s --user=%s %s`...", scmd.Command, scmd.UserName, scmd.Text)

	cctx, cancel := context.WithCancel(ctx)
	go func() {
		for {
			select {
			case <-cctx.Done():
				return
			case <-time.After(5 * time.Second):
				resp("```\n%s```\n", stdout.String())
			}
		}
	}()

	/// Execute Command

	if err := cmd.ExecuteContext(ctx); err != nil {
		fmt.Fprintf(cmd.OutOrStdout(), "Error: %v.\n", err)
	}
	cancel()

	/// Write stdout/stderr buffer back to Slack, formatted as a code block.

	resp("```\n%s```\n", stdout.String())
}

func (b *Bot) rootCmd(scmd vslack.SlashCommand) *cobra.Command {
	root := &cobra.Command{
		Use:   "/augi",
		Short: "/augi - the Augment Infra CLI (Slack 'slash-command' edition).",
	}
	root.CompletionOptions.DisableDefaultCmd = true
	root.AddCommand(b.devpodCmd(scmd))
	return root
}

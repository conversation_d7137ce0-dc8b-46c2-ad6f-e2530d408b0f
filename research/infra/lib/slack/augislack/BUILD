load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(default_visibility = ["//research/infra:internal"])

go_library(
    name = "augislack",
    srcs = [
        "augi-slack.go",
        "devpod.go",
    ],
    importpath = "github.com/augmentcode/augment/research/infra/lib/slack/augislack",
    deps = [
        "//infra/lib/k8s",
        "//infra/lib/logger",
        "//research/infra/cfg/clusters",
        "//research/infra/lib/augment",
        "//research/infra/lib/augment/devpod",
        "//research/infra/lib/augment/devpod/crd/devpodv1",
        "//research/infra/lib/slack",
        "@com_github_mattn_go_shellwords//:go-shellwords",
        "@com_github_slack_go_slack//:slack",
        "@com_github_slack_go_slack//socketmode",
        "@com_github_spf13_cobra//:cobra",
        "@io_k8s_apimachinery//pkg/api/resource",
    ],
)

go_test(
    name = "augislack_test",
    srcs = ["augi-slack_test.go"],
    embed = [":augislack"],
)

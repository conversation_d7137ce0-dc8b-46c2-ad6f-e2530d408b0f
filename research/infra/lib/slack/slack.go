package slack

import (
	"context"
	"strings"

	"github.com/slack-go/slack"

	"github.com/augmentcode/augment/infra/lib/logger"
)

// Client is a generic Slack API client.
type Client struct {
	logger.Logger

	api *slack.Client
}

// NewClient builds a new `Client`. The `appTok` may be empty.
func NewClient(botTok, appTok string) *Client {
	api := slack.New(
		botTok,
		slack.OptionDebug(false),
		slack.OptionAppLevelToken(appTok),
	)
	c := &Client{
		Logger: logger.WithTag(logger.New(nil), "slack"),
		api:    api,
	}
	if !strings.HasPrefix(botTok, "xoxb-") {
		c.<PERSON>(`Slack Bot <PERSON>ken should have "xoxb-" prefix, this client will probably fail.`)
	}
	if appTok != "" && !strings.HasPrefix(appTok, "xapp-") {
		c.<PERSON>(`Slack App Token should have "xapp-" prefix, this client might fail.`)
	}
	return c
}

func (c *Client) PostMessage(ctx context.Context, chanID string, opts ...slack.MsgOption) (string, error) {
	_, ts, err := c.api.PostMessageContext(ctx, chanID, opts...)
	return ts, err
}

func (c *Client) UpdateMessage(ctx context.Context, chanID, ts string, opts ...slack.MsgOption) (string, error) {
	_, ts2, _, err := c.api.UpdateMessageContext(ctx, chanID, ts, opts...)
	return ts2, err
}

func (c *Client) Users(ctx context.Context, opts ...slack.GetUsersOption) ([]slack.User, error) {
	return c.api.GetUsersContext(ctx, opts...)
}

func (c *Client) UserProfile(ctx context.Context, userID string) (*slack.UserProfile, error) {
	return c.api.GetUserProfileContext(ctx, &slack.GetUserProfileParameters{UserID: userID})
}

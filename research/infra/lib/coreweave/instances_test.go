package coreweave

import (
	"testing"
)

func TestInstancesFromFallback(t *testing.T) {
	i, err := InstancesFromFallback()
	if err != nil {
		t.Fatal(err)
	}
	if got, want := len(i.GPU), 13; got != want {
		t.<PERSON><PERSON>rf("len(GPU): got %v, want %v.", got, want)
	}
	if got, want := len(i.CPU), 7; got != want {
		t.<PERSON><PERSON><PERSON>("len(CPU): got %v, want %v.", got, want)
	}
	if got, want := len(i.Storage), 20; got != want {
		t.Errorf("len(Storage): got %v, want %v.", got, want)
	}

	gpu := i.GPU[0]
	gpu.RowHeader() // don't crash
	if got, want := gpu.Row(), "H100_NVLINK_80GB\t8\t128\t1000G\t16\t16\t125.00G\t7.81G\t120.00G\t8\t6%"; got != want {
		t.<PERSON><PERSON><PERSON>("gpu.Row():\n\t-%s\n\t+%s", want, got)
	}

	cpu := i.CPU[0]
	cpu.RowHeader() // don't crash
	if got, want := cpu.Row(), "amd-epyc-milan\t46\t160G\t3.48G\t4.00G\t40\t87%"; got != want {
		t.Errorf("cpu.Row():\n\t-%s\n\t+%s", want, got)
	}
}

// Package coreweave provides support for some of the "documented" CoreWeave APIs:
//
//	https://gist.github.com/salanki/13aab101723134c9aceda8ceb61aa1f7
//
// ... and any other CoreWeave-specific utils.
package coreweave

import (
	"context"
	"fmt"
	"io"
	"net/http"
)

type Client struct {
	http *http.Client
}

func New() *Client {
	return &Client{
		http: http.DefaultClient,
	}
}

func (c Client) HTTPClient() *http.Client {
	return c.http
}

func (c Client) req(ctx context.Context, method, url string) (*http.Response, []byte, error) {
	req, err := http.NewRequestWithContext(ctx, method, url, nil)
	if err != nil {
		return nil, nil, err
	}
	resp, err := c.http.Do(req)
	if err != nil {
		return resp, nil, err
	}
	defer resp.Body.Close()
	buf, err := io.ReadAll(resp.Body)
	if err != nil {
		return resp, nil, err
	}
	return resp, buf, nil
}

func (c Client) get(ctx context.Context, url string) (*http.Response, []byte, error) {
	resp, buf, err := c.req(ctx, http.MethodGet, url)
	if err != nil {
		return resp, buf, err
	}
	if resp.StatusCode != http.StatusOK {
		return resp, buf, fmt.Errorf("%s", resp.Status)
	}
	return resp, buf, nil
}

func (c Client) InstancesRaw(ctx context.Context) ([]byte, error) {
	_, buf, err := c.get(ctx, "https://www.coreweave.com/cloud/api/v1/metadata/instances")
	return buf, err
}

func (c Client) Instances(ctx context.Context) (Instances, error) {
	data, err := c.InstancesRaw(ctx)
	if err != nil {
		return Instances{}, err
	}
	return InstancesFromBytes(data)
}

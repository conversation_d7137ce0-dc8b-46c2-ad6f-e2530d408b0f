[{"id": "Quadro_RTX_4000", "name": "NVIDIA Quadro RTX 4000", "nodeSelector": {"label": "gpu.nvidia.com/class", "value": "Quadro_RTX_4000"}, "connectivity": {"interfaceRate": [10000000000, 1000000000]}, "ephemeralStorage": 100000000000, "optimizedFor": ["batch", "pixel", "vdi"], "specs": {"NVLINK": false, "VRAM": 8000000000, "VRAMDescription": "8GB GDDR6", "cudaParallelProcessingCores": 2304}, "virtualization": {"enabled": true, "supportedOSTypes": ["linux", "windows"]}, "type": "gpu", "gpu": {"billingRate": 0.24, "minCPUPerGPU": 1, "maxCPUPerGPU": 22, "maxGPUperInstance": 7}, "cpu": {"minMemoryPerCPU": 1000000000, "maxMemoryPerCPU": 120000000000, "maxCPUPerInstance": 30, "billingRate": 0.01, "memory": {"maxMemoryPerInstance": 220000000000, "billingRate": 0.005}}}, {"id": "Quadro_RTX_5000", "name": "NVIDIA Quadro RTX 5000", "nodeSelector": {"label": "gpu.nvidia.com/class", "value": "Quadro_RTX_5000"}, "connectivity": {"interfaceRate": [10000000000]}, "ephemeralStorage": 450000000000, "optimizedFor": ["batch", "render", "vdi"], "specs": {"NVLINK": false, "VRAM": 16000000000, "VRAMDescription": "16GB GDDR6", "cudaParallelProcessingCores": 3072}, "virtualization": {"enabled": true, "supportedOSTypes": ["linux", "windows"]}, "type": "gpu", "gpu": {"billingRate": 0.57, "minCPUPerGPU": 1, "maxCPUPerGPU": 24, "maxGPUperInstance": 4}, "cpu": {"minMemoryPerCPU": 1000000000, "maxMemoryPerCPU": 120000000000, "maxCPUPerInstance": 30, "billingRate": 0.01, "memory": {"maxMemoryPerInstance": 154000000000, "billingRate": 0.005}}}, {"id": "RTX_A4000", "name": "NVIDIA RTX A4000", "nodeSelector": {"label": "gpu.nvidia.com/class", "value": "RTX_A4000"}, "connectivity": {"interfaceRate": [100000000000]}, "ephemeralStorage": 450000000000, "optimizedFor": ["rendering", "vdi", "pixel", "batch"], "specs": {"NVLINK": false, "VRAM": 16000000000, "VRAMDescription": "16GB GDDR6", "cudaParallelProcessingCores": 6144}, "virtualization": {"enabled": true, "supportedOSTypes": ["linux", "windows"]}, "type": "gpu", "gpu": {"billingRate": 0.61, "minCPUPerGPU": 1, "maxCPUPerGPU": 32, "maxGPUperInstance": 7}, "cpu": {"minMemoryPerCPU": 1000000000, "maxMemoryPerCPU": 8000000000, "maxCPUPerInstance": 46, "billingRate": 0.01, "memory": {"maxMemoryPerInstance": 350000000000, "billingRate": 0.005}}}, {"id": "RTX_A5000", "name": "NVIDIA RTX A5000", "nodeSelector": {"label": "gpu.nvidia.com/class", "value": "RTX_A5000"}, "connectivity": {"interfaceRate": [100000000000]}, "ephemeralStorage": 450000000000, "optimizedFor": ["rendering", "vdi", "pixel"], "specs": {"NVLINK": false, "VRAM": 24000000000, "VRAMDescription": "24GB GDDR6", "cudaParallelProcessingCores": 8192}, "virtualization": {"enabled": true, "supportedOSTypes": ["linux", "windows"]}, "type": "gpu", "gpu": {"billingRate": 0.77, "minCPUPerGPU": 1, "maxCPUPerGPU": 30, "maxGPUperInstance": 8}, "cpu": {"minMemoryPerCPU": 1000000000, "maxMemoryPerCPU": 48000000000, "maxCPUPerInstance": 122, "billingRate": 0.01, "memory": {"maxMemoryPerInstance": 690000000000, "billingRate": 0.005}}}, {"id": "H100_NVLINK_80GB", "name": "NVIDIA H100 for NVLINK", "nodeSelector": {"label": "gpu.nvidia.com/class", "value": "H100_NVLINK_80GB"}, "connectivity": {"interfaceRate": [100000000000]}, "ephemeralStorage": 7280000000000, "optimizedFor": ["training"], "specs": {"NVLINK": true, "VRAM": 80000000000, "VRAMDescription": "80GB HBM3", "cudaParallelProcessingCores": 18432}, "virtualization": {"enabled": false, "supportedOSTypes": []}, "type": "gpu", "gpu": {"billingRate": 4.87, "minCPUPerGPU": 1, "maxCPUPerGPU": 16, "maxGPUperInstance": 8}, "cpu": {"minMemoryPerCPU": 1000000000, "maxMemoryPerCPU": 120000000000, "maxCPUPerInstance": 128, "billingRate": 0.01, "memory": {"maxMemoryPerInstance": 1000000000000, "billingRate": 0.005}}}, {"id": "RTX_A6000", "name": "NVIDIA RTX A6000", "nodeSelector": {"label": "gpu.nvidia.com/class", "value": "RTX_A6000"}, "connectivity": {"interfaceRate": [10000000000]}, "ephemeralStorage": 450000000000, "optimizedFor": ["rendering", "vdi", "training"], "specs": {"NVLINK": false, "VRAM": 48000000000, "VRAMDescription": "48GB GDDR6", "cudaParallelProcessingCores": 10752}, "virtualization": {"enabled": true, "supportedOSTypes": ["linux", "windows"]}, "type": "gpu", "gpu": {"billingRate": 1.28, "minCPUPerGPU": 1, "maxCPUPerGPU": 40, "maxGPUperInstance": 8}, "cpu": {"minMemoryPerCPU": 1000000000, "maxMemoryPerCPU": 124000000000, "maxCPUPerInstance": 126, "billingRate": 0.01, "memory": {"maxMemoryPerInstance": 690000000000, "billingRate": 0.005}}}, {"id": "A40", "name": "NVIDIA A40", "nodeSelector": {"label": "gpu.nvidia.com/class", "value": "A40"}, "connectivity": {"interfaceRate": [400000000000]}, "ephemeralStorage": 450000000000, "optimizedFor": ["rendering", "vdi", "training"], "specs": {"NVLINK": false, "VRAM": 48000000000, "VRAMDescription": "48GB GDDR6", "cudaParallelProcessingCores": 10752}, "virtualization": {"enabled": true, "supportedOSTypes": ["linux"]}, "type": "gpu", "gpu": {"billingRate": 1.28, "minCPUPerGPU": 1, "maxCPUPerGPU": 40, "maxGPUperInstance": 8}, "cpu": {"minMemoryPerCPU": 1000000000, "maxMemoryPerCPU": 48000000000, "maxCPUPerInstance": 94, "billingRate": 0.01, "memory": {"maxMemoryPerInstance": 686000000000, "billingRate": 0.005}}}, {"id": "Tesla_V100_PCIE", "name": "NVIDIA Tesla V100 for PCIe", "nodeSelector": {"label": "gpu.nvidia.com/class", "value": "Tesla_V100_PCIE"}, "connectivity": {"interfaceRate": [1000000000]}, "ephemeralStorage": 100000000000, "optimizedFor": ["inference", "render", "batch"], "specs": {"NVLINK": false, "VRAM": 16000000000, "VRAMDescription": "16GB HBM2", "cudaParallelProcessingCores": 5120}, "virtualization": {"enabled": true, "supportedOSTypes": ["linux"]}, "type": "gpu", "gpu": {"billingRate": 0.47, "minCPUPerGPU": 1, "maxCPUPerGPU": 8, "maxGPUperInstance": 7}, "cpu": {"minMemoryPerCPU": 1000000000, "maxMemoryPerCPU": 60000000000, "maxCPUPerInstance": 44, "billingRate": 0.005, "memory": {"maxMemoryPerInstance": 224000000000, "billingRate": 0.005}}}, {"id": "Tesla_V100_NVLINK", "name": "NVIDIA Tesla V100 for NVLINK", "nodeSelector": {"label": "gpu.nvidia.com/class", "value": "Tesla_V100_NVLINK"}, "connectivity": {"interfaceRate": [10000000000]}, "ephemeralStorage": 450000000000, "optimizedFor": ["training"], "specs": {"NVLINK": true, "VRAM": 16000000000, "VRAMDescription": "16GB HBM2", "cudaParallelProcessingCores": 5120}, "virtualization": {"enabled": true, "supportedOSTypes": ["linux"]}, "type": "gpu", "gpu": {"billingRate": 0.8, "minCPUPerGPU": 1, "maxCPUPerGPU": 8, "maxGPUperInstance": 8}, "cpu": {"minMemoryPerCPU": 1000000000, "maxMemoryPerCPU": 120000000000, "maxCPUPerInstance": 60, "billingRate": 0.01, "memory": {"maxMemoryPerInstance": 920000000000, "billingRate": 0.005}}}, {"id": "A100_PCIE_40GB", "name": "NVIDIA A100 40GB for PCIe", "nodeSelector": {"label": "gpu.nvidia.com/class", "value": "A100_PCIE_40GB"}, "connectivity": {"interfaceRate": [100000000000]}, "ephemeralStorage": 450000000000, "optimizedFor": ["training"], "specs": {"NVLINK": true, "VRAM": 40000000000, "VRAMDescription": "40GB HBM2e", "cudaParallelProcessingCores": 6912}, "virtualization": {"enabled": true, "supportedOSTypes": ["linux"]}, "type": "gpu", "gpu": {"billingRate": 2.06, "minCPUPerGPU": 1, "maxCPUPerGPU": 18, "maxGPUperInstance": 8}, "cpu": {"minMemoryPerCPU": 1000000000, "maxMemoryPerCPU": 24000000000, "maxCPUPerInstance": 94, "billingRate": 0.01, "memory": {"maxMemoryPerInstance": 472000000000, "billingRate": 0.005}}}, {"id": "A100_PCIE_80GB", "name": "NVIDIA A100 80GB for PCIe", "nodeSelector": {"label": "gpu.nvidia.com/class", "value": "A100_PCIE_80GB"}, "connectivity": {"interfaceRate": [100000000000]}, "ephemeralStorage": 450000000000, "optimizedFor": ["training"], "specs": {"NVLINK": false, "VRAM": 40000000000, "VRAMDescription": "80GB HBM2e", "cudaParallelProcessingCores": 6912}, "virtualization": {"enabled": true, "supportedOSTypes": ["linux"]}, "type": "gpu", "gpu": {"billingRate": 2.21, "minCPUPerGPU": 1, "maxCPUPerGPU": 30, "maxGPUperInstance": 8}, "cpu": {"minMemoryPerCPU": 1000000000, "maxMemoryPerCPU": 48000000000, "maxCPUPerInstance": 94, "billingRate": 0.01, "memory": {"maxMemoryPerInstance": 690000000000, "billingRate": 0.005}}}, {"id": "A100_NVLINK", "name": "NVIDIA A100 40 GB for NVLINK", "nodeSelector": {"label": "gpu.nvidia.com/class", "value": "A100_NVLINK"}, "connectivity": {"interfaceRate": [100000000000]}, "ephemeralStorage": 450000000000, "optimizedFor": ["training"], "specs": {"NVLINK": true, "VRAM": 40000000000, "VRAMDescription": "40GB HBM2e", "cudaParallelProcessingCores": 6912}, "virtualization": {"enabled": false, "supportedOSTypes": ["linux"]}, "type": "gpu", "gpu": {"billingRate": 2.06, "minCPUPerGPU": 1, "maxCPUPerGPU": 30, "maxGPUperInstance": 4}, "cpu": {"minMemoryPerCPU": 1000000000, "maxMemoryPerCPU": 240000000000, "maxCPUPerInstance": 124, "billingRate": 0.01, "memory": {"maxMemoryPerInstance": 948000000000, "billingRate": 0.005}}}, {"id": "A100_NVLINK_80GB", "name": "NVIDIA A100 80GB for NVLINK", "nodeSelector": {"label": "gpu.nvidia.com/class", "value": "A100_NVLINK_80GB"}, "connectivity": {"interfaceRate": [100000000000]}, "ephemeralStorage": 1500000000000, "optimizedFor": ["training"], "specs": {"NVLINK": true, "VRAM": 80000000000, "VRAMDescription": "80GB HBM2e", "cudaParallelProcessingCores": 6912}, "virtualization": {"enabled": false, "supportedOSTypes": []}, "type": "gpu", "gpu": {"billingRate": 2.21, "minCPUPerGPU": 1, "maxCPUPerGPU": 30, "maxGPUperInstance": 8}, "cpu": {"minMemoryPerCPU": 1000000000, "maxMemoryPerCPU": 240000000000, "maxCPUPerInstance": 124, "billingRate": 0.01, "memory": {"maxMemoryPerInstance": 948000000000, "billingRate": 0.005}}}, {"id": "amd-epyc-rome", "name": "AMD Epyc Rome", "nodeSelector": {"label": "node.coreweave.cloud/cpu", "value": "amd-epyc-rome"}, "connectivity": {"interfaceRate": [10000000000]}, "ephemeralStorage": 10000000000, "optimizedFor": ["render", "batch"], "specs": {"AVX-1": true, "AVX-2": true, "AVX-512": false}, "virtualization": {"enabled": true, "supportedOSTypes": ["linux", "windows"]}, "type": "cpu", "gpu": null, "cpu": {"minMemoryPerCPU": 3000000000, "maxMemoryPerCPU": 4000000000, "maxCPUPerInstance": 46, "billingRate": 0.03, "memory": {"maxMemoryPerInstance": 160000000000, "billingRate": 0}}}, {"id": "amd-epyc-milan", "name": "AMD Epyc Milan", "nodeSelector": {"label": "node.coreweave.cloud/cpu", "value": "amd-epyc-milan"}, "connectivity": {"interfaceRate": [10000000000]}, "ephemeralStorage": 10000000000, "optimizedFor": ["render", "batch"], "specs": {"AVX-1": true, "AVX-2": true, "AVX-512": false}, "virtualization": {"enabled": true, "supportedOSTypes": ["linux", "windows"]}, "type": "cpu", "gpu": null, "cpu": {"minMemoryPerCPU": 3000000000, "maxMemoryPerCPU": 4000000000, "maxCPUPerInstance": 46, "billingRate": 0.035, "memory": {"maxMemoryPerInstance": 160000000000, "billingRate": 0}}}, {"id": "intel-xeon-v1", "name": "Intel Xeon v1", "nodeSelector": {"label": "node.coreweave.cloud/cpu", "value": "intel-xeon-v1"}, "connectivity": {"interfaceRate": [1000000000, 10000000000]}, "ephemeralStorage": 10000000000, "optimizedFor": ["batch", "render"], "specs": {"AVX-1": true, "AVX-2": false}, "virtualization": {"enabled": false, "supportedOSTypes": []}, "type": "cpu", "gpu": null, "cpu": {"minMemoryPerCPU": 3000000000, "maxMemoryPerCPU": 3000000000, "maxCPUPerInstance": 78, "billingRate": 0.009, "memory": {"maxMemoryPerInstance": 224000000000, "billingRate": 0}}}, {"id": "intel-xeon-v2", "name": "Intel Xeon v2", "nodeSelector": {"label": "node.coreweave.cloud/cpu", "value": "intel-xeon-v2"}, "connectivity": {"interfaceRate": [1000000000, 10000000000]}, "ephemeralStorage": 10000000000, "optimizedFor": ["batch", "render"], "specs": {"AVX-1": true, "AVX-2": false}, "virtualization": {"enabled": false, "supportedOSTypes": []}, "type": "cpu", "gpu": null, "cpu": {"minMemoryPerCPU": 3000000000, "maxMemoryPerCPU": 3000000000, "maxCPUPerInstance": 92, "billingRate": 0.009, "memory": {"maxMemoryPerInstance": 266000000000, "billingRate": 0}}}, {"id": "intel-xeon-v3", "name": "Intel Xeon v3", "nodeSelector": {"label": "node.coreweave.cloud/cpu", "value": "intel-xeon-v3"}, "connectivity": {"interfaceRate": [10000000000]}, "ephemeralStorage": 10000000000, "optimizedFor": ["batch", "render"], "specs": {"AVX-1": true, "AVX-2": true, "AVX-512": false}, "virtualization": {"enabled": true, "supportedOSTypes": ["linux", "windows"]}, "type": "cpu", "gpu": null, "cpu": {"minMemoryPerCPU": 2000000000, "maxMemoryPerCPU": 4000000000, "maxCPUPerInstance": 70, "billingRate": 0.0125, "memory": {"maxMemoryPerInstance": 154000000000, "billingRate": 0}}}, {"id": "intel-xeon-v4", "name": "Intel Xeon v4", "nodeSelector": {"label": "node.coreweave.cloud/cpu", "value": "intel-xeon-v4"}, "connectivity": {"interfaceRate": [1000000000, 10000000000]}, "ephemeralStorage": 10000000000, "optimizedFor": ["batch", "render"], "specs": {"AVX-1": true, "AVX-2": true, "AVX-512": false}, "virtualization": {"enabled": true, "supportedOSTypes": ["linux", "windows"]}, "type": "cpu", "gpu": null, "cpu": {"minMemoryPerCPU": 2000000000, "maxMemoryPerCPU": 4000000000, "maxCPUPerInstance": 60, "billingRate": 0.02, "memory": {"maxMemoryPerInstance": 222000000000, "billingRate": 0}}}, {"id": "intel-xeon-scalable", "name": "Intel Xeon Scalable", "nodeSelector": {"label": "node.coreweave.cloud/cpu", "value": "intel-xeon-scalable"}, "connectivity": {"interfaceRate": [10000000000]}, "ephemeralStorage": 10000000000, "optimizedFor": ["batch", "render"], "specs": {"AVX-1": true, "AVX-2": true, "AVX-512": true}, "virtualization": {"enabled": true, "supportedOSTypes": ["linux", "windows"]}, "type": "cpu", "gpu": null, "cpu": {"minMemoryPerCPU": 2000000000, "maxMemoryPerCPU": 6000000000, "maxCPUPerInstance": 94, "billingRate": 0.03, "memory": {"maxMemoryPerInstance": 222000000000, "billingRate": 0}}}, {"id": "block-hdd-ewr1", "type": "storage", "name": "Block HDD EWR1", "storageSelector": "block-hdd-ewr1", "billingRate": 5.48e-05}, {"id": "block-hdd-las1", "type": "storage", "name": "Block HDD LAS1", "storageSelector": "block-hdd-las1", "billingRate": 5.48e-05}, {"id": "block-hdd-ord1", "type": "storage", "name": "Block HDD ORD1", "storageSelector": "block-hdd-ord1", "billingRate": 5.48e-05}, {"id": "block-hdd-lga1", "type": "storage", "name": "Block HDD ORD1", "storageSelector": "block-hdd-lga1", "billingRate": 5.48e-05}, {"id": "block-nvme-ewr1", "type": "storage", "name": "Block NVMe EWR1", "storageSelector": "block-nvme-ewr1", "billingRate": 9.6e-05}, {"id": "block-nvme-las1", "type": "storage", "name": "Block NVMe LAS1", "storageSelector": "block-nvme-las1", "billingRate": 9.6e-05}, {"id": "block-nvme-ord1", "type": "storage", "name": "Block NVMe ORD1", "storageSelector": "block-nvme-ord1", "billingRate": 9.6e-05}, {"id": "block-nvme-lga1", "type": "storage", "name": "Block NVMe EWR1", "storageSelector": "block-nvme-lga1", "billingRate": 9.6e-05}, {"id": "shared-nvme-las1", "type": "storage", "name": "Shared NVMe LAS1", "storageSelector": "shared-nvme-las1", "billingRate": 9.6e-05}, {"id": "shared-hdd-ewr1", "type": "storage", "name": "Shared HDD EWR1", "storageSelector": "shared-hdd-ewr1", "billingRate": 5.48e-05}, {"id": "shared-nvme-ewr1", "type": "storage", "name": "Shared NVMe EWR1", "storageSelector": "shared-nvme-ewr1", "billingRate": 9.6e-05}, {"id": "shared-hdd-ord1", "type": "storage", "name": "Shared HDD ORD1", "storageSelector": "shared-hdd-ord1", "billingRate": 5.48e-05}, {"id": "shared-nvme-ord1", "type": "storage", "name": "Shared NVMe ORD1", "storageSelector": "shared-nvme-ord1", "billingRate": 9.6e-05}, {"id": "shared-nvme-lga1", "type": "storage", "name": "Shared NVMe LGA1", "storageSelector": "shared-nvme-lga1", "billingRate": 9.6e-05}, {"id": "shared-hdd-las1", "type": "storage", "name": "Shared HDD LAS1", "storageSelector": "shared-hdd-las1", "billingRate": 5.48e-05}, {"id": "shared-vast-las1", "type": "storage", "name": "Premium NVMe LAS1", "storageSelector": "shared-vast-las1", "billingRate": 0.000137}, {"id": "shared-vast-lga1", "type": "storage", "name": "Premium NVMe LGA1", "storageSelector": "shared-vast-lga1", "billingRate": 0.000137}, {"id": "object-standard-ord1", "type": "storage", "name": "Object Standard ORD1", "storageSelector": "object-standard-ord1", "billingRate": 4.111e-05}, {"id": "object-standard-lga1", "type": "storage", "name": "Object Standard LGA1", "storageSelector": "object-standard-lga1", "billingRate": 4.111e-05}, {"id": "object-standard-las1", "type": "storage", "name": "Object Standard LAS1", "storageSelector": "object-standard-las1", "billingRate": 4.111e-05}]
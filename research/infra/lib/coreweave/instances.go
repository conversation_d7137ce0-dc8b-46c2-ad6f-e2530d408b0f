package coreweave

import (
	"encoding/json"
	"fmt"
	"os"
	"sort"
	"strings"

	_ "embed"
)

//go:embed instances.fallback.json
var instancesFallback []byte

// Instances is a variation of the raw response from the CoreWeave API. The API
// returns a list of objects with a `type` field (one of "gpu", "cpu", or
// "storage") and other fields. This struct breaks up the lists into their
// respective types.
type Instances struct {
	GPU     []GPUInstance
	CPU     []CPUInstance
	Storage []StorageInstance
}

func InstancesFromBytes(data []byte) (Instances, error) {
	i := Instances{}
	return i, i.UnmarshalJSON(data)
}

func InstancesFromFallback() (Instances, error) {
	return InstancesFromBytes(instancesFallback)
}

func (i *Instances) UnmarshalJSON(data []byte) error {
	rawItems := []json.RawMessage{}
	if err := json.Unmarshal(data, &rawItems); err != nil {
		return err
	}
	for _, rawItem := range rawItems {
		inst := Instance{}
		if err := json.Unmarshal(rawItem, &inst); err != nil {
			return err
		}
		switch strings.ToLower(inst.Type) {
		case "gpu":
			gpuInst := GPUInstance{}
			if err := json.Unmarshal(rawItem, &gpuInst); err != nil {
				return err
			}
			i.GPU = append(i.GPU, gpuInst)
		case "cpu":
			cpuInst := CPUInstance{}
			if err := json.Unmarshal(rawItem, &cpuInst); err != nil {
				return err
			}
			i.CPU = append(i.CPU, cpuInst)
		case "storage":
			storageInst := StorageInstance{}
			if err := json.Unmarshal(rawItem, &storageInst); err != nil {
				return err
			}
			i.Storage = append(i.Storage, storageInst)
		default:
			fmt.Fprintf(os.Stderr, "Warning: Ignoring unknown CoreWeave instance of type '%s': (%+v).", inst.Type, inst)
		}
	}

	sort.Slice(i.GPU, func(ai, bi int) bool {
		a, b := i.GPU[ai], i.GPU[bi]
		if a.GPU.BillingRate == b.GPU.BillingRate {
			return a.ID < b.ID
		}
		return a.GPU.BillingRate > b.GPU.BillingRate
	})
	sort.Slice(i.CPU, func(ai, bi int) bool {
		a, b := i.CPU[ai], i.CPU[bi]
		if a.CPU.BillingRate == b.CPU.BillingRate {
			return a.ID < b.ID
		}
		return a.CPU.BillingRate > b.CPU.BillingRate
	})

	return nil
}

// Instance is the common set of fields that apply to all instances.
type Instance struct {
	Type string `json:"type"`
	ID   string `json:"id"`
	Name string `json:"name"`
}

func (i Instance) String() string {
	return fmt.Sprintf("%s %s %s", i.Type, i.ID, i.Name)
}

type CPUInstance struct {
	Instance
	NodeSelector struct {
		Label string `json:"label"`
		Value string `json:"value"`
	} `json:"nodeSelector"`
	Connectivity struct {
		InterfaceRate []int `json:"interfaceRate"`
	} `json:"connectivity"`
	EphemeralStorage int      `json:"ephemeralStorage"`
	OptimizedFor     []string `json:"optimizedFor"`

	CPU struct {
		MinMemoryPerCPU   int     `json:"minMemoryPerCPU"`
		MaxMemoryPerCPU   int     `json:"maxMemoryPerCPU"`
		MaxCPUPerInstance int     `json:"maxCPUPerInstance"`
		BillingRate       float64 `json:"billingRate"`
		Memory            struct {
			MaxMemoryPerInstance int     `json:"maxMemoryPerInstance"`
			BillingRate          float64 `json:"billingRate"`
		} `json:"memory"`
	} `json:"cpu"`
}

func (cpu CPUInstance) CPUs() int {
	return cpu.CPU.MaxCPUPerInstance
}

func (cpu CPUInstance) MemoryB() int {
	return cpu.CPU.Memory.MaxMemoryPerInstance
}

func (cpu CPUInstance) MemoryG() float64 {
	return float64(cpu.MemoryB()) / 1000.0 / 1000.0 / 1000.0
}

func (cpu CPUInstance) MemoryPerCPUG() float64 {
	return float64(cpu.MemoryG()) / float64(cpu.CPUs())
}

func (cpu CPUInstance) MaxMemoryPerCPUB() int {
	return cpu.CPU.MaxMemoryPerCPU
}

func (cpu CPUInstance) MaxMemoryPerCPUG() float64 {
	return float64(cpu.MaxMemoryPerCPUB()) / 1000.0 / 1000.0 / 1000.0
}

func (cpu CPUInstance) MaxMemoryCPUs() int {
	return cpu.MemoryB() / cpu.MaxMemoryPerCPUB()
}

func (cpu CPUInstance) MaxMemoryCPUsRatio() float64 {
	return float64(cpu.MaxMemoryCPUs()) / float64(cpu.CPUs())
}

func (cpu CPUInstance) RowHeader() string {
	items := []string{
		"id",
		"cpus",
		"mem",
		"mem/cpu",
		"mem/cpu (max)",
		"cpus (w/ max mem)",
		"cpus % (w/ max mem)",
	}
	return strings.Join(items, "\t")
}

func (cpu CPUInstance) Row() string {
	items := []string{
		cpu.ID,
		fmt.Sprintf("%d", cpu.CPUs()),
		fmt.Sprintf("%.0fG", cpu.MemoryG()),
		fmt.Sprintf("%.2fG", cpu.MemoryPerCPUG()),
		fmt.Sprintf("%.2fG", cpu.MaxMemoryPerCPUG()),
		fmt.Sprintf("%d", cpu.MaxMemoryCPUs()),
		fmt.Sprintf("%.0f%%", 100.0*cpu.MaxMemoryCPUsRatio()),
	}
	return strings.Join(items, "\t")
}

type GPUInstance struct {
	CPUInstance

	GPU struct {
		BillingRate       float64 `json:"billingRate"`
		MinCPUPerGPU      int     `json:"minCPUPerGPU"`
		MaxCPUPerGPU      int     `json:"maxCPUPerGPU"`
		MaxGPUPerInstance int     `json:"maxGPUperInstance"`
	} `json:"gpu"`
}

func (gpu GPUInstance) GPUs() int {
	return gpu.GPU.MaxGPUPerInstance
}

func (gpu GPUInstance) CPUperGPU() int {
	return gpu.CPUs() / gpu.GPUs()
}

func (gpu GPUInstance) MaxCPUperGPU() int {
	return gpu.GPU.MaxCPUPerGPU
}

func (gpu GPUInstance) MemoryPerGPUG() float64 {
	return float64(gpu.MemoryG()) / float64(gpu.GPUs())
}

func (gpu GPUInstance) RowHeader() string {
	items := []string{
		"id",
		"gpus",
		"cpus",
		"mem",
		"cpu/gpu",
		"cpu/gpu (max)",
		"mem/gpu",
		"mem/cpu",
		"mem/cpu (max)",
		"cpus (w/ max mem)",
		"cpus % (w/ max mem)",
	}
	return strings.Join(items, "\t")
}

func (gpu GPUInstance) Row() string {
	items := []string{
		gpu.ID,
		fmt.Sprintf("%d", gpu.GPUs()),
		fmt.Sprintf("%d", gpu.CPUs()),
		fmt.Sprintf("%.0fG", gpu.MemoryG()),
		fmt.Sprintf("%d", gpu.CPUperGPU()),
		fmt.Sprintf("%d", gpu.MaxCPUperGPU()),
		fmt.Sprintf("%.2fG", gpu.MemoryPerGPUG()),
		fmt.Sprintf("%.2fG", gpu.MemoryPerCPUG()),
		fmt.Sprintf("%.2fG", gpu.MaxMemoryPerCPUG()),
		fmt.Sprintf("%d", gpu.MaxMemoryCPUs()),
		fmt.Sprintf("%.0f%%", 100.0*gpu.MaxMemoryCPUsRatio()),
	}
	return strings.Join(items, "\t")
}

type StorageInstance struct {
	Instance
	StorageSelector string  `json:"storageSelector"`
	BillingRate     float64 `json:"billingRate"`
}

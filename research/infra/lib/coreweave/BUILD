load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

package(default_visibility = ["//research/infra:internal"])

go_library(
    name = "coreweave",
    srcs = [
        "coreweave.go",
        "instances.go",
    ],
    embedsrcs = ["instances.fallback.json"],
    importpath = "github.com/augmentcode/augment/research/infra/lib/coreweave",
)

go_test(
    name = "coreweave_test",
    srcs = ["instances_test.go"],
    embed = [":coreweave"],
)

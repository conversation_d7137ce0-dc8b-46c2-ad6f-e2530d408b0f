load("@io_bazel_rules_go//go:def.bzl", "go_library")

package(default_visibility = ["//research/infra:internal"])

go_library(
    name = "gcs",
    srcs = [
        "bucket.go",
        "client.go",
        "tree.go",
    ],
    importpath = "github.com/augmentcode/augment/research/infra/lib/gcs",
    deps = [
        "//infra/lib/logger",
        "@com_google_cloud_go_storage//:storage",
        "@com_google_cloud_go_storage//control/apiv2",
        "@com_google_cloud_go_storage//control/apiv2/controlpb",
        "@org_golang_google_api//iterator",
        "@org_golang_x_sync//errgroup",
    ],
)

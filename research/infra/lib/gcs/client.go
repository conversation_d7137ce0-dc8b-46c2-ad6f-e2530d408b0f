package gcs

import (
	"context"

	"cloud.google.com/go/storage"
	ctrlapi "cloud.google.com/go/storage/control/apiv2"

	"github.com/augmentcode/augment/infra/lib/logger"
)

// Client is a wrapper around a GCS Client && a GCS Control API Client.
type Client struct {
	logger.Logger
	cli  *storage.Client
	ctrl *ctrlapi.StorageControlClient
}

// NewClient builds a new `Client`.
func NewClient(ctx context.Context) (*Client, error) {
	cli, err := storage.NewClient(ctx)
	if err != nil {
		return nil, err
	}
	ctrl, err := ctrlapi.NewStorageControlClient(ctx)
	if err != nil {
		return nil, err
	}

	return &Client{
		Logger: logger.New(nil),
		cli:    cli,
		ctrl:   ctrl,
	}, nil
}

// NewBucketClient is a convenience for `NewClient().Bucket()` when the caller is only
// going to be interested in a single `Bucket`.
func NewBucketClient(ctx context.Context, name string) (*Bucket, error) {
	cli, err := NewClient(ctx)
	if err != nil {
		return nil, err
	}
	return cli.Bucket(name), nil
}

// Bucket returns a new `Bucket`.
func (c *Client) Bucket(name string) *Bucket {
	bh := c.cli.Bucket(name)
	return &Bucket{
		Logger: c.Logger,
		cli:    c,
		bh:     bh,
	}
}

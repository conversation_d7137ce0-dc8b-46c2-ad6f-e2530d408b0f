package gcs

import (
	"fmt"
	"io"
	"sort"
	"strings"

	"github.com/augmentcode/augment/infra/lib/logger"
)

type treeItem struct {
	logger.Logger
	fullname string
	children map[string]*treeItem
}

func newTree(bucket string, names ...string) (*treeItem, error) {
	root := &treeItem{
		Logger:   logger.New(nil),
		fullname: bucket, // This is a "fake" full name w.r.t. GCS HNS Folders. We put it here only for use with `treeItem.Print()`.
		children: map[string]*treeItem{},
	}
	if err := root.Insert(names...); err != nil {
		return nil, err
	}
	return root, nil
}

func (ti *treeItem) Name() string {
	return ti.fullname
}

// IsContainer returns true when the `treeItem` has children. This applies to the Bucket and all
// descendant Folders. The root Bucket itself is **not** considered a Folder.
func (ti *treeItem) IsContainer() bool {
	return ti.children != nil
}

// IsFolder returns true when the `treeItem` is a GCS HNS Folder, which is true simply when
// the name ends in a `/`.
func (ti *treeItem) IsFolder() bool {
	return strings.HasSuffix(ti.Name(), "/")
}

// IsBucket returns true when the `treeItem` represents the root Bucket, which in GCS HNS is **not**
// considered a Folder.
func (ti *treeItem) IsBucket() bool {
	return ti.IsContainer() && !ti.IsFolder()
}

func (ti *treeItem) NumChildren() int {
	return len(ti.children)
}

func (ti *treeItem) IsEmpty() bool {
	return ti.IsFolder() && ti.NumChildren() == 0
}

func (ti *treeItem) Print(pfx string, w io.Writer) {
	if !ti.IsFolder() {
		fmt.Fprintf(w, "%s%s\n", pfx, ti.Name())
	} else if ti.IsEmptyRecursive() {
		fmt.Fprintf(w, "%s%s [empty]\n", pfx, ti.Name())
	} else if num := ti.NumChildren(); num == 1 {
		fmt.Fprintf(w, "%s%s [%d child]\n", pfx, ti.Name(), num)
	} else {
		fmt.Fprintf(w, "%s%s [%d children]\n", pfx, ti.Name(), num)
	}

	children := []string{}
	for child := range ti.children {
		children = append(children, child)
	}
	sort.Strings(children)
	for _, child := range children {
		ti.children[child].Print(pfx+"\t", w)
	}
}

func (ti *treeItem) Insert(names ...string) error {
	for _, n := range names {
		parts := strings.SplitAfter(n, "/")
		if parts[len(parts)-1] == "" {
			parts = parts[:len(parts)-1]
		}
		if err := ti.insertParts(n, parts...); err != nil {
			return err
		}
	}
	return nil
}

func (ti *treeItem) insertParts(name string, parts ...string) error {
	if !ti.IsContainer() {
		return fmt.Errorf("%q: not a folder (or bucket root): cannot insert parts: %v", ti.Name(), parts)
	}
	if len(parts) == 0 {
		return fmt.Errorf("%q: cannot insert empty parts", ti.Name())
	}
	childpart, descparts := parts[0], parts[1:]
	child := ti.children[childpart]
	if child == nil {
		child = &treeItem{
			Logger: ti.Logger,
			fullname: func() string {
				if ti.IsBucket() {
					// The Bucket treeItem name is used for display only. It isn't a prefix for GCS HNS Folder names.
					return childpart
				}
				// Sometimes this subfolder will exist separately as a placeholder object (from pre GCS FUSE HNS support),
				// other times it won't. So creating this child can act like the `implicit-dirs` option.
				return ti.Name() + childpart
			}(),
		}
		if child.IsFolder() {
			child.children = map[string]*treeItem{}
		}
		ti.children[childpart] = child
	}
	if len(descparts) > 0 {
		child.insertParts(name, descparts...)
	}
	return nil
}

func (ti *treeItem) IsEmptyRecursive() bool {
	if !ti.IsFolder() {
		// Only Folders can be considered empty, not normal Objects or the Bucket itself.
		return false
	}
	for _, child := range ti.children {
		if !child.IsEmptyRecursive() {
			return false
		}
	}
	return true
}

func (ti *treeItem) EmptyFolders() []string {
	queue := []*treeItem{ti}
	empty := []string{}

	for len(queue) > 0 {
		cur := queue[0]
		queue = queue[1:]
		if cur.IsEmptyRecursive() {
			empty = append(empty, cur.Name())
		}
		if cur.IsContainer() {
			for _, child := range cur.children {
				queue = append(queue, child)
			}
		}
	}

	return empty
}

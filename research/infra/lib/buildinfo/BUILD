load("//tools/bzl:go.bzl", "go_library", "go_test")

package(default_visibility = ["//research/infra:internal"])

go_library(
    name = "buildinfo",
    srcs = [
        "collect.go",
        "info.go",
    ],
    importpath = "github.com/augmentcode/augment/research/infra/lib/buildinfo",
    x_defs = {
        "bkVersion": "{STABLE_AUGI_VERSION}",
        "bkTimestamp": "{BUILD_TIMESTAMP}",
        "bkBuilder": "{STABLE_AUGI_BUILDER}",
        "bkHostname": "{BUILD_HOST}",
        "bkBranch": "{STABLE_AUGI_BRANCH}",
        "bkCommit": "{STABLE_AUGI_COMMIT}",
    },
    deps = [
        "//infra/lib/logger",
        "//research/infra/lib/augment",
        "@com_github_go_git_go_git_v5//:go-git",
    ],
)

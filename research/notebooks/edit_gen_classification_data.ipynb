{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### This Notebook creates a next edit gen eval set from the Augment repo's Git history."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.next_edits.edit_gen_sampler import EditGenSampler\n", "from research.next_edits.edit_gen_stages import SamplingConfig\n", "\n", "# use commits before \"Return logits in raw_generate_tokens. (#6835)\"\n", "last_commit_hash = \"24dbfb19bc6bd320a7539f9d04521bc935ac0ba4\"\n", "dataset_name = \"augment_classification_v1\"\n", "\n", "sampling_config = SamplingConfig(\n", "    sampler=EditGenSampler(\n", "        random_edit_region_rate=0.5,\n", "        random_target_file_rate=0.2,\n", "        max_diff_tokens=2000,\n", "        rechunker_rate=1.0,  # always use smart chunks to pick edit region\n", "        max_chunk_size=2000,\n", "    ),\n", "    max_problems_per_repo=2000,\n", "    max_problems_per_commit=10,\n", "    max_files_per_repo=10_000,\n", "    timeout_per_repo=1600,\n", "    max_squashes=0,  # change this if need samples with squashed commits\n", "    small_commits_tokens=500,\n", "    turn_into_negative_sample_rate=0.2,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.constants import AUGMENT_ROOT\n", "from research.next_edits.next_edits_dataset import repo_changes_from_commits\n", "\n", "\n", "repo_changes = repo_changes_from_commits(\n", "    AUGMENT_ROOT,\n", "    max_commits=1000,\n", "    max_workers=8,\n", "    last_commit=last_commit_hash,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from tqdm.auto import tqdm\n", "from research.next_edits.edit_gen_stages import repo_changes_to_problems\n", "\n", "problems_iter = repo_changes_to_problems(\n", "    sampling_config,\n", "    repo_changes,\n", "    seed=42,\n", "    start_time=time.time(),\n", ")\n", "\n", "\n", "problems = list(tqdm(problems_iter, total=sampling_config.max_problems_per_repo))\n", "print(f\"Sampled {len(problems)} problems.\")\n", "has_change_probs = [p for p in problems if p.output.changed]\n", "print(f\"Sampled {len(has_change_probs)} problems with a change.\")\n", "no_change_probs = [p for p in problems if not p.output.changed]\n", "print(f\"Sampled {len(no_change_probs)} problems without a change.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.notebook_uis.edit_gen import display_edit_gen_viewer\n", "\n", "display_edit_gen_viewer(has_change_probs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import pickle\n", "from dataclasses import asdict\n", "import json\n", "\n", "save_path = f\"/mnt/efs/augment/data/eval/next_edits/{dataset_name}.pkl\"\n", "\n", "with open(save_path, \"wb\") as f:\n", "    pickle.dump(problems, f)\n", "\n", "print(f\"Dataset ({len(problems)} problems) saved to: {save_path}\")\n", "\n", "# save the config dict as well\n", "config_dict = asdict(sampling_config)\n", "with open(Path(save_path).with_name(f\"{dataset_name}_config.json\"), \"w\") as f:\n", "    json.dump(config_dict, f, indent=2)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
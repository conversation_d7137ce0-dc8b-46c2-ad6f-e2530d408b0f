{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Import and definitions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "import megatron\n", "import megatron.memorize\n", "import megatron.memorize.memorize_utils\n", "import megatron.text_generation_utils\n", "import megatron.training\n", "from megatron.tokenizer.tokenizer import CodeGenTokenizer\n", "\n", "from colorama import Fore, Style\n", "\n", "from textwrap import dedent\n", "\n", "\n", "# Adapted from gpt_neox.py\n", "class GPTNeoXModel:\n", "    \"\"\"Wrapper for the GPT-NeoX model.\"\"\"\n", "\n", "    def __init__(self):\n", "        self.model = None\n", "        self.neox_args = None\n", "\n", "    def load(self, yml_config_paths, checkpoint_path):\n", "        \"\"\"Load a model.\n", "\n", "        Args:\n", "            yml_config_paths: A list of paths to yaml config files.\n", "            checkpoint_path: Path to the checkpoint to be loaded.\n", "        \"\"\"\n", "        _overwrite_values = {\n", "            \"load\": checkpoint_path,\n", "            \"checkpoint_activations\": <PERSON><PERSON><PERSON>,\n", "            \"partition_activations\": <PERSON><PERSON><PERSON>,\n", "            \"no_load_optim\": True,\n", "            \"zero_optimization\": None,  # disable zero optimization (won't be used in inference, and loading zero optimizer can cause errors)\n", "            # \"memorize_mode\": \"host\",\n", "        }\n", "\n", "        neox_args = megatron.neox_arguments.NeoXArgs.from_ymls(\n", "            paths_to_yml_files=[Path(p) for p in yml_config_paths],\n", "            overwrite_values=_overwrite_values,\n", "        )\n", "        neox_args.configure_distributed_args()\n", "\n", "        neox_args.build_tokenizer()\n", "\n", "        if neox_args.load is None:\n", "            raise ValueError(\"`load` parameter must be supplied to load a model`\")\n", "\n", "        # initialize megatron\n", "        megatron.initialize.initialize_megatron(neox_args)\n", "\n", "        # set up model and load checkpoint.\n", "        model, _, _ = megatron.training.setup_model_and_optimizer(\n", "            neox_args=neox_args,\n", "            use_cache=True,\n", "            iteration=neox_args.iteration,\n", "        )  # we use setup_model_and_optimizer instead of get_model in order to initialize deepspeed\n", "\n", "        # put the model into evaluation model - i.e., model.training will be False\n", "        model.eval()\n", "\n", "        self.model = model\n", "        self.neox_args = neox_args\n", "\n", "        print(\"\\n##########################\")\n", "        print(\"Model loaded successfully!\")\n", "        print(\"##########################\")\n", "\n", "    def show_model(self):\n", "        \"\"\"Print out model architecture.\"\"\"\n", "        print(\"Model details:\")\n", "        print(self.model)\n", "\n", "    def unload(self):\n", "        \"\"\"Unload the model.\"\"\"\n", "        self.model = None\n", "        self.neox_args = None\n", "\n", "    def is_loaded(self):\n", "        return self.model is not None\n", "\n", "    def generate(\n", "        self,\n", "        text: str = \"\",\n", "        eos_token_id: int = None,\n", "        maximum_tokens: int = 64,\n", "        recompute: bool = False,\n", "        temperature: float = None,\n", "        top_k: int = None,\n", "        top_p: float = None,\n", "        mem_object_names: list = None,\n", "    ) -> str:\n", "        \"\"\"Generate text.\n", "\n", "        Args:\n", "            text: the prompt\n", "            eos_token_id: token ID that signifies end-of-sequence\n", "            maximum_tokens: max number of tokens to generate\n", "            temperature: sampling temperature\n", "            top_k: top k sampling\n", "            top_p: top p (nucleus) sampling\n", "\n", "        Returns:\n", "            The generated text.\n", "        \"\"\"\n", "        generated_texts = megatron.text_generation_utils.generate_samples_from_prompt(\n", "            neox_args=self.neox_args,\n", "            model=self.model,\n", "            text_tokens=[self.model.tokenizer.tokenize(text)],\n", "            eos_token_id=eos_token_id,\n", "            maximum_tokens=maximum_tokens,\n", "            recompute=recompute,\n", "            temperature=temperature if temperature is not None else 0.0,\n", "            top_k=top_k if top_k is not None else 0,\n", "            top_p=top_p if top_p is not None else 0,\n", "            mem_object_names=mem_object_names,\n", "        )\n", "\n", "        return generated_texts[0][\"text\"]\n", "\n", "    def memorize(self, text: str):\n", "        \"\"\"Add the given text to memory.\"\"\"\n", "        mem_object_name = megatron.memorize.memorize_utils.memorize_text(\n", "            neox_args=self.neox_args,\n", "            model=self.model,\n", "            text=text,\n", "        )\n", "\n", "        return mem_object_name\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Load a model and generate some text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = GPTNeoXModel()\n", "model.load(\n", "    yml_config_paths=[\"/mnt/efs/augment/configs/codegen-H/model/codegen-2B.ft.yml\"],\n", "    checkpoint_path=\"/mnt/efs/augment/checkpoints/fim/test5\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Standard generation\n", "prompt = r\"\"\"\n", "def hello_world():\n", "    '''A function that prints hello world.'''\n", "\"\"\"\n", "\n", "result = model.generate(prompt, maximum_tokens=64, temperature=0.6, top_k=40)\n", "print(f\"{prompt}{Fore.GREEN}{result}{Style.RESET_ALL}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# FIM generation\n", "FIM_SEP = \"<|fim-sep|>\"\n", "FIM_EOS = \"<|fim-eos|>\"\n", "\n", "def make_fim_prompt(prefix, suffix):\n", "    return f\"{suffix}{FIM_SEP}{prefix}\"\n", "\n", "# Pipe indicates where to do FIM (it's where the \"cursor\" is)\n", "samples = {\n", "    \"factorial\": dedent(\"\"\"\\\n", "def factorial(n):\n", "    '''Compute the factorial of n'''\n", "|    return n * factorial(n-1)\n", "\"\"\"),\n", "\n", "    \"hello_world\": \"def foo:|, World!')\",\n", "\n", "    \"sum_to_n\": dedent(\"\"\"\\\n", "def sum_to_n(n):\n", "    result = 0\n", "|    return result\n", "\"\"\"),\n", "}\n", "\n", "for sample_name, sample in samples.items():\n", "    print(f\"\\n{sample_name}\\n\" + (\"=\" * len(sample_name)))\n", "\n", "    if sample.count(\"|\") != 1:\n", "        raise ValueError(\n", "            f\"Expected 1 occurance of '|' in sample, found {sample.count('|')}\")\n", "\n", "    prefix, suffix = sample.split(\"|\")\n", "    prompt = make_fim_prompt(prefix, suffix)\n", "\n", "    print(\"> prompt:\")\n", "    print(f\"{prefix}{Fore.BLUE}|{Style.RESET_ALL}{suffix}\")\n", "\n", "    raw_result = model.generate(\n", "        text=prompt,\n", "        maximum_tokens=128,\n", "        temperature=0.,\n", "        )\n", "\n", "    print(\"> result:\")\n", "    if FIM_EOS in raw_result:\n", "        result = raw_result.split(FIM_EOS)[0]\n", "        print(f\"{prefix}{Fore.GREEN}{result}{Fore.BLUE}|{Style.RESET_ALL}{suffix}\")\n", "    else:\n", "        print(f\"WARNING: {FIM_EOS} not found, showing full result\")\n", "        print(raw_result)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "36cf16204b8548560b1c020c4e8fb5b57f0e4c58016f52f2d4be01e192833930"}}}, "nbformat": 4, "nbformat_minor": 2}
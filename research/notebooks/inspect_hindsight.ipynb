{"cells": [{"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["import logging\n", "from collections import Counter\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from colorama import Fore, Style\n", "from tqdm.auto import tqdm\n", "\n", "from research.core.utils_for_str import get_first_n_lines, get_last_n_lines\n", "from research.tools.analyze_hindsight import (\n", "    make_df,\n", "    read_data,\n", "    read_records,\n", "    read_runs,\n", "    compute_metrics,\n", "    get_datum_tags,\n", "    get_run_request_id,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Metrics"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["records_path = \"../../experimental/jeff/analysis/hindsight/records-2024-07-01.csv\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["records = read_records(records_path)\n", "data = read_data(records)\n", "runs = read_runs(records)\n", "df = make_df(runs, data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.response_model.value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.lang.value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.analyze_hindsight import (\n", "    mean_by_group,\n", "    summarize_all,\n", "    summarize_comments,\n", "    summarize_non_comments,\n", "    summarize_non_comments_non_test,\n", "    summarize_non_comments_test,\n", ")\n", "\n", "agg_df = summarize_all(df)\n", "# print(agg_df.style.hide().format(precision=3).to_string(delimiter=\"\\t\"))\n", "agg_df.style.hide().format(precision=3)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# Some examples with custom filters/aggregations\n", "group_by = [\n", "    # \"lang\",\n", "    \"response_model\",\n", "    # \"response_model_type\",\n", "    \"model\",\n", "]\n", "agg_df = df.copy()\n", "\n", "agg_df = agg_df.assign(\n", "    response_model_type=agg_df[\"response_model\"].apply(\n", "        lambda x: \"star1\"\n", "        if (first_part := x.split(\"-\")[0]) in {\"roguesl\"}\n", "        else \"star2\"\n", "        if first_part in {\"elden\", \"eldenv2\", \"eldenv3\", \"star2sl\"}\n", "        else \"other\"\n", "    )\n", ")\n", "\n", "# agg_df = agg_df.query(\"response_model in ['eldenv4-15b', 'eldenv4-3-15b', 'eldenv4-0c-15b']\")\n", "# agg_df = agg_df.query(\"model in ['eldenv6-1-15b', 'eldenv6-15b']\")\n", "# agg_df = agg_df.query(\"response_model_type == 'star2'\")\n", "# agg_df = agg_df.query(\"response_model_type != 'other'\")\n", "agg_df = agg_df.query(\"comment == 'code'\")\n", "agg_df = agg_df.query(\"test == 'non_test'\")\n", "# agg_df = agg_df.query(\"lang == 'jupyter'\")\n", "# agg_df = agg_df.query(\"lang in ['python', 'typescript']\")\n", "agg_df = mean_by_group(agg_df, group_by=group_by)\n", "# agg_df = agg_df.query(\"count > 100\")\n", "# agg_df = agg_df.reset_index().sort_values(group_by)\n", "# agg_df = agg_df.groupby(['model']).mean(numeric_only=True)\n", "# print(agg_df.style.hide().format(precision=3).to_string(delimiter=\"\\t\"))\n", "# agg_df.style.hide().format(precision=3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Inspect"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Mean generation length\n", "{\n", "    name: np.mean(\n", "        [\n", "            len(output.generation)\n", "            for output in run.outputs\n", "            if len(output.generation) != 0\n", "        ]\n", "    )\n", "    for name, run in runs.items()\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# empty or whitespace ground truths\n", "Counter(len(datum.ground_truth.strip()) != 0 for datum in data.values())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Num empty texts\n", "{\n", "    name: Counter(len(output.generation) != 0 for output in run.outputs)\n", "    for name, run in runs.items()\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# EMs where eldenv4_0c differs from ground_truth at first char\n", "Counter(\n", "    [\n", "        (\n", "            a.generation == a.ground_truth,\n", "            # b.generation == b.ground_truth,\n", "            c.generation == c.ground_truth,\n", "            # datum.completion.response.model\n", "        )\n", "        for a, b, c in zip(\n", "            runs[\"2024-08-01-v1.2/dogfood-shard; eldenv3-15b\"].outputs,\n", "            runs[\"2024-08-01-v1.2/dogfood-shard; eldenv4-15b\"].outputs,\n", "            runs[\"2024-08-01-v1.2/dogfood-shard; eldenv4-0c-15b\"].outputs,\n", "        )\n", "        if (datum := data[a.request_id])\n", "        if not c.ground_truth.startswith(c.generation[:1])\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["def print_sample(prefix, suffix, generation):\n", "    prefix_string = Fore.BLUE + prefix + Style.RESET_ALL\n", "    generation_string = Fore.YELLOW + generation + Style.RESET_ALL\n", "    suffix_string = Fore.BLUE + suffix + Style.RESET_ALL\n", "    print(prefix_string + generation_string + suffix_string)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Manually look at examples:\n", "run_a = \"2024-08-01-v1.2/dogfood-shard; eldenv3-15b\"\n", "run_b = \"2024-08-01-v1.2/dogfood-shard; eldenv4-0c-15b\"\n", "loop_list = list(zip(runs[run_a].outputs, runs[run_b].outputs))\n", "\n", "for sample_a, sample_b in loop_list[0:]:\n", "    datum = data[sample_a.request_id]\n", "    tags = get_datum_tags(datum)\n", "    if (\n", "        # NOTE: can add filters here.\n", "        # DatumTag(\"lang\", \"jupyter\") in tags\n", "        not (\n", "            datum.ground_truth[:1].isalnum()\n", "            and datum.completion.request.prefix[-1:].isalnum()\n", "        )\n", "        and (\n", "            not datum.ground_truth.startswith(sample_b.generation[:1])\n", "            or not datum.ground_truth.startswith(sample_a.generation[:1])\n", "        )\n", "        and (sample_a.generation != sample_b.generation)\n", "    ):\n", "        print(Fore.WHITE + \"REQUEST\" + Style.RESET_ALL)\n", "        print(sample_b.request_id)\n", "        print(sample_a.path)\n", "        print(datum.completion.response.model)\n", "        print_sample(\n", "            datum.completion.request.prefix[-800:],\n", "            datum.completion.request.suffix[:300],\n", "            datum.ground_truth,\n", "        )\n", "\n", "        print(Fore.GREEN + \"GROUND TRUTH\" + Style.RESET_ALL)\n", "        print(datum.ground_truth)\n", "        print(Fore.RED + \"GENERATION\" + Style.RESET_ALL)\n", "        print(\n", "            Fore.YELLOW\n", "            + f\"model: {run_a}\\nrequest: {get_run_request_id(sample_a)}\"\n", "            + Style.RESET_ALL\n", "        )\n", "        print(sample_a.generation)\n", "        print(\n", "            Fore.YELLOW\n", "            + f\"model: {run_b}\\nrequest: {get_run_request_id(sample_b)}\"\n", "            + Style.RESET_ALL\n", "        )\n", "        print(sample_b.generation)\n", "        print(\"=\" * 80)\n", "        # print(\"\\n\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Export CSV table"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["def make_hyperlink(request_id: str, base_url: str) -> str:\n", "    return f'=HYPERLINK(\"{base_url}{request_id}\", \"{request_id}\")'\n", "\n", "\n", "url_map = {\n", "    \"dogfood\": \"https://support.dogfood.t.us-central1.prod.augmentcode.com/t/dogfood/request/\",\n", "    \"dogfood-shard\": \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/requests/\",\n", "}\n", "\n", "\n", "# For importing into excel, add a single quote to escape the rest of text\n", "def escape_excel_str(s: str) -> str:\n", "    return \"'\" + s\n", "\n", "\n", "runs_by_ids = {\n", "    run_name: (run.tags, {output.request_id: output for output in run.outputs})\n", "    for run_name, run in runs.items()\n", "}\n", "request_ids = sorted(data)\n", "rows = []\n", "for request_id in request_ids[:1000]:\n", "    datum = data[request_id]\n", "    tags = get_datum_tags(datum)\n", "    # NOTE: can filter by tags\n", "    # if DatumTag(\"comment\", \"code\") not in tags:\n", "    #     continue\n", "    row = {}\n", "    for tag in tags:\n", "        row[tag.category] = tag.value\n", "    row[\"context\"] = escape_excel_str(\n", "        get_last_n_lines(datum.completion.request.prefix, n=10)\n", "        + datum.ground_truth\n", "        + get_first_n_lines(datum.completion.request.suffix, n=4)\n", "    )\n", "    row[\"ground_truth\"] = escape_excel_str(datum.ground_truth)\n", "    # TODO(jeff): No way right now to get the tenant from the request_id\n", "    # So needs to be manually set.\n", "    row[\"request_id\"] = make_hyperlink(request_id, url_map[\"dogfood-shard\"])\n", "    row[\"response_model; generation\"] = escape_excel_str(datum.completion.response.text)\n", "    row[\"response_model; em\"] = float(\n", "        datum.completion.response.text == datum.ground_truth\n", "    )\n", "    for run_name, (run_tags, outputs_by_id) in runs_by_ids.items():\n", "        if request_id not in outputs_by_id:\n", "            continue\n", "        output = outputs_by_id[request_id]\n", "        model = [tag.value for tag in run_tags if tag.category == \"model\"][0]\n", "        tenant = [tag.value for tag in run_tags if tag.category == \"tenant\"][0]\n", "        # NOTE: filter by model\n", "        if model not in [\n", "            # \"roguesl-v2-16b-seth616-rec\",\n", "            # \"roguesl-v3-16b-seth616-rec\",\n", "            # \"star2sl-16b-seth616-rec\",\n", "            \"eldenv3-15b\",\n", "            \"eldenv4-15b\",\n", "            \"eldenv4-0c-15b\",\n", "        ]:\n", "            continue\n", "        run_request_id = get_run_request_id(output)\n", "\n", "        row[model + \"; request_id\"] = make_hyperlink(run_request_id, url_map[tenant])\n", "        row[model + \"; generation\"] = escape_excel_str(output.generation)\n", "        metrics = compute_metrics(output)\n", "        row[model + \"; em\"] = metrics[\"exact_match\"]\n", "    rows.append(row)\n", "\n", "df = pd.DataFrame(rows)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.to_csv(\"~/hindsight_runs.csv\", index=False)\n", "# Run on local machine:\n", "# scp augment@<hostname>:~/hindsight_runs.csv ~/Downloads\n", "# Then you can import into Excel/Google sheets"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
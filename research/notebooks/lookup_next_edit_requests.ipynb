{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["request_id = \"f137b169-f252-42e2-bf23-e76a04fce7da\"\n", "log_root = \"/mnt/efs/augment/user/jiayi/model_server_logs\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import glob\n", "from pathlib import Path\n", "\n", "matched = list(glob.glob(f\"{log_root}/session-*/*/{request_id}\"))\n", "\n", "if len(matched) != 1:\n", "    print(f\"{len(matched)} Matching logs:\")\n", "    for path in matched:\n", "        print(path)\n", "else:\n", "    log_dir = Path(matched[0])\n", "    print(f\"Log files under {log_dir}:\")\n", "    for path in log_dir.iterdir():\n", "        print(f\"    {path.name}:\", path)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
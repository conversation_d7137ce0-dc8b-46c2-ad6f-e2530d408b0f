{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## An example of using the signature index"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "%load_ext snakeviz\n", "%load_ext line_profiler"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.data_utils import AUGMENT_ROOT\n", "\n", "repo_root = AUGMENT_ROOT\n", "langs = (\"python\", \"java\", \"javascript\", \"typescript\", \"go\", \"rust\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "from base.static_analysis.parsing import ParsingFailedError, ScopeTreeParser\n", "from base.static_analysis.signature_index import (\n", "    FileSummaryWithSignatures,\n", "    <PERSON>rse<PERSON><PERSON><PERSON>,\n", "    SignatureIndex,\n", "    SignaturePrinter,\n", ")\n", "from research.utils.data_utils import read_src_repo\n", "from tqdm import tqdm\n", "\n", "index = SignatureIndex(usage_distance_metric=\"var_occurrence\")\n", "repo = read_src_repo(langs, repo_root=repo_root)\n", "sig_printer = SignaturePrinter(show_full_method_signatures=True)\n", "summaries = dict[Path, FileSummaryWithSignatures]()\n", "src_map = dict[Path, str]()\n", "n_failed_to_summarize = 0\n", "robust_parser = ScopeTreeParser(parse_errored_root=True)\n", "\n", "t_start = time.time()\n", "for file in tqdm(repo.files, desc=\"Summarizing and indexing\", smoothing=0):\n", "    src_map[file.path] = file.code\n", "    try:\n", "        pfile = ParsedFile.parse(file.path, file.lang, file.code, robust_parser)\n", "        summary = FileSummaryWithSignatures.from_pfile(pfile, sig_printer)\n", "    except ParsingFailedError:\n", "        n_failed_to_summarize += 1\n", "        continue\n", "    summaries[summary.path] = summary\n", "    index.update_file(summary)\n", "total_time = time.time() - t_start\n", "print(\"Failed to summarize:\", n_failed_to_summarize)\n", "print(f\"Summarizing and indexing took: {total_time:.1f}s\")\n", "print(f\"({total_time/len(summaries)*1000:.1f}ms per file)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.ranges.range_types import CharRange\n", "\n", "\n", "def run_get_context_signatures():\n", "    \"\"\"Run get_context_signatures on all files.\"\"\"\n", "    index.usage_distance_metric = \"var_occurrence\"\n", "    t_start = time.time()\n", "    for s in tqdm(summaries.values(), smoothing=0, desc=\"get_context_signatures\"):\n", "        s = s.summary\n", "        cursor_loc = s.size_chars // 2\n", "        prompt_range = CharRange(cursor_loc - 500, cursor_loc + 1000)\n", "        index.get_context_signatures(s, cursor_loc, prompt_range)\n", "    total_time = time.time() - t_start\n", "    print(f\"get_context_signatures took: {total_time:.1f}s\")\n", "    print(f\"({total_time/len(summaries)*1000:.1f}ms per file)\")\n", "\n", "\n", "# %snakeviz -t run_get_context_signatures()\n", "run_get_context_signatures()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.ranges import LineMap\n", "from base.ranges.range_types import CharRange\n", "from pathlib import Path\n", "\n", "index.usage_distance_metric = \"var_occurrence\"\n", "index.top_k_sigs = 2\n", "\n", "ex_file = Path(\"[file name]\")\n", "lmap = LineMap(src_map[ex_file])\n", "ex_summary = summaries[ex_file]\n", "cursor_loc = lmap.get_char_index(894 - 1, 10 - 1)\n", "# cursor_loc = len(src_map[ex_file])\n", "result = index.get_context_signatures(\n", "    ex_summary.summary, cursor_loc, prompt_range=CharRange.point(cursor_loc)\n", ")\n", "for name, uses in result.ctx_signatures.items():\n", "    lrange = lmap.crange_to_lrange(name.use_site)\n", "    print(\"-\" * 100)\n", "    print(f\"At line {lrange.start + 1}, {name}:\")\n", "    for sig in uses:\n", "        print(f\"{sig.text}\\n\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
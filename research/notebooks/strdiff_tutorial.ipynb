{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Building a StrDiff"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.str_diff import build_str_diff, print_diff\n", "\n", "old_text = \"\"\"\\\n", "def guess_lang_from_fp(fname: str | Path) -> LanguageID:\n", "    extension = Path(fname).suffix.lower()\n", "    return file_ext_to_lang_id.get(extension)\n", "\n", "a_long_var = 1\n", "\"\"\"\n", "\n", "new_text = \"\"\"\\\n", "def guess_lang_from_fp(fname: str | Path | None) -> LanguageID | None:\n", "    if fname is None:\n", "        return None\n", "    extension = Path(fname).suffix\n", "    return file_ext_to_lang_id.get(extension)\n", "\n", "a_long_var = 2\n", "\"\"\"\n", "\n", "diff = build_str_diff(old_text, new_text)\n", "print(diff)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the above you can see that a `StrDiff` object stores a sequence of `DiffSpan` objects that records the changes made to each non-overlapping areas of the text.\n", "\n", "Alternatively, we can visualize this as a color diff using the `print_diff()` function, which shows addition in blue, deletion in red, and replacements in cyan."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print_diff(diff)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`StrDiff` is a general format that can represent both character-level and line-level diffs. For example, we can also build a line-diff from the same two strings by passing `algorithm=\"precise_linediff\"` to `build_str_diff`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print_diff(build_str_diff(old_text, new_text, algorithm=\"precise_linediff\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that each of these diff algorithms can also be imported and used directly."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.diff_utils.str_diff import precise_char_diff\n", "\n", "\n", "print_diff(precise_char_diff(old_text, new_text))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Constructing original texts\n", "\n", "We can also reconstruct the original texts from the diff using the `get_before` and `get_after` methods."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diff.get_before() == old_text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diff.get_after() == new_text"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inverting a diff"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `.inverted` property returns a new diff that undoes the effects of the original diff."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print_diff(diff.inverted)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can verify that the inverted diff is equal to the diff directly built from `text2` to `text1`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["precise_char_diff(old_text, new_text).inverted == precise_char_diff(new_text, old_text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Map character ranges\n", "We can use `StrDiff` to map a character range in the before string into a corresponding range in the and after string, and vice versa. Note that a precise location fully contained inside a `NoopSpan` is always mapped to another precise location, but precise locations at the boundaries may be mapped to a larger range due to the inherent diff ambiguities.\n", "\n", "Let's use the same diff example as before."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print_diff(precise_char_diff(old_text, new_text))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.ranges.range_types import CharRange\n", "\n", "\n", "def get_range(key: str, text: str):\n", "    \"\"\"Return `key`'s character range in `text`.\"\"\"\n", "    if key not in text:\n", "        raise ValueError(f\"Key {repr(key)} not found in {repr(text)}\")\n", "    start = text.index(key)\n", "    stop = start + len(key)\n", "    return <PERSON><PERSON><PERSON><PERSON><PERSON>(start, stop)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The line `extension = Path(fname).suffix.lower()` should be mapped to `extension = Path(fname).suffix`, and there is no ambiguity in this case."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["old_range = get_range(\"extension = Path(fname).suffix.lower()\", old_text)\n", "\n", "diff = precise_char_diff(old_text, new_text)\n", "mapped_range = diff.before_range_to_after(old_range)\n", "print(new_text[mapped_range.to_slice()])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["However, the span `-> LangaugeID` is mapped to a larger span because it touches a `ModSpan`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["old_range = get_range(\"-> LanguageID\", old_text)\n", "\n", "diff = precise_char_diff(old_text, new_text)\n", "mapped_range = diff.before_range_to_after(old_range)\n", "print(new_text[mapped_range.to_slice()])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.next_edits.edit_gen_formatters import (\n", "    encode_model_diff_input,\n", "    encode_model_diff_output,\n", ")\n", "\n", "input_str = encode_model_diff_input(old_text)\n", "output_spans = encode_model_diff_output(\n", "    old_text, new_text, max_pause_chars=900, lang=\"Python\"\n", ")\n", "print(\"~=\" * 30, \"Input\", \"~=\" * 30)\n", "print(input_str)\n", "print(\"~=\" * 30, \"Output\", \"~=\" * 30)\n", "for i, span in enumerate(output_spans):\n", "    if i > 0:\n", "        print(\"-\" * 50, \"(Pause)\", \"-\" * 50)\n", "    print(span)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
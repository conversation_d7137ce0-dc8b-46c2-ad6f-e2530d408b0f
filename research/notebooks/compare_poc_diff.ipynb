{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from random import Random\n", "from typing import Iterable\n", "\n", "from tqdm.auto import tqdm\n", "\n", "from research.next_edits.edit_gen_sampler import EditGenProblem, EditGenSampler\n", "from research.utils.repo_change_utils import get_commit_history, iterate_repo_history\n", "\n", "\n", "def sample_from_repo_history(\n", "    repo_path: Path,\n", "    commits: int,\n", "    max_workers: int = 8,\n", ") -> Iterable[EditGenProblem]:\n", "    \"\"\"Sample from the last `commits` commits from the repo history.\"\"\"\n", "\n", "    sampler = EditGenSampler(random_edit_region_rate=0.0, wip_sampling_strategy=\"keep_most\")\n", "    rng = Random(42)\n", "\n", "    commit_history = get_commit_history(repo_path, commits)\n", "    repo_changes = iterate_repo_history(\n", "        repo_path, commit_history, max_workers=max_workers, silent=True\n", "    )\n", "    for repo_change, commit in tqdm(\n", "        zip(repo_changes, commit_history[1:]),\n", "        total=len(commit_history) - 1,\n", "        desc=\"sample_problems\",\n", "        unit=\"commit\",\n", "        disable    = True,\n", "    ):\n", "        for problem in sampler.sample_problems(rng, repo_change, commit):\n", "            yield problem"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.constants import AUGMENT_ROOT\n", "\n", "CHANGED_FILES = 1\n", "\n", "problems_with_changed_file = []\n", "for problem in sample_from_repo_history(AUGMENT_ROOT, commits=100):\n", "    if len(problem.repo_change.changed_files) > 0:\n", "        problems_with_changed_file.append(problem)\n", "\n", "\n", "if len(problems_with_changed_file) == 0:\n", "    raise ValueError(\"No problem with changed files found.\")\n", "# print(f\"Sampled {len(problems)} problems.\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def transform_line(line: str) -> str | None:\n", "            if line.startswith(\"diff --git \") or line.startswith(\"index \") or line.startswith(\"new file mode\"):\n", "                return None\n", "            # below assumes that the diff is generated by git diff\n", "            if line.startswith(\"--- a/\"):\n", "                return \"--- \" + line[6:]\n", "            if line.startswith(\"+++ b/\"):\n", "                return \"+++ \" + line[6:]\n", "            return line\n", "\n", "def clean_repo_diff_output(diff_str: str) -> str:\n", "    diff_lines = diff_str.splitlines(keepends=True)\n", "    diff_lines = [\n", "            transformed_line\n", "            for line in diff_lines\n", "            if (transformed_line := transform_line(line)) is not None\n", "    ]\n", "    return \"\".join(diff_lines)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import difflib\n", "\n", "from IPython.display import HTML, display\n", "\n", "\n", "def get_diff_html(left: str, right: str, fromdesc=\"\", todesc=\"\") -> str:\n", "    \"\"\"Returns HTML to compare `left` and `right`.\"\"\"\n", "    diff_obj = difflib.HtmlDiff(tabsize=2)\n", "    diff_obj._legend = \"\" # type: ignore\n", "    display(\n", "        HTML(\n", "            f\"\"\"\n", "        <style type=\"text/css\">\n", "            {difflib.HtmlDiff()._styles}\n", "            td {{ text-align: left; }}\n", "            :not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td {{ text-align: left; }}\n", "        </style>\n", "        \"\"\"\n", "        )\n", "    )\n", "\n", "    return diff_obj.make_table(\n", "        left.splitlines(),\n", "        right.splitlines(),\n", "        fromdesc=fromdesc,\n", "        todesc=todesc,\n", "    )\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from typing import Sequence\n", "\n", "# from IPython.display import HTML, display\n", "from research.core.changes import (\n", "    Changed,\n", "    get_after,\n", "    get_before,\n", ")\n", "from research.core.diff_utils import File, Repository, compute_repo_diff_str, diff_files\n", "from research.utils.repo_change_utils import FileTuple\n", "\n", "if len(problems_with_changed_file) == 0:\n", "    raise ValueError(\"No problem with changed files found.\")\n", "\n", "\n", "def get_after_file(change: Changed[FileTuple]) -> File | None:\n", "    if after_file := get_after(change):\n", "        return File(str(after_file.path), after_file.code)\n", "    return None\n", "\n", "def get_before_file(change: Changed[FileTuple]) -> File | None:\n", "    if before_file := get_before(change):\n", "        return File(str(before_file.path), before_file.code)\n", "    return None\n", "\n", "def before_after_iter(changed_files: Sequence[Changed[FileTuple]]) -> Iterable[tuple[File | None, File | None]]:\n", "    for change in changed_files:\n", "        yield get_before_file(change), get_after_file(change)\n", "\n", "def get_diff_from_repo(before_file: File | None, after_file: File | None) -> str:\n", "    repo_before = Repository([before_file]) if before_file else Repository([])\n", "    repo_after = Repository([after_file]) if after_file else Repository([])\n", "    diff_str = compute_repo_diff_str(repo_before=repo_before, repo_after=repo_after, num_context_lines=3)\n", "    diff_str = clean_repo_diff_output(diff_str)\n", "    return diff_str\n", "\n", "exact_match_counter = 0\n", "different_match_counter = 0\n", "differences = []\n", "repo_diff_time = 0\n", "difflib_diff_time = 0\n", "for problem in problems_with_changed_file:\n", "\n", "    changed_file = problem.repo_change.changed_files[0]\n", "\n", "\n", "    for (before_file, after_file) in before_after_iter(problem.repo_change.changed_files):\n", "        mark1 = time.time()\n", "        repo_diff_str = get_diff_from_repo(before_file, after_file)\n", "        mark2 = time.time()\n", "        difflib_diff_str = diff_files(before_file, after_file)\n", "        mark3 = time.time()\n", "        repo_diff_time += mark2 - mark1\n", "        difflib_diff_time += mark3 - mark2\n", "        if repo_diff_str == difflib_diff_str:\n", "            exact_match_counter += 1\n", "        else:\n", "            different_match_counter += 1\n", "            differences.append((repo_diff_str, difflib_diff_str))\n", "\n", "print(f\"Difflib time: {difflib_diff_time}, repo diff time: {repo_diff_time}\")\n", "print(f\"Exact match: {exact_match_counter}, different match: {different_match_counter}\")\n", "for diff in differences[5:10]:\n", "    display(HTML(get_diff_html(*diff)))\n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
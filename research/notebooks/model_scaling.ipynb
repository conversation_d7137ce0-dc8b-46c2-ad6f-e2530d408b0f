{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Comparing linear and quadratic FLOPs\n", "\n", "Non-embedding parameters:\n", "\n", "d_ff = 4 * d_model\n", "N = 2 * n_layer * d_model * (2 * d_model + d_ff)\n", "  = 12 * n_layer * d_model^2\n", "\n", "Linear, quadratic parts of the forward pass FLOPs per token:\n", "(for quadratic there is a 2* discrepancy from the OpenAI paper)\n", "C_lin = 2 * N = 24 * n_layer * d_model^2\n", "C_quad = 4 * n_ctx * d_model * n_layer\n", "\n", "Fraction of quadratic to total:\n", "\n", "C_quad / C_lin\n", "= 4 * n_ctx * d_model * n_layer / 24 * n_layer * d_model^2\n", "=  n_ctx / (6 * d_model)\n", "\n", "The quadratic piece becomes significant (ratio is ~ 1) when:\n", "\n", "n_ctx > 6 * d_model\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import yaml\n", "from pathlib import Path\n", "import pandas as pd\n", "import os\n", "\n", "potential_config_roots = [\n", "    Path(\"/mnt/efs/augment/configs/codegen-H\"),\n", "    Path(os.environ[\"HOME\"], \"configs/codegen-H\"),\n", "]\n", "\n", "for configs_root in potential_config_roots:\n", "    if configs_root.is_dir():\n", "        break\n", "\n", "if not configs_root.is_dir():\n", "    raise ValueError(\"Cannot find configs root\")\n", "\n", "print(\"Using config root:\", configs_root)\n", "\n", "model_config_files = [\n", "    \"model/codegen-350M.yml\",\n", "    \"model/codegen-2B.yml\",\n", "    \"model/codegen-6B.yml\",\n", "    \"model/codegen-16B.yml\",\n", "]\n", "\n", "config_dicts = []\n", "\n", "\n", "def compute_model_params(model_config, include_embed_params=False):\n", "    mc = model_config\n", "\n", "    n_params_non_embed = 2 * mc[\"d_model\"] * mc[\"n_layer\"] * (2 * mc[\"d_model\"] + mc[\"d_ff\"])\n", "    if \"embed_weight_tying\" in mc and mc[\"embed_weight_tying\"]:\n", "        n_params_embed = mc[\"d_embed_guess\"] * mc[\"d_model\"]\n", "    else:\n", "        n_params_embed = 2 * mc[\"d_embed\"] * mc[\"d_model\"]\n", "\n", "    if include_embed_params:\n", "        return n_params_embed + n_params_non_embed\n", "    else:\n", "        return n_params_non_embed\n", "\n", "\n", "def compute_forward_pass_flops_detailed(\n", "        model_config, n_ctx=None, include_embed_params=False):\n", "    mc = model_config\n", "    n_params = compute_model_params(mc, include_embed_params)\n", "    if n_ctx is None:\n", "        n_ctx = mc[\"n_ctx\"]\n", "    linear_flops = 2 * n_params\n", "    # OpenAI paper says 2*, but accounting for Q.K^T and for Attn.V seems\n", "    # to give 4!\n", "    quadratic_flops = 4 * mc[\"n_layer\"] * n_ctx * mc[\"d_model\"]\n", "    return linear_flops, quadratic_flops\n", "\n", "\n", "def compute_forward_pass_flops(\n", "        model_config, n_ctx=None, include_embed_params=False):\n", "    linear_flops, quadratic_flops = compute_forward_pass_flops_detailed(\n", "        model_config, n_ctx, include_embed_params)\n", "    return linear_flops + quadratic_flops\n", "\n", "\n", "for config_filename in model_config_files:\n", "    with open(Path(configs_root, config_filename), \"r\") as file:\n", "        data = yaml.safe_load(file)\n", "        n_layer = data[\"num-layers\"]\n", "        d_model = data[\"hidden-size\"]\n", "        d_ff = 4 * d_model\n", "        n_heads = data[\"num-attention-heads\"]\n", "        d_embed_guess = data[\"make_vocab_size_divisible_by\"]\n", "        embed_weight_tying = not data[\"no_weight_tying\"]\n", "        default_n_context = data[\"seq-length\"]\n", "\n", "        model_name = config_filename.split(\"-\")[1].split(\".\")[0]\n", "\n", "        model_config = {\n", "            \"model_family\": \"codegen\",\n", "            \"model_name\": model_name,\n", "            \"n_layer\": n_layer,\n", "            \"d_model\": d_model,\n", "            \"d_ff\": d_ff,\n", "            \"n_heads\": n_heads,\n", "            \"d_embed\": d_embed_guess,\n", "            \"embed_weight_tying\": embed_weight_tying,\n", "            \"n_ctx\": default_n_context,\n", "        }\n", "\n", "        model_config[\"n_params_non_embed\"] = compute_model_params(\n", "            model_config, include_embed_params=False)\n", "        model_config[\"n_params_total\"] = compute_model_params(\n", "            model_config, include_embed_params=True)\n", "\n", "        # n = n_params_non_embed = 2 * d_model * n_layer * (2 * d_model + d_ff)\n", "        # if embed_weight_tying:\n", "        #     n_params_embed = d_embed_guess * d_model\n", "        # else:\n", "        #     n_params_embed = 2 * d_embed_guess * d_model\n", "        # n_params_total = n_params_embed + n_params_non_embed\n", "\n", "        # n_ctx = default_n_context\n", "        # c_forward = 2 * n + 2 * n_layer * n_ctx * d_model\n", "\n", "        config_dicts.append(model_config)\n", "\n", "codegen_df = pd.DataFrame(config_dicts)\n", "codegen_df[\"d_head\"] = codegen_df[\"d_model\"] / codegen_df[\"n_heads\"]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# From the GPT-3 paper\n", "gpt_configs = [\n", "    {\n", "        \"model_name\": \"Small\",\n", "        \"n_params_non_embed\": \"125M\",\n", "        \"n_layer\": 12,\n", "        \"d_model\": 768,\n", "        \"n_heads\": 12,\n", "        \"d_head\": 64,\n", "        \"batch_size\": \"0.5M\",\n", "        \"learning_rate\": 6.0e-4,\n", "    },\n", "    {\n", "        \"model_name\": \"Medium\",\n", "        \"n_params_non_embed\": \"350M\",\n", "        \"n_layer\": 24,\n", "        \"d_model\": 1024,\n", "        \"n_heads\": 16,\n", "        \"d_head\": 64,\n", "        \"batch_size\": \"0.5M\",\n", "        \"learning_rate\": 3.0e-4,\n", "    },\n", "    {\n", "        \"model_name\": \"Large\",\n", "        \"n_params_non_embed\": \"760M\",\n", "        \"n_layer\": 24,\n", "        \"d_model\": 1536,\n", "        \"n_heads\": 16,\n", "        \"d_head\": 96,\n", "        \"batch_size\": \"0.5M\",\n", "        \"learning_rate\": 2.5e-4,\n", "    },\n", "    {\n", "        \"model_name\": \"XL\",\n", "        \"n_params_non_embed\": \"1.3B\",\n", "        \"n_layer\": 24,\n", "        \"d_model\": 2048,\n", "        \"n_heads\": 24,\n", "        \"d_head\": 128,\n", "        \"batch_size\": \"1M\",\n", "        \"learning_rate\": 2.0e-4,\n", "    },\n", "    {\n", "        \"model_name\": \"2.7B\",\n", "        \"n_params_non_embed\": \"2.7B\",\n", "        \"n_layer\": 32,\n", "        \"d_model\": 2560,\n", "        \"n_heads\": 32,\n", "        \"d_head\": 80,\n", "        \"batch_size\": \"1M\",\n", "        \"learning_rate\": 1.6e-4,\n", "    },\n", "    {\n", "        \"model_name\": \"6.7B\",\n", "        \"n_params_non_embed\": \"6.7B\",\n", "        \"n_layer\": 32,\n", "        \"d_model\": 4096,\n", "        \"n_heads\": 32,\n", "        \"d_head\": 128,\n", "        \"batch_size\": \"2M\",\n", "        \"learning_rate\": 1.2e-4,\n", "    },\n", "    {\n", "        \"model_name\": \"13B\",\n", "        \"n_params_non_embed\": \"13.0B\",\n", "        \"n_layer\": 40,\n", "        \"d_model\": 5140,\n", "        \"n_heads\": 40,\n", "        \"d_head\": 128,\n", "        \"batch_size\": \"2M\",\n", "        \"learning_rate\": 1.0e-4,\n", "    },\n", "    {\n", "        \"model_name\": \"175B\",\n", "        \"n_params_non_embed\": \"175.0B\",\n", "        \"n_layer\": 96,\n", "        \"d_model\": 12288,\n", "        \"n_heads\": 96,\n", "        \"d_head\": 128,\n", "        \"batch_size\": \"3.2M\",\n", "        \"learning_rate\": 0.6e-4,\n", "    },\n", "] \n", "\n", "def str_to_num(s):\n", "    \"\"\"Convert '175B' -> 175e9 etc.\"\"\"\n", "    if s.endswith('B'):\n", "        base = float(s[:-1])\n", "        mult = 1e9\n", "    elif s.endswith('M'):\n", "        base = float(s[:-1])\n", "        mult = 1e6\n", "    else:\n", "        base = float(s)\n", "        mult = 1\n", "    return base * mult\n", "\n", "for model in gpt_configs:\n", "    model[\"model_family\"] = \"gpt\"\n", "    model[\"n_params_non_embed\"] = str_to_num(model[\"n_params_non_embed\"])\n", "    model[\"batch_size\"] = str_to_num(model[\"batch_size\"])\n", "\n", "gpt_df = pd.DataFrame(gpt_configs)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Combine model families and normalize\n", "df = pd.concat([codegen_df, gpt_df])\n", "df[\"d_ff\"] = 4 * df[\"d_model\"]\n", "df[\"aspect_ratio\"] = df[\"d_model\"] / df[\"n_layer\"]\n", "df[\"ff_ratio\"] = df[\"d_ff\"] / df[\"d_model\"]\n", "\n", "# Populate FLOPs if needed\n", "# df[\"forward_flops_per_token_linear\"] = 2 * df[\"n_params_total\"]\n", "# df[\"forward_flops_per_token_quadratic\"] = 4 * df[\"n_ctx\"] * df[\"d_model\"] * df[\"n_layer\"]\n", "# df[\"forward_flops_per_token\"] = (\n", "#     df[\"forward_flops_per_token_linear\"] + \n", "#     df[\"forward_flops_per_token_quadratic\"] \n", "# )\n", "# df[\"flops_quadratic_fraction\"] = (\n", "#     df[\"forward_flops_per_token_quadratic\"] / df[\"forward_flops_per_token\"])\n", "# df[\"flops_per_token_per_step\"] = 3 * df[\"forward_flops_per_token\"]\n", "\n", "codegen_df = df[df[\"model_family\"] == \"codegen\"]\n", "gpt_df = df[df[\"model_family\"] == \"gpt\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "sns.set_style(\"whitegrid\")\n", "\n", "fig, axes = plt.subplots(ncols=3, nrows=2, figsize=(12, 8))\n", "\n", "ax = axes[0][0]\n", "ax.plot(gpt_df[\"n_params_non_embed\"], gpt_df[\"aspect_ratio\"], \".-\", label=\"gpt\")\n", "ax.plot(codegen_df[\"n_params_non_embed\"], codegen_df[\"aspect_ratio\"], \".-\", label=\"codegen\")\n", "ax.set_xscale(\"log\")\n", "ax.legend()\n", "ax.set_title(\"aspect_ratio\")\n", "ax.set_xlabel(\"non-embed. params\")\n", "\n", "ax = axes[0][1]\n", "ax.plot(gpt_df[\"n_params_non_embed\"], gpt_df[\"d_head\"], \".-\", label=\"gpt\")\n", "ax.plot(codegen_df[\"n_params_non_embed\"], codegen_df[\"d_head\"], \".-\", label=\"codegen\")\n", "ax.set_xscale(\"log\")\n", "ax.legend()\n", "ax.set_title(\"d_head\")\n", "ax.set_xlabel(\"non-embed. params\")\n", "\n", "ax = axes[0][2]\n", "ax.plot(gpt_df[\"n_params_non_embed\"], gpt_df[\"n_layer\"], \".-\", label=\"gpt\")\n", "ax.plot(codegen_df[\"n_params_non_embed\"], codegen_df[\"n_layer\"], \".-\", label=\"codegen\")\n", "ax.set_xscale(\"log\")\n", "ax.legend()\n", "ax.set_title(\"n_layer\")\n", "ax.set_xlabel(\"non-embed. params\")\n", "\n", "ax = axes[1][0]\n", "ax.plot(gpt_df[\"n_params_non_embed\"], gpt_df[\"d_model\"], \".-\", label=\"gpt\")\n", "ax.plot(codegen_df[\"n_params_non_embed\"], codegen_df[\"d_model\"], \".-\", label=\"codegen\")\n", "ax.set_xscale(\"log\")\n", "ax.legend()\n", "ax.set_title(\"d_model\")\n", "ax.set_xlabel(\"non-embed. params\")\n", "\n", "ax = axes[1][1]\n", "ax.plot(gpt_df[\"n_params_non_embed\"], gpt_df[\"n_heads\"], \".-\", label=\"gpt\")\n", "ax.plot(codegen_df[\"n_params_non_embed\"], codegen_df[\"n_heads\"], \".-\", label=\"codegen\")\n", "ax.set_xscale(\"log\")\n", "ax.legend()\n", "ax.set_title(\"n_heads\")\n", "ax.set_xlabel(\"non-embed. params\")\n", "\n", "fig.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["codegen_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["n_ctx = 2048\n", "mask_memory = n_ctx**2 * 32 * 32 * 2\n", "print(mask_memory)\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## KV cache"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kv_cache_size = 2 * 2 * n_ctx * n_layers * d_head * n_heads\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "36cf16204b8548560b1c020c4e8fb5b57f0e4c58016f52f2d4be01e192833930"}}}, "nbformat": 4, "nbformat_minor": 2}
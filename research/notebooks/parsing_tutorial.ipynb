{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Parsing Tutorial\n", "\n", "This notebook shows how to use the parsing API to parse Python code into a\n", "scope tree. The same API works for other programming languages as well."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "\n", "from research.static_analysis.parsing import ScopeOrSpan, GlobalParser, SrcScope, SrcSpan"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Parsing a scope tree\n", "\n", "Let's use some actual code from our codebase as a running example.\n", "We call `GlobalParser.parse` to parse a source file into a scope tree, in which each node is either a `SrcScope` or a `SrcSpan`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EXAMPLE = '''\n", "from os import getenv\n", "import sys, os\n", "from .common import *\n", "import tree_sitter as ts\n", "\n", "ScopeKind = Literal[\"file\", \"class\", \"function\"]\n", "\n", "SrcSpan = ... # omitted for this tutorial\n", "\n", "@dataclass\n", "class SrcScope:\n", "    \"\"\"A logical scope of source code.\"\"\"\n", "\n", "    name: str\n", "    kind: <PERSON><PERSON><PERSON><PERSON>\n", "    children: \"Sequence[ScopeOrSpan]\"\n", "    prefix: SrcSpan\n", "    suffix: SrcSpan\n", "\n", "    @property\n", "    def range(self) -> ByteRange:\n", "        return ByteRange(self.prefix.range.start, self.suffix.range.stop)\n", "        \n", "\n", "    def pprint(self, file=sys.stdout, indent: int = 4):\n", "        \"\"\"Pretty print the scope tree to the given file.\"\"\"\n", "        tab = \" \" * indent\n", "\n", "        def rec(s: Src<PERSON><PERSON>, level: int):\n", "            print(\n", "                tab * level,\n", "                f\"{s.kind}(name={repr(s.name)}, prefix={repr(s.prefix.code)}, suffix={repr(s.suffix.code)})\",\n", "                file=file,\n", "            )\n", "            for child in s.children:\n", "                if isinstance(child, SrcScope):\n", "                    rec(child, level + 1)\n", "                else:\n", "                    span = f\"span({repr(shorten_str(child.code))})\"\n", "                    print(tab * (level + 1), span, file=file)\n", "\n", "        rec(self, 0)\n", "\n", "'''\n", "\n", "\n", "root_scope = GlobalParser.parse(EXAMPLE, \"static_analysis/parsing.py\", \"python\")\n", "root_scope.pprint()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Printing\n", "\n", "We can perform a simple DFS traversal over the scope tree to print out the encountered nodes. This is guaranteed to give back the original file content.\n", "\n", "There's also a method called `get_code()` defined on `SrcScope` that does the same thing."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def print_code(scope: ScopeOrSpan, as_stub: bool = False):\n", "    \"\"\"Print out its source code using a DFS traversal.\n", "\n", "    This is equivalent to print out `SrcScope.dfs_code()` when `as_stub` is False.\n", "    \"\"\"\n", "    if isinstance(scope, SrcScope):\n", "        # We omit the docstr if print as stub\n", "        prefix_doc = scope.prefix if as_stub else scope.prefix_with_doc()\n", "        print(prefix_doc.code, end=\"\")\n", "        if as_stub and scope.kind == \"function\":\n", "            print(\"...\")\n", "        else:\n", "            for child in scope.children:\n", "                print_code(child, as_stub)\n", "        print(scope.suffix.code, end=\"\")\n", "    else:\n", "        print(scope.code, end=\"\")\n", "\n", "\n", "# This should be equal to the original file content\n", "print_code(root_scope)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We can also omit the function bodies and docstrs to obtain a stub file."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print_code(root_scope, as_stub=True)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Traversing and Merging\n", "\n", "We also have utils for traversing and merging scope trees.\n", "The `SrcScope.get_all_spans` method traverses all spans and returns each span as an \n", "individual scope tree containing only that span and its ancestors."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_spans = list(root_scope.get_all_spans())\n", "\n", "for s in all_spans:\n", "    s.pprint()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(all_spans[-1].get_code())"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We can merge multiple scope trees together as long as they share the same root."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SrcScope.merge_all(*all_spans).pprint()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We can also merge a subset of the spans. This can be handy when presenting multiple retrieved spans to the model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(SrcScope.merge_all(*all_spans[0:None:2]).get_code())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting tree-sitter nodes\n", "The underlying tree-sitter parse tree can be parsed using a helper method defined on `ScopeTreeParser`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from base.static_analysis.parsing import show_ts_node\n", "\n", "\n", "ts_tree = GlobalParser.parse_ts_tree(EXAMPLE, \"python\", Path(\"example.py\"))\n", "print(show_ts_node(ts_tree.root_node, show_unnamed=False))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### ByteMap and LineMap\n", "\n", "The scope tree API uses unicode ranges, while the underlying tree-sitter library uses byte ranges. Sometimes you need to manually convert between these two types of ranges. This can be easily achieved using the `ByteMap` class. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.static_analysis.common import ByteMap\n", "\n", "ex_str = \"a😃b\"\n", "bmap = ByteMap(ex_str)\n", "\n", "char_to_bytes = {i: bmap.char_to_byte(i) for i in range(len(ex_str) + 1)}\n", "print(\"char to bytes:\", char_to_bytes)\n", "bytes_to_char = {i: bmap.byte_to_char(i) for i in range(len(ex_str.encode()) + 1)}\n", "print(\"bytes to char:\", bytes_to_char)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also map between charactor positions and `(line, column)`s: just build a `LineMap` and call the corresponding methods."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.static_analysis.parsing import LineMap\n", "\n", "MultiLineText = \"\"\"\\\n", "abc\n", "\n", "@de\n", "'g '\n", "h\"\"\"\n", "lmap = LineMap(MultiLineText)\n", "\n", "char_id = lmap.get_char_index(line=2, column=0)\n", "print(f\"Char at (2:0): {repr(MultiLineText[char_id])}\")\n", "print(\"-\" * 60)\n", "\n", "for i, char in enumerate(MultiLineText):\n", "    line, column = lmap.get_line_column(i)\n", "    print(f\"char {repr(char)}: {line=}, {column=}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Detect Syntax Errors\n", "\n", "There is a convient function from the parsing module called `find_syntax_errors`, which will return the character ranges of all syntax errors according to tree-sitter."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.static_analysis.parsing import find_syntax_errors\n", "\n", "\n", "bad_code = \"\"\"\n", "\n", "class Foo:\n", "    def f1(x):\n", "        x = \"\n", "        return y\n", "\n", "    def f2():\n", "        (a,))\n", "\"\"\"\n", "\n", "find_syntax_errors(bad_code, \"python\", Path(\"example.py\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Imports Dropout\n", "We can use the `ImportDropout` class to randomly drop some imported symbols.\n", "This can be useful for making the model robust against missing imports."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.fim.import_dropout import ImportDropout\n", "from research.static_analysis.parsing import show_ts_node\n", "from random import Random\n", "\n", "import_code = \"\"\"\n", "import l1\n", "import l2 as l3\n", "from test import a, b, c, d, e\n", "import p1, p2, p3 as p4, p5\n", "from test2 import a as x, b as y, c, d as z\n", "\"\"\"\n", "\n", "dropout = ImportDropout(dropout_rate=0.5)\n", "tree = GlobalParser.parse_ts_tree(code=import_code, lang=\"python\", path=Path(\"example.py\"))\n", "new_code = dropout.transform(tree=tree, lang=\"python\", rng=Random(42))\n", "print(new_code.decode())"]}], "metadata": {"kernelspec": {"display_name": "static-analysis-UqAzVeut-py3.9", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
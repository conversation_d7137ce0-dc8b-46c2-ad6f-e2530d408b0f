{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.languages.language_guesser import guess_language\n", "from research.core.constants import AUGMENT_ROOT\n", "from base.static_analysis.smart_header import show_line_headers\n", "\n", "test_file = \"base/cloud/iap/iap.rs\"\n", "# test_file = \"clients/vscode/src/augment-api.ts\"\n", "# test_file = \"experimental/carl/simplenccl/src/simplenccl.cpp\"\n", "# test_file = \"base/cloud/iap/iap.go\"\n", "# test_file = \"experimental/evan/se_interview/java/SystemTest.java\"\n", "# test_file = \"research/eval/harness/systems/next_edit_gen_system.py\"\n", "example_code = (AUGMENT_ROOT / test_file).read_text()\n", "print(show_line_headers(example_code, guess_language(test_file)))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
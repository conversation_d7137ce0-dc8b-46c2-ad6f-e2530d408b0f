{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Automatic Evaluation with LLMs\n", "\n", "This notebook includes experiments that use GPT-3.5 to compare code completions."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Set path to include augment code.\n", "import re\n", "import sys\n", "from typing import *\n", "\n", "import openai\n", "\n", "sys.path.append(\"/home/<USER>/augment/\")\n", "# NOTE: Enter your API key here.\n", "openai.api_key = \"\"\n", "\n", "K1 = TypeVar(\"K1\")\n", "K2 = TypeVar(\"K2\")\n", "V = TypeVar(\"V\")\n", "def transpose_dict(dct: dict[K1, dict[K2, V]]) -> dict[K2, dict[K1, V]]:\n", "    ret = {}\n", "    for k1, dct_ in dct.items():\n", "        for k2, v in dct_.items():\n", "            ret.setdefault(k2, {})[k1] = v\n", "    return ret"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Load patch data.\n", "\n", "Load the patch data from the path given below."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Comparing 19 patches between ['gpt35', 'indiana']\n"]}], "source": ["import sys\n", "import jsonlines\n", "from pathlib import Path\n", "\n", "from research.eval.patch_lib import Patch\n", "\n", "SYSTEMS = [\"gpt35\", \"indiana\"]\n", "PATH = Path('/mnt/efs/augment/user/arun/repo_eval/20230619')\n", "\n", "assert len(SYSTEMS) == 2, \"Expecting only two systems to compare at the moment.\"\n", "\n", "patches_by_sys = {\n", "    sys: {\n", "       patch.patch_id: patch \n", "       for dct in jsonlines.open(PATH / f\"{sys}.jsonl\")\n", "       if (patch := Patch(**dct)) is not None\n", "    }\n", "    for sys in SYSTEMS\n", "}\n", "patches_by_task = transpose_dict(patches_by_sys)\n", "\n", "# Filter to those with multiple systems.\n", "patches_by_task = {task_id: sys_patches for task_id, sys_patches in patches_by_task.items() if len(sys_patches) > 1}\n", "print(f\"Comparing {len(patches_by_task)} patches between {SYSTEMS}\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# GPT3.5 Prompt"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["PROMPT = \"\"\"\n", "Please act as an impartial judge and evaluate the quality of the responses provided by\n", "two AI code completion assistants. You should choose the assistant that completes the\n", "given code following the user's intent, style and software engineering best practices.\n", "Your evaluation should consider factors such as the correctness, completeness,\n", "error handling, and style of their responses.\n", "Begin your evaluation by comparing the two responses and provide a short explanation.\n", "Avoid any positional biases and ensure that the order in which the responses were\n", "presented does not influence your decision. Do not allow the length of the responses\n", "to influence your evaluation.\n", "Do not favor certain names of the assistants.\n", "Be as objective as possible. After providing your explanation, output your final verdict\n", "by strictly following this format:\n", "\"[[A]]\" if assistant A is better, \"[[B]]\" if assistant B is better, and \"[[C]]\" for a tie.\n", "\n", "[Common Code]\n", "{prefix}\n", "# ADD COMPLETION HERE\n", "{suffix}\n", "\n", "[The Start of Assistant A's Answer]\n", "{patch_a}\n", "[The End of Assistant A's Answer]\n", "[The Start of Assistant B's Answer]\n", "{patch_b}\n", "[The End of Assistant B's Answer]\n", "\n", "[Explanation]\n", "\"\"\"\n", "\n", "RESPONSE = r\"(?P<explanation>.*)\\[\\[(?P<verdict>[A-C])\\]\\]$\"\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def evaluate_with_openai(patch_a: <PERSON>, patch_b: Patch, prompt: str = PROMPT,\n", "                         model_name: str = \"text-davinci-003\",\n", "                         temperature: float = 1.0,\n", "                         max_generated_tokens: int = 256,\n", "                         context_length: int = 300,\n", "                         response_format: str = RESPONSE,\n", "                        ):\n", "    \"\"\"Evaluate whether `patch_a` is better than `patch_b` using OpenAI's API.\"\"\"\n", "    assert patch_a.file_content == patch_b.file_content\n", "    assert patch_a.char_start == patch_b.char_start\n", "    assert patch_a.char_end == patch_b.char_end\n", "\n", "    # TODO(arun): Figure out how to limit context by scoping them.\n", "    prompt = prompt.format(\n", "        prefix=patch_a.file_content[\n", "            max(0,patch_a.char_start-context_length):patch_a.char_start].rstrip(),\n", "        suffix=patch_a.file_content[\n", "            patch_a.char_end:patch_a.char_end+context_length].lstrip(),\n", "        patch_a=patch_a.patch_content,\n", "        patch_b=patch_b.patch_content,\n", "        )\n", "\n", "    response = openai.Completion.create(\n", "        model=model_name,\n", "        prompt=prompt,\n", "        temperature=temperature,\n", "        max_tokens=max_generated_tokens,\n", "        top_p=1,\n", "        frequency_penalty=0,\n", "        presence_penalty=0,\n", "    )\n", "\n", "    assert response, \"Couldn't call OpenAI\"\n", "\n", "    ret = {\n", "        \"raw_prompt\": prompt,\n", "        \"raw_response\": response.choices[0][\"text\"],\n", "    }\n", "\n", "    if (m := re.match(response_format, ret[\"raw_response\"], re.DOTALL)):\n", "        ret.update(m.groupdict())\n", "    return ret"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CarperAI_trlx/0: indiana vs gpt35\n", "A:     def register_decorator(cls):\n", "        _DATAPIPELINE[name] = cls\n", "B:     def decorator(cls: any) -> Any:\n", "        _DATAPIPELINE[name] = cls\n", "        return cls\n", "\n", "    return decorator\n", "\n", "\n", "def get_datapipeline_by_name(name):\n", "    return _DATAPIPELINE[name]\n", "\n", "Winner: B\n", "Explanation: Assistant A's response is incomplete, as it is missing the 'return' clause at the end that would make the registration valid. On the other hand, Assistant B's response is complete, providing the necessary return clause to make the registration valid and also providing an additional function to retrieve the registered architecture by its name.\n", "\n", "\n", "CarperAI_trlx/1: gpt35 vs indiana\n", "A:         global _DATAPIPELINE\n", "        _DATAPIPELINE[name] = cls\n", "\n", "    if name in _DATAPIPELINE.keys():\n", "        msg = \"Cannot register duplicate datapipeline ({})\".format(name)\n", "        raise ValueError(msg)\n", "\n", "B:         _DATAPIPELINE[name] = cls\n", "        return cls\n", "\n", "\n", "Winner: A\n", "Explanation: Assistant A's answer is more complete than Assistant B's answer as it provides duplicate exception handling and includes error messaging that states why the pipeline cannot be registered. With regards to style, both solutions are clean and organized, but Assistant A's answer is more readable, as it is more clearly communicative of the code's intent.\n", "\n", "\n", "CarperAI_trlx/10: gpt35 vs indiana\n", "A:     verbosity_value = os.getenv(\"TRLX_VERBOSITY\", \"\").lower()\n", "    if verbosity_value in log_levels.keys():\n", "        return log_levels[verbosity_value]\n", "    else:\n", "        return _default_log_level\n", "\n", "B:     verbosity = os.getenv(\"TRLX_VERBOSITY\", \"info\")\n", "    if verbosity in log_levels:\n", "Winner: A\n", "Explanation: Assistant A provides a complete response that checks if the value of the environment variable is found in the dictionary, otherwise it falls back to the default log level. This is a good approach to code completion that is both robust and un-biased.\n", "\n", "Assistant B also provides a good response, but the main difference lies in the approach to checking the environment variable. It does not provide a valid default value, nor does it check the value against the values provided in the predefined `log_levels` dictionary.\n", "\n", "\n", "CarperAI_trlx/2: indiana vs gpt35\n", "A:         with open(yml_fp, \"r\") as f:\n", "B:         with open(yml_fp, \"r\") as stream:\n", "            yaml_config = yaml.safe_load(stream)\n", "\n", "        return cls.from_dict(yaml_config)\n", "\n", "Winner: B\n", "Explanation: Assistant A's response provides the code needed to open the yml_fp path, but does not provide functionality for reading and validating the yml file they are provided.\n", "Assistant B's response includes code related to opening the yml_fp path, validating the yml file provided, and a return statement once the validating is complete.\n", "\n", "\n", "CarperAI_trlx/3: indiana vs gpt35\n", "A:         method = MethodConfig.from_dict(config[\"method\"])\n", "        model = ModelConfig.from_dict(config[\"model\"])\n", "B:         method = get_method(config[\"method\"][\"name\"], config[\"method\"].get(\"config_path\"))\n", "\n", "        model = ModelConfig.from_dict(config[\"model\"])\n", "        tokenizer = TokenizerConfig.from_dict(config[\"tokenizer\"])\n", "        optimizer = OptimizerConfig.from_dict(config[\"optimizer\"])\n", "        scheduler = SchedulerConfig.from_dict(config[\"scheduler\"])\n", "        train = TrainConfig.from_dict(config[\"train\"])\n", "\n", "        return cls(method=method, model=model, optimizer=optimizer, scheduler=scheduler, tokenizer=tokenizer, train=train)\n", "\n", "Winner: B\n", "Explanation: Both answers provided by the assistants address the code completion requirement, however Assistant <PERSON>'s response is much more detailed and valuable. It provides a constructor which implements the whole configuration setup with all the necessary calls to helper config objects. Additionally, it performs exception handling in case of non-existent parameters. Conversely, the answer provided by Assistant A mainly consists of instantiations and lacks any meaningful error handling code.\n", "\n", "\n", "CarperAI_trlx/37: indiana vs gpt35\n", "A:         \"\"\"\n", "        :param hs: (bsize, hidden_size)\n", "B:         if grd = states_ixs is None:\n", "            return None, None, None\n", "\n", "        qs = [q_head(hs[states_ixs]) for q_head in self.q_heads]\n", "        target_qs = [q_head(hs[states_ixs]) for q_head in self.target_q_heads]\n", "        vs = self.v_head(hs[states_ixs])\n", "        return qs, target_qs, vs\n", "\n", "Winner: B\n", "Explanation: Both assistants provided correct, complete, and syntactically accurate answers. Assistant <PERSON>'s answer was also formatted more clearly, with the added use of comments and line breaks, making it easier to read and understand. Furthermore, they both included the necessary error handling code for the input variable `states_ixs`, returning `None` if it is `None`. \n", "\n", "\n", "CarperAI_trlx/38: indiana vs gpt35\n", "A:         super().__init__(base_model)\n", "        self.ilql_heads = ILQLHeads(\n", "B:         base_model = self._auto_model_parent_class.from_pretrained(base_model)\n", "        self.two_qs = two_qs\n", "        self.alpha = alpha\n", "\n", "        super().__init__(base_model)\n", "\n", "        hidden_size = hf_get_hidden_size(base_model)\n", "        lm_head = hf_get_lm_head(base_model)\n", "        self.ilql_heads = ILQLHeads(hidden_size, lm_head.n_vocab, two_qs, alpha, base_model.config.dtype)\n", "\n", "Winner: B\n", "Explanation: Assistant B provides a much more comprehensive response than Assistant A. Assistant B takes into consideration the base_model parameter which is a PreTrainedModel. They use a 'from_pretrained' method to instantiate base_model and set two_qs and alpha. Additionally, they calculate the hidden size by using a helper function and also fetch the language model head. This is followed by the initialization of the super class and instantiation of ILQLHeads based on the resulting attributes.\n", "\n", "\n", "CarperAI_trlx/39: gpt35 vs indiana\n", "A:         logits, past_key_values = self.base_model(\n", "            input_ids,\n", "            attention_mask=attention_mask,\n", "            position_ids=position_ids,\n", "            past_key_values=past_key_values,\n", "        )\n", "\n", "        if past_key_values is not None:\n", "            hs = past_key_values[0][-1]\n", "        else:\n", "            pred_hiddens = self.base_model.pred_hiddens(\n", "                input_ids, attention_mask=attention_mask, position_ids=position_ids\n", "            )\n", "            hs = pred_hiddens[-1]\n", "\n", "        qs, target_qs, vs = self.ilql_heads(\n", "            hs, states_ixs=states_ixs, actions_ixs=actions_ixs\n", "        )\n", "\n", "        self.ilql_heads.sync_target_q_heads()\n", "\n", "        return logits, (qs, target_qs, vs), past_key_values\n", "\n", "B:         outputs = self.base_model(\n", "            input_ids,\n", "            attention_mask=attention_mask,\n", "Winner: A\n", "Explanation: Assistant A provides a response with more detail and following industry best practices by adding error handling (e.g. ensuring past_key_values is not None by adding an else case) and synchronization to the target Q-Heads. Assistant B follows the same order of operations presented in the code but provides a less complete response, such as by not including in the code any of the details provided by Assistant <PERSON>. \n", "\n", "\n", "CarperAI_trlx/4: indiana vs gpt35\n", "A:     def register_method_decorator(cls):\n", "        _METHODS[name] = cls\n", "B:     def decorate(cls):\n", "        _METHODS[name.lower()] = cls\n", "        return cls\n", "\n", "    return decorate\n", "\n", "Winner: B\n", "Explanation: The answer of Assistant A is incomplete because it does not return anything for the decorator, meaning that the registered class will not appear in _METHODS. Assistant B provides a complete answer by returning the decorated class which will appear in _METHODS. Assistant B also converts the method name to lowercase, which is considered a software engineering best practice. \n", "\n", "\n", "CarperAI_trlx/44: indiana vs gpt35\n", "A:         # TODO (jon-tow): This should be a `PretrainedConfig` of the specific\n", "        # underlying type.\n", "B:         if kwargs is not None:\n", "            wrapped_model_kwargs, config_kwargs = cls._split_kwargs(kwargs)\n", "        else:\n", "            config_kwargs = {}\n", "            wrapped_model_kwargs = {}\n", "        base_model = cls._auto_model_parent_class.from_config(config, **config_kwargs)\n", "        model = cls(base_model=base_model, **wrapped_model_kwargs)\n", "        return model\n", "\n", "Winner: B\n", "Explanation: Assistant A's response provides a single line but does not provide code to actually instantiate the base model which is the goal from the initial code snippet.\n", " Assistant B's response, on the other hand, provides several lines of code that properly instantiates the base model according to the user's specifications. \n", "\n", "\n"]}], "source": ["# Run mod\n", "import random\n", "responses = []\n", "for _, (task_id, sys_patch) in zip(range(10), patches_by_task.items()):\n", "    (sys_a, patch_a), (sys_b, patch_b), *_ = list(sys_patch.items())\n", "    if random.random() < 0.5:\n", "        (sys_a, patch_a), (sys_b, patch_b) = (sys_b, patch_b), (sys_a, patch_a) \n", "\n", "    print(f\"{task_id}: {sys_a} vs {sys_b}\")\n", "    print(f\"A: {patch_a.patch_content}\")\n", "    print(f\"B: {patch_b.patch_content}\")\n", "\n", "    response = evaluate_with_openai(patch_a, patch_b)\n", "    responses.append(response)\n", "    if \"verdict\" in response:\n", "        print(f\"Winner: {response['verdict']}\")\n", "        print(f\"Explanation: {response['explanation']}\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
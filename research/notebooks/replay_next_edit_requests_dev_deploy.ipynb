{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay requets against dev deploy.\n", "\n", "This notebook includes an example of how to replay requests in dogfood (or aitutor-*) \n", "against a dev deploy. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Gets Requests from BigQuery"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Gets Random Requests\n", "\n", "We get requests from Gcloud BigQuery. Modify the query as needed. "]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["QUERY = \"\"\"\n", "WITH\n", "  request AS (\n", "  SELECT\n", "    request_id,\n", "    raw_json,\n", "    time\n", "  FROM\n", "    `staging_request_insight_full_export_dataset.next_edit_host_request`\n", "  WHERE\n", "    tenant = 'dogfood-shard'\n", "    AND DATE(time) >= '2024-10-05'\n", "    AND JSON_EXTRACT_SCALAR(raw_json, \"$.request.mode\") = \"BACKGROUND\"),\n", "  response AS(\n", "  SELECT\n", "    request_id,\n", "    ARRAY_AGG(raw_json) AS raw_json,\n", "  FROM (\n", "    SELECT\n", "      request_id,\n", "      time,\n", "      raw_json,\n", "    FROM\n", "      `staging_request_insight_full_export_dataset.next_edit_host_response`\n", "    WHERE\n", "      tenant = 'dogfood-shard'\n", "      AND JSON_EXTRACT(raw_json, \"$.suggestions\") IS NOT NULL\n", "      OR JSON_EXTRACT(raw_json, \"$.generation\") IS NOT NULL )\n", "  GROUP BY\n", "    request_id),\n", "  METADATA AS (\n", "    SELECT\n", "      request_id,\n", "      JSON_VALUE(raw_json, \"$.user_id\") AS user_id,\n", "      JSON_VALUE(raw_json, \"$.user_agent\") AS user_agent,\n", "      CAST(REGEXP_EXTRACT(JSON_VALUE(raw_json, \"$.user_agent\"), \"Augment.vscode-augment/0.([0-9]+).0\") AS INT64) AS version\n", "    FROM\n", "      `staging_request_insight_full_export_dataset.request_metadata`\n", "    WHERE\n", "      NOT STARTS_WITH(JSON_VALUE(raw_json, \"$.user_agent\"), 'AugmentHealthCheck')\n", "      AND NOT STARTS_WITH(JSON_VALUE(raw_json, \"$.user_agent\"), 'augment_review_bot')\n", "      AND NOT STARTS_WITH(JSON_VALUE(raw_json, \"$.user_agent\"), 'Augment-EvalHarness')\n", "      AND tenant = 'dogfood-shard'\n", "      -- Excludes eng who are actively working on the feature.\n", "      AND JSON_VALUE(raw_json, \"$.user_id\") NOT IN (\"joel\",\n", "        \"arunch<PERSON>ty\")\n", "      -- Client Version. This effects what events we are recording.\n", "      AND CAST(REGEXP_EXTRACT(JSON_VALUE(raw_json, \"$.user_agent\"), \"Augment.vscode-augment/0.([0-9]+).0\") AS INT64) >= 229)\n", "SELECT\n", "  request.request_id,\n", "  request.raw_json AS request_json,\n", "  response.raw_json as response_json\n", "FROM\n", "  request\n", "JOIN\n", "  METADATA\n", "USING\n", "  (request_id)\n", "JOIN\n", "  response\n", "USING\n", "(request_id)\n", "ORDER BY\n", "  request.request_id\n", "LIMIT 100\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "\n", "from base.datasets import replay_utils, tenants\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "tenant = tenants.DOGFOOD_SHARD\n", "bigquery_client = bigquery.Client(\n", "    project=tenant.project_id, credentials=get_gcp_creds()[0]\n", ")\n", "blob_cache = replay_utils.get_blob_cache(tenant)\n", "checkpoint_cache = replay_utils.get_checkpoint_cache(tenant)\n", "\n", "rows = bigquery_client.query_and_wait(QUERY, page_size=128)\n", "data = list(rows)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Gets Specific Requests"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["TARGET_REQUESTS = [\n", "    \"7e02ba78-5fff-4578-930e-2837e0031f27\",\n", "]"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["QUERY = \"\"\"\n", "SELECT\n", "  request_id,\n", "  raw_json as request_json,\n", "  time\n", "FROM\n", "  `staging_request_insight_full_export_dataset.next_edit_host_request`\n", "WHERE\n", "  tenant = 'dogfood-shard'\n", "  AND request_id IN UNNEST(@request_ids)\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "\n", "from base.datasets import replay_utils, tenants\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "tenant = tenants.DOGFOOD_SHARD\n", "bigquery_client = bigquery.Client(\n", "    project=tenant.project_id, credentials=get_gcp_creds()[0]\n", ")\n", "blob_cache = replay_utils.get_blob_cache(tenant)\n", "checkpoint_cache = replay_utils.get_checkpoint_cache(tenant)\n", "query_parameters = [\n", "    bigquery.ArrayQueryParameter(\"request_ids\", \"STRING\", TARGET_REQUESTS)\n", "]\n", "job_config = bigquery.QueryJobConfig(query_parameters=query_parameters)\n", "\n", "rows = bigquery_client.query_and_wait(QUERY, job_config=job_config, page_size=128)\n", "data = list(rows)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Replay"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Gets Augment client"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from base.augment_client.client import AugmentClient\n", "\n", "with open(os.path.expanduser(\"~/.config/augment/api_token\")) as f:\n", "    client = AugmentClient(\n", "        url=\"https://dev-vzhao.us-central.api.augmentcode.com\",\n", "        token=f.read().strip(),\n", "    )\n", "next_edit_host_name = \"raven-edit-v4-15b\"\n", "model_client = client.client_for_model(next_edit_host_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Upload blobs"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "from base.blob_names import blob_names_pb2\n", "from google.protobuf.json_format import ParseDict\n", "\n", "all_blob_names = set()\n", "\n", "for r in tqdm.tqdm(data):\n", "    blobs_proto = ParseDict(\n", "        r[\"request_json\"][\"request\"][\"blobs\"], blob_names_pb2.Blobs()\n", "    )\n", "    blob_names = replay_utils.resolve_checkpoint(blobs_proto, checkpoint_cache)\n", "    replay_utils.upload_and_ensure_blobs_exist(\n", "        client, blob_cache, blob_names, next_edit_host_name\n", "    )\n", "    all_blob_names.update(blob_names)\n", "\n", "# Verifies that all the blobs are indexed.\n", "replay_utils.index_status(client, all_blob_names, next_edit_host_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Replay requests in dev tenants."]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["from google.protobuf.json_format import MessageToDict, ParseDict\n", "\n", "from base.blob_names import blob_names_pb2\n", "from services.next_edit_host import next_edit_pb2\n", "from services.request_insight import request_insight_pb2\n", "\n", "all_requests = []\n", "for r in tqdm.tqdm(data):\n", "    request_pb = ParseDict(r[\"request_json\"], request_insight_pb2.RINextEditRequest())\n", "    blob_names = replay_utils.resolve_checkpoint(\n", "        request_pb.request.blobs, checkpoint_cache\n", "    )\n", "    # NOTE: We are sending the same request to the dev deploy system for reply. See\n", "    # `FrontNextEditRequest` for all the request fields.\n", "    responses = list(\n", "        model_client.next_edit_stream(\n", "            sequence_id=request_pb.request.sequence_id,\n", "            lang=request_pb.request.lang,\n", "            # The dev deploy system might not have `checkpoint_id`. Therefore, we use\n", "            # the list of blob names directly.\n", "            blobs={\n", "                \"checkpoint_id\": None,\n", "                \"added_blobs\": replay_utils.resolve_checkpoint(\n", "                    request_pb.request.blobs, checkpoint_cache\n", "                ),\n", "                \"deleted_blobs\": [],\n", "            },\n", "            recent_changes=[\n", "                MessageToDict(\n", "                    e,\n", "                    including_default_value_fields=True,\n", "                    preserving_proto_field_name=True,\n", "                )\n", "                for e in request_pb.request.recent_changes\n", "            ],\n", "            instruction=request_pb.request.instruction,\n", "            path=request_pb.request.path,\n", "            blob_name=request_pb.request.blob_name,\n", "            selection_begin_char=request_pb.request.selection_begin_char,\n", "            selection_end_char=request_pb.request.selection_end_char,\n", "            prefix=request_pb.request.prefix,\n", "            selected_text=request_pb.request.selected_text,\n", "            suffix=request_pb.request.suffix,\n", "            diagnostics=[\n", "                MessageToDict(\n", "                    e,\n", "                    including_default_value_fields=True,\n", "                    preserving_proto_field_name=True,\n", "                )\n", "                for e in request_pb.request.diagnostics\n", "            ],\n", "            mode=next_edit_pb2.NextEditMode.Name(request_pb.request.mode),\n", "            scope=next_edit_pb2.NextEditScope.Name(request_pb.request.scope),\n", "            edit_events=[\n", "                MessageToDict(\n", "                    e,\n", "                    including_default_value_fields=True,\n", "                    preserving_proto_field_name=True,\n", "                )\n", "                for e in request_pb.request.edit_events\n", "            ],\n", "            blocked_locations=[\n", "                MessageToDict(\n", "                    e,\n", "                    including_default_value_fields=True,\n", "                    preserving_proto_field_name=True,\n", "                )\n", "                for e in request_pb.request.blocked_locations\n", "            ],\n", "            # TODO: The response json decoding is not working properly. Use the new\n", "            # request id to see results on the support site.\n", "            warn_on_parse_error=True,\n", "        )\n", "    )\n", "    all_requests.append(responses)\n", "\n", "print(\"Old and new request ids:\")\n", "for r, responses in zip(data, all_requests):\n", "    print(f\"Before: {r['request_id']}; After: {responses[0].request_id}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
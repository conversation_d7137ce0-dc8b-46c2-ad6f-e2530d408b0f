{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### The code below can be used to visualize a model's prediction on any given piece of text."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import HTML, display\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "from research.utils.visualize_logits import LogitsVisualizer\n", "\n", "test_prompt = '''\\\n", "<fim_prefix>\n", "def print_odd_even(n: int):\n", "    \"\"\"print whether n is odd or even.\"\"\"\n", "    if n % 2 == 0:\n", "        print(f\"{n} is even\")\n", "    else:<fim_suffix><fim_middle>\n", "        print(f\"{n} is even\")<|endoftext|>\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "<|padding|><|padding|><|padding|><|padding|>\n", "'''\n", "\n", "tkn = StarCoderTokenizer()\n", "prompt_tks = tkn.tokenize(test_prompt)\n", "is_masked = [t % 2 == 0 for t in range(len(prompt_tks))]\n", "\n", "html_str = LogitsVisualizer(tokenizer=tkn).seq_distribution_as_html(\n", "    prompt_tks, None, is_masked=is_masked\n", ")\n", "display(HTML(html_str))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.models.starcoder_models import Starcoderbase1bFimAligned\n", "\n", "model = Starcoderbase1bFimAligned()\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["LogitsVisualizer(tokenizer=tkn).visualize_model_prediction(model, test_prompt)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
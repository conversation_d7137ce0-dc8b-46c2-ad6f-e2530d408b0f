{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### This Notebook creates a next edit gen eval set from the Augment repo's Git history."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.next_edits.edit_gen_sampler import EditGenSampler\n", "from research.next_edits.edit_gen_stages import SamplingConfig\n", "\n", "# use commits before \"Return logits in raw_generate_tokens. (#6835)\"\n", "last_commit_hash = \"24dbfb19bc6bd320a7539f9d04521bc935ac0ba4\"\n", "dataset_name = \"augment_v2_pr6835_uniform\"\n", "\n", "sampling_config = SamplingConfig(\n", "    sampler=EditGenSampler(\n", "        random_edit_region_rate=0.0,\n", "        random_target_file_rate=0.0,\n", "        max_diff_tokens=2000,\n", "        max_edit_region_lines=80,\n", "        min_edit_region_lines=10,\n", "        wip_sampling_strategy=\"uniform\",\n", "    ),\n", "    max_problems_per_repo=2000,\n", "    max_problems_per_commit=5,\n", "    max_files_per_repo=10_000,\n", "    timeout_per_repo=1600,\n", "    max_squashes=0,  # change this if need samples with squashed commits\n", "    small_commits_tokens=500,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tqdm.auto import tqdm\n", "from research.core.constants import AUGMENT_ROOT\n", "from research.next_edits.edit_gen_stages import repo_commits_to_problems\n", "\n", "\n", "problems_iter = repo_commits_to_problems(\n", "    sampling_config,\n", "    repo_path=AUGMENT_ROOT,\n", "    seed=42,\n", "    max_workers=8,\n", "    last_commit_hash=last_commit_hash,\n", ")\n", "problems = list(tqdm(problems_iter, total=sampling_config.max_problems_per_repo))\n", "print(f\"Sampled {len(problems)} problems.\")\n", "has_change_probs = [p for p in problems if p.output.changed]\n", "print(f\"Sampled {len(has_change_probs)} problems with a change.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.notebook_uis.edit_gen import display_edit_gen_viewer\n", "\n", "display_edit_gen_viewer(has_change_probs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import pickle\n", "from dataclasses import asdict\n", "import json\n", "\n", "save_path = f\"/mnt/efs/augment/data/eval/next_edits/{dataset_name}.pkl\"\n", "\n", "with open(save_path, \"wb\") as f:\n", "    pickle.dump(has_change_probs, f)\n", "\n", "print(f\"Dataset saved to: {save_path}\")\n", "\n", "# save the config dict as well\n", "config_dict = asdict(sampling_config)\n", "with open(Path(save_path).with_name(f\"{dataset_name}_config.json\"), \"w\") as f:\n", "    json.dump(config_dict, f, indent=2)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Inspect APICallTask"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%reload_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core import model_input as data_lib\n", "from research.core import utils_for_str as str_lib\n", "from research.eval.harness import tasks as task_lib\n", "\n", "\n", "def show_completion(input: data_lib.ModelInput, max_lines: int = 1000):\n", "    prefix = str_lib.get_last_n_lines(input.prefix, max_lines)\n", "    suffix = str_lib.get_first_n_lines(input.suffix, max_lines)\n", "    middle = input.target\n", "    assert middle is not None\n", "    str_lib.show_completion(prefix, suffix, middle)\n", "\n", "\n", "task = task_lib.ApiCallTask()\n", "print(f\"There are {len(task)} examples in {task.name}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 96\n", "xdata = task[index]\n", "show_completion(xdata.model_input, max_lines=10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task.execute(xdata.model_input, xdata.model_input.target or \"\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analysis the Results from APICallTask"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from research.eval.harness.tasks import api_call_task\n", "from research.eval.harness.utils import read_json\n", "\n", "# Please change this directory path to yours\n", "cache_dir = pathlib.Path(\"/mnt/efs/augment/eval/jobs/Ggd4nhGk/000__basic_rag_HydraTaskV2/cache\")\n", "all_files: list[str] = []\n", "for json_file in cache_dir.glob(\"*-*.json\"):\n", "    all_files.append(str(json_file))\n", "all_files = sorted(all_files)\n", "\n", "all_results = []\n", "for index, json_file in enumerate(all_files):\n", "    # Each cur_result\n", "    cur_result = read_json(pathlib.Path(json_file))\n", "    all_results.append(cur_result)\n", "print(f\"Loaded {len(all_results)} json files.\")\n", "\n", "report = api_call_task.detailed_error_analysis(all_results)\n", "print(report)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
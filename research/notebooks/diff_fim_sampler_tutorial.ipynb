{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Diff-aware FIM sampler tutorial\n", "\n", "This notebook shows how to use the diff-aware FIM sampler to generate FIM instances that look like real code completion problems.\n", "We'll use the following example:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ex_code_before = '''\n", "from __future__ import annotations\n", "\n", "from dataclasses import dataclass\n", "from typing import Iterable, Literal, Union\n", "\n", "import colorama\n", "import fast_diff_match_patch as dmp\n", "from typing_extensions import assert_never\n", "\n", "from base.static_analysis.common import assert_eq\n", "\n", "\n", "@dataclass(frozen=True)\n", "class StrDiff:\n", "    \"\"\"Represents a diff between two strings.\"\"\"\n", "\n", "    spans: tuple[Diff<PERSON>pan, ...]\n", "    \"\"\"Stores all changes as a sequence of DiffSpans.\"\"\"\n", "\n", "    @staticmethod\n", "    def build(old: str, new: str, algorithm: Literal[\"dmp\"] = \"dmp\") -> StrDiff:\n", "        \"\"\"Build a diff between two strings.\"\"\"\n", "        if algorithm == \"dmp\":\n", "            return dmp_diff(old, new)\n", "        else:\n", "            raise ValueError(f\"Unknown algorithm: {algorithm}\")\n", "\n", "    def get_before(self) -> str:\n", "        \"\"\"Construct the old version of the text from this diff.\"\"\"\n", "        return \"\".join(op.before for op in self.spans)\n", "\n", "    def get_after(self) -> str:\n", "        \"\"\"Construct the new version of the text from this diff.\"\"\"\n", "        return \"\".join(op.after for op in self.spans)\n", "\n", "    def inverted(self) -> \"StrDiff\":\n", "        \"\"\"Return a inversed version of this diff.\"\"\"\n", "        new_ops = tuple(op.inverted() for op in self.spans)\n", "        return StrDiff(new_ops)\n", "\n", "    def __repr__(self) -> str:\n", "        ops_str = \", \".join(repr(op) for op in self.spans)\n", "        return f\"StrDiff({ops_str})\"\n", "'''\n", "\n", "ex_code_after = '''\n", "from __future__ import annotations\n", "\n", "import bisect\n", "from dataclasses import dataclass\n", "from functools import cached_property\n", "from typing import Iterable, Literal, Sequence, Union\n", "\n", "import colorama\n", "import fast_diff_match_patch as dmp\n", "from typing_extensions import assert_never\n", "\n", "from base.ranges.range_types import CharRange\n", "from base.static_analysis.common import assert_eq\n", "\n", "\n", "@dataclass(frozen=True)\n", "class StrDiff:\n", "    \"\"\"Represents a diff between two strings.\"\"\"\n", "\n", "    spans: tuple[Diff<PERSON>pan, ...]\n", "    \"\"\"Stores all changes as a sequence of DiffSpans.\"\"\"\n", "\n", "    @staticmethod\n", "    def build(old: str, new: str, algorithm: Literal[\"dmp\"] = \"dmp\") -> StrDiff:\n", "        \"\"\"Build a diff between two strings.\"\"\"\n", "        if algorithm == \"dmp\":\n", "            return dmp_diff(old, new)\n", "        else:\n", "            raise ValueError(f\"Unknown algorithm: {algorithm}\")\n", "\n", "    def get_before(self) -> str:\n", "        \"\"\"Construct the old version of the text from this diff.\"\"\"\n", "        return \"\".join(op.before for op in self.spans)\n", "\n", "    def get_after(self) -> str:\n", "        \"\"\"Construct the new version of the text from this diff.\"\"\"\n", "        return \"\".join(op.after for op in self.spans)\n", "\n", "    def inverted(self) -> StrDiff:\n", "        \"\"\"Return the inverse of this diff.\"\"\"\n", "        new_ops = tuple(op.inverted() for op in self.spans)\n", "        return StrDiff(new_ops)\n", "\n", "    def span_ranges_in_after(self) -> Iterable[CharRange]:\n", "        \"\"\"Return the ranges of each span in the after version of the text.\"\"\"\n", "        offset = 0\n", "        for span in self.spans:\n", "            size = len(span.after)\n", "            yield CharRange(offset, offset + size)\n", "            offset += size\n", "\n", "    def __repr__(self) -> str:\n", "        ops_str = \", \".join(repr(op) for op in self.spans)\n", "        return f\"StrDiff({ops_str})\"\n", "'''"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.str_diff import StrDiff, print_diff\n", "\n", "print_diff(StrDiff.build(ex_code_before, ex_code_after))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from random import Random\n", "\n", "from research.fim.diff_fim_sampling import DiffFimSampler\n", "\n", "sampler = DiffFimSampler()\n", "sampler.fim_sampler.rng = Random(42)\n", "samples = [\n", "    (sample, sample_logs)\n", "    for _ in range(10)\n", "    if (\n", "        sample := sampler.sample_diff_fim(\n", "            (Path(\"example\"), ex_code_before),\n", "            (Path(\"example.py\"), ex_code_after),\n", "            \"python\",\n", "            logs=(sample_logs := []),\n", "        )\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.inspect_indexed_dataset import highlight_special_tokens\n", "\n", "for sampled, logs in samples[:5]:\n", "    to_show = highlight_special_tokens(\n", "        sampled.show_problem(), [\"<|cursor|>\", \"<|pause|>\"]\n", "    )\n", "    print(to_show)\n", "    print(\"\\n\".join(logs))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sample Diff-aware FIM problems from the Augment Repo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.constants import AUGMENT_ROOT\n", "from research.fim.repo_diff_fim_sampling import GitDiffFimSampler\n", "\n", "repo_sampler = GitDiffFimSampler(\n", "    DiffFimSampler(min_node_modify_ratio=0.0, simulate_WIP_context=True)\n", ")\n", "repo_result = repo_sampler.sample_from_commits(\n", "    AUGMENT_ROOT,\n", "    commits=100,\n", "    record_logs=True,\n", "    max_workers=8,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "\n", "failed_counts = Counter([x.category for x in repo_result.errors])\n", "\n", "print(f\"Errors: {len(repo_result.errors)}\")\n", "for category, count in failed_counts.most_common(5):\n", "    print(f\"(n={count}) {category}\")\n", "\n", "sampled_probs = repo_result.ctx_problems\n", "print(\"Successfully sampled problems:\", len(sampled_probs))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.notebook_uis.diff_fim import display_diff_fim_viewer\n", "\n", "display_diff_fim_viewer(repo_result)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
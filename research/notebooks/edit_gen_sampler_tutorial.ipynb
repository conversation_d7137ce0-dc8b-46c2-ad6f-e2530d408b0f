{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "from tqdm.auto import tqdm\n", "\n", "from research.next_edits.edit_gen_stages import (\n", "    <PERSON><PERSON>Config,\n", "    repo_changes_to_problems,\n", ")\n", "from research.core.constants import AUGMENT_ROOT\n", "from research.next_edits.next_edits_dataset import repo_changes_from_commits\n", "\n", "repo_changes = repo_changes_from_commits(AUGMENT_ROOT, max_commits=100)\n", "\n", "sampling_config = SamplingConfig(\n", "    max_problems_per_repo=500,\n", "    max_files_per_repo=20_000,\n", ")\n", "\n", "problems = repo_changes_to_problems(\n", "    sampling_config,\n", "    tqdm(repo_changes, desc=\"sample_problems\"),\n", "    seed=42,\n", "    start_time=time.time(),\n", ")\n", "problems = list(problems)\n", "print(f\"Sampled {len(problems)} problems.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["no_change_probs = [p for p in problems if not p.output.changed]\n", "print(f\"Sampled {len(no_change_probs)} no-change problems.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["turned_samples = [\n", "    p\n", "    for p in problems\n", "    if p.debug_info.get(\"simulate_wip_code\", {}).get(\"turn_into_negative_sample\", False)\n", "]\n", "print(f\"Sampled {len(turned_samples)} turned-into-negative-sample problems.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recency_change_probs = [p for p in problems if p.debug_info[\"num_recency_diffs\"] > 0]\n", "print(f\"Sampled {len(recency_change_probs)} recency-change problems.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["problems_by_size = sorted(\n", "    problems,\n", "    key=lambda p: p.debug_info[\"num_recency_diffs\"],\n", "    reverse=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import_change_probs = [\n", "    p\n", "    for p in problems\n", "    if (info := p.debug_info.get(\"_sample_edit_region\")) and info.get(\"num_imports\", 0)\n", "]\n", "print(f\"Sampled {len(import_change_probs)} import-change problems.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.notebook_uis.edit_gen import display_edit_gen_viewer\n", "\n", "display_edit_gen_viewer(problems_by_size)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.next_edits.edit_gen_stages import (\n", "    RetrievalConfig,\n", "    perform_dense_retrieval,\n", ")\n", "\n", "ret_config = RetrievalConfig(\n", "    retriever_config={\n", "        \"scorer\": {\n", "            \"name\": \"ethanol\",\n", "            \"checkpoint_path\": \"ethanol/ethanol6-04.1\",\n", "        },\n", "        \"chunker\": {\n", "            \"name\": \"line_level\",\n", "            \"max_lines_per_chunk\": 30,\n", "        },\n", "        \"query_formatter\": {\n", "            \"name\": \"ethanol6_query\",\n", "            \"max_tokens\": 1023,\n", "            \"add_path\": True,\n", "        },\n", "        \"document_formatter\": {\n", "            \"name\": \"ethanol6_document\",\n", "            \"max_tokens\": 999,\n", "            \"add_path\": True,\n", "        },\n", "    },\n", "    num_retrieved_chunks=40,\n", "    skip_dense_retrieval=True,\n", ")\n", "\n", "retrieval_augmented = list(perform_dense_retrieval(ret_config, problems, seed=42))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_next_edit.gen_prompt_formatter import EditGenFormatterConfig\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "from research.next_edits.edit_gen_stages import PromptConfig, format_as_tokens\n", "\n", "prompt_config = PromptConfig(\n", "    tokenizer_name=\"starcoder\",\n", "    formatter_config=EditGenFormatterConfig(diff_context_lines=6),\n", ")\n", "tokenizer = StarCoderTokenizer()\n", "\n", "tokenized_probs = list(format_as_tokens(prompt_config, retrieval_augmented, seed=42))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "prompts = list[str]()\n", "for tk_prob in tokenized_probs:\n", "    tokens = tk_prob.tokens\n", "    prompt = tokenizer.detokenize(np.abs(tokens))\n", "    loss_tokens = tokens[tokens >= 0]\n", "    loss_prompt = tokenizer.detokenize(loss_tokens)\n", "    separator = \"~=\" * 20 + \"Tokens with loss\" + \"~=\" * 20\n", "    prompts.append(f\"{prompt}\\n{separator}\\n{loss_prompt}\")\n", "\n", "# update the debug info\n", "for tk_prob, prob in zip(tokenized_probs, problems):\n", "    prob.debug_info = tk_prob.debug_info"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.notebook_uis.edit_gen import display_edit_gen_viewer\n", "\n", "display_edit_gen_viewer(list(problems), problem_prompts=prompts)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
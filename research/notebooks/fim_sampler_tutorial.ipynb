{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "%load_ext snakeviz\n", "%load_ext line_profiler"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["This notebook shows how to use the FIM sampler to generate FIM instances that look like real code completion problems. At a high leve, we need to do the following things:\n", "1. Create a CSTFimSampler, which samples from potential middle spans identified by the tree_sitter CST.\n", "2. Parse the target source using `ParsedFile.parse`. In this tutorial, we will use the source code from \"research/static_analysis/usage_analysis.py\" as the running example.\n", "3. Call `sampler.sample_fim` on the parsed file to get FIM instances. This function also optionally accepts a `cursor_range` argument to help restrict where the middle span is sampled."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.constants import AUGMENT_ROOT\n", "from research.fim.fim_sampling import CSTFimSampler\n", "from research.static_analysis.usage_analysis import ParsedFile\n", "from research.utils.inspect_indexed_dataset import highlight_special_tokens\n", "\n", "\n", "def print_data(code: str):\n", "    specials = [\n", "        \"<fim_prefix>\",\n", "        \"<fim_suffix>\",\n", "        \"<fim_middle>\",\n", "        \"<|pause|>\",\n", "        \"<|skip|>\",\n", "        \"<|endoftext|>\",\n", "        \"<|skip'\",\n", "        \"'|>\",\n", "    ]\n", "    print(highlight_special_tokens(code, specials, highlight_whitespace=True))\n", "\n", "\n", "sampler = CSTFimSampler()\n", "sampler.rng.seed(43)\n", "\n", "ex_file = AUGMENT_ROOT / \"base/static_analysis/usage_analysis.py\"\n", "ex_code = ex_file.read_text()\n", "pfile = ParsedFile.parse(ex_file, \"python\", ex_code)\n", "\n", "# let's sample 8 FIM instances and print them out\n", "n_probs = 30\n", "loggers = [[] for _ in range(n_probs)]\n", "fim_probs = [sampler.sample_fim(pfile, logs=loggers[i]) for i in range(n_probs)]\n", "\n", "for i, prob in enumerate(fim_probs):\n", "    print(f\"Problem {i}\")\n", "    if not prob:\n", "        print(f\"Failed to sample for problem {i}\")\n", "        continue\n", "    print_data(prob.show(prefix_limit=1000, suffix_limit=300))\n", "    print(\"-\" * 30, \"sampler logs below\", \"-\" * 30)\n", "    print(\"\\n\".join(loggers[i]))\n", "    print(\"=\" * 100)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can use `FimPromptFormatter` to convert FIM problems into token sequences."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.fim.fim_prompt import FimPromptFormatter\n", "\n", "formatter = FimPromptFormatter.for_star_coder()\n", "formatter.max_total_tks = 400\n", "formatter.max_middle_tks = 100\n", "formatter.min_suffix_tks = 100\n", "for i, prob in enumerate(fim_probs[:10]):\n", "    if not prob:\n", "        print(f\"-\" * 100)\n", "        print(f\"Failed to sample for problem {i}: {prob}\")\n", "        continue\n", "    formatted = formatter.format(prob, format_middle=True)\n", "    print(\"-\" * 50, f\"Problem {i}\", \"-\" * 50)\n", "    print(formatted.len_details())\n", "    print_data(formatter.tokenizer.detokenize(formatted.tokens))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's now run the FimSampler on the Augment codebase to get a sense of what the sampled distribution looks like."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from random import Random\n", "\n", "from tqdm import tqdm\n", "\n", "from research.static_analysis.common import guess_lang_from_fp\n", "from research.fim.fim_prompt import FormattedFimProblem\n", "from research.fim.fim_sampling import FimProblem, FimSamplingFailed\n", "from research.static_analysis.parsing import ParsingFailedError\n", "\n", "rng = Random(42)\n", "\n", "research_root = AUGMENT_ROOT / \"research\"\n", "python_files = list(research_root.glob(\"**/*.py\"))\n", "\n", "sampler = CSTFimSampler()\n", "sampler.rng = rng\n", "formatter = FimPromptFormatter.for_star_coder()\n", "all_sampled = list[FimProblem]()\n", "all_failed = list[FimSamplingFailed]()\n", "all_formatted = list[FormattedFimProblem]()\n", "for file in tqdm(python_files, smoothing=0):\n", "    code = file.read_text()\n", "    if code.strip() == \"\":\n", "        continue\n", "    lang = guess_lang_from_fp(file)\n", "    assert lang is not None\n", "    try:\n", "        pfile = ParsedFile.parse(file, lang, code)\n", "    except ParsingFailedError:\n", "        continue\n", "    samples, failures = sampler.sample_every_n_lines(\n", "        pfile, 200, max_problems_per_file=4\n", "    )\n", "    all_sampled.extend(samples)\n", "    all_failed.extend(failures)\n", "    for problem in samples:\n", "        formatted = formatter.format(problem, format_middle=True)\n", "        assert len(formatted.tokens) <= formatter.max_total_tks\n", "        all_formatted.append(formatted)\n", "\n", "print(f\"{len(all_sampled)=}\")\n", "print(f\"{len(all_failed)=}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "\n", "failed_counts = Counter([x.category for x in all_failed])\n", "\n", "for category, count in failed_counts.most_common(5):\n", "    print(f\"(n={count}) {category}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "\n", "\n", "def nonwhitespace_size(code: str):\n", "    return sum(not c.isspace() for c in code)\n", "\n", "\n", "def first_middle_size(prob: FimProblem):\n", "    return (\n", "        nonwhitespace_size(prob.middle_spans[0].content.code)\n", "        if prob.middle_spans\n", "        else 0\n", "    )\n", "\n", "\n", "def has_empty_suffix(prob: FimProblem) -> bool:\n", "    \"\"\"Whether the suffix is empty or only contains whitespaces.\"\"\"\n", "    chars = sum(not c.isspace() for c in prob.suffix.code)\n", "    return chars == 0\n", "\n", "\n", "len_dist = [first_middle_size(prob) for prob in all_sampled]\n", "total_len_dist = [\n", "    sum(\n", "        nonwhitespace_size(x.content.code) if not x.skipped else 1\n", "        for x in prob.middle_spans\n", "    )\n", "    for prob in all_sampled\n", "]\n", "\n", "skips_dist = [\n", "    sum(1 if x.skipped else 0 for x in prob.middle_spans) for prob in all_sampled\n", "]\n", "\n", "print(\"First middle length distribution:\")\n", "print(\"Average:\", sum(len_dist) / len(len_dist))\n", "print(\"Max:\", max(len_dist))\n", "display(Counter(len_dist).most_common(10))\n", "\n", "print(\"Total middle length distribution:\")\n", "print(\"Average:\", sum(total_len_dist) / len(total_len_dist))\n", "display(Counter(total_len_dist).most_common(10))\n", "\n", "print(\"Skips distribution:\")\n", "print(\"Average:\", sum(skips_dist) / len(skips_dist))\n", "display(Counter(skips_dist).most_common(10))\n", "\n", "type_counts = Counter([prob.middle_node_type for prob in all_sampled])\n", "print(\"Middle node type distribution:\")\n", "display(type_counts.most_common(10))\n", "\n", "print(\n", "    \"Empty suffix ratio:\",\n", "    sum(has_empty_suffix(prob) for prob in all_sampled) / len(all_sampled),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's look at samples that contain skip spans."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.fim.fim_prompt import FimPromptFormatter\n", "\n", "formatter = FimPromptFormatter.for_star_coder()\n", "formatter.max_total_tks = 400\n", "formatter.max_middle_tks = 200\n", "formatter.min_suffix_tks = 100\n", "\n", "\n", "def n_skips(prob: FimProblem):\n", "    return sum(1 for s in prob.middle_spans if s.skipped)\n", "\n", "\n", "skip_samples = [x for x in all_sampled if n_skips(x) > 0]\n", "\n", "for i, s in enumerate(skip_samples[3:10]):\n", "    print(\"=\" * 50, f\"Problem {i}\", \"=\" * 50)\n", "    print_data(s.show(prefix_limit=300, suffix_limit=200))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The Code below are for performance profiling and can be ignored for the tutorial."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%snakeviz -t\n", "\n", "from random import Random\n", "\n", "from tqdm import tqdm\n", "from base.static_analysis.parsing import ScopeTreeParser\n", "\n", "from research.static_analysis.common import guess_lang_from_fp\n", "from research.fim.fim_prompt import FormattedFimProblem\n", "from research.fim.fim_sampling import FimProblem, FimSamplingFailed\n", "from research.core.types import IntRange\n", "\n", "\n", "def profile_run() -> None:\n", "    rng = Random(42)\n", "    parser = ScopeTreeParser(parse_errored_root=True)\n", "\n", "    research_root = AUGMENT_ROOT / \"research\"\n", "    python_files = list(research_root.glob(\"**/*.py\"))\n", "    rng.shuffle(python_files)\n", "    python_files = python_files[:100]\n", "\n", "    sampler = CSTFimSampler()\n", "    sampler.rng = rng\n", "    formatter = FimPromptFormatter.for_star_coder()\n", "    all_sampled = list[FimProblem]()\n", "    all_failed = list[FimSamplingFailed]()\n", "    all_formatted = list[FormattedFimProblem]()\n", "    for file in tqdm(python_files, smoothing=0):\n", "        code = file.read_text()\n", "        if code.strip() == \"\":\n", "            continue\n", "        lang = guess_lang_from_fp(file)\n", "        assert lang is not None\n", "        pfile = ParsedFile.parse(file, lang, code, parser)\n", "        samples, failures = sampler.sample_every_n_lines(pfile, 100)\n", "        all_sampled.extend(samples)\n", "        all_failed.extend(failures)\n", "        for problem in samples:\n", "            formatted = formatter.format(problem, format_middle=True)\n", "            assert len(formatted.tokens) <= formatter.max_total_tks\n", "            all_formatted.append(formatted)\n", "\n", "    print(f\"{len(all_sampled)=}\")\n", "    print(f\"{len(all_failed)=}\")\n", "\n", "\n", "profile_run()\n", "# %lprun -f IntRange.intersect profile_run()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
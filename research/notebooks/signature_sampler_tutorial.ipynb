{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "from base.ranges.range_types import IntRange\n", "from research.fim.fim_prompt import JustInTimeLookupStrategy\n", "from research.utils.data_utils import read_src_repo\n", "from research.utils.generate_signature_data import SignatureDataProcessor\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "processor = SignatureDataProcessor(\n", "    seq_len_range=IntRange(1024 * 4, 1024 * 8),\n", "    usage_based_sampling_rate=1.0,\n", "    formatter_args={\"inline_lookup_strategy\": JustInTimeLookupStrategy()},\n", ")\n", "# read the augment repo\n", "repo = read_src_repo()\n", "problems = processor.repo_to_problems(\n", "    repo, seed=42, files_to_sample=100, is_training=True, tqdm_args={}\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.fim.fim_prompt import FormattedFimProblem\n", "\n", "pause_id = tokenizer.pause_id\n", "lookup_id = tokenizer.sig_lookup_id\n", "\n", "\n", "def is_first_span_inline_prob(prob: FormattedFimProblem) -> bool:\n", "    middle_tokens = list(prob.tokens[prob.middle_range.to_slice()])\n", "    if pause_id not in middle_tokens:\n", "        return False\n", "    pause_pos = middle_tokens.index(pause_id)\n", "    lookup_pos = middle_tokens.index(lookup_id)\n", "    return lookup_pos < pause_pos\n", "\n", "\n", "inline_probs = [p for p in problems if lookup_id in p.tokens[p.middle_range.to_slice()]]\n", "print(f\"{len(problems)=}\")\n", "print(f\"Inline lookup rate: {len(inline_probs) / len(problems):.1%}\")\n", "\n", "first_span_inline_probs = [p for p in inline_probs if is_first_span_inline_prob(p)]\n", "print(\n", "    f\"First span inline lookup rate: {len(first_span_inline_probs) / len(inline_probs):.1%}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i, prob in enumerate(problems[:8]):\n", "    print(f\"Problem {i}\")\n", "    prob.pprint(tokenizer)\n", "    print(\"=\" * 100)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
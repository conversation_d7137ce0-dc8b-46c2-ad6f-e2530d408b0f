{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Tokenizer Playground\n", "\n", "A notebook for loading and evaluating tokenizers."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.tokenizer.tokenizer import CodeGenTokenizer\n", "from transformers import AutoTokenizer\n", "\n", "# This is the default tokenizer we use for code,\n", "# based on the GPT2 tokenizer\n", "codegen_tokenizer = CodeGenTokenizer()\n", "\n", "# Load a separately trained tokenizer\n", "# custom_tokenizer = AutoTokenizer.from_pretrained(PATH)\n", "\n", "# It's a bit easier to work with the encapsulated tokenizer\n", "tokenizer = codegen_tokenizer.tokenizer\n", "\n", "\n", "# Vocabulary:\n", "print(f\"Vocabulary size is {len(tokenizer.vocab)}\")\n", "\n", "# Find the special vocabulary tokens of the form <|...|>\n", "# (there should be just one)\n", "print(\"\\nSpecial <|..|> tokens:\")\n", "for t in tokenizer.vocab:\n", "    if t.startswith('<|') and t.endswith('|>'):\n", "        print(t, tokenizer.vocab[t])\n", "\n", "text = \"\"\"def foo():\n", "   print(\"Hello, <PERSON>!\")\n", "\"\"\"\n", "\n", "\n", "# Encoding and decoding:\n", "\n", "# tokenize() returns the token strings, encode() returns the token IDs\n", "tokens = tokenizer.tokenize(text)\n", "token_ids = tokenizer.encode(text)\n", "decoded = tokenizer.decode(token_ids)\n", "\n", "print(\"Text:\")\n", "print(text)\n", "\n", "print(\"Tokens:\")\n", "print(tokens)\n", "\n", "print(\"\\nToken IDs:\")\n", "print(token_ids)\n", "\n", "print(\"\\nDecoded text matches original text?\")\n", "assert decoded == text\n", "print(\"Yes!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "31f2aee4e71d21fbe5cf8b01ff0e069b9275f58929596ceb00d14d90e3e16cd6"}}}, "nbformat": 4, "nbformat_minor": 2}
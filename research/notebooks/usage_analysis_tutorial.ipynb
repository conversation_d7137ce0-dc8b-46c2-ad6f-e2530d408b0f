{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Usage Analysis Tutorial\n", "\n", "This notebook illustrates how to use the `UsageIndex` class to perform (project-wise) usage analysis on source files.\n", "\n", "## Analyzing a toy codebase\n", "\n", "Suppose we have the following 2 example Python source files, the first step to perform usage analysis is to build a `UsageIndex` from these 2 files."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "%load_ext snakeviz\n", "%load_ext line_profiler\n", "\n", "\n", "from pathlib import Path\n", "from research.static_analysis.parsing import ScopeTreeParser\n", "from research.static_analysis.usage_analysis import ParsedFile, UsageIndex\n", "\n", "\n", "ex_file1 = \"\"\"\n", "def double(x: int):\n", "    return 2 * x\n", "\n", "def bar():\n", "    pass\n", "\n", "class A:\n", "    def __init__(x: int):\n", "        self.x = x\n", "\n", "    def foo():\n", "        pass\n", "\n", "    def bar(y: int):\n", "        return x + double(y)\n", "\n", "\"\"\"\n", "\n", "ex_file2 = \"\"\"\n", "from file1 import double, Foo\n", "\n", "x = A(5).bar(4)\n", "double(x)\n", "\n", "def main(bar: A):\n", "    return double(bar.foo())\n", "\"\"\"\n", "\n", "# we first parse the source files into a list of ParsedFile objects\n", "all_summaries = [\n", "    ParsedFile.parse(Path(\"file1.py\"), \"python\", ex_file1),\n", "    ParsedFile.parse(Path(\"file2.py\"), \"python\", ex_file2),\n", "]\n", "# we then build a UsageIndex\n", "index = UsageIndex.from_files(all_summaries)\n", "index.pprint()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We see that the `UsageIndex` has recorded all globally visible variables, functions, and classes defined inside this (tiny) codebase. We can now compute the usages of each file using the `UsageIndex.resolve_file_usages` method, which will return a dictionary that maps each call site to its potential defintions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# first file usages\n", "index.resolve_file_usages(all_summaries[0]).site2defs"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The above result says that there is a usage of the 'double' symbol at byte range 184:190 that is potentially referring to the 'double' function definition at file1.py:1.\n", "\n", "Now let's also see the usages in the second file."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# second file usages\n", "index.resolve_file_usages(all_summaries[1]).site2defs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that usages of locally defined definitions (e.g., `x`) are not reported."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Updating Usage Index\n", "\n", "`UsageIndex` also supports efficient updating via the `update_file` and `remove_file` methods. Let's add a new file then see how the usage analysis result will get updated:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ex_file3 = \"\"\"\n", "class B:\n", "    def foo(self):\n", "        # yet another foo\n", "        pass\n", "\"\"\"\n", "\n", "index.update_file(ParsedFile.parse(Path(\"file3.py\"), \"python\", ex_file3))\n", "index.resolve_file_usages(all_summaries[1]).site2defs"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["You can see that the last '.foo' usage now points to one more potential definition.\n", "\n", "## Analyzing the Augment codebase\n", "\n", "Now for something more realistic, let's run usage analysis on the entirety of the Augment codebase :)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext snakeviz\n", "%load_ext line_profiler"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%snakeviz -t\n", "\n", "import time\n", "from typing import Collection\n", "from tqdm import tqdm\n", "\n", "from research.utils.data_utils import read_src_repo, AUGMENT_ROOT\n", "from research.static_analysis.common import LanguageID\n", "from research.static_analysis.usage_analysis import FileSummary, local_usage_analysis\n", "\n", "\n", "def index_repo(\n", "    langs: Collection[LanguageID], ignore_dirs: Collection[str], repo_root: Path\n", "):\n", "    \"\"\"Build an index from all files under the `static_analysis` directory\"\"\"\n", "    parser = ScopeTreeParser(parse_errored_root=True)\n", "\n", "    files_to_parse = read_src_repo(langs, ignore_dirs, repo_root=repo_root).files\n", "    start_time = time.time()\n", "    parsed = [\n", "        ParsedFile.parse(file.path, file.lang, file.code, parser=parser)\n", "        for file in tqdm(files_to_parse, desc=\"Parsing\")\n", "    ]\n", "    summaries = [\n", "        FileSummary.from_pfile(pfile) for pfile in tqdm(parsed, desc=\"Summarizing\")\n", "    ]\n", "    t2 = time.time()\n", "    print(f\"Parsing + summarizing took: {t2 - start_time:.1f}s\")\n", "    index = UsageIndex.from_files(summaries)\n", "    print(f\"Building the index took: {time.time() - t2:.1f}s\")\n", "    return summaries, parsed, index"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repo_root = AUGMENT_ROOT\n", "languages = (\"python\",)\n", "print(f\"{languages=}\")\n", "all_summaries, all_parsed, index = index_repo(languages, ignore_dirs=(), repo_root=repo_root)\n", "print(f\"{len(all_summaries)=}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.static_analysis.usage_analysis import UsageIndex\n", "\n", "def test_resolve_all_files():\n", "    t_start = time.time()\n", "    all_usages = {s.path: index.resolve_file_usages(s) for s in tqdm(all_summaries)}\n", "    print(f\"resolve_file_usages took: {time.time() - t_start:.1f}s\")\n", "\n", "# %lprun -f UsageIndex.resolve_file_usages test_resolve_all_files()\n", "# %snakeviz -t test_resolve_all_files()\n", "# %snakeviz -t test_resolve_all_files()\n", "test_resolve_all_files()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can print out all usages inside a given file. \n", "See also [signature_index_example.ipynb](research/notebooks/signature_index_example.ipynb) for an example of how to generate signatures of used definitions using a signature index (which builds on top of a usage index)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.static_analysis.parsing import LineMap\n", "\n", "path_to_show = Path(\"base/static_analysis/usage_analysis.py\")\n", "path2summary = {s.path: s for s in all_summaries}\n", "path2parsed = {f.path: f for f in all_parsed}\n", "max_usages_per_site = 5\n", "\n", "print(\"caller line (caller range): potential callees\")\n", "print(\"-\" * 50)\n", "lmap = LineMap(path2parsed[path_to_show].code)\n", "file_usages = index.resolve_file_usages(path2summary[path_to_show])\n", "for name, defs in file_usages.site2defs.items():\n", "    line = lmap.get_line_number(name.use_site.start) + 1\n", "    print(f\"line {line} ({str(name.use_site)}):\")\n", "    for def_ in defs[:max_usages_per_site]:\n", "        print(f\"  {def_}\")\n", "    if len(defs) > max_usages_per_site:\n", "        print(f\"  ... and {len(defs) - max_usages_per_site} more\")"]}], "metadata": {"kernelspec": {"display_name": "static-analysis-UqAzVeut-py3.9", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
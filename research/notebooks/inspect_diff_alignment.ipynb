{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["code_before = '''\\\n", "\"\"\"Unit tests for update_versions script used for vscode publishing.\"\"\"\n", "\n", "import json\n", "import unittest\n", "\n", "from update_versions import (\n", "    ExtensionVersions,\n", "    <PERSON><PERSON><PERSON>,\n", "    get_new_version,\n", "    get_sorted_versions,\n", "    get_stable_release_ref,\n", "    versions_from_vsce_data,\n", ")\n", "\n", "\n", "class TestSemver(unittest.TestCase):\n", "    \"\"\"Semver parsing.\"\"\"\n", "\n", "    def test_is_semver_string(self):\n", "        self.assertEqual(Semver.is_semver_string(\"0.0.0\"), True)\n", "        self.assertEqual(Semver.is_semver_string(\"1.12.123\"), True)\n", "        self.assertEqual(Semver.is_semver_string(\"Nope\"), False)\n", "        self.assertEqual(Semver.is_semver_string(\"\"), False)\n", "\n", "    def test_from_string(self):\n", "        self.assertEqual(Semver.from_string(\"0.0.0\"), Semver(0, 0, 0))\n", "        self.assertEqual(Semver.from_string(\"1.12.123\"), <PERSON><PERSON><PERSON>(1, 12, 123))\n", "\n", "    def test_from_string_error(self):\n", "        with self.assertRaises(SystemError) as context:\n", "            Semver.from_string(\"nope\")\n", "\n", "        self.assertTrue(\"Failed to parse version\" in str(context.exception))\n", "\n", "    def test_lt(self):\n", "        self.assertTrue(Semver(0, 0, 0) < Semver(0, 0, 1))\n", "        self.assertTrue(Semver(0, 0, 0) < Semver(0, 1, 0))\n", "        self.assertTrue(Semver(0, 0, 0) < Semver(1, 0, 0))\n", "        self.assertTrue(Semver(0, 0, 1) < Semver(0, 0, 2))\n", "        self.assertTrue(<PERSON><PERSON><PERSON>(0, 1, 1) < Semver(0, 2, 2))\n", "        self.assertTrue(<PERSON><PERSON><PERSON>(1, 1, 1) < <PERSON><PERSON><PERSON>(2, 2, 2))\n", "        self.assertTrue(Se<PERSON>ver(1, 1, 1) < Semver(2, 0, 0))\n", "\n", "    def test_eq(self):\n", "        self.assertTrue(Se<PERSON>ver(0, 0, 0) == Semver(0, 0, 0))\n", "        self.assertTrue(Se<PERSON>ver(0, 0, 1) == <PERSON><PERSON>ver(0, 0, 1))\n", "        self.assertTrue(Se<PERSON>ver(0, 1, 0) == <PERSON><PERSON>ver(0, 1, 0))\n", "        self.assertTrue(Se<PERSON>ver(1, 0, 0) == <PERSON>mver(1, 0, 0))\n", "        self.assertTrue(<PERSON><PERSON><PERSON>(1, 2, 3) == <PERSON><PERSON><PERSON>(1, 2, 3))\n", "\n", "    def test_increment(self):\n", "        self.assertEqual(Semver(0, 0, 0).increment(\"patch\"), Semver(0, 0, 1))\n", "        self.assertEqual(Semver(0, 0, 0).increment(\"minor\"), Se<PERSON>ver(0, 1, 0))\n", "        self.assertEqual(Semver(0, 0, 0).increment(\"major\"), Semver(1, 0, 0))\n", "\n", "    def test_increment_err(self):\n", "        with self.assertRaises(SystemError) as context:\n", "            Semver(0, 0, 0).increment(\"other\")\n", "\n", "        self.assertTrue(\"Unknown update type\" in str(context.exception))\n", "\n", "    def test_get_channel(self):\n", "        self.assertEqual(Semver(0, 0, 0).get_channel(), \"prerelease\")\n", "        self.assertEqual(Semver(0, 0, 1).get_channel(), \"stable\")\n", "        self.assertEqual(Semver(0, 0, 2).get_channel(), \"stable\")\n", "\n", "\n", "class TestExtensionVersions(unittest.TestCase):\n", "    \"\"\"Store and update the marketplace stable + prerelease versions.\"\"\"\n", "\n", "    def test_update(self):\n", "        ev = ExtensionVersions(\n", "            <PERSON><PERSON><PERSON>(0, 0, 1),\n", "            <PERSON><PERSON><PERSON>(0, 1, 0),\n", "        )\n", "        ev.update(\"stable\")\n", "        self.assertEqual(ev.stable, <PERSON><PERSON><PERSON>(0, 0, 2))\n", "        self.assertEqual(ev.prerelease, <PERSON><PERSON><PERSON>(0, 1, 0))\n", "\n", "        ev.update(\"prerelease\")\n", "        self.assertEqual(ev.stable, <PERSON><PERSON><PERSON>(0, 0, 2))\n", "        self.assertEqual(ev.prerelease, <PERSON><PERSON><PERSON>(0, 2, 0))\n", "\n", "    def test_update_err(self):\n", "        with self.assertRaises(SystemError) as context:\n", "            ExtensionVersions(\n", "                <PERSON><PERSON><PERSON>(0, 0, 1),\n", "                <PERSON><PERSON><PERSON>(0, 1, 0),\n", "            ).update(\"other\")\n", "\n", "        self.assertTrue(\"Unknown channel\" in str(context.exception))\n", "'''"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["code_after = '''\\\n", "\"\"\"Unit tests for update_versions script used for vscode publishing.\"\"\"\n", "\n", "import json\n", "import pytest\n", "\n", "from update_versions import (\n", "    ExtensionVersions,\n", "    <PERSON><PERSON><PERSON>,\n", "    get_new_version,\n", "    get_sorted_versions,\n", "    get_stable_release_ref,\n", "    versions_from_vsce_data,\n", ")\n", "\n", "\n", "def test_is_semver_string():\n", "    \"\"\"Semver parsing.\"\"\"\n", "    assert Semver.is_semver_string(\"0.0.0\") is True\n", "    assert Semver.is_semver_string(\"1.12.123\") is True\n", "    assert Semver.is_semver_string(\"Nope\") is False\n", "    assert Semver.is_semver_string(\"\") is False\n", "\n", "def test_from_string():\n", "    assert Semver.from_string(\"0.0.0\") == Semver(0, 0, 0)\n", "    assert Semver.from_string(\"1.12.123\") == Semver(1, 12, 123)\n", "\n", "def test_from_string_error():\n", "    with pytest.raises(SystemError):\n", "        Semver.from_string(\"nope\")\n", "\n", "def test_lt():\n", "    assert Semver(0, 0, 0) < Semver(0, 0, 1)\n", "    assert Semver(0, 0, 0) < Semver(0, 1, 0)\n", "    assert Semver(0, 0, 0) < Semver(1, 0, 0)\n", "    assert Semver(0, 0, 1) < Semver(0, 0, 2)\n", "    assert Semver(0, 1, 1) < Semver(0, 2, 2)\n", "    assert Semver(1, 1, 1) < Semver(2, 2, 2)\n", "    assert Semver(1, 1, 1) < Semver(2, 0, 0)\n", "\n", "def test_eq():\n", "    assert Semver(0, 0, 0) == Semver(0, 0, 0)\n", "    assert Semver(0, 0, 1) == Semver(0, 0, 1)\n", "    assert Semver(0, 1, 0) == Semver(0, 1, 0)\n", "    assert Semver(1, 0, 0) == Semver(1, 0, 0)\n", "    assert Semver(1, 2, 3) == Semver(1, 2, 3)\n", "\n", "def test_increment():\n", "    assert Semver(0, 0, 0).increment(\"patch\") == Semver(0, 0, 1)\n", "    assert Semver(0, 0, 0).increment(\"minor\") == Semver(0, 1, 0)\n", "    assert Semver(0, 0, 0).increment(\"major\") == Semver(1, 0, 0)\n", "\n", "def test_increment_err():\n", "    with pytest.raises(SystemError):\n", "        Semver(0, 0, 0).increment(\"other\")\n", "\n", "def test_get_channel():\n", "    assert Semver(0, 0, 0).get_channel() == \"prerelease\"\n", "    assert Semver(0, 0, 1).get_channel() == \"stable\"\n", "    assert Semver(0, 0, 2).get_channel() == \"stable\"\n", "\n", "\n", "class TestExtensionVersions(unittest.TestCase):\n", "    \"\"\"Store and update the marketplace stable + prerelease versions.\"\"\"\n", "\n", "    def test_update(self):\n", "        ev = ExtensionVersions(\n", "            <PERSON><PERSON><PERSON>(0, 0, 1),\n", "            <PERSON><PERSON><PERSON>(0, 1, 0),\n", "        )\n", "        ev.update(\"stable\")\n", "        self.assertEqual(ev.stable, <PERSON><PERSON><PERSON>(0, 0, 2))\n", "        self.assertEqual(ev.prerelease, <PERSON><PERSON><PERSON>(0, 1, 0))\n", "\n", "        is this a replace?\n", "\n", "    def test_update_err(self):\n", "        with self.assertRaises(SystemError) as context:\n", "            ExtensionVersions(\n", "                <PERSON><PERSON><PERSON>(0, 0, 1),\n", "                <PERSON><PERSON><PERSON>(0, 1, 0),\n", "            ).update(\"other\")\n", "\n", "        self.assertTrue(\"Unknown channel\" in str(context.exception))\n", "'''"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.str_diff import print_diff, precise_line_diff\n", "\n", "\n", "diff = precise_line_diff(code_before, code_after)\n", "print_diff(diff)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
#!/bin/bash

set -eu -o pipefail

declare -r REPO_ROOT="$(readlink -f "$(dirname "$0")/..")"
declare -r PIP_INDEX="https://us-central1-python.pkg.dev/system-services-dev/pypi-public/simple"
declare -ra pip_install=(pip install --extra-index-url "$PIP_INDEX")
declare -i DRY_RUN=0

usage() {
	cat <<-EOF
	Usage:
	  $0 [-r requirements.txt] [--gpu | --cpu] [--reqs-only] [-e|--exclude package] [--no-pre-commit] [--dry-run]

	Augment repo initialization script. Run this on a fresh system after cloning the
	'augment.git' repo. There main steps are:

	1) Run 'pip install -r requirements.txt'. This only reads from the repo,
	   and modifies the system or user python site-packages. With '--reqs-only',
	   only perform this step, which is useful in a Dockerfile.

	2) Run 'bazel //base:install'. This adds generated content to the repo (e.g.,
	   proto generated python libraries).

	3) Run 'pip install -e'. This sets up repo packages in "development mode".

	4) If the repo is a git repo (has a .git directory), run 'pre-commit install'.

	The behavior of (1) is slightly different for GPU and CPU systems. Normally
	the system type is auto-detected. Use --gpu or --cpu to override.

	Options:
	  -r <file>        Override the default \$AUGMENT/research/requirements.txt.
	  --gpu | --cpu    Override GPU/CPU detection. Only one may be set at a time.
	  --reqs-only      Only perform the pip install of requirements.txt, useful in Dockerfile.
	  -e|--exclude=pkg Exclude a package from the requirements.txt. Can be specified multiple times.
	  --no-pre-commit  Do not attempt to install pre-commit in a git repo.
	  --dry-run -n     Dry-Run, print commands that would be run.
	EOF
}


info() {
	declare -r fmt="$1"
	shift
	printf "[research-init.sh] $fmt\n" "$@"
}

run() {
	if (( DRY_RUN )); then
		info "Not Running (dry-run): %s" "$*"
	else
		info "Running: %s" "$*"
		"$@"
	fi
}

main() {
	declare req_file="$(readlink -f "$(dirname "$0")")/requirements.txt"
	declare -a exclude=()
	declare -i gpu=0 cpu=0 reqs_only=0 pre_commit=1
	eval set -- $(getopt -n "$0" -o 'r:nh' --long 'gpu,cpu,reqs-only,exclude:,no-pre-commit,dry-run,help' -- "$@")

	while true; do
		case "$1" in
		--) shift; break ;;
		-r) req_file="$2"; shift 2; continue ;;
		--gpu) gpu=1; shift; continue ;;
		--cpu) cpu=1; shift; continue ;;
		--reqs-only) reqs_only=1; shift; continue ;;
		-e|--exclude) exclude+=("$2"); shift 2; continue ;;
		--no-pre-commit) pre_commit=0; shift; continue ;;
		-n|--dry-run) DRY_RUN=1; shift; continue ;;
		-h|--help) usage; return 0 ;;
		*)
			printf "Invalid Option: %s\n" "$1"
			usage
			return 1
		;;
		esac
	done
	declare -r req_file reqs_only exclude pre_commit

	if (( $# )); then
		info "No positional args expected."
		return 1
	fi
	if (( gpu && cpu )); then
		info "Cannot set both --gpu and --cpu."
		return 1
	elif (( !gpu && !cpu )); then
		info "Autodetecting CPU or GPU system..."
		if detect_gpu; then
			info "GPU detected."
			gpu=1
		else
			info "CPU detected."
			cpu=1
		fi
	fi

	declare -r gpu cpu

	if (( gpu )); then
		info "Installing GPU Requirements"
		init_requirements_gpu "$req_file" "${exclude[@]}"
	else
		info "Installing CPU Requirements"
		init_requirements_cpu "$req_file" "${exclude[@]}"
	fi
	if (( reqs_only )); then
		return
	fi
	(
		run cd "$REPO_ROOT"
		init_base_install
		init_pip_local_repo
		install_pre_commit "$pre_commit"
	)
}

detect_gpu() {
	nvidia-smi >/dev/null 2>&1
}

init_requirements_gpu() {
	declare -r req_file="$1"; shift
	declare -r exclude="$(sed 's# #\\|#g' <<<"$@")"
	run "${pip_install[@]}" -r <(grep -v -e "${exclude:-__EXCLUDE__}" "$req_file")
}

init_requirements_cpu() {
	declare -r req_file="$1"; shift
	declare -r exclude="$(sed 's# #\\|#g' <<<"$@")"
	declare -r first_skip="deepspeed\|mpi4py\|transformer-engine\|flash-attn\|megablocks\|triton"
	declare -r second_only="deepspeed"
	info "Install 1/2: Without: %s" "$first_skip"
	run "${pip_install[@]}" -r <(grep -v -e "$first_skip" -e "${exclude:-__EXCLUDE__}" "$req_file")
	info "Install 2/2: Only: %s" "$second_only"
	run "${pip_install[@]}" -r <(grep "$second_only" "$req_file")
}

init_base_install() {
	declare -r cmd1=(bazel run -c opt //base:install)
	declare -r cmd2=(bazel run -c opt //tools/generate_proto_typestubs)
	if (( $(id -u) == 0 )); then
		info "WARNING: Cannot run bazel as root, trying as 'augment'..."
		run sudo -u augment "${cmd1[@]}"
		run sudo -u augment "${cmd2[@]}"
	else
		run "${cmd1[@]}"
		run "${cmd2[@]}"
	fi
}

init_pip_local_repo() {
	run "${pip_install[@]}" --no-deps -e .
}

install_pre_commit() {
	declare -ri do_install="$1"
	declare -r git_dir="$REPO_ROOT/.git"
	declare -r hook="$git_dir/hooks/pre-commit"

	if [[ ! -d "$git_dir" ]]; then
		info "Not a git repo (%s): so not installing pre-commit." "$git_dir"
		return
	fi

	if [[ -e "$hook" ]]; then
		info "The pre-commit hook (%s) already exists." "$hook"
		return
	fi

	if (( do_install == 0 )); then
		info "WARNING: 'pre-commit' is not installed, and you've disabled with --no-pre-commit"
		return
	fi

	if ! type pre-commit >/dev/null 2>&1; then
		info "WARNING: The 'pre-commit' binary is not installed or not in your PATH."
		return
	fi

	info "Installing pre-commit."
	run pre-commit install
}

main "$@"

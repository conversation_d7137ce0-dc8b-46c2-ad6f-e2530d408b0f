#!/usr/bin/env python3
"""Python library for using the local docker registry.

Can be invoked as a script, in which case, it will list all images and
associated tags from the local docker registry.
"""

import logging
import sys

from research.environments import get_provider

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def main():
    prefix = None
    if len(sys.argv) > 1:
        prefix = sys.argv[1]
    images = get_provider().get_images_and_tags(prefix)
    for img_tup in images:
        reg = img_tup[0]
        tags = img_tup[1]
        for tag in tags:
            print(f"{reg}:{tag}")


if __name__ == "__main__":
    main()

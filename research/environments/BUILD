load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")

py_library(
    name = "environments",
    srcs = [
        "__init__.py",
        "gcp_sync.py",
        "providers.py",
    ],
    data = [
        "node_mounts.yaml",
        ":image_tags",
    ],
    visibility = ["//research:__subpackages__"],
    deps = [
        "//research/core:constants",
        "//research/infra/cfg/clusters:clusters_py",
        requirement("kubernetes"),
    ],
)

filegroup(
    name = "image_tags",
    srcs = glob(["*_tag.txt"]),
    visibility = ["//research/infra:internal"],
)

go_library(
    name = "tags_go",
    srcs = ["tags.go"],
    embedsrcs = [":image_tags"],
    importpath = "github.com/augmentcode/augment/research/environments",
    visibility = ["//research/infra:internal"],
)

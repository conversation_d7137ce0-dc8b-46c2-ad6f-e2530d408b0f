ARG BASE_IMG
FROM $BASE_IMG AS augment_base

USER root

# Items for the CPU only image
COPY --chown=root:root --chmod=0644 requirements.txt /root/
COPY --chown=root:root --chmod=0755 research-init.sh /root/
RUN --mount=type=cache,target=/root/.cache /root/research-init.sh --cpu --reqs-only -r /root/requirements.txt

# NOTE(apex-cpu):
# The Nvidia apex library is only installable from source and only usable with a GPU.
# However, our CPU images may sometimes import code that itself imports from apex -- for
# example, when running pylint. The GPU install of apex occurs in the dai_environments GPU
# base image, so we put a CPU-only install into the base CPU image here as a hack.
# Switch to a different build stage to not pollute the image with extra layers.
FROM augment_base as apex_builder

# This hash is the latest as of 01/25/24
ARG APEX_HASH=141bbf1cf362d4ca4d94f4284393e91dda5105a5
RUN git clone https://github.com/NVIDIA/apex.git /tmp/apex
WORKDIR /tmp/apex
RUN git checkout ${APEX_HASH}
# The setup.py script attempts to run `nvcc` unconditinally to get a CUDA version, even
# though that value is unused in the CPU-only install case. We comment out those calls.
RUN sed -i.bak "/= get_cuda_bare_metal_version/s/^/#/" setup.py
# Build a wheel file
RUN pip wheel -v --disable-pip-version-check --no-build-isolation --no-cache-dir .
ARG APEX_VERSION=0.1+cpu

# Switch back to the main build stage and copy over the wheel file to install it
FROM augment_base as augment_base-2
COPY --from=apex_builder /tmp/apex/apex-0.1-py3-none-any.whl /tmp/apex-0.1-py3-none-any.whl
RUN --mount=type=cache,target=/root/.cache : \
 && pip install --force-reinstall /tmp/apex-0.1-py3-none-any.whl \
 && rm -f /tmp/apex-0.1-py3-none-any.whl
# End apex install

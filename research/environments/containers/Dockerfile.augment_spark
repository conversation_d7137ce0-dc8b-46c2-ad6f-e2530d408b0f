ARG BASE_IMG
FROM $BASE_IMG AS augment_base

USER root

RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && DEBIAN_FRONTEND=noninteractive \
 && apt-get update \
 && apt-get install -y default-jre tini wget zip

# Set the Spark, Hadoop, and Scala versions (note SPARK_VERSION is usually passed in from build invocation).
# NOTE: Apache pulls older patch versions from their mirrors. This is annoying but it should be safe to lookup
# the latest and switch if we hit sudden build failures.
ARG SPARK_VERSION=3.4.3
ARG SPARK_SHA512SUM=1ddaa98ab93e05ee1d369f4db9494884cca0f7f711058acf24970b389b0c1bdd04239fb47990827df06561609e4acc5f69475d2ab0ccf8ee8c5cfe2e6e96b5fb
ARG HADOOP_VERSION=3.3.6
ARG HADOOP_SHA512SUM=de3eaca2e0517e4b569a88b63c89fae19cb8ac6c01ff990f1ff8f0cc0f3128c8e8a23db01577ca562a0e0bb1b4a3889f8c74384e609cd55e537aada3dcaa9f8a
ARG SCALA_VERSION=2.12.14
ARG SCALA_SHA256SUM=ad80eb4a83923c08a2128386850dbd1fde45787c54b4166b0c5ceae402624389

# Scala (https://www.scala-lang.org/download/all.html)
RUN --mount=type=cache,target=/root/dl : \
 && SCALA_DEB="scala-$SCALA_VERSION.deb" \
 && SCALA_URL="https://downloads.lightbend.com/scala/$SCALA_VERSION/$SCALA_DEB" \
 && SCALA_DL="/root/dl/$SCALA_DEB" \
 && test -e "$SCALA_DL" || curl -fsSL "$SCALA_URL" -o "$SCALA_DL" \
 && printf "%s %s\n" "$SCALA_SHA256SUM" "$SCALA_DL" | sha256sum -c \
 && dpkg -i "$SCALA_DL"

# Spark (https://spark.apache.org/downloads.html)
RUN --mount=type=cache,target=/root/dl --mount=type=bind,from=home-cache,target=/root/home-cache : \
 && SPARK_TGZ="spark-$SPARK_VERSION-bin-hadoop${HADOOP_VERSION%%.*}.tgz" \
 && SPARK_URL="https://dlcdn.apache.org/spark/spark-$SPARK_VERSION/$SPARK_TGZ" \
 && SPARK_DL="/root/dl/$SPARK_TGZ" \
 #&& test -e "$SPARK_TGZ" || curl -fsSL "$SPARK_URL" -o "$SPARK_DL" \
 && test -e "$SPARK_TGZ" || cp /root/home-cache/"$SPARK_TGZ" "$SPARK_DL" \
 && printf "%s %s\n" "$SPARK_SHA512SUM" "$SPARK_DL" | sha512sum -c \
 && mkdir /opt/spark \
 && tar -xz --no-same-owner --strip-components=1 -C /opt/spark -f "$SPARK_DL"

# Hadoop -- Native Libs (https://hadoop.apache.org/releases.html)
RUN --mount=type=cache,target=/root/dl : \
 && HADOOP_TGZ="hadoop-$HADOOP_VERSION.tar.gz" \
 && HADOOP_URL="https://dlcdn.apache.org/hadoop/common/hadoop-$HADOOP_VERSION/$HADOOP_TGZ" \
 && HADOOP_DL="/root/dl/$HADOOP_TGZ" \
 && HADOOP_NATIVE_DIR="hadoop-$HADOOP_VERSION" \
 && test -e "$HADOOP_TGZ" || curl -fsSL "$HADOOP_URL" -o "$HADOOP_DL" \
 && printf "%s %s\n" "$HADOOP_SHA512SUM" "$HADOOP_DL" | sha512sum -c \
 && test -d "$HADOOP_NATIVE_DIR" | tar -xzf "$HADOOP_DL" --no-same-owner -C /root/dl/ "$HADOOP_NATIVE_DIR"/lib/native --exclude='*/examples' \
 && cp -a /root/dl/"$HADOOP_NATIVE_DIR"/lib/native/* /usr/local/lib/

# Jars (https://mvnrepository.com)

RUN --mount=type=cache,target=/root/dl : \
 && VER="$HADOOP_VERSION" MD5=1c7173ee9549cbb93f29bce1c5d69198 \
 && URL=https://repo1.maven.org/maven2/org/apache/hadoop/hadoop-aws/"$VER"/hadoop-aws-"$VER".jar \
 && DL="/root/dl/${URL##*/}" \
 && test -e "$DL" || curl -fsSL "$URL" -o "$DL" \
 && printf "%s %s\n" "$MD5" "$DL" | md5sum -c \
 && install -oroot -groot -m0644 "$DL" /opt/spark/jars/

RUN --mount=type=cache,target=/root/dl : \
 && VER="42.6.2" MD5=df1396568d41231c090715a7ffa42b06 \
 && URL=https://repo1.maven.org/maven2/org/postgresql/postgresql/"$VER"/postgresql-"$VER".jar \
 && DL="/root/dl/${URL##*/}" \
 && test -e "$DL" || curl -fsSL "$URL" -o "$DL" \
 && printf "%s %s\n" "$MD5" "$DL" | md5sum -c \
 && install -oroot -groot -m0644 "$DL" /opt/spark/jars/

RUN --mount=type=cache,target=/root/dl : \
 && VER="1.12.778" MD5=8368ea8845f494ca2d8bda41506f9d77 \
 && URL=https://repo1.maven.org/maven2/com/amazonaws/aws-java-sdk-bundle/"$VER"/aws-java-sdk-bundle-"$VER".jar \
 && DL="/root/dl/${URL##*/}" \
 && test -e "$DL" || curl -fsSL "$URL" -o "$DL" \
 && printf "%s %s\n" "$MD5" "$DL" | md5sum -c \
 && install -oroot -groot -m0644 "$DL" /opt/spark/jars/

RUN --mount=type=cache,target=/root/dl : \
 && VER="2.4.0" MD5=65b8dec752d4984b7958d644848e3978 \
 && URL=https://repo1.maven.org/maven2/io/delta/delta-core_${SCALA_VERSION%.*}/"$VER"/delta-core_${SCALA_VERSION%.*}-"$VER".jar \
 && DL="/root/dl/${URL##*/}" \
 && test -e "$DL" || curl -fsSL "$URL" -o "$DL" \
 && printf "%s %s\n" "$MD5" "$DL" | md5sum -c \
 && install -oroot -groot -m0644 "$DL" /opt/spark/jars/

RUN --mount=type=cache,target=/root/dl : \
 && VER="2.4.0" MD5=a83011a52c66e081d4f53a7dc5c9708a \
 && URL=https://repo1.maven.org/maven2/io/delta/delta-storage/"$VER"/delta-storage-"$VER".jar \
 && DL="/root/dl/${URL##*/}" \
 && test -e "$DL" || curl -fsSL "$URL" -o "$DL" \
 && printf "%s %s\n" "$MD5" "$DL" | md5sum -c \
 && install -oroot -groot -m0644 "$DL" /opt/spark/jars/

RUN --mount=type=cache,target=/root/dl : \
 && VER="2.2.25" MD5=3f596585a6059a7755a7251b2104a936 \
 && URL=https://repo1.maven.org/maven2/com/google/cloud/bigdataoss/gcs-connector/hadoop"${HADOOP_VERSION%%.*}"-"$VER"/gcs-connector-hadoop"${HADOOP_VERSION%%.*}"-"$VER"-shaded.jar \
 && DL="/root/dl/${URL##*/}" \
 && test -e "$DL" || curl -fsSL "$URL" -o "$DL" \
 && printf "%s %s\n" "$MD5" "$DL" | md5sum -c \
 && install -oroot -groot -m0644 "$DL" /opt/spark/jars/

RUN --mount=type=cache,target=/root/dl : \
 && VER="0.39.1" MD5=b085a12221a914b0f986661e398da685 \
 && URL=https://repo1.maven.org/maven2/com/google/cloud/spark/spark-bigquery-with-dependencies_${SCALA_VERSION%.*}/"$VER"/spark-bigquery-with-dependencies_${SCALA_VERSION%.*}-"$VER".jar \
 && DL="/root/dl/${URL##*/}" \
 && test -e "$DL" || curl -fsSL "$URL" -o "$DL" \
 && printf "%s %s\n" "$MD5" "$DL" | md5sum -c \
 && install -oroot -groot -m0644 "$DL" /opt/spark/jars/

# NOTE(mattm): entrypoint.sh is for backwards compat with old version of the repo,
# we now call into the original path directly. Per Xieolei, decom.sh might not be
# useful either.
RUN ln -s /opt/spark/kubernetes/dockerfiles/spark/entrypoint.sh /opt/entrypoint.sh \
 && ln -s /opt/spark/kubernetes/dockerfiles/spark/decom.sh /opt/decom.sh

# Set the environment variables
ENV SPARK_HOME=/opt/spark
ENV PATH=$PATH:$SPARK_HOME/bin

# NOTE(mattm): These defaults are here for when the image is used directly, but note
# that the executor is now launched using the DevPod image and init.sh.
USER augment
WORKDIR /home/<USER>
ENTRYPOINT [ "/opt/entrypoint.sh" ]

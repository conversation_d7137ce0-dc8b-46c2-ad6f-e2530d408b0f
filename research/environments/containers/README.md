# Container environments in determined

- augment_gpu - container containing GPU packages (cuda, torch, etc)
- ci_worker - gpu container with an entrypoint for ci actions

- augment_cpu - for cpu intensive tasks
- augment_spark - cpu container with a spark entrypoint

Full documentation of the environments is [on notion](https://www.notion.so/Research-environments-a63eb8d3bb354e69ac4ceb4b842cefde)

## Rebuilding containers

*nb the gpu build will fail on a VM with 16G ram; it works with 64, not sure where the threshold is*

### Updating revisions

Revisions are stored in image_revisions/$target for each revision.  To build an updated container, update the revision(s) you wish to change, first.

`make augment_cpu`
`make augment_gpu`
`make augment_spark`
`make ci_worker`

Don't forget to `docker push` the resulting image.

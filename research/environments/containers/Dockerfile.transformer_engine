# Last updated: 10/21/24 (branch=v0.13-augment)
ARG TE_COMMIT=9b42134
# NOTE: we force the ubuntu20.04 image to avoid issues running on (old) services VMs.
ARG BASE_IMG=nvcr.io/nvidia/cuda:12.4.1-cudnn8-devel-ubuntu20.04
FROM $BASE_IMG AS builder

USER root
ENV DEBIAN_FRONTEND=noninteractive

# Install prereqs, python, and pytorch
RUN apt update && apt install -y software-properties-common curl wget git-all
ARG PYTHON_VERSION
COPY install_python.sh /tmp/install_python.sh
RUN /tmp/install_python.sh ${PYTHON_VERSION}
ENV PATH="$PATH:/opt/conda/bin"
ARG TORCH_VERSION=2.3.0
RUN pip install torch==${TORCH_VERSION}

# Checkout specified commit to build TransformerEngine
ARG TE_COMMIT
RUN git clone --recursive https://github.com/augmentcode/TransformerEngine.git /tmp/TransformerEngine
WORKDIR /tmp/TransformerEngine
RUN git checkout ${TE_COMMIT} && git submodule update

# Build the wheel itself (takes a few mins)
ENV MAX_JOBS=8
RUN pip install ninja
ENV NVTE_FRAMEWORK=pytorch
RUN python setup.py bdist_wheel

# Copy out the resulting wheel file
FROM scratch AS wheel
ARG TE_COMMIT
ARG CU_VERSION
ARG TO_VERSION
ARG WHEEL_FILE="transformer_engine-0.13.0+augment.cu${CU_VERSION}.torch${TO_VERSION}.${TE_COMMIT}-cp311-cp311-linux_x86_64.whl"
COPY --from=builder /tmp/TransformerEngine/dist/${WHEEL_FILE} /${WHEEL_FILE}

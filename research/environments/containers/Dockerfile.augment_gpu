ARG BASE_IMG
FROM $BASE_IMG AS augment_base

USER root

ENV LD_LIBRARY_PATH=/opt/conda/lib/python3.11/site-packages/torch/lib:$LD_LIBRARY_PATH

# Make sure we use the global file, not the root file
RUN rm -f /root/.config/pip/pip.conf /root/.pip/pip.conf

#### OPENMPI
ENV OPENMPI_BASEVERSION=4.1
ENV OPENMPI_VERSION=${OPENMPI_BASEVERSION}.0
RUN mkdir -p /build\
 && cd /build \
 && wget -q -O - https://download.open-mpi.org/release/open-mpi/v${OPENMPI_BASEVERSION}/openmpi-${OPENMPI_VERSION}.tar.gz | tar xzf - \
 && cd openmpi-${OPENMPI_VERSION} \
 && ./configure --prefix=/usr/local/openmpi-${OPENMPI_VERSION} \
 && make -j"$(nproc)" install \
 && ln -s /usr/local/openmpi-${OPENMPI_VERSION} /usr/local/mpi \
 # Sanity check: \
 && test -f /usr/local/mpi/bin/mpic++ \
 && cd ~ \
 && rm -rf /build \
 && mv /usr/local/mpi/bin/mpirun /usr/local/mpi/bin/mpirun.real \
 && echo '#!/bin/bash' > /usr/local/mpi/bin/mpirun \
 && echo 'mpirun.real --allow-run-as-root --prefix /usr/local/mpi "$@"' >> /usr/local/mpi/bin/mpirun \
 && chmod a+x /usr/local/mpi/bin/mpirun

# Clean up stale pydantic cpython .so binaries. The dai base image initially has
# `site-packages/pydantic-1.10.11.dist-info/` and after we're done has that AND
# `site-packages/pydantic-2.10.5.dist-info/`. The module directory
# `site-packages/pydantic` has .py files for 2.10, but stale .so binaries for 1.10 are left behind.
# This is the only package with multiple dist-info directories so the real problem is
# that the old version isn't being fully removed, however removing the stale .so's does the trick.
RUN rm -fr /opt/conda/lib/python3.*/site-packages/pydantic*

COPY --chown=root:root --chmod=0644 requirements.txt /root/
COPY --chown=root:root --chmod=0755 research-init.sh /root/
RUN --mount=type=cache,target=/root/.cache /root/research-init.sh --gpu --reqs-only -r /root/requirements.txt

# Apex install, AFTER the torch install
RUN --mount=type=cache,target=/root/.cache : \
 && pip install --ignore-installed apex==0.1,!=0.1+cpu

RUN mkdir -p /tmp/workspace
COPY --chown=augment:augment fused_kernels /tmp/workspace/fused_kernels
RUN --mount=type=cache,target=/root/.cache : \
 && pip install /tmp/workspace/fused_kernels \
 && /bin/rm -rf /tmp/workspace

#!/bin/bash

# A wrapper around `docker push` which does its best to implement tag
# immutability. This should also be enforced by harbor-master tag immutability.
# harbor-master is hard-coded as the lead regisry; this is not a generic tool.

set -eu -o pipefail

main() {
  # "flags" from env vars
  declare -r K8S_CONTEXT="${K8S_CONTEXT:-gcp-us1}"
  declare -r K8S_NAMESPACE="${K8S_NAMESPACE:-gcp-us1}"
  declare -r K8S_REGISTRY_SECRET="${K8S_REGISTRY_SECRET:-registry-readers}"
  declare -r REGISTRY="${REGISTRY:-harbor-master.r.augmentcode.com}"
  declare -r FORCE_PUSH="${FORCE_PUSH:-""}"

  # Get username:password from K8s Secret

  declare -r user_pass="$(docker_user_pass "$K8S_CONTEXT" "$K8S_NAMESPACE" "$K8S_REGISTRY_SECRET" "$REGISTRY")"

  # Check all images are tagged, and don't exist on the master yet (or WARN with FORCE_PUSH=1)

  declare image
  declare fail=""
  for image in "$@"; do
    if [[ "$image" != *:* ]]; then
      printf "ERROR: UntaggedImageNotSupported: %s\n" "$image" >&2
      fail="true"
      continue
    fi

    image="$(strip_image_registry "$image")"
    if ! checkfail_tag_exists "$user_pass" "$REGISTRY" "$image" "$FORCE_PUSH"; then
      fail="true"
    fi
  done

  if [[ "$fail" ]]; then
    return 1
  fi

  # All validations passed, now do the pushing.

  for image in "$@"; do
    docker_push "$image"
  done
}

docker_user_pass() {
  declare -r context="$1"
  declare -r namespace="$2"
  declare -r secret="$3"
  declare -r registry="$4"

  kubectl --context="$context" --namespace="$namespace" get secret/"$secret" \
    --template='{{index .data ".dockerconfigjson" | base64decode}}' \
    | jq -r ".auths[\"$registry\"].auth" \
    | base64 -d
}

docker_push() {
  declare -r image="$1"
  declare -ra cmd=(docker push "$image")
  printf "INFO: %s\n" "${cmd[*]}" >&2
  "${cmd[@]}"
}

checkfail_tag_exists() {
  declare -r auth="$1"
  declare -r registry="$2"
  declare -r image="$3"
  declare -r force="$4"

  if tag_exists "$auth" "$registry" "$image"; then
    if [[ "$force" ]]; then
      printf "WARNING: AlreadyExists: %s\n" "$image" >&2
    else
      printf "ERROR: AlreadyExists: %s\n" "$image" >&2
      return 1
    fi
  fi

  return 0
}

tag_exists() {
  declare -r auth="$1"
  declare -r registry="$2"
  declare -r image="$3"

  declare -r repo="$(parse_image_repo "$image")"
  declare -r tag="$(parse_image_tag "$image")"

  declare -i resp="$(api_head_code "$auth" "$registry" "$repo/manifests/$tag")"

  (( $resp == 200 ))
}

api_head_code() {
  declare -r auth="$1"
  declare -r registry="$2"
  declare -r path="$3"
  curl -sLI "https://$registry/v2/$path" -w "%{http_code}" -o /dev/null -K- <<<"--user \"$auth\""
}

strip_image_registry() {
  declare -r image="$1"
  printf "%s" "${image#*/}"
}

parse_image_repo() {
  declare -r image="$1"
  printf "%s" "${image%:*}"
}

parse_image_tag() {
  declare -r image="$1"
  printf "%s" "${image##*:}"
}

main "$@"

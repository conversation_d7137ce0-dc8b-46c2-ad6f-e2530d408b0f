#!/bin/bash

# Required so that the wheel is built for the correct GPU architectures.
# CPU build
# unset TORCH_CUDA_ARCH_LIST
# pip wheel --no-cache-dir \
#     --no-build-isolation \
#     --config-settings "--build-option=--cpu_only" \
#     git+https://github.com/augmentcode/apex.git@augment
export TORCH_CUDA_ARCH_LIST="7.0;7.5;8.0;8.6;9.0"
pip wheel --no-cache-dir \
    --no-build-isolation \
    --config-settings "--build-option=--cpp_ext" \
    --config-settings "--build-option=--cuda_ext" \
    git+https://github.com/NVIDIA/apex.git

# Set the wheel glibc version, don't pull in the torch stuff
auditwheel repair --plat manylinux_2_34_x86_64 \
    --exclude libc10.so \
    --exclude libtorch_cpu.so \
    --exclude libc10_cuda.so \
    --exclude libtorch_cuda.so \
    --exclude libtorch_python.so \
    apex-0.1-cp311-cp311-linux_x86_64.whl

local clusters = import '../../../infra/cfg/clusters/clusters.jsonnet';

function(
  cluster='gcp-us1',
  name='userauth',
  namespace=null,
  version='latest',
  image=null,
  image_base=std.stripChars(importstr '../../gh_runner_tag.txt', ' \n\t'),
  replicas=20,
) {

  local C = clusters.cluster(cluster),
  local k8s = C.k8s {
    BaseLabels+:: std.prune({
      app: 'cw-gh',
      'aug.service': name,
      'aug.version': version,
    }),

    Object+:: {
      namespace:: if namespace != null then namespace else super.namespace,
    },
  },

  deploy: k8s.Deployment {
    name:: 'gh-runner',
    spec+: {
      replicas: replicas,
      template+: {
        metadata+: {
          annotations+: {
            'gke-gcsfuse/volumes': 'true',
          },
        },
        spec+: {
          local pod = self,
          restartPolicy: k8s.RESTART_POLICY.ALWAYS,
          serviceAccountName: $.sa.metadata.name,
          containers: [
            k8s.Container {
              name: 'gh-runner',
              image: if image != null then image else C.registry + '/' + image_base,
              volumeMounts: pod.volmount_mounts,
              env: [
                {
                  name: 'GH_RUNNER_APP_PEM_PATH',
                  value: '/mnt/augment/runner-secrets/gh-runner-app.pem',
                },
                {
                  name: 'GH_RUNNER_APP_ID',
                  value: '1151562',
                },
                {
                  name: 'RUNNER_LABELS',
                  value: cluster,
                },
              ],
              command: ['/bin/bash', '/gh_runner.sh'],
            },
          ],
          volmounts:: [
            {
              local vm = self,
              name:: 'gcp-us1-augment-ci-testing',
              volume:: {
                csi: {
                  driver: 'gcsfuse.csi.storage.gke.io',
                  volumeAttributes: {
                    bucketName: vm.name,
                    mountOptions: std.join(',', [
                      'implicit-dirs',
                      'uid=1000',
                      'gid=1000',
                      'file-mode=0440',
                      'dir-mode=0550',
                      'o=noexec',
                      'o=noatime',
                    ]),
                  },
                },
              },
              mount:: { mountPath: '/mnt/efs/augment-ci-testing' },
            },
            {
              local vm = self,
              name:: $.gh_app_pem.metadata.name,
              volume:: { secret: { secretName: vm.name } },
              mount:: { mountPath: '/mnt/augment/secrets/' },
            },
            {
              local vm = self,
              name:: $.gh_runner_app_pem.metadata.name,
              volume:: { secret: { secretName: vm.name } },
              mount:: { mountPath: '/mnt/augment/runner-secrets/' },
            },
          ],
        },
      },
    },
  },

  sa: k8s.ServiceAccount {
    name:: 'github-runner',
    metadata+: {
      annotations+: {
        'iam.gke.io/gcp-service-account': $.gsa.email,
      },
    },
  },

  gsa: k8s.GCP.IAM.ServiceAccount + {
    name:: C.name + '-github-runner',
    description:: 'GitHub Runner Service Account',
    project_id:: C.gcp_project,
    namespace:: 'cc-' + C.name,
  },

  gsa_workload_identity: k8s.GCP.IAM.PolicyMember + {
    name:: $.gsa.metadata.name + '.workload-identity-user',
    namespace:: 'cc-' + C.name,
    spec+: {
      resourceRef: $.gsa.localKindRef,
      member: 'serviceAccount:%s.svc.id.goog[%s/%s]' % [C.gcp_project, $.sa.metadata.namespace, $.sa.metadata.name],
      role: 'roles/iam.workloadIdentityUser',
    },
  },

  gsa_storage_access: std.flattenArrays([
    [
      k8s.GCP.IAM.PolicyMember + {
        name:: $.gsa.metadata.name + '.' + bucket + '.storage-bucketviewer',
        namespace:: 'cc-' + C.name,
        spec+: {
          resourceRef: {
            kind: 'StorageBucket',
            external: bucket,
          },
          member: 'serviceAccount:' + $.gsa.email,
          role: 'roles/storage.bucketViewer',
        },
      },
      k8s.GCP.IAM.PolicyMember + {
        name:: $.gsa.metadata.name + '.' + bucket + '.storage-objectuser',
        namespace:: 'cc-' + C.name,
        spec+: {
          resourceRef: {
            kind: 'StorageBucket',
            external: bucket,
          },
          member: 'serviceAccount:' + $.gsa.email,
          role: 'roles/storage.objectUser',
        },
      },
    ]
    for bucket in [C.name + '-augment-ci-testing', C.name + '-user', C.name + '-checkpoints']
  ]),

  gsa_artifact_reader: k8s.GCP.IAM.PolicyMember + {
    name:: $.gsa.metadata.name + '.artifactregistry-reader',
    namespace:: 'cc-' + C.name,
    spec+: {
      resourceRef: {
        kind: 'Project',
        external: 'project/' + C.gcp_project,
      },
      member: 'serviceAccount:' + $.gsa.email,
      role: 'roles/artifactregistry.reader',
    },
  },

  role: k8s.Role {
    name:: 'gh-runner-role',
    // NOTE: Originally copied from the determined.ai role
    rules: [
      {
        apiGroups: [''],
        resources: ['pods', 'configmaps', 'pods/status', 'pods/log'],
        verbs: ['create', 'get', 'list', 'delete'],
      },
      {
        apiGroups: [''],
        resources: ['pods/log'],
        verbs: ['get', 'list'],
      },
      {
        apiGroups: [''],
        resources: ['services'],
        verbs: ['get', 'list'],
      },
      {
        apiGroups: [''],
        resources: ['pods'],
        verbs: ['watch'],
      },
      {
        apiGroups: [''],
        resources: ['events'],
        verbs: ['list', 'watch'],
      },
      {
        apiGroups: [''],
        resources: ['secrets'],
        resourceNames: ['registry-readers', 'registry-taggers'],
        verbs: ['get', 'list', 'watch'],
      },
    ],
  },

  rb: k8s.RoleBinding {
    name:: 'gh-runner-binding',
    role_name: $.role.metadata.name,
    sas: [$.sa],
  },

  gh_app_pem: k8s.SealedSecret {
    name:: 'gh-app-pem',
    encryptedData: {
      'gh-app.pem': 'AgAF2VKNk/rxkc0SGBaHORBoU8KsnB4j5ltXZdOH5R3n7bpGqRfWU3PE0AcCGvq/P9a8fKRniYJV45NnVvBPCXBfIsz5IkRCQmxWn6lWYZ4uEiq7d4fy04XoV9rOyYV2TCUclptQMqJUknKb0ny/i5qoEcenlGNxxYxl8MflPjohuQM4TSiCtckbIzDk1CzbZa5yPzu1QfknatcongHdblRYfwPphkZ/S0lgmyPpH48BQlqlzHdaLYqODEvMt7IKhCYbw0vH5iJhCUNSE3LcQ3lUm5Dh7ZR4AGNGkrSeuV1cP/zHnplu8SfTKbshGQV8U1fLm4eZq8jRAtcYnjEwZhgMory6QFDDu3Vx+8ZusqSiBjenic1Lu662en7AYEsdODbhmrG5rg4nMnemcvDlSf3mckHN1C5iN0XFtE99Dgocrkrn4EJHj3A/pnleg8hdgBmsL304iGSPvbneNO/F/jmrdSkQQ5AhIXDaejdByiAFPM+4jj1wqgWpgl8cPPfPfjx3rWFoPQRdJZzULFCkbgaYtowQLiGymiKqCfkNBnRlg9sbW2tx1Ym0mnndA+76L7zB8/YOiwYuXJI8djb4mPW3bPb73U9bIo7virgI3qQxIXhup6W5aYkM6s43oZLlBEHYIDh4BWdKgDa2tj9PDgoQ3GNgkDeoBwnAfU9FMPZDr2rYYILvirVj+0RlIt03bkah7hKk0ECVcDLKq5dwae/kNrUdwc2gwAWA0epcuCltHWHA1L2rZh9MNCBWZLSwN4qogeITBeb0/EU2ypuciHL3fFuZWhQz01L8bmMmqriVF+zI7AqlcIa7LYf0hyOWsSWH6wdP3VyFnH3STjCJ2SDsJNfIHRrLoCCV+OL3Ub6B4Laq2Oplf8mApCVClKD1BPE1KO36or43TaEjHsnHwMyv2I4ffR+IxqafcqKxVyICEvdzwnep+NLZYnH7vlqqo2REFELlS+oGzxFwQPhx8X4iRoy3/2CTbLfKGRZkjc0geZ5Ps8yJPpmxl/hW5fxcWNTaQLTmWynb83XgY5xb32+PC2cqVvD7uwkmksmK5VUhL4OFP5HtVGJ6MGpfhSqFQz6cB/kHX9AEd9sAlBLz/aH2S8GWY2M82evIJuo7AIHuEs3mpA6TlunNZOkOlpd6TmrMpu41Vq8NrgMvgyODUNRb+0yPtoSfaTELgP0NpGP5aFpUc83qjccrvGLLSEV1O9Pa6oYaWYxcTkk/ORIXxyWC8xcO/12Ph4InoDWla1a/Zi6eQzqf5mzCHAf4fgWFizIXh9pxuQPXxpxOBb3vz0NWBTE49XDXdnZi+dlcw0L64I/NFPsGFq5FXi3nLVdcxek/O4C35rAuVrE/mGe6zgf0BjJEPoPgCKinPzer0KE1AWGoZcjDlF/gkNIY6ZGA3vh5utENhXZVrbNa+soJsE7HOFdrRD9r91kxq1ETlbPM4jknk2KGbvfhsnylZEw0pFVmZNMAjM6gKR2oXmiupMSZZNTLW9tfSlE2Gjsks3MUaLYPWZagfcclIhxtWFOAdlxvN/x5pshx46bXFtGEVGppdWg1KCRsb/pQXoVreTgINdd5/6g3pgzjyJzXXJ6OOmEISszvboGRei9DhlhcQGxHhWPKPTuDq8aQeMfpezgS26ZRybBXMNT1BYeaop/KWhpsD1VJQp3Wcn+07DFO1nHswhUMApSG56Zy9pLKNy2l7M26jcFJ5tyW1CdxHsmRb2rjFPR57gfXO7Pi2Dyp6z3Wuo9Wn75rg0mT0cshACjOI06rx2Qcj6e6Yjp2+081H9siVhJ3ggR6ICxkrkEb+/o/vPyr3Vr8o7qBIWs6VSLmK/ih9PY5JfhnEOCWd4BLplEcC6ev0/HaJA/6/bb6IRuucL/Z0rKAUIWC107riLGfaNfRXW6jkGZ+Hy2z/opt88vWAD8K0RLlf3ezVI9dyLoQWE3b9EFM3yPYbSC4La5m2XgEcyazMyu7Lz+eqNE+ETMH8IMJsMgzCoc/j/APjNXUWvAqLo3zHl27h61PrrmwlAgzn/tXDd+4ICZd/FL2oCPE8BTiFgwTVLvNnPCztJ2yYXFs58442NEoBOHDUmlqItibIGuv70glkVKQWBDJrdlvWIXcf5oDu2wsdX0O3o8LE3C7nwGFqskh1otvbXoFKQ8vtkFPEACzNAx4UFAmycHPxMG0/G7PSTRdtZQmK30sYACuaR22qqo4318/WgyDDE8A6R8PydVxUKgNAtx/TGAZrWCAE/j0TqTEktmpUeFmg/jyzPXSKx81OFb1xacV1B3kuYDo8XTBQySAf5PD6lWwcvvqw/ZJjQrMhKnjbr92CKG6SdXosICdbRvDQTGe5qceMIdmanfNhhofdkSAMq/NNWHZ6E7CQQfnYYgGYckCSNAwr7+gsi7BYPy+PpXbSEdwbQM44x30VnRi++22zlDNovZfL6VGQl8uEV/oC8/6doYxHqb9Bu78XKIaJPNrq+mxiqNpsn9e11PJKd2IS5qdvA1KLFmTPPoiFKUSUunfyCoGaUfLbXRtUfhl+jxI0dw6NA5Wd/pJdciJwfnH2xu0xXGG5hhRNReKKzXldZzq0PoYy0ZgRxWBpBCP387HIXIrqMbrmoG17o5EJCMPWAqNtdYC0IiXVvaYql2G55Ubo/8hZAVRbcLdp3T4HCkH0Ee3XaJLVt66Q/LHzg+uCorlUj1b4V8GqCGnOZHSzTF9l2+wqWy99ttHeCWNC7DbXF/KXYTCEPzR2cEGH4411pt0ihnCN5N9a85Cl3x4VeEoJVr9pe2eF9NYdgBmdB2rNMsBc24vSLG01x46kDEVW7c65j0VQApXm0KWJHGboWTdw/0wDQXiXN0R/Vrr2uzcYwMuiv6o+QOXIpoSd2Jg508gfF0bR47NSCRfPe80bHAm63d1+6i8tUgh7xLjiE67Efk3QwPpsIX5gSdhv05Wvg==',
    },
  },

  gh_runner_app_pem: k8s.SealedSecret {
    name:: 'gh-runner-app-pem',
    encryptedData: {
      'gh-runner-app.pem': '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',
    },
  },

}

NAME    := gh_runner
VERSION := 2025-02-19.1
CLUSTER := gcp-us1

CONTEXT  := $(shell jsonnet -A name="$(CLUSTER)" -A prop=context_admin -Se "(import '../../../infra/cfg/clusters/clusters.jsonnet').cluster_prop")
K8S_CONF := $(NAME).jsonnet
KUBECTL  := kubectl --context="$(CONTEXT)"
KUBECFG  := kubecfg --context="$(CONTEXT)"  \
	   --tla-code="cluster='$(CLUSTER)'" \
	   --tla-code="name='$(NAME)'" \
	   --tla-code="version='$(VERSION)'"

show:
	$(KUBECFG) show $(K8S_CONF)

diff:
	-$(KUBECFG) diff --diff-strategy=subset $(K8S_CONF)

cleandiff:
	$(KUBECFG) diff --diff-strategy=subset $(K8S_CONF) | awk '{ lines[NR] = $$0; if (NR > 2) { if (lines[NR-2] !~ /unchanged$$/ && lines[NR-1] !~ /unchanged$$/ && lines[NR] !~ /unchanged$$/) { print lines[NR-2]; } } }'

apply:
	$(KUBECFG) update $(K8S_CONF)

logs:
	$(KUBECTL) logs -l aug.service=$(NAME) $(LOGS_EXTRA)

list:
	$(KUBECTL) get -l aug.service=$(NAME) deploy,pod,svc,ing,pvc,role,rolebinding,sa,cm,secret,sealedsecret

deploy: diff apply

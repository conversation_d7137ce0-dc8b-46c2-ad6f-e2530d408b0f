function get_gh_app_token {
    # get the GH app token
    local installation="$1"
    local token="$2"
    local app_token=$(curl -sL -X POST \
        --header "Accept: application/vnd.github+json" \
        --header "Authorization: Bearer $token" \
        --header "X-GitHub-Api-Version: 2022-11-28" \
        https://api.github.com/app/installations/$installation/access_tokens | \
        jq -r '.token')
    printf '%s' "$app_token"
}

function get_gh_app_installation {
    # get the GH app installation ID
    local token="$1"
    local installation=$(curl -sL \
        -H "Accept: application/vnd.github+json" \
        -H "Authorization: Bearer $token" \
        -H "X-GitHub-Api-Version: 2022-11-28" \
        https://api.github.com/app/installations \
        | jq -r '.[] | select(.account.login=="augmentcode") | .id')
    printf '%s' "$installation"
}

function create_gh_jwt {
    # Generate a JWT token for GH, adapted from a GH example
    local app_id=$1
    local pem_path=$2
    local pem=$( cat $pem_path ) # pem file as k8s secret

    local now=$(date +%s)
    local iat=$((${now} - 60)) # Issues 60 seconds in the past
    local exp=$((${now} + 600)) # Expires 10 minutes in the future

    b64enc() { openssl base64 | tr -d '=' | tr '/+' '_-' | tr -d '\n'; }
    local header_json='{
        "typ":"JWT",
        "alg":"RS256"
    }'
    # Header encode
    local header=$( echo -n "${header_json}" | b64enc )

    local payload_json='{
        "iat":'"${iat}"',
        "exp":'"${exp}"',
        "iss":'"${app_id}"'
    }'
    # Payload encode
    local payload=$( echo -n "${payload_json}" | b64enc )

    # Signature
    local header_payload="${header}"."${payload}"
    local signature=$(
        openssl dgst -sha256 -sign <(echo -n "${pem}") \
        <(echo -n "${header_payload}") | b64enc
    )

    # Create JWT
    local JWT="${header_payload}"."${signature}"
    printf '%s' "$JWT"
}

function gh_app_auth {
    local app_id=$1
    local pem_path=$2
    jwt=$(create_gh_jwt $app_id $pem_path)
    app_installation=$(get_gh_app_installation $jwt)
    app_token=$(get_gh_app_token $app_installation $jwt)
    printf '%s' "$app_token"
}

# GH runner dockerfile that launches ci_worker pods, based on augment CPU build

ARG BASE_IMG
FROM $BASE_IMG AS augment_base
USER root

RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && export DEBIAN_FRONTEND=noninteractive \
 && apt-get update -y && \
    apt-get install -y \
        git \
        jq \
        lsb-release \
        pciutils \
        curl \
        gnupg

# Install Google Cloud SDK
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | gpg --dearmor > /usr/share/keyrings/cloud.google.gpg \
 && echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | tee /etc/apt/sources.list.d/google-cloud-sdk.list \
 && apt-get update -y \
 && apt-get install -y google-cloud-cli google-cloud-cli-gke-gcloud-auth-plugin

RUN --mount=type=cache,target=/root/.cache : \
 && pip install -U pip \
 && pip install -U \
    PyYAML==6.0

COPY --chown=root:root --chmod=0755 gh_app_auth.sh /gh_app_auth.sh
COPY --chown=root:root --chmod=0755 gh_runner.sh /gh_runner.sh
ENTRYPOINT ["/gh_runner.sh"]

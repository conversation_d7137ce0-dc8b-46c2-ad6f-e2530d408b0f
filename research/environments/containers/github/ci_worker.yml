# This job spec is used as a template to launch a CI job in coreweave
apiVersion: v1
kind: Pod
metadata:
  name: cw-gh-worker-testing
spec:
  enableServiceLinks: false
  containers:
  - name: gh-worker
    imagePullPolicy: IfNotPresent
    image: Set by ci_launcher.py
    command: ["/bin/bash", "/home/<USER>/augment/research/environments/containers/github/ci_worker.sh"]
    volumeMounts:
    - mountPath: /mnt/efs/augment-ci-testing
      name: gcs-gcp-us1-augment-ci-testing
    - mountPath: /home/<USER>/augment
      name: git-volume
    - mountPath: /mnt/augment/secrets/
      name: gh-app-pem
      readOnly: true
    - mountPath: /mnt/augment/augment-token
      name: augment-token
      readOnly: true
    - mountPath: /dev/shm
      name: dshm
    env:
    - name: AUGMENT_EFS_ROOT
      value: /mnt/efs/augment-ci-testing
    - name: AUGMENT_API_TOKEN
      valueFrom:
        secretKeyRef:
          name: augment-api-key
          key: token
    resources:
      limits:
        cpu: "4"
        memory: "32Gi"
        nvidia.com/gpu: "1"
        ephemeral-storage: "500Gi"
      requests:
        cpu: "4"
        memory: "32Gi"
        nvidia.com/gpu: "1"
        ephemeral-storage: "500Gi"
  initContainers:
  - name: git-checkout
    image: alpine/git
    command:
    - sh
    - -c
    - /home/<USER>/config/ci_git_fetch.sh
    imagePullPolicy: IfNotPresent
    volumeMounts:
    - mountPath: /mnt/efs/augment-ci-testing
      name: gcs-gcp-us1-augment-ci-testing
    - mountPath: /home/<USER>/augment
      name: git-volume
    - mountPath: /home/<USER>/config
      name: ci-git-fetch
    - mountPath: /mnt/augment/secrets/
      name: gh-app-pem
      readOnly: true
    env:
    - name: GITHUB_REPOSITORY
      value: augmentcode/augment.git
    - name: GH_APP_PEM_PATH
      value: /mnt/augment/secrets/gh-app.pem
    - name: GH_APP_ID
      value: "697167"
    resources:
      limits:
        cpu: "1"
        memory: "4Gi"
      requests:
        cpu: "1"
        memory: "4Gi"
  volumes:
  - name: git-volume
    emptyDir:
      sizeLimit:  100Gi
  - name: dshm
    emptyDir:
      medium: Memory
      sizeLimit: "8Gi"
  - name: gcs-gcp-us1-augment-ci-testing
    csi:
      driver: gcsfuse.csi.storage.gke.io
      readOnly: false
      volumeAttributes:
        bucketName: gcp-us1-augment-ci-testing
        fileCacheCapacity: 500Gi
        fileCacheForRangeRead: "false"
        mountOptions: implicit-dirs,uid=1000,gid=1000,file-mode=0666,dir-mode=0777,o=noexec,o=noatime,file-cache:enable-parallel-downloads:true,file-cache:parallel-downloads-per-file:8,file-cache:max-parallel-downloads:-1,file-cache:download-chunk-size-mb:16
  - name: ci-git-fetch
    configMap:
      name: ci-git-fetch
      defaultMode: 0755
  - name: gh-app-pem
    secret:
      secretName: gh-app-pem  # pragma: allowlist secret
  - name: augment-token
    secret:
      secretName: augment-api-key  # pragma: allowlist secret
  restartPolicy: Never
  tolerations:
  - key: r.augmentcode.com/pool-type
    operator: Equal
    value: ci
    effect: NoSchedule

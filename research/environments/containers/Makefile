REGISTRY := harbor-master.r.augmentcode.com/master
GIT_ROOT := $(shell git rev-parse --show-toplevel)

DOCKER_PUSH  := ./docker-push-safe.sh
DOCKER_BUILD := docker build --progress=plain

TORCH_VERSION := 2.5.1
TO_VERSION := 25
DETERMINED_VERSION := 0.36.0
PYTHON_VERSION := 3.11.7
UBUNTU_VERSION := 22.04
CUDA_VERSION := 12.4
CUDA_VERSION_LONG := 12.4.1
CU_VERSION := 124
TRANSFORMER_ENGINE_UBUNTU_VERSION := 20.04

CPU_VERSION := ubuntu$(UBUNTU_VERSION)-py-$(PYTHON_VERSION)
GPU_BASE_VERSION := ubuntu$(UBUNTU_VERSION)-cuda-$(CUDA_VERSION)-py-$(PYTHON_VERSION)-pytorch-$(TORCH_VERSION)-ngc
GPU_VERSION := $(GPU_BASE_VERSION)-det-$(DETERMINED_VERSION)
TRANSFORMER_ENGINE_BASE_IMG := nvcr.io/nvidia/cuda:$(CUDA_VERSION_LONG)-cudnn-devel-ubuntu$(TRANSFORMER_ENGINE_UBUNTU_VERSION)

SPARK_VERSION := 3.4.3

CPU_BASE_IMG := ubuntu:$(UBUNTU_VERSION)
# We don't have to rebuild the base image since these get overlaid anyway, so
# track the base versions and the final versions separately.
BASE_CUDA_VERSION := 12.1
BASE_TORCH_VERSION := 2.3.0
GPU_BASE_IMG := $(REGISTRY)/dai_environments:ubuntu$(UBUNTU_VERSION)-cuda-$(BASE_CUDA_VERSION)-py-$(PYTHON_VERSION)-pytorch-$(BASE_TORCH_VERSION)-ngc

CPU_REVISION := $(file < image_revisions/augment_cpu)
CPU_TAG := augment_cpu:$(CPU_VERSION)-$(CPU_REVISION)

GPU_REVISION := $(file < image_revisions/augment_gpu)
GPU_TAG := augment_gpu:$(GPU_VERSION)-$(GPU_REVISION)

SPARK_CPU_BASE := $(CPU_TAG)
SPARK_GPU_BASE := $(GPU_TAG)
SPARK_CPU_REV  := spark-$(SPARK_VERSION)-s$(file < image_revisions/augment_spark_cpu)
SPARK_GPU_REV  := spark-$(SPARK_VERSION)-s$(file < image_revisions/augment_spark_gpu)
SPARK_CPU_TAG  := augment_spark_python:$(subst augment_cpu:,,$(SPARK_CPU_BASE))-$(SPARK_CPU_REV)
SPARK_GPU_TAG  := augment_spark_python_gpu:$(subst augment_gpu:,,$(SPARK_GPU_BASE))-$(SPARK_GPU_REV)

GH_RUNNER_BASE := $(CPU_TAG)
CI_WORKER_BASE := $(GPU_TAG)
GH_RUNNER_REV  := gh$(file < image_revisions/gh_runner)
CI_WORKER_REV  := ci$(file < image_revisions/ci_worker)
GH_RUNNER_TAG  := gh_runner:$(subst augment_cpu:,,$(GH_RUNNER_BASE))-$(GH_RUNNER_REV)
CI_WORKER_TAG  := augment_ci_worker:$(subst augment_gpu:,,$(CI_WORKER_BASE))-$(CI_WORKER_REV)

# Versioning for devpod is {base}-devcpu{rev}. NOTE(mattm): I couldn't get pattsubst to work with "%:" as a pattern...
AUGMENT_DEVPOD_CPU_BASE := $(SPARK_CPU_TAG)
AUGMENT_DEVPOD_GPU_BASE := $(SPARK_GPU_TAG)
AUGMENT_DEVPOD_CPU_REV  := devpod$(file < image_revisions/augment_devpod_cpu)
AUGMENT_DEVPOD_GPU_REV  := devpod$(file < image_revisions/augment_devpod_gpu)
AUGMENT_DEVPOD_CPU_TAG  := augment_devpod_cpu:$(subst augment_spark_python:,,$(AUGMENT_DEVPOD_CPU_BASE))-$(AUGMENT_DEVPOD_CPU_REV)
AUGMENT_DEVPOD_GPU_TAG  := augment_devpod_gpu:$(subst augment_spark_python_gpu:,,$(AUGMENT_DEVPOD_GPU_BASE))-$(AUGMENT_DEVPOD_CPU_REV)

.PHONY: _augment_base augment_cpu_base augment_gpu_base _augment augment_cpu augment_gpu

usage:
	@echo "Select an image to build: {augment_cpu augment_gpu augment_spark_cpu ci_worker} or 'all'"

registry:
	echo $(REGISTRY)

build_all: build_all_cpu build_all_gpu
build_all_cpu: augment_cpu augment_spark_cpu augment_devpod_cpu gh_runner
build_all_gpu: augment_gpu augment_spark_gpu augment_devpod_gpu ci_worker

_augment_base:
	go build -C "$(GIT_ROOT)"/research/infra -o "$(PWD)"/augi.build augi.go
	$(DOCKER_BUILD) \
		-t $(IMAGE_TAG) \
		--build-arg PYTHON_VERSION=$(PYTHON_VERSION) \
		--build-arg BASE_IMG=$(BASE_IMG) \
		-f $(GIT_ROOT)/research/environments/containers/Dockerfile.augment_base .
	$(RM) augi.build

augment_cpu_base:
	$(MAKE) _augment_base \
		BASE_IMG=$(CPU_BASE_IMG) \
		IMAGE_TAG=augment_cpu_base:latest

augment_gpu_base:
	$(MAKE) _augment_base \
		BASE_IMG=$(GPU_BASE_IMG) \
		IMAGE_TAG=augment_gpu_base:latest

_augment:
	cp -r "$(GIT_ROOT)/research/gpt-neox/megatron/fused_kernels" .
	cp "$(GIT_ROOT)/research/research-init.sh" .
	cp "$(GIT_ROOT)/research/requirements.txt" .
	$(DOCKER_BUILD) \
		-t $(IMAGE_TAG) \
		--build-arg BASE_IMG=$(BASE_IMG) \
		--build-arg DETERMINED_VERSION=$(DETERMINED_VERSION) \
		--build-arg PYTHON_VERSION=$(PYTHON_VERSION) \
		--build-arg FUSED_KERNELS_PATH="$(GIT_ROOT)/research/gpt-neox/megatron/fused_kernels" \
		-f $(DOCKERFILE) .
	rm -rf fused_kernels
	$(RM) "research-init.sh"
	$(RM) "requirements.txt"

augment_cpu: augment_cpu_base
	$(MAKE) _augment \
		BASE_IMG=augment_cpu_base:latest \
		DOCKERFILE=$(GIT_ROOT)/research/environments/containers/Dockerfile.augment_cpu \
		IMAGE_TAG=$(REGISTRY)/$(CPU_TAG)
	$(MAKE) cpu_tag.txt

augment_gpu: augment_gpu_base
	$(MAKE) _augment \
		BASE_IMG=augment_gpu_base:latest \
		DOCKERFILE=$(GIT_ROOT)/research/environments/containers/Dockerfile.augment_gpu \
		IMAGE_TAG=$(REGISTRY)/$(GPU_TAG)
	$(MAKE) gpu_tag.txt

# NOTE(mattm): Remove this when we can update to latest version.
$(HOME)/.cache/spark/spark-$(SPARK_VERSION)-bin-hadoop3.tgz:
	mkdir -p $(HOME)/.cache/spark/
	gcloud --project=augment-research-gcs storage cp gs://gcp-us1-lib/spark-$(SPARK_VERSION)-bin-hadoop3.tgz "$(HOME)/.cache/spark/"

_augment_spark: $(HOME)/.cache/spark/spark-$(SPARK_VERSION)-bin-hadoop3.tgz
	$(DOCKER_BUILD) \
		-t $(IMAGE_TAG) \
		--build-arg BASE_IMG=$(BASE_IMG) \
		--build-arg SPARK_VERSION=$(SPARK_VERSION) \
		--build-context home-cache="$(HOME)"/.cache/spark/ \
		-f Dockerfile.augment_spark .
	$(MAKE) $(TAGFILE)

augment_spark_cpu:
	$(MAKE) _augment_spark \
		BASE_IMG=$(REGISTRY)/$(SPARK_CPU_BASE) \
		IMAGE_TAG=$(REGISTRY)/$(SPARK_CPU_TAG) \
		TAGFILE=spark_cpu_tag.txt

augment_spark_gpu:
	$(MAKE) _augment_spark \
		BASE_IMG=$(REGISTRY)/$(SPARK_GPU_BASE) \
		IMAGE_TAG=$(REGISTRY)/$(SPARK_GPU_TAG) \
		TAGFILE=spark_gpu_tag.txt

INFRA_DIR := $(GIT_ROOT)/research/infra
DEVPOD_DIR := $(INFRA_DIR)/lib/augment/devpod

_augment_devpod:
	go build -C "$(INFRA_DIR)" -o "$(DEVPOD_DIR)"/augi.build augi.go
	$(DOCKER_BUILD) --build-arg BASE_IMG="$(BASE_IMG)" -t "$(IMAGE_TAG)" "$(DEVPOD_DIR)"
	$(RM) "$(DEVPOD_DIR)"/augi.build
	$(MAKE) $(TAGFILE)

augment_devpod_cpu:
	$(MAKE) _augment_devpod \
		BASE_IMG=$(REGISTRY)/$(AUGMENT_DEVPOD_CPU_BASE) \
		IMAGE_TAG=$(REGISTRY)/$(AUGMENT_DEVPOD_CPU_TAG) \
		TAGFILE=devpod_cpu_tag.txt

augment_devpod_gpu:
	$(MAKE) _augment_devpod \
		BASE_IMG=$(REGISTRY)/$(AUGMENT_DEVPOD_GPU_BASE) \
		IMAGE_TAG=$(REGISTRY)/$(AUGMENT_DEVPOD_GPU_TAG) \
		TAGFILE=devpod_gpu_tag.txt

gh_runner:
	$(DOCKER_BUILD) \
		-t $(REGISTRY)/$(GH_RUNNER_TAG) \
		--build-arg BASE_IMG=$(REGISTRY)/$(GH_RUNNER_BASE) \
		-f github/Dockerfile.gh_runner github
	$(MAKE) gh_runner_tag.txt

ci_worker:
	$(DOCKER_BUILD) \
		-t $(REGISTRY)/$(CI_WORKER_TAG) \
		--build-arg BASE_IMG=$(REGISTRY)/$(CI_WORKER_BASE) \
		-f github/Dockerfile.ci_worker github
	$(MAKE) ci_worker_tag.txt

cpu_tag.txt:
	@echo $(CPU_TAG) > $(GIT_ROOT)/research/environments/$@

gpu_tag.txt:
	@echo $(GPU_TAG) > $(GIT_ROOT)/research/environments/$@

spark_cpu_tag.txt:
	@echo $(SPARK_CPU_TAG) > $(GIT_ROOT)/research/environments/$@

spark_gpu_tag.txt:
	@echo $(SPARK_GPU_TAG) > $(GIT_ROOT)/research/environments/$@

devpod_cpu_tag.txt:
	@echo $(AUGMENT_DEVPOD_CPU_TAG) > $(GIT_ROOT)/research/environments/$@

devpod_gpu_tag.txt:
	@echo $(AUGMENT_DEVPOD_GPU_TAG) > $(GIT_ROOT)/research/environments/$@

gh_runner_tag.txt:
	@echo $(GH_RUNNER_TAG) > $(GIT_ROOT)/research/environments/$@

ci_worker_tag.txt:
	@echo $(CI_WORKER_TAG) > $(GIT_ROOT)/research/environments/$@


# Rule to build the wheel for flash-attn v3 and copy it out to the local filesystem
flash_attention3_wheel:
	$(DOCKER_BUILD) \
		--build-arg PYTHON_VERSION=$(PYTHON_VERSION) \
		--build-arg TORCH_VERSION=$(TORCH_VERSION) \
		--target=wheel \
		--output type=local,dest=$(GIT_ROOT)/research/environments/containers \
		-f Dockerfile.flash_attention3 .

# Same for transformer_engine
transformer_engine_wheel:
	$(DOCKER_BUILD) \
		--build-arg PYTHON_VERSION=$(PYTHON_VERSION) \
		--build-arg TORCH_VERSION=$(TORCH_VERSION) \
		--build-arg BASE_IMG=$(TRANSFORMER_ENGINE_BASE_IMG) \
		--build-arg CU_VERSION=$(CU_VERSION) \
		--build-arg TO_VERSION=$(TO_VERSION) \
		--target=wheel \
		--output type=local,dest=$(GIT_ROOT)/research/environments/containers \
		-f Dockerfile.transformer_engine .

.PHONY: test push_cpu push_gpu push_all push_all_cpu push_all_gpu

push_all: push_all_cpu push_all_gpu
push_all_cpu: push_cpu push_spark_cpu push_devpod_cpu push_gh_runner
push_all_gpu: push_gpu push_spark_gpu push_devpod_gpu push_ci_worker

push_cpu:
	-$(DOCKER_PUSH) $(REGISTRY)/$(CPU_TAG)

push_gpu:
	-$(DOCKER_PUSH) $(REGISTRY)/$(GPU_TAG)

push_devpod: push_devpod_cpu push_devpod_gpu

push_devpod_cpu:
	-$(DOCKER_PUSH) $(REGISTRY)/$(AUGMENT_DEVPOD_CPU_TAG)

push_devpod_gpu:
	-$(DOCKER_PUSH) $(REGISTRY)/$(AUGMENT_DEVPOD_GPU_TAG)

push_spark: push_spark_cpu push_spark_gpu

push_spark_cpu:
	-$(DOCKER_PUSH) $(REGISTRY)/$(SPARK_CPU_TAG)

push_spark_gpu:
	-$(DOCKER_PUSH) $(REGISTRY)/$(SPARK_GPU_TAG)

push_gh_runner:
	-$(DOCKER_PUSH) $(REGISTRY)/$(GH_RUNNER_TAG)

push_ci_worker:
	-$(DOCKER_PUSH) $(REGISTRY)/$(CI_WORKER_TAG)

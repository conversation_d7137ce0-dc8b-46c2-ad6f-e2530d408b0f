# Specify FA3 commit at the top so it's accessible to all build stages
# Last updated: 10/25/24 (branch=main)
ARG FA3_COMMIT=c1d146c
# NOTE: we use the nvidia CUDA 12.3 base image because we want to compile with the 12.3 compiler
ARG BASE_IMG=nvidia/cuda:12.3.2-cudnn9-devel-ubuntu22.04
FROM $BASE_IMG AS builder

USER root
ENV DEBIAN_FRONTEND=noninteractive

# Install prereqs, python, and pytorch
RUN apt update && apt install -y software-properties-common curl wget git-all
ARG PYTHON_VERSION
COPY install_python.sh /tmp/install_python.sh
RUN /tmp/install_python.sh ${PYTHON_VERSION}
ENV PATH="$PATH:/opt/conda/bin"
ARG TORCH_VERSION=2.3.0
RUN pip install torch==${TORCH_VERSION}

# Checkout specified commit to build flash-attention v3
ARG FA3_COMMIT
RUN git clone --recursive https://github.com/Dao-AILab/flash-attention.git /tmp/flash-attention
WORKDIR /tmp/flash-attention/hopper
RUN git checkout ${FA3_COMMIT} && git submodule update

# Build the wheel itself (takes a few mins)
ENV MAX_JOBS=8
RUN pip install ninja
ENV FLASHATTN_HOPPER_LOCAL_VERSION=${FA3_COMMIT}
RUN python setup.py bdist_wheel

# Copy out the resulting wheel file to /<wheel-file>
# To get the file _out_ of Docker, invoke as:
# > docker build ... --target=wheel --output type=local,dest=<path-to-output-dir> ...
FROM scratch AS wheel
ARG FA3_COMMIT
ARG WHEEL_FILE="flashattn_hopper-3.0.0b1+${FA3_COMMIT}-cp311-cp311-linux_x86_64.whl"
COPY --from=builder /tmp/flash-attention/hopper/dist/${WHEEL_FILE} /${WHEEL_FILE}

#!/bin/bash

set -eu -o pipefail

main() {
    declare -r harbor="https://harbor-master.r.augmentcode.com"
    declare -r project="master"
    declare -r tag="main"
    declare -r auth_secname="secret/registry-taggers"
    declare -r auth="$(kubectl get "$auth_secname" --template='{{.data.username|base64decode}}:{{.data.password|base64decode}}')"
    declare -r basedir="$(readlink -f "$(dirname "$0")")"
    declare -ra tagfiles=( "$basedir"/../*_tag.txt )

    declare tagfile
    for tagfile in "${tagfiles[@]}"; do
        set_tag "$auth" "$harbor" "$project" "$(read_tagfile "$tagfile")" "$tag"
    done

    declare repl_id repl_name
    while read -r repl_id repl_name; do
        trigger_replication "$auth" "$harbor" "$repl_id" "$repl_name"
    done < <(list_event_based_replications "$auth" "$harbor")
}

read_tagfile() {
   printf "%s" $(<"$1")
}

set_tag() {
    declare -r auth="$1" harbor="$2" project="$3" image="$4" totag="$5"
    declare -r ct="application/vnd.docker.distribution.manifest.v2+json"

    declare -r repo="$(parse_image_repo "$image")"
    declare -r fromtag="$(parse_image_tag "$image")"
    declare -r manifest="$(api_call "$auth" "$harbor" "$project/$repo/manifests/$fromtag" -X GET -H "Accept: $ct")"

    api_call "$auth" "$harbor" "$project/$repo/manifests/$totag" -X PUT --data "$manifest" --fail-with-body -H "Content-Type: $ct" >&2
    printf "%s@%s -> %s\n" "$repo" "$totag" "$fromtag"
}

api_call() {
    declare -r auth="$1" harbor="$2" path="$3"
    shift 3
    curl -sL "$harbor/v2/$path" "$@" -K- <<<"--user \"$auth\""
}

h_api_call() {
    declare -r auth="$1" harbor="$2" path="$3"
    shift 3
    curl -sL "$harbor/api/v2.0/$path" "$@" -K- <<<"--user \"$auth\""
}

parse_image_repo() {
  declare -r image="$1"
  printf "%s" "${image%:*}"
}

parse_image_tag() {
  declare -r image="$1"
  printf "%s" "${image##*:}"
}

list_event_based_replications() {
    declare -r auth="$1" harbor="$2"
    h_api_call "$auth" "$harbor" "replication/policies" | jq -r '.[] | select(.trigger.type=="event_based") | select(.enabled!=false) | [.id, .name] | @tsv' -r
}

trigger_replication() {
    declare -r auth="$1" harbor="$2" repl_name="$4"
    declare -ri repl_id="$3"
    printf "Triggering Replication %s [%d]...\n" "$repl_name" "$repl_id" >&2
    h_api_call "$auth" "$harbor" "replication/executions" -X POST -H "Content-Type: application/json" -d "{\"policy_id\": $repl_id}"
}

main "$@"

#!/usr/bin/env python3
"""Script to query CI logs from loki.

Example invocation:
    ci-logs 2001 --test-path research/gpt-neox --since 840m

The tool has one mandtory argument, the PR number of the tests.
Additional arguments can follow running logs, set a time window, or
limit the output to a particular test directory.

usage: ci-logs [-h] [--test-path TEST_PATH] [--since SINCE] [-f] pr_num

positional arguments:
  pr_num                PR Number

optional arguments:
  -h, --help            show this help message and exit
  --test-path TEST_PATH
  --since SINCE         Start time for search (default 120m)
  -f, --follow

To see output for a specific test, supply a “test-path” argument.
To expand the query window, supply a “since” argument (default 120m)

There is a limit of 5000 log lines returned by the backend;
if you hit that limit, a warning will be printed, and you can
restrict the search by modifying the “since” value.
"""

import argparse
import subprocess
import sys

loki_url = "http://loki-loki-distributed-query-frontend:3100"
logcli = "/usr/local/bin/logcli"  # install location on dev container

since = "120m"
maxlines = 5000  # logcli limit

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("pr_num", type=int, help="PR Number")
    parser.add_argument("--test-path", type=str, required=False)
    parser.add_argument(
        "--since",
        type=str,
        required=False,
        help="Start time for search (default 120m)",
        default=since,
    )
    parser.add_argument(
        "-f", "--follow", action="store_true", required=False, default=False
    )
    args = parser.parse_args()
    if not args.test_path:
        query = f'{{github_pr="{args.pr_num}"}}'
    else:
        query = f'{{github_pr="{args.pr_num}",test_path="{args.test_path}"}}'

    cmd = [
        logcli,
        f"--addr={loki_url}",
        "query",
        "--forward",
        f"--since={args.since}",
        query,
    ]
    if not args.test_path:
        cmd.append("--include-label=test_path")
    else:
        cmd.append("--no-labels")
    if args.follow:
        cmd.append("--follow")
    else:
        cmd.append(f"--limit={maxlines}")

    print(f"Running '{' '.join(cmd)}'\n", file=sys.stderr)
    total_lines = 0
    with subprocess.Popen(
        cmd, stdout=subprocess.PIPE, bufsize=1, text=True, encoding="utf-8"
    ) as logp:
        while True:
            if logp.poll():
                print(f"Command exited with return code {logp.returncode}")
            assert logp.stdout
            output = logp.stdout.readline()
            if not output:
                break
            total_lines += 1
            print(output.rstrip())
            if total_lines == maxlines:
                print(
                    f"\nWARNING: {total_lines} lines returned, output may be truncated",
                    file=sys.stderr,
                )
                print(
                    "         Re-run with shorter time window to capture missing logs\n",
                    file=sys.stderr,
                )

    print(f"Command exited with return code {logp.returncode}")

#!/bin/bash

set -eu -o pipefail

main() {
   declare -r cfg="${1:-"$(readlink -f "$(dirname "$0")")/image-sync-test.cfg.txt"}"
   (( $# > 1 )) && shift

   if ! [[ -r "$cfg" ]]; then
      printf "Config file %s is not readable.\n" "$cfg"
      return 2
   fi

   # Read images from ../*_tag.txt or from an explicit list given on the command line.
   declare -ra images=( $(list_images "$@") )

   # Check URLs in sorted order so debugging is cleaner.
   declare -i code good=0 bad=0 total=0
   while read -r url auth; do
      # We need to manually check outside of the process subsitution subshell below for an error.
      if [[ "$url" == "ERROR" ]]; then
         printf "An error was detected, probable stderr info above. Exiting.\n" >&2
         exit 42
      fi

      code="$(api_head_code "$auth" "$url")"
      if (( code == 200 )); then
         mark="✔"
         good+=1
         total+=1
      else
         mark="✘"
         bad+=1
         total+=1
      fi
      printf "%s %03d %s\n" "$mark" "$code" "$url"
   done < <(get_url_auth_lines "$cfg" "${images[@]}" | sort || echo "ERROR" "ERROR")

   if (( bad > 0 )); then
      declare -ri ret=1
      declare -r msg=FAIL
   else
      declare -ri ret=0
      declare -r msg=SUCCESS
   fi

   printf "%s: %d/%d GOOD; %d/%d BAD\n" "$msg" "$good" "$total" "$bad" "$total"
   return "$ret"

}

list_images() {
   if (( $# == 0 )); then
      declare -r basedir="$(readlink -f "$(dirname $0)")"
      declare -ra tagfiles=( "$basedir"/../*_tag.txt )
   else
      declare -ra tagfiles=( "$@" )
   fi

   declare tagfile image
   for tagfile in "${tagfiles[@]}"; do
      echo $(read_tagfile "$tagfile")
   done \
   | sort
}

read_tagfile() {
   printf "%s" $(<"$1")
}

# Using the cfg file, a list of images, and a k8s secret, print out lines with a URL and USER:PASS.
get_url_auth_lines() {
   declare -r cfg="$1"
   shift
   declare -ra images=( "$@" )

   ## Parse cfg file

   declare auth_sec_name
   declare -A registry_url
   declare -A registry_hostname
   declare -A image_dests

   while read -r line; do

      # Skip empty lines and those starting with a '#' comment.
      line=$(echo -n "$line") # strip whitespace
      line="${line%%#*}" # strip comments
      if [[ -z "$line" ]]; then
         continue
      fi

      # Break line into first word and remaining.
      read -r key line <<<"$line"

      case "$key" in
         AUTH_SEC_NAME)
            auth_sec_name="$line"
            ;;
         REGISTRY)
            read -r registry url <<<"$line"
            registry_url["$registry"]="$url"
            registry_hostname["$registry"]="$(parse_registry_hostname "$url")"
            ;;
         IMAGE)
            read -r image dests <<<"$line"
            image_dests["$image"]="$dests"
            ;;
      esac

   done < "$cfg"

   ## Read auth details from k8s.

   declare -A auths
   while read -r hostname userpass; do
      auths["$hostname"]="$userpass"
   done < <(kubectl get "$auth_sec_name" --template='{{index .data ".dockerconfigjson" | base64decode}}' | jq -r '.auths | to_entries[] | "\(.key) \(.value.auth | @base64d)"')

   ## Format URLs for each IMAGE,DEST pair. Print to stdout.

   for image in "${images[@]}"; do
      repo="$(parse_image_repo "$image")"
      tag="$(parse_image_tag "$image")"

      case "${image_dests["$repo"]}" in
         'x') continue;;
         '*') dests=( "${!registry_url[@]}" );;
         *) dests=( ${image_dests["$repo"]} );;
      esac

      for dest in "${dests[@]}"; do
         base_url="${registry_url["$dest"]}"
         auth="${auths["${registry_hostname["$dest"]}"]}"

         printf "%s/%s/manifests/%s %s\n" "$base_url" "$repo" "$tag" "$auth"
      done
   done
}

api_head_code() {
  declare -r auth="$1"
  declare -r url="$2"
  curl -sLI "$url" -w "%{http_code}" -o /dev/null -K- <<<"--user \"$auth\""
}

parse_registry_hostname() {
   declare -r url="$1"
   declare -r without_proto="${url#https://}"
   declare -r hostname="${without_proto%%/*}"
   printf "%s" "$hostname"
}

parse_image_repo() {
  declare -r image="$1"
  printf "%s" "${image%:*}"
}

parse_image_tag() {
  declare -r image="$1"
  printf "%s" "${image##*:}"
}

main "$@"

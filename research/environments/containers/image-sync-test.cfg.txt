# This file configures image-sync-test.sh. It's a mapping from a base image name
# to one or more destination repository aliases. If no destinations are given, or '*',
# then all destinations are expected. If the destination is 'x' then no destinations are
# checked.

# A k8s secret with a Docker config.json with auths for the registry hostnames (not full URL) below.
AUTH_SEC_NAME secret/registry-readers

REGISTRY master     https://harbor-master.r.augmentcode.com/v2/master
REGISTRY cw-east4   https://registry.cw-east4.r.augmentcode.com/v2/infra
REGISTRY gcp-us1    https://us-central1-docker.pkg.dev/v2/augment-research-gsc/docker-us-central1

IMAGE augment_ci_worker            master gcp-us1
IMAGE augment_cpu                  *
IMAGE augment_devpod_cpu           *
IMAGE augment_devpod_gpu           *
IMAGE augment_gpu                  *
IMAGE augment_spark_python         *
IMAGE augment_spark_python_gpu     *
IMAGE augment_vm_cpu               master # TODO(mattm): delete
IMAGE augment_vm_gpu               master # TODO(mattm): delete
IMAGE gh_runner                    master gcp-us1

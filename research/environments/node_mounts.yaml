CW:
  - name: aug-cw-las1
    path: /mnt/efs/augment
  - name: aug-cw-las1-spark-python
    path: /mnt/efs/spark-python
    tags:
    - vast
    - spark
  - name: aug-cw-las1-hdc
    path: /mnt/efs/hdc
    tags:
    - vast
  - name: aug-cw-las1-spark-data
    path: /mnt/efs/spark-data
    tags:
    - vast
    - spark
  - name: aug-cw-las1-ci-testing
    path: /mnt/efs/augment-ci-testing
  - name: determined-sa-secret
    secret: gh-determined-password  # pragma: allowlist secret
  - name: au-docker-reg-docker-registry-secret
    secret: au-docker-reg-docker-registry-secret  # pragma: allowlist secret
  - name: cw-bot-token
    secret: cw-bot-token  # pragma: allowlist secret
  - name: eval-token
    secret: eval-token  # pragma: allowlist secret
CW-EAST4:
  - name: aug-checkpoints
    path: /mnt/efs/augment/checkpoints
  - name: aug-ci-testing
    path: /mnt/efs/augment-ci-testing
  - name: aug-configs
    path: /mnt/efs/augment/configs
  - name: aug-data
    path: /mnt/efs/augment/data
  - name: aug-eval
    path: /mnt/efs/augment/eval
  - name: aug-external-models
    path: /mnt/efs/augment/external_models
  - name: aug-ftm-checkpoints
    path: /mnt/efs/augment/ftm_checkpoints
  - name: aug-lib
    path: /mnt/efs/augment/lib
  - name: aug-spark-data
    path: /mnt/efs/spark-data
  - name: aug-user
    path: /mnt/efs/augment/user
  - name: cw-bot-token
    secret: cw-bot-token  # pragma: allowlist secret
  - name: eval-token
    secret: eval-token  # pragma: allowlist secret
  - name: determined-sa-key
    secret: determined-sa-key  # pragma: allowlist secret
GCP-US1:
  - name: aug-checkpoints
    path: /mnt/efs/augment/checkpoints
    bucketName: gcp-us1-checkpoints
    tags:
    - spark
    - read-only
  - name: aug-configs
    path: /mnt/efs/augment/configs
    bucketName: gcp-us1-configs
    tags: []
  - name: aug-data
    path: /mnt/efs/augment/data
    bucketName: gcp-us1-data
    tags: []
  - name: aug-eval
    path: /mnt/efs/augment/eval
    bucketName: gcp-us1-eval
    tags: []
  - name: aug-external-models
    path: /mnt/efs/augment/external_models
    bucketName: gcp-us1-external-models
    tags:
    - spark
    - read-only
  - name: aug-ftm-checkpoints
    path: /mnt/efs/augment/ftm_checkpoints
    bucketName: gcp-us1-ftm-checkpoints
    tags: []
  - name: aug-public-html
    path: /mnt/efs/augment/public_html
    bucketName: gcp-us1-public-html
    tags: []
  - name: aug-python-env
    path: /mnt/efs/augment/python_env
    bucketName: gcp-us1-python-env
    tags:
    - spark
    - read-only
  - name: aug-user
    path: /mnt/efs/augment/user
    bucketName: gcp-us1-user
    tags:
    - spark
  - name: aug-spark-data
    path: /mnt/efs/spark-data
    bucketName: gcp-us1-spark-data
    tags:
    - spark
  - name: aug-ci-testing
    path: /mnt/efs/augment-ci-testing
    bucketName: gcp-us1-augment-ci-testing
    tags: []
  - name: cw-bot-token
    secret: cw-bot-token  # pragma: allowlist secret
  - name: registry-readers
    secret: registry-readers  # pragma: allowlist secret
  - name: determined-sa-secret
    secret: gh-determined-password  # pragma: allowlist secret
  - name: eval-token
    secret: eval-token  # pragma: allowlist secret

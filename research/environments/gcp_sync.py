#!/usr/bin/env python3
"""Sync files to/from GCP-singapore.

The quickest sync method (empirically) appears to be
- Sync from CW to a gcs bucket in singapore
- Sync from gcs bucket in singapore to a filesystem in singapore

The reverse is also true, push from gcp-sing to gcs-US, pull to CW.

The configuration option equivalents in the ~/.boto file to enable parallelism are:
upload:
    parallel_composite_upload_threshold = 1G
    parallel_composite_upload_component_size = 50M
download:
    sliced_object_download_threshold = 1G
    sliced_object_download_component_size = 100M
    sliced_object_download_max_components = 60

Usage:
    gcp_sync.py source_path dest_path

"""

import argparse
import subprocess
import sys
from pathlib import Path

from research.environments.providers import detect_provider


gsutil_options = [
    "-m",
    "-o",
    "GSUtil:parallel_composite_upload_threshold=1G",
    "-o",
    "GSUtil:parallel_composite_upload_component_size=50M",
    "-o",
    "GSUtil:sliced_object_download_threshold=1G",
    "-o",
    "GSUtil:sliced_object_download_component_size=100M",
    "-o",
    "GSUtil:sliced_object_download_max_components=60",
]


def bucket_for_path(path: Path) -> tuple[str, str]:
    """Return the proper bucket for a given sync path."""
    # Throw an error if it's not under /mnt/efs/augment
    try:
        relpath = path.relative_to("/mnt/efs/augment")
        syncdir = relpath.parts[0].replace("_", "-")
        bucket = f"gcp-us1-{syncdir}"
        syncpath = relpath.relative_to(relpath.parts[0])
    except ValueError:
        try:
            syncpath = path.relative_to("/mnt/efs/spark-data")
            bucket = "gcp-us1-spark-data"
        except ValueError:
            raise ValueError(
                f"Path {path} is not under /mnt/efs/augment or /mnt/efs/spark-data"
            )
    return bucket, str(syncpath)


def sync(
    source_path: Path,
) -> bool:
    """Sync files to a gcp bucket from a CW filesystem."""
    # Verify we're actually in CW
    if detect_provider() != "CW":
        print("Can't sync from non-CW cluster")
        return False
    # Handle compound paths in fastbackward
    if ";" in source_path.as_posix():
        print(f"Splitting compound path {source_path.as_posix()} on ';'")
        cp = source_path.as_posix().split(";")
        for path in cp:
            sync(Path(path))
    else:
        # Handle name1@path format
        # NOTE - this blows up on paths with '@' in them,
        #   this is feature-parity with the FB code
        if "@" in source_path.as_posix():
            print(f"Splitting {source_path.as_posix()} on '@'")
            _, sp = source_path.as_posix().split("@")
            source_path = Path(sp)
        bucket_name, sync_path = bucket_for_path(source_path)
        sync_to_bucket(bucket_name, str(source_path), str(sync_path))
    return True


def sync_to_bucket(
    bucket_name: str,
    source_path: str,
    dest_path: str,
) -> None:
    """Sync files to a bucket in GCP"""
    if bucket_name.endswith("/"):
        bucket_name = bucket_name[:-1]
    if dest_path.startswith("/"):
        dest_path = dest_path[1:]
    gsurl = f"gs://{bucket_name}/{dest_path}"

    gsutil_cmd = (
        ["gsutil"]
        + gsutil_options
        + [
            "rsync",
            "-Pr",
            source_path,
            gsurl,
        ]
    )
    print(f"Syncing to {gsurl}")
    print(" ".join([str(x) for x in gsutil_cmd]))

    subprocess.check_call(gsutil_cmd)


def main() -> int:
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "source_path",
        type=Path,
        help="source path [dest path], must be absolute",
        nargs="+",
    )
    parser.add_argument(
        "-P",
        "--show-progress",
        action="store_true",
        default=False,
        help="Deprecated, ignored",
    )
    args = parser.parse_args()

    source_path: Path = args.source_path[0]
    # If no dest path is given, use the source path
    if len(args.source_path) > 1:
        dest_path = args.source_path[1]
    else:
        dest_path = source_path

    assert source_path.is_absolute() and dest_path.is_absolute()
    if sync(source_path):
        return 0
    return 1


if __name__ == "__main__":
    sys.exit(main())

"""Utils and script for reading a csv of hindsight runs and compiling them into tables.

See the file in example usage for the records.csv format.

Example usages:
python research/tools/analyze_hindsight.py \
    --records=experimental/jeff/analysis/hindsight/records-2024-07-01.csv \
    --output_dir=experimental/jeff/analysis/hindsight/2024-07-01/

python research/tools/analyze_hindsight.py \
    --records=experimental/jeff/analysis/hindsight/records-2024-05-01.csv \
    --output_dir=experimental/jeff/analysis/hindsight/2024-05-01/
    --summary=star

Then download, e.g. right click experimental/jeff/analysis/hindsight/2024-05-01/
and upload each csv to Google Sheets.
"""

import argparse
import datetime
import logging
from dataclasses import dataclass
from pathlib import Path

import pandas as pd
import yaml
from dataclasses_json import DataClassJsonMixin
from tqdm.auto import tqdm

from base.datasets.completion import (
    CompletionDatum,
    CompletionPosition,
    CompletionRequest,
    CompletionResponse,
)
from base.datasets.hindsight_completion import HindsightCompletionDatum
from research.core.utils_for_file import read_jsonl_zst
from research.eval.harness.metrics import (
    CodeCompleteMetrics,
    DatumTag,
    compute_code_complete_metrics,
)
from research.eval.harness.tasks.hindsight import (
    HindsightOutput,
    convert_hindsight_datum_to_model_input,
    get_hindsight_tags,
)

logger = logging.getLogger(__name__)


@dataclass
class RunRecord(DataClassJsonMixin):
    """A record of a run."""

    dataset: str
    model: str
    override_model: bool
    determined_link: str
    output_path: str

    @property
    def run_name(self) -> str:
        return "; ".join([self.dataset, self.model])


def read_records(records_path: Path | str) -> list[RunRecord]:
    """Read records from a csv."""
    records_path = Path(records_path)
    records = pd.read_csv(records_path)
    records = records.to_dict(orient="records")
    records = RunRecord.schema().load(records, many=True)  # type: ignore
    return records


def load_datum(datum: dict):
    """Load HindsightCompletionDatum from dict.

    Some entries may be missing for speed reasons.
    """
    # from_dict and schema().load() are kind of slow, so manually load.
    completion = datum["completion"]
    request = completion["request"]
    response = completion["response"]
    return HindsightCompletionDatum(
        completion=CompletionDatum(
            request_id=completion["request_id"],
            user_id=completion["user_id"],
            request=CompletionRequest(
                prefix=request["prefix"],
                suffix=request["suffix"],
                path=request["path"],
                blob_names=[],
                output_len=request["output_len"],
                timestamp=datetime.datetime.fromtimestamp(request["timestamp"]),
                position=CompletionPosition(
                    prefix_begin=request["position"]["prefix_begin"],
                    cursor_position=request["position"]["cursor_position"],
                    suffix_end=request["position"]["suffix_end"],
                    blob_name=request["position"]["blob_name"],
                    original_prefix_length=request["position"][
                        "original_prefix_length"
                    ],
                    original_suffix_length=request["position"][
                        "original_suffix_length"
                    ],
                )
                if "position" in request
                else None,
                recency_info=None,
            ),
            response=CompletionResponse(
                text=response["text"],
                model=response["model"],
                skipped_suffix=response["skipped_suffix"],
                suffix_replacement_text=response["suffix_replacement_text"],
                unknown_blob_names=response["unknown_blob_names"],
                retrieved_chunks=[],
                timestamp=datetime.datetime.fromtimestamp(response["timestamp"]),
                tokens=[],
                token_log_probs=[],
                prompt_tokens=[],
            ),
            resolution=None,
            feedback=None,
            inference_response=None,
            user_agent=None,
        ),
        ground_truth=datum["ground_truth"],
    )


def read_dataset(path: Path | str) -> dict[str, HindsightCompletionDatum]:
    """Read a dataset path into a dict from request id to datum."""
    logger.info(f"Reading dataset from {path}")
    data = read_jsonl_zst(path)
    data = [load_datum(datum) for datum in tqdm(data)]
    count = len(data)
    data = {datum.completion.request_id: datum for datum in data}
    assert count == len(data)
    return data


def lookup_file(path: Path | str, pattern: str) -> Path:
    """Finds a single file using the given pattern."""
    path = Path(path)
    files = list(path.glob(pattern))
    assert len(files) == 1, files
    return files[0]


def get_run_request_id(output: HindsightOutput):
    return [
        artifact["request_ids"][-1]
        for artifact in output.artifacts
        if "request_ids" in artifact
    ][0]


def load_output(output: dict) -> HindsightOutput:
    """Load HindsightOutput from dict.

    Some entries may be missing for speed reasons.
    """
    # from_dict and schema().load() are kind of slow, so manually load.
    return HindsightOutput(
        request_id=output["request_id"],
        path=output["path"],
        prefix="",
        suffix="",
        prompt="",
        prompt_tokens=[],
        generation=output["generation"],
        ground_truth=output["ground_truth"],
        metrics=CodeCompleteMetrics(
            exact_match=None,
            exact_match_1_line=None,
            prefix_f1=None,
            prefix_precision=None,
            prefix_recall=None,
            lcs_f1=None,
            lcs_precision=None,
            lcs_recall=None,
            log_likelihood=None,
            token_accuracy=None,
        ),
        tags=[],
        artifacts=output["artifacts"],
    )


def read_outputs(run_dir: Path | str) -> list[HindsightOutput]:
    """Reads a run into a list of outputs."""
    outputs = read_jsonl_zst(lookup_file(run_dir, "*_completed_patches.jsonl.zst"))
    outputs = [load_output(output) for output in outputs]
    return outputs


def get_run_tags(run_record: RunRecord) -> list[DatumTag]:
    """Get tags for a run."""
    with open(lookup_file(run_record.output_path, "*_config.yml")) as f:
        config = yaml.safe_load(f)

    dataset = config["task"]["dataset"]
    tenant = config["task"]["tenant_name"]
    model = run_record.model

    # Verify that the run record matches the config.
    assert run_record.dataset == f"{dataset}/{tenant}"
    if not run_record.override_model:
        assert config["system"]["name"] == "remote_completion"
        assert config["system"]["model_name"] == model, (config, run_record)

    tags = []
    tags.append(DatumTag(category="dataset", value=dataset))
    tags.append(DatumTag(category="tenant", value=tenant))
    tags.append(DatumTag(category="model", value=model))
    return tags


def get_datum_tags(datum: HindsightCompletionDatum) -> list[DatumTag]:
    """Get tags for a datum."""
    model_input = convert_hindsight_datum_to_model_input(datum)
    tags = get_hindsight_tags(model_input, datum)
    return tags


@dataclass
class HindsightRun:
    """A hindsight run, including tags and outputs."""

    record: RunRecord
    tags: list[DatumTag]
    outputs: list[HindsightOutput]


def read_runs(run_records: list[RunRecord]) -> dict[str, HindsightRun]:
    """Read runs from a list of run records."""
    runs = {
        run.run_name: HindsightRun(run, tags, read_outputs(run.output_path))
        for run in tqdm(run_records)
        if (tags := get_run_tags(run))
    }
    assert len(runs) == len(run_records)
    return runs


def read_data(run_records: list[RunRecord]) -> dict[str, HindsightCompletionDatum]:
    """Read data from the given run records."""
    datasets = sorted(set(record.dataset for record in run_records))
    paths = [
        Path(f"/mnt/efs/augment/data/eval/hindsight/{dataset}/data.jsonl.zst")
        for dataset in datasets
    ]
    data_list = [read_dataset(path) for path in paths]
    total_data_count = sum(len(d) for d in data_list)
    data = {k: v for d in data_list for k, v in d.items()}
    assert len(data) == total_data_count
    return data


def compute_metrics(output: HindsightOutput) -> dict[str, float]:
    """Compute metrics for an output."""
    prediction = output.generation
    target = output.ground_truth
    metrics = compute_code_complete_metrics(prediction, target).to_float_dict()
    return metrics


def make_df(
    runs: dict[str, HindsightRun], data: dict[str, HindsightCompletionDatum]
) -> pd.DataFrame:
    """Make a dataframe from the given runs and data."""
    rows = []
    for _, run in runs.items():
        for output in run.outputs:
            datum = data[output.request_id]

            tags = get_datum_tags(datum)
            row = {}
            row.update(compute_metrics(output))

            for tag in run.tags:
                row[tag.category] = tag.value
            for tag in tags:
                row[tag.category] = tag.value
            rows.append(row)

    df = pd.DataFrame(rows)
    return df


def mean_by_group(agg_df: pd.DataFrame, group_by: list[str]):
    """Group the dataframe by the given columns and compute mean and count."""
    agg_df = (
        agg_df.groupby(group_by)
        .mean(numeric_only=True)
        .join(
            agg_df.groupby(group_by)
            .count()
            .rename(columns={"exact_match": "count"})["count"],
            on=group_by,
        )
    )
    agg_df = agg_df.reset_index().sort_values(group_by)
    return agg_df


def summarize_all(df: pd.DataFrame):
    """Summarize all data."""
    agg_df = df.copy()
    agg_df = mean_by_group(agg_df, group_by=["response_model", "model"])
    return agg_df


def summarize_non_comments(df: pd.DataFrame):
    """Summarize non-comments."""
    agg_df = df.copy()
    agg_df = agg_df.query("comment == 'code'")
    agg_df = mean_by_group(agg_df, group_by=["response_model", "model"])
    return agg_df


def summarize_non_comments_non_test(df: pd.DataFrame):
    """Summarize non-comments and non-test."""
    agg_df = df.copy()
    agg_df = agg_df.query("comment == 'code'")
    agg_df = agg_df.query("test == 'non_test'")
    agg_df = mean_by_group(agg_df, group_by=["response_model", "model"])
    return agg_df


def summarize_non_comments_test(df: pd.DataFrame):
    """Summarize non-comments and test."""
    agg_df = df.copy()
    agg_df = agg_df.query("comment == 'code'")
    agg_df = agg_df.query("test == 'test'")
    agg_df = mean_by_group(agg_df, group_by=["response_model", "model"])
    return agg_df


def summarize_comments(df: pd.DataFrame):
    """Summarize comments."""
    agg_df = df.copy()
    agg_df = agg_df.query("comment != 'code'")
    agg_df = mean_by_group(agg_df, group_by=["response_model", "model"])
    return agg_df


def assign_response_model_star_type(df: pd.DataFrame):
    """Assign whether the response model is star1, star2, or other."""
    df = df.assign(
        response_model_star_type=df["response_model"].apply(
            lambda x: "star1"
            if (first_part := x.split("-")[0]) in {"roguesl"}
            else "star2"
            if first_part in {"elden", "eldenv2", "eldenv3", "star2sl"}
            else "other"
        )
    )
    return df


def summarize_all_by_star(df: pd.DataFrame):
    """Summarize all data by response model star type."""
    agg_df = df.copy()
    agg_df = assign_response_model_star_type(agg_df)
    agg_df = mean_by_group(agg_df, group_by=["response_model_star_type", "model"])
    return agg_df


def summarize_non_comments_by_star(df: pd.DataFrame):
    """Summarize non-comments by star model."""
    agg_df = df.copy()
    agg_df = agg_df.query("comment == 'code'")
    agg_df = assign_response_model_star_type(agg_df)
    agg_df = mean_by_group(agg_df, group_by=["response_model_star_type", "model"])
    return agg_df


def summarize_non_comments_non_test_by_star(df: pd.DataFrame):
    """Summarize non-comments and non-test by star model."""
    agg_df = df.copy()
    agg_df = agg_df.query("comment == 'code'")
    agg_df = agg_df.query("test == 'non_test'")
    agg_df = assign_response_model_star_type(agg_df)
    agg_df = mean_by_group(agg_df, group_by=["response_model_star_type", "model"])
    return agg_df


def summarize_non_comments_test_by_star(df: pd.DataFrame):
    """Summarize non-comments and test by star model."""
    agg_df = df.copy()
    agg_df = agg_df.query("comment == 'code'")
    agg_df = agg_df.query("test == 'test'")
    agg_df = assign_response_model_star_type(agg_df)
    agg_df = mean_by_group(agg_df, group_by=["response_model_star_type", "model"])
    return agg_df


def summarize_comments_by_star(df: pd.DataFrame):
    """Summarize comments by star model."""
    agg_df = df.copy()
    agg_df = agg_df.query("comment != 'code'")
    agg_df = assign_response_model_star_type(agg_df)
    agg_df = mean_by_group(agg_df, group_by=["response_model_star_type", "model"])
    return agg_df


SUMMARIES = {
    "all": {
        "all": summarize_all,
        "non_comments": summarize_non_comments,
        "non_comments_non_test": summarize_non_comments_non_test,
        "non_comments_test": summarize_non_comments_test,
        "comments": summarize_comments,
    },
    "star": {
        "all": summarize_all_by_star,
        "non_comments": summarize_non_comments_by_star,
        "non_comments_non_test": summarize_non_comments_non_test_by_star,
        "non_comments_test": summarize_non_comments_test_by_star,
        "comments": summarize_comments_by_star,
    },
}


def make_combined_df(summary_dfs: list[pd.DataFrame]) -> pd.DataFrame:
    """Combine all dataframes into a single one."""
    df = pd.concat(summary_dfs, ignore_index=True)
    df = df[["summary"] + [col for col in df.columns if col != "summary"]]
    assert isinstance(df, pd.DataFrame)
    return df


def main():
    logging.basicConfig(level=logging.DEBUG)

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--records", type=Path, help="Path to a csv of runs", required=True
    )
    parser.add_argument(
        "--output_dir", type=Path, help="Path to output analyses", required=True
    )
    parser.add_argument(
        "--datasets",
        type=str,
        nargs="*",
        help="Datasets to include, none for all",
        default=None,
    )
    parser.add_argument(
        "--models",
        type=str,
        nargs="*",
        help="Models to include, none for all",
        default=None,
    )
    parser.add_argument(
        "--summary",
        type=str,
        help="Summary set to generate",
        choices=list(SUMMARIES.keys()),
        default="all",
    )

    args = parser.parse_args()

    logger.info(f"Args: {args}")
    records = read_records(args.records)
    if args.datasets:
        records = [record for record in records if record.dataset in args.datasets]
    if args.models:
        records = [record for record in records if record.model in args.models]
    logger.info(f"Read {len(records)} records")
    data = read_data(records)
    logger.info(f"Read {len(data)} data")
    runs = read_runs(records)
    logger.info(f"Read {len(runs)} runs")

    df = make_df(runs, data)
    args.output_dir.mkdir(exist_ok=True, parents=True)
    summary_dfs = []
    for name, func in SUMMARIES[args.summary].items():
        logger.info(f"Generating summary: {name}")
        agg_df = func(df)
        agg_df.to_csv(args.output_dir / f"{name}.csv", index=False)
        summary_dfs.append(agg_df.assign(summary=name))

    # Concat and move summary column to the front.
    logger.info("Generating combined summary")
    combined_df = make_combined_df(summary_dfs)
    combined_df.to_csv(args.output_dir / "combined.csv", index=False)


if __name__ == "__main__":
    main()

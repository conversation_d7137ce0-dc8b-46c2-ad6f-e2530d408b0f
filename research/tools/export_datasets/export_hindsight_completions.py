"""Export hindsight completion samples from our vendors and from dogfood.

In order to run this in a CW dev container, the following is needed:

1. Authenticate with Google:
```bash
gcloud auth login
gcloud auth application-default login
```
2. Generate the proto library files (do periodically):
```bash
bazel run //tools/generate_proto_typestubs
```
"""

import argparse
import json
import logging
from collections import Counter
from collections.abc import Iterable, Mapping
from datetime import datetime, timedelta, timezone
from pathlib import Path, PurePath
from typing import Any

import zstandard as zstd

from base.datasets.hindsight_completion_dataset import (
    HindsightCompletionDataset,
    HindsightCompletionProcessArgs,
    HindsightCompletionProcessResults,
    HindsightCompletionQueryArgs,
    HindsightCompletionQueryResults,
    NoCompletionEventError,
    query_and_process,
)
from base.datasets.tenants import get_tenant
from base.datasets.user_event import CompletionRequestIdIssuedEvent


def num_lines(text: str) -> int:
    """Get the number of lines in the given text."""
    return text.count("\n") + 1


def to_date_str(time: datetime) -> str:
    """Bucket the datetime as a string date."""
    return time.astimezone(tz=timezone.utc).date().isoformat()


def sorted_mapping(mapping: Mapping) -> dict:
    """Sorts the mapping by key and returns a dict."""
    return dict(sorted(mapping.items()))


def get_counts(items: Iterable) -> dict[Any, int]:
    """Get a mapping of counts of items, sorted by key."""
    return sorted_mapping(Counter(items))


def round_sig_figs_1(x: int) -> int:
    """Round to 1 significant figure, e.g. 19 -> 20."""
    return int(float(f"{x:.1g}"))


def generate_summary(
    tenant_name: str,
    timestamp_begin: datetime,
    timestamp_end: datetime,
    query_args: HindsightCompletionQueryArgs,
    process_args: HindsightCompletionProcessArgs,
    query_results: HindsightCompletionQueryResults,
    process_results: HindsightCompletionProcessResults,
) -> dict:
    """Generate a summary of the data."""

    all_completion_events = [
        event
        for event in query_results.events
        if isinstance(event, CompletionRequestIdIssuedEvent)
    ]
    query_summary = {
        "user_events": {
            "total": len(query_results.events),
            "by_day": get_counts(
                to_date_str(event.time) for event in query_results.events
            ),
            "by_lang": get_counts(
                PurePath(event.file_path).suffix for event in query_results.events
            ),
            "by_user_id": get_counts(event.user_id for event in query_results.events),
        },
        "completion_events": {
            "total": len(all_completion_events),
            "by_day": get_counts(
                to_date_str(event.time) for event in all_completion_events
            ),
            "by_lang": get_counts(
                PurePath(event.file_path).suffix for event in all_completion_events
            ),
            "by_user_id": get_counts(event.user_id for event in all_completion_events),
        },
        "sampled_completion_events": {
            "total": len(query_results.completion_events),
            "by_day": get_counts(
                to_date_str(event.time) for event in query_results.completion_events
            ),
            "by_lang": get_counts(
                PurePath(event.file_path).suffix
                for event in query_results.completion_events
            ),
            "by_user_id": get_counts(
                event.user_id for event in query_results.completion_events
            ),
        },
        "completions": {
            "total": len(query_results.completions),
            "by_day": get_counts(
                to_date_str(completion.request.timestamp)
                for completion in query_results.completions
            ),
            "by_lang": get_counts(
                PurePath(completion.request.path).suffix
                for completion in query_results.completions
            ),
            "by_user_id": get_counts(
                completion.user_id for completion in query_results.completions
            ),
            "by_accepted": get_counts(
                completion.resolution.accepted
                for completion in query_results.completions
                if completion.resolution is not None
            ),
            "by_blob_count": get_counts(
                round_sig_figs_1(len(completion.request.blob_names))
                for completion in query_results.completions
            ),
            "by_has_recent_changes": get_counts(
                bool(completion.request.recency_info.recent_changes)
                if completion.request.recency_info
                else False
                for completion in query_results.completions
            ),
        },
    }

    exact_matches = [
        datum.completion.response.text == datum.ground_truth
        for datum in process_results.data
    ]
    exact_match = sum(exact_matches) / len(exact_matches)
    process_summary = {
        "data": {
            "total": len(process_results.data),
            "by_day": get_counts(
                to_date_str(datum.completion.request.timestamp)
                for datum in process_results.data
            ),
            "by_lang": get_counts(
                PurePath(datum.completion.request.path).suffix
                for datum in process_results.data
            ),
            "by_num_lines": get_counts(
                num_lines(datum.ground_truth) for datum in process_results.data
            ),
            "by_user_id": get_counts(
                datum.completion.user_id for datum in process_results.data
            ),
            "by_accepted": get_counts(
                datum.completion.resolution.accepted
                for datum in process_results.data
                if datum.completion.resolution is not None
            ),
            "by_blob_count": get_counts(
                round_sig_figs_1(len(datum.completion.request.blob_names))
                for datum in process_results.data
            ),
            "by_has_recent_changes": get_counts(
                bool(datum.completion.request.recency_info.recent_changes)
                if datum.completion.request.recency_info
                else False
                for datum in process_results.data
            ),
            "response_exact_match": exact_match,
        },
        "errors": {
            "total": len(process_results.errors),
            "by_day": get_counts(
                to_date_str(error.event.time) for error in process_results.errors
            ),
            "by_lang": get_counts(
                PurePath(error.event.file_path).suffix
                for error in process_results.errors
            ),
            "by_type": get_counts(error.error_type for error in process_results.errors),
        },
    }

    return {
        "config": {
            "tenant_name": tenant_name,
            "timestamp_begin": timestamp_begin.isoformat(),
            "timestamp_end": timestamp_end.isoformat(),
            "query_args": HindsightCompletionQueryArgs.schema().dump(query_args),  # type: ignore
            "process_args": HindsightCompletionProcessArgs.schema().dump(process_args),  # type: ignore
        },
        "query": query_summary,
        "process": process_summary,
    }


def main():
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s"
    )

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--output_dir",
        type=Path,
        required=True,
        help="The output directory for the exported data",
    )
    parser.add_argument(
        "--compressed_zst",
        action="store_true",
        default=True,
        help="Whether to compress the output. True will save as data.jsonl.zst",
    )
    parser.add_argument(
        "--vendor",
        type=str,
        required=True,
        help="The vendor, 'aitutor-pareto', 'aitutor-turing', or 'dogfood'",
    )
    parser.add_argument(
        "--sample_limit",
        type=int,
        default=20_000,
        help="The number of samples to export. Non-positive for no limit.",
    )
    parser.add_argument(
        "--user_event_limit",
        type=int,
        default=2_000_000,
        help="Limit on the number of user events to query. Non-positive for no limit.",
    )
    parser.add_argument(
        "--timestamp_begin",
        required=True,
        type=datetime.fromisoformat,
        help="The beginning of the time range to query. "
        "Must be timezone-aware, e.g. 2024-01-01T00:00:00+00:00 for Jan 1 in UTC",
    )
    parser.add_argument(
        "--timestamp_end",
        required=True,
        type=datetime.fromisoformat,
        help="The end of the time range to query"
        "Must be timezone-aware, e.g. 2024-01-01T00:00:00+00:00 for Jan 1 in UTC",
    )
    parser.add_argument(
        "--session_id_begin",
        type=str,
        default=None,
        help="The beginning of the session ID range to query. "
        "If set, only events with session IDs >= than this will be returned.",
    )
    parser.add_argument(
        "--session_id_end",
        type=str,
        default=None,
        help="The end of the session ID range to query. "
        "If set, only events with session IDs < than this will be returned.",
    )
    parser.add_argument(
        "--time_limit_s",
        type=int,
        default=3_600,
        help="Time limit in seconds to watch for events after a request.",
    )
    parser.add_argument(
        "--max_interruption_chars",
        type=int,
        default=0,
        help="The max total number of characters across all interruptions to text edits to a "
        "range. These samples will be filtered out if exceeded.",
    )
    parser.add_argument(
        "--min_blobs",
        type=int,
        default=50,
        help="The minimum number of blobs to consider a completion. "
        "This is used to filter out low-quality examples.",
    )
    parser.add_argument(
        "--allow_overlaps",
        action="store_true",
        default=False,
        help="If False, text edits that touch a range, but are not fully contained by it, "
        "will invalidate the sample. These samples will be filtered out.",
    )
    parser.add_argument(
        "--allow_empty_ground_truth",
        action="store_true",
        default=False,
        help="If False, samples with empty ground truth will be filtered out.",
    )
    parser.add_argument(
        "--context_size",
        type=int,
        default=-1,
        help="The number of characters to include in the context range on either side of the "
        "range. If negative, no context is included *and* change trimming is turned off. "
        "This is used to normalize content changes, e.g. 'ab' -> 'abcd' becomes '' to 'cd'",
    )

    args = parser.parse_args()
    tenant = get_tenant(args.vendor)

    user_event_limit = args.user_event_limit if args.user_event_limit > 0 else None
    sample_limit = args.sample_limit if args.sample_limit > 0 else None

    query_args = HindsightCompletionQueryArgs(
        session_id_begin=args.session_id_begin,
        session_id_end=args.session_id_end,
        user_event_limit=user_event_limit,
        sample_limit=sample_limit,
    )
    process_args = HindsightCompletionProcessArgs(
        time_limit=timedelta(seconds=args.time_limit_s),
        max_interruption_chars=args.max_interruption_chars,
        allow_overlaps=args.allow_overlaps,
        allow_empty_ground_truth=args.allow_empty_ground_truth,
        min_blobs=args.min_blobs,
        context_size=args.context_size if args.context_size >= 0 else None,
    )
    print(f"query_args: {query_args}")
    print(f"process_args: {process_args}")
    try:
        query_results, results = query_and_process(
            tenant,
            args.timestamp_begin,
            args.timestamp_end,
            query_args=query_args,
            process_args=process_args,
        )
    except NoCompletionEventError:
        print(
            "No completion events found. Fix your query, or we are missing data. Skipping the rest of processing."
        )
        return

    blobs = HindsightCompletionDataset.create_blobs_from_gcs(tenant)
    dataset = HindsightCompletionDataset(data=results.data, blobs=blobs)

    if args.compressed_zst:
        export_path = args.output_dir / f"{args.vendor}" / "data.jsonl.zst"
        export_path.parent.mkdir(parents=True, exist_ok=True)
        with zstd.open(export_path, "w", encoding="utf-8") as f:
            dataset.dump_data(f)
    else:
        export_path = args.output_dir / f"{args.vendor}" / "data.jsonl"
        export_path.parent.mkdir(parents=True, exist_ok=True)
        with export_path.open("w") as f:
            dataset.dump_data(f)

    summary = generate_summary(
        tenant.name,
        args.timestamp_begin,
        args.timestamp_end,
        query_args=query_args,
        process_args=process_args,
        query_results=query_results,
        process_results=results,
    )
    summary_path = args.output_dir / f"{args.vendor}" / "processing_summary.json"
    with summary_path.open("w") as f:
        json.dump(summary, f, indent=2)

    print(f"Saved exported data for internal use at: {export_path}")


if __name__ == "__main__":
    main()

"""Export code-edit samples from our vendors and from dogfood.

Used as a stop-gap until our webhook is exporting data.

In order to run this in a CW dev container, the following is needed:

1. Authenticate with Google:
```bash
gcloud auth login
gcloud auth application-default login
```
2. Generate the proto library files (do periodically):
```bash
bazel run //tools/generate_proto_typestubs
```
"""

import argparse
import json
import logging
from pathlib import Path

from base.datasets import edit, edit_dataset, tenants

logging.basicConfig(level=logging.INFO)


def process_edit(datum: edit.EditDatum) -> dict:
    assert datum.resolution

    return {
        "request_id": datum.request_id,
        "user_id": datum.user_id,
        "event_type": "edit",
        "request": {
            "path": datum.request.path,
            "prefix": datum.request.prefix,
            "selected_text": datum.request.selected_text,
            "suffix": datum.request.suffix,
            "position": {
                "prefix_begin": (
                    0
                    if not datum.request.position
                    else datum.request.position.prefix_begin
                ),
                "suffix_end": (
                    0
                    if not datum.request.position
                    else datum.request.position.suffix_end
                ),
            },
            "instruction": datum.request.instruction,
            "file_count": len(datum.request.blob_names),
        },
        "feedback": {
            "is_accepted": datum.resolution.is_accepted,
            "annotated_text": datum.resolution.annotated_text,
            "annotated_instruction": datum.resolution.annotated_instruction,
        },
        "response": {
            "text": datum.response.text,
        },
    }


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--output_dir",
        type=Path,
        default=Path.home(),
        help="The output directory for the exported data",
    )
    parser.add_argument(
        "--vendor",
        type=str,
        required=True,
        help="The vendor, 'pareto', 'turing', or 'dogfood'",
    )
    parser.add_argument(
        "--filter_request_ids",
        type=Path,
        default=None,
        help="Path to a file containing a list of request ids to filter by",
    )
    args = parser.parse_args()

    if args.vendor == "pareto":
        tenant = tenants.AITUTOR_PARETO
    elif args.vendor == "turing":
        tenant = tenants.AITUTOR_TURING
    elif args.vendor == "dogfood":
        tenant = tenants.DOGFOOD
    else:
        raise ValueError(f"Unknown vendor: {args.vendor}")

    if args.filter_request_ids:
        with args.filter_request_ids.open("r") as f:
            denied_request_ids = [x.strip() for x in f.readlines()]
    else:
        denied_request_ids = None

    dataset = edit_dataset.EditDataset.create(
        tenant,
        filters=edit_dataset.EditDataset.Filters(
            with_resolution=True,
            denied_request_ids=denied_request_ids,
        ),
    )

    data = list(dataset.get_entries())
    export_data = [process_edit(datum) for datum in data]

    internal_export_path = (
        args.output_dir / f"{args.vendor}_edits_augment_internal.jsonl"
    )
    external_export_path = args.output_dir / f"{args.vendor}_edits_can_be_shared.jsonl"

    with internal_export_path.open("w") as f:
        for row in data:
            f.write(row.to_json())  # type: ignore
            f.write("\n")

    with external_export_path.open("w") as f:
        for row in export_data:
            f.write(json.dumps(row))
            f.write("\n")

    print(f"Saved exported data for internal use at: {internal_export_path}")
    print(
        f"Saved exported data that can be shared externally at: {external_export_path}"
    )


if __name__ == "__main__":
    main()

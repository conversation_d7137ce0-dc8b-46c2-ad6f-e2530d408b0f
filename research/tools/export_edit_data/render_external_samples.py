"""Convert samples from Turing to HTML files."""

import argparse
import difflib
import json
from pathlib import Path

from pygments import highlight
from pygments.formatters import HtmlFormatter  # pylint: disable=no-name-in-module
from pygments.lexers import DiffLexer  # pylint: disable=no-name-in-module

from research.core import utils_for_str


def get_diff_html(prefix, suffix, middle_before, middle_after):
    prefix = utils_for_str.get_last_n_lines(prefix, 15)
    suffix = utils_for_str.get_first_n_lines(suffix, 15)

    lines_before = middle_before.splitlines(keepends=False)
    lines_after = middle_after.splitlines(keepends=False)
    diff_generator = difflib.unified_diff(lines_before, lines_after, lineterm="", n=100)
    # Drop the first three lines from output that looks like this:
    # ---
    # +++
    # @@ -1,3 +1,3 @@
    #  def foo(bar):
    # -    baz = bar + 1
    # +    baz = bar + 2
    #      return baz
    diff_lines: list = list(diff_generator)
    diff_lines = diff_lines[3:]
    diff_text = "\n".join(diff_lines) + "\n"

    num_prefix_lines = len(prefix.splitlines(keepends=True))
    num_suffix_lines = len(suffix.splitlines(keepends=True))
    num_diff_lines = len(diff_text.splitlines(keepends=True))
    highlight_lines = list(range(1, num_prefix_lines + 1)) + list(
        range(
            num_prefix_lines + num_diff_lines,
            num_prefix_lines + num_diff_lines + num_suffix_lines + 1,
        )
    )

    lexer = DiffLexer()
    formatter = HtmlFormatter(linenos=True, cssclass="code", hl_lines=highlight_lines)
    diff_html = highlight(prefix + diff_text + suffix, lexer, formatter)
    return diff_html


def process_file(file_path: Path, output_dir: Path):
    data = json.loads(file_path.read_text())
    output_path = output_dir / file_path.with_suffix(".html").name

    meta_info = f"""\
File Path: {data["request"]["path"]}
User ID: {data["user_id"]}
Request ID: {data["request_id"]}
"""

    prefix = data["request"]["prefix"]
    suffix = data["request"]["suffix"]
    selected_text = data["request"]["selected_text"]
    model_generation = data["response"]["text"]
    annotated_text = data["feedback"]["annotated_text"]

    if not selected_text.endswith("\n"):
        selected_text += "\n"
    if not model_generation.endswith("\n"):
        model_generation += "\n"
    if not annotated_text.endswith("\n"):
        annotated_text += "\n"

    orig_vs_model = get_diff_html(
        prefix,
        suffix,
        selected_text,
        model_generation,
    )

    model_vs_annotation = get_diff_html(
        prefix,
        suffix,
        model_generation,
        annotated_text,
    )

    orig_vs_annotation = get_diff_html(
        prefix,
        suffix,
        selected_text,
        annotated_text,
    )

    html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Code Visualization</title>
    <style>
        pre {{
            background-color: #f4f4f4;
            border: 0.8px solid #ddd;
            <!-- border-left: 0.5px solid #134da3; -->
            color: #999;
            page-break-inside: avoid;
            font-family: monospace;
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 1.6em;
            max-width: 100%;
            overflow: auto;
            padding: 1em 1.5em;
            display: block;
            word-wrap: break-word;
        }}

        /* from pygments for diff highlighting */
        {HtmlFormatter().get_style_defs(".code").replace(".code .hll { background-color: #ffffcc }", ".code .hll { color: #1111ff }")}
    </style>
</head>
<body>
    <h3>Instruction</h3>
    <pre><code>{data["request"]["instruction"]}</code></pre>

    <h3>Info</h3>
    <pre><code>{meta_info}</code></pre>

    <h3>Original code / model generation</h3>
    {orig_vs_model}

    <h3>Model generation / annotation</h3>
    {model_vs_annotation}

    <h3>Original code / annotation</h3>
    {orig_vs_annotation}
</body>
</html>
"""

    output_path.write_text(html, encoding="utf8")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_dir",
        type=Path,
        required=True,
        help="The input directory that contains .json files",
    )
    parser.add_argument(
        "--output_dir",
        type=Path,
        required=True,
        help="The output directory for storing HTMLs",
    )
    args = parser.parse_args()

    for file_path in args.input_dir.glob("*.json"):
        process_file(file_path, args.output_dir)


if __name__ == "__main__":
    main()

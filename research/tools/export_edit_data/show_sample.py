"""Print some samples."""

import argparse
from pathlib import Path

from colorama import Fore, Style

from base.datasets.edit import EditDat<PERSON>


def show_sample(sample: EditDatum, num_context_lines: int = 5):
    print("=" * 100)
    print(f"user_id: {sample.user_id}")
    print(f"user_agent: {sample.user_agent}")
    print(f"path: {sample.request.path}")
    print(f"time: {sample.request.timestamp}")

    print("=" * 100)
    print(f"{Fore.GREEN}Selected text:{Style.RESET_ALL}")

    some_prefix = "".join(
        sample.request.prefix.splitlines(keepends=True)[-num_context_lines:]
    )
    some_suffix = "".join(
        sample.request.suffix.splitlines(keepends=True)[:num_context_lines]
    )

    print(f"{Fore.CYAN}{some_prefix}{Style.RESET_ALL}", end="")
    print(sample.request.selected_text, end="")
    print(f"{Fore.CYAN}{some_suffix}{Style.RESET_ALL}")

    print("=" * 100)
    print(f"{Fore.GREEN}Model response:{Style.RESET_ALL}")
    print(sample.response.text)

    if sample.resolution is not None:
        print("=" * 100)
        print(f"{Fore.GREEN}Annotation:{Style.RESET_ALL}")
        print(sample.resolution.annotated_text)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--request_id", type=str, required=True, help="The request id")
    parser.add_argument("--path", type=Path, required=True, help="Path to data")
    args = parser.parse_args()

    with args.path.open("r", encoding="utf8") as file:
        for line in file:
            sample = EditDatum.from_json(line)  # type: ignore
            if sample.request_id == args.request_id:
                show_sample(sample)


if __name__ == "__main__":
    main()

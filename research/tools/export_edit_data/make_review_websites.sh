#!/bin/bash

ROOT=/mnt/efs/augment/user/guy/export-code-edits

echo "Making Pareto website..."
JSONL_FILE=$ROOT/pareto_edits_augment_internal.jsonl
WEBSITE_DIR=/mnt/efs/augment/public_html/guy/code-edit-review/pareto
mkdir -p $WEBSITE_DIR
rm -f $WEBSITE_DIR/*
python make_code_edit_sample_htmls.py \
    --input_jsonl $JSONL_FILE \
    --output_dir $WEBSITE_DIR
python make_website.py --root $WEBSITE_DIR

echo "Making Turing website..."
JSONL_FILE=$ROOT/turing_edits_augment_internal.jsonl
WEBSITE_DIR=/mnt/efs/augment/public_html/guy/code-edit-review/turing
mkdir -p $WEBSITE_DIR
rm -f $WEBSITE_DIR/*
python make_code_edit_sample_htmls.py \
    --input_jsonl $JSONL_FILE \
    --output_dir $WEBSITE_DIR
python make_website.py --root $WEBSITE_DIR

echo "Making Dogfood website..."
JSONL_FILE=$ROOT/dogfood_edits_augment_internal.jsonl
WEBSITE_DIR=/mnt/efs/augment/public_html/guy/code-edit-review/dogfood
mkdir -p $WEBSITE_DIR
rm -f $WEBSITE_DIR/*
python make_code_edit_sample_htmls.py \
    --input_jsonl $JSONL_FILE \
    --output_dir $WEBSITE_DIR
python make_website.py --root $WEBSITE_DIR

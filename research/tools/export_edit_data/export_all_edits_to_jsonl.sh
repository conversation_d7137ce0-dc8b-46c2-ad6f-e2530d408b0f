#!/bin/bash

ROOT=/mnt/efs/augment/user/guy/export-code-edits
SCRIPT_DIR=$(dirname "$0")
mkdir -p $ROOT

echo "Exporting Pareto..."
time python3 export_edits_to_jsonl.py \
    --vendor pareto \
    --output_dir $ROOT \
    --filter_request_ids $SCRIPT_DIR/corrupted_pareto_request_ids.txt

echo "Exporting Turing..."
time python3 export_edits_to_jsonl.py \
    --vendor turing \
    --output_dir $ROOT

echo "Exporting Dogfood..."
time python3 export_edits_to_jsonl.py \
    --vendor dogfood \
    --output_dir $ROOT

"""Prepare and export data for code edits.

Example usage:
python research/tools/export_edit_data/make_code_edit_sample_htmls.py \
    --input_dir /mnt/efs/hdc/code-edit-data/examples \
    --output_dir ~/processed_examples
"""

import argparse
import csv
import difflib
from pathlib import Path

from pygments import highlight
from pygments.formatters import HtmlFormatter  # pylint: disable=no-name-in-module
from pygments.lexers import DiffLexer  # pylint: disable=no-name-in-module

from base.datasets.edit import EditDatum
from research.core import utils_for_str


def get_diff_html(text_before: str, text_after: str) -> str:
    """Compute the diff html using pygments."""
    lines_before = text_before.splitlines(keepends=False)
    lines_after = text_after.splitlines(keepends=False)
    diff_generator = difflib.unified_diff(lines_before, lines_after, lineterm="", n=100)
    # Drop the first three lines from output that looks like this:
    # ---
    # +++
    # @@ -1,3 +1,3 @@
    #  def foo(bar):
    # -    baz = bar + 1
    # +    baz = bar + 2
    #      return baz
    diff_lines: list = list(diff_generator)
    diff_lines = diff_lines[3:]
    diff_text = "\n".join(diff_lines)
    lexer = DiffLexer()
    formatter = HtmlFormatter(linenos=True, cssclass="code")
    diff_html = highlight(diff_text, lexer, formatter)
    return diff_html


def get_code_html(text: str) -> str:
    """Get the code html using pygments."""
    lexer = DiffLexer()
    formatter = HtmlFormatter(linenos=True, cssclass="code")
    code_html = highlight(text, lexer, formatter)
    return code_html


def get_html_str(data: EditDatum):
    """Get the html string of the edit data."""
    meta_info = f"""\
User ID: {data.user_id}
User Agent: {data.user_agent}
File Path: {data.request.path}
Time Stamp: {data.response.timestamp}
Status: {data.status}
Annotated: {data.is_annotated}
Model: {data.response.model_name}

Request ID: {data.request_id}
"""

    sep_start = "-" * 40 + "-start-selection-" + "-" * 40 + "\n"
    sep_end = "-" * 40 + "-end-selection-" + "-" * 40 + "\n"
    prefix = utils_for_str.get_last_n_lines(data.request.prefix, 15)
    suffix = utils_for_str.get_first_n_lines(data.request.suffix, 15)

    selected_text = data.request.selected_text
    if selected_text and selected_text[-1] != "\n":
        selected_text += "\n"
        sep_end = "(newline-added)" + sep_end

    code_html = get_code_html(prefix + sep_start + selected_text + sep_end + suffix)
    model_response_html = get_code_html(
        prefix + sep_start + data.response.text + sep_end + suffix
    )

    diff_html = get_diff_html(
        prefix + sep_start + selected_text + sep_end + suffix,
        prefix + sep_start + data.response.text + sep_end + suffix,
    )

    annotation_str = ""
    if data.is_annotated:
        annotation_str = f"""\
<h3>Corrected instruction</h3>
<pre><code>{data.annotated_instruction}</code></pre>
"""

    if data.annotated_text:
        annotation_diff_html = get_diff_html(
            prefix + sep_start + data.response.text + sep_end + suffix,
            prefix + sep_start + data.annotated_text + sep_end + suffix,
        )
        annotation_str += f"""\
<h3>Diff: model-generation vs. annotated-code</h3>
<div id="code-diff">{annotation_diff_html}</div>
"""

    return f"""
<!DOCTYPE html>
<html>
<head>

    <!-- Used to display/toggle large content  -->
   <script>
        function toggleCode(blockId) {{
            var x = document.getElementById(blockId);
            if (x.style.display === "none") {{
                x.style.display = "block";
            }} else {{
                x.style.display = "none";
            }}
        }}
    </script>

    <title>Code Visualization</title>
    <style>
        pre {{
            background-color: #f4f4f4;
            border: 0.8px solid #ddd;
            <!-- border-left: 0.5px solid #134da3; -->
            color: #999;
            page-break-inside: avoid;
            font-family: monospace;
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 1.6em;
            max-width: 100%;
            overflow: auto;
            padding: 1em 1.5em;
            display: block;
            word-wrap: break-word;
        }}
    </style>
</head>
<body>
    <h3>Info</h3>
    <pre><code>{meta_info}</code></pre>

    <h3>Instruction</h3>
    <pre><code>{data.request.instruction}</code></pre>

    <h3>Original code</h3>
    <pre><code>{code_html}</code></pre>

    <h3>Model response</h3>
    <pre><code>{model_response_html}</code></pre>

    <h3>Diff: original-code vs. model-generation</h3>
    <div id="code-diff">{diff_html}</div>

{annotation_str}
</body>
</html>
"""


def export_examples_to_htmls(examples: list[EditDatum], output_dir: Path):
    """Export the examples to html files."""
    for example in examples:
        html_str = get_html_str(example)

        if example.resolution is not None and example.resolution.annotated_text:
            annotation_suffix = "annotated"
        else:
            annotation_suffix = "notAnnotated"

        if example.resolution is not None:
            status = f"accepted{example.resolution.is_accepted}"
        else:
            status = "statusUnknown"

        html_file_path = (
            output_dir / f"{example.request_id}-{status}-{annotation_suffix}.html"
        )
        output_path = Path(html_file_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        output_path.write_text(html_str, encoding="utf-8")


def _fix_missing_annotation_chars(example: EditDatum):
    if example.resolution is None:
        return
    if example.resolution.annotated_text is None:
        return

    selected_text = example.request.selected_text
    annotated_text = example.resolution.annotated_text

    match_window_size = 5
    truncated_chars = 3

    if len(selected_text) < match_window_size + truncated_chars:
        return
    if len(annotated_text) < match_window_size:
        return

    # Sometimes selected text doesn't end with a newline but annotated_text does,
    # which messes up the comparison
    if selected_text and selected_text[-1] != "\n":
        selected_text += "\n"

    if (
        selected_text[-(match_window_size + truncated_chars) : -truncated_chars]
        == annotated_text[-match_window_size:]
    ):
        annotated_text += selected_text[-truncated_chars:]
        example.resolution.annotated_text = annotated_text


def load_examples(
    jsonl_file: Path, fix_missing_annotation_chars: bool
) -> list[EditDatum]:
    examples = []
    for line in jsonl_file.open("r", encoding="utf-8"):
        example = EditDatum.from_json(line)  # type: ignore  pylint: disable=no-member
        if fix_missing_annotation_chars:
            _fix_missing_annotation_chars(example)
        examples.append(example)
    return examples


def export_jsonl_to_htmls(
    jsonl_file: Path, output_dir: Path, fix_missing_annotation_chars
):
    """Export the data to html files."""
    examples = load_examples(jsonl_file, fix_missing_annotation_chars)
    export_examples_to_htmls(examples, output_dir)


def export_to_csv(jsonl_file: Path, output_csv: Path, fix_missing_annotation_chars):
    headers = ["Request ID", "Worker", "Instruction", "Resolution", "Annotated"]

    csv_data = []
    examples = load_examples(jsonl_file, fix_missing_annotation_chars)  # type: ignore
    for example in sorted(examples, key=lambda x: x.request_id):
        csv_data.append(
            [
                example.request_id,
                example.user_id,
                example.request.instruction,
                example.status,
                "Yes" if example.annotated_text is not None else "No",
            ]
        )

    with output_csv.open("w", newline="") as f:
        writer = csv.writer(f)
        writer.writerow(headers)
        writer.writerows(csv_data)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_jsonl",
        type=Path,
        required=True,
        help="The input jsonl file",
    )
    parser.add_argument(
        "--output_dir",
        type=Path,
        required=True,
        help="The output directory for the html visualizations",
    )
    parser.add_argument(
        "--output_csv",
        type=Path,
        help="The output path for a CSV summarizing the samples",
    )
    parser.add_argument(
        "--fix_missing_annotation_chars",
        action="store_true",
        help="Fix missing characters in the annotated text",
    )

    args = parser.parse_args()
    export_jsonl_to_htmls(
        args.input_jsonl, args.output_dir, args.fix_missing_annotation_chars
    )

    if args.output_csv:
        export_to_csv(
            args.input_jsonl, args.output_csv, args.fix_missing_annotation_chars
        )

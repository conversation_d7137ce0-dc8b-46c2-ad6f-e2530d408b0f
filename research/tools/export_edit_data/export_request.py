#!/usr/bin/env python
"""Recreate the directory structure of a code-edit request."""

from __future__ import annotations

import argparse
import logging
import sys
from pathlib import Path
from typing import Sequence

from base.datasets import tenants
from base.datasets.edit_dataset import BlobStore, EditDataset, EditDatum, EditRequest

logging.basicConfig(level=logging.WARNING)


def dump_rec(
    outdir: Path,
    request: EditRequest,
    blob_store: BlobStore,
    filter_blobs: Sequence[str] = (),
):
    blobs_to_retrieve = set(request.blob_names)
    if filter_blobs:
        original_count = len(blobs_to_retrieve)
        blobs_to_retrieve = blobs_to_retrieve - set(filter_blobs)
        print(f"Filtered {original_count - len(blobs_to_retrieve)} blobs.")

    logging.debug("Exporting %d to %s", len(blobs_to_retrieve), outdir)
    it = blob_store.get_blobs(blobs_to_retrieve)
    found_blobs = {
        blob_name: blob
        for blob_name, blob in zip(blobs_to_retrieve, it)
        if blob is not None
    }

    if len(found_blobs) < len(blobs_to_retrieve):
        logging.warning(
            f"Requested {len(blobs_to_retrieve)} blobs, found {len(found_blobs)}. Dataset is incomplete."
        )

    # For each blob, create the directory structure and write the content.
    # There should not be multiple blobs referencing the same path.
    for blob in found_blobs.values():
        blob_path = outdir / blob.path
        blob_path.parent.mkdir(parents=True, exist_ok=True)
        with blob_path.open("x") as f:
            f.write(blob.content)

    print(f"Exported {len(found_blobs)}/{len(blobs_to_retrieve)} blobs to {outdir}")

    # The edit prompt is more up to date than the persistent blob content, so we
    # update the file content here. Unlike with completions, The prefix-start
    # and suffix-end for edit requests are not relative to the indexed blob, but
    # to the current buffer, and the prompt should completely contain the buffer.

    blob_path = outdir / request.path
    if blob_path.exists() and request.position:
        # Validate position and warn if the prompt doesn't completely overlap
        # the indexed blob, since that may indicate overlap detection issues.
        assert request.position.prefix_begin == 0
        content = blob_path.read_text()
        if request.position.suffix_end < len(content):
            logging.warning(
                "Suffix_end < length: %d < %d. "
                "Overlap detection may not have properly filtered out content from "
                "the current blob in the original request.",
                request.position.suffix_end,
                len(content),
            )

    # Warn in the unexpected case where the blob is not found (such as with an
    # upload race).
    if not blob_path.exists():
        logging.warning("Blob %s containing prompt not found. Creating.", request.path)
        blob_path.parent.mkdir(parents=True, exist_ok=True)

    if not request.position:
        logging.warning(
            "Edit position is not set for %s. This may be due to not retrieving on this file type.",
            request.path,
        )

    # Prompt should completely contain the buffer.
    content = request.prefix + request.selected_text + request.suffix
    blob_path.write_text(content)
    print(f"Updated prompt written to {request.path}")


def get_tenant_by_vendor(vendor: str) -> tenants.DatasetTenant:
    """Return tenant by vendor name.

    TODO(rich): We use a simplified version of the tenant name to be consistent
    with that in `export_edits_to_jsonl.py`. This results in a bit of duplicate
    code with that script. In addition, other places may reference the tenant
    names directly, potentially causing some confusion. It would be nice to
    unify this code.
    """
    tenant_name = {
        "dogfood": tenants.DOGFOOD,
        "turing": tenants.AITUTOR_TURING,
        "pareto": tenants.AITUTOR_PARETO,
    }

    if vendor not in tenant_name:
        raise ValueError(
            f"Unknown vendor: {vendor}. Must be one of {tenant_name.keys()}"
        )
    return tenant_name[vendor]


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--request_id", type=str, required=True, help="The request id")
    parser.add_argument(
        "--vendor",
        type=str,
        required=True,
        help="The vendor. One of: dogfood, turing, pareto.",
    )
    parser.add_argument(
        "--filter-unknown-blobs",
        action="store_true",
        help="Exclude blobs that are reported as missing in the response. This is useful for reproducibility, for example.",
    )
    parser.add_argument(
        "--jsonl-path",
        type=str,
        help="Optional path to a dataset. If omitted, will read from BigQuery.",
    )
    parser.add_argument(
        "-o", "--output_dir", type=str, required=True, help="The output directory."
    )
    args = parser.parse_args()

    # Don't export if the final directory already exists
    outdir = Path(args.output_dir) / args.request_id
    if outdir.exists():
        print(
            f"Output directory {outdir} already exists. Remove it if you want to export again."
        )
        sys.exit(1)
    outdir.mkdir(parents=True)

    tenant = get_tenant_by_vendor(args.vendor)
    blob_store = BlobStore(tenant=tenant)

    if args.jsonl_path is not None:
        with Path(args.jsonl_path).open("r") as f:
            recs: list[EditDatum] = [EditDatum.from_json(line) for line in f]  # pyright: ignore[reportAttributeAccessIssue]
        recs = [rec for rec in recs if rec.request_id in [args.request_id]]
    else:
        dataset = EditDataset.create(
            tenant,
            filters=EditDataset.Filters(request_ids=[args.request_id]),
        )

        recs = list(dataset.get_entries())

    if not recs:
        print(f"Cannot find the request id {args.request_id}")
        return

    # Supporting only one record for now
    rec = recs[0]

    filter_blobs = []
    if args.filter_unknown_blobs:
        filter_blobs = rec.response.unknown_blob_names
        logging.info(f"Filtering {len(filter_blobs)} unknown blobs.")

    dump_rec(outdir, rec.request, blob_store, filter_blobs=filter_blobs)


if __name__ == "__main__":
    main()

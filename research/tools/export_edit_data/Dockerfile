FROM ubuntu:22.04

ENV DEBIAN_FRONTEND=noninteractive
RUN useradd --create-home --uid 1000 --shell /usr/sbin/nologin edit-data-exporter
WORKDIR /home/<USER>

RUN apt-get update -y \
 && apt-get install -y \
        python3 \
        python3-pip \
        tzdata \
 && apt-get autoremove --purge \
 && rm -fr /var/lib/apt/lists/*

RUN pip3 install \
        dataclasses_json \
        google.auth \
        google.cloud.storage \
        marshmallow

COPY --chmod=0755 edit_data_exporter.py /usr/local/bin/
COPY --chmod=0644 export_edit_data.py   /usr/lib/python3/dist-packages/research/tools/export_edit_data/

ENTRYPOINT ["/usr/local/bin/edit_data_exporter.py"]

USER edit-data-exporter

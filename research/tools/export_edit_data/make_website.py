"""Generate a website for browsing code-edit samples from contractors.

The website uses as input existing HTML files generated for each sample
with make_code_edit_sample_htmls.py

This website can be served by going to the root directory and running:

    python -m http.server
"""

import argparse
import os
from pathlib import Path

from pygments.formatters import HtmlFormatter  # pylint: disable=no-name-in-module

index_html = r"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>HTML File Browser</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="sidebar" class="sidebar"></div>
    <div id="main-content" class="main-content"></div>
    <script src="script.js"></script>
</body>
</html>
"""


style_css = r"""
body {
    display: flex;
    margin: 0;
    height: 100vh;
    overflow: hidden;
    font-family: 'Helvetica', sans-serif;
}

.sidebar {
    width: 27%;
    background: #f4f4f4;
    overflow-y: auto;
    padding: 20px;
    box-sizing: border-box;
}

.main-content {
    width: 80%;
    overflow-y: auto;
    height: 100vh;
    padding: 20px;
    box-sizing: border-box;
}
"""

style_css += (
    "\n\n/* from pygments for diff highlighting */"
    + HtmlFormatter().get_style_defs(".code")
)


script_js_template = r"""
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');

    sidebar_title = document.createElement('h2');
    sidebar_title.innerText = '<TITLE>';
    sidebar.appendChild(sidebar_title)

    // List of HTML files (you might want to generate this dynamically)
    const files = [<FILES>];
    let last_group = "";

    files.forEach(file => {
        let group;
        if (file.includes("/")) {
            group = file.split("/")[0];
        }
        else {
            group = "main";
        }

        if (group != last_group) {
            const group_header = document.createElement('h3');
            group_header.innerText = group;
            sidebar.appendChild(group_header);
            last_group = group;
        }

        // Add link to the sidebar
        const link = document.createElement('a');
        link.href = `#${file}`;
        if (file.includes("/")) {
            link.innerText = file.split("/")[1];
        }
        else {
            link.innerText = file;
        }

        if (link.innerText.includes("-accepted")) {
            link.innerText = link.innerText.split("-accepted")[0];
        }

        sidebar.appendChild(link);
        sidebar.appendChild(document.createElement('br'));

        // Add section for file content
        const section = document.createElement('div');
        section.id = file;

        // Create a header element and set color based on filename
        const header = document.createElement('h2');
        header.textContent = `${file}`;
        let color = 'black';
        if (file.includes('annotated')) {
            color = 'purple';
        } else if (file.includes('accept')) {
            color = 'green';
        } else if (file.includes('reject')) {
            color = 'red';
        }
        header.style.color = color;
        section.appendChild(header);

        const header_link = document.createElement('a');
        header_link.href = `#${file}`;
        header_link.innerText = "[link to this sample]";
        section.appendChild(header_link);

        mainContent.appendChild(section);

        // Load file content
        fetch(file).then(response => {
            if(response.ok) {
                return response.text();
            } else {
                throw new Error('File not found');
            }
        }).then(html => {
            section.innerHTML += html;
        }).catch(error => {
            section.innerHTML = `<h1>${file}</h1><p>Error loading file</p>`;
        });
    });
});
"""


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--root", required=True, help="root directory for the web site")
    args = parser.parse_args()

    if not Path(args.root).exists():
        raise ValueError(f"Path {args.root} doesn't exist")
    os.chdir(args.root)

    html_paths = []
    for path in sorted(Path().rglob("*.html")):
        if path == Path("index.html"):
            continue
        html_paths.append(str(path))
    print(f"Collected {len(html_paths)} paths")

    Path("index.html").write_text(index_html, encoding="utf8")
    Path("style.css").write_text(style_css, encoding="utf8")
    script_js = script_js_template.replace(
        "<FILES>", ", ".join([f"'{path}'" for path in html_paths])
    ).replace("<TITLE>", "Samples from " + args.root.split("/")[-1])
    Path("script.js").write_text(script_js, encoding="utf8")

    print(f"Created website (index.html, ...) in {args.root}")


if __name__ == "__main__":
    main()

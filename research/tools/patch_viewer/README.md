# Patch Viewer.

This directory implements an internal tool to help view, compare and annotate patches.

Example usage:
```
# NOTE: we install patch_viewer as a command. You can also manually run it using
#   cd research/tools/patch_viewer; python3 patch_viewer.py ...
ROOT=/mnt/efs/augment/user/arun/patch_viewer/testdata/
patch_viewer \
    # The format here is system_name=path_to_patches.jsonl \
    igor=$ROOT/igor/000_starcoderbase_neox_hydra.jsonl \
    reranker=$ROOT/reranker/000_starcoderbase_neox_hydra.jsonl \
    # Hydra also generates some additional files with the retrieved chunks. \
    # Point to those files with the same system name to load them. \
    --extra igor=$ROOT/igor/000_starcoderbase_neox_hydra_completed_patches.jsonl.zst \
    reranker=$ROOT/reranker/000_starcoderbase_neox_hydra_completed_patches.jsonl.zst
```

Then visit `http://<your-ip>:5000/`.

## Developing the tools

This frontend for the tool is built with React. To support a reasonable development
environment with an auto-reloading server, please run the patch viewer with the
`--debug` flag (this allows the React devserver to make API calls to it), and run
`npm run start`.

To update the binary, run `npm run build`.

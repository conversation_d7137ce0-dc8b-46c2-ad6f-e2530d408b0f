{"file_content": "\n#include <ctime>\n#include <iostream>\n#include <string>\n\nstruct Person {\n    std::string name;\n    std::string profession;\n    std::tm dob;\n};\n\nstd::ostream& operator<<(std::ostream& os, const Person& person) {\n  os << person.name << \"is a \" << person.profession << \"and was born on \" << std::asctime(&person.dob);\n  return os;\n}\n", "char_start": 211, "char_end": 328, "patch_content": "    os << person.name << \" \" << person.profession << \" \" << person.dob.tm_year + 1900 << \"-\" << person.dob.tm_mon + 1 << \"-\" << person.dob.tm_mday;\n    return os;\n}\n\nint main() {\n    Person person;\n    person.name = \"John\";\n    person.profession = \"Programmer\";\n    person.dob = {2000, 1, 1};\n    std::cout << person << std::endl", "patch_id": "cpp/structure_5.cpp", "repository": "", "commit_sha": "", "file_name": "cpp/structure_5.cpp", "_extra": {}}
{"file_content": "\n#include <vector>\n\nstruct Person {\n    char* name;\n    int age;\n    std::vector<Person*> parents;\n};\n\nvoid get_ancestors(const Person& person, std::vector<const Person*> acc) {\n    acc.push_back(&person);\n    for (const auto* parent : person.parents) {\n        get_ancestors(*parent, acc);\n    }\n}\n", "char_start": 121, "char_end": 298, "patch_content": "(Person* person, std::vector<Person*>& ancestors);\n\n#endif", "patch_id": "cpp/structure_2.cpp", "repository": "", "commit_sha": "", "file_name": "cpp/structure_2.cpp", "_extra": {}}
{"file_content": "\n#include <vector>\n\nstruct Person {\n    char* name;\n    int age;\n    std::vector<Person*> parents;\n};\n\nvoid get_ancestors(const Person& person, std::vector<const Person*> acc) {\n    acc.push_back(&person);\n    for (const auto* parent : person.parents) {\n        get_ancestors(*parent, acc);\n    }\n}\n", "char_start": 65, "char_end": 99, "patch_content": "    std::vector<const Person*> parents;\n", "patch_id": "cpp/structure_1.cpp", "repository": "", "commit_sha": "", "file_name": "cpp/structure_1.cpp", "_extra": {}}
{"file_content": "\nint main() {\n    int *arr = new int[10];\n    // Fill arr with increasing numbers\n    for (int i = 0; i < 10; i++) arr[i] = i;\n    delete arr;\n    // Fill arr with decreasing numbers\n    arr = new int[10];\n    for (int i = 0; i < 10; i++) arr[i] = 9 - i;\n}\n", "char_start": 183, "char_end": 255, "patch_content": "    for (int i = 0; i < 10; i++) arr[i] = 10 - i;\n    delete arr;\n    return 0;\n", "patch_id": "cpp/double_free.cpp", "repository": "", "commit_sha": "", "file_name": "cpp/double_free.cpp", "_extra": {}}
{"file_content": "\n#include <ctime>\n#include <iostream>\n#include <string>\n\nstruct Person {\n    std::string name;\n    std::string profession;\n    std::tm dob;\n};\n\nstd::ostream& operator<<(std::ostream& os, const Person& person) {\n    os << person.name << \"is a \" << person.profession << \"and was born on \" << std::asctime(&person.dob);\n    return os;\n}\n", "char_start": 236, "char_end": 264, "patch_content": "\" is a \" << person.profession", "patch_id": "cpp/structure_4.cpp", "repository": "", "commit_sha": "", "file_name": "cpp/structure_4.cpp", "_extra": {}}
{"file_content": "\n#include <ctime>\n#include <iostream>\n#include <string>\n#include <vector>\n\nstruct Person {\n    std::string name;\n    std::tm dob;\n    std::vector<Person*> parents;\n};\n\nint main() {\n    Person alice = {\n        \"Alice\",\n        {tm_mday: 1, tm_mon: 4, tm_year: 89},\n        {}};\n    int age = std::difftime(std::time(nullptr), std::mktime(&alice.dob)) / (365.25 * 24 * 60 * 60);\n    std::cout << \"Alice is \" << age << \" years old\" << std::endl;\n\n    return 0;\n}\n", "char_start": 292, "char_end": 377, "patch_content": "std::time(nullptr) - std::mktime(&alice.dob);", "patch_id": "cpp/structure_3.cpp", "repository": "", "commit_sha": "", "file_name": "cpp/structure_3.cpp", "_extra": {}}
{"file_content": "package main\n\nimport \"sort\"\n\ntype Person struct {\n\tname    string\n\tage     int\n}\n\nfunc main() {\n\tpeople := []Person{\n\t\t\tPerson{\"alice\", 60},\n\t\t\tPerson{\"bob\", 50},\n\t\t\tPerson{\"charlie\", 20},\n\t\t}\n\t// sort by age\n    sort.SliceStable(people, func(i, j int) bool {\n      return people[i].age < people[j].age\n    })\n}\n", "char_start": 209, "char_end": 310, "patch_content": "\tsort.Slice(people, func(i, j int) bool {\n\t\treturn people[i].age < people[j].age\n\t})\n\tfor _, person := range people {\n\t\tprintln(person.name)\n\t}\n", "patch_id": "go/structure_4.go", "repository": "", "commit_sha": "", "file_name": "go/structure_4.go", "_extra": {}}
{"file_content": "package main\n\ntype Person struct {\n\tname    string\n\tage     int\n\tparents []*Person\n}\n\nfunc get_ancestors(person *Person, acc []*Person) []*Person {\n   \tfor _, parent := range person.parents {\n   \t\tacc = append(acc, parent)\n   \t\tacc = get_ancestors(parent, acc)\n   \t}\n   \treturn acc\n}\n", "char_start": 104, "char_end": 284, "patch_content": "(p *Person) []*Person {\n\tif p == nil {\n\t\treturn nil\n\t}\n\treturn append(get_ancestors(p.parents[0]), p)\n}\n\nfunc main() {\n\tp := &Person{\n\t\tname:    \"Bob\",\n\t\tage:     20,\n\t\tparents: []*Person{&Person{name: \"Alice\", age: 10}},\n\t}\n\tfmt.Println(get_ancestors(p))\n}\n", "patch_id": "go/structure_2.go", "repository": "", "commit_sha": "", "file_name": "go/structure_2.go", "_extra": {}}
{"file_content": "package main\n\ntype Person struct {\n\tname    string\n\tage     int\n\tparents []*Person\n}\n\nfunc get_ancestors(person *Person, acc []*Person) []*Person {\n\tfor _, parent := range person.parents {\n\t\tacc = append(acc, parent)\n\t\tacc = get_ancestors(parent, acc)\n\t}\n\treturn acc\n}\n", "char_start": 65, "char_end": 82, "patch_content": "parents []*Person\n}\n\nfunc main() {\n\tperson := &Person", "patch_id": "go/structure_1.go", "repository": "", "commit_sha": "", "file_name": "go/structure_1.go", "_extra": {}}
{"file_content": "package main\n\ntype Person struct {\n\tname    string\n\tage     int\n\tparents []*Person\n}\n\nfunc get_ancestors(person *Person, acc []*Person) []*Person {\n  for _, parent := range person.parents {\n    acc = append(acc, parent)\n    acc = get_ancestors(parent, acc)\n  }\n  return acc\n}\n", "char_start": 148, "char_end": 274, "patch_content": "\tif person == nil {\n\t\treturn acc\n\t}\n\tacc = append(acc, person)\n\treturn get_ancestors(person.parents[0], acc)\n}\n\nfunc main() {\n\tperson := &Person{\n\t\tname:    \"Bob\",\n\t\tage:     25,\n\t\tparents: []*Person{&Person{name: \"Alice\", age: 20}},\n\t}\n\tancestors := get_ancestors(person, []*Person{})\n\tfor _, a := range ancestors {\n\t\tfmt.Printf(\"%s", "patch_id": "go/structure_3.go", "repository": "", "commit_sha": "", "file_name": "go/structure_3.go", "_extra": {}}
{"file_content": "import dataclasses\n\<EMAIL>\nclass Person:\n    name: str\n    age: int\n    parents: list['Person'] = dataclasses.field(default_factory=list)\n\ndef get_ancestors(person: Person):\n    ret = []\n    for parent\n", "char_start": 84, "char_end": 154, "patch_content": "\<EMAIL>\nclass Employee(Person):\n    salary: int\n\<EMAIL>\nclass Manager(Employee):\n    department: str\n\<EMAIL>\nclass Director(Manager):\n    title: str\n\<EMAIL>\nclass CEO(Director):\n   ", "patch_id": "py/structure_5.py", "repository": "", "commit_sha": "", "file_name": "py/structure_5.py", "_extra": {}}
{"file_content": "\nimport dataclasses\n\<EMAIL>\nclass Person:\n    name: str\n    age: int\n    parents: list['Person']\n\n\ndef get_ancestors(person: Person):\n    ancestors = []\n    for parent in person.parents:\n        ancestors.append(parent)\n        ancestors.extend(get_ancestors(parent))\n    return ancestors\n\n# EOF\n", "char_start": 169, "char_end": 305, "patch_content": "    while person.parents:\n        ancestors.append(person)\n        person = person.parents[0]\n    return ancestors\n\n\ndef get_ancestors_recursive(person: Person):\n    ancestors = []\n    while person.parents:\n        ancestors.append(person)\n        person = person.parents[0]\n    return ancestors\n\n\ndef get_ancestors_generator(person: Person):\n    ancestors = []\n    while person.parents:\n        ancestors.append(person)\n        person = person.parents[0]\n    return ancestors\n\n\ndef get_ancestors_generator_recursive(person: Person):\n   ", "patch_id": "py/structure_2.py", "repository": "", "commit_sha": "", "file_name": "py/structure_2.py", "_extra": {}}
{"file_content": "\n@dataclass\nclass ScopeTreeParser:\n    \"\"\"An opaque class for parsing source code into a scope tree.\"\"\"\n\n    verbose: bool = False\n\n    def parse(\n        self,\n        code: str,\n        file_name: str,\n        lang: LanguageID,\n    ) -> \"SrcScope\":\n        \"\"\"Parse a source file into a scope tree.\"\"\"\n        if lang not in _scoping_supports:\n            raise \n        support = _scoping_supports[lang]\n        scope = support.scope_source(code, parser=self)\n        scope.name = file_name\n        return scope\n\n    def __post_init__(self):\n        self._parser = ts.Parser()\n        self._language_cache = dict[LanguageID, ts.Language]()\n\n    def parse_ts_tree(self, code: bytes, lang: LanguageID) -> ts.Tree:\n        \"\"\"Parse the code as a tree-sitter tree.\"\"\"\n        language = self._get_language(lang)\n        self._parser.set_language(language)\n        tree = self._parser.parse(code)\n        return tree\n\n    def _get_language(self, lang: LanguageID) -> ts.Language:\n        language = self._language_cache.get(lang)\n        if language is None:\n            language = get_language(lang)\n            self._language_cache[lang] = language\n        return language\n\n\nclass LineMap:\n    \"\"\"Maps between character positions and (line, column) numbers.\"\"\"\n\n    # stores the char positions of all line breaks\n    _line_stops: Sequence[int]\n\n    def __init__(self, code: str):\n        last_line_stop = 0\n        line_breaks = list[int]()\n        for line in code.splitlines(keepends=True):\n            last_line_stop += len(line)\n            line_breaks.append(last_line_stop)\n        self._line_stops = line_breaks\n\n    def size_lines(self):\n        \"\"\"Get number of lines in this map.\"\"\"\n        return len(self._line_stops)\n\n    def size_chars(self):\n        \"\"\"Get number of characters covered by this map.\"\"\"\n        return self._line_stops[-1] if self._line_stops else 0\n\n    def get_line_number(self, char_pos: int) -> int:\n        \"\"\"Map a char position into the corresponding line.\"\"\"\n        return bisect.bisect_left(self._line_stops, char_pos + 1)\n\n    def get_line_column(self, char_pos: int) -> tuple[int, int]:\n        \"\"\"Map a char position into (line_number, column_number).\"\"\"\n        line = self.get_line_number(char_pos)\n        line_start = 0 if line == 0 else self._line_stops[line - 1]\n        column = char_pos - line_start\n        return (line, column)\n\n    def get_line_range(self, line: int) -> CharRange:\n        \"\"\"Get the char range of a given line.\"\"\"\n        return CharRange(self.get_char(line, 0), self.get_char(line + 1, 0))\n\n    def get_char(self, line: int, column: int) -> int:\n        \"\"\"Map (line, column) to char position (assuming zero-based indexing).\"\"\"\n        line_start = 0 if line == 0 else self._line_stops[line - 1]\n        return line_start + column\n", "char_start": 364, "char_end": 364, "patch_content": "NotImplementedError(f\"Language {lang} is not supported.\")", "patch_id": "py/fim_real_class_1.py", "repository": "", "commit_sha": "", "file_name": "py/fim_real_class_1.py", "_extra": {}}
{"file_content": "\n_GenericScoping(\n    lang_id=\"python\",\n    scope_types={\n        \"module\": _ScopeDef(\n            \"file\",\n            name_field=(),\n            body_field=(),\n            braced=False,\n        ),\n        \"class_definition\": _ScopeDef(\n            \"class\",\n        ),\n        \"function_definition\": _ScopeDef(\n            \"function\",\n            is_leaf=True,\n        ),\n    },\n    scope_decorator_types={\n        \"decorated_definition\": _DecorationDef(\n            decorated_field=\"definition\",\n        ),\n    },\n    docstr_def=_PythonDocStrDef(),\n).register()\n\n\n_GenericScoping(\n    lang_id=\"java\",\n    scope_types={\n        \"program\": _ScopeDef(\n            \"file\",\n            name_field=(),\n            body_field=(),\n        ),\n        \"class_declaration\": _ScopeDef(\n            \"class\",\n            braced=True,\n        ),\n        \"method_declaration\": _ScopeDef(\n            \"function\",\n            is_leaf=True,\n            braced=True,\n            must_be_class_member=True,\n        ),\n        \"constructor_declaration\": _ScopeDef(\n            \"function\",\n            is_leaf=True,\n            braced=True,\n            must_be_class_member=True,\n        ),\n    },\n    scope_decorator_types={},\n    docstr_def=_JavaLikeDocStrDef(\"block_comment\"),\n).register()\n\n\n_GenericScoping(\n    lang_id=\"cpp\",\n    scope_types={\n        \"translation_unit\": _ScopeDef(\n            \"file\",\n            name_field=(),\n            body_field=(),\n        ),\n        \"class_specifier\": _ScopeDef(\n            \"class\",\n            braced=True,\n        ),\n        \"function_definition\": _ScopeDef(\n            \"function\",\n            name_field=(\"declarator\", \"declarator\"),\n            braced=True,\n        ),\n    },\n    scope_decorator_types={\n        \"template_declaration\": _DecorationDef(\n            decorated_field=-1,  # last child is the decorated\n        ),\n        \"field_declaration\": _DecorationDef(\n            decorated_field=\"type\",\n            allow_no_scope=True,\n        ),\n    },\n    docstr_def=_JavaLikeDocStrDef(\"comment\"),\n).register()\n\n\n_GenericScoping(\n    lang_id=\"javascript\",\n    scope_types={\n        \"program\": _ScopeDef(\n            \"file\",\n            name_field=(),\n            body_field=(),\n        ),\n        \"class_declaration\": _ScopeDef(\n            \"class\",\n            braced=True,\n        ),\n        \"function_declaration\": _ScopeDef(\n            \"function\",\n            is_leaf=True,\n            braced=True,\n        ),\n        \"method_definition\": _ScopeDef(\n            \"function\",\n            is_leaf=True,\n            braced=True,\n            must_be_class_member=True,\n        ),\n    },\n    scope_decorator_types={},\n    docstr_def=_JavaLikeDocStrDef(\"comment\"),\n).register()\n\n_GenericScoping(\n    lang_id=\"typescript\",\n    scope_types={\n        \"program\": _ScopeDef(\n            \"file\",\n            name_field=(),\n            body_field=(),\n        ),\n        \"class_declaration\": _ScopeDef(\n            \"class\",\n            braced=True,\n        ),\n        \"interface_declaration\": _ScopeDef(\n            \"class\",\n            braced=True,\n        ),\n        \"abstract_class_declaration\": _ScopeDef(\n            \"class\",\n            braced=True,\n        ),\n        \"function_declaration\": _ScopeDef(\n            \"function\",\n            is_leaf=True,\n            braced=True,\n        ),\n        \"method_definition\": _ScopeDef(\n            \"function\",\n            is_leaf=True,\n            braced=True,\n            must_be_class_member=True,\n        ),\n    },\n    scope_decorator_types={},\n    docstr_def=_JavaLikeDocStrDef(\"comment\"),\n).register()\n\n_GenericScoping(\n    lang_id=\"go\",\n    scope_types={\n        \n    },\n    scope_decorator_types={},\n    docstr_def=_JavaLikeDocStrDef(\"comment\"),\n).register()\n\n_GenericScoping(\n    lang_id=\"rust\",\n    scope_types={\n        \"source_file\": _ScopeDef(\n            \"file\",\n            name_field=(),\n            body_field=(),\n        ),\n        \"impl_item\": _ScopeDef(\n            \"class\",\n            name_field=\"type\",\n            body_field=\"body\",\n            braced=True,\n        ),\n        \"function_item\": _ScopeDef(\n            \"function\",\n            is_leaf=True,\n            braced=True,\n        ),\n    },\n    scope_decorator_types={},\n    docstr_def=_JavaLikeDocStrDef(\"line_comment\"),\n).register()\n", "char_start": 3647, "char_end": 3647, "patch_content": "\"package\": _ScopeDef(\n            \"file\",\n            name_field=(),\n            body_field=(),\n        ),\n        \"type_declaration\": _ScopeDef(\n            \"class\",\n            braced=True,\n        ),\n        \"function_declaration\": _ScopeDef(\n            \"function\",\n            is_leaf=True,\n            braced=True,\n        ),\n        \"method_declaration\": _ScopeDef(\n            \"function\",\n            is_leaf=True,\n            braced=True,\n            must_be_class_member=True,\n        ),\n    },\n    scope_decorator_types={},\n    docstr_def=_JavaLikeDocStrDef(\"comment\"),\n).register()\n\n_GenericScoping(\n    lang_id=\"php\",\n    scope_types={\n        \"program\": _ScopeDef(\n            \"file\",\n            name_field=(),\n            body_field=(),\n        ),\n        \"class_declaration\": _ScopeDef(\n           ", "patch_id": "py/fim_real_class_2.py", "repository": "", "commit_sha": "", "file_name": "py/fim_real_class_2.py", "_extra": {}}
{"file_content": "import dataclasses\nimport datetime\n\<EMAIL>\nclass Person:\n    name: str\n    dob: datetime.date\n    profession: str\n\n\nalice = Person('Alice', datetime.date.fromisoformat('1989-04-01'), 'Engineer')\nnow = datetime.date.today()\nif datetime.date(now.year, alice.dob.month, alice.dob.day) < now:\n    age = (now.year - alice.dob.year)\nelse:\n    age = (now.year - alice.dob.year) - 1\nprint(f\"Alice is {age} years old!\")\n", "char_start": 239, "char_end": 391, "patch_content": "age = (now - alice.dob).days / 365\n", "patch_id": "py/structure_3.py", "repository": "", "commit_sha": "", "file_name": "py/structure_3.py", "_extra": {}}
{"file_content": "import copy\nfrom dataclasses import dataclass, field\nfrom typing import *\n\n@dataclass\nclass Patch:\n    \"\"\"Represents a patch to a file using its character span.\"\"\"\n\n    file_content: str = \"\"\n    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n    char_start: int = 0\n    \"\"\"Character offset for the patch.\"\"\"\n    char_end: int = 0\n    \"\"\"character offset for the patch.\"\"\"\n    patch_content: str = \"\"\n    \"\"\"the patch content.\"\"\"\n    patch_id: str = \"\"\n\n    @classmethod\n    def for_span(\n        cls,\n        file_content: str,\n        char_start: int,\n        char_end: int,\n        **kwargs,\n    ):\n        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n        return cls(\n            file_content=file_content,\n            char_start=char_start,\n            char_end=char_end,\n            patch_content=file_content[char_start:char_end],\n            **kwargs,\n        )\n\n    def shift(self, amount: int) -> \"Patch\":\n        result = copy.copy(self)\n        result.char_start += amount\n        result.char_end += amount\n        return result\n\n    @classmethod\n    def empty(cls, file_content: str, **kwargs):\n        \"\"\"Create an empty Patch for a file.\"\"\"\n        return Patch(\n            file_content=file_content,\n            char_start=0,\n            char_end=0,\n            patch_content=\"\",\n            **kwargs,\n        )\n", "char_start": 1021, "char_end": 1091, "patch_content": "        result.char_start += amount\n        result.char_end += amount\n        result.patch_content = result.file_content[result.char_start:result.char_end]\n", "patch_id": "py/fim_multi_node_missing.py", "repository": "", "commit_sha": "", "file_name": "py/fim_multi_node_missing.py", "_extra": {}}
{"file_content": "\nimport dataclasses\n\<EMAIL>\nclass Person:\n    name: str\n    age: int\n    profession: str\n\n\ndef sort_by_age(people):\n    people.sort(key=lambda x: x.age)\n\n\ndef test_sort_by_age():\n    p1 = Person('Alice', 42, 'Engineer')\n    p2 = Person('Bill', 22, 'Marketer')\n    p3 = Person('Christine', 12, '')\n    persons = [p1, p2, p3]\n    sort_by_age(persons)\n    assert persons == [p3, p2, p1]\n\n# EOF\n", "char_start": 313, "char_end": 400, "patch_content": "    people = [p1, p2, p3]\n    sort_by_age(people)\n    assert people == [p3, p1, p2]\n\n\nif __name__ == '__main__':\n    test_sort_by_age()", "patch_id": "py/testing_1.py", "repository": "", "commit_sha": "", "file_name": "py/testing_1.py", "_extra": {}}
{"file_content": "\nfrom dataclasses import dataclass, field\nfrom pathlib import Path\nfrom random import Random\nfrom typing import Literal, Optional\n\nimport tree_sitter as ts\n\nfrom .common import CharRange\nfrom .parsing import TsNodeType\nfrom .usage_analysis import ParsedFile\n\n\n@dataclass\nclass FimProblem:\n    \"\"\"An (untokenized) FimProblem instance.\n\n    ## Arguments\n    - `middle`, `prefix`, `suffix`: 3 strings that form the FIM Problem. At inference\n    time, the `middle` can be empty.\n    \"\"\"\n\n    middle: str\n\n    prefix: str\n\n    suffix: str\n\n    middle_range: CharRange\n    \"\"\"The character range of the middle span.\"\"\"\n\n    file_path: Path\n    \"\"\"The path of the file from which this problem is constructed.\"\"\"\n\n    middle_node_type: TsNodeType\n    \"\"\"Type of the tree-sitter node that the middle belongs to.\"\"\"\n\n    middle_dividers: list[int]\n    \"\"\"Character positions that divide the middle into syntactic units.\n    These indices are relative \"\"\"\n\n    def __str__(self) -> str:\n        return self.show()\n\n    def __bool__(self) -> Literal[True]:\n        return True\n\n    def show(self, prefix_limit: int = 1000, suffix_limit: int = 1000):\n        SEP = \"-\" * 50\n        prefix = self.prefix\n        if len(prefix) > prefix_limit:\n            prefix = \"[...]\" + prefix[len(prefix) - prefix_limit :]\n        suffix = self.suffix\n        if len(suffix) > suffix_limit:\n            suffix = suffix[:suffix_limit] + \"[...]\"\n        parts = [\n            f\"FimProblem(file={self.file_path}, middle={self.middle_range}):\",\n            f\"{SEP}Prefix{SEP}\",\n            prefix,\n            f\"{SEP}Middle ({self.middle_node_type}){SEP}\",\n            self.middle,\n            f\"{SEP}Suffix{SEP}\",\n            suffix,\n        ]\n        return \"\\n\".join(parts)\n", "char_start": 941, "char_end": 941, "patch_content": "to the middle span.\"\"\"\n\n    middle_dividers_ranges: list[CharRange]\n    \"\"\"The character ranges of the middle dividers.\"\"\"\n\n    middle_dividers_node_types: list[TsNodeType]\n    \"\"\"The node types of the middle dividers.\"\"\"\n\n    middle_dividers_spans: list[ts.Node]\n    \"\"\"The tree-sitter nodes of the middle dividers.\"\"\"\n\n    middle_dividers_spans_ranges: list[CharRange]\n    \"\"\"The character ranges of the middle dividers.\"\"\"\n\n    middle_dividers_spans_node_types: list[TsNodeType]\n    \"\"\"The node types of the middle dividers.\"\"\"\n\n    middle_dividers_spans_spans: list[ts.Node]\n    \"\"\"The tree-sitter nodes of the middle dividers.\"\"\"\n\n    middle_dividers_spans_spans_ranges: list[CharRange]\n    \"\"\"The character ranges of the middle dividers.\"\"\"\n\n    middle_dividers", "patch_id": "py/fim_real_ex_5.py", "repository": "", "commit_sha": "", "file_name": "py/fim_real_ex_5.py", "_extra": {}}
{"file_content": "from dataclasses import dataclass, field\nfrom typing import *\n\n@dataclass\nclass Datum:\n    \"\"\"Represents a blob of data.\"\"\"\n\n    source: str = \"\"\n    \"\"\"Where the data comes from.\"\"\"\n    char_start: int = 0\n    \"\"\"Character offset for the blob.\"\"\"\n    char_end: int = 0\n    \"\"\"Character offset for the blob.\"\"\"\n    _extra: Mapping = field(default_factory=dict, repr=True, init=True, compare=True)\n    \"\"\"A handy field to store extra data.\"\"\"\n", "char_start": 381, "char_end": 395, "patch_content": "", "patch_id": "py/fim_dataclass_field.py", "repository": "", "commit_sha": "", "file_name": "py/fim_dataclass_field.py", "_extra": {}}
{"file_content": "import argparse\n\nparser = argparse.ArgumentParser()\nparser.add_argument(\n    \"--model\",\n    type=str,\n    default=\"my_model\",\n    help=\"which model to serve\",\n)\nparser.add_argument(\n    \"--disable_completions\",\n    action=\"store_true\",\n    default=False,\n    help=\"disable completion requests\",\n)\nparser.add_argument(\n    \"--single_line_completions\",\n    action=\"store_true\",\n    help=\"whether to return single-line completions\",\n)\n", "char_start": 236, "char_end": 255, "patch_content": "", "patch_id": "py/fim_argparse.py", "repository": "", "commit_sha": "", "file_name": "py/fim_argparse.py", "_extra": {}}
{"file_content": "import dataclasses\n\<EMAIL>\nclass Person:\n    name: str\n    age: int\n    profession: str\n\n\npersons = [\n    Person('Alice', 42, 'Engineer'),\n    Person('Bill', 22, 'Marketer'),\n    Person('Christine', 12, ''),\n    Person('Devi', 18, 'Entrepreneur'),\n]\n\n# Sort persons by age\npersons.sort(key=lambda x: x.age)\n", "char_start": 289, "char_end": 323, "patch_content": "persons.sort(key=lambda person: person.age)\n\n# Sort persons by age in descending order\npersons.sort(key=lambda person: person", "patch_id": "py/structure_4.py", "repository": "", "commit_sha": "", "file_name": "py/structure_4.py", "_extra": {}}
{"file_content": "def remove_duplicates(sorted_lst):\n    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n    if not sorted_lst:\n        return\n    write_idx = 0\n    for i in range(1, len(sorted_lst)):\n        if sorted_lst[i] != sorted_lst[write_idx]:\n            write_idx += 1\n            sorted_lst[write_idx] = sorted_lst[i]\n    del sorted_lst[write_idx+1:]\n\n\nlst = [1, 4, 2, 6, 1, 3, 4]\n# Print the list of unique integers in lst.\nlst.sort()\nremove_duplicates(lst)\nprint(lst)\n", "char_start": 423, "char_end": 468, "patch_content": "print(lst)\nremove_duplicates(lst)\nprint(lst)\n\n# + [markdown] id=\"71611125-1111-426b-9", "patch_id": "py/infer_preconditions.py", "repository": "", "commit_sha": "", "file_name": "py/infer_preconditions.py", "_extra": {}}
{"file_content": "def get_odd_ints(lower, upper):\n    results = []\n    return results\n", "char_start": 48, "char_end": 48, "patch_content": "\n    for i in range(lower, upper + 1, 2):\n        results.append(i)", "patch_id": "py/fim_trailing_return_with_var.py", "repository": "", "commit_sha": "", "file_name": "py/fim_trailing_return_with_var.py", "_extra": {}}
{"file_content": "import copy\nfrom dataclasses import asdict, dataclass\nimport json\nimport pathlib\n\n@dataclass\nclass MyClass:\n    def save_to_json(self, filename):\n        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n        with pathlib.Path(filename).open(\"w\") as f:\n            json.dump(asdict(self), f)\n\n    @classmethod\n    def load_from_json(cls, filename):\n        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n        with pathlib.Path(\n        return cls(**data)\n\n    def clone(self):\n        \"\"\"Returns a deep copy of the instance.\"\"\"\n        return copy.deepcopy(self)\n\n    def __repr__(self):\n        attributes = \",\\n  \".join(\n            f\"{key} = {value!r}\" for key, value in vars(self).items()\n        )\n        return f\"MyClass(\\n  {attributes}\\n)\"\n", "char_start": 438, "char_end": 438, "patch_content": "filename).open(\"r\") as f:\n            data = json.load(f)", "patch_id": "py/fim_multi_function_2.py", "repository": "", "commit_sha": "", "file_name": "py/fim_multi_function_2.py", "_extra": {}}
{"file_content": "def get_odd_ints(lower, upper):\n    \"\"\"Get Get the odd integers between `lower` and `upper`. and `upper`.\"\"\"\n    return [i for i in range(lower, upper) if i % 2 == 1]\n", "char_start": 43, "char_end": 92, "patch_content": "all odd integers between `lower`", "patch_id": "py/fim_docstring_middle_fix.py", "repository": "", "commit_sha": "", "file_name": "py/fim_docstring_middle_fix.py", "_extra": {}}
{"file_content": "import dataclasses\nimport datetime\n\nfrom git import Sequence\n\<EMAIL>\nclass Person:\n    name: str\n    dob: datetime.date\n    profession: str\n    home_address: str\n    parents: Sequence[\"Person\"]\n    children: Sequence[\"Person\"]\n\n    def __str__(self):\n        \n        return f\"{self.name} is {age} years old\"\n\n    def salary(self):\n        raise NotImplementedError()", "char_start": 275, "char_end": 275, "patch_content": "return f\"{self.name} is {self.age} years old\"\n\n    @property\n    def age(self):\n        today = datetime.date.today()\n        return today.year - self.dob.year\n\<EMAIL>\nclass Employee(Person):\n    salary: float\n\n    def __str__(self):\n        age = self.age", "patch_id": "py/fim_demo2.py", "repository": "", "commit_sha": "", "file_name": "py/fim_demo2.py", "_extra": {}}
{"file_content": "import dataclasses\n\<EMAIL>\nclass Person:\n    name: str\n    age: int\n    parents: list['Person'] = dataclasses.field(default_factory=list)\n\n\ndef get_ancestors(person: Person):\n    ret = []\n    for parent in person.parents:\n        ret.append(parent)\n        ret.extend(get_ancestors(parent))\n    return ret\n", "char_start": 84, "char_end": 154, "patch_content": "    parents: list[Person] = dataclasses.field(default_factory=list)\n\ndef get_children(person: Person):\n    ret = []\n    for child in person.children:\n        ret.append(child)\n        ret.extend(get_children(child))\n    return ret", "patch_id": "py/structure_1.py", "repository": "", "commit_sha": "", "file_name": "py/structure_1.py", "_extra": {}}
{"file_content": "from dataclasses import dataclass, field\nfrom typing import *\n\n\n@dataclass\nclass Datum:\n    \"\"\"Represents a blob of data.\"\"\"\n\n    source: str = \"\"\n    \"\"\"Where the data comes from.\"\"\"\n    char_start: int = 0\n    \"\"\"Character offset for the blob.\"\"\"\n    char_end: int = 0\n    \"\"\"Character offset for the blob.\"\"\"\n    _extra: Mapping = field(default_factory=dict, repr=True, init=True)\n    \"\"\"A handy field to store extra data.\"\"\"\n\n\ndef example_datum():\n    return Datum(\n        source=\"example\",\n        char_end=5,\n        _extra={\"a\": 1},\n    )\n", "char_start": 184, "char_end": 429, "patch_content": "\n    char_end: int = 0\n    \"\"\"The character index of the end of the data.\"\"\"\n\n    _extra: Dict[str, Any] = field(default_factory=dict)\n    \"\"\"Extra data.\"\"\"\n", "patch_id": "py/fim_dataclass_attrs.py", "repository": "", "commit_sha": "", "file_name": "py/fim_dataclass_attrs.py", "_extra": {}}
{"file_content": "import math\n\n\ndef make_chunked_requests(payload, chunk_size):\n    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n\n    Args:\n        payload: the payload to chunk and send.\n\n    Returns:\n        A list of requests; each request is a dictionary with keys:\n            chunk_idx: the index of the current chunk.\n            chunk_count: how many chunks are required in total.\n            chunk_payload: the chunk payload.\n    \"\"\"\n    requests = []\n    chunk_count = math.ceil(len(payload) / chunk_size)\n    for chunk_idx in range(chunk_count):\n        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\n        requests.append({\n            'chunk_count': chunk_count,\n            'chunk_idx': chunk_idx,\n            'chunk_payload': chunk_payload,\n        })\n    return requests\n", "char_start": 527, "char_end": 811, "patch_content": "    for chunk_idx in range(chunk_count):\n        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\n        requests.append({\n            'chunk_idx': chunk_idx,\n            'chunk_count': chunk_count,\n            'chunk_payload': chunk_payload,\n        })\n", "patch_id": "py/chunk_request.py", "repository": "", "commit_sha": "", "file_name": "py/chunk_request.py", "_extra": {}}
{"file_content": "import dataclasses\nimport datetime\n\nfrom git import Sequence\n\<EMAIL>\nclass Person:\n    name: str\n    dob: datetime.date\n    profession: str\n    home_address: str\n    parents: Sequence[\"Person\"]\n    children: Sequence[\"Person\"]\n\n    def __str__(self):\n        \n        return f\"{self.name} is {age} years old\"\n\n    def salary(self,", "char_start": 275, "char_end": 275, "patch_content": "age = self.dob.year - datetime.date.today().year", "patch_id": "py/fim_demo1.py", "repository": "", "commit_sha": "", "file_name": "py/fim_demo1.py", "_extra": {}}
{"file_content": "\ndef remove_duplicates(sorted_lst):\n    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n    if not sorted_lst:\n        return\n    write_idx = 0\n    for i in range(1, len(sorted_lst)):\n        if sorted_lst[i] != sorted_lst[write_idx]:\n            write_idx += 1\n            sorted_lst[write_idx] = sorted_lst[i]\n    del sorted_lst[write_idx+1:]\n", "char_start": 131, "char_end": 350, "patch_content": "\ndef remove_duplicates_in_place(lst):\n    \"\"\"Remove duplicates in-place from a list.\"\"\"\n    if not lst:\n        return\n    i = 0\n    while i < len(lst):\n        if i + 1 < len(lst) and lst[i] == lst[i + 1]:\n            lst.pop(i)\n        else:\n            i += 1\n\ndef remove_duplicates_in_place_2(lst):\n    \"\"\"Remove duplicates in-place from a list.\"\"\"\n    if not lst:\n        return\n    i = 0\n    while i < len(lst):\n        if i + 1 < len(lst) and lst[i] == lst[i + 1]:\n            lst.pop(i)\n        else:\n            i += 1\n\ndef remove_duplicates_in_place_3(lst):\n    \"\"\"Remove duplicates in-place from a list.\"\"\"\n    if not lst:\n        return\n    i = 0\n    while i < len(lst):\n        if i + 1", "patch_id": "py/remove_duplicates.py", "repository": "", "commit_sha": "", "file_name": "py/remove_duplicates.py", "_extra": {}}
{"file_content": "import dataclasses\nimport datetime\n\<EMAIL>\nclass Person:\n    name: str\n    dob: datetime.date\n    profession: str\n\n    def __str__(self):\n        return f\"{self.name} is a {self.profession} and is {self.dob.year} years old\"\n\n# EOF\n", "char_start": 154, "char_end": 240, "patch_content": "        return f\"{self.name} is {self.profession} and born on {self.dob}\"\n\nif __name__ == \"__main__\":\n    person = Person(name=\"John\", dob=datetime.date(1980, 1, 1), profession=\"Engineer\")\n    print(person)", "patch_id": "py/structure_7.py", "repository": "", "commit_sha": "", "file_name": "py/structure_7.py", "_extra": {}}
{"file_content": "import dataclasses\nimport datetime\n\<EMAIL>\nclass Person:\n    name: str\n    dob: datetime.date\n    profession: str\n\n    def __str__(self):\n        return f\"{self.name} is a {self.profession} and is {self.dob.year} years old\"\n", "char_start": 183, "char_end": 205, "patch_content": "is a {self.profession}", "patch_id": "py/structure_6.py", "repository": "", "commit_sha": "", "file_name": "py/structure_6.py", "_extra": {}}
{"file_content": "\nimport json\nfrom pathlib import Path\nfrom typing import Sequence\n\nfrom tqdm import tqdm\n\nfrom augment.research.static_analysis.fim_prompt import (\n    FimPromptFormatter,\n    FormattedFimProblem,\n)\nfrom augment.research.utils.generate_fim_data import (\n    FimDataProcessor,\n    IntRange,\n    make_indexed_dataset,\n    split_shuffle_pack_dataset,\n)\n\n\ndef as_million(x):\n    return f\"{x / 10e6:.3}M\"\n\n\ndef prob_statistics(probs: Sequence[FormattedFimProblem]):\n    total_tks = sum(len(p.tokens) for p in probs)\n    middle_tks = sum(len(p.middle_range) for p in probs)\n    prefix_tks = sum(len(p.prefix_range) for p in probs)\n    suffix_tks = sum(len(p.suffix_range) for p in probs)\n    empty_suffix_ratio = sum(not bool(p.suffix_range) for p in probs) / len(probs)\n\n    return {\n        \"n_probs\": str(len(probs)),\n        \"total_tks\": \n    }\n\n\n# %%\nseq_length = 1024 * 8\nprocessor = FimDataProcessor(\n    FimPromptFormatter.for_star_coder, seq_len_range=IntRange(1024, seq_length // 2)\n)\nstack_path = Path(\"/mnt/efs/augment-lga1/data/raw/the-stack-dedup.2023-02-04/data/\")\nproblems, errors = processor.fim_data_from_the_stack(\n    stack_path, language_maps={\"python\": \"python\"}, files_per_lang=500_000\n)\n", "char_start": 836, "char_end": 836, "patch_content": "as_million(total_tks),\n        \"middle_tks\": as_million(middle_tks),\n        \"prefix_tks\": as_million(prefix_tks),\n        \"suffix_tks\": as_million(suffix_tks),\n        \"empty_suffix_ratio\": empty_suffix_ratio,", "patch_id": "py/fim_script.py", "repository": "", "commit_sha": "", "file_name": "py/fim_script.py", "_extra": {}}
{"file_content": "\n@dataclass\nclass _GenericScoping:\n    \"\"\"Implements a basic scope tree parsing algorithm.\n\n    ### Arguments\n    - scope_types: a mapping from tree-sitter node type to scope definition. You can\n    use https://tree-sitter.github.io/tree-sitter/playground to learn the node types of\n    different languages.\n    - scope_decorator_types: a mapping from tree-sitter node type to scope decorator.\n    - seperator: the optional seperator between scopes. This is typically a semicolon.\n    \"\"\"\n\n    lang_id: LanguageID\n    scope_types: Mapping[TsNodeType, _ScopeDef]\n    scope_decorator_types: Mapping[TsNodeType, _DecorationDef]\n    docstr_def: Union[_JavaLikeDocStrDef, _PythonDocStrDef]\n    seperator: str = \";\"\n\n    def __post_init__(self):\n        self._sep_bytes = self.seperator.encode()\n\n    def register(self, override: bool = False) -> None:\n        \"\"\"Register self as the scoping support for the given language.\"\"\"\n        if self.lang_id in _scoping_supports and not override:\n            raise RuntimeError(f\"Scoping support already registered: {self.lang_id}.\")\n        _scoping_supports[self.lang_id] = self\n\n    def scope_source(self, code_str: str, parser: ScopeTreeParser) -> SrcScope:\n        \"\"\"Parse source code into a scope tree.\n\n        This works as follows:\n        1. identify all scopes in the given code (scopes can be nested,\n        and the entire file is always contained in a root scope)\n        2. for each scope, any continuous child region that's not already in a scope\n        becomes a span. Spans containing only whitespaces are merged with neighbors.\n        \"\"\"\n        bmap = ByteMap(code_str)\n        code_bytes = code_str.encode()\n\n        def mkspan(brange: CharRange):\n            \"\"\"Make a span without the code.\"\"\"\n            start, stop = brange.start, brange.stop\n            code = cast(Any, None)  # Delay this for performance.\n            return SrcSpan(CharRange(start, stop), code)\n\n        def _extend_start(start: int) -> int:\n            \"\"\"Move start leftward to include any leading spaces.\"\"\"\n            while start > 0 and code_bytes[start - 1] in _space_tab:\n                start -= 1\n            return start\n\n        def _extend_stop(stop: int) -> int:\n            \"\"\"Move stop rightward to include the trailing newline (if any).\"\"\"\n            if stop < len(code_bytes) and code_bytes[stop] == _newline:\n                stop += 1\n            return stop\n\n        def _extend_range(brange: ByteRange) -> ByteRange:\n            \"\"\"Get extended range that includes leading spaces and trailing newline.\"\"\"\n            left = _extend_start(brange.start)\n            right = _extend_stop(brange.stop)\n            return ByteRange(left, right)\n\n        def prev_end_byte(node: ts.Node) -> int:\n            \"\"\"Get the end byte of the previous sibling.\"\"\"\n            if prev := node.prev_sibling:\n                return _extend_stop(prev.end_byte)\n            else:\n                return _extend_start(node.start_byte)\n\n        def build_scope(\n            node: ts.Node,\n            scope_def: _ScopeDef,\n            in_class: bool,\n            wrapper_node: \"ts.Node|None\",\n        ) -> SrcScope:\n            \"\"\"Turn given node into a SrcScope.\n\n            - `in_class`: whether the current scope is within a class.\n            - `wrapper_node`: the outmost wrapper of this node (if any). In many\n            languages, decorators are implemented as a wrapper node.\n            \"\"\"\n            main_node = wrapper_node or node\n            main_range = (\n                ByteRange(0, len(code_bytes))\n                if scope_def.kind == \"file\"\n                else _get_brange(main_node)\n            )\n            if (\n                scope_def.braced\n                and (next_sib := main_node.next_sibling)\n                and len(_get_brange(next_sib)) == 1\n                and next_sib.text == self._sep_bytes\n            ):\n                # add trailing seperator as part of the suffix\n                main_range = ByteRange(main_range.start, next_sib.end_byte)\n            main_range = _extend_range(main_range)\n\n            body = _get_field_by_address(node, scope_def.body_field)\n            # scope body range should include any leading whitespaces\n            if scope_def.braced:\n                assert len(body.children) >= 2\n                left_range = _extend_range(_get_brange(body.children[0]))\n                right_range = _extend_range(_get_brange(body.children[-1]))\n                body_range = ByteRange(left_range.stop, right_range.start)\n                body_nodes = body.children[1:-1]\n            else:\n                body_range = ByteRange(prev_end_byte(body), _extend_stop(body.end_byte))\n                body_nodes = body.children\n\n            assert main_range.contains(body_range), f\"{main_node=}, {body=}\"\n            prefix_range, suffix_range = main_range.split_by_range(body_range)\n\n            name = \"\"\n            if scope_def.name_field:\n                name = _get_field_by_address(node, scope_def.name_field)\n                name = decode_bytes(name.text)\n\n            # Handle docstrings\n            if isinstance(self.docstr_def, _JavaLikeDocStrDef):\n                if (\n                    comment := main_node.prev_sibling\n                ) and comment.type == self.docstr_def.node_type:\n                    # look for more docstring comments\n                    while (\n                        more_comment := comment.prev_sibling\n                    ) and more_comment.type == self.docstr_def.node_type:\n                        comment = more_comment\n                    doc_range = ByteRange(\n                        _extend_start(comment.start_byte), main_range.start\n                    )\n                else:\n                    doc_range = ByteRange(main_range.start, main_range.start)\n            elif isinstance(self.docstr_def, _PythonDocStrDef):\n                if (\n                    len(body.children) >= 2\n                    and (comment := body.children[0]).type == \"expression_statement\"\n                    and (comment := comment.children[0]).type == \"string\"\n                ):\n                    # we consider the next children as the actual start of the body\n                    body_start = _extend_stop(body.children[0].end_byte)\n                    doc_range = ByteRange(prefix_range.stop, body_start)\n                    body_range = ByteRange(body_start, body_range.stop)\n                else:\n                    doc_range = ByteRange(prefix_range.stop, prefix_range.stop)\n            else:\n                raise NotImplementedError(f\"Unknown docstr def: {self.docstr_def}\")\n\n            if not (\n                prefix_range.stop == doc_range.start\n                or doc_range.stop == prefix_range.start\n            ):\n                raise AssertionError(\n                    \"Prefix not next to doc:\\n\"\n                    f\"  {main_range=}, {prefix_range=}, {doc_range=}, {body_range=}\\n\"\n                    f\"  {node=}\\n\"\n                    f\"  code={repr(code_bytes[main_range.slice()])}\"\n                )\n\n            if scope_def.is_leaf:\n                # treat entire body as a single span\n                subscopes = [mkspan(body_range)]\n            else:\n                # build child scopes\n                child_in_class = in_class or scope_def.kind == \"class\"\n                subscopes = [\n                    s\n                    for c in body_nodes\n                    if (s := build(c, child_in_class, None)) is not None\n                ]\n\n            # turn the gaps between subscopes into spans\n            children = list[ScopeOrSpan]()\n            current_range: ByteRange = body_range\n            for child_scope in subscopes:\n                left, current_range = current_range.split_by_range(child_scope.range)\n                children.append(mkspan(left))\n                children.append(child_scope)\n            children.append(mkspan(current_range))\n\n            scope = SrcScope(\n                name=name,\n                kind=scope_def.kind,\n                is_class_member=in_class or scope_def.must_be_class_member,\n                children=tuple(children),\n                prefix=mkspan(prefix_range),\n                docstr=mkspan(doc_range),\n                suffix=mkspan(suffix_range),\n            )\n            expect_start = min(main_range.start, scope.docstr.range.start)\n            assert_eq(scope.range.start, expect_start)\n            return scope\n\n        def build(\n            node: ts.Node, in_class: bool, wrapper_node: \"ts.Node | None\"\n        ) -> Optional[SrcScope]:\n            \"\"\"Turn given node into a SrcScope if possible.\"\"\"\n            if scope_def := self.scope_types.get(node.type):\n                return build_scope(node, scope_def, in_class, wrapper_node)\n            if dec_def := self.scope_decorator_types.get(node.type):\n                # Extend the prefix range of the decorated\n                decorated = _get_field_by_address(node, dec_def.decorated_field)\n                subscope = build(decorated, in_class, wrapper_node or node)\n                if subscope is None:\n                    if dec_def.allow_no_scope:\n                        return None\n                    raise ValueError(f\"build() return None for {decorated.sexp()}\")\n                return subscope\n            if parser.verbose:\n                warnings.warn(f\"Skip node: {node}\")\n            return None\n\n        def set_src(span: SrcSpan) -> None:\n            \"\"\"Convert the range into char range and add the source text.\"\"\"\n            start = bmap.byte_to_unicode(span.range.start)\n            stop = bmap.byte_to_unicode(span.range.stop)\n            span.code = code_str[start:stop]\n            span.range = \n\n        def rec_set_src(scope: SrcScope) -> None:\n            \"\"\"Add missing source code and remove empty src spans.\"\"\"\n            children_rev = list[ScopeOrSpan]()\n            # going in reversed order since we may want to merge with previous span\n            for i in reversed(range(len(scope.children))):\n                child = scope.children[i]\n                if not child.range:\n                    continue  # drop empty ones\n                if isinstance(child, SrcScope):\n                    rec_set_src(child)\n                    children_rev.append(child)\n                    continue\n                assert isinstance(child, SrcSpan)\n                set_src(child)\n                if child.code.strip():\n                    # keep non-empty spans\n                    children_rev.append(child)\n                    continue\n                # Merge empty span with the previous ScopeOrSpan\n                if i > 0:\n                    prev_span = scope.children[i - 1]\n                    # cut here\n                    if isinstance(prev_span, SrcScope):\n                        prev_span = prev_span.suffix\n                    prev_span.range = child.range.merge(prev_span.range)\n                else:\n                    children_rev.append(child)  # let's keep this in this case\n            scope.children = tuple(reversed(children_rev))\n            set_src(scope.prefix)\n            set_src(scope.suffix)\n            set_src(scope.docstr)\n\n        tree = parser.parse_ts_tree(code_bytes, self.lang_id)\n        root_scope = build(tree.root_node, in_class=False, wrapper_node=None)\n        if root_scope is None:\n            raise ValueError(f\"Failed to parse the code below:\\n{code_str}\")\n        rec_set_src(root_scope)\n        return root_scope\n\n\n# ---------------------------------------------------------------------------\n# --------------------------- supported langauges ---------------------------\n\n_GenericScoping(\n    lang_id=\"python\",\n    scope_types={\n        \"module\": _ScopeDef(\n            \"file\",\n            name_field=(),\n            body_field=(),\n            braced=False,\n        ),\n        \"class_definition\": _ScopeDef(\n            \"class\",\n        ),\n        \"function_definition\": _ScopeDef(\n            \"function\",\n            is_leaf=True,\n        ),\n    },\n    scope_decorator_types={\n        \"decorated_definition\": _DecorationDef(\n            decorated_field=\"definition\",\n        ),\n    },\n    docstr_def=_PythonDocStrDef(),\n).register()\n", "char_start": 9701, "char_end": 9701, "patch_content": "CharRange(start, stop)\n\n        def _get_brange(node: ts.Node) -> ByteRange:\n            \"\"\"Get the byte range of the given node.\"\"\"\n            return ByteRange(node.start_byte, node.end_byte)\n\n        def _get_field_by_address(node: ts.Node, field: ts.Node) -> ts.Node:\n            \"\"\"Get the field of the given node by its address.\"\"\"\n            for child in node.children:\n                if child.address == field.address:\n                    return child\n            raise ValueError(f\"Field not found: {field.sexp()}\")", "patch_id": "py/fim_real_ex_3.py", "repository": "", "commit_sha": "", "file_name": "py/fim_real_ex_3.py", "_extra": {}}
{"file_content": "\n@dataclass\nclass _GenericScoping:\n    \"\"\"Implements a basic scope tree parsing algorithm.\n\n    ### Arguments\n    - scope_types: a mapping from tree-sitter node type to scope definition. You can\n    use https://tree-sitter.github.io/tree-sitter/playground to learn the node types of\n    different languages.\n    - scope_decorator_types: a mapping from tree-sitter node type to scope decorator.\n    - seperator: the optional seperator between scopes. This is typically a semicolon.\n    \"\"\"\n\n    lang_id: LanguageID\n    scope_types: Mapping[TsNodeType, _ScopeDef]\n    scope_decorator_types: Mapping[TsNodeType, _DecorationDef]\n    docstr_def: Union[_JavaLikeDocStrDef, _PythonDocStrDef]\n    seperator: str = \";\"\n\n    def __post_init__(self):\n        self._sep_bytes = self.seperator.encode()\n\n    def register(self, override: bool = False) -> None:\n        \"\"\"Register self as the scoping support for the given language.\"\"\"\n        if self.lang_id in _scoping_supports and not override:\n            raise RuntimeError(f\"Scoping support already registered: {self.lang_id}.\")\n        _scoping_supports[self.lang_id] = self\n\n    def scope_source(self, code_str: str, parser: ScopeTreeParser) -> SrcScope:\n        \"\"\"Parse source code into a scope tree.\n\n        This works as follows:\n        1. identify all scopes in the given code (scopes can be nested,\n        and the entire file is always contained in a root scope)\n        2. for each scope, any continuous child region that's not already in a scope\n        becomes a span. Spans containing only whitespaces are merged with neighbors.\n        \"\"\"\n        bmap = ByteMap(code_str)\n        code_bytes = code_str.encode()\n\n        def mkspan(brange: CharRange):\n            \"\"\"Make a span without the code.\"\"\"\n            start, stop = brange.start, brange.stop\n            code = cast(Any, None)  # Delay this for performance.\n            return SrcSpan(CharRange(start, stop), code)\n\n        def _extend_start(start: int) -> int:\n            \"\"\"Move start leftward to include any leading spaces.\"\"\"\n            while start > 0 and code_bytes[start - 1] in _space_tab:\n                start -= 1\n            return start\n\n        def _extend_stop(stop: int) -> int:\n            \"\"\"Move stop rightward to include the trailing newline (if any).\"\"\"\n            if stop < len(code_bytes) and code_bytes[stop] == _newline:\n                stop += 1\n            return stop\n\n        def _extend_range(brange: ByteRange) -> ByteRange:\n            \"\"\"Get extended range that includes leading spaces and trailing newline.\"\"\"\n            left = _extend_start(brange.start)\n            right = _extend_stop(brange.stop)\n            return ByteRange(left, right)\n\n        def prev_end_byte(node: ts.Node) -> int:\n            \"\"\"Get the end byte of the previous sibling.\"\"\"\n            if prev := node.prev_sibling:\n                return _extend_stop(prev.end_byte)\n            else:\n                return _extend_start(node.start_byte)\n\n        def build_scope(\n            node: ts.Node,\n            scope_def: _ScopeDef,\n            in_class: bool,\n            wrapper_node: \"ts.Node|None\",\n        ) -> SrcScope:\n            \"\"\"Turn given node into a SrcScope.\n\n            - `in_class`: whether the current scope is within a class.\n            - `wrapper_node`: the outmost wrapper of this node (if any). In many\n            languages, decorators are implemented as a wrapper node.\n            \"\"\"\n            main_node = wrapper_node or node\n            main_range = (\n                ByteRange(0, len(code_bytes))\n                if scope_def.kind == \"file\"\n                else _get_brange(main_node)\n            )\n            if (\n                scope_def.braced\n                and (next_sib := main_node.next_sibling)\n                and len(_get_brange(next_sib)) == 1\n                and next_sib.text == self._sep_bytes\n            ):\n                # add trailing seperator as part of the suffix\n                main_range = ByteRange(main_range.start, next_sib.end_byte)\n            main_range = _extend_range(main_range)\n\n            body = _get_field_by_address(node, scope_def.body_field)\n            # scope body range should include any leading whitespaces\n            if scope_def.braced:\n                assert len(body.children) >= 2\n                left_range = _extend_range(_get_brange(body.children[0]))\n                right_range = _extend_range(_get_brange(body.children[-1]))\n                body_range = ByteRange(left_range.stop, right_range.start)\n                body_nodes = body.children[1:-1]\n            else:\n                body_range = ByteRange(prev_end_byte(body), _extend_stop(body.end_byte))\n                body_nodes = body.children\n\n            assert main_range.contains(body_range), f\"{main_node=}, {body=}\"\n            prefix_range, suffix_range = main_range.split_by_range(body_range)\n\n            name = \"\"\n            if scope_def.name_field:\n                name = _get_field_by_address(node, scope_def.name_field)\n                name = decode_bytes(name.text)\n\n            # Handle docstrings\n            if isinstance(self.docstr_def, _JavaLikeDocStrDef):\n                if (\n                    comment := main_node.prev_sibling\n                ) and comment.type == self.docstr_def.node_type:\n                    # look for more docstring comments\n                    while (\n                        more_comment := comment.prev_sibling\n                    ) and more_comment.type == self.docstr_def.node_type:\n                        comment = more_comment\n                    doc_range = ByteRange(\n                        _extend_start(comment.start_byte), main_range.start\n                    )\n                else:\n                    doc_range = ByteRange(main_range.start, main_range.start)\n            elif isinstance(self.docstr_def, _PythonDocStrDef):\n                if (\n                    len(body.children) >= 2\n                    and (comment := body.children[0]).type == \"expression_statement\"\n                    and (comment := comment.children[0]).type == \"string\"\n                ):\n                    # we consider the next children as the actual start of the body\n                    body_start = _extend_stop(body.children[0].end_byte)\n                    doc_range = ByteRange(prefix_range.stop, body_start)\n                    body_range = ByteRange(body_start, body_range.stop)\n                else:\n                    doc_range = ByteRange(prefix_range.stop, prefix_range.stop)\n            else:\n                raise NotImplementedError(f\"Unknown docstr def: {self.docstr_def}\")\n\n            if not (\n                prefix_range.stop == doc_range.start\n                or doc_range.stop == prefix_range.start\n            ):\n                raise AssertionError(\n                    \"Prefix not next to doc:\\n\"\n                    f\"  {main_range=}, {prefix_range=}, {doc_range=}, {body_range=}\\n\"\n                    f\"  {node=}\\n\"\n                    f\"  code={repr(code_bytes[main_range.slice()])}\"\n                )\n\n            if scope_def.is_leaf:\n                # treat entire body as a single span\n                subscopes = [mkspan(body_range)]\n            else:\n                # build child scopes\n                child_in_class = in_class or scope_def.kind == \"class\"\n                subscopes = [\n                    s\n                    for c in body_nodes\n                    if (s := build(c, child_in_class, None)) is not None\n                ]\n\n            # turn the gaps between subscopes into spans\n            children = list[ScopeOrSpan]()\n            current_range: ByteRange = body_range\n            for child_scope in subscopes:\n                left, current_range = current_range.split_by_range(child_scope.range)\n                children.append(mkspan(left))\n                children.append(child_scope)\n            children.append(mkspan(current_range))\n\n            scope = SrcScope(\n                name=name,\n                kind=scope_def.kind,\n                is_class_member=in_class or scope_def.must_be_class_member,\n                children=tuple(children),\n                prefix=mkspan(prefix_range),\n                docstr=mkspan(doc_range),\n                suffix=mkspan(suffix_range),\n            )\n            expect_start = min(main_range.start, scope.docstr.range.start)\n            assert_eq(scope.range.start, expect_start)\n            return scope\n\n        def build(\n            node: ts.Node, in_class: bool, wrapper_node: \"ts.Node | None\"\n        ) -> Optional[SrcScope]:\n            \"\"\"Turn given node into a SrcScope if possible.\"\"\"\n            if scope_def := self.scope_types.get(node.type):\n                return build_scope(node, scope_def, in_class, wrapper_node)\n            if dec_def := self.scope_decorator_types.get(node.type):\n                # Extend the prefix range of the decorated\n                decorated = _get_field_by_address(node, dec_def.decorated_field)\n                subscope = build(decorated, in_class, wrapper_node or node)\n                if subscope is None:\n                    if dec_def.allow_no_scope:\n                        return None\n                    raise ValueError(f\"build() return None for {decorated.sexp()}\")\n                return subscope\n            if parser.verbose:\n                warnings.warn(f\"Skip node: {node}\")\n            return None\n\n        def set_src(span: SrcSpan) -> None:\n            \"\"\"Convert the range into char range and add the source text.\"\"\"\n            start = bmap.byte_to_unicode(span.range.start)\n            stop = bmap.byte_to_unicode(span.range.stop)\n            span.code = code_str[start:stop]\n            span.range = CharRange(start, stop)\n\n        def rec_set_src(scope: SrcScope) -> None:\n            \"\"\"Add missing source code and remove empty src spans.\"\"\"\n            children_rev = list[ScopeOrSpan]()\n            # going in reversed order since we may want to merge with previous span\n            for i in reversed(range(len(scope.children))):\n                child = scope.children[i]\n                if not child.range:\n                    continue  # drop empty ones\n                if isinstance(child, SrcScope):\n                    \n                assert isinstance(child, SrcSpan)\n                set_src(child)\n                if child.code.strip():\n                    # keep non-empty spans\n                    children_rev.append(child)\n                    continue\n                # Merge empty span with the previous ScopeOrSpan\n                if i > 0:\n                    prev_span = scope.children[i - 1]\n                    if isinstance(prev_span, SrcScope):\n                        prev_span = prev_span.suffix\n                    prev_span.range = child.range.merge(prev_span.range)\n                else:\n                    children_rev.append(child)  # let's keep this in this case\n            scope.children = tuple(reversed(children_rev))\n            set_src(scope.prefix)\n            set_src(scope.suffix)\n            set_src(scope.docstr)\n\n        tree = parser.parse_ts_tree(code_bytes, self.lang_id)\n        root_scope = build(tree.root_node, in_class=False, wrapper_node=None)\n        if root_scope is None:\n            raise ValueError(f\"Failed to parse the code below:\\n{code_str}\")\n        rec_set_src(root_scope)\n        return root_scope\n\n\n# ---------------------------------------------------------------------------\n# --------------------------- supported langauges ---------------------------\n\n_GenericScoping(\n    lang_id=\"python\",\n    scope_types={\n        \"module\": _ScopeDef(\n            \"file\",\n            name_field=(),\n            body_field=(),\n            braced=False,\n        ),\n        \"class_definition\": _ScopeDef(\n            \"class\",\n        ),\n        \"function_definition\": _ScopeDef(\n            \"function\",\n            is_leaf=True,\n        ),\n    },\n    scope_decorator_types={\n        \"decorated_definition\": _DecorationDef(\n            decorated_field=\"definition\",\n        ),\n    },\n    docstr_def=_PythonDocStrDef(),\n).register()\n", "char_start": 10229, "char_end": 10229, "patch_content": "rec_set_src(child)\n                    children_rev.append(child)\n                    continue\n                # child is a span", "patch_id": "py/fim_real_ex_4.py", "repository": "", "commit_sha": "", "file_name": "py/fim_real_ex_4.py", "_extra": {}}
{"file_content": "\ndef get_line_at_pct(fname, pct):\n    file = open(fname)\n    lines = sum(1 for line in file)\n    file.close()\n\n    line_no = int(pct * lines)\n    file = open(fname)\n    for i, line in enumerate(file):\n        if i == line_no:\n            return line\n\n# EOF\n", "char_start": 142, "char_end": 250, "patch_content": "    file = open(fname)\n    for i, line in enumerate(file):\n        if i == line_no:\n            return line\n    file.close()\n    return None\n\n# vim:ts=4:sw=4:expandtab", "patch_id": "py/resources_1.py", "repository": "", "commit_sha": "", "file_name": "py/resources_1.py", "_extra": {}}
{"file_content": "import copy\nfrom dataclasses import dataclass, field\nfrom typing import *\n\n@dataclass\nclass Patch:\n    \"\"\"Represents a patch to a file using its character span.\"\"\"\n\n    file_content: str = \"\"\n    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n    char_start: int = 0\n    \"\"\"Character offset for the patch.\"\"\"\n    char_end: int = 0\n    \"\"\"character offset for the patch.\"\"\"\n    patch_content: str = \"\"\n    \"\"\"the patch content.\"\"\"\n    patch_id: str = \"\"\n\n    @classmethod\n    def for_span(\n        cls,\n        file_content: str,\n        char_start: int,\n        char_end: int,\n        **kwargs,\n    ):\n        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n        return cls(\n            file_content=file_content,\n            char_start=char_start,\n            char_end=char_end,\n            patch_content=file_content[char_start:char_end],\n            **kwargs,\n        )\n\n    def shift(self, amount: int) -> \"Patch\":\n        result = copy.copy(self)\n        result.char_start += amount\n        result.char_end += amount\n        return result\n\n    @classmethod\n    def empty(cls, file_content: str, **kwargs):\n        \"\"\"Create an empty Patch for a file.\"\"\"\n        return Patch(\n            file_content=file_content,\n            char_start=0,\n            char_end=0,\n            patch_content=\"\",\n            **kwargs,\n        )\n", "char_start": 1021, "char_end": 1113, "patch_content": "        result.char_start += amount\n        result.char_end += amount\n        result.patch_content = result.file_content[result.char_start:result.char_end]\n        return result\n", "patch_id": "py/fim_dataclass_methods.py", "repository": "", "commit_sha": "", "file_name": "py/fim_dataclass_methods.py", "_extra": {}}
{"file_content": "\nimport dataclasses\n\n\<EMAIL>\nclass Person:\n    name: str\n    age: int\n    profession: str\n\n\ndef sort_by_age(people):\n    people.sort(key=lambda x: x.age)\n\n\ndef test_sort_by_age():\n    p1 = Person(\"Alice\", 42, \"Engineer\")\n    p2 = Person(\"Bill\", 22, \"Marketer\")\n    p3 = Person(\"Christine\", 12, \"\")\n    people = [p1, p2, p3]\n    sort_by_age(people)\n    assert people == [p3, p1, p2]\n\n\n# EOF\n", "char_start": 196, "char_end": 314, "patch_content": "    p1 = Person(\"John\", 25, \"Teacher\")\n    p2 = Person(\"Jane\", 23, \"Teacher\")\n    p3 = Person(\"Bob\", 21, \"Teacher\")\n", "patch_id": "py/testing_2.py", "repository": "", "commit_sha": "", "file_name": "py/testing_2.py", "_extra": {}}
{"file_content": "import copy\nfrom dataclasses import asdict, dataclass\nimport json\nimport pathlib\n\n@dataclass\nclass MyClass:\n    def save_to_json(self, filename):\n        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n        with pathlib.Path(filename).open(\"w\") as f:\n            json.dump(asdict(self), f)\n\n    @classmethod\n    def load_from_json(cls, filename):\n        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n        with pathlib.Path(filename).open(\"r\") as f:\n            data = json.load(f)\n\n        return cls(**data)\n\n    def clone(self):\n        \"\"\"Returns a deep copy of the instance.\"\"\"\n        return copy.deepcopy(self)\n\n    def __repr__(self):\n        attributes = \",\\n  \".join(\n            f\"{key} = {value!r}\" for key, value in vars(self).items()\n        )\n        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "char_start": 597, "char_end": 597, "patch_content": "", "patch_id": "py/archived/fim_multi_function_1.py", "repository": "", "commit_sha": "", "file_name": "py/archived/fim_multi_function_1.py", "_extra": {}}
{"file_content": "def get_odd_ints(lower, upper):\n    results = [i for i in range(lower, upper) if i % 2 == 1]\n\n    return results\n", "char_start": 36, "char_end": 93, "patch_content": "results = []\n    for i in range(lower, upper + 1, 2):\n        results.append(i)", "patch_id": "py/archived/fim_trailing_return.py", "repository": "", "commit_sha": "", "file_name": "py/archived/fim_trailing_return.py", "_extra": {}}
{"file_content": "def hello_world():\n    print(\"Hello World!\")\n", "char_start": 18, "char_end": 18, "patch_content": "", "patch_id": "py/archived/fim_noop.py", "repository": "", "commit_sha": "", "file_name": "py/archived/fim_noop.py", "_extra": {}}
{"file_content": "def hello_world():\n    print(\"Hello World!\")\n", "char_start": 19, "char_end": 23, "patch_content": "    ", "patch_id": "py/archived/fim_leading_spaces.py", "repository": "", "commit_sha": "", "file_name": "py/archived/fim_leading_spaces.py", "_extra": {}}

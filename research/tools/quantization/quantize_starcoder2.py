r"""Quantize StarCoder2 models.

python quantize_starcoder2.py \
    --calibration-steps 200 \
    --log-to-stdout \
    --ckpt-path "/mnt/efs/augment/path/to/your/input/checkpoint" \
    --out-path "/mnt/efs/augment/path/to/your/output/checkpoint" \
    --calibration-data "/mnt/efs/augment/path/to/your/dataset"

NOTE: calibration-data should be the *last* dataset that the BF16 model was trained on.
"""

# pyright rightfully complains about the megatron import
# pyright: reportMissingImports=false
import argparse
import logging
import random
import shutil
import sys
from pathlib import Path
from typing import TypeVar

import numpy as np
import torch.nn as nn

from base.fastforward import quantize_utils
from base.fastforward.checkpoints import save_load
from base.fastforward.starcoder import fwd_starcoder2, model_specs
from research.fastbackward.loss_masking import unmask_token_np

ModuleT = TypeVar("ModuleT", bound=nn.Module)


def main(args):
    # TODO: We should move the quantization scripts into bazel CI so that
    # changes to the models that affect the quantization scripts will be caught.
    # That requires us to add code to read these datasets to fastforward or base.
    from research.data.dataset.indexed_dataset import make_dataset

    ms = model_specs.starcoder2_model_spec_from_checkpoint(args.ckpt_path)
    if args.ckpt_sha256:
        print(f"Overriding checkpoint_sha256 to be {args.ckpt_sha256}")
        ms.checkpoint_sha256 = args.ckpt_sha256
    max_seq_len = ms.max_position_embeddings

    datasets = []
    for path in args.calibration_data:
        datasets.append(make_dataset(path, "mmap", skip_warmup=True))
        assert datasets[-1] is not None
    print(f"Create {len(args.calibration_data)} datasets:\n{args.calibration_data}")
    assert len(datasets)
    calibration_data_ = list[np.ndarray]()
    for i in range(args.calibration_steps):
        for dataset in datasets:
            calibration_data_.append(unmask_token_np(dataset.get(i))[:max_seq_len])
    random.shuffle(calibration_data_)
    calibration_data = []
    for x in calibration_data_:
        calibration_data.append(x[x < args.vocab_threshold])
    print(f"With calibration steps={args.calibration_steps} loaded {len(calibration_data)} examples")  # fmt: skip
    assert len(calibration_data), "calibration_data is empty"
    bf16_model = fwd_starcoder2.generate_step_fn(ms)
    attn = fwd_starcoder2.StarCoder2AttentionFactory(
        ms, pre_attention_kernel_fusion=False, capture_attn_maxes_for_quantization=True
    )(max_seq_len + 1)
    assert isinstance(bf16_model, fwd_starcoder2.StarCoder2)

    calibration_data_kwargs = [{"tokens": x} for x in calibration_data]

    weights_e4m3, _ = quantize_utils.quantize_layer(
        layer_name="starcoder2",
        layer_inp=bf16_model,
        calibration_data=calibration_data_kwargs,
        reset_fn=attn.reset,
        attn=attn,
    )
    quantize_utils.update_weights_with_attn_scales(
        weights_e4m3, attn.captured_attn_maxes, ms.num_layers
    )

    # Write the FP8 checkpoint
    out_path = args.out_path.absolute()
    logging.info("Saving quantized weights to %s", str(out_path))
    out_path.mkdir(parents=True, exist_ok=True)
    save_load.save_weights(out_path, weights_e4m3, incremental=True)
    # copy params.json to the new checkpoint dir
    shutil.copy(args.ckpt_path / "params.json", out_path / "params.json")
    logging.warning("Remember to fix the checkpoint SHA in the new params.json.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Quantize StarCoder2")
    parser.add_argument("-d", "--calibration-data", type=str, required=True, nargs="+")
    parser.add_argument("-n", "--calibration-steps", type=int, default=1024)
    parser.add_argument("-s", "--ckpt-sha256", type=str, default=None)
    parser.add_argument(
        "-v",
        "--vocab-threshold",
        type=int,
        default=52000,
        help=(
            "Ignore the input tokens with index larger than this threshold. "
            "Helpful when the input has tokens that are not in the vocab for "
            "models that are **not** yet finetuned."
        ),
    )

    parser.add_argument("--log-to-stdout", dest="log_to_stdout", action="store_true")
    parser.add_argument(
        "--no-log-to-stdout", dest="log_to_stdout", action="store_false"
    )
    parser.set_defaults(log_to_stdout=True)

    parser.add_argument("-p", "--ckpt-path", type=Path, default=Path())
    parser.add_argument("-o", "--out-path", type=Path, default=Path())
    args = parser.parse_args()

    class _CustomFormatter(logging.Formatter):
        """Logging Formatter to add colors."""

        GREEN = "\x1b[32;20m"
        YELLOW = "\x1b[33;20m"
        RESET = "\x1b[0m"

        FORMATS = {
            logging.INFO: rf"{GREEN}%(levelname)s{RESET} [%(name)s] %(message)s",
            logging.DEBUG: rf"{YELLOW}%(levelname)s{RESET} [%(name)s] %(message)s",
        }

        def format(self, record):
            log_fmt = self.FORMATS.get(record.levelno, self._fmt)
            formatter = logging.Formatter(log_fmt, self.datefmt)
            return formatter.format(record)

    if args.log_to_stdout:
        logging.basicConfig(stream=sys.stdout, level=logging.DEBUG)
        logging.root.handlers[0].setFormatter(_CustomFormatter())
    else:
        ckpt_path: Path = args.ckpt_path
        file_name = f"quantize.{ckpt_path.name}.{args.calibration_steps}.log"
        logging.basicConfig(filename=file_name, encoding="utf-8", level=logging.INFO)

    main(args)

"""Test layer-by-layer LLAMA quantization by quantizing a LLAMA-350M model."""

import tempfile
from pathlib import Path
from typing import Sequence

import torch

from base.fastforward import quantize_utils
from base.fastforward.checkpoints import save_load
from base.fastforward.llama import fwd_llama, model_specs
from research.tools.quantization import quantize_llama_layer_by_layer

LLAMA_350M_MODEL_NAME = "llama-350m"

LLAMA_350M_CHECKPOINT_PATH = Path(
    "/mnt/efs/augment/checkpoints/v2/llama/fastforward/llama-350m"
)

LLAMA_350M_CHECKPOINT_SHA256 = (
    "e22e039ec50c175821833e5b7110e6ffb2ee881a310bccc13527f85861593b5e"
)


def _compare_weights(checkpoint1: Path, checkpoint2: Path):
    weights1 = save_load.load_weights(checkpoint1, override_sha_check=True)
    weights2 = save_load.load_weights(checkpoint2, override_sha_check=True)
    assert weights1.keys() == weights2.keys()
    for name in weights1.keys():
        if name.endswith("attn_qkv_scales"):
            # NOTE(carl): empirically, the attention amaxes can be _slightly_ different between
            # full quantization and layer-by-layer quantization. I don't fully understand this,
            # but it's not a big deal. Since small changes can halve/double the attention scale,
            # we manually check that the computed scales are within a factor of two in either
            # direction.
            ratios = weights1[name] / weights2[name]
            for ratio in ratios:
                # A true equality check on fp values is OK here, since we're checking exact
                # powers-of-two.
                assert ratio.item() in (0.5, 1.0, 2.0)
        else:
            assert torch.allclose(weights1[name], weights2[name])


def _quantize_llama_all_layers_at_once(
    model_name: str,
    checkpoint_path: Path,
    checkpoint_sha256: str,
    calibration_data: Sequence[dict[str, torch.Tensor]],  # type: ignore
    out_path: Path,
):
    ms = model_specs.get_llama_model_spec(
        model_name=model_name,
        checkpoint_path=checkpoint_path,
        checkpoint_sha256=checkpoint_sha256,
    )
    max_seq_len = ms.max_position_embeddings

    bf16_model = fwd_llama.generate_step_fn(ms)
    attn = fwd_llama.LlamaAttentionFactory(
        ms, capture_attn_maxes_for_quantization=True, pre_attention_kernel_fusion=False
    )(max_seq_len + 1)

    weights_e4m3, _ = quantize_utils.quantize_layer(
        layer_name="llama",
        layer_inp=bf16_model,  # type: ignore
        calibration_data=calibration_data,
        reset_fn=attn.reset,
        attn=attn,
    )
    quantize_utils.update_weights_with_attn_scales(
        weights_e4m3, attn.captured_attn_maxes, ms.num_layers
    )

    # Write the FP8 checkpoint
    out_path = out_path.absolute()
    out_path.mkdir(parents=True, exist_ok=True)
    save_load.save_weights(out_path, weights_e4m3, incremental=False)


def test_quantize_model():
    torch.set_printoptions(sci_mode=False, precision=5, linewidth=200)

    with tempfile.TemporaryDirectory() as out_all_layers_at_once, tempfile.TemporaryDirectory() as out_layer_by_layer:
        out_all_layers_at_once = Path(out_all_layers_at_once)
        out_layer_by_layer = Path(out_layer_by_layer)

        calibration_data_kwargs = [
            {
                "tokens": [
                    21,
                    12000,
                    5788,
                    3460,
                    328,
                    4865,
                    203,
                    589,
                    2575,
                    2262,
                    203,
                    202,
                    1216,
                    440,
                    8279,
                    5788,
                ]
            }
        ]
        calibration_data_kwargs = [
            {k: torch.tensor(v, dtype=torch.int32) for k, v in datum.items()}
            for datum in calibration_data_kwargs
        ]
        _quantize_llama_all_layers_at_once(
            model_name=LLAMA_350M_MODEL_NAME,
            checkpoint_path=LLAMA_350M_CHECKPOINT_PATH,
            checkpoint_sha256=LLAMA_350M_CHECKPOINT_SHA256,
            calibration_data=calibration_data_kwargs,
            out_path=out_all_layers_at_once,
        )

        quantize_llama_layer_by_layer.quantize_llama_layer_by_layer(
            model_name=LLAMA_350M_MODEL_NAME,
            checkpoint_path=LLAMA_350M_CHECKPOINT_PATH,
            checkpoint_sha256=LLAMA_350M_CHECKPOINT_SHA256,
            calibration_data=calibration_data_kwargs,
            out_path=out_layer_by_layer,
        )

        _compare_weights(
            checkpoint1=out_all_layers_at_once,
            checkpoint2=out_layer_by_layer,
        )

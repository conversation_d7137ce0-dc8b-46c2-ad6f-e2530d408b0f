"""Test LLAMA quantization by quantizing a LLAMA-350M model."""

import torch

from base.fastforward import (
    fwd_utils,
    quantize_utils,
)
from base.fastforward.starcoder import (
    fwd_starcoder2,
    fwd_starcoder2_fp8,
)


def test_quantize_model(
    starcoder2_100m_bf16_fixture, starcoder2_100m_bf16_model_spec_fixture
):
    torch.set_printoptions(sci_mode=False, precision=5, linewidth=200)

    ms = starcoder2_100m_bf16_model_spec_fixture
    bf16_model, _ = starcoder2_100m_bf16_fixture
    # Initializing a separate attention factory to set the quantization flag.
    attn_factory = fwd_starcoder2.StarCoder2AttentionFactory(
        ms, capture_attn_maxes_for_quantization=True, pre_attention_kernel_fusion=False
    )
    attn = attn_factory(1024)
    # Note: want a non-attn-max-capturing attention factory for the FP8 model.
    attn_factory_fp8 = fwd_starcoder2.StarCoder2AttentionFactory(
        ms, capture_attn_maxes_for_quantization=False
    )
    attn_fp8 = attn_factory_fp8(1024)

    num_output_tokens = 5

    calibration_data = [
        {
            "tokens": [6, 85, 5824, 399, 2044, 2079, 52, 2044, 52, 5248],
        }
    ]

    attn.reset()
    bf16_logits = bf16_model.forward(calibration_data[0]["tokens"], attn).checked_cast(
        torch.Tensor
    )
    bf16_top_k = torch.topk(bf16_logits[-num_output_tokens:], k=2)
    bf16_tokens = bf16_top_k.indices

    attn.reset()
    weights_e4m3, _ = quantize_utils.quantize_layer(
        layer_name="starcoder2",
        layer_inp=bf16_model,
        calibration_data=calibration_data,
        reset_fn=attn.reset,
        attn=attn,
    )
    quantize_utils.update_weights_with_attn_scales(
        weights_e4m3, attn.captured_attn_maxes, ms.num_layers
    )

    fp8_model = fwd_starcoder2_fp8.StarCoder2(ms=ms)
    fp8_model.load_state_dict(weights_e4m3)

    attn_fp8.reset()
    fp8_step_fn = fwd_utils.pad_and_step(fp8_model, round_sizes=[8])
    fp8_logits = fp8_step_fn(calibration_data[0]["tokens"], attn_fp8).checked_cast(
        torch.Tensor
    )
    fp8_top_k = torch.topk(fp8_logits[-num_output_tokens:], k=2)
    fp8_top_k_tokens = fp8_top_k.indices

    # Mask out the one mismatch. Some changes of outputs are to be expected
    # in quantization.
    bf16_tokens[1] = 0
    fp8_top_k_tokens[1] = 0

    assert (
        bf16_tokens == fp8_top_k_tokens
    ).all(), f"{bf16_tokens=} but {fp8_top_k_tokens=}."

r"""Quantize LLAMA models.

python quantize_llama.py \
    --calibration-steps 200 \
    --log-to-stdout \
    --model-size "deepseek-33b" \
    --ckpt-path "/mnt/efs/augment/path/to/your/input/model" \
    --out-path "/mnt/efs/augment/path/to/your/output/model" \
    --calibration-data "/mnt/efs/augment/path/to/your/dataset"

NOTE: calibration-data should be the *last* dataset that the BF16 model was trained on.
"""
# pyright rightfully complains about the megatron import
# pyright: reportMissingImports=false

import logging
import sys
from pathlib import Path
from typing import TypeVar

import torch.nn as nn
from torch import Tensor

from base.fastforward import quantize_utils
from base.fastforward.checkpoints import save_load
from base.fastforward.cached_attention import AttentionImpl
from base.fastforward import layers
from base.fastforward.fwd import OutputTensorType
from base.fastforward.llama import fwd_llama, model_specs
from research.fastbackward.loss_masking import unmask_token_np

ModuleT = TypeVar("ModuleT", bound=nn.Module)


def llama_smooth_quant_fn(model: nn.Module, input_amaxes: dict[str, Tensor]):
    assert isinstance(model, fwd_llama.Llama)
    for name, module in model.named_modules():
        if isinstance(module, layers.LlamaTransformerBlock):
            qkv_amax = input_amaxes[f"{name}.attn.qkv"]
            input_prescale = quantize_utils.compute_input_prescale(
                module.attn.qkv.weight.data, qkv_amax
            )
            module.attn_norm.weight.div_(input_prescale)
            module.attn.qkv.weight.data.mul_(input_prescale)

            expand_amax = input_amaxes[f"{name}.ffn.expand"]
            input_prescale = quantize_utils.compute_input_prescale(
                module.ffn.expand.weight.data, expand_amax
            )
            module.ffn_norm.weight.div_(input_prescale)
            module.ffn.expand.weight.data.mul_(input_prescale)


def main(args):
    # using dynamic import to enable sharing this code mostly with production
    # code (there are build targets!) while still allowing us to run this script
    # locally with our data set loaders from research.
    from research.data.dataset.indexed_dataset import make_dataset

    ms = model_specs.get_llama_model_spec(
        model_name=args.model_size,
        checkpoint_path=args.ckpt_path,
        checkpoint_sha256=args.ckpt_sha256,
    )

    dataset = make_dataset(args.calibration_data, "mmap", skip_warmup=True)
    assert dataset is not None
    # TODO(Xuanyi): we masked out a lot of tokens during the fine-tuning, such as prompt or paddings.
    # I think they shouldn't be treated equally as the target tokens.
    # We need to evaluate the difference between whether to include them or not during the calibration.
    calibration_data = [
        unmask_token_np(dataset.get(i)) for i in range(args.calibration_steps)
    ]
    max_seq_len = args.max_seq_len or calibration_data[0].shape[0]
    logging.warning("Max seq len from calibration data: %d", max_seq_len)

    output_type = (
        OutputTensorType.EMBEDDING
        if args.is_embedder
        else OutputTensorType.VOCAB_LOGITS
    )
    bf16_model = fwd_llama.generate_step_fn(ms, output_type=output_type)
    attn = fwd_llama.LlamaAttentionFactory(
        ms,
        attention_impl=AttentionImpl.MULTI_REQUEST_FLASH_V3,
        pre_attention_kernel_fusion=False,
        capture_attn_maxes_for_quantization=True,
    )(max_seq_len + 1)
    assert isinstance(bf16_model, fwd_llama.Llama)

    calibration_data_kwargs = [{"tokens": x[:max_seq_len]} for x in calibration_data]

    weights_e4m3, _ = quantize_utils.quantize_layer(
        layer_name="llama",
        layer_inp=bf16_model,
        smooth_quant_fn=llama_smooth_quant_fn if args.smoothquant else None,  # type: ignore
        calibration_data=calibration_data_kwargs,
        reset_fn=attn.reset,
        attn=attn,
        excludes=("output_projection",),
    )
    quantize_utils.update_weights_with_attn_scales(
        weights_e4m3, attn.captured_attn_maxes, ms.num_layers
    )

    # Write the FP8 checkpoint
    out_path = args.out_path.absolute()
    logging.info("Saving quantized weights to %s", str(out_path))
    out_path.mkdir(parents=True, exist_ok=True)
    save_load.save_weights(out_path, weights_e4m3, incremental=True)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Quantize LLAMA")
    parser.add_argument("-m", "--model-size", type=str, required=True)
    parser.add_argument("-d", "--calibration-data", type=str, required=True)
    parser.add_argument("-n", "--calibration-steps", type=int, default=1024)
    parser.add_argument("--is-embedder", action="store_true", default=False)
    parser.add_argument("--max-seq-len", type=int, default=None)
    parser.add_argument("--smoothquant", action="store_true", default=False)

    parser.add_argument("--log-to-stdout", dest="log_to_stdout", action="store_true")
    parser.add_argument(
        "--no-log-to-stdout", dest="log_to_stdout", action="store_false"
    )
    parser.set_defaults(log_to_stdout=True)

    parser.add_argument("-p", "--ckpt-path", type=Path, default=Path())
    parser.add_argument("-s", "--ckpt-sha256", type=str, default="")
    parser.add_argument("-o", "--out-path", type=Path, default=Path())
    args = parser.parse_args()

    class _CustomFormatter(logging.Formatter):
        """Logging Formatter to add colors."""

        GREEN = "\x1b[32;20m"
        YELLOW = "\x1b[33;20m"
        RESET = "\x1b[0m"

        FORMATS = {
            logging.INFO: rf"{GREEN}%(levelname)s{RESET} [%(name)s] %(message)s",
            logging.DEBUG: rf"{YELLOW}%(levelname)s{RESET} [%(name)s] %(message)s",
        }

        def format(self, record):
            log_fmt = self.FORMATS.get(record.levelno, self._fmt)
            formatter = logging.Formatter(log_fmt, self.datefmt)
            return formatter.format(record)

    if args.log_to_stdout:
        logging.basicConfig(stream=sys.stdout, level=logging.DEBUG)
        logging.root.handlers[0].setFormatter(_CustomFormatter())
    else:
        file_name = f"quantize.{args.model_size}.{args.calibration_steps}.log"
        logging.basicConfig(filename=file_name, encoding="utf-8", level=logging.INFO)

    main(args)

r"""<PERSON><PERSON><PERSON> to quantize Starcoder to FP8.

Example usage (uses 1024 steps and smooth weights by default):

$ python quantize_starcoder.py \
  --checkpoint_path /mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-17-3.expand2-2.epoch3/neox/global_step4080/ \
  --calibration_data /mnt/efs/augment/user/igor/data/chatanol/chatanol1-17-3.expand2-2/quantization_data/data \
  --output_path /mnt/efs/augment/user/hieu/checkpoints/chatanol-test \
  --is_embedder
"""
# pyright rightfully complains about the megatron import
# pyright: reportMissingImports=false

from pathlib import Path
from typing import TypeVar

import torch
import torch.nn as nn

from base.fastforward import (
    cached_attention,
    fp8,
    fwd,
    fwd_utils,
    layers,
    quantize_utils,
    torch_utils,
)
from base.fastforward.checkpoints import save_load
from base.fastforward.fwd import ModelSpec
from base.fastforward.starcoder import fwd_starcoder, fwd_starcoder_fp8, model_specs

ModuleT = TypeVar("ModuleT", bound=nn.Module)


def smooth_model_weights(
    model: fwd_starcoder.StarCoder,
    input_amaxes: dict[str, torch.Tensor],
):
    """Smooth model weights in place to improve numerical stability for quantization.

    This follows the SmoothQuant[1] trick to smooth out input and weight activations.
    We only use this operation for linear layers preceded by layernorms, so that we can
    push the input scaling into the layernorm. This is also what the SmoothQuant authors
    appear to do.

    [1]: https://arxiv.org/pdf/2211.10438.pdf

    Args:
        model: the model to smooth.
        input_amaxes: a mapping from linear layer names to their input amaxes.
    """
    for name, module in model.named_modules():
        if isinstance(module, layers.QKV):
            if f"{name}.linear_qkv" not in input_amaxes:
                continue
            linear_imax = input_amaxes[f"{name}.linear_qkv"]
            layernorm_weight = module.input_layernorm.weight.data
            layernorm_bias = module.input_layernorm.bias.data
            linear_weight = module.linear_qkv.weight.data
            input_prescale = quantize_utils.compute_input_prescale(
                linear_weight, linear_imax
            )
            layernorm_weight.div_(input_prescale)
            layernorm_bias.div_(input_prescale)
            linear_weight.mul_(input_prescale)
        elif isinstance(module, layers.AttnOutMLP):
            if f"{name}.linear_hto4h" not in input_amaxes:
                continue
            linear_imax = input_amaxes[f"{name}.linear_hto4h"]
            layernorm_weight = module.post_attention_layernorm.weight.data
            layernorm_bias = module.post_attention_layernorm.bias.data
            linear_weight = module.linear_hto4h.weight.data
            input_prescale = quantize_utils.compute_input_prescale(
                linear_weight, linear_imax
            )
            layernorm_weight.div_(input_prescale)
            layernorm_bias.div_(input_prescale)
            linear_weight.mul_(input_prescale)


def _quantize_state_dict(
    fp16_model: fwd_starcoder.StarCoder,
    attn: cached_attention.BasicAttention,
    calibration_data: list[list[int]],
    smooth_quant: bool = False,
) -> dict[str, torch.Tensor]:
    """Quantize the model.

    Args:
        fp16_model: the FP16 model.
        attn: the attention object.
        calibration_data: the calibration data.
        model_spec: the model spec.

    Returns:
        the quantized model state dict.
    """
    calibration_data_kwargs = [{"tokens": x} for x in calibration_data]

    input_amaxes, output_amaxes, _ = quantize_utils.calibrate_input_scale_factors(
        layer_name="starcoder",
        layer=fp16_model,
        calibration_data=calibration_data_kwargs,
        reset_fn=attn.reset,
        excludes=("final_projection",),
        attn=attn,
    )
    if smooth_quant:
        smooth_model_weights(fp16_model, input_amaxes)
        # Re-run calibration to get the new amaxes.
        input_amaxes, output_amaxes, _ = quantize_utils.calibrate_input_scale_factors(
            layer_name="starcoder",
            layer=fp16_model,
            calibration_data=calibration_data_kwargs,
            reset_fn=attn.reset,
            excludes=("final_projection",),
            attn=attn,
        )

    preserve_input = False
    fp8_model_state_dict = fp16_model.state_dict()
    for name, imaxes in input_amaxes.items():
        omaxes = output_amaxes[name]
        fp8_model_state_dict = fp8.convert_linear_state_dict_to_fp8(
            state_dict=fp8_model_state_dict,
            input_amaxes=imaxes,
            output_amaxes=omaxes,
            prefix=name + ".",
            preserve_input=preserve_input,
        )
    return fp8_model_state_dict


def quantize_model(
    fp16_model: fwd_starcoder.StarCoder,
    attn: cached_attention.BasicAttention,
    calibration_data: list[list[int]],
    model_spec: ModelSpec,
    smooth_quant: bool = False,
) -> tuple[fwd_starcoder_fp8.StarCoder, fwd_starcoder_fp8.StarcoderAttentionFactory]:
    """Quantize the model.

    Args:
        fp16_model: the FP16 model.
        attn: the attention object.
        calibration_data: the calibration data.
        model_spec: the model spec.

    Returns:
        the quantized model.
    """
    fp8_model_state_dict = _quantize_state_dict(
        fp16_model=fp16_model,
        attn=attn,
        calibration_data=calibration_data,
        smooth_quant=smooth_quant,
    )
    fp8_model = torch_utils.init_with_weights(
        fwd_starcoder_fp8.StarCoder, fp8_model_state_dict, model_spec
    )
    fp8_attn_factory = fwd_starcoder_fp8.StarcoderAttentionFactory(model_spec)
    return fp8_model, fp8_attn_factory


def main():
    import argparse

    # using dynamic import to enable sharing this code mostly with production
    # code (there are build targets!) while still allowing us to run this script
    # locally with our data set loaders from research.
    from research.data.dataset.indexed_dataset import make_dataset

    parser = argparse.ArgumentParser(description="Quantize StarCoder")
    parser.add_argument("-cp", "--checkpoint_path", type=str, required=True)
    parser.add_argument(
        "--checkpoint_sha256",
        type=str,
        required=True,
    )
    parser.add_argument("-ms", "--model_size", type=str, default=None)
    parser.add_argument(
        "-cd",
        "--calibration_data",
        type=str,
        required=True,
    )
    parser.add_argument(
        "-n",
        "--calibration_steps",
        type=int,
        default=1024,
    )
    parser.add_argument(
        "--smooth_weights",
        type=bool,
        default=True,
    )
    parser.add_argument(
        "-op",
        "--output_path",
        type=Path,
        required=True,
        help="Where to save the quantized model",
    )
    parser.add_argument(
        "-emb",
        "--is_embedder",
        action="store_true",
        help="Whether we are quantizing an embedder model or a decoder model.",
    )
    parser.add_argument(
        "-cf",
        "--checkpoint_format",
        choices=["v1", "v2"],
        type=str,
        default="v2",
        help="Which checkpoint format to save. Now default to V2.",
    )
    args = parser.parse_args()

    output_type = (
        fwd.OutputTensorType.EMBEDDING
        if args.is_embedder
        else fwd.OutputTensorType.VOCAB_LOGITS
    )

    if args.model_size is not None:
        model_spec = model_specs.get_starcoder_model_spec(args.model_size)
        model_spec.checkpoint_path = args.checkpoint_path
    else:
        model_spec = fwd_utils.get_model_spec_from_neox_checkpoint(
            Path(args.checkpoint_path)
        )
    model_spec.checkpoint_sha256 = args.checkpoint_sha256
    max_seq_len = model_spec.max_position_embeddings
    fp16_model = fwd_starcoder.generate_step_fn(
        model_spec, output_type=output_type, auto_capture_graphs=False
    )
    attn = fwd_starcoder.StarcoderAttentionFactory(model_spec)(max_seq_len)
    assert isinstance(fp16_model, fwd_starcoder.StarCoder)

    dataset = make_dataset(args.calibration_data, "mmap", skip_warmup=True)
    assert dataset is not None
    calibration_data = [
        dataset.get(i).tolist()[:max_seq_len] for i in range(args.calibration_steps)
    ]

    fp8_model_state_dict = _quantize_state_dict(
        fp16_model=fp16_model,
        attn=attn,
        calibration_data=calibration_data,
        smooth_quant=args.smooth_weights,
    )

    if args.output_path is None:
        args.output_path = Path(model_spec.checkpoint_path).parent
    args.output_path.mkdir(parents=True, exist_ok=True)

    if args.checkpoint_format == "v1":
        # TODO(arun): Save the checkpoint in a "pipeline" format.
        with (args.output_path / "checkpoint_fp8.pth").open("wb") as f:
            torch.save(fp8_model_state_dict, f)
    else:
        # Write the FP8 checkpoint
        out_path = args.output_path.absolute()
        print(f"Saving quantized weights to {str(out_path)}", flush=True)
        out_path.mkdir(parents=True, exist_ok=True)
        save_load.save_weights(out_path, fp8_model_state_dict, incremental=True)


if __name__ == "__main__":
    main()

r"""Quantize LLAMA models layer-by-layer.

python quantize_llama_layer_by_layer.py \
    --calibration-steps 200 \
    --log-to-stdout \
    --model-size "deepseek-33b" \
    --out-path "/mnt/efs/augment/path/to/your/output/model" \
    --calibration-data "/mnt/efs/augment/path/to/your/dataset"

NOTE: calibration-data should be the *last* dataset that the BF16 model was trained on.
"""
# pyright rightfully complains about the megatron import
# pyright: reportMissingImports=false

import logging
import sys
from pathlib import Path
from typing import Optional, Sequence

import torch
import torch.nn as nn

from base.fastforward import (
    cached_attention,
    fp8,
    quantize_utils,
)
from base.fastforward.checkpoints import save_load
from base.fastforward.fp8 import FP8Linear
from base.fastforward.layers import (
    LlamaTransformerBlock,
    RmsNorm,
    WordEmbeddings,
)
from base.fastforward.layers_fp8 import (
    LlamaTransformerBlock as LlamaTransformerBlockFP8,
)
from base.fastforward.llama import fwd_llama, model_specs
from research.fastbackward.loss_masking import unmask_token_np

# We only support quantizing the model on a single GPU.
GPU_DEVICE = "cuda:0"


def _check_old_weights_are_contained_in_new_weights(
    old_weights: dict[str, torch.Tensor], new_weights: dict[str, torch.Tensor]
):
    """Check old weights are contained in the new weights."""
    old_weights_set = set(old_weights.keys())
    new_weights_set = set(new_weights.keys())
    if not old_weights_set.issubset(new_weights_set):
        raise ValueError(
            "Old weights are not contained in the new weights. "
            f"Old weights: {old_weights_set}, new weights: {new_weights_set}"
        )


def _strip_prefix(name: str, prefix: str) -> str:
    assert name.startswith(prefix), f"{name} does not start with {prefix}"
    return name[len(prefix) :]


def _get_llama_transformer_block_bfp16(
    ms: model_specs.LlamaModelSpec,
) -> nn.Module:
    return LlamaTransformerBlock(
        emb_dim=ms.emb_dim,
        num_heads_q=ms.num_heads_q,
        num_heads_kv=ms.num_heads_kv,
        head_dim=ms.head_dim,
        split_head_mode=ms.attn_split_head_mode,
        mlp_dim=ms.mlp_hidden_dim,
        norm_eps=ms.norm_eps,
        use_bias=False,
        dtype=torch.bfloat16,
        device=GPU_DEVICE,
    )


def _get_llama_transformer_block_fp8(
    ms: model_specs.LlamaModelSpec,
) -> nn.Module:
    return LlamaTransformerBlockFP8(
        emb_dim=ms.emb_dim,
        num_heads_q=ms.num_heads_q,
        num_heads_kv=ms.num_heads_kv,
        head_dim=ms.head_dim,
        split_head_mode=ms.attn_split_head_mode,
        mlp_dim=ms.mlp_hidden_dim,
        norm_eps=ms.norm_eps,
        use_bias=False,
        dtype=torch.bfloat16,
        device=GPU_DEVICE,
    )


def quantize_single_layer(
    layer_index: int | None,
    layer_name: str,
    layer_input: nn.Module,
    layer_output: Optional[nn.Module],
    checkpoint_path: Path,
    checkpoint_sha256: str,
    out_path: Path,
    calibration_data: Sequence[dict[str, torch.Tensor]],  # type: ignore
    attn: Optional[cached_attention.BasicAttention] = None,
    return_outputs: bool = True,
):
    layer_name_as_prefix = layer_name + "."
    logging.info(
        "Quantizing [%s], mem(gb)=%-6.2f",
        layer_name,
        quantize_utils.curr_mem(),
    )
    weights = save_load.load_weights(
        path=checkpoint_path,
        target_sha256=checkpoint_sha256,
        device=GPU_DEVICE,
        require_patterns=[layer_name + "\\..*"],
    )
    weights = {
        _strip_prefix(name, layer_name_as_prefix): v for name, v in weights.items()
    }

    layer_input.load_state_dict(weights, strict=True)

    quantize_layer_kwargs = dict(
        layer_name=layer_name,
        layer_inp=layer_input,
        layer_out=layer_output,
        calibration_data=calibration_data,
        return_outputs=return_outputs,
    )

    if attn is not None:
        quantize_layer_kwargs.update(
            register_tokens_get_positions=True,
            reset_fn=attn.reset,  # type: ignore
            attn=attn,  # type: ignore
        )

    weights_e4m3, outputs = quantize_utils.quantize_layer(**quantize_layer_kwargs)  # type: ignore

    if attn is not None:
        assert layer_index is not None
        layer_maxes = attn.captured_attn_maxes[layer_index]
        attn_qkv_scales = fp8.optimal_scaling_factor_for_amax(layer_maxes)
        weights_e4m3["attn.attn_qkv_scales"] = attn_qkv_scales

    _check_old_weights_are_contained_in_new_weights(
        old_weights=weights, new_weights=weights_e4m3
    )
    weights_e4m3 = {layer_name_as_prefix + k: v for k, v in weights_e4m3.items()}
    save_load.save_weights(path=out_path, weights=weights_e4m3, incremental=True)
    # unload weights
    del weights
    del weights_e4m3
    torch.cuda.empty_cache()
    return outputs


@torch.inference_mode()
def quantize_llama_layer_by_layer(
    model_name: str,
    checkpoint_path: Path,
    checkpoint_sha256: str,
    calibration_data: Sequence[dict[str, torch.Tensor]],
    out_path: Path,
):
    """Quantize the layers of LLAMA."""
    ms = model_specs.get_llama_model_spec(
        model_name=model_name,
        checkpoint_path=checkpoint_path,
        checkpoint_sha256=checkpoint_sha256,
    )
    max_seq_len = ms.max_position_embeddings
    attn = fwd_llama.LlamaAttentionFactory(
        ms, capture_attn_maxes_for_quantization=True, pre_attention_kernel_fusion=False
    )(max_seq_len + 1)

    bfp16_embs = WordEmbeddings(
        vocab_size=ms.vocab_size,
        emb_dim=ms.emb_dim,
        dtype=torch.bfloat16,
        device=GPU_DEVICE,
    )
    outputs = quantize_single_layer(
        layer_index=None,
        layer_name="embs",
        layer_input=bfp16_embs,
        # We don't quantize embeddings, so we pass None.
        layer_output=None,
        checkpoint_path=checkpoint_path,
        checkpoint_sha256=checkpoint_sha256,
        out_path=out_path,
        calibration_data=calibration_data,
    )
    del bfp16_embs
    torch.cuda.empty_cache()

    for layer_index in range(ms.num_layers):
        outputs = [
            {"inputs": output, "tokens": datum["tokens"], "layer_idx": layer_index}
            for output, datum in zip(outputs, calibration_data)
        ]
        bfp16_layer = _get_llama_transformer_block_bfp16(ms)
        fp8_layer = _get_llama_transformer_block_fp8(ms)
        outputs = quantize_single_layer(
            layer_index=layer_index,
            layer_name=f"layers.{layer_index}",
            layer_input=bfp16_layer,
            layer_output=fp8_layer,
            checkpoint_path=checkpoint_path,
            checkpoint_sha256=checkpoint_sha256,
            out_path=out_path,
            calibration_data=outputs,
            attn=attn,
        )
        del bfp16_layer
        del fp8_layer
        torch.cuda.empty_cache()

    final_rms_norm_bfp16 = RmsNorm(
        inp_dim=ms.emb_dim,
        eps=ms.norm_eps,
        dtype=torch.bfloat16,
        device=GPU_DEVICE,
    )
    outputs = quantize_single_layer(
        layer_index=None,
        layer_name="final_rms_norm",
        layer_input=final_rms_norm_bfp16,
        # NOTE: FP8RMSNorm requires additional information from the quantized score
        # layer. We don't have that information here, so we pass None.
        layer_output=None,
        checkpoint_path=checkpoint_path,
        checkpoint_sha256=checkpoint_sha256,
        out_path=out_path,
        calibration_data=[{"x": output} for output in outputs],
    )
    del final_rms_norm_bfp16
    torch.cuda.empty_cache()

    score_bfp16 = nn.Linear(
        in_features=ms.emb_dim,
        out_features=ms.vocab_size,
        bias=False,
        dtype=torch.bfloat16,
        device=GPU_DEVICE,
    )
    score_fp8 = FP8Linear(
        in_features=ms.emb_dim,
        out_features=ms.vocab_size,
        bias=False,
        dtype=torch.bfloat16,
        device=GPU_DEVICE,
    )
    _ = quantize_single_layer(
        layer_index=None,
        layer_name="score",
        layer_input=score_bfp16,
        layer_output=score_fp8,
        checkpoint_path=checkpoint_path,
        checkpoint_sha256=checkpoint_sha256,
        out_path=out_path,
        calibration_data=[{"input": output} for output in outputs],
        return_outputs=False,
    )
    del score_bfp16
    del score_fp8
    torch.cuda.empty_cache()


def main(args):
    # using dynamic import to enable sharing this code mostly with production
    # code (there are build targets!) while still allowing us to run this script
    # locally with our data set loaders from research.
    from research.data.dataset.indexed_dataset import make_dataset

    dataset = make_dataset(args.calibration_data, "mmap", skip_warmup=True)
    assert dataset is not None
    # TODO(Xuanyi): we masked out a lot of tokens during the fine-tuning, such as prompt or paddings.
    # I think they shouldn't be treated equally as the target tokens.
    # We need to evaluate the difference between whether to include them or not during the calibration.
    calibration_data = [
        unmask_token_np(dataset.get(i)) for i in range(args.calibration_steps)
    ]
    calibration_data_kwargs = [
        {"tokens": torch.tensor(x, dtype=torch.int32)} for x in calibration_data
    ]

    # Setup the directory for the FP8 checkpoint
    out_path = args.out_path.absolute()
    out_path.mkdir(parents=True, exist_ok=True)
    logging.info("Saving quantized weights to %s", str(out_path))

    quantize_llama_layer_by_layer(
        model_name=args.model_size,
        checkpoint_path=args.ckpt_path,
        checkpoint_sha256=args.ckpt_sha256,
        calibration_data=calibration_data_kwargs,
        out_path=out_path,
    )


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Quantize LLAMA")
    parser.add_argument("-m", "--model-size", type=str, required=True)
    parser.add_argument("-d", "--calibration-data", type=str, required=True)
    parser.add_argument("-n", "--calibration-steps", type=int, default=2048)

    parser.add_argument("--log-to-stdout", dest="log_to_stdout", action="store_true")
    parser.add_argument(
        "--no-log-to-stdout", dest="log_to_stdout", action="store_false"
    )
    parser.set_defaults(log_to_stdout=True)

    parser.add_argument("-p", "--ckpt-path", type=Path, default=Path())
    parser.add_argument("-s", "--ckpt-sha256", type=str, default="")
    parser.add_argument("-o", "--out-path", type=Path, default=Path())
    args = parser.parse_args()

    class _CustomFormatter(logging.Formatter):
        """Logging Formatter to add colors."""

        GREEN = "\x1b[32;20m"
        YELLOW = "\x1b[33;20m"
        RESET = "\x1b[0m"

        FORMATS = {
            logging.INFO: rf"{GREEN}%(levelname)s{RESET} [%(name)s] %(message)s",
            logging.DEBUG: rf"{YELLOW}%(levelname)s{RESET} [%(name)s] %(message)s",
        }

        def format(self, record):
            log_fmt = self.FORMATS.get(record.levelno, self._fmt)
            formatter = logging.Formatter(log_fmt, self.datefmt)
            return formatter.format(record)

    if args.log_to_stdout:
        logging.basicConfig(stream=sys.stdout, level=logging.DEBUG)
        logging.root.handlers[0].setFormatter(_CustomFormatter())
    else:
        file_name = f"quantize.{args.model_size}.{args.calibration_steps}.log"
        logging.basicConfig(filename=file_name, encoding="utf-8", level=logging.INFO)

    main(args)

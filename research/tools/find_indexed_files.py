"""Script to find indexed files for a request.

This is useful to determined which files are indexed for a given request.
TODO(vzhao): Makes the script support other requests. It only supports completion
requests for now.

Usage:
python research/tools/find_indexed_files.py <request_id> <file_path_regex_pattern>
"""

import base64
import json
import logging
import re
from concurrent.futures import ThreadPoolExecutor

import click
import tqdm
from google.cloud import bigquery, storage

from base.datasets import tenants

logging.basicConfig(level=logging.INFO)


TENANT_NAME = "dogfood"

_QUERY_COMPLETION = """SELECT
  request_id,
  event_type,
  raw_json
FROM
  `system-services-prod.staging_request_insight_full_export_dataset.request_event`
WHERE
  request_id = '{request_id}'
  AND event_type='completion_host_request'"""

_MAX_WORKERS = 10


def _fetch_filepath(
    tenant: tenants.DatasetTenant, bucket: storage.Bucket, blob_name: str
) -> tuple[str, str] | None:
    """Returns the path of the blob."""
    blob = bucket.get_blob(f"{tenant.blob_bucket_prefix}/{blob_name}")
    if blob is None:
        return None
    if blob.metadata is None:
        logging.warning("Found blob %s in GCS but it is missing metadata.", blob_name)
        return None
    return (blob.metadata["path"], blob_name)


def _base64_to_hash(encoded: str) -> str:
    return base64.b64decode(encoded).hex()


def _get_blob_names(
    tenant: tenants.DatasetTenant, checkpoint_bucket: storage.Bucket, data: dict
) -> list[str]:
    """Returns a list of blob names based on blob info."""
    checkpoints = None
    checkpoint_blob_names = set()
    if "baseline_checkpoint_id" in data:
        checkpoints = checkpoint_bucket.get_blob(
            f"{tenant.checkpoint_bucket_prefix}/{data['baseline_checkpoint_id']}"
        )
        if checkpoints is None:
            logging.warning(
                "The checkpoint %s is not found in GCS. Using an empty set.",
                data["baseline_checkpoint_id"],
            )
        else:
            blob_text = checkpoints.download_as_text()
            checkpoint_blob_names = set(json.loads(blob_text))
    added_blob_names = set()
    if "added" in data:
        added_blob_names = set(_base64_to_hash(name) for name in data["added"])
    deleted_blob_names = set()
    if "deleted" in data:
        deleted_blob_names = set(_base64_to_hash(name) for name in data["deleted"])
    return sorted(list((added_blob_names | checkpoint_blob_names) - deleted_blob_names))


@click.command()
@click.argument("request_id", required=True, type=click.Path())
@click.argument("pattern", required=True, type=click.Path())
def main(request_id: str, pattern: str):
    tenant = tenants.get_tenant(TENANT_NAME)
    bigquery_client = bigquery.Client(project=tenant.project_id)
    storage_client = storage.Client(project=tenant.project_id)
    checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)
    blob_bucket = storage_client.bucket(tenant.blob_bucket_name)

    # Get blobs for the request
    query = _QUERY_COMPLETION.format(request_id=request_id)
    rows = list(bigquery_client.query_and_wait(query))
    if not rows:
        logging.info("The completion request is not found: %s", request_id)
        return
    if len(rows) > 1:
        logging.info(
            "The completion request is not unique: %s. Use the first one.", request_id
        )

    blob_info = rows[0]["raw_json"]["blobs"]
    blob_names = _get_blob_names(tenant, checkpoint_bucket, blob_info)

    # get path
    matched = []
    logging.info("Fetching file paths...")
    with ThreadPoolExecutor(max_workers=_MAX_WORKERS) as executor:
        futures = [
            executor.submit(_fetch_filepath, tenant, blob_bucket, blob_name)
            for blob_name in blob_names
        ]
        for future in tqdm.tqdm(futures):
            rst = future.result()
            if rst:
                match = re.fullmatch(pattern, rst[0])
                if match:
                    matched.append(rst)
    if not matched:
        logging.info("No matched files found pattern: %s.", pattern)
        return
    for m in matched:
        print(m)


if __name__ == "__main__":
    main()

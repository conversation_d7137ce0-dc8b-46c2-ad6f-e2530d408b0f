from unittest.mock import MagicMock, patch

import pytest

from base.datasets.tenants import DatasetTenant
from research.core.chat_prompt_input import ResearchChatPromptInput
from research.core.types import Document
from research.tools.chat_replay.replay_utils import BlobGetter, MultipleEventsFound
from services.chat_host import chat_pb2
from services.request_insight import request_insight_pb2


@pytest.fixture
def mock_tenant():
    tenant = MagicMock(spec=DatasetTenant)
    tenant.project_id = "test-project"
    tenant.blob_bucket_name = "test-blob-bucket"
    tenant.blob_bucket_prefix = "test-blob-prefix"
    tenant.checkpoint_bucket_name = "test-checkpoint-bucket"
    tenant.checkpoint_bucket_prefix = "test-checkpoint-prefix"
    return tenant


@pytest.fixture
def mock_blob_cache():
    return MagicMock()


@pytest.fixture
def mock_checkpoint_cache():
    return MagicMock()


@pytest.fixture
def mock_request_fetcher():
    return MagicMock()


@pytest.fixture
def blob_getter(
    mock_tenant, mock_blob_cache, mock_checkpoint_cache, mock_request_fetcher
):
    with patch("research.tools.chat_replay.replay_utils.get_caches") as mock_get_caches:
        mock_get_caches.return_value = (mock_blob_cache, mock_checkpoint_cache)
        with patch(
            "research.tools.chat_replay.replay_utils.GCSRequestInsightFetcher"
        ) as mock_fetcher_class:
            mock_fetcher_class.from_tenant.return_value = mock_request_fetcher
            return BlobGetter(mock_tenant)


def test_init(
    blob_getter,
    mock_tenant,
    mock_blob_cache,
    mock_checkpoint_cache,
    mock_request_fetcher,
):
    """Test that BlobGetter initializes correctly."""
    assert blob_getter.tenant == mock_tenant
    assert blob_getter.blob_cache == mock_blob_cache
    assert blob_getter.checkpoint_cache == mock_checkpoint_cache
    assert blob_getter.request_fetcher == mock_request_fetcher


def test_get_request_event_single_event(blob_getter, mock_request_fetcher):
    """Test get_request_event when there's a single event of the requested type."""
    # Setup
    request_id = "test-request-id"
    event_type = "chat_host_request"

    mock_event = MagicMock()
    mock_chat_request = MagicMock(spec=request_insight_pb2.RIChatRequest)
    setattr(mock_event, event_type, mock_chat_request)
    mock_event.HasField.return_value = True

    mock_request_info = MagicMock()
    mock_request_info.events = [mock_event]
    mock_request_fetcher.get_request.return_value = mock_request_info

    # Execute
    result = blob_getter.get_request_event(request_id, event_type)

    # Verify
    mock_request_fetcher.get_request.assert_called_once_with(request_id=request_id)
    assert result == mock_chat_request


def test_get_request_event_no_events(blob_getter, mock_request_fetcher):
    """Test get_request_event when there are no events of the requested type."""
    # Setup
    request_id = "test-request-id"
    event_type = "chat_host_request"

    mock_event = MagicMock()
    mock_event.HasField.return_value = False

    mock_request_info = MagicMock()
    mock_request_info.events = [mock_event]
    mock_request_fetcher.get_request.return_value = mock_request_info

    # Execute
    result = blob_getter.get_request_event(request_id, event_type)

    # Verify
    mock_request_fetcher.get_request.assert_called_once_with(request_id=request_id)
    assert result is None


def test_get_request_event_multiple_events(blob_getter, mock_request_fetcher):
    """Test get_request_event when there are multiple events of the requested type."""
    # Setup
    request_id = "test-request-id"
    event_type = "chat_host_request"

    mock_event1 = MagicMock()
    mock_chat_request1 = MagicMock(spec=request_insight_pb2.RIChatRequest)
    setattr(mock_event1, event_type, mock_chat_request1)
    mock_event1.HasField.return_value = True

    mock_event2 = MagicMock()
    mock_chat_request2 = MagicMock(spec=request_insight_pb2.RIChatRequest)
    setattr(mock_event2, event_type, mock_chat_request2)
    mock_event2.HasField.return_value = True

    mock_request_info = MagicMock()
    mock_request_info.events = [mock_event1, mock_event2]
    mock_request_fetcher.get_request.return_value = mock_request_info

    # Execute and Verify
    with pytest.raises(
        MultipleEventsFound, match=f"Multiple {event_type} events for {request_id}"
    ):
        blob_getter.get_request_event(request_id, event_type)

    mock_request_fetcher.get_request.assert_called_once_with(request_id=request_id)


def test_get_raw_chat_request(blob_getter):
    """Test get_raw_chat_request method."""
    # Setup
    request_id = "test-request-id"
    mock_chat_request = MagicMock(spec=request_insight_pb2.RIChatRequest)

    with patch.object(
        blob_getter, "get_request_event", return_value=mock_chat_request
    ) as mock_get_request_event:
        # Execute
        result = blob_getter.get_raw_chat_request(request_id)

        # Verify
        mock_get_request_event.assert_called_once_with(
            request_id, "chat_host_request", True
        )
        assert result == mock_chat_request


def test_get_raw_chat_request_none(blob_getter):
    """Test get_raw_chat_request method when no event is found."""
    # Setup
    request_id = "test-request-id"

    with patch.object(
        blob_getter, "get_request_event", return_value=None
    ) as mock_get_request_event:
        # Execute
        result = blob_getter.get_raw_chat_request(request_id)

        # Verify
        mock_get_request_event.assert_called_once_with(
            request_id, "chat_host_request", True
        )
        assert result is None


def test_get_blob_names_from_chat_request_empty(blob_getter):
    """Test get_blob_names_from_chat_request with empty blobs."""
    # Setup
    mock_request = MagicMock(spec=chat_pb2.ChatRequest)
    mock_request.blobs = []

    # Execute
    result = blob_getter.get_blob_names_from_chat_request(mock_request)

    # Verify
    assert result == []


def test_get_blob_names_from_chat_request_with_checkpoint(
    blob_getter, mock_checkpoint_cache
):
    """Test get_blob_names_from_chat_request with checkpoint."""
    # Setup
    mock_request = MagicMock(spec=chat_pb2.ChatRequest)
    mock_blob_object = MagicMock()
    mock_blob_object.baseline_checkpoint_id = "test-checkpoint-id"

    # Mock added and deleted blobs
    mock_added_blob = MagicMock()
    mock_added_blob.hex.return_value = "added-blob-1"
    mock_deleted_blob = MagicMock()
    mock_deleted_blob.hex.return_value = "deleted-blob-1"

    mock_blob_object.added = [mock_added_blob]
    mock_blob_object.deleted = [mock_deleted_blob]

    mock_request.blobs = [mock_blob_object]

    # Mock checkpoint content
    mock_checkpoint_content = MagicMock()
    mock_checkpoint_content.blob_names = ["checkpoint-blob-1", "deleted-blob-1"]
    mock_checkpoint_cache.get.return_value = [mock_checkpoint_content]

    # Execute
    result = blob_getter.get_blob_names_from_chat_request(mock_request)

    # Verify
    mock_checkpoint_cache.get.assert_called_once_with(["test-checkpoint-id"])
    # Should include added blob and checkpoint blob, but not deleted blob
    assert set(result) == {"added-blob-1", "checkpoint-blob-1"}


def test_get_blob_names_from_chat_request_no_checkpoint(blob_getter):
    """Test get_blob_names_from_chat_request without checkpoint."""
    # Setup
    mock_request = MagicMock(spec=chat_pb2.ChatRequest)
    mock_blob_object = MagicMock()
    mock_blob_object.baseline_checkpoint_id = ""  # No checkpoint

    # Mock added and deleted blobs
    mock_added_blob = MagicMock()
    mock_added_blob.hex.return_value = "added-blob-1"
    mock_deleted_blob = MagicMock()
    mock_deleted_blob.hex.return_value = "deleted-blob-1"

    mock_blob_object.added = [mock_added_blob]
    mock_blob_object.deleted = [mock_deleted_blob]

    mock_request.blobs = [mock_blob_object]

    # Execute
    result = blob_getter.get_blob_names_from_chat_request(mock_request)

    # Verify
    # Should include only added blob
    assert result == ["added-blob-1"]


def test_get_blobs(blob_getter, mock_blob_cache):
    """Test get_blobs method."""
    # Setup
    blob_names = ["blob-1", "blob-2"]

    mock_blob1 = MagicMock()
    mock_blob1.content = "content-1"
    mock_blob1.path = "path-1"

    mock_blob2 = MagicMock()
    mock_blob2.content = "content-2"
    mock_blob2.path = "path-2"

    mock_blob_cache.get.return_value = [mock_blob1, mock_blob2]

    # Execute
    result = blob_getter.get_blobs(blob_names)

    # Verify
    mock_blob_cache.get.assert_called_once_with(blob_names)
    assert len(result) == 2
    assert isinstance(result[0], Document)
    assert result[0].id == "blob-1"
    assert result[0].text == "content-1"
    assert result[0].path == "path-1"
    assert result[1].id == "blob-2"
    assert result[1].text == "content-2"
    assert result[1].path == "path-2"


def test_get_blobs_with_none(blob_getter, mock_blob_cache):
    """Test get_blobs method with None in results."""
    # Setup
    blob_names = ["blob-1", "blob-2"]

    mock_blob1 = MagicMock()
    mock_blob1.content = "content-1"
    mock_blob1.path = "path-1"

    # Second blob is None (not found)
    mock_blob_cache.get.return_value = [mock_blob1, None]

    # Execute
    result = blob_getter.get_blobs(blob_names)

    # Verify
    mock_blob_cache.get.assert_called_once_with(blob_names)
    assert len(result) == 1  # Only one valid blob
    assert isinstance(result[0], Document)
    assert result[0].id == "blob-1"
    assert result[0].text == "content-1"
    assert result[0].path == "path-1"


def test_get_chat_request(blob_getter):
    """Test get_chat_request method."""
    # Setup
    request_id = "test-request-id"

    # Mock raw request
    mock_raw_request = MagicMock(spec=chat_pb2.ChatRequest)
    mock_raw_request.message = "test message"
    mock_raw_request.path = "test/path.py"
    mock_raw_request.prefix = "prefix"
    mock_raw_request.selected_code = "selected code"
    mock_raw_request.suffix = "suffix"
    mock_raw_request.chat_history = []
    mock_raw_request.nodes = []

    # Create position mock
    mock_position = MagicMock()
    mock_position.prefix_begin = 0
    mock_position.suffix_end = 100
    mock_raw_request.position = mock_position

    mock_raw_request.retrieved_chunks = []
    mock_raw_request.context_code_exchange_request_id = "context-id"
    mock_raw_request.user_guided_blobs = []

    # Add new fields
    mock_changed_file_stats = MagicMock(spec=chat_pb2.ChangedFileStats)

    # Set up added_file_stats with integer changed_file_count
    mock_added_file_stats = MagicMock()
    mock_added_file_stats.changed_file_count = 0
    mock_added_file_stats.per_file_change_stats_head = []
    mock_added_file_stats.per_file_change_stats_tail = []
    mock_changed_file_stats.added_file_stats = mock_added_file_stats

    # Set up broken_file_stats with integer changed_file_count
    mock_broken_file_stats = MagicMock()
    mock_broken_file_stats.changed_file_count = 0
    mock_broken_file_stats.per_file_change_stats_head = []
    mock_broken_file_stats.per_file_change_stats_tail = []
    mock_changed_file_stats.broken_file_stats = mock_broken_file_stats

    # Set up copied_file_stats with integer changed_file_count
    mock_copied_file_stats = MagicMock()
    mock_copied_file_stats.changed_file_count = 0
    mock_copied_file_stats.per_file_change_stats_head = []
    mock_copied_file_stats.per_file_change_stats_tail = []
    mock_changed_file_stats.copied_file_stats = mock_copied_file_stats

    # Set up deleted_file_stats with integer changed_file_count
    mock_deleted_file_stats = MagicMock()
    mock_deleted_file_stats.changed_file_count = 0
    mock_deleted_file_stats.per_file_change_stats_head = []
    mock_deleted_file_stats.per_file_change_stats_tail = []
    mock_changed_file_stats.deleted_file_stats = mock_deleted_file_stats

    # Set up modified_file_stats with integer changed_file_count
    mock_modified_file_stats = MagicMock()
    mock_modified_file_stats.changed_file_count = 0
    mock_modified_file_stats.per_file_change_stats_head = []
    mock_modified_file_stats.per_file_change_stats_tail = []
    mock_changed_file_stats.modified_file_stats = mock_modified_file_stats

    # Set up renamed_file_stats with integer changed_file_count
    mock_renamed_file_stats = MagicMock()
    mock_renamed_file_stats.changed_file_count = 0
    mock_renamed_file_stats.per_file_change_stats_head = []
    mock_renamed_file_stats.per_file_change_stats_tail = []
    mock_changed_file_stats.renamed_file_stats = mock_renamed_file_stats

    # Set up unmerged_file_stats with integer changed_file_count
    mock_unmerged_file_stats = MagicMock()
    mock_unmerged_file_stats.changed_file_count = 0
    mock_unmerged_file_stats.per_file_change_stats_head = []
    mock_unmerged_file_stats.per_file_change_stats_tail = []
    mock_changed_file_stats.unmerged_file_stats = mock_unmerged_file_stats

    # Set up unknown_file_stats with integer changed_file_count
    mock_unknown_file_stats = MagicMock()
    mock_unknown_file_stats.changed_file_count = 0
    mock_unknown_file_stats.per_file_change_stats_head = []
    mock_unknown_file_stats.per_file_change_stats_tail = []
    mock_changed_file_stats.unknown_file_stats = mock_unknown_file_stats

    mock_raw_request.changed_file_stats = mock_changed_file_stats

    mock_raw_request.diff = "test diff"
    mock_raw_request.relevant_commit_messages = ["commit message 1", "commit message 2"]
    mock_raw_request.example_commit_messages = ["example commit 1"]
    mock_raw_request.workspace_guidelines = "workspace guidelines"
    mock_raw_request.user_guidelines = "user guidelines"
    mock_raw_request.agent_memories = "agent memories"
    mock_raw_request.persona_type = 0

    # Add tool definitions
    mock_tool = MagicMock()
    mock_tool.name = "test_tool"
    mock_tool.description = "test tool description"
    mock_tool.input_schema_json = "{}"
    mock_raw_request.tool_definitions = [mock_tool]

    # Add external source IDs
    mock_raw_request.external_source_ids = ["source-1", "source-2"]

    # Mock raw event
    mock_raw_event = MagicMock(spec=request_insight_pb2.RIChatRequest)
    mock_raw_event.request = mock_raw_request

    # Mock methods
    with patch.object(
        blob_getter, "get_raw_chat_request", return_value=mock_raw_event
    ) as mock_get_raw_chat_request:
        with patch.object(
            blob_getter, "get_blob_names_from_chat_request", return_value=["blob-1"]
        ) as mock_get_blob_names:
            with patch(
                "research.tools.chat_replay.replay_utils.build_retrievals",
                return_value=[],
            ) as mock_build_retrievals:
                with patch(
                    "research.tools.chat_replay.replay_utils.convert_history",
                    return_value=[],
                ) as mock_convert_history:
                    # Execute
                    result = blob_getter.get_chat_request(request_id)

                    # Verify
                    mock_get_raw_chat_request.assert_called_once_with(request_id, True)
                    mock_get_blob_names.assert_called_once_with(mock_raw_request)
                    mock_build_retrievals.assert_called_once_with(mock_raw_event)
                    mock_convert_history.assert_called_once_with(
                        mock_raw_request.chat_history
                    )

                    assert isinstance(result, ResearchChatPromptInput)
                    assert result.message == "test message"
                    assert result.path == "test/path.py"
                    assert result.prefix == "prefix"
                    assert result.selected_code == "selected code"
                    assert result.suffix == "suffix"
                    assert result.chat_history == []
                    assert result.prefix_begin == 0
                    assert result.suffix_end == 100
                    assert result.retrieved_chunks == []
                    assert result.context_code_exchange_request_id == "context-id"
                    assert result.user_guided_blobs == []
                    assert result.doc_ids == ["blob-1"]

                    # Verify new fields
                    assert (
                        result.changed_file_stats is not None
                    )  # Now it's a converted object, not a list
                    assert result.diff == "test diff"
                    assert list(result.relevant_commit_messages) == [
                        "commit message 1",
                        "commit message 2",
                    ]
                    assert list(result.example_commit_messages) == ["example commit 1"]
                    assert result.workspace_guidelines == "workspace guidelines"
                    assert result.user_guidelines == "user guidelines"
                    assert result.memories == "agent memories"
                    assert len(result.tool_definitions) == 1
                    assert result.tool_definitions[0].name == "test_tool"
                    assert (
                        result.tool_definitions[0].description
                        == "test tool description"
                    )
                    assert result.external_source_ids == ["source-1", "source-2"]


def test_get_chat_request_none(blob_getter):
    """Test get_chat_request method when raw request is None."""
    # Setup
    request_id = "test-request-id"

    # Mock methods
    with patch.object(
        blob_getter, "get_raw_chat_request", return_value=None
    ) as mock_get_raw_chat_request:
        # Execute
        result = blob_getter.get_chat_request(request_id)

        # Verify
        mock_get_raw_chat_request.assert_called_once_with(request_id, True)
        assert result is None

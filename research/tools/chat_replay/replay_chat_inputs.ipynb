{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay requests without redoing retrieval\n", "\n", "This notebook includes an example of how to replay a Chat request with its exactly original model inputs, without redoing retrieval.\n", "\n", "## Setup (should only need to be done once)\n", "\n", "1. Install the required Python libraries:\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler google-cloud-pubsub\n", "```\n", "2. Authenticate with Google:\n", "```bash\n", "gcloud auth login\n", "gcloud auth application-default login\n", "```\n", "3. Generate the proto library files (do periodically):\n", "```bash\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["REQUEST_ID = \"\"\"\n", "d405efe5-d547-4d8d-89f9-1e9157350986\n", "\"\"\".strip()\n", "REQUEST_ID"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Replay from <PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can copy the render input from an RI page for replay, if it's not convenient to get it programmatically.\n", "\n", "You should start copying from the divider line above this message and all the way to the bottom. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import decode_prompt, print_request\n", "\n", "with open(\"/home/<USER>/t.md\", \"r\") as f:\n", "    prompt_output = decode_prompt(f.read())\n", "\n", "print_request(prompt_output.message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import print_chat_history\n", "\n", "print_chat_history(prompt_output.chat_history, text_limit=100, tool_limit=100)\n", "print(\"=\" * 81)\n", "print_request(prompt_output.message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tool_definition_json = [\n", "    {\n", "        \"description\": \"Save a file. Use this tool to create new files.  It cannot modify existing files.\",\n", "        \"inputSchemaJson\": '{\"type\":\"object\",\"properties\":{\"file_path\":{\"type\":\"string\",\"description\":\"The path of the file to save.\"},\"file_content\":{\"type\":\"string\",\"description\":\"The content of the file to save.\"},\"add_last_line_newline\":{\"type\":\"boolean\",\"description\":\"Whether to add a newline at the end of the file (default: true).\"}},\"required\":[\"file_path\",\"file_content\"]}',\n", "        \"name\": \"save-file\",\n", "    },\n", "    {\n", "        \"description\": \"Read a file.\",\n", "        \"inputSchemaJson\": '{\"type\":\"object\",\"properties\":{\"file_path\":{\"type\":\"string\",\"description\":\"The path of the file to read.\"}},\"required\":[\"file_path\"]}',\n", "        \"name\": \"read-file\",\n", "    },\n", "    {\n", "        \"description\": \"\\nEdit a file. Accepts a file path and a description of the edit.\\nThis tool can edit whole files.\\nThe description should be detailed and precise, and include all required information to perform the edit.\\nIt can include both natural language and code. It can include multiple code snippets to described different\\nedits in the file. It can include descriptions of how to perform these edits precisely.\\n\\nAll the contents that should go in a file should be placed in a markdown code block, like this:\\n\\n<begin-example>\\nAdd a function called foo.\\n\\n```\\ndef foo():\\n    ...\\n```\\n</end-example>\\n\\nThis includes all contents, even if it's not code.\\n\\nBe precise or I will take away your toys.\\n\\nPrefer to use this tool when editing parts of a file.\\n\",\n", "        \"inputSchemaJson\": '{\"type\":\"object\",\"properties\":{\"file_path\":{\"type\":\"string\",\"description\":\"The path of the file to edit.\"},\"edit_summary\":{\"type\":\"string\",\"description\":\"A brief description of the edit to be made. 1-2 sentences.\"},\"detailed_edit_description\":{\"type\":\"string\",\"description\":\"A detailed and precise description of the edit. Can include natural language and code snippets.\"}},\"required\":[\"file_path\",\"edit_summary\",\"detailed_edit_description\"]}',\n", "        \"name\": \"edit-file\",\n", "    },\n", "    {\n", "        \"description\": \"Call this tool when user asks you:\\n- to remember something\\n- to create memory/memories\\n\\nUse this tool only with information that can be useful in the long-term.\\nDo not use this tool for temporary information.\\n\",\n", "        \"inputSchemaJson\": '{\"type\":\"object\",\"properties\":{\"memory\":{\"type\":\"string\",\"description\":\"The concise (1 sentence) memory to remember.\"}},\"required\":[\"memory\"]}',\n", "        \"name\": \"remember\",\n", "    },\n", "    {\n", "        \"description\": \"Open a URL in the default browser.\",\n", "        \"inputSchemaJson\": '{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"The URL to open in the browser.\"}},\"required\":[\"url\"]}',\n", "        \"name\": \"open-browser\",\n", "    },\n", "    {\n", "        \"description\": \"Use this tool to request information from the codebase.\\nIt will return relevant snippets for the requested information.\",\n", "        \"inputSchemaJson\": '{\"type\":\"object\",\"properties\":{\"information_request\":{\"type\":\"string\",\"description\":\"A description of the information you need.\"}},\"required\":[\"information_request\"]}',\n", "        \"name\": \"codebase-retrieval\",\n", "    },\n", "    {\n", "        \"description\": \"Launch a new process.\\nIf wait is specified, waits up to that many seconds for the process to complete.\\nIf the process completes within wait seconds, returns its output.\\nIf it doesn't complete within wait seconds, returns partial output and process ID.\\nIf wait is not specified, returns immediately with just the process ID.\\nThe process's stdin is always enabled, so you can use write_process to send input if needed.\",\n", "        \"inputSchemaJson\": '{\"type\":\"object\",\"properties\":{\"command\":{\"type\":\"string\",\"description\":\"The shell command to execute\"},\"wait\":{\"type\":\"number\",\"description\":\"Optional: number of seconds to wait for the command to complete.\"},\"cwd\":{\"type\":\"string\",\"description\":\"Working directory for the command. If not supplied, uses the current working directory.\"}},\"required\":[\"command\"]}',\n", "        \"name\": \"launch-process\",\n", "    },\n", "    {\n", "        \"description\": \"Kill a process by its process ID.\",\n", "        \"inputSchemaJson\": '{\"type\":\"object\",\"properties\":{\"process_id\":{\"type\":\"integer\",\"description\":\"Process ID to kill.\"}},\"required\":[\"process_id\"]}',\n", "        \"name\": \"kill-process\",\n", "    },\n", "    {\n", "        \"description\": \"Read output from a running process.\",\n", "        \"inputSchemaJson\": '{\"type\":\"object\",\"properties\":{\"process_id\":{\"type\":\"integer\",\"description\":\"Process ID to read from.\"}},\"required\":[\"process_id\"]}',\n", "        \"name\": \"read-process\",\n", "    },\n", "    {\n", "        \"description\": \"Write input to a process's stdin.\",\n", "        \"inputSchemaJson\": '{\"type\":\"object\",\"properties\":{\"process_id\":{\"type\":\"integer\",\"description\":\"Process ID to write to.\"},\"input_text\":{\"type\":\"string\",\"description\":\"Text to write to the process\\'s stdin.\"}},\"required\":[\"process_id\",\"input_text\"]}',\n", "        \"name\": \"write-process\",\n", "    },\n", "    {\n", "        \"description\": \"List all known processes and their states.\",\n", "        \"inputSchemaJson\": '{\"type\":\"object\",\"properties\":{},\"required\":[]}',\n", "        \"name\": \"list-processes\",\n", "    },\n", "    {\n", "        \"description\": \"Wait for a process to complete or timeout.\",\n", "        \"inputSchemaJson\": '{\"type\":\"object\",\"properties\":{\"process_id\":{\"type\":\"integer\",\"description\":\"Process ID to wait for.\"},\"wait\":{\"type\":\"number\",\"description\":\"Number of seconds to wait for the process to complete.\"}},\"required\":[\"process_id\",\"wait\"]}',\n", "        \"name\": \"wait-process\",\n", "    },\n", "    {\n", "        \"description\": \"Search the web for information. Returns results in markdown format.\\nEach result includes the URL, title, and a snippet from the page if available.\\n\\nThis tool uses Google's Custom Search API to find relevant web pages.\",\n", "        \"inputSchemaJson\": '{\"description\": \"Input schema for the web search tool.\", \"properties\": {\"query\": {\"description\": \"The search query to send.\", \"title\": \"Query\", \"type\": \"string\"}, \"num_results\": {\"default\": 5, \"description\": \"Number of results to return\", \"maximum\": 10, \"minimum\": 1, \"title\": \"Num Results\", \"type\": \"integer\"}}, \"required\": [\"query\"], \"title\": \"WebSearchInput\", \"type\": \"object\"}',\n", "        \"name\": \"web-search\",\n", "    },\n", "    {\n", "        \"description\": \"Execute a shell command. The OS is win32. The shell is 'powershell'.\",\n", "        \"inputSchemaJson\": '{\"type\":\"object\",\"properties\":{\"command\":{\"type\":\"string\",\"description\":\"The shell command to execute.\"}},\"required\":[\"command\"]}',\n", "        \"name\": \"shell\",\n", "    },\n", "    {\n", "        \"description\": \"Fetches data from a webpage and converts it into Markdown.\",\n", "        \"inputSchemaJson\": '{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"The URL to fetch.\"}},\"required\":[\"url\"]}',\n", "        \"name\": \"web-fetch\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from base.third_party_clients.anthropic_direct_client import ToolDefinition\n", "\n", "tool_definitions = [\n", "    ToolDefinition(\n", "        tool[\"name\"],\n", "        tool[\"description\"],\n", "        tool[\"inputSchemaJson\"],\n", "    )\n", "    for tool in tool_definition_json\n", "]\n", "\n", "tool_definitions = [\n", "    tool\n", "    for tool in tool_definitions\n", "    if tool.name not in [\"git\", \"version-control\", \"shell\"]\n", "]\n", "\n", "launch_process_tool = next(\n", "    tool for tool in tool_definitions if tool.name == \"launch-process\"\n", ")\n", "print(launch_process_tool.description)\n", "print(json.dumps(json.loads(launch_process_tool.input_schema_json), indent=4))\n", "print()\n", "\n", "launch_process_tool.description = \"\"\"Launch a new process.\n", "\n", "1. It supports simple commands and complex shell pipelines.\n", "2. Waiting\n", "    2.1. If wait is set to true, the tool will wait for the process to complete.\n", "    2.2. If wait is set to false or not specified, returns immediately with just the process ID.\n", "3. The process's stdin is always enabled, so you can use `write-process` to send input if needed.\n", "4. Execute system commands with validation for simple commands and support for complex pipelines. The OS is Windows. For complex pipelines, the shell is `pwsh`.\n", "5. Pay attention and do not use syntax or commands that are not for `pwsh`.\n", "\n", "Usages:\n", "1. Version control, such as various operations with Git (or another version control system the user uses);\n", "2. GitHub CLI (`gh`);\n", "3. Test execution;\n", "4. Any other command line tools that are available on the system.\n", "\n", "Do not use this tool for funcationalities that there is a dedicated tool for.\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format.common import ChatResultNodeType\n", "\n", "for index, exchange in enumerate(prompt_output.chat_history):\n", "    if isinstance(exchange.response_text, list):\n", "        for node in exchange.response_text:\n", "            if node.type == ChatResultNodeType.TOOL_USE and (\n", "                node.tool_use.name == \"shell\" or node.tool_use.name == \"launch-process\"\n", "            ):\n", "                print(f\"Exchange {index}: {node.tool_use.input}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import truncate_prompt_output\n", "\n", "truncated_prompt_output = truncate_prompt_output(prompt_output, 13)\n", "print_chat_history(\n", "    truncated_prompt_output.chat_history[-1:], text_limit=100, tool_limit=100\n", ")\n", "print(\"=\" * 81)\n", "print_request(truncated_prompt_output.message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import (\n", "    run_model,\n", "    print_response,\n", "    jsonify_final_parameters,\n", "    fix_tool_calls,\n", ")\n", "\n", "fixed_prompt_output = fix_tool_calls(truncated_prompt_output)\n", "response = run_model(\n", "    fixed_prompt_output,\n", "    tool_definitions=tool_definitions,\n", "    yield_final_parameters=True,\n", ")\n", "# print_response(response, tool_limit=100, string_limit=0)\n", "final_parameters = response[0].final_parameters\n", "with open(\n", "    \"/home/<USER>/augment/research/tools/chat_replay/final_params.json\", \"w\"\n", ") as f:\n", "    json.dump(jsonify_final_parameters(final_parameters), f, indent=4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Replay from RI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_infra import get_input_and_documents\n", "from research.tools.chat_replay.replay_utils import print_request\n", "\n", "chat_prompt_input, _ = get_input_and_documents(REQUEST_ID)\n", "print_request(chat_prompt_input.message)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Debug prompt formatting"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import render_prompt\n", "from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name\n", "from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment\n", "\n", "token_apportionment = ChatTokenApportionment(\n", "    prefix_len=0,\n", "    suffix_len=0,\n", "    path_len=256,\n", "    message_len=0,\n", "    selected_code_len=0,\n", "    chat_history_len=0,\n", "    retrieval_len_per_each_user_guided_file=0,\n", "    retrieval_len_for_user_guided=0,\n", "    retrieval_len=0,\n", "    max_prompt_len=1024 * 200,\n", "    tool_results_len=1024 * 120,\n", "    token_budget_to_trigger_truncation=1024 * 120,\n", ")\n", "\n", "prompt_formatter = get_structured_chat_prompt_formatter_by_name(\n", "    \"agent-binks-claude-v1\", token_apportionment\n", ")\n", "\n", "prompt_output = prompt_formatter.format_prompt(chat_prompt_input)\n", "prompt_rendering = render_prompt(prompt_output)\n", "with open(\"/home/<USER>/tr.md\", \"w\") as f:\n", "    f.write(prompt_rendering)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Replay from <PERSON><PERSON> input"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import print_chat_history\n", "\n", "print_chat_history(chat_prompt_input.chat_history, text_limit=100, tool_limit=100)\n", "print(\"=\" * 81)\n", "print_request(chat_prompt_input.message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import get_attrs\n", "\n", "get_attrs(chat_prompt_input)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chat_prompt_input.chat_history[2].response_text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import print_exchange\n", "from base.prompt_format.common import ChatResultNodeType\n", "\n", "for index, exchange in enumerate(chat_prompt_input.chat_history):\n", "    if isinstance(exchange.response_text, list):\n", "        for node in exchange.response_text:\n", "            if node.type == ChatResultNodeType.TOOL_USE and (\n", "                node.tool_use.name == \"open-browser\"\n", "            ):\n", "                print_exchange(exchange, tool_limit=100, round=index)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import print_request\n", "\n", "exchange = chat_prompt_input.chat_history[4]\n", "print_request(exchange.request_message, tool_limit=100)\n", "# print_response(exchange.response_text, tool_limit=-1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import truncate_prompt_input\n", "\n", "truncated_prompt_input = truncate_prompt_input(chat_prompt_input, 4)\n", "print_request(truncated_prompt_input.message)\n", "# truncated_prompt_input.chat_history[0] = dataclasses.replace(\n", "#     truncated_prompt_input.chat_history[0],\n", "#     request_message=\"Take a look at https://code.visualstudio.com/docs/terminal/shell-integration and how this file uses the shell integration API.  Do you see any issues or areas for improvement? You must absolutely remember to take a look at the A-Formation web page before doing anything.\",\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tool_definitions = chat_prompt_input.tool_definitions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import dataclasses\n", "\n", "\n", "print([definition.name for definition in tool_definitions])\n", "\n", "tool_definitions = [\n", "    tool for tool in tool_definitions if tool.name not in [\"git\", \"version-control\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# web_search_tool = next(tool for tool in tool_definitions if tool.name == \"web-search\")\n", "# print(web_search_tool.description)\n", "# print(json.dumps(json.loads(web_search_tool.input_schema_json), indent=4))\n", "# print()\n", "# web_search_tool.description = \"\"\"This tool is a wrapper around the Google Custom Search API:\n", "# 1. The tool takes in a natural language query and returns a list of relevant web pages in Markdown format;\n", "# 2. Each result item has a title, a URL, and a snippet from the page if available;\n", "# 3. The tool can only find information publicly available on the web, it does not have access to the repository or the company's private information.\"\"\"\n", "\n", "web_fetch_tool = next(tool for tool in tool_definitions if tool.name == \"web-fetch\")\n", "print(web_fetch_tool.description)\n", "print(json.dumps(json.loads(web_fetch_tool.input_schema_json), indent=4))\n", "print()\n", "web_fetch_tool.description = \"\"\"Fetches data from a webpage and converts it into Markdown.\n", "\n", "1. The tool takes in a URL and returns the content of the page in Markdown format;\n", "2. If the return is not a valid Markdown, it means the tool I cannot successfully parse this page.\"\"\"\n", "\n", "open_browser_tool = next(\n", "    tool for tool in tool_definitions if tool.name == \"open-browser\"\n", ")\n", "print(open_browser_tool.description)\n", "print(json.dumps(json.loads(open_browser_tool.input_schema_json), indent=4))\n", "print()\n", "open_browser_tool.description = \"\"\"Open a URL in the default browser.\n", "\n", "1. The tool takes in a URL and opens it in the default browser.\n", "2. The tool does not return any content. It is intended for the user to visually inspect and interact with the page. You will not have access to it.\n", "3. You should only call this tool on the same URL once in a conversation, because the page will be open in the user's browser and the user can see it and refresh it themselves. Each call to this tool will jump the user to the browser window and add a new tab for the same page, which is highly annoying when done multiple times.\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["memories = \"\"\"# General\n", "- When user does not specify which frameworks to use, default to modern frameworks, e.g. Next.js for web app.\n", "- You should not use `open-browser` on a URL that you have called the tool on before in the conversation history, because the page is already open in the user's browser and the user can see it and refresh it themselves. Modern frameworks like Next.js have hot reload, so the user can see the changes without a refresh. Each time you call `open-browser`, it will jump the user to the browser window, which is highly annoying to the user.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import run_model\n", "from base.prompt_format_chat import (\n", "    get_structured_chat_prompt_formatter_by_name,\n", ")\n", "from research.tools.chat_replay.replay_utils import TOKEN_APPORTIONMENT\n", "\n", "prompt_formatter = get_structured_chat_prompt_formatter_by_name(\n", "    \"agent-binks-claude-v2\", TOKEN_APPORTIONMENT\n", ")\n", "truncated_prompt_input = dataclasses.replace(\n", "    truncated_prompt_input, tool_definitions=tool_definitions, memories=memories\n", ")\n", "prompt_output = prompt_formatter.format_prompt(truncated_prompt_input)\n", "\n", "response = run_model(prompt_output, tool_definitions=chat_prompt_input.tool_definitions)\n", "print_response(response, tool_limit=-1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
import argparse
import json
from pathlib import Path
import torch
from base.fastforward import fwd
from base.fastforward.checkpoints import save_load
from base.fastforward.llama.fwd_llama import (
    Llama,
    LlamaAttentionFactory,
    generate_step_fn,
)
from base.fastforward.llama.model_specs import LlamaModelSpec, get_llama_model_spec
from research.fastbackward.distributed import init_distributed_for_training
from research.fastbackward.model import ModelArgs
from research.fastbackward.retrieval_models import (
    load_embedder_from_checkpoint,
    TransformerEmbedder,
)
from research.models.embedding_models.fastbackward import (
    create_from_dual_encoder_checkpoint,
)
from research.models.embedding_models.fastforward import FastForwardEmbeddingModel

from research.tools.ckp_converter.fbw2ffw_llama import (
    is_qwen25,
    combine_weights,
    stack_layer,
    convert_weights_to_fwd,
)


def main(ckpt_path: Path | str, output_path: Path | str):
    if isinstance(ckpt_path, str):
        ckpt_path = Path(ckpt_path)
    query_model, doc_model = create_from_dual_encoder_checkpoint(ckpt_path)
    model_params = json.loads((ckpt_path / "params.json").read_text("utf-8"))
    model_args = ModelArgs.load_from_dict(
        model_params["model/query_model/language_model"]["params"]
    )
    print("Finished loading state dicts.")

    lm = query_model.model.lm
    out_proj = query_model.model.output_projection
    out_proj_state_dict = out_proj.state_dict()

    state_dicts = lm.state_dict()
    combined_state_dict = combine_weights(
        [state_dicts], model_args.n_layers, has_qkv_bias=is_qwen25(model_args)
    )

    # Last layer of an embedding model is the output projection.
    combined_state_dict["output_projection.weight"] = out_proj_state_dict["weight"]
    if "bias" in out_proj_state_dict:
        combined_state_dict["output_projection.bias"] = out_proj_state_dict["bias"]

    ffw_state_dict = convert_weights_to_fwd(model_args, combined_state_dict)
    save_load.save_weights(output_path, ffw_state_dict)
    print("Finished saving.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--ckpt-path",
        type=Path,
        required=True,
        help="Path to directory containing fastbackward checkpoint",
    )
    parser.add_argument(
        "--output-path",
        type=Path,
        required=True,
        help="Path to directory where to store fastforward checkpoint. Has to be an absolute path.",
    )
    args = parser.parse_args()

    # init model group
    init_distributed_for_training(1)

    main(
        ckpt_path=args.ckpt_path,
        output_path=args.output_path,
    )

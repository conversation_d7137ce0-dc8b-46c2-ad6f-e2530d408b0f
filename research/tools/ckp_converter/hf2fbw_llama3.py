"""Convert LLaMa 3 and 3.1 family checkpoint from HuggingFace to FastBackward.

Usage:
    python research/tools/ckp_converter/hf2fbw_llama3.py \
        --input_ckpt_dir /mnt/efs/augment/checkpoints/llama3.1/hf/Meta-Llama-3.1-8B \
        --output_ckpt_dir /mnt/efs/augment/checkpoints/llama3.1/fbw/Meta-Llama-3.1-8B \
        --model_name llama31_8b \
        --mp_size 1

    python research/tools/ckp_converter/hf2fbw_llama3.py \
        --input_ckpt_dir /mnt/efs/augment/checkpoints/llama3.1/hf/Meta-Llama-3.1-70B \
        --output_ckpt_dir /mnt/efs/augment/checkpoints/llama3.1/fbw/Meta-Llama-3.1-70B \
        --model_name llama31_70b \
        --mp_size 16
"""

import argparse
import json
import pathlib

import torch
import tqdm
from safetensors import safe_open

import research.fastbackward.configs.llama31_8b as llama31_8b
import research.fastbackward.configs.llama31_70b as llama31_70b
from research.core import utils_for_file, utils_for_log
from research.fastbackward.checkpointing import utils as fbw_ckp_utils
from research.fastbackward.model import ModelArgs

CONFIGS = {
    "llama31_70b": llama31_70b,
    "llama31_8b": llama31_8b,
}


class HFCheckpoint:
    """A class to load the LLAMA 3.1 HuggingFace checkpoint."""

    def __init__(self, ckp_dir: pathlib.Path):
        self.ckp_dir = ckp_dir
        assert self.ckp_dir.exists()
        self.config_json = self.ckp_dir / "config.json"
        assert self.config_json.exists()
        self.config = utils_for_file.read_json(self.config_json)
        # read the hyper-parameters
        self.hidden_size = self.config["hidden_size"]
        self.max_position_embeddings = self.config["max_position_embeddings"]
        self.num_hidden_layers = self.config["num_hidden_layers"]
        # load the model safetensor index
        self.safetensor_index = utils_for_file.read_json(
            self.ckp_dir / "model.safetensors.index.json"
        )
        self.weight_map = self.safetensor_index["weight_map"]
        # create a weight buffer
        self.weight_buffer = {}

    def load_all_weights(self):
        all_safetensor_paths: set[pathlib.Path] = set()
        required_weight_names = []
        for weight_name, safetensor_filename in self.weight_map.items():
            safetensor_path: pathlib.Path = self.ckp_dir / safetensor_filename
            assert safetensor_path.exists(), f"{safetensor_path} does not exist."
            all_safetensor_paths.add(safetensor_path)
            required_weight_names.append(weight_name)
        for safetensor_path in tqdm.tqdm(
            all_safetensor_paths,
            desc="Loading weights from ckp",
            total=len(all_safetensor_paths),
        ):
            with safe_open(safetensor_path, framework="pt", device="cpu") as f:  # type: ignore
                for key in f.keys():
                    if key in self.weight_buffer:
                        print(f"Weight {key} already in the buffer skip.")
                        continue
                    self.weight_buffer[key] = f.get_tensor(key)
        for weight_name in required_weight_names:
            if weight_name not in self.weight_buffer:
                print(f"Still missing {weight_name}!!!!!!!!")
        print(f"There are {len(self.weight_buffer)} weight tensors in the buffer.")
        counts = 0
        for key, value in self.weight_buffer.items():
            counts += value.numel()
        print(f"There are {counts} weights in the buffer.")

    def convert_to_fbw_state_dict(
        self, model_spec: ModelArgs
    ) -> dict[str, torch.Tensor]:
        assert self.num_hidden_layers == model_spec.n_layers
        assert self.hidden_size == model_spec.dim
        assert model_spec.attn_config is not None
        assert not model_spec.attn_config.bias
        weights: dict[str, torch.Tensor] = {}
        weights["tok_embeddings.weight"] = self.weight_buffer["model.embed_tokens.weight"]  # fmt: off
        weights["norm.weight"] = self.weight_buffer["model.norm.weight"]
        weights["output.weight"] = self.weight_buffer["lm_head.weight"]
        for ilayer in range(self.num_hidden_layers):
            # Convert the attention layer
            # fmt: off
            weights[f"layers.{ilayer}.attention.wq.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.q_proj.weight"]
            weights[f"layers.{ilayer}.attention.wk.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.k_proj.weight"]
            weights[f"layers.{ilayer}.attention.wv.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.v_proj.weight"]
            weights[f"layers.{ilayer}.attention.wo.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.o_proj.weight"]
            # Convert the FFN layer
            weights[f"layers.{ilayer}.feed_forward.w1.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.gate_proj.weight"]
            weights[f"layers.{ilayer}.feed_forward.w3.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.up_proj.weight"]
            weights[f"layers.{ilayer}.feed_forward.w2.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.down_proj.weight"]
            # Convert the RMS norm
            weights[f"layers.{ilayer}.attention_norm.weight"] = self.weight_buffer[f"model.layers.{ilayer}.input_layernorm.weight"]
            weights[f"layers.{ilayer}.ffn_norm.weight"] = self.weight_buffer[f"model.layers.{ilayer}.post_attention_layernorm.weight"]
            # fmt: on
        return weights


def main(
    model_name: str,
    input_ckpt_dir: pathlib.Path,
    output_ckpt_dir: pathlib.Path,
    mp_size: int,
):
    """Converts a HuggingFace checkpoint to FastForward."""
    ckp_manager = HFCheckpoint(input_ckpt_dir)
    ckp_manager.load_all_weights()

    output_dir, out_filename = output_ckpt_dir.parent, output_ckpt_dir.name

    config = CONFIGS[model_name]
    model_spec = ModelArgs(
        dim=config.n_embd,
        n_layers=config.n_layers,
        ffn_type="",
        pos_embed_type="rope",
        rotary_config=config.rotary_config,
        attn_config=config.attn_config,
        ffn_config=config.ffn_config,
        first_layer_ffn_config=config.first_layer_ffn_config,
        norm_eps=config.norm_eps,
        max_seq_len=config.block_size,
        vocab_size=config.model_vocab_size,
        use_activation_checkpointing=False,
        use_sequence_parallel=False,
    )
    state_dict = ckp_manager.convert_to_fbw_state_dict(model_spec)
    print("Finished loading weights.")
    # Create the params.json
    params = ModelArgs.schema().dump(model_spec)
    # Remove some deprecated fields
    params.pop("n_heads")
    params.pop("n_kv_heads")
    params.pop("rope_theta")
    params.pop("multiple_of")
    params.pop("ffn_dim_multiplier")
    cur_out_dir = output_dir / f"{out_filename}-mp{mp_size}"
    cur_out_dir.mkdir(parents=False, exist_ok=True)
    print(f"{utils_for_log.time_string()} save into {cur_out_dir}.")
    json.dump(params, open(cur_out_dir / "params.json", "w"), indent=2)

    if mp_size == 1:
        torch.save(state_dict, cur_out_dir / "consolidated.00.pth")
    else:
        for rank in range(mp_size):
            state_dict_mp = fbw_ckp_utils.split_consolidated_checkpoint(
                model_spec, state_dict, mp_size, rank
            )
            torch.save(state_dict_mp, cur_out_dir / f"consolidated.{rank:02d}.pth")


def parse_args():
    """Parse command-line arguments for the script."""
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script for converting finetuned DeepSeek model to fastforward format.",
    )
    parser.add_argument(
        "--model_name",
        type=str,
        required=True,
        choices=list(CONFIGS.keys()),
        help="The name of the model to convert.",
    )
    parser.add_argument(
        "--input_ckpt_dir",
        type=pathlib.Path,
        required=True,
        help="Path to directory containing fastbackward checkpoint",
    )
    parser.add_argument(
        "--output_ckpt_dir",
        type=pathlib.Path,
        required=True,
        help="Path to directory where to store fastforward checkpoint",
    )
    parser.add_argument(
        "--mp_size",
        type=int,
        required=True,
        help="The model parallel size of the checkpoint.",
    )
    return parser.parse_args()


if __name__ == "__main__":
    cli_args = parse_args()
    main(
        cli_args.model_name,
        cli_args.input_ckpt_dir,
        cli_args.output_ckpt_dir,
        cli_args.mp_size,
    )

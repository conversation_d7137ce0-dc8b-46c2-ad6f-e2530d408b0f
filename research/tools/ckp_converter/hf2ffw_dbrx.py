"""Convert DBRX checkpoint from HuggingFace to fastforward.

Usage:
    python research/tools/ckp_converter/hf2ffw_dbrx.py \
        --input_ckpt_dir $HF_CKP_DIR \
        --output_ckpt_dir $FFW_CKP_DIR
"""

import argparse
import pathlib

import torch
import tqdm
from safetensors import safe_open
from torch._subclasses.fake_tensor import FakeTensorMode

from base.fastforward.checkpoints import save_load
from base.fastforward.dbrx import fwd_dbrx, model_specs
from research.core import utils_for_file
from research.core.utils_for_log import time_string


class DbrxHFCheckpoint:
    """A class to load the DBRX HuggingFace checkpoint."""

    def __init__(self, ckp_dir: pathlib.Path):
        self.ckp_dir = ckp_dir
        assert self.ckp_dir.exists()
        self.config_json = self.ckp_dir / "config.json"
        assert self.config_json.exists()
        self.config = utils_for_file.read_json(self.config_json)
        # read the hyper-parameters
        self.d_model = self.config["d_model"]
        self.n_heads = self.config["n_heads"]
        self.n_layers = self.config["n_layers"]
        self.ffn_hidden_size = _load_value_from_nested_dict(
            self.config, ["ffn_config", "ffn_hidden_size"]
        )
        self.moe_num_experts = _load_value_from_nested_dict(
            self.config, ["ffn_config", "moe_num_experts"]
        )
        self.moe_top_k = _load_value_from_nested_dict(
            self.config, ["ffn_config", "moe_top_k"]
        )
        self.max_seq_len = self.config["max_seq_len"]
        # load the model safetensor index
        self.safetensor_index = utils_for_file.read_json(
            self.ckp_dir / "model.safetensors.index.json"
        )
        self.weight_map = self.safetensor_index["weight_map"]
        # create a weight buffer
        self.weight_buffer = {}

    def load_all_weights(self):
        all_safetensor_paths: set[pathlib.Path] = set()
        required_weight_names = []
        for weight_name, safetensor_filename in self.weight_map.items():
            safetensor_path: pathlib.Path = self.ckp_dir / safetensor_filename
            assert safetensor_path.exists(), f"{safetensor_path} does not exist."
            all_safetensor_paths.add(safetensor_path)
            required_weight_names.append(weight_name)
        for safetensor_path in tqdm.tqdm(
            all_safetensor_paths,
            desc="Loading weights from ckp",
            total=len(all_safetensor_paths),
        ):
            with safe_open(safetensor_path, framework="pt", device="cpu") as f:  # type: ignore
                for key in f.keys():
                    if key in self.weight_buffer:
                        print(f"Weight {key} already in the buffer skip.")
                        continue
                    self.weight_buffer[key] = f.get_tensor(key)
        for weight_name in required_weight_names:
            if weight_name not in self.weight_buffer:
                print(f"Still missing {weight_name}!!!!!!!!")


def main(hf_ckpt_dir: pathlib.Path, output_ckpt_dir: pathlib.Path):
    dbrx_ckp = DbrxHFCheckpoint(hf_ckpt_dir)
    dbrx_ckp.load_all_weights()

    model_spec = model_specs.get_model_spec("dbrx-132b")
    fake_mode = FakeTensorMode()
    with fake_mode:
        model = fwd_dbrx.Dbrx(model_spec)
        expected_model_state_dict = model.state_dict()
        expected_model_keys = sorted(list(set(expected_model_state_dict.keys())))

    print(f"{time_string()}: Start to save the checkpoint into {output_ckpt_dir}")
    print(f"{time_string()}: Need to convert {len(expected_model_keys)} keys.")
    output_ckpt_dir.mkdir(parents=False, exist_ok=True)

    # Convert non-Transformer-block layer
    non_transformer_tensors = {}
    non_transformer_tensors["embs.word_embs"] = dbrx_ckp.weight_buffer[
        "transformer.wte.weight"
    ]
    non_transformer_tensors["score.weight"] = dbrx_ckp.weight_buffer["lm_head.weight"]
    non_transformer_tensors["final_layer_norm.weight"] = dbrx_ckp.weight_buffer[
        "transformer.norm_f.weight"
    ]
    for key in non_transformer_tensors.keys():
        assert (
            non_transformer_tensors[key].shape == expected_model_state_dict[key].shape
        )
        assert key in expected_model_keys
        expected_model_keys.remove(key)
    save_load.save_weights(output_ckpt_dir, non_transformer_tensors)
    for ilayer in tqdm.tqdm(
        range(dbrx_ckp.n_layers), desc="Processing Transformer blocks"
    ):
        # Process each Transformer block
        v1 = dbrx_ckp.weight_buffer[f"transformer.blocks.{ilayer}.ffn.experts.mlp.v1"]
        w1 = dbrx_ckp.weight_buffer[f"transformer.blocks.{ilayer}.ffn.experts.mlp.w1"]
        w2 = dbrx_ckp.weight_buffer[f"transformer.blocks.{ilayer}.ffn.experts.mlp.w2"]
        router = dbrx_ckp.weight_buffer[
            f"transformer.blocks.{ilayer}.ffn.router.layer.weight"
        ]
        qkv = dbrx_ckp.weight_buffer[
            f"transformer.blocks.{ilayer}.norm_attn_norm.attn.Wqkv.weight"
        ]
        out_proj = dbrx_ckp.weight_buffer[
            f"transformer.blocks.{ilayer}.norm_attn_norm.attn.out_proj.weight"
        ]
        norm1 = dbrx_ckp.weight_buffer[
            f"transformer.blocks.{ilayer}.norm_attn_norm.norm_1.weight"
        ]
        norm2 = dbrx_ckp.weight_buffer[
            f"transformer.blocks.{ilayer}.norm_attn_norm.norm_2.weight"
        ]
        cur_block_tensors = {}
        cur_block_tensors[f"layers.{ilayer}.ffn.router.weight"] = router
        v1x = v1.view(
            model_spec.moe_num_experts, model_spec.mlp_hidden_dim, model_spec.emb_dim
        )
        cur_block_tensors[f"layers.{ilayer}.ffn.v1"] = v1x.transpose(1, 2).contiguous()
        w1x = w1.view(
            model_spec.moe_num_experts, model_spec.mlp_hidden_dim, model_spec.emb_dim
        )
        cur_block_tensors[f"layers.{ilayer}.ffn.w1"] = w1x.transpose(1, 2).contiguous()
        w2x = w2.view(
            model_spec.moe_num_experts, model_spec.mlp_hidden_dim, model_spec.emb_dim
        )
        cur_block_tensors[f"layers.{ilayer}.ffn.w2"] = w2x.contiguous()
        cur_block_tensors[f"layers.{ilayer}.attn_norm.weight"] = norm1
        cur_block_tensors[f"layers.{ilayer}.ffn_norm.weight"] = norm2
        # Reorganize the qkv tensor.
        q_w, k_w, v_w = qkv.split(
            [
                model_spec.emb_dim,
                model_spec.num_heads_kv * model_spec.head_dim,
                model_spec.num_heads_kv * model_spec.head_dim,
            ],
            dim=0,
        )
        q_w = q_w.view(
            model_spec.num_heads_kv, -1, model_spec.head_dim, model_spec.emb_dim
        ).view(model_spec.num_heads_kv, -1, model_spec.emb_dim)
        k_w = k_w.view(
            model_spec.num_heads_kv, 1, model_spec.head_dim, model_spec.emb_dim
        ).view(model_spec.num_heads_kv, -1, model_spec.emb_dim)
        v_w = v_w.view(
            model_spec.num_heads_kv, 1, model_spec.head_dim, model_spec.emb_dim
        ).view(model_spec.num_heads_kv, -1, model_spec.emb_dim)
        new_qkv = torch.cat([q_w, k_w, v_w], dim=1).view(-1, model_spec.emb_dim)
        cur_block_tensors[f"layers.{ilayer}.attn.qkv.weight"] = new_qkv
        cur_block_tensors[f"layers.{ilayer}.attn.out.weight"] = out_proj
        for key in cur_block_tensors.keys():
            assert cur_block_tensors[key].shape == expected_model_state_dict[key].shape
            assert key in expected_model_keys
            expected_model_keys.remove(key)
        save_load.save_weights(output_ckpt_dir, cur_block_tensors, incremental=True)

    print(f"{time_string()}: finish saving the checkpoint into {output_ckpt_dir}.")
    assert (
        len(expected_model_keys) == 0
    ), f"The following keys are not found in the model: {expected_model_keys}"


def _parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script to convert DBRX checkpoint from huggingface to fastforward format.",
    )
    parser.add_argument(
        "--input_ckpt_dir",
        "-i",
        type=pathlib.Path,
        required=True,
        help="Path to directory containing huggingface checkpoint",
    )
    parser.add_argument(
        "--output_ckpt_dir",
        "-o",
        type=pathlib.Path,
        required=True,
        help="Path to directory where to store fastforward checkpoint",
    )
    return parser.parse_args()


def _load_value_from_nested_dict(dictionary: dict, keys: list[str], default_value=None):
    """Load a value from a nested dictionary.

    For example, the dictionary config = {"a": {"b": {"c": 1}}}
    _load_value_from_nested_dict(config, ["a", "b", "c"]) returns 1.
    """
    current = dictionary
    for key in keys:
        if key in current:
            current = current[key]
        elif default_value is not None:
            return default_value
        else:
            raise ValueError(f"Key {key} not found in the dict: {dictionary}")
    return current


if __name__ == "__main__":
    cli_args = _parse_args()
    main(cli_args.input_ckpt_dir, cli_args.output_ckpt_dir)

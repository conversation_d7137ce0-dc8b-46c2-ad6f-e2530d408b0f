"""Load the HuggingFace format and convert it to the FastBackward checkpoint format for the DeepSeek-Coder-V2-Lite.

python research/tools/ckp_converter/hf2fbw_dsv2.py \
    --hf_checkpoint_dir /mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Base \
    --output_checkpoint_path /mnt/efs/augment/checkpoints/dxy/deepseek-ai/DeepSeek-Coder-V2-Lite-Base-FBW \
    --mp_size 4
"""

import argparse
import json
import pathlib

import torch
import torch.distributed as dist

from research.core import utils_for_log
from research.fastbackward import inference
from research.fastbackward.checkpointing import utils as fbw_ckp_utils
from research.fastbackward.configs import (
    deepseek_coder_v2_lite_dmoe,
    deepseek_coder_v2_lite_smoe,
)
from research.fastbackward.distributed import find_available_local_port
from research.fastbackward.model import DeepSeekV2MoESpec, ModelArgs, Transformer
from research.tools.ckp_converter.hf2ffw_dsv2 import HFCheckpoint as HFCheckpoint4FFW


class HFCheckpoint(HFCheckpoint4FFW):
    """A class to load the DeepSeek-Coder-V2 HuggingFace checkpoint."""

    def convert_to_ffw_state_dict(  # type: ignore
        self, model_spec: ModelArgs
    ) -> dict[str, torch.Tensor]:
        assert self.num_hidden_layers == model_spec.n_layers
        assert isinstance(model_spec.ffn_config, DeepSeekV2MoESpec)
        assert self.n_routed_experts == model_spec.ffn_config.n_routed_experts
        weights: dict[str, torch.Tensor] = {}
        weights["tok_embeddings.weight"] = self.weight_buffer[
            "model.embed_tokens.weight"
        ]
        weights["norm.weight"] = self.weight_buffer["model.norm.weight"]
        weights["output.weight"] = self.weight_buffer["lm_head.weight"]
        for ilayer in range(self.num_hidden_layers):
            # Convert the attention layer
            # fmt: off
            weights[f"layers.{ilayer}.attention.q_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.q_proj.weight"]
            weights[f"layers.{ilayer}.attention.kv_a_proj_with_mqa.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.kv_a_proj_with_mqa.weight"]
            weights[f"layers.{ilayer}.attention.kv_a_layernorm.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.kv_a_layernorm.weight"]
            weights[f"layers.{ilayer}.attention.kv_b_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.kv_b_proj.weight"]
            weights[f"layers.{ilayer}.attention.o_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.o_proj.weight"]
            # Handle the MLP or MoE layer
            if ilayer == 0:
                weights[f"layers.{ilayer}.feed_forward.w1.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.gate_proj.weight"]
                weights[f"layers.{ilayer}.feed_forward.w3.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.up_proj.weight"]
                weights[f"layers.{ilayer}.feed_forward.w2.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.down_proj.weight"]
            else:
                weights[f"layers.{ilayer}.feed_forward.gate_weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.gate.weight"]
                weights[f"layers.{ilayer}.feed_forward.shared_experts.w1.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.shared_experts.gate_proj.weight"]
                weights[f"layers.{ilayer}.feed_forward.shared_experts.w3.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.shared_experts.up_proj.weight"]
                weights[f"layers.{ilayer}.feed_forward.shared_experts.w2.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.shared_experts.down_proj.weight"]
                if model_spec.ffn_config.use_dense_moe:
                    weights[f"layers.{ilayer}.feed_forward.w1"] = torch.stack([self.weight_buffer[f"model.layers.{ilayer}.mlp.experts.{iexpert}.gate_proj.weight"] for iexpert in range(self.n_routed_experts)], dim=0)
                    weights[f"layers.{ilayer}.feed_forward.w3"] = torch.stack([self.weight_buffer[f"model.layers.{ilayer}.mlp.experts.{iexpert}.up_proj.weight"] for iexpert in range(self.n_routed_experts)], dim=0)
                    weights[f"layers.{ilayer}.feed_forward.w2"] = torch.stack([self.weight_buffer[f"model.layers.{ilayer}.mlp.experts.{iexpert}.down_proj.weight"] for iexpert in range(self.n_routed_experts)], dim=0)
                else:
                    weights[f"layers.{ilayer}.feed_forward.w1"] = torch.stack([self.weight_buffer[f"model.layers.{ilayer}.mlp.experts.{iexpert}.gate_proj.weight"] for iexpert in range(self.n_routed_experts)], dim=0)
                    weights[f"layers.{ilayer}.feed_forward.w3"] = torch.stack([self.weight_buffer[f"model.layers.{ilayer}.mlp.experts.{iexpert}.up_proj.weight"] for iexpert in range(self.n_routed_experts)], dim=0)
                    weights[f"layers.{ilayer}.feed_forward.w2"] = torch.stack([self.weight_buffer[f"model.layers.{ilayer}.mlp.experts.{iexpert}.down_proj.weight"] for iexpert in range(self.n_routed_experts)], dim=0)
                    # NOTE(Xuanyi): Please keep this comment here as it is for the "correct" sparse implementation
                    # for inference, however, it will raise some nccl issues during the training.
                    #
                    # for iexpert in range(self.n_routed_experts):
                    #     weights[f"layers.{ilayer}.feed_forward.experts.{iexpert}.w1.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.experts.{iexpert}.gate_proj.weight"]
                    #     weights[f"layers.{ilayer}.feed_forward.experts.{iexpert}.w3.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.experts.{iexpert}.up_proj.weight"]
                    #     weights[f"layers.{ilayer}.feed_forward.experts.{iexpert}.w2.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.experts.{iexpert}.down_proj.weight"]
            # Convert the RMS norm
            weights[f"layers.{ilayer}.attention_norm.weight"] = self.weight_buffer[f"model.layers.{ilayer}.input_layernorm.weight"]
            weights[f"layers.{ilayer}.ffn_norm.weight"] = self.weight_buffer[f"model.layers.{ilayer}.post_attention_layernorm.weight"]
            # fmt: on
        return weights


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script to convert DeepSeek Coder V2 checkpoint from huggingface to fastbackward format.",
    )
    parser.add_argument(
        "--mp_size",
        type=int,
        default=1,
        help="The model parallel size of the checkpoint, if not specified it will be assumed to be 1 for 16B models.",
    )
    parser.add_argument(
        "--hf_checkpoint_dir",
        type=pathlib.Path,
        default=None,
        required=True,
        help="Path to directory containing huggingface checkpoint",
    )
    parser.add_argument(
        "--output_checkpoint_path",
        type=pathlib.Path,
        default=None,
        required=True,
        help="Path to directory where to store fastbackward checkpoint",
    )
    args = parser.parse_args()
    root_out_dir = args.output_checkpoint_path.parent
    out_filename = args.output_checkpoint_path.name

    ckp_manager = HFCheckpoint(args.hf_checkpoint_dir)
    ckp_manager.load_all_weights()
    # ckp_manager.show_weight_names(re.compile(r"^(?!model\.layers)"))
    # self.show_weight_names(re.compile(r'^model\.layers\.1\..*'))

    assert not dist.is_initialized()
    master_port = find_available_local_port()
    print(f"We find the master port: {master_port}")
    inference._parallel_init(0, 1, "127.0.0.1", master_port)
    for identifier, fbw_config in (
        ("sparse", deepseek_coder_v2_lite_smoe),
        ("dense", deepseek_coder_v2_lite_dmoe),
    ):
        model_spec = ModelArgs(
            dim=fbw_config.n_embd,
            n_layers=fbw_config.n_layers,
            ffn_type="",
            pos_embed_type="rope",
            rotary_config=fbw_config.rotary_config,
            attn_config=fbw_config.attn_config,
            ffn_config=fbw_config.ffn_config,
            first_layer_ffn_config=fbw_config.first_layer_ffn_config,
            norm_eps=fbw_config.norm_eps,
            max_seq_len=fbw_config.block_size,
            vocab_size=fbw_config.model_vocab_size,
            use_activation_checkpointing=False,
            use_sequence_parallel=False,
        )
        # model = Transformer(model_spec)
        # model.bfloat16()
        state_dict = ckp_manager.convert_to_ffw_state_dict(model_spec)
        # model.load_state_dict(state_dict, strict=True)

        # Create the params.json
        params = ModelArgs.schema().dump(model_spec)
        # Remove some deprecated fields
        params.pop("n_heads")
        params.pop("n_kv_heads")
        params.pop("rope_theta")
        params.pop("multiple_of")
        params.pop("ffn_dim_multiplier")
        cur_out_dir = root_out_dir / f"{out_filename}-{identifier}-mp{args.mp_size}"
        cur_out_dir.mkdir(parents=False, exist_ok=True)
        print(f"{utils_for_log.time_string()} save into {cur_out_dir}.")
        json.dump(params, open(cur_out_dir / "params.json", "w"), indent=2)
        if args.mp_size == 1:
            torch.save(state_dict, cur_out_dir / "consolidated.00.pth")
        else:
            for rank in range(args.mp_size):
                state_dict_mp = fbw_ckp_utils.split_consolidated_checkpoint(
                    model_spec, state_dict, args.mp_size, rank
                )
                torch.save(state_dict_mp, cur_out_dir / f"consolidated.{rank:02d}.pth")
    print(
        f"{utils_for_log.time_string()} Finish saving the fastforward checkpoint into {root_out_dir}."
    )

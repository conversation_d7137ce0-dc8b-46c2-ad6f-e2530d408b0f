"""Convert Starcoder-1 retriever checkpoint from fastbackward to neox.

The script assumes model parallel size is 1.

Usage:
    python research/tools/ckp_converter/fbw2neox_star1_retriever.py \
        --input_ckpt_dir $FBW_CKP_DIR \
        --output_ckpt_dir $NEOX_CKP_DIR
"""

import argparse
import dataclasses
import json
import pathlib

import torch
import yaml

import base.fastforward.fwd as fwd
import base.fastforward.starcoder.fwd_starcoder as fwd_starcoder
from base.fastforward.checkpoints.migrations import convert_to_v2
from base.fastforward.fwd_utils import get_model_spec_from_neox_checkpoint
from research.fastbackward import distributed, retrieval_models
from research.fastbackward.model import ModelArgs


def make_w_kv(wk: torch.Tensor, wv: torch.Tensor) -> torch.Tensor:
    """Shapes are as follows.

    Args:
        wk: num_heads_kv * head_dim, emb_dim.
        wv: num_heads_kv * head_dim, emb_dim.

    Returns:
        w_kv: [(num_heads_q + 2 * num_heads_kv) * head_dim, emb_dim]
    """
    w_kv = torch.concat(
        [
            wk.contiguous(),
            wv.contiguous(),
        ],
        dim=0,
    ).contiguous()
    return w_kv


def make_bias_qkv(bk: torch.Tensor, bv: torch.Tensor) -> torch.Tensor:
    """Shapes are as follows.

    Args:
        bk: num_heads_kv * head_dim.
        bv: num_heads_kv * head_dim.

    Returns:
        b_kv: [(num_heads_q + 2 * num_heads_kv) * head_dim]
    """

    b_kv = torch.cat(
        [
            bk.contiguous(),
            bv.contiguous(),
        ],
        dim=0,
    )

    return b_kv


def _convert_weights_to_neox(
    params: ModelArgs, ref_weights: dict[str, torch.Tensor]
) -> dict[str, torch.Tensor]:
    """Convert fastbackward model weights to neox format."""

    def make_layer_name(layer_idx: int) -> str:
        return f"layer_{layer_idx:02d}-model_00-model_states.pt"

    neox_weights = {}
    layer_0_weights = {
        "dummy": torch.ones(1),
        "word_embeddings.weight": ref_weights["tok_embeddings.weight"],
        "position_embeddings.weight": ref_weights["pos_embeddings.weight"],
    }
    neox_weights[make_layer_name(0)] = layer_0_weights

    # Actual weights are from 02 to n_layers + 1 (inclusive)
    for layer_idx in range(params.n_layers):
        rp = f"layers.{layer_idx}"
        layer_weights = {
            "input_layernorm.weight": ref_weights[f"{rp}.attention_norm.weight"],
            "input_layernorm.bias": ref_weights[f"{rp}.attention_norm.bias"],
            "attention.query.weight": ref_weights[f"{rp}.attention.wq.weight"],
            "attention.query.bias": ref_weights[f"{rp}.attention.wq.bias"],
            "attention.key_value.weight": make_w_kv(
                ref_weights[f"{rp}.attention.wk.weight"],
                ref_weights[f"{rp}.attention.wv.weight"],
            ),
            "attention.key_value.bias": make_bias_qkv(
                ref_weights[f"{rp}.attention.wk.bias"],
                ref_weights[f"{rp}.attention.wv.bias"],
            ),
            "attention.dense.weight": ref_weights[f"{rp}.attention.wo.weight"],
            "attention.dense.bias": ref_weights[f"{rp}.attention.wo.bias"],
            "post_attention_layernorm.weight": ref_weights[f"{rp}.ffn_norm.weight"],
            "post_attention_layernorm.bias": ref_weights[f"{rp}.ffn_norm.bias"],
            "mlp.dense_h_to_4h.weight": ref_weights[f"{rp}.feed_forward.w1.weight"],
            "mlp.dense_h_to_4h.bias": ref_weights[f"{rp}.feed_forward.w1.bias"],
            "mlp.dense_4h_to_h.weight": ref_weights[f"{rp}.feed_forward.w2.weight"],
            "mlp.dense_4h_to_h.bias": ref_weights[f"{rp}.feed_forward.w2.bias"],
        }

        neox_weights[make_layer_name(layer_idx + 2)] = layer_weights

    # n + 3 is the final norm layer
    final_norm_weights = {
        "norm.weight": ref_weights["norm.weight"],
        "norm.bias": ref_weights["norm.bias"],
    }
    neox_weights[make_layer_name(params.n_layers + 3)] = final_norm_weights

    # n + 4 is the final output layer
    final_output_weights = {
        "projection.weight": ref_weights["projection.weight"],
        "projection.bias": ref_weights["projection.bias"],
    }
    neox_weights[make_layer_name(params.n_layers + 4)] = final_output_weights

    return neox_weights


def load_fastbackward_model(fb_ckpt_dir):
    """Load a fastbackward model from a checkpoint directory."""
    distributed.init_distributed_for_training(1)
    models = retrieval_models.load_checkpoint(fb_ckpt_dir)
    query_model = models["model/query_model"]
    state_dict = query_model.lm.state_dict()
    output_projection_weights = query_model.output_projection.state_dict()
    for k, v in output_projection_weights.items():
        state_dict["projection." + k] = v
    return query_model.lm.params, state_dict


def load_num_training_steps(fb_ckpt_dir):
    """Load the number of training steps from a fastbackward checkpoint directory."""
    with open(fb_ckpt_dir / "last_saved_info.json", "r", encoding="utf-8") as f:
        data = json.load(f)
    return data["iter_num"]


def load_fastforward_model(ff_ckpt_dir, ckpt_sha: str):
    """Load a fastforward model from a checkpoint directory."""
    model_spec = get_model_spec_from_neox_checkpoint(ff_ckpt_dir)
    model_spec.checkpoint_sha256 = ckpt_sha
    _ = fwd_starcoder.generate_step_fn(
        model_spec, output_type=fwd.OutputTensorType.EMBEDDING
    )


def main(input_ckpt_dir: pathlib.Path, output_ckpt_dir: pathlib.Path):
    """
    Convert a finetuned Starcoder 1 retriever model to neox format.
    """
    model_args, state_dict = load_fastbackward_model(input_ckpt_dir)
    print("Finished loading state dicts.")
    dct = ModelArgs.schema().dump(model_args)
    neox_config = {
        "hidden_size": dct["dim"],
        "num_layers": dct["n_layers"],
        "num_attention_heads": dct["n_heads"],
        "make_vocab_size_divisible_by": dct["vocab_size"],
        "layernorm_epsilon": dct["norm_eps"],
        "max_position_embeddings": dct["max_seq_len"],
    }
    if not output_ckpt_dir.exists():
        output_ckpt_dir.mkdir()
    with open(output_ckpt_dir / "config.yml", "w") as f:
        yaml.dump(neox_config, f)
    print("Finished saving config.")
    layer_dict = _convert_weights_to_neox(model_args, state_dict)
    print("Finished converting weights.")

    # Convert weights in two steps.
    # (1) Convert to neox format.
    n_steps = load_num_training_steps(input_ckpt_dir)
    output_weights_dir = output_ckpt_dir / f"global_step{n_steps}"
    if not output_weights_dir.exists():
        output_weights_dir.mkdir()
    for layer_name, layer_weights in layer_dict.items():
        torch.save(layer_weights, output_weights_dir / layer_name)
    print("Finished saving weights in neox format.")

    # (2) Convert to fastforward format.
    output_weights_dir_v2 = output_ckpt_dir / f"global_step{n_steps}-ckpt_v2"
    ckpt_sha = convert_to_v2.migrate_weights(
        str(output_weights_dir), str(output_weights_dir_v2)
    )
    print("Finished saving weights in v2 checkpoint format.")
    print(f"\n\n>>>>>\nCheckpoint sha: {ckpt_sha}\n>>>>>\n\n")

    # Validate
    load_fastforward_model(output_weights_dir_v2, ckpt_sha=ckpt_sha)
    print("Finished loading fastforward model as a sanity check.")
    print("Done.")


def parse_args():
    """
    Parse command-line arguments for the script.

    Returns:
        argparse.Namespace: An object containing the parsed arguments.

    This function uses the argparse library to define and parse command-line arguments.
    It returns an object containing the parsed arguments, which can be used to configure the script's behavior.
    """
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script for converting finetuned Starcoder 1 retriever model to neox format.",
    )
    parser.add_argument(
        "--input_ckpt_dir",
        "-i",
        type=pathlib.Path,
        required=True,
        help="Path to directory containing fastbackward checkpoint",
    )
    parser.add_argument(
        "--output_ckpt_dir",
        "-o",
        type=pathlib.Path,
        required=True,
        help="Path to directory where to store fastforward checkpoint",
    )
    return parser.parse_args()


if __name__ == "__main__":
    cli_args = parse_args()
    main(
        cli_args.input_ckpt_dir,
        cli_args.output_ckpt_dir,
    )

"""Convert Starcoder-1 checkpoint from fastbackward (with different model parallel
 sizes) to neox.

Usage:
    python research/tools/ckp_converter/fbw2neox_star1.py \
        --input_ckpt_dir $FBW_CKP_DIR \
        --output_ckpt_dir $NEOX_CKP_DIR \
        --mp_size $MP_SIZE

This script works for Starcoder-1 models.
"""

import argparse
import pathlib

import torch
import yaml
from tqdm import tqdm

from research.core.utils_for_file import read_json
from research.fastbackward.model import ModelArgs, fix_model_arg_params


def stack_layer(state_dicts, key: str, dim: int):
    if dim is None:
        return state_dicts[0][key]
    return torch.cat([sd[key] for sd in state_dicts], dim=dim)


def combine_weights(state_dicts, num_layers: int):
    result_state_dict = {}
    for key, dim in [
        ("tok_embeddings.weight", 1),
        ("pos_embeddings.weight", 1),
        ("norm.weight", None),
        ("norm.bias", None),
        ("output.weight", 0),
    ]:
        result_state_dict[key] = stack_layer(state_dicts, key, dim).cpu().detach()

    for layer_num in tqdm(range(num_layers), desc="Combining layers"):
        for key_suffix, dim in [
            ("attention.wq.weight", 0),
            ("attention.wq.bias", 0),
            ("attention.wk.weight", None),
            ("attention.wk.bias", None),
            ("attention.wv.weight", None),
            ("attention.wv.bias", None),
            ("attention.wo.weight", 1),
            ("attention.wo.bias", None),
            ("attention_norm.weight", None),
            ("attention_norm.bias", None),
            ("feed_forward.w1.weight", 0),
            ("feed_forward.w1.bias", 0),
            ("feed_forward.w2.weight", 1),
            ("feed_forward.w2.bias", None),
            ("ffn_norm.weight", None),
            ("ffn_norm.bias", None),
        ]:
            key = f"layers.{layer_num}.{key_suffix}"
            result_state_dict[key] = stack_layer(state_dicts, key, dim).cpu().detach()

    return result_state_dict


def make_w_kv(wk: torch.Tensor, wv: torch.Tensor) -> torch.Tensor:
    """Shapes are as follows.

    Args:
        wk: num_heads_kv * head_dim, emb_dim.
        wv: num_heads_kv * head_dim, emb_dim.

    Returns:
        w_kv: [(num_heads_q + 2 * num_heads_kv) * head_dim, emb_dim]
    """
    w_kv = torch.concat(
        [
            wk.contiguous(),
            wv.contiguous(),
        ],
        dim=0,
    ).contiguous()
    return w_kv


def make_bias_qkv(bk: torch.Tensor, bv: torch.Tensor) -> torch.Tensor:
    """Shapes are as follows.

    Args:
        bk: num_heads_kv * head_dim.
        bv: num_heads_kv * head_dim.

    Returns:
        b_kv: [(num_heads_q + 2 * num_heads_kv) * head_dim]
    """

    b_kv = torch.cat(
        [
            bk.contiguous(),
            bv.contiguous(),
        ],
        dim=0,
    )

    return b_kv


def _convert_weights_to_neox(
    params: ModelArgs, ref_weights: dict[str, torch.Tensor]
) -> dict[str, torch.Tensor]:
    def make_layer_name(layer_idx: int) -> str:
        return f"layer_{layer_idx:02d}-model_00-model_states.pt"

    neox_weights = {}
    layer_0_weights = {
        "dummy": torch.ones(1),
        "word_embeddings.weight": ref_weights["tok_embeddings.weight"],
        "position_embeddings.weight": ref_weights["pos_embeddings.weight"],
    }
    neox_weights[make_layer_name(0)] = layer_0_weights

    # Actual weights are from 02 to n_layers + 1 (inclusive)
    for layer_idx in range(params.n_layers):
        rp = f"layers.{layer_idx}"
        layer_weights = {
            "input_layernorm.weight": ref_weights[f"{rp}.attention_norm.weight"],
            "input_layernorm.bias": ref_weights[f"{rp}.attention_norm.bias"],
            "attention.query.weight": ref_weights[f"{rp}.attention.wq.weight"],
            "attention.query.bias": ref_weights[f"{rp}.attention.wq.bias"],
            "attention.key_value.weight": make_w_kv(
                ref_weights[f"{rp}.attention.wk.weight"],
                ref_weights[f"{rp}.attention.wv.weight"],
            ),
            "attention.key_value.bias": make_bias_qkv(
                ref_weights[f"{rp}.attention.wk.bias"],
                ref_weights[f"{rp}.attention.wv.bias"],
            ),
            "attention.dense.weight": ref_weights[f"{rp}.attention.wo.weight"],
            "attention.dense.bias": ref_weights[f"{rp}.attention.wo.bias"],
            "post_attention_layernorm.weight": ref_weights[f"{rp}.ffn_norm.weight"],
            "post_attention_layernorm.bias": ref_weights[f"{rp}.ffn_norm.bias"],
            "mlp.dense_h_to_4h.weight": ref_weights[f"{rp}.feed_forward.w1.weight"],
            "mlp.dense_h_to_4h.bias": ref_weights[f"{rp}.feed_forward.w1.bias"],
            "mlp.dense_4h_to_h.weight": ref_weights[f"{rp}.feed_forward.w2.weight"],
            "mlp.dense_4h_to_h.bias": ref_weights[f"{rp}.feed_forward.w2.bias"],
        }

        neox_weights[make_layer_name(layer_idx + 2)] = layer_weights

    # n + 3 is the final norm layer
    final_norm_weights = {
        "norm.weight": ref_weights["norm.weight"],
        "norm.bias": ref_weights["norm.bias"],
    }
    neox_weights[make_layer_name(params.n_layers + 3)] = final_norm_weights

    # n + 4 is the final output layer
    final_output_weights = {
        "final_linear.weight": ref_weights["output.weight"],
    }
    neox_weights[make_layer_name(params.n_layers + 4)] = final_output_weights

    return neox_weights


def load_fastbackward_model(fb_ckpt_dir, model_parallel_size: int = 4):
    state_dicts = []
    for i in tqdm(range(model_parallel_size), desc="Model loading"):
        state_dict = torch.load(
            fb_ckpt_dir / f"consolidated.{i:02d}.pth", map_location="cpu"
        )
        state_dicts.append(state_dict)

    return state_dicts


def main(input_ckpt_dir: pathlib.Path, output_ckpt_dir: pathlib.Path, mp_size: int):
    dct = read_json(input_ckpt_dir / "params.json")
    dct.pop("dropout", None)
    model_args = ModelArgs.load_from_dict(dct)

    neox_config = {
        "hidden_size": dct["dim"],
        "num_layers": dct["n_layers"],
        "num_attention_heads": dct["n_heads"],
        "make_vocab_size_divisible_by": dct["vocab_size"],
        "layernorm_epsilon": dct["norm_eps"],
        "max_position_embeddings": dct["max_seq_len"],
    }
    with open(output_ckpt_dir / "config.yml", "w") as f:
        yaml.dump(neox_config, f)
    print("Finished saving config.")

    state_dicts = load_fastbackward_model(input_ckpt_dir, mp_size)
    print("Finished loading state dicts.")
    combined_state_dict = combine_weights(state_dicts, model_args.n_layers)
    print("Finished combining weights.")
    layer_dict = _convert_weights_to_neox(model_args, combined_state_dict)
    print("Finished converting weights.")
    for layer_name, layer_weights in layer_dict.items():
        torch.save(layer_weights, output_ckpt_dir / layer_name)
    print("Finished saving weights.")


def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script for converting finetuned Starcoder 1 model to neox format.",
    )
    parser.add_argument(
        "--input_ckpt_dir",
        "-i",
        type=pathlib.Path,
        required=True,
        help="Path to directory containing fastbackward checkpoint",
    )
    parser.add_argument(
        "--output_ckpt_dir",
        "-o",
        type=pathlib.Path,
        required=True,
        help="Path to directory where to store fastforward checkpoint",
    )
    parser.add_argument(
        "--mp_size",
        "-m",
        type=int,
        default=2,
        help="The model parallel size of the checkpoint, if not specified it will be assumed to be 2 for 16B models.",
    )

    return parser.parse_args()


if __name__ == "__main__":
    cli_args = parse_args()
    main(cli_args.input_ckpt_dir, cli_args.output_ckpt_dir, cli_args.mp_size)

"""Convert DeepSeek checkpoint from fastbackward (with different model parallel sizes) to fastforward.

Usage:
    python research/tools/ckp_converter/fbw2ffw_deepseek.py \
        --input_ckpt_dir $FBW_CKP_DIR \
        --output_ckpt_dir $FFW_CKP_DIR \
        --mp_size $MP_SIZE

This script works for DeepSeek-33B models, for other DeepSeek variants, the model parallel size might be specified.
And if the model use a different attention layer, it is not gauranteed to work.
"""

import argparse
import functools
import pathlib

import torch
from tqdm import tqdm

from base.fastforward.checkpoints import save_load
from research.core.utils_for_file import read_json
from research.fastbackward.model import ModelArgs, fix_model_arg_params


def stack_layer(state_dicts, key: str, dim: int):
    if dim is None:
        return state_dicts[0][key]
    return torch.cat([sd[key] for sd in state_dicts], dim=dim)


def combine_weights(state_dicts, num_layers: int):
    result_state_dict = {}
    for key, dim in [
        ("tok_embeddings.weight", 1),
        ("norm.weight", None),
        ("output.weight", 0),
    ]:
        result_state_dict[key] = stack_layer(state_dicts, key, dim).cpu().detach()

    for layer_num in tqdm(range(num_layers), desc="Combining layers"):
        for key_suffix, dim in [
            ("attention.wq.weight", 0),
            ("attention.wk.weight", 0),
            ("attention.wv.weight", 0),
            ("attention.wo.weight", 1),
            ("attention_norm.weight", None),
            ("feed_forward.w1.weight", 0),
            ("feed_forward.w2.weight", 1),
            ("feed_forward.w3.weight", 0),
            ("ffn_norm.weight", None),
        ]:
            key = f"layers.{layer_num}.{key_suffix}"
            result_state_dict[key] = stack_layer(state_dicts, key, dim).cpu().detach()

    return result_state_dict


def make_w_qkv(
    wq: torch.Tensor, wk: torch.Tensor, wv: torch.Tensor, head_dim: int
) -> torch.Tensor:
    """Shapes are as follows.

    Args:
        wq: num_heads_q * head_dim, emb_dim.
        wk: num_heads_kv * head_dim, emb_dim.
        wv: num_heads_kv * head_dim, emb_dim.

    Returns:
        w_qkv: emb_dim, [num_heads_kv, (num_heads_q // num_heads_kv + 2) * head_dim]
    """
    emb_dim = wq.size(1)
    num_heads_kv = wk.size(0) // head_dim
    w_qkv = (
        torch.concat(
            [
                wq.transpose(1, 0)
                .contiguous()
                .reshape(emb_dim, num_heads_kv, -1)
                .contiguous(),
                wk.transpose(1, 0)
                .contiguous()
                .reshape(emb_dim, num_heads_kv, -1)
                .contiguous(),
                wv.transpose(1, 0)
                .contiguous()
                .reshape(emb_dim, num_heads_kv, -1)
                .contiguous(),
            ],
            dim=2,
        )
        .reshape(emb_dim, -1)
        .contiguous()
    )
    return w_qkv.transpose(0, 1).contiguous()


def _convert_weights_to_fwd(
    params: ModelArgs, ref_weights: dict[str, torch.Tensor]
) -> dict[str, torch.Tensor]:
    _make_w_qkv = functools.partial(make_w_qkv, head_dim=params.dim // params.n_heads)

    ffw_weights: dict[str, torch.Tensor] = {
        "embs.word_embs": ref_weights["tok_embeddings.weight"],
        "final_rms_norm.weight": ref_weights["norm.weight"],
        "score.weight": ref_weights["output.weight"],
    }

    for layer_idx in range(params.n_layers):
        lp = f"layers.{layer_idx}"
        ffw_weights.update(
            {
                f"{lp}.attn_norm.weight": ref_weights[f"{lp}.attention_norm.weight"],
                f"{lp}.attn.qkv.weight": _make_w_qkv(
                    ref_weights[f"{lp}.attention.wq.weight"],
                    ref_weights[f"{lp}.attention.wk.weight"],
                    ref_weights[f"{lp}.attention.wv.weight"],
                ),
                f"{lp}.attn.out.weight": ref_weights[f"{lp}.attention.wo.weight"],
                f"{lp}.ffn_norm.weight": ref_weights[f"{lp}.ffn_norm.weight"],
                f"{lp}.ffn.expand.weight": torch.concat(
                    [
                        ref_weights[f"{lp}.feed_forward.w1.weight"],
                        ref_weights[f"{lp}.feed_forward.w3.weight"],
                    ],
                    dim=0,
                ).contiguous(),
                f"{lp}.ffn.shrink.weight": ref_weights[f"{lp}.feed_forward.w2.weight"],
            }
        )

    return ffw_weights


def load_fastbackward_model(fb_ckpt_dir, model_parallel_size: int = 4):
    state_dicts = []
    for i in tqdm(range(model_parallel_size), desc="Model loading"):
        state_dict = torch.load(
            fb_ckpt_dir / f"consolidated.{i:02d}.pth", map_location="cpu"
        )
        state_dicts.append(state_dict)

    return state_dicts


def main(input_ckpt_dir: pathlib.Path, output_ckpt_dir: pathlib.Path, mp_size: int):
    dct = read_json(input_ckpt_dir / "params.json")
    dct.pop("dropout", None)
    model_args = ModelArgs.load_from_dict(dct)

    state_dicts = load_fastbackward_model(input_ckpt_dir, mp_size)
    print("Finished loading state dicts.")
    combined_state_dict = combine_weights(state_dicts, model_args.n_layers)
    del state_dicts
    print("Finished combining weights.")
    ffw_state_dict = _convert_weights_to_fwd(model_args, combined_state_dict)
    del combined_state_dict
    print("Finished converting weights.")
    save_load.save_weights(output_ckpt_dir, ffw_state_dict)
    print("Finished saving.")


def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script for converting finetuned DeepSeek model to fastforward format.",
    )
    parser.add_argument(
        "--input_ckpt_dir",
        "-i",
        type=pathlib.Path,
        required=True,
        help="Path to directory containing fastbackward checkpoint",
    )
    parser.add_argument(
        "--output_ckpt_dir",
        "-o",
        type=pathlib.Path,
        required=True,
        help="Path to directory where to store fastforward checkpoint",
    )
    parser.add_argument(
        "--mp_size",
        "-m",
        type=int,
        default=4,
        help="The model parallel size of the checkpoint, if not specified it will be assumed to be 4 for 33B models.",
    )

    return parser.parse_args()


if __name__ == "__main__":
    cli_args = parse_args()
    main(cli_args.input_ckpt_dir, cli_args.output_ckpt_dir, cli_args.mp_size)

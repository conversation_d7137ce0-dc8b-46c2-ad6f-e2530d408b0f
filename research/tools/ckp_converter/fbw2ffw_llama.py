"""Convert LLaMa-like checkpoint from FastBackward to FastForward.

Note that LLaMa-like architectures include:
- LLaMa 2 family
- LLaMa 3 and 3.1 family
- DeepSeek V1 family (including base and Coder)
- Mistral family (except Mixtral or Codestral Mamba)

Usage:
    python research/tools/ckp_converter/fbw2ffw_llama.py \
        --input_ckpt_dir $FBWD_CKPT_DIR \
        --output_ckpt_dir $FFWD_CKPT_DIR
"""

import argparse
import functools
import pathlib

import torch
from tqdm import tqdm

from base.fastforward.checkpoints import save_load
from research.core.utils_for_file import read_json
from research.fastbackward.model import ModelArgs, GenericAttnSpec, fix_model_arg_params


# Qwen2.5 is _almost_ exactly a llama model, except it has biases for the QKV matmul.
def is_qwen25(params: ModelArgs) -> bool:
    if params.attn_config is not None and isinstance(
        params.attn_config, GenericAttnSpec
    ):
        return params.attn_config.qkv_bias
    return False


def stack_layer(state_dicts, key: str, dim: int | None):
    """Stacks weights from a list of state dictionaries along the dimension."""
    if dim is None:
        return state_dicts[0][key]
    return torch.cat([sd[key] for sd in state_dicts], dim=dim)


def combine_weights(state_dicts, num_layers: int, has_qkv_bias: bool):
    """Combines weights from a list of state dictionaries."""
    result_state_dict = {}
    for key, dim in [
        ("tok_embeddings.weight", 1),
        ("norm.weight", None),
        ("output.weight", 0),
    ]:
        if key not in state_dicts[0]:
            # This is needed since an embedding model does not have an output weight.
            print(f"Skipping {key} because it is not in the state dict.")
            continue
        result_state_dict[key] = stack_layer(state_dicts, key, dim).cpu().detach()

    for layer_num in tqdm(range(num_layers), desc="Combining layers"):
        to_combine = [
            ("attention.wq.weight", 0),
            ("attention.wk.weight", 0),
            ("attention.wv.weight", 0),
            ("attention.wo.weight", 1),
            ("attention_norm.weight", None),
            ("feed_forward.w1.weight", 0),
            ("feed_forward.w2.weight", 1),
            ("feed_forward.w3.weight", 0),
            ("ffn_norm.weight", None),
        ]
        if has_qkv_bias:
            to_combine.extend(
                [
                    ("attention.wq.bias", 0),
                    ("attention.wk.bias", 0),
                    ("attention.wv.bias", 0),
                ]
            )

        for key_suffix, dim in to_combine:
            key = f"layers.{layer_num}.{key_suffix}"
            result_state_dict[key] = stack_layer(state_dicts, key, dim).cpu().detach()

    return result_state_dict


def make_bias_qkv(
    bq: torch.Tensor, bk: torch.Tensor, bv: torch.Tensor, head_dim: int
) -> torch.Tensor:
    """Combine QKV biases (for Qwen) in the same way as weights.

    Shapes:
        bq: [num_heads_q * head_dim]
        bk: [num_heads_kv * head_dim]
        bv: [num_heads_kv * head_dim]

    Returns:
        b_qkv: [(num_heads_q + 2 * num_heads_kv) * head_dim]

    """
    num_heads_kv = bk.size(0) // head_dim
    b_qkv = torch.concat(
        [
            bq.reshape(num_heads_kv, -1),
            bk.reshape(num_heads_kv, -1),
            bv.reshape(num_heads_kv, -1),
        ],
        dim=1,
    ).reshape(-1)
    assert b_qkv.is_contiguous()
    return b_qkv


def make_w_qkv(
    wq: torch.Tensor, wk: torch.Tensor, wv: torch.Tensor, head_dim: int
) -> torch.Tensor:
    """Shapes are as follows.

    Args:
        wq: num_heads_q * head_dim, emb_dim.
        wk: num_heads_kv * head_dim, emb_dim.
        wv: num_heads_kv * head_dim, emb_dim.

    Returns:
        w_qkv: emb_dim, [num_heads_kv, (num_heads_q // num_heads_kv + 2) * head_dim]
    """
    emb_dim = wq.size(1)
    num_heads_kv = wk.size(0) // head_dim
    w_qkv = (
        torch.concat(
            [
                wq.transpose(1, 0)
                .contiguous()
                .reshape(emb_dim, num_heads_kv, -1)
                .contiguous(),
                wk.transpose(1, 0)
                .contiguous()
                .reshape(emb_dim, num_heads_kv, -1)
                .contiguous(),
                wv.transpose(1, 0)
                .contiguous()
                .reshape(emb_dim, num_heads_kv, -1)
                .contiguous(),
            ],
            dim=2,
        )
        .reshape(emb_dim, -1)
        .contiguous()
    )
    return w_qkv.transpose(0, 1).contiguous()


def convert_weights_to_fwd(
    params: ModelArgs, ref_weights: dict[str, torch.Tensor]
) -> dict[str, torch.Tensor]:
    if params.attn_config is not None and isinstance(
        params.attn_config, GenericAttnSpec
    ):
        n_heads = params.attn_config.n_heads
    else:
        n_heads = params.n_heads
    _make_w_qkv = functools.partial(make_w_qkv, head_dim=params.dim // n_heads)
    _make_bias_qkv = functools.partial(make_bias_qkv, head_dim=params.dim // n_heads)
    has_qkv_bias = is_qwen25(params)

    ffw_weights: dict[str, torch.Tensor] = {
        "embs.word_embs": ref_weights["tok_embeddings.weight"],
        "final_rms_norm.weight": ref_weights["norm.weight"],
    }
    if "output.weight" in ref_weights:
        ffw_weights["score.weight"] = ref_weights["output.weight"]

    # For embedding models, we store an embedding projection in the final layer instead of a vocabulary projection.
    if "output_projection.weight" in ref_weights:
        ffw_weights["output_projection.weight"] = ref_weights[
            "output_projection.weight"
        ]
    if "output_projection.bias" in ref_weights:
        ffw_weights["output_projection.bias"] = ref_weights["output_projection.bias"]

    for layer_idx in tqdm(range(params.n_layers), desc="Converting layers"):
        lp = f"layers.{layer_idx}"
        ffw_weights.update(
            {
                f"{lp}.attn_norm.weight": ref_weights[f"{lp}.attention_norm.weight"],
                f"{lp}.attn.qkv.weight": _make_w_qkv(
                    ref_weights[f"{lp}.attention.wq.weight"],
                    ref_weights[f"{lp}.attention.wk.weight"],
                    ref_weights[f"{lp}.attention.wv.weight"],
                ),
                f"{lp}.attn.out.weight": ref_weights[f"{lp}.attention.wo.weight"],
                f"{lp}.ffn_norm.weight": ref_weights[f"{lp}.ffn_norm.weight"],
                f"{lp}.ffn.expand.weight": torch.concat(
                    [
                        ref_weights[f"{lp}.feed_forward.w1.weight"],
                        ref_weights[f"{lp}.feed_forward.w3.weight"],
                    ],
                    dim=0,
                ).contiguous(),
                f"{lp}.ffn.shrink.weight": ref_weights[f"{lp}.feed_forward.w2.weight"],
            }
        )
        if has_qkv_bias:
            ffw_weights.update(
                {
                    f"{lp}.attn.qkv.bias": _make_bias_qkv(
                        ref_weights[f"{lp}.attention.wq.bias"],
                        ref_weights[f"{lp}.attention.wk.bias"],
                        ref_weights[f"{lp}.attention.wv.bias"],
                    ),
                }
            )

    return ffw_weights


def load_fastbackward_model(fb_ckpt_dir):
    """Loads the state dicts from a model-parallel FastBackward checkpoint."""
    weight_paths = sorted(fb_ckpt_dir.glob("consolidated.*.pth"))
    print(f"Loading weights for {len(weight_paths)} MP ranks.")
    state_dicts = []
    for weight_path in tqdm(weight_paths, desc="Loading weights"):
        state_dict = torch.load(weight_path, map_location="cpu")
        state_dicts.append(state_dict)
    return state_dicts


def main(input_ckpt_dir: pathlib.Path, output_ckpt_dir: pathlib.Path):
    """Converts a FastBackward checkpoint to FastForward."""
    dct = read_json(input_ckpt_dir / "params.json")
    dct.pop("dropout", None)
    model_args = ModelArgs.load_from_dict(dct)

    state_dicts = load_fastbackward_model(input_ckpt_dir)
    print("Finished loading state dicts.")
    combined_state_dict = combine_weights(
        state_dicts, model_args.n_layers, has_qkv_bias=is_qwen25(model_args)
    )
    del state_dicts
    print("Finished combining weights.")
    ffw_state_dict = convert_weights_to_fwd(model_args, combined_state_dict)
    del combined_state_dict
    print("Finished converting weights.")
    print("Saving weights...")
    ckpt_manifest = save_load.save_weights(output_ckpt_dir, ffw_state_dict)
    print("Finished saving.")
    return ckpt_manifest


def parse_args():
    """Parse command-line arguments for the script."""
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script for converting finetuned DeepSeek model to fastforward format.",
    )
    parser.add_argument(
        "--input_ckpt_dir",
        "-i",
        type=pathlib.Path,
        required=True,
        help="Path to directory containing fastbackward checkpoint",
    )
    parser.add_argument(
        "--output_ckpt_dir",
        "-o",
        type=pathlib.Path,
        required=True,
        help="Path to directory where to store fastforward checkpoint",
    )
    return parser.parse_args()


if __name__ == "__main__":
    cli_args = parse_args()
    main(cli_args.input_ckpt_dir, cli_args.output_ckpt_dir)

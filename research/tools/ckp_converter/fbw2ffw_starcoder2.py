"""Utils to migrate StarCoder2 checkpoints from FastBackward to FastForward.

Example usage:

python fbw2ffw_starcoder2.py \
    --inp-ckpt="/mnt/efs/augment/checkpoints/roguesl/star2_16b_8k_fb" \
    --out-ckpt="/mnt/efs/augment/user/hieu/starcoder2_test/starcoder2-16b" \
    --model-name="starcoder2-15b"
"""

import argparse
import json
from pathlib import Path
import dataclasses

import torch

from base.fastforward.layers import SplitHeadModes
from base.fastforward.checkpoints import save_load
from base.fastforward.starcoder.model_specs import get_starcoder2_model_spec
from research.core.utils_for_file import read_json
from research.fastbackward import utils as fbw_utils
from research.fastbackward.checkpointing import utils
from base.fastforward.positional_embeddings import DeepSeekV1ExtensionConfig
from research.fastbackward.model import ModelArgs, GenericAttnSpec


def _make_w_qkv(
    wq: torch.Tensor, wk: torch.Tensor, wv: torch.Tensor, head_dim: int
) -> torch.Tensor:
    """Shapes are as follows.

    Args:
        wq: num_heads_q * head_dim, emb_dim.
        wk: num_heads_kv * head_dim, emb_dim.
        wv: num_heads_kv * head_dim, emb_dim.
        head_dim: obvious.

    Returns:
        w_qkv: emb_dim, [num_heads_kv, (num_heads_q // num_heads_kv + 2) * head_dim]
    """
    emb_dim = wq.size(1)
    num_heads_kv = wk.size(0) // head_dim
    w_qkv = (
        torch.concat(
            [
                wq.transpose(1, 0).contiguous().reshape(emb_dim, num_heads_kv, -1),
                wk.transpose(1, 0).contiguous().reshape(emb_dim, num_heads_kv, -1),
                wv.transpose(1, 0).contiguous().reshape(emb_dim, num_heads_kv, -1),
            ],
            dim=2,
        )
        .reshape(emb_dim, -1)
        .contiguous()
    )
    return w_qkv.transpose(0, 1).contiguous()


def _make_b_qkv(
    bq: torch.Tensor, bk: torch.Tensor, bv: torch.Tensor, head_dim: int
) -> torch.Tensor:
    """Shapes are as follows.

    Args:
        bq: num_heads_q * head_dim.
        bk: num_heads_kv * head_dim.
        bv: num_heads_kv * head_dim.
        head_dim: obvious.

    Returns:
        b_qkv: emb_dim, [num_heads_kv, (num_heads_q // num_heads_kv + 2) * head_dim]
    """
    num_heads_kv = bk.size(0) // head_dim
    b_qkv = (
        torch.concat(
            [
                bq.reshape(num_heads_kv, -1).contiguous(),
                bk.reshape(num_heads_kv, -1).contiguous(),
                bv.reshape(num_heads_kv, -1).contiguous(),
            ],
            dim=1,
        )
        .reshape(-1)
        .contiguous()
    )
    return b_qkv.contiguous()


def convert_weights(
    model_args: ModelArgs,
    fbw: dict[str, torch.Tensor],
) -> dict[str, torch.Tensor]:
    # ruff: noqa
    ffw: dict[str, torch.Tensor] = {}

    ffw["embs.word_embs"] = fbw["tok_embeddings.weight"]

    num_layers = model_args.n_layers
    assert isinstance(model_args.attn_config, GenericAttnSpec)
    head_dim = model_args.dim // model_args.attn_config.n_heads

    for layer_idx in range(num_layers):
        prefix = f"layers.{layer_idx}."

        ffw[f"{prefix}attn_norm.weight"] = fbw[f"{prefix}attention_norm.weight"]
        ffw[f"{prefix}attn_norm.bias"] = fbw[f"{prefix}attention_norm.bias"]

        ffw[f"{prefix}attn.qkv.weight"] = _make_w_qkv(
            fbw[f"{prefix}attention.wq.weight"],
            fbw[f"{prefix}attention.wk.weight"],
            fbw[f"{prefix}attention.wv.weight"],
            head_dim=head_dim,
        )
        ffw[f"{prefix}attn.qkv.bias"] = _make_b_qkv(
            fbw[f"{prefix}attention.wq.bias"],
            fbw[f"{prefix}attention.wk.bias"],
            fbw[f"{prefix}attention.wv.bias"],
            head_dim=head_dim,
        )

        ffw[f"{prefix}attn.out.weight"] = fbw[f"{prefix}attention.wo.weight"]
        ffw[f"{prefix}attn.out.bias"] = fbw[f"{prefix}attention.wo.bias"]

        ffw[f"{prefix}ffn_norm.weight"] = fbw[f"{prefix}ffn_norm.weight"]
        ffw[f"{prefix}ffn_norm.bias"] = fbw[f"{prefix}ffn_norm.bias"]

        ffw[f"{prefix}ffn.expand.weight"] = fbw[f"{prefix}feed_forward.w1.weight"]
        ffw[f"{prefix}ffn.expand.bias"] = fbw[f"{prefix}feed_forward.w1.bias"]

        ffw[f"{prefix}ffn.shrink.weight"] = fbw[f"{prefix}feed_forward.w2.weight"]
        ffw[f"{prefix}ffn.shrink.bias"] = fbw[f"{prefix}feed_forward.w2.bias"]

    ffw["final_norm.weight"] = fbw["norm.weight"]
    ffw["final_norm.bias"] = fbw["norm.bias"]

    ffw["score.weight"] = fbw["output.weight"]
    # ruff: noqa-end

    return ffw


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--inp-ckpt", type=str, help="The inp checkpoint path.")
    parser.add_argument("--out-ckpt", type=str, help="The out checkpoint path.")
    parser.add_argument(
        "--model-name", type=str, default="starcoder2-15b", help="The model name."
    )

    args = parser.parse_args()
    print(f"{args = }", flush=True)

    # Load potentially sharded FastBackward checkpoints
    inp_path = Path(args.inp_ckpt)
    ckpt_parts: list[dict[str, torch.Tensor]] = []
    for consolidated_file in sorted(inp_path.glob("consolidated.*.pth")):
        print(f"Loading {str(consolidated_file.absolute())}.", flush=True)
        ckpt_parts.append(torch.load(consolidated_file, map_location="cpu"))

    fbw_utils.clear_cuda_memory_cache()

    # Also load the ModelArgs
    print("Loading model_args.", flush=True)
    model_args_dict = read_json(inp_path / "params.json")
    model_args = ModelArgs.load_from_dict(model_args_dict)
    print(model_args.to_json(sort_keys=True, indent=2))

    # Merge multiple FastBackward ckpt_parts if needed
    if len(ckpt_parts) > 1:
        fbw_weights = utils.merge_model_parallel_consolidated_checkpoints(
            model_args,
            ckpt_parts,
        )
    else:
        fbw_weights = ckpt_parts[0]
    del ckpt_parts
    fbw_utils.clear_cuda_memory_cache()

    # convert
    print("Converting to FastForward.", flush=True)
    ffw_weights = convert_weights(model_args, fbw_weights)
    del fbw_weights
    fbw_utils.clear_cuda_memory_cache()

    # save
    out_path = Path(args.out_ckpt)
    print(f"Saving to {out_path}.", flush=True)
    manifest = save_load.save_weights(out_path, ffw_weights)

    print("Saved weights.", flush=True)
    print(f"Checkpoint sha256: {manifest.manifest_sha256.hex()}")

    # save model spec
    print("Saving model spec.", flush=True)

    class CustomEncoder(json.JSONEncoder):
        def default(self, o):
            if isinstance(o, SplitHeadModes):
                return o.value
            return super().default(o)

    model_spec = get_starcoder2_model_spec(
        model_name=args.model_name, checkpoint_path=out_path
    )
    model_spec.vocab_size = model_args.vocab_size
    model_spec.max_position_embeddings = model_args.max_seq_len
    model_spec.unscaled_max_position_embeddings = model_args.max_seq_len
    model_spec.checkpoint_sha256 = manifest.manifest_sha256.hex()
    assert model_args.rotary_config is not None
    assert model_args.rotary_config.ext_config is not None
    assert isinstance(model_args.rotary_config.ext_config, DeepSeekV1ExtensionConfig)
    model_spec.rotary_scaling_factor = (
        model_args.rotary_config.ext_config.rotary_scaling_factor
    )
    params_dict = json.dumps(
        dataclasses.asdict(model_spec), cls=CustomEncoder, indent=2, sort_keys=True
    )
    (out_path / "params.json").write_text(params_dict, "utf8")


if __name__ == "__main__":
    main()

"""Convert Starcoder-2 checkpoint from HuggingFace to fastbackward.

Usage:
    python research/tools/ckp_converter/hf2fbw_sc2.py
"""

import argparse
import glob
import json
from functools import partial
from pathlib import Path

import fairscale.nn.model_parallel as mpu
import torch
from safetensors.torch import load_file

from research.fastbackward.checkpointing.huggingface import (
    unpermute_qk_weights_from_hf_to_llama,
)
from research.fastbackward.checkpointing.utils import split_weight_for_model_parallel


def main(
    mp_world_size: int,
    hf_checkpoint_dir: Path,
    output_checkpoint_path: Path,
):
    assert hf_checkpoint_dir.exists()
    output_checkpoint_path.mkdir(parents=False, exist_ok=True)
    print(f"Create output checkpoint directory {output_checkpoint_path}")
    config_file = hf_checkpoint_dir / "config.json"
    with config_file.open(mode="r") as fh:
        cfg = json.load(fh)
    print(json.dumps(cfg, indent=2))

    params = {
        "dim": cfg["hidden_size"],
        "n_layers": cfg["num_hidden_layers"],
        "n_heads": cfg["num_attention_heads"],
        "n_kv_heads": cfg["num_key_value_heads"],
        "rope_theta": cfg["rope_theta"],
        "vocab_size": cfg["vocab_size"],
        "multiple_of": 256,
        "ffn_dim_multiplier": 1,
        "norm_eps": cfg["norm_epsilon"],
        "llama": False,
        "starcoder": True,
        # "rope_scaling_factor": 1.0
    }

    json.dump(params, open(output_checkpoint_path / "params.json", "w"), indent=2)

    nlayers = cfg["num_hidden_layers"]
    nheads = cfg["num_attention_heads"]
    hidden_dim = cfg["hidden_size"]
    headdim = hidden_dim // nheads

    for mp_rank in range(mp_world_size):
        model_pattern = hf_checkpoint_dir / "model*.safetensors"
        files = glob.glob(model_pattern.as_posix())
        sd = load_file(files[0])
        for f in files[1:]:
            sd.update(load_file(f))

        result_sd = {}
        if mp_world_size is None and mp_rank is None:
            mp_world_size = mpu.get_model_parallel_world_size()
            mp_rank = mpu.get_model_parallel_rank()
        else:
            assert (
                mp_world_size is not None and mp_rank is not None
            ), "Must set both or neither of mp_{world_size,rank}"

        mp_split_fn = partial(
            split_weight_for_model_parallel,
            world_size=mp_world_size,
            rank=mp_rank,
        )

        remaps = [
            ("model.embed_tokens.weight", "tok_embeddings.weight", 1),
            ("model.norm.weight", "norm.weight", None),
            ("model.norm.bias", "norm.bias", None),
        ]
        if "lm_head.weight" in sd:
            remaps.append(("lm_head.weight", "output.weight", 0))
        else:
            remaps.append(("model.embed_tokens.weight", "output.weight", 0))

        layer_remaps = [
            (
                "model.layers.{}.input_layernorm.weight",
                "layers.{}.attention_norm.weight",
                None,
            ),
            (
                "model.layers.{}.input_layernorm.bias",
                "layers.{}.attention_norm.bias",
                None,
            ),
            (
                "model.layers.{}.self_attn.q_proj.weight",
                "layers.{}.attention.wq.weight",
                0,
            ),
            ("model.layers.{}.self_attn.q_proj.bias", "layers.{}.attention.wq.bias", 0),
            (
                "model.layers.{}.self_attn.k_proj.weight",
                "layers.{}.attention.wk.weight",
                0,
            ),
            ("model.layers.{}.self_attn.k_proj.bias", "layers.{}.attention.wk.bias", 0),
            (
                "model.layers.{}.self_attn.v_proj.weight",
                "layers.{}.attention.wv.weight",
                0,
            ),
            ("model.layers.{}.self_attn.v_proj.bias", "layers.{}.attention.wv.bias", 0),
            (
                "model.layers.{}.self_attn.o_proj.weight",
                "layers.{}.attention.wo.weight",
                1,
            ),
            (
                "model.layers.{}.self_attn.o_proj.bias",
                "layers.{}.attention.wo.bias",
                None,
            ),
            (
                "model.layers.{}.post_attention_layernorm.weight",
                "layers.{}.ffn_norm.weight",
                None,
            ),
            (
                "model.layers.{}.post_attention_layernorm.bias",
                "layers.{}.ffn_norm.bias",
                None,
            ),
            ("model.layers.{}.mlp.c_fc.weight", "layers.{}.feed_forward.w1.weight", 0),
            ("model.layers.{}.mlp.c_fc.bias", "layers.{}.feed_forward.w1.bias", 0),
            (
                "model.layers.{}.mlp.c_proj.weight",
                "layers.{}.feed_forward.w2.weight",
                1,
            ),
            ("model.layers.{}.mlp.c_proj.bias", "layers.{}.feed_forward.w2.bias", None),
        ]

        for src, dst, split_dim in remaps:
            result = sd[src]
            result_sd[dst] = mp_split_fn(result, split_dim)
            if src != "model.embed_tokens.weight":
                del sd[src]

        del sd["model.embed_tokens.weight"]

        for layernum in range(nlayers):
            for src_fmt, dst_fmt, split_dim in layer_remaps:
                src = src_fmt.format(layernum)
                dst = dst_fmt.format(layernum)
                result = sd[src]
                if src.endswith("q_proj.weight"):
                    result = unpermute_qk_weights_from_hf_to_llama(
                        result, params["n_heads"], headdim, params["dim"]
                    )
                elif src.endswith("q_proj.bias"):
                    result = unpermute_qk_weights_from_hf_to_llama(
                        result, params["n_heads"], headdim, 1
                    )
                elif src.endswith("k_proj.weight"):
                    result = unpermute_qk_weights_from_hf_to_llama(
                        result, params["n_kv_heads"], headdim, params["dim"]
                    )
                elif src.endswith("k_proj.bias"):
                    result = unpermute_qk_weights_from_hf_to_llama(
                        result, params["n_kv_heads"], headdim, 1
                    )

                result_sd[dst] = mp_split_fn(result, split_dim)
                del sd[src]

        assert len(sd) == 0
        torch.save(
            result_sd, output_checkpoint_path / f"consolidated.{mp_rank:02d}.pth"
        )


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--mp_world_size",
        type=int,
        default=4,
        help="The model parallel size of the checkpoint, if not specified it will be assumed to be 4 for 15B models.",
    )
    parser.add_argument(
        "--hf_checkpoint_dir",
        type=Path,
        default=Path("/mnt/efs/augment/checkpoints/starcoder2-15b/"),
        help="Path to directory containing huggingface checkpoint",
    )
    parser.add_argument(
        "--output_checkpoint_path",
        type=Path,
        default=Path("/mnt/efs/augment/checkpoints/starcoder2-15b-fb-mp4/"),
        help="Path to directory where to store fastbackward checkpoint",
    )
    args = parser.parse_args()
    main(args.hf_checkpoint_dir, args.output_checkpoint_path, args.mp_world_size)

#!/usr/bin/env python3
"""Batch conversion script for converting multiple Qwen3 models from HuggingFace to FastBackward format.

This script automates the conversion of multiple Qwen3 model sizes with different model parallelism
configurations by calling the existing hf2fbw_qwen3.py conversion script. The conversion script
handles model parallelism internally as a single-process operation.

This is the recommended way to convert Qwen3 models, as it provides better error handling,
detailed logging, and support for converting multiple model sizes and parallelism configurations
in a single command.

Usage:
    # Convert all supported model sizes and mp_size combinations
    python research/tools/ckp_converter/batch_hf2fbw_qwen3.py \
        --input_base_path /mnt/efs/augment/checkpoints/qwen3/hf \
        --output_base_path /mnt/efs/augment/checkpoints/qwen3/fbw

    # Convert only specific model sizes
    python research/tools/ckp_converter/batch_hf2fbw_qwen3.py \
        --input_base_path /mnt/efs/augment/checkpoints/qwen3/hf \
        --output_base_path /mnt/efs/augment/checkpoints/qwen3/fbw \
        --model_sizes 1.7b 4b

    # Run with custom model name patterns
    python research/tools/ckp_converter/batch_hf2fbw_qwen3.py \
        --input_base_path /mnt/efs/augment/checkpoints/qwen3/hf \
        --output_base_path /mnt/efs/augment/checkpoints/qwen3/fbw \
        --model_name_pattern 1.7b "Qwen3-1.7B-Custom"

    # Dry run to see what would be converted
    python research/tools/ckp_converter/batch_hf2fbw_qwen3.py \
        --input_base_path /mnt/efs/augment/checkpoints/qwen3/hf \
        --output_base_path /mnt/efs/augment/checkpoints/qwen3/fbw \
        --dry_run

Note: Conversions run sequentially (one at a time) to avoid GPU resource conflicts.
Each conversion may use multiple GPUs based on the mp_size parameter:
- mp_size=1: Uses 1 GPU
- mp_size=2: Uses 2 GPUs
- mp_size=4: Uses 4 GPUs
- mp_size=8: Uses 8 GPUs
"""

import argparse
import logging
import subprocess
import sys
from pathlib import Path
import time


# Model size to mp_size mapping based on requirements
MODEL_CONFIGS: dict[str, list[int]] = {
    "0.6b": [1],
    "1.7b": [1, 2],
    "4b": [1, 2],
    "8b": [1, 2, 4],
    "14b": [2, 4],
    "32b": [4, 8],
}

# Default model name patterns for input paths
DEFAULT_MODEL_NAME_PATTERNS = {
    "0.6b": "Qwen3-0.6B",
    "1.7b": "Qwen3-1.7B",
    "4b": "Qwen3-4B",
    "8b": "Qwen3-8B",
    "14b": "Qwen3-14B",
    "32b": "Qwen3-32B",
}


def setup_logging(log_level: str = "INFO") -> logging.Logger:
    """Set up logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler("batch_qwen3_conversion.log"),
        ],
    )
    return logging.getLogger(__name__)


def format_duration(duration: float) -> str:
    """Format duration in a human-readable way."""
    if duration < 60:
        return f"{duration:.1f}s"
    elif duration < 3600:
        minutes = int(duration // 60)
        seconds = duration % 60
        return f"{minutes}m {seconds:.1f}s"
    else:
        hours = int(duration // 3600)
        minutes = int((duration % 3600) // 60)
        seconds = duration % 60
        return f"{hours}h {minutes}m {seconds:.1f}s"


def get_conversion_tasks(
    model_sizes: list[str],
    input_base_path: Path,
    output_base_path: Path,
    model_name_patterns: dict[str, str],
) -> list[tuple[str, int, Path, Path]]:
    """Generate list of conversion tasks.

    Returns:
        List of tuples: (model_size, mp_size, input_path, output_path)
    """
    tasks = []

    for model_size in model_sizes:
        if model_size not in MODEL_CONFIGS:
            logging.warning(f"Unknown model size: {model_size}. Skipping.")
            continue

        model_name = model_name_patterns.get(model_size, f"Qwen3-{model_size.upper()}")
        input_path = input_base_path / model_name

        for mp_size in MODEL_CONFIGS[model_size]:
            # Create output path with mp_size suffix for mp > 1
            if mp_size == 1:
                output_path = output_base_path / f"{model_name}-fbw"
            else:
                output_path = output_base_path / f"{model_name}-fbw-mp{mp_size}"

            tasks.append((model_size, mp_size, input_path, output_path))

    return tasks


def run_conversion(
    model_size: str,
    mp_size: int,
    input_path: Path,
    output_path: Path,
    dry_run: bool = False,
) -> bool:
    """Run a single conversion task.

    Returns:
        True if successful, False otherwise
    """
    logger = logging.getLogger(__name__)

    # Construct command
    cmd = [
        sys.executable,
        "research/tools/ckp_converter/hf2fbw_qwen3.py",
        "--input_path",
        str(input_path),
        "--output_path",
        str(output_path),
        "--size",
        model_size,
        "--mp_size",
        str(mp_size),
    ]

    logger.info(f"🔄 Starting conversion: {model_size} (mp_size={mp_size})")
    logger.info(f"   Input:  {input_path}")
    logger.info(f"   Output: {output_path}")
    logger.info(f"   Command: {' '.join(cmd)}")

    if dry_run:
        logger.info("   DRY RUN: Would execute the above command")
        return True

    # Check if input path exists
    if not input_path.exists():
        logger.error(f"❌ Input path does not exist: {input_path}")
        return False

    # Run conversion with detailed timing
    overall_start_time = time.time()
    logger.info(
        f"⏱️  Conversion started at {time.strftime('%H:%M:%S', time.localtime(overall_start_time))}"
    )

    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=3600,  # 1 hour timeout
        )

        overall_duration = time.time() - overall_start_time
        end_time_str = time.strftime("%H:%M:%S", time.localtime())
        duration_str = format_duration(overall_duration)

        if result.returncode == 0:
            logger.info(f"✅ Successfully converted {model_size} (mp_size={mp_size})")
            logger.info(f"   Duration: {duration_str} (finished at {end_time_str})")
            if result.stdout:
                logger.debug(f"STDOUT:\n{result.stdout}")
            return True
        else:
            logger.error(f"❌ Failed to convert {model_size} (mp_size={mp_size})")
            logger.error(f"   Duration: {duration_str} (failed at {end_time_str})")
            logger.error(f"   Return code: {result.returncode}")
            if result.stdout:
                logger.error(f"STDOUT:\n{result.stdout}")
            if result.stderr:
                logger.error(f"STDERR:\n{result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        timeout_duration = time.time() - overall_start_time
        logger.error(
            f"❌ Conversion timed out for {model_size} (mp_size={mp_size}) after {timeout_duration/60:.1f} minutes"
        )
        return False
    except Exception as e:
        error_duration = time.time() - overall_start_time
        logger.error(
            f"❌ Unexpected error converting {model_size} (mp_size={mp_size}) after {error_duration:.1f}s: {e}"
        )
        return False


def main():
    """Main function for batch conversion."""
    parser = argparse.ArgumentParser(
        description="Batch convert Qwen3 models from HuggingFace to FastBackward format",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__,
    )

    parser.add_argument(
        "--input_base_path",
        type=Path,
        required=True,
        help="Base path containing HuggingFace model directories",
    )

    parser.add_argument(
        "--output_base_path",
        type=Path,
        required=True,
        help="Base path where FastBackward models will be saved",
    )

    parser.add_argument(
        "--model_sizes",
        nargs="*",
        choices=list(MODEL_CONFIGS.keys()),
        default=list(MODEL_CONFIGS.keys()),
        help="Model sizes to convert (default: all supported sizes)",
    )

    parser.add_argument(
        "--dry_run",
        action="store_true",
        help="Show what would be converted without actually running conversions",
    )

    parser.add_argument(
        "--log_level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)",
    )

    parser.add_argument(
        "--model_name_pattern",
        action="append",
        nargs=2,
        metavar=("SIZE", "NAME"),
        help="Override model name pattern for a specific size (e.g., --model_name_pattern 1.7b Qwen3-1.7B-Custom)",
    )

    args = parser.parse_args()

    # Set up logging
    logger = setup_logging(args.log_level)

    # Override model name patterns if provided
    model_name_patterns = DEFAULT_MODEL_NAME_PATTERNS.copy()
    if args.model_name_pattern:
        for size, name in args.model_name_pattern:
            if size in MODEL_CONFIGS:
                model_name_patterns[size] = name
                logger.info(f"Using custom model name for {size}: {name}")
            else:
                logger.warning(f"Unknown model size in pattern override: {size}")

    # Generate conversion tasks
    tasks = get_conversion_tasks(
        args.model_sizes,
        args.input_base_path,
        args.output_base_path,
        model_name_patterns,
    )

    if not tasks:
        logger.error("No conversion tasks generated. Check your input parameters.")
        sys.exit(1)

    logger.info(f"Generated {len(tasks)} conversion tasks")

    # Show resource planning information
    max_gpu_usage = max(mp_size for _, mp_size, _, _ in tasks)

    logger.info("\n🔧 Resource Planning:")
    logger.info(f"   Max GPUs needed per conversion: {max_gpu_usage}")

    # Show summary
    logger.info("\n📋 Conversion plan:")
    for i, (model_size, mp_size, input_path, output_path) in enumerate(tasks, 1):
        logger.info(f"  {i:2d}. {model_size} (mp_size={mp_size})")
        logger.info(f"      Input:  {input_path}")
        logger.info(f"      Output: {output_path}")

    if args.dry_run:
        logger.info("\n🧪 DRY RUN: No actual conversions will be performed")

    # Conversions always run sequentially
    logger.info("\n🔄 Running conversions sequentially (one at a time)")

    # Run conversions
    successful = 0
    failed = 0
    failed_tasks = []
    task_timings = []

    batch_start_time = time.time()
    logger.info(
        f"\n🚀 Starting batch conversion at {time.strftime('%H:%M:%S', time.localtime(batch_start_time))}"
    )

    for i, (model_size, mp_size, input_path, output_path) in enumerate(tasks, 1):
        logger.info(f"\n{'='*60}")
        logger.info(
            f"📋 Task {i}/{len(tasks)}: Converting {model_size} (mp_size={mp_size})"
        )
        logger.info(f"{'='*60}")

        task_start_time = time.time()
        success = run_conversion(
            model_size=model_size,
            mp_size=mp_size,
            input_path=input_path,
            output_path=output_path,
            dry_run=args.dry_run,
        )
        task_duration = time.time() - task_start_time

        # Store timing information
        task_timings.append(
            {
                "model_size": model_size,
                "mp_size": mp_size,
                "duration": task_duration,
                "success": success,
            }
        )

        if success:
            successful += 1
            logger.info(
                f"📊 Progress: {successful}/{len(tasks)} completed successfully"
            )
        else:
            failed += 1
            failed_tasks.append((model_size, mp_size, input_path, output_path))
            logger.error(
                f"📊 Progress: {successful}/{len(tasks)} completed, {failed} failed"
            )

        # Show estimated time remaining
        if i < len(tasks):
            avg_time_per_task = (time.time() - batch_start_time) / i
            remaining_tasks = len(tasks) - i
            estimated_remaining = avg_time_per_task * remaining_tasks
            eta_str = format_duration(estimated_remaining)

            logger.info(
                f"⏳ Estimated time remaining: {eta_str} ({remaining_tasks} tasks left)"
            )

    # Final summary
    total_time = time.time() - batch_start_time
    end_time_str = time.strftime("%H:%M:%S", time.localtime())
    total_duration_str = format_duration(total_time)

    logger.info(f"\n{'='*60}")
    logger.info("📊 BATCH CONVERSION SUMMARY")
    logger.info(f"{'='*60}")
    logger.info(f"Total tasks: {len(tasks)}")
    logger.info(f"Successful: {successful}")
    logger.info(f"Failed: {failed}")
    logger.info(f"Total time: {total_duration_str} (finished at {end_time_str})")

    # Show timing breakdown for successful conversions
    if task_timings and not args.dry_run:
        successful_timings = [t for t in task_timings if t["success"]]
        if successful_timings:
            avg_time = sum(t["duration"] for t in successful_timings) / len(
                successful_timings
            )
            min_time = min(t["duration"] for t in successful_timings)
            max_time = max(t["duration"] for t in successful_timings)

            logger.info("\n⏱️  Timing statistics for successful conversions:")
            logger.info(f"   Average: {format_duration(avg_time)}")
            logger.info(f"   Fastest: {format_duration(min_time)}")
            logger.info(f"   Slowest: {format_duration(max_time)}")

            # Show per-model timing breakdown
            logger.info("\n📈 Per-task timing breakdown:")
            for timing in task_timings:
                status = "✅" if timing["success"] else "❌"
                logger.info(
                    f"   {status} {timing['model_size']} (mp_size={timing['mp_size']}): {format_duration(timing['duration'])}"
                )

    if failed_tasks:
        logger.error("\n❌ Failed conversions:")
        for model_size, mp_size, input_path, output_path in failed_tasks:
            logger.error(f"   - {model_size} (mp_size={mp_size}): {input_path}")

    if failed > 0:
        logger.error(
            f"\n❌ {failed} conversion(s) failed. Check the logs above for details."
        )
        sys.exit(1)
    else:
        logger.info(f"\n✅ All {successful} conversion(s) completed successfully!")


if __name__ == "__main__":
    main()

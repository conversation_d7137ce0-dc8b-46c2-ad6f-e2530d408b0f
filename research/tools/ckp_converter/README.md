# Checkpoint Converter

This folder maintains scripts to help us convert checkpoint between different formats, such as:

- HuggingFace to FastForward
- HuggingFace to FastBackward
- FastBackward to FastForward

Most of these scripts take an input checkpoint directory and an output checkpoint directory as arguments (see their detailed argument key in each script) and will then automatically convert the checkpoint for you.
Some scripts support additional argument of model parallel size, which can help you shard the checkpoint for FastForward or FastBackward. Additional arguments may be needed for some scripts. Please take a look at each script's docstring and argument list for more details.

## Individual Conversion Scripts

- `hf2fbw_qwen3.py` - Convert Qwen3 models from HuggingFace to FastBackward format

Example usage:
```bash
# Convert a Qwen3 model with model parallelism
python research/tools/ckp_converter/hf2fbw_qwen3.py \
    --input_path /path/to/huggingface/model \
    --output_path /path/to/output/directory \
    --size 8b \
    --mp_size 4
```

**Note**: For Qwen3 models, it's recommended to use the batch conversion script (`batch_hf2fbw_qwen3.py`) instead of the individual script, as it provides better error handling, detailed logging, and support for converting multiple model sizes and parallelism configurations in a single command.

## Batch Conversion Scripts

Batch conversion scripts are available to automate the conversion process for multiple models or model configurations:

- `batch_hf2fbw_qwen3.py` - **Recommended** - Batch convert multiple Qwen3 models from HuggingFace to FastBackward format with different model parallelism sizes

Example usage:
```bash
# Convert all Qwen3 model sizes with all supported mp_size combinations
python research/tools/ckp_converter/batch_hf2fbw_qwen3.py \
    --input_base_path /mnt/efs/augment/checkpoints/qwen3/hf \
    --output_base_path /mnt/efs/augment/checkpoints/qwen3/fbw

# Convert only specific model sizes
python research/tools/ckp_converter/batch_hf2fbw_qwen3.py \
    --input_base_path /mnt/efs/augment/checkpoints/qwen3/hf \
    --output_base_path /mnt/efs/augment/checkpoints/qwen3/fbw \
    --model_sizes 8b 14b

# Use custom model name patterns
python research/tools/ckp_converter/batch_hf2fbw_qwen3.py \
    --input_base_path /mnt/efs/augment/checkpoints/qwen3/hf \
    --output_base_path /mnt/efs/augment/checkpoints/qwen3/fbw \
    --model_name_pattern 1.7b "Qwen3-1.7B-Custom"

# Dry run to see what would be converted
python research/tools/ckp_converter/batch_hf2fbw_qwen3.py \
    --input_base_path /mnt/efs/augment/checkpoints/qwen3/hf \
    --output_base_path /mnt/efs/augment/checkpoints/qwen3/fbw \
    --dry_run
```

**Resource Management**: The batch script runs conversions sequentially to avoid GPU resource conflicts. Each conversion uses multiple GPUs based on the `mp_size` parameter (1, 2, 4, or 8 GPUs). The script provides resource planning information to help you understand the GPU requirements for your conversion tasks. For example, a conversion with `mp_size=4` will require 4 GPUs.

Rule of thumb:

- These scripts aim to be functionally correct, but not necessarily optimized w.r.t. performance or code quality.
- These scripts should contain a useful guideline for how to use them in the file docstrings.

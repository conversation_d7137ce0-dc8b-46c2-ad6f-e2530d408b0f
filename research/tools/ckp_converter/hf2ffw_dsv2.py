"""Load the HuggingFace format of DeepSeek-Coder-V2-Lite and convert it to the FastForward checkpoint format.

python research/tools/ckp_converter/hf2ffw_dsv2.py \
    --input_ckpt_dir /mnt/efs/augment/checkpoints/dxy/deepseek-ai/DeepSeek-Coder-V2-Lite-Instruct \
    --output_ckpt_dir /mnt/efs/augment/checkpoints/dxy/deepseek-ai/DeepSeek-Coder-V2-Lite-Instruct-ffw
    --spec deepseek-coder-v2-lite
"""

import argparse
import itertools
import pathlib
import re
from typing import Iterable, TypeVar

import torch
import tqdm
from safetensors import safe_open
from torch._subclasses.fake_tensor import FakeTensorMode

from base.fastforward.checkpoints.impl import save_load_v2, sharding
from base.fastforward.deepseek_v2.fwd_dsv2 import DeepSeekV2, shard_args_by_name
from base.fastforward.deepseek_v2.model_specs import DeepSeekV2ModelSpec, get_model_spec
from research.core import utils_for_file, utils_for_log


class HFCheckpoint:
    """A class to load the DeepSeek-Coder-V2 HuggingFace checkpoint."""

    def __init__(self, ckp_dir: pathlib.Path):
        self.ckp_dir = ckp_dir
        assert self.ckp_dir.exists()
        self.config_json = self.ckp_dir / "config.json"
        assert self.config_json.exists()
        self.config = utils_for_file.read_json(self.config_json)
        # read the hyper-parameters
        self.hidden_size = self.config["hidden_size"]
        self.max_position_embeddings = self.config["max_position_embeddings"]
        self.num_hidden_layers = self.config["num_hidden_layers"]
        self.n_routed_experts = self.config["n_routed_experts"]
        # load the model safetensor index
        self.safetensor_index = utils_for_file.read_json(
            self.ckp_dir / "model.safetensors.index.json"
        )
        self.weight_map = self.safetensor_index["weight_map"]
        # create a weight buffer
        self.weight_buffer = {}

    def load_all_weights(self):
        all_safetensor_paths: set[pathlib.Path] = set()
        required_weight_names = []
        for weight_name, safetensor_filename in self.weight_map.items():
            safetensor_path: pathlib.Path = self.ckp_dir / safetensor_filename
            assert safetensor_path.exists(), f"{safetensor_path} does not exist."
            all_safetensor_paths.add(safetensor_path)
            required_weight_names.append(weight_name)
        for safetensor_path in tqdm.tqdm(
            all_safetensor_paths,
            desc="Loading weights from ckp",
            total=len(all_safetensor_paths),
        ):
            with safe_open(safetensor_path, framework="pt", device="cpu") as f:  # type: ignore
                for key in f.keys():
                    if key in self.weight_buffer:
                        print(f"Weight {key} already in the buffer skip.")
                        continue
                    self.weight_buffer[key] = f.get_tensor(key)
        for weight_name in required_weight_names:
            if weight_name not in self.weight_buffer:
                print(f"Still missing {weight_name}!!!!!!!!")
        print(f"There are {len(self.weight_buffer)} weight tensors in the buffer.")
        counts = 0
        for key, value in self.weight_buffer.items():
            counts += value.numel()
        print(f"There are {counts} weights in the buffer.")

    def show_weight_names(self, re_pattern: re.Pattern | None = None):
        avaliable_weight_names = []
        for weight_name in self.weight_map.keys():
            if re_pattern is None or re_pattern.match(weight_name):
                avaliable_weight_names.append(weight_name)
        print(avaliable_weight_names)

    def convert_to_ffw_state_dict(
        self, model_spec: DeepSeekV2ModelSpec
    ) -> dict[str, torch.Tensor]:
        assert self.num_hidden_layers == model_spec.num_layers
        assert self.n_routed_experts == model_spec.moe.n_routed_experts
        weights: dict[str, torch.Tensor] = {}
        weights["embs.word_embs"] = self.weight_buffer["model.embed_tokens.weight"]
        weights["final_layer_norm.weight"] = self.weight_buffer["model.norm.weight"]
        weights["score.weight"] = self.weight_buffer["lm_head.weight"]
        for ilayer in range(self.num_hidden_layers):
            # Convert the attention layer
            # fmt: off
            if model_spec.attention.q_lora_rank is None:
                weights[f"layers.{ilayer}.attn.q_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.q_proj.weight"]
            else:
                weights[f"layers.{ilayer}.attn.q_a_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.q_a_proj.weight"]
                weights[f"layers.{ilayer}.attn.q_a_layernorm.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.q_a_layernorm.weight"]
                weights[f"layers.{ilayer}.attn.q_b_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.q_b_proj.weight"]
            weights[f"layers.{ilayer}.attn.kv_a_proj_with_mqa.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.kv_a_proj_with_mqa.weight"]
            weights[f"layers.{ilayer}.attn.kv_a_layernorm.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.kv_a_layernorm.weight"]
            weights[f"layers.{ilayer}.attn.kv_b_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.kv_b_proj.weight"]
            weights[f"layers.{ilayer}.attn.o_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.o_proj.weight"]
            # Handle the MLP or MoE layer
            if ilayer == 0:
                weights[f"layers.{ilayer}.ffn.gate_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.gate_proj.weight"]
                weights[f"layers.{ilayer}.ffn.up_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.up_proj.weight"]
                weights[f"layers.{ilayer}.ffn.down_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.down_proj.weight"]
            else:
                weights[f"layers.{ilayer}.ffn.gate_weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.gate.weight"]
                weights[f"layers.{ilayer}.ffn.shared_experts.gate_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.shared_experts.gate_proj.weight"]
                weights[f"layers.{ilayer}.ffn.shared_experts.up_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.shared_experts.up_proj.weight"]
                weights[f"layers.{ilayer}.ffn.shared_experts.down_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.shared_experts.down_proj.weight"]
                for iexpert in range(self.n_routed_experts):
                    weights[f"layers.{ilayer}.ffn.routed_experts.experts.{iexpert}.gate_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.experts.{iexpert}.gate_proj.weight"]
                    weights[f"layers.{ilayer}.ffn.routed_experts.experts.{iexpert}.up_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.experts.{iexpert}.up_proj.weight"]
                    weights[f"layers.{ilayer}.ffn.routed_experts.experts.{iexpert}.down_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.experts.{iexpert}.down_proj.weight"]
            # Convert the RMS norm
            weights[f"layers.{ilayer}.attn_norm.weight"] = self.weight_buffer[f"model.layers.{ilayer}.input_layernorm.weight"]
            weights[f"layers.{ilayer}.ffn_norm.weight"] = self.weight_buffer[f"model.layers.{ilayer}.post_attention_layernorm.weight"]
            # fmt: on
        return weights


T = TypeVar("T")


def batched(iterable: Iterable[T], count: int) -> Iterable[Iterable[T]]:
    it = iter(iterable)
    while batch := tuple(itertools.islice(it, count)):
        yield batch


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script to convert DeepSeek Coder V2 checkpoint from huggingface to fastforward format.",
    )
    parser.add_argument(
        "--input-ckpt-dir",
        type=pathlib.Path,
        required=True,
        help="Path to directory containing huggingface checkpoint",
    )
    parser.add_argument(
        "--output-ckpt-dir",
        type=pathlib.Path,
        required=True,
        help="Path to directory where to store fastforward checkpoint",
    )
    parser.add_argument(
        "--spec",
        type=str,
        choices=["deepseek-coder-v2-lite", "deepseek-coder-v2"],
    )
    parser.add_argument(
        "--shard-for-processes",
        type=int,
        default=1,
        help="Shard the weights for the specified number of processes",
    )
    args = parser.parse_args()
    output_ckpt_dir = args.output_ckpt_dir

    ckp_manager = HFCheckpoint(args.input_ckpt_dir)
    ckp_manager.load_all_weights()
    print(f"{utils_for_log.time_string()} Finish loading the checkpoint.")

    model_spec = get_model_spec(args.spec)
    state_dict = ckp_manager.convert_to_ffw_state_dict(model_spec)
    model = None
    with FakeTensorMode(allow_non_fake_inputs=True):
        model = DeepSeekV2(
            ms=model_spec,
            dtype=torch.bfloat16,
            device="cuda",
            process_idx=0,
            num_processes=1,
        )
        model.load_state_dict(state_dict, strict=True)
        print(
            f"{utils_for_log.time_string()} Finish validating weight names and shapes."
        )

    shard_args = {}
    if args.shard_for_processes > 1:
        load_args = shard_args_by_name(
            process_idx=0, num_processes=args.shard_for_processes
        )
        shard_args = {
            k: sharding.ShardSaveArgs.from_load_args(v) for k, v in load_args.items()
        }

    manifest = None
    for batch in batched(state_dict.items(), 10000):
        manifest = save_load_v2.save_weights(
            output_ckpt_dir,
            dict(batch),
            incremental=True,
            name_to_shard_args=shard_args,
        )
    assert manifest is not None
    print(
        f"{utils_for_log.time_string()} Finish saving the fastforward checkpoint "
        f"into {output_ckpt_dir} sha={manifest.content_sha256.hex()}."
    )

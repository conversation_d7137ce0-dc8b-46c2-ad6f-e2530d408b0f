"""Utilities to support augment_trial in determined.

Heavily borrowed from
https://github.com/determined-ai/determined/blob/master/examples/deepspeed/gpt_neox/det_utils.py
"""

import base64
import csv
import hashlib
import itertools
import json
import logging
import os
import socket
import shutil
import subprocess
import sys
import tempfile
import time
from pathlib import Path
from typing import Any, List, Optional

import numpy as np
import requests
import torch
import torch.distributed
import wandb
import yaml
from attrdict import AttrMap
from determined import get_cluster_info  # type: ignore
from determined.experimental import client as det_client  # type: ignore
from determined.pytorch import MetricReducer, PyTorchCallback  # type: ignore
from google.cloud.storage import Client, Blob, Bucket, transfer_manager
from megatron.memorize.memory_context import MemoryAction, MemoryContext
from megatron.neox_arguments import NeoXArgs
from megatron.utils import print_rank_0, wandb_log

from research.core.constants import AUGMENT_EFS_ROOT, AUGMENT_CHECKPOINTS_ROOT

EVAL_DIR = "/run/determined/workdir/research/eval"
EVAL_CONFIG_DIR = f"{EVAL_DIR}/configs"
DET_BINARY = "/opt/conda/bin/det"


def process_parsed_deepy_args(args_parsed, overwrite_values):
    """Apply NeoXArgs.consume_deepy_args() logic to an already-parsed arg list."""
    # load config files
    conf_files = args_parsed.conf_file
    if args_parsed.conf_dir:
        conf_files = (Path(args_parsed.conf_dir) / f for f in conf_files)
    else:
        conf_files = (Path(f) for f in conf_files)

    # enables us to pass in `small` instead of `small.yml`
    conf_files = (
        (cf if cf.suffix == ".yml" else cf.parent / (cf.name + ".yml"))
        for cf in conf_files
    )

    neox_args = NeoXArgs.from_ymls(
        paths_to_yml_files=conf_files,  # type: ignore
        overwrite_values=overwrite_values,
    )

    logging.info("neox_args created")
    neox_args.print()

    return neox_args


def get_augment_args():
    """Pull the augment args all the augment specific args from the hparams."""
    return get_cluster_info().user_data.get("augment", {})  # type: ignore


def get_neox_args(context):
    """Process all the args to feed neox."""
    args = AttrMap(context.get_hparams())
    exp_config = context.get_experiment_config()

    # Gather overrides.
    overwrite_values = get_augment_args().get("overwrite_values", {})
    # We are going to overwrite certain neox_args with determined config values
    # from the experiment config to ensure consistency.
    for key, value in overwrite_values.items():
        new_value = _expand_overwrite_values(value)
        if new_value != value:
            print(f"overwrite_values.{key} expanded to {new_value}")
            overwrite_values[key] = new_value

    assert (
        "batches" in exp_config["searcher"]["max_length"]
    ), "Please specify max_length in batches."
    assert (
        "batches" in exp_config["min_validation_period"]
    ), "Please specify min_validation_period in batches."

    overwrite_values.update(
        {
            "train_iters": exp_config["searcher"]["max_length"]["batches"],
            "save_interval": exp_config["min_checkpoint_period"]["batches"],
            "eval_interval": exp_config["min_validation_period"]["batches"],
            "hostfile": os.environ.get("DET_DEEPSPEED_HOSTFILE_PATH"),
            # "seed": context.env.trial_seed,
        }
    )

    # Build neox args.
    neox_args = process_parsed_deepy_args(args, overwrite_values=overwrite_values)
    return neox_args


def sync_from_s3(url: str, rank: int) -> str:
    """Sync an S3 URL to the shared filesystem."""
    raise NotImplementedError("Sync from S3 not implemented.")


def bucket_for_augment_fs_path(fs_path: Path | str) -> tuple[str, str]:
    """Return the bucket name and remaining path for a given augment filesystem path.

    This is used to guess where in gcs we can find the path the user is looking for.
    """
    fs_path = Path(fs_path).resolve()
    if not fs_path.is_relative_to("/mnt/efs/"):
        raise ValueError(f"Path {fs_path} is not under /mnt/efs/")
    if fs_path.relative_to("/mnt/efs").parts[0] == "spark-data":
        return "gcp-us1-spark-data", str(fs_path.relative_to("/mnt/efs/spark-data"))
    else:
        if not fs_path.is_relative_to("/mnt/efs/augment"):
            raise ValueError(f"Path {fs_path} is not under /mnt/efs/augment")
        lpart = Path(*fs_path.relative_to("/mnt/efs/augment").parts[1:])
        return (
            f"gcp-us1-{fs_path.relative_to('/mnt/efs/augment').parts[0].replace('_', '-')}",
            lpart.as_posix(),
        )


def augment_fs_path_for_bucket(bucket_name: str) -> Path:
    """Return the path to the bucket on the shared filesystem."""
    # Sync locally if it doesn't correspond to a shared FS
    if not bucket_name.startswith("gcp-us1-"):
        base_path = Path("/run/determined/sync")
    else:
        base_path = Path("/mnt/efs")
        if bucket_name == "gcp-us1-spark-data":
            base_path = base_path / "spark-data"
        else:
            first_part = bucket_name.replace("gcp-us1-", "").replace("-", "_")
            base_path = base_path / "augment" / first_part
    return base_path


# TODO(marcmac) this needs a rewrite pass so we only ever
# call it once, with the full list of URLs, and only call
# transfer_manager.download_many() once, for improved performance.
def sync_from_gcs(urls: List[str], rank: int) -> List[str]:
    """Sync a GCS URL to the shared or local filesystem.

    If the url is a "directory" in GCS, sync the whole thing.
    If the url is a "file" in GCS, sync it.
    If the url does not exist in GCS, try syncing URL* (this is for the data_path arg)

    Special case for gs://gcp-us1-spark-data as the only one not under /mnt/efs/augment
    """
    dest_paths = []
    downloads = []
    st = time.time()
    client = Client(project="augment-research-gsc")

    for url in urls:
        if not url.startswith("gs://"):
            dest_paths.append(url)
            continue
        print(f"GCS Sync from {url}")
        url = url.replace("gs://", "")
        bucket_name = url.split("/")[0]
        bucket_path = url.split("/", 1)[-1]
        if bucket_path == bucket_name or bucket_path == "":
            raise ValueError(f"Can't sync entire {url} bucket")

        dest_base = augment_fs_path_for_bucket(bucket_name)
        dest_location = dest_base / bucket_path

        if dest_location.is_relative_to("/mnt/efs"):
            # only sync shared filesystem on rank 0
            if rank != 0:
                dest_paths.append(dest_location.as_posix())
                continue
        dest_paths.append(dest_location.as_posix())

        bucket = client.get_bucket(bucket_name)
        blob_list = list(bucket.list_blobs(prefix=bucket_path))
        dest_list = [dest_base / blob.name for blob in blob_list]
        for src, dest in zip(blob_list, dest_list):
            dest_size = None
            if dest.exists():
                dest_size = dest.stat().st_size
            if src.size == dest_size:
                dest_hash = base64.b64encode(
                    hashlib.md5(dest.read_bytes()).digest()
                ).decode()
                if dest_hash == src.md5_hash:
                    print(
                        f"GCS Sync from gs://{bucket_name}/{src.name} to {dest} (already exists and matches)"
                    )
                    continue
            print(
                f"GCS Sync from gs://{bucket_name}/{src.name} to {dest} {src.size} {dest_size}"
            )
            dest.parent.mkdir(parents=True, exist_ok=True)
            downloads.append((src, str(dest)))

    print(f"GCS Sync processing {len(downloads)} entries.")
    dl_results = transfer_manager.download_many(downloads, deadline=1200)
    print(f"GCS Sync processed {len(dl_results)} entries.")
    exceptions = [result for result in dl_results if isinstance(result, Exception)]
    for exc in exceptions:
        print(f"Failed to download from GCS: {exc}")

    dt = time.time() - st
    print(f"GCS Sync completed in {dt:.2f} seconds")
    return dest_paths


def remove_sync_flag_file():
    """Clean up the sync flag."""
    task_id = os.environ.get("DET_TASK_ID", None)
    assert task_id is not None, "Must be running in a determined job"
    sync_flag_file = AUGMENT_CHECKPOINTS_ROOT / "sync_flags" / f"{task_id}"
    logging.warning(f"Removing the sync flag {sync_flag_file}")
    if sync_flag_file.exists():
        sync_flag_file.unlink()


def sync_all_to_local(local_paths: List[str], rank: int):
    """Given a list of local paths, try to find them in gcs and sync them locally.

    This includes a DIY sync flag stashed on the shared filesystem using the
    DET_TASK_ID environment variable
    """

    # Only applicable in CW-EAST4.
    if is_gcp() or is_legacy_cw():
        return

    task_id = os.environ.get("DET_TASK_ID", None)
    assert task_id is not None, "Must be running in a determined job"
    sync_flag_file = AUGMENT_CHECKPOINTS_ROOT / "sync_flags" / f"{task_id}"
    if rank == 0:
        print("Waiting for file sync")
        st = time.time()
        sync_to_local(local_paths, rank)
        print(f"File sync complete in {time.time() - st:.2f} seconds")
        logging.info(f"Creating the sync flag {sync_flag_file}")
        sync_flag_file.parent.mkdir(parents=True, exist_ok=True)
        sync_flag_file.touch()
    else:
        while True:
            logging.debug(f"Waiting for sync flag file {sync_flag_file}")
            if sync_flag_file.exists():
                logging.debug(f"Found sync flag file {sync_flag_file}")
                break
            time.sleep(5)


def bucketize_local_path_list(local_paths: List[str]):
    """Bucketize a list of local paths."""
    normal_paths = []

    def normalize(pth: str):
        # Handle named paths from fastbackward
        if "@" in pth:
            return pth.split("@")[1]
        return pth

    def bucketize(pth: str):
        try:
            bucket, local_part = bucket_for_augment_fs_path(normalize(pth))
            return f"gs://{bucket}/{local_part}"
        except ValueError:
            logging.warning(f"Path {pth} is not under /mnt/efs/augment")
            return None

    for path in local_paths:
        if path:
            # Handle compound paths
            if ";" in path:
                for p in path.split(";"):
                    normal_paths.append(bucketize(p))
            else:
                normal_paths.append(bucketize(path))
    return [p for p in normal_paths if p is not None]


def sync_to_local(local_paths: List[str] | str, rank: int):
    """Given a local path, try to find it in gcs and sync it locally.

    Only applicable in CW-EAST4
    """
    if is_gcp():
        return

    if not isinstance(local_paths, List):
        local_paths = [local_paths]
    # Get the bucket paths
    bucket_paths = bucketize_local_path_list(local_paths)

    try:
        sync_from_gcs(bucket_paths, rank)
    except ValueError as exc:
        print(f"Failed to sync local path {local_paths}: {exc}")
        return


def is_gcp():
    try:
        r = requests.get(
            "http://metadata.google.internal/computeMetadata/v1/instance/zone",
            headers={"Metadata-Flavor": "Google"},
            timeout=60,
        )
        r.raise_for_status()
        return True
    except requests.RequestException:
        return False


def is_legacy_cw():
    """Temp hack to detect legacy CW vs. CW-EAST-4.

    Only really works in a determined job, relies on different naming
    scheme for pods between different determined versions.
    """
    hostname = socket.gethostname()
    if hostname.startswith("exp-"):
        return True
    return False


def _expand_overwrite_values(item):
    """Expand $EXPERIMENT_ID, $TRIAL_ID in overwrite_values."""
    if isinstance(item, str):
        cluster_info = get_cluster_info()
        if cluster_info is None:
            return item
        experiment_id = str(cluster_info.trial.experiment_id)
        trial_id = str(cluster_info.trial.trial_id)
        item = item.replace("$EXPERIMENT_ID", experiment_id).replace(
            "$TRIAL_ID", trial_id
        )

    if isinstance(item, list):
        items = item
    elif isinstance(item, str):
        items = [item]
    else:
        return item

    new_items = []
    rank = torch.distributed.get_rank()
    gcs_sync_items = []
    local_sync_items = []
    for i in items:
        if not isinstance(i, str):
            continue
        if i.startswith("gs://"):
            gcs_sync_items.append(i)
        elif i.startswith("s3://"):
            new_items.append(sync_from_s3(i, rank))
        elif i.startswith("/mnt/efs/"):
            local_sync_items.append(i)
        else:
            new_items.append(i)

    if gcs_sync_items:
        new_items.extend(sync_from_gcs(gcs_sync_items, rank))
    if local_sync_items:
        new_items.extend(local_sync_items)
        sync_to_local(local_sync_items, rank)

    # This updates the neox args with the new paths
    if isinstance(item, list):
        return new_items
    return new_items[0]


def get_slack_config(key=None):
    slack_config = get_augment_args().get("slack_notifications", {})
    if key is not None:
        return slack_config.get(key, None)
    return slack_config


def peek(iterator, n: int = 1):
    """Peek at the next n items in an iterator.

    Returns (items, iterator) where the iterator contains the original items.
    """
    items = []
    for _ in range(n):
        items.append(next(iterator))
    return items, itertools.chain(items, iterator)


def annotate_experiment(iteration: int, determined_url: str):
    """Annotate determined and WandB.

    AU-190 & AU-341
    Add the wandb URL to the determined experiment notes.
    Add the determined experiment URL to the wandb run
    """
    if torch.distributed.get_rank() == 0:
        run_url = wandb.run.get_url()  # type: ignore
        print(f"Got wandb run url {run_url}")
        cluster_info = get_cluster_info()
        if cluster_info is None:
            return
        experiment_id = str(cluster_info.trial.experiment_id)
        experiment_url = f"{determined_url}/det/experiments/{experiment_id}"
        try:
            wandb_log(
                {
                    "determined": wandb.Html(
                        f'<a target="_blank" href="{experiment_url}">Determined experiment #{experiment_id}</a>'
                    )
                },
                iteration,
            )
        except Exception as exc:  # pylint: disable=broad-except
            print(f"Failed to annotate wandb {exc}")

        try:
            det_sa_user = Path(
                "/run/determined/secrets/determined-sa-secret/username"
            ).read_text()
            det_sa_pass = Path(
                "/run/determined/secrets/determined-sa-secret/password"
            ).read_text()
            print(f"Logging into determined at {cluster_info.master_url}")
            det_client.login(
                master=cluster_info.master_url, user=det_sa_user, password=det_sa_pass
            )
        except ValueError:
            # It throws an exception if you're already logged in?
            pass
        except Exception as exc:  # pylint: disable=broad-except
            print(f"Failed to log in to determined {exc}")
            return
        # The python API doesn't support adding notes, use the REST api
        exp = det_client.get_experiment(int(experiment_id))
        data = {"id": experiment_id, "notes": f"[WandB run URL]({run_url})"}
        print(
            f"Patching {exp} at /api/v1/experiments/{experiment_id} with {json.dumps(data)}"
        )
        r = exp._session.patch(  # pylint: disable=protected-access
            f"/api/v1/experiments/{experiment_id}", data=json.dumps(data)
        )
        print(f"Received response {r} {r.json()['experiment']['notes']}")


def post_slack_update(provider, message: Any):
    if torch.distributed.get_rank() != 0:
        return
    user = get_slack_config("slack_user")
    enabled = get_slack_config("enabled")
    if user is None or enabled is None or not enabled:
        return
    print(f"Posting slack {message} to {user}")
    token = Path("/run/determined/secrets/cw-bot-token/token").read_text()

    cluster_info = get_cluster_info()
    if cluster_info is None:
        return
    experiment_id = str(cluster_info.trial.experiment_id)
    experiment_url = f"{provider.determined_url}/det/experiments/{experiment_id}"

    auth_headers = {"Authorization": f"Bearer {token}"}
    slack_url = "https://slack.com/api/chat.postMessage"
    data = {"channel": user, "unfurl_links": False}

    blocks = [
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"<{experiment_url}|Experiment #{experiment_id}> for user <@{user}>",
            },
        },
        {"type": "section", "text": {"type": "mrkdwn", "text": f"{message}"}},
    ]
    data["blocks"] = json.dumps(blocks)
    r = requests.post(slack_url, data=data, headers=auth_headers, timeout=120)
    if not r.ok:
        print(f"Slack notification returned {r}")
        print(r.text)


class AugmentCallback(PyTorchCallback):
    """Augment specific callbacks."""

    def __init__(self, trial):
        self.trial = trial
        self.augment_args = get_augment_args()

    # TODO deprecate subprocess here
    def fetch_cps(self):
        """Fetch the list of checkpoints for this experiment/trial."""
        cluster_info = get_cluster_info()
        if cluster_info is None:
            return []
        experiment_id = str(cluster_info.trial.experiment_id)
        trial_id = str(cluster_info.trial.trial_id)
        cmd = [
            DET_BINARY,
            "-m",
            cluster_info.master_url,
            "experiment",
            "lc",
            experiment_id,
            "--csv",
        ]
        print_rank_0(f"Checkpoint GC running: {' '.join(cmd)}")
        cp = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=False,
            encoding="utf-8",
            errors="backslashreplace",
        )
        if cp.returncode == 0:
            # Trial ID,# of Batches,State,Validation Metric,UUID,Resources,Size
            csv.field_size_limit(sys.maxsize)
            csvreader = csv.DictReader(cp.stdout.splitlines())
            completed_cps = [
                (
                    row["UUID"],
                    row["# of Batches"],
                    row["Validation Metric"],
                    row["Size"],
                )
                for row in csvreader
                if row["State"] == "COMPLETED" and row["Trial ID"] == trial_id
            ]
            return completed_cps
        else:
            print_rank_0(
                f"Checkpoint GC can't list checkpoints: {cp.returncode}\n{cp.stderr}"
            )
            return []

    # TODO deprecate subprocess here
    def delete_cps(self, cp_ids):
        """Delete a set of checkpoints."""
        if len(cp_ids) == 0:
            return
        cluster_info = get_cluster_info()
        if cluster_info is None:
            return
        print_rank_0(f"Checkpoint GC deleting: {cp_ids}")
        cmd = [
            DET_BINARY,
            "-m",
            f"{cluster_info.master_url}",
            "checkpoint",
            "delete",
            "--yes",
            ",".join(cp_ids),
        ]
        print_rank_0(f"Checkpoint GC running: {' '.join(cmd)}")

        cp = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=False,
            encoding="utf-8",
            errors="backslashreplace",
        )
        if cp.returncode != 0:
            print_rank_0(
                f"Checkpoint GC can't delete checkpoints: {cp.returncode}\n{cp.stderr}"
            )

    def perform_cp_gc(self):
        """Perform on-the-fly GC of checkpoints during the trial.

        Only manage the checkpoints taken by this trial, don't delete other trial's checkpoints
        No matter what, always save the latest checkpoint in the trial
        """
        if torch.distributed.get_rank() == 0:
            cp_storage_policy = self.trial.exp_config["checkpoint_storage"]
            cluster_info = get_cluster_info()
            if cluster_info is None:
                return
            experiment_id = str(cluster_info.trial.experiment_id)
            trial_id = str(cluster_info.trial.trial_id)
            print_rank_0(
                f"Checkpoint GC experiment {experiment_id} trial {trial_id} "
                f"save_trial_best: {cp_storage_policy['save_trial_best']} "
                f"save_trial_latest: {cp_storage_policy['save_trial_latest']}"
            )
            completed_cps = self.fetch_cps()
            print_rank_0(f"Checkpoint GC fetched {len(completed_cps)}")
            if len(completed_cps) == 0:
                return
            best_cps = sorted(
                [
                    cp for cp in completed_cps if cp[2]
                ],  # Only count the CPs with validation metrics
                key=lambda row: float(row[2]),
            )  # validation metric, ascending
            latest_cps = sorted(
                completed_cps, key=lambda row: int(row[1]), reverse=True
            )  # Batch number, descending

            exclude_cps = set([latest_cps[0]])  # Always save the latest
            exclude_cps.update(best_cps[: cp_storage_policy["save_trial_best"]])
            exclude_cps.update(latest_cps[: cp_storage_policy["save_trial_latest"]])

            for row in exclude_cps:
                print_rank_0(f"Checkpoint GC keeping: {row}")
            self.delete_cps([row[0] for row in completed_cps if row not in exclude_cps])

    def default_eval_results_dir(self):
        cluster_info = get_cluster_info()
        if cluster_info is None:
            return str(AUGMENT_EFS_ROOT / "eval/jobs/exp-unknown-trial-unknown")
        experiment_id = str(cluster_info.trial.experiment_id)
        trial_id = str(cluster_info.trial.trial_id)
        return str(AUGMENT_EFS_ROOT / f"eval/jobs/exp-{experiment_id}-trial-{trial_id}")

    def perform_cp_eval(self, uuid):
        """Perform evaluations of checkpoints as the experiment runs.

        - load the eval config file, if specified
        - replace the eval options with the options in our config
        - create the checkpoints array with our checkpoint
        """
        print("Performing Checkpoint Eval")
        template_file = self.cp_handling.get("config")
        eval_config = {}
        if template_file is not None:
            config_file = Path(EVAL_CONFIG_DIR) / template_file
            with config_file.open("r", encoding="utf-8") as cf:
                eval_config = yaml.safe_load(cf)

        # Override the config with any local changes in the experiment
        if self.cp_handling.get("tasks") is not None:
            eval_config["tasks"] = self.cp_handling.get("tasks")
        if self.cp_handling.get("podspec") is not None:
            eval_config["podspec"] = self.cp_handling.get("podspec")
        if self.cp_handling.get("neox_args") is not None:
            eval_config["neox_args"] = self.cp_handling.get("neox_args")
        if self.cp_handling.get("determined") is not None:
            eval_config["determined"] = self.cp_handling.get("determined")
        if self.cp_handling.get("eval_tags") is not None:
            eval_config["eval_tags"] = self.cp_handling.get("eval_tags")
        job_root = _expand_overwrite_values(
            self.cp_handling.get("job_root", self.default_eval_results_dir()),
        )

        eval_config["determined"]["wandb_run_id"] = "eval-$EXPERIMENT_ID"

        for key, value in eval_config["determined"].items():
            new_value = _expand_overwrite_values(value)
            if new_value != value:
                print(
                    f"eval_config.determined.{key} expanded from {value} to {new_value}"
                )
                eval_config["determined"][key] = new_value

        eval_config["checkpoints"] = [
            {"path": f"{self.trial.provider.checkpoint_bucket}/{uuid}"}
        ]

        eval_config["neox_args"].update(
            {
                "use_wandb": True,
                "wandb_project": self.trial.neox_args.wandb_project,
                "wandb_group": self.trial.neox_args.wandb_group,
                "wandb_name": f"{self.trial.neox_args.wandb_name}-eval",
            }
        )

        # TODO deprecate subprocess here
        print("Performing Checkpoint eval with config")
        print(yaml.dump(eval_config))
        with tempfile.NamedTemporaryFile(mode="w", dir="/tmp", delete=False) as f:
            f.write(yaml.dump(eval_config))
            f.flush()
            cmd = [sys.executable, "eval.py", "--job_root", job_root, f.name]
            print_rank_0(f"Checkpoint Eval running: {' '.join(cmd)}")

            cp = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=False,
                encoding="utf-8",
                errors="backslashreplace",
                cwd=EVAL_DIR,
            )
            if cp.returncode != 0:
                print_rank_0(
                    f"Checkpoint Eval can't eval checkpoints: {cp.returncode}\n{cp.stdout}\n{cp.stderr}"
                )
            else:
                print_rank_0(f"Checkpoint Eval running\n{cp.stdout}\n")
            launch_log = Path(EVAL_DIR) / "eval_launch.log"
            log_dest = Path(str(job_root)) / "eval_launch.log"
            print_rank_0(f"Copying {launch_log} to {log_dest}\n")
            log_dest.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy(launch_log, log_dest)  # cross-device, can't rename

    def on_checkpoint_upload_end(self, uuid):  # pylint: disable=unused-argument
        try:
            if self.cp_gc_enabled:
                self.perform_cp_gc()
            elif self.cp_eval_enabled:
                self.perform_cp_eval(uuid)
        except Exception as exc:  # pylint: disable=broad-except
            print(f"Checkpoint handling failed, ignoring: {exc}")

    def on_checkpoint_write_end(self, checkpoint_dir):
        if torch.distributed.get_rank() == 0:
            metadata_filename = "checkpoint_metadata.txt"
            print(f"Writing checkpoint metadata to {metadata_filename}")
            try:
                wandb_run_url = wandb.run.get_url()  # type: ignore
            except AttributeError:
                wandb_run_url = "Wandb URL not available"
            cluster_info = get_cluster_info()
            if cluster_info is None:
                return
            experiment_id = str(cluster_info.trial.experiment_id)
            experiment_url = (
                f"{self.trial.provider.determined_url}/det/experiments/{experiment_id}"
            )
            with (Path(checkpoint_dir) / metadata_filename).open(
                "w", encoding="utf-8"
            ) as mdf:
                mdf.write(f"{wandb_run_url}\n{experiment_url}\n")

            metaconfig_filename = "experiment_metaconfig.yaml"
            print(f"Writing metaconfig to {metaconfig_filename}")
            with (Path(checkpoint_dir) / metaconfig_filename).open(
                "w", encoding="utf-8"
            ) as mcf:
                mcf.write(yaml.dump(self.submitted_metaconfig))

            try:
                running_config_filename = "config.yml"
                config_path = Path(checkpoint_dir) / running_config_filename
                print(f"Writing running config to {config_path.as_posix()}")
                with (config_path).open("w", encoding="utf-8") as rcf:
                    rcf.write(yaml.dump(self.trial.running_args_dict))
            except Exception as exc:  # pylint: disable=broad-exception-caught
                print(f"Failed to write running config: {exc}")

    def on_validation_epoch_start(self):
        # print("on_validation_epoch_start")
        ...

    def on_validation_epoch_end(self, outputs):
        print_rank_0(f"on_validation_epoch_end {outputs}")

    def on_validation_start(self):
        MemoryContext.set(MemoryContext(MemoryAction.TRAIN_EVAL))
        print_rank_0(
            f"Starting validation at iteration {self.trial.neox_args.iteration}"
        )
        # log the sample batch at the start of every validation
        self.trial.examples_logged = 0

    def on_validation_end(self, metrics):
        print_rank_0(f"on_validation_end {metrics}")
        self._log_validation_results(metrics)
        self.post_slack_validation(self.trial.neox_args.iteration, metrics)

    def on_trial_shutdown(self):
        self.post_slack_update(
            f"Trial Shutdown after {self.trial.get_controller().steps_completed} steps"
        )

    def on_trial_startup(
        self,
        first_batch_idx: int,
        checkpoint_uuid: Optional[str],  # pylint: disable=unused-argument
    ) -> None:
        self.post_slack_update(f"Trial Startup at batch {first_batch_idx}")

    def post_slack_validation(self, iteration, metrics):
        if torch.distributed.get_rank() != 0:
            return
        post_validation = get_slack_config("notify_validations")
        if post_validation:
            self.post_slack_update(f"Validation at iteration {iteration}: {metrics}")

    def post_slack_update(self, message: Any):
        if torch.distributed.get_rank() != 0:
            return
        post_slack_update(self.trial.provider, message)

    def _log_validation_results(self, metrics):
        self._wandb_validation_results(metrics)
        string = self._format_validation_results(metrics)
        length = len(string) + 1
        print_rank_0("-" * length)
        print_rank_0(string)
        print_rank_0("-" * length)

    def _format_validation_results(self, metrics):
        """Replicate the format of the original testing harness."""
        string = f" validation results at iteration {self.trial.neox_args.iteration} | "
        for k, v in metrics.items():
            if isinstance(v, dict):
                for k2, v2 in v.items():
                    k3 = "_".join([k, k2])
                    string += f"{k3} value: {v2:.6E} | "
            else:
                string += f"{k} value: {v:.6E} | "
        return string

    def _wandb_validation_results(self, metrics):
        if torch.distributed.get_rank() != 0 or not self.trial.neox_args.use_wandb:
            return
        for k, v in metrics.items():
            if isinstance(v, dict):
                for k2, v2 in v.items():
                    k3 = "_".join([k, k2])
                    wandb_log({f"validation/{k3}": v2}, self.trial.neox_args.iteration)
            else:
                wandb_log({f"validation/{k}": v}, self.trial.neox_args.iteration)

    @property
    def cp_gc_enabled(self):
        gc_enabled = self.cp_handling.get("action", None) == "gc"
        return gc_enabled

    @property
    def cp_eval_enabled(self):
        eval_enabled = self.cp_handling.get("action", None) == "eval"
        return eval_enabled

    @property
    def cp_handling(self):
        cp_handling = self.augment_args.get("checkpoint_handling", {})
        return cp_handling

    @property
    def submitted_metaconfig(self):
        return self.augment_args.get("submitted_metaconfig", {})


class EarlyStoppingCallback(PyTorchCallback):
    """Code pulled in from det_utils.py from the determined example."""

    def __init__(self, trial):
        self.trial = trial

    def on_validation_start(self):
        if self.trial.reported_flops:
            self.trial.context.set_stop_requested(True)


class LMReducers(MetricReducer):
    """Code pulled in from det_utils.py from the determined example."""

    def __init__(self, neox_args):
        self.char_level_ppl = neox_args.char_level_ppl
        self.token_count = 0
        self.char_count = 0
        self.lm_losses = []

    def update(self, lm_loss, token_count=None, char_count=None):
        self.lm_losses.append(lm_loss)
        if self.char_level_ppl:
            if token_count is not None:
                self.token_count += token_count
            if char_count is not None:
                self.char_count += char_count

    def reset(self):
        self.lm_losses = []
        self.token_count = 0
        self.char_count = 0

    def per_slot_reduce(self):
        return self.lm_losses, self.token_count, self.char_count

    def cross_slot_reduce(self, per_slot_metrics):
        lm_losses, token_count, char_count = zip(*per_slot_metrics)
        lm_losses = [item for sublist in lm_losses for item in sublist]

        metrics = {"lm_loss": np.mean(lm_losses)}
        metrics["lm_loss_ppl"] = np.exp(metrics["lm_loss"])
        if self.char_level_ppl:
            tokens_per_char = sum(token_count) / sum(char_count)
            metrics["lm_loss_char_lvl_ppl"] = np.exp(
                metrics["lm_loss"] * tokens_per_char
            )
        return metrics


def run_eval_harness(*args, **kwargs):
    raise NotImplementedError("We have removed lm_eval and run_eval_harness.")


class EvalHarness(PyTorchCallback):
    """Code pulled in from det_utils.py from the determined example."""

    def __init__(self, model, forward_step_fn, neox_args):
        self.model = model
        self.forward_step_fn = forward_step_fn
        self.neox_args = neox_args

    def on_validation_end(self, metrics):
        # TODO: This hangs with pipeline parallel.
        metrics.update(
            run_eval_harness(
                self.model,
                self.forward_step_fn,
                self.neox_args,
                eval_tasks=self.neox_args.eval_tasks,
            )
        )

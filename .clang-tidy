# see https://clang.llvm.org/extra/clang-tidy/
UseColor: true

# Reasonings:
# - google-build-using-namespace: Okay for us. I wish I would have the Pure variant that only checks
#   this in header files
# - -cppcoreguidelines-avoid-magic-numbers: There is no point in creating constants for numbers
# - cppcoreguidelines-avoid-non-const-global-variables: GTEST and absl flags rely on this
# - cppcoreguidelines-pro-bounds-array-to-pointer-decay: coreguidelines can be annoying with pushing gsl
# - cppcoreguidelines-pro-bounds-constant-array-index: same as above
# - clang-diagnostic-deprecated-declarations: from libfmt
# - cert-err58-cpp: either we nor absl nor gtest use exceptions, so the code isn't careful about noexpect
Checks: >
  bugprone-*,
  cppcoreguidelines-*,
  google-*,
  performance-*,
  cert-*,
  -google-build-using-namespace,
  -cppcoreguidelines-avoid-magic-numbers,
  -cppcoreguidelines-avoid-non-const-global-variables,
  -cppcoreguidelines-pro-bounds-array-to-pointer-decay,
  -cppcoreguidelines-pro-bounds-constant-array-index,
  -clang-diagnostic-deprecated-declarations,
  -cert-err58-cpp

WarningsAsErrors: "*"

[flake8]
# Recommended config for compatibility with black formatter
max-line-length = 88
docstring-convention=google
extend-ignore = E203
per-file-ignores =
    # Config files are python but they don't each need a docstring.
    research/fastbackward/configs/*:D100
    # ignore "F401: unused import in this legacy file
    # ignore "E402: we need to untangle these imports over time.
    # ignore "D*": messages in base.py, since there are so many docstring violations.
    research/gpt-neox/megatron/utils.py:E731
    research/gpt-neox/megatron/mpu/__init__.py:F401
    research/gpt-neox/megatron/memorize/__init__.py:F401
    research/gpt-neox/megatron/neox_arguments/__init__.py:F401
    research/gpt-neox/eval_tasks/eval_adapter.py:E402
    research/data/pyarrow/data_step/__init__.py:F401
    research/data/pyarrow/pipelines/the-stack.2022-11-19/*.py:E402

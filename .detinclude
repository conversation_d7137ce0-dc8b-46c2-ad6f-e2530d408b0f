# The order of this file is important.
# Read the notion page at https://www.notion.so/Including-files-in-determined-jobs-91c89c17e6124b488671354df5faea4a
#
# Lines are processed in order.
# Lines starting with # are comments.
# Blank lines are ignored.
#
# Lines starting with ! will exclude previously included files.
#
/MODULE.bazel
/MODULE.bazel.lock
/WORKSPACE
/base
!/experimental
/experimental/__init__.py
/go.mod
/go.sum
/pyproject.toml
/pyrightconfig.ci.json
/research
!/research/data/eval/repos
!/research/fastbackward/wandb
!/research/gpt-neox/megatron/tokenizer/tiktoken_lib/target
!/research/gpt-neox/wandb
!/research/fastbackward/wandb
!/research/tools
!/research/infra/cfg/vendor
!/research/infra/lib/augment
/rustfmt.toml
/services
!/services/embeddings_search_host
/yamlfmt.yaml
/.bazelrc
/.bazelversion
/.augment
/.anthropic
#
# Generic filetype, etc excludes follow
#
!augi.build
!.nfs*
!.pytest_cache
!BUILD
!Cargo.Bazel.lock
!Cargo.lock
!Cargo.toml
!README.md
!node_modules
!package-lock.json
!package.json
!target
!__pycache__
!*.ipynb
!*.swp
!*.gif
!*.png
!*.pdf

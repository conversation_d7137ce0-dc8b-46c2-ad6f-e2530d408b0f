{"version": "1.5.0", "plugins_used": [{"name": "ArtifactoryDetector"}, {"name": "AWSKeyDetector"}, {"name": "AzureStorageKeyDetector"}, {"name": "BasicAuthDetector"}, {"name": "CloudantDetector"}, {"name": "DiscordBotTokenDetector"}, {"name": "GitHubTokenDetector"}, {"name": "GitLabTokenDetector"}, {"name": "IbmCloudIamDetector"}, {"name": "IbmCosHmacDetector"}, {"name": "IPPublicDetector"}, {"name": "JwtTokenDetector"}, {"name": "KeywordDetector", "keyword_exclude": ""}, {"name": "MailchimpDetector"}, {"name": "NpmDetector"}, {"name": "OpenAIDetector"}, {"name": "PrivateKeyDetector"}, {"name": "PypiTokenDetector"}, {"name": "SendGridDetector"}, {"name": "SlackDetector"}, {"name": "SoftlayerDetector"}, {"name": "SquareOAuthDetector"}, {"name": "StripeDetector"}, {"name": "TelegramBotTokenDetector"}, {"name": "TwilioKeyDetector"}], "filters_used": [{"path": "detect_secrets.filters.allowlist.is_line_allowlisted"}, {"path": "detect_secrets.filters.common.is_baseline_file", "filename": ".secrets.baseline"}, {"path": "detect_secrets.filters.common.is_ignored_due_to_verification_policies", "min_level": 2}, {"path": "detect_secrets.filters.heuristic.is_indirect_reference"}, {"path": "detect_secrets.filters.heuristic.is_likely_id_string"}, {"path": "detect_secrets.filters.heuristic.is_lock_file"}, {"path": "detect_secrets.filters.heuristic.is_not_alphanumeric_string"}, {"path": "detect_secrets.filters.heuristic.is_potential_uuid"}, {"path": "detect_secrets.filters.heuristic.is_prefixed_with_dollar_sign"}, {"path": "detect_secrets.filters.heuristic.is_sequential_string"}, {"path": "detect_secrets.filters.heuristic.is_swagger_file"}, {"path": "detect_secrets.filters.heuristic.is_templated_secret"}, {"path": "detect_secrets.filters.regex.should_exclude_file", "pattern": ["package.lock.json|pnpm-lock.yaml|dataset_infos.json"]}], "results": {"deploy/common/reloader-values.yaml": [{"type": "Secret Keyword", "filename": "deploy/common/reloader-values.yaml", "hashed_secret": "5ffe533b830f08a0326348a9160afafc8ada44db", "is_verified": false, "line_number": 3}], "experimental/marcmac/gcp/helm/determined-0.19.9/values-augment.yaml": [{"type": "Secret Keyword", "filename": "experimental/marcmac/gcp/helm/determined-0.19.9/values-augment.yaml", "hashed_secret": "afc848c316af1a89d49826c5ae9d00ed769415f3", "is_verified": false, "line_number": 82}, {"type": "Secret Keyword", "filename": "experimental/marcmac/gcp/helm/determined-0.19.9/values-augment.yaml", "hashed_secret": "3853e551891f60c1dc6465cc5512f94a1e7fcce4", "is_verified": false, "line_number": 373}], "experimental/marcmac/gcp/helm/determined-0.19.9/values.yaml": [{"type": "Secret Keyword", "filename": "experimental/marcmac/gcp/helm/determined-0.19.9/values.yaml", "hashed_secret": "afc848c316af1a89d49826c5ae9d00ed769415f3", "is_verified": false, "line_number": 82}], "experimental/marcmac/gcp/helm/determined-0.23.4/values-augment.yaml": [{"type": "Secret Keyword", "filename": "experimental/marcmac/gcp/helm/determined-0.23.4/values-augment.yaml", "hashed_secret": "afc848c316af1a89d49826c5ae9d00ed769415f3", "is_verified": false, "line_number": 103}, {"type": "Secret Keyword", "filename": "experimental/marcmac/gcp/helm/determined-0.23.4/values-augment.yaml", "hashed_secret": "3853e551891f60c1dc6465cc5512f94a1e7fcce4", "is_verified": false, "line_number": 394}], "experimental/marcmac/gcp/helm/determined-0.23.4/values.yaml": [{"type": "Secret Keyword", "filename": "experimental/marcmac/gcp/helm/determined-0.23.4/values.yaml", "hashed_secret": "afc848c316af1a89d49826c5ae9d00ed769415f3", "is_verified": false, "line_number": 120}], "experimental/vzhao/airflow/README.md": [{"type": "Secret Keyword", "filename": "experimental/vzhao/airflow/README.md", "hashed_secret": "afe59c1dac9ada4e6fc58033b89b7e2be0be2c5d", "is_verified": false, "line_number": 23}, {"type": "Basic Auth Credentials", "filename": "experimental/vzhao/airflow/README.md", "hashed_secret": "afe59c1dac9ada4e6fc58033b89b7e2be0be2c5d", "is_verified": false, "line_number": 49}], "experimental/yangguang/vscode-ui-prototype/src/env.ts.example": [{"type": "JSON Web Token", "filename": "experimental/yangguang/vscode-ui-prototype/src/env.ts.example", "hashed_secret": "33e7550d32c757ca5ddb828c41d209476d5bd364", "is_verified": false, "line_number": 8}], "experimental/yury/edit/prepare_pr_reviews_octopack.ipynb": [{"type": "GitHub Token", "filename": "experimental/yury/edit/prepare_pr_reviews_octopack.ipynb", "hashed_secret": "e175c6f5f2a92e8623bd9a4820edb4e8c1b0fd10", "is_verified": false, "line_number": 87}], "experimental/yury/edit/prepare_pr_suggested_edits.ipynb": [{"type": "GitHub Token", "filename": "experimental/yury/edit/prepare_pr_suggested_edits.ipynb", "hashed_secret": "e175c6f5f2a92e8623bd9a4820edb4e8c1b0fd10", "is_verified": false, "line_number": 77}], "research/environments/cw_docker.py": [{"type": "Secret Keyword", "filename": "research/environments/cw_docker.py", "hashed_secret": "70609fa8fa03373eee7d565be985c780e751ecda", "is_verified": false, "line_number": 21}], "research/tools/patch_viewer/dist/bundle.js": [{"type": "Secret Keyword", "filename": "research/tools/patch_viewer/dist/bundle.js", "hashed_secret": "53e07a32bf191d6917ee6fd863f0b52632a86798", "is_verified": false, "line_number": 2}], "tools/kubecfg/test_data/deployment.json": [{"type": "Secret Keyword", "filename": "tools/kubecfg/test_data/deployment.json", "hashed_secret": "ae8bc327f5709c60acf61c62bd2f9a652ea11900", "is_verified": false, "line_number": 49}], "tools/kubecfg/test_data/deployment_test_kubecfg_push.json": [{"type": "Secret Keyword", "filename": "tools/kubecfg/test_data/deployment_test_kubecfg_push.json", "hashed_secret": "ae8bc327f5709c60acf61c62bd2f9a652ea11900", "is_verified": false, "line_number": 55}], "tools/monitoring/jaeger-operator.yaml": [{"type": "Secret Keyword", "filename": "tools/monitoring/jaeger-operator.yaml", "hashed_secret": "f8192aa7253c1d08d9343f71b922d6eb6d41785e", "is_verified": false, "line_number": 16265}]}, "generated_at": "2024-10-24T17:21:20Z"}
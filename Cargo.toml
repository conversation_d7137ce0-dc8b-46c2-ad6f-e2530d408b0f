[workspace]

resolver = "2"

members = [
    "services/api_proxy/server",
    "services/auth/central/server",
    "services/auth/query/client",
    "services/auth/query/server",
    "services/bigtable_proxy/client",
    "services/content_manager/server",
    "services/content_manager/client",
    "services/content_manager",
    "services/checkpoint_indexer",
    "services/checkpoint_indexer/server",
    "services/examples",
    "services/examples/server/rust",
    "services/embeddings_search_host/cpu_server",
    "services/inference_host/server/continuous_batching",
    "services/integrations/docset/client",
    "services/lib/grpc/auth",
    "services/lib/grpc/client",
    "services/lib/grpc/metrics",
    "services/lib/grpc/stream_mux",
    "services/lib/grpc/testing",
    "services/lib/grpc/tls_config",
    "services/lib/grpc/service",
    "services/lib/request_context",
    "services/memstore/client",
    "services/ping_pong",
    "services/ping_pong/client",
    "services/ping_pong/server",
    "services/remote_agents/client",
    "services/integrations/github/processor/client",
    "services/request_insight/publisher",
    "services/share/client",
    "services/tenant_watcher/client",
    "services/token_exchange/client",
    "services/working_set/client",
    "third_party/bigtable_rs",
    "third_party/tiktoken",
    "third_party/tracing-actix-web",
    "third_party/tracing-stackdriver",
    "base/rust/tracing-tonic",
    "base/rust/numpy",
    "base/blob_names/rust",
    "base/blob_names",
    "base/cloud/iap",
    "base/feature_flags",
    "base/logging",
    "base/logging/audit",
    "base/metrics_server/rust",
    "tools/genie/backend",
    "third_party/scann_rs",
]

exclude = [
    "research/research_lib/tiktoken",
    "bazel-augment/**",
    "bazel-out/**",
    "bazel-bin/**",
    "bazel-testlogs/**",
]

[workspace.dependencies]
actix-http = "3.9"
actix-rt = "*"
actix-web-lab = "0.16.9"
actix-web = { version = "4", features = ["rustls-0_23"] }
actix-files = "*"
actix-csrf = "*"
actix-tls = { version = "3.4.0", features = ["rustls-0_23"] }
assert_unordered = "*"
async-lock = "2.7.0"
async-rwlock = "*"
async-trait = "*"
async-recursion = "*"
async-channel = "1.9.0"
async-std = "*"
awc = { version = "3.1.1", features = ["rustls-0_23"], default-features = false }
backoff = "*"
# backtrace is pinned because of a tokio build workaround in MODULE.bazel
backtrace = "=0.3.74"
base64 = "*"
bigtable_rs = { path = "third_party/bigtable_rs" }
bytes = "*"
chrono = "*"
clap = { version = "4.0", features = ["derive"] }
futures = "*"
futures-util = "*"
futures-buffered = "*"
ginepro = "0.8.2"
google-cloud-gax = "*"
google-cloud-googleapis = { version = "*", features = ["pubsub"] }
google-cloud-pubsub = { version="*", default-features=false, features = [
    "rustls-tls", "auth"
] }
google-cloud-auth = { version="*", default-features=false, features = [
    "rustls-tls",
] }
google-cloud-token = { version = "*" }
hex = "0.4"
hex-literal = "*"
http = "1"
hyper = "1"
image = "0.24"
ipnet = "*"
itertools = "*"
jsonwebtoken = "9.3.0"
lazy_static = "1.4"
k8s-openapi = { version = "0.21.1", features = ["v1_27"] }
kube = { version = "0.90.0", features = ["runtime", "derive"] }
moka = { version = "0.12.5", features = ["future"] }
multimap = "*"
glob = "*"
regex = "1.10.4"
pin-project = "*"
pin-project-lite = "*"
prost = "0.13"
prost-build = { version = "0.13" }
prost-types = "0.13"
prost-wkt = "0.6"
prost-wkt-build = { version = "0.6" }
prost-wkt-types = "0.6"
opentelemetry-otlp = { version = "0.27", features = ["grpc-tonic"] }
opentelemetry = { version = "0.27", features = [] }
opentelemetry-http = "0.27"
opentelemetry_sdk = { version = "0.27", features = [
    "rt-tokio",
    "rt-async-std",
] }
launchdarkly-server-sdk = "2.2.1"
prometheus = { version = "0.14.0", features = ["process"] }
reqwest = { version = "0.12.4", features = [
    "rustls-tls",
], default-features = false }
rand = { version = "0.8", features = ["std_rng"] }
# aws-lc-rs has a dependency on a libc that we can't take yet
rustls = { version = "0.23.18", features = ["ring"], default-features = false }
rustls-native-certs = "0.8.1"
rustls-pemfile = "2.2.0"
rustls-pki-types = "1.10.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "*"
serde_urlencoded = "*"
schemars = "0.8.12"
secrecy = { version = "0.8", features = ["serde"] }
sha2 = "0.10.8"
sha256 = "1.1.3"
scopeguard = "1.2.0"
serial_test = "3.2.0"
tonic = { version = "0.12", features = ["tls", "gzip", "tls-roots", "tls-webpki-roots"] }
tonic-build = "*"
tonic-health = "*"
tonic-reflection = "*"
thiserror = "1.0.56"
tokenbucket = "0.1.5"
tokio = { version = "*", features = ["full"] }
tokio-retry2 = { version = "*" }
tokio-stream = { version = "*", features = ["net"] }
tokio-util = { version = "*", features = ["full"] }
tokio-metrics-collector = { version = "0.3.1", features = ["rt"] }
tracing = { version = "0.1.40", features = ["log"] }
tower = { version = "*", features = ["full"] }
url = "2.5.2"
uuid = { version = "1.3.0", features = ["v4", "fast-rng"] }
tracing-subscriber = { version = "0.3.18", features = [
    "registry",
    "env-filter",
] }
tracing-log = "0.2.0"
tracing-opentelemetry = "*"
x509-parser = "0.16.0"

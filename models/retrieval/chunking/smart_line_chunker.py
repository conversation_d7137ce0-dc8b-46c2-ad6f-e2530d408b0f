"""Smart line chunker implementation. Considers language and indentation."""

from collections.abc import Iterable

from base.retrieval.chunking.smart_chunking import SmartChunker
from models.retrieval.chunking import chunking


class SmartLineChunker(chunking.Chunker):
    """A wrapper around the base.retrieval.chunking.smart_chunking.SmartChunker."""

    def __init__(self, max_chunk_chars: int, max_headers: int):
        self._internal_chunker = SmartChunker(
            max_chunk_chars=max_chunk_chars, max_headers=max_headers
        )

    def split_into_chunks(self, doc: chunking.Document) -> Iterable[chunking.Chunk]:
        """This function takes a document and returns a list of chunks."""
        line_chunks = self._internal_chunker.split_chunks(doc.text, None)
        for chunk in line_chunks:
            yield chunking.Chunk(
                text=chunk.text,
                parent_doc=doc,
                char_offset=chunk.char_offset,
                line_offset=chunk.line_offset,
                length_in_lines=chunk.length_in_lines,
                header=chunk.header,
            )

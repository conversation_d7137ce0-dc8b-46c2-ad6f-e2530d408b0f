# Chunking

A part of the retrieval system is how to split source files (called documents here) into smaller pieces (chunks).
Usually, individual chunks will be added to the completion prompts, not the complete content of a blob ("document")

This directory contains implementations of different chunking strategies.

The two central classes are Document and Chunk.

A Document is a sequence of text of any length, and usually corresponds to a single raw content in the content manager.
The document may be split by the indexer into one or more blocks of shorter sequences, called chunks, for retrieval.

A Chunk represents a part of a Document that is the smallest unit of retrieval.
The sequence of text in the chunk may not exactly match the document text covered by the span (char_offset + length).

Note that the terminology used here differs from the terminology used in the BM25 implementation where what is
here called a "Chunk" is called a "Document".

"""Wrappers around a normal chunker to handle docset documents.

Docset chunks happen to always be pre-chunked, and no other documents are. We
take advantage of this and use the pre-chunked flag to either skip those
documents, or use the pre-chunked chunker, depending on which wrapper the caller
uses.
"""

from typing import Iterable

from models.retrieval.chunking import chunking
from models.retrieval.chunking.pre_chunked_chunker import PreChunkedChunker


class DocsetSkippingChunker(chunking.Chunker):
    """Skips docset documents.

    All other documents are sent to the inner chunker.
    """

    def __init__(self, inner_chunker: chunking.Chunker):
        self.inner_chunker = inner_chunker

    def split_into_chunks(self, doc: chunking.Document) -> Iterable[chunking.Chunk]:
        # Check if the document is from the docset service
        (docset_name, _) = doc.get_docset_metadata()
        if docset_name:
            # Skip this document
            return iter([])

        # If not a docset document, use the inner chunker
        return self.inner_chunker.split_into_chunks(doc)


class DocsetPreservingChunker(chunking.Chunker):
    """Sends docset documents to the pre-chunked chunker.

    All other documents are sent to the inner chunker.
    """

    def __init__(self, inner_chunker: chunking.Chunker):
        self.inner_chunker = inner_chunker
        self.docset_chunker = PreChunkedChunker()

    def split_into_chunks(self, doc: chunking.Document) -> Iterable[chunking.Chunk]:
        is_pre_chunked = False
        if doc.metadata:
            is_pre_chunked = doc.metadata.get("pre-chunked", "false") == "true"

        if is_pre_chunked:
            # If a pre-chunked document, use the pre-chunked chunker
            return self.docset_chunker.split_into_chunks(doc)
        else:
            # If not a pre-chunked document, use the inner chunker
            return self.inner_chunker.split_into_chunks(doc)

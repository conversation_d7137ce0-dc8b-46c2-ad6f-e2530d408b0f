"""Test document chunking.

Note: We have separate tests for the underlying chunking logic in
`base/retrieval/line_based_chunking_test.py`. New tests should be added there,
and the tests in this file are safe to deprecate.
"""

from textwrap import dedent

import pytest
from conftest import validate_chunks

from models.retrieval.chunking.chunking import Document
from models.retrieval.chunking.line_level_chunker_v2 import LineLevelChunkerV2


def test_new_line_level_chunker():
    """Basic test case for LineLevelChunkerWithOverlap."""
    text = dedent(
        """
        a
        b
        c
        dd
        ee
        ff
        ggg
        hhh
        iii
        jjjj
    """
    ).strip()
    doc = Document(blob_name="1", text=text)
    chunker = LineLevelChunkerV2(
        max_lines_per_chunk=3,
        max_chunk_size=1024,
    )
    chunks = list(chunker.split_into_chunks(doc))

    assert len(chunks) == 4
    assert chunks[0].text == "a\nb\nc\n"
    assert chunks[1].text == "dd\nee\nff\n"
    assert chunks[2].text == "ggg\nhhh\niii\n"
    assert chunks[3].text == "jjjj"
    validate_chunks(chunks, doc)

    assert chunks[0].char_offset == 0
    assert chunks[0].length == len(chunks[0].text)
    assert chunks[0].line_offset == 0
    assert chunks[0].length_in_lines == 3

    assert chunks[1].char_offset == text.index("d")
    assert chunks[1].length == len(chunks[1].text)
    assert chunks[1].line_offset == 3
    assert chunks[1].length_in_lines == 3

    assert chunks[2].char_offset == text.index("ggg")
    assert chunks[2].length == len(chunks[2].text)
    assert chunks[2].line_offset == 6
    assert chunks[2].length_in_lines == 3

    assert chunks[3].char_offset == text.index("jjjj")
    assert chunks[3].length == len(chunks[3].text)
    assert chunks[3].line_offset == 9
    assert chunks[3].length_in_lines == 1


def test_new_line_level_chunking_with_overlap():
    """Test that chunk overlap is correctly implemented."""
    text = dedent(
        """
        a
        b
        c
        dd
        ee
        ff
        ggg
        hhh
        iii
        jjjj
    """
    ).strip()
    doc = Document(blob_name="1", text=text)

    chunker_overlap1 = LineLevelChunkerV2(
        max_lines_per_chunk=5,
        overlap_lines=1,
        max_chunk_size=1024,
    )
    chunks = list(chunker_overlap1.split_into_chunks(doc))
    lines = text.splitlines(keepends=True)
    assert len(chunks) == 3
    assert chunks[0].text == "".join(lines[0:5])
    assert chunks[1].text == "".join(lines[4:9])
    assert chunks[2].text == "".join(lines[8:10])

    chunker_overlap2 = LineLevelChunkerV2(
        max_lines_per_chunk=5,
        overlap_lines=2,
        max_chunk_size=1024,
    )
    chunks = list(chunker_overlap2.split_into_chunks(doc))
    lines = text.splitlines(keepends=True)
    assert len(chunks) == 3
    assert chunks[0].text == "".join(lines[0:5])
    assert chunks[1].text == "".join(lines[3:8])
    assert chunks[2].text == "".join(lines[6:10])


def test_new_line_level_chunker_max_chunk_size():
    """Tests the max_chunk_size behavior."""
    text = dedent(
        """
        a
        b
        c
        dd
        ee
        ff
        ggg
        hhh
        iii
        jjjj
    """
    ).strip()
    doc = Document(blob_name="1", text=text)
    chunker = LineLevelChunkerV2(max_lines_per_chunk=10, max_chunk_size=8)
    chunks = list(chunker.split_into_chunks(doc))

    assert len(chunks) == 5
    assert chunks[0].text == "a\nb\nc\n"
    assert chunks[1].text == "dd\nee\n"
    assert chunks[2].text == "ff\nggg\n"
    assert chunks[3].text == "hhh\niii\n"
    assert chunks[4].text == "jjjj"
    validate_chunks(chunks, doc)

    chunker = LineLevelChunkerV2(
        max_lines_per_chunk=10, max_chunk_size=8, overlap_lines=1
    )
    chunks = list(chunker.split_into_chunks(doc))

    assert len(chunks) == 7
    assert chunks[0].text == "a\nb\nc\n"
    assert chunks[1].text == "c\ndd\nee\n"
    assert chunks[2].text == "ee\nff\n"
    assert chunks[3].text == "ff\nggg\n"
    assert chunks[4].text == "ggg\nhhh\n"
    assert chunks[5].text == "hhh\niii\n"
    assert chunks[6].text == "iii\njjjj"


def test_new_line_level_chunker_max_chunk_size_loooooong_line():
    """Tests the max_chunk_size behavior with a very long time."""
    text = dedent(
        """
        a
        b
        c
        dddddddddddddddddddddd
        ee
    """
    ).strip()
    doc = Document(blob_name="1", text=text)
    chunker = LineLevelChunkerV2(max_lines_per_chunk=10, max_chunk_size=8)
    chunks = list(chunker.split_into_chunks(doc))

    assert len(chunks) == 3
    assert chunks[0].text == "a\nb\nc\n"
    assert chunks[1].text == "dddddddddddddddddddddd\n"
    assert chunks[2].text == "ee"
    validate_chunks(chunks, doc)


@pytest.mark.parametrize("count", [1, 2, 3, 4, 5, 6, 7, 8, 9])
def test_new_line_level_chunker_terminal_line_break(count):
    """Tests the behavior with a terminal line break."""
    text = "a\nbb\nccc\ndddd\n"
    doc = Document(blob_name="1", text=text)
    chunker = LineLevelChunkerV2(max_lines_per_chunk=1024, max_chunk_size=count)
    chunks = list(chunker.split_into_chunks(doc))
    validate_chunks(chunks, doc)

    chunker = LineLevelChunkerV2(max_lines_per_chunk=count, max_chunk_size=1024)
    chunks = list(chunker.split_into_chunks(doc))
    validate_chunks(chunks, doc)

    text = "a\nbb\nccc\ndddd"
    doc = Document(blob_name="1", text=text)
    chunker = LineLevelChunkerV2(max_lines_per_chunk=1024, max_chunk_size=count)
    chunks = list(chunker.split_into_chunks(doc))
    validate_chunks(chunks, doc)

    chunker = LineLevelChunkerV2(max_lines_per_chunk=count, max_chunk_size=1024)
    chunks = list(chunker.split_into_chunks(doc))
    validate_chunks(chunks, doc)


def test_new_line_level_chunker_hard_line_limit():
    """Tests the behavior with a hard line limit."""
    text = "a\nbb\ncccccccccccccccccccccccccccccccccccc\ndddd\n"
    doc = Document(blob_name="1", text=text)
    chunker = LineLevelChunkerV2(
        max_lines_per_chunk=1024, max_chunk_size=8, hard_line_limit=20
    )
    chunks = list(chunker.split_into_chunks(doc))
    assert not chunks


def test_new_line_level_chunker_empty():
    """Tests the behavior without content."""
    text = ""
    doc = Document(blob_name="1", text=text)
    chunker = LineLevelChunkerV2(
        max_lines_per_chunk=1024, max_chunk_size=8, hard_line_limit=20
    )
    chunks = list(chunker.split_into_chunks(doc))

    assert len(chunks) == 1
    assert chunks[0].text == ""
    assert chunks[0].char_offset == 0
    validate_chunks(chunks, doc)

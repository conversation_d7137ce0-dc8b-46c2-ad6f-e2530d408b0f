"""Test for JSON serialization/deserialization of various types."""

import chunking


def document() -> chunking.Document:
    """Create a test document."""
    return chunking.Document(
        blob_name="1",
        text="a\nb\nc\ndd\nee\nff\nggg\nhhh\niiii\njjjj",
        path="foo.py",
        metadata={"foo": "bar"},
    )


def test_document_roundtrip():
    """Tests roundtrip serialization of a document."""
    doc = document()
    doc2 = chunking.Document.from_dict(doc.to_dict())
    assert doc.blob_name == doc2.blob_name
    assert doc.text == doc2.text
    assert doc.path == doc2.path
    assert doc.metadata is not None
    assert doc2.metadata is not None
    assert doc.metadata["foo"] == doc2.metadata["foo"]


def test_chunk_roundtrip():
    """Tests roundtrip serialization of a chunk."""
    chunk = chunking.Chunk(
        text="a\nb\nc\ndd\nee\nff\nggg\nhhh\niiii\njjjj",
        parent_doc=document(),
        char_offset=0,
        line_offset=0,
        length_in_lines=1,
        header="",
        documentation_metadata=chunking.DocumentationMetadata(
            name="Git",
            page_id="1",
            headers=["#Header1", "#Header2"],
            source_id="docset://Git",
        ),
    )
    chunk2 = chunking.Chunk.from_dict(chunk.to_dict())
    assert chunk.text == chunk2.text
    assert chunk.parent_doc.blob_name == chunk2.parent_doc.blob_name
    assert chunk.parent_doc.text == chunk2.parent_doc.text
    assert chunk.parent_doc.path == chunk2.parent_doc.path
    assert chunk.char_offset == chunk2.char_offset
    assert chunk.line_offset == chunk2.line_offset
    assert chunk.length_in_lines == chunk2.length_in_lines
    assert chunk.header == chunk2.header
    assert chunk.documentation_metadata is not None
    assert chunk2.documentation_metadata is not None
    assert chunk.documentation_metadata.name == chunk2.documentation_metadata.name

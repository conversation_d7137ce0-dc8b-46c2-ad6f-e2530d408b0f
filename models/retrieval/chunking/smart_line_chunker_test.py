"""Test smart line chunking."""

from models.retrieval.chunking.smart_line_chunker import SmartLineChunker
from models.retrieval.chunking.chunking import Document


def test_smart_line_chunker_wrapper():
    text = "123456789\nabcdefg\n"
    chunk_size = 10  # this limit will break `text` into two chunks
    assert (
        len(
            list(
                SmartLineChunker(
                    max_chunk_chars=chunk_size, max_headers=3
                ).split_into_chunks(Document(blob_name="1", text=text))
            )
        )
        == 2
    )

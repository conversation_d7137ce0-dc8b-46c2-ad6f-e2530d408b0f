import pathlib
from typing import Iterable

from models.retrieval.chunking.docset_chunkers import (
    DocsetSkippingChunker,
    DocsetPreservingChunker,
)
from models.retrieval.chunking.chunking import Chunk, Document


class FakeChunker:
    def split_into_chunks(self, doc: Document) -> Iterable[Chunk]:
        return [
            Chunk(
                text=doc.text,
                parent_doc=doc,
                char_offset=0,
                line_offset=0,
                length_in_lines=len(doc.text.splitlines()),
            )
        ]


def test_docset_skipping_chunker():
    """Tests the docset skipping chunker."""
    text = pathlib.Path(
        "models/retrieval/chunking/test_data/pre_chunked.jsonl"
    ).read_text(encoding="utf8")
    doc = Document(blob_name="1", text=text, metadata={"docset_name": "Git"})
    chunker = DocsetSkippingChunker(FakeChunker())
    chunks = list(chunker.split_into_chunks(doc))

    assert len(chunks) == 0


def test_docset_preserving_chunker():
    """Tests the docset preserving chunker."""
    text = pathlib.Path(
        "models/retrieval/chunking/test_data/pre_chunked.jsonl"
    ).read_text(encoding="utf8")
    doc = Document(blob_name="1", text=text, metadata={"pre-chunked": "true"})
    chunker = DocsetPreservingChunker(FakeChunker())
    chunks = list(chunker.split_into_chunks(doc))

    assert len(chunks) == 4

    doc = Document(blob_name="1", text=text, metadata={"pre-chunked": "false"})
    chunks = list(chunker.split_into_chunks(doc))

    assert len(chunks) == 1

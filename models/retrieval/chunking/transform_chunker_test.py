"""Test document chunking."""

from textwrap import dedent

import pathlib
from conftest import validate_chunks

from models.retrieval.chunking import chunking, line_level_chunker, transform_chunker

TEST_NOTEBOOK = pathlib.Path(__file__).parent.joinpath(
    "test_data/transform_chunker_input.ipynb"
)
TEST_INVALID_NOTEBOOK = pathlib.Path(__file__).parent.joinpath(
    "test_data/transform_chunker_invalid_input.ipynb"
)
TEST_PREPROCESSED_NOTEBOOK = pathlib.Path(__file__).parent.joinpath(
    "test_data/transform_chunker_preconverted_input.py"
)
TEST_PYTHON = pathlib.Path(__file__).parent.joinpath(
    "test_data/transform_chunker_output.py"
)


def test_transform_chunker_with_jupyter():
    """Tests the behavior with jupyter notebook."""
    notebook_text = open(TEST_NOTEBOOK).read()
    python_text = open(TEST_PYTHON).read()

    doc = chunking.Document(blob_name="1", text=notebook_text, path="test.ipynb")
    chunker = transform_chunker.TransformChunker(
        line_level_chunker.LineLevelChunker(max_lines_per_chunk=1024, max_chunk_size=8)
    )
    chunks = list(chunker.split_into_chunks(doc))

    converted_doc = chunking.Document(blob_name="1", text=python_text, path="test.py")

    validate_chunks(chunks, converted_doc)


def test_transform_chunker_with_jupyter_invalid_language():
    """Tests the behavior with jupyter notebook."""
    notebook_text = open(TEST_INVALID_NOTEBOOK).read()

    doc = chunking.Document(blob_name="1", text=notebook_text, path="test.ipynb")
    chunker = transform_chunker.TransformChunker(
        line_level_chunker.LineLevelChunker(max_lines_per_chunk=1024, max_chunk_size=8)
    )
    chunks = list(chunker.split_into_chunks(doc))

    converted_doc = chunking.Document(blob_name="1", text="", path="test.py")

    validate_chunks(chunks, converted_doc)


def test_transform_chunker_with_jupyter_preprocessed():
    """Tests the behavior with jupyter notebook."""
    notebook_text = open(TEST_PREPROCESSED_NOTEBOOK).read()

    doc = chunking.Document(blob_name="1", text=notebook_text, path="test.ipynb")
    chunker = transform_chunker.TransformChunker(
        line_level_chunker.LineLevelChunker(max_lines_per_chunk=1024, max_chunk_size=8)
    )
    chunks = list(chunker.split_into_chunks(doc))

    converted_doc = chunking.Document(blob_name="1", text=notebook_text, path="test.py")

    validate_chunks(chunks, converted_doc)

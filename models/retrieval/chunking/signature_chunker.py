"""Implements a chunker that extracts signatures.

A corresponding research implementation is in research/retrieval/chunking_functions.py
"""

import functools
from collections.abc import Iterable
from pathlib import Path
import threading

import structlog
from grpc import StatusCode

from base.executor.robust_executor import RobustExecutor
from base.static_analysis import common, parsing, signature_utils
from base.static_analysis.usage_analysis import FileSummary, ParsedFile
from models.retrieval.chunking import chunking

log = structlog.get_logger()


class SignatureChunker(chunking.Chunker):
    """Split a document into signature chunks."""

    def __init__(self, max_docstr_chars: int = 0, show_private_members: bool = False):
        """Constructor."""
        self.max_docstr_chars = max_docstr_chars
        self.show_private_members = show_private_members
        self.parser = parsing.ScopeTreeParser(parse_errored_root=True)
        self.signature_printer = signature_utils.SignaturePrinter(
            max_docstr_chars=max_docstr_chars,
            show_full_method_signatures=False,
        )

    def split_into_chunks(self, doc: chunking.Document) -> Iterable[chunking.Chunk]:
        """This function takes a document and returns a list of chunks."""
        lang = common.guess_lang_from_fp(doc.path)
        if (
            doc.path is None
            or lang is None
            or not signature_utils.is_signature_supported_lang(lang)
        ):
            return
        try:
            pfile = ParsedFile.parse(Path(doc.path), lang, doc.text)
            summary = FileSummary.from_pfile(pfile)
            sig_info = self.signature_printer.get_signature_info(
                summary, pfile, show_private=self.show_private_members
            )
            all_sigs = [sig_info.module_signature] + list(
                sig_info.symbol_signatures.values()
            )
        except (parsing.ParsingFailedError, parsing.FileTypeNotSupportedError):
            log.warning("Failed to parse file.")
            all_sigs = []
        for sig in all_sigs:
            yield chunking.Chunk(
                text=sig.text,
                parent_doc=doc,
                char_offset=sig.crange.start,
                line_offset=sig.lrange.start,
                length_in_lines=len(sig.lrange),
            )


class RobustSignatureChunker(chunking.Chunker):
    """Wraps SignatureChunker in a RobustExecutor to handle timeouts and segfaults."""

    def __init__(
        self,
        max_docstr_chars: int = 0,
        show_private_members: bool = False,
        timeout_s: float = 1,
    ):
        self._max_docstr_chars = max_docstr_chars
        self._show_private_members = show_private_members
        self._executor = RobustExecutor(timeout_s=timeout_s)
        self._lock = threading.Lock()

    def split_into_chunks(self, doc: chunking.Document) -> Iterable[chunking.Chunk]:
        # RobustExecutor isn't thread-safe
        with self._lock:
            try:
                return self._executor.run(
                    _split_into_chunks,
                    doc,
                    self._max_docstr_chars,
                    self._show_private_members,
                )
            except TimeoutError:
                log.exception("Chunk processing timed out.")
                raise chunking.ChunkingException(StatusCode.DEADLINE_EXCEEDED)
            except RuntimeError:
                log.exception("Chunk processing failed to complete.")
                raise chunking.ChunkingException(StatusCode.INTERNAL)


CachedSignatureChunker = functools.cache(SignatureChunker)
"""A cached version of SignatureChunker."""


def _split_into_chunks(
    doc: chunking.Document, max_docstr_chars: int, show_private_members: bool
) -> Iterable[chunking.Chunk]:
    """A wrapper around SignatureChunker.split_into_chunks for RobustExecutor."""
    # We get the cached version to minimize overhead of creating the chunker.
    chunker: SignatureChunker = CachedSignatureChunker(
        max_docstr_chars, show_private_members
    )
    # Iterators can't be pickled and returned across process boundaries, so we'll
    # convert to a list first.
    return list(chunker.split_into_chunks(doc))

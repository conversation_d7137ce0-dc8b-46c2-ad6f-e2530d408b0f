{"cells": [{"cell_type": "markdown", "id": "daebdb65-5bb1-4520-a33c-0a629ab5a23d", "metadata": {}, "source": ["abcdefg"]}, {"cell_type": "code", "execution_count": 3, "id": "db760a58-68b8-45f3-9871-f71895da7173", "metadata": {"vscode": {"languageId": "invalid_language"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["this is a very long text entry, please split across lines\n", "hello world\n"]}], "source": ["def test():\n", "    print(\"this is a very long text entry, please split across lines\")\n", "    print(\"hello world\")\n", "\n", "test()"]}], "metadata": {"kernelspec": {"display_name": "invalid_language", "language": "invalid_language", "name": "invalid_language"}, "language_info": {"name": "invalid_language"}}, "nbformat": 4, "nbformat_minor": 5}
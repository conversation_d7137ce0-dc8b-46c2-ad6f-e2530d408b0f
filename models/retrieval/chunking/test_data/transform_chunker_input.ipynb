{"cells": [{"cell_type": "markdown", "id": "daebdb65-5bb1-4520-a33c-0a629ab5a23d", "metadata": {}, "source": ["abcdefg"]}, {"cell_type": "code", "execution_count": 3, "id": "db760a58-68b8-45f3-9871-f71895da7173", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["this is a very long text entry, please split across lines\n", "hello world\n"]}], "source": ["def test():\n", "    print(\"this is a very long text entry, please split across lines\")\n", "    print(\"hello world\")\n", "\n", "\n", "test()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}
"""This file implements a line-level chunker."""

import pandas as pd

from collections.abc import Iterable

from models.retrieval.chunking import chunking
from base.retrieval.chunking.utils.jupyternb_conversion import (
    convert_file,
)


class TransformChunker(chunking.Chunker):
    """Split code and notebooks into chunks based on existing chunkers, with special support for Jupyter notebooks."""

    def __init__(self, chunker: chunking.Chunker):
        self.chunker = chunker

    def split_into_chunks(
        self, doc: chunking.Document, max_output_length: int = 0
    ) -> Iterable[chunking.Chunk]:
        try:
            if doc.path is not None and doc.path.endswith(".ipynb"):
                output = convert_file(doc.text, max_output_length=max_output_length)
                if output is None:
                    output = ""
            else:
                output = doc.text
        except Exception:
            output = doc.text

        textdoc = chunking.Document(
            blob_name=doc.blob_name, text=output, path=doc.path, language=doc.language
        )
        yield from self.chunker.split_into_chunks(textdoc)

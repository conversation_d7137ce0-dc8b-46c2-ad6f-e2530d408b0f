"""Line-level chunker implementation v2."""

from models.retrieval.chunking import chunking
from typing import Iterable
from base.retrieval.chunking import split_line_chunks


class LineLevelChunkerV2(chunking.Chunker):
    """Split a document into chunks based on line-level chunking.

    See `split_line_chunks` for the chunking strategy details.
    This class aims to eventually replace LineLevelChunker.
    """

    def __init__(
        self,
        max_lines_per_chunk: int,
        max_chunk_size: int,
        overlap_lines: int = 0,
        hard_line_limit: int = 2048,
    ):
        assert (
            max_lines_per_chunk > 0
        ), f"max_lines_per_chunk must be positive, got {max_lines_per_chunk}"
        assert (
            max_chunk_size > 0
        ), f"max_chunk_size must be positive, got {max_chunk_size}"
        assert (
            hard_line_limit > 0
        ), f"hard_line_limit must be positive, got {hard_line_limit}"
        self.max_lines_per_chunk = max_lines_per_chunk
        self.max_chunk_size = max_chunk_size
        self.overlap_lines = overlap_lines
        self.hard_line_limit = hard_line_limit

    def split_into_chunks(self, doc: chunking.Document) -> Iterable[chunking.Chunk]:
        """This function takes a document and returns a list of chunks."""
        for chunk_content in split_line_chunks(
            doc.text,
            self.max_lines_per_chunk,
            self.max_chunk_size,
            overlap_lines=self.overlap_lines,
            max_line_width=self.hard_line_limit,
        ):
            yield chunking.Chunk(
                text=chunk_content.text,
                parent_doc=doc,
                char_offset=chunk_content.char_offset,
                line_offset=chunk_content.line_offset,
                length_in_lines=chunk_content.length_in_lines,
                header=chunk_content.header,
            )

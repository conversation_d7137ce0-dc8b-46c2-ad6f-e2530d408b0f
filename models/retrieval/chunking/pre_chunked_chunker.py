"""This file implements a chunker for pre-chunked files."""

import json
from collections.abc import Iterable
from base.prompt_format.common import DocumentationMetadata
from models.retrieval.chunking import chunking


class PreChunkedChunker(chunking.Chunker):
    """Splits a document based a jsonl format."""

    def _get_chunk(self, doc: chunking.Document, chunk: dict) -> chunking.Chunk:
        (docset_name, source_id) = doc.get_docset_metadata()

        if docset_name or source_id:
            metadata = DocumentationMetadata(
                name=docset_name,
                page_id=chunk["page_id"],
                headers=chunk["headers"],
                source_id=source_id,
            )
        else:
            metadata = None

        c = chunking.Chunk(
            text=chunk["text"],
            parent_doc=doc,
            char_offset=chunk["char_offset"],
            line_offset=chunk["line_offset"],
            length_in_lines=chunk["length_in_lines"],
            header=chunk.get("header", ""),
            documentation_metadata=metadata,
        )
        return c

    def split_into_chunks(self, doc: chunking.Document) -> Iterable[chunking.Chunk]:
        """This function takes a document and returns a list of chunks.

        Note: About line_offset and lengths_in_lines: there are already conflicting implementations out there,
        this doesn't make any serious attempt to match any of the other implementations.
        """

        for line in doc.text.splitlines():
            if not line.strip():
                continue
            chunk = json.loads(line)

            yield self._get_chunk(doc, chunk)

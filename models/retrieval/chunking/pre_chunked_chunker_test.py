import pathlib
from typing import Iterable

from models.retrieval.chunking import pre_chunked_chunker
from models.retrieval.chunking.chunking import Chunk, Document


def test_split_into_chunks():
    """Tests the basic pre-chunked chunking behavior."""
    text = pathlib.Path(
        "models/retrieval/chunking/test_data/pre_chunked.jsonl"
    ).read_text(encoding="utf8")
    doc = Document(blob_name="1", text=text)
    chunker = pre_chunked_chunker.PreChunkedChunker()
    chunks = list(chunker.split_into_chunks(doc))

    assert len(chunks) == 4
    assert chunks[0].text.startswith(
        "\n# Docs for Git (Git)\n\n##  Create\n\nClone an existin"
    )
    assert chunks[0].char_offset == 0
    assert chunks[0].line_offset == 0
    assert chunks[0].length_in_lines == 89
    assert chunks[0].length == 1614

    assert chunks[1].text.startswith("# Docs for Git (Git)\n\n##  Commit History\n\nSh")
    assert chunks[1].char_offset == 1614
    assert chunks[1].line_offset == 89
    assert chunks[1].length_in_lines == 86
    assert chunks[1].length == 1443


def test_split_into_chunks_with_docset_metadata():
    """Tests the basic pre-chunked chunking behavior."""
    text = pathlib.Path(
        "models/retrieval/chunking/test_data/pre_chunked.jsonl"
    ).read_text(encoding="utf8")
    doc = Document(blob_name="1", text=text, metadata={"docset_name": "Git"})
    chunker = pre_chunked_chunker.PreChunkedChunker()
    chunks = list(chunker.split_into_chunks(doc))

    assert len(chunks) == 4
    assert chunks[0].text.startswith(
        "\n# Docs for Git (Git)\n\n##  Create\n\nClone an existin"
    )
    assert chunks[0].char_offset == 0
    assert chunks[0].line_offset == 0
    assert chunks[0].length_in_lines == 89
    assert chunks[0].length == 1614
    m = chunks[0].documentation_metadata
    assert m is not None
    assert m.name == "Git"
    assert m.page_id == "1"
    assert m.headers == ["#Header1", "##Header2"]

    assert chunks[1].text.startswith("# Docs for Git (Git)\n\n##  Commit History\n\nSh")
    assert chunks[1].char_offset == 1614
    assert chunks[1].line_offset == 89
    assert chunks[1].length_in_lines == 86
    assert chunks[1].length == 1443
    m = chunks[1].documentation_metadata
    assert m is not None
    assert m.name == "Git"
    assert m.page_id == "1"
    assert m.headers == []

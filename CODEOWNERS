# File format:
# https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners
#
# The people on this list will be automatically added to reviews. It is
# considered good practice to make sure one of them chimes in on your
# review.

/CODEOWNERS  @csapuntz @marcmac @dmeister

/.pre-commit-config.yaml @marcmac @dmeister

/Cargo.toml @dmeister

/go.mod @dmeister

/base/feature_flags/  @csapuntz @dmeister

/clients/common/webviews/src/design-system/ @gauntface @Harry-Dang @ultraeric

/clients/vim/ @mtpauly @richhankins

/data/dashboards/ @jspeiser

/services/api_proxy/public_api.proto @dmeister

/services/auth/  @csapuntz @D-E-Stephenson

/services/bigtable_proxy/ @augmentluke

/services/chat_host/ @ranhalprin @Edwardong

/services/content_manager @dmeister

/services/customer/ @refactornator @dmaskasky

/services/embeddings_search_host @dmeister @augmentluke

/services/integrations/ @surbhiijain @virtualzx-nad

/services/request_insight/ @jspeiser @nikita-sirohi

/services/request_insight/request_insight.proto @julesybe

/services/settings @dmeister

/services/tenant_watcher @aswink

/services/third_party_arbiter/ @ranhalprin @Edwardong

/services/token_exchange @aswink

/tools/backup/bigtable  @csapuntz

/tools/bazel_runner @dmeister

/tools/deploy_runner @dmeister

/tools/python_deps/requirements.txt @dmeister

/base/prompt_format_chat/structured_binks_agent_prompt_formatter.py @bddavis @devangjhabakh

skips: [
  # https://bandit.readthedocs.io/en/latest/plugins/b101_assert_used.html
  # allows assert temporilty. Should be enabled at a later time
  "B101",
  # it is okay to use subprocess. Some forms of subprocess are not safe, but it is not
  # an issue per se.
  "B404", "B603",
  # TOOD(dirk): Fix these. AU-1308
  "B607",
  # there are many valid cases for non-cryptographic random generators
  "B311",
  # binding to 0.0.0.0 is recommended practice in k8s: https://discuss.kubernetes.io/t/security-implications-of-binding-server-to-127-0-0-1-vs-0-0-0-0-vs-pod-ip/13880
  "B104"]

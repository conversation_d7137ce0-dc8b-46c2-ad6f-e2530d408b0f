// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file clients/common/webviews/protos/chat_messages.proto (package com.augmentcode.common.webviews.protos, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { ChatHistoryItem } from "../../../sidecar/node-process/protos/chat_pb.js";

/**
 * @generated from message com.augmentcode.common.webviews.protos.SaveChatRequest
 */
export declare class SaveChatRequest extends Message<SaveChatRequest> {
  /**
   * @generated from field: com.augmentcode.common.webviews.protos.SaveChatRequestData data = 1;
   */
  data?: SaveChatRequestData;

  constructor(data?: PartialMessage<SaveChatRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.common.webviews.protos.SaveChatRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SaveChatRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SaveChatRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SaveChatRequest;

  static equals(a: SaveChatRequest | PlainMessage<SaveChatRequest> | undefined, b: SaveChatRequest | PlainMessage<SaveChatRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.common.webviews.protos.SaveChatRequestData
 */
export declare class SaveChatRequestData extends Message<SaveChatRequestData> {
  /**
   * @generated from field: string conversationId = 1;
   */
  conversationId: string;

  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.chat.ChatHistoryItem chatHistory = 2;
   */
  chatHistory: ChatHistoryItem[];

  /**
   * @generated from field: string title = 3;
   */
  title: string;

  constructor(data?: PartialMessage<SaveChatRequestData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.common.webviews.protos.SaveChatRequestData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SaveChatRequestData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SaveChatRequestData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SaveChatRequestData;

  static equals(a: SaveChatRequestData | PlainMessage<SaveChatRequestData> | undefined, b: SaveChatRequestData | PlainMessage<SaveChatRequestData> | undefined): boolean;
}


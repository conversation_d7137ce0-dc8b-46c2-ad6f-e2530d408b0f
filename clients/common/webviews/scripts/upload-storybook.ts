#!/usr/bin/env -S pnpm tsx
/* eslint-disable no-console */
import { Storage, StorageOptions } from "@google-cloud/storage";
import * as fs from "fs/promises";
import { userInfo } from "node:os";
import { promisify } from "node:util";
import { exec } from "node:child_process";
import { fileURLToPath } from "url";
import readline from "node:readline";
import path from "node:path";

const execAsync = promisify(exec);

async function getCurrentBranchName() {
  const [branchName, shortHash] = await Promise.all([
    execAsync("git rev-parse --abbrev-ref HEAD", { encoding: "utf8" }),
    execAsync("git rev-parse --short=5 HEAD", { encoding: "utf8" }),
  ]);
  return `${branchName.stdout.trim()}-${shortHash.stdout.trim()}`;
}

async function* readFiles(directory = "."): AsyncGenerator<string> {
  for (const entry of await fs.readdir(directory, { withFileTypes: true })) {
    const fullPath = path.join(directory, entry.name);
    if (entry.isDirectory()) {
      yield* readFiles(fullPath);
    } else if (entry.isFile()) {
      yield fullPath;
    }
  }
}

function green(text: string) {
  return `\x1b[32m${text}\x1b[0m`;
}

function red(text: string) {
  return `\x1b[31m❌ ${text}\x1b[0m`;
}

function debug(text: string, ...args: unknown[]) {
  if (process.env.DEBUG) {
    console.log(text, ...args);
  }
}

async function processInBatches<T>(
  items: AsyncIterable<T>,
  handler: (item: T) => Promise<unknown>,
  batchSize: number = 20,
): Promise<void> {
  let batch: T[] = [];
  let i = 0;
  function proc() {
    if (process.env.DEBUG || !process.stdout.isTTY) {
      return;
    }
    process.stdout.write(`Processed  ${green(String(i))} items`);
    readline.cursorTo(process.stdout, 0); //move cursor to start of line
  }

  for await (const it of items) {
    i++;
    batch.push(it);
    if (batch.length < batchSize) {
      continue;
    }
    //copy and empty batch
    await Promise.all(batch.splice(0).map(handler));
    proc();
  }
  //anything left in batch
  if (batch.length) {
    await Promise.all(batch.map(handler));
  }
  proc();
  console.log(`\n✅ All (${green(String(i))}) items processed`);
}

export async function uploadStorybook(
  replaceFiles = false,
  bucketName: string,
  pathPrefix: string,
  storybookBuildDir = "storybook-static",
) {
  console.log(`Current prefix: ${green(pathPrefix ?? "")} `);

  let storageArgs: StorageOptions = {};
  if (process.env.CI_OBJECT_STORE_CONFIG) {
    storageArgs = {
      projectId: "augment-research-gsc",
      credentials: JSON.parse(process.env.CI_OBJECT_STORE_CONFIG),
    };
    console.log(`Using object store config: ${green(JSON.stringify(storageArgs))}`);
  }
  console.log("Connecting to Google Cloud Storage...");
  const storage = new Storage(storageArgs);
  console.log(`Checking bucket... ${green(bucketName)}`);
  let bucket = storage.bucket(bucketName);

  // Check if bucket exists, create if it doesn't
  const [exists] = await bucket.exists();
  if (!exists) {
    console.log(`Creating bucket ${bucketName}...`);
    await storage.createBucket(bucketName, {
      location: "US",
      standard: true,
    });
    console.log(`✅ Bucket ${green(bucketName)} created.`);
  } else {
    console.log(`✅ Bucket ${green(bucketName)} already exists.`);
  }

  let allFiles = new Set<string>();
  if (!replaceFiles) {
    console.log(`Checking for existing files in ${green(bucketName)}/${green(pathPrefix)}...`);
    allFiles = new Set<string>(
      (
        await bucket.getFiles({
          prefix: pathPrefix,
        })
      )[0]?.map((v) => v.name),
    );
    debug("existing files %s", allFiles);
    console.log(
      `Bucket ${green(bucketName)}/${green(pathPrefix)} contains ${green(String(allFiles.size))} files.`,
    );
  }

  const upload = async (source: string, destination: string) => {
    if (allFiles.has(destination)) {
      debug(`Skipping: ${source} -> ${destination}`);
      return false;
    }
    await bucket.upload(source, {
      destination,
      public: false,

      metadata: {
        cacheControl: "public, max-age=31536000",
      },
    });
    debug(`Uploading: ${source} -> ${destination}`);
    return true;
  };

  upload("mocks/robots.txt", "robots.txt");
  console.log("Starting Storybook upload...");
  await processInBatches(readFiles(storybookBuildDir), (fullPath) =>
    upload(fullPath, path.join(pathPrefix, path.relative(storybookBuildDir, fullPath))),
  );

  let urlBase = `https://storage.googleapis.com/${bucketName}/`;
  if (bucketName === "gcp-us1-public-html") {
    urlBase = "https://webserver.gcp-us1.r.augmentcode.com/";
  }
  console.log(`
✅ Upload complete!
Your Storybook is available at:

${green(`${urlBase}${pathPrefix ? "/" + pathPrefix : ""}/index.html`)}
`);
}

export async function main(args = process.argv.slice(2)) {
  process.on("unhandledRejection", (err) => {
    console.log(red("An error occured:"));
    console.error(err);
    process.exit(1);
  });
  const defaultPathPrefix = await getCurrentBranchName();
  const defaultBucket = `augment-${userInfo().username}-storybook`;
  const replaceFiles = args.includes("--replace");
  args = args.filter((arg) => arg !== "--replace");

  if (args.length > 3) {
    console.log(red("Too many arguments, run with --help for help"));
    process.exit(1);
  }
  if (!args[0]) {
    //most not be empty string
    args[0] = defaultBucket;
  }
  //may want an empty branch name.
  if (args[1] == null) {
    //can be empty string
    args[1] = defaultPathPrefix;
  }

  try {
    if (args.includes("--help") || args.includes("-h")) {
      console.log(`
Usage: upload-storybook [bucketName] [pathPrefix] [storybookBuildDir]

bucketName: The name of the Google Cloud Storage bucket. Default is '${defaultBucket}' will create if does not exist.
pathPrefix: The name of the path prefix to use for the upload. Default is  '${defaultPathPrefix}'.
storybookBuildDir: The directory where the Storybook build is located. Default is 'storybook-static'.
`);
      process.exit(1);
    }

    await uploadStorybook(replaceFiles, ...(args as [string, string, string]));
  } catch (error) {
    console.error(red("Error uploading Storybook:"), error);
    process.exit(1);
  }
}
// Run if called directly
if (
  //eslint-disable-next-line @typescript-eslint/ban-ts-comment
  //@ts-ignore
  path.resolve(fileURLToPath(import.meta.url)) === path.resolve(process.argv[1])
) {
  main().catch((err) => {
    console.error(err);
    process.exit(1);
  });
}

<script lang="ts">
  import "$common-webviews/src/common/css/reset.css";
  import "$common-webviews/mocks/hosts/monaco-init";

  import { injectCSSStyles, setMockTheme } from "../mocks/hosts/mock-host";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";

  /**
   * This component thats the theme attribute from storybook and sets up the
   * necessary attributes and injects the styles to emulate a clients theme
   * and styles. This component should shield the component from storybook
   * styling. It also provides an adaptor to this web-component to svelte.
   */

  export let theme: string;

  $: {
    setMockTheme(theme as any);
    injectCSSStyles(theme as any, document.body);
  }
  setMockTheme(theme as any);
  injectCSSStyles(theme as any, document.body);
</script>

<MonacoProvider.Root>
  <div class="augment-story-wrapper">
    <slot />
  </div>
</MonacoProvider.Root>

<style>
  :root {
    /* This is needed to mimic the VSCode clients
    line-height */
    font-size: var(--augment-font-size);
    height: 100%;
  }

  /* Ensure the body allows scrolling */
  :global(body) {
    overflow-y: auto;
    height: 100%;
  }

  /* Ensure the Storybook iframe allows scrolling */
  :global(#storybook-root) {
    height: auto;
    min-height: 100%;
  }

  .augment-story-wrapper {
    display: flex;
    flex-direction: column;
    padding: 20px;
    background-color: var(--augment-window-background);
    width: 100%;
    /* Allow content to determine height, don't force min-height */
    min-height: fit-content;
  }

  /* Remove horizontal padding for fullscreen chat components that need full viewport */
  .augment-story-wrapper:has(.chat),
  .augment-story-wrapper:has(.l-chat-wrapper) {
    padding-right: 0px;
    padding-left: 0px;
    height: 100vh;
    min-height: 100vh;
  }
</style>

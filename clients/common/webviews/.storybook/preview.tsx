import { UserThemeCategory } from "$common-webviews/src/common/hosts/user-themes/augment-theme-attributes";

import type { Preview } from "@storybook/react";

import "$common-webviews/src/design-system/_libs/design-system-init";
import { mockThemeCategory as getCategoryForMockTheme, MockTheme } from "../mocks/hosts/mock-host";
import AugmentStoryWrapper from "./AugmentStoryWrapper.svelte";

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    options: {
      storySort: {
        method: "alphabetical",
        order: ["app", "components", "design system"],
      },
    },
  },

  decorators: [
    (_, ctx) => {
      const mockTheme = ctx.parameters.theme || ctx.globals.theme || MockTheme.vscodeDracula;
      return { Component: AugmentStoryWrapper, props: { theme: mockTheme } } as any;
    },
  ],

  globalTypes: {
    theme: {
      name: "Client Theme",
      description: "Global theme for components",
      defaultValue: MockTheme.vscodeDracula,
      toolbar: {
        // The icon for the toolbar item
        icon: "circlehollow",
        // Array of options
        items: Object.values(MockTheme).map((value) => {
          return {
            icon: getCategoryForMockTheme(value) === UserThemeCategory.dark ? "moon" : "sun",
            title: value,
            value,
          };
        }),
        // Property that specifies if the name of the item will be displayed
        showName: true,
      },
    },
  },
};
export default preview;

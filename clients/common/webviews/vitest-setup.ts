// Cleanup step to reset vitest before next iteration
import "vitest";
import "@testing-library/jest-dom";

import { beforeEach, vi } from "vitest";

// Always use the mock for host
vi.mock("$common-webviews/src/common/hosts/host");

beforeEach(() => {
  // Restoring all mocks will restore original implementations of spys
  vi.restoreAllMocks();
});

// Needed for tests referencing MonacoEditor to load
document.queryCommandSupported = vi.fn().mockImplementation(() => false);

// Mock ResizeObserver globally
class ResizeObserverMock {
  observe = vi.fn();
  unobserve = vi.fn();
  disconnect = vi.fn();
}

global.ResizeObserver = ResizeObserverMock;

// Mock IntersectionObserver globally
class IntersectionObserverMock implements IntersectionObserver {
  readonly root: Element | null = null;
  readonly rootMargin: string = "0px";
  readonly thresholds: ReadonlyArray<number> = [0];

  constructor(
    private callback: IntersectionObserverCallback,
    private options?: IntersectionObserverInit,
  ) {}

  observe = vi.fn();
  unobserve = vi.fn();
  disconnect = vi.fn();
  takeRecords = vi.fn().mockReturnValue([]);
}

global.IntersectionObserver = IntersectionObserverMock as unknown as typeof IntersectionObserver;

load("@aspect_rules_js//js:defs.bzl", "js_library", "js_run_binary", "js_run_devserver", "js_test")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("@npm//clients/common/webviews:vite/package_json.bzl", vite_bin = "bin")
load("@npm//clients/common/webviews:vitest/package_json.bzl", vitest_bin = "bin")
load("@npm//clients/common/webviews:svelte-check/package_json.bzl", svelte_check_bin = "bin")

npm_link_all_packages()

js_library(
    name = "src_files",
    srcs = glob(["src/design-system/icons/augment/rive/*.riv"]) + glob([
        "src/**/*.ts",
        "src/**/*.svelte",
        "src/**/*.css",
        "src/**/*.svg",
        "src/**/*.mp4",
        "src/**/*.json",
        "src/**/*.woff",
        "*.html",
    ]) + [
        "package.json",
        ":node_modules/@bufbuild/protobuf",
        ":node_modules/@connectrpc/connect",
        ":node_modules/@magidoc/plugin-svelte-marked",
        ":node_modules/@popperjs/core",
        ":node_modules/@tiptap/core",
        ":node_modules/@types/diff",
        ":node_modules/@types/lodash",
        ":node_modules/@types/lodash.clonedeep",
        ":node_modules/@types/lodash.debounce",
        ":node_modules/@types/lodash.throttle",
        ":node_modules/diff",
        ":node_modules/fuse.js",
        ":node_modules/github-slugger",
        ":node_modules/highlight.js",
        ":node_modules/lodash",
        ":node_modules/lodash.clonedeep",
        ":node_modules/lodash.debounce",
        ":node_modules/lodash.throttle",
        ":node_modules/marked",
        ":node_modules/monaco-editor",
        ":node_modules/prosemirror-model",
        ":node_modules/tippy.js",
        ":tsconfig",
        ":tsconfig_node",
        "//clients:tsconfig",
        "//clients/sidecar/libs:ts",
        "//clients/sidecar/libs/protos:sidecar_libs_ts_protos",
    ],
    deps = [
        "//clients/vscode:shared_webview_files",
    ],
)

js_library(
    name = "shared_src_files_common",
    srcs = [
        # TSConfigs
        ":tsconfig",
        ":tsconfig_node",

        # Host files
        "src/common/hosts/host.ts",
        "src/common/hosts/create-host.ts",
        "src/common/hosts/vscode/vscode.ts",
        "src/common/hosts/host-types.ts",

        # Models
        "src/common/models/focus-model.ts",

        # CSS Files
        "src/common/css/reset.css",
        "src/common/css/_z-index.css",
        "src/common/css/variables.css",
    ],
)

js_library(
    name = "shared_src_files_monaco",
    srcs = [
        # Utils
        "src/common/utils/monaco-theme.ts",
        "src/common/hosts/user-themes/augment-theme-attributes.ts",
        "src/common/hosts/user-themes/theme-store.ts",

        # Monaco Provider
        "src/design-system/components/MonacoProvider/MonacoProvider.ts",
        "src/design-system/components/MonacoProvider/MonacoContext.ts",
        "src/design-system/components/MonacoProvider/SimpleMonaco.svelte",
        "src/design-system/components/MonacoProvider/types.ts",
        "src/design-system/components/MonacoProvider/index.ts",

        # Node modules
        ":node_modules/highlight.js",
        ":node_modules/monaco-editor",
    ],
)

# TSConfig for the project source files
ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    visibility = [
        "//clients:__pkg__",
    ],
    deps = [
        ":node_modules/@tsconfig/svelte",
        ":tsconfig_node",
    ],
)

# TSConfig used for the vite.config.ts file
ts_config(
    name = "tsconfig_node",
    src = "tsconfig.node.json",
)

CONFIGS = [
    ".eslintrc.cjs",  # Eslint config for this project
    "package.json",  # Lists deps/scripts/etc.
    "svelte.config.js",  # Svelte config for this project
    "vite.config.ts",  # Vite config for this project
    "vitest-setup.ts",  # Run before each vitest test
    ":tsconfig",  # Used as tsconfig for this project
    ":tsconfig_node",  # Used for vite config.ts files
    "//clients:tsconfig",  # Shared tsconfig target
]

VITE_PLUGINS = [
    "scripts/vite-plugins/inline-monaco-bootstrap.ts",
    "scripts/vite-plugins/monaco-bootstrap.js",
]

# Configure the tool used to run the webview apps binary
vite_bin.vite_binary(
    name = "vite",
    chdir = package_name(),
    data = [":node_modules"] + CONFIGS + VITE_PLUGINS,
)

js_run_binary(
    name = "webview-apps",
    srcs = [
        ":src_files",
        # The source files are needed to make svelte check find the correct
        # types. With the .js and .d.ts files, svelte-check seems to ignore
        # the .d.ts files.
        "//clients/sidecar/libs:src",
        "//clients/sidecar/libs/protos:sidecar_libs_ts_protos",
    ],
    args = [
        "build",
        "-c",
        "vite.config.ts",
    ],
    out_dirs = ["dist/"],
    tool = ":vite",
    visibility = ["//clients/vscode:__subpackages__"],
)

# Just verify that the bundle produced "something reasonable" but doesn't
# verify it functions in a browser.
js_test(
    name = "build_smoke_test",
    timeout = "short",
    data = CONFIGS + VITE_PLUGINS + [":webview-apps"],
    entry_point = "build-smoke-test.js",
)

svelte_check_bin.svelte_check_test(
    name = "svelte_check_test",
    args = [
        "--workspace",
        package_name(),
        "--tsconfig",
        "tsconfig.json",
        "--fail-on-warnings",
    ],
    data = [
        ":node_modules",
        ":src_files",

        # The source files are needed to make svelte check find the correct
        # types. With the .js and .d.ts files, svelte-check seems to ignore
        # the .d.ts files.
        "//clients/sidecar/libs:src",
    ] + CONFIGS + VITE_PLUGINS + glob(["mocks/**/*"]),
    include_transitive_types = True,  # Needed for type checking
)

vitest_bin.vitest_test(
    name = "vitest_test",
    args = [
        "run",
        "--config=vite.config.ts",
    ],
    chdir = package_name(),
    data = [
        ":node_modules",
        ":src_files",
        "//clients/vscode:shared_webview_files",

        # The source files are needed to access some types.
        "//clients/sidecar/libs:src",
    ] + CONFIGS + VITE_PLUGINS + glob(["mocks/**/*"]),
)

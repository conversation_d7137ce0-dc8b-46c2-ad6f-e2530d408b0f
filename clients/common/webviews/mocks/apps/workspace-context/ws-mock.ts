import {
  type AsyncWebViewMessage,
  type WebViewMessage,
  WebViewMessageType,
  WSContextFileInclusionState,
  type WSContextFileItem,
  type WSContextGetChildrenRequestData,
  type WSContextSourceFolder,
} from "$vscode/src/webview-providers/webview-messages";
import { isCustomEvent, mockHost } from "../../hosts/mock-host";

function wrapAsyncMsg<ReqT extends WebViewMessage, ResT extends WebViewMessage>(
  request: AsyncWebViewMessage<ReqT>,
  baseResponse: ResT | null,
  error: string | null = null,
): AsyncWebViewMessage<ResT> {
  return {
    type: WebViewMessageType.asyncWrapper,
    requestId: request.requestId,
    error,
    baseMsg: baseResponse,
  };
}

// but don't wait on WScontext requests since
// those aren't going to the network.
const NON_DELAY_ASYNC_RESPONSES = new Set([
  WebViewMessageType.wsContextGetChildrenRequest,
  WebViewMessageType.wsContextGetSourceFoldersRequest,
]);

mockHost.addEventListener("message-from-webview", async (e: Event) => {
  if (!isCustomEvent(e)) {
    console.error("Unexpected event type: ", e);
    throw new Error("Unexpected mock message");
  }

  const request = e.detail;

  // Wait a random duration between 0.5 and 1.5 seconds
  // but don't wait on non-network requests listed in the
  // NON_DELAY_ASYNC_RESPONSES
  if (
    !(
      request.type === WebViewMessageType.asyncWrapper &&
      NON_DELAY_ASYNC_RESPONSES.has(request.baseMsg.type)
    )
  ) {
    await new Promise((resolve) => setTimeout(resolve, 200 + Math.random() * 300));
  }

  switch (request.type) {
    case WebViewMessageType.asyncWrapper:
      switch (request.baseMsg.type) {
        case WebViewMessageType.wsContextGetSourceFoldersRequest:
          console.log("wsContextGetWorkspaceFoldersRequest message received: ", request);
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: WebViewMessageType.wsContextGetSourceFoldersResponse,
                data: {
                  workspaceFolders: [
                    {
                      name: "asdf",
                      inclusionState: WSContextFileInclusionState.included,
                      fileId: {
                        folderRoot: "asdf",
                        relPath: "",
                      },
                      isWorkspaceFolder: true,
                      isNestedFolder: false,
                      isPending: false,
                      trackedFileCount: 10,
                    },
                    {
                      name: "asdf2",
                      fileId: {
                        folderRoot: "asdf2",
                        relPath: "",
                      },
                      inclusionState: WSContextFileInclusionState.partial,
                      isWorkspaceFolder: true,
                      isNestedFolder: false,
                      isPending: false,
                      trackedFileCount: 11,
                    },
                    {
                      name: "asdf3",
                      fileId: {
                        folderRoot: "asdf2",
                        relPath: "asdf3",
                      },
                      inclusionState: WSContextFileInclusionState.partial,
                      isWorkspaceFolder: true,
                      isNestedFolder: true,
                      isPending: false,
                      trackedFileCount: 12,
                    },
                    {
                      name: "really-long-folder-name-that-is-longer-than-the-ui-can-render",
                      fileId: {
                        folderRoot: "asdf2",
                        relPath: "really-long-folder-name-that-is-longer-than-the-ui-can-render",
                      },
                      inclusionState: WSContextFileInclusionState.partial,
                      isWorkspaceFolder: false,
                      isNestedFolder: true,
                      isPending: false,
                      trackedFileCount: 13,
                    },
                    {
                      name: "a-pending-folder",
                      fileId: {
                        folderRoot: "a-pending-folder",
                        relPath: "",
                      },
                      inclusionState: WSContextFileInclusionState.partial,
                      isWorkspaceFolder: false,
                      isNestedFolder: false,
                      isPending: true,
                      trackedFileCount: undefined,
                    },
                  ] as WSContextSourceFolder[],
                },
              },
              null,
            ),
          );
          break;
        case WebViewMessageType.wsContextGetChildrenRequest:
          console.log("wsContextGetChildrenRequest message received: ", request);
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: WebViewMessageType.wsContextGetChildrenResponse,
                data: {
                  children: [
                    {
                      name: "asdf",
                      fileId: {
                        folderRoot: (request.baseMsg.data as WSContextGetChildrenRequestData).fileId
                          .folderRoot,
                        relPath:
                          (request.baseMsg.data as WSContextGetChildrenRequestData).fileId.relPath +
                          "/asdf",
                      },
                      type: "file",
                      inclusionState: WSContextFileInclusionState.included,
                      reason: "asdf",
                    },
                    {
                      name: "asdf2",
                      fileId: {
                        folderRoot: (request.baseMsg.data as WSContextGetChildrenRequestData).fileId
                          .folderRoot,
                        relPath:
                          (request.baseMsg.data as WSContextGetChildrenRequestData).fileId.relPath +
                          "/asdf2",
                      },
                      type: "folder",
                      inclusionState: WSContextFileInclusionState.partial,
                      reason: "asdf2",
                    },
                    {
                      name: "asdf3",
                      fileId: {
                        folderRoot: (request.baseMsg.data as WSContextGetChildrenRequestData).fileId
                          .folderRoot,
                        relPath:
                          (request.baseMsg.data as WSContextGetChildrenRequestData).fileId.relPath +
                          "/asdf3",
                      },
                      type: "file",
                      inclusionState: WSContextFileInclusionState.excluded,
                      reason: "asdf2",
                    },
                    {
                      name: "asdf4",
                      fileId: {
                        folderRoot: (request.baseMsg.data as WSContextGetChildrenRequestData).fileId
                          .folderRoot,
                        relPath:
                          (request.baseMsg.data as WSContextGetChildrenRequestData).fileId.relPath +
                          "/asdf4",
                      },
                      type: "folder",
                      inclusionState: WSContextFileInclusionState.excluded,
                      reason: "asdf2",
                    },
                  ] as WSContextFileItem[],
                },
              },
              null,
            ),
          );
          break;
      }
      break;
    default: {
      console.error(`Unknown message type: ${request.type}`);
      break;
    }
  }
});

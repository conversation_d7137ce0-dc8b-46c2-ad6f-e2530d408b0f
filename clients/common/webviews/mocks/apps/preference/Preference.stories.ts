/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import component from "./PreferenceMock.svelte";
import { withMonaco } from "$common-webviews/mocks/design-system/monaco-provider/monaco-decorator";

const meta = {
  title: "app/Preference",
  component,
  tags: ["autodocs"],
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

const markdownResponse = `Here's how you can implement a binary search tree in TypeScript:

\`\`\`typescript
class TreeNode {
    constructor(
        public value: number,
        public left: TreeNode | null = null,
        public right: TreeNode | null = null
    ) {}
}

class BinarySearchTree {
    private root: TreeNode | null = null;

    insert(value: number): void {
        const newNode = new TreeNode(value);

        if (!this.root) {
            this.root = newNode;
            return;
        }

        let current = this.root;
        while (true) {
            if (value < current.value) {
                if (!current.left) {
                    current.left = newNode;
                    break;
                }
                current = current.left;
            } else {
                if (!current.right) {
                    current.right = newNode;
                    break;
                }
                current = current.right;
            }
        }
    }
}
\`\`\`

This implementation includes:
- A \`TreeNode\` class for individual nodes
- A \`BinarySearchTree\` class with an \`insert\` method
- Type-safe implementation using TypeScript

You can use it like this:

\`\`\`typescript
const bst = new BinarySearchTree();
bst.insert(5);
bst.insert(3);
bst.insert(7);
\`\`\``;

const alternativeResponse = `Here's an alternative implementation using a more functional approach:

\`\`\`typescript
interface TreeNode {
    value: number;
    left: TreeNode | null;
    right: TreeNode | null;
}

const createNode = (value: number): TreeNode => ({
    value,
    left: null,
    right: null
});

const insert = (tree: TreeNode | null, value: number): TreeNode => {
    if (!tree) {
        return createNode(value);
    }

    if (value < tree.value) {
        return {
            ...tree,
            left: insert(tree.left, value)
        };
    }

    return {
        ...tree,
        right: insert(tree.right, value)
    };
};

// Usage example:
const tree = [5, 3, 7].reduce(
    (acc, val) => insert(acc, val),
    null as TreeNode | null
);
\`\`\`

Key differences in this approach:
1. Uses **immutable** data structures
2. Implements a *functional* programming style
3. Leverages TypeScript's type system for better type inference

The trade-offs are:
- More memory usage due to immutability
- Potentially better testing and debugging
- Easier to reason about state changes`;

export const Example: Story = {
  args: {
    input: {
      type: "Chat",
      data: {
        a: {
          message: "How do I implement a binary search tree in TypeScript?",
          response: markdownResponse,
          occuredAt: new Date(),
        },
        b: {
          message: "How do I implement a binary search tree in TypeScript?",
          response: alternativeResponse,
          occuredAt: new Date(),
        },
      },
      enableRetrievalDataCollection: true,
    },
  },
  decorators: [withMonaco],
};

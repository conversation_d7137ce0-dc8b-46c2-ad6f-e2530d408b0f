import { files } from "$common-webviews/mocks/components/code-roll/samples";
import {
  assertAsyncWebviewReadFileRequestMessage,
  assertCustomEvent,
  assertWebviewMessage,
  mockHost,
} from "$common-webviews/mocks/hosts/mock-host";
import { areSameSuggestion } from "$common-webviews/src/common/components/suggestion-tree/navigation-utils";
import {
  ChangeType,
  type IEditSuggestion,
  NextEditMode,
  NextEditScope,
  SuggestionState,
} from "$vscode/src/next-edit/next-edit-types";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import type { FileReader } from "$common-webviews/src/common/components/code-roll/types";

import multiSuggestionsRaw from "./results/multi-1.json?raw";
import multiFile from "./results/multi-1.txt?raw";
import JSON5 from "json5";
const multiSuggestions = JSON5.parse(multiSuggestionsRaw) as IEditSuggestion[];
export function getMultiFile() {
  return multiSuggestions;
}
const allFiles = files();
allFiles[multiSuggestions[0].qualifiedPathName.relPath] = multiFile;

const randFileNameParts = ["foo", "bar", "baz", "qux", "quux", "quuz", "corge"];
const randFileNameExtensions = [".ts", ".js", ".py", ".java", ".cpp", ".c"];
const randFileNamePartsLength = randFileNameParts.length;

export const readFile: FileReader = function (path) {
  if (!allFiles[path.relPath]) {
    throw new Error("File not found");
  }
  return new Promise((resolve) => setTimeout(() => resolve(allFiles[path.relPath]), 100));
};

export function onMockMulti() {
  suggestions = multiSuggestions;
  mockHost.sendMessageToWebView({
    type: WebViewMessageType.nextEditSuggestionsChanged,
    data: {
      suggestions: multiSuggestions,
    },
  });
}

const randomFileName = () => {
  const parts = [];
  for (let i = 0; i < Math.random() * randFileNameParts.length; i++) {
    parts.push(randFileNameParts[Math.floor(Math.random() * randFileNamePartsLength)]);
  }
  return (
    parts.join("/") +
    randFileNameExtensions[Math.floor(Math.random() * randFileNameExtensions.length)]
  );
};
let scoredLocationId = 0;
const scoredLocation = (
  () =>
  (
    path = randomFileName(),
    changeDescription = "Updated function call",
    range = { start: 5, stop: 15 },
    originalCode = "oldFunction()",
    modifiedCode = "newFunction()",
    extraContextAfter = ["\n", "  // Some other code\n"],
    extraContextBefore = ["\n", "  // Some other code\n"],
  ) => ({
    suggestionId: `suggestion-${scoredLocationId++}`,
    location: {
      path,
      range,
    },
    originalCode,
    modifiedCode,
    changeDescription,
    extraContextAfter,
    extraContextBefore,
  })
)();

export const setSuggestions = (s: IEditSuggestion[]) => {
  suggestions = s;
};

let locations = () => [
  scoredLocation("/path/to/ProgressApp.swift", "Add isCritical: true when logging audio session"),
  scoredLocation("/path/to/ProgressApp.swift", "Add isCritical: false trying to apply mask"),

  scoredLocation(
    "/path/to/BackgroundRemover.swift",
    "Add isCritical: true when logging audio session",
  ),
  scoredLocation("/path/to/BackgroundRemover.swift", "Add isCritical: false trying to apply mask"),
  scoredLocation("/path/to/BackgroundRemover.swift", "Add isCritical: true when unable to remove…"),
  scoredLocation("/path/to/PaywallView.swift", "Add isCritical: true when unable to remove…"),
  scoredLocation("/path/to/PaywallView.swift", "Add isCritical: false trying to apply mask"),
];

let suggestionId = 0;
const suggestion = (
  (id = 0) =>
  (location = scoredLocation()): IEditSuggestion => ({
    requestId: "mock-request-id_" + id++,
    mode: NextEditMode.Foreground,
    scope: NextEditScope.File,

    result: {
      suggestionId: location.suggestionId + ("_" + suggestionId++),
      path: location.location.path,
      blobName: "",
      charStart: location.location.range.start,
      charEnd: location.location.range.stop,
      existingCode: location.originalCode ?? "code",
      suggestedCode: location.modifiedCode ?? "suggested code",
      changeDescription: location.changeDescription ?? "make some good changes",
      diffSpans: [], // You might want to generate this based on the original and modified code
      editingScore: 0, // You might want to provide a real score if available
      localizationScore: 0, // You might want to provide a real score if available
      editingScoreThreshold: 1.0,
    },
    qualifiedPathName: { rootPath: "", relPath: location.location.path },
    lineRange: {
      start: location.location.range.start,
      stop: location.location.range.stop,
    },
    uriScheme: "file",
    occurredAt: new Date(),
    state: SuggestionState.fresh,
    changeType: ChangeType.modification,
  })
)();
const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
export let suggestions = locations().map(suggestion);

mockHost.addEventListener("message-from-webview", async (customEvent: Event) => {
  assertCustomEvent(customEvent);
  const { detail: e } = customEvent;
  assertWebviewMessage(e);
  switch (e.type) {
    case WebViewMessageType.asyncWrapper: {
      if (!e.baseMsg) {
        break;
      }
      switch (e.baseMsg.type) {
        case WebViewMessageType.readFileRequest: {
          assertAsyncWebviewReadFileRequestMessage(e);
          assertWebviewMessage(e.baseMsg);
          const { pathName } = e.baseMsg.data;
          const { requestId } = e;
          const content = allFiles[pathName.relPath] ?? "";
          // Generate the ID to be used by the *next* chunk’s request
          const nextReqId = crypto.randomUUID();
          mockHost.sendMessageToWebView({
            type: WebViewMessageType.asyncWrapper,
            requestId,
            error: null,
            baseMsg: {
              type: WebViewMessageType.readFileResponse,
              data: { content, pathName },
            },
            streamCtx: {
              // The index of this chunk
              streamMsgIdx: 0,
              // Let the consumer know they should continue asking for the next chunk
              isStreamComplete: false,
              // The ID the client will use for the next readFileRequest chunk
              // (the client’s stream logic automatically sets requestId=streamNextRequestId)
              streamNextRequestId: nextReqId,
            },
          });
          break;
        }
      }
      break;
    }

    case WebViewMessageType.nextEditOpenSuggestion:
      console.log("open suggestions");
      break;
    case WebViewMessageType.nextEditSuggestionsAction: {
      const action = e.data;

      if ("accept" in action) {
        const find = suggestions.find((s) => areSameSuggestion(s, action.accept));
        if (find) {
          find.state = SuggestionState.stale;
        }
        mockHost.sendMessageToWebView({
          type: WebViewMessageType.nextEditSuggestionsChanged,
          data: {
            focusedSuggestion: find,
            suggestions,
          },
        });
        await wait(1500);
        suggestions = suggestions.filter((s) => !areSameSuggestion(s, action.accept));
      } else if ("reject" in action) {
        suggestions = suggestions.filter((s) => !areSameSuggestion(s, action.reject));
      }
      mockHost.sendMessageToWebView({
        type: WebViewMessageType.nextEditSuggestionsChanged,
        data: {
          focusedSuggestion: suggestions[0],
          suggestions,
        },
      });
      break;
    }
    case WebViewMessageType.nextEditLoaded:
      mockHost.sendMessageToWebView({
        type: WebViewMessageType.nextEditSuggestionsChanged,
        data: {
          suggestions,
        },
      });
      break;
    case WebViewMessageType.nextEditRefreshStarted: {
      for (let i = 2; i < suggestions.length; i++) {
        mockHost.sendMessageToWebView({
          type: WebViewMessageType.nextEditSuggestionsChanged,
          data: {
            suggestions: suggestions.slice(0, i),
          },
        });
        await wait(1000 * Math.random());
      }
      mockHost.sendMessageToWebView({
        type: WebViewMessageType.nextEditRefreshFinished,
      });
      break;
    }
    default:
      console.log(e.type);
  }
});

export function clearSuggestions() {
  mockHost.sendMessageToWebView({
    type: WebViewMessageType.nextEditSuggestionsChanged,
    data: {
      suggestions: [],
    },
  });
}
export function addSuggestion() {
  mockHost.sendMessageToWebView({
    type: WebViewMessageType.nextEditSuggestionsChanged,
    data: {
      suggestions: (suggestions = [...suggestions, suggestion(scoredLocation())]),
    },
  });
}

let busy = false;
export function loading() {
  mockHost.sendMessageToWebView({
    type: !busy
      ? WebViewMessageType.nextEditRefreshFinished
      : WebViewMessageType.nextEditRefreshStarted,
  });

  return (busy = !busy);
}

let open = true;
export function openSuggestionTree() {
  mockHost.sendMessageToWebView({
    type: WebViewMessageType.nextEditToggleSuggestionTree,
    data: { open },
  });
  return (open = !open);
}

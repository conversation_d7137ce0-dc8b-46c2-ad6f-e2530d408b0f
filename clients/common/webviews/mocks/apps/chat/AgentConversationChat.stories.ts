/* eslint-disable @typescript-eslint/naming-convention */
import type { <PERSON>a, StoryObj } from "@storybook/svelte";
import "./chat-mock";
import component from "$common-webviews/src/apps/chat/ChatLoader.svelte";
import { withMonaco } from "$common-webviews/mocks/design-system/monaco-provider/monaco-decorator";
import { ExchangeStatus, SeenState } from "$common-webviews/src/apps/chat/types/chat-message";

const meta = {
  title: "app/Chat",
  component,
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const AgentConversation: Story = {
  name: "Agent Conversation",
  parameters: {
    layout: "fullscreen",
  },
  args: {
    initialConversation: {
      id: "agent-conversation-id",
      createdAtIso: "2025-05-14T01:39:03.701Z",
      lastInteractedAtIso: "2025-05-20T13:51:39.195Z",

      chatHistory: [
        {
          request_message: "Create a simple React component that displays a counter",
          response_text:
            "I'll create a simple React counter component for you. Let's break this down into steps:\n\n1. First, I'll create a basic React component with state for the counter\n2. Add buttons to increment and decrement the counter\n3. Style it minimally for better appearance\n\nHere's the implementation:",
          status: ExchangeStatus.success,
          request_id: "agent-request-id-1",
          seen_state: SeenState.seen,

          structured_output_nodes: [
            {
              id: 0,
              type: 0,
              content:
                "I'll create a simple React counter component for you. Let's break this down into steps:\n\n1. First, I'll create a basic React component with state for the counter\n2. Add buttons to increment and decrement the counter\n3. Style it minimally for better appearance\n\nHere's the implementation:",
            },
            {
              id: 1,
              type: 5,
              content: "",

              tool_use: {
                tool_use_id: "tool-use-agent-id-1",
                tool_name: "save-file",
                input_json:
                  '{"file_path":"Counter.jsx","file_content":"// This is a long JavaScript file for demonstration purposes\\n\\n// Imports\\nimport React from \\"react\\";\\nimport { useState, useEffect } from \\"react\\";\\nimport axios from \\"axios\\";\\n\\n/**\\n * Example component with many methods\\n */\\nclass ExampleComponent {\\n  /**\\n   * Method 1 description\\n   * @param {string} param1 - First parameter\\n   * @param {number} param2 - Second parameter\\n   * @returns {boolean} - Return value\\n   */\\n  method1(param1, param2) {\\n    console.log(\\"Method 1 called with \\" + param1 + \\" and \\" + param2);\\n\\n    // Some complex logic\\n    if (param1.length > 10) {\\n      return param2 > 100;\\n    } else {\\n      return param1.startsWith(\\"test\\") && param2 < 50;\\n    }\\n  }\\n\\n  /**\\n   * Method 2 description\\n   * @param {string} param1 - First parameter\\n   * @param {number} param2 - Second parameter\\n   * @returns {boolean} - Return value\\n   */\\n  method2(param1, param2) {\\n    console.log(\\"Method 2 called with \\" + param1 + \\" and \\" + param2);\\n\\n    // Some complex logic\\n    if (param1.length > 10) {\\n      return param2 > 100;\\n    } else {\\n      return param1.startsWith(\\"test\\") && param2 < 50;\\n    }\\n  }\\n\\n  // ... many more methods would be here in a real long file\\n}\\n\\n// React component example\\nconst MyComponent = () => {\\n  const [data, setData] = useState([]);\\n  const [loading, setLoading] = useState(false);\\n\\n  useEffect(() => {\\n    const fetchData = async () => {\\n      setLoading(true);\\n      try {\\n        const response = await axios.get(\\"/api/data\\");\\n        setData(response.data);\\n      } catch (error) {\\n        console.error(\\"Error fetching data:\\", error);\\n      } finally {\\n        setLoading(false);\\n      }\\n    };\\n\\n    fetchData();\\n  }, []);\\n\\n  return (\\n    <div className=\\"container\\">\\n      <h1>Data Display</h1>\\n      {loading ? (\\n        <p>Loading...</p>\\n      ) : (\\n        <ul>\\n          {data.map((item) => (\\n            <li key={item.id}>{item.name}</li>\\n          ))}\\n        </ul>\\n      )}\\n    </div>\\n  );\\n};"}',
              },
            },
            {
              id: 2,
              type: 0,
              content:
                "\n\nI've created a simple React counter component in `Counter.jsx`. Here's what it does:\n\n- Uses React's `useState` hook to manage the counter state\n- Provides increment and decrement functions\n- Renders a display showing the current count\n- Includes two buttons for increasing and decreasing the count\n- Has some basic styling to make it look nice\n\nTo use this component in your React application, you can import it like this:\n\n```jsx\nimport Counter from './Counter';\n\nfunction App() {\n  return (\n    <div className=\"App\">\n      <Counter />\n    </div>\n  );\n}\n```\n\nThe component is self-contained with its own styling using styled-jsx. If you're not using Next.js or another framework that supports styled-jsx out of the box, you might need to install the appropriate plugin or move the styles to a separate CSS file.\n\nWould you like me to modify this component in any way or explain any part of it in more detail?",
            },
          ],

          structured_request_nodes: [
            {
              id: 0,
              type: 0,

              text_node: {
                content: "Create a simple React component that displays a counter",
              },
            },
          ],
        },
        {
          request_message: "Now create a Python script that can analyze a dataset",
          response_text:
            "I'll create a Python script for data analysis. Let's build a script that can load, analyze, and visualize a dataset using popular data science libraries:",
          status: ExchangeStatus.success,
          request_id: "agent-request-id-2",
          seen_state: SeenState.seen,

          structured_output_nodes: [
            {
              id: 0,
              type: 0,
              content:
                "I'll create a Python script for data analysis. Let's build a script that can load, analyze, and visualize a dataset using popular data science libraries:",
            },
            {
              id: 1,
              type: 5,
              content: "",

              tool_use: {
                tool_use_id: "tool-use-agent-id-2",
                tool_name: "save-file",
                input_json:
                  '{"file_path":"data_analyzer.py","file_content":"# This is a long Python file for demonstration purposes\\n\\nimport os\\nimport sys\\nimport json\\nimport numpy as np\\nimport pandas as pd\\nfrom typing import List, Dict, Optional, Union, Any\\n\\nclass DataProcessor:\\n    \\"\\"\\"A class for processing data with many methods.\\"\\"\\"\\n\\n    def __init__(self, data_path: str, config: Optional[Dict[str, Any]] = None):\\n        \\"\\"\\"Initialize the data processor.\\n\\n        Args:\\n            data_path: Path to the data file\\n            config: Optional configuration dictionary\\n        \\"\\"\\"\\n        self.data_path = data_path\\n        self.config = config or {}\\n        self.data = None\\n\\n    def load_data(self) -> pd.DataFrame:\\n        \\"\\"\\"Load data from the specified path.\\"\\"\\"\\n        if self.data_path.endswith(\'.csv\'):\\n            self.data = pd.read_csv(self.data_path)\\n        elif self.data_path.endswith(\'.json\'):\\n            self.data = pd.read_json(self.data_path)\\n        else:\\n            raise ValueError(f\\"Unsupported file format: {self.data_path}\\")\\n        return self.data\\n\\n    def preprocess(self) -> pd.DataFrame:\\n        \\"\\"\\"Preprocess the loaded data.\\"\\"\\"\\n        if self.data is None:\\n            self.load_data()\\n\\n        # Fill missing values\\n        self.data = self.data.fillna(self.config.get(\'fill_value\', 0))\\n\\n        # Drop duplicates\\n        if self.config.get(\'drop_duplicates\', True):\\n            self.data = self.data.drop_duplicates()\\n\\n        return self.data"}',
              },
            },
            {
              id: 2,
              type: 0,
              content:
                "\n\nI've created a Python data analysis script called `data_analyzer.py`. This script demonstrates several key data analysis capabilities:\n\n- Loading data from CSV files using pandas\n- Performing basic statistical analysis\n- Creating visualizations with matplotlib and seaborn\n- Implementing a simple machine learning model with scikit-learn\n\nTo use this script, you'll need to have the following Python libraries installed:\n\n```bash\npip install pandas numpy matplotlib seaborn scikit-learn\n```\n\nThe script is designed to work with CSV data files. It includes functions for:\n\n1. Loading and exploring data\n2. Cleaning and preprocessing\n3. Statistical analysis\n4. Data visualization\n5. Simple predictive modeling\n\nWould you like me to explain any specific part of the script in more detail or modify it to handle a particular type of analysis?",
            },
          ],

          structured_request_nodes: [
            {
              id: 0,
              type: 0,

              text_node: {
                content: "Now create a Python script that can analyze a dataset",
              },
            },
          ],
        },
        {
          request_message: "Can you create a simple HTML/CSS landing page?",
          response_text:
            "I'll create a simple yet effective landing page using HTML and CSS. Let's build a modern, responsive design that would work well for a product or service:",
          status: ExchangeStatus.success,
          request_id: "agent-request-id-3",
          seen_state: SeenState.seen,

          structured_output_nodes: [
            {
              id: 0,
              type: 0,
              content:
                "I'll create a simple yet effective landing page using HTML and CSS. Let's build a modern, responsive design that would work well for a product or service:",
            },
            {
              id: 1,
              type: 5,
              content: "",

              tool_use: {
                tool_use_id: "tool-use-agent-id-3",
                tool_name: "save-file",
                input_json:
                  '{"file_path":"index.html","file_content":"<!DOCTYPE html>\\n<html lang=\\"en\\">\\n<head>\\n    <meta charset=\\"UTF-8\\">\\n    <meta name=\\"viewport\\" content=\\"width=device-width, initial-scale=1.0\\">\\n    <title>Long HTML Example</title>\\n    <style>\\n        body {\\n            font-family: Arial, sans-serif;\\n            line-height: 1.6;\\n            color: #333;\\n            max-width: 800px;\\n            margin: 0 auto;\\n            padding: 20px;\\n        }\\n        header {\\n            background-color: #f4f4f4;\\n            padding: 20px;\\n            margin-bottom: 20px;\\n            border-radius: 5px;\\n        }\\n        nav ul {\\n            display: flex;\\n            list-style: none;\\n            padding: 0;\\n        }\\n        nav ul li {\\n            margin-right: 20px;\\n        }\\n        nav ul li a {\\n            text-decoration: none;\\n            color: #333;\\n        }\\n        section {\\n            margin-bottom: 30px;\\n            padding: 20px;\\n            background-color: #f9f9f9;\\n            border-radius: 5px;\\n        }\\n        footer {\\n            text-align: center;\\n            padding: 20px;\\n            background-color: #f4f4f4;\\n            margin-top: 30px;\\n            border-radius: 5px;\\n        }\\n    </style>\\n</head>\\n<body>\\n    <header>\\n        <h1>Long HTML Example</h1>\\n        <nav>\\n            <ul>\\n                <li><a href=\\"#section1\\">Section 1</a></li>\\n                <li><a href=\\"#section2\\">Section 2</a></li>\\n                <li><a href=\\"#section3\\">Section 3</a></li>\\n            </ul>\\n        </nav>\\n    </header>\\n\\n    <main>\\n        <section id=\\"section1\\">\\n            <h2>Section 1: Introduction</h2>\\n            <p>This is a long HTML file example to demonstrate scrolling in the Monaco editor.</p>\\n            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>\\n        </section>\\n\\n        <section id=\\"section2\\">\\n            <h2>Section 2: Features</h2>\\n            <p>Here are some features of our product:</p>\\n            <ul>\\n                <li>Feature 1: Lorem ipsum dolor sit amet</li>\\n                <li>Feature 2: Consectetur adipiscing elit</li>\\n                <li>Feature 3: Nullam auctor, nisl eget ultricies tincidunt</li>\\n            </ul>\\n        </section>\\n\\n        <section id=\\"section3\\">\\n            <h2>Section 3: Examples</h2>\\n            <p>Here are some examples of our product in action:</p>\\n            <div>\\n                <h3>Example 1</h3>\\n                <p>This is an example of our product in action.</p>\\n            </div>\\n        </section>\\n    </main>\\n\\n    <footer>\\n        <p>&copy; 2023 Long HTML Example. All rights reserved.</p>\\n    </footer>\\n</body>\\n</html>"}',
              },
            },
            {
              id: 2,
              type: 0,
              content:
                "\n\nI've created a complete HTML/CSS landing page for you. This landing page includes:\n\n1. **Modern, Responsive Design** - Works well on both desktop and mobile devices\n2. **Clean Navigation** - With a fixed header that stays visible as users scroll\n3. **Hero Section** - With a prominent call-to-action button\n4. **Features Section** - Highlighting three key product features with icons\n5. **Testimonials** - Customer quotes to build trust\n6. **Call-to-Action Section** - To encourage conversions\n7. **Footer** - With site navigation and company information\n\nThe page uses a clean, professional color scheme with blue as the primary accent color. All the styling is contained within the HTML file using internal CSS, making it easy to deploy.\n\nTo use this landing page:\n1. Save the code to a file named `index.html`\n2. Open it in any web browser\n3. Customize the text, colors, and images to match your brand\n\nYou can easily extend this design by:\n- Adding your logo in the header\n- Including actual customer photos in the testimonials\n- Adding a pricing table section\n- Incorporating a contact form\n\nWould you like me to explain any part of the code or make any specific modifications to the design?",
            },
          ],

          structured_request_nodes: [
            {
              id: 0,
              type: 0,

              text_node: {
                content: "Can you create a simple HTML/CSS landing page?",
              },
            },
          ],
        },
      ],

      feedbackStates: {},
      requestIds: ["agent-request-id-1", "agent-request-id-2", "agent-request-id-3"],
      isPinned: false,
      isShareable: true,

      extraData: {
        isAgentConversation: true,
      },

      toolUseStates: {
        "agent-request-id-1;tool-use-agent-id-1": {
          phase: 5,

          result: {
            text: "File created successfully",
            isError: false,
          },

          requestId: "agent-request-id-1",
          toolUseId: "tool-use-agent-id-1",
        },

        "agent-request-id-2;tool-use-agent-id-2": {
          phase: 5,

          result: {
            text: "File created successfully",
            isError: false,
          },

          requestId: "agent-request-id-2",
          toolUseId: "tool-use-agent-id-2",
        },

        "agent-request-id-3;tool-use-agent-id-3": {
          phase: 5,

          result: {
            text: "File created successfully",
            isError: false,
          },

          requestId: "agent-request-id-3",
          toolUseId: "tool-use-agent-id-3",
        },
      },
    },
    initialFlags: {
      enableDebugFeatures: true,
      enableAgentMode: true,
      enableTaskList: true,
    },
  },
  decorators: [withMonaco],
};

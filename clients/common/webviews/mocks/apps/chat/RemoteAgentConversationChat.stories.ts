/* eslint-disable @typescript-eslint/naming-convention */
import type { <PERSON>a, StoryObj } from "@storybook/svelte";
import "./chat-mock";
import "../remote-agent-manager/remote-agent-manager-mock";
import component from "$common-webviews/src/apps/chat/ChatLoader.svelte";
import { withMonaco } from "$common-webviews/mocks/design-system/monaco-provider/monaco-decorator";

const meta = {
  title: "app/Chat",
  component,
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const ChatRemoteAgentConversation: Story = {
  name: "Chat with Remote Agent Conversation",
  parameters: {
    layout: "fullscreen",
  },
  args: {
    initialConversation: {
      id: "agent-conversation-id",
      createdAtIso: "2025-05-14T01:39:03.701Z",
      lastInteractedAtIso: "2025-05-20T13:51:39.195Z",
      chatHistory: [],
      feedbackStates: {},
      requestIds: ["agent-request-id-1", "agent-request-id-2", "agent-request-id-3"],
      isPinned: false,
      isShareable: true,

      extraData: {
        isAgentConversation: true,
      },

      toolUseStates: {
        "agent-request-id-1;tool-use-agent-id-1": {
          phase: 5,

          result: {
            text: "File created successfully",
            isError: false,
          },

          requestId: "agent-request-id-1",
          toolUseId: "tool-use-agent-id-1",
        },

        "agent-request-id-2;tool-use-agent-id-2": {
          phase: 5,

          result: {
            text: "File created successfully",
            isError: false,
          },

          requestId: "agent-request-id-2",
          toolUseId: "tool-use-agent-id-2",
        },

        "agent-request-id-3;tool-use-agent-id-3": {
          phase: 5,

          result: {
            text: "File created successfully",
            isError: false,
          },

          requestId: "agent-request-id-3",
          toolUseId: "tool-use-agent-id-3",
        },
      },
    },
    initialFlags: {
      enableDebugFeatures: true,
      enableAgentMode: true,
      enableTaskList: true,
      enableBackgroundAgents: true,
      doUseNewDraftFunctionality: true,
    },
  },
  decorators: [withMonaco],
};

/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import "./chat-mock";
import component from "$common-webviews/src/apps/chat/ChatLoader.svelte";
import { withMonaco } from "$common-webviews/mocks/design-system/monaco-provider/monaco-decorator";

const meta = {
  title: "app/Chat",
  component,
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const NewMessage: Story = {
  name: "Enable New Session Message",
  args: {
    initialFlags: {
      idleNewSessionMessageTimeoutMs: 5000,
      idleNewSessionNotificationTimeoutMs: 1000,
    },
  },
  decorators: [withMonaco],
};

export const MultiModal: Story = {
  name: "Enable Multimodal",
  args: {
    initialFlags: {
      enableChatMultimodal: true,
      idleNewSessionMessageTimeoutMs: 5000,
      idleNewSessionNotificationTimeoutMs: 1000,
    },
  },
  decorators: [withMonaco],
};

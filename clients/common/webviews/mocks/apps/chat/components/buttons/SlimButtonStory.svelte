<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import SlimButton from "$common-webviews/src/apps/chat/components/buttons/SlimButton.svelte";
  import SlashCommandSuggestionItem from "$common-webviews/src/apps/chat/components/slash-commands/SlashCommandSuggestionItem.svelte";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";
</script>

<ColumnLayout>
  <SlimButton selected={false}>Not selected</SlimButton>
  <SlimButton selected={true}>Selected</SlimButton>

  <SlimButton color="error">
    <ExclamationTriangle />
    1 outdated sources hidden
  </SlimButton>

  <SlimButton>
    <Filespan filepath={"example/path/for/file.ts"} />
  </SlimButton>

  <SlimButton>
    <SlashCommandSuggestionItem
      option={{
        id: "example",
        label: "/example",
        description: "example description",
      }}
    />
  </SlimButton>
</ColumnLayout>

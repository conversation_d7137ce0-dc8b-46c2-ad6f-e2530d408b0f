<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import CodeblockContainer from "$common-webviews/src/apps/chat/components/markdown-ext/codeblock/CodeblockContainer.svelte";
  import {
    createCopyToClipboardAction,
    createGoToFileAction,
    createInsertIntoNewFileAction,
  } from "$common-webviews/src/apps/chat/components/markdown-ext/codeblock/codeblockActions";
  import { type ICodeblockMetadata } from "$common-webviews/src/apps/chat/components/markdown-ext/utils";
  import { type Tokens } from "marked";
  import { ExchangeStatus } from "$common-webviews/src/apps/chat/types/chat-message";
  import { createSmartPasteAction } from "$common-webviews/src/apps/chat/components/markdown-ext/codeblock/smartPaste";

  // Props that can be controlled from Storybook
  export let primaryButtonType = "copy";
  export let collapsed = false;
  export let isLoading = false;

  // Sample code token
  const TOKEN = {
    text: `export function foobar() {
    console.log("hello world");
}`,
    lang: "typescript",
    type: "code",
    raw: '```typescript\nexport function foobar() {\n    console.log("hello world");\n}\n```',
  } as Tokens.Code;

  // Sample metadata configurations
  const CODEBLOCK_CONFIG: Record<string, ICodeblockMetadata> = {
    noPath: {
      language: "typescript",
      relPath: null,
      mode: "EDIT",
    },
    filePath: {
      language: "typescript",
      relPath: "src/utils/foobar.ts",
      mode: "EDIT",
    },
    fileNameLong: {
      language: "typescript",
      relPath: "thisisareallylongfilenamethatgoesonseeminglyindefinitelyfoobar.ts",
      mode: "EDIT",
    },
    filePathLong: {
      language: "typescript",
      relPath: "src/very/long/path/to/some/deeply/nested/file/with/a/long/name/foobar.ts",
      mode: "EDIT",
    },
    fileNameAndPathLong: {
      language: "typescript",
      relPath:
        "clients/common/webviews/src/apps/chat/components/markdown-ext/this/is/a/really/long/path/that/goes/on/seemingly/indefinitely/thisisareallylongfilenamethatgoesonseeminglyindefinitelyfoobar.ts",
      mode: "EDIT",
    },
  };

  // Create mock action buttons
  const copyButton = createCopyToClipboardAction(() => TOKEN.text);
  const insertButton = createInsertIntoNewFileAction(() => TOKEN.text, undefined, undefined);
  const goToButton = createGoToFileAction(TOKEN.text, false, CODEBLOCK_CONFIG.filePath, undefined);
  const { smartPasteButton, directApplyButton } = createSmartPasteAction(
    undefined,
    undefined,
    {
      text: "code",
      type: "code",
      raw: "",
    },
    "requestId",
    true,
    true,
    true,
    true,
    () => false,
    () => {},
  );

  // Create button configurations for the component
  const buttons = [copyButton, insertButton];

  // Create a structure to demonstrate different primary buttons
  const primaryButtonExamples = [
    { id: "copy", name: "Copy Button as Primary", button: copyButton },
    { id: "insert", name: "Insert Button as Primary", button: insertButton },
    { id: "goToFile", name: "Go To File Button as Primary", button: goToButton },
    { id: "smartPaste", name: "Smart Paste Button as Primary", button: smartPasteButton },
    { id: "directApply", name: "Direct Apply Button as Primary", button: directApplyButton },
  ];

  // Use the primaryButtonType prop from Storybook controls
  $: selectedPrimaryButtonId = primaryButtonType;
  $: selectedPrimaryButton =
    primaryButtonExamples.find((pb) => pb.id === selectedPrimaryButtonId)?.button || copyButton;
</script>

<ColumnLayout>
  <h1>CodeblockContainer</h1>

  <div class="button-selector">
    <h3>
      Primary Button: {primaryButtonExamples.find((pb) => pb.id === selectedPrimaryButtonId)?.name}
    </h3>
    <p class="description">Change the primary button type using the Storybook controls panel.</p>
  </div>

  {#each Object.entries(CODEBLOCK_CONFIG) as [name, metadata]}
    <h3>{name}</h3>
    <CodeblockContainer
      token={TOKEN}
      codeblockMetadata={metadata}
      requestStatus={isLoading ? ExchangeStatus.sent : undefined}
      primaryButton={selectedPrimaryButton}
      {buttons}
      goToFileButton={goToButton}
    />
  {/each}

  <h2>Long Code Example</h2>
  <CodeblockContainer
    token={{
      ...TOKEN,
      text: Array(20).fill('console.log("hello world");').join("\n"),
    }}
    codeblockMetadata={CODEBLOCK_CONFIG.filePath}
    requestStatus={isLoading ? ExchangeStatus.sent : undefined}
    primaryButton={selectedPrimaryButton}
    {buttons}
    goToFileButton={goToButton}
    {collapsed}
    truncateLines={10}
  />
</ColumnLayout>

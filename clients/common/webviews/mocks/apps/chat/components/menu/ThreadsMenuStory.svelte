<script lang="ts">
  import ThreadsMenu from "$common-webviews/src/apps/chat/components/menu/ThreadsMenu.svelte";
  import { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { SpecialContextInputModel } from "$common-webviews/src/apps/chat/models/context-model";
  import type { IConversation } from "$common-webviews/src/apps/chat/models/types";
  import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
  import { host } from "$common-webviews/src/common/hosts/host";

  const now = Date.now();
  const conversations: Record<string, IConversation> = {};
  for (let i = 1; i <= 20; i++) {
    conversations[`${i}`] = {
      id: i.toString(),
      name: Array.from({ length: i }).fill(`Conversation ${i}`).join(" "),
      createdAtIso: new Date(now - i).toISOString(),
      lastInteractedAtIso: new Date(now - i).toISOString(),
      chatHistory: [],
      feedbackStates: {},
      requestIds: [],
      isShareable: true,
    };
  }

  host.setState({
    currentConversationId: "1",
    conversations,
  });

  const chatModel = new ChatModel(
    new AsyncMsgSender((msg) => {
      host.postMessage(msg);
    }),
    host,
    new SpecialContextInputModel(),
  );
</script>

<div class="l-threads-menu-story">
  <h2>Closed</h2>

  <ThreadsMenu {chatModel} active={false} />

  <p>
    Lorem ipsum odor amet, consectetuer adipiscing elit. Sodales diam per hendrerit dictum fusce
    primis venenatis metus conubia. Mauris dapibus suscipit orci feugiat ornare eleifend integer
    iaculis. Est vehicula fusce neque efficitur dolor. Nec leo rhoncus ornare ultricies cubilia
    fames dolor risus. Per pellentesque lobortis urna sagittis justo justo suspendisse efficitur
    cubilia. Facilisis velit at blandit mauris egestas et?
  </p>

  <h2>Open</h2>

  <ThreadsMenu {chatModel} active={true} />

  <p>
    Lorem ipsum odor amet, consectetuer adipiscing elit. Sodales diam per hendrerit dictum fusce
    primis venenatis metus conubia. Mauris dapibus suscipit orci feugiat ornare eleifend integer
    iaculis. Est vehicula fusce neque efficitur dolor. Nec leo rhoncus ornare ultricies cubilia
    fames dolor risus. Per pellentesque lobortis urna sagittis justo justo suspendisse efficitur
    cubilia. Facilisis velit at blandit mauris egestas et?
  </p>

  <p>
    Lorem elementum suspendisse luctus elementum porttitor scelerisque elementum. Primis cubilia
    habitasse, parturient nec urna nunc. Imperdiet tristique curae nullam tempus vitae quam vehicula
    senectus? Pulvinar metus velit imperdiet proin ante accumsan metus ridiculus lacus. Pellentesque
    sapien mi consectetur, sapien lorem ultrices euismod. Consequat ut ornare lacinia curabitur
    egestas est.
  </p>

  <p>
    Rutrum bibendum ante rhoncus ipsum urna. Orci finibus et laoreet urna vestibulum. Montes id
    natoque amet maecenas integer convallis magna tristique. Pulvinar primis at donec consectetur
    magnis vel. Volutpat nam ornare scelerisque nibh egestas inceptos commodo bibendum. Hac etiam
    vitae nunc tortor efficitur felis! Morbi nullam nunc; fusce non eleifend et. Lacinia primis
    metus dolor pulvinar conubia elementum a nam.
  </p>
</div>

<style>
  .l-threads-menu-story {
    max-width: 400px;
    padding-bottom: 500px;
    margin-inline: auto;
  }
</style>

<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import ThreadMenuItemEditableText from "$common-webviews/src/apps/chat/components/menu/ThreadMenuItemEditableText.svelte";
</script>

<ColumnLayout>
  <h4>Various Lengths</h4>

  <ThreadMenuItemEditableText
    value=""
    onStartEdit={() => {}}
    onAcceptEdit={() => {}}
    onCancelEdit={() => {}}
  />

  <ThreadMenuItemEditableText
    value="Short text"
    onStartEdit={() => {}}
    onAcceptEdit={() => {}}
    onCancelEdit={() => {}}
  />

  <ThreadMenuItemEditableText
    value={"Really really long piece of text ".repeat(10)}
    onStartEdit={() => {}}
    onAcceptEdit={() => {}}
    onCancelEdit={() => {}}
  />

  <h4>Editing</h4>

  <ThreadMenuItemEditableText
    value=""
    isEditing={true}
    cancelOnClickOutside={false}
    onStartEdit={() => {}}
    onAcceptEdit={() => {}}
    onCancelEdit={() => {}}
  />

  <ThreadMenuItemEditableText
    value="Short text"
    placeholder="Short placeholder"
    isEditing={true}
    cancelOnClickOutside={false}
    onStartEdit={() => {}}
    onAcceptEdit={() => {}}
    onCancelEdit={() => {}}
  />

  <ThreadMenuItemEditableText
    value=""
    placeholder="Short placeholder"
    isEditing={true}
    cancelOnClickOutside={false}
    onStartEdit={() => {}}
    onAcceptEdit={() => {}}
    onCancelEdit={() => {}}
  />

  <ThreadMenuItemEditableText
    value={"Really really long piece of text ".repeat(10)}
    placeholder={"Really really long placeholder ".repeat(10)}
    isEditing={true}
    cancelOnClickOutside={false}
    onStartEdit={() => {}}
    onAcceptEdit={() => {}}
    onCancelEdit={() => {}}
  />

  <ThreadMenuItemEditableText
    value={""}
    placeholder={"Really really long placeholder ".repeat(10)}
    isEditing={true}
    cancelOnClickOutside={false}
    onStartEdit={() => {}}
    onAcceptEdit={() => {}}
    onCancelEdit={() => {}}
  />
</ColumnLayout>

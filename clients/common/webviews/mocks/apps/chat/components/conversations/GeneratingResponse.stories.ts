/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import component from "./GeneratingResponseStory.svelte";

const meta = {
  title: "app/Chat/components/conversation/GeneratingResponse",
  component,
  tags: ["autodocs"],
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Example: Story = {
  args: {},
};

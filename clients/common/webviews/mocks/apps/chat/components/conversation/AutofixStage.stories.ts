/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import AutofixStage from "$common-webviews/src/apps/chat/components/conversation/AutofixStage.svelte";
import { type AutofixIteration, AutofixIterationStage } from "$vscode/src/autofix/autofix-state";
import { setContext } from "svelte";

const meta = {
  title: "app/Chat/components/autofix/AutofixStage",
  component: AutofixStage,
  tags: ["autodocs"],
  argTypes: {
    stage: {
      stageCount: 0,
      control: "select",
      options: ["runTest", "applyFix"],
    },
  },
} satisfies Meta<AutofixStage>;

export default meta;

type Story = StoryObj<typeof meta>;

function getMockChatModel(mockIterations: AutofixIteration[]) {
  const mockModel = {
    getAutofixIteration: (id: string) => mockIterations.find((iteration) => iteration.id === id),
    subscribe: (callback: (model: any) => void) => {
      callback(mockModel);
      return () => {}; // Unsubscribe function
    },
  };

  return mockModel;
}

export const TestRunning: Story = {
  decorators: [
    (Story) => {
      setContext(
        "autofixConversationModel",
        getMockChatModel([
          {
            id: "mock-iteration-id",
            isFirstIteration: true,
            currentStage: AutofixIterationStage.runTest,
          },
        ]),
      );
      return Story();
    },
  ],
  args: {
    iterationId: "mock-iteration-id",
    stage: AutofixIterationStage.runTest,
    stageCount: 0,
  },
};

export const TestFailed: Story = {
  decorators: [
    (Story) => {
      setContext(
        "autofixConversationModel",
        getMockChatModel([
          {
            id: "mock-iteration-id",
            isFirstIteration: true,
            currentStage: AutofixIterationStage.runTest,
            commandFailed: true,
            commandOutput: "Some error here",
          },
        ]),
      );
      return Story();
    },
  ],
  args: {
    iterationId: "mock-iteration-id",
    stage: AutofixIterationStage.runTest,
    stageCount: 0,
  },
};

export const TestPassed: Story = {
  decorators: [
    (Story) => {
      setContext(
        "autofixConversationModel",
        getMockChatModel([
          {
            id: "mock-iteration-id",
            isFirstIteration: true,
            currentStage: AutofixIterationStage.runTest,
            commandFailed: false,
            commandOutput: "Success",
          },
        ]),
      );
      return Story();
    },
  ],
  args: {
    iterationId: "mock-iteration-id",
    stage: AutofixIterationStage.runTest,
    stageCount: 0,
  },
};

export const Retesting: Story = {
  decorators: [
    (Story) => {
      setContext(
        "autofixConversationModel",
        getMockChatModel([
          {
            id: "mock-iteration-id",
            isFirstIteration: true,
            currentStage: AutofixIterationStage.runTest,
            commandFailed: true,
            commandOutput: "Some error here",
          },
          {
            id: "mock-iteration-id-2",
            isFirstIteration: false,
            currentStage: AutofixIterationStage.runTest,
          },
        ]),
      );
      return Story();
    },
  ],
  args: {
    iterationId: "mock-iteration-id-2",
    stage: AutofixIterationStage.runTest,
    stageCount: 0,
  },
};

export const GeneratingSolutions: Story = {
  decorators: [
    (Story) => {
      setContext(
        "autofixConversationModel",
        getMockChatModel([
          {
            id: "mock-iteration-id",
            isFirstIteration: true,
            currentStage: AutofixIterationStage.applyFix,
            commandFailed: true,
            commandOutput: "Some error here",
          },
        ]),
      );
      return Story();
    },
  ],
  args: {
    iterationId: "mock-iteration-id",
    stage: AutofixIterationStage.applyFix,
    stageCount: 0,
  },
};

export const SuggestedSolutions: Story = {
  decorators: [
    (Story) => {
      setContext(
        "autofixConversationModel",
        getMockChatModel([
          {
            id: "mock-iteration-id",
            isFirstIteration: true,
            currentStage: AutofixIterationStage.applyFix,
            commandFailed: true,
            commandOutput: "Some error here",
            suggestedSolutions: [
              {
                summary: "Summary",
                replacements: [],
                originalReplacements: [],
              },
            ],
          },
        ]),
      );
      return Story();
    },
  ],
  args: {
    iterationId: "mock-iteration-id",
    stage: AutofixIterationStage.applyFix,
    stageCount: 0,
  },
};

export const SelectedSolutions: Story = {
  decorators: [
    (Story) => {
      setContext(
        "autofixConversationModel",
        getMockChatModel([
          {
            id: "mock-iteration-id",
            isFirstIteration: true,
            currentStage: AutofixIterationStage.applyFix,
            commandFailed: true,
            commandOutput: "Some error here",
            suggestedSolutions: [
              {
                summary: "Summary",
                replacements: [],
                originalReplacements: [],
              },
            ],
            selectedSolutions: [],
          },
        ]),
      );
      return Story();
    },
  ],
  args: {
    iterationId: "mock-iteration-id",
    stage: AutofixIterationStage.applyFix,
    stageCount: 0,
  },
};

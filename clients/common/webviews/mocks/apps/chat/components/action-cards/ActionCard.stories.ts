/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import ActionCardStory from "./ActionCardStory.svelte";

const meta = {
  title: "app/Chat/components/action-cards/ActionCard",
  component: ActionCardStory,
  tags: ["autodocs"],
} satisfies Meta<ActionCardStory>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Example: Story = {
  args: {},
};

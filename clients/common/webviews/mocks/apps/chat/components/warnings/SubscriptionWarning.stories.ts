/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import SubscriptionWarning from "$common-webviews/src/apps/chat/components/warnings/SubscriptionWarning.svelte";

const meta = {
  title: "app/Chat/components/warnings/SubscriptionWarning",
  component: SubscriptionWarning,
  tags: ["autodocs"],
  argTypes: {
    daysRemaining: {
      control: { type: "number" },
      description:
        "Number of days remaining in the subscription. Null if not applicable (e.g., inactive).",
    },
    isInactive: {
      control: { type: "boolean" },
      description: "Whether the subscription is inactive.",
    },
    usageBalanceDepleted: {
      control: { type: "boolean" },
      description: "Whether the usage balance is depleted.",
    },
    onDismiss: {
      description: "Callback when the dismiss button is clicked. Should be a function.",
    },
  },
} satisfies Meta<SubscriptionWarning>;

export default meta;

type Story = StoryObj<typeof meta>;

// Expiring subscription warning
export const ExpiringSubscription: Story = {
  name: "State: Expiring Soon",
  args: {
    daysRemaining: 7,
    isInactive: false,
    usageBalanceDepleted: false,
    onDismiss: () => {},
  },
};

// Almost expired subscription warning
export const AlmostExpiredSubscription: Story = {
  name: "State: Expiring Very Soon (1 day)",
  args: {
    daysRemaining: 1,
    isInactive: false,
    usageBalanceDepleted: false,
    onDismiss: () => {},
  },
};

// Inactive subscription warning
export const InactiveSubscription: Story = {
  name: "State: Inactive",
  args: {
    daysRemaining: null,
    isInactive: true,
    usageBalanceDepleted: false,
    onDismiss: () => {},
  },
};

// Depleted usage balance warning
export const DepletedUsageBalance: Story = {
  name: "State: Depleted Usage Balance",
  args: {
    daysRemaining: 30, // Still has days, but balance depleted
    isInactive: false,
    usageBalanceDepleted: true,
    onDismiss: () => {},
  },
};

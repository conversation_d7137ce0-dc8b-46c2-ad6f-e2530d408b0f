import {
  type AsyncWebViewMessage,
  type <PERSON>t<PERSON><PERSON><PERSON><PERSON><PERSON>ly,
  type ChatUserMessage,
  type WebViewMessage,
  WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";
import { SyncingStatus, type SourceFolderSyncingProgress } from "$vscode/src/workspace/types";
import { assertCustomEvent, isCustomEvent, mockHost } from "../../hosts/mock-host";
import { AsyncMsgHandler } from "$vscode/src/utils/webviews/messaging";
import { isAugmentURI, makeAugmentURI } from "$common-webviews/src/common/utils/augment-uri";
import {
  NEW_QUESTIONS_PROMPT,
  SUMMARY_PROMPT,
} from "$common-webviews/src/apps/chat/models/onboarding-workspace-model";
import { FileType } from "$vscode/src/utils/types";
import { AgentWebViewMessageType } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/agent-messages";
import { TaskWebViewMessageType } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/task-messages";
import { type WebViewMessageTypes } from "$vscode/src/utils/webviews/types";
import {
  TaskState,
  TaskUpdatedBy,
  type HydratedTask,
  type SerializedTask,
} from "@augment-internal/sidecar-libs/src/agent/task/task-types";
import { RuleType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { addDays } from "date-fns";

/**
 * Mock Task Manager for in-memory task operations
 * Provides realistic behavior for task CRUD operations
 */
class MockTaskManager {
  private tasks: Map<string, SerializedTask> = new Map();
  private currentRootTaskUuid: string | null = null;
  private taskIdCounter = 0;

  constructor() {
    // Initialize with some sample tasks
    this.initializeSampleTasks();
  }

  /**
   * Generates a unique UUID for tasks
   */
  private generateUuid(): string {
    return `mock-task-${++this.taskIdCounter}-${Date.now()}`;
  }

  /**
   * Initialize with sample tasks for testing
   */
  private initializeSampleTasks(): void {
    const rootTask: SerializedTask = {
      uuid: "root-task-nested",
      name: "Root Task",
      description: "This is the root task for the conversation",
      state: TaskState.IN_PROGRESS,
      subTasks: ["task-1", "task-2", "task-3", "task-4", "task-5"],
      lastUpdated: Date.now() - 3600000, // 1 hour ago
      lastUpdatedBy: TaskUpdatedBy.AGENT,
    };

    const subTasks: SerializedTask[] = [
      {
        uuid: "task-1",
        name: "Implement login functionality",
        description: "Create login form and authentication logic",
        state: TaskState.IN_PROGRESS,
        subTasks: [],
        lastUpdated: Date.now() - 1800000, // 30 minutes ago
        lastUpdatedBy: TaskUpdatedBy.AGENT,
      },
      {
        uuid: "task-2",
        name: "Set up database schema",
        description: "Design and implement database tables for users and products",
        state: TaskState.COMPLETE,
        subTasks: [],
        lastUpdated: Date.now() - 3600000, // 1 hour ago
        lastUpdatedBy: TaskUpdatedBy.AGENT,
      },
      {
        uuid: "task-3",
        name: "Create API endpoints",
        description: "Implement RESTful API endpoints for user management",
        state: TaskState.IN_PROGRESS,
        subTasks: ["task-3-1", "task-3-2", "task-3-3"],
        lastUpdated: Date.now() - 7200000, // 2 hours ago
        lastUpdatedBy: TaskUpdatedBy.USER,
      },
      {
        uuid: "task-4",
        name: "Add unit tests",
        description: "Write comprehensive unit tests for all components",
        state: TaskState.CANCELLED,
        subTasks: [],
        lastUpdated: Date.now() - 10800000, // 3 hours ago
        lastUpdatedBy: TaskUpdatedBy.USER,
      },
      {
        uuid: "task-5",
        name: "Implement error handling",
        description: "Add proper error handling and validation throughout the application",
        state: TaskState.COMPLETE,
        subTasks: [],
        lastUpdated: Date.now() - 14400000, // 4 hours ago
        lastUpdatedBy: TaskUpdatedBy.AGENT,
      },
      // Sub-tasks for task-3
      {
        uuid: "task-3-1",
        name: "User authentication endpoint",
        description: "Implement login/logout API endpoints",
        state: TaskState.COMPLETE,
        subTasks: [],
        lastUpdated: Date.now() - 7200000, // 2 hours ago
        lastUpdatedBy: TaskUpdatedBy.USER,
      },
      {
        uuid: "task-3-2",
        name: "User profile endpoint",
        description: "Implement user profile CRUD operations",
        state: TaskState.IN_PROGRESS,
        subTasks: [],
        lastUpdated: Date.now() - 7200000, // 2 hours ago
        lastUpdatedBy: TaskUpdatedBy.USER,
      },
      {
        uuid: "task-3-3",
        name: "User permissions endpoint",
        description: "Implement role-based access control",
        state: TaskState.NOT_STARTED,
        subTasks: [],
        lastUpdated: Date.now() - 7200000, // 2 hours ago
        lastUpdatedBy: TaskUpdatedBy.USER,
      },
    ];

    // Store all tasks
    this.tasks.set(rootTask.uuid, rootTask);
    subTasks.forEach((task) => this.tasks.set(task.uuid, task));

    // Set the root task as current
    this.currentRootTaskUuid = rootTask.uuid;
  }

  /**
   * Creates a new task
   */
  createTask(name: string, description: string, parentTaskUuid?: string): string {
    const uuid = this.generateUuid();
    const now = Date.now();

    const newTask: SerializedTask = {
      uuid,
      name,
      description,
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: now,
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    this.tasks.set(uuid, newTask);

    // If this is a sub-task, add it to the parent's subTasks array
    if (parentTaskUuid) {
      const parentTask = this.tasks.get(parentTaskUuid);
      if (parentTask) {
        parentTask.subTasks.push(uuid);
        parentTask.lastUpdated = now;
        this.tasks.set(parentTaskUuid, parentTask);
      }
    }

    return uuid;
  }

  /**
   * Updates an existing task
   */
  updateTask(uuid: string, updates: Partial<SerializedTask>, updatedBy: TaskUpdatedBy): boolean {
    const task = this.tasks.get(uuid);
    if (!task) {
      return false;
    }

    const updatedTask: SerializedTask = {
      ...task,
      ...updates,
      uuid, // Ensure UUID cannot be changed
      lastUpdated: Date.now(),
      lastUpdatedBy: updatedBy,
    };

    this.tasks.set(uuid, updatedTask);
    return true;
  }

  /**
   * Gets a task by UUID
   */
  getTask(uuid: string): SerializedTask | undefined {
    return this.tasks.get(uuid);
  }

  /**
   * Gets a hydrated task tree (with sub-tasks populated)
   */
  getHydratedTask(uuid: string): HydratedTask | undefined {
    const task = this.tasks.get(uuid);
    if (!task) {
      return undefined;
    }

    const hydratedTask: HydratedTask = {
      ...task,
      subTasksData: task.subTasks
        .map((subTaskUuid) => this.getHydratedTask(subTaskUuid))
        .filter((subTask): subTask is HydratedTask => subTask !== undefined),
    };

    return hydratedTask;
  }

  /**
   * Sets the current root task UUID
   */
  setCurrentRootTaskUuid(uuid: string): void {
    this.currentRootTaskUuid = uuid;
  }

  /**
   * Gets the current root task UUID
   */
  getCurrentRootTaskUuid(): string | null {
    return this.currentRootTaskUuid;
  }

  /**
   * Updates a hydrated task tree by diffing it against the existing tree
   */
  updateHydratedTask(
    newTree: HydratedTask,
    updatedBy: TaskUpdatedBy,
  ): { created: number; updated: number; deleted: number } {
    let created = 0;
    let updated = 0;
    let deleted = 0;

    // Helper function to recursively process the tree
    const processTask = (task: HydratedTask): void => {
      const existingTask = this.tasks.get(task.uuid);

      if (existingTask) {
        // Update existing task
        const taskWithoutSubTasksData = { ...task };
        delete taskWithoutSubTasksData.subTasksData;
        this.updateTask(task.uuid, taskWithoutSubTasksData, updatedBy);
        updated++;
      } else {
        // Create new task
        const taskWithoutSubTasksData = { ...task };
        delete taskWithoutSubTasksData.subTasksData;
        this.tasks.set(task.uuid, taskWithoutSubTasksData);
        created++;
      }

      // Process sub-tasks
      if (task.subTasksData) {
        task.subTasksData.forEach((subTask) => processTask(subTask));
      }
    };

    // Process the entire tree
    processTask(newTree);

    // Note: For simplicity, we're not implementing deletion logic here
    // In a real implementation, you'd compare the existing tree with the new tree
    // and mark tasks for deletion that are no longer present

    return { created, updated, deleted };
  }
}

// Create a global instance of the mock task manager
const mockTaskManager = new MockTaskManager();

let currId = 0;
let isAgentAutoModeApproved = false;

function wrapAsyncMsg<ReqT extends WebViewMessageTypes, ResT extends WebViewMessageTypes>(
  request: AsyncWebViewMessage<ReqT>,
  baseResponse: ResT | null,
  error: string | null = null,
): AsyncWebViewMessage<ResT> {
  return {
    type: WebViewMessageType.asyncWrapper,
    requestId: request.requestId,
    error,
    baseMsg: baseResponse,
  };
}

const asyncHandler = new AsyncMsgHandler(
  mockHost.sendMessageToWebView,
  (handler: (msg: AsyncWebViewMessage<WebViewMessage>) => void): (() => void) => {
    const wrappedHandler = async (e: Event) => {
      assertCustomEvent(e);
      handler(e.detail);
    };
    mockHost.addEventListener("message-from-webview", wrappedHandler);
    return () => {
      mockHost.removeEventListener("message-from-webview", wrappedHandler);
    };
  },
);

function fakeSyncing() {
  let progress = 0;
  mockHost.sendMessageToWebView({
    type: WebViewMessageType.sourceFoldersSyncStatus,
    data: {
      status: SyncingStatus.longRunning,
      foldersProgress: [
        {
          folderRoot: "example-repo-root",
          progress: {
            newlyTracked: true,
            trackedFiles: 100,
            backlogSize: 100,
          },
        },
      ],
    },
  });

  function doUpdate() {
    progress += Math.floor(Math.random() * 20);
    progress = Math.min(progress, 100);
    const foldersProgress: Array<SourceFolderSyncingProgress> = [
      {
        folderRoot: "example-repo-root",
        progress: {
          newlyTracked: true,
          trackedFiles: 100,
          backlogSize: 100 - progress,
        },
      },
    ];
    mockHost.sendMessageToWebView({
      type: WebViewMessageType.sourceFoldersSyncStatus,
      data: {
        status: progress === 100 ? SyncingStatus.done : SyncingStatus.longRunning,
        foldersProgress,
      },
    });
    if (progress < 100) {
      setTimeout(doUpdate, 500 + Math.floor(Math.random() * 1000));
    }
  }

  setTimeout(doUpdate, 500 + Math.floor(Math.random() * 1000));
}

let _mockRequestId: number = 0;
asyncHandler.registerStreamHandler(
  WebViewMessageType.chatUserMessage,
  async function* (msg: ChatUserMessage): AsyncGenerator<ChatModelReply> {
    const userMsg = msg?.data.text;
    const requestId = `${_mockRequestId++}`;
    // Delay for a bit
    await new Promise((resolve) => setTimeout(resolve, 500 + Math.random() * 300));
    switch (userMsg?.trim().toLowerCase()) {
      case "stream": {
        for (let i = 0; i < STRESS_TEST_RESPONSE.length / 10; i += 1) {
          await new Promise((resolve) => setTimeout(resolve, 50));
          yield {
            type: WebViewMessageType.chatModelReply,
            data: {
              text: STRESS_TEST_RESPONSE.slice(i * 10, i * 10 + 10),
              requestId,
              streaming: true,
              workspaceFileChunks:
                i === 0
                  ? [
                      {
                        charStart: 0,
                        charEnd: 10,
                        blobName: "example/path/name",
                      },
                    ]
                  : [],
            },
          };
        }
        break;
      }
      case "longcodestream": {
        for (let i = 0; i < LONG_CODE_STREAM_RESPONSE.length / 10; i += 1) {
          await new Promise((resolve) => setTimeout(resolve, 50));
          yield {
            type: WebViewMessageType.chatModelReply,
            data: {
              text: LONG_CODE_STREAM_RESPONSE.slice(i * 10, i * 10 + 10),
              requestId,
              streaming: true,
              workspaceFileChunks: [],
            },
          };
        }
        break;
      }
      case "stress": {
        yield {
          type: WebViewMessageType.chatModelReply,
          data: {
            text: STRESS_TEST_RESPONSE,
            requestId,
            workspaceFileChunks: [
              {
                charStart: 0,
                charEnd: 10,
                file: {
                  repoRoot: "/home/<USER>/example-repo-root",
                  pathName: "example/path/to/file/known-file.ts",
                },
                blobName: "example/path/to/file/known-file.ts-blob-name-123",
              },
            ],
            // A fake tool call
            nodes: [
              /* eslint-disable @typescript-eslint/naming-convention */
              {
                id: 0,
                type: 5, // ChatResultNodeType.TOOL_USE
                content: "",
                tool_use: {
                  tool_use_id: "shell-tool-use-id",
                  tool_name: "shell",
                  input_json: JSON.stringify({
                    command: 'echo "hello world"\n'.repeat(5),
                    workingDirectory: "/home/<USER>/example-repo-root",
                  }),
                },
              },
              /* eslint-enable @typescript-eslint/naming-convention */
            ],
          },
        };
        break;
      }
      case "large-code-block": {
        yield {
          type: WebViewMessageType.chatModelReply,
          data: {
            text: LARGE_TEXT_BLOCK,
            requestId,
            workspaceFileChunks: [
              {
                charStart: 0,
                charEnd: 10,
                blobName: "example/path/name",
              },
            ],
          },
        };
        break;
      }
      case "html": {
        yield {
          type: WebViewMessageType.chatModelReply,
          data: {
            text: HTML_RESPONSE,
            requestId,
            workspaceFileChunks: [
              {
                charStart: 0,
                charEnd: 10,
                blobName: "example/path/name",
              },
            ],
          },
        };
        break;
      }
      case "fail": {
        throw new Error("This is an error. Oh no!");
      }
      case "syncing": {
        yield {
          type: WebViewMessageType.chatModelReply,
          data: {
            text: "Loading syncing messages.",
            requestId,
            workspaceFileChunks: [],
          },
        };

        fakeSyncing();
        break;
      }
      case "summary": {
        yield {
          type: WebViewMessageType.chatModelReply,
          data: {
            text: "triggering summary",
            requestId,
            workspaceFileChunks: [],
          },
        };
        mockHost.sendMessageToWebView({
          type: WebViewMessageType.shouldShowSummary,
        });
        break;
      }
      case "mermaid": {
        yield {
          type: WebViewMessageType.chatModelReply,
          data: {
            text: MERMAID_RESPONSE,
            requestId,
            workspaceFileChunks: [],
          },
        };
        break;
      }
      case "mermaid stream": {
        for (let i = 0; i < MERMAID_RESPONSE.length / 10; i += 1) {
          await new Promise((resolve) => setTimeout(resolve, 50));
          yield {
            type: WebViewMessageType.chatModelReply,
            data: {
              text: MERMAID_RESPONSE.slice(i * 10, i * 10 + 10),
              requestId,
              streaming: true,
              workspaceFileChunks: [],
            },
          };
        }
        break;
      }
      case SUMMARY_PROMPT.trim().toLocaleLowerCase(): {
        const response =
          "This is an example summary of the project.\nDopest project ever.\nTotes cool, many variables, much code.";
        for (let i = 0; i < response.length / 10; i += 1) {
          await new Promise((resolve) => setTimeout(resolve, 50 + Math.random() * 50));
          yield {
            type: WebViewMessageType.chatModelReply,
            data: {
              text: response.slice(i * 10, i * 10 + 10),
              requestId,
              streaming: true,
              workspaceFileChunks: [],
            },
          };
        }
        break;
      }
      case NEW_QUESTIONS_PROMPT.trim().toLowerCase(): {
        await new Promise((resolve) => setTimeout(resolve, 3500));
        yield {
          type: WebViewMessageType.chatModelReply,
          data: {
            text: "This is an example question.\nThis is the second example question, nothing too crazy, but it's the longest question of the bunch; We want to ensure a few lines of text is managed properly.\nLast question, whoop whoop.",
            requestId,
            workspaceFileChunks: [],
          },
        };
        break;
      }
      default:
        yield {
          type: WebViewMessageType.chatModelReply,
          data: {
            text: `echo: ${msg.data.text}`,
            requestId,
            workspaceFileChunks: [
              {
                charStart: 0,
                charEnd: 10,
                blobName: "example/path/name",
              },
            ],
          },
        };
        break;
    }

    // Delay for a bit then send memories udpated
    await new Promise((resolve) => setTimeout(resolve, 500 + Math.random() * 300));
    mockHost.sendMessageToWebView({
      type: WebViewMessageType.chatMemoryHasUpdates,
    });
  },
);

mockHost.addEventListener("message-from-webview", async (e: Event) => {
  if (!isCustomEvent(e)) {
    console.error("Unexpected event type: ", e);
    throw new Error("Unexpected mock message");
  }

  const requestId = `${currId++}`;

  const request = e.detail;

  // Wait a random duration between 0.1 and 0.5 seconds
  await new Promise((resolve) => setTimeout(resolve, 100 + Math.random() * 400));

  switch (request.type) {
    case WebViewMessageType.asyncWrapper: {
      const baseMsg = request.baseMsg;
      switch (baseMsg.type) {
        case WebViewMessageType.findRecentlyOpenedFilesRequest: {
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: WebViewMessageType.findRecentlyOpenedFilesResponse,
                data: [
                  {
                    repoRoot: "/home/<USER>/example-repo-root",
                    pathName: "this/is/open",
                  },
                  {
                    repoRoot: "/home/<USER>/example-repo-root",
                    pathName: "another/is/open",
                  },
                ],
              },
              null,
            ),
          );
          break;
        }
        case WebViewMessageType.findExternalSourcesRequest: {
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: WebViewMessageType.findExternalSourcesResponse,
                data: {
                  sources: [1, 2, 3, 4, 5].map((i) => {
                    return {
                      id: `${i}`,
                      name: `name-${i}-TESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTEST`,
                      title: `title-${i}-TESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTESTTEST`,
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      source_type: 1,
                    };
                  }),
                },
              },
              null,
            ),
          );
          break;
        }
        case WebViewMessageType.getRulesListRequest: {
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: WebViewMessageType.getRulesListResponse,
                data: [1, 2, 3].map((i) => {
                  return {
                    path: `.augment/rules/rule-${i}.md`,
                    content: `# Rule ${i}\n\nThis is a test rule for demonstration purposes.`,
                    type: i === 1 ? RuleType.ALWAYS_ATTACHED : RuleType.MANUAL,
                  };
                }),
              },
              null,
            ),
          );
          break;
        }
        case WebViewMessageType.resolveWorkspaceFileChunkRequest: {
          const chunk = request.baseMsg.data;
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(request, {
              type: WebViewMessageType.resolveWorkspaceFileChunkResponse,
              data: {
                repoRoot: "/home/<USER>/example-repo-root",
                pathName: chunk.blobName,
                fullRange: {
                  startLineNumber: 0,
                  startColumn: 0,
                  endLineNumber: 1,
                  endColumn: 1,
                },
              },
            }),
          );
          break;
        }
        case WebViewMessageType.findFileRequest: {
          const query = request.baseMsg.data;
          const relPath = query.relPath;
          if (isAugmentURI(relPath) || relPath.startsWith("https://")) {
            return mockHost.sendMessageToWebView(
              wrapAsyncMsg(
                request,
                {
                  type: WebViewMessageType.findFileResponse,
                  data: [],
                },
                null,
              ),
            );
          }

          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: WebViewMessageType.findFileResponse,
                data: [1, 2, 3, 4, 5].map((i) => {
                  return {
                    repoRoot: "/home/<USER>/example-repo-root",
                    pathName: `${query.relPath}/file-name-${i}`,
                  };
                }),
              },
              null,
            ),
          );
          break;
        }
        case WebViewMessageType.findFolderRequest: {
          const query = request.baseMsg.data;
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: WebViewMessageType.findFolderResponse,
                data: [1, 2, 3, 4, 5].map((i) => {
                  return {
                    repoRoot: "/home/<USER>/example-repo-root",
                    pathName: `${query.relPath}/folder-name-${i}`,
                  };
                }),
              },
              null,
            ),
          );
          break;
        }
        case WebViewMessageType.chatLoaded: {
          mockHost.sendMessageToWebView({
            type: WebViewMessageType.currentlyOpenFiles,
            data: [
              {
                repoRoot: "/home/<USER>/example-repo-root",
                pathName: "this/is/open",
              },
              {
                repoRoot: "/home/<USER>/example-repo-root",
                pathName: "another/is/open",
              },
              {
                repoRoot: "/home/<USER>/example-repo-root",
                pathName: "test/file-name-1",
              },
            ],
          });
          mockHost.sendMessageToWebView({
            type: WebViewMessageType.fileRangesSelected,
            data: [
              {
                repoRoot: "/home/<USER>/example-repo-root",
                pathName: "another/path/name",
                fullRange: {
                  startLineNumber: 109,
                  startColumn: 1,
                  endLineNumber: 142,
                  endColumn: 142,
                },
                originalCode: `
    id: crypto.randomUUID(),
    name: "",
    title: "",
    description: "",
    generationSource: "",
    supportedActions: [],
    unitOfCodeWork: {
      repoRoot: "",
      pathName: "",
      originalCode,
      modifiedCode,
      lineChanges: {
        lineChanges: [
          {
            originalStart: originalRange.startLineNumber,
            originalEnd: originalRange.endLineNumber,
            modifiedStart: modifiedRange.startLineNumber,`,
              },
            ],
          });
          mockHost.sendMessageToWebView({
            type: WebViewMessageType.sourceFoldersUpdated,
            data: {
              sourceFolders: [
                {
                  folderRoot: "/home/<USER>/example-repo-root",
                  repoRoot: "/home/<USER>/example-repo-root",
                },
                {
                  folderRoot: "/home/<USER>/another-repo-root",
                  repoRoot: "/home/<USER>/another-repo-root",
                },
              ],
            },
          });
          mockHost.sendMessageToWebView({
            type: WebViewMessageType.updateGuidelinesState,
            data: {
              userGuidelines: {
                enabled: false,
                overLimit: false,
                contents: "This is the user guidelines",
              },
              workspaceGuidelines: [
                {
                  workspaceFolder: "/home/<USER>/example-repo-root",
                  enabled: false,
                  overLimit: false,
                },
              ],
            },
          });
          mockHost.sendMessageToWebView({
            type: WebViewMessageType.sourceFoldersSyncStatus,
            data: {
              status: SyncingStatus.done,
              foldersProgress: [],
            },
          });
          // Add the agentEditListReady event
          mockHost.sendMessageToWebView({
            type: WebViewMessageType.chatAgentEditListHasUpdates,
          });

          // Simulate task updates by triggering a message to refresh tasks
          // The task store will call getHydratedTask with the root task UUID
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(request, {
              type: WebViewMessageType.chatInitialize,
              data: {
                enablePreferenceCollection: false,
                enableRetrievalDataCollection: false,
                enableDebugFeatures: true,
                enableSmartPaste: true,
                enableShareService: true,
                enableDesignSystemRichTextEditor: true,
                enableChatMultimodal: true,
                enableAgentMode: true,
                enableRichCheckpointInfo: true,
                modelDisplayNameToId: {
                  /* eslint-disable @typescript-eslint/naming-convention */
                  Augment: "binks-ug-chatanol1-18-reranker-chat",
                  Gemini: "gemini-1.5-flash",
                  Default: null,
                  /* eslint-enable @typescript-eslint/naming-convention */
                },
                agentMemoriesFilePathName: {
                  rootPath: "",
                  relPath: "memories/path/name",
                },
                // Pass through any initialSubscriptionInfo and initialSubscriptionDismissed from the story args
                initialSubscriptionInfo: request.baseMsg.data.initialSubscriptionInfo,
                initialSubscriptionDismissed: request.baseMsg.data.initialSubscriptionDismissed,
              },
            }),
          );
          break;
        }
        case WebViewMessageType.chatUserCancel:
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: WebViewMessageType.chatModelReply,
                data: {
                  text: "",
                  requestId,
                  workspaceFileChunks: [],
                },
              },
              null,
            ),
          );
          break;
        case WebViewMessageType.saveChat: {
          setTimeout(() => {
            mockHost.sendMessageToWebView(
              wrapAsyncMsg(request, {
                type: WebViewMessageType.saveChatDone,
                data: {
                  uuid: "mock-uuid",
                  url: "mock-url",
                },
              }),
            );
          }, 400);
          break;
        }
        case WebViewMessageType.resolveFileRequest: {
          const request = baseMsg.data;
          switch (request.relPath) {
            case "example/path/to/file/known-file.ts": {
              mockHost.sendMessageToWebView({
                type: WebViewMessageType.asyncWrapper,
                requestId: e.detail.requestId,
                error: null,
                baseMsg: {
                  type: WebViewMessageType.resolveFileResponse,
                  data: {
                    repoRoot: "/home/<USER>/example-repo-root",
                    pathName: "example/path/to/file/known-file.ts",
                    fileType: FileType.file,
                  },
                },
              });
              break;
            }
            case "example/path/to/dir/known-dir": {
              mockHost.sendMessageToWebView({
                type: WebViewMessageType.asyncWrapper,
                requestId: e.detail.requestId,
                error: null,
                baseMsg: {
                  type: WebViewMessageType.resolveFileResponse,
                  data: {
                    repoRoot: "/home/<USER>/example-repo-root",
                    pathName: "example/path/to/dir/known-dir",
                    fileType: FileType.directory,
                  },
                },
              });
              break;
            }
            default:
              // Unknown file response
              console.debug("Ignoring requested file: ", request.relPath);
              mockHost.sendMessageToWebView({
                type: WebViewMessageType.asyncWrapper,
                requestId: e.detail.requestId,
                error: null,
                baseMsg: {
                  type: WebViewMessageType.resolveFileResponse,
                  data: null,
                },
              });
              break;
          }
          break;
        }
        case WebViewMessageType.toolCheckSafe: {
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(request, {
              type: WebViewMessageType.toolCheckSafeResponse,
              data: { isSafe: false },
            }),
          );
          break;
        }
        case WebViewMessageType.findSymbolRequest: {
          switch (baseMsg.data.query) {
            case "KnownSymbol.demo": {
              mockHost.sendMessageToWebView({
                type: WebViewMessageType.asyncWrapper,
                requestId: e.detail.requestId,
                error: null,
                baseMsg: {
                  type: WebViewMessageType.findSymbolResponse,
                  data: [
                    {
                      file: {
                        repoRoot: "/home/<USER>/example-repo-root",
                        pathName: "example/path/to/file/known-symbol-file.ts",
                        fileType: FileType.file,
                        range: {
                          startLineNumber: 1,
                          startColumn: 1,
                          endLineNumber: 1,
                          endColumn: 1,
                        },
                      },
                    },
                  ],
                },
              });
              break;
            }
            default:
              // Unknown symbol response
              console.debug("Ignoring requested symbol: ", baseMsg.data.query);
              mockHost.sendMessageToWebView({
                type: WebViewMessageType.asyncWrapper,
                requestId: e.detail.requestId,
                error: null,
                baseMsg: {
                  type: WebViewMessageType.findSymbolResponse,
                  data: [],
                },
              });
              break;
          }
          break;
        }
        case WebViewMessageType.checkAgentAutoModeApproval: {
          // Mock agent auto mode approval as false
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: WebViewMessageType.checkAgentAutoModeApprovalResponse,
                data: isAgentAutoModeApproved,
              },
              null,
            ),
          );
          break;
        }

        case WebViewMessageType.setAgentAutoModeApproved: {
          isAgentAutoModeApproved = baseMsg.data;
          // Mock setting agent auto mode approval
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: WebViewMessageType.empty,
              },
              null,
            ),
          );
          break;
        }

        case AgentWebViewMessageType.getEditListRequest: {
          const { fromCheckpointNumber, toCheckpointNumber } = request.baseMsg.data ?? {};
          const totalCheckpointCount = 3;

          // Helper to create mock file changes
          const createMockFile = (
            path: string,
            added: number,
            removed: number,
            offsetHours = 0,
          ) => {
            const now = new Date();
            now.setHours(now.getHours() - offsetHours);
            return {
              qualifiedPathName: {
                rootPath: "/home/<USER>/example-repo-root",
                relPath: path,
              },
              changesSummary: {
                totalAddedLines: added,
                totalRemovedLines: removed,
              },
            };
          };

          // Different data based on version range
          let edits;
          if (fromCheckpointNumber === 0 && toCheckpointNumber !== undefined) {
            // Cumulative changes (from beginning to specific version)
            // Create a long list of edits to test scrolling
            edits = [
              createMockFile("src/main.ts", 25, 10, 0), // current
              createMockFile("src/utils/helper.ts", 15, 8, 24), // yesterday
              createMockFile("src/components/feature.ts", 30, 20, 72), // 3 days ago
              createMockFile("src/models/user.ts", 18, 5, 1), // 1 hour ago
              createMockFile("src/models/product.ts", 22, 12, 2), // 2 hours ago
              createMockFile("src/services/auth.ts", 35, 15, 3), // 3 hours ago
              createMockFile("src/services/api.ts", 28, 10, 4), // 4 hours ago
              createMockFile("src/utils/format.ts", 12, 6, 5), // 5 hours ago
              createMockFile("src/utils/validation.ts", 20, 8, 6), // 6 hours ago
              createMockFile("src/components/button.ts", 15, 5, 7), // 7 hours ago
              createMockFile("src/components/input.ts", 18, 7, 8), // 8 hours ago
              createMockFile("src/components/modal.ts", 25, 12, 9), // 9 hours ago
              createMockFile("src/components/dropdown.ts", 22, 9, 10), // 10 hours ago
              createMockFile("src/components/table.ts", 40, 20, 11), // 11 hours ago
              createMockFile("src/components/form.ts", 30, 15, 12), // 12 hours ago
              createMockFile("src/components/card.ts", 18, 8, 13), // 13 hours ago
              createMockFile("src/components/avatar.ts", 12, 4, 14), // 14 hours ago
              createMockFile("src/components/tooltip.ts", 15, 6, 15), // 15 hours ago
              createMockFile("src/components/toast.ts", 20, 10, 16), // 16 hours ago
              createMockFile("src/empty.ts", 0, 0, 1), // 1 hour ago with no changes
            ];
          } else if (
            toCheckpointNumber !== undefined &&
            fromCheckpointNumber === toCheckpointNumber - 1
          ) {
            // Single checkpoint changes - also make this long
            edits = [
              createMockFile("src/main.ts", 5, 2, 1), // 1 hour ago
              createMockFile("src/components/feature.ts", 8, 4, 2), // 2 hours ago
              createMockFile("src/models/user.ts", 6, 2, 3), // 3 hours ago
              createMockFile("src/models/product.ts", 7, 3, 4), // 4 hours ago
              createMockFile("src/services/auth.ts", 10, 5, 5), // 5 hours ago
              createMockFile("src/services/api.ts", 8, 3, 6), // 6 hours ago
              createMockFile("src/utils/format.ts", 4, 2, 7), // 7 hours ago
              createMockFile("src/utils/validation.ts", 6, 3, 8), // 8 hours ago
              createMockFile("src/components/button.ts", 5, 2, 9), // 9 hours ago
              createMockFile("src/components/input.ts", 6, 2, 10), // 10 hours ago
              createMockFile("src/components/modal.ts", 8, 4, 11), // 11 hours ago
              createMockFile("src/components/dropdown.ts", 7, 3, 12), // 12 hours ago
              createMockFile("src/empty.ts", 0, 0, 1), // 1 hour ago with no changes
            ];
          } else {
            // Default or other ranges - also make this long
            edits = [
              createMockFile("src/main.ts", 8, 3, 0), // current
              createMockFile("src/utils/helper.ts", 4, 2, 48), // 2 days ago
              createMockFile("src/components/feature.ts", 10, 5, 24), // 1 day ago
              createMockFile("src/models/user.ts", 6, 2, 72), // 3 days ago
              createMockFile("src/models/product.ts", 7, 3, 96), // 4 days ago
              createMockFile("src/services/auth.ts", 10, 5, 120), // 5 days ago
              createMockFile("src/services/api.ts", 8, 3, 144), // 6 days ago
              createMockFile("src/utils/format.ts", 4, 2, 168), // 7 days ago
              createMockFile("src/utils/validation.ts", 6, 3, 192), // 8 days ago
              createMockFile("src/components/button.ts", 5, 2, 216), // 9 days ago
              createMockFile("src/components/input.ts", 6, 2, 240), // 10 days ago
              createMockFile("src/empty.ts", 0, 0, 1), // 1 hour ago with no changes
            ];
          }

          console.log("Sending mock agent edit list response: ", edits);

          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: AgentWebViewMessageType.getEditListResponse,
                data: {
                  edits,
                  totalCheckpointCount,
                  latestTimestamp: Date.now(),
                },
              },
              null,
            ),
          );
          break;
        }
        case WebViewMessageType.chatModeChanged: {
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: WebViewMessageType.chatModeChanged,
                data: request.baseMsg.data,
              },
              null,
            ),
          );
          break;
        }
        case WebViewMessageType.getChatRequestIdeStateRequest: {
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: WebViewMessageType.getChatRequestIdeStateResponse,
                data: {
                  /* eslint-disable @typescript-eslint/naming-convention */
                  workspace_folders: [
                    {
                      repository_root: "/home/<USER>/example-repo-root",
                      folder_root: "/home/<USER>/example-repo-root/example-folder",
                    },
                  ],
                  workspace_folders_unchanged: false,
                  /* eslint-enable @typescript-eslint/naming-convention */
                },
              },
              null,
            ),
          );
          break;
        }
        case TaskWebViewMessageType.createTaskRequest: {
          const createData = request.baseMsg.data;
          const uuid = mockTaskManager.createTask(
            createData.name,
            createData.description,
            createData.parentTaskUuid,
          );

          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: TaskWebViewMessageType.createTaskResponse,
                data: {
                  uuid,
                },
              },
              null,
            ),
          );
          break;
        }
        case TaskWebViewMessageType.updateTaskRequest: {
          const updateData = request.baseMsg.data;
          const success = mockTaskManager.updateTask(
            updateData.uuid,
            updateData.updates,
            updateData.updatedBy,
          );

          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: TaskWebViewMessageType.updateTaskResponse,
              },
              success ? null : "Task not found",
            ),
          );
          break;
        }
        case TaskWebViewMessageType.getHydratedTaskRequest: {
          const getTaskData = request.baseMsg.data;
          const task = mockTaskManager.getHydratedTask(getTaskData.uuid);

          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: TaskWebViewMessageType.getHydratedTaskResponse,
                data: {
                  task,
                },
              },
              null,
            ),
          );
          break;
        }
        case TaskWebViewMessageType.setCurrentRootTaskUuid: {
          const setRootData = request.baseMsg.data;
          mockTaskManager.setCurrentRootTaskUuid(setRootData.uuid);
          // This message type doesn't have a response
          break;
        }
        case TaskWebViewMessageType.updateHydratedTaskRequest: {
          const updateHydratedData = request.baseMsg.data;
          const result = mockTaskManager.updateHydratedTask(
            updateHydratedData.task,
            updateHydratedData.updatedBy,
          );

          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: TaskWebViewMessageType.updateHydratedTaskResponse,
                data: result,
              },
              null,
            ),
          );
          break;
        }
        case WebViewMessageType.getSubscriptionInfo: {
          let responseData = {
            activeSubscription: {
              usageBalanceDepleted: false,
              endDate: addDays(new Date(), 30).toISOString(),
            },
          };

          mockHost.sendMessageToWebView(
            wrapAsyncMsg(
              request,
              {
                type: WebViewMessageType.getSubscriptionInfoResponse,
                data: responseData,
              },
              null,
            ),
          );
          break;
        }
        default: {
          console.error(`Unknown async message type: ${request.baseMsg.type}`);
          break;
        }
      }
      break;
    }
    case WebViewMessageType.chatCreateFile: {
      console.log("chatCreateFile message received: ", request);
      break;
    }
    case WebViewMessageType.openFile: {
      console.log("openFile message received: ", request);
      break;
    }
    case WebViewMessageType.mainPanelPerformAction: {
      switch (request.data) {
        case "set-summary-ready":
          mockHost.sendMessageToWebView({
            type: WebViewMessageType.shouldShowSummary,
          });
          break;
        default:
          console.error(`Unknown main panel perform action: ${request.data}`);
          break;
      }
      break;
    }
    case WebViewMessageType.chatClearMetadata: {
      console.log("augmentCommand '%s' received: ", WebViewMessageType.chatClearMetadata, request);
      break;
    }
    default: {
      console.error(`Unknown message type: ${request.type}`);
      break;
    }
  }
});

const LARGE_TEXT_BLOCK = `
    \`\`\`
    This is a code block
    \`\`\`
`;

const STRESS_TEST_RESPONSE = `

This is a stress test response. It will contain a lot of unusual
outputs so we can easily test the UI.

# First heading

## Second heading

### Third heading

#### Fourth heading

##### Fifth heading

###### Sixth heading

This is a link to [Augment](https://augmentcode.com) with a title: [Augment](https://augmentcode.com "Augment")

This is an augment command link: [Augment](${makeAugmentURI("demo")}) with title: [Augment](${makeAugmentURI("demo")} "Augment")

- This is a list item
    \`\`\`
    This is a code block
    \`\`\`
- This is another list item
    - This is a nested list item
    - This is another nested list item
- Test multiple small codeblocks
- \`example 1\`
- \`example 2\`
- \`example 3\`
- This is a third list item
    1. This is a nested numbered list item
    2. This is another nested numbered list item
- This is a fourth list item
- \`example 4\` followed by some text
- This is an example with \`example 5\` in between some text
- This is an example with \`example 6\` just below the previous text
- These are some links we support:
    - [augmentcode.com](https://augmentcode.com)
    - With a title: [augmentcode.com](https://augmentcode.com "Augment")
    - [Augment URI](${makeAugmentURI("demo")})
    - With title: [Augment URI](${makeAugmentURI("demo")} "Augment")
    - \`example/path/to/file/unknown-file.ts\`
    - \`example/path/to/file/known-file.ts\`
    - \`example/path/to/dir/unknown-dir\`
    - \`example/path/to/dir/known-dir\`
    - \`UnknownSymbol.demo\`
    - \`KnownSymbol.demo\`
- \`This is a really really long codespan. It keeps going on and on to check whether wrapping works. \`
- \`This-is-a-really-really-long-codespan-with-a-really-really-long-title-that-is-longer-than-the-codespan-can-render.\`
- \`This/is/a/really/really/long/codespan/with/a/really/really/long/title/that/is/longer/than/the/codespan/can/render.\`


1. This is a numbered list item
    \`\`\`
    This is a code block
    \`\`\`
2. This is another numbered list item
    1. This is a nested numbered list item
    2. This is another nested numbered list item
    3. This is a third numbered list item
    4. This is a fourth numbered list item
    5. This is a fifth numbered list item
    6. This is a sixth numbered list item
    7. This is a seventh numbered list item
    8. This is an eighth numbered list item
    9. This is a ninth numbered list item
    10. This is a tenth numbered list item
    11. This is an eleventh numbered list item
3. This is a third numbered list item
    - This is a nested list item
    - This is another nested list item
4. This is a fourth numbered list item
5. This is a fifth numbered list item
6. This is a sixth numbered list item
7. This is a seventh numbered list item
8. This is an eighth numbered list item
9. This is a ninth numbered list item
10. This is a tenth numbered list item
11. This is an eleventh numbered list item

Example of **bold** text.

Example of *italic* text.

Example of ***bold italic*** text.

This is a block quote:

> This is a block quote.
>
> This is a second paragraph in the block quote.

Block quote with nested elements:

> #### The quarterly results look great!
>
> - Revenue was off the chart.
> - Profits were higher than ever.
>
>  *Everything* is going according to **plan**.

---

This is a small code block **without** syntax highlighting:

\`\`\`
function helloWorld() {
return "Hello World";
}
\`\`\`

This is a small code block **with** syntax highlighting:

\`\`\`javascript
function helloWorld() {
return "Hello World";
}
\`\`\`

This is a large code block:

\`\`\`javascript
function helloWorld() {
  return "Hello World with a really long piece of text that should trigger the block to scroll, otherwise it might look really really bad, you know? Like terrible. The worst.";
}
function helloWorld() {
  return 1 + 2 + 3 + 4;
}
function helloWorld() {
  return "Hello World";
}
function helloWorld() {
  return "Hello World";
}
function helloWorld() {
  return "Hello World";
}
function helloWorld() {
  return "Hello World";
}
function helloWorld() {
  return "Hello World";
}
function helloWorld() {
  return "Hello World";
}
\`\`\`

Here is a code block with metadata:
\`\`\`javascript path=test/path/to/file/this/is/reallyreallylong/filename/omgsolonghahaha.js mode=EDIT
function helloWorld() {
return "Hello World";
}
\`\`\`

Here is some text with inline code snippets:

At the command prompt, type \`nano\` to edit the \`hosts\` file.

---


This is a table:

| Column 1 | Column 2 | Column 3 | Column 4 | Column 5 | Column 6 | Column 7 | Column 8 | Column 9 | Column 10 | Column 11 | Column 12 | Column 13 | Column 14 | Column 15 | Column 16 | Column 17 | Column 18 | Column 19 | Column 20 |
| -------- | -------- | -------- | -------- | -------- | -------- | -------- | -------- | -------- | --------- | --------- | --------- | --------- | --------- | --------- | --------- | --------- | --------- | --------- | --------- |
| 1        | 2        | 3        | 4        | 5        | 6        | 7        | 8        | 9        | 10        | 11        | 12        | 13        | 14        | 15        | 16        | 17        | 18        | 19        | 20        |
| 21       | 22       | 23       | 24       | 25       | 26       | 27       | 28       | 29       | 30        | 31        | 32        | 33        | 34        | 35        | 36        | 37        | 38        | 39        | 40        |

This is an image:

![Augment Logo](https://images.unsplash.com/photo-1704123297654-9e5002d38c17?q=80&w=1596&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)


This is a single line of un-interrupted text.

!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!TheEnd.

\`What about a REALLY long code span? Let's make this go longer. On a single line. More. MORE. MOREEEEEEE!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!TheEnd.\`

`;

const HTML_RESPONSE = `
To change the key that triggers the \`onSendUserMessage\` function from Enter to Shift+Enter, you can modify the \`onKey\` function to check for the \`Shift\` key along with the \`Enter\` key. Here's how you can do it:

\`\`\`
<script lang="ts">
  import { onKey } from "../../common/utils/keypress";

  // ...

  function onSendUserMessage(event: KeyboardEvent) {
      if (!newMessage.trim()) {
          // Nothing to send
          return;
      }

      // ...
  }

  // ...
</script>

<!-- ... -->

<textarea
  rows="1"
  disabled={disableInput}
  on:keyup={onKey("Enter", onSendUserMessage, true)} // Pass true as the third argument to check for Shift key
  bind:value={newMessage}
  on:focus={() => (inputFocused = true)}
  on:blur={() => (inputFocused = false)}
  placeholder="Type a message..."
></textarea>

<!-- ... -->
\`\`\`

In the \`onKey\` function, the third argument is a boolean that indicates whether to check for the Shift key. If it's \`true\`, the function will only trigger the callback if the Shift key is also pressed.

Please note that the \`onKey\` function you're using might not support this feature. If it doesn't, you might need to implement a custom function to check for the Shift key. Here's an example of how you could do that:

\`\`\`svelte
<script lang="ts">
  function onKey(key: string, callback: (event: KeyboardEvent) => void, shiftKey = false) {
      return (event: KeyboardEvent) => {
          if (event.key === key && (!shiftKey || event.shiftKey)) {
              callback(event);
          }
      };
  }

  function onSendUserMessage(event: KeyboardEvent) {
      if (!newMessage.trim()) {
          // Nothing to send
          return;
      }

      // ...
  }

  // ...
</script>

<!-- ... -->

<textarea
  rows="1"
  disabled={disableInput}
  on:keyup={onKey("Enter", onSendUserMessage, true)} // Pass true as the third argument to check for Shift key
  bind:value={newMessage}
  on:focus={() => (inputFocused = true)}
  on:blur={() => (inputFocused = false)}
  placeholder="Type a message..."
></textarea>

<!-- ... -->
\`\`\`

In this custom \`onKey\` function, we're checking if the \`shiftKey\` property of the \`KeyboardEvent\` is \`true\` when the key is \`Enter\`. If \`shiftKey\` is \`true\`, the callback function is called. If \`shiftKey\` is \`false\`, the function does nothing.

`;
const MERMAID_RESPONSE = `
\`\`\`mermaid
graph TD
    A[Start] --> B{Is it?}
    B -- Yes --> C[OK]
    C --> D[Rethink]
    D --> B
    B -- No --> E[End]
\`\`\`
`;

const LONG_CODE_STREAM_RESPONSE = `
\`\`\`typescript
import React from "react";
import { useState, useEffect } from "react";
import axios from "axios";

const App = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    axios
      .get("https://api.example.com/data")
      .then((response) => {
        setData(response.data);
        setLoading(false);
      })
      .catch((error) => {
        setError(error);
        setLoading(false);
      });
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  return (
    <div>
      <h1>Data Display</h1>
      <ul>
        {data.map((item) => (
          <li key={item.id}>{item.name}</li>
        ))}
      </ul>
    </div>
  );
};

export default App;
\`\`\`
`;

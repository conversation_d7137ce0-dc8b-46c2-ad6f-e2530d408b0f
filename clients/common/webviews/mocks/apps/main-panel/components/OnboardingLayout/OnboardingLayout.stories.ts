/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import component from "./OnboardingLayoutMock.svelte";

const meta = {
  title: "app/MainPanel/components/OnboardingLayout",
  component,
  tags: ["autodocs"],
  parameters: {
    layout: "fullscreen",
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const WithLogo: Story = {
  args: {
    showLogo: true,
  },
};

export const WithoutLogo: Story = {
  args: {
    showLogo: false,
  },
};

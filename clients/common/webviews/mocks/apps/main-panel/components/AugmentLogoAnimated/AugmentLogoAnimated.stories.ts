/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import component from "./AugmentLogoAnimatedMock.svelte";

const meta = {
  title: "app/MainPanel/components/AugmentLogoAnimated",
  component,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

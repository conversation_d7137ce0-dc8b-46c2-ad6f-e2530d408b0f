/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import component from "$common-webviews/src/apps/main-panel/AwaitingSyncingPermission.svelte";

const meta = {
  title: "app/MainPanel/AwaitingSyncingPermission",
  component,
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Example: Story = {};

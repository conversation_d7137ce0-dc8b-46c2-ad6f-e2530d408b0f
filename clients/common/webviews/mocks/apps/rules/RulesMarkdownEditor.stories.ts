/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import component from "./RulesMarkdownEditorStory.svelte";
import { withMonaco } from "$common-webviews/mocks/design-system/monaco-provider/monaco-decorator";

const meta = {
  title: "app/Rules/RulesMarkdownEditor",
  component,
  tags: ["autodocs"],
  parameters: {
    docs: {
      description: {
        component:
          "Editor component for Augment rules markdown files with frontmatter configuration.",
      },
    },
  },
  decorators: [withMonaco],
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    text: `
# Augment Rules Example

This is an example of a rules markdown file that can be edited in the RulesMarkdownEditor.

## Purpose

Rules files define how Augment should behave when working with your codebase.

## Configuration

The \`alwaysApply\` setting in the frontmatter determines whether these rules should be automatically applied to all conversations.

## Example Rules

- Always use TypeScript for new files
- Follow the project's naming conventions
- Include appropriate documentation for public APIs
- Write unit tests for all new functionality
`,
    path: ".augment/rules/example-rule.md",
    alwaysApply: true,
  },
};

export const ManualApply: Story = {
  args: {
    text: `# Manual Apply Rules Example

This is an example of a rules markdown file with manual application setting.

## Purpose

These rules need to be manually applied to conversations.

## Configuration

The \`alwaysApply\` setting is set to \`false\` in the frontmatter.

## Example Rules

- Use these rules only when specifically requested
- Apply special formatting guidelines
- Follow project-specific conventions
`,
    path: ".augment/rules/manual-rule.md",
    alwaysApply: false,
  },
};

export const EmptyFile: Story = {
  args: {
    text: "",
    path: ".augment/rules/new-rule.md",
    alwaysApply: true,
  },
};

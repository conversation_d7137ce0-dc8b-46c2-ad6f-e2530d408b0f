<script lang="ts">
  import RulesMarkdownEditor from "$common-webviews/src/apps/rules/RulesMarkdownEditor.svelte";

  // Mock props
  export let text = `---
alwaysApply: true
---
# Augment Rules Example

This is an example of a rules markdown file that can be edited in the RulesMarkdownEditor.

## Purpose

Rules files define how Augment should behave when working with your codebase.

## Configuration

The \`alwaysApply\` setting in the frontmatter determines whether these rules should be automatically applied to all conversations.

## Example Rules

- Always use TypeScript for new files
- Follow the project's naming conventions
- Include appropriate documentation for public APIs
- Write unit tests for all new functionality
`;

  export let path = ".augment/rules/example-rule.md";
  export let alwaysApply = true;
</script>

<div style="padding: 2rem; max-width: 800px; margin: 0 auto;">
  <h2>Rules Markdown Editor</h2>
  <p>This component allows editing of rules markdown files with frontmatter configuration.</p>

  <div style="margin-top: 1rem;">
    <RulesMarkdownEditor {text} {path} {alwaysApply} />
  </div>
</div>

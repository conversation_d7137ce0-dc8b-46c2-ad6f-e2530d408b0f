/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import { type AutofixIteration, AutofixIterationStage } from "$vscode/src/autofix/autofix-state";
import AutofixDetailsHeader from "$common-webviews/src/apps/autofix/components/AutofixDetailsHeader.svelte";
const meta = {
  title: "app/Autofix/components/AutofixDetailsHeader",
  component: AutofixDetailsHeader,
  tags: ["autodocs"],
  argTypes: {},
} satisfies Meta<AutofixDetailsHeader>;

export default meta;

type Story = StoryObj<typeof meta>;

const randomId = () => Math.floor(Math.random() * 999999).toString();

const iterationTestRunning: () => AutofixIteration = () => ({
  id: randomId(),
  command: "npm test",
  isFirstIteration: true,
  currentStage: AutofixIterationStage.runTest,
});

const iterationWithSolutions: () => AutofixIteration = () => ({
  id: randomId(),
  command: "npm test",
  isFirstIteration: true,
  currentStage: AutofixIterationStage.applyFix,
  suggestedSolutions: [],
});

export const oneIterationTestRunning: Story = {
  args: {
    autofixData: {
      isAutofix: true,
      autofixIterations: [iterationTestRunning()],
    },
  },
};

export const twoIterations: Story = {
  args: {
    autofixData: {
      isAutofix: true,
      autofixIterations: [iterationWithSolutions(), iterationTestRunning()],
    },
  },
};

export const twoIterationsWithSolutions: Story = {
  args: {
    autofixData: {
      isAutofix: true,
      autofixIterations: [iterationWithSolutions(), iterationWithSolutions()],
    },
  },
};

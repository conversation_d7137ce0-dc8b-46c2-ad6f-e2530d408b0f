/* eslint-disable @typescript-eslint/naming-convention */
import AutofixDetailsFooter from "$common-webviews/src/apps/autofix/components/AutofixDetailsFooter.svelte";
import type { Meta, StoryObj } from "@storybook/svelte";
const meta = {
  title: "app/Autofix/components/AutofixDetailsFooter",
  component: AutofixDetailsFooter,
  tags: ["autodocs"],
  argTypes: {},
} satisfies Meta<AutofixDetailsFooter>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Normal: Story = {
  args: {},
};

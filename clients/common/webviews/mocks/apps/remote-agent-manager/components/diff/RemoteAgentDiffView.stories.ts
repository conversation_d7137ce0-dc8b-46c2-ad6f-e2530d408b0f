/* eslint-disable @typescript-eslint/naming-convention */
import type { <PERSON><PERSON>, StoryObj } from "@storybook/svelte";
import "../../remote-agent-manager-mock";
import component from "./RemoteAgentDiffViewStory.svelte";
import type { ChangedFile } from "$vscode/src/remote-agent-manager/types";
import { withMonaco } from "$common-webviews/mocks/design-system/monaco-provider/monaco-decorator";

interface StoryArgs {
  selectedChangeSet: number;
}

const meta = {
  title: "app/RemoteAgentManager/components/diff/RemoteAgentDiffView",
  // @ts-expect-error: wacky setup with dummy set of args
  component,
  argTypes: {
    selectedChangeSet: {
      name: "selectedChangeSet",
      type: "number",
      control: "select",
      options: [
        "Basic Component Changes",
        "File System Changes",
        "TypeScript and Legacy Code",
        "Empty",
      ],
      mapping: {
        "Basic Component Changes": 0,
        "File System Changes": 1,
        "TypeScript and Legacy Code": 2,
        Empty: 3,
      },
      defaultValue: 0,
    },
  },
} satisfies Meta<StoryArgs>;

export default meta;

const demoChangedFiles: ChangedFile[][] = [
  [
    {
      id: "0",
      old_path: "src/components/Navbar.tsx",
      new_path: "src/components/Navbar.tsx",
      old_contents:
        'import React from \'react\';\n\nconst Navbar = () => {\n  const title = "Site Title";\n  const links = ["Home", "About", "Contact"];\n  return (\n    <nav>\n      <h1>{title}</h1>\n      <ul>\n        {links.map(link => <li>{link}</li>)}\n      </ul>\n    </nav>\n  );\n};\n\nexport default Navbar;',
      new_contents:
        'import React from \'react\';\n\nconst Navbar = () => {\n  const title = "My Awesome Site";\n  const links = ["Home", "Services", "Portfolio", "Contact"];\n  return (\n    <nav>\n      <h1>{title}</h1>\n      <p>TODO: add tagline</p>\n      <ul>\n        {links.map(link => <li>{link}</li>)}\n      </ul>\n    </nav>\n  );\n};\n\nexport default Navbar;',
      change_type: 2,
    },
    {
      id: "1",
      old_path: "src/styles/main.css",
      new_path: "src/styles/main.css",
      old_contents:
        "body {\n  margin: 0;\n  padding: 0;\n  font-family: Arial, sans-serif;\n}\n\n.header {\n  background-color: #fff;\n  color: #000;\n}",
      new_contents:
        "body {\n  margin: 10px;\n  padding: 0;\n  font-family: 'Helvetica Neue', sans-serif;\n}\n\n.header {\n  background-color: #333;\n  color: #fff;\n}\n\n.footer {\n  background-color: #f8f8f8;\n  color: #444;\n}",
      change_type: 2,
    },
    {
      id: "2",
      old_path: "src/components/Footer.tsx",
      new_path: "src/components/Footer.tsx",
      old_contents:
        "import React from 'react';\n\nconst Footer = () => {\n  return (\n    <footer>\n      <p>© 2021 My Company</p>\n    </footer>\n  );\n};\n\nexport default Footer;",
      new_contents:
        "import React from 'react';\n\nconst Footer = () => {\n  return (\n    <footer>\n      <p>© 2022 My Company. All rights reserved.</p>\n    </footer>\n  );\n};\n\nexport default Footer;",
      change_type: 2,
    },
    {
      id: "3",
      old_path: "src/components/App.tsx",
      new_path: "src/components/App.tsx",
      old_contents:
        "import React from 'react';\nimport Navbar from './Navbar';\nimport Footer from './Footer';\n\nconst App = () => {\n  return (\n    <div>\n      <Navbar />\n      <main>\n        <h1>Welcome to My Site</h1>\n        <p>This is the main content of the site.</p>\n      </main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default App;",
      new_contents:
        "import React from 'react';\nimport Navbar from './Navbar';\nimport Footer from './Footer';\n\nconst App = () => {\n  return (\n    <div>\n      <Navbar />\n      <main>\n        <h1>Welcome to My Awesome Site</h1>\n        <p>This is the main content of the site.</p>\n      </main>\n      <Footer />\n   </div>\n  );\n};\n\nexport default App;",
      change_type: 2,
    },
  ],
  [
    {
      id: "4",
      old_path: "src/utils/dateHelper.js",
      new_path: "src/utils/timeHelper.js",
      old_contents:
        "function formatDate(date) {\n  const d = new Date(date);\n  return d.toISOString().split('T')[0];\n}\n\nmodule.exports = { formatDate };",
      new_contents:
        "function formatDate(date) {\n  const d = new Date(date);\n  return d.toLocaleDateString();\n}\n\nfunction formatTime(date) {\n  const d = new Date(date);\n  return d.toLocaleTimeString();\n}\n\nmodule.exports = { formatDate, formatTime };",
      change_type: 3,
    },
    {
      id: "5",
      old_path: "src/config/appConfig.json",
      new_path: "src/config/appConfig.json",
      old_contents:
        '{\n  "env": "development",\n  "version": "1.0.0",\n  "features": {\n    "logging": true,\n    "debug": true\n  }\n}',
      new_contents:
        '{\n  "env": "production",\n  "version": "1.1.0",\n  "features": {\n    "logging": false,\n    "debug": false,\n    "tracking": true\n  }\n}',
      change_type: 2,
    },
  ],
  [
    {
      id: "6",
      old_path: "src/index.ts",
      new_path: "src/index.ts",
      old_contents:
        "import React from 'react';\nimport ReactDOM from 'react-dom';\nimport App from './App';\n\nReactDOM.render(<App />, document.getElementById('root'));",
      new_contents:
        "import React from 'react';\nimport ReactDOM from 'react-dom';\nimport App from './App';\n\nconsole.log('Initializing app');\nReactDOM.render(<App />, document.getElementById('root'));",
      change_type: 2,
    },
    {
      id: "7",
      old_path: "src/services/api.ts",
      new_path: "src/services/api.ts",
      old_contents:
        "import axios from 'axios';\n\nexport function getData(endpoint: string) {\n  return axios.get(endpoint);\n}\n\nexport function postData(endpoint: string, data: any) {\n  return axios.post(endpoint, data);\n}",
      new_contents:
        "import axios from 'axios';\n\nexport function getData(endpoint: string) {\n  return axios.get(endpoint);\n}\n\nexport function postData(endpoint: string, data: any) {\n  return axios.post(endpoint, data).catch(error => console.error(error));\n}\n\nexport function putData(endpoint: string, data: any) {\n  return axios.put(endpoint, data);\n}",
      change_type: 2,
    },
    {
      id: "8",
      old_path: "src/old/legacyCode.js",
      new_path: "",
      old_contents:
        'function legacyFunction() {\n  return "This is legacy code";\n}\n\nmodule.exports = { legacyFunction };',
      new_contents: "",
      change_type: 1,
    },
  ],
  [],
];

type Story = StoryObj<typeof meta> & { args: StoryArgs };

export const Example: Story = {
  args: {
    selectedChangeSet: 0,
    changedFiles: [],
  },
  render: (args) => ({
    Component: component,
    props: {
      // @ts-expect-error: wacky setup with dummy set of args
      changedFiles: demoChangedFiles[args.selectedChangeSet],
    },
  }),
  decorators: [withMonaco],
};

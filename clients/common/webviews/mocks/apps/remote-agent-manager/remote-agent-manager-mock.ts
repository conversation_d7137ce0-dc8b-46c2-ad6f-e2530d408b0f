import mockAgentsChatHistory from "./mock-agents-chat-history-with-tools.json";
import mockAgents from "./mock-agents.json";

import {
  RemoteAgentStatus,
  type GetRemoteAgentChatHistoryResponse,
  type RemoteAgent,
} from "$vscode/src/remote-agent-manager/types";
import {
  WebViewMessageType,
  type AsyncWebViewMessage,
  type RemoteAgentChatRequestMessage,
  type WebViewMessage,
} from "$vscode/src/webview-providers/webview-messages";
import { isCustomEvent, mockHost } from "../../hosts/mock-host";

function wrapAsyncMsg<ReqT extends WebViewMessage, ResT extends WebViewMessage>(
  request: AsyncWebViewMessage<ReqT>,
  baseResponse: ResT | null,
  error: string | null = null,
): AsyncWebViewMessage<ResT> {
  return {
    type: WebViewMessageType.asyncWrapper,
    requestId: request.requestId,
    error,
    baseMsg: baseResponse,
  };
}
import { getDiffExplanation, getResponseFromLLM } from "$vscode/src/utils/get-diff-explanation";

const MOCK_AGENTS = mockAgents as RemoteAgent[];
const MOCK_AGENTS_CHAT_HISTORY = mockAgentsChatHistory as GetRemoteAgentChatHistoryResponse;

/**
 * Update mockAgentChatHistory.chat_history with new messages
 */
const updateMockAgentChatHistory = () => {
  MOCK_AGENTS_CHAT_HISTORY.chat_history[
    MOCK_AGENTS_CHAT_HISTORY.chat_history.length - 1
  ].exchange.response_text += " " + Math.random();
  console.log(
    "Updated mock agent chat history",
    MOCK_AGENTS_CHAT_HISTORY.chat_history[MOCK_AGENTS_CHAT_HISTORY.chat_history.length - 1].exchange
      .response_text,
  );
};
let intervalID = setInterval(updateMockAgentChatHistory, 5000);

const handleSendMessageRequest = (message: string, agentId: string) => {
  console.log("User message: ", message);
  mockAgentsChatHistory.chat_history.push({
    exchange: {
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: message,
      response_text: `Echo: ${message}`,
      request_id: "mock-request-id",
      response_nodes: [],
      request_nodes: [],
    },
    changed_files: [],
    /* eslint-enable @typescript-eslint/naming-convention */
  });
  const index = mockAgents.findIndex((agent) => agent.remote_agent_id === agentId);
  if (index !== -1) {
    MOCK_AGENTS[index].status = RemoteAgentStatus.agentRunning;
  }
  clearInterval(intervalID);
  intervalID = setInterval(updateMockAgentChatHistory, 5000);
};

// Set up the event listener immediately
mockHost.addEventListener("message-from-webview", async (e: Event) => {
  if (!isCustomEvent(e)) {
    console.error("Unexpected event type: ", e);
    throw new Error("Unexpected mock message");
  }

  switch (e.detail.type) {
    case WebViewMessageType.asyncWrapper: {
      const baseMsg = e.detail.baseMsg;
      switch (baseMsg.type) {
        case WebViewMessageType.getRemoteAgentOverviewsRequest: {
          console.log("remote-agent-manager-mock: received getRemoteAgentOverviewsRequest message");
          // Simulate a delay so we see the loading state
          await new Promise((resolve) => setTimeout(resolve, 2_000));
          console.log("Sending getRemoteAgentOverviewsResponse message from mock", MOCK_AGENTS);
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(e.detail, {
              type: WebViewMessageType.getRemoteAgentOverviewsResponse,
              data: {
                overviews: MOCK_AGENTS,
                maxRemoteAgents: 20,
                maxActiveRemoteAgents: 10,
              },
            }),
          );
          break;
        }
        case WebViewMessageType.getRemoteAgentChatHistoryRequest: {
          console.log(
            "remote-agent-manager-mock: received getRemoteAgentChatHistoryRequest message",
          );
          // const agentId = e.detail.data.agentId;
          const chatHistory = MOCK_AGENTS_CHAT_HISTORY.chat_history || [];
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(e.detail, {
              type: WebViewMessageType.getRemoteAgentChatHistoryResponse,
              data: {
                chatHistory,
              },
            }),
          );
          break;
        }
        case WebViewMessageType.createRemoteAgentRequest: {
          console.log("remote-agent-manager-mock: received createRemoteAgentRequest message");

          const newAgent = {
            /* eslint-disable @typescript-eslint/naming-convention */
            remote_agent_id: "mock-agent-id" + MOCK_AGENTS.length,
            session_summary: e.detail.data.prompt,
            status: 0 as RemoteAgentStatus,
            turn_summaries: [],
            started_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            diff: [],
            currentBranch: "main/mock-branch",
            is_setup_script_agent: e.detail.data.isSetupScriptAgent ?? false,
            workspace_setup: {
              starting_files: {
                github_commit_ref: {
                  repository_url: "https://github.com/augmentcode/augment",
                  git_ref: "main",
                },
              },
            },
            workspace_status: 1,
            expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
          };
          MOCK_AGENTS.push(newAgent);
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(e.detail, {
              type: WebViewMessageType.createRemoteAgentResponse,
              data: {
                success: true,
                agentId: newAgent.remote_agent_id,
              },
            }),
          );
          break;
        }
        case WebViewMessageType.deleteRemoteAgentRequest: {
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(e.detail, {
              type: WebViewMessageType.deleteRemoteAgentResponse,
              data: {
                success: true,
              },
            }),
          );
          break;
        }
        case WebViewMessageType.remoteAgentChatRequest: {
          console.log("remote-agent-manager-mock: received remoteAgentChatRequest message");
          const text = (baseMsg as RemoteAgentChatRequestMessage).data?.requestDetails
            ?.request_nodes?.[0].text_node?.content;
          const agentId = (baseMsg as RemoteAgentChatRequestMessage).data?.agentId;
          handleSendMessageRequest(text ?? "", agentId ?? "");
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(e.detail, {
              type: WebViewMessageType.remoteAgentChatResponse,
              data: {
                nodes: [],
              },
            }),
          );
          break;
        }
        case WebViewMessageType.remoteAgentInterruptRequest: {
          console.log("remote-agent-manager-mock: received remoteAgentInterruptRequest message");
          const agentId = baseMsg.data.agentId;
          const agent = MOCK_AGENTS.find((agent) => agent.remote_agent_id === agentId);
          console.log("Marking agent idle: ", agent);
          if (agent) {
            agent.status = RemoteAgentStatus.agentIdle;
            clearInterval(intervalID);
          }
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(e.detail, {
              type: WebViewMessageType.remoteAgentInterruptResponse,
              data: RemoteAgentStatus.agentIdle,
            }),
          );
          break;
        }
        case WebViewMessageType.remoteAgentDeleteRequest: {
          console.log("remote-agent-manager-mock: received remoteAgentDeleteRequest message");
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(e.detail, {
              type: WebViewMessageType.remoteAgentDeleteResponse,
              data: {
                success: true,
              },
            }),
          );
          break;
        }
        case WebViewMessageType.diffExplanationRequest: {
          console.log("remote-agent-manager-mock: received diffExplanationRequest message");
          const changedFiles = e.detail.baseMsg.data.changedFiles;
          const apikey = e.detail.baseMsg.data.apikey;
          try {
            const explanation = await getDiffExplanation(changedFiles, (prompt) =>
              getResponseFromLLM(prompt, apikey),
            );
            mockHost.sendMessageToWebView(
              wrapAsyncMsg(e.detail, {
                type: WebViewMessageType.diffExplanationResponse,
                data: {
                  explanation,
                },
              }),
            );
          } catch (error) {
            mockHost.sendMessageToWebView(
              wrapAsyncMsg(e.detail, {
                type: WebViewMessageType.diffExplanationResponse,
                data: {
                  explanation: [],
                },
              }),
            );
          }
          break;
        }
        case WebViewMessageType.applyChangesRequest: {
          await new Promise((resolve) => setTimeout(resolve, 300));
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(e.detail, {
              type: WebViewMessageType.applyChangesResponse,
              data: {
                success: true,
              },
            }),
          );
          break;
        }

        default: {
          console.error(`Unknown message type: ${baseMsg.type}`);
          break;
        }
      }
      break;
    }

    default: {
      console.error(`Unknown message type: ${e.detail.type}`);
      break;
    }
  }
});

/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import ToggleAugmentStory from "./ToggleAugmentStory.svelte";

const meta = {
  title: "design system/ToggleAugment",
  component: ToggleAugmentStory,
  tags: ["autodocs"],
  parameters: {
    docs: {
      description: {
        component: "A switch-style toggle component with different sizes and states.",
      },
    },
  },
} satisfies Meta<ToggleAugmentStory>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

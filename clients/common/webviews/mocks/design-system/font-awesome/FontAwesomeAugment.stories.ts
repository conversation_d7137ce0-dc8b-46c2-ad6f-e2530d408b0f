/* eslint-disable @typescript-eslint/naming-convention */
import type { <PERSON>a, StoryObj } from "@storybook/svelte";
import component from "./FontAwesomeStory.svelte";

const meta = {
  title: "design system/icons/FontAwesome",
  component,
  tags: ["autodocs"],
  argTypes: {
    name: {
      control: "select",
      options: [
        "regular/check",
        "regular/xmark",
        "regular/circle-info",
        "regular/triangle-exclamation",
        "regular/circle-check",
        "regular/circle-xmark",
        "regular/star",
        "regular/heart",
        "regular/user",
        "regular/gear",
        "regular/code",
        "regular/magnifying-glass",
        "brands/github",
        "brands/twitter",
      ],
      description: "The name of the FontAwesome icon to display",
    },
    size: {
      control: "select",
      options: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      description: "The size of the icon (0-9)",
    },
    color: {
      control: "select",
      options: ["accent", "neutral", "error", "success", "primary", "secondary", "current"],
      description: "The color of the icon",
    },
    type: {
      control: "select",
      options: ["all", "size", "color", "icon", "common"],
      description: "The type of showcase to display",
    },
    count: {
      control: "number",
      description: "The number of icons to display per page",
    },
    offset: {
      control: "number",
      description: "The offset of the icons to display",
    },
    search: {
      control: "text",
      description: "The search query to filter the icons",
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    type: "all",
    name: "regular/check",
    size: 2,
    color: "current",
  },
};

export const All: Story = {
  args: {
    type: "all",
    size: 2,
    color: "current",
  },
};

export const Sizes: Story = {
  args: {
    type: "size",
    name: "regular/star",
    color: "accent",
  },
};

export const Colors: Story = {
  args: {
    type: "color",
    size: 5,
    name: "regular/heart",
  },
};

export const Icons: Story = {
  args: {
    type: "icon",
    size: 4,
    name: "regular/user",
    color: "primary",
  },
};

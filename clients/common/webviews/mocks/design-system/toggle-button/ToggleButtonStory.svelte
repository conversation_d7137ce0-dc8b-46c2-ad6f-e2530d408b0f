<script lang="ts">
  import ToggleButtonAugment from "$common-webviews/src/design-system/components/ToggleButtonAugment.svelte";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import type { ButtonSize } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";

  const sizeVariants: ButtonSize[] = [1, 2, 3, 4];

  let value1 = "Option 1";
  let value2 = "Small";

  const handleSelect1 = (option: string) => {
    value1 = option;
    console.log("Selected value 1", value1);
    return true;
  };

  const handleSelect2 = (option: string) => {
    value2 = option;
    console.log("Selected value 2", value2);
    return true;
  };
</script>

<ColumnLayout>
  <section>
    <h3>Basic Usage</h3>
    <ToggleButtonAugment
      options={["Option 1", "Option 2", "Option 3"]}
      onSelectOption={handleSelect1}
    />
  </section>

  <section>
    <h3>Sizes</h3>
    {#each sizeVariants as size}
      <div style="margin-bottom: var(--ds-spacing-2);">
        <ToggleButtonAugment
          options={["Small", "Medium", "Large"]}
          onSelectOption={handleSelect2}
          {size}
        />
      </div>
    {/each}
  </section>

  <section>
    <h3>Disabled</h3>
    <ToggleButtonAugment options={["Option 1", "Option 2"]} onSelectOption={() => true} disabled />
  </section>
</ColumnLayout>

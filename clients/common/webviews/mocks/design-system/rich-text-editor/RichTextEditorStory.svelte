<script lang="ts">
  import Mention from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Mention";
  import RichTextEditorAugment from "$common-webviews/src/design-system/components/RichTextEditorAugment";
  import Placeholder from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Placeholder";
  import type { ContentData } from "$common-webviews/src/design-system/components/RichTextEditorAugment/types";
  import type { JSONContent } from "@tiptap/core";
  import type { IMentionable } from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Mention/types";
  import Keybindings from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Keybindings/Keybindings.svelte";
  import Image from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Image";
  import DropZone from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/DropZone/DropZone.svelte";

  // Get content from stories.ts file
  export let content: string | JSONContent | JSONContent[] | undefined = undefined;
  const onContentChanged = (data: ContentData) => {
    content = data.richTextJsonRepr;
  };

  // Manage query state
  let mentionQuery: string = "";
  let mentionables: IMentionable[] = [];
  const onQueryUpdate = (newQuery: string | undefined) => {
    if (newQuery === undefined) {
      mentionQuery = "";
      mentionables = [];
      return;
    }

    mentionQuery = newQuery;
    mentionables = [
      { id: "1", name: `Mention ${mentionQuery} 1`, label: `Mention ${mentionQuery} 1` },
      { id: "2", name: `Mention ${mentionQuery} 2`, label: `Mention ${mentionQuery} 2` },
      { id: "3", name: `Mention ${mentionQuery} 3`, label: `Mention ${mentionQuery} 3` },
    ];
  };
</script>

<div style="padding: 5rem;">
  <RichTextEditorAugment.Root>
    <RichTextEditorAugment.Content {content} {onContentChanged} />
    <Placeholder placeholder="New placeholder..." />
    <Keybindings
      shortcuts={{
        // eslint-disable-next-line @typescript-eslint/naming-convention
        "Mod-Enter": () => {
          // When mod + enter is pressed, update the content to "Pressed Mod-Enter"
          content = "Pressed Mod-Enter";
          return true;
        },
        // eslint-disable-next-line @typescript-eslint/naming-convention
        "\\": () => {
          // When backslash is pressed, update the content to "Pressed Backslash"
          content = "Pressed Backslash";
          return true;
        },
      }}
    />
    <Mention.Root triggerCharacter="@">
      <Mention.ChipTooltip>
        <svelte:fragment let:mentionable>
          {mentionable.name}
        </svelte:fragment>
      </Mention.ChipTooltip>
      <Mention.Menu.Root {mentionables} {onQueryUpdate}>
        {#each mentionables as mentionable}
          <Mention.Menu.Item {mentionable}>
            {mentionable.label}
          </Mention.Menu.Item>
        {/each}
      </Mention.Menu.Root>
    </Mention.Root>
    <Mention.Root triggerCharacter="/" allowedPrefixes={[]}>
      <Mention.ChipTooltip>
        <svelte:fragment let:mentionable>
          {mentionable.name}
        </svelte:fragment>
      </Mention.ChipTooltip>
      <Mention.Menu.Root {mentionables} {onQueryUpdate}>
        {#each mentionables as mentionable}
          <Mention.Menu.Item {mentionable}>
            {mentionable.label}
          </Mention.Menu.Item>
        {/each}
      </Mention.Menu.Root>
    </Mention.Root>
    <Image />
    <DropZone />
  </RichTextEditorAugment.Root>
  <RichTextEditorAugment.Root>
    <RichTextEditorAugment.Content />
    <Placeholder placeholder="New placeholder..." />
    <Keybindings
      shortcuts={{
        // eslint-disable-next-line @typescript-eslint/naming-convention
        "Mod-Enter": () => {
          // When mod + enter is pressed, update the content to "Pressed Mod-Enter"
          content = "Pressed Mod-Enter";
          return true;
        },
        // eslint-disable-next-line @typescript-eslint/naming-convention
        "\\": () => {
          // When backslash is pressed, update the content to "Pressed Backslash"
          content = "Pressed Backslash";
          return true;
        },
      }}
    />
    <Mention.Root triggerCharacter="@">
      <Mention.ChipTooltip>
        <svelte:fragment let:mentionable>
          {mentionable.name}
        </svelte:fragment>
      </Mention.ChipTooltip>
      <Mention.Menu.Root {mentionables} {onQueryUpdate}>
        {#each mentionables as mentionable}
          <Mention.Menu.Item {mentionable}>
            {mentionable.label}
          </Mention.Menu.Item>
        {/each}
      </Mention.Menu.Root>
    </Mention.Root>
    <Mention.Root triggerCharacter="/" allowedPrefixes={[]}>
      <Mention.ChipTooltip>
        <svelte:fragment let:mentionable>
          {mentionable.name}
        </svelte:fragment>
      </Mention.ChipTooltip>
      <Mention.Menu.Root {mentionables} {onQueryUpdate}>
        {#each mentionables as mentionable}
          <Mention.Menu.Item {mentionable}>
            {mentionable.label}
          </Mention.Menu.Item>
        {/each}
      </Mention.Menu.Root>
    </Mention.Root>
    <Image />
    <DropZone />
  </RichTextEditorAugment.Root>
</div>

/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import component from "./RichTextEditorStory.svelte";

const meta = {
  title: "design system/RichTextEditorAugment",
  component,
  tags: ["autodocs"],
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    content: "Hello, world! \nThis is a new line",
  },
};

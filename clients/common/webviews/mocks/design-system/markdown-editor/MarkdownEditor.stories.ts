/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import component from "./MarkdownEditorStory.svelte";

const meta = {
  title: "design system/MarkdownEditor",
  component,
  tags: ["autodocs"],
  parameters: {
    docs: {
      description: {
        component:
          "Markdown editor component for editing and managing markdown content.",
      },
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// Basic example with default content
export const Default: Story = {
  args: {},
};

// Example with empty content
export const Empty: Story = {
  args: {
    initialValue: "",
  },
};

// Example with longer content
export const LongContent: Story = {
  args: {
    initialValue: `# Markdown Editor with Long Content

## Introduction
This is an example of the Markdown Editor with longer content to demonstrate scrolling and editing capabilities.

## Features
- Edit markdown content
- Auto-save content
- Keyboard shortcuts (Ctrl+S or Cmd+S to save)

## Code Example
\`\`\`typescript
function example() {
  console.log("This is a code example");
  return true;
}
\`\`\`

## Lists
### Unordered List
- Item 1
- Item 2
- Item 3

### Ordered List
1. First item
2. Second item
3. Third item

## Tables
| Header 1 | Header 2 | Header 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Cell 4   | Cell 5   | Cell 6   |

## Conclusion
This is the end of the long content example.`,
  },
};

// Example with custom debounce value
export const CustomDebounce: Story = {
  args: {
    initialValue:
      "# Custom Debounce\n\nThis example uses a custom debounce value of 1000ms.",
    debounceValue: 1000,
  },
};

// Example with vertical resize
export const VerticalResize: Story = {
  args: {
    initialValue:
      "# Vertical Resize\n\nThis example allows vertical resizing of the editor.",
    resize: "vertical",
  },
};

// Example with both resize
export const BothResize: Story = {
  args: {
    initialValue:
      "# Both Resize\n\nThis example allows both horizontal and vertical resizing of the editor.",
    resize: "both",
  },
};

// Example with classic variant
export const ClassicVariant: Story = {
  args: {
    initialValue:
      "# Classic Variant\n\nThis example uses the classic variant styling.",
    variant: "classic",
  },
};

// Example with soft variant
export const SoftVariant: Story = {
  args: {
    initialValue:
      "# Soft Variant\n\nThis example uses the soft variant styling.",
    variant: "soft",
  },
};

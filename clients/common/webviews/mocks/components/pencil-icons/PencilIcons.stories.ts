/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import component from "./PencilIconsMock.svelte";

const meta = {
  title: "components/PencilIcon",
  component,
  tags: ["autodocs"],
  argTypes: {
    isDarkMode: {
      control: { type: "boolean" },
      description: "Toggles the dark theme",
    },
    isMask: {
      control: { type: "boolean" },
      description: "Whether to apply a mask to the icon",
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Example: Story = {
  args: {
    isDarkMode: false,
    isMask: false,
  },
};

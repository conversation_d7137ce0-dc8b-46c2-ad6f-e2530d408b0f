/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import component from "./CopyButtonStory.svelte";

const meta = {
  title: "components/CopyButton",
  component,
  tags: ["autodocs"],
  parameters: {
    docs: {
      description: {
        component: "Examples of CopyButton component with text slots.",
      },
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Example: Story = {
  args: {},
};

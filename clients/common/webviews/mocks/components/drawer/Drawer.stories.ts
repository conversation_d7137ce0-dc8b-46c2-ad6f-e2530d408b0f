/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import component from "./DrawerMock.svelte";

const meta = {
  title: "components/Drawer",
  component,
  tags: ["autodocs"],
  // Add argTypes to define the controls
  argTypes: {
    minimized: {
      control: { type: "boolean" },
      description: "Sets whether the drawer is closed",
    },
    deadzone: {
      control: { type: "number" },
      description:
        "The drag deadzone in pixels. When dragging the drawer below minWidth, if we go beyond 'deadzone' it fully closes.",
    },
    expandedMinWidth: {
      control: { type: "number" },
      description: "The minimum width of the drawer",
    },
    initialWidth: {
      control: { type: "number" },
      description: "The initial width of the drawer",
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Example: Story = {
  args: {},
};

export const ExampleHidden: Story = {
  args: {
    minimized: true,
    deadzone: 20,
    expandedMinWidth: 50,
    initialWidth: 300,
  },
};

/* eslint-disable @typescript-eslint/naming-convention */
import type { <PERSON>a, StoryObj } from "@storybook/svelte";
import component from "./NavigationMock.svelte";
import { createNavigationItem } from "$common-webviews/src/common/components/navigation/Navigation.svelte";
import ToolIcon from "$common-webviews/src/design-system/icons/augment/tools.svelte";
import CompassIcon from "$common-webviews/src/design-system/icons/augment/compass.svelte";
import ContextDoc from "$common-webviews/src/design-system/icons/augment/context-doc.svelte";

const meta = {
  title: "components/Navigation",
  component,
  tags: ["autodocs"],
  argTypes: {
    mode: {
      control: { type: "select", options: ["tree", "flat"] },
      description: "Navigation display mode: tree or flat",
    },
    group: {
      control: "text",
      description: "Default group name for items without a group",
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const TreeMode: Story = {
  args: {
    items: [
      createNavigationItem("Tools", "Configure your tools", ToolIcon, "section-tools"),
      createNavigationItem(
        "Orientation",
        "Codebase orientation settings",
        CompassIcon,
        "section-orientation",
      ),
      createNavigationItem("Context", "Workspace context settings", ContextDoc, "section-context"),
    ],
    mode: "tree",
  } as any,
};

export const FlatMode: Story = {
  args: {
    mode: "flat",
    items: [
      createNavigationItem("Tools", "Configure your tools", ToolIcon, "section-tools"),
      createNavigationItem(
        "Orientation",
        "Codebase orientation settings",
        CompassIcon,
        "section-orientation",
      ),
      createNavigationItem("Context", "Workspace context settings", ContextDoc, "section-context"),
    ],
  } as any,
};

export const WithGroups: Story = {
  args: {
    mode: "tree",
    items: [
      {
        ...createNavigationItem("Tools", "Configure your tools", ToolIcon, "section-tools"),
        group: "Settings",
      },
      {
        ...createNavigationItem(
          "Orientation",
          "Codebase orientation",
          CompassIcon,
          "section-orientation",
        ),
        group: "Settings",
      },
      {
        ...createNavigationItem("Context", "Workspace context", ContextDoc, "section-context"),
        group: "Workspace",
      },
    ],
  } as any,
};

export const WithComponents: Story = {
  args: {
    mode: "tree",
    items: [
      createNavigationItem("Tools", "Configure your tools", ToolIcon, "section-tools", component, {
        mode: "tree",
      }),
      createNavigationItem(
        "Orientation",
        "Codebase orientation",
        CompassIcon,
        "section-orientation",
      ),
    ],
  } as any,
};

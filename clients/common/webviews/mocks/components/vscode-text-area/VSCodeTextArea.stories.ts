/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import component from "./VSCodeTextAreaStory.svelte";

const meta = {
  title: "components/vscode/VSCodeTextArea",
  component,
  tags: ["autodocs"],
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Example: Story = {};

<script lang="ts">
  import VSCodeTextArea from "$common-webviews/src/common/components/vscode/VSCodeTextArea.svelte";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";

  // Define the resize options
  const resizeOptions = ["none", "both", "horizontal", "vertical"] as const;

  // Define row options
  const rowOptions = [1, 3, 5, 10];

  // Sample text for demonstration
  const sampleText = "This is a sample text for the VSCodeTextArea component.";
  const longSampleText = `The VSCodeTextArea component is a simple wrapper around the native textarea element.
It provides a consistent styling that matches the VSCode UI.
It supports various features like resizing, placeholder text, and disabled state.
This component is used in various parts of the Augment application.`;
</script>

<ColumnLayout>
  <h1>VSCodeTextArea</h1>
  <p>
    A textarea component styled to match VSCode's native UI. This component is used in VSCode
    webviews to provide a consistent look and feel with the rest of the VSCode interface.
  </p>

  <Fieldset title="Basic Usage">
    <div class="example-row">
      <VSCodeTextArea placeholder="Type something here..." />
    </div>
  </Fieldset>

  <Fieldset title="With Value">
    <div class="example-row">
      <VSCodeTextArea value={sampleText} />
    </div>
  </Fieldset>

  <Fieldset title="Disabled State">
    <div class="example-row">
      <VSCodeTextArea value={sampleText} disabled />
    </div>
  </Fieldset>

  <Fieldset title="Rows">
    <div class="example-grid">
      {#each rowOptions as rows}
        <div class="example-item">
          <h3>{rows} {rows === 1 ? "row" : "rows"}</h3>
          <VSCodeTextArea value={longSampleText} {rows} />
        </div>
      {/each}
    </div>
  </Fieldset>

  <Fieldset title="Resize Options">
    <div class="example-grid">
      {#each resizeOptions as resize}
        <div class="example-item">
          <h3>resize: {resize}</h3>
          <VSCodeTextArea value={longSampleText} rows={3} {resize} />
        </div>
      {/each}
    </div>
  </Fieldset>

  <Fieldset title="With Placeholder">
    <div class="example-row">
      <VSCodeTextArea placeholder="This is a placeholder text..." rows={3} />
    </div>
  </Fieldset>

  <Fieldset title="With Custom Class">
    <div class="example-row">
      <VSCodeTextArea value={sampleText} class="custom-textarea" rows={3} />
    </div>
  </Fieldset>
</ColumnLayout>

<style>
  .example-row {
    margin-bottom: 1rem;
    width: 100%;
  }

  .example-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    width: 100%;
  }

  .example-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  :global(.custom-textarea) {
    border-color: var(--vscode-editorWarning-foreground) !important;
  }
</style>

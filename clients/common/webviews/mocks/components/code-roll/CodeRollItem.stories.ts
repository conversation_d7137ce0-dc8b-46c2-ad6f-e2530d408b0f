/* eslint-disable @typescript-eslint/naming-convention */
import component from "$common-webviews/src/common/components/code-roll/CodeRollItem.svelte";
import { type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";

import type { Meta, StoryObj } from "@storybook/svelte";

import sample0 from "./samples/suggestion-0";
import sample1 from "./samples/suggestion-1";
import sample2 from "./samples/suggestion-2";
import sample3 from "./samples/suggestion-3";
import sample4 from "./samples/suggestion-4";
import sample5 from "./samples/suggestion-5";
import sample6 from "./samples/suggestion-6";

const edit0 = sample0 as unknown as IEditSuggestion;
const edit1 = sample1 as unknown as IEditSuggestion;
const edit2 = sample2 as unknown as IEditSuggestion;
const edit3 = sample3 as unknown as IEditSuggestion;
const edit4 = sample4 as unknown as IEditSuggestion;
const edit5 = sample5 as unknown as IEditSuggestion;
const edit6 = sample6 as unknown as IEditSuggestion;

const meta = {
  title: "components/code-roll/CodeRollItem",
  component,
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Example: Story = {
  args: {
    suggestions: [edit0, edit1],
    readFile: async function () {
      return (await import("./samples/suggestion-0.txt?raw")).default;
    },
    filepath: sample0.qualifiedPathName,
    onFileAction(action, filename) {
      console.log(action, filename);
    },
  },
};
export const Example0: Story = {
  args: {
    suggestions: [edit1],
    readFile: async function () {
      return (await import("./samples/suggestion-0.txt?raw")).default;
    },
    filepath: sample0.qualifiedPathName,
    onFileAction(action, filename) {
      console.log(action, filename);
    },
  },
};

export const Example1: Story = {
  args: {
    suggestions: [edit2],
    readFile: async function () {
      return (await import("./samples/suggestion-2.txt?raw")).default;
    },
    filepath: edit1.qualifiedPathName,
    onFileAction(action, filename) {
      console.log(action, filename);
    },
  },
};

export const Example2: Story = {
  args: {
    suggestions: [edit2],
    readFile: async function () {
      return (await import("./samples/suggestion-2.txt?raw")).default;
    },
    filepath: edit2.qualifiedPathName,
    onFileAction(action, filename) {
      console.log(action, filename);
    },
  },
};

export const Example3: Story = {
  args: {
    suggestions: [edit4],
    readFile: async function () {
      return (await import("./samples/suggestion-4.txt?raw")).default;
    },
    filepath: edit3.qualifiedPathName,
    onFileAction(action, filename) {
      console.log(action, filename);
    },
  },
};

export const Example5: Story = {
  args: {
    suggestions: [edit5],
    readFile: async function () {
      return (await import("./samples/suggestion-5.txt?raw")).default;
    },
    filepath: edit5.qualifiedPathName,
    onFileAction(action, filename) {
      console.log(action, filename);
    },
  },
};

export const Example6: Story = {
  args: {
    suggestions: [edit6],
    readFile: async function () {
      return (await import("./samples/suggestion-6.txt?raw")).default;
    },
    filepath: edit6.qualifiedPathName,
    onFileAction(action, filename) {
      console.log(action, filename);
    },
  },
};

/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import TextComboStory from "./TextComboStory.svelte";

const meta = {
  title: "components/TextCombo",
  component: TextComboStory,
  tags: ["autodocs"],
  argTypes: {
    size: {
      control: { type: "select" },
      options: [1, 2, 3],
    },
    align: {
      control: { type: "select" },
      options: ["left", "right"],
    },
    text: { control: "text" },
    grayText: { control: "text" },
    greyTextTruncateDirection: {
      control: { type: "select" },
      options: ["left", "right"],
    },
    shrink: { control: "boolean" },
    showIcon: { control: "boolean" },
  },
} satisfies Meta<TextComboStory>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
  args: {
    text: "Main text",
    grayText: "Secondary text",
  },
};

export const WithIcon: Story = {
  args: {
    text: "Pinned item",
    grayText: "Additional information",
    showIcon: true,
  },
};

export const Truncated: Story = {
  args: {
    text: "Compact view",
    grayText: "This is a very long piece of text that should truncate when the space is limited",
    shrink: true,
    showIcon: true,
  },
  parameters: {
    layout: "padded",
    styles: {
      container: {
        width: "200px",
      },
    },
  },
};

export const RightAligned: Story = {
  args: {
    text: "Status:",
    grayText: "Active",
    align: "right",
  },
};

export const LargeSize: Story = {
  args: {
    text: "Large text",
    grayText: "With secondary information",
    size: 3,
    showIcon: true,
  },
};

<script lang="ts">
  export let varNames: string[];
  const bodyStyles = window.getComputedStyle(document.body);
</script>

<table>
  <thead>
    <tr>
      <th>Name</th>
      <th>Value</th>
    </tr>
  </thead>
  {#each varNames as varName}
    <tr>
      <td><code>{varName}</code></td>
      <td>{bodyStyles.getPropertyValue(varName)}</td>
    </tr>
  {/each}
</table>

<style>
  table {
    border-collapse: collapse;
  }

  table,
  th,
  td {
    border: var(--augment-border);
  }

  th,
  td {
    padding: var(--ds-spacing-1);
  }

  th {
    background-color: var(--ds-color-accent-a10);
  }
</style>

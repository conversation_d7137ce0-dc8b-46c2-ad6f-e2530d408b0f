/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import ComponentLoader from "./ComponentLoaderMock.svelte";

const meta = {
  title: "components/ComponentLoader",
  component: ComponentLoader,
  tags: ["autodocs"],
  argTypes: {
    loadingMessages: {
      control: "object",
      description: "Array of messages to display while loading",
    },
    errorMessages: {
      control: "object",
      description: "Array of messages to display when an error occurs",
    },
    title: {
      control: "text",
      description: "Title to display above the loading message",
    },
    randomize: {
      control: "boolean",
      description: "Randomize the order of the loading messages",
    },
  },
} satisfies Meta<ComponentLoader>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic example with default settings
export const Default: Story = {
  args: {},
};

// Example with custom loading messages
export const CustomMessages: Story = {
  args: {
    loadingMessages: [
      "Initializing quantum compiler...",
      "Training AI pigeons...",
      "Downloading more RAM...",
      "Solving P vs NP...",
      "Counting to infinity...",
    ],
  },
};

// Example with custom title
export const CustomTitle: Story = {
  args: {
    title: "Loading Amazing Features",
    randomize: false,
    loadingMessages: [
      "Preparing something spectacular...",
      "Almost there...",
      "Just a moment longer...",
    ],
  },
};

// Example with faster message rotation
export const FastRotation: Story = {
  args: {
    randomize: false,
    loadingMessages: ["Loading.", "Loading..", "Loading...", "Loading...."],
  },
};

// Example with randomized messages
export const RandomizedMessages: Story = {
  args: {
    randomize: true,
    loadingMessages: [
      "Generating random numbers...",
      "Rolling dice...",
      "Shuffling deck...",
      "Spinning wheel of fortune...",
      "Playing rock, paper, scissors...",
    ],
  },
};

// Example with long loading messages
export const LongMessages: Story = {
  args: {
    loadingMessages: [
      "Did you know? The first computer bug was an actual bug - a moth found inside the Harvard Mark II computer in 1947.",
      "Fun fact: The word 'robot' comes from the Czech word 'robota' which means forced labor or work.",
      "Loading trivia: The first computer mouse was made of wood and was invented by Doug Engelbart in the 1960s.",
    ],
  },
};

// Example with error state
export const ErrorState: Story = {
  args: {
    error: new Error("Something went wrong!"),
    fakeRetry: 0,
    retryCount: 0,
  },
};

export const ErrorStateWithRetry: Story = {
  args: {
    fakeRetry: 2,
    fakeLoadTime: 1500,
  },
};

// Example with minimal configuration
export const Minimal: Story = {
  args: {
    loadingMessages: ["Loading..."],
  },
};

// Example with themed messages
export const ThemedMessages: Story = {
  args: {
    title: "Space Launch Sequence",
    loadingMessages: [
      "Initiating launch sequence...",
      "Checking fuel levels...",
      "Calibrating navigation systems...",
      "Starting countdown...",
      "T-minus 3..2..1...",
    ],
  },
};

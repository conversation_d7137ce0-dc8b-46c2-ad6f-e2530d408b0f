/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import CodeInsertButtonStory from "./CodeInsertButtonStory.svelte";

const meta = {
  title: "components/CodeInsertButton",
  component: CodeInsertButtonStory,
  tags: ["autodocs"],
  argTypes: {
    text: {
      control: "text",
      description: "Text shown after button",
      defaultValue: "Insert Code",
    },
    size: {
      control: { type: "select" },
      options: [1, 2, 3, 4],
      description: "Size of the button",
      defaultValue: 1,
    },
    color: {
      control: { type: "select" },
      options: ["neutral", "accent", "success", "error"],
      description: "Color of the button",
      defaultValue: "neutral",
    },
    tooltip: {
      control: "text",
      description: "Tooltip text",
      defaultValue: "Insert",
    },
    successText: {
      control: "text",
      description: "Text shown in tooltip after successful operation",
      defaultValue: "Inserted!",
    },
    stickyColor: {
      control: "boolean",
      description: "Whether the success/error color should persist after tooltip closes",
      defaultValue: false,
    },
    success: {
      control: "boolean",
      description: "Whether to show success/error",
      defaultValue: true,
    },
  },
  parameters: {
    docs: {
      description: {
        component:
          "A button component for inserting code, smart pasting, or direct applying changes.",
      },
    },
  },
} satisfies Meta<CodeInsertButtonStory>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Example: Story = {
  args: {
    text: "Insert Code",
    size: 1,
    color: "neutral",
    tooltip: "Insert",
    successText: "Inserted!",
    stickyColor: false,
    success: true,
  },
};

<script lang="ts" context="module">
  export type TextFieldVariant = "classic" | "surface" | "soft";
  export type TextFieldColor = "accent" | "neutral" | "error";
  export type TextFieldSize = 1 | 2 | 3;
</script>

<script lang="ts">
  import "@radix-ui/colors/gray.css";
  import "@radix-ui/colors/gray-alpha.css";
  import "@radix-ui/colors/black-alpha.css";

  import TextAugment, { type TextType } from "../components/TextAugment.svelte";
  import { dsColorAttribute } from "../_libs/component-utils";

  export let variant: TextFieldVariant = "surface";
  export let size: TextFieldSize = 2;
  export let type: TextType = "default";
  export let color: TextFieldColor | undefined = undefined;

  $: dsColors = color ? dsColorAttribute(color) : dsColorAttribute("accent");
</script>

<div
  {...dsColors}
  class={`c-base-text-input c-base-text-input--${variant} c-base-text-input--size-${size}`}
  class:c-base-text-input--has-color={color !== undefined}
>
  <TextAugment {type} {size}>
    <slot />
  </TextAugment>
</div>

<style>
  .c-base-text-input {
    --base-text-field-border-width: 1px;
    --base-text-field-border-radius: var(--ds-radius-2);
    --base-text-field-height: var(--ds-spacing-5);
    --base-text-field-input-padding: var(--ds-spacing-2);
    --base-text-field-selection-color: var(--ds-color-a5);
    --base-text-field-focus-color: var(--ds-color-8);
    --base-text-field-text-color: var(--gray-12);
    --base-text-field-placeholder-color: var(--gray-a10);
    --base-text-field-slot-color: var(--gray-a11);
    --base-text-field-slot-padding: var(--ds-spacing-1);

    display: flex;
    align-items: stretch;

    height: var(--base-text-field-height);
    border-radius: var(--base-text-field-border-radius);
    box-shadow: var(--base-text-field-box-shadow);

    background-color: var(--base-text-field-bg-color);
    color: var(--base-text-field-text-color);

    /* Disabled & Readonly states */
    &:has(.c-base-text-input__input:where(:disabled, :read-only)) {
      --base-text-field-selection-color: var(--gray-a5);
      --base-text-field-focus-color: var(--gray-8);
      --base-text-field-text-color: var(--gray-a11);
      --base-text-field-placeholder-opacity: 0.5;
      --base-text-field-placeholder-color: var(--gray-a11);
      --base-text-field-placeholder-cursor: not-allowed;
    }
  }

  .c-base-text-input:focus-within {
    outline: 2px solid var(--base-text-field-focus-color);
    outline-offset: -1px;
  }

  .c-base-text-input :global(.c-base-text-input__slot) {
    color: var(--base-text-field-slot-color);

    flex-shrink: 0;
    display: flex;
    align-items: center;
    cursor: text;
    padding-inline: var(--base-text-field-slot-padding);
  }

  .c-base-text-input :global(.c-base-text-input__input) {
    all: unset;

    width: 100%;
    cursor: text;

    /* Clip text to the border radius of the Root */
    border-radius: calc(var(--base-text-field-border-radius) - var(--base-text-field-border-width));

    /* Using text-indent adds padding to the start of the input but allows
      long text to use all available space in the input */
    text-indent: var(--base-text-field-input-padding);
  }

  .c-base-text-input :global(.c-base-text-input__input::placeholder) {
    color: var(--base-text-field-placeholder-color);
    opacity: var(--base-text-field-placeholder-opacity);
  }

  .c-base-text-input :global(.c-base-text-input__input:where(:placeholder-shown)) {
    cursor: var(--base-text-field-placeholder-cursor);
  }

  .c-base-text-input :global(.c-base-text-input__input::selection) {
    background-color: var(--base-text-field-selection-color);
  }

  /* Color */
  .c-base-text-input--has-color {
    --base-text-field-slot-color: var(--ds-color-a11);
  }

  /* Variant */
  .c-base-text-input--surface,
  .c-base-text-input--classic {
    --base-text-field-bg-color: var(--ds-surface);

    background-clip: content-box;

    &:has(.c-base-text-input__input:where(:autofill):not(:disabled, :read-only)) {
      /* Blend with focus color */
      --base-text-field-box-shadow: inset 0 0 0 1px var(--ds-color-a5),
        inset 0 0 0 1px var(--ds-color-a5);
      background-image: linear-gradient(var(--ds-color-a2), var(--ds-color-a2));
    }

    &:has(.c-base-text-input__input:where(:disabled, :read-only)) {
      /* Blend with grey */
      background-image: linear-gradient(var(--gray-a2), var(--gray-a2));
    }
  }

  .c-base-text-input--surface {
    --base-text-field-box-shadow: inset 0 0 0 var(--base-text-field-border-width) var(--gray-a7);

    &:has(.c-base-text-input__input:where(:disabled, :read-only)) {
      /* Blend with grey */
      --base-text-field-box-shadow: inset 0 0 0 var(--base-text-field-border-width) var(--gray-a6);
    }
  }

  .c-base-text-input--classic {
    --base-text-field-box-shadow: inset 0 0 0 1px var(--gray-a5), inset 0 1.5px 2px 0 var(--gray-a2),
      inset 0 1.5px 2px 0 var(--black-a2);
  }

  .c-base-text-input--soft {
    --base-text-field-bg-color: var(--ds-color-a3);
    --base-text-field-text-color: var(--ds-color-12);
    --base-text-field-placeholder-color: var(--ds-color-12);
    --base-text-field-placeholder-opacity: 0.6;
    --base-text-field-border-width: 0px;
    --base-text-field-slot-color: var(--ds-color-12);

    &.c-base-text-input--has-color {
      --base-text-field-slot-color: var(--ds-color-a11);
    }

    &:has(.c-base-text-input__input:where(:autofill):not(:disabled, :read-only)) {
      /* Use gray autofill color when component color is gray */
      --base-text-field-box-shadow: inset 0 0 0 1px var(--ds-color-a5),
        inset 0 0 0 1px var(--gray-a4);
    }

    &:has(.c-base-text-input__input:where(:disabled, :read-only)) {
      --base-text-field-bg-color: var(--gray-a3);
    }
  }

  /* Size */
  .c-base-text-input--size-2 {
    --base-text-field-height: var(--ds-spacing-6);
    --base-text-field-slot-padding: var(--ds-spacing-2);
  }

  .c-base-text-input--size-3 {
    --base-text-field-border-radius: var(--ds-radius-3);
    --base-text-field-height: var(--ds-spacing-7);
    --base-text-field-input-padding: var(--ds-spacing-2);
    --base-text-field-slot-padding: var(--ds-spacing-3);
  }

  /* Hide the number up/down arrows */
  .c-base-text-input__input:where([type="number"]) {
    -moz-appearance: textfield;
    appearance: textfield;
  }
  .c-base-text-input__input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
</style>

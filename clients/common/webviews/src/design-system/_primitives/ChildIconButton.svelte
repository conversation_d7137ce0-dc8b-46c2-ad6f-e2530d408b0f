<script lang="ts">
  import type {
    ButtonColor,
    ButtonRadius,
    ButtonSize,
    ButtonVariant,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import BaseButton from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";

  export let size: ButtonSize = 2;
  export let variant: ButtonVariant = "solid";
  export let color: ButtonColor = "accent";
  export let highContrast: boolean = false;
  export let disabled: boolean = false;
  export let radius: ButtonRadius = "medium";

  $: ({ class: className, ...restProps } = $$restProps);
</script>

<div class={`c-icon-btn c-icon-btn--size-${size}`}>
  <BaseButton
    {size}
    {variant}
    {color}
    {highContrast}
    {disabled}
    {radius}
    class={className}
    on:click
    on:keyup
    on:keydown
    on:mousedown
    on:mouseover
    on:focus
    on:mouseleave
    on:blur
    on:contextmenu
    {...restProps}
  >
    <slot />
  </BaseButton>
</div>

<style>
  .c-icon-btn {
    display: contents;
    --icon-color: var(--base-btn-color, currentColor);
    --icon-size: 16px;
  }
  .c-icon-btn :global(svg) {
    fill: var(--icon-color);
    width: var(--icon-size);
    height: var(--icon-size);
  }
  .c-icon-btn :global(.c-base-btn) {
    width: var(--icon-btn-size);
    height: var(--icon-btn-size);
  }

  .c-icon-btn :global(.c-base-btn.c-base-btn--ghost) {
    /* Position icon inline when ghost variant */
    padding: var(--icon-button-ghost-padding);
    margin: calc(var(--icon-button-ghost-padding) * -1);
  }

  .c-icon-btn--size-1 {
    --icon-btn-size: var(--ds-spacing-5);
    --icon-button-ghost-padding: var(--ds-spacing-1);
  }

  .c-icon-btn--size-2 {
    --icon-btn-size: var(--ds-spacing-6);
    --icon-button-ghost-padding: calc(var(--ds-spacing-1) * 1.5);
  }

  .c-icon-btn--size-3 {
    --icon-btn-size: var(--ds-spacing-7);
    --icon-button-ghost-padding: var(--ds-spacing-2);
  }

  .c-icon-btn--size-4 {
    --icon-btn-size: var(--ds-spacing-8);
    --icon-button-ghost-padding: var(--ds-spacing-3);
  }
</style>

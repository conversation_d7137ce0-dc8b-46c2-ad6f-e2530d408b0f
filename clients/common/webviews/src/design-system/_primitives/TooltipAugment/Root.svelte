<script lang="ts">
  import { setContext } from "svelte";
  import { TooltipContext } from "./context";
  import { TooltipTriggerOn } from "./types";
  import "./tippy-theme.css";

  export let defaultOpen: boolean | undefined = undefined;
  export let open: boolean | undefined = undefined;
  export let onOpenChange: ((open: boolean) => void) | undefined = undefined;
  export let delayDurationMs: number | undefined = undefined;
  export let nested: boolean | undefined = true;
  export let hasPointerEvents = true;
  export let offset: [number, number] | undefined = undefined;

  export let onHoverStart = () => {};
  export let onHoverEnd = () => {};
  export let triggerOn: TooltipTriggerOn[] = [TooltipTriggerOn.Hover, TooltipTriggerOn.Click];

  export const requestOpen = () => context.openTooltip();
  export const requestClose = () => context.closeTooltip();

  /**
   * Tippy theme name
   *
   * If undefined, no theming will be applied to the tooltip content. The
   * default Tippy theme is available using the name "default". Multiple themes
   * can be applied by providing a space separated list of theme names, such as
   * "default my-theme"
   *
   * https://atomiks.github.io/tippyjs/v6/themes/#creating-a-theme
   */
  export let tippyTheme: string | undefined = undefined;

  const context = new TooltipContext({
    defaultOpen,
    open,
    onOpenChange,
    delayDurationMs,
    nested,
    onHoverStart,
    onHoverEnd,
    triggerOn,
    tippyTheme,
    hasPointerEvents,
    offset,
  });
  setContext<TooltipContext>(TooltipContext.CONTEXT_KEY, context);

  $: context.externalControlSetOpen(open);
</script>

<slot />

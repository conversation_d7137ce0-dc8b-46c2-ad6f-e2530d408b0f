<script lang="ts">
  import { getContext } from "svelte";
  import { TooltipContext } from "./context";

  export let referenceClientRect: DOMRect | undefined = undefined;
  let className: string = "";
  export { className as class };

  const context: TooltipContext = getContext<TooltipContext>(TooltipContext.CONTEXT_KEY);

  const onClick = (e: MouseEvent) => {
    if (context.supportsClick) {
      context.toggleTooltip();
      e.stopPropagation();
    }
  };
</script>

<!-- Use `tabindex="-1"` to make the trigger wrapper not part of normal tab flow.
 This allows the end user to tab-focus to the slotted trigger (such as a ButtonAugment),
 but not the trigger wrapper itself. -->
<div
  class={`l-tooltip-trigger ${className}`}
  role="button"
  on:click={onClick}
  on:keydown
  use:context.registerTrigger={referenceClientRect}
  tabindex="-1"
>
  <slot />
</div>

<style>
  .l-tooltip-trigger {
    width: fit-content;
    height: fit-content;
    display: flex;
    align-items: center;
  }
</style>

/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { render } from "@testing-library/svelte";
import userEvent from "@testing-library/user-event";
import { expect, describe, test, afterEach, beforeEach, vi, type Mock } from "vitest";

// Load the mock host API before tests use the host
import TestTooltip from "./index.test.svelte";
import { type ComponentProps } from "svelte";
import { TooltipContext } from "./context";
import { CloseTooltipRequestEvent, TooltipTriggerOn } from "./types";

const INTERACTION_DURATION_MS = 33; // Based on 30FPS
const LONGER_HOVER_DURATION_MS = 300;
const HOVER_THRASH_DURATION_MS = 100; // Arbitrary

let component: ReturnType<typeof render<TestTooltip>>;
beforeEach(() => {
  vi.useFakeTimers();
});

afterEach(() => {
  vi.useRealTimers();
  vi.clearAllMocks();
  component?.unmount();
});

describe("TooltipAugment.svelte", () => {
  const expectNoTrigger = () => {
    const trigger = component.queryByTestId("test-trigger-button");
    expect(trigger).toBeNull();
  };
  const expectNoContent = () => {
    const content = component.queryByTestId("test-content-div");
    expect(content).toBeNull();
  };
  const expectTrigger = () => {
    const trigger = component.queryByTestId("test-trigger-button");
    expect(trigger).not.toBeNull();
    return trigger;
  };
  const expectContent = () => {
    const content = component.queryByTestId("test-content-div");
    expect(content).not.toBeNull();
    return content;
  };

  describe("No trigger", () => {
    test("uncontrolled content should not show on init", () => {
      expect.hasAssertions();
      component = render(TestTooltip, {
        testHasTrigger: false,
        testHasContent: true,
        rootProps: {},
        contentProps: {},
      });

      expectNoTrigger();
      expectNoContent();
    });

    test("controlled content should not show on init", () => {
      expect.hasAssertions();
      component = render(TestTooltip, {
        testHasTrigger: false,
        testHasContent: true,
        rootProps: {
          open: true,
        },
        contentProps: {},
      });

      expectNoTrigger();
      expectNoContent();
    });
  });

  describe("No content", () => {
    test("trigger should always show", async () => {
      expect.hasAssertions();
      component = render(TestTooltip, {
        testHasTrigger: true,
        testHasContent: true,
        rootProps: {},
        contentProps: {},
      });

      expectTrigger();
      expectNoContent();
    });

    test("controlled content should not show on init", async () => {
      expect.hasAssertions();
      component = render(TestTooltip, {
        testHasTrigger: true,
        testHasContent: false,
        rootProps: {
          open: true,
        },
        contentProps: {},
      });

      expectTrigger();
      expectNoContent();
    });

    test("changing closed => open should not show", async () => {
      expect.hasAssertions();
      component = render(TestTooltip, {
        testHasTrigger: true,
        testHasContent: false,
        rootProps: {
          open: false,
        },
        contentProps: {},
      });

      await component.rerender({
        rootProps: {
          open: true,
        },
      });
      expectTrigger();
      expectNoContent();
    });
  });

  describe("Has trigger and content", () => {
    describe("controlled", () => {
      let props: ComponentProps<TestTooltip>;

      beforeEach(() => {
        /* eslint-disable @typescript-eslint/no-unsafe-member-access */
        props = {
          testHasTrigger: true,
          testHasContent: true,
          onClickControlledTrigger: vi.fn(() => {
            props.rootProps!.open = !props.rootProps?.open;
          }),
          rootProps: {
            open: false,
            onOpenChange: vi.fn(),
            onHoverStart: vi.fn(() => {
              props.rootProps!.open = true;
            }),
            onHoverEnd: vi.fn(() => {
              props.rootProps!.open = false;
            }),
          },
          contentProps: {
            onClickOutside: vi.fn(() => {
              props.rootProps!.open = false;
            }),
            onEscapeKeyDown: vi.fn(() => {
              props.rootProps!.open = false;
            }),
          },
        };
        /* eslint-enable @typescript-eslint/no-unsafe-member-access */
        component = render(TestTooltip, props);
      });

      test("content does not show when open = false", async () => {
        expect.hasAssertions();
        props.rootProps.open = false;
        await component.rerender(props);
        expectTrigger();
        expectNoContent();
      });

      test("content does show when open = true", async () => {
        expect.hasAssertions();
        props.rootProps.open = true;
        await component.rerender(props);
        expectTrigger();
        expectContent();
      });

      test("onOpenChange called when changing props", async () => {
        props.rootProps.open = true;
        await component.rerender(props);
        expectTrigger();
        expectContent();

        props.rootProps.open = false;
        await component.rerender(props);

        // Content should be hidden
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        expect(props.rootProps.onOpenChange).toBeCalledWith(false);
        expectTrigger();
        expectNoContent();
      });

      test("onOpenChange called when window clicked", async () => {
        props.rootProps.open = true;
        await component.rerender(props);
        expectTrigger();
        expectContent();

        await clickWithFakeTime(document.body);
        await component.rerender(props);

        // Content should be hidden
        expect(props.rootProps.onOpenChange).toBeCalledWith(false);
        expectTrigger();
        expectNoContent();
      });

      test("onOpenChange *not* called when contents clicked", async () => {
        props.rootProps.open = true;
        await component.rerender({ rootProps: { open: true } });
        expectTrigger();
        const contents = expectContent();

        (props.rootProps.onOpenChange as Mock).mockReset();
        await clickWithFakeTime(contents!);
        expect(props.rootProps.onOpenChange).not.toBeCalled();

        // Content should still be visible
        expectContent();
        expectTrigger();
      });

      test("onOpenChange called with false when trigger clicked while open", async () => {
        props.rootProps.open = true;
        await component.rerender(props);
        expect(props.rootProps.onOpenChange).toHaveBeenLastCalledWith(true);
        const trigger = expectTrigger();
        expectContent();

        await clickWithFakeTime(trigger!);
        await component.rerender(props);

        // Content should be hidden
        expect(props.rootProps.onOpenChange).toHaveBeenCalledWith(false);
        expectNoContent();
        expectTrigger();
      });

      test("onOpenChange called with true when trigger clicked while closed", async () => {
        const trigger = expectTrigger();
        expectNoContent();

        await clickWithFakeTime(trigger!);
        await component.rerender(props);

        // Content should be visible
        expect(props.rootProps.onOpenChange).toHaveBeenLastCalledWith(true);
        expectContent();
        expectTrigger();
      });

      test("onOpenChange called with true when hovering trigger", async () => {
        const trigger = expectTrigger();
        expectNoContent();

        await hoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
        await component.rerender(props);

        expect(props.rootProps.onOpenChange).toHaveBeenLastCalledWith(true);
        expectContent();
      });

      test("onOpenChange called with false when unhovering trigger", async () => {
        expect.hasAssertions();
        const trigger = expectTrigger();
        expectNoContent();

        await hoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
        await component.rerender(props);
        expectContent();

        await unhoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(HOVER_THRASH_DURATION_MS);
        await component.rerender(props);

        expect(props.rootProps.onOpenChange).toHaveBeenLastCalledWith(false);
        expectNoContent();
      });

      test("onTriggerClick called when trigger clicked", async () => {
        const trigger = expectTrigger();
        expectNoContent();

        await clickWithFakeTime(trigger!);
        await component.rerender(props);

        // Content should be visible
        expect(props.onClickControlledTrigger).toBeCalled();
        expectContent();
        expectTrigger();
      });

      test("onClickOutside called when window clicked", async () => {
        const trigger = expectTrigger();
        expectNoContent();

        await clickWithFakeTime(trigger!);
        await component.rerender(props);
        expectContent();

        await clickWithFakeTime(document.body);
        await component.rerender(props);

        // Content should be hidden
        expect(props.contentProps.onClickOutside).toBeCalled();
        expectNoContent();
      });

      test("onClickOutside not called when contents clicked", async () => {
        const trigger = expectTrigger();
        expectNoContent();

        await clickWithFakeTime(trigger!);
        await component.rerender(props);
        expectContent();

        const content = expectContent();
        await clickWithFakeTime(content!);
        await component.rerender(props);

        // Content should still be visible
        expect(props.contentProps.onClickOutside).not.toBeCalled();
        expectContent();
        expectTrigger();
      });

      test("onEscapeKeyDown not called when escape pressed while closed", async () => {
        expectTrigger();
        expectNoContent();

        await keyboardWithFakeTime("{Escape}");
        expect(props.contentProps.onEscapeKeyDown).not.toBeCalled();
      });

      test("onEscapeKeyDown called when escape pressed while open", async () => {
        const trigger = expectTrigger();
        expectNoContent();

        await clickWithFakeTime(trigger!);
        await component.rerender(props);
        expectContent();

        await keyboardWithFakeTime("{Escape}");
        expect(props.contentProps.onEscapeKeyDown).toBeCalled();
      });

      test("onHoverStart called when hovering trigger", async () => {
        const trigger = expectTrigger();
        expectNoContent();

        await hoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
        await component.rerender(props);

        expect(props.rootProps.onHoverStart).toBeCalled();
        expectContent();
      });

      test("onHoverEnd called when unhovering trigger", async () => {
        const trigger = expectTrigger();
        expectNoContent();

        await hoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
        await component.rerender(props);
        expectContent();

        await unhoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(HOVER_THRASH_DURATION_MS);
        await component.rerender(props);

        expect(props.rootProps.onHoverEnd).toBeCalled();
        expectNoContent();
      });
    });

    describe("uncontrolled", () => {
      const onOpenChange = vi.fn();
      beforeEach(() => {
        component = render(TestTooltip, {
          testHasTrigger: true,
          testHasContent: true,
          rootProps: {
            onOpenChange,
          },
          contentProps: {},
        });
      });

      test("callback called when window clicked", async () => {
        const trigger = expectTrigger();
        expectNoContent();
        await clickWithFakeTime(trigger!);
        expect(onOpenChange).toBeCalledWith(true);
        expectContent();

        await clickWithFakeTime(document.body);
        expect(onOpenChange).toBeCalledWith(false);

        expectNoContent();
      });

      test("callback *not* called when contents clicked", async () => {
        const trigger = expectTrigger();
        expectNoContent();
        await clickWithFakeTime(trigger!);
        expect(onOpenChange).toBeCalledWith(true);
        expect(onOpenChange).toBeCalledTimes(1);

        const content = expectContent();
        await clickWithFakeTime(content!);
        expect(onOpenChange).toBeCalledTimes(1);

        expectContent();
        expectTrigger();
      });

      test("callback called with true when trigger clicked while closed", async () => {
        const trigger = expectTrigger();
        expectNoContent();

        await clickWithFakeTime(trigger!);
        expect(onOpenChange).toBeCalledWith(true);
        expectContent();
      });

      test("callback called with false when trigger clicked while open", async () => {
        const trigger = expectTrigger();
        expectNoContent();
        await clickWithFakeTime(trigger!);
        expect(onOpenChange).toBeCalledWith(true);
        expectContent();

        await clickWithFakeTime(trigger!);
        expect(onOpenChange).toBeCalledWith(false);
        expectNoContent();
      });
    });

    describe("hover behavior", () => {
      const onOpenChange = vi.fn();

      beforeEach(() => {
        component = render(TestTooltip, {
          testHasTrigger: true,
          testHasContent: true,
          rootProps: {
            onOpenChange,
            delayDurationMs: 160, // default hover delay
          },
          contentProps: {},
        });
      });

      test("content shows after hovering trigger for delay duration", async () => {
        const trigger = expectTrigger();
        expectNoContent();

        await hoverWithFakeTime(trigger!);
        expectNoContent();
        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
        expectContent();
        expect(onOpenChange).toBeCalledWith(true);
      });

      test("content does not show if hover duration is too short", async () => {
        const trigger = expectTrigger();
        expectNoContent();

        // Simulate brief hover
        await hoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(HOVER_THRASH_DURATION_MS); // Less than delay duration
        await unhoverWithFakeTime(trigger!);

        // Content should not show
        expectNoContent();
        expect(onOpenChange).not.toBeCalled();
      });

      test("content hides when mouse leaves", async () => {
        const trigger = expectTrigger();

        // Show content via hover
        await hoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
        expectContent();
        expect(onOpenChange).toBeCalledWith(true);

        // Move mouse away
        await unhoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(HOVER_THRASH_DURATION_MS);

        expect(onOpenChange).toBeCalledWith(false);
        expectNoContent();
      });

      test("content stays visible when moving from trigger to content", async () => {
        const trigger = expectTrigger();

        // Show content via hover
        await hoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
        const content = expectContent();

        // Move to content
        await unhoverWithFakeTime(trigger!);
        await hoverWithFakeTime(content!);

        // Content should remain visible
        expect(onOpenChange).toBeCalledTimes(1); // Only called once for initial show
        expectContent();
      });

      test("content hides after long delay between trigger and content hover", async () => {
        const trigger = expectTrigger();

        // Show content via hover
        await hoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
        const content = expectContent();

        // Long delay before moving to content
        await unhoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(LONGER_HOVER_DURATION_MS);

        // Assert content is not visible
        expect(content!).not.toBeVisible();
        expect(onOpenChange).toBeCalledWith(false);
        expectNoContent();
      });

      test("rapid hover/unhover does not cause content to show", async () => {
        const trigger = expectTrigger();

        // Rapid hover/unhover sequence
        for (let i = 0; i < 3; i++) {
          await hoverWithFakeTime(trigger!);
          await vi.advanceTimersByTimeAsync(50);
          await unhoverWithFakeTime(trigger!);
          await vi.advanceTimersByTimeAsync(50);
        }

        // Content should not show
        expect(onOpenChange).not.toBeCalled();
        expectNoContent();
      });

      test("content stays visible during rapid mouse movements within content", async () => {
        const trigger = expectTrigger();

        // Initial hover to show content
        await hoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
        const content = expectContent();
        expect(onOpenChange).toBeCalledWith(true);

        // Simulate rapid mouse movements within content
        for (let i = 0; i < 3; i++) {
          await unhoverWithFakeTime(content!);
          await vi.advanceTimersByTimeAsync(30);
          await hoverWithFakeTime(content!);
          await vi.advanceTimersByTimeAsync(30);
        }

        // Content should remain visible
        expect(onOpenChange).toBeCalledTimes(1); // Should not trigger additional opens/closes
        expectContent();
      });

      test("hover behavior respects custom delay duration", async () => {
        component.unmount();

        // Render with custom delay
        component = render(TestTooltip, {
          testHasTrigger: true,
          testHasContent: true,
          rootProps: {
            onOpenChange,
            delayDurationMs: 300, // Longer delay
          },
          contentProps: {},
        });

        const trigger = expectTrigger();

        // Hover for default delay duration
        await hoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);

        // Content should not show yet
        expectNoContent();

        // Wait remaining time
        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);

        // Now content should show
        expect(onOpenChange).toBeCalledWith(true);
        expectContent();
      });

      test("content closes when clicking outside while hovering content", async () => {
        const trigger = expectTrigger();

        // Show content via hover
        await hoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
        expectContent();

        // Click outside while still hovering content
        await clickWithFakeTime(document.body);

        // Content should hide
        expect(onOpenChange).toBeCalledWith(false);
        expectNoContent();
      });

      test("content closes when pressing escape while hovering content", async () => {
        const trigger = expectTrigger();

        // Show content via hover
        await hoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
        expectContent();

        // Press escape while still hovering content
        await keyboardWithFakeTime("{Escape}");

        // Content should hide
        expect(onOpenChange).toBeCalledWith(false);
        expectNoContent();
      });

      test("content closes when pressing escale while focused on trigger", async () => {
        const trigger = expectTrigger();

        // Focus trigger
        trigger?.focus();

        // Show content via hover
        await hoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
        expectContent();

        // Press escape while still hovering content
        await keyboardWithFakeTime("{Escape}");

        // Content should hide
        expect(onOpenChange).toBeCalledWith(false);
        expectNoContent();
      });
    });

    describe("only click, no hover", () => {
      const onOpenChange = vi.fn();
      const onHoverStart = vi.fn();
      const onHoverEnd = vi.fn();
      const onClickOutside = vi.fn();

      beforeEach(async () => {
        component = render(TestTooltip, {
          testHasTrigger: true,
          testHasContent: true,
          rootProps: {
            onOpenChange,
            onHoverStart,
            onHoverEnd,
            triggerOn: [TooltipTriggerOn.Click],
          },
          contentProps: {
            onClickOutside,
          },
        });
      });

      test("content shows when trigger clicked", async () => {
        const trigger = expectTrigger();
        expectNoContent();

        await clickWithFakeTime(trigger!);
        expect(onOpenChange).toBeCalledWith(true);
        expectContent();
      });

      test("content does not show and hover callbacks not called when trigger hovered", async () => {
        const trigger = expectTrigger();
        expectNoContent();

        await hoverWithFakeTime(trigger!);
        expectNoContent();

        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
        expectNoContent();

        // Hover callbacks should not be called
        expect(onHoverStart).not.toBeCalled();
        expect(onHoverEnd).not.toBeCalled();
      });

      test("content still closes and callbacks called when clicking outside", async () => {
        const trigger = expectTrigger();
        expectNoContent();

        await clickWithFakeTime(trigger!);
        expect(onOpenChange).toBeCalledWith(true);
        expectContent();

        await clickWithFakeTime(document.body);
        expect(onOpenChange).toBeCalledWith(false);
        expectNoContent();

        // Click outside callback should be called
        expect(onClickOutside).toBeCalled();
      });
    });

    describe("only hover, no click", () => {
      const onOpenChange = vi.fn();
      const onHoverStart = vi.fn();
      const onHoverEnd = vi.fn();
      const onClickOutside = vi.fn();

      beforeEach(async () => {
        component = render(TestTooltip, {
          testHasTrigger: true,
          testHasContent: true,
          rootProps: {
            onOpenChange,
            onHoverStart,
            onHoverEnd,
            triggerOn: [TooltipTriggerOn.Hover],
          },
          contentProps: {
            onClickOutside,
          },
        });
      });

      test("content shows and callbacks called when trigger hovered", async () => {
        const trigger = expectTrigger();
        expectNoContent();

        // Start hover
        await hoverWithFakeTime(trigger!);
        expectNoContent();

        // Wait hover duration
        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
        expect(onOpenChange).toBeCalledWith(true);
        expectContent();

        // End hover
        await unhoverWithFakeTime(trigger!);
        await vi.advanceTimersByTimeAsync(HOVER_THRASH_DURATION_MS);

        // Hover callbacks should be called
        expect(onHoverStart).toBeCalled();
        expect(onHoverEnd).toBeCalled();
      });

      test("content does not show when trigger clicked", async () => {
        expect.hasAssertions();
        const trigger = expectTrigger();
        expectNoContent();

        await clickWithFakeTime(trigger!);
        expectNoContent();
      });

      test("content still closes and callbacks called when clicking outside", async () => {
        const trigger = expectTrigger();
        expectNoContent();

        // Start hover
        await hoverWithFakeTime(trigger!);
        expectNoContent();

        // Wait hover duration
        await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
        expect(onOpenChange).toBeCalledWith(true);
        expectContent();

        // Click outside
        await clickWithFakeTime(document.body);
        expect(onOpenChange).toBeCalledWith(false);
        expectNoContent();

        // Click outside callback should be called
        expect(onClickOutside).toBeCalled();
      });
    });
  });

  describe("Handles targeted close requests", () => {
    const onRequestClose = vi.fn();
    beforeEach(() => {
      component = render(TestTooltip, {
        testHasTrigger: true,
        testHasContent: true,
        rootProps: {},
        contentProps: {
          onRequestClose,
        },
      });
    });

    test("clicking content does not close tooltip", async () => {
      const trigger = expectTrigger();
      expectNoContent();

      await clickWithFakeTime(trigger!);
      expect(onRequestClose).not.toBeCalled();
      expectContent();
    });

    test("clicking outside closes tooltip, but doesn't call onRequestClose", async () => {
      const trigger = expectTrigger();
      expectNoContent();

      await clickWithFakeTime(trigger!);
      expect(onRequestClose).not.toBeCalled();
      expectContent();

      await clickWithFakeTime(document.body);
      expect(onRequestClose).not.toBeCalled();
      expectNoContent();
    });

    test("pressing escape closes tooltip", async () => {
      const trigger = expectTrigger();
      expectNoContent();

      await clickWithFakeTime(trigger!);
      expect(onRequestClose).not.toBeCalled();
      expectContent();

      await keyboardWithFakeTime("{Escape}");
      expect(onRequestClose).not.toBeCalled();
      expectNoContent();
    });

    test("dispatching a CloseTooltipRequestEvent closes tooltip", async () => {
      const trigger = expectTrigger();
      expectNoContent();

      await clickWithFakeTime(trigger!);
      expect(onRequestClose).not.toBeCalled();
      const content = expectContent();

      content?.dispatchEvent(new CloseTooltipRequestEvent());
      await vi.advanceTimersByTimeAsync(INTERACTION_DURATION_MS);
      expect(onRequestClose).toBeCalled();
      expectNoContent();
    });

    test("dispatching a CloseTooltipRequestEvent from outside content doesn't close tooltip", async () => {
      const trigger = expectTrigger();
      expectNoContent();

      await clickWithFakeTime(trigger!);
      expect(onRequestClose).not.toBeCalled();

      document.body.dispatchEvent(new CloseTooltipRequestEvent());
      await vi.advanceTimersByTimeAsync(INTERACTION_DURATION_MS);
      expect(onRequestClose).not.toBeCalled();
      expectContent();
    });
  });
});

async function clickWithFakeTime(element: Element): Promise<void> {
  const clickPromise = userEvent.click(element);
  await vi.advanceTimersByTimeAsync(INTERACTION_DURATION_MS);
  await clickPromise;
}

async function hoverWithFakeTime(element: Element): Promise<void> {
  const hoverPromise = userEvent.hover(element);
  await vi.advanceTimersByTimeAsync(INTERACTION_DURATION_MS);
  await hoverPromise;
}

async function unhoverWithFakeTime(element: Element): Promise<void> {
  const unhoverPromise = userEvent.unhover(element);
  await vi.advanceTimersByTimeAsync(INTERACTION_DURATION_MS);
  await unhoverPromise;
}

async function keyboardWithFakeTime(key: string): Promise<void> {
  const keyboardPromise = userEvent.keyboard(key);
  await vi.advanceTimersByTimeAsync(INTERACTION_DURATION_MS);
  await keyboardPromise;
}

<script lang="ts">
  import type {
    ButtonColor,
    ButtonSize,
    <PERSON><PERSON>V<PERSON><PERSON>,
    ButtonRadius,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import ChildIconButton from "$common-webviews/src/design-system/_primitives/ChildIconButton.svelte";

  export let size: ButtonSize = 2;
  export let variant: ButtonVariant = "solid";
  export let color: ButtonColor = "accent";
  export let highContrast: boolean = false;
  export let disabled: boolean = false;
  export let radius: ButtonRadius = "medium";
</script>

<ChildIconButton
  {size}
  {variant}
  {color}
  {highContrast}
  {disabled}
  {radius}
  on:click
  on:keyup
  on:keydown
  on:mousedown
  on:mouseover
  on:focus
  on:mouseleave
  on:blur
  on:contextmenu
  {...$$restProps}
>
  <slot />
</ChildIconButton>

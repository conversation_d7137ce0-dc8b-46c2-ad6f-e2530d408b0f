<script lang="ts" generics="T">
  import { onMount, onDestroy, tick } from "svelte";
  import Sortable, { type SortableEvent } from "sortablejs";
  import { SortableJsClasses, type DraggableListOptions } from "./types";

  // Component props
  let className = "";
  export { className as class };
  export let id: string;
  export let items: T[] = [];
  export let options: DraggableListOptions = {};
  export let disabled = false;
  export let useHandle = true;

  // Event handlers
  export let onEnd: (event: SortableEvent) => void = () => {};

  // Internal state
  let listElement: HTMLElement;
  let sortableInstance: Sortable | null = null;

  // Track previous state for comparison
  let prevOptions = { ...options };
  let prevDisabled = disabled;

  // Helper function to clean up Sortable instance
  function cleanupSortableInstance() {
    try {
      if (sortableInstance && typeof sortableInstance.destroy === "function") {
        sortableInstance.destroy();
      }
    } catch (error) {
      console.error("Error destroying Sortable instance:", error);
    } finally {
      sortableInstance = null;
    }
  }

  // Initialize or update the Sortable instance
  async function initSortable() {
    await tick();
    cleanupSortableInstance();
    if (!listElement) return;

    try {
      sortableInstance = new Sortable(listElement, {
        // Default options
        animation: 150,
        fallbackOnBody: true,
        swapThreshold: 0.65,
        ghostClass: SortableJsClasses.ghost,
        chosenClass: SortableJsClasses.chosen,
        dragClass: SortableJsClasses.drag,
        disabled,
        group: options.group || "nested",
        dataIdAttr: "data-item-id",
        // Set handle to the drag handle if we want to use a handle
        handle: useHandle ? ".c-draggable-list-item__handle" : undefined,
        ...options,
        onEnd,
      });
    } catch (error) {
      console.error("Error initializing Sortable:", error);
      sortableInstance = null;
    }
  }

  // Lifecycle and reactivity
  onMount(initSortable);

  // Update disabled state when it changes
  $: if (sortableInstance && listElement && disabled !== prevDisabled) {
    try {
      if (typeof sortableInstance.option === "function") {
        sortableInstance.option("disabled", disabled);
        prevDisabled = disabled;
      }
    } catch (error) {
      console.error("Error updating Sortable disabled state:", error);
    }
  }

  // Re-initialize when options object changes
  $: if (
    sortableInstance &&
    listElement &&
    JSON.stringify(options) !== JSON.stringify(prevOptions)
  ) {
    try {
      prevOptions = { ...options };
      initSortable();
    } catch (error) {
      console.error("Error updating Sortable options:", error);
    }
  }

  // Clean up when component is destroyed
  onDestroy(() => {
    cleanupSortableInstance();
    listElement = null as unknown as HTMLElement;
    prevOptions = {};
  });
</script>

<div
  class="c-draggable-list {className}"
  bind:this={listElement}
  {id}
  data-list-id={id}
  data-testid="draggable-list"
>
  <slot name="items" {items} {onEnd} />
</div>

<style>
  .c-draggable-list {
    position: relative;
    /* Default to vertical list */
    display: flex;
    flex-direction: column;
    gap: calc(0.5 * var(--ds-spacing-1));

    & .c-draggable-list-item__sortable-ghost {
      opacity: 0.4;
      background-color: var(--ds-color-accent-3);
      box-shadow: var(--ds-shadow-1);
    }

    & .c-draggable-list-item__sortable-chosen {
      background-color: var(--ds-color-accent-2);
      box-shadow: var(--ds-shadow-1);
    }

    & .c-draggable-list-item__sortable-drag {
      opacity: 0.9;
      cursor: grabbing;
      box-shadow: var(--ds-shadow-1);
      transform: scale(1.01);
    }
  }
</style>

<script lang="ts" generics="T">
  import IconButtonAugment from "../IconButtonAugment.svelte";
  import ChevronDown from "../../icons/chevron-down.svelte";
  import ChevronRight from "../../icons/chevron-right.svelte";

  // Component props
  let className = "";
  export { className as class };
  export let id: string;
  export let item: T;
  export let disabled = false;
  export let hasNestedItems = false;
  export let expanded = true;
  export let selected = false;
  export let showConnectors = true;

  export let element: HTMLElement | undefined = undefined;

  // Toggle expanded state for nested items
  function toggleExpanded() {
    expanded = !expanded;
  }
</script>

<div
  class="c-draggable-list-item {className}"
  class:is-disabled={disabled}
  class:has-nested-items={hasNestedItems}
  class:is-expanded={expanded}
  class:is-selected={selected}
  bind:this={element}
  {id}
  data-item-id={id}
  data-testid="draggable-list-item"
  tabindex="0"
  role="button"
  on:mousedown
  on:click
  on:keydown
  on:keyup
  on:keypress
  on:focus
  on:blur
  on:focusin
  on:focusout
>
  <div class="c-draggable-list-item__content">
    <!-- Drag handle slot -->
    <div class="c-draggable-list-item__handle">
      <slot name="handle" {item} />
    </div>

    <!-- Expand/collapse button for nested items -->
    {#if hasNestedItems}
      <IconButtonAugment
        class="c-draggable-list-item__expand-collapse-button"
        size={1}
        variant="ghost"
        color="neutral"
        on:click={toggleExpanded}
        aria-expanded={expanded}
        aria-label={expanded ? "Collapse" : "Expand"}
      >
        {#if expanded}
          <ChevronDown />
        {:else}
          <ChevronRight />
        {/if}
      </IconButtonAugment>
    {/if}

    <!-- Main content slot -->
    <div class="c-draggable-list-item__main">
      <slot name="header-contents" {item} />
    </div>

    <!-- Actions slot -->
    <div class="c-draggable-list-item__actions">
      <slot name="actions" {item} />
    </div>
  </div>

  <div
    class="c-draggable-list-item__contents"
    class:c-draggable-list-item__show-connectors={showConnectors}
  >
    <slot name="contents" {item} />
  </div>
</div>

<style>
  .c-draggable-list-item {
    position: relative;
    display: flex;
    flex-direction: column;
    border-radius: var(--ds-radius-2);
    transition:
      background-color 0.15s ease,
      border-color 0.15s ease,
      box-shadow 0.15s ease;
    outline: none;

    &.is-selected,
    &.is-selected:hover,
    &.is-selected:focus {
      outline: none;
      background-color: var(--ds-color-neutral-3);
      box-shadow: 0 0 0 1px var(--ds-color-neutral-5);
    }

    & .c-draggable-list-item__contents.c-draggable-list-item__show-connectors {
      position: relative;
      padding-left: var(--ds-spacing-3);

      &::before {
        content: "";
        position: absolute;
        top: calc(-1 * var(--ds-spacing-1));
        bottom: calc(var(--ds-spacing-2));
        left: calc(var(--ds-spacing-2));
        width: 1px;
        background-color: var(--ds-color-neutral-5);
        z-index: 0;
      }

      & .c-draggable-list-item::before {
        content: "";
        position: absolute;
        top: calc(var(--ds-spacing-3));
        left: calc(-1 * var(--ds-spacing-1));
        width: var(--ds-spacing-1);
        height: 1px;
        background-color: var(--ds-color-neutral-5);
        z-index: 0;
      }
    }
  }

  .c-draggable-list-item:hover {
    &:not(:has(.c-draggable-list-item:hover)) {
      background-color: var(--ds-color-neutral-3);
    }
  }

  /* Add subtle highlight effect on hover */
  .c-draggable-list-item:hover {
    box-shadow: var(--ds-shadow-1);
  }

  .c-draggable-list-item.is-disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  .c-draggable-list-item__content {
    display: flex;
    align-items: center;
    margin: calc(0.5 * var(--ds-spacing-1));
  }

  .c-draggable-list-item__handle {
    cursor: grab;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.4;
    transition: opacity 0.2s ease;
    width: var(--ds-spacing-3);
  }

  /* Show handle more prominently on hover */
  .c-draggable-list-item:hover .c-draggable-list-item__handle {
    opacity: 0.6;
  }

  /* Active state when dragging */
  .c-draggable-list-item__handle:active {
    cursor: grabbing;
    opacity: 1;
  }

  .c-draggable-list-item__main {
    flex: 1;
    min-width: 0; /* Ensure text truncation works */
    display: flex;
    align-items: center;
  }
</style>

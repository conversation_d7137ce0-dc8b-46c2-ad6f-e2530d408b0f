import type {
  ButtonSize,
  <PERSON><PERSON><PERSON><PERSON>t,
  <PERSON>tonColor,
  ButtonRadius,
} from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";

export interface SplitButtonProps {
  size?: ButtonSize;
  variant?: ButtonVariant;
  color?: ButtonColor;
  radius?: ButtonRadius;
  disabled?: boolean;
  highContrast?: boolean;
  showDropdown?: boolean;
}

export interface DropdownProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  side?: "top" | "bottom" | "left" | "right";
  align?: "start" | "center" | "end";
  onClickOutside?: () => void;
  onEscapeKeyDown?: () => void;
}

<script lang="ts" context="module">
  export type TextSize = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;
  export type TextWeight = "light" | "regular" | "medium" | "bold";
  export type TextType = "monospace" | "default";
  export type TextColor = "accent" | "neutral" | "success" | "error" | "primary" | "secondary";
</script>

<script lang="ts">
  import { dsColorAttribute } from "../_libs/component-utils";

  export let size: TextSize = 3;
  export let weight: TextWeight = "regular";
  export let type: TextType = "default";
  export let color: TextColor | undefined = undefined;
  $: ({ class: className, ...restProps } = $$restProps);
</script>

<span
  {...color ? dsColorAttribute(color) : {}}
  class="c-text c-text--size-{size} c-text--weight-{weight} c-text--type-{type} c-text--color-{color} {className}"
  class:c-text--has-color={color !== undefined}
  {...restProps}><slot /></span
>

<style>
  .c-text {
    /* We  don't want the span itself to impact the layout of the slot */
    display: contents;
  }

  .c-text--has-color {
    --text-color: var(--ds-color-a12);

    color: var(--text-color);
  }

  /* Secondary colors for light & cark */
  .c-text--color-secondary {
    --text-color: var(--ds-color-a9);
  }

  :global(.dark) .c-text.c-text--color-secondary {
    --text-color: var(--ds-color-a8);
  }

  .c-text--type-monospace {
    font-family: var(--augment-monospace-font-family), monospace;
  }

  .c-text--size-1 {
    /* 12px */
    font-size: 0.75rem;
    /* 16px */
    line-height: 1rem;
    letter-spacing: 0.0025em;
  }
  .c-text--size-2 {
    /* 14px */
    font-size: 0.875rem;
    /* 20px */
    line-height: 1rem;
    letter-spacing: 0em;
  }
  .c-text--size-3 {
    /* 16px */
    font-size: 1rem;
    /* 24px */
    line-height: 1.5rem;
    letter-spacing: 0em;
  }
  .c-text--size-4 {
    /* 18px */
    font-size: 1.125rem;
    /* 26px */
    line-height: 1.625rem;
    letter-spacing: -0.0025em;
  }
  .c-text--size-5 {
    /* 20px */
    font-size: 1.25rem;
    /* 28px */
    line-height: 1.75rem;
    letter-spacing: -0.005em;
  }
  .c-text--size-6 {
    /* 24px */
    font-size: 1.5rem;
    /* 30px */
    line-height: 1.875rem;
    letter-spacing: -0.00625em;
  }
  .c-text--size-7 {
    /* 28px */
    font-size: 1.75rem;
    /* 36px */
    line-height: 2.25rem;
    letter-spacing: -0.0075em;
  }
  .c-text--size-8 {
    /* 35px */
    font-size: 2.1875rem;
    /* 40px */
    line-height: 2.25rem;
    letter-spacing: -0.01em;
  }
  .c-text--size-9 {
    /* 60px */
    font-size: 3.75rem;
    /* 60px */
    line-height: 3.75rem;
    letter-spacing: -0.025em;
  }
  .c-text--weight-light {
    font-weight: 300;
  }
  .c-text--weight-regular {
    font-weight: 400;
  }
  .c-text--weight-medium {
    font-weight: 500;
  }
  .c-text--weight-bold {
    font-weight: 700;
  }
</style>

<script lang="ts">
  import TextAugment from "./TextAugment.svelte";
  import TextTooltipAugment from "./TextTooltipAugment.svelte";
  import {
    formatTimestampForChatMessage,
    formatFullTimestamp,
  } from "$common-webviews/src/common/utils/time-utils";

  export let timestamp: string | Date | undefined = undefined;
  export let showTooltip: boolean = true;
  export let tooltipSide: "top" | "right" | "bottom" | "left" = "right";
  export let textColor: "primary" | "secondary" = "secondary";
  export let textSize: 1 | 2 | 3 = 1;

  let className: string = "";
  export { className as class };
</script>

{#if timestamp}
  {#if showTooltip}
    <TextTooltipAugment content={formatFullTimestamp(timestamp)} side={tooltipSide}>
      <div class="c-timestamp-display {className}">
        <TextAugment color={textColor} size={textSize}>
          {formatTimestampForChatMessage(timestamp)}
        </TextAugment>
      </div>
    </TextTooltipAugment>
  {:else}
    <div class="c-timestamp-display {className}">
      <TextAugment color={textColor} size={textSize}>
        {formatTimestampForChatMessage(timestamp)}
      </TextAugment>
    </div>
  {/if}
{/if}

<style>
  .c-timestamp-display {
    white-space: nowrap;
    padding: var(--ds-spacing-2);
  }
</style>

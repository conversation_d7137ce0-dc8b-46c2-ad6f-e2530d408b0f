import { describe, test, expect, beforeEach, vi, afterEach } from "vitest";
import DropdownMenuFocusContext from "./focus-context";
import { CloseTooltipRequestEvent } from "../../_primitives/TooltipAugment/types";

describe("DropdownMenuFocusContext", () => {
  let context: DropdownMenuFocusContext;
  let mockRootElement: HTMLElement;
  let mockItems: HTMLElement[];
  let nestedContext: DropdownMenuFocusContext;
  let nestedRootElement: HTMLElement;
  let nestedItem: HTMLElement;

  /**
   * DOM structure:
   * <body>
   *   <div>
   *     <button class="l-dropdown-menu__focusable-item" data-test-id="item-0"></button>
   *     <button class="l-dropdown-menu__focusable-item" data-test-id="item-1"></button>
   *     <button class="l-dropdown-menu__focusable-item" data-test-id="item-2">
   *       <!-- Nested context -->
   *       <div>
   *         <button class="l-dropdown-menu__focusable-item" data-test-id="nested-item"></button>
   *       </div>
   *     </button>
   *   </div>
   * </body>
   */
  beforeEach(() => {
    mockRootElement = document.createElement("div");
    nestedRootElement = document.createElement("div");
    mockItems = [
      document.createElement("button"),
      document.createElement("button"),
      document.createElement("button"),
    ];
    mockItems.forEach((item, index) => {
      item.classList.add(DropdownMenuFocusContext.ITEM_CLASS);
      item.setAttribute("data-test-id", `item-${index}`);
      mockRootElement.appendChild(item);
    });

    nestedItem = document.createElement("button");
    nestedItem.classList.add(DropdownMenuFocusContext.ITEM_CLASS);
    nestedRootElement.appendChild(nestedItem);
    mockItems[2].appendChild(nestedRootElement);
    document.body.appendChild(mockRootElement);

    context = new DropdownMenuFocusContext();
    context.registerRoot(mockRootElement);
    nestedContext = new DropdownMenuFocusContext(context);
    nestedContext.registerRoot(nestedRootElement);
  });

  afterEach(() => {
    mockRootElement.remove();
  });

  test("registerRoot sets the root element", () => {
    expect(context.rootElement).toBe(mockRootElement);
  });

  test("focusIdx focuses the correct item", () => {
    const focusSpy = vi.spyOn(mockItems[1], "focus");
    context.focusIdx(1);
    expect(focusSpy).toHaveBeenCalled();
  });

  test("focusIdx handles index wrapping", () => {
    const focusSpy = vi.spyOn(mockItems[1], "focus");
    context.focusIdx(5); // 6 % 4 = 2 => index 1
    expect(focusSpy).toHaveBeenCalled();
  });

  test("focusNext focuses the next item", () => {
    mockItems[0].focus();
    const focusSpy = vi.spyOn(mockItems[1], "focus");
    context.focusNext();
    expect(focusSpy).toHaveBeenCalled();
  });

  test("focusPrev focuses the previous item", () => {
    mockItems[1].focus();
    const focusSpy = vi.spyOn(mockItems[0], "focus");
    context.focusPrev();
    expect(focusSpy).toHaveBeenCalled();
  });

  test("clickFocusedItem clicks the focused item", async () => {
    const clickSpy = vi.spyOn(mockItems[1], "click");
    mockItems[1].focus();
    await context.clickFocusedItem();
    expect(clickSpy).toHaveBeenCalled();
  });

  test("popNestedFocus returns false when there is no parent context", () => {
    expect(context.popNestedFocus()).toBe(false);
  });

  test("popNestedFocus returns true and focuses parent when there is a parent context", () => {
    // Focus the nested item
    nestedItem.focus();

    // Pop focus back to the parent context
    expect(document.activeElement).toBe(nestedItem);
    expect(nestedContext.popNestedFocus()).toBe(true);

    // Expect the first item in the parent context to be focused, since there is no focus anchor
    expect(document.activeElement).toBe(mockItems[2]);
  });

  test("ArrowDown key focuses the next item", () => {
    mockItems[0].focus();
    const event = new KeyboardEvent("keydown", { key: "ArrowDown" });
    context.rootElement?.dispatchEvent(event);
    expect(document.activeElement).toBe(mockItems[1]);
  });

  test("ArrowUp key focuses the previous item", () => {
    mockItems[1].focus();
    const event = new KeyboardEvent("keydown", { key: "ArrowUp" });
    context.rootElement?.dispatchEvent(event);
    expect(document.activeElement).toBe(mockItems[0]);
  });

  test("ArrowRight key clicks the focused item", () => {
    const clickSpy = vi.spyOn(mockItems[1], "click");
    mockItems[1].focus();
    const event = new KeyboardEvent("keydown", { key: "ArrowRight" });
    context.rootElement?.dispatchEvent(event);
    expect(clickSpy).toHaveBeenCalled();
  });

  test("ArrowLeft key sends close request", () => {
    nestedItem.focus();
    const event = new KeyboardEvent("keydown", { key: "ArrowLeft" });

    const closeRequestReceived = vi.fn();
    document.addEventListener(
      CloseTooltipRequestEvent.eventType,
      closeRequestReceived,
    );
    nestedContext.rootElement?.dispatchEvent(event);

    document.removeEventListener(
      CloseTooltipRequestEvent.eventType,
      closeRequestReceived,
    );
    expect(closeRequestReceived).toHaveBeenCalled();
  });

  test("focusedIndex store updates when focus changes", () => {
    let currentFocusedIndex: number | undefined = undefined;
    const unsubscribe = context.focusedIndex.subscribe((value) => {
      currentFocusedIndex = value;
    });

    // Initially undefined
    expect(currentFocusedIndex).toBeUndefined();

    // Focus first item
    context.focusIdx(0);
    expect(currentFocusedIndex).toBe(0);

    // Focus second item
    context.focusIdx(1);
    expect(currentFocusedIndex).toBe(1);

    // Focus next (third item)
    context.focusNext();
    expect(currentFocusedIndex).toBe(2);

    // Focus previous (back to second item)
    context.focusPrev();
    expect(currentFocusedIndex).toBe(1);

    // Clean up subscription
    unsubscribe();
  });

  test("focusedIndex store updates to undefined when focus leaves dropdown", () => {
    let currentFocusedIndex: number | undefined = undefined;
    const unsubscribe = context.focusedIndex.subscribe((value) => {
      currentFocusedIndex = value;
    });

    // Focus an item
    context.focusIdx(1);
    expect(currentFocusedIndex).toBe(1);

    // Simulate focus leaving the dropdown
    document.body.focus();
    const focusOutEvent = new FocusEvent("focusout", {
      bubbles: true,
      relatedTarget: document.body,
    });
    mockRootElement.dispatchEvent(focusOutEvent);

    // The focusedIndex should be undefined
    expect(currentFocusedIndex).toBeUndefined();

    // Clean up subscription
    unsubscribe();
  });

  test("focusedIndex store updates when getCurrentFocusedIdx is called", () => {
    let currentFocusedIndex: number | undefined = undefined;
    const unsubscribe = context.focusedIndex.subscribe((value) => {
      currentFocusedIndex = value;
    });

    // Initially undefined
    expect(currentFocusedIndex).toBeUndefined();

    // Focus an item directly (without using context methods)
    mockItems[2].focus();

    // Call getCurrentFocusedIdx to update the store
    const idx = context.getCurrentFocusedIdx();

    // Both the return value and the store should be updated
    expect(idx).toBe(2);
    expect(currentFocusedIndex).toBe(2);

    // Clean up subscription
    unsubscribe();
  });

  test("setFocusedIdx updates the store without focusing the element", () => {
    let currentFocusedIndex: number | undefined = undefined;
    const unsubscribe = context.focusedIndex.subscribe((value) => {
      currentFocusedIndex = value;
    });

    // Initially undefined
    expect(currentFocusedIndex).toBeUndefined();

    // Focus mockItems[0] first
    mockItems[0].focus();
    expect(document.activeElement).toBe(mockItems[0]);

    // Set focused index to 2 without actually focusing it
    context.setFocusedIdx(2);

    // The store should be updated
    expect(currentFocusedIndex).toBe(2);

    // But the active element should still be mockItems[0]
    expect(document.activeElement).toBe(mockItems[0]);

    // Clean up subscription
    unsubscribe();
  });
});

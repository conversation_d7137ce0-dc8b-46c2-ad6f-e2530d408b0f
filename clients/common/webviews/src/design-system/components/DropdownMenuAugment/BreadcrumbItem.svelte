<script lang="ts">
  import ChevronRight from "../../icons/chevron-right.svelte";
  import Item from "./Item.svelte";
</script>

<Item class="c-dropdown-menu-augment__breadcrumb-chevron" {...$$restProps}>
  <slot />
  <ChevronRight slot="iconRight" />
</Item>

<style>
  /* Remove a bit of spacing around the chevron for the breadcrumb list items */
  :global(.c-dropdown-menu-augment__item.c-dropdown-menu-augment__breadcrumb-chevron svg) {
    margin: 0 calc(-1 * var(--ds-spacing-1)) 0 0;
  }
</style>

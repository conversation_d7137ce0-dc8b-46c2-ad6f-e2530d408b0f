<script lang="ts">
  import DropdownMenu from ".";
  import ButtonAugment from "../ButtonAugment.svelte";

  export let uuid: string = "DEFAULT-UUID";
  export let open: boolean | undefined = undefined;
  export let requestOpen: () => void = () => {};
  export let requestClose: () => void = () => {};
</script>

<div data-test-id={uuid}>
  <DropdownMenu.Root bind:requestOpen bind:requestClose {open}>
    <DropdownMenu.Trigger>
      <ButtonAugment>Trigger</ButtonAugment>
    </DropdownMenu.Trigger>
    <DropdownMenu.Content>
      <DropdownMenu.Item>Item 1</DropdownMenu.Item>
      <DropdownMenu.Item>Item 2, which is longer</DropdownMenu.Item>
      <DropdownMenu.Item>Item 3, which is even longer</DropdownMenu.Item>
      <DropdownMenu.Separator />
      <DropdownMenu.Item disabled>Item 4, which is disabled</DropdownMenu.Item>
      <DropdownMenu.Sub>
        <DropdownMenu.SubTrigger>Item 5, which is a submenu</DropdownMenu.SubTrigger>
        <DropdownMenu.SubContent side="right">
          <DropdownMenu.Item>Submenu 1 Item 1</DropdownMenu.Item>
          <DropdownMenu.Item>Submenu 1 Item 2</DropdownMenu.Item>
          <DropdownMenu.Item>Submenu 1 Item 3</DropdownMenu.Item>
          <DropdownMenu.Item>Submenu 1 Item 4</DropdownMenu.Item>
          <DropdownMenu.Item>Submenu 1 Item 5</DropdownMenu.Item>
        </DropdownMenu.SubContent>
      </DropdownMenu.Sub>
      <DropdownMenu.Sub>
        <DropdownMenu.SubTrigger>Item 6, which is also a submenu</DropdownMenu.SubTrigger>
        <DropdownMenu.SubContent side="right">
          <DropdownMenu.Item>Submenu 2 Item 1</DropdownMenu.Item>
          <DropdownMenu.Item>Submenu 2 Item 2</DropdownMenu.Item>
          <DropdownMenu.Item>Submenu 2 Item 3</DropdownMenu.Item>
          <DropdownMenu.Item>Submenu 2 Item 4</DropdownMenu.Item>
          <DropdownMenu.Item>Submenu 2 Item 5</DropdownMenu.Item>
        </DropdownMenu.SubContent>
      </DropdownMenu.Sub>
    </DropdownMenu.Content>
  </DropdownMenu.Root>
</div>

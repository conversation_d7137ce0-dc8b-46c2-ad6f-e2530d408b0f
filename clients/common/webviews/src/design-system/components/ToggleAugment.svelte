<script lang="ts">
  export let checked: boolean = false;
  export let disabled: boolean = false;
  export let size: 1 | 2 | 3 = 2;
  export let ariaLabel: string | undefined = undefined;
  export let onText: string | undefined = undefined;
  export let offText: string | undefined = undefined;

  function handleKeydown(event: KeyboardEvent) {
    if (disabled) return;

    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      checked = !checked;
    }
  }

  // Determine if we should show text
  $: showText = onText || offText;
</script>

<label
  on:change
  class="c-toggle-track c-toggle-track-size--{size}"
  class:checked
  class:disabled
  class:has-text={showText}
>
  {#if showText}
    <span class="c-toggle-text c-toggle-text--off" class:visible={!checked && offText}>
      {offText || ""}
    </span>
    <span class="c-toggle-text c-toggle-text--on" class:visible={checked && onText}>
      {onText || ""}
    </span>
  {/if}
  <input
    type="checkbox"
    class="c-toggle-input"
    class:disabled
    bind:checked
    {disabled}
    aria-label={ariaLabel}
    role="switch"
    on:keydown={handleKeydown}
  />
</label>

<style>
  .c-toggle-track {
    --thumb-translate-x: calc(var(--track-width) - var(--thumb-size) - (2 * var(--track-padding)));

    /* Color variables - using DS variables with fallbacks */
    --off-bg-color: var(--ds-color-neutral-a3);
    --off-border-color: var(--ds-color-neutral-a5);
    --on-bg-color: var(--ds-color-accent-9);
    --on-border-color: var(--ds-color-accent-9);
    --thumb-bg-color: white;
    --focus-ring-color: var(--ds-color-focus-ring, var(--ds-color-accent-8));

    display: inline-flex;
    flex-shrink: 0;
    cursor: pointer;
    align-items: center;
    position: relative;
    border-radius: var(--ds-radius-full, 9999px);
    border: 1px solid var(--off-border-color);
    background-color: var(--off-bg-color);
    transition:
      background-color 0.2s ease-in-out,
      border-color 0.2s ease-in-out;
    padding: 0; /* Reset button padding */
    box-sizing: content-box; /* To make w/h define outer box including padding if any was added */
  }

  /* Apply base dimensions */
  .c-toggle-track {
    height: var(--track-height);
    width: var(--track-width);
  }

  /* Size-specific overrides */
  .c-toggle-track.c-toggle-track-size--1 {
    --track-height: 16px;
    --track-width: 36px;
    --thumb-size: 12px;
    --text-font-size: 8px;
    --track-padding: 2px;
  }
  .c-toggle-track.c-toggle-track-size--2 {
    --track-height: 18px;
    --track-width: 42px;
    --thumb-size: 14px;
    --text-font-size: 10px;
    --track-padding: 2px;
  }

  .c-toggle-track.c-toggle-track-size--3 {
    --track-height: 24px;
    --track-width: 56px;
    --thumb-size: 20px;
    --text-font-size: 12px;
    --track-padding: 2px;
  }

  /* Adjust track width when text is present */
  .c-toggle-track.has-text.c-toggle-track-size--1 {
    --track-width: 42px;
  }
  .c-toggle-track.has-text.c-toggle-track-size--2 {
    --track-width: 54px;
  }
  .c-toggle-track.has-text.c-toggle-track-size--3 {
    --track-width: 60px;
  }

  .c-toggle-track.checked {
    background-color: var(--on-bg-color);
    border-color: var(--on-border-color);
  }

  .c-toggle-track.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .c-toggle-input {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    position: absolute;
    display: block;
    margin: 0;
    padding: 0;
    border: none;
    border-radius: var(--ds-radius-round, 50%);
    background-color: var(--thumb-bg-color);
    box-shadow: var(--ds-shadow-2, 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06));
    transition: transform 0.2s ease-in-out;
    top: var(--track-padding);
    left: var(--track-padding);
    height: var(--thumb-size);
    width: var(--thumb-size);
    cursor: pointer; /* Added for clarity, though label also has it */
  }
  .c-toggle-input.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .c-toggle-input:focus {
    outline: 2px solid var(--focus-ring-color);
  }

  .c-toggle-input:checked {
    transform: translateX(var(--thumb-translate-x));
  }

  /* Text styles */
  .c-toggle-text {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: var(--text-font-size);
    line-height: 1;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    z-index: 1;
    white-space: nowrap;
    user-select: none;
    width: calc(var(--track-width) - var(--thumb-size) - var(--track-padding));
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
  }

  .c-toggle-text.visible {
    opacity: 1;
  }

  .c-toggle-text--off {
    /* When OFF, thumb is on left, so center text in right portion */
    right: var(--track-padding);
    color: var(--ds-color-neutral-11);
  }

  .c-toggle-text--on {
    /* When ON, thumb is on right, so center text in left portion */
    left: var(--track-padding);
    color: white;
  }

  /* Ensure text doesn't interfere with thumb */
  .c-toggle-track.has-text .c-toggle-input {
    z-index: 2;
  }
</style>

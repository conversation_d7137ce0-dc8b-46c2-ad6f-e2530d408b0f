<script lang="ts">
  import RegularBellRingIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/bell.svg?component";
  import RegularBellSlashIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/bell-slash.svg?component";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import { createEventDispatcher } from "svelte";
  import { type ButtonColor } from "../_primitives/BaseButton.svelte";

  /** Whether notifications are enabled */
  export let enabled: boolean = false;
  /** Whether there are unread updates */
  export let isUnread: boolean = false;
  /** Number of unread notifications to display next to bell, default to hardcoded 1 */
  export let unreadCount = 1;
  /** Status color for the button when unread */
  export let statusColor: ButtonColor = "neutral";
  /** Tooltip text to display */
  export let tooltipText: string = "";

  const dispatch = createEventDispatcher<{
    toggle: void;
  }>();

  function handleToggle() {
    dispatch("toggle");
  }
</script>

<div class="c-notify-button">
  <TextTooltipAugment content={tooltipText}>
    <ButtonAugment
      color={isUnread ? statusColor : "neutral"}
      size={1}
      variant={isUnread ? "soft" : "ghost-block"}
      on:click={handleToggle}
      radius="full"
    >
      <div slot="iconLeft">
        {#if enabled}
          <RegularBellRingIcon />
        {:else}
          <RegularBellSlashIcon />
        {/if}
      </div>
      {#if isUnread && unreadCount}
        {unreadCount}
      {/if}
    </ButtonAugment>
  </TextTooltipAugment>
</div>

<style>
  .c-notify-button :global(svg) {
    --notify-button-icon-size: 15px;
    width: var(--notify-button-icon-size);
    height: var(--notify-button-icon-size);
  }
</style>

<script lang="ts">
  import { onD<PERSON>roy } from "svelte";
  import { MonacoContext } from "./context";
  import { themeStore } from "$common-webviews/src/common/hosts/user-themes/theme-store";
  import { getMonacoTheme } from "$common-webviews/src/common/utils/monaco-theme";
  import type * as Monaco from "monaco-editor/esm/vs/editor/editor.api";

  // Default options for the Monaco editor
  const DEFAULT_OPTIONS: Monaco.editor.IStandaloneEditorConstructionOptions = {
    tabSize: 4,
    readOnly: true,
    scrollBeyondLastLine: false,
    minimap: {
      enabled: false,
    },
    scrollbar: {
      alwaysConsumeMouseWheel: false,
      vertical: "hidden",
    },
    wordWrap: "on",
    theme: "vs-dark",
    automaticLayout: true,
    unicodeHighlight: {
      ambiguousCharacters: false,
      invisibleCharacters: false,
    },
  };

  export let options: Record<string, any> = {};
  export let model: Monaco.editor.ITextModel;
  export let decorations: Monaco.editor.IModelDeltaDecoration[] | undefined = undefined;
  export let height: number | undefined = undefined;

  // Store all monaco state. Allow external users to bind to the editor
  export let editorInstance: Monaco.editor.IStandaloneCodeEditor | undefined = undefined;
  let editorContainer: HTMLElement;
  let decoratorCollection: Monaco.editor.IEditorDecorationsCollection | undefined = undefined;

  // Get the Monaco context
  const monacoContext = MonacoContext.getContext();
  const monaco = monacoContext.monaco;

  // Function to update the editor height based on content
  function updateEditorHeight() {
    if (!editorInstance) return;

    if (height !== undefined) {
      editorContainer.style.height = `${height}px`;
      editorInstance.layout();
      return;
    }

    const contentHeight = Math.min(1000, editorInstance.getContentHeight());
    editorContainer.style.height = `${contentHeight}px`;
    editorInstance.layout();
  }

  // Function to setup a new model
  function setupNewModel(
    model: Monaco.editor.ITextModel,
    decorations: Monaco.editor.IModelDeltaDecoration[],
  ) {
    if (!editorInstance) return;
    editorInstance.setModel(model);

    if (decorations.length > 0) {
      decoratorCollection?.clear();
      decoratorCollection = editorInstance.createDecorationsCollection(decorations);
    }
    model.onDidChangeContent(updateEditorHeight);
    updateEditorHeight();
  }

  // Subscribe to Monaco to initialize the editor
  $: {
    if ($monaco && editorContainer && !editorInstance) {
      const opts = { ...DEFAULT_OPTIONS, ...options };
      editorInstance = $monaco.editor.create(editorContainer, opts);

      setupNewModel(model, decorations || []);

      editorInstance.onDidChangeModel(updateEditorHeight);
    }
  }
  // Whenever model changes, we want to try setting it up again
  $: setupNewModel(model, decorations || []);

  // Update theme when it changes
  $: {
    const themeDetails = $themeStore;
    const monacoTheme = getMonacoTheme(themeDetails?.category, themeDetails?.intensity);
    editorInstance?.updateOptions({ theme: monacoTheme });
    editorInstance?.layout(undefined);
  }

  // Clean up on component destroy
  onDestroy(() => {
    editorInstance?.dispose();
  });
</script>

<svelte:window on:focus={() => updateEditorHeight()} />
<div class="c-codeblock">
  <div class="c-codeblock__monaco" bind:this={editorContainer} />
</div>

<style>
  .c-codeblock {
    position: relative;
    height: 100%;
    width: 100%;
  }

  .c-codeblock__monaco {
    --border-width: 1px;
    max-height: calc(100% - (2 * var(--border-width)));
    width: calc(100% - (2 * var(--border-width)));
    border: var(--augment-border, 1px solid var(--ds-color-neutral-a6));
    border-radius: var(--augment-border-radius, var(--ds-radius-2));
    overflow: hidden;
    box-sizing: content-box;
  }

  /**
   * Monaco editor adds a `focused` class to the editor container when it is focused.
   */
  .c-codeblock__monaco:has(.monaco-editor.focused) {
    border: 1px solid var(--augment-focus-border-color, var(--ds-color-accent-a8));
  }

  :global(.c-codeblock:has(.monaco-editor.focused) ~ .c-codeblocks__bottom-action-bar) {
    border-color: var(--augment-focus-border-color, var(--ds-color-accent-a8));
  }

  :global(.c-codeblock__monaco .monaco-editor .unexpected-closing-bracket) {
    color: inherit;
  }
</style>

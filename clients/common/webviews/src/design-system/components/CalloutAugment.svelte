<script lang="ts" context="module">
  export type CalloutColor =
    | "accent"
    | "neutral"
    | "error"
    | "success"
    | "warning"
    | "info"
    | "premium";
  export type CalloutVariant = "soft" | "surface" | "outline";
  export type CalloutSize = 1 | 2 | 3;
</script>

<script lang="ts">
  import TextAugment, {
    type TextSize,
  } from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { dsColorAttribute } from "$common-webviews/src/design-system/_libs/component-utils";

  export let color: CalloutColor = "info";
  export let variant: CalloutVariant = "soft";
  export let size: CalloutSize = 2;
  export let highContrast: boolean = false;

  const textSize: TextSize = size;

  $: ({ class: className, ...restProps } = $$restProps);
</script>

<div
  {...dsColorAttribute(color)}
  class={`c-callout c-callout--${color} c-callout--${variant} c-callout--size-${size} ${className}`}
  class:c-callout--highContrast={highContrast}
  {...restProps}
>
  <TextAugment size={textSize}>
    {#if $$slots.icon}
      <div class="c-callout-icon">
        <slot name="icon" />
      </div>
    {/if}

    <div class="c-callout-body">
      <slot />
    </div>
  </TextAugment>
</div>

<style>
  .c-callout {
    /* Colors */
    --callout-text-color: var(--ds-color-a11);
    --callout-high-contrast-text-color: var(--ds-color-12);
    --callout-border-color: var(--ds-color-a6);
    --callout-soft-bg-color: var(--ds-color-a3);
    --callout-surface-bg-color: var(--ds-color-a2);

    /* Sizes */
    --callout-border-radius: var(--ds-radius-3);
    --callout-padding: var(--ds-spacing-3);
    --callout-gap: var(--ds-spacing-2);
    --callout-icon-size: 16px;

    display: flex;
    flex-direction: row;
    gap: var(--callout-gap);
    /* Align the icon with the first line of text */
    align-items: flex-start;

    border-radius: var(--callout-border-radius);
    padding: var(--callout-padding);
    color: var(--callout-text-color);
  }

  /* Variant */
  .c-callout--soft {
    background: var(--callout-soft-bg-color);
  }

  .c-callout--surface {
    background: var(--callout-surface-bg-color);
  }

  .c-callout--outline,
  .c-callout--surface {
    border: 1px solid var(--callout-border-color);
  }

  /* Size */
  .c-callout--size-2 {
    --callout-border-radius: var(--ds-radius-4);
    --callout-padding: var(--ds-spacing-4);
    --callout-gap: var(--ds-spacing-3);
  }

  .c-callout--size-3 {
    --callout-border-radius: var(--ds-radius-5);
    --callout-padding: var(--ds-spacing-5);
    --callout-gap: var(--ds-spacing-4);
    --callout-icon-size: 20px;
  }

  /* High contrast */
  .c-callout.c-callout--highContrast,
  :global([data-augment-theme-intensity="high-contrast"]) .c-callout {
    color: var(--callout-high-contrast-text-color);
  }

  /* Color */
  .c-callout--accent {
    --callout-text-color: var(--ds-text);
    --callout-high-contrast-text-color: var(--ds-text);
  }
  .c-callout--neutral {
    --callout-high-contrast-text-color: var(--ds-text);
  }

  /* Icon styles */
  .c-callout-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;

    /* Align the icon with the first line of text */
    height: 1lh;
  }

  .c-callout-icon :global(svg) {
    /* Remove descenders space below svg */
    display: block;

    width: var(--callout-icon-size);
    height: var(--callout-icon-size);
  }
</style>

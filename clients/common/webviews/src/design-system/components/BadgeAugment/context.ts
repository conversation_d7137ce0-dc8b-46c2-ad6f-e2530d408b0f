import { type ButtonSize } from "../../_primitives/BaseButton.svelte";
import type {
  BadgeColor,
  BadgeSize,
  BadgeVariant,
} from "$common-webviews/src/design-system/components/BadgeAugment/badge-types";

export class BadgeContext {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  static CONTEXT_KEY = "augment-badge";

  constructor(private _opts: BadgeOptions) {}

  public get color(): BadgeColor {
    return this._opts.color;
  }

  public get size(): ButtonSize {
    return this._opts.size ?? 1;
  }

  public get variant(): BadgeVariant {
    return this._opts.variant;
  }
}

type BadgeOptions = {
  color: BadgeColor;
  size: BadgeSize;
  variant: BadgeVariant;
};

<script lang="ts">
  import type {
    ButtonColor,
    ButtonSize,
    ButtonVariant,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import ChildIconButton from "$common-webviews/src/design-system/_primitives/ChildIconButton.svelte";
  import { getContext } from "svelte";
  import { BadgeContext } from "./context";

  const context = getContext<BadgeContext>(BadgeContext.CONTEXT_KEY);

  export let color: ButtonColor = getValidColor(context.color, "neutral");
  export let highContrast: boolean = false;
  export let disabled: boolean = false;

  // For now keep these internal
  let size: ButtonSize = context.size;
  let variant: ButtonVariant = "ghost";

  function getValidColor(badgeColor: unknown, defaultColor: ButtonColor): ButtonColor {
    return typeof badgeColor === "string" &&
      ["accent", "neutral", "error", "success", "warning", "info"].includes(badgeColor)
      ? (badgeColor as ButtonColor)
      : defaultColor;
  }

  $: ({ class: className, ...restProps } = $$restProps);
</script>

<div class={`c-badge-icon-btn c-badge-icon-btn--${context.variant} c-badge-icon-btn--size-${size}`}>
  <ChildIconButton
    {size}
    {variant}
    {color}
    {highContrast}
    {disabled}
    class={`c-badge-icon-btn__base-btn ${className}`}
    on:click
    on:keyup
    on:keydown
    on:mousedown
    on:mouseover
    on:focus
    on:mouseleave
    on:blur
    on:contextmenu
    {...restProps}
  >
    <slot />
  </ChildIconButton>
</div>

<style>
  .c-badge-icon-btn {
    display: contents;
  }

  /* For solid badges, the icons should inherit the badge color */
  .c-badge-icon-btn--solid :global(.c-base-btn) {
    --base-btn-color: currentColor;
  }

  .c-badge-icon-btn :global(.c-icon-btn .c-base-btn.c-badge-icon-btn__base-btn) {
    display: flex;
    border-radius: calc(var(--badge-border-radius) - 1px);
    /* Remove the negative margins on ghost buttons */
    margin: 0;
    /* Inset button vertically within the badge */
    margin-block: calc(var(--badge-padding-vertical) * -1);
  }

  .c-badge-icon-btn--size-1 :global(.c-icon-btn) {
    --icon-btn-size: 20px;
    --icon-button-ghost-padding: var(--ds-spacing-1);
  }

  .c-badge-icon-btn--size-2 :global(.c-icon-btn),
  .c-badge-icon-btn--size-3 :global(.c-icon-btn) {
    --icon-btn-size: 24px;
    --icon-button-ghost-padding: var(--ds-spacing-1);
  }
</style>

<script lang="ts">
  export let color: string = "rgba(255, 255, 255, 0.5)";
  export let speed: number = 4;
  export let repeat: number | "infinite" = 1;
  export let startOnMount: boolean = false;
  export let fadeSpeed: number = 0.3; // Added fade speed parameter
  export let tailLength: number = 5; // Length of the chaser tail in percentage

  export let onStopChaser: () => void = () => {};

  let active = false;
  let repeatNumber = repeat === "infinite" ? Infinity : repeat;

  let stopTimeout: NodeJS.Timeout | undefined = undefined;
  export function startChaser() {
    active = true;
    clearTimeout(stopTimeout);
    stopTimeout = setTimeout(
      () => {
        stopChaser();
      },
      speed * 1000 * repeatNumber,
    );
  }

  export function stopChaser() {
    if (!active) {
      return;
    }

    active = false;
    clearTimeout(stopTimeout);
    onStopChaser();
  }

  if (startOnMount) {
    startChaser();
  }
</script>

<i
  class="c-chaser"
  style:--chaser-color={color}
  style:--chaser-speed={speed + "s"}
  style:--chaser-fade-speed={fadeSpeed + "s"}
  style:--chaser-tail-length={tailLength + "%"}
  class:c-chaser--active={active}
>
  <span class="c-chaser__gradient" />
</i>

<style>
  @keyframes rotate-chaser {
    0% {
      transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }

  /* Chaser effect styles */
  .c-chaser {
    content: "";
    position: absolute;
    z-index: -1;
    inset: 0;
    border-radius: var(--base-btn-border-radius, var(--badge-border-radius));
    padding: max(var(--base-btn-focus-outline-offset, 1px), 1px); /* the thickness of the border */
    /* the below will do the magic */
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      /* this will cover only the content area (no padding) */ linear-gradient(#fff 0 0); /* this will cover all the area */
    mask:
      linear-gradient(#fff 0 0) content-box,
      /* this will cover only the content area (no padding) */ linear-gradient(#fff 0 0); /* this will cover all the area */
    -webkit-mask-composite: xor; /* needed for old browsers until the below is more supported */
    mask-composite: exclude; /* this will exclude the first layer from the second so only the padding area will be kept visible */
    opacity: 0;
    transition: opacity var(--chaser-fade-speed, 0.3s) ease-in-out;
  }

  .c-chaser--active {
    opacity: 1;
  }

  .c-chaser .c-chaser__gradient::before {
    content: "";
    position: absolute;
    left: 50%;
    top: 50%;
    width: 1000%;
    height: 1000%;
    /* Create a radial gradient that's clipped to only show on one side */
    background: conic-gradient(
      from 0deg at 50% 50%,
      transparent 0,
      transparent calc(10% - var(--chaser-tail-length, 5%)),
      var(--chaser-color) 25%,
      var(--chaser-color) calc(25% + var(--chaser-tail-length, 5%)),
      transparent calc(30% + var(--chaser-tail-length, 5%)),
      transparent 100%
    );
    transform: translate(-50%, -50%) rotate(0deg);
    animation: rotate-chaser var(--chaser-speed, 4s) linear infinite;
  }
</style>

<script lang="ts">
  import "@radix-ui/colors/gray.css";
  import "@radix-ui/colors/gray-alpha.css";
  import "@radix-ui/colors/black-alpha.css";

  import BaseTextInput, {
    type TextFieldColor,
    type TextFieldSize,
    type TextFieldVariant,
  } from "../_primitives/BaseTextInput.svelte";
  import { createEventDispatcher } from "svelte";

  const dispatch = createEventDispatcher();

  export let variant: TextFieldVariant = "surface";
  export let size: TextFieldSize = 2;
  export let color: TextFieldColor | undefined = undefined;
  export let textInput: HTMLInputElement | undefined = undefined;
  export let value: string = "";
  export let id: string | undefined = undefined;

  // Generate a unique ID if none is provided
  const generatedId = `text-field-${Math.random().toString(36).substring(2, 11)}`;

  // Use the provided id or fall back to the generated one
  $: inputId = id || generatedId;

  // Handle change event to both call the onChange prop and dispatch the event
  function handleChange(event: Event) {
    dispatch("change", event);
  }

  $: ({ class: className, ...restProps } = $$restProps);
</script>

<div
  class="c-text-field"
  class:c-text-field--has-left-icon={$$slots.iconLeft !== undefined}
  class:c-text-field--has-right-icon={$$slots.iconRight !== undefined}
>
  {#if $$slots.label}
    <label class="c-text-field-label" for={inputId}>
      <slot name="label" />
    </label>
  {/if}
  <BaseTextInput {variant} {size} {color}>
    {#if $$slots.iconLeft}
      <div class="c-text-field__slot c-base-text-input__slot">
        <slot name="iconLeft" />
      </div>
    {/if}
    <input
      spellCheck="false"
      class={`c-text-field__input c-base-text-input__input ${className}`}
      id={inputId}
      bind:this={textInput}
      bind:value
      {...restProps}
      on:change={handleChange}
      on:click
      on:keydown
      on:input
      on:blur
      on:dblclick
      on:focus
    />
    {#if $$slots.iconRight}
      <div class="c-text-field__slot c-base-text-input__slot">
        <slot name="iconRight" />
      </div>
    {/if}
  </BaseTextInput>
</div>

<style>
  .c-text-field-label {
    display: block;
    margin-bottom: var(--ds-spacing-2);
    text-align: left;
  }
  .c-text-field {
    display: flex;
    flex-direction: column;
  }

  /* If there is a left icon, remove left indentation  */
  .c-text-field--has-left-icon .c-text-field__input {
    --base-text-field-input-padding: 0;
  }
</style>

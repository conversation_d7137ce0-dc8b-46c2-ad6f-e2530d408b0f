<script lang="ts" context="module">
  export type SeparatorSize = 1 | 2 | 3 | 4;
  export type SeparatorOrientation = "horizontal" | "vertical";
</script>

<script lang="ts">
  export let size: SeparatorSize = 1;
  export let orientation: SeparatorOrientation = "horizontal";
  export let useCurrentColor: boolean = false;
</script>

<div
  class={`c-separator c-separator--size-${size} c-separator--orientation-${orientation}`}
  class:c-separator--current-color={useCurrentColor}
/>

<style>
  .c-separator {
    --separator-color: var(--slate-a6);

    display: block;
    background-color: var(--separator-color);
  }

  .c-separator--current-color {
    --separator-color: currentColor;
  }

  /* Orientation */
  .c-separator--orientation-horizontal {
    width: var(--separator-size);
    height: 1px;
  }

  .c-separator--orientation-vertical {
    width: 1px;
    height: var(--separator-size);
  }

  /* Sizes */
  .c-separator--size-1 {
    --separator-size: var(--ds-spacing-4);
  }

  .c-separator--size-2 {
    --separator-size: var(--ds-spacing-6);
  }

  .c-separator--size-3 {
    --separator-size: var(--ds-spacing-9);
  }

  .c-separator--size-4 {
    --separator-size: 100%;
  }
</style>

import { render, type RenderResult, waitFor } from "@testing-library/svelte";
import userEvent from "@testing-library/user-event";
import { expect, describe, test, afterEach, beforeEach, vi } from "vitest";

import TestMentions from "./Mentions.test.svelte";
import { EditorTestKit } from "./test-kit";
import { Chip<PERSON>ontroller } from "../plugins/Mention/context/chip-controller";

let component: RenderResult<TestMentions>;
const onQueryUpdate = vi.fn(async (query: string | undefined): Promise<void> => {
  if (query === undefined) {
    void component.rerender({ mentionables: [] });
  } else {
    void component.rerender({
      mentionables: [
        { id: "1", name: `Mention ${query} 1`, label: `Mention ${query} 1` },
        { id: "2", name: `Mention ${query} 2`, label: `Mention ${query} 2` },
        { id: "3", name: `Mention ${query} 3`, label: `Mention ${query} 3` },
      ],
    });
  }
});

const onMentionItemsUpdated = vi.fn();
beforeEach(async () => {
  EditorTestKit.mockTipTapDOMFunctions();
  component = render(TestMentions, {
    mentionables: [],
    onQueryUpdate,
    onMentionItemsUpdated,
  });
});

afterEach(() => {
  component.unmount();
  vi.clearAllMocks();
});

describe("Mention", () => {
  test("should open dropdown menu when trigger character is typed", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    // Expect no content and no menu
    await waitFor(() => expect(editor.textContent).toBe(""));
    expect(component.queryByTestId("mention-menu-item")).toBeNull();

    // Type trigger character
    await userEvent.type(editor, "@");
    expect(onQueryUpdate).toBeCalledWith("");
    await waitFor(() => {
      const menuItems = component.getAllByTestId("mention-menu-item");
      expect(menuItems.length).toBe(3);
    });
  });

  test("should close dropdown menu when trigger character is deleted", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    // Type trigger character
    await userEvent.type(editor, "@");
    await waitFor(() => {
      const menuItems = component.getAllByTestId("mention-menu-item");
      expect(menuItems.length).toBe(3);
    });

    // Delete trigger character
    await userEvent.type(editor, "{backspace}");
    expect(onQueryUpdate).toBeCalledWith(undefined);
    expect(component.queryByTestId("mention-menu-item")).toBeNull();
  });

  test("should close dropdown menu when escape is pressed", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    // Type trigger character
    await userEvent.type(editor, "@");
    await waitFor(() => {
      const menuItems = component.getAllByTestId("mention-menu-item");
      expect(menuItems.length).toBe(3);
    });

    // Press escape
    await userEvent.keyboard("{Escape}");
    expect(onQueryUpdate).toBeCalledWith(undefined);
    expect(component.queryByTestId("mention-menu-item")).toBeNull();
  });

  test("should close dropdown menu when enter is pressed", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    // Type trigger character
    await userEvent.type(editor, "@");
    await waitFor(() => {
      const menuItems = component.getAllByTestId("mention-menu-item");
      expect(menuItems.length).toBe(3);
    });

    // Press enter
    await userEvent.keyboard("{Enter}");
    expect(onQueryUpdate).toBeCalledWith(undefined);
    expect(component.queryByTestId("mention-menu-item")).toBeNull();
  });

  test("should close dropdown menu when click outside", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    // Type trigger character
    await userEvent.type(editor, "@");
    await waitFor(() => {
      const menuItems = component.getAllByTestId("mention-menu-item");
      expect(menuItems.length).toBe(3);
    });

    // Click outside
    await userEvent.click(document.body);
    expect(onQueryUpdate).toBeCalledWith(undefined);
    expect(component.queryByTestId("mention-menu-item")).toBeNull();
  });

  test("should insert mention when menu item is clicked", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    // Type trigger character
    await userEvent.type(editor, "@");
    await waitFor(() => {
      const menuItems = component.getAllByTestId("mention-menu-item");
      expect(menuItems.length).toBe(3);
    });

    // Click first menu item
    const firstMenuItem = component.getAllByTestId("mention-menu-item")[0];
    await userEvent.click(firstMenuItem);

    // Expect menu to close and mention to be inserted
    expect(onQueryUpdate).toBeCalledWith(undefined);
    expect(component.queryByTestId("mention-menu-item")).toBeNull();
    const chipElement = component.baseElement.querySelector(`.${ChipController.CHIP_CLASS_NAME}`);
    expect(chipElement).not.toBeNull();

    // Make sure the chip has the right data on it
    expect(chipElement?.getAttribute("data-label")).toBe("Mention  1");
    expect(chipElement?.getAttribute("data-id")).toBe("1");
    const chipData = JSON.parse(
      chipElement?.getAttribute("data-augment-mention-chip-tooltip") ?? "",
    );
    expect(chipData).toStrictEqual({
      id: "1",
      name: "Mention  1",
      label: "Mention  1",
    });
  });

  test("should insert mention when menu item is selected with arrow keys", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    // Type trigger character
    await userEvent.type(editor, "@");
    await waitFor(() => {
      const menuItems = component.getAllByTestId("mention-menu-item");
      expect(menuItems.length).toBe(3);
    });

    // Select last menu item
    await userEvent.keyboard("{ArrowDown}");
    await userEvent.keyboard("{ArrowDown}");
    await userEvent.keyboard("{Enter}");

    // Expect menu to close and mention to be inserted
    expect(onQueryUpdate).toBeCalledWith(undefined);
    expect(component.queryByTestId("mention-menu-item")).toBeNull();
    const chipElement = component.baseElement.querySelector(`.${ChipController.CHIP_CLASS_NAME}`);
    expect(chipElement).not.toBeNull();

    // Make sure the chip has the right data on it
    expect(chipElement?.getAttribute("data-label")).toBe("Mention  3");
    expect(chipElement?.getAttribute("data-id")).toBe("3");
    const chipData = JSON.parse(
      chipElement?.getAttribute("data-augment-mention-chip-tooltip") ?? "",
    );
    expect(chipData).toStrictEqual({
      id: "3",
      name: "Mention  3",
      label: "Mention  3",
    });
  });

  test("different trigger characters can be used", async () => {
    component.unmount();
    component = render(TestMentions, {
      triggerCharacter: "/",
      mentionables: [],
      onQueryUpdate,
      onMentionItemsUpdated,
    });
    const editor = await EditorTestKit.waitForRichTextInput(component);
    // Type trigger character
    await userEvent.type(editor, "/");
    await waitFor(() => {
      const menuItems = component.getAllByTestId("mention-menu-item");
      expect(menuItems.length).toBe(3);
    });
  });

  test("different allowed prefixes can be used", async () => {
    component.unmount();
    component = render(TestMentions, {
      allowedPrefixes: ["!"],
      mentionables: [],
      onQueryUpdate,
      onMentionItemsUpdated,
    });
    const editor = await EditorTestKit.waitForRichTextInput(component);
    // Type trigger character
    await userEvent.type(editor, "This is a test query @");
    expect(component.queryByTestId("mention-menu-item")).toBeNull();

    // Delete trigger character
    await userEvent.type(editor, "{backspace}");

    // Type trigger character with allowed prefix
    await userEvent.type(editor, "!@");
    await waitFor(() => {
      const menuItems = component.getAllByTestId("mention-menu-item");
      expect(menuItems.length).toBe(3);
    });
  });

  test("Can use empty allowed prefixes", async () => {
    component.unmount();
    component = render(TestMentions, {
      allowedPrefixes: [],
      mentionables: [],
      onQueryUpdate,
      onMentionItemsUpdated,
    });
    const editor = await EditorTestKit.waitForRichTextInput(component);
    // Type trigger character
    await userEvent.type(editor, "This is a test query @");
    // Nothing should show up, since we don't allow any prefixes
    expect(component.queryByTestId("mention-menu-item")).toBeNull();

    // Delete everything
    for (let i = 0; i < 23; i++) {
      await userEvent.type(editor, "{backspace}");
    }

    // Type trigger character
    await userEvent.type(editor, "@");
    await waitFor(() => {
      const menuItems = component.getAllByTestId("mention-menu-item");
      expect(menuItems.length).toBe(3);
    });
  });

  test("onMentionItemsUpdated is called when mention items are selected", async () => {
    const onMentionItemsUpdated = vi.fn();
    component.unmount();
    component = render(TestMentions, {
      allowedPrefixes: [],
      mentionables: [],
      onQueryUpdate,
      onMentionItemsUpdated,
    });
    const editor = await EditorTestKit.waitForRichTextInput(component);
    // Type trigger character
    await userEvent.type(editor, "@");
    await waitFor(() => {
      const menuItems = component.getAllByTestId("mention-menu-item");
      expect(menuItems.length).toBe(3);
    });

    // Not called
    onMentionItemsUpdated.mockClear();

    // Insert a mention
    await userEvent.keyboard("{Enter}");
    expect(onMentionItemsUpdated).toBeCalledWith({
      added: [
        {
          id: "1",
          name: "Mention  1",
          label: "Mention  1",
        },
      ],
      removed: [],
      current: [
        {
          id: "1",
          name: "Mention  1",
          label: "Mention  1",
        },
      ],
    });

    // Delete the mention
    await userEvent.keyboard("{Backspace}");
    expect(onMentionItemsUpdated).toBeCalledWith({
      added: [],
      removed: [
        {
          id: "1",
          name: "Mention  1",
          label: "Mention  1",
        },
      ],
      current: [],
    });
  });
});

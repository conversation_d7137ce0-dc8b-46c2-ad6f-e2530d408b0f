export async function readImageAsBlob(image: File, maxSize = 1024) {
  return new Promise<Blob>((resolve, reject) => {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const img = new Image();
    img.onload = () => {
      // Create a canvas to scale the image

      // Calculate dimensions while preserving aspect ratio
      let width = img.width;
      let height = img.height;

      if (width > height) {
        if (width > maxSize) {
          height = Math.round(height * (maxSize / width));
          width = maxSize;
        }
      } else {
        if (height > maxSize) {
          width = Math.round(width * (maxSize / height));
          height = maxSize;
        }
      }

      // Set canvas dimensions and draw the scaled image
      canvas.width = width;
      canvas.height = height;

      if (!ctx) {
        reject(new Error("Could not get canvas context"));
        return;
      }

      ctx.drawImage(img, 0, 0, width, height);
      canvas.toBlob((blob) => {
        if (blob == null) {
          reject(new Error("Failed to create blob"));
          return;
        }
        resolve(blob);
      });
    };

    img.onerror = () => reject(new Error("Failed to load image"));
    img.src = URL.createObjectURL(image);
  });
}

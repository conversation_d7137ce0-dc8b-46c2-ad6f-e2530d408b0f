import { expect, describe, test, beforeEach, afterEach, vi, type <PERSON><PERSON>, it } from "vitest";
import { ImageController } from "./controller";
import type { Editor, ChainedCommands, JSONContent } from "@tiptap/core";
import { EditorTestKit } from "../../__tests__/test-kit";
import { waitFor } from "@testing-library/dom";
import * as readImageUtil from "./read-image-util";

let imageController: ImageController;
let editor: Editor;
let chainedCommands: ChainedCommands;
let mockSaveImage: Mock;

beforeEach(() => {
  // Mock the DOM functions needed by TipTap
  EditorTestKit.mockTipTapDOMFunctions();
  editor = createMockEditor();
  mockSaveImage = vi.fn().mockResolvedValue("test-image-name");
  imageController = new ImageController({ saveImage: mockSaveImage });
  imageController.registerEditor(editor);
  URL.createObjectURL = vi.fn().mockReturnValue("blob:mock-url");

  // Mock readImageAsDataURL
  vi.spyOn(readImageUtil, "readImageAsBlob").mockImplementation(() =>
    Promise.resolve(new File([Buffer.from("data:image/jpeg;base64,mockdata")], "test.jpg")),
  );
});

afterEach(() => {
  vi.clearAllMocks();
});

describe("ImageController", () => {
  test("insertImagesIntoEditor handles valid image files", async () => {
    const validImageFile = new File([""], "test.jpg", { type: "image/jpeg" });
    const result = imageController.insertImagesIntoEditor([validImageFile]);

    expect(result.errors).toHaveLength(0);
    expect(result.okFiles).toHaveLength(1);
    expect(result.okFiles[0]).toBe(validImageFile);

    await waitFor(() => {
      expect(mockSaveImage).toHaveBeenCalledWith(validImageFile);
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(editor.chain).toHaveBeenCalled();
    });
  });

  test("insertImagesIntoEditor rejects non-image files", () => {
    const nonImageFile = new File([""], "test.txt", { type: "text/plain" });
    const result = imageController.insertImagesIntoEditor([nonImageFile]);

    expect(result.errors).toHaveLength(1);
    expect(result.okFiles).toHaveLength(0);
  });

  test("insertImagesIntoEditor handles multiple files", async () => {
    const validImage1 = new File([""], "test1.jpg", { type: "image/jpeg" });
    const validImage2 = new File([""], "test2.png", { type: "image/png" });
    const invalidFile = new File([""], "test.txt", { type: "text/plain" });

    const result = imageController.insertImagesIntoEditor([validImage1, invalidFile, validImage2]);

    expect(result.errors).toHaveLength(1);
    expect(result.okFiles).toHaveLength(2);
    expect(result.okFiles).toContain(validImage1);
    expect(result.okFiles).toContain(validImage2);

    await waitFor(() => {
      expect(mockSaveImage).toHaveBeenCalledTimes(2);
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(editor.chain).toHaveBeenCalledTimes(2);
    });
    await waitFor(() => {
      expect(chainedCommands.run).toHaveBeenCalledTimes(2);
    });
  });

  test("insertImagesIntoEditor handles empty file array", () => {
    const result = imageController.insertImagesIntoEditor([]);

    expect(result.errors).toHaveLength(0);
    expect(result.okFiles).toHaveLength(0);
  });

  test("insertImagesIntoEditor handles undefined editor", () => {
    imageController.unregisterEditor();
    const result = imageController.insertImagesIntoEditor([
      new File([""], "test.jpg", { type: "image/jpeg" }),
    ]);

    expect(result.errors).toHaveLength(0);
    //due to the way the mock works
    expect(result.okFiles).toHaveLength(1);
  });

  test("insertImagesIntoEditor handles maxImages option", () => {
    imageController.updateOptions({ maxImages: 1, saveImage: mockSaveImage });
    const validImage1 = new File([""], "test1.jpg", { type: "image/jpeg" });
    const validImage2 = new File([""], "test2.png", { type: "image/png" });

    const result = imageController.insertImagesIntoEditor([validImage1, validImage2]);

    expect(result.errors).toHaveLength(1);
    expect(result.okFiles).toHaveLength(1);
    expect(result.okFiles).toContain(validImage1);
  });

  test("insertImagesIntoEditor handles maxImageSizeKb option", () => {
    imageController.updateOptions({
      maxImageSizeKb: 1,
      saveImage: mockSaveImage,
    });
    const validImage = new File([""], "test1.jpg", { type: "image/jpeg" });
    const invalidImage = new File(["x".repeat(2 * 1024)], "test2.png", {
      type: "image/png",
    });

    const result = imageController.insertImagesIntoEditor([validImage, invalidImage]);

    expect(result.errors).toHaveLength(1);
    expect(result.okFiles).toHaveLength(1);
  });

  describe("hasLoadingImages (improved version)", () => {
    it("should return false for null or undefined input", () => {
      expect(ImageController.hasLoadingImages(null)).toBe(false);
      expect(ImageController.hasLoadingImages(undefined)).toBe(false);
    });

    it("should return false for empty content", () => {
      const emptyContent: JSONContent = { type: "doc", content: [] };
      expect(ImageController.hasLoadingImages(emptyContent)).toBe(false);
    });

    it("should return false when no images are present", () => {
      const contentWithoutImages: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [{ type: "text", text: "This is a paragraph without images" }],
          },
        ],
      };
      expect(ImageController.hasLoadingImages(contentWithoutImages)).toBe(false);
    });

    it("should return false when images are present but not loading", () => {
      const contentWithNonLoadingImages: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [{ type: "text", text: "This is a paragraph with an image: " }],
          },
          {
            type: "image",
            attrs: { src: "image.jpg", isLoading: false },
          },
        ],
      };
      expect(ImageController.hasLoadingImages(contentWithNonLoadingImages)).toBe(false);
    });

    it("should return true when at least one image is loading", () => {
      const contentWithLoadingImage: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [{ type: "text", text: "This is a paragraph with a loading image: " }],
          },
          {
            type: "image",
            attrs: { src: "image.jpg", isLoading: true },
          },
        ],
      };
      expect(ImageController.hasLoadingImages(contentWithLoadingImage)).toBe(true);
    });

    it("should handle content with missing attrs property", () => {
      const contentWithMissingAttrs: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [{ type: "text", text: "This is a paragraph" }],
          },
          {
            type: "image", // Missing attrs property
          },
        ],
      };
      expect(ImageController.hasLoadingImages(contentWithMissingAttrs)).toBe(false);
    });

    it("should handle content with empty attrs object", () => {
      const contentWithEmptyAttrs: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [{ type: "text", text: "Paragraph" }],
          },
          {
            type: "image",
            attrs: {}, // Empty attrs object
          },
        ],
      };
      expect(ImageController.hasLoadingImages(contentWithEmptyAttrs)).toBe(false);
    });

    it("should handle malformed content gracefully", () => {
      // @ts-expect-error - Testing with invalid input structure
      expect(ImageController.hasLoadingImages({ type: "doc", content: null })).toBe(false);
      // @ts-expect-error - Testing with invalid input structure
      expect(ImageController.hasLoadingImages({ type: "doc", content: "not an array" })).toBe(
        false,
      );
    });
  });
});

function createMockEditor(): Editor {
  const editor = {
    commands: {
      setImage: vi.fn((): ChainedCommands => chainedCommands),
    },
    state: {
      doc: {
        descendants: vi.fn(),
        content: { size: 0 },
      },
      selection: { from: 0 },
    },
    schema: {
      nodes: {
        image: { create: vi.fn() },
        paragraph: { create: vi.fn() }, // Add the missing paragraph node
      },
    },
    chain: vi.fn((): ChainedCommands => chainedCommands),
    destroy: vi.fn(),
  };
  chainedCommands = {
    ...editor.commands,
    run: vi.fn(),
    command: vi.fn((cb) => {
      cb({
        tr: { insert: vi.fn(), doc: editor.state.doc },
        state: editor.state,
      });
      return chainedCommands;
    }),
    focus: vi.fn().mockReturnThis(),
  } as unknown as ChainedCommands;
  return editor as unknown as Editor;
}

/**
 * This file contains the MentionPluginContext class, which is a core component of the mention plugin
 * for the Rich Text Editor. It integrates with TipTap's Mention extension and manages the lifecycle
 * of mentions within the editor.
 */

import type { Attributes, KeyboardShortcutCommand, Node } from "@tiptap/core";
import type { Node as ProseMirrorNode } from "prosemirror-model";
import { Mention, type MentionOptions } from "@tiptap/extension-mention";
import { Plugin, PluginKey } from "@tiptap/pm/state";

import type { IRichTextEditorPlugin } from "../../../types";
import type {
  CommandArgs,
  IMentionContextOptions,
  IMentionOptionData,
  IMentionable,
} from "../types";
import { findNewMentions, getMentionNodes } from "../utils";
import { type Editor } from "@tiptap/core";
import { ChipController } from "./chip-controller";
import { MentionableMenuContext } from "./mentionable-menu";

/**
 * MentionPluginContext class
 *
 * This class is responsible for managing the mention functionality within the Rich Text Editor.
 * It extends TipTap's Mention extension and provides additional features such as chip tooltips
 * and mentionable menus.
 *
 * @template TOption - The type of mentionable items, which must extend IMentionable
 *
 * Public controls API:
 * - insertMentionNode: Inserts a mention node into the editor
 *
 * Public state API:
 * - chipController: Context for managing chip tooltips
 * - mentionableMenuContext: Context for managing the mentionable menu
 *
 * TipTap hooks (used internally within RichTextEditorAugment)
 * - onProseMirrorUpdate: Callback for when the ProseMirror document is updated
 * - onCreate: Callback for when the editor is created
 * - onDestroy: Callback for when the editor is destroyed
 * - addKeyboardShortcuts: Defines keyboard shortcuts for the mention plugin
 * - addAttributes: Defines custom attributes for the mention nodes
 * - addProseMirrorPlugins: Adds a ProseMirror plugin to listen for document updates
 * - addOptions: Configures additional options for the mention extension
 */
export class MentionPluginContext<TOption extends IMentionable> implements IRichTextEditorPlugin {
  /* eslint-disable @typescript-eslint/naming-convention */
  public static readonly CONTEXT_KEY = "augment-svelte-mention-plugin";
  public static readonly MENTION_LISTENER_PLUGIN_ID_BASE = "{}-listener";
  public static readonly MENTION_PLUGIN_ID_BASE = "augment-prosemirror-mention-{}";
  public static readonly DEFAULT_MENTION_PLUGIN_ID = "mention";
  /* eslint-enable @typescript-eslint/naming-convention */
  private readonly _triggerCharacter: string;

  private _editor: Editor | undefined;
  private _mention: Node<MentionOptions, IMentionOptionData<TOption>>;
  private _chipController: ChipController<TOption>;
  private _mentionableMenuContext: MentionableMenuContext<TOption> =
    new MentionableMenuContext<TOption>();

  /**
   * Constructor for MentionPluginContext
   *
   * @param _options - Configuration options for the mention plugin
   */
  constructor(private _options: IMentionContextOptions<TOption>) {
    // This is the character that triggers the mention menu
    const triggerCharacter = _options.triggerCharacter ?? "@";
    this._triggerCharacter = triggerCharacter;
    const mentionListenerPluginKey = new PluginKey(this._mentionListenerPluginId);
    const mentionPluginKey = new PluginKey(this._mentionPluginId);
    const allowedPrefixes = _options.allowedPrefixes ?? [" ", "\t", "\n"];

    // Default renderText function to turn the mentionable into a string
    const defaultRenderText = (mentionable: TOption) =>
      `${triggerCharacter}${mentionable.name ?? mentionable.id}`;
    const renderText = _options.renderText ?? defaultRenderText;

    // Initialize child contexts
    this._chipController = new ChipController<TOption>(this._mentionPluginId);

    // Bind class methods to preserve 'this' context
    const onCreate = this._onCreate.bind(this);
    const onProseMirrorUpdate = this._onProseMirrorUpdate.bind(this);
    const onDestroy = this._onDestroy.bind(this);

    // Get references to methods from child contexts
    const createMentionChip = this._chipController.createMentionChip;
    const onUpdateSuggestion = this._mentionableMenuContext.onUpdateSuggestion;
    const onExitMenu = this._mentionableMenuContext.exitMenu;
    const onArrowUp = this._mentionableMenuContext.onArrowUp;
    const onArrowDown = this._mentionableMenuContext.onArrowDown;
    const onSelectActiveItem = this._mentionableMenuContext.selectActiveItem;

    // Extend TipTap's Mention extension with custom functionality
    this._mention = Mention.extend({
      name: this._mentionPluginId,
      onCreate(): void {
        onCreate(this.editor);
      },
      onDestroy(): void {
        onDestroy();
      },
      addKeyboardShortcuts(): Record<string, KeyboardShortcutCommand> {
        return {
          /* eslint-disable @typescript-eslint/naming-convention */
          ArrowUp: onArrowUp,
          ArrowDown: onArrowDown,
          Enter: onSelectActiveItem,
          Tab: onSelectActiveItem,
          /* eslint-enable @typescript-eslint/naming-convention */
        };
      },
      // Add a custom 'data' attribute to store additional information with each mention
      addAttributes(): Attributes {
        return {
          ...this.parent?.(),
          // This includes configurations for how to serialize/deserialize
          // the data attribute when converting to/from HTML for things like copy/paste
          data: {
            default: null,
            keepOnSplit: false,
            // How to parse HTML into the data attribute we care about (for WYSISYG editing)
            parseHTML: (e: HTMLElement) => {
              const dataAttr = e.getAttribute(ChipController.CHIP_DATA_ATTR_KEY);
              return dataAttr ? (JSON.parse(dataAttr) as TOption) : null;
            },
            // How to render the data attribute as HTML (for WYSISYG editing)
            renderHTML: (attributes: Record<string, unknown>) => {
              if (attributes.data) {
                return {
                  [ChipController.CHIP_DATA_ATTR_KEY]: JSON.stringify(attributes.data),
                };
              }
              return {};
            },
          },
        };
      },
      // Add a ProseMirror plugin to listen for document updates
      addProseMirrorPlugins() {
        return [
          ...(this.parent?.() ?? []),
          new Plugin({
            key: mentionListenerPluginKey,
            view: () => ({
              update: (view, prevState) => {
                onProseMirrorUpdate(prevState.doc, view.state.doc);
              },
            }),
          }),
        ];
      },
      // Configure additional options for the mention extension
      addOptions(): MentionOptions {
        const parentOpts = this.parent?.();
        return {
          ...parentOpts,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          HTMLAttributes: { class: ChipController.CHIP_CLASS_NAME },
          renderHTML: createMentionChip,
          renderText: ({ node }) => {
            const option: TOption = node.attrs.data as TOption;
            return renderText(option);
          },
          suggestion: {
            ...parentOpts?.suggestion,
            pluginKey: mentionPluginKey,
            char: triggerCharacter,
            allowedPrefixes,
            // Custom command to handle mention insertion
            command: ({ editor, range, props: optionItem }: CommandArgs<TOption>) => {
              if (editor && range) {
                parentOpts.suggestion?.command?.({
                  editor,
                  range,
                  props: {
                    id: optionItem.id,
                    name: optionItem.name ?? optionItem.id,
                    label: optionItem.label,
                    data: optionItem,
                  },
                });
              }
            },
            // Hooks for managing the mentionable menu lifecycle
            render: () => ({
              onStart: onUpdateSuggestion,
              onUpdate: onUpdateSuggestion,
              onExit: onExitMenu,
            }),
          },
        };
      },
    });
  }

  // Getter methods for accessing child contexts
  public get chipController() {
    return this._chipController;
  }

  public get mentionableMenuContext() {
    return this._mentionableMenuContext;
  }

  public insertMentionNode = (mention: IMentionOptionData<TOption>): boolean => {
    if (!this._editor) {
      return false;
    }

    // Try to select the item via the menu context. If it fails, insert the node directly
    if (!this._mentionableMenuContext.replaceQueryWithMentionNode(mention.data)) {
      this._editor.commands.insertContent({
        type: this._mentionPluginId,
        attrs: mention,
      });
    }
    return true;
  };

  // ==== TipTap hooks ===

  private get _mentionPluginId(): string {
    if (this._options.pluginId) {
      return this._options.pluginId;
      // We have a special case here -- @ triggers should always use the default plugin name
      // to be compatible with the default TipTap mention plugin and historical saved
      // chats, which use this mentino plugin name.
    } else if (this._triggerCharacter === "@") {
      return MentionPluginContext.DEFAULT_MENTION_PLUGIN_ID;
    }
    return MentionPluginContext.MENTION_PLUGIN_ID_BASE.replace("{}", this._triggerCharacter);
  }

  private get _mentionListenerPluginId() {
    return MentionPluginContext.MENTION_LISTENER_PLUGIN_ID_BASE.replace(
      "{}",
      this._mentionPluginId,
    );
  }

  /**
   * Callback triggered when the editor is created
   * Notifies the parent of initial mentions to initialize its state
   */
  private _onCreate = (editor: Editor) => {
    const allMentions = getMentionNodes(this._mentionPluginId, editor).map(
      (node) => node.attrs.data as TOption,
    );
    this._options.onMentionItemsUpdated?.({
      added: allMentions,
      removed: [],
      current: allMentions,
    });
    this._editor = editor;
    this._chipController.onCreate(editor);
  };

  /**
   * Callback triggered when the editor is destroyed
   * Clears the internal editor reference
   */
  private _onDestroy = () => {
    // Notify the parent of all mentions being removed, since we are tearing everything down
    if (this._editor) {
      const allMentions = getMentionNodes(this._mentionPluginId, this._editor).map(
        (node) => node.attrs.data as TOption,
      );
      this._options.onMentionItemsUpdated?.({ added: [], removed: allMentions, current: [] });
      this._editor = undefined;
      this._chipController.onDispose();
    }

    // Hide the tooltip and exit the menu
    this._chipController.hideTooltip();
    this._mentionableMenuContext.exitMenu();
  };

  /**
   * Callback triggered when the editor content is updated
   * Notifies the parent of new mentions and removed mentions
   */
  private _onProseMirrorUpdate = (previousDoc: ProseMirrorNode, currentDoc: ProseMirrorNode) => {
    if (previousDoc === currentDoc) {
      return;
    }

    const extractMention = (node: ProseMirrorNode) => node.attrs.data as TOption;
    const currentMentions: TOption[] = [];
    // Find mentions that existed previously but not now
    const removedMentions = findNewMentions(this._mentionPluginId, previousDoc, currentDoc).map(
      extractMention,
    );
    // Find mentions that exist now but not previously
    const addedMentions = findNewMentions(this._mentionPluginId, currentDoc, previousDoc, {
      onNewMention: (node) => currentMentions.push(extractMention(node)),
      onExistingMention: (node) => currentMentions.push(extractMention(node)),
    }).map(extractMention);
    this._options.onMentionItemsUpdated?.({
      added: addedMentions,
      removed: removedMentions,
      current: currentMentions,
    });
  };

  /**
   * Getter for the TipTap extension
   * @implements IRichTextEditorPlugin
   */
  public get tipTapExtension() {
    return this._mention;
  }
}

<script lang="ts">
  import { getContext, onD<PERSON>roy } from "svelte";
  import { RichTextEditorContext } from "../../context";
  import { ImageController } from "./controller";
  import { type JSONContent } from "@tiptap/core";

  export let maxImages: number | undefined = undefined;
  /**
   * Save the image locally
   *
   * @returns a key to retrieve the saved image
   */
  export let saveImage: ((file: File) => Promise<string>) | undefined = undefined;
  /**
   * Delete the image from storage
   *
   * @param imageId - The key of the image to delete
   */
  export let deleteImage: ((imageId: string) => Promise<void>) | undefined = undefined;
  /**
   * Render the image in the editor
   *
   * @param file - The key to retrieve the image
   *
   * @returns base64-encoded data URL or undefined if not found
   */
  export let renderImage: ((file: string) => Promise<string | undefined>) | undefined = undefined;
  /** Whether the editor is editable */
  export let isEditable: () => boolean = () => true;
  /** Optional callback when image mode is changed */
  export let changeImageMode: ((updatedContent?: JSONContent) => void) | undefined = undefined;

  // Initialize the image controller
  const imageController = new ImageController({
    changeImageMode,
    maxImages,
    saveImage,
    deleteImage,
    renderImage,
    isEditable,
  });

  // Register the image plugin and ensure it's unregistered when the component is destroyed
  const editorContext = getContext<RichTextEditorContext>(RichTextEditorContext.CONTEXT_KEY);
  const unregister = editorContext.pluginManager.registerPlugin(imageController);
  onDestroy(unregister);
</script>

<style>
  /* Defines the style of an image */
  :global(.rich-text-editor-image) {
    max-width: 100%;
    height: auto;
    margin: 1em 0;
  }
</style>

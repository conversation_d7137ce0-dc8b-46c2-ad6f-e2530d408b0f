import { type J<PERSON><PERSON><PERSON><PERSON>, type Editor, type Node } from "@tiptap/core";
import Image, { type ImageOptions } from "@tiptap/extension-image";

import type { IRichTextEditorPlugin } from "../../types";
import {
  ImageCountExceededError,
  ImageError,
  validateImage,
  type ValidateImageOptions,
} from "./errors";
import { Plugin, PluginKey } from "prosemirror-state";
import ImageRenderer from "./ImageRenderer.svelte";
import { type Attrs, type Node as ProseMirrorNode } from "prosemirror-model";
import { readImageAsBlob } from "./read-image-util";
import { ImageRendererMode } from "./image-renderer-mode";

/**
 * If this plugin is being used, add this command to the TipTap type system.
 */
declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    insertImagesIntoEditor: {
      insertImagesIntoEditor?: (files: File[]) => ReturnType;
    };
  }
}
declare module "@tiptap/extension-image" {
  interface ImageOptions {
    defaultRenderMode?: ImageRendererMode;
    renderMode?: ImageRendererMode;
    isLoading?: boolean;
  }
}

export type ImageControllerOptions = {
  changeImageMode?: (updatedContent?: JSONContent) => void;
  maxImages?: number;
  /**
   * Save the image locally
   *
   * @returns a key to retrieve the saved image
   */
  saveImage?: (file: File) => Promise<string>;
  /**
   * Delete the image from storage
   *
   * @param imageId - The key of the image to delete
   */
  deleteImage?: (imageId: string) => Promise<void>;
  /**
   * Render the image in the editor
   *
   * @param file - The key to retrieve the image
   *
   * @returns base64-encoded data URL or undefined if not found
   */
  renderImage?: (file: string) => Promise<string | undefined>;
  /** Whether the editor is editable */
  isEditable?: () => boolean;
} & ValidateImageOptions;

/**
 * ImageController class
 *
 * This class implements the IRichTextEditorPlugin interface and provides functionality
 * for handling image drops and pastes in a rich text editor.
 */
export class ImageController implements IRichTextEditorPlugin {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  private static IMAGE_PASTE_HANDLER_PLUGIN_KEY = new PluginKey(
    "augment-prosemirror-image-paste-handler",
  );
  private _tipTapExtension: Node<ImageOptions>;
  private _editor: Editor | undefined;

  /**
   * Constructor for the ImageController class.
   * Initializes the TipTap extension for handling image drops and pastes.
   */
  constructor(private _options?: ImageControllerOptions) {
    this._tipTapExtension = this._createTipTapExtension();
  }

  private _createTipTapExtension = (): Node<ImageOptions> => {
    const registerEditor = this.registerEditor;
    const unregisterEditor = this.unregisterEditor;
    const insertImagesIntoEditor = this.insertImagesIntoEditor;

    return Image.extend({
      addOptions() {
        return {
          ...this.parent?.(),
          allowBase64: true,
          preserveOnSplit: false,
          defaultRenderMode: ImageRendererMode.collapsed,
        };
      },

      addAttributes() {
        return {
          ...this.parent?.(),
          renderMode: {
            default: ImageRendererMode.collapsed,
          },
          title: { default: "" },
          isLoading: { default: false },
        };
      },

      onCreate() {
        this.parent?.();
        registerEditor(this.editor);
      },

      onDestroy() {
        this.parent?.();
        unregisterEditor();
      },

      addCommands() {
        return {
          ...this.parent?.(),
          insertImagesIntoEditor:
            (files: File[]) =>
            ({ chain }: { chain: Editor["chain"] }) => {
              return chain()
                .command(() => !!insertImagesIntoEditor(files))
                .focus("end")
                .run();
            },
        };
      },

      addNodeView: () => this._createNodeView(),
      addProseMirrorPlugins() {
        return [
          ...(this.parent?.() ?? []),
          new Plugin({
            key: ImageController.IMAGE_PASTE_HANDLER_PLUGIN_KEY,
            props: {
              handlePaste: (_, event) => {
                if (!event.clipboardData?.files?.length) {
                  return false;
                }
                const result = insertImagesIntoEditor([...event.clipboardData.files]);
                if (result.okFiles.length > 0) {
                  event.preventDefault();
                  return true;
                }
                return false;
              },
            },
          }),
        ];
      },
    });
  };

  private _createNodeView = () => {
    return ({
      node,
    }: {
      node: ProseMirrorNode;
    }): {
      dom: HTMLElement;
      destroy: () => void;
      update: (node: ProseMirrorNode) => boolean;
    } => {
      const target = document.createElement("div");
      const component = new ImageRenderer({
        target,
        props: {
          title: String(node.attrs.title),
          src: String(node.attrs.src),
          loadImage: this._options?.renderImage ?? (() => Promise.resolve(undefined)),
          renderMode: node.attrs.renderMode as ImageRendererMode,
          isLoading: !!node.attrs.isLoading,
          removeImage: async () => {
            if (!this._editor) {
              return;
            }

            // Delete the image from storage if deleteImage is provided
            try {
              if (this._options?.deleteImage) {
                await this._options.deleteImage(String(node.attrs.src));
              }
            } catch (error) {
              console.error("Failed to delete image:", error);
            }

            // Remove the node from the editor
            this._editor
              .chain()
              .command(({ tr, state }) => {
                state.doc.descendants((currNode, pos) => {
                  if (node === currNode) {
                    tr.delete(pos, pos + currNode.nodeSize);
                    return false;
                  }
                });
                return true;
              })
              .focus("end")
              .run();
          },
          setImageMode: (renderMode: ImageRendererMode) => {
            if (!this._editor || node.attrs.renderMode === renderMode) {
              return;
            }
            updateImageNode(this._editor, node, { renderMode });
            const updatedContent = this._editor.getJSON();
            // Get the updated content after the image mode change and
            // call the changeImageMode callback with the updated content if available
            this._options?.changeImageMode?.(updatedContent);
          },
          isEditable: this._options?.isEditable ?? (() => true),
        },
      });

      return {
        dom: target,
        destroy: () => {
          component.$destroy();
        },
        update: (updatedNode: ProseMirrorNode): boolean => {
          if (updatedNode.type.name !== "image") {
            return false;
          }
          node = updatedNode;

          component.$set({
            src: String(updatedNode.attrs.src),
            renderMode: updatedNode.attrs.renderMode as ImageRendererMode,
            isLoading: !!updatedNode.attrs.isLoading,
          });

          return true;
        },
      };
    };
  };

  public registerEditor = (editor: Editor): void => {
    this._editor = editor;
  };

  public unregisterEditor = (): void => {
    this._editor = undefined;
  };

  /**
   * Counts the number of image nodes in the editor.
   * @returns {number | undefined} The count of image nodes, or undefined if the editor is not available.
   */
  private _getImageNodeCount = (): number | undefined => {
    if (!this._editor) {
      return undefined;
    }

    let imageCount = 0;
    this._editor.state.doc.descendants((node) => {
      if (node.type.name === "image") {
        imageCount++;
      }
    });

    return imageCount;
  };

  public updateOptions = (options: ImageControllerOptions) => {
    this._options = { ...options };
  };

  /**
   * Validates an array of files and separates them into valid images and errors.
   * @param files - Array of files to validate
   * @returns Object containing valid images and validation errors
   */
  private validateImages(files: File[]): {
    validImages: File[];
    errors: ImageError[];
  } {
    const errors: ImageError[] = [];
    const validImages: File[] = [];

    for (const file of files) {
      const result = validateImage(file, this._options);
      if (result instanceof ImageError) {
        errors.push(result);
      } else {
        validImages.push(result);
      }
    }

    return { validImages, errors };
  }

  /**
   * Checks if the number of images to insert exceeds the maximum limit.
   * @param validImages - Array of validated image files
   * @returns Object containing images to insert and any limit-related errors
   */
  private checkImageCountLimit(validImages: File[]): {
    imagesToInsert: File[];
    errors: ImageError[];
  } {
    const numImages = this._getImageNodeCount() ?? 0;
    const maxImages = this._options?.maxImages;

    if (maxImages === undefined || numImages + validImages.length <= maxImages) {
      return { imagesToInsert: validImages, errors: [] };
    }

    const imagesToInsert = validImages.slice(0, maxImages - numImages);
    const rejectedImages = validImages.slice(maxImages - numImages);
    const error = new ImageCountExceededError(maxImages, imagesToInsert, rejectedImages);

    return { imagesToInsert, errors: [error] };
  }

  /**
   * Process a single image
   *
   * Scale the image down, save the scaled image, and replace the loading node with the actual image
   *
   * @param image - The image file to process
   */
  private async _processImage(image: File, uniqId: string): Promise<void> {
    try {
      // Scale the image
      const scale = await readImageAsBlob(image);
      if (!scale || !this._editor) {
        return;
      }

      // Create a new file with the scaled image
      const file = new File([scale], image.name, { type: image.type });

      // Save the image and get its name
      const imageKey = (await this._options?.saveImage?.(file)) ?? file.name;

      // Update the placeholder loading node with the actual image
      this._updateImageNode(uniqId, {
        src: imageKey,
        title: file.name,
        isLoading: false,
      });
    } catch (error) {
      console.error("Could not process image: ", error);

      // Remove the placeholder on error
      if (this._editor) {
        this._removeImageNode(uniqId);
      }

      throw error;
    }
  }

  /**
   * Inserts multiple images into the editor, handling validation and limits.
   *
   * @param files - An array of File objects representing the images to insert.
   *
   * @returns Object containing errors encountered and successfully inserted files.
   */
  public insertImagesIntoEditor = (
    files: File[],
  ): {
    errors: ImageError[];
    okFiles: File[];
  } => {
    // Validate images and check limits
    const { validImages, errors } = this.validateImages(files);
    const { imagesToInsert, errors: limitErrors } = this.checkImageCountLimit(validImages);
    const insertionErrors: ImageError[] = [];

    // Insert placeholder images with loading state immediately
    for (const image of imagesToInsert) {
      if (!this._editor) {
        continue;
      }

      // Insert a placeholder with loading state
      const uniqId = this._insertLoadingImage(this._editor, image.name);

      // Process the image asynchronously
      this._processImage(image, uniqId).catch((error) => {
        insertionErrors.push(new ImageError([image], String(error)));
      });
    }

    return {
      errors: [...errors, ...limitErrors, ...insertionErrors],
      okFiles: imagesToInsert,
    };
  };

  /**
   * Getter for the TipTap extension
   * @returns {Node<ImageOptions>} The configured TipTap extension
   */
  public get tipTapExtension(): Node<ImageOptions> {
    return this._tipTapExtension;
  }

  /**
   * Find an image node by its src
   *
   * @param id - The ID to search for
   *
   * @returns Object with node and position if found
   */
  private _findImageNodeBySrc(id: string): { node: ProseMirrorNode; pos: number } | null {
    if (!this._editor || !id) {
      return null;
    }

    let result = null;
    this._editor.state.doc.descendants((node, pos) => {
      if (node.type.name === "image" && node.attrs.src === id) {
        result = { node, pos };
        return false;
      }
      return true;
    });

    return result;
  }

  /**
   * Inserts an image node with loading state
   *
   * @param editor - The editor instance
   * @param imageName - The name of the image
   */
  private _insertLoadingImage(editor: Editor, imageName: string) {
    const { from } = editor.state.selection;
    // Generate a unique ID for this image instance
    const uniqueId = `${imageName}-${Date.now()}-${Math.random().toString(36).substring(2)}`;

    editor
      .chain()
      .command(({ tr }) => {
        const pos = from || tr.doc.content.size;
        tr.insert(pos, [
          editor.schema.nodes.image.create({
            src: uniqueId, // Use the unique ID as src for the loading state
            originalName: imageName, // Store the original name for later use
            title: imageName,
            renderMode: ImageRendererMode.collapsed,
            isLoading: true,
          }),
          editor.schema.nodes.paragraph.create({ text: "" }),
        ]);
        return true;
      })
      .focus("end")
      .run();

    return uniqueId; // Return the unique ID for tracking
  }

  /**
   * Updates an existing image node with new attributes
   *
   * @param imageName - The name of the loading image
   * @param updates - The attributes to update
   */
  private _updateImageNode(imageName: string, updates: Attrs): void {
    if (!this._editor) {
      return;
    }

    const found = this._findImageNodeBySrc(imageName);
    if (found) {
      this._editor
        .chain()
        .command(({ tr }) => {
          tr.setNodeMarkup(found.pos, undefined, { ...found.node.attrs, ...updates });
          return true;
        })
        .run();
    }
  }

  /**
   * Removes an image node from the editor
   *
   * @param imageName - The name of the image that is loading
   */
  private _removeImageNode(imageName: string): void {
    if (!this._editor) {
      return;
    }

    const found = this._findImageNodeBySrc(imageName);
    if (found) {
      this._editor
        .chain()
        .command(({ tr }) => {
          tr.delete(found.pos, found.pos + found.node.nodeSize);
          return true;
        })
        .run();
    }
  }

  /**
   * Checks if the rich text editor JSON contains any image nodes that are still loading
   *
   * @param json - The rich text editor JSON content to check
   *
   * @returns true if the JSON contains any image nodes that are still loading
   */
  static hasLoadingImages(json: JSONContent | null | undefined): boolean {
    if (!json) {
      return false;
    }

    const traverse = (node: JSONContent): boolean => {
      if (!node) {
        return false;
      }
      if (node.type === "image" && node.attrs?.isLoading) {
        return true;
      }
      if (node.content) {
        if (!Array.isArray(node.content)) {
          return false;
        }
        for (const contentNode of node.content) {
          if (traverse(contentNode)) {
            return true;
          }
        }
      }
      return false;
    };

    return traverse(json);
  }
}

function updateImageNode(editor: Editor, node: ProseMirrorNode, updates: Partial<ImageOptions>) {
  editor
    .chain()
    .command(({ tr, state }) => {
      state.doc.descendants((currNode, pos) => {
        // Compare node type and attributes to identify the same node
        // Using node === currNode might not work reliably for ProseMirror nodes
        if (node.type === currNode.type && node.attrs.src === currNode.attrs.src) {
          tr.setNodeMarkup(pos, undefined, { ...currNode.attrs, ...updates });
          return false; // Stop traversal
        }
      });
      return true;
    })
    .run();
}

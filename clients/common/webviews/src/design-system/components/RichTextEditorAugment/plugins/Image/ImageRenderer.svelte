<script lang="ts">
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";
  import IconButtonAugment from "../../../IconButtonAugment.svelte";
  import { ImageRendererMode } from "./image-renderer-mode";
  import DropdownMenuAugment from "../../../DropdownMenuAugment";
  import DotsHorizontal from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import type DropdownRoot from "../../../DropdownMenuAugment/Root.svelte";
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import TextAugment from "../../../TextAugment.svelte";
  import SpinnerAugment from "../../../SpinnerAugment.svelte";

  /**
   * The name of the original image file. This is user facing text.
   */
  export let title: string;
  /** The name of the image in the asset manager */
  export let src: string;
  export let loadImage: (fileName: string) => Promise<string | undefined>;
  export let renderMode: ImageRendererMode = ImageRendererMode.collapsed;
  export let removeImage: () => void;
  export let setImageMode: (renderMode: ImageRendererMode) => void;
  /** If true, a loading spinner is displayed with the title instead of the image */
  export let isLoading: boolean = false;
  /**
   * Get whether the editor is editable
   *
   * If true, then the image can be removed from the editor
   */
  export let isEditable: () => boolean;

  function _setToMode(mode: ImageRendererMode) {
    setImageMode(mode);
    dropdownRoot?.requestClose();
  }

  function handleKeyDown(e: KeyboardEvent) {
    // Handle arrow keys for navigation and activation
    if (
      e.key === "ArrowUp" ||
      e.key === "ArrowDown" ||
      e.key === "ArrowLeft" ||
      e.key === "ArrowRight"
    ) {
      isActive = true;
      e.preventDefault();
      e.stopPropagation();
    }
  }

  let isActive = false;
  let imgElement: HTMLImageElement | undefined;
  let dropdownRoot: DropdownRoot | undefined;
</script>

<svelte:window
  on:click|capture={(e) => {
    if (!e.composedPath().some((el) => el === imgElement)) {
      isActive = false;
    }
  }}
/>
<CardAugment
  insetContent
  interactive
  size={1}
  on:click={(e) => {
    isActive = true;
    e.preventDefault();
  }}
  on:keydown={(e) => {
    handleKeyDown(e);
    onKey("Escape", () => (isActive = false))(e);
  }}
  class="c-image-renderer"
>
  <div
    class="c-image-container"
    class:is-collapsed={renderMode === "collapsed"}
    class:is-preview={renderMode === "preview"}
    class:is-full={renderMode === "full"}
  >
    {#if isLoading}
      <div class="c-image__loading-spinner">
        <SpinnerAugment size={2} />
      </div>
    {:else}
      {#await loadImage(src)}
        <div class="c-image__loading-spinner">
          <SpinnerAugment size={2} />
        </div>
      {:then base64}
        <img bind:this={imgElement} src={base64} alt="" class="c-rich-text-editor-image" />
      {/await}
    {/if}
    <div class="c-image__action-header" class:is-active={isActive}>
      <TextAugment size={renderMode === "collapsed" ? 2 : 1} class="c-image__title">
        {title}
      </TextAugment>
      {#if !isLoading}
        <DropdownMenuAugment.Root bind:this={dropdownRoot} nested={false}>
          <DropdownMenuAugment.Trigger>
            <IconButtonAugment
              size={1}
              color="neutral"
              variant={renderMode === "collapsed" ? "ghost-block" : "solid"}
            >
              <DotsHorizontal />
            </IconButtonAugment>
          </DropdownMenuAugment.Trigger>
          <DropdownMenuAugment.Content size={1} side="bottom" align="end">
            <DropdownMenuAugment.Label>Image options</DropdownMenuAugment.Label>
            <DropdownMenuAugment.Item
              onSelect={() => _setToMode(ImageRendererMode.collapsed)}
              highlight={renderMode === ImageRendererMode.collapsed}
            >
              Collapsed view
            </DropdownMenuAugment.Item>
            <DropdownMenuAugment.Item
              onSelect={() => _setToMode(ImageRendererMode.preview)}
              highlight={renderMode === ImageRendererMode.preview}
            >
              Preview
            </DropdownMenuAugment.Item>
            <DropdownMenuAugment.Item
              onSelect={() => _setToMode(ImageRendererMode.full)}
              highlight={renderMode === ImageRendererMode.full}
            >
              Full size
            </DropdownMenuAugment.Item>
            {#if isEditable()}
              <DropdownMenuAugment.Separator />
              <DropdownMenuAugment.Item onSelect={removeImage} color="error">
                Remove image
              </DropdownMenuAugment.Item>
            {/if}
          </DropdownMenuAugment.Content>
        </DropdownMenuAugment.Root>
      {/if}
    </div>
  </div>
</CardAugment>

<style>
  :global(.c-image-renderer.c-image-renderer.c-image-renderer) {
    max-width: 98%;
    overflow: hidden;
  }
  :global(.ProseMirror-selectednode) :global(.c-card:has(> .c-image-container)) {
    outline: 2px solid var(--ds-color-accent-8);
    outline-offset: 2px;
  }
  /* Default styling for the card */
  :global(.c-card:has(> .c-image-container)) {
    margin: var(--ds-spacing-1);
    width: fit-content;

    &:has(.c-image__action-header.is-active) {
      outline: 2px solid var(--ds-color-accent-8);
      outline-offset: 2px;
    }
  }

  :global(.c-card:hover > .c-image-container > .c-image__action-header) {
    opacity: 1;
    overflow: hidden;
  }

  /* Default styling for the remove button */
  .c-image__action-header {
    /* Make the action header evenly spaced */
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-1);
    opacity: 0;
    overflow: hidden;

    /* When active, show the remove button */
    &.is-active {
      opacity: 1;
    }
  }

  .c-image-container {
    --c-image--preview-height: 96px;

    overflow: hidden;
    max-width: 100%;
    width: fit-content;
    border-radius: var(--ds-spacing-2);

    /* Should always be distributed in a row. For expanded items, this does nothing. */
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;

    & > img {
      max-width: 100%;
      width: fit-content;
      object-fit: contain;
      display: block;
    }
    & .c-image__title {
      display: flex;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      min-width: 0;
      flex: 1;
    }
    &.is-collapsed {
      padding: var(--ds-spacing-1);
      gap: var(--ds-spacing-1);
      flex-direction: row;

      /* Show text/dropdown to the right of the image when collapsed */
      & > .c-image__action-header {
        position: unset;
        opacity: 1;
        align-items: center;
        max-width: inherit;
      }

      & > img {
        display: inline-block;
        max-height: var(--ds-spacing-5);
      }
    }

    &:not(.is-collapsed) > .c-image__action-header {
      position: absolute;
      top: 0;
      right: 0;

      width: 100%;
      padding: var(--ds-spacing-1);
      /* Expand the flex items */
      justify-content: space-between;
      /* make the text on top more legible */
      backdrop-filter: blur(4px);
    }

    &.is-preview > img {
      max-height: var(--c-image--preview-height);
    }

    &.is-full > img {
      max-height: 100%;
    }

    &.is-preview > .c-image__loading-spinner,
    &.is-full > .c-image__loading-spinner {
      height: var(--c-image--preview-height);
      width: var(--c-image--preview-height);
    }
  }

  .c-image__loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>

import {
  ImageCountExceededError,
  ImageError,
  ImageFileSizeError,
  ImageFileTypeError,
  validateImage,
} from "./errors";
import { expect, describe, it } from "vitest";

describe("Image errors", () => {
  describe("validateImage", () => {
    it("should return an ImageFileTypeError for non-image files", () => {
      const file = new File([""], "test.txt", { type: "text/plain" });
      const result = validateImage(file);
      expect(result).toBeInstanceOf(ImageFileTypeError);
    });

    it("should return an ImageFileSizeError for files exceeding the size limit", () => {
      // Create a 2KB file by repeating a character 2048 times (2 * 1024 bytes)
      const twoKbContent = "x".repeat(2048);
      const file = new File([twoKbContent], "test.png", { type: "image/png" });
      const result = validateImage(file, { maxImageSizeKb: 1 });
      expect(result).toBeInstanceOf(ImageFileSizeError);
    });

    it("should return the file itself if it passes validation", () => {
      const file = new File([""], "test.png", { type: "image/png" });
      const result = validateImage(file);
      expect(result).toBe(file);
    });
  });

  describe("ImageError", () => {
    it("should have the correct name and message", () => {
      const error = new ImageError(
        [new File([""], "test.png", { type: "image/png" })],
        "Test error message",
      );
      expect(error.name).toBe("ImageError");
      expect(error.message).toBe("Test error message");
    });
  });

  describe("ImageFileSizeError", () => {
    it("should have the correct name and message", () => {
      const file = new File([""], "test.png", { type: "image/png" });
      const error = new ImageFileSizeError(file, 100);
      expect(error.name).toBe("ImageError");
      expect(error.message).toContain(`Image "${file.name}" exceeds size limit of 100KB`);
    });
  });

  describe("ImageFileTypeError", () => {
    it("should have the correct name and message", () => {
      const file = new File([""], "test.txt", { type: "text/plain" });
      const error = new ImageFileTypeError(file);
      expect(error.name).toBe("ImageError");
      expect(error.message).toContain(`File "${file.name}" is not an image`);
    });
  });

  describe("ImageCountExceededError", () => {
    it("should have the correct name and message", () => {
      const accepted = [new File([""], "test1.png", { type: "image/png" })];
      const rejected = [new File([""], "test2.png", { type: "image/png" })];
      const error = new ImageCountExceededError(1, accepted, rejected);

      expect(error.name).toBe("ImageError");
      expect(error.message).toContain(`Up to 1 images are supported.`);
      expect(error.message).toContain(`Did not insert images:`);
      expect(error.message).toContain(`- test2.png`);
      expect(error.message).toContain(`Existing count: 0`);
      expect(error.message).toContain(`Accepted count: 1`);
      expect(error.message).toContain(`Rejected count: 1`);
      expect(error.files).toEqual(rejected);
    });
  });
});

/**
 * Custom error class for image-related errors.
 */
export class ImageError extends <PERSON>rror {
  public readonly files: File[];

  /**
   * Creates a new ImageError instance.
   * @param file - The File object that caused the error.
   * @param message - The error message.
   */
  constructor(files: File[], message: string) {
    super(message);
    this.files = files;
    this.name = "ImageError";
  }
}

export class ImageFileSizeError extends ImageError {
  constructor(f: File, maxSizeKb: number) {
    const fileSizeKb = Math.round(f.size / 1024);
    super([f], `Image "${f.name}" exceeds size limit of ${maxSizeKb}KB. Size: ${fileSizeKb}KB `);
  }
}

export class ImageFileTypeError extends ImageError {
  constructor(f: File) {
    super([f], `File "${f.name}" is not an image`);
  }
}

export class ImageCountExceededError extends ImageError {
  constructor(maxImages: number, accepted: File[], rejected: File[]) {
    const errorMessage = [
      `Up to ${maxImages} images are supported.`,
      `- Existing count: ${maxImages - accepted.length}`,
      `- Accepted count: ${accepted.length}`,
      `- Rejected count: ${rejected.length}`,
      `Did not insert images:`,
      ...rejected.map((f) => `- ${f.name}`),
    ].join("\n");

    super(rejected, errorMessage);
  }
}

export type ValidateImageOptions = { maxImageSizeKb?: number };
/**
 * Validates an image file against specified options.
 * @param f - The File object to validate.
 * @param options - Optional validation options.
 * @returns An ImageError if validation fails, undefined otherwise.
 */
export function validateImage(f: File, options?: ValidateImageOptions): ImageError | File {
  // Check if it's actually an image
  if (!isImage(f)) {
    return new ImageFileTypeError(f);
  }

  // Check size limit if specified
  if (options?.maxImageSizeKb !== undefined) {
    const maxSizeBytes = options.maxImageSizeKb * 1024;
    if (f.size > maxSizeBytes) {
      return new ImageFileSizeError(f, options.maxImageSizeKb);
    }
  }

  return f;
}

/**
 * Checks if a file is an image based on its MIME type.
 * @param f - The File object to check.
 * @returns True if the file is an image, false otherwise.
 */
export function isImage(f: File): boolean {
  return f.type.startsWith("image/");
}

<script lang="ts" context="module">
  /* eslint-disable @typescript-eslint/naming-convention */
  const DRAG_DATA_MIME_TYPE = "application/x-augment/drag-origin/{}";
  /* eslint-enable @typescript-eslint/naming-convention */

  function getMimeTypeKey(editorContext: RichTextEditorContext) {
    return DRAG_DATA_MIME_TYPE.replace("{}", editorContext.instanceId);
  }

  function maybeMarkFromRichTextEditor(e: DragEvent, editorContext: RichTextEditorContext) {
    // If this event doesn't pass through the root node of the editor, ignore it
    if (!editorContext.rootNode || !e.composedPath().includes(editorContext.rootNode)) {
      return;
    }

    e.dataTransfer?.setData(getMimeTypeKey(editorContext), editorContext.instanceId);
  }
</script>

<script lang="ts">
  /**
   *
   * This component manages the drag and drop functionality for the rich text editor,
   * providing a drop zone for files and handling the drag state.
   *
   */
  import { getContext } from "svelte";
  import { RichTextEditorContext } from "../../context";
  import FilePlus from "../../../../icons/file-plus.svelte";
  import TextAugment from "../../../TextAugment.svelte";
  import { TextSelection } from "prosemirror-state";
  import { isDraggingStore } from "./state";
  import { type Writable } from "svelte/store";

  /**
   * The store to use for the dragging state
   *
   * The default store is the global store. Or, you can provide a custom store to syncronize
   * the dragging state across different instances of the rich text editor.
   */
  export let draggingStore: Writable<boolean> = isDraggingStore;

  // Get the editor context from the parent component
  const editorContext = getContext<RichTextEditorContext>(RichTextEditorContext.CONTEXT_KEY);

  // Subscribe to the shared dragging state
  let isDragging: boolean;
  draggingStore.subscribe((value) => (isDragging = value));

  /**
   * Handles the drop event, inserting images if present
   * @param {DragEvent} event - The drop event
   */
  function handleDrop(event: DragEvent) {
    draggingStore.set(false);
    if (!event.dataTransfer?.files) {
      return;
    }

    // Handle images, if they exist
    // - First, find the document-relative position of the drop event
    // - Set the selection to that position
    // - Insert the images (at the active selection)
    editorContext?.commandManager
      .chain()
      ?.command(({ view, tr }): boolean => {
        // Get the position of the event in the document and insert at that location
        if (!view) return true;

        const position = view.posAtCoords({
          left: event.clientX,
          top: event.clientY,
        });

        if (position) {
          const resolvedPos = tr.doc.resolve(position.pos);
          // Get the selection for this position
          tr.setSelection(TextSelection.create(tr.doc, resolvedPos.pos));
        }

        return true;
      })
      .insertImagesIntoEditor?.([...(event.dataTransfer?.files ?? [])]);
  }

  /**
   * Sets the dragging state to false when an item leaves the window
   * @param {DragEvent} event - The drag leave event
   */
  function handleWindowDragLeave(event: DragEvent) {
    // Only set isDragging to false if we're leaving the window
    if (!event.relatedTarget || event.relatedTarget === document.body) {
      draggingStore.set(false);
    }
  }

  const setNotDragging = () => draggingStore.set(false);
  const setDragging = (e: DragEvent) => {
    // If the origin of the drag is the rich text editor itself, ignore it
    if (isFromRichTextEditor(e, editorContext)) {
      return;
    }
    // Check if the drag event contains an image
    if (!e.dataTransfer?.types.includes("Files")) {
      return;
    }
    draggingStore.set(true);
  };

  function isFromRichTextEditor(event: DragEvent, context: RichTextEditorContext): boolean {
    return event.dataTransfer?.types.includes(getMimeTypeKey(context)) ?? false;
  }
</script>

<!-- Event listeners for window-level drag and drop events -->
<svelte:window
  on:dragstart={(e) => maybeMarkFromRichTextEditor(e, editorContext)}
  on:dragenter={setDragging}
  on:dragleave={handleWindowDragLeave}
  on:drop={setNotDragging}
  on:blur={setNotDragging}
/>

<!-- The drop zone element -->
<div
  class="c-rich-text-editor-dropzone"
  class:is-active={isDragging}
  on:dragover|stopPropagation|preventDefault
  on:dragenter|stopPropagation|preventDefault
  on:drop|stopPropagation|preventDefault={handleDrop}
  role="button"
  tabindex="-1"
>
  <div class="c-rich-text-editor-dropzone__content">
    <slot>
      <FilePlus />
      <TextAugment class="c-rich-text-editor-dropzone__text" size={2}>
        Drop images to attach as context
      </TextAugment>
      <TextAugment class="c-rich-text-editor-dropzone__text" size={1}>(png, jpg, jpeg)</TextAugment>
    </slot>
  </div>
</div>

<style>
  /**
   * Styles for the drop zone element
   */
  .c-rich-text-editor-dropzone {
    position: absolute;
    inset: 0; /* This replaces top/left/right/bottom: 0 */
    container-type: size;

    opacity: 0;
    pointer-events: none;
    background-color: var(--ds-color-accent-2);
    border: 2px dashed var(--ds-color-accent-6);
    border-radius: var(--ds-radius-4);
    z-index: var(--z-rich-text-editor-drag-overlay);

    /* Remove transition from base state */
    display: flex;
    align-items: center;
    justify-content: center;
    /* Only apply transition when becoming active */
    transition: opacity 50ms ease-in;

    &.is-active {
      opacity: 0.9;
      pointer-events: auto;
      background-color: var(--ds-color-accent-3);
      border-color: var(--ds-color-accent-10);
    }
  }

  .c-rich-text-editor-dropzone__content {
    display: flex;
    gap: var(--spacing-1-augment);
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .c-rich-text-editor-dropzone__content :global(.c-text) {
    /* Override the inline display from the TextAugment component */
    display: block;
  }

  /* If the container is too small, hide the text. This will not affect user-provided slots */
  @container (max-height: 60px) {
    .c-rich-text-editor-dropzone__content :global(.c-rich-text-editor-dropzone__text) {
      display: none;
    }
  }
</style>

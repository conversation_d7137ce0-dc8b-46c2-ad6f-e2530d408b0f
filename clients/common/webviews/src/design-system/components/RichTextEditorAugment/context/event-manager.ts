import { writable } from "svelte/store";
import type { ContentData, IDisposeFn } from "../types";
import type { Editor } from "@tiptap/core";
import { type Readable } from "svelte/motion";

/**
 * EventManager: Manages events for the rich text editor.
 *
 * Key responsibilities:
 * 1. Event Abstraction: Generalizes TipTap events for easier consumption.
 * 2. State Management: Maintains editor state using Svelte stores.
 * 3. Late Initialization Support: Allows registering listeners before TipTap instance is available.
 * 4. Lifecycle Management: Handles editor registration/unregistration and event listener cleanup.
 *
 * Data Flow:
 * - Input: TipTap editor events
 * - Processing: Updates internal Svelte stores
 * - Output: Subscription methods for state changes are called
 *
 * Integration:
 * - Used by RichTextEditorContext for event management
 * - Consumed by RichTextEditorAugment components for state reactions
 * - Enables event hooking without direct TipTap coupling
 *
 * Enhances modularity and testability by decoupling event handling from editor implementation.
 */
export class EventManager {
  // State as reported by the last valid editor instance
  private _isFocused = writable(false);
  private _isEditable = writable(false);
  private _content = writable<ContentData | undefined>(undefined);
  private _disposers: IDisposeFn[] = [];

  // Footer click detection state
  private _isFooterClicked = false;
  private _footerClickTimeout: ReturnType<typeof setTimeout> | undefined;

  // Footer-aware focus state for external consumption
  private _footerAwareFocused = writable(false);

  public registerEditor = (editor: Editor) => {
    this._isFocused.set(editor.isFocused);
    this._isEditable.set(editor.isEditable);
    this._content.set({
      richTextJsonRepr: editor.getJSON(),
      rawText: editor.getText(),
    });

    // Initialize footer-aware focus state
    this._footerAwareFocused.set(editor.isFocused);

    // Define event listeners from the editor
    const onFocus = () => {
      this._isFocused.set(true);
      if (!this._isFooterClicked) {
        this._footerAwareFocused.set(true);
      }
    };
    const onBlur = () => {
      this._isFocused.set(false);
      if (!this._isFooterClicked) {
        this._footerAwareFocused.set(false);
      }
    };
    const onUpdate = () => this._isEditable.set(editor.isEditable);
    const onContentChange = () => {
      this._content.set({
        richTextJsonRepr: editor.getJSON(),
        rawText: editor.getText(),
      });
    };

    // Attach event listeners
    editor.on("focus", onFocus);
    editor.on("blur", onBlur);
    editor.on("update", onUpdate);
    editor.on("update", onContentChange);

    // Store disposers for later cleanup
    this._disposers.push(
      () => editor.off("focus", onFocus),
      () => editor.off("blur", onBlur),
      () => editor.off("update", onUpdate),
      () => editor.off("update", onContentChange),
    );
  };

  // Unregister the editor instance. This will prevent any further events from being tracked.
  // Here, we dispose of any subscriptions attached to the editor previously.
  public unregisterEditor = () => {
    this._isFocused.set(false);
    this._isEditable.set(false);
    this._footerAwareFocused.set(false);

    // Clear any pending footer click timeout
    if (this._footerClickTimeout) {
      clearTimeout(this._footerClickTimeout);
      this._footerClickTimeout = undefined;
    }

    // Dispose all event listeners
    this._disposers.forEach((dispose) => dispose());
    this._disposers = [];
  };

  // Handle footer click to maintain focus state
  public handleFooterClick = () => {
    // Set footer clicked state
    this._isFooterClicked = true;

    // Clear any existing timeout
    if (this._footerClickTimeout) {
      clearTimeout(this._footerClickTimeout);
    }

    // Reset the state after a delay
    this._footerClickTimeout = setTimeout(() => {
      this._isFooterClicked = false;
      this._footerClickTimeout = undefined;
    }, 200);
  };

  // Below is the public API for subscribing to events.
  // Note that we do *not* register the disposers on this object,
  // as externally-facing events are *not* tied to the lifecycle of the editor.
  public onFocus = (cb: () => void): IDisposeFn => {
    return this.onFocusChanged((isFocused) => {
      isFocused && cb();
    });
  };

  public onBlur = (cb: () => void): IDisposeFn => {
    return this.onFocusChanged((isFocused) => {
      !isFocused && cb();
    });
  };

  public onFocusChanged = (cb: (isFocused: boolean) => void): IDisposeFn => {
    return this._isFocused.subscribe(cb);
  };

  public onEditableChanged = (cb: (isEditable: boolean) => void): IDisposeFn => {
    return this._isEditable.subscribe(cb);
  };

  public onContentChanged = (cb: (content: ContentData) => void): IDisposeFn => {
    return this._content.subscribe((maybeContent) => maybeContent && cb(maybeContent));
  };

  public get isFocused(): Readable<boolean> {
    return this._isFocused;
  }

  public get footerAwareFocused(): Readable<boolean> {
    return this._footerAwareFocused;
  }

  public get isEditable(): Readable<boolean> {
    return this._isEditable;
  }

  public get content(): Readable<ContentData | undefined> {
    return this._content;
  }
}

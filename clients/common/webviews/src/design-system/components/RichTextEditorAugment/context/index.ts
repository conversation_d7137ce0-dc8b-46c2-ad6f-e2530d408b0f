import type { IRichTextEditorOptions } from "../types";
import { PluginManager } from "./plugin-manager";
import { Editor, type EditorOptions } from "@tiptap/core";
import { type Action } from "svelte/action";
import { CommandManager } from "./command-manager";
import { get, writable, type Writable } from "svelte/store";
import { EventManager } from "./event-manager";
import { getContext } from "svelte";
/**
 * Context for managing plugins for the RichTextEditorAugment component.
 * Manages the plugins registered with the context.
 * Automatically sets the context on construction, and provides a static method to retrieve it.
 *
 * Usage:
 * 1. Create a new instance of RichTextEditorContext in the root component of the RichTextEditorAugment.
 * 1.a. Make sure to register the context using setContext from svelte
 * 2. Register the plugins with the context using registerPlugin.
 * 3. If you need to access the context from a child component, use getContext from svelte.
 */
export class RichTextEditorContext {
  /* eslint-disable @typescript-eslint/naming-convention */
  public static CONTEXT_KEY = "augment-rich-text-editor";
  private static INSTANCE_IDX = 0;
  private static _getNextInstanceIdx = () => RichTextEditorContext.INSTANCE_IDX++;
  /* eslint-enable @typescript-eslint/naming-convention */

  // Options, as well as reactive sub-states, for reacting to changes in options
  private _opts: Writable<IRichTextEditorOptions> = writable({});

  // The main editor instance
  private _editor: Editor | undefined = undefined;
  private _rootNode: HTMLElement | undefined = undefined;

  // ==== Public API: Subsystems ====
  //
  // These are the public APIs for the subsystems of the editor. They provide some additional guarantees
  // on top of the TipTap/ProseMirror APIs, such that they can be used in a more controlled way.
  //
  // See the respective files for more detail.
  // - The plugin manager manages the TipTap extensions. When its tracked plugins change, they will send
  //   a signal to reinitialize the editor.
  // - The event manager manages the TipTap events. It provides a more controlled way of interacting
  //   with the editor events, such that they can be registered before the editor is initialized.
  // - The command manager manages the TipTap commands. It provides a more controlled way of interacting
  //   with the editor commands, such that they can be called before the editor is initialized.
  public readonly pluginManager: PluginManager = new PluginManager(this._opts);
  public readonly eventManager: EventManager = new EventManager();
  public readonly commandManager: CommandManager = new CommandManager({
    content: this.eventManager.content,
  });
  public readonly instanceId = `augment-rich-text-editor-${RichTextEditorContext._getNextInstanceIdx()}`;

  constructor() {
    this.pluginManager.onPluginsChanged(this._reinitializeEditor);
  }

  /**
   * Registers the root node for the editor.
   * There should never be more than one root per editor.
   *
   * @param node The root node for the editor.
   * @returns A cleanup function to unregister the root node.
   */
  public registerRoot: Action<HTMLElement, IRichTextEditorOptions> = (
    node: HTMLElement,
    opts: IRichTextEditorOptions,
  ) => {
    this._destroyEditor();
    this._rootNode = node;
    this._opts.set(opts);
    this._initializeEditor();

    return {
      update: (newOpts: IRichTextEditorOptions) => {
        this._opts.set(newOpts);
      },
      destroy: () => {
        this._destroyEditor();
      },
    };
  };

  public get rootNode(): HTMLElement | undefined {
    return this._editor?.view.dom;
  }

  // ==== Private Helpers ====
  //
  // These are private helpers for managing the editor instance.
  // They initialize the editor and attach it to the subsystems that manage it.
  //
  private _registerEditorWithManagers = (editor: Editor) => {
    this.eventManager.registerEditor(editor);
    this.commandManager.registerEditor(editor);
  };

  private _unregisterEditorFromManagers = () => {
    this.commandManager.unregisterEditor();
    this.eventManager.unregisterEditor();
  };

  private _reinitializeEditor = () => {
    // Save the content
    const content = get(this.eventManager.content);
    // Destroy and reinitialize the editor
    this._destroyEditor();
    this._initializeEditor();
    // Restore the content
    if (content !== undefined) {
      this.commandManager.setContent(content.richTextJsonRepr);
    }
  };

  private _initializeEditor = () => {
    if (this._rootNode === undefined || this._editor !== undefined) {
      return;
    }

    const opts = get(this._opts);
    const newOptions: Partial<EditorOptions> = {
      element: document.createElement("div"),
      editable: opts.editable ?? true,
      injectCSS: true,
      extensions: this.pluginManager.tipTapExtensions,
      onCreate: ({ editor }: { editor: Editor }): void => {
        this._registerEditorWithManagers(editor);
        if (get(this._opts).focusOnInit) {
          this.commandManager.focus();
        }

        const disposer = this._attachCopyHandler();
        editor.on("destroy", disposer);

        const onFocus = get(this._opts).onFocus;
        onFocus && editor.on("focus", onFocus);
        const onBlur = get(this._opts).onBlur;
        onBlur && editor.on("blur", onBlur);
      },
      onDestroy: (): void => {
        this._unregisterEditorFromManagers();
      },
      onSelectionUpdate: (): void => {
        // Only do if editable
        if (get(this._opts).editable ?? true) {
          this.commandManager.scrollToCursor();
        }
      },
      editorProps: {
        handlePaste: (_, event: ClipboardEvent) => {
          // Check if this is an internal paste (has HTML content with our markers) or if it's an augment rich text
          if (this._isEventFromRichTextEditor(event)) {
            // Let TipTap handle internal pastes naturally
            return false;
          }

          // For external content, always use plain text
          const rawText = event.clipboardData?.getData("text/plain");
          if (rawText) {
            this.commandManager.insertContent(rawText);
            return true;
          }

          return false;
        },
        attributes: {
          style: "min-height: 100%; outline: none;",
          // eslint-disable-next-line @typescript-eslint/naming-convention
          "data-testid": "design-system-rich-text-editor-tiptap",
        },
      },
    };

    this._editor = new Editor(newOptions);
    this._rootNode.appendChild(this._editor.view.dom);
    return this._editor;
  };

  // These handlers will mark events coming from the augment rich text
  // editor as such. We can then use this information to decide how to
  // handle the resulting HTML
  private _attachCopyHandler = () => {
    this._rootNode?.addEventListener("copy", this._copyHandler);
    this._rootNode?.addEventListener("cut", this._copyHandler);
    return () => {
      this._rootNode?.removeEventListener("copy", this._copyHandler);
      this._rootNode?.removeEventListener("cut", this._copyHandler);
    };
  };

  private _copyHandler = (e: ClipboardEvent) => {
    e.clipboardData?.setData("application/x-augment/rich-text", "true");
  };

  private _isEventFromRichTextEditor = (e: ClipboardEvent) => {
    return e.clipboardData?.getData("application/x-augment/rich-text") === "true";
  };

  // Destroys the editor and removes the DOM element.
  private _destroyEditor = () => {
    this._unregisterEditorFromManagers();
    this._editor?.view.dom.remove();
    this._editor?.destroy();
    this._editor = undefined;
  };
}

export function getRichTextEditorContext(): RichTextEditorContext {
  const editor = getContext<RichTextEditorContext>(RichTextEditorContext.CONTEXT_KEY);
  if (!editor) {
    throw new Error(`No editor context '${RichTextEditorContext.CONTEXT_KEY}' found.`);
  }
  return editor;
}

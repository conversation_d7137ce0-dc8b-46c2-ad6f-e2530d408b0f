import { derived, get, type Readable, writable, type Writable } from "svelte/store";
import type { IDisposeFn, IRichTextEditorOptions, IRichTextEditorPlugin } from "../types";
import Paragraph from "@tiptap/extension-paragraph";
import HardBreak from "@tiptap/extension-hard-break";
import Text from "@tiptap/extension-text";
import Document from "@tiptap/extension-document";
import History from "@tiptap/extension-history";
import { type AnyExtension } from "@tiptap/core";

/**
 * The PluginManager extends TipTap's functionality by providing a centralized system for managing editor plugins.
 *
 * Key additions/benefits over vanilla TipTap:
 * - Dynamic plugin management: Allows adding/removing plugins at runtime.
 * - Reactive updates: Automatically updates editor when plugins or options change.
 * - Simplified integration: Provides a uniform interface for both default and custom plugins.
 * - Enhanced modularity: Facilitates easy addition of complex features like mentions and tooltips.
 *
 * Usage:
 * - Plugins should register themselves with the plugin-manager by accessing the context
 *   from Svelte and calling the `registerPlugin` method.
 * - They should also unregister themselves when they are destroyed.
 */
export class PluginManager {
  // The plugins registered with the context
  private _registeredPlugins: Writable<IRichTextEditorPlugin[]> = writable([]);
  // The default plugins, derived from the options
  private _defaultPlugins: Readable<IRichTextEditorPlugin[]>;
  // All plugins, including default plugins. This is a derived store that
  // combines the default plugins and the registered plugins.
  private _allPlugins: Readable<IRichTextEditorPlugin[]>;

  constructor(private _opts: Readable<IRichTextEditorOptions>) {
    this._defaultPlugins = derived(this._opts, this._getDefaultPlugins);
    this._allPlugins = derived(
      [this._defaultPlugins, this._registeredPlugins],
      ([$defaultPlugins, $registeredPlugins]) => [...$defaultPlugins, ...$registeredPlugins],
    );
  }

  /**
   * Derives the default plugins from the RichTextEditor options.
   * Used to set up reactivity on the options => default plugins relationship.
   *
   * @param opts The options to derive the default plugins from.
   * @returns The default plugins.
   */
  private _getDefaultPlugins = (opts: IRichTextEditorOptions): IRichTextEditorPlugin[] => {
    return [
      Document.extend({
        addKeyboardShortcuts: () => ({ ...opts.keyboardShortcuts }),
      }),
      Paragraph,
      Text,
      HardBreak,
      History.configure({
        depth: 100,
        newGroupDelay: 750,
      }),
    ].map((ext: AnyExtension) => ({ tipTapExtension: ext }));
  };

  /**
   * Registers a plugin with the rich text editor context.
   *
   * Wherever a plugin is constructed + registered is also where it should be cleaned up.
   *
   * @param plugin The plugin to register.
   * @returns A cleanup function to unregister the plugin.
   */
  public registerPlugin = (plugin: IRichTextEditorPlugin): (() => void) => {
    // Register the plugin with the editor
    this._registeredPlugins.update((plugins) => [...plugins, plugin]);
    return () => {
      this._registeredPlugins.update((plugins) => plugins.filter((p) => p !== plugin));
    };
  };

  /**
   * Subscribe to changes in the plugins tracked by this class.
   * @param cb Callback to call when the plugins change
   * @returns A cleanup function to unsubscribe from the plugins change event.
   */
  public onPluginsChanged = (cb: (plugins: IRichTextEditorPlugin[]) => void): IDisposeFn => {
    return this._allPlugins.subscribe(cb);
  };

  public get tipTapExtensions(): AnyExtension[] {
    return get(this._allPlugins).map((p) => p.tipTapExtension);
  }
}

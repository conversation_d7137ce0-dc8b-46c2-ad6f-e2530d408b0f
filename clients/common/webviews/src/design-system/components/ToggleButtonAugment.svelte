<script lang="ts">
  import type { ButtonSize } from "../_primitives/BaseButton.svelte";
  import BaseButton from "../_primitives/BaseButton.svelte";
  import TextAugment from "./TextAugment.svelte";
  import { onDestroy } from "svelte";

  export let options: string[];
  export let size: ButtonSize = 2;
  export let disabled: boolean = false;
  export let onSelectOption: (option: string) => boolean;
  export let activeOption: string = options[0];

  let containerElement: HTMLDivElement | undefined;
  let backgroundSliderElement: HTMLDivElement | undefined;

  /**
   * Handles click event on an option
   * @param option - The clicked option
   */
  function handleClick(option: string) {
    if (!disabled && onSelectOption(option)) {
      activeOption = option;
    }
  }

  // Updates the position and size of the background slider based on the active option
  function updateBackgroundSlider() {
    const buttonElements = containerElement?.querySelectorAll(".c-toggle-button__button");
    if (!buttonElements) return;

    const activeButton = buttonElements[options.indexOf(activeOption)];
    if (containerElement && backgroundSliderElement && activeButton) {
      const activeRect = activeButton.getBoundingClientRect();
      const containerRect = containerElement.getBoundingClientRect();

      backgroundSliderElement.style.left = `${activeRect.left - containerRect.left}px`;
      backgroundSliderElement.style.width = `${activeRect.width}px`;
      backgroundSliderElement.style.height = `${activeRect.height}px`;
    }
  }

  // Update slider position when active option changes
  $: activeOption && updateBackgroundSlider();

  // Set up resize observer
  let resizeObserver: ResizeObserver | undefined;
  let isResizing = false;
  let resizeTimeout: NodeJS.Timeout;
  $: if (containerElement && !resizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      isResizing = true;
      updateBackgroundSlider();
      clearTimeout(resizeTimeout);

      // Whenever we have not resized for 100ms, we can turn off the resizing state
      resizeTimeout = setTimeout(() => {
        isResizing = false;
      }, 100);
    });
    resizeObserver.observe(containerElement);
  }

  // Clean up resizing on component destruction
  onDestroy(() => {
    resizeObserver?.disconnect();
    resizeObserver = undefined;
    clearTimeout(resizeTimeout);
  });
</script>

<div
  bind:this={containerElement}
  class="c-toggle-button"
  class:c-toggle-button--disabled={disabled}
  class:c-toggle-button--size-1={size === 1}
  class:c-toggle-button--size-2={size === 2}
  class:c-toggle-button--size-3={size === 3}
  class:c-toggle-button--size-4={size === 4}
  class:c-toggle-button--resizing={isResizing}
>
  <div class="background-slider" bind:this={backgroundSliderElement} />
  {#each options as option}
    <BaseButton
      {size}
      {disabled}
      variant="ghost"
      color="neutral"
      on:click={() => handleClick(option)}
      class="c-toggle-button__button {option === activeOption
        ? 'c-toggle-button__button--active'
        : ''}"
    >
      <slot name="option-button-contents" {option} {size}>
        <TextAugment {size}>
          {option}
        </TextAugment>
      </slot>
    </BaseButton>
  {/each}
</div>

<style>
  .c-toggle-button {
    --toggle-button-padding-vertical: var(--ds-spacing-1);
    --toggle-button-padding-horizontal: var(--ds-spacing-2);

    width: 100%;
    position: relative;
    display: inline-flex;
    overflow: hidden;
    border-radius: var(--ds-radius-2);
    background-color: var(--ds-color-neutral-a2);
    padding: calc(0.5 * var(--ds-spacing-1));
    gap: calc(0.5 * var(--ds-spacing-1));

    & > button.c-base-btn.c-toggle-button__button {
      position: relative;
      border-radius: var(--ds-radius-1);
      padding: var(--toggle-button-padding-vertical) var(--toggle-button-padding-horizontal);
      flex: 1;
      min-width: 0;
      display: flex;
      justify-content: center;
    }

    & > .background-slider {
      position: absolute;
      background-color: var(--ds-color-neutral-1);
      transition: all 0.15s ease-in-out;
      border-radius: var(--ds-radius-1);
      box-shadow: inset 0 0 0 1px var(--gray-a5);
    }

    &.c-toggle-button--resizing > .background-slider {
      transition: none;
    }

    /* Size variants */
    &.c-toggle-button--size-1 {
      --toggle-button-padding-vertical: var(--ds-spacing-1);
      --toggle-button-padding-horizontal: var(--ds-spacing-2);
    }

    &.c-toggle-button--size-2 {
      --toggle-button-padding-vertical: var(--ds-spacing-1);
      --toggle-button-padding-horizontal: var(--ds-spacing-2);
    }

    &.c-toggle-button--size-3 {
      --toggle-button-padding-vertical: var(--ds-spacing-1);
      --toggle-button-padding-horizontal: var(--ds-spacing-3);
    }

    &.c-toggle-button--size-4 {
      --toggle-button-padding-vertical: var(--ds-spacing-1);
      --toggle-button-padding-horizontal: var(--ds-spacing-3);
    }
  }
</style>

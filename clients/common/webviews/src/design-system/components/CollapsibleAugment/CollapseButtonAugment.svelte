<script lang="ts">
  import IconButtonAugment from "../IconButtonAugment.svelte";
  import CaretSort from "../../icons/caret-sort.svelte";
  import Collapse from "../../icons/augment/collapse.svelte";
  import { getCollapsibleContext } from "./context";

  const { collapsed, setCollapsed } = getCollapsibleContext();

  function toggleCollapsed() {
    setCollapsed(!$collapsed);
  }
</script>

<IconButtonAugment
  variant="ghost-block"
  color="neutral"
  size={1}
  on:click={toggleCollapsed}
  {...$$restProps}
>
  {#if $collapsed}
    <CaretSort />
  {:else}
    <Collapse />
  {/if}
</IconButtonAugment>

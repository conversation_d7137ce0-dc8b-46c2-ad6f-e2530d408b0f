export function dsColorAttribute(color: string) {
  return {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    "data-ds-color": color,
  };
}

export function dsRadiusAttribute(radius: DesignSystemRadius) {
  return {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    "data-ds-radius": radius,
  };
}

/**
 * Creates a scoped data attribute for design system components.
 *
 * This function creates data attributes with a specific scope prefix,
 * making them more organized and preventing naming collisions.
 *
 * @param scope - The scope/namespace for the attribute (e.g., "dropdown", "button")
 * @param name - The name of the data attribute
 * @param value - The value to set for the attribute (if undefined or false, returns empty object)
 * @returns An object with the scoped data attribute if value is truthy, otherwise an empty object
 *
 * @example
 * // Usage:
 * const props = dsPublicAttribute('dropdown', 'hidden', true);
 * // Result: { 'data-ds-dropdown-hidden': true }
 *
 * const props = dsPublicAttribute('button', 'disabled', false);
 * // Result: {}
 */
export function dsPublicBooleanAttribute(scope: string, name: string, value: boolean | undefined) {
  if (value) {
    return {
      /* eslint-disable @typescript-eslint/naming-convention */
      [`data-ds-${scope}-${name}`]: true, // Scoped data attribute for design system components
      [`data-${name}`]: true, // Public API for radix, available on all components
      /* eslint-enable @typescript-eslint/naming-convention */
    };
  }
  return {};
}

export type DesignSystemRadius = "none" | "small" | "medium" | "large" | "full";

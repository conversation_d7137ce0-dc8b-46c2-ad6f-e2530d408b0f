import { type ButtonColor } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
import {
  type ChangedFile,
  type RemoteAgentExchange,
  RemoteAgentStatus,
  RemoteAgentWorkspaceStatus,
} from "$vscode/src/remote-agent-manager/types";
import {
  ChatRequestNodeType,
  ChatResultNodeType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";

export function getRelativeTimeForStr(dateStr: string): string {
  return getRelativeTime(new Date(dateStr));
}

export function getRelativeTime(date: Date): string {
  try {
    if (isNaN(date.getTime())) {
      return "Unknown time";
    }

    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSecs < 60) {
      return `${diffSecs}s ago`;
    } else if (diffMins < 60) {
      return `${diffMins}m ago`;
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else if (diffDays < 30) {
      return `${diffDays}d ago`;
    } else {
      // Format the date for older entries
      return date.toLocaleDateString();
    }
  } catch (e) {
    console.error("Error formatting date:", e);
    return "Unknown time";
  }
}

/**
 * Starts a relative time updater that will call the setRelativeTime function
 * with the relative time string.  The interval will adjust based on the age
 * of the date.
 *
 * @param dateStr The date string to update
 * @param setRelativeTime The function to call with the relative time string
 * @returns A function to stop the updater
 *
 * Example:
 * let relativeTime = getRelativeTimeForStr(timestamp);
 * const stopUpdater = startRelativeTimeUpdater(timestamp, (time) => {
 *   relativeTime = time;
 * });
 *
 * onDestroy(() => {
 *   stopUpdater();
 * });
 */
export function startRelativeTimeUpdater(
  dateStr: string,
  setRelativeTime: (relativeTime: string) => void,
): () => void {
  let intervalMs = 1000;
  const date = new Date(dateStr);
  const interval = setInterval(() => {
    // if dateStr is more than 1m ago, update every minute
    const diffMins = Math.floor((new Date().getTime() - date.getTime()) / 1000 / 60);
    if (diffMins >= 1) {
      intervalMs = 60 * 1000;
    }
    // if dateStr is more than 1h ago, update every hour
    if (diffMins >= 60) {
      intervalMs = 60 * 60 * 1000;
    }
    // if dateStr is more than 1d ago, update every day
    if (diffMins >= 60 * 24) {
      intervalMs = 24 * 60 * 60 * 1000;
    }
    setRelativeTime(getRelativeTimeForStr(dateStr));
  }, intervalMs);

  return () => clearInterval(interval);
}

/**
 * Starts a countdown timer that will call the setCountdown function
 * with the countdown string.  The interval will adjust based on the time
 * remaining. Usage is similar to startRelativeTimeUpdater.
 *
 * @param retryAt The date to count down to
 * @param setCountdown The function to call with the countdown string
 * @returns A function to stop the updater
 */
export function startCountdown(
  retryAt: Date,
  setCountdown: (countdown: string) => void,
): () => void {
  let intervalMs = 1000;
  const interval = setInterval(() => {
    const diffMs = retryAt.getTime() - Date.now();
    if (diffMs <= 0) {
      clearInterval(interval);
      return;
    }
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSecs < 60) {
      setCountdown(`${diffSecs}s`);
    } else if (diffMins < 60) {
      setCountdown(`${diffMins}m ${diffSecs % 60}s`);
    } else if (diffHours < 24) {
      setCountdown(`${diffHours}h`);
    } else if (diffDays < 30) {
      setCountdown(`${diffDays}d`);
    } else {
      setCountdown(`1mo`);
    }
  }, intervalMs);

  return () => clearInterval(interval);
}

export function getStatusColor(status: RemoteAgentStatus | undefined): ButtonColor {
  if (status === undefined) return "neutral";

  switch (status) {
    case RemoteAgentStatus.agentPending:
    case RemoteAgentStatus.agentStarting:
    case RemoteAgentStatus.agentRunning:
      return "info" as const;
    case RemoteAgentStatus.agentIdle:
      return "success" as const;
    case RemoteAgentStatus.agentFailed:
      return "error" as const;
    default:
      return "neutral";
  }
}

export function getWorkspaceStatusColor(
  status: RemoteAgentWorkspaceStatus | undefined,
): ButtonColor {
  if (status === undefined) return "neutral";

  switch (status) {
    case RemoteAgentWorkspaceStatus.workspaceRunning:
      return "info" as const;
    case RemoteAgentWorkspaceStatus.workspacePausing:
    case RemoteAgentWorkspaceStatus.workspacePaused:
    case RemoteAgentWorkspaceStatus.workspaceResuming:
      return "neutral" as const;
    default:
      return "neutral";
  }
}

export function getStatusText(status: RemoteAgentStatus): string {
  switch (status) {
    case RemoteAgentStatus.agentStarting:
      return "Starting";
    case RemoteAgentStatus.agentRunning:
      return "Running";
    case RemoteAgentStatus.agentIdle:
      return "Idle";
    case RemoteAgentStatus.agentPending:
      return "Pending";
    case RemoteAgentStatus.agentFailed:
      return "Failed";
    default:
      return "Unknown";
  }
}

export function getWorkspaceStatusText(status: RemoteAgentWorkspaceStatus): string {
  switch (status) {
    case RemoteAgentWorkspaceStatus.workspaceRunning:
      return "Running";
    case RemoteAgentWorkspaceStatus.workspacePausing:
      return "Pausing";
    case RemoteAgentWorkspaceStatus.workspacePaused:
      return "Paused";
    case RemoteAgentWorkspaceStatus.workspaceResuming:
      return "Resuming";
    default:
      return "Unknown";
  }
}

/**
 * Gets aggregate changes from a list of changed files.
 * Excludes files that have been changed but reverted to their original state.
 */
const getAggregateChangesFromFiles = (changedFiles: ChangedFile[]): ChangedFile[] => {
  let aggregateChanges: Record<string, ChangedFile> = {};
  for (const file of changedFiles) {
    const existingFile = aggregateChanges[file.new_path];
    if (!existingFile) {
      aggregateChanges[file.new_path] = file;
    } else {
      aggregateChanges[file.new_path] = {
        ...existingFile,
        /* eslint-disable @typescript-eslint/naming-convention */
        new_contents: file.new_contents,
        /* eslint-disable @typescript-eslint/naming-convention */
        new_path: file.new_path,
      };
    }
  }

  // Filter out files that have been reverted to their original state
  const filteredChanges = Object.values(aggregateChanges).filter((file) => {
    // For modified files, check if the content is actually different
    if (file.old_path && file.new_path && file.old_path === file.new_path) {
      return file.old_contents !== file.new_contents;
    }
    // Keep added and deleted files
    return true;
  });

  return filteredChanges;
};

export const getAggregateChanges = (chatHistory: RemoteAgentExchange[]): ChangedFile[] => {
  const changedFiles = chatHistory.flatMap((e) => e.changed_files);
  return getAggregateChangesFromFiles(changedFiles);
};

/**
 * Given a chat history and a turn index, get the most recent user message
 * preceding the turn. If the turn index refers to a user message, returns that message.
 */
export const getUserMessagePrecedingTurn = (
  chatHistory: RemoteAgentExchange[],
  turnIndex: number,
): string => {
  const indexOfLastUserMessage = getIndexOfPrecedingUserMessage(chatHistory, turnIndex);
  return chatHistory[indexOfLastUserMessage]?.exchange.request_message ?? "";
};

export const getIndexOfPrecedingUserMessage = (
  chatHistory: RemoteAgentExchange[],
  turnIndex: number,
): number => {
  // Handle out of bounds indices
  if (turnIndex < 0 || turnIndex >= chatHistory.length) {
    return -1;
  }

  // if the history at turnIndex is a user message, return that index
  if (chatHistory[turnIndex]?.exchange.request_message) {
    return turnIndex;
  }
  return chatHistory.slice(0, turnIndex).findLastIndex((e) => e.exchange.request_message);
};

/**
 * Gets the index of the next user message after the given turn index.
 * Returns undefined if there is no next user message.
 */
export const getIndexOfNextUserMessage = (
  chatHistory: RemoteAgentExchange[],
  turnIndex: number,
): number | undefined => {
  const indexOfNextUserMessageRelativeToTurn = chatHistory
    .slice(turnIndex)
    .findIndex((e) => e.exchange.request_message);
  if (indexOfNextUserMessageRelativeToTurn === -1) {
    return undefined;
  }
  return turnIndex + indexOfNextUserMessageRelativeToTurn;
};

/**
 * Given a turn index, get all exchanges from the last user message to the next user message.
 * A turn index can just be a small chunk of the conversation, so we need to find the
 * surrounding user messages to determine the whole "turn" that the user sees as an Augment message
 */
export const getTurnList = (
  chatHistory: RemoteAgentExchange[],
  turnIndex: number,
): RemoteAgentExchange[] => {
  const indexOfLastUserMessage = getIndexOfPrecedingUserMessage(chatHistory, turnIndex);
  let indexOfNextUserMessage = getIndexOfNextUserMessage(chatHistory, turnIndex);
  if (indexOfNextUserMessage === undefined) {
    indexOfNextUserMessage = chatHistory.length;
  }
  return chatHistory.slice(indexOfLastUserMessage + 1, indexOfNextUserMessage);
};

/**
 * The "turnIndex" refers to a single exchange. But in a single augment message, there can be
 * multiple exchanges. This function gets all the changes for all exchanges in an Augment message
 * that came after the last user message and before the next user message.
 */
export const getAllChangesBetweenUserMessages = (
  chatHistory: RemoteAgentExchange[],
  turnIndex: number,
): ChangedFile[] => {
  // Handle out of bounds indices
  if (turnIndex < 0 || turnIndex >= chatHistory.length) {
    return [];
  }

  const turnList = getTurnList(chatHistory, turnIndex);
  const changedFiles = turnList.flatMap((e) => e.changed_files);
  return getAggregateChangesFromFiles(changedFiles);
};

/**
 * Sometimes we just want changes related to a specific turn. But sometimes the relevant exchanges
 * are spread out over multiple turns. This function helps us find the relevant changes for a given
 * turn.
 */
export const getChangesForTurn = (
  chatHistory: RemoteAgentExchange[],
  turnIndex: number,
): ChangedFile[] => {
  const nextTurnIndex = getIndexOfNextUserMessage(chatHistory, turnIndex);
  const turnsToCheck = chatHistory.slice(turnIndex, nextTurnIndex);

  const toolCallAtTurn = chatHistory[turnIndex].exchange.response_nodes?.find(
    (node) => node.type === ChatResultNodeType.TOOL_USE,
  );
  if (!toolCallAtTurn) {
    return [];
  }

  const toolUseId = toolCallAtTurn.tool_use?.tool_use_id;
  if (!toolUseId) {
    return [];
  }

  // Find the turn that contains the tool result
  const toolResultTurn = turnsToCheck.find((turn) => {
    return turn.exchange.request_nodes?.some((node) => {
      return (
        node.type === ChatRequestNodeType.TOOL_RESULT &&
        node.tool_result_node?.tool_use_id === toolUseId
      );
    });
  });
  if (!toolResultTurn) {
    return [];
  }

  const changedFiles = turnsToCheck.flatMap((e) => e.changed_files);
  return getAggregateChangesFromFiles(changedFiles);
};

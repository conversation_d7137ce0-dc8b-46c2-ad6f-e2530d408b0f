<script lang="ts">
  import ChatInput from "$common-webviews/src/apps/chat/components/ChatInput/ChatInput.svelte";
  import { type ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { getRemoteAgentsAction } from "$common-webviews/src/apps/chat/utils/chat-input-context";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { type CommitRef, type GithubBranch } from "$vscode/src/remote-agent-manager/types";
  import { type SetupScript } from "$vscode/src/utils/remote-agent-setup/types";
  import { RemoteAgentSessionEventName } from "@augment-internal/sidecar-libs/src/metrics/types";
  import { getContext, onDestroy, onMount } from "svelte";
  import { fly } from "svelte/transition";
  import CommitRefSelector from "../../components/CommitRefSelector.svelte";
  import SetupScriptSelector from "../../components/SetupScriptSelector/SetupScriptSelector.svelte";
  import { GitReferenceModel } from "../../models/git-reference-model";
  import { RemoteAgentsModel } from "../../models/remote-agents-model";
  import AgentLimitCard from "./AgentLimitCard.svelte";

  /** Component to display the remote agent setup screen in chat */

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const chatModel = getContext<ChatModel>("chatModel");
  const gitRefModel = getContext<GitReferenceModel>(GitReferenceModel.key);

  $: commitRef = $remoteAgentsModel.newAgentDraft?.commitRef ?? null;
  $: selectedBranch = $remoteAgentsModel.newAgentDraft?.selectedBranch ?? null;
  $: selectedSetupScript = $remoteAgentsModel.newAgentDraft?.setupScript ?? null;

  let commitRefErrorMessage = "";
  let isCommitRefLoading = false;

  // Use the reactive computation for isDisabled
  $: isDisabled = currentIsDisabled;
  $: isCreatingAgent = $remoteAgentsModel.isCreatingAgent;
  let agentLimitErrorMessage: string | undefined;

  // Add variables to store the last used values
  let lastUsedRepoUrl: string | null = null;
  let lastUsedBranch: string | null = null;
  let lastUsedSetupScript: string | null = null;

  // Reactive computation for error message and disabled state
  $: errorMessage = (() => {
    // Check if we have valid repository and branch data
    const hasValidRepo = commitRef?.github_commit_ref?.repository_url;
    const hasValidBranch = selectedBranch?.name;

    // Additional check: ensure we're not in a loading state where branch data might be incomplete
    const isDataComplete = !isCommitRefLoading && hasValidRepo && hasValidBranch;

    return (
      commitRefErrorMessage ||
      agentLimitErrorMessage ||
      (isCommitRefLoading ? "Loading repos and branches..." : "") ||
      (!hasValidRepo && "Please select a repository") ||
      (!hasValidBranch && "Please select a branch") ||
      (!isDataComplete && hasValidRepo && hasValidBranch ? "Loading branch data..." : "") ||
      ""
    );
  })();

  // Reactive computation for the current disabled state
  $: currentIsDisabled = !!errorMessage;

  // Load the last used values when the component is mounted
  onMount(async () => {
    try {
      const lastSetup = await remoteAgentsModel.getLastRemoteAgentSetup();
      lastUsedRepoUrl = lastSetup.lastRemoteAgentGitRepoUrl;
      lastUsedBranch = lastSetup.lastRemoteAgentGitBranch;
      lastUsedSetupScript = lastSetup.lastRemoteAgentSetupScript;
      // Report the setup page opened event
      await remoteAgentsModel.reportRemoteAgentEvent({
        eventName: RemoteAgentSessionEventName.setupPageOpened,
        remoteAgentId: "",
        eventData: {
          setupPageOpened: {},
        },
      });
    } catch (error) {
      console.error("Failed to load last remote agent setup:", error);
    }
  });

  async function handleCommitRefChange(
    event: CustomEvent<{
      commitRef: CommitRef;
      selectedBranch: GithubBranch;
    }>,
  ) {
    remoteAgentsModel.setRemoteAgentCreationError(null);

    const currentDraft = remoteAgentsModel.newAgentDraft;
    if (currentDraft) {
      remoteAgentsModel.setNewAgentDraft({
        ...currentDraft,
        commitRef: event.detail.commitRef,
        selectedBranch: event.detail.selectedBranch,
      });
    } else {
      remoteAgentsModel.setNewAgentDraft({
        commitRef: event.detail.commitRef,
        selectedBranch: event.detail.selectedBranch,
        setupScript: null,
        isDisabled: currentIsDisabled,
        enableNotification: true,
      });
    }
  }

  function handleSetupScriptChange(event: CustomEvent<{ script: SetupScript | null }>) {
    remoteAgentsModel.setRemoteAgentCreationError(null);
    const currentDraft = remoteAgentsModel.newAgentDraft;

    if (currentDraft) {
      remoteAgentsModel.setNewAgentDraft({
        ...currentDraft,
        setupScript: event.detail.script,
      });
    } else {
      remoteAgentsModel.setNewAgentDraft({
        commitRef: null,
        selectedBranch: null,
        setupScript: event.detail.script,
        isDisabled: currentIsDisabled,
        enableNotification: true,
      });
    }
  }

  // Reactively update the draft's isDisabled state when it changes
  $: if (
    remoteAgentsModel.newAgentDraft &&
    !$remoteAgentsModel.isCreatingAgent &&
    remoteAgentsModel.newAgentDraft.isDisabled !== currentIsDisabled
  ) {
    remoteAgentsModel.setNewAgentDraft({
      ...remoteAgentsModel.newAgentDraft,
      isDisabled: currentIsDisabled,
    });
  }

  onDestroy(() => {
    remoteAgentsModel.setNewAgentDraft(null);
    remoteAgentsModel.setCreationMetrics(undefined);
  });

  const createAgent = getRemoteAgentsAction(
    remoteAgentsModel,
    $chatModel.currentConversationModel,
    gitRefModel,
  );

  async function handleCreateAgent() {
    try {
      createAgent();

      remoteAgentsModel.saveLastRemoteAgentSetup(
        commitRef?.github_commit_ref.repository_url || null,
        selectedBranch?.name || null,
        selectedSetupScript?.path || null,
      );
    } catch (error) {
      console.error("Failed to create agent:", error);
    }
  }
</script>

<div class="remote-agent-setup">
  <div class="content">
    <div class="main-description">
      <p>
        Kick off a remote agent to work <strong>in parallel</strong>, in an
        <strong>isolated environment</strong>
        that will keep running, <strong>even when you shut off your laptop</strong>.
      </p>
    </div>

    <div class="form-fields">
      <div class="error-message" transition:fly={{ y: 10 }}>
        <AgentLimitCard bind:agentLimitErrorMessage />
      </div>
      {#if $remoteAgentsModel.remoteAgentCreationError}
        <div class="error-message" transition:fly={{ y: 10 }}>
          <CalloutAugment color="error" variant="soft" size={2}>
            {$remoteAgentsModel.remoteAgentCreationError}
          </CalloutAugment>
        </div>
      {/if}

      <div class="description">Start from any GitHub repo and branch:</div>
      <div class="commit-ref-selector">
        <CommitRefSelector
          on:commitRefChange={handleCommitRefChange}
          {lastUsedRepoUrl}
          lastUsedBranchName={lastUsedBranch}
          bind:errorMessage={commitRefErrorMessage}
          bind:isLoading={isCommitRefLoading}
        />
      </div>

      <div class="description">
        Select a setup script to prepare the remote environment, so the agent can make better
        changes by running scripts, tests, and building your code:
      </div>
      <div class="setup-script">
        <SetupScriptSelector
          on:setupScriptChange={handleSetupScriptChange}
          lastUsedScriptPath={lastUsedSetupScript}
          disableNewAgentCreation={!!agentLimitErrorMessage ||
            !selectedBranch?.name ||
            !commitRef?.github_commit_ref?.repository_url}
        />
      </div>

      <div class="chat">
        <ChatInput editable hasSendButton={false} />
      </div>

      <div class="create-button">
        <TextTooltipAugment
          class="full-width-button"
          content={errorMessage}
          triggerOn={[TooltipTriggerOn.Hover]}
        >
          <ButtonAugment
            variant="solid"
            color="accent"
            size={2}
            loading={isCreatingAgent}
            on:click={handleCreateAgent}
            disabled={isDisabled}
          >
            Create agent
          </ButtonAugment>
        </TextTooltipAugment>
      </div>
    </div>
  </div>
</div>

<style>
  .description {
    padding: var(--ds-spacing-2) var(--remote-agent-setup-horizontal-padding) var(--ds-spacing-2)
      var(--remote-agent-setup-horizontal-padding);
    margin: 0;
    color: var(--ds-color-neutral-11);
  }
  .commit-ref-selector {
    padding: 0 var(--remote-agent-setup-horizontal-padding) var(--ds-spacing-4);
  }

  .remote-agent-setup {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    --remote-agent-setup-horizontal-padding: var(--ds-spacing-1);
  }

  @media (min-width: 300px) {
    .remote-agent-setup {
      --remote-agent-setup-horizontal-padding: var(--ds-spacing-1);
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 0;
  }
  .main-description {
    padding: 0 var(--remote-agent-setup-horizontal-padding);
  }

  .main-description strong {
    font-weight: 600;
    color: var(--ds-color-neutral-12);
  }

  .setup-script {
    margin: 0 var(--remote-agent-setup-horizontal-padding);
  }

  .error-message {
    margin-top: var(--ds-spacing-4);
    margin-bottom: var(--ds-spacing-2);
    margin-left: var(--remote-agent-setup-horizontal-padding);
    margin-right: var(--remote-agent-setup-horizontal-padding);
  }

  .create-button {
    display: flex;
    justify-content: center;
    width: 100%;
    padding: var(--ds-spacing-4) var(--remote-agent-setup-horizontal-padding);
  }

  .create-button :global(.full-width-button) {
    width: 100%;
  }

  .create-button :global(.full-width-button .c-base-btn) {
    width: 100%;
  }

  .chat {
    position: relative;
    margin: var(--ds-spacing-3) var(--remote-agent-setup-horizontal-padding) 0;
    height: 12em;
    transition: all 0.3s ease;
  }
  .create-button :global(.c-base-btn) {
    padding: var(--ds-spacing-1) var(--ds-spacing-3);
  }
</style>

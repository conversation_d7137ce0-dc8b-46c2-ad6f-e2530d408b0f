import { createTwoFilesPatch } from "diff";
import type { Diff } from "$vscode/src/remote-agent-manager/types";

/**
 * Generate a stable ID for a file path and content
 * This ensures we have consistent IDs across requests
 */
function generateStableId(input: string): string {
  // Create a simple hash of the input
  let hash = 0;
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash).toString(36);
}

/**
 * Generate a diff between two versions of a file
 *
 * @param oldPath - The path of the original file
 * @param newPath - The path of the modified file
 * @param oldContent - The content of the original file
 * @param newContent - The content of the modified file
 * @param options - Options for the diff generation
 * @returns A Diff object containing the diff, original code, and modified code
 */
export function generateDiff(
  oldPath: string,
  newPath: string,
  oldContent: string,
  newContent: string,
  options: {
    context?: number;
    generateId?: boolean;
  } = {},
): Diff {
  const { context = 3, generateId = true } = options;

  // Generate the diff using the diff library
  const diffResult = createTwoFilesPatch(oldPath, newPath, oldContent, newContent, "", "", {
    context,
  });

  // Use the path that exists (for new or deleted files)
  const path = newPath || oldPath;

  // Generate a stable ID if requested
  let id: string;
  if (generateId) {
    const pathId = generateStableId(path);
    const contentHash = generateStableId(oldContent + newContent);
    id = `${pathId}-${contentHash}`;
  } else {
    // Use a random ID if stable ID not requested
    id = Math.random().toString(36).substring(2, 15);
  }

  return {
    id,
    path,
    diff: diffResult,
    originalCode: oldContent,
    modifiedCode: newContent,
  };
}

/**
 * Generate diffs for multiple files
 *
 * @param files - Array of file objects with old and new content
 * @param options - Options for the diff generation
 * @returns Array of Diff objects
 */
export function generateDiffs(
  files: Array<{
    oldPath: string;
    newPath: string;
    oldContent: string;
    newContent: string;
  }>,
  options: {
    context?: number;
    generateId?: boolean;
  } = {},
): Diff[] {
  return files.map((file) =>
    generateDiff(file.oldPath, file.newPath, file.oldContent, file.newContent, options),
  );
}

/**
 * Calculate statistics for a diff
 *
 * @param diff - The diff string to analyze
 * @returns Object containing the number of additions and deletions
 */
export function getDiffStats(diff: string): { additions: number; deletions: number } {
  const lines = diff.split("\n");
  const additions = lines.filter((line) => line.startsWith("+")).length;
  const deletions = lines.filter((line) => line.startsWith("-")).length;

  return { additions, deletions };
}

/**
 * Check if a file is new (added) based on its diff
 *
 * @param diff - The diff object to analyze
 * @returns Boolean indicating if the file is new
 */
export function isNewFile(diff: Diff): boolean {
  return !diff.originalCode || diff.originalCode.trim() === "";
}

/**
 * Check if a file is deleted based on its diff
 *
 * @param diff - The diff object to analyze
 * @returns Boolean indicating if the file is deleted
 */
export function isDeletedFile(diff: Diff): boolean {
  return !diff.modifiedCode || diff.modifiedCode.trim() === "";
}

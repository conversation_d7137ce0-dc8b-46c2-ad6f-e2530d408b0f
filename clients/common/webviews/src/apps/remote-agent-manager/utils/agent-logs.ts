import {
  RemoteWorkspaceSetupStepStatus,
  type RemoteWorkspaceSetupStatus,
  type RemoteWorkspaceSetupStep,
} from "$vscode/src/remote-agent-manager/types";

/**
 * Merges steps with the same step number and deduplicates log lines
 * @param logs The raw agent setup logs, the steps must be sorted by step number and sequence ID
 * @returns Merged logs with unique steps and deduplicated log lines
 */
export function mergeAgentSetupLogs(logs: RemoteWorkspaceSetupStatus): RemoteWorkspaceSetupStatus {
  const steps = logs.steps.reduce((acc, step) => {
    const prevStep = acc[acc.length - 1];
    if (prevStep && prevStep.step_number === step.step_number) {
      // If the step number is the same, we want to update the log and status
      if (prevStep.status !== RemoteWorkspaceSetupStepStatus.success) {
        // If the previous step has already succeeded, we don't want to overwrite its status
        prevStep.status = step.status;
      }
      if (prevStep.step_number === 0) {
        // Specifically for step 0 (container setup status) we want to overwrite the log
        prevStep.logs = step.logs;
      } else if (prevStep.sequence_id < step.sequence_id) {
        // If the sequence ID is different, we want to append the log
        prevStep.logs += `\n${step.logs}`;
        prevStep.sequence_id = step.sequence_id;
      }
    } else {
      // If the step number is different, we want to add the new step
      acc.push(step);
    }
    return acc;
  }, [] as RemoteWorkspaceSetupStep[]);

  return { steps };
}

/**
 * Combines and sorts the logs by step number and sequence ID
 */
export function sortByStepAndSequenceNumber(
  existingLogs: RemoteWorkspaceSetupStatus,
  newLogs: RemoteWorkspaceSetupStatus,
): RemoteWorkspaceSetupStatus {
  return {
    steps: [...existingLogs.steps, ...newLogs.steps].sort((a, b) => {
      if (a.step_number !== b.step_number) {
        return a.step_number - b.step_number;
      }
      return a.sequence_id - b.sequence_id;
    }),
  };
}

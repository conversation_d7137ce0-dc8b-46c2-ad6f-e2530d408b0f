import { type GithubRepo, type GithubBranch } from "$vscode/src/remote-agent-manager/types";
import { type GitBranch } from "$vscode/src/remote-agent-manager/commit-ref-types";

export function searchBranches(branches: GithubBranch[], value: string) {
  return branches.filter((branch) => branch.name.includes(value.toLowerCase()));
}

export function searchRepos(repos: GithubRepo[], value: string) {
  return repos.filter(
    (repo) => repo.name.includes(value.toLowerCase()) || repo.owner.includes(value.toLowerCase()),
  );
}

export function parseRemoteUrl(remoteUrl: string): { owner: string; name: string } | undefined {
  // Match GitHub URLs in the format github.com/owner/name[.git][/...]
  // This regex captures the owner and repository name, handling repository names with periods
  const match = remoteUrl.match(/github\.com\/([^/]+)\/([^/]+?)(?:\.git|\/|$)/);
  if (!match) {
    return undefined;
  }
  return { owner: match[1], name: match[2] };
}

export function removeOriginPrefix(branchName: string): string {
  return branchName.replace(/^origin\//, "");
}

/**
 * Filters out the current and default branch if they are the same. Also filters out any branches that are not remote.
 */
export function filterLocalGitBranches(branches: GitBranch[]): GitBranch[] {
  const currentUserBranch = branches.find((b) => b.isCurrentBranch);
  const defaultBranch = branches.find((b) => b.isDefault);

  // If the current branch is the default branch, then we end up with duplicate entries. We should filter out the default
  // branch in this case, because the current branch was needed for the "Current Workspace" option.
  const isCurrentBranchDefault =
    !!currentUserBranch && currentUserBranch?.name === defaultBranch?.name.replace("origin/", "");

  const filteredBranches = branches.filter((b) => {
    if (isCurrentBranchDefault && b.isDefault) {
      return false;
    }
    if (b.isCurrentBranch && b.isRemote) {
      return false;
    }
    if (!b.isRemote) {
      return false;
    }
    return b.isRemote;
  });

  return filteredBranches;
}

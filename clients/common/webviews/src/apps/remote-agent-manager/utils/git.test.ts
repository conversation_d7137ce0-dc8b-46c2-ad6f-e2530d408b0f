import { describe, expect, it } from "vitest";
import {
  searchBranches,
  searchRepos,
  parseRemoteUrl,
  removeOriginPrefix,
  filterLocalGitBranches,
} from "./git";
import type { GithubBranch, GithubRepo } from "$vscode/src/remote-agent-manager/types";
import type { GitBranch } from "$vscode/src/remote-agent-manager/commit-ref-types";

describe("git utils", () => {
  describe("searchBranches", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const mockBranches: GithubBranch[] = [
      {
        name: "main",
        commit: { sha: "abc123", url: "https://github.com/repo/commit/abc123" },
        protected: true,
      },
      {
        name: "feature/user-auth",
        commit: { sha: "def456", url: "https://github.com/repo/commit/def456" },
        protected: false,
      },
      {
        name: "feature/payment-api",
        commit: { sha: "ghi789", url: "https://github.com/repo/commit/ghi789" },
        protected: false,
      },
      {
        name: "bugfix/login-issue",
        commit: { sha: "jkl012", url: "https://github.com/repo/commit/jkl012" },
        protected: false,
      },
    ];
    /* eslint-enable @typescript-eslint/naming-convention */

    it("should return branches that include the given value", () => {
      const result = searchBranches(mockBranches, "feature");
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe("feature/user-auth");
      expect(result[1].name).toBe("feature/payment-api");
    });

    it("should return an empty array if no branches match", () => {
      const result = searchBranches(mockBranches, "nonexistent");
      expect(result).toHaveLength(0);
    });

    it("should return all branches if search value is empty", () => {
      const result = searchBranches(mockBranches, "");
      expect(result).toHaveLength(4);
    });

    it("should be case insensitive", () => {
      const result = searchBranches(mockBranches, "Feature");
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe("feature/user-auth");
      expect(result[1].name).toBe("feature/payment-api");
    });
  });

  describe("searchRepos", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const mockRepos: GithubRepo[] = [
      {
        owner: "augmentcode",
        name: "augment",
        html_url: "https://github.com/augmentcode/augment",
        created_at: "2022-01-01T00:00:00Z",
        updated_at: "2022-02-01T00:00:00Z",
        default_branch: "main",
      },
      {
        owner: "augmentcode",
        name: "augment-cli",
        html_url: "https://github.com/augmentcode/augment-cli",
        created_at: "2022-01-15T00:00:00Z",
        updated_at: "2022-02-15T00:00:00Z",
        default_branch: "main",
      },
      {
        owner: "augmentcode",
        name: "augment-docs",
        html_url: "https://github.com/augmentcode/augment-docs",
        created_at: "2022-01-20T00:00:00Z",
        updated_at: "2022-02-20T00:00:00Z",
        default_branch: "main",
      },
      {
        owner: "augmentcode",
        name: "demo-repo",
        html_url: "https://github.com/augmentcode/demo-repo",
        created_at: "2022-01-25T00:00:00Z",
        updated_at: "2022-02-25T00:00:00Z",
        default_branch: "main",
      },
    ];
    /* eslint-enable @typescript-eslint/naming-convention */

    it("should return repos that include the given value", () => {
      const result = searchRepos(mockRepos, "augment-");
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe("augment-cli");
      expect(result[1].name).toBe("augment-docs");
    });

    it("should return an empty array if no repos match", () => {
      const result = searchRepos(mockRepos, "nonexistent");
      expect(result).toHaveLength(0);
    });

    it("should return all repos if search value is empty", () => {
      const result = searchRepos(mockRepos, "");
      expect(result).toHaveLength(4);
    });

    it("should be case insensitive", () => {
      const result = searchRepos(mockRepos, "Augment-");
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe("augment-cli");
      expect(result[1].name).toBe("augment-docs");
    });
  });

  describe("parseRemoteUrl", () => {
    it("should parse a standard GitHub URL", () => {
      const result = parseRemoteUrl("https://github.com/augmentcode/augment.git");
      expect(result).toEqual({ owner: "augmentcode", name: "augment" });
    });

    it("should parse a GitHub URL without .git extension", () => {
      const result = parseRemoteUrl("https://github.com/augmentcode/augment");
      expect(result).toEqual({ owner: "augmentcode", name: "augment" });
    });

    it("should parse a GitHub URL with additional path segments", () => {
      const result = parseRemoteUrl("https://github.com/augmentcode/augment/tree/main");
      expect(result).toEqual({ owner: "augmentcode", name: "augment" });
    });

    it("should parse a GitHub URL with a period in the name", () => {
      const result = parseRemoteUrl("https://github.com/augmentcode/augment.vim.git");
      expect(result).toEqual({ owner: "augmentcode", name: "augment.vim" });
    });

    it("should handle SSH URLs", () => {
      const result = parseRemoteUrl("**************:augmentcode/augment.git");
      expect(result).toBeUndefined();
    });

    it("should return undefined for non-GitHub URLs", () => {
      const result = parseRemoteUrl("https://gitlab.com/augmentcode/augment.git");
      expect(result).toBeUndefined();
    });

    it("should return undefined for invalid URLs", () => {
      const result = parseRemoteUrl("not-a-url");
      expect(result).toBeUndefined();
    });

    it("should return undefined for empty strings", () => {
      const result = parseRemoteUrl("");
      expect(result).toBeUndefined();
    });
  });

  describe("removeOriginPrefix", () => {
    it("should remove 'origin/' prefix from branch name", () => {
      const result = removeOriginPrefix("origin/main");
      expect(result).toBe("main");
    });

    it("should not modify branch name without 'origin/' prefix", () => {
      const result = removeOriginPrefix("feature/branch");
      expect(result).toBe("feature/branch");
    });

    it("should handle empty string", () => {
      const result = removeOriginPrefix("");
      expect(result).toBe("");
    });

    it("should only remove prefix at the beginning", () => {
      const result = removeOriginPrefix("feature/origin/branch");
      expect(result).toBe("feature/origin/branch");
    });
  });

  describe("filterLocalGitBranches", () => {
    const createMockBranches = (): GitBranch[] => [
      {
        name: "main",
        isRemote: false,
        isCurrentBranch: true,
        isDefault: false,
      },
      {
        name: "origin/main",
        isRemote: true,
        isCurrentBranch: false,
        isDefault: true,
      },
      {
        name: "origin/feature",
        isRemote: true,
        isCurrentBranch: false,
        isDefault: false,
      },
      {
        name: "feature",
        isRemote: false,
        isCurrentBranch: false,
        isDefault: false,
      },
      {
        name: "origin/bugfix",
        isRemote: true,
        isCurrentBranch: false,
        isDefault: false,
      },
    ];

    it("should filter out non-remote branches", () => {
      const branches = createMockBranches();
      const result = filterLocalGitBranches(branches);

      // There are 3 remote branches in the mock data, but one is filtered out because
      // it's the default branch and the current branch is the default branch
      expect(result).toHaveLength(2);
      expect(result.every((b) => b.isRemote)).toBe(true);
      // Verify that the default branch (origin/main) is filtered out
      expect(result.some((b) => b.name === "origin/main")).toBe(false);
    });

    it("should filter out default branch when current branch is default", () => {
      const branches: GitBranch[] = [
        {
          name: "main",
          isRemote: false,
          isCurrentBranch: true,
          isDefault: false,
        },
        {
          name: "origin/main",
          isRemote: true,
          isCurrentBranch: false,
          isDefault: true,
        },
        {
          name: "origin/feature",
          isRemote: true,
          isCurrentBranch: false,
          isDefault: false,
        },
      ];

      const result = filterLocalGitBranches(branches);

      expect(result).toHaveLength(1);
      expect(result[0].name).toBe("origin/feature");
    });

    it("should not filter out default branch when current branch is different", () => {
      const branches: GitBranch[] = [
        {
          name: "feature",
          isRemote: false,
          isCurrentBranch: true,
          isDefault: false,
        },
        {
          name: "origin/main",
          isRemote: true,
          isCurrentBranch: false,
          isDefault: true,
        },
        {
          name: "origin/feature",
          isRemote: true,
          isCurrentBranch: false,
          isDefault: false,
        },
      ];

      const result = filterLocalGitBranches(branches);

      expect(result).toHaveLength(2);
      expect(result.some((b) => b.isDefault)).toBe(true);
    });

    it("should handle empty array", () => {
      const result = filterLocalGitBranches([]);
      expect(result).toHaveLength(0);
    });

    it("should handle branches without current or default flags", () => {
      const branches: GitBranch[] = [
        {
          name: "origin/main",
          isRemote: true,
        },
        {
          name: "origin/feature",
          isRemote: true,
        },
        {
          name: "local-branch",
          isRemote: false,
        },
      ];

      const result = filterLocalGitBranches(branches);

      expect(result).toHaveLength(2);
      expect(result.every((b) => b.isRemote)).toBe(true);
    });
  });
});

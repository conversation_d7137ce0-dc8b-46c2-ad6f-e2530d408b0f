import { describe, test, expect } from "vitest";
import { mergeRemoteAgentChatHistory } from "./agent-history";
import { type RemoteAgentExchange } from "$vscode/src/remote-agent-manager/types";

describe("mergeRemoteAgentChatHistory", () => {
  test("should merge chat histories when both are empty", () => {
    const existingHistory: RemoteAgentExchange[] = [];
    const newHistory: RemoteAgentExchange[] = [];

    const result = mergeRemoteAgentChatHistory(existingHistory, newHistory);

    expect(result).toEqual([]);
  });

  test("should return new history when existing history is empty", () => {
    const existingHistory: RemoteAgentExchange[] = [];
    const newHistory: RemoteAgentExchange[] = [
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "1", request_message: "Hello", response_text: "Hi" },
        sequence_id: 1,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "2", request_message: "How are you?", response_text: "Good" },
        sequence_id: 2,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ];

    const result = mergeRemoteAgentChatHistory(existingHistory, newHistory);

    expect(result).toEqual(newHistory);
  });

  test("should return existing history when new history is empty", () => {
    const existingHistory: RemoteAgentExchange[] = [
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "1", request_message: "Hello", response_text: "Hi" },
        sequence_id: 1,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "2", request_message: "How are you?", response_text: "Good" },
        sequence_id: 2,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ];
    const newHistory: RemoteAgentExchange[] = [];

    const result = mergeRemoteAgentChatHistory(existingHistory, newHistory);

    expect(result).toEqual(existingHistory);
  });

  test("should merge histories with non-overlapping sequence IDs", () => {
    const existingHistory: RemoteAgentExchange[] = [
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "1", request_message: "Hello", response_text: "Hi" },
        sequence_id: 1,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "2", request_message: "How are you?", response_text: "Good" },
        sequence_id: 2,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ];

    const newHistory: RemoteAgentExchange[] = [
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "3",
          request_message: "What's your name?",
          response_text: "I'm an AI",
        },
        sequence_id: 3,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "4", request_message: "Thanks", response_text: "You're welcome" },
        sequence_id: 4,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ];

    const result = mergeRemoteAgentChatHistory(existingHistory, newHistory);

    expect(result).toEqual([
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "1", request_message: "Hello", response_text: "Hi" },
        sequence_id: 1,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "2", request_message: "How are you?", response_text: "Good" },
        sequence_id: 2,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "3",
          request_message: "What's your name?",
          response_text: "I'm an AI",
        },
        sequence_id: 3,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "4", request_message: "Thanks", response_text: "You're welcome" },
        sequence_id: 4,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ]);
  });

  test("should replace existing exchanges with same sequence IDs from new history", () => {
    const existingHistory: RemoteAgentExchange[] = [
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "1", request_message: "Hello", response_text: "Hi" },
        sequence_id: 1,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "2", request_message: "How are you?", response_text: "Good" },
        sequence_id: 2,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "3",
          request_message: "What's your name?",
          response_text: "I'm an AI",
        },
        sequence_id: 3,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ];

    const newHistory: RemoteAgentExchange[] = [
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "4",
          request_message: "How are you?",
          response_text: "I'm doing great!",
        },
        sequence_id: 2,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "5", request_message: "Thanks", response_text: "You're welcome" },
        sequence_id: 4,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ];

    const result = mergeRemoteAgentChatHistory(existingHistory, newHistory);

    expect(result).toEqual([
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "1", request_message: "Hello", response_text: "Hi" },
        sequence_id: 1,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "4",
          request_message: "How are you?",
          response_text: "I'm doing great!",
        },
        sequence_id: 2,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "3",
          request_message: "What's your name?",
          response_text: "I'm an AI",
        },
        sequence_id: 3,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "5", request_message: "Thanks", response_text: "You're welcome" },
        sequence_id: 4,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ]);
  });

  test("should handle exchanges with undefined sequence IDs", () => {
    const existingHistory: RemoteAgentExchange[] = [
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "1", request_message: "Hello", response_text: "Hi" },
        sequence_id: undefined,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "2", request_message: "How are you?", response_text: "Good" },
        sequence_id: 2,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ];

    const newHistory: RemoteAgentExchange[] = [
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "3",
          request_message: "What's your name?",
          response_text: "I'm an AI",
        },
        sequence_id: undefined,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "4", request_message: "Thanks", response_text: "You're welcome" },
        sequence_id: 3,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ];

    const result = mergeRemoteAgentChatHistory(existingHistory, newHistory);

    // Should skip the undefined sequence IDs and only merge the defined ones
    expect(result).toEqual([
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "2", request_message: "How are you?", response_text: "Good" },
        sequence_id: 2,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "4", request_message: "Thanks", response_text: "You're welcome" },
        sequence_id: 3,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ]);
  });

  test("should handle complex merge with mixed sequence IDs", () => {
    const existingHistory: RemoteAgentExchange[] = [
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "1", request_message: "Hello", response_text: "Hi" },
        sequence_id: 1,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "2", request_message: "How are you?", response_text: "Good" },
        sequence_id: 3,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "3",
          request_message: "What's your name?",
          response_text: "I'm an AI",
        },
        sequence_id: 5,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ];

    const newHistory: RemoteAgentExchange[] = [
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "4",
          request_message: "New message 1",
          response_text: "New response 1",
        },
        sequence_id: 2,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "5",
          request_message: "New message 2",
          response_text: "New response 2",
        },
        sequence_id: 3,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "6",
          request_message: "New message 3",
          response_text: "New response 3",
        },
        sequence_id: 4,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "7",
          request_message: "New message 4",
          response_text: "New response 4",
        },
        sequence_id: 6,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ];

    const result = mergeRemoteAgentChatHistory(existingHistory, newHistory);

    expect(result).toEqual([
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: { request_id: "1", request_message: "Hello", response_text: "Hi" },
        sequence_id: 1,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "4",
          request_message: "New message 1",
          response_text: "New response 1",
        },
        sequence_id: 2,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "5",
          request_message: "New message 2",
          response_text: "New response 2",
        },
        sequence_id: 3,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "6",
          request_message: "New message 3",
          response_text: "New response 3",
        },
        sequence_id: 4,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "3",
          request_message: "What's your name?",
          response_text: "I'm an AI",
        },
        sequence_id: 5,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        exchange: {
          request_id: "7",
          request_message: "New message 4",
          response_text: "New response 4",
        },
        sequence_id: 6,
        changed_files: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ]);
  });
});

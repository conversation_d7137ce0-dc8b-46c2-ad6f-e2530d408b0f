export const IMAGE_EXTENSIONS = ["png", "jpg", "jpeg", "gif", "svg", "webp", "bmp", "ico"];
export const COMMON_BINARY_EXTENSIONS = [
  // Archives
  "zip",
  "tar",
  "gz",
  "7z",
  "rar",
  // Documents
  "pdf",
  "doc",
  "docx",
  "ppt",
  "pptx",
  "xls",
  "xlsx",
  "odt",
  "odp",
  "ods",
  // Executables & Libraries
  "exe",
  "dll",
  "so",
  "dylib",
  "app",
  "msi",
  "deb",
  "rpm",
  // Compiled code / Intermediates
  "o",
  "a",
  "class",
  "jar",
  "pyc",
  "wasm",
  // Media (non-image, though some images might be listed if not handled by IMAGE_EXTENSIONS first)
  "mp3",
  "mp4",
  "avi",
  "mov",
  "wav",
  "mkv",
  // Other
  "DS_Store",
  "db",
  "sqlite",
  "dat",
];

// 1MB threshold for diff display
export const MAX_FILE_SIZE_FOR_DIFF_BYTES = 1024 * 1024;

export function getFileExtension(filePath: string): string {
  if (!filePath) return "";
  const lastDot = filePath.lastIndexOf(".");
  if (lastDot === -1 || lastDot === filePath.length - 1) return "";
  return filePath.substring(lastDot + 1).toLowerCase();
}

export function getMimeType(filePath: string): string {
  const extension = getFileExtension(filePath);
  switch (extension) {
    case "png":
      return "image/png";
    case "jpg":
    case "jpeg":
      return "image/jpeg";
    case "gif":
      return "image/gif";
    case "svg":
      return "image/svg+xml";
    case "webp":
      return "image/webp";
    case "bmp":
      return "image/bmp";
    case "ico":
      return "image/x-icon";
    // Add more common image types if needed
    default:
      return "application/octet-stream"; // Generic binary type
  }
}

export function isImage(filePath: string): boolean {
  const extension = getFileExtension(filePath);
  return IMAGE_EXTENSIONS.includes(extension);
}

export function isKnownBinary(filePath: string): boolean {
  if (isImage(filePath)) {
    return false; // Images are handled separately
  }
  const extension = getFileExtension(filePath);
  return COMMON_BINARY_EXTENSIONS.includes(extension);
}

export function isTooLargeForDiff(contentLength: number): boolean {
  return contentLength > MAX_FILE_SIZE_FOR_DIFF_BYTES;
}

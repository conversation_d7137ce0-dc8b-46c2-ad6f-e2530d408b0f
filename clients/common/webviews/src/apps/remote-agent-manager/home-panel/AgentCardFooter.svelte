<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { onDestroy } from "svelte";
  import { getRelativeTimeForStr, startRelativeTimeUpdater } from "../utils";
  import { RemoteAgentStatus } from "$vscode/src/remote-agent-manager/types";

  export let isRemote: boolean = false;
  export let status: RemoteAgentStatus;
  export let timestamp: string;

  let relativeTime = getRelativeTimeForStr(timestamp);
  const stopUpdater = startRelativeTimeUpdater(timestamp, (time) => {
    relativeTime = time;
  });

  onDestroy(() => {
    stopUpdater();
  });
</script>

<div class="agent-card-footer">
  <TextAugment size={1} color="secondary" class="location-text">
    {isRemote ? "Running in the cloud" : "Running locally"}
  </TextAugment>

  <div class="time-container">
    <TextAugment size={1} color="secondary" class="time-text">
      {status === RemoteAgentStatus.agentRunning ? "Last updated" : "Started"}
      {#if timestamp}
        {relativeTime}
      {:else}
        Unknown time
      {/if}
    </TextAugment>
  </div>
</div>

<style>
  .agent-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--ds-spacing-2) var(--ds-spacing-4) 0;
    border-top: 1px solid var(--ds-color-neutral-a3);
    margin-left: calc(0px - var(--ds-spacing-4));
    margin-bottom: calc(0px - var(--ds-spacing-1));
    width: calc(100% + var(--ds-spacing-4) * 2);
  }
</style>

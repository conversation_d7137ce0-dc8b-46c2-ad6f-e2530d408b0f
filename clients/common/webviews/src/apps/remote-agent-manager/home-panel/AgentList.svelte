<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import {
    type RemoteAgent,
    RemoteAgentStatus,
    RemoteAgentWorkspaceStatus,
  } from "$vscode/src/remote-agent-manager/types";
  import {
    SHARED_AGENT_STORE_CONTEXT_KEY,
    type ChatHomeWebviewState,
  } from "$vscode/src/webview-panels/remote-agents/common-webview-store";
  import { getContext } from "svelte";
  import { type SharedWebviewStoreModel } from "../../../common/models/shared-webview-store-model";
  import { sortOverviews } from "../utils/index";
  import AgentCard from "./AgentCard.svelte";
  import SectionHeader from "./SectionHeader.svelte";

  const sharedWebviewStore = getContext<SharedWebviewStoreModel<ChatHomeWebviewState>>(
    SHARED_AGENT_STORE_CONTEXT_KEY,
  );

  function selectAgent(agentId: string) {
    sharedWebviewStore.update((state) => {
      if (!state) return;
      return {
        ...state,
        selectedAgentId: agentId,
      };
    });
  }

  $: sortedOverviews = sortOverviews($sharedWebviewStore.state?.agentOverviews || []);
  $: pinnedAgents = $sharedWebviewStore.state?.pinnedAgents || {};

  $: groupedAgents = sortedOverviews.reduce(
    (acc, agent) => {
      if (pinnedAgents?.[agent.remote_agent_id] === true) {
        acc.pinned.push(agent);
      } else if (agent.status === RemoteAgentStatus.agentIdle && agent.has_updates) {
        acc.readyToReview.push(agent);
      } else if (
        agent.status === RemoteAgentStatus.agentRunning ||
        agent.status === RemoteAgentStatus.agentStarting ||
        agent.workspace_status === RemoteAgentWorkspaceStatus.workspaceResuming
      ) {
        acc.running.push(agent);
      } else if (agent.status === RemoteAgentStatus.agentFailed) {
        acc.failed.push(agent);
      } else if (
        agent.status === RemoteAgentStatus.agentIdle ||
        agent.workspace_status === RemoteAgentWorkspaceStatus.workspacePaused ||
        agent.workspace_status === RemoteAgentWorkspaceStatus.workspacePausing
      ) {
        acc.idle.push(agent);
      } else {
        acc.additional.push(agent);
      }
      return acc;
    },
    {
      pinned: [] as RemoteAgent[],
      readyToReview: [] as RemoteAgent[],
      running: [] as RemoteAgent[],
      idle: [] as RemoteAgent[],
      failed: [] as RemoteAgent[],
      additional: [] as RemoteAgent[],
    },
  );
</script>

<div class="agent-list">
  {#if $sharedWebviewStore.state?.agentOverviews.length === 0}
    <div class="empty-state">
      <TextAugment size={3} color="secondary">No agents available</TextAugment>
    </div>
  {:else}
    {#if groupedAgents.pinned.length > 0}
      <SectionHeader title="Pinned" />
      <div class="agent-grid">
        {#each groupedAgents.pinned as agent, index (agent.remote_agent_id + index)}
          <AgentCard
            {agent}
            selected={agent.remote_agent_id === $sharedWebviewStore.state?.selectedAgentId}
            onSelect={selectAgent}
          />
        {/each}
      </div>
    {/if}

    {#if groupedAgents.readyToReview.length > 0}
      <SectionHeader title="Ready to review" />
      <div class="agent-grid">
        {#each groupedAgents.readyToReview as agent, index (agent.remote_agent_id + index)}
          <AgentCard
            {agent}
            selected={agent.remote_agent_id === $sharedWebviewStore.state?.selectedAgentId}
            onSelect={selectAgent}
          />
        {/each}
      </div>
    {/if}

    {#if groupedAgents.running.length > 0}
      <SectionHeader title="Running agents" />
      <div class="agent-grid">
        {#each groupedAgents.running as agent, index (agent.remote_agent_id + index)}
          <AgentCard
            {agent}
            selected={agent.remote_agent_id === $sharedWebviewStore.state?.selectedAgentId}
            onSelect={selectAgent}
          />
        {/each}
      </div>
    {/if}

    {#if groupedAgents.idle.length > 0}
      <SectionHeader title="Idle agents" />
      <div class="agent-grid">
        {#each groupedAgents.idle as agent, index (agent.remote_agent_id + index)}
          <AgentCard
            {agent}
            selected={agent.remote_agent_id === $sharedWebviewStore.state?.selectedAgentId}
            onSelect={selectAgent}
          />
        {/each}
      </div>
    {/if}

    {#if groupedAgents.failed.length > 0}
      <SectionHeader title="Failed agents" />
      <div class="agent-grid">
        {#each groupedAgents.failed as agent, index (agent.remote_agent_id + index)}
          <AgentCard
            {agent}
            selected={agent.remote_agent_id === $sharedWebviewStore.state?.selectedAgentId}
            onSelect={selectAgent}
          />
        {/each}
      </div>
    {/if}

    {#if groupedAgents.additional.length > 0}
      <SectionHeader title="Other agents" />
      <div class="agent-grid">
        {#each groupedAgents.additional as agent, index (agent.remote_agent_id + index)}
          <AgentCard
            {agent}
            selected={agent.remote_agent_id === $sharedWebviewStore.state?.selectedAgentId}
            onSelect={selectAgent}
          />
        {/each}
      </div>
    {/if}
  {/if}
</div>

<style>
  .agent-list {
    padding: 1rem;
  }

  .agent-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--ds-spacing-3);
    margin-bottom: 2rem;
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    border-radius: 8px;
    background-color: var(--ds-color-neutral-a2);
  }
</style>

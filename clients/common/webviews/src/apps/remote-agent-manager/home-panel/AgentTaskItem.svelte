<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  export let text: string;
  export let status: "success" | "warning" | "error" | "info" = "info";

  function getStatusColorClass(status: string): string {
    switch (status) {
      case "success":
        return "task-success";
      case "warning":
        return "task-warning";
      case "error":
        return "task-error";
      case "info":
      default:
        return "task-info";
    }
  }

  $: statusColorClass = getStatusColorClass(status);
</script>

<div class="task-item">
  <div class="bullet-point {statusColorClass}"></div>
  <TextTooltipAugment content={text} triggerOn={[TooltipTriggerOn.Hover]} maxWidth="400px">
    <div class="task-text-container">
      <TextAugment size={1} color="secondary">{text}</TextAugment>
    </div>
  </TextTooltipAugment>

  {#if status === "error" || status === "warning"}
    <div class="task-status-indicator">
      <TextAugment size={1} color={status === "error" ? "error" : "neutral"}>
        {status === "error" ? "!" : status === "warning" ? "⚠" : ""}
      </TextAugment>
    </div>
  {/if}
</div>

<style>
  .task-item {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 0.25rem 0;
  }

  .bullet-point {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-top: 0.25rem;
    flex-shrink: 0;
  }

  .task-success {
    background-color: var(--ds-color-success-9);
  }

  .task-warning {
    background-color: var(--ds-color-warning-9);
  }

  .task-error {
    background-color: var(--ds-color-error-9);
  }

  .task-info {
    background-color: var(--ds-color-accent-9);
  }

  .task-text-container {
    flex: 1;
    overflow: hidden;
  }

  .task-text-container :global(.c-text) {
    display: -webkit-box;
    -webkit-line-clamp: 1; /* Limit to 1 line */
    line-clamp: 1; /* Standard property for compatibility */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .task-status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.25rem;
  }
</style>

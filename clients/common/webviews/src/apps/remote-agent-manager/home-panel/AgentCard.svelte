<script lang="ts">
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import Terminal from "$common-webviews/src/design-system/icons/augment/terminal.svelte";
  import Trash from "$common-webviews/src/design-system/icons/trash.svelte";
  import ThumbtackIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/thumbtack.svg?component";
  import ThumbtackSlashIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/thumbtack-slash.svg?component";
  import type { RemoteAgent } from "$vscode/src/remote-agent-manager/types";
  import { RemoteAgentStatus } from "$vscode/src/remote-agent-manager/types";
  import { getContext } from "svelte";
  import { RemoteAgentsClient } from "../models/remote-agents-client";
  import {
    SHARED_AGENT_STORE_CONTEXT_KEY,
    type ChatHomeWebviewState,
  } from "$vscode/src/webview-panels/remote-agents/common-webview-store";
  import { type SharedWebviewStoreModel } from "../../../common/models/shared-webview-store-model";
  import AgentCardFooter from "./AgentCardFooter.svelte";
  import AgentTaskItem from "./AgentTaskItem.svelte";
  import StatusIndicator from "$common-webviews/src/apps/remote-agent-manager/components/StatusIndicator.svelte";

  export let agent: RemoteAgent;
  export let selected: boolean = false;
  export let onSelect: (agentId: string) => void;

  const remoteAgentsClient = getContext<RemoteAgentsClient>(RemoteAgentsClient.key);
  const sharedWebviewStore = getContext<SharedWebviewStoreModel<ChatHomeWebviewState>>(
    SHARED_AGENT_STORE_CONTEXT_KEY,
  );
  const onSsh = async (agent: RemoteAgent) => {
    return await remoteAgentsClient.sshToRemoteAgent(agent.remote_agent_id);
  };

  async function deleteAgent(agentId: string) {
    await remoteAgentsClient.deleteRemoteAgent(agentId);
  }

  async function togglePinned(agentId: string) {
    try {
      if (isPinned) {
        // Also remove from the global store
        await remoteAgentsClient.deletePinnedAgentFromStore(agentId);
      } else {
        // Save to the global store
        await remoteAgentsClient.savePinnedAgentToStore(agentId, true);
      }

      const pinnedAgents = await remoteAgentsClient.getPinnedAgentsFromStore();

      // Update the shared store with the returned pinnedAgents
      sharedWebviewStore.update((state) => {
        if (!state) return;
        return {
          ...state,
          pinnedAgents,
        };
      });
    } catch (error) {
      console.error("Failed to toggle pinned status:", error);
    }
  }

  $: taskItems = agent.turn_summaries || [];
  $: pinnedAgents = $sharedWebviewStore.state?.pinnedAgents || {};
  $: isPinned = pinnedAgents?.[agent.remote_agent_id] === true;

  // SSH is only for running or idle agents
  $: canSsh =
    agent.status === RemoteAgentStatus.agentRunning || agent.status === RemoteAgentStatus.agentIdle;

  $: isRemote = true; // TODO(jw) eventually we want to also show local threads

  function handleSsh() {
    if (canSsh) {
      onSsh(agent);
    }
  }
</script>

<div
  class="card-wrapper"
  class:selected-card={selected}
  class:setup-script-card={agent.is_setup_script_agent}
>
  <CardAugment
    variant="surface"
    size={2}
    interactive={true}
    on:click={() => onSelect(agent.remote_agent_id)}
    on:keydown={(e) => e.key === "Enter" && onSelect(agent.remote_agent_id)}
    class="agent-card"
  >
    <div class="card-header">
      <div
        class="session-summary-container"
        title={agent.is_setup_script_agent ? "Generate a setup script" : agent.session_summary}
      >
        {#if agent.is_setup_script_agent}
          <div class="setup-script-title-container">
            <div class="setup-script-badge">
              <Terminal />
            </div>
            <TextAugment size={2} weight="medium">
              <span class="setup-script-title">Generate a setup script</span>
            </TextAugment>
          </div>
        {:else}
          <TextAugment size={2} weight="medium" class="session-text"
            >{agent.session_summary}</TextAugment
          >
        {/if}
      </div>
      <div class="card-info">
        <StatusIndicator
          status={agent.status}
          workspaceStatus={agent.workspace_status}
          isExpanded={true}
        />
      </div>
    </div>

    <div class="card-content">
      {#if taskItems.length > 0}
        <div class="tasks-list">
          {#each taskItems.slice(0, 3) as task, index (index)}
            <AgentTaskItem text={task} status="success" />
          {/each}
        </div>
      {/if}
    </div>

    <div class="card-actions">
      <TextTooltipAugment
        content={isPinned ? "Unpin agent" : "Pin agent"}
        triggerOn={[TooltipTriggerOn.Hover]}
        side="top"
      >
        <IconButtonAugment
          variant="ghost"
          color="neutral"
          size={1}
          on:click={(e) => {
            e.stopPropagation();
            togglePinned(agent.remote_agent_id);
          }}
        >
          {#if isPinned}
            <ThumbtackSlashIcon />
          {:else}
            <ThumbtackIcon />
          {/if}
        </IconButtonAugment>
      </TextTooltipAugment>
      <TextTooltipAugment content="SSH to agent" triggerOn={[TooltipTriggerOn.Hover]} side="top">
        <IconButtonAugment
          disabled={!canSsh}
          variant="ghost"
          color="neutral"
          size={1}
          on:click={(e) => {
            e.stopPropagation();
            handleSsh();
          }}
          title={canSsh ? "SSH to agent" : "SSH to agent (agent must be running or idle)"}
        >
          <Terminal />
        </IconButtonAugment>
      </TextTooltipAugment>
      <TextTooltipAugment content="Delete agent" triggerOn={[TooltipTriggerOn.Hover]} side="top">
        <IconButtonAugment
          variant="ghost"
          color="neutral"
          size={1}
          on:click={(e) => {
            e.stopPropagation();
            deleteAgent(agent.remote_agent_id);
          }}
          title="Delete agent"
        >
          <Trash />
        </IconButtonAugment>
      </TextTooltipAugment>
    </div>
    <AgentCardFooter
      {isRemote}
      status={agent.status}
      timestamp={agent.updated_at || agent.started_at}
    />
  </CardAugment>
</div>

<style>
  .card-wrapper {
    cursor: pointer;
    transition: transform 0.1s ease-in-out;
    height: fit-content;
    padding-bottom: var(--ds-spacing-2);
    border: 1px solid transparent;
  }

  .selected-card :global(.c-card),
  .selected-card :global(.c-card:hover) {
    box-shadow: 0 0 0 1px var(--ds-color-accent-a8);
    --before-background-color: var(--ds-color-accent-a2);
  }

  .selected-card :global(.c-card::after) {
    box-shadow: var(--after-hover-box-shadow);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: var(--ds-spacing-3);
    min-height: 40px;
  }

  .session-summary-container {
    max-width: 100%;
    overflow: hidden;
    display: flex;
    gap: var(--ds-spacing-1);
    flex-direction: row;
    align-items: center;
  }

  .session-summary-container :global(.c-text) {
    display: -webkit-box;
    -webkit-line-clamp: 3; /* Limit to 3 lines */
    line-clamp: 3; /* For compatibility */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4em;
  }

  .session-summary-container :global(.session-text) {
    flex: 1;
  }

  .card-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: absolute;
    bottom: 10px;
    right: var(--ds-spacing-3);
    opacity: 0;
    transition: all 0.15s ease-in-out;
    transform: translateX(5px);
  }

  .card-wrapper:hover .card-actions,
  .card-wrapper:focus .card-actions,
  .card-wrapper:focus-within .card-actions {
    opacity: 1;
    transform: translateX(0);
  }

  .card-wrapper :global(.time-container) {
    transition: all 0.15s ease-in-out;
  }
  .card-wrapper:hover :global(.time-container),
  .card-wrapper:focus :global(.time-container),
  .card-wrapper:focus-within :global(.time-container) {
    opacity: 0;
    pointer-events: none;
    transform: translateX(5px);
  }

  .card-content {
    padding-bottom: 1rem;
  }

  .tasks-list {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
  }

  /* Setup script agent styles */
  .setup-script-title-container {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
  }

  .setup-script-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--ds-color-neutral-10);
    flex-shrink: 0;
  }

  .setup-script-badge :global(svg) {
    width: 16px;
    height: 16px;
  }

  .setup-script-title {
    color: var(--ds-color-neutral-11);
  }

  /* Prevent text wrapping in tooltips for short action labels */
  .card-actions :global(.tippy-box .tooltip-text) {
    white-space: nowrap;
  }
</style>

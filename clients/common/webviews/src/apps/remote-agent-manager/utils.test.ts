/* eslint-disable @typescript-eslint/naming-convention */
import { describe, expect, it } from "vitest";
import {
  getAggregateChanges,
  getUserMessagePrecedingTurn,
  getIndexOfPrecedingUserMessage,
  getIndexOfNextUserMessage,
  getTurnList,
  getChangesForTurn,
  getAllChangesBetweenUserMessages,
} from "./utils";
import {
  type RemoteAgentExchange,
  type ChangedFile,
  FileChangeType,
} from "$vscode/src/remote-agent-manager/types";
import {
  ChatRequestNodeType,
  ChatResultNodeType,
  type Exchange,
  type ChatRequestNode,
  type ChatResultNode,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";

// Mock data helpers
const createExchange = (
  requestMessage: string = "",
  responseText: string = "",
  requestNodes: ChatRequestNode[] = [],
  responseNodes: ChatResultNode[] = [],
): Exchange => ({
  request_message: requestMessage,
  response_text: responseText,
  request_id: "test-id",
  request_nodes: requestNodes,
  response_nodes: responseNodes,
});

const createRemoteAgentExchange = (
  exchange: Exchange,
  changedFiles: ChangedFile[] = [],
  sequenceId: number = 1,
): RemoteAgentExchange => ({
  exchange,
  changed_files: changedFiles,
  sequence_id: sequenceId,
  finished_at: "2023-01-01T00:00:00Z",
});

const createChangedFile = (
  newPath: string,
  oldPath: string = "",
  newContents: string = "new content",
  oldContents: string = "old content",
  changeType: FileChangeType = FileChangeType.modified,
): ChangedFile => ({
  id: `file-${newPath}`,
  new_path: newPath,
  old_path: oldPath,
  new_contents: newContents,
  old_contents: oldContents,
  change_type: changeType,
});

const createToolUseNode = (toolUseId: string): ChatResultNode => ({
  id: 1,
  type: ChatResultNodeType.TOOL_USE,
  content: "",
  tool_use: {
    tool_use_id: toolUseId,
    tool_name: "test-tool",
    input_json: "{}",
  },
});

const createToolResultNode = (toolUseId: string): ChatRequestNode => ({
  id: 1,
  type: ChatRequestNodeType.TOOL_RESULT,
  tool_result_node: {
    tool_use_id: toolUseId,
    content: "tool result",
    is_error: false,
    content_nodes: [],
  },
});

// Mock exchange list for testing
const mockExchanges: RemoteAgentExchange[] = [
  // Exchange 0: First user message
  createRemoteAgentExchange(createExchange("Create a new React component", ""), [], 1),
  // Exchange 1: Agent response with tool use
  createRemoteAgentExchange(
    createExchange(
      "",
      "I'll create a React component for you.",
      [],
      [createToolUseNode("tool-123")],
    ),
    [
      createChangedFile(
        "Component.tsx",
        "",
        "export const Component = () => <div>Hello</div>;",
        "",
      ),
    ],
    2,
  ),
  // Exchange 2: Tool result
  createRemoteAgentExchange(
    createExchange("", "Component created successfully.", [createToolResultNode("tool-123")]),
    [],
    3,
  ),
  // Exchange 3: Agent continues
  createRemoteAgentExchange(
    createExchange("", "Now let me add some styling."),
    [
      createChangedFile(
        "Component.tsx",
        "Component.tsx",
        "export const Component = () => <div className='hello'>Hello</div>;",
        "export const Component = () => <div>Hello</div>;",
      ),
    ],
    4,
  ),
  // Exchange 4: Second user message
  createRemoteAgentExchange(createExchange("Add a prop for the message", ""), [], 5),
  // Exchange 5: Agent response to second user message
  createRemoteAgentExchange(
    createExchange("", "I'll add a message prop.", [], [createToolUseNode("tool-456")]),
    [
      createChangedFile(
        "Component.tsx",
        "Component.tsx",
        "export const Component = ({ message }: { message: string }) => <div className='hello'>{message}</div>;",
        "export const Component = () => <div className='hello'>Hello</div>;",
      ),
    ],
    6,
  ),
  // Exchange 6: Tool result for second tool use
  createRemoteAgentExchange(
    createExchange("", "Prop added successfully.", [createToolResultNode("tool-456")]),
    [createChangedFile("types.ts", "", "export interface ComponentProps { message: string; }", "")],
    7,
  ),
];

describe("utils - exchange and turn functions", () => {
  describe("getAggregateChanges", () => {
    it("should return empty array for empty chat history", () => {
      const result = getAggregateChanges([]);
      expect(result).toEqual([]);
    });

    it("should aggregate all changes from mock exchanges", () => {
      const result = getAggregateChanges(mockExchanges);
      expect(result).toHaveLength(2); // Component.tsx and types.ts

      const componentFile = result.find((f) => f.new_path === "Component.tsx");
      expect(componentFile).toBeDefined();
      expect(componentFile?.new_contents).toContain("message: string");

      const typesFile = result.find((f) => f.new_path === "types.ts");
      expect(typesFile).toBeDefined();
    });

    it("should handle file modifications correctly", () => {
      // Using exchanges 1-3 which modify Component.tsx
      const result = getAggregateChanges(mockExchanges.slice(1, 4));
      expect(result).toHaveLength(1);
      expect(result[0].new_path).toBe("Component.tsx");
      expect(result[0].new_contents).toContain("className='hello'");
    });

    it("should filter out files that have been reverted to original state", () => {
      const file1v1 = createChangedFile("file1.ts", "file1.ts", "modified", "original");
      const file1v2 = createChangedFile("file1.ts", "file1.ts", "original", "original"); // Reverted

      const exchange1 = createRemoteAgentExchange(createExchange("message1"), [file1v1], 1);
      const exchange2 = createRemoteAgentExchange(createExchange("message2"), [file1v2], 2);

      const result = getAggregateChanges([exchange1, exchange2]);
      expect(result).toHaveLength(0); // Should be filtered out
    });

    it("should keep added and deleted files even if content matches", () => {
      const addedFile = createChangedFile("new.ts", "", "content", "", FileChangeType.added);
      const deletedFile = createChangedFile("", "old.ts", "", "content", FileChangeType.deleted);

      const exchange = createRemoteAgentExchange(
        createExchange("message"),
        [addedFile, deletedFile],
        1,
      );

      const result = getAggregateChanges([exchange]);
      expect(result).toHaveLength(2);
    });
  });

  describe("getUserMessagePrecedingTurn", () => {
    it("should return first user message for turn 1", () => {
      const result = getUserMessagePrecedingTurn(mockExchanges, 1);
      expect(result).toBe("Create a new React component");
    });

    it("should return first user message for turn 3", () => {
      const result = getUserMessagePrecedingTurn(mockExchanges, 3);
      expect(result).toBe("Create a new React component");
    });

    it("should return second user message for turn 5", () => {
      const result = getUserMessagePrecedingTurn(mockExchanges, 5);
      expect(result).toBe("Add a prop for the message");
    });

    it("should return the user message when called on a user message", () => {
      // With the edge case fix, when called on a user message, it returns that message
      const result = getUserMessagePrecedingTurn(mockExchanges, 0);
      expect(result).toBe("Create a new React component");
    });
  });

  describe("getIndexOfPrecedingUserMessage", () => {
    it("should return 0 for exchanges after first user message", () => {
      expect(getIndexOfPrecedingUserMessage(mockExchanges, 1)).toBe(0);
      expect(getIndexOfPrecedingUserMessage(mockExchanges, 2)).toBe(0);
      expect(getIndexOfPrecedingUserMessage(mockExchanges, 3)).toBe(0);
    });

    it("should return 4 for exchanges after second user message", () => {
      expect(getIndexOfPrecedingUserMessage(mockExchanges, 5)).toBe(4);
      expect(getIndexOfPrecedingUserMessage(mockExchanges, 6)).toBe(4);
    });

    it("should return the same index when called on a user message", () => {
      // With the edge case fix, when called on a user message, it returns that index
      expect(getIndexOfPrecedingUserMessage(mockExchanges, 0)).toBe(0);
      expect(getIndexOfPrecedingUserMessage(mockExchanges, 4)).toBe(4);
    });

    // Edge case tests
    it("should handle empty chat history", () => {
      expect(getIndexOfPrecedingUserMessage([], 0)).toBe(-1);
    });

    it("should handle history with no user messages", () => {
      const agentOnlyExchanges = [
        createRemoteAgentExchange(createExchange("", "Agent response 1"), [], 1),
        createRemoteAgentExchange(createExchange("", "Agent response 2"), [], 2),
      ];
      expect(getIndexOfPrecedingUserMessage(agentOnlyExchanges, 0)).toBe(-1);
      expect(getIndexOfPrecedingUserMessage(agentOnlyExchanges, 1)).toBe(-1);
    });

    it("should handle out of bounds turnIndex", () => {
      expect(getIndexOfPrecedingUserMessage(mockExchanges, 100)).toBe(-1); // Out of bounds should return -1
      expect(getIndexOfPrecedingUserMessage(mockExchanges, -1)).toBe(-1); // Negative index should return -1
    });
  });

  describe("getIndexOfNextUserMessage", () => {
    it("should return 4 for exchanges before second user message", () => {
      // The function searches from the turnIndex, not after it
      expect(getIndexOfNextUserMessage(mockExchanges, 1)).toBe(4);
      expect(getIndexOfNextUserMessage(mockExchanges, 2)).toBe(4);
      expect(getIndexOfNextUserMessage(mockExchanges, 3)).toBe(4);
    });

    it("should return undefined for exchanges after last user message", () => {
      expect(getIndexOfNextUserMessage(mockExchanges, 5)).toBeUndefined();
      expect(getIndexOfNextUserMessage(mockExchanges, 6)).toBeUndefined();
    });

    it("should return the same index when called on a user message", () => {
      expect(getIndexOfNextUserMessage(mockExchanges, 0)).toBe(0); // First user message
      expect(getIndexOfNextUserMessage(mockExchanges, 4)).toBe(4); // Second user message
    });
  });

  describe("getTurnList", () => {
    it("should return exchanges for first turn", () => {
      const result = getTurnList(mockExchanges, 1);
      expect(result).toHaveLength(3); // Exchanges 1, 2, 3
      expect(result[0]).toBe(mockExchanges[1]);
      expect(result[1]).toBe(mockExchanges[2]);
      expect(result[2]).toBe(mockExchanges[3]);
    });

    it("should return exchanges for second turn", () => {
      const result = getTurnList(mockExchanges, 5);
      expect(result).toHaveLength(2); // Exchanges 5, 6
      expect(result[0]).toBe(mockExchanges[5]);
      expect(result[1]).toBe(mockExchanges[6]);
    });

    it("should handle turn at the beginning of conversation", () => {
      const result = getTurnList(mockExchanges, 0);
      expect(result).toHaveLength(0); // No exchanges before the first user message
    });

    it("should return all exchanges after last user message", () => {
      const result = getTurnList(mockExchanges, 6);
      expect(result).toHaveLength(2); // Exchanges 5 and 6 (from last user message to end)
      expect(result[0]).toBe(mockExchanges[5]);
      expect(result[1]).toBe(mockExchanges[6]);
    });
  });

  describe("getChangesForTurn", () => {
    it("should return changes for first tool use", () => {
      const result = getChangesForTurn(mockExchanges, 1);
      expect(result).toHaveLength(1); // Only Component.tsx from exchange 1

      const componentFile = result.find((f) => f.new_path === "Component.tsx");
      expect(componentFile).toBeDefined();
      expect(componentFile?.new_contents).toContain("export const Component");
    });

    it("should return changes for second tool use", () => {
      const result = getChangesForTurn(mockExchanges, 5);
      expect(result).toHaveLength(2); // Component.tsx and types.ts

      const componentFile = result.find((f) => f.new_path === "Component.tsx");
      expect(componentFile).toBeDefined();
      expect(componentFile?.new_contents).toContain("message: string");

      const typesFile = result.find((f) => f.new_path === "types.ts");
      expect(typesFile).toBeDefined();
    });

    it("should return empty array if no tool call at turn", () => {
      // Exchange 3 has no tool call
      const result = getChangesForTurn(mockExchanges, 3);
      expect(result).toEqual([]);
    });

    it("should return empty array if at user message turn", () => {
      // Exchange 0 and 4 are user messages
      expect(getChangesForTurn(mockExchanges, 0)).toEqual([]);
      expect(getChangesForTurn(mockExchanges, 4)).toEqual([]);
    });

    it("should handle missing tool result", () => {
      // Create exchange with tool use but no matching tool result
      const exchangeWithNoResult = createRemoteAgentExchange(
        createExchange("", "response", [], [createToolUseNode("orphan-tool")]),
        [createChangedFile("orphan.ts", "", "content", "")],
        8,
      );

      const result = getChangesForTurn([...mockExchanges, exchangeWithNoResult], 7);
      expect(result).toEqual([]);
    });
  });

  describe("getAllChangesBetweenUserMessages", () => {
    it("should return changes for first turn", () => {
      const result = getAllChangesBetweenUserMessages(mockExchanges, 1);
      expect(result).toHaveLength(1); // Component.tsx

      const componentFile = result.find((f) => f.new_path === "Component.tsx");
      expect(componentFile).toBeDefined();
      expect(componentFile?.new_contents).toContain("className='hello'");
    });

    it("should return changes for second turn", () => {
      const result = getAllChangesBetweenUserMessages(mockExchanges, 5);
      expect(result).toHaveLength(2); // Component.tsx and types.ts

      const componentFile = result.find((f) => f.new_path === "Component.tsx");
      expect(componentFile).toBeDefined();
      expect(componentFile?.new_contents).toContain("message: string");

      const typesFile = result.find((f) => f.new_path === "types.ts");
      expect(typesFile).toBeDefined();
    });

    it("should handle conversation ending with user message and response with no changed files", () => {
      // This is the specific edge case mentioned by the user
      const conversationWithNoFilesAtEnd = [
        ...mockExchanges,
        // Add a user message
        createRemoteAgentExchange(createExchange("Just say hello", ""), [], 8),
        // Add agent response with no changed files
        createRemoteAgentExchange(createExchange("", "Hello! How can I help you?"), [], 9),
      ];

      const result = getAllChangesBetweenUserMessages(conversationWithNoFilesAtEnd, 7);
      expect(result).toEqual([]); // Should return empty array since no files were changed
    });

    it("should handle empty chat history", () => {
      const result = getAllChangesBetweenUserMessages([], 0);
      expect(result).toEqual([]);
    });

    it("should handle single user message with no responses", () => {
      const singleUserMessage = [createRemoteAgentExchange(createExchange("Hello", ""), [], 1)];

      const result = getAllChangesBetweenUserMessages(singleUserMessage, 0);
      expect(result).toEqual([]); // No exchanges between user messages
    });

    it("should handle conversation with only agent responses (no user messages)", () => {
      const agentOnlyExchanges = [
        createRemoteAgentExchange(
          createExchange("", "Agent response 1"),
          [createChangedFile("file1.ts", "", "content1", "")],
          1,
        ),
        createRemoteAgentExchange(
          createExchange("", "Agent response 2"),
          [createChangedFile("file2.ts", "", "content2", "")],
          2,
        ),
      ];

      // When there are no user messages, we should return all changes since there are no boundaries
      const result = getAllChangesBetweenUserMessages(agentOnlyExchanges, 0);
      expect(result).toHaveLength(2); // All files are included since there are no user message boundaries
      expect(result.find((f) => f.new_path === "file1.ts")).toBeDefined();
      expect(result.find((f) => f.new_path === "file2.ts")).toBeDefined();
    });

    it("should aggregate changes correctly across multiple exchanges in a turn", () => {
      // Test that it properly aggregates changes from multiple exchanges between user messages
      const result = getAllChangesBetweenUserMessages(mockExchanges, 2);
      expect(result).toHaveLength(1); // Should aggregate Component.tsx changes

      const componentFile = result[0];
      expect(componentFile.new_path).toBe("Component.tsx");
      // Should have the final version after all modifications in the turn
      expect(componentFile.new_contents).toContain("className='hello'");
    });

    it("should handle out of bounds turnIndex", () => {
      const result = getAllChangesBetweenUserMessages(mockExchanges, 100);
      expect(result).toEqual([]); // Out of bounds should return empty array

      const result2 = getAllChangesBetweenUserMessages(mockExchanges, -1);
      expect(result2).toEqual([]); // Negative index should return empty array
    });
  });
});

/* eslint-disable @typescript-eslint/naming-convention */
import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import { RemoteAgentDiffOpsModel } from "./ra-diff-ops-model";

describe("RemoteAgentDiffOpsModel", () => {
  let asyncMsgSender: AsyncMsgSender;

  beforeEach(() => {
    asyncMsgSender = new AsyncMsgSender(vi.fn());

    // Mock the send method
    vi.spyOn(asyncMsgSender, "send").mockImplementation((message) => {
      const type = message.type;

      // Mock responses for different message types
      switch (type) {
        case WebViewMessageType.diffExplanationRequest:
          return Promise.resolve({
            type: WebViewMessageType.diffExplanationResponse,
            data: {
              explanation: [{ title: "Test explanation", content: "Test content" }],
            },
          }) as any;

        case WebViewMessageType.diffGroupChangesRequest:
          return Promise.resolve({
            type: WebViewMessageType.diffGroupChangesResponse,
            data: {
              groupedChanges: [{ file: "test/file.ts", changes: [] }],
            },
          }) as any;

        case WebViewMessageType.diffDescriptionsRequest:
          return Promise.resolve({
            type: WebViewMessageType.diffDescriptionsResponse,
            data: {
              explanation: [{ title: "Test description", content: "Test content" }],
            },
          }) as any;

        case WebViewMessageType.applyChangesRequest:
          return Promise.resolve({
            type: WebViewMessageType.applyChangesResponse,
            data: {
              success: true,
            },
          }) as any;

        default:
          return Promise.resolve({ type: "unknown", data: {} });
      }
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  test("should initialize", () => {
    const model = new RemoteAgentDiffOpsModel(asyncMsgSender);

    expect(model).toBeDefined();
    expect(model.applyingFilePaths).toEqual([]);
    expect(model.appliedFilePaths).toEqual([]);
  });

  test("should get diff explanation with default timeout", async () => {
    const model = new RemoteAgentDiffOpsModel(asyncMsgSender);
    const changedFiles = [{ path: "test/file.ts", diff: "test diff" }];

    const explanation = await model.getDiffExplanation(changedFiles);

    expect(asyncMsgSender.send).toHaveBeenCalledWith(
      expect.objectContaining({
        type: WebViewMessageType.diffExplanationRequest,
        data: expect.objectContaining({
          changedFiles,
        }),
      }),
      30000,
    );

    expect(explanation).toEqual([{ title: "Test explanation", content: "Test content" }]);
  });

  test("should group changes", async () => {
    const model = new RemoteAgentDiffOpsModel(asyncMsgSender);
    const changedFiles = [{ path: "test/file.ts", diff: "test diff" }];

    const groupedChanges = await model.groupChanges(changedFiles);

    expect(asyncMsgSender.send).toHaveBeenCalledWith(
      expect.objectContaining({
        type: WebViewMessageType.diffGroupChangesRequest,
        data: expect.objectContaining({
          changedFiles,
        }),
      }),
    );

    expect(groupedChanges).toEqual([{ file: "test/file.ts", changes: [] }]);
  });

  test("should get descriptions", async () => {
    const model = new RemoteAgentDiffOpsModel(asyncMsgSender);
    const groupedChanges = [{ file: "test/file.ts", changes: [] }];

    const descriptions = await model.getDescriptions(groupedChanges);

    expect(asyncMsgSender.send).toHaveBeenCalledWith(
      expect.objectContaining({
        type: WebViewMessageType.diffDescriptionsRequest,
        data: expect.objectContaining({
          groupedChanges,
        }),
      }),
    );

    expect(descriptions).toEqual([{ title: "Test description", content: "Test content" }]);
  });

  test("should apply changes", async () => {
    const model = new RemoteAgentDiffOpsModel(asyncMsgSender);

    await model.applyChanges("test/file.ts", "original code", "new code");

    expect(asyncMsgSender.send).toHaveBeenCalledWith(
      expect.objectContaining({
        type: WebViewMessageType.applyChangesRequest,
        data: expect.objectContaining({
          path: "test/file.ts",
          originalCode: "original code",
          newCode: "new code",
        }),
      }),
      30000, // 30 second timeout
    );

    // Check that the file path was added to applying paths and then removed
    expect(model.applyingFilePaths).toEqual([]);
    expect(model.appliedFilePaths).toEqual(["test/file.ts"]);
  });
});

/* eslint-disable @typescript-eslint/naming-convention */
import { type GetRemoteAgentHistoryStreamResponse } from "$vscode/src/remote-agent-manager/types";
import { withTimeout } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import { STREAM_CANCELLED, STREAM_TIMEOUT } from "@augment-internal/sidecar-libs/src/api/types";
import { afterEach, describe, expect, test, vi } from "vitest";
import {
  isHistoryStreamUpdate,
  RemoteAgentsHistoryStreamWithRetry,
  StreamRetryExhaustedError,
  type RemoteAgentsHistoryStreamUpdate,
} from "./remote-agents-history-stream-with-retry";
import { type IRemoteAgentsError } from "./remote-agents-state-model";

describe("RemoteAgentsHistoryStreamWithRetry", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  test("should retry on errors and eventually succeed", async () => {
    // Mock data
    const agentId = "test-agent-id";
    const lastProcessedSequenceId = 123;

    // Mock stream function that fails twice, then succeeds
    let attemptCount = 0;
    const mockStartStreamFn = vi.fn(async function* (
      _agentId: string,
      _streamId: string,
      _lastProcessedSequenceId: number,
    ) {
      attemptCount++;

      if (attemptCount <= 2) {
        // First two attempts fail with error
        throw new Error("Service temporarily unavailable");
      }

      // Third attempt succeeds
      yield {
        updates: [
          {
            type: 1, // AgentHistoryUpdateType.AGENT_HISTORY_EXCHANGE
            exchange: {
              exchange: { id: "exchange-1" } as any,
              changed_files: [],
              sequence_id: 124,
            },
          },
        ],
      } as GetRemoteAgentHistoryStreamResponse;

      yield {
        updates: [
          {
            type: 1, // AgentHistoryUpdateType.AGENT_HISTORY_EXCHANGE
            exchange: {
              exchange: { id: "exchange-2" } as any,
              changed_files: [],
              sequence_id: 125,
            },
          },
        ],
      } as GetRemoteAgentHistoryStreamResponse;
    });

    // Mock cancel function
    const mockCancelStreamFn = vi.fn(async (_streamId: string) => {
      // Do nothing in the mock
    });

    // Create instance with shorter delays for testing
    const historyStream = new RemoteAgentsHistoryStreamWithRetry(
      agentId,
      lastProcessedSequenceId,
      mockStartStreamFn,
      mockCancelStreamFn,
      3, // maxRetries
      100, // baseDelay (ms)
    );

    // Collect all chunks from the stream
    const chunks: RemoteAgentsHistoryStreamUpdate[] = [];
    for await (const chunk of historyStream.getStream()) {
      chunks.push(chunk);
    }

    // Verify behavior
    expect(mockStartStreamFn).toHaveBeenCalledTimes(3);
    expect(mockStartStreamFn).toHaveBeenCalledWith(
      agentId,
      historyStream.streamId,
      lastProcessedSequenceId,
    );

    // Check that we got the expected chunks
    // We should have 2 error chunks (one for each retry) and 2 success chunks
    expect(chunks.length).toBe(4);

    // First two chunks should be error messages
    expect(isHistoryStreamUpdate(chunks[0])).toBe(false);
    expect(isHistoryStreamUpdate(chunks[1])).toBe(false);

    // Error chunks should have the expected format
    const errorChunk1 = chunks[0] as IRemoteAgentsError;
    const errorChunk2 = chunks[1] as IRemoteAgentsError;
    expect(errorChunk1.errorMessage).toBe("There was an error connecting to the remote agent.");
    expect(errorChunk1.retryAt).toBeInstanceOf(Date);
    expect(errorChunk2.errorMessage).toBe("There was an error connecting to the remote agent.");
    expect(errorChunk2.retryAt).toBeInstanceOf(Date);

    // Last two chunks should be successful updates
    expect(isHistoryStreamUpdate(chunks[2])).toBe(true);
    expect(isHistoryStreamUpdate(chunks[3])).toBe(true);

    const successChunk1 = chunks[2] as GetRemoteAgentHistoryStreamResponse;
    const successChunk2 = chunks[3] as GetRemoteAgentHistoryStreamResponse;
    expect(successChunk1.updates[0].exchange?.sequence_id).toBe(124);
    expect(successChunk2.updates[0].exchange?.sequence_id).toBe(125);
  });

  test("should throw StreamRetryExhaustedError after maxRetries", async () => {
    // Mock data
    const agentId = "test-agent-id";
    const lastProcessedSequenceId = 123;
    const errorMessage = "Service temporarily unavailable";

    // Mock stream function that always fails
    // eslint-disable-next-line require-yield
    const mockStartStreamFn = vi.fn(async function* (
      _agentId: string,
      _streamId: string,
      _lastProcessedSequenceId: number,
    ) {
      throw new Error(errorMessage);
    });

    // Mock cancel function
    const mockCancelStreamFn = vi.fn(async (_streamId: string) => {
      // Do nothing in the mock
    });

    // Create instance with shorter delays for testing
    const maxRetries = 2;
    const historyStream = new RemoteAgentsHistoryStreamWithRetry(
      agentId,
      lastProcessedSequenceId,
      mockStartStreamFn,
      mockCancelStreamFn,
      maxRetries,
      100, // baseDelay (ms)
    );

    // Collect all chunks from the stream and expect it to throw
    const chunks: RemoteAgentsHistoryStreamUpdate[] = [];
    await expect(async () => {
      for await (const chunk of historyStream.getStream()) {
        chunks.push(chunk);
      }
    }).rejects.toThrow(StreamRetryExhaustedError);

    // Verify behavior
    expect(mockStartStreamFn).toHaveBeenCalledTimes(maxRetries + 1); // Initial attempt + retries

    // Check that we got the expected error chunks before the exception
    expect(chunks.length).toBe(maxRetries);

    // All chunks should be error messages
    for (const chunk of chunks) {
      expect(isHistoryStreamUpdate(chunk)).toBe(false);
      const errorChunk = chunk as IRemoteAgentsError;
      expect(errorChunk.errorMessage).toBe("There was an error connecting to the remote agent.");
      expect(errorChunk.retryAt).toBeInstanceOf(Date);
    }
  });

  test("should handle cancellation during retry delay", async () => {
    // Mock data
    const agentId = "test-agent-id";
    const lastProcessedSequenceId = 123;

    // Mock stream function that fails with error
    // eslint-disable-next-line require-yield
    const mockStartStreamFn = vi.fn(async function* (
      _agentId: string,
      _streamId: string,
      _lastProcessedSequenceId: number,
    ) {
      throw new Error("Service temporarily unavailable");
    });

    // Mock cancel function
    const mockCancelStreamFn = vi.fn(async (_streamId: string) => {
      // Do nothing in the mock
    });

    // Create instance with shorter delay for testing
    const historyStream = new RemoteAgentsHistoryStreamWithRetry(
      agentId,
      lastProcessedSequenceId,
      mockStartStreamFn,
      mockCancelStreamFn,
      3, // maxRetries
      500, // baseDelay (ms)
    );

    // Start a separate process that will cancel the stream after a short delay
    setTimeout(() => {
      historyStream.cancel();
    }, 10);

    // Collect all chunks from the stream
    const chunks: RemoteAgentsHistoryStreamUpdate[] = [];
    for await (const chunk of historyStream.getStream()) {
      chunks.push(chunk);
    }

    // Verify behavior
    expect(mockStartStreamFn).toHaveBeenCalledTimes(1);

    // We should have at most one error chunk (might not even get that if cancellation is fast)
    expect(chunks.length).toBeLessThanOrEqual(1);

    // If we got a chunk, it should be an error message
    if (chunks.length > 0) {
      expect(isHistoryStreamUpdate(chunks[0])).toBe(false);
      const errorChunk = chunks[0] as IRemoteAgentsError;
      expect(errorChunk.errorMessage).toBe("There was an error connecting to the remote agent.");
      expect(errorChunk.retryAt).toBeInstanceOf(Date);
    }
  });

  test("should retry on real timeout errors using AbortSignal.timeout() with error chunks", async () => {
    // Skip this test if AbortSignal.timeout is not available (Node.js < 17.3)
    if (!AbortSignal.timeout) {
      console.warn("Skipping test: AbortSignal.timeout() is not available in this environment");
      return;
    }

    // Mock data
    const agentId = "test-agent-id";
    const lastProcessedSequenceId = 123;

    // Mock stream function that uses a real AbortSignal.timeout
    let attemptCount = 0;
    const mockStartStreamFn = vi.fn(async function* (
      _agentId: string,
      _streamId: string,
      _lastProcessedSequenceId: number,
    ) {
      attemptCount++;

      if (attemptCount === 1) {
        // First attempt uses a real timeout
        try {
          // Create a timeout signal with a very short timeout
          const timeoutSignal = AbortSignal.timeout(0);

          // Wait for the promise to be aborted by the timeout
          await new Promise((_, reject) => {
            setTimeout(() => {
              if (timeoutSignal.aborted) {
                reject(timeoutSignal.reason);
              } else {
                reject("Promise completed before timeout, this is a bug.");
              }
            }, 300);
          });
        } catch (error) {
          console.error("Caught timeout error:", error);
          // Re-throw the actual error to be caught by the retry mechanism
          throw error;
        }

        // This code should never be reached due to the timeout
        yield {
          updates: [],
        } as GetRemoteAgentHistoryStreamResponse;
      }

      // Second attempt succeeds
      yield {
        updates: [
          {
            type: 1, // AgentHistoryUpdateType.AGENT_HISTORY_EXCHANGE
            exchange: {
              exchange: { id: "exchange-1" } as any,
              changed_files: [],
              sequence_id: 124,
            },
          },
        ],
      } as GetRemoteAgentHistoryStreamResponse;
    });

    // Mock cancel function
    const mockCancelStreamFn = vi.fn(async (_streamId: string) => {
      // Do nothing in the mock
    });

    // Create instance with shorter delays for testing
    const historyStream = new RemoteAgentsHistoryStreamWithRetry(
      agentId,
      lastProcessedSequenceId,
      mockStartStreamFn,
      mockCancelStreamFn,
      3, // maxRetries
      100, // baseDelay (ms)
    );

    // Collect all chunks from the stream
    const chunks: RemoteAgentsHistoryStreamUpdate[] = [];
    for await (const chunk of historyStream.getStream()) {
      chunks.push(chunk);
    }

    // Verify behavior
    expect(mockStartStreamFn).toHaveBeenCalledTimes(2);

    // We should have an error chunk and then the success chunk
    // (since AbortSignal.timeout doesn't produce the STREAM_TIMEOUT constant)
    expect(chunks.length).toBe(2);
    expect(isHistoryStreamUpdate(chunks[0])).toBe(false);
    expect(isHistoryStreamUpdate(chunks[1])).toBe(true);

    const errorChunk = chunks[0] as IRemoteAgentsError;
    expect(errorChunk.errorMessage).toBe("There was an error connecting to the remote agent.");
    expect(errorChunk.retryAt).toBeInstanceOf(Date);

    const successChunk = chunks[1] as GetRemoteAgentHistoryStreamResponse;
    expect(successChunk.updates[0].exchange?.sequence_id).toBe(124);
  });

  test("should immediately retry on STREAM_TIMEOUT errors without yielding error chunks", async () => {
    // Mock data
    const agentId = "test-agent-id";
    const lastProcessedSequenceId = 123;

    // Mock stream function that throws STREAM_TIMEOUT error on first attempt
    let attemptCount = 0;
    const mockStartStreamFn = vi.fn(async function* (
      _agentId: string,
      _streamId: string,
      _lastProcessedSequenceId: number,
    ) {
      attemptCount++;

      if (attemptCount === 1) {
        // First attempt throws STREAM_TIMEOUT error
        throw new Error(STREAM_TIMEOUT);
      }

      // Second attempt succeeds
      yield {
        updates: [
          {
            type: 1, // AgentHistoryUpdateType.AGENT_HISTORY_EXCHANGE
            exchange: {
              exchange: { id: "exchange-1" } as any,
              changed_files: [],
              sequence_id: 124,
            },
          },
        ],
      } as GetRemoteAgentHistoryStreamResponse;
    });

    // Mock cancel function
    const mockCancelStreamFn = vi.fn(async (_streamId: string) => {
      // Do nothing in the mock
    });

    // Create instance with shorter delays for testing
    const historyStream = new RemoteAgentsHistoryStreamWithRetry(
      agentId,
      lastProcessedSequenceId,
      mockStartStreamFn,
      mockCancelStreamFn,
      3, // maxRetries
      100, // baseDelay (ms)
    );

    // Collect all chunks from the stream
    const chunks: RemoteAgentsHistoryStreamUpdate[] = [];
    for await (const chunk of historyStream.getStream()) {
      chunks.push(chunk);
    }

    // Verify behavior
    expect(mockStartStreamFn).toHaveBeenCalledTimes(2);

    // We should have only the success chunk, no error chunks for timeout errors
    expect(chunks.length).toBe(1);
    expect(isHistoryStreamUpdate(chunks[0])).toBe(true);

    const successChunk = chunks[0] as GetRemoteAgentHistoryStreamResponse;
    expect(successChunk.updates[0].exchange?.sequence_id).toBe(124);
  });

  test("should not retry on STREAM_CANCELLED errors and mark as cancelled", async () => {
    // Mock data
    const agentId = "test-agent-id";
    const lastProcessedSequenceId = 123;

    // Mock stream function that throws STREAM_CANCELLED error
    // eslint-disable-next-line require-yield
    const mockStartStreamFn = vi.fn(async function* (
      _agentId: string,
      _streamId: string,
      _lastProcessedSequenceId: number,
    ) {
      throw new Error(STREAM_CANCELLED);
    });

    // Mock cancel function
    const mockCancelStreamFn = vi.fn(async (_streamId: string) => {
      // Do nothing in the mock
    });

    // Create instance
    const historyStream = new RemoteAgentsHistoryStreamWithRetry(
      agentId,
      lastProcessedSequenceId,
      mockStartStreamFn,
      mockCancelStreamFn,
      3, // maxRetries
      100, // baseDelay (ms)
    );

    // Collect all chunks from the stream
    const chunks: RemoteAgentsHistoryStreamUpdate[] = [];
    for await (const chunk of historyStream.getStream()) {
      chunks.push(chunk);
    }

    // Verify behavior - should only try once and then stop due to cancellation
    expect(mockStartStreamFn).toHaveBeenCalledTimes(1);
    expect(historyStream.isCancelled).toBe(true);

    // Should have no chunks since cancellation doesn't yield error chunks and stops immediately
    expect(chunks.length).toBe(0);
  });

  test("should retry on timeout errors using withTimeout wrapper with error chunks", async () => {
    // Mock data
    const agentId = "test-agent-id";
    const lastProcessedSequenceId = 123;

    // Mock stream function that uses the withTimeout wrapper
    let attemptCount = 0;
    const mockStartStreamFn = vi.fn(async function* (
      _agentId: string,
      _streamId: string,
      _lastProcessedSequenceId: number,
    ) {
      attemptCount++;

      if (attemptCount === 1) {
        // This will create a promise that never resolves, and will be timed out
        await withTimeout(
          new Promise(() => {
            // Do nothing, just hang forever
          }),
          10,
        );

        // This code should never be reached due to the timeout
        yield {
          updates: [],
        } as GetRemoteAgentHistoryStreamResponse;
      }

      // Second attempt succeeds
      yield {
        updates: [
          {
            type: 1, // AgentHistoryUpdateType.AGENT_HISTORY_EXCHANGE
            exchange: {
              exchange: { id: "exchange-1" } as any,
              changed_files: [],
              sequence_id: 124,
            },
          },
        ],
      } as GetRemoteAgentHistoryStreamResponse;
    });

    // Mock cancel function
    const mockCancelStreamFn = vi.fn(async (_streamId: string) => {
      // Do nothing in the mock
    });

    // Create instance with shorter delays for testing
    const historyStream = new RemoteAgentsHistoryStreamWithRetry(
      agentId,
      lastProcessedSequenceId,
      mockStartStreamFn,
      mockCancelStreamFn,
      3, // maxRetries
      100, // baseDelay (ms)
    );

    // Collect all chunks from the stream
    const chunks: RemoteAgentsHistoryStreamUpdate[] = [];
    for await (const chunk of historyStream.getStream()) {
      chunks.push(chunk);
    }

    // Verify behavior
    expect(mockStartStreamFn).toHaveBeenCalledTimes(2);

    // We should have an error chunk and then the success chunk
    // (since withTimeout doesn't produce the STREAM_TIMEOUT constant)
    expect(chunks.length).toBe(2);
    expect(isHistoryStreamUpdate(chunks[0])).toBe(false);
    expect(isHistoryStreamUpdate(chunks[1])).toBe(true);

    const errorChunk = chunks[0] as IRemoteAgentsError;
    expect(errorChunk.errorMessage).toBe("There was an error connecting to the remote agent.");
    expect(errorChunk.retryAt).toBeInstanceOf(Date);

    const successChunk = chunks[1] as GetRemoteAgentHistoryStreamResponse;
    expect(successChunk.updates[0].exchange?.sequence_id).toBe(124);
  });

  test("should call cancelStreamFn with streamId when cancel is called", async () => {
    // Mock data
    const agentId = "test-agent-id";
    const lastProcessedSequenceId = 123;

    // Mock stream function
    const mockStartStreamFn = vi.fn(async function* (
      _agentId: string,
      _streamId: string,
      _lastProcessedSequenceId: number,
    ) {
      yield {
        updates: [
          {
            type: 1, // AgentHistoryUpdateType.AGENT_HISTORY_EXCHANGE
            exchange: {
              exchange: { id: "exchange-1" } as any,
              changed_files: [],
              sequence_id: 124,
            },
          },
        ],
      } as GetRemoteAgentHistoryStreamResponse;
    });

    // Mock cancel function
    const mockCancelStreamFn = vi.fn(async (_streamId: string) => {
      // Do nothing in the mock
    });

    // Create instance
    const historyStream = new RemoteAgentsHistoryStreamWithRetry(
      agentId,
      lastProcessedSequenceId,
      mockStartStreamFn,
      mockCancelStreamFn,
      3, // maxRetries
      100, // baseDelay (ms)
    );

    // Verify streamId is generated
    expect(historyStream.streamId).toBeDefined();
    expect(typeof historyStream.streamId).toBe("string");

    // Call cancel
    await historyStream.cancel();

    // Verify cancel behavior
    expect(historyStream.isCancelled).toBe(true);
    expect(mockCancelStreamFn).toHaveBeenCalledTimes(1);
    expect(mockCancelStreamFn).toHaveBeenCalledWith(historyStream.streamId);
  });
});

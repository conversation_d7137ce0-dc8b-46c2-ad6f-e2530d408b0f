// RemoteAgentsStateModel.ts
import type {
  RemoteAgent,
  RemoteAgentExchange,
  RemoteWorkspaceSetupStatus,
} from "$vscode/src/remote-agent-manager/types";

export interface IRemoteAgentsError {
  errorMessage: string;
  retryAt?: Date;
}

/**
 * Error types for sendMessage operations
 */
export enum SendMessageErrorType {
  /** Error returned from the remote agent chat request API */
  chatRequestFailed = "chat_request_failed",
  /** Message timed out waiting for response */
  messageTimeout = "message_timeout",
  /** Agent is in failed state and cannot accept messages */
  agentFailed = "agent_failed",
}

/**
 * Error interface for sendMessage operations with retry capability
 */
export interface ISendMessageError {
  type: SendMessageErrorType;
  errorMessage: string;
  /** Whether this error allows retry */
  canRetry: boolean;
  /** The optimistic exchange ID that failed (if applicable) */
  failedExchangeId?: string;
}

/**
 * The state of the remote agents
 */
export interface IRemoteAgentsState {
  agentOverviews: RemoteAgent[];
  agentConversations: Map<string, RemoteAgentExchange[]>;
  agentLogs: Map<string, RemoteWorkspaceSetupStatus | undefined>;

  /** Maximum number of remote agents allowed (from server) */
  // TODO (jw): this number comes in the overviews response. But we can just make the main
  // RemoteAgentsModel call the overviews fetch once and set this number, rather than having
  // it in the state model.
  maxRemoteAgents: number;
  /** Maximum number of active remote agents allowed (from server) */
  maxActiveRemoteAgents: number;

  /**
   * Error states. These states are visible to the user so should only be set when
   * we have repeatedly failed to fetch, or there is something requiring user
   * intervention.
   */
  overviewError: IRemoteAgentsError | undefined;
  conversationError: IRemoteAgentsError | undefined;
  logsError: IRemoteAgentsError | undefined;

  /**
   * Loading states. These states are visible to the user so should only be true when the
   * data is first loading or after a manual refresh.
   */
  isOverviewsLoading: boolean;
  isConversationLoading: boolean;
  isLogsLoading: boolean;

  /**
   * The number of times we have failed to poll for logs. This may occur either because
   * the agent is not yet in the overviews list, or because the agent is no longer in the
   * starting state. For the former case, we should retry a few times before giving up.
   */
  logPollFailedCount: number;
}

export type OverviewsUpdate = {
  type: "overviews";
  data: RemoteAgent[];
  error?: IRemoteAgentsError;
};
export type ConversationUpdate = {
  type: "conversation";
  agentId: string;
  data: RemoteAgentExchange[];
  error?: IRemoteAgentsError;
};
export type LogsUpdate = {
  type: "logs";
  agentId: string;
  data: RemoteWorkspaceSetupStatus;
  error?: IRemoteAgentsError;
};
export type AllUpdate = { type: "all"; data: IRemoteAgentsState };

/**
 * Possible types of state updates
 */
export type RemoteAgentsStateUpdate = OverviewsUpdate | ConversationUpdate | LogsUpdate | AllUpdate;

/**
 * Options for starting to update state
 */
export type StateUpdateOpts = {
  overviews?: boolean;
  conversation?: {
    agentId: string;
  };
  logs?: {
    agentId: string;
  };
};

/**
 * Model for managing the state of remote agents. This lets us abstract away the
 * details of how the state is updated and managed (e.g. polling vs streaming)
 */
export interface IRemoteAgentsStateModel {
  /**
   * Get the current state
   */
  state: IRemoteAgentsState;

  /**
   * Start receiving state updates
   * If options are provided, only start updates for the specified types
   * If no options are provided, start all updates
   */
  startStateUpdates(options?: StateUpdateOpts): void;

  /**
   * Stop receiving state updates for the specified types
   * If options are provided, only stop updates for the specified types
   * If no options are provided, stop all updates
   */
  stopStateUpdates(options?: StateUpdateOpts): void;

  /**
   * Refresh the current agent conversation with the most recent messages
   * @param agentId The ID of the agent to refresh
   */
  refreshCurrentAgent(agentId: string): Promise<void>;

  /**
   * Refresh all agent overviews with the most recent data
   */
  refreshAgentOverviews(): Promise<RemoteAgent[]>;

  /**
   * Refresh agent logs with the most recent data
   * @param agentId The ID of the agent to get logs for
   */
  refreshAgentLogs(agentId: string): Promise<RemoteWorkspaceSetupStatus | undefined>;

  /**
   * Register a callback to be called when state updates are received
   * @param callback The callback to call with updated state
   */
  onStateUpdate(callback: (update: RemoteAgentsStateUpdate) => void): () => void;

  /**
   * Clean up any resources used by the model
   */
  dispose(): void;
}

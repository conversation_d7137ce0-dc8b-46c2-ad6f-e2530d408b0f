import { type GetRemoteAgentHistoryStreamResponse } from "$vscode/src/remote-agent-manager/types";
import { type IRemoteAgentsError } from "./remote-agents-state-model";
import { STREAM_CANCELLED, STREAM_TIMEOUT } from "@augment-internal/sidecar-libs/src/api/types";

/**
 * Custom error class for stream retry exhaustion
 */
export class StreamRetryExhaustedError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "StreamRetryExhaustedError";
  }
}

export type RemoteAgentsHistoryStreamUpdate =
  | GetRemoteAgentHistoryStreamResponse
  | IRemoteAgentsError;

export const isHistoryStreamUpdate = (
  update: RemoteAgentsHistoryStreamUpdate,
): update is GetRemoteAgentHistoryStreamResponse => {
  return (update as GetRemoteAgentHistoryStreamResponse).updates !== undefined;
};

/**
 * RemoteAgentsHistoryStreamWithRetry provides a retry mechanism for streaming remote agent history.
 * It handles retriable errors and cleanup on cancellation.
 */
export class RemoteAgentsHistoryStreamWithRetry {
  private _isCancelled = false;
  /**
   * Unique ID for the stream. The extension keeps a map of streamId to AbortController. When we cancel
   * the stream from the webviews side, we call cancelStreamFn with this ID to close the real stream on the extension side.
   * */
  public readonly streamId: string;

  public get isCancelled() {
    return this._isCancelled;
  }

  /**
   * Creates a new RemoteAgentsHistoryStreamWithRetry instance.
   *
   * @param agentId - The ID of the remote agent
   * @param lastProcessedSequenceId - The last processed sequence ID
   * @param startStreamFn - Function to start a history stream
   * @param cancelStreamFn - Function to cancel a history stream
   * @param maxRetries - Maximum number of retry attempts
   * @param baseDelay - Base delay between retries in milliseconds
   */
  constructor(
    private readonly agentId: string,
    private readonly lastProcessedSequenceId: number,
    private readonly startStreamFn: (
      agentId: string,
      streamId: string,
      lastProcessedSequenceId: number,
    ) => AsyncGenerator<GetRemoteAgentHistoryStreamResponse>,
    private readonly cancelStreamFn: (streamId: string) => Promise<void>,
    private readonly maxRetries: number = 5,
    private readonly baseDelay: number = 4000,
  ) {
    this.streamId = crypto.randomUUID();
  }

  /**
   * Cancels the stream.
   */
  public async cancel(): Promise<void> {
    this._isCancelled = true;
    await this.cancelStreamFn(this.streamId);
  }

  /**
   * Returns the stream of agent history updates.
   *
   * Handles retriable errors on starting the stream. Once the stream has
   * started, any errors are passed through to the caller.
   *
   * @throws StreamRetryExhaustedError when all retry attempts have been exhausted
   * @throws Error for any other errors that occur during the stream
   */
  public async *getStream(): AsyncGenerator<RemoteAgentsHistoryStreamUpdate> {
    let retryCount = 0;

    while (!this._isCancelled) {
      const generator = this.startStreamFn(
        this.agentId,
        this.streamId,
        this.lastProcessedSequenceId,
      );

      try {
        for await (const chunk of generator) {
          if (this._isCancelled) {
            return;
          }
          // Reset retry count on successful stream
          retryCount = 0;
          yield chunk;
        }
        // If we've consumed the stream to completion, we're done
        return;
      } catch (error) {
        // Treat all errors as retriable, only error after exhausting retries or cancellation.
        const errorMessage = error instanceof Error ? error.message : String(error);

        if (errorMessage === STREAM_CANCELLED) {
          // Message from aborted fetch, treat as cancellation
          this._isCancelled = true;
        }
        // Cancellation and exhaustion are the only non-retriable exit conditions
        if (this._isCancelled) {
          return;
        }

        // If we've exhausted retries, throw an error
        retryCount++;
        if (retryCount > this.maxRetries) {
          throw new StreamRetryExhaustedError(
            `Failed after ${this.maxRetries} attempts: ${errorMessage}`,
          );
        }

        let delayMs = this.baseDelay * 2 ** (retryCount - 1);

        // callApiStream has a built in timeout to cancel the fetch. But for remote agents chat history,
        // we want to keep the stream open as long as the user is looking at the conversation. So we
        // treat the timeout error as a signal to check if the stream has been cancelled, and if it hasn't,
        // we should reestablish the connection.
        const isTimeoutError = errorMessage === STREAM_TIMEOUT;
        if (isTimeoutError) {
          // Immediately retry on timeout errors.
          delayMs = 0;
        } else {
          // Before sleeping, yield an error to the client so the user knows we're gonna retry in a bit.
          // This doesn't apply to timeout errors since those should be silently retried.
          const errorToYield = {
            errorMessage: `There was an error connecting to the remote agent.`,
            retryAt: new Date(Date.now() + delayMs),
          };
          yield errorToYield;
        }

        console.warn(
          `Retrying remote agent history stream in ${delayMs / 1000} seconds... (Attempt ${retryCount} of ${this.maxRetries})`,
        );

        await new Promise((resolve) => setTimeout(resolve, delayMs));
        continue;
      }
    }
  }
}

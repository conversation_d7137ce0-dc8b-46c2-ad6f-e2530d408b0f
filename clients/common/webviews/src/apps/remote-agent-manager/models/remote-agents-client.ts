import type { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import {
  type GetRemoteAgentHistoryStreamResponse,
  type IRemoteAgentDiffPanelOptions,
  type RemoteAgentChatRequestDetails,
  type RemoteAgentWorkspaceSetup,
} from "$vscode/src/remote-agent-manager/types";
import { type SetupScriptLocation } from "$vscode/src/utils/remote-agent-setup/types";
import {
  type RemoteAgentNotificationSettingsContext,
  type RemoteAgentPinnedStatusContext,
} from "$vscode/src/webview-panels/remote-agents/types";
import {
  type CancelRemoteAgentHistoryStreamRequest,
  type CreateRemoteAgentRequestMessage,
  type CreateRemoteAgentResponseMessage,
  type DeleteRemoteAgentNotificationEnabledMessage,
  type DeleteRemoteAgentRequestMessage,
  type DeleteRemoteAgentResponseMessage,
  type DeleteSetupScriptRequest,
  type DeleteSetupScriptResponse,
  type EmptyMessage,
  type GetLastRemoteAgentSetupRequest,
  type GetLastRemoteAgentSetupResponse,
  type GetRemoteAgentChatHistoryRequest,
  type GetRemoteAgentChatHistoryResponse,
  type GetRemoteAgentNotificationEnabledMessage,
  type GetRemoteAgentNotificationEnabledResponseMessage,
  type GetRemoteAgentOverviewsRequest,
  type GetRemoteAgentOverviewsResponse,
  type ListSetupScriptsRequest,
  type ListSetupScriptsResponse,
  type RemoteAgentHistoryStreamRequest,
  type RemoteAgentHistoryStreamResponse,
  type OpenDiffInBufferMessage,
  type RemoteAgentChatRequestMessage,
  type RemoteAgentChatResponseMessage,
  type RemoteAgentInterruptRequestMessage,
  type RemoteAgentInterruptResponseMessage,
  type RemoteAgentNotifyReadyMessage,
  type RemoteAgentSshRequestMessage,
  type RemoteAgentSshResponseMessage,
  type RemoteAgentWorkspaceLogsRequestMessage,
  type RemoteAgentWorkspaceLogsResponseMessage,
  type RenameSetupScriptRequest,
  type RenameSetupScriptResponse,
  type SaveLastRemoteAgentSetupRequest,
  type SaveSetupScriptRequest,
  type SaveSetupScriptResponse,
  type SetRemoteAgentNotificationEnabledMessage,
  type WebViewMessage,
  type ReportRemoteAgentEventMessage,
  WebViewMessageType,
  type RemoteAgentPauseRequestMessage,
  type RemoteAgentResumeRequestMessage,
} from "$vscode/src/webview-providers/webview-messages";
import type { RemoteAgentCreationMetrics } from "$vscode/src/webview-providers/webview-messages";
import type { RemoteAgentSessionEventData } from "@augment-internal/sidecar-libs/src/metrics/types";
import {
  type RemoteAgentsHistoryStreamUpdate,
  RemoteAgentsHistoryStreamWithRetry,
} from "./remote-agents-state-model/remote-agents-history-stream-with-retry";

/**
 * RemoteAgentsClient provides a client for interacting with remote agents.
 *
 * This client handles all the communication with the extension via asyncMsgSender.
 */
export class RemoteAgentsClient {
  private _msgBroker: MessageBroker;
  private _activeRetryStreams = new Map<string, RemoteAgentsHistoryStreamWithRetry>();

  public static key = "remoteAgentsClient";

  constructor(msgBroker: MessageBroker) {
    this._msgBroker = msgBroker;
  }

  hasActiveHistoryStream(agentId: string) {
    return this._activeRetryStreams.has(agentId);
  }

  getActiveHistoryStream(agentId: string) {
    return this._activeRetryStreams.get(agentId);
  }

  get activeHistoryStreams() {
    return this._activeRetryStreams;
  }

  async sshToRemoteAgent(agentId: string): Promise<boolean> {
    const response = await this._msgBroker.send<
      RemoteAgentSshRequestMessage,
      RemoteAgentSshResponseMessage
    >(
      {
        type: WebViewMessageType.remoteAgentSshRequest,
        data: { agentId },
      },
      10000,
    );
    if (!response.data.success) {
      console.error("Failed to connect to remote agent:", response.data.error);
      return false;
    }
    return true;
  }

  async deleteRemoteAgent(agentId: string): Promise<boolean> {
    const response = await this._msgBroker.send<
      DeleteRemoteAgentRequestMessage,
      DeleteRemoteAgentResponseMessage
    >(
      {
        type: WebViewMessageType.deleteRemoteAgentRequest,
        data: { agentId },
      },
      10000,
    );

    return response.data.success;
  }

  showRemoteAgentHomePanel(): void {
    this._msgBroker.postMessage({
      type: WebViewMessageType.showRemoteAgentHomePanel,
    });
  }

  closeRemoteAgentHomePanel(): void {
    this._msgBroker.postMessage({
      type: WebViewMessageType.closeRemoteAgentHomePanel,
    });
  }

  /**
   * Get the notification settings for the given agent IDs, or all agents if undefined
   *
   * @param agentIds The IDs of the remote agents to get the notification settings for.
   * If undefined, get all settings for all available agents
   * @returns A map of agent ID to notification enabled setting
   */
  async getRemoteAgentNotificationEnabled(
    agentIds?: string[],
  ): Promise<RemoteAgentNotificationSettingsContext> {
    const response = await this._msgBroker.send<
      GetRemoteAgentNotificationEnabledMessage,
      GetRemoteAgentNotificationEnabledResponseMessage
    >({
      type: WebViewMessageType.getRemoteAgentNotificationEnabledRequest,
      data: { agentIds },
    });
    return response.data;
  }

  async setRemoteAgentNotificationEnabled(agentId: string, enabled: boolean): Promise<void> {
    await this._msgBroker.send<SetRemoteAgentNotificationEnabledMessage, EmptyMessage>({
      type: WebViewMessageType.setRemoteAgentNotificationEnabled,
      data: { agentId, enabled },
    });
  }

  async deleteRemoteAgentNotificationEnabled(agentId: string): Promise<void> {
    await this._msgBroker.send<DeleteRemoteAgentNotificationEnabledMessage, EmptyMessage>({
      type: WebViewMessageType.deleteRemoteAgentNotificationEnabled,
      data: { agentId },
    });
  }

  async notifyRemoteAgentReady(agentId: string): Promise<void> {
    await this._msgBroker.send<RemoteAgentNotifyReadyMessage, EmptyMessage>({
      type: WebViewMessageType.remoteAgentNotifyReady,
      data: { agentId },
    });
  }

  /**
   * Show the remote agent diff panel with the given options
   * @param opts Options for the diff panel
   * @param preloadedExplanation Optional preloaded explanation to include
   */
  showRemoteAgentDiffPanel(opts: IRemoteAgentDiffPanelOptions) {
    this._msgBroker.postMessage({
      type: WebViewMessageType.showRemoteAgentDiffPanel,
      data: opts,
    });
  }

  /**
   * Close the remote agent diff panel
   */
  closeRemoteAgentDiffPanel() {
    this._msgBroker.postMessage({
      type: WebViewMessageType.closeRemoteAgentDiffPanel,
    });
  }

  /**
   * Get the chat history for a remote agent
   * @param agentId The ID of the agent to get the chat history for
   * @param lastProcessedSequenceId The last processed sequence ID
   * @param timeout Optional timeout in milliseconds
   * @returns The chat history response
   */
  async getRemoteAgentChatHistory(
    agentId: string,
    lastProcessedSequenceId: number,
    timeout = 10000,
  ): Promise<GetRemoteAgentChatHistoryResponse> {
    return await this._msgBroker.send<
      GetRemoteAgentChatHistoryRequest,
      GetRemoteAgentChatHistoryResponse
    >(
      {
        type: WebViewMessageType.getRemoteAgentChatHistoryRequest,
        data: {
          agentId,
          lastProcessedSequenceId,
        },
      },
      timeout,
    );
  }

  /**
   * Send a chat message to a remote agent
   * @param agentId The ID of the agent to send the message to
   * @param requestDetails The details of the request
   * @param timeoutMs Optional timeout in milliseconds
   */
  async sendRemoteAgentChatRequest(
    agentId: string,
    requestDetails: RemoteAgentChatRequestDetails,
    timeoutMs = 90_000,
  ): Promise<RemoteAgentChatResponseMessage> {
    return this._msgBroker.send<RemoteAgentChatRequestMessage, RemoteAgentChatResponseMessage>(
      {
        type: WebViewMessageType.remoteAgentChatRequest,
        data: {
          agentId,
          requestDetails,
          timeoutMs: timeoutMs,
        },
      },
      timeoutMs,
    );
  }

  /**
   * Interrupt a remote agent
   * @param agentId The ID of the agent to interrupt
   * @param timeout Optional timeout in milliseconds
   */
  async interruptRemoteAgent(
    agentId: string,
    timeout = 10000,
  ): Promise<RemoteAgentInterruptResponseMessage> {
    return await this._msgBroker.send<
      RemoteAgentInterruptRequestMessage,
      RemoteAgentInterruptResponseMessage
    >(
      {
        type: WebViewMessageType.remoteAgentInterruptRequest,
        data: { agentId },
      },
      timeout,
    );
  }

  /**
   * Create a new remote agent
   * @param prompt The initial prompt for the agent
   * @param workspaceSetup The workspace setup for the agent
   * @param setupScript Optional setup script content
   * @param isSetupScriptAgent Optional flag indicating if this is a setup script agent
   * @param modelId Optional model ID to use
   * @param remoteAgentCreationMetrics Optional metrics indicating if the user changed repo or branch
   * @param timeout Optional timeout in milliseconds
   */
  async createRemoteAgent(
    prompt: string,
    workspaceSetup: RemoteAgentWorkspaceSetup,
    setupScript?: string,
    isSetupScriptAgent?: boolean,
    modelId?: string,
    remoteAgentCreationMetrics?: RemoteAgentCreationMetrics,
    timeout = 10000,
  ): Promise<CreateRemoteAgentResponseMessage> {
    return await this._msgBroker.send<
      CreateRemoteAgentRequestMessage,
      CreateRemoteAgentResponseMessage
    >(
      {
        type: WebViewMessageType.createRemoteAgentRequest,
        data: {
          prompt,
          workspaceSetup,
          setupScript,
          isSetupScriptAgent,
          modelId,
          remoteAgentCreationMetrics,
        },
      },
      timeout,
    );
  }

  /**
   * Get all remote agent overviews
   * @param timeout Optional timeout in milliseconds
   */
  async getRemoteAgentOverviews(timeout = 10000): Promise<GetRemoteAgentOverviewsResponse> {
    return await this._msgBroker.send<
      GetRemoteAgentOverviewsRequest,
      GetRemoteAgentOverviewsResponse
    >(
      {
        type: WebViewMessageType.getRemoteAgentOverviewsRequest,
      },
      timeout,
    );
  }

  /**
   * List all available setup scripts
   * @param timeout Optional timeout in milliseconds
   * @returns Array of setup scripts
   */
  async listSetupScripts(timeout = 5000) {
    return await this._msgBroker.send<ListSetupScriptsRequest, ListSetupScriptsResponse>(
      {
        type: WebViewMessageType.listSetupScriptsRequest,
      },
      timeout,
    );
  }

  /**
   * Save a setup script
   * @param name The name of the script
   * @param content The content of the script
   * @param location The location to save the script to
   * @param timeout Optional timeout in milliseconds
   * @returns Response with success status and path or error
   */
  async saveSetupScript(
    name: string,
    content: string,
    location: SetupScriptLocation,
    timeout = 5000,
  ) {
    return await this._msgBroker.send<SaveSetupScriptRequest, SaveSetupScriptResponse>(
      {
        type: WebViewMessageType.saveSetupScriptRequest,
        data: { name, content, location },
      },
      timeout,
    );
  }

  /**
   * Delete a setup script
   * @param name The name of the script to delete
   * @param location The location of the script
   * @param timeout Optional timeout in milliseconds
   * @returns Response with success status or error
   */
  async deleteSetupScript(name: string, location: SetupScriptLocation, timeout = 5000) {
    return await this._msgBroker.send<DeleteSetupScriptRequest, DeleteSetupScriptResponse>(
      {
        type: WebViewMessageType.deleteSetupScriptRequest,
        data: { name, location },
      },
      timeout,
    );
  }

  /**
   * Rename a setup script
   * @param oldName The current name of the script
   * @param newName The new name for the script
   * @param location The location of the script
   * @param timeout Optional timeout in milliseconds
   * @returns Response with success status, path or error
   */
  async renameSetupScript(
    oldName: string,
    newName: string,
    location: SetupScriptLocation,
    timeout = 5000,
  ) {
    return await this._msgBroker.send<RenameSetupScriptRequest, RenameSetupScriptResponse>(
      {
        type: WebViewMessageType.renameSetupScriptRequest,
        data: { oldName, newName, location },
      },
      timeout,
    );
  }

  /**
   * Get the workspace logs for a remote agent
   * @param agentId The ID of the agent to get the logs for
   * @param lastProcessedStep The last processed step number
   * @param lastProcessedSequenceId The last processed sequence ID
   * @param timeout Optional timeout in milliseconds
   * @returns The workspace logs response
   */
  async getRemoteAgentWorkspaceLogs(
    agentId: string,
    lastProcessedStep?: number,
    lastProcessedSequenceId?: number,
    timeout = 10000,
  ) {
    const result = await this._msgBroker.send<
      RemoteAgentWorkspaceLogsRequestMessage,
      RemoteAgentWorkspaceLogsResponseMessage
    >(
      {
        type: WebViewMessageType.remoteAgentWorkspaceLogsRequest,
        data: {
          agentId,
          lastProcessedStep,
          lastProcessedSequenceId,
        },
      },
      timeout,
    );
    return result;
  }

  /**
   * Save the last used remote agent setup preferences
   * @param gitRepo The git repository URL
   * @param gitBranch The git branch name
   * @param setupScriptPath The setup script path
   */
  async saveLastRemoteAgentSetup(
    gitRepoUrl: string | null,
    gitBranch: string | null,
    setupScriptPath: string | null,
  ): Promise<EmptyMessage> {
    return await this._msgBroker.send<SaveLastRemoteAgentSetupRequest, EmptyMessage>({
      type: WebViewMessageType.saveLastRemoteAgentSetupRequest,
      data: {
        lastRemoteAgentGitRepoUrl: gitRepoUrl,
        lastRemoteAgentGitBranch: gitBranch,
        lastRemoteAgentSetupScript: setupScriptPath,
      },
    });
  }

  /**
   * Get the last used remote agent setup preferences
   * @returns Response with the last used git branch and setup script
   */
  async getLastRemoteAgentSetup(): Promise<GetLastRemoteAgentSetupResponse> {
    return await this._msgBroker.send<
      GetLastRemoteAgentSetupRequest,
      GetLastRemoteAgentSetupResponse
    >({
      type: WebViewMessageType.getLastRemoteAgentSetupRequest,
    });
  }

  /**
   * Start streaming remote agent history updates.
   *
   * @param agentId The ID of the agent to get history for
   * @param streamId Unique ID for the stream
   * @param lastProcessedSequenceId The last processed sequence ID
   * @param timeoutMs Timeout for starting the stream in milliseconds
   * @param streamTimeoutMs Timeout for the entire stream in milliseconds
   * @returns An async generator that yields history updates
   */
  private async *startRemoteAgentHistoryStream(
    agentId: string,
    streamId: string,
    lastProcessedSequenceId: number,
    timeoutMs: number = 60000,
    streamTimeoutMs: number = 300000,
  ): AsyncGenerator<GetRemoteAgentHistoryStreamResponse> {
    const request: RemoteAgentHistoryStreamRequest = {
      type: WebViewMessageType.remoteAgentHistoryStreamRequest,
      data: {
        streamId,
        agentId,
        lastProcessedSequenceId,
      },
    };

    const stream = this._msgBroker.stream<
      RemoteAgentHistoryStreamRequest,
      RemoteAgentHistoryStreamResponse
    >(request, timeoutMs, streamTimeoutMs);

    for await (const response of stream) {
      yield response.data;
    }
  }

  /**
   * Start streaming remote agent history updates with retry capability.
   *
   * @param agentId The ID of the agent to get history for
   * @param lastProcessedSequenceId The last processed sequence ID
   * @param maxRetries Maximum number of retry attempts
   * @param baseDelay Base delay between retries in milliseconds
   * @returns An async generator that yields history updates
   */
  async *startRemoteAgentHistoryStreamWithRetry(
    agentId: string,
    lastProcessedSequenceId: number,
    maxRetries: number = 5,
    baseDelay: number = 4000,
  ): AsyncGenerator<RemoteAgentsHistoryStreamUpdate> {
    // Create a new RemoteAgentsHistoryStreamWithRetry instance
    const historyStream = new RemoteAgentsHistoryStreamWithRetry(
      agentId,
      lastProcessedSequenceId,
      (id, streamId, seqId) => this.startRemoteAgentHistoryStream(id, streamId, seqId),
      (streamId) => this._closeRemoteAgentHistoryStream(streamId),
      maxRetries,
      baseDelay,
    );

    this._activeRetryStreams.get(agentId)?.cancel();
    this._activeRetryStreams.set(agentId, historyStream);

    try {
      yield* historyStream.getStream();
    } finally {
      if (!historyStream.isCancelled) {
        // Stream completed naturally, remove it from the map.
        // If it was cancelled, the map will be cleared by the cancellation logic.
        this._activeRetryStreams.delete(agentId);
      }
    }
  }

  /**
   * Cancel an active remote agent history stream.
   *
   * @param agentId The ID of the stream to cancel
   */
  cancelRemoteAgentHistoryStream(agentId: string) {
    const historyStream = this._activeRetryStreams.get(agentId);
    if (historyStream) {
      historyStream.cancel(); // This will also call _closeRemoteAgentHistoryStream
      this._activeRetryStreams.delete(agentId);
    }
  }

  /**
   * Close a remote agent history stream on the extension side.
   *
   * @param agentId The ID of the agent whose stream to cancel
   */
  private async _closeRemoteAgentHistoryStream(streamId: string) {
    await this._msgBroker.send<CancelRemoteAgentHistoryStreamRequest, EmptyMessage>({
      type: WebViewMessageType.cancelRemoteAgentHistoryStreamRequest,
      data: { streamId },
    });
  }

  /**
   * Cancel all active remote agent history streams.
   */
  cancelAllRemoteAgentHistoryStreams() {
    this._activeRetryStreams.forEach((historyStream) => {
      historyStream.cancel();
    });
    this._activeRetryStreams.clear();
  }

  dispose() {
    this.cancelAllRemoteAgentHistoryStreams();
  }

  /**
   * Get the pinned status for all remote agents from the global store
   * @returns A map of agent ID to pinned status
   */
  async getPinnedAgentsFromStore(): Promise<RemoteAgentPinnedStatusContext> {
    try {
      const response = await this._msgBroker.send<
        WebViewMessage,
        {
          type: WebViewMessageType.getRemoteAgentPinnedStatusResponse;
          data: RemoteAgentPinnedStatusContext;
        }
      >({
        type: WebViewMessageType.getRemoteAgentPinnedStatusRequest,
        data: {},
      });
      return response.data;
    } catch (error) {
      console.error("Failed to get pinned agents from store:", error);
      return {};
    }
  }

  /**
   * Save the pinned status for a remote agent to the global store
   * @param agentId The ID of the agent to set the pinned status for
   * @param isPinned Whether the agent should be pinned
   */
  async savePinnedAgentToStore(agentId: string, isPinned: boolean): Promise<void> {
    try {
      await this._msgBroker.send<WebViewMessage, EmptyMessage>({
        type: WebViewMessageType.setRemoteAgentPinnedStatus,
        data: { agentId, isPinned },
      });
    } catch (error) {
      console.error("Failed to save pinned agent to store:", error);
    }
  }

  /**
   * Delete the pinned status for a remote agent from the global store
   * @param agentId The ID of the agent to delete the pinned status for
   */
  async deletePinnedAgentFromStore(agentId: string): Promise<void> {
    try {
      await this._msgBroker.send<WebViewMessage, EmptyMessage>({
        type: WebViewMessageType.deleteRemoteAgentPinnedStatus,
        data: { agentId },
      });
    } catch (error) {
      console.error("Failed to delete pinned agent from store:", error);
    }
  }

  async openDiffInBuffer(
    oldContents: string,
    newContents: string,
    filePath: string,
  ): Promise<EmptyMessage> {
    return await this._msgBroker.send<OpenDiffInBufferMessage, EmptyMessage>({
      type: WebViewMessageType.openDiffInBuffer,
      data: { oldContents, newContents, filePath },
    });
  }

  async pauseRemoteAgentWorkspace(agentId: string): Promise<EmptyMessage> {
    return await this._msgBroker.send<RemoteAgentPauseRequestMessage, EmptyMessage>(
      {
        type: WebViewMessageType.remoteAgentPauseRequest,
        data: { agentId },
      },
      30000,
    );
  }

  async resumeRemoteAgentWorkspace(agentId: string): Promise<EmptyMessage> {
    return await this._msgBroker.send<RemoteAgentResumeRequestMessage, EmptyMessage>(
      {
        type: WebViewMessageType.remoteAgentResumeRequest,
        data: { agentId },
      },
      90_000, // 90 seconds
    );
  }

  async reportRemoteAgentEvent(eventData: RemoteAgentSessionEventData): Promise<void> {
    await this._msgBroker.send<ReportRemoteAgentEventMessage, EmptyMessage>({
      type: WebViewMessageType.reportRemoteAgentEvent,
      data: eventData,
    });
  }
}

import { type RemoteAgentsModel } from "./remote-agents-model";

export interface IMessageRenderOptions {
  doHideThreadSelector?: boolean;
  doHideStatusBars?: boolean;
  doHideSlashActions?: boolean;
  doHideAtMentions?: boolean;
  doHideNewThreadButton?: boolean;
  doHideMultimodalActions?: boolean;
  doHideContextBar?: boolean;
  doShowTurnSelector?: boolean;
}

/** The default message render options for the chat when not interacting with remote agents */
export const DEFAULT_MESSAGE_RENDER_OPTIONS: IMessageRenderOptions = {
  doHideStatusBars: false,
  doHideSlashActions: false,
  doHideAtMentions: false,
  doHideNewThreadButton: false,
  doHideMultimodalActions: false,
  doHideContextBar: false,
  doShowTurnSelector: false,
};

/** The message render options for the chat when interacting with remote agents */
export const REMOTE_MESSAGE_RENDER_OPTIONS: IMessageRenderOptions = {
  doHideStatusBars: true,
  doHideSlashActions: true,
  doHideAtMentions: true,
  doHideNewThreadButton: true,
  doHideMultimodalActions: true,
  doHideContextBar: true,
  doShowTurnSelector: true,
};

export const SELECTED_TURN_INDEX_CONTEXT_KEY = "selectedTurnIndex";

export function getMessageRenderOptions(
  remoteAgentsModel: RemoteAgentsModel | undefined,
  isRemoteAgentWindow: boolean,
): IMessageRenderOptions {
  let options = DEFAULT_MESSAGE_RENDER_OPTIONS;

  if (remoteAgentsModel?.isActive) {
    options = REMOTE_MESSAGE_RENDER_OPTIONS;

    if (isRemoteAgentWindow) {
      // Allow @ mentions in remote agent window
      options.doHideAtMentions = false;
    }
  }

  return options;
}

import { type AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import {
  type ApplyChangesRequestMessage,
  type ApplyChangesResponseMessage,
  type DiffDescriptionsRequestMessage,
  type DiffDescriptionsResponseMessage,
  type DiffExplanationRequestMessage,
  type DiffExplanationResponseMessage,
  type DiffGroupChangesRequestMessage,
  type DiffGroupChangesResponseMessage,
  WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";
import { writable } from "svelte/store";

/**
 * Model that handles diff operations for remote agent changes.
 */
export class RemoteAgentDiffOpsModel {
  public static key = "remoteAgentsDiffOpsModel"; // for svelte context

  private _applyingFilePaths = writable<string[]>([]);
  private _appliedFilePaths = writable<string[]>([]);

  constructor(private readonly _asyncMsgSender: AsyncMsgSender) {}

  public get applyingFilePaths() {
    let result: string[] = [];
    this._applyingFilePaths.subscribe((value) => {
      result = value;
    })();
    return result;
  }

  public get appliedFilePaths() {
    let result: string[] = [];
    this._appliedFilePaths.subscribe((value) => {
      result = value;
    })();
    return result;
  }

  /**
   * Get explanation for diff changes
   * @param changedFiles The files to get explanations for
   * @param apikey Optional API key for the LLM
   * @param timeoutMs Optional timeout in milliseconds (default: 30000)
   */
  async getDiffExplanation(changedFiles: any[], apikey?: string, timeoutMs: number = 30000) {
    try {
      const response = await this._asyncMsgSender.send<
        DiffExplanationRequestMessage,
        DiffExplanationResponseMessage
      >(
        {
          type: WebViewMessageType.diffExplanationRequest,
          data: {
            changedFiles,
            apikey,
          },
        },
        timeoutMs,
      );
      return response.data.explanation;
    } catch (err) {
      console.error("Failed to get diff explanation:", err);
      return [];
    }
  }

  /**
   * Group changes by file using LLM
   */
  async groupChanges(changedFiles: any[], changesById = false, apikey?: string) {
    try {
      const response = await this._asyncMsgSender.send<
        DiffGroupChangesRequestMessage,
        DiffGroupChangesResponseMessage
      >({
        type: WebViewMessageType.diffGroupChangesRequest,
        data: {
          changedFiles,
          changesById,
          apikey,
        },
      });
      return response.data.groupedChanges;
    } catch (err) {
      console.error("Failed to group changes:", err);
      return [];
    }
  }

  /**
   * Get descriptions for grouped changes
   */
  async getDescriptions(groupedChanges: any[], apikey?: string) {
    try {
      const response = await this._asyncMsgSender.send<
        DiffDescriptionsRequestMessage,
        DiffDescriptionsResponseMessage
      >({
        type: WebViewMessageType.diffDescriptionsRequest,
        data: {
          groupedChanges,
          apikey,
        },
      });
      return response.data.explanation;
    } catch (err) {
      console.error("Failed to get descriptions:", err);
      return [];
    }
  }

  /**
   * Apply changes to a file
   */
  async applyChanges(path: string, originalCode: string, newCode: string): Promise<void> {
    this._applyingFilePaths.update((paths) => [...paths.filter((item) => item !== path), path]);

    try {
      const response = await this._asyncMsgSender.send<
        ApplyChangesRequestMessage,
        ApplyChangesResponseMessage
      >(
        {
          type: WebViewMessageType.applyChangesRequest,
          data: {
            path,
            originalCode,
            newCode,
          },
        },
        30000, // 30 second timeout
      );
      if (response.data.success) {
        this._appliedFilePaths.update((paths) => [...paths.filter((item) => item !== path), path]);
      }
    } catch (err) {
      console.error("applyChanges error", err);
    } finally {
      this._applyingFilePaths.update((paths) => paths.filter((item) => item !== path));
    }
  }
}

import { getContext, setContext } from "svelte";
import { writable, type Writable } from "svelte/store";

// Context key for the focused file path
export const FOCUSED_PATH_KEY = Symbol("focusedPath");

// Create and set the focused path context
export function setFocusedPathContext(initialPath: string | null = null): Writable<string | null> {
  const store = writable<string | null>(initialPath);
  setContext(FOCUSED_PATH_KEY, store);
  return store;
}

// Get the focused path context
export function getFocusedPathContext(): Writable<string | null> {
  return getContext(FOCUSED_PATH_KEY);
}

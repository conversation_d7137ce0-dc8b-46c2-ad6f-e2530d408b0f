<script lang="ts">
  import { type ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  // Import design system components
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import Terminal from "$common-webviews/src/design-system/icons/augment/terminal.svelte";
  import RegularArrowUpRightFromSquareIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-up-right-from-square.svg?component";
  import RegularPenIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pen.svg?component";
  import RegularTrashIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/trash.svg?component";
  import MagicWand from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/wand-magic-sparkles.svg?component";
  import Edit from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pen-to-square.svg?component";
  import Reload from "$common-webviews/src/design-system/icons/reload.svelte";
  import MagnifyingGlass from "$common-webviews/src/design-system/icons/magnifying-glass.svelte";
  import {
    type SetupScript,
    type SetupScriptLocation,
  } from "$vscode/src/utils/remote-agent-setup/types";
  import { createEventDispatcher, getContext, onMount } from "svelte";
  import { RemoteAgentsModel } from "../../models/remote-agents-model";
  import ScriptItem from "./ScriptItem.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import ArrowRight from "$common-webviews/src/design-system/icons/arrow-right.svelte";
  import SearchableDropdown from "../SearchableDropdown.svelte";
  import TextCombo from "$common-webviews/src/common/components/TextCombo.svelte";

  export let errorMessage = "";
  export let isLoading = false;
  export let lastUsedScriptPath: string | null = null;
  export let disableNewAgentCreation = false;

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const dispatch = createEventDispatcher<{
    setupScriptChange: { script: SetupScript | null };
  }>();

  const chatModel = getContext<ChatModel>("chatModel");
  const extensionClient = chatModel.extensionClient;
  const openFile = (script: { path: string; location: SetupScriptLocation }) => {
    extensionClient.openFile({
      repoRoot: "",
      pathName: script.path,
      allowOutOfWorkspace: true,
      openLocalUri: script.location === "home",
    });
  };

  // Starts a new agent with the SETUP_MODE prompt
  const createNewScript = async () => {
    try {
      // Set the is_setup_script_agent flag to true when creating a setup script agent
      const draft = remoteAgentsModel.newAgentDraft;
      if (draft) {
        remoteAgentsModel.setNewAgentDraft({
          ...draft,
          isSetupScriptAgent: true,
        });
      }

      const agentId = await remoteAgentsModel.createRemoteAgentFromDraft("SETUP_MODE");
      if (agentId) {
        remoteAgentsModel.setCurrentAgent(agentId);
      }
      return agentId;
    } catch (error) {
      console.error("Failed to select setup script generation:", error);
    }
  };

  // Creates a manual setup script template in the home directory
  const createManualScript = async () => {
    try {
      // Default script name
      const scriptName = "setup.sh";

      // Template content with explanatory comments
      const templateContent = `#!/bin/bash

# Setup Script for Remote Agent Environment
#
# This script installs dependencies and configures the environment for your project.
# It runs with sudo privileges when needed.
#
# Examples:
# sudo apt-get update && sudo apt-get install -y package-name
# pip install package-name
# npm install -g package-name
# export ENV_VAR=value

# Add your commands below:

`;

      // Save the script to the home directory
      const result = await remoteAgentsModel.saveSetupScript(scriptName, templateContent, "home");

      if (result.success && result.path) {
        // Reload the scripts list
        await loadScripts();

        // Find the newly created script
        const newScript = scripts.find((script) => script.path === result.path);
        if (newScript) {
          // Select the new script
          selectOption(newScript);

          // Open the file in the editor
          openFile(newScript);
        }
      } else {
        console.error("Failed to create manual setup script:", result.error);
        errorMessage = `Failed to create manual setup script: ${result.error || "Unknown error"}`;
      }
    } catch (error) {
      console.error("Error creating manual setup script:", error);
      errorMessage = `Error creating manual setup script: ${error instanceof Error ? error.message : String(error)}`;
    }
  };

  let scripts: SetupScript[] = [];
  let selectedScript: SetupScript | null = remoteAgentsModel.newAgentDraft?.setupScript ?? null;
  let searchValue = "";
  let scriptBeingRenamed: SetupScript | null = null;
  let displayedScripts: (SetupScript | null)[] = scripts;
  let isInitialLoad = true;

  $: hasError = errorMessage !== "";

  // Reactive computation for auto-generate button state and tooltip
  $: autoGenerateDisabled =
    disableNewAgentCreation ||
    remoteAgentsModel.newAgentDraft?.isDisabled ||
    !remoteAgentsModel.newAgentDraft;
  $: autoGenerateTooltip = (() => {
    if (!remoteAgentsModel.newAgentDraft) {
      return "Please select a repository and branch first";
    }
    if (remoteAgentsModel.newAgentDraft?.isDisabled) {
      return "Please resolve the issues with your workspace selection";
    }
    if (disableNewAgentCreation) {
      return "Agent limit reached or other restrictions apply";
    }
    return "An AI agent will automatically generate a setup script for your project.";
  })();

  // Filter scripts based on search
  $: {
    if (searchValue.trim() !== "") {
      // Include the "Use basic environment" option if it matches the search
      const basicOption = "Use basic environment".toLowerCase().includes(searchValue.toLowerCase());

      // Filter scripts based on search
      const filteredScripts = scripts.filter(
        (script) =>
          script.name.toLowerCase().includes(searchValue.toLowerCase()) ||
          script.path.toLowerCase().includes(searchValue.toLowerCase()),
      );

      // Add the "No Setup" option at the beginning if it matches the search
      displayedScripts = basicOption ? [null, ...filteredScripts] : filteredScripts;
    } else {
      // Always include the "Use basic environment" option when no search is active
      displayedScripts = [null, ...scripts];
    }
  }

  const errors = {
    noScriptsFound:
      "No setup scripts found. You can create one in ~/.augment/env/, <git root>/.augment/env/, or <workspace root>/.augment/env/.",
    failedToFetchScripts: "Failed to fetch setup scripts. Please try again.",
  };

  onMount(async () => {
    await loadScripts();
    // Only use the draft script if we don't have a lastUsedScriptPath that was found
    if (lastUsedScriptPath === null) {
      // If lastUsedScriptPath is explicitly null, select "No Setup"
      selectOption(null);
    } else if (remoteAgentsModel.newAgentDraft?.setupScript && !selectedScript) {
      selectOption(remoteAgentsModel.newAgentDraft.setupScript);
    }
  });

  async function loadScripts() {
    errorMessage = "";
    try {
      const previouslySelectedPath = selectedScript?.path;
      scripts = await remoteAgentsModel.listSetupScripts();

      // Only set selectedScript during initial load or if it's not already set
      if (isInitialLoad) {
        // If we have a lastUsedScriptPath and it's not null, try to find that script
        if (lastUsedScriptPath && scripts.length > 0) {
          const savedScript = scripts.find((script) => script.path === lastUsedScriptPath);
          if (savedScript) {
            selectedScript = savedScript;
            updateSelectedScript();
          }
        } else if (lastUsedScriptPath === null) {
          // If lastUsedScriptPath is explicitly null, select "No Setup"
          selectedScript = null;
          updateSelectedScript();
        }
      } else if (previouslySelectedPath) {
        // If we already had a selection, make sure it references the updated script object
        const currentScript = scripts.find((script) => script.path === previouslySelectedPath);
        if (currentScript) {
          selectedScript = currentScript;
        }
      }

      isInitialLoad = false;

      // Check for error conditions
      if (scripts.length === 0) {
        errorMessage = errors.noScriptsFound;
      } else {
        errorMessage = "";
      }
    } catch (error) {
      console.error("Error fetching setup scripts:", error);
      errorMessage = errors.failedToFetchScripts;
    }
  }

  async function startDeleteScript(script: SetupScript, event?: MouseEvent) {
    if (event) {
      event.stopPropagation();
    }

    try {
      const result = await remoteAgentsModel.deleteSetupScript(script.name, script.location);
      if (result.success) {
        // If the deleted script was selected, reset selection
        if (selectedScript?.path === script.path) {
          selectOption(null);
        }
        // Reload scripts list
        await loadScripts();
      } else {
        console.error("Failed to delete script:", result.error);
        setError(`Failed to delete script: ${result.error || "Unknown error"}`);
      }
    } catch (error) {
      console.error("Error deleting script:", error);
      setError(`Error deleting script: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async function startRenameScript(script: SetupScript, event?: MouseEvent) {
    if (event) {
      event.stopPropagation();
    }
    scriptBeingRenamed = script;
  }

  async function handleRename(
    script: SetupScript,
    event: CustomEvent<{ oldName: string; newName: string }>,
  ) {
    const { oldName, newName } = event.detail;

    try {
      const result = await remoteAgentsModel.renameSetupScript(oldName, newName, script.location);
      if (result.success) {
        // Always reload scripts and select the renamed script
        await loadScripts();
        const updatedScript = scripts.find((s) => s.path === result.path);
        if (updatedScript) {
          // Select the script but don't close the dropdown
          selectOption(updatedScript);
        }
      } else {
        console.error("Failed to rename script:", result.error);
        setError(`Failed to rename script: ${result.error || "Unknown error"}`);
      }
    } catch (error) {
      console.error("Error renaming script:", error);
      setError(`Error renaming script: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      cancelRename();
    }
  }

  function cancelRename() {
    scriptBeingRenamed = null;
  }

  function setError(message: string) {
    errorMessage = message;
  }

  function handleSearchChange(value: string) {
    searchValue = value;
  }

  function handleSelect(script: SetupScript | null) {
    selectOption(script);
  }

  function handleOpenChange(open: boolean) {
    if (open) {
      loadScripts();
      searchValue = "";
    }
  }

  async function selectOption(script: SetupScript | null) {
    selectedScript = script;
    updateSelectedScript();
    remoteAgentsModel.saveLastRemoteAgentSetup(null, null, selectedScript?.path || null);
  }

  function updateSelectedScript() {
    dispatch("setupScriptChange", {
      script: selectedScript,
    });
  }

  $: getTitle = () => {
    if (isLoading) {
      return "...";
    }
    if (selectedScript) {
      // Special case for the generate option
      if (selectedScript.isGenerateOption) {
        return selectedScript.name;
      }

      // Use the location property directly
      switch (selectedScript.location) {
        case "home":
          return "~/.augment/env/" + selectedScript.name;
        case "git":
        case "workspace":
        default:
          return selectedScript.path;
      }
    }
    return "Use basic environment";
  };

  $: title = getTitle();
  $: isTitlePath = !!selectedScript?.path;
  $: titleFilename = isTitlePath ? title.split("/").pop()! : title;
  $: titleBasePath = isTitlePath ? title.slice(0, title.lastIndexOf("/")) : "";
</script>

<div class="c-setup-script-selector">
  <div class="c-setup-script-selector__content">
    <!-- Show script selector only when there's no error or when there's a "no scripts found" error -->
    {#if !hasError || errorMessage === errors.noScriptsFound}
      <div class="c-setup-script-selector__script-line-container">
        <div class="c-setup-script-selector__script-line">
          <SearchableDropdown
            placeholder="Search scripts..."
            {isLoading}
            disabled={false}
            bind:searchValue
            items={displayedScripts}
            selectedItem={selectedScript}
            itemLabelFn={(script) => script?.name || ""}
            itemKeyFn={(script) => `${script?.path}-${script?.location}-${script?.name}`}
            isItemSelected={(item, selectedItem) => {
              // Both null means "Use basic environment" is selected
              if (item === null && selectedItem === null) return true;
              // For regular scripts, compare by path
              if (item && selectedItem) return item.path === selectedItem.path;
              return false;
            }}
            noItemsLabel="No scripts found"
            loadingLabel="Loading scripts..."
            on:openChange={(e) => handleOpenChange(e.detail)}
            on:search={(e) => handleSearchChange(e.detail)}
            on:select={(e) => handleSelect(e.detail)}
          >
            <div slot="title">
              {#if isTitlePath}
                <TextCombo>
                  <span slot="text">{titleFilename}</span>
                  <span slot="grayText">{titleBasePath}</span>
                </TextCombo>
              {:else}
                <TextCombo>
                  <span slot="text">{title}</span>
                </TextCombo>
              {/if}
            </div>
            <Terminal slot="icon" />
            <MagnifyingGlass slot="searchIcon" />

            <svelte:fragment slot="item" let:item>
              {#if item === null}
                <!-- "Use basic environment" option -->
                <div class="c-setup-script-selector__basic-option">
                  <Terminal />
                  Use basic environment
                </div>
              {:else}
                <ScriptItem
                  name={item.name}
                  path={item.path}
                  isPath
                  isRenaming={scriptBeingRenamed?.path === item.path}
                  isSelected={!!(selectedScript && selectedScript.path === item.path)}
                  on:rename={(e) => handleRename(item, e)}
                  on:cancelRename={cancelRename}
                >
                  <TextTooltipAugment content="Open script in editor">
                    <IconButtonAugment
                      size={1}
                      variant="ghost-block"
                      class="c-setup-script-selector__action-button"
                      on:click={(e) => {
                        e.stopPropagation();
                        openFile(item);
                        selectOption(item);
                      }}
                    >
                      <RegularArrowUpRightFromSquareIcon />
                    </IconButtonAugment>
                  </TextTooltipAugment>

                  <TextTooltipAugment content="Rename script">
                    <IconButtonAugment
                      size={1}
                      variant="ghost-block"
                      class="c-setup-script-selector__action-button"
                      on:click={(e) => {
                        e.stopPropagation();
                        startRenameScript(item);
                      }}
                    >
                      <RegularPenIcon />
                    </IconButtonAugment>
                  </TextTooltipAugment>

                  <TextTooltipAugment content="Delete script">
                    <IconButtonAugment
                      size={1}
                      variant="ghost-block"
                      class="c-setup-script-selector__action-button"
                      on:click={(e) => {
                        e.stopPropagation();
                        startDeleteScript(item);
                      }}
                    >
                      <RegularTrashIcon />
                    </IconButtonAugment>
                  </TextTooltipAugment>
                </ScriptItem>
              {/if}
            </svelte:fragment>
          </SearchableDropdown>

          <div class="c-setup-script-selector__action-buttons">
            <TextTooltipAugment content={autoGenerateTooltip}>
              <ButtonAugment
                variant="soft"
                color="neutral"
                size={1}
                on:click={createNewScript}
                disabled={autoGenerateDisabled}
              >
                <MagicWand slot="iconLeft" />
                Auto-generate<span class="c-setup-script-selector__long-text"> a script</span>
                <ArrowRight slot="iconRight" />
              </ButtonAugment>
            </TextTooltipAugment>
            <TextTooltipAugment
              content="Open a new file for you to write a setup script that you can edit directly."
            >
              <ButtonAugment
                variant="soft"
                color="neutral"
                size={1}
                on:click={createManualScript}
                highlight={false}
              >
                <Edit slot="iconLeft" />
                Write <span class="c-setup-script-selector__long-text"> a script</span>by hand
                <ArrowRight slot="iconRight" />
              </ButtonAugment>
            </TextTooltipAugment>
          </div>
        </div>
      </div>
    {/if}
    {#if hasError && errorMessage !== errors.noScriptsFound}
      <!-- Show error callout for critical errors -->
      <div class="c-setup-script-selector__error">
        <CalloutAugment color="warning" variant="soft" size={2}>
          <div class="c-setup-script-selector__error-content">
            <div class="c-setup-script-selector__error-message">
              {errorMessage}
            </div>
            <ButtonAugment
              variant="ghost"
              color="warning"
              size={1}
              on:click={loadScripts}
              loading={isLoading}
            >
              <span slot="iconLeft"><Reload /></span>
              Refresh
            </ButtonAugment>
          </div>
        </CalloutAugment>
      </div>
    {/if}
  </div>
</div>

<style>
  .c-setup-script-selector {
    display: flex;
    width: 100%;
    flex-direction: column;
  }

  .c-setup-script-selector :global(button.c-base-btn.c-dropdown-menu-augment__item) {
    padding: 0;
  }

  .c-setup-script-selector__content {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .c-setup-script-selector__error {
    max-width: 600px;
    margin-top: var(--ds-spacing-3);
  }

  :global(.c-setup-script-selector__create-option .c-dropdown-menu-augment__item) {
    color: var(--ds-color-accent-11);
    font-weight: 500;
    width: 100%;
  }

  .c-setup-script-selector__script-line-container {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    gap: var(--ds-spacing-2);
  }

  .c-setup-script-selector__script-line {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    gap: var(--ds-spacing-1);
  }

  .c-setup-script-selector :global(.c-dropdown-menu-augment__separator) {
    margin: 0 var(--ds-spacing-1) var(--ds-spacing-3);
  }

  .c-setup-script-selector__script-line
    :global(
      .c-card-button
        > .l-tooltip-trigger
        > [data-tippy-root]
        > .tippy-box
        > .tippy-content
        > .l-tooltip-contents
    ) {
    margin-left: calc(var(--remote-agent-setup-horizontal-padding) + var(--ds-spacing-1));
    width: 100%;
  }

  .c-setup-script-selector
    :global(.c-card-button > .l-tooltip-trigger > [data-tippy-root] > .tippy-box) {
    max-width: unset !important; /** Tippy sets a max-width of 350px by default on the element */
    width: calc(
      100vw - var(--remote-agent-setup-horizontal-padding) -
        var(--remote-agent-setup-horizontal-padding) - var(--ds-spacing-4) - var(--ds-spacing-1)
    );
    min-width: 250px;
  }

  @media (max-width: 400px) {
    .c-setup-script-selector__script-line :global(.l-tooltip-contents) {
      margin-left: 0;
    }
  }

  .c-setup-script-selector :global(.c-setup-script-selector__action-button svg) {
    width: 12px;
  }

  .c-setup-script-selector__action-buttons {
    display: flex;
    width: 100%;
    gap: var(--ds-spacing-2);
    padding: var(--ds-spacing-1) 0;
  }

  .c-setup-script-selector__long-text {
    display: none;
  }

  @media (min-width: 466px) {
    .c-setup-script-selector__long-text {
      display: inline-block;
      margin-left: 0.37em;
      margin-right: 0.37em;
    }
  }

  .c-setup-script-selector__action-buttons :global(svg) {
    flex: none;
  }
  .c-setup-script-selector__action-buttons :global(.l-tooltip-trigger) {
    flex: 1;
    width: 100%;
  }
  .c-setup-script-selector__action-buttons :global([data-tippy-root]) {
    pointer-events: none;
  }
  .c-setup-script-selector__action-buttons :global(.c-base-btn) {
    width: 100%;
    padding: var(--ds-spacing-1);
  }
  @media (max-width: 366px) {
    .c-setup-script-selector__action-buttons {
      flex-direction: column;
    }
  }

  /* Fix button color in highlighted dropdown items */
  :global(.c-dropdown-menu-augment__item--highlighted .c-setup-script-selector__action-button):not(
      :hover
    ),
  :global(.c-dropdown-menu-augment__item:hover .c-setup-script-selector__action-button):not(
      :hover
    ) {
    color: inherit;
  }

  :global(.c-dropdown-menu-augment__item) {
    width: 100%;
  }

  .c-setup-script-selector__error-content {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
  }

  .c-setup-script-selector__error-message {
    margin-bottom: var(--ds-spacing-1);
  }

  .c-setup-script-selector :global(.c-searchable-dropdown) {
    width: 100%;
  }

  .c-setup-script-selector :global(.l-dropdown-menu-augment__contents) {
    max-height: 300px;
    min-height: 40px;
    overflow-y: auto;
  }

  /* prevent nested scrollbars */
  .c-setup-script-selector :global(.c-searchable-dropdown__content) {
    max-height: none;
  }

  .c-setup-script-selector__basic-option {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    min-width: 50px;
    padding: var(--ds-spacing-2);
    gap: var(--ds-spacing-2);
  }

  /* Make the basic option match the hover state of script items */
  :global(.c-dropdown-menu-augment__item.c-dropdown-menu-augment__item--highlighted)
    .c-setup-script-selector__basic-option,
  :global(.c-dropdown-menu-augment__item:hover) .c-setup-script-selector__basic-option {
    background-color: var(--gray-a4);
  }

  .c-setup-script-selector :global(.l-dropdown-menu-augment__contents) {
    max-height: 300px;
    min-height: 40px;
    overflow-y: auto;
  }

  .c-setup-script-selector :global(.c-card-button),
  .c-setup-script-selector :global(.c-searchable-dropdown) {
    max-width: 100%;
    width: 100%;
  }

  .c-setup-script-selector :global(.l-tooltip-contents) {
    margin-left: calc(var(--remote-agent-setup-horizontal-padding) + var(--ds-spacing-1));
    width: 100%;
  }

  .c-setup-script-selector :global(.tippy-box) {
    max-width: unset !important;
    width: auto;
    max-width: calc(
      100vw - var(--remote-agent-setup-horizontal-padding) -
        var(--remote-agent-setup-horizontal-padding) - var(--ds-spacing-4) - var(--ds-spacing-1)
    );
    min-width: 250px;
  }

  @media (max-width: 400px) {
    .c-setup-script-selector :global(.l-tooltip-contents) {
      margin-left: 0;
    }
  }
</style>

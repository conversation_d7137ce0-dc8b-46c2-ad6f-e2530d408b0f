<script lang="ts">
  import {
    RemoteAgentStatus,
    RemoteAgentWorkspaceStatus,
  } from "$vscode/src/remote-agent-manager/types";
  import { getStatusText, getWorkspaceStatusText } from "../utils";
  import { slide } from "svelte/transition";

  export let status: RemoteAgentStatus;
  export let workspaceStatus: RemoteAgentWorkspaceStatus;
  export let isExpanded: boolean = false;

  // Determine if we should show the label
  let isHovered = false;
  $: doShowLabel = isExpanded || isHovered;

  /** The text to display for the status */
  let statusText: string;
  /** The class to apply to the status dot and label */
  let statusClass: string;
  $: {
    if (
      workspaceStatus === RemoteAgentWorkspaceStatus.workspaceRunning ||
      status === RemoteAgentStatus.agentFailed
    ) {
      // Show the agent's status when the workspace is active or if the agent has failed
      statusText = getStatusText(status);
      statusClass = status.toString();
    } else {
      // When the workspace is not running, show the workspace status
      statusText = getWorkspaceStatusText(workspaceStatus);
      statusClass = "paused";
    }
  }
</script>

<div
  class="status-indicator-container"
  on:mouseenter={() => (isHovered = true)}
  on:mouseleave={() => (isHovered = false)}
  role="status"
  aria-label="Agent status: {statusText}"
  title="Status: {statusText}"
>
  <div class="status-dot status-dot--{statusClass}"></div>

  {#if doShowLabel}
    <div
      class="status-label status-label--{statusClass}"
      transition:slide={{ duration: 100, axis: "x" }}
    >
      {statusText}
    </div>
  {/if}
</div>

<style>
  .status-indicator-container {
    position: relative;
    display: flex;
    align-items: center;
    height: 16px;
    min-width: 8px;
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  /** Agent starting or running */
  .status-dot--1,
  .status-dot--2 {
    background-color: var(--ds-color-accent-9);
  }

  /** Agent idle */
  .status-dot--3,
  .status-dot--5 {
    background-color: var(--ds-color-neutral-9);
  }

  /** Agent failed */
  .status-dot--4 {
    background-color: var(--ds-color-error-9);
  }
  .status-dot--Unread {
    background-color: var(--ds-color-success-9);
  }

  .status-dot--paused {
    background-color: var(--ds-color-neutral-6);
  }
  :global(.light) .status-dot--paused {
    /** use a dark color regardless of theme */
    background-color: var(--slate-12);
  }

  .status-label {
    font-size: 10px;
    white-space: nowrap;
    padding: 0 6px 0 16px;
    border-radius: 10px;
    height: 16px;
    line-height: 16px;
    margin-left: -13px;
    z-index: 1;
  }

  .status-label--1,
  .status-label--2 {
    background-color: var(--ds-color-accent-a3);
    color: var(--ds-color-accent-11);
  }

  .status-label--3,
  .status-label--5,
  .status-label--paused {
    background-color: var(--ds-color-neutral-a3);
    color: var(--ds-color-neutral-11);
  }

  .status-label--4 {
    background-color: var(--ds-color-error-a3);
    color: var(--ds-color-error-11);
  }
  .status-label--Unread {
    background-color: var(--ds-color-success-a3);
    color: var(--ds-color-success-11);
  }
</style>

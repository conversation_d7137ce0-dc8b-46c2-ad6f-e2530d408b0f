<script lang="ts">
  import { host } from "$common-webviews/src/common/hosts/host";
  import {
    MainPanelApp,
    WebViewMessageType,
    type WebViewMessage,
  } from "$vscode/src/webview-providers/webview-messages";
  import Chat from "$common-webviews/src/apps/chat/Chat.svelte";
  import SignIn from "./SignIn.svelte";
  import AwaitingSyncingPermission from "./AwaitingSyncingPermission.svelte";
  import FolderSelection from "./FolderSelection.svelte";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";

  let visibleApp: MainPanelApp | undefined;

  const extensionMessageHandler = (e: MessageEvent<WebViewMessage>) => {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.mainPanelDisplayApp: {
        visibleApp = msg.data;
        break;
      }
    }
  };

  host.postMessage({
    type: WebViewMessageType.mainPanelLoaded,
  });
</script>

<svelte:window on:message={extensionMessageHandler} />
<MonacoProvider.Root>
  <main>
    {#if visibleApp === MainPanelApp.signIn}
      <SignIn />
    {:else if visibleApp === MainPanelApp.chat}
      <Chat />
    {:else if visibleApp === MainPanelApp.awaitingSyncingPermission}
      <AwaitingSyncingPermission />
    {:else if visibleApp === MainPanelApp.folderSelection}
      <FolderSelection />
    {/if}
  </main>
</MonacoProvider.Root>

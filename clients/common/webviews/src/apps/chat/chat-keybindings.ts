import { ChatMetricName } from "$vscode/src/metrics/types";
import { type RemoteAgentsModel } from "../remote-agent-manager/models/remote-agents-model";
import type { ChatModeModel } from "./models/chat-mode-model";
import type { ChatModel } from "./models/chat-model";
import type RichTextEditorRoot from "$common-webviews/src/design-system/components/RichTextEditorAugment/Root.svelte";
import { get } from "svelte/store";

export function onKeyDown(
  e: KeyboardEvent,
  chatModel: ChatModel,
  remoteAgentsModel: RemoteAgentsModel,
  chatModeModel?: ChatModeModel,
  richTextEditorRoot?: RichTextEditorRoot,
): void {
  if (
    e.key === "Enter" &&
    !e.shiftKey &&
    richTextEditorRoot &&
    !get(richTextEditorRoot.isFocused())
  ) {
    richTextEditorRoot.forceFocus();
  }
  // ctrl/meta + l to create new thread of current type
  if ((e.ctrlKey || e.metaKey) && e.key === "l" && !e.shiftKey) {
    if (chatModeModel) {
      // Use the createThreadOfCurrentType method from ChatModeModel
      void chatModeModel.createThreadOfCurrentType();
    } else {
      // Fallback to original behavior if chatModeModel is not available
      if (remoteAgentsModel.isActive) {
        remoteAgentsModel.setCurrentAgent(undefined);
      } else {
        chatModel.setCurrentConversation();
        chatModel.extensionClient.reportWebviewClientEvent(ChatMetricName.chatNewConversation);
      }
    }
    e.preventDefault();
  }
  // ctrl/meta + . to toggle between chat and agent modes (only when debug features are enabled)
  if (
    chatModel.flags.enableDebugFeatures &&
    (e.ctrlKey || e.metaKey) &&
    e.key === "." &&
    !e.shiftKey
  ) {
    if (chatModeModel) {
      void chatModeModel.toggleChatAgentMode();
      e.preventDefault();
    }
  }
  // ctrl/meta + shift + . to toggle chat and agent modes in reverse (only when debug features are enabled)
  if (
    chatModel.flags.enableDebugFeatures &&
    (e.ctrlKey || e.metaKey) &&
    e.key === "." &&
    e.shiftKey
  ) {
    if (chatModeModel) {
      void chatModeModel.toggleChatAgentMode(true);
      e.preventDefault();
    }
  }

  if (chatModel.flags.enableDebugFeatures && !remoteAgentsModel.isActive) {
    // ctrl/meta + shift + l to delete current chat and switch to next
    if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === "l") {
      void chatModel.popCurrentConversation();
      e.preventDefault();
    }

    // alt/option + ] to go to next conversation
    if (e.altKey && e.code === "BracketRight") {
      // don't update the lastInteractedAtIso field when switching conversations
      chatModel.setCurrentConversation(chatModel.nextConversation?.id, false);
      e.preventDefault();
    }

    // alt/option + [ to go to previous conversation
    if (e.altKey && e.code === "BracketLeft") {
      // don't update the lastInteractedAtIso field when switching conversations
      chatModel.setCurrentConversation(chatModel.previousConversation?.id, false);
      e.preventDefault();
    }

    // ctrl/meta + ] to go to next message
    if ((e.ctrlKey || e.metaKey) && e.key === "]") {
      chatModel.currentConversationModel.focusModel.focusNext();
      e.preventDefault();
    }

    // ctrl/meta + [ to go to previous message
    if ((e.ctrlKey || e.metaKey) && e.key === "[") {
      chatModel.currentConversationModel.focusModel.focusPrev();
      e.preventDefault();
    }

    if (e.key === "Escape") {
      chatModel.currentConversationModel.focusModel.setFocusIdx(undefined);
      e.preventDefault();
    }
  }
}

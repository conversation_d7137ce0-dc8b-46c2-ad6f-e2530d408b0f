import { type ToolUseResponse } from "@augment-internal/sidecar-libs/src/tools/tool-types";

export enum ToolUsePhase {
  /** Unknown state. Should not be used. */
  unknown = 0,

  /** A new tool use block has been returned by the model. */
  new = 1,

  /** Checking if the tool is safe to run without user approval. */
  checkingSafety = 2,

  /** <PERSON><PERSON> is waiting for user approval to run. */
  runnable = 3,

  /** <PERSON>l is running. */
  running = 4,

  /** <PERSON><PERSON> has completed successfully. */
  completed = 5,

  /** Tool has failed. */
  error = 6,

  /** Tool use is being cancelled. */
  cancelling = 7,

  /** Tool use has been cancelled. */
  cancelled = 8,
}

/*
State transitions
-----------------
-> new:
    Initial state when tool use block is returned by the model.

new -> checkingSafety: [automatic]
    Triggered once the chat turn containing the tool use has finished streaming.
    We wait for the streaming to finish so that the chat turn ("exchange") is
    ready to be sent as a part of the chat history in a subsequent turn, which
    would typically be the tool result sent from the user to the model.

    Checking safety involves a call out to the Augment extension to determine
    if the tool is safe to run without user approval based on the tool name and input.

checkingSafety -> runnable: [automatic]
    If the safety check concluded that the tool needs user approval,
    we mark the tool as runnable.

checkingSafety -> running: [automatic]
    If the safety check concluded that the tool does not need user approval,
    we run the tool right away.

runnable -> running: [user action]
    User clicks the "Run" button to run the tool.

    This calls out to the Augment extension to run the tool.

running -> completed: [automatic] [terminal state]
    Tool has completed successfully.

running -> error: [automatic] [terminal state]
    Tool has failed.

running -> cancelling: [user action]
    User cancels the operation by clicking "Cancel", sending an alternate message in
    the conversation (e.g., "Actually, do [something else] instead"), or switches
    conversations.

{new, checkingSafety, runnable, cancelling} -> cancelled: [automatic] [terminal state]
    Tool run has been cancelled.
 */

export interface ToolUseKeyData {
  requestId: string;
  toolUseId: string;
}

export interface ToolUseState extends ToolUseKeyData {
  phase: ToolUsePhase;
  result?: ToolUseResponse;
}

export function getToolUseKey(state: ToolUseKeyData): string {
  return state.requestId + ";" + state.toolUseId;
}

export function getToolUseKeyData(key: string): ToolUseKeyData {
  const [requestId, toolUseId] = key.split(";");
  return { requestId, toolUseId };
}

export function stringOrDefault(value: unknown, defaultValue: string): string {
  if (value === undefined || value === null) {
    return defaultValue;
  }
  if (typeof value === "string") {
    return value;
  }
  return defaultValue;
}

export function numberOrDefault(value: unknown, defaultValue: number): number {
  if (value === undefined || value === null) {
    return defaultValue;
  }
  if (typeof value === "number") {
    return value;
  }
  return defaultValue;
}

export function arrayOrDefault<T>(value: unknown, defaultValue: T[]): T[] {
  if (value === undefined || value === null) {
    return defaultValue;
  }
  if (Array.isArray(value)) {
    return value;
  }
  return defaultValue;
}

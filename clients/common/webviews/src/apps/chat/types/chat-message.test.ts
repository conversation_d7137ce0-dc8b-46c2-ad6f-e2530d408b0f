/* eslint-disable @typescript-eslint/naming-convention */
import { describe, expect, test } from "vitest";
import { SeenState, isIChatItemSeenState } from "./chat-message";

describe("isIChatItemSeenState", () => {
  test("should return true for valid IChatItemSeenState with both properties", () => {
    const validState = {
      request_id: "123",
      seen_state: SeenState.seen,
    };
    expect(isIChatItemSeenState(validState)).toBe(true);
  });

  test("should return true for valid IChatItemSeenState with only request_id", () => {
    const validState = {
      request_id: "123",
    };
    expect(isIChatItemSeenState(validState)).toBe(true);
  });

  test("should return true for valid IChatItemSeenState with only seen_state", () => {
    const validState = {
      seen_state: SeenState.unseen,
    };
    expect(isIChatItemSeenState(validState)).toBe(true);
  });

  test("should return true for empty object", () => {
    expect(isIChatItemSeenState({})).toBe(true);
  });

  test("should return false for null", () => {
    expect(isIChatItemSeenState(null)).toBe(false);
  });

  test("should return false for undefined", () => {
    expect(isIChatItemSeenState(undefined)).toBe(false);
  });

  test("should return false for non-object types", () => {
    expect(isIChatItemSeenState("string")).toBe(false);
    expect(isIChatItemSeenState(123)).toBe(false);
    expect(isIChatItemSeenState(true)).toBe(false);
  });

  test("should return false when request_id is not a string", () => {
    const invalidState = {
      request_id: 123,
    };
    expect(isIChatItemSeenState(invalidState)).toBe(false);
  });

  test("should return false when seen_state is invalid", () => {
    const invalidState = {
      seen_state: "invalid",
    };
    expect(isIChatItemSeenState(invalidState)).toBe(false);
  });

  test("should return true for valid state with additional properties", () => {
    const validState = {
      request_id: "123",
      seen_state: SeenState.seen,
      extraProp: "extra",
    };
    expect(isIChatItemSeenState(validState)).toBe(true);
  });
});

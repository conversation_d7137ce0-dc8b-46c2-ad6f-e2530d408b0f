import { expect, describe, test, it } from "vitest";
import {
  fileDetailsToMentionable,
  type IChatMentionableItem,
  isItemFile,
  isItemOfType,
} from "./mention-option";
import { type FileDetails } from "$vscode/src/webview-providers/webview-messages";

describe("mention-option", () => {
  describe("isItemOfType type narrowing", () => {
    test("file type", () => {
      const item = {
        label: "file",
        id: "file",
        file: {
          repoRoot: "/home/<USER>/example-repo-root",
          pathName: "file-name",
        },
      };

      expect(isItemOfType(item, "file")).toBe(true);
      expect(isItemOfType(item, "folder")).toBe(false);
    });

    test("folder type", () => {
      const item = {
        label: "folder",
        id: "folder",
        folder: {
          repoRoot: "/home/<USER>/example-repo-root",
          pathName: "folder-name",
        },
      };

      expect(isItemOfType(item, "file")).toBe(false);
      expect(isItemOfType(item, "folder")).toBe(true);
    });
  });

  describe("type narrowing with other utils", () => {
    test("arrays", () => {
      const items = [
        {
          label: "file",
          id: "file",
          file: {
            repoRoot: "/home/<USER>/example-repo-root",
            pathName: "file-name",
          },
        },
        {
          label: "folder",
          id: "folder",
          folder: {
            repoRoot: "/home/<USER>/example-repo-root",
            pathName: "folder-name",
          },
        },
      ];

      const files = items.filter<IChatMentionableItem<"file">>(isItemFile);
      expect(files.length).toBe(1);
      expect(files[0].file).toBeDefined();

      // This will error if the type narrowing is not working
      // and complain that string | undefined is not assignable to string
      const fileName: string = files[0].file.pathName;
      expect(fileName).toBe("file-name");
    });
  });

  describe("fileDetailsToMentionable", () => {
    it("should normalize file paths in mentionable label", () => {
      const fileDetails: FileDetails = {
        pathName: "path\\to\\file.txt",
        repoRoot: "root",
      };

      const mentionable = fileDetailsToMentionable(fileDetails);
      expect(mentionable.label).toBe("file.txt");
    });

    it("should handle paths with mixed slashes", () => {
      const fileDetails: FileDetails = {
        pathName: "path\\to/another\\file.txt",
        repoRoot: "root",
      };

      const mentionable = fileDetailsToMentionable(fileDetails);
      expect(mentionable.label).toBe("file.txt");
    });

    it("should handle root level files", () => {
      const fileDetails: FileDetails = {
        pathName: "rootfile.txt",
        repoRoot: "root",
      };

      const mentionable = fileDetailsToMentionable(fileDetails);
      expect(mentionable.label).toBe("rootfile.txt");
    });
  });
});

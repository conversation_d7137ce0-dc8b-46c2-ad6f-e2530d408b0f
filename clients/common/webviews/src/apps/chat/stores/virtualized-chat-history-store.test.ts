import { describe, test, expect, beforeEach, vi, afterEach } from "vitest";
import { get, readonly, writable, type Readable, type Writable } from "svelte/store";
import { VirtualizedChatHistoryStore } from "./virtualized-chat-history-store";
import type { ConversationModel } from "../models/conversation-model";
import type { ChatItem } from "../types/chat-message";
import { ChatItemType, ExchangeStatus, SeenState } from "../types/chat-message";
import type { IConversation } from "../models/types";

// Mock chat items for testing
const createMockChatItem = (
  id: string,
  type: ChatItemType = ChatItemType.agentOnboarding,
): ChatItem => ({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  request_id: id,
  chatItemType: type,
  status: ExchangeStatus.success,
  // eslint-disable-next-line @typescript-eslint/naming-convention
  request_message: `Message ${id}`,
  // eslint-disable-next-line @typescript-eslint/naming-convention
  response_text: `Response for ${id}`,
  // eslint-disable-next-line @typescript-eslint/naming-convention
  seen_state: SeenState.seen,
});

// Create a set of mock chat items
const mockChatItems: ChatItem[] = [
  createMockChatItem("item-1"),
  createMockChatItem("item-2"),
  createMockChatItem("item-3"),
  createMockChatItem("item-4"),
  createMockChatItem("item-5"),
  createMockChatItem("item-6"),
  createMockChatItem("item-7"),
  createMockChatItem("item-8"),
  createMockChatItem("item-9"),
  createMockChatItem("item-10"),
];

// Create a mock conversation model
const createMockConversationModel = (chatHistory: ChatItem[] = []): Writable<ConversationModel> => {
  // Create a minimal mock that satisfies the ConversationModel interface
  // We're using a more specific type assertion to avoid TypeScript errors
  const mockModel: Partial<ConversationModel> = {
    chatHistory,
    // Add any other properties needed for tests
  };

  return writable(mockModel as ConversationModel);
};

describe("VirtualizedChatHistoryStore", () => {
  // Mock dependencies
  let mockConversationModel: Writable<IConversation>;
  let wrappedConversationModel: Readable<ConversationModel>;

  // Chat history store that we can update in tests
  let chatHistoryStore: Writable<ChatItem[]>;

  // Instance under test
  let virtualizedStore: VirtualizedChatHistoryStore;

  // Setup before each test
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Create a new chat history store with initial items
    chatHistoryStore = writable([...mockChatItems]);

    // Create a mock conversation model that uses our chat history store
    mockConversationModel = createMockConversationModel();
    wrappedConversationModel = readonly(mockConversationModel as unknown as ConversationModel);

    // Update the mock conversation model with our chat history
    mockConversationModel.update((model) => ({
      ...model,
      chatHistory: get(chatHistoryStore),
    }));

    // Create the virtualized store with our mock conversation model
    virtualizedStore = new VirtualizedChatHistoryStore(wrappedConversationModel, {
      initialVisibleCount: 5,
      batchSize: 3,
    });
  });

  afterEach(() => {
    if (virtualizedStore) {
      virtualizedStore.dispose();
    }
  });

  test("should initialize with the correct number of visible items", () => {
    const items = get(virtualizedStore);

    // Since we configured initialVisibleCount as 5, we should see 5 items
    // Each item is in its own group since we didn't set up tool results
    expect(items.length).toBe(5);

    // The visible items should be the last 5 items (6-10)
    expect(items[0].turns[0].turn.request_id).toBe("item-6");
    expect(items[4].turns[0].turn.request_id).toBe("item-10");
  });

  test("hasMoreBefore should be true when there are more items before the visible window", () => {
    expect(get(virtualizedStore.hasMoreBefore)).toBe(true);
  });

  test("hasMoreAfter should be false when at the end of the list", () => {
    expect(get(virtualizedStore.hasMoreAfter)).toBe(false);
  });

  test("totalItemCount should return the total number of items", () => {
    expect(get(virtualizedStore.totalItemCount)).toBe(10);
  });

  test("loadMoreBefore should load more items at the beginning of the window", async () => {
    // Initially we have items 6-10 visible
    expect(get(virtualizedStore)[0].turns[0].turn.request_id).toBe("item-6");

    // Load more items before
    const result = await virtualizedStore.loadMoreBefore();

    // Should return true to indicate success
    expect(result).toBe(true);

    // Should now have items 3-10 visible (added 3 more at the beginning)
    const items = get(virtualizedStore);
    expect(items[0].turns[0].turn.request_id).toBe("item-3");
    expect(items[items.length - 1].turns[0].turn.request_id).toBe("item-10");
  });

  test("loadMoreBefore should return false when at the beginning of the list", async () => {
    // Reset to show the first items
    virtualizedStore.resetToTop();

    // Try to load more items before
    const result = await virtualizedStore.loadMoreBefore();

    // Should return false since we're at the beginning
    expect(result).toBe(false);
  });

  test("loadMoreAfter should load more items at the end of the window", async () => {
    // Reset to show the first items
    virtualizedStore.resetToTop();

    // Initially we have items 1-5 visible
    expect(get(virtualizedStore)[0].turns[0].turn.request_id).toBe("item-1");

    // Load more items after
    const result = await virtualizedStore.loadMoreAfter();

    // Should return true to indicate success
    expect(result).toBe(true);

    // Should now have items 1-8 visible (added 3 more at the end)
    const items = get(virtualizedStore);
    expect(items[0].turns[0].turn.request_id).toBe("item-1");
    expect(items[items.length - 1].turns[0].turn.request_id).toBe("item-8");
  });

  test("loadMoreAfter should return false when at the end of the list", async () => {
    // Try to load more items after (we're already at the end)
    const result = await virtualizedStore.loadMoreAfter();

    // Should return false since we're at the end
    expect(result).toBe(false);
  });

  test("resetToBottom should show the most recent items", () => {
    // First reset to top
    virtualizedStore.resetToTop();

    // Then reset to bottom
    virtualizedStore.resetToBottom();

    // Should now have items 6-10 visible
    const items = get(virtualizedStore);
    expect(items[0].turns[0].turn.request_id).toBe("item-6");
    expect(items[items.length - 1].turns[0].turn.request_id).toBe("item-10");
  });

  test("resetToTop should show the oldest items", () => {
    // Reset to top
    virtualizedStore.resetToTop();

    // Should now have items 1-5 visible
    const items = get(virtualizedStore);
    expect(items[0].turns[0].turn.request_id).toBe("item-1");
    expect(items[items.length - 1].turns[0].turn.request_id).toBe("item-5");
  });

  test("jumpToMessage should center the view around the specified message", async () => {
    // Jump to item-5
    const result = await virtualizedStore.jumpToMessage("item-5");

    // Should return true to indicate success
    expect(result).toBe(true);

    // Should now have items 3-7 visible (centered around item-5)
    const items = get(virtualizedStore);

    // With initialVisibleCount of 5, we should have 2 items before and 2 after
    expect(
      items.some((group) => group.turns.some(({ turn }) => turn.request_id === "item-3")),
    ).toBe(true);
    expect(
      items.some((group) => group.turns.some(({ turn }) => turn.request_id === "item-5")),
    ).toBe(true);
    expect(
      items.some((group) => group.turns.some(({ turn }) => turn.request_id === "item-7")),
    ).toBe(true);
  });

  test("jumpToMessage should return false for non-existent message", async () => {
    // Jump to a non-existent item
    const result = await virtualizedStore.jumpToMessage("non-existent");

    // Should return false to indicate failure
    expect(result).toBe(false);
  });

  test("should handle empty chat history", () => {
    // Update the chat history to be empty
    chatHistoryStore.set([]);
    mockConversationModel.update((model) => ({
      ...model,
      chatHistory: get(chatHistoryStore),
    }));

    // Create a new store with the empty history
    const emptyStore = new VirtualizedChatHistoryStore(wrappedConversationModel);

    // Should have no items
    expect(get(emptyStore).length).toBe(0);

    // Status indicators should reflect empty state
    expect(get(emptyStore.hasMoreBefore)).toBe(false);
    expect(get(emptyStore.hasMoreAfter)).toBe(false);
    expect(get(emptyStore.totalItemCount)).toBe(0);

    // Clean up
    emptyStore.dispose();
  });

  test("should update when conversation model changes", () => {
    // Add a new item to the chat history
    const newItem = createMockChatItem("item-11");
    chatHistoryStore.update((items) => [...items, newItem]);

    // Update the conversation model
    mockConversationModel.update((model) => ({
      ...model,
      chatHistory: get(chatHistoryStore),
    }));

    // Should now have 11 total items
    expect(get(virtualizedStore.totalItemCount)).toBe(11);

    // Should still show the most recent items (7-11)
    const items = get(virtualizedStore);
    expect(items[items.length - 1].turns[0].turn.request_id).toBe("item-11");
  });

  test("should handle tool results", () => {
    // Create a new conversation with just two items for simplicity
    const simpleItems = [
      // Create items with the agentOnboarding type which is included in the filter
      createMockChatItem("simple-1", ChatItemType.agentOnboarding),
      createMockChatItem("simple-2", ChatItemType.agentOnboarding),
    ];

    // Update the chat history
    chatHistoryStore.set(simpleItems);

    // Update the conversation model
    mockConversationModel.update((model) => ({
      ...model,
      chatHistory: get(chatHistoryStore),
    }));

    // Create a new store with the simple history
    const simpleStore = new VirtualizedChatHistoryStore(wrappedConversationModel, {
      initialVisibleCount: 5,
      batchSize: 3,
    });

    // Verify we have two separate groups initially
    let groups = get(simpleStore);
    expect(groups.length).toBe(2);

    // Create a tool result item that will be included in the filter
    const toolResultItem: ChatItem = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      request_id: "tool-result",
      chatItemType: ChatItemType.agentOnboarding, // Use a type that's included in the filter
      status: ExchangeStatus.success,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      request_message: "Tool result",
      // eslint-disable-next-line @typescript-eslint/naming-convention
      seen_state: SeenState.seen,
      // Add isToolResult property for testing
      isToolResult: true,
    } as ChatItem & { isToolResult: boolean };

    // Add the tool result
    chatHistoryStore.update((items) => [...items, toolResultItem]);

    // Update the conversation model
    mockConversationModel.update((model) => ({
      ...model,
      chatHistory: get(chatHistoryStore),
    }));

    // Now we should have two groups: the first item and then the second item + tool result
    groups = get(simpleStore);

    // Find the group containing the tool result
    const toolResultGroup = groups.find((group) =>
      group.turns.some(({ turn }) => turn.request_id === "tool-result"),
    );

    // Verify the tool result is grouped with the previous item
    expect(toolResultGroup).toBeDefined();
    expect(toolResultGroup?.turns.length).toBe(2);
    expect(toolResultGroup?.turns[0].turn.request_id).toBe("simple-2");
    expect(toolResultGroup?.turns[1].turn.request_id).toBe("tool-result");

    // Clean up
    simpleStore.dispose();
  });

  // We no longer use scroll anchors, only distance from bottom for scroll position preservation

  test("should stay at the bottom when new messages arrive", () => {
    // Initially we have items 6-10 visible
    expect(get(virtualizedStore)[0].turns[0].turn.request_id).toBe("item-6");

    // Add three new items
    const newItems = [
      createMockChatItem("item-11"),
      createMockChatItem("item-12"),
      createMockChatItem("item-13"),
    ];

    chatHistoryStore.update((items) => [...items, ...newItems]);

    // Update the conversation model
    mockConversationModel.update((model) => ({
      ...model,
      chatHistory: get(chatHistoryStore),
    }));

    // Should now have 13 total items
    expect(get(virtualizedStore.totalItemCount)).toBe(13);

    // Should still show the most recent items
    const items = get(virtualizedStore);
    // With endIndex set to undefined, we should see all items from startIndex to the end
    // The last item should be item-13
    expect(items[items.length - 1].turns[0].turn.request_id).toBe("item-13");
    // And we should have at least the last 5 items
    expect(items.length).toBeGreaterThanOrEqual(5);
  });

  test("should handle conversation change with significantly different history", () => {
    // Replace the entire chat history with a completely different set
    const newHistory = [
      createMockChatItem("new-1"),
      createMockChatItem("new-2"),
      createMockChatItem("new-3"),
    ];

    chatHistoryStore.set(newHistory);

    // Update the conversation model
    mockConversationModel.update((model) => ({
      ...model,
      chatHistory: get(chatHistoryStore),
    }));

    // Explicitly reset to bottom to ensure we see the new items
    virtualizedStore.resetToBottom();

    // Should now have 3 total items
    expect(get(virtualizedStore.totalItemCount)).toBe(3);

    // Should show all 3 items
    const items = get(virtualizedStore);
    expect(items.length).toBe(3);
    expect(items[0].turns[0].turn.request_id).toBe("new-1");
    expect(items[items.length - 1].turns[0].turn.request_id).toBe("new-3");
  });

  test("should set and get dynamic batch size", () => {
    // Default batch size should be the one we configured
    expect(virtualizedStore.getCurrentBatchSize()).toBe(3);

    // Set a new batch size
    virtualizedStore.setDynamicBatchSize(5);
    expect(virtualizedStore.getCurrentBatchSize()).toBe(5);

    // Set a batch size below the minimum
    virtualizedStore.setDynamicBatchSize(1);
    // Should be clamped to the minimum (which is the configured batch size by default)
    expect(virtualizedStore.getCurrentBatchSize()).toBe(3);

    // Set a batch size above the maximum
    virtualizedStore.setDynamicBatchSize(20);
    // Should be clamped to the maximum (which is 3x the configured batch size by default)
    expect(virtualizedStore.getCurrentBatchSize()).toBe(9);
  });

  test("should use dynamic batch size when loading more items", async () => {
    // Create a store with more items to test with
    const manyItems = Array.from({ length: 30 }, (_, i) => createMockChatItem(`item-${i + 1}`));

    chatHistoryStore.set(manyItems);
    mockConversationModel.update((model) => ({
      ...model,
      chatHistory: get(chatHistoryStore),
    }));

    // Create a new store with our test configuration
    const testStore = new VirtualizedChatHistoryStore(wrappedConversationModel, {
      initialVisibleCount: 5,
      batchSize: 3,
    });

    // Reset to top to start from the beginning
    testStore.resetToTop();

    // Initially we have items 1-5 visible
    expect(get(testStore)[0].turns[0].turn.request_id).toBe("item-1");

    // Load more items with a custom batch size
    await testStore.loadMoreAfter(7);

    // Should now have items 1-12 visible (added 7 more at the end)
    const items = get(testStore);
    expect(items[0].turns[0].turn.request_id).toBe("item-1");
    expect(items[items.length - 1].turns[0].turn.request_id).toBe("item-12");

    // Clean up
    testStore.dispose();
  });

  test("should respect min and max batch size constraints", () => {
    // Create a store with custom min and max batch sizes
    const customStore = new VirtualizedChatHistoryStore(wrappedConversationModel, {
      initialVisibleCount: 5,
      batchSize: 3,
      minBatchSize: 2,
      maxBatchSize: 6,
    });

    // Default should be the configured batch size
    expect(customStore.getCurrentBatchSize()).toBe(3);

    // Set below minimum
    customStore.setDynamicBatchSize(1);
    expect(customStore.getCurrentBatchSize()).toBe(2); // Clamped to min

    // Set above maximum
    customStore.setDynamicBatchSize(10);
    expect(customStore.getCurrentBatchSize()).toBe(6); // Clamped to max

    // Set within range
    customStore.setDynamicBatchSize(4);
    expect(customStore.getCurrentBatchSize()).toBe(4); // Accepted as is

    // Clean up
    customStore.dispose();
  });
});

import {
  writable,
  derived,
  get,
  type Readable,
  type Writable,
  type Unsubscriber,
} from "svelte/store";
import type { ConversationModel } from "../models/conversation-model";
import type { ChatItem } from "../types/chat-message";
import {
  isChatItemExchangeWithStatus,
  isChatItemAgenticCheckpointDelimiter,
  isChatItemSignInWelcome,
  isChatItemGenerateCommitMessage,
  isChatItemEducateFeatures,
  isChatItemSummaryResponse,
  isChatItemAutofixMessage,
  isChatItemAutofixSteeringMessage,
  isChatItemAutofixStage,
  isChatItemAgentOnboarding,
  hasToolResult,
} from "../types/chat-message";
import type { IDisposable } from "monaco-editor";
import { tick } from "svelte";

/**
 * Configuration options for the virtualization behavior
 */
export interface VirtualizationConfig {
  /** Number of items to show initially */
  initialVisibleCount: number;
  /** Number of items to load at a time when scrolling */
  batchSize: number;
  /** Minimum batch size when using dynamic batch sizes */
  minBatchSize?: number;
  /** Maximum batch size when using dynamic batch sizes */
  maxBatchSize?: number;
}

/**
 * Represents a group of chat items that should be displayed together
 */
export interface GroupedChatItem {
  /** The chat items in this group */
  turns: Array<{ turn: ChatItem; idx: number }>;
  /** The request ID of the first item in the group */
  firstRequestId: string | undefined;
  /** The request ID of the last item in the group */
  lastRequestId: string | undefined;
  /** Whether this is the last group in the list */
  isLastGroup: boolean;
}

/**
 * Interface for the virtualized chat history store
 */
export interface IVirtualizedChatHistoryStore extends Readable<GroupedChatItem[]> {
  /** Whether there are more items to load before the current window */
  hasMoreBefore: Readable<boolean>;
  /** Whether there are more items to load after the current window */
  hasMoreAfter: Readable<boolean>;
  /** Whether the store is currently loading more items */
  isLoading: Readable<boolean>;
  /** The direction in which items are being loaded */
  loadingDirection: Readable<"before" | "after" | null>;
  /** The total number of items in the full history */
  totalItemCount: Readable<number>;

  /** Load more items before the current visible range */
  loadMoreBefore(dynamicBatchSize?: number): boolean;
  /** Load more items after the current visible range */
  loadMoreAfter(dynamicBatchSize?: number): boolean;
  /** Reset the store to show the most recent messages */
  resetToBottom(): void;
  /** Reset the store to show the oldest messages */
  resetToTop(): void;
  /** Jump to a specific message by its request_id */
  jumpToMessage(requestId: string): Promise<boolean>;
  /** Set the current batch size for loading operations */
  setDynamicBatchSize(batchSize: number): void;
  /** Get the current batch size */
  getCurrentBatchSize(): number;
}

/**
 * A store that provides a virtualized view of the conversation history.
 * This store handles loading more messages as the user scrolls and maintains scroll position.
 */
export class VirtualizedChatHistoryStore implements IVirtualizedChatHistoryStore, IDisposable {
  // Configuration
  private _config: VirtualizationConfig;
  private _disposables: Array<Unsubscriber> = [];
  private _currentBatchSize: number;

  // Individual state stores
  private _startIndex: Writable<number>;
  private _endIndex: Writable<number | undefined>;
  private _isLoading: Writable<boolean>;
  private _loadingDirection: Writable<"before" | "after" | null>;

  // Derived stores
  private _allItems: Readable<ChatItem[]>;
  private _groupedAllItems: Readable<GroupedChatItem[]>;
  private _groupedVisibleItems: Readable<GroupedChatItem[]>;

  // Public derived stores
  public hasMoreBefore: Readable<boolean>;
  public hasMoreAfter: Readable<boolean>;
  public isLoading: Readable<boolean>;
  public loadingDirection: Readable<"before" | "after" | null>;
  public totalItemCount: Readable<number>;

  /**
   * Creates a new VirtualizedChatHistoryStore
   *
   * @param conversationModel A readable store containing the conversation model
   * @param config Configuration for the virtualization behavior
   */
  constructor(
    private _conversationModel: Readable<ConversationModel>,
    config: VirtualizationConfig = { initialVisibleCount: 20, batchSize: 10 },
  ) {
    this._config = {
      ...config,
      minBatchSize: config.minBatchSize ?? config.batchSize,
      maxBatchSize: config.maxBatchSize ?? config.batchSize * 3,
    };
    this._currentBatchSize = this._config.batchSize;

    // Initialize individual state stores
    this._startIndex = writable(0);
    this._endIndex = writable(undefined); // undefined means "show all items to the end"
    this._isLoading = writable(false);
    this._loadingDirection = writable(null);

    // Derive the full chat history from the conversation model
    this._allItems = derived(this._conversationModel, ($model) => {
      // Filter the chat history to only include items we want to render
      return $model.chatHistory.filter((m) => {
        return (
          isChatItemExchangeWithStatus(m) ||
          isChatItemSignInWelcome(m) ||
          isChatItemGenerateCommitMessage(m) ||
          isChatItemEducateFeatures(m) ||
          isChatItemSummaryResponse(m) ||
          isChatItemAutofixMessage(m) ||
          isChatItemAutofixSteeringMessage(m) ||
          isChatItemAutofixStage(m) ||
          isChatItemAgentOnboarding(m) ||
          isChatItemAgenticCheckpointDelimiter(m)
        );
      });
    });

    // Group all items first
    this._groupedAllItems = derived(this._allItems, ($allItems) => {
      // Map all items to the format expected by _groupItems
      const allItemsWithIndex = $allItems.map((item, idx) => ({
        turn: item,
        idx: idx,
      }));
      return this._groupItems(allItemsWithIndex);
    });

    // Derive the visible window of grouped items
    this._groupedVisibleItems = derived(
      [this._groupedAllItems, this._startIndex, this._endIndex],
      ([$groupedAllItems, $startIndex, $endIndex]) => {
        // If there are no groups, return empty array
        if ($groupedAllItems.length === 0) {
          return [];
        }

        // Find the first group that contains an item with last index >= startIndex.
        // This will be the first group that is at least partially visible.
        let startGroupIndex = 0;
        for (let i = 0; i < $groupedAllItems.length; i++) {
          const group = $groupedAllItems[i];
          const lastItemIndex = group.turns[group.turns.length - 1].idx;
          if (lastItemIndex >= $startIndex) {
            startGroupIndex = i;
            break;
          }
          // If this is the last group and we haven't found a match,
          // use this group if it contains any items with index >= startIndex
          if (i === $groupedAllItems.length - 1) {
            const lastItemIndex = group.turns[group.turns.length - 1].idx;
            if (lastItemIndex >= $startIndex) {
              startGroupIndex = i;
              break;
            }
          }
        }

        // Find the last group that contains an item with first index <= endIndex.
        // This will be the last group that is at least partially visible.
        let endGroupIndex = $groupedAllItems.length - 1;
        if ($endIndex !== undefined) {
          for (let i = $groupedAllItems.length - 1; i >= 0; i--) {
            const group = $groupedAllItems[i];
            const firstItemIndex = group.turns[0].idx;
            if (firstItemIndex <= $endIndex) {
              endGroupIndex = i;
              break;
            }
            // If this is the first group and we haven't found a match,
            // use this group if it contains any items with index <= endIndex
            if (i === 0) {
              const firstItemIndex = group.turns[0].idx;
              if (firstItemIndex <= $endIndex) {
                endGroupIndex = i;
                break;
              }
            }
          }
        }

        // Get the visible groups
        const visibleGroups = $groupedAllItems.slice(startGroupIndex, endGroupIndex + 1);

        // Update the isLastGroup property
        return visibleGroups.map(
          (group: GroupedChatItem, idx: number, groups: GroupedChatItem[]) => ({
            ...group,
            isLastGroup: idx === groups.length - 1,
          }),
        );
      },
    );

    // Derive status indicators
    this.hasMoreBefore = derived([this._startIndex], ([$startIndex]) => $startIndex > 0);

    this.hasMoreAfter = derived(
      [this._endIndex, this._allItems],
      ([$endIndex, $allItems]) => $endIndex !== undefined && $endIndex < $allItems.length - 1,
    );

    this.isLoading = derived(this._isLoading, ($isLoading) => $isLoading);

    this.loadingDirection = derived(
      this._loadingDirection,
      ($loadingDirection) => $loadingDirection,
    );

    this.totalItemCount = derived(this._allItems, ($allItems) => $allItems.length);

    // Set up subscription to handle conversation changes if the method exists
    const conversationModel = get(this._conversationModel);
    if (typeof conversationModel.onNewConversation === "function") {
      this._disposables.push(
        conversationModel.onNewConversation(() => {
          this.resetToBottom();
        }),
      );
    }

    // Initialize to show the most recent items
    this.resetToBottom();
  }

  /**
   * Svelte store subscription method
   */
  public subscribe(run: (value: GroupedChatItem[]) => void): Unsubscriber {
    return this._groupedVisibleItems.subscribe(run);
  }

  /**
   * Load more items before the current visible range
   * @param dynamicBatchSize Optional batch size to use for this load operation
   * @returns Promise resolving to true if more items were loaded
   */
  public loadMoreBefore(dynamicBatchSize?: number): boolean {
    const $startIndex = get(this._startIndex);
    const $isLoading = get(this._isLoading);

    if ($startIndex <= 0 || $isLoading) {
      return false;
    }

    // Set loading state
    this._isLoading.set(true);
    this._loadingDirection.set("before");

    // Use dynamic batch size if provided, otherwise use current batch size
    const batchSize = this._getValidBatchSize(dynamicBatchSize);

    // Calculate new start index
    const newStartIndex = Math.max(0, $startIndex - batchSize);

    // Update state
    this._startIndex.set(newStartIndex);
    this._isLoading.set(false);
    this._loadingDirection.set(null);

    return true;
  }

  /**
   * Load all items before the current visible range
   * @returns Promise resolving to true if more items were loaded
   */
  public async loadToStart(
    options: {
      smooth?: boolean;
      smoothInterval?: number;
    } = {},
  ): Promise<boolean> {
    const $startIndex = get(this._startIndex);
    const $isLoading = get(this._isLoading);

    if ($startIndex <= 0 || $isLoading) {
      return false;
    }

    // Set loading state
    this._isLoading.set(true);
    this._loadingDirection.set("before");

    const { smooth, smoothInterval = 500 } = options;

    if (!smooth) {
      this._startIndex.set(0);
    } else {
      while (this.loadMoreBefore()) {
        await new Promise((resolve) => setTimeout(resolve, smoothInterval));
        await tick();
      }
    }

    // Update state
    this._isLoading.set(false);
    this._loadingDirection.set(null);

    return true;
  }

  /**
   * Load more items after the current visible range
   * @param dynamicBatchSize Optional batch size to use for this load operation
   * @returns Promise resolving to true if more items were loaded
   */
  public loadMoreAfter(dynamicBatchSize?: number): boolean {
    const $endIndex = get(this._endIndex);
    const $isLoading = get(this._isLoading);
    const $items = get(this._allItems);

    // If endIndex is undefined, we're already showing all items to the end
    // Or if we're already at the end or loading, don't do anything
    if ($endIndex === undefined || $endIndex >= $items.length - 1 || $isLoading) {
      return false;
    }

    // Set loading state
    this._isLoading.set(true);
    this._loadingDirection.set("after");

    // Use dynamic batch size if provided, otherwise use current batch size
    const batchSize = this._getValidBatchSize(dynamicBatchSize);

    // Calculate new end index
    const newEndIndex = Math.min($items.length - 1, $endIndex + batchSize);

    // If we're going to load all the way to the end, just set to undefined
    if (newEndIndex >= $items.length - 1) {
      this._endIndex.set(undefined);
    } else {
      this._endIndex.set(newEndIndex);
    }

    this._isLoading.set(false);
    this._loadingDirection.set(null);

    return true;
  }

  /**
   * Reset the store to show the most recent messages
   * Sets endIndex to undefined to automatically include new messages as they arrive
   */
  public resetToBottom(): void {
    const $items = get(this._allItems);
    if ($items.length === 0) return;

    // Calculate how many items to show from the end
    const startIndex = Math.max(0, $items.length - this._config.initialVisibleCount);

    // Update state
    this._startIndex.set(startIndex);
    this._endIndex.set(undefined); // undefined means "show all items to the end"
  }

  /**
   * Reset the store to show the oldest messages
   */
  public resetToTop(): void {
    const $items = get(this._allItems);
    if ($items.length === 0) return;

    const startIndex = 0;
    const endIndex = Math.min($items.length - 1, this._config.initialVisibleCount - 1);

    // Update state
    this._startIndex.set(startIndex);
    this._endIndex.set(endIndex);
  }

  /**
   * Jump to a specific message by its request_id
   * @param requestId The request_id of the message to jump to
   * @returns Promise resolving to true if the message was found and jumped to
   */
  public async jumpToMessage(requestId: string): Promise<boolean> {
    const $items = get(this._allItems);
    const targetIndex = $items.findIndex((item) => item.request_id === requestId);

    if (targetIndex === -1) {
      return false;
    }

    // Calculate a window around the target item
    const halfWindow = Math.floor(this._config.initialVisibleCount / 2);
    const startIndex = Math.max(0, targetIndex - halfWindow);

    // If we're jumping to a message near the end, set endIndex to undefined
    // to automatically include new messages as they arrive
    const endIndex =
      targetIndex + halfWindow >= $items.length - 5
        ? undefined
        : Math.min($items.length - 1, targetIndex + halfWindow);

    // Update state
    this._startIndex.set(startIndex);
    this._endIndex.set(endIndex);

    return true;
  }

  /**
   * Group chat items into logical groups for display
   * @param items The items to group
   * @returns The grouped items
   */
  private _groupItems(items: Array<{ turn: ChatItem; idx: number }>): Array<GroupedChatItem> {
    // This is adapted from the existing groupedChatHistory logic in MessageList.svelte
    return items
      .reduce((acc: Array<GroupedChatItem>, { turn, idx }) => {
        // Check if the turn has a tool result
        // For testing purposes, we also check for the isToolResult property
        const turnHasToolResult =
          (turn as any).isToolResult === true ||
          (isChatItemExchangeWithStatus(turn) && hasToolResult(turn));

        if (turnHasToolResult && acc.length > 0) {
          // Add to the current group if it has a tool result
          acc[acc.length - 1].turns.push({ turn, idx });
        } else if (isChatItemAgenticCheckpointDelimiter(turn) && acc.length > 0) {
          // Add to the current group if it's a checkpoint delimiter
          acc[acc.length - 1].turns.push({ turn, idx });
        } else {
          // Start a new group
          acc.push({
            turns: [{ turn, idx }],
            firstRequestId: turn.request_id,
            lastRequestId: turn.request_id,
            isLastGroup: false, // Will update this later
          });
        }

        return acc;
      }, [])
      .map((group, groupIdx, groups) => {
        // Get the last non-trivial request ID
        const lastNonTrivialTurn = group.turns.findLast(({ turn }) => !!turn.request_id);
        const lastRequestId = lastNonTrivialTurn?.turn.request_id;

        return {
          ...group,
          lastRequestId,
          isLastGroup: groupIdx === groups.length - 1,
        };
      });
  }

  /**
   * Set the current batch size for loading operations
   * @param batchSize The new batch size to use
   */
  public setDynamicBatchSize(batchSize: number): void {
    this._currentBatchSize = this._getValidBatchSize(batchSize);
  }

  /**
   * Get the current batch size
   * @returns The current batch size
   */
  public getCurrentBatchSize(): number {
    return this._currentBatchSize;
  }

  /**
   * Validates and constrains a batch size to be within the configured min/max range
   * @param batchSize The batch size to validate
   * @returns A valid batch size within the configured range
   */
  private _getValidBatchSize(batchSize?: number): number {
    if (batchSize === undefined) {
      return this._currentBatchSize;
    }

    const minBatchSize = this._config.minBatchSize ?? this._config.batchSize;
    const maxBatchSize = this._config.maxBatchSize ?? this._config.batchSize * 3;

    return Math.max(minBatchSize, Math.min(maxBatchSize, batchSize));
  }

  /**
   * Clean up resources when the store is no longer needed
   */
  public dispose(): void {
    this._disposables.forEach((unsubscribe) => unsubscribe());
  }
}

/**
 * Creates a virtualized chat history store
 * @param conversationModel A readable store containing the conversation model
 * @param config Configuration for the virtualization behavior
 * @returns A store that provides a virtualized view of the chat history
 */
export function createVirtualizedChatHistoryStore(
  conversationModel: Readable<ConversationModel>,
  config: VirtualizationConfig = { initialVisibleCount: 20, batchSize: 10 },
): IVirtualizedChatHistoryStore {
  return new VirtualizedChatHistoryStore(conversationModel, config);
}

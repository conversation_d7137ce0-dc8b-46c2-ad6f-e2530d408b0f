<script lang="ts">
  import MaterialIcon from "$common-webviews/src/common/components/MaterialIcon.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";

  import { getContext, onMount } from "svelte";
  import { type AutofixConversationModel } from "../models/autofix-conversation-model";

  const autofixConversationModel = getContext(
    "autofixConversationModel",
  ) as AutofixConversationModel;
  if (!autofixConversationModel) {
    throw new Error("AutofixConversationModel not found in context");
  }

  const focusInput = () => {
    const input = document.querySelector("input");
    if (input) {
      input.focus();
      input.select();
    }
  };

  let inputText = "";

  async function handleInputKeyDown(e: KeyboardEvent): Promise<boolean> {
    if (e.shiftKey || e.ctrlKey || e.metaKey) {
      return false; // Not handled
    }

    e.stopPropagation();

    if (e.key === "Enter" && inputText.length > 0) {
      autofixConversationModel.setCommand(inputText);
      await autofixConversationModel.startNewAutofixIteration();
      inputText = "";
      return true;
    } else if (e.key === "Escape") {
      return true;
    }

    return false;
  }

  onMount(focusInput);
</script>

<div class="command-input-container">
  <TextFieldAugment
    bind:value={inputText}
    placeholder="Enter test command (e.g. pytest path/to/test.py)"
    on:keydown={handleInputKeyDown}
    variant="surface"
    size={2}
  />
  <ButtonAugment
    disabled={!inputText}
    size={2}
    variant="solid"
    color="accent"
    on:click={() => handleInputKeyDown(new KeyboardEvent("keydown", { key: "Enter" }))}
  >
    <MaterialIcon iconName="play_arrow" />
  </ButtonAugment>
</div>

<style>
  .command-input-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-2-augment);
    padding: var(--spacing-2-augment);
  }

  :global(.command-input-container > :first-child) {
    flex: 1;
  }
</style>

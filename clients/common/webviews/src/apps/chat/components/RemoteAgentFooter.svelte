<script lang="ts">
  import { getContext, onMount } from "svelte";
  import { type Writable } from "svelte/store";
  import { RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";
  import RemoteAgentEditList from "./agent-edits/RemoteAgentEditList.svelte";
  import { type ChangedFile, RemoteAgentStatus } from "$vscode/src/remote-agent-manager/types";
  import { getAggregateChanges } from "../../remote-agent-manager/utils";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import OpenInNewWindow from "$common-webviews/src/design-system/icons/open-in-new-window.svelte";
  import CodePullRequest from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/code-pull-request.svg?component";
  import { type ChatModel } from "../models/chat-model";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import { SELECTED_TURN_INDEX_CONTEXT_KEY } from "../../remote-agent-manager/models/message-render-options";
  import { isAgentFromDifferentRepoStore } from "../../remote-agent-manager/utils/repository-utils";
  import { writable } from "svelte/store";
  import { GitReferenceModel } from "../../remote-agent-manager/models/git-reference-model";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const chatModel = getContext<ChatModel>("chatModel");
  const selectedTurnIndex = getContext<Writable<number>>(SELECTED_TURN_INDEX_CONTEXT_KEY);
  const gitReferenceModel = getContext<GitReferenceModel>(GitReferenceModel.key);

  const currentRepoUrl = writable<string>("");
  const isAgentFromDifferentRepo = isAgentFromDifferentRepoStore(currentRepoUrl);

  onMount(async () => {
    try {
      const { remoteUrl: repoUrl, error: remoteUrlError } = await gitReferenceModel.getRemoteUrl();
      if (remoteUrlError) {
        throw new Error(remoteUrlError);
      }
      currentRepoUrl.set(repoUrl);
    } catch (error) {
      console.error("Failed to get current repository URL:", error);
    }
  });

  let isCreatingPR = false;
  let windowHeight = 0;

  $: doShowFilesByDefault = windowHeight > 660;

  $: currentAgent = $remoteAgentsModel?.currentAgent;
  $: agentId = currentAgent?.remote_agent_id;
  $: isCurrentAgentDetailsLoading = $remoteAgentsModel?.isCurrentAgentDetailsLoading;

  async function sshToRemoteAgent(id: string) {
    if (!id) return;
    const agent = remoteAgentsModel.agentOverviews.find((a) => a.remote_agent_id === id);
    if (!agent) return;
    await remoteAgentsModel.sshToRemoteAgent(agent);
  }

  /** When true, we are in the process of resuming the remote agent workspace before SSHing */
  let resumingBeforeSsh = false;

  $: changedFiles = getAggregateChanges($remoteAgentsModel.currentConversation?.exchanges ?? []);
  $: focusedFilePath = $remoteAgentsModel.focusedFilePath;
  $: isRemoteAgentWindow = $chatModel.flags.isRemoteAgentWindow;

  // Custom handler for review click that uses our openDiffInBuffer method
  async function handleReviewClick(file: ChangedFile) {
    const filePath = file.new_path || file.old_path;

    // Use the messenger to open diff in file
    $remoteAgentsModel.openDiffInBuffer(file.old_contents || "", file.new_contents || "", filePath);
  }

  function handleCreatePR(): void {
    if (!$remoteAgentsModel.currentAgent?.remote_agent_id) {
      console.error("No current agent ID found");
      return;
    }

    isCreatingPR = true;

    const currentConversation = $chatModel.currentConversationModel;
    const selectedModelId = currentConversation?.selectedModelId;

    // Send a message to the current agent asking it to create a PR
    $remoteAgentsModel
      .sendMessage(
        "Please create a pull request with the changes you've made.",
        selectedModelId ?? undefined,
      )
      .then(() => {
        isCreatingPR = false;
      })
      .catch((error) => {
        console.error("Error sending PR creation request:", error);
        isCreatingPR = false;
      });
  }
</script>

<svelte:window bind:innerHeight={windowHeight} />

{#if currentAgent}
  <div class="c-agent-footer">
    <div class="c-agent-footer__status">
      {#if changedFiles.length > 0}
        <RemoteAgentEditList
          {changedFiles}
          collapsed={!doShowFilesByDefault}
          {focusedFilePath}
          emptyMessage={isCurrentAgentDetailsLoading ? "Loading..." : "No changes to show"}
          onReviewClick={handleReviewClick}
          onOpenDiffView={() => {
            selectedTurnIndex.set(-1);
            $remoteAgentsModel.showRemoteAgentDiffPanel({
              turnIdx: -1,
              changedFiles,
              userPrompt: "",
              sessionSummary: "",
              isShowingAggregateChanges: true,
              isAgentFromDifferentRepo: $isAgentFromDifferentRepo(currentAgent),
            });
          }}
        />
      {/if}
    </div>
    {#if agentId && !isRemoteAgentWindow}
      <div class="c-agent-footer__buttons">
        {#if changedFiles.length > 0}
          <div class="c-agent-footer__button">
            <TextTooltipAugment
              triggerOn={[TooltipTriggerOn.Hover]}
              content="Create a GitHub pull request with the changes"
            >
              <ButtonAugment
                size={2}
                variant="soft"
                color="neutral"
                on:click={handleCreatePR}
                class="c-agent-footer__open-btn"
                disabled={[
                  RemoteAgentStatus.agentUnspecified,
                  RemoteAgentStatus.agentPending,
                  RemoteAgentStatus.agentStarting,
                ].includes(currentAgent?.status)}
              >
                <span class="c-agent-footer__button-text">
                  {#if isCreatingPR}
                    Creating a PR...
                  {:else}
                    Create a PR
                  {/if}
                </span>
                <div slot="iconRight">
                  <CodePullRequest />
                </div>
              </ButtonAugment>
            </TextTooltipAugment>
          </div>
        {/if}

        {#if !isRemoteAgentWindow}
          <div class="c-agent-footer__button">
            <TextTooltipAugment
              triggerOn={[TooltipTriggerOn.Hover]}
              content="SSH into the remote agent's workspace in a new VSCode window"
            >
              <ButtonAugment
                size={2}
                variant="soft"
                color="accent"
                on:click={() => {
                  resumingBeforeSsh = true;
                  sshToRemoteAgent(agentId).then(() => {
                    resumingBeforeSsh = false;
                  });
                }}
                class="c-agent-footer__open-btn"
                disabled={[
                  RemoteAgentStatus.agentUnspecified,
                  RemoteAgentStatus.agentPending,
                  RemoteAgentStatus.agentStarting,
                ].includes(currentAgent?.status) || resumingBeforeSsh}
              >
                <span class="c-agent-footer__button-text">
                  {#if resumingBeforeSsh}
                    Connecting...
                  {:else if changedFiles.length > 0}
                    <span class="c-agent-footer__button-text--full">Open remote workspace</span>
                    <span class="c-agent-footer__button-text--short">Open</span>
                  {:else}
                    Open remote workspace
                  {/if}
                </span>
                {#if resumingBeforeSsh}
                  <SpinnerAugment size={1} useCurrentColor={true} slot="iconRight" />
                {:else}
                  <OpenInNewWindow slot="iconRight" />
                {/if}
              </ButtonAugment>
            </TextTooltipAugment>
          </div>
        {/if}
      </div>
    {/if}
  </div>
{/if}

<style lang="css">
  .c-agent-footer {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-agent-footer__button-text {
    display: inline-flex;
    font-size: 0.8rem;
  }

  .c-agent-footer__button-text--short {
    display: none;
  }

  @media (max-width: 420px) and (max-height: 800px) {
    .c-agent-footer__button-text--full {
      display: none;
    }

    .c-agent-footer__button-text--short {
      display: inline;
    }
  }

  @media (max-width: 250px) and (min-height: 800px) {
    .c-agent-footer__button-text--full {
      display: none;
    }

    .c-agent-footer__button-text--short {
      display: inline;
    }
  }

  .c-agent-footer__button {
    width: 100%;
    display: flex;
    justify-content: stretch;
  }

  .c-agent-footer__button :global(> .l-tooltip-trigger) {
    width: 100%;
  }

  .c-agent-footer__button :global([data-tippy-root]) {
    pointer-events: none;
  }

  .c-agent-footer__button :global(.c-base-btn) {
    width: 100%;
  }

  .c-agent-footer__status {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    width: 100%;
  }

  .c-agent-footer__status:empty {
    margin-bottom: calc(0px - var(--ds-spacing-2));
  }

  /* Adjust AgentEditList to fit in the footer */
  .c-agent-footer__status :global(.c-agent-edits-container) {
    width: 100%;
  }

  .c-agent-footer__buttons {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }

  @media (max-height: 800px) and (min-width: 300px) {
    .c-agent-footer__buttons {
      flex-direction: row;
    }
  }
</style>

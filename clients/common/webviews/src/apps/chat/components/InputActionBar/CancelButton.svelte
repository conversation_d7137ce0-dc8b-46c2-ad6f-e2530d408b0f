<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import StopFilled from "$common-webviews/src/design-system/icons/augment/stop-filled.svelte";

  export let cancelAction: () => unknown;

  function handleClick(event: MouseEvent) {
    // Stop event propagation to prevent the richtexteditor from being focused
    event.stopPropagation();
    // Call the original cancel action
    cancelAction();
  }
</script>

<div class="c-cancel-button">
  <ButtonAugment size={1} variant="soft" color="neutral" on:click={handleClick}>
    <StopFilled />
  </ButtonAugment>
</div>

<style>
  .c-cancel-button {
    --cancel-stop-icon-size: 15px;
  }
  .c-cancel-button :global(svg) {
    color: var(--ds-color-error-9);
    height: var(--cancel-stop-icon-size);
    width: var(--cancel-stop-icon-size);
  }
</style>

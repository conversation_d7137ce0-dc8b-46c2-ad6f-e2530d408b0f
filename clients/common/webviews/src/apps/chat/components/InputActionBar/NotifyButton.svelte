<script lang="ts">
  import { getContext } from "svelte";
  import { type RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import {
    RemoteAgentStatus,
    RemoteAgentWorkspaceStatus,
  } from "$vscode/src/remote-agent-manager/types";
  import {
    getStatusColor,
    getWorkspaceStatusColor,
  } from "$common-webviews/src/apps/remote-agent-manager/utils";
  import { type ButtonColor } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import NotifyButtonView from "$common-webviews/src/design-system/components/NotifyButtonView.svelte";

  /** Agent ID to toggle the notification for */
  export let agentId: string | undefined = undefined;
  export let status: RemoteAgentStatus | undefined = undefined;
  export let workspaceStatus: RemoteAgentWorkspaceStatus | undefined = undefined;
  export let hasUpdates: boolean = false;

  const remoteAgentsModel = getContext<RemoteAgentsModel>("remoteAgentsModel");

  let statusColor: ButtonColor;
  $: {
    if (workspaceStatus === RemoteAgentWorkspaceStatus.workspaceRunning) {
      statusColor = getStatusColor(status);
    } else {
      statusColor = getWorkspaceStatusColor(workspaceStatus);
    }
  }
  $: isUnread =
    (status === RemoteAgentStatus.agentIdle || status === RemoteAgentStatus.agentFailed) &&
    hasUpdates;

  /**
   * Internal state of the current agent ID
   *
   * This is reactive if the agentId prop is not provided.
   */
  $: _agentId = agentId ?? $remoteAgentsModel.currentAgentId;

  /**
   * The current draft
   *
   * The user may want to toggle the notification setting before the agent is created.
   */
  $: draft = $remoteAgentsModel.newAgentDraft;

  let enabled = false;

  $: {
    if (_agentId) {
      enabled = $remoteAgentsModel.notificationSettings[_agentId] ?? true;
    } else if (draft) {
      enabled = draft.enableNotification;
    }
  }

  function toggle() {
    if (!_agentId) {
      const draft = remoteAgentsModel.newAgentDraft;
      if (draft) {
        // Toggle the notification setting in the draft when the agent is not created yet
        remoteAgentsModel.setNewAgentDraft({ ...draft, enableNotification: !enabled });
      }
      return;
    }
    remoteAgentsModel.setNotificationEnabled(_agentId, !enabled);
  }

  $: tooltipText = enabled
    ? "You'll be notified when the agent is ready for review. Click to turn off."
    : `Click to turn on notifications when the agent is ready for review. ${agentId === $remoteAgentsModel.currentAgentId ? "You will not be notified while you're actively viewing this agent." : ""}`;
</script>

<NotifyButtonView {enabled} {isUnread} {statusColor} {tooltipText} on:toggle={toggle} />

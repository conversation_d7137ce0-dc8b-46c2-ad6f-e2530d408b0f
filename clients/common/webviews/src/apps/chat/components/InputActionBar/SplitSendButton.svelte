<script lang="ts">
  import { getContext } from "svelte";
  import type { ChatModel } from "../../models/chat-model";
  import SplitButtonAugment from "$common-webviews/src/design-system/components/SplitButtonAugment";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import { onKey } from "$common-webviews/src/common/utils/keypress";

  export let disabled: boolean = false;
  export let onSelectModel: (modelId: string | null) => void;
  export let modelIdToDisplayName: { [modelId: string]: string };
  export let onSend: () => void;

  const chatModel = getContext<ChatModel>("chatModel");
  if (!chatModel) {
    throw new Error("ChatModel not found in context");
  }
</script>

<SplitButtonAugment.Root
  size={1}
  variant="solid"
  {disabled}
  showDropdown={Object.keys(modelIdToDisplayName).length > 1}
  dropdownSide="top"
  dropdownAlign="end"
  on:click={onSend}
  on:keyup={onKey("Enter", onSend)}
>
  <slot />

  <svelte:fragment slot="dropdown-content">
    <DropdownMenuAugment.Label>Models</DropdownMenuAugment.Label>
    {#each Object.keys(modelIdToDisplayName) as modelId}
      {@const displayName = modelIdToDisplayName[modelId]}
      {@const isHighlighted = modelId === chatModel.currentConversationModel.selectedModelId}
      <DropdownMenuAugment.Item
        highlight={isHighlighted}
        onSelect={() => {
          // Close the model selector after selecting it
          onSelectModel(modelId);
        }}
      >
        {displayName}
      </DropdownMenuAugment.Item>
    {/each}
  </svelte:fragment>
</SplitButtonAugment.Root>

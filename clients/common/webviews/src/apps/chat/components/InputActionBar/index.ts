/* eslint-disable @typescript-eslint/naming-convention */
import Root from "./Root.svelte";
import SendButton from "./SendButton.svelte";
import ContextMenu from "./ContextMenu.svelte";
import ActionsMenu from "./ActionsMenu.svelte";
import AddFileButton from "./AddFileButton.svelte";
import CancelButton from "./CancelButton.svelte";
import ResendButton from "./ResendButton.svelte";
import RewritePromptButton from "./RewritePromptButton.svelte";
import NotifyButton from "./NotifyButton.svelte";

export default {
  Root,
  SendButton,
  ContextMenu,
  ActionsMenu,
  AddFileButton,
  ResendButton,
  CancelButton,
  RewritePromptButton,
  NotifyButton,
};
/* eslint-enable @typescript-eslint/naming-convention */

<script lang="ts">
  import { getContext } from "svelte";

  import Send from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/paper-plane-top.svg?component";
  import type { ChatModel } from "../../models/chat-model";
  import type { AgentConversationModel } from "../../models/agent-conversation-model";
  import SplitSendButton from "./SplitSendButton.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  const chatModel = getContext<ChatModel>("chatModel");
  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");
  if (!chatModel) {
    throw new Error("ChatModel not found in context");
  }
  if (!agentConversationModel) {
    throw new Error("AgentConversationModel not found in context");
  }

  export let primaryAction: () => boolean;
  export let isDisabled: boolean = false;
  export let disabledReason: string = "";

  $: conversationModel = $chatModel.currentConversationModel;
  $: flagsModel = $chatModel.flags;

  let modelIdToDisplayName: { [modelId: string]: string } = {};
  $: {
    modelIdToDisplayName = Object.fromEntries(
      Object.entries($chatModel.flags.modelDisplayNameToId).map(([k, v]) => [v, k]),
    );
  }

  $: modelDisplayName = $flagsModel.getModelDisplayName($conversationModel.selectedModelId);
</script>

<TextTooltipAugment
  content={isDisabled ? disabledReason : ""}
  triggerOn={isDisabled ? [TooltipTriggerOn.Hover] : []}
>
  <SplitSendButton
    disabled={isDisabled}
    onSelectModel={(modelId) => conversationModel.setSelectedModelId(modelId)}
    onSend={primaryAction}
    {modelIdToDisplayName}
  >
    <Send />
    {#if modelDisplayName && modelDisplayName?.toLowerCase() !== "default"}
      <span class="c-model-preference-name">{modelDisplayName}</span>
    {/if}
  </SplitSendButton>
</TextTooltipAugment>

<style>
  .c-model-preference-name {
    margin-left: var(--ds-spacing-1);
    text-wrap: nowrap;
  }
</style>

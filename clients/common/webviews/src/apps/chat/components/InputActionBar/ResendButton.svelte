<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import Reload from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/rotate-right.svg?component";

  export let resendAction: () => unknown;
</script>

<div class="c-resend-button">
  <ButtonAugment
    size={1}
    variant="soft"
    color="neutral"
    on:click={resendAction}
    on:keyup={resendAction}
  >
    <Reload slot="iconLeft" />
    <TextAugment size={1}>Resend</TextAugment>
  </ButtonAugment>
</div>

<style>
  .c-resend-button {
    --cancel-stop-icon-size: 15px;
  }
  .c-resend-button :global(svg) {
    height: var(--cancel-stop-icon-size);
    width: var(--cancel-stop-icon-size);
  }
</style>

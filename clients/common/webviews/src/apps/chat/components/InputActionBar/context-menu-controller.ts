import { derived, get, writable, type Writable, type Readable } from "svelte/store";
import type { IChatMentionable, IChatGroup } from "../../types/mention-option";
import {
  CLEAR_CONTEXT,
  getIncludedGroups,
  isItemAllDefaultContext,
  isItemGroup,
  USE_DEFAULT_CONTEXT,
  USER_GUIDELINES,
} from "../../types/mention-option";
import type { ChatModel } from "../../models/chat-model";
import { throttle } from "lodash";
import Fuse from "fuse.js";
import { ChatMetricName } from "$vscode/src/metrics/types";
import { isAgentConversation } from "../../models/types";

export type ChatMentionableDropdownData = IChatMentionable & {
  type: "breadcrumb" | "breadcrumb-back" | "item";
};

/**
 * Controller for the context menu dropdown.
 * It only actively controls state when the dropdown is open.
 *
 * The controller interacts with the system in the following way:
 *
 *    +-------------------------+
 *    |    User Actions         |
 *    +-------------------------+
 *    | 1. Open/Close Dropdown  |
 *    | 2. Navigate Breadcrumbs |
 *    | 3. Type Search Query    |
 *    | 4. Select Mentionable   |
 *    +-------------------------+
 *                |
 *                v
 *    +-------------------------+
 *    |    Controller Hooks     |
 *    +-------------------------+
 *    | 1. Update Dropdown State|
 *    | 2. Update Breadcrumbs   |
 *    | 3. Update Search Query  |
 *    | 4. Handle Selection     |
 *    +-------------------------+
 *                |
 *         +------+------+
 *         |             |
 *         v             v
 * +-----------+ +--------------------+
 * |  Refresh  | |     Compute        |
 * |   Data    | | Intermediate State |
 * +-----------+ +--------------------+
 *         |     | 1. Current Group   |
 *         |     | 2. All Groups      |
 *         |     | 3. Breadcrumb IDs  |
 *         |     +--------------------+
 *         |             |
 *         +-------------+
 *                |
 *                v
 *    +-------------------------+
 *    |    Compute Display      |
 *    |         Items           |
 *    +-------------------------+
 *                |
 *                v
 *    +-------------------------+
 *    |         Output          |
 *    +-------------------------+
 *    | 1. Updated Display Items|
 *    | 2. Callback Execution   |
 *    |    (on selection)       |
 *    +-------------------------+
 *
 * 1. User-triggered actions call exposed hooks
 * 2. Hooks update internal state and trigger data refresh and intermediate state computation
 * 3. Refreshed data and intermediate state flow into display item computation
 * 4. Output new display items, and call the callback for selections if needed
 */
export class ContextMenuController {
  /* eslint-disable @typescript-eslint/naming-convention */
  /**
   * The delay in milliseconds between refreshes of mentionables.
   * This helps to prevent excessive API calls when the user is typing quickly.
   */
  public static readonly REFRESH_THROTTLE_MS = 600;

  /**
   * The maximum number of items to display when showing a single group.
   * This helps to keep the dropdown menu from becoming too long.
   */
  public static readonly SINGLE_GROUP_MAX_ITEMS = 12;

  /**
   * The maximum number of items to display per group when showing multiple groups.
   * This helps to keep the dropdown menu from becoming too cluttered.
   */
  public static readonly MULTI_GROUP_MAX_ITEMS = 6;
  /* eslint-enable @typescript-eslint/naming-convention */

  /**
   * Array of functions to be called when the controller is disposed.
   * These functions typically unsubscribe from Svelte stores.
   */
  private _disposers: Array<() => void> = [];

  /**
   * Svelte store containing all fetched mentionables.
   * This is the raw data fetched from the API.
   */
  private _allMentionables: Writable<IChatMentionable[]> = writable([]);

  /**
   * Svelte store containing the current breadcrumb path.
   * Each string in the array represents the ID of a breadcrumb.
   */
  private _breadcrumbIds: Writable<string[]> = writable([]);

  /**
   * Svelte store containing the current search query.
   * This is updated by the input field as the user types.
   */
  private _userQuery: Writable<string> = writable("");

  /**
   * Svelte store indicating whether the dropdown is currently open.
   * This is updated by the trigger that opens/closes the dropdown.
   */
  private _active: Writable<boolean> = writable(false);

  /**
   * Derived Svelte store that computes all of the groups from the mentionables.
   * It only returns groups when the dropdown is active.
   */
  private _allGroups: Readable<IChatGroup[]> = derived(
    [this._active, this._allMentionables],
    ([$active, $allMentionables]) => {
      return $active ? getIncludedGroups($allMentionables) : [];
    },
  );

  /**
   * Derived Svelte store that finds the latest state of the current group.
   * It uses the last breadcrumb ID to find the corresponding group in allGroups.
   */
  private _currentGroup: Readable<IChatGroup | undefined> = derived(
    [this._breadcrumbIds, this._allGroups],
    ([$breadcrumbIds, $allGroups]) => {
      // If we have no breadcrumbs, we have no current group
      if ($breadcrumbIds.length === 0) {
        return undefined;
      }

      // Get the ID of the last breadcrumb
      const lastId = $breadcrumbIds[$breadcrumbIds.length - 1];
      // Find the group with the matching ID
      const group = $allGroups.find((s) => isItemGroup(s) && s.id === lastId) as
        | IChatGroup
        | undefined;
      return group;
    },
  );

  /**
   * Constructor for the ContextMenuController.
   * @param _chatModel The chat model to use for API calls and context management.
   * @param _insertMentionNode Callback function to be called when a mentionable node is inserted.
   */
  constructor(
    private _chatModel: ChatModel,
    private _insertMentionNode: undefined | ((mentionable: IChatMentionable) => boolean),
  ) {
    // Set up subscriptions to refresh mentionables when query or active state changes
    this._disposers.push(this._userQuery.subscribe(this._refreshMentionables));
    this._disposers.push(this._active.subscribe(this._refreshMentionables));
  }

  /**
   * Disposes of the controller by calling all disposer functions.
   * This should be called when the controller is no longer needed to prevent memory leaks.
   */
  dispose = (): void => {
    for (const dispose of this._disposers) {
      dispose();
    }
  };

  // ==== Public Reactive State API ====
  public get allGroups(): Readable<IChatGroup[]> {
    return this._allGroups;
  }

  public get currentGroup(): Readable<IChatGroup | undefined> {
    return this._currentGroup;
  }

  public get breadcrumbIds(): Readable<string[]> {
    return this._breadcrumbIds;
  }

  public get displayItems(): Readable<ChatMentionableDropdownData[]> {
    return this._displayItems;
  }

  public get active(): Readable<boolean> {
    return this._active;
  }

  get userQuery(): Writable<string> {
    return this._userQuery;
  }

  // ==== Public Hooks API ====
  openDropdown = (): void => {
    this._active.set(true);
  };

  closeDropdown = (): void => {
    this._active.set(false);
    this._resetState();
  };

  toggleDropdown = (): boolean => {
    if (get(this._active)) {
      this.closeDropdown();
      return false;
    } else {
      this.openDropdown();
      return true;
    }
  };

  /**
   * Adds a new breadcrumb to the path.
   * @param group The group to add as a breadcrumb.
   */
  pushBreadcrumb = (group: IChatGroup): void => {
    // If not active, do nothing
    if (!get(this._active)) {
      return;
    }
    this._breadcrumbIds.update((v) => [...v, group.id]);
  };

  /**
   * Removes the last breadcrumb from the path.
   */
  popBreadcrumb = (): void => {
    // If not active, do nothing
    if (!get(this._active)) {
      return;
    }
    this._breadcrumbIds.update((v) => v.slice(0, -1));
  };

  /**
   * Handles the selection of a mentionable item.
   * This method performs different actions based on the type of mentionable selected.
   * @param mentionable The selected mentionable item.
   */
  selectMentionable = (mentionable: ChatMentionableDropdownData): boolean => {
    const extensionClient = this._chatModel.extensionClient;
    const contextModel = this._chatModel.specialContextInputModel;
    // If we select a group, push it as a breadcrumb
    if (isItemGroup(mentionable) && mentionable.type === "breadcrumb") {
      this.pushBreadcrumb(mentionable);
      return true;
      // If we select the backlink, pop the last breadcrumb
    } else if (mentionable.type === "breadcrumb-back") {
      this.popBreadcrumb();
      return true;
      // If we select "all default context", mark all inactive items as active
    } else if (isItemAllDefaultContext(mentionable)) {
      contextModel.markAllActive();
      this.closeDropdown();
      extensionClient.reportWebviewClientEvent(ChatMetricName.chatRestoreDefaultContext);
      return true;
      // If we select "clear context", mark all active items as inactive
    } else if (mentionable.clearContext) {
      contextModel.markAllInactive();
      this.closeDropdown();
      extensionClient.reportWebviewClientEvent(ChatMetricName.chatClearContext);
      return true;
      // If we select "user guidelines", open the user guidelines input in the settings panel
    } else if (mentionable.userGuidelines) {
      extensionClient.openSettingsPage("userGuidelines");
      this.closeDropdown();
      return true;
      // If we select a normal item, call the callback and close the dropdown
    } else {
      this._insertMentionNode?.(mentionable);
      this.closeDropdown();
      return true;
    }
    return false;
  };

  /**
   * Derived store that computes the items to display in the dropdown.
   * The displayed items change based on the current state (active, breadcrumbs, query, etc.).
   */
  private _displayItems = derived(
    [this._active, this._breadcrumbIds, this._userQuery, this._currentGroup, this.allGroups],
    ([
      $active,
      $breadcrumbIds,
      $userQuery,
      $currentGroup,
      $allGroups,
    ]): ChatMentionableDropdownData[] => {
      if (!$active) {
        return [];
      }

      // If we have breadcrumbs and a current group, show the current group with a backlink and its items
      if ($breadcrumbIds.length > 0 && $currentGroup) {
        return [
          { ...$currentGroup, type: "breadcrumb-back" },
          ...$currentGroup.group.items
            .slice(0, ContextMenuController.SINGLE_GROUP_MAX_ITEMS)
            .map((i): ChatMentionableDropdownData => ({ ...i, type: "item" })),
        ];
        // If we have no breadcrumbs but a query, show all groups and their items
      } else if ($userQuery.length > 0) {
        return $allGroups.flatMap((group): ChatMentionableDropdownData[] => [
          { ...group, type: "breadcrumb" },
          ...group.group.items
            .slice(0, ContextMenuController.MULTI_GROUP_MAX_ITEMS)
            .map((i): ChatMentionableDropdownData => ({ ...i, type: "item" })),
        ]);
        // If we have no breadcrumbs and no query, show all groups as breadcrumbs, no items
      } else {
        return [
          { ...USE_DEFAULT_CONTEXT, type: "item" },
          ...$allGroups.map((i): ChatMentionableDropdownData => ({ ...i, type: "breadcrumb" })),
          { ...CLEAR_CONTEXT, type: "item" },
          { ...USER_GUIDELINES, type: "item" },
        ];
      }
    },
  );

  /**
   * A throttled function to refresh the mentionables.
   * If a newer request comes in while we're waiting, we'll ignore the old one.
   */
  private _refreshSeqNum: number = 0;
  private _refreshMentionables = throttle(
    async (): Promise<void> => {
      if (!get(this._active)) {
        return;
      }

      // Reserve a sequence number for this refresh
      this._refreshSeqNum++;
      const seqNum = this._refreshSeqNum;

      // Check if the current conversation is in agent mode
      const isAgentMode =
        this._chatModel.currentConversationModel &&
        isAgentConversation(this._chatModel.currentConversationModel);

      // Fetch mentionables
      const userQuery = get(this._userQuery);
      const mentionables = await this._chatModel.extensionClient.getSuggestions(
        userQuery,
        isAgentMode,
      );

      // If this is the latest call in the sequence, update the mentionables
      if (seqNum === this._refreshSeqNum) {
        this._allMentionables.set(sortMentionables(userQuery, mentionables));
      }
    },
    ContextMenuController.REFRESH_THROTTLE_MS,
    { leading: true, trailing: true },
  );

  /**
   * Resets the internal state of the controller.
   * This is typically called when closing the dropdown.
   */
  private _resetState(): void {
    this._breadcrumbIds.set([]);
    this._userQuery.set("");
  }
}

/**
 * Uses Fuse.js to sort mentionables based on the given query.
 * This function provides fuzzy search capabilities for mentionable items.
 *
 * @param query The search query to use for sorting.
 * @param mentionables The array of mentionables to sort.
 * @returns A sorted array of mentionables based on the query.
 */
const sortMentionables = (query: string, mentionables: IChatMentionable[]): IChatMentionable[] => {
  // If the query is very short, don't bother sorting
  if (query.length <= 1) {
    return mentionables;
  }

  // Configure Fuse.js for fuzzy searching
  const fuse = new Fuse(mentionables, {
    keys: ["label"],
    threshold: 1.0,
    minMatchCharLength: 0,
    ignoreLocation: true,
    includeScore: true,
    useExtendedSearch: false,
    shouldSort: true,
    findAllMatches: true,
  });

  // Perform the search
  const result = fuse.search(query);

  // If no results, return the original array
  if (result.length === 0) {
    return mentionables;
  }

  // Return the sorted items
  return result.map((x) => x.item);
};

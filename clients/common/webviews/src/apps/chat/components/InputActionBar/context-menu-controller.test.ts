import { expect, describe, it, beforeEach, vi, afterEach } from "vitest";

import { type ChatMentionableDropdownData, ContextMenuController } from "./context-menu-controller";
import {
  type IChatMentionable,
  type IChatGroup,
  USE_DEFAULT_CONTEXT,
  CLEAR_CONTEXT,
  USER_GUIDELINES,
} from "../../types/mention-option";
import { type ChatModel } from "../../models/chat-model";
import { get } from "svelte/store";

let contextMenuController: ContextMenuController;
let chatModel: ChatModel;
const onInsertMentionable = vi.fn();
beforeEach(() => {
  vi.useFakeTimers();
  chatModel = {
    extensionClient: {
      reportWebviewClientEvent: vi.fn(),
      getSuggestions: vi.fn(
        (query: string): Promise<IChatMentionable[]> =>
          Promise.resolve([
            createMockFile(`${query}-file-1`),
            createMockFile(`${query}-file-2`),
            createMockFolder(`${query}-folder-1`),
            createMockFolder(`${query}-folder-2`),
          ]),
      ),
      openSettingsPage: vi.fn(),
    },
    specialContextInputModel: {
      markAllActive: vi.fn(),
      markAllInactive: vi.fn(),
      openUserGuidelinesInput: vi.fn(),
      recentInactiveItems: [{ id: "1", label: "Inactive Item" }],
      recentActiveItems: [{ id: "2", label: "Active Item" }],
    },
  } as unknown as ChatModel;
  contextMenuController = new ContextMenuController(chatModel, onInsertMentionable);
});

afterEach(() => {
  contextMenuController.dispose();
  vi.useRealTimers();
});

describe("ContextMenuController - Open/Close", () => {
  it("should start closed", () => {
    expect(get(contextMenuController.active)).toBe(false);
  });

  it("should open and close the dropdown", async () => {
    expect(get(contextMenuController.active)).toBe(false);
    await contextMenuController.openDropdown();
    expect(get(contextMenuController.active)).toBe(true);
    contextMenuController.closeDropdown();
    expect(get(contextMenuController.active)).toBe(false);
  });

  it("should toggle the dropdown", () => {
    expect(get(contextMenuController.active)).toBe(false);
    contextMenuController.toggleDropdown();
    expect(get(contextMenuController.active)).toBe(true);
    contextMenuController.toggleDropdown();
    expect(get(contextMenuController.active)).toBe(false);
  });

  it("state should reset on close", () => {
    // Open + set a bunch of state
    contextMenuController.openDropdown();
    contextMenuController.userQuery.set("test");
    contextMenuController.pushBreadcrumb(createEmptyGroup("Test Group"));
    expect(get(contextMenuController.breadcrumbIds)).toEqual([createEmptyGroup("Test Group").id]);
    expect(get(contextMenuController.userQuery)).toEqual("test");
    expect(get(contextMenuController.active)).toBe(true);

    // Close and verify state is reset
    contextMenuController.closeDropdown();
    expect(get(contextMenuController.breadcrumbIds)).toEqual([]);
    expect(get(contextMenuController.userQuery)).toEqual("");
    expect(get(contextMenuController.active)).toBe(false);
  });
});

describe("ContextMenuController - Breadcrumbs", () => {
  it("should start with no breadcrumbs", () => {
    expect(get(contextMenuController.breadcrumbIds)).toEqual([]);
  });

  it("if not active, push breadcrumbs should not change state", () => {
    expect(get(contextMenuController.breadcrumbIds)).toEqual([]);
    contextMenuController.pushBreadcrumb(createEmptyGroup("Test Group"));
    expect(get(contextMenuController.breadcrumbIds)).toEqual([]);
  });

  it("if active, push and pop breadcrumbs should change state", () => {
    contextMenuController.openDropdown();
    expect(get(contextMenuController.breadcrumbIds)).toEqual([]);
    const group = createEmptyGroup("Test Group");
    expect(get(contextMenuController.breadcrumbIds)).toEqual([]);
    contextMenuController.pushBreadcrumb(group);
    expect(get(contextMenuController.breadcrumbIds)).toEqual([group.id]);
    contextMenuController.popBreadcrumb();
    expect(get(contextMenuController.breadcrumbIds)).toEqual([]);
  });
});

describe("ContextMenuController - Mentionables", () => {
  it("should start with no mentionables", () => {
    expect(get(contextMenuController.allGroups)).toEqual([]);
  });

  it("should update mentionables when opening the dropdown", async () => {
    expect(get(contextMenuController.allGroups)).toEqual([]);
    await contextMenuController.openDropdown();
    await vi.advanceTimersByTimeAsync(ContextMenuController.REFRESH_THROTTLE_MS);
    expect(get(contextMenuController.allGroups)).not.toEqual([]);
    expect(get(contextMenuController.allGroups)).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ group: expect.objectContaining({ type: "file" }) }),
        expect.objectContaining({ group: expect.objectContaining({ type: "folder" }) }),
      ]),
    );
  });

  it("should update mentionables when query changes", async () => {
    await contextMenuController.openDropdown();
    expect(get(contextMenuController.allGroups)).toEqual([]);
    contextMenuController.userQuery.set("test");
    await vi.advanceTimersByTimeAsync(ContextMenuController.REFRESH_THROTTLE_MS);

    // Verify the mentionables are correct
    expect(get(contextMenuController.allGroups)).not.toEqual([]);
    expect(get(contextMenuController.allGroups)).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          group: expect.objectContaining({
            type: "file",
            items: expect.arrayContaining([
              expect.objectContaining({ label: "test-file-1" }),
              expect.objectContaining({ label: "test-file-2" }),
            ]),
          }),
        }),
        expect.objectContaining({
          group: expect.objectContaining({
            type: "folder",
            items: expect.arrayContaining([
              expect.objectContaining({ label: "test-folder-1" }),
              expect.objectContaining({ label: "test-folder-2" }),
            ]),
          }),
        }),
      ]),
    );
  });

  it("selecting default context should call markItemsActive", () => {
    const defaultContext: ChatMentionableDropdownData = {
      id: "1",
      label: "Default Context",
      allDefaultContext: true,
      type: "item",
    };
    const mockInputModel = chatModel.specialContextInputModel;
    expect(mockInputModel.markAllActive).not.toHaveBeenCalled();
    contextMenuController.selectMentionable(defaultContext);
    expect(mockInputModel.markAllActive).toHaveBeenCalled();
  });

  it("selecting clear context should call markItemsInactive", () => {
    const clearContext: ChatMentionableDropdownData = {
      id: "1",
      label: "Clear Context",
      clearContext: true,
      type: "item",
    };
    const mockInputModel = chatModel.specialContextInputModel;
    expect(mockInputModel.markAllInactive).not.toHaveBeenCalled();
    contextMenuController.selectMentionable(clearContext);
    expect(mockInputModel.markAllInactive).toHaveBeenCalled();
  });

  it("selecting user guidelines should open the settings page with userGuidelines section", () => {
    const userGuidelines: ChatMentionableDropdownData = {
      id: "1",
      label: "User Guidelines",
      userGuidelines: { enabled: true, overLimit: false, contents: "", lengthLimit: 2000 },
      type: "item",
    };
    const extensionClient = chatModel.extensionClient;
    expect(extensionClient.openSettingsPage).not.toHaveBeenCalled();
    contextMenuController.selectMentionable(userGuidelines);
    expect(extensionClient.openSettingsPage).toHaveBeenCalledWith("userGuidelines");
  });

  it("should reset state when closing the dropdown", () => {
    contextMenuController.pushBreadcrumb(createEmptyGroup("Test Group"));
    contextMenuController.closeDropdown();
    expect(get(contextMenuController.allGroups)).toEqual([]);
  });

  describe("Selecting an item", () => {
    beforeEach(async () => {
      await contextMenuController.openDropdown();
    });

    afterEach(() => {
      contextMenuController.closeDropdown();
    });

    it("should call onSelectMentionable", () => {
      const mentionable: ChatMentionableDropdownData = {
        id: "1",
        label: "Mentionable 1",
        type: "item",
      };
      expect(onInsertMentionable).not.toHaveBeenCalled();
      contextMenuController.selectMentionable(mentionable);
      expect(onInsertMentionable).toHaveBeenCalledWith(mentionable);
    });

    it("should close the dropdown", () => {
      expect(get(contextMenuController.active)).toBe(true);
      contextMenuController.selectMentionable({ id: "1", label: "Mentionable 1", type: "item" });
      expect(get(contextMenuController.active)).toBe(false);
    });

    it("should reset state", () => {
      contextMenuController.userQuery.set("test");
      contextMenuController.selectMentionable({ id: "1", label: "Mentionable 1", type: "item" });
      expect(get(contextMenuController.userQuery)).toEqual("");
    });

    it("should push breadcrumbs if selecting a group", () => {
      const group = createEmptyGroup("Test Group");
      expect(get(contextMenuController.breadcrumbIds)).toEqual([]);
      contextMenuController.selectMentionable(group);
      expect(get(contextMenuController.breadcrumbIds)).toEqual([group.id]);
    });

    it("should not push breadcrumbs if selecting a mentionable", () => {
      const mentionable: ChatMentionableDropdownData = {
        id: "1",
        label: "Mentionable 1",
        type: "item",
      };
      expect(get(contextMenuController.breadcrumbIds)).toEqual([]);
      contextMenuController.selectMentionable(mentionable);
      expect(get(contextMenuController.breadcrumbIds)).toEqual([]);
    });

    it("should not push breadcrumbs or call onSelectMentionable if selecting a special item", () => {
      expect(get(contextMenuController.breadcrumbIds)).toEqual([]);
      contextMenuController.selectMentionable({ ...USE_DEFAULT_CONTEXT, type: "item" });
      contextMenuController.selectMentionable({ ...CLEAR_CONTEXT, type: "item" });
      contextMenuController.selectMentionable({ ...USER_GUIDELINES, type: "item" });
      expect(get(contextMenuController.breadcrumbIds)).toEqual([]);
      expect(onInsertMentionable).not.toHaveBeenCalled();
    });

    it("calls markAllActive if selecting default context", () => {
      const mockInputModel = chatModel.specialContextInputModel;
      expect(mockInputModel.markAllActive).not.toHaveBeenCalled();
      contextMenuController.selectMentionable({ ...USE_DEFAULT_CONTEXT, type: "item" });
      expect(mockInputModel.markAllActive).toHaveBeenCalled();
    });

    it("calls markAllInactive if selecting clear context", () => {
      const mockInputModel = chatModel.specialContextInputModel;
      expect(mockInputModel.markAllInactive).not.toHaveBeenCalled();
      contextMenuController.selectMentionable({ ...CLEAR_CONTEXT, type: "item" });
      expect(mockInputModel.markAllInactive).toHaveBeenCalled();
    });

    it("calls openSettingsPage if selecting user guidelines", () => {
      const extensionClient = chatModel.extensionClient;
      expect(extensionClient.openSettingsPage).not.toHaveBeenCalled();
      contextMenuController.selectMentionable({ ...USER_GUIDELINES, type: "item" });
      expect(extensionClient.openSettingsPage).toHaveBeenCalledWith("userGuidelines");
    });
  });
});

describe("ContextMenuController - Display Items Reactivity", () => {
  it("should start with no display items", () => {
    expect(get(contextMenuController.displayItems)).toEqual([]);
  });

  it("should update display items when opening the dropdown", async () => {
    expect(get(contextMenuController.displayItems)).toEqual([]);
    await contextMenuController.openDropdown();
    await vi.advanceTimersByTimeAsync(ContextMenuController.REFRESH_THROTTLE_MS);

    expect(get(contextMenuController.displayItems)).not.toEqual([]);
    expect(get(contextMenuController.displayItems)).toEqual(
      expect.arrayContaining([
        expect.objectContaining(USE_DEFAULT_CONTEXT),
        expect.objectContaining({
          group: expect.objectContaining({ type: "file" }),
          type: "breadcrumb",
        }),
        expect.objectContaining({
          group: expect.objectContaining({ type: "folder" }),
          type: "breadcrumb",
        }),
        expect.objectContaining(CLEAR_CONTEXT),
        expect.objectContaining(USER_GUIDELINES),
      ]),
    );
  });

  it("if a query is set do not show default context, clear context, or user guidelines", async () => {
    await contextMenuController.openDropdown();

    // Start with default context, clear context, and user guidelines
    expect(get(contextMenuController.displayItems)).toEqual(CONTAINS_DEFAULT_ITEMS);
    contextMenuController.userQuery.set("test");
    await vi.advanceTimersByTimeAsync(ContextMenuController.REFRESH_THROTTLE_MS);

    // Verify the display items do not contain default context, clear context, or user guidelines
    expect(get(contextMenuController.displayItems)).not.toEqual([]);
    expect(get(contextMenuController.displayItems)).not.toEqual(CONTAINS_DEFAULT_ITEMS);
  });

  it("if breadcrumbs are set do not show default context, clear context, or user guidelines", async () => {
    await contextMenuController.openDropdown();
    await vi.advanceTimersByTimeAsync(ContextMenuController.REFRESH_THROTTLE_MS);

    // Start with default context, clear context, and user guidelines
    expect(get(contextMenuController.displayItems)).toEqual(CONTAINS_DEFAULT_ITEMS);
    contextMenuController.pushBreadcrumb(createEmptyGroup("folders"));
    await vi.advanceTimersByTimeAsync(ContextMenuController.REFRESH_THROTTLE_MS);

    // Verify the display items do not contain default context, clear context, or user guidelines
    expect(get(contextMenuController.displayItems)).not.toEqual([]);
    expect(get(contextMenuController.displayItems)).toEqual(
      expect.not.arrayContaining([
        expect.objectContaining(USE_DEFAULT_CONTEXT),
        expect.objectContaining(CLEAR_CONTEXT),
        expect.objectContaining(USER_GUIDELINES),
      ]),
    );

    // Pop the breadcrumb
    contextMenuController.popBreadcrumb();
    await vi.advanceTimersByTimeAsync(ContextMenuController.REFRESH_THROTTLE_MS);

    // Verify the display items now contain default context, clear context, and user guidelines
    expect(get(contextMenuController.displayItems)).toEqual(CONTAINS_DEFAULT_ITEMS);
  });
});

describe("ContextMenuController - Query", () => {
  it("if not active, query should not trigger refresh", async () => {
    contextMenuController.userQuery.set("test");
    await vi.advanceTimersByTimeAsync(ContextMenuController.REFRESH_THROTTLE_MS);
    expect(chatModel.extensionClient.getSuggestions).not.toHaveBeenCalled();
  });

  it("if active, query should trigger refresh", async () => {
    await contextMenuController.openDropdown();
    expect(chatModel.extensionClient.getSuggestions).not.toHaveBeenCalled();
    contextMenuController.userQuery.set("test");
    await vi.advanceTimersByTimeAsync(ContextMenuController.REFRESH_THROTTLE_MS);
    expect(chatModel.extensionClient.getSuggestions).toHaveBeenCalled();
  });

  it("if active, query should only trigger refresh once per throttle period", async () => {
    await contextMenuController.openDropdown();
    expect(chatModel.extensionClient.getSuggestions).not.toHaveBeenCalled();
    contextMenuController.userQuery.set("test");
    await vi.advanceTimersByTimeAsync(ContextMenuController.REFRESH_THROTTLE_MS / 2);
    contextMenuController.userQuery.set("test2");
    await vi.advanceTimersByTimeAsync(ContextMenuController.REFRESH_THROTTLE_MS);
    expect(chatModel.extensionClient.getSuggestions).toHaveBeenCalledTimes(1);
  });
});

// Helper match target for default items
const CONTAINS_DEFAULT_ITEMS = expect.arrayContaining([
  expect.objectContaining(USE_DEFAULT_CONTEXT),
  expect.objectContaining(CLEAR_CONTEXT),
  expect.objectContaining(USER_GUIDELINES),
]);

function createEmptyGroup(name: string): IChatGroup & ChatMentionableDropdownData {
  return {
    id: `${name}`,
    label: name,
    group: {
      items: [],
      materialIcon: "test-icon",
      type: "file",
      backlinks: [],
      class: "test-class",
    },
    type: "breadcrumb",
  };
}

function createMockFile(name: string): IChatMentionable {
  return {
    id: `${name}-id`,
    label: name,
    file: {
      repoRoot: "/home/<USER>/example-repo-root",
      pathName: name,
    },
  };
}

function createMockFolder(name: string): IChatMentionable {
  return {
    id: `${name}-id`,
    label: name,
    folder: {
      repoRoot: "/home/<USER>/example-repo-root",
      pathName: name,
    },
  };
}

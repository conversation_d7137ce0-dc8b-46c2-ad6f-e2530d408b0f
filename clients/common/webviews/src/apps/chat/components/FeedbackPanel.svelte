<script lang="ts">
  import { onKey } from "../../../common/utils/keypress";
  import { FeedbackRating } from "../types/feedback-rating";
  import type { FeedbackState } from "../types/feedback-state";
  import VsCodeTextArea from "$common-webviews/src/common/components/vscode/VSCodeTextArea.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import Clipboard from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/clipboard.svg?component";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";
  import CopyButton from "$common-webviews/src/common/components/CopyButton.svelte";
  import ThumbsDown from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/thumbs-down.svg?component";
  import ThumbsUp from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/thumbs-up.svg?component";

  export let requestId: string;
  export let feedbackState: FeedbackState | undefined; // Current state in the backend
  export let onSendUserRating: (
    requestId: string,
    rating: FeedbackRating,
    note?: string,
  ) => Promise<void>;
  export let showFeedbackInput: boolean = false;
  export let reponseText: string | undefined;
  let feedbackToSend: FeedbackState = {
    selectedRating: feedbackState?.selectedRating ?? FeedbackRating.unset,
    feedbackNote: feedbackState?.feedbackNote ?? "",
  };

  function onNoteChange(e: Event) {
    const textarea = e.target as HTMLTextAreaElement;
    feedbackToSend.feedbackNote = textarea.value;
  }

  function handleSendFeedback() {
    showFeedbackInput = false;
    onSendUserRating(requestId, feedbackToSend.selectedRating, feedbackToSend.feedbackNote);
    showThankYouNote();
  }

  function onFeedbackClick(rating: FeedbackRating) {
    feedbackToSend.selectedRating = rating;
    showFeedbackInput = true;
  }

  function cancelFeedback() {
    showFeedbackInput = false;

    // Reset feedback to the last known state
    if (feedbackState) {
      feedbackToSend = { ...feedbackState };
    } else {
      feedbackToSend.selectedRating = FeedbackRating.unset;
    }
  }

  let thankYouNote = "";
  let thankYouTimeout: ReturnType<typeof setTimeout> | undefined;
  function showThankYouNote() {
    thankYouNote = "Thanks for the feedback!";
    clearTimeout(thankYouTimeout);
    thankYouTimeout = setTimeout(() => {
      thankYouNote = "";
    }, 3000);
  }

  function scrollOnShow(node: HTMLElement) {
    node.scrollIntoView({
      block: "end",
      behavior: "smooth",
    });
  }
</script>

<div class="c-feedback">
  <div class="c-feedback__ratings">
    <div class="c-feedback__buttons">
      <slot />
      <TextTooltipAugment content="Give feedback">
        <IconButtonAugment
          class={`c-feedback__positive ${feedbackToSend.selectedRating === FeedbackRating.positive ? "c-feedback--selected" : ""}`}
          title="Feedback Positive"
          variant="ghost-block"
          size={1}
          color={feedbackToSend.selectedRating === FeedbackRating.positive ? "success" : "neutral"}
          on:keyup={onKey("Enter", () => onFeedbackClick(FeedbackRating.positive))}
          on:click={() => onFeedbackClick(FeedbackRating.positive)}
        >
          <ThumbsUp />
        </IconButtonAugment>
      </TextTooltipAugment>
      <TextTooltipAugment content="Give feedback">
        <IconButtonAugment
          class={`c-feedback__negative ${feedbackToSend.selectedRating === FeedbackRating.negative ? "c-feedback--selected" : ""}`}
          title="Feedback Negative"
          variant="ghost-block"
          size={1}
          color={feedbackToSend.selectedRating === FeedbackRating.negative ? "error" : "neutral"}
          on:keyup={onKey("Enter", () => onFeedbackClick(FeedbackRating.negative))}
          on:click={() => onFeedbackClick(FeedbackRating.negative)}
        >
          <ThumbsDown />
        </IconButtonAugment>
      </TextTooltipAugment>
      {#if reponseText}
        <CopyButton text={reponseText} tooltip="Copy response" />
      {/if}
      <CopyButton text={requestId} tooltip="Copy Message ID">
        <Clipboard slot="icon" />
      </CopyButton>
      <span class="c-feedback__thankyou">{thankYouNote}</span>
    </div>
  </div>
  {#if showFeedbackInput}
    <div class="c-feedback__input-container">
      <CardAugment size={1}>
        <VsCodeTextArea
          class="c-feedback__textarea"
          rows={3}
          placeholder="Enter your feedback..."
          value={feedbackToSend.feedbackNote}
          on:input={onNoteChange}
        />
        <div class="l-feedback__send" use:scrollOnShow>
          <CopyButton text={requestId} tooltip="Copy Message ID">Message ID</CopyButton>
          <div class="l-feedback__send-btns">
            <ButtonAugment
              class="c-feedback__cancel"
              title="Cancel"
              variant="soft"
              color="neutral"
              on:keyup={onKey("Enter", cancelFeedback)}
              on:click={cancelFeedback}
              size={1}
            >
              Cancel
            </ButtonAugment>
            <ButtonAugment
              class="c-feedback__send"
              title="Share feedback"
              variant="soft"
              color="accent"
              on:keyup={onKey("Enter", handleSendFeedback)}
              on:click={handleSendFeedback}
              size={1}
            >
              {feedbackState ? "Update feedback" : "Share feedback"}
            </ButtonAugment>
          </div>
        </div>
      </CardAugment>
    </div>
  {/if}
</div>

<style>
  .c-feedback {
    grid-area: 1 / 1 / 4 / 4;
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .c-feedback__input-container {
    margin-top: var(--ds-spacing-2);
  }

  .c-feedback :global(.c-feedback__textarea) {
    width: 100%;
    min-height: 75px;
    resize: none;
    font-size: inherit;
    color: inherit;
    margin-top: 5px;
    margin-bottom: 5px;
    font-size: 12px;
  }

  .c-feedback__buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .l-feedback__send {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--ds-spacing-2);
  }

  .l-feedback__send-btns {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-1);
  }

  .c-feedback__thankyou {
    flex: 1 1 auto;
    text-align: right;
  }

  .c-feedback__ratings :global(.c-feedback__positive:not([disabled]):hover),
  .c-feedback__ratings :global(.c-feedback__positive.c-feedback--selected) {
    color: var(
      --vscode-gitDecoration-addedResourceForeground,
      var(--intellij-progressBar-passedColor)
    );
  }

  .c-feedback__ratings :global(.c-feedback__negative:not([disabled]):hover),
  .c-feedback__ratings :global(.c-feedback__negative.c-feedback--selected) {
    color: var(
      --vscode-gitDecoration-deletedResourceForeground,
      var(--intellij-progressBar-failedColor)
    );
  }
</style>

import { render } from "@testing-library/svelte";
import { expect, describe, test, vi, beforeEach } from "vitest";

import ThreadsMenuConvercationItem from "./ThreadMenuConversationItem.svelte";
import { ChatModel, type StoredState } from "../../models/chat-model";
import { SpecialContextInputModel } from "../../models/context-model";
import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import { HostClientType } from "$common-webviews/src/common/hosts/host-types";

describe("ThreadsMenuConvercationItemTestKit.svelte", async () => {
  let testKit: ThreadsMenuConvercationItemTestKit;

  beforeEach(() => {
    testKit = new ThreadsMenuConvercationItemTestKit();
  });

  test("should render", async () => {
    const conversation = testKit.chat.conversations["1"];
    const component = render(ThreadsMenuConvercationItem, {
      chatModel: testKit.chat,
      conversation: conversation,
      onConversationClicked: () => {},
      deleteConversation: () => {},
    });

    const conversationName = component.getByText(conversation.name || "");
    expect(conversationName).toBeInTheDocument();

    const optionsBtn = component.getByTitle("Conversation Options");
    expect(optionsBtn).toBeInTheDocument();

    let pinBtn = component.queryByTitle("Pin Conversation");
    expect(pinBtn).toBeNull();
    let deleteBtn = component.queryByTitle("Delete Conversation");
    expect(deleteBtn).toBeNull();
    let shareBtn = component.queryByTitle("Copy link to session");
    expect(shareBtn).toBeNull();

    optionsBtn.click();

    pinBtn = await component.findByTitle("Pin session");
    expect(pinBtn).toBeInTheDocument();
    deleteBtn = await component.findByTitle("Delete session");
    expect(deleteBtn).toBeInTheDocument();

    // Conversation is not shareable so this should be null
    shareBtn = component.queryByTitle("Copy link to session");
    expect(shareBtn).toBeNull();
  });

  describe("deleteConversation", async () => {
    test("should call deleteConversation when delete button is clicked", async () => {
      const conversation = testKit.chat.conversations["1"];
      const deleteSpy = vi.fn();
      const component = render(ThreadsMenuConvercationItem, {
        chatModel: testKit.chat,
        conversation: conversation,
        onConversationClicked: () => {},
        deleteConversation: deleteSpy,
      });

      const conversationName = component.getByText(conversation.name || "");
      expect(conversationName).toBeInTheDocument();

      const optionsBtn = component.getByTitle("Conversation Options");
      expect(optionsBtn).toBeInTheDocument();

      let deleteBtn = component.queryByTitle("Delete Conversation");
      expect(deleteBtn).toBeNull();

      optionsBtn.click();

      deleteBtn = await component.findByTitle("Delete session");
      expect(deleteBtn).toBeInTheDocument();
      await deleteBtn.click();

      expect(deleteSpy).toHaveBeenCalledWith(conversation.id);
    });
  });
});

class ThreadsMenuConvercationItemTestKit {
  private _chatModel: ChatModel;
  private _contextModel: SpecialContextInputModel;

  constructor() {
    const now = new Date();
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(now.getDate() - 3);
    const conversations = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      "1": {
        id: "1",
        name: "Conversation 1",
        createdAtIso: now.toISOString(),
        lastInteractedAtIso: now.toISOString(),
        chatHistory: [],
        feedbackStates: {},
        requestIds: [],
      },
    };
    const chatModelState: StoredState = {
      currentConversationId: undefined,
      conversations,
    };
    const host = {
      clientType: HostClientType.vscode,
      postMessage: vi.fn(),
      getState: vi.fn().mockReturnValue(chatModelState),
      setState: vi.fn(),
    };
    this._contextModel = new SpecialContextInputModel();
    this._chatModel = new ChatModel(new MessageBroker(host), host, this._contextModel);
  }

  get chat(): ChatModel {
    return this._chatModel;
  }
}

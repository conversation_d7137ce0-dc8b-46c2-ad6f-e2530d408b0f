<script lang="ts" context="module">
  export const CONVERSATIONS_OPTIONS_BUTTON_TITLE = "Conversation Options";
  export const PIN_THREAD_BUTTON_TITLE = "Pin session";
  export const DELETE_THREAD_BUTTON_TITLE = "Delete session";
</script>

<script lang="ts">
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import type { ChatModel } from "../../models/chat-model";
  import { ConversationModel } from "../../models/conversation-model";
  import { isAgentConversation, type IConversation } from "../../models/types";
  import ThreadMenuItemEditableText from "./ThreadMenuItemEditableText.svelte";
  import LayoutThreadMenuItem from "./LayoutThreadMenuItem.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import Check from "$common-webviews/src/design-system/icons/check.svelte";
  import Cross from "$common-webviews/src/design-system/icons/cross-2.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import DotsHorizontal from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import PencilIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pen-to-square.svg?component";
  import SewingPin from "$common-webviews/src/design-system/icons/sewing-pin.svelte";
  import SewingPinFilled from "$common-webviews/src/design-system/icons/sewing-pin-filled.svelte";
  import Trash from "$common-webviews/src/design-system/icons/trash.svelte";
  import Share from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/share-nodes.svg?component";
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";

  export let conversation: IConversation;
  export let chatModel: ChatModel;

  export let onConversationClicked: (conversationId: string) => void;
  export let deleteConversation: (conversationId: string) => void;

  $: enableShareService = $chatModel.flags.enableShareService;

  export let editableTitle: ThreadMenuItemEditableText | undefined = undefined;
  export let isEditingTitle: boolean = false;

  let active: boolean = false;
  let showCopiedMessage: "retrieving" | "copied" | "failure" | undefined = undefined;

  async function shareConversation(conversationId: string | undefined) {
    if (conversationId === undefined) {
      return;
    }

    if (showCopiedMessage !== undefined) {
      // A share request is in progress
      return;
    }

    showCopiedMessage = conversation.lastUrl ? "copied" : "retrieving";
    try {
      const url = await chatModel.getConversationUrl(conversationId);
      navigator.clipboard.writeText(url);
      showCopiedMessage = "copied";
    } catch (err) {
      console.error("Failed to get conversation URL: ", err);
      showCopiedMessage = "failure";
    } finally {
      setTimeout(() => {
        active = false;
        showCopiedMessage = undefined;
      }, 1200);
    }
  }

  $: selected = conversation.id === chatModel.currentConversationId && !isEditingTitle;
</script>

<LayoutThreadMenuItem
  {selected}
  isSelectable={true}
  on:click={() => onConversationClicked(conversation.id)}
  on:keydown={onKey("Enter", () => onConversationClicked(conversation.id))}
>
  <span class="l-thread-menu-item__title" data-testid="thread-menu-item-title" slot="title">
    <ThreadMenuItemEditableText
      bind:this={editableTitle}
      bind:isEditing={isEditingTitle}
      value={ConversationModel.getDisplayName(conversation)}
      placeholder="Conversation name"
      onAcceptEdit={(value) => {
        chatModel.renameConversation(conversation.id, value);
      }}
      onStartEdit={() => {}}
      onCancelEdit={() => {}}
    />
    {#if isAgentConversation(conversation)}
      {#if selected}
        <BadgeAugment.Root color="accent" size={1} variant="solid" highContrast
          >Agent</BadgeAugment.Root
        >
      {:else}
        <BadgeAugment.Root color="accent" size={1} variant="soft">Agent</BadgeAugment.Root>
      {/if}
    {/if}
  </span>

  <div slot="right" class="l-thread-menu-item__right">
    {#if isEditingTitle}
      <IconButtonAugment
        size={1}
        variant="soft"
        color="success"
        on:click={editableTitle.acceptEdit}
        title="Save"
      >
        <Check />
      </IconButtonAugment>
      <IconButtonAugment
        size={1}
        variant="soft"
        color="neutral"
        on:click={editableTitle.cancelEdit}
        title="Cancel"
      >
        <Cross />
      </IconButtonAugment>
    {:else}
      <DropdownMenuAugment.Root open={active}>
        <DropdownMenuAugment.Trigger>
          <div class="l-thread-menu-item__options">
            <IconButtonAugment
              variant="ghost-block"
              color="neutral"
              size={1}
              title={CONVERSATIONS_OPTIONS_BUTTON_TITLE}
              on:click={() => {
                active = !active;
              }}
            >
              <DotsHorizontal />
            </IconButtonAugment>
          </div>
        </DropdownMenuAugment.Trigger>

        <DropdownMenuAugment.Content
          size={1}
          side="bottom"
          align="end"
          onClickOutside={() => (active = false)}
          onEscapeKeyDown={() => (active = false)}
        >
          <DropdownMenuAugment.Label>Options</DropdownMenuAugment.Label>
          {#if enableShareService && conversation.isShareable}
            <DropdownMenuAugment.Item
              class="c-thread-menu__share-link"
              onSelect={() => shareConversation(conversation.id)}
            >
              <svelte:fragment slot="iconLeft">
                {#if showCopiedMessage === "retrieving"}
                  <SpinnerAugment size={1} />
                {:else if showCopiedMessage === "copied"}
                  <Check />
                {:else if showCopiedMessage === "failure"}
                  <Cross />
                {:else}
                  <Share />
                {/if}
              </svelte:fragment>

              {#if showCopiedMessage === "retrieving"}
                Creating link
              {:else if showCopiedMessage === "copied"}
                Link copied
              {:else if showCopiedMessage === "failure"}
                Unable to create link
              {:else}
                Share link to session
              {/if}
            </DropdownMenuAugment.Item>
          {/if}

          <DropdownMenuAugment.Item
            onSelect={(e) => {
              active = false;
              editableTitle?.startEdit(e);
            }}
          >
            <PencilIcon slot="iconLeft" />
            Rename
          </DropdownMenuAugment.Item>

          <DropdownMenuAugment.Item
            title={PIN_THREAD_BUTTON_TITLE}
            onSelect={() => {
              $chatModel.toggleConversationPinned(conversation.id);
              active = false;
            }}
          >
            <svelte:fragment slot="iconLeft">
              {#if conversation.isPinned}
                <SewingPin />
              {:else}
                <SewingPinFilled />
              {/if}
            </svelte:fragment>
            {conversation.isPinned ? "Unpin" : "Pin"}
          </DropdownMenuAugment.Item>

          <DropdownMenuAugment.Item
            title={DELETE_THREAD_BUTTON_TITLE}
            color="error"
            onSelect={async () => {
              deleteConversation(conversation.id);
              active = false;
            }}
          >
            <Trash slot="iconLeft" />
            Delete
          </DropdownMenuAugment.Item>
        </DropdownMenuAugment.Content>
      </DropdownMenuAugment.Root>
    {/if}
    <slot name="right" />
  </div>
</LayoutThreadMenuItem>

<style>
  .l-thread-menu-item__right :global(.l-tooltip-contents .c-card) {
    box-shadow: none;
  }

  /* Prevent share link for changing width of drop down as the content
   is changed. */
  .l-thread-menu-item__right :global(.c-thread-menu__share-link) {
    min-width: 150px;
  }

  /* Ensure the base btn color inherights from the items color. Important
    when the item is selected */
  .l-thread-menu-item__options :global(.c-base-btn) {
    color: inherit;
  }

  /* Make sure the title is all on one line */
  .l-thread-menu-item__title {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;

    overflow: hidden;
    white-space: nowrap;
  }
</style>

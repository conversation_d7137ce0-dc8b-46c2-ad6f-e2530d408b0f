<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let active: boolean = false;
  export let element: HTMLElement | undefined;
  export let title: string = "";

  let className: string = "";
  export { className as class };
</script>

<div
  bind:this={element}
  data-testid="threads-menu"
  class={`l-threads-menu ${className}`}
  class:l-threads-menu--active={active}
>
  <div class="l-threads-menu__wrapper">
    <div class="l-threads-menu__content">
      <!-- Header -->
      <div
        class="l-threads-menu__header"
        on:click|preventDefault|stopPropagation
        on:keydown
        role="button"
        tabindex="0"
        data-testid="threads-menu-header"
      >
        <div class="l-threads-menu__title" data-testid="threads-menu-header-title">
          <TextAugment size={1} weight="regular">
            {title}
          </TextAugment>
        </div>
        <div>
          <slot name="buttons" />
        </div>
      </div>

      <!-- Sort Options -->
      {#if active}
        <div class="l-threads-menu__sort-options">
          <slot name="sort-options" />
        </div>
      {/if}

      <!-- Body -->
      {#if active}
        <div class="l-threads-menu__body">
          <slot name="body" />
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  .l-threads-menu {
    /* Padding above/below the header */
    --l-threads-menu-vertical-padding: var(--ds-spacing-1);
    /* Icon Button height: --ds-spacing-5 */
    --l-threads-menu-header-height: var(--ds-spacing-5);

    position: relative;

    /* Reserve space for the header to appear as a block level element */
    padding-bottom: calc(
      (var(--l-threads-menu-vertical-padding) * 2) + var(--l-threads-menu-header-height)
    );
  }

  .l-threads-menu__wrapper {
    /* Allow position of head and body to float above content */
    position: absolute;
    width: 100%;
    padding: var(--l-threads-menu-vertical-padding);
    box-shadow: var(--ds-shadow-2);
  }

  .l-threads-menu__wrapper,
  .l-threads-menu__wrapper::before {
    border-radius: var(--ds-radius-3);
  }

  /* Apply blur to pseudo element so the stacking context doesn't
  impact body content (i.e. tooltips work as expected) */
  .l-threads-menu__wrapper::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;

    /* Backdrop must be applied with the color */
    backdrop-filter: blur(20px);
    background-color: var(--ds-panel-translucent);

    /* Ensure the backdrop is below the body */
    z-index: var(--z-threads-menu-backdrop);

    /* Prevent the corners from the blur showing beyond the border radius */
    overflow: hidden;
  }

  /* Position the header and body together */
  .l-threads-menu__content {
    /* Position the header and body relative to the wrapper */
    position: relative;

    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);

    /* Ensure the conversation menu is above the up scroll arrow */
    z-index: var(--z-threads-menu-body);
  }

  .l-threads-menu__header {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--ds-spacing-1);
    align-items: center;

    /* The height of the header should match the padding of the
    root element as the padding is used to reserve space for
    the header */
    height: var(--l-threads-menu-header-height);
  }

  .l-threads-menu__title {
    padding-left: var(--ds-spacing-2);
    color: var(--ds-color-accent-12);
    opacity: 0.5;

    /* Prevent long titles from overflowing */
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .l-threads-menu__sort-options {
    padding: 0 var(--ds-spacing-3);
  }

  .l-threads-menu__body {
    /* Allow the body to scroll for long lists of threads */
    max-height: 50vh;
    overflow-y: auto;

    /* Make scrollbar track transparent */
    scrollbar-color: var(--augment-scrollbar-color) transparent;
  }
</style>

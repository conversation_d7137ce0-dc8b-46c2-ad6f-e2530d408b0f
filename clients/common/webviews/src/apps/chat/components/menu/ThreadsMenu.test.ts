import { render, waitFor } from "@testing-library/svelte";
import { expect, describe, test, vi, beforeEach, afterEach, type Mock } from "vitest";

import ThreadsMenu, {
  COLLAPSE_THREADS_BUTTON_TITLE,
  ConversationAge,
  EXPAND_THREADS_BUTTON_TITLE,
  NEW_THREADS_BUTTON_TEST_ID,
} from "./ThreadsMenu.svelte";
import { ChatModel, type StoredState } from "../../models/chat-model";
import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import { SpecialContextInputModel } from "../../models/context-model";
import userEvent from "@testing-library/user-event";
import {
  CONVERSATIONS_OPTIONS_BUTTON_TITLE,
  PIN_THREAD_BUTTON_TITLE,
} from "./ThreadMenuConversationItem.svelte";
import { type IConversation } from "../../models/types";
import { DEFAULT_CONVERSATION_NAME } from "../../models/conversation-model";
import { HostClientType } from "$common-webviews/src/common/hosts/host-types";

describe("ThreadsMenu.svelte", async () => {
  let testKit: ThreadsMenuTestKit;
  let component: ReturnType<typeof render<ThreadsMenu>>;
  let onActiveChangedCallback: Mock;

  beforeEach(() => {
    onActiveChangedCallback = vi.fn();
    testKit = new ThreadsMenuTestKit();
    component = render(ThreadsMenu, {
      chatModel: testKit.chat,
      onActiveChanged: onActiveChangedCallback,
    });
  });

  afterEach(() => {
    component.unmount();
  });

  /**
   * Helper functions to wait for and retrieve ThreadsMenu UI elements.
   * Each function follows the pattern of waiting for a specific element to be present
   * in the DOM and then returning it. Available helpers:
   *
   * - waitForMenuContainer: Main menu container (data-testid="threads-menu")
   * - waitForMenuHeader: Menu header section (data-testid="threads-menu-header")
   * - waitForMenuBody: Menu body with conversation list (data-testid="threads-menu-body")
   * - waitForNewThreadButton: New conversation button (title="New Session")
   * - waitForShowThreadsButton: Menu expand button (title="Show Sessions")
   * - waitForCollapseThreadsButton: Menu collapse button (title="Collapse Sessions")
   *
   * All functions return Promise<HTMLElement> and throw a Testing Library error
   * if the element is not found within the default timeout.
   */
  const waitForMenuContainer = async (): Promise<HTMLElement> => {
    await waitFor(() => {
      const container = component.getByTestId("threads-menu");
      expect(container).toBeInTheDocument();
    });
    return component.getByTestId("threads-menu");
  };

  const waitForActiveMenu = async (): Promise<HTMLElement> => {
    const menu = await waitForMenuContainer();
    expect(menu).toHaveClass("l-threads-menu--active");
    await waitForMenuBody();
    await waitForCollapseThreadsButton();
    return menu;
  };

  const waitForInactiveMenu = async (): Promise<HTMLElement> => {
    const menu = await waitForMenuContainer();
    expect(menu).not.toHaveClass("l-threads-menu--active");
    await waitForShowThreadsButton();
    return menu;
  };

  const waitForMenuBody = async (): Promise<HTMLElement> => {
    await waitFor(() => {
      const body = component.getByTestId("threads-menu-body");
      expect(body).toBeInTheDocument();
    });
    return component.getByTestId("threads-menu-body");
  };

  const waitForNewThreadButton = async (): Promise<HTMLElement> => {
    await waitFor(() => {
      const newThreadBtn = component.getByTestId(NEW_THREADS_BUTTON_TEST_ID);
      expect(newThreadBtn).toBeInTheDocument();
    });
    return component.getByTestId(NEW_THREADS_BUTTON_TEST_ID);
  };

  const waitForShowThreadsButton = async (): Promise<HTMLElement> => {
    await waitFor(() => {
      const showThreadsBtn = component.getByTitle(EXPAND_THREADS_BUTTON_TITLE);
      expect(showThreadsBtn).toBeInTheDocument();
    });
    return component.getByTitle(EXPAND_THREADS_BUTTON_TITLE);
  };

  const waitForCollapseThreadsButton = async (): Promise<HTMLElement> => {
    await waitFor(() => {
      const collapseThreadsBtn = component.getByTitle(COLLAPSE_THREADS_BUTTON_TITLE);
      expect(collapseThreadsBtn).toBeInTheDocument();
    });
    return component.getByTitle(COLLAPSE_THREADS_BUTTON_TITLE);
  };

  const waitForConversationOptionsButtons = async (): Promise<HTMLElement[]> => {
    await waitFor(() => {
      const optionsBtns = component.getAllByTitle(CONVERSATIONS_OPTIONS_BUTTON_TITLE);
      optionsBtns.forEach((btn) => {
        expect(btn).toBeInTheDocument();
      });
    });

    return component.getAllByTitle(CONVERSATIONS_OPTIONS_BUTTON_TITLE);
  };

  const waitForMenuTitle = async (title: string): Promise<void> => {
    await waitFor(() => {
      const titleElement = component.getByTestId("threads-menu-header-title");
      expect(titleElement).toBeInTheDocument();
      expect(titleElement.textContent?.trim()).toEqual(title);
    });
  };

  /**
   * Shows the threads menu by clicking the expand button and waiting for the UI to update.
   * The function waits for:
   * 1. The menu header to appear
   * 2. The collapse button to replace the expand button
   * 3. The menu body to become visible
   * @throws Testing library error if any required elements are not found
   */
  const showThreads = async () => {
    const expandToggleBtn = await waitForShowThreadsButton();

    expandToggleBtn.click();

    // Wait for collapse button to appear
    await waitForActiveMenu();
  };

  /**
   * Hides the threads menu by clicking the collapse button and waiting for the UI to update.
   * The function waits for:
   * 1. The menu header to remain visible
   * 2. The expand button to replace the collapse button
   * @throws Testing library error if any required elements are not found
   */
  const hideThreads = async () => {
    const collapseToggleBtn = await waitForCollapseThreadsButton();
    collapseToggleBtn.click();

    // Wait for expand button to appear
    await waitForInactiveMenu();
  };

  test("should render with grouped conversations", async () => {
    await showThreads();
    await waitForActiveMenu();

    // Ensure the conversations are shown
    const titles = ["Conversation 1", "Conversation 2", "Conversation 3"];
    for (const t of titles) {
      const c = component.getByText(t);
      expect(c).toBeInTheDocument();
    }

    // Ensure the groups are shown
    const displayGroups = [ConversationAge.Today, ConversationAge.ThisWeek];
    for (const g of displayGroups) {
      const c = component.getByText(g);
      expect(c).toBeInTheDocument();
    }

    // Ensure other groups are not shown
    const otherGroups = [ConversationAge.Pinned, ConversationAge.ThisMonth, ConversationAge.Older];
    for (const g of otherGroups) {
      const c = component.queryByText(g);
      expect(c).not.toBeInTheDocument();
    }
  });

  test("closing menu should make menu not visible", async () => {
    expect.hasAssertions();
    await showThreads();
    await waitForActiveMenu();

    await hideThreads();
    await waitForInactiveMenu();
  });

  test("clicking header should toggle menu", async () => {
    const header = component.getByTestId("threads-menu-header");
    expect(header).toBeInTheDocument();

    header.click();
    await waitForActiveMenu();

    header.click();
    await waitForInactiveMenu();
  });

  test("clicking outside should close menu", async () => {
    // First show the menu
    await showThreads();
    const activeMenu = await waitForActiveMenu();
    expect(activeMenu).toBeInTheDocument();
    expect(activeMenu).toHaveClass("l-threads-menu--active");

    // Simulate clicking outside
    document.body.click();

    // Verify menu is now inactive
    const inactiveMenu = await waitForInactiveMenu();
    expect(inactiveMenu).not.toHaveClass("l-threads-menu--active");
    expect(onActiveChangedCallback).toHaveBeenCalledWith(false);
  });

  test("clicking on menu should not close menu", async () => {
    await showThreads();
    await waitForActiveMenu();

    const menuBody = await waitForMenuBody();
    expect(menuBody).toBeInTheDocument();

    menuBody.click();
    await waitForActiveMenu();
  });

  test("trigger event to close menu multiple times should only trigger one on active change event", async () => {
    expect(onActiveChangedCallback).not.toHaveBeenCalled();
    await showThreads();
    await waitForActiveMenu();
    expect(onActiveChangedCallback).toHaveBeenCalledTimes(1);
    expect(onActiveChangedCallback).lastCalledWith(true);

    await userEvent.keyboard("{Escape}");
    await waitForInactiveMenu();
    expect(onActiveChangedCallback).toHaveBeenCalledTimes(2);
    expect(onActiveChangedCallback).lastCalledWith(false);

    await userEvent.keyboard("{Escape}");
    expect(onActiveChangedCallback).toHaveBeenCalledTimes(2);
  });

  test("pressing esc should close menu", async () => {
    expect.hasAssertions();
    await showThreads();
    await waitForActiveMenu();

    await userEvent.keyboard("{Escape}");
    await waitForInactiveMenu();
  });

  test("pressing new thread button should create new conversation", async () => {
    // Mock the decidePersonaType method to return a synchronous result
    vi.spyOn(testKit.chat.currentConversationModel, "decidePersonaType").mockResolvedValue(0); // PersonaType.DEFAULT

    // Make sure we aren't on a "new" conversation
    testKit.chat.setCurrentConversation(testKit.conversations["1"].id);
    expect(testKit.chat.currentConversationId).toBe(testKit.conversations["1"].id);
    await waitForMenuTitle(testKit.conversations["1"].name as string);

    await showThreads();
    await waitForActiveMenu();

    // Store the current ID before creating a new conversation
    const oldConversationId = testKit.chat.currentConversationId;

    const newThreadBtn = await waitForNewThreadButton();
    await userEvent.click(newThreadBtn);

    // Wait for the async operations to complete
    await waitForInactiveMenu();

    // Wait for the conversation ID to change
    await waitFor(() => {
      expect(testKit.chat.currentConversationId).not.toBe(oldConversationId);
    });

    await waitForMenuTitle(DEFAULT_CONVERSATION_NAME);
  });

  test("pressing options should open options sub-dropdown", async () => {
    await showThreads();
    await waitForActiveMenu();

    const optionsBtns = await waitForConversationOptionsButtons();
    expect(optionsBtns[0]).toBeInTheDocument();

    await userEvent.click(optionsBtns[0]);
    await waitForActiveMenu();
    await waitFor(() => expect(component.getByTitle(PIN_THREAD_BUTTON_TITLE)).toBeInTheDocument());
  });
});

class ThreadsMenuTestKit {
  private _chatModel: ChatModel;
  private _contextModel: SpecialContextInputModel;
  public readonly conversations: { [id: string]: IConversation };

  constructor() {
    const now = new Date();
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(now.getDate() - 3);
    this.conversations = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      "1": {
        id: "1",
        name: "Conversation 1",
        createdAtIso: now.toISOString(),
        lastInteractedAtIso: now.toISOString(),
        chatHistory: [],
        feedbackStates: {},
        requestIds: [],
      },
      // eslint-disable-next-line @typescript-eslint/naming-convention
      "2": {
        id: "2",
        name: "Conversation 2",
        createdAtIso: threeDaysAgo.toISOString(),
        lastInteractedAtIso: threeDaysAgo.toISOString(),
        chatHistory: [],
        feedbackStates: {},
        requestIds: [],
      },
      // eslint-disable-next-line @typescript-eslint/naming-convention
      "3": {
        id: "3",
        name: "Conversation 3",
        createdAtIso: now.toISOString(),
        lastInteractedAtIso: now.toISOString(),
        chatHistory: [],
        feedbackStates: {},
        draftExchange: undefined,
        requestIds: [],
      },
    };
    const chatModelState: StoredState = {
      currentConversationId: undefined,
      conversations: this.conversations,
    };
    const host = {
      clientType: HostClientType.vscode,
      postMessage: vi.fn(),
      getState: vi.fn().mockReturnValue(chatModelState),
      setState: vi.fn(),
    };
    this._contextModel = new SpecialContextInputModel();
    this._chatModel = new ChatModel(new MessageBroker(host), host, this._contextModel);
  }

  get chat(): ChatModel {
    return this._chatModel;
  }
}

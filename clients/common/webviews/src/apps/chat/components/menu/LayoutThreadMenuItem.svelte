<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let selected: boolean = false;
  export let isSelectable: boolean = false;
</script>

<div
  class="l-thread-menu-item"
  class:l-thread-menu-item--selected={selected && isSelectable}
  class:l-thread-menu-item--selectable={isSelectable}
  data-testid="thread-menu-item"
  on:click
  on:mousedown
  on:keydown
  role="button"
  tabindex="0"
>
  <!-- Title -->
  <TextAugment size={1} weight="regular">
    <div class="l-thread-menu-item__title">
      <slot name="title" />
    </div>
  </TextAugment>

  <!-- Right section -->
  <div class="l-thread-menu-item__right">
    <slot name="right" />
  </div>
</div>

<style>
  .l-thread-menu-item {
    display: grid;
    grid-template-columns: minmax(0, 1fr) auto;
    align-items: center;
    gap: var(--ds-spacing-2);
    padding: var(--ds-spacing-1) var(--ds-spacing-3);
    grid-template-areas: "title right";
    border-radius: var(--ds-radius-2);
    color: var(--ds-color-neutral-a11);
  }

  .l-thread-menu-item--selectable.l-thread-menu-item {
    cursor: pointer;
  }

  .l-thread-menu-item__title {
    grid-area: title;
  }

  .l-thread-menu-item--selectable {
    color: var(--ds-text);
  }

  .l-thread-menu-item__right {
    grid-area: right;
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-1);
  }

  .l-thread-menu-item--selectable.l-thread-menu-item:hover {
    background-color: var(--ds-color-neutral-a3);
  }

  .l-thread-menu-item--selectable.l-thread-menu-item--selected {
    background-color: var(--ds-color-accent-9);
    color: var(--ds-accent-contrast);
  }

  .l-thread-menu-item--selectable.l-thread-menu-item--selected.l-thread-menu-item:hover {
    background-color: var(--ds-color-accent-10);
  }
</style>

<script lang="ts" context="module">
  export const THREADS_MENU_CLASS = "js-threads-menu";
  export const NEW_THREADS_BUTTON_TEST_ID = "new-thread-button";
  export const EXPAND_THREADS_BUTTON_TITLE = "Show Sessions";
  export const COLLAPSE_THREADS_BUTTON_TITLE = "Collapse Sessions";
  /**
   * Enum representing different age categories for conversations.
   */
  export enum ConversationAge {
    /* eslint-disable @typescript-eslint/naming-convention */
    Pinned = "Pinned",
    Active = "Active remote agents",
    Today = "Today",
    ThisWeek = "Last 7 days",
    ThisMonth = "Last 30 days",
    Older = "Older",
    /* eslint-enable @typescript-eslint/naming-convention */
  }
</script>

<script lang="ts">
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import { type ChatModel } from "../../models/chat-model";
  import { ConversationModel } from "../../models/conversation-model";
  import { isAgentConversation, type IConversation } from "../../models/types";
  import LayoutThreadsMenu from "./LayoutThreadsMenu.svelte";
  import ThreadMenuGroup from "./ThreadMenuGroup.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import Plus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";
  import Share from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/share-nodes.svg?component";
  import ChevronDown from "$common-webviews/src/design-system/icons/chevron-down.svelte";
  import SeparatorAugment from "$common-webviews/src/design-system/components/SeparatorAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import KeyboardShortcutHint from "$common-webviews/src/common/components/KeyboardShortcutHint.svelte";
  import { PlatformKeyIcon } from "$common-webviews/src/common/components/key-icons";
  import type DropdownMenuRoot from "$common-webviews/src/design-system/components/DropdownMenuAugment/Root.svelte";

  export let chatModel: ChatModel;
  export let onActiveChanged: (a: boolean) => void = () => {};

  // If the active prop is set, it'll control the active state
  // and the menu will ignore its internal events
  export let active: boolean | undefined = undefined;

  let _internalActive: boolean = active || false;

  // Sort options for conversations
  const sortOptions = [
    { label: "Last Message", value: "lastMessageTimestamp" },
    { label: "Created Date", value: "createdAt" },
  ] as const;
  // Get the sort preference from the writable store

  $: sortConversationsBy = $chatModel.sortConversationsBy;
  $: conversationModel = $chatModel.currentConversationModel;
  $: isConversationShareable = $conversationModel.id
    ? chatModel.isConversationShareable($conversationModel.id)
    : false;

  /**
   * Group the ordered conversations by date or pin conversations to the top:
   * - Pinned
   * - Today
   * - This week
   * - This month
   * - Older
   * */
  const displayOrder: ConversationAge[] = [
    ConversationAge.Pinned,
    ConversationAge.Active,
    ConversationAge.Today,
    ConversationAge.ThisWeek,
    ConversationAge.ThisMonth,
    ConversationAge.Older,
  ];

  /**
   * Map to store conversations grouped by their age category.
   */
  let conversationsToDisplay: Array<{
    groupTitle: ConversationAge;
    conversations: IConversation[];
  }> = [];

  /**
   * Updates the conversation groups based on the provided conversations.
   * This function categorizes conversations into age groups and updates the global conversationGroups map.
   *
   * @param conversations - An array of IConversation objects to be grouped.
   */
  const updateConversationGroups = (conversations: IConversation[]) => {
    const newGroups: Map<ConversationAge, IConversation[]> = new Map();
    const now = new Date();
    const today = new Date(now.setHours(0, 0, 0, 0));
    const timeRanges = [
      { age: ConversationAge.Today, date: today },
      { age: ConversationAge.ThisWeek, date: new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000) },
      {
        age: ConversationAge.ThisMonth,
        date: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000),
      },
    ];

    for (const conversation of conversations) {
      const date = ConversationModel.getTime(conversation, $sortConversationsBy);
      let age = timeRanges.find((range) => date >= range.date)?.age || ConversationAge.Older;
      if (conversation.isPinned) {
        age = ConversationAge.Pinned;
      }

      newGroups.set(age, [...(newGroups.get(age) || []), conversation]);
    }

    conversationsToDisplay = displayOrder
      .map((age) => ({
        groupTitle: age,
        conversations: newGroups.get(age) || [],
      }))
      .filter((group) => group.conversations.length > 0);
  };
  $: updateConversationGroups(
    $chatModel.orderedConversations($sortConversationsBy).filter((c) => {
      return chatModel.flags.enableAgentMode ? true : !isAgentConversation(c);
    }),
  );

  function setActive(newActive: boolean) {
    if (active !== undefined) {
      // External active defined and takes control of the state
      // changes.
      return;
    }

    if (_internalActive === newActive) {
      return;
    }
    _internalActive = newActive;
    onActiveChanged(newActive);
  }

  function toggleMenu(e: MouseEvent | KeyboardEvent) {
    setActive(!_internalActive);
    e.stopPropagation();
    e.preventDefault();
  }

  function closeMenu() {
    setActive(false);
  }

  function createConversation() {
    chatModel.setCurrentConversation();
    closeMenu();
  }

  function setConversation(conversationId: string) {
    chatModel.setCurrentConversation(conversationId);
    closeMenu();
  }

  let disableBlurClose = false;
  async function deleteConversation(conversationId: string) {
    try {
      disableBlurClose = true;
      await $chatModel.deleteConversation(conversationId);
    } finally {
      disableBlurClose = false;
    }
  }

  function onMenuBlur() {
    if (disableBlurClose) {
      // We are doing something that causes the webview/chat to lose focus
      // but we don't want to close the menu.
      return;
    }

    closeMenu();
  }

  let showCopiedMessage: "retrieving" | "copied" | "failure" | undefined = undefined;
  let copyLinkError: string = "";
  let tooltip: TextTooltipAugment | undefined = undefined;
  let hideTooltipTimeoutId: ReturnType<typeof setTimeout> | undefined;

  async function shareConversation(conversationId: string | undefined) {
    if (conversationId === undefined) {
      return;
    }

    if (tooltip) {
      clearTimeout(hideTooltipTimeoutId);
      tooltip.requestOpen();
    }

    showCopiedMessage = "retrieving";
    copyLinkError = "";
    try {
      const url = await chatModel.getConversationUrl(conversationId);
      navigator.clipboard.writeText(url);
      showCopiedMessage = "copied";
    } catch (err) {
      const e = err as Error;
      showCopiedMessage = "failure";
      copyLinkError = e.message;
    } finally {
      setTimeout(() => {
        tooltip?.requestClose();
        // showCopiedMessage will be set to undefined by the tippy `onHidden`
        // callback
      }, 1200);
    }
  }

  let threadsMenuElement: HTMLElement | undefined;
  let sortDropdown: DropdownMenuRoot | undefined;
</script>

<svelte:window
  on:blur={onMenuBlur}
  on:keydown={onKey("Escape", closeMenu)}
  on:click|capture={(e) => {
    if (threadsMenuElement && e.composedPath().includes(threadsMenuElement)) {
      return;
    }
    closeMenu();
  }}
/>

<LayoutThreadsMenu
  bind:element={threadsMenuElement}
  active={_internalActive}
  class={THREADS_MENU_CLASS}
  title={ConversationModel.getDisplayName(conversationModel)}
  on:click={toggleMenu}
  on:keydown={onKey("Enter", toggleMenu)}
>
  <!-- Header buttons -->
  <div class="c-threads-menu__right-btns" slot="buttons">
    <!-- Share link to session -->
    {#if $chatModel.flags.enableShareService && isConversationShareable}
      <TextTooltipAugment bind:this={tooltip} triggerOn={[TooltipTriggerOn.Click]}>
        <IconButtonAugment
          variant="ghost-block"
          color="neutral"
          size={1}
          title="Share session"
          on:click={(e) => {
            shareConversation(conversationModel.id);
            e.stopPropagation();
          }}
        >
          <Share />
        </IconButtonAugment>

        <div slot="content">
          {#if showCopiedMessage === "retrieving"}
            <div>
              <SpinnerAugment size={1} useCurrentColor={true} /> Copying link...
            </div>
          {:else if showCopiedMessage === "copied"}
            Copied link to clipboard!
          {:else if showCopiedMessage === "failure"}
            {copyLinkError}
          {:else}
            Copy link to thread
          {/if}
        </div>
      </TextTooltipAugment>
    {/if}

    <!-- Expand/Collapse menu button -->
    <IconButtonAugment
      variant="ghost-block"
      color="neutral"
      size={1}
      title={_internalActive ? COLLAPSE_THREADS_BUTTON_TITLE : EXPAND_THREADS_BUTTON_TITLE}
      on:click={toggleMenu}
      on:keydown={onKey("Enter", toggleMenu)}
    >
      <ChevronDown />
    </IconButtonAugment>

    <!-- New thread Button -->
    <TextTooltipAugment>
      <div slot="content" class="c-threads-menu__new-thread-tooltip-content">
        <KeyboardShortcutHint icons={[`${PlatformKeyIcon.cmdOrCtrl}`, "L"]} />
      </div>
      <IconButtonAugment
        variant="solid"
        color="neutral"
        size={1}
        on:click={(e) => {
          createConversation();
          e.stopPropagation();
        }}
        data-testid={NEW_THREADS_BUTTON_TEST_ID}
      >
        <Plus />
      </IconButtonAugment>
    </TextTooltipAugment>
  </div>

  <!-- Sort options -->
  <div slot="sort-options" class="c-threads-menu__sort-options">
    <div class="c-threads-menu__sort-label">
      <TextAugment size={1} weight="medium">Sort by:</TextAugment>
    </div>
    <DropdownMenuAugment.Root bind:this={sortDropdown}>
      <DropdownMenuAugment.Trigger>
        <div class="c-threads-menu__sort-trigger">
          <TextAugment size={1}>
            {$sortConversationsBy === "lastMessageTimestamp" ? "Last Message" : "Created Date"}
          </TextAugment>
          <ChevronDown />
        </div>
      </DropdownMenuAugment.Trigger>
      <DropdownMenuAugment.Content size={1} side="bottom" align="start">
        {#each sortOptions as { label, value }}
          <DropdownMenuAugment.Item
            onSelect={() => {
              chatModel.setSortConversationsBy(value);
              sortDropdown?.requestClose();
            }}
            highlight={$sortConversationsBy === value}
          >
            {label}
          </DropdownMenuAugment.Item>
        {/each}
      </DropdownMenuAugment.Content>
    </DropdownMenuAugment.Root>
  </div>

  <!-- Body of the Threads Menu -->
  <div slot="body" data-testid="threads-menu-body">
    {#each conversationsToDisplay as group, i}
      <ThreadMenuGroup
        groupTitle={group.groupTitle}
        conversations={group.conversations}
        onConversationClicked={setConversation}
        {deleteConversation}
        chatModel={$chatModel}
      />
      {#if i < conversationsToDisplay.length - 1}
        <div class="c-threads-menu__separator">
          <SeparatorAugment size={4} />
        </div>
      {/if}
    {/each}
  </div>
</LayoutThreadsMenu>

<style>
  .c-threads-menu__separator {
    margin: var(--ds-spacing-2) var(--ds-spacing-3);
  }

  .c-threads-menu__right-btns {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-1);
  }

  .c-threads-menu__sort-options {
    width: 100%;
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-threads-menu__sort-label {
    white-space: nowrap;
  }

  .c-threads-menu__sort-trigger {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
    border-radius: var(--ds-radius-1);
    background-color: var(--ds-color-neutral-a2);
    cursor: pointer;
  }

  .c-threads-menu__new-thread-tooltip-content :global(.c-keyboard-shortcut-hint__icon) {
    color: var(--gray-1);
    border-color: var(--gray-1);
  }
</style>

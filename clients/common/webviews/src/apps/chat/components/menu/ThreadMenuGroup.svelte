<script lang="ts">
  import type { IConversation } from "../../models/types";
  import LayoutThreadMenuItem from "./LayoutThreadMenuItem.svelte";
  import ThreadMenuConversationItem from "./ThreadMenuConversationItem.svelte";
  import type { ChatModel } from "../../models/chat-model";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import RegularEllipsisIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import Trash from "$common-webviews/src/design-system/icons/trash.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  export let groupTitle: string;
  export let conversations: IConversation[];
  export let onConversationClicked: (conversationId: string) => void;
  export let deleteConversation: (conversationId: string) => void;
  export let chatModel: ChatModel;

  async function deleteAllConversations() {
    await chatModel.deleteConversations(conversations.map((c) => c.id));
  }
  let dropdownActive = false;
</script>

{#if conversations.length > 0}
  <div class="l-thread-group">
    <!-- Group Heading -->
    <div class="l-thread-group__header">
      <LayoutThreadMenuItem>
        <span slot="title">{groupTitle}</span>
        <div slot="right" class="l-thread-group__options">
          <DropdownMenuAugment.Root
            open={dropdownActive}
            triggerOn={[TooltipTriggerOn.Hover, TooltipTriggerOn.Click]}
            onHoverStart={() => {}}
            onHoverEnd={() => (dropdownActive = false)}
            onOpenChange={(open) => {
              // Blur the dropdown when we lose focus if focus is in the dropdown
              if (!open) {
                const isFocusInDropdown = document.activeElement?.closest(
                  ".l-thread-group__options",
                );
                if (isFocusInDropdown && document.activeElement instanceof HTMLElement) {
                  document.activeElement.blur();
                }
              }
            }}
          >
            <DropdownMenuAugment.Trigger>
              <IconButtonAugment
                variant="ghost-block"
                color="neutral"
                size={1}
                title="Group options"
                on:click={() => (dropdownActive = !dropdownActive)}
              >
                <RegularEllipsisIcon />
              </IconButtonAugment>
            </DropdownMenuAugment.Trigger>
            <DropdownMenuAugment.Content size={1} side="bottom" align="end">
              <DropdownMenuAugment.Item color="error" onSelect={deleteAllConversations}>
                <Trash slot="iconLeft" />
                Delete {conversations.length} thread{conversations.length > 1 ? "s" : ""}
              </DropdownMenuAugment.Item>
            </DropdownMenuAugment.Content>
          </DropdownMenuAugment.Root>
        </div>
      </LayoutThreadMenuItem>
    </div>

    <!-- Group Body -->
    <div>
      {#each conversations as c}
        <ThreadMenuConversationItem
          conversation={c}
          {chatModel}
          {onConversationClicked}
          {deleteConversation}
        />
      {/each}
    </div>
  </div>
{/if}

<style>
  .l-thread-group__header :global(.l-thread-group__options) {
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .l-thread-group__header:hover :global(.l-thread-group__options),
  .l-thread-group__header:focus-within :global(.l-thread-group__options) {
    opacity: 1;
  }
</style>

<script lang="ts">
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import { THREADS_MENU_CLASS } from "./ThreadsMenu.svelte";

  export let isEditing: boolean = false;
  export let value: string;
  export let cancelOnClickOutside: boolean = true;
  export let placeholder: string = "";
  export let disabled: boolean = false;

  export let onStartEdit: () => void;
  export let onAcceptEdit: (newValue: string) => void;
  export let onCancelEdit: () => void;

  // Editing lifecycle is below. A user can
  // - Start editing
  // - While editing, either accept or reject
  let draftValue: string = value;
  let textInput: HTMLInputElement | undefined;

  // Focus the text area whenever it is mounted
  $: textInput?.focus();
  export function startEdit(e?: Event) {
    if (isEditing || disabled) {
      return;
    }
    draftValue = value;
    isEditing = true;
    onStartEdit();
    e?.stopPropagation();
  }
  export function acceptEdit(e?: MouseEvent | KeyboardEvent) {
    if (!isEditing) {
      return;
    }
    onAcceptEdit(draftValue);
    isEditing = false;
    draftValue = "";
    e?.stopPropagation();
  }
  export function cancelEdit(e?: MouseEvent | KeyboardEvent) {
    if (!isEditing || !cancelOnClickOutside) {
      return;
    }
    onCancelEdit();
    isEditing = false;
    draftValue = "";
    e?.stopPropagation();
  }

  // Key handlers
  function onKeyDown(e: KeyboardEvent) {
    if (!isEditing) {
      return;
    }

    if (e.key === "Enter") {
      acceptEdit(e);
    } else if (e.key === "Escape") {
      cancelEdit(e);
    }
  }

  function onBodyClick(e: MouseEvent) {
    const target = e.target as HTMLElement | null;
    if (target) {
      const threadMenu = (target as HTMLElement).closest(`.${THREADS_MENU_CLASS}`);
      if (threadMenu) {
        // If the users clicks are inside the thread menu, don't cancel the edit
        // The intent of this is to cancel the edit when the user clicks
        // outside the thread menu / dropdown (i.e. on the chat window)
        return;
      }
    }
    cancelEdit(e);
  }
</script>

<svelte:body on:click={onBodyClick} />

<!-- EditableTextSpan component -->
{#if !disabled && isEditing}
  <TextFieldAugment
    bind:textInput
    size={1}
    variant="surface"
    {placeholder}
    bind:value={draftValue}
    on:click={(e) => e.stopPropagation()}
    on:keydown={onKeyDown}
  />
{:else}
  <div
    class="c-editable-text__text"
    class:c-editable-text--disabled={disabled}
    on:dblclick={startEdit}
    role="button"
    tabindex="0"
  >
    {value}
  </div>
{/if}

<style>
  .c-editable-text__text {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  .c-editable-text--disabled {
    user-select: none;
  }
</style>

<script lang="ts">
  import PinButton from "$common-webviews/src/apps/chat/components/PinButton.svelte";
  import { HoverContext, onHover } from "$common-webviews/src/common/actions/onHoverAction";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ContextIcon from "$common-webviews/src/apps/chat/components/context/ContextIcon.svelte";
  import type { ChatModel } from "../../models/chat-model";
  import type { IContextInfo } from "../../models/types";
  import {
    isItemAgentMemories,
    isItemExternalSource,
    isItemFile,
    isItemFolder,
    isItemRecentFile,
    isItemRule,
    isItemSelection,
    isItemSourceFolder,
    isItemSourceFolderGroup,
    isItemUserGuidelines,
    type IChatMentionable,
  } from "../../types/mention-option";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import ChatMentionHoverContents from "../mentions/ChatMentionHoverContents.svelte";
  import ContextBadge from "./ContextBadge.svelte";
  import MultiRepoChip from "./MultiRepoChip.svelte";
  import SelectionChip from "./SelectionChip.svelte";
  import GuidelinesChip from "./GuidelinesChip.svelte";
  import MemoriesChip from "./MemoriesChip.svelte";
  import { AgentSessionEventName } from "@augment-internal/sidecar-libs/src/metrics/types";
  import {
    AUGMENT_DIRECTORY_ROOT,
    AUGMENT_RULES_FOLDER,
  } from "@augment-internal/sidecar-libs/src/utils/rules-parser";

  export let chatModel: ChatModel | undefined;
  export let item: IChatMentionable & IContextInfo;

  let isHovered = false;

  const hoverContext = new HoverContext({
    onHoverStart: () => {
      isHovered = true;
    },
    onHoverEnd: () => {
      isHovered = false;
    },
    hoverTriggerDuration: 0,
  });

  $: contextModel = $chatModel?.specialContextInputModel;

  const removeItem = () => {
    $contextModel?.markInactive(item);
    $contextModel?.unpin(item);
  };

  const getChipConfig = (item: IChatMentionable & IContextInfo) => {
    const baseConfig = {
      onClick: undefined as (() => void) | undefined,
      removeTitle: "",
      filepath: "",
      pinnable: true,
      iconName: undefined,
      showWarning: false,
    };

    if (isItemFile(item)) {
      return {
        ...baseConfig,
        onClick: () => {
          chatModel?.extensionClient.openFile(item.file);
        },
        removeTitle: "Remove file from context",
        filepath: item.file.pathName,
      };
    } else if (isItemRecentFile(item)) {
      return {
        ...baseConfig,
        onClick: () => {
          chatModel?.extensionClient.openFile(item.recentFile);
        },
        removeTitle: "Remove file from context",
        filepath: item.recentFile.pathName,
      };
    } else if (isItemFolder(item)) {
      return {
        ...baseConfig,
        onClick: () => {
          chatModel?.extensionClient.openFile(item.folder);
        },
        removeTitle: "Remove folder from context",
        filepath: item.folder.pathName,
        iconName: "folder-opened",
      };
    } else if (isItemExternalSource(item)) {
      return {
        ...baseConfig,
        removeTitle: "Remove external source from context",
        filepath: item.label,
        iconName: "book",
      };
    } else if (isItemSourceFolder(item)) {
      return {
        ...baseConfig,
        removeTitle: "Remove source folder from context",
        filepath: item.sourceFolder.folderRoot,
        iconName: "root-folder",
        pinnable: false,
        showWarning: item.showWarning,
      };
    } else if (isItemSourceFolderGroup(item)) {
      return {
        ...baseConfig,
        iconName: "root-folder",
        pinnable: false,
      };
    } else if (isItemRule(item)) {
      const rulePath = `${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/${item.rule.path}`;
      return {
        ...baseConfig,
        onClick: () => {
          // Open the rule file using the same approach as in settings
          chatModel?.extensionClient.openFile({
            repoRoot: "",
            pathName: rulePath,
          });
        },
        removeTitle: "Remove rule from context",
        filepath: rulePath,
        iconName: "rule",
      };
    } else if (isItemAgentMemories(item)) {
      return {
        ...baseConfig,
        onClick: () => {
          const memoriesPath = chatModel?.flags.agentMemoriesFilePathName;
          // Report the metric when memories file is opened
          if (chatModel?.currentConversationId) {
            chatModel.extensionClient.reportAgentSessionEvent({
              eventName: AgentSessionEventName.openedMemoriesFile,
              conversationId: chatModel.currentConversationId,
              eventData: {
                memoriesFileOpenData: {
                  memoriesPathUndefined: !memoriesPath,
                },
              },
            });
          }
          chatModel?.extensionClient.openMemoriesFile();
        },
        pinnable: false,
      };
    }

    return baseConfig;
  };
  $: chipConfig = getChipConfig(item);
  $: pinProps = {
    title: item.pinned ? "Unpin" : "Pin to context",
    onPin: () => $contextModel?.togglePinned(item),
    isPinned: item.pinned,
  };
  $: showPin = chipConfig.pinnable && (isHovered || item.pinned);
</script>

{#if item}
  {#if isItemSelection(item)}
    <SelectionChip originalCode={item.selection.originalCode} onRemove={removeItem} />
  {:else if isItemUserGuidelines(item)}
    <GuidelinesChip {item} />
  {:else if isItemSourceFolderGroup(item)}
    <MultiRepoChip {item} {chatModel} />
  {:else if isItemAgentMemories(item)}
    <MemoriesChip on:click={chipConfig.onClick} />
  {:else}
    <div use:onHover={hoverContext} class="pulse-chip-container">
      {#if showPin}
        <ContextBadge
          on:click={chipConfig.onClick}
          removeBtn={{ title: chipConfig.removeTitle, onRemove: removeItem }}
          showWarning={chipConfig.showWarning}
        >
          <PinButton {...pinProps} slot="leftButtons" />
          <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]}>
            <Filespan nopath filepath={chipConfig.filepath} />
            <ChatMentionHoverContents option={item} slot="content" />
          </TextTooltipAugment>
        </ContextBadge>
      {:else if chipConfig.iconName}
        <ContextBadge
          on:click={chipConfig.onClick}
          removeBtn={{ title: chipConfig.removeTitle, onRemove: removeItem }}
          showWarning={chipConfig.showWarning}
        >
          <ContextIcon icon={chipConfig.iconName} slot="leftIcon" />
          <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]}>
            <Filespan nopath filepath={chipConfig.filepath} />
            <ChatMentionHoverContents option={item} slot="content" />
          </TextTooltipAugment>
        </ContextBadge>
      {:else}
        <ContextBadge
          on:click={chipConfig.onClick}
          removeBtn={{ title: chipConfig.removeTitle, onRemove: removeItem }}
          showWarning={chipConfig.showWarning}
        >
          <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]}>
            <Filespan nopath filepath={chipConfig.filepath} />
            <ChatMentionHoverContents option={item} slot="content" />
          </TextTooltipAugment>
        </ContextBadge>
      {/if}
    </div>
  {/if}
{/if}

<style>
  .pulse-chip-container {
    display: inline-flex;
  }

  :global(.c-user-guidelines-warning-icon) {
    color: var(--yellow-7);
    width: 1rem;
    height: 1rem;
  }
</style>

<script lang="ts">
  import "$common-webviews/node_modules/@radix-ui/colors/gray-alpha.css";
  import "$common-webviews/node_modules/@radix-ui/colors/gray-dark-alpha.css";
  import { getFilename } from "$common-webviews/src/common/utils/file-paths";
  import { dsColorAttribute } from "$common-webviews/src/design-system/_libs/component-utils";
  import type {
    ButtonColor,
    ButtonSize,
    ButtonVariant,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import Cross2 from "$common-webviews/src/design-system/icons/cross-2.svelte";
  import DotsHorizontal from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import Gear from "$common-webviews/src/design-system/icons/gear.svelte";
  import { type ChatModel } from "../../models/chat-model";
  import type { IContextInfo } from "../../models/types";
  import type { IChatMentionable, IChatMentionableItem } from "../../types/mention-option";
  import ContextBadge from "./ContextBadge.svelte";

  export let item: IChatMentionableItem<"sourceFolderGroup"> & IContextInfo;
  export let chatModel: ChatModel | undefined;

  $: contextModel = $chatModel?.specialContextInputModel;

  function removeItem(item: IChatMentionable) {
    $contextModel?.markInactive(item);
    $contextModel?.unpin(item);
  }

  function editGuideline(filePath: string) {
    chatModel?.extensionClient.openGuidelines(filePath);
  }

  const iconAttributes = {
    size: 1 as ButtonSize,
    variant: "ghost-block" as ButtonVariant,
    color: "neutral" as ButtonColor,
  };

  $: menuItems = item.sourceFolderGroup.map((sourceItem) => ({
    filePath: sourceItem.sourceFolder.folderRoot,
    name: getFilename(sourceItem.sourceFolder.folderRoot),
    onRemove: () => removeItem(sourceItem),
    onEditGuideline:
      !!sourceItem.sourceFolder.guidelinesEnabled &&
      (() => editGuideline(sourceItem.sourceFolder.folderRoot)),
  }));
  // when one item has a guidelines button, we should add a spacer to align the remove button
  $: hasGuideliens = menuItems.some((item) => item.onEditGuideline);
</script>

<ContextBadge>
  <div {...dsColorAttribute("accent")} slot="leftIcon" class="c-multi-repo-chip__count">
    <TextAugment size={1}>
      {item.sourceFolderGroup.length}
    </TextAugment>
  </div>
  Repos
  <DropdownMenuAugment.Root slot="rightButtons">
    <DropdownMenuAugment.Trigger>
      <BadgeAugment.IconButton title="Open menu" variant="ghost" color="neutral" size={1}>
        <DotsHorizontal />
      </BadgeAugment.IconButton>
    </DropdownMenuAugment.Trigger>
    <DropdownMenuAugment.Content>
      <DropdownMenuAugment.Label>Repos</DropdownMenuAugment.Label>
      {#each menuItems as item}
        <DropdownMenuAugment.Item
          {...item}
          class="c-multi-repo-chip__dropdown-item"
          highlight={false}
          disabled
        >
          {item.name}
          <div
            class="c-multi-repo-chip__dropdown-item__actions"
            slot="iconRight"
            class:c-multi-repo-chip__dropdown-item__guideline-space={hasGuideliens &&
              !item.onEditGuideline}
          >
            {#if item.onEditGuideline}
              <TextTooltipAugment content="Edit repo instructions">
                <IconButtonAugment {...iconAttributes} on:click={item.onEditGuideline}>
                  <Gear />
                </IconButtonAugment>
              </TextTooltipAugment>
            {/if}
            <TextTooltipAugment content="Remove repo from context">
              <IconButtonAugment {...iconAttributes} on:click={item.onRemove}>
                <Cross2 />
              </IconButtonAugment>
            </TextTooltipAugment>
          </div>
        </DropdownMenuAugment.Item>
      {/each}
    </DropdownMenuAugment.Content>
  </DropdownMenuAugment.Root>
</ContextBadge>

<style>
  .c-multi-repo-chip__count {
    background-color: var(--ds-color-a8);
    padding-inline: 2px;
    border-radius: 2px;
    line-height: 1rem;
  }

  .c-multi-repo-chip__dropdown-item__actions {
    display: flex;
    flex-direction: row;

    &.c-multi-repo-chip__dropdown-item__guideline-space {
      padding-left: var(--ds-spacing-5);
    }
  }

  /* prettier-ignore */
  :global(button.c-base-btn.c-dropdown-menu-augment__item.c-multi-repo-chip__dropdown-item):disabled {
    color: var(--gray-a12);
    /** For this specific dropdown, items should not be interactable */
    background-color: unset;
    cursor: default;

    padding: 0px var(--ds-spacing-2);

    & .c-dropdown-menu-augment__item-icon {
      height: var(--ds-spacing-5);
      padding-left: var(--ds-spacing-2);
    }

    &:hover {
      /** When hovering, child buttons inherit the wrong color so we need to override it */
      --base-btn-bg-color: unset;
    };
  }
</style>

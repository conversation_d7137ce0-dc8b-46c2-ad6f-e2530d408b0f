<script lang="ts">
  import { getFilename } from "$common-webviews/src/common/utils/file-paths";
  import BadgeRoot from "$common-webviews/src/design-system/components/BadgeAugment/BadgeRoot.svelte";
  import type { ChatModel } from "../../models/chat-model";
  import { ContextStatus, isAgentConversation, type IContextInfo } from "../../models/types";
  import {
    type IChatMentionable,
    type IChatMentionableItem,
    isItemSourceFolder,
  } from "../../types/mention-option";
  import ContextItemBadge from "./ContextItemBadge.svelte";

  export let chatModel: ChatModel;

  $: contextModel = chatModel.specialContextInputModel;
  $: conversationModel = chatModel.currentConversationModel;

  // Render source folders at the end
  let allItems = [] as (IChatMentionable & IContextInfo)[];
  $: {
    const items = $contextModel.recentActiveItems;
    const sourceFolders = [] as IChatMentionableItem<"sourceFolder">[];
    const filteredItems = items.filter((item) => {
      if (isItemSourceFolder(item)) {
        sourceFolders.push(item);
        return false;
      }
      return true;
    });
    if (sourceFolders.length > 1) {
      // if this workspace has multiple repos, group them together into one chip
      filteredItems.push({
        id: "sourceFoldersGroup",
        label: "Repos",
        status: ContextStatus.active,
        sourceFolderGroup: sourceFolders.sort((a, b) => {
          const aName = getFilename(a.sourceFolder.folderRoot);
          const bName = getFilename(b.sourceFolder.folderRoot);
          return aName.localeCompare(bName);
        }),
        referenceCount: 1,
      });
      allItems = filteredItems.reverse();
    } else {
      allItems = items.reverse();
    }
  }
</script>

<div class="c-action-bar-context">
  <div class="c-action-bar-context-container">
    {#each allItems as item}
      <ContextItemBadge {item} {chatModel} />
    {/each}
  </div>

</div>

<style>
  .c-action-bar-context {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
    width: 100%;
    justify-content: space-between;
  }
  .c-action-bar-context :global(.c-badge[data-ds-color="premium"]) {
    border-radius: 25%/50%;
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
    align-self: flex-start;
  }
  .c-action-bar-context-container {
    user-select: none;
    height: fit-content;
    width: 100%;

    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--ds-spacing-1);
  }
</style>

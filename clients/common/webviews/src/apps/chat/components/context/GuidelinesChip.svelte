<script lang="ts">
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";
  import "@radix-ui/colors/yellow.css";
  import "@radix-ui/colors/yellow-dark.css";
  import type { IContextInfo } from "../../models/types";
  import type { IChatMentionableItem } from "../../types/mention-option";
  import ChatMentionHoverContents from "../mentions/ChatMentionHoverContents.svelte";
  import ContextBadge from "./ContextBadge.svelte";
  import { getChatModel } from "../../chat-context";
  import RegularLayerGroupIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/layer-group.svg?component";

  export let item: IChatMentionableItem<"userGuidelines"> & IContextInfo;

  const chatModel = getChatModel();

  $: isOverLimit = item.showWarning;
  $: isEnabled = item.userGuidelines?.enabled ?? false;
  $: enableRules = $chatModel?.flags.enableRules ?? false;
  $: displayText = enableRules ? "Rules & Guidelines" : "User Guidelines";

  function openSettingsPanel() {
    // Open the settings panel to access user guidelines
    if (chatModel) {
      // Pass 'guidelines' as the section to navigate to
      chatModel.extensionClient.openSettingsPage("guidelines");
    }
  }
</script>

{#if isEnabled}
  {#if isOverLimit}
    <TextTooltipAugment>
      <ContextBadge>
        <ExclamationTriangle class="c-user-guidelines-warning-icon" slot="leftIcon" />
        <div
          on:click|stopPropagation={openSettingsPanel}
          on:keyup={onKey("Enter", openSettingsPanel)}
          role="button"
          tabindex="0"
          style:cursor="pointer"
        >
          {displayText}
        </div>
      </ContextBadge>
      <ChatMentionHoverContents option={item} slot="content" />
    </TextTooltipAugment>
  {:else}
    <div
      on:click={openSettingsPanel}
      on:keyup={onKey("Enter", openSettingsPanel)}
      role="button"
      tabindex="0"
      style:cursor="pointer"
    >
      <ContextBadge>
        <RegularLayerGroupIcon slot="leftIcon" />
        {displayText}
      </ContextBadge>
    </div>
  {/if}
{/if}

<style>
  :global(.c-user-guidelines-warning-icon) {
    color: var(--yellow-7);
    width: 1rem;
    height: 1rem;
  }
</style>

<script lang="ts">
  import CursorText from "$common-webviews/src/design-system/icons/cursor-text.svelte";
  import SelectionHoverContents from "../mentions/SelectionHoverContents.svelte";
  import ContextBadge from "./ContextBadge.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";

  export let originalCode: string | undefined;
  export let onRemove: () => void;
</script>

<TextTooltipAugment maxWidth="">
  <!-- Trigger for hover tooltip -->
  <ContextBadge color="info" removeBtn={{ title: "Remove selection from context", onRemove }}>
    <CursorText slot="leftIcon" />
    Selection
    <slot />
  </ContextBadge>

  <!-- Content to show in tooltip -->
  <SelectionHoverContents slot="content" {originalCode} />
</TextTooltipAugment>

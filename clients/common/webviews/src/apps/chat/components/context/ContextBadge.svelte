<script lang="ts">
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import type {
    BadgeColor,
    BadgeSize,
  } from "$common-webviews/src/design-system/components/BadgeAugment/badge-types";
  import RegularFileIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file.svg?component";
  import RegularXmarkIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/xmark.svg?component";
  import RegularTriangleExclamationIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/triangle-exclamation.svg?component";

  export let color: BadgeColor | undefined = "neutral";
  export let size: BadgeSize | undefined = 1;
  export let removeBtn: { title: string; onRemove: () => void } | undefined = undefined;
  export let showWarning: boolean | undefined = undefined;
</script>

<div class="c-context-badge">
  <BadgeAugment.Root {color} {size} on:click>
    <slot name="chaser" slot="chaser" />
    <!-- Left buttons are inset on the left of the badge -->
    <slot name="leftButtons" slot="leftButtons">
      <div class="c-context-badge__left-icon">
        <BadgeAugment.IconButton disabled={true}>
          <slot name="leftIcon">
            <RegularFileIcon />
          </slot>
        </BadgeAugment.IconButton>
      </div>
    </slot>

    <!-- For non-buttons,  use left icon so the content has appropriate padding -->
    <div class="c-context-badge__content">
      <!-- Content Slot -->
      <slot />

      {#if showWarning}
        <RegularTriangleExclamationIcon class="c-pulse-chip-warning-icon" />
      {/if}
    </div>

    <!-- Right buttons are inset on the right of the badge -->
    <svelte:fragment slot="rightButtons">
      <slot name="rightButtons" />
      {#if removeBtn}
        <BadgeAugment.IconButton
          class="c-context-badge__remove-btn"
          title={removeBtn.title}
          on:click={removeBtn.onRemove}
          {size}
          variant="ghost"
          color="neutral"
        >
          <RegularXmarkIcon />
        </BadgeAugment.IconButton>
      {/if}
    </svelte:fragment>
  </BadgeAugment.Root>
</div>

<style>
  .c-context-badge {
    /* This is needed to ensure all context badges have the height of the contents */
    display: flex;
  }

  .c-context-badge__content {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    gap: calc(var(--ds-spacing-1) * 1.5);
  }

  .c-context-badge__left-icon :global(.c-base-btn:disabled) {
    cursor: initial;
    color: inherit;
  }
</style>

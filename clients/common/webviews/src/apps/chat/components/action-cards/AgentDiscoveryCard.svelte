<script lang="ts">
  import InfoCircled from "$common-webviews/src/design-system/icons/info-circled.svelte";
  import { getContext } from "svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import BaseCard from "./BaseCard.svelte";
  import { type AgentConversationModel } from "../../models/agent-conversation-model";

  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");
  $: hasEverUsedAgent = agentConversationModel.hasEverUsedAgent;
  $: shouldShowBanner = $hasEverUsedAgent === false;
</script>

<!-- Callout for Agent Model availability at the top -->
{#if shouldShowBanner}
  <BaseCard
    color="accent"
    description="Agent Mode is now available"
    onClose={async () => {
      // Close the banner by updating our local store (doesn't persist to global store)
      shouldShowBanner = false;
      agentConversationModel.setHasEverUsedAgent(true);
    }}
  >
    <InfoCircled slot="icon" />
    <ButtonAugment
      size={2}
      variant="solid"
      color="accent"
      on:click={async () => {
        shouldShowBanner = false;
        await agentConversationModel.setToAgentic();
      }}
      title="Try it out"
    >
      Try it out
    </ButtonAugment>
  </BaseCard>
{/if}

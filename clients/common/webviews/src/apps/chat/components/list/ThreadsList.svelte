<script lang="ts" context="module">
  export const EXPAND_THREADS_BUTTON_TEST_ID = "threads-expand-button";

  // Define thread types for type safety
  /* eslint-disable @typescript-eslint/naming-convention */
  export type ChatThread = {
    id: string;
    type: "chat";
    isNew?: boolean;
    title: string;
    updated_at: string;
    started_at: string;
    isPinned: boolean | undefined;
    conversation: IConversation;
    sortTimestamp: Date;
  };

  export type LocalAgentThread = {
    id: string;
    type: "localAgent";
    isNew?: boolean;
    title: string;
    updated_at: string;
    started_at: string;
    isPinned: boolean | undefined;
    conversation: IConversation;
    sortTimestamp: Date;
  };

  export type RemoteAgentThread = {
    id: string;
    type: "remoteAgent";
    isNew?: boolean;
    title: string;
    updated_at: string;
    started_at: string;
    status: RemoteAgentStatus;
    workspace_status: RemoteAgentWorkspaceStatus;
    is_setup_script_agent: boolean | undefined;
    workspace_setup: RemoteAgentWorkspaceSetup;
    has_updates?: boolean;
    agent: RemoteAgent;
    sortTimestamp: Date;
    isPinned?: boolean | undefined;
    conversation?: IConversation;
  };
  /* eslint-enable @typescript-eslint/naming-convention */

  // Helper function to check if a remote agent thread is active
  export function isActiveThread(
    thread: ChatThread | LocalAgentThread | RemoteAgentThread,
    currentlySelectedThreadId: string | undefined,
  ): boolean {
    if (thread.id === NEW_AGENT_KEY) return true;
    if (thread.id === currentlySelectedThreadId) return true;

    if (thread.isNew) return false;
    if (thread.type !== "remoteAgent") {
      return false;
    }
    const remoteThread = thread as RemoteAgentThread;
    return (
      // any agent that has updates OR is not idle OR is currently selected
      remoteThread.has_updates || remoteThread.status !== RemoteAgentStatus.agentIdle
    );
  }

  /**
   * Returns true if a thread is unpaused or has failed.
   *
   * A failed agent status takes precedence over a paused workspace status.
   *
   * Unspecified workspaces are treated as paused.
   */
  export function isUnpausedRemoteAgent(
    thread: ChatThread | LocalAgentThread | RemoteAgentThread,
  ): boolean {
    if (thread.isNew) return false;
    if (thread.type !== "remoteAgent") {
      return false;
    }
    const remoteThread = thread as RemoteAgentThread;
    return (
      (remoteThread.workspace_status !== RemoteAgentWorkspaceStatus.workspacePaused &&
        remoteThread.workspace_status !== RemoteAgentWorkspaceStatus.workspaceUnspecified) ||
      remoteThread.status === RemoteAgentStatus.agentFailed
    );
  }
</script>

<script lang="ts">
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/chevron-down.svelte";
  import SearchIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/magnifying-glass.svg?component";
  import RegularSidebarIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/sidebar.svg?component";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/triangle-exclamation.svg?component";
  import XMarkIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/xmark.svg?component";
  import RegularEllipsisIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import Trash from "$common-webviews/src/design-system/icons/trash.svelte";
  import CloudArrowUp from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/cloud-arrow-up.svg?component";

  import { ChatModeModel } from "$common-webviews/src/apps/chat/models/chat-mode-model";
  import type {
    ChatModel,
    SortableConversationFieldType,
  } from "$common-webviews/src/apps/chat/models/chat-model";
  import { ConversationModel } from "$common-webviews/src/apps/chat/models/conversation-model";
  import type { IConversation } from "$common-webviews/src/apps/chat/models/types";
  import { getThreadsWithSearchQuery } from "$common-webviews/src/apps/chat/utils/thread-search";
  import {
    groupThreadsByAgeWithActive,
    groupThreadsForCollapsedViewWithActive,
  } from "$common-webviews/src/apps/chat/utils/thread-grouping";
  import { GitReferenceModel } from "$common-webviews/src/apps/remote-agent-manager/models/git-reference-model";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { getRelativeTimeForStr } from "$common-webviews/src/apps/remote-agent-manager/utils";
  import {
    getOrgRepoFromUrl,
    getRepoNameFromUrl,
  } from "$common-webviews/src/apps/remote-agent-manager/utils/repository-utils";
  import { type SharedWebviewStoreModel } from "$common-webviews/src/common/models/shared-webview-store-model";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import {
    type RemoteAgent,
    RemoteAgentStatus,
    type RemoteAgentWorkspaceSetup,
    RemoteAgentWorkspaceStatus,
  } from "$vscode/src/remote-agent-manager/types";
  import {
    type ChatHomeWebviewState,
    SHARED_AGENT_STORE_CONTEXT_KEY,
  } from "$vscode/src/webview-panels/remote-agents/common-webview-store";

  import { getContext, onMount } from "svelte";
  import { writable } from "svelte/store";
  import { fly, slide } from "svelte/transition";
  import NewThreadDropdown from "./NewThreadDropdown.svelte";
  import ThreadsListRowItem from "./ThreadsListRowItem.svelte";
  import ThreadTypeFilterDropdown, {
    type ThreadTypeFilter,
  } from "./ThreadTypeFilterDropdown.svelte";
  import debounce from "lodash.debounce";
  import { NEW_AGENT_KEY } from "../../models/agent-constants";

  /**
   * This is a threads menu that shows all threads (chat, local agents, and remote agents).
   */

  //#region Context and states

  const NEW_REMOTE_AGENT_THREAD_ID = NEW_AGENT_KEY;

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const sharedWebviewStore = getContext<SharedWebviewStoreModel<ChatHomeWebviewState>>(
    SHARED_AGENT_STORE_CONTEXT_KEY,
  );
  const gitReferenceModel = getContext<GitReferenceModel>(GitReferenceModel.key);
  const chatModeModel = getContext<ChatModeModel>(ChatModeModel.key);

  // Get the chat model from props or context
  export let chatModelProp: ChatModel | undefined = undefined;
  let chatModel = chatModelProp || getContext<ChatModel>("chatModel");

  // Store for the current repository URL
  const currentRepoUrl = writable<string>("");

  // Variables for chat thread menu
  const enableShareService = writable(false);

  let searchInputElement: HTMLInputElement | undefined = undefined;

  // Fetch the current repository URL on mount
  onMount(async () => {
    try {
      const { remoteUrl, error: remoteUrlError } = await gitReferenceModel.getRemoteUrl();
      if (remoteUrlError) {
        console.error("Failed to get remote url:", remoteUrlError);
        return;
      }
      currentRepoUrl.set(remoteUrl);

      // Set enableShareService based on chat model flags
      if (chatModel && chatModel.flags) {
        enableShareService.update(() => chatModel.flags.enableShareService || false);
      }
    } catch (error) {
      console.error("Failed to get current repository URL:", error);
    }
  });

  $: currentAgentId = $remoteAgentsModel.currentAgentId;
  $: remoteAgents = $remoteAgentsModel.agentOverviews || [];

  // Get conversations directly from the chat model if available
  $: allChatModelConversations = chatModel ? Object.values($chatModel.orderedConversations()) : [];
  $: newLocalConversation = allChatModelConversations.find(ConversationModel.isNew);
  $: chatConversations = chatModel
    ? allChatModelConversations.filter(
        (c) => !c.extraData?.isAgentConversation && c !== newLocalConversation,
      )
    : [];
  $: localAgentConversations = chatModel
    ? allChatModelConversations.filter(
        (c) => c.extraData?.isAgentConversation === true && c !== newLocalConversation,
      )
    : [];

  $: threadsError = $remoteAgentsModel.agentThreadsError; // Use the specific error state for agent threads
  $: isLoading = $remoteAgentsModel.isLoading;
  $: hasFetchedOnce = $remoteAgentsModel.hasFetchedOnce;
  $: lastSuccessfulOverviewFetch = $remoteAgentsModel.lastSuccessfulOverviewFetch;
  $: isHomePanelOpen = $sharedWebviewStore.state?.activeWebviews.includes("home");

  // Thread counts are displayed in the filter dropdown

  // Get the current sort preference from the chat model
  $: sortConversationsBy = chatModel
    ? chatModel.sortConversationsBy
    : writable<SortableConversationFieldType>("lastMessageTimestamp");
  $: currentSortPreference = $sortConversationsBy || "lastMessageTimestamp";
  $: agentExecutionMode = $chatModel.agentExecutionMode;

  // Helper function to get the correct timestamp for sorting based on user preference
  function getConversationTimestamp(conversation: IConversation | undefined): Date {
    if (!conversation) {
      return new Date(0);
    }
    const sortBy = currentSortPreference;
    return ConversationModel.getTime(conversation, sortBy);
  }

  // Track the currently selected thread ID
  $: selectedThreadId = $remoteAgentsModel.isActive
    ? (currentAgentId ?? NEW_REMOTE_AGENT_THREAD_ID)
    : $chatModel.currentConversationId;

  //#region Search functionality

  let searchQuery = "";
  let isSearchActive = false;

  function toggleSearch() {
    isSearchActive = !isSearchActive;
    isExpanded = isSearchActive;
    if (!isSearchActive) {
      searchQuery = "";
    } else {
      setTimeout(() => {
        searchInputElement?.focus();
      }, 30);
    }
  }

  function clearSearch() {
    searchQuery = "";
    isSearchActive = false;
    isExpanded = false;
  }

  //#region Scroll Logic

  // Track scroll gestures for expand/collapse functionality
  let accumulatedUpwardScroll = 0;
  let accumulatedDownwardScroll = 0;
  const COLLAPSE_THRESHOLD = 200; // Amount of upward scroll needed to collapse (in pixels)
  const EXPAND_THRESHOLD = 150; // Amount of downward scroll needed to expand when collapsed (in pixels)
  let lastScrollTime = 0;
  let wasAtTopOnLastScroll = false; // Track if we were at top on the previous scroll event
  let wasAtBottomOnLastScroll = false; // Track if we were at bottom on the previous scroll event
  let lastScrollDirection: "up" | "down" | null = null; // Track the last scroll direction
  const SCROLL_RESET_DELAY = 100; // Reset accumulated scroll after this many ms of no scrolling
  let heightOfList = 0;
  let heightOfListContainer = 0;
  let containerWidth = 0;
  $: maxScrollTopWhenCollapsed = heightOfListContainer - heightOfList;

  /**
   * Resets scroll tracking state
   */
  function resetScrollTracking() {
    accumulatedUpwardScroll = 0;
    accumulatedDownwardScroll = 0;
  }

  /**
   * Handles upward scroll gestures for collapsing when at the top
   * @param deltaY The scroll delta Y value
   * @param isAtTop Whether the scroll position is at the top
   */
  function handleScrollUp(deltaY: number, isAtTop: boolean) {
    if (!isAtTop) return;
    if (!isExpanded) return;

    // Only start accumulating if we were already at the top on the previous scroll
    // This ensures the upward scroll gesture started from the top
    if (wasAtTopOnLastScroll) {
      accumulatedUpwardScroll += Math.abs(deltaY);

      // Only collapse if we've accumulated enough upward scroll
      if (accumulatedUpwardScroll >= COLLAPSE_THRESHOLD) {
        isExpanded = false;
        resetScrollTracking(); // Reset after collapsing
      }
    } else {
      // First upward scroll at top - start tracking but don't accumulate yet
      accumulatedUpwardScroll = 0;
    }
  }

  /**
   * Handles downward scroll gestures for expanding when collapsed
   * @param deltaY The scroll delta Y value
   */
  function handleScrollDown(deltaY: number) {
    if (isExpanded) {
      // If already expanded, just reset tracking and stay expanded
      resetScrollTracking();
      return;
    }
    if (wasAtBottomOnLastScroll) {
      accumulatedDownwardScroll += Math.abs(deltaY);
      if (accumulatedDownwardScroll >= EXPAND_THRESHOLD) {
        isExpanded = true;
        resetScrollTracking(); // Reset after expanding
      }
    } else {
      // First downward scroll at bottom - start tracking but don't accumulate yet
      accumulatedDownwardScroll = 0;
    }
  }

  /**
   * Handles wheel events on the agents list to expand/collapse based on scroll direction
   * @param event The wheel event
   */
  function handleWheel(event: WheelEvent) {
    if (!agentsListElement) {
      return;
    }

    const { deltaY } = event;
    const { scrollTop } = agentsListElement;
    const currentTime = Date.now();
    const isAtTop = scrollTop === 0;
    const isAtBottom = scrollTop === maxScrollTopWhenCollapsed;
    const currentDirection: "up" | "down" | null = deltaY > 0 ? "down" : deltaY < 0 ? "up" : null;

    // Reset accumulated scroll if too much time has passed since last scroll
    if (currentTime - lastScrollTime > SCROLL_RESET_DELAY) {
      resetScrollTracking();
      wasAtTopOnLastScroll = isAtTop;
      wasAtBottomOnLastScroll = isAtBottom;
      lastScrollDirection = null;
    }

    // Reset accumulation if direction changed
    if (lastScrollDirection && currentDirection && lastScrollDirection !== currentDirection) {
      resetScrollTracking();
    }

    // don't count the tiny, physics-driven scrolls at the end
    if (Math.abs(deltaY) > 5) {
      lastScrollTime = currentTime;
      lastScrollDirection = currentDirection;
    }

    if (!isAtTop) wasAtTopOnLastScroll = false;
    if (!isAtBottom) wasAtBottomOnLastScroll = false;

    // Handle different scroll directions
    if (deltaY > 0) {
      // Scrolling down
      if (scrollTop === maxScrollTopWhenCollapsed) {
        handleScrollDown(deltaY);
      } else {
        resetScrollTracking();
      }
    } else if (deltaY < 0) {
      // Scrolling up
      if (scrollTop > 0) {
        resetScrollTracking();
      } else {
        // At the top, handle potential collapse
        handleScrollUp(deltaY, isAtTop);
      }
    } else {
      // Reset if deltaY is 0 (shouldn't happen but safety check)
      resetScrollTracking();
    }
  }

  // Reference to the agents list element
  let agentsListElement: HTMLDivElement | undefined = undefined;

  //#region Threads

  /**
   * Toggles the pinned status of a thread
   * @param thread The thread to toggle pinned status for
   */
  async function toggleThreadPinned(
    thread: ChatThread | LocalAgentThread | RemoteAgentThread,
    event?: Event,
  ) {
    if (event) {
      event.stopPropagation(); // Prevent triggering thread selection
    }

    if (thread.isNew) {
      return;
    } else if (thread.type === "remoteAgent") {
      // Get the updated pinnedAgents from the toggleAgentPinned method
      const pinnedAgents = await remoteAgentsModel.toggleAgentPinned(
        thread.id,
        thread.isPinned || false,
      );

      // Update the shared store with the returned pinnedAgents
      sharedWebviewStore.update((state) => {
        if (!state) return;
        return {
          ...state,
          pinnedAgents,
        };
      });
    } else if (["chat", "localAgent"].includes(thread.type)) {
      // For chat and local agent threads, use the chat model's pinning functionality
      if (chatModel) {
        chatModel.toggleConversationPinned(thread.id);
      }
    }
  }
  // Create thread objects for chat and local agent conversations
  /* eslint-disable @typescript-eslint/naming-convention */
  $: newThread = (() => {
    if ($remoteAgentsModel.isActive) {
      if ($remoteAgentsModel.isCreatingAgent) {
        return undefined;
      }
      const draft = $chatModel.currentConversationModel.draftExchange?.request_message;
      return {
        id: NEW_REMOTE_AGENT_THREAD_ID,
        isNew: true,
        type: "remoteAgent",
        title: draft,
        updated_at: new Date().toISOString(),
        started_at: new Date().toISOString(),
        isPinned: false,
        conversation: undefined,
        sortTimestamp: new Date(),
      };
    }

    // newConversation is the draft local agent or chat conversation
    if (newLocalConversation) {
      return {
        id: newLocalConversation.id,
        isNew: true,
        type: newLocalConversation.extraData?.isAgentConversation ? "localAgent" : "chat",
        title: "",
        updated_at: newLocalConversation.lastInteractedAtIso,
        started_at: newLocalConversation.createdAtIso,
        isPinned: false,
        conversation: newLocalConversation,
        sortTimestamp: getConversationTimestamp(newLocalConversation),
      };
    }
  })();

  $: chatThreads = chatConversations.map(
    (conversation: IConversation): ChatThread => ({
      id: conversation.id,
      isNew: false,
      type: "chat",
      title: conversation.name || "",
      updated_at: conversation.lastInteractedAtIso,
      started_at: conversation.createdAtIso,
      isPinned: conversation.isPinned,
      conversation: conversation,
      sortTimestamp: getConversationTimestamp(conversation),
    }),
  );

  $: localAgentThreads = localAgentConversations.map(
    (conversation: IConversation): LocalAgentThread => ({
      id: conversation.id,
      isNew: false,
      type: "localAgent",
      title: conversation.name || "",
      updated_at: conversation.lastInteractedAtIso,
      started_at: conversation.createdAtIso,
      isPinned: conversation.isPinned,
      conversation: conversation,
      sortTimestamp: getConversationTimestamp(conversation),
    }),
  );

  // Get pinned agents from both the model and the shared store to ensure they're in sync
  $: pinnedAgents = $sharedWebviewStore.state?.pinnedAgents || {};

  // Convert remote agents to thread objects
  $: remoteAgentThreads = remoteAgents.map(
    (agent: RemoteAgent): RemoteAgentThread => ({
      id: agent.remote_agent_id,
      isNew: false,
      type: "remoteAgent",
      title: agent.session_summary,
      updated_at: agent.updated_at,
      started_at: agent.started_at,
      status: agent.status,
      workspace_status: agent.workspace_status,
      is_setup_script_agent: agent.is_setup_script_agent,
      workspace_setup: agent.workspace_setup || {},
      agent: agent,
      // For remote agents, use the updated_at timestamp for sorting
      sortTimestamp: new Date(agent.updated_at || agent.started_at),
      // Get pinned status from the global store
      isPinned: pinnedAgents[agent.remote_agent_id] || false,
      has_updates: agent.has_updates,
    }),
  );
  /* eslint-enable @typescript-eslint/naming-convention */

  $: doShowNewThread = newThread && selectedThreadId === newThread.id;

  // Combine all thread types
  $: allThreads = [
    doShowNewThread && newThread,
    ...chatThreads,
    ...localAgentThreads,
    ...remoteAgentThreads,
  ].filter(Boolean) as (ChatThread | LocalAgentThread | RemoteAgentThread)[];

  // Thread type filter
  $: threadTypeFilters = [
    { value: "all" as ThreadTypeFilter, label: "All threads", count: allThreads.length },
    { value: "chat" as ThreadTypeFilter, label: "Chats", count: chatThreads.length },
    {
      value: "localAgent" as ThreadTypeFilter,
      label: "Local agents",
      count: localAgentThreads.length,
    },
    {
      value: "remoteAgent" as ThreadTypeFilter,
      label: "Remote agents",
      count: remoteAgentThreads.length,
    },
    {
      value: "readyForReview" as ThreadTypeFilter,
      label: "Ready for review",
      count: readyForReview.length,
    },
    {
      value: "readyForReviewSuccess" as ThreadTypeFilter,
      label: "Ready for review (succeeded)",
      count: readyForReviewSuccess.length,
    },
    {
      value: "readyForReviewFailure" as ThreadTypeFilter,
      label: "Ready for review (failed)",
      count: readyForReviewFailure.length,
    },
  ];
  const activeThreadTypeFilter = writable<ThreadTypeFilter>("all");

  // Filter threads by type and search query
  $: filteredThreads = allThreads
    .filter((thread) => {
      // First filter by type
      if ($activeThreadTypeFilter === "readyForReview") {
        if (
          !(
            thread.type === "remoteAgent" &&
            (thread.agent?.status === RemoteAgentStatus.agentIdle ||
              thread.agent?.status === RemoteAgentStatus.agentFailed) &&
            thread.has_updates
          )
        ) {
          return false;
        }
      } else if ($activeThreadTypeFilter === "readyForReviewSuccess") {
        if (
          !(
            thread.type === "remoteAgent" &&
            thread.agent?.status === RemoteAgentStatus.agentIdle &&
            thread.has_updates
          )
        ) {
          return false;
        }
      } else if ($activeThreadTypeFilter === "readyForReviewFailure") {
        if (
          !(
            thread.type === "remoteAgent" &&
            thread.agent?.status === RemoteAgentStatus.agentFailed &&
            thread.has_updates
          )
        ) {
          return false;
        }
      } else if ($activeThreadTypeFilter !== "all" && thread.type !== $activeThreadTypeFilter) {
        return false;
      }

      // Then filter by search query if active
      return getThreadsWithSearchQuery(
        thread,
        searchQuery,
        $remoteAgentsModel.currentAgentId,
        $remoteAgentsModel.currentConversation,
      );
    })
    // limit to 300 for perf
    .slice(0, 300);

  //#region Expand/collapse

  // State for manual expansion/collapse
  let isExpanded = false;

  // State for showing remote agents when collapsed
  const showRemoteAgentsWhenCollapsed = writable(true);

  // Toggle the expanded state
  function toggleExpanded() {
    isExpanded = !isExpanded;
  }

  // Toggle showing remote agents when collapsed
  function toggleShowRemoteAgents() {
    showRemoteAgentsWhenCollapsed.update((value) => !value);
  }

  /**
   * Switches to the appropriate mode based on the thread type and updates all necessary state.
   * This function centralizes the mode switching logic to ensure consistency across the application.
   *
   * @param thread The thread to switch to
   * @returns A boolean indicating whether the switch was successful
   */
  function switchToThread(thread: ChatThread | LocalAgentThread | RemoteAgentThread): boolean {
    if (!thread) return false;

    // Optimistically clear has_updates for remote agent threads
    if (thread.type === "remoteAgent" && thread.has_updates) {
      remoteAgentsModel.optimisticallyClearAgentUpdates(thread.id);
    }

    // Set the active state based on thread type
    remoteAgentsModel.setIsActive(thread.type === "remoteAgent");

    // Use the ChatModeModel's switchToThread method to handle the mode switching
    const success = chatModeModel.switchToThread(thread.type, thread.id, $agentExecutionMode);

    return success;
  }

  function onWindowFocus() {
    remoteAgentsModel.setIsPanelFocused(true);
  }

  function onWindowBlur() {
    remoteAgentsModel.setIsPanelFocused(false);
  }

  // Number of skeleton rows to show when loading
  const skeletonRowCount = 3;

  // Create an array of skeleton rows
  $: skeletonRows = Array(skeletonRowCount).fill(null);

  // Override the actual state with debug state when in debug mode
  $: overviewError = threadsError?.errorMessage;
  $: effectiveIsLoading = isLoading && !hasFetchedOnce;
  $: effectiveHasError = !!overviewError && hasFetchedOnce;
  $: effectiveIsEmpty = hasFetchedOnce && filteredThreads.length === 0;

  // Calculate displayed groups - always use grouped structure, but limit threads when collapsed
  $: displayedGroups = (() => {
    if (isExpanded) {
      // When expanded, pass filtered threads and all threads to ensure active agents are always shown
      return groupThreadsByAgeWithActive(filteredThreads, allThreads, selectedThreadId);
    }

    // When collapsed, show unpaused remote agents only if the toggle is enabled
    if ($showRemoteAgentsWhenCollapsed) {
      return groupThreadsForCollapsedViewWithActive(filteredThreads, selectedThreadId);
    } else {
      // When collapsed and remote agents are hidden, show empty list
      return [];
    }
  })();

  //#region Resizing

  let expandedListHeight: number;
  $: {
    expandedListHeight = numberOfVisibleAgentsBase * THREAD_ITEM_HEIGHT;
    // calculate number of groups
    let groupCount = 0;
    let countedThreads = 0;
    for (const group of displayedGroups) {
      countedThreads += group.threads.length;
      groupCount++;
      if (countedThreads + groupCount >= numberOfVisibleAgentsBase) {
        break;
      }
    }
    expandedListHeight += groupCount * THREAD_ITEM_HEIGHT;
  }
  $: {
    if (agentsListElement) {
      agentsListElement.style.height = isExpanded ? `${expandedListHeight}px` : "";
    }
  }

  function numberOfAgentsFromHeight(height: number) {
    return height > 1 ? Math.round(Math.max(0, height) / THREAD_ITEM_HEIGHT) : 1;
  }

  // Calculate visible agents based on height and hover state
  let maxNumberOfVisibleAgents: number;
  $: maxNumberOfVisibleAgents = numberOfAgentsFromHeight(currentHeight);
  // Calculate total agents count
  $: totalAgentsCount = filteredThreads.length;
  // Calculate how many agents to show when not hovered
  $: numberOfVisibleAgentsBase = Math.min(maxNumberOfVisibleAgents, totalAgentsCount);

  let isDragging = false;
  let startY: number;
  let startHeight: number;
  const THREAD_ITEM_HEIGHT = 25;
  let currentHeight = THREAD_ITEM_HEIGHT;
  const minHeight = THREAD_ITEM_HEIGHT;
  let windowHeight = 0;
  let hasUserResized = false; // Track if user has manually resized
  $: fullAgentsListHeight = filteredThreads.length * THREAD_ITEM_HEIGHT;
  $: maxHeight = Math.max(Math.min(fullAgentsListHeight, Math.floor(windowHeight - 250)));
  const onResize = () => {
    if (searchQuery) return;
    // Only set default height if user hasn't manually resized
    if (windowHeight > 0 && !isDragging && !hasUserResized) {
      const defaultHeight = Math.min(THREAD_ITEM_HEIGHT * 10, fullAgentsListHeight, maxHeight);
      const newHeight = Math.max(minHeight, Math.min(maxHeight, defaultHeight));
      currentHeight = newHeight;
    } else {
      // make sure we're not too large
      currentHeight = Math.min(
        currentHeight,
        fullAgentsListHeight,
        Math.max(0, Math.floor(windowHeight - 250)),
      );
    }
    maxNumberOfVisibleAgents = numberOfAgentsFromHeight(currentHeight);
  };

  // Initialize height on component mount
  $: if (windowHeight > 0 && !hasUserResized) {
    onResize();
  }

  $: agentCount = filteredThreads.length;
  $: agentCount, onResize();
  const onResizeDebounce = debounce(onResize, 100);
  let hasDraggedThisClick = false;
  // No longer need mouse enter/leave handlers as we're using a click toggle
  function startDrag(event: MouseEvent) {
    isDragging = true;
    hasDraggedThisClick = false;
    startY = event.clientY;
    startHeight = currentHeight;
    // Prevent text selection during drag
    event.preventDefault();
  }
  function doDrag(event: MouseEvent) {
    if (!isDragging) {
      return;
    }
    hasDraggedThisClick = true;
    const deltaY = event.clientY - startY;
    const newHeight = startHeight + deltaY;
    // Snap to increments of snapIncrement
    const snappedHeight = Math.round(newHeight / THREAD_ITEM_HEIGHT) * THREAD_ITEM_HEIGHT;
    // Constrain height within min and max bounds
    currentHeight = Math.max(minHeight, Math.min(maxHeight, snappedHeight));
  }
  function stopDrag() {
    if (isDragging) hasUserResized = true; // Mark that user has manually resized
    isDragging = false;
    setTimeout(() => {
      hasDraggedThisClick = false;
    }, 0);
  }

  //#region Repo

  // Helper function to get repository URL from agent
  function getAgentRepoUrl(agent: RemoteAgent): string {
    return agent.workspace_setup?.starting_files?.github_commit_ref?.repository_url || "";
  }

  // Helper function to check if an agent is from a different repo
  function isFromDifferentRepo(agent: RemoteAgent): boolean {
    if (!agent || typeof agent !== "object" || !("workspace_setup" in agent)) {
      return false;
    }
    const agentRepoUrl = getAgentRepoUrl(agent);
    return !!agentRepoUrl && !!$currentRepoUrl && agentRepoUrl !== $currentRepoUrl;
  }

  // Helper function to get repo info for display
  function getRepoInfo(agent: RemoteAgent): { name: string; fullPath: string } {
    if (!agent || typeof agent !== "object" || !("workspace_setup" in agent)) {
      return { name: "Unknown", fullPath: "Unknown" };
    }
    const repoUrl = getAgentRepoUrl(agent);
    return {
      name: getRepoNameFromUrl(repoUrl),
      fullPath: getOrgRepoFromUrl(repoUrl),
    };
  }

  //#region Active remote agents

  // Calculate active remote agents (remote workspaces that are not paused)
  $: unpausedRemoteAgent = filteredThreads.filter(isUnpausedRemoteAgent);
  $: unpausedRemoteAgentCount = unpausedRemoteAgent.length;

  $: readyForReview = filteredThreads.filter(
    (thread): thread is RemoteAgentThread => thread.type === "remoteAgent" && !!thread.has_updates,
  );

  $: readyForReviewSuccess = readyForReview.filter(
    (thread) => thread.agent?.status === RemoteAgentStatus.agentIdle,
  );
  $: readyForReviewFailure = readyForReview.filter(
    (thread) => thread.agent?.status === RemoteAgentStatus.agentFailed,
  );

  /**
   * Deletes all threads in a group
   * @param threads - Array of threads to delete
   */
  async function deleteAllThreadsInGroup(
    threads: (ChatThread | LocalAgentThread | RemoteAgentThread)[],
  ) {
    // Separate conversation IDs from remote agent IDs
    const conversationIds: string[] = [];
    const remoteAgentIds: string[] = [];

    for (const thread of threads) {
      if (thread.type === "remoteAgent") {
        remoteAgentIds.push(thread.id);
      } else {
        conversationIds.push(thread.id);
      }
    }

    await chatModel.deleteConversations(
      conversationIds,
      undefined, // nextConvoId
      remoteAgentIds, // Remote agent IDs to delete
      remoteAgentsModel, // Pass the remote agents model for handling remote agent deletions
    );
  }
</script>

<svelte:window
  on:mousemove={doDrag}
  on:mouseup={stopDrag}
  bind:innerHeight={windowHeight}
  on:resize={onResizeDebounce}
  on:focus={onWindowFocus}
  on:blur={onWindowBlur}
/>

<div class="remote-agent-threads-container">
  <div class="remote-agent-threads" bind:clientWidth={containerWidth}>
    <div
      class="agents-list-container"
      class:is-dragging={isDragging}
      class:is-expanded={isExpanded}
      role="region"
      aria-label="Remote agents list"
    >
      <div class="header">
        <div class="header-left">
          <!-- Expand/collapse toggle button -->
          <TextTooltipAugment
            content={isExpanded ? "Collapse threads" : "Expand threads"}
            nested={false}
            delayDurationMs={1000}
            triggerOn={[TooltipTriggerOn.Hover]}
          >
            <button
              class="expand-toggle-button"
              on:click={toggleExpanded}
              on:keydown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  toggleExpanded();
                }
              }}
              aria-label={isExpanded ? "Collapse threads" : "Expand threads"}
              aria-expanded={isExpanded}
              type="button"
              data-testid={EXPAND_THREADS_BUTTON_TEST_ID}
            >
              <ChevronDown />
            </button>
          </TextTooltipAugment>

          <!-- Threads text outside of dropdown -->
          <div class="header-text">
            <TextAugment size={1} class="header-text-main">Threads</TextAugment>
          </div>

          <div class="flip-icon">
            <!-- Thread type filter dropdown (when expanded) or remote agents toggle (when collapsed) -->
            {#if !isSearchActive}
              {#if isExpanded}
                <div class="flip-icon__child" transition:fly={{ y: -5, duration: 150 }}>
                  <ThreadTypeFilterDropdown {threadTypeFilters} {activeThreadTypeFilter} />
                </div>
              {:else if unpausedRemoteAgentCount > 0}
                <!-- Remote agents toggle when collapsed -->
                <div class="flip-icon__child" transition:fly={{ y: 5, duration: 150 }}>
                  <TextTooltipAugment
                    content={$showRemoteAgentsWhenCollapsed
                      ? `Showing ${unpausedRemoteAgentCount} active remote agent${unpausedRemoteAgentCount === 1 ? "" : "s"}. Click to hide.`
                      : `${unpausedRemoteAgentCount} active remote agent${unpausedRemoteAgentCount === 1 ? "" : "s"}. Click to show while collapsed.`}
                    nested={false}
                  >
                    <ButtonAugment
                      variant={$showRemoteAgentsWhenCollapsed ? "soft" : "ghost"}
                      color={$showRemoteAgentsWhenCollapsed ? "accent" : "neutral"}
                      size={1}
                      on:click={toggleShowRemoteAgents}
                    >
                      <CloudArrowUp slot="iconLeft" />
                      {unpausedRemoteAgentCount}
                    </ButtonAugment>
                  </TextTooltipAugment>
                </div>
              {/if}
            {/if}
          </div>
          <div
            class="search-container action-button"
            class:is-active={isSearchActive}
            transition:fly={{ y: -2, duration: 150 }}
          >
            {#if isSearchActive}
              <div class="search-field-container" transition:slide={{ axis: "x", duration: 150 }}>
                <TextFieldAugment
                  bind:value={searchQuery}
                  placeholder={`Search ${$activeThreadTypeFilter === "all" ? "threads" : threadTypeFilters.find((f) => f.value === $activeThreadTypeFilter)?.label?.toLowerCase()}`}
                  size={1}
                  variant="surface"
                  class="search-input"
                  bind:textInput={searchInputElement}
                  on:keydown={(event) => {
                    if (event.key === "Escape") {
                      clearSearch();
                    }
                  }}
                  on:focus={() => {
                    isSearchActive = true;
                  }}
                  on:blur={() => {
                    if (!searchQuery.trim()) {
                      isSearchActive = false;
                      isExpanded = false;
                    }
                  }}
                >
                  <div class="search-field-container__icon" slot="iconLeft">
                    <SearchIcon />
                  </div>
                </TextFieldAugment>
                {#if searchQuery}
                  <div class="clear-search-button">
                    <TextTooltipAugment content="Clear search" nested={false}>
                      <IconButtonAugment
                        variant="ghost"
                        color="neutral"
                        size={1}
                        on:click={clearSearch}
                      >
                        <XMarkIcon />
                      </IconButtonAugment>
                    </TextTooltipAugment>
                  </div>
                {/if}
              </div>
            {/if}
            <div class="search-icon" class:is-active={isSearchActive}>
              <TextTooltipAugment content="Search threads" nested={false} hasPointerEvents={false}>
                <IconButtonAugment
                  variant="ghost"
                  color={searchQuery.trim() !== "" ? "accent" : "neutral"}
                  size={1}
                  on:click={toggleSearch}
                >
                  <SearchIcon />
                </IconButtonAugment>
              </TextTooltipAugment>
            </div>
          </div>

          <!-- show home panel button -->
          <div class="action-button" transition:fly={{ y: -2, duration: 150 }}>
            <TextTooltipAugment
              content="Expand remote agents dashboard"
              nested={false}
              triggerOn={[TooltipTriggerOn.Hover]}
              hasPointerEvents={false}
            >
              <IconButtonAugment
                variant="ghost"
                color={isHomePanelOpen ? "accent" : "neutral"}
                size={1}
                on:click={() => {
                  if (isHomePanelOpen) {
                    remoteAgentsModel.closeRemoteAgentHomePanel();
                  } else {
                    remoteAgentsModel.showRemoteAgentHomePanel();
                  }
                }}
              >
                <RegularSidebarIcon />
              </IconButtonAugment>
            </TextTooltipAugment>
          </div>

          {#if overviewError && !effectiveHasError}
            <TextTooltipAugment
              content="Data may be outdated. Last refreshed {lastSuccessfulOverviewFetch > 0
                ? getRelativeTimeForStr(new Date(lastSuccessfulOverviewFetch).toISOString())
                : 'never'}"
              nested={false}
            >
              <div class="stale-indicator">
                <div class="stale-dot"></div>
              </div>
            </TextTooltipAugment>
          {/if}
        </div>

        <div class="header-right">
          <!-- Search input -->
          <!-- new agent button with dropdown -->
          <NewThreadDropdown />
        </div>
      </div>
      {#if !effectiveIsLoading && !effectiveIsEmpty}
        <div
          class="agents-list"
          bind:this={agentsListElement}
          bind:clientHeight={heightOfList}
          on:wheel={handleWheel}
        >
          <div class="agents-list-container__inner" bind:clientHeight={heightOfListContainer}>
            <!-- Always show the same list, but with conditional group headers -->
            {#each displayedGroups as group (`${group.groupTitle}-${group.threads.length}`)}
              <!-- Group header - only visible when expanded -->
              {#if isExpanded}
                <div class="thread-group-header" transition:slide={{ duration: 150 }}>
                  <div class="thread-group-header-content">
                    <TextAugment size={1} weight="medium" color="secondary"
                      >{group.groupTitle}</TextAugment
                    >
                    <div class="thread-group-options">
                      <DropdownMenuAugment.Root>
                        <DropdownMenuAugment.Trigger>
                          <IconButtonAugment
                            variant="ghost-block"
                            color="neutral"
                            size={1}
                            title="Group options"
                          >
                            <RegularEllipsisIcon />
                          </IconButtonAugment>
                        </DropdownMenuAugment.Trigger>
                        <DropdownMenuAugment.Content size={1} side="bottom" align="end">
                          <DropdownMenuAugment.Item
                            color="error"
                            onSelect={() => deleteAllThreadsInGroup(group.threads)}
                          >
                            <Trash slot="iconLeft" />
                            Delete {group.threads.length} thread{group.threads.length > 1
                              ? "s"
                              : ""}
                          </DropdownMenuAugment.Item>
                        </DropdownMenuAugment.Content>
                      </DropdownMenuAugment.Root>
                    </div>
                  </div>
                </div>
              {/if}

              <!-- Group threads - always visible -->
              {#each group.threads as thread, index (`${thread.id}-${thread.isPinned}-${index}`)}
                <ThreadsListRowItem
                  {thread}
                  {containerWidth}
                  isSelected={selectedThreadId === thread.id}
                  afterDelete={() => {
                    if (selectedThreadId !== thread.id) return;
                    const nextThread = group.threads[index + 1] || group.threads[index - 1];
                    if (nextThread) {
                      switchToThread(nextThread);
                    }
                  }}
                  onSelect={() => switchToThread(thread)}
                  onTogglePinned={(event) => toggleThreadPinned(thread, event)}
                  {isFromDifferentRepo}
                  {getRepoInfo}
                  enableShareService={$enableShareService}
                  {isExpanded}
                />
              {/each}
            {/each}
          </div>
        </div>
      {/if}
    </div>

    <!-- Loading and error states below the agent list -->
    {#if effectiveIsLoading}
      <div class="loading-state">
        <!-- Skeleton loading state -->
        {#each skeletonRows as _, i (i)}
          <div class="agent-row skeleton-row">
            <div class="agent-content">
              <div class="agent-details">
                <div class="agent-details--left">
                  <div class="agent-icon">
                    <div class="skeleton-dot"></div>
                  </div>
                  <div class="skeleton-text"></div>
                </div>
              </div>
            </div>
          </div>
        {/each}
      </div>
    {:else if effectiveHasError}
      <!-- Error state - only show full error state if we have no data -->
      {#if remoteAgents.length === 0}
        <div class="error-message">
          <ExclamationTriangle />
          Failed to load threads. {overviewError}
        </div>
      {:else}
        <!-- Show a non-disruptive error banner if we have data -->
        <div class="error-banner">
          <ExclamationTriangle />
          <TextAugment size={1} color="error">{overviewError}</TextAugment>
        </div>
      {/if}
    {:else if effectiveIsEmpty}
      <!-- Empty state -->
      <div class="empty-message">
        {#if searchQuery.trim() !== ""}
          <TextAugment size={1} color="secondary"
            >No threads with "{searchQuery}" in the title.</TextAugment
          >
        {:else}
          <TextAugment size={1} color="secondary">Create a new thread to get started.</TextAugment>
        {/if}
      </div>
    {/if}

    {#if !searchQuery}
      <div class="resize-handle-container">
        <TextTooltipAugment
          content={isExpanded ? "Resize or click to collapse" : "Expand threads"}
          side="bottom"
          hasPointerEvents={false}
          offset={[0, 8]}
        >
          <button
            class="resize-handle"
            class:resize-handle--dragging={isDragging}
            class:resize-handle--expanded={isExpanded}
            on:mousedown={startDrag}
            on:click={() => {
              if (hasDraggedThisClick) return;
              toggleExpanded();
            }}
          >
            <div class="resize-handle-line" />
          </button>
        </TextTooltipAugment>
      </div>
    {/if}
  </div>
</div>

<style>
  .remote-agent-threads-container {
    margin-top: calc(0px - var(--ds-spacing-2));
    margin-left: calc(0px - var(--chat-padding));
    margin-right: calc(0px - var(--chat-padding));
    border-bottom: 1px solid var(--augment-border-color);
    background-color: var(--augment-window-background);
    width: 100dvw;
  }
  .remote-agent-threads {
    position: relative;
    width: 100%;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px var(--ds-spacing-3) var(--ds-spacing-1) 4px;
  }

  .header-text {
    display: flex;
    align-items: center;
    gap: 3px;
    padding: 4px 4px 4px 0;
  }

  .header-text :global(.chat-btn) {
    color: var(--ds-color-neutral-9);
  }

  .header-text :global(button.c-base-btn.c-base-btn--ghost.expand-collapse-button) {
    color: var(--ds-color-neutral-10);
    padding: 0;
    width: 20px;
    height: 20px;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    appearance: none;
    flex: 1;
    background: none;
    border: none;
    color: inherit;
  }
  .header-right {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-3);
  }

  .action-button {
    transition: all 0.2s ease-out;
  }
  .agents-list-container {
    display: flex;
    flex-direction: column;
  }

  .agents-list {
    display: flex;
    flex-direction: column;
    flex: none;
    position: relative;
    z-index: var(--z-thread-list);
    width: 100%;
    max-height: 125px;
    overflow: auto;
    /* Ensure scrollbar is always visible when content overflows */
    scrollbar-width: thin;
    scrollbar-color: var(--augment-scrollbar-color) transparent;
  }

  /* Webkit scrollbar styling for consistency */
  .agents-list::-webkit-scrollbar {
    width: 8px;
  }

  .agents-list::-webkit-scrollbar-track {
    background: transparent;
  }

  .agents-list::-webkit-scrollbar-thumb {
    background-color: var(--augment-scrollbar-color);
    border-radius: 4px;
  }

  .agents-list::-webkit-scrollbar-thumb:hover {
    background-color: var(--vscode-scrollbarSlider-hoverBackground, var(--augment-scrollbar-color));
  }

  .resize-handle-container :global(.l-tooltip-trigger) {
    width: 100%;
  }
  .resize-handle {
    position: absolute;
    bottom: -5.5px;
    left: 0;
    right: 0;
    height: 11px;
    cursor: ns-resize;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--ds-color-panel);
    z-index: 20;
    appearance: none;
    border: none;
    color: inherit;
  }
  .resize-handle-line {
    width: 100%;
    height: 2px;
    background-color: var(--ds-color-border);
    border-radius: 1px;
  }

  .resize-handle:hover {
    background-color: var(--ds-color-panel-hover);
  }
  .resize-handle:hover .resize-handle-line {
    background-color: var(--ds-color-accent-9);
  }
  .agents-list-container.is-dragging {
    user-select: none;
    cursor: ns-resize;
    pointer-events: none;
  }
  .resize-handle:not(.resize-handle--expanded) {
    cursor: pointer;
  }

  /* Disable pointer events on child elements while dragging */
  .agents-list-container.is-dragging .agents-list * {
    pointer-events: none;
  }
  .resize-handle--dragging {
    background-color: var(--ds-color-panel-hover);
  }
  .resize-handle--dragging .resize-handle-line {
    background-color: var(--ds-color-accent-9);
  }

  .agent-row {
    width: 100%;
    transition: all 0.2s ease;
    flex: none;
    height: 25px;
    padding: 0 var(--ds-spacing-3);
    cursor: pointer;
    max-width: 100%;
    box-sizing: border-box;
  }

  .agent-row:hover {
    color: var(--ds-color-neutral-12);
  }

  .agent-row :global(.c-agent-header__delete-btn) {
    opacity: 0;
    width: 0;
    transition: all 0.03s ease;
  }

  .agent-row:hover :global(.c-agent-header__delete-btn) {
    opacity: 1;
    width: 22px;
  }
  .agent-content {
    display: flex;
    justify-content: space-between;
    width: 100%;
    max-width: 100%;
  }

  .agent-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    min-width: 0;
  }

  .agent-details--left {
    display: inline-flex;
    gap: 6px;
    flex: 1;
    min-width: 0;
  }

  @media (max-width: 400px) {
    .agent-details {
      max-width: calc(100% - 25px);
    }
  }

  @keyframes hide-scroll {
    from,
    to {
      overflow: hidden;
    }
  }
  .agents-list-container.is-expanded .agents-list {
    animation: hide-scroll 0.2s backwards;
    max-height: calc(80vh - 10rem);
    overflow: auto;
  }

  .header-left :global(.expand-toggle-button) {
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    padding: 4px;
    margin-left: 3px;
    cursor: pointer;
    color: var(--ds-color-neutral-10);
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  .header-left :global(.expand-toggle-button:hover) {
    background-color: var(--ds-color-panel-hover);
    color: var(--ds-color-neutral-11);
  }

  .header-left :global(.expand-toggle-button svg) {
    width: 14px;
    height: 14px;
    transform: rotate(-90deg);
    transition: transform 0.2s ease;
  }

  .is-expanded .header-left :global(.expand-toggle-button svg) {
    transform: rotate(0deg);
  }

  /* Search styles */
  .search-container {
    position: relative;
    width: 25px;
  }

  .search-container.is-active {
    width: 180px;
  }

  .search-container :global(.search-field-container__icon) {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 14px;
    margin-left: 2px;
    margin-right: 2px;
  }

  .search-icon {
    position: absolute;
    left: 4px;
    top: 50%;
    transform: translateY(-50%);
  }
  .search-icon.is-active {
    opacity: 0;
    pointer-events: none;
  }

  .search-field-container {
    position: relative;
    width: 100%;
  }

  .search-icon :global(svg) {
    width: 14px;
    height: 14px;
  }

  .clear-search-button {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :global(.search-input input) {
    padding-left: 30px !important;
  }

  /* Indicator styles */
  .stale-indicator {
    display: flex;
    align-items: center;
    margin-left: 6px;
  }

  .stale-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--ds-color-warning-9);
  }

  /* Skeleton loading styles */
  .skeleton-row {
    pointer-events: none;
  }

  .skeleton-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--ds-color-neutral-8);
    animation: pulse 1.5s infinite;
  }

  .skeleton-text {
    height: 14px;
    width: 90%;
    background-color: var(--ds-color-neutral-8);
    border-radius: 3px;
    animation: pulse 1.5s infinite;
  }
  .skeleton-row:first-child .skeleton-text {
    width: 70%;
  }
  .skeleton-row:nth-child(2) .skeleton-text {
    width: 60%;
  }
  .skeleton-row:nth-child(3) .skeleton-text {
    width: 90%;
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 0.3;
    }
    100% {
      opacity: 0.6;
    }
  }

  /* Loading, error and empty state styles */
  .empty-message {
    display: flex;
    align-items: center;
    justify-content: center;
    font-style: italic;
    padding: var(--ds-spacing-3) var(--ds-spacing-2) var(--ds-spacing-4);
    text-align: center;
    color: var(--ds-color-neutral-11);
    min-height: 30px;
  }

  .error-message {
    padding: var(--ds-spacing-2) var(--ds-spacing-3) var(--ds-spacing-3);
    display: flex;
    gap: var(--ds-spacing-2);
    color: var(--ds-color-error-11);
  }

  .error-banner {
    padding: var(--ds-spacing-2) var(--ds-spacing-3);
    display: flex;
    gap: var(--ds-spacing-3);
    align-items: center;
    background-color: var(--ds-color-error-1);
  }

  .error-message :global(svg),
  .error-banner :global(svg) {
    margin-top: 2px;
    flex: none;
    fill: currentColor;
    width: 13px;
  }

  /* Make skeleton rows in the loading state match the agent rows */
  .loading-state .agent-row {
    margin-bottom: var(--ds-spacing-2);
  }

  /* Thread group styles */
  .thread-group-header {
    padding: 0 var(--ds-spacing-3);
    border-bottom: 1px solid var(--ds-color-border);
    flex: none;
  }

  .thread-group-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .thread-group-options {
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .thread-group-header:hover .thread-group-options,
  .thread-group-header:focus-within .thread-group-options {
    opacity: 1;
  }

  .flip-icon {
    /* make sure children don't take up space when transitioning */
    display: grid;
  }
  .flip-icon__child {
    grid-column-start: 1;
    grid-column-end: 2;
    grid-row-start: 1;
    grid-row-end: 2;
  }
</style>

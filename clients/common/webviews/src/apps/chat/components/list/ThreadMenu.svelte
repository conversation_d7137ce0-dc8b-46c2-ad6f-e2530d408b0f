<script lang="ts">
  export { default } from "./ThreadMenu.svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import DotsHorizontal from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import PencilIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pen-to-square.svg?component";
  import Share from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/share-nodes.svg?component";
  import Bookmark from "$common-webviews/src/design-system/icons/fontawesome/svgs/solid/bookmark.svg?component";
  import Check from "$common-webviews/src/design-system/icons/check.svelte";
  import Cross from "$common-webviews/src/design-system/icons/cross-2.svelte";
  import Trash from "$common-webviews/src/design-system/icons/trash.svelte";
  import ClipboardCopy from "$common-webviews/src/design-system/icons/clipboard-copy.svelte";
  import { getContext } from "svelte";
  import { writable } from "svelte/store";
  import type { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";

  let chatModel = getContext<ChatModel>("chatModel");
  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  export let thread: {
    id: string;
    type: "chat" | "localAgent" | "remoteAgent";
    title: string;
    isPinned?: boolean;
    conversation?: {
      id?: string;
      isShareable?: boolean;
    };
  };
  export let type: "chat" | "localAgent" | "remoteAgent" = "chat";
  export let afterDelete: (() => void) | undefined = undefined;
  export let enableShareService: boolean = false;
  export let onRename: ((threadId: string, newTitle: string) => void) | undefined = undefined;

  let requestClose: () => void;

  // State for share functionality
  let showCopiedMessage: "retrieving" | "copied" | "failure" | undefined = undefined;
  let copyLinkError: string = "";

  // State for copy agent ID functionality
  const agentIdCopyTextMap = writable(new Map<string, string>());

  // Toggle pinned status
  function togglePinned() {
    if (type === "remoteAgent") {
      remoteAgentsModel?.toggleAgentPinned(thread.id, thread.isPinned || false);
    } else {
      // For chat and local agent threads, use the chat model's pinning functionality
      if (chatModel) {
        chatModel.toggleConversationPinned(thread.id);
      }
    }
  }

  $: isDebugFeatures = chatModel.flags.enableDebugFeatures;

  async function deleteThread() {
    afterDelete?.();
    if (type === "remoteAgent") {
      await remoteAgentsModel?.deleteAgent(thread.id);
    } else {
      await chatModel.deleteConversation(thread.id);
    }
  }

  // Rename thread
  function renameThread() {
    onRename?.(thread.id, thread.title);
    requestClose();
  }

  // Copy agent ID to clipboard
  function copyAgentId() {
    navigator.clipboard.writeText(thread.id);
    agentIdCopyTextMap.update((map) => {
      map.set(thread.id, "Copied!");
      return map;
    });
    setTimeout(() => {
      agentIdCopyTextMap.update((map) => {
        map.delete(thread.id);
        return map;
      });
    }, 1000);
  }

  // Share conversation
  async function shareConversation() {
    if (!thread.conversation?.id) return;

    if (showCopiedMessage !== undefined) {
      // A share request is in progress
      return;
    }

    const conversation = chatModel?.conversations[thread.conversation.id];
    showCopiedMessage = conversation?.lastUrl ? "copied" : "retrieving";

    try {
      if (chatModel) {
        const url = await chatModel.getConversationUrl(thread.conversation.id);
        navigator.clipboard.writeText(url);
        showCopiedMessage = "copied";
      }
    } catch (err) {
      console.error("Failed to get conversation URL: ", err);
      copyLinkError = err instanceof Error ? err.message : "Unknown error";
      showCopiedMessage = "failure";
    } finally {
      setTimeout(() => {
        showCopiedMessage = undefined;
      }, 1200);
    }
  }
</script>

<DropdownMenuAugment.Root bind:requestClose nested={false}>
  <DropdownMenuAugment.Trigger>
    <IconButtonAugment
      variant="ghost-block"
      color="neutral"
      size={1}
      class="c-agent-header__options-btn"
    >
      <DotsHorizontal />
    </IconButtonAugment>
  </DropdownMenuAugment.Trigger>
  <DropdownMenuAugment.Content size={1} side="bottom" align="end">
    <div class="c-thread-menu">
      {#if type !== "remoteAgent"}
        {#if enableShareService && thread.conversation?.isShareable}
          <DropdownMenuAugment.Item class="c-thread-menu__share-link" onSelect={shareConversation}>
            <svelte:fragment slot="iconLeft">
              {#if showCopiedMessage === "retrieving"}
                <SpinnerAugment size={1} />
              {:else if showCopiedMessage === "copied"}
                <Check />
              {:else if showCopiedMessage === "failure"}
                <Cross />
              {:else}
                <Share />
              {/if}
            </svelte:fragment>

            {#if copyLinkError}
              {copyLinkError}
            {:else if showCopiedMessage === "retrieving"}
              Creating link
            {:else if showCopiedMessage === "copied"}
              Link copied
            {:else if showCopiedMessage === "failure"}
              Unable to create link
            {:else}
              Share link to session
            {/if}
          </DropdownMenuAugment.Item>
        {/if}

        {#if onRename}
          <DropdownMenuAugment.Item onSelect={renameThread}>
            <PencilIcon slot="iconLeft" />
            Rename
          </DropdownMenuAugment.Item>
        {/if}
      {/if}

      <DropdownMenuAugment.Item onSelect={togglePinned}>
        <svelte:fragment slot="iconLeft">
          <div class="bookmark-icon">
            <Bookmark />
          </div>
        </svelte:fragment>
        {thread.isPinned ? "Unpin" : "Pin"}
      </DropdownMenuAugment.Item>

      {#if type === "remoteAgent"}
        <DropdownMenuAugment.Item onSelect={copyAgentId}>
          <ClipboardCopy slot="iconLeft" />
          {#if $agentIdCopyTextMap.get(thread.id)}
            {$agentIdCopyTextMap.get(thread.id)}
          {:else}
            Copy ID
          {/if}
        </DropdownMenuAugment.Item>
      {/if}

      <DropdownMenuAugment.Item color="error" onSelect={deleteThread}>
        <svelte:fragment slot="iconLeft">
          <div class="trash-icon">
            <Trash />
          </div>
        </svelte:fragment>
        Delete
      </DropdownMenuAugment.Item>

      {#if type === "remoteAgent" && isDebugFeatures}
        <DropdownMenuAugment.Separator />
        <DropdownMenuAugment.Label>INTERNAL Debug Features</DropdownMenuAugment.Label>
        <DropdownMenuAugment.Item
          onSelect={() => remoteAgentsModel.pauseRemoteAgentWorkspace(thread.id)}
        >
          Pause
        </DropdownMenuAugment.Item>
        <DropdownMenuAugment.Item
          onSelect={() => remoteAgentsModel.resumeRemoteAgentWorkspace(thread.id)}
        >
          Resume
        </DropdownMenuAugment.Item>
      {/if}
    </div>
  </DropdownMenuAugment.Content>
</DropdownMenuAugment.Root>

<style>
  .c-thread-menu :global(svg) {
    width: 12px;
    height: 12px;
  }

  .trash-icon :global(svg) {
    width: 15px;
    height: 15px;
  }

  .c-thread-menu :global(.c-base-btn) {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .c-thread-menu :global(.c-dropdown-menu-augment__item-icon) {
    display: flex;
    align-items: center;
  }
  .c-thread-menu {
    display: flex;
    flex-direction: column;
  }
</style>

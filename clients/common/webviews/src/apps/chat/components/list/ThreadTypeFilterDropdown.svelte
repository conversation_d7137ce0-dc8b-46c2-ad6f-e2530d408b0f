<script context="module" lang="ts">
  // Define thread types for type safety
  export type ThreadTypeFilter =
    | "all"
    | "chat"
    | "localAgent"
    | "remoteAgent"
    | "readyForReview"
    | "readyForReviewSuccess"
    | "readyForReviewFailure";
</script>

<script lang="ts">
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment/index";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/chevron-down.svelte";
  import { writable, type Writable } from "svelte/store";

  // Export props
  export let threadTypeFilters: Array<{
    value: ThreadTypeFilter;
    label: string;
    count: number;
  }> = [];

  // Create a store for the active filter
  export let activeThreadTypeFilter: Writable<ThreadTypeFilter> = writable<ThreadTypeFilter>("all");

  // Function to close the dropdown
  let requestCloseFilterDropdown: () => void;
</script>

<div class="c-thread-type-filter-dropdown">
  <DropdownMenuAugment.Root bind:requestClose={requestCloseFilterDropdown}>
    <DropdownMenuAugment.Trigger>
      <div class="c-thread-type-filter-dropdown__trigger">
        <TextAugment size={1}>
          {$activeThreadTypeFilter !== "all"
            ? `${threadTypeFilters.find((f) => f.value === $activeThreadTypeFilter)?.label || ""}`
            : "All"}
        </TextAugment>
        <div class="dropdown-indicator">
          <ChevronDown />
        </div>
      </div>
    </DropdownMenuAugment.Trigger>
    <DropdownMenuAugment.Content side="bottom" align="start" size={1}>
      <div class="filters-list">
        {#each threadTypeFilters as filter}
          <DropdownMenuAugment.Item
            highlight={$activeThreadTypeFilter === filter.value}
            onSelect={() => {
              activeThreadTypeFilter.set(filter.value);
              requestCloseFilterDropdown();
            }}
          >
            <div class="filter-item">
              <span class="filter-label">
                <TextAugment size={2}>
                  {filter.label}
                </TextAugment>
              </span>
              <span class="filter-count">{filter.count}</span>
            </div>
          </DropdownMenuAugment.Item>
        {/each}
      </div>
    </DropdownMenuAugment.Content>
  </DropdownMenuAugment.Root>
</div>

<style>
  .c-thread-type-filter-dropdown__trigger {
    display: flex;
    align-items: center;
    gap: 3px;
    border-radius: 4px;
    padding: 2px 8px;
    background-color: var(--ds-color-neutral-a3);
    color: var(--ds-color-neutral-a11);
    cursor: pointer;
    white-space: nowrap;
  }

  .dropdown-indicator {
    margin-right: 4px;
    display: flex;
    align-items: center;
    color: var(--ds-color-neutral-a8);
  }

  /* Filter dropdown styles */
  .filters-list {
    display: flex;
    flex-direction: column;
  }

  .filter-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 2px var(--ds-spacing-2);
    width: 100%;
  }

  .filter-label {
    flex: 1;
  }

  .filter-count {
    opacity: 0.6;
    font-size: 0.9em;
    margin-left: auto;
    transition: color 0.05s ease-out;
  }
</style>

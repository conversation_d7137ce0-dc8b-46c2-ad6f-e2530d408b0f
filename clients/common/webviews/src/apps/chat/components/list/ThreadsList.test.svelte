<script lang="ts">
  import ThreadsList from "./ThreadsList.svelte";
  import { type ChatModel } from "../../models/chat-model";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { setContext } from "svelte";
  import { SHARED_AGENT_STORE_CONTEXT_KEY } from "$vscode/src/webview-panels/remote-agents/common-webview-store";

  export let chatModel: ChatModel;
  export let remoteAgentsModel: RemoteAgentsModel;
  export let sharedWebviewStore: any;

  // Set up the context for the component
  setContext("chatModel", chatModel);
  setContext(RemoteAgentsModel.key, remoteAgentsModel);
  setContext(SHARED_AGENT_STORE_CONTEXT_KEY, sharedWebviewStore);
</script>

<ThreadsList chatModelProp={chatModel} />

<script lang="ts" context="module">
  export const THREAD_ITEM_TEST_ID = "thread-list-item";
</script>

<script lang="ts">
  import { onMount } from "svelte";

  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import RemoteAgentIcon from "./RemoteAgentIcon.svelte";
  import Terminal from "$common-webviews/src/design-system/icons/augment/terminal.svelte";
  import BranchIcon from "$common-webviews/src/design-system/icons/branch.svelte";
  import ThreadMenu from "./ThreadMenu.svelte";
  import NotifyButton from "$common-webviews/src/apps/chat/components/InputActionBar/NotifyButton.svelte";
  import { type RemoteAgent } from "$vscode/src/remote-agent-manager/types";
  import Bookmark from "$common-webviews/src/design-system/icons/fontawesome/svgs/solid/bookmark.svg?component";
  import { getContext } from "svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import { type ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import type { ChatThread, LocalAgentThread, RemoteAgentThread } from "./ThreadsList.svelte";
  import { NEW_AGENT_KEY } from "../../models/agent-constants";
  import { type ExchangeWithStatus } from "../../types/chat-message";
  import { slideScale } from "./animation-util";
  import StatusIndicator from "$common-webviews/src/apps/remote-agent-manager/components/StatusIndicator.svelte";
  import { getModeDisplayInfo } from "../buttons/mode-display-helper";

  export let thread: ChatThread | LocalAgentThread | RemoteAgentThread;

  export let isSelected: boolean = false;
  export let containerWidth: number = 0;
  export let isExpanded: boolean = false;
  export let afterDelete: (() => void) | undefined = undefined;
  export let onSelect: () => void;
  export let onTogglePinned: (event?: Event) => void;
  export let isFromDifferentRepo: (agent: RemoteAgent) => boolean = () => false;
  export let getRepoInfo: (agent: RemoteAgent) => { name: string; fullPath: string } = () => ({
    name: "Unknown",
    fullPath: "Unknown",
  });
  export let enableShareService: boolean = false;

  // Get the chat model from context
  let chatModel = getContext<ChatModel>("chatModel");

  // State for editing thread title
  let isEditing = false;
  let editedTitle = thread.title;
  let textInput: HTMLInputElement | undefined;

  // Function to handle thread renaming
  function handleRename(newTitle: string) {
    if (newTitle.trim() && newTitle !== thread.title) {
      if (["chat", "localAgent"].includes(thread.type)) {
        // Get the chat model from the shared agent store
        if (chatModel) {
          chatModel.renameConversation(thread.id, newTitle);
          thread.title = newTitle;
        }
      }
    }
    isEditing = false;
  }

  // Function to cancel editing
  function cancelEditing() {
    editedTitle = thread.title;
    isEditing = false;
  }

  // Handle key events in the input field
  function handleKeyDown(e: KeyboardEvent) {
    if (e.key === "Enter") {
      e.preventDefault();
      handleRename(editedTitle);
    } else if (e.key === "Escape") {
      e.preventDefault();
      cancelEditing();
    }
  }

  // Start editing mode when receiving the edit-thread event from ThreadMenu
  function startEditing() {
    isEditing = true;
    editedTitle = thread.title;
    // Focus the input field after the DOM updates
    setTimeout(() => textInput?.focus(), 0);
  }

  // Set up click outside listener
  onMount(() => {
    const handleDocumentClick = (e: MouseEvent) => {
      if (isEditing && textInput && !textInput.contains(e.target as Node)) {
        handleRename(editedTitle);
      }
    };

    document.addEventListener("click", handleDocumentClick);

    return () => {
      document.removeEventListener("click", handleDocumentClick);
    };
  });

  /* eslint-disable @typescript-eslint/naming-convention */
  $: userMessages = (
    getThreadProperty<Array<{ request_message?: string }>>(thread, "conversation.chatHistory") || []
  )
    .map((item) => item.request_message)
    .filter(Boolean);
  /* eslint-enable @typescript-eslint/naming-convention */

  $: isNewThread = thread.isNew;

  // Determine mode display parameters based on thread type
  $: isConversationAgentic = thread.type === "localAgent";
  $: isBackgroundAgent = thread.type === "remoteAgent";
  $: agentExecutionMode = chatModel?.agentExecutionMode;

  // Get mode display info for the correct icon - make the entire function call reactive
  $: modeInfo = getModeDisplayInfo({
    isConversationAgentic,
    agentExecutionMode: agentExecutionMode ? $agentExecutionMode : undefined,
    isBackgroundAgent,
  });

  // Helper function to safely access thread properties
  function getThreadProperty<T>(
    thread: ChatThread | LocalAgentThread | RemoteAgentThread,
    path: string,
  ): T | undefined {
    return path
      .split(".")
      .reduce((obj: any, key) => (obj && obj[key] !== undefined ? obj[key] : undefined), thread) as
      | T
      | undefined;
  }

  function truncate(str = "", maxLength: number) {
    if (str.length <= maxLength) {
      return str;
    }
    return str.slice(0, maxLength - 3) + "...";
  }

  $: type =
    thread.type ||
    (isNewThread
      ? getThreadProperty(thread, "conversation.extraData.isRemoteAgentConversation")
        ? "remoteAgent"
        : "chat"
      : "chat");
  const typeLabels = {
    chat: "Chat",
    localAgent: "Local Agent",
    remoteAgent: "Remote Agent",
  };

  const getFirstMessage = (thread: ChatThread | LocalAgentThread | RemoteAgentThread) => {
    const allMessages = thread?.conversation?.chatHistory || [];
    const firstUserMessage = allMessages.find(
      (item) => (item as ExchangeWithStatus).request_message,
    ) as ExchangeWithStatus;
    if (!firstUserMessage) {
      return "";
    }
    return firstUserMessage.request_message || "";
  };
</script>

<div
  class="agent-row class:agent-row--{thread.type}"
  class:agent-row--active={isSelected}
  class:agent-row--new={isNewThread}
  class:agent-row--setup-script={thread.type === "remoteAgent" && thread.is_setup_script_agent}
  on:click={onSelect}
  on:keydown={(e) => {
    if (e.key === "Enter") {
      onSelect();
    }
  }}
  role="button"
  tabindex="0"
  transition:slideScale|global={{ duration: 150 }}
  data-testid={THREAD_ITEM_TEST_ID}
>
  {#key `${thread.title}--${thread.isPinned}`}
    <div class="agent-content">
      <div class="agent-details">
        <div class="agent-details--left">
          <div class="agent-icon">
            {#if type === "remoteAgent"}
              <RemoteAgentIcon hasUpdates={thread.type === "remoteAgent" && thread.has_updates} />
            {:else}
              <div class="thread-type-icon thread-type-icon--{modeInfo.type}">
                <svelte:component this={modeInfo.icon} />
              </div>
            {/if}
          </div>

          {#if !isNewThread && isExpanded}
            <div class="pin-icon" class:is-pinned={thread.isPinned}>
              <TextTooltipAugment
                nested={false}
                hasPointerEvents={false}
                content={thread.isPinned ? "Unpin thread" : "Pin thread"}
                triggerOn={[TooltipTriggerOn.Hover]}
              >
                <button
                  class="pin-icon__button"
                  on:click={(e) => {
                    e.stopPropagation();
                    onTogglePinned(e);
                  }}
                  on:keydown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.stopPropagation();
                      onTogglePinned(e);
                    }
                  }}
                  type="button"
                  aria-label={thread.isPinned ? "Unpin thread" : "Pin thread"}
                >
                  <Bookmark />
                </button>
              </TextTooltipAugment>
            </div>
          {/if}

          {#if thread.type === "remoteAgent" && thread.agent && isFromDifferentRepo && isFromDifferentRepo(thread.agent)}
            {@const repoInfo = getRepoInfo
              ? getRepoInfo(thread.agent)
              : { name: "Unknown", fullPath: "Unknown" }}
            <TextTooltipAugment
              nested={false}
              hasPointerEvents={false}
              content={`Repository: ${repoInfo.fullPath}`}
              triggerOn={[TooltipTriggerOn.Hover]}
            >
              <div class="repo-pill">
                <div class="repo-pill-icon">
                  <BranchIcon />
                </div>
                <div class="repo-pill-text">
                  {repoInfo.name}
                </div>
              </div>
            </TextTooltipAugment>
          {/if}

          <div class="agent-title">
            <TextTooltipAugment
              nested={false}
              hasPointerEvents={false}
              content={truncate(thread.title, 200)}
              triggerOn={[TooltipTriggerOn.Hover]}
              delayDurationMs={1000}
            >
              <div class="agent-title-content">
                {#if thread.type === "remoteAgent" && thread.is_setup_script_agent}
                  <TextTooltipAugment
                    nested={false}
                    hasPointerEvents={false}
                    triggerOn={[TooltipTriggerOn.Hover]}
                    content="Setup Script Agent"
                  >
                    <div class="setup-script-badge">
                      <Terminal />
                    </div>
                  </TextTooltipAugment>
                {/if}
                {#if isNewThread}
                  <TextTooltipAugment
                    nested={false}
                    hasPointerEvents={false}
                    triggerOn={[TooltipTriggerOn.Hover]}
                    content={getThreadProperty(thread, "conversation.hasDraft")
                      ? "Draft message"
                      : "New thread"}
                  >
                    <div class="draft-badge">
                      <TextAugment size={1}
                        >{getThreadProperty(thread, "conversation.hasDraft")
                          ? "Draft"
                          : "New"}</TextAugment
                      >
                    </div>
                  </TextTooltipAugment>
                {/if}
                {#if thread.type === "remoteAgent" && thread.is_setup_script_agent}
                  <TextAugment size={1} truncate class="setup-script-title"
                    >Generate a setup script</TextAugment
                  >
                {:else if isNewThread}
                  <TextAugment size={1} truncate class="draft-title">
                    {thread.title ||
                      thread.conversation?.draftExchange?.request_message ||
                      "New Thread"}
                  </TextAugment>
                {:else if isEditing && ["chat", "localAgent"].includes(thread.type)}
                  <div class="edit-title-container">
                    <TextFieldAugment
                      bind:textInput
                      bind:value={editedTitle}
                      size={1}
                      variant="surface"
                      on:keydown={handleKeyDown}
                      on:blur={() => handleRename(editedTitle)}
                      class="edit-title-input"
                    />
                  </div>
                {:else}
                  <TextAugment
                    size={1}
                    truncate
                    weight={thread.type === "remoteAgent" && thread.has_updates
                      ? "medium"
                      : "regular"}
                  >
                    {thread.title || getFirstMessage(thread) || `New ${typeLabels[thread.type]}`}
                  </TextAugment>
                {/if}
              </div>
            </TextTooltipAugment>
          </div>
        </div>

        {#if thread.type === "remoteAgent" && thread.status}
          <div class="agent-status">
            <StatusIndicator
              workspaceStatus={thread.workspace_status}
              status={thread.status}
              isExpanded={containerWidth > 330}
            />
          </div>
          <NotifyButton
            agentId={thread.agent.remote_agent_id}
            status={thread.status}
            workspaceStatus={thread.workspace_status}
          />
        {/if}
      </div>

      {#if userMessages.length > 0}
        {#key JSON.stringify(userMessages.slice(-9))}
          <div class="agent-chat-history">
            {#each userMessages.slice(-9) as message, index (message + "-" + index)}
              <TextTooltipAugment
                delayDurationMs={0}
                content={truncate(message || "", 170)}
                side="bottom"
                nested={false}
                hasPointerEvents={false}
                triggerOn={[TooltipTriggerOn.Hover]}
              >
                <div class="agent-chat-history__item">|</div>
              </TextTooltipAugment>
            {/each}
          </div>
        {/key}
      {/if}

      {#if thread.id !== NEW_AGENT_KEY}
        <div class="agent-actions">
          <ThreadMenu {thread} {type} {afterDelete} {enableShareService} onRename={startEditing} />
        </div>
      {/if}
    </div>
  {/key}
</div>

<style>
  .agent-row {
    width: 100%;
    transition: all 0.2s ease;
    flex: none;
    display: flex;
    align-items: center;
    height: 25px;
    padding: 0 var(--ds-spacing-3);
    cursor: pointer;
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
  }

  .agent-row:hover {
    color: var(--ds-color-neutral-12);
  }

  .agent-row--active {
    /* background-color: var(--ds-panel-translucent); */
    background: var(--ds-color-accent-a3);
    color: var(--ds-color-accent-a10);
  }

  .agent-row--active:hover {
    background: var(--ds-color-accent-a3);
    color: var(--ds-color-accent-a10);
  }

  .agent-content {
    display: flex;
    justify-content: space-between;
    width: 100%;
    max-width: 100%;
  }

  .agent-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    min-width: 0;
  }

  .agent-details--left {
    display: inline-flex;
    gap: 6px;
    flex: 1;
    min-width: 0;
  }

  .agent-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: none;
    width: 14px;
  }

  .thread-type-icon {
    flex: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 14px;
  }

  .thread-type-icon--chat {
    width: 12px;
    margin: 0 1px;
  }

  .pin-icon {
    display: flex;
    flex: none;
    align-items: center;
    justify-content: center;
    margin: 0 2px;
    min-width: 0;
  }
  .pin-icon__button {
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    padding: 0;
    cursor: pointer;
    transition: color 0.2s ease;
    color: var(--ds-panel-translucent);
  }

  .pin-icon:hover {
    color: var(--ds-color-neutral-9);
  }

  .pin-icon.is-pinned .pin-icon__button {
    opacity: 1;
    color: var(--ds-color-accent-9);
  }

  .pin-icon :global(svg) {
    width: 12px;
    height: 12px;
  }

  .chat-history-item {
    font-size: 0.7rem;
    white-space: nowrap;
    max-width: 80vw;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .repo-pill {
    display: inline-flex;
    align-items: center;
    flex: none;
    background-color: var(--ds-color-neutral-3);
    color: var(--ds-color-neutral-11);
    border-radius: 12px;
    padding: 0 5px;
    font-size: 0.7em;
    max-width: 80px;
    transition: max-width 0.2s ease-in-out;
    line-height: 1.2;
    vertical-align: middle;
    height: 18px;
  }

  .repo-pill-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 2px;
    color: var(--ds-color-neutral-9);
  }
  .repo-pill-icon :global(svg) {
    width: 11px;
  }

  .repo-pill-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  @media (max-width: 300px) {
    .repo-pill {
      max-width: 19px;
    }
  }

  .repo-pill:hover {
    max-width: 200px;
  }

  .agent-title {
    max-width: 100%;
    min-width: 0;
    flex: 1;
  }

  .agent-title-content {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 5px;
  }

  .agent-title :global(> .l-tooltip-trigger) {
    width: 100%;
    min-width: 0;
  }

  .agent-title-content :global(.c-text) {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
  }

  .agent-title-content :global(.setup-script-title),
  .agent-title-content :global(.draft-title) {
    color: var(--ds-color-neutral-10);
  }

  .setup-script-badge,
  .draft-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--ds-color-neutral-10);
    flex-shrink: 0;
  }

  .setup-script-badge :global(svg) {
    width: 14px;
    height: 14px;
  }

  .draft-badge {
    background-color: var(--ds-color-neutral-3);
    border-radius: 4px;
    padding: 0 6px;
    display: flex;
    align-items: center;
    color: var(--ds-color-accent-10);
    height: 18px;
  }

  .agent-row--new .draft-badge {
    background-color: var(--ds-color-accent-3);
  }

  .draft-title {
    font-style: italic;
  }

  .agent-actions {
    position: relative;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    margin-left: var(--ds-spacing-1);
  }

  .agent-actions :global(.l-tooltip-contents) {
    position: absolute;
    right: 0;
    bottom: 0;
    transform: translate(0, 100%);
    z-index: 10;
  }

  .agent-actions :global(.c-thread-menu__share-link) {
    white-space: nowrap;
  }

  .agent-status {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    margin-left: var(--ds-spacing-1);
  }

  .agent-chat-history {
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    align-items: center;
    color: var(--ds-color-neutral-9);
    margin-left: var(--ds-spacing-3);
  }

  .agent-chat-history :global(.c-text) {
    font-size: 0.7rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
    max-width: 100%;
  }

  .agent-chat-history__item {
    padding: 0 0.5px;
    color: var(--ds-color-neutral-7);
  }

  .edit-title-container {
    flex: 1;
    min-width: 0;
    max-width: 100%;
    margin-left: -8px;
    margin-top: 0.5px;
  }

  .edit-title-container :global(.c-base-text-input) {
    display: block;
  }

  .edit-title-container :global(.edit-title-input) {
    width: 100%;
    padding: 3.5px 0px;
  }
</style>

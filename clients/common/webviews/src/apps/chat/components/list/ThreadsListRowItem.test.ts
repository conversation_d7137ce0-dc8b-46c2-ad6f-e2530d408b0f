import { describe, test, expect, vi } from "vitest";
import ThreadsListRowItem from "./ThreadsListRowItem.svelte";

// Mock the components that are used in ThreadsListRowItem
vi.mock("$common-webviews/src/design-system/components/TextTooltipAugment.svelte", () => ({
  default: {
    render: () => ({
      component: {
        $$: {
          callbacks: {},
        },
      },
    }),
  },
}));

vi.mock("./ThreadMenu.svelte", () => ({
  default: {
    render: () => ({
      component: {
        $$: {
          callbacks: {},
        },
      },
    }),
  },
}));

describe("ThreadsListRowItem.svelte", () => {
  test("component should exist", () => {
    expect(ThreadsListRowItem).toBeDefined();
  });
});

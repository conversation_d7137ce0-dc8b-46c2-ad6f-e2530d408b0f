<script lang="ts">
  import RichTextEditorAugment from "$common-webviews/src/design-system/components/RichTextEditorAugment";
  import Placeholder from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Placeholder";
  import { getContext, tick } from "svelte";
  import type { ChatModel } from "../../models/chat-model";
  import type { IChatMentionable } from "../../types/mention-option";
  import ContextBar from "../context/ContextBar.svelte";
  import InputActionBar from "../InputActionBar";
  import Keybindings from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Keybindings/Keybindings.svelte";
  import AtMentions from "./AtMentions.svelte";
  import type { JSONContent } from "@tiptap/core";
  import type { ContentData } from "$common-webviews/src/design-system/components/RichTextEditorAugment/types";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import RegularPaperPlaneTopIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/paper-plane-top.svg?component";

  import type RichTextEditorRoot from "$common-webviews/src/design-system/components/RichTextEditorAugment/Root.svelte";
  import Expand from "$common-webviews/src/design-system/icons/augment/expand.svelte";
  import Image from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Image";
  import DropZone from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/DropZone/DropZone.svelte";
  import { ImageController } from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Image/controller";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";

  // Get chat model from context
  const chatModel: ChatModel = getContext("chatModel");
  $: currentConversationModel = $chatModel.currentConversationModel;

  const remoteAgentModel: RemoteAgentsModel = getContext(RemoteAgentsModel.key);
  $: isRemoteAgent = !!remoteAgentModel?.isActive;

  // Props
  export let requestId: string | undefined = undefined;
  export let placeholder = "Edit your message...";
  export let content: string | JSONContent | JSONContent[];
  export let collapsed: boolean = false;
  export let onSubmitEdit: (newContent: ContentData, mentions: IChatMentionable[]) => void;
  export let onCancelEdit: () => void;
  export let setIsCollapsed: (isCollapsed: boolean) => void;
  export let userExpanded: boolean;

  // Controls API
  export const requestStartEdit = () => void startEdit();

  // ==== Reactive state ====
  // Reported content from the editor. If editable is false,
  // draftContents should always be undefined
  let isEditing: boolean = false;
  let draftContents: ContentData | undefined = undefined;
  let mentionNodes: IChatMentionable[] = [];
  $: canUserEdit = $chatModel.flags.enableEditableHistory && !isRemoteAgent;
  $: canSubmitEdit =
    isEditing &&
    canUserEdit &&
    draftContents !== undefined &&
    draftContents.rawText.trim() !== "" &&
    draftContents.rawText !== content &&
    draftContents.richTextJsonRepr !== content &&
    !$currentConversationModel.awaitingReply &&
    !ImageController.hasLoadingImages(draftContents.richTextJsonRepr);
  // Any time controlled content changes, cancel the edit

  // ==== Hooks to modify state ====
  // ==== Hooks to modify state ====
  async function startEdit(): Promise<void> {
    if (canUserEdit) {
      userExpanded = true;
      if (collapsed) {
        setIsCollapsed(false);
      }

      isEditing = true;
      await tick();
      forceEditorFocus();
    }
  }

  function submitEdit(): boolean {
    if (!canSubmitEdit || !draftContents) {
      return false;
    }
    onSubmitEdit(draftContents, mentionNodes);
    return true;
  }

  function cancelEdit(): boolean {
    userExpanded = false;
    isEditing = false;
    draftContents = undefined;
    onCancelEdit();
    return true;
  }

  function onContentChanged(data: ContentData) {
    if (data !== draftContents) {
      draftContents = data;
    }
  }

  function onMentionItemsUpdated(data: {
    added: IChatMentionable[];
    removed: IChatMentionable[];
    current: IChatMentionable[];
  }) {
    mentionNodes = data.current;
  }

  // Define keyboard shortcuts active during editing
  $: shortcuts = {
    /* eslint-disable @typescript-eslint/naming-convention */
    Enter: submitEdit,
    Escape: cancelEdit,
    /* eslint-enable @typescript-eslint/naming-convention */
  };

  // Hooks provided by children that we bind to
  let atMentionsComponent: AtMentions | undefined = undefined;
  let richTextEditorRoot: RichTextEditorRoot | undefined = undefined;
  const requestEditorFocus = () => richTextEditorRoot?.requestFocus();
  const forceEditorFocus = () => richTextEditorRoot?.forceFocus();

  let editorContainer: HTMLElement;

  export function getEditorContainer(): HTMLElement {
    return editorContainer;
  }
</script>

<svelte:window on:mousedown={cancelEdit} />
<div
  class="c-chat-input"
  role="button"
  tabindex="-1"
  class:is-collapsed={collapsed}
  class:is-editing={isEditing}
  bind:this={editorContainer}
  on:mousedown|stopPropagation
>
  <RichTextEditorAugment.Root
    editable={isEditing}
    bind:this={richTextEditorRoot}
    on:click={(e) => e.stopPropagation()}
    on:dblclick={startEdit}
  >
    <svelte:fragment slot="header">
      {#if isEditing}
        <!-- Header -->
        <ContextBar {chatModel} />
      {/if}
    </svelte:fragment>
    {#if $chatModel.flags.enableChatMultimodal}
      {#if isEditing}
        <!-- Do not allow the user to drop images into the user message unless
         the user is editing this message -->
        <DropZone />
      {/if}
      <Image
        changeImageMode={(updatedContent) => {
          if (requestId && updatedContent) {
            $currentConversationModel.updateChatItem(requestId, {
              // eslint-disable-next-line @typescript-eslint/naming-convention
              rich_text_json_repr: updatedContent,
            });
          }
        }}
        saveImage={$chatModel.saveImage}
        deleteImage={$chatModel.deleteImage}
        renderImage={$chatModel.renderImage}
        isEditable={() => isEditing}
      />
    {/if}
    <!-- Main content and plugins -->
    <Keybindings {shortcuts} />
    <AtMentions bind:this={atMentionsComponent} {requestEditorFocus} {onMentionItemsUpdated} />
    <Placeholder {placeholder} />
    <RichTextEditorAugment.Content
      content={draftContents?.richTextJsonRepr ?? content}
      {onContentChanged}
    />

    <svelte:fragment slot="footer">
      {#if isEditing}
        <!-- Footer -->
        <InputActionBar.Root>
          <InputActionBar.ContextMenu
            slot="leftAlign"
            onCloseDropdown={requestEditorFocus}
            onInsertMentionable={atMentionsComponent?.insertMentionNode}
          />
          <svelte:fragment slot="rightAlign">
            <ButtonAugment size={1} variant="solid" disabled={!canSubmitEdit} on:click={submitEdit}>
              <RegularPaperPlaneTopIcon />
            </ButtonAugment>
          </svelte:fragment>
        </InputActionBar.Root>
      {/if}

      {#if collapsed}
        <div class="c-user-msg__collapse-button">
          <ButtonAugment
            variant="solid"
            color="neutral"
            size={1}
            on:click={() => {
              userExpanded = true;
              setIsCollapsed(false);
            }}
          >
            <Expand slot="iconLeft" />
            <span>Expand</span>
          </ButtonAugment>
        </div>
      {/if}
    </svelte:fragment>
    <slot />
  </RichTextEditorAugment.Root>
</div>

<style>
  :root {
    --expand-button-height: 24px;
    /* must match COLLAPSED_MAX_HEIGHT in UserMessage.svelte */
    --chat-input-collapsed-height: 120px;
  }

  .c-user-msg__collapse-button {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: var(--ds-spacing-1);
  }

  .c-user-msg__collapse-button :global(.c-base-btn) {
    width: 100%;
  }

  .c-chat-input {
    position: relative;
  }

  .c-chat-input.is-editing {
    min-width: 120px;
  }

  .c-chat-input.is-collapsed {
    --max-height: var(--chat-input-collapsed-height);
  }

  .c-chat-input.is-collapsed :global(.l-rich-text-editor-augment) {
    max-height: var(--chat-input-collapsed-height);
    overflow: hidden;
  }
</style>

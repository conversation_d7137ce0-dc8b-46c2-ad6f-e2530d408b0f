<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let totalAddedLines: number = 0;
  export let totalRemovedLines: number = 0;
</script>

<div class="edit-item__changes">
  {#if totalAddedLines > 0}
    <TextAugment class="edit-item__added-lines" size={1}>
      +{totalAddedLines}
    </TextAugment>
  {/if}
  {#if totalRemovedLines > 0}
    <TextAugment class="edit-item__removed-lines" size={1}>
      -{totalRemovedLines}
    </TextAugment>
  {/if}
</div>

<style>
  .edit-item__changes {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    min-width: fit-content;
    gap: var(--ds-spacing-1);
    padding: 0 var(--ds-spacing-1);

    & .edit-item__added-lines {
      color: var(--ds-color-success-7);
    }

    & .edit-item__removed-lines {
      color: var(--ds-color-error-7);
    }
  }
</style>

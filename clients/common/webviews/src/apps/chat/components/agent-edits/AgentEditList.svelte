<script lang="ts" context="module">
  export function formatTimestamp(timestamp?: number): string {
    if (!timestamp) return "";
    if (timestamp === Number.MAX_SAFE_INTEGER) return "Now";

    const date = new Date(timestamp);
    const now = new Date();
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const ampm = hours >= 12 ? "PM" : "AM";
    const hours12 = hours % 12 || 12;
    const timeStr = `${hours12}:${minutes}${ampm}`;

    // Calculate day difference
    const dayDiff = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (dayDiff === 0) return timeStr;
    if (dayDiff === 1) return `Yesterday at ${timeStr}`;
    return `${dayDiff} days ago at ${timeStr}`;
  }
</script>

<script lang="ts">
  import { getContext } from "svelte";
  import type { ChatModel } from "../../models/chat-model";
  import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
  import { type AgentConversationModel } from "../../models/agent-conversation-model";
  import AgentEditBottomControls from "./AgentEditBottomControls.svelte";
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  // TODO(Eric): Re-enable the checkpoint counter
  // import CheckpointCounter from "./CheckpointCounter.svelte";
  import AgentEditListItem from "./AgentEditListItem.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import type { CheckpointStore } from "../../models/checkpoint-store";
  import AgentEditListHeader from "./AgentEditListHeader.svelte";
  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../../models/task-store";

  // Get required context models
  const chatModel = getContext<ChatModel>("chatModel");
  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");
  const checkpointStore = getContext<CheckpointStore>("checkpointStore");
  const taskStore = getContext<ICurrentConversationTaskStore>(CurrentConversationTaskStore.key);

  // Extract required reactive properties
  const {
    targetCheckpointSummary,
    totalCheckpointCount,
    targetCheckpoint,
    targetCheckpointHasChanges,
  } = checkpointStore;
  const { isCurrConversationAgentic } = agentConversationModel;
  const { canShowTaskList } = taskStore;

  // Compute files that have actual changes (added or removed lines)
  $: filesWithEditsToShow = $targetCheckpointSummary.filter(
    (file) =>
      file.changesSummary &&
      (file.changesSummary.totalAddedLines > 0 || file.changesSummary.totalRemovedLines > 0),
  );
  $: diffSummary = filesWithEditsToShow.reduce(
    (acc, file) => {
      acc.totalAddedLines += file.changesSummary?.totalAddedLines ?? 0;
      acc.totalRemovedLines += file.changesSummary?.totalRemovedLines ?? 0;
      return acc;
    },
    { totalAddedLines: 0, totalRemovedLines: 0 },
  );

  // State for collapsible UI. Keep chat model notified of changes
  $: collapsed = $chatModel.isPanelCollapsed;

  function handleFileClick(qualifiedPathName: IQualifiedPathName) {
    chatModel.extensionClient.openFile({
      repoRoot: qualifiedPathName.rootPath,
      pathName: qualifiedPathName.relPath,
      allowOutOfWorkspace: true,
    });
  }

  function handleReviewClick(qualifiedPathName: IQualifiedPathName) {
    // Use the conversation's baseline timestamp from extraData instead of 0
    const baselineTimestamp = agentConversationModel.getBaselineTimestamp();
    void chatModel.extensionClient.showAgentReview(
      qualifiedPathName,
      baselineTimestamp,
      $targetCheckpoint?.toTimestamp,
      false, // explicitly set retainFocus to false for View Diff button
    );
  }

  function handleRevertClick(qualifiedPathName: IQualifiedPathName) {
    // Use the conversation's baseline timestamp from extraData instead of 0
    const baselineTimestamp = agentConversationModel.getBaselineTimestamp();
    void checkpointStore.revertDocumentToTimestamp(qualifiedPathName, baselineTimestamp);
  }

  async function handleRejectAll() {
    await checkpointStore.rejectAll();
  }

  async function handleAcceptAll() {
    await checkpointStore.acceptAll();
  }
</script>

{#if ($isCurrConversationAgentic && $totalCheckpointCount > 0 && filesWithEditsToShow.length > 0) || $canShowTaskList}
  <div class="c-agent-edits-container">
    <CollapsibleAugment
      class="c-agent-edits-container__block-container"
      bind:collapsed={$collapsed}
    >
      <AgentEditListHeader
        filesCount={filesWithEditsToShow.length}
        {diffSummary}
        collapsed={$collapsed}
        hasChanges={$targetCheckpointHasChanges}
        onRevert={handleRejectAll}
        onKeep={handleAcceptAll}
        slot="header"
      />
      <!-- TODO(Eric): Re-enable the checkpoint counter -->
      <!-- <CheckpointCounter
        totalCheckpoints={$totalCheckpointCount}
        bind:currentCheckpoint={$targetCheckpointIdx}
      /> -->

      {#if filesWithEditsToShow.length > 0}
        <div class="c-edits-list">
          <!-- Single file changes section -->
          <div class="c-edits-section">
            {#each filesWithEditsToShow as file}
              <AgentEditListItem
                qualifiedPathName={file.qualifiedPathName}
                lineChanges={file.changesSummary}
                onClickFile={() => handleFileClick(file.qualifiedPathName)}
                onClickReview={() => handleReviewClick(file.qualifiedPathName)}
                onClickRevert={() => handleRevertClick(file.qualifiedPathName)}
              />
            {/each}
          </div>
        </div>
      {:else}
        <div class="c-edits-list c-edits-list--empty">
          <TextAugment size={1} color="neutral">No changes to show</TextAugment>
        </div>
      {/if}
      <AgentEditBottomControls slot="footer" />
    </CollapsibleAugment>
  </div>
{/if}

<style>
  .c-agent-edits-container {
    max-width: 100%;
    --agent-edits-list-max-height: min(400px, 30vh);
  }

  .c-edits-list {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    padding: var(--ds-spacing-1);
    max-height: var(--agent-edits-list-max-height);
    overflow-y: auto;

    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: var(--ds-scrollbar-thumb) var(--ds-scrollbar-track);
  }

  /* Webkit scrollbar styling */
  .c-edits-list::-webkit-scrollbar {
    width: 8px;
  }

  .c-edits-list::-webkit-scrollbar-track {
    background: var(--ds-scrollbar-track, transparent);
  }

  .c-edits-list::-webkit-scrollbar-thumb {
    background-color: var(--ds-scrollbar-thumb, rgba(255, 255, 255, 0.2));
    border-radius: 4px;
  }

  .c-edits-list--empty {
    opacity: 50%;

    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--ds-spacing-2);
    max-height: var(--agent-edits-list-max-height);

    /* Disable selection */
    user-select: none;
  }

  .c-edits-section {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }
</style>

<script lang="ts">
  import { getContext } from "svelte";

  import CollapseButtonAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapseButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import EditChangeSummary from "./EditChangeSummary.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import KeepAll from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/check-double.svg?component";
  import DiscardAll from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-turn-left.svg?component";
  import SwitchModeButton from "../buttons/SwitchModeButton.svelte";
  import { type ChatModel } from "../../models/chat-model";

  // Props
  export let filesCount: number;
  export let diffSummary: { totalAddedLines: number; totalRemovedLines: number };
  export let collapsed: boolean;
  export let hasChanges: boolean;
  export let onRevert: (() => void) | undefined = undefined;
  export let onKeep: (() => void) | undefined = undefined;

  const chatModel = getContext<ChatModel>("chatModel");
  const flagsModel = chatModel.flags;
</script>

<div class="c-agent-edits-header">
  <div class="c-agent-edits-header__content">
    <div class="c-agent-edits-header__collapse-btn">
      <CollapseButtonAugment />
    </div>
    {#if $flagsModel.enableTaskList}
      <div class="c-agent-edits-header__switch-btn">
        <SwitchModeButton />
      </div>
    {/if}
    <div class="c-agent-edits-header__info-container">
      <slot name="text">
        <TextAugment size={1} color="neutral"
          >{filesCount} file{filesCount === 1 ? "" : "s"} changed</TextAugment
        >
      </slot>
    </div>
    {#if hasChanges}
      <div class="c-agent-edits-header__summary">
        <EditChangeSummary
          totalAddedLines={diffSummary.totalAddedLines}
          totalRemovedLines={diffSummary.totalRemovedLines}
        />
      </div>
    {/if}
  </div>
  {#if (collapsed && hasChanges && (onRevert || onKeep)) || $$slots.actions}
    <div class="c-agent-edits-header__actions">
      {#if onRevert}
        <ButtonAugment
          class="c-agent-edits-header__revert-btn"
          variant="soft"
          color="neutral"
          size={1}
          on:click={onRevert}
        >
          <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} content="Discard all changes">
            <DiscardAll />
            <span class="c-agent-edits-header__revert-btn__text"> Discard All </span>
          </TextTooltipAugment>
        </ButtonAugment>
      {/if}
      {#if onKeep}
        <ButtonAugment
          class="c-agent-edits-header__accept-btn"
          variant="soft"
          color="success"
          size={1}
          on:click={onKeep}
        >
          <TextTooltipAugment
            triggerOn={[TooltipTriggerOn.Hover]}
            content="Keep all changes and clear checkpoints"
          >
            <KeepAll />
            <span class="c-agent-edits-header__accept-btn__text"> Keep All </span>
          </TextTooltipAugment>
        </ButtonAugment>
      {/if}
      <slot name="actions" />
    </div>
  {/if}
</div>

<style>
  .c-agent-edits-header {
    display: flex;
    align-items: center;
    width: 100%;
    min-width: 0;
    max-width: 100%;
    overflow: hidden;

    padding: var(--ds-spacing-1);
    gap: var(--ds-spacing-2);
    justify-content: space-between;

    & .c-agent-edits-header__content {
      display: flex;
      gap: var(--ds-spacing-1);
      min-width: 0;
      max-width: 100%;
      overflow: hidden;
      flex: 1 1 auto;

      /* Vertically center the content */
      align-items: center;

      & .c-agent-edits-header__collapse-btn,
      & .c-agent-edits-header__switch-btn {
        flex-shrink: 0;
      }

      & .c-agent-edits-header__info-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: var(--ds-spacing-2);
        min-width: 0;
        flex: 1 1 auto;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-left: var(--ds-spacing-1);
      }

      & .c-agent-edits-header__summary {
        flex-shrink: 0;
      }
    }

    & .c-agent-edits-header__actions {
      display: flex;
      align-items: center;
      gap: var(--ds-spacing-1);
      min-width: max-content;
      flex-shrink: 0;

      & .c-agent-edits-header__revert-btn,
      & .c-agent-edits-header__accept-btn {
        & .l-tooltip-trigger {
          /* Vertically center */
          display: flex;
          align-items: center;
          justify-content: center;
          gap: var(--ds-spacing-1);
        }
      }

      /* Hide icons when width is small (360px or less) */
      @media (min-width: 300px) and (max-width: 360px) {
        /* Hide non-text */
        & .l-tooltip-trigger svg {
          display: none;
        }
      }

      /* Hide button text when width is very small (300px or less) */
      @media (max-width: 300px) {
        /* Show icons */
        & .c-button--content {
          /* Reduce padding to make buttons smaller */
          padding: var(--ds-spacing-1);
          /* Style for only icons */
          height: var(--ds-spacing-5);
          width: var(--ds-spacing-5);
        }

        /* Hide the text */
        & .c-agent-edits-header__revert-btn__text,
        & .c-agent-edits-header__accept-btn__text {
          display: none;
        }
      }
    }
  }
</style>

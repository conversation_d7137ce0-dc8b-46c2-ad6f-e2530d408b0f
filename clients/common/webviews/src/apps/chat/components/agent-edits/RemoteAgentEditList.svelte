<script lang="ts">
  import { getContext } from "svelte";
  import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
  import { type ChatAgentFileChangeSummary } from "$vscode/src/webview-providers/webview-messages";
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import AgentEditListItem from "./AgentEditListItem.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import AgentEditListHeader from "./AgentEditListHeader.svelte";
  import type { ChangedFile } from "$vscode/src/remote-agent-manager/types";
  import { DiffOperations } from "../../../remote-agent-manager/utils/diff";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  import type { ChatModel } from "../../models/chat-model";
  import VsCodeCodicon from "$common-webviews/src/common/components/vscode/VSCodeCodicon.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/chevron-down.svelte";
  import ChevronUp from "$common-webviews/src/design-system/icons/chevron-up.svelte";
  import { slide } from "svelte/transition";

  // Props
  export let changedFiles: ChangedFile[] = [];
  export let collapsed: boolean = false;
  export let focusedFilePath: string | null = null;
  export let emptyMessage: string = "No changes to show";
  export let onFileClick: ((filePath: string) => void) | undefined = undefined;
  export let onReviewClick: ((file: ChangedFile) => void) | undefined = undefined;
  export let onRevertClick: ((file: ChangedFile) => void) | undefined = undefined;
  export let onOpenDiffView: (() => void) | undefined = undefined;

  // Get messenger from context
  const chatModel = getContext<ChatModel>("chatModel");

  // Show more/less functionality
  const MAX_FILES_TO_SHOW = 3;
  let showAllFiles = false;
  $: hasMoreFiles = filesWithEditsToShow.length > MAX_FILES_TO_SHOW;

  function toggleShowAllFiles() {
    showAllFiles = !showAllFiles;
  }

  // Function to toggle collapse of unchanged regions in diff editor
  function toggleCollapseUnchangedRegions() {
    chatModel.extensionClient.toggleCollapseUnchangedRegions();
  }

  // Process changed files to get file changes with line counts
  $: processedFiles = changedFiles.map((file) => {
    // Generate a diff for the file
    const diff = DiffOperations.generateDiff(
      file.old_path,
      file.new_path,
      file.old_contents || "",
      file.new_contents || "",
    );

    // Get the diff stats
    const stats = DiffOperations.getDiffObjectStats(diff);

    // Create a file change summary
    const changesSummary: ChatAgentFileChangeSummary = {
      totalAddedLines: stats.additions,
      totalRemovedLines: stats.deletions,
    };

    // Create a qualified path name
    const qualifiedPathName: IQualifiedPathName = {
      rootPath: "", // This would need to be set by the parent if needed
      relPath: file.new_path || file.old_path,
    };

    return {
      file,
      qualifiedPathName,
      changesSummary,
    };
  });

  // Filter files that have actual changes
  $: filesWithEditsToShow = processedFiles.filter(
    (item) => item.changesSummary.totalAddedLines > 0 || item.changesSummary.totalRemovedLines > 0,
  );

  // Calculate summary statistics
  $: diffSummary = filesWithEditsToShow.reduce(
    (acc, item) => {
      acc.totalAddedLines += item.changesSummary.totalAddedLines;
      acc.totalRemovedLines += item.changesSummary.totalRemovedLines;
      return acc;
    },
    { totalAddedLines: 0, totalRemovedLines: 0 },
  );
</script>

{#if filesWithEditsToShow.length > 0}
  <div class="c-agent-edits-container">
    <CollapsibleAugment class="c-agent-edits-container__block-container" bind:collapsed>
      <AgentEditListHeader
        filesCount={filesWithEditsToShow.length}
        {diffSummary}
        {collapsed}
        hasChanges={true}
        slot="header"
      >
        <div class="c-agent-edits-header__actions" slot="text">
          <div class="c-agent-edits-header__actions__count">
            {filesWithEditsToShow.length} file{filesWithEditsToShow.length === 1 ? "" : "s"} changed
          </div>
          <div class="c-agent-edits-header__buttons">
            <ButtonAugment on:click={onOpenDiffView} color="neutral" size={1} variant="soft">
              <div class="c-agent-edits-header__buttons__text--long">See full diff</div>
              <div class="c-agent-edits-header__buttons__text--short">
                <VsCodeCodicon icon="diff-multiple" />
              </div>
            </ButtonAugment>
          </div>
        </div>
      </AgentEditListHeader>

      <div class="c-edits-list">
        <!-- Single file changes section -->
        <div class="c-edits-section">
          {#each showAllFiles ? filesWithEditsToShow : filesWithEditsToShow.slice(0, MAX_FILES_TO_SHOW) as item}
            {@const isFocused =
              focusedFilePath === item.file.new_path || focusedFilePath === item.file.old_path}
            <div class="c-edit" transition:slide>
              <AgentEditListItem
                qualifiedPathName={item.qualifiedPathName}
                lineChanges={item.changesSummary}
                {isFocused}
                onClickFile={onFileClick
                  ? () => onFileClick(item.file.new_path || item.file.old_path)
                  : undefined}
                onClickReview={onReviewClick ? () => onReviewClick(item.file) : undefined}
                onClickRevert={onRevertClick ? () => onRevertClick(item.file) : undefined}
              >
                <div slot="actions">
                  {#if isFocused}
                    <TextTooltipAugment
                      content="Toggle collapse unchanged regions"
                      triggerOn={[TooltipTriggerOn.Hover]}
                    >
                      <IconButtonAugment
                        on:click={toggleCollapseUnchangedRegions}
                        color="neutral"
                        size={1}
                        variant="soft"
                      >
                        <VsCodeCodicon icon="map" />
                      </IconButtonAugment>
                    </TextTooltipAugment>
                  {/if}
                </div>
              </AgentEditListItem>
            </div>
          {/each}

          {#if hasMoreFiles}
            <div class="c-show-more-container">
              <ButtonAugment on:click={toggleShowAllFiles} color="neutral" size={1} variant="soft">
                {#if showAllFiles}
                  <span>Show less</span>
                  <ChevronUp slot="iconRight" />
                {:else}
                  <span>Show {filesWithEditsToShow.length - MAX_FILES_TO_SHOW} more files</span>
                  <ChevronDown slot="iconRight" />
                {/if}
              </ButtonAugment>
            </div>
          {/if}
        </div>
      </div>
    </CollapsibleAugment>
  </div>
{:else}
  <div class="c-edits-list c-edits-list--empty">
    <TextAugment size={1} color="neutral">{emptyMessage}</TextAugment>
  </div>
{/if}

<style>
  .c-agent-edits-container {
    max-width: 100%;
    --agent-edits-list-max-height: min(400px, 30vh);
  }

  .c-edits-list {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    padding: var(--ds-spacing-1);
    max-height: var(--agent-edits-list-max-height);
    overflow-y: auto;

    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: var(--ds-scrollbar-thumb) var(--ds-scrollbar-track);
  }

  /* Webkit scrollbar styling */
  .c-edits-list::-webkit-scrollbar {
    width: 8px;
  }

  .c-edits-list::-webkit-scrollbar-track {
    background: var(--ds-scrollbar-track, transparent);
  }

  .c-edits-list::-webkit-scrollbar-thumb {
    background-color: var(--ds-scrollbar-thumb, rgba(255, 255, 255, 0.2));
    border-radius: 4px;
  }

  .c-edits-list--empty {
    opacity: 50%;

    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--ds-spacing-3);
    /* Keep max-height consistent with parent but adjust for empty state */
    min-height: 100px;
    max-height: var(--agent-edits-list-max-height);

    /* Disable selection */
    user-select: none;
  }

  .c-edits-section {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }

  .c-agent-edits-header__actions {
    flex: 1;
    display: flex;
    justify-content: space-between;
    gap: var(--ds-spacing-1);
  }

  .c-agent-edits-header__buttons {
    display: flex;
    gap: var(--ds-spacing-1);
  }
  .c-agent-edits-header__buttons__text--long {
    display: inline;
  }

  .c-agent-edits-header__buttons__text--short {
    display: none;
  }

  @media (max-width: 300px) {
    .c-agent-edits-header__buttons__text--long {
      display: none;
    }
    .c-agent-edits-header__buttons__text--short {
      display: inline;
    }
  }

  /* Style for the toggle folds button */
  :global(.c-agent-edits-header__buttons .c-button--content) {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .c-show-more-container {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .c-show-more-container :global(button) {
    width: 100%;
    min-width: 120px;
  }
</style>

/**
 * Tracks scroll velocity to optimize loading behavior in virtualized lists.
 *
 * This utility:
 * - Tracks scroll positions with timestamps
 * - Calculates instantaneous and average velocity
 * - Detects scroll direction and momentum
 * - Provides prediction of future scroll positions
 */
export interface ScrollSample {
  position: number;
  timestamp: number;
}

export class ScrollVelocityTracker {
  private samples: ScrollSample[] = [];
  private readonly maxSamples: number;

  /**
   * Creates a new ScrollVelocityTracker
   * @param maxSamples Maximum number of samples to keep (default: 10)
   */
  constructor(maxSamples = 10) {
    this.maxSamples = maxSamples;
  }

  /**
   * Adds a new scroll position sample
   * @param position Current scroll position (typically scrollTop)
   * @param timestamp Optional timestamp (defaults to performance.now())
   */
  addSample(position: number, timestamp = performance.now()): void {
    this.samples.push({ position, timestamp });

    // Keep only the most recent samples
    if (this.samples.length > this.maxSamples) {
      this.samples.shift();
    }
  }

  /**
   * Gets the current scroll velocity in pixels per millisecond
   * @returns Velocity (positive = scrolling down, negative = scrolling up)
   */
  getVelocity(): number {
    if (this.samples.length < 2) return 0;

    // Calculate velocity using the most recent samples
    const newest = this.samples.at(-1)!;
    const oldest = this.samples.at(0)!;

    const positionDelta = newest.position - oldest.position;
    const timeDelta = newest.timestamp - oldest.timestamp;

    // Return pixels per millisecond
    return timeDelta > 0 ? positionDelta / timeDelta : 0;
  }

  /**
   * Gets the current scroll velocity in pixels per second
   * @returns Velocity in pixels per second
   */
  getVelocityPPS(): number {
    return this.getVelocity() * 1000;
  }

  /**
   * Determines if the current scroll is considered "fast"
   * @param threshold Velocity threshold in pixels per second (default: 500)
   * @returns True if the scroll is faster than the threshold
   */
  isFastScroll(threshold = 500): boolean {
    return Math.abs(this.getVelocityPPS()) > threshold;
  }

  /**
   * Gets the current scroll direction
   * @returns Direction as 'up', 'down', or 'static'
   */
  getDirection(): "up" | "down" | "static" {
    const velocity = this.getVelocity();
    if (velocity > 0.05) return "down";
    if (velocity < -0.05) return "up";
    return "static";
  }

  /**
   * Predicts the scroll position after a specified time
   * @param ms Time in milliseconds
   * @returns Predicted scroll position
   */
  predictPositionAfter(ms: number): number {
    if (this.samples.length === 0) return 0;

    const velocity = this.getVelocity();
    const currentPosition = this.samples[this.samples.length - 1].position;

    return currentPosition + velocity * ms;
  }

  /**
   * Clears all stored samples
   */
  reset(): void {
    this.samples = [];
  }

  /**
   * Gets the most recent scroll position
   * @returns The most recent position or 0 if no samples
   */
  getCurrentPosition(): number {
    if (this.samples.length === 0) return 0;
    return this.samples[this.samples.length - 1].position;
  }
}

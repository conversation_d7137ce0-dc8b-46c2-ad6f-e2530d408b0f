import { describe, test, expect, vi, beforeEach } from "vitest";
import { ScrollPositionManager } from "./scrollPositionManager";

describe("ScrollPositionManager", () => {
  let scrollPositionManager: ScrollPositionManager;
  let mockScrollContainer: HTMLElement;

  beforeEach(() => {
    scrollPositionManager = new ScrollPositionManager();

    // Create a mock scroll container
    mockScrollContainer = {
      scrollHeight: 1000,
      scrollTop: 200,
      clientHeight: 500,
      getBoundingClientRect: () => ({
        top: 100,
        bottom: 600,
        height: 500,
        width: 800,
        left: 0,
        right: 800,
        x: 0,
        y: 100,
        toJSON: () => ({}),
      }),
      querySelector: vi.fn(),
      querySelectorAll: vi.fn(),
      scroll: vi.fn(),
      scrollTo: vi.fn(),
    } as unknown as HTMLElement;
  });

  test("should capture scroll position correctly", () => {
    const position = scrollPositionManager.captureScrollPosition(mockScrollContainer);

    expect(position).toEqual({
      scrollHeight: 1000,
      scrollTop: 200,
      clientHeight: 500,
      distanceFromBottom: 300, // 1000 - 200 - 500
      timestamp: expect.any(Number),
    });
  });

  test("should restore position using distance from bottom", () => {
    const position = {
      scrollHeight: 1000,
      scrollTop: 200,
      clientHeight: 500,
      distanceFromBottom: 300,
      timestamp: performance.now(),
    };

    // Mock a container that has grown in height
    const newMockContainer = {
      ...mockScrollContainer,
      scrollHeight: 1200, // Height increased by 200px
      scrollTop: 200, // This will be updated by the restore function
    } as unknown as HTMLElement;

    scrollPositionManager.restoreScrollPosition(newMockContainer, position);

    // New scrollTop should maintain the same distance from bottom:
    // newScrollTop = newScrollHeight - distanceFromBottom - clientHeight
    // newScrollTop = 1200 - 300 - 500 = 400
    expect(newMockContainer.scrollTop).toBe(400);
  });

  test("should handle edge case with small scroll container", () => {
    const position = {
      scrollHeight: 1000,
      scrollTop: 200,
      clientHeight: 500,
      distanceFromBottom: 300,
      timestamp: performance.now(),
    };

    // Mock a container that has shrunk in height
    const smallMockContainer = {
      ...mockScrollContainer,
      scrollHeight: 400, // Height decreased to 400px
      scrollTop: 200, // This will be updated by the restore function
    } as unknown as HTMLElement;

    scrollPositionManager.restoreScrollPosition(smallMockContainer, position);

    // New scrollTop calculation would be: 400 - 300 - 500 = -400
    // But we clamp to 0 to prevent negative scrollTop
    expect(smallMockContainer.scrollTop).toBe(0);
  });
});

/**
 * Manages scroll position preservation during dynamic content loading.
 *
 * This utility:
 * - Captures scroll state including position and velocity
 * - Restores scroll position after content changes
 * - Provides distance-based anchoring for stable positioning
 * - Integrates with velocity data for predictive loading
 */
import { distanceFromBottom } from "../../utils/scroll-utils";

/**
 * Information about the captured scroll state
 */
export interface ScrollPositionInfo {
  /** Scroll height at time of capture */
  scrollHeight: number;
  /** Scroll top at time of capture */
  scrollTop: number;
  /** Client height at time of capture */
  clientHeight: number;
  /** Distance from bottom at time of capture */
  distanceFromBottom: number;
  /** Timestamp of the capture */
  timestamp: number;
}

export class ScrollPositionManager {
  /**
   * Captures the current scroll position state
   *
   * @param scrollContainer The scrollable container element
   * @returns Captured scroll position information
   */
  captureScrollPosition(scrollContainer: HTMLElement): ScrollPositionInfo {
    const { scrollHeight, scrollTop, clientHeight } = scrollContainer;
    const distFromBottom = distanceFromBottom(scrollContainer);

    return {
      scrollHeight,
      scrollTop,
      clientHeight,
      distanceFromBottom: distFromBottom,
      timestamp: performance.now(),
    };
  }

  /**
   * Restores scroll position using distance from bottom
   *
   * @param scrollContainer The scrollable container element
   * @param positionInfo Previously captured position information
   */
  public restoreScrollPosition(
    scrollContainer: HTMLElement,
    positionInfo: ScrollPositionInfo,
  ): void {
    // Calculate new scroll position to maintain the same distance from bottom
    const newScrollTop =
      scrollContainer.scrollHeight - positionInfo.distanceFromBottom - scrollContainer.clientHeight;

    // Apply the new scroll position
    scrollContainer.scrollTop = Math.max(0, newScrollTop);
  }
}

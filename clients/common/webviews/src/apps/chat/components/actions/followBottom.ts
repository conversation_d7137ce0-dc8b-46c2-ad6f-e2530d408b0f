import { throttle } from "lodash";
import scroll from "scroll";

export interface ScrollToOptions {
  topBuffer?: number;
  smooth?: boolean;
  scrollDuration?: number;
  bottom?: boolean;
  disableScrollUp?: boolean;
  disableScrollDown?: boolean;
  onScrollFinish?: () => void;
}

export interface FollowBottomProps extends ScrollToOptions {
  follow: boolean;
  scrollContainer?: HTMLElement | undefined;
}

/**
 * A svelte action that follows an element as it changes size.
 *
 * @param element
 * @param props
 * @returns
 */
export function followElement(element: HTMLElement, props: FollowBottomProps) {
  let currProps: FollowBottomProps = props;
  let cancelCurrAutoScroll: (() => void) | undefined = undefined;

  // Tries to scroll to the current element, reading from `currProps`
  const tryScrollToElement = throttle(
    () => {
      if (currProps.follow && currProps.scrollContainer) {
        cancelCurrAutoScroll?.();
        cancelCurrAutoScroll = scrollTo(currProps.scrollContainer, element, currProps);
      }
    },
    300,
    { leading: true, trailing: true },
  );

  // Starts following the element immediately
  const follow = () => {
    resizeObserver.observe(element);
    tryScrollToElement();
  };

  // Stops following the element immediately
  const unfollow = () => {
    cancelCurrAutoScroll?.();
    resizeObserver.disconnect();
  };

  // Create a ResizeObserver to track the current element as it changes size
  const resizeObserver: ResizeObserver = new ResizeObserver(tryScrollToElement);
  if (currProps.follow) {
    resizeObserver.observe(element);
    tryScrollToElement();
  }

  return {
    update: (props: FollowBottomProps) => {
      const prevProps = currProps;
      currProps = props;
      if (prevProps.follow !== currProps.follow) {
        props.follow ? follow() : unfollow();
      }
    },
    destroy: unfollow,
  };
}

/**
 * Computes the scrollY value needed to scroll the element into view
 *
 * @param element: target element to scroll to
 * @param options: scroll options
 * @returns: the scrollY value to scroll the element into view
 */
const SCROLL_DURATION = 350;
function computeScrollToY(element: HTMLElement, options: ScrollToOptions = {}): number {
  const { bottom, topBuffer } = options;
  const offsetBuffer = topBuffer ?? 0;
  if (bottom) {
    return element.offsetTop + element.offsetHeight + offsetBuffer;
  } else {
    return element.offsetTop + offsetBuffer;
  }
}

/**
 * Computes whether or not it is possible to scroll to a particular element
 * given the constraints
 *
 * @param scrollContainer
 * @param element
 * @param options
 * @returns
 */
export function canScrollTo(
  scrollContainer: HTMLElement,
  element: HTMLElement,
  options: ScrollToOptions = {},
): boolean {
  const { disableScrollUp, disableScrollDown } = options;
  const scrollToY = computeScrollToY(element, options);

  if (disableScrollUp && scrollToY < scrollContainer.scrollTop) {
    return false;
  } else if (disableScrollDown && scrollToY > scrollContainer.scrollTop) {
    return false;
  }

  return true;
}

/**
 * Scrolls to the last turn in the scrollContainer element.
 * If smooth is true, the scroll will be animated and return a cancel function.
 *
 * @param scrollContainer: The scrollable element containing the target element
 * @param element: The target element to scroll to
 * @param options: Configuration options for the scroll
 * @returns: A cancel function if smooth is true
 */
export function scrollTo(
  scrollContainer: HTMLElement,
  element: HTMLElement,
  options: ScrollToOptions = {},
): (() => void) | undefined {
  if (!canScrollTo(scrollContainer, element, options)) {
    return;
  }

  const scrollY = computeScrollToY(element, options);
  return scrollToY(scrollContainer, scrollY, options);
}

export function scrollToTop(scrollContainer: HTMLElement, options: ScrollToOptions = {}) {
  return scrollToY(scrollContainer, 0, options);
}

export function scrollToBottom(scrollContainer: HTMLElement, options: ScrollToOptions = {}) {
  return scrollToY(scrollContainer, scrollContainer.scrollHeight, options);
}

function scrollToY(
  scrollContainer: HTMLElement,
  scrollY: number,
  options: ScrollToOptions = {},
): (() => void) | undefined {
  // If smooth is true, return the cancel function
  if (options.smooth) {
    return scroll.top(
      scrollContainer,
      scrollY,
      { duration: options.scrollDuration ?? SCROLL_DURATION },
      options.onScrollFinish,
    );
  } else {
    scrollContainer.scroll({ top: scrollY, behavior: "instant" });
    options.onScrollFinish?.();
  }
}

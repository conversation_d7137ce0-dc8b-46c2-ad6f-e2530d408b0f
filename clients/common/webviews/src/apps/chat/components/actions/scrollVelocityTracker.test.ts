import { describe, test, expect, beforeEach } from "vitest";
import { ScrollVelocityTracker } from "./scrollVelocityTracker";

describe("ScrollVelocityTracker", () => {
  let tracker: ScrollVelocityTracker;

  beforeEach(() => {
    tracker = new ScrollVelocityTracker(5); // Use smaller sample size for testing
  });

  test("should initialize with empty samples", () => {
    expect(tracker.getVelocity()).toBe(0);
    expect(tracker.getCurrentPosition()).toBe(0);
  });

  test("should calculate velocity correctly", () => {
    // Add samples with 100ms intervals and 10px movements
    tracker.addSample(0, 0);
    tracker.addSample(10, 100);

    // 10px / 100ms = 0.1px/ms
    expect(tracker.getVelocity()).toBeCloseTo(0.1);
    expect(tracker.getVelocityPPS()).toBeCloseTo(100);
  });

  test("should detect scroll direction", () => {
    // Scrolling down
    tracker.addSample(0, 0);
    tracker.addSample(10, 100);
    expect(tracker.getDirection()).toBe("down");

    // Reset and test scrolling up
    tracker.reset();
    tracker.addSample(100, 0);
    tracker.addSample(50, 100);
    expect(tracker.getDirection()).toBe("up");

    // Reset and test static
    tracker.reset();
    tracker.addSample(100, 0);
    tracker.addSample(100, 100);
    expect(tracker.getDirection()).toBe("static");
  });

  test("should detect fast scrolling", () => {
    // Fast scroll (600px/s)
    tracker.addSample(0, 0);
    tracker.addSample(60, 100);
    expect(tracker.isFastScroll(500)).toBe(true);

    // Slow scroll (200px/s)
    tracker.reset();
    tracker.addSample(0, 0);
    tracker.addSample(20, 100);
    expect(tracker.isFastScroll(500)).toBe(false);
  });

  test("should predict future position", () => {
    // Scrolling at 100px/s (0.1px/ms)
    tracker.addSample(0, 0);
    tracker.addSample(10, 100);

    // Predict 100ms into the future: current (10) + velocity (0.1) * time (100) = 20
    expect(tracker.predictPositionAfter(100)).toBeCloseTo(20);
  });

  test("should limit the number of samples", () => {
    // Add more samples than the limit (5)
    for (let i = 0; i < 10; i++) {
      tracker.addSample(i * 10, i * 100);
    }

    // Should only use the 5 most recent samples for velocity calculation
    // Latest sample should be at position 90
    expect(tracker.getCurrentPosition()).toBe(90);

    // Velocity should be calculated from samples 5-9 (positions 50-90)
    // (90 - 50) / (900 - 500) = 40 / 400 = 0.1px/ms
    expect(tracker.getVelocity()).toBeCloseTo(0.1);
  });

  test("should handle reset", () => {
    tracker.addSample(0, 0);
    tracker.addSample(10, 100);
    expect(tracker.getVelocity()).not.toBe(0);

    tracker.reset();
    expect(tracker.getVelocity()).toBe(0);
    expect(tracker.getCurrentPosition()).toBe(0);
  });
});

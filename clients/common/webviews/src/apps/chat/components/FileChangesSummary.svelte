<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import MagnifyingGlass from "$common-webviews/src/design-system/icons/magnifying-glass.svelte";
  import { getContext, onMount } from "svelte";
  import { RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";
  import type { ChangedFile } from "$vscode/src/remote-agent-manager/types";
  import EditChangeSummary from "./agent-edits/EditChangeSummary.svelte";
  import {
    getAggregateChanges,
    getUserMessagePrecedingTurn,
  } from "../../remote-agent-manager/utils";
  import { applyPreparedChanges } from "../../remote-agent-manager/utils/index";
  import { DiffOperations } from "../../remote-agent-manager/utils/diff";
  import { RemoteAgentDiffOpsModel } from "../../remote-agent-manager/models/ra-diff-ops-model";
  import { SELECTED_TURN_INDEX_CONTEXT_KEY } from "$common-webviews/src/apps/remote-agent-manager/models/message-render-options";
  import { writable, type Writable } from "svelte/store";
  import { isAgentFromDifferentRepoStore } from "../../remote-agent-manager/utils/repository-utils";
  import { GitReferenceModel } from "../../remote-agent-manager/models/git-reference-model";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";

  export let hasActions: boolean = false;
  export let changedFiles: ChangedFile[] | undefined = undefined;
  export let canMakePR: boolean = false;
  export let suffix: string = "";
  export let turnIndex: number | undefined = -1;

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const diffOperationsModel = getContext<RemoteAgentDiffOpsModel>(RemoteAgentDiffOpsModel.key);
  const selectedTurnIndex = getContext<Writable<number>>(SELECTED_TURN_INDEX_CONTEXT_KEY);
  const gitReferenceModel = getContext<GitReferenceModel>(GitReferenceModel.key);

  // Track whether changes have been applied
  let hasApplied = false;
  let isCreatingPR = false;

  const currentRepoUrl = writable<string>("");
  const isAgentFromDifferentRepo = isAgentFromDifferentRepoStore(currentRepoUrl);

  onMount(async () => {
    try {
      const { remoteUrl, error: remoteUrlError } = await gitReferenceModel.getRemoteUrl();
      if (remoteUrlError) {
        console.error("Failed to get remote url:", remoteUrlError);
        return;
      }
      currentRepoUrl.set(remoteUrl);
    } catch (error) {
      console.error("Failed to get current repository URL:", error);
    }
  });

  function handleCreatePR(): void {
    if (!$remoteAgentsModel.currentAgent?.remote_agent_id) {
      console.error("No current agent ID found");
      return;
    }

    isCreatingPR = true;

    // Send a message to the current agent asking it to create a PR
    $remoteAgentsModel
      .sendMessage("Please create a pull request with the changes you've made.")
      .then(() => {
        isCreatingPR = false;
      })
      .catch((error) => {
        console.error("Error sending PR creation request:", error);
        isCreatingPR = false;
      });
  }

  /** True if we cannot apply locally. This is when if the remote agent is on a different repository */
  $: applyLocallyDisabled =
    !$remoteAgentsModel.currentAgent || $isAgentFromDifferentRepo($remoteAgentsModel.currentAgent);

  function handleApplyLocally(): void {
    if (applyLocallyDisabled) {
      console.error("Cannot apply changes locally from a different repository");
      return;
    }

    let filesToProcess: ChangedFile[] = [];

    // Use provided changedFiles if available, otherwise get aggregate changes
    if (changedFiles) {
      filesToProcess = changedFiles;
    } else {
      filesToProcess = getAggregateChanges($remoteAgentsModel.currentConversation?.exchanges ?? []);
    }

    // For FileChangesSummary, we don't have diffExplanation, so we'll create a simpler version
    // that works directly with changedFiles
    const filesToApply = filesToProcess
      .map((file) => ({
        path: file.new_path || file.old_path,
        originalCode: file.old_contents || "",
        newCode: file.new_contents || "",
      }))
      .filter((file) => file.path && file.newCode);

    // Apply all changes locally using the shared helper function
    applyPreparedChanges(filesToApply, async (path, originalCode, newCode) => {
      await diffOperationsModel.applyChanges(path, originalCode, newCode);
    });

    // Mark changes as applied
    hasApplied = true;
  }

  $: isActive =
    $remoteAgentsModel.isDiffPanelOpen &&
    // Only show active if the diff panel is open for the current agent
    $remoteAgentsModel.currentAgentId === $remoteAgentsModel.diffPanelAgentId &&
    // For aggregate view (no turnIndex specified)
    ((turnIndex === undefined && $selectedTurnIndex === -1) ||
      // For specific turn view
      (turnIndex !== undefined && $selectedTurnIndex === turnIndex));

  function handleViewChanges(): void {
    if (isActive) {
      $remoteAgentsModel.closeRemoteAgentDiffPanel();
      // Reset the selected turn index when closing the panel
      selectedTurnIndex.set(-1);
      return;
    }

    let filesToShow: ChangedFile[] = [];
    let userPrompt = "";
    let isAggregate = true;
    let turnIdx = -1;

    // Use provided changedFiles if available, otherwise get aggregate changes
    if (changedFiles) {
      filesToShow = changedFiles;

      // Use the provided turnIndex if available
      if (turnIndex !== undefined) {
        turnIdx = turnIndex;
        // If we're showing specific turn changes, we're not showing aggregate changes
        isAggregate = false;
        // Update the selected turn index in the context
        selectedTurnIndex.set(turnIndex);
        userPrompt = getUserMessagePrecedingTurn(
          $remoteAgentsModel.currentConversation?.exchanges ?? [],
          turnIndex,
        );
      } else {
        // For changes without a turnIndex, we're still showing aggregate changes
        isAggregate = true;
        // For aggregate changes, set the selected turn index to -1
        selectedTurnIndex.set(-1);
        // For specific turn changes without a turnIndex, use the most recent user prompt
        userPrompt = getUserMessagePrecedingTurn(
          $remoteAgentsModel.currentConversation?.exchanges ?? [],
          1,
        );
      }
    } else {
      filesToShow = getAggregateChanges($remoteAgentsModel.currentConversation?.exchanges ?? []);
      // We're showing aggregate changes
      isAggregate = true;
      // Set the selected turn index to -1 for aggregate changes
      selectedTurnIndex.set(-1);
      // Get the most recent user prompt for aggregate view
      userPrompt = getUserMessagePrecedingTurn(
        $remoteAgentsModel.currentConversation?.exchanges ?? [],
        1,
      );
    }

    // Make sure we're using the correct turn index
    const currentTurnIdx = turnIdx;

    // Show the remote agent diff panel with the changes
    $remoteAgentsModel.showRemoteAgentDiffPanel({
      turnIdx: currentTurnIdx,
      changedFiles: filesToShow,
      userPrompt: userPrompt,
      sessionSummary: $remoteAgentsModel.currentAgent?.session_summary ?? "",
      isShowingAggregateChanges: isAggregate,
      isAgentFromDifferentRepo:
        !!$remoteAgentsModel.currentAgent &&
        $isAgentFromDifferentRepo($remoteAgentsModel.currentAgent),
    });
  }

  // Calculate summary statistics
  $: filesToShow =
    changedFiles || getAggregateChanges($remoteAgentsModel.currentConversation?.exchanges ?? []);

  $: totalFiles = filesToShow.length;

  // Calculate diff stats for all files
  $: diffStats = filesToShow.map((file) => {
    // Generate a diff for the file
    const diff = DiffOperations.generateDiff(
      file.old_path,
      file.new_path,
      file.old_contents || "",
      file.new_contents || "",
    );
    // Get the diff stats
    return DiffOperations.getDiffObjectStats(diff);
  });

  // Calculate total additions
  $: totalAddedLines = diffStats.reduce((sum, stats) => sum + stats.additions, 0);

  // Calculate total deletions
  $: totalRemovedLines = diffStats.reduce((sum, stats) => sum + stats.deletions, 0);

  $: hasChanges = totalFiles > 0;
</script>

{#if hasChanges}
  <div class="file-changes-summary" class:file-changes-summary--has-actions={hasActions}>
    <div class="file-changes-header">
      <div
        class="file-changes-info"
        on:click={handleViewChanges}
        on:keydown={(e) => e.key === "Enter" && handleViewChanges()}
        role="button"
        tabindex="0"
        class:active={isActive}
      >
        <div class="file-changes-info__text">
          <TextAugment size={1} weight="medium" truncate>
            {totalFiles} file{totalFiles === 1 ? "" : "s"} changed{suffix}
          </TextAugment>
          <EditChangeSummary {totalAddedLines} {totalRemovedLines} />
        </div>
        <MagnifyingGlass />
      </div>
    </div>

    {#if hasActions}
      <div class="file-changes-actions">
        <TextTooltipAugment
          content={applyLocallyDisabled
            ? "Cannot apply changes from a different repository locally"
            : "Apply changes locally"}
        >
          <ButtonAugment
            variant="ghost"
            color="neutral"
            size={1}
            on:click={handleApplyLocally}
            disabled={hasApplied || applyLocallyDisabled}
          >
            {#if hasApplied}
              Applied
            {:else}
              Apply locally
            {/if}
          </ButtonAugment>
        </TextTooltipAugment>

        {#if canMakePR}
          <ButtonAugment
            variant="ghost"
            color="neutral"
            size={1}
            on:click={handleCreatePR}
            disabled={isCreatingPR}
          >
            {#if isCreatingPR}
              Creating PR...
            {:else}
              Create PR
            {/if}
          </ButtonAugment>
        {/if}
      </div>
    {/if}
  </div>
{/if}

<style>
  .file-changes-summary {
    display: flex;
    flex-direction: column;
    background-color: var(--ds-color-neutral-1);
    border-radius: var(--ds-radius-4);
    padding: 0;
    gap: 2px;
    width: 100%;
  }
  .file-changes-summary--has-actions {
    padding: var(--ds-spacing-1);
  }

  .file-changes-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: var(--ds-panel-solid);
    border-radius: var(--ds-radius-3);
  }

  :global(.light) .file-changes-summary {
    background-color: var(--ds-panel-solid);
  }

  :global(.light) .file-changes-header {
    background-color: var(--ds-color-neutral-2);
  }

  .file-changes-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: var(--ds-spacing-2);
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: var(--ds-radius-3);
    padding: calc(var(--ds-spacing-1) * 1.3) var(--ds-spacing-2);

    &:hover {
      border-color: var(--ds-color-accent-a6);
      background-color: var(--ds-color-accent-a2);
    }

    &:focus-visible {
      outline: none;
      border-color: var(--ds-color-accent-a8);
      box-shadow: 0 0 0 2px var(--ds-color-accent-a4);
    }
  }

  .file-changes-info__text {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .file-changes-actions {
    display: flex;
    gap: var(--ds-spacing-1);
    padding: 0 1px;
  }

  .file-changes-info.active {
    border-color: var(--ds-color-accent-a6);
    background-color: var(--ds-color-accent-a2);
  }

  @media (max-width: 250px) {
    .file-changes-header {
      padding: var(--ds-spacing-2);
    }

    .file-changes-info {
      flex-direction: column;
      align-items: flex-start;
    }

    .file-changes-actions {
      flex-direction: column;
    }
  }
</style>

<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  // Props
  export let title: string = "";
  export let isSquished: boolean = false;

  // Expose window height binding to parent components
  let windowHeight = 0;
  $: isWindowSquished = windowHeight < 700;
  $: effectiveIsSquished = isSquished || isWindowSquished;
</script>

<svelte:window bind:innerHeight={windowHeight} />

<div class="c-thread-header">
  <!-- Banner slot for status information (e.g., "Running in the cloud") -->
  <div class="c-thread-header-banner">
    <slot name="banner">
      <!-- Default banner content -->
    </slot>
  </div>

  <!-- Title area -->
  <div class="c-thread-title">
    <slot name="title">
      <TextAugment size={1} weight="bold" truncate class="c-thread-title__text">{title}</TextAugment
      >
    </slot>
  </div>

  <!-- Info row for metadata (e.g., repository, branch) -->
  {#if !effectiveIsSquished}
    <div class="c-thread-info-row">
      <slot name="info-row">
        <!-- Default info row content -->
      </slot>
    </div>
  {/if}

  <!-- Status area for file changes summary or other status information -->
  <div class="c-thread-header-status">
    <slot name="status">
      <!-- Default status content -->
    </slot>
  </div>
</div>

<style lang="css">
  .c-thread-header {
    position: relative;
    display: flex;
    flex-direction: column;
    margin-left: calc(0px - var(--chat-padding));
    margin-right: calc(0px - var(--chat-padding));
    padding-bottom: var(--ds-spacing-2);
    width: 100dvw;
    transition: background-color 0.15s ease;
  }

  .c-thread-header-banner {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background-color: var(--ds-color-neutral-2);
    padding: 0.5px var(--ds-spacing-3) 0.5px var(--ds-spacing-3);
    color: var(--vscode-descriptionForeground);
    font-size: 0.66rem;
    gap: var(--ds-spacing-2);
    transition: background-color 0.15s ease;
  }

  .c-thread-title {
    padding: 8px var(--ds-spacing-3) 2px;
    margin-bottom: 4px;
  }

  .c-thread-title :global(.c-thread-title__text) {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .c-thread-info-row {
    width: 100%;
    padding: 4px var(--ds-spacing-2) 0;
    font-size: 0.7rem;
    color: var(--vscode-descriptionForeground);
    margin-bottom: var(--ds-spacing-2);
  }
  .c-thread-info-row :global(> div) {
    display: flex;
    width: 100%;
    gap: 8px;
  }

  @media (max-height: 700px) {
    .c-thread-info-row {
      display: none;
    }
  }

  @media (max-width: 250px) {
    .c-thread-info-row {
      flex-direction: column;
      gap: 2px;
    }
  }

  .c-thread-header-status {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-thread-header-status:empty {
    margin-bottom: calc(0px - var(--ds-spacing-2));
  }

  .c-thread-header-status:not(:empty) {
    padding: 0 var(--ds-spacing-2);
  }
</style>

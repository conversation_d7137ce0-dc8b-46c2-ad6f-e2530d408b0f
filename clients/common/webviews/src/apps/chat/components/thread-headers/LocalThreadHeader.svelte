<script lang="ts">
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import { getContext, onMount } from "svelte";
  import { type ChatModel } from "../../models/chat-model";
  import BaseThreadHeader from "./BaseThreadHeader.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { type ExchangeWithStatus } from "../../types/chat-message";

  // Get the chat model from context
  const chatModel = getContext<ChatModel>("chatModel");
  $: conversationModel = $chatModel.currentConversationModel;

  $: conversationId = $chatModel.currentConversationId || "";
  $: isLocalAgent = conversationModel?.extraData?.isAgentConversation === true;

  // Get the current conversation
  $: conversation = $chatModel.conversations[conversationId];
  $: title = conversation?.name || getFirstMessage() || `New ${isLocalAgent ? "Agent" : "Chat"}`;

  // State for editing thread title
  let isEditing = false;
  $: editedTitle = title;
  let textInput: HTMLInputElement | undefined;

  // Function to handle thread renaming
  function handleRename(newTitle: string) {
    if (newTitle.trim() && newTitle !== title) {
      chatModel.renameConversation(conversationId, newTitle);
    }
    isEditing = false;
  }

  // Function to cancel editing
  function cancelEditing() {
    editedTitle = title;
    isEditing = false;
  }

  // Handle key events in the input field
  function handleKeyDown(e: KeyboardEvent) {
    if (e.key === "Enter") {
      e.preventDefault();
      handleRename(editedTitle);
    } else if (e.key === "Escape") {
      e.preventDefault();
      cancelEditing();
    }
  }

  // Start editing mode when receiving the edit-thread event from ThreadMenu
  function startEditing() {
    isEditing = true;
    editedTitle = title;
    // Focus the input field after the DOM updates
    setTimeout(() => textInput?.focus(), 0);
  }

  // Set up click outside listener
  onMount(() => {
    const handleDocumentClick = (e: MouseEvent) => {
      if (isEditing && textInput && !textInput.contains(e.target as Node)) {
        handleRename(editedTitle);
      }
    };

    document.addEventListener("click", handleDocumentClick);

    return () => {
      document.removeEventListener("click", handleDocumentClick);
    };
  });

  const getFirstMessage = () => {
    const firstUserMessage = (conversation?.chatHistory || []).find(
      (item) => (item as ExchangeWithStatus).request_message,
    ) as ExchangeWithStatus;
    if (!firstUserMessage) {
      return "";
    }
    return firstUserMessage.request_message || "";
  };
</script>

<BaseThreadHeader>
  <div slot="title">
    <div class="edit-title-container" class:edit-title-container--isEditing={isEditing}>
      {#if isEditing}
        <div class="edit-title-input">
          <TextFieldAugment
            bind:textInput
            bind:value={editedTitle}
            size={2}
            variant="surface"
            on:keydown={handleKeyDown}
            on:blur={() => handleRename(editedTitle)}
          />
        </div>
      {/if}
      <div class="edit-title-text" on:dblclick={startEditing} role="button" tabindex="-1">
        <TextAugment size={1} weight="bold">{title}</TextAugment>
      </div>
    </div>
  </div>
</BaseThreadHeader>

<style>
  .edit-title-container {
    position: relative;
    flex: 1;
    min-width: 0;
    max-width: 100%;
    padding-bottom: 8px;
    margin-bottom: -32px;
  }

  .edit-title-input {
    width: 100%;
    position: absolute;
    top: -8px;
    left: -8px;
    right: 0;
  }

  .edit-title-input :global(input) {
    font-weight: 500 !important;
  }

  .edit-title-container--isEditing :global(.edit-title-text) {
    opacity: 0;
  }

  .edit-title-text :global(.c-text) {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>

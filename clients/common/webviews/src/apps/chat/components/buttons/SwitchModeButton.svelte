<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import { getContext } from "svelte";
  import {
    SWAPPABLE_PANEL_CONTEXT_KEY,
    type SwappablePanelContext,
    PanelType,
  } from "../SwappablePanel.svelte";

  // Import icons for tasks and code changes
  import TaskListIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/list-check.svg?component";
  import DiffIcon from "$common-webviews/src/design-system/icons/diff.svelte";
  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../../models/task-store";

  // Get the swappable panel context
  const swappablePanelContext = getContext<SwappablePanelContext | undefined>(
    SWAPPABLE_PANEL_CONTEXT_KEY,
  );
  const taskStore = getContext<ICurrentConversationTaskStore | undefined>(
    CurrentConversationTaskStore.key,
  );
  const activePanel = swappablePanelContext?.activePanel;

  // Determine the tooltip text based on the current active panel
  $: tooltipText =
    $activePanel === PanelType.agentEdits ? "Switch to Tasks view" : "Switch to Changes view";

  // Handle panel toggle with task upgrade if needed
  async function togglePanel() {
    swappablePanelContext?.togglePanel();
  }
</script>

{#if swappablePanelContext && taskStore}
  <TextTooltipAugment content={tooltipText} triggerOn={[TooltipTriggerOn.Hover]} side="top">
    <div class="c-switch-mode-button">
      <div class="c-switch-mode-button__group">
        <ButtonAugment
          variant={$activePanel === PanelType.tasks ? "soft" : "ghost"}
          color={$activePanel === PanelType.tasks ? "accent" : "neutral"}
          size={1}
          class="c-switch-mode-button__tasks-btn"
          on:click={togglePanel}
        >
          <div class="c-switch-mode-button__content">
            <TaskListIcon />
          </div>
        </ButtonAugment>

        <ButtonAugment
          variant={$activePanel === PanelType.agentEdits ? "soft" : "ghost"}
          color={$activePanel === PanelType.agentEdits ? "accent" : "neutral"}
          size={1}
          class="c-switch-mode-button__changes-btn"
          on:click={togglePanel}
        >
          <div class="c-switch-mode-button__content">
            <DiffIcon />
          </div>
        </ButtonAugment>
      </div>
    </div>
  </TextTooltipAugment>
{/if}

<style>
  .c-switch-mode-button {
    display: flex;
    align-items: center;
  }

  .c-switch-mode-button__group {
    display: inline-flex;
    background-color: var(--ds-color-neutral-a3);
    border-radius: var(--ds-radius-2);
    padding: calc(0.25 * var(--ds-spacing-1));
    overflow: hidden;
    gap: calc(0.25 * var(--ds-spacing-1));
  }

  /* Style for the buttons */
  :global(.c-switch-mode-button__tasks-btn),
  :global(.c-switch-mode-button__changes-btn) {
    border-radius: var(--ds-radius-1);
    transition: all 0.2s ease-in-out;
  }

  /* Active button styling */
  :global(.c-switch-mode-button__tasks-btn.c-base-btn--variant-soft),
  :global(.c-switch-mode-button__changes-btn.c-base-btn--variant-soft) {
    background-color: var(--ds-color-neutral-2);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    min-width: 80px;
    font-weight: 500;
  }

  /* Inactive button styling */
  :global(.c-switch-mode-button__tasks-btn.c-base-btn--variant-ghost),
  :global(.c-switch-mode-button__changes-btn.c-base-btn--variant-ghost) {
    min-width: 32px;
    width: 32px;
    padding-left: var(--ds-spacing-1);
    padding-right: var(--ds-spacing-1);
  }

  /* Content layout */
  .c-switch-mode-button__content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--ds-spacing-1);
    white-space: nowrap;
  }

  /* Ensure icons have consistent size */
  .c-switch-mode-button__content :global(svg) {
    width: 14px;
    height: 14px;
    flex-shrink: 0;
  }
</style>

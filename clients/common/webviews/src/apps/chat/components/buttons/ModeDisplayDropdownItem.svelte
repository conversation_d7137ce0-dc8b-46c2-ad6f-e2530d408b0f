<script lang="ts">
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import { type AgentExecutionMode } from "../../models/chat-model";
  import ModeDisplay from "./ModeDisplay.svelte";

  export let onSelect: (e: Event) => void;
  export let description: string = "";
  export let isConversationAgentic: boolean = false;
  export let agentExecutionMode: AgentExecutionMode | undefined = undefined;
  export let selected: boolean = false;
  export let isBackgroundAgent: boolean = false;
  export let willStartNewThread: boolean = false;

  $: ({ class: restClassName = "", ...restProps } = $$restProps);
</script>

<div
  class="c-mode-display-item-container {restClassName}"
  class:c-mode-display-item--selected={selected}
>
  <DropdownMenu.Item
    onSelect={(e) => {
      onSelect(e);
    }}
    {...restProps}
  >
    <div class="c-mode-display-item">
      <ModeDisplay
        {isConversationAgentic}
        {agentExecutionMode}
        {isBackgroundAgent}
        {willStartNewThread}
        {description}
        {selected}
      />
    </div>
  </DropdownMenu.Item>
</div>

<style>
  .c-mode-display-item-container,
  .c-mode-display-item,
  .c-mode-display-item-container :global(.l-tooltip-trigger),
  .c-mode-display-item-container :global(.c-base-btn) {
    width: 100%;
  }
  .c-mode-display-item--selected :global(.c-base-btn--ghost) {
    --base-btn-bg-color: var(--ds-color-a3);
  }
</style>

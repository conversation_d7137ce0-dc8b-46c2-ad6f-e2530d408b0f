import RegularChatIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/message.svg?component";
import RegularCloudIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/cloud.svg?component";
import RegularUserIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/user.svg?component";
import { AgentExecutionMode } from "../../models/chat-model";

export interface ModeDisplayParams {
  isConversationAgentic: boolean;
  agentExecutionMode: AgentExecutionMode | undefined;
  isBackgroundAgent: boolean;
}

export interface ModeDisplayInfo {
  icon: any; // Svelte component
  type: ChatModeType;
  primaryText: string;
  secondaryText?: string;
  bannerText: string;
}

export type ChatModeType = "chat" | "localAgent" | "remoteAgent";

/**
 * Helper function to determine the appropriate icon and text for a given mode configuration.
 * This centralizes the logic that was previously duplicated across components.
 */
export function getModeDisplayInfo(params: ModeDisplayParams): ModeDisplayInfo {
  const { isConversationAgentic, agentExecutionMode, isBackgroundAgent } = params;

  if (isBackgroundAgent) {
    return {
      icon: RegularCloudIcon,
      type: "remoteAgent",
      primaryText: "Remote Agent",
      bannerText: "Running in the cloud",
    };
  }

  if (!isConversationAgentic) {
    return {
      icon: RegularChatIcon,
      type: "chat",
      primaryText: "Chat",
      bannerText: "Running locally",
    };
  }

  if (agentExecutionMode === AgentExecutionMode.manual) {
    return {
      icon: RegularUserIcon,
      type: "localAgent",
      primaryText: "Agent",
      bannerText: "Running locally",
    };
  }

  if (agentExecutionMode === AgentExecutionMode.auto) {
    return {
      icon: RegularUserIcon,
      type: "localAgent",
      primaryText: "Agent",
      bannerText: "Running locally",
    };
  }

  return {
    icon: RegularUserIcon,
    type: "localAgent",
    primaryText: "Agent",
    bannerText: "Running locally",
  };
}

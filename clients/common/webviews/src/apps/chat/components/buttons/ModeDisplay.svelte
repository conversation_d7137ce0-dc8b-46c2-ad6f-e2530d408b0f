<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { type AgentExecutionMode } from "../../models/chat-model";
  import Plus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";
  import Check from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/check.svg?component";
  import { getModeDisplayInfo } from "./mode-display-helper";

  export let isConversationAgentic: boolean = false;
  export let agentExecutionMode: AgentExecutionMode | undefined = undefined;
  export let isBackgroundAgent: boolean = false;
  export let willStartNewThread: boolean = false;
  export let description: string = "";
  export let doShowSecondaryText: boolean = true;
  export let selected: boolean = false;

  $: modeInfo = getModeDisplayInfo({
    isConversationAgentic,
    agentExecutionMode,
    isBackgroundAgent,
  });
</script>

<div class="c-mode-display">
  <div class="c-mode-display__top">
    <svelte:component this={modeInfo.icon} />
    <TextAugment size={1}>{modeInfo.primaryText}</TextAugment>
    {#if modeInfo.secondaryText}
      <TextAugment size={1} class="c-mode-secondary-label">{modeInfo.secondaryText}</TextAugment>
    {/if}

    {#if willStartNewThread}
      <div class="c-mode-right-label">
        <span class="c-mode-right-label__text"> Creates new thread </span>
        <Plus />
      </div>
    {:else if selected}
      <div class="c-mode-checkmark">
        <Check />
      </div>
    {/if}
  </div>
  {#if description}
    <div class="c-mode-display__bottom" class:c-mode-display__bottom--hidden={!doShowSecondaryText}>
      <TextAugment size={1} class="c-mode-secondary-label">{description}</TextAugment>
    </div>
  {/if}
</div>

<style>
  .c-mode-display {
    text-wrap: nowrap;
  }

  .c-mode-display__top {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-mode-display__top :global(svg) {
    flex: none;
  }

  .c-mode-display__bottom {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-2);
    padding-left: var(--ds-spacing-5);
  }
  .c-mode-display__bottom--hidden {
    height: 0;
    overflow: hidden;
  }

  .c-mode-display :global(.c-mode-secondary-label) {
    /* We can use currentColor with opacity to get 50% of the parent's text color */
    color: color-mix(in srgb, currentColor 60%, transparent);
  }
  .c-mode-right-label {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    flex: 1;
    min-width: 0;
  }
  .c-mode-right-label :global(svg) {
    color: color-mix(in srgb, currentColor 60%, transparent);
    width: 12px;
    height: 12px;
  }
  .c-mode-display :global(.c-mode-right-label) {
    display: flex;
    margin-left: auto;
    flex: 1;
    justify-content: flex-end;
  }
  .c-mode-right-label__text {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    opacity: 0;
    transform: translateX(3px);
    transition: all 0.2s ease-in-out;
    color: color-mix(in srgb, currentColor 60%, transparent);
    padding-left: var(--ds-spacing-5);
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: right;
  }
  :global(.c-base-btn--solid:hover .c-mode-right-label__text) {
    opacity: 1;
    transform: translateX(0);
  }
  .c-mode-checkmark {
    display: flex;
    align-items: center;
    margin-left: auto;
    flex-shrink: 0;
  }
</style>

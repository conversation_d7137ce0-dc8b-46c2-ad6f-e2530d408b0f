<script lang="ts">
  import { setContext } from "svelte";
  import UserMessage from "./UserMessage.svelte";
  import type { ChatModel } from "../../models/chat-model";
  import { writable } from "svelte/store";

  export let chatModel: ChatModel;
  export let msg: string;
  export let requestId: string | undefined = undefined;
  export let richTextJsonRepr: any = undefined;
  export let onStartEdit: () => void = () => {};
  export let onAcceptEdit: () => void = () => {};
  export let onCancelEdit: () => void = () => {};

  const mockAgentConversationModel = {
    isCurrConversationAgentic: writable<boolean>(false),
    distillMemory: () => {},
  };

  setContext("chatModel", chatModel);
  setContext("agentConversationModel", mockAgentConversationModel);
</script>

<UserMessage
  {chatModel}
  {msg}
  {requestId}
  {richTextJsonRepr}
  {onStartEdit}
  {onAcceptEdit}
  {onCancelEdit}
/>

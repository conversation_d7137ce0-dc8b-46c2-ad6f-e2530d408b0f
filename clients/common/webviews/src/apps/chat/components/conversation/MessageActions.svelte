<script lang="ts">
  import SuccessfulButton from "$common-webviews/src/apps/chat/components/buttons/SuccessfulButton.svelte";

  export let items: Array<{
    label: string;
    action: (event: Event) => void;
    id: string;
    disabled: boolean;
    icon: any;
    successMessage?: string;
  }>;
</script>

<div class="c-message-actions">
  {#each items as item}
    <SuccessfulButton
      data-testid={`${item.id}-button`}
      variant="ghost-block"
      size={1}
      defaultColor="neutral"
      disabled={item.disabled}
      icon
      stickyColor={false}
      onClick={(e) => {
        item.action(e);
        return item.successMessage ? "success" : "neutral";
      }}
      tooltip={{
        neutral: item.label,
        success: item.successMessage,
      }}
    >
      <svelte:component this={item.icon} />
    </SuccessfulButton>
  {/each}
</div>

<style>
  .c-message-actions {
    display: flex;
    gap: var(--ds-spacing-1);
    align-items: center;
  }

  .c-message-actions :global(svg) {
    width: 16px;
    height: 16px;
  }
</style>

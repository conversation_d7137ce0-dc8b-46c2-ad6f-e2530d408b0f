<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import CheckIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-check.svg?component";
  import CrossCircled from "$common-webviews/src/design-system/icons/augment/cross-circled.svelte";
  import DotsVertical from "$common-webviews/src/design-system/icons/dots-vertical.svelte";
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import { dsColorAttribute } from "$common-webviews/src/design-system/_libs/component-utils";
  import { ToolUsePhase } from "../../../../types/tool-use-state";
  import Play from "$common-webviews/src/design-system/icons/augment/play.svelte";
  import CancelHover from "./CancelHover.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import { getToolUseContext } from "./tool-context";
  import CircleWithXGreenCircle from "$common-webviews/src/design-system/icons/augment/circle-with-x-green-circle.svelte";

  const ctx = getToolUseContext();

  export let isCommandError = false;
  export let toolUseState = $ctx.toolUseState;
  $: {
    toolUseState = $ctx.toolUseState;
  }
  let requestClose: (() => void) | undefined = undefined;

  function onToolSkipSelect() {
    $ctx.onToolSkip($ctx.requestId, $ctx.toolUse.tool_use_id);
    requestClose?.();
  }

  function handleToolRun(e: Event) {
    e.stopPropagation();
    $ctx.onToolRun($ctx.requestId, $ctx.toolUse, $ctx.toolUseInput);
  }

  function handleToolCancel() {
    $ctx.onToolCancel($ctx.requestId, $ctx.toolUse.tool_use_id);
  }
</script>

<div class="c-tooluse__main-bar__top-right" data-tool-use-state={toolUseState.phase}>
  {#if toolUseState.phase === ToolUsePhase.runnable}
    {#if $ctx.isLastTurn}
      <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} content="Run">
        <IconButtonAugment size={1} on:click={handleToolRun}>
          <Play />
        </IconButtonAugment>
      </TextTooltipAugment>
      <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} content="More Options">
        <DropdownMenu.Root bind:requestClose nested={false}>
          <DropdownMenu.Trigger>
            <IconButtonAugment
              variant="soft"
              color="neutral"
              size={1}
              class="c-tooluse__main-vertical-icon"
            >
              <DotsVertical />
            </IconButtonAugment>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content side="bottom" align="end">
            <DropdownMenu.Item onSelect={onToolSkipSelect}>Skip</DropdownMenu.Item>
          </DropdownMenu.Content>
        </DropdownMenu.Root>
      </TextTooltipAugment>
    {:else}
      <!-- what do we do when a comman does not have isLastTurn? -->
      <IconButtonAugment size={1} disabled>
        <Play />
      </IconButtonAugment>
    {/if}
  {:else if isCommandError}
    <!-- tool command did not exit 0-->
    <!--
      To try different icon variations, replace <CircleWithX /> with one of:
      <CircleWithXGreenCircle /> - Green circle with X in current color
      <CircleWithXGreenCircleRedX /> - Green circle with red X
    -->
    <TextTooltipAugment content="Command failed">
      <div class="c-tooluse__main-bar__icon">
        <CircleWithXGreenCircle />
      </div>
    </TextTooltipAugment>
  {:else if toolUseState.phase === ToolUsePhase.new}
    <TextTooltipAugment content="Waiting for end of message...">
      <div class="c-tooluse__main-bar__icon">
        <SpinnerAugment size={1} />
      </div>
    </TextTooltipAugment>
  {:else if toolUseState.phase === ToolUsePhase.checkingSafety}
    <TextTooltipAugment content="Looking up tool...">
      <div class="c-tooluse__main-bar__icon">
        <SpinnerAugment size={1} />
      </div>
    </TextTooltipAugment>
  {:else if toolUseState.phase === ToolUsePhase.running}
    <CancelHover onClick={handleToolCancel} />
  {:else if toolUseState.phase === ToolUsePhase.cancelled}
    <TextTooltipAugment content="Cancelled">
      <div
        class="c-tooluse__main-bar__icon"
        {...dsColorAttribute("error")}
        style={`color: var(--ds-color-a11);`}
      >
        <CrossCircled />
      </div>
    </TextTooltipAugment>
  {:else if toolUseState.phase === ToolUsePhase.error}
    <TextTooltipAugment content="Failed">
      <div
        class="c-tooluse__main-bar__icon"
        {...dsColorAttribute("error")}
        style={`color: var(--ds-color-a11);`}
      >
        <CrossCircled />
      </div>
    </TextTooltipAugment>
  {:else if toolUseState.phase === ToolUsePhase.cancelling}
    <TextTooltipAugment content="Cancelling...">
      <div
        class="c-tooluse__main-bar__icon"
        {...dsColorAttribute("error")}
        style={`color: var(--ds-color-a11);`}
      >
        <SpinnerAugment size={1} />
      </div>
    </TextTooltipAugment>
  {:else}
    <TextTooltipAugment content="Completed">
      <div
        class="c-tooluse__main-bar__icon"
        {...dsColorAttribute("success")}
        style={`color: var(--ds-color-a11);`}
      >
        <CheckIcon />
      </div>
    </TextTooltipAugment>
  {/if}
</div>

<style>
  .c-tooluse__main-bar__top-right {
    display: flex;
    gap: var(--ds-spacing-1);
    align-items: center;
    justify-self: flex-end;
    justify-content: flex-end;
    flex: var(--toolusestatus-flex, 0 0 auto);
    flex-shrink: 0;
    & .c-tooluse__main-vertical-icon {
      width: 16px;
    }
  }

  .c-tooluse__main-bar__icon {
    display: flex;
  }
  .c-tooluse__main-bar__icon :global(svg) {
    width: 16px;
    height: 16px;
    fill: currentColor;
  }
</style>

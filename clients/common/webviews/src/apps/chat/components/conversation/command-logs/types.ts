import { RemoteWorkspaceSetupStepStatus } from "$vscode/src/remote-agent-manager/types";
import { CommandStatus } from "../blocks/tools/components/local/setup-script-types";

export type CommandOutputStatus = "success" | "error" | "skipped" | "unknown" | null;

const commandStatusToOutputStatus: Record<CommandStatus, CommandOutputStatus> = {
  [CommandStatus.SUCCESS]: "success",
  [CommandStatus.FAILED]: "error",
  [CommandStatus.SKIPPED]: "skipped",
};

const setupStepStatusToOutputStatus: Record<RemoteWorkspaceSetupStepStatus, CommandOutputStatus> = {
  [RemoteWorkspaceSetupStepStatus.success]: "success",
  [RemoteWorkspaceSetupStepStatus.failure]: "error",
  [RemoteWorkspaceSetupStepStatus.running]: null,
  [RemoteWorkspaceSetupStepStatus.unknown]: "unknown",
  [RemoteWorkspaceSetupStepStatus.skipped]: "skipped",
};

/**
 * Converts a command status or setup step status to a command output status
 * This is because we use the same component for both setup steps and commands,
 * but the backend types are different.
 */
export function getCommandOutputStatus(
  status: CommandStatus | RemoteWorkspaceSetupStepStatus,
): CommandOutputStatus {
  if (status in commandStatusToOutputStatus) {
    return commandStatusToOutputStatus[status as CommandStatus];
  }
  if (status in setupStepStatusToOutputStatus) {
    return setupStepStatusToOutputStatus[status as RemoteWorkspaceSetupStepStatus];
  }
  return null;
}

export function getStatusText(status: CommandOutputStatus) {
  switch (status) {
    case "success":
      return "Success";
    case "error":
      return "Failed";
    case "skipped":
      return "Skipped";
    case "unknown":
      return "Unknown";
    case null:
      return "Running";
  }
}

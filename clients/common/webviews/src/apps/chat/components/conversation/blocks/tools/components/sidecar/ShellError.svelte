<script lang="ts">
  import ShowMore from "$common-webviews/src/common/components/ShowMore.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CopyButton from "$common-webviews/src/common/components/CopyButton.svelte";

  export let isError = false;
  export let text: string | undefined = "";
</script>

{#if isError}
  <div class="c-shell-error">
    <div class="c-shell-error__code">
      <TextAugment size={1} weight="medium">Error</TextAugment>
      <CopyButton text={text ?? ""} />
    </div>
    {#if text}
      <ShowMore maxHeight={90}>
        <div class="c-shell-error__message">
          <TextAugment size={1} weight="light" type="monospace"><pre>{text}</pre></TextAugment>
        </div>
      </ShowMore>
    {/if}
  </div>
{/if}

<style>
  .c-shell-error {
    display: flex;
    flex-direction: column;

    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-2) var(--ds-spacing-2) var(--ds-spacing-2) var(--ds-spacing-4);
    background-color: var(--ds-color-error-2);
    border-radius: var(--ds-radius-2);
    /** I'm sorry but its what it is*/
    margin-left: var(--ds-spacing-1);
  }
  .c-shell-error :global(.c-copy-button) {
    visibility: hidden;
  }
  .c-shell-error:hover :global(.c-copy-button) {
    visibility: visible;
  }
  .c-shell-error__code {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: var(--ds-spacing-2);
    color: var(--ds-color-error-8);
  }
  .c-shell-error__message {
    color: var(--augment-text-color);
    & pre {
      line-height: 1.3;
      margin: 0;
      font-size: 0.8rem;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
</style>

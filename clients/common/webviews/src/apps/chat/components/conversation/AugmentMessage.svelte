<script lang="ts">
  import { writable } from "svelte/store";
  import { type ComponentType, getContext, setContext, type SvelteComponent } from "svelte";
  import ChatMessage from "../ChatMessage.svelte";
  import Markdown from "$common-webviews/src/common/components/markdown/Markdown.svelte";
  import Codespan from "$common-webviews/src/apps/chat/components/markdown-ext/Codespan.svelte";
  import Link from "$common-webviews/src/apps/chat/components/markdown-ext/Link.svelte";
  import CodeRenderer from "$common-webviews/src/apps/chat/components/markdown-ext/CodeRenderer.svelte";
  import {
    type ChatItem,
    ChatItemType,
    ExchangeStatus,
    type ExchangeWithStatus,
  } from "../../types/chat-message";
  import { isAgentConversation } from "../../models/types";
  import FeedbackPanel from "../FeedbackPanel.svelte";
  import { type ChatModel } from "../../models/chat-model";
  import { ChatResultNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import SuggestedQuestions from "./blocks/SuggestedQuestions.svelte";
  import { ChatMetricName } from "$vscode/src/metrics/types";
  import Share from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/share-nodes.svg?component";
  import Update from "$common-webviews/src/design-system/icons/update.svelte";
  import MessageActions from "./MessageActions.svelte";
  import ToolUses from "./blocks/ToolUses.svelte";
  import { type ToolsWebviewModel } from "../../models/tools-webview-model";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import ViewChangesButton from "./ViewChangesButton.svelte";
  import {
    getAugmentMessageContext,
    type MessageItemName,
  } from "../../utils/augment-message-context";
  import { ChatMode } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import { type GroupedChatItem } from "../../utils/message-list-context";
  import { getMessageRenderOptions } from "$common-webviews/src/apps/remote-agent-manager/models/message-render-options";

  export let chatModel: ChatModel | undefined = undefined;
  export let turn: ExchangeWithStatus | undefined = undefined;
  export let requestId: string | undefined = undefined;
  export let markdown: string;
  export let turnIndex: number = 0;
  export let isLastTurn: boolean = false;
  export let showName: boolean = false;
  export let showFooter: boolean = true;
  export let timestamp: string | undefined = undefined;
  export let messageListContainer: HTMLElement | undefined = undefined;
  export let group: GroupedChatItem | undefined = undefined;

  const toolsWebviewModel = getContext<ToolsWebviewModel>("toolsWebviewModel");
  const remoteAgentsModel = getContext<RemoteAgentsModel | undefined>(RemoteAgentsModel.key);

  // Helper function to check if a turn has response_text property and get it safely
  function getResponseText(turn: ChatItem): string {
    return "response_text" in turn ? (turn.response_text ?? "") : "";
  }

  const agentResponse = group
    ? group
        .map((t) => getResponseText(t.turn))
        .filter((text) => text.length > 0)
        .join("\n")
    : undefined;

  $: isRemoteAgentWindow = $chatModel?.flags.isRemoteAgentWindow ?? false;
  $: messageRenderOptions = getMessageRenderOptions($remoteAgentsModel, isRemoteAgentWindow);

  // Initialize reactive state to only show the timestamp if we also show the footer
  // this prevents agent tool calls from showing too many timestamps
  $: displayedTimestamp = showFooter ? timestamp : undefined;

  $: conversationModel = $chatModel?.currentConversationModel;
  // Add a computed property to determine if message is still streaming
  $: showActions =
    turn?.status === ExchangeStatus.success ||
    turn?.status === ExchangeStatus.failed ||
    turn?.status === ExchangeStatus.cancelled;

  const icons: Record<MessageItemName, ComponentType<SvelteComponent>> = {
    share: Share,
    retry: Update,
  };

  $: augmentMessageContext = getAugmentMessageContext(
    $chatModel,
    $conversationModel,
    $remoteAgentsModel,
  );
  $: contextTooltipItems = augmentMessageContext.tooltipItems;

  $: tooltipItems = [
    ...contextTooltipItems.map((item) => ({
      ...item,
      icon: icons[item.icon],
    })),
    // Only include retry button when it would be enabled
    ...(isLastTurn && turn?.status === ExchangeStatus.failed
      ? [
          {
            label: "Retry",
            action: () => {
              if (conversationModel && turn) {
                conversationModel.resendTurn(turn);
              }
            },
            id: "retry-message",
            disabled: false,
            icon: Update,
          },
        ]
      : []),
  ];

  // Set the request ID so any child component here can read it.
  // Useful for getting metadata from the chat conversation in child
  // components
  const requestIdStore = writable(requestId || "");
  $: requestId && requestIdStore.set(requestId);
  setContext("requestId", requestIdStore);

  let suggestedQuestions: string[] = [];
  let reportedMetrics = false;
  $: {
    if (
      turn &&
      turn.status !== ExchangeStatus.sent &&
      requestId &&
      isLastTurn &&
      turn.structured_output_nodes &&
      turn.structured_output_nodes.length > 0
    ) {
      suggestedQuestions =
        turn.structured_output_nodes
          .find((node) => node.type === ChatResultNodeType.SUGGESTED_QUESTIONS)
          ?.content.split("\n") ?? [];
      if ($chatModel && suggestedQuestions.length > 0 && !reportedMetrics) {
        reportedMetrics = true;
        $chatModel.extensionClient.reportWebviewClientEvent(
          ChatMetricName.chatDisplaySuggestedQuestions,
        );
      }
    } else {
      suggestedQuestions = [];
    }
  }

  let showFeedbackInput: boolean | undefined;
</script>

<ChatMessage isAugment {showName} timestamp={displayedTimestamp} {turn}>
  <svelte:fragment slot="content">
    {#if markdown.length > 0}
      <Markdown
        renderers={{
          code: CodeRenderer,
          codespan: Codespan,
          link: Link,
        }}
        {markdown}
      />
    {/if}

    {#if $conversationModel && turn && $chatModel && requestId && turn.structured_output_nodes}
      {@const toolUseNodes = turn.structured_output_nodes.filter(
        (node) => node.type === ChatResultNodeType.TOOL_USE,
      )}
      {#if toolUseNodes.length > 0 && toolsWebviewModel}
        <ToolUses
          {requestId}
          {toolsWebviewModel}
          {remoteAgentsModel}
          conversationModel={$conversationModel}
          {toolUseNodes}
          {isLastTurn}
          {turnIndex}
          {messageListContainer}
        />
      {/if}
    {/if}
  </svelte:fragment>

  <svelte:fragment slot="footer">
    <div class="c-aug-msg__footer">
      <!-- Suggested questions -->
      {#if $conversationModel && turn && suggestedQuestions.length > 0}
        <div class="c-aug-msg__suggestions">
          <SuggestedQuestions
            conversationModel={$conversationModel}
            turn={{
              questionsLoaded: true,
              questions: suggestedQuestions,
              status: turn.status,
              /* eslint-disable @typescript-eslint/naming-convention */
              request_message: turn.request_message,
              request_id: turn.request_id,
              /* eslint-enable @typescript-eslint/naming-convention */
              chatItemType: ChatItemType.summaryResponse,
            }}
            showIntroText={false}
          />
        </div>
      {/if}
      {#if showFooter && showActions}
        <div class="c-aug-msg__actions-row">
          <!-- Feedback -->
          {#if turn && $conversationModel && $chatModel && (turn.status === ExchangeStatus.success || turn.status === ExchangeStatus.failed || turn.status === ExchangeStatus.cancelled) && requestId}
            {@const feedbackState = $conversationModel.feedbackStates[requestId]}
            <div class="c-aug-msg__feedback" class:c-aug-msg__feedback-active={showFeedbackInput}>
              <FeedbackPanel
                {requestId}
                {feedbackState}
                reponseText={agentResponse}
                onSendUserRating={async (requestId, rating, note) => {
                  let chatMode = ChatMode.chat;
                  if ($remoteAgentsModel?.isActive) {
                    chatMode = ChatMode.remoteAgent;
                  } else if (isAgentConversation($conversationModel)) {
                    chatMode = ChatMode.agent;
                  }
                  await $chatModel.extensionClient.sendUserRating(
                    requestId,
                    chatMode,
                    rating,
                    note,
                  );
                }}
                bind:showFeedbackInput
              >
                <div class="c-aug-msg__actions">
                  <MessageActions items={tooltipItems} />
                </div>
              </FeedbackPanel>
            </div>
          {:else}
            <!-- Show actions without feedback panel -->
            <div class="c-aug-msg__actions">
              <MessageActions items={tooltipItems} />
            </div>
          {/if}
        </div>
        {#if messageRenderOptions.doShowTurnSelector}
          <ViewChangesButton {turnIndex} {isLastTurn} />
        {/if}
      {/if}
    </div>
  </svelte:fragment>
</ChatMessage>

<style>
  .c-aug-msg__footer {
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .c-aug-msg__actions-row {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    padding: 0 var(--ds-spacing-2);
  }

  .c-aug-msg__feedback {
    width: 100%;
    display: flex;
  }

  .c-aug-msg__actions {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-right: auto;
  }

  .c-aug-msg__suggestions,
  .c-aug-msg__feedback {
    width: 100%;
  }

  :global(.tooltip-options) {
    display: flex;
    flex-direction: column;
  }

  .c-aug-msg__suggestions {
    padding-block: var(--ds-spacing-1);
  }

  :global(.c-chat-message.c-chat-message--augment) {
    &:hover .c-aug-msg__feedback,
    & .c-aug-msg__feedback.c-aug-msg__feedback-active {
      opacity: 0.99;
    }
    & .c-aug-msg__feedback {
      opacity: 0;
      transform: opacity 0.3s ease;
    }
  }
</style>

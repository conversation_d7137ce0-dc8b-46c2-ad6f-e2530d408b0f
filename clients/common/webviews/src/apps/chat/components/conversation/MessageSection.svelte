<script lang="ts">
  import LazyRemoteAgentSetup from "$common-webviews/src/apps/remote-agent-manager/lazy-wrappers/LazyRemoteAgentSetup.svelte";
  import { type RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { type ChatModel } from "../../models/chat-model";
  import { type OnboardingWorkspaceModel } from "../../models/onboarding-workspace-model";
  import LazyMessageList from "./LazyMessageList.svelte";

  export let chatModel: ChatModel;
  export let onboardingWorkspaceModel: OnboardingWorkspaceModel;
  export let remoteAgentsModel: RemoteAgentsModel;

  $: isCreateRemoteAgentView = $remoteAgentsModel.isActive && !$remoteAgentsModel.currentAgentId;

  $: shouldShowMessageList = !isCreateRemoteAgentView;
</script>

{#if shouldShowMessageList}
  <LazyMessageList {chatModel} {onboardingWorkspaceModel} {remoteAgentsModel} />
{:else if isCreateRemoteAgentView}
  <div class="l-center-contents">
    <LazyRemoteAgentSetup />
  </div>
{/if}

<style>
  .l-center-contents {
    max-width: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
  }
</style>

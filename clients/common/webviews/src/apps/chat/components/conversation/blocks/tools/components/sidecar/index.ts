import ShellToolComponent from "./ShellToolComponent.svelte";
import StrReplaceEditorToolComponent from "./StrReplaceEditorToolComponent/StrReplaceEditorToolComponent.svelte";
import WebFetchToolComponent from "./WebFetchToolComponent.svelte";
import OpenInBrowserToolComponent from "./OpenInBrowserToolComponent.svelte";
import CodebaseRetrievalToolComponent from "./CodebaseRetrievalToolComponent.svelte";
import RemoveFilesToolComponent from "./RemoveFilesToolComponent.svelte";
import ViewToolComponent from "./ViewToolComponent/ViewToolComponent.svelte";
import { RenderMermaidToolComponent } from "./RenderMermaidToolComponent";
import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import UpdateTaskListToolComponent from "./TaskToolComponent/UpdateTaskListToolComponent.svelte";
import ViewTaskListToolComponent from "./TaskToolComponent/ViewTaskListToolComponent.svelte";
import GrepSearchToolComponent from "./GrepSearchToolComponent.svelte";

// Map of tool types to their components
export const sidecarToolComponents = {
  [SidecarToolType.shell]: ShellToolComponent,
  [SidecarToolType.webFetch]: WebFetchToolComponent,
  [SidecarToolType.strReplaceEditor]: StrReplaceEditorToolComponent,
  [SidecarToolType.codebaseRetrieval]: CodebaseRetrievalToolComponent,
  [SidecarToolType.removeFiles]: RemoveFilesToolComponent,
  [SidecarToolType.view]: ViewToolComponent,
  [SidecarToolType.viewTaskList]: ViewTaskListToolComponent,
  [SidecarToolType.updateTaskList]: UpdateTaskListToolComponent,
  [SidecarToolType.renderMermaid]: RenderMermaidToolComponent,
  [SidecarToolType.grepSearch]: GrepSearchToolComponent,
} as const;

/**
 * Returns the appropriate tool component for a given tool type.
 * @param toolName SidecarToolType
 * @returns
 */
export function resolveSidecarTool(toolName: SidecarToolType) {
  return toolName in sidecarToolComponents
    ? sidecarToolComponents[toolName as keyof typeof sidecarToolComponents]
    : undefined;
}

export {
  ShellToolComponent,
  WebFetchToolComponent,
  OpenInBrowserToolComponent,
  CodebaseRetrievalToolComponent,
  StrReplaceEditorToolComponent,
  RemoveFilesToolComponent,
  ViewToolComponent,
  ViewTaskListToolComponent,
  UpdateTaskListToolComponent,
  RenderMermaidToolComponent,
  GrepSearchToolComponent,
};

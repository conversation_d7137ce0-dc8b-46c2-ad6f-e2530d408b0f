<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { getChatModel } from "$common-webviews/src/apps/chat/chat-context";
  import { getToolUseContext } from "../../../tool-context";
  import { ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import { visibilityObserverOnce } from "$common-webviews/src/apps/chat/components/actions/trackOnScreen";

  const ctx = getToolUseContext();
  const app = getChatModel();
  export let requestId: string;
  export let toolUseState = $ctx.toolUseState;
  export let totalAddedLines = 0;
  export let totalRemovedLines = 0;

  let hasBeenVisible = false;
  let hasLoaded = false;
  async function loadChanges(phase: ToolUsePhase, rid: string) {
    if (hasLoaded || phase !== ToolUsePhase.completed || !hasBeenVisible) return;
    try {
      const changes = await app.extensionClient?.getAgentEditChangesByRequestId(rid);
      totalAddedLines = changes?.totalAddedLines ?? 0;
      totalRemovedLines = changes?.totalRemovedLines ?? 0;
    } catch (e) {
      console.warn(e);
    }
  }

  // Watch for phase transitions to completed
  $: if (toolUseState.phase === ToolUsePhase.completed && hasBeenVisible && !hasLoaded) {
    void loadChanges(toolUseState.phase, requestId);
  }
</script>

<div
  class="c-line-diff-count"
  use:visibilityObserverOnce={{
    onVisible: () => (hasBeenVisible = true),
    scrollTarget: document.body,
  }}
>
  {#if totalAddedLines > 0}
    <TextAugment class="c-line-diff-count__added" size={1}>+{totalAddedLines}</TextAugment>
  {/if}
  {#if totalRemovedLines > 0}
    <TextAugment class="c-line-diff-count__removed" size={1}>-{totalRemovedLines}</TextAugment>
  {/if}
</div>

<style>
  .c-line-diff-count {
    display: flex;
    gap: var(--ds-spacing-1);
  }
  .c-line-diff-count :global(.c-line-diff-count__added) {
    color: var(--ds-color-success-11);
  }
  .c-line-diff-count :global(.c-line-diff-count__removed) {
    color: var(--ds-color-error-11);
  }
</style>

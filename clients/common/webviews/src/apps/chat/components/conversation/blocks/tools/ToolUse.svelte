<script lang="ts">
  import type { ToolUseState } from "../../../../types/tool-use-state";
  import type { ChatResultToolUse } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import { resolveTool } from "./components";
  import { setToolUseContext } from "./tool-context";

  export let toolUse: ChatResultToolUse;
  export let toolUseState: ToolUseState;
  export let isLastTurn: boolean;
  export let requestId: string;
  export let onToolRun: (
    requestId: string,
    toolUse: ChatResultToolUse,
    toolUseInput: any,
  ) => void = () => {};
  export let onToolCancel: (requestId: string, toolUseId: string) => void = () => {};
  export let onToolSkip: (requestId: string, toolUseId: string) => void = () => {};
  export let turnIndex: number = 0;
  export let messageListContainer: HTMLElement | undefined = undefined;

  // Get the appropriate tool component based on the tool type
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const ToolComponent = resolveTool(toolUse);
  const { readableCtx, writableCtx } = setToolUseContext({
    toolUse,
    toolUseState,
    isLastTurn,
    requestId,
    onToolRun,
    onToolCancel,
    onToolSkip,
    turnIndex,
    messageListContainer,
  });

  $: $writableCtx = {
    toolUse,
    toolUseState,
    isLastTurn,
    requestId,
    onToolRun,
    onToolCancel,
    onToolSkip,
    turnIndex,
    messageListContainer,
  };
</script>

<svelte:component this={ToolComponent} {...$readableCtx} {messageListContainer} />

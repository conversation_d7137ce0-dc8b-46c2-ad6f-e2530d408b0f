import ReadFileToolComponent from "./ReadFileToolComponent.svelte";
import SaveFileToolComponent from "./SaveFileToolComponent.svelte";
import RememberToolComponent from "./RememberToolComponent.svelte";
import DiagnosticsToolComponent from "./DiagnosticsToolComponent.svelte";
import { LocalToolType } from "$vscode/src/webview-providers/tool-types";
import ShellToolComponent from "../sidecar/ShellToolComponent.svelte";
import {
  CodebaseRetrievalToolComponent,
  StrReplaceEditorToolComponent,
  OpenInBrowserToolComponent,
} from "../sidecar";
import SetupScriptToolComponent from "./SetupScriptToolComponent.svelte";

// Map of tool types to their components
export const localToolComponents = {
  [LocalToolType.editFile]: StrReplaceEditorToolComponent,
  [LocalToolType.readFile]: ReadFileToolComponent,
  [LocalToolType.saveFile]: SaveFileToolComponent,
  [LocalToolType.launchProcess]: ShellToolComponent,
  [LocalToolType.killProcess]: ShellToolComponent,
  [LocalToolType.readProcess]: ShellToolComponent,
  [LocalToolType.writeProcess]: ShellToolComponent,
  [LocalToolType.listProcesses]: ShellToolComponent,
  [LocalToolType.waitProcess]: ShellToolComponent,
  [LocalToolType.openBrowser]: OpenInBrowserToolComponent,
  [LocalToolType.remember]: RememberToolComponent,
  [LocalToolType.diagnostics]: DiagnosticsToolComponent,
  [LocalToolType.setupScript]: SetupScriptToolComponent,
  [LocalToolType.readTerminal]: ShellToolComponent,
  [LocalToolType.gitCommitRetrieval]: CodebaseRetrievalToolComponent,
  // Add more local tool components as needed
} as const;

/**
 * Returns the appropriate tool component for a given tool type.
 * @param toolName LocalToolType
 * @returns
 */
export function resolveLocalTool(toolName: LocalToolType) {
  return toolName in localToolComponents
    ? localToolComponents[toolName as keyof typeof localToolComponents]
    : undefined;
}

export { ReadFileToolComponent, SaveFileToolComponent, RememberToolComponent };

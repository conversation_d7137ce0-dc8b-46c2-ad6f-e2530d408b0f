<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import Expand from "$common-webviews/src/design-system/icons/augment/expand.svelte";
  import Collapse from "$common-webviews/src/design-system/icons/augment/collapse.svelte";

  export let logs: string;
  let containerWidth: number;
  $: processedLogs = logs.replace(/\r/g, "\n");

  // Estimate if the text is likely to wrap based on length
  // A reasonable character count for a single line in a typical monospace font
  $: estimatedCharsPerLine = containerWidth / 6;

  // Check for actual line breaks in the text
  $: heightOfLines = processedLogs
    .trim()
    .split("\n")
    .map((line) => Math.max(1, Math.ceil(line.length / estimatedCharsPerLine)));
  $: approximateNumberOfLines = heightOfLines.reduce((acc, line) => acc + line, 0);

  let isExpanded = false;

  $: lastLineHeight = heightOfLines[heightOfLines.length - 1];
  $: numberOfVisibleLinesWhenCollapsed = lastLineHeight > 1 ? 2 : 1;

  // Determine if content should show expand/collapse buttons
  $: shouldShowButtons = approximateNumberOfLines > numberOfVisibleLinesWhenCollapsed;

  function toggleExpand() {
    isExpanded = !isExpanded;
  }
</script>

<div class="c-expandable-log">
  <div
    class="c-expandable-log-preview"
    class:c-expandable-log-preview--expanded={isExpanded}
    class:c-expandable-log-preview--single-line={!shouldShowButtons}
  >
    <div
      class="c-expandable-log-content c-expandable-log-content--lines-{numberOfVisibleLinesWhenCollapsed}"
      bind:clientWidth={containerWidth}
    >
      <TextAugment size={1} type="monospace">{processedLogs}</TextAugment>
    </div>

    {#if shouldShowButtons}
      <div class="c-expandable-log-toggle">
        <IconButtonAugment variant="ghost" color="neutral" size={1} on:click={toggleExpand}>
          {#if isExpanded}
            <Collapse />
          {:else}
            <Expand />
          {/if}
        </IconButtonAugment>
      </div>
    {/if}
  </div>
</div>

<style>
  .c-expandable-log {
    width: 100%;
    position: relative;
  }

  .c-expandable-log-preview {
    display: flex;
    justify-content: space-between;
    background-color: var(--ds-color-neutral-1);
    border-radius: var(--ds-border-radius-1);
    padding: var(--ds-spacing-1) var(--ds-spacing-1) var(--ds-spacing-1) var(--ds-spacing-3);
  }
  .c-expandable-log-preview :global(.c-text) {
    font-size: 0.6rem;
  }

  .c-expandable-log-content {
    flex: 1;
    max-height: 1.6em;
    padding-right: 20px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    overflow: hidden;
    text-overflow: ellipsis;
    background-color: var(--ds-color-neutral-1);
    font-size: 0.7rem;
    line-height: 1.4;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-all;
    overflow-y: auto;
    transition: max-height 0.3s ease-in-out;
  }
  .c-expandable-log-content--lines-2 {
    max-height: 3em;
  }

  .c-expandable-log-preview--expanded .c-expandable-log-content {
    max-height: 30em;
    justify-content: flex-start;
  }

  .c-expandable-log-preview--single-line .c-expandable-log-content {
    white-space: pre-wrap;
    overflow-x: auto;
    padding-right: 0;
    max-height: none;
  }

  .c-expandable-log-toggle {
    position: absolute;
    right: 5px;
    top: 5px;
  }
</style>

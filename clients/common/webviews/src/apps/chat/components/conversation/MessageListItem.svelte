<script lang="ts">
  import { onMount } from "svelte";
  import {
    followElement,
    scrollTo,
    scrollToBottom,
    type ScrollToOptions,
  } from "../actions/followBottom";
  import type { ChatModel } from "../../models/chat-model";

  export let requestId;
  export let chatModel: ChatModel;
  export let isLastItem: boolean = false;
  export let userControlsScroll: boolean = false;
  export let releaseScroll: () => void = () => {};
  export let messageListContainer: HTMLElement | undefined = undefined;
  export let minHeight: number;
  let clazz: string = "";
  export { clazz as class };

  // Additional HTML attributes
  export let dataRequestId: string | undefined = undefined;

  // Scroll to the current turn if it is focused
  let turnElement: HTMLElement | undefined = undefined;
  function scrollToTurn() {
    if (messageListContainer && turnElement) {
      const options: ScrollToOptions = {
        topBuffer: 0,
        smooth: true,
        scrollDuration: 100,
        onScrollFinish: releaseScroll,
      };
      scrollTo(messageListContainer, turnElement, options);
    }
  }

  $: focusModel = $chatModel?.currentConversationModel?.focusModel;
  $: isFocused = $focusModel.focusedItem?.request_id === requestId;
  $: isFocused && scrollToTurn();

  onMount(() => {
    // Scroll to the bottom of the message list if this is the last turn and we are just mounting it
    // We release the scroll after we finish.
    if (messageListContainer && isLastItem) {
      scrollToBottom(messageListContainer, { smooth: true, onScrollFinish: releaseScroll });
    }
  });
</script>

<div
  class={`c-msg-list__item ${clazz}`}
  style={`min-height: calc(${minHeight}px - (var(--msg-list-item-spacing) * 2));`}
  use:followElement={{
    follow: !userControlsScroll && isLastItem,
    scrollContainer: messageListContainer,
    disableScrollUp: true,
    smooth: true,
    bottom: true,
  }}
  bind:this={turnElement}
  data-request-id={dataRequestId}
>
  <slot />
</div>

<style>
  .c-msg-list__item {
    /* Assign to a CSS variable so it can be used in the min-height calculation */
    --msg-list-item-spacing: var(--ds-spacing-2);
    margin-block: var(--msg-list-item-spacing);
  }
</style>

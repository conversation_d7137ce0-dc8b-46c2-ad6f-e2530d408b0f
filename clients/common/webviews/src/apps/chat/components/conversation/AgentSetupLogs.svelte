<script lang="ts">
  import {
    RemoteAgentStatus,
    RemoteWorkspaceSetupStepStatus,
  } from "$vscode/src/remote-agent-manager/types";
  import { getContext } from "svelte";
  import { RemoteAgentsModel } from "../../../remote-agent-manager/models/remote-agents-model";
  import { type ChatModel } from "../../models/chat-model";
  import CommandOutputCollapsible from "./command-logs/CommandOutputCollapsible.svelte";
  import { getCommandOutputStatus } from "./command-logs/types";

  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import Check from "$common-webviews/src/design-system/icons/check.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/chevron-down.svelte";

  // Reference to the logs container element for scrolling
  let logsContainerElement: HTMLElement;

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const chatModel = getContext<ChatModel>("chatModel");

  $: agentStatus = $remoteAgentsModel?.currentAgent?.status || RemoteAgentStatus.agentUnspecified;

  $: hasCompletedSetup = [
    RemoteAgentStatus.agentIdle,
    RemoteAgentStatus.agentRunning,
    RemoteAgentStatus.agentFailed,
  ].includes(agentStatus);

  $: agentSetupLogs = $remoteAgentsModel?.agentSetupLogs;

  // State to track if logs are expanded or collapsed
  let logsExpanded = false;
  $: logsExpanded = !hasCompletedSetup;

  function openStepLogs(logs: string) {
    if (!logs || !chatModel) return;
    chatModel.extensionClient.openScratchFile(logs, "plaintext");
  }

  function toggleLogs() {
    logsExpanded = !logsExpanded;
  }

  function handleButtonClick(event: Event) {
    // Prevent the click from propagating to the parent div
    event.stopPropagation();
    toggleLogs();
  }

  function handleKeyDown(event: KeyboardEvent) {
    // Toggle logs when Enter or Space is pressed
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      toggleLogs();
    }
  }
</script>

{#if agentSetupLogs && agentSetupLogs.steps && agentSetupLogs.steps.length > 0}
  <div
    class="c-agent-setup-logs-container"
    class:c-agent-setup-logs-container--loading={!hasCompletedSetup}
    bind:this={logsContainerElement}
  >
    <!-- Summary line when setup is complete -->
    <div
      class="c-agent-setup-logs-summary"
      on:click={toggleLogs}
      on:keydown={handleKeyDown}
      role="button"
      tabindex="0"
      aria-expanded={logsExpanded}
      aria-controls="agent-setup-logs-details"
    >
      <div class="c-agent-setup-logs-summary-content">
        <div class="c-agent-setup-logs-summary-left">
          <IconButtonAugment
            variant="ghost"
            color="neutral"
            size={1}
            on:click={handleButtonClick}
            class="c-agent-setup-logs-toggle-button {logsExpanded
              ? 'c-agent-setup-logs-toggle-button--expanded'
              : ''}"
          >
            <ChevronDown />
          </IconButtonAugment>
          {#if hasCompletedSetup}
            <TextAugment size={1} class="c-agent-setup-logs-summary-text">
              Environment created
            </TextAugment>
          {:else}
            <TextAugment size={1} class="c-agent-setup-logs-summary-text">
              Environment is being created...
            </TextAugment>
          {/if}
        </div>
        <div class="c-agent-setup-logs-summary-icon">
          {#if hasCompletedSetup}
            <Check />
          {:else}
            <SpinnerAugment size={1} />
          {/if}
        </div>
      </div>
    </div>

    <!-- Show detailed logs when expanding or when setup is still in progress -->
    <div class="c-agent-setup-logs-wrapper" class:is-hidden={!logsExpanded}>
      <div class="c-agent-setup-logs">
        {#each agentSetupLogs.steps as step}
          <CommandOutputCollapsible
            title={step.step_description}
            output={step.logs}
            status={getCommandOutputStatus(step.status)}
            isLoading={step.status === RemoteWorkspaceSetupStepStatus.running}
            collapsed={step.status !== RemoteWorkspaceSetupStepStatus.running}
            showCollapseButton={!!step.logs}
            viewButtonTooltip="View full output in editor"
            onViewOutput={(e, output) => openStepLogs(output)}
          />
        {/each}
      </div>
    </div>
  </div>
{:else}
  <div class="c-agent-no-setup-logs">
    <TextAugment size={1} color="secondary">Waiting to start agent environment...</TextAugment>
    <SpinnerAugment size={1} />
  </div>
{/if}

<style>
  .c-agent-no-setup-logs {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-2);
    justify-content: space-between;
  }

  .c-agent-setup-logs-container {
    margin: 0 var(--ds-spacing-2) var(--ds-spacing-2);
    background-color: var(--ds-color-neutral-a2);
  }

  .c-agent-setup-logs-summary {
    border-radius: var(--ds-radius-2);
    padding: var(--ds-spacing-2);
    cursor: pointer;
  }

  .c-agent-setup-logs-summary-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--ds-spacing-1);
    flex-wrap: wrap;
  }
  .c-agent-setup-logs-summary-content :global(.c-agent-setup-logs-toggle-button) {
    transform: rotate(-90deg);
    transition: transform 0.2s ease;
  }
  .c-agent-setup-logs-summary-content :global(.c-agent-setup-logs-toggle-button svg) {
    width: 13px;
  }

  .c-agent-setup-logs-summary-content :global(.c-agent-setup-logs-toggle-button--expanded) {
    transform: rotate(0deg);
  }

  .c-agent-setup-logs-summary-left {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    flex: 1;
    min-width: 0;
  }

  .c-agent-setup-logs-summary-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--ds-color-success-9);
    flex-shrink: 0;
  }

  .c-agent-setup-logs-wrapper {
    display: grid;
    grid-template-rows: 1fr;
    transition:
      grid-template-rows 0.3s ease-in-out,
      opacity 0.3s ease-in-out,
      margin-top 0.3s ease-in-out,
      padding 0.3s ease-in-out;
    padding: 0 var(--ds-spacing-2) var(--ds-spacing-2);
    opacity: 1;
  }

  .c-agent-setup-logs-wrapper.is-hidden {
    grid-template-rows: 0fr;
    opacity: 0;
    margin-top: 0;
    padding: 0 var(--ds-spacing-1);
    overflow: hidden;
  }

  .c-agent-setup-logs {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    overflow: hidden;
    min-height: 0;
  }
</style>

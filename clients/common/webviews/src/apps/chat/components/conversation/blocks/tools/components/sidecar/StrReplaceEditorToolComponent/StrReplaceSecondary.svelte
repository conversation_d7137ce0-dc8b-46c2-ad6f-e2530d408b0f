<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import { getToolUseContext } from "../../../tool-context";
  import LineDiffCount from "./LineDiffCount.svelte";
  import { getCollapsibleContext } from "$common-webviews/src/design-system/components/CollapsibleAugment/context";
  const ctx = getToolUseContext();
  const collapsed = getCollapsibleContext().collapsed;
  export let start: number | undefined = undefined;
  export let stop: number | undefined = undefined;
</script>

<TextAugment size={2} class="c-strreplace__header__secondary"
  >{#if $ctx.toolUseInput.command === "view"}
    {($ctx.toolUseInput.edit_summary ?? $ctx.toolUseState.phase === ToolUsePhase.running)
      ? "Reading file..."
      : !(start && stop)
        ? `Read file`
        : `Read lines ${start}-${stop}`}
  {:else}
    {$ctx.toolUseState.phase === ToolUsePhase.running ? "Editing file..." : "Edited file"}
    {#if $collapsed}
      <LineDiffCount requestId={$ctx.requestId} />
    {/if}
  {/if}
</TextAugment>

<style>
  :global(.c-strreplace__added-lines) {
    color: var(--ds-color-success-11);
  }
  :global(.c-strreplace__removed-lines) {
    color: var(--ds-color-error-11);
  }
  :global(.c-strreplace__header__secondary.c-strreplace__header__secondary) {
    display: flex;
    align-items: center;
    display: flex;
    column-gap: var(--ds-spacing-1);
  }
</style>

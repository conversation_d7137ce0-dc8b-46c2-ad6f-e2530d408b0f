<script lang="ts">
  import { type RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { type ChatModel } from "../../models/chat-model";
  import type { OnboardingWorkspaceModel } from "../../models/onboarding-workspace-model";
  import LoadingError from "./LoadingError.svelte";

  export let chatModel: ChatModel;
  export let onboardingWorkspaceModel: OnboardingWorkspaceModel;
  export let remoteAgentsModel: RemoteAgentsModel;

  $: conversationModel = $chatModel.currentConversationModel;
  $: flagsModel = $chatModel.flags;
  $: remoteAgentId = $remoteAgentsModel.currentAgentId;
  $: isRemoteAgent = $remoteAgentsModel.isActive;
  $: hasRemoteAgentChatHistory =
    isRemoteAgent && $remoteAgentsModel.getCurrentChatHistory()?.length;
  $: hasLogs = isRemoteAgent && $remoteAgentsModel.agentSetupLogs?.steps.length !== 0;
  $: isEmptyConversation = isRemoteAgent
    ? !hasRemoteAgentChatHistory && !hasLogs
    : $conversationModel.chatHistory.length === 0;

  // Use the virtualized message list based on the flag and if we're not in the remote agents context
  $: useVirtualizedList = $flagsModel.enableVirtualizedMessageList && !isRemoteAgent;

  // Key for the message list
  $: key = `${$conversationModel.id}-${isRemoteAgent ? remoteAgentId : ""}`;
</script>

{#if useVirtualizedList}
  {#await import("./VirtualizedMessageList.svelte")}
    {#key key}
      {#if isEmptyConversation}
        <div class="l-center-contents"></div>
      {:else}
        <div class="l-center-contents">Loading...</div>
      {/if}
    {/key}
  {:then c}
    {#key key}
      {#if isEmptyConversation}
        <div class="l-center-contents" data-testid="l-lazy-message-list-center-contents"></div>
      {:else}
        <svelte:component
          this={c.default}
          {chatModel}
          {onboardingWorkspaceModel}
          {...$$restProps}
        />
      {/if}
    {/key}
  {:catch error}
    <div class="l-center-contents" data-testid="l-lazy-message-list-loading-error">
      <LoadingError errorMessage={error.message} />
    </div>
  {/await}
{:else}
  {#await import("./MessageList.svelte")}
    {#key key}
      {#if isEmptyConversation}
        <div class="l-center-contents"></div>
      {:else}
        <div class="l-center-contents">Loading...</div>
      {/if}
    {/key}
  {:then c}
    {#key key}
      {#if isEmptyConversation}
        <div class="l-center-contents" data-testid="l-lazy-message-list-center-contents"></div>
      {:else}
        <svelte:component
          this={c.default}
          {chatModel}
          {onboardingWorkspaceModel}
          {...$$restProps}
        />
      {/if}
    {/key}
  {:catch error}
    <div class="l-center-contents" data-testid="l-lazy-message-list-loading-error">
      <LoadingError errorMessage={error.message} />
    </div>
  {/await}
{/if}

<style>
  .l-center-contents {
    max-width: 100%;
    overflow-x: hidden;
    overflow-y: auto;

    /* Center the content */
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
  }
</style>

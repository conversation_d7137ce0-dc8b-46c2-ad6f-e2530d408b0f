<script lang="ts">
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import { createHoverAction } from "$common-webviews/src/common/actions/onHoverAction";
  import ToolUseHeader from "./ToolUseHeader.svelte";
  import FloatingToolUseHeader from "./FloatingToolUseHeader.svelte";
  import ToolUseDetails from "../ToolUseDetails.svelte";
  import { isKnownToolType } from "$vscode/src/webview-providers/tool-types";
  import { getToolUseContext } from "../tool-context";
  import { scrollTo } from "$common-webviews/src/apps/chat/components/actions/followBottom";
  import { STICKY_HEADER_TOP_PX } from "$common-webviews/src/design-system/components/CollapsibleAugment/constants";

  const ctx = getToolUseContext();

  export let showToolOutput: boolean | undefined = true;
  export let toolUse = $ctx.toolUse;
  export let toolUseState = $ctx.toolUseState;
  export let toolUseInput: Record<string, unknown> = $ctx.toolUseInput;
  export let collapsed: boolean = true;

  // Accept the message list container as a prop
  export let messageListContainer: HTMLElement | undefined = $ctx.messageListContainer;

  let showOutput: boolean;
  $: showOutput =
    showToolOutput === undefined ? !isKnownToolType(toolUse.tool_name) : showToolOutput;

  // Parse the tool input JSON and export it for child components to use
  const hover = createHoverAction(0);
  export let toggle: (() => void) | undefined = undefined;

  // Element references for the tool use container and content
  let collapsibleContentElement: HTMLElement | undefined;
  // Track whether the header is currently sticky
  let isHeaderStuck: boolean;
  $: showScrollToTopPill = !collapsed && showOutput && isHeaderStuck;

  // Function to scroll to the top of the collapsible content
  function onScrollToTop() {
    if (messageListContainer && collapsibleContentElement) {
      scrollTo(messageListContainer, collapsibleContentElement, {
        smooth: true,
      });
    } else if (collapsibleContentElement) {
      // Find a scrollable parent element
      let scrollableParent = collapsibleContentElement.parentElement;
      while (
        scrollableParent &&
        !(
          window.getComputedStyle(scrollableParent).overflowY === "scroll" ||
          window.getComputedStyle(scrollableParent).overflowY === "auto"
        )
      ) {
        scrollableParent = scrollableParent.parentElement;
      }

      // If we found a scrollable parent, use it
      if (scrollableParent) {
        scrollTo(scrollableParent, collapsibleContentElement, {
          smooth: true,
        });
      }
    }
  }

  $: {
    toolUse = $ctx.toolUse;
    toolUseState = $ctx.toolUseState;
    toolUseInput = $ctx.toolUseInput;
  }
</script>

<div
  class="c-tool-use"
  class:is-sticky={isHeaderStuck}
  use:hover
  class:is-expandable={showOutput}
  bind:this={collapsibleContentElement}
>
  <CollapsibleAugment
    stickyHeaderTop={STICKY_HEADER_TOP_PX}
    stickyHeader
    class="c-tool-use__container"
    bind:toggle
    bind:collapsed
    bind:isHeaderStuck
    expandable={showOutput}
  >
    <div slot="header" class="c-tool-use__header-container">
      <div class="c-tool-use__header">
        <FloatingToolUseHeader {onScrollToTop} isVisible={showScrollToTopPill} />
        <!-- svelte-ignore a11y-click-events-have-key-events -->
        <div role="button" tabindex="0" class="c-tool-use__content" on:click={toggle}>
          <slot name="header" {toolUseInput} {toolUse}>
            <ToolUseHeader {toolUseInput} toolName={toolUse.tool_name} />
          </slot>
        </div>
      </div>
    </div>

    <slot name="details" {toolUseInput}>
      <ToolUseDetails {toolUseInput} {toolUseState} {showOutput} />
    </slot>
  </CollapsibleAugment>
  <slot name="error" />
</div>

<style>
  /*
   * Increase specificity by using a more specific selector chain
   * This targets the monaco editor specifically within tool use containers
   */
  .c-tool-use :global(.c-tool-use__container) :global(.c-codeblock__monaco),
  .c-tool-use :global(.c-tool-use__container .c-codeblock__monaco) {
    border: 0;
    border-radius: 0;
    overflow: hidden;
  }
  .c-tool-use {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    &:not(.is-expandable) {
      cursor: default;
    }
  }

  .c-tool-use {
    flex-direction: column;
    &.is-expandable:hover:not(:has(.c-icon-btn:hover)) {
      opacity: 0.75;
    }
    & > .c-tool-use__container {
      --base-btn-disabled-color: var(--augment-text-color);
      gap: 0;
      cursor: pointer;
      padding: 0;
      border: 0;

      /* Necessary for sticky headers and floating scroll to top button */
      overflow: visible;
    }
  }
  .c-tool-use :global(.c-collapsible__header-inner) {
    padding: var(--ds-spacing-2) var(--ds-spacing-2) var(--ds-spacing-2) var(--ds-spacing-1);
  }

  .c-tool-use__header-container {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    width: 100%;
    position: relative;
  }

  .c-tool-use__header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: var(--ds-spacing-2);
    width: 100%;
    min-height: 24px; /* Ensure consistent height */
    position: relative; /* Ensure proper stacking context */
    & button {
      cursor: pointer;
    }
  }

  .c-tool-use__content {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-2);
    flex: 1;
    background: none;
    border: none;
    padding: 0;
    text-align: left;
    color: var(--augment-text-color);
    overflow: hidden;
    min-height: 24px; /* Ensure consistent height */
  }
</style>

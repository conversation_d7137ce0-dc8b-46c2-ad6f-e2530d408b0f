<script lang="ts">
  import BaseToolComponent from "../../BaseToolComponent.svelte";
  import ToolUseHeader from "../../ToolUseHeader.svelte";
  import ListIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/list-check.svg?component";
  import {
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import TaskListToolUseDetails from "./TaskListToolUseDetails.svelte";
  import ShellError from "../ShellError.svelte";

  export let toolUseState: ToolUseState;
  let collapsed = true;
</script>

<BaseToolComponent bind:collapsed showToolOutput={true}>
  <ToolUseHeader slot="header" toolName="View Task List">
    <ListIcon slot="icon" />
  </ToolUseHeader>

  <div slot="details" class="c-view-task-list-tool__details">
    {#if toolUseState.phase === ToolUsePhase.completed}
      <TaskListToolUseDetails editable={false} />
    {/if}
  </div>
  <ShellError slot="error" {...toolUseState.result ?? {}} />
</BaseToolComponent>

<style>
  .c-view-task-list-tool__details {
    padding: var(--ds-spacing-2);
  }
</style>

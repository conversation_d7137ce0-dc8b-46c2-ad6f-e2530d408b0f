<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import { getToolUseContext } from "../../../tool-context";
  const ctx = getToolUseContext();
  export let start: number | undefined = undefined;
  export let stop: number | undefined = undefined;
  export let isDirectory: boolean = false;
  export let searchQueryRegex: string | undefined = undefined;
  export let caseSensitive: boolean = false;
</script>

<TextAugment size={2} class="c-strreplace__header__secondary">
  {#if $ctx.toolUseState.phase <= ToolUsePhase.running}
    {#if searchQueryRegex}
      Searching for pattern...
    {:else if isDirectory}
      Reading directory...
    {:else}
      Reading file...
    {/if}
  {:else if isDirectory}
    Read directory
  {:else if searchQueryRegex}
    Search for: <code>{searchQueryRegex}</code>
    {#if caseSensitive}
      <span class="case-sensitive">(case-sensitive)</span>
    {/if}
  {:else if !(start && stop)}
    Read file
  {:else}
    Read lines {start}-{stop}
  {/if}
</TextAugment>

<style>
  :global(.c-strreplace__header__secondary.c-strreplace__header__secondary) {
    display: flex;
    align-items: center;
    display: flex;
    column-gap: var(--ds-spacing-1);
  }

  code {
    font-family: var(--ds-font-family-mono);
    font-size: 0.9em;
    background-color: var(--ds-color-neutral-100);
    padding: 0 var(--ds-spacing-0-5);
    border-radius: var(--ds-radius-1);
  }

  .case-sensitive {
    font-size: 0.85em;
    color: var(--ds-color-neutral-600);
  }
</style>

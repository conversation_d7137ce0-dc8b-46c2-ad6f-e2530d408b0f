/* eslint-disable @typescript-eslint/naming-convention */
import { expect, describe, test, vi, beforeEach } from "vitest";
import type { ToolUseContextProps } from "./tool-context";
import type { ToolUseState } from "$common-webviews/src/apps/chat/types/tool-use-state";
import type { ChatResultToolUse } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";

// Mock the Svelte context functions
vi.mock("svelte", () => {
  return {
    setContext: vi.fn(),
    getContext: vi.fn(),
    derived: vi.fn((_store, _callback) => {
      // Create a simple mock of a derived store
      const subscribe = (fn: any) => {
        fn({});
        return () => {};
      };
      return { subscribe };
    }),
  };
});

// Import after mocking
import { setToolUseContext } from "./tool-context";

// Helper function to create a test store for checking update conditions
function createTestStore(initialProps: ToolUseContextProps) {
  // Create spies to track different types of updates
  const updateSpy = vi.fn();
  const updateReasons = {
    tool_use_id: vi.fn(),
    input_json: vi.fn(),
    tool_name: vi.fn(),
    phase: vi.fn(),
    result_isError: vi.fn(),
    result_text: vi.fn(),
    isLastTurn: vi.fn(),
    requestId: vi.fn(),
  };

  // Keep track of the last known props
  let lastKnownProps: ToolUseContextProps | undefined = undefined;

  // Function to check if an update should occur
  const shouldUpdate = (newProps: ToolUseContextProps): boolean => {
    if (!lastKnownProps) {
      lastKnownProps = JSON.parse(JSON.stringify(newProps)); // Deep clone
      return true;
    }

    // Check each condition individually and track the reason for updates
    let shouldTriggerUpdate = false;

    if (newProps.toolUse.tool_use_id !== lastKnownProps.toolUse.tool_use_id) {
      updateReasons.tool_use_id();
      shouldTriggerUpdate = true;
    }

    if (newProps.toolUse.input_json !== lastKnownProps.toolUse.input_json) {
      updateReasons.input_json();
      shouldTriggerUpdate = true;
    }

    if (newProps.toolUse.tool_name !== lastKnownProps.toolUse.tool_name) {
      updateReasons.tool_name();
      shouldTriggerUpdate = true;
    }

    if (newProps.toolUseState.phase !== lastKnownProps.toolUseState.phase) {
      updateReasons.phase();
      shouldTriggerUpdate = true;
    }

    // Handle result which might be undefined
    const oldResult = lastKnownProps.toolUseState.result;
    const newResult = newProps.toolUseState.result;

    // Check if one is defined and the other isn't
    const resultExistenceChanged =
      (oldResult === undefined && newResult !== undefined) ||
      (oldResult !== undefined && newResult === undefined);

    if (resultExistenceChanged) {
      updateReasons.result_isError();
      updateReasons.result_text();
      shouldTriggerUpdate = true;
    } else if (oldResult && newResult) {
      // Both are defined, check their properties
      if (oldResult.isError !== newResult.isError) {
        updateReasons.result_isError();
        shouldTriggerUpdate = true;
      }

      if (oldResult.text !== newResult.text) {
        updateReasons.result_text();
        shouldTriggerUpdate = true;
      }
    }

    if (newProps.isLastTurn !== lastKnownProps.isLastTurn) {
      updateReasons.isLastTurn();
      shouldTriggerUpdate = true;
    }

    if (newProps.requestId !== lastKnownProps.requestId) {
      updateReasons.requestId();
      shouldTriggerUpdate = true;
    }

    if (shouldTriggerUpdate) {
      lastKnownProps = JSON.parse(JSON.stringify(newProps)); // Deep clone
      updateSpy();
      return true;
    }

    return false;
  };

  // Initialize with the initial props
  shouldUpdate(initialProps);
  updateSpy.mockClear(); // Clear the initial update
  Object.values(updateReasons).forEach((spy) => spy.mockClear()); // Clear all reason spies

  return {
    update: (props: ToolUseContextProps) => shouldUpdate(props),
    updateSpy,
    updateReasons,
    getCurrentProps: () => lastKnownProps,
  };
}

describe("tool-context", () => {
  let mockToolUse: ChatResultToolUse;
  let mockToolUseState: ToolUseState;
  let mockProps: ToolUseContextProps;

  beforeEach(() => {
    mockToolUse = {
      tool_use_id: "test-tool-id",
      tool_name: "test-tool",
      input_json: JSON.stringify({ command: "test" }),
    };

    mockToolUseState = {
      phase: ToolUsePhase.new,
      result: undefined,
      requestId: "test-request-id",
      toolUseId: "test-tool-id",
    };

    mockProps = {
      toolUse: mockToolUse,
      toolUseState: mockToolUseState,
      isLastTurn: false,
      requestId: "test-request-id",
      onToolRun: vi.fn(),
      onToolCancel: vi.fn(),
      onToolSkip: vi.fn(),
      turnIndex: 0,
      messageListContainer: undefined,
    };

    // Reset mocks
    vi.clearAllMocks();
  });

  test("should not update context when props haven't changed", () => {
    // Setup the test store
    const { update, updateSpy } = createTestStore(mockProps);

    // Update with the same props
    update(mockProps);

    // The derived store should not call set again since nothing changed
    expect(updateSpy).not.toHaveBeenCalled();
  });

  test("should update context when tool_use_id changes", () => {
    // Setup the test store
    const { update, updateSpy, updateReasons } = createTestStore(mockProps);

    // Update with different tool_use_id
    const updatedProps = {
      ...mockProps,
      toolUse: {
        ...mockProps.toolUse,
        tool_use_id: "new-tool-id",
      },
    };
    update(updatedProps);

    // The derived store should call set since tool_use_id changed
    expect(updateSpy).toHaveBeenCalledTimes(1);
    expect(updateReasons.tool_use_id).toHaveBeenCalledTimes(1);

    // Other properties should not trigger updates
    expect(updateReasons.input_json).not.toHaveBeenCalled();
    expect(updateReasons.tool_name).not.toHaveBeenCalled();
    expect(updateReasons.phase).not.toHaveBeenCalled();
    expect(updateReasons.result_isError).not.toHaveBeenCalled();
    expect(updateReasons.result_text).not.toHaveBeenCalled();
    expect(updateReasons.isLastTurn).not.toHaveBeenCalled();
    expect(updateReasons.requestId).not.toHaveBeenCalled();
  });

  test("should update context when input_json changes", () => {
    // Setup the test store
    const { update, updateSpy, updateReasons } = createTestStore(mockProps);

    // Update with different input_json
    const updatedProps = {
      ...mockProps,
      toolUse: {
        ...mockProps.toolUse,
        input_json: JSON.stringify({ command: "new-test" }),
      },
    };
    update(updatedProps);

    // The derived store should call set since input_json changed
    expect(updateSpy).toHaveBeenCalledTimes(1);
    expect(updateReasons.input_json).toHaveBeenCalledTimes(1);

    // Other properties should not trigger updates
    expect(updateReasons.tool_use_id).not.toHaveBeenCalled();
    expect(updateReasons.tool_name).not.toHaveBeenCalled();
    expect(updateReasons.phase).not.toHaveBeenCalled();
    expect(updateReasons.result_isError).not.toHaveBeenCalled();
    expect(updateReasons.result_text).not.toHaveBeenCalled();
    expect(updateReasons.isLastTurn).not.toHaveBeenCalled();
    expect(updateReasons.requestId).not.toHaveBeenCalled();
  });

  test("should update context when tool_name changes", () => {
    // Setup the test store
    const { update, updateSpy, updateReasons } = createTestStore(mockProps);

    // Update with different tool_name
    const updatedProps = {
      ...mockProps,
      toolUse: {
        ...mockProps.toolUse,
        tool_name: "new-tool-name",
      },
    };
    update(updatedProps);

    // The derived store should call set since tool_name changed
    expect(updateSpy).toHaveBeenCalledTimes(1);
    expect(updateReasons.tool_name).toHaveBeenCalledTimes(1);

    // Other properties should not trigger updates
    expect(updateReasons.tool_use_id).not.toHaveBeenCalled();
    expect(updateReasons.input_json).not.toHaveBeenCalled();
    expect(updateReasons.phase).not.toHaveBeenCalled();
    expect(updateReasons.result_isError).not.toHaveBeenCalled();
    expect(updateReasons.result_text).not.toHaveBeenCalled();
    expect(updateReasons.isLastTurn).not.toHaveBeenCalled();
    expect(updateReasons.requestId).not.toHaveBeenCalled();
  });

  test("should update context when phase changes", () => {
    // Setup the test store
    const { update, updateSpy, updateReasons } = createTestStore(mockProps);

    // Update with different phase
    const updatedProps = {
      ...mockProps,
      toolUseState: {
        ...mockProps.toolUseState,
        phase: ToolUsePhase.running,
      },
    };
    update(updatedProps);

    // The derived store should call set since phase changed
    expect(updateSpy).toHaveBeenCalledTimes(1);
    expect(updateReasons.phase).toHaveBeenCalledTimes(1);

    // Other properties should not trigger updates
    expect(updateReasons.tool_use_id).not.toHaveBeenCalled();
    expect(updateReasons.input_json).not.toHaveBeenCalled();
    expect(updateReasons.tool_name).not.toHaveBeenCalled();
    expect(updateReasons.result_isError).not.toHaveBeenCalled();
    expect(updateReasons.result_text).not.toHaveBeenCalled();
    expect(updateReasons.isLastTurn).not.toHaveBeenCalled();
    expect(updateReasons.requestId).not.toHaveBeenCalled();
  });

  test("should update context when result changes", () => {
    // Setup the test store
    const { update, updateSpy, updateReasons } = createTestStore(mockProps);

    // Update with a result
    const updatedProps = {
      ...mockProps,
      toolUseState: {
        ...mockProps.toolUseState,
        result: {
          isError: false,
          text: "Test result",
        },
      },
    };
    update(updatedProps);

    // The derived store should call set since result changed
    expect(updateSpy).toHaveBeenCalledTimes(1);
    expect(updateReasons.result_isError).toHaveBeenCalledTimes(1);
    expect(updateReasons.result_text).toHaveBeenCalledTimes(1);

    // Other properties should not trigger updates
    expect(updateReasons.tool_use_id).not.toHaveBeenCalled();
    expect(updateReasons.input_json).not.toHaveBeenCalled();
    expect(updateReasons.tool_name).not.toHaveBeenCalled();
    expect(updateReasons.phase).not.toHaveBeenCalled();
    expect(updateReasons.isLastTurn).not.toHaveBeenCalled();
    expect(updateReasons.requestId).not.toHaveBeenCalled();
  });

  test("should update context when isLastTurn changes", () => {
    // Setup the test store
    const { update, updateSpy, updateReasons } = createTestStore(mockProps);

    // Update with different isLastTurn
    const updatedProps = {
      ...mockProps,
      isLastTurn: true,
    };
    update(updatedProps);

    // The derived store should call set since isLastTurn changed
    expect(updateSpy).toHaveBeenCalledTimes(1);
    expect(updateReasons.isLastTurn).toHaveBeenCalledTimes(1);

    // Other properties should not trigger updates
    expect(updateReasons.tool_use_id).not.toHaveBeenCalled();
    expect(updateReasons.input_json).not.toHaveBeenCalled();
    expect(updateReasons.tool_name).not.toHaveBeenCalled();
    expect(updateReasons.phase).not.toHaveBeenCalled();
    expect(updateReasons.result_isError).not.toHaveBeenCalled();
    expect(updateReasons.result_text).not.toHaveBeenCalled();
    expect(updateReasons.requestId).not.toHaveBeenCalled();
  });

  test("should update context when requestId changes", () => {
    // Setup the test store
    const { update, updateSpy, updateReasons } = createTestStore(mockProps);

    // Update with different requestId
    const updatedProps = {
      ...mockProps,
      requestId: "new-request-id",
    };
    update(updatedProps);

    // The derived store should call set since requestId changed
    expect(updateSpy).toHaveBeenCalledTimes(1);
    expect(updateReasons.requestId).toHaveBeenCalledTimes(1);

    // Other properties should not trigger updates
    expect(updateReasons.tool_use_id).not.toHaveBeenCalled();
    expect(updateReasons.input_json).not.toHaveBeenCalled();
    expect(updateReasons.tool_name).not.toHaveBeenCalled();
    expect(updateReasons.phase).not.toHaveBeenCalled();
    expect(updateReasons.result_isError).not.toHaveBeenCalled();
    expect(updateReasons.result_text).not.toHaveBeenCalled();
    expect(updateReasons.isLastTurn).not.toHaveBeenCalled();
  });

  test("should update context when result.text changes", () => {
    // Setup the context with a result
    const propsWithResult = {
      ...mockProps,
      toolUseState: {
        ...mockProps.toolUseState,
        result: {
          isError: false,
          text: "Test result",
        },
      },
    };
    const { update, updateSpy, updateReasons } = createTestStore(propsWithResult);

    // Update with the same result.isError but different text
    const updatedProps = {
      ...propsWithResult,
      toolUseState: {
        ...propsWithResult.toolUseState,
        result: {
          isError: false,
          text: "Different text but same isError",
        },
      },
    };
    update(updatedProps);

    // The derived store should call set since result.text changed
    expect(updateSpy).toHaveBeenCalledTimes(1);
    expect(updateReasons.result_text).toHaveBeenCalledTimes(1);

    // Other properties should not trigger updates
    expect(updateReasons.tool_use_id).not.toHaveBeenCalled();
    expect(updateReasons.input_json).not.toHaveBeenCalled();
    expect(updateReasons.tool_name).not.toHaveBeenCalled();
    expect(updateReasons.phase).not.toHaveBeenCalled();
    expect(updateReasons.result_isError).not.toHaveBeenCalled();
    expect(updateReasons.isLastTurn).not.toHaveBeenCalled();
    expect(updateReasons.requestId).not.toHaveBeenCalled();
  });

  test("should not update context when unrelated object properties change", () => {
    // Setup the context with an extra property
    const propsWithExtra = {
      ...mockProps,
      extraProp: "extra",
    } as ToolUseContextProps & { extraProp: string };

    const { update, updateSpy, updateReasons } = createTestStore(propsWithExtra);

    // Update with different extraProp
    const updatedProps = {
      ...propsWithExtra,
      extraProp: "new-extra",
    };
    update(updatedProps);

    // The derived store should not call set since extraProp is not tracked
    expect(updateSpy).not.toHaveBeenCalled();

    // None of the tracked properties should register updates
    expect(updateReasons.tool_use_id).not.toHaveBeenCalled();
    expect(updateReasons.input_json).not.toHaveBeenCalled();
    expect(updateReasons.tool_name).not.toHaveBeenCalled();
    expect(updateReasons.phase).not.toHaveBeenCalled();
    expect(updateReasons.result_isError).not.toHaveBeenCalled();
    expect(updateReasons.result_text).not.toHaveBeenCalled();
    expect(updateReasons.isLastTurn).not.toHaveBeenCalled();
    expect(updateReasons.requestId).not.toHaveBeenCalled();
  });

  // This test is just to ensure the function doesn't throw errors
  test("setToolUseContext should not throw errors", () => {
    // Call the actual setToolUseContext function and expect it not to throw
    expect(() => setToolUseContext(mockProps)).not.toThrow();
  });

  test("should handle multiple property changes in a single update", () => {
    // Setup the test store
    const { update, updateSpy, updateReasons } = createTestStore(mockProps);

    // Update multiple properties at once
    const updatedProps = {
      ...mockProps,
      toolUse: {
        ...mockProps.toolUse,
        tool_use_id: "new-tool-id",
        tool_name: "new-tool-name",
      },
      isLastTurn: true,
    };
    update(updatedProps);

    // The derived store should call set only once despite multiple changes
    expect(updateSpy).toHaveBeenCalledTimes(1);

    // But each individual property change should be tracked
    expect(updateReasons.tool_use_id).toHaveBeenCalledTimes(1);
    expect(updateReasons.tool_name).toHaveBeenCalledTimes(1);
    expect(updateReasons.isLastTurn).toHaveBeenCalledTimes(1);

    // Other properties should not trigger updates
    expect(updateReasons.input_json).not.toHaveBeenCalled();
    expect(updateReasons.phase).not.toHaveBeenCalled();
    expect(updateReasons.result_isError).not.toHaveBeenCalled();
    expect(updateReasons.result_text).not.toHaveBeenCalled();
    expect(updateReasons.requestId).not.toHaveBeenCalled();
  });

  test("should handle result appearing and disappearing", () => {
    // Setup the test store
    const { update, updateSpy, updateReasons } = createTestStore(mockProps);

    // Add a result
    const propsWithResult = {
      ...mockProps,
      toolUseState: {
        ...mockProps.toolUseState,
        result: {
          isError: false,
          text: "Test result",
        },
      },
    };
    update(propsWithResult);

    // The derived store should call set when result is added
    expect(updateSpy).toHaveBeenCalledTimes(1);
    expect(updateReasons.result_isError).toHaveBeenCalledTimes(1);
    expect(updateReasons.result_text).toHaveBeenCalledTimes(1);

    // Reset spies
    updateSpy.mockClear();
    Object.values(updateReasons).forEach((spy) => spy.mockClear());

    // Remove the result
    update(mockProps);

    // The derived store should call set when result is removed
    expect(updateSpy).toHaveBeenCalledTimes(1);
    expect(updateReasons.result_isError).toHaveBeenCalledTimes(1);
    expect(updateReasons.result_text).toHaveBeenCalledTimes(1);
  });

  test("should handle consecutive updates with the same values", () => {
    // Setup the test store
    const { update, updateSpy } = createTestStore(mockProps);

    // Update with the same props multiple times
    update(mockProps);
    update(mockProps);
    update(mockProps);

    // The derived store should not call set since nothing changed
    expect(updateSpy).not.toHaveBeenCalled();

    // Make a change
    const updatedProps = {
      ...mockProps,
      requestId: "new-request-id",
    };
    update(updatedProps);

    // The derived store should call set once
    expect(updateSpy).toHaveBeenCalledTimes(1);

    // Reset spy
    updateSpy.mockClear();

    // Update with the same updated props multiple times
    update(updatedProps);
    update(updatedProps);

    // The derived store should not call set again
    expect(updateSpy).not.toHaveBeenCalled();
  });

  test("should handle complex nested object changes correctly", () => {
    // Setup the test store with a complex initial state
    const complexProps = {
      ...mockProps,
      toolUseState: {
        ...mockProps.toolUseState,
        phase: ToolUsePhase.completed,
        result: {
          isError: true,
          text: "Error message",
        },
      },
    };
    const { update, updateSpy, updateReasons } = createTestStore(complexProps);

    // Update with a change to result.isError only
    const updatedProps = {
      ...complexProps,
      toolUseState: {
        ...complexProps.toolUseState,
        result: {
          ...complexProps.toolUseState.result!,
          isError: false,
        },
      },
    };
    update(updatedProps);

    // The derived store should call set
    expect(updateSpy).toHaveBeenCalledTimes(1);
    expect(updateReasons.result_isError).toHaveBeenCalledTimes(1);
    expect(updateReasons.result_text).not.toHaveBeenCalled();

    // Reset spies
    updateSpy.mockClear();
    Object.values(updateReasons).forEach((spy) => spy.mockClear());

    // Update with a change to both phase and result.text
    const updatedProps2 = {
      ...updatedProps,
      toolUseState: {
        ...updatedProps.toolUseState,
        phase: ToolUsePhase.running,
        result: {
          ...updatedProps.toolUseState.result!,
          text: "New message",
        },
      },
    };
    update(updatedProps2);

    // The derived store should call set once despite multiple changes
    expect(updateSpy).toHaveBeenCalledTimes(1);
    expect(updateReasons.phase).toHaveBeenCalledTimes(1);
    expect(updateReasons.result_text).toHaveBeenCalledTimes(1);
    expect(updateReasons.result_isError).not.toHaveBeenCalled();
  });

  test("should handle JSON string changes in input_json", () => {
    // Setup the test store
    const { update, updateSpy, updateReasons } = createTestStore(mockProps);

    // Update with a structurally identical but differently formatted JSON string
    const updatedProps = {
      ...mockProps,
      toolUse: {
        ...mockProps.toolUse,
        // Same content but different whitespace
        input_json: JSON.stringify({ command: "test" }, null, 2),
      },
    };
    update(updatedProps);

    // The derived store should call set since the string representation changed
    // even though the parsed objects would be equivalent
    expect(updateSpy).toHaveBeenCalledTimes(1);
    expect(updateReasons.input_json).toHaveBeenCalledTimes(1);
  });
});

import type { ToolUseState } from "$common-webviews/src/apps/chat/types/tool-use-state";
import type { ChatResultToolUse } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { getContext, setContext } from "svelte";
import { derived, type Readable, writable, type Writable } from "svelte/store";
import { safeJsonParse } from "./components/tool-util";

const toolContext = Symbol("toolContext");

export interface ToolUseContextProps {
  toolUse: ChatResultToolUse;
  toolUseState: ToolUseState;
  isLastTurn: boolean;
  requestId: string;
  onToolRun(requestId: string, toolUse: ChatResultToolUse, toolUseInput: any): void;
  onToolCancel(requestId: string, toolUseId: string): void;
  onToolSkip(requestId: string, toolUseId: string): void;
  turnIndex: number;
  messageListContainer: HTMLElement | undefined;
}

export interface ToolUseContext extends ToolUseContextProps {
  toolUseInput: Record<string, unknown>;
}

export function getToolUseContext() {
  const ctx = getContext<Readable<ToolUseContext>>(toolContext);

  if (!ctx) {
    throw new Error("ToolUseContext context not found");
  }

  return ctx;
}

/**
 * Tool uses can potentially be very large, if we are for example reading a process
 * tool and the shell process is long-running/has a lot of output. In those cases,
 * doing a JSON.parse on the input JSON can be expensive. So we memoize the result
 * of the JSON.parse and only update the store if the toolUse changes in a way
 * that matters.
 *
 * To avoid this readable parsing, we store all our input data in:
 * - the writable store (which takes updates from props, represented as stringified JSON)
 * - the readable store (which filters out unneeded updates, represented as JS objects)
 *
 * The writable store is updated when the component is rerendered with new props.
 * The readable store is updated when the writable store changes in a way that matters.
 *
 * @param ctx - The initial context
 * @returns - The writable and readable stores
 */
export function setToolUseContext(ctx: ToolUseContextProps): {
  writableCtx: Writable<ToolUseContextProps>;
  readableCtx: Readable<ToolUseContext>;
} {
  const writableCtx = writable(ctx);

  // Store the last known props to avoid unnecessary updates
  let lastKnownProps: ToolUseContextProps | undefined = undefined;
  const readableCtx: Readable<ToolUseContext> = derived(writableCtx, ($writableCtx, set) => {
    // Gate the updates based on what we know
    if (
      $writableCtx.toolUse.tool_use_id === lastKnownProps?.toolUse.tool_use_id &&
      $writableCtx.toolUse.input_json === lastKnownProps?.toolUse.input_json &&
      $writableCtx.toolUse.tool_name === lastKnownProps?.toolUse.tool_name &&
      $writableCtx.toolUseState.phase === lastKnownProps?.toolUseState.phase &&
      $writableCtx.toolUseState.result?.isError === lastKnownProps?.toolUseState.result?.isError &&
      $writableCtx.toolUseState.result?.text === lastKnownProps?.toolUseState.result?.text &&
      $writableCtx.isLastTurn === lastKnownProps?.isLastTurn &&
      $writableCtx.requestId === lastKnownProps?.requestId &&
      $writableCtx.turnIndex === lastKnownProps?.turnIndex
    ) {
      return;
    }
    lastKnownProps = $writableCtx;
    const toolUseInput = safeJsonParse($writableCtx.toolUse.input_json);
    set({
      ...lastKnownProps,
      toolUseInput: toolUseInput ?? {},
    });
  });
  setContext<Readable<ToolUseContext>>(toolContext, readableCtx);

  return {
    writableCtx,
    readableCtx,
  };
}

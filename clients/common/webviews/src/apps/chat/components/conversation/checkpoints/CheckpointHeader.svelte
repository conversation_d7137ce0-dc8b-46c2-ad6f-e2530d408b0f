<!-- A component that displays the checkpoint header with title and summary -->
<script lang="ts">
  import TextCombo from "$common-webviews/src/common/components/TextCombo.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import Revert from "$common-webviews/src/design-system/icons/revert.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import Signpost from "$common-webviews/src/design-system/icons/signpost.svelte";
  import EditChangeSummary from "../../agent-edits/EditChangeSummary.svelte";
  import CollapseButtonAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapseButtonAugment.svelte";

  export let displayCheckpointIdx: number;
  export let filesCount: number = 0;
  export let timestamp: string = "";
  export let revertMessage: string | undefined = undefined;
  export let diffSummary: { totalAddedLines: number; totalRemovedLines: number } = {
    totalAddedLines: 0,
    totalRemovedLines: 0,
  };
  export let hasChanges: boolean = false;
  export let isTarget: boolean = false;
  export let onRevertClick: () => void;
</script>

<div class="c-checkpoint-header">
  <div class="c-checkpoint-tag">
    <CollapseButtonAugment />
    <TextCombo size={1} shrink align="left">
      <Signpost slot="leftIcon" />
      <svelte:fragment slot="text">
        Checkpoint {displayCheckpointIdx}
        {#if filesCount > 0}
          <span class="c-checkpoint-files-count">
            ({filesCount} file{filesCount === 1 ? "" : "s"})
          </span>
        {/if}
      </svelte:fragment>
      <svelte:fragment slot="grayText">
        {#if revertMessage}
          {revertMessage}
        {:else if timestamp}
          {timestamp}
        {/if}
      </svelte:fragment>
    </TextCombo>
  </div>

  {#if hasChanges}
    <div class="c-checkpoint-summary">
      <EditChangeSummary
        totalAddedLines={diffSummary.totalAddedLines}
        totalRemovedLines={diffSummary.totalRemovedLines}
      />
    </div>
  {/if}

  {#if !isTarget}
    <IconButtonAugment
      variant="ghost-block"
      color="neutral"
      size={1}
      on:click={onRevertClick}
      class="c-revert-button"
      data-testid="revert-button"
    >
      <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} content="Revert to this Checkpoint">
        <Revert />
      </TextTooltipAugment>
    </IconButtonAugment>
  {/if}
</div>

<style>
  .c-checkpoint-header {
    padding: var(--ds-spacing-1);
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    width: 100%;

    /* Spread out all items */
    justify-content: space-between;

    & .c-revert-button {
      flex-shrink: 0;
    }

    & .c-checkpoint-tag {
      max-width: 100%;
      min-width: 0;
      display: flex;
      align-items: center;
      gap: var(--ds-spacing-1);

      user-select: none;
      flex: 1;

      & .c-text-combo {
        width: 100%;
      }
    }

    & .c-checkpoint-summary {
      flex-shrink: 0;
    }

    & .c-checkpoint-files-count {
      font-weight: normal;
      opacity: 0.8;
    }
  }
</style>

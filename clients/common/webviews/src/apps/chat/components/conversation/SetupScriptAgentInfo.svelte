<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import Terminal from "$common-webviews/src/design-system/icons/augment/terminal.svelte";

  /**
   * This component displays information about setup script agents
   * It replaces the "SETUP_MODE" message in the chat thread
   */
</script>

<div class="setup-script-info">
  <CalloutAugment color="accent" variant="soft" size={1}>
    <div class="setup-script-info-content">
      <div class="setup-script-info-icon">
        <Terminal />
      </div>
      <div class="setup-script-info-text">
        <TextAugment size={1} weight="medium">Setup Script Generation</TextAugment>
        <TextAugment size={1}>
          This agent is creating a setup script for your project. With access to a limited number of tools, it generates a script to help set up the development environment with the necessary dependencies and configurations.
        </TextAugment>
      </div>
    </div>
  </CalloutAugment>
</div>

<style>
  .setup-script-info {
    margin-top: var(--ds-spacing-5);
  }

  .setup-script-info-content {
    display: flex;
    gap: var(--ds-spacing-3);
    align-items: flex-start;
  }

  .setup-script-info-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--ds-color-accent-11);
    flex-shrink: 0;
  }

  .setup-script-info-icon :global(svg) {
    width: 20px;
    height: 20px;
  }

  .setup-script-info-text {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }
</style>

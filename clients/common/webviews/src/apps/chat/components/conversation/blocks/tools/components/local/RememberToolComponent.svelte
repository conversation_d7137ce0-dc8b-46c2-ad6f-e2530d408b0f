<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import ArchiveIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/box-archive.svg?component";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import { stringOrDefault } from "$common-webviews/src/apps/chat/types/tool-use-state";

  export let toolUseInput: Record<string, unknown> = {};
</script>

<BaseToolComponent>
  <ToolUseHeader
    slot="header"
    toolName="Remember"
    formattedToolArgs={[stringOrDefault(toolUseInput.memory, "")]}
  >
    <ArchiveIcon slot="icon" />
  </ToolUseHeader>
</BaseToolComponent>

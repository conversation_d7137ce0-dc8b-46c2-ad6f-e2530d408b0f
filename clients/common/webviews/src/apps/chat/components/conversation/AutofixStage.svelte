<script lang="ts">
  import MaterialIcon from "$common-webviews/src/common/components/MaterialIcon.svelte";
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import { dsColorAttribute } from "$common-webviews/src/design-system/_libs/component-utils";
  import { type AutofixIterationStage, AutofixMessages } from "$vscode/src/autofix/autofix-state";
  import { getContext } from "svelte";
  import { type AutofixConversationModel } from "../../models/autofix-conversation-model";
  import { getIterationMessage } from "../../utils/autofix-utils";

  export let stage: AutofixIterationStage;
  export let iterationId: string;
  export let stageCount: number | undefined;

  const autofixConversationModel: AutofixConversationModel = getContext("autofixConversationModel");

  const messageColors: Record<AutofixMessages, string> = {
    [AutofixMessages.retesting]: "info",
    [AutofixMessages.testRunning]: "info",
    [AutofixMessages.testFailed]: "error",
    [AutofixMessages.testPassed]: "success",
    [AutofixMessages.generatingSolutions]: "info",
    [AutofixMessages.suggestedSolutions]: "warning",
    [AutofixMessages.selectedSolutions]: "success",
  };

  const messageIcons: Record<AutofixMessages, { iconName: string; color: string }> = {
    [AutofixMessages.retesting]: { iconName: "cached", color: "#FFFFFF" },
    [AutofixMessages.testRunning]: { iconName: "cached", color: "#FFFFFF" },
    [AutofixMessages.testFailed]: { iconName: "error", color: "#DB3B4B" },
    [AutofixMessages.testPassed]: { iconName: "check_circle", color: "#388A34" },
    [AutofixMessages.generatingSolutions]: { iconName: "cached", color: "#FFFFFF" },
    [AutofixMessages.suggestedSolutions]: { iconName: "edit", color: "#FFFFFF" },
    [AutofixMessages.selectedSolutions]: { iconName: "edit", color: "#FFFFFF" },
  };

  $: iteration = $autofixConversationModel?.getAutofixIteration(iterationId);
  $: isCurrentStage =
    iteration &&
    $autofixConversationModel.extraData?.autofixIterations?.at(-1)?.id === iteration.id &&
    iteration.currentStage === stage;

  $: message = getIterationMessage(iteration, stage, stageCount, isCurrentStage);

  const onClick = () => {
    if (message === AutofixMessages.generatingSolutions) {
      return;
    }

    autofixConversationModel.launchAutofixPanel(iterationId, stage);
  };
</script>

{#if message}
  <div
    class="stage-container"
    class:active={isCurrentStage}
    {...message ? dsColorAttribute(messageColors[message]) : {}}
    on:click={onClick}
    on:keydown={onKey("Enter", onClick)}
    role="button"
    tabindex="0"
  >
    <MaterialIcon {...messageIcons[message]} />
    <div class="message">
      {message}
    </div>
  </div>
{/if}

<style>
  .stage-container {
    display: flex;
    align-items: center;
    padding: var(--ds-spacing-3);
    gap: var(--ds-spacing-3);
    background-color: #ecf3ff05;
    border-radius: 5px;
  }

  .active {
    background-color: #3574f0;
  }

  .message {
    font-size: var(--augment-font-size);
    color: #ffffff;
  }
</style>

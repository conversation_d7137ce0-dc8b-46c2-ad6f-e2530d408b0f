<script lang="ts">
  import { scrollToBottom } from "../actions/followBottom";
  import MessageListFloating from "./MessageListFloating.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import ArrowDown from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-down.svg?component";

  // Props for controlling visibility of different buttons
  export let showScrollDown: boolean = false;
  // The message list element to scroll
  export let messageListElement: HTMLElement | null = null;

  // Handle scroll to bottom click
  const handleScrollToBottom = () => {
    if (messageListElement) {
      scrollToBottom(messageListElement, { smooth: true });
    }
  };
</script>

<MessageListFloating position="bottom">
  {#if showScrollDown}
    <div class="c-msg-list-bottom-button">
      <IconButtonAugment
        class="c-chat-floating-button"
        variant="solid"
        color="neutral"
        size={1}
        radius="full"
        on:click={handleScrollToBottom}
      >
        <ArrowDown />
      </IconButtonAugment>
    </div>
  {/if}
</MessageListFloating>

<style>
  .c-msg-list-bottom-button {
    padding-bottom: var(--ds-spacing-2);
  }
  /* Style the SVG to use currentColor and size to the button */
  :global(.c-chat-floating-button svg) {
    fill: var(--icon-color, currentColor);
    height: 14px;
    aspect-ratio: 1/1;
  }
</style>

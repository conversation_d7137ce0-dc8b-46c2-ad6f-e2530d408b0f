<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { getToolUseContext } from "../../../tool-context";

  const ctx = getToolUseContext();

  export let toolUseInput: Record<string, unknown> = $ctx.toolUseInput;

  $: searchQueryRegex =
    typeof toolUseInput.search_query_regex === "string"
      ? toolUseInput.search_query_regex
      : undefined;

  $: caseSensitive =
    typeof toolUseInput.case_sensitive === "boolean" ? toolUseInput.case_sensitive : false;

  $: contextLinesBefore =
    typeof toolUseInput.context_lines_before === "number" ? toolUseInput.context_lines_before : 5;

  $: contextLinesAfter =
    typeof toolUseInput.context_lines_after === "number" ? toolUseInput.context_lines_after : 5;

  $: viewRange =
    toolUseInput.view_range &&
    Array.isArray(toolUseInput.view_range) &&
    (toolUseInput.view_range[0] !== 0 || toolUseInput.view_range[1] !== 0)
      ? toolUseInput.view_range
      : undefined;
</script>

{#if searchQueryRegex || viewRange}
  <div class="c-view-tool-use-details">
    <div class="c-view-tool-use-details__content">
      {#if searchQueryRegex}
        <div class="c-view-tool-use-details__section">
          <TextAugment size={1} color="secondary" class="c-view-tool-use-details__label">
            Search Pattern
          </TextAugment>
          <code class="c-view-tool-use-details__value">{searchQueryRegex}</code>
        </div>

        <div class="c-view-tool-use-details__section">
          <TextAugment size={1} color="secondary" class="c-view-tool-use-details__label">
            Case Sensitive
          </TextAugment>
          <TextAugment size={1} class="c-view-tool-use-details__value">
            {caseSensitive ? "Yes" : "No"}
          </TextAugment>
        </div>

        <div class="c-view-tool-use-details__section">
          <TextAugment size={1} color="secondary" class="c-view-tool-use-details__label">
            Context Lines
          </TextAugment>
          <TextAugment size={1} class="c-view-tool-use-details__value">
            {contextLinesBefore} before, {contextLinesAfter} after
          </TextAugment>
        </div>
      {/if}

      {#if viewRange}
        <div class="c-view-tool-use-details__section">
          <TextAugment size={1} color="secondary" class="c-view-tool-use-details__label">
            View Range
          </TextAugment>
          <TextAugment size={1} class="c-view-tool-use-details__value">
            Lines {viewRange[0]} to {viewRange[1] === -1 ? "end" : viewRange[1]}
          </TextAugment>
        </div>
      {/if}
    </div>
  </div>
{/if}

<style>
  .c-view-tool-use-details {
    padding: var(--ds-spacing-2) var(--ds-spacing-3);
    background-color: var(--ds-color-neutral-a2);
    border-radius: var(--ds-radius-2);
    margin: 0 var(--ds-spacing-3) var(--ds-spacing-2) var(--ds-spacing-3);
  }

  .c-view-tool-use-details__content {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1-5);
  }

  .c-view-tool-use-details__section {
    display: flex;
    align-items: baseline;
    gap: var(--ds-spacing-2);
  }

  :global(.c-view-tool-use-details__label) {
    min-width: 120px;
    flex-shrink: 0;
  }

  .c-view-tool-use-details__value {
    flex: 1;
  }

  code {
    font-family: var(--ds-font-family-mono);
    font-size: 0.9em;
    background-color: var(--ds-color-neutral-100);
    padding: var(--ds-spacing-0-5) var(--ds-spacing-1);
    border-radius: var(--ds-radius-1);
    word-break: break-all;
  }
</style>

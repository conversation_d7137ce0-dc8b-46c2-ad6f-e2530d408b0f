<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import FileTextIcon from "$common-webviews/src/design-system/icons/file-text.svelte";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import { stringOrDefault } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import OpenFileButton from "../OpenFileButton.svelte";

  export let toolUseInput: Record<string, unknown> = {};
</script>

<BaseToolComponent>
  <ToolUseHeader
    slot="header"
    toolName="Read"
    formattedToolArgs={[stringOrDefault(toolUseInput.file_path, "")]}
  >
    <FileTextIcon slot="icon" />
    <OpenFileButton slot="toolAction" path={stringOrDefault(toolUseInput.file_path, "")} />
  </ToolUseHeader>
</BaseToolComponent>

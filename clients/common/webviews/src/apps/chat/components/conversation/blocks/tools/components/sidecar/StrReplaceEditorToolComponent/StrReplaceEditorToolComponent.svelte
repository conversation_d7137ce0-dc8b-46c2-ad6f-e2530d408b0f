<script lang="ts" context="module">
  function viewRange(toolUseInput: Record<string, unknown>): { start: number; stop: number } {
    if (toolUseInput.view_range && Array.isArray(toolUseInput.view_range)) {
      return { start: toolUseInput.view_range[0], stop: toolUseInput.view_range[1] } as any;
    }
    return { start: 0, stop: 0 };
  }
</script>

<script lang="ts">
  import BaseToolComponent from "../../BaseToolComponent.svelte";
  import ToolUseHeader from "../../ToolUseHeader.svelte";
  import FileTextIcon from "$common-webviews/src/design-system/icons/file-text.svelte";
  import Pencil2Icon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pen-to-square.svg?component";
  import EditFileToolUseDetails from "./EditFileToolUseDetails.svelte";
  import {
    stringOrDefault,
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import StrReplaceSecondary from "./StrReplaceSecondary.svelte";
  import ToolUseStatus from "../../../ToolUseStatus.svelte";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import ShellError from "../ShellError.svelte";
  import OpenFileButton from "../../OpenFileButton.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;
  let collapsed = true;
  let path: string;
  $: {
    path = stringOrDefault(toolUseInput.path || toolUseInput.file_path, "");
  }
</script>

<BaseToolComponent bind:collapsed showToolOutput={toolUseInput.command === "str_replace"}>
  <ToolUseHeader slot="header">
    <span slot="icon" style="display:contents">
      {#if toolUseInput.command === "str_replace"}
        <Pencil2Icon />
      {:else}
        <FileTextIcon />
      {/if}
    </span>
    <StrReplaceSecondary slot="toolName" {...viewRange(toolUseInput)} />
    <Filespan slot="secondary" filepath={path} />
    <OpenFileButton
      slot="toolAction"
      path={collapsed && toolUseState.phase === ToolUsePhase.completed ? path : undefined}
      {...viewRange(toolUseInput)}
    />
    <div slot="toolStatus" class="c-str-replace__tool-status">
      <ToolUseStatus />
    </div>
  </ToolUseHeader>

  <div slot="details" class="c-str-replace__details">
    {#if toolUseState.phase === ToolUsePhase.completed}
      <EditFileToolUseDetails />
    {/if}
  </div>
  <ShellError slot="error" {...toolUseState.result ?? {}} />
</BaseToolComponent>

<style>
  :global(.c-str-replace__header__directory) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    direction: rtl;
    text-align: left;
    flex: 1;
    color: var(--augment-text-color-secondary);
  }
  .c-str-replace__tool-status {
    display: flex;
    gap: var(--ds-spacing-1);
    align-items: center;
    flex: 1;
    justify-content: flex-end;
    overflow: hidden;
  }
  .c-str-replace__details {
    padding-left: var(--ds-spacing-1);
    padding-top: var(--ds-spacing-2);
  }
</style>

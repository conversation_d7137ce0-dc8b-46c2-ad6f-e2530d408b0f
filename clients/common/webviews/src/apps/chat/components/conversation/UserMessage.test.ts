import { render, waitFor } from "@testing-library/svelte";
import { expect, describe, test, vi, beforeEach, afterEach } from "vitest";
import userEvent from "@testing-library/user-event";

import UserMessageTest from "./UserMessage.test.svelte";
import { ChatModel } from "../../models/chat-model";
import { SpecialContextInputModel } from "../../models/context-model";
import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { HostClientType, type HostInterface } from "$common-webviews/src/common/hosts/host-types";
import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import { EditorTestKit } from "$common-webviews/src/design-system/components/RichTextEditorAugment/__tests__/test-kit";
import { type AsyncWebViewMessage } from "$vscode/src/webview-providers/webview-messages";
import { type WebViewMessageTypes } from "$vscode/src/utils/webviews/types";

class ChatModelTestKit {
  host: HostInterface;
  contextModel: SpecialContextInputModel;
  chatModel: ChatModel;
  uuidCounter: number;

  constructor() {
    this.host = {
      clientType: HostClientType.vscode,
      postMessage: vi.fn(),
      getState: vi.fn(),
      setState: vi.fn(),
    };
    this.contextModel = new SpecialContextInputModel();
    this.uuidCounter = 0;

    vi.spyOn(crypto, "randomUUID").mockImplementation(() => {
      return this.mockUUID(this.uuidCounter++);
    });

    this.chatModel = new ChatModel(
      new AsyncMsgSender((message: AsyncWebViewMessage<WebViewMessageTypes>) => {
        this.host.postMessage(message);
      }),
      this.host,
      this.contextModel,
      { initialFlags: { fullFeatured: true, enableEditableHistory: true } },
    );
  }

  mockUUID(id: number | string): `${string}-${string}-${string}-${string}-${string}` {
    return `mocked-uuid-a-b-${id}`;
  }

  reset() {
    this.uuidCounter = 0;
    vi.clearAllMocks();
  }
}

describe("UserMessage.svelte", () => {
  let testKit: ChatModelTestKit;
  let onStartEdit = vi.fn();
  let onAcceptEdit = vi.fn();
  let onCancelEdit = vi.fn();
  let component: ReturnType<typeof render<UserMessageTest>>;
  let input: HTMLElement;

  beforeEach(async () => {
    EditorTestKit.mockTipTapDOMFunctions();
    testKit = new ChatModelTestKit();
    onStartEdit = vi.fn();
    onAcceptEdit = vi.fn();
    onCancelEdit = vi.fn();
    component = render(UserMessageTest, {
      props: {
        chatModel: testKit.chatModel,
        msg: "Original message",
        requestId: "123",
        richTextJsonRepr: undefined,
        onStartEdit,
        onAcceptEdit,
        onCancelEdit,
      },
    });
    input = await EditorTestKit.waitForRichTextInput(component);
  });

  afterEach(() => {
    component.unmount();
    testKit.reset();
    vi.clearAllMocks();
  });

  test("renders user message correctly", async () => {
    const input = await EditorTestKit.waitForRichTextInput(component);
    expect(input).toBeInTheDocument();

    // Wait for the content to be set in the editor
    await waitFor(() => {
      expect(input?.textContent).toBe("Original message");
    });
  });

  test("edit functionality works", async () => {
    const user = userEvent.setup();
    const { getByText } = component;

    // Click edit button to start editing
    const editButton = component.getByTestId("edit-message-button");
    expect(editButton).toBeInTheDocument();
    await user.click(editButton!);
    expect(onStartEdit).toHaveBeenCalled();

    // Wait for editor to be ready and focused
    const editor = await EditorTestKit.waitForRichTextInput(component);
    await waitFor(() => {
      expect(editor.getAttribute("contenteditable")).toBe("true");
    });

    // Simulate editing by appending text
    await user.clear(editor);
    await user.type(editor, "Original message Edited");
    await user.keyboard("{Enter}");
    expect(onAcceptEdit).toHaveBeenCalled();

    // Verify the edited message is displayed
    await waitFor(() => {
      expect(getByText("Original message Edited")).toBeInTheDocument();
    });
  });

  test("cancel edit functionality works", async () => {
    const user = userEvent.setup();
    const { getByText } = component;

    // Click edit button to start editing
    const editButton = component.getByTestId("edit-message-button");
    expect(editButton).toBeInTheDocument();
    await user.click(editButton!);
    expect(onStartEdit).toHaveBeenCalled();

    // Simulate editing
    await user.type(input, " Edited");

    // Cancel edit
    await user.keyboard("{Escape}");
    expect(onCancelEdit).toHaveBeenCalled();

    // Verify the original message is still displayed
    expect(getByText("Original message")).toBeInTheDocument();
  });

  test("edit button is not visible when fullFeatured flag is false", async () => {
    component.unmount();
    component = render(UserMessageTest, {
      props: {
        chatModel: new ChatModel(
          new MessageBroker(testKit.host),
          testKit.host,
          testKit.contextModel,
          { initialFlags: { fullFeatured: false } },
        ),
        msg: "Hello",
        requestId: "123",
        richTextJsonRepr: undefined,
      },
    });

    await waitFor(() => {
      const editButton = component.queryByTestId("edit-message-button");
      expect(editButton).toBeNull();
    });

    const contextBar = component.queryByTestId("user-msg-context");
    expect(contextBar).not.toBeInTheDocument();
  });
});

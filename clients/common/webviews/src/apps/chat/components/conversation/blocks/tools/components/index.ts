import BaseToolComponent from "./BaseToolComponent.svelte";
import { type LocalToolType, type RemoteToolType } from "$vscode/src/webview-providers/tool-types";
import { type SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import { type SvelteComponent, type ComponentType } from "svelte";
import { resolveLocalTool } from "./local";
import { resolveRemoteTool } from "./remote";
import { resolveSidecarTool } from "./sidecar";
import { type ToolUseContext } from "../tool-context";
import { type MCPToolType } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { resolveMCPTool } from "./mcp";
import { type ChatResultToolUse } from "@augment-internal/sidecar-libs/src/chat/chat-types";

// Export all tool components
export * from "./sidecar";
export * from "./local";
export * from "./remote";
export { BaseToolComponent };

/**
 * Get the appropriate tool component for a given tool type
 */
export function resolveTool(
  toolType: ChatResultToolUse,
): ComponentType<SvelteComponent<ToolUseContext>> {
  // First try to resolve the tool by its name using the specific tool resolvers
  const resolvedTool =
    resolveLocalTool(toolType.tool_name as LocalToolType) ??
    resolveRemoteTool(toolType.tool_name as RemoteToolType) ??
    resolveSidecarTool(toolType.tool_name as SidecarToolType);

  if (resolvedTool) {
    return resolvedTool as ComponentType<SvelteComponent<ToolUseContext>>;
  }

  // If no specific tool resolver worked, check if it's an MCP tool
  const mcpTool = resolveMCPTool(toolType);
  if (mcpTool) {
    return mcpTool as ComponentType<SvelteComponent<ToolUseContext>>;
  }

  // Fall back to the base tool component if no specific tool was found
  return BaseToolComponent as ComponentType<SvelteComponent<ToolUseContext>>;
}

export type Tool = LocalToolType | RemoteToolType | SidecarToolType | MCPToolType;

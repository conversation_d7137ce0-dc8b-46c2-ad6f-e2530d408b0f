<script lang="ts">
  import { getContext, onMount } from "svelte";
  import AutofixStage from "./AutofixStage.svelte";

  import { type ChatModel } from "../../models/chat-model";
  import {
    ExchangeStatus,
    type IChatItemSeenState,
    isChatItemAgenticCheckpointDelimiter,
    isChatItemAgentOnboarding,
    isChatItemAutofixMessage,
    isChatItemAutofixStage,
    isChatItemAutofixSteeringMessage,
    isChatItemEducateFeatures,
    isChatItemExchangeWithStatus,
    isChatItemGenerateCommitMessage,
    isChatItemSignInWelcome,
    isChatItemSummaryResponse,
    isIChatItemSeenState,
    SeenState,
  } from "../../types/chat-message";
  import { trackScrollBehavior } from "../actions/trackScrollBehavior";

  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import GradientMask from "$common-webviews/src/design-system/components/GradientMask.svelte";
  import { type AgentConversationModel } from "../../models/agent-conversation-model";
  import {
    type OnboardingWorkspaceModel,
    SUMMARY_PREAMBLE,
  } from "../../models/onboarding-workspace-model";
  import { trackHeight } from "../actions/trackHeight";
  import { trackSeen } from "../actions/trackSeen";
  import ChatTurn from "./ChatTurn.svelte";
  import MessageListItem from "./MessageListItem.svelte";
  import EducateFeaturesBlock from "./blocks/EducateFeaturesBlock.svelte";
  import StreamingResponseBlock from "./blocks/StreamingResponseBlock.svelte";
  import SuggestedQuestions from "./blocks/SuggestedQuestions.svelte";

  import AugmentMessage from "./AugmentMessage.svelte";
  import UserMessage from "./UserMessage.svelte";
  import AwaitingUserInput from "./status-decorations/AwaitingUserInput.svelte";
  import GeneratingResponse from "./status-decorations/GeneratingResponse.svelte";
  import ResumingRemoteAgent from "./status-decorations/ResumingRemoteAgent.svelte";
  import RetryingResponse from "./status-decorations/RetryingResponse.svelte";
  import Stopped from "./status-decorations/Stopped.svelte";

  import AggregateCheckpointVersion from "./checkpoints/AggregateCheckpointVersion.svelte";
  import CheckpointVersionList from "./checkpoints/CheckpointVersionList.svelte";

  import { getMessageListContext } from "../../utils/message-list-context";
  import AgentSetupLogs from "./AgentSetupLogs.svelte";
  import MessageListBottomButton from "./MessageListBottomButton.svelte";
  import RemoteAgentRetry from "./status-decorations/RemoteAgentRetry.svelte";

  export let chatModel: ChatModel;
  export let onboardingWorkspaceModel: OnboardingWorkspaceModel;
  export let msgListElement: HTMLElement | undefined = undefined;

  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");
  const { agentExchangeStatus: ideAgentExchangeStatus, isCurrConversationAgentic } =
    agentConversationModel;
  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  $: conversationModel = $chatModel.currentConversationModel;

  $: flagsModel = $chatModel.flags;
  $: messageListContext = getMessageListContext(
    $chatModel,
    $ideAgentExchangeStatus,
    $isCurrConversationAgentic,
    $remoteAgentsModel,
  );

  $: chatHistory = messageListContext.chatHistory;
  $: groupedChatHistory = messageListContext.groupedChatHistory;
  $: lastGroupConfig = messageListContext.lastGroupConfig;
  $: doShowFloatingButtons = messageListContext.doShowFloatingButtons;
  $: doShowAgentSetupLogs = messageListContext.doShowAgentSetupLogs;
  $: remoteAgentErrorConfig = lastGroupConfig.remoteAgentErrorConfig;

  // Scroll behavior handlers. We use these to determine whether the user
  // controls scroll or Augment controls scroll, and whether we should
  // follow the bottom of the message list
  let userControlsScroll: boolean = false;
  function onScrollIntoBottom(): void {
    userControlsScroll = false;
  }
  function onScrollAwayFromBottom(): void {
    userControlsScroll = true;
  }
  function releaseControlToUser() {
    userControlsScroll = true;
  }
  onMount(() => {
    // If we have NOT seen the last exchange, the user has control of the scroll
    if ($conversationModel.lastExchange?.seen_state === SeenState.unseen) {
      releaseControlToUser();
    }
  });

  let containerHeight: number = 0;
  $: turnHeight = containerHeight;

  $: canAutoScrollDown = userControlsScroll;
  const onSeen = (turn: IChatItemSeenState) => $conversationModel.markSeen(turn);
</script>

<div
  class="c-msg-list-container"
  class:c-msg-list--minimal={!$flagsModel.fullFeatured}
  data-testid="chat-message-list"
>
  <GradientMask>
    <div
      class="c-msg-list"
      class:c-msg-list--minimal={!$flagsModel.fullFeatured}
      bind:this={msgListElement}
      use:trackScrollBehavior={{
        onScrollIntoBottom,
        onScrollAwayFromBottom,
        onScroll: (scrollTop) => {
          if (scrollTop <= 1) {
            releaseControlToUser();
          }
        },
      }}
      use:trackHeight={{ onHeightChange: (height) => (containerHeight = height) }}
    >
      {#if doShowAgentSetupLogs}
        <AgentSetupLogs />
      {/if}
      {#each groupedChatHistory as group, groupIdx}
        {@const isLastGroup = groupIdx + 1 === groupedChatHistory.length}
        <MessageListItem
          class="c-msg-list__item--grouped"
          {chatModel}
          isLastItem={isLastGroup}
          {userControlsScroll}
          requestId={group[0].turn.request_id}
          releaseScroll={() => (userControlsScroll = true)}
          messageListContainer={msgListElement}
          minHeight={isLastGroup ? turnHeight : 0}
        >
          {#each group as { turn, idx } (turn.request_id ?? `no-request-id-${idx}`)}
            {@const isLastTurn = idx + 1 === chatHistory.length}
            {#if isChatItemSignInWelcome(turn)}
              <StreamingResponseBlock {turn} />
            {:else if isChatItemEducateFeatures(turn)}
              <EducateFeaturesBlock {flagsModel} {turn} />
            {:else if isChatItemSummaryResponse(turn)}
              <StreamingResponseBlock
                {turn}
                preamble={SUMMARY_PREAMBLE}
                resendTurn={() => onboardingWorkspaceModel.retryProjectSummary(turn)}
              >
                <SuggestedQuestions conversationModel={$conversationModel} {turn} />
              </StreamingResponseBlock>
            {:else if isChatItemAutofixMessage(turn)}
              <AugmentMessage
                {group}
                markdown={turn.response_text ?? ""}
                messageListContainer={msgListElement}
              />
            {:else if isChatItemAutofixSteeringMessage(turn)}
              <UserMessage {chatModel} msg={turn.response_text ?? ""} />
            {:else if isChatItemAutofixStage(turn)}
              <AutofixStage
                stage={turn.stage}
                iterationId={turn.iterationId}
                stageCount={turn.stageCount}
              />
            {:else if isChatItemExchangeWithStatus(turn) || isChatItemGenerateCommitMessage(turn) || isChatItemAgentOnboarding(turn)}
              <ChatTurn
                {group}
                {chatModel}
                {turn}
                turnIndex={idx}
                {isLastTurn}
                messageListContainer={msgListElement}
              />
            {:else if isChatItemAgenticCheckpointDelimiter(turn) && turn.status === ExchangeStatus.success}
              {#if $flagsModel.enableRichCheckpointInfo}
                <CheckpointVersionList {turn} />
              {:else}
                <AggregateCheckpointVersion {turn} />
              {/if}
            {/if}
            {#if isIChatItemSeenState(turn)}
              <!-- When this div enters the screen, the turn is considered "seen" -->
              <div
                class="c-msg-list__turn-seen"
                use:trackSeen={{
                  onSeen: () => onSeen(turn),
                  track: turn.seen_state !== SeenState.seen,
                }}
              ></div>
            {/if}
          {/each}
          {#if isLastGroup}
            {#if remoteAgentErrorConfig}
              <RemoteAgentRetry
                error={remoteAgentErrorConfig.error}
                onRetry={remoteAgentErrorConfig.onRetry}
                onDelete={remoteAgentErrorConfig.onDelete}
              />
            {:else if lastGroupConfig.retryMessage}
              <RetryingResponse message={lastGroupConfig.retryMessage} />
            {:else if lastGroupConfig.showResumingRemoteAgent}
              <ResumingRemoteAgent />
            {:else if lastGroupConfig.showGeneratingResponse}
              <GeneratingResponse />
            {:else if lastGroupConfig.showAwaitingUserInput}
              <AwaitingUserInput />
            {:else if lastGroupConfig.showStopped}
              <Stopped />
            {/if}
            {#if lastGroupConfig.showRunningSpacer}
              <div class="c-agent-running-spacer"></div>
            {/if}
          {/if}
        </MessageListItem>
      {/each}
      {#if !chatHistory.length && remoteAgentErrorConfig}
        <RemoteAgentRetry
          error={remoteAgentErrorConfig.error}
          onRetry={remoteAgentErrorConfig.onRetry}
          onDelete={remoteAgentErrorConfig.onDelete}
        />
      {/if}
    </div>
  </GradientMask>
  {#if doShowFloatingButtons}
    <MessageListBottomButton
      messageListElement={msgListElement}
      showScrollDown={canAutoScrollDown}
    />
  {/if}
</div>

<style>
  /* Outermost component for the message list */
  .c-msg-list-container {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: end;
    overflow: hidden;
    flex: 1;
    flex-basis: 0;
  }

  /* Floating container styles are now in MessageListFloating.svelte */

  .c-msg-list > :global(:first-child) {
    margin-top: var(--ds-spacing-2);
  }

  .c-msg-list {
    /* Allows for `offsetTop` to be relative to this */
    position: relative;
    height: 100%;
    width: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
    padding-bottom: var(--ds-spacing-4); /* add extra padding for overscroll for mask effect */
    scrollbar-color: var(--augment-scrollbar-color) transparent;
  }

  .c-msg-list-container :global(.c-chat-floating-button.error) {
    color: var(--ds-color-error-11);
    background-color: var(--ds-color-neutral-2);
    border: 1px solid var(--ds-color-neutral-a3);
  }

  .c-agent-running-spacer {
    /* Make space for the Stop button */
    margin-bottom: var(--ds-spacing-8);
  }
</style>

<!-- A component that displays and sets the checkpoint version -->
<script lang="ts">
  // Import grey CSS variables
  import "@radix-ui/colors/gray-alpha.css";
  import "@radix-ui/colors/gray-dark-alpha.css";

  import { getContext } from "svelte";
  import TextCombo from "$common-webviews/src/common/components/TextCombo.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import Revert from "$common-webviews/src/design-system/icons/revert.svelte";
  import {
    type AgenticRevertTargetInfo,
    isCheckpointRevert,
    type AgenticCheckpointDelimiter,
  } from "../../../types/chat-message";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import type { CheckpointStore } from "../../../models/checkpoint-store";
  import Signpost from "$common-webviews/src/design-system/icons/signpost.svelte";
  import { formatTimestamp } from "../../agent-edits/AgentEditList.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";

  export let turn: AgenticCheckpointDelimiter;

  const checkpointStore = getContext<CheckpointStore>("checkpointStore");
  const { targetCheckpointIdx, totalCheckpointCount, uuidToIdx } = checkpointStore;

  function handleClick(checkpointIdx: number) {
    $targetCheckpointIdx = checkpointIdx;
  }

  async function handleRevert() {
    await checkpointStore.revertToCheckpoint(turn.uuid);
  }

  /**
   * Determines if a checkpoint is the target checkpoint.
   * Returns true if the checkpoint matches target, or if no target is set and it's the latest checkpoint.
   * @param checkpointIdx - The index of the checkpoint to check
   * @param totalCheckpointCount - Total number of checkpoints
   * @param targetCheckpoint - Optional target checkpoint idx to compare against
   * @returns boolean indicating if this is the target checkpoint
   */
  function isTargetCheckpoint(
    checkpointIdx: number,
    totalCheckpointCount: number,
    targetCheckpoint?: number,
  ) {
    return (
      checkpointIdx === targetCheckpoint ||
      // When the target checkpoint is undefined, it defaults to the last checkpoint, which has
      // idx = totalCheckpointCount - 1
      (targetCheckpoint === undefined && checkpointIdx === totalCheckpointCount - 1)
    );
  }

  /**
   * If the checkpoint is the result of a revert, returns a message indicating the version it was reverted from.
   * @param revertTarget - The revert target, if any
   * @returns A message indicating the version it was reverted from, or undefined if not a revert
   */
  function getRevertMessage(
    turn: AgenticCheckpointDelimiter & AgenticRevertTargetInfo,
  ): string | undefined {
    if (turn.revertTarget?.uuid) {
      const targetIdx = $uuidToIdx.get(turn.revertTarget.uuid);
      if (targetIdx === undefined) {
        return undefined;
      }
      return `Reverted to Checkpoint ${targetIdx + 1}`;
    } else if (turn.revertTarget?.filePath) {
      return `Undid changes to ${turn.revertTarget.filePath.relPath}`;
    } else {
      return undefined;
    }
  }

  /**
   * Determines if we should dim all items after this checkpoint.
   * If we are on the target checkpoint, and the target is *not* the last checkpoint, we should dim.
   * @param checkpointIdx - The idx of the checkpoint to check
   * @param totalCheckpointCount - Total number of checkpoints
   * @param targetCheckpoint - Optional target checkpoint idx to compare against
   * @returns boolean indicating if this checkpoint is at or after the target
   */
  function dimDelimiter(
    checkpointIdx: number,
    totalCheckpointCount: number,
    targetCheckpoint?: number,
  ) {
    return (
      checkpointIdx === targetCheckpoint &&
      targetCheckpoint !== undefined &&
      targetCheckpoint < totalCheckpointCount - 1
    );
  }

  $: checkpointIdx = $uuidToIdx.get(turn.uuid) ?? -1;
  $: toTimestamp = turn.toTimestamp;

  $: isTarget = isTargetCheckpoint(checkpointIdx, $totalCheckpointCount, $targetCheckpointIdx);
  $: shouldDimAfter = dimDelimiter(checkpointIdx, $totalCheckpointCount, $targetCheckpointIdx);
  $: isLatest = checkpointIdx === $totalCheckpointCount - 1;
  $: displayCheckpointIdx = checkpointIdx + 1;

  // Revert info
  $: isRevert = isCheckpointRevert(turn);
  $: revertMessage = isCheckpointRevert(turn) ? getRevertMessage(turn) : undefined;
</script>

<!-- Don't render for the latest checkpoint, unless it's a revert -->
{#if !isLatest || isRevert}
  <div
    class="c-checkpoint-container"
    class:c-checkpoint-container--target-checkpoint={isTarget}
    class:c-checkpoint-container--dimmed-marker={shouldDimAfter}
    data-checkpoint-number={checkpointIdx}
  >
    <CardAugment
      size={1}
      insetContent
      variant="ghost"
      on:click={() => handleClick(checkpointIdx)}
      class="c-checkpoint-tag"
      data-testid="checkpoint-version-tag"
    >
      <TextCombo size={1} shrink align="left">
        <Signpost slot="leftIcon" />
        <svelte:fragment slot="text">
          Checkpoint {displayCheckpointIdx}
        </svelte:fragment>
        <svelte:fragment slot="grayText">
          {#if revertMessage}
            {revertMessage}
          {:else if isTarget}
            {formatTimestamp(toTimestamp)}
          {/if}
        </svelte:fragment>
      </TextCombo>
    </CardAugment>
    <IconButtonAugment
      variant={isTarget ? "soft" : "ghost-block"}
      color="neutral"
      size={1}
      disabled={isTarget || isLatest}
      on:click={handleRevert}
      class="c-revert-button"
      data-testid="revert-button"
    >
      <TextTooltipAugment
        triggerOn={[TooltipTriggerOn.Hover]}
        content={isTarget || isLatest
          ? "Cannot revert to current version"
          : "Revert to this version"}
      >
        <Revert />
      </TextTooltipAugment>
    </IconButtonAugment>
  </div>
{/if}

<style>
  .c-checkpoint-container {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    width: 100%;
    padding: var(--ds-spacing-1) var(--ds-spacing-1) var(--ds-spacing-1) 0;

    /* Align all items to the left */
    justify-content: flex-start;

    & .c-revert-button {
      flex-shrink: 0;
    }

    & .c-checkpoint-tag {
      user-select: none;
      background-color: var(--gray-a4);
      border-radius: var(--ds-radius-1);
      /* Style for the TextCombo component inside */
      & .c-text-combo {
        padding: var(--ds-spacing-1);
        width: 100%;
      }
    }
  }
</style>

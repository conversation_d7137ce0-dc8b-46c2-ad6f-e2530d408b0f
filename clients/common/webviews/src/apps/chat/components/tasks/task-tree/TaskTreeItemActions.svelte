<script lang="ts">
  import DotsVertical from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis-vertical.svg?component";
  import FileExport from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file-export.svg?component";
  import DiagramNext from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/diagram-next.svg?component";
  import DiagramSubtask from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/diagram-subtask.svg?component";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";

  import type { HydratedTask } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import {
    TaskState,
    TaskUpdatedBy,
  } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import type { ICurrentConversationTaskStore } from "../../../models/task-store";
  import { tick } from "svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import TaskIcon from "../task-status/TaskIcon.svelte";
  import { getTaskStatusLabel } from "../utils/task-status-utils";

  // Props
  export let task: HydratedTask;
  export let taskStore: ICurrentConversationTaskStore;
  export let editable: boolean = true;

  const { isImportingExporting } = taskStore;

  let dropdownOpen = false;

  async function exportTask() {
    if ($isImportingExporting || !editable) return;
    await taskStore.exportTask(task, {
      baseName: task.name,
    });
    dropdownOpen = false;
  }

  async function exportTaskTree() {
    if ($isImportingExporting || !editable) return;
    await taskStore.exportTask(task, {
      baseName: `${task.name}_tree`,
    });
    dropdownOpen = false;
  }

  async function addTaskAfter() {
    if ($isImportingExporting || !editable) return;

    // Create a new empty task to insert
    const newTask: HydratedTask = {
      uuid: crypto.randomUUID(),
      name: "",
      description: "",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    // Use the addNewTaskAfter method to insert it at the correct position
    const addedTask = await taskStore.addNewTaskAfter(task.uuid, newTask);
    if (addedTask) {
      await tick();
      // Focus the new task
      const taskItem = document.getElementById(`task-${addedTask.uuid}`);
      const taskName = taskItem?.querySelector(".c-task-tree-item__name-editable");
      const input = taskName?.querySelector("input");
      input?.focus();
    }
    dropdownOpen = false;
  }

  async function addChildTask() {
    if ($isImportingExporting || !editable) return;
    const uuid = await taskStore.createTask("", "", task.uuid);
    if (uuid) {
      await tick();
      // Focus the new task
      const taskItem = document.getElementById(`task-${uuid}`);
      const taskName = taskItem?.querySelector(".c-task-tree-item__name-editable");
      const input = taskName?.querySelector("input");
      input?.focus();
    }
    dropdownOpen = false;
  }

  $: hasSubtasks = task.subTasksData && task.subTasksData.length > 0;

  // Task state change handler
  async function changeTaskState(newState: TaskState) {
    if (!editable || newState === task.state) return;
    await taskStore.updateTask(task.uuid, { state: newState }, TaskUpdatedBy.USER);
    dropdownOpen = false;
  }

  // Get all available task states for the dropdown
  $: taskStates = Object.values(TaskState);
</script>

<!-- Task tree item actions dropdown -->
<DropdownMenuAugment.Root
  triggerOn={[TooltipTriggerOn.Hover, TooltipTriggerOn.Click]}
  open={dropdownOpen}
  onHoverEnd={() => (dropdownOpen = false)}
>
  <DropdownMenuAugment.Trigger>
    <TextTooltipAugment content="More actions" triggerOn={[TooltipTriggerOn.Hover]}>
      <IconButtonAugment
        size={1}
        variant="ghost"
        color="neutral"
        disabled={!editable || $isImportingExporting}
        class="c-task-tree-item-actions__trigger"
        on:click={() => (dropdownOpen = !dropdownOpen)}
      >
        {#if $isImportingExporting}
          <SpinnerAugment size={1} />
        {:else}
          <DotsVertical />
        {/if}
      </IconButtonAugment>
    </TextTooltipAugment>
  </DropdownMenuAugment.Trigger>
  <DropdownMenuAugment.Content size={1} side="bottom" align="end">
    <!-- Task State Section -->
    <DropdownMenuAugment.Label>Update Status</DropdownMenuAugment.Label>
    {#each taskStates as state}
      <DropdownMenuAugment.Item
        onSelect={() => changeTaskState(state)}
        highlight={state === task.state}
        disabled={!editable || state === task.state}
      >
        <TaskIcon slot="iconLeft" taskState={state} size={1} />
        {getTaskStatusLabel(state)}
      </DropdownMenuAugment.Item>
    {/each}

    <!-- Add Section -->
    <DropdownMenuAugment.Separator />
    <DropdownMenuAugment.Label>Add</DropdownMenuAugment.Label>
    <DropdownMenuAugment.Item onSelect={addTaskAfter} disabled={$isImportingExporting || !editable}>
      <DiagramNext slot="iconLeft" />
      Add Task After
    </DropdownMenuAugment.Item>
    <DropdownMenuAugment.Item onSelect={addChildTask} disabled={$isImportingExporting || !editable}>
      <DiagramSubtask slot="iconLeft" />
      Add Child Task
    </DropdownMenuAugment.Item>

    <!-- Export Section -->
    <DropdownMenuAugment.Separator />
    <DropdownMenuAugment.Label>Export</DropdownMenuAugment.Label>
    {#if hasSubtasks}
      <!-- Export full task tree -->
      <DropdownMenuAugment.Item
        onSelect={exportTaskTree}
        disabled={$isImportingExporting || !editable}
      >
        <FileExport slot="iconLeft" />
        Export Task Tree
      </DropdownMenuAugment.Item>
    {:else}
      <!-- Export single task -->
      <DropdownMenuAugment.Item onSelect={exportTask} disabled={$isImportingExporting || !editable}>
        <FileExport slot="iconLeft" />
        Export Task
      </DropdownMenuAugment.Item>
    {/if}
  </DropdownMenuAugment.Content>
</DropdownMenuAugment.Root>

<style>
  :global(.c-task-tree-item-actions__trigger) {
    opacity: 0.6;
    transition: opacity 0.2s ease;
  }

  :global(.c-task-tree-item-actions__trigger:hover) {
    opacity: 1;
  }
</style>

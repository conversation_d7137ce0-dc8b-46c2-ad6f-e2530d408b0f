import { describe, it, expect } from "vitest";
import { TaskState } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
import { getTaskStatusColor, getTaskStatusLabel, getTaskStatusIcon } from "./task-status-utils";

describe("task-status-utils", () => {
  describe("getTaskStatusColor", () => {
    it("should return correct colors for all task states", () => {
      expect(getTaskStatusColor(TaskState.NOT_STARTED)).toBe("neutral");
      expect(getTaskStatusColor(TaskState.IN_PROGRESS)).toBe("info");
      expect(getTaskStatusColor(TaskState.COMPLETE)).toBe("success");
      expect(getTaskStatusColor(TaskState.CANCELLED)).toBe("error");
    });

    it("should return neutral for unknown task states", () => {
      // Test with an invalid task state (cast to TaskState for testing)
      expect(getTaskStatusColor("UNKNOWN_STATE" as TaskState)).toBe("neutral");
    });

    it("should handle all enum values", () => {
      // Ensure we test all possible TaskState enum values
      const allTaskStates = Object.values(TaskState);
      expect(allTaskStates).toHaveLength(4);

      allTaskStates.forEach((state) => {
        const color = getTaskStatusColor(state);
        expect(["neutral", "info", "success", "error", "warning"]).toContain(color);
      });
    });
  });

  describe("getTaskStatusLabel", () => {
    it("should return correct labels for all task states", () => {
      expect(getTaskStatusLabel(TaskState.NOT_STARTED)).toBe("Not Started");
      expect(getTaskStatusLabel(TaskState.IN_PROGRESS)).toBe("In Progress");
      expect(getTaskStatusLabel(TaskState.COMPLETE)).toBe("Completed");
      expect(getTaskStatusLabel(TaskState.CANCELLED)).toBe("Cancelled");
    });

    it("should return 'Not Started' for unknown task states", () => {
      // Test with an invalid task state (cast to TaskState for testing)
      expect(getTaskStatusLabel("UNKNOWN_STATE" as TaskState)).toBe("Not Started");
    });

    it("should return human-readable strings", () => {
      const allTaskStates = Object.values(TaskState);

      allTaskStates.forEach((state) => {
        const label = getTaskStatusLabel(state);
        expect(typeof label).toBe("string");
        expect(label.length).toBeGreaterThan(0);
        // Labels should be properly capitalized
        expect(label[0]).toBe(label[0].toUpperCase());
      });
    });

    it("should have consistent label format", () => {
      // All labels should be title case and contain only letters and spaces
      const allTaskStates = Object.values(TaskState);

      allTaskStates.forEach((state) => {
        const label = getTaskStatusLabel(state);
        expect(label).toMatch(/^[A-Z][a-z]*(\s[A-Z][a-z]*)*$/);
      });
    });
  });

  describe("getTaskStatusIcon", () => {
    it("should return valid Svelte components for all task states", () => {
      expect(getTaskStatusIcon(TaskState.NOT_STARTED)).toBeDefined();
      expect(getTaskStatusIcon(TaskState.IN_PROGRESS)).toBeDefined();
      expect(getTaskStatusIcon(TaskState.COMPLETE)).toBeDefined();
      expect(getTaskStatusIcon(TaskState.CANCELLED)).toBeDefined();
    });

    it("should return different icons for different states", () => {
      const notStartedIcon = getTaskStatusIcon(TaskState.NOT_STARTED);
      const inProgressIcon = getTaskStatusIcon(TaskState.IN_PROGRESS);
      const completeIcon = getTaskStatusIcon(TaskState.COMPLETE);
      const cancelledIcon = getTaskStatusIcon(TaskState.CANCELLED);

      // Each state should have a unique icon component
      const icons = [notStartedIcon, inProgressIcon, completeIcon, cancelledIcon];
      const uniqueIcons = new Set(icons);
      expect(uniqueIcons.size).toBe(4);
    });

    it("should return function/component type", () => {
      const allTaskStates = Object.values(TaskState);

      allTaskStates.forEach((state) => {
        const icon = getTaskStatusIcon(state);
        expect(typeof icon).toBe("function");
      });
    });

    it("should handle unknown task states gracefully", () => {
      // Should return the default icon (same as NOT_STARTED)
      const unknownIcon = getTaskStatusIcon("UNKNOWN_STATE" as TaskState);
      const notStartedIcon = getTaskStatusIcon(TaskState.NOT_STARTED);
      expect(unknownIcon).toBe(notStartedIcon);
    });
  });

  describe("integration tests", () => {
    it("should have consistent mappings across all functions", () => {
      const allTaskStates = Object.values(TaskState);

      allTaskStates.forEach((state) => {
        // All functions should handle the same task state without throwing
        expect(() => getTaskStatusColor(state)).not.toThrow();
        expect(() => getTaskStatusLabel(state)).not.toThrow();
        expect(() => getTaskStatusIcon(state)).not.toThrow();
      });
    });

    it("should provide complete coverage of TaskState enum", () => {
      // Verify that we have explicit test cases for all enum values
      const testedStates = [
        TaskState.NOT_STARTED,
        TaskState.IN_PROGRESS,
        TaskState.COMPLETE,
        TaskState.CANCELLED,
      ];

      const allTaskStates = Object.values(TaskState);
      expect(testedStates.sort()).toEqual(allTaskStates.sort());
    });
  });
});

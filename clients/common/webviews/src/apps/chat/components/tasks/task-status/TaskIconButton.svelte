<script lang="ts">
  import {
    TaskState,
    TaskUpdatedBy,
  } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import { getTaskStatusColor, getTaskStatusIcon } from "../utils/task-status-utils";
  import type {
    ButtonSize,
    ButtonColor,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import type { ICurrentConversationTaskStore } from "../../../models/task-store";
  import Play from "$common-webviews/src/design-system/icons/augment/play.svelte";
  import type { ComponentType, SvelteComponent } from "svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";

  /** Current state of the task */
  export let taskState: TaskState;

  /** Size of the button */
  export let size: ButtonSize = 1;

  /** Task UUID for interactive functionality */
  export let taskUuid: string | undefined = undefined;

  /** Task store for interactive functionality */
  export let taskStore: ICurrentConversationTaskStore | undefined = undefined;

  /** Whether to enable interactive state transitions */
  export let interactive: boolean = true;

  /** Whether the button is disabled */
  export let disabled: boolean = false;

  // State for hover behavior
  let isHovered = false;

  // Task action interface
  interface TaskAction {
    currentState: TaskState;
    nextState: TaskState;
    icon: ComponentType<SvelteComponent>;
    color: ButtonColor;
    tooltip: string;
    handler: () => Promise<void>;
  }

  $: uuidToTask = taskStore?.uuidToTask;

  // Single reactive computation that contains all action information
  $: currentAction = ((): TaskAction => {
    if (taskState === TaskState.NOT_STARTED) {
      return {
        currentState: TaskState.NOT_STARTED,
        nextState: TaskState.IN_PROGRESS,
        icon: Play,
        color: "accent",
        tooltip: "Run task",
        handler: handleRunTask,
      };
    } else if (taskState === TaskState.IN_PROGRESS) {
      return {
        currentState: TaskState.IN_PROGRESS,
        nextState: TaskState.COMPLETE,
        icon: getTaskStatusIcon(TaskState.COMPLETE),
        color: "success",
        tooltip: "Complete task",
        handler: handleCompleteTask,
      };
    } else if (taskState === TaskState.COMPLETE || taskState === TaskState.CANCELLED) {
      return {
        currentState: taskState,
        nextState: TaskState.NOT_STARTED,
        icon: getTaskStatusIcon(TaskState.NOT_STARTED),
        color: "accent",
        tooltip: "Mark as Not Started",
        handler: handleRestartTask,
      };
    } else {
      // Fallback
      return {
        currentState: taskState,
        nextState: TaskState.NOT_STARTED,
        icon: getTaskStatusIcon(TaskState.NOT_STARTED),
        color: "neutral",
        tooltip: "Mark as Not Started",
        handler: handleRestartTask,
      };
    }
  })();

  // Derive display properties from the current action
  $: currentIcon = getTaskStatusIcon(currentAction.currentState);
  $: currentColor = getTaskStatusColor(currentAction.currentState) as ButtonColor;
  $: hoverIcon = currentAction.icon;
  $: hoverColor = currentAction.color;
  $: tooltip = currentAction.tooltip;
  $: displayColor = isHovered ? hoverColor : currentColor;

  // Handle the transition action
  async function handleRunTask() {
    if (!taskUuid || !taskStore || disabled) return;
    const task = $uuidToTask?.get(taskUuid);
    if (!task) return;
    await taskStore.updateTask(taskUuid, { state: TaskState.IN_PROGRESS }, TaskUpdatedBy.USER);
    await taskStore.runHydratedTask(task);
  }

  async function handleCompleteTask() {
    if (!taskUuid || !taskStore || disabled) return;
    await taskStore.updateTask(taskUuid, { state: TaskState.COMPLETE }, TaskUpdatedBy.USER);
  }

  async function handleRestartTask() {
    if (!taskUuid || !taskStore || disabled) return;
    await taskStore.updateTask(taskUuid, { state: TaskState.NOT_STARTED }, TaskUpdatedBy.USER);
  }

  // Handle the transition action based on current action
  async function handleTransition() {
    await currentAction.handler();
  }
</script>

{#if interactive && taskUuid && taskStore}
  <div class="c-task-icon-button">
    <TextTooltipAugment content={tooltip}>
      <IconButtonAugment
        {size}
        variant="ghost"
        color={displayColor}
        {disabled}
        {...$$restProps}
        on:click={handleTransition}
        on:mouseover={() => (isHovered = true)}
        on:mouseleave={() => (isHovered = false)}
      >
        {#if isHovered}
          <svelte:component this={hoverIcon} {size} />
        {:else}
          <svelte:component this={currentIcon} {size} />
        {/if}
      </IconButtonAugment>
    </TextTooltipAugment>
  </div>
{:else}
  <IconButtonAugment
    {size}
    variant="ghost"
    color={currentColor}
    {disabled}
    {...$$restProps}
    on:click
    on:keyup
    on:keydown
    on:mousedown
    on:mouseover
    on:focus
    on:mouseleave
    on:blur
    on:contextmenu
  >
    <svelte:component this={currentIcon} {size} />
  </IconButtonAugment>
{/if}

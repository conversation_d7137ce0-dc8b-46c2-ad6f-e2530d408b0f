<script lang="ts">
  import {
    type HydratedTask,
    TaskUpdatedBy,
  } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextAreaAugment from "$common-webviews/src/design-system/components/TextAreaAugment.svelte";
  import type { ICurrentConversationTaskStore } from "../../../models/task-store";

  // Props
  export let task: HydratedTask;
  export let taskStore: ICurrentConversationTaskStore;
  export let editable: boolean = true; // Controls whether the task details can be edited

  // Local state
  let draftDescription = task.description;
  let descriptionInput: HTMLTextAreaElement | undefined;

  function acceptEditDescription() {
    if (draftDescription.trim() !== task.description) {
      taskStore.updateTask(task.uuid, { description: draftDescription.trim() }, TaskUpdatedBy.USER);
    }
    descriptionInput?.blur();
  }

  function cancelEditDescription() {
    draftDescription = task.description;
    descriptionInput?.blur();
  }

  function handleDescriptionKeydown(e: KeyboardEvent) {
    if (e.key === "Enter" && !e.shiftKey && !e.ctrlKey && !e.metaKey) {
      e.preventDefault();
      e.stopPropagation();
      acceptEditDescription();
    } else if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      cancelEditDescription();
    }
  }
</script>

<div class="c-task-details">
  <div class="c-task-details__content">
    <!-- Task description section -->
    <div class="c-task-details__section c-task-details__description-contents">
      <TextAreaAugment
        bind:textInput={descriptionInput}
        size={1}
        variant="surface"
        placeholder="Add task description..."
        bind:value={draftDescription}
        on:keydown={handleDescriptionKeydown}
        on:blur={acceptEditDescription}
        disabled={!editable}
        rows={1}
        resize="vertical"
      />
    </div>

    <!-- Task metadata section -->
    <div class="c-task-details__section">
      <div class="c-task-details__metadata">
        <div class="c-task-details__metadata-row">
          <div class="c-task-details__metadata-label">
            <TextAugment size={1} weight="medium" color="neutral">Last Updated:</TextAugment>
          </div>
          <div class="c-task-details__metadata-value">
            <TextAugment size={1} color="secondary"
              >{new Date(task.lastUpdated).toLocaleString()}</TextAugment
            >
          </div>
        </div>
        <div class="c-task-details__metadata-row">
          <div class="c-task-details__metadata-label">
            <TextAugment size={1} weight="medium" color="neutral">Updated By:</TextAugment>
          </div>
          <div class="c-task-details__metadata-value">
            <TextAugment size={1} color="secondary">{task.lastUpdatedBy}</TextAugment>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .c-task-details {
    padding: 0;
    max-height: 0;
    overflow: hidden;

    & .c-task-details__description-contents {
      margin-left: calc(-1 * var(--ds-spacing-1));
      & .c-base-text-input {
        --base-text-field-bg-color: transparent;
        --base-text-field-box-shadow: none;
        --base-text-field-input-padding: var(--ds-spacing-1);
      }
    }

    padding: var(--ds-spacing-1);
    max-height: 400px;
    border-top: 1px solid var(--ds-color-neutral-3);
  }

  .c-task-details__content {
    display: flex;
    flex-direction: column;
  }

  .c-task-details__section {
    display: flex;
    flex-direction: column;
    margin-bottom: var(--ds-spacing-1);
  }

  .c-task-details__metadata {
    display: flex;
    flex-direction: column;
    margin-top: 0;
  }

  .c-task-details__metadata-row {
    display: flex;
    flex-direction: row;
  }

  .c-task-details__metadata-label {
    flex-shrink: 0;
    text-align: left;
    padding-right: var(--ds-spacing-2);
  }

  .c-task-details__metadata-value {
    flex: 1;
    word-break: break-word;
  }
</style>
